{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\NodeDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\NodeDialog.vue", "mtime": 1757668578477}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IFNhdmVOb2RlIH0gZnJvbSAnQC9hcGkvUFJPL2ZhY3RvcnljaGVjaycKaW1wb3J0IHsgR2V0RW50aXR5Tm9kZSB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snCi8vIGltcG9ydCB7IFNhdmVDaGVja1R5cGUgfSBmcm9tICJAL2FwaS9QUk8vZmFjdG9yeWNoZWNrIjsKaW1wb3J0IHsgR2V0RmFjdG9yeVBlb3BsZWxpc3QgfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJwppbXBvcnQgeyBHZXRQcm9jZXNzQ29kZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJwpleHBvcnQgZGVmYXVsdCB7CiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIG1vZGU6ICcnLCAvLyDljLrliIbpobnnm67lkozlt6XljoIKICAgICAgUHJvamVjdElkOiAnJywgLy8g6aG555uuSWQKICAgICAgQ2hlY2tfT2JqZWN0X0lkOiAnJywKICAgICAgQm9tX0xldmVsOiAnJywKICAgICAgZm9ybTogewogICAgICAgIE5vZGVfQ29kZTogJycsCiAgICAgICAgQ2hhbmdlX0NoZWNrX1R5cGU6IFtdLAogICAgICAgIERpc3BsYXlfTmFtZTogJycsCiAgICAgICAgVENfVXNlcklkOiAnJywKICAgICAgICBaTF9Vc2VySWQ6ICcnLAogICAgICAgIERlbWFuZF9TcG90X0NoZWNrX1JhdGU6IHVuZGVmaW5lZCwKICAgICAgICBSZXF1aXJlbWVudF9TcG90X0NoZWNrX1JhdGU6IHVuZGVmaW5lZCwKICAgICAgICBUQ19Vc2VySWRzOiBbXSwKICAgICAgICBaTF9Vc2VySWRzOiBbXSwKICAgICAgICBDaGVja19TdHlsZTogJycsCiAgICAgICAgVHNfUmVxdWlyZV9UaW1lOiAnJwogICAgICB9LAoKICAgICAgcnVsZXM6IHsKICAgICAgICBEaXNwbGF5X05hbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgICAgXSwKICAgICAgICBDaGVja19UeXBlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+35aGr5YaZ5a6M5pW06KGo5Y2VJywgdHJpZ2dlcjogJ2NoYW5nZScgfQogICAgICAgIF0sCiAgICAgICAgQ2hhbmdlX0NoZWNrX1R5cGU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHZhbGlkYXRvcjogdGhpcy5DaGVja19UeXBlX3J1bGVzLCBtZXNzYWdlOiAn6K+35aGr5YaZ5a6M5pW06KGo5Y2VJywgdHJpZ2dlcjogJ2NoYW5nZScgfQogICAgICAgIF0sCiAgICAgICAgQ2hlY2tfU3R5bGU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgICAgXQogICAgICB9LAogICAgICBydWxlc19abDogeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOihqOWNlScsIHRyaWdnZXI6ICdidXInIH0sCiAgICAgIHJ1bGVzX1RjOiB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+35aGr5YaZ5a6M5pW06KGo5Y2VJywgdHJpZ2dlcjogJ2J1cicgfSwKICAgICAgWkxfVXNlcklkc19SdWxlczogWwogICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHZhbGlkYXRvcjogdGhpcy5DaGVja19aTF9Vc2VySWRzLCBtZXNzYWdlOiAn6K+35aGr5YaZ5a6M5pW06KGo5Y2VJywgdHJpZ2dlcjogJ2NoYW5nZScgfQogICAgICBdLAogICAgICBUQ19Vc2VySWRzX1J1bGVzOiBbCiAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdmFsaWRhdG9yOiB0aGlzLkNoZWNrX1RDX1VzZXJJZHMsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgIF0sCiAgICAgIHRpdGxlOiAnJywKICAgICAgZWRpdEluZm86IHt9LAogICAgICBRdWFsaXR5Tm9kZUxpc3Q6IFt7IE5hbWU6ICflhaXlupMnIH0sIHsgTmFtZTogJ+WHuuW6kycgfV0sIC8vIOi0qOajgOiKgueCueWIl+ihqAogICAgICBDaGVja1R5cGVMaXN0OiBbCiAgICAgICAgewogICAgICAgICAgTmFtZTogJ+i0qOmHjycsCiAgICAgICAgICBJZDogMQogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgTmFtZTogJ+aOouS8pCcsCiAgICAgICAgICBJZDogMgogICAgICAgIH0KICAgICAgXSwgLy8g6LSo5qOA57G75Z6LCiAgICAgIFVzZXJMaXN0OiBbXSwgLy8g6LSo6YeP5ZGY77yM5o6i5Lyk5Lq65ZGYCiAgICAgIENoZWNrU3R5bGVMaXN0OiBbCiAgICAgICAgewogICAgICAgICAgTmFtZTogJ+aKveajgCcsCiAgICAgICAgICBJZDogMAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgTmFtZTogJ+WFqOajgCcsCiAgICAgICAgICBJZDogMQogICAgICAgIH0KICAgICAgXSwgLy8g6LSo5qOA5pa55byPCiAgICAgIHF1YWxpdHlJbnNwZWN0aW9uOiAxCiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgTm9kZV9Db2RlX0NvbTogZnVuY3Rpb24oKSB7CiAgICAgIGlmICh0aGlzLmZvcm0uTm9kZV9Db2RlKSB7CiAgICAgICAgcmV0dXJuIHRydWUKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gZmFsc2UKICAgICAgfQogICAgfQogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMuZ2V0RmFjdG9yeVBlb3BsZWxpc3QoKQogIH0sCiAgbWV0aG9kczogewogICAgQ2hlY2tfWkxfVXNlcklkcyhydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZSAmJiB0aGlzLmZvcm0uQ2hhbmdlX0NoZWNrX1R5cGUuaW5jbHVkZXMoMSkgJiYgdGhpcy5mb3JtLlpMX1VzZXJJZHMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCfor7floavlhpnlrozmlbTooajljZUnKSkKICAgICAgfSBlbHNlIHsKICAgICAgICBjYWxsYmFjaygpCiAgICAgIH0KICAgIH0sCiAgICBDaGVja19UQ19Vc2VySWRzKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgewogICAgICBpZiAoIXRoaXMuTm9kZV9Db2RlX0NvbSAmJiAhKHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZVswXSAhPSAyICYmIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZS5sZW5ndGggIT0gMikgJiYgdGhpcy5mb3JtLlRDX1VzZXJJZHMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCfor7floavlhpnlrozmlbTooajljZUnKSkKICAgICAgfSBlbHNlIHsKICAgICAgICBjYWxsYmFjaygpCiAgICAgIH0KICAgIH0sCiAgICBDaGVja19UeXBlX3J1bGVzKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgewogICAgICBpZiAodGhpcy5mb3JtLkNoYW5nZV9DaGVja19UeXBlLmxlbmd0aCA9PT0gMCkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6K+35aGr5YaZ5a6M5pW06KGo5Y2VJykpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgY2FsbGJhY2soKQogICAgICB9CiAgICB9LAogICAgU2VsZWN0VHlwZShpdGVtKSB7CiAgICAgIHRoaXMuJGZvcmNlVXBkYXRlKCkKICAgICAgdGhpcy5mb3JtLkNoYW5nZV9DaGVja19UeXBlID0gaXRlbQogICAgICBpZiAoaXRlbS5sZW5ndGggPT0gMSkgewogICAgICAgIHRoaXMuZm9ybS5DaGVja19UeXBlID0gaXRlbVswXQogICAgICB9IGVsc2UgaWYgKGl0ZW0ubGVuZ3RoID09IDIpIHsKICAgICAgICB0aGlzLmZvcm0uQ2hlY2tfVHlwZSA9IC0xCiAgICAgIH0KCiAgICAgIGlmICghaXRlbS5pbmNsdWRlcygxKSkgewogICAgICAgIHRoaXMuZm9ybS5aTF9Vc2VySWQgPSAnJwogICAgICAgIHRoaXMuZm9ybS5aTF9Vc2VySWRzID0gW10KICAgICAgfQogICAgICBpZiAoIWl0ZW0uaW5jbHVkZXMoMikpIHsKICAgICAgICB0aGlzLmZvcm0uVENfVXNlcklkID0gJycKICAgICAgICB0aGlzLmZvcm0uVENfVXNlcklkcyA9IFtdCiAgICAgIH0KICAgICAgY29uc29sZS5sb2codGhpcy5mb3JtLkNoYW5nZV9DaGVja19UeXBlKQogICAgfSwKICAgIHJlbW92ZVR5cGUoaXRlbSkgewogICAgICBjb25zb2xlLmxvZyhpdGVtLCAnYicpCiAgICAgIC8vIGlmIChpdGVtID09IDEpIHsKICAgICAgLy8gICB0aGlzLmZvcm0uWkxfVXNlcklkID0gIiI7CiAgICAgIC8vIH0gZWxzZSBpZiAoaXRlbSA9PSAyKSB7CiAgICAgIC8vICAgdGhpcy5mb3JtLlRDX1VzZXJJZCA9ICIiOwogICAgICAvLyB9CiAgICB9LAogICAgY2xlYXJUeXBlKHZhbCkgewogICAgICBjb25zb2xlLmxvZyh2YWwpCiAgICAgIHRoaXMuZm9ybS5aTF9Vc2VySWQgPSAnJwogICAgICB0aGlzLmZvcm0uVENfVXNlcklkID0gJycKICAgICAgdGhpcy5mb3JtLlpMX1VzZXJJZHMgPSBbXQogICAgICB0aGlzLmZvcm0uVENfVXNlcklkcyA9IFtdCiAgICB9LAogICAgaW5pdCh0aXRsZSwgY2hlY2tUeXBlLCBkYXRhKSB7CiAgICAgIHRoaXMuQ2hlY2tfT2JqZWN0X0lkID0gY2hlY2tUeXBlLklkCiAgICAgIHRoaXMuQm9tX0xldmVsID0gY2hlY2tUeXBlLkNvZGUKICAgICAgdGhpcy50aXRsZSA9IHRpdGxlCiAgICAgIGlmICh0aXRsZSA9PT0gJ+e8lui+kScpIHsKICAgICAgICBjb25zb2xlLmxvZyhkYXRhKQogICAgICAgIHRoaXMuZm9ybS5JZCA9IGRhdGEuSWQKICAgICAgICB0aGlzLmdldEVudGl0eU5vZGUoZGF0YSkKICAgICAgfQogICAgICB0aGlzLmdldENoZWNrTm9kZSgpCiAgICB9LAogICAgYXN5bmMgYWRkQ2hlY2tOb2RlKCkgewogICAgICBjb25zdCB7IERlbWFuZF9TcG90X0NoZWNrX1JhdGUsIC4uLm90aGVycyB9ID0gdGhpcy5mb3JtCiAgICAgIGNvbnN0IHN1Ym1pdCA9IHsKICAgICAgICAuLi5vdGhlcnMsCiAgICAgICAgQ2hlY2tfT2JqZWN0X0lkOiB0aGlzLkNoZWNrX09iamVjdF9JZCwKICAgICAgICBCb21fTGV2ZWw6IHRoaXMuQm9tX0xldmVsCiAgICAgIH0KICAgICAgaWYgKHRoaXMuZm9ybS5DaGVja19TdHlsZSA9PT0gMCkgewogICAgICAgIHN1Ym1pdC5EZW1hbmRfU3BvdF9DaGVja19SYXRlID0gRGVtYW5kX1Nwb3RfQ2hlY2tfUmF0ZQogICAgICB9CiAgICAgIGF3YWl0IFNhdmVOb2RlKHN1Ym1pdCkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfkv53lrZjmiJDlip8nCiAgICAgICAgICB9KQogICAgICAgICAgdGhpcy4kZW1pdCgnY2xvc2UnKQogICAgICAgICAgdGhpcy5kaWFsb2dEYXRhID0ge30KICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICBnZXRGYWN0b3J5UGVvcGxlbGlzdCgpIHsKICAgICAgR2V0RmFjdG9yeVBlb3BsZWxpc3QoKS50aGVuKChyZXMpID0+IHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgY29uc29sZS5sb2cocmVzLkRhdGEpCiAgICAgICAgICB0aGlzLlVzZXJMaXN0ID0gcmVzLkRhdGEKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICAvLyDliKTmlq3mmK/lt6XljoLov5jmmK/pobnnm67ojrflj5botKjmo4DoioLngrkKICAgIGdldENoZWNrTm9kZSgpIHsKICAgICAgY29uc3QgUGxhdGZvcm0gPQogICAgICAgIGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdQbGF0Zm9ybScpIHx8IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdDdXJQbGF0Zm9ybScpCiAgICAgIGlmIChQbGF0Zm9ybSA9PT0gJzInKSB7CiAgICAgICAgdGhpcy5tb2RlID0gJ2ZhY3RvcnknCiAgICAgICAgLy8gdGhpcy5nZXRGYWN0b3J5Tm9kZSgpOwogICAgICB9CiAgICAgIC8vIOiOt+WPlumhueebri/lt6XljoJpZAogICAgICB0aGlzLlByb2plY3RJZCA9CiAgICAgICAgdGhpcy5tb2RlID09PSAnZmFjdG9yeScKICAgICAgICAgID8gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ0N1clJlZmVyZW5jZUlkJykKICAgICAgICAgIDogdGhpcy5Qcm9qZWN0SWQKICAgIH0sCiAgICAvLyDlpoLmnpzmmK/lt6XljoLojrflj5botKjmo4DoioLngrkKICAgIC8vIGdldEZhY3RvcnlOb2RlKCkgewogICAgLy8gICBHZXRQcm9jZXNzQ29kZUxpc3Qoe3N5c193b3Jrb2JqZWN0X2lkOnRoaXMuQ2hlY2tfT2JqZWN0X0lkfSkudGhlbigocmVzKSA9PiB7CiAgICAvLyAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgIC8vICAgICAgIGxldCBDaGVja0pzb24gPSByZXMuRGF0YTsKICAgIC8vICAgICAgIENoZWNrSnNvbi5wdXNoKHsgTmFtZTogIuWFpeW6kyIgfSwgeyBOYW1lOiAi5Ye65bqTIiB9KTsKICAgIC8vICAgICAgIGNvbnNvbGUubG9nKENoZWNrSnNvbik7CiAgICAvLyAgICAgICB0aGlzLlF1YWxpdHlOb2RlTGlzdCA9IENoZWNrSnNvbjsKICAgIC8vICAgICAgIGNvbnNvbGUubG9nKHRoaXMuUXVhbGl0eU5vZGVMaXN0KTsKICAgIC8vICAgICB9IGVsc2UgewogICAgLy8gICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAvLyAgICAgICAgIHR5cGU6ICJlcnJvciIsCiAgICAvLyAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgLy8gICAgICAgfSk7CiAgICAvLyAgICAgfQogICAgLy8gICB9KTsKICAgIC8vIH0sCgogICAgLy8g6LSo5qOA6IqC54K56I635Y+W6LSo5qOA6IqC54K55ZCNCiAgICBjaGFuZ2VOb2RlQ29kZSh2YWwpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIE5vZGVfQ29kZTogJycsCiAgICAgICAgQ2hhbmdlX0NoZWNrX1R5cGU6IFtdLAogICAgICAgIERpc3BsYXlfTmFtZTogJycsCiAgICAgICAgVENfVXNlcklkOiAnJywKICAgICAgICBaTF9Vc2VySWQ6ICcnLAogICAgICAgIFRDX1VzZXJJZHM6IFtdLAogICAgICAgIFpMX1VzZXJJZHM6IFtdLAogICAgICAgIENoZWNrX1N0eWxlOiAnJwogICAgICB9CiAgICAgIHRoaXMuZm9ybS5EaXNwbGF5X05hbWUgPSB2YWwKICAgICAgdGhpcy5mb3JtLk5vZGVfQ29kZSA9IG51bGwKICAgICAgLy8gdGhpcy5mb3JtLkNoYW5nZV9DaGVja19UeXBlID0gW107CiAgICAgIC8vIHRyeSB7CiAgICAgIC8vICAgdGhpcy5mb3JtLk5vZGVfQ29kZSA9IHRoaXMuUXVhbGl0eU5vZGVMaXN0LmZpbmQoKHYpID0+IHsKICAgICAgLy8gICAgIHJldHVybiB2Lk5hbWUgPT0gdmFsOwogICAgICAvLyAgIH0pLklkOwogICAgICAvLyB9IGNhdGNoIChlcnIpIHsKICAgICAgLy8gICB0aGlzLmZvcm0uTm9kZV9Db2RlID0gbnVsbDsKICAgICAgLy8gfQogICAgICAvLyBjb25zb2xlLmxvZwogICAgICAvLyBsZXQgYXJyID0ge307CiAgICAgIC8vIGFyciA9IHRoaXMuUXVhbGl0eU5vZGVMaXN0LmZpbmQoKHYpID0+IHsKICAgICAgLy8gICByZXR1cm4gdi5OYW1lID09IHZhbDsKICAgICAgLy8gfSk7CiAgICAgIC8vIGNvbnNvbGUubG9nKGFycik7CiAgICAgIC8vIGlmIChhcnIpIHsKICAgICAgLy8gICB0aGlzLmZvcm0uQ2hlY2tfU3R5bGUgPSBhcnIuQ2hlY2tfU3R5bGUgPyBOdW1iZXIoYXJyLkNoZWNrX1N0eWxlKSA6ICIiOwogICAgICAvLyAgIGFyci5Jc19OZWVkX1RDICYmKHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZS5wdXNoKDIpKQogICAgICAvLyAgIGFyci5Jc19OZWVkX1pMICYmKCB0aGlzLmZvcm0uQ2hhbmdlX0NoZWNrX1R5cGUucHVzaCgxKSk7CiAgICAgIC8vICAgdGhpcy5TZWxlY3RUeXBlKHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZSk7CgogICAgICAvLyAgIHRoaXMuZm9ybS5aTF9Vc2VySWQgPSBhcnIuWkxfQ2hlY2tfVXNlcklkID8gYXJyLlpMX0NoZWNrX1VzZXJJZDogIiI7CiAgICAgIC8vICAgdGhpcy5mb3JtLlRDX1VzZXJJZCA9IGFyci5UQ19DaGVja19Vc2VySWQgPyBhcnIuVENfQ2hlY2tfVXNlcklkIDogIiIKICAgICAgLy8gICBjb25zb2xlLmxvZyh0aGlzLmZvcm0uWkxfVXNlcklkKQogICAgICAvLyB9CiAgICB9LAogICAgY2hhbmdlWkxVc2VyKHZhbCkgewogICAgICBjb25zb2xlLmxvZyh2YWwpCiAgICAgIC8vIOino+WGs+S4i+aLieahhuWbnuaYvumXrumimAogICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpCiAgICAgIHRoaXMuZm9ybS5aTF9Vc2VySWQgPSAnJwogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHZhbC5sZW5ndGg7IGkrKykgewogICAgICAgIGlmIChpID09IHZhbC5sZW5ndGggLSAxKSB7CiAgICAgICAgICB0aGlzLmZvcm0uWkxfVXNlcklkICs9IHZhbFtpXQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLmZvcm0uWkxfVXNlcklkICs9IHZhbFtpXSArICcsJwogICAgICAgIH0KICAgICAgfQogICAgICBjb25zb2xlLmxvZyh0aGlzLmZvcm0uWkxfVXNlcklkLCAndGhpcy5mb3JtLlpMX1VzZXJJZCAnKQogICAgfSwKICAgIGNoYW5nZVRDVXNlcih2YWwpIHsKICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKQogICAgICB0aGlzLmZvcm0uVENfVXNlcklkID0gJycKICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB2YWwubGVuZ3RoOyBpKyspIHsKICAgICAgICBpZiAoaSA9PSB2YWwubGVuZ3RoIC0gMSkgewogICAgICAgICAgdGhpcy5mb3JtLlRDX1VzZXJJZCArPSB2YWxbaV0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5mb3JtLlRDX1VzZXJJZCArPSB2YWxbaV0gKyAnLCcKICAgICAgICB9CiAgICAgIH0KICAgICAgLy8g6Kej5Yaz5LiL5ouJ5qGG5Zue5pi+6Zeu6aKYCgogICAgICBjb25zb2xlLmxvZyh0aGlzLmZvcm0uVENfVXNlcklkLCAndGhpcy5mb3JtLlRDX1VzZXJJZCAnKQogICAgfSwKICAgIGdldEVudGl0eU5vZGUoZGF0YSkgewogICAgICBHZXRFbnRpdHlOb2RlKHsgaWQ6IGRhdGEuSWQgfSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIGNvbnNvbGUubG9nKHJlcy5EYXRhKQogICAgICAgICAgdGhpcy5mb3JtID0gcmVzLkRhdGFbMF0KICAgICAgICAgIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZSA9IFtdCiAgICAgICAgICBpZiAodGhpcy5mb3JtLkNoZWNrX1R5cGUgPT0gMSB8fCB0aGlzLmZvcm0uQ2hlY2tfVHlwZSA9PSAyKSB7CiAgICAgICAgICAgIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZS5wdXNoKHRoaXMuZm9ybS5DaGVja19UeXBlKQogICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmZvcm0uQ2hlY2tfVHlwZSA9PSAtMSkgewogICAgICAgICAgICB0aGlzLmZvcm0uQ2hhbmdlX0NoZWNrX1R5cGUgPSBbMSwgMl0KICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZSA9IFtdCiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLmZvcm0uWkxfVXNlcklkcyA9IHRoaXMuZm9ybS5aTF9Vc2VySWQgPyB0aGlzLmZvcm0uWkxfVXNlcklkLnNwbGl0KCcsJykgOiBbXQogICAgICAgICAgdGhpcy5mb3JtLlRDX1VzZXJJZHMgPSB0aGlzLmZvcm0uVENfVXNlcklkID8gdGhpcy5mb3JtLlRDX1VzZXJJZC5zcGxpdCgnLCcpIDogW10KICAgICAgICAgIGNvbnNvbGUubG9nKHRoaXMuZm9ybS5aTF9Vc2VySWRzLCB0aGlzLmZvcm0uVENfVXNlcklkKQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZVN1Ym1pdChmb3JtKSB7CiAgICAgIHRoaXMuJHJlZnNbZm9ybV0udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB0aGlzLmFkZENoZWNrTm9kZSgpCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHJldHVybiBmYWxzZQogICAgICAgIH0KICAgICAgfSkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["NodeDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "NodeDialog.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup/components/Dialog", "sourcesContent": ["<template>\n  <div>\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"140px\">\n      <el-row>\n        <el-col :span=\"12\">\n          <el-form-item label=\"质检节点\" prop=\"Display_Name\">\n            <el-select\n              v-model=\"form.Display_Name\"\n              :disabled=\"Node_Code_Com\"\n              clearable\n              style=\"width: 100%\"\n              filterable\n              allow-create\n              placeholder=\"请输入质检节点\"\n              @change=\"changeNodeCode\"\n            >\n              <el-option\n                v-for=\"(item, index) in QualityNodeList\"\n                :key=\"index\"\n                :label=\"item.Name\"\n                :value=\"item.Name\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"专检类型\" prop=\"Change_Check_Type\">\n            <el-select\n              v-model=\"form.Change_Check_Type\"\n              style=\"width: 100%\"\n              placeholder=\"请选择专检类型\"\n              multiple\n              :disabled=\"Node_Code_Com\"\n              @change=\"SelectType\"\n              @remove-tag=\"removeType\"\n            >\n              <el-option\n                v-for=\"(item, index) in CheckTypeList\"\n                :key=\"index\"\n                :label=\"item.Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item\n            label=\"质量员\"\n            prop=\"ZL_UserIds\"\n            :rules=\"ZL_UserIds_Rules\"\n          >\n            <el-select\n              v-model=\"form.ZL_UserIds\"\n              filterable\n              clearable\n              multiple\n              style=\"width: 100%\"\n              placeholder=\"请选择质量员\"\n              :disabled=\"\n                Node_Code_Com ||\n                  (form.Change_Check_Type[0] != 1 &&\n                    form.Change_Check_Type.length != 2)\n              \"\n              @change=\"changeZLUser\"\n            >\n              <el-option\n                v-for=\"(item, index) in UserList\"\n                :key=\"index\"\n                :label=\"item.Display_Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item\n            label=\"探伤员\"\n            prop=\"TC_UserIds\"\n            :rules=\"TC_UserIds_Rules\"\n          >\n            <el-select\n              v-model=\"form.TC_UserIds\"\n              filterable\n              clearable\n              multiple\n              style=\"width: 100%\"\n              :disabled=\"\n                Node_Code_Com ||\n                  (form.Change_Check_Type[0] != 2 &&\n                    form.Change_Check_Type.length != 2)\n              \"\n              placeholder=\"请选择探伤员\"\n              @change=\"changeTCUser\"\n            >\n              <el-option\n                v-for=\"(item, index) in UserList\"\n                :key=\"index\"\n                :label=\"item.Display_Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"专检方式\" prop=\"Check_Style\">\n            <el-select\n              v-model=\"form.Check_Style\"\n              clearable\n              :disabled=\"Node_Code_Com\"\n              style=\"width: 100%\"\n              placeholder=\"请选择专检方式\"\n            >\n              <el-option\n                v-for=\"(item, index) in CheckStyleList\"\n                :key=\"index\"\n                :label=\"item.Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"质检流程\">\n            <el-radio-group v-model=\"qualityInspection\">\n              <el-radio :label=\"1\">专检</el-radio>\n            </el-radio-group>\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Change_Check_Type && form.Change_Check_Type.includes(2)\" :span=\"12\">\n          <el-form-item label=\"探伤要求时间(h)\" prop=\"Ts_Require_Time\">\n            <el-input v-model=\"form.Ts_Require_Time\" @input=\"handleInputFormat(1)\" />\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Check_Style===0 && form.Change_Check_Type && form.Change_Check_Type.includes(1)\" :span=\"12\">\n          <el-form-item label=\"质量要求合格率(%)\" prop=\"Demand_Spot_Check_Rate\">\n            <el-input v-model=\"form.Demand_Spot_Check_Rate\" class=\"cs-number-btn-hidden w100\" placeholder=\"请输入\" clearable @input=\"handleInputFormat(2, 'mm')\" />\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Check_Style===0 && form.Change_Check_Type && form.Change_Check_Type.includes(1)\" :span=\"12\">\n          <el-form-item label=\"质量要求抽检率(%)\" prop=\"Requirement_Spot_Check_Rate\">\n            <el-input v-model=\"form.Requirement_Spot_Check_Rate\" class=\"cs-number-btn-hidden w100\" placeholder=\"请输入\" clearable @input=\"handleInputFormat(2, 'mm')\" />\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Check_Style===0 && form.Change_Check_Type && form.Change_Check_Type.includes(2)\" :span=\"12\">\n          <el-form-item label=\"探伤要求合格率(%)\" prop=\"Demand_Spot_Check_Rate\">\n            <el-input v-model=\"form.Demand_Spot_Check_Rate\" class=\"cs-number-btn-hidden w100\" placeholder=\"请输入\" clearable @input=\"handleInputFormat(2, 'mm')\" />\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Check_Style===0 && form.Change_Check_Type && form.Change_Check_Type.includes(2)\" :span=\"12\">\n          <el-form-item label=\"探伤要求抽检率(%)\" prop=\"Requirement_Spot_Check_Rate\">\n            <el-input v-model=\"form.Requirement_Spot_Check_Rate\" class=\"cs-number-btn-hidden w100\" placeholder=\"请输入\" clearable @input=\"handleInputFormat(2, 'mm')\" />\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item style=\"text-align: right\">\n            <el-button @click=\"$emit('close')\">关 闭</el-button>\n            <el-button\n              type=\"primary\"\n              @click=\"handleSubmit('form')\"\n            >确 定</el-button>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n  </div>\n</template>\n\n<script>\nimport { SaveNode } from '@/api/PRO/factorycheck'\nimport { GetEntityNode } from '@/api/PRO/factorycheck'\n// import { SaveCheckType } from \"@/api/PRO/factorycheck\";\nimport { GetFactoryPeoplelist } from '@/api/PRO/factorycheck'\nimport { GetProcessCodeList } from '@/api/PRO/factorycheck'\nexport default {\n  data() {\n    return {\n      mode: '', // 区分项目和工厂\n      ProjectId: '', // 项目Id\n      Check_Object_Id: '',\n      Bom_Level: '',\n      form: {\n        Node_Code: '',\n        Change_Check_Type: [],\n        Display_Name: '',\n        TC_UserId: '',\n        ZL_UserId: '',\n        Demand_Spot_Check_Rate: undefined,\n        Requirement_Spot_Check_Rate: undefined,\n        TC_UserIds: [],\n        ZL_UserIds: [],\n        Check_Style: '',\n        Ts_Require_Time: ''\n      },\n\n      rules: {\n        Display_Name: [\n          { required: true, message: '请填写完整表单', trigger: 'change' }\n        ],\n        Check_Type: [\n          { required: true, message: '请填写完整表单', trigger: 'change' }\n        ],\n        Change_Check_Type: [\n          { required: true, validator: this.Check_Type_rules, message: '请填写完整表单', trigger: 'change' }\n        ],\n        Check_Style: [\n          { required: true, message: '请填写完整表单', trigger: 'change' }\n        ]\n      },\n      rules_Zl: { required: true, message: '请填写完整表单', trigger: 'bur' },\n      rules_Tc: { required: true, message: '请填写完整表单', trigger: 'bur' },\n      ZL_UserIds_Rules: [\n        { required: true, validator: this.Check_ZL_UserIds, message: '请填写完整表单', trigger: 'change' }\n      ],\n      TC_UserIds_Rules: [\n        { required: true, validator: this.Check_TC_UserIds, message: '请填写完整表单', trigger: 'change' }\n      ],\n      title: '',\n      editInfo: {},\n      QualityNodeList: [{ Name: '入库' }, { Name: '出库' }], // 质检节点列表\n      CheckTypeList: [\n        {\n          Name: '质量',\n          Id: 1\n        },\n        {\n          Name: '探伤',\n          Id: 2\n        }\n      ], // 质检类型\n      UserList: [], // 质量员，探伤人员\n      CheckStyleList: [\n        {\n          Name: '抽检',\n          Id: 0\n        },\n        {\n          Name: '全检',\n          Id: 1\n        }\n      ], // 质检方式\n      qualityInspection: 1\n    }\n  },\n  computed: {\n    Node_Code_Com: function() {\n      if (this.form.Node_Code) {\n        return true\n      } else {\n        return false\n      }\n    }\n  },\n  mounted() {\n    this.getFactoryPeoplelist()\n  },\n  methods: {\n    Check_ZL_UserIds(rule, value, callback) {\n      if (this.form.Change_Check_Type && this.form.Change_Check_Type.includes(1) && this.form.ZL_UserIds.length === 0) {\n        callback(new Error('请填写完整表单'))\n      } else {\n        callback()\n      }\n    },\n    Check_TC_UserIds(rule, value, callback) {\n      if (!this.Node_Code_Com && !(this.form.Change_Check_Type[0] != 2 && this.form.Change_Check_Type.length != 2) && this.form.TC_UserIds.length === 0) {\n        callback(new Error('请填写完整表单'))\n      } else {\n        callback()\n      }\n    },\n    Check_Type_rules(rule, value, callback) {\n      if (this.form.Change_Check_Type.length === 0) {\n        callback(new Error('请填写完整表单'))\n      } else {\n        callback()\n      }\n    },\n    SelectType(item) {\n      this.$forceUpdate()\n      this.form.Change_Check_Type = item\n      if (item.length == 1) {\n        this.form.Check_Type = item[0]\n      } else if (item.length == 2) {\n        this.form.Check_Type = -1\n      }\n\n      if (!item.includes(1)) {\n        this.form.ZL_UserId = ''\n        this.form.ZL_UserIds = []\n      }\n      if (!item.includes(2)) {\n        this.form.TC_UserId = ''\n        this.form.TC_UserIds = []\n      }\n      console.log(this.form.Change_Check_Type)\n    },\n    removeType(item) {\n      console.log(item, 'b')\n      // if (item == 1) {\n      //   this.form.ZL_UserId = \"\";\n      // } else if (item == 2) {\n      //   this.form.TC_UserId = \"\";\n      // }\n    },\n    clearType(val) {\n      console.log(val)\n      this.form.ZL_UserId = ''\n      this.form.TC_UserId = ''\n      this.form.ZL_UserIds = []\n      this.form.TC_UserIds = []\n    },\n    init(title, checkType, data) {\n      this.Check_Object_Id = checkType.Id\n      this.Bom_Level = checkType.Code\n      this.title = title\n      if (title === '编辑') {\n        console.log(data)\n        this.form.Id = data.Id\n        this.getEntityNode(data)\n      }\n      this.getCheckNode()\n    },\n    async addCheckNode() {\n      const { Demand_Spot_Check_Rate, ...others } = this.form\n      const submit = {\n        ...others,\n        Check_Object_Id: this.Check_Object_Id,\n        Bom_Level: this.Bom_Level\n      }\n      if (this.form.Check_Style === 0) {\n        submit.Demand_Spot_Check_Rate = Demand_Spot_Check_Rate\n      }\n      await SaveNode(submit).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            type: 'success',\n            message: '保存成功'\n          })\n          this.$emit('close')\n          this.dialogData = {}\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    getFactoryPeoplelist() {\n      GetFactoryPeoplelist().then((res) => {\n        if (res.IsSucceed) {\n          console.log(res.Data)\n          this.UserList = res.Data\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    // 判断是工厂还是项目获取质检节点\n    getCheckNode() {\n      const Platform =\n        localStorage.getItem('Platform') || localStorage.getItem('CurPlatform')\n      if (Platform === '2') {\n        this.mode = 'factory'\n        // this.getFactoryNode();\n      }\n      // 获取项目/工厂id\n      this.ProjectId =\n        this.mode === 'factory'\n          ? localStorage.getItem('CurReferenceId')\n          : this.ProjectId\n    },\n    // 如果是工厂获取质检节点\n    // getFactoryNode() {\n    //   GetProcessCodeList({sys_workobject_id:this.Check_Object_Id}).then((res) => {\n    //     if (res.IsSucceed) {\n    //       let CheckJson = res.Data;\n    //       CheckJson.push({ Name: \"入库\" }, { Name: \"出库\" });\n    //       console.log(CheckJson);\n    //       this.QualityNodeList = CheckJson;\n    //       console.log(this.QualityNodeList);\n    //     } else {\n    //       this.$message({\n    //         type: \"error\",\n    //         message: res.Message,\n    //       });\n    //     }\n    //   });\n    // },\n\n    // 质检节点获取质检节点名\n    changeNodeCode(val) {\n      this.form = {\n        Node_Code: '',\n        Change_Check_Type: [],\n        Display_Name: '',\n        TC_UserId: '',\n        ZL_UserId: '',\n        TC_UserIds: [],\n        ZL_UserIds: [],\n        Check_Style: ''\n      }\n      this.form.Display_Name = val\n      this.form.Node_Code = null\n      // this.form.Change_Check_Type = [];\n      // try {\n      //   this.form.Node_Code = this.QualityNodeList.find((v) => {\n      //     return v.Name == val;\n      //   }).Id;\n      // } catch (err) {\n      //   this.form.Node_Code = null;\n      // }\n      // console.log\n      // let arr = {};\n      // arr = this.QualityNodeList.find((v) => {\n      //   return v.Name == val;\n      // });\n      // console.log(arr);\n      // if (arr) {\n      //   this.form.Check_Style = arr.Check_Style ? Number(arr.Check_Style) : \"\";\n      //   arr.Is_Need_TC &&(this.form.Change_Check_Type.push(2))\n      //   arr.Is_Need_ZL &&( this.form.Change_Check_Type.push(1));\n      //   this.SelectType(this.form.Change_Check_Type);\n\n      //   this.form.ZL_UserId = arr.ZL_Check_UserId ? arr.ZL_Check_UserId: \"\";\n      //   this.form.TC_UserId = arr.TC_Check_UserId ? arr.TC_Check_UserId : \"\"\n      //   console.log(this.form.ZL_UserId)\n      // }\n    },\n    changeZLUser(val) {\n      console.log(val)\n      // 解决下拉框回显问题\n      this.$forceUpdate()\n      this.form.ZL_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i == val.length - 1) {\n          this.form.ZL_UserId += val[i]\n        } else {\n          this.form.ZL_UserId += val[i] + ','\n        }\n      }\n      console.log(this.form.ZL_UserId, 'this.form.ZL_UserId ')\n    },\n    changeTCUser(val) {\n      this.$forceUpdate()\n      this.form.TC_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i == val.length - 1) {\n          this.form.TC_UserId += val[i]\n        } else {\n          this.form.TC_UserId += val[i] + ','\n        }\n      }\n      // 解决下拉框回显问题\n\n      console.log(this.form.TC_UserId, 'this.form.TC_UserId ')\n    },\n    getEntityNode(data) {\n      GetEntityNode({ id: data.Id }).then((res) => {\n        if (res.IsSucceed) {\n          console.log(res.Data)\n          this.form = res.Data[0]\n          this.form.Change_Check_Type = []\n          if (this.form.Check_Type == 1 || this.form.Check_Type == 2) {\n            this.form.Change_Check_Type.push(this.form.Check_Type)\n          } else if (this.form.Check_Type == -1) {\n            this.form.Change_Check_Type = [1, 2]\n          } else {\n            this.form.Change_Check_Type = []\n          }\n          this.form.ZL_UserIds = this.form.ZL_UserId ? this.form.ZL_UserId.split(',') : []\n          this.form.TC_UserIds = this.form.TC_UserId ? this.form.TC_UserId.split(',') : []\n          console.log(this.form.ZL_UserIds, this.form.TC_UserId)\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    handleSubmit(form) {\n      this.$refs[form].validate((valid) => {\n        if (valid) {\n          this.addCheckNode()\n        } else {\n          return false\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped></style>\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\NodeDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\NodeDialog.vue", "mtime": 1757468112775}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBTYXZlTm9kZSB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snDQppbXBvcnQgeyBHZXRFbnRpdHlOb2RlIH0gZnJvbSAnQC9hcGkvUFJPL2ZhY3RvcnljaGVjaycNCi8vIGltcG9ydCB7IFNhdmVDaGVja1R5cGUgfSBmcm9tICJAL2FwaS9QUk8vZmFjdG9yeWNoZWNrIjsNCmltcG9ydCB7IEdldEZhY3RvcnlQZW9wbGVsaXN0IH0gZnJvbSAnQC9hcGkvUFJPL2ZhY3RvcnljaGVjaycNCmltcG9ydCB7IEdldFByb2Nlc3NDb2RlTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snDQpleHBvcnQgZGVmYXVsdCB7DQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIG1vZGU6ICcnLCAvLyDljLrliIbpobnnm67lkozlt6XljoINCiAgICAgIFByb2plY3RJZDogJycsIC8vIOmhueebrklkDQogICAgICBDaGVja19PYmplY3RfSWQ6ICcnLA0KICAgICAgQm9tX0xldmVsOiAnJywNCiAgICAgIGZvcm06IHsNCiAgICAgICAgTm9kZV9Db2RlOiAnJywNCiAgICAgICAgQ2hhbmdlX0NoZWNrX1R5cGU6IFtdLA0KICAgICAgICBEaXNwbGF5X05hbWU6ICcnLA0KICAgICAgICBUQ19Vc2VySWQ6ICcnLA0KICAgICAgICBaTF9Vc2VySWQ6ICcnLA0KICAgICAgICBEZW1hbmRfU3BvdF9DaGVja19SYXRlOiB1bmRlZmluZWQsDQogICAgICAgIFJlcXVpcmVtZW50X1Nwb3RfQ2hlY2tfUmF0ZTogdW5kZWZpbmVkLA0KICAgICAgICBUQ19Vc2VySWRzOiBbXSwNCiAgICAgICAgWkxfVXNlcklkczogW10sDQogICAgICAgIENoZWNrX1N0eWxlOiAnJw0KICAgICAgfSwNCg0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgRGlzcGxheV9OYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOihqOWNlScsIHRyaWdnZXI6ICdjaGFuZ2UnIH0NCiAgICAgICAgXSwNCiAgICAgICAgQ2hlY2tfVHlwZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9DQogICAgICAgIF0sDQogICAgICAgIENoYW5nZV9DaGVja19UeXBlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdmFsaWRhdG9yOiB0aGlzLkNoZWNrX1R5cGVfcnVsZXMsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9DQogICAgICAgIF0sDQogICAgICAgIENoZWNrX1N0eWxlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOihqOWNlScsIHRyaWdnZXI6ICdjaGFuZ2UnIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIHJ1bGVzX1psOiB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+35aGr5YaZ5a6M5pW06KGo5Y2VJywgdHJpZ2dlcjogJ2J1cicgfSwNCiAgICAgIHJ1bGVzX1RjOiB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+35aGr5YaZ5a6M5pW06KGo5Y2VJywgdHJpZ2dlcjogJ2J1cicgfSwNCiAgICAgIFpMX1VzZXJJZHNfUnVsZXM6IFsNCiAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdmFsaWRhdG9yOiB0aGlzLkNoZWNrX1pMX1VzZXJJZHMsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9DQogICAgICBdLA0KICAgICAgVENfVXNlcklkc19SdWxlczogWw0KICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB2YWxpZGF0b3I6IHRoaXMuQ2hlY2tfVENfVXNlcklkcywgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOihqOWNlScsIHRyaWdnZXI6ICdjaGFuZ2UnIH0NCiAgICAgIF0sDQogICAgICB0aXRsZTogJycsDQogICAgICBlZGl0SW5mbzoge30sDQogICAgICBRdWFsaXR5Tm9kZUxpc3Q6IFt7IE5hbWU6ICflhaXlupMnIH0sIHsgTmFtZTogJ+WHuuW6kycgfV0sIC8vIOi0qOajgOiKgueCueWIl+ihqA0KICAgICAgQ2hlY2tUeXBlTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgTmFtZTogJ+i0qOmHjycsDQogICAgICAgICAgSWQ6IDENCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIE5hbWU6ICfmjqLkvKQnLA0KICAgICAgICAgIElkOiAyDQogICAgICAgIH0NCiAgICAgIF0sIC8vIOi0qOajgOexu+Weiw0KICAgICAgVXNlckxpc3Q6IFtdLCAvLyDotKjph4/lkZjvvIzmjqLkvKTkurrlkZgNCiAgICAgIENoZWNrU3R5bGVMaXN0OiBbDQogICAgICAgIHsNCiAgICAgICAgICBOYW1lOiAn5oq95qOAJywNCiAgICAgICAgICBJZDogMA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgTmFtZTogJ+WFqOajgCcsDQogICAgICAgICAgSWQ6IDENCiAgICAgICAgfQ0KICAgICAgXSAvLyDotKjmo4DmlrnlvI8NCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgTm9kZV9Db2RlX0NvbTogZnVuY3Rpb24oKSB7DQogICAgICBpZiAodGhpcy5mb3JtLk5vZGVfQ29kZSkgew0KICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICB9DQogICAgfQ0KICB9LA0KICBtb3VudGVkKCkgew0KICAgIHRoaXMuZ2V0RmFjdG9yeVBlb3BsZWxpc3QoKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgQ2hlY2tfWkxfVXNlcklkcyhydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsNCiAgICAgIGlmICh0aGlzLmZvcm0uQ2hhbmdlX0NoZWNrX1R5cGUuaW5jbHVkZXMoMSkgJiYgdGhpcy5mb3JtLlpMX1VzZXJJZHMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6K+35aGr5YaZ5a6M5pW06KGo5Y2VJykpDQogICAgICB9IGVsc2Ugew0KICAgICAgICBjYWxsYmFjaygpDQogICAgICB9DQogICAgfSwNCiAgICBDaGVja19UQ19Vc2VySWRzKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgew0KICAgICAgaWYgKCF0aGlzLk5vZGVfQ29kZV9Db20gJiYgISh0aGlzLmZvcm0uQ2hhbmdlX0NoZWNrX1R5cGVbMF0gIT0gMiAmJiB0aGlzLmZvcm0uQ2hhbmdlX0NoZWNrX1R5cGUubGVuZ3RoICE9IDIpICYmIHRoaXMuZm9ybS5UQ19Vc2VySWRzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+ivt+Whq+WGmeWujOaVtOihqOWNlScpKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgY2FsbGJhY2soKQ0KICAgICAgfQ0KICAgIH0sDQogICAgQ2hlY2tfVHlwZV9ydWxlcyhydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsNCiAgICAgIGlmICh0aGlzLmZvcm0uQ2hhbmdlX0NoZWNrX1R5cGUubGVuZ3RoID09PSAwKSB7DQogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6K+35aGr5YaZ5a6M5pW06KGo5Y2VJykpDQogICAgICB9IGVsc2Ugew0KICAgICAgICBjYWxsYmFjaygpDQogICAgICB9DQogICAgfSwNCiAgICBTZWxlY3RUeXBlKGl0ZW0pIHsNCiAgICAgIHRoaXMuJGZvcmNlVXBkYXRlKCkNCiAgICAgIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZSA9IGl0ZW0NCiAgICAgIGlmIChpdGVtLmxlbmd0aCA9PSAxKSB7DQogICAgICAgIHRoaXMuZm9ybS5DaGVja19UeXBlID0gaXRlbVswXQ0KICAgICAgfSBlbHNlIGlmIChpdGVtLmxlbmd0aCA9PSAyKSB7DQogICAgICAgIHRoaXMuZm9ybS5DaGVja19UeXBlID0gLTENCiAgICAgIH0NCg0KICAgICAgaWYgKCFpdGVtLmluY2x1ZGVzKDEpKSB7DQogICAgICAgIHRoaXMuZm9ybS5aTF9Vc2VySWQgPSAnJw0KICAgICAgICB0aGlzLmZvcm0uWkxfVXNlcklkcyA9IFtdDQogICAgICB9DQogICAgICBpZiAoIWl0ZW0uaW5jbHVkZXMoMikpIHsNCiAgICAgICAgdGhpcy5mb3JtLlRDX1VzZXJJZCA9ICcnDQogICAgICAgIHRoaXMuZm9ybS5UQ19Vc2VySWRzID0gW10NCiAgICAgIH0NCiAgICAgIGNvbnNvbGUubG9nKHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZSkNCiAgICB9LA0KICAgIHJlbW92ZVR5cGUoaXRlbSkgew0KICAgICAgY29uc29sZS5sb2coaXRlbSwgJ2InKQ0KICAgICAgLy8gaWYgKGl0ZW0gPT0gMSkgew0KICAgICAgLy8gICB0aGlzLmZvcm0uWkxfVXNlcklkID0gIiI7DQogICAgICAvLyB9IGVsc2UgaWYgKGl0ZW0gPT0gMikgew0KICAgICAgLy8gICB0aGlzLmZvcm0uVENfVXNlcklkID0gIiI7DQogICAgICAvLyB9DQogICAgfSwNCiAgICBjbGVhclR5cGUodmFsKSB7DQogICAgICBjb25zb2xlLmxvZyh2YWwpDQogICAgICB0aGlzLmZvcm0uWkxfVXNlcklkID0gJycNCiAgICAgIHRoaXMuZm9ybS5UQ19Vc2VySWQgPSAnJw0KICAgICAgdGhpcy5mb3JtLlpMX1VzZXJJZHMgPSBbXQ0KICAgICAgdGhpcy5mb3JtLlRDX1VzZXJJZHMgPSBbXQ0KICAgIH0sDQogICAgaW5pdCh0aXRsZSwgY2hlY2tUeXBlLCBkYXRhKSB7DQogICAgICB0aGlzLkNoZWNrX09iamVjdF9JZCA9IGNoZWNrVHlwZS5JZA0KICAgICAgdGhpcy5Cb21fTGV2ZWwgPSBjaGVja1R5cGUuQ29kZQ0KICAgICAgdGhpcy50aXRsZSA9IHRpdGxlDQogICAgICBpZiAodGl0bGUgPT09ICfnvJbovpEnKSB7DQogICAgICAgIGNvbnNvbGUubG9nKGRhdGEpDQogICAgICAgIHRoaXMuZm9ybS5JZCA9IGRhdGEuSWQNCiAgICAgICAgdGhpcy5nZXRFbnRpdHlOb2RlKGRhdGEpDQogICAgICB9DQogICAgICB0aGlzLmdldENoZWNrTm9kZSgpDQogICAgfSwNCiAgICBhc3luYyBhZGRDaGVja05vZGUoKSB7DQogICAgICBjb25zdCB7IERlbWFuZF9TcG90X0NoZWNrX1JhdGUsIC4uLm90aGVycyB9ID0gdGhpcy5mb3JtDQogICAgICBjb25zdCBzdWJtaXQgPSB7DQogICAgICAgIC4uLm90aGVycywNCiAgICAgICAgQ2hlY2tfT2JqZWN0X0lkOiB0aGlzLkNoZWNrX09iamVjdF9JZCwNCiAgICAgICAgQm9tX0xldmVsOiB0aGlzLkJvbV9MZXZlbA0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuZm9ybS5DaGVja19TdHlsZSA9PT0gMCkgew0KICAgICAgICBzdWJtaXQuRGVtYW5kX1Nwb3RfQ2hlY2tfUmF0ZSA9IERlbWFuZF9TcG90X0NoZWNrX1JhdGUNCiAgICAgIH0NCiAgICAgIGF3YWl0IFNhdmVOb2RlKHN1Ym1pdCkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsDQogICAgICAgICAgICBtZXNzYWdlOiAn5L+d5a2Y5oiQ5YqfJw0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy4kZW1pdCgnY2xvc2UnKQ0KICAgICAgICAgIHRoaXMuZGlhbG9nRGF0YSA9IHt9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICB0eXBlOiAnZXJyb3InLA0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgZ2V0RmFjdG9yeVBlb3BsZWxpc3QoKSB7DQogICAgICBHZXRGYWN0b3J5UGVvcGxlbGlzdCgpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIGNvbnNvbGUubG9nKHJlcy5EYXRhKQ0KICAgICAgICAgIHRoaXMuVXNlckxpc3QgPSByZXMuRGF0YQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOWIpOaWreaYr+W3peWOgui/mOaYr+mhueebruiOt+WPlui0qOajgOiKgueCuQ0KICAgIGdldENoZWNrTm9kZSgpIHsNCiAgICAgIGNvbnN0IFBsYXRmb3JtID0NCiAgICAgICAgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ1BsYXRmb3JtJykgfHwgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ0N1clBsYXRmb3JtJykNCiAgICAgIGlmIChQbGF0Zm9ybSA9PT0gJzInKSB7DQogICAgICAgIHRoaXMubW9kZSA9ICdmYWN0b3J5Jw0KICAgICAgICAvLyB0aGlzLmdldEZhY3RvcnlOb2RlKCk7DQogICAgICB9DQogICAgICAvLyDojrflj5bpobnnm64v5bel5Y6CaWQNCiAgICAgIHRoaXMuUHJvamVjdElkID0NCiAgICAgICAgdGhpcy5tb2RlID09PSAnZmFjdG9yeScNCiAgICAgICAgICA/IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdDdXJSZWZlcmVuY2VJZCcpDQogICAgICAgICAgOiB0aGlzLlByb2plY3RJZA0KICAgIH0sDQogICAgLy8g5aaC5p6c5piv5bel5Y6C6I635Y+W6LSo5qOA6IqC54K5DQogICAgLy8gZ2V0RmFjdG9yeU5vZGUoKSB7DQogICAgLy8gICBHZXRQcm9jZXNzQ29kZUxpc3Qoe3N5c193b3Jrb2JqZWN0X2lkOnRoaXMuQ2hlY2tfT2JqZWN0X0lkfSkudGhlbigocmVzKSA9PiB7DQogICAgLy8gICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgLy8gICAgICAgbGV0IENoZWNrSnNvbiA9IHJlcy5EYXRhOw0KICAgIC8vICAgICAgIENoZWNrSnNvbi5wdXNoKHsgTmFtZTogIuWFpeW6kyIgfSwgeyBOYW1lOiAi5Ye65bqTIiB9KTsNCiAgICAvLyAgICAgICBjb25zb2xlLmxvZyhDaGVja0pzb24pOw0KICAgIC8vICAgICAgIHRoaXMuUXVhbGl0eU5vZGVMaXN0ID0gQ2hlY2tKc29uOw0KICAgIC8vICAgICAgIGNvbnNvbGUubG9nKHRoaXMuUXVhbGl0eU5vZGVMaXN0KTsNCiAgICAvLyAgICAgfSBlbHNlIHsNCiAgICAvLyAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAvLyAgICAgICAgIHR5cGU6ICJlcnJvciIsDQogICAgLy8gICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAvLyAgICAgICB9KTsNCiAgICAvLyAgICAgfQ0KICAgIC8vICAgfSk7DQogICAgLy8gfSwNCg0KICAgIC8vIOi0qOajgOiKgueCueiOt+WPlui0qOajgOiKgueCueWQjQ0KICAgIGNoYW5nZU5vZGVDb2RlKHZhbCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBOb2RlX0NvZGU6ICcnLA0KICAgICAgICBDaGFuZ2VfQ2hlY2tfVHlwZTogW10sDQogICAgICAgIERpc3BsYXlfTmFtZTogJycsDQogICAgICAgIFRDX1VzZXJJZDogJycsDQogICAgICAgIFpMX1VzZXJJZDogJycsDQogICAgICAgIFRDX1VzZXJJZHM6IFtdLA0KICAgICAgICBaTF9Vc2VySWRzOiBbXSwNCiAgICAgICAgQ2hlY2tfU3R5bGU6ICcnDQogICAgICB9DQogICAgICB0aGlzLmZvcm0uRGlzcGxheV9OYW1lID0gdmFsDQogICAgICB0aGlzLmZvcm0uTm9kZV9Db2RlID0gbnVsbA0KICAgICAgLy8gdGhpcy5mb3JtLkNoYW5nZV9DaGVja19UeXBlID0gW107DQogICAgICAvLyB0cnkgew0KICAgICAgLy8gICB0aGlzLmZvcm0uTm9kZV9Db2RlID0gdGhpcy5RdWFsaXR5Tm9kZUxpc3QuZmluZCgodikgPT4gew0KICAgICAgLy8gICAgIHJldHVybiB2Lk5hbWUgPT0gdmFsOw0KICAgICAgLy8gICB9KS5JZDsNCiAgICAgIC8vIH0gY2F0Y2ggKGVycikgew0KICAgICAgLy8gICB0aGlzLmZvcm0uTm9kZV9Db2RlID0gbnVsbDsNCiAgICAgIC8vIH0NCiAgICAgIC8vIGNvbnNvbGUubG9nDQogICAgICAvLyBsZXQgYXJyID0ge307DQogICAgICAvLyBhcnIgPSB0aGlzLlF1YWxpdHlOb2RlTGlzdC5maW5kKCh2KSA9PiB7DQogICAgICAvLyAgIHJldHVybiB2Lk5hbWUgPT0gdmFsOw0KICAgICAgLy8gfSk7DQogICAgICAvLyBjb25zb2xlLmxvZyhhcnIpOw0KICAgICAgLy8gaWYgKGFycikgew0KICAgICAgLy8gICB0aGlzLmZvcm0uQ2hlY2tfU3R5bGUgPSBhcnIuQ2hlY2tfU3R5bGUgPyBOdW1iZXIoYXJyLkNoZWNrX1N0eWxlKSA6ICIiOw0KICAgICAgLy8gICBhcnIuSXNfTmVlZF9UQyAmJih0aGlzLmZvcm0uQ2hhbmdlX0NoZWNrX1R5cGUucHVzaCgyKSkNCiAgICAgIC8vICAgYXJyLklzX05lZWRfWkwgJiYoIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZS5wdXNoKDEpKTsNCiAgICAgIC8vICAgdGhpcy5TZWxlY3RUeXBlKHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZSk7DQoNCiAgICAgIC8vICAgdGhpcy5mb3JtLlpMX1VzZXJJZCA9IGFyci5aTF9DaGVja19Vc2VySWQgPyBhcnIuWkxfQ2hlY2tfVXNlcklkOiAiIjsNCiAgICAgIC8vICAgdGhpcy5mb3JtLlRDX1VzZXJJZCA9IGFyci5UQ19DaGVja19Vc2VySWQgPyBhcnIuVENfQ2hlY2tfVXNlcklkIDogIiINCiAgICAgIC8vICAgY29uc29sZS5sb2codGhpcy5mb3JtLlpMX1VzZXJJZCkNCiAgICAgIC8vIH0NCiAgICB9LA0KICAgIGNoYW5nZVpMVXNlcih2YWwpIHsNCiAgICAgIGNvbnNvbGUubG9nKHZhbCkNCiAgICAgIC8vIOino+WGs+S4i+aLieahhuWbnuaYvumXrumimA0KICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKQ0KICAgICAgdGhpcy5mb3JtLlpMX1VzZXJJZCA9ICcnDQogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHZhbC5sZW5ndGg7IGkrKykgew0KICAgICAgICBpZiAoaSA9PSB2YWwubGVuZ3RoIC0gMSkgew0KICAgICAgICAgIHRoaXMuZm9ybS5aTF9Vc2VySWQgKz0gdmFsW2ldDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5mb3JtLlpMX1VzZXJJZCArPSB2YWxbaV0gKyAnLCcNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgY29uc29sZS5sb2codGhpcy5mb3JtLlpMX1VzZXJJZCwgJ3RoaXMuZm9ybS5aTF9Vc2VySWQgJykNCiAgICB9LA0KICAgIGNoYW5nZVRDVXNlcih2YWwpIHsNCiAgICAgIHRoaXMuJGZvcmNlVXBkYXRlKCkNCiAgICAgIHRoaXMuZm9ybS5UQ19Vc2VySWQgPSAnJw0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB2YWwubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgaWYgKGkgPT0gdmFsLmxlbmd0aCAtIDEpIHsNCiAgICAgICAgICB0aGlzLmZvcm0uVENfVXNlcklkICs9IHZhbFtpXQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuZm9ybS5UQ19Vc2VySWQgKz0gdmFsW2ldICsgJywnDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIC8vIOino+WGs+S4i+aLieahhuWbnuaYvumXrumimA0KDQogICAgICBjb25zb2xlLmxvZyh0aGlzLmZvcm0uVENfVXNlcklkLCAndGhpcy5mb3JtLlRDX1VzZXJJZCAnKQ0KICAgIH0sDQogICAgZ2V0RW50aXR5Tm9kZShkYXRhKSB7DQogICAgICBHZXRFbnRpdHlOb2RlKHsgaWQ6IGRhdGEuSWQgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgY29uc29sZS5sb2cocmVzLkRhdGEpDQogICAgICAgICAgdGhpcy5mb3JtID0gcmVzLkRhdGFbMF0NCiAgICAgICAgICB0aGlzLmZvcm0uQ2hhbmdlX0NoZWNrX1R5cGUgPSBbXQ0KICAgICAgICAgIGlmICh0aGlzLmZvcm0uQ2hlY2tfVHlwZSA9PSAxIHx8IHRoaXMuZm9ybS5DaGVja19UeXBlID09IDIpIHsNCiAgICAgICAgICAgIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZS5wdXNoKHRoaXMuZm9ybS5DaGVja19UeXBlKQ0KICAgICAgICAgIH0gZWxzZSBpZiAodGhpcy5mb3JtLkNoZWNrX1R5cGUgPT0gLTEpIHsNCiAgICAgICAgICAgIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZSA9IFsxLCAyXQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLmZvcm0uQ2hhbmdlX0NoZWNrX1R5cGUgPSBbXQ0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLmZvcm0uWkxfVXNlcklkcyA9IHRoaXMuZm9ybS5aTF9Vc2VySWQgPyB0aGlzLmZvcm0uWkxfVXNlcklkLnNwbGl0KCcsJykgOiBbXQ0KICAgICAgICAgIHRoaXMuZm9ybS5UQ19Vc2VySWRzID0gdGhpcy5mb3JtLlRDX1VzZXJJZCA/IHRoaXMuZm9ybS5UQ19Vc2VySWQuc3BsaXQoJywnKSA6IFtdDQogICAgICAgICAgY29uc29sZS5sb2codGhpcy5mb3JtLlpMX1VzZXJJZHMsIHRoaXMuZm9ybS5UQ19Vc2VySWQpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICB0eXBlOiAnZXJyb3InLA0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlU3VibWl0KGZvcm0pIHsNCiAgICAgIHRoaXMuJHJlZnNbZm9ybV0udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIHRoaXMuYWRkQ2hlY2tOb2RlKCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["NodeDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "NodeDialog.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup/components/Dialog", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"120px\">\r\n      <el-row>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"质检节点\" prop=\"Display_Name\">\r\n            <el-select\r\n              v-model=\"form.Display_Name\"\r\n              :disabled=\"Node_Code_Com\"\r\n              clearable\r\n              style=\"width: 100%\"\r\n              filterable\r\n              allow-create\r\n              placeholder=\"请输入质检节点\"\r\n              @change=\"changeNodeCode\"\r\n            >\r\n              <el-option\r\n                v-for=\"(item, index) in QualityNodeList\"\r\n                :key=\"index\"\r\n                :label=\"item.Name\"\r\n                :value=\"item.Name\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"专检类型\" prop=\"Change_Check_Type\">\r\n            <el-select\r\n              v-model=\"form.Change_Check_Type\"\r\n              style=\"width: 100%\"\r\n              placeholder=\"请选择专检类型\"\r\n              multiple\r\n              :disabled=\"Node_Code_Com\"\r\n              @change=\"SelectType\"\r\n              @remove-tag=\"removeType\"\r\n            >\r\n              <el-option\r\n                v-for=\"(item, index) in CheckTypeList\"\r\n                :key=\"index\"\r\n                :label=\"item.Name\"\r\n                :value=\"item.Id\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item\r\n            label=\"质量员\"\r\n            prop=\"ZL_UserIds\"\r\n            :rules=\"ZL_UserIds_Rules\"\r\n          >\r\n            <el-select\r\n              v-model=\"form.ZL_UserIds\"\r\n              filterable\r\n              clearable\r\n              multiple\r\n              style=\"width: 100%\"\r\n              placeholder=\"请选择质量员\"\r\n              :disabled=\"\r\n                Node_Code_Com ||\r\n                  (form.Change_Check_Type[0] != 1 &&\r\n                    form.Change_Check_Type.length != 2)\r\n              \"\r\n              @change=\"changeZLUser\"\r\n            >\r\n              <el-option\r\n                v-for=\"(item, index) in UserList\"\r\n                :key=\"index\"\r\n                :label=\"item.Display_Name\"\r\n                :value=\"item.Id\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item\r\n            label=\"探伤员\"\r\n            prop=\"TC_UserIds\"\r\n            :rules=\"TC_UserIds_Rules\"\r\n          >\r\n            <el-select\r\n              v-model=\"form.TC_UserIds\"\r\n              filterable\r\n              clearable\r\n              multiple\r\n              style=\"width: 100%\"\r\n              :disabled=\"\r\n                Node_Code_Com ||\r\n                  (form.Change_Check_Type[0] != 2 &&\r\n                    form.Change_Check_Type.length != 2)\r\n              \"\r\n              placeholder=\"请选择探伤员\"\r\n              @change=\"changeTCUser\"\r\n            >\r\n              <el-option\r\n                v-for=\"(item, index) in UserList\"\r\n                :key=\"index\"\r\n                :label=\"item.Display_Name\"\r\n                :value=\"item.Id\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"专检方式\" prop=\"Check_Style\">\r\n            <el-select\r\n              v-model=\"form.Check_Style\"\r\n              clearable\r\n              :disabled=\"Node_Code_Com\"\r\n              style=\"width: 100%\"\r\n              placeholder=\"请选择专检方式\"\r\n            >\r\n              <el-option\r\n                v-for=\"(item, index) in CheckStyleList\"\r\n                :key=\"index\"\r\n                :label=\"item.Name\"\r\n                :value=\"item.Id\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col v-if=\"form.Check_Style===0\" :span=\"12\">\r\n          <el-form-item label=\"要求合格率(%)\" prop=\"Demand_Spot_Check_Rate\">\r\n            <el-input-number v-model=\"form.Demand_Spot_Check_Rate\" :min=\"0\" :max=\"100\" class=\"cs-number-btn-hidden w100\" placeholder=\"请输入\" clearable />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col v-if=\"form.Check_Style===0\" :span=\"12\">\r\n          <el-form-item label=\"要求抽检率(%)\" prop=\"Requirement_Spot_Check_Rate\">\r\n            <el-input-number v-model=\"form.Requirement_Spot_Check_Rate\" :min=\"0\" :max=\"100\" class=\"cs-number-btn-hidden w100\" placeholder=\"请输入\" clearable />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"24\">\r\n          <el-form-item style=\"text-align: right\">\r\n            <el-button @click=\"$emit('close')\">关 闭</el-button>\r\n            <el-button\r\n              type=\"primary\"\r\n              @click=\"handleSubmit('form')\"\r\n            >确 定</el-button>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { SaveNode } from '@/api/PRO/factorycheck'\r\nimport { GetEntityNode } from '@/api/PRO/factorycheck'\r\n// import { SaveCheckType } from \"@/api/PRO/factorycheck\";\r\nimport { GetFactoryPeoplelist } from '@/api/PRO/factorycheck'\r\nimport { GetProcessCodeList } from '@/api/PRO/factorycheck'\r\nexport default {\r\n  data() {\r\n    return {\r\n      mode: '', // 区分项目和工厂\r\n      ProjectId: '', // 项目Id\r\n      Check_Object_Id: '',\r\n      Bom_Level: '',\r\n      form: {\r\n        Node_Code: '',\r\n        Change_Check_Type: [],\r\n        Display_Name: '',\r\n        TC_UserId: '',\r\n        ZL_UserId: '',\r\n        Demand_Spot_Check_Rate: undefined,\r\n        Requirement_Spot_Check_Rate: undefined,\r\n        TC_UserIds: [],\r\n        ZL_UserIds: [],\r\n        Check_Style: ''\r\n      },\r\n\r\n      rules: {\r\n        Display_Name: [\r\n          { required: true, message: '请填写完整表单', trigger: 'change' }\r\n        ],\r\n        Check_Type: [\r\n          { required: true, message: '请填写完整表单', trigger: 'change' }\r\n        ],\r\n        Change_Check_Type: [\r\n          { required: true, validator: this.Check_Type_rules, message: '请填写完整表单', trigger: 'change' }\r\n        ],\r\n        Check_Style: [\r\n          { required: true, message: '请填写完整表单', trigger: 'change' }\r\n        ]\r\n      },\r\n      rules_Zl: { required: true, message: '请填写完整表单', trigger: 'bur' },\r\n      rules_Tc: { required: true, message: '请填写完整表单', trigger: 'bur' },\r\n      ZL_UserIds_Rules: [\r\n        { required: true, validator: this.Check_ZL_UserIds, message: '请填写完整表单', trigger: 'change' }\r\n      ],\r\n      TC_UserIds_Rules: [\r\n        { required: true, validator: this.Check_TC_UserIds, message: '请填写完整表单', trigger: 'change' }\r\n      ],\r\n      title: '',\r\n      editInfo: {},\r\n      QualityNodeList: [{ Name: '入库' }, { Name: '出库' }], // 质检节点列表\r\n      CheckTypeList: [\r\n        {\r\n          Name: '质量',\r\n          Id: 1\r\n        },\r\n        {\r\n          Name: '探伤',\r\n          Id: 2\r\n        }\r\n      ], // 质检类型\r\n      UserList: [], // 质量员，探伤人员\r\n      CheckStyleList: [\r\n        {\r\n          Name: '抽检',\r\n          Id: 0\r\n        },\r\n        {\r\n          Name: '全检',\r\n          Id: 1\r\n        }\r\n      ] // 质检方式\r\n    }\r\n  },\r\n  computed: {\r\n    Node_Code_Com: function() {\r\n      if (this.form.Node_Code) {\r\n        return true\r\n      } else {\r\n        return false\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getFactoryPeoplelist()\r\n  },\r\n  methods: {\r\n    Check_ZL_UserIds(rule, value, callback) {\r\n      if (this.form.Change_Check_Type.includes(1) && this.form.ZL_UserIds.length === 0) {\r\n        callback(new Error('请填写完整表单'))\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    Check_TC_UserIds(rule, value, callback) {\r\n      if (!this.Node_Code_Com && !(this.form.Change_Check_Type[0] != 2 && this.form.Change_Check_Type.length != 2) && this.form.TC_UserIds.length === 0) {\r\n        callback(new Error('请填写完整表单'))\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    Check_Type_rules(rule, value, callback) {\r\n      if (this.form.Change_Check_Type.length === 0) {\r\n        callback(new Error('请填写完整表单'))\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    SelectType(item) {\r\n      this.$forceUpdate()\r\n      this.form.Change_Check_Type = item\r\n      if (item.length == 1) {\r\n        this.form.Check_Type = item[0]\r\n      } else if (item.length == 2) {\r\n        this.form.Check_Type = -1\r\n      }\r\n\r\n      if (!item.includes(1)) {\r\n        this.form.ZL_UserId = ''\r\n        this.form.ZL_UserIds = []\r\n      }\r\n      if (!item.includes(2)) {\r\n        this.form.TC_UserId = ''\r\n        this.form.TC_UserIds = []\r\n      }\r\n      console.log(this.form.Change_Check_Type)\r\n    },\r\n    removeType(item) {\r\n      console.log(item, 'b')\r\n      // if (item == 1) {\r\n      //   this.form.ZL_UserId = \"\";\r\n      // } else if (item == 2) {\r\n      //   this.form.TC_UserId = \"\";\r\n      // }\r\n    },\r\n    clearType(val) {\r\n      console.log(val)\r\n      this.form.ZL_UserId = ''\r\n      this.form.TC_UserId = ''\r\n      this.form.ZL_UserIds = []\r\n      this.form.TC_UserIds = []\r\n    },\r\n    init(title, checkType, data) {\r\n      this.Check_Object_Id = checkType.Id\r\n      this.Bom_Level = checkType.Code\r\n      this.title = title\r\n      if (title === '编辑') {\r\n        console.log(data)\r\n        this.form.Id = data.Id\r\n        this.getEntityNode(data)\r\n      }\r\n      this.getCheckNode()\r\n    },\r\n    async addCheckNode() {\r\n      const { Demand_Spot_Check_Rate, ...others } = this.form\r\n      const submit = {\r\n        ...others,\r\n        Check_Object_Id: this.Check_Object_Id,\r\n        Bom_Level: this.Bom_Level\r\n      }\r\n      if (this.form.Check_Style === 0) {\r\n        submit.Demand_Spot_Check_Rate = Demand_Spot_Check_Rate\r\n      }\r\n      await SaveNode(submit).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            type: 'success',\r\n            message: '保存成功'\r\n          })\r\n          this.$emit('close')\r\n          this.dialogData = {}\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getFactoryPeoplelist() {\r\n      GetFactoryPeoplelist().then((res) => {\r\n        if (res.IsSucceed) {\r\n          console.log(res.Data)\r\n          this.UserList = res.Data\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 判断是工厂还是项目获取质检节点\r\n    getCheckNode() {\r\n      const Platform =\r\n        localStorage.getItem('Platform') || localStorage.getItem('CurPlatform')\r\n      if (Platform === '2') {\r\n        this.mode = 'factory'\r\n        // this.getFactoryNode();\r\n      }\r\n      // 获取项目/工厂id\r\n      this.ProjectId =\r\n        this.mode === 'factory'\r\n          ? localStorage.getItem('CurReferenceId')\r\n          : this.ProjectId\r\n    },\r\n    // 如果是工厂获取质检节点\r\n    // getFactoryNode() {\r\n    //   GetProcessCodeList({sys_workobject_id:this.Check_Object_Id}).then((res) => {\r\n    //     if (res.IsSucceed) {\r\n    //       let CheckJson = res.Data;\r\n    //       CheckJson.push({ Name: \"入库\" }, { Name: \"出库\" });\r\n    //       console.log(CheckJson);\r\n    //       this.QualityNodeList = CheckJson;\r\n    //       console.log(this.QualityNodeList);\r\n    //     } else {\r\n    //       this.$message({\r\n    //         type: \"error\",\r\n    //         message: res.Message,\r\n    //       });\r\n    //     }\r\n    //   });\r\n    // },\r\n\r\n    // 质检节点获取质检节点名\r\n    changeNodeCode(val) {\r\n      this.form = {\r\n        Node_Code: '',\r\n        Change_Check_Type: [],\r\n        Display_Name: '',\r\n        TC_UserId: '',\r\n        ZL_UserId: '',\r\n        TC_UserIds: [],\r\n        ZL_UserIds: [],\r\n        Check_Style: ''\r\n      }\r\n      this.form.Display_Name = val\r\n      this.form.Node_Code = null\r\n      // this.form.Change_Check_Type = [];\r\n      // try {\r\n      //   this.form.Node_Code = this.QualityNodeList.find((v) => {\r\n      //     return v.Name == val;\r\n      //   }).Id;\r\n      // } catch (err) {\r\n      //   this.form.Node_Code = null;\r\n      // }\r\n      // console.log\r\n      // let arr = {};\r\n      // arr = this.QualityNodeList.find((v) => {\r\n      //   return v.Name == val;\r\n      // });\r\n      // console.log(arr);\r\n      // if (arr) {\r\n      //   this.form.Check_Style = arr.Check_Style ? Number(arr.Check_Style) : \"\";\r\n      //   arr.Is_Need_TC &&(this.form.Change_Check_Type.push(2))\r\n      //   arr.Is_Need_ZL &&( this.form.Change_Check_Type.push(1));\r\n      //   this.SelectType(this.form.Change_Check_Type);\r\n\r\n      //   this.form.ZL_UserId = arr.ZL_Check_UserId ? arr.ZL_Check_UserId: \"\";\r\n      //   this.form.TC_UserId = arr.TC_Check_UserId ? arr.TC_Check_UserId : \"\"\r\n      //   console.log(this.form.ZL_UserId)\r\n      // }\r\n    },\r\n    changeZLUser(val) {\r\n      console.log(val)\r\n      // 解决下拉框回显问题\r\n      this.$forceUpdate()\r\n      this.form.ZL_UserId = ''\r\n      for (let i = 0; i < val.length; i++) {\r\n        if (i == val.length - 1) {\r\n          this.form.ZL_UserId += val[i]\r\n        } else {\r\n          this.form.ZL_UserId += val[i] + ','\r\n        }\r\n      }\r\n      console.log(this.form.ZL_UserId, 'this.form.ZL_UserId ')\r\n    },\r\n    changeTCUser(val) {\r\n      this.$forceUpdate()\r\n      this.form.TC_UserId = ''\r\n      for (let i = 0; i < val.length; i++) {\r\n        if (i == val.length - 1) {\r\n          this.form.TC_UserId += val[i]\r\n        } else {\r\n          this.form.TC_UserId += val[i] + ','\r\n        }\r\n      }\r\n      // 解决下拉框回显问题\r\n\r\n      console.log(this.form.TC_UserId, 'this.form.TC_UserId ')\r\n    },\r\n    getEntityNode(data) {\r\n      GetEntityNode({ id: data.Id }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          console.log(res.Data)\r\n          this.form = res.Data[0]\r\n          this.form.Change_Check_Type = []\r\n          if (this.form.Check_Type == 1 || this.form.Check_Type == 2) {\r\n            this.form.Change_Check_Type.push(this.form.Check_Type)\r\n          } else if (this.form.Check_Type == -1) {\r\n            this.form.Change_Check_Type = [1, 2]\r\n          } else {\r\n            this.form.Change_Check_Type = []\r\n          }\r\n          this.form.ZL_UserIds = this.form.ZL_UserId ? this.form.ZL_UserId.split(',') : []\r\n          this.form.TC_UserIds = this.form.TC_UserId ? this.form.TC_UserId.split(',') : []\r\n          console.log(this.form.ZL_UserIds, this.form.TC_UserId)\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleSubmit(form) {\r\n      this.$refs[form].validate((valid) => {\r\n        if (valid) {\r\n          this.addCheckNode()\r\n        } else {\r\n          return false\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped></style>\r\n"]}]}
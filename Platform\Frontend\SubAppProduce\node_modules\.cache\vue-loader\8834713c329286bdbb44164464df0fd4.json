{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\NodeDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\NodeDialog.vue", "mtime": 1757666764694}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IFNhdmVOb2RlIH0gZnJvbSAnQC9hcGkvUFJPL2ZhY3RvcnljaGVjaycKaW1wb3J0IHsgR2V0RW50aXR5Tm9kZSB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snCi8vIGltcG9ydCB7IFNhdmVDaGVja1R5cGUgfSBmcm9tICJAL2FwaS9QUk8vZmFjdG9yeWNoZWNrIjsKaW1wb3J0IHsgR2V0RmFjdG9yeVBlb3BsZWxpc3QgfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJwppbXBvcnQgeyBHZXRQcm9jZXNzQ29kZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJwpleHBvcnQgZGVmYXVsdCB7CiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIG1vZGU6ICcnLCAvLyDljLrliIbpobnnm67lkozlt6XljoIKICAgICAgUHJvamVjdElkOiAnJywgLy8g6aG555uuSWQKICAgICAgQ2hlY2tfT2JqZWN0X0lkOiAnJywKICAgICAgQm9tX0xldmVsOiAnJywKICAgICAgZm9ybTogewogICAgICAgIE5vZGVfQ29kZTogJycsCiAgICAgICAgQ2hhbmdlX0NoZWNrX1R5cGU6IFtdLAogICAgICAgIERpc3BsYXlfTmFtZTogJycsCiAgICAgICAgVENfVXNlcklkOiAnJywKICAgICAgICBaTF9Vc2VySWQ6ICcnLAogICAgICAgIERlbWFuZF9TcG90X0NoZWNrX1JhdGU6IHVuZGVmaW5lZCwKICAgICAgICBSZXF1aXJlbWVudF9TcG90X0NoZWNrX1JhdGU6IHVuZGVmaW5lZCwKICAgICAgICBUQ19Vc2VySWRzOiBbXSwKICAgICAgICBaTF9Vc2VySWRzOiBbXSwKICAgICAgICBDaGVja19TdHlsZTogJycsCiAgICAgICAgVHNfUmVxdWlyZV9UaW1lOiAnJwogICAgICB9LAoKICAgICAgcnVsZXM6IHsKICAgICAgICBEaXNwbGF5X05hbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgICAgXSwKICAgICAgICBDaGVja19UeXBlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+35aGr5YaZ5a6M5pW06KGo5Y2VJywgdHJpZ2dlcjogJ2NoYW5nZScgfQogICAgICAgIF0sCiAgICAgICAgQ2hhbmdlX0NoZWNrX1R5cGU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHZhbGlkYXRvcjogdGhpcy5DaGVja19UeXBlX3J1bGVzLCBtZXNzYWdlOiAn6K+35aGr5YaZ5a6M5pW06KGo5Y2VJywgdHJpZ2dlcjogJ2NoYW5nZScgfQogICAgICAgIF0sCiAgICAgICAgQ2hlY2tfU3R5bGU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgICAgXQogICAgICB9LAogICAgICBydWxlc19abDogeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOihqOWNlScsIHRyaWdnZXI6ICdidXInIH0sCiAgICAgIHJ1bGVzX1RjOiB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+35aGr5YaZ5a6M5pW06KGo5Y2VJywgdHJpZ2dlcjogJ2J1cicgfSwKICAgICAgWkxfVXNlcklkc19SdWxlczogWwogICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHZhbGlkYXRvcjogdGhpcy5DaGVja19aTF9Vc2VySWRzLCBtZXNzYWdlOiAn6K+35aGr5YaZ5a6M5pW06KGo5Y2VJywgdHJpZ2dlcjogJ2NoYW5nZScgfQogICAgICBdLAogICAgICBUQ19Vc2VySWRzX1J1bGVzOiBbCiAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdmFsaWRhdG9yOiB0aGlzLkNoZWNrX1RDX1VzZXJJZHMsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgIF0sCiAgICAgIHRpdGxlOiAnJywKICAgICAgZWRpdEluZm86IHt9LAogICAgICBRdWFsaXR5Tm9kZUxpc3Q6IFt7IE5hbWU6ICflhaXlupMnIH0sIHsgTmFtZTogJ+WHuuW6kycgfV0sIC8vIOi0qOajgOiKgueCueWIl+ihqAogICAgICBDaGVja1R5cGVMaXN0OiBbCiAgICAgICAgewogICAgICAgICAgTmFtZTogJ+i0qOmHjycsCiAgICAgICAgICBJZDogMQogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgTmFtZTogJ+aOouS8pCcsCiAgICAgICAgICBJZDogMgogICAgICAgIH0KICAgICAgXSwgLy8g6LSo5qOA57G75Z6LCiAgICAgIFVzZXJMaXN0OiBbXSwgLy8g6LSo6YeP5ZGY77yM5o6i5Lyk5Lq65ZGYCiAgICAgIENoZWNrU3R5bGVMaXN0OiBbCiAgICAgICAgewogICAgICAgICAgTmFtZTogJ+aKveajgCcsCiAgICAgICAgICBJZDogMAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgTmFtZTogJ+WFqOajgCcsCiAgICAgICAgICBJZDogMQogICAgICAgIH0KICAgICAgXSAvLyDotKjmo4DmlrnlvI8KICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICBOb2RlX0NvZGVfQ29tOiBmdW5jdGlvbigpIHsKICAgICAgaWYgKHRoaXMuZm9ybS5Ob2RlX0NvZGUpIHsKICAgICAgICByZXR1cm4gdHJ1ZQogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiBmYWxzZQogICAgICB9CiAgICB9CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5nZXRGYWN0b3J5UGVvcGxlbGlzdCgpCiAgfSwKICBtZXRob2RzOiB7CiAgICBDaGVja19aTF9Vc2VySWRzKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgewogICAgICBpZiAodGhpcy5mb3JtLkNoYW5nZV9DaGVja19UeXBlLmluY2x1ZGVzKDEpICYmIHRoaXMuZm9ybS5aTF9Vc2VySWRzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6K+35aGr5YaZ5a6M5pW06KGo5Y2VJykpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgY2FsbGJhY2soKQogICAgICB9CiAgICB9LAogICAgQ2hlY2tfVENfVXNlcklkcyhydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKCF0aGlzLk5vZGVfQ29kZV9Db20gJiYgISh0aGlzLmZvcm0uQ2hhbmdlX0NoZWNrX1R5cGVbMF0gIT0gMiAmJiB0aGlzLmZvcm0uQ2hhbmdlX0NoZWNrX1R5cGUubGVuZ3RoICE9IDIpICYmIHRoaXMuZm9ybS5UQ19Vc2VySWRzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6K+35aGr5YaZ5a6M5pW06KGo5Y2VJykpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgY2FsbGJhY2soKQogICAgICB9CiAgICB9LAogICAgQ2hlY2tfVHlwZV9ydWxlcyhydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZS5sZW5ndGggPT09IDApIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+ivt+Whq+WGmeWujOaVtOihqOWNlScpKQogICAgICB9IGVsc2UgewogICAgICAgIGNhbGxiYWNrKCkKICAgICAgfQogICAgfSwKICAgIFNlbGVjdFR5cGUoaXRlbSkgewogICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpCiAgICAgIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZSA9IGl0ZW0KICAgICAgaWYgKGl0ZW0ubGVuZ3RoID09IDEpIHsKICAgICAgICB0aGlzLmZvcm0uQ2hlY2tfVHlwZSA9IGl0ZW1bMF0KICAgICAgfSBlbHNlIGlmIChpdGVtLmxlbmd0aCA9PSAyKSB7CiAgICAgICAgdGhpcy5mb3JtLkNoZWNrX1R5cGUgPSAtMQogICAgICB9CgogICAgICBpZiAoIWl0ZW0uaW5jbHVkZXMoMSkpIHsKICAgICAgICB0aGlzLmZvcm0uWkxfVXNlcklkID0gJycKICAgICAgICB0aGlzLmZvcm0uWkxfVXNlcklkcyA9IFtdCiAgICAgIH0KICAgICAgaWYgKCFpdGVtLmluY2x1ZGVzKDIpKSB7CiAgICAgICAgdGhpcy5mb3JtLlRDX1VzZXJJZCA9ICcnCiAgICAgICAgdGhpcy5mb3JtLlRDX1VzZXJJZHMgPSBbXQogICAgICB9CiAgICAgIGNvbnNvbGUubG9nKHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZSkKICAgIH0sCiAgICByZW1vdmVUeXBlKGl0ZW0pIHsKICAgICAgY29uc29sZS5sb2coaXRlbSwgJ2InKQogICAgICAvLyBpZiAoaXRlbSA9PSAxKSB7CiAgICAgIC8vICAgdGhpcy5mb3JtLlpMX1VzZXJJZCA9ICIiOwogICAgICAvLyB9IGVsc2UgaWYgKGl0ZW0gPT0gMikgewogICAgICAvLyAgIHRoaXMuZm9ybS5UQ19Vc2VySWQgPSAiIjsKICAgICAgLy8gfQogICAgfSwKICAgIGNsZWFyVHlwZSh2YWwpIHsKICAgICAgY29uc29sZS5sb2codmFsKQogICAgICB0aGlzLmZvcm0uWkxfVXNlcklkID0gJycKICAgICAgdGhpcy5mb3JtLlRDX1VzZXJJZCA9ICcnCiAgICAgIHRoaXMuZm9ybS5aTF9Vc2VySWRzID0gW10KICAgICAgdGhpcy5mb3JtLlRDX1VzZXJJZHMgPSBbXQogICAgfSwKICAgIGluaXQodGl0bGUsIGNoZWNrVHlwZSwgZGF0YSkgewogICAgICB0aGlzLkNoZWNrX09iamVjdF9JZCA9IGNoZWNrVHlwZS5JZAogICAgICB0aGlzLkJvbV9MZXZlbCA9IGNoZWNrVHlwZS5Db2RlCiAgICAgIHRoaXMudGl0bGUgPSB0aXRsZQogICAgICBpZiAodGl0bGUgPT09ICfnvJbovpEnKSB7CiAgICAgICAgY29uc29sZS5sb2coZGF0YSkKICAgICAgICB0aGlzLmZvcm0uSWQgPSBkYXRhLklkCiAgICAgICAgdGhpcy5nZXRFbnRpdHlOb2RlKGRhdGEpCiAgICAgIH0KICAgICAgdGhpcy5nZXRDaGVja05vZGUoKQogICAgfSwKICAgIGFzeW5jIGFkZENoZWNrTm9kZSgpIHsKICAgICAgY29uc3QgeyBEZW1hbmRfU3BvdF9DaGVja19SYXRlLCAuLi5vdGhlcnMgfSA9IHRoaXMuZm9ybQogICAgICBjb25zdCBzdWJtaXQgPSB7CiAgICAgICAgLi4ub3RoZXJzLAogICAgICAgIENoZWNrX09iamVjdF9JZDogdGhpcy5DaGVja19PYmplY3RfSWQsCiAgICAgICAgQm9tX0xldmVsOiB0aGlzLkJvbV9MZXZlbAogICAgICB9CiAgICAgIGlmICh0aGlzLmZvcm0uQ2hlY2tfU3R5bGUgPT09IDApIHsKICAgICAgICBzdWJtaXQuRGVtYW5kX1Nwb3RfQ2hlY2tfUmF0ZSA9IERlbWFuZF9TcG90X0NoZWNrX1JhdGUKICAgICAgfQogICAgICBhd2FpdCBTYXZlTm9kZShzdWJtaXQpLnRoZW4oKHJlcykgPT4gewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICBtZXNzYWdlOiAn5L+d5a2Y5oiQ5YqfJwogICAgICAgICAgfSkKICAgICAgICAgIHRoaXMuJGVtaXQoJ2Nsb3NlJykKICAgICAgICAgIHRoaXMuZGlhbG9nRGF0YSA9IHt9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZQogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgZ2V0RmFjdG9yeVBlb3BsZWxpc3QoKSB7CiAgICAgIEdldEZhY3RvcnlQZW9wbGVsaXN0KCkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIGNvbnNvbGUubG9nKHJlcy5EYXRhKQogICAgICAgICAgdGhpcy5Vc2VyTGlzdCA9IHJlcy5EYXRhCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZQogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgLy8g5Yik5pat5piv5bel5Y6C6L+Y5piv6aG555uu6I635Y+W6LSo5qOA6IqC54K5CiAgICBnZXRDaGVja05vZGUoKSB7CiAgICAgIGNvbnN0IFBsYXRmb3JtID0KICAgICAgICBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnUGxhdGZvcm0nKSB8fCBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnQ3VyUGxhdGZvcm0nKQogICAgICBpZiAoUGxhdGZvcm0gPT09ICcyJykgewogICAgICAgIHRoaXMubW9kZSA9ICdmYWN0b3J5JwogICAgICAgIC8vIHRoaXMuZ2V0RmFjdG9yeU5vZGUoKTsKICAgICAgfQogICAgICAvLyDojrflj5bpobnnm64v5bel5Y6CaWQKICAgICAgdGhpcy5Qcm9qZWN0SWQgPQogICAgICAgIHRoaXMubW9kZSA9PT0gJ2ZhY3RvcnknCiAgICAgICAgICA/IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdDdXJSZWZlcmVuY2VJZCcpCiAgICAgICAgICA6IHRoaXMuUHJvamVjdElkCiAgICB9LAogICAgLy8g5aaC5p6c5piv5bel5Y6C6I635Y+W6LSo5qOA6IqC54K5CiAgICAvLyBnZXRGYWN0b3J5Tm9kZSgpIHsKICAgIC8vICAgR2V0UHJvY2Vzc0NvZGVMaXN0KHtzeXNfd29ya29iamVjdF9pZDp0aGlzLkNoZWNrX09iamVjdF9JZH0pLnRoZW4oKHJlcykgPT4gewogICAgLy8gICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAvLyAgICAgICBsZXQgQ2hlY2tKc29uID0gcmVzLkRhdGE7CiAgICAvLyAgICAgICBDaGVja0pzb24ucHVzaCh7IE5hbWU6ICLlhaXlupMiIH0sIHsgTmFtZTogIuWHuuW6kyIgfSk7CiAgICAvLyAgICAgICBjb25zb2xlLmxvZyhDaGVja0pzb24pOwogICAgLy8gICAgICAgdGhpcy5RdWFsaXR5Tm9kZUxpc3QgPSBDaGVja0pzb247CiAgICAvLyAgICAgICBjb25zb2xlLmxvZyh0aGlzLlF1YWxpdHlOb2RlTGlzdCk7CiAgICAvLyAgICAgfSBlbHNlIHsKICAgIC8vICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgLy8gICAgICAgICB0eXBlOiAiZXJyb3IiLAogICAgLy8gICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgIC8vICAgICAgIH0pOwogICAgLy8gICAgIH0KICAgIC8vICAgfSk7CiAgICAvLyB9LAoKICAgIC8vIOi0qOajgOiKgueCueiOt+WPlui0qOajgOiKgueCueWQjQogICAgY2hhbmdlTm9kZUNvZGUodmFsKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBOb2RlX0NvZGU6ICcnLAogICAgICAgIENoYW5nZV9DaGVja19UeXBlOiBbXSwKICAgICAgICBEaXNwbGF5X05hbWU6ICcnLAogICAgICAgIFRDX1VzZXJJZDogJycsCiAgICAgICAgWkxfVXNlcklkOiAnJywKICAgICAgICBUQ19Vc2VySWRzOiBbXSwKICAgICAgICBaTF9Vc2VySWRzOiBbXSwKICAgICAgICBDaGVja19TdHlsZTogJycKICAgICAgfQogICAgICB0aGlzLmZvcm0uRGlzcGxheV9OYW1lID0gdmFsCiAgICAgIHRoaXMuZm9ybS5Ob2RlX0NvZGUgPSBudWxsCiAgICAgIC8vIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZSA9IFtdOwogICAgICAvLyB0cnkgewogICAgICAvLyAgIHRoaXMuZm9ybS5Ob2RlX0NvZGUgPSB0aGlzLlF1YWxpdHlOb2RlTGlzdC5maW5kKCh2KSA9PiB7CiAgICAgIC8vICAgICByZXR1cm4gdi5OYW1lID09IHZhbDsKICAgICAgLy8gICB9KS5JZDsKICAgICAgLy8gfSBjYXRjaCAoZXJyKSB7CiAgICAgIC8vICAgdGhpcy5mb3JtLk5vZGVfQ29kZSA9IG51bGw7CiAgICAgIC8vIH0KICAgICAgLy8gY29uc29sZS5sb2cKICAgICAgLy8gbGV0IGFyciA9IHt9OwogICAgICAvLyBhcnIgPSB0aGlzLlF1YWxpdHlOb2RlTGlzdC5maW5kKCh2KSA9PiB7CiAgICAgIC8vICAgcmV0dXJuIHYuTmFtZSA9PSB2YWw7CiAgICAgIC8vIH0pOwogICAgICAvLyBjb25zb2xlLmxvZyhhcnIpOwogICAgICAvLyBpZiAoYXJyKSB7CiAgICAgIC8vICAgdGhpcy5mb3JtLkNoZWNrX1N0eWxlID0gYXJyLkNoZWNrX1N0eWxlID8gTnVtYmVyKGFyci5DaGVja19TdHlsZSkgOiAiIjsKICAgICAgLy8gICBhcnIuSXNfTmVlZF9UQyAmJih0aGlzLmZvcm0uQ2hhbmdlX0NoZWNrX1R5cGUucHVzaCgyKSkKICAgICAgLy8gICBhcnIuSXNfTmVlZF9aTCAmJiggdGhpcy5mb3JtLkNoYW5nZV9DaGVja19UeXBlLnB1c2goMSkpOwogICAgICAvLyAgIHRoaXMuU2VsZWN0VHlwZSh0aGlzLmZvcm0uQ2hhbmdlX0NoZWNrX1R5cGUpOwoKICAgICAgLy8gICB0aGlzLmZvcm0uWkxfVXNlcklkID0gYXJyLlpMX0NoZWNrX1VzZXJJZCA/IGFyci5aTF9DaGVja19Vc2VySWQ6ICIiOwogICAgICAvLyAgIHRoaXMuZm9ybS5UQ19Vc2VySWQgPSBhcnIuVENfQ2hlY2tfVXNlcklkID8gYXJyLlRDX0NoZWNrX1VzZXJJZCA6ICIiCiAgICAgIC8vICAgY29uc29sZS5sb2codGhpcy5mb3JtLlpMX1VzZXJJZCkKICAgICAgLy8gfQogICAgfSwKICAgIGNoYW5nZVpMVXNlcih2YWwpIHsKICAgICAgY29uc29sZS5sb2codmFsKQogICAgICAvLyDop6PlhrPkuIvmi4nmoYblm57mmL7pl67popgKICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKQogICAgICB0aGlzLmZvcm0uWkxfVXNlcklkID0gJycKICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB2YWwubGVuZ3RoOyBpKyspIHsKICAgICAgICBpZiAoaSA9PSB2YWwubGVuZ3RoIC0gMSkgewogICAgICAgICAgdGhpcy5mb3JtLlpMX1VzZXJJZCArPSB2YWxbaV0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5mb3JtLlpMX1VzZXJJZCArPSB2YWxbaV0gKyAnLCcKICAgICAgICB9CiAgICAgIH0KICAgICAgY29uc29sZS5sb2codGhpcy5mb3JtLlpMX1VzZXJJZCwgJ3RoaXMuZm9ybS5aTF9Vc2VySWQgJykKICAgIH0sCiAgICBjaGFuZ2VUQ1VzZXIodmFsKSB7CiAgICAgIHRoaXMuJGZvcmNlVXBkYXRlKCkKICAgICAgdGhpcy5mb3JtLlRDX1VzZXJJZCA9ICcnCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdmFsLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgaWYgKGkgPT0gdmFsLmxlbmd0aCAtIDEpIHsKICAgICAgICAgIHRoaXMuZm9ybS5UQ19Vc2VySWQgKz0gdmFsW2ldCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuZm9ybS5UQ19Vc2VySWQgKz0gdmFsW2ldICsgJywnCiAgICAgICAgfQogICAgICB9CiAgICAgIC8vIOino+WGs+S4i+aLieahhuWbnuaYvumXrumimAoKICAgICAgY29uc29sZS5sb2codGhpcy5mb3JtLlRDX1VzZXJJZCwgJ3RoaXMuZm9ybS5UQ19Vc2VySWQgJykKICAgIH0sCiAgICBnZXRFbnRpdHlOb2RlKGRhdGEpIHsKICAgICAgR2V0RW50aXR5Tm9kZSh7IGlkOiBkYXRhLklkIH0pLnRoZW4oKHJlcykgPT4gewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICBjb25zb2xlLmxvZyhyZXMuRGF0YSkKICAgICAgICAgIHRoaXMuZm9ybSA9IHJlcy5EYXRhWzBdCiAgICAgICAgICB0aGlzLmZvcm0uQ2hhbmdlX0NoZWNrX1R5cGUgPSBbXQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5DaGVja19UeXBlID09IDEgfHwgdGhpcy5mb3JtLkNoZWNrX1R5cGUgPT0gMikgewogICAgICAgICAgICB0aGlzLmZvcm0uQ2hhbmdlX0NoZWNrX1R5cGUucHVzaCh0aGlzLmZvcm0uQ2hlY2tfVHlwZSkKICAgICAgICAgIH0gZWxzZSBpZiAodGhpcy5mb3JtLkNoZWNrX1R5cGUgPT0gLTEpIHsKICAgICAgICAgICAgdGhpcy5mb3JtLkNoYW5nZV9DaGVja19UeXBlID0gWzEsIDJdCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLmZvcm0uQ2hhbmdlX0NoZWNrX1R5cGUgPSBbXQogICAgICAgICAgfQogICAgICAgICAgdGhpcy5mb3JtLlpMX1VzZXJJZHMgPSB0aGlzLmZvcm0uWkxfVXNlcklkID8gdGhpcy5mb3JtLlpMX1VzZXJJZC5zcGxpdCgnLCcpIDogW10KICAgICAgICAgIHRoaXMuZm9ybS5UQ19Vc2VySWRzID0gdGhpcy5mb3JtLlRDX1VzZXJJZCA/IHRoaXMuZm9ybS5UQ19Vc2VySWQuc3BsaXQoJywnKSA6IFtdCiAgICAgICAgICBjb25zb2xlLmxvZyh0aGlzLmZvcm0uWkxfVXNlcklkcywgdGhpcy5mb3JtLlRDX1VzZXJJZCkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICBoYW5kbGVTdWJtaXQoZm9ybSkgewogICAgICB0aGlzLiRyZWZzW2Zvcm1dLnZhbGlkYXRlKCh2YWxpZCkgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgdGhpcy5hZGRDaGVja05vZGUoKQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICByZXR1cm4gZmFsc2UKICAgICAgICB9CiAgICAgIH0pCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["NodeDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "NodeDialog.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup/components/Dialog", "sourcesContent": ["<template>\n  <div>\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"120px\">\n      <el-row>\n        <el-col :span=\"12\">\n          <el-form-item label=\"质检节点\" prop=\"Display_Name\">\n            <el-select\n              v-model=\"form.Display_Name\"\n              :disabled=\"Node_Code_Com\"\n              clearable\n              style=\"width: 100%\"\n              filterable\n              allow-create\n              placeholder=\"请输入质检节点\"\n              @change=\"changeNodeCode\"\n            >\n              <el-option\n                v-for=\"(item, index) in QualityNodeList\"\n                :key=\"index\"\n                :label=\"item.Name\"\n                :value=\"item.Name\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"专检类型\" prop=\"Change_Check_Type\">\n            <el-select\n              v-model=\"form.Change_Check_Type\"\n              style=\"width: 100%\"\n              placeholder=\"请选择专检类型\"\n              multiple\n              :disabled=\"Node_Code_Com\"\n              @change=\"SelectType\"\n              @remove-tag=\"removeType\"\n            >\n              <el-option\n                v-for=\"(item, index) in CheckTypeList\"\n                :key=\"index\"\n                :label=\"item.Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item\n            label=\"质量员\"\n            prop=\"ZL_UserIds\"\n            :rules=\"ZL_UserIds_Rules\"\n          >\n            <el-select\n              v-model=\"form.ZL_UserIds\"\n              filterable\n              clearable\n              multiple\n              style=\"width: 100%\"\n              placeholder=\"请选择质量员\"\n              :disabled=\"\n                Node_Code_Com ||\n                  (form.Change_Check_Type[0] != 1 &&\n                    form.Change_Check_Type.length != 2)\n              \"\n              @change=\"changeZLUser\"\n            >\n              <el-option\n                v-for=\"(item, index) in UserList\"\n                :key=\"index\"\n                :label=\"item.Display_Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item\n            label=\"探伤员\"\n            prop=\"TC_UserIds\"\n            :rules=\"TC_UserIds_Rules\"\n          >\n            <el-select\n              v-model=\"form.TC_UserIds\"\n              filterable\n              clearable\n              multiple\n              style=\"width: 100%\"\n              :disabled=\"\n                Node_Code_Com ||\n                  (form.Change_Check_Type[0] != 2 &&\n                    form.Change_Check_Type.length != 2)\n              \"\n              placeholder=\"请选择探伤员\"\n              @change=\"changeTCUser\"\n            >\n              <el-option\n                v-for=\"(item, index) in UserList\"\n                :key=\"index\"\n                :label=\"item.Display_Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"专检方式\" prop=\"Check_Style\">\n            <el-select\n              v-model=\"form.Check_Style\"\n              clearable\n              :disabled=\"Node_Code_Com\"\n              style=\"width: 100%\"\n              placeholder=\"请选择专检方式\"\n            >\n              <el-option\n                v-for=\"(item, index) in CheckStyleList\"\n                :key=\"index\"\n                :label=\"item.Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Change_Check_Type.includes(1)\" :span=\"12\">\n          <el-form-item label=\"探伤要求时间(h)\" prop=\"Ts_Require_Time\">\n            <el-input v-model=\"form.Ts_Require_Time\" />\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Check_Style===0\" :span=\"12\">\n          <el-form-item label=\"要求合格率(%)\" prop=\"Demand_Spot_Check_Rate\">\n            <el-input-number v-model=\"form.Demand_Spot_Check_Rate\" :min=\"0\" :max=\"100\" class=\"cs-number-btn-hidden w100\" placeholder=\"请输入\" clearable />\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Check_Style===0\" :span=\"12\">\n          <el-form-item label=\"要求抽检率(%)\" prop=\"Requirement_Spot_Check_Rate\">\n            <el-input-number v-model=\"form.Requirement_Spot_Check_Rate\" :min=\"0\" :max=\"100\" class=\"cs-number-btn-hidden w100\" placeholder=\"请输入\" clearable />\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item style=\"text-align: right\">\n            <el-button @click=\"$emit('close')\">关 闭</el-button>\n            <el-button\n              type=\"primary\"\n              @click=\"handleSubmit('form')\"\n            >确 定</el-button>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n  </div>\n</template>\n\n<script>\nimport { SaveNode } from '@/api/PRO/factorycheck'\nimport { GetEntityNode } from '@/api/PRO/factorycheck'\n// import { SaveCheckType } from \"@/api/PRO/factorycheck\";\nimport { GetFactoryPeoplelist } from '@/api/PRO/factorycheck'\nimport { GetProcessCodeList } from '@/api/PRO/factorycheck'\nexport default {\n  data() {\n    return {\n      mode: '', // 区分项目和工厂\n      ProjectId: '', // 项目Id\n      Check_Object_Id: '',\n      Bom_Level: '',\n      form: {\n        Node_Code: '',\n        Change_Check_Type: [],\n        Display_Name: '',\n        TC_UserId: '',\n        ZL_UserId: '',\n        Demand_Spot_Check_Rate: undefined,\n        Requirement_Spot_Check_Rate: undefined,\n        TC_UserIds: [],\n        ZL_UserIds: [],\n        Check_Style: '',\n        Ts_Require_Time: ''\n      },\n\n      rules: {\n        Display_Name: [\n          { required: true, message: '请填写完整表单', trigger: 'change' }\n        ],\n        Check_Type: [\n          { required: true, message: '请填写完整表单', trigger: 'change' }\n        ],\n        Change_Check_Type: [\n          { required: true, validator: this.Check_Type_rules, message: '请填写完整表单', trigger: 'change' }\n        ],\n        Check_Style: [\n          { required: true, message: '请填写完整表单', trigger: 'change' }\n        ]\n      },\n      rules_Zl: { required: true, message: '请填写完整表单', trigger: 'bur' },\n      rules_Tc: { required: true, message: '请填写完整表单', trigger: 'bur' },\n      ZL_UserIds_Rules: [\n        { required: true, validator: this.Check_ZL_UserIds, message: '请填写完整表单', trigger: 'change' }\n      ],\n      TC_UserIds_Rules: [\n        { required: true, validator: this.Check_TC_UserIds, message: '请填写完整表单', trigger: 'change' }\n      ],\n      title: '',\n      editInfo: {},\n      QualityNodeList: [{ Name: '入库' }, { Name: '出库' }], // 质检节点列表\n      CheckTypeList: [\n        {\n          Name: '质量',\n          Id: 1\n        },\n        {\n          Name: '探伤',\n          Id: 2\n        }\n      ], // 质检类型\n      UserList: [], // 质量员，探伤人员\n      CheckStyleList: [\n        {\n          Name: '抽检',\n          Id: 0\n        },\n        {\n          Name: '全检',\n          Id: 1\n        }\n      ] // 质检方式\n    }\n  },\n  computed: {\n    Node_Code_Com: function() {\n      if (this.form.Node_Code) {\n        return true\n      } else {\n        return false\n      }\n    }\n  },\n  mounted() {\n    this.getFactoryPeoplelist()\n  },\n  methods: {\n    Check_ZL_UserIds(rule, value, callback) {\n      if (this.form.Change_Check_Type.includes(1) && this.form.ZL_UserIds.length === 0) {\n        callback(new Error('请填写完整表单'))\n      } else {\n        callback()\n      }\n    },\n    Check_TC_UserIds(rule, value, callback) {\n      if (!this.Node_Code_Com && !(this.form.Change_Check_Type[0] != 2 && this.form.Change_Check_Type.length != 2) && this.form.TC_UserIds.length === 0) {\n        callback(new Error('请填写完整表单'))\n      } else {\n        callback()\n      }\n    },\n    Check_Type_rules(rule, value, callback) {\n      if (this.form.Change_Check_Type.length === 0) {\n        callback(new Error('请填写完整表单'))\n      } else {\n        callback()\n      }\n    },\n    SelectType(item) {\n      this.$forceUpdate()\n      this.form.Change_Check_Type = item\n      if (item.length == 1) {\n        this.form.Check_Type = item[0]\n      } else if (item.length == 2) {\n        this.form.Check_Type = -1\n      }\n\n      if (!item.includes(1)) {\n        this.form.ZL_UserId = ''\n        this.form.ZL_UserIds = []\n      }\n      if (!item.includes(2)) {\n        this.form.TC_UserId = ''\n        this.form.TC_UserIds = []\n      }\n      console.log(this.form.Change_Check_Type)\n    },\n    removeType(item) {\n      console.log(item, 'b')\n      // if (item == 1) {\n      //   this.form.ZL_UserId = \"\";\n      // } else if (item == 2) {\n      //   this.form.TC_UserId = \"\";\n      // }\n    },\n    clearType(val) {\n      console.log(val)\n      this.form.ZL_UserId = ''\n      this.form.TC_UserId = ''\n      this.form.ZL_UserIds = []\n      this.form.TC_UserIds = []\n    },\n    init(title, checkType, data) {\n      this.Check_Object_Id = checkType.Id\n      this.Bom_Level = checkType.Code\n      this.title = title\n      if (title === '编辑') {\n        console.log(data)\n        this.form.Id = data.Id\n        this.getEntityNode(data)\n      }\n      this.getCheckNode()\n    },\n    async addCheckNode() {\n      const { Demand_Spot_Check_Rate, ...others } = this.form\n      const submit = {\n        ...others,\n        Check_Object_Id: this.Check_Object_Id,\n        Bom_Level: this.Bom_Level\n      }\n      if (this.form.Check_Style === 0) {\n        submit.Demand_Spot_Check_Rate = Demand_Spot_Check_Rate\n      }\n      await SaveNode(submit).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            type: 'success',\n            message: '保存成功'\n          })\n          this.$emit('close')\n          this.dialogData = {}\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    getFactoryPeoplelist() {\n      GetFactoryPeoplelist().then((res) => {\n        if (res.IsSucceed) {\n          console.log(res.Data)\n          this.UserList = res.Data\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    // 判断是工厂还是项目获取质检节点\n    getCheckNode() {\n      const Platform =\n        localStorage.getItem('Platform') || localStorage.getItem('CurPlatform')\n      if (Platform === '2') {\n        this.mode = 'factory'\n        // this.getFactoryNode();\n      }\n      // 获取项目/工厂id\n      this.ProjectId =\n        this.mode === 'factory'\n          ? localStorage.getItem('CurReferenceId')\n          : this.ProjectId\n    },\n    // 如果是工厂获取质检节点\n    // getFactoryNode() {\n    //   GetProcessCodeList({sys_workobject_id:this.Check_Object_Id}).then((res) => {\n    //     if (res.IsSucceed) {\n    //       let CheckJson = res.Data;\n    //       CheckJson.push({ Name: \"入库\" }, { Name: \"出库\" });\n    //       console.log(CheckJson);\n    //       this.QualityNodeList = CheckJson;\n    //       console.log(this.QualityNodeList);\n    //     } else {\n    //       this.$message({\n    //         type: \"error\",\n    //         message: res.Message,\n    //       });\n    //     }\n    //   });\n    // },\n\n    // 质检节点获取质检节点名\n    changeNodeCode(val) {\n      this.form = {\n        Node_Code: '',\n        Change_Check_Type: [],\n        Display_Name: '',\n        TC_UserId: '',\n        ZL_UserId: '',\n        TC_UserIds: [],\n        ZL_UserIds: [],\n        Check_Style: ''\n      }\n      this.form.Display_Name = val\n      this.form.Node_Code = null\n      // this.form.Change_Check_Type = [];\n      // try {\n      //   this.form.Node_Code = this.QualityNodeList.find((v) => {\n      //     return v.Name == val;\n      //   }).Id;\n      // } catch (err) {\n      //   this.form.Node_Code = null;\n      // }\n      // console.log\n      // let arr = {};\n      // arr = this.QualityNodeList.find((v) => {\n      //   return v.Name == val;\n      // });\n      // console.log(arr);\n      // if (arr) {\n      //   this.form.Check_Style = arr.Check_Style ? Number(arr.Check_Style) : \"\";\n      //   arr.Is_Need_TC &&(this.form.Change_Check_Type.push(2))\n      //   arr.Is_Need_ZL &&( this.form.Change_Check_Type.push(1));\n      //   this.SelectType(this.form.Change_Check_Type);\n\n      //   this.form.ZL_UserId = arr.ZL_Check_UserId ? arr.ZL_Check_UserId: \"\";\n      //   this.form.TC_UserId = arr.TC_Check_UserId ? arr.TC_Check_UserId : \"\"\n      //   console.log(this.form.ZL_UserId)\n      // }\n    },\n    changeZLUser(val) {\n      console.log(val)\n      // 解决下拉框回显问题\n      this.$forceUpdate()\n      this.form.ZL_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i == val.length - 1) {\n          this.form.ZL_UserId += val[i]\n        } else {\n          this.form.ZL_UserId += val[i] + ','\n        }\n      }\n      console.log(this.form.ZL_UserId, 'this.form.ZL_UserId ')\n    },\n    changeTCUser(val) {\n      this.$forceUpdate()\n      this.form.TC_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i == val.length - 1) {\n          this.form.TC_UserId += val[i]\n        } else {\n          this.form.TC_UserId += val[i] + ','\n        }\n      }\n      // 解决下拉框回显问题\n\n      console.log(this.form.TC_UserId, 'this.form.TC_UserId ')\n    },\n    getEntityNode(data) {\n      GetEntityNode({ id: data.Id }).then((res) => {\n        if (res.IsSucceed) {\n          console.log(res.Data)\n          this.form = res.Data[0]\n          this.form.Change_Check_Type = []\n          if (this.form.Check_Type == 1 || this.form.Check_Type == 2) {\n            this.form.Change_Check_Type.push(this.form.Check_Type)\n          } else if (this.form.Check_Type == -1) {\n            this.form.Change_Check_Type = [1, 2]\n          } else {\n            this.form.Change_Check_Type = []\n          }\n          this.form.ZL_UserIds = this.form.ZL_UserId ? this.form.ZL_UserId.split(',') : []\n          this.form.TC_UserIds = this.form.TC_UserId ? this.form.TC_UserId.split(',') : []\n          console.log(this.form.ZL_UserIds, this.form.TC_UserId)\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    handleSubmit(form) {\n      this.$refs[form].validate((valid) => {\n        if (valid) {\n          this.addCheckNode()\n        } else {\n          return false\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped></style>\n"]}]}
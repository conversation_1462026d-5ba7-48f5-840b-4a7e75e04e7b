{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\factory-attribute\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\factory-attribute\\index.vue", "mtime": 1757468112116}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRVc2VyUGFnZSB9IGZyb20gJ0AvYXBpL3N5cycNCmltcG9ydCB7IEdldEZhY3RvcnlFbnRpdHksIFN1cHBseUZhY3RvcnlJbmZvIH0gZnJvbSAnQC9hcGkvUFJPL2ZhY3RvcnknDQppbXBvcnQgeyBHZXRQcm9mZXNzaW9uYWxUeXBlIH0gZnJvbSAnQC9hcGkvcGxtL21hdGVyaWFsJw0KaW1wb3J0IE9TU1VwbG9hZCBmcm9tICdAL3ZpZXdzL3BsbS9jb21wb25lbnRzL29zc3VwbG9hZCcNCmltcG9ydCBnZXRDb21tb25EYXRhIGZyb20gJ0AvbWl4aW5zL1BSTy9nZXQtY29tbW9uLWRhdGEnDQpleHBvcnQgZGVmYXVsdCB7DQogIGNvbXBvbmVudHM6IHsNCiAgICBPU1NVcGxvYWQNCiAgfSwNCiAgbWl4aW5zOiBbZ2V0Q29tbW9uRGF0YV0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGJ0bkxvYWRpbmc6IGZhbHNlLA0KICAgICAgdXNlck9wdGlvbnM6IFtdLA0KICAgICAgc3JjOiAnJywNCiAgICAgIHNyY0xpc3Q6IFsnJ10sDQogICAgICBudW06IDEyLA0KICAgICAgZm9ybTogew0KICAgICAgICBTY2FuX1VuaXF1ZUNvZGVfRW5hYmxlZDogZmFsc2UsDQogICAgICAgIElzX01hdF9EdXBsaWNhdGU6IGZhbHNlLA0KICAgICAgICBDb21wb25lbnRfU2hpcHBpbmdfQXBwcm92YWw6IGZhbHNlLA0KICAgICAgICBTaGlwcGluZ19BcHByb3ZhbF9Mb3dlckxpbWl0OiB1bmRlZmluZWQsDQogICAgICAgIFNob3J0X05hbWU6ICcnLA0KICAgICAgICBOYW1lOiAnJywNCiAgICAgICAgTWFuYWdlcjogJycsDQogICAgICAgIE1hbmFnZXJfSWQ6ICcnLA0KICAgICAgICBBZGRyZXNzOiAnJywNCiAgICAgICAgRmluYW5jaWFsX1NldHRsZW1lbnRfT3JnYW5pemF0aW9uX0NvZGU6ICcnLA0KICAgICAgICBGaW5hbmNpYWxfU2V0dGxlbWVudF9Pcmdhbml6YXRpb246ICcnLA0KICAgICAgICBQcm9kdWN0aXZpdHk6ICcnLA0KICAgICAgICBDYXRlZ29yeTogJycsDQogICAgICAgIFByb2Zlc3Npb25hbF9Db2RlczogWycnXSwNCiAgICAgICAgUGljX1BhdGg6ICcnLA0KICAgICAgICBJZDogJycsDQogICAgICAgIENvbXBhbnlfSWQ6ICcnLA0KICAgICAgICBXZWlnaF9XYXJuaW5nX1RocmVzaG9sZDogdW5kZWZpbmVkLA0KICAgICAgICBJc19TcHJheWluZ19XaXRoX1N0b2NrSW46IGZhbHNlLA0KICAgICAgICBJc19Xb3Jrc2hvcF9FbmFibGVkOiBmYWxzZSwgLy8g5piv5ZCm5byA5ZCv6L2m6Ze0566h55CGDQogICAgICAgIE1hbmFnZV9DeWNsZV9FbmFibGVkOiBmYWxzZSwgLy8g5piv5ZCm5byA5ZCv57uf6K6h5ZGo5pyfDQogICAgICAgIElzX1BhcnRfUHJlcGFyZTogdHJ1ZSwgLy8g5piv5ZCm5byA5ZCv6Zu25Lu26b2Q5aWX566h55CGDQogICAgICAgIFNoaXBwaW5nX1dlaWdoX0VuYWJsZWQ6IHRydWUsDQogICAgICAgIGFsbG93RGlyZWN0UmVwb3J0aW5nQWZ0ZXJOZXN0aW5nOiB0cnVlLA0KICAgICAgICBJc19TaGlwcGluZ19QbGFuX0FwcHJvdmFsOiB0cnVlLCAvLyDmmK/lkKblj5HotKforqHliJLpgJrov4flrqHmibnmiY3og73lj5HotKcNCiAgICAgICAgTWFuYWdlX0N5Y2xlX0JlZ2luX1R5cGU6ICcnLA0KICAgICAgICBNYW5hZ2VfQ3ljbGVfQmVnaW5fRGF0ZTogJycsDQogICAgICAgIE1hbmFnZV9DeWNsZV9FbmRfVHlwZTogJycsDQogICAgICAgIE1hbmFnZV9DeWNsZV9FbmRfRGF0ZTogJycsDQogICAgICAgIElzX1NraXBfV2FyZWhvdXNpbmdfT3BlcmF0aW9uOiBmYWxzZSwNCiAgICAgICAgU2hpcHBlcjogJycsDQogICAgICAgIENvbXBfQ29tcHV0ZV9XaXRoX1BhcnQ6IGZhbHNlLA0KICAgICAgICBJc19SYXdfSW5zdG9yZV9DaGVjazogZmFsc2UsDQogICAgICAgIElzX0F1eF9JbnN0b3JlX0NoZWNrOiBmYWxzZSwNCiAgICAgICAgU2hpcHBpbmdfT3JkZXJfTnVtYmVyX0F1dG9fR2VuZXJhdGU6IHRydWUsDQogICAgICAgIE1hdGVyaWVsX1VuaXF1ZV9UeXBlczogW10NCiAgICAgIH0sDQogICAgICBmb3JtMjogew0KICAgICAgICBmb3JtQXJyOiBbXQ0KICAgICAgfSwNCiAgICAgIGZvcm1BcnJDb3B5OiBbXSwNCiAgICAgIGNoYW5nZUZvcm0yOiBbXSwNCiAgICAgIEFsbFRhcmdldFZhbHVlOiAwLA0KICAgICAgQWxsWWVhcjogJycsDQogICAgICBBbGxZZWFyUGlja2VyOiAnJywNCiAgICAgIE5ld1llYXI6IG5ldyBEYXRlKCkuZ2V0RnVsbFllYXIoKSwNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIFNob3J0X05hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5bel5Y6C5ZCN56ewJywgdHJpZ2dlcjogJ2JsdXInIH0NCiAgICAgICAgXSwNCiAgICAgICAgU2hpcHBpbmdfQXBwcm92YWxfTG93ZXJMaW1pdDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaUnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICBDYXRlZ29yeTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6knLCB0cmlnZ2VyOiAnY2hhbmdlJyB9XSwNCiAgICAgICAgUHJvZHVjdGl2aXR5OiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICB2YWxpZGF0b3I6IHRoaXMucHJvZHVjdGl2aXR5U3RhdHVzLA0KICAgICAgICAgICAgdHJpZ2dlcjogJ2JsdXInDQogICAgICAgICAgfQ0KICAgICAgICBdDQogICAgICB9LA0KICAgICAgcnVsZXMyOiB7DQogICAgICAgIFRhcmdldF92YWx1ZTogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHJlcXVpcmVkOiBmYWxzZSwNCiAgICAgICAgICAgIHZhbGlkYXRvcjogdGhpcy50YXJnZXRWYWx1ZVN0YXR1cywNCiAgICAgICAgICAgIHRyaWdnZXI6ICdibHVyJw0KICAgICAgICAgIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIGNvbVR5cGU6ICcnLA0KICAgICAgQWJsaXR5X0xpc3Q6IFtdIC8vIOS6p+iDveW5s+ihoeaVsOaNrg0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldEZhY3RvcnlFbnRpdHlGb3JtKCkNCiAgICB0aGlzLmdldEZhY3RvcnlQZW9wbGVsaXN0KCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8vIOe7n+iuoeWRqOacn+W8gOWFsw0KICAgIGNoYW5nZU1hbmFnZUN5Y2xlRW5hYmxlZChlKSB7DQogICAgICAvLyB0aGlzLmZvcm0uTWFuYWdlX0N5Y2xlX0VuYWJsZWQgPSAhZQ0KICAgICAgaWYgKCFlKSB7DQogICAgICAgIHRoaXMuJGNvbmZpcm0oDQogICAgICAgICAgJ+e7n+iuoeWRqOacn+W8gOWQr+WQjuaXoOazleWFs+mXre+8jOivt+ehruiupOWQjuW8gOWQrycsDQogICAgICAgICAgew0KICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgICB9DQogICAgICAgICkNCiAgICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgICB0aGlzLmZvcm0uTWFuYWdlX0N5Y2xlX0VuYWJsZWQgPSB0cnVlDQogICAgICAgICAgfSkNCiAgICAgICAgICAuY2F0Y2goKCkgPT4gew0KICAgICAgICAgICAgdGhpcy5mb3JtLk1hbmFnZV9DeWNsZV9FbmFibGVkID0gZmFsc2UNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICB0eXBlOiAnaW5mbycsDQogICAgICAgICAgICAgIG1lc3NhZ2U6ICflt7Llj5bmtognDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICAvLyDpm7bku7bpvZDlpZfnrqHnkIblvIDlhbMNCiAgICBjaGFuZ2VQYXJ0UHJlcGFyZUVuYWJsZWQoZSkgew0KICAgICAgaWYgKGUpIHsNCiAgICAgICAgdGhpcy4kY29uZmlybSgNCiAgICAgICAgICAn6Zu25Lu26b2Q5aWX566h55CG5oyJ6ZKu5YWz6Zet5ZCO5LiN5Y+v5YaN5byA5ZCv77yM6K+356Gu6K6k5ZCO5YWz6ZetJywNCiAgICAgICAgICB7DQogICAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICAgIH0NCiAgICAgICAgKQ0KICAgICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuZm9ybS5Jc19QYXJ0X1ByZXBhcmUgPSBmYWxzZQ0KICAgICAgICAgIH0pDQogICAgICAgICAgLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuZm9ybS5Jc19QYXJ0X1ByZXBhcmUgPSB0cnVlDQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgdHlwZTogJ2luZm8nLA0KICAgICAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raIJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g57uf6K6h5pel5pyf5qCh6aqMDQogICAgY2hhbmdlQ2hlY2tOdW0oZSwgdHlwZSkgew0KICAgICAgaWYgKE51bWJlcihlKSA8IDEgfHwgTnVtYmVyKGUpID4gMzEpIHsNCiAgICAgICAgaWYgKHR5cGUgPT09IDEpIHsNCiAgICAgICAgICB0aGlzLmZvcm0uTWFuYWdlX0N5Y2xlX0JlZ2luX0RhdGUgPSAnJw0KICAgICAgICB9IGVsc2UgaWYgKHR5cGUgPT09IDIpIHsNCiAgICAgICAgICB0aGlzLmZvcm0uTWFuYWdlX0N5Y2xlX0VuZF9EYXRlID0gJycNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g6L2m6Ze0566h55CG5byA5YWzDQogICAgY2hhbmdlV29ya3Nob3AoZSkgew0KICAgICAgLy8gY29uc29sZS5sb2coZSwgImVlZSIpOw0KICAgICAgaWYgKCFlKSB7DQogICAgICAgIHRoaXMuJGNvbmZpcm0oDQogICAgICAgICAgJ+i9pumXtOeuoeeQhuW8gOWQr+WQjuaXoOazleWFs+mXre+8jOivt+ehruiupOaCqOeahOS4muWKoeeuoeeQhuaWueW8j+S4rea2ieWPiui9pumXtOWxgue6pycsDQogICAgICAgICAgew0KICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgICB9DQogICAgICAgICkNCiAgICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgICB0aGlzLmZvcm0uSXNfV29ya3Nob3BfRW5hYmxlZCA9IHRydWUNCiAgICAgICAgICB9KQ0KICAgICAgICAgIC5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgICB0aGlzLmZvcm0uSXNfV29ya3Nob3BfRW5hYmxlZCA9IGZhbHNlDQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgdHlwZTogJ2luZm8nLA0KICAgICAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raIJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9KQ0KICAgICAgfQ0KICAgICAgLy8gY29uc29sZS5sb2codGhpcy5mb3JtLklzX1dvcmtzaG9wX0VuYWJsZWQsICJ0aGlzLmZvcm0uSXNfV29ya3Nob3BfRW5hYmxlZCIpOw0KICAgIH0sDQogICAgcHJvZHVjdGl2aXR5U3RhdHVzKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgew0KICAgICAgaWYgKHZhbHVlID09PSAnJykgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+ivt+i+k+WFpScpKQ0KICAgICAgfSBlbHNlIGlmICghTnVtYmVyKHZhbHVlKSAmJiBOdW1iZXIodmFsdWUpICE9PSAwKSB7DQogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign5Y+q6IO95Li65pWw5a2XJykpDQogICAgICB9IGVsc2Ugew0KICAgICAgICBjYWxsYmFjaygpDQogICAgICB9DQogICAgfSwNCiAgICB0YXJnZXRWYWx1ZVN0YXR1cyhydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsNCiAgICAgIGlmICghdmFsdWUpIHsNCiAgICAgICAgY2FsbGJhY2soKQ0KICAgICAgfSBlbHNlIGlmICghTnVtYmVyKHZhbHVlKSAmJiBOdW1iZXIodmFsdWUpICE9PSAwKSB7DQogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign5Y+q6IO95Li65pWw5a2XJykpDQogICAgICB9IGVsc2Ugew0KICAgICAgICBjYWxsYmFjaygpDQogICAgICB9DQogICAgfSwNCiAgICAvLyB0YXJnZXRWYWx1ZVN0YXR1czIocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7DQogICAgLy8gICBpZiAoIXZhbHVlKSB7DQogICAgLy8gICAgIGNhbGxiYWNrKCk7DQogICAgLy8gICB9IGVsc2UgaWYgKCFCb29sZWFuKE51bWJlcih2YWx1ZSkpICYmIE51bWJlcih2YWx1ZSkgIT09IDApIHsNCiAgICAvLyAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLlj6rog73kuLrmlbDlrZciKSk7DQogICAgLy8gICB9IGVsc2Ugew0KICAgIC8vICAgICBjYWxsYmFjaygpOw0KICAgIC8vICAgfQ0KICAgIC8vIH0sDQoNCiAgICBnZXRGYWN0b3J5RW50aXR5Rm9ybSgpIHsNCiAgICAgIEdldEZhY3RvcnlFbnRpdHkoew0KICAgICAgICBpZDogbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ0N1clJlZmVyZW5jZUlkJykNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIGNvbnN0IHsNCiAgICAgICAgICAgIE5hbWUsDQogICAgICAgICAgICBTaG9ydF9OYW1lLA0KICAgICAgICAgICAgQ2F0ZWdvcnksDQogICAgICAgICAgICBQcm9mZXNzaW9uYWxfQ29kZXMsDQogICAgICAgICAgICBJZCwNCiAgICAgICAgICAgIENvbXBhbnlfSWQsDQogICAgICAgICAgICBNYW5hZ2VyLA0KICAgICAgICAgICAgTWFuYWdlcl9JZCwNCiAgICAgICAgICAgIEFkZHJlc3MsDQogICAgICAgICAgICBGaW5hbmNpYWxfU2V0dGxlbWVudF9Pcmdhbml6YXRpb25fQ29kZSwNCiAgICAgICAgICAgIEZpbmFuY2lhbF9TZXR0bGVtZW50X09yZ2FuaXphdGlvbiwNCiAgICAgICAgICAgIFByb2R1Y3Rpdml0eSwNCiAgICAgICAgICAgIFBpY19QYXRoLA0KICAgICAgICAgICAgV2VpZ2hfV2FybmluZ19UaHJlc2hvbGQsDQogICAgICAgICAgICBJc19Ta2lwX1dhcmVob3VzaW5nX09wZXJhdGlvbiwNCiAgICAgICAgICAgIElzX1dvcmtzaG9wX0VuYWJsZWQsDQogICAgICAgICAgICBOZXN0ZWRfTXVzdF9CZWZvcmVfUHJvY2Vzc2luZywNCiAgICAgICAgICAgIE1hbmFnZV9DeWNsZV9FbmFibGVkLA0KICAgICAgICAgICAgSXNfUGFydF9QcmVwYXJlLA0KICAgICAgICAgICAgSXNfU3ByYXlpbmdfV2l0aF9TdG9ja0luLA0KICAgICAgICAgICAgTWFuYWdlX0N5Y2xlX0JlZ2luX1R5cGUsDQogICAgICAgICAgICBNYW5hZ2VfQ3ljbGVfQmVnaW5fRGF0ZSwNCiAgICAgICAgICAgIE1hbmFnZV9DeWNsZV9FbmRfVHlwZSwNCiAgICAgICAgICAgIE1hbmFnZV9DeWNsZV9FbmRfRGF0ZSwNCiAgICAgICAgICAgIENvbXBvbmVudF9TaGlwcGluZ19BcHByb3ZhbCwNCiAgICAgICAgICAgIElzX01hdF9EdXBsaWNhdGUsDQogICAgICAgICAgICBTY2FuX1VuaXF1ZUNvZGVfRW5hYmxlZCwNCiAgICAgICAgICAgIFNoaXBwaW5nX1dlaWdoX0VuYWJsZWQsDQogICAgICAgICAgICBTaGlwcGluZ19BcHByb3ZhbF9Mb3dlckxpbWl0LA0KICAgICAgICAgICAgU2hpcHBlciwNCiAgICAgICAgICAgIENvbXBfQ29tcHV0ZV9XaXRoX1BhcnQsDQogICAgICAgICAgICBJc19SYXdfSW5zdG9yZV9DaGVjaywNCiAgICAgICAgICAgIElzX0F1eF9JbnN0b3JlX0NoZWNrLA0KICAgICAgICAgICAgU2hpcHBpbmdfT3JkZXJfTnVtYmVyX0F1dG9fR2VuZXJhdGUsDQogICAgICAgICAgICBNYXRlcmllbF9VbmlxdWVfVHlwZXMsDQogICAgICAgICAgICBJc19TaGlwcGluZ19QbGFuX0FwcHJvdmFsDQogICAgICAgICAgfSA9IHJlcy5EYXRhLmVudGl0eQ0KICAgICAgICAgIHRoaXMuZm9ybS5Jc19NYXRfRHVwbGljYXRlID0gSXNfTWF0X0R1cGxpY2F0ZQ0KICAgICAgICAgIHRoaXMuZm9ybS5TY2FuX1VuaXF1ZUNvZGVfRW5hYmxlZCA9IFNjYW5fVW5pcXVlQ29kZV9FbmFibGVkDQogICAgICAgICAgdGhpcy5mb3JtLlNoaXBwaW5nX1dlaWdoX0VuYWJsZWQgPSBTaGlwcGluZ19XZWlnaF9FbmFibGVkDQogICAgICAgICAgdGhpcy5mb3JtLkNvbXBvbmVudF9TaGlwcGluZ19BcHByb3ZhbCA9IENvbXBvbmVudF9TaGlwcGluZ19BcHByb3ZhbA0KICAgICAgICAgIHRoaXMuZm9ybS5TaGlwcGluZ19BcHByb3ZhbF9Mb3dlckxpbWl0ID0gU2hpcHBpbmdfQXBwcm92YWxfTG93ZXJMaW1pdA0KICAgICAgICAgIHRoaXMuZm9ybS5TaG9ydF9OYW1lID0gU2hvcnRfTmFtZQ0KICAgICAgICAgIHRoaXMuZm9ybS5OYW1lID0gTmFtZQ0KICAgICAgICAgIHRoaXMuZm9ybS5DYXRlZ29yeSA9IENhdGVnb3J5DQogICAgICAgICAgdGhpcy5mb3JtLlByb2Zlc3Npb25hbF9Db2RlcyA9IFByb2Zlc3Npb25hbF9Db2Rlcw0KICAgICAgICAgIHRoaXMuZm9ybS5JZCA9IElkDQogICAgICAgICAgdGhpcy5mb3JtLkNvbXBhbnlfSWQgPSBDb21wYW55X0lkDQogICAgICAgICAgdGhpcy5mb3JtLk1hbmFnZXIgPSBNYW5hZ2VyDQogICAgICAgICAgdGhpcy5mb3JtLk1hbmFnZXJfSWQgPSBNYW5hZ2VyX0lkDQogICAgICAgICAgdGhpcy5mb3JtLkFkZHJlc3MgPSBBZGRyZXNzDQogICAgICAgICAgdGhpcy5mb3JtLkZpbmFuY2lhbF9TZXR0bGVtZW50X09yZ2FuaXphdGlvbl9Db2RlID0NCiAgICAgICAgICAgIEZpbmFuY2lhbF9TZXR0bGVtZW50X09yZ2FuaXphdGlvbl9Db2RlDQogICAgICAgICAgdGhpcy5mb3JtLkZpbmFuY2lhbF9TZXR0bGVtZW50X09yZ2FuaXphdGlvbiA9DQogICAgICAgICAgICBGaW5hbmNpYWxfU2V0dGxlbWVudF9Pcmdhbml6YXRpb24NCiAgICAgICAgICB0aGlzLmZvcm0uUHJvZHVjdGl2aXR5ID0gUHJvZHVjdGl2aXR5DQogICAgICAgICAgdGhpcy5mb3JtLlBpY19QYXRoID0gUGljX1BhdGgNCiAgICAgICAgICB0aGlzLmZvcm0uSXNfV29ya3Nob3BfRW5hYmxlZCA9IElzX1dvcmtzaG9wX0VuYWJsZWQNCiAgICAgICAgICB0aGlzLmZvcm0uYWxsb3dEaXJlY3RSZXBvcnRpbmdBZnRlck5lc3RpbmcgPSAhTmVzdGVkX011c3RfQmVmb3JlX1Byb2Nlc3NpbmcNCiAgICAgICAgICB0aGlzLmZvcm0uSXNfU2hpcHBpbmdfUGxhbl9BcHByb3ZhbCA9IElzX1NoaXBwaW5nX1BsYW5fQXBwcm92YWwNCiAgICAgICAgICB0aGlzLmZvcm0uSXNfU2tpcF9XYXJlaG91c2luZ19PcGVyYXRpb24gPSBJc19Ta2lwX1dhcmVob3VzaW5nX09wZXJhdGlvbg0KICAgICAgICAgIHRoaXMuZm9ybS5XZWlnaF9XYXJuaW5nX1RocmVzaG9sZCA9IFdlaWdoX1dhcm5pbmdfVGhyZXNob2xkIHx8IHVuZGVmaW5lZA0KICAgICAgICAgIHRoaXMuZm9ybS5NYW5hZ2VfQ3ljbGVfRW5hYmxlZCA9IE1hbmFnZV9DeWNsZV9FbmFibGVkDQogICAgICAgICAgdGhpcy5mb3JtLklzX1BhcnRfUHJlcGFyZSA9IElzX1BhcnRfUHJlcGFyZQ0KICAgICAgICAgIHRoaXMuZm9ybS5Jc19TcHJheWluZ19XaXRoX1N0b2NrSW4gPSBJc19TcHJheWluZ19XaXRoX1N0b2NrSW4NCiAgICAgICAgICB0aGlzLmZvcm0uTWFuYWdlX0N5Y2xlX0JlZ2luX1R5cGUgPSBNYW5hZ2VfQ3ljbGVfQmVnaW5fVHlwZSB8fCAnJw0KICAgICAgICAgIHRoaXMuZm9ybS5NYW5hZ2VfQ3ljbGVfQmVnaW5fRGF0ZSA9IE1hbmFnZV9DeWNsZV9CZWdpbl9EYXRlIHx8ICcnDQogICAgICAgICAgdGhpcy5mb3JtLk1hbmFnZV9DeWNsZV9FbmRfVHlwZSA9IE1hbmFnZV9DeWNsZV9FbmRfVHlwZSB8fCAnJw0KICAgICAgICAgIHRoaXMuZm9ybS5NYW5hZ2VfQ3ljbGVfRW5kX0RhdGUgPSBNYW5hZ2VfQ3ljbGVfRW5kX0RhdGUgfHwgJycNCiAgICAgICAgICB0aGlzLmZvcm0uQ29tcF9Db21wdXRlX1dpdGhfUGFydCA9IENvbXBfQ29tcHV0ZV9XaXRoX1BhcnQNCiAgICAgICAgICB0aGlzLmZvcm0uSXNfUmF3X0luc3RvcmVfQ2hlY2sgPSBJc19SYXdfSW5zdG9yZV9DaGVjaw0KICAgICAgICAgIHRoaXMuZm9ybS5Jc19BdXhfSW5zdG9yZV9DaGVjayA9IElzX0F1eF9JbnN0b3JlX0NoZWNrDQogICAgICAgICAgdGhpcy5mb3JtLlNoaXBwaW5nX09yZGVyX051bWJlcl9BdXRvX0dlbmVyYXRlID0gU2hpcHBpbmdfT3JkZXJfTnVtYmVyX0F1dG9fR2VuZXJhdGUNCiAgICAgICAgICB0aGlzLmZvcm0uTWF0ZXJpZWxfVW5pcXVlX1R5cGVzID0gTWF0ZXJpZWxfVW5pcXVlX1R5cGVzLnNwbGl0KCcsJykNCiAgICAgICAgICB0aGlzLmZvcm0uU2hpcHBlciA9IFNoaXBwZXIgfHwgJycNCiAgICAgICAgICB0aGlzLnNyYyA9IFBpY19QYXRoDQogICAgICAgICAgdGhpcy5zcmNMaXN0WzBdID0gUGljX1BhdGgNCiAgICAgICAgICB0aGlzLmZvcm1BcnJDb3B5ID0gcmVzLkRhdGEubGlzdA0KICAgICAgICAgIHRoaXMuQWxsWWVhciA9IHRoaXMuTmV3WWVhcg0KICAgICAgICAgIHRoaXMuQWxsWWVhclBpY2tlciA9ICcnDQogICAgICAgICAgdGhpcy5BbGxUYXJnZXRWYWx1ZSA9IDANCiAgICAgICAgICB0aGlzLmZvcm0yLmZvcm1BcnIgPSByZXMuRGF0YS5saXN0LmZpbHRlcigoaXRlbSkgPT4gew0KICAgICAgICAgICAgcmV0dXJuIGl0ZW0uWWVhciA9PSB0aGlzLk5ld1llYXINCiAgICAgICAgICB9KQ0KICAgICAgICAgIHRoaXMuZm9ybTIuZm9ybUFyci5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgICB0aGlzLkFsbFRhcmdldFZhbHVlID0NCiAgICAgICAgICAgICAgdGhpcy5BbGxUYXJnZXRWYWx1ZSArIE51bWJlcihpdGVtLlRhcmdldF92YWx1ZSkNCiAgICAgICAgICB9KQ0KICAgICAgICAgIGlmICh0aGlzLmZvcm0yLmZvcm1BcnIubGVuZ3RoIDwgMTIpIHsNCiAgICAgICAgICAgIC8vIHRoaXMuZm9ybTIuZm9ybUFyciA9IHJlcy5EYXRhLmxpc3Q7DQogICAgICAgICAgICBmb3IgKGxldCBpID0gMTsgaSA8PSAxMjsgaSsrKSB7DQogICAgICAgICAgICAgIGNvbnN0IHRlbXAgPSB0aGlzLmZvcm0yLmZvcm1BcnIuZmluZCgoaXRlbSkgPT4gew0KICAgICAgICAgICAgICAgIHJldHVybiBpdGVtLk1vbnRoID09IGkNCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgaWYgKCF0ZW1wKSB7DQogICAgICAgICAgICAgICAgY29uc3QgbW9udGggPSB7DQogICAgICAgICAgICAgICAgICBGYWN0b3J5X2lkOiBJZCwNCiAgICAgICAgICAgICAgICAgIFByb2Zlc3Npb25hbF9Db2RlOiBQcm9mZXNzaW9uYWxfQ29kZXNbMF0sDQogICAgICAgICAgICAgICAgICBZZWFyOiB0aGlzLk5ld1llYXIsDQogICAgICAgICAgICAgICAgICBNb250aDogaSwNCiAgICAgICAgICAgICAgICAgIFRhcmdldF92YWx1ZTogJycNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgdGhpcy5mb3JtMi5mb3JtQXJyLnB1c2gobW9udGgpDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy5mb3JtMi5mb3JtQXJyLnNvcnQoZnVuY3Rpb24oYSwgYikgew0KICAgICAgICAgICAgcmV0dXJuIGEuTW9udGggLSBiLk1vbnRoDQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLkFibGl0eV9MaXN0ID0gcmVzLkRhdGEuQWJsaXR5X0xpc3QNCiAgICAgICAgICB0aGlzLmdldFByb2Zlc3Npb25hbFR5cGVMaXN0KCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBnZXRQcm9mZXNzaW9uYWxUeXBlTGlzdCgpIHsNCiAgICAgIEdldFByb2Zlc3Npb25hbFR5cGUoew0KICAgICAgICBpc19TeXN0ZW06IGZhbHNlLA0KICAgICAgICBwYWdlc2l6ZTogLTEsDQogICAgICAgIGNvbXBhbnlJZDogdGhpcy5mb3JtLkNvbXBhbnlfSWQNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLmNvbVR5cGUgPSByZXMuRGF0YS5EYXRhDQogICAgICB9KQ0KICAgIH0sDQogICAgY2hhbmdlQ2F0ZWdvcnkoZSkgew0KICAgICAgY29uc3QgdGVtcCA9IHRoaXMuY29tVHlwZS5maW5kKChpdGVtKSA9PiB7DQogICAgICAgIHJldHVybiBpdGVtLk5hbWUgPT0gZQ0KICAgICAgfSkNCiAgICAgIHRoaXMuZm9ybS5Qcm9mZXNzaW9uYWxfQ29kZXNbMF0gPSB0ZW1wLkNvZGUNCiAgICAgIHRoaXMuZm9ybTIuZm9ybUFyci5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgIGl0ZW0uUHJvZmVzc2lvbmFsX0NvZGUgPSB0ZW1wLkNvZGUNCiAgICAgIH0pDQogICAgICB0aGlzLmZvcm1BcnJDb3B5LmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgaXRlbS5Qcm9mZXNzaW9uYWxfQ29kZSA9IHRlbXAuQ29kZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGNoYW5nZVllYXIoZSkgew0KICAgICAgaWYgKGUpIHsNCiAgICAgICAgdGhpcy5BbGxZZWFyID0gZQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5BbGxZZWFyID0gdGhpcy5OZXdZZWFyDQogICAgICB9DQogICAgICBpZiAodGhpcy5BbGxZZWFyID09IHRoaXMuTmV3WWVhciB8fCB0aGlzLkFsbFllYXIgPT0gdGhpcy5OZXdZZWFyIC0gMSkgew0KICAgICAgICB0aGlzLkFsbFllYXJQaWNrZXIgPSAnJw0KICAgICAgfQ0KICAgICAgdGhpcy5BbGxUYXJnZXRWYWx1ZSA9IDANCiAgICAgIHRoaXMuZm9ybTIuZm9ybUFyciA9IHRoaXMuZm9ybUFyckNvcHkuZmlsdGVyKChpdGVtKSA9PiB7DQogICAgICAgIHJldHVybiBpdGVtLlllYXIgPT0gdGhpcy5BbGxZZWFyDQogICAgICB9KQ0KICAgICAgaWYgKHRoaXMuZm9ybTIuZm9ybUFyci5sZW5ndGggPCAxMikgew0KICAgICAgICBmb3IgKGxldCBpID0gMTsgaSA8PSAxMjsgaSsrKSB7DQogICAgICAgICAgY29uc3QgdGVtcCA9IHRoaXMuZm9ybTIuZm9ybUFyci5maW5kKChpdGVtKSA9PiB7DQogICAgICAgICAgICByZXR1cm4gaXRlbS5Nb250aCA9PSBpDQogICAgICAgICAgfSkNCiAgICAgICAgICBpZiAoIXRlbXApIHsNCiAgICAgICAgICAgIGNvbnN0IG1vbnRoID0gew0KICAgICAgICAgICAgICBGYWN0b3J5X2lkOiB0aGlzLmZvcm0uSWQsDQogICAgICAgICAgICAgIFByb2Zlc3Npb25hbF9Db2RlOiB0aGlzLmZvcm0uUHJvZmVzc2lvbmFsX0NvZGVzWzBdLA0KICAgICAgICAgICAgICBZZWFyOiB0aGlzLkFsbFllYXIsDQogICAgICAgICAgICAgIE1vbnRoOiBpLA0KICAgICAgICAgICAgICBUYXJnZXRfdmFsdWU6ICcnDQogICAgICAgICAgICB9DQogICAgICAgICAgICB0aGlzLmZvcm0yLmZvcm1BcnIucHVzaChtb250aCkNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMuZm9ybTIuZm9ybUFyci5zb3J0KGZ1bmN0aW9uKGEsIGIpIHsNCiAgICAgICAgcmV0dXJuIGEuTW9udGggLSBiLk1vbnRoDQogICAgICB9KQ0KICAgICAgdGhpcy5mb3JtMi5mb3JtQXJyLmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgdGhpcy5BbGxUYXJnZXRWYWx1ZSA9IHRoaXMuQWxsVGFyZ2V0VmFsdWUgKyBOdW1iZXIoaXRlbS5UYXJnZXRfdmFsdWUpDQogICAgICB9KQ0KICAgIH0sDQogICAgVGFyZ2V0VmFsdWVJbnB1dChlLCBpbmRleCkgew0KICAgICAgaWYgKHRoaXMuZm9ybUFyckNvcHkubGVuZ3RoICE9IDApIHsNCiAgICAgICAgY29uc3QgdGVtcCA9IHRoaXMuZm9ybUFyckNvcHkuZmluZCgoaXRlbSkgPT4gew0KICAgICAgICAgIHJldHVybiAoDQogICAgICAgICAgICBpdGVtLlllYXIgPT0gdGhpcy5mb3JtMi5mb3JtQXJyW2luZGV4XS5ZZWFyICYmDQogICAgICAgICAgICBpdGVtLk1vbnRoID09IHRoaXMuZm9ybTIuZm9ybUFycltpbmRleF0uTW9udGgNCiAgICAgICAgICApDQogICAgICAgIH0pDQogICAgICAgIGlmICh0ZW1wKSB7DQogICAgICAgICAgdGVtcC5UYXJnZXRfdmFsdWUgPSBlDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5mb3JtQXJyQ29weS5wdXNoKHRoaXMuZm9ybTIuZm9ybUFycltpbmRleF0pDQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuZm9ybUFyckNvcHkucHVzaCh0aGlzLmZvcm0yLmZvcm1BcnJbaW5kZXhdKQ0KICAgICAgfQ0KICAgICAgdGhpcy5BbGxUYXJnZXRWYWx1ZSA9IDANCiAgICAgIHRoaXMuZm9ybTIuZm9ybUFyci5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgIHRoaXMuQWxsVGFyZ2V0VmFsdWUgPSB0aGlzLkFsbFRhcmdldFZhbHVlICsgTnVtYmVyKGl0ZW0uVGFyZ2V0X3ZhbHVlKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFzeW5jIHF1ZXJ5U2VhcmNoQXN5bmMocXVlcnlTdHJpbmcsIGNiKSB7DQogICAgICBsZXQgcmVzdWx0cyA9IFtdDQogICAgICByZXN1bHRzID0gYXdhaXQgdGhpcy5nZXRVc2VyTGlzdChxdWVyeVN0cmluZykNCiAgICAgIGNiKHJlc3VsdHMpDQogICAgfSwNCiAgICBnZXRVc2VyTGlzdChxdWVyeVN0cmluZyA9ICcnKSB7DQogICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHsNCiAgICAgICAgR2V0VXNlclBhZ2Uoew0KICAgICAgICAgIFBhZ2U6IDEsDQogICAgICAgICAgcGFnZVNpemU6IDIwLA0KICAgICAgICAgIFNlYXJjaDogcXVlcnlTdHJpbmcNCiAgICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgdGhpcy51c2VyT3B0aW9ucyA9IHJlcy5EYXRhLkRhdGEubWFwKCh2KSA9PiB7DQogICAgICAgICAgICB0aGlzLiRzZXQodiwgJ3ZhbHVlJywgdi5EaXNwbGF5X05hbWUpDQogICAgICAgICAgICByZXR1cm4gdg0KICAgICAgICAgIH0pDQogICAgICAgICAgcmVzb2x2ZSh0aGlzLnVzZXJPcHRpb25zKQ0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGJsdXIoKSB7DQogICAgICBjb25zdCB0ZW1wID0gdGhpcy51c2VyT3B0aW9ucy5maW5kKChpdGVtKSA9PiB7DQogICAgICAgIHJldHVybiBpdGVtLkRpc3BsYXlfTmFtZSA9PSB0aGlzLmZvcm0uTWFuYWdlcg0KICAgICAgfSkNCiAgICAgIGlmICh0ZW1wKSB7DQogICAgICAgIHRoaXMuZm9ybS5NYW5hZ2VyX0lkID0gdGVtcC5JZA0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5mb3JtLk1hbmFnZXJfSWQgPSAnJw0KICAgICAgfQ0KICAgIH0sDQogICAgY2xlYXIoKSB7DQogICAgICB0aGlzLmZvcm0uTWFuYWdlcl9JZCA9ICcnDQogICAgICB0aGlzLmZvcm0uTWFuYWdlciA9ICcnDQogICAgICB0aGlzLiRyZWZzLmF1dG9jb21wbGV0ZS5hY3RpdmF0ZWQgPSB0cnVlDQogICAgfSwNCiAgICBoYW5kbGVTZWxlY3QoaXRlbSkgew0KICAgICAgdGhpcy5mb3JtLk1hbmFnZXJfSWQgPSBpdGVtLklkDQogICAgICB0aGlzLmZvcm0uTWFuYWdlciA9IGl0ZW0uRGlzcGxheV9OYW1lDQogICAgfSwNCiAgICB1cGxvYWRTdWNjZXNzKHJlc3BvbnNlLCBmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgY29uc3QgaW1nT2JqID0geyBGaWxlX05hbWU6ICcnLCBGaWxlX1VybDogJycgfQ0KICAgICAgaWYgKGZpbGUuaGFzT3duUHJvcGVydHkoJ3Jlc3BvbnNlJykpIHsNCiAgICAgICAgaW1nT2JqLkZpbGVfVXJsID0gZmlsZS5yZXNwb25zZS5EYXRhLnNwbGl0KCcqJylbMF0NCiAgICAgICAgaW1nT2JqLkZpbGVfTmFtZSA9IGZpbGUucmVzcG9uc2UuRGF0YS5zcGxpdCgnKicpWzNdDQogICAgICB9IGVsc2Ugew0KICAgICAgICBpbWdPYmouRmlsZV9VcmwgPSBmaWxlLnVybA0KICAgICAgfQ0KICAgICAgdGhpcy5mb3JtLlBpY19QYXRoID0gaW1nT2JqLkZpbGVfVXJsDQogICAgICB0aGlzLnNyYyA9IGZpbGUucmVzcG9uc2UuZW5jcnlwdGlvblVybA0KICAgICAgdGhpcy5zcmNMaXN0WzBdID0gZmlsZS5yZXNwb25zZS5lbmNyeXB0aW9uVXJsDQogICAgICAvLyBsZXQgdGVtcCA9IHRoaXMuc3JjTGlzdDsNCiAgICAgIC8vIHRoaXMuJHNldCh0ZW1wLCAiMCIsIHRoaXMuc3JjTGlzdFswXSk7DQogICAgfSwNCiAgICB1cGxvYWRFeGNlZWQoZmlsZXMsIGZpbGVMaXN0KSB7DQogICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnLA0KICAgICAgICBtZXNzYWdlOiAn5bey6LaF6L+H5paH5Lu25LiK5Lyg5pyA5aSn5pWw6YePJw0KICAgICAgfSkNCiAgICB9LA0KICAgIHVwbG9hZFJlbW92ZShmaWxlLCBmaWxlTGlzdCkge30sDQogICAgaGFuZGxlUHJldmlldyhmaWxlKSB7fSwNCiAgICBkZWxldGVJdGVtKCkgew0KICAgICAgdGhpcy5mb3JtLlBpY19QYXRoID0gJycNCiAgICAgIHRoaXMuc3JjID0gJycNCiAgICAgIHRoaXMuc3JjTGlzdFswXSA9ICcnDQogICAgfSwNCiAgICBoYW5kbGVTdWJtaXQoZm9ybU5hbWUxLCBmb3JtTmFtZTIpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruiupOS/neWtmCcsICfmj5DnpLonLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIGNvbnN0IGZvcm0gPSB7IC4uLnRoaXMuZm9ybSB9DQogICAgICAgICAgY29uc3QgZm9ybTIgPSBbLi4udGhpcy5mb3JtQXJyQ29weV0NCiAgICAgICAgICBjb25zdCBmb3JtMyA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy5BYmxpdHlfTGlzdCkpDQogICAgICAgICAgY29uc29sZS5sb2codGhpcy5BYmxpdHlfTGlzdCwgJ0FibGl0eV9MaXN0JykNCiAgICAgICAgICBmb3JtMy5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgICBkZWxldGUgaXRlbVsnQ29tcG9uZW50X1R5cGVfTmFtZSddDQogICAgICAgICAgICBpdGVtLlByb2R1Y3Rpb25fQ2FwYWNpdHkgPSBpdGVtLlByb2R1Y3Rpb25fQ2FwYWNpdHkgPyBOdW1iZXIoaXRlbS5Qcm9kdWN0aW9uX0NhcGFjaXR5KSA6IGl0ZW0uUHJvZHVjdGlvbl9DYXBhY2l0eQ0KICAgICAgICAgIH0pDQogICAgICAgICAgZm9ybS5NYXRlcmllbF9VbmlxdWVfVHlwZXMgPSBmb3JtLk1hdGVyaWVsX1VuaXF1ZV9UeXBlcy5qb2luKCcsJykNCiAgICAgICAgICBmb3JtLk5lc3RlZF9NdXN0X0JlZm9yZV9Qcm9jZXNzaW5nID0gIWZvcm0uYWxsb3dEaXJlY3RSZXBvcnRpbmdBZnRlck5lc3RpbmcNCiAgICAgICAgICBjb25zb2xlLmxvZyhmb3JtLCAnZm9ybScpDQogICAgICAgICAgdGhpcy4kcmVmc1tmb3JtTmFtZTFdLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgICAgIHRoaXMuJHJlZnNbZm9ybU5hbWUyXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICAgICAgICAgIGNvbnN0IG9iaiA9IHsNCiAgICAgICAgICAgICAgICAgICAgZW50aXR5OiBmb3JtLA0KICAgICAgICAgICAgICAgICAgICBsaXN0OiBmb3JtMiwNCiAgICAgICAgICAgICAgICAgICAgQWJpbGl0eV9MaXN0OiBmb3JtMw0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgaWYgKGZvcm0uTWFuYWdlX0N5Y2xlX0VuYWJsZWQpIHsNCiAgICAgICAgICAgICAgICAgICAgaWYgKCFmb3JtLk1hbmFnZV9DeWNsZV9CZWdpbl9UeXBlIHx8ICFmb3JtLk1hbmFnZV9DeWNsZV9CZWdpbl9EYXRlIHx8ICFmb3JtLk1hbmFnZV9DeWNsZV9FbmRfVHlwZSB8fCAhZm9ybS5NYW5hZ2VfQ3ljbGVfRW5kX0RhdGUpIHsNCiAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+ihpeWFqOe7n+iuoeWRqOacnycpDQogICAgICAgICAgICAgICAgICAgICAgcmV0dXJuDQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IHRydWUNCiAgICAgICAgICAgICAgICAgIFN1cHBseUZhY3RvcnlJbmZvKG9iaikudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAn5L+d5a2Y5oiQ5YqfJywNCiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgICAgICAgdGhpcy5nZXRGYWN0b3J5RW50aXR5Rm9ybSgpDQogICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIHR5cGU6ICdpbmZvJywNCiAgICAgICAgICAgIG1lc3NhZ2U6ICflt7Llj5bmtognDQogICAgICAgICAgfSkNCiAgICAgICAgfSkNCiAgICB9LA0KICAgIHJlc2V0Rm9ybSgpIHsNCiAgICAgIHRoaXMuZ2V0RmFjdG9yeUVudGl0eUZvcm0oKQ0KICAgIH0sDQogICAgY29tcHV0ZWRDaGFuZ2UodmFsKSB7DQogICAgICBjb25zb2xlLmxvZyh2YWwpDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4ZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/basic-information/factory-attribute", "sourcesContent": ["<template>\r\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\r\n    <div class=\"cs-z-page-main-content\">\r\n      <div class=\"fff cs-z-tb-wrapper\">\r\n        <div class=\"basic-information\">\r\n          <header>基本信息</header>\r\n          <el-form\r\n            ref=\"form\"\r\n            :inline=\"true\"\r\n            :model=\"form\"\r\n            class=\"demo-form-inline\"\r\n            :rules=\"rules\"\r\n            style=\"padding-left: 20px\"\r\n            label-width=\"140px\"\r\n          >\r\n            <el-form-item label=\"工厂名称\" prop=\"Short_Name\">\r\n              <el-input\r\n                v-model=\"form.Short_Name\"\r\n                placeholder=\"请输入\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"工厂全称\" prop=\"Name\">\r\n              <el-input v-model=\"form.Name\" placeholder=\"请输入\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"厂长\" prop=\"Manager\">\r\n              <el-autocomplete\r\n                ref=\"autocomplete\"\r\n                v-model=\"form.Manager\"\r\n                :fetch-suggestions=\"querySearchAsync\"\r\n                clearable\r\n                placeholder=\"请选择\"\r\n                style=\"width: 100%\"\r\n                @clear=\"clear\"\r\n                @select=\"handleSelect\"\r\n                @blur=\"blur\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"工厂位置\" prop=\"Address\">\r\n              <el-input v-model=\"form.Address\" placeholder=\"请输入\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"工厂专业类别\" prop=\"Category\">\r\n              <!-- <el-radio-group\r\n                v-model=\"form.Category\"\r\n                style=\"width: 250px; margin-right: 30px\"\r\n              >\r\n                <el-radio\r\n                  v-for=\"item in comType\"\r\n                  :key=\"item.Id\"\r\n                  @change=\"changeCategory(item.Code)\"\r\n                  :label=\"item.Name\"\r\n                />\r\n              </el-radio-group> -->\r\n              <el-select\r\n                v-model=\"form.Category\"\r\n                placeholder=\"请选择\"\r\n                disabled\r\n                @change=\"changeCategory\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in comType\"\r\n                  :key=\"item.Id\"\r\n                  :value=\"item.Name\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"产能\" prop=\"Productivity\">\r\n              <el-input\r\n                v-model=\"form.Productivity\"\r\n                placeholder=\"请输入\"\r\n              />\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"工厂图片\" class=\"factory-img\">\r\n              <OSSUpload\r\n                class=\"upload-demo\"\r\n                action=\"alioss\"\r\n                accept=\"image/*\"\r\n                :on-success=\"\r\n                  (response, file, fileList) => {\r\n                    uploadSuccess(response, file, fileList);\r\n                  }\r\n                \"\r\n                :on-remove=\"uploadRemove\"\r\n                :on-preview=\"handlePreview\"\r\n                :show-file-list=\"false\"\r\n              >\r\n                <el-button type=\"primary\">上传图片</el-button>\r\n              </OSSUpload>\r\n              <div style=\"position: relative; width:200px\">\r\n                <el-image\r\n                  style=\"\r\n                    width: 200px;\r\n                    height: 120px;\r\n                    background-color: #eee;\r\n                    color: #999;\r\n                    font-size: 40px;\r\n                    margin-top: 10px;\r\n                    display: flex;\r\n                    justify-content: center;\r\n                    align-items: center;\r\n                  \"\r\n                  :src=\"src\"\r\n                  :preview-src-list=\"srcList\"\r\n                >\r\n                  <div slot=\"error\" class=\"image-slot\">\r\n                    <i class=\"el-icon-picture\" />\r\n                  </div>\r\n                </el-image>\r\n                <i\r\n                  v-show=\"src\"\r\n                  class=\"el-icon-circle-close img-item-icon\"\r\n                  @click=\"deleteItem()\"\r\n                />\r\n              </div>\r\n            </el-form-item>\r\n            <!--          </el-form>-->\r\n\r\n            <el-divider />\r\n            <div class=\"year-batch-production\">\r\n              <header>工厂年度产量目标</header>\r\n              <div class=\"radio-box\">\r\n                <el-radio-group v-model=\"AllYear\" size=\"small\" @change=\"changeYear\">\r\n                  <el-radio-button :label=\"NewYear\">{{ NewYear }}</el-radio-button>\r\n                  <el-radio-button :label=\"NewYear - 1\">{{\r\n                    NewYear - 1\r\n                  }}</el-radio-button>\r\n                  <el-date-picker\r\n                    v-model=\"AllYearPicker\"\r\n                    type=\"year\"\r\n                    placeholder=\"其他年份\"\r\n                    :editable=\"false\"\r\n                    size=\"small\"\r\n                    style=\"width: 120px\"\r\n                    value-format=\"yyyy\"\r\n                    @change=\"changeYear\"\r\n                  />\r\n                </el-radio-group>\r\n              </div>\r\n              <div style=\"margin: 20px\" />\r\n              <p>年度产量目标：{{ AllTargetValue }}</p>\r\n              <el-form\r\n                ref=\"form2\"\r\n                :inline=\"true\"\r\n                class=\"demo-form-inline\"\r\n                style=\"padding-left: 20px\"\r\n                label-width=\"70px\"\r\n                :model=\"form2\"\r\n              >\r\n                <el-form-item\r\n                  v-for=\"(item, index) of form2.formArr\"\r\n                  :key=\"index\"\r\n                  :label=\"`${item.Month}月目标`\"\r\n                  :prop=\"`formArr[${index}].Target_value`\"\r\n                  :rules=\"[\r\n                    {\r\n                      validator: (rule, value, callback) => {\r\n                        targetValueStatus(rule, value, callback);\r\n                      }, //后面的这几个是传的自定义参数\r\n                      trigger: 'blur',\r\n                      required: false,\r\n                    },\r\n                  ]\"\r\n                >\r\n                  <el-input\r\n                    v-model=\"item.Target_value\"\r\n                    class=\"input-number\"\r\n                    type=\"number\"\r\n                    min=\"0\"\r\n                    @input=\"TargetValueInput(item.Target_value, index)\"\r\n                  />\r\n                </el-form-item>\r\n              </el-form>\r\n            </div>\r\n            <div class=\"year-batch-production\">\r\n              <header>工厂月均产能</header>\r\n              <div v-if=\"Ablity_List.length !== 0\">\r\n                <el-form\r\n                  v-for=\"(item, index) of Ablity_List\"\r\n                  :key=\"index\"\r\n                  ref=\"form3\"\r\n                  :inline=\"true\"\r\n                  class=\"demo-form-inline\"\r\n                  style=\"padding-left: 20px; display: inline-block;\"\r\n                  label-width=\"100px\"\r\n                  :model=\"item\"\r\n                >\r\n                  <el-form-item\r\n                    :label=\"item.Component_Type_Name + '(t)'\"\r\n                    prop=\"Production_Capacity\"\r\n                  >\r\n                    <el-input\r\n                      v-model=\"item.Production_Capacity\"\r\n                      class=\"input-number\"\r\n                      type=\"number\"\r\n                      min=\"0\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-form>\r\n              </div>\r\n              <div v-else style=\"padding-left: 40px;\">暂无数据</div>\r\n            </div>\r\n\r\n            <el-divider />\r\n\r\n            <!--        <el-form ref=\"form\" :model=\"form\" label-width=\"80px\">-->\r\n            <el-form-item\r\n              label=\"财务结算组织\"\r\n              prop=\"Financial_Settlement_Organization\"\r\n            >\r\n              <el-input\r\n                v-model=\"form.Financial_Settlement_Organization\"\r\n                placeholder=\"请输入\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item\r\n              label=\"财务结算组织编号\"\r\n              prop=\"Financial_Settlement_Organization_Code\"\r\n            >\r\n              <el-input\r\n                v-model=\"form.Financial_Settlement_Organization_Code\"\r\n                placeholder=\"请输入\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"过磅预警阈值\" prop=\"Weigh_Warning_Threshold\">\r\n              <el-input-number v-model.number=\"form.Weigh_Warning_Threshold\" :min=\"0\" style=\"width: 80%\" class=\"cs-number-btn-hidden cs-input\" placeholder=\"\" clearable />\r\n              <span class=\"ml-8\">kg</span>\r\n              <el-tooltip class=\"item\" effect=\"dark\" content=\"当货物磅重与理重上下浮动超过该值时进行预警提示\" placement=\"top-start\">\r\n                <i class=\"el-icon-question\" />\r\n              </el-tooltip>\r\n            </el-form-item>\r\n            <el-form-item label=\"发货员\" prop=\"Shipper\">\r\n              <el-select\r\n                v-model=\"form.Shipper\"\r\n                placeholder=\"请选择\"\r\n                clearable=\"\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in factoryPeoplelist\"\r\n                  :key=\"item.Id\"\r\n                  :value=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item\r\n              v-if=\"form.Component_Shipping_Approval\"\r\n              label=\"项目发货总量大于\"\r\n              prop=\"Shipping_Approval_LowerLimit\"\r\n            >\r\n\r\n              <el-input-number v-model=\"form.Shipping_Approval_LowerLimit\" class=\"cs-number-btn-hidden w80\" placeholder=\"请输入\" clearable=\"\" />t\r\n            </el-form-item>\r\n            <el-divider />\r\n            <el-form-item label=\"是否开启车间管理\" prop=\"Is_Workshop_Enabled\">\r\n              <div :class=\"form.Is_Workshop_Enabled ? 'is-workshop' : ''\">\r\n                <el-switch\r\n                  v-model=\"form.Is_Workshop_Enabled\"\r\n                  disabled\r\n                  active-color=\"#13ce66\"\r\n                  inactive-color=\"#ff4949\"\r\n                  @click.native=\"changeWorkshop(form.Is_Workshop_Enabled)\"\r\n                />\r\n              </div>\r\n            </el-form-item>\r\n            <el-form-item label=\"物料重复判定\" prop=\"Is_Mat_Duplicate\">\r\n              <el-switch\r\n                v-model=\"form.Is_Mat_Duplicate\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n              />\r\n              <el-tooltip class=\"item\" effect=\"dark\" content=\"原料、辅料入库、退库时相同属性的原料、辅料数量自动合并\" placement=\"top-start\">\r\n                <i class=\"el-icon-question\" style=\"margin-left: 8px;\" />\r\n              </el-tooltip>\r\n            </el-form-item>\r\n            <el-form-item label=\"末道工序直接入库\" prop=\"Is_Skip_Warehousing_Operation\">\r\n              <el-switch\r\n                v-model=\"form.Is_Skip_Warehousing_Operation\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"是否唯一码管理\" prop=\"Is_Workshop_Enabled\">\r\n              <el-switch\r\n                v-model=\"form.Scan_UniqueCode_Enabled\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"统计周期\" prop=\"Manage_Cycle_Enabled\">\r\n              <div :class=\"form.Manage_Cycle_Enabled ? 'is-workshop' : ''\">\r\n                <el-switch\r\n                  v-model=\"form.Manage_Cycle_Enabled\"\r\n                  disabled\r\n                  @click.native=\"changeManageCycleEnabled(form.Manage_Cycle_Enabled)\"\r\n                />\r\n                <template v-if=\"form.Manage_Cycle_Enabled\">\r\n                  <el-input v-model.number=\"form.Manage_Cycle_Begin_Date\" type=\"number\" :min=\"1\" :max=\"31\" class=\"manage-cycle-input input-number\" step=\"any\" placeholder=\"请输入日期\" @change=\"changeCheckNum($event,1)\">\r\n                    <el-select slot=\"prepend\" v-model=\"form.Manage_Cycle_Begin_Type\" class=\"manage-cycle-select\" placeholder=\"请选择\">\r\n                      <el-option label=\"上月\" :value=\"1\" />\r\n                      <el-option label=\"当月\" :value=\"2\" />\r\n                      <el-option label=\"下月\" :value=\"3\" />\r\n                    </el-select>\r\n                  </el-input>\r\n                  <div class=\"text\">至</div>\r\n                  <el-input v-model.number=\"form.Manage_Cycle_End_Date\" type=\"number\" :min=\"1\" :max=\"31\" class=\"manage-cycle-input input-number\" step=\"any\" placeholder=\"请输入日期\" @change=\"changeCheckNum($event,2)\">\r\n                    <el-select slot=\"prepend\" v-model=\"form.Manage_Cycle_End_Type\" class=\"manage-cycle-select\" placeholder=\"请选择\">\r\n                      <el-option label=\"上月\" :value=\"1\" />\r\n                      <el-option label=\"当月\" :value=\"2\" />\r\n                      <el-option label=\"下月\" :value=\"3\" />\r\n                    </el-select>\r\n                  </el-input>\r\n                </template>\r\n              </div>\r\n            </el-form-item>\r\n            <el-form-item label=\"发货单审核\" prop=\"Component_Shipping_Approval\">\r\n              <el-switch\r\n                v-model=\"form.Component_Shipping_Approval\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"零件齐套管理\" prop=\"Is_Part_Prepare\">\r\n              <div :class=\"form.Is_Part_Prepare ? '' : 'is-workshop'\">\r\n                <el-switch\r\n                  v-model=\"form.Is_Part_Prepare\"\r\n                  disabled\r\n                  active-color=\"#13ce66\"\r\n                  inactive-color=\"#ff4949\"\r\n                  @click.native=\"changePartPrepareEnabled(form.Is_Part_Prepare)\"\r\n                />\r\n              </div>\r\n            </el-form-item>\r\n            <el-form-item label=\"喷码直接入库\" prop=\"Is_Part_Prepare\">\r\n              <el-switch\r\n                v-model=\"form.Is_Spraying_With_StockIn\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"清单导入构件重量\" prop=\"Comp_Compute_With_Part\">\r\n              <el-radio-group v-model=\"form.Comp_Compute_With_Part\" size=\"small\" @change=\"computedChange\">\r\n                <el-radio :label=\"false\">按清单导入</el-radio>\r\n                <el-radio :label=\"true\">按零件导入</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <el-form-item label=\"发货过磅\" prop=\"Shipping_Weigh_Enabled\">\r\n              <el-switch\r\n                v-model=\"form.Shipping_Weigh_Enabled\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"原料入库是否质检\" prop=\"Is_Raw_Instore_Check\">\r\n              <el-switch\r\n                v-model=\"form.Is_Raw_Instore_Check\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"辅料入库是否质检\" prop=\"Is_Aux_Instore_Check\">\r\n              <el-switch\r\n                v-model=\"form.Is_Aux_Instore_Check\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"发货单号自动生成\" prop=\"Shipping_Order_Number_Auto_Generate\">\r\n              <el-switch\r\n                v-model=\"form.Shipping_Order_Number_Auto_Generate\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"唯一码管理\" prop=\"Materiel_Unique_Types\">\r\n              <el-checkbox-group v-model=\"form.Materiel_Unique_Types\">\r\n                <el-checkbox label=\"1\">板材</el-checkbox>\r\n                <el-checkbox label=\"2\">型材</el-checkbox>\r\n                <el-checkbox label=\"3\">钢卷</el-checkbox>\r\n                <el-checkbox label=\"99\">其他原料</el-checkbox>\r\n                <el-checkbox label=\"100\">辅料</el-checkbox>\r\n              </el-checkbox-group>\r\n            </el-form-item>\r\n            <el-form-item label-width=\"160px\" label=\"套料工序允许直接报工\" prop=\"allowDirectReportingAfterNesting\">\r\n              <el-switch\r\n                v-model=\"form.allowDirectReportingAfterNesting\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label-width=\"220px\" label=\"是否发货计划通过审批才能发货\" prop=\"Is_Shipping_Plan_Approval\">\r\n              <el-switch\r\n                v-model=\"form.Is_Shipping_Plan_Approval\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n              />\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"submit-btn\">\r\n      <el-button\r\n        type=\"primary\"\r\n        :loading=\"btnLoading\"\r\n        @click=\"handleSubmit('form', 'form2')\"\r\n      >保存</el-button>\r\n      <el-button plain @click=\"resetForm()\">重置</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetUserPage } from '@/api/sys'\r\nimport { GetFactoryEntity, SupplyFactoryInfo } from '@/api/PRO/factory'\r\nimport { GetProfessionalType } from '@/api/plm/material'\r\nimport OSSUpload from '@/views/plm/components/ossupload'\r\nimport getCommonData from '@/mixins/PRO/get-common-data'\r\nexport default {\r\n  components: {\r\n    OSSUpload\r\n  },\r\n  mixins: [getCommonData],\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      userOptions: [],\r\n      src: '',\r\n      srcList: [''],\r\n      num: 12,\r\n      form: {\r\n        Scan_UniqueCode_Enabled: false,\r\n        Is_Mat_Duplicate: false,\r\n        Component_Shipping_Approval: false,\r\n        Shipping_Approval_LowerLimit: undefined,\r\n        Short_Name: '',\r\n        Name: '',\r\n        Manager: '',\r\n        Manager_Id: '',\r\n        Address: '',\r\n        Financial_Settlement_Organization_Code: '',\r\n        Financial_Settlement_Organization: '',\r\n        Productivity: '',\r\n        Category: '',\r\n        Professional_Codes: [''],\r\n        Pic_Path: '',\r\n        Id: '',\r\n        Company_Id: '',\r\n        Weigh_Warning_Threshold: undefined,\r\n        Is_Spraying_With_StockIn: false,\r\n        Is_Workshop_Enabled: false, // 是否开启车间管理\r\n        Manage_Cycle_Enabled: false, // 是否开启统计周期\r\n        Is_Part_Prepare: true, // 是否开启零件齐套管理\r\n        Shipping_Weigh_Enabled: true,\r\n        allowDirectReportingAfterNesting: true,\r\n        Is_Shipping_Plan_Approval: true, // 是否发货计划通过审批才能发货\r\n        Manage_Cycle_Begin_Type: '',\r\n        Manage_Cycle_Begin_Date: '',\r\n        Manage_Cycle_End_Type: '',\r\n        Manage_Cycle_End_Date: '',\r\n        Is_Skip_Warehousing_Operation: false,\r\n        Shipper: '',\r\n        Comp_Compute_With_Part: false,\r\n        Is_Raw_Instore_Check: false,\r\n        Is_Aux_Instore_Check: false,\r\n        Shipping_Order_Number_Auto_Generate: true,\r\n        Materiel_Unique_Types: []\r\n      },\r\n      form2: {\r\n        formArr: []\r\n      },\r\n      formArrCopy: [],\r\n      changeForm2: [],\r\n      AllTargetValue: 0,\r\n      AllYear: '',\r\n      AllYearPicker: '',\r\n      NewYear: new Date().getFullYear(),\r\n      rules: {\r\n        Short_Name: [\r\n          { required: true, message: '请输入工厂名称', trigger: 'blur' }\r\n        ],\r\n        Shipping_Approval_LowerLimit: [\r\n          { required: true, message: '请输入', trigger: 'blur' }\r\n        ],\r\n        Category: [{ required: true, message: '请选择', trigger: 'change' }],\r\n        Productivity: [\r\n          {\r\n            required: true,\r\n            validator: this.productivityStatus,\r\n            trigger: 'blur'\r\n          }\r\n        ]\r\n      },\r\n      rules2: {\r\n        Target_value: [\r\n          {\r\n            required: false,\r\n            validator: this.targetValueStatus,\r\n            trigger: 'blur'\r\n          }\r\n        ]\r\n      },\r\n      comType: '',\r\n      Ablity_List: [] // 产能平衡数据\r\n    }\r\n  },\r\n  created() {\r\n    this.getFactoryEntityForm()\r\n    this.getFactoryPeoplelist()\r\n  },\r\n  methods: {\r\n    // 统计周期开关\r\n    changeManageCycleEnabled(e) {\r\n      // this.form.Manage_Cycle_Enabled = !e\r\n      if (!e) {\r\n        this.$confirm(\r\n          '统计周期开启后无法关闭，请确认后开启',\r\n          {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }\r\n        )\r\n          .then(() => {\r\n            this.form.Manage_Cycle_Enabled = true\r\n          })\r\n          .catch(() => {\r\n            this.form.Manage_Cycle_Enabled = false\r\n            this.$message({\r\n              type: 'info',\r\n              message: '已取消'\r\n            })\r\n          })\r\n      }\r\n    },\r\n    // 零件齐套管理开关\r\n    changePartPrepareEnabled(e) {\r\n      if (e) {\r\n        this.$confirm(\r\n          '零件齐套管理按钮关闭后不可再开启，请确认后关闭',\r\n          {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }\r\n        )\r\n          .then(() => {\r\n            this.form.Is_Part_Prepare = false\r\n          })\r\n          .catch(() => {\r\n            this.form.Is_Part_Prepare = true\r\n            this.$message({\r\n              type: 'info',\r\n              message: '已取消'\r\n            })\r\n          })\r\n      }\r\n    },\r\n    // 统计日期校验\r\n    changeCheckNum(e, type) {\r\n      if (Number(e) < 1 || Number(e) > 31) {\r\n        if (type === 1) {\r\n          this.form.Manage_Cycle_Begin_Date = ''\r\n        } else if (type === 2) {\r\n          this.form.Manage_Cycle_End_Date = ''\r\n        }\r\n      }\r\n    },\r\n    // 车间管理开关\r\n    changeWorkshop(e) {\r\n      // console.log(e, \"eee\");\r\n      if (!e) {\r\n        this.$confirm(\r\n          '车间管理开启后无法关闭，请确认您的业务管理方式中涉及车间层级',\r\n          {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }\r\n        )\r\n          .then(() => {\r\n            this.form.Is_Workshop_Enabled = true\r\n          })\r\n          .catch(() => {\r\n            this.form.Is_Workshop_Enabled = false\r\n            this.$message({\r\n              type: 'info',\r\n              message: '已取消'\r\n            })\r\n          })\r\n      }\r\n      // console.log(this.form.Is_Workshop_Enabled, \"this.form.Is_Workshop_Enabled\");\r\n    },\r\n    productivityStatus(rule, value, callback) {\r\n      if (value === '') {\r\n        callback(new Error('请输入'))\r\n      } else if (!Number(value) && Number(value) !== 0) {\r\n        callback(new Error('只能为数字'))\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    targetValueStatus(rule, value, callback) {\r\n      if (!value) {\r\n        callback()\r\n      } else if (!Number(value) && Number(value) !== 0) {\r\n        callback(new Error('只能为数字'))\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    // targetValueStatus2(rule, value, callback) {\r\n    //   if (!value) {\r\n    //     callback();\r\n    //   } else if (!Boolean(Number(value)) && Number(value) !== 0) {\r\n    //     callback(new Error(\"只能为数字\"));\r\n    //   } else {\r\n    //     callback();\r\n    //   }\r\n    // },\r\n\r\n    getFactoryEntityForm() {\r\n      GetFactoryEntity({\r\n        id: localStorage.getItem('CurReferenceId')\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const {\r\n            Name,\r\n            Short_Name,\r\n            Category,\r\n            Professional_Codes,\r\n            Id,\r\n            Company_Id,\r\n            Manager,\r\n            Manager_Id,\r\n            Address,\r\n            Financial_Settlement_Organization_Code,\r\n            Financial_Settlement_Organization,\r\n            Productivity,\r\n            Pic_Path,\r\n            Weigh_Warning_Threshold,\r\n            Is_Skip_Warehousing_Operation,\r\n            Is_Workshop_Enabled,\r\n            Nested_Must_Before_Processing,\r\n            Manage_Cycle_Enabled,\r\n            Is_Part_Prepare,\r\n            Is_Spraying_With_StockIn,\r\n            Manage_Cycle_Begin_Type,\r\n            Manage_Cycle_Begin_Date,\r\n            Manage_Cycle_End_Type,\r\n            Manage_Cycle_End_Date,\r\n            Component_Shipping_Approval,\r\n            Is_Mat_Duplicate,\r\n            Scan_UniqueCode_Enabled,\r\n            Shipping_Weigh_Enabled,\r\n            Shipping_Approval_LowerLimit,\r\n            Shipper,\r\n            Comp_Compute_With_Part,\r\n            Is_Raw_Instore_Check,\r\n            Is_Aux_Instore_Check,\r\n            Shipping_Order_Number_Auto_Generate,\r\n            Materiel_Unique_Types,\r\n            Is_Shipping_Plan_Approval\r\n          } = res.Data.entity\r\n          this.form.Is_Mat_Duplicate = Is_Mat_Duplicate\r\n          this.form.Scan_UniqueCode_Enabled = Scan_UniqueCode_Enabled\r\n          this.form.Shipping_Weigh_Enabled = Shipping_Weigh_Enabled\r\n          this.form.Component_Shipping_Approval = Component_Shipping_Approval\r\n          this.form.Shipping_Approval_LowerLimit = Shipping_Approval_LowerLimit\r\n          this.form.Short_Name = Short_Name\r\n          this.form.Name = Name\r\n          this.form.Category = Category\r\n          this.form.Professional_Codes = Professional_Codes\r\n          this.form.Id = Id\r\n          this.form.Company_Id = Company_Id\r\n          this.form.Manager = Manager\r\n          this.form.Manager_Id = Manager_Id\r\n          this.form.Address = Address\r\n          this.form.Financial_Settlement_Organization_Code =\r\n            Financial_Settlement_Organization_Code\r\n          this.form.Financial_Settlement_Organization =\r\n            Financial_Settlement_Organization\r\n          this.form.Productivity = Productivity\r\n          this.form.Pic_Path = Pic_Path\r\n          this.form.Is_Workshop_Enabled = Is_Workshop_Enabled\r\n          this.form.allowDirectReportingAfterNesting = !Nested_Must_Before_Processing\r\n          this.form.Is_Shipping_Plan_Approval = Is_Shipping_Plan_Approval\r\n          this.form.Is_Skip_Warehousing_Operation = Is_Skip_Warehousing_Operation\r\n          this.form.Weigh_Warning_Threshold = Weigh_Warning_Threshold || undefined\r\n          this.form.Manage_Cycle_Enabled = Manage_Cycle_Enabled\r\n          this.form.Is_Part_Prepare = Is_Part_Prepare\r\n          this.form.Is_Spraying_With_StockIn = Is_Spraying_With_StockIn\r\n          this.form.Manage_Cycle_Begin_Type = Manage_Cycle_Begin_Type || ''\r\n          this.form.Manage_Cycle_Begin_Date = Manage_Cycle_Begin_Date || ''\r\n          this.form.Manage_Cycle_End_Type = Manage_Cycle_End_Type || ''\r\n          this.form.Manage_Cycle_End_Date = Manage_Cycle_End_Date || ''\r\n          this.form.Comp_Compute_With_Part = Comp_Compute_With_Part\r\n          this.form.Is_Raw_Instore_Check = Is_Raw_Instore_Check\r\n          this.form.Is_Aux_Instore_Check = Is_Aux_Instore_Check\r\n          this.form.Shipping_Order_Number_Auto_Generate = Shipping_Order_Number_Auto_Generate\r\n          this.form.Materiel_Unique_Types = Materiel_Unique_Types.split(',')\r\n          this.form.Shipper = Shipper || ''\r\n          this.src = Pic_Path\r\n          this.srcList[0] = Pic_Path\r\n          this.formArrCopy = res.Data.list\r\n          this.AllYear = this.NewYear\r\n          this.AllYearPicker = ''\r\n          this.AllTargetValue = 0\r\n          this.form2.formArr = res.Data.list.filter((item) => {\r\n            return item.Year == this.NewYear\r\n          })\r\n          this.form2.formArr.forEach((item) => {\r\n            this.AllTargetValue =\r\n              this.AllTargetValue + Number(item.Target_value)\r\n          })\r\n          if (this.form2.formArr.length < 12) {\r\n            // this.form2.formArr = res.Data.list;\r\n            for (let i = 1; i <= 12; i++) {\r\n              const temp = this.form2.formArr.find((item) => {\r\n                return item.Month == i\r\n              })\r\n              if (!temp) {\r\n                const month = {\r\n                  Factory_id: Id,\r\n                  Professional_Code: Professional_Codes[0],\r\n                  Year: this.NewYear,\r\n                  Month: i,\r\n                  Target_value: ''\r\n                }\r\n                this.form2.formArr.push(month)\r\n              }\r\n            }\r\n          }\r\n          this.form2.formArr.sort(function(a, b) {\r\n            return a.Month - b.Month\r\n          })\r\n          this.Ablity_List = res.Data.Ablity_List\r\n          this.getProfessionalTypeList()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getProfessionalTypeList() {\r\n      GetProfessionalType({\r\n        is_System: false,\r\n        pagesize: -1,\r\n        companyId: this.form.Company_Id\r\n      }).then((res) => {\r\n        this.comType = res.Data.Data\r\n      })\r\n    },\r\n    changeCategory(e) {\r\n      const temp = this.comType.find((item) => {\r\n        return item.Name == e\r\n      })\r\n      this.form.Professional_Codes[0] = temp.Code\r\n      this.form2.formArr.forEach((item) => {\r\n        item.Professional_Code = temp.Code\r\n      })\r\n      this.formArrCopy.forEach((item) => {\r\n        item.Professional_Code = temp.Code\r\n      })\r\n    },\r\n    changeYear(e) {\r\n      if (e) {\r\n        this.AllYear = e\r\n      } else {\r\n        this.AllYear = this.NewYear\r\n      }\r\n      if (this.AllYear == this.NewYear || this.AllYear == this.NewYear - 1) {\r\n        this.AllYearPicker = ''\r\n      }\r\n      this.AllTargetValue = 0\r\n      this.form2.formArr = this.formArrCopy.filter((item) => {\r\n        return item.Year == this.AllYear\r\n      })\r\n      if (this.form2.formArr.length < 12) {\r\n        for (let i = 1; i <= 12; i++) {\r\n          const temp = this.form2.formArr.find((item) => {\r\n            return item.Month == i\r\n          })\r\n          if (!temp) {\r\n            const month = {\r\n              Factory_id: this.form.Id,\r\n              Professional_Code: this.form.Professional_Codes[0],\r\n              Year: this.AllYear,\r\n              Month: i,\r\n              Target_value: ''\r\n            }\r\n            this.form2.formArr.push(month)\r\n          }\r\n        }\r\n      }\r\n      this.form2.formArr.sort(function(a, b) {\r\n        return a.Month - b.Month\r\n      })\r\n      this.form2.formArr.forEach((item) => {\r\n        this.AllTargetValue = this.AllTargetValue + Number(item.Target_value)\r\n      })\r\n    },\r\n    TargetValueInput(e, index) {\r\n      if (this.formArrCopy.length != 0) {\r\n        const temp = this.formArrCopy.find((item) => {\r\n          return (\r\n            item.Year == this.form2.formArr[index].Year &&\r\n            item.Month == this.form2.formArr[index].Month\r\n          )\r\n        })\r\n        if (temp) {\r\n          temp.Target_value = e\r\n        } else {\r\n          this.formArrCopy.push(this.form2.formArr[index])\r\n        }\r\n      } else {\r\n        this.formArrCopy.push(this.form2.formArr[index])\r\n      }\r\n      this.AllTargetValue = 0\r\n      this.form2.formArr.forEach((item) => {\r\n        this.AllTargetValue = this.AllTargetValue + Number(item.Target_value)\r\n      })\r\n    },\r\n    async querySearchAsync(queryString, cb) {\r\n      let results = []\r\n      results = await this.getUserList(queryString)\r\n      cb(results)\r\n    },\r\n    getUserList(queryString = '') {\r\n      return new Promise((resolve) => {\r\n        GetUserPage({\r\n          Page: 1,\r\n          pageSize: 20,\r\n          Search: queryString\r\n        }).then((res) => {\r\n          this.userOptions = res.Data.Data.map((v) => {\r\n            this.$set(v, 'value', v.Display_Name)\r\n            return v\r\n          })\r\n          resolve(this.userOptions)\r\n        })\r\n      })\r\n    },\r\n    blur() {\r\n      const temp = this.userOptions.find((item) => {\r\n        return item.Display_Name == this.form.Manager\r\n      })\r\n      if (temp) {\r\n        this.form.Manager_Id = temp.Id\r\n      } else {\r\n        this.form.Manager_Id = ''\r\n      }\r\n    },\r\n    clear() {\r\n      this.form.Manager_Id = ''\r\n      this.form.Manager = ''\r\n      this.$refs.autocomplete.activated = true\r\n    },\r\n    handleSelect(item) {\r\n      this.form.Manager_Id = item.Id\r\n      this.form.Manager = item.Display_Name\r\n    },\r\n    uploadSuccess(response, file, fileList) {\r\n      const imgObj = { File_Name: '', File_Url: '' }\r\n      if (file.hasOwnProperty('response')) {\r\n        imgObj.File_Url = file.response.Data.split('*')[0]\r\n        imgObj.File_Name = file.response.Data.split('*')[3]\r\n      } else {\r\n        imgObj.File_Url = file.url\r\n      }\r\n      this.form.Pic_Path = imgObj.File_Url\r\n      this.src = file.response.encryptionUrl\r\n      this.srcList[0] = file.response.encryptionUrl\r\n      // let temp = this.srcList;\r\n      // this.$set(temp, \"0\", this.srcList[0]);\r\n    },\r\n    uploadExceed(files, fileList) {\r\n      this.$message({\r\n        type: 'warning',\r\n        message: '已超过文件上传最大数量'\r\n      })\r\n    },\r\n    uploadRemove(file, fileList) {},\r\n    handlePreview(file) {},\r\n    deleteItem() {\r\n      this.form.Pic_Path = ''\r\n      this.src = ''\r\n      this.srcList[0] = ''\r\n    },\r\n    handleSubmit(formName1, formName2) {\r\n      this.$confirm('确认保存', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          const form = { ...this.form }\r\n          const form2 = [...this.formArrCopy]\r\n          const form3 = JSON.parse(JSON.stringify(this.Ablity_List))\r\n          console.log(this.Ablity_List, 'Ablity_List')\r\n          form3.map(item => {\r\n            delete item['Component_Type_Name']\r\n            item.Production_Capacity = item.Production_Capacity ? Number(item.Production_Capacity) : item.Production_Capacity\r\n          })\r\n          form.Materiel_Unique_Types = form.Materiel_Unique_Types.join(',')\r\n          form.Nested_Must_Before_Processing = !form.allowDirectReportingAfterNesting\r\n          console.log(form, 'form')\r\n          this.$refs[formName1].validate((valid) => {\r\n            if (valid) {\r\n              this.$refs[formName2].validate((valid) => {\r\n                if (valid) {\r\n                  const obj = {\r\n                    entity: form,\r\n                    list: form2,\r\n                    Ability_List: form3\r\n                  }\r\n                  if (form.Manage_Cycle_Enabled) {\r\n                    if (!form.Manage_Cycle_Begin_Type || !form.Manage_Cycle_Begin_Date || !form.Manage_Cycle_End_Type || !form.Manage_Cycle_End_Date) {\r\n                      this.$message.warning('请补全统计周期')\r\n                      return\r\n                    }\r\n                  }\r\n                  this.btnLoading = true\r\n                  SupplyFactoryInfo(obj).then((res) => {\r\n                    if (res.IsSucceed) {\r\n                      this.$message({\r\n                        message: '保存成功',\r\n                        type: 'success'\r\n                      })\r\n                      this.getFactoryEntityForm()\r\n                    } else {\r\n                      this.$message({\r\n                        message: res.Message,\r\n                        type: 'error'\r\n                      })\r\n                    }\r\n                  })\r\n                  this.btnLoading = false\r\n                } else {\r\n                  return false\r\n                }\r\n              })\r\n            } else {\r\n              return false\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n    },\r\n    resetForm() {\r\n      this.getFactoryEntityForm()\r\n    },\r\n    computedChange(val) {\r\n      console.log(val)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\nheader {\r\n  padding: 20px;\r\n  font-size: 20px;\r\n}\r\n\r\n.cs-z-page-main-content{\r\n  padding-bottom: 62px;\r\n}\r\n\r\n.manage-cycle-input{\r\n  ::v-deep{\r\n    .el-select .el-input {\r\n      width: 120px !important;\r\n    }\r\n  }\r\n}\r\n\r\n.basic-information {\r\n  ::v-deep {\r\n    .el-form-item__content {\r\n      min-width: 250px;\r\n      margin-right: 30px;\r\n      .cs-input{\r\n\r\n        .el-input {\r\n          width: unset;\r\n        }\r\n      }\r\n      .el-input {\r\n        width: 250px;\r\n      }\r\n    }\r\n\r\n    .el-switch.is-disabled .el-switch__core,\r\n    .el-switch.is-disabled .el-switch__label {\r\n      cursor: pointer;\r\n    }\r\n    .is-workshop {\r\n      .el-switch.is-disabled .el-switch__core,\r\n      .el-switch.is-disabled .el-switch__label {\r\n        cursor: not-allowed;\r\n      }\r\n    }\r\n    .el-switch.is-disabled {\r\n      opacity: 1;\r\n      .el-switch.is-checked .el-switch__core {\r\n        border-color: #298dff;\r\n        background-color: #298dff;\r\n      }\r\n    }\r\n  }\r\n  .manage-cycle{\r\n    display: block;\r\n    .text {\r\n      margin: 12px 0;\r\n      text-align: center;\r\n    }\r\n    ::v-deep {\r\n      .manage-cycle-select {\r\n        .el-input{\r\n          width: 80px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .factory-img {\r\n    //display: block;\r\n  }\r\n\r\n  .img-item-icon {\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    color: #333;\r\n    font-size: 20px;\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.year-batch-production {\r\n  .radio-box {\r\n    .el-radio-group {\r\n      padding-left: 30px;\r\n    }\r\n    .el-radio-button {\r\n      font-size: 20px;\r\n    }\r\n    ::v-deep .el-input--small .el-input__inner {\r\n      height: 30px;\r\n      line-height: 30px;\r\n      border-radius: 0 4px 4px 0;\r\n      border-left: none;\r\n    }\r\n    ::v-deep .el-icon-circle-close {\r\n      color: #d0d3db;\r\n    }\r\n  }\r\n\r\n  p {\r\n    padding-left: 30px;\r\n  }\r\n}\r\n.submit-btn {\r\n  padding: 16px 0 16px 32px;\r\n  position: absolute;\r\n  z-index: 99;\r\n  bottom: 16px;\r\n  background-color: #fff;\r\n  width: calc(100% - 48px);\r\n}\r\n\r\n::v-deep .input-number{\r\n      input{\r\n        padding-right: 2px;\r\n      }\r\n    }\r\n\r\n.ml-8{\r\n  margin: 0 8px;\r\n}\r\n.w80{\r\n  ::v-deep .el-input{\r\n    width: 80% !important;\r\n\r\n  }\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\index.vue?vue&type=style&index=0&id=37abe55c&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\index.vue", "mtime": 1758266768055}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYXBwLWNvbnRhaW5lcnsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IHJvdzsNCiAgaGVpZ2h0OiAxMDAlOw0KICAuY2FyZC14ew0KICAgIHBhZGRpbmc6IDE2cHg7DQogICAgZmxleDogMTsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZjsNCiAgICAuY2FyZC14LXRvcHsNCiAgICAgIG1hcmdpbi1ib3R0b206IDE2cHg7DQogICAgfQ0KICAgIC50YWJsZS1zZWN0aW9uIHsNCiAgICAgIGZsZXg6IDE7DQogICAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/project-config/product-mfg-path", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <ProjectData @setProjectData=\"setProjectData\" />\r\n    <div class=\"card-x\">\r\n      <div class=\"card-x-top\">\r\n        <el-button type=\"success\" :disabled=\"!sysProjectId\" @click=\"handleAdd\">新增</el-button>\r\n\r\n        <el-button type=\"primary\" :disabled=\"!sysProjectId\" @click=\"handleAddProject\">同步项目配置</el-button>\r\n        <el-button type=\"primary\" :disabled=\"!sysProjectId\" @click=\"handleReset\">恢复工厂默认配置</el-button>\r\n      </div>\r\n      <div class=\"table-section\">\r\n        <bt-table\r\n          ref=\"projectTable\"\r\n          code=\"ProductMftPathConfig\"\r\n          :custom-table-config=\"tableConfig\"\r\n          :grid-data-handler=\"handleGridData\"\r\n          :loading=\"loading\"\r\n        >\r\n          <template #Type=\"{row}\">\r\n            <div>\r\n              <span :style=\"{color:row.Type===1 ?'#d29730': row.Type===2?'#20bbc7':'#de85e4'}\">\r\n                {{ getCurBomName(row.Bom_Level) }}工艺\r\n              </span>\r\n            </div>\r\n          </template>\r\n          <template #Type1=\"{row}\">\r\n            {{ row.Component_Type }}\r\n          </template>\r\n          <template #actions=\"{row}\">\r\n            <div>\r\n              <el-button v-if=\"!row.isSysDefault\" type=\"text\" @click=\"handleEdit(row)\">编辑</el-button>\r\n              <el-button type=\"text\" style=\"color: red\" @click=\"handleDelete(row)\">删除</el-button>\r\n            </div>\r\n          </template>\r\n        </bt-table>\r\n\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"title\"\r\n      class=\"plm-custom-dialog\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"30%\"\r\n      top=\"5vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        ref=\"content\"\r\n        :sys-project-id=\"sysProjectId\"\r\n        @refresh=\"fetchData\"\r\n        @close=\"handleClose\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <AddDialog ref=\"dialog\" :bom-list=\"bomList\" :sys-project-id=\"sysProjectId\" @refresh=\"fetchData\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ProjectData from '../components/ProjectData.vue'\r\nimport { GetLibList, DeleteTechnology, RestoreFactoryTechnologyFromProject } from '@/api/PRO/technology-lib'\r\nimport ProjectAdd from './component/ProjectAddDialog.vue'\r\nimport AddDialog from '@/views/PRO/process-path/compoments/Add.vue'\r\nimport { GetBOMInfo, getBomName } from '@/views/PRO/bom-setting/utils'\r\n\r\nexport default {\r\n  name: 'PROProductMfgPath',\r\n  components: {\r\n    ProjectData,\r\n    ProjectAdd,\r\n    AddDialog\r\n  },\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      currentComponent: '',\r\n      bomList: [],\r\n      title: '',\r\n      sysProjectId: '',\r\n      tableConfig: {\r\n        tableColumns: [],\r\n        tableActions: [],\r\n        tableData: [],\r\n        operateOptions: {\r\n          width: 120,\r\n          align: 'center',\r\n          isShow: false\r\n        }\r\n      },\r\n      loading: false,\r\n      selectedProjects: []\r\n    }\r\n  },\r\n  async mounted() {\r\n    // this.fetchData()\r\n    const { list } = await GetBOMInfo()\r\n    this.bomList = list\r\n    console.log('bomList', this.bomList)\r\n  },\r\n  methods: {\r\n    getCurBomName(code) {\r\n      const currentBomInfo = this.bomList.find(item => {\r\n        return item.Code.toString() === code.toString()\r\n      })\r\n      return currentBomInfo?.Display_Name || ''\r\n    },\r\n    async fetchData() {\r\n      if (!this.sysProjectId) {\r\n        this.tableConfig.tableData = []\r\n        return\r\n      }\r\n      this.loading = true\r\n      try {\r\n        const params = {\r\n          // Bom_Level: 1,\r\n          Type: 0,\r\n          Sys_Project_Id: this.sysProjectId\r\n        }\r\n        const res = await GetLibList(params)\r\n        if (res.IsSucceed) {\r\n          this.tableConfig.tableData = await Promise.all(\r\n            res.Data.map(async v => {\r\n              v.isSysDefault = !v.Sys_Project_Id\r\n              v.bomName = await getBomName(v.Type)\r\n              return v\r\n            })\r\n          )\r\n        }\r\n      } catch (error) {\r\n        console.log('error', error)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    handleGridData(data) {\r\n      console.log('data', data)\r\n      return data\r\n    },\r\n    handleAdd() {\r\n      console.log('新增')\r\n      this.$nextTick(() => {\r\n        this.$refs['dialog'].handleOpen()\r\n      })\r\n    },\r\n    handleEdit(row) {\r\n      console.log(row)\r\n      this.$nextTick(() => {\r\n        this.$refs['dialog'].handleOpen(row)\r\n      })\r\n    },\r\n    handleDelete(row) {\r\n      this.$confirm('是否删除该工艺', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbLoading = true\r\n        DeleteTechnology({\r\n          technologyId: row.Id,\r\n          sysProjectId: this.sysProjectId\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.$confirm('此操作将会恢复到工厂的产品生产路径, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        RestoreFactoryTechnologyFromProject({\r\n          Sys_Project_Id: this.sysProjectId\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '恢复成功!'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    handleAddProject() {\r\n      console.log('同步项目配置')\r\n      this.dialogVisible = true\r\n      this.currentComponent = 'ProjectAdd'\r\n      this.title = '同步项目配置'\r\n    },\r\n\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n      this.currentComponent = ''\r\n      this.title = ''\r\n    },\r\n    setProjectData(data) {\r\n      this.selectedProjects = data\r\n      this.sysProjectId = data?.Sys_Project_Id || ''\r\n      console.log('selectedProjects', this.selectedProjects)\r\n      this.fetchData()\r\n    }\r\n  }\r\n\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container{\r\n  display: flex;\r\n  flex-direction: row;\r\n  height: 100%;\r\n  .card-x{\r\n    padding: 16px;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    background-color: #ffffff;\r\n    .card-x-top{\r\n      margin-bottom: 16px;\r\n    }\r\n    .table-section {\r\n      flex: 1;\r\n      background: #fff;\r\n      border-radius: 4px;\r\n      overflow: hidden;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
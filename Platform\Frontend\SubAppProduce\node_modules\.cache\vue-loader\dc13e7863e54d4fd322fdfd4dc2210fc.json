{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\index.vue?vue&type=style&index=0&id=37abe55c&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\index.vue", "mtime": 1757468128031}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYXBwLWNvbnRhaW5lcnsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IHJvdzsNCiAgaGVpZ2h0OiAxMDAlOw0KICAuY2FyZC14ew0KICAgIHBhZGRpbmc6IDE2cHg7DQogICAgZmxleDogMTsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZjsNCiAgICAuY2FyZC14LXRvcHsNCiAgICAgIG1hcmdpbi1ib3R0b206IDE2cHg7DQogICAgfQ0KICAgIC50YWJsZS1zZWN0aW9uIHsNCiAgICAgIGZsZXg6IDE7DQogICAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/project-config/product-mfg-path", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <ProjectData />\r\n    <div class=\"card-x\">\r\n      <div class=\"card-x-top\">\r\n        <el-button type=\"primary\" @click=\"handleAdd\">新增</el-button>\r\n        <el-button type=\"danger\" @click=\"handleDelete\">删除</el-button>\r\n\r\n        <el-button type=\"primary\" @click=\"handleAddProject\">从项目添加</el-button>\r\n        <el-button type=\"primary\" @click=\"handleAddFactory\">从工厂添加</el-button>\r\n      </div>\r\n      <div class=\"table-section\">\r\n        <bt-table\r\n          ref=\"projectTable\"\r\n          code=\"ProductMftPathConfig\"\r\n          :custom-table-config=\"tableConfig\"\r\n          :grid-data-handler=\"handleGridData\"\r\n          :loading=\"loading\"\r\n          @selection-change=\"handleSelectionChange\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"title\"\r\n      class=\"plm-custom-dialog\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"80%\"\r\n      top=\"5vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        ref=\"content\"\r\n        @close=\"handleClose\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ProjectData from '../components/ProjectData.vue'\r\nimport { GetLibList } from '@/api/PRO/technology-lib'\r\nimport ProjectAdd from './component/ProjectAddDialog.vue'\r\nimport FactoryAdd from './component/FactoryAddDialog.vue'\r\nexport default {\r\n  name: 'PROProductMfgPath',\r\n  components: {\r\n    ProjectData,\r\n    ProjectAdd,\r\n    FactoryAdd\r\n  },\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      currentComponent: '',\r\n      title: '',\r\n      tableConfig: {\r\n        tableColumns: [],\r\n        tableActions: [],\r\n        tableData: [],\r\n        checkbox: true,\r\n        operateOptions: {\r\n          width: 120,\r\n          align: 'center',\r\n          isShow: false\r\n        }\r\n      },\r\n      loading: false,\r\n      selectedProjects: []\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    async fetchData() {\r\n      this.loading = true\r\n      try {\r\n        const params = {\r\n          Id: '',\r\n          Type: 0\r\n        }\r\n        const res = await GetLibList(params)\r\n        if (res.IsSucceed) {\r\n          this.tableConfig.tableData = res.Data\r\n        }\r\n      } catch (error) {\r\n        console.log('error', error)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    handleGridData(data) {\r\n      console.log('data', data)\r\n      return data\r\n    },\r\n    handleSelectionChange(selection) {\r\n      console.log('selection', selection)\r\n    },\r\n    handleAdd() {\r\n      console.log('新增')\r\n    },\r\n    handleDelete() {\r\n      console.log('删除')\r\n    },\r\n    handleAddProject() {\r\n      console.log('从项目添加')\r\n      this.dialogVisible = true\r\n      this.currentComponent = 'ProjectAdd'\r\n      this.title = '从项目添加'\r\n    },\r\n    handleAddFactory() {\r\n      console.log('从工厂添加')\r\n      this.dialogVisible = true\r\n      this.currentComponent = 'FactoryAdd'\r\n      this.title = '从工厂添加'\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n      this.currentComponent = ''\r\n      this.title = ''\r\n    }\r\n  }\r\n\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container{\r\n  display: flex;\r\n  flex-direction: row;\r\n  height: 100%;\r\n  .card-x{\r\n    padding: 16px;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    background-color: #ffffff;\r\n    .card-x-top{\r\n      margin-bottom: 16px;\r\n    }\r\n    .table-section {\r\n      flex: 1;\r\n      background: #fff;\r\n      border-radius: 4px;\r\n      overflow: hidden;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
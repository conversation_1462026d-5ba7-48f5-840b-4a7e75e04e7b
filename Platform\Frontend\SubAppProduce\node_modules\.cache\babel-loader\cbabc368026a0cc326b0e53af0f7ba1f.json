{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\ToleranceConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\ToleranceConfig.vue", "mtime": 1757468112847}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["DeleteToleranceSetting", "GetToleranceSettingList", "data", "tbLoading", "tbData", "dialogVisible", "form", "Id", "Length", "Demand", "Type", "mounted", "getToleranceList", "methods", "_this", "then", "res", "IsSucceed", "Data", "map", "item", "Modify_Date", "Create_Date", "TypeTag", "$message", "type", "message", "Message", "finally", "editEvent", "row", "JSON", "parse", "stringify", "$emit", "removeEvent", "_this2", "$confirm", "confirmButtonText", "cancelButtonText", "id", "catch"], "sources": ["src/views/PRO/factoryQuality/checkoutGroup/components/ToleranceConfig.vue"], "sourcesContent": ["<template>\r\n  <div style=\"height: calc(100vh - 300px)\">\r\n    <vxe-table\r\n      v-loading=\"tbLoading\"\r\n      :empty-render=\"{name: 'NotData'}\"\r\n      show-header-overflow\r\n      element-loading-spinner=\"el-icon-loading\"\r\n      element-loading-text=\"拼命加载中\"\r\n      empty-text=\"暂无数据\"\r\n      height=\"100%\"\r\n      align=\"left\"\r\n      stripe\r\n      :data=\"tbData\"\r\n      resizable\r\n      :auto-resize=\"true\"\r\n      class=\"cs-vxe-table\"\r\n      :tooltip-config=\"{ enterable: true }\"\r\n    >\r\n      <vxe-column\r\n        show-overflow=\"tooltip\"\r\n        sortable\r\n        field=\"Length\"\r\n        title=\"长度\"\r\n        min-width=\"150\"\r\n        align=\"center\"\r\n      >\r\n        <template #default=\"{ row }\">\r\n          <span>\r\n            {{ row.TypeTag }}\r\n            {{ row.Length }}\r\n          </span>\r\n        </template>\r\n      </vxe-column>\r\n      <vxe-column\r\n        show-overflow=\"tooltip\"\r\n        sortable\r\n        min-width=\"150\"\r\n        align=\"left\"\r\n        field=\"Demand\"\r\n        title=\"公差要求\"\r\n      />\r\n      <vxe-column\r\n        show-overflow=\"tooltip\"\r\n        sortable\r\n        field=\"Modify_Date\"\r\n        title=\"编辑时间\"\r\n        min-width=\"150\"\r\n        align=\"center\"\r\n      />\r\n      <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" align=\"center\" show-overflow>\r\n        <template #default=\"{ row }\">\r\n          <el-button type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\r\n          <el-divider direction=\"vertical\" />\r\n          <el-button type=\"text\" @click=\"removeEvent(row)\">删除</el-button>\r\n        </template>\r\n      </vxe-column>\r\n    </vxe-table>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport {\r\n  DeleteToleranceSetting,\r\n  GetToleranceSettingList\r\n} from '@/api/PRO/qualityInspect/quality-management'\r\n\r\nexport default {\r\n\r\n  data() {\r\n    return {\r\n      tbLoading: false,\r\n      tbData: [],\r\n      dialogVisible: false,\r\n      form: {\r\n        Id: '',\r\n        Length: 0,\r\n        Demand: '',\r\n        Type: 1\r\n      }\r\n\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getToleranceList()\r\n  },\r\n  methods: {\r\n    getToleranceList() {\r\n      this.tbLoading = true\r\n      GetToleranceSettingList({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.map(item => {\r\n            item.Modify_Date = item.Modify_Date || item.Create_Date\r\n            item.TypeTag = item.Type === 1 ? '<' : item.Type === 2 ? '<=' : item.Type === 3 ? '>' : item.Type === 4 ? '>=' : '='\r\n            return item\r\n          })\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    editEvent(row) {\r\n      const form = JSON.parse(JSON.stringify(row))\r\n      this.$emit('edit', form)\r\n    },\r\n    removeEvent(row) {\r\n      this.$confirm('此操作将永久删除该公差配置, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          DeleteToleranceSetting({ id: row.Id }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                type: 'success',\r\n                message: '删除成功!'\r\n              })\r\n              this.getToleranceList()\r\n            } else {\r\n              this.$message({\r\n                type: 'error',\r\n                message: res.Message\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    }\r\n\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA,SACAA,sBAAA,EACAC,uBAAA,QACA;AAEA;EAEAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,MAAA;MACAC,aAAA;MACAC,IAAA;QACAC,EAAA;QACAC,MAAA;QACAC,MAAA;QACAC,IAAA;MACA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,gBAAA;EACA;EACAC,OAAA;IACAD,gBAAA,WAAAA,iBAAA;MAAA,IAAAE,KAAA;MACA,KAAAX,SAAA;MACAF,uBAAA,KAAAc,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAH,KAAA,CAAAV,MAAA,GAAAY,GAAA,CAAAE,IAAA,CAAAC,GAAA,WAAAC,IAAA;YACAA,IAAA,CAAAC,WAAA,GAAAD,IAAA,CAAAC,WAAA,IAAAD,IAAA,CAAAE,WAAA;YACAF,IAAA,CAAAG,OAAA,GAAAH,IAAA,CAAAV,IAAA,eAAAU,IAAA,CAAAV,IAAA,gBAAAU,IAAA,CAAAV,IAAA,eAAAU,IAAA,CAAAV,IAAA;YACA,OAAAU,IAAA;UACA;QACA;UACAN,KAAA,CAAAU,QAAA;YACAC,IAAA;YACAC,OAAA,EAAAV,GAAA,CAAAW;UACA;QACA;MACA,GAAAC,OAAA;QACAd,KAAA,CAAAX,SAAA;MACA;IACA;IACA0B,SAAA,WAAAA,UAAAC,GAAA;MACA,IAAAxB,IAAA,GAAAyB,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAH,GAAA;MACA,KAAAI,KAAA,SAAA5B,IAAA;IACA;IACA6B,WAAA,WAAAA,YAAAL,GAAA;MAAA,IAAAM,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAd,IAAA;MACA,GACAV,IAAA;QACAf,sBAAA;UAAAwC,EAAA,EAAAV,GAAA,CAAAvB;QAAA,GAAAQ,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAmB,MAAA,CAAAZ,QAAA;cACAC,IAAA;cACAC,OAAA;YACA;YACAU,MAAA,CAAAxB,gBAAA;UACA;YACAwB,MAAA,CAAAZ,QAAA;cACAC,IAAA;cACAC,OAAA,EAAAV,GAAA,CAAAW;YACA;UACA;QACA;MACA,GACAc,KAAA;QACAL,MAAA,CAAAZ,QAAA;UACAC,IAAA;UACAC,OAAA;QACA;MACA;IACA;EAEA;AACA", "ignoreList": []}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\CombinationDialog.vue?vue&type=style&index=0&id=a9bdef16&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\CombinationDialog.vue", "mtime": 1757922150916}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCjo6di1kZWVwIHsKICAuY2hlY2tJdGVtIHsKICAgIHdpZHRoOiAxMDAlOwogICAgLmVsLWZvcm0taXRlbV9fY29udGVudCB7CiAgICAgIG1hcmdpbi1sZWZ0OiAwICFpbXBvcnRhbnQ7CiAgICB9CiAgfQogIC5hZGRjaGVja0l0ZW0gewogICAgZm9udC1zaXplOiAxNnB4OwogICAgbWFyZ2luLWJvdHRvbTogMTBweDsKICB9Cn0KOjp2LWRlZXAgLmVsLWZvcm0taXRlbSB7CiAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogIC5lbC1mb3JtLWl0ZW1fX2NvbnRlbnQgewogICAgJiA+IC5lbC1pbnB1dCB7CiAgICAgIHdpZHRoOiAyMjBweCAhaW1wb3J0YW50OwogICAgfQogICAgJiA+IC5lbC1zZWxlY3QgewogICAgICB3aWR0aDogMjIwcHggIWltcG9ydGFudDsKICAgIH0KICAgIC5lbC10cmVlLXNlbGVjdC1pbnB1dCB7CiAgICAgIHdpZHRoOiAyMjBweCAhaW1wb3J0YW50OwogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["CombinationDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2sBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CombinationDialog.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup/components/Dialog", "sourcesContent": ["<template>\n  <div>\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"130px\">\n      <el-row>\n        <el-form-item label=\"检查项组合名称\" prop=\"Group_Name\">\n          <el-input v-model=\"form.Group_Name\" />\n        </el-form-item>\n\n        <el-form-item label=\"质检节点\" prop=\"Check_Node_Id\">\n          <el-select\n            v-model=\"form.Check_Node_Id\"\n            clearable\n            style=\"width: 100%\"\n            placeholder=\"请选择质检节点\"\n            @change=\"changeNode\"\n          >\n            <el-option\n              v-for=\"(item, index) in CheckNodeList\"\n              :key=\"index\"\n              :label=\"item.Display_Name\"\n              :value=\"item.Id\"\n            />\n          </el-select>\n        </el-form-item>\n\n        <el-form-item label=\"质检类型\" prop=\"Check_Type\">\n          <el-select\n            v-model=\"Change_Check_Type\"\n            clearable\n            multiple\n            :multiple-limit=\"1\"\n            style=\"width: 100%\"\n            :disabled=\"Isdisable\"\n            placeholder=\"请选择质检类型\"\n            @change=\"SelectType\"\n          >\n            <el-option\n              v-for=\"(item, index) in QualityTypeList\"\n              :key=\"index\"\n              :label=\"item.Name\"\n              :value=\"item.Id\"\n            />\n          </el-select>\n        </el-form-item>\n\n        <el-form-item label=\"专业类别\" prop=\"Pro_Category_Id\">\n          <el-select\n            v-model=\"form.Pro_Category_Id\"\n            clearable\n            style=\"width: 100%\"\n            placeholder=\"请选择专业类别\"\n            @change=\"changeCategory\"\n          >\n            <el-option\n              v-for=\"(item, index) in ProCategoryList\"\n              :key=\"index\"\n              :label=\"item.Name\"\n              :value=\"item.Id\"\n            />\n          </el-select>\n        </el-form-item>\n\n        <el-form-item label=\"检查类型\" prop=\"Questionlab_Ids\">\n          <el-select\n            v-model=\"form.Questionlab_Ids\"\n            style=\"width: 100%\"\n            multiple\n            placeholder=\"请选择检查类型\"\n            @change=\"ChangeCheckType\"\n            @remove-tag=\"removeCheckType\"\n          >\n            <el-option\n              v-for=\"(item, index) in CheckTypeList\"\n              :key=\"index\"\n              :label=\"item.Name\"\n              :value=\"item.Id\"\n            />\n          </el-select>\n        </el-form-item>\n\n        <el-form-item\n          label=\"产品类型\"\n          prop=\"Object_Type_Ids\"\n        >\n          <el-tree-select\n            ref=\"treeSelectObjectType\"\n            v-model=\"form.Object_Type_Ids\"\n            :disabled=\"!Boolean(form.Pro_Category_Id)\"\n            class=\"cs-tree-x\"\n            :tree-params=\"ObjectTypeList\"\n            value-key=\"Id\"\n            @removeTag=\"removeTagFn\"\n          />\n        </el-form-item>\n\n        <el-col :span=\"24\">\n          <h3>检查项设置</h3>\n          <el-form-item label=\"\" prop=\"\" class=\"checkItem\">\n            <el-table :data=\"ProcessFlow\" border style=\"width: 100%\">\n              <el-table-column prop=\"\" label=\"*检查类型\" align=\"center\">\n                <template slot-scope=\"{ row }\">\n                  <el-select\n                    v-model=\"row.Questionlab_Id\"\n                    style=\"width: 100%\"\n                    clearable\n                    placeholder=\"请选择\"\n                  >\n                    <el-option\n                      v-for=\"(item, index) in CheckTypeList\"\n                      :key=\"index\"\n                      :label=\"item.Name\"\n                      :value=\"item.Id\"\n                    />\n                  </el-select>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"\" label=\"*检查项内容\" align=\"center\">\n                <template slot-scope=\"{ row, $index }\">\n                  <el-select\n                    v-model=\"row.Check_Item_Id\"\n                    style=\"width: 100%\"\n                    clearable\n                    placeholder=\"请选择\"\n                    @change=\"ChangeItem($event, $index, row)\"\n                  >\n                    <el-option\n                      v-for=\"(item, index) in CheckItemList\"\n                      :key=\"index\"\n                      :label=\"item.Check_Content\"\n                      :value=\"item.Id\"\n                    />\n                  </el-select>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"\" label=\"*合格标准\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  <el-input\n                    v-model=\"scope.row.Eligibility_Criteria\"\n                    disabled\n                  />\n                </template>\n              </el-table-column>\n              <el-table-column\n                prop=\"address\"\n                label=\"操作\"\n                width=\"140\"\n                align=\"center\"\n              >\n                <template slot-scope=\"{ row, $index }\">\n                  <el-button\n                    type=\"text\"\n                    icon=\"el-icon-top\"\n                    :disabled=\"$index == 0\"\n                    @click=\"moveUpward(row, $index)\"\n                  />\n                  <el-button\n                    type=\"text\"\n                    icon=\"el-icon-bottom\"\n                    :disabled=\"$index == ProcessFlow.length - 1\"\n                    @click=\"moveDown(row, $index)\"\n                  />\n                  <el-button\n                    type=\"text\"\n                    icon=\"el-icon-delete\"\n                    style=\"color: #f56c6c\"\n                    @click.native.prevent=\"deleteRow($index, ProcessFlow)\"\n                  />\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-button\n            type=\"text\"\n            class=\"addcheckItem\"\n            @click=\"addTableData\"\n          >+ 新增检查项</el-button>\n        </el-col>\n        <el-col :span=\"24\" style=\"text-align: right\">\n          <el-form-item style=\"text-align: right\">\n            <el-button @click=\"$emit('close')\">关 闭</el-button>\n            <el-button\n              type=\"primary\"\n              @click=\"handleSubmit('form')\"\n            >确 定</el-button>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n  </div>\n</template>\n\n<script>\nimport { AddCheckItemCombination } from '@/api/PRO/factorycheck'\nimport { EntityQualityList } from '@/api/PRO/factorycheck'\n\nimport { GetCheckTypeList } from '@/api/PRO/factorycheck'\nimport { GetCheckItemList } from '@/api/PRO/factorycheck'\nimport { GetNodeList } from '@/api/PRO/factorycheck'\nimport { GetCompTypeTree } from '@/api/PRO/factorycheck'\nimport {\n  GetFactoryProfessionalByCode,\n  GetMaterialType\n} from '@/api/PRO/factorycheck'\nimport { GetPartTypeTree } from '@/api/PRO/partType'\n\nexport default {\n  data() {\n    return {\n      mode: '', // 区分项目和工厂\n      ProjectId: '', // 项目Id\n      Check_Object_Id: '',\n      checkType: {}, // 区分构件、零件、物料\n      form: {\n        Object_Type_Ids: []\n      },\n      rules: {\n        Check_Content: [\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\n        ],\n        Eligibility_Criteria: [\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\n        ],\n        Group_Name: [\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\n        ],\n        Check_Type: [\n          { required: true, message: '请填写完整表单', trigger: 'change' }\n        ],\n        Questionlab_Ids: [\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\n        ]\n      },\n      title: '',\n      options: [],\n      ProcessFlow: [],\n      CheckTypeList: [], // 检查类型下拉\n      CheckItemList: [], // 检查项下拉\n      Change_Check_Type: [],\n      QualityTypeList: [\n        {\n          Name: '质量',\n          Id: 1\n        },\n        {\n          Name: '探伤',\n          Id: 2\n        }\n      ], // 质检类型\n      ProCategoryList: [], // 专业类别下拉\n      CheckNodeList: [], // 质检节点下拉\n      verification: false,\n      ProCategoryCode: '', // 专业类别Code\n      Eligibility_Criteria: '',\n      ObjectTypeList: {\n        // 对象类型\n        'check-strictly': true,\n        'default-expand-all': true,\n        filterable: false,\n        clickParent: true,\n        data: [],\n        props: {\n          children: 'Children',\n          label: 'Label',\n          value: 'Id'\n        }\n      },\n      Isdisable: false,\n      typeCode: '',\n      typeId: '',\n      partGrade: ''\n    }\n  },\n  watch: {\n    ProcessFlow: {\n      handler(newName, oldName) {\n        console.log(newName)\n        this.form.Questionlab_Ids = []\n        this.ProcessFlow.forEach((item) => {\n          if (\n            item.Questionlab_Id &&\n            !this.form.Questionlab_Ids.includes(item.Questionlab_Id)\n          ) {\n            this.form.Questionlab_Ids.push(item.Questionlab_Id)\n          }\n        })\n      },\n      deep: true\n    }\n  },\n  mounted() {},\n  methods: {\n    async init(title, checkType, data) {\n      this.partGrade = checkType.Code\n      this.Check_Object_Id = checkType.Id\n      this.checkType = checkType\n      this.title = title\n      this.form.Check_Object_Id = checkType.Id\n      this.form.Bom_Level = checkType.Code\n      await this.getProfessionalType() // 专业类别\n      await this.getCheckTypeList() // 检查类型\n      await this.getCheckItemList()\n      await this.getNodeList(data) // 质检节点\n    },\n    async addCheckItemCombination() {\n      await AddCheckItemCombination({\n        Group: this.form,\n        Items: this.ProcessFlow\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            type: 'success',\n            message: '保存成功'\n          })\n          this.$emit('refresh')\n          this.$emit('close')\n          this.dialogData = {}\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    removeTagFn(ids, tag) {\n      console.log('ids', ids)\n      console.log('tag', tag)\n    },\n    SelectType(item) {\n      console.log('item', item)\n\n      if (item.length === 1) {\n        this.form.Check_Type = item[0]\n      } else {\n        this.form.Check_Type = -1\n      }\n      console.log('this.form.Check_Type', this.form.Check_Type)\n    },\n    changeNode(val) {\n      console.log(val)\n      console.log(this.CheckNodeList)\n      if (val) {\n        this.form.Check_Type = this.CheckNodeList.find((v) => {\n          return v.Id === val\n        }).Check_Type\n        // 处理质检类型数据\n\n        this.Change_Check_Type = []\n        if (this.form.Check_Type === 1 || this.form.Check_Type === 2) {\n          this.Isdisable = true\n          this.Change_Check_Type.push(this.form.Check_Type)\n        } else if (this.form.Check_Type === -1) {\n          this.Isdisable = false // 质检类型可编辑\n          this.Change_Check_Type = []\n        } else {\n          this.Change_Check_Type = []\n          this.Isdisable = false\n        }\n        console.log(' this.Isdisable', this.Isdisable)\n      } else {\n        this.Change_Check_Type = []\n      }\n    },\n    getEntityCheckType(data) {\n      console.log(data)\n      EntityQualityList({\n        id: data.Id,\n        check_object_id: this.Check_Object_Id,\n        Bom_Level: this.form.Bom_Level\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.form = res.Data[0].Group\n          console.log(this.form.Object_Type_Ids, 'Object_Type_Ids')\n\n          this.ProcessFlow = res.Data[0].Items\n          this.Change_Check_Type = []\n          // 处理质检类型数据\n          if (this.form.Check_Type === 1 || this.form.Check_Type === 2) {\n            this.Change_Check_Type.push(this.form.Check_Type)\n            if (res.Data[0].CheckNode_Type === -1) {\n              this.Isdisable = false\n            } else {\n              this.Isdisable = true\n            }\n          } else if (this.form.Check_Type === -1) {\n            this.Change_Check_Type = [1, 2]\n            this.Isdisable = true // 质检类型不可编辑\n          } else {\n            this.Change_Check_Type = []\n            this.Isdisable = false\n          }\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    handleSubmit(form) {\n      if (this.Change_Check_Type.length === 0) {\n        this.$message({\n          type: 'error',\n          message: '请选择检查类型'\n        })\n        return\n      }\n      let verification = true\n      if (this.ProcessFlow.length === 0) {\n        verification = false\n      } else {\n        this.ProcessFlow.forEach((val) => {\n          for (const key in val) {\n            if (val[key] === '') {\n              verification = false\n            }\n          }\n        })\n      }\n      if (!verification) {\n        this.$message({\n          type: 'error',\n          message: '请填写完整检查项设置内容'\n        })\n        return\n      }\n\n      const processFlowCopy = JSON.parse(JSON.stringify(this.ProcessFlow))\n      const processFlowNew = []\n      processFlowCopy.forEach((item) => {\n        const processFlowJson = {}\n        processFlowJson.Check_Item_Id = item.Check_Item_Id\n        processFlowJson.Eligibility_Criteria = item.Eligibility_Criteria\n        processFlowJson.Questionlab_Id = item.Questionlab_Id\n        processFlowNew.push(processFlowJson)\n      })\n      const processFlowTemp = processFlowNew.map((item) => {\n        return JSON.stringify(item)\n      })\n      if (new Set(processFlowTemp).size !== processFlowTemp.length) {\n        this.$message({\n          type: 'error',\n          message: '检查项设置内容不能完全相同'\n        })\n        return\n      }\n\n      const processFlowArr = this.ProcessFlow.map((v) => {\n        return v.Questionlab_Id\n      })\n\n      const isIncludes = this.form.Questionlab_Ids.every((item) =>\n        processFlowArr.includes(item)\n      )\n      if (!isIncludes) {\n        this.$message({\n          type: 'error',\n          message: '检查项设置必须包含已选检查类型'\n        })\n        return\n      }\n\n      this.$refs[form].validate((valid) => {\n        if (valid) {\n          this.addCheckItemCombination()\n        } else {\n          return false\n        }\n      })\n    },\n    // 获取专业类别\n    async getProfessionalType() {\n      const Platform =\n        localStorage.getItem('Platform') || localStorage.getItem('CurPlatform')\n      if (Platform === '2') {\n        this.mode = 'factory'\n        await GetFactoryProfessionalByCode().then((res) => {\n          if (res.IsSucceed) {\n            this.ProCategoryList = res.Data\n            const {\n              Code,\n              Id\n            } = res.Data?.find(item => item.Code === 'Steel') || {}\n            this.typeCode = Code\n            this.typeId = Id\n          } else {\n            this.$message({\n              type: 'error',\n              message: res.Message\n            })\n          }\n        })\n      }\n\n      // 获取项目/工厂id\n      this.ProjectId =\n        this.mode === 'factory'\n          ? localStorage.getItem('CurReferenceId')\n          : this.ProjectId\n    },\n\n    // 获取检查类型下拉框\n    async getCheckTypeList() {\n      await GetCheckTypeList({ check_object_id: this.Check_Object_Id, Bom_Level: this.form.Bom_Level }).then(\n        (res) => {\n          if (res.IsSucceed) {\n            this.CheckTypeList = res.Data\n            console.log(res.Data)\n          } else {\n            this.$message({\n              type: 'error',\n              message: res.Message\n            })\n          }\n        }\n      )\n    },\n    // 检查项内容\n    async getCheckItemList() {\n      await GetCheckItemList({ check_object_id: this.Check_Object_Id }).then(\n        (res) => {\n          if (res.IsSucceed) {\n            this.CheckItemList = res.Data\n            console.log(res.Data)\n          } else {\n            this.$message({\n              type: 'error',\n              message: res.Message\n            })\n          }\n        }\n      )\n    },\n    // 通过专业类别选择对象类型\n    changeCategory(val) {\n      this.form.Object_Type_Ids = []\n      this.chooseType(val)\n    },\n    // 通过专业类别选择对象类型\n    chooseType(val) {\n      console.log(this.ProCategoryList)\n      this.ProCategoryCode = this.ProCategoryList.find((v) => {\n        return v.Id === val\n      }).Code\n      this.getObjectTypeList(this.ProCategoryCode) // 对象类型\n    },\n    // 选中表格外检查类型\n    ChangeCheckType(val) {\n      const arrJson = Object.assign([], val)\n      // let index = arrJson.indexOf(Isexist);\n      // this.ProcessFlow.splice(index, 1);\n      console.log(arrJson)\n      if (this.ProcessFlow.length > arrJson.length) {\n        const arrJsonTemp = arrJson.map((item) => {\n          const itemField = {\n            Check_Item_Id: '',\n            Eligibility_Criteria: '',\n            Questionlab_Id: item\n          }\n          this.ProcessFlow.forEach((items) => {\n            if (items.Questionlab_Id === item) {\n              itemField.Check_Item_Id = items.Check_Item_Id\n              itemField.Eligibility_Criteria = items.Eligibility_Criteria\n            }\n          })\n\n          return itemField\n        })\n        this.ProcessFlow = [].concat(arrJsonTemp)\n      } else {\n        for (var i = 0; i < arrJson.length; i++) {\n          const Isexist = this.ProcessFlow.find((v) => {\n            return v.Questionlab_Id === arrJson[i]\n          })\n          if (!Isexist) {\n            this.ProcessFlow.push({\n              Questionlab_Id: arrJson[i],\n              Check_Item_Id: '',\n              Eligibility_Criteria: ''\n            })\n          }\n        }\n      }\n\n      console.log('ChangeCheckType()', this.ProcessFlow)\n    },\n\n    removeCheckType(val) {\n      const Isexist = this.ProcessFlow.find((v) => {\n        return v.Questionlab_Id === val\n      })\n      const index = this.ProcessFlow.indexOf(Isexist)\n      if (Isexist) {\n        this.ProcessFlow.splice(index, 1)\n      }\n    },\n    // 选中检查项内容\n    ChangeItem(data, index, row) {\n      // console.log(data);\n      // console.log(index);\n      // console.log(row)\n      // console.log(this.CheckItemList);\n      row.Eligibility_Criteria = ''\n      this.Eligibility_Criteria = ''\n      this.Eligibility_Criteria = this.CheckItemList.find((v) => {\n        return v.Id === data\n      })?.Eligibility_Criteria\n      this.$set(\n        this.ProcessFlow[index],\n        'Eligibility_Criteria',\n        this.Eligibility_Criteria\n      )\n      this.$set(this.ProcessFlow[index], 'sort', index)\n      console.log(this.ProcessFlow)\n    },\n\n    async editHandleData(data) {\n      if (this.title === '编辑') {\n        console.log('data', data)\n        this.form.Id = data.Id\n        this.getEntityCheckType(data)\n        await this.chooseType(data.Pro_Category_Id)\n      }\n    },\n\n    // 质检节点下拉菜单\n    async getNodeList(data) {\n      await GetNodeList({ check_object_id: this.Check_Object_Id, Bom_Level: this.form.Bom_Level }).then(\n        (res) => {\n          if (res.IsSucceed) {\n            this.CheckNodeList = res.Data\n            this.editHandleData(data)\n            console.log(res.Data)\n          } else {\n            this.$message({\n              type: 'error',\n              message: res.Message\n            })\n          }\n        }\n      )\n    },\n    // 对象类型下拉\n    async getObjectTypeList(code) {\n      if (this.checkType.Display_Name === '物料') {\n        GetMaterialType({}).then((res) => {\n          this.ObjectTypeList = res.Data\n        })\n      } else {\n        let res\n        if (this.partGrade === '-1') {\n          res = await GetCompTypeTree({ professional: code })\n        } else {\n          res = await GetPartTypeTree({ professionalId: this.typeId, partGrade: this.partGrade })\n        }\n        if (res.IsSucceed) {\n          this.ObjectTypeList.data = res.Data\n          this.$nextTick((_) => {\n            this.$refs.treeSelectObjectType.treeDataUpdateFun(res.Data)\n          })\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n          this.ObjectTypeList.data = []\n          this.$nextTick((_) => {\n            this.$refs.treeSelectObjectType.treeDataUpdateFun([])\n          })\n        }\n      }\n    },\n\n    // 检查项设置部分\n    addTableData() {\n      this.ProcessFlow.push({\n        Check_Item_Id: '',\n        Eligibility_Criteria: '',\n        Questionlab_Id: ''\n      })\n      console.log('addTableData()', this.ProcessFlow)\n    },\n    deleteRow(index, rows) {\n      console.log(index)\n      rows.splice(index, 1)\n      console.log(this.ProcessFlow)\n      if (this.ProcessFlow.length > 0 && index !== this.ProcessFlow.length) {\n        this.$set(this.ProcessFlow[index], 'sort', index)\n      }\n    },\n    moveUpward(row, index) {\n      console.log(index)\n      const upData = this.ProcessFlow[index - 1]\n      this.ProcessFlow.splice(index - 1, 1)\n      this.ProcessFlow.splice(index, 0, upData)\n      this.$set(this.ProcessFlow[index - 1], 'sort', index - 1)\n      this.$set(this.ProcessFlow[index], 'sort', index)\n      console.log(this.ProcessFlow)\n    },\n    moveDown(row, index) {\n      console.log(index)\n      const downData = this.ProcessFlow[index + 1]\n      this.ProcessFlow.splice(index + 1, 1)\n      this.ProcessFlow.splice(index, 0, downData)\n      console.log(this.ProcessFlow)\n      this.$set(this.ProcessFlow[index], 'sort', index)\n      this.$set(this.ProcessFlow[index + 1], 'sort', index + 1)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n::v-deep {\n  .checkItem {\n    width: 100%;\n    .el-form-item__content {\n      margin-left: 0 !important;\n    }\n  }\n  .addcheckItem {\n    font-size: 16px;\n    margin-bottom: 10px;\n  }\n}\n::v-deep .el-form-item {\n  display: inline-block;\n  .el-form-item__content {\n    & > .el-input {\n      width: 220px !important;\n    }\n    & > .el-select {\n      width: 220px !important;\n    }\n    .el-tree-select-input {\n      width: 220px !important;\n    }\n  }\n}\n</style>\n"]}]}
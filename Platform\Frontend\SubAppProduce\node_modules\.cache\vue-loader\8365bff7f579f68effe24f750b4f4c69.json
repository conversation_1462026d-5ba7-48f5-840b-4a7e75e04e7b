{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\CombinationDialog.vue?vue&type=style&index=0&id=a9bdef16&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\CombinationDialog.vue", "mtime": 1757468112748}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KOjp2LWRlZXAgew0KICAuY2hlY2tJdGVtIHsNCiAgICB3aWR0aDogMTAwJTsNCiAgICAuZWwtZm9ybS1pdGVtX19jb250ZW50IHsNCiAgICAgIG1hcmdpbi1sZWZ0OiAwICFpbXBvcnRhbnQ7DQogICAgfQ0KICB9DQogIC5hZGRjaGVja0l0ZW0gew0KICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICB9DQp9DQo6OnYtZGVlcCAuZWwtZm9ybS1pdGVtIHsNCiAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICAuZWwtZm9ybS1pdGVtX19jb250ZW50IHsNCiAgICAmID4gLmVsLWlucHV0IHsNCiAgICAgIHdpZHRoOiAyMjBweCAhaW1wb3J0YW50Ow0KICAgIH0NCiAgICAmID4gLmVsLXNlbGVjdCB7DQogICAgICB3aWR0aDogMjIwcHggIWltcG9ydGFudDsNCiAgICB9DQogICAgLmVsLXRyZWUtc2VsZWN0LWlucHV0IHsNCiAgICAgIHdpZHRoOiAyMjBweCAhaW1wb3J0YW50Ow0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["CombinationDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAysBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CombinationDialog.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup/components/Dialog", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"130px\">\r\n      <el-row>\r\n        <el-form-item label=\"检查项组合名称\" prop=\"Group_Name\">\r\n          <el-input v-model=\"form.Group_Name\" />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"质检节点\" prop=\"Check_Node_Id\">\r\n          <el-select\r\n            v-model=\"form.Check_Node_Id\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n            placeholder=\"请选择质检节点\"\r\n            @change=\"changeNode\"\r\n          >\r\n            <el-option\r\n              v-for=\"(item, index) in CheckNodeList\"\r\n              :key=\"index\"\r\n              :label=\"item.Display_Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"质检类型\" prop=\"Check_Type\">\r\n          <el-select\r\n            v-model=\"Change_Check_Type\"\r\n            clearable\r\n            multiple\r\n            :multiple-limit=\"1\"\r\n            style=\"width: 100%\"\r\n            :disabled=\"Isdisable\"\r\n            placeholder=\"请选择质检类型\"\r\n            @change=\"SelectType\"\r\n          >\r\n            <el-option\r\n              v-for=\"(item, index) in QualityTypeList\"\r\n              :key=\"index\"\r\n              :label=\"item.Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"专业类别\" prop=\"Pro_Category_Id\">\r\n          <el-select\r\n            v-model=\"form.Pro_Category_Id\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n            placeholder=\"请选择专业类别\"\r\n            @change=\"changeCategory\"\r\n          >\r\n            <el-option\r\n              v-for=\"(item, index) in ProCategoryList\"\r\n              :key=\"index\"\r\n              :label=\"item.Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"检查类型\" prop=\"Questionlab_Ids\">\r\n          <el-select\r\n            v-model=\"form.Questionlab_Ids\"\r\n            style=\"width: 100%\"\r\n            multiple\r\n            placeholder=\"请选择检查类型\"\r\n            @change=\"ChangeCheckType\"\r\n            @remove-tag=\"removeCheckType\"\r\n          >\r\n            <el-option\r\n              v-for=\"(item, index) in CheckTypeList\"\r\n              :key=\"index\"\r\n              :label=\"item.Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item\r\n          label=\"产品类型\"\r\n          prop=\"Object_Type_Ids\"\r\n        >\r\n          <el-tree-select\r\n            ref=\"treeSelectObjectType\"\r\n            v-model=\"form.Object_Type_Ids\"\r\n            :disabled=\"!Boolean(form.Pro_Category_Id)\"\r\n            class=\"cs-tree-x\"\r\n            :tree-params=\"ObjectTypeList\"\r\n            value-key=\"Id\"\r\n            @removeTag=\"removeTagFn\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-col :span=\"24\">\r\n          <h3>检查项设置</h3>\r\n          <el-form-item label=\"\" prop=\"\" class=\"checkItem\">\r\n            <el-table :data=\"ProcessFlow\" border style=\"width: 100%\">\r\n              <el-table-column prop=\"\" label=\"*检查类型\" align=\"center\">\r\n                <template slot-scope=\"{ row }\">\r\n                  <el-select\r\n                    v-model=\"row.Questionlab_Id\"\r\n                    style=\"width: 100%\"\r\n                    clearable\r\n                    placeholder=\"请选择\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"(item, index) in CheckTypeList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.Name\"\r\n                      :value=\"item.Id\"\r\n                    />\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"\" label=\"*检查项内容\" align=\"center\">\r\n                <template slot-scope=\"{ row, $index }\">\r\n                  <el-select\r\n                    v-model=\"row.Check_Item_Id\"\r\n                    style=\"width: 100%\"\r\n                    clearable\r\n                    placeholder=\"请选择\"\r\n                    @change=\"ChangeItem($event, $index, row)\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"(item, index) in CheckItemList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.Check_Content\"\r\n                      :value=\"item.Id\"\r\n                    />\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"\" label=\"*合格标准\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-input\r\n                    v-model=\"scope.row.Eligibility_Criteria\"\r\n                    disabled\r\n                  />\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"address\"\r\n                label=\"操作\"\r\n                width=\"140\"\r\n                align=\"center\"\r\n              >\r\n                <template slot-scope=\"{ row, $index }\">\r\n                  <el-button\r\n                    type=\"text\"\r\n                    icon=\"el-icon-top\"\r\n                    :disabled=\"$index == 0\"\r\n                    @click=\"moveUpward(row, $index)\"\r\n                  />\r\n                  <el-button\r\n                    type=\"text\"\r\n                    icon=\"el-icon-bottom\"\r\n                    :disabled=\"$index == ProcessFlow.length - 1\"\r\n                    @click=\"moveDown(row, $index)\"\r\n                  />\r\n                  <el-button\r\n                    type=\"text\"\r\n                    icon=\"el-icon-delete\"\r\n                    style=\"color: #f56c6c\"\r\n                    @click.native.prevent=\"deleteRow($index, ProcessFlow)\"\r\n                  />\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"24\">\r\n          <el-button\r\n            type=\"text\"\r\n            class=\"addcheckItem\"\r\n            @click=\"addTableData\"\r\n          >+ 新增检查项</el-button>\r\n        </el-col>\r\n        <el-col :span=\"24\" style=\"text-align: right\">\r\n          <el-form-item style=\"text-align: right\">\r\n            <el-button @click=\"$emit('close')\">关 闭</el-button>\r\n            <el-button\r\n              type=\"primary\"\r\n              @click=\"handleSubmit('form')\"\r\n            >确 定</el-button>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { AddCheckItemCombination } from '@/api/PRO/factorycheck'\r\nimport { EntityQualityList } from '@/api/PRO/factorycheck'\r\n\r\nimport { GetCheckTypeList } from '@/api/PRO/factorycheck'\r\nimport { GetCheckItemList } from '@/api/PRO/factorycheck'\r\nimport { GetNodeList } from '@/api/PRO/factorycheck'\r\nimport { GetCompTypeTree } from '@/api/PRO/factorycheck'\r\nimport {\r\n  GetFactoryProfessionalByCode,\r\n  GetMaterialType\r\n} from '@/api/PRO/factorycheck'\r\nimport { GetPartTypeTree } from '@/api/PRO/partType'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      mode: '', // 区分项目和工厂\r\n      ProjectId: '', // 项目Id\r\n      Check_Object_Id: '',\r\n      checkType: {}, // 区分构件、零件、物料\r\n      form: {\r\n        Object_Type_Ids: []\r\n      },\r\n      rules: {\r\n        Check_Content: [\r\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\r\n        ],\r\n        Eligibility_Criteria: [\r\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\r\n        ],\r\n        Group_Name: [\r\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\r\n        ],\r\n        Check_Type: [\r\n          { required: true, message: '请填写完整表单', trigger: 'change' }\r\n        ],\r\n        Questionlab_Ids: [\r\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\r\n        ]\r\n      },\r\n      title: '',\r\n      options: [],\r\n      ProcessFlow: [],\r\n      CheckTypeList: [], // 检查类型下拉\r\n      CheckItemList: [], // 检查项下拉\r\n      Change_Check_Type: [],\r\n      QualityTypeList: [\r\n        {\r\n          Name: '质量',\r\n          Id: 1\r\n        },\r\n        {\r\n          Name: '探伤',\r\n          Id: 2\r\n        }\r\n      ], // 质检类型\r\n      ProCategoryList: [], // 专业类别下拉\r\n      CheckNodeList: [], // 质检节点下拉\r\n      verification: false,\r\n      ProCategoryCode: '', // 专业类别Code\r\n      Eligibility_Criteria: '',\r\n      ObjectTypeList: {\r\n        // 对象类型\r\n        'check-strictly': true,\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      Isdisable: false,\r\n      typeCode: '',\r\n      typeId: '',\r\n      partGrade: ''\r\n    }\r\n  },\r\n  watch: {\r\n    ProcessFlow: {\r\n      handler(newName, oldName) {\r\n        console.log(newName)\r\n        this.form.Questionlab_Ids = []\r\n        this.ProcessFlow.forEach((item) => {\r\n          if (\r\n            item.Questionlab_Id &&\r\n            !this.form.Questionlab_Ids.includes(item.Questionlab_Id)\r\n          ) {\r\n            this.form.Questionlab_Ids.push(item.Questionlab_Id)\r\n          }\r\n        })\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  mounted() {},\r\n  methods: {\r\n    async init(title, checkType, data) {\r\n      this.partGrade = checkType.Code\r\n      this.Check_Object_Id = checkType.Id\r\n      this.checkType = checkType\r\n      this.title = title\r\n      this.form.Check_Object_Id = checkType.Id\r\n      this.form.Bom_Level = checkType.Code\r\n      await this.getProfessionalType() // 专业类别\r\n      await this.getCheckTypeList() // 检查类型\r\n      await this.getCheckItemList()\r\n      await this.getNodeList(data) // 质检节点\r\n    },\r\n    async addCheckItemCombination() {\r\n      await AddCheckItemCombination({\r\n        Group: this.form,\r\n        Items: this.ProcessFlow\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            type: 'success',\r\n            message: '保存成功'\r\n          })\r\n          this.$emit('close')\r\n          this.dialogData = {}\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    removeTagFn(ids, tag) {\r\n      console.log('ids', ids)\r\n      console.log('tag', tag)\r\n    },\r\n    SelectType(item) {\r\n      console.log('item', item)\r\n\r\n      if (item.length === 1) {\r\n        this.form.Check_Type = item[0]\r\n      } else {\r\n        this.form.Check_Type = -1\r\n      }\r\n      console.log('this.form.Check_Type', this.form.Check_Type)\r\n    },\r\n    changeNode(val) {\r\n      console.log(val)\r\n      console.log(this.CheckNodeList)\r\n      if (val) {\r\n        this.form.Check_Type = this.CheckNodeList.find((v) => {\r\n          return v.Id === val\r\n        }).Check_Type\r\n        // 处理质检类型数据\r\n\r\n        this.Change_Check_Type = []\r\n        if (this.form.Check_Type === 1 || this.form.Check_Type === 2) {\r\n          this.Isdisable = true\r\n          this.Change_Check_Type.push(this.form.Check_Type)\r\n        } else if (this.form.Check_Type === -1) {\r\n          this.Isdisable = false // 质检类型可编辑\r\n          this.Change_Check_Type = []\r\n        } else {\r\n          this.Change_Check_Type = []\r\n          this.Isdisable = false\r\n        }\r\n        console.log(' this.Isdisable', this.Isdisable)\r\n      } else {\r\n        this.Change_Check_Type = []\r\n      }\r\n    },\r\n    getEntityCheckType(data) {\r\n      console.log(data)\r\n      EntityQualityList({\r\n        id: data.Id,\r\n        check_object_id: this.Check_Object_Id\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.form = res.Data[0].Group\r\n          console.log(this.form.Object_Type_Ids, 'Object_Type_Ids')\r\n\r\n          this.ProcessFlow = res.Data[0].Items\r\n          this.Change_Check_Type = []\r\n          // 处理质检类型数据\r\n          if (this.form.Check_Type === 1 || this.form.Check_Type === 2) {\r\n            this.Change_Check_Type.push(this.form.Check_Type)\r\n            if (res.Data[0].CheckNode_Type === -1) {\r\n              this.Isdisable = false\r\n            } else {\r\n              this.Isdisable = true\r\n            }\r\n          } else if (this.form.Check_Type === -1) {\r\n            this.Change_Check_Type = [1, 2]\r\n            this.Isdisable = true // 质检类型不可编辑\r\n          } else {\r\n            this.Change_Check_Type = []\r\n            this.Isdisable = false\r\n          }\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleSubmit(form) {\r\n      if (this.Change_Check_Type.length === 0) {\r\n        this.$message({\r\n          type: 'error',\r\n          message: '请选择检查类型'\r\n        })\r\n        return\r\n      }\r\n      let verification = true\r\n      if (this.ProcessFlow.length === 0) {\r\n        verification = false\r\n      } else {\r\n        this.ProcessFlow.forEach((val) => {\r\n          for (const key in val) {\r\n            if (val[key] === '') {\r\n              verification = false\r\n            }\r\n          }\r\n        })\r\n      }\r\n      if (!verification) {\r\n        this.$message({\r\n          type: 'error',\r\n          message: '请填写完整检查项设置内容'\r\n        })\r\n        return\r\n      }\r\n\r\n      const processFlowCopy = JSON.parse(JSON.stringify(this.ProcessFlow))\r\n      const processFlowNew = []\r\n      processFlowCopy.forEach((item) => {\r\n        const processFlowJson = {}\r\n        processFlowJson.Check_Item_Id = item.Check_Item_Id\r\n        processFlowJson.Eligibility_Criteria = item.Eligibility_Criteria\r\n        processFlowJson.Questionlab_Id = item.Questionlab_Id\r\n        processFlowNew.push(processFlowJson)\r\n      })\r\n      const processFlowTemp = processFlowNew.map((item) => {\r\n        return JSON.stringify(item)\r\n      })\r\n      if (new Set(processFlowTemp).size !== processFlowTemp.length) {\r\n        this.$message({\r\n          type: 'error',\r\n          message: '检查项设置内容不能完全相同'\r\n        })\r\n        return\r\n      }\r\n\r\n      const processFlowArr = this.ProcessFlow.map((v) => {\r\n        return v.Questionlab_Id\r\n      })\r\n\r\n      const isIncludes = this.form.Questionlab_Ids.every((item) =>\r\n        processFlowArr.includes(item)\r\n      )\r\n      if (!isIncludes) {\r\n        this.$message({\r\n          type: 'error',\r\n          message: '检查项设置必须包含已选检查类型'\r\n        })\r\n        return\r\n      }\r\n\r\n      this.$refs[form].validate((valid) => {\r\n        if (valid) {\r\n          this.addCheckItemCombination()\r\n        } else {\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    // 获取专业类别\r\n    async getProfessionalType() {\r\n      const Platform =\r\n        localStorage.getItem('Platform') || localStorage.getItem('CurPlatform')\r\n      if (Platform === '2') {\r\n        this.mode = 'factory'\r\n        await GetFactoryProfessionalByCode().then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.ProCategoryList = res.Data\r\n            const {\r\n              Code,\r\n              Id\r\n            } = res.Data?.find(item => item.Code === 'Steel') || {}\r\n            this.typeCode = Code\r\n            this.typeId = Id\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        })\r\n      }\r\n\r\n      // 获取项目/工厂id\r\n      this.ProjectId =\r\n        this.mode === 'factory'\r\n          ? localStorage.getItem('CurReferenceId')\r\n          : this.ProjectId\r\n    },\r\n\r\n    // 获取检查类型下拉框\r\n    async getCheckTypeList() {\r\n      await GetCheckTypeList({ check_object_id: this.Check_Object_Id, Bom_Level: this.form.Bom_Level }).then(\r\n        (res) => {\r\n          if (res.IsSucceed) {\r\n            this.CheckTypeList = res.Data\r\n            console.log(res.Data)\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        }\r\n      )\r\n    },\r\n    // 检查项内容\r\n    async getCheckItemList() {\r\n      await GetCheckItemList({ check_object_id: this.Check_Object_Id }).then(\r\n        (res) => {\r\n          if (res.IsSucceed) {\r\n            this.CheckItemList = res.Data\r\n            console.log(res.Data)\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        }\r\n      )\r\n    },\r\n    // 通过专业类别选择对象类型\r\n    changeCategory(val) {\r\n      this.form.Object_Type_Ids = []\r\n      this.chooseType(val)\r\n    },\r\n    // 通过专业类别选择对象类型\r\n    chooseType(val) {\r\n      console.log(this.ProCategoryList)\r\n      this.ProCategoryCode = this.ProCategoryList.find((v) => {\r\n        return v.Id === val\r\n      }).Code\r\n      this.getObjectTypeList(this.ProCategoryCode) // 对象类型\r\n    },\r\n    // 选中表格外检查类型\r\n    ChangeCheckType(val) {\r\n      const arrJson = Object.assign([], val)\r\n      // let index = arrJson.indexOf(Isexist);\r\n      // this.ProcessFlow.splice(index, 1);\r\n      console.log(arrJson)\r\n      if (this.ProcessFlow.length > arrJson.length) {\r\n        const arrJsonTemp = arrJson.map((item) => {\r\n          const itemField = {\r\n            Check_Item_Id: '',\r\n            Eligibility_Criteria: '',\r\n            Questionlab_Id: item\r\n          }\r\n          this.ProcessFlow.forEach((items) => {\r\n            if (items.Questionlab_Id === item) {\r\n              itemField.Check_Item_Id = items.Check_Item_Id\r\n              itemField.Eligibility_Criteria = items.Eligibility_Criteria\r\n            }\r\n          })\r\n\r\n          return itemField\r\n        })\r\n        this.ProcessFlow = [].concat(arrJsonTemp)\r\n      } else {\r\n        for (var i = 0; i < arrJson.length; i++) {\r\n          const Isexist = this.ProcessFlow.find((v) => {\r\n            return v.Questionlab_Id === arrJson[i]\r\n          })\r\n          if (!Isexist) {\r\n            this.ProcessFlow.push({\r\n              Questionlab_Id: arrJson[i],\r\n              Check_Item_Id: '',\r\n              Eligibility_Criteria: ''\r\n            })\r\n          }\r\n        }\r\n      }\r\n\r\n      console.log('ChangeCheckType()', this.ProcessFlow)\r\n    },\r\n\r\n    removeCheckType(val) {\r\n      const Isexist = this.ProcessFlow.find((v) => {\r\n        return v.Questionlab_Id === val\r\n      })\r\n      const index = this.ProcessFlow.indexOf(Isexist)\r\n      if (Isexist) {\r\n        this.ProcessFlow.splice(index, 1)\r\n      }\r\n    },\r\n    // 选中检查项内容\r\n    ChangeItem(data, index, row) {\r\n      // console.log(data);\r\n      // console.log(index);\r\n      // console.log(row)\r\n      // console.log(this.CheckItemList);\r\n      row.Eligibility_Criteria = ''\r\n      this.Eligibility_Criteria = ''\r\n      this.Eligibility_Criteria = this.CheckItemList.find((v) => {\r\n        return v.Id === data\r\n      })?.Eligibility_Criteria\r\n      this.$set(\r\n        this.ProcessFlow[index],\r\n        'Eligibility_Criteria',\r\n        this.Eligibility_Criteria\r\n      )\r\n      this.$set(this.ProcessFlow[index], 'sort', index)\r\n      console.log(this.ProcessFlow)\r\n    },\r\n\r\n    async editHandleData(data) {\r\n      if (this.title === '编辑') {\r\n        console.log('data', data)\r\n        this.form.Id = data.Id\r\n        this.getEntityCheckType(data)\r\n        await this.chooseType(data.Pro_Category_Id)\r\n      }\r\n    },\r\n\r\n    // 质检节点下拉菜单\r\n    async getNodeList(data) {\r\n      await GetNodeList({ check_object_id: this.Check_Object_Id, Bom_Level: this.form.Bom_Level }).then(\r\n        (res) => {\r\n          if (res.IsSucceed) {\r\n            this.CheckNodeList = res.Data\r\n            this.editHandleData(data)\r\n            console.log(res.Data)\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        }\r\n      )\r\n    },\r\n    // 对象类型下拉\r\n    async getObjectTypeList(code) {\r\n      if (this.checkType.Display_Name === '物料') {\r\n        GetMaterialType({}).then((res) => {\r\n          this.ObjectTypeList = res.Data\r\n        })\r\n      } else {\r\n        let res\r\n        if (this.partGrade === '-1') {\r\n          res = await GetCompTypeTree({ professional: code })\r\n        } else {\r\n          res = await GetPartTypeTree({ professionalId: this.typeId, partGrade: this.partGrade })\r\n        }\r\n        if (res.IsSucceed) {\r\n          this.ObjectTypeList.data = res.Data\r\n          this.$nextTick((_) => {\r\n            this.$refs.treeSelectObjectType.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.ObjectTypeList.data = []\r\n          this.$nextTick((_) => {\r\n            this.$refs.treeSelectObjectType.treeDataUpdateFun([])\r\n          })\r\n        }\r\n      }\r\n    },\r\n\r\n    // 检查项设置部分\r\n    addTableData() {\r\n      this.ProcessFlow.push({\r\n        Check_Item_Id: '',\r\n        Eligibility_Criteria: '',\r\n        Questionlab_Id: ''\r\n      })\r\n      console.log('addTableData()', this.ProcessFlow)\r\n    },\r\n    deleteRow(index, rows) {\r\n      console.log(index)\r\n      rows.splice(index, 1)\r\n      console.log(this.ProcessFlow)\r\n      if (this.ProcessFlow.length > 0 && index !== this.ProcessFlow.length) {\r\n        this.$set(this.ProcessFlow[index], 'sort', index)\r\n      }\r\n    },\r\n    moveUpward(row, index) {\r\n      console.log(index)\r\n      const upData = this.ProcessFlow[index - 1]\r\n      this.ProcessFlow.splice(index - 1, 1)\r\n      this.ProcessFlow.splice(index, 0, upData)\r\n      this.$set(this.ProcessFlow[index - 1], 'sort', index - 1)\r\n      this.$set(this.ProcessFlow[index], 'sort', index)\r\n      console.log(this.ProcessFlow)\r\n    },\r\n    moveDown(row, index) {\r\n      console.log(index)\r\n      const downData = this.ProcessFlow[index + 1]\r\n      this.ProcessFlow.splice(index + 1, 1)\r\n      this.ProcessFlow.splice(index, 0, downData)\r\n      console.log(this.ProcessFlow)\r\n      this.$set(this.ProcessFlow[index], 'sort', index)\r\n      this.$set(this.ProcessFlow[index + 1], 'sort', index + 1)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n::v-deep {\r\n  .checkItem {\r\n    width: 100%;\r\n    .el-form-item__content {\r\n      margin-left: 0 !important;\r\n    }\r\n  }\r\n  .addcheckItem {\r\n    font-size: 16px;\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n::v-deep .el-form-item {\r\n  display: inline-block;\r\n  .el-form-item__content {\r\n    & > .el-input {\r\n      width: 220px !important;\r\n    }\r\n    & > .el-select {\r\n      width: 220px !important;\r\n    }\r\n    .el-tree-select-input {\r\n      width: 220px !important;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\utils.js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\utils.js", "mtime": 1756109946527}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetBomLevelList", "GetBOMInfo", "_GetBOMInfo", "apply", "arguments", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "code", "res", "list", "comInfo", "partInfo", "comName", "partName", "currentBOMInfo", "currentParentBOMInfo", "index", "_args", "wrap", "_callee$", "_context", "prev", "next", "length", "undefined", "sent", "IsSucceed", "Data", "filter", "v", "Is_Enabled", "map", "item", "Sort", "parseInt", "sort", "a", "b", "find", "Code", "Display_Name", "findIndex", "toString", "abrupt", "stop"], "sources": ["D:/project/platform_framework_master/platform_framework/Platform/Frontend/SubAppProduce/src/views/PRO/bom-setting/utils.js"], "sourcesContent": ["import { GetBomLevelList } from '@/api/PRO/bom-level'\r\n\r\n// code根据层级配置来，URL中level同Code,只有部件存在level\r\n// 0:零件BOM ，-1：一级BOM,1:二级BOM,2:三级BOM,3:四级BOM\r\nexport async function GetBOMInfo(code = null) {\r\n  const res = await GetBomLevelList()\r\n  if (res.IsSucceed) {\r\n    const list = (res.Data || []).filter(v => v.Is_Enabled).map(item => {\r\n      item.Sort = parseInt(item.Sort)\r\n      return item\r\n    }).sort((a, b) => a.Sort - b.Sort)\r\n\r\n    const comInfo = list.find(v => v.Code === '-1')\r\n    const partInfo = list.find(v => v.Code === '0')\r\n\r\n    const comName = comInfo?.Display_Name || ''\r\n    const partName = partInfo?.Display_Name || ''\r\n\r\n    let currentBOMInfo = null\r\n    let currentParentBOMInfo = null\r\n\r\n    if (typeof code === 'number') {\r\n      const index = list.findIndex(v => v.Code === code.toString())\r\n      currentBOMInfo = list[index]\r\n      currentParentBOMInfo = list[index - 1]\r\n    }\r\n\r\n    return {\r\n      comName,\r\n      partName,\r\n      list,\r\n      currentBOMInfo,\r\n      currentParentBOMInfo\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;AAAA,SAASA,eAAe,QAAQ,qBAAqB;;AAErD;AACA;AACA,gBAAsBC,UAAUA,CAAA;EAAA,OAAAC,WAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AA+B/B,SAAAF,YAAA;EAAAA,WAAA,GAAAG,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,CA/BM,SAAAC,QAAA;IAAA,IAAAC,IAAA;MAAAC,GAAA;MAAAC,IAAA;MAAAC,OAAA;MAAAC,QAAA;MAAAC,OAAA;MAAAC,QAAA;MAAAC,cAAA;MAAAC,oBAAA;MAAAC,KAAA;MAAAC,KAAA,GAAAf,SAAA;IAAA,OAAAE,mBAAA,GAAAc,IAAA,UAAAC,SAAAC,QAAA;MAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;QAAA;UAA0Bf,IAAI,GAAAU,KAAA,CAAAM,MAAA,QAAAN,KAAA,QAAAO,SAAA,GAAAP,KAAA,MAAG,IAAI;UAAAG,QAAA,CAAAE,IAAA;UAAA,OACxBxB,eAAe,CAAC,CAAC;QAAA;UAA7BU,GAAG,GAAAY,QAAA,CAAAK,IAAA;UAAA,KACLjB,GAAG,CAACkB,SAAS;YAAAN,QAAA,CAAAE,IAAA;YAAA;UAAA;UACTb,IAAI,GAAG,CAACD,GAAG,CAACmB,IAAI,IAAI,EAAE,EAAEC,MAAM,CAAC,UAAAC,CAAC;YAAA,OAAIA,CAAC,CAACC,UAAU;UAAA,EAAC,CAACC,GAAG,CAAC,UAAAC,IAAI,EAAI;YAClEA,IAAI,CAACC,IAAI,GAAGC,QAAQ,CAACF,IAAI,CAACC,IAAI,CAAC;YAC/B,OAAOD,IAAI;UACb,CAAC,CAAC,CAACG,IAAI,CAAC,UAACC,CAAC,EAAEC,CAAC;YAAA,OAAKD,CAAC,CAACH,IAAI,GAAGI,CAAC,CAACJ,IAAI;UAAA,EAAC;UAE5BvB,OAAO,GAAGD,IAAI,CAAC6B,IAAI,CAAC,UAAAT,CAAC;YAAA,OAAIA,CAAC,CAACU,IAAI,KAAK,IAAI;UAAA,EAAC;UACzC5B,QAAQ,GAAGF,IAAI,CAAC6B,IAAI,CAAC,UAAAT,CAAC;YAAA,OAAIA,CAAC,CAACU,IAAI,KAAK,GAAG;UAAA,EAAC;UAEzC3B,OAAO,GAAG,CAAAF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE8B,YAAY,KAAI,EAAE;UACrC3B,QAAQ,GAAG,CAAAF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6B,YAAY,KAAI,EAAE;UAEzC1B,cAAc,GAAG,IAAI;UACrBC,oBAAoB,GAAG,IAAI;UAE/B,IAAI,OAAOR,IAAI,KAAK,QAAQ,EAAE;YACtBS,KAAK,GAAGP,IAAI,CAACgC,SAAS,CAAC,UAAAZ,CAAC;cAAA,OAAIA,CAAC,CAACU,IAAI,KAAKhC,IAAI,CAACmC,QAAQ,CAAC,CAAC;YAAA,EAAC;YAC7D5B,cAAc,GAAGL,IAAI,CAACO,KAAK,CAAC;YAC5BD,oBAAoB,GAAGN,IAAI,CAACO,KAAK,GAAG,CAAC,CAAC;UACxC;UAAC,OAAAI,QAAA,CAAAuB,MAAA,WAEM;YACL/B,OAAO,EAAPA,OAAO;YACPC,QAAQ,EAARA,QAAQ;YACRJ,IAAI,EAAJA,IAAI;YACJK,cAAc,EAAdA,cAAc;YACdC,oBAAoB,EAApBA;UACF,CAAC;QAAA;QAAA;UAAA,OAAAK,QAAA,CAAAwB,IAAA;MAAA;IAAA,GAAAtC,OAAA;EAAA,CAEJ;EAAA,OAAAN,WAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA", "ignoreList": []}]}
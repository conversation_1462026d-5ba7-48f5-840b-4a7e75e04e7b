{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\utils.js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\utils.js", "mtime": 1757909680919}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["store", "GetBOMInfo", "_GetBOMInfo", "apply", "arguments", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "code", "res", "list", "comInfo", "partInfo", "comName", "partName", "currentBOMInfo", "currentParentBOMInfo", "index", "_args", "wrap", "_callee$", "_context", "prev", "next", "length", "undefined", "dispatch", "sent", "IsSucceed", "Data", "find", "v", "Code", "Display_Name", "findIndex", "toString", "abrupt", "stop", "getBomCode", "maps", "getBomName", "_info$currentBOMInfo", "info", "checkIsUnitPart", "parseInt"], "sources": ["D:/project/platform_framework_master/platform_framework/Platform/Frontend/SubAppProduce/src/views/PRO/bom-setting/utils.js"], "sourcesContent": ["import store from '@/store'\r\n\r\n// code根据层级配置来，URL中level同Code,只有部件存在level\r\n// 0:零件BOM ，-1：一级BOM,1:二级BOM,2:三级BOM,3:四级BOM\r\nexport async function GetBOMInfo(code = null) {\r\n  const res = await store.dispatch('bomInfo/getBomLevelList')\r\n  if (res.IsSucceed) {\r\n    const list = res.Data\r\n\r\n    const comInfo = list.find(v => v.Code === '-1')\r\n    const partInfo = list.find(v => v.Code === '0')\r\n\r\n    const comName = comInfo?.Display_Name || ''\r\n    const partName = partInfo?.Display_Name || ''\r\n\r\n    let currentBOMInfo = null\r\n    let currentParentBOMInfo = null\r\n\r\n    if (typeof code === 'number') {\r\n      const index = list.findIndex(v => v.Code === code.toString())\r\n      currentBOMInfo = list[index]\r\n      currentParentBOMInfo = list[index - 1]\r\n    }\r\n\r\n    return {\r\n      comName,\r\n      partName,\r\n      list,\r\n      currentBOMInfo,\r\n      currentParentBOMInfo\r\n    }\r\n  }\r\n}\r\n\r\nexport function getBomCode(code) {\r\n  const maps = {\r\n    '-1': '-1',\r\n    '0': '0',\r\n    '1': '1',\r\n    '2': '2',\r\n    '3': '3'\r\n  }\r\n  return maps[code] || code\r\n}\r\n\r\nexport function getBomName(code) {\r\n  const info = GetBOMInfo(code)\r\n  return info.currentBOMInfo?.Display_Name || ''\r\n}\r\n\r\nexport function checkIsUnitPart(code) {\r\n  return parseInt(code) > 0\r\n}\r\n"], "mappings": ";;;;;;;;AAAA,OAAOA,KAAK,MAAM,SAAS;;AAE3B;AACA;AACA,gBAAsBC,UAAUA,CAAA;EAAA,OAAAC,WAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AA4B/B,SAAAF,YAAA;EAAAA,WAAA,GAAAG,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,CA5BM,SAAAC,QAAA;IAAA,IAAAC,IAAA;MAAAC,GAAA;MAAAC,IAAA;MAAAC,OAAA;MAAAC,QAAA;MAAAC,OAAA;MAAAC,QAAA;MAAAC,cAAA;MAAAC,oBAAA;MAAAC,KAAA;MAAAC,KAAA,GAAAf,SAAA;IAAA,OAAAE,mBAAA,GAAAc,IAAA,UAAAC,SAAAC,QAAA;MAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;QAAA;UAA0Bf,IAAI,GAAAU,KAAA,CAAAM,MAAA,QAAAN,KAAA,QAAAO,SAAA,GAAAP,KAAA,MAAG,IAAI;UAAAG,QAAA,CAAAE,IAAA;UAAA,OACxBxB,KAAK,CAAC2B,QAAQ,CAAC,yBAAyB,CAAC;QAAA;UAArDjB,GAAG,GAAAY,QAAA,CAAAM,IAAA;UAAA,KACLlB,GAAG,CAACmB,SAAS;YAAAP,QAAA,CAAAE,IAAA;YAAA;UAAA;UACTb,IAAI,GAAGD,GAAG,CAACoB,IAAI;UAEflB,OAAO,GAAGD,IAAI,CAACoB,IAAI,CAAC,UAAAC,CAAC;YAAA,OAAIA,CAAC,CAACC,IAAI,KAAK,IAAI;UAAA,EAAC;UACzCpB,QAAQ,GAAGF,IAAI,CAACoB,IAAI,CAAC,UAAAC,CAAC;YAAA,OAAIA,CAAC,CAACC,IAAI,KAAK,GAAG;UAAA,EAAC;UAEzCnB,OAAO,GAAG,CAAAF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsB,YAAY,KAAI,EAAE;UACrCnB,QAAQ,GAAG,CAAAF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqB,YAAY,KAAI,EAAE;UAEzClB,cAAc,GAAG,IAAI;UACrBC,oBAAoB,GAAG,IAAI;UAE/B,IAAI,OAAOR,IAAI,KAAK,QAAQ,EAAE;YACtBS,KAAK,GAAGP,IAAI,CAACwB,SAAS,CAAC,UAAAH,CAAC;cAAA,OAAIA,CAAC,CAACC,IAAI,KAAKxB,IAAI,CAAC2B,QAAQ,CAAC,CAAC;YAAA,EAAC;YAC7DpB,cAAc,GAAGL,IAAI,CAACO,KAAK,CAAC;YAC5BD,oBAAoB,GAAGN,IAAI,CAACO,KAAK,GAAG,CAAC,CAAC;UACxC;UAAC,OAAAI,QAAA,CAAAe,MAAA,WAEM;YACLvB,OAAO,EAAPA,OAAO;YACPC,QAAQ,EAARA,QAAQ;YACRJ,IAAI,EAAJA,IAAI;YACJK,cAAc,EAAdA,cAAc;YACdC,oBAAoB,EAApBA;UACF,CAAC;QAAA;QAAA;UAAA,OAAAK,QAAA,CAAAgB,IAAA;MAAA;IAAA,GAAA9B,OAAA;EAAA,CAEJ;EAAA,OAAAN,WAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,OAAO,SAASmC,UAAUA,CAAC9B,IAAI,EAAE;EAC/B,IAAM+B,IAAI,GAAG;IACX,IAAI,EAAE,IAAI;IACV,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE;EACP,CAAC;EACD,OAAOA,IAAI,CAAC/B,IAAI,CAAC,IAAIA,IAAI;AAC3B;AAEA,OAAO,SAASgC,UAAUA,CAAChC,IAAI,EAAE;EAAA,IAAAiC,oBAAA;EAC/B,IAAMC,IAAI,GAAG1C,UAAU,CAACQ,IAAI,CAAC;EAC7B,OAAO,EAAAiC,oBAAA,GAAAC,IAAI,CAAC3B,cAAc,cAAA0B,oBAAA,uBAAnBA,oBAAA,CAAqBR,YAAY,KAAI,EAAE;AAChD;AAEA,OAAO,SAASU,eAAeA,CAACnC,IAAI,EAAE;EACpC,OAAOoC,QAAQ,CAACpC,IAAI,CAAC,GAAG,CAAC;AAC3B", "ignoreList": []}]}
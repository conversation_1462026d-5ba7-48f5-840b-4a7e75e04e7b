<template>
  <el-form ref="formRef" :model="form" label-width="120px" :rules="rules">
    <el-form-item label="长度" prop="Length">
      <div class="cs-flex">
        <el-select v-model="form.Type" style="margin-right: 10px;width: 100px;" placeholder="请选择">
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-input-number v-model.number="form.Length" :min="0" class="cs-number-btn-hidden w100" clearble />
      </div>

    </el-form-item>
    <el-form-item label="公差要求" prop="Demand">
      <el-input v-model="form.Demand" placeholder="请输入公差要求" />
    </el-form-item>
    <el-form-item style="text-align: right">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="submitForm">确定</el-button>
    </el-form-item>

  </el-form>

</template>

<script>
import { SaveToleranceSetting } from '@/api/PRO/qualityInspect/quality-management'
export default {
  data() {
    return {
      loading: false,
      typeOptions: [
        { label: '<', value: 1 },
        { label: '<=', value: 2 },
        { label: '>', value: 3 },
        { label: '>=', value: 4 },
        { label: '=', value: 5 }
      ],
      form: {
        Length: '',
        Demand: '',
        Type: 1
      },
      rules: {
        Length: [{ required: true, message: '请选择长度' }],
        Demand: [{ required: true, message: '请输入公差要求' }]
      }
    }
  },
  methods: {
    init(type, typeId, data) {
      if (type === '编辑' && data) {
        this.form.Id = data.Id
        this.form.Length = data.Length
        this.form.Demand = data.Demand
        this.form.Type = data.Type
      }
    },
    handleClose() {
      this.$emit('close')
    },
    submitForm() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.loading = true

          SaveToleranceSetting(this.form).then(res => {
            if (res.IsSucceed) {
              this.$message.success('保存成功')
              this.$emit('ToleranceRefresh')
              this.handleClose()
            } else {
              this.$message.error(res.Message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.cs-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>

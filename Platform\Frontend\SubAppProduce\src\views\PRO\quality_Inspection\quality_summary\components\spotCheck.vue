<template>
  <div style="height: 100%">
    <!--    <div class="table_warrap">
      <div class="table_content">
        <el-main
          v-loading="loading"
          class="no-v-padding"
          style="padding: 0; height: 100%"
        >
          <DynamicDataTable
            ref="table"
            :config="tbConfig"
            :columns="columns"
            :data="tbData"
            :total="pageInfo.TotalCount"
            :page="pageInfo.Page"
            stripe
            height="100%"
            class="cs-plm-dy-table"
            border
            @gridPageChange="gridPageChange"
            @gridSizeChange="gridSizeChange"
            @multiSelectedChange="multiSelectedChange"
          >
            <template slot="Number" slot-scope="{ row }">
              <span>{{ row.Number || "-" }}</span>
            </template>
            <template slot="Rectify_Date" slot-scope="{ row }">
              <span>{{ row.Rectify_Date || "-" }}</span>
            </template>
            <template slot="Rectifier_name" slot-scope="{ row }">
              <span>{{ row.Rectifier_name || "-" }}</span>
            </template>
            <template slot="Partcipant_name" slot-scope="{ row }">
              <span>{{ row.Partcipant_name || "-" }}</span>
            </template>
            <template slot="Check_Result" slot-scope="{ row }">
              <span>{{ row.Check_Result || "-" }}</span>
            </template>
            <template slot="Pick_Date" slot-scope="{ row }">
              <span>{{ row.Pick_Date || "-" }}</span>
            </template>
            <template slot="Rectifier_Code" slot-scope="{ row }">
              <el-button
                v-if="Boolean(row.Rectifier_Code)"
                type="text"
                @click="handelSheet(row)"
              >{{ row.Rectifier_Code }}</el-button>
              <span v-else>-</span>
            </template>
            <template slot="op" slot-scope="{ row }">
              <el-button
                v-if="row.Status != '草稿'"
                type="text"
                @click="handelView(row)"
              >查看</el-button>
            </template>
          </DynamicDataTable>
        </el-main>
      </div>
    </div>-->
    <div class="cs-bottom-wapper">
      <div class="fff tb-x">
        <vxe-table
          :empty-render="{name: 'NotData'}"
          show-header-overflow
          :loading="loading"
          element-loading-spinner="el-icon-loading"
          element-loading-text="拼命加载中"
          empty-text="暂无数据"
          class="cs-vxe-table"
          height="100%"
          align="left"
          stripe
          :data="tbData"
          resizable
          :tooltip-config="{ enterable: true}"
          :checkbox-config="{checkField: 'checked', trigger: 'row'}"
          :row-config="{ isHover: true }"
          @checkbox-all="multiSelectedChange"
          @checkbox-change="multiSelectedChange"
        >
          <vxe-column fixed="left" type="checkbox" width="44" />
          <template v-for="item in columns">
            <vxe-column
              :key="item.Code"
              :min-width="item.Width"
              :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
              show-overflow="tooltip"
              sortable
              :align="item.Align"
              :field="item.Code"
              :title="item.Display_Name"
            >
              <template v-if="item.Code === 'Rectifier_Code' " #default="{ row }">
                <el-button
                  v-if="Boolean(row.Rectifier_Code)"
                  type="text"
                  @click="handelSheet(row)"
                >{{ row.Rectifier_Code }}</el-button>
                <span v-else>-</span>
              </template>
              <template v-if="item.Code === 'Check_Result' " #default="{ row }">
                <span v-if="!row.Check_Result">-</span>
                <template v-else>
                  <el-tag v-if="row.Check_Result==='合格'" type="success">{{ row.Check_Result }}</el-tag>
                  <el-tag v-else type="danger">{{ row.Check_Result }}</el-tag>
                </template>
              </template>
              <template v-else-if="item.Code === 'Status'" #default="{ row }">
                <span v-if="row.Status === '已完成'" class="by-dot by-dot-success">
                  {{ row.Status || "-" }}
                </span>
                <span v-else-if="row.Status === '待复核' || row.Status === '待整改'" class="by-dot by-dot-primary">
                  {{ row.Status || "-" }}
                </span>
                <span v-else-if="row.Status === '待质检' || row.Status === '草稿'" class="by-dot by-dot-info">
                  {{ row.Status || "-" }}
                </span>
                <span v-else>
                  {{ row.Status || "-" }}
                </span>
              </template>
              <template v-else #default="{ row }">
                <span>{{ row[item.Code] | displayValue }}</span>
              </template>
            </vxe-column>
          </template>
          <vxe-column fixed="right" title="操作" align="center" width="60">
            <template #default="{ row }">
              <el-button
                v-if="row.Status != '草稿'"
                type="text"
                @click="handelView(row)"
              >查看</el-button>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <div class="data-info">
        <el-tag
          size="medium"
          class="info-x"
        >已选 {{ selectList.length }} 条数据
        </el-tag>
        <Pagination
          :total="total"
          :page-sizes="tablePageSize"
          :page.sync="queryInfo.Page"
          :limit.sync="queryInfo.PageSize"
          @pagination="pageChange"
        />
      </div>
    </div>
    <el-dialog
      v-if="dialogVisible"
      ref="content"
      v-el-drag-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :width="width"
      class="plm-custom-dialog"
      @close="handleClose"
    >
      <component :is="currentComponent" ref="content" @close="handleClose" />
    </el-dialog>
  </div>
</template>

<script>
import InspectDoc from './add/InspectDoc.vue'
import rectificationSheet from './rectification/rectificationSheet'
import { GetGridByCode } from '@/api/sys'
import { GetPageQualitySummary } from '@/api/PRO/qualityInspect/start-Inspect'
import DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'
import elDragDialog from '@/directive/el-drag-dialog'
import { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'
import { timeFormat } from '@/filters'
import { ExportInspsectionSummaryInfo } from '@/api/PRO/factorycheck'
import { combineURL } from '@/utils'
import Pagination from '@/components/Pagination/index.vue'
import getTbInfo from '@/mixins/PRO/get-table-info'
import { tablePageSize } from '@/views/PRO/setting'
export default {
  directives: { elDragDialog },
  components: {
    Pagination,
    DynamicDataTable,
    rectificationSheet,
    InspectDoc
  },
  mixins: [getTbInfo],
  props: {
    searchDetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      width: '60%',
      code: '',
      TypeId: '',
      typeOption: '',
      dialogVisible: false,
      loading: false,
      dialogTitle: '',
      Ismodal: true,
      dialogData: {},
      currentComponent: '',
      tbConfig: {
      },
      Data: [],
      columns: [],
      tbData: [],
      queryInfo: {
        Page: 1,
        PageSize: tablePageSize[0]
      },
      tablePageSize: tablePageSize,
      total: 0,
      gridCode: 'Pro_Inpection_summary_list_spot',
      searchHeight: 0,
      CheckResultData: [],
      CheckNodeList: [], // 质检节点
      CheckObjectData: [], // 质检对象
      check_object_id: '',
      ProjectNameData: [],
      check_object_Name: '',
      selectList: []
    }
  },
  mounted() {
    this.getTypeList()
  },
  methods: {
    handleSearch() {},
    async getTypeList() {
      let res, data
      res = await GetFactoryProfessionalByCode({
        factoryId: localStorage.getItem('CurReferenceId')
      })
      data = res.Data
      if (res.IsSucceed) {
        this.typeOption = Object.freeze(data)
        console.log(this.typeOption)
        if (this.typeOption.length > 0) {
          this.TypeId = this.typeOption[0]?.Id
          this.code = this.typeOption.find((i) => i.Id === this.TypeId).Code
          this.getTableConfig(this.gridCode + ',' + this.code)
        }
        this.fetchData(1)
      } else {
        this.$message({
          message: res.Message,
          type: 'error'
        })
      }
    },
    fetchData(page) {
      page && (this.queryInfo.Page = page)
      this.loading = true
      const SeachParams = JSON.parse(JSON.stringify(this.searchDetail))
      const SteelName = SeachParams.SteelName.trim().replaceAll(' ', '\n')
      if (SeachParams.Pick_Date && SeachParams.Pick_Date.length === 2) {
        SeachParams.BeginDate = SeachParams.Pick_Date[0]
        SeachParams.EndDate = SeachParams.Pick_Date[1]
      } else {
        SeachParams.BeginDate = null
        SeachParams.EndDate = null
      }
      GetPageQualitySummary({
        pageInfo: this.queryInfo,
        ...SeachParams,
        SteelName,
        Check_Style: 0
      })
        .then((res) => {
          if (res.IsSucceed) {
            return this.setGridData(res.Data)
          }
        })
        .catch(console.error)
        .finally(() => {
          // 结束loading
          this.loading = false
        })
    },
    handelSheet(row) {
      console.log(row)
      this.generateComponent('整改单', 'rectificationSheet')
      this.$nextTick((_) => {
        this.$refs['content'].init(this.code, row)
      })
    },
    handelView(row) {
      this.generateComponent('查看质检单', 'inspectDoc')
      this.$nextTick((_) => {
        this.$refs['content'].init(this.code, row)
      })
    },

    setGridData(data) {
      this.tbData = this.tbData = data.Data.map((v) => {
        v.Id = v.SheetId // 解决全选框打勾问题
        v.Rectify_Date = v.Rectify_Date
          ? timeFormat(v.Rectify_Date, '{y}-{m}-{d}')
          : '-'
        v.Pick_Date = v.Pick_Date
          ? timeFormat(v.Pick_Date, '{y}-{m}-{d}')
          : '-'
        return v
      })

      this.total = data.TotalCount
    },

    multiSelectedChange(array) {
      this.selectList = array.records
      this.$emit('selectChange', this.selectList)
    },
    generateComponent(title, component) {
      this.dialogTitle = title
      this.currentComponent = component
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false
      this.fetchData(1)
    },
    exportTb() {
      const SheetIds = this.selectList.map((v) => v.SheetId)
      this.$emit('setExportLoading', true)
      const SeachParams = JSON.parse(JSON.stringify(this.searchDetail))
      const SteelName = SeachParams.SteelName.trim().replaceAll(' ', '\n')
      if (SeachParams.Pick_Date && SeachParams.Pick_Date.length === 2) {
        SeachParams.BeginDate = SeachParams.Pick_Date[0]
        SeachParams.EndDate = SeachParams.Pick_Date[1]
      } else {
        SeachParams.BeginDate = null
        SeachParams.EndDate = null
      }
      ExportInspsectionSummaryInfo({
        pageInfo: this.queryInfo,
        ...SeachParams,
        SteelName,
        Check_Style: 0,
        SheetIds
      }).then(res => {
        if (res.IsSucceed) {
          window.open(combineURL(this.$baseUrl, res.Data), '_blank')
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
        this.$emit('setExportLoading', false)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
@import "~@/styles/variables.scss";
.search_wrapper {
  padding: 16px 16px 0;
  box-sizing: border-box;
  ::v-deep .el-form-item {
    .el-form-item__content {
      & > .el-input {
        width: 220px;
      }
      & > .el-select {
        width: 220px;
      }
    }
  }
}

.cs-bottom-wapper {
  padding: 0 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  .tb-x {
    flex: 1;
    height: 0;
  }

  .pagination-container {
    text-align: right;
    padding: 16px;
    margin: 0;
  }

  .data-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.by-dot {
  display: flex;
  align-items: center;
  justify-content: center;
  &:before {
    content: "";
    display: inline-block;
    width: 5px;
    height: 5px;
    background: #f56c6c;
    border-radius: 50%;
    margin-right: 5px;
  }
}
.by-dot-success {
  color: #67c23a;
  &:before {
    background: #67c23a;
  }
}
.by-dot-primary {
  color: #409eff;
  &:before {
    background: #409eff;
  }
}
.by-dot-fail {
  color: #ff0000;
  &:before {
    background: #ff0000;
  }
}
.by-dot-info {
  &:before {
    background: #909399;
  }
}
</style>

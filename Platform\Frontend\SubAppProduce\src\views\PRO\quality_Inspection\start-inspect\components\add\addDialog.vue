<template>
  <div>
    <el-form ref="form" :rules="rules" :model="form" label-width="100px">
      <el-form-item label="质检对象" prop="Check_Object_Type">
        <el-select
          v-model="form.Check_Object_Type"
          filterable
          clearable
          placeholder="请选择"
          :disabled="type == '查看'"
          @change="changeObject"
        >
          <el-option
            v-for="item in CheckObjectData"
            :key="item.Id"
            :label="item.Display_Name"
            :value="item.Id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="质检节点" prop="Check_Node_Id">
        <el-select
          v-model="form.Check_Node_Id"
          filterable
          clearable
          placeholder="请选择"
          :disabled="!form.Check_Object_Type || type == '查看'"
          @change="changeCheckNode"
        >
          <el-option
            v-for="item in CheckNodeList"
            :key="item.Id"
            :label="item.Display_Name"
            :value="item.Id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="名称" prop="SteelName">
        <el-input v-model="form.SteelName" type="text" disabled>
          <el-button
            slot="append"
            icon="el-icon-search"
            :disabled="!form.Check_Object_Type || type == '查看'"
            @click="chooseComponent"
          />
        </el-input>
      </el-form-item>
      <el-form-item label="质检类型" prop="Check_Type">
        <el-select
          v-model="form.Check_Type"
          placeholder="请选择"
          :disabled="
            !form.Check_Node_Id || CheckTypeList.length == 1 || type == '查看'
          "
          @change="$forceUpdate()"
        >
          <el-option
            v-for="item in CheckTypeList"
            :key="item.Id"
            :label="item.Name"
            :value="item.Id"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="type != '查看'">
        <el-button @click="$emit('close')">取 消</el-button>
        <el-button
          type="primary"
          :loading="SaveLoading"
          @click="AddSave('form', false)"
        >保 存</el-button>
        <el-button
          type="primary"
          :loading="SubmitLoading"
          @click="AddSave('form', true)"
        >提 交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'
import elDragDialog from '@/directive/el-drag-dialog'
import {
  GetDictionaryDetailListByCode,
  GetNodeList
} from '@/api/PRO/factorycheck'
import { AddLanch, Add } from '@/api/PRO/qualityInspect/start-Inspect'
export default {
  directives: { elDragDialog },
  components: {
    DynamicDataTable
  },
  data() {
    return {
      SaveLoading: false,
      SubmitLoading: false,
      form: {
        Check_Object_Type: '',
        SteelName: '',
        Check_Node_Id: '',
        Check_Type: ''
      },
      chooseTitle: '', // 质检对象名称
      currentComponent: '',
      title: '',
      dialogVisible: false,
      dialogTitle: '',
      width: '60%',
      type: '', // 区分是否是新增（查看）
      addComTitle: '添加构件',
      CheckTypeList: [
        {
          Name: '质量',
          Id: '1'
        },
        {
          Name: '探伤',
          Id: '2'
        }
      ], // 质检类型
      CheckNodeList: [], // 质检节点
      CheckObjectData: [], // 质检对象
      rules: {
        Check_Object_Type: [
          { required: true, message: '请填写完整表单', trigger: 'change' }
        ],
        Check_Node_Id: [
          { required: true, message: '请填写完整表单', trigger: 'change' }
        ],
        Check_Type: [
          { required: true, message: '请填写完整表单', trigger: 'change' }
        ],
        SteelName: [
          { required: true, message: '请填写完整表单', trigger: 'blur' }
        ]
      }
    }
  },
  mounted() {
    this.getCheckType()
  },
  methods: {
    async init(type, row) {
      this.type = type || ''
      if (type == '查看') {
        console.log('row', row)

        // this.form.Check_Object_Type = row.Check_Object_Type
        await this.getCheckType()
        console.log('this.CheckObjectData', this.CheckObjectData)
        this.form.Check_Object_Type = this.CheckObjectData.find((v) => {
          return v.Display_Name === row.Check_Object_Type
        })?.Id
        console.log('this.form.Check_Object_Type', this.form.Check_Object_Type)
        this.changeObject(this.form.Check_Object_Type)
        this.form.Check_Node_Id = row.Check_Node_Id
        this.form.SteelName = row.SteelName
        this.form.Check_Type = row.Check_Type
      }
    },
    // 获取带过来的构件名称
    handelName(val) {
      console.log(val)
      this.form.SteelName = val.SteelName ? val.SteelName : val.Code
      this.form.Check_Object_Id = val.Id
    },
    async getCheckType() {
      await GetDictionaryDetailListByCode({ dictionaryCode: 'Quality_Code' })
        .then((res) => {
          if (res.IsSucceed) {
            this.CheckObjectData = res.Data
          } else {
            this.$message({
              type: 'error',
              message: 'res.Message'
            })
          }
        })
        .catch(() => {
          console.log('sdfd')
        })
    },
    changeObject(val) {
      console.log('val', val)
      this.form.Check_Node_Id = ''
      this.form.Check_Type = ''
      this.form.SteelName = ''
      const checkObj = this.CheckObjectData.find((v) => {
        return v.Id == val
      })?.Display_Name
      this.chooseTitle = checkObj
      switch (checkObj) {
        case '构件':
          this.check_object_id = '0'
          break
        case '零件':
          this.check_object_id = '1'
          break
        case '物料':
          this.check_object_id = '2'
          break
        case '部件':
          this.check_object_id = '3'
          break
        default:
          this.check_object_id = '0'
      }
      console.log('this.check_object_id', this.check_object_id)
      GetNodeList({ check_object_id: val, Check_Style: '1' }).then((res) => {
        if (res.IsSucceed) {
          this.CheckNodeList = res.Data
        } else {
          this.$message({
            type: 'error',
            message: 'res.Message'
          })
        }
      })
    },
    changeCheckNode(val) {
      this.CheckTypeList = []
      this.form.Check_Type = ''
      const checkTypeId = this.CheckNodeList.find((v) => {
        return v.Id === val
      })?.Check_Type
      console.log(checkTypeId)
      if (checkTypeId == '-1') {
        this.CheckTypeList = [
          {
            Name: '质量',
            Id: '1'
          },
          {
            Name: '探伤',
            Id: '2'
          }
        ]
        this.form.Check_Type = '1'
      } else if (checkTypeId == '1') {
        this.CheckTypeList = [
          {
            Name: '质量',
            Id: '1'
          }
        ]
        this.form.Check_Type = '1'
      } else if (checkTypeId == '2') {
        this.CheckTypeList = [
          {
            Name: '探伤',
            Id: '2'
          }
        ]
        this.form.Check_Type = '2'
      }
      console.log(this.form.Check_Type)
    },
    chooseComponent() {
      this.$store.dispatch('qualityCheck/changeRadio', true)
      this.$emit('openDialog', this.check_object_id, this.chooseTitle)
    },
    handleClose() {
      this.dialogVisible = false
    },
    AddSave(form, val) {
      if (val) {
        this.SubmitLoading = true
      } else {
        this.SaveLoading = true
      }
      this.$refs[form].validate((valid) => {
        if (valid) {
          Add({
            SheetModel: {
              ...this.form,
              Check_Object_Type: this.check_object_id,
              Check_Object_Type_Id: this.form.Check_Object_Type,
              Check_Style: 1
            },
            sumbimt: val
          }).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                type: 'success',
                message: '保存成功'
              })
              this.SubmitLoading = false
              this.SaveLoading = false
              this.$emit('close')
              this.$emit('refresh')
            } else {
              this.SubmitLoading = false
              this.SaveLoading = false
              this.$message({
                type: 'warning',
                message: res.Message
              })
            }
          })
        } else {
          this.SubmitLoading = false
          this.SaveLoading = false
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
@import "~@/styles/variables.scss";

.add-dialog {
  z-index: 9999 !important;

  ::v-deep {
    .el-dialog__header {
      background-color: #298dff;

      .el-dialog__title,
      .el-dialog__close {
        color: #ffffff;
      }
    }

    .el-dialog__body {
      max-height: 700px;
      overflow: auto;
      @include scrollBar;

      &::-webkit-scrollbar {
        width: 8px;
      }
    }
  }
}

::v-deep .el-form-item {
  .el-form-item__content {
    & > .el-input {
      width: 280px;
    }

    & > .el-select {
      width: 280px;
    }
  }
}
</style>

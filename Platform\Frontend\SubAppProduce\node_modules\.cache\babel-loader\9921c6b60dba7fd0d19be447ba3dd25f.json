{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\components\\ProjectData.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\components\\ProjectData.vue", "mtime": 1757468113454}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["ExpandableSection", "GetProjectListForPlanTrace", "components", "data", "Active_Sys_Project_Id", "showExpand", "treeLoading", "projectName", "treeData", "originalTreeData", "created", "fetchTreeData", "methods", "_this", "then", "res", "IsSucceed", "$message", "message", "Message", "type", "Data", "length", "resData", "_toConsumableArray", "handleNodeClick", "Sys_Project_Id", "$emit", "fetchTreeDataLocal", "trim", "searchTerm", "toLowerCase", "filter", "item", "Short_Name", "includes"], "sources": ["src/views/PRO/project-config/components/ProjectData.vue"], "sourcesContent": ["<template>\r\n  <ExpandableSection v-model=\"showExpand\" :width=\"300\" class=\"cs-left fff\">\r\n    <div class=\"inner-wrapper\">\r\n      <div class=\"tree-search\">\r\n        <el-input\r\n          v-model.trim=\"projectName\"\r\n          placeholder=\"关键词搜索\"\r\n          size=\"small\"\r\n          clearable\r\n          suffix-icon=\"el-icon-search\"\r\n          @blur=\"fetchTreeDataLocal\"\r\n          @clear=\"fetchTreeDataLocal\"\r\n          @keydown.enter.native=\"fetchTreeDataLocal\"\r\n        />\r\n      </div>\r\n      <el-divider class=\"cs-divider\" />\r\n      <div class=\"tree-x cs-scroll\">\r\n        <div v-for=\"item in treeData\" :key=\"item.Sys_Project_Id\" class=\"project-list\" :class=\"{ active: item.Sys_Project_Id === Active_Sys_Project_Id }\" @click=\"handleNodeClick(item)\">\r\n          <el-tooltip class=\"item\" effect=\"dark\" :content=\"item.Short_Name\" :open-delay=\"200\" placement=\"top\">\r\n            <div class=\"project-inner\">\r\n              <div>\r\n                <svg-icon\r\n                  icon-class=\"icon-folder\"\r\n                  class-name=\"class-icon\"\r\n                />\r\n              </div>\r\n              <span class=\"code\">({{ item.Code }})</span>\r\n              <span class=\"name\">{{ item.Short_Name }}</span>\r\n            </div>\r\n          </el-tooltip>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </ExpandableSection>\r\n</template>\r\n\r\n<script>\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport {\r\n  GetProjectListForPlanTrace\r\n} from '@/api/PRO/project'\r\nexport default {\r\n  components: {\r\n    ExpandableSection\r\n  },\r\n  data() {\r\n    return {\r\n      Active_Sys_Project_Id: '',\r\n      showExpand: true,\r\n      treeLoading: true,\r\n      projectName: '',\r\n      treeData: [],\r\n      originalTreeData: [] // 保存原始数据\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchTreeData()\r\n  },\r\n  methods: {\r\n    // 项目数据集\r\n    fetchTreeData() {\r\n      GetProjectListForPlanTrace({ }).then((res) => {\r\n        if (!res.IsSucceed) {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.treeLoading = false\r\n          this.treeData = []\r\n          return\r\n        }\r\n        if (res.Data.length === 0) {\r\n          this.treeLoading = false\r\n          return\r\n        }\r\n        const resData = res.Data\r\n        this.originalTreeData = [...resData] // 保存原始数据\r\n        this.treeData = [...resData]\r\n        this.treeLoading = false\r\n        if (this.treeData.length > 0) {\r\n          this.handleNodeClick(resData[0])\r\n        }\r\n      })\r\n    },\r\n    // 选中左侧项目节点\r\n    handleNodeClick(data) {\r\n      this.Active_Sys_Project_Id = data.Sys_Project_Id\r\n      this.$emit('setProjectData', data)\r\n    },\r\n    fetchTreeDataLocal() {\r\n      // 如果搜索关键词为空，恢复原始数据\r\n      if (!this.projectName || this.projectName.trim() === '') {\r\n        this.treeData = [...this.originalTreeData]\r\n        // 恢复原始数据后，如果有数据则选中第一个\r\n        if (this.treeData.length > 0) {\r\n          this.handleNodeClick(this.treeData[0])\r\n        }\r\n        return\r\n      }\r\n\r\n      // 从原始数据中过滤，支持大小写不敏感搜索\r\n      const searchTerm = this.projectName.trim().toLowerCase()\r\n      this.treeData = this.originalTreeData.filter((item) => {\r\n        return item.Short_Name &&\r\n               item.Short_Name.toLowerCase().includes(searchTerm)\r\n      })\r\n\r\n      // 搜索后如果有数据，自动选中第一个项目\r\n      // if (this.treeData.length > 0) {\r\n      //   this.handleNodeClick(this.treeData[0])\r\n      // }\r\n    }\r\n  }\r\n\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n  @import \"~@/styles/mixin.scss\";\r\n  @import \"~@/styles/tabs.scss\";\r\n  .cs-left {\r\n    position: relative;\r\n    margin-right: 20px;\r\n\r\n  }\r\n  .cs-left-contract {\r\n    padding-left: 0;\r\n    position: relative;\r\n    width: 20px;\r\n    margin-right: 26px;\r\n  }\r\n  .inner-wrapper {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    padding: 16px 10px 16px 16px;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n\r\n    .tree-search {\r\n      display: flex;\r\n\r\n      .search-select {\r\n        margin-right: 8px;\r\n      }\r\n    }\r\n\r\n    .tree-x {\r\n      overflow: auto;\r\n      margin-top: 16px;\r\n      flex: 1;\r\n      .project-list {\r\n        height: 32px;\r\n        padding-left: 16px;\r\n        font-size: 14px;\r\n        .project-inner {\r\n          height: 100%;\r\n          cursor: pointer;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: start;\r\n          white-space: nowrap; /* 禁止换行 */\r\n          overflow: hidden; /* 隐藏溢出内容 */\r\n          text-overflow: ellipsis; /* 溢出显示省略号 */\r\n          .code {\r\n            color: #5ac8fa;\r\n            margin-left: 5px;\r\n            margin-right: 5px;\r\n            flex-shrink: 0; /* 禁止压缩 */\r\n          }\r\n          .name {\r\n            color: rgba(34, 40, 52, .65);\r\n            flex-shrink: 1; /* 允许适当压缩 */\r\n            min-width: 0; /* 允许文本截断 */\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n          }\r\n        }\r\n      }\r\n      .project-list:hover {\r\n        background-color: rgba(41, 141, 255, .04);\r\n      }\r\n      .project-list.active {\r\n        background-color: #eef6ff;\r\n      }\r\n    }\r\n    .cs-scroll {\r\n      overflow-y: auto;\r\n      @include scrollBar;\r\n    }\r\n  }\r\n  .cs-divider {\r\n    margin: 16px 0 0 0;\r\n  }\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA,OAAAA,iBAAA;AACA,SACAC,0BAAA,QACA;AACA;EACAC,UAAA;IACAF,iBAAA,EAAAA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,qBAAA;MACAC,UAAA;MACAC,WAAA;MACAC,WAAA;MACAC,QAAA;MACAC,gBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACA;IACAD,aAAA,WAAAA,cAAA;MAAA,IAAAE,KAAA;MACAZ,0BAAA,KAAAa,IAAA,WAAAC,GAAA;QACA,KAAAA,GAAA,CAAAC,SAAA;UACAH,KAAA,CAAAI,QAAA;YACAC,OAAA,EAAAH,GAAA,CAAAI,OAAA;YACAC,IAAA;UACA;UACAP,KAAA,CAAAP,WAAA;UACAO,KAAA,CAAAL,QAAA;UACA;QACA;QACA,IAAAO,GAAA,CAAAM,IAAA,CAAAC,MAAA;UACAT,KAAA,CAAAP,WAAA;UACA;QACA;QACA,IAAAiB,OAAA,GAAAR,GAAA,CAAAM,IAAA;QACAR,KAAA,CAAAJ,gBAAA,GAAAe,kBAAA,CAAAD,OAAA;QACAV,KAAA,CAAAL,QAAA,GAAAgB,kBAAA,CAAAD,OAAA;QACAV,KAAA,CAAAP,WAAA;QACA,IAAAO,KAAA,CAAAL,QAAA,CAAAc,MAAA;UACAT,KAAA,CAAAY,eAAA,CAAAF,OAAA;QACA;MACA;IACA;IACA;IACAE,eAAA,WAAAA,gBAAAtB,IAAA;MACA,KAAAC,qBAAA,GAAAD,IAAA,CAAAuB,cAAA;MACA,KAAAC,KAAA,mBAAAxB,IAAA;IACA;IACAyB,kBAAA,WAAAA,mBAAA;MACA;MACA,UAAArB,WAAA,SAAAA,WAAA,CAAAsB,IAAA;QACA,KAAArB,QAAA,GAAAgB,kBAAA,MAAAf,gBAAA;QACA;QACA,SAAAD,QAAA,CAAAc,MAAA;UACA,KAAAG,eAAA,MAAAjB,QAAA;QACA;QACA;MACA;;MAEA;MACA,IAAAsB,UAAA,QAAAvB,WAAA,CAAAsB,IAAA,GAAAE,WAAA;MACA,KAAAvB,QAAA,QAAAC,gBAAA,CAAAuB,MAAA,WAAAC,IAAA;QACA,OAAAA,IAAA,CAAAC,UAAA,IACAD,IAAA,CAAAC,UAAA,CAAAH,WAAA,GAAAI,QAAA,CAAAL,UAAA;MACA;;MAEA;MACA;MACA;MACA;IACA;EACA;AAEA", "ignoreList": []}]}
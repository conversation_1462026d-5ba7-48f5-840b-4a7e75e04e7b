{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\home.vue?vue&type=style&index=0&id=1cf01392&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\home.vue", "mtime": 1757468127995}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmNvbnRhaW5lciB7DQogIHBhZGRpbmc6IDE2cHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQoNCiAgLmNzLXRhYnN7DQogICAgcGFkZGluZzogMCAxNnB4IDA7DQogICAgYmFja2dyb3VuZDogI2ZmZmZmZjsNCiAgfQ0KICAuc2VhcmNoLXdyYXBwZXIgew0KICAgIG1hcmdpbi10b3A6IDE2cHg7DQogICAgcGFkZGluZzogMTZweCAxNnB4IDA7DQogICAgYmFja2dyb3VuZDogI2ZmZmZmZjsNCiAgICBib3JkZXItcmFkaXVzOiA0cHggNHB4IDRweCA0cHg7DQogIH0NCn0NCg0KLm1haW4td3JhcHBlciB7DQogIGJhY2tncm91bmQ6ICNmZmZmZmY7DQogIC8vbWFyZ2luLXRvcDogMTZweDsNCiAgZmxleDogMTsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgYm9yZGVyLXJhZGl1czogNHB4IDRweCA0cHggNHB4Ow0KICBwYWRkaW5nOiAwcHggMTZweCAwOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KDQogIC5idG4td3JhcHBlciB7DQogICAgcGFkZGluZy1ib3R0b206IDE2cHg7DQogIH0NCiAgLnRiLXdyYXBwZXJ7DQogICAgZmxleDogMTsNCiAgICBoZWlnaHQ6IDA7DQogIH0NCiAgLnBhZ2luYXRpb24tY29udGFpbmVyIHsNCiAgICB0ZXh0LWFsaWduOiByaWdodDsNCiAgICBwYWRkaW5nOiAxNnB4Ow0KICAgIG1hcmdpbjogMDsNCiAgfQ0KfQ0KDQoucGxtLWN1c3RvbS1kaWFsb2cgew0KICA6OnYtZGVlcCB7DQogICAgLmVsLWRpYWxvZyAuZWwtZGlhbG9nX19ib2R5IHsNCiAgICAgIG1heC1oZWlnaHQ6IDcwdmg7DQogICAgICBvdmVyZmxvdzogYXV0bzsNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["home.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmyBA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "home.vue", "sourceRoot": "src/views/PRO/plan-production/schedule-production-new-part", "sourcesContent": ["<template>\r\n  <div class=\"container abs100\">\r\n    <div class=\"cs-tabs\">\r\n      <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\r\n        <el-tab-pane label=\"进行中\" :name=\"statusMap.ordered\" />\r\n        <el-tab-pane label=\"已完成\" :name=\"statusMap.finish\" />\r\n        <el-tab-pane label=\"未下达\" :name=\"statusMap.unOrdered\" />\r\n        <!--        <el-tab-pane label=\"已下达\" :name=\"statusMap.ordered\" />-->\r\n      </el-tabs>\r\n    </div>\r\n    <div class=\"search-wrapper\">\r\n      <el-form ref=\"form\" :model=\"queryForm\" inline label-width=\"100px\">\r\n        <el-form-item>\r\n          <div class=\"btn-wrapper\">\r\n            <el-button\r\n              v-if=\"getRoles(isCom?'ComAddSchedule':'PartAddScheduleNew')\"\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >新增排产单</el-button>\r\n            <el-button v-if=\"getRoles('ProImportPartScheduleNew')\" @click=\"handlePartImport\">导入{{ partName }}排产</el-button>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label-width=\"70px\" label=\"项目名称\" prop=\"projectId\">\r\n          <el-select\r\n            v-model=\"queryForm.projectId\"\r\n            filterable\r\n            clearable\r\n            placeholder=\"请选择\"\r\n            style=\"width:150px\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in projectOption\"\r\n              :key=\"item.Id\"\r\n              :label=\"item.Short_Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <!--        <el-form-item label=\"区域名称\" prop=\"areaId\">\r\n          <el-tree-select\r\n            ref=\"treeSelect\"\r\n            v-model=\"queryForm.areaId\"\r\n            :disabled=\"!queryForm.projectId\"\r\n            :select-params=\"{\r\n              clearable: true,\r\n            }\"\r\n            class=\"cs-tree-x\"\r\n            :tree-params=\"treeParams\"\r\n            @select-clear=\"areaClear\"\r\n            @node-click=\"areaChange\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"批次\" prop=\"install\">\r\n          <el-select\r\n            v-model=\"queryForm.install\"\r\n            :disabled=\"!queryForm.areaId\"\r\n            clearable\r\n            placeholder=\"请选择\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in installOption\"\r\n              :key=\"item.Id\"\r\n              :label=\"item.Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>-->\r\n        <el-form-item\r\n          v-if=\"workshopEnabled\"\r\n          label=\"所属车间\"\r\n          prop=\"Workshop_Id\"\r\n          label-width=\"70px\"\r\n        >\r\n          <el-select\r\n            v-model=\"queryForm.Workshop_Id\"\r\n            filterable\r\n            clearable\r\n            placeholder=\"请选择\"\r\n            style=\"width:150px\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in workShopOption\"\r\n              :key=\"item.Id\"\r\n              :label=\"item.Display_Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item v-show=\"activeName!==statusMap.unOrdered\" label-width=\"70px \" label=\"排产单号\" prop=\"Schduling_Code\">\r\n          <el-input v-model=\"queryForm.Schduling_Code\" style=\"width:150px\" clearable type=\"text\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"要求完成时间\" prop=\"finishTime\">\r\n          <el-date-picker\r\n            v-model=\"finishTime\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            style=\"width: 220px\"\r\n            type=\"daterange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button @click=\"handleReset\">重置</el-button>\r\n          <el-button type=\"primary\" @click=\"search(1)\">搜索</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n    <div class=\"main-wrapper\">\r\n      <div class=\"tb-wrapper\">\r\n        <vxe-table\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          :loading=\"pgLoading\"\r\n          element-loading-spinner=\"el-icon-loading\"\r\n          element-loading-text=\"拼命加载中\"\r\n          empty-text=\"暂无数据\"\r\n          class=\"cs-vxe-table\"\r\n          height=\"100%\"\r\n          align=\"left\"\r\n          stripe\r\n          :data=\"tbData\"\r\n          resizable\r\n          :tooltip-config=\"{ enterable: true}\"\r\n        >\r\n          <template v-for=\"item in columns\">\r\n            <vxe-column\r\n              :key=\"item.Code\"\r\n              :min-width=\"item.Width\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :align=\"item.Align\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n            >\r\n              <template v-if=\"item.Code === 'Schduling_Code'\" #default=\"{ row }\">\r\n                <el-link type=\"primary\" @click=\"handleView(row)\">{{ row.Schduling_Code }}</el-link>\r\n              </template>\r\n              <template v-else-if=\"['Finish_Date','Operator_Date','Order_Date'].includes(item.Code) \" #default=\"{ row }\">\r\n                {{ moment(row[item.Code]) | displayValue }}\r\n              </template>\r\n              <template v-else-if=\"item.Code === 'Status'\" #default=\"{ row }\">\r\n                {{ row.Status === 0 ? \"草稿\" : \"已下达\" }}\r\n              </template>\r\n              <template v-else-if=\"item.Code === 'Cancel_Count'\" #default=\"{ row }\">\r\n                <el-link\r\n                  v-if=\"row.Cancel_Count\"\r\n                  type=\"primary\"\r\n                  @click=\"handleCanCelDetail(row)\"\r\n                >{{ row.Cancel_Count }}</el-link>\r\n                <span v-else>{{ row.Cancel_Count | displayValue }}</span>\r\n              </template>\r\n            </vxe-column>\r\n\r\n          </template>\r\n          <vxe-column v-if=\"statusMap.finish!==activeName\" fixed=\"right\" title=\"操作\" :width=\"activeName === statusMap.ordered ? 170 : 220\" :min-width=\"activeName === statusMap.ordered ? 170 : 220\" show-overflow>\r\n            <template #default=\"{ row }\">\r\n              <el-button\r\n                v-if=\"canEditBtn(row)\"\r\n                type=\"text\"\r\n                @click=\"handleEdit(row)\"\r\n              >修改\r\n              </el-button>\r\n              <!--              <el-button\r\n                v-if=\"canImportBtn(row)\"\r\n                type=\"text\"\r\n                @click=\"handleRowImport(row)\"\r\n              >导入\r\n              </el-button>-->\r\n              <el-button\r\n                v-if=\"canOrderBtn(row)\"\r\n                type=\"text\"\r\n                @click=\"handleSave(row)\"\r\n              >下达\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"statusMap.unOrdered===activeName\"\r\n                type=\"text\"\r\n                @click=\"handleView(row)\"\r\n              >查看</el-button>\r\n              <el-button\r\n                v-if=\"canWithdrawBtn(row)\"\r\n                type=\"text\"\r\n                @click=\"handleWithdraw(row)\"\r\n              >撤销排产\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"canWithdrawDraftBtn(row)\"\r\n                type=\"text\"\r\n                @click=\"handleWithdrawAll(row)\"\r\n              >撤回草稿\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"canDeleteBtn(row)\"\r\n                type=\"text\"\r\n                style=\"color: red\"\r\n                @click=\"handleDelete(row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n      </div>\r\n      <div class=\"data-info\">\r\n        <Pagination\r\n          :total=\"total\"\r\n          max-height=\"100%\"\r\n          :page-sizes=\"tablePageSize\"\r\n          :page.sync=\"queryInfo.Page\"\r\n          :limit.sync=\"queryInfo.PageSize\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          @pagination=\"pageChange\"\r\n        />\r\n      </div>\r\n\r\n      <!--      <div\r\n              v-loading=\"pgLoading\"\r\n              style=\"height: 0; flex: 1\"\r\n              class=\"cs-z-tb-wrapper\"\r\n              element-loading-text=\"加载中\"\r\n              element-loading-spinner=\"el-icon-loading\"\r\n            >\r\n              <dynamic-data-table\r\n                ref=\"dyTable\"\r\n                :columns=\"columns\"\r\n                :data=\"tbData\"\r\n                :config=\"tbConfig\"\r\n                :page=\"queryInfo.Page\"\r\n                :total=\"total\"\r\n                border\r\n                class=\"cs-plm-dy-table\"\r\n                stripe\r\n                @gridPageChange=\"handlePageChange\"\r\n                @gridSizeChange=\"handlePageChange\"\r\n              >\r\n                <template slot=\"Schduling_Code\" slot-scope=\"{ row }\">\r\n                  <el-link type=\"primary\" @click=\"handleView(row)\">{{ row.Schduling_Code }}</el-link>\r\n                </template>\r\n                <template slot=\"Finish_Date\" slot-scope=\"{ row }\">\r\n                  {{ moment(row.Finish_Date) }}\r\n                </template>\r\n                <template slot=\"Operator_Date\" slot-scope=\"{ row }\">\r\n                  {{ moment(row.Operator_Date) }}\r\n                </template>\r\n                <template slot=\"Order_Date\" slot-scope=\"{ row }\">\r\n                  {{ moment(row.Order_Date) }}\r\n                </template>\r\n                <template slot=\"Status\" slot-scope=\"{ row }\">\r\n                  {{ row.Status === 0 ? \"草稿\" : \"已下达\" }}\r\n                </template>\r\n                <template slot=\"Cancel_Count\" slot-scope=\"{ row }\">\r\n                  <el-link\r\n                    v-if=\"row.Cancel_Count\"\r\n                    type=\"primary\"\r\n                    @click=\"handleCanCelDetail(row)\"\r\n                  >{{ row.Cancel_Count }}</el-link>\r\n                  <span v-else>{{ row.Cancel_Count }}</span>\r\n                </template>\r\n\r\n                <template v-if=\"activeName!==statusMap.finish\" slot=\"op\" slot-scope=\"{ row }\">\r\n                  <el-button\r\n                    v-if=\"canEditBtn(row)\"\r\n                    type=\"text\"\r\n                    @click=\"handleEdit(row)\"\r\n                  >修改\r\n                  </el-button>\r\n                  <el-button\r\n                    v-if=\"canImportBtn(row)\"\r\n                    type=\"text\"\r\n                    @click=\"handleRowImport(row)\"\r\n                  >导入\r\n                  </el-button>\r\n                  <el-button\r\n                    v-if=\"canOrderBtn(row)\"\r\n                    type=\"text\"\r\n                    @click=\"handleSave(row)\"\r\n                  >下达\r\n                  </el-button>\r\n                  <el-button v-if=\"statusMap.unOrdered===activeName\" type=\"text\" @click=\"handleView(row)\">查看</el-button>\r\n                  &lt;!&ndash; row.Cancel_Count 暂时不加撤回数量为0判断&ndash;&gt;\r\n                  <el-button\r\n                    v-if=\"canWithdrawBtn(row)\"\r\n                    type=\"text\"\r\n                    @click=\"handleWithdraw(row)\"\r\n                  >撤销排产\r\n                  </el-button>\r\n                  <el-button\r\n                    v-if=\"canWithdrawDraftBtn(row)\"\r\n                    type=\"text\"\r\n                    @click=\"handleWithdrawAll(row)\"\r\n                  >撤回草稿\r\n                  </el-button>\r\n                  <el-button\r\n                    v-if=\"canDeleteBtn(row)\"\r\n                    type=\"text\"\r\n                    style=\"color: red\"\r\n                    @click=\"handleDelete(row)\"\r\n                  >删除\r\n                  </el-button>\r\n                </template>\r\n\r\n              </dynamic-data-table>\r\n            </div>-->\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"dWidth\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :com-name=\"comName\"\r\n        :part-name=\"partName\"\r\n        @close=\"handleClose\"\r\n        @refresh=\"fetchData(1)\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { debounce } from '@/utils'\r\nimport AddSchedule from './components/AddSchedule'\r\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport { getDraftQuery } from './constant'\r\nimport {\r\n  DelSchdulingPlanById,\r\n  GetCompSchdulingPageList,\r\n  SaveSchdulingTaskById, WithdrawScheduling\r\n} from '@/api/PRO/production-task'\r\nimport { GetPartSchdulingPageList } from '@/api/PRO/production-part'\r\nimport ComImport from './components/ComImport'\r\nimport Withdraw from './components/Withdraw'\r\nimport PartImport from './components/partImport'\r\nimport getQueryInfo from './mixin/index'\r\nimport moment from 'moment'\r\nimport WithdrawHistory from './components/WithdrawHistory'\r\nimport { timeFormat } from '@/filters'\r\nimport { mapGetters } from 'vuex'\r\nimport { GetWorkshopPageList } from '@/api/PRO/basic-information/workshop'\r\nimport Pagination from '@/components/Pagination/index.vue'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport { RoleAuthorization } from '@/api/user'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\n\r\nexport default {\r\n  inject: ['pageType'],\r\n  components: {\r\n    Pagination,\r\n    WithdrawHistory,\r\n    AddSchedule,\r\n    DynamicDataTable,\r\n    Withdraw,\r\n    ComImport,\r\n    PartImport\r\n  },\r\n  mixins: [getTbInfo, getQueryInfo],\r\n  data() {\r\n    return {\r\n      bomList: [],\r\n      comName: '',\r\n      partName: '',\r\n      statusMap: {\r\n        finish: '9', // 已完成\r\n        unOrdered: '0', // 未下达\r\n        ordered: '1' // 进行中\r\n      },\r\n      scheduleType: {\r\n        comp: 1,\r\n        part: 2,\r\n        comp_part: 3\r\n      },\r\n      activeName: '1',\r\n      pgLoading: false,\r\n      dialogVisible: false,\r\n      currentComponent: '',\r\n      title: '',\r\n      dWidth: '40%',\r\n      queryForm: {\r\n        Finish_Date_Begin: '',\r\n        Finish_Date_End: '',\r\n        Status: 0,\r\n        Workshop_Id: '',\r\n        Schduling_Code: ''\r\n      },\r\n      tablePageSize: tablePageSize,\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: tablePageSize[0]\r\n      },\r\n      workShopOption: [],\r\n      columns: [],\r\n      tbData: [],\r\n      total: 0,\r\n      search: () => ({}),\r\n      roleList: []\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    },\r\n    finishTime: {\r\n      get() {\r\n        return [\r\n          timeFormat(this.queryForm.Finish_Date_Begin),\r\n          timeFormat(this.queryForm.Finish_Date_End)\r\n        ]\r\n      },\r\n      set(v) {\r\n        if (!v) {\r\n          this.queryForm.Finish_Date_Begin = ''\r\n          this.queryForm.Finish_Date_End = ''\r\n        } else {\r\n          const start = v[0]\r\n          const end = v[1]\r\n          this.queryForm.Finish_Date_Begin = timeFormat(start)\r\n          this.queryForm.Finish_Date_End = timeFormat(end)\r\n        }\r\n      }\r\n    },\r\n    ...mapGetters('factoryInfo', ['workshopEnabled'])\r\n  },\r\n  watch: {\r\n    activeName(newValue, oldValue) {\r\n      this.queryForm.Status = +newValue\r\n      this.pgLoading = true\r\n      this.getPageInfo()\r\n    }\r\n  },\r\n  activated() {\r\n    console.log('activated')\r\n    !this.isUpdate && this.fetchData(1)\r\n  },\r\n  async mounted() {\r\n    const { list, partName, comName } = await GetBOMInfo(-1)\r\n    this.bomList = list || []\r\n    this.partName = partName\r\n    this.comName = comName\r\n\r\n    this.isUpdate = true\r\n    this.getRoleAuthorization()\r\n    await this.getFactoryInfo()\r\n    this.workshopEnabled && this.getWorkshop()\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    await this.getPageInfo()\r\n  },\r\n  methods: {\r\n    async getPageInfo() {\r\n      let tab = ''\r\n      if (this.isCom) {\r\n        tab = this.activeName === '0'\r\n          ? 'PROScheduleUnOrder' : (this.activeName === '1' ? 'PROScheduleIsOrder' : 'PROScheduleFinish')\r\n      } else {\r\n        tab = this.activeName === '0'\r\n          ? 'PROScheduleIsUnorderPart' : (this.activeName === '1' ? 'PROScheduleIsOrderParting' : 'PROScheduleIsOrderPartFinish')\r\n      }\r\n      await this.getTableConfig(tab)\r\n      if (!this.workshopEnabled) {\r\n        this.columns = this.columns.filter(v => v.Code !== 'Workshop_Name')\r\n      }\r\n      this.fetchData()\r\n    },\r\n    handleClick() {\r\n\r\n    },\r\n    async getFactoryInfo() {\r\n      await this.$store.dispatch('factoryInfo/getWorkshop')\r\n    },\r\n    canEditBtn({ Status, Schduling_Model }) {\r\n      if (Schduling_Model === this.scheduleType.comp_part) {\r\n        return false\r\n      }\r\n      return Status === +this.statusMap.unOrdered\r\n    },\r\n    canImportBtn({ Status, Schduling_Model, Area_Id }) {\r\n      if (Schduling_Model === this.scheduleType.comp_part && !this.isCom) {\r\n        return false\r\n      }\r\n      if (Area_Id && typeof Area_Id === 'string' && Area_Id.split(',').length > 1) {\r\n        return false\r\n      }\r\n      return Status === +this.statusMap.unOrdered\r\n    },\r\n    canOrderBtn({ Status, Schduling_Model }) {\r\n      if (Schduling_Model === this.scheduleType.comp_part && !this.isCom) {\r\n        return false\r\n      }\r\n      return Status === +this.statusMap.unOrdered\r\n    },\r\n    canWithdrawBtn({ Generate_Source, Status, Schduling_Model }) {\r\n      // if (Generate_Source === 1) return false\r\n      if (Schduling_Model === this.scheduleType.comp_part && !this.isCom) {\r\n        return false\r\n      }\r\n      return Status === +this.statusMap.ordered\r\n    },\r\n    canWithdrawDraftBtn({ Generate_Source, Status, Schduling_Model, Receive_Count, Cancel_Count, Total_Change_Count }) {\r\n      if (Generate_Source === 1) return false\r\n      if (\r\n        (Schduling_Model === this.scheduleType.comp_part && !this.isCom) ||\r\n        Receive_Count > 0 || Cancel_Count > 0 || Total_Change_Count > 0) {\r\n        return false\r\n      }\r\n      return Status === +this.statusMap.ordered\r\n    },\r\n    canDeleteBtn({ Status, Schduling_Model }) {\r\n      if (Schduling_Model === this.scheduleType.comp_part && !this.isCom) {\r\n        return false\r\n      }\r\n      return Status === +this.statusMap.unOrdered\r\n    },\r\n    handleAdd() {\r\n      // this.dWidth = '40%'\r\n      // this.currentComponent = 'AddSchedule'\r\n      // this.title = '选择项目'\r\n      // this.dialogVisible = true\r\n      const info = getDraftQuery('PRO2PartScheduleDraftNew2', 'add', 'part', {}, this.$route)\r\n      this.$router.push({\r\n        ...info\r\n      })\r\n    },\r\n    handleRowImport(row) {\r\n      if (this.isCom) {\r\n        this.handleComImport(row)\r\n      } else {\r\n        this.dWidth = '40%'\r\n        this.currentComponent = 'PartImport'\r\n        this.title = '导入'\r\n        this.dialogVisible = true\r\n        this.$nextTick(_ => {\r\n          this.$refs['content'].setRow(row)\r\n        })\r\n      }\r\n    },\r\n    handleComImport(row, type) {\r\n      this.dWidth = '40%'\r\n      this.currentComponent = 'ComImport'\r\n      this.title = '导入'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        if (row) {\r\n          this.$refs['content'].setRow(row)\r\n        } else {\r\n          this.$refs['content'].setRow(null, type)\r\n        }\r\n      })\r\n    },\r\n    handlePartImport() {\r\n      this.dWidth = '40%'\r\n      this.currentComponent = 'PartImport'\r\n      this.title = '导入'\r\n      this.dialogVisible = true\r\n    },\r\n    fetchData(page) {\r\n      this.pgLoading = true\r\n      page && (this.queryInfo.Page = page)\r\n      let fun = null\r\n      const {\r\n        projectId,\r\n        areaId,\r\n        install,\r\n        Status,\r\n        Schduling_Code,\r\n        Workshop_Id,\r\n        Finish_Date_Begin,\r\n        Finish_Date_End\r\n      } = this.queryForm\r\n      const obj = {\r\n        ...this.queryInfo,\r\n        Project_Id: projectId,\r\n        Area_Id: areaId,\r\n        InstallUnit_Id: install,\r\n        Status: +this.activeName,\r\n        Schduling_Code,\r\n        Workshop_Id,\r\n        Finish_Date_Begin,\r\n        Finish_Date_End,\r\n        Is_New_Schduling: true\r\n      }\r\n      if (this.isCom) {\r\n        fun = GetCompSchdulingPageList\r\n      } else {\r\n        fun = GetPartSchdulingPageList\r\n      }\r\n      fun(obj).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.Data\r\n          this.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.tbData = []\r\n          this.total = 0\r\n        }\r\n      }).finally(_ => {\r\n        this.isUpdate = false\r\n        this.pgLoading = false\r\n      })\r\n    },\r\n    handleDelete(row) {\r\n      this.$confirm('是否删除该排产单?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        DelSchdulingPlanById({ schdulingPlanId: row.Schduling_Id }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '删除成功',\r\n              type: 'success'\r\n            })\r\n            this.fetchData(1)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    },\r\n    handleSave(row) {\r\n      this.$confirm('是否下达该任务?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.pgLoading = true\r\n        SaveSchdulingTaskById({\r\n          schdulingPlanId: row.Schduling_Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.fetchData(1)\r\n            this.$message({\r\n              message: '下达成功',\r\n              type: 'success'\r\n            })\r\n            this.pgLoading = false\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            this.pgLoading = false\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    handleCanCelDetail(row) {\r\n      this.dWidth = '80%'\r\n      this.currentComponent = 'WithdrawHistory'\r\n      this.title = '撤回历史'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n    handleEdit(row) {\r\n      const name = 'PRO2PartScheduleDraftNew2'\r\n      const info = getDraftQuery(name, 'edit', this.pageType, {\r\n        pid: row.Schduling_Id,\r\n        areaId: row.Area_Id,\r\n        install: row.InstallUnit_Id,\r\n        model: row.Schduling_Model\r\n      }, this.$route)\r\n      this.$router.push({\r\n        ...info\r\n      })\r\n    },\r\n    handleView(row) {\r\n      const name = 'PRO2PartScheduleDetailNew2'\r\n      const info = getDraftQuery(name, 'view', this.pageType, {\r\n        pid: row.Schduling_Id,\r\n        areaId: row.Area_Id,\r\n        install: row.InstallUnit_Id,\r\n        type: row.Generate_Source === 1 ? '1' : undefined\r\n      }, this.$route)\r\n      this.$router.push({\r\n        ...info\r\n      })\r\n    },\r\n    handleWithdraw(row, isWithdrawDraft) {\r\n      this.dWidth = '80%'\r\n      this.currentComponent = 'Withdraw'\r\n      this.title = '撤回'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n    handleWithdrawAll(row) {\r\n      this.$confirm('是否撤销排产单回草稿?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.pgLoading = true\r\n        WithdrawScheduling({\r\n          schedulingId: row.Schduling_Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '撤回草稿成功',\r\n              type: 'success'\r\n            })\r\n            this.fetchData(1)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            this.pgLoading = false\r\n          }\r\n        }).catch(e => {\r\n          this.pgLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    handleReset() {\r\n      this.$refs['form'].resetFields()\r\n      this.finishTime = ''\r\n      this.search(1)\r\n    },\r\n    moment(v, format = 'YYYY-MM-DD') {\r\n      if ((v ?? '') == '') {\r\n        return ''\r\n      } else {\r\n        return moment(v).format(format)\r\n      }\r\n    },\r\n    getWorkshop() {\r\n      GetWorkshopPageList({ Page: 1, PageSize: -1 }).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (!res?.Data?.Data) {\r\n            this.workShopOption = []\r\n          }\r\n          this.workShopOption = res.Data.Data.map(item => {\r\n            return {\r\n              Id: item.Id,\r\n              Display_Name: item.Display_Name\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getRoles(code) {\r\n      return this.roleList.includes(code)\r\n    },\r\n    async getRoleAuthorization() {\r\n      const res = await RoleAuthorization({\r\n        roleType: 3,\r\n        menuType: 1,\r\n        menuId: this.$route.meta.Id\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.roleList = res.Data.map((v) => v.Code)\r\n      } else {\r\n        this.$message({\r\n          type: 'warning',\r\n          message: res.Message\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.container {\r\n  padding: 16px;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .cs-tabs{\r\n    padding: 0 16px 0;\r\n    background: #ffffff;\r\n  }\r\n  .search-wrapper {\r\n    margin-top: 16px;\r\n    padding: 16px 16px 0;\r\n    background: #ffffff;\r\n    border-radius: 4px 4px 4px 4px;\r\n  }\r\n}\r\n\r\n.main-wrapper {\r\n  background: #ffffff;\r\n  //margin-top: 16px;\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  border-radius: 4px 4px 4px 4px;\r\n  padding: 0px 16px 0;\r\n  overflow: hidden;\r\n\r\n  .btn-wrapper {\r\n    padding-bottom: 16px;\r\n  }\r\n  .tb-wrapper{\r\n    flex: 1;\r\n    height: 0;\r\n  }\r\n  .pagination-container {\r\n    text-align: right;\r\n    padding: 16px;\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n.plm-custom-dialog {\r\n  ::v-deep {\r\n    .el-dialog .el-dialog__body {\r\n      max-height: 70vh;\r\n      overflow: auto;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\store\\modules\\bomInfo.js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\store\\modules\\bomInfo.js", "mtime": 1757468111977}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetBomLevelList", "state", "bomLevelList", "bomLevelLoading", "mutations", "SET_BOM_LEVEL_LIST", "list", "SET_BOM_LEVEL_LOADING", "loading", "actions", "getBomLevelList", "_ref", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "commit", "res", "wrap", "_callee$", "_context", "prev", "next", "length", "abrupt", "IsSucceed", "Data", "sent", "filter", "v", "Is_Enabled", "map", "item", "Sort", "parseInt", "sort", "a", "b", "t0", "console", "error", "Message", "finish", "stop", "clearBomLevelCache", "_ref2", "namespaced"], "sources": ["D:/project/platform_framework_master/platform_framework/Platform/Frontend/SubAppProduce/src/store/modules/bomInfo.js"], "sourcesContent": ["import { GetBomLevelList } from '@/api/PRO/bom-level'\r\n\r\nconst state = {\r\n  bomLevelList: [],\r\n  bomLevelLoading: false\r\n}\r\n\r\nconst mutations = {\r\n  SET_BOM_LEVEL_LIST(state, list) {\r\n    state.bomLevelList = list\r\n  },\r\n  SET_BOM_LEVEL_LOADING(state, loading) {\r\n    state.bomLevelLoading = loading\r\n  }\r\n}\r\n\r\nconst actions = {\r\n  async getBomLevelList({ commit, state }) {\r\n    if (state.bomLevelList.length > 0) {\r\n      return {\r\n        IsSucceed: true,\r\n        Data: state.bomLevelList\r\n      }\r\n    }\r\n\r\n    commit('SET_BOM_LEVEL_LOADING', true)\r\n    try {\r\n      const res = await GetBomLevelList()\r\n      if (res.IsSucceed) {\r\n        const list = (res.Data || []).filter(v => v.Is_Enabled).map(item => {\r\n          item.Sort = parseInt(item.Sort)\r\n          return item\r\n        }).sort((a, b) => a.Sort - b.Sort)\r\n\r\n        commit('SET_BOM_LEVEL_LIST', list)\r\n        return {\r\n          IsSucceed: true,\r\n          Data: list\r\n        }\r\n      }\r\n      return res\r\n    } catch (error) {\r\n      console.error('获取BOM层级列表失败:', error)\r\n      return {\r\n        IsSucceed: false,\r\n        Message: '获取BOM层级列表失败'\r\n      }\r\n    } finally {\r\n      commit('SET_BOM_LEVEL_LOADING', false)\r\n    }\r\n  },\r\n\r\n  clearBomLevelCache({ commit }) {\r\n    commit('SET_BOM_LEVEL_LIST', [])\r\n  }\r\n}\r\n\r\nexport default {\r\n  namespaced: true,\r\n  state,\r\n  mutations,\r\n  actions\r\n}\r\n"], "mappings": ";;;;;;;;;AAAA,SAASA,eAAe,QAAQ,qBAAqB;AAErD,IAAMC,KAAK,GAAG;EACZC,YAAY,EAAE,EAAE;EAChBC,eAAe,EAAE;AACnB,CAAC;AAED,IAAMC,SAAS,GAAG;EAChBC,kBAAkB,WAAlBA,kBAAkBA,CAACJ,KAAK,EAAEK,IAAI,EAAE;IAC9BL,KAAK,CAACC,YAAY,GAAGI,IAAI;EAC3B,CAAC;EACDC,qBAAqB,WAArBA,qBAAqBA,CAACN,KAAK,EAAEO,OAAO,EAAE;IACpCP,KAAK,CAACE,eAAe,GAAGK,OAAO;EACjC;AACF,CAAC;AAED,IAAMC,OAAO,GAAG;EACRC,eAAe,WAAfA,eAAeA,CAAAC,IAAA,EAAoB;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAAC,MAAA,EAAAf,KAAA,EAAAgB,GAAA,EAAAX,IAAA;MAAA,OAAAO,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAjBN,MAAM,GAAAL,IAAA,CAANK,MAAM,EAAEf,KAAK,GAAAU,IAAA,CAALV,KAAK;YAAA,MAC/BA,KAAK,CAACC,YAAY,CAACqB,MAAM,GAAG,CAAC;cAAAH,QAAA,CAAAE,IAAA;cAAA;YAAA;YAAA,OAAAF,QAAA,CAAAI,MAAA,WACxB;cACLC,SAAS,EAAE,IAAI;cACfC,IAAI,EAAEzB,KAAK,CAACC;YACd,CAAC;UAAA;YAGHc,MAAM,CAAC,uBAAuB,EAAE,IAAI,CAAC;YAAAI,QAAA,CAAAC,IAAA;YAAAD,QAAA,CAAAE,IAAA;YAAA,OAEjBtB,eAAe,CAAC,CAAC;UAAA;YAA7BiB,GAAG,GAAAG,QAAA,CAAAO,IAAA;YAAA,KACLV,GAAG,CAACQ,SAAS;cAAAL,QAAA,CAAAE,IAAA;cAAA;YAAA;YACThB,IAAI,GAAG,CAACW,GAAG,CAACS,IAAI,IAAI,EAAE,EAAEE,MAAM,CAAC,UAAAC,CAAC;cAAA,OAAIA,CAAC,CAACC,UAAU;YAAA,EAAC,CAACC,GAAG,CAAC,UAAAC,IAAI,EAAI;cAClEA,IAAI,CAACC,IAAI,GAAGC,QAAQ,CAACF,IAAI,CAACC,IAAI,CAAC;cAC/B,OAAOD,IAAI;YACb,CAAC,CAAC,CAACG,IAAI,CAAC,UAACC,CAAC,EAAEC,CAAC;cAAA,OAAKD,CAAC,CAACH,IAAI,GAAGI,CAAC,CAACJ,IAAI;YAAA,EAAC;YAElCjB,MAAM,CAAC,oBAAoB,EAAEV,IAAI,CAAC;YAAA,OAAAc,QAAA,CAAAI,MAAA,WAC3B;cACLC,SAAS,EAAE,IAAI;cACfC,IAAI,EAAEpB;YACR,CAAC;UAAA;YAAA,OAAAc,QAAA,CAAAI,MAAA,WAEIP,GAAG;UAAA;YAAAG,QAAA,CAAAC,IAAA;YAAAD,QAAA,CAAAkB,EAAA,GAAAlB,QAAA;YAEVmB,OAAO,CAACC,KAAK,CAAC,cAAc,EAAApB,QAAA,CAAAkB,EAAO,CAAC;YAAA,OAAAlB,QAAA,CAAAI,MAAA,WAC7B;cACLC,SAAS,EAAE,KAAK;cAChBgB,OAAO,EAAE;YACX,CAAC;UAAA;YAAArB,QAAA,CAAAC,IAAA;YAEDL,MAAM,CAAC,uBAAuB,EAAE,KAAK,CAAC;YAAA,OAAAI,QAAA,CAAAsB,MAAA;UAAA;UAAA;YAAA,OAAAtB,QAAA,CAAAuB,IAAA;QAAA;MAAA,GAAA5B,OAAA;IAAA;EAE1C,CAAC;EAED6B,kBAAkB,WAAlBA,kBAAkBA,CAAAC,KAAA,EAAa;IAAA,IAAV7B,MAAM,GAAA6B,KAAA,CAAN7B,MAAM;IACzBA,MAAM,CAAC,oBAAoB,EAAE,EAAE,CAAC;EAClC;AACF,CAAC;AAED,eAAe;EACb8B,UAAU,EAAE,IAAI;EAChB7C,KAAK,EAALA,KAAK;EACLG,SAAS,EAATA,SAAS;EACTK,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}
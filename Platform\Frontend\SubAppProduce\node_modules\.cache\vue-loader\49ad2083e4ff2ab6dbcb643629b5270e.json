{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue?vue&type=style&index=0&id=d2411270&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue", "mtime": 1757574017769}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgpAaW1wb3J0ICJ+QC9zdHlsZXMvbWl4aW4uc2NzcyI7Ci5mb3JtLXdyYXBwZXIgewogIGhlaWdodDogNzB2aDsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CgogIC5mb3JtLWNvbnRlbnQgewogICAgZmxleDogMTsKICAgIG92ZXJmbG93OiBhdXRvOwogICAgcGFkZGluZy1yaWdodDogMTZweDsKICAgIEBpbmNsdWRlIHNjcm9sbEJhcjsKCiAgICAuZm9ybS14IHsKICAgICAgcGFkZGluZy1ib3R0b206IDIwcHg7CiAgICB9CiAgfQoKICAuZm9ybS1mb290ZXIgewogICAgdGV4dC1hbGlnbjogcmlnaHQ7CiAgICBmbGV4LXNocmluazogMDsKICAgIHBhZGRpbmctdG9wOiAxNnB4OwogICAgYmFja2dyb3VuZDogI2ZmZjsKICB9Cn0K"}, {"version": 3, "sources": ["PartTakeConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6KA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PartTakeConfig.vue", "sourceRoot": "src/views/PRO/process-settings/management/component", "sourcesContent": ["<template>\n  <div class=\"form-wrapper\">\n    <div class=\"form-recognition-tabs\">\n      <el-tabs v-model=\"bomActiveName\" @tab-click=\"bomClick\">\n        <el-tab-pane v-for=\"(item, index) in tabBomList\" :key=\"index\" :label=\"item.Display_Name\" :name=\"item.Code\" />\n      </el-tabs>\n    </div>\n    <div class=\"form-content\">\n      <div style=\"display: flex; margin-bottom: 16px;\">\n        <div style=\"width: 120px;\" />\n        <div style=\"flex: 1; display: flex; justify-content: flex-start; font-size: 16px; color: #333333;\">\n          <div v-for=\"(item, index) in parentBomList\" :key=\"index\" :style=\"{ width: (100 / parentBomList.length) + '%', textAlign: 'center' }\">{{ item.Display_Name }}</div>\n        </div>\n      </div>\n      <div>\n        <div v-for=\"(item, index) in filteredList\" :key=\"index\" style=\"display: flex;  justify-content: center; align-items: center; margin-bottom: 16px;\">\n          <div style=\"width: 120px; font-size: 14px; color: #333333; text-align: right;\">{{ item.Part_Type_Name }}</div>\n          <div style=\"flex: 1; display: flex; justify-content: flex-start;\">\n            <div v-for=\"bom in parentBomList\" :key=\"bom.Code\" style=\"margin-left: 12px; text-align: center;\" :style=\"{ width: (100 / parentBomList.length) + '%' }\">\n              <el-select v-model=\"item.Working_Process_Id\" clearable style=\"width: 100%;\">\n                <el-option v-for=\"op in getFilteredSelectList(bom.Code)\" :key=\"op.Id\" :label=\"op.Name\" :value=\"op.Id\" />\n              </el-select>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div class=\"form-footer\">\n      <el-button @click=\"$emit('close')\">取 消</el-button>\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport { GetConsumingProcessAllList, SaveConsumingProcessAllList, GetConsumingAllList, SaveConsumingProcessAllList2 } from '@/api/PRO/partType'\nimport { GetProcessList } from '@/api/PRO/technology-lib'\n\nexport default {\n  data() {\n    return {\n      list: [],\n      btnLoading: false,\n      selectList: [],\n      bomList: [],\n      tabBomList: [],\n      comName: '',\n      partName: '',\n      bomActiveName: '',\n      parentBomList: []\n    }\n  },\n  computed: {\n    // 根据当前选中的BOM层级过滤数据\n    filteredList() {\n      if (!this.list || !this.bomActiveName) {\n        return []\n      }\n      return this.list.filter(item => item.Use_Bom_Level.toString() === this.bomActiveName)\n    }\n  },\n  async mounted() {\n    await this.getBom()\n    await this.getParentBom()\n    await this.getProcessList()\n  },\n  methods: {\n    async getBom() {\n      const { comName, partName, list } = await GetBOMInfo()\n      this.comName = comName\n      this.partName = partName\n      this.bomList = list\n      this.tabBomList = list.filter(i => i.Code !== '-1')\n      this.bomActiveName = this.tabBomList[this.tabBomList.length - 1].Code\n    },\n    getTypeList() {\n      GetConsumingAllList({}).then(res => {\n        if (res.IsSucceed) {\n          const resData = res.Data\n          const Process_List_All = []\n          this.parentBomList.map(item => {\n            const Process_List_Json = {}\n            Process_List_Json.Bom_Level = item.Code\n            Process_List_Json.Working_Process_Id = ''\n            Process_List_Json.Working_Process_Code = ''\n            Process_List_Json.Working_Process_Name = ''\n            Process_List_All.push(Process_List_Json)\n          })\n\n          // 遍历 resData 中的每个项目，为每个项目的 Process_List 添加缺失的数据\n          resData.forEach(dataItem => {\n            // 确保每个项目的 Process_List 存在\n            if (!dataItem.Process_List) {\n              dataItem.Process_List = []\n            }\n\n            // 如果 Process_List 为空，直接追加所有 Process_List_All 的数据\n            if (dataItem.Process_List.length === 0) {\n              dataItem.Process_List.push(...Process_List_All)\n            } else {\n              // 将 Process_List_All 中不存在于当前项目 Process_List 的数据追加进去\n              Process_List_All.forEach(newItem => {\n                // 检查当前项目的 Process_List 中是否已存在相同的 Bom_Level\n                const exists = dataItem.Process_List.some(existingItem =>\n                  existingItem.Bom_Level === newItem.Bom_Level ||\n                  existingItem.Bom_Level === newItem.Bom_Level.toString()\n                )\n\n                // 如果不存在，则追加到当前项目的 Process_List\n                if (!exists) {\n                  dataItem.Process_List.push(newItem)\n                }\n              })\n            }\n          })\n          this.list = resData\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    getProcessList() {\n      GetProcessList({ }).then(res => {\n        this.selectList = res.Data\n      }).finally(() => {\n        this.getTypeList()\n      })\n    },\n    handleSubmit() {\n      this.btnLoading = true\n      SaveConsumingProcessAllList2(this.list.filter(i => i.Working_Process_Id)).then(res => {\n        if (res.IsSucceed) {\n          this.$message.success('保存成功')\n          this.$emit('close')\n        } else {\n          this.$message.error(res.Message)\n        }\n      }).finally(() => {\n        this.btnLoading = false\n      })\n    },\n    bomClick() {\n      this.getParentBom()\n    },\n    // 获取当前bom层级的所有父级bom层级信息\n    getParentBom() {\n      // 找到当前code在bomList中的索引位置\n      const currentIndex = this.bomList.findIndex(item => item.Code === this.bomActiveName)\n\n      // 如果找到了，则截取该索引之前的所有数据\n      if (currentIndex > 0) {\n        this.parentBomList = this.bomList.slice(0, currentIndex)\n      } else {\n        // 如果是第一个或者没找到，则返回空数组\n        this.parentBomList = []\n      }\n    },\n    // 根据BOM层级Code过滤selectList\n    getFilteredSelectList(code) {\n      if (!this.selectList || !code) {\n        return []\n      }\n      return this.selectList.filter(item => item.Bom_Level.toString() === code || item.Bom_Level.toString() === code)\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"~@/styles/mixin.scss\";\n.form-wrapper {\n  height: 70vh;\n  display: flex;\n  flex-direction: column;\n\n  .form-content {\n    flex: 1;\n    overflow: auto;\n    padding-right: 16px;\n    @include scrollBar;\n\n    .form-x {\n      padding-bottom: 20px;\n    }\n  }\n\n  .form-footer {\n    text-align: right;\n    flex-shrink: 0;\n    padding-top: 16px;\n    background: #fff;\n  }\n}\n</style>\n"]}]}
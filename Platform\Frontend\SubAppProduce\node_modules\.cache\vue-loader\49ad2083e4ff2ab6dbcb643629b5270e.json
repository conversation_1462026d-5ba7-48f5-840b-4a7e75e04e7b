{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue?vue&type=style&index=0&id=d2411270&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue", "mtime": 1757577002377}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgpAaW1wb3J0ICJ+QC9zdHlsZXMvbWl4aW4uc2NzcyI7Ci5mb3JtLXdyYXBwZXIgewogIGhlaWdodDogNTB2aDsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CgogIC5mb3JtLWNvbnRlbnQgewogICAgZmxleDogMTsKICAgIG92ZXJmbG93OiBhdXRvOwogICAgcGFkZGluZy1yaWdodDogMTZweDsKICAgIEBpbmNsdWRlIHNjcm9sbEJhcjsKCiAgICAuZm9ybS14IHsKICAgICAgcGFkZGluZy1ib3R0b206IDIwcHg7CiAgICB9CiAgfQoKICAuZm9ybS1mb290ZXIgewogICAgdGV4dC1hbGlnbjogcmlnaHQ7CiAgICBmbGV4LXNocmluazogMDsKICAgIHBhZGRpbmctdG9wOiAxNnB4OwogICAgYmFja2dyb3VuZDogI2ZmZjsKICB9Cn0KCi5jYW4tcHJvY2Vzcy10aXRsZSB7CiAgZGlzcGxheTogZmxleDsKICBoZWlnaHQ6IDM4cHg7CiAgLmNhbi1wcm9jZXNzLWxpc3QgewogICAgZmxleDogMTsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtc3RhcnQ7CiAgICBmb250LXNpemU6IDE2cHg7CiAgICBjb2xvcjogIzMzMzMzMzsKICB9Cn0KCi5jYW4tcHJvY2Vzcy1ib3ggewogIGhlaWdodDogY2FsYygxMDAlIC0gMzhweCk7CiAgLmNhbi1wcm9jZXNzLWVtcHR5IHsKICAgIGhlaWdodDogMTAwJTsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgfQogIC5jYW4tcHJvY2Vzcy1pdGVtIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBtYXJnaW4tYm90dG9tOiAxNnB4OwogICAgLmNhbi1wcm9jZXNzLXR5cGUgewogICAgICBmb250LXNpemU6IDE0cHg7CiAgICAgIGNvbG9yOiAjMzMzMzMzOwogICAgICB0ZXh0LWFsaWduOiByaWdodDsKICAgIH0KICAgIC5jYW4tcHJvY2Vzcy1ib20gewogICAgICBmbGV4OiAxOwogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtc3RhcnQ7CiAgICAgIC5jYW4tcHJvY2Vzcy1zZWxlY3QgewogICAgICAgIG1hcmdpbi1sZWZ0OiAxMnB4OwogICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgICAgfQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["PartTakeConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4RA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PartTakeConfig.vue", "sourceRoot": "src/views/PRO/process-settings/management/component", "sourcesContent": ["<template>\n  <div class=\"form-wrapper\">\n    <div class=\"form-recognition-tabs\">\n      <el-tabs v-model=\"bomActiveName\" @tab-click=\"bomClick\">\n        <el-tab-pane v-for=\"(item, index) in tabBomList\" :key=\"index\" :label=\"item.Display_Name\" :name=\"item.Code\" />\n      </el-tabs>\n    </div>\n    <div class=\"form-content\">\n      <div class=\"can-process-title\">\n        <div style=\"width: 120px;\" />\n        <div class=\"can-process-list\">\n          <div v-for=\"(item, index) in parentBomList\" :key=\"index\" :style=\"{ width: (100 / parentBomList.length) + '%', textAlign: 'center' }\">{{ item.Display_Name }}</div>\n        </div>\n      </div>\n      <div class=\"can-process-box\">\n        <div v-if=\"!filteredList.length\" class=\"can-process-empty\">\n          暂无数据\n        </div>\n        <div v-for=\"(item, index) in filteredList\" :key=\"index\" class=\"can-process-item\">\n          <div class=\"can-process-type\" style=\"width: 120px;\">{{ item.Part_Type_Name }}</div>\n          <div class=\"can-process-bom\">\n            <div v-for=\"bom in parentBomList\" :key=\"`${item.Part_Type_Id || index}-${bom.Code}`\" class=\"can-process-select\" :style=\"{ width: (100 / parentBomList.length) + '%' }\">\n              <el-select\n                :key=\"`select-${item.Part_Type_Id || item.Part_Type_Name || index}-${bom.Code}`\"\n                :value=\"getProcessId(item, bom.Code)\"\n                clearable\n                style=\"width: 100%;\"\n                placeholder=\"请选择\"\n                @change=\"updateProcessId(item, bom.Code, $event)\"\n              >\n                <el-option v-for=\"op in getFilteredSelectList(bom.Code)\" :key=\"op.Id\" :label=\"op.Name\" :value=\"op.Id\" />\n              </el-select>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div class=\"form-footer\">\n      <el-button @click=\"$emit('close')\">取 消</el-button>\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport { GetConsumingAllList, SaveConsumingProcessAllList2 } from '@/api/PRO/partType'\nimport { GetProcessList } from '@/api/PRO/technology-lib'\n\nexport default {\n  data() {\n    return {\n      list: [],\n      btnLoading: false,\n      selectList: [],\n      bomList: [],\n      tabBomList: [],\n      comName: '',\n      partName: '',\n      bomActiveName: '',\n      parentBomList: []\n    }\n  },\n  computed: {\n    // 根据当前选中的BOM层级过滤数据\n    filteredList() {\n      if (!this.list || !this.bomActiveName) {\n        return []\n      }\n      return this.list.filter(item => item.Use_Bom_Level.toString() === this.bomActiveName)\n    }\n  },\n  async mounted() {\n    await this.getBom()\n    await this.getParentBom()\n    await this.getProcessList()\n  },\n  methods: {\n    async getBom() {\n      const { comName, partName, list } = await GetBOMInfo()\n      this.comName = comName\n      this.partName = partName\n      this.bomList = list\n      this.tabBomList = list.filter(i => i.Code !== '-1')\n      this.bomActiveName = this.tabBomList[this.tabBomList.length - 1].Code\n    },\n    getTypeList() {\n      GetConsumingAllList({}).then(res => {\n        if (res.IsSucceed) {\n          const resData = res.Data\n          const Process_List_All = []\n          this.parentBomList.map(item => {\n            const Process_List_Json = {}\n            Process_List_Json.Bom_Level = item.Code\n            Process_List_Json.Working_Process_Id = ''\n            Process_List_Json.Working_Process_Code = ''\n            Process_List_Json.Working_Process_Name = ''\n            Process_List_All.push(Process_List_Json)\n          })\n\n          // 遍历 resData 中的每个项目，为每个项目的 Process_List 添加缺失的数据\n          resData.forEach(dataItem => {\n            // 确保每个项目的 Process_List 存在\n            if (!dataItem.Process_List) {\n              dataItem.Process_List = []\n            }\n\n            // 如果 Process_List 为空，直接追加所有 Process_List_All 的数据（深拷贝）\n            if (dataItem.Process_List.length === 0) {\n              Process_List_All.forEach(item => {\n                dataItem.Process_List.push({\n                  Bom_Level: item.Bom_Level,\n                  Working_Process_Id: item.Working_Process_Id,\n                  Working_Process_Code: item.Working_Process_Code,\n                  Working_Process_Name: item.Working_Process_Name\n                })\n              })\n            } else {\n              // 将 Process_List_All 中不存在于当前项目 Process_List 的数据追加进去\n              Process_List_All.forEach(newItem => {\n                // 检查当前项目的 Process_List 中是否已存在相同的 Bom_Level\n                const exists = dataItem.Process_List.some(existingItem =>\n                  existingItem.Bom_Level.toString() === newItem.Bom_Level.toString()\n                )\n\n                // 如果不存在，则追加到当前项目的 Process_List（深拷贝）\n                if (!exists) {\n                  dataItem.Process_List.push({\n                    Bom_Level: newItem.Bom_Level,\n                    Working_Process_Id: newItem.Working_Process_Id,\n                    Working_Process_Code: newItem.Working_Process_Code,\n                    Working_Process_Name: newItem.Working_Process_Name\n                  })\n                }\n              })\n            }\n          })\n          this.list = resData\n          // 确保数据独立性\n          this.$nextTick(() => {\n            this.ensureDataIndependence()\n          })\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    getProcessList() {\n      GetProcessList({ }).then(res => {\n        this.selectList = res.Data\n      }).finally(() => {\n        this.getTypeList()\n      })\n    },\n    handleSubmit() {\n      this.btnLoading = true\n      SaveConsumingProcessAllList2(this.list).then(res => {\n        if (res.IsSucceed) {\n          this.$message.success('保存成功')\n          this.$emit('close')\n        } else {\n          this.$message.error(res.Message)\n        }\n      }).finally(() => {\n        this.btnLoading = false\n      })\n    },\n    bomClick() {\n      this.getParentBom()\n    },\n    // 获取当前bom层级的所有父级bom层级信息\n    getParentBom() {\n      // 找到当前code在bomList中的索引位置\n      const currentIndex = this.bomList.findIndex(item => item.Code === this.bomActiveName)\n\n      // 如果找到了，则截取该索引之前的所有数据\n      if (currentIndex > 0) {\n        this.parentBomList = this.bomList.slice(0, currentIndex)\n      } else {\n        // 如果是第一个或者没找到，则返回空数组\n        this.parentBomList = []\n      }\n    },\n    // 根据BOM层级Code过滤selectList\n    getFilteredSelectList(code) {\n      if (!this.selectList || !code) {\n        return []\n      }\n      return this.selectList.filter(item => item.Bom_Level.toString() === code || item.Bom_Level.toString() === code)\n    },\n    // 获取指定item和bomCode对应的Working_Process_Id\n    getProcessId(item, bomCode) {\n      if (!item.Process_List || !bomCode) {\n        return ''\n      }\n\n      // 确保 Process_List 是数组\n      if (!Array.isArray(item.Process_List)) {\n        return ''\n      }\n\n      const processItem = item.Process_List.find(p =>\n        p && p.Bom_Level && p.Bom_Level.toString() === bomCode.toString()\n      )\n\n      return processItem ? (processItem.Working_Process_Id || '') : ''\n    },\n    // 更新指定item和bomCode对应的Working_Process_Id\n    updateProcessId(item, bomCode, value) {\n      // 确保 Process_List 存在且是数组\n      if (!item.Process_List || !Array.isArray(item.Process_List)) {\n        this.$set(item, 'Process_List', [])\n      }\n\n      // 查找对应的 Bom_Level 项目\n      const processItemIndex = item.Process_List.findIndex(p =>\n        p && p.Bom_Level && p.Bom_Level.toString() === bomCode.toString()\n      )\n\n      if (processItemIndex !== -1) {\n        // 如果找到了对应的项目，更新其值\n        const processItem = item.Process_List[processItemIndex]\n\n        // 使用 $set 确保响应式更新\n        this.$set(processItem, 'Working_Process_Id', value)\n\n        // 同时更新对应的工艺信息\n        if (value) {\n          const selectedProcess = this.selectList.find(s => s.Id === value)\n          if (selectedProcess) {\n            this.$set(processItem, 'Working_Process_Code', selectedProcess.Code || '')\n            this.$set(processItem, 'Working_Process_Name', selectedProcess.Name || '')\n          }\n        } else {\n          this.$set(processItem, 'Working_Process_Code', '')\n          this.$set(processItem, 'Working_Process_Name', '')\n        }\n      } else {\n        // 如果没找到，创建新的项目\n        const newProcessItem = {\n          Bom_Level: bomCode,\n          Working_Process_Id: value,\n          Working_Process_Code: '',\n          Working_Process_Name: ''\n        }\n\n        // 如果有选中值，填充工艺信息\n        if (value) {\n          const selectedProcess = this.selectList.find(s => s.Id === value)\n          if (selectedProcess) {\n            newProcessItem.Working_Process_Code = selectedProcess.Code || ''\n            newProcessItem.Working_Process_Name = selectedProcess.Name || ''\n          }\n        }\n\n        // 使用 $set 添加新项目\n        item.Process_List.push(newProcessItem)\n      }\n    },\n    // 确保每个item的Process_List都是独立的\n    ensureDataIndependence() {\n      if (this.list && Array.isArray(this.list)) {\n        this.list.forEach(item => {\n          if (item.Process_List && Array.isArray(item.Process_List)) {\n            // 深拷贝 Process_List 确保数据独立\n            const originalProcessList = item.Process_List\n            item.Process_List = originalProcessList.map(processItem => ({\n              Bom_Level: processItem.Bom_Level,\n              Working_Process_Id: processItem.Working_Process_Id || '',\n              Working_Process_Code: processItem.Working_Process_Code || '',\n              Working_Process_Name: processItem.Working_Process_Name || ''\n            }))\n          }\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"~@/styles/mixin.scss\";\n.form-wrapper {\n  height: 50vh;\n  display: flex;\n  flex-direction: column;\n\n  .form-content {\n    flex: 1;\n    overflow: auto;\n    padding-right: 16px;\n    @include scrollBar;\n\n    .form-x {\n      padding-bottom: 20px;\n    }\n  }\n\n  .form-footer {\n    text-align: right;\n    flex-shrink: 0;\n    padding-top: 16px;\n    background: #fff;\n  }\n}\n\n.can-process-title {\n  display: flex;\n  height: 38px;\n  .can-process-list {\n    flex: 1;\n    display: flex;\n    justify-content: flex-start;\n    font-size: 16px;\n    color: #333333;\n  }\n}\n\n.can-process-box {\n  height: calc(100% - 38px);\n  .can-process-empty {\n    height: 100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  .can-process-item {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    margin-bottom: 16px;\n    .can-process-type {\n      font-size: 14px;\n      color: #333333;\n      text-align: right;\n    }\n    .can-process-bom {\n      flex: 1;\n      display: flex;\n      justify-content: flex-start;\n      .can-process-select {\n        margin-left: 12px;\n        text-align: center;\n      }\n    }\n  }\n}\n</style>\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue?vue&type=style&index=0&id=d2411270&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue", "mtime": 1757468113428}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCkBpbXBvcnQgIn5AL3N0eWxlcy9taXhpbi5zY3NzIjsNCi5mb3JtLXdyYXBwZXIgew0KICBoZWlnaHQ6IDcwdmg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQoNCiAgLmZvcm0tY29udGVudCB7DQogICAgZmxleDogMTsNCiAgICBvdmVyZmxvdzogYXV0bzsNCiAgICBwYWRkaW5nLXJpZ2h0OiAxNnB4Ow0KICAgIEBpbmNsdWRlIHNjcm9sbEJhcjsNCg0KICAgIC5mb3JtLXggew0KICAgICAgcGFkZGluZy1ib3R0b206IDIwcHg7DQogICAgfQ0KICB9DQoNCiAgLmZvcm0tZm9vdGVyIHsNCiAgICB0ZXh0LWFsaWduOiByaWdodDsNCiAgICBmbGV4LXNocmluazogMDsNCiAgICBwYWRkaW5nLXRvcDogMTZweDsNCiAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICB9DQp9DQo="}, {"version": 3, "sources": ["PartTakeConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2EA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PartTakeConfig.vue", "sourceRoot": "src/views/PRO/process-settings/management/component", "sourcesContent": ["<template>\r\n  <div class=\"form-wrapper\">\r\n    <div class=\"form-content\">\r\n      <el-form ref=\"form\" label-width=\"120px\" class=\"form-x\">\r\n        <el-form-item v-for=\"(item,index) in list\" :key=\"index\" :show-message=\"false\" :label=\"item.Part_Type_Name\" prop=\"mainPart\">\r\n          <el-select v-model=\"item.Working_Process_Id\" clearable>\r\n            <el-option v-for=\"op in selectList\" :key=\"op.Id\" :label=\"op.Name\" :value=\"op.Id\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n    <div class=\"form-footer\">\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { GetConsumingProcessAllList, SaveConsumingProcessAllList } from '@/api/PRO/partType'\r\nimport { GetProcessList } from '@/api/PRO/technology-lib'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      list: [],\r\n      btnLoading: false,\r\n      selectList: []\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getProcessList()\r\n  },\r\n  methods: {\r\n    getTypeList() {\r\n      GetConsumingProcessAllList({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.list = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getProcessList() {\r\n      GetProcessList({ type: 1 }).then(res => {\r\n        this.selectList = res.Data\r\n      }).finally(() => {\r\n        this.getTypeList()\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      this.btnLoading = true\r\n      SaveConsumingProcessAllList(this.list.filter(i => i.Working_Process_Id)).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message.success('保存成功')\r\n          this.$emit('close')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      }).finally(() => {\r\n        this.btnLoading = false\r\n      })\r\n    },\r\n    mainBlur(e) {\r\n\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"~@/styles/mixin.scss\";\r\n.form-wrapper {\r\n  height: 70vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .form-content {\r\n    flex: 1;\r\n    overflow: auto;\r\n    padding-right: 16px;\r\n    @include scrollBar;\r\n\r\n    .form-x {\r\n      padding-bottom: 20px;\r\n    }\r\n  }\r\n\r\n  .form-footer {\r\n    text-align: right;\r\n    flex-shrink: 0;\r\n    padding-top: 16px;\r\n    background: #fff;\r\n  }\r\n}\r\n</style>\r\n"]}]}
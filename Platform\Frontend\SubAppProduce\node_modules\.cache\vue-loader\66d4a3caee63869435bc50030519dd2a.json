{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\index.vue?vue&type=style&index=0&id=0a6b2a04&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\index.vue", "mtime": 1757468112875}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuSA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"app-container abs100\">\r\n      <div class=\"h100 wrapper-c parent\">\r\n        <div class=\"title\">\r\n          <span\r\n            v-for=\"(item, index) in title\"\r\n            :key=\"index\"\r\n            style=\"cursor: pointer\"\r\n            :class=\"spanCurr == index ? 'clickindex' : 'index'\"\r\n            @click=\"handelIndex(index, item)\"\r\n          >{{ item.Display_Name }}</span>\r\n        </div>\r\n        <div class=\"detail\">\r\n          <template>\r\n            <el-tabs\r\n              v-model=\"activeName\"\r\n              type=\"card\"\r\n              style=\"width: 100%; height: 100%\"\r\n            >\r\n              <el-tab-pane label=\"检查类型\" name=\"检查类型\">\r\n                <CheckType\r\n                  ref=\"checkTypeRef\"\r\n                  :check-type=\"checkType\"\r\n                  @optionFn=\"optionEdit\"\r\n                />\r\n              </el-tab-pane>\r\n              <el-tab-pane label=\"检查项\" name=\"检查项\">\r\n                <CheckItem\r\n                  ref=\"checkItemRef\"\r\n                  :check-type=\"checkType\"\r\n                  @ItemEdit=\"ItemEdit\"\r\n                />\r\n              </el-tab-pane>\r\n              <el-tab-pane label=\"检查项组合\" name=\"检查项组合\">\r\n                <CheckCombination\r\n                  ref=\"checkCombinationRef\"\r\n                  :check-type=\"checkType\"\r\n                  @CombinationEdit=\"CombinationEdit\"\r\n                />\r\n              </el-tab-pane>\r\n              <el-tab-pane label=\"质检节点配置\" name=\"质检节点配置\">\r\n                <CheckNode\r\n                  ref=\"checkNodeRef\"\r\n                  :check-type=\"checkType\"\r\n                  @NodeEdit=\"NodeEdit\"\r\n                />\r\n              </el-tab-pane>\r\n              <el-tab-pane v-if=\"isCom\" label=\"公差配置\" name=\"公差配置\">\r\n                <ToleranceConfig\r\n                  ref=\"toleranceConfigRef\"\r\n                  :check-type=\"checkType\"\r\n                  @edit=\"addToleranceConfig\"\r\n                />\r\n              </el-tab-pane>\r\n              <el-button\r\n                type=\"primary\"\r\n                class=\"addbtn\"\r\n                @click=\"addData\"\r\n              >新增</el-button>\r\n            </el-tabs>\r\n          </template>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      ref=\"content\"\r\n      v-el-drag-dialog\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      :width=\"width\"\r\n      class=\"z-dialog\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :dialog-data=\"dialogData\"\r\n        @ToleranceRefresh=\"ToleranceRefresh\"\r\n        @close=\"handleClose\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CheckType from './components/CheckType' // 检查类型\r\nimport CheckCombination from './components/CheckCombination' // 检查项组合\r\nimport CheckNode from './components/CheckNode' // 质检节点配置\r\nimport CheckItem from './components/CheckItem' // 检查项\r\nimport TypeDialog from './components/Dialog/TypeDialog' // 检查类型弹窗\r\nimport ItemDialog from './components/Dialog/ItemDialog' // 检查项弹窗\r\nimport CombinationDialog from './components/Dialog/CombinationDialog' // 检查项组合弹窗\r\nimport NodeDialog from './components/Dialog/NodeDialog' // 质检节点组合弹窗\r\nimport ToleranceConfig from './components/ToleranceConfig.vue' // 公差配置\r\n// import { GetDictionaryDetailListByCode } from '@/api/PRO/factorycheck'\r\nimport elDragDialog from '@/directive/el-drag-dialog'\r\nimport ToleranceDialog from './components/Dialog/ToleranceDialog' // 公差配置弹窗\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\nexport default {\r\n  name: 'PLMFactoryGroupList',\r\n  directives: { elDragDialog },\r\n  components: {\r\n    CheckType,\r\n    ToleranceConfig,\r\n    CheckCombination,\r\n    CheckNode,\r\n    CheckItem,\r\n    TypeDialog,\r\n    ItemDialog,\r\n    CombinationDialog,\r\n    NodeDialog,\r\n    ToleranceDialog\r\n  },\r\n  data() {\r\n    return {\r\n      spanCurr: 0,\r\n      title: [],\r\n      activeName: '检查类型',\r\n      checkType: {},\r\n      tbLoading: false,\r\n      tbData: [],\r\n      dialogVisible: false,\r\n      currentComponent: '',\r\n      dialogTitle: '',\r\n      isCom: false,\r\n      width: '60%',\r\n      dialogData: {}\r\n    }\r\n  },\r\n  created() {},\r\n  async mounted() {\r\n    this.getCheckType()\r\n  },\r\n  methods: {\r\n    async getCheckType() {\r\n      const bomLevel = await GetBOMInfo()\r\n      this.title = bomLevel.list\r\n      this.checkType = bomLevel.list[0]\r\n      this.isCom = bomLevel.list.find(v => v.Code === '-1')\r\n      // GetDictionaryDetailListByCode({ dictionaryCode: 'Quality_Code' }).then(\r\n      //   (res) => {\r\n      //     if (res.IsSucceed) {\r\n      //       this.title = res.Data // wtf\r\n      //       this.checkType = this.title[0]// wtf\r\n      //       this.isCom = res.Data.find(v => v.Value === '0')\r\n      //     } else {\r\n      //       this.$message({\r\n      //         type: 'error',\r\n      //         message: 'res.Message'\r\n      //       })\r\n      //     }\r\n      //   }\r\n      // )\r\n    },\r\n    handelIndex(index, item) {\r\n      this.isCom = item.Code === '-1'\r\n      if (!this.isCom && this.activeName === '公差配置') {\r\n        this.activeName = '检查类型'\r\n      }\r\n      this.checkType = item\r\n      this.spanCurr = index\r\n    },\r\n    addData() {\r\n      console.log(this.activeName)\r\n      switch (this.activeName) {\r\n        case '检查类型':\r\n          this.addCheckType()\r\n          break\r\n        case '检查项':\r\n          this.addCheckItem()\r\n          break\r\n        case '检查项组合':\r\n          this.addCheckCombination()\r\n          break\r\n        case '质检节点配置':\r\n          this.addCheckNode()\r\n          break\r\n        case '公差配置':\r\n          this.addToleranceConfig()\r\n          break\r\n        default:\r\n          this.addCheckType()\r\n          console.log('111')\r\n      }\r\n    },\r\n    addCheckType() {\r\n      this.width = '30%'\r\n      this.generateComponent('新增检查类型', 'TypeDialog')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init('新增', this.checkType)\r\n      })\r\n    },\r\n    editCheckType(data) {\r\n      this.width = '30%'\r\n      this.generateComponent('编辑检查类型', 'TypeDialog')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init('编辑', this.checkType, data)\r\n      })\r\n    },\r\n    addCheckItem() {\r\n      this.width = '30%'\r\n      this.generateComponent('新增检查项', 'ItemDialog')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init('新增', this.checkType)\r\n      })\r\n    },\r\n    editCheckItem(data) {\r\n      this.width = '30%'\r\n      this.generateComponent('编辑检查项', 'ItemDialog')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init('编辑', this.checkType, data)\r\n      })\r\n    },\r\n    addCheckCombination() {\r\n      this.width = '40%'\r\n      this.generateComponent('新增检查项组合', 'CombinationDialog')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init('新增', this.checkType)\r\n      })\r\n    },\r\n    editCheckCombination(data) {\r\n      this.width = '40%'\r\n      this.generateComponent('编辑检查项组合', 'CombinationDialog')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init('编辑', this.checkType, data)\r\n      })\r\n    },\r\n    addCheckNode() {\r\n      this.width = '45%'\r\n      this.generateComponent('新增质检节点配置', 'NodeDialog')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init('新增', this.checkType)\r\n      })\r\n    },\r\n    editCheckNode(data) {\r\n      this.width = '45%'\r\n      this.generateComponent('编辑质检节点配置', 'NodeDialog')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init('编辑', this.checkType, data)\r\n      })\r\n    },\r\n    addToleranceConfig(data) {\r\n      this.width = '45%'\r\n      this.generateComponent(data?.Id ? '编辑公差配置' : '新增公差配置', 'ToleranceDialog')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(data?.Id ? '编辑' : '新增', this.checkType.Id, data)\r\n      })\r\n    },\r\n    handleClose() {\r\n      switch (this.activeName) {\r\n        case '检查类型':\r\n          this.$refs.checkTypeRef.getCheckTypeList()\r\n          break\r\n        case '检查项':\r\n          this.$refs.checkItemRef.getCheckItemList()\r\n          break\r\n        case '检查项组合':\r\n          this.$refs.checkCombinationRef.getQualityList()\r\n          break\r\n        case '质检节点配置':\r\n          this.$refs.checkNodeRef.getNodeList()\r\n          break\r\n      }\r\n\r\n      this.dialogVisible = false\r\n    },\r\n    generateComponent(title, component) {\r\n      this.dialogTitle = title\r\n      this.currentComponent = component\r\n      this.dialogVisible = true\r\n    },\r\n    optionEdit(data) {\r\n      // this.dialogData = Object.assign({},data)\r\n      this.editCheckType(data)\r\n    },\r\n    ItemEdit(data) {\r\n      this.editCheckItem(data)\r\n    },\r\n    CombinationEdit(data) {\r\n      this.editCheckCombination(data)\r\n    },\r\n    NodeEdit(data) {\r\n      this.editCheckNode(data)\r\n    },\r\n    ToleranceRefresh(data) {\r\n      this.$refs.toleranceConfigRef.getToleranceList()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/styles/mixin.scss\";\r\n@import \"~@/styles/variables.scss\";\r\n.wrapper-c {\r\n  background: #fff;\r\n  box-sizing: border-box;\r\n  // padding: 8px 16px 0;\r\n}\r\n.title {\r\n  width: 100%;\r\n  height: 48px;\r\n  padding: 0 16px;\r\n  background-color: white;\r\n\r\n  .index {\r\n    font-size: 16px;\r\n    line-height: 48px;\r\n    margin-right: 16px;\r\n    padding: 0 16px;\r\n    display: inline-block;\r\n    text-align: center;\r\n    color: #999999;\r\n  }\r\n  .clickindex {\r\n    border-bottom: 2px solid #298dff;\r\n    font-size: 16px;\r\n    line-height: 46px;\r\n    margin-right: 16px;\r\n    padding: 0 16px;\r\n    display: inline-block;\r\n    text-align: center;\r\n    color: #298dff;\r\n  }\r\n}\r\n::v-deep {\r\n  .el-tabs {\r\n    display: inline-block;\r\n  }\r\n  .el-tabs--card .el-tabs__header {\r\n    border: 0;\r\n    margin: 0;\r\n  }\r\n  .el-tabs--card > .el-tabs__header .el-tabs__nav {\r\n    border-bottom: 1px solid #dfe4ed;\r\n  }\r\n  .el-tabs__content {\r\n    margin-top: 16px !important;\r\n  }\r\n}\r\n.detail {\r\n  height: calc(100vh - 240px);\r\n  box-sizing: border-box;\r\n  padding: 16px;\r\n  border-top: 16px solid #f8f8f8;\r\n}\r\n.addbtn {\r\n  position: fixed;\r\n  right: 38px;\r\n  top: 210px;\r\n}\r\n.z-dialog {\r\n  ::v-deep {\r\n    .el-dialog__header {\r\n      background-color: #298dff;\r\n\r\n      .el-dialog__title,\r\n      .el-dialog__close {\r\n        color: #ffffff;\r\n      }\r\n    }\r\n\r\n    .el-dialog__body {\r\n      max-height: 700px;\r\n      overflow: auto;\r\n      @include scrollBar;\r\n\r\n      &::-webkit-scrollbar {\r\n        width: 8px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\index.vue?vue&type=style&index=0&id=0a6b2a04&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\index.vue", "mtime": 1756109219900}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoSA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup", "sourcesContent": ["<template>\n  <div>\n    <div class=\"app-container abs100\">\n      <div class=\"h100 wrapper-c parent\">\n        <div class=\"title\">\n          <span\n            v-for=\"(item, index) in title\"\n            :key=\"index\"\n            style=\"cursor: pointer\"\n            :class=\"spanCurr == index ? 'clickindex' : 'index'\"\n            @click=\"handelIndex(index, item)\"\n          >{{ item.Display_Name }}</span>\n        </div>\n        <div class=\"detail\">\n          <template>\n            <el-tabs\n              v-model=\"activeName\"\n              type=\"card\"\n              style=\"width: 100%; height: 100%\"\n            >\n              <el-tab-pane label=\"检查类型\" name=\"检查类型\">\n                <CheckType\n                  ref=\"checkTypeRef\"\n                  :check-type=\"checkType\"\n                  @optionFn=\"optionEdit\"\n                />\n              </el-tab-pane>\n              <el-tab-pane label=\"检查项\" name=\"检查项\">\n                <CheckItem\n                  ref=\"checkItemRef\"\n                  :check-type=\"checkType\"\n                  @ItemEdit=\"ItemEdit\"\n                />\n              </el-tab-pane>\n              <el-tab-pane label=\"检查项组合\" name=\"检查项组合\">\n                <CheckCombination\n                  ref=\"checkCombinationRef\"\n                  :check-type=\"checkType\"\n                  @CombinationEdit=\"CombinationEdit\"\n                />\n              </el-tab-pane>\n              <el-tab-pane label=\"质检节点配置\" name=\"质检节点配置\">\n                <CheckNode\n                  ref=\"checkNodeRef\"\n                  :check-type=\"checkType\"\n                  @NodeEdit=\"NodeEdit\"\n                />\n              </el-tab-pane>\n              <el-tab-pane v-if=\"isCom\" label=\"公差配置\" name=\"公差配置\">\n                <ToleranceConfig\n                  ref=\"toleranceConfigRef\"\n                  :check-type=\"checkType\"\n                  @edit=\"addToleranceConfig\"\n                />\n              </el-tab-pane>\n              <el-button\n                type=\"primary\"\n                class=\"addbtn\"\n                @click=\"addData\"\n              >新增</el-button>\n            </el-tabs>\n          </template>\n        </div>\n      </div>\n    </div>\n    <el-dialog\n      v-if=\"dialogVisible\"\n      ref=\"content\"\n      v-el-drag-dialog\n      :title=\"dialogTitle\"\n      :visible.sync=\"dialogVisible\"\n      :close-on-click-modal=\"false\"\n      :width=\"width\"\n      class=\"z-dialog\"\n      @close=\"handleClose\"\n    >\n      <component\n        :is=\"currentComponent\"\n        ref=\"content\"\n        :dialog-data=\"dialogData\"\n        @ToleranceRefresh=\"ToleranceRefresh\"\n        @close=\"handleClose\"\n      />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport CheckType from './components/CheckType' // 检查类型\nimport CheckCombination from './components/CheckCombination' // 检查项组合\nimport CheckNode from './components/CheckNode' // 质检节点配置\nimport CheckItem from './components/CheckItem' // 检查项\nimport TypeDialog from './components/Dialog/TypeDialog' // 检查类型弹窗\nimport ItemDialog from './components/Dialog/ItemDialog' // 检查项弹窗\nimport CombinationDialog from './components/Dialog/CombinationDialog' // 检查项组合弹窗\nimport NodeDialog from './components/Dialog/NodeDialog' // 质检节点组合弹窗\nimport ToleranceConfig from './components/ToleranceConfig.vue' // 公差配置\nimport { GetDictionaryDetailListByCode } from '@/api/PRO/factorycheck'\nimport elDragDialog from '@/directive/el-drag-dialog'\nimport ToleranceDialog from './components/Dialog/ToleranceDialog' // 公差配置弹窗\nexport default {\n  name: 'PLMFactoryGroupList',\n  directives: { elDragDialog },\n  components: {\n    CheckType,\n    ToleranceConfig,\n    CheckCombination,\n    CheckNode,\n    CheckItem,\n    TypeDialog,\n    ItemDialog,\n    CombinationDialog,\n    NodeDialog,\n    ToleranceDialog\n  },\n  data() {\n    return {\n      spanCurr: 0,\n      title: [],\n      activeName: '检查类型',\n      checkType: {},\n      tbLoading: false,\n      tbData: [],\n      dialogVisible: false,\n      currentComponent: '',\n      dialogTitle: '',\n      isCom: false,\n      width: '60%',\n      dialogData: {}\n    }\n  },\n  created() {},\n  async mounted() {\n    this.getCheckType()\n  },\n  methods: {\n    getCheckType() {\n      GetDictionaryDetailListByCode({ dictionaryCode: 'Quality_Code' }).then(\n        (res) => {\n          if (res.IsSucceed) {\n            this.title = res.Data // wtf\n            this.checkType = this.title[0]// wtf\n            this.isCom = res.Data.find(v => v.Value === '0')\n          } else {\n            this.$message({\n              type: 'error',\n              message: 'res.Message'\n            })\n          }\n        }\n      )\n    },\n    handelIndex(index, item) {\n      this.isCom = item.Value === '0'\n      console.log(index)\n      console.log(item)\n      if (!this.isCom && this.activeName === '公差配置') {\n        this.activeName = '检查类型'\n      }\n      this.checkType = item\n      this.spanCurr = index\n    },\n    addData() {\n      console.log(this.activeName)\n      switch (this.activeName) {\n        case '检查类型':\n          this.addCheckType()\n          break\n        case '检查项':\n          this.addCheckItem()\n          break\n        case '检查项组合':\n          this.addCheckCombination()\n          break\n        case '质检节点配置':\n          this.addCheckNode()\n          break\n        case '公差配置':\n          this.addToleranceConfig()\n          break\n        default:\n          this.addCheckType()\n          console.log('111')\n      }\n    },\n    addCheckType() {\n      this.width = '30%'\n      this.generateComponent('新增检查类型', 'TypeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType.Id)\n      })\n    },\n    editCheckType(data) {\n      this.width = '30%'\n      this.generateComponent('编辑检查类型', 'TypeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType.Id, data)\n      })\n    },\n    addCheckItem() {\n      this.width = '30%'\n      this.generateComponent('新增检查项', 'ItemDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType.Id)\n      })\n    },\n    editCheckItem(data) {\n      this.width = '30%'\n      this.generateComponent('编辑检查项', 'ItemDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType.Id, data)\n      })\n    },\n    addCheckCombination() {\n      this.width = '40%'\n      this.generateComponent('新增检查项组合', 'CombinationDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType)\n      })\n    },\n    editCheckCombination(data) {\n      this.width = '40%'\n      this.generateComponent('编辑检查项组合', 'CombinationDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType, data)\n      })\n    },\n    addCheckNode() {\n      this.width = '45%'\n      this.generateComponent('新增质检节点配置', 'NodeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType.Id)\n      })\n    },\n    editCheckNode(data) {\n      this.width = '45%'\n      this.generateComponent('编辑质检节点配置', 'NodeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType.Id, data)\n      })\n    },\n    addToleranceConfig(data) {\n      this.width = '45%'\n      this.generateComponent(data?.Id ? '编辑公差配置' : '新增公差配置', 'ToleranceDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init(data?.Id ? '编辑' : '新增', this.checkType.Id, data)\n      })\n    },\n    handleClose() {\n      switch (this.activeName) {\n        case '检查类型':\n          this.$refs.checkTypeRef.getCheckTypeList()\n          break\n        case '检查项':\n          this.$refs.checkItemRef.getCheckItemList()\n          break\n        case '检查项组合':\n          this.$refs.checkCombinationRef.getQualityList()\n          break\n        case '质检节点配置':\n          this.$refs.checkNodeRef.getNodeList()\n          break\n      }\n\n      this.dialogVisible = false\n    },\n    generateComponent(title, component) {\n      this.dialogTitle = title\n      this.currentComponent = component\n      this.dialogVisible = true\n    },\n    optionEdit(data) {\n      // this.dialogData = Object.assign({},data)\n      this.editCheckType(data)\n    },\n    ItemEdit(data) {\n      this.editCheckItem(data)\n    },\n    CombinationEdit(data) {\n      this.editCheckCombination(data)\n    },\n    NodeEdit(data) {\n      this.editCheckNode(data)\n    },\n    ToleranceRefresh(data) {\n      this.$refs.toleranceConfigRef.getToleranceList()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/mixin.scss\";\n@import \"~@/styles/variables.scss\";\n.wrapper-c {\n  background: #fff;\n  box-sizing: border-box;\n  // padding: 8px 16px 0;\n}\n.title {\n  width: 100%;\n  height: 48px;\n  padding: 0 16px;\n  background-color: white;\n\n  .index {\n    font-size: 16px;\n    line-height: 48px;\n    margin-right: 16px;\n    padding: 0 16px;\n    display: inline-block;\n    text-align: center;\n    color: #999999;\n  }\n  .clickindex {\n    border-bottom: 2px solid #298dff;\n    font-size: 16px;\n    line-height: 46px;\n    margin-right: 16px;\n    padding: 0 16px;\n    display: inline-block;\n    text-align: center;\n    color: #298dff;\n  }\n}\n::v-deep {\n  .el-tabs {\n    display: inline-block;\n  }\n  .el-tabs--card .el-tabs__header {\n    border: 0;\n    margin: 0;\n  }\n  .el-tabs--card > .el-tabs__header .el-tabs__nav {\n    border-bottom: 1px solid #dfe4ed;\n  }\n  .el-tabs__content {\n    margin-top: 16px !important;\n  }\n}\n.detail {\n  height: calc(100vh - 240px);\n  box-sizing: border-box;\n  padding: 16px;\n  border-top: 16px solid #f8f8f8;\n}\n.addbtn {\n  position: fixed;\n  right: 38px;\n  top: 210px;\n}\n.z-dialog {\n  ::v-deep {\n    .el-dialog__header {\n      background-color: #298dff;\n\n      .el-dialog__title,\n      .el-dialog__close {\n        color: #ffffff;\n      }\n    }\n\n    .el-dialog__body {\n      max-height: 700px;\n      overflow: auto;\n      @include scrollBar;\n\n      &::-webkit-scrollbar {\n        width: 8px;\n      }\n    }\n  }\n}\n</style>\n"]}]}
<template>
  <div class="container abs100">
    <div class="filter-bar">
      <el-form ref="formRef" :model="form" label-width="80px" inline>
        <el-form-item label="项目名称" prop="Project_Id">
          <el-select v-model="form.Project_Id" placeholder="请选择" clearable @change="projectChange">
            <el-option
              v-for="item in projectOption"
              :key="item.Id"
              :label="item.Short_Name"
              :value="item.Id"
            />
          </el-select>

        </el-form-item>
        <el-form-item label="区域" prop="Area_Id">
          <el-tree-select
            ref="treeSelectArea"
            v-model="form.Area_Id"
            :disabled="!form.Project_Id"
            :select-params="{
              clearable: true,
            }"
            class="cs-tree-x w100"
            :tree-params="treeParamsArea"
            @select-clear="areaClear"
            @node-click="areaChange"
          />
        </el-form-item>
        <el-form-item label="批次" prop="InstallUnit_Id">
          <el-select v-model="form.InstallUnit_Id" clearable placeholder="请选择" :disabled="!form.Area_Id">
            <el-option v-for="option in installOption" :key="option.Id" :label="option.Name" :value="option.Id" />
          </el-select>
        </el-form-item>
        <el-form-item label="制作车间" prop="Workshop_Id">
          <el-select v-model="form.Workshop_Id" clearable class="w100" placeholder="请选择">
            <el-option
              v-for="item in workshopOptions"
              :key="item.Id"
              :label="item.Display_Name"
              :value="item.Id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="收图日期">
          <el-date-picker
            v-model="dateRange"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button :disabled="tbLoading" @click="reset">重置</el-button>
          <el-button type="primary" :disabled="tbLoading" @click="search">搜索</el-button>
        </el-form-item>

      </el-form>
    </div>

    <div class="main-content">
      <vxe-toolbar>
        <template #buttons>
          <el-button type="success" :loading="exportLoading" @click="exportData">导出数据</el-button>
        </template>
      </vxe-toolbar>
      <div class="tb-x">
        <vxe-table
          ref="xTable"
          :empty-render="{name: 'NotData'}"
          show-header-overflow
          :data="tableData"
          class="cs-vxe-table"
          :row-config="{ isCurrent: true, isHover: true }"
          align="left"
          height="auto"
          show-overflow
          :loading="tbLoading"
          stripe
          size="medium"
          resizable
          :tooltip-config="{ enterable: true }"
          :edit-config="{
            trigger: 'click',
            mode: 'cell',
            showIcon: true
          }"
          @checkbox-all="tbSelectChange"
          @checkbox-change="tbSelectChange"
        >
          <template v-for="(item) in columns">
            <vxe-column
              :key="item.Code"
              :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
              :field="item.Code"
              :title="item.Display_Name"
              :min-width="item.Width"
              :align="item.Align"
              :edit-render="item.Is_Edit ? {} : null"
            >
              <template v-if="item.Is_Edit" #edit="{ row }">
                <template v-if="['Drawing_Receive_Date'].includes(item.Code)">
                  <vxe-input
                    v-model="row[item.Code]"
                    type="date"
                    placeholder="请选择日期"
                    @change="saveRow(row, item.Code)"
                  />
                </template>
                <template v-else>
                  <vxe-input
                    v-model="row[item.Code]"
                    placeholder="请输入"
                    @blur="saveRow(row, item.Code)"
                  />
                </template>
              </template>
              <template #default="{ row }">
                <span> {{ row[item.Code] }}</span>
              </template>
            </vxe-column>
          </template>
        </vxe-table>
      </div>
      <Pagination
        :total="total"
        :page-sizes="tablePageSize"
        :page.sync="queryInfo.Page"
        :limit.sync="queryInfo.PageSize"
        class="pagination"
        @pagination="pageChange"
      />
    </div>

  </div>
</template>

<script>
import { GetInstallUnitPageList } from '@/api/PRO/install-unit'
import { GeAreaTrees, GetProjectPageList } from '@/api/PRO/project'
import { tablePageSize } from '@/views/PRO/setting'
import {
  BatchUpdateTrackEditableFields,
  ExportstBatchTrackFromSourceAsync,
  InsertBatchTrack,
  SaveQIReportData
} from '@/api/PRO/qualityInspect/quality-management'
import { GetWorkshopPageList } from '@/api/PRO/basic-information/workshop'
import { combineURL } from '@/utils'
import getTbInfo from '@/mixins/PRO/get-table-info'
import DynamicTableFields from '@/components/DynamicTableFields/index.vue'
import { GetPartTypeList } from '@/api/PRO/partType'
import Pagination from '@/components/Pagination/index.vue'
import moment from 'moment'

export default {
  name: 'PROBatchTracking1',
  components: { DynamicTableFields, Pagination },
  mixins: [getTbInfo],
  data() {
    return {
      dateRange: '',
      dialogVisible: false,
      exportLoading: false,
      gridCode: '',
      currentComponent: '',
      title: '',
      currentRow: null,
      form: {
        Project_Id: '',
        Area_Id: '',
        InstallUnit_Id: '',
        Workshop_Id: '',
        DrawingReceiveDateStart: '',
        DrawingReceiveDateEnd: ''
      },

      projectOption: [], // Define options as needed
      treeParamsArea: {
        'default-expand-all': true,
        filterable: false,
        clickParent: true,
        data: [],
        props: {
          disabled: 'disabled',
          children: 'Children',
          label: 'Label',
          value: 'Id'
        }
      },
      multipleSelection: [],
      areaOption: [],
      installOption: [],
      workshopOptions: [],
      columns: [],
      tableData: [],
      customColumns: [],
      tbLoading: false,
      tablePageSize: tablePageSize,
      queryInfo: {
        Page: 1,
        PageSize: tablePageSize[0]
      },
      total: 0,
      typeList: []
    }
  },
  watch: {
    dateRange: {
      handler(newValue) {
        if (newValue) {
          const [start, end] = newValue
          this.form.DrawingReceiveDateStart = start
          this.form.DrawingReceiveDateEnd = end
        } else {
          this.form.DrawingReceiveDateStart = ''
          this.form.DrawingReceiveDateEnd = ''
        }
      },
      deep: true
    }
  },
  async created() {
    try {
      this.tbLoading = true
      this.gridCode = 'PROBatchTracking'
      this.initColumn()
      await this.getProjectOption()
      this.getWorkShop()
      this.fetchData(1)
    } catch (error) {
      this.tbLoading = false
      console.error('Error during created lifecycle:', error)
    }
  },
  mounted() {
  },
  methods: {
    async initColumn() {
      await this.getTableConfig(this.gridCode, true)
      const res = await GetPartTypeList({ Part_Grade: 0 })
      this.typeList = res.Data
      this.columns = this.columns.filter(v => v.Is_Display)
      const expandedColumns = []
      this.typeList.forEach(item => {
        // 添加数量列
        expandedColumns.push({
          Code: `${item.Code}_Count`,
          Display_Name: `${item.Name}数量`,
          Is_Display: true,
          Is_Edit: false,
          Is_Frozen: false,
          Width: 150
        })
        // 添加重量列
        expandedColumns.push({
          Code: `${item.Code}_Weight`,
          Display_Name: `${item.Name}重量(kg)`,
          Is_Display: true,
          Is_Edit: false,
          Is_Frozen: false,
          Width: 150
        })
      })
      this.columns.splice(5, 0, ...expandedColumns)
    },
    handleClose() {
      this.dialogVisible = false
    },
    fetchData(page) {
      this.tbLoading = true
      page && (this.queryInfo.Page = page)
      InsertBatchTrack({
        QueryParams: this.form,
        PageInfo: this.queryInfo
      }).then(res => {
        if (res.IsSucceed) {
          this.tableData = res.Data.Data.map(item => {
            item.Drawing_Receive_Date = item.Drawing_Receive_Date ? moment(item.Drawing_Receive_Date).format('YYYY-MM-DD') : ''
            return item
          })
          this.total = res.Data.TotalCount
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      }).finally(() => {
        this.tbLoading = false
      })
    },
    getWorkShop() {
      GetWorkshopPageList({ Page: 1, PageSize: -1 }).then(res => {
        if (res.IsSucceed) {
          if (!res?.Data?.Data) {
            this.workshopOptions = []
          }
          this.workshopOptions = res.Data.Data.map(item => {
            return {
              Id: item.Id,
              Display_Name: item.Display_Name
            }
          })
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    reset() {
      this.$refs.formRef.resetFields()
      this.form.Project_Id = ''
      this.dateRange = ''
      this.form.DrawingReceiveDateStart = ''
      this.form.DrawingReceiveDateEnd = ''
      this.fetchData(1)
    },
    search() {
      this.fetchData(1)
    },
    getProjectOption() {
      return new Promise((resolve, reject) => {
        GetProjectPageList({
          Page: 1,
          PageSize: -1
        }).then(res => {
          if (res.IsSucceed) {
            this.projectOption = res.Data?.Data || []
            if (this.projectOption.length) {
              this.form.Project_Id = ''
              this.getAreaList()
            }
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
          resolve()
        })
      })
    },
    projectChange(e) {
      this.form.Area_Id = ''
      this.form.InstallUnit_Id = ''
      this.treeParamsArea.data = []
      this.installOption = []
      this.form.InstallUnit_Id = ''
      if (e) {
        this.getAreaList()
      }
    },

    areaClear() {
      this.form.Area_Id = ''
      this.form.InstallUnit_Id = ''
    },
    getAreaList() {
      if (!this.form.Project_Id) {
        this.$refs.treeSelectArea?.treeDataUpdateFun([])
        return
      }
      GeAreaTrees({
        projectId: this.form.Project_Id
      }).then(res => {
        if (res.IsSucceed) {
          const tree = res.Data
          this.setDisabledTree(tree)
          this.treeParamsArea.data = tree
          this.$nextTick(_ => {
            this.$refs.treeSelectArea?.treeDataUpdateFun(tree)
          })
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    setDisabledTree(root) {
      if (!root) return
      root.forEach((element) => {
        const { Children } = element
        if (Children && Children.length) {
          element.disabled = true
        } else {
          element.disabled = false
          this.setDisabledTree(Children)
        }
      })
    },
    areaChange() {
      this.form.InstallUnit_Id = ''
      this.getInstall()
    },
    getInstall() {
      GetInstallUnitPageList({
        Area_Id: this.form.Area_Id,
        Page: 1,
        PageSize: -1
      }).then(res => {
        if (res.IsSucceed) {
          this.installOption = res.Data.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },

    exportData() {
      this.exportLoading = true
      ExportstBatchTrackFromSourceAsync({
        QueryParams: this.form
      }).then(res => {
        if (res.IsSucceed) {
          this.$message({
            message: '导出成功',
            type: 'success'
          })
          window.open(combineURL(this.$baseUrl, res.Data), '_blank')
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      }).finally(() => {
        this.exportLoading = false
      })
    },
    tbSelectChange(array) {
      this.multipleSelection = array.records
    },

    handleSubmit(formData) {
      // Call the API to save the updated data
      this.tbLoading = true
      SaveQIReportData(formData).then(res => {
        if (res.IsSucceed) {
          this.$message({
            message: '保存成功',
            type: 'success'
          })
          this.handleClose()
          this.fetchData(1)
        } else {
          this.$message({
            message: res.Message || '保存失败',
            type: 'error'
          })
        }
      }).catch(error => {
        this.$message({
          message: '保存失败: ' + error,
          type: 'error'
        })
      }).finally(() => {
        this.tbLoading = false
      })
    },

    // 重新保存改行
    saveRow(row, fieldCode) {
      BatchUpdateTrackEditableFields({
        items: [{
          project_Id: row.Project_Id,
          area_Id: row.Area_Id,
          installUnit_Id: row.InstallUnit_Id,
          workshop_id: row.Workshop_Id,
          structure_Type: row.Structure_Type,
          drawing_Receive_Date: row.Drawing_Receive_Date,
          conclusion: row.Conclusion
        }]
      })
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  padding: 16px;
  display: flex;
  flex-direction: column;
  background-color: unset;
}

.filter-bar {
  padding-top: 16px;
  box-sizing: border-box;
  margin-bottom: 16px;
  background-color: #fff;
}

.main-content {
  padding: 16px 16px 0 16px;
  flex: 1;
  background-color: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .tb-x {
    flex: 1;
    height: 0;
    overflow: hidden;
  }

  .pagination-container {
    text-align: right;
    padding: 16px;
    margin: 0;
  }
}
</style>

{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\component-list\\v4\\component\\BatchEditor.vue?vue&type=template&id=74c80ace&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\component-list\\v4\\component\\BatchEditor.vue", "mtime": 1758266753081}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}
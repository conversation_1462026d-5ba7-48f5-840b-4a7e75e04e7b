{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\ToleranceConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\ToleranceConfig.vue", "mtime": 1758085667851}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["DeleteToleranceSetting", "GetToleranceSettingList", "props", "sysProjectId", "type", "String", "default", "data", "tbLoading", "tbData", "dialogVisible", "form", "Id", "Length", "Demand", "Type", "mounted", "getToleranceList", "methods", "_this", "then", "res", "IsSucceed", "Data", "map", "item", "Modify_Date", "Create_Date", "TypeTag", "$message", "message", "Message", "finally", "editEvent", "row", "JSON", "parse", "stringify", "$emit", "removeEvent", "_this2", "$confirm", "confirmButtonText", "cancelButtonText", "id", "catch"], "sources": ["src/views/PRO/project-config/project-quality/components/ToleranceConfig.vue"], "sourcesContent": ["<template>\n  <div style=\"height: calc(100vh - 300px)\">\n    <vxe-table\n      v-loading=\"tbLoading\"\n      :empty-render=\"{name: 'NotData'}\"\n      show-header-overflow\n      element-loading-spinner=\"el-icon-loading\"\n      element-loading-text=\"拼命加载中\"\n      empty-text=\"暂无数据\"\n      height=\"100%\"\n      align=\"left\"\n      stripe\n      :data=\"tbData\"\n      resizable\n      :auto-resize=\"true\"\n      class=\"cs-vxe-table\"\n      :tooltip-config=\"{ enterable: true }\"\n    >\n      <vxe-column\n        show-overflow=\"tooltip\"\n        sortable\n        field=\"Length\"\n        title=\"长度\"\n        min-width=\"150\"\n        align=\"center\"\n      >\n        <template #default=\"{ row }\">\n          <span>\n            {{ row.TypeTag }}\n            {{ row.Length }}\n          </span>\n        </template>\n      </vxe-column>\n      <vxe-column\n        show-overflow=\"tooltip\"\n        sortable\n        min-width=\"150\"\n        align=\"left\"\n        field=\"Demand\"\n        title=\"公差要求\"\n      />\n      <vxe-column\n        show-overflow=\"tooltip\"\n        sortable\n        field=\"Modify_Date\"\n        title=\"编辑时间\"\n        min-width=\"150\"\n        align=\"center\"\n      />\n      <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" align=\"center\" show-overflow>\n        <template #default=\"{ row }\">\n          <el-button type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\n          <el-divider direction=\"vertical\" />\n          <el-button type=\"text\" @click=\"removeEvent(row)\">删除</el-button>\n        </template>\n      </vxe-column>\n    </vxe-table>\n  </div>\n</template>\n\n<script>\n\nimport {\n  DeleteToleranceSetting,\n  GetToleranceSettingList\n} from '@/api/PRO/qualityInspect/quality-management'\n\nexport default {\n  props: {\n    sysProjectId: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      tbLoading: false,\n      tbData: [],\n      dialogVisible: false,\n      form: {\n        Id: '',\n        Length: 0,\n        Demand: '',\n        Type: 1\n      }\n\n    }\n  },\n  mounted() {\n    this.getToleranceList()\n  },\n  methods: {\n    getToleranceList() {\n      this.tbLoading = true\n      GetToleranceSettingList({ sysProjectId: this.sysProjectId }).then(res => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data.map(item => {\n            item.Modify_Date = item.Modify_Date || item.Create_Date\n            item.TypeTag = item.Type === 1 ? '<' : item.Type === 2 ? '<=' : item.Type === 3 ? '>' : item.Type === 4 ? '>=' : '='\n            return item\n          })\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      }).finally(() => {\n        this.tbLoading = false\n      })\n    },\n    editEvent(row) {\n      const form = JSON.parse(JSON.stringify(row))\n      this.$emit('edit', form)\n    },\n    removeEvent(row) {\n      this.$confirm('此操作将永久删除该公差配置, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DeleteToleranceSetting({ id: row.Id }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getToleranceList()\n            } else {\n              this.$message({\n                type: 'error',\n                message: res.Message\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    }\n\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA,SACAA,sBAAA,EACAC,uBAAA,QACA;AAEA;EACAC,KAAA;IACAC,YAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,MAAA;MACAC,aAAA;MACAC,IAAA;QACAC,EAAA;QACAC,MAAA;QACAC,MAAA;QACAC,IAAA;MACA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,gBAAA;EACA;EACAC,OAAA;IACAD,gBAAA,WAAAA,iBAAA;MAAA,IAAAE,KAAA;MACA,KAAAX,SAAA;MACAP,uBAAA;QAAAE,YAAA,OAAAA;MAAA,GAAAiB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAH,KAAA,CAAAV,MAAA,GAAAY,GAAA,CAAAE,IAAA,CAAAC,GAAA,WAAAC,IAAA;YACAA,IAAA,CAAAC,WAAA,GAAAD,IAAA,CAAAC,WAAA,IAAAD,IAAA,CAAAE,WAAA;YACAF,IAAA,CAAAG,OAAA,GAAAH,IAAA,CAAAV,IAAA,eAAAU,IAAA,CAAAV,IAAA,gBAAAU,IAAA,CAAAV,IAAA,eAAAU,IAAA,CAAAV,IAAA;YACA,OAAAU,IAAA;UACA;QACA;UACAN,KAAA,CAAAU,QAAA;YACAzB,IAAA;YACA0B,OAAA,EAAAT,GAAA,CAAAU;UACA;QACA;MACA,GAAAC,OAAA;QACAb,KAAA,CAAAX,SAAA;MACA;IACA;IACAyB,SAAA,WAAAA,UAAAC,GAAA;MACA,IAAAvB,IAAA,GAAAwB,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAH,GAAA;MACA,KAAAI,KAAA,SAAA3B,IAAA;IACA;IACA4B,WAAA,WAAAA,YAAAL,GAAA;MAAA,IAAAM,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAvC,IAAA;MACA,GACAgB,IAAA;QACApB,sBAAA;UAAA4C,EAAA,EAAAV,GAAA,CAAAtB;QAAA,GAAAQ,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAkB,MAAA,CAAAX,QAAA;cACAzB,IAAA;cACA0B,OAAA;YACA;YACAU,MAAA,CAAAvB,gBAAA;UACA;YACAuB,MAAA,CAAAX,QAAA;cACAzB,IAAA;cACA0B,OAAA,EAAAT,GAAA,CAAAU;YACA;UACA;QACA;MACA,GACAc,KAAA;QACAL,MAAA,CAAAX,QAAA;UACAzB,IAAA;UACA0B,OAAA;QACA;MACA;IACA;EAEA;AACA", "ignoreList": []}]}
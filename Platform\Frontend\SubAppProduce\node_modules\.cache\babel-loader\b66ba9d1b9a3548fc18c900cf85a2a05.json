{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\ToleranceConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\ToleranceConfig.vue", "mtime": 1758099106750}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["DeleteToleranceSetting", "GetToleranceSettingList", "props", "sysProjectId", "type", "String", "default", "data", "tbLoading", "tbData", "dialogVisible", "form", "Id", "Length", "Demand", "Type", "watch", "handler", "newVal", "getToleranceList", "immediate", "mounted", "methods", "_this", "then", "res", "IsSucceed", "Data", "map", "item", "Modify_Date", "Create_Date", "TypeTag", "$message", "message", "Message", "finally", "editEvent", "row", "JSON", "parse", "stringify", "$emit", "removeEvent", "_this2", "$confirm", "confirmButtonText", "cancelButtonText", "id", "catch"], "sources": ["src/views/PRO/project-config/project-quality/components/ToleranceConfig.vue"], "sourcesContent": ["<template>\n  <div style=\"height: calc(100vh - 300px)\">\n    <vxe-table\n      v-loading=\"tbLoading\"\n      :empty-render=\"{name: 'NotData'}\"\n      show-header-overflow\n      element-loading-spinner=\"el-icon-loading\"\n      element-loading-text=\"拼命加载中\"\n      empty-text=\"暂无数据\"\n      height=\"100%\"\n      align=\"left\"\n      stripe\n      :data=\"tbData\"\n      resizable\n      :auto-resize=\"true\"\n      class=\"cs-vxe-table\"\n      :tooltip-config=\"{ enterable: true }\"\n    >\n      <vxe-column\n        show-overflow=\"tooltip\"\n        sortable\n        field=\"Length\"\n        title=\"长度\"\n        min-width=\"150\"\n        align=\"center\"\n      >\n        <template #default=\"{ row }\">\n          <span>\n            {{ row.TypeTag }}\n            {{ row.Length }}\n          </span>\n        </template>\n      </vxe-column>\n      <vxe-column\n        show-overflow=\"tooltip\"\n        sortable\n        min-width=\"150\"\n        align=\"left\"\n        field=\"Demand\"\n        title=\"公差要求\"\n      />\n      <vxe-column\n        show-overflow=\"tooltip\"\n        sortable\n        field=\"Modify_Date\"\n        title=\"编辑时间\"\n        min-width=\"150\"\n        align=\"center\"\n      />\n      <!-- <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" align=\"center\" show-overflow>\n        <template #default=\"{ row }\">\n          <el-button type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\n          <el-divider direction=\"vertical\" />\n          <el-button type=\"text\" @click=\"removeEvent(row)\">删除</el-button>\n        </template>\n      </vxe-column> -->\n    </vxe-table>\n  </div>\n</template>\n\n<script>\n\nimport {\n  DeleteToleranceSetting,\n  GetToleranceSettingList\n} from '@/api/PRO/qualityInspect/quality-management'\n\nexport default {\n  props: {\n    sysProjectId: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      tbLoading: false,\n      tbData: [],\n      dialogVisible: false,\n      form: {\n        Id: '',\n        Length: 0,\n        Demand: '',\n        Type: 1\n      }\n\n    }\n  },\n  watch: {\n    sysProjectId: {\n      handler(newVal) {\n        if (newVal) {\n          this.getToleranceList()\n        }\n      },\n      immediate: true\n    }\n  },\n  mounted() {\n    // 移除直接调用，改为通过 watch 监听 sysProjectId\n  },\n  methods: {\n    getToleranceList() {\n      this.tbLoading = true\n      GetToleranceSettingList({ sysProjectId: this.sysProjectId }).then(res => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data.map(item => {\n            item.Modify_Date = item.Modify_Date || item.Create_Date\n            item.TypeTag = item.Type === 1 ? '<' : item.Type === 2 ? '<=' : item.Type === 3 ? '>' : item.Type === 4 ? '>=' : '='\n            return item\n          })\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      }).finally(() => {\n        this.tbLoading = false\n      })\n    },\n    editEvent(row) {\n      const form = JSON.parse(JSON.stringify(row))\n      this.$emit('edit', form)\n    },\n    removeEvent(row) {\n      this.$confirm('此操作将永久删除该公差配置, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DeleteToleranceSetting({ id: row.Id }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getToleranceList()\n            } else {\n              this.$message({\n                type: 'error',\n                message: res.Message\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    }\n\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA,SACAA,sBAAA,EACAC,uBAAA,QACA;AAEA;EACAC,KAAA;IACAC,YAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,MAAA;MACAC,aAAA;MACAC,IAAA;QACAC,EAAA;QACAC,MAAA;QACAC,MAAA;QACAC,IAAA;MACA;IAEA;EACA;EACAC,KAAA;IACAb,YAAA;MACAc,OAAA,WAAAA,QAAAC,MAAA;QACA,IAAAA,MAAA;UACA,KAAAC,gBAAA;QACA;MACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;EAAA,CACA;EACAC,OAAA;IACAH,gBAAA,WAAAA,iBAAA;MAAA,IAAAI,KAAA;MACA,KAAAf,SAAA;MACAP,uBAAA;QAAAE,YAAA,OAAAA;MAAA,GAAAqB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAH,KAAA,CAAAd,MAAA,GAAAgB,GAAA,CAAAE,IAAA,CAAAC,GAAA,WAAAC,IAAA;YACAA,IAAA,CAAAC,WAAA,GAAAD,IAAA,CAAAC,WAAA,IAAAD,IAAA,CAAAE,WAAA;YACAF,IAAA,CAAAG,OAAA,GAAAH,IAAA,CAAAd,IAAA,eAAAc,IAAA,CAAAd,IAAA,gBAAAc,IAAA,CAAAd,IAAA,eAAAc,IAAA,CAAAd,IAAA;YACA,OAAAc,IAAA;UACA;QACA;UACAN,KAAA,CAAAU,QAAA;YACA7B,IAAA;YACA8B,OAAA,EAAAT,GAAA,CAAAU;UACA;QACA;MACA,GAAAC,OAAA;QACAb,KAAA,CAAAf,SAAA;MACA;IACA;IACA6B,SAAA,WAAAA,UAAAC,GAAA;MACA,IAAA3B,IAAA,GAAA4B,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAH,GAAA;MACA,KAAAI,KAAA,SAAA/B,IAAA;IACA;IACAgC,WAAA,WAAAA,YAAAL,GAAA;MAAA,IAAAM,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACA3C,IAAA;MACA,GACAoB,IAAA;QACAxB,sBAAA;UAAAgD,EAAA,EAAAV,GAAA,CAAA1B;QAAA,GAAAY,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAkB,MAAA,CAAAX,QAAA;cACA7B,IAAA;cACA8B,OAAA;YACA;YACAU,MAAA,CAAAzB,gBAAA;UACA;YACAyB,MAAA,CAAAX,QAAA;cACA7B,IAAA;cACA8B,OAAA,EAAAT,GAAA,CAAAU;YACA;UACA;QACA;MACA,GACAc,KAAA;QACAL,MAAA,CAAAX,QAAA;UACA7B,IAAA;UACA8B,OAAA;QACA;MACA;IACA;EAEA;AACA", "ignoreList": []}]}
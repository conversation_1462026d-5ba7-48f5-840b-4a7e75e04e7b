{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\factory\\component\\Card.vue?vue&type=style&index=0&id=7b677bc5&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\factory\\component\\Card.vue", "mtime": 1758011160329}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmNhcmQtd3JhcHBlciB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIHdpZHRoOiBjYWxjKDI1JSAtIDE2cHgpOwogIG1pbi13aWR0aDogMzg3cHg7CiAgbWFyZ2luOiA4cHg7CiAgcGFkZGluZzogMjBweDsKICBiYWNrZ3JvdW5kOiAjRkZGRkZGOwogIGJveC1zaGFkb3c6IDBweCAycHggOHB4IHJnYmEoMjAsIDM1LCA3OCwgMC4wOCk7CiAgYm9yZGVyLXJhZGl1czogOHB4OwoKICAudG9wLWJveCB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIC50LWJveC1idG4gewogICAgICBhbGlnbi1zZWxmOiBmbGV4LXN0YXJ0OwogICAgICBmbGV4OiAwIDEgMTMwcHg7CiAgICB9CiAgICAudC1ib3ggewogICAgICBmbGV4OiAxOwogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBhbGlnbi1pdGVtczogY2VudGVyOwoKICAgICAgLmljb25mb250IHsKICAgICAgICBhbGlnbi1zZWxmOiBmbGV4LXN0YXJ0OwogICAgICAgIGZvbnQtc2l6ZTogMjBweDsKICAgICAgICBjb2xvcjogIzI5OERGRjsKICAgICAgICBtYXJnaW4tcmlnaHQ6IDhweDsKICAgICAgfQoKICAgICAgLnRpdGxlIHsKICAgICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7CiAgICAgICAgZm9udC1zaXplOiAxOHB4OwogICAgICB9CiAgICB9CgogIH0KCiAgLnN1Yi10aXRsZSB7CiAgICBtYXJnaW4tdG9wOiA4cHg7CiAgICBoZWlnaHQ6IDE5cHg7CiAgICBmb250LXNpemU6IDE0cHg7CiAgICBsaW5lLWhlaWdodDogMjJweDsKICAgIGNvbG9yOiByZ2JhKDM0LCA0MCwgNTIsIDAuNjUpOwogIH0KCiAgLnRhZy1jb250YWluZXIgewogICAgbWFyZ2luLXRvcDogMjJweDsKCiAgICAudGFnLWJveCB7CiAgICAgIGhlaWdodDogMjRweDsKICAgICAgbGluZS1oZWlnaHQ6IDI0cHg7CiAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICAgICAgcGFkZGluZzogMnB4IDZweDsKICAgICAgbWFyZ2luOiA0cHggOHB4OwogICAgICBjb2xvcjogIzgxOEZCNzsKICAgICAgYmFja2dyb3VuZDogcmdiYSgxMjksIDE0MywgMTgzLCAwLjEyKTsKICAgICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgfQogIH0KCiAgLmNzLWJvdHRvbSB7CiAgICBtYXJnaW4tdG9wOiAxOXB4OwoKICAgIC5jcy1sYWJlbCB7CiAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgY29sb3I6IHJnYmEoMzQsIDQwLCA1MiwgMC40KTsKICAgIH0KCiAgICAudGl0bGUgewogICAgICBjb2xvcjogIzIyMjgzNDsKICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogICAgICBtYXJnaW46IDhweCA0cHg7CgogICAgICAuaWNvbi1ob21lMyB7CiAgICAgICAgbWFyZ2luLXJpZ2h0OiAycHg7CiAgICAgIH0KICAgIH0KICB9Cgp9Cg=="}, {"version": 3, "sources": ["Card.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA", "file": "Card.vue", "sourceRoot": "src/views/PRO/basic-information/factory/component", "sourcesContent": ["<template>\n  <div class=\"card-wrapper\">\n    <div class=\"top-box\">\n      <div class=\"t-box\">\n        <i class=\"iconfont icon-steel\" />\n        <strong class=\"title\">{{ item.Short_Name }}</strong>\n      </div>\n      <div class=\"t-box-btn\">\n        <el-button v-if=\"true\" size=\"mini\" type=\"danger\" @click=\"handleDelete()\">删 除</el-button>\n        <el-button size=\"mini\" @click=\"handleEdit\">编 辑</el-button>\n      </div>\n    </div>\n    <div class=\"sub-title\">\n      {{ item.Name }}\n    </div>\n\n    <div class=\"tag-container\">\n      <span class=\"tag-box\">{{ item.Category }}</span>\n    </div>\n\n    <!--    <div class=\"cs-bottom\">\n      <div class=\"cs-label\">对接仓库：</div>\n      <span v-for=\"(item,index) in WarehouseList\" :key=\"index\" class=\"title\">\n        <i class=\"iconfont icon-home3\" />\n        <span>{{ item }}</span>\n      </span>\n    </div>-->\n  </div>\n</template>\n\n<script>\nexport default {\n  props: {\n    item: {\n      type: Object,\n      default: () => {\n      }\n    }\n  },\n  computed: {\n    WarehouseList() {\n      if (this.item.Warehouse_Name && this.item.Warehouse_Name.length) {\n        return this.item.Warehouse_Name.split(',')\n      } else {\n        return []\n      }\n    }\n  },\n  methods: {\n    handleEdit(row) {\n      this.$emit('edit', row)\n    },\n    handleDelete(row) {\n      this.$emit('delete', row)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.card-wrapper {\n  display: flex;\n  flex-direction: column;\n  width: calc(25% - 16px);\n  min-width: 387px;\n  margin: 8px;\n  padding: 20px;\n  background: #FFFFFF;\n  box-shadow: 0px 2px 8px rgba(20, 35, 78, 0.08);\n  border-radius: 8px;\n\n  .top-box {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    .t-box-btn {\n      align-self: flex-start;\n      flex: 0 1 130px;\n    }\n    .t-box {\n      flex: 1;\n      display: flex;\n      align-items: center;\n\n      .iconfont {\n        align-self: flex-start;\n        font-size: 20px;\n        color: #298DFF;\n        margin-right: 8px;\n      }\n\n      .title {\n        display: inline-block;\n        font-size: 18px;\n      }\n    }\n\n  }\n\n  .sub-title {\n    margin-top: 8px;\n    height: 19px;\n    font-size: 14px;\n    line-height: 22px;\n    color: rgba(34, 40, 52, 0.65);\n  }\n\n  .tag-container {\n    margin-top: 22px;\n\n    .tag-box {\n      height: 24px;\n      line-height: 24px;\n      display: inline-block;\n      padding: 2px 6px;\n      margin: 4px 8px;\n      color: #818FB7;\n      background: rgba(129, 143, 183, 0.12);\n      border-radius: 4px;\n    }\n  }\n\n  .cs-bottom {\n    margin-top: 19px;\n\n    .cs-label {\n      font-size: 14px;\n      color: rgba(34, 40, 52, 0.4);\n    }\n\n    .title {\n      color: #222834;\n      display: inline-block;\n      margin: 8px 4px;\n\n      .icon-home3 {\n        margin-right: 2px;\n      }\n    }\n  }\n\n}\n</style>\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\RecognitionConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\RecognitionConfig.vue", "mtime": 1757473454985}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IEdldEJPTUluZm8gfSBmcm9tICdAL3ZpZXdzL1BSTy9ib20tc2V0dGluZy91dGlscycKaW1wb3J0IGNvbXBSZWNvZ25pdGlvbkNvbmZpZyBmcm9tICcuL2NvbXBSZWNvZ25pdGlvbkNvbmZpZycKaW1wb3J0IHBhcnRSZWNvZ25pdGlvbkNvbmZpZyBmcm9tICcuL3BhcnRSZWNvZ25pdGlvbkNvbmZpZycKaW1wb3J0IHVuaXRQYXJ0UmVjb2duaXRpb25Db25maWcgZnJvbSAnLi91bml0UGFydFJlY29nbml0aW9uQ29uZmlnJwppbXBvcnQgeyBudW1iZXIgfSBmcm9tICdlY2hhcnRzJwoKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsKICAgIGNvbXBSZWNvZ25pdGlvbkNvbmZpZywKICAgIHBhcnRSZWNvZ25pdGlvbkNvbmZpZywKICAgIHVuaXRQYXJ0UmVjb2duaXRpb25Db25maWcKICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBib21MZXZlbDogW10sCiAgICAgIGJvbUFjdGl2ZU5hbWU6ICcnLAogICAgICBidG5Mb2FkaW5nOiBmYWxzZQogICAgfQogIH0sCiAgYXN5bmMgbW91bnRlZCgpIHsKICAgIHRoaXMuYm9tTGV2ZWwgPSBhd2FpdCBHZXRCT01JbmZvKCkKICAgIHRoaXMuYm9tQWN0aXZlTmFtZSA9IHRoaXMuYm9tTGV2ZWwubGlzdFswXS5Db2RlCiAgICBjb25zb2xlLmxvZygndGhpcy5ib21MZXZlbCcsIHRoaXMuYm9tTGV2ZWwpCiAgfSwKICBtZXRob2RzOiB7CiAgICBoYW5kbGVDbG9zZSgpIHsKICAgICAgdGhpcy4kZW1pdCgnY2xvc2UnKQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["RecognitionConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAgBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RecognitionConfig.vue", "sourceRoot": "src/views/PRO/process-settings/management/component", "sourcesContent": ["<template>\n  <div class=\"form-recognition-wrapper\">\n    <div class=\"form-recognition-tabs\">\n      <el-tabs v-model=\"bomActiveName\">\n        <el-tab-pane v-for=\"(item, index) in bomLevel.list\" :key=\"index\" :label=\"item.Display_Name\" :name=\"item.Code\" />\n      </el-tabs>\n    </div>\n    <div>\n      <comp-recognition-config v-if=\"bomActiveName === '-1'\" @close=\"handleClose\" />\n      <part-recognition-config v-else-if=\"bomActiveName === '0'\" @close=\"handleClose\" />\n      <unit-part-recognition-config v-else :level=\"number(bomActiveName)\" @close=\"handleClose\" />\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport compRecognitionConfig from './compRecognitionConfig'\nimport partRecognitionConfig from './partRecognitionConfig'\nimport unitPartRecognitionConfig from './unitPartRecognitionConfig'\nimport { number } from 'echarts'\n\nexport default {\n  components: {\n    compRecognitionConfig,\n    partRecognitionConfig,\n    unitPartRecognitionConfig\n  },\n  data() {\n    return {\n      bomLevel: [],\n      bomActiveName: '',\n      btnLoading: false\n    }\n  },\n  async mounted() {\n    this.bomLevel = await GetBOMInfo()\n    this.bomActiveName = this.bomLevel.list[0].Code\n    console.log('this.bomLevel', this.bomLevel)\n  },\n  methods: {\n    handleClose() {\n      this.$emit('close')\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"~@/styles/mixin.scss\";\n.form-recognition-wrapper {\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  max-height: 70vh;\n  .form-recognition-tabs {\n\n  }\n}\n</style>\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\RecognitionConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\RecognitionConfig.vue", "mtime": 1758157160305}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IEdldEJPTUluZm8gfSBmcm9tICdAL3ZpZXdzL1BSTy9ib20tc2V0dGluZy91dGlscycKaW1wb3J0IGNvbXBSZWNvZ25pdGlvbkNvbmZpZyBmcm9tICcuL2NvbXBSZWNvZ25pdGlvbkNvbmZpZycKaW1wb3J0IHBhcnRSZWNvZ25pdGlvbkNvbmZpZyBmcm9tICcuL3BhcnRSZWNvZ25pdGlvbkNvbmZpZycKaW1wb3J0IHVuaXRQYXJ0UmVjb2duaXRpb25Db25maWcgZnJvbSAnLi91bml0UGFydFJlY29nbml0aW9uQ29uZmlnJwoKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsKICAgIGNvbXBSZWNvZ25pdGlvbkNvbmZpZywKICAgIHBhcnRSZWNvZ25pdGlvbkNvbmZpZywKICAgIHVuaXRQYXJ0UmVjb2duaXRpb25Db25maWcKICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBib21MaXN0OiBbXSwKICAgICAgY29tTmFtZTogJycsCiAgICAgIHBhcnROYW1lOiAnJywKICAgICAgYm9tQWN0aXZlTmFtZTogJycsCiAgICAgIGJ0bkxvYWRpbmc6IGZhbHNlCiAgICB9CiAgfSwKICBhc3luYyBtb3VudGVkKCkgewogICAgY29uc3QgeyBjb21OYW1lLCBwYXJ0TmFtZSwgbGlzdCB9ID0gYXdhaXQgR2V0Qk9NSW5mbygpCiAgICB0aGlzLmNvbU5hbWUgPSBjb21OYW1lCiAgICB0aGlzLnBhcnROYW1lID0gcGFydE5hbWUKICAgIHRoaXMuYm9tTGlzdCA9IGxpc3QKICAgIHRoaXMuYm9tQWN0aXZlTmFtZSA9IGxpc3RbMF0uQ29kZQogIH0sCiAgbWV0aG9kczogewogICAgaGFuZGxlQ2xvc2UoKSB7CiAgICAgIHRoaXMuJGVtaXQoJ2Nsb3NlJykKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["RecognitionConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;AAcA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RecognitionConfig.vue", "sourceRoot": "src/views/PRO/process-settings/management/component", "sourcesContent": ["<template>\n  <div class=\"form-recognition-wrapper\">\n    <div class=\"form-recognition-tabs\">\n      <el-tabs v-model=\"bomActiveName\">\n        <el-tab-pane v-for=\"(item, index) in bomList\" :key=\"index\" :label=\"item.Display_Name\" :name=\"item.Code\" />\n      </el-tabs>\n    </div>\n    <comp-recognition-config v-if=\"bomActiveName === '-1'\" :bom-list=\"bomList\" :level=\"Number(bomActiveName)\" @close=\"handleClose\" />\n    <part-recognition-config v-if=\"bomActiveName === '0'\" :bom-list=\"bomList\" :level=\"Number(bomActiveName)\" @close=\"handleClose\" />\n    <unit-part-recognition-config v-if=\"bomActiveName !== '-1' && bomActiveName !== '0'\" :bom-list=\"bomList\" :level=\"Number(bomActiveName)\" @close=\"handleClose\" />\n  </div>\n</template>\n\n<script>\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport compRecognitionConfig from './compRecognitionConfig'\nimport partRecognitionConfig from './partRecognitionConfig'\nimport unitPartRecognitionConfig from './unitPartRecognitionConfig'\n\nexport default {\n  components: {\n    compRecognitionConfig,\n    partRecognitionConfig,\n    unitPartRecognitionConfig\n  },\n  data() {\n    return {\n      bomList: [],\n      comName: '',\n      partName: '',\n      bomActiveName: '',\n      btnLoading: false\n    }\n  },\n  async mounted() {\n    const { comName, partName, list } = await GetBOMInfo()\n    this.comName = comName\n    this.partName = partName\n    this.bomList = list\n    this.bomActiveName = list[0].Code\n  },\n  methods: {\n    handleClose() {\n      this.$emit('close')\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n  .form-recognition-wrapper {\n    display: flex;\n    flex-direction: column;\n    overflow: hidden;\n    max-height: 70vh;\n    .form-recognition-tabs {\n\n    }\n  }\n</style>\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\TypeDialog.vue?vue&type=template&id=2efa32be&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\TypeDialog.vue", "mtime": 1757921826136}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXY+CiAgPGVsLWZvcm0gcmVmPSJmb3JtIiA6cnVsZXM9InJ1bGVzIiA6bW9kZWw9ImZvcm0iIGxhYmVsLXdpZHRoPSI4MHB4Ij4KICAgIDxlbC1yb3c+CiAgICAgIDxlbC1jb2wgOnNwYW49IjI0Ij4KICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmo4Dmn6XnsbvlnosiIHByb3A9Ik5hbWUiPgogICAgICAgICAgPGVsLWlucHV0IHYtbW9kZWw9ImZvcm0uTmFtZSIgLz4KICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgPC9lbC1jb2w+CiAgICAgIDxlbC1jb2wgOnNwYW49IjI0Ij4KICAgICAgICA8ZWwtZm9ybS1pdGVtIHN0eWxlPSJ0ZXh0LWFsaWduOiByaWdodCI+CiAgICAgICAgICA8ZWwtYnV0dG9uIEBjbGljaz0iJGVtaXQoJ2Nsb3NlJykiPuWFsyDpl608L2VsLWJ1dHRvbj4KICAgICAgICAgIDxlbC1idXR0b24KICAgICAgICAgICAgdHlwZT0icHJpbWFyeSIKICAgICAgICAgICAgQGNsaWNrPSJoYW5kbGVTdWJtaXQoJ2Zvcm0nKSIKICAgICAgICAgID7noa4g5a6aPC9lbC1idXR0b24+CiAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgIDwvZWwtY29sPgogICAgPC9lbC1yb3c+CiAgPC9lbC1mb3JtPgo8L2Rpdj4K"}, null]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\detail.vue", "mtime": 1757909680922}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRHcmlkQnlDb2RlIH0gZnJvbSAnQC9hcGkvc3lzJw0KaW1wb3J0IHsgRklYX0NPTFVNTiB9IGZyb20gJ0Avdmlld3MvUFJPL3BsYW4tcHJvZHVjdGlvbi9zY2hlZHVsZS1wcm9kdWN0aW9uL2NvbnN0YW50Jw0KaW1wb3J0IHsNCiAgQWRqdXN0VGVhbVByb2Nlc3NBbGxvY2F0aW9uLA0KICBHZXRUZWFtUHJvY2Vzc0FsbG9jYXRpb24sDQogIEFkanVzdFBhcnRUZWFtUHJvY2Vzc0FsbG9jYXRpb24sDQogIEFkanVzdFN1YkFzc2VtYmx5VGVhbVByb2Nlc3NBbGxvY2F0aW9uLA0KICBHZXRTdG9wTGlzdCwNCiAgR2V0UHJlU3RlcFRhc2tBbGxvY2F0aW9uDQp9IGZyb20gJ0AvYXBpL1BSTy9wcm9kdWN0aW9uLXRhc2snDQppbXBvcnQgUXJjb2RlVnVlIGZyb20gJ3FyY29kZS52dWUnDQppbXBvcnQgeyB2NCBhcyB1dWlkdjQgfSBmcm9tICd1dWlkJw0KaW1wb3J0IG51bWVyYWwgZnJvbSAnbnVtZXJhbCcNCmltcG9ydCB7IGNsb3NlVGFnVmlldywgZGVlcENsb25lIH0gZnJvbSAnQC91dGlscycNCmltcG9ydCB7IEdldENvbXBUeXBlVHJlZSB9IGZyb20gJ0AvYXBpL1BSTy9wcm9mZXNzaW9uYWxUeXBlJw0KDQpjb25zdCBTUExJVF9TWU1CT0wgPSAnJF8kJw0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7DQogICAgUXJjb2RlVnVlDQogIH0sDQogIGZpbHRlcnM6IHsNCiAgICBmaWx0ZXJOdW0odmFsdWUpIHsNCiAgICAgIHJldHVybiBudW1lcmFsKHZhbHVlKS5kaXZpZGUoMTAwMCkuZm9ybWF0KCcwLlswMF0nKQ0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgdHJlZVNlbGVjdFBhcmFtczogew0KICAgICAgICBwbGFjZWhvbGRlcjogJ+ivt+mAieaLqScsDQogICAgICAgIGNsZWFyYWJsZTogdHJ1ZQ0KICAgICAgfSwNCiAgICAgIE9iamVjdFR5cGVMaXN0OiB7DQogICAgICAgIC8vIOaehOS7tuexu+Weiw0KICAgICAgICAnY2hlY2stc3RyaWN0bHknOiB0cnVlLA0KICAgICAgICAnZGVmYXVsdC1leHBhbmQtYWxsJzogdHJ1ZSwNCiAgICAgICAgY2xpY2tQYXJlbnQ6IHRydWUsDQogICAgICAgIGRhdGE6IFtdLA0KICAgICAgICBwcm9wczogew0KICAgICAgICAgIGNoaWxkcmVuOiAnQ2hpbGRyZW4nLA0KICAgICAgICAgIGxhYmVsOiAnTGFiZWwnLA0KICAgICAgICAgIHZhbHVlOiAnRGF0YScNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIHRiTG9hZGluZzogZmFsc2UsDQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIGFjdGl2ZU5hbWU6ICdmaXJzdCcsDQogICAgICB0aXBMYWJlbDogJycsDQogICAgICB0YkRhdGE6IFtdLA0KICAgICAgZmlsdGVyVGJEYXRhOiBbXSwNCiAgICAgIG11bHRpcGxlU2VsZWN0aW9uOiBbXSwNCiAgICAgIGNvbHVtbnM6IFtdLA0KICAgICAgd29ya2luZ1RlYW06IFtdLA0KICAgICAgd29ya2luZ1RlYW1Db2x1bW46IFtdLA0KICAgICAgZm9ybUlubGluZToge30sDQogICAgICBwZ190eXBlOiAnJywNCiAgICAgIHNlYXJjaFR5cGU6ICcnLA0KICAgICAgdHlwZTogJycsDQogICAgICBxdWVyeUZvcm06IHsNCiAgICAgICAgQ29tcF9Db2RlczogJycsDQogICAgICAgIFBhcnRfQ29kZTogJycsDQogICAgICAgIFNwZWM6ICcnLA0KICAgICAgICBDb21wX0NvZGVzX1ZhZ3VlOiAnJywNCiAgICAgICAgUGFydF9Db2RlX1ZhZ3VlOiAnJw0KICAgICAgfSwNCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgZGlhbG9nVGlwc1Zpc2libGU6IGZhbHNlLA0KICAgICAgZm9ybTogew0KICAgICAgICBUZWFtR3JvdXA6ICcnIC8vIOePree7hA0KICAgICAgfSwNCg0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgVGVhbUdyb3VwOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeePree7hOWQjeensCcsIHRyaWdnZXI6ICdjaGFuZ2UnIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIElzX1dvcmtzaG9wX0VuYWJsZWQ6IGZhbHNlLA0KICAgICAgV29ya2luZ19Qcm9jZXNzX0lkOiAnJw0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICBpc1ZpZXcoKSB7DQogICAgICByZXR1cm4gdGhpcy50eXBlID09PSAndmlldycNCiAgICB9LA0KICAgIGlzQ29tKCkgew0KICAgICAgcmV0dXJuIHRoaXMucGdfdHlwZSA9PT0gJ2NvbScNCiAgICB9LA0KICAgIGlzVW5pdFBhcnQoKSB7DQogICAgICByZXR1cm4gdGhpcy5wZ190eXBlID09PSAndW5pdFBhcnQnDQogICAgfQ0KICB9LA0KDQogIGFzeW5jIG1vdW50ZWQoKSB7DQogICAgdHJ5IHsNCiAgICAgIGNvbnN0IHJvd0luZm8gPSBKU09OLnBhcnNlKGRlY29kZVVSSUNvbXBvbmVudCh0aGlzLiRyb3V0ZS5xdWVyeS5vdGhlcikpDQogICAgICBjb25zb2xlLmxvZygncm93SW5mbycsIHJvd0luZm8pDQogICAgICB0aGlzLmZvcm1JbmxpbmUgPSBPYmplY3QuYXNzaWduKHt9LCB0aGlzLmZvcm1JbmxpbmUsIHJvd0luZm8pDQogICAgICB0aGlzLnBnX3R5cGUgPSB0aGlzLiRyb3V0ZS5xdWVyeS5wZ190eXBlDQogICAgICB0aGlzLmJvbUxldmVsID0gdGhpcy4kcm91dGUucXVlcnkuYm9tTGV2ZWwNCiAgICAgIHRoaXMudHlwZSA9IHRoaXMuJHJvdXRlLnF1ZXJ5LnR5cGUNCiAgICAgIHRoaXMuSXNfV29ya3Nob3BfRW5hYmxlZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LklzX1dvcmtzaG9wX0VuYWJsZWQNCiAgICAgIGF3YWl0IHRoaXMuZ2V0VGFibGVDb25maWcodGhpcy5pc1VuaXRQYXJ0ID8gJ1BST1Rhc2tVbml0QWxsb2NhdGlvbkNoYW5nZScgOiAnUFJPVGFza0FsbG9jYXRpb25DaGFuZ2UnKQ0KICAgICAgaWYgKHRoaXMuaXNDb20pIHsNCiAgICAgICAgY29uc3QgaWR4ID0gdGhpcy5jb2x1bW5zLmZpbmRJbmRleChpdGVtID0+IGl0ZW0uQ29kZSA9PT0gJ1BhcnRfQ29kZScpDQogICAgICAgIGlkeCAhPT0gLTEgJiYgdGhpcy5jb2x1bW5zLnNwbGljZShpZHgsIDEpDQogICAgICAgIGNvbnN0IGlkeDIgPSB0aGlzLmNvbHVtbnMuZmluZEluZGV4KGl0ZW0gPT4gaXRlbS5Db2RlID09PSAnQ29tcG9uZW50X0NvZGUnKQ0KICAgICAgICBpZHgyICE9PSAtMSAmJiB0aGlzLmNvbHVtbnMuc3BsaWNlKGlkeDIsIDEpDQogICAgICB9IGVsc2Ugew0KICAgICAgICBjb25zdCBpZHggPSB0aGlzLmNvbHVtbnMuZmluZEluZGV4KGl0ZW0gPT4gaXRlbS5Db2RlID09PSAnQ29tcF9Db2RlJykNCiAgICAgICAgaWR4ICE9PSAtMSAmJiB0aGlzLmNvbHVtbnMuc3BsaWNlKGlkeCwgMSkNCiAgICAgICAgY29uc3QgaWR4MiA9IHRoaXMuY29sdW1ucy5maW5kSW5kZXgoaXRlbSA9PiBpdGVtLkNvZGUgPT09ICdUeXBlJykNCiAgICAgICAgaWR4MiAhPT0gLTEgJiYgdGhpcy5jb2x1bW5zLnNwbGljZShpZHgyLCAxKQ0KICAgICAgfQ0KICAgICAgaWYgKCF0aGlzLklzX1dvcmtzaG9wX0VuYWJsZWQpIHsNCiAgICAgICAgY29uc3QgaWR4MyA9IHRoaXMuY29sdW1ucy5maW5kSW5kZXgoaXRlbSA9PiBpdGVtLkNvZGUgPT09ICdXb3Jrc2hvcF9OYW1lJykNCiAgICAgICAgaWR4MyAhPT0gLTEgJiYgdGhpcy5jb2x1bW5zLnNwbGljZShpZHgzLCAxKQ0KICAgICAgfQ0KICAgICAgdGhpcy5nZXRPYmplY3RUeXBlTGlzdCgpDQogICAgICB0aGlzLmZldGNoRGF0YSgpDQogICAgfSBjYXRjaCAoZSkgew0KICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgIG1lc3NhZ2U6ICflj4LmlbDplJnor68s6K+36YeN5paw5pON5L2cJywNCiAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgfSkNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBnZXRSb3dDQ29kZShyb3csIHByZWZpeCA9ICcnLCBzdWZmaXggPSAnJykgew0KICAgICAgaWYgKHRoaXMuYWN0aXZlTmFtZSA9PT0gJ2ZpcnN0Jykgew0KICAgICAgICByZXR1cm4gJ2FsbG9jYXRlZFRhc2snICsgc3VmZml4DQogICAgICB9IGVsc2Ugew0KICAgICAgICBjb25zdCBhcnIgPSB0aGlzLmFjdGl2ZU5hbWUuc3BsaXQoU1BMSVRfU1lNQk9MKQ0KICAgICAgICBjb25zdCB0ZWFtID0gdGhpcy53b3JraW5nVGVhbS5maW5kKHYgPT4gdi5Xb3JraW5nX1RlYW1fTmFtZSA9PT0gYXJyWzBdKQ0KICAgICAgICBjb25zdCB1ID0gdGhpcy5nZXRSb3dVbmlxdWUocm93LnV1aWQsIHJvdy5Qcm9jZXNzX0NvZGUsIHRlYW0uV29ya2luZ19UZWFtX0lkKQ0KICAgICAgICBpZiAoc3VmZml4ID09PSAnTWF4Jykgew0KICAgICAgICAgIHJldHVybiB1DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgcmV0dXJuIHByZWZpeCArIHUgKyBzdWZmaXgNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlQ2xpY2sodmFsKSB7DQogICAgICBjb25zb2xlLmxvZygnaGFuZGxlQ2xpY2snLCB2YWwpDQogICAgICBpZiAodmFsLm5hbWUgPT09ICdmaXJzdCcpIHsNCiAgICAgICAgY29uc3QgYzEgPSB0aGlzLiRyZWZzLnhUYWJsZS5nZXRDb2x1bW5CeUZpZWxkKCdTY2hkdWxlZF9Db3VudCcpDQogICAgICAgIGNvbnN0IGMyID0gdGhpcy4kcmVmcy54VGFibGUuZ2V0Q29sdW1uQnlGaWVsZCgnQ2FuX0FsbG9jYXRpb25fQ291bnQnKQ0KICAgICAgICBjMS52aXNpYmxlID0gZmFsc2UNCiAgICAgICAgYzIudmlzaWJsZSA9IHRydWUNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNvbnN0IGMyID0gdGhpcy4kcmVmcy54VGFibGUuZ2V0Q29sdW1uQnlGaWVsZCgnU2NoZHVsZWRfQ291bnQnKQ0KICAgICAgICBjb25zdCBjMyA9IHRoaXMuJHJlZnMueFRhYmxlLmdldENvbHVtbkJ5RmllbGQoJ0Nhbl9BbGxvY2F0aW9uX0NvdW50JykNCiAgICAgICAgYzIudmlzaWJsZSA9IHRydWUNCiAgICAgICAgYzMudmlzaWJsZSA9IGZhbHNlDQogICAgICB9DQoNCiAgICAgIHRoaXMud29ya2luZ1RlYW0uZm9yRWFjaCgoZWxlbWVudCwgaWR4KSA9PiB7DQogICAgICAgIGNvbnN0IGNvZGVzID0gYCR7ZWxlbWVudC5Xb3JraW5nX1RlYW1fTmFtZX0kXyQke2VsZW1lbnQuUHJvY2Vzc19Db2RlfWANCg0KICAgICAgICBjb25zdCBjID0gdGhpcy4kcmVmcy54VGFibGUuZ2V0Q29sdW1uQnlGaWVsZChjb2RlcykNCg0KICAgICAgICBjLnZpc2libGUgPSBjb2RlcyA9PT0gdmFsLm5hbWUNCiAgICAgIH0pDQogICAgICB0aGlzLiRyZWZzLnhUYWJsZS5yZWZyZXNoQ29sdW1uKCkNCg0KICAgICAgdGhpcy5maWx0ZXJUYkRhdGEgPSB0aGlzLnRiRGF0YS5maWx0ZXIocm93ID0+IHsNCiAgICAgICAgcmV0dXJuIHRoaXMuZmlsdGVyWmVybyhyb3cpDQogICAgICB9KQ0KDQogICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gW10NCiAgICAgIHRoaXMuJHJlZnMueFRhYmxlLmNsZWFyQ2hlY2tib3hSb3coKQ0KICAgIH0sDQogICAgZmlsdGVyWmVybyhyb3cpIHsNCiAgICAgIGlmICh0aGlzLmFjdGl2ZU5hbWUgPT09ICdmaXJzdCcpIHsNCiAgICAgICAgcmV0dXJuIHJvdy5hbGxvY2F0ZWRUYXNrID4gMA0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgY29uc3QgYXJyID0gdGhpcy5hY3RpdmVOYW1lLnNwbGl0KFNQTElUX1NZTUJPTCkNCiAgICAgICAgY29uc3QgdGVhbSA9IHRoaXMud29ya2luZ1RlYW0uZmluZCh2ID0+IHYuV29ya2luZ19UZWFtX05hbWUgPT09IGFyclswXSkNCiAgICAgICAgY29uc3QgY29kZSA9IHRoaXMuZ2V0Um93VW5pcXVlKHJvdy51dWlkLCB0ZWFtLlByb2Nlc3NfQ29kZSwgdGVhbS5Xb3JraW5nX1RlYW1fSWQpDQogICAgICAgIHJldHVybiByb3dbY29kZV0gPiAwDQogICAgICB9DQogICAgfSwNCiAgICBmZXRjaERhdGEoKSB7DQogICAgICBjb25zdCBDb21wX0NvZGVzID0gIXRoaXMucXVlcnlGb3JtLkNvbXBfQ29kZXMgPyBbXSA6IHRoaXMucXVlcnlGb3JtLkNvbXBfQ29kZXMudHJpbSgpLnNwbGl0KCcgJykNCiAgICAgIGNvbnN0IFBhcnRfQ29kZSA9ICF0aGlzLnF1ZXJ5Rm9ybS5QYXJ0X0NvZGUgPyBbXSA6IHRoaXMucXVlcnlGb3JtLlBhcnRfQ29kZS50cmltKCkuc3BsaXQoJyAnKQ0KICAgICAgbGV0IFByb2Nlc3NfVHlwZSA9IDINCiAgICAgIFByb2Nlc3NfVHlwZSA9IHRoaXMuaXNDb20gPyAyIDogdGhpcy5pc1VuaXRQYXJ0ID8gMyA6IDENCiAgICAgIHRoaXMudGJMb2FkaW5nID0gdHJ1ZQ0KICAgICAgR2V0VGVhbVByb2Nlc3NBbGxvY2F0aW9uKHsNCiAgICAgICAgUGFnZTogMSwNCiAgICAgICAgUGFnZVNpemU6IC0xLA0KICAgICAgICBTdGVwOiB0aGlzLmZvcm1JbmxpbmUuU3RlcCwNCiAgICAgICAgUHJvY2Vzc19UeXBlLA0KICAgICAgICBCb21fTGV2ZWw6IHRoaXMuYm9tTGV2ZWwsDQogICAgICAgIFNjaGR1bGluZ19Db2RlOiB0aGlzLmZvcm1JbmxpbmUuU2NoZHVsaW5nX0NvZGUsDQogICAgICAgIFByb2Nlc3NfQ29kZTogdGhpcy5mb3JtSW5saW5lLlByb2Nlc3NfQ29kZSwNCiAgICAgICAgV29ya3Nob3BfTmFtZTogdGhpcy5mb3JtSW5saW5lLldvcmtzaG9wX05hbWUsDQogICAgICAgIEFyZWFfSWQ6IHRoaXMuZm9ybUlubGluZS5BcmVhX0lkLA0KICAgICAgICBJbnN0YWxsVW5pdF9JZDogdGhpcy5mb3JtSW5saW5lLkluc3RhbGxVbml0X0lkLA0KICAgICAgICBDb21wX0NvZGVzLA0KICAgICAgICBQYXJ0X0NvZGUNCiAgICAgIH0pLnRoZW4oYXN5bmMocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgY29uc3QgeyBTY2hkdWxpbmdfUGxhbiwgU2NoZHVsaW5nX0NvbXBzIH0gPSByZXMuRGF0YQ0KICAgICAgICAgIHRoaXMucGxhbkluZm9UZW1wID0gU2NoZHVsaW5nX1BsYW4NCiAgICAgICAgICBhd2FpdCB0aGlzLmdldFN0b3BMaXN0KFNjaGR1bGluZ19Db21wcykNCiAgICAgICAgICB0aGlzLmluaXRUYkRhdGEoU2NoZHVsaW5nX0NvbXBzKQ0KICAgICAgICAgIHRoaXMuV29ya2luZ19Qcm9jZXNzX0lkID0gcmVzLkRhdGEuV29ya2luZ19Qcm9jZXNzX0lkDQoNCiAgICAgICAgICBpZiAoU2NoZHVsaW5nX0NvbXBzLmxlbmd0aCkgew0KICAgICAgICAgICAgdGhpcy53b3JraW5nVGVhbSA9IFNjaGR1bGluZ19Db21wc1swXS5BbGxvY2F0aW9uX1RlYW1zDQogICAgICAgICAgICBjb25zdCBfa2sgPSB0aGlzLndvcmtpbmdUZWFtLm1hcCh2ID0+IHsNCiAgICAgICAgICAgICAgdi52aXNpYmxlID0gZmFsc2UNCiAgICAgICAgICAgICAgcmV0dXJuIHYNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICB0aGlzLndvcmtpbmdUZWFtQ29sdW1uID0gZGVlcENsb25lKF9raykNCiAgICAgICAgICB9DQogICAgICAgICAgY29uc29sZS5sb2coJyB0aGlzLnRiRGF0YScsIHRoaXMudGJEYXRhKQ0KICAgICAgICAgIHRoaXMuZmlsdGVyRGF0YSgpDQogICAgICAgICAgY29uc29sZS5sb2coJ2ZpbHRlclRiRGF0YScsIHRoaXMuZmlsdGVyVGJEYXRhKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkuZmluYWxseShfID0+IHsNCiAgICAgICAgdGhpcy50YkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGNoZWNrTWV0aG9kKHsgcm93IH0pIHsNCiAgICAgIHJldHVybiAhcm93LnN0b3BGbGFnDQogICAgfSwNCiAgICBhc3luYyBnZXRTdG9wTGlzdChsaXN0KSB7DQogICAgICBjb25zdCBrZXkgPSAnSWQnDQogICAgICBjb25zdCBzdWJtaXRPYmogPSBsaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICBJZDogaXRlbVtrZXldLA0KICAgICAgICAgIEJvbV9MZXZlbDogdGhpcy5ib21MZXZlbCwNCiAgICAgICAgICBUeXBlOiB0aGlzLmlzQ29tID8gMiA6IHRoaXMuaXNVbml0UGFydCA/IDMgOiAxIC8vIDHvvJrpm7bku7bvvIwz77ya6YOo5Lu277yMMu+8muaehOS7tg0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgYXdhaXQgR2V0U3RvcExpc3Qoc3VibWl0T2JqKS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgY29uc3Qgc3RvcE1hcCA9IHt9DQogICAgICAgICAgcmVzLkRhdGEuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICAgIHN0b3BNYXBbaXRlbS5JZF0gPSAhIWl0ZW0uSXNfU3RvcA0KICAgICAgICAgIH0pDQogICAgICAgICAgbGlzdC5mb3JFYWNoKHJvdyA9PiB7DQogICAgICAgICAgICBpZiAoc3RvcE1hcFtyb3dba2V5XV0pIHsNCiAgICAgICAgICAgICAgdGhpcy4kc2V0KHJvdywgJ3N0b3BGbGFnJywgc3RvcE1hcFtyb3dba2V5XV0pDQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGZpbHRlckRhdGEoKSB7DQogICAgICBjb25zb2xlLmxvZygnc2VhcmNoVHlwZScsIHRoaXMuc2VhcmNoVHlwZSkNCiAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24gPSBbXQ0KICAgICAgdGhpcy4kcmVmcy54VGFibGUuY2xlYXJDaGVja2JveFJvdygpDQogICAgICBjb25zdCBjb2RlID0gdGhpcy5pc0NvbSA/ICdDb21wX0NvZGUnIDogJ1BhcnRfQ29kZScNCiAgICAgIGNvbnN0IHF1ZXJ5Q29kZSA9IHRoaXMuaXNDb20gPyAnQ29tcF9Db2RlcycgOiAnUGFydF9Db2RlJw0KICAgICAgY29uc3QgY29kZUxpc3QgPSB0aGlzLnF1ZXJ5Rm9ybVtxdWVyeUNvZGVdLnNwbGl0KCcgJykuZmlsdGVyKHYgPT4gISF2KQ0KDQogICAgICBjb25zdCBxdWVyeUNvZGVWYWd1ZSA9IHRoaXMuaXNDb20gPyAnQ29tcF9Db2Rlc19WYWd1ZScgOiAnUGFydF9Db2RlX1ZhZ3VlJw0KICAgICAgY29uc3QgY29kZUxpc3RWYWd1ZSA9IHRoaXMucXVlcnlGb3JtW3F1ZXJ5Q29kZVZhZ3VlXQ0KDQogICAgICBjb25zdCBzZWFyY2hUYkRhdGEgPSB0aGlzLnRiRGF0YS5maWx0ZXIodiA9PiB7DQogICAgICAgIGlmICghY29kZUxpc3QubGVuZ3RoICYmIGNvZGVMaXN0VmFndWUgPT09ICcnKSB7DQogICAgICAgICAgcmV0dXJuIHRydWUNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICByZXR1cm4gY29kZUxpc3QuaW5jbHVkZXModltjb2RlXSkNCiAgICAgICAgfQ0KICAgICAgfSkNCg0KICAgICAgY29uc3QgdmFndWVEYXRhID0gc2VhcmNoVGJEYXRhLmxlbmd0aCA+IDAgJiYgY29kZUxpc3RWYWd1ZSA9PT0gJycgPyBzZWFyY2hUYkRhdGEgOiBzZWFyY2hUYkRhdGEubGVuZ3RoID09PSAwICYmIGNvZGVMaXN0VmFndWUgPT09ICcnID8gc2VhcmNoVGJEYXRhIDogdGhpcy50YkRhdGENCiAgICAgIGNvbnN0IHNlYXJjaFZhZ3VlVGJEYXRhID0gdmFndWVEYXRhLmZpbHRlcih2ID0+IHsNCiAgICAgICAgaWYgKGNvZGVMaXN0VmFndWUgPT09ICcnICYmICFjb2RlTGlzdC5sZW5ndGgpIHsNCiAgICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHJldHVybiB2W2NvZGVdLmluY2x1ZGVzKGNvZGVMaXN0VmFndWUpDQogICAgICAgIH0NCiAgICAgIH0pDQoNCiAgICAgIC8vIOWQiOW5tuS4pOS4quaVsOe7hA0KICAgICAgY29uc3QgbWVyZ2VkQXJyYXkgPSBzZWFyY2hUYkRhdGEuY29uY2F0KHNlYXJjaFZhZ3VlVGJEYXRhKQ0KICAgICAgLy8g5qC55o2uIFNjaGR1bGluZ19EZXRhaWxfSWQg6L+b6KGM5Y676YeNDQogICAgICBjb25zdCB1bmlxdWVBcnJheSA9IG1lcmdlZEFycmF5LnJlZHVjZSgoYWNjLCBjdXJyZW50KSA9PiB7DQogICAgICAgIGNvbnN0IGV4aXN0aW5nT2JqZWN0ID0gYWNjLmZpbmQoaXRlbSA9PiBpdGVtLlNjaGR1bGluZ19EZXRhaWxfSWQgPT09IGN1cnJlbnQuU2NoZHVsaW5nX0RldGFpbF9JZCkNCiAgICAgICAgaWYgKCFleGlzdGluZ09iamVjdCkgew0KICAgICAgICAgIGFjYy5wdXNoKGN1cnJlbnQpDQogICAgICAgIH0NCiAgICAgICAgcmV0dXJuIGFjYw0KICAgICAgfSwgW10pDQoNCiAgICAgIHRoaXMuZmlsdGVyVGJEYXRhID0gdW5pcXVlQXJyYXkuZmlsdGVyKHYgPT4gew0KICAgICAgICBpZiAoIXRoaXMuc2VhcmNoVHlwZSkgcmV0dXJuIHRydWUNCiAgICAgICAgcmV0dXJuIHRoaXMuc2VhcmNoVHlwZSA9PT0gdi5UeXBlDQogICAgICB9KS5maWx0ZXIodiA9PiB7DQogICAgICAgIGlmICghdGhpcy5xdWVyeUZvcm0uU3BlYykgcmV0dXJuIHRydWUNCiAgICAgICAgcmV0dXJuICh2LlNwZWMgfHwgJycpLmluY2x1ZGVzKHRoaXMucXVlcnlGb3JtLlNwZWMpDQogICAgICB9KS5maWx0ZXIocm93ID0+IHsNCiAgICAgICAgcmV0dXJuIHRoaXMuZmlsdGVyWmVybyhyb3cpDQogICAgICB9KQ0KICAgIH0sDQogICAgaW5pdFRiRGF0YShsaXN0LCB0ZWFtS2V5ID0gJ0FsbG9jYXRpb25fVGVhbXMnKSB7DQogICAgICB0aGlzLnRiRGF0YSA9IGxpc3QubWFwKHJvdyA9PiB7DQogICAgICAgIGNvbnN0IHByb2Nlc3NMaXN0ID0gcm93LlRlY2hub2xvZ3lfUGF0aD8uc3BsaXQoJy8nKSB8fCBbXQ0KICAgICAgICAvLyDlt7J1dWlk5L2c5Li6cm935ZSv5LiA5YC877ybDQogICAgICAgIC8vIHV1aWQr5bel5bqPK+ePree7hOS4uui+k+WFpeahhuWAvA0KICAgICAgICByb3cudXVpZCA9IHV1aWR2NCgpDQogICAgICAgIGNvbnN0IG5ld0RhdGEgPSByb3dbdGVhbUtleV0uZmlsdGVyKChyKSA9PiBwcm9jZXNzTGlzdC5maW5kSW5kZXgoKHApID0+IHIuUHJvY2Vzc19Db2RlID09PSBwKSAhPT0gLTEpDQogICAgICAgIHJvdy5kZWZhdWx0Q2FuX0FsbG9jYXRpb25fQ291bnQgPSByb3cuQ2FuX0FsbG9jYXRpb25fQ291bnQNCiAgICAgICAgbGV0IF9pbnB1dE51bSA9IDANCiAgICAgICAgbmV3RGF0YS5mb3JFYWNoKChlbGUsIGluZGV4KSA9PiB7DQogICAgICAgICAgY29uc3QgY29kZSA9IHRoaXMuZ2V0Um93VW5pcXVlKHJvdy51dWlkLCBlbGUuUHJvY2Vzc19Db2RlLCBlbGUuV29ya2luZ19UZWFtX0lkKQ0KICAgICAgICAgIGNvbnN0IG1heCA9IHRoaXMuZ2V0Um93VW5pcXVlTWF4KHJvdy51dWlkLCBlbGUuUHJvY2Vzc19Db2RlLCBlbGUuV29ya2luZ19UZWFtX0lkKQ0KICAgICAgICAgIHJvd1tjb2RlXSA9IGVsZS5Db3VudA0KICAgICAgICAgIHRoaXMuJHNldChyb3csICdhbENvdW50JyArIGNvZGUsIGVsZS5Db3VudCkNCiAgICAgICAgICByb3dbbWF4XSA9IDANCiAgICAgICAgICBfaW5wdXROdW0gKz0gZWxlLkNvdW50DQogICAgICAgICAgdGhpcy4kc2V0KHJvdywgJ3RvdGFsVGFzaycgKyBlbGUuV29ya2luZ19UZWFtX05hbWUsIGVsZS5Ub3RhbF9SZWNlaXZlX0NvdW50ICsgZWxlLkNvdW50KQ0KICAgICAgICB9KQ0KDQogICAgICAgIHRoaXMuJHNldChyb3csICdhbGxvY2F0ZWRUYXNrJywgcm93LmRlZmF1bHRDYW5fQWxsb2NhdGlvbl9Db3VudCAtIF9pbnB1dE51bSkNCiAgICAgICAgcm93LkNhbl9BbGxvY2F0aW9uX0NvdW50ID0gcm93LmFsbG9jYXRlZFRhc2sNCiAgICAgICAgdGhpcy4kc2V0KHJvdywgJ2FsbG9jYXRlZFRhc2tNYXgnLCByb3cuQ2FuX0FsbG9jYXRpb25fQ291bnQpDQoNCiAgICAgICAgdGhpcy5zZXRJbnB1dE1heChyb3cpDQoNCiAgICAgICAgcm93LmNoZWNrZWQgPSBmYWxzZQ0KICAgICAgICByZXR1cm4gcm93DQogICAgICB9KQ0KICAgIH0sDQogICAgaW5wdXRDaGFuZ2Uocm93KSB7DQogICAgICB0aGlzLnNldElucHV0TWF4KHJvdykNCiAgICB9LA0KICAgIHNldElucHV0TWF4KHJvdykgew0KICAgICAgbGV0IF9pbnB1dE51bSA9IDANCiAgICAgIGNvbnN0IGlucHV0VmFsdWVzS2V5cyA9IE9iamVjdC5rZXlzKHJvdykNCiAgICAgICAgLmZpbHRlcih2ID0+ICF2LmVuZHNXaXRoKCdtYXgnKSAmJiB2LnN0YXJ0c1dpdGgocm93LnV1aWQpICYmIHYubGVuZ3RoID4gcm93LnV1aWQubGVuZ3RoKQ0KICAgICAgaW5wdXRWYWx1ZXNLZXlzLmZvckVhY2goKHZhbCkgPT4gew0KICAgICAgICBjb25zdCBjdXJDb2RlID0gdmFsLnNwbGl0KFNQTElUX1NZTUJPTClbMV0NCiAgICAgICAgY29uc3Qgb3RoZXJUb3RhbCA9IGlucHV0VmFsdWVzS2V5cy5maWx0ZXIoeCA9PiB7DQogICAgICAgICAgY29uc3QgY29kZSA9IHguc3BsaXQoU1BMSVRfU1lNQk9MKVsxXQ0KICAgICAgICAgIHJldHVybiB4ICE9PSB2YWwgJiYgY29kZSA9PT0gY3VyQ29kZQ0KICAgICAgICB9KS5yZWR1Y2UoKGFjYywgaXRlbSkgPT4gew0KICAgICAgICAgIHJldHVybiBhY2MgKyBudW1lcmFsKHJvd1tpdGVtXSkudmFsdWUoKQ0KICAgICAgICB9LCAwKQ0KICAgICAgICByb3dbdmFsICsgU1BMSVRfU1lNQk9MICsgJ21heCddID0gcm93LlNjaGR1bGVkX0NvdW50IC0gb3RoZXJUb3RhbA0KICAgICAgICBfaW5wdXROdW0gKz0gK3Jvd1t2YWxdDQogICAgICB9KQ0KICAgICAgLy8gcm93LmFsbG9jYXRlZENvdW50ID0gcm93LmRlZmF1bHRDYW5fQWxsb2NhdGlvbl9Db3VudCAtIF9pbnB1dE51bQ0KICAgICAgLy8gcm93LkNhbl9BbGxvY2F0aW9uX0NvdW50ID0gcm93LmFsbG9jYXRlZENvdW50DQogICAgfSwNCiAgICBjaGVja1Blcm1pc3Npb25UZWFtKHByb2Nlc3NTdHIsIHByb2Nlc3NDb2RlKSB7DQogICAgICBpZiAoIXByb2Nlc3NTdHIpIHJldHVybiBmYWxzZQ0KICAgICAgY29uc3QgbGlzdCA9IHByb2Nlc3NTdHI/LnNwbGl0KCcvJykgfHwgW10NCiAgICAgIHJldHVybiAhIWxpc3Quc29tZSh2ID0+IHYgPT09IHByb2Nlc3NDb2RlKQ0KICAgIH0sDQoNCiAgICBnZXRTdWJtaXRUYkluZm8odGJEYXRhID0gdGhpcy50YkRhdGEpIHsNCiAgICAgIC8vIOWkhOeQhuS4iuS8oOeahOaVsOaNrg0KICAgICAgY29uc3QgdGFibGVEYXRhID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0YkRhdGEpKQ0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0YWJsZURhdGEubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgY29uc3QgZWxlbWVudCA9IHRhYmxlRGF0YVtpXQ0KICAgICAgICBjb25zdCBsaXN0ID0gW10NCiAgICAgICAgaWYgKCFlbGVtZW50LlRlY2hub2xvZ3lfUGF0aCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogJ+W3peW6j+S4jeiDveS4uuepuicsDQogICAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgICB9KQ0KICAgICAgICAgIHJldHVybiB7IHN0YXR1czogZmFsc2UgfQ0KICAgICAgICB9DQogICAgICAgIGNvbnN0IHByb2Nlc3NMaXN0ID0gQXJyYXkuZnJvbShuZXcgU2V0KGVsZW1lbnQuVGVjaG5vbG9neV9QYXRoLnNwbGl0KCcvJykpKQ0KICAgICAgICBwcm9jZXNzTGlzdC5mb3JFYWNoKGNvZGUgPT4gew0KICAgICAgICAgIGNvbnN0IGdyb3VwcyA9IHRoaXMud29ya2luZ1RlYW0uZmlsdGVyKHYgPT4gdi5Qcm9jZXNzX0NvZGUgPT09IGNvZGUpDQoNCiAgICAgICAgICBjb25zdCBncm91cHNMaXN0ID0gZ3JvdXBzLm1hcCgoZ3JvdXAsIGluZGV4KSA9PiB7DQogICAgICAgICAgICBjb25zdCB1Q29kZSA9IHRoaXMuZ2V0Um93VW5pcXVlKGVsZW1lbnQudXVpZCwgY29kZSwgZ3JvdXAuV29ya2luZ19UZWFtX0lkKQ0KICAgICAgICAgICAgY29uc3QgdU1heCA9IHRoaXMuZ2V0Um93VW5pcXVlTWF4KGVsZW1lbnQudXVpZCwgY29kZSwgZ3JvdXAuV29ya2luZ19UZWFtX0lkKQ0KICAgICAgICAgICAgY29uc3Qgb2JqID0gew0KICAgICAgICAgICAgICBDb21wX0NvZGU6IGVsZW1lbnQuQ29tcF9Db2RlLA0KICAgICAgICAgICAgICBBZ2Fpbl9Db3VudDogK2VsZW1lbnRbdUNvZGVdLA0KICAgICAgICAgICAgICBQYXJ0X0NvZGU6IHRoaXMuaXNDb20gPyBudWxsIDogZWxlbWVudC5QYXJ0X0NvZGUsDQogICAgICAgICAgICAgIFByb2Nlc3NfQ29kZTogY29kZSwNCiAgICAgICAgICAgICAgVGVjaG5vbG9neV9QYXRoOiBlbGVtZW50LlRlY2hub2xvZ3lfUGF0aCwNCiAgICAgICAgICAgICAgV29ya2luZ19UZWFtX0lkOiBncm91cC5Xb3JraW5nX1RlYW1fSWQsDQogICAgICAgICAgICAgIFdvcmtpbmdfVGVhbV9OYW1lOiBncm91cC5Xb3JraW5nX1RlYW1fTmFtZSwNCiAgICAgICAgICAgICAgVGVhbV9UYXNrX0lkOiBlbGVtZW50LkFsbG9jYXRpb25fVGVhbXMuZmluZCgoaXRlbSkgPT4gaXRlbS5Xb3JraW5nX1RlYW1fSWQgPT09IGdyb3VwLldvcmtpbmdfVGVhbV9JZCkuVGVhbV9UYXNrX0lkDQogICAgICAgICAgICB9DQogICAgICAgICAgICBkZWxldGUgZWxlbWVudFsnYWxDb3VudCcgKyB1Q29kZV0NCiAgICAgICAgICAgIGRlbGV0ZSBlbGVtZW50WydhbGxvY2F0ZWRUYXNrJ10NCiAgICAgICAgICAgIGRlbGV0ZSBlbGVtZW50WydhbGxvY2F0ZWRUYXNrTWF4J10NCiAgICAgICAgICAgIGRlbGV0ZSBlbGVtZW50W3VDb2RlXQ0KICAgICAgICAgICAgZGVsZXRlIGVsZW1lbnRbdU1heF0NCiAgICAgICAgICAgIHJldHVybiBvYmoNCiAgICAgICAgICB9KQ0KICAgICAgICAgIGxpc3QucHVzaCguLi5ncm91cHNMaXN0KQ0KICAgICAgICB9KQ0KICAgICAgICBjb25zb2xlLmxvZyhsaXN0KQ0KICAgICAgICBkZWxldGUgZWxlbWVudFsndXVpZCddDQogICAgICAgIGRlbGV0ZSBlbGVtZW50WydwdXVpZCddDQogICAgICAgIGVsZW1lbnQuQWxsb2NhdGlvbl9UZWFtcyA9IGxpc3QNCiAgICAgIH0NCiAgICAgIHJldHVybiB7IHRhYmxlRGF0YSwgc3RhdHVzOiB0cnVlIH0NCiAgICB9LA0KICAgIGhhbmRsZVN1Ym1pdCgpIHsNCiAgICAgIGNvbnN0IHsgdGFibGVEYXRhLCBzdGF0dXMgfSA9IHRoaXMuZ2V0U3VibWl0VGJJbmZvKCkNCiAgICAgIGlmICghc3RhdHVzKSByZXR1cm4NCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuaPkOS6pOW9k+WJjeaVsOaNrj8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlDQogICAgICAgIGNvbnN0IG9iaiA9IHsNCiAgICAgICAgICBTY2hkdWxpbmdfUGxhbjogdGhpcy5wbGFuSW5mb1RlbXAsDQogICAgICAgICAgU2NoZHVsaW5nX0NvbXBzOiB0YWJsZURhdGENCiAgICAgICAgfQ0KICAgICAgICBjb25zdCBQYXJ0b2JqID0gew0KICAgICAgICAgIFNjaGR1bGluZ19QbGFuOiB0aGlzLnBsYW5JbmZvVGVtcCwNCiAgICAgICAgICBTYXJlUGFydHNNb2RlbDogdGFibGVEYXRhDQogICAgICAgIH0NCiAgICAgICAgY29uc3QgcmVxdWVzdEZuID0gdGhpcy5pc0NvbSA/IEFkanVzdFRlYW1Qcm9jZXNzQWxsb2NhdGlvbiA6IHRoaXMuaXNVbml0UGFydCA/IEFkanVzdFN1YkFzc2VtYmx5VGVhbVByb2Nlc3NBbGxvY2F0aW9uIDogQWRqdXN0UGFydFRlYW1Qcm9jZXNzQWxsb2NhdGlvbg0KICAgICAgICBjb25zb2xlLmxvZygnb2JqJywgb2JqKQ0KICAgICAgICByZXF1ZXN0Rm4odGhpcy5pc0NvbSA/IG9iaiA6IFBhcnRvYmopLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6ICfmk43kvZzmiJDlip8nLA0KICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICB0aGlzLmhhbmRsZUNsb3NlKCkNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB9KQ0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICB0eXBlOiAnaW5mbycsDQogICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iCcNCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVDbG9zZSgpIHsNCiAgICAgIHRoaXMuY2xvc2VWaWV3KCkNCiAgICB9LA0KICAgIGNsb3NlVmlldygpIHsNCiAgICAgIGNsb3NlVGFnVmlldyh0aGlzLiRzdG9yZSwgdGhpcy4kcm91dGUpDQogICAgfSwNCiAgICB0YlNlbGVjdENoYW5nZShhcnJheSkgew0KICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbiA9IGFycmF5LnJlY29yZHMNCiAgICAgIGNvbnNvbGUubG9nKHRoaXMudGJEYXRhKQ0KICAgIH0sDQogICAgZ2V0VGFza0NvZGUobmFtZSA9ICcnKSB7DQogICAgICBpZiAobmFtZSkgcmV0dXJuICd0b3RhbFRhc2snICsgbmFtZQ0KICAgICAgcmV0dXJuICd0b3RhbFRhc2snICsgdGhpcy5hY3RpdmVOYW1lLnNwbGl0KFNQTElUX1NZTUJPTClbMF0NCiAgICB9LA0KICAgIC8vIOWPjemAiQ0KICAgIHJldmVyc2VTZWxlY3Rpb24oKSB7DQogICAgICBjb25zdCBsaXN0ID0gdGhpcy4kcmVmcy54VGFibGUuZ2V0Q2hlY2tib3hSZWNvcmRzKCkuZmlsdGVyKGl0ZW0gPT4gIWl0ZW0uc3RvcEZsYWcpDQogICAgICBjb25zdCB1blNlbGVjdExpc3QgPSB0aGlzLmZpbHRlclRiRGF0YS5maWx0ZXIoaXRlbSA9PiAhbGlzdC5pbmNsdWRlcyhpdGVtKSAmJiAhaXRlbS5zdG9wRmxhZykNCiAgICAgIHRoaXMuJHJlZnMueFRhYmxlLnNldENoZWNrYm94Um93KGxpc3QsIGZhbHNlKQ0KICAgICAgdGhpcy4kcmVmcy54VGFibGUuc2V0Q2hlY2tib3hSb3codW5TZWxlY3RMaXN0LCB0cnVlKQ0KICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbiA9IHRoaXMuJHJlZnMueFRhYmxlLmdldENoZWNrYm94UmVjb3JkcygpDQogICAgfSwNCiAgICBhc3luYyBnZXRUYWJsZUNvbmZpZyhjb2RlKSB7DQogICAgICBhd2FpdCBHZXRHcmlkQnlDb2RlKHsNCiAgICAgICAgY29kZQ0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGNvbnN0IHsgSXNTdWNjZWVkLCBEYXRhLCBNZXNzYWdlIH0gPSByZXMNCiAgICAgICAgaWYgKElzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMudGJDb25maWcgPSBPYmplY3QuYXNzaWduKHt9LCB0aGlzLnRiQ29uZmlnLCBEYXRhLkdyaWQpDQogICAgICAgICAgY29uc3QgbGlzdCA9IERhdGEuQ29sdW1uTGlzdCB8fCBbXQ0KICAgICAgICAgIHRoaXMuY29sdW1ucyA9IGxpc3QuZmlsdGVyKHYgPT4gdi5Jc19EaXNwbGF5KS5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgICBpZiAoRklYX0NPTFVNTi5pbmNsdWRlcyhpdGVtLkNvZGUpKSB7DQogICAgICAgICAgICAgIGl0ZW0uZml4ZWQgPSAnbGVmdCcNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGlmIChpdGVtLkNvZGUgPT09ICdTY2hkdWxlZF9Db3VudCcpIHsNCiAgICAgICAgICAgICAgaXRlbS52aXNpYmxlID0gZmFsc2UNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHJldHVybiBpdGVtDQogICAgICAgICAgfSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IE1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFjdGl2ZUNlbGxNZXRob2QoeyByb3csIGNvbHVtbiwgY29sdW1uSW5kZXggfSkgew0KICAgICAgaWYgKHRoaXMuaXNWaWV3KSByZXR1cm4gZmFsc2UNCiAgICAgIGlmIChjb2x1bW4uZmllbGQgPT09ICdBbGxvY2F0ZWRDb3VudCcpIHJldHVybiB0cnVlDQogICAgICBjb25zdCBwcm9jZXNzQ29kZSA9IGNvbHVtbi5maWVsZD8uc3BsaXQoJyRfJCcpWzFdDQogICAgICByZXR1cm4gdGhpcy5jaGVja1Blcm1pc3Npb25UZWFtKHJvdy5UZWNobm9sb2d5X1BhdGgsIHByb2Nlc3NDb2RlKQ0KICAgIH0sDQogICAgZ2V0Um93VW5pcXVlKHV1aWQsIHByb2Nlc3NDb2RlLCB3b3JraW5nSWQpIHsNCiAgICAgIHJldHVybiBgJHt1dWlkfSR7U1BMSVRfU1lNQk9MfSR7cHJvY2Vzc0NvZGV9JHtTUExJVF9TWU1CT0x9JHt3b3JraW5nSWR9YA0KICAgIH0sDQogICAgZ2V0Um93VW5pcXVlTWF4KHV1aWQsIHByb2Nlc3NDb2RlLCB3b3JraW5nSWQpIHsNCiAgICAgIHJldHVybiB0aGlzLmdldFJvd1VuaXF1ZSh1dWlkLCBwcm9jZXNzQ29kZSwgd29ya2luZ0lkKSArIGAke1NQTElUX1NZTUJPTH1tYXhgDQogICAgfSwNCiAgICAvLyDmibnph4/liIbphY0NCiAgICBCYXRjaGFsbG9jYXRpb24oKSB7DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgICBjb25zb2xlLmxvZyh0aGlzLndvcmtpbmdUZWFtKQ0KICAgIH0sDQogICAgLy8g5LiK6YGT5bel5bqP5YiG6YWNDQogICAgcHJlU3RlcFRhc2tBbGxvY2F0aW9uKCkgew0KICAgICAgY29uc3QgU2NoZHVsaW5nX0RldGFpbF9JZHMgPSB0aGlzLm11bHRpcGxlU2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uU2NoZHVsaW5nX0RldGFpbF9JZCkNCiAgICAgIGNvbnN0IFdvcmtpbmdfUHJvY2Vzc19Db2RlID0gdGhpcy5tdWx0aXBsZVNlbGVjdGlvblswXS5Qcm9jZXNzX0NvZGUNCiAgICAgIEdldFByZVN0ZXBUYXNrQWxsb2NhdGlvbih7DQogICAgICAgIFNjaGR1bGluZ19EZXRhaWxfSWRzLA0KICAgICAgICBXb3JraW5nX1Byb2Nlc3NfQ29kZSwNCiAgICAgICAgV29ya2luZ19Qcm9jZXNzX0lkOiB0aGlzLldvcmtpbmdfUHJvY2Vzc19JZCwNCiAgICAgICAgUHJvY2Vzc19UeXBlOiB0aGlzLmlzQ29tID8gMiA6IHRoaXMuaXNVbml0UGFydCA/IDMgOiAxLA0KICAgICAgICBCb21fTGV2ZWw6IHRoaXMuYm9tTGV2ZWwNCiAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICBpZiAocmVzLkRhdGEubGVuZ3RoID09PSAwKSB7DQogICAgICAgICAgICB0aGlzLmRpYWxvZ1RpcHNWaXNpYmxlID0gdHJ1ZQ0KICAgICAgICAgICAgcmV0dXJuDQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMucHJlRG9BbGxvY2F0aW9uKHJlcy5EYXRhKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZURpYWxvZygpIHsNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgICAvLyB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gW10NCiAgICB9LA0KICAgIGhhbmRlbERhdGEoKSB7DQogICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uLmZvckVhY2goKGl0ZW0pID0+DQogICAgICAgIGl0ZW0uQWxsb2NhdGlvbl9UZWFtcy5mb3JFYWNoKCh2KSA9PiB7DQogICAgICAgICAgaWYgKHYuV29ya2luZ19UZWFtX0lkID09PSB0aGlzLmZvcm0uVGVhbUdyb3VwKSB7DQogICAgICAgICAgICB2LkNvdW50ID0gaXRlbS5DYW5fQWxsb2NhdGlvbl9Db3VudA0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB2LkNvdW50ID0gMA0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICkNCiAgICAgIC8vIGNvbnN0IHRhYmxlRGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy5tdWx0aXBsZVNlbGVjdGlvbikpDQogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMubXVsdGlwbGVTZWxlY3Rpb24ubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgY29uc3QgZWxlbWVudCA9IHRoaXMubXVsdGlwbGVTZWxlY3Rpb25baV0NCiAgICAgICAgY29uc3QgcHJvY2Vzc0xpc3QgPSBBcnJheS5mcm9tKG5ldyBTZXQoZWxlbWVudC5UZWNobm9sb2d5X1BhdGguc3BsaXQoJy8nKSkpDQogICAgICAgIHByb2Nlc3NMaXN0LmZvckVhY2goY29kZSA9PiB7DQogICAgICAgICAgY29uc3QgZ3JvdXBzID0gdGhpcy53b3JraW5nVGVhbS5maWx0ZXIodiA9PiB2LlByb2Nlc3NfQ29kZSA9PT0gY29kZSkNCiAgICAgICAgICBncm91cHMuZm9yRWFjaChncm91cCA9PiB7DQogICAgICAgICAgICBjb25zdCB1bmlDb2RlID0gdGhpcy5nZXRSb3dVbmlxdWUoZWxlbWVudC51dWlkLCBjb2RlLCB0aGlzLmZvcm0uVGVhbUdyb3VwKQ0KICAgICAgICAgICAgY29uc3QgdUNvZGUgPSB0aGlzLmdldFJvd1VuaXF1ZShlbGVtZW50LnV1aWQsIGNvZGUsIGdyb3VwLldvcmtpbmdfVGVhbV9JZCkNCiAgICAgICAgICAgIGNvbnN0IHVuaU1heCA9IHRoaXMuZ2V0Um93VW5pcXVlTWF4KGVsZW1lbnQudXVpZCwgY29kZSwgdGhpcy5mb3JtLlRlYW1Hcm91cCkNCiAgICAgICAgICAgIGNvbnN0IHVNYXggPSB0aGlzLmdldFJvd1VuaXF1ZU1heChlbGVtZW50LnV1aWQsIGNvZGUsIGdyb3VwLldvcmtpbmdfVGVhbV9JZCkNCg0KICAgICAgICAgICAgaWYgKHVuaUNvZGUgPT09IHVDb2RlICYmIHVuaU1heCA9PT0gdU1heCkgew0KICAgICAgICAgICAgICBlbGVtZW50W3VDb2RlXSA9IGVsZW1lbnRbJ0Nhbl9BbGxvY2F0aW9uX0NvdW50J10NCiAgICAgICAgICAgICAgZWxlbWVudFt1TWF4XSA9IGVsZW1lbnRbJ0Nhbl9BbGxvY2F0aW9uX0NvdW50J10NCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIGVsZW1lbnRbdUNvZGVdID0gMA0KICAgICAgICAgICAgICBlbGVtZW50W3VNYXhdID0gMA0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0pDQogICAgICB9DQogICAgICBjb25zb2xlLmxvZyh0aGlzLm11bHRpcGxlU2VsZWN0aW9uKQ0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLnRiRGF0YS5sZW5ndGg7IGkrKykgew0KICAgICAgICBmb3IgKGxldCBrID0gMDsgayA8IHRoaXMubXVsdGlwbGVTZWxlY3Rpb24ubGVuZ3RoOyBrKyspIHsNCiAgICAgICAgICBpZiAodGhpcy50YkRhdGFbaV0udXVpZCA9PT0gdGhpcy5tdWx0aXBsZVNlbGVjdGlvbltrXS51dWlkKSB7DQogICAgICAgICAgICB0aGlzLiRuZXh0VGljaygoXykgPT4gew0KICAgICAgICAgICAgICB0aGlzLnRiRGF0YVtpXSA9IHRoaXMubXVsdGlwbGVTZWxlY3Rpb25ba10NCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBzdWJtaXRGb3JtKGZvcm1OYW1lKSB7DQogICAgICB0aGlzLiRyZWZzW2Zvcm1OYW1lXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgLy8gdGhpcy5oYW5kZWxEYXRhKCkNCiAgICAgICAgICB0aGlzLmRvQWxsb2NhdGlvbigpDQogICAgICAgICAgLy8gdGhpcy53b3JraW5nVGVhbS5maW5kKHY9PiB2LldvcmtpbmdfVGVhbV9JZCA9PT0gdGhpcy5mb3JtLlRlYW1Hcm91cCkuY291bnQNCiAgICAgICAgICB0aGlzLmhhbmRsZURpYWxvZygpDQogICAgICAgICAgY29uc29sZS5sb2codGhpcy50YkRhdGEpDQogICAgICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbiA9IFtdDQogICAgICAgICAgdGhpcy4kcmVmcy54VGFibGUuY2xlYXJDaGVja2JveFJvdygpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyDkuIrpgZPlt6Xluo/liIbphY0NCiAgICBwcmVEb0FsbG9jYXRpb24ocHJlUHJvY2Vzc0RhdGEpIHsNCiAgICAgIGNvbnN0IHByZVByb2Nlc3NEYXRhTWFwID0gbmV3IE1hcCgpDQogICAgICBwcmVQcm9jZXNzRGF0YS5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICBjb25zdCBrZXkgPSBgJHtpdGVtLlNjaGR1bGluZ19EZXRhaWxfSWR9XyR7aXRlbS5Xb3JraW5nX1RlYW1fSWR9YA0KICAgICAgICBwcmVQcm9jZXNzRGF0YU1hcC5zZXQoa2V5LCBpdGVtLkN1cnJlbnRfVGFza19Db3VudCkNCiAgICAgIH0pDQoNCiAgICAgIGNvbnN0IGFsbG9jYXRlRm9yVGVhbSA9IChyb3csIHRlYW0sIGFtb3VudCkgPT4gew0KICAgICAgICBjb25zdCB0YXJDb2RlID0gdGhpcy5nZXRSb3dVbmlxdWUocm93LnV1aWQsIHRlYW0uUHJvY2Vzc19Db2RlLCB0ZWFtLldvcmtpbmdfVGVhbV9JZCkNCiAgICAgICAgY29uc3QgYktleSA9IGAke3Jvdy5TY2hkdWxpbmdfRGV0YWlsX0lkfV8ke3RlYW0uV29ya2luZ19UZWFtX0lkfWANCiAgICAgICAgY29uc3QgY3VycmVudFRhc2tDb3VudCA9IHByZVByb2Nlc3NEYXRhTWFwLmdldChiS2V5KSB8fCAwDQogICAgICAgIGNvbnN0IGFsbG9jYXRlZCA9IE1hdGgubWluKGFtb3VudCwgY3VycmVudFRhc2tDb3VudCwgcm93LkNhbl9BbGxvY2F0aW9uX0NvdW50KQ0KDQogICAgICAgIHJvd1t0YXJDb2RlXSA9IChOdW1iZXIocm93W3RhckNvZGVdKSB8fCAwKSArIGFsbG9jYXRlZA0KICAgICAgICByb3cuQ2FuX0FsbG9jYXRpb25fQ291bnQgLT0gYWxsb2NhdGVkDQogICAgICAgIHJvd1t0aGlzLmdldFRhc2tDb2RlKHRlYW0uV29ya2luZ19UZWFtX05hbWUpXSA9IChyb3dbdGhpcy5nZXRUYXNrQ29kZSh0ZWFtLldvcmtpbmdfVGVhbV9OYW1lKV0gfHwgMCkgKyBhbGxvY2F0ZWQNCiAgICAgICAgcm93W3RoaXMuZ2V0VGFza0NvZGUoKV0gPSAocm93W3RoaXMuZ2V0VGFza0NvZGUoKV0gfHwgMCkgLSBhbGxvY2F0ZWQNCiAgICAgICAgcm93WydhbENvdW50JyArIHRhckNvZGVdID0gcm93W3RhckNvZGVdDQoNCiAgICAgICAgcmV0dXJuIGFsbG9jYXRlZA0KICAgICAgfQ0KICAgICAgbGV0IGlzTWVzc2FnZSA9IHRydWUNCiAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24uZm9yRWFjaChyb3cgPT4gew0KICAgICAgICBpZiAoIXJvdy5DYW5fQWxsb2NhdGlvbl9Db3VudCAmJiB0aGlzLmFjdGl2ZU5hbWUgPT09ICdmaXJzdCcpIHJldHVybg0KDQogICAgICAgIGNvbnN0IGVsaWdpYmxlVGVhbXMgPSByb3cuQWxsb2NhdGlvbl9UZWFtcy5maWx0ZXIodGVhbSA9Pg0KICAgICAgICAgIHByZVByb2Nlc3NEYXRhTWFwLmhhcyhgJHtyb3cuU2NoZHVsaW5nX0RldGFpbF9JZH1fJHt0ZWFtLldvcmtpbmdfVGVhbV9JZH1gKQ0KICAgICAgICApDQoNCiAgICAgICAgaWYgKGVsaWdpYmxlVGVhbXMubGVuZ3RoID09PSAwKSByZXR1cm4NCiAgICAgICAgaWYgKHRoaXMuYWN0aXZlTmFtZSA9PT0gJ2ZpcnN0Jykgew0KICAgICAgICAgIGxldCBJc0FsbE5vID0gMA0KICAgICAgICAgIGNvbnN0IHRvdGFsQXZhaWxhYmxlID0gZWxpZ2libGVUZWFtcy5yZWR1Y2UoKHN1bSwgdGVhbSkgPT4gew0KICAgICAgICAgICAgY29uc3QgYktleSA9IGAke3Jvdy5TY2hkdWxpbmdfRGV0YWlsX0lkfV8ke3RlYW0uV29ya2luZ19UZWFtX0lkfWANCiAgICAgICAgICAgIHJldHVybiBzdW0gKyAocHJlUHJvY2Vzc0RhdGFNYXAuZ2V0KGJLZXkpIHx8IDApDQogICAgICAgICAgfSwgMCkNCg0KICAgICAgICAgIGlmIChyb3cuYWxsb2NhdGVkVGFza01heCA8IHRvdGFsQXZhaWxhYmxlKSB7DQogICAgICAgICAgICBJc0FsbE5vKysNCiAgICAgICAgICAgIGlmIChJc0FsbE5vID09PSB0aGlzLm11bHRpcGxlU2VsZWN0aW9uLmxlbmd0aCkgew0KICAgICAgICAgICAgICBpc01lc3NhZ2UgPSBmYWxzZQ0KICAgICAgICAgICAgICB0aGlzLmRpYWxvZ1RpcHNWaXNpYmxlID0gdHJ1ZQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgcmV0dXJuDQogICAgICAgICAgfQ0KDQogICAgICAgICAgbGV0IHJlbWFpbmluZyA9IE1hdGgubWluKHJvdy5hbGxvY2F0ZWRUYXNrLCByb3cuQ2FuX0FsbG9jYXRpb25fQ291bnQpDQogICAgICAgICAgY29uc3QgcGVyVGVhbUFsbG9jYXRpb24gPSBNYXRoLmZsb29yKHJlbWFpbmluZyAvIGVsaWdpYmxlVGVhbXMubGVuZ3RoKQ0KDQogICAgICAgICAgZWxpZ2libGVUZWFtcy5mb3JFYWNoKCh0ZWFtLCBpbmRleCkgPT4gew0KICAgICAgICAgICAgaWYgKHJlbWFpbmluZyA8PSAwKSByZXR1cm4NCg0KICAgICAgICAgICAgY29uc3QgYWxsb2NhdGVBbW91bnQgPSBpbmRleCA9PT0gZWxpZ2libGVUZWFtcy5sZW5ndGggLSAxDQogICAgICAgICAgICAgID8gcmVtYWluaW5nDQogICAgICAgICAgICAgIDogTWF0aC5taW4ocGVyVGVhbUFsbG9jYXRpb24sIHJlbWFpbmluZykNCg0KICAgICAgICAgICAgcmVtYWluaW5nIC09IGFsbG9jYXRlRm9yVGVhbShyb3csIHRlYW0sIGFsbG9jYXRlQW1vdW50KQ0KICAgICAgICAgIH0pDQoNCiAgICAgICAgICByb3cuYWxsb2NhdGVkVGFza01heCA9IHJvdy5DYW5fQWxsb2NhdGlvbl9Db3VudA0KICAgICAgICAgIHJvdy5hbGxvY2F0ZWRUYXNrID0gcm93LkNhbl9BbGxvY2F0aW9uX0NvdW50DQogICAgICAgICAgaWYgKElzQWxsTm8gPT09IHRoaXMubXVsdGlwbGVTZWxlY3Rpb24ubGVuZ3RoKSB7DQogICAgICAgICAgICBpc01lc3NhZ2UgPSBmYWxzZQ0KICAgICAgICAgICAgdGhpcy5kaWFsb2dUaXBzVmlzaWJsZSA9IHRydWUNCiAgICAgICAgICAgIHJldHVybg0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBlbGlnaWJsZVRlYW1zLmZvckVhY2godGVhbSA9PiB7DQogICAgICAgICAgICBjb25zdCBiS2V5ID0gYCR7cm93LlNjaGR1bGluZ19EZXRhaWxfSWR9XyR7dGVhbS5Xb3JraW5nX1RlYW1fSWR9YA0KICAgICAgICAgICAgY29uc3QgY3VycmVudFRhc2tDb3VudCA9IHByZVByb2Nlc3NEYXRhTWFwLmdldChiS2V5KSB8fCAwDQoNCiAgICAgICAgICAgIGlmIChyb3dbdGhpcy5nZXRSb3dDQ29kZShyb3cpXSA8IGN1cnJlbnRUYXNrQ291bnQpIHsNCiAgICAgICAgICAgICAgcmV0dXJuDQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIGNvbnN0IHNlbGVjdE51bSA9IE1hdGgubWluKA0KICAgICAgICAgICAgICByb3dbdGhpcy5nZXRSb3dDQ29kZShyb3csICdhbENvdW50JyldIHx8IDAsDQogICAgICAgICAgICAgIHJvd1t0aGlzLmdldFJvd0NDb2RlKHJvdyldIHx8IDAsDQogICAgICAgICAgICAgIGN1cnJlbnRUYXNrQ291bnQNCiAgICAgICAgICAgICkNCg0KICAgICAgICAgICAgaWYgKHNlbGVjdE51bSA+IDApIHsNCiAgICAgICAgICAgICAgY29uc3QgdGFyQ29kZSA9IHRoaXMuZ2V0Um93VW5pcXVlKHJvdy51dWlkLCB0ZWFtLlByb2Nlc3NfQ29kZSwgdGVhbS5Xb3JraW5nX1RlYW1fSWQpDQogICAgICAgICAgICAgIHJvd1t0aGlzLmdldFRhc2tDb2RlKHRlYW0uV29ya2luZ19UZWFtX05hbWUpXSA9IChyb3dbdGhpcy5nZXRUYXNrQ29kZSh0ZWFtLldvcmtpbmdfVGVhbV9OYW1lKV0gfHwgMCkgKyBzZWxlY3ROdW0NCiAgICAgICAgICAgICAgcm93W3RoaXMuZ2V0VGFza0NvZGUoKV0gPSAocm93W3RoaXMuZ2V0VGFza0NvZGUoKV0gfHwgMCkgLSBzZWxlY3ROdW0NCiAgICAgICAgICAgICAgcm93W3RhckNvZGVdID0gKE51bWJlcihyb3dbdGFyQ29kZV0pIHx8IDApICsgc2VsZWN0TnVtDQogICAgICAgICAgICAgIHJvd1t0aGlzLmdldFJvd0NDb2RlKHJvdyldID0gKHJvd1t0aGlzLmdldFJvd0NDb2RlKHJvdyldIHx8IDApIC0gc2VsZWN0TnVtDQogICAgICAgICAgICAgIHJvd1t0aGlzLmdldFJvd0NDb2RlKHJvdywgJ2FsQ291bnQnKV0gPSAocm93W3RoaXMuZ2V0Um93Q0NvZGUocm93LCAnYWxDb3VudCcpXSB8fCAwKSAtIHNlbGVjdE51bQ0KICAgICAgICAgICAgICByb3dbJ2FsQ291bnQnICsgdGFyQ29kZV0gPSByb3dbdGFyQ29kZV0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgLy8gaWYgKHRoaXMuYWN0aXZlTmFtZSA9PT0gJ2ZpcnN0Jykgew0KICAgICAgLy8gICBsZXQgSXNBbGxObyA9IDANCiAgICAgIC8vICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5mb3JFYWNoKChyb3csIGlkeCkgPT4gew0KICAgICAgLy8gICAgIGlmIChyb3cuQ2FuX0FsbG9jYXRpb25fQ291bnQpIHsNCiAgICAgIC8vICAgICAgIGNvbnN0IHZhbGlkVGVhbXMgPSByb3cuQWxsb2NhdGlvbl9UZWFtcy5maWx0ZXIodGVhbSA9PiB7DQogICAgICAvLyAgICAgICAgIGNvbnN0IGtleSA9IGAke3Jvdy5TY2hkdWxpbmdfRGV0YWlsX0lkfV8ke3RlYW0uV29ya2luZ19UZWFtX0lkfWANCiAgICAgIC8vICAgICAgICAgcmV0dXJuIHByZVByb2Nlc3NEYXRhTWFwLmhhcyhrZXkpDQogICAgICAvLyAgICAgICB9KQ0KICAgICAgLy8gICAgICAgaWYgKHZhbGlkVGVhbXMubGVuZ3RoID4gMCkgew0KICAgICAgLy8gICAgICAgICBjb25zdCB0ZWFtID0gdmFsaWRUZWFtc1swXQ0KICAgICAgLy8gICAgICAgICBjb25zdCB0YXJDb2RlID0gdGhpcy5nZXRSb3dVbmlxdWUocm93LnV1aWQsIHRlYW0uUHJvY2Vzc19Db2RlLCB0ZWFtLldvcmtpbmdfVGVhbV9JZCkNCg0KICAgICAgLy8gICAgICAgICBjb25zdCBwcmVQcm9jZXNzS2V5ID0gYCR7cm93LlNjaGR1bGluZ19EZXRhaWxfSWR9XyR7dGVhbS5Xb3JraW5nX1RlYW1fSWR9YA0KICAgICAgLy8gICAgICAgICBjb25zdCBjdXJyZW50VGFza0NvdW50ID0gcHJlUHJvY2Vzc0RhdGFNYXAuZ2V0KHByZVByb2Nlc3NLZXkpIHx8IDANCg0KICAgICAgLy8gICAgICAgICBpZiAoY3VycmVudFRhc2tDb3VudCA+IHJvdy5hbGxvY2F0ZWRUYXNrTWF4KSB7DQogICAgICAvLyAgICAgICAgICAgSXNBbGxObysrDQogICAgICAvLyAgICAgICAgICAgcmV0dXJuDQogICAgICAvLyAgICAgICAgIH0NCiAgICAgIC8vICAgICAgICAgY29uc3QgYWxsb2NhdGVkID0gTWF0aC5taW4ocm93LmFsbG9jYXRlZFRhc2ssIGN1cnJlbnRUYXNrQ291bnQpDQoNCiAgICAgIC8vICAgICAgICAgcm93W3RhckNvZGVdID0gTnVtYmVyKHJvd1t0YXJDb2RlXSB8fCAwKSArIGFsbG9jYXRlZA0KICAgICAgLy8gICAgICAgICByb3cuQ2FuX0FsbG9jYXRpb25fQ291bnQgPSByb3cuQ2FuX0FsbG9jYXRpb25fQ291bnQgLSBhbGxvY2F0ZWQNCiAgICAgIC8vICAgICAgICAgcm93W3RoaXMuZ2V0VGFza0NvZGUodGVhbS5Xb3JraW5nX1RlYW1fTmFtZSkgfHwgMF0gPSAocm93W3RoaXMuZ2V0VGFza0NvZGUodGVhbS5Xb3JraW5nX1RlYW1fTmFtZSldIHx8IDApICsgYWxsb2NhdGVkDQogICAgICAvLyAgICAgICAgIHJvd1t0aGlzLmdldFRhc2tDb2RlKCldID0gKHJvd1t0aGlzLmdldFRhc2tDb2RlKCldIHx8IDApIC0gYWxsb2NhdGVkDQogICAgICAvLyAgICAgICAgIHJvdy5hbGxvY2F0ZWRUYXNrTWF4ID0gcm93LkNhbl9BbGxvY2F0aW9uX0NvdW50DQogICAgICAvLyAgICAgICAgIHJvdy5hbGxvY2F0ZWRUYXNrID0gcm93LkNhbl9BbGxvY2F0aW9uX0NvdW50DQogICAgICAvLyAgICAgICAgIHJvd1snYWxDb3VudCcgKyB0YXJDb2RlXSA9IHJvd1t0YXJDb2RlXQ0KICAgICAgLy8gICAgICAgfQ0KICAgICAgLy8gICAgIH0NCiAgICAgIC8vICAgfSkNCiAgICAgIC8vICAgaWYgKElzQWxsTm8gPT09IHRoaXMubXVsdGlwbGVTZWxlY3Rpb24ubGVuZ3RoKSB7DQogICAgICAvLyAgICAgdGhpcy5kaWFsb2dUaXBzVmlzaWJsZSA9IHRydWUNCiAgICAgIC8vICAgICByZXR1cm4NCiAgICAgIC8vICAgfQ0KICAgICAgLy8gfSBlbHNlIHsNCiAgICAgIC8vICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5mb3JFYWNoKChyb3csIGlkeCkgPT4gew0KICAgICAgLy8gICAgIGNvbnN0IHZhbGlkVGVhbXMgPSByb3cuQWxsb2NhdGlvbl9UZWFtcy5maWx0ZXIodGVhbSA9PiB7DQogICAgICAvLyAgICAgICBjb25zdCBrZXkgPSBgJHtyb3cuU2NoZHVsaW5nX0RldGFpbF9JZH1fJHt0ZWFtLldvcmtpbmdfVGVhbV9JZH1gDQogICAgICAvLyAgICAgICByZXR1cm4gcHJlUHJvY2Vzc0RhdGFNYXAuaGFzKGtleSkNCiAgICAgIC8vICAgICB9KQ0KICAgICAgLy8gICAgIGlmICh2YWxpZFRlYW1zLmxlbmd0aCA+IDApIHsNCiAgICAgIC8vICAgICAgIGNvbnN0IHRlYW0gPSB2YWxpZFRlYW1zWzBdDQogICAgICAvLyAgICAgICBjb25zdCB0YXJDb2RlID0gdGhpcy5nZXRSb3dVbmlxdWUocm93LnV1aWQsIHRlYW0uUHJvY2Vzc19Db2RlLCB0ZWFtLldvcmtpbmdfVGVhbV9JZCkNCg0KICAgICAgLy8gICAgICAgY29uc3QgcHJlUHJvY2Vzc0tleSA9IGAke3Jvdy5TY2hkdWxpbmdfRGV0YWlsX0lkfV8ke3RlYW0uV29ya2luZ19UZWFtX0lkfWANCiAgICAgIC8vICAgICAgIGNvbnN0IGN1cnJlbnRUYXNrQ291bnQgPSBwcmVQcm9jZXNzRGF0YU1hcC5nZXQocHJlUHJvY2Vzc0tleSkgfHwgMA0KDQogICAgICAvLyAgICAgICBjb25zdCBzZWxlY3ROdW0gPSBNYXRoLm1pbigNCiAgICAgIC8vICAgICAgICAgcm93W3RoaXMuZ2V0Um93Q0NvZGUocm93LCAnYWxDb3VudCcpXSB8fCAwLA0KICAgICAgLy8gICAgICAgICByb3dbdGhpcy5nZXRSb3dDQ29kZShyb3cpXSB8fCAwLA0KICAgICAgLy8gICAgICAgICBjdXJyZW50VGFza0NvdW50DQogICAgICAvLyAgICAgICApDQoNCiAgICAgIC8vICAgICAgIHJvd1t0aGlzLmdldFRhc2tDb2RlKHRlYW0uV29ya2luZ19UZWFtX05hbWUpXSA9IChyb3dbdGhpcy5nZXRUYXNrQ29kZSh0ZWFtLldvcmtpbmdfVGVhbV9OYW1lKV0gfHwgMCkgKyBzZWxlY3ROdW0NCiAgICAgIC8vICAgICAgIHJvd1t0aGlzLmdldFRhc2tDb2RlKCldID0gKHJvd1t0aGlzLmdldFRhc2tDb2RlKCldIHx8IDApIC0gc2VsZWN0TnVtDQogICAgICAvLyAgICAgICByb3dbdGFyQ29kZV0gPSAoTnVtYmVyKHJvd1t0YXJDb2RlXSkgfHwgMCkgKyBzZWxlY3ROdW0NCiAgICAgIC8vICAgICAgIHJvd1t0aGlzLmdldFJvd0NDb2RlKHJvdyldID0gKHJvd1t0aGlzLmdldFJvd0NDb2RlKHJvdyldIHx8IDApIC0gc2VsZWN0TnVtDQogICAgICAvLyAgICAgICByb3dbdGhpcy5nZXRSb3dDQ29kZShyb3csICdhbENvdW50JyldID0gKHJvd1t0aGlzLmdldFJvd0NDb2RlKHJvdywgJ2FsQ291bnQnKV0gfHwgMCkgLSBzZWxlY3ROdW0NCiAgICAgIC8vICAgICAgIHJvd1snYWxDb3VudCcgKyB0YXJDb2RlXSA9IHJvd1t0YXJDb2RlXQ0KICAgICAgLy8gICAgIH0NCiAgICAgIC8vICAgfSkNCiAgICAgIC8vIH0NCiAgICAgIHRoaXMuZmlsdGVyVGJEYXRhID0gdGhpcy50YkRhdGEuZmlsdGVyKHJvdyA9PiB7DQogICAgICAgIHJldHVybiB0aGlzLmZpbHRlclplcm8ocm93KQ0KICAgICAgfSkNCiAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24gPSBbXQ0KICAgICAgdGhpcy4kcmVmcy54VGFibGUuY2xlYXJDaGVja2JveFJvdygpDQogICAgICBpZiAoaXNNZXNzYWdlKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICflkIzmraXmiJDlip8nLA0KICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5om56YeP5YiG6YWN5o+Q5LqkDQogICAgZG9BbGxvY2F0aW9uKCkgew0KICAgICAgaWYgKHRoaXMuYWN0aXZlTmFtZSA9PT0gJ2ZpcnN0Jykgew0KICAgICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uDQogICAgICAgICAgLmZvckVhY2goKHJvdywgaWR4KSA9PiB7DQogICAgICAgICAgICBpZiAocm93LkNhbl9BbGxvY2F0aW9uX0NvdW50KSB7DQogICAgICAgICAgICAgIGNvbnN0IHRlYW0gPSByb3cuQWxsb2NhdGlvbl9UZWFtcy5maW5kKHYgPT4gdi5Xb3JraW5nX1RlYW1fSWQgPT09IHRoaXMuZm9ybS5UZWFtR3JvdXApDQogICAgICAgICAgICAgIGNvbnN0IHRhckNvZGUgPSB0aGlzLmdldFJvd1VuaXF1ZShyb3cudXVpZCwgdGVhbS5Qcm9jZXNzX0NvZGUsIHRlYW0uV29ya2luZ19UZWFtX0lkKQ0KICAgICAgICAgICAgICByb3dbdGFyQ29kZV0gPSBOdW1iZXIocm93W3RhckNvZGVdKSArIHJvdy5hbGxvY2F0ZWRUYXNrDQogICAgICAgICAgICAgIHJvdy5DYW5fQWxsb2NhdGlvbl9Db3VudCA9IHJvdy5DYW5fQWxsb2NhdGlvbl9Db3VudCAtIHJvdy5hbGxvY2F0ZWRUYXNrDQogICAgICAgICAgICAgIHJvd1t0aGlzLmdldFRhc2tDb2RlKHRlYW0uV29ya2luZ19UZWFtX05hbWUpXSArPSByb3cuYWxsb2NhdGVkVGFzaw0KICAgICAgICAgICAgICByb3dbdGhpcy5nZXRUYXNrQ29kZSgpXSAtPSByb3cuYWxsb2NhdGVkVGFzaw0KICAgICAgICAgICAgICByb3cuYWxsb2NhdGVkVGFza01heCA9IHJvdy5DYW5fQWxsb2NhdGlvbl9Db3VudA0KICAgICAgICAgICAgICByb3cuYWxsb2NhdGVkVGFzayA9IHJvdy5DYW5fQWxsb2NhdGlvbl9Db3VudA0KICAgICAgICAgICAgICByb3dbJ2FsQ291bnQnICsgdGFyQ29kZV0gPSByb3dbdGFyQ29kZV0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbg0KICAgICAgICAgIC5mb3JFYWNoKChyb3csIGlkeCkgPT4gew0KICAgICAgICAgICAgY29uc3QgdGVhbSA9IHJvdy5BbGxvY2F0aW9uX1RlYW1zLmZpbmQodiA9PiB2LldvcmtpbmdfVGVhbV9JZCA9PT0gdGhpcy5mb3JtLlRlYW1Hcm91cCkNCiAgICAgICAgICAgIGNvbnN0IHRhckNvZGUgPSB0aGlzLmdldFJvd1VuaXF1ZShyb3cudXVpZCwgdGVhbS5Qcm9jZXNzX0NvZGUsIHRlYW0uV29ya2luZ19UZWFtX0lkKQ0KDQogICAgICAgICAgICBjb25zdCBzZWxlY3ROdW0gPSBNYXRoLm1pbihyb3dbdGhpcy5nZXRSb3dDQ29kZShyb3csICdhbENvdW50JyldLCByb3dbdGhpcy5nZXRSb3dDQ29kZShyb3cpXSkNCg0KICAgICAgICAgICAgcm93W3RoaXMuZ2V0VGFza0NvZGUodGVhbS5Xb3JraW5nX1RlYW1fTmFtZSldICs9IHNlbGVjdE51bQ0KICAgICAgICAgICAgcm93W3RoaXMuZ2V0VGFza0NvZGUoKV0gLT0gc2VsZWN0TnVtDQoNCiAgICAgICAgICAgIHJvd1t0YXJDb2RlXSA9IE51bWJlcihyb3dbdGFyQ29kZV0pICsgc2VsZWN0TnVtDQogICAgICAgICAgICByb3dbdGhpcy5nZXRSb3dDQ29kZShyb3cpXSAtPSBzZWxlY3ROdW0NCiAgICAgICAgICAgIHJvd1t0aGlzLmdldFJvd0NDb2RlKHJvdywgJ2FsQ291bnQnKV0gLT0gc2VsZWN0TnVtDQoNCiAgICAgICAgICAgIHJvd1snYWxDb3VudCcgKyB0YXJDb2RlXSA9IHJvd1t0YXJDb2RlXQ0KICAgICAgICAgIH0pDQogICAgICB9DQogICAgICB0aGlzLmZpbHRlclRiRGF0YSA9IHRoaXMudGJEYXRhLmZpbHRlcihyb3cgPT4gew0KICAgICAgICByZXR1cm4gdGhpcy5maWx0ZXJaZXJvKHJvdykNCiAgICAgIH0pDQogICAgfSwNCiAgICByZXNldEZvcm0oZm9ybU5hbWUpIHsNCiAgICAgIHRoaXMuJHJlZnNbZm9ybU5hbWVdLnJlc2V0RmllbGRzKCkNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgfSwNCiAgICBmaWx0ZXJUeXBlTWV0aG9kKHsgb3B0aW9uLCByb3cgfSkgew0KICAgICAgcmV0dXJuIHJvdy5UeXBlLmluY2x1ZGVzKG9wdGlvbi5kYXRhKQ0KICAgIH0sDQogICAgZmlsdGVyVHlwZVJlY292ZXJNZXRob2QoeyBvcHRpb24gfSkgew0KICAgICAgb3B0aW9uLmRhdGEgPSAnJw0KICAgIH0sDQogICAgZmlsdGVyQ29kZU1ldGhvZCh7IG9wdGlvbiwgcm93IH0pIHsNCiAgICAgIGNvbnNvbGUubG9nKCdvcHRpb24sIHJvdycsIG9wdGlvbiwgcm93KQ0KICAgICAgcmV0dXJuIHJvd1t0aGlzLmlzQ29tID8gJ0NvbXBfQ29kZScgOiAnUGFydF9Db2RlJ10uaW5jbHVkZXMob3B0aW9uLmRhdGEpDQogICAgfSwNCiAgICBmaWx0ZXJDb2RlUmVjb3Zlck1ldGhvZCh7IG9wdGlvbiB9KSB7DQogICAgICBvcHRpb24uZGF0YSA9ICcnDQogICAgfSwNCiAgICBnZXRPYmplY3RUeXBlTGlzdCgpIHsNCiAgICAgIEdldENvbXBUeXBlVHJlZSh7IHByb2Zlc3Npb25hbDogJ1N0ZWVsJyB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLk9iamVjdFR5cGVMaXN0LmRhdGEgPSByZXMuRGF0YQ0KICAgICAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgICAgICB0aGlzLiRyZWZzPy50cmVlU2VsZWN0T2JqZWN0VHlwZT8udHJlZURhdGFVcGRhdGVGdW4ocmVzLkRhdGEpDQogICAgICAgICAgfSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsDQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAi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file": "detail.vue", "sourceRoot": "src/views/PRO/plan-production/task-allocation/v4", "sourcesContent": ["<template>\r\n  <div v-loading=\"loading\" class=\"app-container abs100\">\r\n    <el-card class=\"box-card h100\">\r\n      <h4 class=\"topTitle\"><span />基本信息</h4>\r\n\r\n      <el-form\r\n        ref=\"formInline\"\r\n        label-position=\"right\"\r\n        label-width=\"90px\"\r\n        :inline=\"true\"\r\n        :model=\"formInline\"\r\n        class=\"demo-form-inline\"\r\n      >\r\n        <el-row>\r\n          <el-col :span=\"20\">\r\n            <el-row>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"排产单号:\" label-width=\"75px\" prop=\"Schduling_Code\">\r\n                  <span>{{ formInline.Schduling_Code }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"项目名称:\" prop=\"Project_Name\">\r\n                  <span>{{ formInline.Project_Name }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"区域:\" prop=\"Area_Name\">\r\n                  <span>{{ formInline.Area_Name }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"批次:\" prop=\"Installunit_Name\">\r\n                  <span>{{ formInline.Installunit_Name }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"任务数量:\" label-width=\"75px\" prop=\"Allocation_Count\">\r\n                  <span>{{ formInline.Allocation_Count }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"任务重量:\" prop=\"Allocation_Weight\">\r\n                  <span>{{ formInline.Allocation_Weight | filterNum }}(t)</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"已完成数量:\" prop=\"Finish_Count\">\r\n                  <span>{{ formInline.Finish_Count }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"已完成重量:\" prop=\"Finish_Weight\">\r\n                  <span>{{ formInline.Finish_Weight | filterNum }}(t)</span>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <qrcode-vue\r\n              :size=\"79\"\r\n              :value=\"formInline.Schduling_Code\"\r\n              class-name=\"qrcode\"\r\n              level=\"H\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <el-divider class=\"elDivder\" />\r\n      <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\r\n        <el-tab-pane label=\"待分配\" name=\"first\" />\r\n        <el-tab-pane\r\n          v-for=\"(element, index2) in workingTeam\"\r\n          :key=\"index2\"\r\n          :label=\"element.Working_Team_Name\"\r\n          :name=\"`${element.Working_Team_Name}$_$${element.Process_Code}`\"\r\n        />\r\n      </el-tabs>\r\n      <div class=\"tb-options\" :style=\"{'justify-content': !isView ? 'space-between' : 'end'}\">\r\n        <div>\r\n          <el-button v-if=\"!isView\" plain type=\"primary\" @click=\"reverseSelection()\">反选</el-button>\r\n          <el-button v-if=\"!isView\" type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"Batchallocation()\">批量分配</el-button>\r\n          <el-button v-if=\"!isView && activeName==='first'\" type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"preStepTaskAllocation()\">上道工序同步</el-button>\r\n        </div>\r\n        <el-form :inline=\"true\" :model=\"queryForm\" class=\"demo-form-inline\">\r\n          <el-form-item label=\"规格\">\r\n            <el-input v-model.trim=\"queryForm.Spec\" clearable placeholder=\"请输入\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"isCom\" :label=\"isCom ? '构件类型' :isUnitPart?'部件类型':'零件类型'\">\r\n            <el-tree-select\r\n              ref=\"treeSelectObjectType\"\r\n              v-model=\"searchType\"\r\n              style=\"width: 100%\"\r\n              class=\"cs-tree-x\"\r\n              :select-params=\"treeSelectParams\"\r\n              :tree-params=\"ObjectTypeList\"\r\n              value-key=\"Id\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item :label=\"isCom ? '构件编号' :isUnitPart?'部件名称':'零件名称'\">\r\n            <el-input v-if=\"isCom\" v-model=\"queryForm.Comp_Codes\" clearable placeholder=\"请输入(空格区分/多个搜索)\" />\r\n            <el-input v-else v-model=\"queryForm.Part_Code\" clearable placeholder=\"请输入(空格区分/多个搜索)\" />\r\n            <el-input v-if=\"isCom\" v-model.trim=\"queryForm.Comp_Codes_Vague\" style=\"margin-left: 10px;\" clearable placeholder=\"模糊查找(请输入关键字)\" />\r\n            <el-input v-else v-model.trim=\"queryForm.Part_Code_Vague\" style=\"margin-left: 10px;\" clearable placeholder=\"模糊查找(请输入关键字)\" />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" @click=\"filterData\">查询</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n      <div class=\"tb-x\">\r\n        <vxe-table\r\n          ref=\"xTable\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          class=\"cs-vxe-table\"\r\n          :row-config=\"{ isCurrent: true, isHover: true }\"\r\n          align=\"left\"\r\n          height=\"100%\"\r\n          show-overflow\r\n          :loading=\"tbLoading\"\r\n          :checkbox-config=\"{checkField: 'checked',checkMethod }\"\r\n          stripe\r\n          size=\"medium\"\r\n          :edit-config=\"{\r\n            trigger: 'click',\r\n            mode: 'cell',\r\n            showIcon: !isView,\r\n            beforeEditMethod: activeCellMethod,\r\n          }\"\r\n          :data=\"filterTbData\"\r\n          resizable\r\n          :tooltip-config=\"{ enterable: true }\"\r\n          @checkbox-all=\"tbSelectChange\"\r\n          @checkbox-change=\"tbSelectChange\"\r\n        >\r\n          <vxe-column type=\"checkbox\" width=\"60\" fixed=\"left\" />\r\n          <template v-for=\"item in columns\">\r\n\r\n            <!--            <vxe-column :align=\"item.Align\"\r\n              v-if=\"item.Code==='Comp_Code'||item.Code==='Part_Code'\"\r\n              :key=\"item.Id\"\r\n              :filters=\"[{ data: '' }]\"\r\n              :filter-method=\"filterCodeMethod\"\r\n              :filter-recover-method=\"filterCodeRecoverMethod\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #filter=\"{ $panel, column }\">\r\n                <template v-for=\"(option, index) in column.filters\">\r\n                  <input :key=\"index\" v-model=\"option.data\" class=\"my-input\" type=\"type\" placeholder=\"按回车确认筛选\" @input=\"$panel.changeOption($event, !!option.data, option)\" @keyup.enter=\"$panel.confirmFilter()\">\r\n                </template>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column :align=\"item.Align\"\r\n              v-else-if=\"item.Code==='Type'\"\r\n              :key=\"item.Id\"\r\n              :filters=\"[{ data: '' }]\"\r\n              :filter-method=\"filterTypeMethod\"\r\n              :filter-recover-method=\"filterTypeRecoverMethod\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #filter=\"{ $panel, column }\">\r\n                <template v-for=\"(option, index) in column.filters\">\r\n                  <input :key=\"index\" v-model=\"option.data\" class=\"my-input\" type=\"type\" placeholder=\"按回车确认筛选\" @input=\"$panel.changeOption($event, !!option.data, option)\" @keyup.enter=\"$panel.confirmFilter()\">\r\n                </template>\r\n              </template>\r\n            </vxe-column>-->\r\n            <vxe-column\r\n              v-if=\"item.Code==='Comp_Code'|| item.Code==='Part_Code'\"\r\n              :key=\"item.Id\"\r\n              :align=\"item.Align\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :visible=\"item.visible\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                <el-tag v-if=\"!!row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                <span>{{ row[item.Code] }}</span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column\r\n              v-else-if=\"item.Code==='Schduled_Count'\"\r\n              :key=\"`SchduledCount${item.Id}`\"\r\n              :align=\"item.Align\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :visible=\"item.visible\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                {{ activeName === 'first' ?'':row[getTaskCode()] }}\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column\r\n              v-else\r\n              :key=\"`Default${item.Id}`\"\r\n              :align=\"item.Align\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              :visible=\"item.visible\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n            />\r\n          </template>\r\n\r\n          <vxe-column\r\n            v-for=\"(element, index2) in workingTeamColumn\"\r\n            :key=\"index2\"\r\n            :align=\"element.Align\"\r\n            :visible=\"element.visible\"\r\n            fixed=\"right\"\r\n            :field=\"`${element.Working_Team_Name}$_$${element.Process_Code}`\"\r\n            title=\"可分配数量\"\r\n            sortable\r\n            min-width=\"170\"\r\n          >\r\n            <template #edit=\"{ row }\">\r\n              <vxe-input\r\n                v-model.number=\"\r\n                  row[\r\n                    getRowUnique(\r\n                      row.uuid,\r\n                      element.Process_Code,\r\n                      element.Working_Team_Id\r\n                    )\r\n                  ]\r\n                \"\r\n                type=\"integer\"\r\n                :min=\"0\"\r\n                :max=\"\r\n                  row[\r\n                    getRowUniqueMax(\r\n                      row.uuid,\r\n                      element.Process_Code,\r\n                      element.Working_Team_Id\r\n                    )\r\n                  ]\r\n                \"\r\n                @change=\"\r\n                  inputChange(\r\n                    row,\r\n                    element.Process_Code,\r\n                    element.Working_Team_Id\r\n                  )\r\n                \"\r\n              />\r\n            </template>\r\n            <template #default=\"{ row }\">\r\n              <template\r\n                v-if=\"\r\n                  checkPermissionTeam(row.Technology_Path, element.Process_Code)\r\n                \"\r\n              >\r\n                {{\r\n                  row[\r\n                    getRowUnique(\r\n                      row.uuid,\r\n                      element.Process_Code,\r\n                      element.Working_Team_Id\r\n                    )\r\n                  ]\r\n                }}\r\n              </template>\r\n              <template v-else> -</template>\r\n            </template>\r\n          </vxe-column>\r\n\r\n          <vxe-column\r\n            :key=\"activeName\"\r\n            align=\"left\"\r\n            :edit-render=\"{}\"\r\n            field=\"AllocatedCount\"\r\n            title=\"分配数量\"\r\n            sortable\r\n            fixed=\"right\"\r\n            min-width=\"180\"\r\n          >\r\n            <template #edit=\"{ row }\">\r\n              <vxe-input\r\n                :key=\"activeName\"\r\n                v-model.number=\"row[getRowCCode(row,'alCount')]\"\r\n                type=\"integer\"\r\n                :min=\"0\"\r\n                :max=\"row[getRowCCode(row,'','Max')]\"\r\n              />\r\n            </template>\r\n            <template #default=\"{ row }\">\r\n              {{ row[getRowCCode(row,'alCount')] | displayValue }}\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n      </div>\r\n      <footer>\r\n        <div class=\"data-info\">\r\n          <el-tag size=\"medium\" class=\"info-x\">已选{{ multipleSelection.length }}条数据</el-tag>\r\n          <el-tag v-if=\"tipLabel\" size=\"medium\" class=\"info-x\">{{ tipLabel }}</el-tag>\r\n        </div>\r\n        <div>\r\n          <el-button @click=\"handleClose\">取消 </el-button>\r\n          <el-button\r\n            v-if=\"!isView\"\r\n            type=\"primary\"\r\n            :loading=\"loading\"\r\n            @click=\"handleSubmit\"\r\n          >提交</el-button>\r\n        </div>\r\n      </footer>\r\n    </el-card>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      title=\"批量分配\"\r\n      class=\"plm-custom-dialog\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"30%\"\r\n      @close=\"handleDialog\"\r\n    >\r\n      <el-form\r\n        ref=\"form\"\r\n        :model=\"form\"\r\n        :rules=\"rules\"\r\n        label-width=\"80px\"\r\n        class=\"demo-ruleForm\"\r\n      >\r\n        <el-form-item label=\"选择班组\" prop=\"TeamGroup\">\r\n          <el-select\r\n            v-model=\"form.TeamGroup\"\r\n            class=\"w100\"\r\n            placeholder=\"请选择\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"item in workingTeam\"\r\n              :key=\"item.Working_Team_Id\"\r\n              :label=\"item.Working_Team_Name\"\r\n              :value=\"item.Working_Team_Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item style=\"text-align: right\">\r\n          <el-button @click=\"resetForm('form')\">取 消</el-button>\r\n          <el-button\r\n            type=\"primary\"\r\n            @click=\"submitForm('form');resetForm('form')\"\r\n          >确 定</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-dialog>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      title=\"提示\"\r\n      class=\"plm-custom-dialog\"\r\n      :visible.sync=\"dialogTipsVisible\"\r\n      width=\"450px\"\r\n      @close=\"handleDialog\"\r\n    >\r\n      <div style=\"text-align: center; font-size: 16px;\">部分{{ isCom ? '构件' : isUnitPart ? '部件' : '零件' }}与上道工序加工班组不同，请手动分配</div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogTipsVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"dialogTipsVisible = false\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { FIX_COLUMN } from '@/views/PRO/plan-production/schedule-production/constant'\r\nimport {\r\n  AdjustTeamProcessAllocation,\r\n  GetTeamProcessAllocation,\r\n  AdjustPartTeamProcessAllocation,\r\n  AdjustSubAssemblyTeamProcessAllocation,\r\n  GetStopList,\r\n  GetPreStepTaskAllocation\r\n} from '@/api/PRO/production-task'\r\nimport QrcodeVue from 'qrcode.vue'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport numeral from 'numeral'\r\nimport { closeTagView, deepClone } from '@/utils'\r\nimport { GetCompTypeTree } from '@/api/PRO/professionalType'\r\n\r\nconst SPLIT_SYMBOL = '$_$'\r\nexport default {\r\n  components: {\r\n    QrcodeVue\r\n  },\r\n  filters: {\r\n    filterNum(value) {\r\n      return numeral(value).divide(1000).format('0.[00]')\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        clearable: true\r\n      },\r\n      ObjectTypeList: {\r\n        // 构件类型\r\n        'check-strictly': true,\r\n        'default-expand-all': true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      },\r\n      tbLoading: false,\r\n      loading: false,\r\n      activeName: 'first',\r\n      tipLabel: '',\r\n      tbData: [],\r\n      filterTbData: [],\r\n      multipleSelection: [],\r\n      columns: [],\r\n      workingTeam: [],\r\n      workingTeamColumn: [],\r\n      formInline: {},\r\n      pg_type: '',\r\n      searchType: '',\r\n      type: '',\r\n      queryForm: {\r\n        Comp_Codes: '',\r\n        Part_Code: '',\r\n        Spec: '',\r\n        Comp_Codes_Vague: '',\r\n        Part_Code_Vague: ''\r\n      },\r\n      dialogVisible: false,\r\n      dialogTipsVisible: false,\r\n      form: {\r\n        TeamGroup: '' // 班组\r\n      },\r\n\r\n      rules: {\r\n        TeamGroup: [\r\n          { required: true, message: '请输入班组名称', trigger: 'change' }\r\n        ]\r\n      },\r\n      Is_Workshop_Enabled: false,\r\n      Working_Process_Id: ''\r\n    }\r\n  },\r\n  computed: {\r\n    isView() {\r\n      return this.type === 'view'\r\n    },\r\n    isCom() {\r\n      return this.pg_type === 'com'\r\n    },\r\n    isUnitPart() {\r\n      return this.pg_type === 'unitPart'\r\n    }\r\n  },\r\n\r\n  async mounted() {\r\n    try {\r\n      const rowInfo = JSON.parse(decodeURIComponent(this.$route.query.other))\r\n      console.log('rowInfo', rowInfo)\r\n      this.formInline = Object.assign({}, this.formInline, rowInfo)\r\n      this.pg_type = this.$route.query.pg_type\r\n      this.bomLevel = this.$route.query.bomLevel\r\n      this.type = this.$route.query.type\r\n      this.Is_Workshop_Enabled = this.$route.query.Is_Workshop_Enabled\r\n      await this.getTableConfig(this.isUnitPart ? 'PROTaskUnitAllocationChange' : 'PROTaskAllocationChange')\r\n      if (this.isCom) {\r\n        const idx = this.columns.findIndex(item => item.Code === 'Part_Code')\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n        const idx2 = this.columns.findIndex(item => item.Code === 'Component_Code')\r\n        idx2 !== -1 && this.columns.splice(idx2, 1)\r\n      } else {\r\n        const idx = this.columns.findIndex(item => item.Code === 'Comp_Code')\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n        const idx2 = this.columns.findIndex(item => item.Code === 'Type')\r\n        idx2 !== -1 && this.columns.splice(idx2, 1)\r\n      }\r\n      if (!this.Is_Workshop_Enabled) {\r\n        const idx3 = this.columns.findIndex(item => item.Code === 'Workshop_Name')\r\n        idx3 !== -1 && this.columns.splice(idx3, 1)\r\n      }\r\n      this.getObjectTypeList()\r\n      this.fetchData()\r\n    } catch (e) {\r\n      this.$message({\r\n        message: '参数错误,请重新操作',\r\n        type: 'error'\r\n      })\r\n    }\r\n  },\r\n  methods: {\r\n    getRowCCode(row, prefix = '', suffix = '') {\r\n      if (this.activeName === 'first') {\r\n        return 'allocatedTask' + suffix\r\n      } else {\r\n        const arr = this.activeName.split(SPLIT_SYMBOL)\r\n        const team = this.workingTeam.find(v => v.Working_Team_Name === arr[0])\r\n        const u = this.getRowUnique(row.uuid, row.Process_Code, team.Working_Team_Id)\r\n        if (suffix === 'Max') {\r\n          return u\r\n        } else {\r\n          return prefix + u + suffix\r\n        }\r\n      }\r\n    },\r\n    handleClick(val) {\r\n      console.log('handleClick', val)\r\n      if (val.name === 'first') {\r\n        const c1 = this.$refs.xTable.getColumnByField('Schduled_Count')\r\n        const c2 = this.$refs.xTable.getColumnByField('Can_Allocation_Count')\r\n        c1.visible = false\r\n        c2.visible = true\r\n      } else {\r\n        const c2 = this.$refs.xTable.getColumnByField('Schduled_Count')\r\n        const c3 = this.$refs.xTable.getColumnByField('Can_Allocation_Count')\r\n        c2.visible = true\r\n        c3.visible = false\r\n      }\r\n\r\n      this.workingTeam.forEach((element, idx) => {\r\n        const codes = `${element.Working_Team_Name}$_$${element.Process_Code}`\r\n\r\n        const c = this.$refs.xTable.getColumnByField(codes)\r\n\r\n        c.visible = codes === val.name\r\n      })\r\n      this.$refs.xTable.refreshColumn()\r\n\r\n      this.filterTbData = this.tbData.filter(row => {\r\n        return this.filterZero(row)\r\n      })\r\n\r\n      this.multipleSelection = []\r\n      this.$refs.xTable.clearCheckboxRow()\r\n    },\r\n    filterZero(row) {\r\n      if (this.activeName === 'first') {\r\n        return row.allocatedTask > 0\r\n      } else {\r\n        const arr = this.activeName.split(SPLIT_SYMBOL)\r\n        const team = this.workingTeam.find(v => v.Working_Team_Name === arr[0])\r\n        const code = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\r\n        return row[code] > 0\r\n      }\r\n    },\r\n    fetchData() {\r\n      const Comp_Codes = !this.queryForm.Comp_Codes ? [] : this.queryForm.Comp_Codes.trim().split(' ')\r\n      const Part_Code = !this.queryForm.Part_Code ? [] : this.queryForm.Part_Code.trim().split(' ')\r\n      let Process_Type = 2\r\n      Process_Type = this.isCom ? 2 : this.isUnitPart ? 3 : 1\r\n      this.tbLoading = true\r\n      GetTeamProcessAllocation({\r\n        Page: 1,\r\n        PageSize: -1,\r\n        Step: this.formInline.Step,\r\n        Process_Type,\r\n        Bom_Level: this.bomLevel,\r\n        Schduling_Code: this.formInline.Schduling_Code,\r\n        Process_Code: this.formInline.Process_Code,\r\n        Workshop_Name: this.formInline.Workshop_Name,\r\n        Area_Id: this.formInline.Area_Id,\r\n        InstallUnit_Id: this.formInline.InstallUnit_Id,\r\n        Comp_Codes,\r\n        Part_Code\r\n      }).then(async(res) => {\r\n        if (res.IsSucceed) {\r\n          const { Schduling_Plan, Schduling_Comps } = res.Data\r\n          this.planInfoTemp = Schduling_Plan\r\n          await this.getStopList(Schduling_Comps)\r\n          this.initTbData(Schduling_Comps)\r\n          this.Working_Process_Id = res.Data.Working_Process_Id\r\n\r\n          if (Schduling_Comps.length) {\r\n            this.workingTeam = Schduling_Comps[0].Allocation_Teams\r\n            const _kk = this.workingTeam.map(v => {\r\n              v.visible = false\r\n              return v\r\n            })\r\n            this.workingTeamColumn = deepClone(_kk)\r\n          }\r\n          console.log(' this.tbData', this.tbData)\r\n          this.filterData()\r\n          console.log('filterTbData', this.filterTbData)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    checkMethod({ row }) {\r\n      return !row.stopFlag\r\n    },\r\n    async getStopList(list) {\r\n      const key = 'Id'\r\n      const submitObj = list.map(item => {\r\n        return {\r\n          Id: item[key],\r\n          Bom_Level: this.bomLevel,\r\n          Type: this.isCom ? 2 : this.isUnitPart ? 3 : 1 // 1：零件，3：部件，2：构件\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = !!item.Is_Stop\r\n          })\r\n          list.forEach(row => {\r\n            if (stopMap[row[key]]) {\r\n              this.$set(row, 'stopFlag', stopMap[row[key]])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    filterData() {\r\n      console.log('searchType', this.searchType)\r\n      this.multipleSelection = []\r\n      this.$refs.xTable.clearCheckboxRow()\r\n      const code = this.isCom ? 'Comp_Code' : 'Part_Code'\r\n      const queryCode = this.isCom ? 'Comp_Codes' : 'Part_Code'\r\n      const codeList = this.queryForm[queryCode].split(' ').filter(v => !!v)\r\n\r\n      const queryCodeVague = this.isCom ? 'Comp_Codes_Vague' : 'Part_Code_Vague'\r\n      const codeListVague = this.queryForm[queryCodeVague]\r\n\r\n      const searchTbData = this.tbData.filter(v => {\r\n        if (!codeList.length && codeListVague === '') {\r\n          return true\r\n        } else {\r\n          return codeList.includes(v[code])\r\n        }\r\n      })\r\n\r\n      const vagueData = searchTbData.length > 0 && codeListVague === '' ? searchTbData : searchTbData.length === 0 && codeListVague === '' ? searchTbData : this.tbData\r\n      const searchVagueTbData = vagueData.filter(v => {\r\n        if (codeListVague === '' && !codeList.length) {\r\n          return true\r\n        } else {\r\n          return v[code].includes(codeListVague)\r\n        }\r\n      })\r\n\r\n      // 合并两个数组\r\n      const mergedArray = searchTbData.concat(searchVagueTbData)\r\n      // 根据 Schduling_Detail_Id 进行去重\r\n      const uniqueArray = mergedArray.reduce((acc, current) => {\r\n        const existingObject = acc.find(item => item.Schduling_Detail_Id === current.Schduling_Detail_Id)\r\n        if (!existingObject) {\r\n          acc.push(current)\r\n        }\r\n        return acc\r\n      }, [])\r\n\r\n      this.filterTbData = uniqueArray.filter(v => {\r\n        if (!this.searchType) return true\r\n        return this.searchType === v.Type\r\n      }).filter(v => {\r\n        if (!this.queryForm.Spec) return true\r\n        return (v.Spec || '').includes(this.queryForm.Spec)\r\n      }).filter(row => {\r\n        return this.filterZero(row)\r\n      })\r\n    },\r\n    initTbData(list, teamKey = 'Allocation_Teams') {\r\n      this.tbData = list.map(row => {\r\n        const processList = row.Technology_Path?.split('/') || []\r\n        // 已uuid作为row唯一值；\r\n        // uuid+工序+班组为输入框值\r\n        row.uuid = uuidv4()\r\n        const newData = row[teamKey].filter((r) => processList.findIndex((p) => r.Process_Code === p) !== -1)\r\n        row.defaultCan_Allocation_Count = row.Can_Allocation_Count\r\n        let _inputNum = 0\r\n        newData.forEach((ele, index) => {\r\n          const code = this.getRowUnique(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n          const max = this.getRowUniqueMax(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n          row[code] = ele.Count\r\n          this.$set(row, 'alCount' + code, ele.Count)\r\n          row[max] = 0\r\n          _inputNum += ele.Count\r\n          this.$set(row, 'totalTask' + ele.Working_Team_Name, ele.Total_Receive_Count + ele.Count)\r\n        })\r\n\r\n        this.$set(row, 'allocatedTask', row.defaultCan_Allocation_Count - _inputNum)\r\n        row.Can_Allocation_Count = row.allocatedTask\r\n        this.$set(row, 'allocatedTaskMax', row.Can_Allocation_Count)\r\n\r\n        this.setInputMax(row)\r\n\r\n        row.checked = false\r\n        return row\r\n      })\r\n    },\r\n    inputChange(row) {\r\n      this.setInputMax(row)\r\n    },\r\n    setInputMax(row) {\r\n      let _inputNum = 0\r\n      const inputValuesKeys = Object.keys(row)\r\n        .filter(v => !v.endsWith('max') && v.startsWith(row.uuid) && v.length > row.uuid.length)\r\n      inputValuesKeys.forEach((val) => {\r\n        const curCode = val.split(SPLIT_SYMBOL)[1]\r\n        const otherTotal = inputValuesKeys.filter(x => {\r\n          const code = x.split(SPLIT_SYMBOL)[1]\r\n          return x !== val && code === curCode\r\n        }).reduce((acc, item) => {\r\n          return acc + numeral(row[item]).value()\r\n        }, 0)\r\n        row[val + SPLIT_SYMBOL + 'max'] = row.Schduled_Count - otherTotal\r\n        _inputNum += +row[val]\r\n      })\r\n      // row.allocatedCount = row.defaultCan_Allocation_Count - _inputNum\r\n      // row.Can_Allocation_Count = row.allocatedCount\r\n    },\r\n    checkPermissionTeam(processStr, processCode) {\r\n      if (!processStr) return false\r\n      const list = processStr?.split('/') || []\r\n      return !!list.some(v => v === processCode)\r\n    },\r\n\r\n    getSubmitTbInfo(tbData = this.tbData) {\r\n      // 处理上传的数据\r\n      const tableData = JSON.parse(JSON.stringify(tbData))\r\n      for (let i = 0; i < tableData.length; i++) {\r\n        const element = tableData[i]\r\n        const list = []\r\n        if (!element.Technology_Path) {\r\n          this.$message({\r\n            message: '工序不能为空',\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        const processList = Array.from(new Set(element.Technology_Path.split('/')))\r\n        processList.forEach(code => {\r\n          const groups = this.workingTeam.filter(v => v.Process_Code === code)\r\n\r\n          const groupsList = groups.map((group, index) => {\r\n            const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)\r\n            const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)\r\n            const obj = {\r\n              Comp_Code: element.Comp_Code,\r\n              Again_Count: +element[uCode],\r\n              Part_Code: this.isCom ? null : element.Part_Code,\r\n              Process_Code: code,\r\n              Technology_Path: element.Technology_Path,\r\n              Working_Team_Id: group.Working_Team_Id,\r\n              Working_Team_Name: group.Working_Team_Name,\r\n              Team_Task_Id: element.Allocation_Teams.find((item) => item.Working_Team_Id === group.Working_Team_Id).Team_Task_Id\r\n            }\r\n            delete element['alCount' + uCode]\r\n            delete element['allocatedTask']\r\n            delete element['allocatedTaskMax']\r\n            delete element[uCode]\r\n            delete element[uMax]\r\n            return obj\r\n          })\r\n          list.push(...groupsList)\r\n        })\r\n        console.log(list)\r\n        delete element['uuid']\r\n        delete element['puuid']\r\n        element.Allocation_Teams = list\r\n      }\r\n      return { tableData, status: true }\r\n    },\r\n    handleSubmit() {\r\n      const { tableData, status } = this.getSubmitTbInfo()\r\n      if (!status) return\r\n      this.$confirm('是否提交当前数据?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.loading = true\r\n        const obj = {\r\n          Schduling_Plan: this.planInfoTemp,\r\n          Schduling_Comps: tableData\r\n        }\r\n        const Partobj = {\r\n          Schduling_Plan: this.planInfoTemp,\r\n          SarePartsModel: tableData\r\n        }\r\n        const requestFn = this.isCom ? AdjustTeamProcessAllocation : this.isUnitPart ? AdjustSubAssemblyTeamProcessAllocation : AdjustPartTeamProcessAllocation\r\n        console.log('obj', obj)\r\n        requestFn(this.isCom ? obj : Partobj).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '操作成功',\r\n              type: 'success'\r\n            })\r\n            this.handleClose()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.loading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.closeView()\r\n    },\r\n    closeView() {\r\n      closeTagView(this.$store, this.$route)\r\n    },\r\n    tbSelectChange(array) {\r\n      this.multipleSelection = array.records\r\n      console.log(this.tbData)\r\n    },\r\n    getTaskCode(name = '') {\r\n      if (name) return 'totalTask' + name\r\n      return 'totalTask' + this.activeName.split(SPLIT_SYMBOL)[0]\r\n    },\r\n    // 反选\r\n    reverseSelection() {\r\n      const list = this.$refs.xTable.getCheckboxRecords().filter(item => !item.stopFlag)\r\n      const unSelectList = this.filterTbData.filter(item => !list.includes(item) && !item.stopFlag)\r\n      this.$refs.xTable.setCheckboxRow(list, false)\r\n      this.$refs.xTable.setCheckboxRow(unSelectList, true)\r\n      this.multipleSelection = this.$refs.xTable.getCheckboxRecords()\r\n    },\r\n    async getTableConfig(code) {\r\n      await GetGridByCode({\r\n        code\r\n      }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n          const list = Data.ColumnList || []\r\n          this.columns = list.filter(v => v.Is_Display).map(item => {\r\n            if (FIX_COLUMN.includes(item.Code)) {\r\n              item.fixed = 'left'\r\n            }\r\n            if (item.Code === 'Schduled_Count') {\r\n              item.visible = false\r\n            }\r\n            return item\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    activeCellMethod({ row, column, columnIndex }) {\r\n      if (this.isView) return false\r\n      if (column.field === 'AllocatedCount') return true\r\n      const processCode = column.field?.split('$_$')[1]\r\n      return this.checkPermissionTeam(row.Technology_Path, processCode)\r\n    },\r\n    getRowUnique(uuid, processCode, workingId) {\r\n      return `${uuid}${SPLIT_SYMBOL}${processCode}${SPLIT_SYMBOL}${workingId}`\r\n    },\r\n    getRowUniqueMax(uuid, processCode, workingId) {\r\n      return this.getRowUnique(uuid, processCode, workingId) + `${SPLIT_SYMBOL}max`\r\n    },\r\n    // 批量分配\r\n    Batchallocation() {\r\n      this.dialogVisible = true\r\n      console.log(this.workingTeam)\r\n    },\r\n    // 上道工序分配\r\n    preStepTaskAllocation() {\r\n      const Schduling_Detail_Ids = this.multipleSelection.map(item => item.Schduling_Detail_Id)\r\n      const Working_Process_Code = this.multipleSelection[0].Process_Code\r\n      GetPreStepTaskAllocation({\r\n        Schduling_Detail_Ids,\r\n        Working_Process_Code,\r\n        Working_Process_Id: this.Working_Process_Id,\r\n        Process_Type: this.isCom ? 2 : this.isUnitPart ? 3 : 1,\r\n        Bom_Level: this.bomLevel\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (res.Data.length === 0) {\r\n            this.dialogTipsVisible = true\r\n            return\r\n          }\r\n          this.preDoAllocation(res.Data)\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleDialog() {\r\n      this.dialogVisible = false\r\n      // this.multipleSelection = []\r\n    },\r\n    handelData() {\r\n      this.multipleSelection.forEach((item) =>\r\n        item.Allocation_Teams.forEach((v) => {\r\n          if (v.Working_Team_Id === this.form.TeamGroup) {\r\n            v.Count = item.Can_Allocation_Count\r\n          } else {\r\n            v.Count = 0\r\n          }\r\n        })\r\n      )\r\n      // const tableData = JSON.parse(JSON.stringify(this.multipleSelection))\r\n      for (let i = 0; i < this.multipleSelection.length; i++) {\r\n        const element = this.multipleSelection[i]\r\n        const processList = Array.from(new Set(element.Technology_Path.split('/')))\r\n        processList.forEach(code => {\r\n          const groups = this.workingTeam.filter(v => v.Process_Code === code)\r\n          groups.forEach(group => {\r\n            const uniCode = this.getRowUnique(element.uuid, code, this.form.TeamGroup)\r\n            const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)\r\n            const uniMax = this.getRowUniqueMax(element.uuid, code, this.form.TeamGroup)\r\n            const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)\r\n\r\n            if (uniCode === uCode && uniMax === uMax) {\r\n              element[uCode] = element['Can_Allocation_Count']\r\n              element[uMax] = element['Can_Allocation_Count']\r\n            } else {\r\n              element[uCode] = 0\r\n              element[uMax] = 0\r\n            }\r\n          })\r\n        })\r\n      }\r\n      console.log(this.multipleSelection)\r\n      for (let i = 0; i < this.tbData.length; i++) {\r\n        for (let k = 0; k < this.multipleSelection.length; k++) {\r\n          if (this.tbData[i].uuid === this.multipleSelection[k].uuid) {\r\n            this.$nextTick((_) => {\r\n              this.tbData[i] = this.multipleSelection[k]\r\n            })\r\n          }\r\n        }\r\n      }\r\n    },\r\n    submitForm(formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          // this.handelData()\r\n          this.doAllocation()\r\n          // this.workingTeam.find(v=> v.Working_Team_Id === this.form.TeamGroup).count\r\n          this.handleDialog()\r\n          console.log(this.tbData)\r\n          this.multipleSelection = []\r\n          this.$refs.xTable.clearCheckboxRow()\r\n        } else {\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    // 上道工序分配\r\n    preDoAllocation(preProcessData) {\r\n      const preProcessDataMap = new Map()\r\n      preProcessData.forEach(item => {\r\n        const key = `${item.Schduling_Detail_Id}_${item.Working_Team_Id}`\r\n        preProcessDataMap.set(key, item.Current_Task_Count)\r\n      })\r\n\r\n      const allocateForTeam = (row, team, amount) => {\r\n        const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\r\n        const bKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\r\n        const currentTaskCount = preProcessDataMap.get(bKey) || 0\r\n        const allocated = Math.min(amount, currentTaskCount, row.Can_Allocation_Count)\r\n\r\n        row[tarCode] = (Number(row[tarCode]) || 0) + allocated\r\n        row.Can_Allocation_Count -= allocated\r\n        row[this.getTaskCode(team.Working_Team_Name)] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + allocated\r\n        row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - allocated\r\n        row['alCount' + tarCode] = row[tarCode]\r\n\r\n        return allocated\r\n      }\r\n      let isMessage = true\r\n      this.multipleSelection.forEach(row => {\r\n        if (!row.Can_Allocation_Count && this.activeName === 'first') return\r\n\r\n        const eligibleTeams = row.Allocation_Teams.filter(team =>\r\n          preProcessDataMap.has(`${row.Schduling_Detail_Id}_${team.Working_Team_Id}`)\r\n        )\r\n\r\n        if (eligibleTeams.length === 0) return\r\n        if (this.activeName === 'first') {\r\n          let IsAllNo = 0\r\n          const totalAvailable = eligibleTeams.reduce((sum, team) => {\r\n            const bKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\r\n            return sum + (preProcessDataMap.get(bKey) || 0)\r\n          }, 0)\r\n\r\n          if (row.allocatedTaskMax < totalAvailable) {\r\n            IsAllNo++\r\n            if (IsAllNo === this.multipleSelection.length) {\r\n              isMessage = false\r\n              this.dialogTipsVisible = true\r\n            }\r\n            return\r\n          }\r\n\r\n          let remaining = Math.min(row.allocatedTask, row.Can_Allocation_Count)\r\n          const perTeamAllocation = Math.floor(remaining / eligibleTeams.length)\r\n\r\n          eligibleTeams.forEach((team, index) => {\r\n            if (remaining <= 0) return\r\n\r\n            const allocateAmount = index === eligibleTeams.length - 1\r\n              ? remaining\r\n              : Math.min(perTeamAllocation, remaining)\r\n\r\n            remaining -= allocateForTeam(row, team, allocateAmount)\r\n          })\r\n\r\n          row.allocatedTaskMax = row.Can_Allocation_Count\r\n          row.allocatedTask = row.Can_Allocation_Count\r\n          if (IsAllNo === this.multipleSelection.length) {\r\n            isMessage = false\r\n            this.dialogTipsVisible = true\r\n            return\r\n          }\r\n        } else {\r\n          eligibleTeams.forEach(team => {\r\n            const bKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\r\n            const currentTaskCount = preProcessDataMap.get(bKey) || 0\r\n\r\n            if (row[this.getRowCCode(row)] < currentTaskCount) {\r\n              return\r\n            }\r\n\r\n            const selectNum = Math.min(\r\n              row[this.getRowCCode(row, 'alCount')] || 0,\r\n              row[this.getRowCCode(row)] || 0,\r\n              currentTaskCount\r\n            )\r\n\r\n            if (selectNum > 0) {\r\n              const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\r\n              row[this.getTaskCode(team.Working_Team_Name)] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + selectNum\r\n              row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - selectNum\r\n              row[tarCode] = (Number(row[tarCode]) || 0) + selectNum\r\n              row[this.getRowCCode(row)] = (row[this.getRowCCode(row)] || 0) - selectNum\r\n              row[this.getRowCCode(row, 'alCount')] = (row[this.getRowCCode(row, 'alCount')] || 0) - selectNum\r\n              row['alCount' + tarCode] = row[tarCode]\r\n            }\r\n          })\r\n        }\r\n      })\r\n      // if (this.activeName === 'first') {\r\n      //   let IsAllNo = 0\r\n      //   this.multipleSelection.forEach((row, idx) => {\r\n      //     if (row.Can_Allocation_Count) {\r\n      //       const validTeams = row.Allocation_Teams.filter(team => {\r\n      //         const key = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\r\n      //         return preProcessDataMap.has(key)\r\n      //       })\r\n      //       if (validTeams.length > 0) {\r\n      //         const team = validTeams[0]\r\n      //         const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\r\n\r\n      //         const preProcessKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\r\n      //         const currentTaskCount = preProcessDataMap.get(preProcessKey) || 0\r\n\r\n      //         if (currentTaskCount > row.allocatedTaskMax) {\r\n      //           IsAllNo++\r\n      //           return\r\n      //         }\r\n      //         const allocated = Math.min(row.allocatedTask, currentTaskCount)\r\n\r\n      //         row[tarCode] = Number(row[tarCode] || 0) + allocated\r\n      //         row.Can_Allocation_Count = row.Can_Allocation_Count - allocated\r\n      //         row[this.getTaskCode(team.Working_Team_Name) || 0] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + allocated\r\n      //         row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - allocated\r\n      //         row.allocatedTaskMax = row.Can_Allocation_Count\r\n      //         row.allocatedTask = row.Can_Allocation_Count\r\n      //         row['alCount' + tarCode] = row[tarCode]\r\n      //       }\r\n      //     }\r\n      //   })\r\n      //   if (IsAllNo === this.multipleSelection.length) {\r\n      //     this.dialogTipsVisible = true\r\n      //     return\r\n      //   }\r\n      // } else {\r\n      //   this.multipleSelection.forEach((row, idx) => {\r\n      //     const validTeams = row.Allocation_Teams.filter(team => {\r\n      //       const key = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\r\n      //       return preProcessDataMap.has(key)\r\n      //     })\r\n      //     if (validTeams.length > 0) {\r\n      //       const team = validTeams[0]\r\n      //       const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\r\n\r\n      //       const preProcessKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\r\n      //       const currentTaskCount = preProcessDataMap.get(preProcessKey) || 0\r\n\r\n      //       const selectNum = Math.min(\r\n      //         row[this.getRowCCode(row, 'alCount')] || 0,\r\n      //         row[this.getRowCCode(row)] || 0,\r\n      //         currentTaskCount\r\n      //       )\r\n\r\n      //       row[this.getTaskCode(team.Working_Team_Name)] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + selectNum\r\n      //       row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - selectNum\r\n      //       row[tarCode] = (Number(row[tarCode]) || 0) + selectNum\r\n      //       row[this.getRowCCode(row)] = (row[this.getRowCCode(row)] || 0) - selectNum\r\n      //       row[this.getRowCCode(row, 'alCount')] = (row[this.getRowCCode(row, 'alCount')] || 0) - selectNum\r\n      //       row['alCount' + tarCode] = row[tarCode]\r\n      //     }\r\n      //   })\r\n      // }\r\n      this.filterTbData = this.tbData.filter(row => {\r\n        return this.filterZero(row)\r\n      })\r\n      this.multipleSelection = []\r\n      this.$refs.xTable.clearCheckboxRow()\r\n      if (isMessage) {\r\n        this.$message({\r\n          message: '同步成功',\r\n          type: 'success'\r\n        })\r\n      }\r\n    },\r\n    // 批量分配提交\r\n    doAllocation() {\r\n      if (this.activeName === 'first') {\r\n        this.multipleSelection\r\n          .forEach((row, idx) => {\r\n            if (row.Can_Allocation_Count) {\r\n              const team = row.Allocation_Teams.find(v => v.Working_Team_Id === this.form.TeamGroup)\r\n              const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\r\n              row[tarCode] = Number(row[tarCode]) + row.allocatedTask\r\n              row.Can_Allocation_Count = row.Can_Allocation_Count - row.allocatedTask\r\n              row[this.getTaskCode(team.Working_Team_Name)] += row.allocatedTask\r\n              row[this.getTaskCode()] -= row.allocatedTask\r\n              row.allocatedTaskMax = row.Can_Allocation_Count\r\n              row.allocatedTask = row.Can_Allocation_Count\r\n              row['alCount' + tarCode] = row[tarCode]\r\n            }\r\n          })\r\n      } else {\r\n        this.multipleSelection\r\n          .forEach((row, idx) => {\r\n            const team = row.Allocation_Teams.find(v => v.Working_Team_Id === this.form.TeamGroup)\r\n            const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\r\n\r\n            const selectNum = Math.min(row[this.getRowCCode(row, 'alCount')], row[this.getRowCCode(row)])\r\n\r\n            row[this.getTaskCode(team.Working_Team_Name)] += selectNum\r\n            row[this.getTaskCode()] -= selectNum\r\n\r\n            row[tarCode] = Number(row[tarCode]) + selectNum\r\n            row[this.getRowCCode(row)] -= selectNum\r\n            row[this.getRowCCode(row, 'alCount')] -= selectNum\r\n\r\n            row['alCount' + tarCode] = row[tarCode]\r\n          })\r\n      }\r\n      this.filterTbData = this.tbData.filter(row => {\r\n        return this.filterZero(row)\r\n      })\r\n    },\r\n    resetForm(formName) {\r\n      this.$refs[formName].resetFields()\r\n      this.dialogVisible = false\r\n    },\r\n    filterTypeMethod({ option, row }) {\r\n      return row.Type.includes(option.data)\r\n    },\r\n    filterTypeRecoverMethod({ option }) {\r\n      option.data = ''\r\n    },\r\n    filterCodeMethod({ option, row }) {\r\n      console.log('option, row', option, row)\r\n      return row[this.isCom ? 'Comp_Code' : 'Part_Code'].includes(option.data)\r\n    },\r\n    filterCodeRecoverMethod({ option }) {\r\n      option.data = ''\r\n    },\r\n    getObjectTypeList() {\r\n      GetCompTypeTree({ professional: 'Steel' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.ObjectTypeList.data = res.Data\r\n          this.$nextTick((_) => {\r\n            this.$refs?.treeSelectObjectType?.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.pagination-container {\r\n  padding: 0;\r\n  text-align: right;\r\n}\r\n\r\n::v-deep .el-card__body {\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .el-divider {\r\n    margin-top: 0;\r\n    margin-bottom: 16px;\r\n    background-color: #EEEEEE;\r\n  }\r\n\r\n  .tb-options {\r\n    margin-bottom: 16px;\r\n    display: flex;\r\n\r\n    .el-form-item--small.el-form-item {\r\n      margin-bottom: 0;\r\n    }\r\n    .el-input {\r\n      width: 250px;\r\n    }\r\n  }\r\n\r\n  footer {\r\n    text-align: inherit;\r\n    display: flex;\r\n    justify-content: space-between;\r\n  }\r\n}\r\n\r\n.tb-x {\r\n  flex: 1;\r\n  height: 0;\r\n  margin-bottom: 10px;\r\n  overflow: auto;\r\n}\r\n\r\n.topTitle {\r\n  height: 14px;\r\n  line-height: 14px;\r\n  font-size: 14px;\r\n  margin: 0 0 16px;\r\n\r\n  span {\r\n    display: inline-block;\r\n    width: 2px;\r\n    height: 14px;\r\n    background: #009dff;\r\n    vertical-align: middle;\r\n    margin-right: 6px;\r\n  }\r\n}\r\n\r\n.el-icon-edit {\r\n  cursor: pointer;\r\n}\r\n\r\n.cs-bottom {\r\n  position: relative;\r\n  height: 40px;\r\n  line-height: 40px;\r\n\r\n  .data-info {\r\n    position: absolute;\r\n    bottom: 0;\r\n\r\n    .info-x {\r\n      margin-right: 20px;\r\n    }\r\n  }\r\n}\r\n.my-input {\r\n  margin: 10px;\r\n  width: 140px;\r\n  height: 32px;\r\n}\r\n</style>\r\n"]}]}
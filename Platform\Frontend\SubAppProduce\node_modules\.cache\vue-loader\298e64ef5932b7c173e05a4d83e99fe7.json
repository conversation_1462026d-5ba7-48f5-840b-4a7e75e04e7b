{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\detail.vue", "mtime": 1758330074629}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IEdldEdyaWRCeUNvZGUgfSBmcm9tICdAL2FwaS9zeXMnCmltcG9ydCB7IEZJWF9DT0xVTU4gfSBmcm9tICdAL3ZpZXdzL1BSTy9wbGFuLXByb2R1Y3Rpb24vc2NoZWR1bGUtcHJvZHVjdGlvbi9jb25zdGFudCcKaW1wb3J0IHsKICBBZGp1c3RUZWFtUHJvY2Vzc0FsbG9jYXRpb24sCiAgR2V0VGVhbVByb2Nlc3NBbGxvY2F0aW9uLAogIEFkanVzdFBhcnRUZWFtUHJvY2Vzc0FsbG9jYXRpb24sCiAgQWRqdXN0U3ViQXNzZW1ibHlUZWFtUHJvY2Vzc0FsbG9jYXRpb24sCiAgR2V0U3RvcExpc3QsCiAgR2V0UHJlU3RlcFRhc2tBbGxvY2F0aW9uCn0gZnJvbSAnQC9hcGkvUFJPL3Byb2R1Y3Rpb24tdGFzaycKaW1wb3J0IFFyY29kZVZ1ZSBmcm9tICdxcmNvZGUudnVlJwppbXBvcnQgeyB2NCBhcyB1dWlkdjQgfSBmcm9tICd1dWlkJwppbXBvcnQgbnVtZXJhbCBmcm9tICdudW1lcmFsJwppbXBvcnQgeyBjbG9zZVRhZ1ZpZXcsIGRlZXBDbG9uZSB9IGZyb20gJ0AvdXRpbHMnCmltcG9ydCB7IEdldENvbXBUeXBlVHJlZSB9IGZyb20gJ0AvYXBpL1BSTy9wcm9mZXNzaW9uYWxUeXBlJwppbXBvcnQgeyBHZXRCT01JbmZvLCBnZXRCb21Db2RlLCBnZXRCb21OYW1lLCBjaGVja0lzVW5pdFBhcnQgfSBmcm9tICdAL3ZpZXdzL1BSTy9ib20tc2V0dGluZy91dGlscycKCmNvbnN0IFNQTElUX1NZTUJPTCA9ICckXyQnCmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7CiAgICBRcmNvZGVWdWUKICB9LAogIGZpbHRlcnM6IHsKICAgIGZpbHRlck51bSh2YWx1ZSkgewogICAgICByZXR1cm4gbnVtZXJhbCh2YWx1ZSkuZGl2aWRlKDEwMDApLmZvcm1hdCgnMC5bMDBdJykKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB0cmVlU2VsZWN0UGFyYW1zOiB7CiAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fpgInmi6knLAogICAgICAgIGNsZWFyYWJsZTogdHJ1ZQogICAgICB9LAogICAgICBPYmplY3RUeXBlTGlzdDogewogICAgICAgIC8vIOaehOS7tuexu+WeiwogICAgICAgICdjaGVjay1zdHJpY3RseSc6IHRydWUsCiAgICAgICAgJ2RlZmF1bHQtZXhwYW5kLWFsbCc6IHRydWUsCiAgICAgICAgY2xpY2tQYXJlbnQ6IHRydWUsCiAgICAgICAgZGF0YTogW10sCiAgICAgICAgcHJvcHM6IHsKICAgICAgICAgIGNoaWxkcmVuOiAnQ2hpbGRyZW4nLAogICAgICAgICAgbGFiZWw6ICdMYWJlbCcsCiAgICAgICAgICB2YWx1ZTogJ0RhdGEnCiAgICAgICAgfQogICAgICB9LAogICAgICB0YkxvYWRpbmc6IGZhbHNlLAogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgYWN0aXZlTmFtZTogJ2ZpcnN0JywKICAgICAgdGlwTGFiZWw6ICcnLAogICAgICB0YkRhdGE6IFtdLAogICAgICBmaWx0ZXJUYkRhdGE6IFtdLAogICAgICBtdWx0aXBsZVNlbGVjdGlvbjogW10sCiAgICAgIGNvbHVtbnM6IFtdLAogICAgICB3b3JraW5nVGVhbTogW10sCiAgICAgIHdvcmtpbmdUZWFtQ29sdW1uOiBbXSwKICAgICAgZm9ybUlubGluZToge30sCiAgICAgIHBnX3R5cGU6ICcnLAogICAgICBzZWFyY2hUeXBlOiAnJywKICAgICAgdHlwZTogJycsCiAgICAgIHF1ZXJ5Rm9ybTogewogICAgICAgIENvbXBfQ29kZXM6ICcnLAogICAgICAgIFBhcnRfQ29kZTogJycsCiAgICAgICAgU3BlYzogJycsCiAgICAgICAgQ29tcF9Db2Rlc19WYWd1ZTogJycsCiAgICAgICAgUGFydF9Db2RlX1ZhZ3VlOiAnJwogICAgICB9LAogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgZGlhbG9nVGlwc1Zpc2libGU6IGZhbHNlLAogICAgICBmb3JtOiB7CiAgICAgICAgVGVhbUdyb3VwOiAnJyAvLyDnj63nu4QKICAgICAgfSwKCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgVGVhbUdyb3VwOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl54+t57uE5ZCN56ewJywgdHJpZ2dlcjogJ2NoYW5nZScgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgSXNfV29ya3Nob3BfRW5hYmxlZDogZmFsc2UsCiAgICAgIFdvcmtpbmdfUHJvY2Vzc19JZDogJycsCiAgICAgIGJvbUxpc3Q6IFtdLAogICAgICBib21MZXZlbDogJycsCiAgICAgIGJvbU5hbWU6ICcnCiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgaXNWaWV3KCkgewogICAgICByZXR1cm4gdGhpcy50eXBlID09PSAndmlldycKICAgIH0sCiAgICBpc0NvbSgpIHsKICAgICAgcmV0dXJuIHRoaXMuYm9tTGV2ZWwgPT09IGdldEJvbUNvZGUoJy0xJykKICAgIH0sCiAgICBpc1VuaXRQYXJ0KCkgewogICAgICByZXR1cm4gY2hlY2tJc1VuaXRQYXJ0KHRoaXMuYm9tTGV2ZWwpCiAgICB9CiAgfSwKCiAgYXN5bmMgbW91bnRlZCgpIHsKICAgIHRyeSB7CiAgICAgIGNvbnN0IHsgbGlzdCB9ID0gYXdhaXQgR2V0Qk9NSW5mbygpCiAgICAgIHRoaXMuYm9tTGlzdCA9IGxpc3QgfHwgW10KICAgICAgY29uc3Qgcm93SW5mbyA9IEpTT04ucGFyc2UoZGVjb2RlVVJJQ29tcG9uZW50KHRoaXMuJHJvdXRlLnF1ZXJ5Lm90aGVyKSkKICAgICAgY29uc29sZS5sb2coJ3Jvd0luZm8nLCByb3dJbmZvKQogICAgICB0aGlzLmZvcm1JbmxpbmUgPSBPYmplY3QuYXNzaWduKHt9LCB0aGlzLmZvcm1JbmxpbmUsIHJvd0luZm8pCiAgICAgIHRoaXMucGdfdHlwZSA9IHRoaXMuJHJvdXRlLnF1ZXJ5LnBnX3R5cGUKICAgICAgdGhpcy5ib21MZXZlbCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LmJvbUxldmVsCiAgICAgIHRoaXMuYm9tTmFtZSA9IGF3YWl0IGdldEJvbU5hbWUodGhpcy5ib21MZXZlbCkKICAgICAgdGhpcy50eXBlID0gdGhpcy4kcm91dGUucXVlcnkudHlwZQogICAgICB0aGlzLklzX1dvcmtzaG9wX0VuYWJsZWQgPSB0aGlzLiRyb3V0ZS5xdWVyeS5Jc19Xb3Jrc2hvcF9FbmFibGVkCiAgICAgIGF3YWl0IHRoaXMuZ2V0VGFibGVDb25maWcodGhpcy5pc1VuaXRQYXJ0ID8gJ1BST1Rhc2tVbml0QWxsb2NhdGlvbkNoYW5nZScgOiAnUFJPVGFza0FsbG9jYXRpb25DaGFuZ2UnKQogICAgICBpZiAodGhpcy5pc0NvbSkgewogICAgICAgIGNvbnN0IGlkeCA9IHRoaXMuY29sdW1ucy5maW5kSW5kZXgoaXRlbSA9PiBpdGVtLkNvZGUgPT09ICdQYXJ0X0NvZGUnKQogICAgICAgIGlkeCAhPT0gLTEgJiYgdGhpcy5jb2x1bW5zLnNwbGljZShpZHgsIDEpCiAgICAgICAgY29uc3QgaWR4MiA9IHRoaXMuY29sdW1ucy5maW5kSW5kZXgoaXRlbSA9PiBpdGVtLkNvZGUgPT09ICdDb21wb25lbnRfQ29kZScpCiAgICAgICAgaWR4MiAhPT0gLTEgJiYgdGhpcy5jb2x1bW5zLnNwbGljZShpZHgyLCAxKQogICAgICB9IGVsc2UgewogICAgICAgIGNvbnN0IGlkeCA9IHRoaXMuY29sdW1ucy5maW5kSW5kZXgoaXRlbSA9PiBpdGVtLkNvZGUgPT09ICdDb21wX0NvZGUnKQogICAgICAgIGlkeCAhPT0gLTEgJiYgdGhpcy5jb2x1bW5zLnNwbGljZShpZHgsIDEpCiAgICAgICAgY29uc3QgaWR4MiA9IHRoaXMuY29sdW1ucy5maW5kSW5kZXgoaXRlbSA9PiBpdGVtLkNvZGUgPT09ICdUeXBlJykKICAgICAgICBpZHgyICE9PSAtMSAmJiB0aGlzLmNvbHVtbnMuc3BsaWNlKGlkeDIsIDEpCiAgICAgIH0KICAgICAgaWYgKCF0aGlzLklzX1dvcmtzaG9wX0VuYWJsZWQpIHsKICAgICAgICBjb25zdCBpZHgzID0gdGhpcy5jb2x1bW5zLmZpbmRJbmRleChpdGVtID0+IGl0ZW0uQ29kZSA9PT0gJ1dvcmtzaG9wX05hbWUnKQogICAgICAgIGlkeDMgIT09IC0xICYmIHRoaXMuY29sdW1ucy5zcGxpY2UoaWR4MywgMSkKICAgICAgfQogICAgICB0aGlzLmdldE9iamVjdFR5cGVMaXN0KCkKICAgICAgdGhpcy5mZXRjaERhdGEoKQogICAgfSBjYXRjaCAoZSkgewogICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICBtZXNzYWdlOiAn5Y+C5pWw6ZSZ6K+vLOivt+mHjeaWsOaTjeS9nCcsCiAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICB9KQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgZ2V0Um93Q0NvZGUocm93LCBwcmVmaXggPSAnJywgc3VmZml4ID0gJycpIHsKICAgICAgaWYgKHRoaXMuYWN0aXZlTmFtZSA9PT0gJ2ZpcnN0JykgewogICAgICAgIHJldHVybiAnYWxsb2NhdGVkVGFzaycgKyBzdWZmaXgKICAgICAgfSBlbHNlIHsKICAgICAgICBjb25zdCBhcnIgPSB0aGlzLmFjdGl2ZU5hbWUuc3BsaXQoU1BMSVRfU1lNQk9MKQogICAgICAgIGNvbnN0IHRlYW0gPSB0aGlzLndvcmtpbmdUZWFtLmZpbmQodiA9PiB2LldvcmtpbmdfVGVhbV9OYW1lID09PSBhcnJbMF0pCiAgICAgICAgY29uc3QgdSA9IHRoaXMuZ2V0Um93VW5pcXVlKHJvdy51dWlkLCByb3cuUHJvY2Vzc19Db2RlLCB0ZWFtLldvcmtpbmdfVGVhbV9JZCkKICAgICAgICBpZiAoc3VmZml4ID09PSAnTWF4JykgewogICAgICAgICAgcmV0dXJuIHUKICAgICAgICB9IGVsc2UgewogICAgICAgICAgcmV0dXJuIHByZWZpeCArIHUgKyBzdWZmaXgKICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICBoYW5kbGVDbGljayh2YWwpIHsKICAgICAgY29uc29sZS5sb2coJ2hhbmRsZUNsaWNrJywgdmFsKQogICAgICBpZiAodmFsLm5hbWUgPT09ICdmaXJzdCcpIHsKICAgICAgICBjb25zdCBjMSA9IHRoaXMuJHJlZnMueFRhYmxlLmdldENvbHVtbkJ5RmllbGQoJ1NjaGR1bGVkX0NvdW50JykKICAgICAgICBjb25zdCBjMiA9IHRoaXMuJHJlZnMueFRhYmxlLmdldENvbHVtbkJ5RmllbGQoJ0Nhbl9BbGxvY2F0aW9uX0NvdW50JykKICAgICAgICBjMS52aXNpYmxlID0gZmFsc2UKICAgICAgICBjMi52aXNpYmxlID0gdHJ1ZQogICAgICB9IGVsc2UgewogICAgICAgIGNvbnN0IGMyID0gdGhpcy4kcmVmcy54VGFibGUuZ2V0Q29sdW1uQnlGaWVsZCgnU2NoZHVsZWRfQ291bnQnKQogICAgICAgIGNvbnN0IGMzID0gdGhpcy4kcmVmcy54VGFibGUuZ2V0Q29sdW1uQnlGaWVsZCgnQ2FuX0FsbG9jYXRpb25fQ291bnQnKQogICAgICAgIGMyLnZpc2libGUgPSB0cnVlCiAgICAgICAgYzMudmlzaWJsZSA9IGZhbHNlCiAgICAgIH0KCiAgICAgIHRoaXMud29ya2luZ1RlYW0uZm9yRWFjaCgoZWxlbWVudCwgaWR4KSA9PiB7CiAgICAgICAgY29uc3QgY29kZXMgPSBgJHtlbGVtZW50LldvcmtpbmdfVGVhbV9OYW1lfSRfJCR7ZWxlbWVudC5Qcm9jZXNzX0NvZGV9YAoKICAgICAgICBjb25zdCBjID0gdGhpcy4kcmVmcy54VGFibGUuZ2V0Q29sdW1uQnlGaWVsZChjb2RlcykKCiAgICAgICAgYy52aXNpYmxlID0gY29kZXMgPT09IHZhbC5uYW1lCiAgICAgIH0pCiAgICAgIHRoaXMuJHJlZnMueFRhYmxlLnJlZnJlc2hDb2x1bW4oKQoKICAgICAgdGhpcy5maWx0ZXJUYkRhdGEgPSB0aGlzLnRiRGF0YS5maWx0ZXIocm93ID0+IHsKICAgICAgICByZXR1cm4gdGhpcy5maWx0ZXJaZXJvKHJvdykKICAgICAgfSkKCiAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24gPSBbXQogICAgICB0aGlzLiRyZWZzLnhUYWJsZS5jbGVhckNoZWNrYm94Um93KCkKICAgIH0sCiAgICBmaWx0ZXJaZXJvKHJvdykgewogICAgICBpZiAodGhpcy5hY3RpdmVOYW1lID09PSAnZmlyc3QnKSB7CiAgICAgICAgcmV0dXJuIHJvdy5hbGxvY2F0ZWRUYXNrID4gMAogICAgICB9IGVsc2UgewogICAgICAgIGNvbnN0IGFyciA9IHRoaXMuYWN0aXZlTmFtZS5zcGxpdChTUExJVF9TWU1CT0wpCiAgICAgICAgY29uc3QgdGVhbSA9IHRoaXMud29ya2luZ1RlYW0uZmluZCh2ID0+IHYuV29ya2luZ19UZWFtX05hbWUgPT09IGFyclswXSkKICAgICAgICBjb25zdCBjb2RlID0gdGhpcy5nZXRSb3dVbmlxdWUocm93LnV1aWQsIHRlYW0uUHJvY2Vzc19Db2RlLCB0ZWFtLldvcmtpbmdfVGVhbV9JZCkKICAgICAgICByZXR1cm4gcm93W2NvZGVdID4gMAogICAgICB9CiAgICB9LAogICAgZmV0Y2hEYXRhKCkgewogICAgICBjb25zdCBDb21wX0NvZGVzID0gIXRoaXMucXVlcnlGb3JtLkNvbXBfQ29kZXMgPyBbXSA6IHRoaXMucXVlcnlGb3JtLkNvbXBfQ29kZXMudHJpbSgpLnNwbGl0KCcgJykKICAgICAgY29uc3QgUGFydF9Db2RlID0gIXRoaXMucXVlcnlGb3JtLlBhcnRfQ29kZSA/IFtdIDogdGhpcy5xdWVyeUZvcm0uUGFydF9Db2RlLnRyaW0oKS5zcGxpdCgnICcpCiAgICAgIGxldCBQcm9jZXNzX1R5cGUgPSAyCiAgICAgIFByb2Nlc3NfVHlwZSA9IHRoaXMuaXNDb20gPyAyIDogdGhpcy5pc1VuaXRQYXJ0ID8gMyA6IDEKICAgICAgdGhpcy50YkxvYWRpbmcgPSB0cnVlCiAgICAgIEdldFRlYW1Qcm9jZXNzQWxsb2NhdGlvbih7CiAgICAgICAgUGFnZTogMSwKICAgICAgICBQYWdlU2l6ZTogLTEsCiAgICAgICAgU3RlcDogdGhpcy5mb3JtSW5saW5lLlN0ZXAsCiAgICAgICAgUHJvY2Vzc19UeXBlLAogICAgICAgIEJvbV9MZXZlbDogdGhpcy5ib21MZXZlbCwKICAgICAgICBTY2hkdWxpbmdfQ29kZTogdGhpcy5mb3JtSW5saW5lLlNjaGR1bGluZ19Db2RlLAogICAgICAgIFByb2Nlc3NfQ29kZTogdGhpcy5mb3JtSW5saW5lLlByb2Nlc3NfQ29kZSwKICAgICAgICBXb3Jrc2hvcF9OYW1lOiB0aGlzLmZvcm1JbmxpbmUuV29ya3Nob3BfTmFtZSwKICAgICAgICBBcmVhX0lkOiB0aGlzLmZvcm1JbmxpbmUuQXJlYV9JZCwKICAgICAgICBJbnN0YWxsVW5pdF9JZDogdGhpcy5mb3JtSW5saW5lLkluc3RhbGxVbml0X0lkLAogICAgICAgIENvbXBfQ29kZXMsCiAgICAgICAgUGFydF9Db2RlCiAgICAgIH0pLnRoZW4oYXN5bmMocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIGNvbnN0IHsgU2NoZHVsaW5nX1BsYW4sIFNjaGR1bGluZ19Db21wcyB9ID0gcmVzLkRhdGEKICAgICAgICAgIHRoaXMucGxhbkluZm9UZW1wID0gU2NoZHVsaW5nX1BsYW4KICAgICAgICAgIGF3YWl0IHRoaXMuZ2V0U3RvcExpc3QoU2NoZHVsaW5nX0NvbXBzKQogICAgICAgICAgdGhpcy5pbml0VGJEYXRhKFNjaGR1bGluZ19Db21wcykKICAgICAgICAgIHRoaXMuV29ya2luZ19Qcm9jZXNzX0lkID0gcmVzLkRhdGEuV29ya2luZ19Qcm9jZXNzX0lkCgogICAgICAgICAgaWYgKFNjaGR1bGluZ19Db21wcy5sZW5ndGgpIHsKICAgICAgICAgICAgdGhpcy53b3JraW5nVGVhbSA9IFNjaGR1bGluZ19Db21wc1swXS5BbGxvY2F0aW9uX1RlYW1zCiAgICAgICAgICAgIGNvbnN0IF9rayA9IHRoaXMud29ya2luZ1RlYW0ubWFwKHYgPT4gewogICAgICAgICAgICAgIHYudmlzaWJsZSA9IGZhbHNlCiAgICAgICAgICAgICAgcmV0dXJuIHYKICAgICAgICAgICAgfSkKICAgICAgICAgICAgdGhpcy53b3JraW5nVGVhbUNvbHVtbiA9IGRlZXBDbG9uZShfa2spCiAgICAgICAgICB9CiAgICAgICAgICBjb25zb2xlLmxvZygnIHRoaXMudGJEYXRhJywgdGhpcy50YkRhdGEpCiAgICAgICAgICB0aGlzLmZpbHRlckRhdGEoKQogICAgICAgICAgY29uc29sZS5sb2coJ2ZpbHRlclRiRGF0YScsIHRoaXMuZmlsdGVyVGJEYXRhKQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KS5maW5hbGx5KF8gPT4gewogICAgICAgIHRoaXMudGJMb2FkaW5nID0gZmFsc2UKICAgICAgfSkKICAgIH0sCiAgICBjaGVja01ldGhvZCh7IHJvdyB9KSB7CiAgICAgIHJldHVybiAhcm93LnN0b3BGbGFnCiAgICB9LAogICAgYXN5bmMgZ2V0U3RvcExpc3QobGlzdCkgewogICAgICBjb25zdCBrZXkgPSAnSWQnCiAgICAgIGNvbnN0IHN1Ym1pdE9iaiA9IGxpc3QubWFwKGl0ZW0gPT4gewogICAgICAgIHJldHVybiB7CiAgICAgICAgICBJZDogaXRlbVtrZXldLAogICAgICAgICAgQm9tX0xldmVsOiB0aGlzLmJvbUxldmVsLAogICAgICAgICAgVHlwZTogdGhpcy5pc0NvbSA/IDIgOiB0aGlzLmlzVW5pdFBhcnQgPyAzIDogMSAvLyAx77ya6Zu25Lu277yMM++8mumDqOS7tu+8jDLvvJrmnoTku7YKICAgICAgICB9CiAgICAgIH0pCiAgICAgIGF3YWl0IEdldFN0b3BMaXN0KHN1Ym1pdE9iaikudGhlbihyZXMgPT4gewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICBjb25zdCBzdG9wTWFwID0ge30KICAgICAgICAgIHJlcy5EYXRhLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICAgIHN0b3BNYXBbaXRlbS5JZF0gPSAhIWl0ZW0uSXNfU3RvcAogICAgICAgICAgfSkKICAgICAgICAgIGxpc3QuZm9yRWFjaChyb3cgPT4gewogICAgICAgICAgICBpZiAoc3RvcE1hcFtyb3dba2V5XV0pIHsKICAgICAgICAgICAgICB0aGlzLiRzZXQocm93LCAnc3RvcEZsYWcnLCBzdG9wTWFwW3Jvd1trZXldXSkKICAgICAgICAgICAgfQogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgZmlsdGVyRGF0YSgpIHsKICAgICAgY29uc29sZS5sb2coJ3NlYXJjaFR5cGUnLCB0aGlzLnNlYXJjaFR5cGUpCiAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24gPSBbXQogICAgICB0aGlzLiRyZWZzLnhUYWJsZS5jbGVhckNoZWNrYm94Um93KCkKICAgICAgY29uc3QgY29kZSA9IHRoaXMuaXNDb20gPyAnQ29tcF9Db2RlJyA6ICdQYXJ0X0NvZGUnCiAgICAgIGNvbnN0IHF1ZXJ5Q29kZSA9IHRoaXMuaXNDb20gPyAnQ29tcF9Db2RlcycgOiAnUGFydF9Db2RlJwogICAgICBjb25zdCBjb2RlTGlzdCA9IHRoaXMucXVlcnlGb3JtW3F1ZXJ5Q29kZV0uc3BsaXQoJyAnKS5maWx0ZXIodiA9PiAhIXYpCgogICAgICBjb25zdCBxdWVyeUNvZGVWYWd1ZSA9IHRoaXMuaXNDb20gPyAnQ29tcF9Db2Rlc19WYWd1ZScgOiAnUGFydF9Db2RlX1ZhZ3VlJwogICAgICBjb25zdCBjb2RlTGlzdFZhZ3VlID0gdGhpcy5xdWVyeUZvcm1bcXVlcnlDb2RlVmFndWVdCgogICAgICBjb25zdCBzZWFyY2hUYkRhdGEgPSB0aGlzLnRiRGF0YS5maWx0ZXIodiA9PiB7CiAgICAgICAgaWYgKCFjb2RlTGlzdC5sZW5ndGggJiYgY29kZUxpc3RWYWd1ZSA9PT0gJycpIHsKICAgICAgICAgIHJldHVybiB0cnVlCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHJldHVybiBjb2RlTGlzdC5pbmNsdWRlcyh2W2NvZGVdKQogICAgICAgIH0KICAgICAgfSkKCiAgICAgIGNvbnN0IHZhZ3VlRGF0YSA9IHNlYXJjaFRiRGF0YS5sZW5ndGggPiAwICYmIGNvZGVMaXN0VmFndWUgPT09ICcnID8gc2VhcmNoVGJEYXRhIDogc2VhcmNoVGJEYXRhLmxlbmd0aCA9PT0gMCAmJiBjb2RlTGlzdFZhZ3VlID09PSAnJyA/IHNlYXJjaFRiRGF0YSA6IHRoaXMudGJEYXRhCiAgICAgIGNvbnN0IHNlYXJjaFZhZ3VlVGJEYXRhID0gdmFndWVEYXRhLmZpbHRlcih2ID0+IHsKICAgICAgICBpZiAoY29kZUxpc3RWYWd1ZSA9PT0gJycgJiYgIWNvZGVMaXN0Lmxlbmd0aCkgewogICAgICAgICAgcmV0dXJuIHRydWUKICAgICAgICB9IGVsc2UgewogICAgICAgICAgcmV0dXJuIHZbY29kZV0uaW5jbHVkZXMoY29kZUxpc3RWYWd1ZSkKICAgICAgICB9CiAgICAgIH0pCgogICAgICAvLyDlkIjlubbkuKTkuKrmlbDnu4QKICAgICAgY29uc3QgbWVyZ2VkQXJyYXkgPSBzZWFyY2hUYkRhdGEuY29uY2F0KHNlYXJjaFZhZ3VlVGJEYXRhKQogICAgICAvLyDmoLnmja4gU2NoZHVsaW5nX0RldGFpbF9JZCDov5vooYzljrvph40KICAgICAgY29uc3QgdW5pcXVlQXJyYXkgPSBtZXJnZWRBcnJheS5yZWR1Y2UoKGFjYywgY3VycmVudCkgPT4gewogICAgICAgIGNvbnN0IGV4aXN0aW5nT2JqZWN0ID0gYWNjLmZpbmQoaXRlbSA9PiBpdGVtLlNjaGR1bGluZ19EZXRhaWxfSWQgPT09IGN1cnJlbnQuU2NoZHVsaW5nX0RldGFpbF9JZCkKICAgICAgICBpZiAoIWV4aXN0aW5nT2JqZWN0KSB7CiAgICAgICAgICBhY2MucHVzaChjdXJyZW50KQogICAgICAgIH0KICAgICAgICByZXR1cm4gYWNjCiAgICAgIH0sIFtdKQoKICAgICAgdGhpcy5maWx0ZXJUYkRhdGEgPSB1bmlxdWVBcnJheS5maWx0ZXIodiA9PiB7CiAgICAgICAgaWYgKCF0aGlzLnNlYXJjaFR5cGUpIHJldHVybiB0cnVlCiAgICAgICAgcmV0dXJuIHRoaXMuc2VhcmNoVHlwZSA9PT0gdi5UeXBlCiAgICAgIH0pLmZpbHRlcih2ID0+IHsKICAgICAgICBpZiAoIXRoaXMucXVlcnlGb3JtLlNwZWMpIHJldHVybiB0cnVlCiAgICAgICAgcmV0dXJuICh2LlNwZWMgfHwgJycpLmluY2x1ZGVzKHRoaXMucXVlcnlGb3JtLlNwZWMpCiAgICAgIH0pLmZpbHRlcihyb3cgPT4gewogICAgICAgIHJldHVybiB0aGlzLmZpbHRlclplcm8ocm93KQogICAgICB9KQogICAgfSwKICAgIGluaXRUYkRhdGEobGlzdCwgdGVhbUtleSA9ICdBbGxvY2F0aW9uX1RlYW1zJykgewogICAgICB0aGlzLnRiRGF0YSA9IGxpc3QubWFwKHJvdyA9PiB7CiAgICAgICAgY29uc3QgcHJvY2Vzc0xpc3QgPSByb3cuVGVjaG5vbG9neV9QYXRoPy5zcGxpdCgnLycpIHx8IFtdCiAgICAgICAgLy8g5beydXVpZOS9nOS4unJvd+WUr+S4gOWAvO+8mwogICAgICAgIC8vIHV1aWQr5bel5bqPK+ePree7hOS4uui+k+WFpeahhuWAvAogICAgICAgIHJvdy51dWlkID0gdXVpZHY0KCkKICAgICAgICBjb25zdCBuZXdEYXRhID0gcm93W3RlYW1LZXldLmZpbHRlcigocikgPT4gcHJvY2Vzc0xpc3QuZmluZEluZGV4KChwKSA9PiByLlByb2Nlc3NfQ29kZSA9PT0gcCkgIT09IC0xKQogICAgICAgIHJvdy5kZWZhdWx0Q2FuX0FsbG9jYXRpb25fQ291bnQgPSByb3cuQ2FuX0FsbG9jYXRpb25fQ291bnQKICAgICAgICBsZXQgX2lucHV0TnVtID0gMAogICAgICAgIG5ld0RhdGEuZm9yRWFjaCgoZWxlLCBpbmRleCkgPT4gewogICAgICAgICAgY29uc3QgY29kZSA9IHRoaXMuZ2V0Um93VW5pcXVlKHJvdy51dWlkLCBlbGUuUHJvY2Vzc19Db2RlLCBlbGUuV29ya2luZ19UZWFtX0lkKQogICAgICAgICAgY29uc3QgbWF4ID0gdGhpcy5nZXRSb3dVbmlxdWVNYXgocm93LnV1aWQsIGVsZS5Qcm9jZXNzX0NvZGUsIGVsZS5Xb3JraW5nX1RlYW1fSWQpCiAgICAgICAgICByb3dbY29kZV0gPSBlbGUuQ291bnQKICAgICAgICAgIHRoaXMuJHNldChyb3csICdhbENvdW50JyArIGNvZGUsIGVsZS5Db3VudCkKICAgICAgICAgIHJvd1ttYXhdID0gMAogICAgICAgICAgX2lucHV0TnVtICs9IGVsZS5Db3VudAogICAgICAgICAgdGhpcy4kc2V0KHJvdywgJ3RvdGFsVGFzaycgKyBlbGUuV29ya2luZ19UZWFtX05hbWUsIGVsZS5Ub3RhbF9SZWNlaXZlX0NvdW50ICsgZWxlLkNvdW50KQogICAgICAgIH0pCgogICAgICAgIHRoaXMuJHNldChyb3csICdhbGxvY2F0ZWRUYXNrJywgcm93LmRlZmF1bHRDYW5fQWxsb2NhdGlvbl9Db3VudCAtIF9pbnB1dE51bSkKICAgICAgICByb3cuQ2FuX0FsbG9jYXRpb25fQ291bnQgPSByb3cuYWxsb2NhdGVkVGFzawogICAgICAgIHRoaXMuJHNldChyb3csICdhbGxvY2F0ZWRUYXNrTWF4Jywgcm93LkNhbl9BbGxvY2F0aW9uX0NvdW50KQoKICAgICAgICB0aGlzLnNldElucHV0TWF4KHJvdykKCiAgICAgICAgcm93LmNoZWNrZWQgPSBmYWxzZQogICAgICAgIHJldHVybiByb3cKICAgICAgfSkKICAgIH0sCiAgICBpbnB1dENoYW5nZShyb3cpIHsKICAgICAgdGhpcy5zZXRJbnB1dE1heChyb3cpCiAgICB9LAogICAgc2V0SW5wdXRNYXgocm93KSB7CiAgICAgIGxldCBfaW5wdXROdW0gPSAwCiAgICAgIGNvbnN0IGlucHV0VmFsdWVzS2V5cyA9IE9iamVjdC5rZXlzKHJvdykKICAgICAgICAuZmlsdGVyKHYgPT4gIXYuZW5kc1dpdGgoJ21heCcpICYmIHYuc3RhcnRzV2l0aChyb3cudXVpZCkgJiYgdi5sZW5ndGggPiByb3cudXVpZC5sZW5ndGgpCiAgICAgIGlucHV0VmFsdWVzS2V5cy5mb3JFYWNoKCh2YWwpID0+IHsKICAgICAgICBjb25zdCBjdXJDb2RlID0gdmFsLnNwbGl0KFNQTElUX1NZTUJPTClbMV0KICAgICAgICBjb25zdCBvdGhlclRvdGFsID0gaW5wdXRWYWx1ZXNLZXlzLmZpbHRlcih4ID0+IHsKICAgICAgICAgIGNvbnN0IGNvZGUgPSB4LnNwbGl0KFNQTElUX1NZTUJPTClbMV0KICAgICAgICAgIHJldHVybiB4ICE9PSB2YWwgJiYgY29kZSA9PT0gY3VyQ29kZQogICAgICAgIH0pLnJlZHVjZSgoYWNjLCBpdGVtKSA9PiB7CiAgICAgICAgICByZXR1cm4gYWNjICsgbnVtZXJhbChyb3dbaXRlbV0pLnZhbHVlKCkKICAgICAgICB9LCAwKQogICAgICAgIHJvd1t2YWwgKyBTUExJVF9TWU1CT0wgKyAnbWF4J10gPSByb3cuU2NoZHVsZWRfQ291bnQgLSBvdGhlclRvdGFsCiAgICAgICAgX2lucHV0TnVtICs9ICtyb3dbdmFsXQogICAgICB9KQogICAgICAvLyByb3cuYWxsb2NhdGVkQ291bnQgPSByb3cuZGVmYXVsdENhbl9BbGxvY2F0aW9uX0NvdW50IC0gX2lucHV0TnVtCiAgICAgIC8vIHJvdy5DYW5fQWxsb2NhdGlvbl9Db3VudCA9IHJvdy5hbGxvY2F0ZWRDb3VudAogICAgfSwKICAgIGNoZWNrUGVybWlzc2lvblRlYW0ocHJvY2Vzc1N0ciwgcHJvY2Vzc0NvZGUpIHsKICAgICAgaWYgKCFwcm9jZXNzU3RyKSByZXR1cm4gZmFsc2UKICAgICAgY29uc3QgbGlzdCA9IHByb2Nlc3NTdHI/LnNwbGl0KCcvJykgfHwgW10KICAgICAgcmV0dXJuICEhbGlzdC5zb21lKHYgPT4gdiA9PT0gcHJvY2Vzc0NvZGUpCiAgICB9LAoKICAgIGdldFN1Ym1pdFRiSW5mbyh0YkRhdGEgPSB0aGlzLnRiRGF0YSkgewogICAgICAvLyDlpITnkIbkuIrkvKDnmoTmlbDmja4KICAgICAgY29uc3QgdGFibGVEYXRhID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0YkRhdGEpKQogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRhYmxlRGF0YS5sZW5ndGg7IGkrKykgewogICAgICAgIGNvbnN0IGVsZW1lbnQgPSB0YWJsZURhdGFbaV0KICAgICAgICBjb25zdCBsaXN0ID0gW10KICAgICAgICBpZiAoIWVsZW1lbnQuVGVjaG5vbG9neV9QYXRoKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogJ+W3peW6j+S4jeiDveS4uuepuicsCiAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgICAgfSkKICAgICAgICAgIHJldHVybiB7IHN0YXR1czogZmFsc2UgfQogICAgICAgIH0KICAgICAgICBjb25zdCBwcm9jZXNzTGlzdCA9IEFycmF5LmZyb20obmV3IFNldChlbGVtZW50LlRlY2hub2xvZ3lfUGF0aC5zcGxpdCgnLycpKSkKICAgICAgICBwcm9jZXNzTGlzdC5mb3JFYWNoKGNvZGUgPT4gewogICAgICAgICAgY29uc3QgZ3JvdXBzID0gdGhpcy53b3JraW5nVGVhbS5maWx0ZXIodiA9PiB2LlByb2Nlc3NfQ29kZSA9PT0gY29kZSkKCiAgICAgICAgICBjb25zdCBncm91cHNMaXN0ID0gZ3JvdXBzLm1hcCgoZ3JvdXAsIGluZGV4KSA9PiB7CiAgICAgICAgICAgIGNvbnN0IHVDb2RlID0gdGhpcy5nZXRSb3dVbmlxdWUoZWxlbWVudC51dWlkLCBjb2RlLCBncm91cC5Xb3JraW5nX1RlYW1fSWQpCiAgICAgICAgICAgIGNvbnN0IHVNYXggPSB0aGlzLmdldFJvd1VuaXF1ZU1heChlbGVtZW50LnV1aWQsIGNvZGUsIGdyb3VwLldvcmtpbmdfVGVhbV9JZCkKICAgICAgICAgICAgY29uc3Qgb2JqID0gewogICAgICAgICAgICAgIENvbXBfQ29kZTogZWxlbWVudC5Db21wX0NvZGUsCiAgICAgICAgICAgICAgQWdhaW5fQ291bnQ6ICtlbGVtZW50W3VDb2RlXSwKICAgICAgICAgICAgICBQYXJ0X0NvZGU6IHRoaXMuaXNDb20gPyBudWxsIDogZWxlbWVudC5QYXJ0X0NvZGUsCiAgICAgICAgICAgICAgUHJvY2Vzc19Db2RlOiBjb2RlLAogICAgICAgICAgICAgIFRlY2hub2xvZ3lfUGF0aDogZWxlbWVudC5UZWNobm9sb2d5X1BhdGgsCiAgICAgICAgICAgICAgV29ya2luZ19UZWFtX0lkOiBncm91cC5Xb3JraW5nX1RlYW1fSWQsCiAgICAgICAgICAgICAgV29ya2luZ19UZWFtX05hbWU6IGdyb3VwLldvcmtpbmdfVGVhbV9OYW1lLAogICAgICAgICAgICAgIFRlYW1fVGFza19JZDogZWxlbWVudC5BbGxvY2F0aW9uX1RlYW1zLmZpbmQoKGl0ZW0pID0+IGl0ZW0uV29ya2luZ19UZWFtX0lkID09PSBncm91cC5Xb3JraW5nX1RlYW1fSWQpLlRlYW1fVGFza19JZAogICAgICAgICAgICB9CiAgICAgICAgICAgIGRlbGV0ZSBlbGVtZW50WydhbENvdW50JyArIHVDb2RlXQogICAgICAgICAgICBkZWxldGUgZWxlbWVudFsnYWxsb2NhdGVkVGFzayddCiAgICAgICAgICAgIGRlbGV0ZSBlbGVtZW50WydhbGxvY2F0ZWRUYXNrTWF4J10KICAgICAgICAgICAgZGVsZXRlIGVsZW1lbnRbdUNvZGVdCiAgICAgICAgICAgIGRlbGV0ZSBlbGVtZW50W3VNYXhdCiAgICAgICAgICAgIHJldHVybiBvYmoKICAgICAgICAgIH0pCiAgICAgICAgICBsaXN0LnB1c2goLi4uZ3JvdXBzTGlzdCkKICAgICAgICB9KQogICAgICAgIGNvbnNvbGUubG9nKGxpc3QpCiAgICAgICAgZGVsZXRlIGVsZW1lbnRbJ3V1aWQnXQogICAgICAgIGRlbGV0ZSBlbGVtZW50WydwdXVpZCddCiAgICAgICAgZWxlbWVudC5BbGxvY2F0aW9uX1RlYW1zID0gbGlzdAogICAgICB9CiAgICAgIHJldHVybiB7IHRhYmxlRGF0YSwgc3RhdHVzOiB0cnVlIH0KICAgIH0sCiAgICBoYW5kbGVTdWJtaXQoKSB7CiAgICAgIGNvbnN0IHsgdGFibGVEYXRhLCBzdGF0dXMgfSA9IHRoaXMuZ2V0U3VibWl0VGJJbmZvKCkKICAgICAgaWYgKCFzdGF0dXMpIHJldHVybgogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbmj5DkuqTlvZPliY3mlbDmja4/JywgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMubG9hZGluZyA9IHRydWUKICAgICAgICBjb25zdCBvYmogPSB7CiAgICAgICAgICBTY2hkdWxpbmdfUGxhbjogdGhpcy5wbGFuSW5mb1RlbXAsCiAgICAgICAgICBTY2hkdWxpbmdfQ29tcHM6IHRhYmxlRGF0YQogICAgICAgIH0KICAgICAgICBjb25zdCBQYXJ0b2JqID0gewogICAgICAgICAgU2NoZHVsaW5nX1BsYW46IHRoaXMucGxhbkluZm9UZW1wLAogICAgICAgICAgU2FyZVBhcnRzTW9kZWw6IHRhYmxlRGF0YQogICAgICAgIH0KICAgICAgICBjb25zdCByZXF1ZXN0Rm4gPSB0aGlzLmlzQ29tID8gQWRqdXN0VGVhbVByb2Nlc3NBbGxvY2F0aW9uIDogdGhpcy5pc1VuaXRQYXJ0ID8gQWRqdXN0U3ViQXNzZW1ibHlUZWFtUHJvY2Vzc0FsbG9jYXRpb24gOiBBZGp1c3RQYXJ0VGVhbVByb2Nlc3NBbGxvY2F0aW9uCiAgICAgICAgY29uc29sZS5sb2coJ29iaicsIG9iaikKICAgICAgICByZXF1ZXN0Rm4odGhpcy5pc0NvbSA/IG9iaiA6IFBhcnRvYmopLnRoZW4ocmVzID0+IHsKICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIG1lc3NhZ2U6ICfmk43kvZzmiJDlip8nLAogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJwogICAgICAgICAgICB9KQogICAgICAgICAgICB0aGlzLmhhbmRsZUNsb3NlKCkKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgICAgfSkKICAgICAgICAgIH0KICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlCiAgICAgICAgfSkKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ2luZm8nLAogICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iCcKICAgICAgICB9KQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZUNsb3NlKCkgewogICAgICB0aGlzLmNsb3NlVmlldygpCiAgICB9LAogICAgY2xvc2VWaWV3KCkgewogICAgICBjbG9zZVRhZ1ZpZXcodGhpcy4kc3RvcmUsIHRoaXMuJHJvdXRlKQogICAgfSwKICAgIHRiU2VsZWN0Q2hhbmdlKGFycmF5KSB7CiAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24gPSBhcnJheS5yZWNvcmRzCiAgICAgIGNvbnNvbGUubG9nKHRoaXMudGJEYXRhKQogICAgfSwKICAgIGdldFRhc2tDb2RlKG5hbWUgPSAnJykgewogICAgICBpZiAobmFtZSkgcmV0dXJuICd0b3RhbFRhc2snICsgbmFtZQogICAgICByZXR1cm4gJ3RvdGFsVGFzaycgKyB0aGlzLmFjdGl2ZU5hbWUuc3BsaXQoU1BMSVRfU1lNQk9MKVswXQogICAgfSwKICAgIC8vIOWPjemAiQogICAgcmV2ZXJzZVNlbGVjdGlvbigpIHsKICAgICAgY29uc3QgbGlzdCA9IHRoaXMuJHJlZnMueFRhYmxlLmdldENoZWNrYm94UmVjb3JkcygpLmZpbHRlcihpdGVtID0+ICFpdGVtLnN0b3BGbGFnKQogICAgICBjb25zdCB1blNlbGVjdExpc3QgPSB0aGlzLmZpbHRlclRiRGF0YS5maWx0ZXIoaXRlbSA9PiAhbGlzdC5pbmNsdWRlcyhpdGVtKSAmJiAhaXRlbS5zdG9wRmxhZykKICAgICAgdGhpcy4kcmVmcy54VGFibGUuc2V0Q2hlY2tib3hSb3cobGlzdCwgZmFsc2UpCiAgICAgIHRoaXMuJHJlZnMueFRhYmxlLnNldENoZWNrYm94Um93KHVuU2VsZWN0TGlzdCwgdHJ1ZSkKICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbiA9IHRoaXMuJHJlZnMueFRhYmxlLmdldENoZWNrYm94UmVjb3JkcygpCiAgICB9LAogICAgYXN5bmMgZ2V0VGFibGVDb25maWcoY29kZSkgewogICAgICBhd2FpdCBHZXRHcmlkQnlDb2RlKHsKICAgICAgICBjb2RlCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gewogICAgICAgIGNvbnN0IHsgSXNTdWNjZWVkLCBEYXRhLCBNZXNzYWdlIH0gPSByZXMKICAgICAgICBpZiAoSXNTdWNjZWVkKSB7CiAgICAgICAgICB0aGlzLnRiQ29uZmlnID0gT2JqZWN0LmFzc2lnbih7fSwgdGhpcy50YkNvbmZpZywgRGF0YS5HcmlkKQogICAgICAgICAgY29uc3QgbGlzdCA9IERhdGEuQ29sdW1uTGlzdCB8fCBbXQogICAgICAgICAgdGhpcy5jb2x1bW5zID0gbGlzdC5maWx0ZXIodiA9PiB2LklzX0Rpc3BsYXkpLm1hcChpdGVtID0+IHsKICAgICAgICAgICAgaWYgKEZJWF9DT0xVTU4uaW5jbHVkZXMoaXRlbS5Db2RlKSkgewogICAgICAgICAgICAgIGl0ZW0uZml4ZWQgPSAnbGVmdCcKICAgICAgICAgICAgfQogICAgICAgICAgICBpZiAoaXRlbS5Db2RlID09PSAnU2NoZHVsZWRfQ291bnQnKSB7CiAgICAgICAgICAgICAgaXRlbS52aXNpYmxlID0gZmFsc2UKICAgICAgICAgICAgfQogICAgICAgICAgICByZXR1cm4gaXRlbQogICAgICAgICAgfSkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIG1lc3NhZ2U6IE1lc3NhZ2UsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGFjdGl2ZUNlbGxNZXRob2QoeyByb3csIGNvbHVtbiwgY29sdW1uSW5kZXggfSkgewogICAgICBpZiAodGhpcy5pc1ZpZXcpIHJldHVybiBmYWxzZQogICAgICBpZiAoY29sdW1uLmZpZWxkID09PSAnQWxsb2NhdGVkQ291bnQnKSByZXR1cm4gdHJ1ZQogICAgICBjb25zdCBwcm9jZXNzQ29kZSA9IGNvbHVtbi5maWVsZD8uc3BsaXQoJyRfJCcpWzFdCiAgICAgIHJldHVybiB0aGlzLmNoZWNrUGVybWlzc2lvblRlYW0ocm93LlRlY2hub2xvZ3lfUGF0aCwgcHJvY2Vzc0NvZGUpCiAgICB9LAogICAgZ2V0Um93VW5pcXVlKHV1aWQsIHByb2Nlc3NDb2RlLCB3b3JraW5nSWQpIHsKICAgICAgcmV0dXJuIGAke3V1aWR9JHtTUExJVF9TWU1CT0x9JHtwcm9jZXNzQ29kZX0ke1NQTElUX1NZTUJPTH0ke3dvcmtpbmdJZH1gCiAgICB9LAogICAgZ2V0Um93VW5pcXVlTWF4KHV1aWQsIHByb2Nlc3NDb2RlLCB3b3JraW5nSWQpIHsKICAgICAgcmV0dXJuIHRoaXMuZ2V0Um93VW5pcXVlKHV1aWQsIHByb2Nlc3NDb2RlLCB3b3JraW5nSWQpICsgYCR7U1BMSVRfU1lNQk9MfW1heGAKICAgIH0sCiAgICAvLyDmibnph4/liIbphY0KICAgIEJhdGNoYWxsb2NhdGlvbigpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQogICAgICBjb25zb2xlLmxvZyh0aGlzLndvcmtpbmdUZWFtKQogICAgfSwKICAgIC8vIOS4iumBk+W3peW6j+WIhumFjQogICAgcHJlU3RlcFRhc2tBbGxvY2F0aW9uKCkgewogICAgICBjb25zdCBTY2hkdWxpbmdfRGV0YWlsX0lkcyA9IHRoaXMubXVsdGlwbGVTZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5TY2hkdWxpbmdfRGV0YWlsX0lkKQogICAgICBjb25zdCBXb3JraW5nX1Byb2Nlc3NfQ29kZSA9IHRoaXMubXVsdGlwbGVTZWxlY3Rpb25bMF0uUHJvY2Vzc19Db2RlCiAgICAgIEdldFByZVN0ZXBUYXNrQWxsb2NhdGlvbih7CiAgICAgICAgU2NoZHVsaW5nX0RldGFpbF9JZHMsCiAgICAgICAgV29ya2luZ19Qcm9jZXNzX0NvZGUsCiAgICAgICAgV29ya2luZ19Qcm9jZXNzX0lkOiB0aGlzLldvcmtpbmdfUHJvY2Vzc19JZCwKICAgICAgICBQcm9jZXNzX1R5cGU6IHRoaXMuaXNDb20gPyAyIDogdGhpcy5pc1VuaXRQYXJ0ID8gMyA6IDEsCiAgICAgICAgQm9tX0xldmVsOiB0aGlzLmJvbUxldmVsCiAgICAgIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgaWYgKHJlcy5EYXRhLmxlbmd0aCA9PT0gMCkgewogICAgICAgICAgICB0aGlzLmRpYWxvZ1RpcHNWaXNpYmxlID0gdHJ1ZQogICAgICAgICAgICByZXR1cm4KICAgICAgICAgIH0KICAgICAgICAgIHRoaXMucHJlRG9BbGxvY2F0aW9uKHJlcy5EYXRhKQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZURpYWxvZygpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UKICAgICAgLy8gdGhpcy5tdWx0aXBsZVNlbGVjdGlvbiA9IFtdCiAgICB9LAogICAgaGFuZGVsRGF0YSgpIHsKICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5mb3JFYWNoKChpdGVtKSA9PgogICAgICAgIGl0ZW0uQWxsb2NhdGlvbl9UZWFtcy5mb3JFYWNoKCh2KSA9PiB7CiAgICAgICAgICBpZiAodi5Xb3JraW5nX1RlYW1fSWQgPT09IHRoaXMuZm9ybS5UZWFtR3JvdXApIHsKICAgICAgICAgICAgdi5Db3VudCA9IGl0ZW0uQ2FuX0FsbG9jYXRpb25fQ291bnQKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHYuQ291bnQgPSAwCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgKQogICAgICAvLyBjb25zdCB0YWJsZURhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMubXVsdGlwbGVTZWxlY3Rpb24pKQogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMubXVsdGlwbGVTZWxlY3Rpb24ubGVuZ3RoOyBpKyspIHsKICAgICAgICBjb25zdCBlbGVtZW50ID0gdGhpcy5tdWx0aXBsZVNlbGVjdGlvbltpXQogICAgICAgIGNvbnN0IHByb2Nlc3NMaXN0ID0gQXJyYXkuZnJvbShuZXcgU2V0KGVsZW1lbnQuVGVjaG5vbG9neV9QYXRoLnNwbGl0KCcvJykpKQogICAgICAgIHByb2Nlc3NMaXN0LmZvckVhY2goY29kZSA9PiB7CiAgICAgICAgICBjb25zdCBncm91cHMgPSB0aGlzLndvcmtpbmdUZWFtLmZpbHRlcih2ID0+IHYuUHJvY2Vzc19Db2RlID09PSBjb2RlKQogICAgICAgICAgZ3JvdXBzLmZvckVhY2goZ3JvdXAgPT4gewogICAgICAgICAgICBjb25zdCB1bmlDb2RlID0gdGhpcy5nZXRSb3dVbmlxdWUoZWxlbWVudC51dWlkLCBjb2RlLCB0aGlzLmZvcm0uVGVhbUdyb3VwKQogICAgICAgICAgICBjb25zdCB1Q29kZSA9IHRoaXMuZ2V0Um93VW5pcXVlKGVsZW1lbnQudXVpZCwgY29kZSwgZ3JvdXAuV29ya2luZ19UZWFtX0lkKQogICAgICAgICAgICBjb25zdCB1bmlNYXggPSB0aGlzLmdldFJvd1VuaXF1ZU1heChlbGVtZW50LnV1aWQsIGNvZGUsIHRoaXMuZm9ybS5UZWFtR3JvdXApCiAgICAgICAgICAgIGNvbnN0IHVNYXggPSB0aGlzLmdldFJvd1VuaXF1ZU1heChlbGVtZW50LnV1aWQsIGNvZGUsIGdyb3VwLldvcmtpbmdfVGVhbV9JZCkKCiAgICAgICAgICAgIGlmICh1bmlDb2RlID09PSB1Q29kZSAmJiB1bmlNYXggPT09IHVNYXgpIHsKICAgICAgICAgICAgICBlbGVtZW50W3VDb2RlXSA9IGVsZW1lbnRbJ0Nhbl9BbGxvY2F0aW9uX0NvdW50J10KICAgICAgICAgICAgICBlbGVtZW50W3VNYXhdID0gZWxlbWVudFsnQ2FuX0FsbG9jYXRpb25fQ291bnQnXQogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIGVsZW1lbnRbdUNvZGVdID0gMAogICAgICAgICAgICAgIGVsZW1lbnRbdU1heF0gPSAwCiAgICAgICAgICAgIH0KICAgICAgICAgIH0pCiAgICAgICAgfSkKICAgICAgfQogICAgICBjb25zb2xlLmxvZyh0aGlzLm11bHRpcGxlU2VsZWN0aW9uKQogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMudGJEYXRhLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgZm9yIChsZXQgayA9IDA7IGsgPCB0aGlzLm11bHRpcGxlU2VsZWN0aW9uLmxlbmd0aDsgaysrKSB7CiAgICAgICAgICBpZiAodGhpcy50YkRhdGFbaV0udXVpZCA9PT0gdGhpcy5tdWx0aXBsZVNlbGVjdGlvbltrXS51dWlkKSB7CiAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy50YkRhdGFbaV0gPSB0aGlzLm11bHRpcGxlU2VsZWN0aW9uW2tdCiAgICAgICAgICAgIH0pCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgc3VibWl0Rm9ybShmb3JtTmFtZSkgewogICAgICB0aGlzLiRyZWZzW2Zvcm1OYW1lXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIC8vIHRoaXMuaGFuZGVsRGF0YSgpCiAgICAgICAgICB0aGlzLmRvQWxsb2NhdGlvbigpCiAgICAgICAgICAvLyB0aGlzLndvcmtpbmdUZWFtLmZpbmQodj0+IHYuV29ya2luZ19UZWFtX0lkID09PSB0aGlzLmZvcm0uVGVhbUdyb3VwKS5jb3VudAogICAgICAgICAgdGhpcy5oYW5kbGVEaWFsb2coKQogICAgICAgICAgY29uc29sZS5sb2codGhpcy50YkRhdGEpCiAgICAgICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gW10KICAgICAgICAgIHRoaXMuJHJlZnMueFRhYmxlLmNsZWFyQ2hlY2tib3hSb3coKQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICByZXR1cm4gZmFsc2UKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgLy8g5LiK6YGT5bel5bqP5YiG6YWNCiAgICBwcmVEb0FsbG9jYXRpb24ocHJlUHJvY2Vzc0RhdGEpIHsKICAgICAgY29uc3QgcHJlUHJvY2Vzc0RhdGFNYXAgPSBuZXcgTWFwKCkKICAgICAgcHJlUHJvY2Vzc0RhdGEuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICBjb25zdCBrZXkgPSBgJHtpdGVtLlNjaGR1bGluZ19EZXRhaWxfSWR9XyR7aXRlbS5Xb3JraW5nX1RlYW1fSWR9YAogICAgICAgIHByZVByb2Nlc3NEYXRhTWFwLnNldChrZXksIGl0ZW0uQ3VycmVudF9UYXNrX0NvdW50KQogICAgICB9KQoKICAgICAgY29uc3QgYWxsb2NhdGVGb3JUZWFtID0gKHJvdywgdGVhbSwgYW1vdW50KSA9PiB7CiAgICAgICAgY29uc3QgdGFyQ29kZSA9IHRoaXMuZ2V0Um93VW5pcXVlKHJvdy51dWlkLCB0ZWFtLlByb2Nlc3NfQ29kZSwgdGVhbS5Xb3JraW5nX1RlYW1fSWQpCiAgICAgICAgY29uc3QgYktleSA9IGAke3Jvdy5TY2hkdWxpbmdfRGV0YWlsX0lkfV8ke3RlYW0uV29ya2luZ19UZWFtX0lkfWAKICAgICAgICBjb25zdCBjdXJyZW50VGFza0NvdW50ID0gcHJlUHJvY2Vzc0RhdGFNYXAuZ2V0KGJLZXkpIHx8IDAKICAgICAgICBjb25zdCBhbGxvY2F0ZWQgPSBNYXRoLm1pbihhbW91bnQsIGN1cnJlbnRUYXNrQ291bnQsIHJvdy5DYW5fQWxsb2NhdGlvbl9Db3VudCkKCiAgICAgICAgcm93W3RhckNvZGVdID0gKE51bWJlcihyb3dbdGFyQ29kZV0pIHx8IDApICsgYWxsb2NhdGVkCiAgICAgICAgcm93LkNhbl9BbGxvY2F0aW9uX0NvdW50IC09IGFsbG9jYXRlZAogICAgICAgIHJvd1t0aGlzLmdldFRhc2tDb2RlKHRlYW0uV29ya2luZ19UZWFtX05hbWUpXSA9IChyb3dbdGhpcy5nZXRUYXNrQ29kZSh0ZWFtLldvcmtpbmdfVGVhbV9OYW1lKV0gfHwgMCkgKyBhbGxvY2F0ZWQKICAgICAgICByb3dbdGhpcy5nZXRUYXNrQ29kZSgpXSA9IChyb3dbdGhpcy5nZXRUYXNrQ29kZSgpXSB8fCAwKSAtIGFsbG9jYXRlZAogICAgICAgIHJvd1snYWxDb3VudCcgKyB0YXJDb2RlXSA9IHJvd1t0YXJDb2RlXQoKICAgICAgICByZXR1cm4gYWxsb2NhdGVkCiAgICAgIH0KICAgICAgbGV0IGlzTWVzc2FnZSA9IHRydWUKICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5mb3JFYWNoKHJvdyA9PiB7CiAgICAgICAgaWYgKCFyb3cuQ2FuX0FsbG9jYXRpb25fQ291bnQgJiYgdGhpcy5hY3RpdmVOYW1lID09PSAnZmlyc3QnKSByZXR1cm4KCiAgICAgICAgY29uc3QgZWxpZ2libGVUZWFtcyA9IHJvdy5BbGxvY2F0aW9uX1RlYW1zLmZpbHRlcih0ZWFtID0+CiAgICAgICAgICBwcmVQcm9jZXNzRGF0YU1hcC5oYXMoYCR7cm93LlNjaGR1bGluZ19EZXRhaWxfSWR9XyR7dGVhbS5Xb3JraW5nX1RlYW1fSWR9YCkKICAgICAgICApCgogICAgICAgIGlmIChlbGlnaWJsZVRlYW1zLmxlbmd0aCA9PT0gMCkgcmV0dXJuCiAgICAgICAgaWYgKHRoaXMuYWN0aXZlTmFtZSA9PT0gJ2ZpcnN0JykgewogICAgICAgICAgbGV0IElzQWxsTm8gPSAwCiAgICAgICAgICBjb25zdCB0b3RhbEF2YWlsYWJsZSA9IGVsaWdpYmxlVGVhbXMucmVkdWNlKChzdW0sIHRlYW0pID0+IHsKICAgICAgICAgICAgY29uc3QgYktleSA9IGAke3Jvdy5TY2hkdWxpbmdfRGV0YWlsX0lkfV8ke3RlYW0uV29ya2luZ19UZWFtX0lkfWAKICAgICAgICAgICAgcmV0dXJuIHN1bSArIChwcmVQcm9jZXNzRGF0YU1hcC5nZXQoYktleSkgfHwgMCkKICAgICAgICAgIH0sIDApCgogICAgICAgICAgaWYgKHJvdy5hbGxvY2F0ZWRUYXNrTWF4IDwgdG90YWxBdmFpbGFibGUpIHsKICAgICAgICAgICAgSXNBbGxObysrCiAgICAgICAgICAgIGlmIChJc0FsbE5vID09PSB0aGlzLm11bHRpcGxlU2VsZWN0aW9uLmxlbmd0aCkgewogICAgICAgICAgICAgIGlzTWVzc2FnZSA9IGZhbHNlCiAgICAgICAgICAgICAgdGhpcy5kaWFsb2dUaXBzVmlzaWJsZSA9IHRydWUKICAgICAgICAgICAgfQogICAgICAgICAgICByZXR1cm4KICAgICAgICAgIH0KCiAgICAgICAgICBsZXQgcmVtYWluaW5nID0gTWF0aC5taW4ocm93LmFsbG9jYXRlZFRhc2ssIHJvdy5DYW5fQWxsb2NhdGlvbl9Db3VudCkKICAgICAgICAgIGNvbnN0IHBlclRlYW1BbGxvY2F0aW9uID0gTWF0aC5mbG9vcihyZW1haW5pbmcgLyBlbGlnaWJsZVRlYW1zLmxlbmd0aCkKCiAgICAgICAgICBlbGlnaWJsZVRlYW1zLmZvckVhY2goKHRlYW0sIGluZGV4KSA9PiB7CiAgICAgICAgICAgIGlmIChyZW1haW5pbmcgPD0gMCkgcmV0dXJuCgogICAgICAgICAgICBjb25zdCBhbGxvY2F0ZUFtb3VudCA9IGluZGV4ID09PSBlbGlnaWJsZVRlYW1zLmxlbmd0aCAtIDEKICAgICAgICAgICAgICA/IHJlbWFpbmluZwogICAgICAgICAgICAgIDogTWF0aC5taW4ocGVyVGVhbUFsbG9jYXRpb24sIHJlbWFpbmluZykKCiAgICAgICAgICAgIHJlbWFpbmluZyAtPSBhbGxvY2F0ZUZvclRlYW0ocm93LCB0ZWFtLCBhbGxvY2F0ZUFtb3VudCkKICAgICAgICAgIH0pCgogICAgICAgICAgcm93LmFsbG9jYXRlZFRhc2tNYXggPSByb3cuQ2FuX0FsbG9jYXRpb25fQ291bnQKICAgICAgICAgIHJvdy5hbGxvY2F0ZWRUYXNrID0gcm93LkNhbl9BbGxvY2F0aW9uX0NvdW50CiAgICAgICAgICBpZiAoSXNBbGxObyA9PT0gdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5sZW5ndGgpIHsKICAgICAgICAgICAgaXNNZXNzYWdlID0gZmFsc2UKICAgICAgICAgICAgdGhpcy5kaWFsb2dUaXBzVmlzaWJsZSA9IHRydWUKICAgICAgICAgICAgcmV0dXJuCiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGVsaWdpYmxlVGVhbXMuZm9yRWFjaCh0ZWFtID0+IHsKICAgICAgICAgICAgY29uc3QgYktleSA9IGAke3Jvdy5TY2hkdWxpbmdfRGV0YWlsX0lkfV8ke3RlYW0uV29ya2luZ19UZWFtX0lkfWAKICAgICAgICAgICAgY29uc3QgY3VycmVudFRhc2tDb3VudCA9IHByZVByb2Nlc3NEYXRhTWFwLmdldChiS2V5KSB8fCAwCgogICAgICAgICAgICBpZiAocm93W3RoaXMuZ2V0Um93Q0NvZGUocm93KV0gPCBjdXJyZW50VGFza0NvdW50KSB7CiAgICAgICAgICAgICAgcmV0dXJuCiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIGNvbnN0IHNlbGVjdE51bSA9IE1hdGgubWluKAogICAgICAgICAgICAgIHJvd1t0aGlzLmdldFJvd0NDb2RlKHJvdywgJ2FsQ291bnQnKV0gfHwgMCwKICAgICAgICAgICAgICByb3dbdGhpcy5nZXRSb3dDQ29kZShyb3cpXSB8fCAwLAogICAgICAgICAgICAgIGN1cnJlbnRUYXNrQ291bnQKICAgICAgICAgICAgKQoKICAgICAgICAgICAgaWYgKHNlbGVjdE51bSA+IDApIHsKICAgICAgICAgICAgICBjb25zdCB0YXJDb2RlID0gdGhpcy5nZXRSb3dVbmlxdWUocm93LnV1aWQsIHRlYW0uUHJvY2Vzc19Db2RlLCB0ZWFtLldvcmtpbmdfVGVhbV9JZCkKICAgICAgICAgICAgICByb3dbdGhpcy5nZXRUYXNrQ29kZSh0ZWFtLldvcmtpbmdfVGVhbV9OYW1lKV0gPSAocm93W3RoaXMuZ2V0VGFza0NvZGUodGVhbS5Xb3JraW5nX1RlYW1fTmFtZSldIHx8IDApICsgc2VsZWN0TnVtCiAgICAgICAgICAgICAgcm93W3RoaXMuZ2V0VGFza0NvZGUoKV0gPSAocm93W3RoaXMuZ2V0VGFza0NvZGUoKV0gfHwgMCkgLSBzZWxlY3ROdW0KICAgICAgICAgICAgICByb3dbdGFyQ29kZV0gPSAoTnVtYmVyKHJvd1t0YXJDb2RlXSkgfHwgMCkgKyBzZWxlY3ROdW0KICAgICAgICAgICAgICByb3dbdGhpcy5nZXRSb3dDQ29kZShyb3cpXSA9IChyb3dbdGhpcy5nZXRSb3dDQ29kZShyb3cpXSB8fCAwKSAtIHNlbGVjdE51bQogICAgICAgICAgICAgIHJvd1t0aGlzLmdldFJvd0NDb2RlKHJvdywgJ2FsQ291bnQnKV0gPSAocm93W3RoaXMuZ2V0Um93Q0NvZGUocm93LCAnYWxDb3VudCcpXSB8fCAwKSAtIHNlbGVjdE51bQogICAgICAgICAgICAgIHJvd1snYWxDb3VudCcgKyB0YXJDb2RlXSA9IHJvd1t0YXJDb2RlXQogICAgICAgICAgICB9CiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgICAgLy8gaWYgKHRoaXMuYWN0aXZlTmFtZSA9PT0gJ2ZpcnN0JykgewogICAgICAvLyAgIGxldCBJc0FsbE5vID0gMAogICAgICAvLyAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24uZm9yRWFjaCgocm93LCBpZHgpID0+IHsKICAgICAgLy8gICAgIGlmIChyb3cuQ2FuX0FsbG9jYXRpb25fQ291bnQpIHsKICAgICAgLy8gICAgICAgY29uc3QgdmFsaWRUZWFtcyA9IHJvdy5BbGxvY2F0aW9uX1RlYW1zLmZpbHRlcih0ZWFtID0+IHsKICAgICAgLy8gICAgICAgICBjb25zdCBrZXkgPSBgJHtyb3cuU2NoZHVsaW5nX0RldGFpbF9JZH1fJHt0ZWFtLldvcmtpbmdfVGVhbV9JZH1gCiAgICAgIC8vICAgICAgICAgcmV0dXJuIHByZVByb2Nlc3NEYXRhTWFwLmhhcyhrZXkpCiAgICAgIC8vICAgICAgIH0pCiAgICAgIC8vICAgICAgIGlmICh2YWxpZFRlYW1zLmxlbmd0aCA+IDApIHsKICAgICAgLy8gICAgICAgICBjb25zdCB0ZWFtID0gdmFsaWRUZWFtc1swXQogICAgICAvLyAgICAgICAgIGNvbnN0IHRhckNvZGUgPSB0aGlzLmdldFJvd1VuaXF1ZShyb3cudXVpZCwgdGVhbS5Qcm9jZXNzX0NvZGUsIHRlYW0uV29ya2luZ19UZWFtX0lkKQoKICAgICAgLy8gICAgICAgICBjb25zdCBwcmVQcm9jZXNzS2V5ID0gYCR7cm93LlNjaGR1bGluZ19EZXRhaWxfSWR9XyR7dGVhbS5Xb3JraW5nX1RlYW1fSWR9YAogICAgICAvLyAgICAgICAgIGNvbnN0IGN1cnJlbnRUYXNrQ291bnQgPSBwcmVQcm9jZXNzRGF0YU1hcC5nZXQocHJlUHJvY2Vzc0tleSkgfHwgMAoKICAgICAgLy8gICAgICAgICBpZiAoY3VycmVudFRhc2tDb3VudCA+IHJvdy5hbGxvY2F0ZWRUYXNrTWF4KSB7CiAgICAgIC8vICAgICAgICAgICBJc0FsbE5vKysKICAgICAgLy8gICAgICAgICAgIHJldHVybgogICAgICAvLyAgICAgICAgIH0KICAgICAgLy8gICAgICAgICBjb25zdCBhbGxvY2F0ZWQgPSBNYXRoLm1pbihyb3cuYWxsb2NhdGVkVGFzaywgY3VycmVudFRhc2tDb3VudCkKCiAgICAgIC8vICAgICAgICAgcm93W3RhckNvZGVdID0gTnVtYmVyKHJvd1t0YXJDb2RlXSB8fCAwKSArIGFsbG9jYXRlZAogICAgICAvLyAgICAgICAgIHJvdy5DYW5fQWxsb2NhdGlvbl9Db3VudCA9IHJvdy5DYW5fQWxsb2NhdGlvbl9Db3VudCAtIGFsbG9jYXRlZAogICAgICAvLyAgICAgICAgIHJvd1t0aGlzLmdldFRhc2tDb2RlKHRlYW0uV29ya2luZ19UZWFtX05hbWUpIHx8IDBdID0gKHJvd1t0aGlzLmdldFRhc2tDb2RlKHRlYW0uV29ya2luZ19UZWFtX05hbWUpXSB8fCAwKSArIGFsbG9jYXRlZAogICAgICAvLyAgICAgICAgIHJvd1t0aGlzLmdldFRhc2tDb2RlKCldID0gKHJvd1t0aGlzLmdldFRhc2tDb2RlKCldIHx8IDApIC0gYWxsb2NhdGVkCiAgICAgIC8vICAgICAgICAgcm93LmFsbG9jYXRlZFRhc2tNYXggPSByb3cuQ2FuX0FsbG9jYXRpb25fQ291bnQKICAgICAgLy8gICAgICAgICByb3cuYWxsb2NhdGVkVGFzayA9IHJvdy5DYW5fQWxsb2NhdGlvbl9Db3VudAogICAgICAvLyAgICAgICAgIHJvd1snYWxDb3VudCcgKyB0YXJDb2RlXSA9IHJvd1t0YXJDb2RlXQogICAgICAvLyAgICAgICB9CiAgICAgIC8vICAgICB9CiAgICAgIC8vICAgfSkKICAgICAgLy8gICBpZiAoSXNBbGxObyA9PT0gdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5sZW5ndGgpIHsKICAgICAgLy8gICAgIHRoaXMuZGlhbG9nVGlwc1Zpc2libGUgPSB0cnVlCiAgICAgIC8vICAgICByZXR1cm4KICAgICAgLy8gICB9CiAgICAgIC8vIH0gZWxzZSB7CiAgICAgIC8vICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5mb3JFYWNoKChyb3csIGlkeCkgPT4gewogICAgICAvLyAgICAgY29uc3QgdmFsaWRUZWFtcyA9IHJvdy5BbGxvY2F0aW9uX1RlYW1zLmZpbHRlcih0ZWFtID0+IHsKICAgICAgLy8gICAgICAgY29uc3Qga2V5ID0gYCR7cm93LlNjaGR1bGluZ19EZXRhaWxfSWR9XyR7dGVhbS5Xb3JraW5nX1RlYW1fSWR9YAogICAgICAvLyAgICAgICByZXR1cm4gcHJlUHJvY2Vzc0RhdGFNYXAuaGFzKGtleSkKICAgICAgLy8gICAgIH0pCiAgICAgIC8vICAgICBpZiAodmFsaWRUZWFtcy5sZW5ndGggPiAwKSB7CiAgICAgIC8vICAgICAgIGNvbnN0IHRlYW0gPSB2YWxpZFRlYW1zWzBdCiAgICAgIC8vICAgICAgIGNvbnN0IHRhckNvZGUgPSB0aGlzLmdldFJvd1VuaXF1ZShyb3cudXVpZCwgdGVhbS5Qcm9jZXNzX0NvZGUsIHRlYW0uV29ya2luZ19UZWFtX0lkKQoKICAgICAgLy8gICAgICAgY29uc3QgcHJlUHJvY2Vzc0tleSA9IGAke3Jvdy5TY2hkdWxpbmdfRGV0YWlsX0lkfV8ke3RlYW0uV29ya2luZ19UZWFtX0lkfWAKICAgICAgLy8gICAgICAgY29uc3QgY3VycmVudFRhc2tDb3VudCA9IHByZVByb2Nlc3NEYXRhTWFwLmdldChwcmVQcm9jZXNzS2V5KSB8fCAwCgogICAgICAvLyAgICAgICBjb25zdCBzZWxlY3ROdW0gPSBNYXRoLm1pbigKICAgICAgLy8gICAgICAgICByb3dbdGhpcy5nZXRSb3dDQ29kZShyb3csICdhbENvdW50JyldIHx8IDAsCiAgICAgIC8vICAgICAgICAgcm93W3RoaXMuZ2V0Um93Q0NvZGUocm93KV0gfHwgMCwKICAgICAgLy8gICAgICAgICBjdXJyZW50VGFza0NvdW50CiAgICAgIC8vICAgICAgICkKCiAgICAgIC8vICAgICAgIHJvd1t0aGlzLmdldFRhc2tDb2RlKHRlYW0uV29ya2luZ19UZWFtX05hbWUpXSA9IChyb3dbdGhpcy5nZXRUYXNrQ29kZSh0ZWFtLldvcmtpbmdfVGVhbV9OYW1lKV0gfHwgMCkgKyBzZWxlY3ROdW0KICAgICAgLy8gICAgICAgcm93W3RoaXMuZ2V0VGFza0NvZGUoKV0gPSAocm93W3RoaXMuZ2V0VGFza0NvZGUoKV0gfHwgMCkgLSBzZWxlY3ROdW0KICAgICAgLy8gICAgICAgcm93W3RhckNvZGVdID0gKE51bWJlcihyb3dbdGFyQ29kZV0pIHx8IDApICsgc2VsZWN0TnVtCiAgICAgIC8vICAgICAgIHJvd1t0aGlzLmdldFJvd0NDb2RlKHJvdyldID0gKHJvd1t0aGlzLmdldFJvd0NDb2RlKHJvdyldIHx8IDApIC0gc2VsZWN0TnVtCiAgICAgIC8vICAgICAgIHJvd1t0aGlzLmdldFJvd0NDb2RlKHJvdywgJ2FsQ291bnQnKV0gPSAocm93W3RoaXMuZ2V0Um93Q0NvZGUocm93LCAnYWxDb3VudCcpXSB8fCAwKSAtIHNlbGVjdE51bQogICAgICAvLyAgICAgICByb3dbJ2FsQ291bnQnICsgdGFyQ29kZV0gPSByb3dbdGFyQ29kZV0KICAgICAgLy8gICAgIH0KICAgICAgLy8gICB9KQogICAgICAvLyB9CiAgICAgIHRoaXMuZmlsdGVyVGJEYXRhID0gdGhpcy50YkRhdGEuZmlsdGVyKHJvdyA9PiB7CiAgICAgICAgcmV0dXJuIHRoaXMuZmlsdGVyWmVybyhyb3cpCiAgICAgIH0pCiAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24gPSBbXQogICAgICB0aGlzLiRyZWZzLnhUYWJsZS5jbGVhckNoZWNrYm94Um93KCkKICAgICAgaWYgKGlzTWVzc2FnZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgbWVzc2FnZTogJ+WQjOatpeaIkOWKnycsCiAgICAgICAgICB0eXBlOiAnc3VjY2VzcycKICAgICAgICB9KQogICAgICB9CiAgICB9LAogICAgLy8g5om56YeP5YiG6YWN5o+Q5LqkCiAgICBkb0FsbG9jYXRpb24oKSB7CiAgICAgIGlmICh0aGlzLmFjdGl2ZU5hbWUgPT09ICdmaXJzdCcpIHsKICAgICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uCiAgICAgICAgICAuZm9yRWFjaCgocm93LCBpZHgpID0+IHsKICAgICAgICAgICAgaWYgKHJvdy5DYW5fQWxsb2NhdGlvbl9Db3VudCkgewogICAgICAgICAgICAgIGNvbnN0IHRlYW0gPSByb3cuQWxsb2NhdGlvbl9UZWFtcy5maW5kKHYgPT4gdi5Xb3JraW5nX1RlYW1fSWQgPT09IHRoaXMuZm9ybS5UZWFtR3JvdXApCiAgICAgICAgICAgICAgY29uc3QgdGFyQ29kZSA9IHRoaXMuZ2V0Um93VW5pcXVlKHJvdy51dWlkLCB0ZWFtLlByb2Nlc3NfQ29kZSwgdGVhbS5Xb3JraW5nX1RlYW1fSWQpCiAgICAgICAgICAgICAgcm93W3RhckNvZGVdID0gTnVtYmVyKHJvd1t0YXJDb2RlXSkgKyByb3cuYWxsb2NhdGVkVGFzawogICAgICAgICAgICAgIHJvdy5DYW5fQWxsb2NhdGlvbl9Db3VudCA9IHJvdy5DYW5fQWxsb2NhdGlvbl9Db3VudCAtIHJvdy5hbGxvY2F0ZWRUYXNrCiAgICAgICAgICAgICAgcm93W3RoaXMuZ2V0VGFza0NvZGUodGVhbS5Xb3JraW5nX1RlYW1fTmFtZSldICs9IHJvdy5hbGxvY2F0ZWRUYXNrCiAgICAgICAgICAgICAgcm93W3RoaXMuZ2V0VGFza0NvZGUoKV0gLT0gcm93LmFsbG9jYXRlZFRhc2sKICAgICAgICAgICAgICByb3cuYWxsb2NhdGVkVGFza01heCA9IHJvdy5DYW5fQWxsb2NhdGlvbl9Db3VudAogICAgICAgICAgICAgIHJvdy5hbGxvY2F0ZWRUYXNrID0gcm93LkNhbl9BbGxvY2F0aW9uX0NvdW50CiAgICAgICAgICAgICAgcm93WydhbENvdW50JyArIHRhckNvZGVdID0gcm93W3RhckNvZGVdCiAgICAgICAgICAgIH0KICAgICAgICAgIH0pCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbgogICAgICAgICAgLmZvckVhY2goKHJvdywgaWR4KSA9PiB7CiAgICAgICAgICAgIGNvbnN0IHRlYW0gPSByb3cuQWxsb2NhdGlvbl9UZWFtcy5maW5kKHYgPT4gdi5Xb3JraW5nX1RlYW1fSWQgPT09IHRoaXMuZm9ybS5UZWFtR3JvdXApCiAgICAgICAgICAgIGNvbnN0IHRhckNvZGUgPSB0aGlzLmdldFJvd1VuaXF1ZShyb3cudXVpZCwgdGVhbS5Qcm9jZXNzX0NvZGUsIHRlYW0uV29ya2luZ19UZWFtX0lkKQoKICAgICAgICAgICAgY29uc3Qgc2VsZWN0TnVtID0gTWF0aC5taW4ocm93W3RoaXMuZ2V0Um93Q0NvZGUocm93LCAnYWxDb3VudCcpXSwgcm93W3RoaXMuZ2V0Um93Q0NvZGUocm93KV0pCgogICAgICAgICAgICByb3dbdGhpcy5nZXRUYXNrQ29kZSh0ZWFtLldvcmtpbmdfVGVhbV9OYW1lKV0gKz0gc2VsZWN0TnVtCiAgICAgICAgICAgIHJvd1t0aGlzLmdldFRhc2tDb2RlKCldIC09IHNlbGVjdE51bQoKICAgICAgICAgICAgcm93W3RhckNvZGVdID0gTnVtYmVyKHJvd1t0YXJDb2RlXSkgKyBzZWxlY3ROdW0KICAgICAgICAgICAgcm93W3RoaXMuZ2V0Um93Q0NvZGUocm93KV0gLT0gc2VsZWN0TnVtCiAgICAgICAgICAgIHJvd1t0aGlzLmdldFJvd0NDb2RlKHJvdywgJ2FsQ291bnQnKV0gLT0gc2VsZWN0TnVtCgogICAgICAgICAgICByb3dbJ2FsQ291bnQnICsgdGFyQ29kZV0gPSByb3dbdGFyQ29kZV0KICAgICAgICAgIH0pCiAgICAgIH0KICAgICAgdGhpcy5maWx0ZXJUYkRhdGEgPSB0aGlzLnRiRGF0YS5maWx0ZXIocm93ID0+IHsKICAgICAgICByZXR1cm4gdGhpcy5maWx0ZXJaZXJvKHJvdykKICAgICAgfSkKICAgIH0sCiAgICByZXNldEZvcm0oZm9ybU5hbWUpIHsKICAgICAgdGhpcy4kcmVmc1tmb3JtTmFtZV0ucmVzZXRGaWVsZHMoKQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZQogICAgfSwKICAgIGZpbHRlclR5cGVNZXRob2QoeyBvcHRpb24sIHJvdyB9KSB7CiAgICAgIHJldHVybiByb3cuVHlwZS5pbmNsdWRlcyhvcHRpb24uZGF0YSkKICAgIH0sCiAgICBmaWx0ZXJUeXBlUmVjb3Zlck1ldGhvZCh7IG9wdGlvbiB9KSB7CiAgICAgIG9wdGlvbi5kYXRhID0gJycKICAgIH0sCiAgICBmaWx0ZXJDb2RlTWV0aG9kKHsgb3B0aW9uLCByb3cgfSkgewogICAgICBjb25zb2xlLmxvZygnb3B0aW9uLCByb3cnLCBvcHRpb24sIHJvdykKICAgICAgcmV0dXJuIHJvd1t0aGlzLmlzQ29tID8gJ0NvbXBfQ29kZScgOiAnUGFydF9Db2RlJ10uaW5jbHVkZXMob3B0aW9uLmRhdGEpCiAgICB9LAogICAgZmlsdGVyQ29kZVJlY292ZXJNZXRob2QoeyBvcHRpb24gfSkgewogICAgICBvcHRpb24uZGF0YSA9ICcnCiAgICB9LAogICAgZ2V0T2JqZWN0VHlwZUxpc3QoKSB7CiAgICAgIEdldENvbXBUeXBlVHJlZSh7IHByb2Zlc3Npb25hbDogJ1N0ZWVsJyB9KS50aGVuKChyZXMpID0+IHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgdGhpcy5PYmplY3RUeXBlTGlzdC5kYXRhID0gcmVzLkRhdGEKICAgICAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7CiAgICAgICAgICAgIHRoaXMuJHJlZnM/LnRyZWVTZWxlY3RPYmplY3RUeXBlPy50cmVlRGF0YVVwZGF0ZUZ1bihyZXMuRGF0YSkKICAgICAgICAgIH0pCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZQogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAg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file": "detail.vue", "sourceRoot": "src/views/PRO/plan-production/task-allocation/v4", "sourcesContent": ["<template>\n  <div v-loading=\"loading\" class=\"app-container abs100\">\n    <el-card class=\"box-card h100\">\n      <h4 class=\"topTitle\"><span />基本信息</h4>\n      <el-form\n        ref=\"formInline\"\n        label-position=\"right\"\n        label-width=\"90px\"\n        :inline=\"true\"\n        :model=\"formInline\"\n        class=\"demo-form-inline\"\n      >\n        <el-row>\n          <el-col :span=\"20\">\n            <el-row>\n              <el-col :span=\"6\">\n                <el-form-item label=\"排产单号:\" label-width=\"75px\" prop=\"Schduling_Code\">\n                  <span>{{ formInline.Schduling_Code }}</span>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"6\">\n                <el-form-item label=\"项目名称:\" prop=\"Project_Name\">\n                  <span>{{ formInline.Project_Name }}</span>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"6\">\n                <el-form-item label=\"区域:\" prop=\"Area_Name\">\n                  <span>{{ formInline.Area_Name }}</span>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"6\">\n                <el-form-item label=\"批次:\" prop=\"Installunit_Name\">\n                  <span>{{ formInline.Installunit_Name }}</span>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"6\">\n                <el-form-item label=\"任务数量:\" label-width=\"75px\" prop=\"Allocation_Count\">\n                  <span>{{ formInline.Allocation_Count }}</span>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"6\">\n                <el-form-item label=\"任务重量:\" prop=\"Allocation_Weight\">\n                  <span>{{ formInline.Allocation_Weight | filterNum }}(t)</span>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"6\">\n                <el-form-item label=\"已完成数量:\" prop=\"Finish_Count\">\n                  <span>{{ formInline.Finish_Count }}</span>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"6\">\n                <el-form-item label=\"已完成重量:\" prop=\"Finish_Weight\">\n                  <span>{{ formInline.Finish_Weight | filterNum }}(t)</span>\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </el-col>\n          <el-col :span=\"4\">\n            <qrcode-vue\n              :size=\"79\"\n              :value=\"formInline.Schduling_Code\"\n              class-name=\"qrcode\"\n              level=\"H\"\n            />\n          </el-col>\n        </el-row>\n      </el-form>\n      <el-divider class=\"elDivder\" />\n      <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\n        <el-tab-pane label=\"待分配\" name=\"first\" />\n        <el-tab-pane\n          v-for=\"(element, index2) in workingTeam\"\n          :key=\"index2\"\n          :label=\"element.Working_Team_Name\"\n          :name=\"`${element.Working_Team_Name}$_$${element.Process_Code}`\"\n        />\n      </el-tabs>\n      <div class=\"tb-options\" :style=\"{'justify-content': !isView ? 'space-between' : 'end'}\">\n        <div>\n          <el-button v-if=\"!isView\" plain type=\"primary\" @click=\"reverseSelection()\">反选</el-button>\n          <el-button v-if=\"!isView\" type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"Batchallocation()\">批量分配</el-button>\n          <el-button v-if=\"!isView && activeName==='first'\" type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"preStepTaskAllocation()\">上道工序同步</el-button>\n        </div>\n        <el-form :inline=\"true\" :model=\"queryForm\" class=\"demo-form-inline\">\n          <el-form-item label=\"规格\">\n            <el-input v-model.trim=\"queryForm.Spec\" clearable placeholder=\"请输入\" />\n          </el-form-item>\n          <el-form-item v-if=\"isCom\" :label=\"`${bomName}类型`\">\n            <el-tree-select\n              ref=\"treeSelectObjectType\"\n              v-model=\"searchType\"\n              style=\"width: 100%\"\n              class=\"cs-tree-x\"\n              :select-params=\"treeSelectParams\"\n              :tree-params=\"ObjectTypeList\"\n              value-key=\"Id\"\n            />\n          </el-form-item>\n          <el-form-item :label=\"`${bomName}名称`\">\n            <el-input v-if=\"isCom\" v-model=\"queryForm.Comp_Codes\" clearable placeholder=\"请输入(空格区分/多个搜索)\" />\n            <el-input v-else v-model=\"queryForm.Part_Code\" clearable placeholder=\"请输入(空格区分/多个搜索)\" />\n            <el-input v-if=\"isCom\" v-model.trim=\"queryForm.Comp_Codes_Vague\" style=\"margin-left: 10px;\" clearable placeholder=\"模糊查找(请输入关键字)\" />\n            <el-input v-else v-model.trim=\"queryForm.Part_Code_Vague\" style=\"margin-left: 10px;\" clearable placeholder=\"模糊查找(请输入关键字)\" />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"filterData\">查询</el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n      <div class=\"tb-x\">\n        <vxe-table\n          ref=\"xTable\"\n          :empty-render=\"{name: 'NotData'}\"\n          show-header-overflow\n          class=\"cs-vxe-table\"\n          :row-config=\"{ isCurrent: true, isHover: true }\"\n          align=\"left\"\n          height=\"100%\"\n          show-overflow\n          :loading=\"tbLoading\"\n          :checkbox-config=\"{checkField: 'checked',checkMethod }\"\n          stripe\n          size=\"medium\"\n          :edit-config=\"{\n            trigger: 'click',\n            mode: 'cell',\n            showIcon: !isView,\n            beforeEditMethod: activeCellMethod,\n          }\"\n          :data=\"filterTbData\"\n          resizable\n          :tooltip-config=\"{ enterable: true }\"\n          @checkbox-all=\"tbSelectChange\"\n          @checkbox-change=\"tbSelectChange\"\n        >\n          <vxe-column type=\"checkbox\" width=\"60\" fixed=\"left\" />\n          <template v-for=\"item in columns\">\n\n            <!--            <vxe-column :align=\"item.Align\"\n              v-if=\"item.Code==='Comp_Code'||item.Code==='Part_Code'\"\n              :key=\"item.Id\"\n              :filters=\"[{ data: '' }]\"\n              :filter-method=\"filterCodeMethod\"\n              :filter-recover-method=\"filterCodeRecoverMethod\"\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n              show-overflow=\"tooltip\"\n              sortable\n              :field=\"item.Code\"\n              :title=\"item.Display_Name\"\n              :min-width=\"item.Width\"\n            >\n              <template #filter=\"{ $panel, column }\">\n                <template v-for=\"(option, index) in column.filters\">\n                  <input :key=\"index\" v-model=\"option.data\" class=\"my-input\" type=\"type\" placeholder=\"按回车确认筛选\" @input=\"$panel.changeOption($event, !!option.data, option)\" @keyup.enter=\"$panel.confirmFilter()\">\n                </template>\n              </template>\n            </vxe-column>\n            <vxe-column :align=\"item.Align\"\n              v-else-if=\"item.Code==='Type'\"\n              :key=\"item.Id\"\n              :filters=\"[{ data: '' }]\"\n              :filter-method=\"filterTypeMethod\"\n              :filter-recover-method=\"filterTypeRecoverMethod\"\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n              show-overflow=\"tooltip\"\n              sortable\n              :field=\"item.Code\"\n              :title=\"item.Display_Name\"\n              :min-width=\"item.Width\"\n            >\n              <template #filter=\"{ $panel, column }\">\n                <template v-for=\"(option, index) in column.filters\">\n                  <input :key=\"index\" v-model=\"option.data\" class=\"my-input\" type=\"type\" placeholder=\"按回车确认筛选\" @input=\"$panel.changeOption($event, !!option.data, option)\" @keyup.enter=\"$panel.confirmFilter()\">\n                </template>\n              </template>\n            </vxe-column>-->\n            <vxe-column\n              v-if=\"item.Code==='Comp_Code'|| item.Code==='Part_Code'\"\n              :key=\"item.Id\"\n              :align=\"item.Align\"\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n              show-overflow=\"tooltip\"\n              sortable\n              :visible=\"item.visible\"\n              :field=\"item.Code\"\n              :title=\"item.Display_Name\"\n              :min-width=\"item.Width\"\n            >\n              <template #default=\"{ row }\">\n                <el-tag v-if=\"!!row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\n                <span>{{ row[item.Code] }}</span>\n              </template>\n            </vxe-column>\n            <vxe-column\n              v-else-if=\"item.Code==='Schduled_Count'\"\n              :key=\"`SchduledCount${item.Id}`\"\n              :align=\"item.Align\"\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n              show-overflow=\"tooltip\"\n              sortable\n              :visible=\"item.visible\"\n              :field=\"item.Code\"\n              :title=\"item.Display_Name\"\n              :min-width=\"item.Width\"\n            >\n              <template #default=\"{ row }\">\n                {{ activeName === 'first' ?'':row[getTaskCode()] }}\n              </template>\n            </vxe-column>\n            <vxe-column\n              v-else\n              :key=\"`Default${item.Id}`\"\n              :align=\"item.Align\"\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n              :visible=\"item.visible\"\n              show-overflow=\"tooltip\"\n              sortable\n              :field=\"item.Code\"\n              :title=\"item.Display_Name\"\n              :min-width=\"item.Width\"\n            />\n          </template>\n\n          <vxe-column\n            v-for=\"(element, index2) in workingTeamColumn\"\n            :key=\"index2\"\n            :align=\"element.Align\"\n            :visible=\"element.visible\"\n            fixed=\"right\"\n            :field=\"`${element.Working_Team_Name}$_$${element.Process_Code}`\"\n            title=\"可分配数量\"\n            sortable\n            min-width=\"170\"\n          >\n            <template #edit=\"{ row }\">\n              <vxe-input\n                v-model.number=\"\n                  row[\n                    getRowUnique(\n                      row.uuid,\n                      element.Process_Code,\n                      element.Working_Team_Id\n                    )\n                  ]\n                \"\n                type=\"integer\"\n                :min=\"0\"\n                :max=\"\n                  row[\n                    getRowUniqueMax(\n                      row.uuid,\n                      element.Process_Code,\n                      element.Working_Team_Id\n                    )\n                  ]\n                \"\n                @change=\"\n                  inputChange(\n                    row,\n                    element.Process_Code,\n                    element.Working_Team_Id\n                  )\n                \"\n              />\n            </template>\n            <template #default=\"{ row }\">\n              <template\n                v-if=\"\n                  checkPermissionTeam(row.Technology_Path, element.Process_Code)\n                \"\n              >\n                {{\n                  row[\n                    getRowUnique(\n                      row.uuid,\n                      element.Process_Code,\n                      element.Working_Team_Id\n                    )\n                  ]\n                }}\n              </template>\n              <template v-else> -</template>\n            </template>\n          </vxe-column>\n\n          <vxe-column\n            :key=\"activeName\"\n            align=\"left\"\n            :edit-render=\"{}\"\n            field=\"AllocatedCount\"\n            title=\"分配数量\"\n            sortable\n            fixed=\"right\"\n            min-width=\"180\"\n          >\n            <template #edit=\"{ row }\">\n              <vxe-input\n                :key=\"activeName\"\n                v-model.number=\"row[getRowCCode(row,'alCount')]\"\n                type=\"integer\"\n                :min=\"0\"\n                :max=\"row[getRowCCode(row,'','Max')]\"\n              />\n            </template>\n            <template #default=\"{ row }\">\n              {{ row[getRowCCode(row,'alCount')] | displayValue }}\n            </template>\n          </vxe-column>\n        </vxe-table>\n      </div>\n      <footer>\n        <div class=\"data-info\">\n          <el-tag size=\"medium\" class=\"info-x\">已选{{ multipleSelection.length }}条数据</el-tag>\n          <el-tag v-if=\"tipLabel\" size=\"medium\" class=\"info-x\">{{ tipLabel }}</el-tag>\n        </div>\n        <div>\n          <el-button @click=\"handleClose\">取消 </el-button>\n          <el-button\n            v-if=\"!isView\"\n            type=\"primary\"\n            :loading=\"loading\"\n            @click=\"handleSubmit\"\n          >提交</el-button>\n        </div>\n      </footer>\n    </el-card>\n    <el-dialog\n      v-dialogDrag\n      title=\"批量分配\"\n      class=\"plm-custom-dialog\"\n      :visible.sync=\"dialogVisible\"\n      width=\"30%\"\n      @close=\"handleDialog\"\n    >\n      <el-form\n        ref=\"form\"\n        :model=\"form\"\n        :rules=\"rules\"\n        label-width=\"80px\"\n        class=\"demo-ruleForm\"\n      >\n        <el-form-item label=\"选择班组\" prop=\"TeamGroup\">\n          <el-select\n            v-model=\"form.TeamGroup\"\n            class=\"w100\"\n            placeholder=\"请选择\"\n            filterable\n            clearable\n          >\n            <el-option\n              v-for=\"item in workingTeam\"\n              :key=\"item.Working_Team_Id\"\n              :label=\"item.Working_Team_Name\"\n              :value=\"item.Working_Team_Id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item style=\"text-align: right\">\n          <el-button @click=\"resetForm('form')\">取 消</el-button>\n          <el-button\n            type=\"primary\"\n            @click=\"submitForm('form');resetForm('form')\"\n          >确 定</el-button>\n        </el-form-item>\n      </el-form>\n    </el-dialog>\n    <el-dialog\n      v-dialogDrag\n      title=\"提示\"\n      class=\"plm-custom-dialog\"\n      :visible.sync=\"dialogTipsVisible\"\n      width=\"450px\"\n      @close=\"handleDialog\"\n    >\n      <div style=\"text-align: center; font-size: 16px;\">部分{{ bomName }}与上道工序加工班组不同，请手动分配</div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogTipsVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"dialogTipsVisible = false\">确 定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { GetGridByCode } from '@/api/sys'\nimport { FIX_COLUMN } from '@/views/PRO/plan-production/schedule-production/constant'\nimport {\n  AdjustTeamProcessAllocation,\n  GetTeamProcessAllocation,\n  AdjustPartTeamProcessAllocation,\n  AdjustSubAssemblyTeamProcessAllocation,\n  GetStopList,\n  GetPreStepTaskAllocation\n} from '@/api/PRO/production-task'\nimport QrcodeVue from 'qrcode.vue'\nimport { v4 as uuidv4 } from 'uuid'\nimport numeral from 'numeral'\nimport { closeTagView, deepClone } from '@/utils'\nimport { GetCompTypeTree } from '@/api/PRO/professionalType'\nimport { GetBOMInfo, getBomCode, getBomName, checkIsUnitPart } from '@/views/PRO/bom-setting/utils'\n\nconst SPLIT_SYMBOL = '$_$'\nexport default {\n  components: {\n    QrcodeVue\n  },\n  filters: {\n    filterNum(value) {\n      return numeral(value).divide(1000).format('0.[00]')\n    }\n  },\n  data() {\n    return {\n      treeSelectParams: {\n        placeholder: '请选择',\n        clearable: true\n      },\n      ObjectTypeList: {\n        // 构件类型\n        'check-strictly': true,\n        'default-expand-all': true,\n        clickParent: true,\n        data: [],\n        props: {\n          children: 'Children',\n          label: 'Label',\n          value: 'Data'\n        }\n      },\n      tbLoading: false,\n      loading: false,\n      activeName: 'first',\n      tipLabel: '',\n      tbData: [],\n      filterTbData: [],\n      multipleSelection: [],\n      columns: [],\n      workingTeam: [],\n      workingTeamColumn: [],\n      formInline: {},\n      pg_type: '',\n      searchType: '',\n      type: '',\n      queryForm: {\n        Comp_Codes: '',\n        Part_Code: '',\n        Spec: '',\n        Comp_Codes_Vague: '',\n        Part_Code_Vague: ''\n      },\n      dialogVisible: false,\n      dialogTipsVisible: false,\n      form: {\n        TeamGroup: '' // 班组\n      },\n\n      rules: {\n        TeamGroup: [\n          { required: true, message: '请输入班组名称', trigger: 'change' }\n        ]\n      },\n      Is_Workshop_Enabled: false,\n      Working_Process_Id: '',\n      bomList: [],\n      bomLevel: '',\n      bomName: ''\n    }\n  },\n  computed: {\n    isView() {\n      return this.type === 'view'\n    },\n    isCom() {\n      return this.bomLevel === getBomCode('-1')\n    },\n    isUnitPart() {\n      return checkIsUnitPart(this.bomLevel)\n    }\n  },\n\n  async mounted() {\n    try {\n      const { list } = await GetBOMInfo()\n      this.bomList = list || []\n      const rowInfo = JSON.parse(decodeURIComponent(this.$route.query.other))\n      console.log('rowInfo', rowInfo)\n      this.formInline = Object.assign({}, this.formInline, rowInfo)\n      this.pg_type = this.$route.query.pg_type\n      this.bomLevel = this.$route.query.bomLevel\n      this.bomName = await getBomName(this.bomLevel)\n      this.type = this.$route.query.type\n      this.Is_Workshop_Enabled = this.$route.query.Is_Workshop_Enabled\n      await this.getTableConfig(this.isUnitPart ? 'PROTaskUnitAllocationChange' : 'PROTaskAllocationChange')\n      if (this.isCom) {\n        const idx = this.columns.findIndex(item => item.Code === 'Part_Code')\n        idx !== -1 && this.columns.splice(idx, 1)\n        const idx2 = this.columns.findIndex(item => item.Code === 'Component_Code')\n        idx2 !== -1 && this.columns.splice(idx2, 1)\n      } else {\n        const idx = this.columns.findIndex(item => item.Code === 'Comp_Code')\n        idx !== -1 && this.columns.splice(idx, 1)\n        const idx2 = this.columns.findIndex(item => item.Code === 'Type')\n        idx2 !== -1 && this.columns.splice(idx2, 1)\n      }\n      if (!this.Is_Workshop_Enabled) {\n        const idx3 = this.columns.findIndex(item => item.Code === 'Workshop_Name')\n        idx3 !== -1 && this.columns.splice(idx3, 1)\n      }\n      this.getObjectTypeList()\n      this.fetchData()\n    } catch (e) {\n      this.$message({\n        message: '参数错误,请重新操作',\n        type: 'error'\n      })\n    }\n  },\n  methods: {\n    getRowCCode(row, prefix = '', suffix = '') {\n      if (this.activeName === 'first') {\n        return 'allocatedTask' + suffix\n      } else {\n        const arr = this.activeName.split(SPLIT_SYMBOL)\n        const team = this.workingTeam.find(v => v.Working_Team_Name === arr[0])\n        const u = this.getRowUnique(row.uuid, row.Process_Code, team.Working_Team_Id)\n        if (suffix === 'Max') {\n          return u\n        } else {\n          return prefix + u + suffix\n        }\n      }\n    },\n    handleClick(val) {\n      console.log('handleClick', val)\n      if (val.name === 'first') {\n        const c1 = this.$refs.xTable.getColumnByField('Schduled_Count')\n        const c2 = this.$refs.xTable.getColumnByField('Can_Allocation_Count')\n        c1.visible = false\n        c2.visible = true\n      } else {\n        const c2 = this.$refs.xTable.getColumnByField('Schduled_Count')\n        const c3 = this.$refs.xTable.getColumnByField('Can_Allocation_Count')\n        c2.visible = true\n        c3.visible = false\n      }\n\n      this.workingTeam.forEach((element, idx) => {\n        const codes = `${element.Working_Team_Name}$_$${element.Process_Code}`\n\n        const c = this.$refs.xTable.getColumnByField(codes)\n\n        c.visible = codes === val.name\n      })\n      this.$refs.xTable.refreshColumn()\n\n      this.filterTbData = this.tbData.filter(row => {\n        return this.filterZero(row)\n      })\n\n      this.multipleSelection = []\n      this.$refs.xTable.clearCheckboxRow()\n    },\n    filterZero(row) {\n      if (this.activeName === 'first') {\n        return row.allocatedTask > 0\n      } else {\n        const arr = this.activeName.split(SPLIT_SYMBOL)\n        const team = this.workingTeam.find(v => v.Working_Team_Name === arr[0])\n        const code = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\n        return row[code] > 0\n      }\n    },\n    fetchData() {\n      const Comp_Codes = !this.queryForm.Comp_Codes ? [] : this.queryForm.Comp_Codes.trim().split(' ')\n      const Part_Code = !this.queryForm.Part_Code ? [] : this.queryForm.Part_Code.trim().split(' ')\n      let Process_Type = 2\n      Process_Type = this.isCom ? 2 : this.isUnitPart ? 3 : 1\n      this.tbLoading = true\n      GetTeamProcessAllocation({\n        Page: 1,\n        PageSize: -1,\n        Step: this.formInline.Step,\n        Process_Type,\n        Bom_Level: this.bomLevel,\n        Schduling_Code: this.formInline.Schduling_Code,\n        Process_Code: this.formInline.Process_Code,\n        Workshop_Name: this.formInline.Workshop_Name,\n        Area_Id: this.formInline.Area_Id,\n        InstallUnit_Id: this.formInline.InstallUnit_Id,\n        Comp_Codes,\n        Part_Code\n      }).then(async(res) => {\n        if (res.IsSucceed) {\n          const { Schduling_Plan, Schduling_Comps } = res.Data\n          this.planInfoTemp = Schduling_Plan\n          await this.getStopList(Schduling_Comps)\n          this.initTbData(Schduling_Comps)\n          this.Working_Process_Id = res.Data.Working_Process_Id\n\n          if (Schduling_Comps.length) {\n            this.workingTeam = Schduling_Comps[0].Allocation_Teams\n            const _kk = this.workingTeam.map(v => {\n              v.visible = false\n              return v\n            })\n            this.workingTeamColumn = deepClone(_kk)\n          }\n          console.log(' this.tbData', this.tbData)\n          this.filterData()\n          console.log('filterTbData', this.filterTbData)\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      }).finally(_ => {\n        this.tbLoading = false\n      })\n    },\n    checkMethod({ row }) {\n      return !row.stopFlag\n    },\n    async getStopList(list) {\n      const key = 'Id'\n      const submitObj = list.map(item => {\n        return {\n          Id: item[key],\n          Bom_Level: this.bomLevel,\n          Type: this.isCom ? 2 : this.isUnitPart ? 3 : 1 // 1：零件，3：部件，2：构件\n        }\n      })\n      await GetStopList(submitObj).then(res => {\n        if (res.IsSucceed) {\n          const stopMap = {}\n          res.Data.forEach(item => {\n            stopMap[item.Id] = !!item.Is_Stop\n          })\n          list.forEach(row => {\n            if (stopMap[row[key]]) {\n              this.$set(row, 'stopFlag', stopMap[row[key]])\n            }\n          })\n        }\n      })\n    },\n    filterData() {\n      console.log('searchType', this.searchType)\n      this.multipleSelection = []\n      this.$refs.xTable.clearCheckboxRow()\n      const code = this.isCom ? 'Comp_Code' : 'Part_Code'\n      const queryCode = this.isCom ? 'Comp_Codes' : 'Part_Code'\n      const codeList = this.queryForm[queryCode].split(' ').filter(v => !!v)\n\n      const queryCodeVague = this.isCom ? 'Comp_Codes_Vague' : 'Part_Code_Vague'\n      const codeListVague = this.queryForm[queryCodeVague]\n\n      const searchTbData = this.tbData.filter(v => {\n        if (!codeList.length && codeListVague === '') {\n          return true\n        } else {\n          return codeList.includes(v[code])\n        }\n      })\n\n      const vagueData = searchTbData.length > 0 && codeListVague === '' ? searchTbData : searchTbData.length === 0 && codeListVague === '' ? searchTbData : this.tbData\n      const searchVagueTbData = vagueData.filter(v => {\n        if (codeListVague === '' && !codeList.length) {\n          return true\n        } else {\n          return v[code].includes(codeListVague)\n        }\n      })\n\n      // 合并两个数组\n      const mergedArray = searchTbData.concat(searchVagueTbData)\n      // 根据 Schduling_Detail_Id 进行去重\n      const uniqueArray = mergedArray.reduce((acc, current) => {\n        const existingObject = acc.find(item => item.Schduling_Detail_Id === current.Schduling_Detail_Id)\n        if (!existingObject) {\n          acc.push(current)\n        }\n        return acc\n      }, [])\n\n      this.filterTbData = uniqueArray.filter(v => {\n        if (!this.searchType) return true\n        return this.searchType === v.Type\n      }).filter(v => {\n        if (!this.queryForm.Spec) return true\n        return (v.Spec || '').includes(this.queryForm.Spec)\n      }).filter(row => {\n        return this.filterZero(row)\n      })\n    },\n    initTbData(list, teamKey = 'Allocation_Teams') {\n      this.tbData = list.map(row => {\n        const processList = row.Technology_Path?.split('/') || []\n        // 已uuid作为row唯一值；\n        // uuid+工序+班组为输入框值\n        row.uuid = uuidv4()\n        const newData = row[teamKey].filter((r) => processList.findIndex((p) => r.Process_Code === p) !== -1)\n        row.defaultCan_Allocation_Count = row.Can_Allocation_Count\n        let _inputNum = 0\n        newData.forEach((ele, index) => {\n          const code = this.getRowUnique(row.uuid, ele.Process_Code, ele.Working_Team_Id)\n          const max = this.getRowUniqueMax(row.uuid, ele.Process_Code, ele.Working_Team_Id)\n          row[code] = ele.Count\n          this.$set(row, 'alCount' + code, ele.Count)\n          row[max] = 0\n          _inputNum += ele.Count\n          this.$set(row, 'totalTask' + ele.Working_Team_Name, ele.Total_Receive_Count + ele.Count)\n        })\n\n        this.$set(row, 'allocatedTask', row.defaultCan_Allocation_Count - _inputNum)\n        row.Can_Allocation_Count = row.allocatedTask\n        this.$set(row, 'allocatedTaskMax', row.Can_Allocation_Count)\n\n        this.setInputMax(row)\n\n        row.checked = false\n        return row\n      })\n    },\n    inputChange(row) {\n      this.setInputMax(row)\n    },\n    setInputMax(row) {\n      let _inputNum = 0\n      const inputValuesKeys = Object.keys(row)\n        .filter(v => !v.endsWith('max') && v.startsWith(row.uuid) && v.length > row.uuid.length)\n      inputValuesKeys.forEach((val) => {\n        const curCode = val.split(SPLIT_SYMBOL)[1]\n        const otherTotal = inputValuesKeys.filter(x => {\n          const code = x.split(SPLIT_SYMBOL)[1]\n          return x !== val && code === curCode\n        }).reduce((acc, item) => {\n          return acc + numeral(row[item]).value()\n        }, 0)\n        row[val + SPLIT_SYMBOL + 'max'] = row.Schduled_Count - otherTotal\n        _inputNum += +row[val]\n      })\n      // row.allocatedCount = row.defaultCan_Allocation_Count - _inputNum\n      // row.Can_Allocation_Count = row.allocatedCount\n    },\n    checkPermissionTeam(processStr, processCode) {\n      if (!processStr) return false\n      const list = processStr?.split('/') || []\n      return !!list.some(v => v === processCode)\n    },\n\n    getSubmitTbInfo(tbData = this.tbData) {\n      // 处理上传的数据\n      const tableData = JSON.parse(JSON.stringify(tbData))\n      for (let i = 0; i < tableData.length; i++) {\n        const element = tableData[i]\n        const list = []\n        if (!element.Technology_Path) {\n          this.$message({\n            message: '工序不能为空',\n            type: 'warning'\n          })\n          return { status: false }\n        }\n        const processList = Array.from(new Set(element.Technology_Path.split('/')))\n        processList.forEach(code => {\n          const groups = this.workingTeam.filter(v => v.Process_Code === code)\n\n          const groupsList = groups.map((group, index) => {\n            const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)\n            const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)\n            const obj = {\n              Comp_Code: element.Comp_Code,\n              Again_Count: +element[uCode],\n              Part_Code: this.isCom ? null : element.Part_Code,\n              Process_Code: code,\n              Technology_Path: element.Technology_Path,\n              Working_Team_Id: group.Working_Team_Id,\n              Working_Team_Name: group.Working_Team_Name,\n              Team_Task_Id: element.Allocation_Teams.find((item) => item.Working_Team_Id === group.Working_Team_Id).Team_Task_Id\n            }\n            delete element['alCount' + uCode]\n            delete element['allocatedTask']\n            delete element['allocatedTaskMax']\n            delete element[uCode]\n            delete element[uMax]\n            return obj\n          })\n          list.push(...groupsList)\n        })\n        console.log(list)\n        delete element['uuid']\n        delete element['puuid']\n        element.Allocation_Teams = list\n      }\n      return { tableData, status: true }\n    },\n    handleSubmit() {\n      const { tableData, status } = this.getSubmitTbInfo()\n      if (!status) return\n      this.$confirm('是否提交当前数据?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.loading = true\n        const obj = {\n          Schduling_Plan: this.planInfoTemp,\n          Schduling_Comps: tableData\n        }\n        const Partobj = {\n          Schduling_Plan: this.planInfoTemp,\n          SarePartsModel: tableData\n        }\n        const requestFn = this.isCom ? AdjustTeamProcessAllocation : this.isUnitPart ? AdjustSubAssemblyTeamProcessAllocation : AdjustPartTeamProcessAllocation\n        console.log('obj', obj)\n        requestFn(this.isCom ? obj : Partobj).then(res => {\n          if (res.IsSucceed) {\n            this.$message({\n              message: '操作成功',\n              type: 'success'\n            })\n            this.handleClose()\n          } else {\n            this.$message({\n              message: res.Message,\n              type: 'error'\n            })\n          }\n          this.loading = false\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消'\n        })\n      })\n    },\n    handleClose() {\n      this.closeView()\n    },\n    closeView() {\n      closeTagView(this.$store, this.$route)\n    },\n    tbSelectChange(array) {\n      this.multipleSelection = array.records\n      console.log(this.tbData)\n    },\n    getTaskCode(name = '') {\n      if (name) return 'totalTask' + name\n      return 'totalTask' + this.activeName.split(SPLIT_SYMBOL)[0]\n    },\n    // 反选\n    reverseSelection() {\n      const list = this.$refs.xTable.getCheckboxRecords().filter(item => !item.stopFlag)\n      const unSelectList = this.filterTbData.filter(item => !list.includes(item) && !item.stopFlag)\n      this.$refs.xTable.setCheckboxRow(list, false)\n      this.$refs.xTable.setCheckboxRow(unSelectList, true)\n      this.multipleSelection = this.$refs.xTable.getCheckboxRecords()\n    },\n    async getTableConfig(code) {\n      await GetGridByCode({\n        code\n      }).then((res) => {\n        const { IsSucceed, Data, Message } = res\n        if (IsSucceed) {\n          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\n          const list = Data.ColumnList || []\n          this.columns = list.filter(v => v.Is_Display).map(item => {\n            if (FIX_COLUMN.includes(item.Code)) {\n              item.fixed = 'left'\n            }\n            if (item.Code === 'Schduled_Count') {\n              item.visible = false\n            }\n            return item\n          })\n        } else {\n          this.$message({\n            message: Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    activeCellMethod({ row, column, columnIndex }) {\n      if (this.isView) return false\n      if (column.field === 'AllocatedCount') return true\n      const processCode = column.field?.split('$_$')[1]\n      return this.checkPermissionTeam(row.Technology_Path, processCode)\n    },\n    getRowUnique(uuid, processCode, workingId) {\n      return `${uuid}${SPLIT_SYMBOL}${processCode}${SPLIT_SYMBOL}${workingId}`\n    },\n    getRowUniqueMax(uuid, processCode, workingId) {\n      return this.getRowUnique(uuid, processCode, workingId) + `${SPLIT_SYMBOL}max`\n    },\n    // 批量分配\n    Batchallocation() {\n      this.dialogVisible = true\n      console.log(this.workingTeam)\n    },\n    // 上道工序分配\n    preStepTaskAllocation() {\n      const Schduling_Detail_Ids = this.multipleSelection.map(item => item.Schduling_Detail_Id)\n      const Working_Process_Code = this.multipleSelection[0].Process_Code\n      GetPreStepTaskAllocation({\n        Schduling_Detail_Ids,\n        Working_Process_Code,\n        Working_Process_Id: this.Working_Process_Id,\n        Process_Type: this.isCom ? 2 : this.isUnitPart ? 3 : 1,\n        Bom_Level: this.bomLevel\n      }).then(res => {\n        if (res.IsSucceed) {\n          if (res.Data.length === 0) {\n            this.dialogTipsVisible = true\n            return\n          }\n          this.preDoAllocation(res.Data)\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    handleDialog() {\n      this.dialogVisible = false\n      // this.multipleSelection = []\n    },\n    handelData() {\n      this.multipleSelection.forEach((item) =>\n        item.Allocation_Teams.forEach((v) => {\n          if (v.Working_Team_Id === this.form.TeamGroup) {\n            v.Count = item.Can_Allocation_Count\n          } else {\n            v.Count = 0\n          }\n        })\n      )\n      // const tableData = JSON.parse(JSON.stringify(this.multipleSelection))\n      for (let i = 0; i < this.multipleSelection.length; i++) {\n        const element = this.multipleSelection[i]\n        const processList = Array.from(new Set(element.Technology_Path.split('/')))\n        processList.forEach(code => {\n          const groups = this.workingTeam.filter(v => v.Process_Code === code)\n          groups.forEach(group => {\n            const uniCode = this.getRowUnique(element.uuid, code, this.form.TeamGroup)\n            const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)\n            const uniMax = this.getRowUniqueMax(element.uuid, code, this.form.TeamGroup)\n            const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)\n\n            if (uniCode === uCode && uniMax === uMax) {\n              element[uCode] = element['Can_Allocation_Count']\n              element[uMax] = element['Can_Allocation_Count']\n            } else {\n              element[uCode] = 0\n              element[uMax] = 0\n            }\n          })\n        })\n      }\n      console.log(this.multipleSelection)\n      for (let i = 0; i < this.tbData.length; i++) {\n        for (let k = 0; k < this.multipleSelection.length; k++) {\n          if (this.tbData[i].uuid === this.multipleSelection[k].uuid) {\n            this.$nextTick((_) => {\n              this.tbData[i] = this.multipleSelection[k]\n            })\n          }\n        }\n      }\n    },\n    submitForm(formName) {\n      this.$refs[formName].validate((valid) => {\n        if (valid) {\n          // this.handelData()\n          this.doAllocation()\n          // this.workingTeam.find(v=> v.Working_Team_Id === this.form.TeamGroup).count\n          this.handleDialog()\n          console.log(this.tbData)\n          this.multipleSelection = []\n          this.$refs.xTable.clearCheckboxRow()\n        } else {\n          return false\n        }\n      })\n    },\n    // 上道工序分配\n    preDoAllocation(preProcessData) {\n      const preProcessDataMap = new Map()\n      preProcessData.forEach(item => {\n        const key = `${item.Schduling_Detail_Id}_${item.Working_Team_Id}`\n        preProcessDataMap.set(key, item.Current_Task_Count)\n      })\n\n      const allocateForTeam = (row, team, amount) => {\n        const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\n        const bKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\n        const currentTaskCount = preProcessDataMap.get(bKey) || 0\n        const allocated = Math.min(amount, currentTaskCount, row.Can_Allocation_Count)\n\n        row[tarCode] = (Number(row[tarCode]) || 0) + allocated\n        row.Can_Allocation_Count -= allocated\n        row[this.getTaskCode(team.Working_Team_Name)] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + allocated\n        row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - allocated\n        row['alCount' + tarCode] = row[tarCode]\n\n        return allocated\n      }\n      let isMessage = true\n      this.multipleSelection.forEach(row => {\n        if (!row.Can_Allocation_Count && this.activeName === 'first') return\n\n        const eligibleTeams = row.Allocation_Teams.filter(team =>\n          preProcessDataMap.has(`${row.Schduling_Detail_Id}_${team.Working_Team_Id}`)\n        )\n\n        if (eligibleTeams.length === 0) return\n        if (this.activeName === 'first') {\n          let IsAllNo = 0\n          const totalAvailable = eligibleTeams.reduce((sum, team) => {\n            const bKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\n            return sum + (preProcessDataMap.get(bKey) || 0)\n          }, 0)\n\n          if (row.allocatedTaskMax < totalAvailable) {\n            IsAllNo++\n            if (IsAllNo === this.multipleSelection.length) {\n              isMessage = false\n              this.dialogTipsVisible = true\n            }\n            return\n          }\n\n          let remaining = Math.min(row.allocatedTask, row.Can_Allocation_Count)\n          const perTeamAllocation = Math.floor(remaining / eligibleTeams.length)\n\n          eligibleTeams.forEach((team, index) => {\n            if (remaining <= 0) return\n\n            const allocateAmount = index === eligibleTeams.length - 1\n              ? remaining\n              : Math.min(perTeamAllocation, remaining)\n\n            remaining -= allocateForTeam(row, team, allocateAmount)\n          })\n\n          row.allocatedTaskMax = row.Can_Allocation_Count\n          row.allocatedTask = row.Can_Allocation_Count\n          if (IsAllNo === this.multipleSelection.length) {\n            isMessage = false\n            this.dialogTipsVisible = true\n            return\n          }\n        } else {\n          eligibleTeams.forEach(team => {\n            const bKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\n            const currentTaskCount = preProcessDataMap.get(bKey) || 0\n\n            if (row[this.getRowCCode(row)] < currentTaskCount) {\n              return\n            }\n\n            const selectNum = Math.min(\n              row[this.getRowCCode(row, 'alCount')] || 0,\n              row[this.getRowCCode(row)] || 0,\n              currentTaskCount\n            )\n\n            if (selectNum > 0) {\n              const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\n              row[this.getTaskCode(team.Working_Team_Name)] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + selectNum\n              row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - selectNum\n              row[tarCode] = (Number(row[tarCode]) || 0) + selectNum\n              row[this.getRowCCode(row)] = (row[this.getRowCCode(row)] || 0) - selectNum\n              row[this.getRowCCode(row, 'alCount')] = (row[this.getRowCCode(row, 'alCount')] || 0) - selectNum\n              row['alCount' + tarCode] = row[tarCode]\n            }\n          })\n        }\n      })\n      // if (this.activeName === 'first') {\n      //   let IsAllNo = 0\n      //   this.multipleSelection.forEach((row, idx) => {\n      //     if (row.Can_Allocation_Count) {\n      //       const validTeams = row.Allocation_Teams.filter(team => {\n      //         const key = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\n      //         return preProcessDataMap.has(key)\n      //       })\n      //       if (validTeams.length > 0) {\n      //         const team = validTeams[0]\n      //         const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\n\n      //         const preProcessKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\n      //         const currentTaskCount = preProcessDataMap.get(preProcessKey) || 0\n\n      //         if (currentTaskCount > row.allocatedTaskMax) {\n      //           IsAllNo++\n      //           return\n      //         }\n      //         const allocated = Math.min(row.allocatedTask, currentTaskCount)\n\n      //         row[tarCode] = Number(row[tarCode] || 0) + allocated\n      //         row.Can_Allocation_Count = row.Can_Allocation_Count - allocated\n      //         row[this.getTaskCode(team.Working_Team_Name) || 0] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + allocated\n      //         row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - allocated\n      //         row.allocatedTaskMax = row.Can_Allocation_Count\n      //         row.allocatedTask = row.Can_Allocation_Count\n      //         row['alCount' + tarCode] = row[tarCode]\n      //       }\n      //     }\n      //   })\n      //   if (IsAllNo === this.multipleSelection.length) {\n      //     this.dialogTipsVisible = true\n      //     return\n      //   }\n      // } else {\n      //   this.multipleSelection.forEach((row, idx) => {\n      //     const validTeams = row.Allocation_Teams.filter(team => {\n      //       const key = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\n      //       return preProcessDataMap.has(key)\n      //     })\n      //     if (validTeams.length > 0) {\n      //       const team = validTeams[0]\n      //       const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\n\n      //       const preProcessKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\n      //       const currentTaskCount = preProcessDataMap.get(preProcessKey) || 0\n\n      //       const selectNum = Math.min(\n      //         row[this.getRowCCode(row, 'alCount')] || 0,\n      //         row[this.getRowCCode(row)] || 0,\n      //         currentTaskCount\n      //       )\n\n      //       row[this.getTaskCode(team.Working_Team_Name)] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + selectNum\n      //       row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - selectNum\n      //       row[tarCode] = (Number(row[tarCode]) || 0) + selectNum\n      //       row[this.getRowCCode(row)] = (row[this.getRowCCode(row)] || 0) - selectNum\n      //       row[this.getRowCCode(row, 'alCount')] = (row[this.getRowCCode(row, 'alCount')] || 0) - selectNum\n      //       row['alCount' + tarCode] = row[tarCode]\n      //     }\n      //   })\n      // }\n      this.filterTbData = this.tbData.filter(row => {\n        return this.filterZero(row)\n      })\n      this.multipleSelection = []\n      this.$refs.xTable.clearCheckboxRow()\n      if (isMessage) {\n        this.$message({\n          message: '同步成功',\n          type: 'success'\n        })\n      }\n    },\n    // 批量分配提交\n    doAllocation() {\n      if (this.activeName === 'first') {\n        this.multipleSelection\n          .forEach((row, idx) => {\n            if (row.Can_Allocation_Count) {\n              const team = row.Allocation_Teams.find(v => v.Working_Team_Id === this.form.TeamGroup)\n              const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\n              row[tarCode] = Number(row[tarCode]) + row.allocatedTask\n              row.Can_Allocation_Count = row.Can_Allocation_Count - row.allocatedTask\n              row[this.getTaskCode(team.Working_Team_Name)] += row.allocatedTask\n              row[this.getTaskCode()] -= row.allocatedTask\n              row.allocatedTaskMax = row.Can_Allocation_Count\n              row.allocatedTask = row.Can_Allocation_Count\n              row['alCount' + tarCode] = row[tarCode]\n            }\n          })\n      } else {\n        this.multipleSelection\n          .forEach((row, idx) => {\n            const team = row.Allocation_Teams.find(v => v.Working_Team_Id === this.form.TeamGroup)\n            const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\n\n            const selectNum = Math.min(row[this.getRowCCode(row, 'alCount')], row[this.getRowCCode(row)])\n\n            row[this.getTaskCode(team.Working_Team_Name)] += selectNum\n            row[this.getTaskCode()] -= selectNum\n\n            row[tarCode] = Number(row[tarCode]) + selectNum\n            row[this.getRowCCode(row)] -= selectNum\n            row[this.getRowCCode(row, 'alCount')] -= selectNum\n\n            row['alCount' + tarCode] = row[tarCode]\n          })\n      }\n      this.filterTbData = this.tbData.filter(row => {\n        return this.filterZero(row)\n      })\n    },\n    resetForm(formName) {\n      this.$refs[formName].resetFields()\n      this.dialogVisible = false\n    },\n    filterTypeMethod({ option, row }) {\n      return row.Type.includes(option.data)\n    },\n    filterTypeRecoverMethod({ option }) {\n      option.data = ''\n    },\n    filterCodeMethod({ option, row }) {\n      console.log('option, row', option, row)\n      return row[this.isCom ? 'Comp_Code' : 'Part_Code'].includes(option.data)\n    },\n    filterCodeRecoverMethod({ option }) {\n      option.data = ''\n    },\n    getObjectTypeList() {\n      GetCompTypeTree({ professional: 'Steel' }).then((res) => {\n        if (res.IsSucceed) {\n          this.ObjectTypeList.data = res.Data\n          this.$nextTick((_) => {\n            this.$refs?.treeSelectObjectType?.treeDataUpdateFun(res.Data)\n          })\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.pagination-container {\n  padding: 0;\n  text-align: right;\n}\n\n::v-deep .el-card__body {\n  display: flex;\n  flex-direction: column;\n\n  .el-divider {\n    margin-top: 0;\n    margin-bottom: 16px;\n    background-color: #EEEEEE;\n  }\n\n  .tb-options {\n    margin-bottom: 16px;\n    display: flex;\n\n    .el-form-item--small.el-form-item {\n      margin-bottom: 0;\n    }\n    .el-input {\n      width: 250px;\n    }\n  }\n\n  footer {\n    text-align: inherit;\n    display: flex;\n    justify-content: space-between;\n  }\n}\n\n.tb-x {\n  flex: 1;\n  height: 0;\n  margin-bottom: 10px;\n  overflow: auto;\n}\n\n.topTitle {\n  height: 14px;\n  line-height: 14px;\n  font-size: 14px;\n  margin: 0 0 16px;\n\n  span {\n    display: inline-block;\n    width: 2px;\n    height: 14px;\n    background: #009dff;\n    vertical-align: middle;\n    margin-right: 6px;\n  }\n}\n\n.el-icon-edit {\n  cursor: pointer;\n}\n\n.cs-bottom {\n  position: relative;\n  height: 40px;\n  line-height: 40px;\n\n  .data-info {\n    position: absolute;\n    bottom: 0;\n\n    .info-x {\n      margin-right: 20px;\n    }\n  }\n}\n.my-input {\n  margin: 10px;\n  width: 140px;\n  height: 32px;\n}\n</style>\n"]}]}
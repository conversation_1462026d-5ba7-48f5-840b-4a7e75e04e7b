{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\Group.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\Group.vue", "mtime": 1745557754678}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Group.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAqBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Group.vue", "sourceRoot": "src/views/PRO/project-config/process-settings/component", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-transfer\r\n      v-model=\"selectList\"\r\n      filterable\r\n      :button-texts=\"['移除','添加']\"\r\n      :filter-method=\"filterMethod\"\r\n      :titles=\"['全部班组', '已选班组']\"\r\n      filter-placeholder=\"搜索...\"\r\n      :data=\"allList\"\r\n      :props=\"{label:'Display_Name',value:'Id',key:'Id'}\"\r\n      @change=\"change\"\r\n    />\r\n    <div style=\"text-align: right;margin-top: 10px\">\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button type=\"primary\" @click=\"handleSubmit\">确 定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  GetWorkingTeamBase,\r\n  GetWorkingTeams,\r\n  UpdateProcessTeam\r\n} from '@/api/PRO/technology-lib'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      allList: [],\r\n      selectList: [],\r\n      list: [],\r\n      value: [],\r\n      processId: '',\r\n      filterMethod(query, item) {\r\n        return item.Display_Name.indexOf(query) > -1\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    async init(row) {\r\n      this.processId = row.Id\r\n      await this.getAllList()\r\n      await this.getCurrentList(row)\r\n    },\r\n    getAllList() {\r\n      return new Promise((resolve) => {\r\n        GetWorkingTeams({}).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.allList = res.Data.map(v => {\r\n              this.$set(v, 'Display_Name', v.Name)\r\n              return v\r\n            })\r\n            resolve()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    getCurrentList() {\r\n      return new Promise((resolve) => {\r\n        GetWorkingTeamBase({\r\n          processId: this.processId\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.selectList = res.Data.map(v => v.Id)\r\n            resolve()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    change(array) {\r\n      console.log('array', array)\r\n      this.selectList = array\r\n    },\r\n    handleSubmit() {\r\n      UpdateProcessTeam({\r\n        processId: this.processId,\r\n        teams: this.selectList\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$emit('close')\r\n          this.$emit('refresh')\r\n          this.$message({\r\n            message: '修改成功',\r\n            type: 'success'\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"]}]}
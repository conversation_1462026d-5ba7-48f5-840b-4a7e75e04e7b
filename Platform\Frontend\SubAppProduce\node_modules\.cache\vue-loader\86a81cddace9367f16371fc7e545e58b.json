{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\change-management\\contact-list\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\change-management\\contact-list\\index.vue", "mtime": 1757468112548}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyB0YWJsZVBhZ2VTaXplIH0gZnJvbSAnQC92aWV3cy9QUk8vc2V0dGluZycNCmltcG9ydCBnZXRUYkluZm8gZnJvbSAnQC9taXhpbnMvUFJPL2dldC10YWJsZS1pbmZvJw0KaW1wb3J0IFBhZ2luYXRpb24gZnJvbSAnQC9jb21wb25lbnRzL1BhZ2luYXRpb24vaW5kZXgudnVlJw0KaW1wb3J0IHsNCiAgRGVsZXRlTW9jVHlwZSwNCiAgRGVsZXRlTW9jT3JkZXIsDQogIEdldE1vY09yZGVyUGFnZUxpc3QsIEdldE1vY09yZGVyVHlwZUxpc3QsDQogIFNhdmVNb2NPcmRlclR5cGUsIENoYW5nZU1vY09yZGVyU3RhdHVzLCBTdWJtaXRNb2NPcmRlcg0KfSBmcm9tICdAL2FwaS9QUk8vY2hhbmdlTWFuYWdlbWVudCcNCmltcG9ydCBhZGRSb3V0ZXJQYWdlIGZyb20gJ0AvbWl4aW5zL2FkZC1yb3V0ZXItcGFnZScNCmltcG9ydCB7IEdlQXJlYVRyZWVzLCBHZXRQcm9qZWN0UGFnZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vcHJvamVjdCcNCmltcG9ydCBNb25pdG9yIGZyb20gJ0AvY29tcG9uZW50cy9Nb25pdG9yL2luZGV4LnZ1ZScNCmltcG9ydCB7IENhbmNlbEZsb3cgfSBmcm9tICdAL2FwaS9QUk8vY29tcG9uZW50LXN0b2NrLW91dCcNCmltcG9ydCBEeW5hbWljVGFibGVGaWVsZHMgZnJvbSAnQC9jb21wb25lbnRzL0R5bmFtaWNUYWJsZUZpZWxkcy9pbmRleC52dWUnDQppbXBvcnQgeyBkZWJvdW5jZSB9IGZyb20gJ0AvdXRpbHMnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ1BST0VuZ2luZWVyaW5nQ2hhbmdlT3JkZXInLA0KICBjb21wb25lbnRzOiB7DQogICAgRHluYW1pY1RhYmxlRmllbGRzLA0KICAgIE1vbml0b3IsDQogICAgUGFnaW5hdGlvbg0KICB9LA0KICBtaXhpbnM6IFtnZXRUYkluZm8sIGFkZFJvdXRlclBhZ2VdLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBhZGRQYWdlQXJyYXk6IFsNCiAgICAgICAgew0KICAgICAgICAgIHBhdGg6IHRoaXMuJHJvdXRlLnBhdGggKyAnL2FkZCcsDQogICAgICAgICAgaGlkZGVuOiB0cnVlLA0KICAgICAgICAgIGNvbXBvbmVudDogKCkgPT4gaW1wb3J0KCcuL2FkZC52dWUnKSwNCiAgICAgICAgICBuYW1lOiAnUFJPRW5naW5lZXJpbmdDaGFuZ2VPcmRlckFkZCcsDQogICAgICAgICAgbWV0YTogeyB0aXRsZTogJ+aWsOWinicgfQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgcGF0aDogdGhpcy4kcm91dGUucGF0aCArICcvZWRpdCcsDQogICAgICAgICAgaGlkZGVuOiB0cnVlLA0KICAgICAgICAgIGNvbXBvbmVudDogKCkgPT4gaW1wb3J0KCcuL2FkZC52dWUnKSwNCiAgICAgICAgICBuYW1lOiAnUFJPRW5naW5lZXJpbmdDaGFuZ2VPcmRlckVkaXQnLA0KICAgICAgICAgIG1ldGE6IHsgdGl0bGU6ICfnvJbovpEnIH0NCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHBhdGg6IHRoaXMuJHJvdXRlLnBhdGggKyAnL3ZpZXcnLA0KICAgICAgICAgIGhpZGRlbjogdHJ1ZSwNCiAgICAgICAgICBjb21wb25lbnQ6ICgpID0+IGltcG9ydCgnLi9hZGQudnVlJyksDQogICAgICAgICAgbmFtZTogJ1BST0VuZ2luZWVyaW5nQ2hhbmdlT3JkZXJWaWV3JywNCiAgICAgICAgICBtZXRhOiB7IHRpdGxlOiAn5p+l55yLJyB9DQogICAgICAgIH0NCiAgICAgIF0sDQogICAgICBmb3JtOiB7DQogICAgICAgIFN5c19Qcm9qZWN0X0lkOiAnJywNCiAgICAgICAgU3RhdHVzOiAnJywNCiAgICAgICAgRXhlY19TdGF0dXM6ICcnLA0KICAgICAgICBDaGFuZ2VfRGF0ZTogJycsDQogICAgICAgIE1vY19UeXBlX0lkOiAnJywNCiAgICAgICAgQXJlYV9JZDogJycsDQogICAgICAgIFVyZ2VuY3k6ICcnDQogICAgICB9LA0KICAgICAgYWN0aXZlTmFtZTogJ3NlY29uZCcsDQogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHRiTG9hZGluZzogZmFsc2UsDQogICAgICBzZXR0aW5nTG9hZGluZzogZmFsc2UsDQogICAgICB0YkRhdGE6IFtdLA0KICAgICAgcHJvamVjdExpc3Q6IFtdLA0KICAgICAgZGlhbG9nVGFibGU6IFtdLA0KICAgICAgbW9jVHlwZTogW10sDQogICAgICBpbnN0YWxsVW5pdExpc3Q6IFtdLA0KICAgICAgdHJlZVBhcmFtc0FyZWE6IHsNCiAgICAgICAgJ2RlZmF1bHQtZXhwYW5kLWFsbCc6IHRydWUsDQogICAgICAgIGZpbHRlcmFibGU6IGZhbHNlLA0KICAgICAgICBjbGlja1BhcmVudDogdHJ1ZSwNCiAgICAgICAgZGF0YTogW10sDQogICAgICAgIHByb3BzOiB7DQogICAgICAgICAgZGlzYWJsZWQ6ICdkaXNhYmxlZCcsDQogICAgICAgICAgY2hpbGRyZW46ICdDaGlsZHJlbicsDQogICAgICAgICAgbGFiZWw6ICdMYWJlbCcsDQogICAgICAgICAgdmFsdWU6ICdJZCcNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIHRiQ29uZmlnOiB7fSwNCiAgICAgIG11bHRpcGxlU2VsZWN0aW9uOiBbXSwNCiAgICAgIHNlYXJjaDogKCkgPT4gKHt9KSwNCiAgICAgIGNvbHVtbnM6IFtdLA0KICAgICAgZ3JpZENvZGU6ICdQUk9FbmdDaGFuZ2VPcmRlcicsDQogICAgICB0YWJsZVBhZ2VTaXplOiB0YWJsZVBhZ2VTaXplLA0KICAgICAgcXVlcnlJbmZvOiB7DQogICAgICAgIFBhZ2U6IDEsDQogICAgICAgIFBhZ2VTaXplOiB0YWJsZVBhZ2VTaXplWzBdDQogICAgICB9LA0KICAgICAgdG90YWw6IDAsDQogICAgICBidXR0b25Db25maWdzOiB7DQogICAgICAgIGRyYWZ0OiBbDQogICAgICAgICAgeyB0ZXh0OiAn5o+Q5Lqk5a6h5qC4JywgaGFuZGxlcjogdGhpcy5oYW5kbGVTdWJtaXRBdWRpdCwgY2hlY2tLZXk6IHRoaXMuY2hlY2tLZXkgfSwNCiAgICAgICAgICB7IHRleHQ6ICfnvJbovpEnLCBoYW5kbGVyOiB0aGlzLmhhbmRsZUVkaXQsIGNoZWNrS2V5OiB0aGlzLmNoZWNrS2V5IH0sDQogICAgICAgICAgLy8geyB0ZXh0OiAn55uR5o6nJywgaGFuZGxlcjogdGhpcy5oYW5kbGVNb25pdG9yLCBjaGVja0tleTogdGhpcy5jaGVja0tleSB9LA0KICAgICAgICAgIHsgdGV4dDogJ+WIoOmZpCcsIGlzUmVkOiB0cnVlLCBoYW5kbGVyOiB0aGlzLmhhbmRsZURlbGV0ZSwgY2hlY2tLZXk6IHRoaXMuY2hlY2tLZXkgfQ0KICAgICAgICBdLA0KICAgICAgICByZXZpZXdpbmc6IFsNCiAgICAgICAgICB7IHRleHQ6ICfmn6XnnIsnLCBoYW5kbGVyOiB0aGlzLmhhbmRsZVZpZXcsIGNoZWNrS2V5OiB0aGlzLmNoZWNrS2V5IH0sDQogICAgICAgICAgeyB0ZXh0OiAn55uR5o6nJywga2V5OiAnbW9uaXRvcicsIGhhbmRsZXI6IHRoaXMuaGFuZGxlTW9uaXRvciwgY2hlY2tLZXk6IHRoaXMuY2hlY2tLZXkgfSwNCiAgICAgICAgICB7IHRleHQ6ICflm57mlLYnLCBoYW5kbGVyOiB0aGlzLmhhbmRsZVJlY3ljbGUsIGNoZWNrS2V5OiB0aGlzLmNoZWNrS2V5IH0NCiAgICAgICAgXSwNCiAgICAgICAgYXBwcm92ZWQ6IFsNCiAgICAgICAgICB7IHRleHQ6ICfmn6XnnIsnLCBoYW5kbGVyOiB0aGlzLmhhbmRsZVZpZXcsIGNoZWNrS2V5OiB0aGlzLmNoZWNrS2V5IH0sDQogICAgICAgICAgeyB0ZXh0OiAn55uR5o6nJywga2V5OiAnbW9uaXRvcicsIGhhbmRsZXI6IHRoaXMuaGFuZGxlTW9uaXRvciwgY2hlY2tLZXk6IHRoaXMuY2hlY2tLZXkgfQ0KICAgICAgICBdLA0KICAgICAgICBmaW5pc2g6IFsNCiAgICAgICAgICB7IHRleHQ6ICflrozmiJAnLCBoYW5kbGVyOiB0aGlzLmhhbmRsZUNvbXBsZXRlLCBjaGVja0tleTogdGhpcy5jaGVja0tleSB9DQogICAgICAgIF0NCiAgICAgIH0NCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5zZWFyY2ggPSBkZWJvdW5jZSh0aGlzLmZldGNoRGF0YSwgODAwLCB0cnVlKQ0KICAgIHRoaXMuZ2V0VGFibGVDb25maWcodGhpcy5ncmlkQ29kZSkNCiAgICB0aGlzLmZldGNoRGF0YSgxKQ0KICAgIHRoaXMuZ2V0QmFzaWNEYXRhKCkNCiAgICB0aGlzLmdldFNldHRpbmdJbmZvKCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGZldGNoRGF0YShwYWdlKSB7DQogICAgICBwYWdlICYmICh0aGlzLnF1ZXJ5SW5mby5QYWdlID0gcGFnZSkNCiAgICAgIGNvbnN0IHsgQ2hhbmdlX0RhdGUsIC4uLm90aGVycyB9ID0gdGhpcy5mb3JtDQogICAgICBjb25zdCBDaGFuZ2VfQmVnaW4gPSBDaGFuZ2VfRGF0ZVswXQ0KICAgICAgY29uc3QgQ2hhbmdlX0VuZCA9IENoYW5nZV9EYXRlWzFdDQogICAgICB0aGlzLnRiTG9hZGluZyA9IHRydWUNCiAgICAgIEdldE1vY09yZGVyUGFnZUxpc3QoeyAuLi5vdGhlcnMsIENoYW5nZV9CZWdpbiwgQ2hhbmdlX0VuZCwgLi4udGhpcy5xdWVyeUluZm8gfSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMudGJEYXRhID0gcmVzPy5EYXRhPy5EYXRhIHx8IFtdDQogICAgICAgICAgdGhpcy50b3RhbCA9IHJlcy5EYXRhLlRvdGFsQ291bnQNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgICAgdGhpcy50YkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLnRiTG9hZGluZyA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlU2V0dGluZygpIHsNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuZ2V0U2V0dGluZ0luZm8oKQ0KICAgIH0sDQogICAgYXN5bmMgaGFuZGxlQWRkU2V0dGluZyhyb3cpIHsNCiAgICAgIGNvbnN0ICR0YWJsZSA9IHRoaXMuJHJlZnMueFRhYmxlDQogICAgICBjb25zdCByZWNvcmQgPSB7DQogICAgICAgIERpc3BsYXlfTmFtZTogJycsDQogICAgICAgIElzX0RlZXBlbl9DaGFuZ2U6IGZhbHNlDQogICAgICB9DQogICAgICBjb25zdCB7IHJvdzogbmV3Um93IH0gPSBhd2FpdCAkdGFibGUuaW5zZXJ0QXQocmVjb3JkLCByb3cpDQogICAgICBhd2FpdCAkdGFibGUuc2V0RWRpdENlbGwobmV3Um93LCAnbmFtZScpDQogICAgfSwNCiAgICByZW1vdmVSb3dFdmVudChyb3cpIHsNCiAgICAgIGlmICghcm93LklkKSB7DQogICAgICAgIHRoaXMuJHJlZnMueFRhYmxlLnJlbW92ZShyb3cpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgdGhpcy4kY29uZmlybSgnIOaYr+WQpuWIoOmZpOivpeexu+Weiz8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICBEZWxldGVNb2NUeXBlKHsNCiAgICAgICAgICBpZHM6IHJvdy5JZA0KICAgICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsDQogICAgICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTmiJDlip8hJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHRoaXMuZ2V0U2V0dGluZ0luZm8oKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICB0eXBlOiAnaW5mbycsDQogICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iCcNCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfSwNCiAgICBnZXRTZXR0aW5nSW5mbygpIHsNCiAgICAgIEdldE1vY09yZGVyVHlwZUxpc3Qoe30pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLmRpYWxvZ1RhYmxlID0gcmVzLkRhdGENCiAgICAgICAgICB0aGlzLm1vY1R5cGUgPSByZXMuRGF0YQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFzeW5jIGNoYW5nZUNvbHVtbigpIHsNCiAgICAgIGF3YWl0IHRoaXMuZ2V0VGFibGVDb25maWcodGhpcy5ncmlkQ29kZSkNCiAgICB9LA0KICAgIGVkaXRDbG9zZWRFdmVudCh7IHJvdywgY29sdW1uIH0pIHsNCiAgICAgIGlmICghcm93LkRpc3BsYXlfTmFtZSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAn5ZCN56ew5LiN6IO95Li656m6JywNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgfSkNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICBjb25zdCAkdGFibGUgPSB0aGlzLiRyZWZzLnhUYWJsZQ0KICAgICAgY29uc3QgZmllbGQgPSBjb2x1bW4uZmllbGQNCiAgICAgIGxldCBmbGFnID0gZmFsc2UNCiAgICAgIGlmICgkdGFibGUuaXNVcGRhdGVCeVJvdyhyb3csIGZpZWxkKSAmJiByb3cuSWQgfHwgIXJvdy5JZCkgew0KICAgICAgICBmbGFnID0gdHJ1ZQ0KICAgICAgfQ0KICAgICAgaWYgKGZsYWcpIHsNCiAgICAgICAgY29uc3Qgb2JqID0gew0KICAgICAgICAgIERpc3BsYXlfTmFtZTogcm93LkRpc3BsYXlfTmFtZSwNCiAgICAgICAgICBJc19EZWVwZW5fQ2hhbmdlOiByb3cuSXNfRGVlcGVuX0NoYW5nZQ0KICAgICAgICB9DQogICAgICAgIHJvdy5JZCAmJiAob2JqLklkID0gcm93LklkKQ0KICAgICAgICBTYXZlTW9jT3JkZXJUeXBlKG9iaikudGhlbihyZXMgPT4gew0KICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+S/neWtmOaIkOWKnycsDQogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICR0YWJsZS5yZWxvYWRSb3cocm93LCBudWxsLCBmaWVsZCkNCiAgICAgICAgICAgIHRoaXMuZ2V0U2V0dGluZ0luZm8oKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBnZXRCYXNpY0RhdGEoKSB7DQogICAgICBHZXRQcm9qZWN0UGFnZUxpc3QoeyBQYWdlU2l6ZTogLTEgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy5wcm9qZWN0TGlzdCA9IHJlcy5EYXRhLkRhdGENCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGdldEFyZWFMaXN0KFN5c19Qcm9qZWN0X0lkKSB7DQogICAgICBHZUFyZWFUcmVlcyh7DQogICAgICAgIHN5c1Byb2plY3RJZDogU3lzX1Byb2plY3RfSWQNCiAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICBjb25zdCB0cmVlID0gcmVzLkRhdGENCiAgICAgICAgICB0aGlzLnNldERpc2FibGVkVHJlZSh0cmVlKQ0KICAgICAgICAgIHRoaXMudHJlZVBhcmFtc0FyZWEuZGF0YSA9IHJlcy5EYXRhDQogICAgICAgICAgdGhpcy4kbmV4dFRpY2soXyA9PiB7DQogICAgICAgICAgICB0aGlzLiRyZWZzLnRyZWVTZWxlY3RBcmVhLnRyZWVEYXRhVXBkYXRlRnVuKHJlcy5EYXRhKQ0KICAgICAgICAgIH0pDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgc2V0RGlzYWJsZWRUcmVlKHJvb3QpIHsNCiAgICAgIGlmICghcm9vdCkgcmV0dXJuDQogICAgICByb290LmZvckVhY2goKGVsZW1lbnQpID0+IHsNCiAgICAgICAgY29uc3QgeyBDaGlsZHJlbiB9ID0gZWxlbWVudA0KICAgICAgICBpZiAoQ2hpbGRyZW4gJiYgQ2hpbGRyZW4ubGVuZ3RoKSB7DQogICAgICAgICAgZWxlbWVudC5kaXNhYmxlZCA9IHRydWUNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBlbGVtZW50LmRpc2FibGVkID0gZmFsc2UNCiAgICAgICAgICB0aGlzLnNldERpc2FibGVkVHJlZShDaGlsZHJlbikNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qICAgIGdldEluc3RhbGxVbml0UGFnZUxpc3QoKSB7DQogICAgICBHZXRJbnN0YWxsVW5pdFBhZ2VMaXN0KHsNCiAgICAgICAgQXJlYV9JZDogdGhpcy5mb3JtLkFyZWFfSWQsDQogICAgICAgIFBhZ2U6IDEsDQogICAgICAgIFBhZ2VTaXplOiAtMQ0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIHRoaXMuaW5zdGFsbFVuaXRMaXN0ID0gcmVzLkRhdGEuRGF0YQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LCovDQogICAgcHJvamVjdENoYW5nZShlKSB7DQogICAgICBjb25zdCBTeXNfUHJvamVjdF9JZCA9IGUNCiAgICAgIHRoaXMuZm9ybS5BcmVhX0lkID0gJycNCiAgICAgIHRoaXMudHJlZVBhcmFtc0FyZWEuZGF0YSA9IFtdDQogICAgICB0aGlzLiRuZXh0VGljayhfID0+IHsNCiAgICAgICAgdGhpcy4kcmVmcy50cmVlU2VsZWN0QXJlYS50cmVlRGF0YVVwZGF0ZUZ1bihbXSkNCiAgICAgIH0pDQogICAgICBpZiAoZSkgew0KICAgICAgICB0aGlzLmdldEFyZWFMaXN0KFN5c19Qcm9qZWN0X0lkKQ0KICAgICAgfQ0KICAgIH0sDQogICAgYXJlYUNoYW5nZSgpIHsNCiAgICAgIC8vIHRoaXMuZ2V0SW5zdGFsbFVuaXRQYWdlTGlzdCgpDQogICAgfSwNCiAgICBhcmVhQ2xlYXIoKSB7DQogICAgICB0aGlzLmZvcm0uQXJlYV9JZCA9ICcnDQogICAgfSwNCg0KICAgIGhhbmRsZUFkZCh0YWIsIGV2ZW50KSB7DQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7IG5hbWU6ICdQUk9FbmdpbmVlcmluZ0NoYW5nZU9yZGVyQWRkJywgcXVlcnk6IHsgcGdfcmVkaXJlY3Q6IHRoaXMuJHJvdXRlLm5hbWUgfX0pDQogICAgfSwNCiAgICBoYW5kbGVTZWFyY2godGFiLCBldmVudCkgew0KICAgICAgY29uc29sZS5sb2codGFiLCBldmVudCkNCiAgICB9LA0KICAgIG11bHRpU2VsZWN0ZWRDaGFuZ2UoYXJyYXkpIHsNCiAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24gPSBhcnJheS5yZWNvcmRzDQogICAgfSwNCiAgICBoYW5kbGVSZXNldCgpIHsNCiAgICAgIHRoaXMuJHJlZnNbJ2Zvcm0nXS5yZXNldEZpZWxkcygpDQogICAgICB0aGlzLnNlYXJjaCgxKQ0KICAgIH0sDQogICAgZ2V0QnV0dG9uc0J5U3RhdHVzKHN0YXR1cywgcm93KSB7DQogICAgICAvLyArLTHvvJrojYnnqL/vvIwy77ya5a6h5om55Lit77yMM++8muW3sumAmui/h+WujOaIkO+8jC0y77ya5a6h5qC45pyq6YCa6L+HDQogICAgICBzd2l0Y2ggKHN0YXR1cykgew0KICAgICAgICBjYXNlIC0xOg0KICAgICAgICBjYXNlIDE6DQogICAgICAgIGNhc2UgLTI6DQogICAgICAgICAgcmV0dXJuIHRoaXMuYnV0dG9uQ29uZmlncy5kcmFmdA0KICAgICAgICBjYXNlIDI6DQogICAgICAgICAgcmV0dXJuIHRoaXMuYnV0dG9uQ29uZmlncy5yZXZpZXdpbmcNCiAgICAgICAgY2FzZSAzOg0KICAgICAgICAgIGlmIChyb3cuRXhlY19TdGF0dXMgPT09IDIpIHsNCiAgICAgICAgICAgIHJldHVybiBbLi4udGhpcy5idXR0b25Db25maWdzLmFwcHJvdmVkLCAuLi50aGlzLmJ1dHRvbkNvbmZpZ3MuZmluaXNoXQ0KICAgICAgICAgIH0NCiAgICAgICAgICByZXR1cm4gdGhpcy5idXR0b25Db25maWdzLmFwcHJvdmVkDQogICAgICAgIGRlZmF1bHQ6DQogICAgICAgICAgcmV0dXJuIFtdDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVTdWJtaXRBdWRpdChyb3cpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfmj5DkuqTlrqHmoLgnLCByb3cpDQogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbmj5DkuqTlrqHmoLg/JywgJ+aPkOekuicsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy50YkxvYWRpbmcgPSB0cnVlDQogICAgICAgIFN1Ym1pdE1vY09yZGVyKHsNCiAgICAgICAgICBJZDogcm93LklkDQogICAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywNCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+aPkOS6pOaIkOWKnyEnDQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgdGhpcy5mZXRjaERhdGEoMSkNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLnRiTG9hZGluZyA9IGZhbHNlDQogICAgICAgIH0pDQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIHR5cGU6ICdpbmZvJywNCiAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raIJw0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZUVkaXQocm93KSB7DQogICAgICBjb25zb2xlLmxvZygn57yW6L6RJywgcm93KQ0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goeyBuYW1lOiAnUFJPRW5naW5lZXJpbmdDaGFuZ2VPcmRlckVkaXQnLCBxdWVyeTogeyBwZ19yZWRpcmVjdDogdGhpcy4kcm91dGUubmFtZSwgdHlwZTogMSwgaWQ6IHJvdy5JZCB9fSkNCiAgICB9LA0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuWIoOmZpOivpeaVsOaNrj8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLnRiTG9hZGluZyA9IHRydWUNCiAgICAgICAgRGVsZXRlTW9jT3JkZXIoew0KICAgICAgICAgIElkOiByb3cuSWQNCiAgICAgICAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLA0KICAgICAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5oiQ5YqfIScNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICB0aGlzLmZldGNoRGF0YSgxKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMudGJMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgfSkNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgdHlwZTogJ2luZm8nLA0KICAgICAgICAgIG1lc3NhZ2U6ICflt7Llj5bmtognDQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlVmlldyhyb3cpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfmn6XnnIsnLCByb3cpDQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7IG5hbWU6ICdQUk9FbmdpbmVlcmluZ0NoYW5nZU9yZGVyVmlldycsIHF1ZXJ5OiB7IHBnX3JlZGlyZWN0OiB0aGlzLiRyb3V0ZS5uYW1lLCBpZDogcm93LklkLCB0eXBlOiAyIH19KQ0KICAgIH0sDQogICAgaGFuZGxlTW9uaXRvcihyb3cpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfnm5HmjqcnLCByb3cpDQogICAgICB0aGlzLiRyZWZzWydtb25pdG9yJ10ub3BlbmRpYWxvZyhyb3cuSW5zdGFuY2VfSWQsIGZhbHNlKQ0KICAgIH0sDQogICAgaGFuZGxlUmVjeWNsZShyb3cpIHsNCiAgICAgIGNvbnNvbGUubG9nKCflm57mlLYnLCByb3cpDQogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKblm57mlLY/JywgJ+aPkOekuicsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy50YkxvYWRpbmcgPSB0cnVlDQogICAgICAgIENhbmNlbEZsb3coew0KICAgICAgICAgIGluc3RhbmNlSWQ6IHJvdy5JbnN0YW5jZV9JZA0KICAgICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiAn5Zue5pS25oiQ5YqfJywNCiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgdGhpcy5mZXRjaERhdGEoKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMudGJMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgfSkNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgdHlwZTogJ2luZm8nLA0KICAgICAgICAgIG1lc3NhZ2U6ICflt7Llj5bmtognDQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0sDQogICAgZWRpdERpc2FibGVkRXZlbnQoeyByb3csIGNvbHVtbiB9KSB7DQogICAgICAvLyBjb25zdCAkdGFibGUgPSB0aGlzLiRyZWZzLnhUYWJsZQ0KICAgICAgLy8gJHRhYmxlLm1vZGFsLm1lc3NhZ2UoeyBjb250ZW50OiAn56aB5q2i57yW6L6RJywgc3RhdHVzOiAnZXJyb3InIH0pDQogICAgfSwNCiAgICBhY3RpdmVSb3dNZXRob2QoeyByb3csIHJvd0luZGV4IH0pIHsNCiAgICAgIHJldHVybiAhcm93LklkDQogICAgfSwNCiAgICBoYW5kbGVDb21wbGV0ZShyb3cpIHsNCiAgICAgIGNvbnNvbGUubG9nKCflrozmiJAnLCByb3cpDQogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKblrozmiJA/JywgJ+aPkOekuicsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy50YkxvYWRpbmcgPSB0cnVlDQogICAgICAgIENoYW5nZU1vY09yZGVyU3RhdHVzKHsNCiAgICAgICAgICBJZDogcm93LklkDQogICAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywNCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+aTjeS9nOaIkOWKnyEnDQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgdGhpcy5mZXRjaERhdGEoMSkNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLnRiTG9hZGluZyA9IGZhbHNlDQogICAgICAgIH0pDQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIHR5cGU6ICdpbmZvJywNCiAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raIJw0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGNoZWNrS2V5KGtleSwgcm93KSB7DQogICAgICBpZiAoIWtleSkgcmV0dXJuIHRydWUNCiAgICAgIGlmIChrZXkgPT09ICdtb25pdG9yJykgew0KICAgICAgICByZXR1cm4gISFyb3dbJ0luc3RhbmNlX0lkJ10NCiAgICAgIH0NCiAgICAgIHJldHVybiB0cnVlDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy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file": "index.vue", "sourceRoot": "src/views/PRO/change-management/contact-list", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <div class=\"cs-box mb10\">\r\n      <el-row>\r\n        <el-form ref=\"form\" :model=\"form\" label-width=\"100px\">\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"项目名称\" prop=\"Sys_Project_Id\">\r\n              <el-select\r\n                v-model=\"form.Sys_Project_Id\"\r\n                clearable\r\n                style=\"width: 100%\"\r\n                placeholder=\"请选择\"\r\n                filterable\r\n                @change=\"projectChange(form.Sys_Project_Id)\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in projectList\"\r\n                  :key=\"item.Sys_Project_Id\"\r\n                  :label=\"item.Short_Name\"\r\n                  :value=\"item.Sys_Project_Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"区域\" prop=\"Area_Id\">\r\n              <el-tree-select\r\n                ref=\"treeSelectArea\"\r\n                v-model=\"form.Area_Id\"\r\n                class=\"cs-tree-x\"\r\n                :disabled=\"!form.Sys_Project_Id\"\r\n                :select-params=\"{\r\n                  clearable: true,\r\n                }\"\r\n                :tree-params=\"treeParamsArea\"\r\n                @select-clear=\"areaClear\"\r\n                @node-click=\"areaChange\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"单据状态\" prop=\"Status\">\r\n              <el-select v-model=\"form.Status\" clearable placeholder=\"请选择\" style=\"width: 100%\">\r\n                <el-option label=\"草稿\" :value=\"1\" />\r\n                <el-option label=\"审核中\" :value=\"2\" />\r\n                <el-option label=\"审核未通过\" :value=\"-2\" />\r\n                <el-option label=\"审核通过\" :value=\"3\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"变更类型\" prop=\"Moc_Type_Id\">\r\n              <el-select v-model=\"form.Moc_Type_Id\" placeholder=\"请选择\" clearable=\"\">\r\n                <el-option\r\n                  v-for=\"item in mocType\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"执行情况\" prop=\"Exec_Status\">\r\n              <el-select v-model=\"form.Exec_Status\" clearable placeholder=\"请选择\" style=\"width: 100%\">\r\n                <el-option label=\"未开始\" :value=\"1\" />\r\n                <el-option label=\"执行中\" :value=\"2\" />\r\n                <el-option label=\"已完成\" :value=\"3\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"变更日期\" prop=\"Change_Date\">\r\n              <el-date-picker\r\n                v-model=\"form.Change_Date\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                type=\"daterange\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始日期\"\r\n                end-placeholder=\"结束日期\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"紧急程度\" prop=\"Urgency\">\r\n              <el-select v-model=\"form.Urgency\" clearable placeholder=\"请选择\" style=\"width: 100%\">\r\n                <el-option label=\"普通\" :value=\"1\" />\r\n                <el-option label=\"紧急\" :value=\"2\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label-width=\"20px\">\r\n              <el-button type=\"primary\" @click=\"search(1)\">搜索</el-button>\r\n              <el-button @click=\"handleReset\">重置</el-button>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-form>\r\n      </el-row>\r\n    </div>\r\n    <div class=\"cs-box cs-main\">\r\n      <vxe-toolbar\r\n        ref=\"xToolbar1\"\r\n        class=\"cs-toolBar\"\r\n      >\r\n        <template #buttons>\r\n          <el-button type=\"primary\" @click=\"handleAdd\">新建联系单</el-button>\r\n        </template>\r\n        <template #tools>\r\n          <el-button type=\"primary\" @click=\"handleSetting\">变更类型配置</el-button>\r\n          <DynamicTableFields\r\n            title=\"表格配置\"\r\n            :table-config-code=\"gridCode\"\r\n            @updateColumn=\"changeColumn\"\r\n          />\r\n        </template>\r\n      </vxe-toolbar>\r\n\r\n      <div class=\"cs-bottom-wapper\">\r\n        <div class=\"fff tb-x\">\r\n          <vxe-table\r\n            :empty-render=\"{name: 'NotData'}\"\r\n            show-header-overflow\r\n            :loading=\"tbLoading\"\r\n            element-loading-spinner=\"el-icon-loading\"\r\n            element-loading-text=\"拼命加载中\"\r\n            empty-text=\"暂无数据\"\r\n            class=\"cs-vxe-table\"\r\n            height=\"100%\"\r\n            align=\"left\"\r\n            stripe\r\n            :data=\"tbData\"\r\n            resizable\r\n            :tooltip-config=\"{ enterable: true}\"\r\n            :checkbox-config=\"{checkField: 'checked'}\"\r\n            @checkbox-all=\"multiSelectedChange\"\r\n            @checkbox-change=\"multiSelectedChange\"\r\n          >\r\n            <!--            <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" />-->\r\n            <template v-for=\"item in columns\">\r\n              <vxe-column\r\n                :key=\"item.Code\"\r\n                :min-width=\"item.Width\"\r\n                show-overflow=\"tooltip\"\r\n                sortable\r\n                align=\"center\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                :fixed=\"item.Is_Frozen ? (item.Frozen_Dirction || 'left') : ''\"\r\n              >\r\n                <template v-if=\"['Change_Date','Demand_Date','Create_Date','Change_End'].includes(item.Code) \" #default=\"{ row }\">\r\n                  {{ row[item.Code] | timeFormat }}\r\n                </template>\r\n                <template v-else-if=\"item.Code === 'Exec_Status'\" #default=\"{ row }\">\r\n                  <span :class=\"['cs-tags',row[item.Code]===1?'cs-red':row[item.Code]===2?'cs-blue':'cs-green']\">{{ row[item.Code]===1?'未开始':row[item.Code]===2?'执行中':row[item.Code]===3?'已完成':'' }}</span>\r\n                </template>\r\n                <template v-else-if=\"item.Code === 'Urgency'\" #default=\"{ row }\">\r\n                  <el-tag v-if=\"row.Urgency == 1\" type=\"primary\">普通</el-tag>\r\n                  <el-tag v-else-if=\"row.Urgency == 2\" type=\"danger\">紧急</el-tag>\r\n                  <span v-else>-</span>\r\n                </template>\r\n                <!--                <template v-else-if=\"item.Code === 'Change_Type'\" #default=\"{ row }\">-->\r\n                <!--                  <span> {{ row[item.Code] ==='0'?'完整变更':row[item.Code] ==='1'?'部分变更':row[item.Code] ==='2'?'手动变更':'' }}</span>-->\r\n                <!--                </template>-->\r\n                <template v-else #default=\"{ row }\">\r\n                  <span> {{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n            </template>\r\n            <vxe-column fixed=\"right\" title=\"操作\" width=\"180\">\r\n              <template #default=\"{ row }\">\r\n                <template v-for=\"btn in getButtonsByStatus(row.Status,row)\">\r\n                  <el-button\r\n                    v-if=\"btn.checkKey(btn.key,row)\"\r\n                    :key=\"btn.text\"\r\n                    :class=\"{'txt-red':btn.isRed}\"\r\n                    type=\"text\"\r\n                    @click=\"btn.handler(row)\"\r\n                  >{{ btn.text }}</el-button>\r\n                </template>\r\n\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-table>\r\n        </div>\r\n        <div class=\"data-info\">\r\n          <!--          <el-tag-->\r\n          <!--            size=\"medium\"-->\r\n          <!--            class=\"info-x\"-->\r\n          <!--          >已选 {{ multipleSelection.length }} 条数据-->\r\n          <!--          </el-tag>-->\r\n          <Pagination\r\n            :total=\"total\"\r\n            :page-sizes=\"tablePageSize\"\r\n            :page.sync=\"queryInfo.Page\"\r\n            :limit.sync=\"queryInfo.PageSize\"\r\n            @pagination=\"pageChange\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      title=\"变更类型\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"50%\"\r\n      class=\"plm-custom-dialog cs-dialog\"\r\n      @close=\"dialogVisible=false\"\r\n    >\r\n      <div>\r\n        <vxe-toolbar>\r\n          <template #buttons>\r\n            <vxe-button status=\"primary\" content=\"添加\" @click=\"handleAddSetting()\" />\r\n          </template>\r\n        </vxe-toolbar>\r\n\r\n        <vxe-table\r\n          ref=\"xTable\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          border\r\n          class=\"cs-vxe-table\"\r\n          stripe\r\n          resizable\r\n          show-overflow\r\n          :loading=\"settingLoading\"\r\n          :data=\"dialogTable\"\r\n          :edit-config=\"{beforeEditMethod: activeRowMethod,trigger: 'click',showStatus: true, mode: 'row'}\"\r\n          @edit-closed=\"editClosedEvent\"\r\n          @edit-disabled=\"editDisabledEvent\"\r\n        >\r\n\r\n          <vxe-column\r\n            align=\"left\"\r\n            field=\"Display_Name\"\r\n            title=\"类型名称\"\r\n            min-width=\"180\"\r\n            :edit-render=\"{autofocus: '.vxe-input--inner'}\"\r\n          >\r\n            <template #edit=\"{ row }\">\r\n              <vxe-input v-model=\"row.Display_Name\" type=\"text\" />\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column\r\n            align=\"left\"\r\n            field=\"Is_Deepen_Change\"\r\n            title=\"是否变更清单\"\r\n            min-width=\"180\"\r\n            :edit-render=\"{}\"\r\n          >\r\n            <template #edit=\"{ row }\">\r\n              <el-radio v-model=\"row.Is_Deepen_Change\" :label=\"true\">是</el-radio>\r\n              <el-radio v-model=\"row.Is_Deepen_Change\" :label=\"false\">否</el-radio>\r\n            </template>\r\n            <template #default=\"{row}\">\r\n              <el-tag v-if=\" row.Is_Deepen_Change\" type=\"success\">是</el-tag>\r\n              <el-tag v-else type=\"danger\">否</el-tag>\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column\r\n            align=\"left\"\r\n            title=\"操作\"\r\n          >\r\n            <template #default=\"{ row }\">\r\n              <el-button type=\"text\" class=\"txt-red\" @click=\"removeRowEvent(row)\">删除</el-button>\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">关 闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <Monitor ref=\"monitor\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport Pagination from '@/components/Pagination/index.vue'\r\nimport {\r\n  DeleteMocType,\r\n  DeleteMocOrder,\r\n  GetMocOrderPageList, GetMocOrderTypeList,\r\n  SaveMocOrderType, ChangeMocOrderStatus, SubmitMocOrder\r\n} from '@/api/PRO/changeManagement'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nimport { GeAreaTrees, GetProjectPageList } from '@/api/PRO/project'\r\nimport Monitor from '@/components/Monitor/index.vue'\r\nimport { CancelFlow } from '@/api/PRO/component-stock-out'\r\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\r\nimport { debounce } from '@/utils'\r\n\r\nexport default {\r\n  name: 'PROEngineeringChangeOrder',\r\n  components: {\r\n    DynamicTableFields,\r\n    Monitor,\r\n    Pagination\r\n  },\r\n  mixins: [getTbInfo, addRouterPage],\r\n  data() {\r\n    return {\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/add',\r\n          hidden: true,\r\n          component: () => import('./add.vue'),\r\n          name: 'PROEngineeringChangeOrderAdd',\r\n          meta: { title: '新增' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/edit',\r\n          hidden: true,\r\n          component: () => import('./add.vue'),\r\n          name: 'PROEngineeringChangeOrderEdit',\r\n          meta: { title: '编辑' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/view',\r\n          hidden: true,\r\n          component: () => import('./add.vue'),\r\n          name: 'PROEngineeringChangeOrderView',\r\n          meta: { title: '查看' }\r\n        }\r\n      ],\r\n      form: {\r\n        Sys_Project_Id: '',\r\n        Status: '',\r\n        Exec_Status: '',\r\n        Change_Date: '',\r\n        Moc_Type_Id: '',\r\n        Area_Id: '',\r\n        Urgency: ''\r\n      },\r\n      activeName: 'second',\r\n      dialogVisible: false,\r\n      tbLoading: false,\r\n      settingLoading: false,\r\n      tbData: [],\r\n      projectList: [],\r\n      dialogTable: [],\r\n      mocType: [],\r\n      installUnitList: [],\r\n      treeParamsArea: {\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          disabled: 'disabled',\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      tbConfig: {},\r\n      multipleSelection: [],\r\n      search: () => ({}),\r\n      columns: [],\r\n      gridCode: 'PROEngChangeOrder',\r\n      tablePageSize: tablePageSize,\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: tablePageSize[0]\r\n      },\r\n      total: 0,\r\n      buttonConfigs: {\r\n        draft: [\r\n          { text: '提交审核', handler: this.handleSubmitAudit, checkKey: this.checkKey },\r\n          { text: '编辑', handler: this.handleEdit, checkKey: this.checkKey },\r\n          // { text: '监控', handler: this.handleMonitor, checkKey: this.checkKey },\r\n          { text: '删除', isRed: true, handler: this.handleDelete, checkKey: this.checkKey }\r\n        ],\r\n        reviewing: [\r\n          { text: '查看', handler: this.handleView, checkKey: this.checkKey },\r\n          { text: '监控', key: 'monitor', handler: this.handleMonitor, checkKey: this.checkKey },\r\n          { text: '回收', handler: this.handleRecycle, checkKey: this.checkKey }\r\n        ],\r\n        approved: [\r\n          { text: '查看', handler: this.handleView, checkKey: this.checkKey },\r\n          { text: '监控', key: 'monitor', handler: this.handleMonitor, checkKey: this.checkKey }\r\n        ],\r\n        finish: [\r\n          { text: '完成', handler: this.handleComplete, checkKey: this.checkKey }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    this.getTableConfig(this.gridCode)\r\n    this.fetchData(1)\r\n    this.getBasicData()\r\n    this.getSettingInfo()\r\n  },\r\n  methods: {\r\n    fetchData(page) {\r\n      page && (this.queryInfo.Page = page)\r\n      const { Change_Date, ...others } = this.form\r\n      const Change_Begin = Change_Date[0]\r\n      const Change_End = Change_Date[1]\r\n      this.tbLoading = true\r\n      GetMocOrderPageList({ ...others, Change_Begin, Change_End, ...this.queryInfo }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res?.Data?.Data || []\r\n          this.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.tbLoading = false\r\n      }).catch(() => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    handleSetting() {\r\n      this.dialogVisible = true\r\n      this.getSettingInfo()\r\n    },\r\n    async handleAddSetting(row) {\r\n      const $table = this.$refs.xTable\r\n      const record = {\r\n        Display_Name: '',\r\n        Is_Deepen_Change: false\r\n      }\r\n      const { row: newRow } = await $table.insertAt(record, row)\r\n      await $table.setEditCell(newRow, 'name')\r\n    },\r\n    removeRowEvent(row) {\r\n      if (!row.Id) {\r\n        this.$refs.xTable.remove(row)\r\n        return\r\n      }\r\n      this.$confirm(' 是否删除该类型?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        DeleteMocType({\r\n          ids: row.Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.getSettingInfo()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    getSettingInfo() {\r\n      GetMocOrderTypeList({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.dialogTable = res.Data\r\n          this.mocType = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async changeColumn() {\r\n      await this.getTableConfig(this.gridCode)\r\n    },\r\n    editClosedEvent({ row, column }) {\r\n      if (!row.Display_Name) {\r\n        this.$message({\r\n          message: '名称不能为空',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      const $table = this.$refs.xTable\r\n      const field = column.field\r\n      let flag = false\r\n      if ($table.isUpdateByRow(row, field) && row.Id || !row.Id) {\r\n        flag = true\r\n      }\r\n      if (flag) {\r\n        const obj = {\r\n          Display_Name: row.Display_Name,\r\n          Is_Deepen_Change: row.Is_Deepen_Change\r\n        }\r\n        row.Id && (obj.Id = row.Id)\r\n        SaveMocOrderType(obj).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            $table.reloadRow(row, null, field)\r\n            this.getSettingInfo()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n    },\r\n\r\n    getBasicData() {\r\n      GetProjectPageList({ PageSize: -1 }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.projectList = res.Data.Data\r\n        }\r\n      })\r\n    },\r\n    getAreaList(Sys_Project_Id) {\r\n      GeAreaTrees({\r\n        sysProjectId: Sys_Project_Id\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const tree = res.Data\r\n          this.setDisabledTree(tree)\r\n          this.treeParamsArea.data = res.Data\r\n          this.$nextTick(_ => {\r\n            this.$refs.treeSelectArea.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setDisabledTree(root) {\r\n      if (!root) return\r\n      root.forEach((element) => {\r\n        const { Children } = element\r\n        if (Children && Children.length) {\r\n          element.disabled = true\r\n        } else {\r\n          element.disabled = false\r\n          this.setDisabledTree(Children)\r\n        }\r\n      })\r\n    },\r\n    /*    getInstallUnitPageList() {\r\n      GetInstallUnitPageList({\r\n        Area_Id: this.form.Area_Id,\r\n        Page: 1,\r\n        PageSize: -1\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          if (res.IsSucceed) {\r\n            this.installUnitList = res.Data.Data\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },*/\r\n    projectChange(e) {\r\n      const Sys_Project_Id = e\r\n      this.form.Area_Id = ''\r\n      this.treeParamsArea.data = []\r\n      this.$nextTick(_ => {\r\n        this.$refs.treeSelectArea.treeDataUpdateFun([])\r\n      })\r\n      if (e) {\r\n        this.getAreaList(Sys_Project_Id)\r\n      }\r\n    },\r\n    areaChange() {\r\n      // this.getInstallUnitPageList()\r\n    },\r\n    areaClear() {\r\n      this.form.Area_Id = ''\r\n    },\r\n\r\n    handleAdd(tab, event) {\r\n      this.$router.push({ name: 'PROEngineeringChangeOrderAdd', query: { pg_redirect: this.$route.name }})\r\n    },\r\n    handleSearch(tab, event) {\r\n      console.log(tab, event)\r\n    },\r\n    multiSelectedChange(array) {\r\n      this.multipleSelection = array.records\r\n    },\r\n    handleReset() {\r\n      this.$refs['form'].resetFields()\r\n      this.search(1)\r\n    },\r\n    getButtonsByStatus(status, row) {\r\n      // +-1：草稿，2：审批中，3：已通过完成，-2：审核未通过\r\n      switch (status) {\r\n        case -1:\r\n        case 1:\r\n        case -2:\r\n          return this.buttonConfigs.draft\r\n        case 2:\r\n          return this.buttonConfigs.reviewing\r\n        case 3:\r\n          if (row.Exec_Status === 2) {\r\n            return [...this.buttonConfigs.approved, ...this.buttonConfigs.finish]\r\n          }\r\n          return this.buttonConfigs.approved\r\n        default:\r\n          return []\r\n      }\r\n    },\r\n    handleSubmitAudit(row) {\r\n      console.log('提交审核', row)\r\n      this.$confirm('是否提交审核?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbLoading = true\r\n        SubmitMocOrder({\r\n          Id: row.Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '提交成功!'\r\n            })\r\n            this.fetchData(1)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.tbLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    handleEdit(row) {\r\n      console.log('编辑', row)\r\n      this.$router.push({ name: 'PROEngineeringChangeOrderEdit', query: { pg_redirect: this.$route.name, type: 1, id: row.Id }})\r\n    },\r\n    handleDelete(row) {\r\n      this.$confirm('是否删除该数据?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbLoading = true\r\n        DeleteMocOrder({\r\n          Id: row.Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.fetchData(1)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.tbLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    handleView(row) {\r\n      console.log('查看', row)\r\n      this.$router.push({ name: 'PROEngineeringChangeOrderView', query: { pg_redirect: this.$route.name, id: row.Id, type: 2 }})\r\n    },\r\n    handleMonitor(row) {\r\n      console.log('监控', row)\r\n      this.$refs['monitor'].opendialog(row.Instance_Id, false)\r\n    },\r\n    handleRecycle(row) {\r\n      console.log('回收', row)\r\n      this.$confirm('是否回收?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbLoading = true\r\n        CancelFlow({\r\n          instanceId: row.Instance_Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '回收成功',\r\n              type: 'success'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.tbLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    editDisabledEvent({ row, column }) {\r\n      // const $table = this.$refs.xTable\r\n      // $table.modal.message({ content: '禁止编辑', status: 'error' })\r\n    },\r\n    activeRowMethod({ row, rowIndex }) {\r\n      return !row.Id\r\n    },\r\n    handleComplete(row) {\r\n      console.log('完成', row)\r\n      this.$confirm('是否完成?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbLoading = true\r\n        ChangeMocOrderStatus({\r\n          Id: row.Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '操作成功!'\r\n            })\r\n            this.fetchData(1)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.tbLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    checkKey(key, row) {\r\n      if (!key) return true\r\n      if (key === 'monitor') {\r\n        return !!row['Instance_Id']\r\n      }\r\n      return true\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.app-container{\r\n  display: flex;\r\n  flex-direction: column;\r\n  .cs-main{\r\n    overflow: hidden;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    .cs-bottom-wapper{\r\n      display: flex;\r\n      flex-direction: column;\r\n      flex: 1;\r\n      overflow: hidden;\r\n      .tb-x{\r\n        flex: 1;\r\n        overflow: hidden;\r\n      }\r\n      .data-info{\r\n        //display: flex;\r\n        //justify-content: space-between;\r\n        //align-items: center;\r\n        text-align: right;\r\n        margin-top: 10px;\r\n      }\r\n      .pagination-container {\r\n        padding: 0;\r\n        padding-bottom: 8px;\r\n        text-align: right;\r\n        margin-top: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n.cs-box{\r\n  background-color: #FFFFFF;\r\n  padding: 16px;\r\n  border-radius: 4px;\r\n}\r\n.mb10{\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.cs-tree-x {\r\n  ::v-deep {\r\n    .el-select {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n.cs-tags{\r\n  padding: 2px 4px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.cs-red{\r\n  color: #FB6B7F;\r\n  background-color: rgba(251, 107, 127, .1);\r\n}\r\n.cs-blue{\r\n  color: #3ECC93;\r\n  background-color:rgba(62, 204, 147, .1);\r\n}\r\n.cs-green{\r\n  color: #52C41A;\r\n  background-color: rgba(82,196,26, .1);\r\n}\r\n.cs-zbtn{\r\n  pointer-events: none;\r\n  z-index: 1;\r\n}\r\n.cs-toolBar {\r\n  ::v-deep {\r\n    .vxe-button--icon.vxe-icon-custom-column{\r\n      display: none;\r\n    }\r\n\r\n    .vxe-button.type--button.is--circle {\r\n      width: 97px;\r\n      z-index: 0;\r\n      border-radius: 4px;\r\n    }\r\n\r\n    .el-form-item {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n.cs-dialog{\r\n  ::v-deep{\r\n    .el-dialog__body{\r\n      max-height: 70vh;\r\n      overflow: auto;\r\n    }\r\n  }\r\n}\r\n\r\n</style>\r\n"]}]}
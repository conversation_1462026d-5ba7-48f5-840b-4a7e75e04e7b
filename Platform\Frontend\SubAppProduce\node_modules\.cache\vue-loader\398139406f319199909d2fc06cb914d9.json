{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\component\\FactoryAddDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\component\\FactoryAddDialog.vue", "mtime": 1757468128015}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["FactoryAddDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "FactoryAddDialog.vue", "sourceRoot": "src/views/PRO/project-config/product-mfg-path/component", "sourcesContent": ["<template>\r\n  <div class=\"add-project-container\">\r\n    <!-- 搜索区域 -->\r\n    <div class=\"search-section\">\r\n      <el-form :model=\"searchForm\" inline>\r\n        <el-form-item label=\"项目编号：\">\r\n          <el-input\r\n            v-model=\"searchForm.ProjectCode\"\r\n            placeholder=\"请输入\"\r\n            clearable\r\n            style=\"width: 200px\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"项目简称：\">\r\n          <el-input\r\n            v-model=\"searchForm.ProjectAbbr\"\r\n            placeholder=\"请输入\"\r\n            clearable\r\n            style=\"width: 200px\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">搜索</el-button>\r\n          <el-button @click=\"handleReset\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n\r\n    <!-- 提示信息 -->\r\n    <div class=\"instruction-section\">\r\n      <div class=\"instruction\">\r\n        请选择工厂级配置的工序类型，添加到项目\r\n      </div>\r\n      <el-button type=\"primary\" @click=\"handleAddToList\"> 加入列表 </el-button>\r\n    </div>\r\n\r\n    <!-- 项目表格 -->\r\n    <div class=\"table-section\">\r\n      <bt-table\r\n        ref=\"projectTable\"\r\n        code=\"AddProjectList\"\r\n        :custom-table-config=\"tableConfig\"\r\n        :grid-data-handler=\"handleGridData\"\r\n        :loading=\"loading\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        @row-click=\"handleRowClick\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 底部按钮 -->\r\n    <div class=\"footer-actions\">\r\n      <el-button @click=\"handleCancel\">取消</el-button>\r\n      <el-button type=\"primary\" :disabled=\"selectedProjects.length === 0\" @click=\"handleConfirm\">\r\n        确定\r\n      </el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetProjectPageList } from '@/api/PRO/project'\r\n\r\nexport default {\r\n  name: 'AddProject',\r\n  props: {\r\n    typeId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    addLevel: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    parentId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    activeType: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    typeCode: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    isComp: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    showDirect: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      searchForm: {\r\n        ProjectCode: '',\r\n        ProjectAbbr: ''\r\n      },\r\n      selectedProjects: [],\r\n      tableConfig: {\r\n        tableColumns: [],\r\n        tableActions: [],\r\n        tableData: [],\r\n        checkbox: false,\r\n        operateOptions: {\r\n          width: 120,\r\n          align: 'center',\r\n          isShow: false\r\n        }\r\n\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchProjectList()\r\n  },\r\n  methods: {\r\n\r\n    // 获取项目列表\r\n    async fetchProjectList() {\r\n      this.loading = true\r\n      try {\r\n        const params = {\r\n          Page: this.tableConfig.currentPage,\r\n          PageSize: this.tableConfig.pageSize\r\n          // SortName: 'Create_Date',\r\n          // SortOrder: 'DESC',\r\n          // Search: '',\r\n          // ParameterJson: JSON.stringify({\r\n          //   ProjectCode: this.searchForm.ProjectCode,\r\n          //   ProjectAbbr: this.searchForm.ProjectAbbr\r\n          // })\r\n        }\r\n\r\n        const res = await GetProjectPageList(params)\r\n        if (res.IsSucceed) {\r\n          const table = res.Data.Data || []\r\n\r\n          // 为数据添加行号\r\n          table.forEach((item, index) => {\r\n            // item.IscheckMethod = undefined\r\n          })\r\n          this.tableConfig.tableData = table\r\n        } else {\r\n          this.$message.error(res.Message || '获取项目列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取项目列表失败:', error)\r\n        this.$message.error('获取项目列表失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    handleGridData(data) {\r\n      console.log(data, 3313)\r\n      return data\r\n    },\r\n    // 搜索\r\n    handleSearch() {\r\n      this.tableConfig.currentPage = 1\r\n      this.fetchProjectList()\r\n    },\r\n\r\n    // 重置\r\n    handleReset() {\r\n      this.searchForm = {\r\n        ProjectCode: '',\r\n        ProjectAbbr: ''\r\n      }\r\n      this.tableConfig.currentPage = 1\r\n      this.fetchProjectList()\r\n    },\r\n\r\n    // 选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedProjects = selection\r\n    },\r\n\r\n    // 行点击\r\n    handleRowClick(row) {\r\n      // 可以在这里添加行点击逻辑\r\n      console.log('点击行:', row)\r\n    },\r\n\r\n    // 取消\r\n    handleCancel() {\r\n      this.$emit('close')\r\n    },\r\n\r\n    // 确认选择\r\n    handleConfirm() {\r\n      if (this.selectedProjects.length === 0) {\r\n        this.$message.warning('请至少选择一个项目')\r\n        return\r\n      }\r\n\r\n      // 这里可以处理选中的项目数据\r\n      console.log('选中的项目:', this.selectedProjects)\r\n\r\n      // 触发父组件事件\r\n      this.$emit('close')\r\n      this.$emit('getTreeList')\r\n\r\n      this.$message.success(`已选择 ${this.selectedProjects.length} 个项目`)\r\n    },\r\n    handleAddToList() {\r\n      console.log('加入列表')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.add-project-container {\r\n  height:70vh;\r\n\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .search-section {\r\n    background: #fff;\r\n    border-radius: 4px;\r\n  }\r\n\r\n  .instruction-section {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n\r\n  .instruction {\r\n    background: #f0f9ff;\r\n    border: 1px solid #b3d8ff;\r\n    color: #1890ff;\r\n    padding: 12px 16px;\r\n    border-radius: 4px;\r\n    margin-bottom: 16px;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .table-section {\r\n    flex: 1;\r\n    background: #fff;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .footer-actions {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    padding: 0px 8px 0 0;\r\n    background: #fff;\r\n  }\r\n}\r\n</style>\r\n"]}]}
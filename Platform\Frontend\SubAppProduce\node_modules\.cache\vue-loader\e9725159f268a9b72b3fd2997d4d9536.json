{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\index.vue?vue&type=template&id=5f9c40e8&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\index.vue", "mtime": 1757470958558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}
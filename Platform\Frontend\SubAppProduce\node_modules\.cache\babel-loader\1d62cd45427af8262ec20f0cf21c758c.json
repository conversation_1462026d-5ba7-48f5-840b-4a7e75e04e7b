{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\ProjectAddDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\ProjectAddDialog.vue", "mtime": 1758266768046}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetProcessOfProjectList", "SyncProjectProcessFromProject", "components", "props", "sysProjectId", "type", "String", "default", "data", "btnLoading", "pLoading", "projectList", "form", "From_Sys_Project_Id", "mounted", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getProcessOfProjectList", "stop", "methods", "_this2", "_callee2", "res", "_callee2$", "_context2", "sent", "IsSucceed", "Data", "handleSubmit", "_this3", "$message", "warning", "To_Sys_Project_Id", "then", "$emit", "success", "error", "msg"], "sources": ["src/views/PRO/project-config/process-settings/component/ProjectAddDialog.vue"], "sourcesContent": ["<template>\r\n  <div class=\"form-wrapper\">\r\n    <div class=\"instruction\">请选择项目，添加所选项目的<span>所有工序</span></div>\r\n    <div>\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"82px\">\r\n        <el-form-item v-if=\"!projectList.length\" label=\"项目名称：\">\r\n          <div v-loading=\"pLoading\">暂无可同步的项目</div>\r\n        </el-form-item>\r\n        <el-form-item v-else label=\"项目名称：\">\r\n          <el-select v-model=\"form.From_Sys_Project_Id\" clearable filterable placeholder=\"请选择项目\" style=\"width: 300px\">\r\n            <el-option v-for=\"(item, index) in projectList\" :key=\"index\" :label=\"item.Short_Name\" :value=\"item.Sys_Project_Id\" :disabled=\"item.Sys_Project_Id===sysProjectId\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n    <div class=\"btn-x\">\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button v-if=\"projectList.length\" type=\"primary\" :loading=\"btnLoading\" :disabled=\"!form.From_Sys_Project_Id\" @click=\"handleSubmit\">确 定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetProcessOfProjectList, SyncProjectProcessFromProject } from '@/api/PRO/technology-lib'\r\nexport default {\r\n  components: {\r\n  },\r\n  props: {\r\n    sysProjectId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      pLoading: false,\r\n      projectList: [],\r\n      form: {\r\n        From_Sys_Project_Id: ''\r\n      }\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.getProcessOfProjectList()\r\n  },\r\n  methods: {\r\n    async getProcessOfProjectList() {\r\n      this.pLoading = true\r\n      const res = await GetProcessOfProjectList({ })\r\n      if (res.IsSucceed) {\r\n        this.projectList = res.Data || []\r\n      }\r\n      this.pLoading = false\r\n    },\r\n    handleSubmit() {\r\n      if (this.form.From_Sys_Project_Id === '') return this.$message.warning('请选择项目')\r\n      this.btnLoading = true\r\n      SyncProjectProcessFromProject({\r\n        From_Sys_Project_Id: this.form.From_Sys_Project_Id,\r\n        To_Sys_Project_Id: this.sysProjectId\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$emit('refresh')\r\n          this.$emit('close')\r\n          this.$message.success('同步成功！')\r\n        } else {\r\n          this.$message.error(res.msg)\r\n        }\r\n        this.btnLoading = false\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n  .form-wrapper {\r\n    display: flex;\r\n    flex-direction: column;\r\n    overflow: hidden;\r\n    max-height: 70vh;\r\n\r\n    .btn-x {\r\n      padding-top: 16px;\r\n      text-align: right;\r\n    }\r\n    .instruction {\r\n    background: #f0f9ff;\r\n    border: 1px solid #b3d8ff;\r\n    color: #1890ff;\r\n    padding: 12px 16px;\r\n    border-radius: 4px;\r\n    margin-bottom: 16px;\r\n    font-weight: 500;\r\n  }\r\n  }\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,SAAAA,uBAAA,EAAAC,6BAAA;AACA;EACAC,UAAA,GACA;EACAC,KAAA;IACAC,YAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,QAAA;MACAC,WAAA;MACAC,IAAA;QACAC,mBAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACAT,KAAA,CAAAU,uBAAA;UAAA;UAAA;YAAA,OAAAH,QAAA,CAAAI,IAAA;QAAA;MAAA,GAAAP,OAAA;IAAA;EACA;EACAQ,OAAA;IACAF,uBAAA,WAAAA,wBAAA;MAAA,IAAAG,MAAA;MAAA,OAAAZ,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAW,SAAA;QAAA,IAAAC,GAAA;QAAA,OAAAb,mBAAA,GAAAG,IAAA,UAAAW,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAT,IAAA,GAAAS,SAAA,CAAAR,IAAA;YAAA;cACAI,MAAA,CAAAlB,QAAA;cAAAsB,SAAA,CAAAR,IAAA;cAAA,OACAxB,uBAAA;YAAA;cAAA8B,GAAA,GAAAE,SAAA,CAAAC,IAAA;cACA,IAAAH,GAAA,CAAAI,SAAA;gBACAN,MAAA,CAAAjB,WAAA,GAAAmB,GAAA,CAAAK,IAAA;cACA;cACAP,MAAA,CAAAlB,QAAA;YAAA;YAAA;cAAA,OAAAsB,SAAA,CAAAN,IAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IACA;IACAO,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,SAAAzB,IAAA,CAAAC,mBAAA,qBAAAyB,QAAA,CAAAC,OAAA;MACA,KAAA9B,UAAA;MACAR,6BAAA;QACAY,mBAAA,OAAAD,IAAA,CAAAC,mBAAA;QACA2B,iBAAA,OAAApC;MACA,GAAAqC,IAAA,WAAAX,GAAA;QACA,IAAAA,GAAA,CAAAI,SAAA;UACAG,MAAA,CAAAK,KAAA;UACAL,MAAA,CAAAK,KAAA;UACAL,MAAA,CAAAC,QAAA,CAAAK,OAAA;QACA;UACAN,MAAA,CAAAC,QAAA,CAAAM,KAAA,CAAAd,GAAA,CAAAe,GAAA;QACA;QACAR,MAAA,CAAA5B,UAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
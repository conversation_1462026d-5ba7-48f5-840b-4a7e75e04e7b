{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\recognitionConfig.vue?vue&type=template&id=6c8b085d&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\recognitionConfig.vue", "mtime": 1745557754679}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImZvcm0td3JhcHBlciI+CiAgPGRpdiBjbGFzcz0iZm9ybS14Ij4KICAgIDxlbC1mb3JtIHJlZj0iZm9ybSIgOm1vZGVsPSJmb3JtIiBsYWJlbC13aWR0aD0iMTIwcHgiPgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmmK/lkKblkK/nlKgiIHByb3A9ImVuYWJsZSI+CiAgICAgICAgPGVsLXJhZGlvLWdyb3VwIHYtbW9kZWw9ImZvcm0uZW5hYmxlIj4KICAgICAgICAgIDxlbC1yYWRpbyA6bGFiZWw9ImZhbHNlIj7lkKY8L2VsLXJhZGlvPgogICAgICAgICAgPGVsLXJhZGlvIDpsYWJlbD0idHJ1ZSI+5pivPC9lbC1yYWRpbz4KICAgICAgICA8L2VsLXJhZGlvLWdyb3VwPgogICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgPHRlbXBsYXRlIHYtaWY9ImZvcm0uZW5hYmxlIj4KICAgICAgICA8ZWwtZm9ybS1pdGVtCiAgICAgICAgICB2LWZvcj0iKGVsZW1lbnQsaW5kZXgpIGluIGxpc3QiCiAgICAgICAgICA6a2V5PSJpbmRleCIKICAgICAgICAgIDpzaG93LW1lc3NhZ2U9ImZhbHNlIgogICAgICAgICAgOmxhYmVsPSJlbGVtZW50LkNvbXBfVHlwZV9OYW1lIgogICAgICAgICAgcHJvcD0ibWFpblBhcnQiCiAgICAgICAgPgogICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgIHYtbW9kZWwudHJpbT0iZm9ybVsnaXRlbScraW5kZXhdIgogICAgICAgICAgICA6cGxhY2Vob2xkZXI9ImDor7fovpPlhaXvvIjlpJrkuKrkvb/nlKgnJHtzcGxpdFN5bWJvbH0n6ZqU5byA77yJ77yM5Y2V5Liq6YWN572u5LiN6LaF6L+HMTDkuKrlrZfnrKZgIgogICAgICAgICAgICBjbGVhcmFibGUKICAgICAgICAgICAgQGJsdXI9Im1haW5CbHVyIgogICAgICAgICAgLz4KICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgPC90ZW1wbGF0ZT4KCiAgICA8L2VsLWZvcm0+CiAgPC9kaXY+CiAgPGRpdiBjbGFzcz0iYnRuLXgiPgogICAgPGVsLWJ1dHRvbiBAY2xpY2s9IiRlbWl0KCdjbG9zZScpIj7lj5Yg5raIPC9lbC1idXR0b24+CiAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIDpsb2FkaW5nPSJidG5Mb2FkaW5nIiBAY2xpY2s9ImhhbmRsZVN1Ym1pdCI+56GuIOWumjwvZWwtYnV0dG9uPgogIDwvZGl2Pgo8L2Rpdj4K"}, null]}
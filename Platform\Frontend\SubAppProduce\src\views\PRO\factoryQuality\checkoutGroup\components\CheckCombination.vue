<template>
  <div style="height: calc(100vh - 300px)">
    <vxe-table
      v-loading="tbLoading"
      :empty-render="{name: 'NotData'}"
      show-header-overflow
      element-loading-spinner="el-icon-loading"
      element-loading-text="拼命加载中"
      empty-text="暂无数据"
      height="100%"
      :data="tbData"
      stripe
      resizable
      :auto-resize="true"
      class="cs-vxe-table"
      :tooltip-config="{ enterable: true }"
    >
      <!-- <vxe-column fixed="left" type="checkbox" width="60" /> -->
      <vxe-column
        v-for="(item, index) in columns"
        :key="index"
        :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
        show-overflow="tooltip"
        sortable
        :align="item.Align"
        :field="item.Code"
        :title="item.Display_Name"
      >
        <template #default="{ row }">
          <span>{{ row[item.Code] || '-' }}</span>
        </template>
      </vxe-column>
      <vxe-column fixed="right" title="操作" width="200" align="center" show-overflow>
        <template #default="{ row }">
          <el-button type="text" @click="editEvent(row)">编辑</el-button>
          <el-divider direction="vertical" />
          <el-button type="text" @click="removeEvent(row)">删除</el-button>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<script>
import { GetGridByCode } from '@/api/sys'
import { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'
import { QualityList } from '@/api/PRO/factorycheck'
import { DelQualityList } from '@/api/PRO/factorycheck'
import { timeFormat } from '@/filters'
export default {
  props: {
    checkType: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      columns: null,
      tbLoading: false,
      TypeId: '',
      typeOption: '',
      tbData: []

    }
  },
  watch: {
    checkType: {
      handler(newName, oldName) {
        this.checkType = newName
        this.getQualityList()
      },
      deep: true
    }
  },
  mounted() {
    this.getQualityList()
    this.getTypeList()
  },
  methods: {
    async getTypeList() {
      let res = null
      let data = null
      res = await GetFactoryProfessionalByCode({
        factoryId: localStorage.getItem('CurReferenceId')
      })
      data = res.Data
      if (res.IsSucceed) {
        this.typeOption = Object.freeze(data)
        console.log(this.typeOption)
        if (this.typeOption.length > 0) {
          this.TypeId = this.typeOption[0]?.Id
          this.fetchData()
        }
      } else {
        this.$message({
          message: res.Message,
          type: 'error'
        })
      }
    },
    async fetchData() {
      await this.getTableConfig('Check_item_combination')
    //   this.tbLoading = true;
    },
    // 获取列表
    getTableConfig(code) {
      GetGridByCode({ code: code + ',' + this.typeOption.find((i) => i.Id === this.TypeId).Code }).then((res) => {
        const { IsSucceed, Data, Message } = res
        if (IsSucceed) {
          if (!Data) {
            this.$message.error('当前专业没有配置相对应表格')
            this.tbLoading = true
            return
          }
          this.tbLoading = false
          const list = Data.ColumnList || []
          this.columns = list.filter((v) => v.Is_Display).map((item) => {
            if (item.Code === 'CheckName') {
              item.fixed = 'left'
            }
            return item
          })
          console.log(this.columns)
        } else {
          this.$message({
            message: Message,
            type: 'error'
          })
        }
      })
    },
    // 获取检查项组合列表
    getQualityList() {
      this.tbLoading = true
      QualityList({ check_object_id: this.checkType.Id, Bom_Level: this.checkType.Code }).then((res) => {
        if (res.IsSucceed) {
          this.tbData = res.Data.map((v) => {
            switch (v.Check_Type) {
              case 1 : v.Check_Type = '质量'; break
              case 2 : v.Check_Type = '探伤'; break
              case -1 : v.Check_Type = '质量、探伤'; break
              default: v.Check_Type = ''
            }
            v.Create_Date = timeFormat(v.Create_Date, '{y}-{m}-{d} {h}:{i}:{s}')
            return v
          })
          this.tbLoading = false
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
          this.tbLoading = false
        }
      })
    },
    // 删除单个检查项组合
    removeEvent(row) {
      console.log(row)
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          DelQualityList({ id: row.Id }).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getQualityList()
            } else {
              this.$message({
                type: 'error',
                message: res.Message
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 编辑每行信息
    editEvent(row) {
      // 获取每行内容
      console.log('row', row)
      this.$emit('CombinationEdit', row)
    }
  }
}
</script>

<style scoped></style>

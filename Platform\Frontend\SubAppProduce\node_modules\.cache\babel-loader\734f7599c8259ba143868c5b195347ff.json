{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\ship\\component\\Import.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\ship\\component\\Import.vue", "mtime": 1758677034219}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["UploadExcel", "ImportBoat", "BoatDataTemplate", "combineURL", "GetFactoryList", "name", "components", "data", "options", "btnLoading", "proOption", "fileList", "mounted", "methods", "getFactory", "_this", "then", "res", "Data", "handleDownload", "_this2", "IsSucceed", "window", "open", "$baseUrl", "$message", "message", "Message", "type", "beforeUpload", "file", "_this3", "console", "log", "fileFormData", "FormData", "append", "$emit", "handleSubmit", "$refs", "upload", "handleChange", "length", "shift"], "sources": ["src/views/PRO/basic-information/ship/component/Import.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"box\">\r\n      <span>1.下载模板</span>\r\n      <el-button size=\"large\" @click=\"handleDownload\">\r\n        <svg-icon icon-class=\"document_form_icon\" />\r\n        船舶模板\r\n      </el-button>\r\n    </div>\r\n\r\n    <div class=\"upload-box\">\r\n      <span style=\"margin-bottom: 20px;display: inline-block\">\r\n        2. 完善内容，重新上传。\r\n      </span>\r\n      <upload-excel ref=\"upload\" :before-upload=\"beforeUpload\" :limit=\"2\" :on-change=\"handleChange\" :file-list = \"fileList\"/>\r\n    </div>\r\n\r\n    <!--    <div class=\"box\">\r\n      <span style=\"display: inline-block;\">3. 选择导入工厂</span>\r\n      <el-select v-model=\"ProjectId\" filterable clearable style=\"width: 70%\" placeholder=\"请选择\">\r\n        <el-option\r\n          v-for=\"item in proOption\"\r\n          :key=\"item.Id\"\r\n          :label=\"item.Name\"\r\n          :value=\"item.Id\"\r\n        />\r\n      </el-select>\r\n    </div>-->\r\n\r\n    <footer class=\"cs-footer\">\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\r\n    </footer>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UploadExcel from '@/components/UploadExcel'\r\nimport { ImportBoat, BoatDataTemplate } from '@/api/PRO/car'\r\nimport { combineURL } from '@/utils'\r\nimport { GetFactoryList } from '@/api/PRO/pro-schedules'\r\n\r\nexport default {\r\n  name: 'Import',\r\n  components: {\r\n    UploadExcel\r\n  },\r\n  data() {\r\n    return {\r\n      options: [],\r\n      btnLoading: false,\r\n      // ProjectId: '',\r\n      proOption: [],\r\n      fileList:[]\r\n    }\r\n  },\r\n  mounted() {\r\n\r\n  },\r\n  methods: {\r\n    getFactory() {\r\n      GetFactoryList({}).then(res => {\r\n        this.proOption = res?.Data\r\n      })\r\n    },\r\n    handleDownload() {\r\n      BoatDataTemplate({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    beforeUpload(file) {\r\n      console.log(file)\r\n      const fileFormData = new FormData()\r\n      // fileFormData.append('factoryId', this.ProjectId)\r\n      fileFormData.append('files', file)\r\n      this.btnLoading = true\r\n      ImportBoat(fileFormData).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.btnLoading = false\r\n          this.$message({\r\n            message: '导入成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('refresh')\r\n          this.$emit('close')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.btnLoading = false\r\n        }\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      /*      if (!this.ProjectId) {\r\n        this.$message({\r\n          message: '请选择工厂',\r\n          type: 'info'\r\n        })\r\n        return\r\n      }*/\r\n      console.log(this.fileList)\r\n      this.$refs.upload.handleSubmit()\r\n    },\r\n    handleChange(file, fileList) {\r\n      console.log(file,fileList)\r\n      // this.fileList = fileList.slice(-1);\r\n      this.fileList = fileList;\r\n      console.log(this.fileList)\r\n      if(fileList.length>1) {\r\n        this.fileList.shift()\r\n      }\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.box{\r\n  border: 1px dashed #D9DBE2;\r\n  padding: 0 16px;\r\n  display: flex;\r\n  height: 64px;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background-color: #F7F8F9;\r\n  & ~ .box{\r\n    margin-top: 20px;\r\n  }\r\n\r\n}\r\n.upload-box{\r\n  background-color:  #F7F8F9;\r\n  border: 1px dashed #D9DBE2;\r\n  margin-top: 16px;\r\n  padding: 16px;\r\n}\r\n.cs-footer{\r\n  margin-top: 10px;\r\n  text-align: center;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA,OAAAA,WAAA;AACA,SAAAC,UAAA,EAAAC,gBAAA;AACA,SAAAC,UAAA;AACA,SAAAC,cAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAN,WAAA,EAAAA;EACA;EACAO,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;MACA;MACAC,SAAA;MACAC,QAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA,GAEA;EACAC,OAAA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,KAAA;MACAX,cAAA,KAAAY,IAAA,WAAAC,GAAA;QACAF,KAAA,CAAAL,SAAA,GAAAO,GAAA,aAAAA,GAAA,uBAAAA,GAAA,CAAAC,IAAA;MACA;IACA;IACAC,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACAlB,gBAAA,KAAAc,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAI,SAAA;UACAC,MAAA,CAAAC,IAAA,CAAApB,UAAA,CAAAiB,MAAA,CAAAI,QAAA,EAAAP,GAAA,CAAAC,IAAA;QACA;UACAE,MAAA,CAAAK,QAAA;YACAC,OAAA,EAAAT,GAAA,CAAAU,OAAA;YACAC,IAAA;UACA;QACA;MACA;IACA;IACAC,YAAA,WAAAA,aAAAC,IAAA;MAAA,IAAAC,MAAA;MACAC,OAAA,CAAAC,GAAA,CAAAH,IAAA;MACA,IAAAI,YAAA,OAAAC,QAAA;MACA;MACAD,YAAA,CAAAE,MAAA,UAAAN,IAAA;MACA,KAAArB,UAAA;MACAR,UAAA,CAAAiC,YAAA,EAAAlB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAI,SAAA;UACAU,MAAA,CAAAtB,UAAA;UACAsB,MAAA,CAAAN,QAAA;YACAC,OAAA;YACAE,IAAA;UACA;UACAG,MAAA,CAAAM,KAAA;UACAN,MAAA,CAAAM,KAAA;QACA;UACAN,MAAA,CAAAN,QAAA;YACAC,OAAA,EAAAT,GAAA,CAAAU,OAAA;YACAC,IAAA;UACA;UACAG,MAAA,CAAAtB,UAAA;QACA;MACA;IACA;IACA6B,YAAA,WAAAA,aAAA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;MACAN,OAAA,CAAAC,GAAA,MAAAtB,QAAA;MACA,KAAA4B,KAAA,CAAAC,MAAA,CAAAF,YAAA;IACA;IACAG,YAAA,WAAAA,aAAAX,IAAA,EAAAnB,QAAA;MACAqB,OAAA,CAAAC,GAAA,CAAAH,IAAA,EAAAnB,QAAA;MACA;MACA,KAAAA,QAAA,GAAAA,QAAA;MACAqB,OAAA,CAAAC,GAAA,MAAAtB,QAAA;MACA,IAAAA,QAAA,CAAA+B,MAAA;QACA,KAAA/B,QAAA,CAAAgC,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
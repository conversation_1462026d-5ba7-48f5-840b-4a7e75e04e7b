<template>
  <el-card class="card">
    <div class="content">
      <div class="header">
        <span class="project-name">{{ curProject && curProject.Short_Name }}</span>
        <span>
          <i class="el-icon-time" />
          <span class="label">总工期</span>
          <span class="value">{{ totalProjectDuration }}天</span>
        </span>
        <span>
          <i class="el-icon-time" />
          <span class="label">剩余工期</span>
          <span class="value">{{ remainingDuration }}天</span>
        </span>
      </div>
      <div class="summary">
        <div v-for="(item) in nodeList" :key="item.Id" class="block" @click="toDetail(item)">
          <div class="top" :style="{background: `linear-gradient( 135deg, ${item.color}33 0%, #D6EAFF33 100%)`}">
            <div class="block-name-wrap">
              <img :src="item.icon" class="icon">
              <div class="block-name">{{ item.name }}</div>
            </div>
            <div style="flex:1;height: 100%;display: flex;flex-direction: column;justify-content: center;margin-top: 8px">
              <div class="progress-container">
                <div class="progress-bar">
                  <div class="progress-bg">
                    <div
                      class="progress-fill"
                      :style="{
                        width: parseFloat(item.finishPercent) > 100 ? '100%' : item.finishPercent,
                        backgroundColor: item.color
                      }"
                    >
                      <div
                        class="progress-dot"
                        :style="{
                          backgroundColor: item.color
                        }"
                      />
                    </div>
                    <div
                      class="progress-data"
                      :style="{
                        left: parseFloat(item.finishPercent) > 100 ? '100%' : item.finishPercent,
                        color: item.color,
                        transform: parseFloat(item.finishPercent) > 80 ? 'translateX(-100%)' : 'translateX(0%)'
                      }"
                    >
                      <span class="finish-value">完成:{{ item.finishSummary }}t</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="plan-percent">
                <div class="plan">
                  计划：{{ item.planSummary }}t
                </div>
                <div :style="{color:item.color,fontWeight:'bold'}">
                  {{ item.finishPercent }}
                </div>
              </div>
            </div>
          </div>
          <div class="bottom">
            <span>总：{{ item.duration }}</span>
            <span style="margin-left: 14px"> <i class="el-icon-date" /> {{ item.beginDate }} - {{ item.endDate }}</span>
          </div>
        </div>
      </div>
      <el-form ref="form" inline style="display: flex;align-items: center">
        <el-form-item>
          <el-upload
            :action="$baseUrl + 'PRO/ControlPlan/ImportTotalPlan'"
            :show-file-list="false"
            :on-success="uploadSuccess"
            :headers="headers"
            :data="uploadData"
          >
            <el-button type="primary">导入总控计划</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :loading="exportLoading" @click="exportFile()">导出总控计划</el-button>
        </el-form-item>
        <el-form-item style="margin-right: auto">
          <el-button type="primary" :loading="updateLoading" @click="updateData()">手动更新计划</el-button>
        </el-form-item>
        <el-form-item label="区域">
          <SelectArea v-model="queryModel.Areas" :project-id="curProject.Sys_Project_Id" />
        </el-form-item>
        <el-form-item label="显示范围">
          <el-select v-model="queryModel.Range" multiple clearable collapse-tags>
            <el-option v-for="item in nodeList" :key="item.code" :label="`过滤${item.name}完成`" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">搜索</el-button>
        </el-form-item>
      </el-form>
      <bt-table
        v-if="nodeList && nodeList.length"
        class="bt-table"
        :config="config"
        :header-cell-style="cellStyle"
      >
        <template #actions="{row}">
          <el-button type="text" @click="openDialog(row)">编辑</el-button>
        </template>
      </bt-table>
    </div>

    <!-- 编辑计划弹窗 -->
    <el-dialog
      v-dialogDrag
      class="plm-custom-dialog"
      title="编辑计划"
      :visible.sync="dialogVisible"
      width="1200px"
      :before-close="handleClose"
    >
      <el-form ref="planForm" :model="planForm" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="单体/分区">
              <span>{{ currentRow.FullAreaName }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 深化设计 -->
        <el-row>
          <el-col :span="4">
            <div class="phase-label">{{ nodeList[0] && nodeList[0].name }}</div>
          </el-col>
          <el-col :span="10">
            <el-form-item label="计划时间">
              <el-date-picker
                v-model="planForm.Deepen_Date_Range"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 100%"
                @change="(val) => handleDateRangeChange('Deepen', val)"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="总工程量(t)">
              <el-input v-model="planForm.Deepen_Engineer_Quantity" type="number" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="总工期">
              <span>{{ planForm.Deepen_Duration }}天</span>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 材料采购 -->
        <el-row>
          <el-col :span="4">
            <div class="phase-label">{{ nodeList[1] && nodeList[1].name }}</div>
          </el-col>
          <el-col :span="10">
            <el-form-item label="计划时间">
              <el-date-picker
                v-model="planForm.Purchase_Date_Range"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 100%"
                @change="(val) => handleDateRangeChange('Purchase', val)"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="总工程量(t)">
              <el-input v-model="planForm.Purchase_Engineer_Quantity" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="总工期">
              <span>{{ planForm.Purchase_Duration }}天</span>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 生产加工 -->
        <el-row>
          <el-col :span="4">
            <div class="phase-label">{{ nodeList[2] && nodeList[2].name }}</div>
          </el-col>
          <el-col :span="10">
            <el-form-item label="计划时间">
              <el-date-picker
                v-model="planForm.Product_Date_Range"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 100%"
                @change="(val) => handleDateRangeChange('Product', val)"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="总工程量(t)">
              <el-input v-model="planForm.Product_Engineer_Quantity" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="总工期">
              <span>{{ planForm.Product_Duration }}天</span>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 施工安装 -->
        <el-row>
          <el-col :span="4">
            <div class="phase-label">{{ nodeList[3] && nodeList[3].name }}</div>
          </el-col>
          <el-col :span="10">
            <el-form-item label="计划时间">
              <el-date-picker
                v-model="planForm.Install_Date_Range"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 100%"
                @change="(val) => handleDateRangeChange('Install', val)"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="总工程量(t)">
              <el-input v-model="planForm.Install_Engineer_Quantity" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="总工期">
              <span>{{ planForm.Install_Duration }}天</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="saveAreaPlan">确定</el-button>
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import {
  ExportTotalControlPlan, GenerateProjectPlanBusinessData,
  GetConfigs,
  GetMonomerListByProjectId,
  GetTotalControlPlanEntity,
  GetTotalControlPlanList,
  SaveTotalControlPlanEntity
} from '@/api/plm/projects'
import SelectArea from '@/components/Select/SelectArea/index.vue'
import moment from 'moment'
import { combineURL } from '@/utils'
import { getToken } from '@/utils/auth'
import { isRouteNameExists } from '@/utils/router'

export default {
  name: 'OverallControlPlanContent',
  components: { SelectArea },
  props: {
    curProject: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      queryModel: {
        Areas: [],
        Range: []
      },
      // 总工期和剩余工期
      totalProjectDuration: 0,
      remainingDuration: 0,
      config: {
        pageSizeOptions: [10, 20, 50, 100],
        pageSize: 20,
        total: 0,
        loading: false,
        currentPage: 1,
        tableColumns: [],
        tableData: [],
        keyField: 'Area_Id'
      },
      monomerList: [],
      nodeList: [],
      // 弹窗相关数据
      dialogVisible: false,
      currentRow: {},
      planForm: {
        Area_Id: '',
        Deepen_Begin_Date: '',
        Deepen_End_Date: '',
        Deepen_Date_Range: [],
        Deepen_Duration: 0,
        Deepen_Engineer_Quantity: 0,
        Purchase_Begin_Date: '',
        Purchase_End_Date: '',
        Purchase_Date_Range: [],
        Purchase_Duration: 0,
        Purchase_Engineer_Quantity: 0,
        Product_Begin_Date: '',
        Product_End_Date: '',
        Product_Date_Range: [],
        Product_Duration: 0,
        Product_Engineer_Quantity: 0,
        Install_Begin_Date: '',
        Install_End_Date: '',
        Install_Date_Range: [],
        Install_Duration: 0,
        Install_Engineer_Quantity: 0
      },
      cloneTableData: [],
      exportLoading: false,
      updateLoading: false,
      saveLoading: false,
      headers: {
        Authorization: getToken(),
        Last_Working_Object_Id: localStorage.getItem('Last_Working_Object_Id')
      }
    }
  },
  computed: {
    uploadData() {
      return { data: JSON.stringify({ ProjectId: this.curProject?.Sys_Project_Id, CompanyId: localStorage.getItem('CurReferenceId') }) }
    }
  },
  watch: {
    curProject: {
      deep: true,
      handler: function() {
        if (this.curProject && this.curProject.Sys_Project_Id) {
          this.queryModel.Areas = []
          this.getMonomerList()
          this.getTableData()
        }
      }
    }
  },
  async created() {
    await this.getConfig()
    if (this.curProject && this.curProject.Sys_Project_Id) {
      this.getMonomerList()
      this.getTableData()
    }
  },
  methods: {
    cellStyle({ column }) {
      const style = {}
      if (column.field && column.field.startsWith('Deepen')) {
        style.backgroundColor = `rgba(41, 141, 255, 0.1)!important`
      }
      if (column.field && column.field.startsWith('Purchase')) {
        style.backgroundColor = `rgba(189, 109, 246, 0.1)!important`
      }
      if (column.field && column.field.startsWith('Product')) {
        style.backgroundColor = `rgba(0, 195, 97, 0.1)!important`
      }
      if (column.field && column.field.startsWith('Install')) {
        style.backgroundColor = `rgba(0, 177, 191, 0.1)!important`
      }
      return style
    },
    async getConfig() {
      const res = await GetConfigs({
        CompanyId: localStorage.getItem('CurReferenceId')
      })
      this.nodeList = res.Data.map(item => {
        return {
          ...item,
          name: item.Plan_Name,
          code: ['Deepen', 'Purchase', 'Product', 'Install'][item.Plan_Type - 1],
          icon: require(`@/assets/PLM/plan_summary_${item.Plan_Type}.png`),
          color: ['#298DFF', '#BD6DF6', '#00C361', '#00B1BF'][item.Plan_Type - 1],
          route: ['PRODeepenPlanTrackCom', 'PROPurchasePlanTrackCom', 'PROProducePlanTrackCom', 'PROConstructionPlanTrackCom'][item.Plan_Type - 1],
          visible: true
        }
      })
      this.getRenderColumns()
    },
    getRenderColumns() {
      const columns = this.nodeList.filter(i => i.visible).map(item => {
        return {
          width: 150,
          label: item.name,
          otherOptions: { align: 'center', fixed: '' },
          key: item.code,
          children: [
            { key: item.code + '_Begin_Date', width: '160',
              label: '开始时间', otherOptions: { align: 'center' },
              render: (row) => {
                if (!row[item.code + '_Begin_Date']) return '-'
                const value = moment(row[item.code + '_Begin_Date']).format('YYYY-MM-DD')
                return this.$createElement('div', {}, value)
              }
            },
            { key: item.code + '_End_Date', width: '160',
              label: '结束时间', otherOptions: { align: 'center' },
              render: (row) => {
                if (!row[item.code + '_End_Date']) return '-'
                const value = moment(row[item.code + '_End_Date']).format('YYYY-MM-DD')
                return this.$createElement('div', { }, value)
              }
            },
            { key: item.code + '_Duration', width: '160',
              label: '总工期(天)', otherOptions: { align: 'center' }
            },
            { key: item.code + '_Engineer_Quantity', width: '160',
              label: '计划工程量(t)', otherOptions: { align: 'center' }
            },
            { key: item.code + '_Finish_Quantity', width: '160',
              label: '实际量(t)', otherOptions: { align: 'center' }
            },
            { key: item.code + '_Finish_State', width: '160',
              label: '完成状态', otherOptions: { align: 'center' },
              render: (row) => {
                const value = row[item.code + '_Finish_State']
                return this.$createElement('div', {
                  style: {
                    color: value === '已完成' ? '#67C23A' : value === '未完成' ? '#F56C6C' : ''
                  }
                }, value)
              }
            }
          ]
        }
      })
      this.config.tableColumns = [{
        width: 150, label: '单体/分区', key: 'FullAreaName', otherOptions: { align: 'left', fixed: 'left' }
      }, ...columns]
    },
    search() {
      // 过滤渲染列
      this.nodeList.forEach(i => i.visible = true)
      if (this.queryModel.Range && this.queryModel.Range.length > 0) {
        this.queryModel.Range.forEach(i => {
          this.nodeList.forEach(j => {
            if (i === j.code) {
              j.visible = false
            }
          })
        })
      }
      this.getRenderColumns()
      // 过滤区域
      if (this.queryModel.Areas && this.queryModel.Areas.length > 0) {
        this.config.tableData = this.cloneTableData.filter(item => {
          return this.queryModel.Areas.includes(item.Area_Id)
        })
      } else {
        this.config.tableData = JSON.parse(JSON.stringify(this.cloneTableData))
      }
      this.handleTableData()
    },
    getTableData() {
      if (!this.curProject || !this.curProject.Sys_Project_Id) return
      this.config.loading = true
      GetTotalControlPlanList({
        ProjectId: this.curProject.Sys_Project_Id,
        CompanyId: localStorage.getItem('CurReferenceId')
      }).then(res => {
        this.config.tableData = res.Data.map(item => {
          item.id = item.Area_Id
          return item
        })
        this.cloneTableData = JSON.parse(JSON.stringify(res.Data))
        this.handleTableData()
      }).finally(() => {
        this.config.loading = false
      })
    },
    handleTableData() {
      // 收集所有日期用于计算总工期和剩余工期
      const allBeginDates = []
      const allEndDates = []
      this.totalProjectDuration = 0
      this.remainingDuration = 0
      this.nodeList.forEach(item => {
        const beginDates = this.config.tableData
          .map(row => row[item.code + '_Begin_Date'])
          .filter(date => date && date.trim() !== '')
        const endDates = this.config.tableData
          .map(row => row[item.code + '_End_Date'])
          .filter(date => date && date.trim() !== '')

        allBeginDates.push(...beginDates)
        allEndDates.push(...endDates)
      })
      // 计算项目总工期：最晚日期 - 最早日期 + 1
      if (allBeginDates.length > 0 && allEndDates.length > 0) {
        const earliestDate = allBeginDates.sort()[0]
        const latestDate = allEndDates.sort((a, b) => new Date(b) - new Date(a))[0]

        if (earliestDate && latestDate) {
          const begin = new Date(earliestDate)
          const end = new Date(latestDate)
          this.totalProjectDuration = Math.floor((end - begin) / (1000 * 60 * 60 * 24)) + 1
          // 计算剩余工期：最晚日期 - 当前日期 + 1
          const today = new Date()
          const remainingDays = Math.floor((end - today) / (1000 * 60 * 60 * 24)) + 1
          this.remainingDuration = remainingDays > 0 ? remainingDays : 0
        }
      }

      this.nodeList = this.nodeList.map(item => {
        // 计算计划工程量汇总
        const planSummary = this.config.tableData.reduce((sum, row) => {
          const value = parseFloat(row[item.code + '_Engineer_Quantity']) || 0
          return sum + value
        }, 0)

        // 计算实际完成量汇总
        const finishSummary = this.config.tableData.reduce((sum, row) => {
          const value = parseFloat(row[item.code + '_Finish_Quantity']) || 0
          return sum + value
        }, 0)

        // 计算完成百分比
        const finishPercent = planSummary > 0 ? (finishSummary / planSummary * 100) : 0

        // 获取最早开始日期（排除空值）
        const beginDates = this.config.tableData
          .map(row => row[item.code + '_Begin_Date'])
          .filter(date => date && date.trim() !== '')
          .sort()
        const beginDate = beginDates.length > 0 ? moment(beginDates[0]).format('YYYY/MM/DD') : ''

        // 获取最晚结束日期（排除空值）
        const endDates = this.config.tableData
          .map(row => row[item.code + '_End_Date'])
          .filter(date => date && date.trim() !== '')
          .sort((a, b) => new Date(b) - new Date(a))
        const endDate = endDates.length > 0 ? moment(endDates[0]).format('YYYY/MM/DD') : ''

        // 计算总工期
        let duration = 0
        if (beginDate && endDate) {
          const begin = new Date(beginDate)
          const end = new Date(endDate)
          duration = Math.floor((end - begin) / (1000 * 60 * 60 * 24)) + 1
        }

        return {
          ...item,
          planSummary: planSummary.toFixed(2),
          finishSummary: finishSummary.toFixed(2),
          finishPercent: finishPercent.toFixed(2) + '%',
          beginDate: beginDate,
          endDate: endDate,
          duration: duration > 0 ? duration + '天' : ''
        }
      })
    },
    async getMonomerList() {
      if (!this.curProject || !this.curProject.Sys_Project_Id) return
      const res = await GetMonomerListByProjectId({
        projectId: this.curProject.Sys_Project_Id
      })
      this.monomerList = res.Data
    },
    openDialog(row) {
      this.currentRow = row
      this.dialogVisible = true
      GetTotalControlPlanEntity({
        Area_Id: row.Area_Id
      }).then(res => {
        if (res.Data) {
          this.planForm = { ...res.Data }
          // 将开始和结束日期组合成日期范围
          this.$set(this.planForm, 'Deepen_Date_Range', [res.Data.Deepen_Begin_Date || '', res.Data.Deepen_End_Date || ''])
          this.$set(this.planForm, 'Purchase_Date_Range', [res.Data.Purchase_Begin_Date || '', res.Data.Purchase_End_Date || ''])
          this.$set(this.planForm, 'Product_Date_Range', [res.Data.Product_Begin_Date || '', res.Data.Product_End_Date || ''])
          this.$set(this.planForm, 'Install_Date_Range', [res.Data.Install_Begin_Date || '', res.Data.Install_End_Date || ''])
          // 重新计算各阶段工期
          this.calculateDuration('Deepen')
          this.calculateDuration('Purchase')
          this.calculateDuration('Product')
          this.calculateDuration('Install')
        }
      })
    },
    saveAreaPlan() {
      this.saveLoading = true
      SaveTotalControlPlanEntity({
        ...this.planForm,
        Area_Id: this.currentRow.Area_Id,
        Company_Id: localStorage.getItem('CurReferenceId'),
        Project_Id: this.curProject.Sys_Project_Id
      }).then(res => {
        this.$message.success('保存成功')
        this.dialogVisible = false
        this.getTableData()
      }).finally(() => {
        this.saveLoading = false
      })
    },
    handleDateRangeChange(type, dateRange) {
      if (dateRange && dateRange.length === 2) {
        this.$set(this.planForm, `${type}_Begin_Date`, dateRange[0])
        this.$set(this.planForm, `${type}_End_Date`, dateRange[1])
        this.$set(this.planForm, `${type}_Date_Range`, dateRange)
        this.calculateDuration(type)
      } else {
        this.$set(this.planForm, `${type}_Begin_Date`, '')
        this.$set(this.planForm, `${type}_End_Date`, '')
        this.$set(this.planForm, `${type}_Date_Range`, [])
        this.$set(this.planForm, `${type}_Duration`, 0)
      }
      // 强制更新视图
      this.$forceUpdate()
    },
    calculateDuration(type) {
      const beginDate = this.planForm[`${type}_Begin_Date`]
      const endDate = this.planForm[`${type}_End_Date`]
      if (beginDate && endDate) {
        const begin = new Date(beginDate)
        const end = new Date(endDate)
        // 计算天数差并加1（包含开始和结束当天）
        const duration = Math.floor((end - begin) / (1000 * 60 * 60 * 24)) + 1
        this.$set(this.planForm, `${type}_Duration`, duration > 0 ? duration : 0)
      } else {
        this.$set(this.planForm, `${type}_Duration`, 0)
      }
    },
    handleClose() {
      this.dialogVisible = false
      this.planForm = {
        Area_Id: '',
        Deepen_Begin_Date: '',
        Deepen_End_Date: '',
        Deepen_Date_Range: [],
        Deepen_Duration: 0,
        Deepen_Engineer_Quantity: 0,
        Purchase_Begin_Date: '',
        Purchase_End_Date: '',
        Purchase_Date_Range: [],
        Purchase_Duration: 0,
        Purchase_Engineer_Quantity: 0,
        Product_Begin_Date: '',
        Product_End_Date: '',
        Product_Date_Range: [],
        Product_Duration: 0,
        Product_Engineer_Quantity: 0,
        Install_Begin_Date: '',
        Install_End_Date: '',
        Install_Date_Range: [],
        Install_Duration: 0,
        Install_Engineer_Quantity: 0
      }
    },
    uploadSuccess(res) {
      if (res.IsSucceed) {
        this.$message.success('导入成功')
        this.getTableData()
      } else {
        this.$message.error(res.Message)
        if (res.Data) {
          window.open(combineURL(this.$baseUrl, res.Data), '_blank')
        }
      }
    },
    exportFile() {
      this.exportLoading = true
      ExportTotalControlPlan({
        CompanyId: localStorage.getItem('CurReferenceId'),
        ProjectId: this.curProject.Sys_Project_Id,
        FilterTypes: this.queryModel.Range.map(item => {
          return ['Deepen', 'Purchase', 'Product', 'Install'].indexOf(item) + 1
        }),
        AreaIds: this.queryModel.Areas
      }).then(res => {
        if (res.IsSucceed) {
          window.open(combineURL(this.$baseUrl, res.Data), '_blank')
        } else {
          this.$message.error(res.Message)
        }
      }).finally(() => {
        this.exportLoading = false
      })
    },
    updateData() {
      this.updateLoading = true
      GenerateProjectPlanBusinessData({
        tenantIds: localStorage.getItem('tenant')
      }).then(res => {
        if (res.IsSucceed) {
          this.$message.success('更新成功')
          this.getTableData()
        } else {
          this.$message.error(res.Message)
        }
      }).finally(() => {
        this.updateLoading = false
      })
    },
    toDetail(item) {
      if (isRouteNameExists(item.route)) {
        this.$router.push({
          name: item.route
        })
      } else {
        this.$message.error('当前账户无访问权限')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.card{
  flex:1;
  height: 100%;
  margin-left: 16px;
  .content{
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .bt-table{
    flex:1;
  }
}
.header{
  display: flex;
  align-items: flex-end;
  .project-name{
    font-size: 16px;
    color: #333333;
    font-weight: bold;
    margin-right: 8px;
  }
  .el-icon-time{
    margin-left: 8px;
    margin-right: 4px;
    font-size: 14px;
  }
  .label{
    color: #333333;
    font-size: 12px;
  }
  .value{
    font-size: 14px;
    color: #333333;
    font-weight: bold;
    margin-left: 7px;
  }
}

.phase-label {
  font-weight: bold;
  font-size: 14px;
  color: #333;
  line-height: 40px;
  text-align: center;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-right: 10px;
}

.dialog-footer {
  text-align: right;
}

.el-row {
  margin-bottom: 15px;
}

.el-form-item {
  margin-bottom: 0;
}
.summary{
  display: flex;
  justify-content: space-between;
  align-items: center;
  column-gap: 12px;
  margin: 12px 0;
  .block{
    height: 105px;
    background: #FFFFFF;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #E2E4E9;
    flex: 1;
    cursor: pointer;
    .icon{
      width: 48px;
      height: 48px;
    }
    .finish{
      font-weight: bold;
      font-size: 12px;
      text-align: center;
    }
    .progress-container {
       margin-bottom: 8px;
     }
    .progress-bar {
       width: 100%;
       height: 2px;
     }
     .progress-bg {
       width: 100%;
       height: 100%;
       background-color: #f0f0f0;
       border-radius: 1px;
       overflow: visible;
       position: relative;
     }
     .progress-fill {
       height: 100%;
       border-radius: 1px;
       transition: width 0.3s ease;
       min-width: 2px;
       position: relative;
     }
     .progress-dot {
        position: absolute;
        right: -3px;
        top: -2px;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        transition: all 0.3s ease;
      }
      .progress-data {
        position: absolute;
        top: -20px;
        font-size: 14px;
        font-weight: bold;
        white-space: nowrap;
        transition: all 0.3s ease;
        pointer-events: none;
      }
    .top{
      height: 76px;
      display: flex;
      padding: 0 15px 0 8px;
      .block-name-wrap{
        width: 72px;
        text-align: center;
        margin-right: 8px;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
      .block-name{
        color: #666666;
        font-size: 14px;
        font-weight: bold;
        position: relative;
        top: -6px;
        // 超出省略号
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .plan-percent{
        font-size: 14px;
        display: flex;
        justify-content: space-between;
      }
      .plan{
        color: #666666;
      }
    }
    .bottom{
      height: 29px;
      line-height: 29px;
      color: #666666;
      font-size: 14px;
      padding: 0 12px;
    }
  }
}
</style>
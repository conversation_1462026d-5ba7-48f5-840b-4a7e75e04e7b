<template>
  <div>
    <el-form ref="form" :rules="rules" :model="form" label-width="120px">
      <el-row>
        <el-col :span="24">
          <el-form-item label="检查项内容" prop="Check_Content">
            <el-input v-model="form.Check_Content" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="合格标准" prop="Eligibility_Criteria">
            <el-input v-model="form.Eligibility_Criteria" maxlength="100" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item style="text-align: right">
            <el-button @click="$emit('close')">关 闭</el-button>
            <el-button
              type="primary"
              @click="handleSubmit('form')"
            >确 定</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { AddCheckItem } from '@/api/PRO/factorycheck'
import { EntityCheckItem } from '@/api/PRO/factorycheck'
import { SaveCheckItem } from '@/api/PRO/factorycheck'
export default {
  data() {
    return {
      check_object_id: '',
      form: {},
      rules: {
        Check_Content: [{ required: true, message: '请填写完整表单', trigger: 'blur' }],
        Eligibility_Criteria: [{ required: true, message: '请填写完整表单', trigger: 'blur' }]
      },
      title: '',
      editInfo: {}
    }
  },
  mounted() {},
  methods: {
    init(title, Id, data) {
      this.title = title
      if (title == '新增') {
        this.Check_Object_Id = Id
      } else {
        this.Check_Object_Id = Id
        this.editInfo = data
        console.log(this.editInfo)
        this.getEntityCheckType(data)
      }
    },
    async addCheckType() {
      await AddCheckItem({
        ...this.form,
        Check_Object_Id: this.Check_Object_Id
      }).then((res) => {
        if (res.IsSucceed) {
          this.$message({
            type: 'success',
            message: '保存成功'
          })
          this.$emit('close')
          this.dialogData = {}
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    getEntityCheckType(data) {
      console.log(data)
      EntityCheckItem({ id: data.Id }).then((res) => {
        if (res.IsSucceed) {
          console.log(res.Data)
          this.form = res.Data[0]
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    editCheckType() {
      SaveCheckItem({
        Id: this.editInfo.Id,
        ...this.form,
        Check_Object_Id: this.Check_Object_Id
      }).then((res) => {
        if (res.IsSucceed) {
          this.$message({
            type: 'success',
            message: '编辑成功'
          })
          this.$emit('close')
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    handleSubmit(form) {
      this.$refs[form].validate((valid) => {
        if (valid) {
          this.title == '新增' ? this.addCheckType() : this.editCheckType()
        } else {
          return false
        }
      })
    }
  }
}
</script>

  <style scoped></style>

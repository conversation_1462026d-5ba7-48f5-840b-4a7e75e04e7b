{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\material-inventory-reconfig\\raw-outbound-new\\components\\ReceiveTb.vue?vue&type=style&index=0&id=18b49f30&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\material-inventory-reconfig\\raw-outbound-new\\components\\ReceiveTb.vue", "mtime": 1757926768435}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoucmVjZWl2ZS10YnsNCiBoZWlnaHQ6IDEwMCU7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIC50Yi14ew0KICAgIGZsZXg6MTsNCiAgICBvdmVyZmxvdzogaGlkZGVuOw0KICB9DQogIGZvb3RlciB7DQogICAgbWFyZ2luLXRvcDogMTZweDsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["ReceiveTb.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ReceiveTb.vue", "sourceRoot": "src/views/PRO/material-inventory-reconfig/raw-outbound-new/components", "sourcesContent": ["<template>\r\n  <div class=\"receive-tb\">\r\n    <div v-if=\"!isView&&!isReturn\" class=\"toolbar-container\" style=\"margin-bottom: 8px\">\r\n      <vxe-toolbar>\r\n        <template #buttons>\r\n          <el-button type=\"primary\" @click=\"openAddDialog(null)\">新增</el-button>\r\n          <el-button\r\n            :disabled=\"!multipleSelection.length\"\r\n            type=\"danger\"\r\n            :loading=\"deleteLoading\"\r\n            @click=\"handleDelete\"\r\n          >删除\r\n          </el-button>\r\n          <el-button type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"batchDialogVisible = true\">批量编辑领用项目</el-button>\r\n          <PickSelect style=\"margin-left: 10px\" :selected-list=\"currentTbData\" :material-type=\"0\" @addList=\"setTbData\" />\r\n          <el-button v-if=\"isOutsourcing\" style=\"margin-left: 10px\" :disabled=\"!multipleSelection.length\" type=\"primary\" @click=\"BulkEdit\">整车含税单价</el-button>\r\n          <DynamicTableFields\r\n            style=\"margin-left: auto\"\r\n            title=\"表格配置\"\r\n            :table-config-code=\"gridCode\"\r\n            @updateColumn=\"init\"\r\n          />\r\n        </template>\r\n      </vxe-toolbar>\r\n    </div>\r\n    <div class=\"tb-x\">\r\n      <vxe-table\r\n        v-if=\"renderComponent\"\r\n        ref=\"xTable\"\r\n        :empty-render=\"{name: 'NotData'}\"\r\n        show-header-overflow\r\n        class=\"cs-vxe-table\"\r\n        :row-config=\"{ isCurrent: true, isHover: true}\"\r\n        align=\"left\"\r\n        height=\"auto\"\r\n        show-overflow\r\n        auto-resize\r\n        :loading=\"tbLoading\"\r\n        stripe\r\n        size=\"medium\"\r\n        :data=\"currentTbData\"\r\n        resizable\r\n        :edit-config=\"{\r\n          enabled:enabledEdit,\r\n          trigger: 'click',\r\n          mode: 'cell',\r\n          showIcon: !isView, showStatus: true\r\n        }\"\r\n        :edit-rules=\"validRules\"\r\n        :tooltip-config=\"{ enterable: true }\"\r\n        show-footer\r\n        :footer-method=\"footerMethod\"\r\n        @checkbox-all=\"tbSelectChange\"\r\n        @checkbox-change=\"tbSelectChange\"\r\n      >\r\n        <vxe-column v-if=\"!isView\" fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n        <template v-for=\"item in columns\">\r\n          <vxe-column\r\n            :key=\"item.Code\"\r\n            :fixed=\"item.Is_Frozen ? (item.Frozen_Dirction || 'left') : ''\"\r\n            show-overflow=\"tooltip\"\r\n            :align=\"item.Align\"\r\n            :field=\"item.Code\"\r\n            :visible=\"item.Is_Display\"\r\n            :title=\"item.Display_Name\"\r\n            :min-width=\"item.Width\"\r\n            :edit-render=\"item.Is_Edit ? {} : null\"\r\n            :sortable=\"item.Is_Sort\"\r\n          >\r\n            <template v-if=\"item.Style.tips\" #header>\r\n              <span>{{ item.Display_Name }}</span>\r\n              <el-tooltip class=\"item\" effect=\"dark\">\r\n                <div slot=\"content\" v-html=\"item.Style.tips\" />\r\n                <i class=\"el-icon-question\" style=\"cursor:pointer;font-size: 16px\" />\r\n              </el-tooltip>\r\n            </template>\r\n            <template #default=\"{ row }\">\r\n              <span v-if=\"item.Code === 'Warehouse_Location'\">\r\n                {{ row.WarehouseName }}/{{ row.LocationName }}\r\n              </span>\r\n              <span v-else-if=\"item.Code === 'InStoreDate'\">\r\n                {{ row.InStoreDate | timeFormat }}\r\n              </span>\r\n              <template v-else-if=\"item.Code === 'RawName'\">\r\n                <div>\r\n                  <el-tag v-if=\"row.Is_PartA\" type=\"danger\" effect=\"dark\" size=\"mini\">甲供</el-tag>\r\n                  <el-tag v-if=\"row.Is_Replace_Purchase\" type=\"success\" effect=\"dark\" size=\"mini\">代购</el-tag>\r\n                  <el-tag v-if=\"row.Is_Surplus\" type=\"warning\" effect=\"dark\" size=\"mini\">余料</el-tag>\r\n                  {{ row.RawName }}</div>\r\n              </template>\r\n              <template v-else-if=\"item.Code === 'OutStoreWeight'\">\r\n                {{ row.OutStoreWeight | getFormatNum(WEIGHT_DECIMAL) }}\r\n              </template>\r\n              <template v-else-if=\"item.Code === 'Voucher_Weight'\">\r\n                {{ row.Voucher_Weight | getFormatNum(3) }}\r\n              </template>\r\n              <span v-else> {{ row[item.Code] | displayValue }}</span>\r\n            </template>\r\n            <template v-if=\"item.Is_Edit\" #edit=\"{ row }\">\r\n              <div v-if=\"item.Code === 'Actual_Thick'\">\r\n                <el-input v-model=\"row[item.Code]\" type=\"text\" @change=\"$emit('updateRow')\" />\r\n              </div>\r\n              <div v-else-if=\"item.Code === 'Width'\">\r\n                <el-input\r\n                  v-model=\"row[item.Code]\"\r\n                  :min=\"0\"\r\n                  type=\"number\"\r\n                  @change=\"checkWeight(row)\"\r\n                />\r\n              </div>\r\n              <div v-else-if=\"item.Code === 'Length'\">\r\n                <el-input\r\n                  v-model=\"row[item.Code]\"\r\n                  :min=\"0\"\r\n                  type=\"number\"\r\n                  @change=\"checkWeight(row)\"\r\n                />\r\n              </div>\r\n              <div v-else-if=\"item.Code === 'OutStoreCount'\">\r\n                <el-input\r\n                  v-model=\"row[item.Code]\"\r\n                  v-inp-num=\"{ toFixed: COUNT_DECIMAL, min: 0 }\"\r\n                  :min=\"0\"\r\n                  :disabled=\"!!row.PickSubId\"\r\n                  :max=\"row.AvailableCount\"\r\n                  @change=\"checkCount(row)\"\r\n                />\r\n              </div>\r\n              <div v-else-if=\"item.Code === 'OutStoreWeight'\">\r\n                <span> {{ row[item.Code] | displayValue }}</span>\r\n              </div>\r\n              <template v-else-if=\"item.Code === 'Pick_Project_Name'\">\r\n                <vxe-select\r\n                  v-model=\"row.Pick_Sys_Project_Id\"\r\n                  style=\"width: 100%\"\r\n                  placeholder=\"请选择\"\r\n                  transfer\r\n                  clearable\r\n                  filterable\r\n                  :disabled=\"!!row.PickSubId\"\r\n                  @change=\"(e)=>changeProject(e,row)\"\r\n                >\r\n                  <vxe-option\r\n                    v-for=\"item in projectOptions\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Short_Name\"\r\n                    :value=\"item.Sys_Project_Id\"\r\n                  />\r\n                </vxe-select>\r\n              </template>\r\n              <div v-else>\r\n                <el-input v-model=\"row[item.Code]\" type=\"text\" @blur=\"$emit('updateRow')\" />\r\n              </div>\r\n            </template>\r\n          </vxe-column>\r\n        </template>\r\n      </vxe-table>\r\n    </div>\r\n    <footer v-if=\"!isView\">\r\n      <div class=\"data-info\">\r\n        <el-tag v-if=\"!isReturn\" size=\"medium\" class=\"info-x\">已选{{ multipleSelection.length }}条数据 </el-tag>\r\n      </div>\r\n      <div>\r\n        <slot />\r\n      </div>\r\n    </footer>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      title=\"选择含税单价\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"30%\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <BatchEdit v-if=\"dialogVisible\" @close=\"handleClose\" @taxUnitPrice=\"getTaxUnitPrice\" />\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      title=\"批量编辑领用项目\"\r\n      :visible.sync=\"batchDialogVisible\"\r\n      top=\"10vh\"\r\n      width=\"350px\"\r\n      @close=\"closeBatchDialog\"\r\n    >\r\n      <el-select\r\n        v-model=\"batchProjectId\"\r\n        style=\"width: 300px\"\r\n        placeholder=\"请选择\"\r\n        clearable\r\n        filterable\r\n      >\r\n        <el-option\r\n          v-for=\"item in projectOptions\"\r\n          :key=\"item.Id\"\r\n          :label=\"item.Short_Name\"\r\n          :value=\"item.Sys_Project_Id\"\r\n        />\r\n      </el-select>\r\n      <p style=\"margin: 20px\">\r\n        <i>注：仅能批量编辑公共库存的领用项目</i>\r\n      </p>\r\n      <div style=\"text-align: right\">\r\n        <el-button @click=\"closeBatchDialog\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"batchChangeProject\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n\r\n</template>\r\n<script>\r\nimport { getTableConfig } from '@/views/PRO/material-receipt-management/utils'\r\nimport { formatNum } from '@/views/PRO/material-inventory-reconfig/raw-outbound-new/utils'\r\nimport Warehouse from '@/views/PRO/material-inventory-reconfig/raw-outbound-new/components/Warehouse.vue'\r\nimport BatchEdit from './BatchEdit.vue'\r\nimport SelectProject from '@/components/Select/SelectProject/index.vue'\r\nimport { GetProjectPageList } from '@/api/PRO/project'\r\nimport { COUNT_DECIMAL, OutBOUND_DETAIL_SUMMARY_FIELDS, WEIGHT_DECIMAL } from '@/views/PRO/material_v4/config'\r\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\r\nimport PickSelect from '@/views/PRO/material_v4/pickApply/select.vue'\r\n\r\nexport default {\r\n  components: { PickSelect, DynamicTableFields, SelectProject, Warehouse, BatchEdit },\r\n  filters: {\r\n    getFormatNum(value, num) {\r\n      return formatNum(value, num)\r\n    }\r\n  },\r\n  props: {\r\n    isView: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    isReturn: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    isOutsourcing: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      COUNT_DECIMAL,\r\n      WEIGHT_DECIMAL,\r\n      dialogVisible: false,\r\n      enabledEdit: false,\r\n      deleteLoading: false,\r\n      tbLoading: false,\r\n      currentTbData: [],\r\n      columns: [],\r\n      multipleSelection: [],\r\n      bigTypeData: 1,\r\n      validRules: {\r\n        OutStoreCount: [\r\n          { required: true, type: 'number', min: 0, message: '请输入' }\r\n        ],\r\n        Pick_Sys_Project_Id: [\r\n          { required: true, type: 'string', min: 0, message: '请选择' }\r\n        ]\r\n      },\r\n      projectOptions: [],\r\n      batchDialogVisible: false,\r\n      batchProjectId: '',\r\n      excludedRoutes: ['PRORawMaterialOutboundView'],\r\n      gridCode: 'PRORawReceiveOutList',\r\n      renderComponent: true\r\n\r\n    }\r\n  },\r\n  async mounted() {\r\n    this.getProject()\r\n    this.tbData = []\r\n    await this.init()\r\n  },\r\n  methods: {\r\n    // 重新渲染vux-table\r\n    forceRerender() {\r\n      // 从 DOM 中删除 my-component 组件\r\n      this.renderComponent = false\r\n      this.$nextTick(() => {\r\n        // 在 DOM 中添加 my-component 组件\r\n        this.renderComponent = true\r\n      })\r\n    },\r\n    closeBatchDialog() {\r\n      this.batchProjectId = ''\r\n      this.batchDialogVisible = false\r\n    },\r\n    batchChangeProject() {\r\n      this.multipleSelection.forEach((element, idx) => {\r\n        const item = this.tbData.find((v) => v.uuid === element.uuid)\r\n        const i = this.tbData.findIndex((v) => v.uuid === element.uuid)\r\n        console.log({ i })\r\n        // 更新项目ID和项目名称\r\n        item.Pick_Sys_Project_Id = this.batchProjectId\r\n        item.Pick_Project_Name = this.projectOptions.find(proj => proj.Sys_Project_Id === this.batchProjectId)?.Short_Name\r\n        this.$set(this.tbData, i, item)\r\n      })\r\n      // 更新currentTbData以刷新视图\r\n      this.mergeData()\r\n      this.closeBatchDialog()\r\n    },\r\n    /**\r\n     * 获取所属项目\r\n     */\r\n    getProject() {\r\n      GetProjectPageList({\r\n        Page: 1,\r\n        PageSize: -1\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.projectOptions = res.Data.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    getTaxUnitPrice(val) {\r\n      this.multipleSelection.forEach(row => {\r\n        row.TaxUnitPrice = val\r\n      })\r\n    },\r\n    async init() {\r\n      this.enabledEdit = !this.isView\r\n      this.tbLoading = true\r\n      this.columns = await getTableConfig(this.gridCode)\r\n      this.columns = this.columns.map(item => {\r\n        item.Style = item.Style ? JSON.parse(item.Style) : ''\r\n        return item\r\n      })\r\n      this.forceRerender()\r\n      this.tbLoading = false\r\n    },\r\n    setTbData(list, checkOver, info) {\r\n      list.forEach(item => {\r\n        this.checkCount(item, checkOver)\r\n        item.Pick_Project_Name = item.Project_Name // 默认领用项目为所属项目\r\n      })\r\n      this.tbData.push(...list)\r\n      this.filterMethod()\r\n      this.$emit('setInfo', info)\r\n    },\r\n    changeProject(e, item) {\r\n      item.Pick_Project_Name = this.projectOptions.find(item => item.Sys_Project_Id === e.value)?.Short_Name\r\n    },\r\n    tbSelectChange(array) {\r\n      this.multipleSelection = array.records\r\n    },\r\n    checkWeight(row, checkOver = true) {\r\n      if (this.excludedRoutes.includes(this.$route.name)) {\r\n        return\r\n      }\r\n      row.OutStoreWeight = (row.Unit_Weight * row.OutStoreCount).toFixed(WEIGHT_DECIMAL) / 1\r\n      if (row.OutStoreWeight >= row.AvailableWeight && checkOver) {\r\n        row.OutStoreWeight = row.AvailableWeight\r\n        row.OutStoreCount = row.AvailableCount\r\n      }\r\n    },\r\n    checkCount(row, checkOver = true) {\r\n      if (row.OutStoreCount >= row.AvailableCount && checkOver) {\r\n        row.OutStoreCount = row.AvailableCount\r\n        row.OutStoreWeight = row.AvailableWeight\r\n        return\r\n      }\r\n      this.checkWeight(row, false)\r\n    },\r\n\r\n    getTbData() {\r\n      return this.tbData\r\n    },\r\n\r\n    openAddDialog() {\r\n      this.$emit('openAddDialog')\r\n    },\r\n    handleDelete() {\r\n      this.deleteLoading = true\r\n      setTimeout(() => {\r\n        const ids = this.multipleSelection.map(v => v.uuid)\r\n        this.tbData = this.tbData.filter(row => !ids.includes(row.uuid))\r\n        this.multipleSelection = []\r\n        console.log(this.tbData)\r\n        this.deleteLoading = false\r\n        this.filterMethod()\r\n      }, 0)\r\n    },\r\n    BulkEdit() {\r\n      this.dialogVisible = true\r\n    },\r\n    clearTb() {\r\n      this.tbData = []\r\n      this.filterMethod()\r\n    },\r\n    mergeData() {\r\n      // 使用深拷贝确保响应式更新\r\n      this.currentTbData = JSON.parse(JSON.stringify(this.tbData))\r\n    },\r\n    filterMethod(filterInfo) {\r\n      const filterKeys = (array, filters) => {\r\n        return array.filter(item => {\r\n          let flag = true\r\n          for (let i = 0; i < filters.length; i++) {\r\n            const element = filters[i]\r\n            let rowLabel = item[element.key] || ''\r\n            if (element.value === '') {\r\n              flag = true\r\n            }\r\n            if (typeof rowLabel !== 'string') {\r\n              rowLabel = rowLabel.toString()\r\n            }\r\n            if (rowLabel.includes(element.value)) {\r\n              flag = true\r\n            } else {\r\n              flag = false\r\n              break\r\n            }\r\n          }\r\n          return flag\r\n        })\r\n      }\r\n\r\n      if (!filterInfo) {\r\n        this.currentTbData = this.tbData\r\n      } else {\r\n        const filters = []\r\n        for (const filterInfoKey in filterInfo) {\r\n          filters.push({\r\n            key: filterInfoKey,\r\n            value: filterInfo[filterInfoKey]\r\n          })\r\n        }\r\n        console.log('filterInfoKey', filters)\r\n\r\n        this.currentTbData = filterKeys(this.tbData, filters)\r\n      }\r\n      console.log('this.currentTbData', this.currentTbData)\r\n    },\r\n    checkValidate(tbData) {\r\n      if (!tbData.length) {\r\n        this.$message({\r\n          message: '数据不能为空',\r\n          type: 'warning'\r\n        })\r\n        return {\r\n          status: false\r\n        }\r\n      }\r\n      const tb = tbData.filter(item => item.OutStoreCount > 0)\r\n      if (!tb.length) {\r\n        this.$message({\r\n          message: '出库数量不能为0',\r\n          type: 'warning'\r\n        })\r\n        return {\r\n          status: false\r\n        }\r\n      }\r\n    },\r\n    // 合计\r\n    footerMethod({ columns, data }) {\r\n      const footerData = [\r\n        columns.map((column, index) => {\r\n          if (OutBOUND_DETAIL_SUMMARY_FIELDS.includes(column.field)) {\r\n            return this.sumNum(data, column.field, 5)\r\n          }\r\n          if (index === 0) {\r\n            return '合计'\r\n          }\r\n          return null\r\n        })\r\n      ]\r\n      return footerData\r\n    },\r\n    // 进行合计\r\n    sumNum(costForm, field, digit) {\r\n      let total = 0\r\n      for (let i = 0; i < costForm.length; i++) {\r\n        total += Number(costForm[i][field]) || 0\r\n      }\r\n      return total.toFixed(digit) / 1\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.receive-tb{\r\n height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .tb-x{\r\n    flex:1;\r\n    overflow: hidden;\r\n  }\r\n  footer {\r\n    margin-top: 16px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n  }\r\n}\r\n</style>\r\n"]}]}
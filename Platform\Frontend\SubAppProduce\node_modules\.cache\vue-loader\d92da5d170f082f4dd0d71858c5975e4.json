{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\ItemDialog.vue?vue&type=template&id=4420d18c&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\ItemDialog.vue", "mtime": 1757992267192}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXY+CiAgPGVsLWZvcm0gcmVmPSJmb3JtIiA6cnVsZXM9InJ1bGVzIiA6bW9kZWw9ImZvcm0iIGxhYmVsLXdpZHRoPSIxMjBweCI+CiAgICA8ZWwtcm93PgogICAgICA8ZWwtY29sIDpzcGFuPSIyNCI+CiAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5qOA5p+l6aG55YaF5a65IiBwcm9wPSJDaGVja19Db250ZW50Ij4KICAgICAgICAgIDxlbC1pbnB1dCB2LW1vZGVsPSJmb3JtLkNoZWNrX0NvbnRlbnQiIDpkaXNhYmxlZD0idHJ1ZSIgLz4KICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgPC9lbC1jb2w+CiAgICAgIDxlbC1jb2wgOnNwYW49IjI0Ij4KICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLlkIjmoLzmoIflh4YiIHByb3A9IkVsaWdpYmlsaXR5X0NyaXRlcmlhIj4KICAgICAgICAgIDxlbC1pbnB1dCB2LW1vZGVsPSJmb3JtLkVsaWdpYmlsaXR5X0NyaXRlcmlhIiBtYXhsZW5ndGg9IjEwMCIgLz4KICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgPC9lbC1jb2w+CiAgICAgIDxlbC1jb2wgOnNwYW49IjI0Ij4KICAgICAgICA8ZWwtZm9ybS1pdGVtIHN0eWxlPSJ0ZXh0LWFsaWduOiByaWdodCI+CiAgICAgICAgICA8ZWwtYnV0dG9uIEBjbGljaz0iJGVtaXQoJ2Nsb3NlJykiPuWFsyDpl608L2VsLWJ1dHRvbj4KICAgICAgICAgIDxlbC1idXR0b24KICAgICAgICAgICAgdHlwZT0icHJpbWFyeSIKICAgICAgICAgICAgQGNsaWNrPSJoYW5kbGVTdWJtaXQoJ2Zvcm0nKSIKICAgICAgICAgID7noa4g5a6aPC9lbC1idXR0b24+CiAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgIDwvZWwtY29sPgogICAgPC9lbC1yb3c+CiAgPC9lbC1mb3JtPgo8L2Rpdj4K"}, null]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\factory\\component\\Card.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\factory\\component\\Card.vue", "mtime": 1758011160329}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBwcm9wczogewogICAgaXRlbTogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQ6IGZ1bmN0aW9uIF9kZWZhdWx0KCkge30KICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICBXYXJlaG91c2VMaXN0OiBmdW5jdGlvbiBXYXJlaG91c2VMaXN0KCkgewogICAgICBpZiAodGhpcy5pdGVtLldhcmVob3VzZV9OYW1lICYmIHRoaXMuaXRlbS5XYXJlaG91c2VfTmFtZS5sZW5ndGgpIHsKICAgICAgICByZXR1cm4gdGhpcy5pdGVtLldhcmVob3VzZV9OYW1lLnNwbGl0KCcsJyk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuIFtdOwogICAgICB9CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBoYW5kbGVFZGl0OiBmdW5jdGlvbiBoYW5kbGVFZGl0KHJvdykgewogICAgICB0aGlzLiRlbWl0KCdlZGl0Jywgcm93KTsKICAgIH0sCiAgICBoYW5kbGVEZWxldGU6IGZ1bmN0aW9uIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgdGhpcy4kZW1pdCgnZGVsZXRlJywgcm93KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["props", "item", "type", "Object", "default", "computed", "WarehouseList", "Warehouse_Name", "length", "split", "methods", "handleEdit", "row", "$emit", "handleDelete"], "sources": ["src/views/PRO/basic-information/factory/component/Card.vue"], "sourcesContent": ["<template>\n  <div class=\"card-wrapper\">\n    <div class=\"top-box\">\n      <div class=\"t-box\">\n        <i class=\"iconfont icon-steel\" />\n        <strong class=\"title\">{{ item.Short_Name }}</strong>\n      </div>\n      <div class=\"t-box-btn\">\n        <el-button v-if=\"true\" size=\"mini\" type=\"danger\" @click=\"handleDelete()\">删 除</el-button>\n        <el-button size=\"mini\" @click=\"handleEdit\">编 辑</el-button>\n      </div>\n    </div>\n    <div class=\"sub-title\">\n      {{ item.Name }}\n    </div>\n\n    <div class=\"tag-container\">\n      <span class=\"tag-box\">{{ item.Category }}</span>\n    </div>\n\n    <!--    <div class=\"cs-bottom\">\n      <div class=\"cs-label\">对接仓库：</div>\n      <span v-for=\"(item,index) in WarehouseList\" :key=\"index\" class=\"title\">\n        <i class=\"iconfont icon-home3\" />\n        <span>{{ item }}</span>\n      </span>\n    </div>-->\n  </div>\n</template>\n\n<script>\nexport default {\n  props: {\n    item: {\n      type: Object,\n      default: () => {\n      }\n    }\n  },\n  computed: {\n    WarehouseList() {\n      if (this.item.Warehouse_Name && this.item.Warehouse_Name.length) {\n        return this.item.Warehouse_Name.split(',')\n      } else {\n        return []\n      }\n    }\n  },\n  methods: {\n    handleEdit(row) {\n      this.$emit('edit', row)\n    },\n    handleDelete(row) {\n      this.$emit('delete', row)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.card-wrapper {\n  display: flex;\n  flex-direction: column;\n  width: calc(25% - 16px);\n  min-width: 387px;\n  margin: 8px;\n  padding: 20px;\n  background: #FFFFFF;\n  box-shadow: 0px 2px 8px rgba(20, 35, 78, 0.08);\n  border-radius: 8px;\n\n  .top-box {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    .t-box-btn {\n      align-self: flex-start;\n      flex: 0 1 130px;\n    }\n    .t-box {\n      flex: 1;\n      display: flex;\n      align-items: center;\n\n      .iconfont {\n        align-self: flex-start;\n        font-size: 20px;\n        color: #298DFF;\n        margin-right: 8px;\n      }\n\n      .title {\n        display: inline-block;\n        font-size: 18px;\n      }\n    }\n\n  }\n\n  .sub-title {\n    margin-top: 8px;\n    height: 19px;\n    font-size: 14px;\n    line-height: 22px;\n    color: rgba(34, 40, 52, 0.65);\n  }\n\n  .tag-container {\n    margin-top: 22px;\n\n    .tag-box {\n      height: 24px;\n      line-height: 24px;\n      display: inline-block;\n      padding: 2px 6px;\n      margin: 4px 8px;\n      color: #818FB7;\n      background: rgba(129, 143, 183, 0.12);\n      border-radius: 4px;\n    }\n  }\n\n  .cs-bottom {\n    margin-top: 19px;\n\n    .cs-label {\n      font-size: 14px;\n      color: rgba(34, 40, 52, 0.4);\n    }\n\n    .title {\n      color: #222834;\n      display: inline-block;\n      margin: 8px 4px;\n\n      .icon-home3 {\n        margin-right: 2px;\n      }\n    }\n  }\n\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA;EACAA,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA,GACA;IACA;EACA;EACAC,QAAA;IACAC,aAAA,WAAAA,cAAA;MACA,SAAAL,IAAA,CAAAM,cAAA,SAAAN,IAAA,CAAAM,cAAA,CAAAC,MAAA;QACA,YAAAP,IAAA,CAAAM,cAAA,CAAAE,KAAA;MACA;QACA;MACA;IACA;EACA;EACAC,OAAA;IACAC,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAAC,KAAA,SAAAD,GAAA;IACA;IACAE,YAAA,WAAAA,aAAAF,GAAA;MACA,KAAAC,KAAA,WAAAD,GAAA;IACA;EACA;AACA", "ignoreList": []}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\component\\ProjectAddDialog.vue?vue&type=style&index=0&id=278490d2&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\component\\ProjectAddDialog.vue", "mtime": 1758266768053}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmFkZC1wcm9qZWN0LWNvbnRhaW5lciB7DQoNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCg0KICAuc2VhcmNoLXNlY3Rpb24gew0KICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICB9DQoNCiAgLmluc3RydWN0aW9uIHsNCiAgICBiYWNrZ3JvdW5kOiAjZjBmOWZmOw0KICAgIGJvcmRlcjogMXB4IHNvbGlkICNiM2Q4ZmY7DQogICAgY29sb3I6ICMxODkwZmY7DQogICAgcGFkZGluZzogMTJweCAxNnB4Ow0KICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogIH0NCg0KICAudGFibGUtc2VjdGlvbiB7DQogICAgZmxleDogMTsNCiAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICBvdmVyZmxvdzogaGlkZGVuOw0KICB9DQoNCiAgLmZvb3Rlci1hY3Rpb25zIHsNCiAgICBtYXJnaW4tdG9wOiAxNnB4Ow0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsNCiAgICBwYWRkaW5nOiAwcHggOHB4IDAgMDsNCiAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICB9DQp9DQo="}, {"version": 3, "sources": ["ProjectAddDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoHA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ProjectAddDialog.vue", "sourceRoot": "src/views/PRO/project-config/product-mfg-path/component", "sourcesContent": ["<template>\r\n  <div class=\"add-project-container\">\r\n    <div class=\"instruction\">\r\n      请选择项目，添加所选项目的所有生产路径\r\n    </div>\r\n    <div class=\"search-section\">\r\n      <el-form :model=\"searchForm\" inline>\r\n        <el-form-item v-if=\"!projectList.length\" label=\"项目名称：\">\r\n          <div v-loading=\"pLoading\">暂无可同步的项目</div>\r\n        </el-form-item>\r\n        <el-form-item v-else label=\"项目名称：\">\r\n          <el-select\r\n            v-model=\"searchForm.ProjectCode\"\r\n            clearable\r\n            filterable\r\n            placeholder=\"请选择项目\"\r\n            style=\"width: 200px\"\r\n          >\r\n            <el-option v-for=\"item in projectList\" :key=\"item.Sys_Project_Id\" :label=\"item.Short_Name\" :value=\"item.Sys_Project_Id\" :disabled=\"item.Sys_Project_Id===sysProjectId\" />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n      </el-form>\r\n    </div>\r\n\r\n    <!-- 提示信息 -->\r\n\r\n    <!-- 底部按钮 -->\r\n    <div class=\"footer-actions\">\r\n      <el-button @click=\"handleCancel\">取消</el-button>\r\n      <el-button v-if=\"projectList.length\" type=\"primary\" :loading=\"loading\" :disabled=\"searchForm.ProjectCode === ''\" @click=\"handleConfirm\">\r\n        确定\r\n      </el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetTechnologyOfProjectList } from '@/api/PRO/technology-lib'\r\nimport { SyncProjectTechnologyFromProject } from '@/api/PRO/technology-lib'\r\nexport default {\r\n  name: 'AddProject',\r\n  props: {\r\n    sysProjectId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      pLoading: false,\r\n      projectList: [],\r\n      searchForm: {\r\n        ProjectCode: ''\r\n      }\r\n\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchProjectList()\r\n  },\r\n  methods: {\r\n\r\n    // 获取项目列表\r\n    async fetchProjectList() {\r\n      try {\r\n        const params = {\r\n        }\r\n        this.pLoading = true\r\n        const res = await GetTechnologyOfProjectList(params)\r\n        if (res.IsSucceed) {\r\n          const table = res?.Data || []\r\n          this.projectList = table\r\n          // this.projectList = table.filter(item => item.Sys_Project_Id !== this.sysProjectId)\r\n        } else {\r\n          this.$message.error(res.Message || '获取项目列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取项目列表失败:', error)\r\n        this.$message.error('获取项目列表失败')\r\n      } finally {\r\n        this.pLoading = false\r\n      }\r\n    },\r\n    handleCancel() {\r\n      this.$emit('close')\r\n    },\r\n    // 确认选择\r\n    handleConfirm() {\r\n      if (this.searchForm.ProjectCode === '') {\r\n        this.$message.warning('请至少选择一个项目')\r\n        return\r\n      }\r\n      this.loading = true\r\n\r\n      SyncProjectTechnologyFromProject({\r\n        From_Sys_Project_Id: this.searchForm.ProjectCode,\r\n        To_Sys_Project_Id: this.sysProjectId\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$emit('refresh')\r\n          this.$emit('close')\r\n          this.$message.success('同步成功！')\r\n        } else {\r\n          this.$message.error(res.Message || '同步失败')\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.add-project-container {\r\n\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .search-section {\r\n    background: #fff;\r\n    border-radius: 4px;\r\n  }\r\n\r\n  .instruction {\r\n    background: #f0f9ff;\r\n    border: 1px solid #b3d8ff;\r\n    color: #1890ff;\r\n    padding: 12px 16px;\r\n    border-radius: 4px;\r\n    margin-bottom: 16px;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .table-section {\r\n    flex: 1;\r\n    background: #fff;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .footer-actions {\r\n    margin-top: 16px;\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    padding: 0px 8px 0 0;\r\n    background: #fff;\r\n  }\r\n}\r\n</style>\r\n"]}]}
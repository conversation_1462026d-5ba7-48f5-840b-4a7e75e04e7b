{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\actually-sent\\v4\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\actually-sent\\v4\\index.vue", "mtime": 1757468128067}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9yZWdlbmVyYXRvclJ1bnRpbWUgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9yZWdlbmVyYXRvclJ1bnRpbWUuanMiOwppbXBvcnQgX2FzeW5jVG9HZW5lcmF0b3IgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9hc3luY1RvR2VuZXJhdG9yLmpzIjsKaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0IF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkIGZyb20gIkQ6L3Byb2plY3QvcGxhdGZvcm1fZnJhbWV3b3JrX21hc3Rlci9wbGF0Zm9ybV9mcmFtZXdvcmsvUGxhdGZvcm0vRnJvbnRlbmQvU3ViQXBwUHJvZHVjZS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vaW50ZXJvcFJlcXVpcmVXaWxkY2FyZC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZpbHRlci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZpbmQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5wdXNoLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5maWx0ZXIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5maW5kLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IubWFwLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuanNvbi5zdHJpbmdpZnkuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3Qua2V5cy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaXRlcmF0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLml0ZXJhdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLnVybC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi51cmwudG8tanNvbi5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi51cmwtc2VhcmNoLXBhcmFtcy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi51cmwtc2VhcmNoLXBhcmFtcy5kZWxldGUuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy93ZWIudXJsLXNlYXJjaC1wYXJhbXMuaGFzLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLnVybC1zZWFyY2gtcGFyYW1zLnNpemUuanMiOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwoKaW1wb3J0IE1vbml0b3IgZnJvbSAnQC9jb21wb25lbnRzL01vbml0b3IvaW5kZXgudnVlJzsKaW1wb3J0IFRvcEhlYWRlciBmcm9tICdAL2NvbXBvbmVudHMvVG9wSGVhZGVyL2luZGV4LnZ1ZSc7CmltcG9ydCBhZGRSb3V0ZXJQYWdlIGZyb20gJ0AvbWl4aW5zL2FkZC1yb3V0ZXItcGFnZSc7CmltcG9ydCBEeW5hbWljRGF0YVRhYmxlIGZyb20gJ0AvY29tcG9uZW50cy9EeW5hbWljRGF0YVRhYmxlL0R5bmFtaWNEYXRhVGFibGUudnVlJzsKaW1wb3J0IGdldFRiSW5mbyBmcm9tICdAL21peGlucy9QUk8vZ2V0LXRhYmxlLWluZm8tcHJvMic7CmltcG9ydCB7IEdldFByb2plY3RTZW5kaW5nSW5mb1BhZ2VsaXN0LCBEZWxldGVQcm9qZWN0U2VuZGluZ0luZm8sIFN1Ym1pdFByb2plY3RTZW5kaW5nLCBHZXRQcm9qZWN0U2VuZGluZ0FsbENvdW50LCBUcmFuc2Zvcm1zV2l0aG91dFdlaWdodCwgU3VibWl0V2VpZ2hpbmdGb3JQQywgV2l0aGRyYXdEcmFmdCwgRXhwb3J0SW52b2ljZUxpc3QsIFN1Ym1pdEFwcHJvdmFsLCBDYW5jZWxGbG93IH0gZnJvbSAnQC9hcGkvUFJPL2NvbXBvbmVudC1zdG9jay1vdXQnOwppbXBvcnQgeyBHZXRQcm9qZWN0UGFnZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vcHJvLXNjaGVkdWxlcyc7CmltcG9ydCB7IEdlQXJlYVRyZWVzIH0gZnJvbSAnQC9hcGkvUFJPL3Byb2plY3QnOwppbXBvcnQgeyBHZXRJbnN0YWxsVW5pdFBhZ2VMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL2luc3RhbGwtdW5pdCc7CmltcG9ydCB7IHBhcnNlVGltZSB9IGZyb20gJ0AvdXRpbHMnOwppbXBvcnQgeyBiYXNlVXJsIH0gZnJvbSAnQC91dGlscy9iYXNldXJsJzsKaW1wb3J0IHsgR2V0RmFjdG9yeVByb2Zlc3Npb25hbEJ5Q29kZSB9IGZyb20gJ0AvYXBpL1BSTy9wcm9mZXNzaW9uYWxUeXBlJzsKaW1wb3J0IFByaW50RGlhbG9nIGZyb20gJy4vY29tcG9uZW50L3ByaW50RGlhbG9nLnZ1ZSc7CmltcG9ydCByYWRpb0RpYWxvZyBmcm9tICcuL2NvbXBvbmVudC9kaWFsb2cudnVlJzsKaW1wb3J0IGRpYWxvZ0V4Y2VsIGZyb20gJy4vY29tcG9uZW50L2RpYWxvZ0V4Y2VsLnZ1ZSc7CmltcG9ydCBjaGVja0RpYWxvZyBmcm9tICcuL2NvbXBvbmVudC9jaGVjay52dWUnOwppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCc7CmltcG9ydCBFeHBvcnRDdXN0b21SZXBvcnQgZnJvbSAiQC9jb21wb25lbnRzL0V4cG9ydEN1c3RvbVJlcG9ydC9pbmRleC52dWUiOwp2YXIgU3RhdHVzTWFwID0gewogIDA6ICfojYnnqL8nLAogIDI6ICflvoXov4fno4UnLAogIDM6ICflt7Lov4fno4UnLAogIDQ6ICfmnKrpqozmlLYnLAogIDU6ICfpg6jliIbpqozmlLYnLAogIDY6ICflt7LpqozmlLYnLAogIDc6ICflt7LpgIDotKcnLAogIDk5OTogJ+WuoeaJueS4rScsCiAgJy0xJzogJ+W3sumAgOWbnicKfTsKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsKICAgIEV4cG9ydEN1c3RvbVJlcG9ydDogRXhwb3J0Q3VzdG9tUmVwb3J0LAogICAgVG9wSGVhZGVyOiBUb3BIZWFkZXIsCiAgICBNb25pdG9yOiBNb25pdG9yLAogICAgRHluYW1pY0RhdGFUYWJsZTogRHluYW1pY0RhdGFUYWJsZSwKICAgIFByaW50RGlhbG9nOiBQcmludERpYWxvZywKICAgIHJhZGlvRGlhbG9nOiByYWRpb0RpYWxvZywKICAgIGNoZWNrRGlhbG9nOiBjaGVja0RpYWxvZywKICAgIGRpYWxvZ0V4Y2VsOiBkaWFsb2dFeGNlbAogIH0sCiAgZmlsdGVyczogewogICAgc2VuZERhdGVGaWx0ZXI6IGZ1bmN0aW9uIHNlbmREYXRlRmlsdGVyKGUpIHsKICAgICAgLy8gY29uc29sZS5sb2coZSwiZWVlZSIpOwogICAgICByZXR1cm4gcGFyc2VUaW1lKG5ldyBEYXRlKGUpKTsKICAgIH0KICB9LAogIG1peGluczogW2FkZFJvdXRlclBhZ2UsIGdldFRiSW5mb10sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHNlbGVjdExpc3Q6IFtdLAogICAgICBzdGF0dXNJbmZvOiBTdGF0dXNNYXAsCiAgICAgIElzVmlzYWJlbDogZmFsc2UsCiAgICAgIGJ0bkxvYWRpbmc6IGZhbHNlLAogICAgICBmb3JtOiB7CiAgICAgICAgQ29kZTogJycsCiAgICAgICAgU3RhdHVzOiBudWxsLAogICAgICAgIFByb2plY3RJZDogJycsCiAgICAgICAgVmVoaWNsZU5vOiAnJywKICAgICAgICBDb25zaWduZWU6ICcnLAogICAgICAgIElzUmV0dXJuOiBudWxsLAogICAgICAgIElzX1dlaWdodF9XYXJuaW5nOiBudWxsLAogICAgICAgIGRhdGVSYW5nZTogWycnLCAnJ10sCiAgICAgICAgUGFnZUluZm86IHsKICAgICAgICAgIFBhcmFtZXRlckpzb246IFtdLAogICAgICAgICAgUGFnZTogMSwKICAgICAgICAgIFBhZ2VTaXplOiAyMAogICAgICAgIH0KICAgICAgfSwKICAgICAgZm9ybTI6IHsKICAgICAgICBQcm9qZWN0SWQ6ICcnLAogICAgICAgIEFyZWFfSWQ6ICcnLAogICAgICAgIEluc3RhbGxVbml0X0lkOiAnJwogICAgICB9LAogICAgICBydWxlczogewogICAgICAgIFByb2plY3RJZDogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+mAieaLqScsCiAgICAgICAgICB0cmlnZ2VyOiAnY2hhbmdlJwogICAgICAgIH1dCiAgICAgICAgLy8gQXJlYV9JZDogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6kiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9XSwKICAgICAgfSwKICAgICAgcGlja2VyT3B0aW9uczogewogICAgICAgIHNob3J0Y3V0czogW3sKICAgICAgICAgIHRleHQ6ICfku4rlpKknLAogICAgICAgICAgb25DbGljazogZnVuY3Rpb24gb25DbGljayhwaWNrZXIpIHsKICAgICAgICAgICAgdmFyIGVuZCA9IG5ldyBEYXRlKCk7CiAgICAgICAgICAgIHZhciBzdGFydCA9IG5ldyBEYXRlKCk7CiAgICAgICAgICAgIHN0YXJ0LnNldFRpbWUoc3RhcnQuZ2V0VGltZSgpIC0gMzYwMCAqIDEwMDAgKiAyNCAqIDEpOwogICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBbc3RhcnQsIGVuZF0pOwogICAgICAgICAgfQogICAgICAgIH0sIHsKICAgICAgICAgIHRleHQ6ICfmnIDov5HkuIDlkagnLAogICAgICAgICAgb25DbGljazogZnVuY3Rpb24gb25DbGljayhwaWNrZXIpIHsKICAgICAgICAgICAgdmFyIGVuZCA9IG5ldyBEYXRlKCk7CiAgICAgICAgICAgIHZhciBzdGFydCA9IG5ldyBEYXRlKCk7CiAgICAgICAgICAgIHN0YXJ0LnNldFRpbWUoc3RhcnQuZ2V0VGltZSgpIC0gMzYwMCAqIDEwMDAgKiAyNCAqIDcpOwogICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBbc3RhcnQsIGVuZF0pOwogICAgICAgICAgfQogICAgICAgIH0sIHsKICAgICAgICAgIHRleHQ6ICfmnIDov5HkuIDkuKrmnIgnLAogICAgICAgICAgb25DbGljazogZnVuY3Rpb24gb25DbGljayhwaWNrZXIpIHsKICAgICAgICAgICAgdmFyIGVuZCA9IG5ldyBEYXRlKCk7CiAgICAgICAgICAgIHZhciBzdGFydCA9IG5ldyBEYXRlKCk7CiAgICAgICAgICAgIHN0YXJ0LnNldFRpbWUoc3RhcnQuZ2V0VGltZSgpIC0gMzYwMCAqIDEwMDAgKiAyNCAqIDMwKTsKICAgICAgICAgICAgcGlja2VyLiRlbWl0KCdwaWNrJywgW3N0YXJ0LCBlbmRdKTsKICAgICAgICAgIH0KICAgICAgICB9XQogICAgICB9LAogICAgICBzZWxlY3RQYXJhbXM6IHsKICAgICAgICBjbGVhcmFibGU6IHRydWUsCiAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fpgInmi6knCiAgICAgIH0sCiAgICAgIFByb2plY3RJZDogJycsCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBwYWdlTG9hZGluZzogZmFsc2UsCiAgICAgIGFkZFBhZ2VBcnJheTogW3sKICAgICAgICBwYXRoOiB0aGlzLiRyb3V0ZS5wYXRoICsgJy9hZGQnLAogICAgICAgIGhpZGRlbjogdHJ1ZSwKICAgICAgICBjb21wb25lbnQ6IGZ1bmN0aW9uIGNvbXBvbmVudCgpIHsKICAgICAgICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUoKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgcmV0dXJuIF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkKHJlcXVpcmUoJ0Avdmlld3MvUFJPL3NoaXBtZW50L2FjdHVhbGx5LXNlbnQvdjQvYWRkLnZ1ZScpKTsKICAgICAgICAgIH0pOwogICAgICAgIH0sCiAgICAgICAgbmFtZTogJ1BST1NoaXBTZW50QWRkJywKICAgICAgICBtZXRhOiB7CiAgICAgICAgICB0aXRsZTogJ+aWsOW7uuWPkei0p+WNlScKICAgICAgICB9CiAgICAgIH0sIHsKICAgICAgICBwYXRoOiB0aGlzLiRyb3V0ZS5wYXRoICsgJy9lZGl0JywKICAgICAgICBoaWRkZW46IHRydWUsCiAgICAgICAgY29tcG9uZW50OiBmdW5jdGlvbiBjb21wb25lbnQoKSB7CiAgICAgICAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKCkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgIHJldHVybiBfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZChyZXF1aXJlKCdAL3ZpZXdzL1BSTy9zaGlwbWVudC9hY3R1YWxseS1zZW50L3Y0L2VkaXQudnVlJykpOwogICAgICAgICAgfSk7CiAgICAgICAgfSwKICAgICAgICBuYW1lOiAnUFJPU2hpcFNlbnRFZGl0JywKICAgICAgICBtZXRhOiB7CiAgICAgICAgICB0aXRsZTogJ+e8lui+keWPkei0p+WNlScKICAgICAgICB9CiAgICAgIH0sIHsKICAgICAgICBwYXRoOiB0aGlzLiRyb3V0ZS5wYXRoICsgJy9kZXRhaWwnLAogICAgICAgIGhpZGRlbjogdHJ1ZSwKICAgICAgICBjb21wb25lbnQ6IGZ1bmN0aW9uIGNvbXBvbmVudCgpIHsKICAgICAgICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUoKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgcmV0dXJuIF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkKHJlcXVpcmUoJ0Avdmlld3MvUFJPL3NoaXBtZW50L2FjdHVhbGx5LXNlbnQvdjQvZGV0YWlsLnZ1ZScpKTsKICAgICAgICAgIH0pOwogICAgICAgIH0sCiAgICAgICAgbmFtZTogJ1BST1NoaXBTZW50RGV0YWlsJywKICAgICAgICBtZXRhOiB7CiAgICAgICAgICB0aXRsZTogJ+ivpuaDhScKICAgICAgICB9CiAgICAgIH0sIHsKICAgICAgICBwYXRoOiB0aGlzLiRyb3V0ZS5wYXRoICsgJy9jaGFuZ2VSZWNvcmQnLAogICAgICAgIGhpZGRlbjogdHJ1ZSwKICAgICAgICBjb21wb25lbnQ6IGZ1bmN0aW9uIGNvbXBvbmVudCgpIHsKICAgICAgICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUoKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgcmV0dXJuIF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkKHJlcXVpcmUoJ0Avdmlld3MvUFJPL3NoaXBtZW50L2FjdHVhbGx5LXNlbnQvdjQvY2hhbmdlUmVjb3JkLnZ1ZScpKTsKICAgICAgICAgIH0pOwogICAgICAgIH0sCiAgICAgICAgbmFtZTogJ1BST1NoaXBTZW50Q2hhbmdlUmVjb3JkJywKICAgICAgICBtZXRhOiB7CiAgICAgICAgICB0aXRsZTogJ+WPkei0p+WNleWPmOabtOiusOW9lScKICAgICAgICB9CiAgICAgIH1dLAogICAgICBwcm9qZWN0czogW10sCiAgICAgIC8vIOWMuuWfn+aVsOaNrgogICAgICB0cmVlUGFyYW1zQXJlYTogewogICAgICAgICdjaGVjay1zdHJpY3RseSc6IHRydWUsCiAgICAgICAgJ2V4cGFuZC1vbi1jbGljay1ub2RlJzogZmFsc2UsCiAgICAgICAgJ2RlZmF1bHQtZXhwYW5kLWFsbCc6IHRydWUsCiAgICAgICAgZmlsdGVyYWJsZTogZmFsc2UsCiAgICAgICAgY2xpY2tQYXJlbnQ6IHRydWUsCiAgICAgICAgZGF0YTogW10sCiAgICAgICAgcHJvcHM6IHsKICAgICAgICAgIGNoaWxkcmVuOiAnQ2hpbGRyZW4nLAogICAgICAgICAgbGFiZWw6ICdMYWJlbCcsCiAgICAgICAgICB2YWx1ZTogJ0lkJwogICAgICAgIH0KICAgICAgfSwKICAgICAgc3R5bGVzOiB7CiAgICAgICAgd2lkdGg6ICcxMDAlJwogICAgICB9LAogICAgICBTZXR1cFBvc2l0aW9uRGF0YTogW10sCiAgICAgIHF1ZXJ5SW5mbzogewogICAgICAgIFBhZ2U6IDEsCiAgICAgICAgUGFnZVNpemU6IDEwLAogICAgICAgIFBhcmFtZXRlckpzb246IFtdCiAgICAgIH0sCiAgICAgIHF1ZXJ5SW5mbzI6IHsKICAgICAgICBCZWdpbkRhdGU6ICcnLAogICAgICAgIEVuZERhdGU6ICcnLAogICAgICAgIFBhZ2VJbmZvOiB7CiAgICAgICAgICBQYXJhbWV0ZXJKc29uOiBbXSwKICAgICAgICAgIFBhZ2U6IDEsCiAgICAgICAgICBQYWdlU2l6ZTogMgogICAgICAgIH0KICAgICAgfSwKICAgICAgdGJDb25maWc6IHsKICAgICAgICBQYWdlcl9BbGlnbjogJ2NlbnRlcicsCiAgICAgICAgT3BfV2lkdGg6IDI4MAogICAgICB9LAogICAgICBjb2x1bW5zOiBbXSwKICAgICAgdGJEYXRhOiBbXSwKICAgICAgdG90YWw6IDAsCiAgICAgIHRiTG9hZGluZzogZmFsc2UsCiAgICAgIHNlbGVjdFJvdzoge30sCiAgICAgIHRvdGFsRGF0YTogewogICAgICAgIEFsbHN0ZWVsYW1vdW50OiAwLAogICAgICAgIEFsbHN0ZWVsd2VpZ2h0OiAwCiAgICAgIH0sCiAgICAgIFByb2Zlc3Npb25hbFR5cGU6IG51bGwsCiAgICAgIHRpdGxlOiAnJywKICAgICAgSXNfSW50ZWdyYXRpb246IGZhbHNlIC8vIOaYr+WQpuS4gOS9k+WMlgogICAgfTsKICB9LAogIGNvbXB1dGVkOiBfb2JqZWN0U3ByZWFkKHsKICAgIHNlbGVjdEVtcHR5OiBmdW5jdGlvbiBzZWxlY3RFbXB0eSgpIHsKICAgICAgcmV0dXJuIE9iamVjdC5rZXlzKHRoaXMuc2VsZWN0Um93KS5sZW5ndGggPT09IDA7CiAgICB9LAogICAgY29tTGlzdDogZnVuY3Rpb24gY29tTGlzdCgpIHsKICAgICAgaWYgKCF0aGlzLklzX0ludGVncmF0aW9uKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuY29sdW1ucy5maWx0ZXIoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgIHJldHVybiBpdGVtLkNvZGUgIT09ICdTdW1BY2NlcHRDb3VudCcgJiYgaXRlbS5Db2RlICE9PSAnU3VtUGVuZGluZ0NvdW50JzsKICAgICAgICB9KTsKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gdGhpcy5jb2x1bW5zOwogICAgICB9CiAgICB9CiAgfSwgbWFwR2V0dGVycygnZmFjdG9yeUluZm8nLCBbJ0NvbXBvbmVudF9TaGlwcGluZ19BcHByb3ZhbCcsICdhdXRvR2VuZXJhdGUnLCAnU2hpcHBpbmdfQXBwcm92YWxfTG93ZXJMaW1pdCcsICdTaGlwcGluZ19XZWlnaF9FbmFibGVkJ10pKSwKICBhY3RpdmF0ZWQ6IGZ1bmN0aW9uIGFjdGl2YXRlZCgpIHsKICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlJChfY29udGV4dCkgewogICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0LnByZXYgPSBfY29udGV4dC5uZXh0KSB7CiAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdhY3RpdmF0ZWQnKTsKICAgICAgICAgICAgaWYgKF90aGlzLiRyb3V0ZS5xdWVyeS5yZWZyZXNoKSB7CiAgICAgICAgICAgICAgX3RoaXMuZmV0Y2hEYXRhKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgLy8gdGhpcy5mZXRjaERhdGEoKQogICAgICAgICAgY2FzZSAyOgogICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LnN0b3AoKTsKICAgICAgICB9CiAgICAgIH0sIF9jYWxsZWUpOwogICAgfSkpKCk7CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUyKCkgewogICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTIkKF9jb250ZXh0MikgewogICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0Mi5wcmV2ID0gX2NvbnRleHQyLm5leHQpIHsKICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgX3RoaXMyLiRzdG9yZS5kaXNwYXRjaCgnZmFjdG9yeUluZm8vZ2V0V29ya3Nob3AnKTsKICAgICAgICAgICAgX2NvbnRleHQyLm5leHQgPSAzOwogICAgICAgICAgICByZXR1cm4gX3RoaXMyLiRzdG9yZS5kaXNwYXRjaCgndXNlci9nZXRQcmVmZXJlbmNlU2V0dGluZycsICdJc19JbnRlZ3JhdGlvbicpOwogICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICBfdGhpczIuSXNfSW50ZWdyYXRpb24gPSBfY29udGV4dDIuc2VudDsKICAgICAgICAgICAgX3RoaXMyLmdldEZhY3RvcnlUeXBlT3B0aW9uKCk7CiAgICAgICAgICAgIF90aGlzMi5nZXRQcm9qZWN0UGFnZUxpc3QoKTsKICAgICAgICAgIGNhc2UgNjoKICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgIHJldHVybiBfY29udGV4dDIuc3RvcCgpOwogICAgICAgIH0KICAgICAgfSwgX2NhbGxlZTIpOwogICAgfSkpKCk7CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgY29uc29sZS5sb2coJ21vdW50ZWQnKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGhhbmRsZUNhbmNlbEZsb3c6IGZ1bmN0aW9uIGhhbmRsZUNhbmNlbEZsb3coaW5zdGFuY2VJZCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm5pKk5ZuePycsICfmj5DnpLonLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBDYW5jZWxGbG93KHsKICAgICAgICAgIGluc3RhbmNlSWQ6IGluc3RhbmNlSWQKICAgICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICAgIF90aGlzMy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgbWVzc2FnZTogJ+aTjeS9nOaIkOWKnycsCiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICBfdGhpczMuZmV0Y2hEYXRhKCk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBfdGhpczMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ2luZm8nLAogICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iCcKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgc3VibWl0Rm9yUmV2aWV3OiBmdW5jdGlvbiBzdWJtaXRGb3JSZXZpZXcocm93KSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB2YXIgSWQgPSByb3cuSWQ7CiAgICAgIGlmICghcm93LlZlaGljbGVObykgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgbWVzc2FnZTogJ+WPkei0p+WNlei9pui+huS/oeaBr+acquWujOWWhO+8jOivt+WujOWWhOWQjuaPkOS6pCcsCiAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICB9KTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm5o+Q5Lqk5a6h5qC4PycsICfmj5DnpLonLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBTdWJtaXRBcHByb3ZhbCh7CiAgICAgICAgICBJZDogSWQKICAgICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICAgIF90aGlzNC5mZXRjaERhdGEoKTsKICAgICAgICAgICAgX3RoaXM0LiRtZXNzYWdlKHsKICAgICAgICAgICAgICBtZXNzYWdlOiAn5pON5L2c5oiQ5YqfJywKICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBfdGhpczQuJG1lc3NhZ2UoewogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczQuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ2luZm8nLAogICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iCcKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlTW9uaXRvcjogZnVuY3Rpb24gaGFuZGxlTW9uaXRvcihyb3dJZCkgewogICAgICB0aGlzLiRyZWZzWydtb25pdG9yJ10ub3BlbmRpYWxvZyhyb3dJZCwgZmFsc2UpOwogICAgfSwKICAgIGdldEF1ZGl0U3RhdHVzOiBmdW5jdGlvbiBnZXRBdWRpdFN0YXR1cyhyb3cpIHsKICAgICAgcmV0dXJuIHRoaXMuQ29tcG9uZW50X1NoaXBwaW5nX0FwcHJvdmFsICYmICh0aGlzLlNoaXBwaW5nX0FwcHJvdmFsX0xvd2VyTGltaXQgfHwgMCkgKiAxMDAwIDwgcm93LlByb2plY3RfU2VuZGluZ19XZWlnaHQ7CiAgICB9LAogICAgZ2V0RmFjdG9yeVR5cGVPcHRpb246IGZ1bmN0aW9uIGdldEZhY3RvcnlUeXBlT3B0aW9uKCkgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlMygpIHsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTMkKF9jb250ZXh0MykgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQzLnByZXYgPSBfY29udGV4dDMubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX2NvbnRleHQzLm5leHQgPSAyOwogICAgICAgICAgICAgIHJldHVybiBHZXRGYWN0b3J5UHJvZmVzc2lvbmFsQnlDb2RlKHsKICAgICAgICAgICAgICAgIGZhY3RvcnlJZDogbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ0N1clJlZmVyZW5jZUlkJykKICAgICAgICAgICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzNS5Qcm9mZXNzaW9uYWxUeXBlID0gcmVzLkRhdGE7CiAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICBfdGhpczUuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICBfY29udGV4dDMubmV4dCA9IDQ7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzNS5nZXRUYWJsZUNvbmZpZygicHJvX2NvbXBvbmVudF9vdXRfYmlsbF9saXN0LCIuY29uY2F0KF90aGlzNS5Qcm9mZXNzaW9uYWxUeXBlWzBdLkNvZGUpKTsKICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgIF90aGlzNS5mZXRjaERhdGEoKTsKICAgICAgICAgICAgY2FzZSA1OgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDMuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUzKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgZmV0Y2hEYXRhOiBmdW5jdGlvbiBmZXRjaERhdGEoKSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwogICAgICB0aGlzLnRiTG9hZGluZyA9IHRydWU7CiAgICAgIHZhciBmb3JtID0gX29iamVjdFNwcmVhZCh7fSwgdGhpcy5mb3JtKTsKICAgICAgZGVsZXRlIGZvcm1bJ2RhdGVSYW5nZSddOwogICAgICB0aGlzLmZvcm0uZGF0ZVJhbmdlID0gdGhpcy5mb3JtLmRhdGVSYW5nZSB8fCBbXTsKICAgICAgZm9ybS5CZWdpbkRhdGUgPSBwYXJzZVRpbWUodGhpcy5mb3JtLmRhdGVSYW5nZVswXSkgPyBwYXJzZVRpbWUodGhpcy5mb3JtLmRhdGVSYW5nZVswXSkgOiAnJzsKICAgICAgZm9ybS5FbmREYXRlID0gcGFyc2VUaW1lKHRoaXMuZm9ybS5kYXRlUmFuZ2VbMV0pID8gcGFyc2VUaW1lKHRoaXMuZm9ybS5kYXRlUmFuZ2VbMV0pIDogJyc7CiAgICAgIEdldFByb2plY3RTZW5kaW5nSW5mb1BhZ2VsaXN0KGZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICBfdGhpczYudGJEYXRhID0gcmVzLkRhdGEuRGF0YS5tYXAoZnVuY3Rpb24gKHYpIHsKICAgICAgICAgICAgdi5TZW5kRGF0ZSA9IHYuU2VuZERhdGUgPyBwYXJzZVRpbWUobmV3IERhdGUodi5TZW5kRGF0ZSksICd7eX0te219LXtkfScpIDogdi5TZW5kRGF0ZTsKICAgICAgICAgICAgcmV0dXJuIHY7CiAgICAgICAgICB9KTsKICAgICAgICAgIC8vIHRoaXMudGJEYXRhID0gcmVzLkRhdGEuRGF0YTsKICAgICAgICAgIF90aGlzNi50b3RhbCA9IHJlcy5EYXRhLlRvdGFsQ291bnQ7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzNi4kbWVzc2FnZSh7CiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgICAgX3RoaXM2LnRiTG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgICAgR2V0UHJvamVjdFNlbmRpbmdBbGxDb3VudChfb2JqZWN0U3ByZWFkKHt9LCBmb3JtKSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIC8vIGNvbnNvbGUubG9nKHJlcy5EYXRhLCJyZXMuRGF0YSIpOwogICAgICAgICAgX3RoaXM2LnRvdGFsRGF0YSA9IHJlcy5EYXRhOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpczYuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBnZXRQYWdlTGlzdDogZnVuY3Rpb24gZ2V0UGFnZUxpc3QoKSB7CiAgICAgIHRoaXMuZmV0Y2hEYXRhKCk7CiAgICAgIGNvbnNvbGUubG9nKHRoaXMuZm9ybSwgJ3RoaXMuZm9ybScpOwogICAgfSwKICAgIGhhbmRTdWJtaXQ6IGZ1bmN0aW9uIGhhbmRTdWJtaXQocm93KSB7CiAgICAgIHZhciBfdGhpczcgPSB0aGlzOwogICAgICB2YXIgSWQgPSByb3cuSWQ7CiAgICAgIGlmICghcm93LlZlaGljbGVObykgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgbWVzc2FnZTogJ+WPkei0p+WNlei9pui+huS/oeaBr+acquWujOWWhO+8jOivt+WujOWWhOWQjuaPkOS6pCcsCiAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICB9KTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm5o+Q5Lqk6L+H56OFPycsICfmj5DnpLonLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBTdWJtaXRXZWlnaGluZ0ZvclBDKHsKICAgICAgICAgIElkOiBJZAogICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgICAgX3RoaXM3LmZldGNoRGF0YSgpOwogICAgICAgICAgICBfdGhpczcuJG1lc3NhZ2UoewogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5EYXRhLAogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIF90aGlzNy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzNy4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAnaW5mbycsCiAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raIJwogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICBkYXRlUGlja2Vyd3JhcHBlcjogZnVuY3Rpb24gZGF0ZVBpY2tlcndyYXBwZXIoKSB7CiAgICAgIC8vIGNvbnNvbGUubG9nKGZvcm0sImZvcm0xMTExMTExMTExIik7CiAgICAgIC8vIGNvbnNvbGUubG9nKG5ldyBEYXRlKCIyMDIyLTA5LTI4VDEwOjEyOjM1LjU4M1oiKSwibmV3IERhdGUxMTEiKTsKICAgICAgLy8gY29uc29sZS5sb2cocGFyc2VUaW1lKG5ldyBEYXRlKCIyMDIyLTEwLTExVDE0OjAzOjU0IikpLCJuZXcgRGF0ZTIyMiIpOwogICAgICBpZiAoIXRoaXMuZm9ybS5kYXRlUmFuZ2UpIHsKICAgICAgICB0aGlzLmZvcm0uZGF0ZVJhbmdlID0gWycnLCAnJ107CiAgICAgIH0KICAgICAgdGhpcy5mZXRjaERhdGEoKTsKICAgIH0sCiAgICByZXNldEZvcm06IGZ1bmN0aW9uIHJlc2V0Rm9ybShmb3JtTmFtZSkgewogICAgICB0aGlzLiRyZWZzW2Zvcm1OYW1lXS5yZXNldEZpZWxkcygpOwogICAgICB0aGlzLmZldGNoRGF0YSgpOwogICAgfSwKICAgIHN1Ym1pdEZvcm0yOiBmdW5jdGlvbiBzdWJtaXRGb3JtMihmb3JtTmFtZSkgewogICAgICB2YXIgX3RoaXM4ID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1tmb3JtTmFtZV0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB2YXIgX3RoaXM4JGZvcm0gPSBfdGhpczguZm9ybTIsCiAgICAgICAgICAgIFByb2plY3RJZCA9IF90aGlzOCRmb3JtLlByb2plY3RJZCwKICAgICAgICAgICAgQXJlYV9JZCA9IF90aGlzOCRmb3JtLkFyZWFfSWQsCiAgICAgICAgICAgIEluc3RhbGxVbml0X0lkID0gX3RoaXM4JGZvcm0uSW5zdGFsbFVuaXRfSWQ7CiAgICAgICAgICB2YXIgX3RoaXM4JHByb2plY3RzJGZpbmQgPSBfdGhpczgucHJvamVjdHMuZmluZChmdW5jdGlvbiAodikgewogICAgICAgICAgICAgIHJldHVybiB2LklkID09PSBfdGhpczguZm9ybTIuUHJvamVjdElkOwogICAgICAgICAgICB9KSwKICAgICAgICAgICAgTmFtZSA9IF90aGlzOCRwcm9qZWN0cyRmaW5kLk5hbWUsCiAgICAgICAgICAgIElkID0gX3RoaXM4JHByb2plY3RzJGZpbmQuSWQsCiAgICAgICAgICAgIENvZGUgPSBfdGhpczgkcHJvamVjdHMkZmluZC5Db2RlLAogICAgICAgICAgICBTUElDX1VzZXJOYW1lID0gX3RoaXM4JHByb2plY3RzJGZpbmQuU1BJQ19Vc2VyTmFtZSwKICAgICAgICAgICAgQWRkcmVzcyA9IF90aGlzOCRwcm9qZWN0cyRmaW5kLkFkZHJlc3MsCiAgICAgICAgICAgIFJlY2VpdmVyID0gX3RoaXM4JHByb2plY3RzJGZpbmQuUmVjZWl2ZXIsCiAgICAgICAgICAgIFJlY2VpdmVyX1RlbCA9IF90aGlzOCRwcm9qZWN0cyRmaW5kLlJlY2VpdmVyX1RlbCwKICAgICAgICAgICAgU3lzX1Byb2plY3RfSWQgPSBfdGhpczgkcHJvamVjdHMkZmluZC5TeXNfUHJvamVjdF9JZCwKICAgICAgICAgICAgUmVjZWl2ZV9Vc2VyTmFtZSA9IF90aGlzOCRwcm9qZWN0cyRmaW5kLlJlY2VpdmVfVXNlck5hbWU7CiAgICAgICAgICB2YXIgZGF0YSA9IHsKICAgICAgICAgICAgUHJvamVjdElkOiBQcm9qZWN0SWQsCiAgICAgICAgICAgIEFyZWFfSWQ6IEFyZWFfSWQsCiAgICAgICAgICAgIEluc3RhbGxVbml0X0lkOiBJbnN0YWxsVW5pdF9JZCwKICAgICAgICAgICAgSWQ6IElkLAogICAgICAgICAgICBOYW1lOiBOYW1lLAogICAgICAgICAgICBDb2RlOiBDb2RlLAogICAgICAgICAgICBBZGRyZXNzOiBBZGRyZXNzLAogICAgICAgICAgICBSZWNlaXZlcjogUmVjZWl2ZXIsCiAgICAgICAgICAgIFJlY2VpdmVyX1RlbDogUmVjZWl2ZXJfVGVsLAogICAgICAgICAgICBTeXNfUHJvamVjdF9JZDogU3lzX1Byb2plY3RfSWQsCiAgICAgICAgICAgIFJlY2VpdmVfVXNlck5hbWU6IFJlY2VpdmVfVXNlck5hbWUsCiAgICAgICAgICAgIGF1dG9HZW5lcmF0ZTogX3RoaXM4LmF1dG9HZW5lcmF0ZSwKICAgICAgICAgICAgUHJvZmVzc2lvbmFsVHlwZTogX3RoaXM4LlByb2Zlc3Npb25hbFR5cGUKICAgICAgICAgIH07CiAgICAgICAgICBfdGhpczguJHJvdXRlci5wdXNoKHsKICAgICAgICAgICAgbmFtZTogJ1BST1NoaXBTZW50QWRkJywKICAgICAgICAgICAgcXVlcnk6IHsKICAgICAgICAgICAgICBwZ19yZWRpcmVjdDogJ1BST1NoaXBTZW50JywKICAgICAgICAgICAgICBwOiBlbmNvZGVVUklDb21wb25lbnQoSlNPTi5zdHJpbmdpZnkoZGF0YSkpCiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgICAgX3RoaXM4LmRpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgICAgIF90aGlzOC4kcmVmcy5mb3JtMi5yZXNldEZpZWxkcygpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBjb25zb2xlLmxvZygnZXJyb3Igc3VibWl0ISEnKTsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIHJlc2V0Rm9ybTI6IGZ1bmN0aW9uIHJlc2V0Rm9ybTIoZm9ybU5hbWUpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgIHRoaXMuJHJlZnNbZm9ybU5hbWVdLnJlc2V0RmllbGRzKCk7CiAgICB9LAogICAgaGFuZGxlQ2xvc2U6IGZ1bmN0aW9uIGhhbmRsZUNsb3NlKCkgewogICAgICB0aGlzLiRyZWZzLmZvcm0yLnJlc2V0RmllbGRzKCk7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgfSwKICAgIGhhbmRsZUFkZDogZnVuY3Rpb24gaGFuZGxlQWRkKCkgewogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgICAvLyB0aGlzLiRyb3V0ZXIucHVzaCh7IG5hbWU6ICdQUk9TaGlwU2VudEFkZCcsIHF1ZXJ5OiB7IHBnX3JlZGlyZWN0OiAnUFJPU2hpcFNlbnQnIH19KQogICAgfSwKICAgIGdldFByb2plY3RQYWdlTGlzdDogZnVuY3Rpb24gZ2V0UHJvamVjdFBhZ2VMaXN0KCkgewogICAgICB2YXIgX3RoaXM5ID0gdGhpczsKICAgICAgR2V0UHJvamVjdFBhZ2VMaXN0KHsKICAgICAgICBQYWdlU2l6ZTogLTEKICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIF90aGlzOS5wcm9qZWN0cyA9IHJlcy5EYXRhLkRhdGE7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBwcm9qZWN0SWRDaGFuZ2U6IGZ1bmN0aW9uIHByb2plY3RJZENoYW5nZShlKSB7CiAgICAgIGNvbnNvbGUubG9nKGUsICdlJyk7CiAgICAgIC8vIGlmIChlKSB7CiAgICAgIC8vICAgdGhpcy5nZXRBcmVhTGlzdCgpOwogICAgICAvLyB9CiAgICB9LAogICAgcHJvamVjdElkQ2xlYXI6IGZ1bmN0aW9uIHByb2plY3RJZENsZWFyKGUpIHsKICAgICAgdGhpcy4kcmVmcy5mb3JtMi5yZXNldEZpZWxkcygpOwogICAgfSwKICAgIC8vIOiOt+WPluWMuuWfnwogICAgZ2V0QXJlYUxpc3Q6IGZ1bmN0aW9uIGdldEFyZWFMaXN0KCkgewogICAgICB2YXIgX3RoaXMwID0gdGhpczsKICAgICAgR2VBcmVhVHJlZXMoewogICAgICAgIHByb2plY3RJZDogdGhpcy5mb3JtMi5Qcm9qZWN0SWQKICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIF90aGlzMC50cmVlUGFyYW1zQXJlYS5kYXRhID0gcmVzLkRhdGE7CiAgICAgICAgICBfdGhpczAuJG5leHRUaWNrKGZ1bmN0aW9uIChfKSB7CiAgICAgICAgICAgIF90aGlzMC4kcmVmcy50cmVlU2VsZWN0QXJlYS50cmVlRGF0YVVwZGF0ZUZ1bihyZXMuRGF0YSk7CiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXMwLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgZmlsdGVyRnVuOiBmdW5jdGlvbiBmaWx0ZXJGdW4odmFsLCByZWYpIHsKICAgICAgdGhpcy4kcmVmc1tyZWZdLmZpbHRlckZ1bih2YWwpOwogICAgfSwKICAgIGFyZWFDaGFuZ2U6IGZ1bmN0aW9uIGFyZWFDaGFuZ2UoZSkgewogICAgICBjb25zb2xlLmxvZyhlLCAnZScpOwogICAgICB0aGlzLmdldEluc3RhbGwoKTsKICAgIH0sCiAgICAvLyDmuIXnqbrljLrln58KICAgIGFyZWFDbGVhcjogZnVuY3Rpb24gYXJlYUNsZWFyKCkgewogICAgICB0aGlzLmZvcm0yLkFyZWFfSWQgPSAnJzsKICAgICAgdGhpcy5mb3JtLkluc3RhbGxVbml0X0lkID0gJyc7CiAgICB9LAogICAgLy8g6I635Y+W5om55qyhCiAgICBnZXRJbnN0YWxsOiBmdW5jdGlvbiBnZXRJbnN0YWxsKCkgewogICAgICB2YXIgX3RoaXMxID0gdGhpczsKICAgICAgR2V0SW5zdGFsbFVuaXRQYWdlTGlzdCh7CiAgICAgICAgQXJlYV9JZDogdGhpcy5mb3JtMi5BcmVhX0lkLAogICAgICAgIFBhZ2U6IDEsCiAgICAgICAgUGFnZVNpemU6IC0xCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICBfdGhpczEuU2V0dXBQb3NpdGlvbkRhdGEgPSByZXMuRGF0YS5EYXRhOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgX3RoaXMxLiRtZXNzYWdlKHsKICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpczEuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVFZGl0OiBmdW5jdGlvbiBoYW5kbGVFZGl0KGlkLCBpc1N1YikgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgbmFtZTogJ1BST1NoaXBTZW50RWRpdCcsCiAgICAgICAgcXVlcnk6IHsKICAgICAgICAgIHBnX3JlZGlyZWN0OiAnUFJPU2hpcFNlbnQnLAogICAgICAgICAgaWQ6IGlkLAogICAgICAgICAgaXNTdWI6IGlzU3ViID8gJzEnIDogJzAnLAogICAgICAgICAgcDogZW5jb2RlVVJJQ29tcG9uZW50KEpTT04uc3RyaW5naWZ5KHsKICAgICAgICAgICAgYXV0b0dlbmVyYXRlOiB0aGlzLmF1dG9HZW5lcmF0ZQogICAgICAgICAgfSkpCiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvLyDmkqTlm57oh7PojYnnqL8KICAgIGhhbmRsZVdpdGhkcmF3OiBmdW5jdGlvbiBoYW5kbGVXaXRoZHJhdyhpZCkgewogICAgICB2YXIgX3RoaXMxMCA9IHRoaXM7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aSpOWbnuiHs+iNieeovywg5piv5ZCm57un57utPycsICfmj5DnpLonLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBXaXRoZHJhd0RyYWZ0KHsKICAgICAgICAgIGlkOiBpZAogICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgICAgX3RoaXMxMC4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgbWVzc2FnZTogJ+aSpOmUgOaIkOWKnycsCiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICBfdGhpczEwLmZldGNoRGF0YSgpOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgX3RoaXMxMC4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkge30pOwogICAgfSwKICAgIGhhbmRsZVN1YjogZnVuY3Rpb24gaGFuZGxlU3ViKHJvdykgewogICAgICB2YXIgX3RoaXMxMSA9IHRoaXM7CiAgICAgIHZhciBpZCA9IHJvdy5JZDsKICAgICAgaWYgKCFyb3cuVmVoaWNsZU5vKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICBtZXNzYWdlOiAn5Y+R6LSn5Y2V6L2m6L6G5L+h5oGv5pyq5a6M5ZaE77yM6K+35a6M5ZaE5ZCO5o+Q5LqkJywKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgIH0pOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLiRjb25maXJtKCfmj5DkuqTor6Xlj5HotKfljZUsIOaYr+WQpue7p+e7rT8nLCAn5o+Q56S6JywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMxMS5wYWdlTG9hZGluZyA9IHRydWU7CiAgICAgICAgU3VibWl0UHJvamVjdFNlbmRpbmcoewogICAgICAgICAgSWQ6IGlkCiAgICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICBfdGhpczExLiRtZXNzYWdlKHsKICAgICAgICAgICAgICBtZXNzYWdlOiAn5Y+R6LSn5oiQ5YqfJywKICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIF90aGlzMTEuZmV0Y2hEYXRhKCk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBfdGhpczExLiRtZXNzYWdlKHsKICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgICAgX3RoaXMxMS5wYWdlTG9hZGluZyA9IGZhbHNlOwogICAgICAgIH0pOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMxMS5wYWdlTG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVEZWw6IGZ1bmN0aW9uIGhhbmRsZURlbChpZCkgewogICAgICB2YXIgX3RoaXMxMiA9IHRoaXM7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuWIoOmZpOivpeWPkei0p+WNlT8nLCAn5o+Q56S6JywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgRGVsZXRlUHJvamVjdFNlbmRpbmdJbmZvKHsKICAgICAgICAgIElkOiBpZAogICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgICAgX3RoaXMxMi4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKnycsCiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICBfdGhpczEyLmZldGNoRGF0YSgpOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgX3RoaXMxMi4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMTIuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ2luZm8nLAogICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iOWIoOmZpCcKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlSW5mbzogZnVuY3Rpb24gaGFuZGxlSW5mbyhpZCkgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgbmFtZTogJ1BST1NoaXBTZW50RGV0YWlsJywKICAgICAgICBxdWVyeTogewogICAgICAgICAgcGdfcmVkaXJlY3Q6ICdQUk9TaGlwU2VudCcsCiAgICAgICAgICBpZDogaWQKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZUNoYW5nZTogZnVuY3Rpb24gaGFuZGxlQ2hhbmdlKGlkKSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBuYW1lOiAnUFJPU2hpcFNlbnRDaGFuZ2VSZWNvcmQnLAogICAgICAgIHF1ZXJ5OiB7CiAgICAgICAgICBwZ19yZWRpcmVjdDogJ1BST1NoaXBTZW50JywKICAgICAgICAgIGlkOiBpZAogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy8gcHJpbnRNZSgpIHt9LAogICAgaGFuZGxlRXhwb3J0OiBmdW5jdGlvbiBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIHZhciBfdGhpczEzID0gdGhpczsKICAgICAgdGhpcy50aXRsZSA9ICflr7zlh7onOwogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoXykgewogICAgICAgIF90aGlzMTMuJHJlZnMucmFkaW9EaWFsb2cuaGFuZGxlT3BlbihfdGhpczEzLnNlbGVjdExpc3QpOwogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVFeHBvcnRFeGNlbDogZnVuY3Rpb24gaGFuZGxlRXhwb3J0RXhjZWwoKSB7CiAgICAgIHRoaXMudGl0bGUgPSAn5a+85Ye6JzsKICAgICAgdGhpcy4kcmVmcy5kaWFsb2dFeGNlbC5oYW5kbGVPcGVuKHRoaXMuc2VsZWN0TGlzdCk7CiAgICB9LAogICAgaGFuZGxlUHJpbnQ6IGZ1bmN0aW9uIGhhbmRsZVByaW50KCkgewogICAgICB0aGlzLnRpdGxlID0gJ+aJk+WNsCc7CiAgICAgIHRoaXMuJHJlZnMucmFkaW9EaWFsb2cuaGFuZGxlT3Blbih0aGlzLnNlbGVjdExpc3QpOwogICAgfSwKICAgIGhhbmRsZVByaW50Tm9XZWlnaHQ6IGZ1bmN0aW9uIGhhbmRsZVByaW50Tm9XZWlnaHQoKSB7CiAgICAgIHZhciBfdGhpczE0ID0gdGhpczsKICAgICAgLy8gY29uc29sZS5sb2codGhpcy5zZWxlY3RSb3cuQ29kZSwgInRoaXMuc2VsZWN0Um93LkNvZGUiKTsKICAgICAgLy8gY29uc29sZS5sb2codGhpcy5zZWxlY3RSb3cuSWQsICJ0aGlzLnNlbGVjdFJvdy5JZCIpOwogICAgICBUcmFuc2Zvcm1zV2l0aG91dFdlaWdodCh7CiAgICAgICAgc2VuZElkOiB0aGlzLnNlbGVjdFJvdy5JZAogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgLy8gY29uc3QgdGVtcGxhdGVVcmwgPSBjb21iaW5lVVJMKHRoaXMuJGJhc2VVcmwsIHJlcy5EYXRhKTsKICAgICAgICAgIC8vIHdpbmRvdy5vcGVuKHRlbXBsYXRlVXJsLCAiX2JsYW5rIik7CiAgICAgICAgICB2YXIgdXJsID0gbmV3IFVSTChyZXMuRGF0YSwgYmFzZVVybCgpKTsKICAgICAgICAgIHdpbmRvdy5vcGVuKHVybC5ocmVmLCAnX2JsYW5rJyk7CiAgICAgICAgICBfdGhpczE0LiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICBtZXNzYWdlOiAn5omT5Y2w5oiQ5YqfIScKICAgICAgICAgIH0pOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpczE0LiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICAgIC8vIHRoaXMuJHJlZnMuUHJpbnREaWFsb2cub3Blbih0aGlzLnNlbGVjdFJvdy5Db2RlKQogICAgfSwKICAgIC8vIOWvvOWHugogICAgaGFuZGxlTGVhZGluZ091dDogZnVuY3Rpb24gaGFuZGxlTGVhZGluZ091dCgpIHsKICAgICAgdmFyIF90aGlzMTUgPSB0aGlzOwogICAgICB0aGlzLmJ0bkxvYWRpbmcgPSB0cnVlOwogICAgICB2YXIgZm9ybSA9IF9vYmplY3RTcHJlYWQoe30sIHRoaXMuZm9ybSk7CiAgICAgIGRlbGV0ZSBmb3JtWydkYXRlUmFuZ2UnXTsKICAgICAgZGVsZXRlIGZvcm1bJ1BhZ2VJbmZvJ107CiAgICAgIHRoaXMuZm9ybS5kYXRlUmFuZ2UgPSB0aGlzLmZvcm0uZGF0ZVJhbmdlIHx8IFtdOwogICAgICBmb3JtLkJlZ2luRGF0ZSA9IHBhcnNlVGltZSh0aGlzLmZvcm0uZGF0ZVJhbmdlWzBdKSA/IHBhcnNlVGltZSh0aGlzLmZvcm0uZGF0ZVJhbmdlWzBdKSA6ICcnOwogICAgICBmb3JtLkVuZERhdGUgPSBwYXJzZVRpbWUodGhpcy5mb3JtLmRhdGVSYW5nZVsxXSkgPyBwYXJzZVRpbWUodGhpcy5mb3JtLmRhdGVSYW5nZVsxXSkgOiAnJzsKICAgICAgRXhwb3J0SW52b2ljZUxpc3QoX29iamVjdFNwcmVhZCh7fSwgZm9ybSkpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICBfdGhpczE1LiRtZXNzYWdlLnN1Y2Nlc3MoJ+WvvOWHuuaIkOWKnycpOwogICAgICAgICAgdmFyIHVybCA9IG5ldyBVUkwocmVzLkRhdGEsIGJhc2VVcmwoKSk7CiAgICAgICAgICB3aW5kb3cub3Blbih1cmwuaHJlZik7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzMTUuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KS5maW5hbGx5KGZ1bmN0aW9uICgpIHsKICAgICAgICAvLyDnu5PmnZ9sb2FkaW5nCiAgICAgICAgX3RoaXMxNS5idG5Mb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZTogZnVuY3Rpb24gaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKGxpc3QpIHsKICAgICAgdGhpcy5zZWxlY3RMaXN0ID0gbGlzdDsKICAgIH0sCiAgICBzZWxlY3RDaGFuZ2U6IGZ1bmN0aW9uIHNlbGVjdENoYW5nZShfcmVmKSB7CiAgICAgIHZhciBzZWxlY3Rpb24gPSBfcmVmLnNlbGVjdGlvbiwKICAgICAgICByb3cgPSBfcmVmLnJvdzsKICAgICAgdGhpcy4kcmVmcy5keVRhYmxlLiRyZWZzLmR0YWJsZS5jbGVhclNlbGVjdGlvbigpOwogICAgICBpZiAoc2VsZWN0aW9uLmxlbmd0aCAhPSAwKSB7CiAgICAgICAgdGhpcy5zZWxlY3RSb3cgPSByb3c7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5zZWxlY3RSb3cgPSB7fTsKICAgICAgfQogICAgICBpZiAoc2VsZWN0aW9uLmxlbmd0aCA+IDEpIHsKICAgICAgICBzZWxlY3Rpb24uc2hpZnQoKTsKICAgICAgfQogICAgICAvLyBjb25zb2xlLmxvZyhzZWxlY3Rpb24sICJzZWxlY3Rpb24yIik7CiAgICAgIHRoaXMuJHJlZnMuZHlUYWJsZS4kcmVmcy5kdGFibGUudG9nZ2xlUm93U2VsZWN0aW9uKHJvdywgISFzZWxlY3Rpb24ubGVuZ3RoKTsKICAgIH0sCiAgICBoYW5kbGVTZWxlY3RBbGw6IGZ1bmN0aW9uIGhhbmRsZVNlbGVjdEFsbCgpIHsKICAgICAgLy8gdGhpcy4kcmVmcy5keVRhYmxlLiRyZWZzLmR0YWJsZS5jbGVhclNlbGVjdGlvbigpCiAgICB9LAogICAgaGFuZGVsVmlldzogZnVuY3Rpb24gaGFuZGVsVmlldyhJZCkgewogICAgICB2YXIgX3RoaXMxNiA9IHRoaXM7CiAgICAgIHRoaXMuSXNWaXNhYmVsID0gdHJ1ZTsKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKF8pIHsKICAgICAgICBfdGhpczE2LiRyZWZzLmNoZWNrRGlhbG9nLmhhbmRlbE9wZW4oSWQpOwogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["Monitor", "TopHeader", "addRouterPage", "DynamicDataTable", "getTbInfo", "GetProjectSendingInfoPagelist", "DeleteProjectSendingInfo", "SubmitProjectSending", "GetProjectSendingAllCount", "TransformsWithoutWeight", "SubmitWeighingForPC", "WithdrawDraft", "ExportInvoiceList", "SubmitApproval", "CancelFlow", "GetProjectPageList", "GeAreaTrees", "GetInstallUnitPageList", "parseTime", "baseUrl", "GetFactoryProfessionalByCode", "PrintDialog", "radioDialog", "dialogExcel", "checkDialog", "mapGetters", "ExportCustomReport", "StatusMap", "components", "filters", "sendDate<PERSON>ilter", "e", "Date", "mixins", "data", "selectList", "statusInfo", "<PERSON><PERSON><PERSON><PERSON>", "btnLoading", "form", "Code", "Status", "ProjectId", "VehicleNo", "Consignee", "IsReturn", "Is_Weight_Warning", "date<PERSON><PERSON><PERSON>", "PageInfo", "Parameter<PERSON>son", "Page", "PageSize", "form2", "Area_Id", "InstallUnit_Id", "rules", "required", "message", "trigger", "pickerOptions", "shortcuts", "text", "onClick", "picker", "end", "start", "setTime", "getTime", "$emit", "selectParams", "clearable", "placeholder", "dialogVisible", "pageLoading", "addPageArray", "path", "$route", "hidden", "component", "Promise", "resolve", "then", "_interopRequireWildcard", "require", "name", "meta", "title", "projects", "treeParamsArea", "filterable", "clickParent", "props", "children", "label", "value", "styles", "width", "SetupPositionData", "queryInfo", "queryInfo2", "BeginDate", "EndDate", "tbConfig", "Pager_<PERSON>gn", "<PERSON>_<PERSON><PERSON>th", "columns", "tbData", "total", "tbLoading", "selectRow", "totalData", "Allsteelamount", "Allsteelweight", "ProfessionalType", "Is_Integration", "computed", "_objectSpread", "selectEmpty", "Object", "keys", "length", "comList", "filter", "item", "activated", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "console", "log", "query", "refresh", "fetchData", "stop", "created", "_this2", "_callee2", "_callee2$", "_context2", "$store", "dispatch", "sent", "getFactoryTypeOption", "getProjectPageList", "mounted", "methods", "handleCancelFlow", "instanceId", "_this3", "$confirm", "confirmButtonText", "cancelButtonText", "type", "res", "IsSucceed", "$message", "Message", "catch", "submitForReview", "row", "_this4", "Id", "handleMonitor", "rowId", "$refs", "opendialog", "getAuditStatus", "Component_Shipping_Approval", "Shipping_Approval_LowerLimit", "Project_Sending_Weight", "_this5", "_callee3", "_callee3$", "_context3", "factoryId", "localStorage", "getItem", "Data", "getTableConfig", "concat", "_this6", "map", "v", "SendDate", "TotalCount", "getPageList", "handSubmit", "_this7", "datePickerwrapper", "resetForm", "formName", "resetFields", "submitForm2", "_this8", "validate", "valid", "_this8$form", "_this8$projects$find", "find", "Name", "SPIC_UserName", "Address", "Receiver", "Receiver_Tel", "Sys_Project_Id", "Receive_UserName", "autoGenerate", "$router", "push", "pg_redirect", "p", "encodeURIComponent", "JSON", "stringify", "resetForm2", "handleClose", "handleAdd", "_this9", "projectIdChange", "projectIdClear", "getAreaList", "_this0", "projectId", "$nextTick", "_", "treeSelectArea", "treeDataUpdateFun", "filterFun", "val", "ref", "areaChange", "getInstall", "areaClear", "_this1", "handleEdit", "id", "isSub", "handleWithdraw", "_this10", "handleSub", "_this11", "handleDel", "_this12", "handleInfo", "handleChange", "handleExport", "_this13", "handleOpen", "handleExportExcel", "handlePrint", "handlePrintNoWeight", "_this14", "sendId", "url", "URL", "window", "open", "href", "handleLeadingOut", "_this15", "success", "finally", "handleSelectionChange", "list", "selectChange", "_ref", "selection", "dyTable", "dtable", "clearSelection", "shift", "toggleRowSelection", "handleSelectAll", "handelView", "_this16", "handelOpen"], "sources": ["src/views/PRO/shipment/actually-sent/v4/index.vue"], "sourcesContent": ["<!--成品发货-->\r\n<template>\r\n  <div\r\n    v-loading=\"pageLoading\"\r\n    class=\"abs100 cs-z-flex-pd16-wrap\"\r\n    style=\"display: flex; flex-direction: column\"\r\n  >\r\n    <div\r\n      class=\"cs-z-page-main-content\"\r\n      style=\"height: auto; margin-bottom: 16px\"\r\n    >\r\n      <top-header style=\"height: 100px; line-height: normal\">\r\n        <template #left>\r\n          <el-form\r\n            ref=\"searchForm\"\r\n            :inline=\"true\"\r\n            :model=\"form\"\r\n            class=\"demo-form-inline form-search\"\r\n            style=\"height: 100px\"\r\n          >\r\n            <el-form-item label=\"发货单号：\" prop=\"Code\">\r\n              <el-input\r\n                v-model=\"form.Code\"\r\n                clearable\r\n                placeholder=\"请输入\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"发货单状态：\" prop=\"Status\">\r\n              <el-select v-model=\"form.Status\" clearable placeholder=\"请选择\">\r\n                <!--                <el-option label=\"未发货\" :value=\"0\" />\r\n                <el-option label=\"已发货\" :value=\"1\" />\r\n                <el-option label=\"部分验收\" :value=\"2\" />\r\n                <el-option label=\"已验收\" :value=\"3\" />-->\r\n                <!--                <el-option label=\"草稿\" :value=\"0\" />\r\n                <el-option label=\"待过磅\" :value=\"2\" />\r\n                <el-option label=\"已过磅\" :value=\"3\" />\r\n                <el-option label=\"未验收\" :value=\"4\" />\r\n                <el-option label=\"部分验收\" :value=\"5\" />\r\n                <el-option label=\"已验收\" :value=\"6\" />-->\r\n\r\n                <el-option v-for=\"(item,key) in statusInfo\" :key=\"key\" :label=\"item\" :value=\"key\" />\r\n\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"项目名称\" prop=\"ProjectId\">\r\n              <el-select\r\n                v-model=\"form.ProjectId\"\r\n                class=\"w100\"\r\n                placeholder=\"请选择\"\r\n                filterable\r\n                clearable\r\n              >\r\n                <el-option\r\n                  v-for=\"item in projects\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Short_Name\"\r\n                  :value=\"item.Sys_Project_Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"收货人：\" prop=\"Consignee\">\r\n              <el-input\r\n                v-model=\"form.Consignee\"\r\n                clearable\r\n                placeholder=\"请输入\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"车牌号：\" prop=\"VehicleNo\">\r\n              <el-input\r\n                v-model=\"form.VehicleNo\"\r\n                clearable\r\n                placeholder=\"请输入\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"是否退货\" prop=\"IsReturn\">\r\n              <el-select v-model=\"form.IsReturn\" clearable placeholder=\"请选择\">\r\n                <el-option label=\"无退货\" :value=\"false\" />\r\n                <el-option label=\"有退货\" :value=\"true\" />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"过磅预警\" prop=\"Is_Weight_Warning\">\r\n              <el-select v-model=\"form.Is_Weight_Warning\" clearable placeholder=\"请选择\">\r\n                <el-option label=\"是\" :value=\"true\" />\r\n                <el-option label=\"否\" :value=\"false\" />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item>\r\n              <el-button\r\n                type=\"primary\"\r\n                @click=\"\r\n                  () => {\r\n                    form.PageInfo.Page = 1;\r\n                    getPageList();\r\n                  }\r\n                \"\r\n              >查询</el-button>\r\n              <el-button @click=\"resetForm('searchForm')\">重置</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </template>\r\n      </top-header>\r\n    </div>\r\n    <div class=\"cs-z-page-main-content\" style=\"flex: 1; display: -webkit-box\">\r\n      <div style=\"color: rgba(34, 40, 52, 0.65); padding: 10px 0px\">\r\n        <el-button type=\"primary\" @click=\"handleAdd\">新增发货单</el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          :disabled=\"!selectList.length\"\r\n          @click=\"handleExport\"\r\n        >导出发货单(pdf)</el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          :disabled=\"selectEmpty\"\r\n          @click=\"handleExportExcel\"\r\n        >导出发货单(excel)</el-button>\r\n        <ExportCustomReport code=\"Shipping_single_template\" style=\"margin:0 10px\" name=\"自定义发货单(excel)\" :ids=\"selectList.map(i=>i.Id)\"></ExportCustomReport>\r\n        <el-button\r\n          type=\"success\"\r\n          :disabled=\"!selectList.length\"\r\n          @click=\"handlePrint\"\r\n        >打印</el-button>\r\n        <!--        <el-button-->\r\n        <!--          type=\"success\"-->\r\n        <!--          @click=\"handlePrintNoWeight\"-->\r\n        <!--          :disabled=\"!selectRow\"-->\r\n        <!--          >预览(无重量)-->\r\n        <!--        </el-button>-->\r\n        <el-button\r\n          type=\"success\"\r\n          :loading=\"btnLoading\"\r\n          :disabled=\"tbData.length === 0\"\r\n          @click=\"handleLeadingOut\"\r\n        >导出</el-button>\r\n        <div class=\"date-picker-wrapper\">\r\n          <el-date-picker\r\n            v-model=\"form.dateRange\"\r\n            style=\"width: 100%\"\r\n            type=\"daterange\"\r\n            align=\"right\"\r\n            unlink-panels\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            :picker-options=\"pickerOptions\"\r\n            @change=\"datePickerwrapper\"\r\n          />\r\n        </div>\r\n        <div v-if=\"ProfessionalType\" class=\"total-wrapper\">\r\n          <span\r\n            style=\"margin: 0 24px 0 12px\"\r\n          >总数：{{ totalData.Allsteelamount }}</span><span>总重：{{ totalData.Allsteelweight }}（{{\r\n            ProfessionalType[0].Unit\r\n          }}）</span>\r\n        </div>\r\n      </div>\r\n      <div\r\n        v-loading=\"tbLoading\"\r\n        class=\"fff cs-z-tb-wrapper\"\r\n        style=\"flex: 1 1 auto\"\r\n      >\r\n        <dynamic-data-table\r\n          ref=\"dyTable\"\r\n          class=\"cs-plm-dy-table\"\r\n          :columns=\"comList\"\r\n          :config=\"tbConfig\"\r\n          :data=\"tbData\"\r\n          :page=\"queryInfo.Page\"\r\n          :total=\"total\"\r\n          border\r\n          stripe\r\n          @gridPageChange=\"handlePageChange\"\r\n          @gridSizeChange=\"handleSizeChange\"\r\n          @select=\"selectChange\"\r\n          @multiSelectedChange=\"handleSelectionChange\"\r\n          @selectAll=\"handleSelectAll\"\r\n        >\r\n          <template slot=\"SumNetWeight\" slot-scope=\"{ row }\">\r\n            {{ row.SumNetWeight | displayValue }}\r\n          </template>\r\n          <template slot=\"Status\" slot-scope=\"{ row }\">\r\n            {{ statusInfo[row.Status] }}\r\n          </template>\r\n          <template slot=\"Number\" slot-scope=\"{ row }\">\r\n            <div>{{ row.Number || '-' }}</div>\r\n          </template>\r\n          <template slot=\"SumReturnCount\" slot-scope=\"{ row }\">\r\n            <span>{{ row.SumReturnCount != null ? row.SumReturnCount : '-' }}</span>\r\n          </template>\r\n          <template slot=\"SumAcceptCount\" slot-scope=\"{ row }\">\r\n            <span>{{ row.SumAcceptCount != null ? row.SumAcceptCount : '-' }}</span>\r\n          </template>\r\n          <template slot=\"SumPendingCount\" slot-scope=\"{ row }\">\r\n            <span>{{ row.SumPendingCount != null ? row.SumPendingCount : '-' }}</span>\r\n          </template>\r\n          <template slot=\"SendDate\" slot-scope=\"{ row }\">\r\n            <div>\r\n              {{ row.SendDate || \"—\" }}\r\n            </div>\r\n          </template>\r\n          <template slot=\"op\" slot-scope=\"{ row, index }\">\r\n            <template v-if=\"row.Status == '0'\">\r\n              <el-button\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handleEdit(row.Id, false)\"\r\n              >编辑</el-button>\r\n              <el-button\r\n                v-if=\"Shipping_Weigh_Enabled\"\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handSubmit(row)\"\r\n              >提交过磅</el-button>\r\n              <template v-else>\r\n                <el-button v-if=\"getAuditStatus(row)\" type=\"text\" @click=\"submitForReview(row)\">提交审核</el-button>\r\n                <el-button\r\n                  v-else\r\n                  :index=\"index\"\r\n                  type=\"text\"\r\n                  @click=\"handleSub(row)\"\r\n                >提交发货</el-button>\r\n              </template>\r\n              <el-button\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                style=\"color:red\"\r\n                @click=\"handleDel(row.Id)\"\r\n              >删除</el-button>\r\n            </template>\r\n            <template v-else>\r\n              <el-button\r\n                v-if=\"row.Status == '2'\"\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handleWithdraw(row.Id)\"\r\n              >撤回草稿</el-button>\r\n              <el-button\r\n                v-if=\"row.Status != '999'\"\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handleEdit(row.Id, row.Status!='-1')\"\r\n              >编辑</el-button>\r\n              <el-button\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handleInfo(row.Id)\"\r\n              >查看</el-button>\r\n              <el-button\r\n                v-if=\"[4,5,6].includes(+row.Status)\"\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handleChange(row.Id)\"\r\n              >变更记录</el-button>\r\n              <el-button\r\n                v-if=\"Is_Integration&&[4,5,6].includes(+row.Status)\"\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handelView(row.Id)\"\r\n              >验收情况</el-button>\r\n            </template>\r\n            <el-button v-if=\"row.Status==999\" type=\"text\" @click=\"handleCancelFlow(row.FlowId)\">撤回</el-button>\r\n            <template v-if=\"row.Status==3\">\r\n              <el-button\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handleWithdraw(row.Id)\"\r\n              >撤回草稿</el-button>\r\n              <el-button v-if=\"getAuditStatus(row)\" type=\"text\" @click=\"submitForReview(row)\">提交审核</el-button>\r\n              <el-button\r\n                v-else\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handleSub(row)\"\r\n              >提交发货</el-button>\r\n            </template>\r\n            <el-button v-if=\"row.FlowId\" type=\"text\" @click=\"handleMonitor(row.FlowId)\">监控</el-button>\r\n            <template v-if=\"row.Status==-1\">\r\n              <el-button v-if=\"Shipping_Weigh_Enabled\" type=\"text\" @click=\"handSubmit(row)\">提交过磅</el-button>\r\n              <template v-else>\r\n                <el-button v-if=\"getAuditStatus(row)\" type=\"text\" @click=\"submitForReview(row)\">提交审核</el-button>\r\n                <el-button\r\n                  v-else\r\n                  :index=\"index\"\r\n                  type=\"text\"\r\n                  @click=\"handleSub(row)\"\r\n                >提交发货</el-button>\r\n              </template>\r\n            </template>\r\n          </template>\r\n          <!-- <template slot=\"Out_Date\" slot-scope=\"{ row }\">\r\n            {{ row.Out_Date | timeFormat }}\r\n          </template> -->\r\n        </dynamic-data-table>\r\n      </div>\r\n      <el-dialog\r\n        v-dialogDrag\r\n        title=\"新增发货单\"\r\n        class=\"plm-custom-dialog\"\r\n        :visible.sync=\"dialogVisible\"\r\n        width=\"30%\"\r\n        @close=\"handleClose\"\r\n      >\r\n        <el-form\r\n          ref=\"form2\"\r\n          :model=\"form2\"\r\n          :rules=\"rules\"\r\n          label-width=\"70px\"\r\n          class=\"demo-ruleForm\"\r\n        >\r\n          <el-form-item label=\"项目\" prop=\"ProjectId\">\r\n            <el-select\r\n              v-model=\"form2.ProjectId\"\r\n              class=\"w100\"\r\n              placeholder=\"请选择\"\r\n              filterable\r\n              clearable\r\n              @change=\"projectIdChange\"\r\n              @clear=\"projectIdClear\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in projects\"\r\n                :key=\"item.Id\"\r\n                :label=\"item.Short_Name\"\r\n                :value=\"item.Id\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item style=\"text-align: right\">\r\n            <el-button @click=\"resetForm2('form2')\">取 消</el-button>\r\n            <el-button\r\n              type=\"primary\"\r\n              @click=\"submitForm2('form2')\"\r\n            >确 定</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-dialog>\r\n    </div>\r\n    <PrintDialog ref=\"PrintDialog\" />\r\n    <radioDialog\r\n      ref=\"radioDialog\"\r\n      :send-id=\"selectRow.Id\"\r\n      :title=\"title\"\r\n      :send-data=\"selectRow\"\r\n    />\r\n    <dialogExcel\r\n      ref=\"dialogExcel\"\r\n      :send-id=\"selectRow.Id\"\r\n      :title=\"title\"\r\n      :send-data=\"selectRow\"\r\n    />\r\n    <checkDialog ref=\"checkDialog\" />\r\n    <Monitor ref=\"monitor\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Monitor from '@/components/Monitor/index.vue'\r\nimport TopHeader from '@/components/TopHeader/index.vue'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable.vue'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info-pro2'\r\nimport {\r\n  GetProjectSendingInfoPagelist,\r\n  DeleteProjectSendingInfo,\r\n  SubmitProjectSending,\r\n  GetProjectSendingAllCount,\r\n  TransformsWithoutWeight,\r\n  SubmitWeighingForPC,\r\n  WithdrawDraft,\r\n  ExportInvoiceList, SubmitApproval, CancelFlow\r\n} from '@/api/PRO/component-stock-out'\r\nimport { GetProjectPageList } from '@/api/PRO/pro-schedules'\r\nimport { GeAreaTrees } from '@/api/PRO/project'\r\nimport { GetInstallUnitPageList } from '@/api/PRO/install-unit'\r\nimport { parseTime } from '@/utils'\r\nimport { baseUrl } from '@/utils/baseurl'\r\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\r\nimport PrintDialog from './component/printDialog.vue'\r\nimport radioDialog from './component/dialog.vue'\r\nimport dialogExcel from './component/dialogExcel.vue'\r\nimport checkDialog from './component/check.vue'\r\nimport { mapGetters } from 'vuex'\r\nimport ExportCustomReport from \"@/components/ExportCustomReport/index.vue\";\r\n\r\nconst StatusMap = {\r\n  0: '草稿',\r\n  2: '待过磅',\r\n  3: '已过磅',\r\n  4: '未验收',\r\n  5: '部分验收',\r\n  6: '已验收',\r\n  7: '已退货',\r\n  999: '审批中',\r\n  '-1': '已退回'\r\n}\r\n\r\nexport default {\r\n  components: {\r\n    ExportCustomReport,\r\n    TopHeader,\r\n    Monitor,\r\n    DynamicDataTable,\r\n    PrintDialog,\r\n    radioDialog,\r\n    checkDialog,\r\n    dialogExcel\r\n  },\r\n  filters: {\r\n    sendDateFilter(e) {\r\n      // console.log(e,\"eeee\");\r\n      return parseTime(new Date(e))\r\n    }\r\n  },\r\n  mixins: [addRouterPage, getTbInfo],\r\n  data() {\r\n    return {\r\n      selectList: [],\r\n      statusInfo: StatusMap,\r\n      IsVisabel: false,\r\n      btnLoading: false,\r\n      form: {\r\n        Code: '',\r\n        Status: null,\r\n        ProjectId: '',\r\n        VehicleNo: '',\r\n        Consignee: '',\r\n        IsReturn: null,\r\n        Is_Weight_Warning: null,\r\n        dateRange: ['', ''],\r\n        PageInfo: {\r\n          ParameterJson: [],\r\n          Page: 1,\r\n          PageSize: 20\r\n        }\r\n      },\r\n      form2: {\r\n        ProjectId: '',\r\n        Area_Id: '',\r\n        InstallUnit_Id: ''\r\n      },\r\n      rules: {\r\n        ProjectId: [{ required: true, message: '请选择', trigger: 'change' }]\r\n        // Area_Id: [{ required: true, message: \"请选择\", trigger: \"change\" }],\r\n      },\r\n      pickerOptions: {\r\n        shortcuts: [\r\n          {\r\n            text: '今天',\r\n            onClick(picker) {\r\n              const end = new Date()\r\n              const start = new Date()\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 1)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          },\r\n          {\r\n            text: '最近一周',\r\n            onClick(picker) {\r\n              const end = new Date()\r\n              const start = new Date()\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          },\r\n          {\r\n            text: '最近一个月',\r\n            onClick(picker) {\r\n              const end = new Date()\r\n              const start = new Date()\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      selectParams: {\r\n        clearable: true,\r\n        placeholder: '请选择'\r\n      },\r\n      ProjectId: '',\r\n      dialogVisible: false,\r\n      pageLoading: false,\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/add',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/shipment/actually-sent/v4/add.vue'),\r\n          name: 'PROShipSentAdd',\r\n          meta: { title: '新建发货单' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/edit',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/shipment/actually-sent/v4/edit.vue'),\r\n          name: 'PROShipSentEdit',\r\n          meta: { title: '编辑发货单' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/detail',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/shipment/actually-sent/v4/detail.vue'),\r\n          name: 'PROShipSentDetail',\r\n          meta: { title: '详情' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/changeRecord',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/shipment/actually-sent/v4/changeRecord.vue'),\r\n          name: 'PROShipSentChangeRecord',\r\n          meta: { title: '发货单变更记录' }\r\n        }\r\n      ],\r\n      projects: [],\r\n      // 区域数据\r\n      treeParamsArea: {\r\n        'check-strictly': true,\r\n        'expand-on-click-node': false,\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      styles: { width: '100%' },\r\n      SetupPositionData: [],\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: 10,\r\n        ParameterJson: []\r\n      },\r\n      queryInfo2: {\r\n        BeginDate: '',\r\n        EndDate: '',\r\n        PageInfo: {\r\n          ParameterJson: [],\r\n          Page: 1,\r\n          PageSize: 2\r\n        }\r\n      },\r\n      tbConfig: {\r\n        Pager_Align: 'center',\r\n        Op_Width: 280\r\n      },\r\n      columns: [],\r\n      tbData: [],\r\n      total: 0,\r\n      tbLoading: false,\r\n      selectRow: {},\r\n      totalData: {\r\n        Allsteelamount: 0,\r\n        Allsteelweight: 0\r\n      },\r\n      ProfessionalType: null,\r\n      title: '',\r\n      Is_Integration: false // 是否一体化\r\n    }\r\n  },\r\n  computed: {\r\n    selectEmpty() {\r\n      return Object.keys(this.selectRow).length === 0\r\n    },\r\n    comList() {\r\n      if (!this.Is_Integration) {\r\n        return this.columns.filter((item) => {\r\n          return (\r\n            item.Code !== 'SumAcceptCount' && item.Code !== 'SumPendingCount'\r\n          )\r\n        })\r\n      } else {\r\n        return this.columns\r\n      }\r\n    },\r\n    ...mapGetters('factoryInfo', ['Component_Shipping_Approval', 'autoGenerate', 'Shipping_Approval_LowerLimit', 'Shipping_Weigh_Enabled'])\r\n  },\r\n  async activated() {\r\n    console.log('activated')\r\n    if (this.$route.query.refresh) {\r\n      this.fetchData()\r\n    }\r\n    // this.fetchData()\r\n  },\r\n  async created() {\r\n    this.$store.dispatch('factoryInfo/getWorkshop')\r\n\r\n    this.Is_Integration = await this.$store.dispatch('user/getPreferenceSetting', 'Is_Integration')\r\n    this.getFactoryTypeOption()\r\n    this.getProjectPageList()\r\n  },\r\n  mounted() {\r\n    console.log('mounted')\r\n  },\r\n  methods: {\r\n    handleCancelFlow(instanceId) {\r\n      this.$confirm('是否撤回?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        CancelFlow({\r\n          instanceId\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '操作成功',\r\n              type: 'success'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    submitForReview(row) {\r\n      const Id = row.Id\r\n      if (!row.VehicleNo) {\r\n        this.$message({\r\n          message: '发货单车辆信息未完善，请完善后提交',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n\r\n      this.$confirm('是否提交审核?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        SubmitApproval({\r\n          Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.fetchData()\r\n            this.$message({\r\n              message: '操作成功',\r\n              type: 'success'\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    handleMonitor(rowId) {\r\n      this.$refs['monitor'].opendialog(rowId, false)\r\n    },\r\n    getAuditStatus(row) {\r\n      return this.Component_Shipping_Approval && (this.Shipping_Approval_LowerLimit || 0) * 1000 < row.Project_Sending_Weight\r\n    },\r\n    async getFactoryTypeOption() {\r\n      await GetFactoryProfessionalByCode({\r\n        factoryId: localStorage.getItem('CurReferenceId')\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.ProfessionalType = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      await this.getTableConfig(\r\n        `pro_component_out_bill_list,${this.ProfessionalType[0].Code}`\r\n      )\r\n      this.fetchData()\r\n    },\r\n    fetchData() {\r\n      this.tbLoading = true\r\n      const form = { ...this.form }\r\n      delete form['dateRange']\r\n      this.form.dateRange = this.form.dateRange || []\r\n      form.BeginDate = parseTime(this.form.dateRange[0])\r\n        ? parseTime(this.form.dateRange[0])\r\n        : ''\r\n      form.EndDate = parseTime(this.form.dateRange[1])\r\n        ? parseTime(this.form.dateRange[1])\r\n        : ''\r\n      GetProjectSendingInfoPagelist(form).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.Data.map((v) => {\r\n            v.SendDate = v.SendDate\r\n              ? parseTime(new Date(v.SendDate), '{y}-{m}-{d}')\r\n              : v.SendDate\r\n            return v\r\n          })\r\n          // this.tbData = res.Data.Data;\r\n          this.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.tbLoading = false\r\n      })\r\n      GetProjectSendingAllCount({ ...form }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          // console.log(res.Data,\"res.Data\");\r\n          this.totalData = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getPageList() {\r\n      this.fetchData()\r\n      console.log(this.form, 'this.form')\r\n    },\r\n    handSubmit(row) {\r\n      const Id = row.Id\r\n      if (!row.VehicleNo) {\r\n        this.$message({\r\n          message: '发货单车辆信息未完善，请完善后提交',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      this.$confirm('是否提交过磅?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        SubmitWeighingForPC({\r\n          Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.fetchData()\r\n            this.$message({\r\n              message: res.Data,\r\n              type: 'success'\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    datePickerwrapper() {\r\n      // console.log(form,\"form1111111111\");\r\n      // console.log(new Date(\"2022-09-28T10:12:35.583Z\"),\"new Date111\");\r\n      // console.log(parseTime(new Date(\"2022-10-11T14:03:54\")),\"new Date222\");\r\n      if (!this.form.dateRange) {\r\n        this.form.dateRange = ['', '']\r\n      }\r\n      this.fetchData()\r\n    },\r\n    resetForm(formName) {\r\n      this.$refs[formName].resetFields()\r\n      this.fetchData()\r\n    },\r\n    submitForm2(formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          const { ProjectId, Area_Id, InstallUnit_Id } = this.form2\r\n          const {\r\n            Name,\r\n            Id,\r\n            Code,\r\n            SPIC_UserName,\r\n            Address,\r\n            Receiver,\r\n            Receiver_Tel,\r\n            Sys_Project_Id,\r\n            Receive_UserName\r\n          } = this.projects.find((v) => v.Id === this.form2.ProjectId)\r\n          const data = {\r\n            ProjectId,\r\n            Area_Id,\r\n            InstallUnit_Id,\r\n            Id,\r\n            Name,\r\n            Code,\r\n            Address,\r\n            Receiver,\r\n            Receiver_Tel,\r\n            Sys_Project_Id,\r\n            Receive_UserName,\r\n            autoGenerate: this.autoGenerate,\r\n            ProfessionalType: this.ProfessionalType\r\n          }\r\n          this.$router.push({\r\n            name: 'PROShipSentAdd',\r\n            query: {\r\n              pg_redirect: 'PROShipSent',\r\n              p: encodeURIComponent(JSON.stringify(data))\r\n            }\r\n          })\r\n          this.dialogVisible = false\r\n          this.$refs.form2.resetFields()\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    resetForm2(formName) {\r\n      this.dialogVisible = false\r\n      this.$refs[formName].resetFields()\r\n    },\r\n    handleClose() {\r\n      this.$refs.form2.resetFields()\r\n      this.dialogVisible = false\r\n    },\r\n    handleAdd() {\r\n      this.dialogVisible = true\r\n      // this.$router.push({ name: 'PROShipSentAdd', query: { pg_redirect: 'PROShipSent' }})\r\n    },\r\n    getProjectPageList() {\r\n      GetProjectPageList({ PageSize: -1 }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.projects = res.Data.Data\r\n        }\r\n      })\r\n    },\r\n    projectIdChange(e) {\r\n      console.log(e, 'e')\r\n      // if (e) {\r\n      //   this.getAreaList();\r\n      // }\r\n    },\r\n    projectIdClear(e) {\r\n      this.$refs.form2.resetFields()\r\n    },\r\n    // 获取区域\r\n    getAreaList() {\r\n      GeAreaTrees({\r\n        projectId: this.form2.ProjectId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.treeParamsArea.data = res.Data\r\n          this.$nextTick((_) => {\r\n            this.$refs.treeSelectArea.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    filterFun(val, ref) {\r\n      this.$refs[ref].filterFun(val)\r\n    },\r\n    areaChange(e) {\r\n      console.log(e, 'e')\r\n      this.getInstall()\r\n    },\r\n    // 清空区域\r\n    areaClear() {\r\n      this.form2.Area_Id = ''\r\n      this.form.InstallUnit_Id = ''\r\n    },\r\n    // 获取批次\r\n    getInstall() {\r\n      GetInstallUnitPageList({\r\n        Area_Id: this.form2.Area_Id,\r\n        Page: 1,\r\n        PageSize: -1\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          if (res.IsSucceed) {\r\n            this.SetupPositionData = res.Data.Data\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleEdit(id, isSub) {\r\n      this.$router.push({\r\n        name: 'PROShipSentEdit',\r\n        query: { pg_redirect: 'PROShipSent', id, isSub: isSub ? '1' : '0',\r\n          p: encodeURIComponent(JSON.stringify({ autoGenerate: this.autoGenerate }))\r\n        }\r\n      })\r\n    },\r\n    // 撤回至草稿\r\n    handleWithdraw(id) {\r\n      this.$confirm('撤回至草稿, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          WithdrawDraft({\r\n            id: id\r\n          }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                message: '撤销成功',\r\n                type: 'success'\r\n              })\r\n              this.fetchData()\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => { })\r\n    },\r\n    handleSub(row) {\r\n      const id = row.Id\r\n      if (!row.VehicleNo) {\r\n        this.$message({\r\n          message: '发货单车辆信息未完善，请完善后提交',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      this.$confirm('提交该发货单, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          this.pageLoading = true\r\n          SubmitProjectSending({\r\n            Id: id\r\n          }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                message: '发货成功',\r\n                type: 'success'\r\n              })\r\n              this.fetchData()\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n            this.pageLoading = false\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.pageLoading = false\r\n        })\r\n    },\r\n    handleDel(id) {\r\n      this.$confirm('是否删除该发货单?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        DeleteProjectSendingInfo({\r\n          Id: id\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '删除成功',\r\n              type: 'success'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    },\r\n    handleInfo(id) {\r\n      this.$router.push({\r\n        name: 'PROShipSentDetail',\r\n        query: { pg_redirect: 'PROShipSent', id }\r\n      })\r\n    },\r\n    handleChange(id) {\r\n      this.$router.push({\r\n        name: 'PROShipSentChangeRecord',\r\n        query: { pg_redirect: 'PROShipSent', id }\r\n      })\r\n    },\r\n    // printMe() {},\r\n    handleExport() {\r\n      this.title = '导出'\r\n      this.$nextTick(_ => {\r\n        this.$refs.radioDialog.handleOpen(this.selectList)\r\n      })\r\n    },\r\n    handleExportExcel() {\r\n      this.title = '导出'\r\n      this.$refs.dialogExcel.handleOpen(this.selectList)\r\n    },\r\n    handlePrint() {\r\n      this.title = '打印'\r\n      this.$refs.radioDialog.handleOpen(this.selectList)\r\n    },\r\n    handlePrintNoWeight() {\r\n      // console.log(this.selectRow.Code, \"this.selectRow.Code\");\r\n      // console.log(this.selectRow.Id, \"this.selectRow.Id\");\r\n      TransformsWithoutWeight({\r\n        sendId: this.selectRow.Id\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          // const templateUrl = combineURL(this.$baseUrl, res.Data);\r\n          // window.open(templateUrl, \"_blank\");\r\n          const url = new URL(res.Data, baseUrl())\r\n          window.open(url.href, '_blank')\r\n          this.$message({\r\n            type: 'success',\r\n            message: '打印成功!'\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      // this.$refs.PrintDialog.open(this.selectRow.Code)\r\n    },\r\n\r\n    // 导出\r\n    handleLeadingOut() {\r\n      this.btnLoading = true\r\n      const form = { ...this.form }\r\n      delete form['dateRange']\r\n      delete form['PageInfo']\r\n      this.form.dateRange = this.form.dateRange || []\r\n      form.BeginDate = parseTime(this.form.dateRange[0])\r\n        ? parseTime(this.form.dateRange[0])\r\n        : ''\r\n      form.EndDate = parseTime(this.form.dateRange[1])\r\n        ? parseTime(this.form.dateRange[1])\r\n        : ''\r\n      ExportInvoiceList({ ...form })\r\n        .then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message.success('导出成功')\r\n            const url = new URL(res.Data, baseUrl())\r\n            window.open(url.href)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n        .finally(() => {\r\n          // 结束loading\r\n          this.btnLoading = false\r\n        })\r\n    },\r\n    handleSelectionChange(list) {\r\n      this.selectList = list\r\n    },\r\n    selectChange({ selection, row }) {\r\n      this.$refs.dyTable.$refs.dtable.clearSelection()\r\n      if (selection.length != 0) {\r\n        this.selectRow = row\r\n      } else {\r\n        this.selectRow = {}\r\n      }\r\n      if (selection.length > 1) {\r\n        selection.shift()\r\n      }\r\n      // console.log(selection, \"selection2\");\r\n      this.$refs.dyTable.$refs.dtable.toggleRowSelection(\r\n        row,\r\n        !!selection.length\r\n      )\r\n    },\r\n    handleSelectAll() {\r\n      // this.$refs.dyTable.$refs.dtable.clearSelection()\r\n    },\r\n    handelView(Id) {\r\n      this.IsVisabel = true\r\n      this.$nextTick((_) => {\r\n        this.$refs.checkDialog.handelOpen(Id)\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.total-wrapper {\r\n  float: right;\r\n  color: #298dff;\r\n  background-color: #f5faff;\r\n  font-size: 14px;\r\n  padding: 6px 10px 6px 10px;\r\n  margin-right: 10px;\r\n}\r\n.date-picker-wrapper {\r\n  float: right;\r\n  width: 20%;\r\n}\r\n::v-deep .form-search {\r\n  width: 100%;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  .el-form-item {\r\n    width: 24%;\r\n    display: flex;\r\n  }\r\n  .el-form-item__label {\r\n    width: 100px;\r\n  }\r\n  .el-form-item__content {\r\n    min-width: 10px;\r\n    flex: 1;\r\n  }\r\n  .el-select {\r\n    width: 100%;\r\n  }\r\n}\r\n.license-box {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100px;\r\n  height: 28px;\r\n  background: #818fb7;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n\r\n  .inner-box {\r\n    width: 98px;\r\n    height: 24px;\r\n    border: 1px solid #ffffff;\r\n    border-radius: 3px;\r\n    color: #ffffff;\r\n    font-size: 14px;\r\n  }\r\n}\r\n::v-deep .custom-pagination .checked-count {\r\n  top: 20px;\r\n}\r\n::v-deep .pagination {\r\n  justify-content: right !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsWA,OAAAA,OAAA;AACA,OAAAC,SAAA;AACA,OAAAC,aAAA;AACA,OAAAC,gBAAA;AACA,OAAAC,SAAA;AACA,SACAC,6BAAA,EACAC,wBAAA,EACAC,oBAAA,EACAC,yBAAA,EACAC,uBAAA,EACAC,mBAAA,EACAC,aAAA,EACAC,iBAAA,EAAAC,cAAA,EAAAC,UAAA,QACA;AACA,SAAAC,kBAAA;AACA,SAAAC,WAAA;AACA,SAAAC,sBAAA;AACA,SAAAC,SAAA;AACA,SAAAC,OAAA;AACA,SAAAC,4BAAA;AACA,OAAAC,WAAA;AACA,OAAAC,WAAA;AACA,OAAAC,WAAA;AACA,OAAAC,WAAA;AACA,SAAAC,UAAA;AACA,OAAAC,kBAAA;AAEA,IAAAC,SAAA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA;AAEA;EACAC,UAAA;IACAF,kBAAA,EAAAA,kBAAA;IACAzB,SAAA,EAAAA,SAAA;IACAD,OAAA,EAAAA,OAAA;IACAG,gBAAA,EAAAA,gBAAA;IACAkB,WAAA,EAAAA,WAAA;IACAC,WAAA,EAAAA,WAAA;IACAE,WAAA,EAAAA,WAAA;IACAD,WAAA,EAAAA;EACA;EACAM,OAAA;IACAC,cAAA,WAAAA,eAAAC,CAAA;MACA;MACA,OAAAb,SAAA,KAAAc,IAAA,CAAAD,CAAA;IACA;EACA;EACAE,MAAA,GAAA/B,aAAA,EAAAE,SAAA;EACA8B,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,UAAA,EAAAT,SAAA;MACAU,SAAA;MACAC,UAAA;MACAC,IAAA;QACAC,IAAA;QACAC,MAAA;QACAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,QAAA;QACAC,iBAAA;QACAC,SAAA;QACAC,QAAA;UACAC,aAAA;UACAC,IAAA;UACAC,QAAA;QACA;MACA;MACAC,KAAA;QACAV,SAAA;QACAW,OAAA;QACAC,cAAA;MACA;MACAC,KAAA;QACAb,SAAA;UAAAc,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACA;MACA;MACAC,aAAA;QACAC,SAAA,GACA;UACAC,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAC,GAAA,OAAAhC,IAAA;YACA,IAAAiC,KAAA,OAAAjC,IAAA;YACAiC,KAAA,CAAAC,OAAA,CAAAD,KAAA,CAAAE,OAAA;YACAJ,MAAA,CAAAK,KAAA,UAAAH,KAAA,EAAAD,GAAA;UACA;QACA,GACA;UACAH,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAC,GAAA,OAAAhC,IAAA;YACA,IAAAiC,KAAA,OAAAjC,IAAA;YACAiC,KAAA,CAAAC,OAAA,CAAAD,KAAA,CAAAE,OAAA;YACAJ,MAAA,CAAAK,KAAA,UAAAH,KAAA,EAAAD,GAAA;UACA;QACA,GACA;UACAH,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAC,GAAA,OAAAhC,IAAA;YACA,IAAAiC,KAAA,OAAAjC,IAAA;YACAiC,KAAA,CAAAC,OAAA,CAAAD,KAAA,CAAAE,OAAA;YACAJ,MAAA,CAAAK,KAAA,UAAAH,KAAA,EAAAD,GAAA;UACA;QACA;MAEA;MACAK,YAAA;QACAC,SAAA;QACAC,WAAA;MACA;MACA7B,SAAA;MACA8B,aAAA;MACAC,WAAA;MACAC,YAAA,GACA;QACAC,IAAA,OAAAC,MAAA,CAAAD,IAAA;QACAE,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAC,IAAA;QACAC,IAAA;UAAAC,KAAA;QAAA;MACA,GACA;QACAX,IAAA,OAAAC,MAAA,CAAAD,IAAA;QACAE,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAC,IAAA;QACAC,IAAA;UAAAC,KAAA;QAAA;MACA,GACA;QACAX,IAAA,OAAAC,MAAA,CAAAD,IAAA;QACAE,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAC,IAAA;QACAC,IAAA;UAAAC,KAAA;QAAA;MACA,GACA;QACAX,IAAA,OAAAC,MAAA,CAAAD,IAAA;QACAE,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAC,IAAA;QACAC,IAAA;UAAAC,KAAA;QAAA;MACA,EACA;MACAC,QAAA;MACA;MACAC,cAAA;QACA;QACA;QACA;QACAC,UAAA;QACAC,WAAA;QACAxD,IAAA;QACAyD,KAAA;UACAC,QAAA;UACAC,KAAA;UACAC,KAAA;QACA;MACA;MACAC,MAAA;QAAAC,KAAA;MAAA;MACAC,iBAAA;MACAC,SAAA;QACAhD,IAAA;QACAC,QAAA;QACAF,aAAA;MACA;MACAkD,UAAA;QACAC,SAAA;QACAC,OAAA;QACArD,QAAA;UACAC,aAAA;UACAC,IAAA;UACAC,QAAA;QACA;MACA;MACAmD,QAAA;QACAC,WAAA;QACAC,QAAA;MACA;MACAC,OAAA;MACAC,MAAA;MACAC,KAAA;MACAC,SAAA;MACAC,SAAA;MACAC,SAAA;QACAC,cAAA;QACAC,cAAA;MACA;MACAC,gBAAA;MACA3B,KAAA;MACA4B,cAAA;IACA;EACA;EACAC,QAAA,EAAAC,aAAA;IACAC,WAAA,WAAAA,YAAA;MACA,OAAAC,MAAA,CAAAC,IAAA,MAAAV,SAAA,EAAAW,MAAA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA,UAAAP,cAAA;QACA,YAAAT,OAAA,CAAAiB,MAAA,WAAAC,IAAA;UACA,OACAA,IAAA,CAAAnF,IAAA,yBAAAmF,IAAA,CAAAnF,IAAA;QAEA;MACA;QACA,YAAAiE,OAAA;MACA;IACA;EAAA,GACAhF,UAAA,2HACA;EACAmG,SAAA,WAAAA,UAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACAC,OAAA,CAAAC,GAAA;YACA,IAAAX,KAAA,CAAAjD,MAAA,CAAA6D,KAAA,CAAAC,OAAA;cACAb,KAAA,CAAAc,SAAA;YACA;YACA;UAAA;UAAA;YAAA,OAAAP,QAAA,CAAAQ,IAAA;QAAA;MAAA,GAAAX,OAAA;IAAA;EACA;EACAY,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAhB,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAe,SAAA;MAAA,OAAAhB,mBAAA,GAAAG,IAAA,UAAAc,UAAAC,SAAA;QAAA,kBAAAA,SAAA,CAAAZ,IAAA,GAAAY,SAAA,CAAAX,IAAA;UAAA;YACAQ,MAAA,CAAAI,MAAA,CAAAC,QAAA;YAAAF,SAAA,CAAAX,IAAA;YAAA,OAEAQ,MAAA,CAAAI,MAAA,CAAAC,QAAA;UAAA;YAAAL,MAAA,CAAA5B,cAAA,GAAA+B,SAAA,CAAAG,IAAA;YACAN,MAAA,CAAAO,oBAAA;YACAP,MAAA,CAAAQ,kBAAA;UAAA;UAAA;YAAA,OAAAL,SAAA,CAAAL,IAAA;QAAA;MAAA,GAAAG,QAAA;IAAA;EACA;EACAQ,OAAA,WAAAA,QAAA;IACAhB,OAAA,CAAAC,GAAA;EACA;EACAgB,OAAA;IACAC,gBAAA,WAAAA,iBAAAC,UAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAA9E,IAAA;QACAnE,UAAA;UACA4I,UAAA,EAAAA;QACA,GAAAzE,IAAA,WAAA+E,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAN,MAAA,CAAAO,QAAA;cACAzG,OAAA;cACAsG,IAAA;YACA;YACAJ,MAAA,CAAAhB,SAAA;UACA;YACAgB,MAAA,CAAAO,QAAA;cACAzG,OAAA,EAAAuG,GAAA,CAAAG,OAAA;cACAJ,IAAA;YACA;UACA;QACA;MACA,GAAAK,KAAA;QACAT,MAAA,CAAAO,QAAA;UACAH,IAAA;UACAtG,OAAA;QACA;MACA;IACA;IACA4G,eAAA,WAAAA,gBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,EAAA,GAAAF,GAAA,CAAAE,EAAA;MACA,KAAAF,GAAA,CAAA3H,SAAA;QACA,KAAAuH,QAAA;UACAzG,OAAA;UACAsG,IAAA;QACA;QACA;MACA;MAEA,KAAAH,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAA9E,IAAA;QACApE,cAAA;UACA2J,EAAA,EAAAA;QACA,GAAAvF,IAAA,WAAA+E,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAM,MAAA,CAAA5B,SAAA;YACA4B,MAAA,CAAAL,QAAA;cACAzG,OAAA;cACAsG,IAAA;YACA;UACA;YACAQ,MAAA,CAAAL,QAAA;cACAzG,OAAA,EAAAuG,GAAA,CAAAG,OAAA;cACAJ,IAAA;YACA;UACA;QACA;MACA,GAAAK,KAAA;QACAG,MAAA,CAAAL,QAAA;UACAH,IAAA;UACAtG,OAAA;QACA;MACA;IACA;IACAgH,aAAA,WAAAA,cAAAC,KAAA;MACA,KAAAC,KAAA,YAAAC,UAAA,CAAAF,KAAA;IACA;IACAG,cAAA,WAAAA,eAAAP,GAAA;MACA,YAAAQ,2BAAA,UAAAC,4BAAA,gBAAAT,GAAA,CAAAU,sBAAA;IACA;IACA3B,oBAAA,WAAAA,qBAAA;MAAA,IAAA4B,MAAA;MAAA,OAAAnD,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkD,SAAA;QAAA,OAAAnD,mBAAA,GAAAG,IAAA,UAAAiD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/C,IAAA,GAAA+C,SAAA,CAAA9C,IAAA;YAAA;cAAA8C,SAAA,CAAA9C,IAAA;cAAA,OACAlH,4BAAA;gBACAiK,SAAA,EAAAC,YAAA,CAAAC,OAAA;cACA,GAAAtG,IAAA,WAAA+E,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAgB,MAAA,CAAAhE,gBAAA,GAAA+C,GAAA,CAAAwB,IAAA;gBACA;kBACAP,MAAA,CAAAf,QAAA;oBACAzG,OAAA,EAAAuG,GAAA,CAAAG,OAAA;oBACAJ,IAAA;kBACA;gBACA;cACA;YAAA;cAAAqB,SAAA,CAAA9C,IAAA;cAAA,OACA2C,MAAA,CAAAQ,cAAA,gCAAAC,MAAA,CACAT,MAAA,CAAAhE,gBAAA,IAAAzE,IAAA,CACA;YAAA;cACAyI,MAAA,CAAAtC,SAAA;YAAA;YAAA;cAAA,OAAAyC,SAAA,CAAAxC,IAAA;UAAA;QAAA,GAAAsC,QAAA;MAAA;IACA;IACAvC,SAAA,WAAAA,UAAA;MAAA,IAAAgD,MAAA;MACA,KAAA/E,SAAA;MACA,IAAArE,IAAA,GAAA6E,aAAA,UAAA7E,IAAA;MACA,OAAAA,IAAA;MACA,KAAAA,IAAA,CAAAQ,SAAA,QAAAR,IAAA,CAAAQ,SAAA;MACAR,IAAA,CAAA6D,SAAA,GAAAlF,SAAA,MAAAqB,IAAA,CAAAQ,SAAA,OACA7B,SAAA,MAAAqB,IAAA,CAAAQ,SAAA,OACA;MACAR,IAAA,CAAA8D,OAAA,GAAAnF,SAAA,MAAAqB,IAAA,CAAAQ,SAAA,OACA7B,SAAA,MAAAqB,IAAA,CAAAQ,SAAA,OACA;MACA1C,6BAAA,CAAAkC,IAAA,EAAA0C,IAAA,WAAA+E,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA0B,MAAA,CAAAjF,MAAA,GAAAsD,GAAA,CAAAwB,IAAA,CAAAA,IAAA,CAAAI,GAAA,WAAAC,CAAA;YACAA,CAAA,CAAAC,QAAA,GAAAD,CAAA,CAAAC,QAAA,GACA5K,SAAA,KAAAc,IAAA,CAAA6J,CAAA,CAAAC,QAAA,oBACAD,CAAA,CAAAC,QAAA;YACA,OAAAD,CAAA;UACA;UACA;UACAF,MAAA,CAAAhF,KAAA,GAAAqD,GAAA,CAAAwB,IAAA,CAAAO,UAAA;QACA;UACAJ,MAAA,CAAAzB,QAAA;YACAzG,OAAA,EAAAuG,GAAA,CAAAG,OAAA;YACAJ,IAAA;UACA;QACA;QACA4B,MAAA,CAAA/E,SAAA;MACA;MACApG,yBAAA,CAAA4G,aAAA,KAAA7E,IAAA,GAAA0C,IAAA,WAAA+E,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA;UACA0B,MAAA,CAAA7E,SAAA,GAAAkD,GAAA,CAAAwB,IAAA;QACA;UACAG,MAAA,CAAAzB,QAAA;YACAzG,OAAA,EAAAuG,GAAA,CAAAG,OAAA;YACAJ,IAAA;UACA;QACA;MACA;IACA;IACAiC,WAAA,WAAAA,YAAA;MACA,KAAArD,SAAA;MACAJ,OAAA,CAAAC,GAAA,MAAAjG,IAAA;IACA;IACA0J,UAAA,WAAAA,WAAA3B,GAAA;MAAA,IAAA4B,MAAA;MACA,IAAA1B,EAAA,GAAAF,GAAA,CAAAE,EAAA;MACA,KAAAF,GAAA,CAAA3H,SAAA;QACA,KAAAuH,QAAA;UACAzG,OAAA;UACAsG,IAAA;QACA;QACA;MACA;MACA,KAAAH,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAA9E,IAAA;QACAvE,mBAAA;UACA8J,EAAA,EAAAA;QACA,GAAAvF,IAAA,WAAA+E,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAiC,MAAA,CAAAvD,SAAA;YACAuD,MAAA,CAAAhC,QAAA;cACAzG,OAAA,EAAAuG,GAAA,CAAAwB,IAAA;cACAzB,IAAA;YACA;UACA;YACAmC,MAAA,CAAAhC,QAAA;cACAzG,OAAA,EAAAuG,GAAA,CAAAG,OAAA;cACAJ,IAAA;YACA;UACA;QACA;MACA,GAAAK,KAAA;QACA8B,MAAA,CAAAhC,QAAA;UACAH,IAAA;UACAtG,OAAA;QACA;MACA;IACA;IACA0I,iBAAA,WAAAA,kBAAA;MACA;MACA;MACA;MACA,UAAA5J,IAAA,CAAAQ,SAAA;QACA,KAAAR,IAAA,CAAAQ,SAAA;MACA;MACA,KAAA4F,SAAA;IACA;IACAyD,SAAA,WAAAA,UAAAC,QAAA;MACA,KAAA1B,KAAA,CAAA0B,QAAA,EAAAC,WAAA;MACA,KAAA3D,SAAA;IACA;IACA4D,WAAA,WAAAA,YAAAF,QAAA;MAAA,IAAAG,MAAA;MACA,KAAA7B,KAAA,CAAA0B,QAAA,EAAAI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,WAAA,GAAAH,MAAA,CAAApJ,KAAA;YAAAV,SAAA,GAAAiK,WAAA,CAAAjK,SAAA;YAAAW,OAAA,GAAAsJ,WAAA,CAAAtJ,OAAA;YAAAC,cAAA,GAAAqJ,WAAA,CAAArJ,cAAA;UACA,IAAAsJ,oBAAA,GAUAJ,MAAA,CAAAjH,QAAA,CAAAsH,IAAA,WAAAhB,CAAA;cAAA,OAAAA,CAAA,CAAArB,EAAA,KAAAgC,MAAA,CAAApJ,KAAA,CAAAV,SAAA;YAAA;YATAoK,IAAA,GAAAF,oBAAA,CAAAE,IAAA;YACAtC,EAAA,GAAAoC,oBAAA,CAAApC,EAAA;YACAhI,IAAA,GAAAoK,oBAAA,CAAApK,IAAA;YACAuK,aAAA,GAAAH,oBAAA,CAAAG,aAAA;YACAC,OAAA,GAAAJ,oBAAA,CAAAI,OAAA;YACAC,QAAA,GAAAL,oBAAA,CAAAK,QAAA;YACAC,YAAA,GAAAN,oBAAA,CAAAM,YAAA;YACAC,cAAA,GAAAP,oBAAA,CAAAO,cAAA;YACAC,gBAAA,GAAAR,oBAAA,CAAAQ,gBAAA;UAEA,IAAAlL,IAAA;YACAQ,SAAA,EAAAA,SAAA;YACAW,OAAA,EAAAA,OAAA;YACAC,cAAA,EAAAA,cAAA;YACAkH,EAAA,EAAAA,EAAA;YACAsC,IAAA,EAAAA,IAAA;YACAtK,IAAA,EAAAA,IAAA;YACAwK,OAAA,EAAAA,OAAA;YACAC,QAAA,EAAAA,QAAA;YACAC,YAAA,EAAAA,YAAA;YACAC,cAAA,EAAAA,cAAA;YACAC,gBAAA,EAAAA,gBAAA;YACAC,YAAA,EAAAb,MAAA,CAAAa,YAAA;YACApG,gBAAA,EAAAuF,MAAA,CAAAvF;UACA;UACAuF,MAAA,CAAAc,OAAA,CAAAC,IAAA;YACAnI,IAAA;YACAqD,KAAA;cACA+E,WAAA;cACAC,CAAA,EAAAC,kBAAA,CAAAC,IAAA,CAAAC,SAAA,CAAA1L,IAAA;YACA;UACA;UACAsK,MAAA,CAAAhI,aAAA;UACAgI,MAAA,CAAA7B,KAAA,CAAAvH,KAAA,CAAAkJ,WAAA;QACA;UACA/D,OAAA,CAAAC,GAAA;UACA;QACA;MACA;IACA;IACAqF,UAAA,WAAAA,WAAAxB,QAAA;MACA,KAAA7H,aAAA;MACA,KAAAmG,KAAA,CAAA0B,QAAA,EAAAC,WAAA;IACA;IACAwB,WAAA,WAAAA,YAAA;MACA,KAAAnD,KAAA,CAAAvH,KAAA,CAAAkJ,WAAA;MACA,KAAA9H,aAAA;IACA;IACAuJ,SAAA,WAAAA,UAAA;MACA,KAAAvJ,aAAA;MACA;IACA;IACA8E,kBAAA,WAAAA,mBAAA;MAAA,IAAA0E,MAAA;MACAjN,kBAAA;QAAAoC,QAAA;MAAA,GAAA8B,IAAA,WAAA+E,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA+D,MAAA,CAAAzI,QAAA,GAAAyE,GAAA,CAAAwB,IAAA,CAAAA,IAAA;QACA;MACA;IACA;IACAyC,eAAA,WAAAA,gBAAAlM,CAAA;MACAwG,OAAA,CAAAC,GAAA,CAAAzG,CAAA;MACA;MACA;MACA;IACA;IACAmM,cAAA,WAAAA,eAAAnM,CAAA;MACA,KAAA4I,KAAA,CAAAvH,KAAA,CAAAkJ,WAAA;IACA;IACA;IACA6B,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACApN,WAAA;QACAqN,SAAA,OAAAjL,KAAA,CAAAV;MACA,GAAAuC,IAAA,WAAA+E,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAmE,MAAA,CAAA5I,cAAA,CAAAtD,IAAA,GAAA8H,GAAA,CAAAwB,IAAA;UACA4C,MAAA,CAAAE,SAAA,WAAAC,CAAA;YACAH,MAAA,CAAAzD,KAAA,CAAA6D,cAAA,CAAAC,iBAAA,CAAAzE,GAAA,CAAAwB,IAAA;UACA;QACA;UACA4C,MAAA,CAAAlE,QAAA;YACAzG,OAAA,EAAAuG,GAAA,CAAAG,OAAA;YACAJ,IAAA;UACA;QACA;MACA;IACA;IACA2E,SAAA,WAAAA,UAAAC,GAAA,EAAAC,GAAA;MACA,KAAAjE,KAAA,CAAAiE,GAAA,EAAAF,SAAA,CAAAC,GAAA;IACA;IACAE,UAAA,WAAAA,WAAA9M,CAAA;MACAwG,OAAA,CAAAC,GAAA,CAAAzG,CAAA;MACA,KAAA+M,UAAA;IACA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,KAAA3L,KAAA,CAAAC,OAAA;MACA,KAAAd,IAAA,CAAAe,cAAA;IACA;IACA;IACAwL,UAAA,WAAAA,WAAA;MAAA,IAAAE,MAAA;MACA/N,sBAAA;QACAoC,OAAA,OAAAD,KAAA,CAAAC,OAAA;QACAH,IAAA;QACAC,QAAA;MACA,GAAA8B,IAAA,WAAA+E,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA,IAAAD,GAAA,CAAAC,SAAA;YACA+E,MAAA,CAAA/I,iBAAA,GAAA+D,GAAA,CAAAwB,IAAA,CAAAA,IAAA;UACA;YACAwD,MAAA,CAAA9E,QAAA;cACAzG,OAAA,EAAAuG,GAAA,CAAAG,OAAA;cACAJ,IAAA;YACA;UACA;QACA;UACAiF,MAAA,CAAA9E,QAAA;YACAzG,OAAA,EAAAuG,GAAA,CAAAG,OAAA;YACAJ,IAAA;UACA;QACA;MACA;IACA;IACAkF,UAAA,WAAAA,WAAAC,EAAA,EAAAC,KAAA;MACA,KAAA7B,OAAA,CAAAC,IAAA;QACAnI,IAAA;QACAqD,KAAA;UAAA+E,WAAA;UAAA0B,EAAA,EAAAA,EAAA;UAAAC,KAAA,EAAAA,KAAA;UACA1B,CAAA,EAAAC,kBAAA,CAAAC,IAAA,CAAAC,SAAA;YAAAP,YAAA,OAAAA;UAAA;QACA;MACA;IACA;IACA;IACA+B,cAAA,WAAAA,eAAAF,EAAA;MAAA,IAAAG,OAAA;MACA,KAAAzF,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACA9E,IAAA;QACAtE,aAAA;UACAuO,EAAA,EAAAA;QACA,GAAAjK,IAAA,WAAA+E,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAoF,OAAA,CAAAnF,QAAA;cACAzG,OAAA;cACAsG,IAAA;YACA;YACAsF,OAAA,CAAA1G,SAAA;UACA;YACA0G,OAAA,CAAAnF,QAAA;cACAzG,OAAA,EAAAuG,GAAA,CAAAG,OAAA;cACAJ,IAAA;YACA;UACA;QACA;MACA,GACAK,KAAA;IACA;IACAkF,SAAA,WAAAA,UAAAhF,GAAA;MAAA,IAAAiF,OAAA;MACA,IAAAL,EAAA,GAAA5E,GAAA,CAAAE,EAAA;MACA,KAAAF,GAAA,CAAA3H,SAAA;QACA,KAAAuH,QAAA;UACAzG,OAAA;UACAsG,IAAA;QACA;QACA;MACA;MACA,KAAAH,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACA9E,IAAA;QACAsK,OAAA,CAAA9K,WAAA;QACAlE,oBAAA;UACAiK,EAAA,EAAA0E;QACA,GAAAjK,IAAA,WAAA+E,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAsF,OAAA,CAAArF,QAAA;cACAzG,OAAA;cACAsG,IAAA;YACA;YACAwF,OAAA,CAAA5G,SAAA;UACA;YACA4G,OAAA,CAAArF,QAAA;cACAzG,OAAA,EAAAuG,GAAA,CAAAG,OAAA;cACAJ,IAAA;YACA;UACA;UACAwF,OAAA,CAAA9K,WAAA;QACA;MACA,GACA2F,KAAA;QACAmF,OAAA,CAAA9K,WAAA;MACA;IACA;IACA+K,SAAA,WAAAA,UAAAN,EAAA;MAAA,IAAAO,OAAA;MACA,KAAA7F,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAA9E,IAAA;QACA3E,wBAAA;UACAkK,EAAA,EAAA0E;QACA,GAAAjK,IAAA,WAAA+E,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAwF,OAAA,CAAAvF,QAAA;cACAzG,OAAA;cACAsG,IAAA;YACA;YACA0F,OAAA,CAAA9G,SAAA;UACA;YACA8G,OAAA,CAAAvF,QAAA;cACAzG,OAAA,EAAAuG,GAAA,CAAAG,OAAA;cACAJ,IAAA;YACA;UACA;QACA;MACA,GAAAK,KAAA;QACAqF,OAAA,CAAAvF,QAAA;UACAH,IAAA;UACAtG,OAAA;QACA;MACA;IACA;IACAiM,UAAA,WAAAA,WAAAR,EAAA;MACA,KAAA5B,OAAA,CAAAC,IAAA;QACAnI,IAAA;QACAqD,KAAA;UAAA+E,WAAA;UAAA0B,EAAA,EAAAA;QAAA;MACA;IACA;IACAS,YAAA,WAAAA,aAAAT,EAAA;MACA,KAAA5B,OAAA,CAAAC,IAAA;QACAnI,IAAA;QACAqD,KAAA;UAAA+E,WAAA;UAAA0B,EAAA,EAAAA;QAAA;MACA;IACA;IACA;IACAU,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,KAAAvK,KAAA;MACA,KAAAgJ,SAAA,WAAAC,CAAA;QACAsB,OAAA,CAAAlF,KAAA,CAAArJ,WAAA,CAAAwO,UAAA,CAAAD,OAAA,CAAA1N,UAAA;MACA;IACA;IACA4N,iBAAA,WAAAA,kBAAA;MACA,KAAAzK,KAAA;MACA,KAAAqF,KAAA,CAAApJ,WAAA,CAAAuO,UAAA,MAAA3N,UAAA;IACA;IACA6N,WAAA,WAAAA,YAAA;MACA,KAAA1K,KAAA;MACA,KAAAqF,KAAA,CAAArJ,WAAA,CAAAwO,UAAA,MAAA3N,UAAA;IACA;IACA8N,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,OAAA;MACA;MACA;MACAzP,uBAAA;QACA0P,MAAA,OAAAtJ,SAAA,CAAA2D;MACA,GAAAvF,IAAA,WAAA+E,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA;UACA;UACA,IAAAmG,GAAA,OAAAC,GAAA,CAAArG,GAAA,CAAAwB,IAAA,EAAArK,OAAA;UACAmP,MAAA,CAAAC,IAAA,CAAAH,GAAA,CAAAI,IAAA;UACAN,OAAA,CAAAhG,QAAA;YACAH,IAAA;YACAtG,OAAA;UACA;QACA;UACAyM,OAAA,CAAAhG,QAAA;YACAzG,OAAA,EAAAuG,GAAA,CAAAG,OAAA;YACAJ,IAAA;UACA;QACA;MACA;MACA;IACA;IAEA;IACA0G,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MACA,KAAApO,UAAA;MACA,IAAAC,IAAA,GAAA6E,aAAA,UAAA7E,IAAA;MACA,OAAAA,IAAA;MACA,OAAAA,IAAA;MACA,KAAAA,IAAA,CAAAQ,SAAA,QAAAR,IAAA,CAAAQ,SAAA;MACAR,IAAA,CAAA6D,SAAA,GAAAlF,SAAA,MAAAqB,IAAA,CAAAQ,SAAA,OACA7B,SAAA,MAAAqB,IAAA,CAAAQ,SAAA,OACA;MACAR,IAAA,CAAA8D,OAAA,GAAAnF,SAAA,MAAAqB,IAAA,CAAAQ,SAAA,OACA7B,SAAA,MAAAqB,IAAA,CAAAQ,SAAA,OACA;MACAnC,iBAAA,CAAAwG,aAAA,KAAA7E,IAAA,GACA0C,IAAA,WAAA+E,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAyG,OAAA,CAAAxG,QAAA,CAAAyG,OAAA;UACA,IAAAP,GAAA,OAAAC,GAAA,CAAArG,GAAA,CAAAwB,IAAA,EAAArK,OAAA;UACAmP,MAAA,CAAAC,IAAA,CAAAH,GAAA,CAAAI,IAAA;QACA;UACAE,OAAA,CAAAxG,QAAA;YACAzG,OAAA,EAAAuG,GAAA,CAAAG,OAAA;YACAJ,IAAA;UACA;QACA;MACA,GACA6G,OAAA;QACA;QACAF,OAAA,CAAApO,UAAA;MACA;IACA;IACAuO,qBAAA,WAAAA,sBAAAC,IAAA;MACA,KAAA3O,UAAA,GAAA2O,IAAA;IACA;IACAC,YAAA,WAAAA,aAAAC,IAAA;MAAA,IAAAC,SAAA,GAAAD,IAAA,CAAAC,SAAA;QAAA3G,GAAA,GAAA0G,IAAA,CAAA1G,GAAA;MACA,KAAAK,KAAA,CAAAuG,OAAA,CAAAvG,KAAA,CAAAwG,MAAA,CAAAC,cAAA;MACA,IAAAH,SAAA,CAAAzJ,MAAA;QACA,KAAAX,SAAA,GAAAyD,GAAA;MACA;QACA,KAAAzD,SAAA;MACA;MACA,IAAAoK,SAAA,CAAAzJ,MAAA;QACAyJ,SAAA,CAAAI,KAAA;MACA;MACA;MACA,KAAA1G,KAAA,CAAAuG,OAAA,CAAAvG,KAAA,CAAAwG,MAAA,CAAAG,kBAAA,CACAhH,GAAA,EACA,EAAA2G,SAAA,CAAAzJ,MACA;IACA;IACA+J,eAAA,WAAAA,gBAAA;MACA;IAAA,CACA;IACAC,UAAA,WAAAA,WAAAhH,EAAA;MAAA,IAAAiH,OAAA;MACA,KAAApP,SAAA;MACA,KAAAiM,SAAA,WAAAC,CAAA;QACAkD,OAAA,CAAA9G,KAAA,CAAAnJ,WAAA,CAAAkQ,UAAA,CAAAlH,EAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
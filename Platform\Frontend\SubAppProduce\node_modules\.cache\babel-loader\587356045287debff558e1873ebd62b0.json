{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\CheckCombination.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\CheckCombination.vue", "mtime": 1757919153145}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetGridByCode", "GetFactoryProfessionalByCode", "QualityList", "DelQualityList", "timeFormat", "props", "checkType", "type", "Object", "default", "data", "columns", "tbLoading", "TypeId", "typeOption", "tbData", "watch", "handler", "newName", "old<PERSON>ame", "getQualityList", "deep", "mounted", "getTypeList", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "_this$typeOption$", "wrap", "_callee$", "_context", "prev", "next", "factoryId", "localStorage", "getItem", "sent", "Data", "IsSucceed", "freeze", "console", "log", "length", "Id", "fetchData", "$message", "message", "Message", "stop", "_this2", "_callee2", "_callee2$", "_context2", "getTableConfig", "code", "_this3", "find", "i", "Code", "then", "error", "list", "ColumnList", "filter", "v", "Is_Display", "map", "item", "fixed", "_this4", "check_object_id", "Bom_Level", "Check_Type", "Create_Date", "removeEvent", "row", "_this5", "$confirm", "confirmButtonText", "cancelButtonText", "id", "catch", "editEvent", "$emit"], "sources": ["src/views/PRO/factoryQuality/checkoutGroup/components/CheckCombination.vue"], "sourcesContent": ["<template>\n  <div style=\"height: calc(100vh - 300px)\">\n    <vxe-table\n      v-loading=\"tbLoading\"\n      :empty-render=\"{name: 'NotData'}\"\n      show-header-overflow\n      element-loading-spinner=\"el-icon-loading\"\n      element-loading-text=\"拼命加载中\"\n      empty-text=\"暂无数据\"\n      height=\"100%\"\n      :data=\"tbData\"\n      stripe\n      resizable\n      :auto-resize=\"true\"\n      class=\"cs-vxe-table\"\n      :tooltip-config=\"{ enterable: true }\"\n    >\n      <!-- <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" /> -->\n      <vxe-column\n        v-for=\"(item, index) in columns\"\n        :key=\"index\"\n        :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n        show-overflow=\"tooltip\"\n        sortable\n        :align=\"item.Align\"\n        :field=\"item.Code\"\n        :title=\"item.Display_Name\"\n      >\n        <template #default=\"{ row }\">\n          <span>{{ row[item.Code] || '-' }}</span>\n        </template>\n      </vxe-column>\n      <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" align=\"center\" show-overflow>\n        <template #default=\"{ row }\">\n          <el-button type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\n          <el-divider direction=\"vertical\" />\n          <el-button type=\"text\" @click=\"removeEvent(row)\">删除</el-button>\n        </template>\n      </vxe-column>\n    </vxe-table>\n  </div>\n</template>\n\n<script>\nimport { GetGridByCode } from '@/api/sys'\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\nimport { QualityList } from '@/api/PRO/factorycheck'\nimport { DelQualityList } from '@/api/PRO/factorycheck'\nimport { timeFormat } from '@/filters'\nexport default {\n  props: {\n    checkType: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      columns: null,\n      tbLoading: false,\n      TypeId: '',\n      typeOption: '',\n      tbData: []\n\n    }\n  },\n  watch: {\n    checkType: {\n      handler(newName, oldName) {\n        this.checkType = newName\n        this.getQualityList()\n      },\n      deep: true\n    }\n  },\n  mounted() {\n    // this.getQualityList()\n    this.getTypeList()\n  },\n  methods: {\n    async getTypeList() {\n      let res = null\n      let data = null\n      res = await GetFactoryProfessionalByCode({\n        factoryId: localStorage.getItem('CurReferenceId')\n      })\n      data = res.Data\n      if (res.IsSucceed) {\n        this.typeOption = Object.freeze(data)\n        console.log(this.typeOption)\n        if (this.typeOption.length > 0) {\n          this.TypeId = this.typeOption[0]?.Id\n          this.fetchData()\n        }\n      } else {\n        this.$message({\n          message: res.Message,\n          type: 'error'\n        })\n      }\n    },\n    async fetchData() {\n      await this.getTableConfig('Check_item_combination')\n    //   this.tbLoading = true;\n    },\n    // 获取列表\n    getTableConfig(code) {\n      GetGridByCode({ code: code + ',' + this.typeOption.find((i) => i.Id === this.TypeId).Code }).then((res) => {\n        const { IsSucceed, Data, Message } = res\n        if (IsSucceed) {\n          if (!Data) {\n            this.$message.error('当前专业没有配置相对应表格')\n            this.tbLoading = true\n            return\n          }\n          this.tbLoading = false\n          const list = Data.ColumnList || []\n          this.columns = list.filter((v) => v.Is_Display).map((item) => {\n            if (item.Code === 'CheckName') {\n              item.fixed = 'left'\n            }\n            return item\n          })\n          console.log(this.columns)\n        } else {\n          this.$message({\n            message: Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    // 获取检查项组合列表\n    getQualityList() {\n      this.tbLoading = true\n      QualityList({ check_object_id: this.checkType.Id, Bom_Level: this.checkType.Code }).then((res) => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data.map((v) => {\n            switch (v.Check_Type) {\n              case 1 : v.Check_Type = '质量'; break\n              case 2 : v.Check_Type = '探伤'; break\n              case -1 : v.Check_Type = '质量、探伤'; break\n              default: v.Check_Type = ''\n            }\n            v.Create_Date = timeFormat(v.Create_Date, '{y}-{m}-{d} {h}:{i}:{s}')\n            return v\n          })\n          this.tbLoading = false\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n          this.tbLoading = false\n        }\n      })\n    },\n    // 删除单个检查项组合\n    removeEvent(row) {\n      console.log(row)\n      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DelQualityList({ id: row.Id }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getQualityList()\n            } else {\n              this.$message({\n                type: 'error',\n                message: res.Message\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    },\n    // 编辑每行信息\n    editEvent(row) {\n      // 获取每行内容\n      console.log('row', row)\n      this.$emit('CombinationEdit', row)\n    }\n  }\n}\n</script>\n\n<style scoped></style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,SAAAA,aAAA;AACA,SAAAC,4BAAA;AACA,SAAAC,WAAA;AACA,SAAAC,cAAA;AACA,SAAAC,UAAA;AACA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;MACAC,MAAA;MACAC,UAAA;MACAC,MAAA;IAEA;EACA;EACAC,KAAA;IACAV,SAAA;MACAW,OAAA,WAAAA,QAAAC,OAAA,EAAAC,OAAA;QACA,KAAAb,SAAA,GAAAY,OAAA;QACA,KAAAE,cAAA;MACA;MACAC,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACAD,WAAA,WAAAA,YAAA;MAAA,IAAAE,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA,EAAApB,IAAA,EAAAqB,iBAAA;QAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAN,GAAA;cACApB,IAAA;cAAAwB,QAAA,CAAAE,IAAA;cAAA,OACAnC,4BAAA;gBACAoC,SAAA,EAAAC,YAAA,CAAAC,OAAA;cACA;YAAA;cAFAT,GAAA,GAAAI,QAAA,CAAAM,IAAA;cAGA9B,IAAA,GAAAoB,GAAA,CAAAW,IAAA;cACA,IAAAX,GAAA,CAAAY,SAAA;gBACAjB,KAAA,CAAAX,UAAA,GAAAN,MAAA,CAAAmC,MAAA,CAAAjC,IAAA;gBACAkC,OAAA,CAAAC,GAAA,CAAApB,KAAA,CAAAX,UAAA;gBACA,IAAAW,KAAA,CAAAX,UAAA,CAAAgC,MAAA;kBACArB,KAAA,CAAAZ,MAAA,IAAAkB,iBAAA,GAAAN,KAAA,CAAAX,UAAA,iBAAAiB,iBAAA,uBAAAA,iBAAA,CAAAgB,EAAA;kBACAtB,KAAA,CAAAuB,SAAA;gBACA;cACA;gBACAvB,KAAA,CAAAwB,QAAA;kBACAC,OAAA,EAAApB,GAAA,CAAAqB,OAAA;kBACA5C,IAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA2B,QAAA,CAAAkB,IAAA;UAAA;QAAA,GAAAvB,OAAA;MAAA;IACA;IACAmB,SAAA,WAAAA,UAAA;MAAA,IAAAK,MAAA;MAAA,OAAA3B,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA0B,SAAA;QAAA,OAAA3B,mBAAA,GAAAK,IAAA,UAAAuB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArB,IAAA,GAAAqB,SAAA,CAAApB,IAAA;YAAA;cAAAoB,SAAA,CAAApB,IAAA;cAAA,OACAiB,MAAA,CAAAI,cAAA;YAAA;YAAA;cAAA,OAAAD,SAAA,CAAAJ,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IAEA;IACA;IACAG,cAAA,WAAAA,eAAAC,IAAA;MAAA,IAAAC,MAAA;MACA3D,aAAA;QAAA0D,IAAA,EAAAA,IAAA,cAAA5C,UAAA,CAAA8C,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAd,EAAA,KAAAY,MAAA,CAAA9C,MAAA;QAAA,GAAAiD;MAAA,GAAAC,IAAA,WAAAjC,GAAA;QACA,IAAAY,SAAA,GAAAZ,GAAA,CAAAY,SAAA;UAAAD,IAAA,GAAAX,GAAA,CAAAW,IAAA;UAAAU,OAAA,GAAArB,GAAA,CAAAqB,OAAA;QACA,IAAAT,SAAA;UACA,KAAAD,IAAA;YACAkB,MAAA,CAAAV,QAAA,CAAAe,KAAA;YACAL,MAAA,CAAA/C,SAAA;YACA;UACA;UACA+C,MAAA,CAAA/C,SAAA;UACA,IAAAqD,IAAA,GAAAxB,IAAA,CAAAyB,UAAA;UACAP,MAAA,CAAAhD,OAAA,GAAAsD,IAAA,CAAAE,MAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAC,UAAA;UAAA,GAAAC,GAAA,WAAAC,IAAA;YACA,IAAAA,IAAA,CAAAT,IAAA;cACAS,IAAA,CAAAC,KAAA;YACA;YACA,OAAAD,IAAA;UACA;UACA3B,OAAA,CAAAC,GAAA,CAAAc,MAAA,CAAAhD,OAAA;QACA;UACAgD,MAAA,CAAAV,QAAA;YACAC,OAAA,EAAAC,OAAA;YACA5C,IAAA;UACA;QACA;MACA;IACA;IACA;IACAa,cAAA,WAAAA,eAAA;MAAA,IAAAqD,MAAA;MACA,KAAA7D,SAAA;MACAV,WAAA;QAAAwE,eAAA,OAAApE,SAAA,CAAAyC,EAAA;QAAA4B,SAAA,OAAArE,SAAA,CAAAwD;MAAA,GAAAC,IAAA,WAAAjC,GAAA;QACA,IAAAA,GAAA,CAAAY,SAAA;UACA+B,MAAA,CAAA1D,MAAA,GAAAe,GAAA,CAAAW,IAAA,CAAA6B,GAAA,WAAAF,CAAA;YACA,QAAAA,CAAA,CAAAQ,UAAA;cACA;gBAAAR,CAAA,CAAAQ,UAAA;gBAAA;cACA;gBAAAR,CAAA,CAAAQ,UAAA;gBAAA;cACA;gBAAAR,CAAA,CAAAQ,UAAA;gBAAA;cACA;gBAAAR,CAAA,CAAAQ,UAAA;YACA;YACAR,CAAA,CAAAS,WAAA,GAAAzE,UAAA,CAAAgE,CAAA,CAAAS,WAAA;YACA,OAAAT,CAAA;UACA;UACAK,MAAA,CAAA7D,SAAA;QACA;UACA6D,MAAA,CAAAxB,QAAA;YACA1C,IAAA;YACA2C,OAAA,EAAApB,GAAA,CAAAqB;UACA;UACAsB,MAAA,CAAA7D,SAAA;QACA;MACA;IACA;IACA;IACAkE,WAAA,WAAAA,YAAAC,GAAA;MAAA,IAAAC,MAAA;MACApC,OAAA,CAAAC,GAAA,CAAAkC,GAAA;MACA,KAAAE,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACA5E,IAAA;MACA,GACAwD,IAAA;QACA5D,cAAA;UAAAiF,EAAA,EAAAL,GAAA,CAAAhC;QAAA,GAAAgB,IAAA,WAAAjC,GAAA;UACA,IAAAA,GAAA,CAAAY,SAAA;YACAsC,MAAA,CAAA/B,QAAA;cACA1C,IAAA;cACA2C,OAAA;YACA;YACA8B,MAAA,CAAA5D,cAAA;UACA;YACA4D,MAAA,CAAA/B,QAAA;cACA1C,IAAA;cACA2C,OAAA,EAAApB,GAAA,CAAAqB;YACA;UACA;QACA;MACA,GACAkC,KAAA;QACAL,MAAA,CAAA/B,QAAA;UACA1C,IAAA;UACA2C,OAAA;QACA;MACA;IACA;IACA;IACAoC,SAAA,WAAAA,UAAAP,GAAA;MACA;MACAnC,OAAA,CAAAC,GAAA,QAAAkC,GAAA;MACA,KAAAQ,KAAA,oBAAAR,GAAA;IACA;EACA;AACA", "ignoreList": []}]}
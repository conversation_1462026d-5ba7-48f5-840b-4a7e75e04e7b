{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\CheckCombination.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\CheckCombination.vue", "mtime": 1757468112631}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetGridByCode", "GetFactoryProfessionalByCode", "QualityList", "DelQualityList", "timeFormat", "props", "checkType", "type", "Object", "default", "data", "columns", "tbLoading", "TypeId", "typeOption", "tbData", "watch", "handler", "newName", "old<PERSON>ame", "getQualityList", "deep", "mounted", "getTypeList", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "_this$typeOption$", "wrap", "_callee$", "_context", "prev", "next", "factoryId", "localStorage", "getItem", "sent", "Data", "IsSucceed", "freeze", "console", "log", "length", "Id", "fetchData", "$message", "message", "Message", "stop", "_this2", "_callee2", "_callee2$", "_context2", "getTableConfig", "code", "_this3", "find", "i", "Code", "then", "error", "list", "ColumnList", "filter", "v", "Is_Display", "map", "item", "fixed", "_this4", "check_object_id", "Bom_Level", "Check_Type", "Create_Date", "removeEvent", "row", "_this5", "$confirm", "confirmButtonText", "cancelButtonText", "id", "catch", "editEvent", "$emit"], "sources": ["src/views/PRO/factoryQuality/checkoutGroup/components/CheckCombination.vue"], "sourcesContent": ["<template>\r\n  <div style=\"height: calc(100vh - 300px)\">\r\n    <vxe-table\r\n      v-loading=\"tbLoading\"\r\n      :empty-render=\"{name: 'NotData'}\"\r\n      show-header-overflow\r\n      element-loading-spinner=\"el-icon-loading\"\r\n      element-loading-text=\"拼命加载中\"\r\n      empty-text=\"暂无数据\"\r\n      height=\"100%\"\r\n      :data=\"tbData\"\r\n      stripe\r\n      resizable\r\n      :auto-resize=\"true\"\r\n      class=\"cs-vxe-table\"\r\n      :tooltip-config=\"{ enterable: true }\"\r\n    >\r\n      <!-- <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" /> -->\r\n      <vxe-column\r\n        v-for=\"(item, index) in columns\"\r\n        :key=\"index\"\r\n        :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n        show-overflow=\"tooltip\"\r\n        sortable\r\n        :align=\"item.Align\"\r\n        :field=\"item.Code\"\r\n        :title=\"item.Display_Name\"\r\n      >\r\n        <template #default=\"{ row }\">\r\n          <span>{{ row[item.Code] || '-' }}</span>\r\n        </template>\r\n      </vxe-column>\r\n      <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" align=\"center\" show-overflow>\r\n        <template #default=\"{ row }\">\r\n          <el-button type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\r\n          <el-divider direction=\"vertical\" />\r\n          <el-button type=\"text\" @click=\"removeEvent(row)\">删除</el-button>\r\n        </template>\r\n      </vxe-column>\r\n    </vxe-table>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\r\nimport { QualityList } from '@/api/PRO/factorycheck'\r\nimport { DelQualityList } from '@/api/PRO/factorycheck'\r\nimport { timeFormat } from '@/filters'\r\nexport default {\r\n  props: {\r\n    checkType: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      columns: null,\r\n      tbLoading: false,\r\n      TypeId: '',\r\n      typeOption: '',\r\n      tbData: []\r\n\r\n    }\r\n  },\r\n  watch: {\r\n    checkType: {\r\n      handler(newName, oldName) {\r\n        this.checkType = newName\r\n        this.getQualityList()\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getQualityList()\r\n    this.getTypeList()\r\n  },\r\n  methods: {\r\n    async getTypeList() {\r\n      let res = null\r\n      let data = null\r\n      res = await GetFactoryProfessionalByCode({\r\n        factoryId: localStorage.getItem('CurReferenceId')\r\n      })\r\n      data = res.Data\r\n      if (res.IsSucceed) {\r\n        this.typeOption = Object.freeze(data)\r\n        console.log(this.typeOption)\r\n        if (this.typeOption.length > 0) {\r\n          this.TypeId = this.typeOption[0]?.Id\r\n          this.fetchData()\r\n        }\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    async fetchData() {\r\n      await this.getTableConfig('Check_item_combination')\r\n    //   this.tbLoading = true;\r\n    },\r\n    // 获取列表\r\n    getTableConfig(code) {\r\n      GetGridByCode({ code: code + ',' + this.typeOption.find((i) => i.Id === this.TypeId).Code }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          if (!Data) {\r\n            this.$message.error('当前专业没有配置相对应表格')\r\n            this.tbLoading = true\r\n            return\r\n          }\r\n          this.tbLoading = false\r\n          const list = Data.ColumnList || []\r\n          this.columns = list.filter((v) => v.Is_Display).map((item) => {\r\n            if (item.Code === 'CheckName') {\r\n              item.fixed = 'left'\r\n            }\r\n            return item\r\n          })\r\n          console.log(this.columns)\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 获取检查项组合列表\r\n    getQualityList() {\r\n      this.tbLoading = true\r\n      QualityList({ check_object_id: this.checkType.Id, Bom_Level: this.checkType.Code }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.map((v) => {\r\n            switch (v.Check_Type) {\r\n              case 1 : v.Check_Type = '质量'; break\r\n              case 2 : v.Check_Type = '探伤'; break\r\n              case -1 : v.Check_Type = '质量、探伤'; break\r\n              default: v.Check_Type = ''\r\n            }\r\n            v.Create_Date = timeFormat(v.Create_Date, '{y}-{m}-{d} {h}:{i}:{s}')\r\n            return v\r\n          })\r\n          this.tbLoading = false\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n          this.tbLoading = false\r\n        }\r\n      })\r\n    },\r\n    // 删除单个检查项组合\r\n    removeEvent(row) {\r\n      console.log(row)\r\n      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          DelQualityList({ id: row.Id }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                type: 'success',\r\n                message: '删除成功!'\r\n              })\r\n              this.getQualityList()\r\n            } else {\r\n              this.$message({\r\n                type: 'error',\r\n                message: res.Message\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n    // 编辑每行信息\r\n    editEvent(row) {\r\n      // 获取每行内容\r\n      console.log('row', row)\r\n      this.$emit('CombinationEdit', row)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped></style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,SAAAA,aAAA;AACA,SAAAC,4BAAA;AACA,SAAAC,WAAA;AACA,SAAAC,cAAA;AACA,SAAAC,UAAA;AACA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;MACAC,MAAA;MACAC,UAAA;MACAC,MAAA;IAEA;EACA;EACAC,KAAA;IACAV,SAAA;MACAW,OAAA,WAAAA,QAAAC,OAAA,EAAAC,OAAA;QACA,KAAAb,SAAA,GAAAY,OAAA;QACA,KAAAE,cAAA;MACA;MACAC,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAF,cAAA;IACA,KAAAG,WAAA;EACA;EACAC,OAAA;IACAD,WAAA,WAAAA,YAAA;MAAA,IAAAE,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA,EAAApB,IAAA,EAAAqB,iBAAA;QAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAN,GAAA;cACApB,IAAA;cAAAwB,QAAA,CAAAE,IAAA;cAAA,OACAnC,4BAAA;gBACAoC,SAAA,EAAAC,YAAA,CAAAC,OAAA;cACA;YAAA;cAFAT,GAAA,GAAAI,QAAA,CAAAM,IAAA;cAGA9B,IAAA,GAAAoB,GAAA,CAAAW,IAAA;cACA,IAAAX,GAAA,CAAAY,SAAA;gBACAjB,KAAA,CAAAX,UAAA,GAAAN,MAAA,CAAAmC,MAAA,CAAAjC,IAAA;gBACAkC,OAAA,CAAAC,GAAA,CAAApB,KAAA,CAAAX,UAAA;gBACA,IAAAW,KAAA,CAAAX,UAAA,CAAAgC,MAAA;kBACArB,KAAA,CAAAZ,MAAA,IAAAkB,iBAAA,GAAAN,KAAA,CAAAX,UAAA,iBAAAiB,iBAAA,uBAAAA,iBAAA,CAAAgB,EAAA;kBACAtB,KAAA,CAAAuB,SAAA;gBACA;cACA;gBACAvB,KAAA,CAAAwB,QAAA;kBACAC,OAAA,EAAApB,GAAA,CAAAqB,OAAA;kBACA5C,IAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA2B,QAAA,CAAAkB,IAAA;UAAA;QAAA,GAAAvB,OAAA;MAAA;IACA;IACAmB,SAAA,WAAAA,UAAA;MAAA,IAAAK,MAAA;MAAA,OAAA3B,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA0B,SAAA;QAAA,OAAA3B,mBAAA,GAAAK,IAAA,UAAAuB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArB,IAAA,GAAAqB,SAAA,CAAApB,IAAA;YAAA;cAAAoB,SAAA,CAAApB,IAAA;cAAA,OACAiB,MAAA,CAAAI,cAAA;YAAA;YAAA;cAAA,OAAAD,SAAA,CAAAJ,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IAEA;IACA;IACAG,cAAA,WAAAA,eAAAC,IAAA;MAAA,IAAAC,MAAA;MACA3D,aAAA;QAAA0D,IAAA,EAAAA,IAAA,cAAA5C,UAAA,CAAA8C,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAd,EAAA,KAAAY,MAAA,CAAA9C,MAAA;QAAA,GAAAiD;MAAA,GAAAC,IAAA,WAAAjC,GAAA;QACA,IAAAY,SAAA,GAAAZ,GAAA,CAAAY,SAAA;UAAAD,IAAA,GAAAX,GAAA,CAAAW,IAAA;UAAAU,OAAA,GAAArB,GAAA,CAAAqB,OAAA;QACA,IAAAT,SAAA;UACA,KAAAD,IAAA;YACAkB,MAAA,CAAAV,QAAA,CAAAe,KAAA;YACAL,MAAA,CAAA/C,SAAA;YACA;UACA;UACA+C,MAAA,CAAA/C,SAAA;UACA,IAAAqD,IAAA,GAAAxB,IAAA,CAAAyB,UAAA;UACAP,MAAA,CAAAhD,OAAA,GAAAsD,IAAA,CAAAE,MAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAC,UAAA;UAAA,GAAAC,GAAA,WAAAC,IAAA;YACA,IAAAA,IAAA,CAAAT,IAAA;cACAS,IAAA,CAAAC,KAAA;YACA;YACA,OAAAD,IAAA;UACA;UACA3B,OAAA,CAAAC,GAAA,CAAAc,MAAA,CAAAhD,OAAA;QACA;UACAgD,MAAA,CAAAV,QAAA;YACAC,OAAA,EAAAC,OAAA;YACA5C,IAAA;UACA;QACA;MACA;IACA;IACA;IACAa,cAAA,WAAAA,eAAA;MAAA,IAAAqD,MAAA;MACA,KAAA7D,SAAA;MACAV,WAAA;QAAAwE,eAAA,OAAApE,SAAA,CAAAyC,EAAA;QAAA4B,SAAA,OAAArE,SAAA,CAAAwD;MAAA,GAAAC,IAAA,WAAAjC,GAAA;QACA,IAAAA,GAAA,CAAAY,SAAA;UACA+B,MAAA,CAAA1D,MAAA,GAAAe,GAAA,CAAAW,IAAA,CAAA6B,GAAA,WAAAF,CAAA;YACA,QAAAA,CAAA,CAAAQ,UAAA;cACA;gBAAAR,CAAA,CAAAQ,UAAA;gBAAA;cACA;gBAAAR,CAAA,CAAAQ,UAAA;gBAAA;cACA;gBAAAR,CAAA,CAAAQ,UAAA;gBAAA;cACA;gBAAAR,CAAA,CAAAQ,UAAA;YACA;YACAR,CAAA,CAAAS,WAAA,GAAAzE,UAAA,CAAAgE,CAAA,CAAAS,WAAA;YACA,OAAAT,CAAA;UACA;UACAK,MAAA,CAAA7D,SAAA;QACA;UACA6D,MAAA,CAAAxB,QAAA;YACA1C,IAAA;YACA2C,OAAA,EAAApB,GAAA,CAAAqB;UACA;UACAsB,MAAA,CAAA7D,SAAA;QACA;MACA;IACA;IACA;IACAkE,WAAA,WAAAA,YAAAC,GAAA;MAAA,IAAAC,MAAA;MACApC,OAAA,CAAAC,GAAA,CAAAkC,GAAA;MACA,KAAAE,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACA5E,IAAA;MACA,GACAwD,IAAA;QACA5D,cAAA;UAAAiF,EAAA,EAAAL,GAAA,CAAAhC;QAAA,GAAAgB,IAAA,WAAAjC,GAAA;UACA,IAAAA,GAAA,CAAAY,SAAA;YACAsC,MAAA,CAAA/B,QAAA;cACA1C,IAAA;cACA2C,OAAA;YACA;YACA8B,MAAA,CAAA5D,cAAA;UACA;YACA4D,MAAA,CAAA/B,QAAA;cACA1C,IAAA;cACA2C,OAAA,EAAApB,GAAA,CAAAqB;YACA;UACA;QACA;MACA,GACAkC,KAAA;QACAL,MAAA,CAAA/B,QAAA;UACA1C,IAAA;UACA2C,OAAA;QACA;MACA;IACA;IACA;IACAoC,SAAA,WAAAA,UAAAP,GAAA;MACA;MACAnC,OAAA,CAAAC,GAAA,QAAAkC,GAAA;MACA,KAAAQ,KAAA,oBAAAR,GAAA;IACA;EACA;AACA", "ignoreList": []}]}
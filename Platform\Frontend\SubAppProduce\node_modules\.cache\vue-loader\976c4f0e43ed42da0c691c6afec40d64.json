{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\detailPrint.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\detailPrint.vue", "mtime": 1758595482001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBnZXRUYkluZm8gZnJvbSAnQC9taXhpbnMvUFJPL2dldC10YWJsZS1pbmZvJwppbXBvcnQgeyBHZXRUZWFtVGFza0RldGFpbHMsIEdldFN1Z2dlc3REZXZpY2VBbmRSZW1hcmsgfSBmcm9tICdAL2FwaS9QUk8vcHJvZHVjdGlvbi10YXNrJwppbXBvcnQgeyBjbG9zZVRhZ1ZpZXcgfSBmcm9tICdAL3V0aWxzJwppbXBvcnQgUXJjb2RlVnVlIGZyb20gJ3FyY29kZS52dWUnCmltcG9ydCB7IG1hcEdldHRlcnMgfSBmcm9tICd2dWV4JwppbXBvcnQgeyBnZXRCb21Db2RlLCBnZXRCb21OYW1lLCBjaGVja0lzVW5pdFBhcnQgfSBmcm9tICdAL3ZpZXdzL1BSTy9ib20tc2V0dGluZy91dGlscycKCmNvbnN0IHByaW50U3R5bGUgPSBgCiAgICAgICAgLnRpdGxlIHsKICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgICAgICB9CiAgICAgICAgLmlzLS1wcmludHsKICAgICAgICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7CiAgICAgICAgICB3aWR0aDo5NSUgIWltcG9ydGFudDsKICAgICAgICAgIG1hcmdpbjowIGF1dG8gIWltcG9ydGFudDsKICAgICAgICB9CiAgICAgICAgLm15LWxpc3Qtcm93IHsKICAgICAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICAgICAgICAgIHdpZHRoOiAxMDAlOwogICAgICAgICAgbWFyZ2luLWxlZnQ6MyU7CiAgICAgICAgfQogICAgICAgIC5teS1saXN0LXJvdy1maXJzdCB7CiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxMHB4OwogICAgICAgIH0KICAgICAgICAubXktbGlzdC1yb3ctc2Vjb25kIHsKICAgICAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgICAgICAgfQogICAgICAgIC5teS1saXN0LXJvdy10aGlyZCB7CiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxMHB4OwogICAgICAgIH0KICAgICAgICAubXktbGlzdC1jb2wgewogICAgICAgICAgd2lkdGg6MzAlOwogICAgICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogICAgICAgICAgZmxvYXQ6IGxlZnQ7CiAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDElOwogICAgICAgICAgd29yZC13cmFwOmJyZWFrLXdvcmQ7CiAgICAgICAgICB3b3JkLWJyZWFrOm5vcm1hbDsKICAgICAgICB9CiAgICAgICAgLmxlZnR7CiAgICAgICAgICBmbGV4OjE7CiAgICAgICAgfQogICAgICAgIC5teS10b3AgewogICAgICAgICAgZGlzcGxheTpmbGV4OwogICAgICAgICAgZm9udC1zaXplOiAxMnB4OwogICAgICAgICAgbWFyZ2luLWJvdHRvbTogNXB4OwogICAgICAgIH0KICAgICAgICAucXJjb2RlewogICAgICAgICAgbWFyZ2luLXJpZ2h0OjEwcHgKICAgICAgICB9CiAgICAgICAgLmNzLWltZ3sKICAgICAgICAgIHBvc2l0aW9uOnJlbGF0aXZlOwogICAgICAgICAgcmlnaHQ6MzBweAogICAgICAgIH0KICAgICAgICBgCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1BST1Rhc2tMaXN0RGV0YWlsUHJpbnQnLAogIGNvbXBvbmVudHM6IHsKICAgIFFyY29kZVZ1ZQogIH0sCiAgbWl4aW5zOiBbZ2V0VGJJbmZvXSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgYm9tTmFtZTogJycsCiAgICAgIHByaW50RGF0YTogW10sCiAgICAgIHRiQ29uZmlnOiB7CiAgICAgICAgT3BfV2lkdGg6IDEyMAogICAgICB9LAogICAgICB0YkxvYWRpbmc6IGZhbHNlLAogICAgICB0YkRhdGE6IFtdLAogICAgICBjb2x1bW5zOiBbXSwKICAgICAgcGFnZVR5cGU6ICcnLAogICAgICBjb21tYW5kOiAnJywKICAgICAgcHJpbnRDb2x1bW5zOiBbXSwKICAgICAgaW5mbzogewogICAgICAgIFRhc2tfQ29kZTogJycsCiAgICAgICAgUHJvamVjdF9OYW1lOiAnJywKICAgICAgICBBcmVhX05hbWU6ICcnLAogICAgICAgIEluc3RhbGxVbml0X05hbWU6ICcnLAogICAgICAgIFNjaGR1bGluZ19Db2RlOiAnJywKICAgICAgICBUYXNrX0ZpbmlzaF9EYXRlOiAnJywKICAgICAgICBGaW5pc2hfRGF0ZTI6ICcnLAogICAgICAgIE9yZGVyX0RhdGU6ICcnLAogICAgICAgIFdvcmtpbmdfVGVhbV9OYW1lOiAnJywKICAgICAgICBXb3JraW5nX1Byb2Nlc3NfTmFtZTogJycsCiAgICAgICAgUHJvY2Vzc19TdGFydF9EYXRlOiAnJywKICAgICAgICBQcm9jZXNzX0ZpbmlzaF9EYXRlOiAnJwogICAgICB9LAogICAgICBwcmludENvbmZpZzogewogICAgICAgIHNoZWV0TmFtZTogJ+S7u+WKoeWNleivpuaDhScsCiAgICAgICAgc3R5bGU6IHByaW50U3R5bGUsCiAgICAgICAgYmVmb3JlUHJpbnRNZXRob2Q6ICh7IGNvbnRlbnQgfSkgPT4gewogICAgICAgICAgcmV0dXJuIHRoaXMudG9wSHRtbCArIGNvbnRlbnQKICAgICAgICB9CiAgICAgIH0sCiAgICAgIFRlbmFudF9Db2RlOiBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndGVuYW50JyksCiAgICAgIFJlbWFyazogJycsCiAgICAgIGVxcHRJbmZvTGlzdDogW10sCiAgICAgIGVxcHRJbmZvTGlzdFN0cjogJycKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICAuLi5tYXBHZXR0ZXJzKCd0ZW5hbnQnLCBbJ2lzVmVyc2lvbkZvdXInXSksCiAgICBpc0NvbSgpIHsKICAgICAgcmV0dXJuIHRoaXMucGFnZVR5cGUgPT09IGdldEJvbUNvZGUoJy0xJykKICAgIH0sCiAgICBpc1VuaXRQYXJ0KCkgewogICAgICByZXR1cm4gY2hlY2tJc1VuaXRQYXJ0KHRoaXMucGFnZVR5cGUpCiAgICB9LAogICAgaXNQYXJ0KCkgewogICAgICByZXR1cm4gdGhpcy5wYWdlVHlwZSA9PT0gZ2V0Qm9tQ29kZSgnMCcpCiAgICB9CiAgfSwKICBhc3luYyBtb3VudGVkKCkgewogICAgdGhpcy5wYWdlVHlwZSA9IHRoaXMuJHJvdXRlLnF1ZXJ5LnR5cGUKICAgIHRoaXMuY29tbWFuZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LmNvbW1hbmQKICAgIHRoaXMuYm9tTmFtZSA9IGF3YWl0IGdldEJvbU5hbWUodGhpcy5wYWdlVHlwZSkKICAgIHRoaXMuaW5mbyA9IEpTT04ucGFyc2UoZGVjb2RlVVJJQ29tcG9uZW50KHRoaXMuJHJvdXRlLnF1ZXJ5Lm90aGVyKSkKICAgIGF3YWl0IHRoaXMuZ2V0U3VnZ2VzdERldmljZUFuZFJlbWFyaygpCiAgICBhd2FpdCB0aGlzLmdldFRhYmxlQ29uZmlnKHRoaXMuaXNDb20gPyAnUFJPQ29tVGFza0xpc3REZXRhaWwnIDogdGhpcy5pc1VuaXRQYXJ0ID8gJ1BST1VuaXRQYXJ0VGFza0xpc3REZXRhaWwnIDogJ1BST1BhcnRUYXNrTGlzdERldGFpbCcpCiAgICBpZiAodGhpcy5pc0NvbSkgewogICAgICB0aGlzLmNvbHVtbnMgPSB0aGlzLmNvbHVtbnMuZmlsdGVyKGl0ZW0gPT4gaXRlbS5Db2RlICE9PSAnUGFydF9Db2RlJykKICAgICAgdGhpcy5wcmludENvbHVtbnMgPSB0aGlzLmNvbHVtbnMuZmlsdGVyKGl0ZW0gPT4gaXRlbS5Db2RlICE9PSAnUGFydF9Db2RlJyAmJiBpdGVtLkNvZGUgIT09ICdDb21wX0Rlc2NyaXB0aW9uJykKICAgICAgaWYgKHRoaXMuY29tbWFuZCA9PT0gJ2NvZGUnKSB7CiAgICAgICAgdGhpcy5jb2x1bW5zID0gdGhpcy5jb2x1bW5zLmZpbHRlcihpdGVtID0+IGl0ZW0uQ29kZSAhPT0gJ0NvbXBfQ29kZScgJiYgaXRlbS5Db2RlICE9PSAnUGFydF9Db2RlJykKICAgICAgICB0aGlzLnByaW50Q29sdW1ucyA9IHRoaXMuY29sdW1ucy5maWx0ZXIoaXRlbSA9PiBpdGVtLkNvZGUgIT09ICdDb21wX0NvZGUnICYmIGl0ZW0uQ29kZSAhPT0gJ0NvbXBfRGVzY3JpcHRpb24nICYmIGl0ZW0uQ29kZSAhPT0gJ1BhcnRfQ29kZScpCiAgICAgIH0KICAgIH0KCiAgICBpZiAodGhpcy5pc1VuaXRQYXJ0KSB7CiAgICAgIHRoaXMucHJpbnRDb2x1bW5zID0gdGhpcy5jb2x1bW5zLmZpbHRlcihjb2x1bW4gPT4gY29sdW1uLkNvZGUgIT09ICdQcm9qZWN0X05hbWUnICYmIGNvbHVtbi5Db2RlICE9PSAnQXJlYV9OYW1lJyAmJiBjb2x1bW4uQ29kZSAhPT0gJ0ZpbmlzaF9Db3VudCcgJiYgY29sdW1uLkNvZGUgIT09ICdGaW5pc2hfV2VpZ2h0JyAmJiBjb2x1bW4uQ29kZSAhPT0gJ0NvbXBfRGVzY3JpcHRpb24nKQogICAgfQogICAgaWYgKHRoaXMuaXNQYXJ0KSB7CiAgICAgIHRoaXMucHJpbnRDb2x1bW5zID0gdGhpcy5jb2x1bW5zLmZpbHRlcihjb2x1bW4gPT4gY29sdW1uLkNvZGUgIT09ICdQcm9qZWN0X05hbWUnICYmIGNvbHVtbi5Db2RlICE9PSAnQXJlYV9OYW1lJyAmJiBjb2x1bW4uQ29kZSAhPT0gJ0ZpbmlzaF9Db3VudCcgJiYgY29sdW1uLkNvZGUgIT09ICdGaW5pc2hfV2VpZ2h0JyAmJiBjb2x1bW4uQ29kZSAhPT0gJ0NvbXBfRGVzY3JpcHRpb24nKQogICAgfQoKICAgIGNvbnN0IGRhdGEgPSBhd2FpdCB0aGlzLmZldGNoRGF0YSh0aGlzLmluZm8uVGFza19Db2RlLCB0aGlzLmluZm8uV29ya2luZ19UZWFtX0lkKQogICAgaWYgKHRoaXMuaXNDb20gJiYgdGhpcy5jb21tYW5kID09PSAnY29kZScpIHsKICAgICAgdGhpcy50YkRhdGEgPSB0aGlzLm1lcmdlU2ltaWxhckl0ZW1zKGRhdGEpCiAgICB9IGVsc2UgewogICAgICB0aGlzLnRiRGF0YSA9IGRhdGEKICAgIH0KCiAgICB0aGlzLmdldEh0bWwoKQogICAgdGhpcy5wcmludEV2ZW50KCkKICB9LAogIG1ldGhvZHM6IHsKICAgIGdldFN1Z2dlc3REZXZpY2VBbmRSZW1hcmsoKSB7CiAgICAgIEdldFN1Z2dlc3REZXZpY2VBbmRSZW1hcmsoewogICAgICAgIEJvbV9MZXZlbDogdGhpcy5wYWdlVHlwZSwKICAgICAgICBUYXNrX0NvZGU6IHRoaXMuaW5mby5UYXNrX0NvZGUKICAgICAgfSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIHRoaXMuUmVtYXJrID0gcmVzLkRhdGE/LlJlbWFyawogICAgICAgICAgdGhpcy5lcXB0SW5mb0xpc3QgPSByZXMuRGF0YT8uZXFwdEluZm9MaXN0IHx8IFtdCiAgICAgICAgICB0aGlzLmVxcHRJbmZvTGlzdFN0ciA9IHRoaXMuZXFwdEluZm9MaXN0Lm1hcChpdGVtID0+IGl0ZW0uRGlzcGxheU5hbWUpLmpvaW4oJywnKQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGFzeW5jIGZldGNoRGF0YShUYXNrX0NvZGUsIFdvcmtpbmdfVGVhbV9JZCwgaWR4KSB7CiAgICAgIHRoaXMudGJMb2FkaW5nID0gdHJ1ZQogICAgICBjb25zdCByZXMgPSBhd2FpdCBHZXRUZWFtVGFza0RldGFpbHMoewogICAgICAgIFBhZ2U6IC0xLAogICAgICAgIFBhZ2VTaXplOiAtMSwKICAgICAgICBCb21fTGV2ZWw6IHRoaXMucGFnZVR5cGUsCiAgICAgICAgUHJvY2Vzc19UeXBlOiB0aGlzLmlzQ29tID8gMiA6IHRoaXMuaXNQYXJ0ID8gMSA6IDMsIC8vIDHpm7bku7bvvIwy5p6E5Lu2CiAgICAgICAgV29ya2luZ19UZWFtX0lkOiBXb3JraW5nX1RlYW1fSWQsCiAgICAgICAgVGFza19Db2RlOiBUYXNrX0NvZGUsCiAgICAgICAgTmV4dF9UZWFtX0lkOiAnJywKICAgICAgICBOZXh0X1Byb2Nlc3NfSWQ6ICcnCiAgICAgIH0pCiAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgY29uc3QgZGF0YSA9IHJlcy5EYXRhLkRhdGEuZmlsdGVyKGl0ZW0gPT4gewogICAgICAgICAgcmV0dXJuIGl0ZW0uQWxsb2NhdGlvbl9Db3VudCAhPT0gMAogICAgICAgIH0pCiAgICAgICAgdGhpcy50YkxvYWRpbmcgPSBmYWxzZQogICAgICAgIHJldHVybiBkYXRhCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy50YkxvYWRpbmcgPSBmYWxzZQogICAgICB9CiAgICB9LAoKICAgIG1lcmdlU2ltaWxhckl0ZW1zKGRhdGEpIHsKICAgICAgLy8g5Yib5bu65LiA5LiqIE1hcCDmnaXlrZjlgqjlkIjlubblkI7nmoTmlbDmja7vvIzkvb/nlKjlhbPplK7lrZfmrrXnmoTnu4TlkIjkvZzkuLrplK4KICAgICAgY29uc3QgbWVyZ2VkTWFwID0gbmV3IE1hcCgpCgogICAgICAvLyDpgY3ljobljp/lp4vmlbDmja4KICAgICAgZm9yIChjb25zdCBpdGVtIG9mIGRhdGEpIHsKICAgICAgICAvLyDlpoLmnpwgU3RlZWxfQ29kZSDkuLrnqbrmiJYgbnVsbO+8jOebtOaOpeWKoOWFpee7k+aenO+8jOS4jeWPguS4juWQiOW5tgogICAgICAgIGlmIChpdGVtLlN0ZWVsX0NvZGUgPT0gbnVsbCB8fCBpdGVtLlN0ZWVsX0NvZGUgPT09ICcnKSB7CiAgICAgICAgICAvLyDnoa7kv53mlbDlgLzlrZfmrrXmnInpu5jorqTlgLwKICAgICAgICAgIGNvbnN0IG5ld0l0ZW0gPSB7CiAgICAgICAgICAgIC4uLml0ZW0sCiAgICAgICAgICAgIEFsbG9jYXRpb25fV2VpZ2h0OiBpdGVtLkFsbG9jYXRpb25fV2VpZ2h0IHx8IDAsCiAgICAgICAgICAgIEZpbmlzaF9XZWlnaHQ6IGl0ZW0uRmluaXNoX1dlaWdodCB8fCAwLAogICAgICAgICAgICBBbGxvY2F0aW9uX0NvdW50OiBpdGVtLkFsbG9jYXRpb25fQ291bnQgfHwgMCwKICAgICAgICAgICAgRmluaXNoX0NvdW50OiBpdGVtLkZpbmlzaF9Db3VudCB8fCAwCiAgICAgICAgICB9CiAgICAgICAgICAvLyDkvb/nlKjkuIDkuKrllK/kuIDplK7vvIjlpoLntKLlvJXvvInmnaXpgb/lhY3opobnm5YKICAgICAgICAgIG1lcmdlZE1hcC5zZXQoYG51bGxfJHttZXJnZWRNYXAuc2l6ZX1gLCBuZXdJdGVtKQogICAgICAgICAgY29udGludWUKICAgICAgICB9CgogICAgICAgIC8vIOWIm+W7uuWFs+mUruWtl+auteeahOe7hOWQiOS9nOS4uumUrgogICAgICAgIGNvbnN0IGtleSA9IFsKICAgICAgICAgIGl0ZW0uU3RlZWxfQ29kZSwKICAgICAgICAgIGl0ZW0uU3BlYywKICAgICAgICAgIGl0ZW0uV2VpZ2h0LAogICAgICAgICAgaXRlbS5NYWluX1BhcnQsCiAgICAgICAgICBpdGVtLkF0dGFjaGVkQm9hcmRzTnVtYmVyLAogICAgICAgICAgaXRlbS5MZW5ndGgsCiAgICAgICAgICBpdGVtLlRleHR1cmUsCiAgICAgICAgICBpdGVtLk5leHRfUHJvY2Vzc19OYW1lLAogICAgICAgICAgaXRlbS5OZXh0X1dvcmtpbmdfVGVhbV9OYW1lcywKICAgICAgICAgIGl0ZW0uSW5zdGFsbFVuaXRfTmFtZQogICAgICAgIF0uam9pbignfCcpCgogICAgICAgIC8vIOajgOafpeaYr+WQpuW3sue7j+acieebuOWQjOeahOiusOW9lQogICAgICAgIGlmIChtZXJnZWRNYXAuaGFzKGtleSkpIHsKICAgICAgICAgIC8vIOiOt+WPluW3suWtmOWcqOeahOiusOW9lQogICAgICAgICAgY29uc3QgZXhpc3RpbmdJdGVtID0gbWVyZ2VkTWFwLmdldChrZXkpCgogICAgICAgICAgLy8g57Sv5Yqg5pWw5YC85a2X5q61CiAgICAgICAgICBleGlzdGluZ0l0ZW0uQWxsb2NhdGlvbl9XZWlnaHQgKz0gaXRlbS5BbGxvY2F0aW9uX1dlaWdodCB8fCAwCiAgICAgICAgICBleGlzdGluZ0l0ZW0uRmluaXNoX1dlaWdodCArPSBpdGVtLkZpbmlzaF9XZWlnaHQgfHwgMAogICAgICAgICAgZXhpc3RpbmdJdGVtLkFsbG9jYXRpb25fQ291bnQgKz0gaXRlbS5BbGxvY2F0aW9uX0NvdW50IHx8IDAKICAgICAgICAgIGV4aXN0aW5nSXRlbS5GaW5pc2hfQ291bnQgKz0gaXRlbS5GaW5pc2hfQ291bnQgfHwgMAogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBjb25zdCBuZXdJdGVtID0gewogICAgICAgICAgICAuLi5pdGVtLAogICAgICAgICAgICBBbGxvY2F0aW9uX1dlaWdodDogaXRlbS5BbGxvY2F0aW9uX1dlaWdodCB8fCAwLAogICAgICAgICAgICBGaW5pc2hfV2VpZ2h0OiBpdGVtLkZpbmlzaF9XZWlnaHQgfHwgMCwKICAgICAgICAgICAgQWxsb2NhdGlvbl9Db3VudDogaXRlbS5BbGxvY2F0aW9uX0NvdW50IHx8IDAsCiAgICAgICAgICAgIEZpbmlzaF9Db3VudDogaXRlbS5GaW5pc2hfQ291bnQgfHwgMAogICAgICAgICAgfQoKICAgICAgICAgIC8vIOWmguaenOaYr+esrOS4gOasoeWHuueOsO+8jOebtOaOpeaUvuWFpSBNYXAKICAgICAgICAgIG1lcmdlZE1hcC5zZXQoa2V5LCBuZXdJdGVtKQogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g5bCGIE1hcCDovazmjaLlm57mlbDnu4QKICAgICAgcmV0dXJuIEFycmF5LmZyb20obWVyZ2VkTWFwLnZhbHVlcygpKQogICAgfSwKCiAgICBoYW5kbGVSZXNldCgpIHsKICAgICAgdGhpcy4kcmVmc1snZm9ybSddLnJlc2V0RmllbGRzKCkKICAgIH0sCgogICAgZ2V0SHRtbCgpIHsKICAgICAgY29uc3QgcXIgPSB0aGlzLiRyZWZzWydxcmNvZGVSZWYnXQogICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4gewogICAgICAgIHRoaXMuJG5leHRUaWNrKF8gPT4gewogICAgICAgICAgY29uc3QgY2FudmFzID0gcXIuJHJlZnNbJ3FyY29kZS12dWUnXQogICAgICAgICAgY29uc3QgZGF0YVVSTCA9IGNhbnZhcy50b0RhdGFVUkwoJ2ltYWdlL3BuZycpCiAgICAgICAgICB0aGlzLnRvcEh0bWwgPSBgCiAgICAgICAgPGgxIGNsYXNzPSJ0aXRsZSI+IyR7dGhpcy5pbmZvLldvcmtpbmdfUHJvY2Vzc19OYW1lIHx8ICcnfSMg5Yqg5bel5Lu75Yqh5Y2VPC9oMT4KICAgICAgICA8ZGl2IGNsYXNzPSJteS10b3AiPgogICAgICAgICAgPGRpdiBjbGFzcz0ibGVmdCI+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9Im15LWxpc3Qtcm93IG15LWxpc3Qtcm93LWZpcnN0Ij4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJteS1saXN0LWNvbCI+6aG555uu5ZCN56ewL+WMuuWfn++8miR7dGhpcy5pbmZvLlByb2plY3RfTmFtZSB8fCAnJ30vJHt0aGlzLmluZm8uQXJlYV9OYW1lIHx8ICcnfTwvZGl2PgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9Im15LWxpc3QtY29sIj7mjpLkuqfljZXlj7fvvJoke3RoaXMuaW5mby5TY2hkdWxpbmdfQ29kZSB8fCAnJ308L2Rpdj4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJteS1saXN0LWNvbCI+5Yqg5bel54+t57uE77yaJHt0aGlzLmluZm8uV29ya2luZ19UZWFtX05hbWUgfHwgJyd9PC9kaXY+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJteS1saXN0LXJvdyBteS1saXN0LXJvdy1zZWNvbmQiPgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9Im15LWxpc3QtY29sIj7ku7vliqHkuIvljZXml7bpl7TvvJoke3RoaXMuaW5mby5PcmRlcl9EYXRlIHx8ICcnfTwvZGl2PgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9Im15LWxpc3QtY29sIj7ku7vliqHljZXlj7fvvJoke3RoaXMuaW5mbz8uVGFza19Db2RlIHx8ICcnfTwvZGl2PgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9Im15LWxpc3QtY29sIj7lt6Xluo/orqHliJLlvIDlp4vml7bpl7TvvJoke3RoaXMuaW5mby5Qcm9jZXNzX1N0YXJ0X0RhdGUgfHwgJyd9PC9kaXY+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJteS1saXN0LXJvdyBteS1saXN0LXJvdy10aGlyZCI+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ibXktbGlzdC1jb2wiPuW3peW6j+iuoeWIkuWujOaIkOaXtumXtO+8miR7dGhpcy5pbmZvLlByb2Nlc3NfRmluaXNoX0RhdGUgfHwgJyd9PC9kaXY+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ibXktbGlzdC1jb2wiPuWkh+azqO+8miR7dGhpcy5SZW1hcmsgfHwgJyd9PC9kaXY+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJteS1saXN0LXJvdyI+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ibXktbGlzdC1jb2wiPuW7uuiuruiuvuWkh++8miR7dGhpcy5lcXB0SW5mb0xpc3RTdHIgfHwgJyd9PC9kaXY+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJyaWdodCI+CiAgICAgICAgICAgPGltZyBjbGFzcz0iY3MtaW1nIiBzcmM9IiR7ZGF0YVVSTH0iIGFsdD0iIj4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvZGl2PgogICAgICAgIGAKICAgICAgICAgIHJlc29sdmUoKQogICAgICAgIH0pCiAgICAgIH0pCiAgICB9LAogICAgcHJpbnRFdmVudCgpIHsKICAgICAgdGhpcy5nZXRIdG1sKCkudGhlbigoXykgPT4gewogICAgICAgIHRoaXMuJHJlZnMueFRhYmxlLnByaW50KHsKICAgICAgICAgIHNoZWV0TmFtZTogdGhpcy5wcmludENvbmZpZy5zaGVldE5hbWUsCiAgICAgICAgICBzdHlsZTogcHJpbnRTdHlsZSwKICAgICAgICAgIG1vZGU6ICdjdXJyZW50JywKICAgICAgICAgIGNvbHVtbnM6IHRoaXMucHJpbnRDb2x1bW5zLm1hcCgodikgPT4gewogICAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICAgIGZpZWxkOiB2LkNvZGUKICAgICAgICAgICAgfQogICAgICAgICAgfSksCiAgICAgICAgICBiZWZvcmVQcmludE1ldGhvZDogKHsgY29udGVudCB9KSA9PiB7CiAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IHRoaXMudG9wSHRtbCArIGNvbnRlbnQKICAgICAgICAgICAgcmV0dXJuIHJlc3VsdAogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlVmlldyhyb3cpIHsKICAgIH0sCiAgICB0b0JhY2soKSB7CiAgICAgIGNsb3NlVGFnVmlldyh0aGlzLiRzdG9yZSwgdGhpcy4kcm91dGUpCiAgICB9LAoKICAgIC8vIOWNleWFg+agvOeCueWHu+aXtumXtAogICAgY2VsbENsaWNrRXZlbnQoeyByb3csIHJvd0luZGV4LCBjb2x1bW4sIGNvbHVtbkluZGV4IH0pIHsKICAgICAgLy8gaWYgKGNvbHVtbi5wcm9wZXJ0eSA9PT0gIkZpbmlzaF9Db3VudCIgJiYgcm93LkZpbmlzaF9Db3VudCA+IDApIHsKICAgICAgLy8gICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgIC8vICAgICB0aGlzLiRyZWZzLlRyYW5zZmVyRGV0YWlsLmluaXQocm93LCB0aGlzLmlzQ29tKTsKICAgICAgLy8gICB9KTsKICAgICAgLy8gfQogICAgfSwKCiAgICAvLyDmlLnlj5jljZXlhYPmoLzmoLflvI8KICAgIGNlbGxDbGFzc05hbWUoeyByb3csIHJvd0luZGV4LCBjb2x1bW4sIGNvbHVtbkluZGV4IH0pIHsKICAgICAgLy8gaWYgKGNvbHVtbi5wcm9wZXJ0eSA9PT0gIkZpbmlzaF9Db3VudCIpIHsKICAgICAgLy8gICByZXR1cm4gImNvbC1ibHVlIjsKICAgICAgLy8gfQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["detailPrint.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsFA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detailPrint.vue", "sourceRoot": "src/views/PRO/plan-production/task-list", "sourcesContent": ["<template>\n  <div v-loading=\"tbLoading\" element-loading-text=\"打印数据生成中\" class=\"abs100 cs-z-flex-pd16-wrap\">\n    <div class=\"top-btn\" @click=\"toBack\">\n      <el-button>返回</el-button>\n    </div>\n    <div class=\"top-btn-print\" @click=\"printEvent\">\n      <el-button type=\"primary\">打印</el-button>\n    </div>\n    <div class=\"cs-z-page-main-content\">\n      <!-- <div v-for=\"(item, index) in printData\" :key=\"index\" style=\"height: 100%; display: flex; flex-direction: column;\"> -->\n      <el-form ref=\"form\" inline label-width=\"140px\">\n        <el-row>\n          <el-col :span=\"20\">\n            <el-form-item label=\"项目名称/区域：\">\n              {{ info.Project_Name }}/{{ info.Area_Name }}\n            </el-form-item>\n            <el-form-item label=\"排产单号：\">\n              {{ info.Schduling_Code }}\n            </el-form-item>\n            <el-form-item label=\"加工班组：\">\n              {{ info.Working_Team_Name }}\n            </el-form-item>\n            <el-form-item label=\"任务下达时间：\">\n              {{ info.Order_Date }}\n            </el-form-item>\n            <el-form-item label=\"任务单号：\">\n              {{ info.Task_Code }}\n            </el-form-item>\n            <el-form-item label=\"工序计划开始时间\" prop=\"Process_Start_Date\">\n              {{ info.Process_Start_Date || '-' }}\n            </el-form-item>\n            <el-form-item label=\"工序计划完成时间\" prop=\"Process_Finish_Date\">\n              {{ info.Process_Finish_Date || '-' }}\n            </el-form-item>\n            <el-form-item label=\"备注\" prop=\"Remark\">\n              {{ Remark || '-' }}\n            </el-form-item>\n            <el-form-item label=\"建议设备\" prop=\"eqptInfoListStr\">\n              {{ eqptInfoListStr || '-' }}\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n            <qrcode-vue ref=\"qrcodeRef\" :size=\"79\" :value=\"`T=${info.Task_Code}&C=${Tenant_Code}`\" class-name=\"qrcode\" level=\"H\" />\n          </el-col>\n        </el-row>\n      </el-form>\n      <div class=\"tb-x\">\n        <vxe-table\n          ref=\"xTable\"\n          :empty-render=\"{name: 'NotData'}\"\n          show-header-overflow\n          :print-config=\"printConfig\"\n          :row-config=\"{ isCurrent: true, isHover: true }\"\n          class=\"cs-vxe-table\"\n          align=\"left\"\n          height=\"auto\"\n          show-overflow\n          :loading=\"tbLoading\"\n          stripe\n          size=\"medium\"\n          :data=\"tbData\"\n          resizable\n          :tooltip-config=\"{ enterable: true }\"\n          :cell-class-name=\"cellClassName\"\n          @cell-click=\"cellClickEvent\"\n        >\n          <template v-for=\"column in columns\">\n            <vxe-column\n              :key=\"column.Id\"\n              :fixed=\"column.Is_Frozen ? column.Frozen_Dirction : ''\"\n              show-overflow=\"tooltip\"\n              sortable\n              :align=\"column.Align\"\n              :field=\"column.Code\"\n              :title=\"column.Display_Name\"\n              :min-width=\"column.Width\"\n            />\n          </template>\n        </vxe-table>\n      </div>\n      <!-- </div> -->\n    </div>\n  </div>\n</template>\n\n<script>\nimport getTbInfo from '@/mixins/PRO/get-table-info'\nimport { GetTeamTaskDetails, GetSuggestDeviceAndRemark } from '@/api/PRO/production-task'\nimport { closeTagView } from '@/utils'\nimport QrcodeVue from 'qrcode.vue'\nimport { mapGetters } from 'vuex'\nimport { getBomCode, getBomName, checkIsUnitPart } from '@/views/PRO/bom-setting/utils'\n\nconst printStyle = `\n        .title {\n          text-align: center;\n        }\n        .is--print{\n          box-sizing: border-box;\n          width:95% !important;\n          margin:0 auto !important;\n        }\n        .my-list-row {\n          display: inline-block;\n          width: 100%;\n          margin-left:3%;\n        }\n        .my-list-row-first {\n          margin-bottom: 10px;\n        }\n        .my-list-row-second {\n          margin-bottom: 10px;\n        }\n        .my-list-row-third {\n          margin-bottom: 10px;\n        }\n        .my-list-col {\n          width:30%;\n          display: inline-block;\n          float: left;\n          margin-right: 1%;\n          word-wrap:break-word;\n          word-break:normal;\n        }\n        .left{\n          flex:1;\n        }\n        .my-top {\n          display:flex;\n          font-size: 12px;\n          margin-bottom: 5px;\n        }\n        .qrcode{\n          margin-right:10px\n        }\n        .cs-img{\n          position:relative;\n          right:30px\n        }\n        `\n\nexport default {\n  name: 'PROTaskListDetailPrint',\n  components: {\n    QrcodeVue\n  },\n  mixins: [getTbInfo],\n  data() {\n    return {\n      bomName: '',\n      printData: [],\n      tbConfig: {\n        Op_Width: 120\n      },\n      tbLoading: false,\n      tbData: [],\n      columns: [],\n      pageType: '',\n      command: '',\n      printColumns: [],\n      info: {\n        Task_Code: '',\n        Project_Name: '',\n        Area_Name: '',\n        InstallUnit_Name: '',\n        Schduling_Code: '',\n        Task_Finish_Date: '',\n        Finish_Date2: '',\n        Order_Date: '',\n        Working_Team_Name: '',\n        Working_Process_Name: '',\n        Process_Start_Date: '',\n        Process_Finish_Date: ''\n      },\n      printConfig: {\n        sheetName: '任务单详情',\n        style: printStyle,\n        beforePrintMethod: ({ content }) => {\n          return this.topHtml + content\n        }\n      },\n      Tenant_Code: localStorage.getItem('tenant'),\n      Remark: '',\n      eqptInfoList: [],\n      eqptInfoListStr: ''\n    }\n  },\n  computed: {\n    ...mapGetters('tenant', ['isVersionFour']),\n    isCom() {\n      return this.pageType === getBomCode('-1')\n    },\n    isUnitPart() {\n      return checkIsUnitPart(this.pageType)\n    },\n    isPart() {\n      return this.pageType === getBomCode('0')\n    }\n  },\n  async mounted() {\n    this.pageType = this.$route.query.type\n    this.command = this.$route.query.command\n    this.bomName = await getBomName(this.pageType)\n    this.info = JSON.parse(decodeURIComponent(this.$route.query.other))\n    await this.getSuggestDeviceAndRemark()\n    await this.getTableConfig(this.isCom ? 'PROComTaskListDetail' : this.isUnitPart ? 'PROUnitPartTaskListDetail' : 'PROPartTaskListDetail')\n    if (this.isCom) {\n      this.columns = this.columns.filter(item => item.Code !== 'Part_Code')\n      this.printColumns = this.columns.filter(item => item.Code !== 'Part_Code' && item.Code !== 'Comp_Description')\n      if (this.command === 'code') {\n        this.columns = this.columns.filter(item => item.Code !== 'Comp_Code' && item.Code !== 'Part_Code')\n        this.printColumns = this.columns.filter(item => item.Code !== 'Comp_Code' && item.Code !== 'Comp_Description' && item.Code !== 'Part_Code')\n      }\n    }\n\n    if (this.isUnitPart) {\n      this.printColumns = this.columns.filter(column => column.Code !== 'Project_Name' && column.Code !== 'Area_Name' && column.Code !== 'Finish_Count' && column.Code !== 'Finish_Weight' && column.Code !== 'Comp_Description')\n    }\n    if (this.isPart) {\n      this.printColumns = this.columns.filter(column => column.Code !== 'Project_Name' && column.Code !== 'Area_Name' && column.Code !== 'Finish_Count' && column.Code !== 'Finish_Weight' && column.Code !== 'Comp_Description')\n    }\n\n    const data = await this.fetchData(this.info.Task_Code, this.info.Working_Team_Id)\n    if (this.isCom && this.command === 'code') {\n      this.tbData = this.mergeSimilarItems(data)\n    } else {\n      this.tbData = data\n    }\n\n    this.getHtml()\n    this.printEvent()\n  },\n  methods: {\n    getSuggestDeviceAndRemark() {\n      GetSuggestDeviceAndRemark({\n        Bom_Level: this.pageType,\n        Task_Code: this.info.Task_Code\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.Remark = res.Data?.Remark\n          this.eqptInfoList = res.Data?.eqptInfoList || []\n          this.eqptInfoListStr = this.eqptInfoList.map(item => item.DisplayName).join(',')\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    async fetchData(Task_Code, Working_Team_Id, idx) {\n      this.tbLoading = true\n      const res = await GetTeamTaskDetails({\n        Page: -1,\n        PageSize: -1,\n        Bom_Level: this.pageType,\n        Process_Type: this.isCom ? 2 : this.isPart ? 1 : 3, // 1零件，2构件\n        Working_Team_Id: Working_Team_Id,\n        Task_Code: Task_Code,\n        Next_Team_Id: '',\n        Next_Process_Id: ''\n      })\n      if (res.IsSucceed) {\n        const data = res.Data.Data.filter(item => {\n          return item.Allocation_Count !== 0\n        })\n        this.tbLoading = false\n        return data\n      } else {\n        this.tbLoading = false\n      }\n    },\n\n    mergeSimilarItems(data) {\n      // 创建一个 Map 来存储合并后的数据，使用关键字段的组合作为键\n      const mergedMap = new Map()\n\n      // 遍历原始数据\n      for (const item of data) {\n        // 如果 Steel_Code 为空或 null，直接加入结果，不参与合并\n        if (item.Steel_Code == null || item.Steel_Code === '') {\n          // 确保数值字段有默认值\n          const newItem = {\n            ...item,\n            Allocation_Weight: item.Allocation_Weight || 0,\n            Finish_Weight: item.Finish_Weight || 0,\n            Allocation_Count: item.Allocation_Count || 0,\n            Finish_Count: item.Finish_Count || 0\n          }\n          // 使用一个唯一键（如索引）来避免覆盖\n          mergedMap.set(`null_${mergedMap.size}`, newItem)\n          continue\n        }\n\n        // 创建关键字段的组合作为键\n        const key = [\n          item.Steel_Code,\n          item.Spec,\n          item.Weight,\n          item.Main_Part,\n          item.AttachedBoardsNumber,\n          item.Length,\n          item.Texture,\n          item.Next_Process_Name,\n          item.Next_Working_Team_Names,\n          item.InstallUnit_Name\n        ].join('|')\n\n        // 检查是否已经有相同的记录\n        if (mergedMap.has(key)) {\n          // 获取已存在的记录\n          const existingItem = mergedMap.get(key)\n\n          // 累加数值字段\n          existingItem.Allocation_Weight += item.Allocation_Weight || 0\n          existingItem.Finish_Weight += item.Finish_Weight || 0\n          existingItem.Allocation_Count += item.Allocation_Count || 0\n          existingItem.Finish_Count += item.Finish_Count || 0\n        } else {\n          const newItem = {\n            ...item,\n            Allocation_Weight: item.Allocation_Weight || 0,\n            Finish_Weight: item.Finish_Weight || 0,\n            Allocation_Count: item.Allocation_Count || 0,\n            Finish_Count: item.Finish_Count || 0\n          }\n\n          // 如果是第一次出现，直接放入 Map\n          mergedMap.set(key, newItem)\n        }\n      }\n\n      // 将 Map 转换回数组\n      return Array.from(mergedMap.values())\n    },\n\n    handleReset() {\n      this.$refs['form'].resetFields()\n    },\n\n    getHtml() {\n      const qr = this.$refs['qrcodeRef']\n      return new Promise((resolve, reject) => {\n        this.$nextTick(_ => {\n          const canvas = qr.$refs['qrcode-vue']\n          const dataURL = canvas.toDataURL('image/png')\n          this.topHtml = `\n        <h1 class=\"title\">#${this.info.Working_Process_Name || ''}# 加工任务单</h1>\n        <div class=\"my-top\">\n          <div class=\"left\">\n            <div class=\"my-list-row my-list-row-first\">\n              <div class=\"my-list-col\">项目名称/区域：${this.info.Project_Name || ''}/${this.info.Area_Name || ''}</div>\n              <div class=\"my-list-col\">排产单号：${this.info.Schduling_Code || ''}</div>\n              <div class=\"my-list-col\">加工班组：${this.info.Working_Team_Name || ''}</div>\n            </div>\n            <div class=\"my-list-row my-list-row-second\">\n              <div class=\"my-list-col\">任务下单时间：${this.info.Order_Date || ''}</div>\n              <div class=\"my-list-col\">任务单号：${this.info?.Task_Code || ''}</div>\n              <div class=\"my-list-col\">工序计划开始时间：${this.info.Process_Start_Date || ''}</div>\n            </div>\n            <div class=\"my-list-row my-list-row-third\">\n              <div class=\"my-list-col\">工序计划完成时间：${this.info.Process_Finish_Date || ''}</div>\n              <div class=\"my-list-col\">备注：${this.Remark || ''}</div>\n            </div>\n            <div class=\"my-list-row\">\n              <div class=\"my-list-col\">建议设备：${this.eqptInfoListStr || ''}</div>\n            </div>\n          </div>\n          <div class=\"right\">\n           <img class=\"cs-img\" src=\"${dataURL}\" alt=\"\">\n          </div>\n        </div>\n        `\n          resolve()\n        })\n      })\n    },\n    printEvent() {\n      this.getHtml().then((_) => {\n        this.$refs.xTable.print({\n          sheetName: this.printConfig.sheetName,\n          style: printStyle,\n          mode: 'current',\n          columns: this.printColumns.map((v) => {\n            return {\n              field: v.Code\n            }\n          }),\n          beforePrintMethod: ({ content }) => {\n            const result = this.topHtml + content\n            return result\n          }\n        })\n      })\n    },\n    handleView(row) {\n    },\n    toBack() {\n      closeTagView(this.$store, this.$route)\n    },\n\n    // 单元格点击时间\n    cellClickEvent({ row, rowIndex, column, columnIndex }) {\n      // if (column.property === \"Finish_Count\" && row.Finish_Count > 0) {\n      //   this.$nextTick(() => {\n      //     this.$refs.TransferDetail.init(row, this.isCom);\n      //   });\n      // }\n    },\n\n    // 改变单元格样式\n    cellClassName({ row, rowIndex, column, columnIndex }) {\n      // if (column.property === \"Finish_Count\") {\n      //   return \"col-blue\";\n      // }\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.el-divider {\n  margin: 0 0 10px;\n}\n\n.tb-x {\n  flex: 1;\n  overflow:auto;\n\n  ::v-deep {\n    .cs-vxe-table .vxe-body--column.col-blue {\n      color: #298dff;\n      cursor: pointer;\n    }\n  }\n}\n\n.cs-z-flex-pd16-wrap {\n  padding-top: 50px;\n  .top-btn {\n    position: absolute;\n    top: 12px;\n    left: 20px;\n    z-index: 99;\n\n    .el-button {\n      background-color: #f7f8f9;\n    }\n  }\n  .top-btn-print {\n    position: absolute;\n    top: 12px;\n    right: 20px;\n    z-index: 99;\n  }\n\n  .cs-z-page-main-content {\n    overflow-y: auto;\n    ::v-deep {\n      .el-form-item__content {\n        min-width: 200px;\n      }\n    }\n  }\n}\n</style>\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\index.vue?vue&type=template&id=06dae726&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\index.vue", "mtime": 1756109946518}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}
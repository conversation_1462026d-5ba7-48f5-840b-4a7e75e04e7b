<template>
  <div class="form-wrapper">
    <div class="form-content">
      <el-form ref="form" label-width="120px" class="form-x">
        <el-form-item v-for="(item,index) in list" :key="index" :show-message="false" :label="item.Part_Type_Name" prop="mainPart">
          <el-select v-model="item.Working_Process_Id" clearable>
            <el-option v-for="op in selectList" :key="op.Id" :label="op.Name" :value="op.Id" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div class="form-footer">
      <el-button @click="$emit('close')">取 消</el-button>
      <el-button type="primary" :loading="btnLoading" @click="handleSubmit">确 定</el-button>
    </div>
  </div>
</template>

<script>

import { GetConsumingProcessAllList, SaveConsumingProcessAllList } from '@/api/PRO/partType'
import { GetProcessList } from '@/api/PRO/technology-lib'

export default {
  data() {
    return {
      list: [],
      btnLoading: false,
      selectList: []
    }
  },
  mounted() {
    this.getProcessList()
  },
  methods: {
    getTypeList() {
      GetConsumingProcessAllList({}).then(res => {
        if (res.IsSucceed) {
          this.list = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    getProcessList() {
      GetProcessList({ type: 1 }).then(res => {
        this.selectList = res.Data
      }).finally(() => {
        this.getTypeList()
      })
    },
    handleSubmit() {
      this.btnLoading = true
      SaveConsumingProcessAllList(this.list.filter(i => i.Working_Process_Id)).then(res => {
        if (res.IsSucceed) {
          this.$message.success('保存成功')
          this.$emit('close')
        } else {
          this.$message.error(res.Message)
        }
      }).finally(() => {
        this.btnLoading = false
      })
    },
    mainBlur(e) {

    }
  }
}
</script>

<style scoped lang="scss">
@import "~@/styles/mixin.scss";
.form-wrapper {
  height: 70vh;
  display: flex;
  flex-direction: column;

  .form-content {
    flex: 1;
    overflow: auto;
    padding-right: 16px;
    @include scrollBar;

    .form-x {
      padding-bottom: 20px;
    }
  }

  .form-footer {
    text-align: right;
    flex-shrink: 0;
    padding-top: 16px;
    background: #fff;
  }
}
</style>

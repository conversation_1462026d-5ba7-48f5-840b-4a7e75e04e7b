<template>
  <div class="form-wrapper">
    <div class="form-recognition-tabs">
      <el-tabs v-model="bomActiveName">
        <el-tab-pane v-for="(item, index) in bomList" :key="index" :label="item.Display_Name" :name="item.Code" />
      </el-tabs>
    </div>
    <div class="form-content">
      <div style="display: flex;">
        <div style="width: 120px;">1</div>
        <div style="flex: 1; display: flex; justify-content: center; align-items: center;">
          <div style="flex: 1; text-align: center;">构件</div>
          <div style="flex: 1; text-align: center;">半成品</div>
          <div style="flex: 1; text-align: center;">成品</div>
        </div>
      </div>
      <div>
        <div v-for="(item, index) in list" :key="index" style="display: flex;">
          <div style="width: 120px;">{{ item.Part_Type_Name }}</div>
          <div style="flex: 1; display: flex; justify-content: center; align-items: center;">
            <div style="flex: 1; text-align: center;">
              <el-select v-model="item.Working_Process_Id" clearable>
                <el-option v-for="op in selectList" :key="op.Id" :label="op.Name" :value="op.Id" />
              </el-select>
            </div>
            <div style="flex: 1; text-align: center;">
              <el-select v-model="item.Working_Process_Id" clearable>
                <el-option v-for="op in selectList" :key="op.Id" :label="op.Name" :value="op.Id" />
              </el-select>
            </div>
            <div style="flex: 1; text-align: center;">
              <el-select v-model="item.Working_Process_Id" clearable>
                <el-option v-for="op in selectList" :key="op.Id" :label="op.Name" :value="op.Id" />
              </el-select>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="form-footer">
      <el-button @click="$emit('close')">取 消</el-button>
      <el-button type="primary" :loading="btnLoading" @click="handleSubmit">确 定</el-button>
    </div>
  </div>
</template>

<script>
import { GetBOMInfo } from '@/views/PRO/bom-setting/utils'
import { GetConsumingProcessAllList, SaveConsumingProcessAllList } from '@/api/PRO/partType'
import { GetProcessList } from '@/api/PRO/technology-lib'

export default {
  data() {
    return {
      list: [],
      btnLoading: false,
      selectList: [],
      bomList: [],
      comName: '',
      partName: '',
      bomActiveName: ''
    }
  },
  async mounted() {
    await this.getBom()
    await this.getProcessList()
  },
  methods: {
    async getBom() {
      const { comName, partName, list } = await GetBOMInfo()
      this.comName = comName
      this.partName = partName
      this.bomList = list.filter(i => i.Code !== '-1')
      this.bomActiveName = this.bomList[0].Code
    },
    getTypeList() {
      GetConsumingProcessAllList({}).then(res => {
        if (res.IsSucceed) {
          this.list = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    getProcessList() {
      GetProcessList({ type: 1 }).then(res => {
        this.selectList = res.Data
      }).finally(() => {
        this.getTypeList()
      })
    },
    handleSubmit() {
      this.btnLoading = true
      SaveConsumingProcessAllList(this.list.filter(i => i.Working_Process_Id)).then(res => {
        if (res.IsSucceed) {
          this.$message.success('保存成功')
          this.$emit('close')
        } else {
          this.$message.error(res.Message)
        }
      }).finally(() => {
        this.btnLoading = false
      })
    },
    mainBlur(e) {

    }
  }
}
</script>

<style scoped lang="scss">
@import "~@/styles/mixin.scss";
.form-wrapper {
  height: 70vh;
  display: flex;
  flex-direction: column;

  .form-content {
    flex: 1;
    overflow: auto;
    padding-right: 16px;
    @include scrollBar;

    .form-x {
      padding-bottom: 20px;
    }
  }

  .form-footer {
    text-align: right;
    flex-shrink: 0;
    padding-top: 16px;
    background: #fff;
  }
}
</style>

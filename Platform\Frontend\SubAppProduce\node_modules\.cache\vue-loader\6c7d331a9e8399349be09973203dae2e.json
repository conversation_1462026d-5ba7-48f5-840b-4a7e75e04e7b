{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\change-management\\contact-list\\index.vue?vue&type=style&index=0&id=2495d68e&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\change-management\\contact-list\\index.vue", "mtime": 1757468112548}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYXBwLWNvbnRhaW5lcnsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgLmNzLW1haW57DQogICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgICBmbGV4OiAxOw0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICAuY3MtYm90dG9tLXdhcHBlcnsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgICAgZmxleDogMTsNCiAgICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgICAudGIteHsNCiAgICAgICAgZmxleDogMTsNCiAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgICAgIH0NCiAgICAgIC5kYXRhLWluZm97DQogICAgICAgIC8vZGlzcGxheTogZmxleDsNCiAgICAgICAgLy9qdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgICAgIC8vYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgICAgdGV4dC1hbGlnbjogcmlnaHQ7DQogICAgICAgIG1hcmdpbi10b3A6IDEwcHg7DQogICAgICB9DQogICAgICAucGFnaW5hdGlvbi1jb250YWluZXIgew0KICAgICAgICBwYWRkaW5nOiAwOw0KICAgICAgICBwYWRkaW5nLWJvdHRvbTogOHB4Ow0KICAgICAgICB0ZXh0LWFsaWduOiByaWdodDsNCiAgICAgICAgbWFyZ2luLXRvcDogMDsNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCi5jcy1ib3h7DQogIGJhY2tncm91bmQtY29sb3I6ICNGRkZGRkY7DQogIHBhZGRpbmc6IDE2cHg7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCn0NCi5tYjEwew0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KfQ0KDQouY3MtdHJlZS14IHsNCiAgOjp2LWRlZXAgew0KICAgIC5lbC1zZWxlY3Qgew0KICAgICAgd2lkdGg6IDEwMCU7DQogICAgfQ0KICB9DQp9DQouY3MtdGFnc3sNCiAgcGFkZGluZzogMnB4IDRweDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KfQ0KDQouY3MtcmVkew0KICBjb2xvcjogI0ZCNkI3RjsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTEsIDEwNywgMTI3LCAuMSk7DQp9DQouY3MtYmx1ZXsNCiAgY29sb3I6ICMzRUNDOTM7DQogIGJhY2tncm91bmQtY29sb3I6cmdiYSg2MiwgMjA0LCAxNDcsIC4xKTsNCn0NCi5jcy1ncmVlbnsNCiAgY29sb3I6ICM1MkM0MUE7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoODIsMTk2LDI2LCAuMSk7DQp9DQouY3MtemJ0bnsNCiAgcG9pbnRlci1ldmVudHM6IG5vbmU7DQogIHotaW5kZXg6IDE7DQp9DQouY3MtdG9vbEJhciB7DQogIDo6di1kZWVwIHsNCiAgICAudnhlLWJ1dHRvbi0taWNvbi52eGUtaWNvbi1jdXN0b20tY29sdW1uew0KICAgICAgZGlzcGxheTogbm9uZTsNCiAgICB9DQoNCiAgICAudnhlLWJ1dHRvbi50eXBlLS1idXR0b24uaXMtLWNpcmNsZSB7DQogICAgICB3aWR0aDogOTdweDsNCiAgICAgIHotaW5kZXg6IDA7DQogICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgfQ0KDQogICAgLmVsLWZvcm0taXRlbSB7DQogICAgICBtYXJnaW4tYm90dG9tOiAwOw0KICAgIH0NCiAgfQ0KfQ0KLmNzLWRpYWxvZ3sNCiAgOjp2LWRlZXB7DQogICAgLmVsLWRpYWxvZ19fYm9keXsNCiAgICAgIG1heC1oZWlnaHQ6IDcwdmg7DQogICAgICBvdmVyZmxvdzogYXV0bzsNCiAgICB9DQogIH0NCn0NCg0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsxBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/change-management/contact-list", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <div class=\"cs-box mb10\">\r\n      <el-row>\r\n        <el-form ref=\"form\" :model=\"form\" label-width=\"100px\">\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"项目名称\" prop=\"Sys_Project_Id\">\r\n              <el-select\r\n                v-model=\"form.Sys_Project_Id\"\r\n                clearable\r\n                style=\"width: 100%\"\r\n                placeholder=\"请选择\"\r\n                filterable\r\n                @change=\"projectChange(form.Sys_Project_Id)\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in projectList\"\r\n                  :key=\"item.Sys_Project_Id\"\r\n                  :label=\"item.Short_Name\"\r\n                  :value=\"item.Sys_Project_Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"区域\" prop=\"Area_Id\">\r\n              <el-tree-select\r\n                ref=\"treeSelectArea\"\r\n                v-model=\"form.Area_Id\"\r\n                class=\"cs-tree-x\"\r\n                :disabled=\"!form.Sys_Project_Id\"\r\n                :select-params=\"{\r\n                  clearable: true,\r\n                }\"\r\n                :tree-params=\"treeParamsArea\"\r\n                @select-clear=\"areaClear\"\r\n                @node-click=\"areaChange\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"单据状态\" prop=\"Status\">\r\n              <el-select v-model=\"form.Status\" clearable placeholder=\"请选择\" style=\"width: 100%\">\r\n                <el-option label=\"草稿\" :value=\"1\" />\r\n                <el-option label=\"审核中\" :value=\"2\" />\r\n                <el-option label=\"审核未通过\" :value=\"-2\" />\r\n                <el-option label=\"审核通过\" :value=\"3\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"变更类型\" prop=\"Moc_Type_Id\">\r\n              <el-select v-model=\"form.Moc_Type_Id\" placeholder=\"请选择\" clearable=\"\">\r\n                <el-option\r\n                  v-for=\"item in mocType\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"执行情况\" prop=\"Exec_Status\">\r\n              <el-select v-model=\"form.Exec_Status\" clearable placeholder=\"请选择\" style=\"width: 100%\">\r\n                <el-option label=\"未开始\" :value=\"1\" />\r\n                <el-option label=\"执行中\" :value=\"2\" />\r\n                <el-option label=\"已完成\" :value=\"3\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"变更日期\" prop=\"Change_Date\">\r\n              <el-date-picker\r\n                v-model=\"form.Change_Date\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                type=\"daterange\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始日期\"\r\n                end-placeholder=\"结束日期\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"紧急程度\" prop=\"Urgency\">\r\n              <el-select v-model=\"form.Urgency\" clearable placeholder=\"请选择\" style=\"width: 100%\">\r\n                <el-option label=\"普通\" :value=\"1\" />\r\n                <el-option label=\"紧急\" :value=\"2\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label-width=\"20px\">\r\n              <el-button type=\"primary\" @click=\"search(1)\">搜索</el-button>\r\n              <el-button @click=\"handleReset\">重置</el-button>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-form>\r\n      </el-row>\r\n    </div>\r\n    <div class=\"cs-box cs-main\">\r\n      <vxe-toolbar\r\n        ref=\"xToolbar1\"\r\n        class=\"cs-toolBar\"\r\n      >\r\n        <template #buttons>\r\n          <el-button type=\"primary\" @click=\"handleAdd\">新建联系单</el-button>\r\n        </template>\r\n        <template #tools>\r\n          <el-button type=\"primary\" @click=\"handleSetting\">变更类型配置</el-button>\r\n          <DynamicTableFields\r\n            title=\"表格配置\"\r\n            :table-config-code=\"gridCode\"\r\n            @updateColumn=\"changeColumn\"\r\n          />\r\n        </template>\r\n      </vxe-toolbar>\r\n\r\n      <div class=\"cs-bottom-wapper\">\r\n        <div class=\"fff tb-x\">\r\n          <vxe-table\r\n            :empty-render=\"{name: 'NotData'}\"\r\n            show-header-overflow\r\n            :loading=\"tbLoading\"\r\n            element-loading-spinner=\"el-icon-loading\"\r\n            element-loading-text=\"拼命加载中\"\r\n            empty-text=\"暂无数据\"\r\n            class=\"cs-vxe-table\"\r\n            height=\"100%\"\r\n            align=\"left\"\r\n            stripe\r\n            :data=\"tbData\"\r\n            resizable\r\n            :tooltip-config=\"{ enterable: true}\"\r\n            :checkbox-config=\"{checkField: 'checked'}\"\r\n            @checkbox-all=\"multiSelectedChange\"\r\n            @checkbox-change=\"multiSelectedChange\"\r\n          >\r\n            <!--            <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" />-->\r\n            <template v-for=\"item in columns\">\r\n              <vxe-column\r\n                :key=\"item.Code\"\r\n                :min-width=\"item.Width\"\r\n                show-overflow=\"tooltip\"\r\n                sortable\r\n                align=\"center\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                :fixed=\"item.Is_Frozen ? (item.Frozen_Dirction || 'left') : ''\"\r\n              >\r\n                <template v-if=\"['Change_Date','Demand_Date','Create_Date','Change_End'].includes(item.Code) \" #default=\"{ row }\">\r\n                  {{ row[item.Code] | timeFormat }}\r\n                </template>\r\n                <template v-else-if=\"item.Code === 'Exec_Status'\" #default=\"{ row }\">\r\n                  <span :class=\"['cs-tags',row[item.Code]===1?'cs-red':row[item.Code]===2?'cs-blue':'cs-green']\">{{ row[item.Code]===1?'未开始':row[item.Code]===2?'执行中':row[item.Code]===3?'已完成':'' }}</span>\r\n                </template>\r\n                <template v-else-if=\"item.Code === 'Urgency'\" #default=\"{ row }\">\r\n                  <el-tag v-if=\"row.Urgency == 1\" type=\"primary\">普通</el-tag>\r\n                  <el-tag v-else-if=\"row.Urgency == 2\" type=\"danger\">紧急</el-tag>\r\n                  <span v-else>-</span>\r\n                </template>\r\n                <!--                <template v-else-if=\"item.Code === 'Change_Type'\" #default=\"{ row }\">-->\r\n                <!--                  <span> {{ row[item.Code] ==='0'?'完整变更':row[item.Code] ==='1'?'部分变更':row[item.Code] ==='2'?'手动变更':'' }}</span>-->\r\n                <!--                </template>-->\r\n                <template v-else #default=\"{ row }\">\r\n                  <span> {{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n            </template>\r\n            <vxe-column fixed=\"right\" title=\"操作\" width=\"180\">\r\n              <template #default=\"{ row }\">\r\n                <template v-for=\"btn in getButtonsByStatus(row.Status,row)\">\r\n                  <el-button\r\n                    v-if=\"btn.checkKey(btn.key,row)\"\r\n                    :key=\"btn.text\"\r\n                    :class=\"{'txt-red':btn.isRed}\"\r\n                    type=\"text\"\r\n                    @click=\"btn.handler(row)\"\r\n                  >{{ btn.text }}</el-button>\r\n                </template>\r\n\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-table>\r\n        </div>\r\n        <div class=\"data-info\">\r\n          <!--          <el-tag-->\r\n          <!--            size=\"medium\"-->\r\n          <!--            class=\"info-x\"-->\r\n          <!--          >已选 {{ multipleSelection.length }} 条数据-->\r\n          <!--          </el-tag>-->\r\n          <Pagination\r\n            :total=\"total\"\r\n            :page-sizes=\"tablePageSize\"\r\n            :page.sync=\"queryInfo.Page\"\r\n            :limit.sync=\"queryInfo.PageSize\"\r\n            @pagination=\"pageChange\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      title=\"变更类型\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"50%\"\r\n      class=\"plm-custom-dialog cs-dialog\"\r\n      @close=\"dialogVisible=false\"\r\n    >\r\n      <div>\r\n        <vxe-toolbar>\r\n          <template #buttons>\r\n            <vxe-button status=\"primary\" content=\"添加\" @click=\"handleAddSetting()\" />\r\n          </template>\r\n        </vxe-toolbar>\r\n\r\n        <vxe-table\r\n          ref=\"xTable\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          border\r\n          class=\"cs-vxe-table\"\r\n          stripe\r\n          resizable\r\n          show-overflow\r\n          :loading=\"settingLoading\"\r\n          :data=\"dialogTable\"\r\n          :edit-config=\"{beforeEditMethod: activeRowMethod,trigger: 'click',showStatus: true, mode: 'row'}\"\r\n          @edit-closed=\"editClosedEvent\"\r\n          @edit-disabled=\"editDisabledEvent\"\r\n        >\r\n\r\n          <vxe-column\r\n            align=\"left\"\r\n            field=\"Display_Name\"\r\n            title=\"类型名称\"\r\n            min-width=\"180\"\r\n            :edit-render=\"{autofocus: '.vxe-input--inner'}\"\r\n          >\r\n            <template #edit=\"{ row }\">\r\n              <vxe-input v-model=\"row.Display_Name\" type=\"text\" />\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column\r\n            align=\"left\"\r\n            field=\"Is_Deepen_Change\"\r\n            title=\"是否变更清单\"\r\n            min-width=\"180\"\r\n            :edit-render=\"{}\"\r\n          >\r\n            <template #edit=\"{ row }\">\r\n              <el-radio v-model=\"row.Is_Deepen_Change\" :label=\"true\">是</el-radio>\r\n              <el-radio v-model=\"row.Is_Deepen_Change\" :label=\"false\">否</el-radio>\r\n            </template>\r\n            <template #default=\"{row}\">\r\n              <el-tag v-if=\" row.Is_Deepen_Change\" type=\"success\">是</el-tag>\r\n              <el-tag v-else type=\"danger\">否</el-tag>\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column\r\n            align=\"left\"\r\n            title=\"操作\"\r\n          >\r\n            <template #default=\"{ row }\">\r\n              <el-button type=\"text\" class=\"txt-red\" @click=\"removeRowEvent(row)\">删除</el-button>\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">关 闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <Monitor ref=\"monitor\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport Pagination from '@/components/Pagination/index.vue'\r\nimport {\r\n  DeleteMocType,\r\n  DeleteMocOrder,\r\n  GetMocOrderPageList, GetMocOrderTypeList,\r\n  SaveMocOrderType, ChangeMocOrderStatus, SubmitMocOrder\r\n} from '@/api/PRO/changeManagement'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nimport { GeAreaTrees, GetProjectPageList } from '@/api/PRO/project'\r\nimport Monitor from '@/components/Monitor/index.vue'\r\nimport { CancelFlow } from '@/api/PRO/component-stock-out'\r\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\r\nimport { debounce } from '@/utils'\r\n\r\nexport default {\r\n  name: 'PROEngineeringChangeOrder',\r\n  components: {\r\n    DynamicTableFields,\r\n    Monitor,\r\n    Pagination\r\n  },\r\n  mixins: [getTbInfo, addRouterPage],\r\n  data() {\r\n    return {\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/add',\r\n          hidden: true,\r\n          component: () => import('./add.vue'),\r\n          name: 'PROEngineeringChangeOrderAdd',\r\n          meta: { title: '新增' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/edit',\r\n          hidden: true,\r\n          component: () => import('./add.vue'),\r\n          name: 'PROEngineeringChangeOrderEdit',\r\n          meta: { title: '编辑' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/view',\r\n          hidden: true,\r\n          component: () => import('./add.vue'),\r\n          name: 'PROEngineeringChangeOrderView',\r\n          meta: { title: '查看' }\r\n        }\r\n      ],\r\n      form: {\r\n        Sys_Project_Id: '',\r\n        Status: '',\r\n        Exec_Status: '',\r\n        Change_Date: '',\r\n        Moc_Type_Id: '',\r\n        Area_Id: '',\r\n        Urgency: ''\r\n      },\r\n      activeName: 'second',\r\n      dialogVisible: false,\r\n      tbLoading: false,\r\n      settingLoading: false,\r\n      tbData: [],\r\n      projectList: [],\r\n      dialogTable: [],\r\n      mocType: [],\r\n      installUnitList: [],\r\n      treeParamsArea: {\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          disabled: 'disabled',\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      tbConfig: {},\r\n      multipleSelection: [],\r\n      search: () => ({}),\r\n      columns: [],\r\n      gridCode: 'PROEngChangeOrder',\r\n      tablePageSize: tablePageSize,\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: tablePageSize[0]\r\n      },\r\n      total: 0,\r\n      buttonConfigs: {\r\n        draft: [\r\n          { text: '提交审核', handler: this.handleSubmitAudit, checkKey: this.checkKey },\r\n          { text: '编辑', handler: this.handleEdit, checkKey: this.checkKey },\r\n          // { text: '监控', handler: this.handleMonitor, checkKey: this.checkKey },\r\n          { text: '删除', isRed: true, handler: this.handleDelete, checkKey: this.checkKey }\r\n        ],\r\n        reviewing: [\r\n          { text: '查看', handler: this.handleView, checkKey: this.checkKey },\r\n          { text: '监控', key: 'monitor', handler: this.handleMonitor, checkKey: this.checkKey },\r\n          { text: '回收', handler: this.handleRecycle, checkKey: this.checkKey }\r\n        ],\r\n        approved: [\r\n          { text: '查看', handler: this.handleView, checkKey: this.checkKey },\r\n          { text: '监控', key: 'monitor', handler: this.handleMonitor, checkKey: this.checkKey }\r\n        ],\r\n        finish: [\r\n          { text: '完成', handler: this.handleComplete, checkKey: this.checkKey }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    this.getTableConfig(this.gridCode)\r\n    this.fetchData(1)\r\n    this.getBasicData()\r\n    this.getSettingInfo()\r\n  },\r\n  methods: {\r\n    fetchData(page) {\r\n      page && (this.queryInfo.Page = page)\r\n      const { Change_Date, ...others } = this.form\r\n      const Change_Begin = Change_Date[0]\r\n      const Change_End = Change_Date[1]\r\n      this.tbLoading = true\r\n      GetMocOrderPageList({ ...others, Change_Begin, Change_End, ...this.queryInfo }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res?.Data?.Data || []\r\n          this.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.tbLoading = false\r\n      }).catch(() => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    handleSetting() {\r\n      this.dialogVisible = true\r\n      this.getSettingInfo()\r\n    },\r\n    async handleAddSetting(row) {\r\n      const $table = this.$refs.xTable\r\n      const record = {\r\n        Display_Name: '',\r\n        Is_Deepen_Change: false\r\n      }\r\n      const { row: newRow } = await $table.insertAt(record, row)\r\n      await $table.setEditCell(newRow, 'name')\r\n    },\r\n    removeRowEvent(row) {\r\n      if (!row.Id) {\r\n        this.$refs.xTable.remove(row)\r\n        return\r\n      }\r\n      this.$confirm(' 是否删除该类型?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        DeleteMocType({\r\n          ids: row.Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.getSettingInfo()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    getSettingInfo() {\r\n      GetMocOrderTypeList({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.dialogTable = res.Data\r\n          this.mocType = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async changeColumn() {\r\n      await this.getTableConfig(this.gridCode)\r\n    },\r\n    editClosedEvent({ row, column }) {\r\n      if (!row.Display_Name) {\r\n        this.$message({\r\n          message: '名称不能为空',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      const $table = this.$refs.xTable\r\n      const field = column.field\r\n      let flag = false\r\n      if ($table.isUpdateByRow(row, field) && row.Id || !row.Id) {\r\n        flag = true\r\n      }\r\n      if (flag) {\r\n        const obj = {\r\n          Display_Name: row.Display_Name,\r\n          Is_Deepen_Change: row.Is_Deepen_Change\r\n        }\r\n        row.Id && (obj.Id = row.Id)\r\n        SaveMocOrderType(obj).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            $table.reloadRow(row, null, field)\r\n            this.getSettingInfo()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n    },\r\n\r\n    getBasicData() {\r\n      GetProjectPageList({ PageSize: -1 }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.projectList = res.Data.Data\r\n        }\r\n      })\r\n    },\r\n    getAreaList(Sys_Project_Id) {\r\n      GeAreaTrees({\r\n        sysProjectId: Sys_Project_Id\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const tree = res.Data\r\n          this.setDisabledTree(tree)\r\n          this.treeParamsArea.data = res.Data\r\n          this.$nextTick(_ => {\r\n            this.$refs.treeSelectArea.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setDisabledTree(root) {\r\n      if (!root) return\r\n      root.forEach((element) => {\r\n        const { Children } = element\r\n        if (Children && Children.length) {\r\n          element.disabled = true\r\n        } else {\r\n          element.disabled = false\r\n          this.setDisabledTree(Children)\r\n        }\r\n      })\r\n    },\r\n    /*    getInstallUnitPageList() {\r\n      GetInstallUnitPageList({\r\n        Area_Id: this.form.Area_Id,\r\n        Page: 1,\r\n        PageSize: -1\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          if (res.IsSucceed) {\r\n            this.installUnitList = res.Data.Data\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },*/\r\n    projectChange(e) {\r\n      const Sys_Project_Id = e\r\n      this.form.Area_Id = ''\r\n      this.treeParamsArea.data = []\r\n      this.$nextTick(_ => {\r\n        this.$refs.treeSelectArea.treeDataUpdateFun([])\r\n      })\r\n      if (e) {\r\n        this.getAreaList(Sys_Project_Id)\r\n      }\r\n    },\r\n    areaChange() {\r\n      // this.getInstallUnitPageList()\r\n    },\r\n    areaClear() {\r\n      this.form.Area_Id = ''\r\n    },\r\n\r\n    handleAdd(tab, event) {\r\n      this.$router.push({ name: 'PROEngineeringChangeOrderAdd', query: { pg_redirect: this.$route.name }})\r\n    },\r\n    handleSearch(tab, event) {\r\n      console.log(tab, event)\r\n    },\r\n    multiSelectedChange(array) {\r\n      this.multipleSelection = array.records\r\n    },\r\n    handleReset() {\r\n      this.$refs['form'].resetFields()\r\n      this.search(1)\r\n    },\r\n    getButtonsByStatus(status, row) {\r\n      // +-1：草稿，2：审批中，3：已通过完成，-2：审核未通过\r\n      switch (status) {\r\n        case -1:\r\n        case 1:\r\n        case -2:\r\n          return this.buttonConfigs.draft\r\n        case 2:\r\n          return this.buttonConfigs.reviewing\r\n        case 3:\r\n          if (row.Exec_Status === 2) {\r\n            return [...this.buttonConfigs.approved, ...this.buttonConfigs.finish]\r\n          }\r\n          return this.buttonConfigs.approved\r\n        default:\r\n          return []\r\n      }\r\n    },\r\n    handleSubmitAudit(row) {\r\n      console.log('提交审核', row)\r\n      this.$confirm('是否提交审核?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbLoading = true\r\n        SubmitMocOrder({\r\n          Id: row.Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '提交成功!'\r\n            })\r\n            this.fetchData(1)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.tbLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    handleEdit(row) {\r\n      console.log('编辑', row)\r\n      this.$router.push({ name: 'PROEngineeringChangeOrderEdit', query: { pg_redirect: this.$route.name, type: 1, id: row.Id }})\r\n    },\r\n    handleDelete(row) {\r\n      this.$confirm('是否删除该数据?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbLoading = true\r\n        DeleteMocOrder({\r\n          Id: row.Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.fetchData(1)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.tbLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    handleView(row) {\r\n      console.log('查看', row)\r\n      this.$router.push({ name: 'PROEngineeringChangeOrderView', query: { pg_redirect: this.$route.name, id: row.Id, type: 2 }})\r\n    },\r\n    handleMonitor(row) {\r\n      console.log('监控', row)\r\n      this.$refs['monitor'].opendialog(row.Instance_Id, false)\r\n    },\r\n    handleRecycle(row) {\r\n      console.log('回收', row)\r\n      this.$confirm('是否回收?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbLoading = true\r\n        CancelFlow({\r\n          instanceId: row.Instance_Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '回收成功',\r\n              type: 'success'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.tbLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    editDisabledEvent({ row, column }) {\r\n      // const $table = this.$refs.xTable\r\n      // $table.modal.message({ content: '禁止编辑', status: 'error' })\r\n    },\r\n    activeRowMethod({ row, rowIndex }) {\r\n      return !row.Id\r\n    },\r\n    handleComplete(row) {\r\n      console.log('完成', row)\r\n      this.$confirm('是否完成?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbLoading = true\r\n        ChangeMocOrderStatus({\r\n          Id: row.Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '操作成功!'\r\n            })\r\n            this.fetchData(1)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.tbLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    checkKey(key, row) {\r\n      if (!key) return true\r\n      if (key === 'monitor') {\r\n        return !!row['Instance_Id']\r\n      }\r\n      return true\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.app-container{\r\n  display: flex;\r\n  flex-direction: column;\r\n  .cs-main{\r\n    overflow: hidden;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    .cs-bottom-wapper{\r\n      display: flex;\r\n      flex-direction: column;\r\n      flex: 1;\r\n      overflow: hidden;\r\n      .tb-x{\r\n        flex: 1;\r\n        overflow: hidden;\r\n      }\r\n      .data-info{\r\n        //display: flex;\r\n        //justify-content: space-between;\r\n        //align-items: center;\r\n        text-align: right;\r\n        margin-top: 10px;\r\n      }\r\n      .pagination-container {\r\n        padding: 0;\r\n        padding-bottom: 8px;\r\n        text-align: right;\r\n        margin-top: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n.cs-box{\r\n  background-color: #FFFFFF;\r\n  padding: 16px;\r\n  border-radius: 4px;\r\n}\r\n.mb10{\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.cs-tree-x {\r\n  ::v-deep {\r\n    .el-select {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n.cs-tags{\r\n  padding: 2px 4px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.cs-red{\r\n  color: #FB6B7F;\r\n  background-color: rgba(251, 107, 127, .1);\r\n}\r\n.cs-blue{\r\n  color: #3ECC93;\r\n  background-color:rgba(62, 204, 147, .1);\r\n}\r\n.cs-green{\r\n  color: #52C41A;\r\n  background-color: rgba(82,196,26, .1);\r\n}\r\n.cs-zbtn{\r\n  pointer-events: none;\r\n  z-index: 1;\r\n}\r\n.cs-toolBar {\r\n  ::v-deep {\r\n    .vxe-button--icon.vxe-icon-custom-column{\r\n      display: none;\r\n    }\r\n\r\n    .vxe-button.type--button.is--circle {\r\n      width: 97px;\r\n      z-index: 0;\r\n      border-radius: 4px;\r\n    }\r\n\r\n    .el-form-item {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n.cs-dialog{\r\n  ::v-deep{\r\n    .el-dialog__body{\r\n      max-height: 70vh;\r\n      overflow: auto;\r\n    }\r\n  }\r\n}\r\n\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\nesting-management\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\nesting-management\\index.vue", "mtime": 1757468127975}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Pagination", "getTbInfo", "tablePageSize", "addRouterPage", "DeleteNestingResult", "GetNestingResultPageList", "SurplusRaw", "NestReport", "NestResult", "NestThumbs", "PartsLayout", "mapActions", "DynamicTableFields", "ExportProcess", "combineURL", "OSSUpload", "getToken", "name", "components", "mixins", "data", "addPageArray", "path", "hidden", "component", "Promise", "resolve", "then", "_interopRequireWildcard", "require", "meta", "title", "query", "pg_redirect", "$route", "form", "Nesting_Result_Name", "Raw_Name", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "Texture", "Cut_Status", "Type", "dialogVisible", "width", "currentComponent", "tbLoading", "options", "columns", "tbData", "multipleSelection", "total", "queryInfo", "Page", "PageSize", "downloadLoading", "headers", "Authorization", "Last_Working_Object_Id", "localStorage", "getItem", "mounted", "getTbConfig", "fetchData", "methods", "_objectSpread", "uploadSuccess", "response", "IsSucceed", "$message", "message", "type", "Message", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "aaa", "wrap", "_callee$", "_context", "prev", "next", "getTableConfig", "filter", "item", "includes", "Code", "stop", "page", "_this2", "res", "Data", "TotalCount", "handleSearch", "handleImport", "$nextTick", "_", "handleImportResult", "handleImportThumbs", "handleDownload", "_this3", "ids", "map", "v", "Id", "window", "open", "$baseUrl", "finally", "handleDelete", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "toString", "catch", "handleSubmit", "tbSelectChange", "array", "console", "log", "records", "handleRaw", "row", "_this5", "$refs", "getData", "handleAmount", "_this6", "handleSchedule", "s", "changeNestIds", "$router", "push", "status", "pg_type", "handleClose", "handleReset", "resetFields", "toCreatePickList", "some", "PickNo", "error", "pickType", "i", "toCreateReturnList", "isNesting"], "sources": ["src/views/PRO/nesting-management/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"cs-z-flex-pd16-wrap abs100\">\r\n    <div class=\"cs-z-page-main-content\">\r\n      <el-form ref=\"form\" :model=\"form\" inline label-width=\"80px\" style=\"width: 100%\">\r\n        <el-form-item label=\"排版名称\" prop=\"Nesting_Result_Name\">\r\n          <el-input v-model=\"form.Nesting_Result_Name\" placeholder=\"请输入\" clearable type=\"text\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"原料名称\" prop=\"Raw_Name\">\r\n          <el-input v-model=\"form.Raw_Name\" placeholder=\"请输入\" type=\"text\" clearable />\r\n        </el-form-item>\r\n        <el-form-item label=\"厚度\" prop=\"Thickness\" label-width=\"50px\">\r\n          <el-input-number v-model=\"form.Thickness\" :min=\"0\" :max=\"1000000\" class=\"w100 cs-number-btn-hidden\" placeholder=\"请输入\" clearable />\r\n        </el-form-item>\r\n        <el-form-item label=\"材质\" prop=\"Texture\" label-width=\"50px\">\r\n          <el-input v-model=\"form.Texture\" placeholder=\"请输入\" type=\"text\" clearable />\r\n        </el-form-item>\r\n        <el-form-item label=\"切割状态\" prop=\"Cut_Status\">\r\n          <el-select v-model=\"form.Cut_Status\" placeholder=\"请选择\" clearable style=\"width: 120px\">\r\n            <el-option label=\"待切割\" value=\"待切割\" />\r\n            <el-option label=\"已切割\" value=\"已切割\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"类型\" prop=\"Type\" label-width=\"50px\">\r\n          <el-select v-model=\"form.Type\" placeholder=\"请选择\" clearable style=\"width: 120px\">\r\n            <el-option label=\"手动导入\" :value=\"1\" />\r\n            <el-option label=\"系统推送\" :value=\"2\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"fetchData(1)\">查询</el-button>\r\n          <el-button @click=\"handleReset\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <vxe-toolbar ref=\"xToolbar\">\r\n        <template #buttons>\r\n          <el-button type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"toCreatePickList\">生成内调单</el-button>\r\n          <el-button type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"toCreateReturnList\">生成余料退库单</el-button>\r\n          <el-button type=\"success\" @click=\"handleImport\">导入套料报告</el-button>\r\n          <el-button type=\"success\" @click=\"handleImportResult\">导入套料结果</el-button>\r\n          <el-button type=\"success\" @click=\"handleImportThumbs\">导入缩略图</el-button>\r\n          <el-button type=\"danger\" :disabled=\"!multipleSelection.length\" @click=\"handleDelete\">删除</el-button>\r\n          <el-button type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"handleSchedule\">下发排产任务</el-button>\r\n          <el-button type=\"default\" :disabled=\"!multipleSelection.length\" :loading=\"downloadLoading\" @click=\"handleDownload\">导出加工信息</el-button>\r\n          <el-upload\r\n            :action=\"$baseUrl + 'Pro/Nesting/ImportProcess'\"\r\n            :show-file-list=\"false\"\r\n            :headers=\"headers\"\r\n            style=\"margin: 0 10px\"\r\n            accept=\".xlsx,.xls\"\r\n            :on-success=\"()=>fetchData(1)\"\r\n          >\r\n            <el-button type=\"default\">导入加工信息</el-button>\r\n          </el-upload>\r\n          <el-upload\r\n            :action=\"$baseUrl + 'Pro/Nesting/ImportProfiles'\"\r\n            :show-file-list=\"false\"\r\n            :on-success=\"uploadSuccess\"\r\n            :headers=\"headers\"\r\n            accept=\".xlsx,.xls\"\r\n          >\r\n            <el-button type=\"default\">导入型材</el-button>\r\n          </el-upload>\r\n          <DynamicTableFields\r\n            style=\"margin-left: auto\"\r\n            title=\"表格配置\"\r\n            table-config-code=\"PRONestingManagementIndex\"\r\n            @updateColumn=\"getTbConfig\"\r\n          />\r\n        </template>\r\n      </vxe-toolbar>\r\n\r\n      <div v-loading=\"tbLoading\" class=\"tb-x\">\r\n        <vxe-table\r\n          v-if=\"!tbLoading\"\r\n          ref=\"xTable\"\r\n          class=\"cs-vxe-table\"\r\n          :checkbox-config=\"{checkField: 'checked'}\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          :row-config=\"{ isCurrent: true, isHover: true}\"\r\n          align=\"center\"\r\n          height=\"auto\"\r\n          show-overflow\r\n          :auto-resize=\"true\"\r\n          stripe\r\n          size=\"medium\"\r\n          :data=\"tbData\"\r\n          resizable\r\n          :tooltip-config=\"{ enterable: true }\"\r\n          @checkbox-all=\"tbSelectChange\"\r\n          @checkbox-change=\"tbSelectChange\"\r\n        >\r\n          <vxe-column fixed=\"left\" type=\"checkbox\" />\r\n\r\n          <template v-for=\"item in columns\">\r\n            <vxe-column\r\n              :key=\"item.Code\"\r\n              :fixed=\"item.Is_Frozen ? (item.Frozen_Dirction || 'left') : ''\"\r\n              show-overflow=\"tooltip\"\r\n              :align=\"item.Align\"\r\n              :field=\"item.Code\"\r\n              :visible=\"item.Is_Display\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n              :edit-render=\"item.Is_Edit ? {} : null\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                <span v-if=\"item.Code === 'Type'\">\r\n                  <el-tag v-if=\"row[item.Code] === 1\" effect=\"plain\" type=\"success\">手动导入</el-tag>\r\n                  <el-tag v-else effect=\"plain\" type=\"warning\">自动推送</el-tag>\r\n                </span>\r\n                <span v-else-if=\"item.Code === 'Surplus_Raw'\">\r\n                  <el-button v-if=\"row.Surplus_Count>0\" type=\"text\" @click=\"handleRaw(row)\">查看</el-button>\r\n                </span>\r\n                <span v-else-if=\"item.Code === 'Part_Amount'\">\r\n                  <el-link type=\"primary\" :underline=\"false\" @click=\"handleAmount(row)\">{{ row[item.Code] }}</el-link>\r\n                </span>\r\n                <span v-else-if=\"item.Code === 'Utilization'\">\r\n                  {{ row[item.Code] }}<span v-if=\"row[item.Code]\">%</span>\r\n                </span>\r\n                <span v-else>{{ row[item.Code] }}</span>\r\n              </template>\r\n            </vxe-column>\r\n          </template>\r\n        </vxe-table>\r\n      </div>\r\n      <footer class=\"data-info\">\r\n        <el-tag\r\n          size=\"medium\"\r\n          class=\"info-x\"\r\n        >已选 {{ multipleSelection.length }} 条数据\r\n        </el-tag>\r\n        <Pagination\r\n          :total=\"total\"\r\n          max-height=\"100%\"\r\n          :page-sizes=\"tablePageSize\"\r\n          :page.sync=\"queryInfo.Page\"\r\n          :limit.sync=\"queryInfo.PageSize\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          @pagination=\"pageChange\"\r\n        />\r\n      </footer>\r\n\r\n      <el-dialog\r\n        v-if=\"dialogVisible\"\r\n        v-dialogDrag\r\n        class=\"plm-custom-dialog\"\r\n        :title=\"title\"\r\n        :visible.sync=\"dialogVisible\"\r\n        :width=\"width\"\r\n        @close=\"handleClose\"\r\n      >\r\n        <component\r\n          :is=\"currentComponent\"\r\n          ref=\"content\"\r\n          @close=\"handleClose\"\r\n          @refresh=\"fetchData(1)\"\r\n        />\r\n      </el-dialog>\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Pagination from '@/components/Pagination/index.vue'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nimport { DeleteNestingResult, GetNestingResultPageList } from '@/api/PRO/production-task'\r\nimport SurplusRaw from './components/SurplusRaw.vue'\r\nimport NestReport from './components/NestReport.vue'\r\nimport NestResult from './components/NestResult.vue'\r\nimport NestThumbs from './components/NestThumbs.vue'\r\nimport PartsLayout from './components/PartsLayout.vue'\r\nimport { mapActions } from 'vuex'\r\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\r\nimport { ExportProcess } from '@/api/PRO/materialManagement'\r\nimport { combineURL } from '@/utils'\r\nimport OSSUpload from '@/views/plm/components/ossupload.vue'\r\nimport { getToken } from '@/utils/auth'\r\n\r\nexport default {\r\n  name: 'PRONestingManagement',\r\n  components: { OSSUpload, DynamicTableFields, Pagination, SurplusRaw, NestReport, NestResult, PartsLayout, NestThumbs },\r\n  mixins: [getTbInfo, addRouterPage],\r\n  data() {\r\n    return {\r\n      addPageArray: [\r\n        {\r\n          path: '/material/pick/add',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/material_v4/pickApply/add.vue'),\r\n          name: 'AddMaterialPickList',\r\n          meta: { title: '新增领料单' },\r\n          query: { pg_redirect: this.$route.name }\r\n        },\r\n        {\r\n          path: this.$route.path + '/requisition',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/nesting-management/requisition.vue'),\r\n          name: 'ModelCompare',\r\n          meta: { title: '领料单' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/schedule',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/nesting-management/schedule.vue'),\r\n          name: 'PRONestingSchedule',\r\n          meta: { title: '下发排产任务' }\r\n        }, {\r\n          path: this.$route.path + '/draft',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/plan-production/schedule-production-new-part/draft'),\r\n          name: 'PRO2PartScheduleDraftNestNew',\r\n          meta: { title: '草稿' }\r\n        }, {\r\n          path: this.$route.path + '/add-return',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/material-receipt-management/raw-stock-return/add.vue'),\r\n          name: 'PRORawMaterialStockReturnAddReturn',\r\n          meta: { title: '新建退库单' }\r\n        }\r\n      ],\r\n      form: {\r\n        Nesting_Result_Name: '',\r\n        Raw_Name: '',\r\n        Thickness: undefined,\r\n        Texture: '',\r\n        Cut_Status: '',\r\n        Type: undefined\r\n      },\r\n      dialogVisible: false,\r\n      width: '70%',\r\n      title: '',\r\n      currentComponent: '',\r\n      tbLoading: false,\r\n      options: [],\r\n      columns: [],\r\n      tbData: [],\r\n      multipleSelection: [],\r\n      tablePageSize: tablePageSize,\r\n      total: 0,\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: 20\r\n      },\r\n      downloadLoading: false,\r\n      headers: {\r\n        Authorization: getToken(),\r\n        Last_Working_Object_Id: localStorage.getItem('Last_Working_Object_Id')\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getTbConfig()\r\n    this.fetchData(1)\r\n  },\r\n  methods: {\r\n    uploadSuccess(response) {\r\n      if (response.IsSucceed) {\r\n        this.$message({\r\n          message: '上传成功',\r\n          type: 'success'\r\n        })\r\n        this.fetchData(1)\r\n      } else {\r\n        this.$message({\r\n          message: response.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    async getTbConfig() {\r\n      this.tbLoading = true\r\n      await this.getTableConfig('PRONestingManagementIndex')\r\n      this.tbLoading = false\r\n      const aaa = ['Picking_Bills', 'Out_Bills', 'Machining_Files', 'Thumbnail']\r\n      this.columns = this.columns.filter(item => !aaa.includes(item.Code))\r\n    },\r\n    ...mapActions('schedule', ['changeNestIds']),\r\n    fetchData(page) {\r\n      page && (this.queryInfo.Page = page)\r\n      this.tbLoading = true\r\n      GetNestingResultPageList({\r\n        ...this.queryInfo,\r\n        ...this.form\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.Data\r\n          this.total = res.Data.TotalCount\r\n          this.multipleSelection = []\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    handleSearch() {\r\n\r\n    },\r\n    handleImport() {\r\n      this.currentComponent = 'NestReport'\r\n      this.width = '30%'\r\n      this.title = '导入套料报告'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n\r\n      })\r\n    },\r\n    handleImportResult() {\r\n      this.currentComponent = 'NestResult'\r\n      this.width = '30%'\r\n      this.title = '导入套料结果'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n\r\n      })\r\n    },\r\n    handleImportThumbs() {\r\n      this.currentComponent = 'NestThumbs'\r\n      this.width = '30%'\r\n      this.title = '导入缩略图'\r\n      this.dialogVisible = true\r\n    },\r\n    handleDownload() {\r\n      this.downloadLoading = true\r\n      ExportProcess({ ids: this.multipleSelection.map(v => v.Id) }).then(res => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data))\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.downloadLoading = false\r\n      })\r\n    },\r\n    handleDelete() {\r\n      this.$confirm(' 是否删除该数据?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        DeleteNestingResult({\r\n          ids: this.multipleSelection.map(v => v.Id).toString()\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.fetchData(1)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    handleSubmit() {\r\n\r\n    },\r\n    tbSelectChange(array) {\r\n      console.log('111111')\r\n      this.multipleSelection = array.records\r\n    },\r\n    handleRaw(row) {\r\n      this.width = '70%'\r\n      this.currentComponent = 'SurplusRaw'\r\n      this.title = '余料信息'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].getData(row.Id)\r\n      })\r\n    },\r\n    handleAmount(row) {\r\n      this.width = '70%'\r\n      this.currentComponent = 'PartsLayout'\r\n      this.title = '排版零件'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].getData(row.Id)\r\n      })\r\n    },\r\n    handleSchedule() {\r\n      const ids = this.multipleSelection.filter(s => s.Type === 1).map(v => v.Id)\r\n      if (!ids) {\r\n        return\r\n      }\r\n      this.changeNestIds(ids)\r\n      this.$router.push({ name: 'PRO2PartScheduleDraftNestNew', query: { status: 'edit', pg_type: 'part', pg_redirect: this.$route.name, type: '1' }})\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    handleReset() {\r\n      this.$refs['form'].resetFields()\r\n      this.fetchData(1)\r\n    },\r\n    toCreatePickList() {\r\n      console.log(this.multipleSelection)\r\n      if (this.multipleSelection.some(item => item.PickNo)) {\r\n        this.$message.error('已有内调单号的不能重复生成')\r\n        return\r\n      }\r\n      this.$router.push({\r\n        name: 'AddMaterialPickList',\r\n        query: { pg_redirect: this.$route.name, type: 0, pickType: 1, ids: this.multipleSelection.map(i => i.Id) } // type原料为0,pickType：0手动生成 1套料生成\r\n      })\r\n    },\r\n    toCreateReturnList() {\r\n      this.$router.push({\r\n        name: 'PRORawMaterialStockReturnAddReturn',\r\n        query: { pg_redirect: this.$route.name, isNesting: 1, ids: this.multipleSelection.map(i => i.Id) }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.el-divider{\r\n  margin:0 0 8px  0;\r\n}\r\n.cs-z-page-main-content{\r\n  display: flex;\r\n  flex-direction: column;\r\n  .tb-x{\r\n    display: flex;\r\n    flex-direction: column;\r\n    flex: 1;\r\n    overflow: hidden;\r\n  }\r\n  .data-info{\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    .info-x{\r\n      margin-right: 16px;\r\n    }\r\n  }\r\n}\r\n.pagination-container {\r\n  text-align: right;\r\n  padding: 16px 16px 0 16px;\r\n  margin: 0;\r\n}\r\n.el-form-item{\r\n  margin-bottom: 10px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqKA,OAAAA,UAAA;AACA,OAAAC,SAAA;AACA,SAAAC,aAAA;AACA,OAAAC,aAAA;AACA,SAAAC,mBAAA,EAAAC,wBAAA;AACA,OAAAC,UAAA;AACA,OAAAC,UAAA;AACA,OAAAC,UAAA;AACA,OAAAC,UAAA;AACA,OAAAC,WAAA;AACA,SAAAC,UAAA;AACA,OAAAC,kBAAA;AACA,SAAAC,aAAA;AACA,SAAAC,UAAA;AACA,OAAAC,SAAA;AACA,SAAAC,QAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAH,SAAA,EAAAA,SAAA;IAAAH,kBAAA,EAAAA,kBAAA;IAAAZ,UAAA,EAAAA,UAAA;IAAAM,UAAA,EAAAA,UAAA;IAAAC,UAAA,EAAAA,UAAA;IAAAC,UAAA,EAAAA,UAAA;IAAAE,WAAA,EAAAA,WAAA;IAAAD,UAAA,EAAAA;EAAA;EACAU,MAAA,GAAAlB,SAAA,EAAAE,aAAA;EACAiB,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA,GACA;QACAC,IAAA;QACAC,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAZ,IAAA;QACAa,IAAA;UAAAC,KAAA;QAAA;QACAC,KAAA;UAAAC,WAAA,OAAAC,MAAA,CAAAjB;QAAA;MACA,GACA;QACAK,IAAA,OAAAY,MAAA,CAAAZ,IAAA;QACAC,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAZ,IAAA;QACAa,IAAA;UAAAC,KAAA;QAAA;MACA,GACA;QACAT,IAAA,OAAAY,MAAA,CAAAZ,IAAA;QACAC,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAZ,IAAA;QACAa,IAAA;UAAAC,KAAA;QAAA;MACA;QACAT,IAAA,OAAAY,MAAA,CAAAZ,IAAA;QACAC,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAZ,IAAA;QACAa,IAAA;UAAAC,KAAA;QAAA;MACA;QACAT,IAAA,OAAAY,MAAA,CAAAZ,IAAA;QACAC,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAZ,IAAA;QACAa,IAAA;UAAAC,KAAA;QAAA;MACA,EACA;MACAI,IAAA;QACAC,mBAAA;QACAC,QAAA;QACAC,SAAA,EAAAC,SAAA;QACAC,OAAA;QACAC,UAAA;QACAC,IAAA,EAAAH;MACA;MACAI,aAAA;MACAC,KAAA;MACAb,KAAA;MACAc,gBAAA;MACAC,SAAA;MACAC,OAAA;MACAC,OAAA;MACAC,MAAA;MACAC,iBAAA;MACAhD,aAAA,EAAAA,aAAA;MACAiD,KAAA;MACAC,SAAA;QACAC,IAAA;QACAC,QAAA;MACA;MACAC,eAAA;MACAC,OAAA;QACAC,aAAA,EAAAzC,QAAA;QACA0C,sBAAA,EAAAC,YAAA,CAAAC,OAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA,EAAAC,aAAA,CAAAA,aAAA;IACAC,aAAA,WAAAA,cAAAC,QAAA;MACA,IAAAA,QAAA,CAAAC,SAAA;QACA,KAAAC,QAAA;UACAC,OAAA;UACAC,IAAA;QACA;QACA,KAAAR,SAAA;MACA;QACA,KAAAM,QAAA;UACAC,OAAA,EAAAH,QAAA,CAAAK,OAAA;UACAD,IAAA;QACA;MACA;IACA;IACAT,WAAA,WAAAA,YAAA;MAAA,IAAAW,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAV,KAAA,CAAA3B,SAAA;cAAAmC,QAAA,CAAAE,IAAA;cAAA,OACAV,KAAA,CAAAW,cAAA;YAAA;cACAX,KAAA,CAAA3B,SAAA;cACAgC,GAAA;cACAL,KAAA,CAAAzB,OAAA,GAAAyB,KAAA,CAAAzB,OAAA,CAAAqC,MAAA,WAAAC,IAAA;gBAAA,QAAAR,GAAA,CAAAS,QAAA,CAAAD,IAAA,CAAAE,IAAA;cAAA;YAAA;YAAA;cAAA,OAAAP,QAAA,CAAAQ,IAAA;UAAA;QAAA,GAAAZ,OAAA;MAAA;IACA;EAAA,GACAlE,UAAA;IACAoD,SAAA,WAAAA,UAAA2B,IAAA;MAAA,IAAAC,MAAA;MACAD,IAAA,UAAAtC,SAAA,CAAAC,IAAA,GAAAqC,IAAA;MACA,KAAA5C,SAAA;MACAzC,wBAAA,CAAA4D,aAAA,CAAAA,aAAA,KACA,KAAAb,SAAA,GACA,KAAAjB,IAAA,CACA,EAAAR,IAAA,WAAAiE,GAAA;QACA,IAAAA,GAAA,CAAAxB,SAAA;UACAuB,MAAA,CAAA1C,MAAA,GAAA2C,GAAA,CAAAC,IAAA,CAAAA,IAAA;UACAF,MAAA,CAAAxC,KAAA,GAAAyC,GAAA,CAAAC,IAAA,CAAAC,UAAA;UACAH,MAAA,CAAAzC,iBAAA;QACA;UACAyC,MAAA,CAAAtB,QAAA;YACAC,OAAA,EAAAsB,GAAA,CAAApB,OAAA;YACAD,IAAA;UACA;QACA;QACAoB,MAAA,CAAA7C,SAAA;MACA;IACA;IACAiD,YAAA,WAAAA,aAAA,GAEA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAnD,gBAAA;MACA,KAAAD,KAAA;MACA,KAAAb,KAAA;MACA,KAAAY,aAAA;MACA,KAAAsD,SAAA,WAAAC,CAAA,GAEA;IACA;IACAC,kBAAA,WAAAA,mBAAA;MACA,KAAAtD,gBAAA;MACA,KAAAD,KAAA;MACA,KAAAb,KAAA;MACA,KAAAY,aAAA;MACA,KAAAsD,SAAA,WAAAC,CAAA,GAEA;IACA;IACAE,kBAAA,WAAAA,mBAAA;MACA,KAAAvD,gBAAA;MACA,KAAAD,KAAA;MACA,KAAAb,KAAA;MACA,KAAAY,aAAA;IACA;IACA0D,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAA/C,eAAA;MACA1C,aAAA;QAAA0F,GAAA,OAAArD,iBAAA,CAAAsD,GAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,EAAA;QAAA;MAAA,GAAA/E,IAAA,WAAAiE,GAAA;QACA,IAAAA,GAAA,CAAAxB,SAAA;UACAuC,MAAA,CAAAC,IAAA,CAAA9F,UAAA,CAAAwF,MAAA,CAAAO,QAAA,EAAAjB,GAAA,CAAAC,IAAA;QACA;UACAS,MAAA,CAAAjC,QAAA;YACAC,OAAA,EAAAsB,GAAA,CAAApB,OAAA;YACAD,IAAA;UACA;QACA;MACA,GAAAuC,OAAA;QACAR,MAAA,CAAA/C,eAAA;MACA;IACA;IACAwD,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACA5C,IAAA;MACA,GAAA5C,IAAA;QACAvB,mBAAA;UACAmG,GAAA,EAAAS,MAAA,CAAA9D,iBAAA,CAAAsD,GAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAC,EAAA;UAAA,GAAAU,QAAA;QACA,GAAAzF,IAAA,WAAAiE,GAAA;UACA,IAAAA,GAAA,CAAAxB,SAAA;YACA4C,MAAA,CAAA3C,QAAA;cACAE,IAAA;cACAD,OAAA;YACA;YACA0C,MAAA,CAAAjD,SAAA;UACA;YACAiD,MAAA,CAAA3C,QAAA;cACAC,OAAA,EAAAsB,GAAA,CAAApB,OAAA;cACAD,IAAA;YACA;UACA;QACA;MACA,GAAA8C,KAAA;QACAL,MAAA,CAAA3C,QAAA;UACAE,IAAA;UACAD,OAAA;QACA;MACA;IACA;IACAgD,YAAA,WAAAA,aAAA,GAEA;IACAC,cAAA,WAAAA,eAAAC,KAAA;MACAC,OAAA,CAAAC,GAAA;MACA,KAAAxE,iBAAA,GAAAsE,KAAA,CAAAG,OAAA;IACA;IACAC,SAAA,WAAAA,UAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAlF,KAAA;MACA,KAAAC,gBAAA;MACA,KAAAd,KAAA;MACA,KAAAY,aAAA;MACA,KAAAsD,SAAA,WAAAC,CAAA;QACA4B,MAAA,CAAAC,KAAA,YAAAC,OAAA,CAAAH,GAAA,CAAAnB,EAAA;MACA;IACA;IACAuB,YAAA,WAAAA,aAAAJ,GAAA;MAAA,IAAAK,MAAA;MACA,KAAAtF,KAAA;MACA,KAAAC,gBAAA;MACA,KAAAd,KAAA;MACA,KAAAY,aAAA;MACA,KAAAsD,SAAA,WAAAC,CAAA;QACAgC,MAAA,CAAAH,KAAA,YAAAC,OAAA,CAAAH,GAAA,CAAAnB,EAAA;MACA;IACA;IACAyB,cAAA,WAAAA,eAAA;MACA,IAAA5B,GAAA,QAAArD,iBAAA,CAAAmC,MAAA,WAAA+C,CAAA;QAAA,OAAAA,CAAA,CAAA1F,IAAA;MAAA,GAAA8D,GAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,EAAA;MAAA;MACA,KAAAH,GAAA;QACA;MACA;MACA,KAAA8B,aAAA,CAAA9B,GAAA;MACA,KAAA+B,OAAA,CAAAC,IAAA;QAAAtH,IAAA;QAAAe,KAAA;UAAAwG,MAAA;UAAAC,OAAA;UAAAxG,WAAA,OAAAC,MAAA,CAAAjB,IAAA;UAAAsD,IAAA;QAAA;MAAA;IACA;IACAmE,WAAA,WAAAA,YAAA;MACA,KAAA/F,aAAA;IACA;IACAgG,WAAA,WAAAA,YAAA;MACA,KAAAZ,KAAA,SAAAa,WAAA;MACA,KAAA7E,SAAA;IACA;IACA8E,gBAAA,WAAAA,iBAAA;MACApB,OAAA,CAAAC,GAAA,MAAAxE,iBAAA;MACA,SAAAA,iBAAA,CAAA4F,IAAA,WAAAxD,IAAA;QAAA,OAAAA,IAAA,CAAAyD,MAAA;MAAA;QACA,KAAA1E,QAAA,CAAA2E,KAAA;QACA;MACA;MACA,KAAAV,OAAA,CAAAC,IAAA;QACAtH,IAAA;QACAe,KAAA;UAAAC,WAAA,OAAAC,MAAA,CAAAjB,IAAA;UAAAsD,IAAA;UAAA0E,QAAA;UAAA1C,GAAA,OAAArD,iBAAA,CAAAsD,GAAA,WAAA0C,CAAA;YAAA,OAAAA,CAAA,CAAAxC,EAAA;UAAA;QAAA;MACA;IACA;IACAyC,kBAAA,WAAAA,mBAAA;MACA,KAAAb,OAAA,CAAAC,IAAA;QACAtH,IAAA;QACAe,KAAA;UAAAC,WAAA,OAAAC,MAAA,CAAAjB,IAAA;UAAAmI,SAAA;UAAA7C,GAAA,OAAArD,iBAAA,CAAAsD,GAAA,WAAA0C,CAAA;YAAA,OAAAA,CAAA,CAAAxC,EAAA;UAAA;QAAA;MACA;IACA;EAAA;AAEA", "ignoreList": []}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\part-list\\v4\\index.vue?vue&type=template&id=00eaac54&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\part-list\\v4\\index.vue", "mtime": 1757583738731}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}
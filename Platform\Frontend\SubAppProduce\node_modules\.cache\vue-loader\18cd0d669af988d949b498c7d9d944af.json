{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\part-list\\v4\\index.vue?vue&type=template&id=00eaac54&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\part-list\\v4\\index.vue", "mtime": 1757468112995}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}
<template>
  <div class="app-container abs100 flex-row">
    <div v-if="isAdd" class="cs-left">
      <ExpandableSection v-model="showExpand">
        <div class="cs-tree-wrapper">
          <div class="tree-search">
            <el-select
              v-model="statusType"
              clearable
              class="search-select"
              placeholder="请选择"
              @change="fetchTreeStatus"
            >
              <el-option label="可排产" value="可排产" />
              <el-option label="排产完成" value="排产完成" />
              <el-option label="未导入" value="未导入" />
            </el-select>
            <el-input
              v-model.trim="projectName"
              placeholder="搜索..."
              size="small"
              clearable
              suffix-icon="el-icon-search"
              @blur="fetchTreeDataLocal"
              @clear="fetchTreeDataLocal"
              @keydown.enter.native="fetchTreeDataLocal"
            />
          </div>
          <el-divider />
          <tree-detail
            ref="tree"
            icon="icon-folder"
            is-custom-filter
            :custom-filter-fun="customFilterFun"
            :loading="treeLoading"
            :tree-data="treeData"
            show-status
            show-detail
            :filter-text="filterText"
            :expanded-key="expandedKey"
            @handleNodeClick="handleNodeClick"
          >
            <template #csLabel="{showStatus,data}">
              <span v-if="!data.ParentNodes" class="cs-blue">({{ data.Code }})</span>{{ data.Label }}
              <template v-if="showStatus">
                <i
                  v-if="data.Data[statusCode]"
                  :class="[data.Data[statusCode]=='可排产' ? 'fourGreen' : data.Data[statusCode]=='排产完成' ?'fourOrange':data.Data[statusCode]=='未导入'?'fourRed':'']"
                >
                  <span>({{ data.Data[statusCode] }})</span>
                </i>
              </template>
            </template>
          </tree-detail>
        </div>
      </ExpandableSection>
    </div>
    <div class="cs-right">
      <el-card v-loading="pgLoading" class="box-card h100" element-loading-text="正在处理...">
        <h4 class="topTitle"><span />基本信息</h4>
        <el-form
          ref="formInline"
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
        >
          <el-form-item v-if="!isAdd&&!isNest" label="排产单号" prop="Schduling_Code">
            <span v-if="isView">{{ formInline.Status === 0 ? '' : formInline.Schduling_Code }}</span>
            <el-input v-else v-model="formInline.Schduling_Code" disabled />
          </el-form-item>
          <el-form-item label="计划员" prop="Create_UserName">
            <span v-if="isView">{{ formInline.Create_UserName }}</span>
            <el-input
              v-else
              v-model="formInline.Create_UserName"
              disabled
            />
          </el-form-item>
          <el-form-item
            label="要求完成时间"
            prop="Finish_Date"
            :rules="{ required: true, message: '请选择', trigger: 'change' }"
          >
            <span v-if="isView">{{ formInline.Finish_Date | timeFormat }}</span>
            <el-date-picker
              v-else
              v-model="formInline.Finish_Date"
              :picker-options="pickerOptions"
              :disabled="isView"
              value-format="yyyy-MM-dd"
              type="date"
              placeholder="选择日期"
            />
          </el-form-item>
          <el-form-item v-if="!isNest && !isVersionFour" label="批次" prop="Create_UserName">
            <span v-if="isView">{{ installName }}</span>
            <el-select
              v-else
              v-model="formInline.InstallUnit_Id"
              :disabled="!isAdd"
              filterable
              placeholder="请选择"
              @change="installChange"
            >
              <el-option
                v-for="item in installUnitIdList"
                :key="item.Id"
                :label="item.Name"
                :value="item.Id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="Remark">
            <span v-if="isView">{{ formInline.Remark }}</span>
            <el-input
              v-else
              v-model="formInline.Remark"
              :disabled="isView"
              style="width: 320px"
              placeholder="请输入"
            />
          </el-form-item>

        </el-form>
        <el-divider class="elDivder" />
        <div class="btn-x">
          <div v-if="!isView">
            <div ref="searchDom" class="search-container">
              <el-form ref="searchForm" :model="innerForm" inline>
                <el-form-item label-width="80px" prop="searchContent" :label="`${comName}名称` ">
                  <el-input
                    v-model="innerForm.searchContent"
                    clearable
                    class="input-with-select"
                    placeholder="请输入(空格区分/多个搜索)"
                    size="small"
                  >
                    <el-select
                      slot="prepend"
                      v-model="curSearch"
                      placeholder="请选择"
                      style="width: 100px"
                    >
                      <el-option label="精准查询" :value="1" />
                      <el-option label="模糊查询" :value="0" />
                    </el-select>
                  </el-input>
                </el-form-item>

                <el-form-item label-width="80px" :label="isCom?`${comName}类型`:'零件类型'" prop="searchComTypeSearch">
                  <el-tree-select
                    v-if="$route.query.status!=='view'"
                    ref="treeSelectComponentType"
                    v-model="innerForm.searchComTypeSearch"
                    placeholder="请选择"
                    :select-params="treeSelectParams"
                    class="cs-tree-x"
                    :tree-params="treeParamsComponentType"
                    @searchFun="componentTypeFilter"
                  />
                </el-form-item>

                <el-form-item label="规格" label-width="50px" prop="searchSpecSearch">
                  <el-input v-model="innerForm.searchSpecSearch" placeholder="请输入" clearable="" />
                </el-form-item>
                <el-form-item v-if="isCom" label="是否直发件" prop="searchDirect">
                  <el-select v-model="innerForm.searchDirect" placeholder="请选择" clearable style="width: 120px">
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                  </el-select>
                </el-form-item>

                <el-form-item>
                  <el-button type="primary" @click="innerFilter">搜索</el-button>
                  <el-button @click="resetInnerForm">重置</el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>
        <vxe-toolbar
          ref="xToolbar1"
        >
          <template #buttons>
            <template v-if="!isView">
              <el-button v-if="!isNest" type="primary" :disabled="disabledAdd" @click="handleAddDialog()">添加</el-button>
              <el-button
                v-if="workshopEnabled"
                :disabled="!multipleSelection.length"
                @click="handleBatchWorkshop(1)"
              >分配车间
              </el-button>
              <el-button
                v-if="!isCom"
                :disabled="!multipleSelection.length"
                @click="handleSelectMenu('process')"
              >分配工序
              </el-button>
              <el-dropdown v-if="isCom" style="margin:0 10px" @command="handleSelectMenu">
                <el-button :disabled="!multipleSelection.length" type="primary" plain>
                  分配工序<i class="el-icon-arrow-down el-icon--right" />
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="process"
                  >批量分配工序
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="isCom"
                    command="deal"
                  >{{ comName }}类型自动分配
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="isVersionFour"
                    command="craft"
                  >工艺代码分配
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-button
                v-if="!isCom && !isOwnerNull"
                :disabled="!multipleSelection.length"
                @click="handleBatchOwner(1)"
              >批量分配领用工序
              </el-button>
              <el-button
                plain
                :disabled="!tbData.length"
                :loading="false"
                @click="handleReverse"
              >反选
              </el-button>
              <el-button
                type="danger"
                plain
                :loading="deleteLoading"
                :disabled="!multipleSelection.length"
                @click="handleDelete"
              >删除
              </el-button>
            </template>
            <template v-else>
              <el-button :disabled="!tbData.length" @click="handleExport">导出</el-button>
            </template>
          </template>
          <template #tools>
            <DynamicTableFields
              title="表格配置"
              :table-config-code="gridCode"
              @updateColumn="changeColumn"
            />
          </template>
        </vxe-toolbar>

        <div class="tb-x">
          <!--          activeMethod: activeCellMethod,-->
          <vxe-table
            ref="xTable"
            :key="tbKey"
            :empty-render="{name: 'NotData'}"
            show-header-overflow
            :checkbox-config="{checkField: 'checked'}"
            class="cs-vxe-table"
            :row-config="{isCurrent: true, isHover: true}"
            align="left"
            height="100%"
            :filter-config="{showIcon:false}"
            show-overflow
            :loading="tbLoading"
            stripe
            :scroll-y="{enabled: true, gt: 20}"
            size="medium"
            :edit-config="{
              trigger: 'click',
              mode: 'cell',
              showIcon: !isView,

            }"
            :data="tbData"
            resizable
            :tooltip-config="{ enterable: true }"
            @checkbox-all="tbSelectChange"
            @checkbox-change="tbSelectChange"
          >
            <vxe-column v-if="!isView" fixed="left" type="checkbox" width="60" />
            <template v-for="item in columns">
              <vxe-column
                v-if="item.Code === 'Is_Component'"
                :key="item.Code"
                :align="item.Align"
                :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
                :field="item.Code"
                :title="item.Display_Name"
                sortable
                :filters="isComponentOptions"
                :filter-method="filterComponentMethod"
                :width="item.Width"
              >
                <template #default="{ row }">
                  <el-tag
                    :type="row.Is_Component ? 'danger' : 'success'"
                  >{{ row.Is_Component ? '否' : '是' }}
                  </el-tag>
                </template>
              </vxe-column>

              <vxe-column
                v-else-if="['Type','Type_Name'].includes(item.Code)"
                :key="item.Code"
                :align="item.Align"
                :filter-method="filterTypeMethod"
                :field="item.Code"
                :filters="filterTypeOption"
                :title="item.Display_Name"
                :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
                sortable
                :width="item.Width"
              >
                <template #filter="{ $panel, column }">
                  <input v-for="(option, index) in column.filters" :key="index" v-model="option.data" type="type" @input="$panel.changeOption($event, !!option.data, option)">
                </template>
                <template #default="{ row }">
                  {{ row[item.Code] | displayValue }}
                </template>
              </vxe-column>
              <vxe-column
                v-else-if="['Comp_Code','Part_Code'].includes(item.Code)"
                :key="item.Code"
                :align="item.Align"
                :filter-method="filterCodeMethod"
                :field="item.Code"
                :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
                :filters="filterCodeOption"
                :title="item.Display_Name"
                sortable
                :width="item.Width"
              >
                <template #filter="{ $panel, column }">
                  <input v-for="(option, index) in column.filters" :key="index" v-model="option.data" type="type" @input="$panel.changeOption($event, !!option.data, option)">
                </template>
                <template #default="{ row }">
                  <el-tag v-if="row.Is_Change" style="margin: 8px;" type="danger">变</el-tag>
                  <el-tag v-if="row.stopFlag" style="margin-right: 8px;" type="danger">停</el-tag>
                  <!--                  <el-link v-if="row.DwgCount>0" type="primary" @click.stop="handleDwg(row)"> {{  row[item.Code]  | displayValue }}</el-link>-->
                  <span>{{ row[item.Code] | displayValue }}</span>
                </template>
              </vxe-column>
              <vxe-column
                v-else-if="item.Code === 'Spec'"
                :key="item.Code"
                :align="item.Align"
                :field="item.Code"
                :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
                :filters="specOptions"
                :filter-method="filterSpecMethod"
                :title="item.Display_Name"
                sortable
                :width="item.Width"
              >
                <template #filter="{ $panel, column }">
                  <input v-for="(option, index) in column.filters" :key="index" v-model="option.data" type="type" @input="$panel.changeOption($event, !!option.data, option)">
                </template>
                <template #default="{ row }">
                  {{ row.Spec | displayValue }}
                </template>
              </vxe-column>
              <vxe-column
                v-else-if="item.Code === 'Schduled_Weight'"
                :key="item.Code"
                :align="item.Align"
                :field="item.Code"
                :title="item.Display_Name"
                :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
                sortable
                :width="item.Width"
              >
                <template #default="{ row }">
                  {{ (row.Schduled_Count * row.Weight).toFixed(2)/1 }}
                </template>
              </vxe-column>
              <vxe-column
                v-else-if="item.Code === 'Technology_Path'"
                :key="item.Code"
                :align="item.Align"
                :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
                :field="item.Code"
                :show-overflow="false"
                :title="item.Display_Name"
                sortable
                :min-width="item.Width"
              >
                <template #default="{ row }">
                  <div class="cs-column-row">
                    <div class="cs-ell">
                      <el-tooltip class="item" effect="dark" :content="row.Technology_Path" placement="top">
                        <span>{{ row.Technology_Path | displayValue }}</span>
                      </el-tooltip>
                    </div>
                    <i
                      v-if="!isView"
                      class="el-icon-edit"
                      @click="openBPADialog(2, row)"
                    />
                  </div>
                </template>
              </vxe-column>
              <vxe-column
                v-else-if="item.Code === 'Part_Used_Process'"
                :key="item.Code"
                :align="item.Align"
                :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
                :field="item.Code"
                :title="item.Display_Name"
                sortable
                :show-overflow="false"
                :min-width="item.Width"
              >
                <template #default="{ row }">
                  <div class="cs-column-row">
                    <div class="cs-ell">
                      <el-tooltip class="item" effect="dark" :content="row.Part_Used_Process" placement="top">
                        <span>{{ row.Part_Used_Process | displayValue }}</span>
                      </el-tooltip>
                    </div>
                    <i
                      v-if="showPartUsedProcess(row)"
                      class="el-icon-edit"
                      @click="handleBatchOwner(2, row)"
                    />
                  </div>
                </template>
              </vxe-column>
              <vxe-column
                v-else-if="item.Code === 'Workshop_Name'"
                :key="item.Code"
                :align="item.Align"
                :show-overflow="false"
                :field="item.Code"
                :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
                :title="item.Display_Name"
                sortable
                :min-width="item.Width"
              >
                <template #default="{ row }">
                  <div class="cs-column-row">
                    <div class="cs-ell">
                      <el-tooltip class="item" effect="dark" :content="row.Workshop_Name" placement="top">
                        <span>{{ row.Workshop_Name | displayValue }}</span>
                      </el-tooltip>
                    </div>
                    <i
                      v-if="!isView"
                      class="el-icon-edit"
                      @click="handleBatchWorkshop(2, row)"
                    />
                  </div>
                </template>
              </vxe-column>
              <vxe-column
                v-else-if="item.Code === 'Schduled_Count'"
                :key="item.Code"
                :align="item.Align"
                :field="item.Code"
                :title="item.Display_Name"
                sortable
                :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
                :edit-render="{enabled:!isView}"
                :min-width="item.Width"
              >
                <template #edit="{ row }">
                  <vxe-input
                    v-model.number="row.Schduled_Count"
                    type="integer"
                    min="0"
                    :max="row.chooseCount"
                  />
                </template>
                <template #default="{ row }">
                  {{ row.Schduled_Count | displayValue }}
                </template>
              </vxe-column>
              <vxe-column
                v-else
                :key="item.Id"
                :align="item.Align"
                :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
                show-overflow="tooltip"
                sortable
                :field="item.Code"
                :title="item.Display_Name"
                :min-width="item.Width"
              />
            </template>

          </vxe-table>
        </div>
        <el-divider v-if="!isView" class="elDivder" />
        <footer v-if="!isView">
          <div class="data-info">
            <el-tag
              size="medium"
              class="info-x"
            >已选 {{ multipleSelection.length }} 条数据
            </el-tag>
            <el-tag v-if="tipLabel" size="medium" class="info-x">{{
              tipLabel
            }}
            </el-tag>
          </div>
          <div>
            <el-button v-if="workshopEnabled&&!isNest " type="primary" @click="saveWorkShop">保存车间分配</el-button>
            <el-button
              v-if="!isNest"
              type="primary"
              :loading="saveLoading"
              @click="saveDraft(false)"
            >保存草稿
            </el-button>
            <el-button :disabled="deleteLoading || tbData.some(item=>item.stopFlag)" @click="handleSubmit">下发任务</el-button>
          </div>
        </footer>
      </el-card>
    </div>

    <el-dialog
      v-if="dialogVisible"
      v-dialogDrag
      class="plm-custom-dialog"
      :title="title"
      :visible.sync="dialogVisible"
      :width="dWidth"
      top="10vh"
      @close="handleClose"
    >
      <component
        :is="currentComponent"
        ref="content"
        :is-nest="isNest"
        :is-version-four="isVersionFour"
        :is-part-prepare="isPartPrepare"
        :process-list="processList"
        :page-type="pageType"
        :part-type-option="typeOption"
        :level-code="levelCode"
        @close="handleClose"
        @sendProcess="sendProcess"
        @workShop="getWorkShop"
        @refresh="fetchData"
        @setProcessList="setProcessList"
      />
    </el-dialog>

    <el-dialog
      :key="addDraftKey"
      v-dialogDrag
      class="plm-custom-dialog"
      :title="title"
      :visible.sync="openAddDraft"
      :width="dWidth"
      top="10vh"
      @close="handleClose"
    >
      <add-draft
        ref="draft"
        :level-code="levelCode"
        :com-name="comName"
        :current-ids="currentIds"
        :is-part-prepare="isPartPrepare"
        :area-id="areaId"
        :install-id="formInline.InstallUnit_Id"
        :schedule-id="scheduleId"
        :show-dialog="openAddDraft"
        :page-type="pageType"
        @sendSelectList="mergeSelectList"
        @close="handleClose"
      />
    </el-dialog>
  </div>
</template>

<script>

import { closeTagView, debounce } from '@/utils'
import BatchProcessAdjust from './components/BatchProcessAdjust'
import {
  GetCanSchdulingPartList,
  GetCompSchdulingInfoDetail,
  GetDwg,
  GetPartSchdulingInfoDetail,
  GetSchdulingWorkingTeams,
  SaveComponentSchedulingWorkshop,
  SaveCompSchdulingDraft,
  SavePartSchdulingDraftNew,
  SavePartSchedulingWorkshopNew,
  SaveSchdulingTaskById
} from '@/api/PRO/production-task'
import { GetStopList } from '@/api/PRO/production-task'
import AddDraft from './components/addDraft'
import OwnerProcess from './components/OwnerProcess'
import Workshop from './components/Workshop.vue'
import { GetGridByCode } from '@/api/sys'
import { uniqueCode } from './constant'
import { v4 as uuidv4 } from 'uuid'
import numeral from 'numeral'
import { GetLibListType, GetProcessFlowListWithTechnology, GetProcessListBase } from '@/api/PRO/technology-lib'
import { AreaGetEntity } from '@/api/plm/projects'
import { mapActions, mapGetters } from 'vuex'
import { GetPartTypeList } from '@/api/PRO/partType'
import moment from 'moment'
import ExpandableSection from '@/components/ExpandableSection/index.vue'
import TreeDetail from '@/components/TreeDetail/index.vue'
import { GetInstallUnitIdNameList, GetProjectAreaTreeList } from '@/api/PRO/project'

import { GetCompTypeTree } from '@/api/PRO/factorycheck'
import { parseOssUrl } from '@/utils/file'
import DynamicTableFields from '@/components/DynamicTableFields/index.vue'
import { GetBOMInfo } from '@/views/PRO/bom-setting/utils'

const SPLIT_SYMBOL = '$_$'
export default {
  components: { DynamicTableFields, TreeDetail, ExpandableSection, BatchProcessAdjust, AddDraft, Workshop, OwnerProcess },
  data() {
    return {
      bomList: [],
      comName: '',
      levelCode: '',
      isComponentOptions: [
        { label: '是', value: false },
        { label: '否', value: true }
      ],
      specOptions: [{ data: '' }],
      filterTypeOption: [{ data: '' }],
      filterCodeOption: [{ data: '' }],
      pickerOptions: {
        disabledDate(time) {
        }
      },
      innerForm: {
        searchContent: '',
        searchComTypeSearch: '',
        searchSpecSearch: '',
        searchDirect: ''
      },
      curSearch: 1,
      searchType: '',
      formInline: {
        Schduling_Code: '',
        Create_UserName: '',
        Finish_Date: '',
        InstallUnit_Id: '',
        Remark: ''
      },
      total: 0,
      currentIds: '',
      gridCode: '',
      columns: [],
      tbData: [],
      tbConfig: {},
      TotalCount: 0,
      multipleSelection: [],
      showExpand: true,
      pgLoading: false,
      deleteLoading: false,
      workShopIsOpen: false,
      isOwnerNull: false,
      dialogVisible: false,
      openAddDraft: false,
      saveLoading: false,
      tbLoading: false,
      isCheckAll: false,
      currentComponent: '',
      dWidth: '25%',
      title: '',
      tbKey: 100,
      search: () => ({}),
      pageType: undefined,
      tipLabel: '',
      technologyOption: [],
      typeOption: [],
      workingTeam: [],
      pageStatus: undefined,
      scheduleId: '',
      partComOwnerColumn: null,

      installUnitIdList: [],
      projectId: '',
      areaId: '',
      projectName: '',
      statusType: '可排产',
      expandedKey: '',
      treeLoading: false,
      treeData: [],
      treeParamsComponentType: {
        'default-expand-all': true,
        'check-strictly': true,
        filterable: true,
        clickParent: true,
        data: [],
        props: {
          children: 'Children',
          label: 'Label',
          value: 'Data'
        }
      },
      treeSelectParams: {
        placeholder: '请选择',
        collapseTags: true,
        clearable: true
      },
      disabledAdd: true
    }
  },
  watch: {
    'tbData.length': {
      handler(n, o) {
        this.checkOwner()
      },
      immediate: false
    }

  },

  computed: {
    isCom() {
      return this.pageType === 'com'
    },
    isView() {
      return this.pageStatus === 'view'
    },
    isEdit() {
      return this.pageStatus === 'edit'
    },
    isAdd() {
      return this.pageStatus === 'add'
    },
    addDraftKey() {
      return this.expandedKey + this.formInline.InstallUnit_Id
    },
    filterText() {
      return this.projectName + SPLIT_SYMBOL + this.statusType
    },
    statusCode() {
      return this.isCom ? 'Comp_Schdule_Status' : 'Part_Schdule_Status'
    },
    installName() {
      const item = this.installUnitIdList.find(v => v.Id === this.formInline.InstallUnit_Id)
      if (item) {
        return item.Name
      } else {
        return ''
      }
    },
    isPartPrepare() {
      return this.getIsPartPrepare && !this.isCom
    },
    isNest() {
      return this.$route.query.type === '1'
    },
    ...mapGetters('factoryInfo', ['workshopEnabled', 'getIsPartPrepare']),
    ...mapGetters('schedule', ['processList', 'nestIds']),
    ...mapGetters('tenant', ['isVersionFour'])
  },
  async mounted() {
    const { list, comName, currentBOMInfo } = await GetBOMInfo(-1)
    this.bomList = list || []
    this.comName = comName
    this.levelCode = currentBOMInfo?.Code
    console.log('currentBOMInfo', currentBOMInfo)
    console.log('levelCode', this.levelCode)
    this.initProcessList()
    this.tbDataMap = {}
    this.craftCodeMap = {}
    this.pageType = this.$route.query.pg_type
    this.pageStatus = this.$route.query.status
    this.model = this.$route.query.model
    this.scheduleId = this.$route.query.pid || ''
    // // this.formInline.Create_UserName = this.$store.getters.name
    // // 框架问题引起store数据丢失，已反馈，结果：此处先使用localStorage
    this.formInline.Create_UserName = localStorage.getItem('UserAccount')
    // if (!this.isCom) {
    //   this.getPartType()
    // } else {
    // }

    this.unique = uniqueCode()
    this.checkWorkshopIsOpen()

    this.search = debounce(this.fetchData, 800, true)
    await this.mergeConfig()
    if (this.isView || this.isEdit) {
      const { areaId, install } = this.$route.query
      this.areaId = areaId
      this.formInline.InstallUnit_Id = install
      this.getInstallUnitIdNameList()
      this.fetchData()
    }

    if (this.isAdd) {
      this.fetchTreeData()
      this.getType()
    }
    if (this.isEdit) {
      this.getType()
    }
  },
  methods: {
    ...mapActions('schedule', ['changeProcessList', 'initProcessList']),
    checkOwner() {
      if (this.isCom) return
      this.isOwnerNull = this.tbData.every(v => !v.Comp_Import_Detail_Id) && !this.isNest
      const idx = this.columns.findIndex(v => v.Code === 'Part_Used_Process')
      if (this.isOwnerNull) {
        idx !== -1 && this.columns.splice(idx, 1)
      } else {
        if (idx === -1) {
          if (!this.ownerColumn) {
            this.$message({
              message: '列表配置字段缺少零件领用工序字段',
              type: 'success'
            })
            return
          }
          this.columns.push(this.ownerColumn)
        }
        this.comPart = true
      }
    },
    async mergeConfig() {
      await this.getConfig()
      await this.getWorkTeam()
    },
    async getConfig() {
      let configCode = ''
      if (this.isNest) {
        if (this.isView) {
          configCode = 'PRONestingScheduleDetail'
        } else {
          configCode = 'PRONestingScheduleConfig'
        }
      } else {
        configCode = (this.isView ? 'PROComViewPageTbConfig' : 'PROComDraftPageTbConfig')
      }
      this.gridCode = configCode
      await this.getTableConfig(configCode)
      if (!this.workshopEnabled) {
        this.columns = this.columns.filter(v => v.Code !== 'Workshop_Name')
      }
      if (!this.isVersionFour) {
        this.columns = this.columns.filter(v => v.Code !== 'Technology_Code')
      }
      this.checkOwner()
    },
    async changeColumn() {
      await this.getTableConfig(this.gridCode)
      this.tbKey++
    },
    handleNodeClick(data) {
      this.expandedKey = data.Id
      if (this.areaId === data.Id) {
        this.disabledAdd = false
        return
      }
      this.disabledAdd = true
      if (!data.ParentNodes || data.Children?.length > 0) {
        return
      }
      if (data?.Data[this.statusCode] === '未导入') {
        this.$message({
          message: '清单未导入，请联系深化人员导入清单',
          type: 'warning'
        })

        return
      }

      const initData = ({ Data }) => {
        this.areaId = Data.Id
        this.projectId = Data.Project_Id
        this.expandedKey = this.areaId
        this.formInline.Finish_Date = ''
        this.formInline.InstallUnit_Id = ''
        this.formInline.Remark = ''
        this.tbData = []
        this.getAreaInfo()
        this.getInstallUnitIdNameList()
      }

      if (this.tbData.length) {
        this.$confirm('切换区域右侧数据清空，是否确认?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          initData(data)
          this.disabledAdd = false
          this.tbDataMap = {}
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
      } else {
        this.disabledAdd = false
        initData(data)
      }
    },

    customFilterFun(value, data, node) {
      // console.log('customFilterFun', value, data, node)

      const arr = value.split(SPLIT_SYMBOL)
      const labelVal = arr[0]
      const statusVal = arr[1]
      if (!value) return true
      let parentNode = node.parent
      let labels = [node.label]
      let status = [data.Data[this.statusCode]]
      let level = 1
      while (level < node.level) {
        labels = [...labels, parentNode.label]
        status = [...status, data.Data[this.statusCode]]
        parentNode = parentNode.parent
        level++
      }
      labels = labels.filter(v => !!v)
      status = status.filter(v => !!v)
      let resultLabel = true
      let resultStatus = true
      if (this.statusType) {
        resultStatus = status.some(s => s.indexOf(statusVal) !== -1)
      }
      if (this.projectName) {
        resultLabel = labels.some(s => s.indexOf(labelVal) !== -1)
      }
      return resultLabel && resultStatus
    },
    async fetchData() {
      this.tbLoading = true
      let resData = null
      if (this.isNest) {
        if (this.isView) {
          resData = await this.getPartPageList()
        } else {
          resData = await this.getNestPageList()
        }
      } else {
        resData = await this.getComPageList()
        console.log('resData', resData)
      }

      this.initTbData(resData)
      this.tbLoading = false
    },
    fetchTreeDataLocal() {
      // this.filterText = this.projectName
    },
    fetchTreeStatus() {
      // this.filterText = this.statusType
    },
    fetchTreeData() {
      this.treeLoading = true
      console.log('78,this.$route.meta', this.$route, this.$router)
      GetProjectAreaTreeList({ MenuId: this.$route.meta.Id, projectName: this.projectName, Type: this.isCom ? 1 : 2 }).then((res) => {
        if (!res.IsSucceed) {
          this.$message({
            message: res.Message,
            type: 'error'
          })
          this.treeData = []
          this.treeLoading = false
          return
        }
        if (res.Data.length === 0) {
          this.treeLoading = false
          this.treeData = []
          return
        }
        const resData = res.Data.map(item => {
          item.Is_Directory = true
          return item
        })
        this.treeData = resData
        this.$nextTick(_ => {
          this.$refs.tree.filterRef(this.filterText)
          const result = this.setKey()
          if (!result) {
            this.pgLoading = false
          }
        })
        this.treeLoading = false
      }).catch((e) => {
        console.log('catche', e)
        this.treeLoading = false
        this.treeData = []
      })
    },
    setKey() {
      const deepFilter = (tree) => {
        for (let i = 0; i < tree.length; i++) {
          const item = tree[i]
          const { Data, Children } = item
          const node = getNode(Data.Id)
          if (Data.ParentId && !Children?.length && node.visible) {
            this.handleNodeClick(item)
            return true
          } else {
            if (Children?.length) {
              const shouldStop = deepFilter(Children)
              if (shouldStop) return true
            }
          }
        }
        return false
      }
      const getNode = (key) => {
        return this.$refs['tree'].getNodeByKey(key)
      }
      return deepFilter(this.treeData)
    },
    closeView() {
      closeTagView(this.$store, this.$route)
    },
    checkWorkshopIsOpen() {
      this.workShopIsOpen = true
    },
    tbSelectChange(array) {
      this.multipleSelection = array.records
    },
    getAreaInfo() {
      this.formInline.Finish_Date = ''
      AreaGetEntity({
        id: this.areaId
      }).then(res => {
        if (res.IsSucceed) {
          if (!res.Data) {
            return []
          }

          const start = moment(res.Data?.Demand_Begin_Date)
          const end = moment(res.Data?.Demand_End_Date)
          this.pickerOptions.disabledDate = (time) => {
            return time.getTime() < start || time.getTime() > end
          }
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    handleClose() {
      this.dialogVisible = false
      this.openAddDraft = false
    },
    getNestPageList() {
      return new Promise((resolve, reject) => {
        GetCanSchdulingPartList({
          Ids: this.nestIds
        }).then(res => {
          if (res.IsSucceed) {
            const _list = res?.Data || []
            const list = _list.map(v => {
              if (v.Scheduled_Used_Process) {
                // 已存在操作过数据
                v.Part_Used_Process = v.Scheduled_Used_Process
              }
              // v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''
              v.Workshop_Id = v.Scheduled_Workshop_Id
              v.Workshop_Name = v.Scheduled_Workshop_Name
              v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path
              v.chooseCount = v.Can_Schduling_Count
              return v
            })

            resolve(list)
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
            reject()
          }
        })
      })
    },
    async getComPageList() {
      const {
        pid
      } = this.$route.query
      const result = await GetCompSchdulingInfoDetail({
        Schduling_Plan_Id: pid
      }).then((res) => {
        if (res.IsSucceed) {
          const { Schduling_Plan, Schduling_Comps, Process_List } = res.Data
          this.formInline = Object.assign(this.formInline, Schduling_Plan)
          Process_List.forEach(item => {
            const plist = {
              key: item.Process_Code,
              value: item
            }
            this.changeProcessList(plist)
          })
          const list = Schduling_Comps.map(v => {
            v.chooseCount = v.Can_Schduling_Count
            return v
          })
          this.getStopList(list)
          return list || []
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
      return result || []
    },
    async getStopList(list) {
      console.log('getStopList', list)
      const submitObj = list.map(item => {
        return {
          Id: item.Comp_Import_Detail_Id,
          Type: 2
        }
      })
      await GetStopList(submitObj).then(res => {
        if (res.IsSucceed) {
          const stopMap = {}
          res.Data.forEach(item => {
            stopMap[item.Id] = !!item.Is_Stop
          })
          list.forEach(row => {
            if (stopMap[row.Comp_Import_Detail_Id]) {
              this.$set(row, 'stopFlag', stopMap[row.Comp_Import_Detail_Id])
            }
          })
        }
      })
    },
    getPartPageList() {
      return new Promise((resolve, reject) => {
        const {
          pid
        } = this.$route.query
        GetPartSchdulingInfoDetail({
          Schduling_Plan_Id: pid
        }).then((res) => {
          if (res.IsSucceed) {
            const SarePartsModel = res.Data?.SarePartsModel.map(v => {
              if (v.Scheduled_Used_Process) {
                // 已存在操作过数据
                v.Part_Used_Process = v.Scheduled_Used_Process
              }
              v.chooseCount = v.Can_Schduling_Count
              return v
            })
            this.formInline = Object.assign(this.formInline, res.Data?.Schduling_Plan)
            resolve(SarePartsModel || [])
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
            reject()
          }
        })
      })
    },
    initTbData(list, teamKey = 'Allocation_Teams') {
      this.tbData = list.map(row => {
        const processList = row.Technology_Path?.split('/') || []
        row.uuid = uuidv4()
        this.addElementToTbData(row)
        if (row[teamKey]) {
          const newData = row[teamKey].filter((r) => processList.findIndex((p) => r.Process_Code === p) !== -1)
          newData.forEach((ele, index) => {
            const code = this.getRowUnique(row.uuid, ele.Process_Code, ele.Working_Team_Id)
            const max = this.getRowUniqueMax(row.uuid, ele.Process_Code, ele.Working_Team_Id)
            row[code] = ele.Count
            row[max] = 0
          })
        }
        this.setInputMax(row)
        return row
      })
      let ids = ''
      if (this.isCom) {
        ids = this.tbData.map(v => v.Comp_Import_Detail_Id).toString()
      } else {
        ids = this.tbData.map(v => v.Part_Aggregate_Id).toString()
      }
      this.currentIds = ids
    },
    async mergeSelectList(newList) {
      if (this.isVersionFour) {
        await this.mergeCraftProcess(newList)
      }
      console.time('mergeSelectListTime')
      let hasUsedPartFlag = true
      newList.forEach((element, index) => {
        const cur = this.getMergeUniqueRow(element)
        if (!cur) {
          element.puuid = element.uuid
          element.Schduled_Count = element.chooseCount
          element.Schduled_Weight = numeral(element.chooseCount * element.Weight).format('0.[00]')
          if (this.isVersionFour && !element.Technology_Path) {
            /*          if (this.craftCodeMap[element.Technology_Code] && this.craftCodeMap[element.Technology_Code] instanceof Array) {
              const curPathArr = this.craftCodeMap[element.Technology_Code]
              if (element.Part_Used_Process && !curPathArr.includes(element.Part_Used_Process)) {
                hasUsedPartFlag = false
              } else {
                element.Technology_Path = curPathArr.join('/')
              }
            }*/
            if (this.craftCodeMap[element.Technology_Code] instanceof Array) {
              const curPathArr = this.craftCodeMap[element.Technology_Code]
              if (element.Part_Used_Process) {
                const partUsedProcessArr = element.Part_Used_Process.split(',')
                const allPartsIncluded = partUsedProcessArr.every(part => curPathArr.includes(part))

                if (!allPartsIncluded) {
                  hasUsedPartFlag = false
                } else {
                  element.Technology_Path = curPathArr.join('/')
                }
              } else {
                element.Technology_Path = curPathArr.join('/')
              }
            }
          }
          this.tbData.push(element)
          this.addElementToTbData(element)
          return
        }

        cur.puuid = element.uuid

        cur.Schduled_Count += element.chooseCount
        cur.Schduled_Weight = numeral(cur.Schduled_Weight).add(element.chooseCount * element.Weight).format('0.[00]')
        if (!cur.Technology_Path) {
          return
        }
        this.setInputMax(cur)
      })
      this.showCraftUsedPartResult(hasUsedPartFlag)

      // if (this.isCom) {
      //   this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)
      // } else {
      //   this.tbData.sort((a, b) => a.Part_Code.localeCompare(b.Part_Code))
      // }
      this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)
      console.timeEnd('mergeSelectListTime')
    },
    addElementToTbData(element) {
      const key = this.getUniKey(element)
      this.tbDataMap[key] = element
    },
    getMergeUniqueRow(element) {
      const key = this.getUniKey(element)
      return this.tbDataMap[key]
    },
    getUniKey(element) {
      if (this.isVersionFour) {
        return this.isCom ? (element.Comp_Code + element.InstallUnit_Name).toString().trim() : ((element.Component_Code ?? '') + element.Part_Code + element.Part_Aggregate_Id)
      } else {
        return this.isCom ? element.Comp_Code : ((element.Component_Code ?? '') + element.Part_Code + element.Part_Aggregate_Id)
      }
    },
    checkForm() {
      let isValidate = true
      this.$refs['formInline'].validate((valid) => {
        if (!valid) isValidate = false
      })
      return isValidate
    },
    async saveDraft(isOrder = false) {
      const checkSuccess = this.checkForm()
      if (!checkSuccess) return false
      const { tableData, status } = this.getSubmitTbInfo()
      if (!status) return false
      if (!isOrder) {
        this.saveLoading = true
      }

      const isSuccess = await this.handleSaveDraft(tableData, isOrder)
      console.log('isSuccess', isSuccess)
      if (!isSuccess) return false
      if (isOrder) return isSuccess
      this.$refs['draft']?.fetchData()
      this.saveLoading = false
    },
    async saveWorkShop() {
      const checkSuccess = this.checkForm()
      if (!checkSuccess) return false
      const obj = {}
      if (!this.tbData.length) {
        this.$message({
          message: '数据不能为空',
          type: 'success'
        })
        return
      }
      if (this.isCom) {
        obj.Schduling_Comps = this.tbData
      } else {
        obj.SarePartsModel = this.tbData
      }
      if (this.isEdit) {
        obj.Schduling_Plan = this.formInline
      } else {
        obj.Schduling_Plan = {
          ...this.formInline,
          Project_Id: this.projectId,
          Area_Id: this.areaId,
          Schduling_Model: this.model // 1构件单独排产，2零件单独排产，3构/零件一起排产
        }
      }
      this.pgLoading = true
      const _fun = this.isCom ? SaveComponentSchedulingWorkshop : SavePartSchedulingWorkshopNew
      _fun(obj).then(res => {
        if (res.IsSucceed) {
          this.pgLoading = false
          this.$message({
            message: '保存成功',
            type: 'success'
          })
          this.closeView()
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
          this.pgLoading = false
        }
      })
    },
    getSubmitTbInfo() {
      // 处理上传的数据
      let tableData = JSON.parse(JSON.stringify(this.tbData))
      tableData = tableData.filter(item => item.Schduled_Count > 0)
      for (let i = 0; i < tableData.length; i++) {
        const element = tableData[i]
        let list = []
        if (!element.Technology_Path) {
          this.$message({
            message: '工序不能为空',
            type: 'warning'
          })
          return { status: false }
        }
        if (this.isPartPrepare && !element.Part_Used_Process && element.Type !== 'Direct' && this.comPart) {
          const msg = '领用工序不能为空'
          if (this.isNest) {
            if (element.Comp_Import_Detail_Id) {
              this.$message({
                message: msg,
                type: 'warning'
              })
              return { status: false }
            }
          } else {
            this.$message({
              message: msg,
              type: 'warning'
            })
            return { status: false }
          }
        }
        // if (!this.isCom && element.Comp_Import_Detail_Id && !element.Part_Used_Process) {
        //   // 零构件 零件单独排产
        //   this.$message({
        //     message: '零件领用工序不能为空',
        //     type: 'warning'
        //   })
        //   return { status: false }
        // }
        if (element.Scheduled_Technology_Path && element.Scheduled_Technology_Path !== element.Technology_Path) {
          this.$message({
            message: `请和该区域批次下已排产同${this.isCom ? `${this.comName}` : '零件'}保持工序一致`,
            type: 'warning'
          })
          return { status: false }
        }
        if (element.Scheduled_Used_Process && element.Scheduled_Used_Process !== element.Part_Used_Process) {
          this.$message({
            message: `请和该区域批次下已排产同零件领用工序保持一致`,
            type: 'warning'
          })
          return { status: false }
        }
        const processList = Array.from(new Set(element.Technology_Path.split('/')))
        /* processList.forEach(code => {
          const groups = this.workingTeam.filter(v => v.Process_Code === code)
          const groupsList = groups.map(group => {
            const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)
            const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)
            const obj = {
              Team_Task_Id: element.Team_Task_Id,
              Comp_Code: element.Comp_Code,
              Again_Count: +element[uCode] || 0, // 不填，后台让传0
              Part_Code: this.isCom ? null : '',
              Process_Code: code,
              Technology_Path: element.Technology_Path,
              Working_Team_Id: group.Working_Team_Id,
              Working_Team_Name: group.Working_Team_Name
            }
            delete element[uCode]
            delete element[uMax]
            return obj
          })
          list.push(...groupsList)
        })*/
        for (let j = 0; j < processList.length; j++) {
          const code = processList[j]
          const schduledCount = element.Schduled_Count || 0
          let groups = []
          if (element.Allocation_Teams) {
            groups = element.Allocation_Teams.filter(v => v.Process_Code === code)
          }
          const againCount = groups.reduce((acc, cur) => {
            return acc + (cur.Again_Count || 0)
          }, 0)
          if (againCount > schduledCount) {
            list = []
            break
          } else {
            list.push(...groups)
          }
        }
        const hasInput = Object.keys(element).filter(_ => _.startsWith(element['uuid']))
        hasInput.forEach((item) => {
          delete element[item]
        })
        delete element['uuid']
        delete element['_X_ROW_KEY']
        delete element['puuid']
        element.Allocation_Teams = list
      }
      return { tableData, status: true }
    },
    async handleSaveDraft(tableData, isOrder) {
      console.log('保存草稿')
      const _fun = this.isCom ? SaveCompSchdulingDraft : SavePartSchdulingDraftNew
      const obj = {}
      if (this.isCom) {
        obj.Schduling_Comps = tableData
        const p = []
        for (const objKey in this.processList) {
          if (this.processList.hasOwnProperty(objKey)) {
            p.push(this.processList[objKey])
          }
        }
        obj.Process_List = p
      } else {
        obj.SarePartsModel = tableData
      }
      if (this.isEdit) {
        obj.Schduling_Plan = this.formInline
      } else {
        obj.Schduling_Plan = {
          ...this.formInline,
          Project_Id: this.projectId,
          Area_Id: this.areaId,
          Schduling_Model: this.model // 1构件单独排产，2零件单独排产，3构/零件一起排产
        }
      }
      let orderSuccess = false
      console.log('obj', obj)

      await _fun(obj).then(res => {
        if (res.IsSucceed) {
          if (!isOrder) {
            this.pgLoading = false
            this.$message({
              message: '保存成功',
              type: 'success'
            })
            this.closeView()
          } else {
            this.templateScheduleCode = res.Data
            orderSuccess = true
            console.log('保存草稿成功 ')
          }
        } else {
          this.saveLoading = false
          this.pgLoading = false
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
      console.log('结束 ')
      return orderSuccess
    },
    handleDelete() {
      this.deleteLoading = true
      setTimeout(() => {
        const selectedUuids = new Set(this.multipleSelection.map(v => v.uuid))
        this.tbData = this.tbData.filter(item => {
          const isSelected = selectedUuids.has(item.uuid)
          if (isSelected) {
            const key = this.getUniKey(item)
            delete this.tbDataMap[key]
          }
          return !isSelected
        })
        this.$nextTick(_ => {
          this.$refs['draft']?.mergeData(this.multipleSelection)
          this.multipleSelection = []
        })
        this.deleteLoading = false
      }, 0)
    },
    async getWorkTeam() {
      await GetSchdulingWorkingTeams({
        type: this.isCom ? 1 : 2
      }).then(res => {
        if (res.IsSucceed) {
          this.workingTeam = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    handleSubmit() {
      this.$refs['formInline'].validate((valid) => {
        if (!valid) return
        const { tableData, status } = this.getSubmitTbInfo()
        if (!status) return
        this.$confirm('是否提交当前数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.saveDraftDoSubmit(tableData)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
      })
    },
    async saveDraftDoSubmit() {
      this.pgLoading = true
      if (this.formInline?.Schduling_Code) {
        const isSuccess = await this.saveDraft(true)
        console.log('saveDraftDoSubmit', isSuccess)
        isSuccess && this.doSubmit(this.formInline.Id)
      } else {
        const isSuccess = await this.saveDraft(true)
        isSuccess && this.doSubmit(this.templateScheduleCode)
      }
    },
    doSubmit(scheduleCode) {
      SaveSchdulingTaskById({
        schdulingPlanId: scheduleCode
      }).then(res => {
        if (res.IsSucceed) {
          this.$message({
            message: '下达成功',
            type: 'success'
          })
          this.closeView()
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      }).finally(_ => {
        this.pgLoading = false
      }).catch(_ => {
        this.pgLoading = false
      })
    },
    async getWorkShop(value) {
      const {
        origin,
        row,
        workShop: {
          Id,
          Display_Name
        }
      } = value
      if (origin === 2) {
        if (value.workShop?.Id) {
          row.Workshop_Name = Display_Name
          row.Workshop_Id = Id
          this.setPath(row, Id)
        } else {
          row.Workshop_Name = ''
          row.Workshop_Id = ''
        }
      } else {
        // const gyMap = await this.getCraftProcess()
        // const _process = await this.getProcessOption(value.workShop?.Id)
        this.multipleSelection.forEach(item => {
          if (value.workShop?.Id) {
            item.Workshop_Name = Display_Name
            item.Workshop_Id = Id
            this.setPath(item, Id)
          } else {
            item.Workshop_Name = ''
            item.Workshop_Id = ''
          }
        })
      }
    },
    setPath(row, Id) {
      if (row?.Scheduled_Workshop_Id) {
        if (row.Scheduled_Workshop_Id !== Id) {
          row.Technology_Path = ''
        }
      } else {
        row.Technology_Path = ''
      }
    },
    handleBatchWorkshop(origin, row) {
      this.title = origin === 1 ? '批量分配车间' : '分配车间'
      this.currentComponent = 'Workshop'
      this.dWidth = '30%'
      this.dialogVisible = true
      this.$nextTick(_ => {
        this.$refs['content'].fetchData(origin, row, this.multipleSelection)
      })
    },
    async mergeCraftProcess(list) {
      let codes = [...new Set(list.map(v => v.Technology_Code))]
      for (const key in this.craftCodeMap) {
        if (this.craftCodeMap.hasOwnProperty(key)) {
          codes = codes.filter(code => code !== key)
        }
      }
      const _craftCodeMap = await this.getCraftProcess(codes)
      Object.assign(this.craftCodeMap, _craftCodeMap)
    },
    getCraftProcess(gyGroup = []) {
      gyGroup = gyGroup.filter(v => !!v)
      if (!gyGroup.length) return Promise.resolve({})
      return new Promise((resolve, reject) => {
        GetProcessFlowListWithTechnology({
          TechnologyCodes: gyGroup,
          Type: 1
        }).then(res => {
          if (res.IsSucceed) {
            const gyList = res.Data || []
            const gyMap = gyList.reduce((acc, item) => {
              acc[item.Code] = item.Technology_Path
              return acc
            }, {})
            console.log('gyMap', gyMap)
            resolve(gyMap)
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
            reject()
          }
        })
      })
    },
    /*   checkProcess(pList, flowList) {
      return flowList.every(item => pList.includes(item))
    },*/
    async handleAutoDeal() {
      /*      if (this.workshopEnabled) {
        const hasWorkShop = this.checkHasWorkShop(1, this.multipleSelection)
        if (!hasWorkShop) return
      }*/

      this.$confirm(`是否将选中数据按${this.comName}类型自动分配`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (this.workshopEnabled) {
          const p = this.multipleSelection.map(item => {
            return {
              uniqueType: `${item.Type}$_$${item.Workshop_Id}`
            }
          })
          const codes = Array.from(new Set(p.map(v => v.uniqueType)))
          const objKey = {}
          Promise.all(codes.map(v => {
            const info = v.split('$_$')
            return this.setLibType(info[0], info[1])
          })
          ).then(res => {
            const hasUndefined = res.some(item => item == undefined)
            if (hasUndefined) {
              this.$message({
                message: `所选车间内工序班组与${this.comName}类型工序不匹配，请手动分配工序`,
                type: 'warning'
              })
            }

            res.forEach((element, idx) => {
              objKey[codes[idx]] = element
            })
            this.multipleSelection.forEach((element) => {
              element.Technology_Path = objKey[`${element.Type}$_$${element.Workshop_Id}`]
              this.resetWorkTeamMax(element, element.Technology_Path)
            })
          })
        } else {
          const p = this.multipleSelection.map(item => item.Type)
          const codes = Array.from(new Set(p))
          const objKey = {}

          Promise.all(codes.map(v => {
            return this.setLibType(v)
          })).then(res => {
            res.forEach((element, idx) => {
              objKey[codes[idx]] = element
            })
            this.multipleSelection.forEach((element) => {
              element.Technology_Path = objKey[element.Type]
              this.resetWorkTeamMax(element, element.Technology_Path)
            })
          })
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    getProcessOption(workshopId) {
      return new Promise((resolve, reject) => {
        GetProcessListBase({
          workshopId: workshopId,
          type: 1 // 0:全部，工艺类型1：构件工艺，2：零件工艺
        }).then(res => {
          if (res.IsSucceed) {
            const process = res.Data.map(v => v.Code)
            resolve(process)
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      })
    },
    setLibType(code, workshopId) {
      return new Promise((resolve) => {
        const obj = {
          Component_type: code,
          type: 1
        }
        if (this.workshopEnabled) {
          obj.workshopId = workshopId
        }
        GetLibListType(obj).then(res => {
          if (res.IsSucceed) {
            if (res.Data.Data && res.Data.Data.length) {
              const info = res.Data.Data[0]
              const workCode = info.WorkCode && info.WorkCode.replace(/\\/g, '/')
              resolve(workCode)
            } else {
              resolve(undefined)
            }
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      })
    },
    inputChange(row) {
      this.setInputMax(row)
    },
    setInputMax(row) {
      const inputValuesKeys = Object.keys(row)
        .filter(v => !v.endsWith('max') && v.startsWith(row.uuid) && v.length > row.uuid.length)
      inputValuesKeys.forEach((val) => {
        const curCode = val.split(SPLIT_SYMBOL)[1]
        const otherTotal = inputValuesKeys.filter(x => {
          const code = x.split(SPLIT_SYMBOL)[1]
          return x !== val && code === curCode
        }).reduce((acc, item) => {
          return acc + numeral(row[item]).value()
        }, 0)
        row[val + SPLIT_SYMBOL + 'max'] = row.Schduled_Count - otherTotal
      })
    },
    sendProcess({ arr, str }) {
      let isSuccess = true
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i]
        if (item.originalPath && item.originalPath !== str) {
          isSuccess = false
          break
        }
        item.Technology_Path = str
      }
      if (!isSuccess) {
        this.$message({
          message: `请和该区域批次下已排产同${this.comName}保持工序一致`,
          type: 'warning'
        })
      }
    },
    resetWorkTeamMax(row, str) {
      if (str) {
        row.Technology_Path = str
      } else {
        str = row.Technology_Path
      }
      const list = str?.split('/') || []
      this.workingTeam.forEach((element, idx) => {
        const cur = list.some(k => k === element.Process_Code)
        const code = this.getRowUnique(row.uuid, element.Process_Code, element.Working_Team_Id)
        const max = this.getRowUniqueMax(row.uuid, element.Process_Code, element.Working_Team_Id)
        if (cur) {
          if (!row[code]) {
            this.$set(row, code, 0)
            this.$set(row, max, row.Schduled_Count)
          }
        } else {
          this.$delete(row, code)
          this.$delete(row, max)
        }
      })
    },
    checkPermissionTeam(processStr, processCode) {
      if (!processStr) return false
      const list = processStr?.split('/') || []
      return !!list.some(v => v === processCode)
    },

    async getTableConfig(code) {
      await GetGridByCode({
        code
      }).then((res) => {
        const { IsSucceed, Data, Message } = res
        if (IsSucceed) {
          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)
          const list = Data.ColumnList || []
          this.ownerColumn = list.find(item => item.Code === 'Part_Used_Process')
          this.ownerColumn2 = list.find(item => item.Code === 'Is_Main_Part')
          this.columns = this.setColumnDisplay(list)
        } else {
          this.$message({
            message: Message,
            type: 'error'
          })
        }
      })
    },
    setColumnDisplay(list) {
      return list.filter(v => v.Is_Display)
      // .map(item => {
      //   if (FIX_COLUMN.includes(item.Code)) {
      //     item.fixed = 'left'
      //   }
      //   return item
      // })
    },
    activeCellMethod({ row, column, columnIndex }) {
      if (this.isView) return false
      const processCode = column.field?.split('$_$')[1]
      return this.checkPermissionTeam(row.Technology_Path, processCode)
    },
    openBPADialog(type, row) {
      if (this.workshopEnabled) {
        if (type === 1) {
          const IsUnique = this.checkIsUniqueWorkshop()
          if (!IsUnique) return
        }
      }
      this.title = type === 2 ? '工序调整' : '批量工序调整'
      this.currentComponent = 'BatchProcessAdjust'
      this.dWidth = this.isCom ? '60%' : '35%'
      this.dialogVisible = true
      this.$nextTick(_ => {
        this.$refs['content'].setData(type === 2 ? [row] : this.multipleSelection, type === 2 ? row.Technology_Path : '')
      })
    },
    checkIsUniqueWorkshop() {
      let isUnique = true
      const firstV = this.multipleSelection[0].Workshop_Name
      for (let i = 1; i < this.multipleSelection.length; i++) {
        const item = this.multipleSelection[i]
        if (item.Workshop_Name !== firstV) {
          isUnique = false
          break
        }
      }
      if (!isUnique) {
        this.$message({
          message: '批量分配工序时只有相同车间下的才可一起批量分配',
          type: 'warning'
        })
      }
      return isUnique
    },
    checkHasWorkShop(type, arr) {
      let hasWorkShop = true
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i]
        if (!item.Workshop_Name) {
          hasWorkShop = false
          break
        }
      }
      if (!hasWorkShop) {
        this.$message({
          message: '请先选择车间后再进行工序分配',
          type: 'warning'
        })
      }
      return hasWorkShop
    },
    handleAddDialog(type = 'add') {
      if (this.isCom) {
        this.title = `${this.comName}排产`
      } else {
        this.title = '添加零件'
      }
      this.currentComponent = 'AddDraft'
      this.dWidth = '80%'
      this.openAddDraft = true
      this.$nextTick(_ => {
        this.$refs['draft'].setPageData()
      })
    },
    getRowUnique(uuid, processCode, workingId) {
      return `${uuid}${SPLIT_SYMBOL}${processCode}${SPLIT_SYMBOL}${workingId}`
    },
    getRowUniqueMax(uuid, processCode, workingId) {
      return this.getRowUnique(uuid, processCode, workingId) + `${SPLIT_SYMBOL}max`
    },
    async handleSelectMenu(v) {
      if (v === 'process') {
        this.openBPADialog(1)
      } else if (v === 'deal') {
        await this.handleAutoDeal(1)
      } else if (v === 'craft') {
        await this.handleSetCraftProcess()
      }
    },
    async handleSetCraftProcess() {
      const showSuccess = () => {
        this.$message({
          message: '已分配成功',
          type: 'success'
        })
      }
      const rowList = this.multipleSelection.map(v => v.Technology_Code).filter(v => !!v)
      if (!rowList.length) {
        this.$message({
          message: '工艺代码不存在',
          type: 'warning'
        })
        return
      }
      await this.mergeCraftProcess(this.multipleSelection)
      const workshopIds = Array.from(new Set(this.multipleSelection.map(v => v.Workshop_Id).filter(v => !!v)))
      const w_process = []
      if (workshopIds.length) {
        workshopIds.forEach(workshopId => {
          w_process.push(this.getProcessOption(workshopId).then(result => ({
            [workshopId]: result
          })))
        })
        const workshopPromise = Promise.all(w_process).then((values) => {
          return Object.assign({}, ...values)
        })
        workshopPromise.then(workshop => {
          let flag = true
          let usedPartFlag = true
          for (let i = 0; i < this.multipleSelection.length; i++) {
            const curRow = this.multipleSelection[i]
            console.log('curRow', JSON.parse(JSON.stringify(curRow)))
            const workshopProcess = workshop[curRow.Workshop_Id]
            console.log('workshopProcess', workshopProcess)
            const craftArray = this.craftCodeMap[curRow.Technology_Code]
            console.log('craftArray', craftArray)

            if (craftArray) {
              const isIncluded = craftArray.every(process => workshopProcess.includes(process))
              if (!isIncluded) {
                flag = false
                continue
              }
              const hasUsedPart = this.checkHasCraftUsedPart(curRow, craftArray)
              if (hasUsedPart) {
                curRow.Technology_Path = craftArray.join('/')
              } else {
                usedPartFlag = false
              }
            }
          }
          if (!flag) {
            setTimeout(() => {
              this.$alert('所选车间下班组加工工序不包含工艺代码工序请手动排产', '提示', {
                confirmButtonText: '确定'
              })
            }, 200)
          }

          const isSuccess = this.showCraftUsedPartResult(usedPartFlag)
          flag && isSuccess && showSuccess()
        })
      } else {
        let usedPartFlag = true
        this.multipleSelection.forEach((curRow) => {
          const craftArray = this.craftCodeMap[curRow.Technology_Code]
          if (craftArray) {
            const hasUsedPart = this.checkHasCraftUsedPart(curRow, craftArray)
            if (hasUsedPart) {
              curRow.Technology_Path = craftArray.join('/')
            } else {
              usedPartFlag = false
            }
          }
        })
        const isSuccess = this.showCraftUsedPartResult(usedPartFlag)
        isSuccess && showSuccess()
      }
    },
    checkHasCraftUsedPart(curRow, craftArray) {
      if (!curRow.Part_Used_Process) return true
      const partUsedProcess = curRow.Part_Used_Process.split(',')
      const result = partUsedProcess.every(item => craftArray.includes(item))
      return result
      // return !(curRow.Part_Used_Process && !craftArray.includes(curRow.Part_Used_Process))
    },
    showCraftUsedPartResult(hasUsedPart) {
      if (hasUsedPart) return true
      setTimeout(() => {
        this.$alert(`部分${this.comName}工序路径内不包含零件领用工序请手动排产`, '提示', {
          confirmButtonText: '确定'
        })
      }, 200)
      return false
    },
    handleBatchOwner(type, row) {
      this.title = '批量分配领用工序'
      this.currentComponent = 'OwnerProcess'
      this.dWidth = '30%'
      this.dialogVisible = true
      this.$nextTick(_ => {
        this.$refs['content'].setOption(type === 2, type === 2 ? [row] : this.multipleSelection)
      })
    },
    handleReverse() {
      const cur = []
      this.tbData.forEach((element, idx) => {
        element.checked = !element.checked
        if (element.checked) {
          cur.push(element)
        }
      })
      this.multipleSelection = cur
      if (this.multipleSelection.length === this.tbData.length) {
        this.$refs['xTable'].setAllCheckboxRow(true)
      }
      if (this.multipleSelection.length === 0) {
        this.$refs['xTable'].setAllCheckboxRow(false)
      }
    },
    // tbFilterChange() {
    //   const xTable = this.$refs.xTable
    //   const column = xTable.getColumnByField('Type_Name')
    //   if (!column?.filters?.length) return
    //   column.filters.forEach(d => {
    //     d.checked = d.value === this.searchType
    //   })
    //   xTable.updateData()
    // },
    getType() {
      const getCompTree = () => {
        const fun = this.isCom ? GetCompTypeTree : GetPartTypeList
        fun({}).then(res => {
          if (res.IsSucceed) {
            let result = res.Data
            if (!this.isCom) {
              result = result
                .map((v, idx) => {
                  return {
                    Data: v.Name,
                    Label: v.Name
                  }
                })
            }
            this.treeParamsComponentType.data = result
            this.$nextTick((_) => {
              this.$refs.treeSelectComponentType?.treeDataUpdateFun(result)
            })
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      }

      getCompTree()
    },
    // 查看图纸
    handleDwg(row) {
      const obj = {}
      if (this.isCom) {
        obj.Comp_Id = row.Comp_Import_Detail_Id
      } else {
        obj.Part_Id = row.Part_Aggregate_Id
      }
      GetDwg(obj).then(res => {
        if (res.IsSucceed) {
          const fileurl = res?.Data?.length && res.Data[0].File_Url
          window.open('http://dwgv1.bimtk.com:5432/?CadUrl=' + parseOssUrl(fileurl), '_blank')
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    setProcessList(info) {
      this.changeProcessList(info)
    },
    resetInnerForm() {
      this.$refs['searchForm'].resetFields()
      this.$refs.xTable.clearFilter()
    },
    innerFilter() {
      this.multipleSelection = []
      const arr = []
      if (this.isCom) {
        arr.push('Type', 'Comp_Code', 'Spec', 'Is_Component')
      } else {
        arr.push('Part_Code', 'Spec', 'Type_Name')
      }

      const xTable = this.$refs.xTable
      xTable.clearCheckboxRow()
      arr.forEach((element, idx) => {
        const column = xTable.getColumnByField(element)
        if (element === 'Is_Component') {
          column.filters.forEach((option, idx) => {
            option.checked = idx === (this.innerForm.searchDirect ? 0 : 1)
          })
        }
        if (element === 'Spec') {
          const option = column.filters[0]
          option.data = this.innerForm.searchSpecSearch
          option.checked = true
        }
        if (element === 'Type' || element === 'Type_Name') {
          const option = column.filters[0]
          option.data = this.innerForm.searchComTypeSearch
          option.checked = true
        }
        if (element === 'Comp_Code' || element === 'Part_Code') {
          const option = column.filters[0]
          option.data = this.innerForm.searchContent
          option.checked = true
        }
      })
      xTable.updateData()
    },
    filterComponentMethod({ option, row }) {
      if (this.innerForm.searchDirect === '') {
        return true
      }
      return row.Is_Component === !this.innerForm.searchDirect
    },
    filterSpecMethod({ option, row }) {
      if (this.innerForm.searchSpecSearch.trim() === '') {
        return true
      }
      const splitAndClean = (input) => input.trim().replace(/\s+/g, ' ').split(' ')
      const specArray = splitAndClean(this.innerForm.searchSpecSearch)
      return specArray.some(code => (row.Spec || '').includes(code))
    },

    filterTypeMethod({ option, row }) {
      if (this.innerForm.searchComTypeSearch === '') {
        return true
      }
      const cur = this.isCom ? 'Type' : 'Type_Name'
      return row[cur] === this.innerForm.searchComTypeSearch
    },
    filterCodeMethod({ option, row }) {
      if (this.innerForm.searchContent.trim() === '') {
        return true
      }

      const splitAndClean = (input) => input.trim().replace(/\s+/g, ' ').split(' ')

      const cur = this.isCom ? 'Comp_Code' : 'Part_Code'

      const arr = splitAndClean(this.innerForm.searchContent)

      if (this.curSearch === 1) {
        return arr.some(code => row[cur] === code)
      } else {
        for (let i = 0; i < arr.length; i++) {
          const item = arr[i]
          if (row[cur].includes(item)) {
            return true
          }
        }
        return false
      }
    },
    componentTypeFilter(e) {
      this.$refs?.treeSelectComponentType.filterFun(e)
    },
    getInstallUnitIdNameList(id) {
      if (!this.areaId || this.isVersionFour) {
        this.installUnitIdList = []
        this.disabledAdd = false
      } else {
        this.disabledAdd = true
        GetInstallUnitIdNameList({ Area_Id: this.areaId }).then(res => {
          this.installUnitIdList = res.Data
          if (this.installUnitIdList.length) {
            this.formInline.InstallUnit_Id = this.installUnitIdList[0].Id
          }
          this.disabledAdd = false
        })
      }
    },
    installChange() {
      if (!this.tbData.length) {
        this.$refs['searchForm'].resetFields()
        this.$refs.xTable.clearFilter()
        return
      }
      this.$confirm('切换区域右侧数据清空, 是否确认?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tbData = []
        this.resetInnerForm()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    showPartUsedProcess(row) {
      if (this.isNest) {
        return !!row.Comp_Import_Detail_Id
      } else {
        return !this.isView && row.Type !== 'Direct'
      }
    },
    handleExport() {
      if (!this.tbData.length) {
        this.$message({
          message: '暂无数据',
          type: 'warning'
        })
        return
      }
      console.log(7, this.$refs.xTable)
      const item = this.tbData[0]
      this.$refs.xTable.exportData({
        filename: `${this.comName}排产-${item.Project_Name}-${item.Area_Name}-${this.formInline.Schduling_Code}(${this.comName})`,
        type: 'xlsx',
        data: this.tbData
      })
    }
  }
}
</script>
<style scoped lang="scss">
.flex-row {
  display: flex;

  .cs-left {
    background-color: #ffffff;
    margin-right: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .cs-tree-wrapper {
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      padding: 16px;

      .tree-search {
        display: flex;

        .search-select {
          margin-right: 8px;
        }
      }

      .el-tree {
        flex: 1;
        overflow: auto;
      }
    }
  }

  .cs-right {
    flex: 1;
    overflow: hidden;
  }
}

.pagination-container {
  padding: 0;
  text-align: right;
}

::v-deep .el-card__body {
  display: flex;
  flex-direction: column;
}

.tb-x {
  flex: 1;
  height: 0;
  margin-bottom: 10px;
  overflow: auto;
}

.topTitle {
  font-size: 14px;
  margin: 0 0 16px;

  span {
    display: inline-block;
    width: 2px;
    height: 14px;
    background: #009dff;
    vertical-align: middle;
    margin-right: 6px;
  }
}

::v-deep .elDivder {
  margin: 10px;
}

.btn-x {
  //margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}

.el-icon-edit {
  cursor: pointer;
}

footer {
  display: flex;
  justify-content: space-between;
}

.cs-bottom {
  position: relative;
  height: 40px;
  line-height: 40px;

  .data-info {
    position: absolute;
    bottom: 0;

    .info-x {
      margin-right: 20px;
    }
  }
}

.fourGreen {
  color: #00C361;
  font-style: normal;
}

.fourOrange {
  color: #FF9400;
  font-style: normal;
}

.fourRed {
  color: #FF0000;
  font-style: normal;
}

.cs-blue {
  color: #5AC8FA;
}
.cs-column-row{
  display: flex;
  align-items: center;

  .cs-ell{
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

  }
}
</style>

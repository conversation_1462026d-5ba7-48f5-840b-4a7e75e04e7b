{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\group\\component\\info.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\group\\component\\info.vue", "mtime": 1757468112139}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["info.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6EA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "info.vue", "sourceRoot": "src/views/PRO/basic-information/group/component", "sourcesContent": ["<template>\r\n  <div style=\"padding: 16px 0;\">\r\n    <el-form\r\n      ref=\"form\"\r\n      :model=\"form\"\r\n      inline\r\n      :rules=\"rules\"\r\n      label-width=\"130px\"\r\n      style=\"width: 100%\"\r\n    >\r\n      <h3>基本信息</h3>\r\n      <el-row>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"班组名称：\">\r\n            {{ form.Name || \"-\" }}\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"班组长：\">\r\n            {{ form.Manager_UserName || \"-\" }}\r\n          </el-form-item>\r\n        </el-col>\r\n        <!-- <el-form-item label=\"负荷提醒线：\">\r\n        {{ form.Load || \"-\" }}\r\n      </el-form-item> -->\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"排序号：\">\r\n            {{ form.Sort || \"-\" }}\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"班组月均负荷(t)：\">\r\n            {{ form.Month_Avg_Load || \"-\" }}\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"是否外协：\">\r\n            {{ form.Is_Outsource === true ? \"是\" : \"否\" }}\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"是否启用：\">\r\n            {{ form. Is_Enabled === true ? \"是\" : \"否\" }}\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row>\r\n        <el-col :span=\"16\">\r\n          <el-form-item label=\"关联仓库/库位：\">\r\n            {{ form.Warehouse_Name ? form.Warehouse_Name + '/' + form.Location_Name : \"-\" }}\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <el-form-item v-if=\"Is_Workshop_Enabled\" label=\"所属车间：\">\r\n            {{ form.Workshop_Name || \"-\" }}\r\n          </el-form-item>\r\n        </el-col></el-row>\r\n      <h3>班组成员</h3>\r\n      <div class=\"tag-x\">\r\n        <div class=\"tag-wrapper\">\r\n          <el-tag\r\n            v-for=\"tag in tags\"\r\n            :key=\"tag.User_Id\"\r\n            size=\"large\"\r\n            type=\"info\"\r\n          >\r\n            {{ tag.User_Name }}\r\n          </el-tag>\r\n        </div>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetWorkingTeamInfo } from '@/api/PRO/technology-lib'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      tags: [],\r\n      form: {\r\n        Name: '',\r\n        Manager_UserName: '',\r\n        Manager_UserId: '',\r\n        Load: '',\r\n        Workshop_Name: '',\r\n        Month_Avg_Load: null,\r\n        Sort: 0,\r\n        Is_Outsource: false,\r\n        Is_Enabled: true,\r\n        Warehouse_Id: '',\r\n        Location_Id: '',\r\n        Warehouse_Name: '',\r\n        Location_Name: ''\r\n      },\r\n      rules: {},\r\n\r\n      Is_Workshop_Enabled: ''\r\n    }\r\n  },\r\n  created() {\r\n  },\r\n  methods: {\r\n    initData(row, Is_Workshop_Enabled) {\r\n      GetWorkingTeamInfo({\r\n        id: row.Id\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const { Manager_UserName, Manager_UserId, Load, Name, Users, Month_Avg_Load, Sort, Is_Outsource, Is_Enabled, Warehouse_Name, Location_Name } =\r\n            res.Data\r\n          this.form.Manager_UserName = Manager_UserName\r\n          this.form.Manager_UserId = Manager_UserId\r\n          this.form.Load = Load\r\n          this.form.Name = Name\r\n          this.tags = Users\r\n          this.form.Workshop_Name = row.Workshop_Name\r\n          this.Is_Workshop_Enabled = Is_Workshop_Enabled\r\n          this.form.Month_Avg_Load = Month_Avg_Load\r\n          this.form.Sort = Sort\r\n          this.form.Is_Outsource = Is_Outsource\r\n          this.form.Is_Enabled = Is_Enabled\r\n          this.form.Warehouse_Name = Warehouse_Name\r\n          this.form.Location_Name = Location_Name\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"~@/styles/mixin.scss\";\r\n\r\nh3 {\r\n  color: #298dff;\r\n}\r\n.tag-x {\r\n  text-align: left;\r\n  .tag-wrapper {\r\n    display: inline-block;\r\n    flex-wrap: wrap;\r\n    height: 160px;\r\n    overflow: auto;\r\n    @include scrollBar;\r\n    .el-tag {\r\n      margin: 8px 0 0 8px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
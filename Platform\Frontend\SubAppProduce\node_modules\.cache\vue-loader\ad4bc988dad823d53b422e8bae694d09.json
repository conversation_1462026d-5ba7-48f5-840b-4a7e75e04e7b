{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\com-config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\com-config\\index.vue", "mtime": 1757468112228}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRUYWJsZVNldHRpbmdMaXN0LCBVcGRhdGVDb21wb25lbnRQYXJ0VGFibGVTZXR0aW5nLCBVcGRhdGVDb2x1bW5TZXR0aW5nIH0gZnJvbSAnQC9hcGkvUFJPL2NvbXBvbmVudC10eXBlJw0KaW1wb3J0IHsgY2xvc2VUYWdWaWV3IH0gZnJvbSAnQC91dGlscycNCmltcG9ydCB7IEdldEJPTUluZm8gfSBmcm9tICdAL3ZpZXdzL1BSTy9ib20tc2V0dGluZy91dGlscycNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ1BST0NvbXBvbmVudENvbmZpZycsDQogIGNvbXBvbmVudHM6IHsNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgYWN0aXZlTmFtZUFwaTogJ3BsbV9jb21wb25lbnRfZmllbGRfcGFnZV9saXN0JywNCiAgICAgIGFjdGl2ZU5hbWU6ICdwbG1fY29tcG9uZW50X2ZpZWxkX3BhZ2VfbGlzdCcsDQogICAgICBjdXJyZW50Q29kZTogJ3BsbV9jb21wb25lbnRfcGFnZV9saXN0JywNCiAgICAgIHR5cGVDb2RlOiAnJywNCiAgICAgIG1hdGVyaWFsQ29kZTogJycsDQogICAgICBjdXJyZW50RmluYWxUeXBlQ29kZTogJycsDQogICAgICB0YWJQb3NpdGlvbjogJ2xlZnQnLA0KICAgICAgLy8gdGFiTGlzdDogWw0KICAgICAgLy8gICB7DQogICAgICAvLyAgICAgbGFiZWw6ICfmnoTku7blrZfmrrXnu7TmiqQnLA0KICAgICAgLy8gICAgIHZhbHVlOiAncGxtX2NvbXBvbmVudF9maWVsZF9wYWdlX2xpc3QnDQogICAgICAvLyAgIH0sDQogICAgICAvLyAgIHsNCiAgICAgIC8vICAgICBsYWJlbDogJ+aehOS7tueuoeeQhuWIl+ihqCcsDQogICAgICAvLyAgICAgdmFsdWU6ICdwbG1fY29tcG9uZW50X3BhZ2VfbGlzdCcNCiAgICAgIC8vICAgfSwNCiAgICAgIC8vICAgew0KICAgICAgLy8gICAgIGxhYmVsOiAn5p6E5Lu25rex5YyW5riF5Y2VJywNCiAgICAgIC8vICAgICB2YWx1ZTogJ3BsbV9jb21wb25lbnRfZGV0YWlsSW1wb3J0Jw0KICAgICAgLy8gICB9LA0KICAgICAgLy8gICB7DQogICAgICAvLyAgICAgbGFiZWw6ICfmnoTku7bmqKHlnovmuIXljZUnLA0KICAgICAgLy8gICAgIHZhbHVlOiAncGxtX2NvbXBvbmVudF9tb2RlbEltcG9ydCcNCiAgICAgIC8vICAgfSwNCiAgICAgIC8vICAgew0KICAgICAgLy8gICAgIGxhYmVsOiAn55Sf5Lqn6K+m5oOF5YiX6KGoJywNCiAgICAgIC8vICAgICB2YWx1ZTogJ3BsbV9jb21wb25lbnRfcHJvZHVjZURldGFpbCcNCiAgICAgIC8vICAgfSwNCiAgICAgIC8vICAgew0KICAgICAgLy8gICAgIGxhYmVsOiAn5omT5YyF5qih5p2/JywNCiAgICAgIC8vICAgICB2YWx1ZTogJ3BsbV9jb21wb25lbnRfcGFja2FnZVRlbXBsYXRlJw0KICAgICAgLy8gICB9LA0KICAgICAgLy8gICB7DQogICAgICAvLyAgICAgbGFiZWw6ICfmqKHlnovlrZfmrrXlr7nnhafooagnLA0KICAgICAgLy8gICAgIHZhbHVlOiAncGxtX2NvbXBvbmVudF9tb2RlbEZpZWxkJw0KICAgICAgLy8gICB9DQogICAgICAvLyBdLA0KICAgICAgc2VhcmNoVmFsOiAnJywNCiAgICAgIG1ham9yTmFtZTogJycsDQogICAgICBjb21OYW1lOiAn5p6E5Lu2JywNCiAgICAgIHVuaXQ6ICcnLA0KICAgICAgc3RlZWxVbml0OiAnJywNCiAgICAgIHRlbXBsYXRlTGlzdDogW10sDQogICAgICB0ZW1wbGF0ZUxpc3ROZXc6IFtdLA0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBzeXN0ZW1GaWVsZDogZmFsc2UsDQogICAgICBleHBhbmRGaWVsZDogZmFsc2UsDQogICAgICBidXNpbmVzc0ZpZWxkOiBmYWxzZQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICB0YWJMaXN0KCkgew0KICAgICAgcmV0dXJuIFsNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiB0aGlzLmNvbU5hbWUgKyAn5a2X5q6157u05oqkJywNCiAgICAgICAgICB2YWx1ZTogJ3BsbV9jb21wb25lbnRfZmllbGRfcGFnZV9saXN0Jw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6IHRoaXMuY29tTmFtZSArICfnrqHnkIbliJfooagnLA0KICAgICAgICAgIHZhbHVlOiAncGxtX2NvbXBvbmVudF9wYWdlX2xpc3QnDQogICAgICAgIH0sDQogICAgICAgIC8vIHsNCiAgICAgICAgLy8gICBsYWJlbDogdGhpcy5jb21OYW1lICsgJ+a3seWMlua4heWNlScsDQogICAgICAgIC8vICAgdmFsdWU6ICdwbG1fY29tcG9uZW50X2RldGFpbEltcG9ydCcNCiAgICAgICAgLy8gfSwNCiAgICAgICAgLy8gew0KICAgICAgICAvLyAgIGxhYmVsOiB0aGlzLmNvbU5hbWUgKyAn5qih5Z6L5riF5Y2VJywNCiAgICAgICAgLy8gICB2YWx1ZTogJ3BsbV9jb21wb25lbnRfbW9kZWxJbXBvcnQnDQogICAgICAgIC8vIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogJ+eUn+S6p+ivpuaDheWIl+ihqCcsDQogICAgICAgICAgdmFsdWU6ICdwbG1fY29tcG9uZW50X3Byb2R1Y2VEZXRhaWwnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogJ+aJk+WMheaooeadvycsDQogICAgICAgICAgdmFsdWU6ICdwbG1fY29tcG9uZW50X3BhY2thZ2VUZW1wbGF0ZScNCiAgICAgICAgfQ0KICAgICAgICAvLyB7DQogICAgICAgIC8vICAgbGFiZWw6ICfmqKHlnovlrZfmrrXlr7nnhafooagnLA0KICAgICAgICAvLyAgIHZhbHVlOiAncGxtX2NvbXBvbmVudF9tb2RlbEZpZWxkJw0KICAgICAgICAvLyB9DQogICAgICBdDQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMudHlwZUNvZGUgPSB0aGlzLiRyb3V0ZS5xdWVyeS50eXBlQ29kZSB8fCAnU3RlZWwnDQogICAgdGhpcy5tYXRlcmlhbENvZGUgPSB0aGlzLiRyb3V0ZS5xdWVyeS5tYXRlcmlhbENvZGUgfHwgJ1N0cnVjdHVyYWxBcycNCiAgICB0aGlzLmN1cnJlbnRGaW5hbFR5cGVDb2RlID0gdGhpcy50eXBlQ29kZQ0KICB9LA0KICBtb3VudGVkKCkgew0KICAgIHRoaXMubWFqb3JOYW1lID0gdGhpcy4kcm91dGUucXVlcnkubmFtZSB8fCAn6ZKi57uT5p6EJw0KICAgIHRoaXMudW5pdCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LnVuaXQgfHwgJ3QnDQogICAgdGhpcy5zdGVlbFVuaXQgPSB0aGlzLiRyb3V0ZS5xdWVyeS5zdGVlbF91bml0IHx8ICdrZycNCiAgICB0aGlzLkdldFRhYmxlU2V0dGluZ0xpc3RGbigpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBjaGFuZ2VTdGF0dXMoJGV2ZW50LCBpZCkgew0KICAgICAgY29uc3QgZGlzcGxheU5hbWUgPSB0aGlzLnRlbXBsYXRlTGlzdC5maW5kKChpdGVtKSA9PiB7IHJldHVybiBpdGVtLklkID09IGlkIH0pLkRpc3BsYXlfTmFtZQ0KICAgICAgaWYgKGRpc3BsYXlOYW1lID09ICcnICYmICRldmVudCA9PSB0cnVlKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UoeyB0eXBlOiAnZXJyb3InLCBtZXNzYWdlOiAn6K+35YWI5aGr5YaZ5a2X5q615ZCNJyB9KQ0KICAgICAgICB0aGlzLnRlbXBsYXRlTGlzdC5tYXAoKGl0ZW0pID0+IHsNCiAgICAgICAgICBpZiAoaXRlbS5JZCA9PSBpZCkgew0KICAgICAgICAgICAgaXRlbS5Jc19FbmFibGVkID0gZmFsc2UNCiAgICAgICAgICB9DQogICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZUNsaWNrKHRhYiwgZXZlbnQpIHsNCiAgICAgIHRoaXMuY3VycmVudENvZGUgPSB0YWIubmFtZQ0KICAgICAgdGhpcy5HZXRUYWJsZVNldHRpbmdMaXN0Rm4oKQ0KICAgIH0sDQoNCiAgICBzZWFyY2hWYWx1ZSgpIHsNCiAgICAgIGlmICghdGhpcy5zZWFyY2hWYWwpIHsNCiAgICAgICAgdGhpcy50ZW1wbGF0ZUxpc3ROZXcgPSB0aGlzLnRlbXBsYXRlTGlzdA0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgY29uc3QgZmlsdGVyTGlzdCA9IFtdDQogICAgICAgIHRoaXMudGVtcGxhdGVMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICBpZiAoaXRlbS5EaXNwbGF5X05hbWUuc2VhcmNoKG5ldyBSZWdFeHAodGhpcy5zZWFyY2hWYWwsICdpZycpKSAhPT0gLTEpIHsNCiAgICAgICAgICAgIGZpbHRlckxpc3QucHVzaChpdGVtKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgdGhpcy50ZW1wbGF0ZUxpc3ROZXcgPSBmaWx0ZXJMaXN0DQogICAgICB9DQogICAgfSwNCg0KICAgIHNhdmVNb2RpZnlDaGFuZ2VzRm4oKSB7DQogICAgICBpZiAodGhpcy5hY3RpdmVOYW1lID09IHRoaXMuYWN0aXZlTmFtZUFwaSkgew0KICAgICAgICB0aGlzLlVwZGF0ZUNvbXBvbmVudFBhcnRUYWJsZVNldHRpbmcoKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5VcGRhdGVDb2x1bW5TZXR0aW5nKCkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgYXN5bmMgVXBkYXRlQ29sdW1uU2V0dGluZygpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IFVwZGF0ZUNvbHVtblNldHRpbmcodGhpcy50ZW1wbGF0ZUxpc3QpDQogICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7IHR5cGU6ICdzdWNjZXNzJywgbWVzc2FnZTogJ+S/neWtmOaIkOWKnycgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UoeyB0eXBlOiAnZXJyb3InLCBtZXNzYWdlOiByZXMuTWVzc2FnZSB9KQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBhc3luYyBVcGRhdGVDb21wb25lbnRQYXJ0VGFibGVTZXR0aW5nKCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQ0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgVXBkYXRlQ29tcG9uZW50UGFydFRhYmxlU2V0dGluZyh0aGlzLnRlbXBsYXRlTGlzdCkNCiAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsgdHlwZTogJ3N1Y2Nlc3MnLCBtZXNzYWdlOiAn5L+d5a2Y5oiQ5YqfJyB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7IHR5cGU6ICdlcnJvcicsIG1lc3NhZ2U6IHJlcy5NZXNzYWdlIH0pDQogICAgICB9DQogICAgfSwNCg0KICAgIGFzeW5jIEdldFRhYmxlU2V0dGluZ0xpc3RGbigpIHsNCiAgICAgIGxldCBkYXRhID0ge30NCiAgICAgIGlmICh0aGlzLmFjdGl2ZU5hbWUgPT0gdGhpcy5hY3RpdmVOYW1lQXBpKSB7DQogICAgICAgIGRhdGEgPSB7IElzQ29tcG9uZW50OiB0cnVlLCBQcm9mZXNzaW9uYWxDb2RlOiB0aGlzLmN1cnJlbnRGaW5hbFR5cGVDb2RlIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGRhdGEgPSB7IElzQ29tcG9uZW50OiB0cnVlLCBQcm9mZXNzaW9uYWxDb2RlOiB0aGlzLmN1cnJlbnRGaW5hbFR5cGVDb2RlLCBUeXBlQ29kZTogdGhpcy5jdXJyZW50Q29kZSArICcsJyArIHRoaXMuY3VycmVudEZpbmFsVHlwZUNvZGUgfQ0KICAgICAgfQ0KICAgICAgZGF0YS5MZXZlbCA9IC0xDQogICAgICBjb25zdCB7IGNvbU5hbWUgfSA9IGF3YWl0IEdldEJPTUluZm8oKQ0KICAgICAgdGhpcy5jb21OYW1lID0gY29tTmFtZQ0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0VGFibGVTZXR0aW5nTGlzdChkYXRhKQ0KICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgdGhpcy50ZW1wbGF0ZUxpc3QgPSByZXMuRGF0YSB8fCBbXQ0KICAgICAgICBpZiAodGhpcy50ZW1wbGF0ZUxpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoaXMudGVtcGxhdGVMaXN0LmZvckVhY2godiA9PiB7DQogICAgICAgICAgICBpZiAodi5Db2RlID09PSAnU3RlZWxOYW1lJykgew0KICAgICAgICAgICAgICB2LkRpc3BsYXlfTmFtZSA9IGNvbU5hbWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICAgIHRoaXMudGVtcGxhdGVMaXN0TmV3ID0gdGhpcy50ZW1wbGF0ZUxpc3QNCiAgICAgICAgdGhpcy5zeXN0ZW1GaWVsZCA9IHRoaXMudGVtcGxhdGVMaXN0LnNvbWUoaXRlbSA9PiB7IHJldHVybiBpdGVtLkNvbHVtbl9UeXBlID09IDAgfSkNCiAgICAgICAgdGhpcy5leHBhbmRGaWVsZCA9IHRoaXMudGVtcGxhdGVMaXN0LnNvbWUoaXRlbSA9PiB7IHJldHVybiBpdGVtLkNvbHVtbl9UeXBlID09IDEgfSkNCiAgICAgICAgdGhpcy5idXNpbmVzc0ZpZWxkID0gdGhpcy50ZW1wbGF0ZUxpc3Quc29tZShpdGVtID0+IHsgcmV0dXJuIGl0ZW0uQ29sdW1uX1R5cGUgPT0gMiB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7IHR5cGU6ICdlcnJvcicsIG1lc3NhZ2U6IHJlcy5NZXNzYWdlIH0pDQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/bom-setting/com-config", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"page-container\">\r\n      <div class=\"top-wrapper\">\r\n        <!-- <div class=\"title\">专业模板配置：</div> -->\r\n        <div class=\"info\">\r\n          <template v-if=\"!!majorName\">\r\n            <div class=\"title\">当前专业：</div>\r\n            <div class=\"value\">{{ majorName }}</div>\r\n          </template>\r\n          <template v-if=\"!!unit\">\r\n            <div class=\"title\">统计单位：</div>\r\n            <div class=\"value\">{{ unit }}</div>\r\n          </template>\r\n          <template v-if=\"!!steelUnit\">\r\n            <div class=\"title\">构件单位：</div>\r\n            <div class=\"value\">{{ steelUnit }}</div>\r\n          </template>\r\n          <template>\r\n            <div class=\"title\">单位统计字段：</div>\r\n            <div v-for=\"(item,index) in templateListNew\" v-show=\"item.Code==='SteelAmount'\" :key=\"index\" style=\"display: flex;flex-direction: row\">\r\n              {{ item.Display_Name }}\r\n            </div>\r\n            <div v-for=\"(item,index) in templateListNew\" v-show=\"item.Code==='SteelWeight'\" :key=\"index+999\" style=\"display: flex;flex-direction: row\">\r\n              *{{ item.Display_Name }}\r\n            </div>\r\n          </template>\r\n        </div>\r\n        <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\r\n          <el-tab-pane v-for=\"(item,index) in tabList\" :key=\"comName+index\" :label=\"item.label\" :name=\"item.value\" />\r\n\r\n        </el-tabs>\r\n      </div>\r\n      <div class=\"content-wrapper\" style=\"min-height: calc(100vh - 340px)\">\r\n        <div class=\"right-c\">\r\n          <el-row type=\"flex\" justify=\"space-between\">\r\n            <div class=\"right-c-title\">\r\n              <div class=\"setting-title\">{{ systemField==true ? '系统字段' : businessField==true ? '业务字段' : expandField==true ? '拓展字段' : '' }}</div>\r\n            </div>\r\n            <div style=\"display: flex;flex-direction: row\">\r\n              <span style=\"width:140px;font-size:12px;height:32px; line-height: 32px;display: inline-block;\">字段名称：</span>\r\n              <el-input v-model=\"searchVal\" placeholder=\"请输入字段名称\" clearable />\r\n              <el-button type=\"primary\" style=\"margin-left: 10px\" @click=\"searchValue\">查询</el-button>\r\n              <el-button type=\"primary\" :loading=\"loading\" @click=\"saveModifyChangesFn\">保存设置</el-button>\r\n            </div>\r\n          </el-row>\r\n          <el-form label-width=\"120px\" style=\"margin-top: 24px\">\r\n            <template v-for=\"(item,index) in templateListNew\">\r\n              <el-row v-if=\"item.Column_Type==0\" :key=\"index\">\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"item.Code\" label-width=\"150px\">\r\n                    <el-input v-model=\"item.Display_Name\" :disabled=\"false\" style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"'备注说明'\">\r\n                    <el-input v-model=\"item.Remark\" disabled style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"排序\">\r\n                    <el-input v-model=\"item.Sort\" style=\"width: 100px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"是否显示\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </template>\r\n            <div v-show=\"businessField==true && systemField==true\" class=\"setting-title\">业务字段</div>\r\n            <template v-for=\"(item,index) in templateListNew\">\r\n              <el-row v-if=\"item.Column_Type==2\" :key=\"index\">\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"item.Code\" label-width=\"150px\">\r\n                    <el-input v-model=\"item.Display_Name\" style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"'备注说明'\">\r\n                    <el-input v-model=\"item.Remark\" disabled style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"排序\">\r\n                    <el-input v-model=\"item.Sort\" style=\"width: 100px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName==activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"是否启用\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-else :span=\"4\">\r\n                  <el-form-item label=\"是否显示\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </template>\r\n            <div v-show=\"expandField==true\" class=\"setting-title\">拓展字段</div>\r\n            <template v-for=\"(item,index) in templateListNew\">\r\n              <el-row v-if=\"item.Column_Type==1\" :key=\"index\">\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"item.Code\" label-width=\"150px\">\r\n                    <el-input v-model=\"item.Display_Name\" style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"'备注说明'\">\r\n                    <el-input v-model=\"item.Remark\" disabled style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"排序\">\r\n                    <el-input v-model=\"item.Sort\" style=\"width: 100px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName==activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"是否启用\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-else :span=\"4\">\r\n                  <el-form-item label=\"是否显示\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </template>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { GetTableSettingList, UpdateComponentPartTableSetting, UpdateColumnSetting } from '@/api/PRO/component-type'\r\nimport { closeTagView } from '@/utils'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\nexport default {\r\n  name: 'PROComponentConfig',\r\n  components: {\r\n  },\r\n  data() {\r\n    return {\r\n      activeNameApi: 'plm_component_field_page_list',\r\n      activeName: 'plm_component_field_page_list',\r\n      currentCode: 'plm_component_page_list',\r\n      typeCode: '',\r\n      materialCode: '',\r\n      currentFinalTypeCode: '',\r\n      tabPosition: 'left',\r\n      // tabList: [\r\n      //   {\r\n      //     label: '构件字段维护',\r\n      //     value: 'plm_component_field_page_list'\r\n      //   },\r\n      //   {\r\n      //     label: '构件管理列表',\r\n      //     value: 'plm_component_page_list'\r\n      //   },\r\n      //   {\r\n      //     label: '构件深化清单',\r\n      //     value: 'plm_component_detailImport'\r\n      //   },\r\n      //   {\r\n      //     label: '构件模型清单',\r\n      //     value: 'plm_component_modelImport'\r\n      //   },\r\n      //   {\r\n      //     label: '生产详情列表',\r\n      //     value: 'plm_component_produceDetail'\r\n      //   },\r\n      //   {\r\n      //     label: '打包模板',\r\n      //     value: 'plm_component_packageTemplate'\r\n      //   },\r\n      //   {\r\n      //     label: '模型字段对照表',\r\n      //     value: 'plm_component_modelField'\r\n      //   }\r\n      // ],\r\n      searchVal: '',\r\n      majorName: '',\r\n      comName: '构件',\r\n      unit: '',\r\n      steelUnit: '',\r\n      templateList: [],\r\n      templateListNew: [],\r\n      loading: false,\r\n      systemField: false,\r\n      expandField: false,\r\n      businessField: false\r\n    }\r\n  },\r\n  computed: {\r\n    tabList() {\r\n      return [\r\n        {\r\n          label: this.comName + '字段维护',\r\n          value: 'plm_component_field_page_list'\r\n        },\r\n        {\r\n          label: this.comName + '管理列表',\r\n          value: 'plm_component_page_list'\r\n        },\r\n        // {\r\n        //   label: this.comName + '深化清单',\r\n        //   value: 'plm_component_detailImport'\r\n        // },\r\n        // {\r\n        //   label: this.comName + '模型清单',\r\n        //   value: 'plm_component_modelImport'\r\n        // },\r\n        {\r\n          label: '生产详情列表',\r\n          value: 'plm_component_produceDetail'\r\n        },\r\n        {\r\n          label: '打包模板',\r\n          value: 'plm_component_packageTemplate'\r\n        }\r\n        // {\r\n        //   label: '模型字段对照表',\r\n        //   value: 'plm_component_modelField'\r\n        // }\r\n      ]\r\n    }\r\n  },\r\n  created() {\r\n    this.typeCode = this.$route.query.typeCode || 'Steel'\r\n    this.materialCode = this.$route.query.materialCode || 'StructuralAs'\r\n    this.currentFinalTypeCode = this.typeCode\r\n  },\r\n  mounted() {\r\n    this.majorName = this.$route.query.name || '钢结构'\r\n    this.unit = this.$route.query.unit || 't'\r\n    this.steelUnit = this.$route.query.steel_unit || 'kg'\r\n    this.GetTableSettingListFn()\r\n  },\r\n  methods: {\r\n    changeStatus($event, id) {\r\n      const displayName = this.templateList.find((item) => { return item.Id == id }).Display_Name\r\n      if (displayName == '' && $event == true) {\r\n        this.$message({ type: 'error', message: '请先填写字段名' })\r\n        this.templateList.map((item) => {\r\n          if (item.Id == id) {\r\n            item.Is_Enabled = false\r\n          }\r\n          return item\r\n        })\r\n      }\r\n    },\r\n    handleClick(tab, event) {\r\n      this.currentCode = tab.name\r\n      this.GetTableSettingListFn()\r\n    },\r\n\r\n    searchValue() {\r\n      if (!this.searchVal) {\r\n        this.templateListNew = this.templateList\r\n      } else {\r\n        const filterList = []\r\n        this.templateList.map(item => {\r\n          if (item.Display_Name.search(new RegExp(this.searchVal, 'ig')) !== -1) {\r\n            filterList.push(item)\r\n          }\r\n        })\r\n        this.templateListNew = filterList\r\n      }\r\n    },\r\n\r\n    saveModifyChangesFn() {\r\n      if (this.activeName == this.activeNameApi) {\r\n        this.UpdateComponentPartTableSetting()\r\n      } else {\r\n        this.UpdateColumnSetting()\r\n      }\r\n    },\r\n\r\n    async UpdateColumnSetting() {\r\n      this.loading = true\r\n      const res = await UpdateColumnSetting(this.templateList)\r\n      this.loading = false\r\n      if (res.IsSucceed) {\r\n        this.$message({ type: 'success', message: '保存成功' })\r\n      } else {\r\n        this.$message({ type: 'error', message: res.Message })\r\n      }\r\n    },\r\n\r\n    async UpdateComponentPartTableSetting() {\r\n      this.loading = true\r\n      const res = await UpdateComponentPartTableSetting(this.templateList)\r\n      this.loading = false\r\n      if (res.IsSucceed) {\r\n        this.$message({ type: 'success', message: '保存成功' })\r\n      } else {\r\n        this.$message({ type: 'error', message: res.Message })\r\n      }\r\n    },\r\n\r\n    async GetTableSettingListFn() {\r\n      let data = {}\r\n      if (this.activeName == this.activeNameApi) {\r\n        data = { IsComponent: true, ProfessionalCode: this.currentFinalTypeCode }\r\n      } else {\r\n        data = { IsComponent: true, ProfessionalCode: this.currentFinalTypeCode, TypeCode: this.currentCode + ',' + this.currentFinalTypeCode }\r\n      }\r\n      data.Level = -1\r\n      const { comName } = await GetBOMInfo()\r\n      this.comName = comName\r\n      const res = await GetTableSettingList(data)\r\n      if (res.IsSucceed) {\r\n        this.templateList = res.Data || []\r\n        if (this.templateList.length > 0) {\r\n          this.templateList.forEach(v => {\r\n            if (v.Code === 'SteelName') {\r\n              v.Display_Name = comName\r\n            }\r\n          })\r\n        }\r\n        this.templateListNew = this.templateList\r\n        this.systemField = this.templateList.some(item => { return item.Column_Type == 0 })\r\n        this.expandField = this.templateList.some(item => { return item.Column_Type == 1 })\r\n        this.businessField = this.templateList.some(item => { return item.Column_Type == 2 })\r\n      } else {\r\n        this.$message({ type: 'error', message: res.Message })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n  .page-container{\r\n    margin:16px;\r\n    box-sizing: border-box;\r\n    .top-wrapper{\r\n      background: #fff;\r\n      padding:16px;\r\n      box-sizing: border-box;\r\n      .title{\r\n        font-size: 16px;\r\n        font-weight: 500;\r\n        color:#333333;\r\n      }\r\n      .info{\r\n        font-size: 14px;\r\n        margin:8px 0 24px 0;\r\n        display: flex;\r\n        flex-direction: row;\r\n        .title{\r\n          font-size: 14px;\r\n          color: #999999;\r\n        }\r\n        .value{\r\n          color: #333333;\r\n          margin-right: 24px;\r\n        }\r\n      }\r\n    }\r\n    .content-wrapper{\r\n      margin-top:16px;\r\n      display: flex;\r\n      flex-direction: row;\r\n      .left-c{\r\n        width: 160px;\r\n        background: #fff;\r\n        margin-right: 16px;\r\n      }\r\n      .right-c{\r\n        background: #fff;\r\n        width: 100%;\r\n        padding: 16px 24px;\r\n        box-sizing: border-box;\r\n        .right-c-title .setting-title {\r\n          margin: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  // ::v-deep.el-tabs--left .el-tabs__nav-wrap.is-left::after{\r\n  //   left:0\r\n  // }\r\n  // ::v-deep.el-tabs--left .el-tabs__active-bar.is-left{\r\n  //   left: 0;\r\n  // }\r\n  .setting-title {\r\n    font-weight: 400;\r\n    color: #1f2f3d;\r\n    margin: 30px 0 20px;\r\n    font-size: 22px;\r\n  }\r\n</style>\r\n"]}]}
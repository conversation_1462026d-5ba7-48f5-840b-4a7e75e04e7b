{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\nesting-management\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\nesting-management\\index.vue", "mtime": 1757468127975}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgUGFnaW5hdGlvbiBmcm9tICdAL2NvbXBvbmVudHMvUGFnaW5hdGlvbi9pbmRleC52dWUnDQppbXBvcnQgZ2V0VGJJbmZvIGZyb20gJ0AvbWl4aW5zL1BSTy9nZXQtdGFibGUtaW5mbycNCmltcG9ydCB7IHRhYmxlUGFnZVNpemUgfSBmcm9tICdAL3ZpZXdzL1BSTy9zZXR0aW5nJw0KaW1wb3J0IGFkZFJvdXRlclBhZ2UgZnJvbSAnQC9taXhpbnMvYWRkLXJvdXRlci1wYWdlJw0KaW1wb3J0IHsgRGVsZXRlTmVzdGluZ1Jlc3VsdCwgR2V0TmVzdGluZ1Jlc3VsdFBhZ2VMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL3Byb2R1Y3Rpb24tdGFzaycNCmltcG9ydCBTdXJwbHVzUmF3IGZyb20gJy4vY29tcG9uZW50cy9TdXJwbHVzUmF3LnZ1ZScNCmltcG9ydCBOZXN0UmVwb3J0IGZyb20gJy4vY29tcG9uZW50cy9OZXN0UmVwb3J0LnZ1ZScNCmltcG9ydCBOZXN0UmVzdWx0IGZyb20gJy4vY29tcG9uZW50cy9OZXN0UmVzdWx0LnZ1ZScNCmltcG9ydCBOZXN0VGh1bWJzIGZyb20gJy4vY29tcG9uZW50cy9OZXN0VGh1bWJzLnZ1ZScNCmltcG9ydCBQYXJ0c0xheW91dCBmcm9tICcuL2NvbXBvbmVudHMvUGFydHNMYXlvdXQudnVlJw0KaW1wb3J0IHsgbWFwQWN0aW9ucyB9IGZyb20gJ3Z1ZXgnDQppbXBvcnQgRHluYW1pY1RhYmxlRmllbGRzIGZyb20gJ0AvY29tcG9uZW50cy9EeW5hbWljVGFibGVGaWVsZHMvaW5kZXgudnVlJw0KaW1wb3J0IHsgRXhwb3J0UHJvY2VzcyB9IGZyb20gJ0AvYXBpL1BSTy9tYXRlcmlhbE1hbmFnZW1lbnQnDQppbXBvcnQgeyBjb21iaW5lVVJMIH0gZnJvbSAnQC91dGlscycNCmltcG9ydCBPU1NVcGxvYWQgZnJvbSAnQC92aWV3cy9wbG0vY29tcG9uZW50cy9vc3N1cGxvYWQudnVlJw0KaW1wb3J0IHsgZ2V0VG9rZW4gfSBmcm9tICdAL3V0aWxzL2F1dGgnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ1BST05lc3RpbmdNYW5hZ2VtZW50JywNCiAgY29tcG9uZW50czogeyBPU1NVcGxvYWQsIER5bmFtaWNUYWJsZUZpZWxkcywgUGFnaW5hdGlvbiwgU3VycGx1c1JhdywgTmVzdFJlcG9ydCwgTmVzdFJlc3VsdCwgUGFydHNMYXlvdXQsIE5lc3RUaHVtYnMgfSwNCiAgbWl4aW5zOiBbZ2V0VGJJbmZvLCBhZGRSb3V0ZXJQYWdlXSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgYWRkUGFnZUFycmF5OiBbDQogICAgICAgIHsNCiAgICAgICAgICBwYXRoOiAnL21hdGVyaWFsL3BpY2svYWRkJywNCiAgICAgICAgICBoaWRkZW46IHRydWUsDQogICAgICAgICAgY29tcG9uZW50OiAoKSA9PiBpbXBvcnQoJ0Avdmlld3MvUFJPL21hdGVyaWFsX3Y0L3BpY2tBcHBseS9hZGQudnVlJyksDQogICAgICAgICAgbmFtZTogJ0FkZE1hdGVyaWFsUGlja0xpc3QnLA0KICAgICAgICAgIG1ldGE6IHsgdGl0bGU6ICfmlrDlop7poobmlpnljZUnIH0sDQogICAgICAgICAgcXVlcnk6IHsgcGdfcmVkaXJlY3Q6IHRoaXMuJHJvdXRlLm5hbWUgfQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgcGF0aDogdGhpcy4kcm91dGUucGF0aCArICcvcmVxdWlzaXRpb24nLA0KICAgICAgICAgIGhpZGRlbjogdHJ1ZSwNCiAgICAgICAgICBjb21wb25lbnQ6ICgpID0+IGltcG9ydCgnQC92aWV3cy9QUk8vbmVzdGluZy1tYW5hZ2VtZW50L3JlcXVpc2l0aW9uLnZ1ZScpLA0KICAgICAgICAgIG5hbWU6ICdNb2RlbENvbXBhcmUnLA0KICAgICAgICAgIG1ldGE6IHsgdGl0bGU6ICfpoobmlpnljZUnIH0NCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHBhdGg6IHRoaXMuJHJvdXRlLnBhdGggKyAnL3NjaGVkdWxlJywNCiAgICAgICAgICBoaWRkZW46IHRydWUsDQogICAgICAgICAgY29tcG9uZW50OiAoKSA9PiBpbXBvcnQoJ0Avdmlld3MvUFJPL25lc3RpbmctbWFuYWdlbWVudC9zY2hlZHVsZS52dWUnKSwNCiAgICAgICAgICBuYW1lOiAnUFJPTmVzdGluZ1NjaGVkdWxlJywNCiAgICAgICAgICBtZXRhOiB7IHRpdGxlOiAn5LiL5Y+R5o6S5Lqn5Lu75YqhJyB9DQogICAgICAgIH0sIHsNCiAgICAgICAgICBwYXRoOiB0aGlzLiRyb3V0ZS5wYXRoICsgJy9kcmFmdCcsDQogICAgICAgICAgaGlkZGVuOiB0cnVlLA0KICAgICAgICAgIGNvbXBvbmVudDogKCkgPT4gaW1wb3J0KCdAL3ZpZXdzL1BSTy9wbGFuLXByb2R1Y3Rpb24vc2NoZWR1bGUtcHJvZHVjdGlvbi1uZXctcGFydC9kcmFmdCcpLA0KICAgICAgICAgIG5hbWU6ICdQUk8yUGFydFNjaGVkdWxlRHJhZnROZXN0TmV3JywNCiAgICAgICAgICBtZXRhOiB7IHRpdGxlOiAn6I2J56i/JyB9DQogICAgICAgIH0sIHsNCiAgICAgICAgICBwYXRoOiB0aGlzLiRyb3V0ZS5wYXRoICsgJy9hZGQtcmV0dXJuJywNCiAgICAgICAgICBoaWRkZW46IHRydWUsDQogICAgICAgICAgY29tcG9uZW50OiAoKSA9PiBpbXBvcnQoJ0Avdmlld3MvUFJPL21hdGVyaWFsLXJlY2VpcHQtbWFuYWdlbWVudC9yYXctc3RvY2stcmV0dXJuL2FkZC52dWUnKSwNCiAgICAgICAgICBuYW1lOiAnUFJPUmF3TWF0ZXJpYWxTdG9ja1JldHVybkFkZFJldHVybicsDQogICAgICAgICAgbWV0YTogeyB0aXRsZTogJ+aWsOW7uumAgOW6k+WNlScgfQ0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgZm9ybTogew0KICAgICAgICBOZXN0aW5nX1Jlc3VsdF9OYW1lOiAnJywNCiAgICAgICAgUmF3X05hbWU6ICcnLA0KICAgICAgICBUaGlja25lc3M6IHVuZGVmaW5lZCwNCiAgICAgICAgVGV4dHVyZTogJycsDQogICAgICAgIEN1dF9TdGF0dXM6ICcnLA0KICAgICAgICBUeXBlOiB1bmRlZmluZWQNCiAgICAgIH0sDQogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHdpZHRoOiAnNzAlJywNCiAgICAgIHRpdGxlOiAnJywNCiAgICAgIGN1cnJlbnRDb21wb25lbnQ6ICcnLA0KICAgICAgdGJMb2FkaW5nOiBmYWxzZSwNCiAgICAgIG9wdGlvbnM6IFtdLA0KICAgICAgY29sdW1uczogW10sDQogICAgICB0YkRhdGE6IFtdLA0KICAgICAgbXVsdGlwbGVTZWxlY3Rpb246IFtdLA0KICAgICAgdGFibGVQYWdlU2l6ZTogdGFibGVQYWdlU2l6ZSwNCiAgICAgIHRvdGFsOiAwLA0KICAgICAgcXVlcnlJbmZvOiB7DQogICAgICAgIFBhZ2U6IDEsDQogICAgICAgIFBhZ2VTaXplOiAyMA0KICAgICAgfSwNCiAgICAgIGRvd25sb2FkTG9hZGluZzogZmFsc2UsDQogICAgICBoZWFkZXJzOiB7DQogICAgICAgIEF1dGhvcml6YXRpb246IGdldFRva2VuKCksDQogICAgICAgIExhc3RfV29ya2luZ19PYmplY3RfSWQ6IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdMYXN0X1dvcmtpbmdfT2JqZWN0X0lkJykNCiAgICAgIH0NCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5nZXRUYkNvbmZpZygpDQogICAgdGhpcy5mZXRjaERhdGEoMSkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIHVwbG9hZFN1Y2Nlc3MocmVzcG9uc2UpIHsNCiAgICAgIGlmIChyZXNwb25zZS5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogJ+S4iuS8oOaIkOWKnycsDQogICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgIH0pDQogICAgICAgIHRoaXMuZmV0Y2hEYXRhKDEpDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiByZXNwb25zZS5NZXNzYWdlLA0KICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIGdldFRiQ29uZmlnKCkgew0KICAgICAgdGhpcy50YkxvYWRpbmcgPSB0cnVlDQogICAgICBhd2FpdCB0aGlzLmdldFRhYmxlQ29uZmlnKCdQUk9OZXN0aW5nTWFuYWdlbWVudEluZGV4JykNCiAgICAgIHRoaXMudGJMb2FkaW5nID0gZmFsc2UNCiAgICAgIGNvbnN0IGFhYSA9IFsnUGlja2luZ19CaWxscycsICdPdXRfQmlsbHMnLCAnTWFjaGluaW5nX0ZpbGVzJywgJ1RodW1ibmFpbCddDQogICAgICB0aGlzLmNvbHVtbnMgPSB0aGlzLmNvbHVtbnMuZmlsdGVyKGl0ZW0gPT4gIWFhYS5pbmNsdWRlcyhpdGVtLkNvZGUpKQ0KICAgIH0sDQogICAgLi4ubWFwQWN0aW9ucygnc2NoZWR1bGUnLCBbJ2NoYW5nZU5lc3RJZHMnXSksDQogICAgZmV0Y2hEYXRhKHBhZ2UpIHsNCiAgICAgIHBhZ2UgJiYgKHRoaXMucXVlcnlJbmZvLlBhZ2UgPSBwYWdlKQ0KICAgICAgdGhpcy50YkxvYWRpbmcgPSB0cnVlDQogICAgICBHZXROZXN0aW5nUmVzdWx0UGFnZUxpc3Qoew0KICAgICAgICAuLi50aGlzLnF1ZXJ5SW5mbywNCiAgICAgICAgLi4udGhpcy5mb3JtDQogICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy50YkRhdGEgPSByZXMuRGF0YS5EYXRhDQogICAgICAgICAgdGhpcy50b3RhbCA9IHJlcy5EYXRhLlRvdGFsQ291bnQNCiAgICAgICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gW10NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgICAgdGhpcy50YkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZVNlYXJjaCgpIHsNCg0KICAgIH0sDQogICAgaGFuZGxlSW1wb3J0KCkgew0KICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ05lc3RSZXBvcnQnDQogICAgICB0aGlzLndpZHRoID0gJzMwJScNCiAgICAgIHRoaXMudGl0bGUgPSAn5a+85YWl5aWX5paZ5oql5ZGKJw0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgICAgdGhpcy4kbmV4dFRpY2soXyA9PiB7DQoNCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVJbXBvcnRSZXN1bHQoKSB7DQogICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSAnTmVzdFJlc3VsdCcNCiAgICAgIHRoaXMud2lkdGggPSAnMzAlJw0KICAgICAgdGhpcy50aXRsZSA9ICflr7zlhaXlpZfmlpnnu5PmnpwnDQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgICB0aGlzLiRuZXh0VGljayhfID0+IHsNCg0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZUltcG9ydFRodW1icygpIHsNCiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9ICdOZXN0VGh1bWJzJw0KICAgICAgdGhpcy53aWR0aCA9ICczMCUnDQogICAgICB0aGlzLnRpdGxlID0gJ+WvvOWFpee8qeeVpeWbvicNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIGhhbmRsZURvd25sb2FkKCkgew0KICAgICAgdGhpcy5kb3dubG9hZExvYWRpbmcgPSB0cnVlDQogICAgICBFeHBvcnRQcm9jZXNzKHsgaWRzOiB0aGlzLm11bHRpcGxlU2VsZWN0aW9uLm1hcCh2ID0+IHYuSWQpIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB3aW5kb3cub3Blbihjb21iaW5lVVJMKHRoaXMuJGJhc2VVcmwsIHJlcy5EYXRhKSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pLmZpbmFsbHkoKCkgPT4gew0KICAgICAgICB0aGlzLmRvd25sb2FkTG9hZGluZyA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlRGVsZXRlKCkgew0KICAgICAgdGhpcy4kY29uZmlybSgnIOaYr+WQpuWIoOmZpOivpeaVsOaNrj8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICBEZWxldGVOZXN0aW5nUmVzdWx0KHsNCiAgICAgICAgICBpZHM6IHRoaXMubXVsdGlwbGVTZWxlY3Rpb24ubWFwKHYgPT4gdi5JZCkudG9TdHJpbmcoKQ0KICAgICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsDQogICAgICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTmiJDlip8hJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHRoaXMuZmV0Y2hEYXRhKDEpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIHR5cGU6ICdpbmZvJywNCiAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raIJw0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZVN1Ym1pdCgpIHsNCg0KICAgIH0sDQogICAgdGJTZWxlY3RDaGFuZ2UoYXJyYXkpIHsNCiAgICAgIGNvbnNvbGUubG9nKCcxMTExMTEnKQ0KICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbiA9IGFycmF5LnJlY29yZHMNCiAgICB9LA0KICAgIGhhbmRsZVJhdyhyb3cpIHsNCiAgICAgIHRoaXMud2lkdGggPSAnNzAlJw0KICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ1N1cnBsdXNSYXcnDQogICAgICB0aGlzLnRpdGxlID0gJ+S9meaWmeS/oeaBrycNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuJG5leHRUaWNrKF8gPT4gew0KICAgICAgICB0aGlzLiRyZWZzWydjb250ZW50J10uZ2V0RGF0YShyb3cuSWQpDQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlQW1vdW50KHJvdykgew0KICAgICAgdGhpcy53aWR0aCA9ICc3MCUnDQogICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSAnUGFydHNMYXlvdXQnDQogICAgICB0aGlzLnRpdGxlID0gJ+aOkueJiOmbtuS7ticNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuJG5leHRUaWNrKF8gPT4gew0KICAgICAgICB0aGlzLiRyZWZzWydjb250ZW50J10uZ2V0RGF0YShyb3cuSWQpDQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlU2NoZWR1bGUoKSB7DQogICAgICBjb25zdCBpZHMgPSB0aGlzLm11bHRpcGxlU2VsZWN0aW9uLmZpbHRlcihzID0+IHMuVHlwZSA9PT0gMSkubWFwKHYgPT4gdi5JZCkNCiAgICAgIGlmICghaWRzKSB7DQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgdGhpcy5jaGFuZ2VOZXN0SWRzKGlkcykNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsgbmFtZTogJ1BSTzJQYXJ0U2NoZWR1bGVEcmFmdE5lc3ROZXcnLCBxdWVyeTogeyBzdGF0dXM6ICdlZGl0JywgcGdfdHlwZTogJ3BhcnQnLCBwZ19yZWRpcmVjdDogdGhpcy4kcm91dGUubmFtZSwgdHlwZTogJzEnIH19KQ0KICAgIH0sDQogICAgaGFuZGxlQ2xvc2UoKSB7DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgIH0sDQogICAgaGFuZGxlUmVzZXQoKSB7DQogICAgICB0aGlzLiRyZWZzWydmb3JtJ10ucmVzZXRGaWVsZHMoKQ0KICAgICAgdGhpcy5mZXRjaERhdGEoMSkNCiAgICB9LA0KICAgIHRvQ3JlYXRlUGlja0xpc3QoKSB7DQogICAgICBjb25zb2xlLmxvZyh0aGlzLm11bHRpcGxlU2VsZWN0aW9uKQ0KICAgICAgaWYgKHRoaXMubXVsdGlwbGVTZWxlY3Rpb24uc29tZShpdGVtID0+IGl0ZW0uUGlja05vKSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflt7LmnInlhoXosIPljZXlj7fnmoTkuI3og73ph43lpI3nlJ/miJAnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsNCiAgICAgICAgbmFtZTogJ0FkZE1hdGVyaWFsUGlja0xpc3QnLA0KICAgICAgICBxdWVyeTogeyBwZ19yZWRpcmVjdDogdGhpcy4kcm91dGUubmFtZSwgdHlwZTogMCwgcGlja1R5cGU6IDEsIGlkczogdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5tYXAoaSA9PiBpLklkKSB9IC8vIHR5cGXljp/mlpnkuLowLHBpY2tUeXBl77yaMOaJi+WKqOeUn+aIkCAx5aWX5paZ55Sf5oiQDQogICAgICB9KQ0KICAgIH0sDQogICAgdG9DcmVhdGVSZXR1cm5MaXN0KCkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICBuYW1lOiAnUFJPUmF3TWF0ZXJpYWxTdG9ja1JldHVybkFkZFJldHVybicsDQogICAgICAgIHF1ZXJ5OiB7IHBnX3JlZGlyZWN0OiB0aGlzLiRyb3V0ZS5uYW1lLCBpc05lc3Rpbmc6IDEsIGlkczogdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5tYXAoaSA9PiBpLklkKSB9DQogICAgICB9KQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/nesting-management", "sourcesContent": ["<template>\r\n  <div class=\"cs-z-flex-pd16-wrap abs100\">\r\n    <div class=\"cs-z-page-main-content\">\r\n      <el-form ref=\"form\" :model=\"form\" inline label-width=\"80px\" style=\"width: 100%\">\r\n        <el-form-item label=\"排版名称\" prop=\"Nesting_Result_Name\">\r\n          <el-input v-model=\"form.Nesting_Result_Name\" placeholder=\"请输入\" clearable type=\"text\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"原料名称\" prop=\"Raw_Name\">\r\n          <el-input v-model=\"form.Raw_Name\" placeholder=\"请输入\" type=\"text\" clearable />\r\n        </el-form-item>\r\n        <el-form-item label=\"厚度\" prop=\"Thickness\" label-width=\"50px\">\r\n          <el-input-number v-model=\"form.Thickness\" :min=\"0\" :max=\"1000000\" class=\"w100 cs-number-btn-hidden\" placeholder=\"请输入\" clearable />\r\n        </el-form-item>\r\n        <el-form-item label=\"材质\" prop=\"Texture\" label-width=\"50px\">\r\n          <el-input v-model=\"form.Texture\" placeholder=\"请输入\" type=\"text\" clearable />\r\n        </el-form-item>\r\n        <el-form-item label=\"切割状态\" prop=\"Cut_Status\">\r\n          <el-select v-model=\"form.Cut_Status\" placeholder=\"请选择\" clearable style=\"width: 120px\">\r\n            <el-option label=\"待切割\" value=\"待切割\" />\r\n            <el-option label=\"已切割\" value=\"已切割\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"类型\" prop=\"Type\" label-width=\"50px\">\r\n          <el-select v-model=\"form.Type\" placeholder=\"请选择\" clearable style=\"width: 120px\">\r\n            <el-option label=\"手动导入\" :value=\"1\" />\r\n            <el-option label=\"系统推送\" :value=\"2\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"fetchData(1)\">查询</el-button>\r\n          <el-button @click=\"handleReset\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <vxe-toolbar ref=\"xToolbar\">\r\n        <template #buttons>\r\n          <el-button type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"toCreatePickList\">生成内调单</el-button>\r\n          <el-button type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"toCreateReturnList\">生成余料退库单</el-button>\r\n          <el-button type=\"success\" @click=\"handleImport\">导入套料报告</el-button>\r\n          <el-button type=\"success\" @click=\"handleImportResult\">导入套料结果</el-button>\r\n          <el-button type=\"success\" @click=\"handleImportThumbs\">导入缩略图</el-button>\r\n          <el-button type=\"danger\" :disabled=\"!multipleSelection.length\" @click=\"handleDelete\">删除</el-button>\r\n          <el-button type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"handleSchedule\">下发排产任务</el-button>\r\n          <el-button type=\"default\" :disabled=\"!multipleSelection.length\" :loading=\"downloadLoading\" @click=\"handleDownload\">导出加工信息</el-button>\r\n          <el-upload\r\n            :action=\"$baseUrl + 'Pro/Nesting/ImportProcess'\"\r\n            :show-file-list=\"false\"\r\n            :headers=\"headers\"\r\n            style=\"margin: 0 10px\"\r\n            accept=\".xlsx,.xls\"\r\n            :on-success=\"()=>fetchData(1)\"\r\n          >\r\n            <el-button type=\"default\">导入加工信息</el-button>\r\n          </el-upload>\r\n          <el-upload\r\n            :action=\"$baseUrl + 'Pro/Nesting/ImportProfiles'\"\r\n            :show-file-list=\"false\"\r\n            :on-success=\"uploadSuccess\"\r\n            :headers=\"headers\"\r\n            accept=\".xlsx,.xls\"\r\n          >\r\n            <el-button type=\"default\">导入型材</el-button>\r\n          </el-upload>\r\n          <DynamicTableFields\r\n            style=\"margin-left: auto\"\r\n            title=\"表格配置\"\r\n            table-config-code=\"PRONestingManagementIndex\"\r\n            @updateColumn=\"getTbConfig\"\r\n          />\r\n        </template>\r\n      </vxe-toolbar>\r\n\r\n      <div v-loading=\"tbLoading\" class=\"tb-x\">\r\n        <vxe-table\r\n          v-if=\"!tbLoading\"\r\n          ref=\"xTable\"\r\n          class=\"cs-vxe-table\"\r\n          :checkbox-config=\"{checkField: 'checked'}\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          :row-config=\"{ isCurrent: true, isHover: true}\"\r\n          align=\"center\"\r\n          height=\"auto\"\r\n          show-overflow\r\n          :auto-resize=\"true\"\r\n          stripe\r\n          size=\"medium\"\r\n          :data=\"tbData\"\r\n          resizable\r\n          :tooltip-config=\"{ enterable: true }\"\r\n          @checkbox-all=\"tbSelectChange\"\r\n          @checkbox-change=\"tbSelectChange\"\r\n        >\r\n          <vxe-column fixed=\"left\" type=\"checkbox\" />\r\n\r\n          <template v-for=\"item in columns\">\r\n            <vxe-column\r\n              :key=\"item.Code\"\r\n              :fixed=\"item.Is_Frozen ? (item.Frozen_Dirction || 'left') : ''\"\r\n              show-overflow=\"tooltip\"\r\n              :align=\"item.Align\"\r\n              :field=\"item.Code\"\r\n              :visible=\"item.Is_Display\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n              :edit-render=\"item.Is_Edit ? {} : null\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                <span v-if=\"item.Code === 'Type'\">\r\n                  <el-tag v-if=\"row[item.Code] === 1\" effect=\"plain\" type=\"success\">手动导入</el-tag>\r\n                  <el-tag v-else effect=\"plain\" type=\"warning\">自动推送</el-tag>\r\n                </span>\r\n                <span v-else-if=\"item.Code === 'Surplus_Raw'\">\r\n                  <el-button v-if=\"row.Surplus_Count>0\" type=\"text\" @click=\"handleRaw(row)\">查看</el-button>\r\n                </span>\r\n                <span v-else-if=\"item.Code === 'Part_Amount'\">\r\n                  <el-link type=\"primary\" :underline=\"false\" @click=\"handleAmount(row)\">{{ row[item.Code] }}</el-link>\r\n                </span>\r\n                <span v-else-if=\"item.Code === 'Utilization'\">\r\n                  {{ row[item.Code] }}<span v-if=\"row[item.Code]\">%</span>\r\n                </span>\r\n                <span v-else>{{ row[item.Code] }}</span>\r\n              </template>\r\n            </vxe-column>\r\n          </template>\r\n        </vxe-table>\r\n      </div>\r\n      <footer class=\"data-info\">\r\n        <el-tag\r\n          size=\"medium\"\r\n          class=\"info-x\"\r\n        >已选 {{ multipleSelection.length }} 条数据\r\n        </el-tag>\r\n        <Pagination\r\n          :total=\"total\"\r\n          max-height=\"100%\"\r\n          :page-sizes=\"tablePageSize\"\r\n          :page.sync=\"queryInfo.Page\"\r\n          :limit.sync=\"queryInfo.PageSize\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          @pagination=\"pageChange\"\r\n        />\r\n      </footer>\r\n\r\n      <el-dialog\r\n        v-if=\"dialogVisible\"\r\n        v-dialogDrag\r\n        class=\"plm-custom-dialog\"\r\n        :title=\"title\"\r\n        :visible.sync=\"dialogVisible\"\r\n        :width=\"width\"\r\n        @close=\"handleClose\"\r\n      >\r\n        <component\r\n          :is=\"currentComponent\"\r\n          ref=\"content\"\r\n          @close=\"handleClose\"\r\n          @refresh=\"fetchData(1)\"\r\n        />\r\n      </el-dialog>\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Pagination from '@/components/Pagination/index.vue'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nimport { DeleteNestingResult, GetNestingResultPageList } from '@/api/PRO/production-task'\r\nimport SurplusRaw from './components/SurplusRaw.vue'\r\nimport NestReport from './components/NestReport.vue'\r\nimport NestResult from './components/NestResult.vue'\r\nimport NestThumbs from './components/NestThumbs.vue'\r\nimport PartsLayout from './components/PartsLayout.vue'\r\nimport { mapActions } from 'vuex'\r\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\r\nimport { ExportProcess } from '@/api/PRO/materialManagement'\r\nimport { combineURL } from '@/utils'\r\nimport OSSUpload from '@/views/plm/components/ossupload.vue'\r\nimport { getToken } from '@/utils/auth'\r\n\r\nexport default {\r\n  name: 'PRONestingManagement',\r\n  components: { OSSUpload, DynamicTableFields, Pagination, SurplusRaw, NestReport, NestResult, PartsLayout, NestThumbs },\r\n  mixins: [getTbInfo, addRouterPage],\r\n  data() {\r\n    return {\r\n      addPageArray: [\r\n        {\r\n          path: '/material/pick/add',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/material_v4/pickApply/add.vue'),\r\n          name: 'AddMaterialPickList',\r\n          meta: { title: '新增领料单' },\r\n          query: { pg_redirect: this.$route.name }\r\n        },\r\n        {\r\n          path: this.$route.path + '/requisition',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/nesting-management/requisition.vue'),\r\n          name: 'ModelCompare',\r\n          meta: { title: '领料单' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/schedule',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/nesting-management/schedule.vue'),\r\n          name: 'PRONestingSchedule',\r\n          meta: { title: '下发排产任务' }\r\n        }, {\r\n          path: this.$route.path + '/draft',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/plan-production/schedule-production-new-part/draft'),\r\n          name: 'PRO2PartScheduleDraftNestNew',\r\n          meta: { title: '草稿' }\r\n        }, {\r\n          path: this.$route.path + '/add-return',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/material-receipt-management/raw-stock-return/add.vue'),\r\n          name: 'PRORawMaterialStockReturnAddReturn',\r\n          meta: { title: '新建退库单' }\r\n        }\r\n      ],\r\n      form: {\r\n        Nesting_Result_Name: '',\r\n        Raw_Name: '',\r\n        Thickness: undefined,\r\n        Texture: '',\r\n        Cut_Status: '',\r\n        Type: undefined\r\n      },\r\n      dialogVisible: false,\r\n      width: '70%',\r\n      title: '',\r\n      currentComponent: '',\r\n      tbLoading: false,\r\n      options: [],\r\n      columns: [],\r\n      tbData: [],\r\n      multipleSelection: [],\r\n      tablePageSize: tablePageSize,\r\n      total: 0,\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: 20\r\n      },\r\n      downloadLoading: false,\r\n      headers: {\r\n        Authorization: getToken(),\r\n        Last_Working_Object_Id: localStorage.getItem('Last_Working_Object_Id')\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getTbConfig()\r\n    this.fetchData(1)\r\n  },\r\n  methods: {\r\n    uploadSuccess(response) {\r\n      if (response.IsSucceed) {\r\n        this.$message({\r\n          message: '上传成功',\r\n          type: 'success'\r\n        })\r\n        this.fetchData(1)\r\n      } else {\r\n        this.$message({\r\n          message: response.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    async getTbConfig() {\r\n      this.tbLoading = true\r\n      await this.getTableConfig('PRONestingManagementIndex')\r\n      this.tbLoading = false\r\n      const aaa = ['Picking_Bills', 'Out_Bills', 'Machining_Files', 'Thumbnail']\r\n      this.columns = this.columns.filter(item => !aaa.includes(item.Code))\r\n    },\r\n    ...mapActions('schedule', ['changeNestIds']),\r\n    fetchData(page) {\r\n      page && (this.queryInfo.Page = page)\r\n      this.tbLoading = true\r\n      GetNestingResultPageList({\r\n        ...this.queryInfo,\r\n        ...this.form\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.Data\r\n          this.total = res.Data.TotalCount\r\n          this.multipleSelection = []\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    handleSearch() {\r\n\r\n    },\r\n    handleImport() {\r\n      this.currentComponent = 'NestReport'\r\n      this.width = '30%'\r\n      this.title = '导入套料报告'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n\r\n      })\r\n    },\r\n    handleImportResult() {\r\n      this.currentComponent = 'NestResult'\r\n      this.width = '30%'\r\n      this.title = '导入套料结果'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n\r\n      })\r\n    },\r\n    handleImportThumbs() {\r\n      this.currentComponent = 'NestThumbs'\r\n      this.width = '30%'\r\n      this.title = '导入缩略图'\r\n      this.dialogVisible = true\r\n    },\r\n    handleDownload() {\r\n      this.downloadLoading = true\r\n      ExportProcess({ ids: this.multipleSelection.map(v => v.Id) }).then(res => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data))\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.downloadLoading = false\r\n      })\r\n    },\r\n    handleDelete() {\r\n      this.$confirm(' 是否删除该数据?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        DeleteNestingResult({\r\n          ids: this.multipleSelection.map(v => v.Id).toString()\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.fetchData(1)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    handleSubmit() {\r\n\r\n    },\r\n    tbSelectChange(array) {\r\n      console.log('111111')\r\n      this.multipleSelection = array.records\r\n    },\r\n    handleRaw(row) {\r\n      this.width = '70%'\r\n      this.currentComponent = 'SurplusRaw'\r\n      this.title = '余料信息'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].getData(row.Id)\r\n      })\r\n    },\r\n    handleAmount(row) {\r\n      this.width = '70%'\r\n      this.currentComponent = 'PartsLayout'\r\n      this.title = '排版零件'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].getData(row.Id)\r\n      })\r\n    },\r\n    handleSchedule() {\r\n      const ids = this.multipleSelection.filter(s => s.Type === 1).map(v => v.Id)\r\n      if (!ids) {\r\n        return\r\n      }\r\n      this.changeNestIds(ids)\r\n      this.$router.push({ name: 'PRO2PartScheduleDraftNestNew', query: { status: 'edit', pg_type: 'part', pg_redirect: this.$route.name, type: '1' }})\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    handleReset() {\r\n      this.$refs['form'].resetFields()\r\n      this.fetchData(1)\r\n    },\r\n    toCreatePickList() {\r\n      console.log(this.multipleSelection)\r\n      if (this.multipleSelection.some(item => item.PickNo)) {\r\n        this.$message.error('已有内调单号的不能重复生成')\r\n        return\r\n      }\r\n      this.$router.push({\r\n        name: 'AddMaterialPickList',\r\n        query: { pg_redirect: this.$route.name, type: 0, pickType: 1, ids: this.multipleSelection.map(i => i.Id) } // type原料为0,pickType：0手动生成 1套料生成\r\n      })\r\n    },\r\n    toCreateReturnList() {\r\n      this.$router.push({\r\n        name: 'PRORawMaterialStockReturnAddReturn',\r\n        query: { pg_redirect: this.$route.name, isNesting: 1, ids: this.multipleSelection.map(i => i.Id) }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.el-divider{\r\n  margin:0 0 8px  0;\r\n}\r\n.cs-z-page-main-content{\r\n  display: flex;\r\n  flex-direction: column;\r\n  .tb-x{\r\n    display: flex;\r\n    flex-direction: column;\r\n    flex: 1;\r\n    overflow: hidden;\r\n  }\r\n  .data-info{\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    .info-x{\r\n      margin-right: 16px;\r\n    }\r\n  }\r\n}\r\n.pagination-container {\r\n  text-align: right;\r\n  padding: 16px 16px 0 16px;\r\n  margin: 0;\r\n}\r\n.el-form-item{\r\n  margin-bottom: 10px;\r\n}\r\n</style>\r\n"]}]}
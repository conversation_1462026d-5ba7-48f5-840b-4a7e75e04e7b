{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\quality_Inspection\\quality_summary\\components\\spotCheck.vue?vue&type=template&id=2cb2c2b6&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\quality_Inspection\\quality_summary\\components\\spotCheck.vue", "mtime": 1757572678833}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}
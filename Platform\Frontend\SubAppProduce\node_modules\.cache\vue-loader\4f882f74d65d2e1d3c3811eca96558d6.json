{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\detail.vue?vue&type=style&index=0&id=381ae030&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\detail.vue", "mtime": 1758683411838}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5lbC1kaXZpZGVyIHsKICBtYXJnaW46IDAgMCAxMHB4Owp9CgoudGIteCB7CiAgZmxleDogMTsKICBvdmVyZmxvdzphdXRvOwoKICA6OnYtZGVlcCB7CiAgICAuY3MtdnhlLXRhYmxlIC52eGUtYm9keS0tY29sdW1uLmNvbC1ibHVlIHsKICAgICAgY29sb3I6ICMyOThkZmY7CiAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgIH0KICB9Cn0KCi5jcy16LWZsZXgtcGQxNi13cmFwIHsKICBwYWRkaW5nLXRvcDogNTBweDsKCiAgLnRvcC1idG4gewogICAgcG9zaXRpb246IGFic29sdXRlOwogICAgdG9wOiAxMnB4OwogICAgbGVmdDogMjBweDsKICAgIHotaW5kZXg6IDk5OwoKICAgIC5lbC1idXR0b24gewogICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjdmOGY5OwogICAgfQogIH0KCiAgLmNzLXotcGFnZS1tYWluLWNvbnRlbnQgewogICAgb3ZlcmZsb3c6aGlkZGVuOwogICAgOjp2LWRlZXAgewogICAgICAuZWwtZm9ybS1pdGVtX19jb250ZW50IHsKICAgICAgICBtaW4td2lkdGg6IDIwMHB4OwogICAgICB9CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkjBA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/PRO/plan-production/task-list", "sourcesContent": ["<template>\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\n    <div class=\"top-btn\" @click=\"toBack\">\n      <el-button>返回</el-button>\n    </div>\n    <div class=\"cs-z-page-main-content\">\n      <el-form ref=\"form\" :model=\"queryForm\" inline label-width=\"130px\">\n        <el-row>\n          <el-col :span=\"20\">\n            <el-form-item label=\"排产单号\" prop=\"Schduling_Code\">\n              {{ info.Schduling_Code }}\n            </el-form-item>\n            <el-form-item label=\"任务单号\" prop=\"Task_Code\">\n              {{ info.Task_Code }}\n            </el-form-item>\n            <el-form-item label=\"项目名称\" prop=\"projectId\">\n              {{ info.Project_Name }}\n            </el-form-item>\n            <el-form-item label=\"区域名称\" prop=\"areaId\">\n              {{ info.Area_Name }}\n            </el-form-item>\n            <el-form-item v-if=\"!isVersionFour\" label=\"批次\" prop=\"install\">\n              {{ info.InstallUnit_Name }}\n            </el-form-item>\n            <!-- <br /> -->\n            <el-form-item label=\"任务下达时间\" prop=\"Order_Date\">\n              {{ info.Order_Date || '-' }}\n            </el-form-item>\n            <el-form-item label=\"工序计划开始时间\" prop=\"Process_Start_Date\">\n              {{ info.Process_Start_Date || '-' }}\n            </el-form-item>\n            <el-form-item label=\"工序计划完成时间\" prop=\"Process_Finish_Date\">\n              {{ info.Process_Finish_Date || '-' }}\n            </el-form-item>\n            <el-form-item label=\"完成时间\" prop=\"Finish_Date2\">\n              {{ info.Finish_Date2 || '-' }}\n            </el-form-item>\n            <el-form-item label=\"备注\" prop=\"Remark\">\n              {{ Remark || '-' }}\n            </el-form-item>\n            <el-form-item label=\"建议设备\" prop=\"eqptInfoListStr\">\n              {{ eqptInfoListStr || '-' }}\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\" style=\"margin-top: 12px;\">\n            <qrcode-vue ref=\"qrcodeRef\" :size=\"79\" :value=\"`T=${info.Task_Code}&C=${Tenant_Code}`\" class-name=\"qrcode\" level=\"H\" />\n          </el-col>\n        </el-row>\n        <br>\n        <el-form-item label=\"下道工序\" prop=\"Next_Process_Id\">\n          <el-select\n            v-model=\"queryForm.Next_Process_Id\"\n            clearable\n            placeholder=\"请选择\"\n            class=\"w100\"\n          >\n            <el-option\n              v-for=\"item in processOption\"\n              :key=\"item.Id\"\n              :label=\"item.Name\"\n              :value=\"item.Id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"下道班组\" prop=\"Next_Team_Id\">\n          <el-select\n            v-model=\"queryForm.Next_Team_Id\"\n            clearable\n            placeholder=\"请选择\"\n            class=\"w100\"\n          >\n            <el-option\n              v-for=\"item in groupOption\"\n              :key=\"item.Id\"\n              :label=\"item.Name\"\n              :value=\"item.Id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"search(1)\">搜索</el-button>\n          <el-button @click=\"handleReset()\">重置</el-button>\n          <el-button\n            :disabled=\"!multipleSelection.length\"\n            @click=\"printEvent()\"\n          >打印\n          </el-button>\n          <el-button\n            type=\"success\"\n            @click=\"handleExport\"\n          >导出\n          </el-button>\n          <el-button type=\"primary\" @click=\"handleSuggestDeviceDialog()\">设备详情</el-button>\n        </el-form-item>\n      </el-form>\n      <!--      <el-divider />-->\n      <div class=\"tb-x\">\n        <vxe-table\n          ref=\"xTable\"\n          :empty-render=\"{name: 'NotData'}\"\n          show-header-overflow\n          :print-config=\"printConfig\"\n          :row-config=\"{ isCurrent: true, isHover: true }\"\n          class=\"cs-vxe-table\"\n          align=\"left\"\n          height=\"auto\"\n          show-overflow\n          :loading=\"tbLoading\"\n          stripe\n          size=\"medium\"\n          :data=\"tbData\"\n          resizable\n          :tooltip-config=\"{ enterable: true }\"\n          :cell-class-name=\"cellClassName\"\n          @checkbox-all=\"tbSelectChange\"\n          @checkbox-change=\"tbSelectChange\"\n          @cell-click=\"cellClickEvent\"\n        >\n          <vxe-column type=\"checkbox\" align=\"center\" />\n          <template v-for=\"item in columns\">\n            <vxe-column\n              :key=\"item.Id\"\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n              show-overflow=\"tooltip\"\n              sortable\n              :align=\"item.Align\"\n              :field=\"item.Code\"\n              :title=\"item.Display_Name\"\n              :min-width=\"item.Width\"\n            />\n          </template>\n        </vxe-table>\n      </div>\n    </div>\n    <el-dialog\n      v-dialogDrag\n      class=\"plm-custom-dialog\"\n      :title=\"title\"\n      :visible.sync=\"deviceDialogVisible\"\n      :width=\"dWidth\"\n      :close-on-click-modal=\"false\"\n      top=\"10vh\"\n      @close=\"handleClose\"\n    >\n      <component\n        :is=\"currentComponent\"\n        ref=\"content\"\n        @close=\"handleClose\"\n      />\n    </el-dialog>\n    <el-dialog\n      v-dialogDrag\n      class=\"plm-custom-dialog\"\n      title=\"转移详情\"\n      :visible.sync=\"dialogVisible\"\n      width=\"576px\"\n      @close=\"handleClose\"\n    >\n      <TransferDetail ref=\"TransferDetail\" />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport getTbInfo from '@/mixins/PRO/get-table-info'\nimport { GetTeamTaskDetails, ExportTaskCodeDetails, GetSuggestDeviceAndRemark } from '@/api/PRO/production-task'\nimport { debounce, closeTagView } from '@/utils'\nimport QrcodeVue from 'qrcode.vue'\nimport {\n  GetProcessList,\n  GetWorkingTeamsPageList\n} from '@/api/PRO/technology-lib'\n// import { GetProcessList } from '@/api/PRO/technology-lib'\nimport TransferDetail from './transferDetail'\n// import { Export } from 'vxe-table'\nimport { combineURL } from '@/utils'\nimport { mapGetters } from 'vuex'\nimport SuggestDevice from './suggestDevice'\nimport { getBomCode, getBomName, checkIsUnitPart } from '@/views/PRO/bom-setting/utils'\nconst printStyle = `\n        .title {\n          text-align: center;\n        }\n        .is--print{\n          box-sizing: border-box;\n          width:95% !important;\n          margin:0 auto !important;\n        }\n        .my-list-row {\n          display: inline-block;\n          width: 100%;\n          margin-left:3%;\n        }\n        .my-list-row-first {\n          margin-bottom: 10px;\n        }\n        .my-list-col {\n          width:30%;\n          display: inline-block;\n          float: left;\n          margin-right: 1%;\n          word-wrap:break-word;\n          word-break:normal;\n        }\n        .left{\n          flex:1;\n        }\n        .my-top {\n          display:flex;\n          font-size: 12px;\n          margin-bottom: 5px;\n        }\n        .qrcode{\n          margin-right:10px\n        }\n        .cs-img{\n          position:relative;\n          right:30px\n        }\n        `\n\nexport default {\n  name: 'PROTaskListDetail',\n  components: {\n    TransferDetail,\n    QrcodeVue,\n    SuggestDevice\n  },\n  mixins: [getTbInfo],\n  data() {\n    return {\n      bomName: '',\n      deviceDialogVisible: false,\n      dialogVisible: false,\n      queryForm: {\n        // Project_Id: '',\n        // Area_Id: '',\n        // InstallUnit_Id: '',\n        Next_Process_Id: '',\n        Next_Team_Id: ''\n      },\n      queryInfo: {\n        Page: 1,\n        PageSize: -1\n      },\n      tbConfig: {\n        Op_Width: 120\n      },\n      tbLoading: false,\n      columns: [],\n      multipleSelection: [],\n      tbData: [],\n      finishList: [],\n      processOption: [],\n      groupOption: [],\n      total: 0,\n      printColumns: [],\n      search: () => ({}),\n      pageType: '',\n      info: {\n        Task_Code: '',\n        Project_Name: '',\n        Area_Name: '',\n        InstallUnit_Name: '',\n        Schduling_Code: '',\n        Task_Finish_Date: '',\n        Finish_Date2: '',\n        Order_Date: '',\n        Working_Team_Name: '',\n        Working_Process_Name: '',\n        Process_Start_Date: '',\n        Process_Finish_Date: ''\n      },\n      printConfig: {\n        sheetName: '任务单详情',\n        style: printStyle,\n        beforePrintMethod: ({ content }) => {\n          return this.topHtml + content\n        }\n      },\n      Tenant_Code: localStorage.getItem('tenant'),\n      Remark: '',\n      eqptInfoList: [],\n      eqptInfoListStr: ''\n    }\n  },\n  computed: {\n    ...mapGetters('tenant', ['isVersionFour']),\n    isCom() {\n      return this.pageType === getBomCode('-1')\n    },\n    isUnitPart() {\n      return checkIsUnitPart(this.pageType)\n    },\n    isPart() {\n      return this.pageType === getBomCode('0')\n    }\n  },\n  async mounted() {\n    this.pageType = this.$route.query.type\n    this.bomName = await getBomName(this.pageType)\n    this.workTeamId = this.$route.query.tid\n    this.taskCode = this.$route.query.id\n    this.info = JSON.parse(decodeURIComponent(this.$route.query.other))\n    await this.getSuggestDeviceAndRemark()\n    await this.getTableConfig(this.isCom ? 'PROComTaskListDetail' : this.isUnitPart ? 'PROUnitPartTaskListDetail' : 'PROPartTaskListDetail')\n    if (this.isCom) {\n      const idx = this.columns.findIndex((item) => item.Code === 'Part_Code')\n      idx !== -1 && this.columns.splice(idx, 1)\n\n      this.printColumns = this.columns.filter(column => column.Code !== 'Comp_Description')\n    }\n    if (this.isUnitPart) {\n      this.printColumns = this.columns.filter(column => column.Code !== 'Project_Name' && column.Code !== 'Area_Name' && column.Code !== 'Finish_Count' && column.Code !== 'Finish_Weight' && column.Code !== 'Comp_Description')\n    }\n    if (this.isPart) {\n      this.printColumns = this.columns.filter(column => column.Code !== 'Project_Name' && column.Code !== 'Area_Name' && column.Code !== 'Finish_Count' && column.Code !== 'Finish_Weight' && column.Code !== 'Comp_Description')\n    }\n\n    // else {\n    //   const idx = this.columns.findIndex((item) => item.Code === 'Comp_Code')\n    //   idx !== -1 && this.columns.splice(idx, 1)\n    // }\n    this.search = debounce(this.fetchData, 800, true)\n    this.fetchData()\n    this.getProcessOption()\n    this.getAllTeamOption()\n    this.getHtml()\n  },\n  methods: {\n    getSuggestDeviceAndRemark() {\n      GetSuggestDeviceAndRemark({\n        Bom_Level: this.pageType,\n        Task_Code: this.info.Task_Code\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.Remark = res.Data?.Remark\n          this.eqptInfoList = res.Data?.eqptInfoList || []\n          this.eqptInfoListStr = this.eqptInfoList.map(item => item.DisplayName).join(',')\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    tbSelectChange(array) {\n      this.multipleSelection = array.records\n    },\n    // 导出\n    handleExport() {\n      console.log(this.info)\n      ExportTaskCodeDetails({\n        Process_Type: this.isCom ? 2 : this.isPart ? 1 : 3, // 1零件，2构件\n        Working_Team_Id: this.workTeamId,\n        Task_Code: this.taskCode\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            message: '导出成功',\n            type: 'success'\n          })\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n      // const filterVal = this.columns.map((v) => v.Code)\n      // const data = formatJson(filterVal, this.multipleSelection)\n      // const header = this.columns.map((v) => v.Display_Name)\n      // import('@/vendor/Export2Excel').then((excel) => {\n      //   excel.export_json_to_excel({\n      //     header: header,\n      //     data,\n      //     filename: `${\n      //       this.info?.Task_Code ||\n      //       (this.isCom ? '构件任务单详情' : '零件任务单详情')\n      //     }`,\n      //     autoWidth: true,\n      //     bookType: 'xlsx'\n      //   })\n      // })\n      // function formatJson(filterVal, jsonData) {\n      //   return jsonData.map((v) => filterVal.map((j) => v[j]))\n      // }\n    },\n    fetchData(page) {\n      page && (this.queryInfo.Page = page)\n      console.log(this.queryInfo, 'this.queryInfo')\n      this.tbLoading = true\n      GetTeamTaskDetails({\n        // ...this.queryInfo,\n        Bom_Level: this.pageType,\n        Page: -1,\n        PageSize: -1,\n        Process_Type: this.isCom ? 2 : this.isPart ? 1 : 3, // 1零件，2构件\n        Working_Team_Id: this.workTeamId,\n        Task_Code: this.taskCode,\n        Next_Team_Id: this.queryForm.Next_Team_Id,\n        Next_Process_Id: this.queryForm.Next_Process_Id\n      })\n        .then((res) => {\n          if (res.IsSucceed) {\n            this.tbData = res.Data.Data.filter(item => {\n              return item.Allocation_Count !== 0\n            })\n            // this.total = res.Data.TotalCount\n          } else {\n            this.$message({\n              message: res.Message,\n              type: 'error'\n            })\n          }\n        })\n        .finally((_) => {\n          this.tbLoading = false\n        })\n    },\n    handleReset() {\n      this.$refs['form'].resetFields()\n      this.search(1)\n    },\n    getProcessOption() {\n      GetProcessList({\n        type: this.isCom ? 1 : this.isPart ? 2 : 3, // 1构件，2零件\n        Bom_Level: this.pageType\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.processOption = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    getAllTeamOption() {\n      GetWorkingTeamsPageList({\n        pageInfo: { Page: -1, PageSize: -1, ParameterJson: [] }\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.groupOption = res.Data.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    getHtml() {\n      // ${this.printConfig.sheetName}\n      const qr = this.$refs['qrcodeRef']\n      return new Promise((resolve, reject) => {\n        this.$nextTick(_ => {\n          const canvas = qr.$refs['qrcode-vue']\n          const dataURL = canvas.toDataURL('image/png')\n          this.topHtml = `\n        <h1 class=\"title\">#${this.info.Working_Process_Name || ''}# 加工任务单</h1>\n        <div class=\"my-top\">\n          <div class=\"left\">\n            <div class=\"my-list-row my-list-row-first\">\n              <div class=\"my-list-col\">项目名称/区域：${this.info.Project_Name || ''}/${this.info.Area_Name || ''}</div>\n              <div class=\"my-list-col\">排产单号：${this.info.Schduling_Code || ''}</div>\n              <div class=\"my-list-col\">加工班组：${this.info.Working_Team_Name || ''}</div>\n            </div>\n            <div class=\"my-list-row\">\n              <div class=\"my-list-col\">任务下单时间：${this.info.Order_Date || ''}</div>\n              <div class=\"my-list-col\">任务单号：${this.info?.Task_Code || ''}</div>\n              <div class=\"my-list-col\">工序计划完成时间：${this.info.Process_Finish_Date || ''}</div>\n            </div>\n          </div>\n          <div class=\"right\">\n           <img class=\"cs-img\" src=\"${dataURL}\" alt=\"\">\n          </div>\n        </div>\n        `\n          resolve()\n        })\n      })\n    },\n    printEvent() {\n      this.getHtml().then((_) => {\n        console.log(this.printConfig.sheetName, 'this.printConfig.sheetName')\n        this.$refs.xTable.print({\n          sheetName: this.printConfig.sheetName,\n          style: printStyle,\n          mode: 'selected',\n          columns: this.printColumns.map((v) => {\n            return {\n              field: v.Code\n            }\n          }),\n          beforePrintMethod: ({ content }) => {\n            // 拦截打印之前，返回自定义的 html 内容\n            const result = this.topHtml + content\n            console.log('result', result)\n            return result\n          }\n        })\n      })\n    },\n    handleView(row) {\n    },\n    getInnerTable(row, column) {\n      const ob = {\n        num: 1,\n        date: '2022-23-52'\n      }\n      for (let i = 0; i < 50; i++) {\n        this.finishList.push(ob)\n      }\n    },\n    toBack() {\n      closeTagView(this.$store, this.$route)\n    },\n\n    // 关闭弹窗\n    handleClose() {\n      this.$refs.TransferDetail.resetForm()\n      this.dialogVisible = false\n      this.deviceDialogVisible = false\n    },\n\n    // 单元格点击时间\n    cellClickEvent({ row, rowIndex, column, columnIndex }) {\n      // if (column.property === \"Finish_Count\" && row.Finish_Count > 0) {\n      //   this.dialogVisible = true;\n      //   this.$nextTick(() => {\n      //     this.$refs.TransferDetail.init(row, this.isCom);\n      //   });\n      // }\n    },\n\n    // 改变单元格样式\n    cellClassName({ row, rowIndex, column, columnIndex }) {\n      // if (column.property === \"Finish_Count\") {\n      //   return \"col-blue\";\n      // }\n    },\n\n    handleSuggestDeviceDialog() {\n      this.title = `生产设备详情`\n\n      this.currentComponent = 'SuggestDevice'\n      this.dWidth = '800px'\n      this.deviceDialogVisible = true\n\n      this.$nextTick(_ => {\n        this.$refs['draft'].initData()\n      })\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.el-divider {\n  margin: 0 0 10px;\n}\n\n.tb-x {\n  flex: 1;\n  overflow:auto;\n\n  ::v-deep {\n    .cs-vxe-table .vxe-body--column.col-blue {\n      color: #298dff;\n      cursor: pointer;\n    }\n  }\n}\n\n.cs-z-flex-pd16-wrap {\n  padding-top: 50px;\n\n  .top-btn {\n    position: absolute;\n    top: 12px;\n    left: 20px;\n    z-index: 99;\n\n    .el-button {\n      background-color: #f7f8f9;\n    }\n  }\n\n  .cs-z-page-main-content {\n    overflow:hidden;\n    ::v-deep {\n      .el-form-item__content {\n        min-width: 200px;\n      }\n    }\n  }\n}\n</style>\n"]}]}
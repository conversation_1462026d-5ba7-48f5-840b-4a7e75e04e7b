{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\detail.vue?vue&type=style&index=0&id=381ae030&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\detail.vue", "mtime": 1757909680923}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmVsLWRpdmlkZXIgew0KICBtYXJnaW46IDAgMCAxMHB4Ow0KfQ0KDQoudGIteCB7DQogIGZsZXg6IDE7DQogIG92ZXJmbG93OmF1dG87DQoNCiAgOjp2LWRlZXAgew0KICAgIC5jcy12eGUtdGFibGUgLnZ4ZS1ib2R5LS1jb2x1bW4uY29sLWJsdWUgew0KICAgICAgY29sb3I6ICMyOThkZmY7DQogICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgfQ0KICB9DQp9DQoNCi5jcy16LWZsZXgtcGQxNi13cmFwIHsNCiAgcGFkZGluZy10b3A6IDUwcHg7DQoNCiAgLnRvcC1idG4gew0KICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICB0b3A6IDEycHg7DQogICAgbGVmdDogMjBweDsNCiAgICB6LWluZGV4OiA5OTsNCg0KICAgIC5lbC1idXR0b24gew0KICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y3ZjhmOTsNCiAgICB9DQogIH0NCg0KICAuY3Mtei1wYWdlLW1haW4tY29udGVudCB7DQogICAgb3ZlcmZsb3c6aGlkZGVuOw0KICAgIDo6di1kZWVwIHsNCiAgICAgIC5lbC1mb3JtLWl0ZW1fX2NvbnRlbnQgew0KICAgICAgICBtaW4td2lkdGg6IDIwMHB4Ow0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8eA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/PRO/plan-production/task-list", "sourcesContent": ["<template>\r\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\r\n    <div class=\"top-btn\" @click=\"toBack\">\r\n      <el-button>返回</el-button>\r\n    </div>\r\n    <div class=\"cs-z-page-main-content\">\r\n      <el-form ref=\"form\" :model=\"queryForm\" inline label-width=\"130px\">\r\n        <el-row>\r\n          <el-col :span=\"20\">\r\n            <el-form-item label=\"排产单号\" prop=\"Schduling_Code\">\r\n              {{ info.Schduling_Code }}\r\n            </el-form-item>\r\n            <el-form-item label=\"任务单号\" prop=\"Task_Code\">\r\n              {{ info.Task_Code }}\r\n            </el-form-item>\r\n            <el-form-item label=\"项目名称\" prop=\"projectId\">\r\n              {{ info.Project_Name }}\r\n            </el-form-item>\r\n            <el-form-item label=\"区域名称\" prop=\"areaId\">\r\n              {{ info.Area_Name }}\r\n            </el-form-item>\r\n            <el-form-item v-if=\"!isVersionFour\" label=\"批次\" prop=\"install\">\r\n              {{ info.InstallUnit_Name }}\r\n            </el-form-item>\r\n            <!-- <br /> -->\r\n            <el-form-item label=\"任务下达时间\" prop=\"Order_Date\">\r\n              {{ info.Order_Date }}\r\n            </el-form-item>\r\n            <el-form-item label=\"工序要求完成时间\" prop=\"Process_Finish_Date\">\r\n              {{ info.Process_Finish_Date }}\r\n            </el-form-item>\r\n            <el-form-item label=\"完成时间\" prop=\"Finish_Date2\">\r\n              {{ info.Finish_Date2 }}\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\" style=\"margin-top: 12px;\">\r\n            <qrcode-vue ref=\"qrcodeRef\" :size=\"79\" :value=\"`T=${info.Task_Code}&C=${Tenant_Code}`\" class-name=\"qrcode\" level=\"H\" />\r\n          </el-col>\r\n        </el-row>\r\n        <br>\r\n        <el-form-item label=\"下道工序\" prop=\"Next_Process_Id\">\r\n          <el-select\r\n            v-model=\"queryForm.Next_Process_Id\"\r\n            clearable\r\n            placeholder=\"请选择\"\r\n            class=\"w100\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in processOption\"\r\n              :key=\"item.Id\"\r\n              :label=\"item.Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"下道班组\" prop=\"Next_Team_Id\">\r\n          <el-select\r\n            v-model=\"queryForm.Next_Team_Id\"\r\n            clearable\r\n            placeholder=\"请选择\"\r\n            class=\"w100\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in groupOption\"\r\n              :key=\"item.Id\"\r\n              :label=\"item.Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"search(1)\">搜索</el-button>\r\n          <el-button @click=\"handleReset()\">重置</el-button>\r\n          <el-button\r\n            :disabled=\"!multipleSelection.length\"\r\n            @click=\"printEvent()\"\r\n          >打印\r\n          </el-button>\r\n          <el-button\r\n            type=\"success\"\r\n            @click=\"handleExport\"\r\n          >导出\r\n          </el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <!--      <el-divider />-->\r\n      <div class=\"tb-x\">\r\n        <vxe-table\r\n          ref=\"xTable\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          :print-config=\"printConfig\"\r\n          :row-config=\"{ isCurrent: true, isHover: true }\"\r\n          class=\"cs-vxe-table\"\r\n          align=\"left\"\r\n          height=\"auto\"\r\n          show-overflow\r\n          :loading=\"tbLoading\"\r\n          stripe\r\n          size=\"medium\"\r\n          :data=\"tbData\"\r\n          resizable\r\n          :tooltip-config=\"{ enterable: true }\"\r\n          :cell-class-name=\"cellClassName\"\r\n          @checkbox-all=\"tbSelectChange\"\r\n          @checkbox-change=\"tbSelectChange\"\r\n          @cell-click=\"cellClickEvent\"\r\n        >\r\n          <vxe-column type=\"checkbox\" align=\"center\" />\r\n          <template v-for=\"item in columns\">\r\n            <vxe-column\r\n              :key=\"item.Id\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :align=\"item.Align\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n            />\r\n          </template>\r\n        </vxe-table>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      title=\"转移详情\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"576px\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <TransferDetail ref=\"TransferDetail\" />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport { GetTeamTaskDetails, ExportTaskCodeDetails } from '@/api/PRO/production-task'\r\nimport { debounce, closeTagView } from '@/utils'\r\nimport QrcodeVue from 'qrcode.vue'\r\nimport {\r\n  GetProcessList,\r\n  GetWorkingTeamsPageList\r\n} from '@/api/PRO/technology-lib'\r\n// import { GetProcessList } from '@/api/PRO/technology-lib'\r\nimport TransferDetail from './transferDetail'\r\nimport { Export } from 'vxe-table'\r\nimport { combineURL } from '@/utils'\r\nimport { mapGetters } from 'vuex'\r\nimport { getBomCode, checkIsUnitPart } from '@/views/PRO/bom-setting/utils'\r\nconst printStyle = `\r\n        .title {\r\n          text-align: center;\r\n        }\r\n        .is--print{\r\n          box-sizing: border-box;\r\n          width:95% !important;\r\n          margin:0 auto !important;\r\n        }\r\n        .my-list-row {\r\n          display: inline-block;\r\n          width: 100%;\r\n          margin-left:3%;\r\n        }\r\n        .my-list-row-first {\r\n          margin-bottom: 10px;\r\n        }\r\n        .my-list-col {\r\n          width:30%;\r\n          display: inline-block;\r\n          float: left;\r\n          margin-right: 1%;\r\n          word-wrap:break-word;\r\n          word-break:normal;\r\n        }\r\n        .left{\r\n          flex:1;\r\n        }\r\n        .my-top {\r\n          display:flex;\r\n          font-size: 12px;\r\n          margin-bottom: 5px;\r\n        }\r\n        .qrcode{\r\n          margin-right:10px\r\n        }\r\n        .cs-img{\r\n          position:relative;\r\n          right:30px\r\n        }\r\n        `\r\n\r\nexport default {\r\n  name: 'PROTaskListDetail',\r\n  components: {\r\n    TransferDetail,\r\n    QrcodeVue\r\n  },\r\n  mixins: [getTbInfo],\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      queryForm: {\r\n        // Project_Id: '',\r\n        // Area_Id: '',\r\n        // InstallUnit_Id: '',\r\n        Next_Process_Id: '',\r\n        Next_Team_Id: ''\r\n      },\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: -1\r\n      },\r\n      tbConfig: {\r\n        Op_Width: 120\r\n      },\r\n      tbLoading: false,\r\n      columns: [],\r\n      multipleSelection: [],\r\n      tbData: [],\r\n      finishList: [],\r\n      processOption: [],\r\n      groupOption: [],\r\n      total: 0,\r\n      printColumns: [],\r\n      search: () => ({}),\r\n      pageType: '',\r\n      info: {\r\n        Task_Code: '',\r\n        Project_Name: '',\r\n        Area_Name: '',\r\n        InstallUnit_Name: '',\r\n        Schduling_Code: '',\r\n        Task_Finish_Date: '',\r\n        Finish_Date2: '',\r\n        Order_Date: '',\r\n        Working_Team_Name: '',\r\n        Working_Process_Name: ''\r\n      },\r\n      printConfig: {\r\n        sheetName: '任务单详情',\r\n        style: printStyle,\r\n        beforePrintMethod: ({ content }) => {\r\n          return this.topHtml + content\r\n        }\r\n      },\r\n      Tenant_Code: localStorage.getItem('tenant')\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters('tenant', ['isVersionFour']),\r\n    isCom() {\r\n      return this.pageType === getBomCode('-1')\r\n    },\r\n    isUnitPart() {\r\n      return checkIsUnitPart(this.pageType)\r\n    },\r\n    isPart() {\r\n      return this.pageType === getBomCode('0')\r\n    }\r\n  },\r\n  async mounted() {\r\n    this.pageType = this.$route.query.type\r\n    this.workTeamId = this.$route.query.tid\r\n    this.taskCode = this.$route.query.id\r\n    this.info = JSON.parse(decodeURIComponent(this.$route.query.other))\r\n    await this.getTableConfig(this.isCom ? 'PROComTaskListDetail' : this.isUnitPart ? 'PROUnitPartTaskListDetail' : 'PROPartTaskListDetail')\r\n    if (this.isCom) {\r\n      const idx = this.columns.findIndex((item) => item.Code === 'Part_Code')\r\n      idx !== -1 && this.columns.splice(idx, 1)\r\n\r\n      this.printColumns = this.columns.filter(column => column.Code !== 'Comp_Description')\r\n    }\r\n    if (this.isUnitPart) {\r\n      this.printColumns = this.columns.filter(column => column.Code !== 'Project_Name' && column.Code !== 'Area_Name' && column.Code !== 'Finish_Count' && column.Code !== 'Finish_Weight' && column.Code !== 'Comp_Description')\r\n    }\r\n    if (this.isPart) {\r\n      this.printColumns = this.columns.filter(column => column.Code !== 'Project_Name' && column.Code !== 'Area_Name' && column.Code !== 'Finish_Count' && column.Code !== 'Finish_Weight' && column.Code !== 'Comp_Description')\r\n    }\r\n    // else {\r\n    //   const idx = this.columns.findIndex((item) => item.Code === 'Comp_Code')\r\n    //   idx !== -1 && this.columns.splice(idx, 1)\r\n    // }\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    this.fetchData()\r\n    this.getProcessOption()\r\n    this.getAllTeamOption()\r\n    this.getHtml()\r\n  },\r\n  methods: {\r\n    tbSelectChange(array) {\r\n      this.multipleSelection = array.records\r\n    },\r\n    // 导出\r\n    handleExport() {\r\n      console.log(this.info)\r\n      ExportTaskCodeDetails({\r\n        Process_Type: this.isCom ? 2 : this.isPart ? 1 : 3, // 1零件，2构件\r\n        Working_Team_Id: this.workTeamId,\r\n        Task_Code: this.taskCode\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '导出成功',\r\n            type: 'success'\r\n          })\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      // const filterVal = this.columns.map((v) => v.Code)\r\n      // const data = formatJson(filterVal, this.multipleSelection)\r\n      // const header = this.columns.map((v) => v.Display_Name)\r\n      // import('@/vendor/Export2Excel').then((excel) => {\r\n      //   excel.export_json_to_excel({\r\n      //     header: header,\r\n      //     data,\r\n      //     filename: `${\r\n      //       this.info?.Task_Code ||\r\n      //       (this.isCom ? '构件任务单详情' : '零件任务单详情')\r\n      //     }`,\r\n      //     autoWidth: true,\r\n      //     bookType: 'xlsx'\r\n      //   })\r\n      // })\r\n      // function formatJson(filterVal, jsonData) {\r\n      //   return jsonData.map((v) => filterVal.map((j) => v[j]))\r\n      // }\r\n    },\r\n    fetchData(page) {\r\n      page && (this.queryInfo.Page = page)\r\n      console.log(this.queryInfo, 'this.queryInfo')\r\n      this.tbLoading = true\r\n      GetTeamTaskDetails({\r\n        // ...this.queryInfo,\r\n        Bom_Level: this.pageType,\r\n        Page: -1,\r\n        PageSize: -1,\r\n        Process_Type: this.isCom ? 2 : this.isPart ? 1 : 3, // 1零件，2构件\r\n        Working_Team_Id: this.workTeamId,\r\n        Task_Code: this.taskCode,\r\n        Next_Team_Id: this.queryForm.Next_Team_Id,\r\n        Next_Process_Id: this.queryForm.Next_Process_Id\r\n      })\r\n        .then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.tbData = res.Data.Data.filter(item => {\r\n              return item.Allocation_Count !== 0\r\n            })\r\n            // this.total = res.Data.TotalCount\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n        .finally((_) => {\r\n          this.tbLoading = false\r\n        })\r\n    },\r\n    handleReset() {\r\n      this.$refs['form'].resetFields()\r\n      this.search(1)\r\n    },\r\n    getProcessOption() {\r\n      GetProcessList({\r\n        type: this.isCom ? 1 : this.isPart ? 2 : 3, // 1构件，2零件\r\n        Bom_Level: this.pageType\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.processOption = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getAllTeamOption() {\r\n      GetWorkingTeamsPageList({\r\n        pageInfo: { Page: -1, PageSize: -1, ParameterJson: [] }\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.groupOption = res.Data.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getHtml() {\r\n      // ${this.printConfig.sheetName}\r\n      const qr = this.$refs['qrcodeRef']\r\n      return new Promise((resolve, reject) => {\r\n        this.$nextTick(_ => {\r\n          const canvas = qr.$refs['qrcode-vue']\r\n          const dataURL = canvas.toDataURL('image/png')\r\n          this.topHtml = `\r\n        <h1 class=\"title\">#${this.info.Working_Process_Name || ''}# 加工任务单</h1>\r\n        <div class=\"my-top\">\r\n          <div class=\"left\">\r\n            <div class=\"my-list-row my-list-row-first\">\r\n              <div class=\"my-list-col\">项目名称/区域：${this.info.Project_Name || ''}/${this.info.Area_Name || ''}</div>\r\n              <div class=\"my-list-col\">排产单号：${this.info.Schduling_Code || ''}</div>\r\n              <div class=\"my-list-col\">加工班组：${this.info.Working_Team_Name || ''}</div>\r\n            </div>\r\n            <div class=\"my-list-row\">\r\n              <div class=\"my-list-col\">任务下单时间：${this.info.Order_Date || ''}</div>\r\n              <div class=\"my-list-col\">任务单号：${this.info?.Task_Code || ''}</div>\r\n              <div class=\"my-list-col\">工序要求完成时间：${this.info.Process_Finish_Date || ''}</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"right\">\r\n           <img class=\"cs-img\" src=\"${dataURL}\" alt=\"\">\r\n          </div>\r\n        </div>\r\n        `\r\n          resolve()\r\n        })\r\n      })\r\n    },\r\n    printEvent() {\r\n      this.getHtml().then((_) => {\r\n        console.log(this.printConfig.sheetName, 'this.printConfig.sheetName')\r\n        this.$refs.xTable.print({\r\n          sheetName: this.printConfig.sheetName,\r\n          style: printStyle,\r\n          mode: 'selected',\r\n          columns: this.printColumns.map((v) => {\r\n            return {\r\n              field: v.Code\r\n            }\r\n          }),\r\n          beforePrintMethod: ({ content }) => {\r\n            // 拦截打印之前，返回自定义的 html 内容\r\n            const result = this.topHtml + content\r\n            console.log('result', result)\r\n            return result\r\n          }\r\n        })\r\n      })\r\n    },\r\n    handleView(row) {\r\n    },\r\n    getInnerTable(row, column) {\r\n      const ob = {\r\n        num: 1,\r\n        date: '2022-23-52'\r\n      }\r\n      for (let i = 0; i < 50; i++) {\r\n        this.finishList.push(ob)\r\n      }\r\n    },\r\n    toBack() {\r\n      closeTagView(this.$store, this.$route)\r\n    },\r\n\r\n    // 关闭弹窗\r\n    handleClose() {\r\n      this.$refs.TransferDetail.resetForm()\r\n      this.dialogVisible = false\r\n    },\r\n\r\n    // 单元格点击时间\r\n    cellClickEvent({ row, rowIndex, column, columnIndex }) {\r\n      // if (column.property === \"Finish_Count\" && row.Finish_Count > 0) {\r\n      //   this.dialogVisible = true;\r\n      //   this.$nextTick(() => {\r\n      //     this.$refs.TransferDetail.init(row, this.isCom);\r\n      //   });\r\n      // }\r\n    },\r\n\r\n    // 改变单元格样式\r\n    cellClassName({ row, rowIndex, column, columnIndex }) {\r\n      // if (column.property === \"Finish_Count\") {\r\n      //   return \"col-blue\";\r\n      // }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.el-divider {\r\n  margin: 0 0 10px;\r\n}\r\n\r\n.tb-x {\r\n  flex: 1;\r\n  overflow:auto;\r\n\r\n  ::v-deep {\r\n    .cs-vxe-table .vxe-body--column.col-blue {\r\n      color: #298dff;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n.cs-z-flex-pd16-wrap {\r\n  padding-top: 50px;\r\n\r\n  .top-btn {\r\n    position: absolute;\r\n    top: 12px;\r\n    left: 20px;\r\n    z-index: 99;\r\n\r\n    .el-button {\r\n      background-color: #f7f8f9;\r\n    }\r\n  }\r\n\r\n  .cs-z-page-main-content {\r\n    overflow:hidden;\r\n    ::v-deep {\r\n      .el-form-item__content {\r\n        min-width: 200px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\part-type\\component\\Lack.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\part-type\\component\\Lack.vue", "mtime": 1757468112175}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetPartTypeList", "Set<PERSON>Default", "data", "btnLoading", "form", "Id", "options", "rules", "required", "message", "trigger", "mounted", "getOption", "methods", "_this", "Part_Grade", "then", "res", "IsSucceed", "Data", "find", "ele", "<PERSON><PERSON>De<PERSON><PERSON>", "$message", "Message", "type", "handleClose", "$emit", "handleSubmit", "_this2", "$refs", "validate", "valid", "id"], "sources": ["src/views/PRO/basic-information/part-type/component/Lack.vue"], "sourcesContent": ["<template>\r\n  <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"190px\" style=\"width: 100%\">\r\n    <el-form-item label=\"分类缺省时，默认选择为：\" prop=\"Id\">\r\n      <el-select v-model=\"form.Id\" placeholder=\"请选择\" clearable=\"\">\r\n        <el-option\r\n          v-for=\"item in options\"\r\n          :key=\"item.Id\"\r\n          :label=\"item.Name\"\r\n          :value=\"item.Id\"\r\n        />\r\n      </el-select>\r\n    </el-form-item>\r\n    <span class=\"tip\">当导入的零件清单中，零件分类缺省时，系统自动补全上述选择的分类。</span>\r\n    <el-form-item class=\"cs-footer\">\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit('form')\">确 定</el-button>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n\r\n<script>\r\nimport { GetPartTypeList, SettingDefault } from '@/api/PRO/partType'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      form: {\r\n        Id: ''\r\n      },\r\n      options: [],\r\n      rules: {\r\n        Id: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ]\r\n      }\r\n\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getOption()\r\n  },\r\n  methods: {\r\n    getOption() {\r\n      GetPartTypeList({ Part_Grade: 0 }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.options = res.Data\r\n          this.form.Id = this.options.find(ele => ele.Is_Default).Id || ''\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.$emit('close')\r\n    },\r\n    handleSubmit() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (!valid) return\r\n        SettingDefault({\r\n          id: this.form.Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '操作成功',\r\n              type: 'success'\r\n            })\r\n            this.$emit('close')\r\n            this.$emit('refresh')\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n      this.btnLoading = false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.cs-footer{\r\n  text-align: center;\r\n  margin-top: 40px;\r\n}\r\n.tip{\r\n  color: rgba(34, 40, 52, 0.4);\r\n  font-size: 12px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,SAAAA,eAAA,EAAAC,cAAA;AAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,IAAA;QACAC,EAAA;MACA;MACAC,OAAA;MACAC,KAAA;QACAF,EAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACAD,SAAA,WAAAA,UAAA;MAAA,IAAAE,KAAA;MACAd,eAAA;QAAAe,UAAA;MAAA,GAAAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAJ,KAAA,CAAAR,OAAA,GAAAW,GAAA,CAAAE,IAAA;UACAL,KAAA,CAAAV,IAAA,CAAAC,EAAA,GAAAS,KAAA,CAAAR,OAAA,CAAAc,IAAA,WAAAC,GAAA;YAAA,OAAAA,GAAA,CAAAC,UAAA;UAAA,GAAAjB,EAAA;QACA;UACAS,KAAA,CAAAS,QAAA;YACAd,OAAA,EAAAQ,GAAA,CAAAO,OAAA;YACAC,IAAA;UACA;QACA;MACA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAC,KAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;QACA/B,cAAA;UACAgC,EAAA,EAAAJ,MAAA,CAAAzB,IAAA,CAAAC;QACA,GAAAW,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAW,MAAA,CAAAN,QAAA;cACAd,OAAA;cACAgB,IAAA;YACA;YACAI,MAAA,CAAAF,KAAA;YACAE,MAAA,CAAAF,KAAA;UACA;YACAE,MAAA,CAAAN,QAAA;cACAd,OAAA,EAAAQ,GAAA,CAAAO,OAAA;cACAC,IAAA;YACA;UACA;QACA;MACA;MACA,KAAAtB,UAAA;IACA;EACA;AACA", "ignoreList": []}]}
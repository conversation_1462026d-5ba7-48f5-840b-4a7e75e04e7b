<template>
  <div class="app-container abs100">
    <bt-tree :data="treeData" :props="treeProps" :default-selected-key="treeDefaultSelectedKey" node-key="Sys_Project_Id" @node-click="nodeClick">
      <template #default="{ data }">
        <span style="color: #5ac8fa!important;">({{ data.Code }})</span>
        <span>{{ data.Short_Name }}</span>
      </template>
    </bt-tree>
    <OverallControlPlanContent :cur-project="curProject" />
  </div>
</template>

<script>
import { GetCurrCompanyProjectList } from '@/api/plm/projects'
import OverallControlPlanContent from './components/OverallControlPlanContent.vue'

export default {
  name: 'OverallControlPlan',
  components: { OverallControlPlanContent },
  data() {
    return {
      treeData: [],
      treeProps: {
        label: 'Short_Name',
        id: 'Sys_Project_Id'
      },
      treeDefaultSelectedKey: '',
      curProject: ''
    }
  },
  created() {
    this.getTreeData()
  },
  methods: {
    getTreeData() {
      GetCurrCompanyProjectList({
        companId: localStorage.getItem('CurReferenceId'),
        IsCascade: false
      }).then(res => {
        this.treeData = res.Data.map(item => {
          item.loading = false
          return item
        })
        if (this.treeData.length) {
          this.treeDefaultSelectedKey = this.treeData[0].Sys_Project_Id
          this.curProject = this.treeData[0]
        }
      })
    },
    nodeClick(node) {
      this.curProject = node
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  font-family: PingFang SC, PingFang SC;
  display: flex;
}
</style>

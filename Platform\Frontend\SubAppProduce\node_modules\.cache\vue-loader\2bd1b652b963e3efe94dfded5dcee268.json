{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\component-list\\v4\\component\\bimdialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\component-list\\v4\\component\\bimdialog.vue", "mtime": 1757468112574}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["bimdialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAs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file": "bimdialog.vue", "sourceRoot": "src/views/PRO/component-list/v4/component", "sourcesContent": ["<template>\r\n  <el-dialog\r\n    class=\"plm-custom-dialog\"\r\n    :title=\"title\"\r\n    :visible.sync=\"dialogVisible\"\r\n    width=\"570px\"\r\n    top=\"5vh\"\r\n    :loading=\"loading\"\r\n    @submitbtn=\"handleSubmit('form')\"\r\n    @cancelbtn=\"handleClose\"\r\n    @handleClose=\"handleClose\"\r\n    @close=\"handleClose\"\r\n  >\r\n    <div class=\"cs-alert\">\r\n      <i class=\"el-icon-warning-outline\" />注意：请先<el-button type=\"text\" @click=\"getTemplate\">下载构件导入模板</el-button>\r\n    </div>\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n      <!--      <el-form-item v-if=\"!isVersionFour\" label=\"下载模板\" prop=\"Template_Type\">\r\n        <el-radio-group v-model=\"form.Template_Type\" @input=\"radioChange\">\r\n          <el-radio :label=\"2\">固定模板</el-radio>\r\n          <el-radio :label=\"1\">动态模板</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>-->\r\n      <el-form-item label=\"导入方式\" prop=\"areaType\">\r\n        <el-radio-group v-model=\"areaType\">\r\n          <el-radio :label=\"2\">多区域导入</el-radio>\r\n          <el-radio :label=\"1\">单区域导入</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item label=\"是否跳过生产\" prop=\"Is_Skip_Production\">\r\n        <el-radio-group v-model=\"form.Is_Skip_Production\">\r\n          <el-radio :label=\"true\">是</el-radio>\r\n          <el-radio :label=\"false\">否</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item label=\"项目名称\" prop=\"Project_Name\">\r\n        <el-input v-model=\"form.Project_Name\" style=\"width: 360px\" disabled />\r\n      </el-form-item>\r\n      <el-form-item v-if=\"areaType===1\" label=\"区域\" prop=\"Area_Name\">\r\n        <el-input v-model=\"form.Area_Name\" style=\"width: 360px\" disabled />\r\n      </el-form-item>\r\n      <el-form-item label=\"类别名称\" prop=\"Type_Name\">\r\n        <el-input v-model=\"form.Type_Name\" style=\"width: 360px\" disabled />\r\n      </el-form-item>\r\n      <el-form-item label=\"标题\" prop=\"Doc_Title\">\r\n        <el-input v-model=\"form.Doc_Title\" style=\"width: 360px\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"简要描述\" prop=\"Doc_Content\">\r\n        <el-input v-model=\"form.Doc_Content\" style=\"width: 360px\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"附件信息\" prop=\"Doc_File\">\r\n        <el-input v-model=\"form.Doc_File\" style=\"width: 360px\" disabled />\r\n      </el-form-item><!--      <el-form-item\r\n        v-if=\"!isVersionFour&&form.Type === 1&&!isDynamicTemplate\"\r\n        :rules=\" [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ]\"\r\n        label=\"自动拆分直发件\"\r\n        prop=\"Is_Auto_Split\"\r\n      >\r\n        <el-radio-group v-model=\"form.Is_Auto_Split\" :disabled=\"[true,false].includes(isAutoSplit)\">\r\n          <el-radio :label=\"false\">否</el-radio>\r\n          <el-radio :label=\"true\">是</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>-->\r\n\r\n      <el-form-item label=\"上传附件\">\r\n        <OSSUpload\r\n          ref=\"company\"\r\n          drag\r\n          class=\"upload-demo\"\r\n          :action=\"$store.state.uploadUrl\"\r\n          :on-change=\"handleChange\"\r\n          :before-upload=\"beforeUpload\"\r\n          :file-list=\"fileList\"\r\n          :limit=\"2\"\r\n          :on-success=\"uploadSuccess\"\r\n          :on-error=\"uploadError\"\r\n          :before-remove=\"beforeRemove\"\r\n          :on-remove=\"handleRemove\"\r\n          :multiple=\"false\"\r\n          :accept=\"allowFile\"\r\n        >\r\n          <!-- :on-exceed=\"onExceed\" -->\r\n          <i class=\"el-icon-upload\" />\r\n          <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n        </OSSUpload>\r\n      </el-form-item>\r\n    </el-form>\r\n    <span slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n      <el-button\r\n        type=\"primary\"\r\n        :loading=\"btnLoading\"\r\n        @click=\"handleSubmit()\"\r\n      >确 定</el-button>\r\n    </span>\r\n  </el-dialog>\r\n\r\n</template>\r\n\r\n<script>\r\nimport OSSUpload from '@/views/plm/components/ossupload.vue'\r\nimport {\r\n  GenerateDeepenFileFromDirect,\r\n  UpdatePartAggregateId, AppendImportDeepFiles, ThreeBomImportTemplate\r\n} from '@/api/PRO/component'\r\nimport { combineURL } from '@/utils'\r\nimport { mapGetters } from 'vuex'\r\nconst form = {\r\n  Id: '',\r\n  Is_Auto_Split: undefined,\r\n  Doc_Catelog: '',\r\n  Doc_Type: '',\r\n  Project_Name: '',\r\n  Project_Id: '',\r\n  Sys_Project_Id: '',\r\n  Area_Name: '',\r\n  Area_Id: '',\r\n  Type_Name: '',\r\n  Doc_Title: '',\r\n  Doc_Content: '',\r\n  IsChanged: false,\r\n  Is_Load: false,\r\n  Doc_File: '',\r\n  ishistory: true,\r\n  Is_Skip_Production: false,\r\n  ProfessionalCode: '',\r\n  Type: 0,\r\n  Bom_Level: '-1',\r\n  Template_Type: 2 // 1：动态模板，2：固定模板\r\n}\r\nexport default {\r\n  components: { OSSUpload },\r\n  props: {\r\n    typeEntity: {\r\n      type: Object,\r\n      default: () => {}\r\n    },\r\n    isAutoSplit: {\r\n      type: [Boolean, undefined],\r\n      default: undefined\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters('tenant', ['isVersionFour'])\r\n  },\r\n  data() {\r\n    return {\r\n      isDynamicTemplate: false,\r\n      btnLoading: false,\r\n      type: '',\r\n      areaType: 2,\r\n      allowFile: 'image/*,video/*,.txt,.pdf,.xlsx,.xls,.docx,.doc,.zip,.rar,.dwg,.nwd,.rvt,.ifc,.bzip,.bzip2',\r\n      fileList: [],\r\n      dialogVisible: false,\r\n      title: '上传文件',\r\n      loading: false,\r\n      form: { ...form },\r\n      attachments: [],\r\n      rules: {\r\n        Doc_Title: [\r\n          { required: true, message: '请输入标题', trigger: 'blur' }\r\n        ]\r\n        // Is_Auto_Split: [\r\n        //   { required: true, message: '请选择', trigger: 'change' }\r\n        // ]\r\n      },\r\n      fileType: '',\r\n      curFile: '',\r\n      bimvizId: '',\r\n      isDeep: false,\r\n      projectId: '',\r\n      isSHQD: '',\r\n      command: 'cover'\r\n    }\r\n  },\r\n  watch: {\r\n    isAutoSplit(newValue, oldValue) {\r\n      this.$set(this.form, 'Is_Auto_Split', newValue)\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$set(this.form, 'Is_Auto_Split', this.isAutoSplit)\r\n  },\r\n  created() {\r\n    this.fileType = this.$route.name\r\n  },\r\n\r\n  methods: {\r\n    onExceed() {\r\n      this.$message.error('只能上传一个文件')\r\n    },\r\n    getTemplate() {\r\n      console.log(this.form.Type, 'this.form.Type')\r\n      console.log(this.form.Template_Type, 'form.Template_Type')\r\n      const query = { ProfessionalCode: this.form.ProfessionalCode, Type: this.form.Type }\r\n      // if (this.form.Type === 1) {\r\n      query.Template_Type = this.form.Template_Type\r\n      // }\r\n      console.log(query, 'query=======')\r\n      ThreeBomImportTemplate({ }).then(res => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        // this.downFile(res.Data)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 通过文件下载url拿到对应的blob对象\r\n    getBlob(url) {\r\n      return new Promise(resolve => {\r\n        const xhr = new XMLHttpRequest()\r\n        xhr.open('GET', url, true)\r\n        xhr.responseType = 'blob'\r\n        xhr.onload = () => {\r\n          if (xhr.status === 200) {\r\n            resolve(xhr.response)\r\n          }\r\n        }\r\n\r\n        xhr.send()\r\n        console.log(xhr)\r\n      })\r\n    },\r\n    // 下载文件 　　js模拟点击a标签进行下载\r\n    saveAs(blob, filename) {\r\n      var link = document.createElement('a')\r\n      link.href = window.URL.createObjectURL(blob)\r\n      link.download = filename\r\n      link.click()\r\n    },\r\n    // 文件下载\r\n    downFile(fileUrl) {\r\n      this.getBlob(fileUrl).then(blob => {\r\n        this.saveAs(blob, '信用权证使用导入模板件名.xlsx')\r\n      })\r\n    },\r\n\r\n    handleChange(file, fileList) {\r\n      this.fileList = fileList.slice(-1)\r\n      if (fileList.length > 1) {\r\n        this.attachments.splice(-1)\r\n        this.form.Doc_File = ''\r\n        this.form.Doc_Content = ''\r\n        this.form.Doc_Title = ''\r\n      }\r\n    },\r\n    beforeUpload(file) {\r\n      this.curFile = file\r\n      console.log('beforeFile', file)\r\n      this.loading = true\r\n      this.btnLoading = true\r\n    },\r\n    beforeRemove(file) {\r\n      return this.$confirm(`确定移除 ${file.name}？`)\r\n    },\r\n    handleRemove(file, fileList) {\r\n      let i = 0\r\n      this.fileList.filter((item, index) => {\r\n        if (item.name === file.name) {\r\n          i = index\r\n        }\r\n      })\r\n      this.fileList.splice(i, 1)\r\n      this.attachments.splice(i, 1)\r\n      this.form.Doc_File = this.form.Doc_File.replace(file.name, '')\r\n      this.form.Doc_Content = this.form.Doc_File.replace(file.name, '')\r\n      this.form.Doc_Title = this.form.Doc_File.replace(file.name, '')\r\n      console.log('fileList', fileList)\r\n      this.loading = !fileList.every((item) => item.status === 'success')\r\n      setTimeout(() => {\r\n        this.btnLoading = !fileList.every((item) => item.status === 'success')\r\n      }, 1000)\r\n    },\r\n    uploadError(err, file, fileList) {\r\n      this.$message.error(`${file.name}上传失败`)\r\n    },\r\n    uploadSuccess(response, file, fileList) {\r\n      console.log('response', response)\r\n      console.log('uploadSuccess', file)\r\n      console.log('uploadSuccessList', fileList)\r\n      this.fileList = fileList\r\n      this.attachments.push(\r\n        {\r\n          File_Url: response.Data.split('*')[0],\r\n          File_Size: response.Data.split('*')[1],\r\n          File_Type: response.Data.split('*')[2],\r\n          File_Name: response.Data.split('*')[3]\r\n        }\r\n      )\r\n      const title = this.form.Doc_Title + (this.form.Doc_Title ? ',' : '') + response.Data.split('*')[3]\r\n      this.form.Doc_Title = title.substring(0, title.lastIndexOf('.'))\r\n      this.form.Doc_Content = this.form.Doc_Title\r\n      this.form.Doc_File = this.form.Doc_File + (this.form.Doc_File ? ',' : '') + response.Data.split('*')[3]\r\n      this.loading = !fileList.every((item) => item.status === 'success')\r\n      setTimeout(() => {\r\n        this.btnLoading = !fileList.every((item) => item.status === 'success')\r\n      }, 1000)\r\n    },\r\n    // isDeep是否是从构件管理打开的(深化)\r\n    handleOpen(type, row, bimvizId, isDeep = false, projectId, importType, productionConfirm, command = 'cover', customParams) {\r\n      this.projectId = projectId\r\n      this.isDeep = isDeep\r\n      this.form = Object.assign(this.form, form)\r\n\r\n      this.form.Type_Name = row.name\r\n      this.form.Doc_Type = row.Id\r\n      this.form.Doc_Catelog = row.Catalog_Code\r\n      this.isSHQD = row.isSHQD\r\n      this.form.ProfessionalCode = row.Code\r\n      this.dialogVisible = true\r\n      this.type = type\r\n      this.bimvizId = bimvizId\r\n      this.form.Type = importType\r\n      // this.form.Is_Skip_Production = (productionConfirm === '' ? false : productionConfirm)\r\n      this.command = command\r\n      console.log(command, 'command========')\r\n      this.form.Project_Name = customParams.Project_Name\r\n      this.form.Sys_Project_Id = customParams.Sys_Project_Id\r\n      this.form.Area_Name = customParams.Area_Name\r\n      this.form.Area_Id = customParams.Area_Id\r\n      this.isDynamicTemplate = false\r\n      this.Template_Type = 2\r\n\r\n      if (this.type === 'add') {\r\n        this.fileList = []\r\n        // this.title = '新增文件'\r\n        this.form.Id = ''\r\n        // this.$delete(this.form, \"Id\");\r\n      }\r\n      // importType  1:增量导入，2：覆盖导入 3：部分覆盖\r\n      if (this.command === 'cover') {\r\n        this.title = '覆盖文件'\r\n        this.form.ImportType = 2\r\n      } else if (this.command === 'add') {\r\n        this.title = '新增文件'\r\n        this.form.ImportType = 1\r\n      } else if (this.command === 'halfcover') {\r\n        this.title = '部分覆盖导入'\r\n        this.form.ImportType = 3\r\n      }\r\n      this.$set(this.form, 'Is_Auto_Split', this.isAutoSplit)\r\n    },\r\n    handleClose() {\r\n      try {\r\n        this.attachments = []\r\n        this.$refs['form'].resetFields()\r\n        this.btnLoading = false\r\n        this.loading = false\r\n        this.fileList = []\r\n        this.dialogVisible = false\r\n      } catch (e) {\r\n\r\n      }\r\n    },\r\n    handleSubmit(IsOk = false) {\r\n      this.$refs['form'].validate(async(valid) => {\r\n        if (valid) {\r\n          this.loading = true\r\n          this.btnLoading = true\r\n          // this.$delete(this.form, 'Type_Name')\r\n          this.updateInfo(IsOk)\r\n        } else {\r\n          this.$message({\r\n            message: '请将表单填写完整',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n\r\n    async updateInfo(IsOk) {\r\n      // Type 1零构件 0 构件\r\n      const form = { ...this.form, IsOk }\r\n      // if (form.Type === 0) {\r\n      //   delete form['Template_Type']\r\n      // }\r\n      console.log(form, 'form=========')\r\n      // if (form.Is_Auto_Split && form.Type === 1) {\r\n      //   this.getSplitInfo().then(() => {\r\n      //     this.updatePartAggregateId()\r\n      //   })\r\n      //   return\r\n      // }\r\n      this.submitAdd(form)\r\n      // if (this.command === 'cover') {\r\n      //   console.log(this.command, 'command========cover')\r\n      //   this.submitCoverAdd(form)\r\n      // } else if (this.command === 'add') {\r\n      //   console.log(this.command, 'command========add')\r\n      //   this.submitAdd(form)\r\n      // } else if (this.command === 'halfcover') {\r\n      //   console.log(this.command, 'command========add')\r\n      //   this.submitCoverAdd(form)\r\n      // }\r\n    },\r\n\r\n    async submitCoverAdd(form) {\r\n      try {\r\n        const res = await AppendImportDeepFiles({ ...form, AttachmentList: this.attachments })\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '保存成功',\r\n            type: 'success'\r\n          })\r\n          await this.updatePartAggregateId() // 确保在保存成功后执行\r\n          this.$emit('getData', this.form.Doc_Type)\r\n          this.$emit('getProjectAreaData')\r\n          this.handleClose()\r\n        } else {\r\n          res.Data && window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n          this.$message.error(res.Message)\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('保存失败')\r\n      } finally {\r\n        this.loading = false\r\n        this.btnLoading = false\r\n      }\r\n    },\r\n\r\n    async submitAdd(form) {\r\n      try {\r\n        const _form = { ...form }\r\n        if (this.areaType === 2) {\r\n          _form.Area_Id = undefined\r\n          _form.Area_Name = undefined\r\n        }\r\n        const res = await AppendImportDeepFiles({ ..._form, AttachmentList: this.attachments })\r\n\r\n        if (res.IsSucceed) {\r\n          if (!res.Data) {\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            await this.updatePartAggregateId() // 确保在保存成功后执行\r\n            this.$emit('getData', this.form.Doc_Type)\r\n            this.$emit('getProjectAreaData')\r\n            this.handleClose()\r\n          } else {\r\n            this.$confirm(res.Data, '提示', {\r\n              confirmButtonText: '确定',\r\n              cancelButtonText: '取消',\r\n              type: 'warning'\r\n            }).then(() => {\r\n              this.handleSubmit(true)\r\n            }).catch(() => {\r\n              this.$message({\r\n                type: 'info',\r\n                message: '已取消'\r\n              })\r\n            })\r\n          }\r\n        } else {\r\n          res.Data && window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n          this.$message.error(res.Message)\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('保存失败')\r\n      } finally {\r\n        this.loading = false\r\n        this.btnLoading = false\r\n      }\r\n    },\r\n    getSplitInfo() {\r\n      const { ProfessionalCode,\r\n        Type,\r\n        Is_Skip_Production,\r\n        Sys_Project_Id,\r\n        Area_Id\r\n      } = this.form\r\n      const obj = {\r\n        'ProfessionalCode': ProfessionalCode,\r\n        'Type': Type,\r\n        'Is_Skip_Production': Is_Skip_Production,\r\n        'Sys_Project_Id': Sys_Project_Id,\r\n        'Area_Id': Area_Id,\r\n        'AttachmentList': this.attachments,\r\n        'Is_Auto_Split': true\r\n      }\r\n      GenerateDeepenFileFromDirect(obj).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.open(res.Data)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    async  updatePartAggregateId() {\r\n      console.log('更新成功=========')\r\n      await UpdatePartAggregateId({ AreaId: this.form.Area_Id }).then((res) => {\r\n        if (!res.IsSucceed) {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    open(url) {\r\n      const h = this.$createElement\r\n      let fileName = ''\r\n      const match = url.match(/\\/([^/]+\\.xls)$/)\r\n      if (match) {\r\n        fileName = match[1]\r\n      }\r\n      const form = { ...this.form }\r\n      // if (form.Type === 0) {\r\n      //   delete form['Template_Type']\r\n      // }\r\n      this.$msgbox({\r\n        title: '提示',\r\n        message: h('div', null, [\r\n          h('div', null, '清单已拆分完成, 是否确定导入?'),\r\n          h('a', {\r\n            attrs: {\r\n              href: combineURL(this.$baseUrl, url),\r\n              target: '_blank',\r\n              style: 'color: #298DFF'\r\n            }\r\n          }, fileName)\r\n        ]),\r\n        showCancelButton: true,\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        beforeClose: async(action, instance, done) => {\r\n          if (action === 'confirm') {\r\n            instance.confirmButtonLoading = true\r\n            instance.confirmButtonText = '提交...'\r\n            // if (this.command === 'cover') {\r\n            //   await this.submitCoverAdd(form)\r\n            // } else if (this.command === 'add') {\r\n            //   await this.submitAdd(form)\r\n            // }\r\n            await this.submitAdd(form)\r\n            done()\r\n            setTimeout(() => {\r\n              instance.confirmButtonLoading = false\r\n            }, 300)\r\n          } else {\r\n            this.loading = false\r\n            this.btnLoading = false\r\n            done()\r\n          }\r\n        }\r\n      }).then(action => {\r\n\r\n      })\r\n    },\r\n    radioChange(val) {\r\n      if (val === 1) {\r\n        this.isDynamicTemplate = true\r\n        this.form.Is_Auto_Split = undefined\r\n      } else {\r\n        this.isDynamicTemplate = false\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .cs-alert {\r\n    position: relative;\r\n    height: 38px;\r\n    line-height: 38px;\r\n    color: #F5C15A;\r\n    border-radius: 4px;\r\n    margin-bottom: 30px;\r\n\r\n    &-info {\r\n      color: #298DFF;\r\n    }\r\n\r\n    .el-icon-warning-outline {\r\n      margin-left: 16px;\r\n    }\r\n\r\n    &:after {\r\n      content: '';\r\n      top: 0;\r\n      left: 0;\r\n      position: absolute;\r\n      width: 100%;\r\n      height: 100%;\r\n      background: #F5C15A;\r\n      opacity: 0.12;\r\n      pointer-events: none;\r\n    }\r\n  }\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\plan\\add.vue?vue&type=style&index=0&id=637445e4&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\plan\\add.vue", "mtime": 1757468128084}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi50Yi1zdGF0dXMgewogIGJhY2tncm91bmQ6ICNmYWU2YmI7CiAgcGFkZGluZzogMTZweCAyMHB4OwogIGZvbnQtc2l6ZTogMS4yZW07CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgZGlzcGxheTogZmxleDsKCiAgKiB7CiAgICBtYXJnaW4tcmlnaHQ6IDEycHg7CiAgfQp9CgouZWwtZm9ybSB7CiAgbWFyZ2luOiAxNnB4IDEwcHg7Cn0KCi50aXRsZSB7CiAgbWFyZ2luLWxlZnQ6IDEwcHg7Cn0KCi5jcy1yZWR7CiAgY29sb3I6cmVkCn0KCi5zdGF0aXN0aWNzLWNvbnRhaW5lciB7CiAgZGlzcGxheTogZmxleDsKICAuc3RhdGlzdGljcy1pdGVtIHsKICAgIG1hcmdpbi1yaWdodDogMzJweDsKICAgIC5jcy1sYWJlbHsKICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogICAgICBmb250LXNpemU6IDE0cHg7CiAgICAgIGxpbmUtaGVpZ2h0OiAxOHB4OwogICAgICBmb250LXdlaWdodDogNTAwOwogICAgICBjb2xvcjogIzk5OTk5OTsKICAgICAgbWFyZ2luLWxlZnQ6IDEwcHg7CiAgICAgIC8vIG1hcmdpbi1yaWdodDogMTZweDsKICAgIH0KICAgIC5jcy1udW0gewogICAgICBmb250LXNpemU6IDE2cHg7CiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7CiAgICAgIGNvbG9yOiAjMDBjMzYxOwogICAgfQogIH0KfQouaGVhZGVyewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgLnJpZ2h0ewogICAgZGlzcGxheTogZmxleDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgfQp9Cg=="}, {"version": 3, "sources": ["add.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8jBA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "add.vue", "sourceRoot": "src/views/PRO/shipment/plan", "sourcesContent": ["<template>\r\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\r\n    <div class=\"cs-z-page-main-content\">\r\n      <top-header>\r\n        <template #left>\r\n          <div class=\"cs-header\">\r\n            <el-button @click=\"toBack\">返回</el-button>\r\n          </div>\r\n        </template>\r\n        <template #right>\r\n          <ExportCustomReport v-if=\"form.Id\" code=\"Shipping_plan_template\" style=\"margin:0 10px\" name=\"导出派工单\" :ids=\"[form.Id]\" />\r\n          <template v-if=\"!readonly\">\r\n            <el-button type=\"primary\" :loading=\"loading\" @click=\"handleSubmit(1)\">保存草稿</el-button>\r\n            <el-button type=\"primary\" :loading=\"loading\" @click=\"handleSubmit(2)\">提交</el-button>\r\n          </template>\r\n        </template>\r\n      </top-header>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"110px\">\r\n        <el-row>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"发货计划单号\" prop=\"Code\">\r\n              <el-input\r\n                v-model=\"form.Code\"\r\n                :disabled=\"true\"\r\n                placeholder=\"自动生成\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"项目名称\" prop=\"projectName\">\r\n              <el-input v-model=\"form.projectName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"装车人\" prop=\"Loader_UserId\">\r\n              <el-select v-model=\"form.Loader_UserId\" :disabled=\"readonly\" placeholder=\"请选择\" clearable filterable style=\"width: 100%\">\r\n                <el-option v-for=\"item in allUsers\" :key=\"item.id\" :value=\"item.Id\" :label=\"item.Display_Name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"发货人\" prop=\"Shipper_UserId\">\r\n              <el-select v-model=\"form.Shipper_UserId\" :disabled=\"readonly\" placeholder=\"请选择\" clearable filterable style=\"width: 100%\">\r\n                <el-option v-for=\"item in allUsers\" :key=\"item.id\" :value=\"item.Id\" :label=\"item.Display_Name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"计划发货日期\" prop=\"Plan_Date\">\r\n              <el-date-picker\r\n                v-model=\"form.Plan_Date\"\r\n                :disabled=\"readonly\"\r\n                placeholder=\"请选择\"\r\n                style=\"width: 100%\"\r\n                type=\"date\"\r\n                value-format=\"yyyy-MM-dd\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"计划编制人\" prop=\"Plan_UserId\">\r\n              <el-select v-model=\"form.Plan_UserId\" :disabled=\"readonly\" placeholder=\"请选择\" clearable filterable style=\"width: 100%\">\r\n                <el-option v-for=\"item in allUsers\" :key=\"item.id\" :value=\"item.Id\" :label=\"item.Display_Name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"备注\">\r\n              <el-input\r\n                v-model=\"form.Remark\"\r\n                :disabled=\"readonly\"\r\n                :autosize=\"{ minRows: 2, maxRows: 2 }\"\r\n                :maxlength=\"500\"\r\n                show-word-limit\r\n                type=\"textarea\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n\r\n      <div class=\"header\">\r\n        <el-form inline label-width=\"70px\">\r\n          <el-form-item label=\"构件名称\"><el-input v-model=\"query.code\" clearable /></el-form-item>\r\n          <el-form-item label=\"区域\">\r\n            <el-tree-select\r\n              ref=\"treeSelectArea\"\r\n              v-model=\"query.area\"\r\n              class=\"treeselect\"\r\n              :select-params=\"selectParams\"\r\n              :tree-params=\"treeParamsArea\"\r\n              @searchFun=\"filterFun($event, 'treeSelectArea')\"\r\n              @select-clear=\"areaClear\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\">\r\n            <el-select v-model=\"query.status\" clearable>\r\n              <el-option label=\"未完成\" value=\"未完成\" />\r\n              <el-option label=\"已完成\" value=\"已完成\" />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item><el-button type=\"primary\" @click=\"search\">搜索</el-button></el-form-item>\r\n        </el-form>\r\n        <div class=\"right\">\r\n          <div style=\"margin-right: 10px\">理论总重：{{ sumWeight }}t</div>\r\n          <template v-if=\"!readonly\">\r\n            <el-button\r\n              :disabled=\"!selectList.length\"\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleDelete\"\r\n            >删除</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >添加</el-button>\r\n          </template>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-loading=\"tbLoading\" class=\"fff cs-z-tb-wrapper\">\r\n        <vxe-table\r\n          ref=\"vxeTable\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          empty-text=\"暂无数据\"\r\n          height=\"auto\"\r\n          show-overflow\r\n          :loading=\"tbLoading\"\r\n          class=\"cs-vxe-table\"\r\n          align=\"left\"\r\n          stripe\r\n          :data=\"filterData\"\r\n          resizable\r\n          :edit-config=\"{trigger: 'click', mode: 'cell', activeMethod: activeCellMethod}\"\r\n          :tooltip-config=\"{ enterable: true }\"\r\n          @checkbox-change=\"checkboxChange\"\r\n          @checkbox-all=\"selectAllCheckboxChange\"\r\n        >\r\n          <vxe-column v-if=\"!readonly\" type=\"checkbox\" width=\"60\" />\r\n          <template v-for=\"item in columns\">\r\n            <vxe-column\r\n              v-if=\"item.Code === 'Plan_Count' && !readonly\"\r\n              :key=\"item.Code\"\r\n              :field=\"item.Code\"\r\n              :align=\"item.Align\"\r\n              :title=\"item.Display_Name\"\r\n              sortable\r\n              :edit-render=\"{}\"\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #edit=\"{ row }\">\r\n                <vxe-input\r\n                  v-model=\"row.Plan_Count\"\r\n                  type=\"integer\"\r\n                  :min=\"1\"\r\n                  @change=\"(val)=>sumItem(row)\"\r\n                />\r\n              </template>\r\n              <template #default=\"{ row }\">\r\n                {{ row.Plan_Count }}\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column\r\n              v-else-if=\"item.Code === 'Is_Direct'\"\r\n              :key=\"item.Code\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              sortable\r\n              :width=\"item.Width\"\r\n              :align=\"item.Align\"\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                {{ row.Is_Direct==true ? \"是\" : \"否\" }}\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column\r\n              v-else\r\n              :key=\"item.Code\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :align=\"item.Align\"\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                {{ row[item.Code] ? row[item.Code] : \"-\" }}\r\n              </template>\r\n            </vxe-column>\r\n          </template>\r\n        </vxe-table>\r\n      </div>\r\n      <el-dialog\r\n        v-if=\"dialogVisible\"\r\n        v-dialogDrag\r\n        class=\"plm-custom-dialog\"\r\n        :title=\"title\"\r\n        :visible.sync=\"dialogVisible\"\r\n        :width=\"width\"\r\n        :top=\"topDialog\"\r\n        @close=\"close\"\r\n      >\r\n        <component\r\n          :is=\"currentComponent\"\r\n          ref=\"content\"\r\n          :dialog-visible=\"dialogVisible\"\r\n          :project-id=\"projectId\"\r\n          :sys-project-id=\"form.Sys_Project_Id\"\r\n          :checked-data=\"tbData\"\r\n          @close=\"close\"\r\n          @reCount=\"getTotal\"\r\n          @selectList=\"addSelectList\"\r\n        />\r\n      </el-dialog>\r\n      <check-info ref=\"info\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport TitleInfo from '@/views/PRO/shipment/actually-sent/v3/component/TitleInfo'\r\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'\r\nimport TopHeader from '@/components/TopHeader'\r\nimport CheckInfo from '@/views/PRO/Component/GetPackingDetail'\r\nimport { closeTagView, parseTime } from '@/utils'\r\nimport { GetProjectEntity, GetProjectPageList } from '@/api/PRO/pro-schedules'\r\nimport { GetAllUserPage, GetGridByCode } from '@/api/sys'\r\nimport numeral from 'numeral'\r\nimport AddDialog from './component/add'\r\nimport { GetLastUser, GetOutPlanEntity, SaveOutPlan } from '@/api/PRO/ship-plan'\r\nimport { GeAreaTrees } from '@/api/PRO/project'\r\nimport ExportCustomReport from \"@/components/ExportCustomReport/index.vue\";\r\n\r\nexport default {\r\n  name: 'ShipPlanDetail',\r\n  components: {\r\n    ExportCustomReport,\r\n    TitleInfo,\r\n    TopHeader,\r\n    DynamicDataTable,\r\n    CheckInfo,\r\n    AddDialog\r\n  },\r\n  data() {\r\n    return {\r\n      query: {\r\n        code: '',\r\n        area: '',\r\n        status: ''\r\n      },\r\n      width: '80vw',\r\n      currentComponent: '',\r\n      title: '',\r\n      dialogVisible: false,\r\n      topDialog: '5vh',\r\n      sendNumber: '',\r\n      totalNum: '',\r\n      totalWeight: '',\r\n      form: {\r\n        Sys_Project_Id: '',\r\n        Remark: '',\r\n        Loader_UserId: '',\r\n        Shipper_UserId: '',\r\n        Plan_UserId: localStorage.getItem('UserId'),\r\n        Plan_Date: '',\r\n        Status: '', // 状态, 1:草稿，2：审批中，3：结束，-1：已驳回\r\n        projectName: ''\r\n      },\r\n      rules: {\r\n        projectId: [{ required: true, message: '请选择项目', trigger: 'change' }],\r\n        // Loader_UserId: [{ required: true, message: '请选择装车人', trigger: 'change' }],\r\n        // Shipper_UserId: [{ required: true, message: '请选择发货人', trigger: 'change' }],\r\n        Plan_Date: [{ required: true, message: '请选计划发货日期', trigger: 'change' }],\r\n        Plan_UserId: [{ required: true, message: '请选计划编制人', trigger: 'change' }]\r\n      },\r\n      PageInfo: {\r\n        ParameterJson: [],\r\n        Page: 1,\r\n        PageSize: 20\r\n      },\r\n      plm_ProjectSendingInfo: {},\r\n      Itemdetail: [],\r\n      projects: '',\r\n      Id: '',\r\n      projectId: '',\r\n      tbConfig: {\r\n        Pager_Align: 'center'\r\n      },\r\n      columns: [],\r\n      tbData: [],\r\n      total: 0,\r\n      tbLoading: false,\r\n      selectList: [],\r\n      sums: [],\r\n      allUsers: [],\r\n      loading: false,\r\n      filterData: [],\r\n      type: 'view',\r\n      selectParams: {\r\n        placeholder: '请选择',\r\n        clearable: true\r\n      },\r\n      // 区域数据\r\n      treeParamsArea: {\r\n        'check-strictly': true,\r\n        'expand-on-click-node': false,\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Label'\r\n        }\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    sumWeight() {\r\n      let sum = 0\r\n      this.filterData.forEach(item => {\r\n        sum += Number(item.Total_Weight)\r\n      })\r\n      return (sum / 1000).toFixed(5)\r\n    },\r\n    readonly() {\r\n      return this.type === 'view'\r\n    }\r\n  },\r\n  watch: {\r\n  },\r\n  created() {\r\n    this.getAllUsers()\r\n    this.getTableConfig()\r\n  },\r\n  async mounted() {\r\n    this.type = this.$route.query.type || 'add'\r\n    if (this.type === 'add') {\r\n      const {\r\n        Name,\r\n        Id,\r\n        Sys_Project_Id\r\n      } = JSON.parse(decodeURIComponent(this.$route.query.p))\r\n      this.projectId = Id\r\n      this.form.projectName = Name\r\n      this.form.Sys_Project_Id = Sys_Project_Id\r\n      this.getProjectEntity(this.projectId)\r\n      this.getLastUser()\r\n    } else {\r\n      const {\r\n        Name\r\n      } = JSON.parse(decodeURIComponent(this.$route.query.p))\r\n      console.log(JSON.parse(decodeURIComponent(this.$route.query.p)))\r\n      this.form.projectName = Name\r\n      this.getInfo()\r\n    }\r\n  },\r\n  methods: {\r\n    sumItem(row) {\r\n      row.Total_Weight = (row.SteelWeight * row.Plan_Count).toFixed(2)\r\n    },\r\n    checkboxChange() {\r\n      this.selectList = this.$refs.vxeTable.getCheckboxRecords()\r\n    },\r\n    selectAllCheckboxChange() {\r\n      this.selectList = this.$refs.vxeTable.getCheckboxRecords()\r\n    },\r\n    filterFun(val, ref) {\r\n      this.$refs[ref].filterFun(val)\r\n    },\r\n    // 清空区域\r\n    areaClear() {\r\n      this.query.Area_Id = ''\r\n    },\r\n    // 获取区域\r\n    getAreaList() {\r\n      GeAreaTrees({\r\n        sysProjectId: this.form.Sys_Project_Id\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.treeParamsArea.data = res.Data\r\n          this.$nextTick((_) => {\r\n            this.$refs.treeSelectArea.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    search() {\r\n      this.filterData = this.tbData.filter(item => {\r\n        return (item.Component_Code || '').includes(this.query.code) && (item.FullAreaposition || '').includes(this.query.area) && (item.Status || '').includes(this.query.status)\r\n      })\r\n    },\r\n    getLastUser() {\r\n      GetLastUser({\r\n        Sys_Project_Id: this.form.Sys_Project_Id\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.form.Loader_UserId = res.Data.Loader_UserId\r\n          this.form.Shipper_UserId = res.Data.Shipper_UserId\r\n        }\r\n      })\r\n    },\r\n    activeCellMethod({ row, column, columnIndex }) {\r\n      return column.field === 'Plan_Count'\r\n    },\r\n    async handleSubmit(status) {\r\n      await this.$refs['form'].validate()\r\n      if (!this.tbData || !this.tbData.length) {\r\n        this.$message.error('请添加明细')\r\n        return\r\n      }\r\n      let flag = false\r\n      this.tbData.forEach(item => {\r\n        if (item.Plan_Count < 1) {\r\n          flag = true\r\n        }\r\n      })\r\n      if (flag) {\r\n        this.$message.error('应发数量不能小于1')\r\n        return\r\n      }\r\n      this.isClicked = true\r\n      const submitObj = {\r\n        Main: {\r\n          ...this.form,\r\n          Status: status\r\n        },\r\n        Details: this.tbData\r\n      }\r\n      const res = await SaveOutPlan(submitObj)\r\n      if (res.IsSucceed) {\r\n        this.$message.success('保存成功')\r\n        this.toBack()\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    getNum(a, b) {\r\n      return numeral(a).subtract(b).format('0.[000]')\r\n    },\r\n    getProjectEntity(Id) {\r\n      GetProjectEntity({ Id }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const Consignee = res.Data.Contacts.find((item) => {\r\n            return item.Type == 'Consignee'\r\n          })\r\n          console.log(Consignee, 'Consignee')\r\n          this.form.receiveName = Consignee?.Name\r\n          this.form.Receiver_Tel = Consignee?.Tel\r\n        }\r\n      })\r\n    },\r\n    getProjectPageList() {\r\n      GetProjectPageList({ PageSize: -1 }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.projects = res.Data.Data\r\n        }\r\n      })\r\n    },\r\n    toBack() {\r\n      closeTagView(this.$store, this.$route)\r\n    },\r\n    getInfo() {\r\n      GetOutPlanEntity({\r\n        id: this.$route.query.id\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const name = this.form.projectName\r\n          this.form = res.Data.Main\r\n          this.form.projectName = name\r\n          this.form.projectId = this.form.Sys_Project_Id\r\n          this.tbData = res.Data.Details.map(item => {\r\n            item.Status = ((item.Accept_Count - item.Plan_Count >= 0) && item.Accept_Count) ? '已完成' : '未完成'\r\n            return item\r\n          })\r\n          this.search()\r\n          this.getAreaList()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    addSelectList(list) {\r\n      list.forEach((item) => {\r\n        item.Status = '未完成'\r\n        this.tbData.push(item)\r\n      })\r\n      this.total = this.tbData.length\r\n      this.search()\r\n    },\r\n    getTotal() {},\r\n    handleAdd() {\r\n      this.currentComponent = 'AddDialog'\r\n      this.dialogVisible = true\r\n      this.title = '新增'\r\n    },\r\n    handleDelete() {\r\n      this.$confirm('删除该数据, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          this.selectList.forEach((item) => {\r\n            const index = this.tbData.findIndex((v) => v.Component_Id === item.Component_Id)\r\n            index !== -1 && this.tbData.splice(index, 1)\r\n          })\r\n          this.search()\r\n          this.$message({\r\n            type: 'success',\r\n            message: '删除成功!'\r\n          })\r\n        })\r\n        .catch(() => {})\r\n    },\r\n    close() {\r\n      this.dialogVisible = false\r\n    },\r\n    handleInfo(row) {\r\n      this.$refs.info.handleOpen(row)\r\n    },\r\n    getTableConfig() {\r\n      return new Promise((resolve) => {\r\n        GetGridByCode({\r\n          code: 'PROShipPlanDetail'\r\n        }).then((res) => {\r\n          const { IsSucceed, Data, Message } = res\r\n          if (IsSucceed) {\r\n            if (!Data) {\r\n              this.$message({\r\n                message: '表格配置不存在',\r\n                type: 'error'\r\n              })\r\n              return\r\n            }\r\n            this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n            this.columns = Data.ColumnList.map((item) => {\r\n              item.Is_Resizable = true\r\n              return item\r\n            })\r\n            this.PageInfo.PageSize = +Data.Grid.Row_Number\r\n            resolve(this.columns)\r\n          } else {\r\n            this.$message({\r\n              message: Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    getAllUsers() {\r\n      GetAllUserPage().then(res => {\r\n        this.allUsers = res.Data.Data\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .tb-status {\r\n    background: #fae6bb;\r\n    padding: 16px 20px;\r\n    font-size: 1.2em;\r\n    font-weight: bold;\r\n    display: flex;\r\n\r\n    * {\r\n      margin-right: 12px;\r\n    }\r\n  }\r\n\r\n  .el-form {\r\n    margin: 16px 10px;\r\n  }\r\n\r\n  .title {\r\n    margin-left: 10px;\r\n  }\r\n\r\n  .cs-red{\r\n    color:red\r\n  }\r\n\r\n  .statistics-container {\r\n    display: flex;\r\n    .statistics-item {\r\n      margin-right: 32px;\r\n      .cs-label{\r\n        display: inline-block;\r\n        font-size: 14px;\r\n        line-height: 18px;\r\n        font-weight: 500;\r\n        color: #999999;\r\n        margin-left: 10px;\r\n        // margin-right: 16px;\r\n      }\r\n      .cs-num {\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        color: #00c361;\r\n      }\r\n    }\r\n  }\r\n  .header{\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    .right{\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n  }\r\n</style>\r\n"]}]}
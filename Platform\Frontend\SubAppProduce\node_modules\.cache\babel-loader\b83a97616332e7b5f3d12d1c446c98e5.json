{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\detail.vue", "mtime": 1758519821153}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getTbInfo", "FIX_COLUMN", "AdjustTeamProcessAllocation", "GetTeamProcessAllocation", "AdjustPartTeamProcessAllocation", "AdjustSubAssemblyTeamProcessAllocation", "GetStopList", "GetPreStepTaskAllocation", "QrcodeVue", "v4", "uuidv4", "numeral", "closeTagView", "deepClone", "GetCompTypeTree", "GetBOMInfo", "getBomCode", "getBomName", "checkIsUnitPart", "SPLIT_SYMBOL", "components", "filters", "filterNum", "value", "divide", "format", "mixins", "data", "treeSelectParams", "placeholder", "clearable", "ObjectTypeList", "clickParent", "props", "children", "label", "tbLoading", "loading", "activeName", "tipLabel", "tbData", "filterTbData", "multipleSelection", "columns", "workingTeam", "workingTeamColumn", "formInline", "pg_type", "searchType", "type", "queryForm", "Comp_Codes", "Part_Code", "Spec", "Comp_Codes_Vague", "Part_Code_Vague", "dialogVisible", "dialogTipsVisible", "form", "TeamGroup", "rules", "required", "message", "trigger", "Is_Workshop_Enabled", "Working_Process_Id", "bomList", "bomLevel", "bom<PERSON>ame", "computed", "<PERSON><PERSON><PERSON><PERSON>", "isCom", "isUnitPart", "mounted", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "_yield$GetBOMInfo", "list", "rowInfo", "idx", "idx2", "_idx", "_idx2", "idx3", "wrap", "_callee$", "_context", "prev", "next", "sent", "JSON", "parse", "decodeURIComponent", "$route", "query", "other", "console", "log", "Object", "assign", "getTableConfig", "filter", "v", "Is_Display", "map", "item", "includes", "Code", "fixed", "visible", "findIndex", "splice", "getObjectTypeList", "fetchData", "t0", "$message", "stop", "methods", "getRowCCode", "row", "prefix", "arguments", "length", "undefined", "suffix", "arr", "split", "team", "find", "Working_Team_Name", "u", "getRowUnique", "uuid", "Process_Code", "Working_Team_Id", "handleClick", "val", "_this2", "name", "c1", "$refs", "xTable", "getColumnByField", "c2", "c3", "for<PERSON>ach", "element", "codes", "concat", "c", "refreshColumn", "filterZero", "clearCheckboxRow", "allocatedTask", "code", "_this3", "trim", "Process_Type", "Page", "PageSize", "Step", "Bom_Level", "Schduling_Code", "Workshop_Name", "Area_Id", "InstallUnit_Id", "then", "_ref", "_callee2", "res", "_res$Data", "Schduling_Plan", "Sch<PERSON>ling_Comps", "_kk", "_callee2$", "_context2", "IsSucceed", "Data", "planInfoTemp", "getStopList", "initTbData", "Allocation_Teams", "filterData", "Message", "_x", "apply", "finally", "_", "checkMethod", "_ref2", "stopFlag", "_this4", "_callee3", "key", "submitObj", "_callee3$", "_context3", "Id", "Type", "stopMap", "Is_Stop", "$set", "_this5", "queryCode", "codeList", "queryCodeVague", "codeListVague", "searchTbData", "vagueData", "searchVagueTbData", "mergedArray", "uniqueArray", "reduce", "acc", "current", "existingObject", "<PERSON><PERSON><PERSON>ling_Detail_Id", "push", "_this6", "teamKey", "_row$Technology_Path", "processList", "Technology_Path", "newData", "r", "p", "defaultCan_Allocation_Count", "Can_Allocation_Count", "_inputNum", "ele", "index", "max", "getRowUniqueMax", "Count", "Total_Receive_Count", "setInputMax", "checked", "inputChange", "inputValuesKeys", "keys", "endsWith", "startsWith", "curCode", "otherTotal", "x", "<PERSON><PERSON><PERSON><PERSON>_Count", "checkPermissionTeam", "processStr", "processCode", "some", "getSubmitTbInfo", "_this7", "tableData", "stringify", "_loop", "i", "status", "Array", "from", "Set", "groups", "groupsList", "group", "uCode", "uMax", "obj", "Comp_Code", "Again_Count", "Team_Task_Id", "_toConsumableArray", "_ret", "handleSubmit", "_this8", "_this$getSubmitTbInfo", "$confirm", "confirmButtonText", "cancelButtonText", "<PERSON><PERSON><PERSON>", "SarePartsModel", "requestFn", "handleClose", "catch", "<PERSON><PERSON><PERSON><PERSON>", "$store", "tbSelectChange", "array", "records", "getTaskCode", "reverseSelection", "getCheckboxRecords", "unSelectList", "setCheckboxRow", "activeCellMethod", "_ref3", "_column$field", "column", "columnIndex", "field", "workingId", "Batchallocation", "preStepTaskAllocation", "_this9", "Sc<PERSON><PERSON>ling_Detail_Ids", "Working_Process_Code", "preDoAllocation", "handleDialog", "handelData", "_this0", "_loop2", "uniCode", "uniMax", "_loop3", "_i", "_loop4", "k", "$nextTick", "submitForm", "formName", "_this1", "validate", "valid", "doAllocation", "preProcessData", "_this10", "preProcessDataMap", "Map", "set", "Current_Task_Count", "allocateForTeam", "amount", "tarCode", "b<PERSON><PERSON>", "currentTaskCount", "get", "allocated", "Math", "min", "Number", "isMessage", "eligibleTeams", "has", "IsAllNo", "totalAvailable", "sum", "allocatedTaskMax", "remaining", "perTeamAllocation", "floor", "allocateAmount", "selectNum", "_this11", "resetForm", "resetFields", "filterTypeMethod", "_ref4", "option", "filterTypeRecoverMethod", "_ref5", "filterCodeMethod", "_ref6", "filterCodeRecoverMethod", "_ref7", "_this12", "professional", "_this12$$refs", "treeSelectObjectType", "treeDataUpdateFun"], "sources": ["src/views/PRO/plan-production/task-allocation/v4/detail.vue"], "sourcesContent": ["<template>\n  <div v-loading=\"loading\" class=\"app-container abs100\">\n    <el-card class=\"box-card h100\">\n      <h4 class=\"topTitle\"><span />基本信息</h4>\n      <el-form\n        ref=\"formInline\"\n        label-position=\"right\"\n        label-width=\"90px\"\n        :inline=\"true\"\n        :model=\"formInline\"\n        class=\"demo-form-inline\"\n      >\n        <el-row>\n          <el-col :span=\"20\">\n            <el-row>\n              <el-col :span=\"6\">\n                <el-form-item label=\"排产单号:\" label-width=\"75px\" prop=\"Schduling_Code\">\n                  <span>{{ formInline.Schduling_Code }}</span>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"6\">\n                <el-form-item label=\"项目名称:\" prop=\"Project_Name\">\n                  <span>{{ formInline.Project_Name }}</span>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"6\">\n                <el-form-item label=\"区域:\" prop=\"Area_Name\">\n                  <span>{{ formInline.Area_Name }}</span>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"6\">\n                <el-form-item label=\"批次:\" prop=\"Installunit_Name\">\n                  <span>{{ formInline.Installunit_Name }}</span>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"6\">\n                <el-form-item label=\"任务数量:\" label-width=\"75px\" prop=\"Allocation_Count\">\n                  <span>{{ formInline.Allocation_Count }}</span>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"6\">\n                <el-form-item label=\"任务重量:\" prop=\"Allocation_Weight\">\n                  <span>{{ formInline.Allocation_Weight | filterNum }}(t)</span>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"6\">\n                <el-form-item label=\"已完成数量:\" prop=\"Finish_Count\">\n                  <span>{{ formInline.Finish_Count }}</span>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"6\">\n                <el-form-item label=\"已完成重量:\" prop=\"Finish_Weight\">\n                  <span>{{ formInline.Finish_Weight | filterNum }}(t)</span>\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </el-col>\n          <el-col :span=\"4\">\n            <qrcode-vue\n              :size=\"79\"\n              :value=\"formInline.Schduling_Code\"\n              class-name=\"qrcode\"\n              level=\"H\"\n            />\n          </el-col>\n        </el-row>\n      </el-form>\n      <el-divider class=\"elDivder\" />\n      <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\n        <el-tab-pane label=\"待分配\" name=\"first\" />\n        <el-tab-pane\n          v-for=\"(element, index2) in workingTeam\"\n          :key=\"index2\"\n          :label=\"element.Working_Team_Name\"\n          :name=\"`${element.Working_Team_Name}$_$${element.Process_Code}`\"\n        />\n      </el-tabs>\n      <div class=\"tb-options\" :style=\"{'justify-content': !isView ? 'space-between' : 'end'}\">\n        <div>\n          <el-button v-if=\"!isView\" plain type=\"primary\" @click=\"reverseSelection()\">反选</el-button>\n          <el-button v-if=\"!isView\" type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"Batchallocation()\">批量分配</el-button>\n          <el-button v-if=\"!isView && activeName==='first'\" type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"preStepTaskAllocation()\">上道工序同步</el-button>\n        </div>\n        <el-form :inline=\"true\" :model=\"queryForm\" class=\"demo-form-inline\">\n          <el-form-item label=\"规格\">\n            <el-input v-model.trim=\"queryForm.Spec\" clearable placeholder=\"请输入\" />\n          </el-form-item>\n          <el-form-item v-if=\"isCom\" :label=\"`${bomName}类型`\">\n            <el-tree-select\n              ref=\"treeSelectObjectType\"\n              v-model=\"searchType\"\n              style=\"width: 100%\"\n              class=\"cs-tree-x\"\n              :select-params=\"treeSelectParams\"\n              :tree-params=\"ObjectTypeList\"\n              value-key=\"Id\"\n            />\n          </el-form-item>\n          <el-form-item :label=\"`${bomName}名称`\">\n            <el-input v-if=\"isCom\" v-model=\"queryForm.Comp_Codes\" clearable placeholder=\"请输入(空格区分/多个搜索)\" />\n            <el-input v-else v-model=\"queryForm.Part_Code\" clearable placeholder=\"请输入(空格区分/多个搜索)\" />\n            <el-input v-if=\"isCom\" v-model.trim=\"queryForm.Comp_Codes_Vague\" style=\"margin-left: 10px;\" clearable placeholder=\"模糊查找(请输入关键字)\" />\n            <el-input v-else v-model.trim=\"queryForm.Part_Code_Vague\" style=\"margin-left: 10px;\" clearable placeholder=\"模糊查找(请输入关键字)\" />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"filterData\">查询</el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n      <div class=\"tb-x\">\n        <vxe-table\n          ref=\"xTable\"\n          :empty-render=\"{name: 'NotData'}\"\n          show-header-overflow\n          class=\"cs-vxe-table\"\n          :row-config=\"{ isCurrent: true, isHover: true }\"\n          align=\"left\"\n          height=\"100%\"\n          show-overflow\n          :loading=\"tbLoading\"\n          :checkbox-config=\"{checkField: 'checked',checkMethod }\"\n          stripe\n          size=\"medium\"\n          :edit-config=\"{\n            trigger: 'click',\n            mode: 'cell',\n            showIcon: !isView,\n            beforeEditMethod: activeCellMethod,\n          }\"\n          :data=\"filterTbData\"\n          resizable\n          :tooltip-config=\"{ enterable: true }\"\n          @checkbox-all=\"tbSelectChange\"\n          @checkbox-change=\"tbSelectChange\"\n        >\n          <vxe-column type=\"checkbox\" width=\"60\" fixed=\"left\" />\n          <template v-for=\"item in columns\">\n\n            <!--            <vxe-column :align=\"item.Align\"\n              v-if=\"item.Code==='Comp_Code'||item.Code==='Part_Code'\"\n              :key=\"item.Id\"\n              :filters=\"[{ data: '' }]\"\n              :filter-method=\"filterCodeMethod\"\n              :filter-recover-method=\"filterCodeRecoverMethod\"\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n              show-overflow=\"tooltip\"\n              sortable\n              :field=\"item.Code\"\n              :title=\"item.Display_Name\"\n              :min-width=\"item.Width\"\n            >\n              <template #filter=\"{ $panel, column }\">\n                <template v-for=\"(option, index) in column.filters\">\n                  <input :key=\"index\" v-model=\"option.data\" class=\"my-input\" type=\"type\" placeholder=\"按回车确认筛选\" @input=\"$panel.changeOption($event, !!option.data, option)\" @keyup.enter=\"$panel.confirmFilter()\">\n                </template>\n              </template>\n            </vxe-column>\n            <vxe-column :align=\"item.Align\"\n              v-else-if=\"item.Code==='Type'\"\n              :key=\"item.Id\"\n              :filters=\"[{ data: '' }]\"\n              :filter-method=\"filterTypeMethod\"\n              :filter-recover-method=\"filterTypeRecoverMethod\"\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n              show-overflow=\"tooltip\"\n              sortable\n              :field=\"item.Code\"\n              :title=\"item.Display_Name\"\n              :min-width=\"item.Width\"\n            >\n              <template #filter=\"{ $panel, column }\">\n                <template v-for=\"(option, index) in column.filters\">\n                  <input :key=\"index\" v-model=\"option.data\" class=\"my-input\" type=\"type\" placeholder=\"按回车确认筛选\" @input=\"$panel.changeOption($event, !!option.data, option)\" @keyup.enter=\"$panel.confirmFilter()\">\n                </template>\n              </template>\n            </vxe-column>-->\n            <vxe-column\n              v-if=\"item.Code==='Comp_Code'|| item.Code==='Part_Code'\"\n              :key=\"item.Id\"\n              :align=\"item.Align\"\n              :fixed=\"item.fixed\"\n              show-overflow=\"tooltip\"\n              sortable\n              :visible=\"item.visible\"\n              :field=\"item.Code\"\n              :title=\"item.Display_Name\"\n              :min-width=\"item.Width\"\n            >\n              <template #default=\"{ row }\">\n                <el-tag v-if=\"!!row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\n                <span>{{ row[item.Code] }}</span>\n              </template>\n            </vxe-column>\n            <vxe-column\n              v-else-if=\"item.Code==='Schduled_Count'\"\n              :key=\"`SchduledCount${item.Id}`\"\n              :align=\"item.Align\"\n              :fixed=\"item.fixed\"\n              show-overflow=\"tooltip\"\n              sortable\n              :visible=\"item.visible\"\n              :field=\"item.Code\"\n              :title=\"item.Display_Name\"\n              :min-width=\"item.Width\"\n            >\n              <template #default=\"{ row }\">\n                {{ activeName === 'first' ?'':row[getTaskCode()] }}\n              </template>\n            </vxe-column>\n            <vxe-column\n              v-else\n              :key=\"`Default${item.Id}`\"\n              :align=\"item.Align\"\n              :fixed=\"item.fixed\"\n              :visible=\"item.visible\"\n              show-overflow=\"tooltip\"\n              sortable\n              :field=\"item.Code\"\n              :title=\"item.Display_Name\"\n              :min-width=\"item.Width\"\n            />\n          </template>\n\n          <vxe-column\n            v-for=\"(element, index2) in workingTeamColumn\"\n            :key=\"index2\"\n            :align=\"element.Align\"\n            :visible=\"element.visible\"\n            fixed=\"right\"\n            :field=\"`${element.Working_Team_Name}$_$${element.Process_Code}`\"\n            title=\"可分配数量\"\n            sortable\n            min-width=\"170\"\n          >\n            <template #edit=\"{ row }\">\n              <vxe-input\n                v-model.number=\"\n                  row[\n                    getRowUnique(\n                      row.uuid,\n                      element.Process_Code,\n                      element.Working_Team_Id\n                    )\n                  ]\n                \"\n                type=\"integer\"\n                :min=\"0\"\n                :max=\"\n                  row[\n                    getRowUniqueMax(\n                      row.uuid,\n                      element.Process_Code,\n                      element.Working_Team_Id\n                    )\n                  ]\n                \"\n                @change=\"\n                  inputChange(\n                    row,\n                    element.Process_Code,\n                    element.Working_Team_Id\n                  )\n                \"\n              />\n            </template>\n            <template #default=\"{ row }\">\n              <template\n                v-if=\"\n                  checkPermissionTeam(row.Technology_Path, element.Process_Code)\n                \"\n              >\n                {{\n                  row[\n                    getRowUnique(\n                      row.uuid,\n                      element.Process_Code,\n                      element.Working_Team_Id\n                    )\n                  ]\n                }}\n              </template>\n              <template v-else> -</template>\n            </template>\n          </vxe-column>\n\n          <vxe-column\n            :key=\"activeName\"\n            align=\"left\"\n            :edit-render=\"{}\"\n            field=\"AllocatedCount\"\n            title=\"分配数量\"\n            sortable\n            fixed=\"right\"\n            min-width=\"180\"\n          >\n            <template #edit=\"{ row }\">\n              <vxe-input\n                :key=\"activeName\"\n                v-model.number=\"row[getRowCCode(row,'alCount')]\"\n                type=\"integer\"\n                :min=\"0\"\n                :max=\"row[getRowCCode(row,'','Max')]\"\n              />\n            </template>\n            <template #default=\"{ row }\">\n              {{ row[getRowCCode(row,'alCount')] | displayValue }}\n            </template>\n          </vxe-column>\n        </vxe-table>\n      </div>\n      <footer>\n        <div class=\"data-info\">\n          <el-tag size=\"medium\" class=\"info-x\">已选{{ multipleSelection.length }}条数据</el-tag>\n          <el-tag v-if=\"tipLabel\" size=\"medium\" class=\"info-x\">{{ tipLabel }}</el-tag>\n        </div>\n        <div>\n          <el-button @click=\"handleClose\">取消 </el-button>\n          <el-button\n            v-if=\"!isView\"\n            type=\"primary\"\n            :loading=\"loading\"\n            @click=\"handleSubmit\"\n          >提交</el-button>\n        </div>\n      </footer>\n    </el-card>\n    <el-dialog\n      v-dialogDrag\n      title=\"批量分配\"\n      class=\"plm-custom-dialog\"\n      :visible.sync=\"dialogVisible\"\n      width=\"30%\"\n      @close=\"handleDialog\"\n    >\n      <el-form\n        ref=\"form\"\n        :model=\"form\"\n        :rules=\"rules\"\n        label-width=\"80px\"\n        class=\"demo-ruleForm\"\n      >\n        <el-form-item label=\"选择班组\" prop=\"TeamGroup\">\n          <el-select\n            v-model=\"form.TeamGroup\"\n            class=\"w100\"\n            placeholder=\"请选择\"\n            filterable\n            clearable\n          >\n            <el-option\n              v-for=\"item in workingTeam\"\n              :key=\"item.Working_Team_Id\"\n              :label=\"item.Working_Team_Name\"\n              :value=\"item.Working_Team_Id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item style=\"text-align: right\">\n          <el-button @click=\"resetForm('form')\">取 消</el-button>\n          <el-button\n            type=\"primary\"\n            @click=\"submitForm('form');resetForm('form')\"\n          >确 定</el-button>\n        </el-form-item>\n      </el-form>\n    </el-dialog>\n    <el-dialog\n      v-dialogDrag\n      title=\"提示\"\n      class=\"plm-custom-dialog\"\n      :visible.sync=\"dialogTipsVisible\"\n      width=\"450px\"\n      @close=\"handleDialog\"\n    >\n      <div style=\"text-align: center; font-size: 16px;\">部分{{ bomName }}与上道工序加工班组不同，请手动分配</div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogTipsVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"dialogTipsVisible = false\">确 定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport getTbInfo from '@/mixins/PRO/get-table-info'\nimport { FIX_COLUMN } from '@/views/PRO/plan-production/schedule-production/constant'\nimport {\n  AdjustTeamProcessAllocation,\n  GetTeamProcessAllocation,\n  AdjustPartTeamProcessAllocation,\n  AdjustSubAssemblyTeamProcessAllocation,\n  GetStopList,\n  GetPreStepTaskAllocation\n} from '@/api/PRO/production-task'\nimport QrcodeVue from 'qrcode.vue'\nimport { v4 as uuidv4 } from 'uuid'\nimport numeral from 'numeral'\nimport { closeTagView, deepClone } from '@/utils'\nimport { GetCompTypeTree } from '@/api/PRO/professionalType'\nimport { GetBOMInfo, getBomCode, getBomName, checkIsUnitPart } from '@/views/PRO/bom-setting/utils'\n\nconst SPLIT_SYMBOL = '$_$'\nexport default {\n  components: {\n    QrcodeVue\n  },\n  filters: {\n    filterNum(value) {\n      return numeral(value).divide(1000).format('0.[00]')\n    }\n  },\n  mixins: [getTbInfo],\n  data() {\n    return {\n      treeSelectParams: {\n        placeholder: '请选择',\n        clearable: true\n      },\n      ObjectTypeList: {\n        // 构件类型\n        'check-strictly': true,\n        'default-expand-all': true,\n        clickParent: true,\n        data: [],\n        props: {\n          children: 'Children',\n          label: 'Label',\n          value: 'Data'\n        }\n      },\n      tbLoading: false,\n      loading: false,\n      activeName: 'first',\n      tipLabel: '',\n      tbData: [],\n      filterTbData: [],\n      multipleSelection: [],\n      columns: [],\n      workingTeam: [],\n      workingTeamColumn: [],\n      formInline: {},\n      pg_type: '',\n      searchType: '',\n      type: '',\n      queryForm: {\n        Comp_Codes: '',\n        Part_Code: '',\n        Spec: '',\n        Comp_Codes_Vague: '',\n        Part_Code_Vague: ''\n      },\n      dialogVisible: false,\n      dialogTipsVisible: false,\n      form: {\n        TeamGroup: '' // 班组\n      },\n\n      rules: {\n        TeamGroup: [\n          { required: true, message: '请输入班组名称', trigger: 'change' }\n        ]\n      },\n      Is_Workshop_Enabled: false,\n      Working_Process_Id: '',\n      bomList: [],\n      bomLevel: '',\n      bomName: ''\n    }\n  },\n  computed: {\n    isView() {\n      return this.type === 'view'\n    },\n    isCom() {\n      return this.bomLevel === getBomCode('-1')\n    },\n    isUnitPart() {\n      return checkIsUnitPart(this.bomLevel)\n    }\n  },\n\n  async mounted() {\n    try {\n      const { list } = await GetBOMInfo()\n      this.bomList = list || []\n      const rowInfo = JSON.parse(decodeURIComponent(this.$route.query.other))\n      console.log('rowInfo', rowInfo)\n      this.formInline = Object.assign({}, this.formInline, rowInfo)\n      this.pg_type = this.$route.query.pg_type\n      this.bomLevel = this.$route.query.bomLevel\n      this.bomName = await getBomName(this.bomLevel)\n      this.type = this.$route.query.type\n      this.Is_Workshop_Enabled = this.$route.query.Is_Workshop_Enabled\n      await this.getTableConfig(this.isUnitPart ? 'PROTaskUnitAllocationChange' : 'PROTaskAllocationChange')\n      this.columns = this.columns.filter(v => v.Is_Display).map(item => {\n        if (FIX_COLUMN.includes(item.Code)) {\n          item.fixed = 'left'\n        }\n        if (item.Code === 'Schduled_Count') {\n          item.visible = false\n        }\n        return item\n      })\n\n      if (this.isCom) {\n        const idx = this.columns.findIndex(item => item.Code === 'Part_Code')\n        idx !== -1 && this.columns.splice(idx, 1)\n        const idx2 = this.columns.findIndex(item => item.Code === 'Component_Code')\n        idx2 !== -1 && this.columns.splice(idx2, 1)\n      } else {\n        const idx = this.columns.findIndex(item => item.Code === 'Comp_Code')\n        idx !== -1 && this.columns.splice(idx, 1)\n        const idx2 = this.columns.findIndex(item => item.Code === 'Type')\n        idx2 !== -1 && this.columns.splice(idx2, 1)\n      }\n      if (!this.Is_Workshop_Enabled) {\n        const idx3 = this.columns.findIndex(item => item.Code === 'Workshop_Name')\n        idx3 !== -1 && this.columns.splice(idx3, 1)\n      }\n      this.getObjectTypeList()\n      this.fetchData()\n    } catch (e) {\n      this.$message({\n        message: '参数错误,请重新操作',\n        type: 'error'\n      })\n    }\n  },\n  methods: {\n    getRowCCode(row, prefix = '', suffix = '') {\n      if (this.activeName === 'first') {\n        return 'allocatedTask' + suffix\n      } else {\n        const arr = this.activeName.split(SPLIT_SYMBOL)\n        const team = this.workingTeam.find(v => v.Working_Team_Name === arr[0])\n        const u = this.getRowUnique(row.uuid, row.Process_Code, team.Working_Team_Id)\n        if (suffix === 'Max') {\n          return u\n        } else {\n          return prefix + u + suffix\n        }\n      }\n    },\n    handleClick(val) {\n      console.log('handleClick', val)\n      if (val.name === 'first') {\n        const c1 = this.$refs.xTable.getColumnByField('Schduled_Count')\n        const c2 = this.$refs.xTable.getColumnByField('Can_Allocation_Count')\n        c1.visible = false\n        c2.visible = true\n      } else {\n        const c2 = this.$refs.xTable.getColumnByField('Schduled_Count')\n        const c3 = this.$refs.xTable.getColumnByField('Can_Allocation_Count')\n        c2.visible = true\n        c3.visible = false\n      }\n\n      this.workingTeam.forEach((element, idx) => {\n        const codes = `${element.Working_Team_Name}$_$${element.Process_Code}`\n\n        const c = this.$refs.xTable.getColumnByField(codes)\n\n        c.visible = codes === val.name\n      })\n      this.$refs.xTable.refreshColumn()\n\n      this.filterTbData = this.tbData.filter(row => {\n        return this.filterZero(row)\n      })\n\n      this.multipleSelection = []\n      this.$refs.xTable.clearCheckboxRow()\n    },\n    filterZero(row) {\n      if (this.activeName === 'first') {\n        return row.allocatedTask > 0\n      } else {\n        const arr = this.activeName.split(SPLIT_SYMBOL)\n        const team = this.workingTeam.find(v => v.Working_Team_Name === arr[0])\n        const code = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\n        return row[code] > 0\n      }\n    },\n    fetchData() {\n      const Comp_Codes = !this.queryForm.Comp_Codes ? [] : this.queryForm.Comp_Codes.trim().split(' ')\n      const Part_Code = !this.queryForm.Part_Code ? [] : this.queryForm.Part_Code.trim().split(' ')\n      let Process_Type = 2\n      Process_Type = this.isCom ? 2 : this.isUnitPart ? 3 : 1\n      this.tbLoading = true\n      GetTeamProcessAllocation({\n        Page: 1,\n        PageSize: -1,\n        Step: this.formInline.Step,\n        Process_Type,\n        Bom_Level: this.bomLevel,\n        Schduling_Code: this.formInline.Schduling_Code,\n        Process_Code: this.formInline.Process_Code,\n        Workshop_Name: this.formInline.Workshop_Name,\n        Area_Id: this.formInline.Area_Id,\n        InstallUnit_Id: this.formInline.InstallUnit_Id,\n        Comp_Codes,\n        Part_Code\n      }).then(async(res) => {\n        if (res.IsSucceed) {\n          const { Schduling_Plan, Schduling_Comps } = res.Data\n          this.planInfoTemp = Schduling_Plan\n          await this.getStopList(Schduling_Comps)\n          this.initTbData(Schduling_Comps)\n          this.Working_Process_Id = res.Data.Working_Process_Id\n\n          if (Schduling_Comps.length) {\n            this.workingTeam = Schduling_Comps[0].Allocation_Teams\n            const _kk = this.workingTeam.map(v => {\n              v.visible = false\n              return v\n            })\n            this.workingTeamColumn = deepClone(_kk)\n          }\n          console.log(' this.tbData', this.tbData)\n          this.filterData()\n          console.log('filterTbData', this.filterTbData)\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      }).finally(_ => {\n        this.tbLoading = false\n      })\n    },\n    checkMethod({ row }) {\n      return !row.stopFlag\n    },\n    async getStopList(list) {\n      const key = 'Id'\n      const submitObj = list.map(item => {\n        return {\n          Id: item[key],\n          Bom_Level: this.bomLevel,\n          Type: this.isCom ? 2 : this.isUnitPart ? 3 : 1 // 1：零件，3：部件，2：构件\n        }\n      })\n      await GetStopList(submitObj).then(res => {\n        if (res.IsSucceed) {\n          const stopMap = {}\n          res.Data.forEach(item => {\n            stopMap[item.Id] = !!item.Is_Stop\n          })\n          list.forEach(row => {\n            if (stopMap[row[key]]) {\n              this.$set(row, 'stopFlag', stopMap[row[key]])\n            }\n          })\n        }\n      })\n    },\n    filterData() {\n      console.log('searchType', this.searchType)\n      this.multipleSelection = []\n      this.$refs.xTable.clearCheckboxRow()\n      const code = this.isCom ? 'Comp_Code' : 'Part_Code'\n      const queryCode = this.isCom ? 'Comp_Codes' : 'Part_Code'\n      const codeList = this.queryForm[queryCode].split(' ').filter(v => !!v)\n\n      const queryCodeVague = this.isCom ? 'Comp_Codes_Vague' : 'Part_Code_Vague'\n      const codeListVague = this.queryForm[queryCodeVague]\n\n      const searchTbData = this.tbData.filter(v => {\n        if (!codeList.length && codeListVague === '') {\n          return true\n        } else {\n          return codeList.includes(v[code])\n        }\n      })\n\n      const vagueData = searchTbData.length > 0 && codeListVague === '' ? searchTbData : searchTbData.length === 0 && codeListVague === '' ? searchTbData : this.tbData\n      const searchVagueTbData = vagueData.filter(v => {\n        if (codeListVague === '' && !codeList.length) {\n          return true\n        } else {\n          return v[code].includes(codeListVague)\n        }\n      })\n\n      // 合并两个数组\n      const mergedArray = searchTbData.concat(searchVagueTbData)\n      // 根据 Schduling_Detail_Id 进行去重\n      const uniqueArray = mergedArray.reduce((acc, current) => {\n        const existingObject = acc.find(item => item.Schduling_Detail_Id === current.Schduling_Detail_Id)\n        if (!existingObject) {\n          acc.push(current)\n        }\n        return acc\n      }, [])\n\n      this.filterTbData = uniqueArray.filter(v => {\n        if (!this.searchType) return true\n        return this.searchType === v.Type\n      }).filter(v => {\n        if (!this.queryForm.Spec) return true\n        return (v.Spec || '').includes(this.queryForm.Spec)\n      }).filter(row => {\n        return this.filterZero(row)\n      })\n    },\n    initTbData(list, teamKey = 'Allocation_Teams') {\n      this.tbData = list.map(row => {\n        const processList = row.Technology_Path?.split('/') || []\n        // 已uuid作为row唯一值；\n        // uuid+工序+班组为输入框值\n        row.uuid = uuidv4()\n        const newData = row[teamKey].filter((r) => processList.findIndex((p) => r.Process_Code === p) !== -1)\n        row.defaultCan_Allocation_Count = row.Can_Allocation_Count\n        let _inputNum = 0\n        newData.forEach((ele, index) => {\n          const code = this.getRowUnique(row.uuid, ele.Process_Code, ele.Working_Team_Id)\n          const max = this.getRowUniqueMax(row.uuid, ele.Process_Code, ele.Working_Team_Id)\n          row[code] = ele.Count\n          this.$set(row, 'alCount' + code, ele.Count)\n          row[max] = 0\n          _inputNum += ele.Count\n          this.$set(row, 'totalTask' + ele.Working_Team_Name, ele.Total_Receive_Count + ele.Count)\n        })\n\n        this.$set(row, 'allocatedTask', row.defaultCan_Allocation_Count - _inputNum)\n        row.Can_Allocation_Count = row.allocatedTask\n        this.$set(row, 'allocatedTaskMax', row.Can_Allocation_Count)\n\n        this.setInputMax(row)\n\n        row.checked = false\n        return row\n      })\n    },\n    inputChange(row) {\n      this.setInputMax(row)\n    },\n    setInputMax(row) {\n      let _inputNum = 0\n      const inputValuesKeys = Object.keys(row)\n        .filter(v => !v.endsWith('max') && v.startsWith(row.uuid) && v.length > row.uuid.length)\n      inputValuesKeys.forEach((val) => {\n        const curCode = val.split(SPLIT_SYMBOL)[1]\n        const otherTotal = inputValuesKeys.filter(x => {\n          const code = x.split(SPLIT_SYMBOL)[1]\n          return x !== val && code === curCode\n        }).reduce((acc, item) => {\n          return acc + numeral(row[item]).value()\n        }, 0)\n        row[val + SPLIT_SYMBOL + 'max'] = row.Schduled_Count - otherTotal\n        _inputNum += +row[val]\n      })\n      // row.allocatedCount = row.defaultCan_Allocation_Count - _inputNum\n      // row.Can_Allocation_Count = row.allocatedCount\n    },\n    checkPermissionTeam(processStr, processCode) {\n      if (!processStr) return false\n      const list = processStr?.split('/') || []\n      return !!list.some(v => v === processCode)\n    },\n\n    getSubmitTbInfo(tbData = this.tbData) {\n      // 处理上传的数据\n      const tableData = JSON.parse(JSON.stringify(tbData))\n      for (let i = 0; i < tableData.length; i++) {\n        const element = tableData[i]\n        const list = []\n        if (!element.Technology_Path) {\n          this.$message({\n            message: '工序不能为空',\n            type: 'warning'\n          })\n          return { status: false }\n        }\n        const processList = Array.from(new Set(element.Technology_Path.split('/')))\n        processList.forEach(code => {\n          const groups = this.workingTeam.filter(v => v.Process_Code === code)\n\n          const groupsList = groups.map((group, index) => {\n            const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)\n            const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)\n            const obj = {\n              Comp_Code: element.Comp_Code,\n              Again_Count: +element[uCode],\n              Part_Code: this.isCom ? null : element.Part_Code,\n              Process_Code: code,\n              Technology_Path: element.Technology_Path,\n              Working_Team_Id: group.Working_Team_Id,\n              Working_Team_Name: group.Working_Team_Name,\n              Team_Task_Id: element.Allocation_Teams.find((item) => item.Working_Team_Id === group.Working_Team_Id).Team_Task_Id\n            }\n            delete element['alCount' + uCode]\n            delete element['allocatedTask']\n            delete element['allocatedTaskMax']\n            delete element[uCode]\n            delete element[uMax]\n            return obj\n          })\n          list.push(...groupsList)\n        })\n        console.log(list)\n        delete element['uuid']\n        delete element['puuid']\n        element.Allocation_Teams = list\n      }\n      return { tableData, status: true }\n    },\n    handleSubmit() {\n      const { tableData, status } = this.getSubmitTbInfo()\n      if (!status) return\n      this.$confirm('是否提交当前数据?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.loading = true\n        const obj = {\n          Schduling_Plan: this.planInfoTemp,\n          Schduling_Comps: tableData\n        }\n        const Partobj = {\n          Schduling_Plan: this.planInfoTemp,\n          SarePartsModel: tableData\n        }\n        const requestFn = this.isCom ? AdjustTeamProcessAllocation : this.isUnitPart ? AdjustSubAssemblyTeamProcessAllocation : AdjustPartTeamProcessAllocation\n        console.log('obj', obj)\n        requestFn(this.isCom ? obj : Partobj).then(res => {\n          if (res.IsSucceed) {\n            this.$message({\n              message: '操作成功',\n              type: 'success'\n            })\n            this.handleClose()\n          } else {\n            this.$message({\n              message: res.Message,\n              type: 'error'\n            })\n          }\n          this.loading = false\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消'\n        })\n      })\n    },\n    handleClose() {\n      this.closeView()\n    },\n    closeView() {\n      closeTagView(this.$store, this.$route)\n    },\n    tbSelectChange(array) {\n      this.multipleSelection = array.records\n      console.log(this.tbData)\n    },\n    getTaskCode(name = '') {\n      if (name) return 'totalTask' + name\n      return 'totalTask' + this.activeName.split(SPLIT_SYMBOL)[0]\n    },\n    // 反选\n    reverseSelection() {\n      const list = this.$refs.xTable.getCheckboxRecords().filter(item => !item.stopFlag)\n      const unSelectList = this.filterTbData.filter(item => !list.includes(item) && !item.stopFlag)\n      this.$refs.xTable.setCheckboxRow(list, false)\n      this.$refs.xTable.setCheckboxRow(unSelectList, true)\n      this.multipleSelection = this.$refs.xTable.getCheckboxRecords()\n    },\n    // async getTableConfig(code) {\n    //   await GetGridByCode({\n    //     code\n    //   }).then((res) => {\n    //     const { IsSucceed, Data, Message } = res\n    //     if (IsSucceed) {\n    //       this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\n    //       const list = Data.ColumnList || []\n    //       this.columns = list.filter(v => v.Is_Display).map(item => {\n    //         if (FIX_COLUMN.includes(item.Code)) {\n    //           item.fixed = 'left'\n    //         }\n    //         if (item.Code === 'Schduled_Count') {\n    //           item.visible = false\n    //         }\n    //         return item\n    //       })\n    //     } else {\n    //       this.$message({\n    //         message: Message,\n    //         type: 'error'\n    //       })\n    //     }\n    //   })\n    // },\n    activeCellMethod({ row, column, columnIndex }) {\n      if (this.isView) return false\n      if (column.field === 'AllocatedCount') return true\n      const processCode = column.field?.split('$_$')[1]\n      return this.checkPermissionTeam(row.Technology_Path, processCode)\n    },\n    getRowUnique(uuid, processCode, workingId) {\n      return `${uuid}${SPLIT_SYMBOL}${processCode}${SPLIT_SYMBOL}${workingId}`\n    },\n    getRowUniqueMax(uuid, processCode, workingId) {\n      return this.getRowUnique(uuid, processCode, workingId) + `${SPLIT_SYMBOL}max`\n    },\n    // 批量分配\n    Batchallocation() {\n      this.dialogVisible = true\n      console.log(this.workingTeam)\n    },\n    // 上道工序分配\n    preStepTaskAllocation() {\n      const Schduling_Detail_Ids = this.multipleSelection.map(item => item.Schduling_Detail_Id)\n      const Working_Process_Code = this.multipleSelection[0].Process_Code\n      GetPreStepTaskAllocation({\n        Schduling_Detail_Ids,\n        Working_Process_Code,\n        Working_Process_Id: this.Working_Process_Id,\n        Process_Type: this.isCom ? 2 : this.isUnitPart ? 3 : 1,\n        Bom_Level: this.bomLevel\n      }).then(res => {\n        if (res.IsSucceed) {\n          if (res.Data.length === 0) {\n            this.dialogTipsVisible = true\n            return\n          }\n          this.preDoAllocation(res.Data)\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    handleDialog() {\n      this.dialogVisible = false\n      // this.multipleSelection = []\n    },\n    handelData() {\n      this.multipleSelection.forEach((item) =>\n        item.Allocation_Teams.forEach((v) => {\n          if (v.Working_Team_Id === this.form.TeamGroup) {\n            v.Count = item.Can_Allocation_Count\n          } else {\n            v.Count = 0\n          }\n        })\n      )\n      // const tableData = JSON.parse(JSON.stringify(this.multipleSelection))\n      for (let i = 0; i < this.multipleSelection.length; i++) {\n        const element = this.multipleSelection[i]\n        const processList = Array.from(new Set(element.Technology_Path.split('/')))\n        processList.forEach(code => {\n          const groups = this.workingTeam.filter(v => v.Process_Code === code)\n          groups.forEach(group => {\n            const uniCode = this.getRowUnique(element.uuid, code, this.form.TeamGroup)\n            const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)\n            const uniMax = this.getRowUniqueMax(element.uuid, code, this.form.TeamGroup)\n            const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)\n\n            if (uniCode === uCode && uniMax === uMax) {\n              element[uCode] = element['Can_Allocation_Count']\n              element[uMax] = element['Can_Allocation_Count']\n            } else {\n              element[uCode] = 0\n              element[uMax] = 0\n            }\n          })\n        })\n      }\n      console.log(this.multipleSelection)\n      for (let i = 0; i < this.tbData.length; i++) {\n        for (let k = 0; k < this.multipleSelection.length; k++) {\n          if (this.tbData[i].uuid === this.multipleSelection[k].uuid) {\n            this.$nextTick((_) => {\n              this.tbData[i] = this.multipleSelection[k]\n            })\n          }\n        }\n      }\n    },\n    submitForm(formName) {\n      this.$refs[formName].validate((valid) => {\n        if (valid) {\n          // this.handelData()\n          this.doAllocation()\n          // this.workingTeam.find(v=> v.Working_Team_Id === this.form.TeamGroup).count\n          this.handleDialog()\n          console.log(this.tbData)\n          this.multipleSelection = []\n          this.$refs.xTable.clearCheckboxRow()\n        } else {\n          return false\n        }\n      })\n    },\n    // 上道工序分配\n    preDoAllocation(preProcessData) {\n      const preProcessDataMap = new Map()\n      preProcessData.forEach(item => {\n        const key = `${item.Schduling_Detail_Id}_${item.Working_Team_Id}`\n        preProcessDataMap.set(key, item.Current_Task_Count)\n      })\n\n      const allocateForTeam = (row, team, amount) => {\n        const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\n        const bKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\n        const currentTaskCount = preProcessDataMap.get(bKey) || 0\n        const allocated = Math.min(amount, currentTaskCount, row.Can_Allocation_Count)\n\n        row[tarCode] = (Number(row[tarCode]) || 0) + allocated\n        row.Can_Allocation_Count -= allocated\n        row[this.getTaskCode(team.Working_Team_Name)] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + allocated\n        row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - allocated\n        row['alCount' + tarCode] = row[tarCode]\n\n        return allocated\n      }\n      let isMessage = true\n      this.multipleSelection.forEach(row => {\n        if (!row.Can_Allocation_Count && this.activeName === 'first') return\n\n        const eligibleTeams = row.Allocation_Teams.filter(team =>\n          preProcessDataMap.has(`${row.Schduling_Detail_Id}_${team.Working_Team_Id}`)\n        )\n\n        if (eligibleTeams.length === 0) return\n        if (this.activeName === 'first') {\n          let IsAllNo = 0\n          const totalAvailable = eligibleTeams.reduce((sum, team) => {\n            const bKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\n            return sum + (preProcessDataMap.get(bKey) || 0)\n          }, 0)\n\n          if (row.allocatedTaskMax < totalAvailable) {\n            IsAllNo++\n            if (IsAllNo === this.multipleSelection.length) {\n              isMessage = false\n              this.dialogTipsVisible = true\n            }\n            return\n          }\n\n          let remaining = Math.min(row.allocatedTask, row.Can_Allocation_Count)\n          const perTeamAllocation = Math.floor(remaining / eligibleTeams.length)\n\n          eligibleTeams.forEach((team, index) => {\n            if (remaining <= 0) return\n\n            const allocateAmount = index === eligibleTeams.length - 1\n              ? remaining\n              : Math.min(perTeamAllocation, remaining)\n\n            remaining -= allocateForTeam(row, team, allocateAmount)\n          })\n\n          row.allocatedTaskMax = row.Can_Allocation_Count\n          row.allocatedTask = row.Can_Allocation_Count\n          if (IsAllNo === this.multipleSelection.length) {\n            isMessage = false\n            this.dialogTipsVisible = true\n            return\n          }\n        } else {\n          eligibleTeams.forEach(team => {\n            const bKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\n            const currentTaskCount = preProcessDataMap.get(bKey) || 0\n\n            if (row[this.getRowCCode(row)] < currentTaskCount) {\n              return\n            }\n\n            const selectNum = Math.min(\n              row[this.getRowCCode(row, 'alCount')] || 0,\n              row[this.getRowCCode(row)] || 0,\n              currentTaskCount\n            )\n\n            if (selectNum > 0) {\n              const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\n              row[this.getTaskCode(team.Working_Team_Name)] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + selectNum\n              row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - selectNum\n              row[tarCode] = (Number(row[tarCode]) || 0) + selectNum\n              row[this.getRowCCode(row)] = (row[this.getRowCCode(row)] || 0) - selectNum\n              row[this.getRowCCode(row, 'alCount')] = (row[this.getRowCCode(row, 'alCount')] || 0) - selectNum\n              row['alCount' + tarCode] = row[tarCode]\n            }\n          })\n        }\n      })\n      // if (this.activeName === 'first') {\n      //   let IsAllNo = 0\n      //   this.multipleSelection.forEach((row, idx) => {\n      //     if (row.Can_Allocation_Count) {\n      //       const validTeams = row.Allocation_Teams.filter(team => {\n      //         const key = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\n      //         return preProcessDataMap.has(key)\n      //       })\n      //       if (validTeams.length > 0) {\n      //         const team = validTeams[0]\n      //         const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\n\n      //         const preProcessKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\n      //         const currentTaskCount = preProcessDataMap.get(preProcessKey) || 0\n\n      //         if (currentTaskCount > row.allocatedTaskMax) {\n      //           IsAllNo++\n      //           return\n      //         }\n      //         const allocated = Math.min(row.allocatedTask, currentTaskCount)\n\n      //         row[tarCode] = Number(row[tarCode] || 0) + allocated\n      //         row.Can_Allocation_Count = row.Can_Allocation_Count - allocated\n      //         row[this.getTaskCode(team.Working_Team_Name) || 0] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + allocated\n      //         row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - allocated\n      //         row.allocatedTaskMax = row.Can_Allocation_Count\n      //         row.allocatedTask = row.Can_Allocation_Count\n      //         row['alCount' + tarCode] = row[tarCode]\n      //       }\n      //     }\n      //   })\n      //   if (IsAllNo === this.multipleSelection.length) {\n      //     this.dialogTipsVisible = true\n      //     return\n      //   }\n      // } else {\n      //   this.multipleSelection.forEach((row, idx) => {\n      //     const validTeams = row.Allocation_Teams.filter(team => {\n      //       const key = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\n      //       return preProcessDataMap.has(key)\n      //     })\n      //     if (validTeams.length > 0) {\n      //       const team = validTeams[0]\n      //       const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\n\n      //       const preProcessKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\n      //       const currentTaskCount = preProcessDataMap.get(preProcessKey) || 0\n\n      //       const selectNum = Math.min(\n      //         row[this.getRowCCode(row, 'alCount')] || 0,\n      //         row[this.getRowCCode(row)] || 0,\n      //         currentTaskCount\n      //       )\n\n      //       row[this.getTaskCode(team.Working_Team_Name)] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + selectNum\n      //       row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - selectNum\n      //       row[tarCode] = (Number(row[tarCode]) || 0) + selectNum\n      //       row[this.getRowCCode(row)] = (row[this.getRowCCode(row)] || 0) - selectNum\n      //       row[this.getRowCCode(row, 'alCount')] = (row[this.getRowCCode(row, 'alCount')] || 0) - selectNum\n      //       row['alCount' + tarCode] = row[tarCode]\n      //     }\n      //   })\n      // }\n      this.filterTbData = this.tbData.filter(row => {\n        return this.filterZero(row)\n      })\n      this.multipleSelection = []\n      this.$refs.xTable.clearCheckboxRow()\n      if (isMessage) {\n        this.$message({\n          message: '同步成功',\n          type: 'success'\n        })\n      }\n    },\n    // 批量分配提交\n    doAllocation() {\n      if (this.activeName === 'first') {\n        this.multipleSelection\n          .forEach((row, idx) => {\n            if (row.Can_Allocation_Count) {\n              const team = row.Allocation_Teams.find(v => v.Working_Team_Id === this.form.TeamGroup)\n              const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\n              row[tarCode] = Number(row[tarCode]) + row.allocatedTask\n              row.Can_Allocation_Count = row.Can_Allocation_Count - row.allocatedTask\n              row[this.getTaskCode(team.Working_Team_Name)] += row.allocatedTask\n              row[this.getTaskCode()] -= row.allocatedTask\n              row.allocatedTaskMax = row.Can_Allocation_Count\n              row.allocatedTask = row.Can_Allocation_Count\n              row['alCount' + tarCode] = row[tarCode]\n            }\n          })\n      } else {\n        this.multipleSelection\n          .forEach((row, idx) => {\n            const team = row.Allocation_Teams.find(v => v.Working_Team_Id === this.form.TeamGroup)\n            const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\n\n            const selectNum = Math.min(row[this.getRowCCode(row, 'alCount')], row[this.getRowCCode(row)])\n\n            row[this.getTaskCode(team.Working_Team_Name)] += selectNum\n            row[this.getTaskCode()] -= selectNum\n\n            row[tarCode] = Number(row[tarCode]) + selectNum\n            row[this.getRowCCode(row)] -= selectNum\n            row[this.getRowCCode(row, 'alCount')] -= selectNum\n\n            row['alCount' + tarCode] = row[tarCode]\n          })\n      }\n      this.filterTbData = this.tbData.filter(row => {\n        return this.filterZero(row)\n      })\n    },\n    resetForm(formName) {\n      this.$refs[formName].resetFields()\n      this.dialogVisible = false\n    },\n    filterTypeMethod({ option, row }) {\n      return row.Type.includes(option.data)\n    },\n    filterTypeRecoverMethod({ option }) {\n      option.data = ''\n    },\n    filterCodeMethod({ option, row }) {\n      console.log('option, row', option, row)\n      return row[this.isCom ? 'Comp_Code' : 'Part_Code'].includes(option.data)\n    },\n    filterCodeRecoverMethod({ option }) {\n      option.data = ''\n    },\n    getObjectTypeList() {\n      GetCompTypeTree({ professional: 'Steel' }).then((res) => {\n        if (res.IsSucceed) {\n          this.ObjectTypeList.data = res.Data\n          this.$nextTick((_) => {\n            this.$refs?.treeSelectObjectType?.treeDataUpdateFun(res.Data)\n          })\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.pagination-container {\n  padding: 0;\n  text-align: right;\n}\n\n::v-deep .el-card__body {\n  display: flex;\n  flex-direction: column;\n\n  .el-divider {\n    margin-top: 0;\n    margin-bottom: 16px;\n    background-color: #EEEEEE;\n  }\n\n  .tb-options {\n    margin-bottom: 16px;\n    display: flex;\n\n    .el-form-item--small.el-form-item {\n      margin-bottom: 0;\n    }\n    .el-input {\n      width: 250px;\n    }\n  }\n\n  footer {\n    text-align: inherit;\n    display: flex;\n    justify-content: space-between;\n  }\n}\n\n.tb-x {\n  flex: 1;\n  height: 0;\n  margin-bottom: 10px;\n  overflow: auto;\n}\n\n.topTitle {\n  height: 14px;\n  line-height: 14px;\n  font-size: 14px;\n  margin: 0 0 16px;\n\n  span {\n    display: inline-block;\n    width: 2px;\n    height: 14px;\n    background: #009dff;\n    vertical-align: middle;\n    margin-right: 6px;\n  }\n}\n\n.el-icon-edit {\n  cursor: pointer;\n}\n\n.cs-bottom {\n  position: relative;\n  height: 40px;\n  line-height: 40px;\n\n  .data-info {\n    position: absolute;\n    bottom: 0;\n\n    .info-x {\n      margin-right: 20px;\n    }\n  }\n}\n.my-input {\n  margin: 10px;\n  width: 140px;\n  height: 32px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgYA,OAAAA,SAAA;AACA,SAAAC,UAAA;AACA,SACAC,2BAAA,EACAC,wBAAA,EACAC,+BAAA,EACAC,sCAAA,EACAC,WAAA,EACAC,wBAAA,QACA;AACA,OAAAC,SAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AACA,OAAAC,OAAA;AACA,SAAAC,YAAA,EAAAC,SAAA;AACA,SAAAC,eAAA;AACA,SAAAC,UAAA,EAAAC,UAAA,EAAAC,UAAA,EAAAC,eAAA;AAEA,IAAAC,YAAA;AACA;EACAC,UAAA;IACAZ,SAAA,EAAAA;EACA;EACAa,OAAA;IACAC,SAAA,WAAAA,UAAAC,KAAA;MACA,OAAAZ,OAAA,CAAAY,KAAA,EAAAC,MAAA,OAAAC,MAAA;IACA;EACA;EACAC,MAAA,GAAA1B,SAAA;EACA2B,IAAA,WAAAA,KAAA;IACA;MACAC,gBAAA;QACAC,WAAA;QACAC,SAAA;MACA;MACAC,cAAA;QACA;QACA;QACA;QACAC,WAAA;QACAL,IAAA;QACAM,KAAA;UACAC,QAAA;UACAC,KAAA;UACAZ,KAAA;QACA;MACA;MACAa,SAAA;MACAC,OAAA;MACAC,UAAA;MACAC,QAAA;MACAC,MAAA;MACAC,YAAA;MACAC,iBAAA;MACAC,OAAA;MACAC,WAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,OAAA;MACAC,UAAA;MACAC,IAAA;MACAC,SAAA;QACAC,UAAA;QACAC,SAAA;QACAC,IAAA;QACAC,gBAAA;QACAC,eAAA;MACA;MACAC,aAAA;MACAC,iBAAA;MACAC,IAAA;QACAC,SAAA;MACA;MAEAC,KAAA;QACAD,SAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,mBAAA;MACAC,kBAAA;MACAC,OAAA;MACAC,QAAA;MACAC,OAAA;IACA;EACA;EACAC,QAAA;IACAC,MAAA,WAAAA,OAAA;MACA,YAAArB,IAAA;IACA;IACAsB,KAAA,WAAAA,MAAA;MACA,YAAAJ,QAAA,KAAAnD,UAAA;IACA;IACAwD,UAAA,WAAAA,WAAA;MACA,OAAAtD,eAAA,MAAAiD,QAAA;IACA;EACA;EAEAM,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAAC,iBAAA,EAAAC,IAAA,EAAAC,OAAA,EAAAC,GAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,KAAA,EAAAC,IAAA;MAAA,OAAAV,mBAAA,GAAAW,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAC,IAAA;YAAAD,QAAA,CAAAE,IAAA;YAAA,OAEA5E,UAAA;UAAA;YAAAgE,iBAAA,GAAAU,QAAA,CAAAG,IAAA;YAAAZ,IAAA,GAAAD,iBAAA,CAAAC,IAAA;YACAN,KAAA,CAAAR,OAAA,GAAAc,IAAA;YACAC,OAAA,GAAAY,IAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAArB,KAAA,CAAAsB,MAAA,CAAAC,KAAA,CAAAC,KAAA;YACAC,OAAA,CAAAC,GAAA,YAAAnB,OAAA;YACAP,KAAA,CAAA5B,UAAA,GAAAuD,MAAA,CAAAC,MAAA,KAAA5B,KAAA,CAAA5B,UAAA,EAAAmC,OAAA;YACAP,KAAA,CAAA3B,OAAA,GAAA2B,KAAA,CAAAsB,MAAA,CAAAC,KAAA,CAAAlD,OAAA;YACA2B,KAAA,CAAAP,QAAA,GAAAO,KAAA,CAAAsB,MAAA,CAAAC,KAAA,CAAA9B,QAAA;YAAAsB,QAAA,CAAAE,IAAA;YAAA,OACA1E,UAAA,CAAAyD,KAAA,CAAAP,QAAA;UAAA;YAAAO,KAAA,CAAAN,OAAA,GAAAqB,QAAA,CAAAG,IAAA;YACAlB,KAAA,CAAAzB,IAAA,GAAAyB,KAAA,CAAAsB,MAAA,CAAAC,KAAA,CAAAhD,IAAA;YACAyB,KAAA,CAAAV,mBAAA,GAAAU,KAAA,CAAAsB,MAAA,CAAAC,KAAA,CAAAjC,mBAAA;YAAAyB,QAAA,CAAAE,IAAA;YAAA,OACAjB,KAAA,CAAA6B,cAAA,CAAA7B,KAAA,CAAAF,UAAA;UAAA;YACAE,KAAA,CAAA/B,OAAA,GAAA+B,KAAA,CAAA/B,OAAA,CAAA6D,MAAA,WAAAC,CAAA;cAAA,OAAAA,CAAA,CAAAC,UAAA;YAAA,GAAAC,GAAA,WAAAC,IAAA;cACA,IAAA3G,UAAA,CAAA4G,QAAA,CAAAD,IAAA,CAAAE,IAAA;gBACAF,IAAA,CAAAG,KAAA;cACA;cACA,IAAAH,IAAA,CAAAE,IAAA;gBACAF,IAAA,CAAAI,OAAA;cACA;cACA,OAAAJ,IAAA;YACA;YAEA,IAAAlC,KAAA,CAAAH,KAAA;cACAW,GAAA,GAAAR,KAAA,CAAA/B,OAAA,CAAAsE,SAAA,WAAAL,IAAA;gBAAA,OAAAA,IAAA,CAAAE,IAAA;cAAA;cACA5B,GAAA,WAAAR,KAAA,CAAA/B,OAAA,CAAAuE,MAAA,CAAAhC,GAAA;cACAC,IAAA,GAAAT,KAAA,CAAA/B,OAAA,CAAAsE,SAAA,WAAAL,IAAA;gBAAA,OAAAA,IAAA,CAAAE,IAAA;cAAA;cACA3B,IAAA,WAAAT,KAAA,CAAA/B,OAAA,CAAAuE,MAAA,CAAA/B,IAAA;YACA;cACAD,IAAA,GAAAR,KAAA,CAAA/B,OAAA,CAAAsE,SAAA,WAAAL,IAAA;gBAAA,OAAAA,IAAA,CAAAE,IAAA;cAAA;cACA5B,IAAA,WAAAR,KAAA,CAAA/B,OAAA,CAAAuE,MAAA,CAAAhC,IAAA;cACAC,KAAA,GAAAT,KAAA,CAAA/B,OAAA,CAAAsE,SAAA,WAAAL,IAAA;gBAAA,OAAAA,IAAA,CAAAE,IAAA;cAAA;cACA3B,KAAA,WAAAT,KAAA,CAAA/B,OAAA,CAAAuE,MAAA,CAAA/B,KAAA;YACA;YACA,KAAAT,KAAA,CAAAV,mBAAA;cACAsB,IAAA,GAAAZ,KAAA,CAAA/B,OAAA,CAAAsE,SAAA,WAAAL,IAAA;gBAAA,OAAAA,IAAA,CAAAE,IAAA;cAAA;cACAxB,IAAA,WAAAZ,KAAA,CAAA/B,OAAA,CAAAuE,MAAA,CAAA5B,IAAA;YACA;YACAZ,KAAA,CAAAyC,iBAAA;YACAzC,KAAA,CAAA0C,SAAA;YAAA3B,QAAA,CAAAE,IAAA;YAAA;UAAA;YAAAF,QAAA,CAAAC,IAAA;YAAAD,QAAA,CAAA4B,EAAA,GAAA5B,QAAA;YAEAf,KAAA,CAAA4C,QAAA;cACAxD,OAAA;cACAb,IAAA;YACA;UAAA;UAAA;YAAA,OAAAwC,QAAA,CAAA8B,IAAA;QAAA;MAAA,GAAAzC,OAAA;IAAA;EAEA;EACA0C,OAAA;IACAC,WAAA,WAAAA,YAAAC,GAAA;MAAA,IAAAC,MAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MAAA,IAAAG,MAAA,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MACA,SAAAtF,UAAA;QACA,yBAAAyF,MAAA;MACA;QACA,IAAAC,GAAA,QAAA1F,UAAA,CAAA2F,KAAA,CAAA9G,YAAA;QACA,IAAA+G,IAAA,QAAAtF,WAAA,CAAAuF,IAAA,WAAA1B,CAAA;UAAA,OAAAA,CAAA,CAAA2B,iBAAA,KAAAJ,GAAA;QAAA;QACA,IAAAK,CAAA,QAAAC,YAAA,CAAAZ,GAAA,CAAAa,IAAA,EAAAb,GAAA,CAAAc,YAAA,EAAAN,IAAA,CAAAO,eAAA;QACA,IAAAV,MAAA;UACA,OAAAM,CAAA;QACA;UACA,OAAAV,MAAA,GAAAU,CAAA,GAAAN,MAAA;QACA;MACA;IACA;IACAW,WAAA,WAAAA,YAAAC,GAAA;MAAA,IAAAC,MAAA;MACAzC,OAAA,CAAAC,GAAA,gBAAAuC,GAAA;MACA,IAAAA,GAAA,CAAAE,IAAA;QACA,IAAAC,EAAA,QAAAC,KAAA,CAAAC,MAAA,CAAAC,gBAAA;QACA,IAAAC,EAAA,QAAAH,KAAA,CAAAC,MAAA,CAAAC,gBAAA;QACAH,EAAA,CAAA9B,OAAA;QACAkC,EAAA,CAAAlC,OAAA;MACA;QACA,IAAAkC,EAAA,QAAAH,KAAA,CAAAC,MAAA,CAAAC,gBAAA;QACA,IAAAE,EAAA,QAAAJ,KAAA,CAAAC,MAAA,CAAAC,gBAAA;QACAC,EAAA,CAAAlC,OAAA;QACAmC,EAAA,CAAAnC,OAAA;MACA;MAEA,KAAApE,WAAA,CAAAwG,OAAA,WAAAC,OAAA,EAAAnE,GAAA;QACA,IAAAoE,KAAA,MAAAC,MAAA,CAAAF,OAAA,CAAAjB,iBAAA,SAAAmB,MAAA,CAAAF,OAAA,CAAAb,YAAA;QAEA,IAAAgB,CAAA,GAAAZ,MAAA,CAAAG,KAAA,CAAAC,MAAA,CAAAC,gBAAA,CAAAK,KAAA;QAEAE,CAAA,CAAAxC,OAAA,GAAAsC,KAAA,KAAAX,GAAA,CAAAE,IAAA;MACA;MACA,KAAAE,KAAA,CAAAC,MAAA,CAAAS,aAAA;MAEA,KAAAhH,YAAA,QAAAD,MAAA,CAAAgE,MAAA,WAAAkB,GAAA;QACA,OAAAkB,MAAA,CAAAc,UAAA,CAAAhC,GAAA;MACA;MAEA,KAAAhF,iBAAA;MACA,KAAAqG,KAAA,CAAAC,MAAA,CAAAW,gBAAA;IACA;IACAD,UAAA,WAAAA,WAAAhC,GAAA;MACA,SAAApF,UAAA;QACA,OAAAoF,GAAA,CAAAkC,aAAA;MACA;QACA,IAAA5B,GAAA,QAAA1F,UAAA,CAAA2F,KAAA,CAAA9G,YAAA;QACA,IAAA+G,IAAA,QAAAtF,WAAA,CAAAuF,IAAA,WAAA1B,CAAA;UAAA,OAAAA,CAAA,CAAA2B,iBAAA,KAAAJ,GAAA;QAAA;QACA,IAAA6B,IAAA,QAAAvB,YAAA,CAAAZ,GAAA,CAAAa,IAAA,EAAAL,IAAA,CAAAM,YAAA,EAAAN,IAAA,CAAAO,eAAA;QACA,OAAAf,GAAA,CAAAmC,IAAA;MACA;IACA;IACAzC,SAAA,WAAAA,UAAA;MAAA,IAAA0C,MAAA;MACA,IAAA3G,UAAA,SAAAD,SAAA,CAAAC,UAAA,aAAAD,SAAA,CAAAC,UAAA,CAAA4G,IAAA,GAAA9B,KAAA;MACA,IAAA7E,SAAA,SAAAF,SAAA,CAAAE,SAAA,aAAAF,SAAA,CAAAE,SAAA,CAAA2G,IAAA,GAAA9B,KAAA;MACA,IAAA+B,YAAA;MACAA,YAAA,QAAAzF,KAAA,YAAAC,UAAA;MACA,KAAApC,SAAA;MACAjC,wBAAA;QACA8J,IAAA;QACAC,QAAA;QACAC,IAAA,OAAArH,UAAA,CAAAqH,IAAA;QACAH,YAAA,EAAAA,YAAA;QACAI,SAAA,OAAAjG,QAAA;QACAkG,cAAA,OAAAvH,UAAA,CAAAuH,cAAA;QACA7B,YAAA,OAAA1F,UAAA,CAAA0F,YAAA;QACA8B,aAAA,OAAAxH,UAAA,CAAAwH,aAAA;QACAC,OAAA,OAAAzH,UAAA,CAAAyH,OAAA;QACAC,cAAA,OAAA1H,UAAA,CAAA0H,cAAA;QACArH,UAAA,EAAAA,UAAA;QACAC,SAAA,EAAAA;MACA,GAAAqH,IAAA;QAAA,IAAAC,IAAA,GAAA/F,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA8F,SAAAC,GAAA;UAAA,IAAAC,SAAA,EAAAC,cAAA,EAAAC,eAAA,EAAAC,GAAA;UAAA,OAAApG,mBAAA,GAAAW,IAAA,UAAA0F,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAxF,IAAA,GAAAwF,SAAA,CAAAvF,IAAA;cAAA;gBAAA,KACAiF,GAAA,CAAAO,SAAA;kBAAAD,SAAA,CAAAvF,IAAA;kBAAA;gBAAA;gBAAAkF,SAAA,GACAD,GAAA,CAAAQ,IAAA,EAAAN,cAAA,GAAAD,SAAA,CAAAC,cAAA,EAAAC,eAAA,GAAAF,SAAA,CAAAE,eAAA;gBACAjB,MAAA,CAAAuB,YAAA,GAAAP,cAAA;gBAAAI,SAAA,CAAAvF,IAAA;gBAAA,OACAmE,MAAA,CAAAwB,WAAA,CAAAP,eAAA;cAAA;gBACAjB,MAAA,CAAAyB,UAAA,CAAAR,eAAA;gBACAjB,MAAA,CAAA7F,kBAAA,GAAA2G,GAAA,CAAAQ,IAAA,CAAAnH,kBAAA;gBAEA,IAAA8G,eAAA,CAAAlD,MAAA;kBACAiC,MAAA,CAAAlH,WAAA,GAAAmI,eAAA,IAAAS,gBAAA;kBACAR,GAAA,GAAAlB,MAAA,CAAAlH,WAAA,CAAA+D,GAAA,WAAAF,CAAA;oBACAA,CAAA,CAAAO,OAAA;oBACA,OAAAP,CAAA;kBACA;kBACAqD,MAAA,CAAAjH,iBAAA,GAAAhC,SAAA,CAAAmK,GAAA;gBACA;gBACA7E,OAAA,CAAAC,GAAA,iBAAA0D,MAAA,CAAAtH,MAAA;gBACAsH,MAAA,CAAA2B,UAAA;gBACAtF,OAAA,CAAAC,GAAA,iBAAA0D,MAAA,CAAArH,YAAA;gBAAAyI,SAAA,CAAAvF,IAAA;gBAAA;cAAA;gBAEAmE,MAAA,CAAAxC,QAAA;kBACAxD,OAAA,EAAA8G,GAAA,CAAAc,OAAA;kBACAzI,IAAA;gBACA;cAAA;cAAA;gBAAA,OAAAiI,SAAA,CAAA3D,IAAA;YAAA;UAAA,GAAAoD,QAAA;QAAA,CAEA;QAAA,iBAAAgB,EAAA;UAAA,OAAAjB,IAAA,CAAAkB,KAAA,OAAAhE,SAAA;QAAA;MAAA,KAAAiE,OAAA,WAAAC,CAAA;QACAhC,MAAA,CAAA1H,SAAA;MACA;IACA;IACA2J,WAAA,WAAAA,YAAAC,KAAA;MAAA,IAAAtE,GAAA,GAAAsE,KAAA,CAAAtE,GAAA;MACA,QAAAA,GAAA,CAAAuE,QAAA;IACA;IACAX,WAAA,WAAAA,YAAAtG,IAAA;MAAA,IAAAkH,MAAA;MAAA,OAAAvH,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAsH,SAAA;QAAA,IAAAC,GAAA,EAAAC,SAAA;QAAA,OAAAzH,mBAAA,GAAAW,IAAA,UAAA+G,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7G,IAAA,GAAA6G,SAAA,CAAA5G,IAAA;YAAA;cACAyG,GAAA;cACAC,SAAA,GAAArH,IAAA,CAAA2B,GAAA,WAAAC,IAAA;gBACA;kBACA4F,EAAA,EAAA5F,IAAA,CAAAwF,GAAA;kBACAhC,SAAA,EAAA8B,MAAA,CAAA/H,QAAA;kBACAsI,IAAA,EAAAP,MAAA,CAAA3H,KAAA,OAAA2H,MAAA,CAAA1H,UAAA;gBACA;cACA;cAAA+H,SAAA,CAAA5G,IAAA;cAAA,OACArF,WAAA,CAAA+L,SAAA,EAAA5B,IAAA,WAAAG,GAAA;gBACA,IAAAA,GAAA,CAAAO,SAAA;kBACA,IAAAuB,OAAA;kBACA9B,GAAA,CAAAQ,IAAA,CAAAhC,OAAA,WAAAxC,IAAA;oBACA8F,OAAA,CAAA9F,IAAA,CAAA4F,EAAA,MAAA5F,IAAA,CAAA+F,OAAA;kBACA;kBACA3H,IAAA,CAAAoE,OAAA,WAAA1B,GAAA;oBACA,IAAAgF,OAAA,CAAAhF,GAAA,CAAA0E,GAAA;sBACAF,MAAA,CAAAU,IAAA,CAAAlF,GAAA,cAAAgF,OAAA,CAAAhF,GAAA,CAAA0E,GAAA;oBACA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAG,SAAA,CAAAhF,IAAA;UAAA;QAAA,GAAA4E,QAAA;MAAA;IACA;IACAV,UAAA,WAAAA,WAAA;MAAA,IAAAoB,MAAA;MACA1G,OAAA,CAAAC,GAAA,oBAAApD,UAAA;MACA,KAAAN,iBAAA;MACA,KAAAqG,KAAA,CAAAC,MAAA,CAAAW,gBAAA;MACA,IAAAE,IAAA,QAAAtF,KAAA;MACA,IAAAuI,SAAA,QAAAvI,KAAA;MACA,IAAAwI,QAAA,QAAA7J,SAAA,CAAA4J,SAAA,EAAA7E,KAAA,MAAAzB,MAAA,WAAAC,CAAA;QAAA,SAAAA,CAAA;MAAA;MAEA,IAAAuG,cAAA,QAAAzI,KAAA;MACA,IAAA0I,aAAA,QAAA/J,SAAA,CAAA8J,cAAA;MAEA,IAAAE,YAAA,QAAA1K,MAAA,CAAAgE,MAAA,WAAAC,CAAA;QACA,KAAAsG,QAAA,CAAAlF,MAAA,IAAAoF,aAAA;UACA;QACA;UACA,OAAAF,QAAA,CAAAlG,QAAA,CAAAJ,CAAA,CAAAoD,IAAA;QACA;MACA;MAEA,IAAAsD,SAAA,GAAAD,YAAA,CAAArF,MAAA,QAAAoF,aAAA,UAAAC,YAAA,GAAAA,YAAA,CAAArF,MAAA,UAAAoF,aAAA,UAAAC,YAAA,QAAA1K,MAAA;MACA,IAAA4K,iBAAA,GAAAD,SAAA,CAAA3G,MAAA,WAAAC,CAAA;QACA,IAAAwG,aAAA,YAAAF,QAAA,CAAAlF,MAAA;UACA;QACA;UACA,OAAApB,CAAA,CAAAoD,IAAA,EAAAhD,QAAA,CAAAoG,aAAA;QACA;MACA;;MAEA;MACA,IAAAI,WAAA,GAAAH,YAAA,CAAA3D,MAAA,CAAA6D,iBAAA;MACA;MACA,IAAAE,WAAA,GAAAD,WAAA,CAAAE,MAAA,WAAAC,GAAA,EAAAC,OAAA;QACA,IAAAC,cAAA,GAAAF,GAAA,CAAArF,IAAA,WAAAvB,IAAA;UAAA,OAAAA,IAAA,CAAA+G,mBAAA,KAAAF,OAAA,CAAAE,mBAAA;QAAA;QACA,KAAAD,cAAA;UACAF,GAAA,CAAAI,IAAA,CAAAH,OAAA;QACA;QACA,OAAAD,GAAA;MACA;MAEA,KAAA/K,YAAA,GAAA6K,WAAA,CAAA9G,MAAA,WAAAC,CAAA;QACA,KAAAoG,MAAA,CAAA7J,UAAA;QACA,OAAA6J,MAAA,CAAA7J,UAAA,KAAAyD,CAAA,CAAAgG,IAAA;MACA,GAAAjG,MAAA,WAAAC,CAAA;QACA,KAAAoG,MAAA,CAAA3J,SAAA,CAAAG,IAAA;QACA,QAAAoD,CAAA,CAAApD,IAAA,QAAAwD,QAAA,CAAAgG,MAAA,CAAA3J,SAAA,CAAAG,IAAA;MACA,GAAAmD,MAAA,WAAAkB,GAAA;QACA,OAAAmF,MAAA,CAAAnD,UAAA,CAAAhC,GAAA;MACA;IACA;IACA6D,UAAA,WAAAA,WAAAvG,IAAA;MAAA,IAAA6I,MAAA;MAAA,IAAAC,OAAA,GAAAlG,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MACA,KAAApF,MAAA,GAAAwC,IAAA,CAAA2B,GAAA,WAAAe,GAAA;QAAA,IAAAqG,oBAAA;QACA,IAAAC,WAAA,KAAAD,oBAAA,GAAArG,GAAA,CAAAuG,eAAA,cAAAF,oBAAA,uBAAAA,oBAAA,CAAA9F,KAAA;QACA;QACA;QACAP,GAAA,CAAAa,IAAA,GAAA7H,MAAA;QACA,IAAAwN,OAAA,GAAAxG,GAAA,CAAAoG,OAAA,EAAAtH,MAAA,WAAA2H,CAAA;UAAA,OAAAH,WAAA,CAAA/G,SAAA,WAAAmH,CAAA;YAAA,OAAAD,CAAA,CAAA3F,YAAA,KAAA4F,CAAA;UAAA;QAAA;QACA1G,GAAA,CAAA2G,2BAAA,GAAA3G,GAAA,CAAA4G,oBAAA;QACA,IAAAC,SAAA;QACAL,OAAA,CAAA9E,OAAA,WAAAoF,GAAA,EAAAC,KAAA;UACA,IAAA5E,IAAA,GAAAgE,MAAA,CAAAvF,YAAA,CAAAZ,GAAA,CAAAa,IAAA,EAAAiG,GAAA,CAAAhG,YAAA,EAAAgG,GAAA,CAAA/F,eAAA;UACA,IAAAiG,GAAA,GAAAb,MAAA,CAAAc,eAAA,CAAAjH,GAAA,CAAAa,IAAA,EAAAiG,GAAA,CAAAhG,YAAA,EAAAgG,GAAA,CAAA/F,eAAA;UACAf,GAAA,CAAAmC,IAAA,IAAA2E,GAAA,CAAAI,KAAA;UACAf,MAAA,CAAAjB,IAAA,CAAAlF,GAAA,cAAAmC,IAAA,EAAA2E,GAAA,CAAAI,KAAA;UACAlH,GAAA,CAAAgH,GAAA;UACAH,SAAA,IAAAC,GAAA,CAAAI,KAAA;UACAf,MAAA,CAAAjB,IAAA,CAAAlF,GAAA,gBAAA8G,GAAA,CAAApG,iBAAA,EAAAoG,GAAA,CAAAK,mBAAA,GAAAL,GAAA,CAAAI,KAAA;QACA;QAEAf,MAAA,CAAAjB,IAAA,CAAAlF,GAAA,mBAAAA,GAAA,CAAA2G,2BAAA,GAAAE,SAAA;QACA7G,GAAA,CAAA4G,oBAAA,GAAA5G,GAAA,CAAAkC,aAAA;QACAiE,MAAA,CAAAjB,IAAA,CAAAlF,GAAA,sBAAAA,GAAA,CAAA4G,oBAAA;QAEAT,MAAA,CAAAiB,WAAA,CAAApH,GAAA;QAEAA,GAAA,CAAAqH,OAAA;QACA,OAAArH,GAAA;MACA;IACA;IACAsH,WAAA,WAAAA,YAAAtH,GAAA;MACA,KAAAoH,WAAA,CAAApH,GAAA;IACA;IACAoH,WAAA,WAAAA,YAAApH,GAAA;MACA,IAAA6G,SAAA;MACA,IAAAU,eAAA,GAAA5I,MAAA,CAAA6I,IAAA,CAAAxH,GAAA,EACAlB,MAAA,WAAAC,CAAA;QAAA,QAAAA,CAAA,CAAA0I,QAAA,WAAA1I,CAAA,CAAA2I,UAAA,CAAA1H,GAAA,CAAAa,IAAA,KAAA9B,CAAA,CAAAoB,MAAA,GAAAH,GAAA,CAAAa,IAAA,CAAAV,MAAA;MAAA;MACAoH,eAAA,CAAA7F,OAAA,WAAAT,GAAA;QACA,IAAA0G,OAAA,GAAA1G,GAAA,CAAAV,KAAA,CAAA9G,YAAA;QACA,IAAAmO,UAAA,GAAAL,eAAA,CAAAzI,MAAA,WAAA+I,CAAA;UACA,IAAA1F,IAAA,GAAA0F,CAAA,CAAAtH,KAAA,CAAA9G,YAAA;UACA,OAAAoO,CAAA,KAAA5G,GAAA,IAAAkB,IAAA,KAAAwF,OAAA;QACA,GAAA9B,MAAA,WAAAC,GAAA,EAAA5G,IAAA;UACA,OAAA4G,GAAA,GAAA7M,OAAA,CAAA+G,GAAA,CAAAd,IAAA,GAAArF,KAAA;QACA;QACAmG,GAAA,CAAAiB,GAAA,GAAAxH,YAAA,YAAAuG,GAAA,CAAA8H,cAAA,GAAAF,UAAA;QACAf,SAAA,KAAA7G,GAAA,CAAAiB,GAAA;MACA;MACA;MACA;IACA;IACA8G,mBAAA,WAAAA,oBAAAC,UAAA,EAAAC,WAAA;MACA,KAAAD,UAAA;MACA,IAAA1K,IAAA,IAAA0K,UAAA,aAAAA,UAAA,uBAAAA,UAAA,CAAAzH,KAAA;MACA,SAAAjD,IAAA,CAAA4K,IAAA,WAAAnJ,CAAA;QAAA,OAAAA,CAAA,KAAAkJ,WAAA;MAAA;IACA;IAEAE,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MAAA,IAAAtN,MAAA,GAAAoF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,WAAApF,MAAA;MACA;MACA,IAAAuN,SAAA,GAAAlK,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAmK,SAAA,CAAAxN,MAAA;MAAA,IAAAyN,KAAA,YAAAA,MAAA,EACA;UACA,IAAA5G,OAAA,GAAA0G,SAAA,CAAAG,CAAA;UACA,IAAAlL,IAAA;UACA,KAAAqE,OAAA,CAAA4E,eAAA;YACA6B,MAAA,CAAAxI,QAAA;cACAxD,OAAA;cACAb,IAAA;YACA;YAAA;cAAAwD,CAAA,EACA;gBAAA0J,MAAA;cAAA;YAAA;UACA;UACA,IAAAnC,WAAA,GAAAoC,KAAA,CAAAC,IAAA,KAAAC,GAAA,CAAAjH,OAAA,CAAA4E,eAAA,CAAAhG,KAAA;UACA+F,WAAA,CAAA5E,OAAA,WAAAS,IAAA;YACA,IAAA0G,MAAA,GAAAT,MAAA,CAAAlN,WAAA,CAAA4D,MAAA,WAAAC,CAAA;cAAA,OAAAA,CAAA,CAAA+B,YAAA,KAAAqB,IAAA;YAAA;YAEA,IAAA2G,UAAA,GAAAD,MAAA,CAAA5J,GAAA,WAAA8J,KAAA,EAAAhC,KAAA;cACA,IAAAiC,KAAA,GAAAZ,MAAA,CAAAxH,YAAA,CAAAe,OAAA,CAAAd,IAAA,EAAAsB,IAAA,EAAA4G,KAAA,CAAAhI,eAAA;cACA,IAAAkI,IAAA,GAAAb,MAAA,CAAAnB,eAAA,CAAAtF,OAAA,CAAAd,IAAA,EAAAsB,IAAA,EAAA4G,KAAA,CAAAhI,eAAA;cACA,IAAAmI,GAAA;gBACAC,SAAA,EAAAxH,OAAA,CAAAwH,SAAA;gBACAC,WAAA,GAAAzH,OAAA,CAAAqH,KAAA;gBACAtN,SAAA,EAAA0M,MAAA,CAAAvL,KAAA,UAAA8E,OAAA,CAAAjG,SAAA;gBACAoF,YAAA,EAAAqB,IAAA;gBACAoE,eAAA,EAAA5E,OAAA,CAAA4E,eAAA;gBACAxF,eAAA,EAAAgI,KAAA,CAAAhI,eAAA;gBACAL,iBAAA,EAAAqI,KAAA,CAAArI,iBAAA;gBACA2I,YAAA,EAAA1H,OAAA,CAAAmC,gBAAA,CAAArD,IAAA,WAAAvB,IAAA;kBAAA,OAAAA,IAAA,CAAA6B,eAAA,KAAAgI,KAAA,CAAAhI,eAAA;gBAAA,GAAAsI;cACA;cACA,OAAA1H,OAAA,aAAAqH,KAAA;cACA,OAAArH,OAAA;cACA,OAAAA,OAAA;cACA,OAAAA,OAAA,CAAAqH,KAAA;cACA,OAAArH,OAAA,CAAAsH,IAAA;cACA,OAAAC,GAAA;YACA;YACA5L,IAAA,CAAA4I,IAAA,CAAAhC,KAAA,CAAA5G,IAAA,EAAAgM,kBAAA,CAAAR,UAAA;UACA;UACArK,OAAA,CAAAC,GAAA,CAAApB,IAAA;UACA,OAAAqE,OAAA;UACA,OAAAA,OAAA;UACAA,OAAA,CAAAmC,gBAAA,GAAAxG,IAAA;QACA;QAAAiM,IAAA;MAxCA,SAAAf,CAAA,MAAAA,CAAA,GAAAH,SAAA,CAAAlI,MAAA,EAAAqI,CAAA;QAAAe,IAAA,GAAAhB,KAAA;QAAA,IAAAgB,IAAA,SAAAA,IAAA,CAAAxK,CAAA;MAAA;MAyCA;QAAAsJ,SAAA,EAAAA,SAAA;QAAAI,MAAA;MAAA;IACA;IACAe,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,qBAAA,QAAAvB,eAAA;QAAAE,SAAA,GAAAqB,qBAAA,CAAArB,SAAA;QAAAI,MAAA,GAAAiB,qBAAA,CAAAjB,MAAA;MACA,KAAAA,MAAA;MACA,KAAAkB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAtO,IAAA;MACA,GAAAwH,IAAA;QACA0G,MAAA,CAAA9O,OAAA;QACA,IAAAuO,GAAA;UACA9F,cAAA,EAAAqG,MAAA,CAAA9F,YAAA;UACAN,eAAA,EAAAgF;QACA;QACA,IAAAyB,OAAA;UACA1G,cAAA,EAAAqG,MAAA,CAAA9F,YAAA;UACAoG,cAAA,EAAA1B;QACA;QACA,IAAA2B,SAAA,GAAAP,MAAA,CAAA5M,KAAA,GAAArE,2BAAA,GAAAiR,MAAA,CAAA3M,UAAA,GAAAnE,sCAAA,GAAAD,+BAAA;QACA+F,OAAA,CAAAC,GAAA,QAAAwK,GAAA;QACAc,SAAA,CAAAP,MAAA,CAAA5M,KAAA,GAAAqM,GAAA,GAAAY,OAAA,EAAA/G,IAAA,WAAAG,GAAA;UACA,IAAAA,GAAA,CAAAO,SAAA;YACAgG,MAAA,CAAA7J,QAAA;cACAxD,OAAA;cACAb,IAAA;YACA;YACAkO,MAAA,CAAAQ,WAAA;UACA;YACAR,MAAA,CAAA7J,QAAA;cACAxD,OAAA,EAAA8G,GAAA,CAAAc,OAAA;cACAzI,IAAA;YACA;UACA;UACAkO,MAAA,CAAA9O,OAAA;QACA;MACA,GAAAuP,KAAA;QACAT,MAAA,CAAA7J,QAAA;UACArE,IAAA;UACAa,OAAA;QACA;MACA;IACA;IACA6N,WAAA,WAAAA,YAAA;MACA,KAAAE,SAAA;IACA;IACAA,SAAA,WAAAA,UAAA;MACAjR,YAAA,MAAAkR,MAAA,OAAA9L,MAAA;IACA;IACA+L,cAAA,WAAAA,eAAAC,KAAA;MACA,KAAAtP,iBAAA,GAAAsP,KAAA,CAAAC,OAAA;MACA9L,OAAA,CAAAC,GAAA,MAAA5D,MAAA;IACA;IACA0P,WAAA,WAAAA,YAAA;MAAA,IAAArJ,IAAA,GAAAjB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MACA,IAAAiB,IAAA,uBAAAA,IAAA;MACA,0BAAAvG,UAAA,CAAA2F,KAAA,CAAA9G,YAAA;IACA;IACA;IACAgR,gBAAA,WAAAA,iBAAA;MACA,IAAAnN,IAAA,QAAA+D,KAAA,CAAAC,MAAA,CAAAoJ,kBAAA,GAAA5L,MAAA,WAAAI,IAAA;QAAA,QAAAA,IAAA,CAAAqF,QAAA;MAAA;MACA,IAAAoG,YAAA,QAAA5P,YAAA,CAAA+D,MAAA,WAAAI,IAAA;QAAA,QAAA5B,IAAA,CAAA6B,QAAA,CAAAD,IAAA,MAAAA,IAAA,CAAAqF,QAAA;MAAA;MACA,KAAAlD,KAAA,CAAAC,MAAA,CAAAsJ,cAAA,CAAAtN,IAAA;MACA,KAAA+D,KAAA,CAAAC,MAAA,CAAAsJ,cAAA,CAAAD,YAAA;MACA,KAAA3P,iBAAA,QAAAqG,KAAA,CAAAC,MAAA,CAAAoJ,kBAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAG,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAC,aAAA;MAAA,IAAA/K,GAAA,GAAA8K,KAAA,CAAA9K,GAAA;QAAAgL,MAAA,GAAAF,KAAA,CAAAE,MAAA;QAAAC,WAAA,GAAAH,KAAA,CAAAG,WAAA;MACA,SAAArO,MAAA;MACA,IAAAoO,MAAA,CAAAE,KAAA;MACA,IAAAjD,WAAA,IAAA8C,aAAA,GAAAC,MAAA,CAAAE,KAAA,cAAAH,aAAA,uBAAAA,aAAA,CAAAxK,KAAA;MACA,YAAAwH,mBAAA,CAAA/H,GAAA,CAAAuG,eAAA,EAAA0B,WAAA;IACA;IACArH,YAAA,WAAAA,aAAAC,IAAA,EAAAoH,WAAA,EAAAkD,SAAA;MACA,UAAAtJ,MAAA,CAAAhB,IAAA,EAAAgB,MAAA,CAAApI,YAAA,EAAAoI,MAAA,CAAAoG,WAAA,EAAApG,MAAA,CAAApI,YAAA,EAAAoI,MAAA,CAAAsJ,SAAA;IACA;IACAlE,eAAA,WAAAA,gBAAApG,IAAA,EAAAoH,WAAA,EAAAkD,SAAA;MACA,YAAAvK,YAAA,CAAAC,IAAA,EAAAoH,WAAA,EAAAkD,SAAA,OAAAtJ,MAAA,CAAApI,YAAA;IACA;IACA;IACA2R,eAAA,WAAAA,gBAAA;MACA,KAAAtP,aAAA;MACA2C,OAAA,CAAAC,GAAA,MAAAxD,WAAA;IACA;IACA;IACAmQ,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,oBAAA,QAAAvQ,iBAAA,CAAAiE,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA+G,mBAAA;MAAA;MACA,IAAAuF,oBAAA,QAAAxQ,iBAAA,IAAA8F,YAAA;MACAjI,wBAAA;QACA0S,oBAAA,EAAAA,oBAAA;QACAC,oBAAA,EAAAA,oBAAA;QACAjP,kBAAA,OAAAA,kBAAA;QACA+F,YAAA,OAAAzF,KAAA,YAAAC,UAAA;QACA4F,SAAA,OAAAjG;MACA,GAAAsG,IAAA,WAAAG,GAAA;QACA,IAAAA,GAAA,CAAAO,SAAA;UACA,IAAAP,GAAA,CAAAQ,IAAA,CAAAvD,MAAA;YACAmL,MAAA,CAAAvP,iBAAA;YACA;UACA;UACAuP,MAAA,CAAAG,eAAA,CAAAvI,GAAA,CAAAQ,IAAA;QACA;UACA4H,MAAA,CAAA1L,QAAA;YACArE,IAAA;YACAa,OAAA,EAAA8G,GAAA,CAAAc;UACA;QACA;MACA;IACA;IACA0H,YAAA,WAAAA,aAAA;MACA,KAAA5P,aAAA;MACA;IACA;IACA6P,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAA5Q,iBAAA,CAAA0G,OAAA,WAAAxC,IAAA;QAAA,OACAA,IAAA,CAAA4E,gBAAA,CAAApC,OAAA,WAAA3C,CAAA;UACA,IAAAA,CAAA,CAAAgC,eAAA,KAAA6K,MAAA,CAAA5P,IAAA,CAAAC,SAAA;YACA8C,CAAA,CAAAmI,KAAA,GAAAhI,IAAA,CAAA0H,oBAAA;UACA;YACA7H,CAAA,CAAAmI,KAAA;UACA;QACA;MAAA,CACA;MACA;MAAA,IAAA2E,MAAA,YAAAA,OAAA,EACA;QACA,IAAAlK,OAAA,GAAAiK,MAAA,CAAA5Q,iBAAA,CAAAwN,CAAA;QACA,IAAAlC,WAAA,GAAAoC,KAAA,CAAAC,IAAA,KAAAC,GAAA,CAAAjH,OAAA,CAAA4E,eAAA,CAAAhG,KAAA;QACA+F,WAAA,CAAA5E,OAAA,WAAAS,IAAA;UACA,IAAA0G,MAAA,GAAA+C,MAAA,CAAA1Q,WAAA,CAAA4D,MAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAA+B,YAAA,KAAAqB,IAAA;UAAA;UACA0G,MAAA,CAAAnH,OAAA,WAAAqH,KAAA;YACA,IAAA+C,OAAA,GAAAF,MAAA,CAAAhL,YAAA,CAAAe,OAAA,CAAAd,IAAA,EAAAsB,IAAA,EAAAyJ,MAAA,CAAA5P,IAAA,CAAAC,SAAA;YACA,IAAA+M,KAAA,GAAA4C,MAAA,CAAAhL,YAAA,CAAAe,OAAA,CAAAd,IAAA,EAAAsB,IAAA,EAAA4G,KAAA,CAAAhI,eAAA;YACA,IAAAgL,MAAA,GAAAH,MAAA,CAAA3E,eAAA,CAAAtF,OAAA,CAAAd,IAAA,EAAAsB,IAAA,EAAAyJ,MAAA,CAAA5P,IAAA,CAAAC,SAAA;YACA,IAAAgN,IAAA,GAAA2C,MAAA,CAAA3E,eAAA,CAAAtF,OAAA,CAAAd,IAAA,EAAAsB,IAAA,EAAA4G,KAAA,CAAAhI,eAAA;YAEA,IAAA+K,OAAA,KAAA9C,KAAA,IAAA+C,MAAA,KAAA9C,IAAA;cACAtH,OAAA,CAAAqH,KAAA,IAAArH,OAAA;cACAA,OAAA,CAAAsH,IAAA,IAAAtH,OAAA;YACA;cACAA,OAAA,CAAAqH,KAAA;cACArH,OAAA,CAAAsH,IAAA;YACA;UACA;QACA;MACA;MApBA,SAAAT,CAAA,MAAAA,CAAA,QAAAxN,iBAAA,CAAAmF,MAAA,EAAAqI,CAAA;QAAAqD,MAAA;MAAA;MAqBApN,OAAA,CAAAC,GAAA,MAAA1D,iBAAA;MAAA,IAAAgR,MAAA,YAAAA,OAAAC,EAAA,EACA;QAAA,IAAAC,MAAA,YAAAA,OAAAC,CAAA,EACA;UACA,IAAAP,MAAA,CAAA9Q,MAAA,CAAA0N,EAAA,EAAA3H,IAAA,KAAA+K,MAAA,CAAA5Q,iBAAA,CAAAmR,CAAA,EAAAtL,IAAA;YACA+K,MAAA,CAAAQ,SAAA,WAAAhI,CAAA;cACAwH,MAAA,CAAA9Q,MAAA,CAAA0N,EAAA,IAAAoD,MAAA,CAAA5Q,iBAAA,CAAAmR,CAAA;YACA;UACA;QACA;QANA,SAAAA,CAAA,MAAAA,CAAA,GAAAP,MAAA,CAAA5Q,iBAAA,CAAAmF,MAAA,EAAAgM,CAAA;UAAAD,MAAA,CAAAC,CAAA;QAAA;MAOA;MARA,SAAA3D,EAAA,MAAAA,EAAA,QAAA1N,MAAA,CAAAqF,MAAA,EAAAqI,EAAA;QAAAwD,MAAA,CAAAC,EAAA;MAAA;IASA;IACAI,UAAA,WAAAA,WAAAC,QAAA;MAAA,IAAAC,MAAA;MACA,KAAAlL,KAAA,CAAAiL,QAAA,EAAAE,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACAF,MAAA,CAAAG,YAAA;UACA;UACAH,MAAA,CAAAb,YAAA;UACAjN,OAAA,CAAAC,GAAA,CAAA6N,MAAA,CAAAzR,MAAA;UACAyR,MAAA,CAAAvR,iBAAA;UACAuR,MAAA,CAAAlL,KAAA,CAAAC,MAAA,CAAAW,gBAAA;QACA;UACA;QACA;MACA;IACA;IACA;IACAwJ,eAAA,WAAAA,gBAAAkB,cAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,iBAAA,OAAAC,GAAA;MACAH,cAAA,CAAAjL,OAAA,WAAAxC,IAAA;QACA,IAAAwF,GAAA,MAAA7C,MAAA,CAAA3C,IAAA,CAAA+G,mBAAA,OAAApE,MAAA,CAAA3C,IAAA,CAAA6B,eAAA;QACA8L,iBAAA,CAAAE,GAAA,CAAArI,GAAA,EAAAxF,IAAA,CAAA8N,kBAAA;MACA;MAEA,IAAAC,eAAA,YAAAA,gBAAAjN,GAAA,EAAAQ,IAAA,EAAA0M,MAAA;QACA,IAAAC,OAAA,GAAAP,OAAA,CAAAhM,YAAA,CAAAZ,GAAA,CAAAa,IAAA,EAAAL,IAAA,CAAAM,YAAA,EAAAN,IAAA,CAAAO,eAAA;QACA,IAAAqM,IAAA,MAAAvL,MAAA,CAAA7B,GAAA,CAAAiG,mBAAA,OAAApE,MAAA,CAAArB,IAAA,CAAAO,eAAA;QACA,IAAAsM,gBAAA,GAAAR,iBAAA,CAAAS,GAAA,CAAAF,IAAA;QACA,IAAAG,SAAA,GAAAC,IAAA,CAAAC,GAAA,CAAAP,MAAA,EAAAG,gBAAA,EAAArN,GAAA,CAAA4G,oBAAA;QAEA5G,GAAA,CAAAmN,OAAA,KAAAO,MAAA,CAAA1N,GAAA,CAAAmN,OAAA,WAAAI,SAAA;QACAvN,GAAA,CAAA4G,oBAAA,IAAA2G,SAAA;QACAvN,GAAA,CAAA4M,OAAA,CAAApC,WAAA,CAAAhK,IAAA,CAAAE,iBAAA,MAAAV,GAAA,CAAA4M,OAAA,CAAApC,WAAA,CAAAhK,IAAA,CAAAE,iBAAA,WAAA6M,SAAA;QACAvN,GAAA,CAAA4M,OAAA,CAAApC,WAAA,OAAAxK,GAAA,CAAA4M,OAAA,CAAApC,WAAA,YAAA+C,SAAA;QACAvN,GAAA,aAAAmN,OAAA,IAAAnN,GAAA,CAAAmN,OAAA;QAEA,OAAAI,SAAA;MACA;MACA,IAAAI,SAAA;MACA,KAAA3S,iBAAA,CAAA0G,OAAA,WAAA1B,GAAA;QACA,KAAAA,GAAA,CAAA4G,oBAAA,IAAAgG,OAAA,CAAAhS,UAAA;QAEA,IAAAgT,aAAA,GAAA5N,GAAA,CAAA8D,gBAAA,CAAAhF,MAAA,WAAA0B,IAAA;UAAA,OACAqM,iBAAA,CAAAgB,GAAA,IAAAhM,MAAA,CAAA7B,GAAA,CAAAiG,mBAAA,OAAApE,MAAA,CAAArB,IAAA,CAAAO,eAAA;QAAA,CACA;QAEA,IAAA6M,aAAA,CAAAzN,MAAA;QACA,IAAAyM,OAAA,CAAAhS,UAAA;UACA,IAAAkT,OAAA;UACA,IAAAC,cAAA,GAAAH,aAAA,CAAA/H,MAAA,WAAAmI,GAAA,EAAAxN,IAAA;YACA,IAAA4M,IAAA,MAAAvL,MAAA,CAAA7B,GAAA,CAAAiG,mBAAA,OAAApE,MAAA,CAAArB,IAAA,CAAAO,eAAA;YACA,OAAAiN,GAAA,IAAAnB,iBAAA,CAAAS,GAAA,CAAAF,IAAA;UACA;UAEA,IAAApN,GAAA,CAAAiO,gBAAA,GAAAF,cAAA;YACAD,OAAA;YACA,IAAAA,OAAA,KAAAlB,OAAA,CAAA5R,iBAAA,CAAAmF,MAAA;cACAwN,SAAA;cACAf,OAAA,CAAA7Q,iBAAA;YACA;YACA;UACA;UAEA,IAAAmS,SAAA,GAAAV,IAAA,CAAAC,GAAA,CAAAzN,GAAA,CAAAkC,aAAA,EAAAlC,GAAA,CAAA4G,oBAAA;UACA,IAAAuH,iBAAA,GAAAX,IAAA,CAAAY,KAAA,CAAAF,SAAA,GAAAN,aAAA,CAAAzN,MAAA;UAEAyN,aAAA,CAAAlM,OAAA,WAAAlB,IAAA,EAAAuG,KAAA;YACA,IAAAmH,SAAA;YAEA,IAAAG,cAAA,GAAAtH,KAAA,KAAA6G,aAAA,CAAAzN,MAAA,OACA+N,SAAA,GACAV,IAAA,CAAAC,GAAA,CAAAU,iBAAA,EAAAD,SAAA;YAEAA,SAAA,IAAAjB,eAAA,CAAAjN,GAAA,EAAAQ,IAAA,EAAA6N,cAAA;UACA;UAEArO,GAAA,CAAAiO,gBAAA,GAAAjO,GAAA,CAAA4G,oBAAA;UACA5G,GAAA,CAAAkC,aAAA,GAAAlC,GAAA,CAAA4G,oBAAA;UACA,IAAAkH,OAAA,KAAAlB,OAAA,CAAA5R,iBAAA,CAAAmF,MAAA;YACAwN,SAAA;YACAf,OAAA,CAAA7Q,iBAAA;YACA;UACA;QACA;UACA6R,aAAA,CAAAlM,OAAA,WAAAlB,IAAA;YACA,IAAA4M,IAAA,MAAAvL,MAAA,CAAA7B,GAAA,CAAAiG,mBAAA,OAAApE,MAAA,CAAArB,IAAA,CAAAO,eAAA;YACA,IAAAsM,gBAAA,GAAAR,iBAAA,CAAAS,GAAA,CAAAF,IAAA;YAEA,IAAApN,GAAA,CAAA4M,OAAA,CAAA7M,WAAA,CAAAC,GAAA,KAAAqN,gBAAA;cACA;YACA;YAEA,IAAAiB,SAAA,GAAAd,IAAA,CAAAC,GAAA,CACAzN,GAAA,CAAA4M,OAAA,CAAA7M,WAAA,CAAAC,GAAA,oBACAA,GAAA,CAAA4M,OAAA,CAAA7M,WAAA,CAAAC,GAAA,SACAqN,gBACA;YAEA,IAAAiB,SAAA;cACA,IAAAnB,OAAA,GAAAP,OAAA,CAAAhM,YAAA,CAAAZ,GAAA,CAAAa,IAAA,EAAAL,IAAA,CAAAM,YAAA,EAAAN,IAAA,CAAAO,eAAA;cACAf,GAAA,CAAA4M,OAAA,CAAApC,WAAA,CAAAhK,IAAA,CAAAE,iBAAA,MAAAV,GAAA,CAAA4M,OAAA,CAAApC,WAAA,CAAAhK,IAAA,CAAAE,iBAAA,WAAA4N,SAAA;cACAtO,GAAA,CAAA4M,OAAA,CAAApC,WAAA,OAAAxK,GAAA,CAAA4M,OAAA,CAAApC,WAAA,YAAA8D,SAAA;cACAtO,GAAA,CAAAmN,OAAA,KAAAO,MAAA,CAAA1N,GAAA,CAAAmN,OAAA,WAAAmB,SAAA;cACAtO,GAAA,CAAA4M,OAAA,CAAA7M,WAAA,CAAAC,GAAA,MAAAA,GAAA,CAAA4M,OAAA,CAAA7M,WAAA,CAAAC,GAAA,WAAAsO,SAAA;cACAtO,GAAA,CAAA4M,OAAA,CAAA7M,WAAA,CAAAC,GAAA,iBAAAA,GAAA,CAAA4M,OAAA,CAAA7M,WAAA,CAAAC,GAAA,sBAAAsO,SAAA;cACAtO,GAAA,aAAAmN,OAAA,IAAAnN,GAAA,CAAAmN,OAAA;YACA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,KAAApS,YAAA,QAAAD,MAAA,CAAAgE,MAAA,WAAAkB,GAAA;QACA,OAAA4M,OAAA,CAAA5K,UAAA,CAAAhC,GAAA;MACA;MACA,KAAAhF,iBAAA;MACA,KAAAqG,KAAA,CAAAC,MAAA,CAAAW,gBAAA;MACA,IAAA0L,SAAA;QACA,KAAA/N,QAAA;UACAxD,OAAA;UACAb,IAAA;QACA;MACA;IACA;IACA;IACAmR,YAAA,WAAAA,aAAA;MAAA,IAAA6B,OAAA;MACA,SAAA3T,UAAA;QACA,KAAAI,iBAAA,CACA0G,OAAA,WAAA1B,GAAA,EAAAxC,GAAA;UACA,IAAAwC,GAAA,CAAA4G,oBAAA;YACA,IAAApG,IAAA,GAAAR,GAAA,CAAA8D,gBAAA,CAAArD,IAAA,WAAA1B,CAAA;cAAA,OAAAA,CAAA,CAAAgC,eAAA,KAAAwN,OAAA,CAAAvS,IAAA,CAAAC,SAAA;YAAA;YACA,IAAAkR,OAAA,GAAAoB,OAAA,CAAA3N,YAAA,CAAAZ,GAAA,CAAAa,IAAA,EAAAL,IAAA,CAAAM,YAAA,EAAAN,IAAA,CAAAO,eAAA;YACAf,GAAA,CAAAmN,OAAA,IAAAO,MAAA,CAAA1N,GAAA,CAAAmN,OAAA,KAAAnN,GAAA,CAAAkC,aAAA;YACAlC,GAAA,CAAA4G,oBAAA,GAAA5G,GAAA,CAAA4G,oBAAA,GAAA5G,GAAA,CAAAkC,aAAA;YACAlC,GAAA,CAAAuO,OAAA,CAAA/D,WAAA,CAAAhK,IAAA,CAAAE,iBAAA,MAAAV,GAAA,CAAAkC,aAAA;YACAlC,GAAA,CAAAuO,OAAA,CAAA/D,WAAA,OAAAxK,GAAA,CAAAkC,aAAA;YACAlC,GAAA,CAAAiO,gBAAA,GAAAjO,GAAA,CAAA4G,oBAAA;YACA5G,GAAA,CAAAkC,aAAA,GAAAlC,GAAA,CAAA4G,oBAAA;YACA5G,GAAA,aAAAmN,OAAA,IAAAnN,GAAA,CAAAmN,OAAA;UACA;QACA;MACA;QACA,KAAAnS,iBAAA,CACA0G,OAAA,WAAA1B,GAAA,EAAAxC,GAAA;UACA,IAAAgD,IAAA,GAAAR,GAAA,CAAA8D,gBAAA,CAAArD,IAAA,WAAA1B,CAAA;YAAA,OAAAA,CAAA,CAAAgC,eAAA,KAAAwN,OAAA,CAAAvS,IAAA,CAAAC,SAAA;UAAA;UACA,IAAAkR,OAAA,GAAAoB,OAAA,CAAA3N,YAAA,CAAAZ,GAAA,CAAAa,IAAA,EAAAL,IAAA,CAAAM,YAAA,EAAAN,IAAA,CAAAO,eAAA;UAEA,IAAAuN,SAAA,GAAAd,IAAA,CAAAC,GAAA,CAAAzN,GAAA,CAAAuO,OAAA,CAAAxO,WAAA,CAAAC,GAAA,eAAAA,GAAA,CAAAuO,OAAA,CAAAxO,WAAA,CAAAC,GAAA;UAEAA,GAAA,CAAAuO,OAAA,CAAA/D,WAAA,CAAAhK,IAAA,CAAAE,iBAAA,MAAA4N,SAAA;UACAtO,GAAA,CAAAuO,OAAA,CAAA/D,WAAA,OAAA8D,SAAA;UAEAtO,GAAA,CAAAmN,OAAA,IAAAO,MAAA,CAAA1N,GAAA,CAAAmN,OAAA,KAAAmB,SAAA;UACAtO,GAAA,CAAAuO,OAAA,CAAAxO,WAAA,CAAAC,GAAA,MAAAsO,SAAA;UACAtO,GAAA,CAAAuO,OAAA,CAAAxO,WAAA,CAAAC,GAAA,iBAAAsO,SAAA;UAEAtO,GAAA,aAAAmN,OAAA,IAAAnN,GAAA,CAAAmN,OAAA;QACA;MACA;MACA,KAAApS,YAAA,QAAAD,MAAA,CAAAgE,MAAA,WAAAkB,GAAA;QACA,OAAAuO,OAAA,CAAAvM,UAAA,CAAAhC,GAAA;MACA;IACA;IACAwO,SAAA,WAAAA,UAAAlC,QAAA;MACA,KAAAjL,KAAA,CAAAiL,QAAA,EAAAmC,WAAA;MACA,KAAA3S,aAAA;IACA;IACA4S,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAC,MAAA,GAAAD,KAAA,CAAAC,MAAA;QAAA5O,GAAA,GAAA2O,KAAA,CAAA3O,GAAA;MACA,OAAAA,GAAA,CAAA+E,IAAA,CAAA5F,QAAA,CAAAyP,MAAA,CAAA3U,IAAA;IACA;IACA4U,uBAAA,WAAAA,wBAAAC,KAAA;MAAA,IAAAF,MAAA,GAAAE,KAAA,CAAAF,MAAA;MACAA,MAAA,CAAA3U,IAAA;IACA;IACA8U,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAJ,MAAA,GAAAI,KAAA,CAAAJ,MAAA;QAAA5O,GAAA,GAAAgP,KAAA,CAAAhP,GAAA;MACAvB,OAAA,CAAAC,GAAA,gBAAAkQ,MAAA,EAAA5O,GAAA;MACA,OAAAA,GAAA,MAAAnD,KAAA,8BAAAsC,QAAA,CAAAyP,MAAA,CAAA3U,IAAA;IACA;IACAgV,uBAAA,WAAAA,wBAAAC,KAAA;MAAA,IAAAN,MAAA,GAAAM,KAAA,CAAAN,MAAA;MACAA,MAAA,CAAA3U,IAAA;IACA;IACAwF,iBAAA,WAAAA,kBAAA;MAAA,IAAA0P,OAAA;MACA/V,eAAA;QAAAgW,YAAA;MAAA,GAAArM,IAAA,WAAAG,GAAA;QACA,IAAAA,GAAA,CAAAO,SAAA;UACA0L,OAAA,CAAA9U,cAAA,CAAAJ,IAAA,GAAAiJ,GAAA,CAAAQ,IAAA;UACAyL,OAAA,CAAA/C,SAAA,WAAAhI,CAAA;YAAA,IAAAiL,aAAA;YACA,CAAAA,aAAA,GAAAF,OAAA,CAAA9N,KAAA,cAAAgO,aAAA,gBAAAA,aAAA,GAAAA,aAAA,CAAAC,oBAAA,cAAAD,aAAA,eAAAA,aAAA,CAAAE,iBAAA,CAAArM,GAAA,CAAAQ,IAAA;UACA;QACA;UACAyL,OAAA,CAAAvP,QAAA;YACArE,IAAA;YACAa,OAAA,EAAA8G,GAAA,CAAAc;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
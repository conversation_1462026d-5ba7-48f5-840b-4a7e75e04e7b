{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\detail.vue", "mtime": 1757909680922}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetGridByCode", "FIX_COLUMN", "AdjustTeamProcessAllocation", "GetTeamProcessAllocation", "AdjustPartTeamProcessAllocation", "AdjustSubAssemblyTeamProcessAllocation", "GetStopList", "GetPreStepTaskAllocation", "QrcodeVue", "v4", "uuidv4", "numeral", "closeTagView", "deepClone", "GetCompTypeTree", "SPLIT_SYMBOL", "components", "filters", "filterNum", "value", "divide", "format", "data", "treeSelectParams", "placeholder", "clearable", "ObjectTypeList", "clickParent", "props", "children", "label", "tbLoading", "loading", "activeName", "tipLabel", "tbData", "filterTbData", "multipleSelection", "columns", "workingTeam", "workingTeamColumn", "formInline", "pg_type", "searchType", "type", "queryForm", "Comp_Codes", "Part_Code", "Spec", "Comp_Codes_Vague", "Part_Code_Vague", "dialogVisible", "dialogTipsVisible", "form", "TeamGroup", "rules", "required", "message", "trigger", "Is_Workshop_Enabled", "Working_Process_Id", "computed", "<PERSON><PERSON><PERSON><PERSON>", "isCom", "isUnitPart", "mounted", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "rowInfo", "idx", "idx2", "_idx", "_idx2", "idx3", "wrap", "_callee$", "_context", "prev", "next", "JSON", "parse", "decodeURIComponent", "$route", "query", "other", "console", "log", "Object", "assign", "bomLevel", "getTableConfig", "findIndex", "item", "Code", "splice", "getObjectTypeList", "fetchData", "t0", "$message", "stop", "methods", "getRowCCode", "row", "prefix", "arguments", "length", "undefined", "suffix", "arr", "split", "team", "find", "v", "Working_Team_Name", "u", "getRowUnique", "uuid", "Process_Code", "Working_Team_Id", "handleClick", "val", "_this2", "name", "c1", "$refs", "xTable", "getColumnByField", "c2", "visible", "c3", "for<PERSON>ach", "element", "codes", "concat", "c", "refreshColumn", "filter", "filterZero", "clearCheckboxRow", "allocatedTask", "code", "_this3", "trim", "Process_Type", "Page", "PageSize", "Step", "Bom_Level", "Schduling_Code", "Workshop_Name", "Area_Id", "InstallUnit_Id", "then", "_ref", "_callee2", "res", "_res$Data", "Schduling_Plan", "Sch<PERSON>ling_Comps", "_kk", "_callee2$", "_context2", "IsSucceed", "Data", "planInfoTemp", "getStopList", "initTbData", "Allocation_Teams", "map", "filterData", "Message", "_x", "apply", "finally", "_", "checkMethod", "_ref2", "stopFlag", "list", "_this4", "_callee3", "key", "submitObj", "_callee3$", "_context3", "Id", "Type", "stopMap", "Is_Stop", "$set", "_this5", "queryCode", "codeList", "queryCodeVague", "codeListVague", "searchTbData", "includes", "vagueData", "searchVagueTbData", "mergedArray", "uniqueArray", "reduce", "acc", "current", "existingObject", "<PERSON><PERSON><PERSON>ling_Detail_Id", "push", "_this6", "teamKey", "_row$Technology_Path", "processList", "Technology_Path", "newData", "r", "p", "defaultCan_Allocation_Count", "Can_Allocation_Count", "_inputNum", "ele", "index", "max", "getRowUniqueMax", "Count", "Total_Receive_Count", "setInputMax", "checked", "inputChange", "inputValuesKeys", "keys", "endsWith", "startsWith", "curCode", "otherTotal", "x", "<PERSON><PERSON><PERSON><PERSON>_Count", "checkPermissionTeam", "processStr", "processCode", "some", "getSubmitTbInfo", "_this7", "tableData", "stringify", "_loop", "i", "status", "Array", "from", "Set", "groups", "groupsList", "group", "uCode", "uMax", "obj", "Comp_Code", "Again_Count", "Team_Task_Id", "_toConsumableArray", "_ret", "handleSubmit", "_this8", "_this$getSubmitTbInfo", "$confirm", "confirmButtonText", "cancelButtonText", "<PERSON><PERSON><PERSON>", "SarePartsModel", "requestFn", "handleClose", "catch", "<PERSON><PERSON><PERSON><PERSON>", "$store", "tbSelectChange", "array", "records", "getTaskCode", "reverseSelection", "getCheckboxRecords", "unSelectList", "setCheckboxRow", "_this9", "_callee4", "_callee4$", "_context4", "tbConfig", "Grid", "ColumnList", "Is_Display", "fixed", "activeCellMethod", "_ref3", "_column$field", "column", "columnIndex", "field", "workingId", "Batchallocation", "preStepTaskAllocation", "_this0", "Sc<PERSON><PERSON>ling_Detail_Ids", "Working_Process_Code", "preDoAllocation", "handleDialog", "handelData", "_this1", "_loop2", "uniCode", "uniMax", "_loop3", "_i", "_loop4", "k", "$nextTick", "submitForm", "formName", "_this10", "validate", "valid", "doAllocation", "preProcessData", "_this11", "preProcessDataMap", "Map", "set", "Current_Task_Count", "allocateForTeam", "amount", "tarCode", "b<PERSON><PERSON>", "currentTaskCount", "get", "allocated", "Math", "min", "Number", "isMessage", "eligibleTeams", "has", "IsAllNo", "totalAvailable", "sum", "allocatedTaskMax", "remaining", "perTeamAllocation", "floor", "allocateAmount", "selectNum", "_this12", "resetForm", "resetFields", "filterTypeMethod", "_ref4", "option", "filterTypeRecoverMethod", "_ref5", "filterCodeMethod", "_ref6", "filterCodeRecoverMethod", "_ref7", "_this13", "professional", "_this13$$refs", "treeSelectObjectType", "treeDataUpdateFun"], "sources": ["src/views/PRO/plan-production/task-allocation/v4/detail.vue"], "sourcesContent": ["<template>\r\n  <div v-loading=\"loading\" class=\"app-container abs100\">\r\n    <el-card class=\"box-card h100\">\r\n      <h4 class=\"topTitle\"><span />基本信息</h4>\r\n\r\n      <el-form\r\n        ref=\"formInline\"\r\n        label-position=\"right\"\r\n        label-width=\"90px\"\r\n        :inline=\"true\"\r\n        :model=\"formInline\"\r\n        class=\"demo-form-inline\"\r\n      >\r\n        <el-row>\r\n          <el-col :span=\"20\">\r\n            <el-row>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"排产单号:\" label-width=\"75px\" prop=\"Schduling_Code\">\r\n                  <span>{{ formInline.Schduling_Code }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"项目名称:\" prop=\"Project_Name\">\r\n                  <span>{{ formInline.Project_Name }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"区域:\" prop=\"Area_Name\">\r\n                  <span>{{ formInline.Area_Name }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"批次:\" prop=\"Installunit_Name\">\r\n                  <span>{{ formInline.Installunit_Name }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"任务数量:\" label-width=\"75px\" prop=\"Allocation_Count\">\r\n                  <span>{{ formInline.Allocation_Count }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"任务重量:\" prop=\"Allocation_Weight\">\r\n                  <span>{{ formInline.Allocation_Weight | filterNum }}(t)</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"已完成数量:\" prop=\"Finish_Count\">\r\n                  <span>{{ formInline.Finish_Count }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"已完成重量:\" prop=\"Finish_Weight\">\r\n                  <span>{{ formInline.Finish_Weight | filterNum }}(t)</span>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <qrcode-vue\r\n              :size=\"79\"\r\n              :value=\"formInline.Schduling_Code\"\r\n              class-name=\"qrcode\"\r\n              level=\"H\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <el-divider class=\"elDivder\" />\r\n      <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\r\n        <el-tab-pane label=\"待分配\" name=\"first\" />\r\n        <el-tab-pane\r\n          v-for=\"(element, index2) in workingTeam\"\r\n          :key=\"index2\"\r\n          :label=\"element.Working_Team_Name\"\r\n          :name=\"`${element.Working_Team_Name}$_$${element.Process_Code}`\"\r\n        />\r\n      </el-tabs>\r\n      <div class=\"tb-options\" :style=\"{'justify-content': !isView ? 'space-between' : 'end'}\">\r\n        <div>\r\n          <el-button v-if=\"!isView\" plain type=\"primary\" @click=\"reverseSelection()\">反选</el-button>\r\n          <el-button v-if=\"!isView\" type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"Batchallocation()\">批量分配</el-button>\r\n          <el-button v-if=\"!isView && activeName==='first'\" type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"preStepTaskAllocation()\">上道工序同步</el-button>\r\n        </div>\r\n        <el-form :inline=\"true\" :model=\"queryForm\" class=\"demo-form-inline\">\r\n          <el-form-item label=\"规格\">\r\n            <el-input v-model.trim=\"queryForm.Spec\" clearable placeholder=\"请输入\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"isCom\" :label=\"isCom ? '构件类型' :isUnitPart?'部件类型':'零件类型'\">\r\n            <el-tree-select\r\n              ref=\"treeSelectObjectType\"\r\n              v-model=\"searchType\"\r\n              style=\"width: 100%\"\r\n              class=\"cs-tree-x\"\r\n              :select-params=\"treeSelectParams\"\r\n              :tree-params=\"ObjectTypeList\"\r\n              value-key=\"Id\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item :label=\"isCom ? '构件编号' :isUnitPart?'部件名称':'零件名称'\">\r\n            <el-input v-if=\"isCom\" v-model=\"queryForm.Comp_Codes\" clearable placeholder=\"请输入(空格区分/多个搜索)\" />\r\n            <el-input v-else v-model=\"queryForm.Part_Code\" clearable placeholder=\"请输入(空格区分/多个搜索)\" />\r\n            <el-input v-if=\"isCom\" v-model.trim=\"queryForm.Comp_Codes_Vague\" style=\"margin-left: 10px;\" clearable placeholder=\"模糊查找(请输入关键字)\" />\r\n            <el-input v-else v-model.trim=\"queryForm.Part_Code_Vague\" style=\"margin-left: 10px;\" clearable placeholder=\"模糊查找(请输入关键字)\" />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" @click=\"filterData\">查询</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n      <div class=\"tb-x\">\r\n        <vxe-table\r\n          ref=\"xTable\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          class=\"cs-vxe-table\"\r\n          :row-config=\"{ isCurrent: true, isHover: true }\"\r\n          align=\"left\"\r\n          height=\"100%\"\r\n          show-overflow\r\n          :loading=\"tbLoading\"\r\n          :checkbox-config=\"{checkField: 'checked',checkMethod }\"\r\n          stripe\r\n          size=\"medium\"\r\n          :edit-config=\"{\r\n            trigger: 'click',\r\n            mode: 'cell',\r\n            showIcon: !isView,\r\n            beforeEditMethod: activeCellMethod,\r\n          }\"\r\n          :data=\"filterTbData\"\r\n          resizable\r\n          :tooltip-config=\"{ enterable: true }\"\r\n          @checkbox-all=\"tbSelectChange\"\r\n          @checkbox-change=\"tbSelectChange\"\r\n        >\r\n          <vxe-column type=\"checkbox\" width=\"60\" fixed=\"left\" />\r\n          <template v-for=\"item in columns\">\r\n\r\n            <!--            <vxe-column :align=\"item.Align\"\r\n              v-if=\"item.Code==='Comp_Code'||item.Code==='Part_Code'\"\r\n              :key=\"item.Id\"\r\n              :filters=\"[{ data: '' }]\"\r\n              :filter-method=\"filterCodeMethod\"\r\n              :filter-recover-method=\"filterCodeRecoverMethod\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #filter=\"{ $panel, column }\">\r\n                <template v-for=\"(option, index) in column.filters\">\r\n                  <input :key=\"index\" v-model=\"option.data\" class=\"my-input\" type=\"type\" placeholder=\"按回车确认筛选\" @input=\"$panel.changeOption($event, !!option.data, option)\" @keyup.enter=\"$panel.confirmFilter()\">\r\n                </template>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column :align=\"item.Align\"\r\n              v-else-if=\"item.Code==='Type'\"\r\n              :key=\"item.Id\"\r\n              :filters=\"[{ data: '' }]\"\r\n              :filter-method=\"filterTypeMethod\"\r\n              :filter-recover-method=\"filterTypeRecoverMethod\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #filter=\"{ $panel, column }\">\r\n                <template v-for=\"(option, index) in column.filters\">\r\n                  <input :key=\"index\" v-model=\"option.data\" class=\"my-input\" type=\"type\" placeholder=\"按回车确认筛选\" @input=\"$panel.changeOption($event, !!option.data, option)\" @keyup.enter=\"$panel.confirmFilter()\">\r\n                </template>\r\n              </template>\r\n            </vxe-column>-->\r\n            <vxe-column\r\n              v-if=\"item.Code==='Comp_Code'|| item.Code==='Part_Code'\"\r\n              :key=\"item.Id\"\r\n              :align=\"item.Align\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :visible=\"item.visible\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                <el-tag v-if=\"!!row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                <span>{{ row[item.Code] }}</span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column\r\n              v-else-if=\"item.Code==='Schduled_Count'\"\r\n              :key=\"`SchduledCount${item.Id}`\"\r\n              :align=\"item.Align\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :visible=\"item.visible\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                {{ activeName === 'first' ?'':row[getTaskCode()] }}\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column\r\n              v-else\r\n              :key=\"`Default${item.Id}`\"\r\n              :align=\"item.Align\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              :visible=\"item.visible\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n            />\r\n          </template>\r\n\r\n          <vxe-column\r\n            v-for=\"(element, index2) in workingTeamColumn\"\r\n            :key=\"index2\"\r\n            :align=\"element.Align\"\r\n            :visible=\"element.visible\"\r\n            fixed=\"right\"\r\n            :field=\"`${element.Working_Team_Name}$_$${element.Process_Code}`\"\r\n            title=\"可分配数量\"\r\n            sortable\r\n            min-width=\"170\"\r\n          >\r\n            <template #edit=\"{ row }\">\r\n              <vxe-input\r\n                v-model.number=\"\r\n                  row[\r\n                    getRowUnique(\r\n                      row.uuid,\r\n                      element.Process_Code,\r\n                      element.Working_Team_Id\r\n                    )\r\n                  ]\r\n                \"\r\n                type=\"integer\"\r\n                :min=\"0\"\r\n                :max=\"\r\n                  row[\r\n                    getRowUniqueMax(\r\n                      row.uuid,\r\n                      element.Process_Code,\r\n                      element.Working_Team_Id\r\n                    )\r\n                  ]\r\n                \"\r\n                @change=\"\r\n                  inputChange(\r\n                    row,\r\n                    element.Process_Code,\r\n                    element.Working_Team_Id\r\n                  )\r\n                \"\r\n              />\r\n            </template>\r\n            <template #default=\"{ row }\">\r\n              <template\r\n                v-if=\"\r\n                  checkPermissionTeam(row.Technology_Path, element.Process_Code)\r\n                \"\r\n              >\r\n                {{\r\n                  row[\r\n                    getRowUnique(\r\n                      row.uuid,\r\n                      element.Process_Code,\r\n                      element.Working_Team_Id\r\n                    )\r\n                  ]\r\n                }}\r\n              </template>\r\n              <template v-else> -</template>\r\n            </template>\r\n          </vxe-column>\r\n\r\n          <vxe-column\r\n            :key=\"activeName\"\r\n            align=\"left\"\r\n            :edit-render=\"{}\"\r\n            field=\"AllocatedCount\"\r\n            title=\"分配数量\"\r\n            sortable\r\n            fixed=\"right\"\r\n            min-width=\"180\"\r\n          >\r\n            <template #edit=\"{ row }\">\r\n              <vxe-input\r\n                :key=\"activeName\"\r\n                v-model.number=\"row[getRowCCode(row,'alCount')]\"\r\n                type=\"integer\"\r\n                :min=\"0\"\r\n                :max=\"row[getRowCCode(row,'','Max')]\"\r\n              />\r\n            </template>\r\n            <template #default=\"{ row }\">\r\n              {{ row[getRowCCode(row,'alCount')] | displayValue }}\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n      </div>\r\n      <footer>\r\n        <div class=\"data-info\">\r\n          <el-tag size=\"medium\" class=\"info-x\">已选{{ multipleSelection.length }}条数据</el-tag>\r\n          <el-tag v-if=\"tipLabel\" size=\"medium\" class=\"info-x\">{{ tipLabel }}</el-tag>\r\n        </div>\r\n        <div>\r\n          <el-button @click=\"handleClose\">取消 </el-button>\r\n          <el-button\r\n            v-if=\"!isView\"\r\n            type=\"primary\"\r\n            :loading=\"loading\"\r\n            @click=\"handleSubmit\"\r\n          >提交</el-button>\r\n        </div>\r\n      </footer>\r\n    </el-card>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      title=\"批量分配\"\r\n      class=\"plm-custom-dialog\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"30%\"\r\n      @close=\"handleDialog\"\r\n    >\r\n      <el-form\r\n        ref=\"form\"\r\n        :model=\"form\"\r\n        :rules=\"rules\"\r\n        label-width=\"80px\"\r\n        class=\"demo-ruleForm\"\r\n      >\r\n        <el-form-item label=\"选择班组\" prop=\"TeamGroup\">\r\n          <el-select\r\n            v-model=\"form.TeamGroup\"\r\n            class=\"w100\"\r\n            placeholder=\"请选择\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"item in workingTeam\"\r\n              :key=\"item.Working_Team_Id\"\r\n              :label=\"item.Working_Team_Name\"\r\n              :value=\"item.Working_Team_Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item style=\"text-align: right\">\r\n          <el-button @click=\"resetForm('form')\">取 消</el-button>\r\n          <el-button\r\n            type=\"primary\"\r\n            @click=\"submitForm('form');resetForm('form')\"\r\n          >确 定</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-dialog>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      title=\"提示\"\r\n      class=\"plm-custom-dialog\"\r\n      :visible.sync=\"dialogTipsVisible\"\r\n      width=\"450px\"\r\n      @close=\"handleDialog\"\r\n    >\r\n      <div style=\"text-align: center; font-size: 16px;\">部分{{ isCom ? '构件' : isUnitPart ? '部件' : '零件' }}与上道工序加工班组不同，请手动分配</div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogTipsVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"dialogTipsVisible = false\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { FIX_COLUMN } from '@/views/PRO/plan-production/schedule-production/constant'\r\nimport {\r\n  AdjustTeamProcessAllocation,\r\n  GetTeamProcessAllocation,\r\n  AdjustPartTeamProcessAllocation,\r\n  AdjustSubAssemblyTeamProcessAllocation,\r\n  GetStopList,\r\n  GetPreStepTaskAllocation\r\n} from '@/api/PRO/production-task'\r\nimport QrcodeVue from 'qrcode.vue'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport numeral from 'numeral'\r\nimport { closeTagView, deepClone } from '@/utils'\r\nimport { GetCompTypeTree } from '@/api/PRO/professionalType'\r\n\r\nconst SPLIT_SYMBOL = '$_$'\r\nexport default {\r\n  components: {\r\n    QrcodeVue\r\n  },\r\n  filters: {\r\n    filterNum(value) {\r\n      return numeral(value).divide(1000).format('0.[00]')\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        clearable: true\r\n      },\r\n      ObjectTypeList: {\r\n        // 构件类型\r\n        'check-strictly': true,\r\n        'default-expand-all': true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      },\r\n      tbLoading: false,\r\n      loading: false,\r\n      activeName: 'first',\r\n      tipLabel: '',\r\n      tbData: [],\r\n      filterTbData: [],\r\n      multipleSelection: [],\r\n      columns: [],\r\n      workingTeam: [],\r\n      workingTeamColumn: [],\r\n      formInline: {},\r\n      pg_type: '',\r\n      searchType: '',\r\n      type: '',\r\n      queryForm: {\r\n        Comp_Codes: '',\r\n        Part_Code: '',\r\n        Spec: '',\r\n        Comp_Codes_Vague: '',\r\n        Part_Code_Vague: ''\r\n      },\r\n      dialogVisible: false,\r\n      dialogTipsVisible: false,\r\n      form: {\r\n        TeamGroup: '' // 班组\r\n      },\r\n\r\n      rules: {\r\n        TeamGroup: [\r\n          { required: true, message: '请输入班组名称', trigger: 'change' }\r\n        ]\r\n      },\r\n      Is_Workshop_Enabled: false,\r\n      Working_Process_Id: ''\r\n    }\r\n  },\r\n  computed: {\r\n    isView() {\r\n      return this.type === 'view'\r\n    },\r\n    isCom() {\r\n      return this.pg_type === 'com'\r\n    },\r\n    isUnitPart() {\r\n      return this.pg_type === 'unitPart'\r\n    }\r\n  },\r\n\r\n  async mounted() {\r\n    try {\r\n      const rowInfo = JSON.parse(decodeURIComponent(this.$route.query.other))\r\n      console.log('rowInfo', rowInfo)\r\n      this.formInline = Object.assign({}, this.formInline, rowInfo)\r\n      this.pg_type = this.$route.query.pg_type\r\n      this.bomLevel = this.$route.query.bomLevel\r\n      this.type = this.$route.query.type\r\n      this.Is_Workshop_Enabled = this.$route.query.Is_Workshop_Enabled\r\n      await this.getTableConfig(this.isUnitPart ? 'PROTaskUnitAllocationChange' : 'PROTaskAllocationChange')\r\n      if (this.isCom) {\r\n        const idx = this.columns.findIndex(item => item.Code === 'Part_Code')\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n        const idx2 = this.columns.findIndex(item => item.Code === 'Component_Code')\r\n        idx2 !== -1 && this.columns.splice(idx2, 1)\r\n      } else {\r\n        const idx = this.columns.findIndex(item => item.Code === 'Comp_Code')\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n        const idx2 = this.columns.findIndex(item => item.Code === 'Type')\r\n        idx2 !== -1 && this.columns.splice(idx2, 1)\r\n      }\r\n      if (!this.Is_Workshop_Enabled) {\r\n        const idx3 = this.columns.findIndex(item => item.Code === 'Workshop_Name')\r\n        idx3 !== -1 && this.columns.splice(idx3, 1)\r\n      }\r\n      this.getObjectTypeList()\r\n      this.fetchData()\r\n    } catch (e) {\r\n      this.$message({\r\n        message: '参数错误,请重新操作',\r\n        type: 'error'\r\n      })\r\n    }\r\n  },\r\n  methods: {\r\n    getRowCCode(row, prefix = '', suffix = '') {\r\n      if (this.activeName === 'first') {\r\n        return 'allocatedTask' + suffix\r\n      } else {\r\n        const arr = this.activeName.split(SPLIT_SYMBOL)\r\n        const team = this.workingTeam.find(v => v.Working_Team_Name === arr[0])\r\n        const u = this.getRowUnique(row.uuid, row.Process_Code, team.Working_Team_Id)\r\n        if (suffix === 'Max') {\r\n          return u\r\n        } else {\r\n          return prefix + u + suffix\r\n        }\r\n      }\r\n    },\r\n    handleClick(val) {\r\n      console.log('handleClick', val)\r\n      if (val.name === 'first') {\r\n        const c1 = this.$refs.xTable.getColumnByField('Schduled_Count')\r\n        const c2 = this.$refs.xTable.getColumnByField('Can_Allocation_Count')\r\n        c1.visible = false\r\n        c2.visible = true\r\n      } else {\r\n        const c2 = this.$refs.xTable.getColumnByField('Schduled_Count')\r\n        const c3 = this.$refs.xTable.getColumnByField('Can_Allocation_Count')\r\n        c2.visible = true\r\n        c3.visible = false\r\n      }\r\n\r\n      this.workingTeam.forEach((element, idx) => {\r\n        const codes = `${element.Working_Team_Name}$_$${element.Process_Code}`\r\n\r\n        const c = this.$refs.xTable.getColumnByField(codes)\r\n\r\n        c.visible = codes === val.name\r\n      })\r\n      this.$refs.xTable.refreshColumn()\r\n\r\n      this.filterTbData = this.tbData.filter(row => {\r\n        return this.filterZero(row)\r\n      })\r\n\r\n      this.multipleSelection = []\r\n      this.$refs.xTable.clearCheckboxRow()\r\n    },\r\n    filterZero(row) {\r\n      if (this.activeName === 'first') {\r\n        return row.allocatedTask > 0\r\n      } else {\r\n        const arr = this.activeName.split(SPLIT_SYMBOL)\r\n        const team = this.workingTeam.find(v => v.Working_Team_Name === arr[0])\r\n        const code = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\r\n        return row[code] > 0\r\n      }\r\n    },\r\n    fetchData() {\r\n      const Comp_Codes = !this.queryForm.Comp_Codes ? [] : this.queryForm.Comp_Codes.trim().split(' ')\r\n      const Part_Code = !this.queryForm.Part_Code ? [] : this.queryForm.Part_Code.trim().split(' ')\r\n      let Process_Type = 2\r\n      Process_Type = this.isCom ? 2 : this.isUnitPart ? 3 : 1\r\n      this.tbLoading = true\r\n      GetTeamProcessAllocation({\r\n        Page: 1,\r\n        PageSize: -1,\r\n        Step: this.formInline.Step,\r\n        Process_Type,\r\n        Bom_Level: this.bomLevel,\r\n        Schduling_Code: this.formInline.Schduling_Code,\r\n        Process_Code: this.formInline.Process_Code,\r\n        Workshop_Name: this.formInline.Workshop_Name,\r\n        Area_Id: this.formInline.Area_Id,\r\n        InstallUnit_Id: this.formInline.InstallUnit_Id,\r\n        Comp_Codes,\r\n        Part_Code\r\n      }).then(async(res) => {\r\n        if (res.IsSucceed) {\r\n          const { Schduling_Plan, Schduling_Comps } = res.Data\r\n          this.planInfoTemp = Schduling_Plan\r\n          await this.getStopList(Schduling_Comps)\r\n          this.initTbData(Schduling_Comps)\r\n          this.Working_Process_Id = res.Data.Working_Process_Id\r\n\r\n          if (Schduling_Comps.length) {\r\n            this.workingTeam = Schduling_Comps[0].Allocation_Teams\r\n            const _kk = this.workingTeam.map(v => {\r\n              v.visible = false\r\n              return v\r\n            })\r\n            this.workingTeamColumn = deepClone(_kk)\r\n          }\r\n          console.log(' this.tbData', this.tbData)\r\n          this.filterData()\r\n          console.log('filterTbData', this.filterTbData)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    checkMethod({ row }) {\r\n      return !row.stopFlag\r\n    },\r\n    async getStopList(list) {\r\n      const key = 'Id'\r\n      const submitObj = list.map(item => {\r\n        return {\r\n          Id: item[key],\r\n          Bom_Level: this.bomLevel,\r\n          Type: this.isCom ? 2 : this.isUnitPart ? 3 : 1 // 1：零件，3：部件，2：构件\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = !!item.Is_Stop\r\n          })\r\n          list.forEach(row => {\r\n            if (stopMap[row[key]]) {\r\n              this.$set(row, 'stopFlag', stopMap[row[key]])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    filterData() {\r\n      console.log('searchType', this.searchType)\r\n      this.multipleSelection = []\r\n      this.$refs.xTable.clearCheckboxRow()\r\n      const code = this.isCom ? 'Comp_Code' : 'Part_Code'\r\n      const queryCode = this.isCom ? 'Comp_Codes' : 'Part_Code'\r\n      const codeList = this.queryForm[queryCode].split(' ').filter(v => !!v)\r\n\r\n      const queryCodeVague = this.isCom ? 'Comp_Codes_Vague' : 'Part_Code_Vague'\r\n      const codeListVague = this.queryForm[queryCodeVague]\r\n\r\n      const searchTbData = this.tbData.filter(v => {\r\n        if (!codeList.length && codeListVague === '') {\r\n          return true\r\n        } else {\r\n          return codeList.includes(v[code])\r\n        }\r\n      })\r\n\r\n      const vagueData = searchTbData.length > 0 && codeListVague === '' ? searchTbData : searchTbData.length === 0 && codeListVague === '' ? searchTbData : this.tbData\r\n      const searchVagueTbData = vagueData.filter(v => {\r\n        if (codeListVague === '' && !codeList.length) {\r\n          return true\r\n        } else {\r\n          return v[code].includes(codeListVague)\r\n        }\r\n      })\r\n\r\n      // 合并两个数组\r\n      const mergedArray = searchTbData.concat(searchVagueTbData)\r\n      // 根据 Schduling_Detail_Id 进行去重\r\n      const uniqueArray = mergedArray.reduce((acc, current) => {\r\n        const existingObject = acc.find(item => item.Schduling_Detail_Id === current.Schduling_Detail_Id)\r\n        if (!existingObject) {\r\n          acc.push(current)\r\n        }\r\n        return acc\r\n      }, [])\r\n\r\n      this.filterTbData = uniqueArray.filter(v => {\r\n        if (!this.searchType) return true\r\n        return this.searchType === v.Type\r\n      }).filter(v => {\r\n        if (!this.queryForm.Spec) return true\r\n        return (v.Spec || '').includes(this.queryForm.Spec)\r\n      }).filter(row => {\r\n        return this.filterZero(row)\r\n      })\r\n    },\r\n    initTbData(list, teamKey = 'Allocation_Teams') {\r\n      this.tbData = list.map(row => {\r\n        const processList = row.Technology_Path?.split('/') || []\r\n        // 已uuid作为row唯一值；\r\n        // uuid+工序+班组为输入框值\r\n        row.uuid = uuidv4()\r\n        const newData = row[teamKey].filter((r) => processList.findIndex((p) => r.Process_Code === p) !== -1)\r\n        row.defaultCan_Allocation_Count = row.Can_Allocation_Count\r\n        let _inputNum = 0\r\n        newData.forEach((ele, index) => {\r\n          const code = this.getRowUnique(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n          const max = this.getRowUniqueMax(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n          row[code] = ele.Count\r\n          this.$set(row, 'alCount' + code, ele.Count)\r\n          row[max] = 0\r\n          _inputNum += ele.Count\r\n          this.$set(row, 'totalTask' + ele.Working_Team_Name, ele.Total_Receive_Count + ele.Count)\r\n        })\r\n\r\n        this.$set(row, 'allocatedTask', row.defaultCan_Allocation_Count - _inputNum)\r\n        row.Can_Allocation_Count = row.allocatedTask\r\n        this.$set(row, 'allocatedTaskMax', row.Can_Allocation_Count)\r\n\r\n        this.setInputMax(row)\r\n\r\n        row.checked = false\r\n        return row\r\n      })\r\n    },\r\n    inputChange(row) {\r\n      this.setInputMax(row)\r\n    },\r\n    setInputMax(row) {\r\n      let _inputNum = 0\r\n      const inputValuesKeys = Object.keys(row)\r\n        .filter(v => !v.endsWith('max') && v.startsWith(row.uuid) && v.length > row.uuid.length)\r\n      inputValuesKeys.forEach((val) => {\r\n        const curCode = val.split(SPLIT_SYMBOL)[1]\r\n        const otherTotal = inputValuesKeys.filter(x => {\r\n          const code = x.split(SPLIT_SYMBOL)[1]\r\n          return x !== val && code === curCode\r\n        }).reduce((acc, item) => {\r\n          return acc + numeral(row[item]).value()\r\n        }, 0)\r\n        row[val + SPLIT_SYMBOL + 'max'] = row.Schduled_Count - otherTotal\r\n        _inputNum += +row[val]\r\n      })\r\n      // row.allocatedCount = row.defaultCan_Allocation_Count - _inputNum\r\n      // row.Can_Allocation_Count = row.allocatedCount\r\n    },\r\n    checkPermissionTeam(processStr, processCode) {\r\n      if (!processStr) return false\r\n      const list = processStr?.split('/') || []\r\n      return !!list.some(v => v === processCode)\r\n    },\r\n\r\n    getSubmitTbInfo(tbData = this.tbData) {\r\n      // 处理上传的数据\r\n      const tableData = JSON.parse(JSON.stringify(tbData))\r\n      for (let i = 0; i < tableData.length; i++) {\r\n        const element = tableData[i]\r\n        const list = []\r\n        if (!element.Technology_Path) {\r\n          this.$message({\r\n            message: '工序不能为空',\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        const processList = Array.from(new Set(element.Technology_Path.split('/')))\r\n        processList.forEach(code => {\r\n          const groups = this.workingTeam.filter(v => v.Process_Code === code)\r\n\r\n          const groupsList = groups.map((group, index) => {\r\n            const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)\r\n            const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)\r\n            const obj = {\r\n              Comp_Code: element.Comp_Code,\r\n              Again_Count: +element[uCode],\r\n              Part_Code: this.isCom ? null : element.Part_Code,\r\n              Process_Code: code,\r\n              Technology_Path: element.Technology_Path,\r\n              Working_Team_Id: group.Working_Team_Id,\r\n              Working_Team_Name: group.Working_Team_Name,\r\n              Team_Task_Id: element.Allocation_Teams.find((item) => item.Working_Team_Id === group.Working_Team_Id).Team_Task_Id\r\n            }\r\n            delete element['alCount' + uCode]\r\n            delete element['allocatedTask']\r\n            delete element['allocatedTaskMax']\r\n            delete element[uCode]\r\n            delete element[uMax]\r\n            return obj\r\n          })\r\n          list.push(...groupsList)\r\n        })\r\n        console.log(list)\r\n        delete element['uuid']\r\n        delete element['puuid']\r\n        element.Allocation_Teams = list\r\n      }\r\n      return { tableData, status: true }\r\n    },\r\n    handleSubmit() {\r\n      const { tableData, status } = this.getSubmitTbInfo()\r\n      if (!status) return\r\n      this.$confirm('是否提交当前数据?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.loading = true\r\n        const obj = {\r\n          Schduling_Plan: this.planInfoTemp,\r\n          Schduling_Comps: tableData\r\n        }\r\n        const Partobj = {\r\n          Schduling_Plan: this.planInfoTemp,\r\n          SarePartsModel: tableData\r\n        }\r\n        const requestFn = this.isCom ? AdjustTeamProcessAllocation : this.isUnitPart ? AdjustSubAssemblyTeamProcessAllocation : AdjustPartTeamProcessAllocation\r\n        console.log('obj', obj)\r\n        requestFn(this.isCom ? obj : Partobj).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '操作成功',\r\n              type: 'success'\r\n            })\r\n            this.handleClose()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.loading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.closeView()\r\n    },\r\n    closeView() {\r\n      closeTagView(this.$store, this.$route)\r\n    },\r\n    tbSelectChange(array) {\r\n      this.multipleSelection = array.records\r\n      console.log(this.tbData)\r\n    },\r\n    getTaskCode(name = '') {\r\n      if (name) return 'totalTask' + name\r\n      return 'totalTask' + this.activeName.split(SPLIT_SYMBOL)[0]\r\n    },\r\n    // 反选\r\n    reverseSelection() {\r\n      const list = this.$refs.xTable.getCheckboxRecords().filter(item => !item.stopFlag)\r\n      const unSelectList = this.filterTbData.filter(item => !list.includes(item) && !item.stopFlag)\r\n      this.$refs.xTable.setCheckboxRow(list, false)\r\n      this.$refs.xTable.setCheckboxRow(unSelectList, true)\r\n      this.multipleSelection = this.$refs.xTable.getCheckboxRecords()\r\n    },\r\n    async getTableConfig(code) {\r\n      await GetGridByCode({\r\n        code\r\n      }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n          const list = Data.ColumnList || []\r\n          this.columns = list.filter(v => v.Is_Display).map(item => {\r\n            if (FIX_COLUMN.includes(item.Code)) {\r\n              item.fixed = 'left'\r\n            }\r\n            if (item.Code === 'Schduled_Count') {\r\n              item.visible = false\r\n            }\r\n            return item\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    activeCellMethod({ row, column, columnIndex }) {\r\n      if (this.isView) return false\r\n      if (column.field === 'AllocatedCount') return true\r\n      const processCode = column.field?.split('$_$')[1]\r\n      return this.checkPermissionTeam(row.Technology_Path, processCode)\r\n    },\r\n    getRowUnique(uuid, processCode, workingId) {\r\n      return `${uuid}${SPLIT_SYMBOL}${processCode}${SPLIT_SYMBOL}${workingId}`\r\n    },\r\n    getRowUniqueMax(uuid, processCode, workingId) {\r\n      return this.getRowUnique(uuid, processCode, workingId) + `${SPLIT_SYMBOL}max`\r\n    },\r\n    // 批量分配\r\n    Batchallocation() {\r\n      this.dialogVisible = true\r\n      console.log(this.workingTeam)\r\n    },\r\n    // 上道工序分配\r\n    preStepTaskAllocation() {\r\n      const Schduling_Detail_Ids = this.multipleSelection.map(item => item.Schduling_Detail_Id)\r\n      const Working_Process_Code = this.multipleSelection[0].Process_Code\r\n      GetPreStepTaskAllocation({\r\n        Schduling_Detail_Ids,\r\n        Working_Process_Code,\r\n        Working_Process_Id: this.Working_Process_Id,\r\n        Process_Type: this.isCom ? 2 : this.isUnitPart ? 3 : 1,\r\n        Bom_Level: this.bomLevel\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (res.Data.length === 0) {\r\n            this.dialogTipsVisible = true\r\n            return\r\n          }\r\n          this.preDoAllocation(res.Data)\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleDialog() {\r\n      this.dialogVisible = false\r\n      // this.multipleSelection = []\r\n    },\r\n    handelData() {\r\n      this.multipleSelection.forEach((item) =>\r\n        item.Allocation_Teams.forEach((v) => {\r\n          if (v.Working_Team_Id === this.form.TeamGroup) {\r\n            v.Count = item.Can_Allocation_Count\r\n          } else {\r\n            v.Count = 0\r\n          }\r\n        })\r\n      )\r\n      // const tableData = JSON.parse(JSON.stringify(this.multipleSelection))\r\n      for (let i = 0; i < this.multipleSelection.length; i++) {\r\n        const element = this.multipleSelection[i]\r\n        const processList = Array.from(new Set(element.Technology_Path.split('/')))\r\n        processList.forEach(code => {\r\n          const groups = this.workingTeam.filter(v => v.Process_Code === code)\r\n          groups.forEach(group => {\r\n            const uniCode = this.getRowUnique(element.uuid, code, this.form.TeamGroup)\r\n            const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)\r\n            const uniMax = this.getRowUniqueMax(element.uuid, code, this.form.TeamGroup)\r\n            const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)\r\n\r\n            if (uniCode === uCode && uniMax === uMax) {\r\n              element[uCode] = element['Can_Allocation_Count']\r\n              element[uMax] = element['Can_Allocation_Count']\r\n            } else {\r\n              element[uCode] = 0\r\n              element[uMax] = 0\r\n            }\r\n          })\r\n        })\r\n      }\r\n      console.log(this.multipleSelection)\r\n      for (let i = 0; i < this.tbData.length; i++) {\r\n        for (let k = 0; k < this.multipleSelection.length; k++) {\r\n          if (this.tbData[i].uuid === this.multipleSelection[k].uuid) {\r\n            this.$nextTick((_) => {\r\n              this.tbData[i] = this.multipleSelection[k]\r\n            })\r\n          }\r\n        }\r\n      }\r\n    },\r\n    submitForm(formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          // this.handelData()\r\n          this.doAllocation()\r\n          // this.workingTeam.find(v=> v.Working_Team_Id === this.form.TeamGroup).count\r\n          this.handleDialog()\r\n          console.log(this.tbData)\r\n          this.multipleSelection = []\r\n          this.$refs.xTable.clearCheckboxRow()\r\n        } else {\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    // 上道工序分配\r\n    preDoAllocation(preProcessData) {\r\n      const preProcessDataMap = new Map()\r\n      preProcessData.forEach(item => {\r\n        const key = `${item.Schduling_Detail_Id}_${item.Working_Team_Id}`\r\n        preProcessDataMap.set(key, item.Current_Task_Count)\r\n      })\r\n\r\n      const allocateForTeam = (row, team, amount) => {\r\n        const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\r\n        const bKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\r\n        const currentTaskCount = preProcessDataMap.get(bKey) || 0\r\n        const allocated = Math.min(amount, currentTaskCount, row.Can_Allocation_Count)\r\n\r\n        row[tarCode] = (Number(row[tarCode]) || 0) + allocated\r\n        row.Can_Allocation_Count -= allocated\r\n        row[this.getTaskCode(team.Working_Team_Name)] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + allocated\r\n        row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - allocated\r\n        row['alCount' + tarCode] = row[tarCode]\r\n\r\n        return allocated\r\n      }\r\n      let isMessage = true\r\n      this.multipleSelection.forEach(row => {\r\n        if (!row.Can_Allocation_Count && this.activeName === 'first') return\r\n\r\n        const eligibleTeams = row.Allocation_Teams.filter(team =>\r\n          preProcessDataMap.has(`${row.Schduling_Detail_Id}_${team.Working_Team_Id}`)\r\n        )\r\n\r\n        if (eligibleTeams.length === 0) return\r\n        if (this.activeName === 'first') {\r\n          let IsAllNo = 0\r\n          const totalAvailable = eligibleTeams.reduce((sum, team) => {\r\n            const bKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\r\n            return sum + (preProcessDataMap.get(bKey) || 0)\r\n          }, 0)\r\n\r\n          if (row.allocatedTaskMax < totalAvailable) {\r\n            IsAllNo++\r\n            if (IsAllNo === this.multipleSelection.length) {\r\n              isMessage = false\r\n              this.dialogTipsVisible = true\r\n            }\r\n            return\r\n          }\r\n\r\n          let remaining = Math.min(row.allocatedTask, row.Can_Allocation_Count)\r\n          const perTeamAllocation = Math.floor(remaining / eligibleTeams.length)\r\n\r\n          eligibleTeams.forEach((team, index) => {\r\n            if (remaining <= 0) return\r\n\r\n            const allocateAmount = index === eligibleTeams.length - 1\r\n              ? remaining\r\n              : Math.min(perTeamAllocation, remaining)\r\n\r\n            remaining -= allocateForTeam(row, team, allocateAmount)\r\n          })\r\n\r\n          row.allocatedTaskMax = row.Can_Allocation_Count\r\n          row.allocatedTask = row.Can_Allocation_Count\r\n          if (IsAllNo === this.multipleSelection.length) {\r\n            isMessage = false\r\n            this.dialogTipsVisible = true\r\n            return\r\n          }\r\n        } else {\r\n          eligibleTeams.forEach(team => {\r\n            const bKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\r\n            const currentTaskCount = preProcessDataMap.get(bKey) || 0\r\n\r\n            if (row[this.getRowCCode(row)] < currentTaskCount) {\r\n              return\r\n            }\r\n\r\n            const selectNum = Math.min(\r\n              row[this.getRowCCode(row, 'alCount')] || 0,\r\n              row[this.getRowCCode(row)] || 0,\r\n              currentTaskCount\r\n            )\r\n\r\n            if (selectNum > 0) {\r\n              const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\r\n              row[this.getTaskCode(team.Working_Team_Name)] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + selectNum\r\n              row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - selectNum\r\n              row[tarCode] = (Number(row[tarCode]) || 0) + selectNum\r\n              row[this.getRowCCode(row)] = (row[this.getRowCCode(row)] || 0) - selectNum\r\n              row[this.getRowCCode(row, 'alCount')] = (row[this.getRowCCode(row, 'alCount')] || 0) - selectNum\r\n              row['alCount' + tarCode] = row[tarCode]\r\n            }\r\n          })\r\n        }\r\n      })\r\n      // if (this.activeName === 'first') {\r\n      //   let IsAllNo = 0\r\n      //   this.multipleSelection.forEach((row, idx) => {\r\n      //     if (row.Can_Allocation_Count) {\r\n      //       const validTeams = row.Allocation_Teams.filter(team => {\r\n      //         const key = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\r\n      //         return preProcessDataMap.has(key)\r\n      //       })\r\n      //       if (validTeams.length > 0) {\r\n      //         const team = validTeams[0]\r\n      //         const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\r\n\r\n      //         const preProcessKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\r\n      //         const currentTaskCount = preProcessDataMap.get(preProcessKey) || 0\r\n\r\n      //         if (currentTaskCount > row.allocatedTaskMax) {\r\n      //           IsAllNo++\r\n      //           return\r\n      //         }\r\n      //         const allocated = Math.min(row.allocatedTask, currentTaskCount)\r\n\r\n      //         row[tarCode] = Number(row[tarCode] || 0) + allocated\r\n      //         row.Can_Allocation_Count = row.Can_Allocation_Count - allocated\r\n      //         row[this.getTaskCode(team.Working_Team_Name) || 0] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + allocated\r\n      //         row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - allocated\r\n      //         row.allocatedTaskMax = row.Can_Allocation_Count\r\n      //         row.allocatedTask = row.Can_Allocation_Count\r\n      //         row['alCount' + tarCode] = row[tarCode]\r\n      //       }\r\n      //     }\r\n      //   })\r\n      //   if (IsAllNo === this.multipleSelection.length) {\r\n      //     this.dialogTipsVisible = true\r\n      //     return\r\n      //   }\r\n      // } else {\r\n      //   this.multipleSelection.forEach((row, idx) => {\r\n      //     const validTeams = row.Allocation_Teams.filter(team => {\r\n      //       const key = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\r\n      //       return preProcessDataMap.has(key)\r\n      //     })\r\n      //     if (validTeams.length > 0) {\r\n      //       const team = validTeams[0]\r\n      //       const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\r\n\r\n      //       const preProcessKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\r\n      //       const currentTaskCount = preProcessDataMap.get(preProcessKey) || 0\r\n\r\n      //       const selectNum = Math.min(\r\n      //         row[this.getRowCCode(row, 'alCount')] || 0,\r\n      //         row[this.getRowCCode(row)] || 0,\r\n      //         currentTaskCount\r\n      //       )\r\n\r\n      //       row[this.getTaskCode(team.Working_Team_Name)] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + selectNum\r\n      //       row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - selectNum\r\n      //       row[tarCode] = (Number(row[tarCode]) || 0) + selectNum\r\n      //       row[this.getRowCCode(row)] = (row[this.getRowCCode(row)] || 0) - selectNum\r\n      //       row[this.getRowCCode(row, 'alCount')] = (row[this.getRowCCode(row, 'alCount')] || 0) - selectNum\r\n      //       row['alCount' + tarCode] = row[tarCode]\r\n      //     }\r\n      //   })\r\n      // }\r\n      this.filterTbData = this.tbData.filter(row => {\r\n        return this.filterZero(row)\r\n      })\r\n      this.multipleSelection = []\r\n      this.$refs.xTable.clearCheckboxRow()\r\n      if (isMessage) {\r\n        this.$message({\r\n          message: '同步成功',\r\n          type: 'success'\r\n        })\r\n      }\r\n    },\r\n    // 批量分配提交\r\n    doAllocation() {\r\n      if (this.activeName === 'first') {\r\n        this.multipleSelection\r\n          .forEach((row, idx) => {\r\n            if (row.Can_Allocation_Count) {\r\n              const team = row.Allocation_Teams.find(v => v.Working_Team_Id === this.form.TeamGroup)\r\n              const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\r\n              row[tarCode] = Number(row[tarCode]) + row.allocatedTask\r\n              row.Can_Allocation_Count = row.Can_Allocation_Count - row.allocatedTask\r\n              row[this.getTaskCode(team.Working_Team_Name)] += row.allocatedTask\r\n              row[this.getTaskCode()] -= row.allocatedTask\r\n              row.allocatedTaskMax = row.Can_Allocation_Count\r\n              row.allocatedTask = row.Can_Allocation_Count\r\n              row['alCount' + tarCode] = row[tarCode]\r\n            }\r\n          })\r\n      } else {\r\n        this.multipleSelection\r\n          .forEach((row, idx) => {\r\n            const team = row.Allocation_Teams.find(v => v.Working_Team_Id === this.form.TeamGroup)\r\n            const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\r\n\r\n            const selectNum = Math.min(row[this.getRowCCode(row, 'alCount')], row[this.getRowCCode(row)])\r\n\r\n            row[this.getTaskCode(team.Working_Team_Name)] += selectNum\r\n            row[this.getTaskCode()] -= selectNum\r\n\r\n            row[tarCode] = Number(row[tarCode]) + selectNum\r\n            row[this.getRowCCode(row)] -= selectNum\r\n            row[this.getRowCCode(row, 'alCount')] -= selectNum\r\n\r\n            row['alCount' + tarCode] = row[tarCode]\r\n          })\r\n      }\r\n      this.filterTbData = this.tbData.filter(row => {\r\n        return this.filterZero(row)\r\n      })\r\n    },\r\n    resetForm(formName) {\r\n      this.$refs[formName].resetFields()\r\n      this.dialogVisible = false\r\n    },\r\n    filterTypeMethod({ option, row }) {\r\n      return row.Type.includes(option.data)\r\n    },\r\n    filterTypeRecoverMethod({ option }) {\r\n      option.data = ''\r\n    },\r\n    filterCodeMethod({ option, row }) {\r\n      console.log('option, row', option, row)\r\n      return row[this.isCom ? 'Comp_Code' : 'Part_Code'].includes(option.data)\r\n    },\r\n    filterCodeRecoverMethod({ option }) {\r\n      option.data = ''\r\n    },\r\n    getObjectTypeList() {\r\n      GetCompTypeTree({ professional: 'Steel' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.ObjectTypeList.data = res.Data\r\n          this.$nextTick((_) => {\r\n            this.$refs?.treeSelectObjectType?.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.pagination-container {\r\n  padding: 0;\r\n  text-align: right;\r\n}\r\n\r\n::v-deep .el-card__body {\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .el-divider {\r\n    margin-top: 0;\r\n    margin-bottom: 16px;\r\n    background-color: #EEEEEE;\r\n  }\r\n\r\n  .tb-options {\r\n    margin-bottom: 16px;\r\n    display: flex;\r\n\r\n    .el-form-item--small.el-form-item {\r\n      margin-bottom: 0;\r\n    }\r\n    .el-input {\r\n      width: 250px;\r\n    }\r\n  }\r\n\r\n  footer {\r\n    text-align: inherit;\r\n    display: flex;\r\n    justify-content: space-between;\r\n  }\r\n}\r\n\r\n.tb-x {\r\n  flex: 1;\r\n  height: 0;\r\n  margin-bottom: 10px;\r\n  overflow: auto;\r\n}\r\n\r\n.topTitle {\r\n  height: 14px;\r\n  line-height: 14px;\r\n  font-size: 14px;\r\n  margin: 0 0 16px;\r\n\r\n  span {\r\n    display: inline-block;\r\n    width: 2px;\r\n    height: 14px;\r\n    background: #009dff;\r\n    vertical-align: middle;\r\n    margin-right: 6px;\r\n  }\r\n}\r\n\r\n.el-icon-edit {\r\n  cursor: pointer;\r\n}\r\n\r\n.cs-bottom {\r\n  position: relative;\r\n  height: 40px;\r\n  line-height: 40px;\r\n\r\n  .data-info {\r\n    position: absolute;\r\n    bottom: 0;\r\n\r\n    .info-x {\r\n      margin-right: 20px;\r\n    }\r\n  }\r\n}\r\n.my-input {\r\n  margin: 10px;\r\n  width: 140px;\r\n  height: 32px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiYA,SAAAA,aAAA;AACA,SAAAC,UAAA;AACA,SACAC,2BAAA,EACAC,wBAAA,EACAC,+BAAA,EACAC,sCAAA,EACAC,WAAA,EACAC,wBAAA,QACA;AACA,OAAAC,SAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AACA,OAAAC,OAAA;AACA,SAAAC,YAAA,EAAAC,SAAA;AACA,SAAAC,eAAA;AAEA,IAAAC,YAAA;AACA;EACAC,UAAA;IACAR,SAAA,EAAAA;EACA;EACAS,OAAA;IACAC,SAAA,WAAAA,UAAAC,KAAA;MACA,OAAAR,OAAA,CAAAQ,KAAA,EAAAC,MAAA,OAAAC,MAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,gBAAA;QACAC,WAAA;QACAC,SAAA;MACA;MACAC,cAAA;QACA;QACA;QACA;QACAC,WAAA;QACAL,IAAA;QACAM,KAAA;UACAC,QAAA;UACAC,KAAA;UACAX,KAAA;QACA;MACA;MACAY,SAAA;MACAC,OAAA;MACAC,UAAA;MACAC,QAAA;MACAC,MAAA;MACAC,YAAA;MACAC,iBAAA;MACAC,OAAA;MACAC,WAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,OAAA;MACAC,UAAA;MACAC,IAAA;MACAC,SAAA;QACAC,UAAA;QACAC,SAAA;QACAC,IAAA;QACAC,gBAAA;QACAC,eAAA;MACA;MACAC,aAAA;MACAC,iBAAA;MACAC,IAAA;QACAC,SAAA;MACA;MAEAC,KAAA;QACAD,SAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,mBAAA;MACAC,kBAAA;IACA;EACA;EACAC,QAAA;IACAC,MAAA,WAAAA,OAAA;MACA,YAAAlB,IAAA;IACA;IACAmB,KAAA,WAAAA,MAAA;MACA,YAAArB,OAAA;IACA;IACAsB,UAAA,WAAAA,WAAA;MACA,YAAAtB,OAAA;IACA;EACA;EAEAuB,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAAC,OAAA,EAAAC,GAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,KAAA,EAAAC,IAAA;MAAA,OAAAR,mBAAA,GAAAS,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAC,IAAA;YAEAT,OAAA,GAAAW,IAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAAlB,KAAA,CAAAmB,MAAA,CAAAC,KAAA,CAAAC,KAAA;YACAC,OAAA,CAAAC,GAAA,YAAAlB,OAAA;YACAL,KAAA,CAAAzB,UAAA,GAAAiD,MAAA,CAAAC,MAAA,KAAAzB,KAAA,CAAAzB,UAAA,EAAA8B,OAAA;YACAL,KAAA,CAAAxB,OAAA,GAAAwB,KAAA,CAAAmB,MAAA,CAAAC,KAAA,CAAA5C,OAAA;YACAwB,KAAA,CAAA0B,QAAA,GAAA1B,KAAA,CAAAmB,MAAA,CAAAC,KAAA,CAAAM,QAAA;YACA1B,KAAA,CAAAtB,IAAA,GAAAsB,KAAA,CAAAmB,MAAA,CAAAC,KAAA,CAAA1C,IAAA;YACAsB,KAAA,CAAAP,mBAAA,GAAAO,KAAA,CAAAmB,MAAA,CAAAC,KAAA,CAAA3B,mBAAA;YAAAoB,QAAA,CAAAE,IAAA;YAAA,OACAf,KAAA,CAAA2B,cAAA,CAAA3B,KAAA,CAAAF,UAAA;UAAA;YACA,IAAAE,KAAA,CAAAH,KAAA;cACAS,GAAA,GAAAN,KAAA,CAAA5B,OAAA,CAAAwD,SAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAAC,IAAA;cAAA;cACAxB,GAAA,WAAAN,KAAA,CAAA5B,OAAA,CAAA2D,MAAA,CAAAzB,GAAA;cACAC,IAAA,GAAAP,KAAA,CAAA5B,OAAA,CAAAwD,SAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAAC,IAAA;cAAA;cACAvB,IAAA,WAAAP,KAAA,CAAA5B,OAAA,CAAA2D,MAAA,CAAAxB,IAAA;YACA;cACAD,IAAA,GAAAN,KAAA,CAAA5B,OAAA,CAAAwD,SAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAAC,IAAA;cAAA;cACAxB,IAAA,WAAAN,KAAA,CAAA5B,OAAA,CAAA2D,MAAA,CAAAzB,IAAA;cACAC,KAAA,GAAAP,KAAA,CAAA5B,OAAA,CAAAwD,SAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAAC,IAAA;cAAA;cACAvB,KAAA,WAAAP,KAAA,CAAA5B,OAAA,CAAA2D,MAAA,CAAAxB,KAAA;YACA;YACA,KAAAP,KAAA,CAAAP,mBAAA;cACAiB,IAAA,GAAAV,KAAA,CAAA5B,OAAA,CAAAwD,SAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAAC,IAAA;cAAA;cACApB,IAAA,WAAAV,KAAA,CAAA5B,OAAA,CAAA2D,MAAA,CAAArB,IAAA;YACA;YACAV,KAAA,CAAAgC,iBAAA;YACAhC,KAAA,CAAAiC,SAAA;YAAApB,QAAA,CAAAE,IAAA;YAAA;UAAA;YAAAF,QAAA,CAAAC,IAAA;YAAAD,QAAA,CAAAqB,EAAA,GAAArB,QAAA;YAEAb,KAAA,CAAAmC,QAAA;cACA5C,OAAA;cACAb,IAAA;YACA;UAAA;UAAA;YAAA,OAAAmC,QAAA,CAAAuB,IAAA;QAAA;MAAA,GAAAhC,OAAA;IAAA;EAEA;EACAiC,OAAA;IACAC,WAAA,WAAAA,YAAAC,GAAA;MAAA,IAAAC,MAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MAAA,IAAAG,MAAA,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MACA,SAAA1E,UAAA;QACA,yBAAA6E,MAAA;MACA;QACA,IAAAC,GAAA,QAAA9E,UAAA,CAAA+E,KAAA,CAAAjG,YAAA;QACA,IAAAkG,IAAA,QAAA1E,WAAA,CAAA2E,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,iBAAA,KAAAL,GAAA;QAAA;QACA,IAAAM,CAAA,QAAAC,YAAA,CAAAb,GAAA,CAAAc,IAAA,EAAAd,GAAA,CAAAe,YAAA,EAAAP,IAAA,CAAAQ,eAAA;QACA,IAAAX,MAAA;UACA,OAAAO,CAAA;QACA;UACA,OAAAX,MAAA,GAAAW,CAAA,GAAAP,MAAA;QACA;MACA;IACA;IACAY,WAAA,WAAAA,YAAAC,GAAA;MAAA,IAAAC,MAAA;MACApC,OAAA,CAAAC,GAAA,gBAAAkC,GAAA;MACA,IAAAA,GAAA,CAAAE,IAAA;QACA,IAAAC,EAAA,QAAAC,KAAA,CAAAC,MAAA,CAAAC,gBAAA;QACA,IAAAC,EAAA,QAAAH,KAAA,CAAAC,MAAA,CAAAC,gBAAA;QACAH,EAAA,CAAAK,OAAA;QACAD,EAAA,CAAAC,OAAA;MACA;QACA,IAAAD,EAAA,QAAAH,KAAA,CAAAC,MAAA,CAAAC,gBAAA;QACA,IAAAG,EAAA,QAAAL,KAAA,CAAAC,MAAA,CAAAC,gBAAA;QACAC,EAAA,CAAAC,OAAA;QACAC,EAAA,CAAAD,OAAA;MACA;MAEA,KAAA5F,WAAA,CAAA8F,OAAA,WAAAC,OAAA,EAAA9D,GAAA;QACA,IAAA+D,KAAA,MAAAC,MAAA,CAAAF,OAAA,CAAAlB,iBAAA,SAAAoB,MAAA,CAAAF,OAAA,CAAAd,YAAA;QAEA,IAAAiB,CAAA,GAAAb,MAAA,CAAAG,KAAA,CAAAC,MAAA,CAAAC,gBAAA,CAAAM,KAAA;QAEAE,CAAA,CAAAN,OAAA,GAAAI,KAAA,KAAAZ,GAAA,CAAAE,IAAA;MACA;MACA,KAAAE,KAAA,CAAAC,MAAA,CAAAU,aAAA;MAEA,KAAAtG,YAAA,QAAAD,MAAA,CAAAwG,MAAA,WAAAlC,GAAA;QACA,OAAAmB,MAAA,CAAAgB,UAAA,CAAAnC,GAAA;MACA;MAEA,KAAApE,iBAAA;MACA,KAAA0F,KAAA,CAAAC,MAAA,CAAAa,gBAAA;IACA;IACAD,UAAA,WAAAA,WAAAnC,GAAA;MACA,SAAAxE,UAAA;QACA,OAAAwE,GAAA,CAAAqC,aAAA;MACA;QACA,IAAA/B,GAAA,QAAA9E,UAAA,CAAA+E,KAAA,CAAAjG,YAAA;QACA,IAAAkG,IAAA,QAAA1E,WAAA,CAAA2E,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,iBAAA,KAAAL,GAAA;QAAA;QACA,IAAAgC,IAAA,QAAAzB,YAAA,CAAAb,GAAA,CAAAc,IAAA,EAAAN,IAAA,CAAAO,YAAA,EAAAP,IAAA,CAAAQ,eAAA;QACA,OAAAhB,GAAA,CAAAsC,IAAA;MACA;IACA;IACA5C,SAAA,WAAAA,UAAA;MAAA,IAAA6C,MAAA;MACA,IAAAlG,UAAA,SAAAD,SAAA,CAAAC,UAAA,aAAAD,SAAA,CAAAC,UAAA,CAAAmG,IAAA,GAAAjC,KAAA;MACA,IAAAjE,SAAA,SAAAF,SAAA,CAAAE,SAAA,aAAAF,SAAA,CAAAE,SAAA,CAAAkG,IAAA,GAAAjC,KAAA;MACA,IAAAkC,YAAA;MACAA,YAAA,QAAAnF,KAAA,YAAAC,UAAA;MACA,KAAAjC,SAAA;MACA5B,wBAAA;QACAgJ,IAAA;QACAC,QAAA;QACAC,IAAA,OAAA5G,UAAA,CAAA4G,IAAA;QACAH,YAAA,EAAAA,YAAA;QACAI,SAAA,OAAA1D,QAAA;QACA2D,cAAA,OAAA9G,UAAA,CAAA8G,cAAA;QACA/B,YAAA,OAAA/E,UAAA,CAAA+E,YAAA;QACAgC,aAAA,OAAA/G,UAAA,CAAA+G,aAAA;QACAC,OAAA,OAAAhH,UAAA,CAAAgH,OAAA;QACAC,cAAA,OAAAjH,UAAA,CAAAiH,cAAA;QACA5G,UAAA,EAAAA,UAAA;QACAC,SAAA,EAAAA;MACA,GAAA4G,IAAA;QAAA,IAAAC,IAAA,GAAAzF,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAwF,SAAAC,GAAA;UAAA,IAAAC,SAAA,EAAAC,cAAA,EAAAC,eAAA,EAAAC,GAAA;UAAA,OAAA9F,mBAAA,GAAAS,IAAA,UAAAsF,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAApF,IAAA,GAAAoF,SAAA,CAAAnF,IAAA;cAAA;gBAAA,KACA6E,GAAA,CAAAO,SAAA;kBAAAD,SAAA,CAAAnF,IAAA;kBAAA;gBAAA;gBAAA8E,SAAA,GACAD,GAAA,CAAAQ,IAAA,EAAAN,cAAA,GAAAD,SAAA,CAAAC,cAAA,EAAAC,eAAA,GAAAF,SAAA,CAAAE,eAAA;gBACAjB,MAAA,CAAAuB,YAAA,GAAAP,cAAA;gBAAAI,SAAA,CAAAnF,IAAA;gBAAA,OACA+D,MAAA,CAAAwB,WAAA,CAAAP,eAAA;cAAA;gBACAjB,MAAA,CAAAyB,UAAA,CAAAR,eAAA;gBACAjB,MAAA,CAAApF,kBAAA,GAAAkG,GAAA,CAAAQ,IAAA,CAAA1G,kBAAA;gBAEA,IAAAqG,eAAA,CAAArD,MAAA;kBACAoC,MAAA,CAAAzG,WAAA,GAAA0H,eAAA,IAAAS,gBAAA;kBACAR,GAAA,GAAAlB,MAAA,CAAAzG,WAAA,CAAAoI,GAAA,WAAAxD,CAAA;oBACAA,CAAA,CAAAgB,OAAA;oBACA,OAAAhB,CAAA;kBACA;kBACA6B,MAAA,CAAAxG,iBAAA,GAAA3B,SAAA,CAAAqJ,GAAA;gBACA;gBACA1E,OAAA,CAAAC,GAAA,iBAAAuD,MAAA,CAAA7G,MAAA;gBACA6G,MAAA,CAAA4B,UAAA;gBACApF,OAAA,CAAAC,GAAA,iBAAAuD,MAAA,CAAA5G,YAAA;gBAAAgI,SAAA,CAAAnF,IAAA;gBAAA;cAAA;gBAEA+D,MAAA,CAAA3C,QAAA;kBACA5C,OAAA,EAAAqG,GAAA,CAAAe,OAAA;kBACAjI,IAAA;gBACA;cAAA;cAAA;gBAAA,OAAAwH,SAAA,CAAA9D,IAAA;YAAA;UAAA,GAAAuD,QAAA;QAAA,CAEA;QAAA,iBAAAiB,EAAA;UAAA,OAAAlB,IAAA,CAAAmB,KAAA,OAAApE,SAAA;QAAA;MAAA,KAAAqE,OAAA,WAAAC,CAAA;QACAjC,MAAA,CAAAjH,SAAA;MACA;IACA;IACAmJ,WAAA,WAAAA,YAAAC,KAAA;MAAA,IAAA1E,GAAA,GAAA0E,KAAA,CAAA1E,GAAA;MACA,QAAAA,GAAA,CAAA2E,QAAA;IACA;IACAZ,WAAA,WAAAA,YAAAa,IAAA;MAAA,IAAAC,MAAA;MAAA,OAAAnH,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkH,SAAA;QAAA,IAAAC,GAAA,EAAAC,SAAA;QAAA,OAAArH,mBAAA,GAAAS,IAAA,UAAA6G,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3G,IAAA,GAAA2G,SAAA,CAAA1G,IAAA;YAAA;cACAuG,GAAA;cACAC,SAAA,GAAAJ,IAAA,CAAAV,GAAA,WAAA5E,IAAA;gBACA;kBACA6F,EAAA,EAAA7F,IAAA,CAAAyF,GAAA;kBACAlC,SAAA,EAAAgC,MAAA,CAAA1F,QAAA;kBACAiG,IAAA,EAAAP,MAAA,CAAAvH,KAAA,OAAAuH,MAAA,CAAAtH,UAAA;gBACA;cACA;cAAA2H,SAAA,CAAA1G,IAAA;cAAA,OACA3E,WAAA,CAAAmL,SAAA,EAAA9B,IAAA,WAAAG,GAAA;gBACA,IAAAA,GAAA,CAAAO,SAAA;kBACA,IAAAyB,OAAA;kBACAhC,GAAA,CAAAQ,IAAA,CAAAjC,OAAA,WAAAtC,IAAA;oBACA+F,OAAA,CAAA/F,IAAA,CAAA6F,EAAA,MAAA7F,IAAA,CAAAgG,OAAA;kBACA;kBACAV,IAAA,CAAAhD,OAAA,WAAA5B,GAAA;oBACA,IAAAqF,OAAA,CAAArF,GAAA,CAAA+E,GAAA;sBACAF,MAAA,CAAAU,IAAA,CAAAvF,GAAA,cAAAqF,OAAA,CAAArF,GAAA,CAAA+E,GAAA;oBACA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAG,SAAA,CAAArF,IAAA;UAAA;QAAA,GAAAiF,QAAA;MAAA;IACA;IACAX,UAAA,WAAAA,WAAA;MAAA,IAAAqB,MAAA;MACAzG,OAAA,CAAAC,GAAA,oBAAA9C,UAAA;MACA,KAAAN,iBAAA;MACA,KAAA0F,KAAA,CAAAC,MAAA,CAAAa,gBAAA;MACA,IAAAE,IAAA,QAAAhF,KAAA;MACA,IAAAmI,SAAA,QAAAnI,KAAA;MACA,IAAAoI,QAAA,QAAAtJ,SAAA,CAAAqJ,SAAA,EAAAlF,KAAA,MAAA2B,MAAA,WAAAxB,CAAA;QAAA,SAAAA,CAAA;MAAA;MAEA,IAAAiF,cAAA,QAAArI,KAAA;MACA,IAAAsI,aAAA,QAAAxJ,SAAA,CAAAuJ,cAAA;MAEA,IAAAE,YAAA,QAAAnK,MAAA,CAAAwG,MAAA,WAAAxB,CAAA;QACA,KAAAgF,QAAA,CAAAvF,MAAA,IAAAyF,aAAA;UACA;QACA;UACA,OAAAF,QAAA,CAAAI,QAAA,CAAApF,CAAA,CAAA4B,IAAA;QACA;MACA;MAEA,IAAAyD,SAAA,GAAAF,YAAA,CAAA1F,MAAA,QAAAyF,aAAA,UAAAC,YAAA,GAAAA,YAAA,CAAA1F,MAAA,UAAAyF,aAAA,UAAAC,YAAA,QAAAnK,MAAA;MACA,IAAAsK,iBAAA,GAAAD,SAAA,CAAA7D,MAAA,WAAAxB,CAAA;QACA,IAAAkF,aAAA,YAAAF,QAAA,CAAAvF,MAAA;UACA;QACA;UACA,OAAAO,CAAA,CAAA4B,IAAA,EAAAwD,QAAA,CAAAF,aAAA;QACA;MACA;;MAEA;MACA,IAAAK,WAAA,GAAAJ,YAAA,CAAA9D,MAAA,CAAAiE,iBAAA;MACA;MACA,IAAAE,WAAA,GAAAD,WAAA,CAAAE,MAAA,WAAAC,GAAA,EAAAC,OAAA;QACA,IAAAC,cAAA,GAAAF,GAAA,CAAA3F,IAAA,WAAAnB,IAAA;UAAA,OAAAA,IAAA,CAAAiH,mBAAA,KAAAF,OAAA,CAAAE,mBAAA;QAAA;QACA,KAAAD,cAAA;UACAF,GAAA,CAAAI,IAAA,CAAAH,OAAA;QACA;QACA,OAAAD,GAAA;MACA;MAEA,KAAAzK,YAAA,GAAAuK,WAAA,CAAAhE,MAAA,WAAAxB,CAAA;QACA,KAAA8E,MAAA,CAAAtJ,UAAA;QACA,OAAAsJ,MAAA,CAAAtJ,UAAA,KAAAwE,CAAA,CAAA0E,IAAA;MACA,GAAAlD,MAAA,WAAAxB,CAAA;QACA,KAAA8E,MAAA,CAAApJ,SAAA,CAAAG,IAAA;QACA,QAAAmE,CAAA,CAAAnE,IAAA,QAAAuJ,QAAA,CAAAN,MAAA,CAAApJ,SAAA,CAAAG,IAAA;MACA,GAAA2F,MAAA,WAAAlC,GAAA;QACA,OAAAwF,MAAA,CAAArD,UAAA,CAAAnC,GAAA;MACA;IACA;IACAgE,UAAA,WAAAA,WAAAY,IAAA;MAAA,IAAA6B,MAAA;MAAA,IAAAC,OAAA,GAAAxG,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MACA,KAAAxE,MAAA,GAAAkJ,IAAA,CAAAV,GAAA,WAAAlE,GAAA;QAAA,IAAA2G,oBAAA;QACA,IAAAC,WAAA,KAAAD,oBAAA,GAAA3G,GAAA,CAAA6G,eAAA,cAAAF,oBAAA,uBAAAA,oBAAA,CAAApG,KAAA;QACA;QACA;QACAP,GAAA,CAAAc,IAAA,GAAA7G,MAAA;QACA,IAAA6M,OAAA,GAAA9G,GAAA,CAAA0G,OAAA,EAAAxE,MAAA,WAAA6E,CAAA;UAAA,OAAAH,WAAA,CAAAvH,SAAA,WAAA2H,CAAA;YAAA,OAAAD,CAAA,CAAAhG,YAAA,KAAAiG,CAAA;UAAA;QAAA;QACAhH,GAAA,CAAAiH,2BAAA,GAAAjH,GAAA,CAAAkH,oBAAA;QACA,IAAAC,SAAA;QACAL,OAAA,CAAAlF,OAAA,WAAAwF,GAAA,EAAAC,KAAA;UACA,IAAA/E,IAAA,GAAAmE,MAAA,CAAA5F,YAAA,CAAAb,GAAA,CAAAc,IAAA,EAAAsG,GAAA,CAAArG,YAAA,EAAAqG,GAAA,CAAApG,eAAA;UACA,IAAAsG,GAAA,GAAAb,MAAA,CAAAc,eAAA,CAAAvH,GAAA,CAAAc,IAAA,EAAAsG,GAAA,CAAArG,YAAA,EAAAqG,GAAA,CAAApG,eAAA;UACAhB,GAAA,CAAAsC,IAAA,IAAA8E,GAAA,CAAAI,KAAA;UACAf,MAAA,CAAAlB,IAAA,CAAAvF,GAAA,cAAAsC,IAAA,EAAA8E,GAAA,CAAAI,KAAA;UACAxH,GAAA,CAAAsH,GAAA;UACAH,SAAA,IAAAC,GAAA,CAAAI,KAAA;UACAf,MAAA,CAAAlB,IAAA,CAAAvF,GAAA,gBAAAoH,GAAA,CAAAzG,iBAAA,EAAAyG,GAAA,CAAAK,mBAAA,GAAAL,GAAA,CAAAI,KAAA;QACA;QAEAf,MAAA,CAAAlB,IAAA,CAAAvF,GAAA,mBAAAA,GAAA,CAAAiH,2BAAA,GAAAE,SAAA;QACAnH,GAAA,CAAAkH,oBAAA,GAAAlH,GAAA,CAAAqC,aAAA;QACAoE,MAAA,CAAAlB,IAAA,CAAAvF,GAAA,sBAAAA,GAAA,CAAAkH,oBAAA;QAEAT,MAAA,CAAAiB,WAAA,CAAA1H,GAAA;QAEAA,GAAA,CAAA2H,OAAA;QACA,OAAA3H,GAAA;MACA;IACA;IACA4H,WAAA,WAAAA,YAAA5H,GAAA;MACA,KAAA0H,WAAA,CAAA1H,GAAA;IACA;IACA0H,WAAA,WAAAA,YAAA1H,GAAA;MACA,IAAAmH,SAAA;MACA,IAAAU,eAAA,GAAA5I,MAAA,CAAA6I,IAAA,CAAA9H,GAAA,EACAkC,MAAA,WAAAxB,CAAA;QAAA,QAAAA,CAAA,CAAAqH,QAAA,WAAArH,CAAA,CAAAsH,UAAA,CAAAhI,GAAA,CAAAc,IAAA,KAAAJ,CAAA,CAAAP,MAAA,GAAAH,GAAA,CAAAc,IAAA,CAAAX,MAAA;MAAA;MACA0H,eAAA,CAAAjG,OAAA,WAAAV,GAAA;QACA,IAAA+G,OAAA,GAAA/G,GAAA,CAAAX,KAAA,CAAAjG,YAAA;QACA,IAAA4N,UAAA,GAAAL,eAAA,CAAA3F,MAAA,WAAAiG,CAAA;UACA,IAAA7F,IAAA,GAAA6F,CAAA,CAAA5H,KAAA,CAAAjG,YAAA;UACA,OAAA6N,CAAA,KAAAjH,GAAA,IAAAoB,IAAA,KAAA2F,OAAA;QACA,GAAA9B,MAAA,WAAAC,GAAA,EAAA9G,IAAA;UACA,OAAA8G,GAAA,GAAAlM,OAAA,CAAA8F,GAAA,CAAAV,IAAA,GAAA5E,KAAA;QACA;QACAsF,GAAA,CAAAkB,GAAA,GAAA5G,YAAA,YAAA0F,GAAA,CAAAoI,cAAA,GAAAF,UAAA;QACAf,SAAA,KAAAnH,GAAA,CAAAkB,GAAA;MACA;MACA;MACA;IACA;IACAmH,mBAAA,WAAAA,oBAAAC,UAAA,EAAAC,WAAA;MACA,KAAAD,UAAA;MACA,IAAA1D,IAAA,IAAA0D,UAAA,aAAAA,UAAA,uBAAAA,UAAA,CAAA/H,KAAA;MACA,SAAAqE,IAAA,CAAA4D,IAAA,WAAA9H,CAAA;QAAA,OAAAA,CAAA,KAAA6H,WAAA;MAAA;IACA;IAEAE,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MAAA,IAAAhN,MAAA,GAAAwE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,WAAAxE,MAAA;MACA;MACA,IAAAiN,SAAA,GAAAlK,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAmK,SAAA,CAAAlN,MAAA;MAAA,IAAAmN,KAAA,YAAAA,MAAA,EACA;UACA,IAAAhH,OAAA,GAAA8G,SAAA,CAAAG,CAAA;UACA,IAAAlE,IAAA;UACA,KAAA/C,OAAA,CAAAgF,eAAA;YACA6B,MAAA,CAAA9I,QAAA;cACA5C,OAAA;cACAb,IAAA;YACA;YAAA;cAAAuE,CAAA,EACA;gBAAAqI,MAAA;cAAA;YAAA;UACA;UACA,IAAAnC,WAAA,GAAAoC,KAAA,CAAAC,IAAA,KAAAC,GAAA,CAAArH,OAAA,CAAAgF,eAAA,CAAAtG,KAAA;UACAqG,WAAA,CAAAhF,OAAA,WAAAU,IAAA;YACA,IAAA6G,MAAA,GAAAT,MAAA,CAAA5M,WAAA,CAAAoG,MAAA,WAAAxB,CAAA;cAAA,OAAAA,CAAA,CAAAK,YAAA,KAAAuB,IAAA;YAAA;YAEA,IAAA8G,UAAA,GAAAD,MAAA,CAAAjF,GAAA,WAAAmF,KAAA,EAAAhC,KAAA;cACA,IAAAiC,KAAA,GAAAZ,MAAA,CAAA7H,YAAA,CAAAgB,OAAA,CAAAf,IAAA,EAAAwB,IAAA,EAAA+G,KAAA,CAAArI,eAAA;cACA,IAAAuI,IAAA,GAAAb,MAAA,CAAAnB,eAAA,CAAA1F,OAAA,CAAAf,IAAA,EAAAwB,IAAA,EAAA+G,KAAA,CAAArI,eAAA;cACA,IAAAwI,GAAA;gBACAC,SAAA,EAAA5H,OAAA,CAAA4H,SAAA;gBACAC,WAAA,GAAA7H,OAAA,CAAAyH,KAAA;gBACAhN,SAAA,EAAAoM,MAAA,CAAApL,KAAA,UAAAuE,OAAA,CAAAvF,SAAA;gBACAyE,YAAA,EAAAuB,IAAA;gBACAuE,eAAA,EAAAhF,OAAA,CAAAgF,eAAA;gBACA7F,eAAA,EAAAqI,KAAA,CAAArI,eAAA;gBACAL,iBAAA,EAAA0I,KAAA,CAAA1I,iBAAA;gBACAgJ,YAAA,EAAA9H,OAAA,CAAAoC,gBAAA,CAAAxD,IAAA,WAAAnB,IAAA;kBAAA,OAAAA,IAAA,CAAA0B,eAAA,KAAAqI,KAAA,CAAArI,eAAA;gBAAA,GAAA2I;cACA;cACA,OAAA9H,OAAA,aAAAyH,KAAA;cACA,OAAAzH,OAAA;cACA,OAAAA,OAAA;cACA,OAAAA,OAAA,CAAAyH,KAAA;cACA,OAAAzH,OAAA,CAAA0H,IAAA;cACA,OAAAC,GAAA;YACA;YACA5E,IAAA,CAAA4B,IAAA,CAAAlC,KAAA,CAAAM,IAAA,EAAAgF,kBAAA,CAAAR,UAAA;UACA;UACArK,OAAA,CAAAC,GAAA,CAAA4F,IAAA;UACA,OAAA/C,OAAA;UACA,OAAAA,OAAA;UACAA,OAAA,CAAAoC,gBAAA,GAAAW,IAAA;QACA;QAAAiF,IAAA;MAxCA,SAAAf,CAAA,MAAAA,CAAA,GAAAH,SAAA,CAAAxI,MAAA,EAAA2I,CAAA;QAAAe,IAAA,GAAAhB,KAAA;QAAA,IAAAgB,IAAA,SAAAA,IAAA,CAAAnJ,CAAA;MAAA;MAyCA;QAAAiI,SAAA,EAAAA,SAAA;QAAAI,MAAA;MAAA;IACA;IACAe,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,qBAAA,QAAAvB,eAAA;QAAAE,SAAA,GAAAqB,qBAAA,CAAArB,SAAA;QAAAI,MAAA,GAAAiB,qBAAA,CAAAjB,MAAA;MACA,KAAAA,MAAA;MACA,KAAAkB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAhO,IAAA;MACA,GAAA+G,IAAA;QACA6G,MAAA,CAAAxO,OAAA;QACA,IAAAiO,GAAA;UACAjG,cAAA,EAAAwG,MAAA,CAAAjG,YAAA;UACAN,eAAA,EAAAmF;QACA;QACA,IAAAyB,OAAA;UACA7G,cAAA,EAAAwG,MAAA,CAAAjG,YAAA;UACAuG,cAAA,EAAA1B;QACA;QACA,IAAA2B,SAAA,GAAAP,MAAA,CAAAzM,KAAA,GAAA7D,2BAAA,GAAAsQ,MAAA,CAAAxM,UAAA,GAAA3D,sCAAA,GAAAD,+BAAA;QACAoF,OAAA,CAAAC,GAAA,QAAAwK,GAAA;QACAc,SAAA,CAAAP,MAAA,CAAAzM,KAAA,GAAAkM,GAAA,GAAAY,OAAA,EAAAlH,IAAA,WAAAG,GAAA;UACA,IAAAA,GAAA,CAAAO,SAAA;YACAmG,MAAA,CAAAnK,QAAA;cACA5C,OAAA;cACAb,IAAA;YACA;YACA4N,MAAA,CAAAQ,WAAA;UACA;YACAR,MAAA,CAAAnK,QAAA;cACA5C,OAAA,EAAAqG,GAAA,CAAAe,OAAA;cACAjI,IAAA;YACA;UACA;UACA4N,MAAA,CAAAxO,OAAA;QACA;MACA,GAAAiP,KAAA;QACAT,MAAA,CAAAnK,QAAA;UACAzD,IAAA;UACAa,OAAA;QACA;MACA;IACA;IACAuN,WAAA,WAAAA,YAAA;MACA,KAAAE,SAAA;IACA;IACAA,SAAA,WAAAA,UAAA;MACAtQ,YAAA,MAAAuQ,MAAA,OAAA9L,MAAA;IACA;IACA+L,cAAA,WAAAA,eAAAC,KAAA;MACA,KAAAhP,iBAAA,GAAAgP,KAAA,CAAAC,OAAA;MACA9L,OAAA,CAAAC,GAAA,MAAAtD,MAAA;IACA;IACAoP,WAAA,WAAAA,YAAA;MAAA,IAAA1J,IAAA,GAAAlB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MACA,IAAAkB,IAAA,uBAAAA,IAAA;MACA,0BAAA5F,UAAA,CAAA+E,KAAA,CAAAjG,YAAA;IACA;IACA;IACAyQ,gBAAA,WAAAA,iBAAA;MACA,IAAAnG,IAAA,QAAAtD,KAAA,CAAAC,MAAA,CAAAyJ,kBAAA,GAAA9I,MAAA,WAAA5C,IAAA;QAAA,QAAAA,IAAA,CAAAqF,QAAA;MAAA;MACA,IAAAsG,YAAA,QAAAtP,YAAA,CAAAuG,MAAA,WAAA5C,IAAA;QAAA,QAAAsF,IAAA,CAAAkB,QAAA,CAAAxG,IAAA,MAAAA,IAAA,CAAAqF,QAAA;MAAA;MACA,KAAArD,KAAA,CAAAC,MAAA,CAAA2J,cAAA,CAAAtG,IAAA;MACA,KAAAtD,KAAA,CAAAC,MAAA,CAAA2J,cAAA,CAAAD,YAAA;MACA,KAAArP,iBAAA,QAAA0F,KAAA,CAAAC,MAAA,CAAAyJ,kBAAA;IACA;IACA5L,cAAA,WAAAA,eAAAkD,IAAA;MAAA,IAAA6I,MAAA;MAAA,OAAAzN,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAwN,SAAA;QAAA,OAAAzN,mBAAA,GAAAS,IAAA,UAAAiN,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/M,IAAA,GAAA+M,SAAA,CAAA9M,IAAA;YAAA;cAAA8M,SAAA,CAAA9M,IAAA;cAAA,OACAjF,aAAA;gBACA+I,IAAA,EAAAA;cACA,GAAAY,IAAA,WAAAG,GAAA;gBACA,IAAAO,SAAA,GAAAP,GAAA,CAAAO,SAAA;kBAAAC,IAAA,GAAAR,GAAA,CAAAQ,IAAA;kBAAAO,OAAA,GAAAf,GAAA,CAAAe,OAAA;gBACA,IAAAR,SAAA;kBACAuH,MAAA,CAAAI,QAAA,GAAAtM,MAAA,CAAAC,MAAA,KAAAiM,MAAA,CAAAI,QAAA,EAAA1H,IAAA,CAAA2H,IAAA;kBACA,IAAA5G,IAAA,GAAAf,IAAA,CAAA4H,UAAA;kBACAN,MAAA,CAAAtP,OAAA,GAAA+I,IAAA,CAAA1C,MAAA,WAAAxB,CAAA;oBAAA,OAAAA,CAAA,CAAAgL,UAAA;kBAAA,GAAAxH,GAAA,WAAA5E,IAAA;oBACA,IAAA9F,UAAA,CAAAsM,QAAA,CAAAxG,IAAA,CAAAC,IAAA;sBACAD,IAAA,CAAAqM,KAAA;oBACA;oBACA,IAAArM,IAAA,CAAAC,IAAA;sBACAD,IAAA,CAAAoC,OAAA;oBACA;oBACA,OAAApC,IAAA;kBACA;gBACA;kBACA6L,MAAA,CAAAvL,QAAA;oBACA5C,OAAA,EAAAoH,OAAA;oBACAjI,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAmP,SAAA,CAAAzL,IAAA;UAAA;QAAA,GAAAuL,QAAA;MAAA;IACA;IACAQ,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAC,aAAA;MAAA,IAAA9L,GAAA,GAAA6L,KAAA,CAAA7L,GAAA;QAAA+L,MAAA,GAAAF,KAAA,CAAAE,MAAA;QAAAC,WAAA,GAAAH,KAAA,CAAAG,WAAA;MACA,SAAA3O,MAAA;MACA,IAAA0O,MAAA,CAAAE,KAAA;MACA,IAAA1D,WAAA,IAAAuD,aAAA,GAAAC,MAAA,CAAAE,KAAA,cAAAH,aAAA,uBAAAA,aAAA,CAAAvL,KAAA;MACA,YAAA8H,mBAAA,CAAArI,GAAA,CAAA6G,eAAA,EAAA0B,WAAA;IACA;IACA1H,YAAA,WAAAA,aAAAC,IAAA,EAAAyH,WAAA,EAAA2D,SAAA;MACA,UAAAnK,MAAA,CAAAjB,IAAA,EAAAiB,MAAA,CAAAzH,YAAA,EAAAyH,MAAA,CAAAwG,WAAA,EAAAxG,MAAA,CAAAzH,YAAA,EAAAyH,MAAA,CAAAmK,SAAA;IACA;IACA3E,eAAA,WAAAA,gBAAAzG,IAAA,EAAAyH,WAAA,EAAA2D,SAAA;MACA,YAAArL,YAAA,CAAAC,IAAA,EAAAyH,WAAA,EAAA2D,SAAA,OAAAnK,MAAA,CAAAzH,YAAA;IACA;IACA;IACA6R,eAAA,WAAAA,gBAAA;MACA,KAAAzP,aAAA;MACAqC,OAAA,CAAAC,GAAA,MAAAlD,WAAA;IACA;IACA;IACAsQ,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,oBAAA,QAAA1Q,iBAAA,CAAAsI,GAAA,WAAA5E,IAAA;QAAA,OAAAA,IAAA,CAAAiH,mBAAA;MAAA;MACA,IAAAgG,oBAAA,QAAA3Q,iBAAA,IAAAmF,YAAA;MACAjH,wBAAA;QACAwS,oBAAA,EAAAA,oBAAA;QACAC,oBAAA,EAAAA,oBAAA;QACApP,kBAAA,OAAAA,kBAAA;QACAsF,YAAA,OAAAnF,KAAA,YAAAC,UAAA;QACAsF,SAAA,OAAA1D;MACA,GAAA+D,IAAA,WAAAG,GAAA;QACA,IAAAA,GAAA,CAAAO,SAAA;UACA,IAAAP,GAAA,CAAAQ,IAAA,CAAA1D,MAAA;YACAkM,MAAA,CAAA1P,iBAAA;YACA;UACA;UACA0P,MAAA,CAAAG,eAAA,CAAAnJ,GAAA,CAAAQ,IAAA;QACA;UACAwI,MAAA,CAAAzM,QAAA;YACAzD,IAAA;YACAa,OAAA,EAAAqG,GAAA,CAAAe;UACA;QACA;MACA;IACA;IACAqI,YAAA,WAAAA,aAAA;MACA,KAAA/P,aAAA;MACA;IACA;IACAgQ,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAA/Q,iBAAA,CAAAgG,OAAA,WAAAtC,IAAA;QAAA,OACAA,IAAA,CAAA2E,gBAAA,CAAArC,OAAA,WAAAlB,CAAA;UACA,IAAAA,CAAA,CAAAM,eAAA,KAAA2L,MAAA,CAAA/P,IAAA,CAAAC,SAAA;YACA6D,CAAA,CAAA8G,KAAA,GAAAlI,IAAA,CAAA4H,oBAAA;UACA;YACAxG,CAAA,CAAA8G,KAAA;UACA;QACA;MAAA,CACA;MACA;MAAA,IAAAoF,MAAA,YAAAA,OAAA,EACA;QACA,IAAA/K,OAAA,GAAA8K,MAAA,CAAA/Q,iBAAA,CAAAkN,CAAA;QACA,IAAAlC,WAAA,GAAAoC,KAAA,CAAAC,IAAA,KAAAC,GAAA,CAAArH,OAAA,CAAAgF,eAAA,CAAAtG,KAAA;QACAqG,WAAA,CAAAhF,OAAA,WAAAU,IAAA;UACA,IAAA6G,MAAA,GAAAwD,MAAA,CAAA7Q,WAAA,CAAAoG,MAAA,WAAAxB,CAAA;YAAA,OAAAA,CAAA,CAAAK,YAAA,KAAAuB,IAAA;UAAA;UACA6G,MAAA,CAAAvH,OAAA,WAAAyH,KAAA;YACA,IAAAwD,OAAA,GAAAF,MAAA,CAAA9L,YAAA,CAAAgB,OAAA,CAAAf,IAAA,EAAAwB,IAAA,EAAAqK,MAAA,CAAA/P,IAAA,CAAAC,SAAA;YACA,IAAAyM,KAAA,GAAAqD,MAAA,CAAA9L,YAAA,CAAAgB,OAAA,CAAAf,IAAA,EAAAwB,IAAA,EAAA+G,KAAA,CAAArI,eAAA;YACA,IAAA8L,MAAA,GAAAH,MAAA,CAAApF,eAAA,CAAA1F,OAAA,CAAAf,IAAA,EAAAwB,IAAA,EAAAqK,MAAA,CAAA/P,IAAA,CAAAC,SAAA;YACA,IAAA0M,IAAA,GAAAoD,MAAA,CAAApF,eAAA,CAAA1F,OAAA,CAAAf,IAAA,EAAAwB,IAAA,EAAA+G,KAAA,CAAArI,eAAA;YAEA,IAAA6L,OAAA,KAAAvD,KAAA,IAAAwD,MAAA,KAAAvD,IAAA;cACA1H,OAAA,CAAAyH,KAAA,IAAAzH,OAAA;cACAA,OAAA,CAAA0H,IAAA,IAAA1H,OAAA;YACA;cACAA,OAAA,CAAAyH,KAAA;cACAzH,OAAA,CAAA0H,IAAA;YACA;UACA;QACA;MACA;MApBA,SAAAT,CAAA,MAAAA,CAAA,QAAAlN,iBAAA,CAAAuE,MAAA,EAAA2I,CAAA;QAAA8D,MAAA;MAAA;MAqBA7N,OAAA,CAAAC,GAAA,MAAApD,iBAAA;MAAA,IAAAmR,MAAA,YAAAA,OAAAC,EAAA,EACA;QAAA,IAAAC,MAAA,YAAAA,OAAAC,CAAA,EACA;UACA,IAAAP,MAAA,CAAAjR,MAAA,CAAAoN,EAAA,EAAAhI,IAAA,KAAA6L,MAAA,CAAA/Q,iBAAA,CAAAsR,CAAA,EAAApM,IAAA;YACA6L,MAAA,CAAAQ,SAAA,WAAA3I,CAAA;cACAmI,MAAA,CAAAjR,MAAA,CAAAoN,EAAA,IAAA6D,MAAA,CAAA/Q,iBAAA,CAAAsR,CAAA;YACA;UACA;QACA;QANA,SAAAA,CAAA,MAAAA,CAAA,GAAAP,MAAA,CAAA/Q,iBAAA,CAAAuE,MAAA,EAAA+M,CAAA;UAAAD,MAAA,CAAAC,CAAA;QAAA;MAOA;MARA,SAAApE,EAAA,MAAAA,EAAA,QAAApN,MAAA,CAAAyE,MAAA,EAAA2I,EAAA;QAAAiE,MAAA,CAAAC,EAAA;MAAA;IASA;IACAI,UAAA,WAAAA,WAAAC,QAAA;MAAA,IAAAC,OAAA;MACA,KAAAhM,KAAA,CAAA+L,QAAA,EAAAE,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACAF,OAAA,CAAAG,YAAA;UACA;UACAH,OAAA,CAAAb,YAAA;UACA1N,OAAA,CAAAC,GAAA,CAAAsO,OAAA,CAAA5R,MAAA;UACA4R,OAAA,CAAA1R,iBAAA;UACA0R,OAAA,CAAAhM,KAAA,CAAAC,MAAA,CAAAa,gBAAA;QACA;UACA;QACA;MACA;IACA;IACA;IACAoK,eAAA,WAAAA,gBAAAkB,cAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,iBAAA,OAAAC,GAAA;MACAH,cAAA,CAAA9L,OAAA,WAAAtC,IAAA;QACA,IAAAyF,GAAA,MAAAhD,MAAA,CAAAzC,IAAA,CAAAiH,mBAAA,OAAAxE,MAAA,CAAAzC,IAAA,CAAA0B,eAAA;QACA4M,iBAAA,CAAAE,GAAA,CAAA/I,GAAA,EAAAzF,IAAA,CAAAyO,kBAAA;MACA;MAEA,IAAAC,eAAA,YAAAA,gBAAAhO,GAAA,EAAAQ,IAAA,EAAAyN,MAAA;QACA,IAAAC,OAAA,GAAAP,OAAA,CAAA9M,YAAA,CAAAb,GAAA,CAAAc,IAAA,EAAAN,IAAA,CAAAO,YAAA,EAAAP,IAAA,CAAAQ,eAAA;QACA,IAAAmN,IAAA,MAAApM,MAAA,CAAA/B,GAAA,CAAAuG,mBAAA,OAAAxE,MAAA,CAAAvB,IAAA,CAAAQ,eAAA;QACA,IAAAoN,gBAAA,GAAAR,iBAAA,CAAAS,GAAA,CAAAF,IAAA;QACA,IAAAG,SAAA,GAAAC,IAAA,CAAAC,GAAA,CAAAP,MAAA,EAAAG,gBAAA,EAAApO,GAAA,CAAAkH,oBAAA;QAEAlH,GAAA,CAAAkO,OAAA,KAAAO,MAAA,CAAAzO,GAAA,CAAAkO,OAAA,WAAAI,SAAA;QACAtO,GAAA,CAAAkH,oBAAA,IAAAoH,SAAA;QACAtO,GAAA,CAAA2N,OAAA,CAAA7C,WAAA,CAAAtK,IAAA,CAAAG,iBAAA,MAAAX,GAAA,CAAA2N,OAAA,CAAA7C,WAAA,CAAAtK,IAAA,CAAAG,iBAAA,WAAA2N,SAAA;QACAtO,GAAA,CAAA2N,OAAA,CAAA7C,WAAA,OAAA9K,GAAA,CAAA2N,OAAA,CAAA7C,WAAA,YAAAwD,SAAA;QACAtO,GAAA,aAAAkO,OAAA,IAAAlO,GAAA,CAAAkO,OAAA;QAEA,OAAAI,SAAA;MACA;MACA,IAAAI,SAAA;MACA,KAAA9S,iBAAA,CAAAgG,OAAA,WAAA5B,GAAA;QACA,KAAAA,GAAA,CAAAkH,oBAAA,IAAAyG,OAAA,CAAAnS,UAAA;QAEA,IAAAmT,aAAA,GAAA3O,GAAA,CAAAiE,gBAAA,CAAA/B,MAAA,WAAA1B,IAAA;UAAA,OACAoN,iBAAA,CAAAgB,GAAA,IAAA7M,MAAA,CAAA/B,GAAA,CAAAuG,mBAAA,OAAAxE,MAAA,CAAAvB,IAAA,CAAAQ,eAAA;QAAA,CACA;QAEA,IAAA2N,aAAA,CAAAxO,MAAA;QACA,IAAAwN,OAAA,CAAAnS,UAAA;UACA,IAAAqT,OAAA;UACA,IAAAC,cAAA,GAAAH,aAAA,CAAAxI,MAAA,WAAA4I,GAAA,EAAAvO,IAAA;YACA,IAAA2N,IAAA,MAAApM,MAAA,CAAA/B,GAAA,CAAAuG,mBAAA,OAAAxE,MAAA,CAAAvB,IAAA,CAAAQ,eAAA;YACA,OAAA+N,GAAA,IAAAnB,iBAAA,CAAAS,GAAA,CAAAF,IAAA;UACA;UAEA,IAAAnO,GAAA,CAAAgP,gBAAA,GAAAF,cAAA;YACAD,OAAA;YACA,IAAAA,OAAA,KAAAlB,OAAA,CAAA/R,iBAAA,CAAAuE,MAAA;cACAuO,SAAA;cACAf,OAAA,CAAAhR,iBAAA;YACA;YACA;UACA;UAEA,IAAAsS,SAAA,GAAAV,IAAA,CAAAC,GAAA,CAAAxO,GAAA,CAAAqC,aAAA,EAAArC,GAAA,CAAAkH,oBAAA;UACA,IAAAgI,iBAAA,GAAAX,IAAA,CAAAY,KAAA,CAAAF,SAAA,GAAAN,aAAA,CAAAxO,MAAA;UAEAwO,aAAA,CAAA/M,OAAA,WAAApB,IAAA,EAAA6G,KAAA;YACA,IAAA4H,SAAA;YAEA,IAAAG,cAAA,GAAA/H,KAAA,KAAAsH,aAAA,CAAAxO,MAAA,OACA8O,SAAA,GACAV,IAAA,CAAAC,GAAA,CAAAU,iBAAA,EAAAD,SAAA;YAEAA,SAAA,IAAAjB,eAAA,CAAAhO,GAAA,EAAAQ,IAAA,EAAA4O,cAAA;UACA;UAEApP,GAAA,CAAAgP,gBAAA,GAAAhP,GAAA,CAAAkH,oBAAA;UACAlH,GAAA,CAAAqC,aAAA,GAAArC,GAAA,CAAAkH,oBAAA;UACA,IAAA2H,OAAA,KAAAlB,OAAA,CAAA/R,iBAAA,CAAAuE,MAAA;YACAuO,SAAA;YACAf,OAAA,CAAAhR,iBAAA;YACA;UACA;QACA;UACAgS,aAAA,CAAA/M,OAAA,WAAApB,IAAA;YACA,IAAA2N,IAAA,MAAApM,MAAA,CAAA/B,GAAA,CAAAuG,mBAAA,OAAAxE,MAAA,CAAAvB,IAAA,CAAAQ,eAAA;YACA,IAAAoN,gBAAA,GAAAR,iBAAA,CAAAS,GAAA,CAAAF,IAAA;YAEA,IAAAnO,GAAA,CAAA2N,OAAA,CAAA5N,WAAA,CAAAC,GAAA,KAAAoO,gBAAA;cACA;YACA;YAEA,IAAAiB,SAAA,GAAAd,IAAA,CAAAC,GAAA,CACAxO,GAAA,CAAA2N,OAAA,CAAA5N,WAAA,CAAAC,GAAA,oBACAA,GAAA,CAAA2N,OAAA,CAAA5N,WAAA,CAAAC,GAAA,SACAoO,gBACA;YAEA,IAAAiB,SAAA;cACA,IAAAnB,OAAA,GAAAP,OAAA,CAAA9M,YAAA,CAAAb,GAAA,CAAAc,IAAA,EAAAN,IAAA,CAAAO,YAAA,EAAAP,IAAA,CAAAQ,eAAA;cACAhB,GAAA,CAAA2N,OAAA,CAAA7C,WAAA,CAAAtK,IAAA,CAAAG,iBAAA,MAAAX,GAAA,CAAA2N,OAAA,CAAA7C,WAAA,CAAAtK,IAAA,CAAAG,iBAAA,WAAA0O,SAAA;cACArP,GAAA,CAAA2N,OAAA,CAAA7C,WAAA,OAAA9K,GAAA,CAAA2N,OAAA,CAAA7C,WAAA,YAAAuE,SAAA;cACArP,GAAA,CAAAkO,OAAA,KAAAO,MAAA,CAAAzO,GAAA,CAAAkO,OAAA,WAAAmB,SAAA;cACArP,GAAA,CAAA2N,OAAA,CAAA5N,WAAA,CAAAC,GAAA,MAAAA,GAAA,CAAA2N,OAAA,CAAA5N,WAAA,CAAAC,GAAA,WAAAqP,SAAA;cACArP,GAAA,CAAA2N,OAAA,CAAA5N,WAAA,CAAAC,GAAA,iBAAAA,GAAA,CAAA2N,OAAA,CAAA5N,WAAA,CAAAC,GAAA,sBAAAqP,SAAA;cACArP,GAAA,aAAAkO,OAAA,IAAAlO,GAAA,CAAAkO,OAAA;YACA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,KAAAvS,YAAA,QAAAD,MAAA,CAAAwG,MAAA,WAAAlC,GAAA;QACA,OAAA2N,OAAA,CAAAxL,UAAA,CAAAnC,GAAA;MACA;MACA,KAAApE,iBAAA;MACA,KAAA0F,KAAA,CAAAC,MAAA,CAAAa,gBAAA;MACA,IAAAsM,SAAA;QACA,KAAA9O,QAAA;UACA5C,OAAA;UACAb,IAAA;QACA;MACA;IACA;IACA;IACAsR,YAAA,WAAAA,aAAA;MAAA,IAAA6B,OAAA;MACA,SAAA9T,UAAA;QACA,KAAAI,iBAAA,CACAgG,OAAA,WAAA5B,GAAA,EAAAjC,GAAA;UACA,IAAAiC,GAAA,CAAAkH,oBAAA;YACA,IAAA1G,IAAA,GAAAR,GAAA,CAAAiE,gBAAA,CAAAxD,IAAA,WAAAC,CAAA;cAAA,OAAAA,CAAA,CAAAM,eAAA,KAAAsO,OAAA,CAAA1S,IAAA,CAAAC,SAAA;YAAA;YACA,IAAAqR,OAAA,GAAAoB,OAAA,CAAAzO,YAAA,CAAAb,GAAA,CAAAc,IAAA,EAAAN,IAAA,CAAAO,YAAA,EAAAP,IAAA,CAAAQ,eAAA;YACAhB,GAAA,CAAAkO,OAAA,IAAAO,MAAA,CAAAzO,GAAA,CAAAkO,OAAA,KAAAlO,GAAA,CAAAqC,aAAA;YACArC,GAAA,CAAAkH,oBAAA,GAAAlH,GAAA,CAAAkH,oBAAA,GAAAlH,GAAA,CAAAqC,aAAA;YACArC,GAAA,CAAAsP,OAAA,CAAAxE,WAAA,CAAAtK,IAAA,CAAAG,iBAAA,MAAAX,GAAA,CAAAqC,aAAA;YACArC,GAAA,CAAAsP,OAAA,CAAAxE,WAAA,OAAA9K,GAAA,CAAAqC,aAAA;YACArC,GAAA,CAAAgP,gBAAA,GAAAhP,GAAA,CAAAkH,oBAAA;YACAlH,GAAA,CAAAqC,aAAA,GAAArC,GAAA,CAAAkH,oBAAA;YACAlH,GAAA,aAAAkO,OAAA,IAAAlO,GAAA,CAAAkO,OAAA;UACA;QACA;MACA;QACA,KAAAtS,iBAAA,CACAgG,OAAA,WAAA5B,GAAA,EAAAjC,GAAA;UACA,IAAAyC,IAAA,GAAAR,GAAA,CAAAiE,gBAAA,CAAAxD,IAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAM,eAAA,KAAAsO,OAAA,CAAA1S,IAAA,CAAAC,SAAA;UAAA;UACA,IAAAqR,OAAA,GAAAoB,OAAA,CAAAzO,YAAA,CAAAb,GAAA,CAAAc,IAAA,EAAAN,IAAA,CAAAO,YAAA,EAAAP,IAAA,CAAAQ,eAAA;UAEA,IAAAqO,SAAA,GAAAd,IAAA,CAAAC,GAAA,CAAAxO,GAAA,CAAAsP,OAAA,CAAAvP,WAAA,CAAAC,GAAA,eAAAA,GAAA,CAAAsP,OAAA,CAAAvP,WAAA,CAAAC,GAAA;UAEAA,GAAA,CAAAsP,OAAA,CAAAxE,WAAA,CAAAtK,IAAA,CAAAG,iBAAA,MAAA0O,SAAA;UACArP,GAAA,CAAAsP,OAAA,CAAAxE,WAAA,OAAAuE,SAAA;UAEArP,GAAA,CAAAkO,OAAA,IAAAO,MAAA,CAAAzO,GAAA,CAAAkO,OAAA,KAAAmB,SAAA;UACArP,GAAA,CAAAsP,OAAA,CAAAvP,WAAA,CAAAC,GAAA,MAAAqP,SAAA;UACArP,GAAA,CAAAsP,OAAA,CAAAvP,WAAA,CAAAC,GAAA,iBAAAqP,SAAA;UAEArP,GAAA,aAAAkO,OAAA,IAAAlO,GAAA,CAAAkO,OAAA;QACA;MACA;MACA,KAAAvS,YAAA,QAAAD,MAAA,CAAAwG,MAAA,WAAAlC,GAAA;QACA,OAAAsP,OAAA,CAAAnN,UAAA,CAAAnC,GAAA;MACA;IACA;IACAuP,SAAA,WAAAA,UAAAlC,QAAA;MACA,KAAA/L,KAAA,CAAA+L,QAAA,EAAAmC,WAAA;MACA,KAAA9S,aAAA;IACA;IACA+S,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAC,MAAA,GAAAD,KAAA,CAAAC,MAAA;QAAA3P,GAAA,GAAA0P,KAAA,CAAA1P,GAAA;MACA,OAAAA,GAAA,CAAAoF,IAAA,CAAAU,QAAA,CAAA6J,MAAA,CAAA9U,IAAA;IACA;IACA+U,uBAAA,WAAAA,wBAAAC,KAAA;MAAA,IAAAF,MAAA,GAAAE,KAAA,CAAAF,MAAA;MACAA,MAAA,CAAA9U,IAAA;IACA;IACAiV,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAJ,MAAA,GAAAI,KAAA,CAAAJ,MAAA;QAAA3P,GAAA,GAAA+P,KAAA,CAAA/P,GAAA;MACAjB,OAAA,CAAAC,GAAA,gBAAA2Q,MAAA,EAAA3P,GAAA;MACA,OAAAA,GAAA,MAAA1C,KAAA,8BAAAwI,QAAA,CAAA6J,MAAA,CAAA9U,IAAA;IACA;IACAmV,uBAAA,WAAAA,wBAAAC,KAAA;MAAA,IAAAN,MAAA,GAAAM,KAAA,CAAAN,MAAA;MACAA,MAAA,CAAA9U,IAAA;IACA;IACA4E,iBAAA,WAAAA,kBAAA;MAAA,IAAAyQ,OAAA;MACA7V,eAAA;QAAA8V,YAAA;MAAA,GAAAjN,IAAA,WAAAG,GAAA;QACA,IAAAA,GAAA,CAAAO,SAAA;UACAsM,OAAA,CAAAjV,cAAA,CAAAJ,IAAA,GAAAwI,GAAA,CAAAQ,IAAA;UACAqM,OAAA,CAAA/C,SAAA,WAAA3I,CAAA;YAAA,IAAA4L,aAAA;YACA,CAAAA,aAAA,GAAAF,OAAA,CAAA5O,KAAA,cAAA8O,aAAA,gBAAAA,aAAA,GAAAA,aAAA,CAAAC,oBAAA,cAAAD,aAAA,eAAAA,aAAA,CAAAE,iBAAA,CAAAjN,GAAA,CAAAQ,IAAA;UACA;QACA;UACAqM,OAAA,CAAAtQ,QAAA;YACAzD,IAAA;YACAa,OAAA,EAAAqG,GAAA,CAAAe;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
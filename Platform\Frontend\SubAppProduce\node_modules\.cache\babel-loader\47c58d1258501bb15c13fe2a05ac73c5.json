{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\part-list\\v4\\component\\Schduling.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\part-list\\v4\\component\\Schduling.vue", "mtime": 1757468112945}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IEdldFNjaGVkdWxpbmdQYXJ0TGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9wcm9qZWN0JzsKZXhwb3J0IGRlZmF1bHQgewogIHByb3BzOiB7CiAgICB0eXBlRW50aXR5OiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogZnVuY3Rpb24gX2RlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIHt9OwogICAgICB9CiAgICB9LAogICAgbGV2ZWxOYW1lOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB0YWJsZURhdGE6IFtdCiAgICB9OwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHt9LAogIG1ldGhvZHM6IHsKICAgIGluaXQ6IGZ1bmN0aW9uIGluaXQocm93RGF0YSkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICBHZXRTY2hlZHVsaW5nUGFydExpc3QoewogICAgICAgIGlkOiByb3dEYXRhLlBhcnRfQWdncmVnYXRlX0lkCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICBfdGhpcy50YWJsZURhdGEgPSByZXMuRGF0YS5TY2hlZHVsaW5nX0xpc3Q7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["GetSchedulingPartList", "props", "typeEntity", "type", "Object", "default", "levelName", "String", "data", "tableData", "mounted", "methods", "init", "rowData", "_this", "id", "Part_Aggregate_Id", "then", "res", "IsSucceed", "Data", "Scheduling_List", "$message", "message", "Message"], "sources": ["src/views/PRO/part-list/v4/component/Schduling.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-row>\r\n      <el-table stripe class=\"cs-custom-table\" :data=\"tableData\" empty-text=\"暂无数据\" border style=\"width: 100%\">\r\n        <el-table-column prop=\"Scheduling_Status\" :label=\"levelName + '状态'\" align=\"center\" />\r\n        <el-table-column prop=\"Component_Count\" label=\"数量\" align=\"center\" />\r\n      </el-table>\r\n\r\n      <el-col :span=\"24\">\r\n        <div style=\"text-align: right; margin-top: 10px\">\r\n          <el-button @click=\"$emit('close')\">关 闭</el-button>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetSchedulingPartList } from '@/api/PRO/project'\r\nexport default {\r\n  props: {\r\n    typeEntity: {\r\n      type: Object,\r\n      default: () => {\r\n        return {}\r\n      }\r\n\r\n    },\r\n    levelName: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      tableData: [\r\n\r\n      ]\r\n    }\r\n  },\r\n  mounted() {},\r\n  methods: {\r\n    init(rowData) {\r\n      GetSchedulingPartList({ id: rowData.Part_Aggregate_Id }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.tableData = res.Data.Scheduling_List\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped></style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAkBA,SAAAA,qBAAA;AACA;EACAC,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QACA;MACA;IAEA;IACAC,SAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;IAGA;EACA;EACAC,OAAA,WAAAA,QAAA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAAC,OAAA;MAAA,IAAAC,KAAA;MACAd,qBAAA;QAAAe,EAAA,EAAAF,OAAA,CAAAG;MAAA,GAAAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAL,KAAA,CAAAL,SAAA,GAAAS,GAAA,CAAAE,IAAA,CAAAC,eAAA;QACA;UACAP,KAAA,CAAAQ,QAAA;YACAC,OAAA,EAAAL,GAAA,CAAAM,OAAA;YACArB,IAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
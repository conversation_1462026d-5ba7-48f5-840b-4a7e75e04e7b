<template>
  <div class="company-add-container">
    <div class="title">
      <el-button type="primary" @click="handleAddToList">加入列表</el-button>
    </div>
    <el-tabs v-model="activeType" type="card" @tab-click="handleClick">
      <el-tab-pane v-for="item in bomList" :key="item.Code" :label="item.Display_Name" :name="item.Code" />
    </el-tabs>
    <div class="tree-container">
      <el-tree
        ref="tree"
        v-loading="loading"
        :current-node-key="currentNodeKey"
        element-loading-text="加载中"
        element-loading-spinner="el-icon-loading"
        empty-text="暂无数据"
        highlight-current
        show-checkbox
        node-key="Id"
        default-expand-all
        :expand-on-click-node="false"
        :data="treeData"
        :props="{
          label:'Label',
          children:'Children'
        }"
        @node-click="handleNodeClick"
      >
        <span slot-scope="{ node, data }" class="custom-tree-node">
          <svg-icon
            :icon-class="
              node.expanded ? 'icon-folder-open' : 'icon-folder'
            "
            class-name="class-icon"
          />
          <span class="cs-label" :title="node.label">{{ node.label }}</span>
        </span>
      </el-tree>
    </div>
    <footer>
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleAdd">确 定</el-button>
    </footer>
  </div>
</template>

<script>
import { GetCompTypeTree } from '@/api/PRO/component-type'
import { GetPartTypeTree } from '@/api/PRO/partType'
import { GetBOMInfo } from '@/views/PRO/bom-setting/utils'
export default {
  components: {

  },
  props: {
    typeCode: {
      type: String,
      default: ''
    },
    typeId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      treeData: [],
      loading: true,
      currentNodeKey: '',
      activeType: '-1',
      bomList: []
    }
  },
  async mounted() {
    const { list } = await GetBOMInfo()
    this.bomList = list
    this.fetchData()
  },
  methods: {
    async fetchData() {
      this.loading = true
      let res
      if (this.activeType === '-1') {
        res = await GetCompTypeTree({ professional: this.typeCode })
      } else {
        res = await GetPartTypeTree({ professionalId: this.typeId, partGrade: this.activeType.toString() })
      }
      if (res.IsSucceed) {
        this.treeData = res.Data
      }
      this.loading = false
    },
    handleNodeClick(node) {
      console.log(node)
    },
    handleClick(tab, event) {
      this.activeType = tab.name
      this.fetchData()
    },
    handleAddToList() {
      console.log('加入列表')
    },
    handleAdd() {
      console.log('添加')
    },
    handleCancel() {
      console.log('取消')
      this.$emit('close')
    }
  }

}
</script>

<style lang="scss">
@import "~@/styles/mixin.scss";
.company-add-container {
  height: 70vh;
  display: flex;
  flex-direction: column;

  .title {
    margin-bottom: 16px;
  }

  .tree-container {
    flex: 1;
    overflow: hidden;

    .el-tree {
      @include scrollBar;
      height: 100%;
      overflow: auto;
    }
  }

  footer {
    text-align: right;
    margin-top: 16px;
  }
}

</style>

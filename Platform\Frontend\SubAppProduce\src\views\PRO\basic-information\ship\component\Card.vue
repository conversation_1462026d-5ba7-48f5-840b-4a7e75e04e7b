<template>
  <div class="card-x">
    <i class="el-icon-ship" style="font-size: 32px;color: #298DFF;margin-right: 12px" />
    <main class="cs-main">
      <div class="clearfix">
        <strong class="number">{{ item.Shipnumber }}</strong>
        <el-button style="float: right" @click="handleEdit(item)">编 辑</el-button>
        <el-button style="float: right;margin-right:10px" type="danger"  @click="handleDelete(item)">删 除</el-button>
      </div>
      <span class="info">
        {{ item.Captain }}
        <el-divider direction="vertical" />
        {{ item.Mobile }}
      </span>
      <div class="tag-x">
        <span v-for="i in item.Factory_Name" :key="i" class="tag-w">{{ i }}</span>
      </div>
    </main>
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    handleEdit() {
      this.$emit('edit', this.item)
    },
    handleDelete(){
      this.$emit('delete', this.item)
    }
  }
}
</script>

<style scoped lang="scss">
.card-x{
  display: flex;
  background-color: #FFFFFF;
  width: calc(25% - 16px);
  min-width: 387px;
  min-height: 148px;
  margin: 8px;
  padding: 20px;
  box-shadow: 0px 1px 4px rgba(20, 35, 78, 0.08);
  border-radius: 8px;

  .icon-steeringwheel{
    display: inline-block;
    margin-right: 12px;
    font-size: 32px;
    color: #298DFF;
  }
  .cs-main{
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding-top: 3px;

    .number{
      line-height: 30px;
    }

    .info{
      margin-bottom: 20px;
      display: inline-block;
      font-size: 14px;
      color: rgba(34, 40, 52, 0.65);
    }
    .tag-x{
      color: #818FB7;
      .tag-w{
        display: inline-block;
        background: rgba(129, 143, 183, 0.12);
        border-radius: 4px;
        font-size: 14px;
        padding:3px 6px;
        margin: 4px;
      }
    }

  }
}
</style>

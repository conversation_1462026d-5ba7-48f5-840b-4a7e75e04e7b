{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\change-management\\contact-list\\add.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\change-management\\contact-list\\add.vue", "mtime": 1757572678775}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZUFyZWFUcmVlcywgR2V0SW5zdGFsbFVuaXRJZE5hbWVMaXN0LCBHZXRQcm9qZWN0UGFnZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vcHJvamVjdCcNCmltcG9ydCB7IEdldEZhY3RvcnlQZW9wbGVsaXN0IH0gZnJvbSAnQC9hcGkvUFJPL2Jhc2ljLWluZm9ybWF0aW9uL3dvcmtzaG9wJw0KaW1wb3J0IHsNCiAgR2V0TW9jT3JkZXJJbmZvLA0KICBHZXRNb2NPcmRlclR5cGVMaXN0LA0KICBJbXBvcnRDaGFuZ0ZpbGUsDQogIFNhdmVNb2NPcmRlciwNCiAgU3VibWl0TW9jT3JkZXINCn0gZnJvbSAnQC9hcGkvUFJPL2NoYW5nZU1hbmFnZW1lbnQnDQppbXBvcnQgeyBHZXRDb21wYW55RGVwYXJ0VHJlZSwgR2V0T3NzVXJsIH0gZnJvbSAnQC9hcGkvc3lzJw0KaW1wb3J0IEltcG9ydEZpbGUgZnJvbSAnLi9jb21wb25lbnRzL2ltcG9ydEZpbGUudnVlJw0KaW1wb3J0IE9TU1VwbG9hZCBmcm9tICdAL3ZpZXdzL3BsbS9jb21wb25lbnRzL29zc3VwbG9hZC52dWUnDQppbXBvcnQgeyBjbG9zZVRhZ1ZpZXcsIGNvbWJpbmVVUkwsIGRlYm91bmNlLCBkZWVwQ2xvbmUgfSBmcm9tICdAL3V0aWxzJw0KaW1wb3J0IFN0YXR1c0RpYWxvZyBmcm9tICcuL2NvbXBvbmVudHMvZGlhbG9nLnZ1ZScNCmltcG9ydCBudW1lcmFsIGZyb20gJ251bWVyYWwnDQppbXBvcnQgeyBHZXRUYWJsZVNldHRpbmdMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL2NvbXBvbmVudC10eXBlJw0KaW1wb3J0IEhhbmRsZUVkaXQgZnJvbSAnLi9jb21wb25lbnRzL0hhbmRsZUVkaXQudnVlJw0KaW1wb3J0IEFkZEhhbmRsZSBmcm9tICcuL2NvbXBvbmVudHMvYWRkSGFuZGxlLnZ1ZScNCmltcG9ydCB7IGNoYW5nZVR5cGUsIGNoYW5nZVR5cGVSZXZlcnNlLCBnZXRBbGxDb2Rlc0J5VHlwZSB9IGZyb20gJy4vdXRpbHMnDQppbXBvcnQgeyBnZXRGaWxlTmFtZUZyb21VcmwgfSBmcm9tICdAL3V0aWxzL2ZpbGUnDQppbXBvcnQgeyBpc0FycmF5IH0gZnJvbSAnYWxpLW9zcy9saWIvY29tbW9uL3V0aWxzL2lzQXJyYXknDQppbXBvcnQgU3RlZWxDb21wb25lbnRNYW5hZ2VyIGZyb20gJ0Avdmlld3MvUFJPL2NoYW5nZS1tYW5hZ2VtZW50L2NvbnRhY3QtbGlzdC9pbmZvJw0KaW1wb3J0IHsgR2V0Q3VyRmFjdG9yeSB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Jw0KaW1wb3J0IHByb2Nlc3NIZWFkIGZyb20gJ0Avdmlld3MvUFJPL2NvbXBvbmVudHMvcHJvY2Vzc0hlYWQnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ1BST0VuZ2luZWVyaW5nQ2hhbmdlT3JkZXJBZGQnLA0KICBjb21wb25lbnRzOiB7DQogICAgT1NTVXBsb2FkLA0KICAgIFN0YXR1c0RpYWxvZywNCiAgICBJbXBvcnRGaWxlLA0KICAgIEhhbmRsZUVkaXQsDQogICAgQWRkSGFuZGxlLA0KICAgIHByb2Nlc3NIZWFkDQogIH0sDQogIG1peGluczogW1N0ZWVsQ29tcG9uZW50TWFuYWdlcl0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGZhY3RvcnlSZWZlcmVuY2VJZDogJycsDQogICAgICBjdXJTdGF0dXM6IHsNCiAgICAgICAgZGVsOiAn5bey5YigJywNCiAgICAgICAgY2hhbmdlOiAn5Y+Y5pu0JywNCiAgICAgICAgYWRkOiAn5paw5aKeJywNCiAgICAgICAgaW5jcmVhc2U6ICfmlbDph4/lop7liqAnLA0KICAgICAgICBkZWNyZWFzZTogJ+aVsOmHj+WHj+WwkScsDQogICAgICAgIHVuQ2hhbmdlOiAn5peg5Y+Y5pu0Jw0KICAgICAgfSwNCg0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICB0aXRsZTogJycsDQogICAgICB3aWR0aDogJzUwJScsDQogICAgICBjdXJyZW50Q29tcG9uZW50OiAnJywNCiAgICAgIGZpbGVQYXRoOiAnJywNCiAgICAgIGZpbmlzaEZlZTogMCwNCiAgICAgIHBhZ2VMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHNhdmVMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHN1Ym1pdExvYWRpbmc6IGZhbHNlLA0KICAgICAgdXBsb2FkTG9hZGluZzogZmFsc2UsDQogICAgICB0YkxvYWRpbmc6IGZhbHNlLA0KICAgICAgdGJEYXRhOiBbXSwNCiAgICAgIGFjdGl2aXRpZXM6IFtdLA0KICAgICAgZmlsZUxpc3Q6IFtdLA0KICAgICAgbXVsdGlwbGVTZWxlY3Rpb246IFtdLA0KICAgICAgY2hhbmdlUm93Q29udGVudExpc3Q6IFtdLA0KICAgICAgZmlsdGVyQ29kZU9wdGlvbnM6IFt7IGRhdGE6ICcnIH1dLA0KICAgICAgY29sdW1uczogW10sDQogICAgICBjaGFuZ2VNZXRob2Q6IDEsDQogICAgICBzZWFyY2hGb3JtOiB7DQogICAgICAgIGNvbXBvbmVudF9uYW1lOiAnJywNCiAgICAgICAgcGFydF9uYW1lOiAnJywNCiAgICAgICAgYXNzZW1ibHlfbmFtZTogJycsDQogICAgICAgIGNvbXBvbmVudF9zZWFyY2hfbW9kZTogMSwNCiAgICAgICAgcGFydF9zZWFyY2hfbW9kZTogMSwNCiAgICAgICAgYXNzZW1ibHlfc2VhcmNoX21vZGU6IDEsDQogICAgICAgIGNvbnRlbnQ6ICcnDQogICAgICB9LA0KICAgICAgZm9ybTogew0KICAgICAgICBTeXNfUHJvamVjdF9JZDogJycsDQogICAgICAgIEFyZWFfSWQ6ICcnLA0KICAgICAgICBJbnN0YWxsVW5pdF9JZHM6IFtdLA0KICAgICAgICBIYW5kbGVfVXNlcklkOiAnJywNCiAgICAgICAgTW9jX1R5cGVfSWQ6ICcnLA0KICAgICAgICBGZWU6IHVuZGVmaW5lZCwNCiAgICAgICAgSG91cnM6IHVuZGVmaW5lZCwNCiAgICAgICAgVXJnZW5jeTogMSwNCiAgICAgICAgRGVtYW5kX0RhdGU6ICcnLA0KICAgICAgICBGZWVfRGVwYXJ0SWQ6ICcnLA0KICAgICAgICBSZW1hcms6ICcnLA0KICAgICAgICBBdHRhY2htZW50TGlzdDogW10NCiAgICAgIH0sDQogICAgICBzaG93SW1wb3J0OiBmYWxzZSwNCiAgICAgIHRyZWVQYXJhbXM6IHsNCiAgICAgICAgZGF0YTogW10sDQogICAgICAgIGZpbHRlcmFibGU6IGZhbHNlLA0KICAgICAgICBjbGlja1BhcmVudDogdHJ1ZSwNCiAgICAgICAgcHJvcHM6IHsNCiAgICAgICAgICBkaXNhYmxlZDogJ2Rpc2FibGVkJywNCiAgICAgICAgICBjaGlsZHJlbjogJ0NoaWxkcmVuJywNCiAgICAgICAgICBsYWJlbDogJ0xhYmVsJywNCiAgICAgICAgICB2YWx1ZTogJ0lkJw0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgcGVvcGxlTGlzdDogW10sDQogICAgICBwcm9qZWN0TGlzdDogW10sDQogICAgICBjaGFuZ2VUeXBlTGlzdDogW10sDQogICAgICBpbnN0YWxsVW5pdExpc3Q6IFtdLA0KICAgICAgdHJlZVBhcmFtc0FyZWE6IHsNCiAgICAgICAgJ2RlZmF1bHQtZXhwYW5kLWFsbCc6IHRydWUsDQogICAgICAgIGZpbHRlcmFibGU6IGZhbHNlLA0KICAgICAgICBjbGlja1BhcmVudDogdHJ1ZSwNCiAgICAgICAgZGF0YTogW10sDQogICAgICAgIHByb3BzOiB7DQogICAgICAgICAgZGlzYWJsZWQ6ICdkaXNhYmxlZCcsDQogICAgICAgICAgY2hpbGRyZW46ICdDaGlsZHJlbicsDQogICAgICAgICAgbGFiZWw6ICdMYWJlbCcsDQogICAgICAgICAgdmFsdWU6ICdJZCcNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIFN5c19Qcm9qZWN0X0lkOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqScsIHRyaWdnZXI6ICdjaGFuZ2UnIH0NCiAgICAgICAgXSwNCiAgICAgICAgQXJlYV9JZDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6knLCB0cmlnZ2VyOiAnY2hhbmdlJyB9DQogICAgICAgIF0sDQogICAgICAgIEhhbmRsZV9Vc2VySWQ6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oupJywgdHJpZ2dlcjogJ2NoYW5nZScgfQ0KICAgICAgICBdLA0KICAgICAgICBNb2NfVHlwZV9JZDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6knLCB0cmlnZ2VyOiAnY2hhbmdlJyB9DQogICAgICAgIF0sDQogICAgICAgIEluc3RhbGxVbml0X0lkczogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6knLCB0cmlnZ2VyOiAnY2hhbmdlJyB9DQogICAgICAgIF0NCiAgICAgIH0sDQogICAgICB0b29sYmFyQnV0dG9uczogWw0KICAgICAgICB7IGNvZGU6ICdteVRvb2xiYXJFeHBvcnQnLCBuYW1lOiAn54K55Ye75a+85Ye6JyB9LA0KICAgICAgICB7IGNvZGU6ICdteVRvb2xiYXJMaW5rJywgbmFtZTogJ+eCueWHu+i3s+i9rCcgfSwNCiAgICAgICAgeyBjb2RlOiAnbXlUb29sYmFyQ3VzdG9tJywgbmFtZTogJ+aJk+W8gOiHquWumuS5ieWIlycgfQ0KICAgICAgXQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICBkaXNhYmxlU2F2ZSgpIHsNCiAgICAgIHJldHVybiB0aGlzLnN1Ym1pdExvYWRpbmcgfHwgdGhpcy51cGxvYWRMb2FkaW5nDQogICAgfSwNCiAgICBpc1ZpZXcoKSB7DQogICAgICByZXR1cm4gdGhpcy4kcm91dGUucXVlcnkudHlwZSA9PSAnMicNCiAgICB9LA0KICAgIGlzRWRpdCgpIHsNCiAgICAgIHJldHVybiB0aGlzLiRyb3V0ZS5xdWVyeS50eXBlID09ICcxJw0KICAgIH0sDQogICAgc2hvd0RldGFpbCgpIHsNCiAgICAgIGNvbnN0IHpidGsgPSB0aGlzLmNoYW5nZVR5cGVMaXN0LmZpbmQoaXRlbSA9PiB7DQogICAgICAgIHJldHVybiBpdGVtLklkID09PSB0aGlzLmZvcm0uTW9jX1R5cGVfSWQNCiAgICAgIH0pDQogICAgICByZXR1cm4gemJ0az8uSXNfRGVlcGVuX0NoYW5nZSB8fCBmYWxzZQ0KICAgIH0sDQogICAgZ2V0RmlsZU5hbWUoKSB7DQogICAgICByZXR1cm4gZ2V0RmlsZU5hbWVGcm9tVXJsKHRoaXMuZmlsZVBhdGgpDQogICAgfQ0KICB9LA0KICB3YXRjaDogew0KICAgICdmb3JtLkFyZWFfSWQnOiB7DQogICAgICBoYW5kbGVyKG5ld1ZhbCkgew0KICAgICAgICBpZiAoIW5ld1ZhbCkgew0KICAgICAgICAgIHRoaXMucnVsZXMuSW5zdGFsbFVuaXRfSWRzWzBdLnJlcXVpcmVkID0gZmFsc2UNCiAgICAgICAgICB0aGlzLiRyZWZzPy5pbnN0YWxsVW5pdFJlZj8uY2xlYXJWYWxpZGF0ZSgpDQogICAgICAgIH0NCiAgICAgIH0sDQogICAgICBpbW1lZGlhdGU6IHRydWUNCiAgICB9LA0KICAgICdmb3JtLkluc3RhbGxVbml0X0lkcyc6IHsNCiAgICAgIGhhbmRsZXIobmV3VmFsKSB7DQogICAgICAgIGlmICghdGhpcy5BcmVhX0lkKSByZXR1cm4NCiAgICAgICAgaWYgKHRoaXMuaW5zdGFsbFVuaXRMaXN0Lmxlbmd0aCkgew0KICAgICAgICAgIHRoaXMucnVsZXMuSW5zdGFsbFVuaXRfSWRzWzBdLnJlcXVpcmVkID0gdHJ1ZQ0KICAgICAgICAgIHRoaXMuJHJlZnMuaW5zdGFsbFVuaXRSZWYuY2xlYXJWYWxpZGF0ZSgpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5ydWxlcy5JbnN0YWxsVW5pdF9JZHNbMF0ucmVxdWlyZWQgPSBmYWxzZQ0KICAgICAgICAgIHRoaXMuJHJlZnMuaW5zdGFsbFVuaXRSZWYuY2xlYXJWYWxpZGF0ZSgpDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0sDQogIGFzeW5jIGNyZWF0ZWQoKSB7DQogICAgY29uc3QgUGVybWl0dGVkRmFjdG9yeUxpc3QgPSBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdQZXJtaXR0ZWRGYWN0b3J5TGlzdCcpKSB8fCBbXQ0KICAgIHRoaXMuZmFjdG9yeVJlZmVyZW5jZUlkID0gUGVybWl0dGVkRmFjdG9yeUxpc3QuZmluZChpdGVtID0+IGl0ZW0uSWQgPT09IHRoaXMuJHJvdXRlLnF1ZXJ5LmZhY3RvcnlJZCk/LlJlZmVyZW5jZV9JZCB8fCAnJw0KICAgIC8vIGF3YWl0IHRoaXMuZ2V0VGFibGVDb25maWcoJ1BST0VuZ0NoYW5nZU9yZGVyQWRkJykgUHJvZmVzc2lvbmFsQ29kZQ0KICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdjb250YWN0TGlzdC9yZXNldENoYW5nZUNvZGUnKQ0KICAgIHRoaXMuZ2V0UHJvamVjdERhdGEoKQ0KICAgIHRoaXMuZ2V0RmFjdG9yeVBlb3BsZSgpDQogICAgYXdhaXQgdGhpcy5nZXREZXBUcmVlKCkNCiAgICB0aGlzLmdldEZhY3RvcnlDaGFuZ2VUeXBlTGlzdCgpDQoNCiAgICBjb25zdCBpZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LmlkDQogICAgaWQgJiYgYXdhaXQgdGhpcy5nZXRJbmZvKGlkKQ0KICAgIHRoaXMuZ2V0VGFibGVDb25maWcoKQ0KICAgIC8vIHRoaXMuZ2V0VGFibGVJbmZvKCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGFzeW5jIGdldFRhYmxlQ29uZmlnKCkgew0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0VGFibGVTZXR0aW5nTGlzdCh7IFByb2Zlc3Npb25hbENvZGU6ICdTdGVlbCcgfSkNCiAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgIHRoaXMuYWxsQ29kZXMgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHJlcy5EYXRhKSkNCiAgICAgICAgLy8gRmlsdGVyIG91dCB0aGUgdGhyZWUgbmFtZSBjb2x1bW5zDQogICAgICAgIGNvbnN0IGZpbHRlcmVkQ29sdW1ucyA9IHJlcy5EYXRhLmZpbHRlcihpdGVtID0+DQogICAgICAgICAgIVsnU3RlZWxOYW1lJywgJ0NvbXBvbmVudE5hbWUnLCAnUGFydE5hbWUnXS5pbmNsdWRlcyhpdGVtLkNvZGUpDQogICAgICAgICkNCg0KICAgICAgICAvLyBBZGQgdGhlIG5ldyBDUENvZGUgY29sdW1uDQogICAgICAgIGNvbnN0IGNvZGVDb2x1bW4gPSB7DQogICAgICAgICAgQ29kZTogJ0NQQ29kZScsDQogICAgICAgICAgRGlzcGxheV9OYW1lOiAn5p6E5Lu2L+mDqOS7ti/pm7bku7blkI3np7AnLA0KICAgICAgICAgIElzX0Zyb3plbjogdHJ1ZSwNCiAgICAgICAgICBGcm96ZW5fRGlyY3Rpb246ICdsZWZ0JywNCiAgICAgICAgICBXaWR0aDogMTgwLA0KICAgICAgICAgIEFsaWduOiAnbGVmdCcNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIEluc2VydCB0aGUgQ1BDb2RlIGNvbHVtbiBhdCB0aGUgYmVnaW5uaW5nDQogICAgICAgIGZpbHRlcmVkQ29sdW1ucy51bnNoaWZ0KGNvZGVDb2x1bW4pDQogICAgICAgIGNvbnN0IF9jdXN0b21Db2x1bW5zID0gW3sNCiAgICAgICAgICBDb2RlOiAnUHJvZHVjdGlvbl9TdGF0dXMnLA0KICAgICAgICAgIERpc3BsYXlfTmFtZTogJ+eUn+S6p+aDheWGtScsDQogICAgICAgICAgQWxpZ246ICdjZW50ZXInLA0KICAgICAgICAgIElzX0Zyb3plbjogdHJ1ZSwNCiAgICAgICAgICBGcm96ZW5fRGlyY3Rpb246ICdyaWdodCcsDQogICAgICAgICAgV2lkdGg6IDEyMA0KICAgICAgICB9LCB7DQogICAgICAgICAgQ29kZTogJ2NoYW5nZUNvbnRlbnQnLA0KICAgICAgICAgIERpc3BsYXlfTmFtZTogJ+WPmOabtOWGheWuuScsDQogICAgICAgICAgQWxpZ246ICdjZW50ZXInLA0KICAgICAgICAgIElzX0Zyb3plbjogdHJ1ZSwNCiAgICAgICAgICBGcm96ZW5fRGlyY3Rpb246ICdyaWdodCcsDQogICAgICAgICAgV2lkdGg6IDEyMA0KICAgICAgICB9XQ0KDQogICAgICAgIGZpbHRlcmVkQ29sdW1ucy5wdXNoKC4uLl9jdXN0b21Db2x1bW5zKQ0KICAgICAgICBsZXQgX2NvbHVtbnMgPSBbXQ0KDQogICAgICAgIHRoaXMucm9vdENvbHVtbnMgPSBkZWVwQ2xvbmUoZmlsdGVyZWRDb2x1bW5zLm1hcChpdGVtID0+IHsNCiAgICAgICAgICBjb25zdCBkaXNwbGF5TmFtZUxlbmd0aCA9IGl0ZW0uRGlzcGxheV9OYW1lPy5sZW5ndGggfHwgMA0KICAgICAgICAgIGNvbnN0IHdpZHRoID0gTWF0aC5tYXgoMTIwLCAxMjAgKyBNYXRoLm1heCgwLCBkaXNwbGF5TmFtZUxlbmd0aCAtIDQpICogMTApDQogICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIC4uLml0ZW0sDQogICAgICAgICAgICBXaWR0aDogd2lkdGgsDQogICAgICAgICAgICBBbGlnbjogJ2NlbnRlcicNCiAgICAgICAgICB9DQogICAgICAgIH0pKQ0KDQogICAgICAgIGlmICh0aGlzLmNoYW5nZU1ldGhvZCA9PT0gMykgew0KICAgICAgICAgIGNvbnN0IGNvbHVtbkNvZGUgPSBbJ0NQQ29kZScsICdTZXR1cFBvc2l0aW9uJywgJ1Byb2R1Y3Rpb25fU3RhdHVzJywgJ2NoYW5nZUNvbnRlbnQnXQ0KICAgICAgICAgIF9jb2x1bW5zID0gdGhpcy5yb290Q29sdW1ucy5maWx0ZXIoaXRlbSA9PiBjb2x1bW5Db2RlLmluY2x1ZGVzKGl0ZW0uQ29kZSkpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgX2NvbHVtbnMgPSB0aGlzLnJvb3RDb2x1bW5zDQogICAgICAgIH0NCiAgICAgICAgdGhpcy5jb2x1bW5zID0gX2NvbHVtbnMNCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIGdldEluZm8oaWQpIHsNCiAgICAgIHRoaXMucGFnZUxvYWRpbmcgPSB0cnVlDQogICAgICBhd2FpdCBHZXRNb2NPcmRlckluZm8oew0KICAgICAgICBJZDogaWQsDQogICAgICAgIGZhY3RvcnlSZWZlcmVuY2VJZDogdGhpcy5mYWN0b3J5UmVmZXJlbmNlSWQNCiAgICAgIH0pLnRoZW4oYXN5bmMgcmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICBjb25zdCB7DQogICAgICAgICAgICBEZWVwZW5fRmlsZV9VcmwsDQogICAgICAgICAgICBTeXNfUHJvamVjdF9JZCwNCiAgICAgICAgICAgIEFyZWFfSWQsDQogICAgICAgICAgICBJbnN0YWxsVW5pdF9JZHMsDQogICAgICAgICAgICBIYW5kbGVfVXNlcklkLA0KICAgICAgICAgICAgTW9jX1R5cGVfSWQsDQogICAgICAgICAgICBDaGFuZ2VfVHlwZSwNCiAgICAgICAgICAgIEZlZSwNCiAgICAgICAgICAgIEhvdXJzLA0KICAgICAgICAgICAgVXJnZW5jeSwNCiAgICAgICAgICAgIERlbWFuZF9EYXRlLA0KICAgICAgICAgICAgRmVlX0RlcGFydElkLA0KICAgICAgICAgICAgUmVtYXJrLA0KICAgICAgICAgICAgRmVlSGlzdG9yeSwNCiAgICAgICAgICAgIElkLA0KICAgICAgICAgICAgU3RhdHVzLA0KICAgICAgICAgICAgQXR0YWNobWVudExpc3QsDQogICAgICAgICAgICBPcmRlckRldGFpbA0KICAgICAgICAgIH0gPSByZXMuRGF0YQ0KICAgICAgICAgIHRoaXMuYWN0aXZpdGllcyA9IEZlZUhpc3RvcnkNCiAgICAgICAgICBpZiAoRmVlSGlzdG9yeS5sZW5ndGgpIHsNCiAgICAgICAgICAgIGNvbnN0IFtsYXN0XSA9IEZlZUhpc3Rvcnkuc2xpY2UoLTEpDQogICAgICAgICAgICB0aGlzLmZpbmlzaEZlZSA9IG51bWVyYWwobGFzdD8uRmVlIHx8IDApLmZvcm1hdCgnMC5bMDBdJykNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKFN0YXR1cyA9PT0gMykgew0KICAgICAgICAgICAgY29uc3QgaWR4ID0gdGhpcy5jb2x1bW5zLmZpbmRJbmRleChpdGVtID0+IGl0ZW0uQ29kZSA9PT0gJ1Byb2R1Y3Rpb25fU3RhdHVzJykNCiAgICAgICAgICAgIGlmIChpZHggIT09IC0xKSB7DQogICAgICAgICAgICAgIHRoaXMuY29sdW1ucy5zcGxpY2UoaWR4LCAxKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGlmIChBdHRhY2htZW50TGlzdD8ubGVuZ3RoKSB7DQogICAgICAgICAgICBBdHRhY2htZW50TGlzdC5mb3JFYWNoKChlbGVtZW50LCBpZHgpID0+IHsNCiAgICAgICAgICAgICAgY29uc3Qgb2JqID0gew0KICAgICAgICAgICAgICAgIG5hbWU6IGVsZW1lbnQuRmlsZV9OYW1lLA0KICAgICAgICAgICAgICAgIHVybDogZWxlbWVudC5GaWxlX1VybA0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIHRoaXMuZmlsZUxpc3QucHVzaChvYmopDQogICAgICAgICAgICAgIHRoaXMuZm9ybS5BdHRhY2htZW50TGlzdC5wdXNoKHsNCiAgICAgICAgICAgICAgICBGaWxlX1VybDogZWxlbWVudC5GaWxlX1VybCwNCiAgICAgICAgICAgICAgICBGaWxlX05hbWU6IGVsZW1lbnQuRmlsZV9OYW1lDQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgICBhd2FpdCB0aGlzLmdldEFyZWFMaXN0KFN5c19Qcm9qZWN0X0lkKQ0KICAgICAgICAgIGF3YWl0IHRoaXMuZ2V0SW5zdGFsbFVuaXRQYWdlTGlzdChBcmVhX0lkKQ0KDQogICAgICAgICAgT2JqZWN0LmFzc2lnbih0aGlzLmZvcm0sIHsNCiAgICAgICAgICAgIC4uLnJlcy5EYXRhLA0KICAgICAgICAgICAgU3lzX1Byb2plY3RfSWQsDQogICAgICAgICAgICBBcmVhX0lkLA0KICAgICAgICAgICAgRGVlcGVuX0ZpbGVfVXJsLA0KICAgICAgICAgICAgSWQsDQogICAgICAgICAgICBJbnN0YWxsVW5pdF9JZHM6IEluc3RhbGxVbml0X0lkcyA/ICh0eXBlb2YgSW5zdGFsbFVuaXRfSWRzID09PSAnc3RyaW5nJyA/IEluc3RhbGxVbml0X0lkcy5zcGxpdCgnLCcpIDogSW5zdGFsbFVuaXRfSWRzKSA6IFtdLA0KICAgICAgICAgICAgSGFuZGxlX1VzZXJJZCwNCiAgICAgICAgICAgIE1vY19UeXBlX0lkLA0KICAgICAgICAgICAgRmVlOiBGZWUgfHwgdW5kZWZpbmVkLA0KICAgICAgICAgICAgSG91cjogSG91cnMgfHwgdW5kZWZpbmVkLA0KICAgICAgICAgICAgVXJnZW5jeTogTnVtYmVyKFVyZ2VuY3kpLA0KICAgICAgICAgICAgRGVtYW5kX0RhdGUsDQogICAgICAgICAgICBGZWVfRGVwYXJ0SWQsDQogICAgICAgICAgICBSZW1hcmsNCiAgICAgICAgICB9KQ0KDQogICAgICAgICAgdGhpcy5zZXRUYkRhdGEoT3JkZXJEZXRhaWwpDQogICAgICAgICAgdGhpcy5maWxlUGF0aCA9IERlZXBlbl9GaWxlX1VybA0KICAgICAgICAgIHRoaXMuY2hhbmdlTWV0aG9kID0gQ2hhbmdlX1R5cGUgPT09IDAgPyAxIDogQ2hhbmdlX1R5cGUgPT09IDEgPyAyIDogMw0KICAgICAgICAgIC8vIERlZXBlbl9GaWxlX1VybA0KICAgICAgICAgIC8vIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgIC8vIERlZXBlbl9GaWxlX1VybCAmJiB0aGlzLmdldFRhYmxlSW5mbyh7IEZpbGVfVXJsOiBEZWVwZW5fRmlsZV9VcmwgfSkNCiAgICAgICAgICAvLyB9LCAwKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLnBhZ2VMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICBtb2NUeXBlQ2hhbmdlKCkgew0KICAgICAgdGhpcy50YkRhdGEgPSBbXQ0KICAgICAgdGhpcy5oYW5kbGVSZXNldCgpDQogICAgfSwNCg0KICAgIGhhbmRsZUV4Y2VlZChmaWxlcywgZmlsZUxpc3QpIHsNCiAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZyhg5b2T5YmN6ZmQ5Yi26YCJ5oupIDUg5Liq5paH5Lu277yM5pys5qyh6YCJ5oup5LqGICR7ZmlsZXMubGVuZ3RofSDkuKrmlofku7bvvIzlhbHpgInmi6nkuoYgJHtmaWxlcy5sZW5ndGggKyBmaWxlTGlzdC5sZW5ndGh9IOS4quaWh+S7tmApDQogICAgfSwNCiAgICBoYW5kbGVQcm9ncmVzcyhldmVudCwgZmlsZXMsIGZpbGVMaXN0KSB7DQogICAgfSwNCiAgICBoYW5kbGVFcnJvcihlcnIsIGZpbGVzLCBmaWxlTGlzdCkgew0KICAgICAgY29uc29sZS5sb2coJ2VycjMnLCBlcnIsIGZpbGVzLCBmaWxlTGlzdCkNCiAgICAgIHRoaXMuY2hlY2tVcGxvYWRpbmcoZmlsZUxpc3QpDQogICAgfSwNCiAgICBjaGVja1VwbG9hZGluZyhmaWxlTGlzdCkgew0KICAgICAgY29uc3QgZmxhZyA9IGZpbGVMaXN0LmV2ZXJ5KHYgPT4gdi5zdGF0dXMgPT09ICdzdWNjZXNzJykNCiAgICAgIGZsYWcgJiYgKHRoaXMudXBsb2FkTG9hZGluZyA9IGZhbHNlKQ0KICAgIH0sDQogICAgaGFuZGxlSW1wb3J0KCkgew0KICAgICAgdGhpcy4kcmVmc1snZm9ybSddLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBjb25zb2xlLmxvZygndmFsaWQnLCB2YWxpZCkNCiAgICAgICAgICB0aGlzLiRyZWZzWydkaWFsb2cnXS5oYW5kbGVPcGVuKCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLmxvZygnZXJyb3Igc3VibWl0ISEnKQ0KICAgICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy4kcmVmc1snZm9ybSddLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICB0aGlzLnRpdGxlID0gJ+a3u+WKoOWPmOabtOWGheWuuScNCiAgICAgICAgICB0aGlzLndpZHRoID0gJzcwJScNCiAgICAgICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSAnYWRkSGFuZGxlJw0KICAgICAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgICAgICB0aGlzLiRuZXh0VGljayhfID0+IHsNCiAgICAgICAgICAgIHRoaXMuJHJlZnNbJ2NvbnRlbnQnXS5oYW5kbGVPcGVuKHRoaXMuZm9ybSwgdGhpcy50YkRhdGEpDQogICAgICAgICAgfSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLmxvZygnZXJyb3Igc3VibWl0ISEnKQ0KICAgICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlT3Blbihyb3cpIHsNCiAgICAgIHRoaXMuJHJlZnNbJ3N0YXR1c0RpYWxvZyddLmhhbmRsZU9wZW4ocm93KQ0KICAgIH0sDQogICAgZ2V0UHJvamVjdERhdGEoKSB7DQogICAgICBHZXRQcm9qZWN0UGFnZUxpc3QoeyBQYWdlU2l6ZTogLTEsIGZhY3RvcnlSZWZlcmVuY2VJZDogdGhpcy5mYWN0b3J5UmVmZXJlbmNlSWQgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy5wcm9qZWN0TGlzdCA9IHJlcy5EYXRhLkRhdGENCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHNldERpc2FibGVkVHJlZShyb290KSB7DQogICAgICBpZiAoIXJvb3QpIHJldHVybg0KDQogICAgICByb290LmZvckVhY2goKGVsZW1lbnQpID0+IHsNCiAgICAgICAgY29uc3QgeyBDaGlsZHJlbiB9ID0gZWxlbWVudA0KICAgICAgICBpZiAoQ2hpbGRyZW4gJiYgQ2hpbGRyZW4ubGVuZ3RoKSB7DQogICAgICAgICAgZWxlbWVudC5kaXNhYmxlZCA9IHRydWUNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBlbGVtZW50LmRpc2FibGVkID0gZmFsc2UNCiAgICAgICAgICB0aGlzLnNldERpc2FibGVkVHJlZShDaGlsZHJlbikNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFzeW5jIGdldEFyZWFMaXN0KFBpZCkgew0KICAgICAgYXdhaXQgR2VBcmVhVHJlZXMoew0KICAgICAgICBzeXNQcm9qZWN0SWQ6IFBpZCwNCiAgICAgICAgZmFjdG9yeVJlZmVyZW5jZUlkOiB0aGlzLmZhY3RvcnlSZWZlcmVuY2VJZA0KICAgICAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIGNvbnN0IHRyZWUgPSByZXMuRGF0YQ0KICAgICAgICAgIHRoaXMuc2V0RGlzYWJsZWRUcmVlKHRyZWUpDQogICAgICAgICAgdGhpcy50cmVlUGFyYW1zQXJlYS5kYXRhID0gcmVzLkRhdGENCiAgICAgICAgICB0aGlzLiRuZXh0VGljayhfID0+IHsNCiAgICAgICAgICAgIHRoaXMuJHJlZnMudHJlZVNlbGVjdEFyZWEudHJlZURhdGFVcGRhdGVGdW4ocmVzLkRhdGEpDQogICAgICAgICAgfSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBhc3luYyBnZXRJbnN0YWxsVW5pdFBhZ2VMaXN0KGFyZWFJZCkgew0KICAgICAgYXdhaXQgR2V0SW5zdGFsbFVuaXRJZE5hbWVMaXN0KHsNCiAgICAgICAgQXJlYV9JZDogYXJlYUlkLA0KICAgICAgICBQYWdlOiAxLA0KICAgICAgICBQYWdlU2l6ZTogLTENCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMuaW5zdGFsbFVuaXRMaXN0ID0gcmVzLkRhdGENCiAgICAgICAgICBpZiAodGhpcy5pbnN0YWxsVW5pdExpc3QubGVuZ3RoKSB7DQogICAgICAgICAgICB0aGlzLnJ1bGVzLkluc3RhbGxVbml0X0lkc1swXS5yZXF1aXJlZCA9IHRydWUNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy5ydWxlcy5JbnN0YWxsVW5pdF9JZHNbMF0ucmVxdWlyZWQgPSBmYWxzZQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBhc3luYyBwcm9qZWN0Q2hhbmdlKCkgew0KICAgICAgY29uc3QgU3lzX1Byb2plY3RfSWQgPSB0aGlzLmZvcm0uU3lzX1Byb2plY3RfSWQNCiAgICAgIHRoaXMuY2xlYXJUYigpDQogICAgICB0aGlzLmZvcm0uQXJlYV9JZCA9ICcnDQogICAgICB0aGlzLmZvcm0uSW5zdGFsbFVuaXRfSWRzID0gW10NCiAgICAgIHRoaXMudHJlZVBhcmFtc0FyZWEuZGF0YSA9IFtdDQogICAgICB0aGlzLiRuZXh0VGljayhfID0+IHsNCiAgICAgICAgdGhpcy4kcmVmcy50cmVlU2VsZWN0QXJlYS50cmVlRGF0YVVwZGF0ZUZ1bihbXSkNCiAgICAgIH0pDQogICAgICBpZiAoU3lzX1Byb2plY3RfSWQpIHsNCiAgICAgICAgYXdhaXQgdGhpcy5nZXRBcmVhTGlzdChTeXNfUHJvamVjdF9JZCkNCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIGFyZWFDaGFuZ2UoKSB7DQogICAgICB0aGlzLmNsZWFyVGIoKQ0KICAgICAgYXdhaXQgdGhpcy5nZXRJbnN0YWxsVW5pdFBhZ2VMaXN0KHRoaXMuZm9ybS5BcmVhX0lkKQ0KICAgICAgaWYgKHRoaXMuaW5zdGFsbFVuaXRMaXN0Lmxlbmd0aCAmJiB0aGlzLmZvcm0uQXJlYV9JZCkgew0KICAgICAgICB0aGlzLmZvcm0uSW5zdGFsbFVuaXRfSWRzID0gW3RoaXMuaW5zdGFsbFVuaXRMaXN0WzBdLklkXQ0KICAgICAgICB0aGlzLnJ1bGVzLkluc3RhbGxVbml0X0lkc1swXS5yZXF1aXJlZCA9IHRydWUNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMucnVsZXMuSW5zdGFsbFVuaXRfSWRzWzBdLnJlcXVpcmVkID0gZmFsc2UNCiAgICAgICAgdGhpcy4kcmVmcy5pbnN0YWxsVW5pdFJlZi5jbGVhclZhbGlkYXRlKCkNCiAgICAgIH0NCiAgICB9LA0KICAgIGFyZWFDbGVhcigpIHsNCiAgICAgIHRoaXMuZm9ybS5BcmVhX0lkID0gJycNCiAgICAgIHRoaXMuZm9ybS5JbnN0YWxsVW5pdF9JZHMgPSBbXQ0KICAgICAgdGhpcy5jbGVhclRiKCkNCiAgICB9LA0KICAgIGhhbmRsZUNsb3NlKCkgew0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICB9LA0KICAgIGdldEZhY3RvcnlQZW9wbGUoKSB7DQogICAgICBHZXRGYWN0b3J5UGVvcGxlbGlzdCh7IGZhY3RvcnlSZWZlcmVuY2VJZDogdGhpcy5mYWN0b3J5UmVmZXJlbmNlSWQgfSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMucGVvcGxlTGlzdCA9IE9iamVjdC5mcmVlemUocmVzLkRhdGEgfHwgW10pDQoNCiAgICAgICAgICBjb25zdCBjdXJJZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdVc2VySWQnKQ0KICAgICAgICAgIGNvbnN0IGN1ciA9IHRoaXMucGVvcGxlTGlzdC5maW5kKHYgPT4gdi5JZCA9PT0gY3VySWQpDQogICAgICAgICAgaWYgKGN1cikgew0KICAgICAgICAgICAgdGhpcy5mb3JtLkhhbmRsZV9Vc2VySWQgPSBjdXIuSWQNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgZ2V0RmFjdG9yeUNoYW5nZVR5cGVMaXN0KCkgew0KICAgICAgR2V0TW9jT3JkZXJUeXBlTGlzdCh7IGZhY3RvcnlSZWZlcmVuY2VJZDogdGhpcy5mYWN0b3J5UmVmZXJlbmNlSWQgfSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMuY2hhbmdlVHlwZUxpc3QgPSByZXMuRGF0YQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFzeW5jIGdldERlcFRyZWUoKSB7DQogICAgICBjb25zdCBnZXRGYWN0b3J5RGVwdElkID0gYXN5bmMoKSA9PiB7DQogICAgICAgIHJldHVybiBhd2FpdCBHZXRDdXJGYWN0b3J5KHsgZmFjdG9yeVJlZmVyZW5jZUlkOiB0aGlzLmZhY3RvcnlSZWZlcmVuY2VJZCB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgcmV0dXJuIHJlcz8uRGF0YVswXT8uRGVwdF9JZA0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfQ0KDQogICAgICBjb25zdCBnZXREZXB0ID0gYXN5bmMoZGVwSWQpID0+IHsNCiAgICAgICAgYXdhaXQgR2V0Q29tcGFueURlcGFydFRyZWUoe30pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgY29uc3Qgb3JpZ2luID0gcmVzLkRhdGE/LlswXQ0KICAgICAgICAgICAgaWYgKG9yaWdpbi5DaGlsZHJlbi5sZW5ndGgpIHsNCiAgICAgICAgICAgICAgY29uc3QgdHJlZSA9IG9yaWdpbi5DaGlsZHJlbi5maWx0ZXIodiA9PiB2LklkID09PSBkZXBJZCkNCg0KICAgICAgICAgICAgICBjb25zdCBkaXNhYmxlRGlyZWN0b3J5ID0gKHRyZWVBcnJheSkgPT4gew0KICAgICAgICAgICAgICAgIHRyZWVBcnJheS5tYXAoZWxlbWVudCA9PiB7DQogICAgICAgICAgICAgICAgICBpZiAoZWxlbWVudC5DaGlsZHJlbiAmJiBlbGVtZW50LkNoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgICAgICAgZWxlbWVudC5kaXNhYmxlZCA9IHRydWUNCiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZURpcmVjdG9yeShlbGVtZW50LkNoaWxkcmVuKQ0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgZGlzYWJsZURpcmVjdG9yeSh0cmVlKQ0KICAgICAgICAgICAgICB0aGlzLiRyZWZzLnRyZWVTZWxlY3QudHJlZURhdGFVcGRhdGVGdW4odHJlZSB8fCBbXSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICAgIGNvbnN0IGRlcElkID0gYXdhaXQgZ2V0RmFjdG9yeURlcHRJZCgpDQogICAgICBhd2FpdCBnZXREZXB0KGRlcElkKQ0KICAgIH0sDQogICAgZ2V0VGFibGVJbmZvKGZpbGVPYmopIHsNCiAgICAgIHRoaXMudGJMb2FkaW5nID0gdHJ1ZQ0KICAgICAgY29uc3QgZm9ybSA9IHsgLi4udGhpcy5mb3JtIH0NCiAgICAgIEltcG9ydENoYW5nRmlsZSh7DQogICAgICAgIC4uLmZvcm0sDQogICAgICAgIEltcG9ydFR5cGU6IHRoaXMuY2hhbmdlTWV0aG9kLA0KICAgICAgICBJbnN0YWxsVW5pdF9JZHM6IHRoaXMuZm9ybS5JbnN0YWxsVW5pdF9JZHMudG9TdHJpbmcoKSwNCiAgICAgICAgQXR0YWNobWVudExpc3Q6IFtmaWxlT2JqXQ0KICAgICAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMuaXNJbXBvcnRGaWxlID0gdHJ1ZQ0KICAgICAgICAgIGNvbnN0IHsgQXR0YWNobWVudExpc3QsIE1vY09yZGVyRGV0YWlsTGlzdCB9ID0gcmVzLkRhdGENCiAgICAgICAgICAvLyB0aGlzLmdldFRiTGlzdChPcmlnbmFsX0RlZXBlbl9MaXN0LCBJbXBvcnRfRGVlcGVuX0xpc3QpDQogICAgICAgICAgLy8gZmlsZVBhdGgNCiAgICAgICAgICBpZiAoQXR0YWNobWVudExpc3QubGVuZ3RoKSB7DQogICAgICAgICAgICB0aGlzLmZpbGVQYXRoID0gQXR0YWNobWVudExpc3RbMF0uRmlsZV9VcmwNCiAgICAgICAgICAgIHRoaXMuZm9ybS5EZWVwZW5fRmlsZV9VcmwgPSB0aGlzLmZpbGVQYXRoDQogICAgICAgICAgICB0aGlzLmZvcm0uRGVlcGVuX0ZpbGVfVXJsX0xpc3QgPSBbZmlsZU9ial0NCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy5zZXRUYkRhdGEoTW9jT3JkZXJEZXRhaWxMaXN0KQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGlmIChyZXMuRGF0YSAmJiByZXMuRGF0YS5FcnJvckZpbGVVcmwpIHsNCiAgICAgICAgICAgIHdpbmRvdy5vcGVuKGNvbWJpbmVVUkwodGhpcy4kYmFzZVVybCwgcmVzLkRhdGEuRXJyb3JGaWxlVXJsKSwgJ19ibGFuaycpDQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLnRiTG9hZGluZyA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQogICAgc2V0QWxsV2VpZ2h0KHJvdykgew0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgU3RlZWxBbGxXZWlnaHQ6IG51bWVyYWwocm93LlN0ZWVsV2VpZ2h0KS5tdWx0aXBseShyb3cuU3RlZWxBbW91bnQpLmZvcm1hdCgnMC5bMDAwXScpDQogICAgICB9DQogICAgfSwNCiAgICBzZXRUYkRhdGEobGlzdCkgew0KICAgICAgY29uc3Qgc2V0SXRlbSA9IChsaXN0LCBpdGVtLCBpc0FmdGVyVmFsdWUgPSBmYWxzZSkgPT4gew0KICAgICAgICBjb25zdCB1cGRhdGVkSXRlbSA9IHsNCiAgICAgICAgICAuLi5pdGVtLA0KICAgICAgICAgIENvZGVUeXBlOiBpdGVtLlR5cGUgPT09IDAgPyAxIDogaXRlbS5UeXBlID09PSAxID8gMiA6IDMsDQogICAgICAgICAgcGFyZW50Q2hpbGRyZW5JZDogaXRlbS5JZCwNCiAgICAgICAgICB1dWlkOiBpdGVtLk1vY0lkQmVmb3JlLA0KICAgICAgICAgIGNoZWNrZWQ6IGZhbHNlLA0KICAgICAgICAgIGNoYW5nZUNvbnRlbnQ6IGl0ZW0uTW9jVHlwZSwNCiAgICAgICAgICBjaGFuZ2VUeXBlOiBjaGFuZ2VUeXBlUmV2ZXJzZVtpdGVtLk1vY1R5cGVdLA0KICAgICAgICAgIC4uLnRoaXMuc2V0QWxsV2VpZ2h0KGl0ZW0pDQogICAgICAgIH0NCiAgICAgICAgdGhpcy5zZXRJdGVtTW9jQ29udGVudCh1cGRhdGVkSXRlbSwgaXNBZnRlclZhbHVlKQ0KICAgICAgICBpZiAodXBkYXRlZEl0ZW0uY2hhbmdlVHlwZSA9PT0gJ2lzRGVsZXRlJykgew0KICAgICAgICAgIGNvbnN0IGNoaWxkcmVuSXRlbXMgPSB0aGlzLmZpbmRDaGlsZEl0ZW1zKHVwZGF0ZWRJdGVtLCBsaXN0KQ0KICAgICAgICAgIGlmIChjaGlsZHJlbkl0ZW1zLmxlbmd0aCkgew0KICAgICAgICAgICAgY2hpbGRyZW5JdGVtcy5mb3JFYWNoKGNoaWxkSXRlbSA9PiB7DQogICAgICAgICAgICAgIGNoaWxkSXRlbS5pc0Rpc2FibGVkID0gdHJ1ZQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgcmV0dXJuIHVwZGF0ZWRJdGVtDQogICAgICB9DQoNCiAgICAgIHRoaXMuZGVmYXVsdFRiRGF0YSA9IGxpc3QubWFwKGl0ZW0gPT4gc2V0SXRlbShsaXN0LCBpdGVtKSkNCg0KICAgICAgdGhpcy50YkRhdGEgPSBsaXN0Lm1hcChpdGVtID0+IHNldEl0ZW0obGlzdCwgaXRlbSwgdHJ1ZSkpDQogICAgfSwNCiAgICBzZXRJdGVtTW9jQ29udGVudChpdGVtLCBpc0FmdGVyVmFsdWUgPSBmYWxzZSkgew0KICAgICAgaWYgKGl0ZW0uTW9jQ29udGVudCkgew0KICAgICAgICBsZXQgX01vY0NvbnRlbnQgPSBKU09OLnBhcnNlKGl0ZW0uTW9jQ29udGVudCkNCiAgICAgICAgaWYgKGlzQXJyYXkoX01vY0NvbnRlbnQpICYmIF9Nb2NDb250ZW50Lmxlbmd0aCkgew0KICAgICAgICAgIF9Nb2NDb250ZW50ID0gX01vY0NvbnRlbnQuZmlsdGVyKG0gPT4gbS5DaGFuZ2VGaWVsZENvZGUgIT09ICdQYXJ0TnVtJykNCiAgICAgICAgICBjb25zdCBfbGlzdCA9IF9Nb2NDb250ZW50Lm1hcChtID0+IHsNCiAgICAgICAgICAgIGNvbnN0IF9jb2RlcyA9IGdldEFsbENvZGVzQnlUeXBlKGl0ZW0uQ29kZVR5cGUpDQogICAgICAgICAgICBjb25zdCBjdXIgPSBfY29kZXMuZmluZCh2ID0+IHYuQ29kZSA9PT0gbS5DaGFuZ2VGaWVsZENvZGUpDQogICAgICAgICAgICBpdGVtW20uQ2hhbmdlRmllbGRDb2RlXSA9IGlzQWZ0ZXJWYWx1ZSA/IG0uQWZ0ZXJWYWx1ZSA6IG0uQmVmb3JlVmFsdWUNCg0KICAgICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgICAgRmllbGRfVHlwZTogY3VyPy5GaWVsZF9UeXBlIHx8ICdzdHJpbmcnLA0KICAgICAgICAgICAgICBJc0NvcmVGaWVsZDogY3VyPy5Jc0NvcmVGaWVsZCB8fCBmYWxzZSwNCiAgICAgICAgICAgICAgQ29kZTogbS5DaGFuZ2VGaWVsZENvZGUsDQogICAgICAgICAgICAgIE5hbWU6IG0uQ2hhbmdlRmllbGROYW1lLA0KICAgICAgICAgICAgICBWYWx1ZTogbS5CZWZvcmVWYWx1ZSwNCiAgICAgICAgICAgICAgTmV3VmFsdWU6IG0uQWZ0ZXJWYWx1ZQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2NvbnRhY3RMaXN0L2FkZENoYW5nZUNvZGUnLCB7IHV1aWQ6IGl0ZW0udXVpZCwgbGlzdDogX2xpc3QgfSkNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgYmVmb3JlVXBsb2FkKGZpbGUpIHsNCiAgICAgIHRoaXMudXBsb2FkTG9hZGluZyA9IHRydWUNCiAgICB9LA0KICAgIGhhbmRsZUNoYW5nZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy5maWxlTGlzdCA9IGZpbGVMaXN0DQogICAgfSwNCiAgICBhc3luYyBoYW5kbGVQcmV2aWV3KGZpbGUpIHsNCiAgICAgIGNvbnNvbGUubG9nKGZpbGUpDQogICAgICBjb25zdCBhcnIgPSBmaWxlLm5hbWUuc3BsaXQoJy4nKQ0KICAgICAgY29uc3QgaXNEd2cgPSBhcnJbYXJyLmxlbmd0aCAtIDFdID09PSAnZHdnJw0KICAgICAgY29uc3QgeyBEYXRhIH0gPSBhd2FpdCBHZXRPc3NVcmwoeyB1cmw6IGZpbGUudXJsIH0pDQogICAgICBpZiAoaXNEd2cpIHsNCiAgICAgICAgd2luZG93Lm9wZW4oJ2h0dHA6Ly9kd2d2MS5iaW10ay5jb206NTQzMi8/Q2FkVXJsPScgKyBEYXRhLCAnX2JsYW5rJykNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHdpbmRvdy5vcGVuKERhdGEpDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVSZW1vdmUoZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIHRoaXMuZmlsZUxpc3QgPSBmaWxlTGlzdA0KICAgICAgdGhpcy5jaGVja1VwbG9hZGluZyhmaWxlTGlzdCkNCiAgICAgIHRoaXMuZm9ybS5BdHRhY2htZW50TGlzdCA9IHRoaXMuZmlsZUxpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICBpZiAoaXRlbS51cmwpIHsNCiAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgRmlsZV9Vcmw6IGl0ZW0udXJsLA0KICAgICAgICAgICAgRmlsZV9OYW1lOiBpdGVtLm5hbWUNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc3QgdXJsID0gaXRlbS5yZXNwb25zZS5EYXRhDQogICAgICAgICAgY29uc3QgZmlsZUluZm8gPSB1cmwuc3BsaXQoJyonKQ0KICAgICAgICAgIGNvbnN0IGZpbGVPYmogPSB7DQogICAgICAgICAgICBGaWxlX1VybDogZmlsZUluZm9bMF0sDQogICAgICAgICAgICBGaWxlX1NpemU6IGZpbGVJbmZvWzFdLA0KICAgICAgICAgICAgRmlsZV9UeXBlOiBmaWxlSW5mb1syXSwNCiAgICAgICAgICAgIEZpbGVfTmFtZTogZmlsZUluZm9bM10NCiAgICAgICAgICB9DQogICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIEZpbGVfVXJsOiBmaWxlT2JqLkZpbGVfVXJsLA0KICAgICAgICAgICAgRmlsZV9OYW1lOiBmaWxlT2JqLkZpbGVfTmFtZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHVwbG9hZFN1Y2Nlc3MocmVzcG9uc2UsIGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICBpZiAoIXJlc3BvbnNlIHx8ICFyZXNwb25zZS5EYXRhKSB7DQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgdGhpcy5jaGVja1VwbG9hZGluZyhmaWxlTGlzdCkNCiAgICAgIHRoaXMuZm9ybS5BdHRhY2htZW50TGlzdCA9IHRoaXMuZmlsZUxpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICBpZiAoaXRlbS51cmwpIHsNCiAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgRmlsZV9Vcmw6IGl0ZW0udXJsLA0KICAgICAgICAgICAgRmlsZV9OYW1lOiBpdGVtLm5hbWUNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgaWYgKGl0ZW0uc3RhdHVzICE9PSAnc3VjY2VzcycpIHJldHVybg0KICAgICAgICAgIGNvbnN0IHVybCA9IGl0ZW0ucmVzcG9uc2UuRGF0YQ0KICAgICAgICAgIGNvbnN0IGZpbGVJbmZvID0gdXJsLnNwbGl0KCcqJykNCiAgICAgICAgICBjb25zdCBmaWxlT2JqID0gew0KICAgICAgICAgICAgRmlsZV9Vcmw6IGZpbGVJbmZvWzBdLA0KICAgICAgICAgICAgRmlsZV9TaXplOiBmaWxlSW5mb1sxXSwNCiAgICAgICAgICAgIEZpbGVfVHlwZTogZmlsZUluZm9bMl0sDQogICAgICAgICAgICBGaWxlX05hbWU6IGZpbGVJbmZvWzNdDQogICAgICAgICAgfQ0KICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICBGaWxlX1VybDogZmlsZU9iai5GaWxlX1VybCwNCiAgICAgICAgICAgIEZpbGVfTmFtZTogZmlsZU9iai5GaWxlX05hbWUNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIGhhbmRsZVNhdmUoKSB7DQogICAgICB0aGlzLiRyZWZzWydmb3JtJ10udmFsaWRhdGUoYXN5bmModmFsaWQpID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgdGhpcy5zYXZlTG9hZGluZyA9IHRydWUNCiAgICAgICAgICBhd2FpdCB0aGlzLnN1Ym1pdCh0cnVlKQ0KICAgICAgICAgIHRoaXMuc2F2ZUxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUubG9nKCdlcnJvciBzdWJtaXQhIScpDQogICAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBhc3luYyBoYW5kbGVTdWJtaXQoKSB7DQogICAgICB0aGlzLiRyZWZzWydmb3JtJ10udmFsaWRhdGUoYXN5bmModmFsaWQpID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgdGhpcy5zdWJtaXRMb2FkaW5nID0gdHJ1ZQ0KICAgICAgICAgIGF3YWl0IHRoaXMuc3VibWl0KGZhbHNlKQ0KICAgICAgICAgIHRoaXMuc3VibWl0TG9hZGluZyA9IGZhbHNlDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS5sb2coJ2Vycm9yIHN1Ym1pdCEhJykNCiAgICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFzeW5jIHN1Ym1pdChpc0RyYWZ0KSB7DQogICAgICBjb25zb2xlLmxvZygndGhpcy5mb3JtJywgdGhpcy5mb3JtKQ0KICAgICAgY29uc3QgX2Zvcm0gPSB7IC4uLnRoaXMuZm9ybSB9DQogICAgICBsZXQgc3VibWl0VGIgPSBbXQ0KICAgICAgaWYgKHRoaXMuY2hhbmdlTWV0aG9kID09PSAzKSB7DQogICAgICAgIHN1Ym1pdFRiID0gdGhpcy50YkRhdGEubWFwKChpdGVtKSA9PiB7DQogICAgICAgICAgY29uc3QgeyBjaGlsZHJlbiwgdXVpZCwgY2hhbmdlQ29udGVudCwgY2hlY2tlZCwgY2hhbmdlVHlwZSwgaXNTaG93LCAuLi5vdGhlcnMgfSA9IGl0ZW0NCiAgICAgICAgICBjb25zdCBjaGFuZ2VNYXAgPSB0aGlzLiRzdG9yZS5zdGF0ZS5jb250YWN0TGlzdC5jaGFuZ2VDb2RlDQogICAgICAgICAgY29uc3QgX2xpc3QgPSAoY2hhbmdlTWFwW3V1aWRdIHx8IFtdKS5tYXAodiA9PiB7DQogICAgICAgICAgICBvdGhlcnNbdi5Db2RlXSA9IHYuTmV3VmFsdWUNCiAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgIENoYW5nZUZpZWxkQ29kZTogdi5Db2RlLA0KICAgICAgICAgICAgICBDaGFuZ2VGaWVsZE5hbWU6IHYuTmFtZSwNCiAgICAgICAgICAgICAgQmVmb3JlVmFsdWU6IHYuVmFsdWUsDQogICAgICAgICAgICAgIEFmdGVyVmFsdWU6IHYuTmV3VmFsdWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICAgIG90aGVycy5Nb2NDb250ZW50ID0gSlNPTi5zdHJpbmdpZnkoX2xpc3QpDQogICAgICAgICAgb3RoZXJzLk1vY1R5cGUgPSBjaGFuZ2VDb250ZW50DQogICAgICAgICAgcmV0dXJuIG90aGVycw0KICAgICAgICB9KQ0KICAgICAgICBjb25zb2xlLmxvZyhKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHN1Ym1pdFRiKSkpDQogICAgICAgIF9mb3JtLkRlZXBlbl9GaWxlX1VybCA9IG51bGwNCiAgICAgICAgX2Zvcm0uRGVlcGVuX0ZpbGVfVXJsX0xpc3QgPSBudWxsDQogICAgICB9IGVsc2Ugew0KICAgICAgICBzdWJtaXRUYiA9IHRoaXMudGJEYXRhDQogICAgICB9DQogICAgICBjb25zdCBpc1JlTmV3ID0gdGhpcy5pc0ltcG9ydEZpbGUgJiYgdGhpcy5jaGFuZ2VNZXRob2QgIT09IDMgJiYgdGhpcy5pc0VkaXQNCiAgICAgIGNvbnN0IHN1Yk9iaiA9IHsNCiAgICAgICAgLi4uX2Zvcm0sDQogICAgICAgIElzTmV3SW1wb3J0RmlsZTogaXNSZU5ldywNCiAgICAgICAgSGFuZGxlX1VzZXJOYW1lOiBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnVXNlck5hbWUnKSwNCiAgICAgICAgTW9jX1R5cGVfTmFtZTogdGhpcy5jaGFuZ2VUeXBlTGlzdC5maW5kKGl0ZW0gPT4gaXRlbS5JZCA9PT0gX2Zvcm0uTW9jX1R5cGVfSWQpPy5EaXNwbGF5X05hbWUsDQogICAgICAgIENoYW5nZV9UeXBlOiB0aGlzLmNoYW5nZU1ldGhvZCA9PT0gMSA/IDAgOiB0aGlzLmNoYW5nZU1ldGhvZCA9PT0gMiA/IDEgOiAyLA0KICAgICAgICBJbnN0YWxsVW5pdF9JZHM6IEFycmF5LmlzQXJyYXkoX2Zvcm0uSW5zdGFsbFVuaXRfSWRzKSA/IF9mb3JtLkluc3RhbGxVbml0X0lkcy5qb2luKCcsJykgOiBfZm9ybS5JbnN0YWxsVW5pdF9JZHMsDQogICAgICAgIElzX0RyYWZ0OiBpc0RyYWZ0LA0KICAgICAgICBPcmRlckRldGFpbDogc3VibWl0VGINCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmNoYW5nZU1ldGhvZCAhPT0gMykgew0KICAgICAgICBzdWJPYmouRGVlcGVuX0ZpbGVfVXJsID0gdGhpcy5maWxlUGF0aA0KICAgICAgfQ0KICAgICAgYXdhaXQgU2F2ZU1vY09yZGVyKHN1Yk9iaikudGhlbihhc3luYyByZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIGlmIChpc0RyYWZ0KSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+S/neWtmOaIkOWKnycsDQogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIGNsb3NlVGFnVmlldyh0aGlzLiRzdG9yZSwgdGhpcy4kcm91dGUpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGlmICghcmVzLkRhdGEpIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgbWVzc2FnZTogJ+aPkOS6pOWksei0pScsDQogICAgICAgICAgICAgICAgdHlwZTogJ3dyYXJuaW5nJw0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGF3YWl0IHRoaXMuc3VibWl0Q2hlY2socmVzLkRhdGEpDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGlmIChyZXMuRGF0YSAmJiByZXMuRGF0YS5QYXRoKSB7DQogICAgICAgICAgICB3aW5kb3cub3Blbihjb21iaW5lVVJMKHRoaXMuJGJhc2VVcmwsIHJlcy5EYXRhLlBhdGgpLCAnX2JsYW5rJykNCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgYXN5bmMgc3VibWl0Q2hlY2soSWQpIHsNCiAgICAgIGF3YWl0IFN1Ym1pdE1vY09yZGVyKHsNCiAgICAgICAgSWQNCiAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6ICfmj5DkuqTmiJDlip8nLA0KICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgICAgfSkNCiAgICAgICAgICBjbG9zZVRhZ1ZpZXcodGhpcy4kc3RvcmUsIHRoaXMuJHJvdXRlKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZVJlc2V0KCkgew0KICAgICAgdGhpcy5zZWFyY2hGb3JtLmNvbXBvbmVudF9uYW1lID0gJycNCiAgICAgIHRoaXMuc2VhcmNoRm9ybS5wYXJ0X25hbWUgPSAnJw0KICAgICAgdGhpcy5zZWFyY2hGb3JtLmFzc2VtYmx5X25hbWUgPSAnJw0KICAgICAgdGhpcy5zZWFyY2hGb3JtLmNvbXBvbmVudF9zZWFyY2hfbW9kZSA9IDENCiAgICAgIHRoaXMuc2VhcmNoRm9ybS5wYXJ0X3NlYXJjaF9tb2RlID0gMQ0KICAgICAgdGhpcy5zZWFyY2hGb3JtLmFzc2VtYmx5X3NlYXJjaF9tb2RlID0gMQ0KICAgICAgdGhpcy4kcmVmcz8udGFibGVSZWY/LnNldEFsbFRyZWVFeHBhbmQoZmFsc2UpDQogICAgICB0aGlzLiRyZWZzPy50YWJsZVJlZj8uY2xlYXJGaWx0ZXIoKQ0KICAgIH0sDQogICAgY2xlYXJUYigpIHsNCiAgICAgIHRoaXMudGJEYXRhID0gW10NCiAgICAgIHRoaXMuZGVmYXVsdFRiRGF0YSA9IFtdDQogICAgICB0aGlzLmhhbmRsZVJlc2V0KCkNCiAgICB9LA0KICAgIGhhbmRsZUZpbHRlcigpIHsNCiAgICAgIHRoaXMubmFtZU1hcHBpbmcgPSB7DQogICAgICAgIENvbXBvbmVudE5hbWU6IHt9LA0KICAgICAgICBTdGVlbE5hbWU6IHt9LA0KICAgICAgICBQYXJ0TmFtZToge30NCiAgICAgIH0NCiAgICAgIGNvbnN0IGNoYW5nZU1hcHMgPSB0aGlzLiRzdG9yZS5zdGF0ZS5jb250YWN0TGlzdC5jaGFuZ2VDb2RlDQogICAgICBPYmplY3Qua2V5cyhjaGFuZ2VNYXBzKS5mb3JFYWNoKHV1aWQgPT4gew0KICAgICAgICBjb25zdCBjaGFuZ2VMaXN0ID0gY2hhbmdlTWFwc1t1dWlkXQ0KICAgICAgICBjaGFuZ2VMaXN0LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgaWYgKGl0ZW0uQ29kZSA9PT0gJ0NvbXBvbmVudE5hbWUnKSB7DQogICAgICAgICAgICB0aGlzLm5hbWVNYXBwaW5nLkNvbXBvbmVudE5hbWVbaXRlbS5WYWx1ZV0gPSBpdGVtLk5ld1ZhbHVlDQogICAgICAgICAgfSBlbHNlIGlmIChpdGVtLkNvZGUgPT09ICdTdGVlbE5hbWUnKSB7DQogICAgICAgICAgICB0aGlzLm5hbWVNYXBwaW5nLlN0ZWVsTmFtZVtpdGVtLlZhbHVlXSA9IGl0ZW0uTmV3VmFsdWUNCiAgICAgICAgICB9IGVsc2UgaWYgKGl0ZW0uQ29kZSA9PT0gJ1BhcnROYW1lJykgew0KICAgICAgICAgICAgdGhpcy5uYW1lTWFwcGluZy5QYXJ0TmFtZVtpdGVtLlZhbHVlXSA9IGl0ZW0uTmV3VmFsdWUNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KQ0KDQogICAgICBjb25zdCB4VGFibGUgPSB0aGlzLiRyZWZzLnRhYmxlUmVmDQogICAgICBjb25zdCBjb2RlQ29sdW1uID0geFRhYmxlLmdldENvbHVtbkJ5RmllbGQoJ0NQQ29kZScpDQogICAgICBjb25zdCBvcHRpb24gPSBjb2RlQ29sdW1uLmZpbHRlcnNbMF0NCiAgICAgIG9wdGlvbi5kYXRhID0gW3RoaXMuc2VhcmNoRm9ybS5jb21wb25lbnRfbmFtZSwgdGhpcy5zZWFyY2hGb3JtLmFzc2VtYmx5X25hbWUsIHRoaXMuc2VhcmNoRm9ybS5wYXJ0X25hbWVdDQogICAgICBvcHRpb24uY2hlY2tlZCA9IHRydWUNCiAgICAgIHhUYWJsZS51cGRhdGVEYXRhKCkNCiAgICAgIHRoaXMuJHJlZnMudGFibGVSZWYuc2V0QWxsVHJlZUV4cGFuZCh0cnVlKQ0KICAgICAgdGhpcy4kcmVmcy50YWJsZVJlZi5jbGVhckNoZWNrYm94Um93KCkNCiAgICB9LA0KDQogICAgZ2V0RmVlR2FwKGFjdGl2aXR5LCBpbmRleCkgew0KICAgICAgaWYgKGluZGV4ID09PSAwKSB7DQogICAgICAgIHJldHVybiBhY3Rpdml0eS5GZWUgfHwgMA0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgY29uc3QgcmVzdWx0ID0gbnVtZXJhbChhY3Rpdml0eS5GZWUgfHwgMCkNCiAgICAgICAgICAuc3VidHJhY3QodGhpcy5hY3Rpdml0aWVzW2luZGV4IC0gMV0uRmVlIHx8IDApDQoNCiAgICAgICAgaWYgKHJlc3VsdC52YWx1ZSgpIDwgMCkgew0KICAgICAgICAgIGFjdGl2aXR5LmlzUmVkID0gdHJ1ZQ0KICAgICAgICB9IGVsc2UgaWYgKHJlc3VsdC52YWx1ZSgpID4gMCkgew0KICAgICAgICAgIGFjdGl2aXR5LmlzQmx1ZSA9IHRydWUNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gcmVzdWx0LnZhbHVlKCkgPT09IDAgPyAwIDogcmVzdWx0LmZvcm1hdCgnKzAuWzAwXScpDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVFZGl0KHJvdykgew0KICAgICAgY29uc29sZS5sb2cocm93LCAncm93JykNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9ICdIYW5kbGVFZGl0Jw0KICAgICAgdGhpcy53aWR0aCA9ICc1MCUnDQogICAgICB0aGlzLnRpdGxlID0gYOe8lui+ke+8iCR7dGhpcy5nZXRDcENvZGUocm93KX3vvIlgDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIGNvbnN0IGRlZmF1bHRSb3cgPSB0aGlzLmRlZmF1bHRUYkRhdGEuZmluZChpdGVtID0+IGl0ZW0udXVpZCA9PT0gcm93LnV1aWQpDQogICAgICAgIGNvbnNvbGUubG9nKGRlZmF1bHRSb3csICdkZWZhdWx0Um93JykNCiAgICAgICAgdGhpcy4kcmVmcy5jb250ZW50Py5pbml0KHJvdywgZGVmYXVsdFJvdywgdGhpcy5pc0VkaXQsIHRoaXMudGJEYXRhLCB0aGlzLmFsbENvZGVzKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnNvbGUubG9nKCdyb3cnLCByb3cpDQogICAgICB0aGlzLiRjb25maXJtKCfnoa7orqTopoHliKDpmaTov5nmnaHorrDlvZXlkJc/JywgJ+aPkOekuicsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5kZWxldGVUYWJsZUl0ZW0ocm93LnV1aWQpDQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIC8vIFVzZXIgY2FuY2VsZWQNCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVSZXN0b3JlKHJvdykgew0KICAgICAgdGhpcy5yZXN0b3JlVGFibGVJdGVtKHJvdy51dWlkKQ0KICAgIH0sDQogICAgY2hhbmdlTWV0aG9kRnVuKHZhbCkgew0KICAgICAgY29uc29sZS5sb2coJ3ZhbCcsIHZhbCkNCiAgICAgIGNvbnN0IHNldFRiQ2xvdW1uID0gKCkgPT4gew0KICAgICAgICBpZiAodmFsID09PSAzKSB7DQogICAgICAgICAgY29uc3QgY29sdW1uQ29kZSA9IFsnQ1BDb2RlJywgJ1NldHVwUG9zaXRpb24nLCAnUHJvZHVjdGlvbl9TdGF0dXMnLCAnY2hhbmdlQ29udGVudCddDQogICAgICAgICAgY29uc3QgX2NvbHVtbnMgPSB0aGlzLmNvbHVtbnMuZmlsdGVyKGl0ZW0gPT4gY29sdW1uQ29kZS5pbmNsdWRlcyhpdGVtLkNvZGUpKQ0KICAgICAgICAgIHRoaXMuY29sdW1ucyA9IF9jb2x1bW5zDQogICAgICAgICAgdGhpcy4kbmV4dFRpY2soXyA9PiB7DQogICAgICAgICAgICB0aGlzLiRyZWZzLnRhYmxlUmVmLnJlZnJlc2hDb2x1bW4oKQ0KICAgICAgICAgIH0pDQogICAgICAgIH0gZWxzZSBpZiAodmFsID09PSAxKSB7DQogICAgICAgICAgdGhpcy5jb2x1bW5zID0gZGVlcENsb25lKHRoaXMucm9vdENvbHVtbnMpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5jb2x1bW5zID0gZGVlcENsb25lKHRoaXMucm9vdENvbHVtbnMpDQogICAgICAgIH0NCiAgICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2NvbnRhY3RMaXN0L3Jlc2V0Q2hhbmdlQ29kZScpDQogICAgICAgIHRoaXMuY2hhbmdlTWV0aG9kID0gdmFsDQogICAgICB9DQoNCiAgICAgIGlmICh0aGlzLnRiRGF0YSAmJiB0aGlzLnRiRGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgIHJldHVybiB0aGlzLiRjb25maXJtKCfliIfmjaLlj5jmm7TmlrnlvI/kvJrmuIXnqbrlvZPliY3lt7Lmt7vliqDnmoTlj5jmm7TmmI7nu4bvvIzmmK/lkKbnu6fnu63vvJ8nLCAn5o+Q56S6Jywgew0KICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy50YkRhdGEgPSBbXQ0KICAgICAgICAgIHNldFRiQ2xvdW1uKCkNCiAgICAgICAgICB0aGlzLmZpbGVQYXRoID0gJycNCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gew0KDQogICAgICAgIH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmZpbGVQYXRoID0gJycNCiAgICAgICAgc2V0VGJDbG91bW4oKQ0KICAgICAgfQ0KICAgIH0sDQogICAgZ2V0TW9jTW9kZWxMaXN0KGxpc3QpIHsNCiAgICAgIGNvbnN0IGV4aXN0aW5nVXVpZHMgPSBuZXcgU2V0KHRoaXMudGJEYXRhLm1hcChpdGVtID0+IGl0ZW0udXVpZCkpDQogICAgICBsaXN0ID0gbGlzdC5maWx0ZXIoaXRlbSA9PiAhZXhpc3RpbmdVdWlkcy5oYXMoaXRlbS5Nb2NJZEJlZm9yZSkpDQoNCiAgICAgIGlmICghbGlzdC5sZW5ndGgpIHsNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIGxpc3QgPSBsaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgLy8gY29uc3QgY3VyUGFyZW50ID0gdGhpcy5maW5kUGFyZW50SXRlbShpdGVtKQ0KICAgICAgICAvLyBpZiAoY3VyUGFyZW50ICYmIGN1clBhcmVudC5jaGFuZ2VUeXBlID09PSAnaXNEZWxldGUnKSB7DQogICAgICAgIC8vICAgaXRlbS5jaGFuZ2VUeXBlID0gJ2lzRGVsZXRlJw0KICAgICAgICAvLyAgIGl0ZW0uY2hhbmdlQ29udGVudCA9IHRoaXMuZ2V0Q2hhbmdlVHlwZVRleHQoaXRlbS5jaGFuZ2VUeXBlKQ0KICAgICAgICAvLyAgIGl0ZW0uaXNEaXNhYmxlZCA9IHRydWUNCiAgICAgICAgLy8gICB0aGlzLmRlbGV0ZVRhYmxlSXRlbShpdGVtLnV1aWQpDQogICAgICAgIC8vICAgcmV0dXJuIHsNCiAgICAgICAgLy8gICAgIC4uLml0ZW0sDQogICAgICAgIC8vICAgICBwYXJlbnRDaGlsZHJlbklkOiBpdGVtLklkLA0KICAgICAgICAvLyAgICAgdXVpZDogaXRlbS5Nb2NJZEJlZm9yZSwNCiAgICAgICAgLy8gICAgIGNoZWNrZWQ6IGZhbHNlLA0KICAgICAgICAvLyAgICAgQ29kZVR5cGU6IGl0ZW0uVHlwZSA9PT0gMCA/IDEgOiBpdGVtLlR5cGUgPT09IDEgPyAyIDogMywNCiAgICAgICAgLy8gICAgIC4uLnRoaXMuc2V0QWxsV2VpZ2h0KGl0ZW0pDQogICAgICAgIC8vICAgfQ0KICAgICAgICAvLyB9IGVsc2Ugew0KICAgICAgICB0aGlzLnVwZGF0ZUl0ZW1DaGFuZ2VTdGF0dXMoaXRlbSwgW10pDQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgY2hhbmdlVHlwZTogJ2lzTm9DaGFuZ2UnLA0KICAgICAgICAgIGNoYW5nZUNvbnRlbnQ6IHRoaXMuZ2V0Q2hhbmdlVHlwZVRleHQoJ2lzTm9DaGFuZ2UnKSwNCiAgICAgICAgICAuLi5pdGVtLA0KICAgICAgICAgIHBhcmVudENoaWxkcmVuSWQ6IGl0ZW0uSWQsDQogICAgICAgICAgdXVpZDogaXRlbS5Nb2NJZEJlZm9yZSwNCiAgICAgICAgICBjaGVja2VkOiBmYWxzZSwNCiAgICAgICAgICBDb2RlVHlwZTogaXRlbS5UeXBlID09PSAwID8gMSA6IGl0ZW0uVHlwZSA9PT0gMSA/IDIgOiAzLA0KICAgICAgICAgIC4uLnRoaXMuc2V0QWxsV2VpZ2h0KGl0ZW0pDQogICAgICAgIH0NCiAgICAgICAgLy8gfQ0KICAgICAgfSkNCg0KICAgICAgdGhpcy50YkRhdGEgPSBbLi4udGhpcy50YkRhdGEsIC4uLmxpc3RdLnNvcnQoKGEsIGIpID0+IGIuVHlwZSAtIGEuVHlwZSkNCg0KICAgICAgY29uc3QgX2RlZmF1bHRUYkRhdGEgPSB0aGlzLmRlZmF1bHRUYkRhdGEgfHwgW10NCiAgICAgIHRoaXMuZGVmYXVsdFRiRGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkoWy4uLl9kZWZhdWx0VGJEYXRhLCAuLi5saXN0XSkpDQogICAgICB0aGlzLnNldFNhbWVJdGVtcyh0aGlzLnRiRGF0YSkNCiAgICB9LA0KICAgIHNldFNhbWVJdGVtcyh0YkRhdGEpIHsNCiAgICAgIGNvbnN0IGNoYW5nZUluZm9zID0geyAuLi50aGlzLiRzdG9yZS5zdGF0ZS5jb250YWN0TGlzdC5jaGFuZ2VDb2RlIH0NCiAgICAgIGNvbnN0IG1vY0JlZm9yZUl0ZW1zID0gdGJEYXRhLmZpbHRlcihpdGVtID0+IHsNCiAgICAgICAgcmV0dXJuICEhY2hhbmdlSW5mb3NbaXRlbS51dWlkXQ0KICAgICAgfSkNCiAgICAgIGNvbnN0IGlzRGVsZXRlSXRlbXMgPSB0YkRhdGEuZmlsdGVyKGl0ZW0gPT4gaXRlbS5jaGFuZ2VUeXBlID09PSAnaXNEZWxldGUnKQ0KICAgICAgaWYgKGlzRGVsZXRlSXRlbXMubGVuZ3RoKSB7DQogICAgICAgIGNvbnNvbGUubG9nKGlzRGVsZXRlSXRlbXMsICdpc0RlbGV0ZUl0ZW1zJykNCiAgICAgICAgY29uc3QgdW5pdFBhcnQgPSBpc0RlbGV0ZUl0ZW1zLmZpbHRlcihpdGVtID0+IGl0ZW0uVHlwZSA9PT0gMykNCiAgICAgICAgaWYgKHVuaXRQYXJ0Lmxlbmd0aCkgew0KICAgICAgICAgIHVuaXRQYXJ0LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgICBjb25zdCB1bml0UCA9IHRoaXMuZmluZFBhcmVudEl0ZW0oaXRlbSkNCiAgICAgICAgICAgIGlmICh1bml0UCAmJiB1bml0UC5jaGFuZ2VUeXBlICE9PSAnaXNEZWxldGUnKSB7DQogICAgICAgICAgICAgIGNvbnN0IHNpbWlsYXJVbml0UGFydEl0ZW1zID0gdGhpcy5maW5kU2ltaWxhckl0ZW1zKGl0ZW0pDQogICAgICAgICAgICAgIGlmIChzaW1pbGFyVW5pdFBhcnRJdGVtcy5sZW5ndGgpIHsNCiAgICAgICAgICAgICAgICBzaW1pbGFyVW5pdFBhcnRJdGVtcy5mb3JFYWNoKHNpbWlsYXJJdGVtID0+IHsNCiAgICAgICAgICAgICAgICAgIGNvbnN0IGlzU2FtZSA9IHRoaXMuaXNTYW1lUGFyZW50KGl0ZW0sIHNpbWlsYXJJdGVtKQ0KICAgICAgICAgICAgICAgICAgaWYgKCFpc1NhbWUpIHJldHVybg0KICAgICAgICAgICAgICAgICAgdGhpcy4kc2V0KHNpbWlsYXJJdGVtLCAnY2hhbmdlVHlwZScsICdpc0RlbGV0ZScpDQogICAgICAgICAgICAgICAgICB0aGlzLiRzZXQoc2ltaWxhckl0ZW0sICdjaGFuZ2VDb250ZW50JywgdGhpcy5nZXRDaGFuZ2VUeXBlVGV4dCgnaXNEZWxldGUnKSkNCiAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgICAvLyBpc0RlbGV0ZUl0ZW1zLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgIC8vICAgY29uc3Qgc2ltaWxhckl0ZW1zID0gdGhpcy5maW5kU2ltaWxhckl0ZW1zKGl0ZW0pDQogICAgICAgIC8vICAgaWYgKHNpbWlsYXJJdGVtcy5sZW5ndGgpIHsNCiAgICAgICAgLy8gICAgIHNpbWlsYXJJdGVtcy5mb3JFYWNoKHNpbWlsYXJJdGVtID0+IHsNCiAgICAgICAgLy8gICAgICAgY29uc29sZS5sb2coaXRlbS5Db2RlLCAnc2ltaWxhckl0ZW1zJykNCiAgICAgICAgLy8gICAgICAgdGhpcy4kc2V0KHNpbWlsYXJJdGVtLCAnY2hhbmdlVHlwZScsIGl0ZW0uY2hhbmdlVHlwZSkNCiAgICAgICAgLy8gICAgICAgdGhpcy4kc2V0KHNpbWlsYXJJdGVtLCAnY2hhbmdlQ29udGVudCcsIGl0ZW0uY2hhbmdlQ29udGVudCkNCiAgICAgICAgLy8gICAgICAgLy8gY29uc3QgaXNEaXNhYmxlZCA9IHRoaXMuaXNTYW1lUGFyZW50KGl0ZW0sIHNpbWlsYXJJdGVtKQ0KICAgICAgICAvLyAgICAgICAvLyB0aGlzLiRzZXQoc2ltaWxhckl0ZW0sICdpc0Rpc2FibGVkJywgIWlzRGlzYWJsZWQpDQogICAgICAgIC8vICAgICAgIC8vIGlmIChpc0Rpc2FibGVkKSB7DQogICAgICAgIC8vIHRoaXMuJHNldChzaW1pbGFySXRlbSwgJ2NoYW5nZVR5cGUnLCBpdGVtLmNoYW5nZVR5cGUpDQogICAgICAgIC8vIHRoaXMuJHNldChzaW1pbGFySXRlbSwgJ2NoYW5nZUNvbnRlbnQnLCBpdGVtLmNoYW5nZUNvbnRlbnQpDQogICAgICAgIC8vICAgICAgIC8vIH0NCiAgICAgICAgLy8gICAgIH0pDQogICAgICAgIC8vICAgfQ0KICAgICAgICAvLyB9KQ0KICAgICAgfQ0KICAgICAgaWYgKCFtb2NCZWZvcmVJdGVtcy5sZW5ndGgpIHJldHVybg0KICAgICAgbW9jQmVmb3JlSXRlbXMuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgbGV0IF9saXN0ID0gdGhpcy5maW5kU2ltaWxhckl0ZW1zKGl0ZW0pDQogICAgICAgIF9saXN0ID0gX2xpc3QuZmlsdGVyKGsgPT4gIWNoYW5nZUluZm9zW2sudXVpZF0pDQogICAgICAgIGlmIChfbGlzdC5sZW5ndGgpIHsNCiAgICAgICAgICBfbGlzdC5mb3JFYWNoKGN1ciA9PiB7DQogICAgICAgICAgICBpZiAodGhpcy5pc1NhbWVQYXJlbnQoaXRlbSwgY3VyKSkgew0KICAgICAgICAgICAgICBjb25zdCBjaGFuZ2VMaXN0ID0gdGhpcy4kc3RvcmUuc3RhdGUuY29udGFjdExpc3QuY2hhbmdlQ29kZVtpdGVtLnV1aWRdDQogICAgICAgICAgICAgIGNoYW5nZUxpc3QuZm9yRWFjaChjaGFuZ2UgPT4gew0KICAgICAgICAgICAgICAgIGN1cltjaGFuZ2UuQ29kZV0gPSBpdGVtW2NoYW5nZS5Db2RlXQ0KICAgICAgICAgICAgICB9KQ0KDQogICAgICAgICAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdjb250YWN0TGlzdC9hZGRDaGFuZ2VDb2RlJywgew0KICAgICAgICAgICAgICAgIHV1aWQ6IGN1ci51dWlkLA0KICAgICAgICAgICAgICAgIGxpc3Q6IGNoYW5nZUxpc3QNCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ2N1cicsIGl0ZW0uaXNEaXNhYmxlZCkNCiAgICAgICAgICAgICAgaWYgKGl0ZW0uY2hhbmdlVHlwZSA9PT0gJ2lzRGVsZXRlJykgew0KICAgICAgICAgICAgICAgIC8vIHRoaXMuJHNldChjdXIsICdpc0Rpc2FibGVkJywgaXRlbS5pc0Rpc2FibGVkKQ0KDQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgdGhpcy51cGRhdGVJdGVtQ2hhbmdlU3RhdHVzKGN1ciwgY2hhbmdlTGlzdCkNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgY29uc3QgeyBTdGVlbEFtb3VudCwgLi4ub3RoZXJzIH0gPSBpdGVtDQogICAgICAgICAgICAgIGNvbnN0IGZpbHRlcmVkTGlzdCA9ICh0aGlzLiRzdG9yZS5zdGF0ZS5jb250YWN0TGlzdC5jaGFuZ2VDb2RlW2l0ZW0udXVpZF0gfHwgW10pLmZpbHRlcihjaGFuZ2UgPT4gY2hhbmdlLkNvZGUgIT09ICdTdGVlbEFtb3VudCcpDQogICAgICAgICAgICAgIGZpbHRlcmVkTGlzdC5mb3JFYWNoKGNoYW5nZSA9PiB7DQogICAgICAgICAgICAgICAgY3VyW2NoYW5nZS5Db2RlXSA9IGl0ZW1bY2hhbmdlLkNvZGVdDQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgIC8vIGN1ci5DUENvZGUgPSBpdGVtLkNQQ29kZQ0KICAgICAgICAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnY29udGFjdExpc3QvYWRkQ2hhbmdlQ29kZScsIHsNCiAgICAgICAgICAgICAgICB1dWlkOiBjdXIudXVpZCwNCiAgICAgICAgICAgICAgICBsaXN0OiBmaWx0ZXJlZExpc3QNCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgdGhpcy51cGRhdGVJdGVtQ2hhbmdlU3RhdHVzKGN1ciwgZmlsdGVyZWRMaXN0KQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBlZGl0SW5mbyh7IHJvdywgbGlzdCB9KSB7DQogICAgICBjb25zb2xlLmxvZygnZWRpdEluZm8gcm93LCBsaXN0Jywgcm93LCBsaXN0KQ0KICAgICAgY29uc3QgX2NoYW5nZU1hcHMgPSB7fQ0KICAgICAgbGlzdC5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICBfY2hhbmdlTWFwc1tpdGVtLkNvZGVdID0gaXRlbS5OZXdWYWx1ZQ0KICAgICAgfSkNCiAgICAgIC8vIHRoaXMucmVzZXREZWZhdWx0VmFsKCkNCg0KICAgICAgY29uc3QgZXhpc3RpbmdDaGFuZ2VzID0gdGhpcy4kc3RvcmUuc3RhdGUuY29udGFjdExpc3QuY2hhbmdlQ29kZVtyb3cudXVpZF0gfHwgW10NCiAgICAgIGNvbnN0IGV4aXN0aW5nQ2hhbmdlQ29kZXMgPSBleGlzdGluZ0NoYW5nZXMubWFwKGNoYW5nZSA9PiBjaGFuZ2UuQ29kZSkNCg0KICAgICAgY29uc3QgcmVtb3ZlZENoYW5nZUNvZGVzID0gZXhpc3RpbmdDaGFuZ2VDb2Rlcy5maWx0ZXIoY29kZSA9PiAhbGlzdC5zb21lKGl0ZW0gPT4gaXRlbS5Db2RlID09PSBjb2RlKSkNCiAgICAgIGNvbnNvbGUubG9nKCflt7Lnp7vpmaTnmoTlrZfmrrUnLCByZW1vdmVkQ2hhbmdlQ29kZXMpDQoNCiAgICAgIGlmIChyZW1vdmVkQ2hhbmdlQ29kZXMubGVuZ3RoKSB7DQogICAgICAgIGNvbnN0IGRlZmF1bHRSb3cgPSB0aGlzLmRlZmF1bHRUYkRhdGEuZmluZChpdGVtID0+IGl0ZW0udXVpZCA9PT0gcm93LnV1aWQpDQogICAgICAgIHJlbW92ZWRDaGFuZ2VDb2Rlcy5mb3JFYWNoKGNvZGUgPT4gew0KICAgICAgICAgIGNvbnNvbGUubG9nKGDph43nva7lrZfmrrUgJHtjb2RlfSDkuLrljp/lp4vlgLw6YCwgZGVmYXVsdFJvd1tjb2RlXSkNCiAgICAgICAgICBfY2hhbmdlTWFwc1tjb2RlXSA9IGRlZmF1bHRSb3dbY29kZV0NCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICAgIGNvbnNvbGUubG9nKCdfY2hhbmdlTWFwcycsIEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkoX2NoYW5nZU1hcHMpKSkNCg0KICAgICAgLy8g5om56YeP5pu05paw6KGo5qC86aG5DQogICAgICB0aGlzLmJhdGNoVXBkYXRlVGFibGVJdGVtKHJvdy51dWlkLCBfY2hhbmdlTWFwcykNCiAgICAgIC8vIHRoaXMudXBkYXRlQ29kZXNOYW1lKHJvdywgX2NoYW5nZU1hcHMpDQogICAgfSwNCiAgICAvLyB1cGRhdGVDb2Rlc05hbWUodGFyZ2V0SXRlbSwgX2NoYW5nZU1hcHMpIHsNCiAgICAvLyAgIGlmIChfY2hhbmdlTWFwcy5TdGVlbE5hbWUpIHsNCiAgICAvLyAgICAgdGFyZ2V0SXRlbS5TdGVlbE5hbWUgPSBfY2hhbmdlTWFwcy5TdGVlbE5hbWUNCiAgICAvLyAgICAgdGFyZ2V0SXRlbS5DUENvZGUgPSBfY2hhbmdlTWFwcy5TdGVlbE5hbWUNCiAgICAvLyAgIH0gZWxzZSBpZiAoX2NoYW5nZU1hcHMuQ29tcG9uZW50TmFtZSkgew0KICAgIC8vICAgICB0YXJnZXRJdGVtLkNvbXBvbmVudE5hbWUgPSBfY2hhbmdlTWFwcy5Db21wb25lbnROYW1lDQogICAgLy8gICAgIHRhcmdldEl0ZW0uQ1BDb2RlID0gX2NoYW5nZU1hcHMuQ29tcG9uZW50TmFtZQ0KICAgIC8vICAgfSBlbHNlIGlmIChfY2hhbmdlTWFwcy5QYXJ0TmFtZSkgew0KICAgIC8vICAgICB0YXJnZXRJdGVtLlBhcnROYW1lID0gX2NoYW5nZU1hcHMuUGFydE5hbWUNCiAgICAvLyAgICAgdGFyZ2V0SXRlbS5DUENvZGUgPSBfY2hhbmdlTWFwcy5QYXJ0TmFtZQ0KICAgIC8vICAgfSBlbHNlIHsNCiAgICAvLyAgICAgY29uc3QgZGVmYXVsdFJvdyA9IHRoaXMuZGVmYXVsdFRiRGF0YS5maW5kKGl0ZW0gPT4gaXRlbS51dWlkID09PSB0YXJnZXRJdGVtLnV1aWQpDQogICAgLy8gICAgIGNvbnNvbGUubG9nKCdkZWZhdWx0Um93JywgSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShkZWZhdWx0Um93KSkpDQogICAgLy8gICAgIGlmIChkZWZhdWx0Um93KSB7DQogICAgLy8gICAgICAgdGFyZ2V0SXRlbS5TdGVlbE5hbWUgPSBkZWZhdWx0Um93LlN0ZWVsTmFtZQ0KICAgIC8vICAgICAgIHRhcmdldEl0ZW0uQ1BDb2RlID0gZGVmYXVsdFJvdy5DUENvZGUNCiAgICAvLyAgICAgfQ0KICAgIC8vICAgfQ0KICAgIC8vICAgY29uc29sZS5sb2coJ3RhcmdldEl0ZW0nLCBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRhcmdldEl0ZW0pKSkNCiAgICAvLyAgIGNvbnN0IF9saXN0ID0gdGhpcy5maW5kU2ltaWxhckl0ZW1zKHRhcmdldEl0ZW0pDQogICAgLy8gICBpZiAoX2xpc3QubGVuZ3RoKSB7DQogICAgLy8gICAgIF9saXN0LmZvckVhY2goaXRlbSA9PiB7DQogICAgLy8gICAgICAgaXRlbS5TdGVlbE5hbWUgPSB0YXJnZXRJdGVtLlN0ZWVsTmFtZQ0KICAgIC8vICAgICAgIGl0ZW0uQ1BDb2RlID0gdGFyZ2V0SXRlbS5DUENvZGUNCiAgICAvLyAgICAgfSkNCiAgICAvLyAgIH0NCiAgICAvLyB9LA0KICAgIGhhbmRsZUNhbmNlbENoYW5nZSgpIHsNCiAgICAgIGNvbnN0IHNlbGVjdGVkUmVjb3JkcyA9IFtdDQogICAgICBjb25zdCBnZXRJZHMgPSAoYXJyYXkpID0+IHsNCiAgICAgICAgaWYgKCFhcnJheSB8fCAhYXJyYXkubGVuZ3RoKSByZXR1cm4NCiAgICAgICAgYXJyYXkuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICBzZWxlY3RlZFJlY29yZHMucHVzaChpdGVtLnV1aWQpDQogICAgICAgICAgaWYgKGl0ZW0uY2hpbGRyZW4gJiYgaXRlbS5jaGlsZHJlbi5sZW5ndGgpIHsNCiAgICAgICAgICAgIGdldElkcyhpdGVtLmNoaWxkcmVuKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICAgIGdldElkcyh0aGlzLm11bHRpcGxlU2VsZWN0aW9uKQ0KICAgICAgY29uc29sZS5sb2coJ3NlbGVjdGVkUmVjb3JkcycsIHNlbGVjdGVkUmVjb3JkcykNCiAgICAgIHNlbGVjdGVkUmVjb3Jkcy5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnY29udGFjdExpc3QvZGVsQ2hhbmdlQ29kZScsIGl0ZW0pDQogICAgICB9KQ0KICAgICAgdGhpcy50YkRhdGEgPSB0aGlzLnRiRGF0YS5maWx0ZXIoaXRlbSA9PiAhc2VsZWN0ZWRSZWNvcmRzLmluY2x1ZGVzKGl0ZW0udXVpZCkpDQogICAgICB0aGlzLmRlZmF1bHRUYkRhdGEgPSB0aGlzLmRlZmF1bHRUYkRhdGEuZmlsdGVyKGl0ZW0gPT4gIXNlbGVjdGVkUmVjb3Jkcy5pbmNsdWRlcyhpdGVtLnV1aWQpKQ0KICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbiA9IFtdDQogICAgfSwNCiAgICBtdWx0aVNlbGVjdGVkQ2hhbmdlKGFycmF5KSB7DQogICAgICBjb25zb2xlLmxvZygnYXJyYXknLCBhcnJheSkNCiAgICAgIGNvbnNvbGUubG9nKCdhcnJheS5yZWNvcmRzJywgdGhpcy4kcmVmcy50YWJsZVJlZi5nZXRDaGVja2JveFJlY29yZHModHJ1ZSkpDQogICAgICBjb25zdCB7IHJlY29yZHMgfSA9IGFycmF5DQogICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gYXJyYXkucmVjb3Jkcw0KICAgIH0sDQogICAgaGFuZGVsRmlsZVBhdGgoKSB7DQogICAgICBHZXRPc3NVcmwoew0KICAgICAgICB1cmw6IHRoaXMuZmlsZVBhdGgNCiAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgd2luZG93Lm9wZW4ocmVzLkRhdGEpDQogICAgICB9KQ0KICAgIH0sDQogICAgZ2V0Q2hhbmdlU3R5bGUoY2hhbmdlTmFtZSkgew0KICAgICAgY29uc3QgYXJyID0gY2hhbmdlTmFtZS5zcGxpdCgnLCcpDQogICAgICBjb25zdCBydXN1bHQgPSBbJ2NzLWMtYm94J10NCiAgICAgIGlmIChhcnIuaW5jbHVkZXMoY2hhbmdlVHlwZS5pc0FkZCkpIHsNCiAgICAgICAgcnVzdWx0LnB1c2goJ2NzLWNoYW5nZS1ncmVlbicpDQogICAgICB9IGVsc2UgaWYgKGFyci5pbmNsdWRlcyhjaGFuZ2VUeXBlLmlzQWRqdXN0KSkgew0KICAgICAgICBydXN1bHQucHVzaCgnY3MtY2hhbmdlLXllbGxvdycpDQogICAgICB9IGVsc2UgaWYgKGFyci5pbmNsdWRlcyhjaGFuZ2VUeXBlLmlzRGVjcmVhc2UpIHx8IGFyci5pbmNsdWRlcyhjaGFuZ2VUeXBlLmlzSW5jcmVhc2UpKSB7DQogICAgICAgIHJ1c3VsdC5wdXNoKCdjcy1jaGFuZ2UtYmx1ZScpDQogICAgICB9IGVsc2UgaWYgKGFyci5pbmNsdWRlcyhjaGFuZ2VUeXBlLmlzRGVsZXRlKSkgew0KICAgICAgICBydXN1bHQucHVzaCgnY3MtY2hhbmdlLXJlZCcpDQogICAgICB9IGVsc2Ugew0KICAgICAgICBydXN1bHQucHVzaCgnY3MtZGVmYXVsdCcpDQogICAgICB9DQogICAgICByZXR1cm4gcnVzdWx0DQogICAgfSwNCiAgICBnZXRDcENvZGUocm93KSB7DQogICAgICBpZiAocm93LlR5cGUgPT09IDApIHsNCiAgICAgICAgcmV0dXJuIHJvdy5TdGVlbE5hbWUNCiAgICAgIH0gZWxzZSBpZiAocm93LlR5cGUgPT09IDEpIHsNCiAgICAgICAgcmV0dXJuIHJvdy5Db21wb25lbnROYW1lDQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gcm93LlBhcnROYW1lDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVTaG93KHJvdykgew0KICAgICAgY29uc3QgY2hhbmdlTGlzdCA9IHRoaXMuJHN0b3JlLnN0YXRlLmNvbnRhY3RMaXN0LmNoYW5nZUNvZGVbcm93LnV1aWRdDQogICAgICB0aGlzLmNoYW5nZVJvd0NvbnRlbnRMaXN0ID0gY2hhbmdlTGlzdCB8fCBbXQ0KICAgIH0sDQogICAgZmlsdGVyTmFtZU1ldGhvZCh7IG9wdGlvbiwgdmFsdWVzLCBjZWxsVmFsdWUsIHJvdywgY29sdW1uIH0pIHsNCiAgICAgIGNvbnN0IHJlc3VsdCA9IHRoaXMuZmlsdGVyQ3VzdG9tKHJvdykNCiAgICAgIHJldHVybiByZXN1bHQNCiAgICB9LA0KICAgIGZpbHRlckN1c3RvbShyb3cpIHsNCiAgICAgIGNvbnN0IHsgY29tcG9uZW50X25hbWUsIGNvbXBvbmVudF9zZWFyY2hfbW9kZSwgYXNzZW1ibHlfbmFtZSwNCiAgICAgICAgYXNzZW1ibHlfc2VhcmNoX21vZGUsIHBhcnRfbmFtZSwgcGFydF9zZWFyY2hfbW9kZSB9ID0gdGhpcy5zZWFyY2hGb3JtDQoNCiAgICAgIGNvbnN0IF9Db21wb25lbnROYW1lID0gdGhpcy5uYW1lTWFwcGluZy5Db21wb25lbnROYW1lW3Jvdy5Db21wb25lbnROYW1lXSB8fCByb3cuQ29tcG9uZW50TmFtZSB8fCAnJw0KICAgICAgY29uc3QgX1N0ZWVsTmFtZSA9IHRoaXMubmFtZU1hcHBpbmcuU3RlZWxOYW1lW3Jvdy5TdGVlbE5hbWVdIHx8IHJvdy5TdGVlbE5hbWUgfHwgJycNCiAgICAgIGNvbnN0IF9QYXJ0TmFtZSA9IHRoaXMubmFtZU1hcHBpbmcuUGFydE5hbWVbcm93LlBhcnROYW1lXSB8fCByb3cuUGFydE5hbWUgfHwgJycNCg0KICAgICAgbGV0IHBhcnRNYXRjaCA9IHRydWUNCg0KICAgICAgaWYgKHBhcnRfbmFtZSkgew0KICAgICAgICBpZiAocGFydF9zZWFyY2hfbW9kZSA9PT0gMSkgew0KICAgICAgICAgIHBhcnRNYXRjaCA9IF9QYXJ0TmFtZS5pbmNsdWRlcyhwYXJ0X25hbWUpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgcGFydE1hdGNoID0gX1BhcnROYW1lID09PSBwYXJ0X25hbWUNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgbGV0IGFzc2VtYmx5TWF0Y2ggPSB0cnVlDQogICAgICBpZiAoYXNzZW1ibHlfbmFtZSkgew0KICAgICAgICBpZiAoYXNzZW1ibHlfc2VhcmNoX21vZGUgPT09IDEpIHsNCiAgICAgICAgICBhc3NlbWJseU1hdGNoID0gX0NvbXBvbmVudE5hbWUuaW5jbHVkZXMoYXNzZW1ibHlfbmFtZSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBhc3NlbWJseU1hdGNoID0gX0NvbXBvbmVudE5hbWUgPT09IGFzc2VtYmx5X25hbWUNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgbGV0IGNvbXBvbmVudE1hdGNoID0gdHJ1ZQ0KICAgICAgaWYgKGNvbXBvbmVudF9uYW1lKSB7DQogICAgICAgIGlmIChjb21wb25lbnRfc2VhcmNoX21vZGUgPT09IDEpIHsNCiAgICAgICAgICBjb21wb25lbnRNYXRjaCA9IF9TdGVlbE5hbWUuaW5jbHVkZXMoY29tcG9uZW50X25hbWUpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29tcG9uZW50TWF0Y2ggPSBfU3RlZWxOYW1lID09PSBjb21wb25lbnRfbmFtZQ0KICAgICAgICB9DQogICAgICB9DQogICAgICAvLyBjb25zb2xlLmxvZyhjb21wb25lbnRNYXRjaCwgYXNzZW1ibHlNYXRjaCwgcGFydE1hdGNoKQ0KDQogICAgICBjb25zdCByZXN1bHQgPSBjb21wb25lbnRNYXRjaCAmJiBhc3NlbWJseU1hdGNoICYmIHBhcnRNYXRjaA0KICAgICAgaWYgKHJlc3VsdCkgew0KICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICB9DQogICAgfSwNCiAgICBpbnN0YWxsQ2hhbmdlKGFycikgew0KICAgICAgaWYgKCFhcnIgfHwgIWFyci5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy5jbGVhclRiKCkNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICB0aGlzLnRiRGF0YSA9IHRoaXMudGJEYXRhLmZpbHRlcihpdGVtID0+IGFyci5zb21lKGlkID0+IGlkID09PSBpdGVtLkluc3RhbGxVbml0X0lkKSkNCiAgICAgIHRoaXMuZGVmYXVsdFRiRGF0YSA9IHRoaXMuZGVmYXVsdFRiRGF0YS5maWx0ZXIoaXRlbSA9PiBhcnIuc29tZShpZCA9PiBpZCA9PT0gaXRlbS5JbnN0YWxsVW5pdF9JZCkpDQogICAgfSwNCiAgICBhZnRlckFwcHJvdmFsKCkgew0KICAgICAgY2xvc2VUYWdWaWV3KHRoaXMuJHN0b3JlLCB0aGlzLiRyb3V0ZSkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["add.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2dA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "add.vue", "sourceRoot": "src/views/PRO/change-management/contact-list", "sourcesContent": ["<template>\r\n  <div class=\"page-container\">\r\n    <div class=\"form-x\">\r\n      <div>\r\n        <span class=\"cs-title\">工程联系单</span>\r\n      </div>\r\n      <el-divider />\r\n      <el-row v-loading=\"pageLoading\" element-loading-text=\"加载中\">\r\n        <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"120px\">\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"项目名称\" prop=\"Sys_Project_Id\">\r\n              <el-select\r\n                v-model=\"form.Sys_Project_Id\"\r\n                :disabled=\"isView||tbLoading\"\r\n                clearable\r\n                class=\"w100\"\r\n                placeholder=\"请选择\"\r\n                filterable\r\n                @change=\"projectChange()\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in projectList\"\r\n                  :key=\"item.Sys_Project_Id\"\r\n                  :label=\"item.Short_Name\"\r\n                  :value=\"item.Sys_Project_Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"区域名称\" prop=\"Area_Id\">\r\n              <el-tree-select\r\n                ref=\"treeSelectArea\"\r\n                v-model=\"form.Area_Id\"\r\n                :disabled=\"!form.Sys_Project_Id||isView||tbLoading\"\r\n                :select-params=\"{\r\n                  clearable: true,\r\n\r\n                }\"\r\n                :tree-params=\"treeParamsArea\"\r\n                @select-clear=\"areaClear\"\r\n                @node-click=\"areaChange\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item ref=\"installUnitRef\" label=\"批次\" prop=\"InstallUnit_Ids\">\r\n              <el-select\r\n                v-model=\"form.InstallUnit_Ids\"\r\n                filterable\r\n                multiple\r\n                class=\"w100\"\r\n                :disabled=\"isView||!form.Area_Id||tbLoading\"\r\n                placeholder=\"请选择\"\r\n                @change=\"installChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in installUnitList\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"变更人\" prop=\"Handle_UserId\">\r\n              <el-select\r\n                v-model=\"form.Handle_UserId\"\r\n                :disabled=\"isView\"\r\n                filterable\r\n                class=\"w100\"\r\n                clearable\r\n                placeholder=\"请选择\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in peopleList\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"变更类型\" prop=\"Moc_Type_Id\">\r\n              <el-select\r\n                v-model=\"form.Moc_Type_Id\"\r\n                class=\"w100\"\r\n                :disabled=\"isView\"\r\n                filterable\r\n                placeholder=\"请选择\"\r\n                @change=\"mocTypeChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in changeTypeList\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"变更费用\" prop=\"Fee\">\r\n              <el-input-number v-model=\"form.Fee\" placeholder=\"请输入\" style=\"width: 100%\" clearable :disabled=\"isView\" class=\"cs-number-btn-hidden\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"变更工时\" prop=\"Hours\">\r\n              <el-input-number v-model=\"form.Hours\" placeholder=\"请输入\" style=\"width: 100%\" clearable :disabled=\"isView\" class=\"cs-number-btn-hidden\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"紧急程度\" prop=\"Urgency\">\r\n              <el-select v-model=\"form.Urgency\" placeholder=\"请选择\" style=\"width: 100%\" :disabled=\"isView\">\r\n                <el-option label=\"普通\" :value=\"1\" />\r\n                <el-option label=\"紧急\" :value=\"2\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"要求完成时间\" prop=\"Demand_Date\">\r\n              <el-date-picker\r\n                v-model=\"form.Demand_Date\"\r\n                :disabled=\"isView\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                type=\"date\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"费用部门\" prop=\"Fee_DepartId\">\r\n              <el-tree-select\r\n                ref=\"treeSelect\"\r\n                v-model=\"form.Fee_DepartId\"\r\n                :disabled=\"isView\"\r\n                :select-params=\"{\r\n                  clearable: true,\r\n                }\"\r\n                :tree-params=\"treeParams\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"变更说明\" prop=\"Remark\">\r\n              <el-input\r\n                v-model=\"form.Remark\"\r\n                type=\"textarea\"\r\n                :disabled=\"isView\"\r\n                :autosize=\"{ minRows: 3, maxRows: 3}\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"附件\" prop=\"region\">\r\n              <OSSUpload\r\n                ref=\"upload\"\r\n                :disabled=\"isView\"\r\n                action=\"\"\r\n                :before-upload=\"beforeUpload\"\r\n                :limit=\"5\"\r\n                multiple\r\n                :file-list=\"fileList\"\r\n                :on-progress=\"handleProgress\"\r\n                :on-exceed=\"handleExceed\"\r\n                :on-error=\"handleError\"\r\n                :on-success=\"uploadSuccess\"\r\n                :on-change=\"handleChange\"\r\n                :on-remove=\"handleRemove\"\r\n                :on-preview=\"handlePreview\"\r\n                show-file-list\r\n                accept=\".xls, .xlsx,.pdf,.jpg,.png,.dwg,.doc,.docx\"\r\n                btn-icon=\"el-icon-upload\"\r\n                :class=\"isView ? 'z-upload hiddenBtn' : 'z-upload'\"\r\n              >\r\n                <el-button v-if=\"!isView\" :loading=\"uploadLoading\" type=\"primary\">上传文件</el-button>\r\n              </OSSUpload>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-form>\r\n\r\n      </el-row>\r\n    </div>\r\n    <div v-if=\"isView\" class=\"cs-fee\">\r\n      <span class=\"cs-title\">费用变动</span>\r\n      <span class=\"cs-label\">当前金额：<span class=\"fw cs-blue\">{{ finishFee }}元</span></span>\r\n      <el-divider />\r\n      <el-timeline>\r\n        <el-timeline-item\r\n          v-for=\"(activity, index) in activities\"\r\n          :key=\"index\"\r\n          hide-timestamp\r\n        >\r\n          <div class=\"line-content\">\r\n            <span class=\"fee-name\">{{ activity.Create_UserName }}</span>\r\n            <span :class=\"['fee-num',{'txt-red':activity.isRed,'txt-blue':activity.isBlue}]\">{{ getFeeGap(activity,index) }}</span>\r\n            <span class=\"fee-time\">{{ activity.Create_Date }}</span>\r\n            <span class=\"fee-remark\">备注：{{ activity.Remark || '-' }}</span>\r\n          </div>\r\n          <template #dot>\r\n            <span class=\"circle\" />\r\n          </template>\r\n        </el-timeline-item>\r\n      </el-timeline>\r\n    </div>\r\n    <div class=\"cs-main\">\r\n      <template v-if=\"showDetail\">\r\n        <span class=\"cs-title\">变更明细</span>\r\n        <el-divider />\r\n\r\n        <el-form inline class=\"change-method-form\">\r\n          <el-form-item label=\"变更方式：\">\r\n            <template v-if=\"!isView\">\r\n              <el-radio :value=\"changeMethod\" :label=\"1\" @input=\"changeMethodFun(1)\">完整清单导入</el-radio>\r\n              <el-radio :value=\"changeMethod\" :label=\"2\" @input=\"changeMethodFun(2)\">部分清单导入</el-radio>\r\n              <el-radio :value=\"changeMethod\" :label=\"3\" @input=\"changeMethodFun(3)\">手动修改</el-radio>\r\n            </template>\r\n            <template v-else>\r\n              {{ changeMethod === 1 ? '完整清单导入' : changeMethod === 2 ? '部分清单导入' : '手动修改' }}\r\n            </template>\r\n          </el-form-item>\r\n        </el-form>\r\n        <div v-if=\"filePath\">\r\n          <i style=\"color:#409EFF\" class=\"el-icon-download\" />\r\n          <el-link type=\"primary\" :underline=\"false\" @click=\"handelFilePath\">{{ getFileName }}</el-link>\r\n        </div>\r\n        <vxe-toolbar ref=\"xToolbar1\" class=\"cs-toolBar\">\r\n          <template #buttons>\r\n            <el-button v-if=\"!isView && changeMethod !== 3\" type=\"primary\" @click=\"handleImport\">导入变更清单</el-button>\r\n            <el-button v-if=\"!isView && changeMethod === 3\" type=\"primary\" @click=\"handleAdd\">添加变更内容</el-button>\r\n            <el-button v-if=\"!isView && changeMethod === 3\" type=\"danger\" :disabled=\"!multipleSelection.length\" plain @click=\"handleCancelChange\">取消变更</el-button>\r\n          </template>\r\n          <template #tools>\r\n            <el-form ref=\"form2\" inline :model=\"searchForm\" label-width=\"70px\">\r\n              <el-form-item label=\"构件名称\">\r\n                <el-input\r\n                  v-model=\"searchForm.component_name\"\r\n                  clearable\r\n                  style=\"width: 260px;\"\r\n                  placeholder=\"请输入\"\r\n                  class=\"input-with-select\"\r\n                >\r\n                  <el-select\r\n                    slot=\"prepend\"\r\n                    v-model=\"searchForm.component_search_mode\"\r\n                    placeholder=\"请选择\"\r\n                    style=\"width: 100px\"\r\n                  >\r\n                    <el-option\r\n                      label=\"模糊搜索\"\r\n                      :value=\"1\"\r\n                    />\r\n                    <el-option\r\n                      label=\"精确搜索\"\r\n                      :value=\"2\"\r\n                    />\r\n                  </el-select>\r\n                </el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"部件名称\">\r\n                <el-input\r\n                  v-model=\"searchForm.assembly_name\"\r\n                  style=\"width: 260px;\"\r\n                  clearable\r\n                  placeholder=\"请输入\"\r\n                  class=\"input-with-select\"\r\n                >\r\n                  <el-select\r\n                    slot=\"prepend\"\r\n                    v-model=\"searchForm.assembly_search_mode\"\r\n                    placeholder=\"请选择\"\r\n                    style=\"width: 100px\"\r\n                  >\r\n                    <el-option\r\n                      label=\"模糊搜索\"\r\n                      :value=\"1\"\r\n                    />\r\n                    <el-option\r\n                      label=\"精确搜索\"\r\n                      :value=\"2\"\r\n                    />\r\n                  </el-select>\r\n                </el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"零件名称\">\r\n                <el-input\r\n                  v-model=\"searchForm.part_name\"\r\n                  clearable\r\n                  style=\"width: 260px;\"\r\n                  placeholder=\"请输入\"\r\n                  class=\"input-with-select\"\r\n                >\r\n                  <el-select\r\n                    slot=\"prepend\"\r\n                    v-model=\"searchForm.part_search_mode\"\r\n                    placeholder=\"请选择\"\r\n                    style=\"width: 100px\"\r\n                  >\r\n                    <el-option\r\n                      label=\"模糊搜索\"\r\n                      :value=\"1\"\r\n                    />\r\n                    <el-option\r\n                      label=\"精确搜索\"\r\n                      :value=\"2\"\r\n                    />\r\n                  </el-select>\r\n                </el-input>\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button @click=\"handleReset\">重置</el-button>\r\n                <el-button type=\"primary\" @click=\"handleFilter\">搜索</el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n          </template>\r\n        </vxe-toolbar>\r\n        <div class=\"fff tb-x\">\r\n          <vxe-table\r\n            id=\"uuid\"\r\n            ref=\"tableRef\"\r\n            :empty-render=\"{name: 'NotData'}\"\r\n            show-header-overflow\r\n            :loading=\"tbLoading\"\r\n            element-loading-spinner=\"el-icon-loading\"\r\n            element-loading-text=\"拼命加载中\"\r\n            empty-text=\"暂无数据\"\r\n            class=\"cs-vxe-table cs-tree-table\"\r\n            height=\"500\"\r\n            stripe\r\n            :filter-config=\"{showIcon: false}\"\r\n            :row-config=\"{keyField:'uuid', 'isHover': true, 'isCurrent': true}\"\r\n            :data=\"tbData\"\r\n            resizable\r\n            :checkbox-config=\"{checkField: 'checked',labelField: 'CPCode', highlight: true}\"\r\n            :tree-config=\"{transform: true, showIcon: true, rowField: 'parentChildrenId', parentField: 'ParentId'}\"\r\n            :tooltip-config=\"{ enterable: true}\"\r\n            @checkbox-all=\"multiSelectedChange\"\r\n            @checkbox-change=\"multiSelectedChange\"\r\n          >\r\n            <vxe-column v-if=\"changeMethod === 3 && !isView\" fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n            <!-- :type=\"index===0 && changeMethod === 3 && !isView? 'checkbox':''\" -->\r\n\r\n            <template v-for=\"(item,index) in columns\">\r\n              <vxe-column\r\n                :key=\"item.Code+changeMethod\"\r\n                :tree-node=\"index===0\"\r\n                :fixed=\"item.Is_Frozen ? (item.Frozen_Dirction || 'left') : ''\"\r\n                :min-width=\"item.Width\"\r\n                show-overflow=\"tooltip\"\r\n                sortable\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :filters=\"item.Code==='CPCode' ? filterCodeOptions : null\"\r\n                :filter-method=\"item.Code==='CPCode' ? filterNameMethod : null\"\r\n                :title=\"item.Display_Name\"\r\n              >\r\n                <template v-if=\"item.Code==='CPCode'\" #filter=\"{ $panel, column }\">\r\n                  <input v-for=\"(option, index1) in column.filters\" :key=\"index1\" v-model=\"option.data\" type=\"type\" @input=\"$panel.changeOption($event, !!option.data, option)\">\r\n                </template>\r\n                <template v-if=\"item.Code==='CPCode'\" #default=\"{ row }\">\r\n                  {{ getCpCode(row) }}\r\n                  <el-tag v-if=\"row.Type===0\" size=\"mini\">构</el-tag>\r\n                  <el-tag v-else-if=\"row.Type===1\" type=\"warning\" size=\"mini\">部</el-tag>\r\n                  <el-tag v-else type=\"success\" size=\"mini\">零</el-tag>\r\n                </template>\r\n                <template v-else-if=\"item.Code === 'changeContent'\" #default=\"{ row }\">\r\n\r\n                  <el-popover\r\n                    placement=\"left-start\"\r\n                    width=\"400\"\r\n                    trigger=\"click\"\r\n                    @show=\"handleShow(row)\"\r\n                  >\r\n                    <el-table max-height=\"300\" stripe resizable class=\"cs-custom-table\" :data=\"changeRowContentList\">\r\n                      <el-table-column align=\"center\" property=\"Name\" width=\"100\" label=\"变更字段\" />\r\n                      <el-table-column align=\"center\" property=\"Value\" label=\"变更前\" />\r\n                      <el-table-column align=\"center\" property=\"NewValue\" label=\"变更后\" />\r\n                    </el-table>\r\n                    <span slot=\"reference\" style=\"cursor: pointer;\" :class=\"getChangeStyle(row.changeContent)\">\r\n                      {{ row.changeContent }}\r\n                    </span>\r\n                  </el-popover>\r\n\r\n                </template>\r\n                <template v-else-if=\"item.Code === 'Production_Status'\" #default=\"{ row }\">\r\n                  <el-link v-if=\"row.MocIdBefore&& row[item.Code]\" :type=\"row.Production_Status === '未生产' ? 'info' : 'primary'\" @click=\"handleOpen(row)\"> {{ row[item.Code] | displayValue }}</el-link>\r\n                  <span v-else>-</span>\r\n                </template>\r\n                <template v-else-if=\"item.Code === 'Is_Component_Mark'\" #default=\"{ row }\">\r\n                  <!--             是否直发件 是：是直发件；否：非直发件；-->\r\n                  <template v-if=\"row.Type===0\">\r\n                    <el-tag v-if=\"row[item.Code]==='是'\" type=\"primary\">是</el-tag>\r\n                    <el-tag v-else type=\"danger\">否</el-tag>\r\n                  </template>\r\n                  <template v-else>\r\n                    <el-tag v-if=\"row.PartType==='直发件'\" type=\"primary\">是</el-tag>\r\n                    <el-tag v-else type=\"danger\">否</el-tag>\r\n                  </template>\r\n\r\n                </template>\r\n                <template v-else #default=\"{ row }\">\r\n                  <span> {{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n            </template>\r\n\r\n            <!-- Operations column for manual edit mode -->\r\n            <vxe-column v-if=\"changeMethod === 3 && !isView\" fixed=\"right\" title=\"操作\" width=\"160\">\r\n              <template #default=\"{ row }\">\r\n                <el-button\r\n                  v-if=\"row.changeType !== 'isDelete'\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  @click=\"handleEdit(row)\"\r\n                >\r\n                  编辑\r\n                </el-button>\r\n                <el-button\r\n                  v-if=\"row.changeType !== 'isDelete'\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  style=\"color: #FB6B7F;\"\r\n                  @click=\"handleDelete(row)\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n                <el-button\r\n                  v-if=\"row.changeType === 'isDelete' && !row.isDisabled\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  style=\"color: #298DFF;\"\r\n                  @click=\"handleRestore(row)\"\r\n                >\r\n                  撤销删除\r\n                </el-button>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-table>\r\n        </div>\r\n      </template>\r\n      <footer v-if=\"!isView\">\r\n        <el-button :disabled=\"disableSave\" :loading=\"saveLoading\" @click=\"handleSave\">保存草稿</el-button>\r\n        <el-button :disabled=\"disableSave || uploadLoading\" :loading=\"submitLoading\" type=\"primary\" @click=\"handleSubmit\">提交审核</el-button>\r\n      </footer>\r\n      <footer v-if=\"$route.query.operate === 'audit' && $route.query.type==='2'\">\r\n        <processHead :no-style=\"true\" :process-id=\"$route.query.processId\" :business-id=\"$route.query.id\" :web-id=\"$route.query.webId\" @afterapproval=\"afterApproval\" />\r\n      </footer>\r\n    </div>\r\n    <ImportFile ref=\"dialog\" @refresh=\"getTableInfo\" />\r\n    <StatusDialog ref=\"statusDialog\" />\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      v-dialogDrag\r\n      top=\"6vh\"\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"width\"\r\n      class=\"plm-custom-dialog\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        @getMocModelList=\"getMocModelList\"\r\n        @editInfo=\"editInfo\"\r\n        @close=\"handleClose\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GeAreaTrees, GetInstallUnitIdNameList, GetProjectPageList } from '@/api/PRO/project'\r\nimport { GetFactoryPeoplelist } from '@/api/PRO/basic-information/workshop'\r\nimport {\r\n  GetMocOrderInfo,\r\n  GetMocOrderTypeList,\r\n  ImportChangFile,\r\n  SaveMocOrder,\r\n  SubmitMocOrder\r\n} from '@/api/PRO/changeManagement'\r\nimport { GetCompanyDepartTree, GetOssUrl } from '@/api/sys'\r\nimport ImportFile from './components/importFile.vue'\r\nimport OSSUpload from '@/views/plm/components/ossupload.vue'\r\nimport { closeTagView, combineURL, debounce, deepClone } from '@/utils'\r\nimport StatusDialog from './components/dialog.vue'\r\nimport numeral from 'numeral'\r\nimport { GetTableSettingList } from '@/api/PRO/component-type'\r\nimport HandleEdit from './components/HandleEdit.vue'\r\nimport AddHandle from './components/addHandle.vue'\r\nimport { changeType, changeTypeReverse, getAllCodesByType } from './utils'\r\nimport { getFileNameFromUrl } from '@/utils/file'\r\nimport { isArray } from 'ali-oss/lib/common/utils/isArray'\r\nimport SteelComponentManager from '@/views/PRO/change-management/contact-list/info'\r\nimport { GetCurFactory } from '@/api/PRO/factory'\r\nimport processHead from '@/views/PRO/components/processHead'\r\n\r\nexport default {\r\n  name: 'PROEngineeringChangeOrderAdd',\r\n  components: {\r\n    OSSUpload,\r\n    StatusDialog,\r\n    ImportFile,\r\n    HandleEdit,\r\n    AddHandle,\r\n    processHead\r\n  },\r\n  mixins: [SteelComponentManager],\r\n  data() {\r\n    return {\r\n      factoryReferenceId: '',\r\n      curStatus: {\r\n        del: '已删',\r\n        change: '变更',\r\n        add: '新增',\r\n        increase: '数量增加',\r\n        decrease: '数量减少',\r\n        unChange: '无变更'\r\n      },\r\n\r\n      dialogVisible: false,\r\n      title: '',\r\n      width: '50%',\r\n      currentComponent: '',\r\n      filePath: '',\r\n      finishFee: 0,\r\n      pageLoading: false,\r\n      saveLoading: false,\r\n      submitLoading: false,\r\n      uploadLoading: false,\r\n      tbLoading: false,\r\n      tbData: [],\r\n      activities: [],\r\n      fileList: [],\r\n      multipleSelection: [],\r\n      changeRowContentList: [],\r\n      filterCodeOptions: [{ data: '' }],\r\n      columns: [],\r\n      changeMethod: 1,\r\n      searchForm: {\r\n        component_name: '',\r\n        part_name: '',\r\n        assembly_name: '',\r\n        component_search_mode: 1,\r\n        part_search_mode: 1,\r\n        assembly_search_mode: 1,\r\n        content: ''\r\n      },\r\n      form: {\r\n        Sys_Project_Id: '',\r\n        Area_Id: '',\r\n        InstallUnit_Ids: [],\r\n        Handle_UserId: '',\r\n        Moc_Type_Id: '',\r\n        Fee: undefined,\r\n        Hours: undefined,\r\n        Urgency: 1,\r\n        Demand_Date: '',\r\n        Fee_DepartId: '',\r\n        Remark: '',\r\n        AttachmentList: []\r\n      },\r\n      showImport: false,\r\n      treeParams: {\r\n        data: [],\r\n        filterable: false,\r\n        clickParent: true,\r\n        props: {\r\n          disabled: 'disabled',\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      peopleList: [],\r\n      projectList: [],\r\n      changeTypeList: [],\r\n      installUnitList: [],\r\n      treeParamsArea: {\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          disabled: 'disabled',\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      rules: {\r\n        Sys_Project_Id: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        Area_Id: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        Handle_UserId: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        Moc_Type_Id: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        InstallUnit_Ids: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ]\r\n      },\r\n      toolbarButtons: [\r\n        { code: 'myToolbarExport', name: '点击导出' },\r\n        { code: 'myToolbarLink', name: '点击跳转' },\r\n        { code: 'myToolbarCustom', name: '打开自定义列' }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    disableSave() {\r\n      return this.submitLoading || this.uploadLoading\r\n    },\r\n    isView() {\r\n      return this.$route.query.type == '2'\r\n    },\r\n    isEdit() {\r\n      return this.$route.query.type == '1'\r\n    },\r\n    showDetail() {\r\n      const zbtk = this.changeTypeList.find(item => {\r\n        return item.Id === this.form.Moc_Type_Id\r\n      })\r\n      return zbtk?.Is_Deepen_Change || false\r\n    },\r\n    getFileName() {\r\n      return getFileNameFromUrl(this.filePath)\r\n    }\r\n  },\r\n  watch: {\r\n    'form.Area_Id': {\r\n      handler(newVal) {\r\n        if (!newVal) {\r\n          this.rules.InstallUnit_Ids[0].required = false\r\n          this.$refs?.installUnitRef?.clearValidate()\r\n        }\r\n      },\r\n      immediate: true\r\n    },\r\n    'form.InstallUnit_Ids': {\r\n      handler(newVal) {\r\n        if (!this.Area_Id) return\r\n        if (this.installUnitList.length) {\r\n          this.rules.InstallUnit_Ids[0].required = true\r\n          this.$refs.installUnitRef.clearValidate()\r\n        } else {\r\n          this.rules.InstallUnit_Ids[0].required = false\r\n          this.$refs.installUnitRef.clearValidate()\r\n        }\r\n      }\r\n    }\r\n  },\r\n  async created() {\r\n    const PermittedFactoryList = JSON.parse(localStorage.getItem('PermittedFactoryList')) || []\r\n    this.factoryReferenceId = PermittedFactoryList.find(item => item.Id === this.$route.query.factoryId)?.Reference_Id || ''\r\n    // await this.getTableConfig('PROEngChangeOrderAdd') ProfessionalCode\r\n    this.$store.dispatch('contactList/resetChangeCode')\r\n    this.getProjectData()\r\n    this.getFactoryPeople()\r\n    await this.getDepTree()\r\n    this.getFactoryChangeTypeList()\r\n\r\n    const id = this.$route.query.id\r\n    id && await this.getInfo(id)\r\n    this.getTableConfig()\r\n    // this.getTableInfo()\r\n  },\r\n  methods: {\r\n    async getTableConfig() {\r\n      const res = await GetTableSettingList({ ProfessionalCode: 'Steel' })\r\n      if (res.IsSucceed) {\r\n        this.allCodes = JSON.parse(JSON.stringify(res.Data))\r\n        // Filter out the three name columns\r\n        const filteredColumns = res.Data.filter(item =>\r\n          !['SteelName', 'ComponentName', 'PartName'].includes(item.Code)\r\n        )\r\n\r\n        // Add the new CPCode column\r\n        const codeColumn = {\r\n          Code: 'CPCode',\r\n          Display_Name: '构件/部件/零件名称',\r\n          Is_Frozen: true,\r\n          Frozen_Dirction: 'left',\r\n          Width: 180,\r\n          Align: 'left'\r\n        }\r\n\r\n        // Insert the CPCode column at the beginning\r\n        filteredColumns.unshift(codeColumn)\r\n        const _customColumns = [{\r\n          Code: 'Production_Status',\r\n          Display_Name: '生产情况',\r\n          Align: 'center',\r\n          Is_Frozen: true,\r\n          Frozen_Dirction: 'right',\r\n          Width: 120\r\n        }, {\r\n          Code: 'changeContent',\r\n          Display_Name: '变更内容',\r\n          Align: 'center',\r\n          Is_Frozen: true,\r\n          Frozen_Dirction: 'right',\r\n          Width: 120\r\n        }]\r\n\r\n        filteredColumns.push(..._customColumns)\r\n        let _columns = []\r\n\r\n        this.rootColumns = deepClone(filteredColumns.map(item => {\r\n          const displayNameLength = item.Display_Name?.length || 0\r\n          const width = Math.max(120, 120 + Math.max(0, displayNameLength - 4) * 10)\r\n          return {\r\n            ...item,\r\n            Width: width,\r\n            Align: 'center'\r\n          }\r\n        }))\r\n\r\n        if (this.changeMethod === 3) {\r\n          const columnCode = ['CPCode', 'SetupPosition', 'Production_Status', 'changeContent']\r\n          _columns = this.rootColumns.filter(item => columnCode.includes(item.Code))\r\n        } else {\r\n          _columns = this.rootColumns\r\n        }\r\n        this.columns = _columns\r\n      }\r\n    },\r\n    async getInfo(id) {\r\n      this.pageLoading = true\r\n      await GetMocOrderInfo({\r\n        Id: id,\r\n        factoryReferenceId: this.factoryReferenceId\r\n      }).then(async res => {\r\n        if (res.IsSucceed) {\r\n          const {\r\n            Deepen_File_Url,\r\n            Sys_Project_Id,\r\n            Area_Id,\r\n            InstallUnit_Ids,\r\n            Handle_UserId,\r\n            Moc_Type_Id,\r\n            Change_Type,\r\n            Fee,\r\n            Hours,\r\n            Urgency,\r\n            Demand_Date,\r\n            Fee_DepartId,\r\n            Remark,\r\n            FeeHistory,\r\n            Id,\r\n            Status,\r\n            AttachmentList,\r\n            OrderDetail\r\n          } = res.Data\r\n          this.activities = FeeHistory\r\n          if (FeeHistory.length) {\r\n            const [last] = FeeHistory.slice(-1)\r\n            this.finishFee = numeral(last?.Fee || 0).format('0.[00]')\r\n          }\r\n          if (Status === 3) {\r\n            const idx = this.columns.findIndex(item => item.Code === 'Production_Status')\r\n            if (idx !== -1) {\r\n              this.columns.splice(idx, 1)\r\n            }\r\n          }\r\n\r\n          if (AttachmentList?.length) {\r\n            AttachmentList.forEach((element, idx) => {\r\n              const obj = {\r\n                name: element.File_Name,\r\n                url: element.File_Url\r\n              }\r\n              this.fileList.push(obj)\r\n              this.form.AttachmentList.push({\r\n                File_Url: element.File_Url,\r\n                File_Name: element.File_Name\r\n              })\r\n            })\r\n          }\r\n          await this.getAreaList(Sys_Project_Id)\r\n          await this.getInstallUnitPageList(Area_Id)\r\n\r\n          Object.assign(this.form, {\r\n            ...res.Data,\r\n            Sys_Project_Id,\r\n            Area_Id,\r\n            Deepen_File_Url,\r\n            Id,\r\n            InstallUnit_Ids: InstallUnit_Ids ? (typeof InstallUnit_Ids === 'string' ? InstallUnit_Ids.split(',') : InstallUnit_Ids) : [],\r\n            Handle_UserId,\r\n            Moc_Type_Id,\r\n            Fee: Fee || undefined,\r\n            Hour: Hours || undefined,\r\n            Urgency: Number(Urgency),\r\n            Demand_Date,\r\n            Fee_DepartId,\r\n            Remark\r\n          })\r\n\r\n          this.setTbData(OrderDetail)\r\n          this.filePath = Deepen_File_Url\r\n          this.changeMethod = Change_Type === 0 ? 1 : Change_Type === 1 ? 2 : 3\r\n          // Deepen_File_Url\r\n          // setTimeout(() => {\r\n          // Deepen_File_Url && this.getTableInfo({ File_Url: Deepen_File_Url })\r\n          // }, 0)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.pageLoading = false\r\n      })\r\n    },\r\n    mocTypeChange() {\r\n      this.tbData = []\r\n      this.handleReset()\r\n    },\r\n\r\n    handleExceed(files, fileList) {\r\n      this.$message.warning(`当前限制选择 5 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)\r\n    },\r\n    handleProgress(event, files, fileList) {\r\n    },\r\n    handleError(err, files, fileList) {\r\n      console.log('err3', err, files, fileList)\r\n      this.checkUploading(fileList)\r\n    },\r\n    checkUploading(fileList) {\r\n      const flag = fileList.every(v => v.status === 'success')\r\n      flag && (this.uploadLoading = false)\r\n    },\r\n    handleImport() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (valid) {\r\n          console.log('valid', valid)\r\n          this.$refs['dialog'].handleOpen()\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    handleAdd() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (valid) {\r\n          this.title = '添加变更内容'\r\n          this.width = '70%'\r\n          this.currentComponent = 'addHandle'\r\n          this.dialogVisible = true\r\n          this.$nextTick(_ => {\r\n            this.$refs['content'].handleOpen(this.form, this.tbData)\r\n          })\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    handleOpen(row) {\r\n      this.$refs['statusDialog'].handleOpen(row)\r\n    },\r\n    getProjectData() {\r\n      GetProjectPageList({ PageSize: -1, factoryReferenceId: this.factoryReferenceId }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.projectList = res.Data.Data\r\n        }\r\n      })\r\n    },\r\n    setDisabledTree(root) {\r\n      if (!root) return\r\n\r\n      root.forEach((element) => {\r\n        const { Children } = element\r\n        if (Children && Children.length) {\r\n          element.disabled = true\r\n        } else {\r\n          element.disabled = false\r\n          this.setDisabledTree(Children)\r\n        }\r\n      })\r\n    },\r\n    async getAreaList(Pid) {\r\n      await GeAreaTrees({\r\n        sysProjectId: Pid,\r\n        factoryReferenceId: this.factoryReferenceId\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const tree = res.Data\r\n          this.setDisabledTree(tree)\r\n          this.treeParamsArea.data = res.Data\r\n          this.$nextTick(_ => {\r\n            this.$refs.treeSelectArea.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async getInstallUnitPageList(areaId) {\r\n      await GetInstallUnitIdNameList({\r\n        Area_Id: areaId,\r\n        Page: 1,\r\n        PageSize: -1\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.installUnitList = res.Data\r\n          if (this.installUnitList.length) {\r\n            this.rules.InstallUnit_Ids[0].required = true\r\n          } else {\r\n            this.rules.InstallUnit_Ids[0].required = false\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async projectChange() {\r\n      const Sys_Project_Id = this.form.Sys_Project_Id\r\n      this.clearTb()\r\n      this.form.Area_Id = ''\r\n      this.form.InstallUnit_Ids = []\r\n      this.treeParamsArea.data = []\r\n      this.$nextTick(_ => {\r\n        this.$refs.treeSelectArea.treeDataUpdateFun([])\r\n      })\r\n      if (Sys_Project_Id) {\r\n        await this.getAreaList(Sys_Project_Id)\r\n      }\r\n    },\r\n    async areaChange() {\r\n      this.clearTb()\r\n      await this.getInstallUnitPageList(this.form.Area_Id)\r\n      if (this.installUnitList.length && this.form.Area_Id) {\r\n        this.form.InstallUnit_Ids = [this.installUnitList[0].Id]\r\n        this.rules.InstallUnit_Ids[0].required = true\r\n      } else {\r\n        this.rules.InstallUnit_Ids[0].required = false\r\n        this.$refs.installUnitRef.clearValidate()\r\n      }\r\n    },\r\n    areaClear() {\r\n      this.form.Area_Id = ''\r\n      this.form.InstallUnit_Ids = []\r\n      this.clearTb()\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    getFactoryPeople() {\r\n      GetFactoryPeoplelist({ factoryReferenceId: this.factoryReferenceId }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.peopleList = Object.freeze(res.Data || [])\r\n\r\n          const curId = localStorage.getItem('UserId')\r\n          const cur = this.peopleList.find(v => v.Id === curId)\r\n          if (cur) {\r\n            this.form.Handle_UserId = cur.Id\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getFactoryChangeTypeList() {\r\n      GetMocOrderTypeList({ factoryReferenceId: this.factoryReferenceId }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.changeTypeList = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async getDepTree() {\r\n      const getFactoryDeptId = async() => {\r\n        return await GetCurFactory({ factoryReferenceId: this.factoryReferenceId }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            return res?.Data[0]?.Dept_Id\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n\r\n      const getDept = async(depId) => {\r\n        await GetCompanyDepartTree({}).then(res => {\r\n          if (res.IsSucceed) {\r\n            const origin = res.Data?.[0]\r\n            if (origin.Children.length) {\r\n              const tree = origin.Children.filter(v => v.Id === depId)\r\n\r\n              const disableDirectory = (treeArray) => {\r\n                treeArray.map(element => {\r\n                  if (element.Children && element.Children.length > 0) {\r\n                    element.disabled = true\r\n                    disableDirectory(element.Children)\r\n                  }\r\n                })\r\n              }\r\n              disableDirectory(tree)\r\n              this.$refs.treeSelect.treeDataUpdateFun(tree || [])\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n      const depId = await getFactoryDeptId()\r\n      await getDept(depId)\r\n    },\r\n    getTableInfo(fileObj) {\r\n      this.tbLoading = true\r\n      const form = { ...this.form }\r\n      ImportChangFile({\r\n        ...form,\r\n        ImportType: this.changeMethod,\r\n        InstallUnit_Ids: this.form.InstallUnit_Ids.toString(),\r\n        AttachmentList: [fileObj]\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.isImportFile = true\r\n          const { AttachmentList, MocOrderDetailList } = res.Data\r\n          // this.getTbList(Orignal_Deepen_List, Import_Deepen_List)\r\n          // filePath\r\n          if (AttachmentList.length) {\r\n            this.filePath = AttachmentList[0].File_Url\r\n            this.form.Deepen_File_Url = this.filePath\r\n            this.form.Deepen_File_Url_List = [fileObj]\r\n          }\r\n          this.setTbData(MocOrderDetailList)\r\n        } else {\r\n          if (res.Data && res.Data.ErrorFileUrl) {\r\n            window.open(combineURL(this.$baseUrl, res.Data.ErrorFileUrl), '_blank')\r\n          }\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    setAllWeight(row) {\r\n      return {\r\n        SteelAllWeight: numeral(row.SteelWeight).multiply(row.SteelAmount).format('0.[000]')\r\n      }\r\n    },\r\n    setTbData(list) {\r\n      const setItem = (list, item, isAfterValue = false) => {\r\n        const updatedItem = {\r\n          ...item,\r\n          CodeType: item.Type === 0 ? 1 : item.Type === 1 ? 2 : 3,\r\n          parentChildrenId: item.Id,\r\n          uuid: item.MocIdBefore,\r\n          checked: false,\r\n          changeContent: item.MocType,\r\n          changeType: changeTypeReverse[item.MocType],\r\n          ...this.setAllWeight(item)\r\n        }\r\n        this.setItemMocContent(updatedItem, isAfterValue)\r\n        if (updatedItem.changeType === 'isDelete') {\r\n          const childrenItems = this.findChildItems(updatedItem, list)\r\n          if (childrenItems.length) {\r\n            childrenItems.forEach(childItem => {\r\n              childItem.isDisabled = true\r\n            })\r\n          }\r\n        }\r\n        return updatedItem\r\n      }\r\n\r\n      this.defaultTbData = list.map(item => setItem(list, item))\r\n\r\n      this.tbData = list.map(item => setItem(list, item, true))\r\n    },\r\n    setItemMocContent(item, isAfterValue = false) {\r\n      if (item.MocContent) {\r\n        let _MocContent = JSON.parse(item.MocContent)\r\n        if (isArray(_MocContent) && _MocContent.length) {\r\n          _MocContent = _MocContent.filter(m => m.ChangeFieldCode !== 'PartNum')\r\n          const _list = _MocContent.map(m => {\r\n            const _codes = getAllCodesByType(item.CodeType)\r\n            const cur = _codes.find(v => v.Code === m.ChangeFieldCode)\r\n            item[m.ChangeFieldCode] = isAfterValue ? m.AfterValue : m.BeforeValue\r\n\r\n            return {\r\n              Field_Type: cur?.Field_Type || 'string',\r\n              IsCoreField: cur?.IsCoreField || false,\r\n              Code: m.ChangeFieldCode,\r\n              Name: m.ChangeFieldName,\r\n              Value: m.BeforeValue,\r\n              NewValue: m.AfterValue\r\n            }\r\n          })\r\n          this.$store.dispatch('contactList/addChangeCode', { uuid: item.uuid, list: _list })\r\n        }\r\n      }\r\n    },\r\n    beforeUpload(file) {\r\n      this.uploadLoading = true\r\n    },\r\n    handleChange(file, fileList) {\r\n      this.fileList = fileList\r\n    },\r\n    async handlePreview(file) {\r\n      console.log(file)\r\n      const arr = file.name.split('.')\r\n      const isDwg = arr[arr.length - 1] === 'dwg'\r\n      const { Data } = await GetOssUrl({ url: file.url })\r\n      if (isDwg) {\r\n        window.open('http://dwgv1.bimtk.com:5432/?CadUrl=' + Data, '_blank')\r\n      } else {\r\n        window.open(Data)\r\n      }\r\n    },\r\n    handleRemove(file, fileList) {\r\n      this.fileList = fileList\r\n      this.checkUploading(fileList)\r\n      this.form.AttachmentList = this.fileList.map(item => {\r\n        if (item.url) {\r\n          return {\r\n            File_Url: item.url,\r\n            File_Name: item.name\r\n          }\r\n        } else {\r\n          const url = item.response.Data\r\n          const fileInfo = url.split('*')\r\n          const fileObj = {\r\n            File_Url: fileInfo[0],\r\n            File_Size: fileInfo[1],\r\n            File_Type: fileInfo[2],\r\n            File_Name: fileInfo[3]\r\n          }\r\n          return {\r\n            File_Url: fileObj.File_Url,\r\n            File_Name: fileObj.File_Name\r\n          }\r\n        }\r\n      })\r\n    },\r\n    uploadSuccess(response, file, fileList) {\r\n      if (!response || !response.Data) {\r\n        return\r\n      }\r\n      this.checkUploading(fileList)\r\n      this.form.AttachmentList = this.fileList.map(item => {\r\n        if (item.url) {\r\n          return {\r\n            File_Url: item.url,\r\n            File_Name: item.name\r\n          }\r\n        } else {\r\n          if (item.status !== 'success') return\r\n          const url = item.response.Data\r\n          const fileInfo = url.split('*')\r\n          const fileObj = {\r\n            File_Url: fileInfo[0],\r\n            File_Size: fileInfo[1],\r\n            File_Type: fileInfo[2],\r\n            File_Name: fileInfo[3]\r\n          }\r\n          return {\r\n            File_Url: fileObj.File_Url,\r\n            File_Name: fileObj.File_Name\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    handleSave() {\r\n      this.$refs['form'].validate(async(valid) => {\r\n        if (valid) {\r\n          this.saveLoading = true\r\n          await this.submit(true)\r\n          this.saveLoading = false\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    async handleSubmit() {\r\n      this.$refs['form'].validate(async(valid) => {\r\n        if (valid) {\r\n          this.submitLoading = true\r\n          await this.submit(false)\r\n          this.submitLoading = false\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    async submit(isDraft) {\r\n      console.log('this.form', this.form)\r\n      const _form = { ...this.form }\r\n      let submitTb = []\r\n      if (this.changeMethod === 3) {\r\n        submitTb = this.tbData.map((item) => {\r\n          const { children, uuid, changeContent, checked, changeType, isShow, ...others } = item\r\n          const changeMap = this.$store.state.contactList.changeCode\r\n          const _list = (changeMap[uuid] || []).map(v => {\r\n            others[v.Code] = v.NewValue\r\n            return {\r\n              ChangeFieldCode: v.Code,\r\n              ChangeFieldName: v.Name,\r\n              BeforeValue: v.Value,\r\n              AfterValue: v.NewValue\r\n            }\r\n          })\r\n          others.MocContent = JSON.stringify(_list)\r\n          others.MocType = changeContent\r\n          return others\r\n        })\r\n        console.log(JSON.parse(JSON.stringify(submitTb)))\r\n        _form.Deepen_File_Url = null\r\n        _form.Deepen_File_Url_List = null\r\n      } else {\r\n        submitTb = this.tbData\r\n      }\r\n      const isReNew = this.isImportFile && this.changeMethod !== 3 && this.isEdit\r\n      const subObj = {\r\n        ..._form,\r\n        IsNewImportFile: isReNew,\r\n        Handle_UserName: localStorage.getItem('UserName'),\r\n        Moc_Type_Name: this.changeTypeList.find(item => item.Id === _form.Moc_Type_Id)?.Display_Name,\r\n        Change_Type: this.changeMethod === 1 ? 0 : this.changeMethod === 2 ? 1 : 2,\r\n        InstallUnit_Ids: Array.isArray(_form.InstallUnit_Ids) ? _form.InstallUnit_Ids.join(',') : _form.InstallUnit_Ids,\r\n        Is_Draft: isDraft,\r\n        OrderDetail: submitTb\r\n      }\r\n      if (this.changeMethod !== 3) {\r\n        subObj.Deepen_File_Url = this.filePath\r\n      }\r\n      await SaveMocOrder(subObj).then(async res => {\r\n        if (res.IsSucceed) {\r\n          if (isDraft) {\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            closeTagView(this.$store, this.$route)\r\n          } else {\r\n            if (!res.Data) {\r\n              this.$message({\r\n                message: '提交失败',\r\n                type: 'wrarning'\r\n              })\r\n              return\r\n            }\r\n            await this.submitCheck(res.Data)\r\n          }\r\n        } else {\r\n          if (res.Data && res.Data.Path) {\r\n            window.open(combineURL(this.$baseUrl, res.Data.Path), '_blank')\r\n          }\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async submitCheck(Id) {\r\n      await SubmitMocOrder({\r\n        Id\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '提交成功',\r\n            type: 'success'\r\n          })\r\n          closeTagView(this.$store, this.$route)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.searchForm.component_name = ''\r\n      this.searchForm.part_name = ''\r\n      this.searchForm.assembly_name = ''\r\n      this.searchForm.component_search_mode = 1\r\n      this.searchForm.part_search_mode = 1\r\n      this.searchForm.assembly_search_mode = 1\r\n      this.$refs?.tableRef?.setAllTreeExpand(false)\r\n      this.$refs?.tableRef?.clearFilter()\r\n    },\r\n    clearTb() {\r\n      this.tbData = []\r\n      this.defaultTbData = []\r\n      this.handleReset()\r\n    },\r\n    handleFilter() {\r\n      this.nameMapping = {\r\n        ComponentName: {},\r\n        SteelName: {},\r\n        PartName: {}\r\n      }\r\n      const changeMaps = this.$store.state.contactList.changeCode\r\n      Object.keys(changeMaps).forEach(uuid => {\r\n        const changeList = changeMaps[uuid]\r\n        changeList.forEach(item => {\r\n          if (item.Code === 'ComponentName') {\r\n            this.nameMapping.ComponentName[item.Value] = item.NewValue\r\n          } else if (item.Code === 'SteelName') {\r\n            this.nameMapping.SteelName[item.Value] = item.NewValue\r\n          } else if (item.Code === 'PartName') {\r\n            this.nameMapping.PartName[item.Value] = item.NewValue\r\n          }\r\n        })\r\n      })\r\n\r\n      const xTable = this.$refs.tableRef\r\n      const codeColumn = xTable.getColumnByField('CPCode')\r\n      const option = codeColumn.filters[0]\r\n      option.data = [this.searchForm.component_name, this.searchForm.assembly_name, this.searchForm.part_name]\r\n      option.checked = true\r\n      xTable.updateData()\r\n      this.$refs.tableRef.setAllTreeExpand(true)\r\n      this.$refs.tableRef.clearCheckboxRow()\r\n    },\r\n\r\n    getFeeGap(activity, index) {\r\n      if (index === 0) {\r\n        return activity.Fee || 0\r\n      } else {\r\n        const result = numeral(activity.Fee || 0)\r\n          .subtract(this.activities[index - 1].Fee || 0)\r\n\r\n        if (result.value() < 0) {\r\n          activity.isRed = true\r\n        } else if (result.value() > 0) {\r\n          activity.isBlue = true\r\n        }\r\n        return result.value() === 0 ? 0 : result.format('+0.[00]')\r\n      }\r\n    },\r\n    handleEdit(row) {\r\n      console.log(row, 'row')\r\n      this.dialogVisible = true\r\n      this.currentComponent = 'HandleEdit'\r\n      this.width = '50%'\r\n      this.title = `编辑（${this.getCpCode(row)}）`\r\n      this.$nextTick(() => {\r\n        const defaultRow = this.defaultTbData.find(item => item.uuid === row.uuid)\r\n        console.log(defaultRow, 'defaultRow')\r\n        this.$refs.content?.init(row, defaultRow, this.isEdit, this.tbData, this.allCodes)\r\n      })\r\n    },\r\n    handleDelete(row) {\r\n      console.log('row', row)\r\n      this.$confirm('确认要删除这条记录吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.deleteTableItem(row.uuid)\r\n      }).catch(() => {\r\n        // User canceled\r\n      })\r\n    },\r\n    handleRestore(row) {\r\n      this.restoreTableItem(row.uuid)\r\n    },\r\n    changeMethodFun(val) {\r\n      console.log('val', val)\r\n      const setTbCloumn = () => {\r\n        if (val === 3) {\r\n          const columnCode = ['CPCode', 'SetupPosition', 'Production_Status', 'changeContent']\r\n          const _columns = this.columns.filter(item => columnCode.includes(item.Code))\r\n          this.columns = _columns\r\n          this.$nextTick(_ => {\r\n            this.$refs.tableRef.refreshColumn()\r\n          })\r\n        } else if (val === 1) {\r\n          this.columns = deepClone(this.rootColumns)\r\n        } else {\r\n          this.columns = deepClone(this.rootColumns)\r\n        }\r\n        this.$store.dispatch('contactList/resetChangeCode')\r\n        this.changeMethod = val\r\n      }\r\n\r\n      if (this.tbData && this.tbData.length > 0) {\r\n        return this.$confirm('切换变更方式会清空当前已添加的变更明细，是否继续？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.tbData = []\r\n          setTbCloumn()\r\n          this.filePath = ''\r\n        }).catch(() => {\r\n\r\n        })\r\n      } else {\r\n        this.filePath = ''\r\n        setTbCloumn()\r\n      }\r\n    },\r\n    getMocModelList(list) {\r\n      const existingUuids = new Set(this.tbData.map(item => item.uuid))\r\n      list = list.filter(item => !existingUuids.has(item.MocIdBefore))\r\n\r\n      if (!list.length) {\r\n        return\r\n      }\r\n\r\n      list = list.map(item => {\r\n        // const curParent = this.findParentItem(item)\r\n        // if (curParent && curParent.changeType === 'isDelete') {\r\n        //   item.changeType = 'isDelete'\r\n        //   item.changeContent = this.getChangeTypeText(item.changeType)\r\n        //   item.isDisabled = true\r\n        //   this.deleteTableItem(item.uuid)\r\n        //   return {\r\n        //     ...item,\r\n        //     parentChildrenId: item.Id,\r\n        //     uuid: item.MocIdBefore,\r\n        //     checked: false,\r\n        //     CodeType: item.Type === 0 ? 1 : item.Type === 1 ? 2 : 3,\r\n        //     ...this.setAllWeight(item)\r\n        //   }\r\n        // } else {\r\n        this.updateItemChangeStatus(item, [])\r\n        return {\r\n          changeType: 'isNoChange',\r\n          changeContent: this.getChangeTypeText('isNoChange'),\r\n          ...item,\r\n          parentChildrenId: item.Id,\r\n          uuid: item.MocIdBefore,\r\n          checked: false,\r\n          CodeType: item.Type === 0 ? 1 : item.Type === 1 ? 2 : 3,\r\n          ...this.setAllWeight(item)\r\n        }\r\n        // }\r\n      })\r\n\r\n      this.tbData = [...this.tbData, ...list].sort((a, b) => b.Type - a.Type)\r\n\r\n      const _defaultTbData = this.defaultTbData || []\r\n      this.defaultTbData = JSON.parse(JSON.stringify([..._defaultTbData, ...list]))\r\n      this.setSameItems(this.tbData)\r\n    },\r\n    setSameItems(tbData) {\r\n      const changeInfos = { ...this.$store.state.contactList.changeCode }\r\n      const mocBeforeItems = tbData.filter(item => {\r\n        return !!changeInfos[item.uuid]\r\n      })\r\n      const isDeleteItems = tbData.filter(item => item.changeType === 'isDelete')\r\n      if (isDeleteItems.length) {\r\n        console.log(isDeleteItems, 'isDeleteItems')\r\n        const unitPart = isDeleteItems.filter(item => item.Type === 3)\r\n        if (unitPart.length) {\r\n          unitPart.forEach(item => {\r\n            const unitP = this.findParentItem(item)\r\n            if (unitP && unitP.changeType !== 'isDelete') {\r\n              const similarUnitPartItems = this.findSimilarItems(item)\r\n              if (similarUnitPartItems.length) {\r\n                similarUnitPartItems.forEach(similarItem => {\r\n                  const isSame = this.isSameParent(item, similarItem)\r\n                  if (!isSame) return\r\n                  this.$set(similarItem, 'changeType', 'isDelete')\r\n                  this.$set(similarItem, 'changeContent', this.getChangeTypeText('isDelete'))\r\n                })\r\n              }\r\n            }\r\n          })\r\n        }\r\n        // isDeleteItems.forEach(item => {\r\n        //   const similarItems = this.findSimilarItems(item)\r\n        //   if (similarItems.length) {\r\n        //     similarItems.forEach(similarItem => {\r\n        //       console.log(item.Code, 'similarItems')\r\n        //       this.$set(similarItem, 'changeType', item.changeType)\r\n        //       this.$set(similarItem, 'changeContent', item.changeContent)\r\n        //       // const isDisabled = this.isSameParent(item, similarItem)\r\n        //       // this.$set(similarItem, 'isDisabled', !isDisabled)\r\n        //       // if (isDisabled) {\r\n        // this.$set(similarItem, 'changeType', item.changeType)\r\n        // this.$set(similarItem, 'changeContent', item.changeContent)\r\n        //       // }\r\n        //     })\r\n        //   }\r\n        // })\r\n      }\r\n      if (!mocBeforeItems.length) return\r\n      mocBeforeItems.forEach(item => {\r\n        let _list = this.findSimilarItems(item)\r\n        _list = _list.filter(k => !changeInfos[k.uuid])\r\n        if (_list.length) {\r\n          _list.forEach(cur => {\r\n            if (this.isSameParent(item, cur)) {\r\n              const changeList = this.$store.state.contactList.changeCode[item.uuid]\r\n              changeList.forEach(change => {\r\n                cur[change.Code] = item[change.Code]\r\n              })\r\n\r\n              this.$store.dispatch('contactList/addChangeCode', {\r\n                uuid: cur.uuid,\r\n                list: changeList\r\n              })\r\n              console.log('cur', item.isDisabled)\r\n              if (item.changeType === 'isDelete') {\r\n                // this.$set(cur, 'isDisabled', item.isDisabled)\r\n\r\n              } else {\r\n                this.updateItemChangeStatus(cur, changeList)\r\n              }\r\n            } else {\r\n              const { SteelAmount, ...others } = item\r\n              const filteredList = (this.$store.state.contactList.changeCode[item.uuid] || []).filter(change => change.Code !== 'SteelAmount')\r\n              filteredList.forEach(change => {\r\n                cur[change.Code] = item[change.Code]\r\n              })\r\n              // cur.CPCode = item.CPCode\r\n              this.$store.dispatch('contactList/addChangeCode', {\r\n                uuid: cur.uuid,\r\n                list: filteredList\r\n              })\r\n              this.updateItemChangeStatus(cur, filteredList)\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    editInfo({ row, list }) {\r\n      console.log('editInfo row, list', row, list)\r\n      const _changeMaps = {}\r\n      list.forEach(item => {\r\n        _changeMaps[item.Code] = item.NewValue\r\n      })\r\n      // this.resetDefaultVal()\r\n\r\n      const existingChanges = this.$store.state.contactList.changeCode[row.uuid] || []\r\n      const existingChangeCodes = existingChanges.map(change => change.Code)\r\n\r\n      const removedChangeCodes = existingChangeCodes.filter(code => !list.some(item => item.Code === code))\r\n      console.log('已移除的字段', removedChangeCodes)\r\n\r\n      if (removedChangeCodes.length) {\r\n        const defaultRow = this.defaultTbData.find(item => item.uuid === row.uuid)\r\n        removedChangeCodes.forEach(code => {\r\n          console.log(`重置字段 ${code} 为原始值:`, defaultRow[code])\r\n          _changeMaps[code] = defaultRow[code]\r\n        })\r\n      }\r\n      console.log('_changeMaps', JSON.parse(JSON.stringify(_changeMaps)))\r\n\r\n      // 批量更新表格项\r\n      this.batchUpdateTableItem(row.uuid, _changeMaps)\r\n      // this.updateCodesName(row, _changeMaps)\r\n    },\r\n    // updateCodesName(targetItem, _changeMaps) {\r\n    //   if (_changeMaps.SteelName) {\r\n    //     targetItem.SteelName = _changeMaps.SteelName\r\n    //     targetItem.CPCode = _changeMaps.SteelName\r\n    //   } else if (_changeMaps.ComponentName) {\r\n    //     targetItem.ComponentName = _changeMaps.ComponentName\r\n    //     targetItem.CPCode = _changeMaps.ComponentName\r\n    //   } else if (_changeMaps.PartName) {\r\n    //     targetItem.PartName = _changeMaps.PartName\r\n    //     targetItem.CPCode = _changeMaps.PartName\r\n    //   } else {\r\n    //     const defaultRow = this.defaultTbData.find(item => item.uuid === targetItem.uuid)\r\n    //     console.log('defaultRow', JSON.parse(JSON.stringify(defaultRow)))\r\n    //     if (defaultRow) {\r\n    //       targetItem.SteelName = defaultRow.SteelName\r\n    //       targetItem.CPCode = defaultRow.CPCode\r\n    //     }\r\n    //   }\r\n    //   console.log('targetItem', JSON.parse(JSON.stringify(targetItem)))\r\n    //   const _list = this.findSimilarItems(targetItem)\r\n    //   if (_list.length) {\r\n    //     _list.forEach(item => {\r\n    //       item.SteelName = targetItem.SteelName\r\n    //       item.CPCode = targetItem.CPCode\r\n    //     })\r\n    //   }\r\n    // },\r\n    handleCancelChange() {\r\n      const selectedRecords = []\r\n      const getIds = (array) => {\r\n        if (!array || !array.length) return\r\n        array.forEach(item => {\r\n          selectedRecords.push(item.uuid)\r\n          if (item.children && item.children.length) {\r\n            getIds(item.children)\r\n          }\r\n        })\r\n      }\r\n      getIds(this.multipleSelection)\r\n      console.log('selectedRecords', selectedRecords)\r\n      selectedRecords.forEach(item => {\r\n        this.$store.dispatch('contactList/delChangeCode', item)\r\n      })\r\n      this.tbData = this.tbData.filter(item => !selectedRecords.includes(item.uuid))\r\n      this.defaultTbData = this.defaultTbData.filter(item => !selectedRecords.includes(item.uuid))\r\n      this.multipleSelection = []\r\n    },\r\n    multiSelectedChange(array) {\r\n      console.log('array', array)\r\n      console.log('array.records', this.$refs.tableRef.getCheckboxRecords(true))\r\n      const { records } = array\r\n      this.multipleSelection = array.records\r\n    },\r\n    handelFilePath() {\r\n      GetOssUrl({\r\n        url: this.filePath\r\n      }).then(res => {\r\n        window.open(res.Data)\r\n      })\r\n    },\r\n    getChangeStyle(changeName) {\r\n      const arr = changeName.split(',')\r\n      const rusult = ['cs-c-box']\r\n      if (arr.includes(changeType.isAdd)) {\r\n        rusult.push('cs-change-green')\r\n      } else if (arr.includes(changeType.isAdjust)) {\r\n        rusult.push('cs-change-yellow')\r\n      } else if (arr.includes(changeType.isDecrease) || arr.includes(changeType.isIncrease)) {\r\n        rusult.push('cs-change-blue')\r\n      } else if (arr.includes(changeType.isDelete)) {\r\n        rusult.push('cs-change-red')\r\n      } else {\r\n        rusult.push('cs-default')\r\n      }\r\n      return rusult\r\n    },\r\n    getCpCode(row) {\r\n      if (row.Type === 0) {\r\n        return row.SteelName\r\n      } else if (row.Type === 1) {\r\n        return row.ComponentName\r\n      } else {\r\n        return row.PartName\r\n      }\r\n    },\r\n    handleShow(row) {\r\n      const changeList = this.$store.state.contactList.changeCode[row.uuid]\r\n      this.changeRowContentList = changeList || []\r\n    },\r\n    filterNameMethod({ option, values, cellValue, row, column }) {\r\n      const result = this.filterCustom(row)\r\n      return result\r\n    },\r\n    filterCustom(row) {\r\n      const { component_name, component_search_mode, assembly_name,\r\n        assembly_search_mode, part_name, part_search_mode } = this.searchForm\r\n\r\n      const _ComponentName = this.nameMapping.ComponentName[row.ComponentName] || row.ComponentName || ''\r\n      const _SteelName = this.nameMapping.SteelName[row.SteelName] || row.SteelName || ''\r\n      const _PartName = this.nameMapping.PartName[row.PartName] || row.PartName || ''\r\n\r\n      let partMatch = true\r\n\r\n      if (part_name) {\r\n        if (part_search_mode === 1) {\r\n          partMatch = _PartName.includes(part_name)\r\n        } else {\r\n          partMatch = _PartName === part_name\r\n        }\r\n      }\r\n      let assemblyMatch = true\r\n      if (assembly_name) {\r\n        if (assembly_search_mode === 1) {\r\n          assemblyMatch = _ComponentName.includes(assembly_name)\r\n        } else {\r\n          assemblyMatch = _ComponentName === assembly_name\r\n        }\r\n      }\r\n      let componentMatch = true\r\n      if (component_name) {\r\n        if (component_search_mode === 1) {\r\n          componentMatch = _SteelName.includes(component_name)\r\n        } else {\r\n          componentMatch = _SteelName === component_name\r\n        }\r\n      }\r\n      // console.log(componentMatch, assemblyMatch, partMatch)\r\n\r\n      const result = componentMatch && assemblyMatch && partMatch\r\n      if (result) {\r\n        return true\r\n      } else {\r\n        return false\r\n      }\r\n    },\r\n    installChange(arr) {\r\n      if (!arr || !arr.length) {\r\n        this.clearTb()\r\n        return\r\n      }\r\n      this.tbData = this.tbData.filter(item => arr.some(id => id === item.InstallUnit_Id))\r\n      this.defaultTbData = this.defaultTbData.filter(item => arr.some(id => id === item.InstallUnit_Id))\r\n    },\r\n    afterApproval() {\r\n      closeTagView(this.$store, this.$route)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.cs-type {\r\n  padding: 2px;\r\n  font-weight: 400;\r\n  font-size: 12px;\r\n  color: #146EB4;\r\n  border-radius: 4px;\r\n  margin-right: 4px;\r\n  background-color: rgba(66, 107, 216, .1);\r\n}\r\n\r\n.cs-change {\r\n  padding: 2px 4px;\r\n  font-weight: 400;\r\n  font-size: 12px;\r\n  color: #298DFF;\r\n  border-radius: 4px;\r\n  background-color: rgba(41, 141, 255, .1)\r\n}\r\n\r\n.cs-c-box {\r\n  padding: 2px 4px;\r\n  font-weight: 400;\r\n  font-size: 12px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.cs-change-green {\r\n  color: #ffffff;\r\n  background-color: #3ECC93\r\n}\r\n\r\n.cs-change-green-p {\r\n  color: #3ECC93;\r\n  background-color: rgba(62, 204, 147, .1);\r\n}\r\n\r\n.cs-change-blue {\r\n  color: #ffffff;\r\n  background-color: #298DFF\r\n}\r\n\r\n.cs-change-blue-p {\r\n  color: #298DFF;\r\n  background-color: rgba(41, 141, 255, .1)\r\n}\r\n\r\n.cs-change-red {\r\n  color: #ffffff;\r\n  background-color: #FB6B7F\r\n}\r\n\r\n.cs-change-red-p {\r\n  color: #FB6B7F;\r\n  background-color: rgba(251, 107, 127, .1)\r\n}\r\n\r\n.cs-default {\r\n  color: #ffffff;\r\n  background-color: #8E95AA\r\n}\r\n\r\n.cs-default-p {\r\n  color: #8E95AA;\r\n  background-color: rgba(142, 149, 170, .1)\r\n}\r\n\r\n.cs-change-yellow {\r\n  color: #ffffff;\r\n  background-color:  #FB8F00;\r\n}\r\n.cs-green {\r\n  background: rgba(62, 204, 147, .1);\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  color: #3ECC93;\r\n  padding: 3px 10px;\r\n}\r\n\r\n.cs-yellow {\r\n  background: rgba(241, 180, 48, .1);\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  color: #F1B430;\r\n  padding: 3px 10px;\r\n}\r\n\r\n.cs-red {\r\n  background: rgba(251, 107, 127, .1);\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  color: #FB6B7F;\r\n  padding: 3px 10px;\r\n}\r\n\r\n.m-4 {\r\n  margin: 0 4px;\r\n}\r\n\r\n.cs-title {\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  color: rgba(34, 40, 52, 0.85);\r\n}\r\n\r\n.page-container {\r\n  margin: 16px;\r\n\r\n  .form-x {\r\n    background-color: #FFFFFF;\r\n    padding: 16px;\r\n  }\r\n\r\n  .cs-main {\r\n    margin-top: 16px;\r\n    background-color: #FFFFFF;\r\n    padding: 16px;\r\n  }\r\n}\r\n\r\n.cs-fee{\r\n  margin-top: 16px;\r\n  background-color: #FFFFFF;\r\n  padding: 16px 16px 0 16px;\r\n  .line-content{\r\n    align-items: center;\r\n    display: flex;\r\n  }\r\n  .cs-title{\r\n    margin-right: 8px;\r\n  }\r\n  .cs-label{\r\n    font-size: 14px;\r\n  }\r\n  .fw{\r\n    font-weight: bold;\r\n  }\r\n  .cs-blue{\r\n    color: #298DFF\r\n  }\r\n  .cs-red{\r\n    color: #FB6B7F\r\n  }\r\n  .fee-name{\r\n    font-weight: bold;\r\n    font-size: 14px;\r\n    color: rgba(34,40,52,0.85);\r\n    margin-right: 32px;\r\n  }\r\n  .fee-sub{\r\n    font-weight: bold;\r\n    font-size: 12px;\r\n    color: rgba(34,40,52,0.65);\r\n    margin-right: 32px;\r\n  }\r\n  .fee-num{\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    margin-right: 32px;\r\n  }\r\n  .fee-time{\r\n    font-weight: 400;\r\n    margin-right: 32px;\r\n    font-size: 12px;\r\n    color: rgba(34,40,52,0.65);\r\n  }\r\n  .fee-remark{\r\n    font-weight: bold;\r\n    font-size: 12px;\r\n    color: rgba(34,40,52,0.65);\r\n  }\r\n  .circle {\r\n    width: 14px;\r\n    height: 14px;\r\n    border-radius: 50%;\r\n    border: 4px solid #458CF7;\r\n    background-color: white;\r\n  }\r\n  ::v-deep{\r\n    .el-timeline-item__tail{\r\n      height: 37%;\r\n      margin: 18px 0;\r\n      width: 1px;\r\n      left: 5px;\r\n      border: 1px solid rgba(41, 141, 255, 0.3);\r\n    }\r\n  }\r\n}\r\n\r\n.el-divider--horizontal {\r\n  margin: 16px 0;\r\n}\r\n\r\nfooter {\r\n  margin-top: 16px;\r\n  text-align: right;\r\n}\r\n\r\n.cs-toolBar {\r\n  ::v-deep {\r\n    .vxe-button--icon.vxe-icon-custom-column{\r\n      display: none;\r\n    }\r\n\r\n    .vxe-button.type--button.is--circle {\r\n      width: 97px;\r\n      z-index: 0;\r\n      border-radius: 4px;\r\n    }\r\n\r\n    .el-form-item {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n.el-tree-select{\r\n  ::v-deep{\r\n    .el-select{\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n\r\n.change-method-form {\r\n  margin-bottom: 0px;\r\n}\r\n.cs-tree-table{\r\n ::v-deep{\r\n   .vxe-tree-cell{\r\n     text-align: left !important;\r\n   }\r\n   .vxe-checkbox--label{\r\n     color:#333333;\r\n   }\r\n   .col--checkbox{\r\n    .vxe-cell{\r\n      padding-left: 10px !important;\r\n    }\r\n   }\r\n }\r\n}\r\n\r\n.z-upload.hiddenBtn{\r\n  ::v-deep{\r\n    .el-upload{\r\n      display: none;\r\n    }\r\n    .el-upload-list__item:first-child{\r\n      margin-top: 4px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
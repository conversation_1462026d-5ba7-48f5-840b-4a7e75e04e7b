{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\quality_Inspection\\batch-tracking\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\quality_Inspection\\batch-tracking\\index.vue", "mtime": 1757468113496}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRJbnN0YWxsVW5pdFBhZ2VMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL2luc3RhbGwtdW5pdCcNCmltcG9ydCB7IEdlQXJlYVRyZWVzLCBHZXRQcm9qZWN0UGFnZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vcHJvamVjdCcNCmltcG9ydCB7IHRhYmxlUGFnZVNpemUgfSBmcm9tICdAL3ZpZXdzL1BSTy9zZXR0aW5nJw0KaW1wb3J0IHsNCiAgQmF0Y2hVcGRhdGVUcmFja0VkaXRhYmxlRmllbGRzLA0KICBFeHBvcnRzdEJhdGNoVHJhY2tGcm9tU291cmNlQXN5bmMsDQogIEluc2VydEJhdGNoVHJhY2ssDQogIFNhdmVRSVJlcG9ydERhdGENCn0gZnJvbSAnQC9hcGkvUFJPL3F1YWxpdHlJbnNwZWN0L3F1YWxpdHktbWFuYWdlbWVudCcNCmltcG9ydCB7IEdldFdvcmtzaG9wUGFnZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vYmFzaWMtaW5mb3JtYXRpb24vd29ya3Nob3AnDQppbXBvcnQgeyBjb21iaW5lVVJMIH0gZnJvbSAnQC91dGlscycNCmltcG9ydCBnZXRUYkluZm8gZnJvbSAnQC9taXhpbnMvUFJPL2dldC10YWJsZS1pbmZvJw0KaW1wb3J0IER5bmFtaWNUYWJsZUZpZWxkcyBmcm9tICdAL2NvbXBvbmVudHMvRHluYW1pY1RhYmxlRmllbGRzL2luZGV4LnZ1ZScNCmltcG9ydCB7IEdldFBhcnRUeXBlTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9wYXJ0VHlwZScNCmltcG9ydCBQYWdpbmF0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9QYWdpbmF0aW9uL2luZGV4LnZ1ZScNCmltcG9ydCBtb21lbnQgZnJvbSAnbW9tZW50Jw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdQUk9CYXRjaFRyYWNraW5nMScsDQogIGNvbXBvbmVudHM6IHsgRHluYW1pY1RhYmxlRmllbGRzLCBQYWdpbmF0aW9uIH0sDQogIG1peGluczogW2dldFRiSW5mb10sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGRhdGVSYW5nZTogJycsDQogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGV4cG9ydExvYWRpbmc6IGZhbHNlLA0KICAgICAgZ3JpZENvZGU6ICcnLA0KICAgICAgY3VycmVudENvbXBvbmVudDogJycsDQogICAgICB0aXRsZTogJycsDQogICAgICBjdXJyZW50Um93OiBudWxsLA0KICAgICAgZm9ybTogew0KICAgICAgICBQcm9qZWN0X0lkOiAnJywNCiAgICAgICAgQXJlYV9JZDogJycsDQogICAgICAgIEluc3RhbGxVbml0X0lkOiAnJywNCiAgICAgICAgV29ya3Nob3BfSWQ6ICcnLA0KICAgICAgICBEcmF3aW5nUmVjZWl2ZURhdGVTdGFydDogJycsDQogICAgICAgIERyYXdpbmdSZWNlaXZlRGF0ZUVuZDogJycNCiAgICAgIH0sDQoNCiAgICAgIHByb2plY3RPcHRpb246IFtdLCAvLyBEZWZpbmUgb3B0aW9ucyBhcyBuZWVkZWQNCiAgICAgIHRyZWVQYXJhbXNBcmVhOiB7DQogICAgICAgICdkZWZhdWx0LWV4cGFuZC1hbGwnOiB0cnVlLA0KICAgICAgICBmaWx0ZXJhYmxlOiBmYWxzZSwNCiAgICAgICAgY2xpY2tQYXJlbnQ6IHRydWUsDQogICAgICAgIGRhdGE6IFtdLA0KICAgICAgICBwcm9wczogew0KICAgICAgICAgIGRpc2FibGVkOiAnZGlzYWJsZWQnLA0KICAgICAgICAgIGNoaWxkcmVuOiAnQ2hpbGRyZW4nLA0KICAgICAgICAgIGxhYmVsOiAnTGFiZWwnLA0KICAgICAgICAgIHZhbHVlOiAnSWQnDQogICAgICAgIH0NCiAgICAgIH0sDQogICAgICBtdWx0aXBsZVNlbGVjdGlvbjogW10sDQogICAgICBhcmVhT3B0aW9uOiBbXSwNCiAgICAgIGluc3RhbGxPcHRpb246IFtdLA0KICAgICAgd29ya3Nob3BPcHRpb25zOiBbXSwNCiAgICAgIGNvbHVtbnM6IFtdLA0KICAgICAgdGFibGVEYXRhOiBbXSwNCiAgICAgIGN1c3RvbUNvbHVtbnM6IFtdLA0KICAgICAgdGJMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHRhYmxlUGFnZVNpemU6IHRhYmxlUGFnZVNpemUsDQogICAgICBxdWVyeUluZm86IHsNCiAgICAgICAgUGFnZTogMSwNCiAgICAgICAgUGFnZVNpemU6IHRhYmxlUGFnZVNpemVbMF0NCiAgICAgIH0sDQogICAgICB0b3RhbDogMCwNCiAgICAgIHR5cGVMaXN0OiBbXQ0KICAgIH0NCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICBkYXRlUmFuZ2U6IHsNCiAgICAgIGhhbmRsZXIobmV3VmFsdWUpIHsNCiAgICAgICAgaWYgKG5ld1ZhbHVlKSB7DQogICAgICAgICAgY29uc3QgW3N0YXJ0LCBlbmRdID0gbmV3VmFsdWUNCiAgICAgICAgICB0aGlzLmZvcm0uRHJhd2luZ1JlY2VpdmVEYXRlU3RhcnQgPSBzdGFydA0KICAgICAgICAgIHRoaXMuZm9ybS5EcmF3aW5nUmVjZWl2ZURhdGVFbmQgPSBlbmQNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmZvcm0uRHJhd2luZ1JlY2VpdmVEYXRlU3RhcnQgPSAnJw0KICAgICAgICAgIHRoaXMuZm9ybS5EcmF3aW5nUmVjZWl2ZURhdGVFbmQgPSAnJw0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgZGVlcDogdHJ1ZQ0KICAgIH0NCiAgfSwNCiAgYXN5bmMgY3JlYXRlZCgpIHsNCiAgICB0cnkgew0KICAgICAgdGhpcy50YkxvYWRpbmcgPSB0cnVlDQogICAgICB0aGlzLmdyaWRDb2RlID0gJ1BST0JhdGNoVHJhY2tpbmcnDQogICAgICB0aGlzLmluaXRDb2x1bW4oKQ0KICAgICAgYXdhaXQgdGhpcy5nZXRQcm9qZWN0T3B0aW9uKCkNCiAgICAgIHRoaXMuZ2V0V29ya1Nob3AoKQ0KICAgICAgdGhpcy5mZXRjaERhdGEoMSkNCiAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgdGhpcy50YkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZHVyaW5nIGNyZWF0ZWQgbGlmZWN5Y2xlOicsIGVycm9yKQ0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGFzeW5jIGluaXRDb2x1bW4oKSB7DQogICAgICBhd2FpdCB0aGlzLmdldFRhYmxlQ29uZmlnKHRoaXMuZ3JpZENvZGUsIHRydWUpDQogICAgICBjb25zdCByZXMgPSBhd2FpdCBHZXRQYXJ0VHlwZUxpc3QoeyBQYXJ0X0dyYWRlOiAwIH0pDQogICAgICB0aGlzLnR5cGVMaXN0ID0gcmVzLkRhdGENCiAgICAgIHRoaXMuY29sdW1ucyA9IHRoaXMuY29sdW1ucy5maWx0ZXIodiA9PiB2LklzX0Rpc3BsYXkpDQogICAgICBjb25zdCBleHBhbmRlZENvbHVtbnMgPSBbXQ0KICAgICAgdGhpcy50eXBlTGlzdC5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAvLyDmt7vliqDmlbDph4/liJcNCiAgICAgICAgZXhwYW5kZWRDb2x1bW5zLnB1c2goew0KICAgICAgICAgIENvZGU6IGAke2l0ZW0uQ29kZX1fQ291bnRgLA0KICAgICAgICAgIERpc3BsYXlfTmFtZTogYCR7aXRlbS5OYW1lfeaVsOmHj2AsDQogICAgICAgICAgSXNfRGlzcGxheTogdHJ1ZSwNCiAgICAgICAgICBJc19FZGl0OiBmYWxzZSwNCiAgICAgICAgICBJc19Gcm96ZW46IGZhbHNlLA0KICAgICAgICAgIFdpZHRoOiAxNTANCiAgICAgICAgfSkNCiAgICAgICAgLy8g5re75Yqg6YeN6YeP5YiXDQogICAgICAgIGV4cGFuZGVkQ29sdW1ucy5wdXNoKHsNCiAgICAgICAgICBDb2RlOiBgJHtpdGVtLkNvZGV9X1dlaWdodGAsDQogICAgICAgICAgRGlzcGxheV9OYW1lOiBgJHtpdGVtLk5hbWV96YeN6YePKGtnKWAsDQogICAgICAgICAgSXNfRGlzcGxheTogdHJ1ZSwNCiAgICAgICAgICBJc19FZGl0OiBmYWxzZSwNCiAgICAgICAgICBJc19Gcm96ZW46IGZhbHNlLA0KICAgICAgICAgIFdpZHRoOiAxNTANCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgICB0aGlzLmNvbHVtbnMuc3BsaWNlKDUsIDAsIC4uLmV4cGFuZGVkQ29sdW1ucykNCiAgICB9LA0KICAgIGhhbmRsZUNsb3NlKCkgew0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICB9LA0KICAgIGZldGNoRGF0YShwYWdlKSB7DQogICAgICB0aGlzLnRiTG9hZGluZyA9IHRydWUNCiAgICAgIHBhZ2UgJiYgKHRoaXMucXVlcnlJbmZvLlBhZ2UgPSBwYWdlKQ0KICAgICAgSW5zZXJ0QmF0Y2hUcmFjayh7DQogICAgICAgIFF1ZXJ5UGFyYW1zOiB0aGlzLmZvcm0sDQogICAgICAgIFBhZ2VJbmZvOiB0aGlzLnF1ZXJ5SW5mbw0KICAgICAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMudGFibGVEYXRhID0gcmVzLkRhdGEuRGF0YS5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgICBpdGVtLkRyYXdpbmdfUmVjZWl2ZV9EYXRlID0gaXRlbS5EcmF3aW5nX1JlY2VpdmVfRGF0ZSA/IG1vbWVudChpdGVtLkRyYXdpbmdfUmVjZWl2ZV9EYXRlKS5mb3JtYXQoJ1lZWVktTU0tREQnKSA6ICcnDQogICAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy50b3RhbCA9IHJlcy5EYXRhLlRvdGFsQ291bnQNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pLmZpbmFsbHkoKCkgPT4gew0KICAgICAgICB0aGlzLnRiTG9hZGluZyA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQogICAgZ2V0V29ya1Nob3AoKSB7DQogICAgICBHZXRXb3Jrc2hvcFBhZ2VMaXN0KHsgUGFnZTogMSwgUGFnZVNpemU6IC0xIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICBpZiAoIXJlcz8uRGF0YT8uRGF0YSkgew0KICAgICAgICAgICAgdGhpcy53b3Jrc2hvcE9wdGlvbnMgPSBbXQ0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLndvcmtzaG9wT3B0aW9ucyA9IHJlcy5EYXRhLkRhdGEubWFwKGl0ZW0gPT4gew0KICAgICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgICAgSWQ6IGl0ZW0uSWQsDQogICAgICAgICAgICAgIERpc3BsYXlfTmFtZTogaXRlbS5EaXNwbGF5X05hbWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy4kcmVmcy5mb3JtUmVmLnJlc2V0RmllbGRzKCkNCiAgICAgIHRoaXMuZm9ybS5Qcm9qZWN0X0lkID0gJycNCiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gJycNCiAgICAgIHRoaXMuZm9ybS5EcmF3aW5nUmVjZWl2ZURhdGVTdGFydCA9ICcnDQogICAgICB0aGlzLmZvcm0uRHJhd2luZ1JlY2VpdmVEYXRlRW5kID0gJycNCiAgICAgIHRoaXMuZmV0Y2hEYXRhKDEpDQogICAgfSwNCiAgICBzZWFyY2goKSB7DQogICAgICB0aGlzLmZldGNoRGF0YSgxKQ0KICAgIH0sDQogICAgZ2V0UHJvamVjdE9wdGlvbigpIHsNCiAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7DQogICAgICAgIEdldFByb2plY3RQYWdlTGlzdCh7DQogICAgICAgICAgUGFnZTogMSwNCiAgICAgICAgICBQYWdlU2l6ZTogLTENCiAgICAgICAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICB0aGlzLnByb2plY3RPcHRpb24gPSByZXMuRGF0YT8uRGF0YSB8fCBbXQ0KICAgICAgICAgICAgaWYgKHRoaXMucHJvamVjdE9wdGlvbi5sZW5ndGgpIHsNCiAgICAgICAgICAgICAgdGhpcy5mb3JtLlByb2plY3RfSWQgPSAnJw0KICAgICAgICAgICAgICB0aGlzLmdldEFyZWFMaXN0KCkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgICByZXNvbHZlKCkNCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfSwNCiAgICBwcm9qZWN0Q2hhbmdlKGUpIHsNCiAgICAgIHRoaXMuZm9ybS5BcmVhX0lkID0gJycNCiAgICAgIHRoaXMuZm9ybS5JbnN0YWxsVW5pdF9JZCA9ICcnDQogICAgICB0aGlzLnRyZWVQYXJhbXNBcmVhLmRhdGEgPSBbXQ0KICAgICAgdGhpcy5pbnN0YWxsT3B0aW9uID0gW10NCiAgICAgIHRoaXMuZm9ybS5JbnN0YWxsVW5pdF9JZCA9ICcnDQogICAgICBpZiAoZSkgew0KICAgICAgICB0aGlzLmdldEFyZWFMaXN0KCkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgYXJlYUNsZWFyKCkgew0KICAgICAgdGhpcy5mb3JtLkFyZWFfSWQgPSAnJw0KICAgICAgdGhpcy5mb3JtLkluc3RhbGxVbml0X0lkID0gJycNCiAgICB9LA0KICAgIGdldEFyZWFMaXN0KCkgew0KICAgICAgaWYgKCF0aGlzLmZvcm0uUHJvamVjdF9JZCkgew0KICAgICAgICB0aGlzLiRyZWZzLnRyZWVTZWxlY3RBcmVhPy50cmVlRGF0YVVwZGF0ZUZ1bihbXSkNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICBHZUFyZWFUcmVlcyh7DQogICAgICAgIHByb2plY3RJZDogdGhpcy5mb3JtLlByb2plY3RfSWQNCiAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICBjb25zdCB0cmVlID0gcmVzLkRhdGENCiAgICAgICAgICB0aGlzLnNldERpc2FibGVkVHJlZSh0cmVlKQ0KICAgICAgICAgIHRoaXMudHJlZVBhcmFtc0FyZWEuZGF0YSA9IHRyZWUNCiAgICAgICAgICB0aGlzLiRuZXh0VGljayhfID0+IHsNCiAgICAgICAgICAgIHRoaXMuJHJlZnMudHJlZVNlbGVjdEFyZWE/LnRyZWVEYXRhVXBkYXRlRnVuKHRyZWUpDQogICAgICAgICAgfSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBzZXREaXNhYmxlZFRyZWUocm9vdCkgew0KICAgICAgaWYgKCFyb290KSByZXR1cm4NCiAgICAgIHJvb3QuZm9yRWFjaCgoZWxlbWVudCkgPT4gew0KICAgICAgICBjb25zdCB7IENoaWxkcmVuIH0gPSBlbGVtZW50DQogICAgICAgIGlmIChDaGlsZHJlbiAmJiBDaGlsZHJlbi5sZW5ndGgpIHsNCiAgICAgICAgICBlbGVtZW50LmRpc2FibGVkID0gdHJ1ZQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGVsZW1lbnQuZGlzYWJsZWQgPSBmYWxzZQ0KICAgICAgICAgIHRoaXMuc2V0RGlzYWJsZWRUcmVlKENoaWxkcmVuKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgYXJlYUNoYW5nZSgpIHsNCiAgICAgIHRoaXMuZm9ybS5JbnN0YWxsVW5pdF9JZCA9ICcnDQogICAgICB0aGlzLmdldEluc3RhbGwoKQ0KICAgIH0sDQogICAgZ2V0SW5zdGFsbCgpIHsNCiAgICAgIEdldEluc3RhbGxVbml0UGFnZUxpc3Qoew0KICAgICAgICBBcmVhX0lkOiB0aGlzLmZvcm0uQXJlYV9JZCwNCiAgICAgICAgUGFnZTogMSwNCiAgICAgICAgUGFnZVNpemU6IC0xDQogICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy5pbnN0YWxsT3B0aW9uID0gcmVzLkRhdGEuRGF0YQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgZXhwb3J0RGF0YSgpIHsNCiAgICAgIHRoaXMuZXhwb3J0TG9hZGluZyA9IHRydWUNCiAgICAgIEV4cG9ydHN0QmF0Y2hUcmFja0Zyb21Tb3VyY2VBc3luYyh7DQogICAgICAgIFF1ZXJ5UGFyYW1zOiB0aGlzLmZvcm0NCiAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6ICflr7zlh7rmiJDlip8nLA0KICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgICAgfSkNCiAgICAgICAgICB3aW5kb3cub3Blbihjb21iaW5lVVJMKHRoaXMuJGJhc2VVcmwsIHJlcy5EYXRhKSwgJ19ibGFuaycpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KS5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgdGhpcy5leHBvcnRMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICB0YlNlbGVjdENoYW5nZShhcnJheSkgew0KICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbiA9IGFycmF5LnJlY29yZHMNCiAgICB9LA0KDQogICAgaGFuZGxlU3VibWl0KGZvcm1EYXRhKSB7DQogICAgICAvLyBDYWxsIHRoZSBBUEkgdG8gc2F2ZSB0aGUgdXBkYXRlZCBkYXRhDQogICAgICB0aGlzLnRiTG9hZGluZyA9IHRydWUNCiAgICAgIFNhdmVRSVJlcG9ydERhdGEoZm9ybURhdGEpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6ICfkv53lrZjmiJDlip8nLA0KICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLmhhbmRsZUNsb3NlKCkNCiAgICAgICAgICB0aGlzLmZldGNoRGF0YSgxKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UgfHwgJ+S/neWtmOWksei0pScsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAn5L+d5a2Y5aSx6LSlOiAnICsgZXJyb3IsDQogICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICB9KQ0KICAgICAgfSkuZmluYWxseSgoKSA9PiB7DQogICAgICAgIHRoaXMudGJMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOmHjeaWsOS/neWtmOaUueihjA0KICAgIHNhdmVSb3cocm93LCBmaWVsZENvZGUpIHsNCiAgICAgIEJhdGNoVXBkYXRlVHJhY2tFZGl0YWJsZUZpZWxkcyh7DQogICAgICAgIGl0ZW1zOiBbew0KICAgICAgICAgIHByb2plY3RfSWQ6IHJvdy5Qcm9qZWN0X0lkLA0KICAgICAgICAgIGFyZWFfSWQ6IHJvdy5BcmVhX0lkLA0KICAgICAgICAgIGluc3RhbGxVbml0X0lkOiByb3cuSW5zdGFsbFVuaXRfSWQsDQogICAgICAgICAgd29ya3Nob3BfaWQ6IHJvdy5Xb3Jrc2hvcF9JZCwNCiAgICAgICAgICBzdHJ1Y3R1cmVfVHlwZTogcm93LlN0cnVjdHVyZV9UeXBlLA0KICAgICAgICAgIGRyYXdpbmdfUmVjZWl2ZV9EYXRlOiByb3cuRHJhd2luZ19SZWNlaXZlX0RhdGUsDQogICAgICAgICAgY29uY2x1c2lvbjogcm93LkNvbmNsdXNpb24NCiAgICAgICAgfV0NCiAgICAgIH0pDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/quality_Inspection/batch-tracking", "sourcesContent": ["<template>\r\n  <div class=\"container abs100\">\r\n    <div class=\"filter-bar\">\r\n      <el-form ref=\"formRef\" :model=\"form\" label-width=\"80px\" inline>\r\n        <el-form-item label=\"项目名称\" prop=\"Project_Id\">\r\n          <el-select v-model=\"form.Project_Id\" placeholder=\"请选择\" clearable @change=\"projectChange\">\r\n            <el-option\r\n              v-for=\"item in projectOption\"\r\n              :key=\"item.Id\"\r\n              :label=\"item.Short_Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n\r\n        </el-form-item>\r\n        <el-form-item label=\"区域\" prop=\"Area_Id\">\r\n          <el-tree-select\r\n            ref=\"treeSelectArea\"\r\n            v-model=\"form.Area_Id\"\r\n            :disabled=\"!form.Project_Id\"\r\n            :select-params=\"{\r\n              clearable: true,\r\n            }\"\r\n            class=\"cs-tree-x w100\"\r\n            :tree-params=\"treeParamsArea\"\r\n            @select-clear=\"areaClear\"\r\n            @node-click=\"areaChange\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"批次\" prop=\"InstallUnit_Id\">\r\n          <el-select v-model=\"form.InstallUnit_Id\" clearable placeholder=\"请选择\" :disabled=\"!form.Area_Id\">\r\n            <el-option v-for=\"option in installOption\" :key=\"option.Id\" :label=\"option.Name\" :value=\"option.Id\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"制作车间\" prop=\"Workshop_Id\">\r\n          <el-select v-model=\"form.Workshop_Id\" clearable class=\"w100\" placeholder=\"请选择\">\r\n            <el-option\r\n              v-for=\"item in workshopOptions\"\r\n              :key=\"item.Id\"\r\n              :label=\"item.Display_Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"收图日期\">\r\n          <el-date-picker\r\n            v-model=\"dateRange\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"daterange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button :disabled=\"tbLoading\" @click=\"reset\">重置</el-button>\r\n          <el-button type=\"primary\" :disabled=\"tbLoading\" @click=\"search\">搜索</el-button>\r\n        </el-form-item>\r\n\r\n      </el-form>\r\n    </div>\r\n\r\n    <div class=\"main-content\">\r\n      <vxe-toolbar>\r\n        <template #buttons>\r\n          <el-button type=\"success\" :loading=\"exportLoading\" @click=\"exportData\">导出数据</el-button>\r\n        </template>\r\n      </vxe-toolbar>\r\n      <div class=\"tb-x\">\r\n        <vxe-table\r\n          ref=\"xTable\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          :data=\"tableData\"\r\n          class=\"cs-vxe-table\"\r\n          :row-config=\"{ isCurrent: true, isHover: true }\"\r\n          align=\"left\"\r\n          height=\"auto\"\r\n          show-overflow\r\n          :loading=\"tbLoading\"\r\n          stripe\r\n          size=\"medium\"\r\n          resizable\r\n          :tooltip-config=\"{ enterable: true }\"\r\n          :edit-config=\"{\r\n            trigger: 'click',\r\n            mode: 'cell',\r\n            showIcon: true\r\n          }\"\r\n          @checkbox-all=\"tbSelectChange\"\r\n          @checkbox-change=\"tbSelectChange\"\r\n        >\r\n          <template v-for=\"(item) in columns\">\r\n            <vxe-column\r\n              :key=\"item.Code\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n              :align=\"item.Align\"\r\n              :edit-render=\"item.Is_Edit ? {} : null\"\r\n            >\r\n              <template v-if=\"item.Is_Edit\" #edit=\"{ row }\">\r\n                <template v-if=\"['Drawing_Receive_Date'].includes(item.Code)\">\r\n                  <vxe-input\r\n                    v-model=\"row[item.Code]\"\r\n                    type=\"date\"\r\n                    placeholder=\"请选择日期\"\r\n                    @change=\"saveRow(row, item.Code)\"\r\n                  />\r\n                </template>\r\n                <template v-else>\r\n                  <vxe-input\r\n                    v-model=\"row[item.Code]\"\r\n                    placeholder=\"请输入\"\r\n                    @blur=\"saveRow(row, item.Code)\"\r\n                  />\r\n                </template>\r\n              </template>\r\n              <template #default=\"{ row }\">\r\n                <span> {{ row[item.Code] }}</span>\r\n              </template>\r\n            </vxe-column>\r\n          </template>\r\n        </vxe-table>\r\n      </div>\r\n      <Pagination\r\n        :total=\"total\"\r\n        :page-sizes=\"tablePageSize\"\r\n        :page.sync=\"queryInfo.Page\"\r\n        :limit.sync=\"queryInfo.PageSize\"\r\n        class=\"pagination\"\r\n        @pagination=\"pageChange\"\r\n      />\r\n    </div>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetInstallUnitPageList } from '@/api/PRO/install-unit'\r\nimport { GeAreaTrees, GetProjectPageList } from '@/api/PRO/project'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport {\r\n  BatchUpdateTrackEditableFields,\r\n  ExportstBatchTrackFromSourceAsync,\r\n  InsertBatchTrack,\r\n  SaveQIReportData\r\n} from '@/api/PRO/qualityInspect/quality-management'\r\nimport { GetWorkshopPageList } from '@/api/PRO/basic-information/workshop'\r\nimport { combineURL } from '@/utils'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\nimport Pagination from '@/components/Pagination/index.vue'\r\nimport moment from 'moment'\r\n\r\nexport default {\r\n  name: 'PROBatchTracking1',\r\n  components: { DynamicTableFields, Pagination },\r\n  mixins: [getTbInfo],\r\n  data() {\r\n    return {\r\n      dateRange: '',\r\n      dialogVisible: false,\r\n      exportLoading: false,\r\n      gridCode: '',\r\n      currentComponent: '',\r\n      title: '',\r\n      currentRow: null,\r\n      form: {\r\n        Project_Id: '',\r\n        Area_Id: '',\r\n        InstallUnit_Id: '',\r\n        Workshop_Id: '',\r\n        DrawingReceiveDateStart: '',\r\n        DrawingReceiveDateEnd: ''\r\n      },\r\n\r\n      projectOption: [], // Define options as needed\r\n      treeParamsArea: {\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          disabled: 'disabled',\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      multipleSelection: [],\r\n      areaOption: [],\r\n      installOption: [],\r\n      workshopOptions: [],\r\n      columns: [],\r\n      tableData: [],\r\n      customColumns: [],\r\n      tbLoading: false,\r\n      tablePageSize: tablePageSize,\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: tablePageSize[0]\r\n      },\r\n      total: 0,\r\n      typeList: []\r\n    }\r\n  },\r\n  watch: {\r\n    dateRange: {\r\n      handler(newValue) {\r\n        if (newValue) {\r\n          const [start, end] = newValue\r\n          this.form.DrawingReceiveDateStart = start\r\n          this.form.DrawingReceiveDateEnd = end\r\n        } else {\r\n          this.form.DrawingReceiveDateStart = ''\r\n          this.form.DrawingReceiveDateEnd = ''\r\n        }\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  async created() {\r\n    try {\r\n      this.tbLoading = true\r\n      this.gridCode = 'PROBatchTracking'\r\n      this.initColumn()\r\n      await this.getProjectOption()\r\n      this.getWorkShop()\r\n      this.fetchData(1)\r\n    } catch (error) {\r\n      this.tbLoading = false\r\n      console.error('Error during created lifecycle:', error)\r\n    }\r\n  },\r\n  mounted() {\r\n  },\r\n  methods: {\r\n    async initColumn() {\r\n      await this.getTableConfig(this.gridCode, true)\r\n      const res = await GetPartTypeList({ Part_Grade: 0 })\r\n      this.typeList = res.Data\r\n      this.columns = this.columns.filter(v => v.Is_Display)\r\n      const expandedColumns = []\r\n      this.typeList.forEach(item => {\r\n        // 添加数量列\r\n        expandedColumns.push({\r\n          Code: `${item.Code}_Count`,\r\n          Display_Name: `${item.Name}数量`,\r\n          Is_Display: true,\r\n          Is_Edit: false,\r\n          Is_Frozen: false,\r\n          Width: 150\r\n        })\r\n        // 添加重量列\r\n        expandedColumns.push({\r\n          Code: `${item.Code}_Weight`,\r\n          Display_Name: `${item.Name}重量(kg)`,\r\n          Is_Display: true,\r\n          Is_Edit: false,\r\n          Is_Frozen: false,\r\n          Width: 150\r\n        })\r\n      })\r\n      this.columns.splice(5, 0, ...expandedColumns)\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    fetchData(page) {\r\n      this.tbLoading = true\r\n      page && (this.queryInfo.Page = page)\r\n      InsertBatchTrack({\r\n        QueryParams: this.form,\r\n        PageInfo: this.queryInfo\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.tableData = res.Data.Data.map(item => {\r\n            item.Drawing_Receive_Date = item.Drawing_Receive_Date ? moment(item.Drawing_Receive_Date).format('YYYY-MM-DD') : ''\r\n            return item\r\n          })\r\n          this.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    getWorkShop() {\r\n      GetWorkshopPageList({ Page: 1, PageSize: -1 }).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (!res?.Data?.Data) {\r\n            this.workshopOptions = []\r\n          }\r\n          this.workshopOptions = res.Data.Data.map(item => {\r\n            return {\r\n              Id: item.Id,\r\n              Display_Name: item.Display_Name\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    reset() {\r\n      this.$refs.formRef.resetFields()\r\n      this.form.Project_Id = ''\r\n      this.dateRange = ''\r\n      this.form.DrawingReceiveDateStart = ''\r\n      this.form.DrawingReceiveDateEnd = ''\r\n      this.fetchData(1)\r\n    },\r\n    search() {\r\n      this.fetchData(1)\r\n    },\r\n    getProjectOption() {\r\n      return new Promise((resolve, reject) => {\r\n        GetProjectPageList({\r\n          Page: 1,\r\n          PageSize: -1\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.projectOption = res.Data?.Data || []\r\n            if (this.projectOption.length) {\r\n              this.form.Project_Id = ''\r\n              this.getAreaList()\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          resolve()\r\n        })\r\n      })\r\n    },\r\n    projectChange(e) {\r\n      this.form.Area_Id = ''\r\n      this.form.InstallUnit_Id = ''\r\n      this.treeParamsArea.data = []\r\n      this.installOption = []\r\n      this.form.InstallUnit_Id = ''\r\n      if (e) {\r\n        this.getAreaList()\r\n      }\r\n    },\r\n\r\n    areaClear() {\r\n      this.form.Area_Id = ''\r\n      this.form.InstallUnit_Id = ''\r\n    },\r\n    getAreaList() {\r\n      if (!this.form.Project_Id) {\r\n        this.$refs.treeSelectArea?.treeDataUpdateFun([])\r\n        return\r\n      }\r\n      GeAreaTrees({\r\n        projectId: this.form.Project_Id\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const tree = res.Data\r\n          this.setDisabledTree(tree)\r\n          this.treeParamsArea.data = tree\r\n          this.$nextTick(_ => {\r\n            this.$refs.treeSelectArea?.treeDataUpdateFun(tree)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setDisabledTree(root) {\r\n      if (!root) return\r\n      root.forEach((element) => {\r\n        const { Children } = element\r\n        if (Children && Children.length) {\r\n          element.disabled = true\r\n        } else {\r\n          element.disabled = false\r\n          this.setDisabledTree(Children)\r\n        }\r\n      })\r\n    },\r\n    areaChange() {\r\n      this.form.InstallUnit_Id = ''\r\n      this.getInstall()\r\n    },\r\n    getInstall() {\r\n      GetInstallUnitPageList({\r\n        Area_Id: this.form.Area_Id,\r\n        Page: 1,\r\n        PageSize: -1\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.installOption = res.Data.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    exportData() {\r\n      this.exportLoading = true\r\n      ExportstBatchTrackFromSourceAsync({\r\n        QueryParams: this.form\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '导出成功',\r\n            type: 'success'\r\n          })\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.exportLoading = false\r\n      })\r\n    },\r\n    tbSelectChange(array) {\r\n      this.multipleSelection = array.records\r\n    },\r\n\r\n    handleSubmit(formData) {\r\n      // Call the API to save the updated data\r\n      this.tbLoading = true\r\n      SaveQIReportData(formData).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '保存成功',\r\n            type: 'success'\r\n          })\r\n          this.handleClose()\r\n          this.fetchData(1)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message || '保存失败',\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).catch(error => {\r\n        this.$message({\r\n          message: '保存失败: ' + error,\r\n          type: 'error'\r\n        })\r\n      }).finally(() => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n\r\n    // 重新保存改行\r\n    saveRow(row, fieldCode) {\r\n      BatchUpdateTrackEditableFields({\r\n        items: [{\r\n          project_Id: row.Project_Id,\r\n          area_Id: row.Area_Id,\r\n          installUnit_Id: row.InstallUnit_Id,\r\n          workshop_id: row.Workshop_Id,\r\n          structure_Type: row.Structure_Type,\r\n          drawing_Receive_Date: row.Drawing_Receive_Date,\r\n          conclusion: row.Conclusion\r\n        }]\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.container {\r\n  padding: 16px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: unset;\r\n}\r\n\r\n.filter-bar {\r\n  padding-top: 16px;\r\n  box-sizing: border-box;\r\n  margin-bottom: 16px;\r\n  background-color: #fff;\r\n}\r\n\r\n.main-content {\r\n  padding: 16px 16px 0 16px;\r\n  flex: 1;\r\n  background-color: #fff;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .tb-x {\r\n    flex: 1;\r\n    height: 0;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .pagination-container {\r\n    text-align: right;\r\n    padding: 16px;\r\n    margin: 0;\r\n  }\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production\\draft.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production\\draft.vue", "mtime": 1757468113354}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQoNCmltcG9ydCB7IGNsb3NlVGFnVmlldywgZGVib3VuY2UgfSBmcm9tICdAL3V0aWxzJw0KaW1wb3J0IEJhdGNoUHJvY2Vzc0FkanVzdCBmcm9tICcuL2NvbXBvbmVudHMvQmF0Y2hQcm9jZXNzQWRqdXN0Jw0KaW1wb3J0IHsNCiAgR2V0Q29tcFNjaGR1bGluZ0luZm9EZXRhaWwsDQogIEdldFBhcnRTY2hkdWxpbmdJbmZvRGV0YWlsLA0KICBHZXRTY2hkdWxpbmdXb3JraW5nVGVhbXMsDQogIFNhdmVDb21wb25lbnRTY2hlZHVsaW5nV29ya3Nob3AsDQogIFNhdmVDb21wU2NoZHVsaW5nRHJhZnQsDQogIFNhdmVQYXJ0U2NoZHVsaW5nRHJhZnQsDQogIFNhdmVQYXJ0U2NoZWR1bGluZ1dvcmtzaG9wLA0KICBTYXZlU2NoZHVsaW5nVGFza0J5SWQNCn0gZnJvbSAnQC9hcGkvUFJPL3Byb2R1Y3Rpb24tdGFzaycNCmltcG9ydCBBZGREcmFmdCBmcm9tICcuL2NvbXBvbmVudHMvYWRkRHJhZnQnDQppbXBvcnQgT3duZXJQcm9jZXNzIGZyb20gJy4vY29tcG9uZW50cy9Pd25lclByb2Nlc3MnDQppbXBvcnQgV29ya3Nob3AgZnJvbSAnLi9jb21wb25lbnRzL1dvcmtzaG9wLnZ1ZScNCmltcG9ydCB7IEdldEdyaWRCeUNvZGUgfSBmcm9tICdAL2FwaS9zeXMnDQppbXBvcnQgeyBGSVhfQ09MVU1OLCB1bmlxdWVDb2RlIH0gZnJvbSAnQC92aWV3cy9QUk8vcGxhbi1wcm9kdWN0aW9uL3NjaGVkdWxlLXByb2R1Y3Rpb24vY29uc3RhbnQnDQppbXBvcnQgeyB2NCBhcyB1dWlkdjQgfSBmcm9tICd1dWlkJw0KaW1wb3J0IG51bWVyYWwgZnJvbSAnbnVtZXJhbCcNCmltcG9ydCB7IEdldExpYkxpc3RUeXBlLCBHZXRQcm9jZXNzTGlzdEJhc2UgfSBmcm9tICdAL2FwaS9QUk8vdGVjaG5vbG9neS1saWInDQppbXBvcnQgeyBBcmVhR2V0RW50aXR5IH0gZnJvbSAnQC9hcGkvcGxtL3Byb2plY3RzJw0KaW1wb3J0IHsgbWFwQWN0aW9ucywgbWFwR2V0dGVycyB9IGZyb20gJ3Z1ZXgnDQppbXBvcnQgeyBHZXRQYXJ0VHlwZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vcGFydFR5cGUnDQppbXBvcnQgKiBhcyBtb21lbnQgZnJvbSAnbW9tZW50L21vbWVudCcNCg0KY29uc3QgU1BMSVRfU1lNQk9MID0gJyRfJCcNCmV4cG9ydCBkZWZhdWx0IHsNCiAgY29tcG9uZW50czogeyBCYXRjaFByb2Nlc3NBZGp1c3QsIEFkZERyYWZ0LCBXb3Jrc2hvcCwgT3duZXJQcm9jZXNzIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHBpY2tlck9wdGlvbnM6IHsNCiAgICAgICAgZGlzYWJsZWREYXRlKHRpbWUpIHsNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIHNlYXJjaFR5cGU6ICcnLA0KICAgICAgZm9ybUlubGluZTogew0KICAgICAgICBTY2hkdWxpbmdfQ29kZTogJycsDQogICAgICAgIENyZWF0ZV9Vc2VyTmFtZTogJycsDQogICAgICAgIEZpbmlzaF9EYXRlOiAnJywNCiAgICAgICAgUmVtYXJrOiAnJw0KICAgICAgfSwNCiAgICAgIHRvdGFsOiAwLA0KICAgICAgY29sdW1uczogW10sDQogICAgICB0YkRhdGE6IFtdLA0KICAgICAgdGJDb25maWc6IHt9LA0KICAgICAgVG90YWxDb3VudDogMCwNCiAgICAgIG11bHRpcGxlU2VsZWN0aW9uOiBbXSwNCiAgICAgIHBnTG9hZGluZzogZmFsc2UsDQogICAgICBkZWxldGVMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHdvcmtTaG9wSXNPcGVuOiBmYWxzZSwNCiAgICAgIGlzT3duZXJOdWxsOiBmYWxzZSwNCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgb3BlbkFkZERyYWZ0OiBmYWxzZSwNCiAgICAgIHNhdmVMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHRiTG9hZGluZzogZmFsc2UsDQogICAgICBpc0NoZWNrQWxsOiBmYWxzZSwNCiAgICAgIGN1cnJlbnRDb21wb25lbnQ6ICcnLA0KICAgICAgZFdpZHRoOiAnMjUlJywNCiAgICAgIHRpdGxlOiAnJywNCiAgICAgIHNlYXJjaDogKCkgPT4gKHt9KSwNCiAgICAgIHBhZ2VUeXBlOiB1bmRlZmluZWQsDQogICAgICB0aXBMYWJlbDogJycsDQogICAgICB0ZWNobm9sb2d5T3B0aW9uOiBbXSwNCiAgICAgIHR5cGVPcHRpb246IFtdLA0KICAgICAgd29ya2luZ1RlYW06IFtdLA0KICAgICAgcGFnZVN0YXR1czogdW5kZWZpbmVkLA0KICAgICAgc2NoZWR1bGVJZDogJycsDQogICAgICBwYXJ0Q29tT3duZXJDb2x1bW46IG51bGwNCiAgICB9DQogIH0sDQogIHdhdGNoOiB7DQogICAgJ3RiRGF0YS5sZW5ndGgnOiB7DQogICAgICBoYW5kbGVyKG4sIG8pIHsNCiAgICAgICAgdGhpcy5jaGVja093bmVyKCkNCiAgICAgIH0sDQogICAgICBpbW1lZGlhdGU6IGZhbHNlDQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIGlzQ29tKCkgew0KICAgICAgcmV0dXJuIHRoaXMucGFnZVR5cGUgPT09ICdjb20nDQogICAgfSwNCiAgICBpc1ZpZXcoKSB7DQogICAgICByZXR1cm4gdGhpcy5wYWdlU3RhdHVzID09PSAndmlldycNCiAgICB9LA0KICAgIGlzRWRpdCgpIHsNCiAgICAgIHJldHVybiB0aGlzLnBhZ2VTdGF0dXMgPT09ICdlZGl0Jw0KICAgIH0sDQogICAgaXNBZGQoKSB7DQogICAgICByZXR1cm4gdGhpcy5wYWdlU3RhdHVzID09PSAnYWRkJw0KICAgIH0sDQogICAgLi4ubWFwR2V0dGVycygnZmFjdG9yeUluZm8nLCBbJ3dvcmtzaG9wRW5hYmxlZCddKSwNCiAgICAuLi5tYXBHZXR0ZXJzKCdzY2hlZHVsZScsIFsncHJvY2Vzc0xpc3QnXSkNCiAgfSwNCiAgYmVmb3JlUm91dGVFbnRlcih0bywgZnJvbSwgbmV4dCkgew0KICAgIGlmICh0by5xdWVyeS5zdGF0dXMgPT09ICd2aWV3Jykgew0KICAgICAgdG8ubWV0YS50aXRsZSA9ICfmn6XnnIsnDQogICAgfSBlbHNlIHsNCiAgICAgIHRvLm1ldGEudGl0bGUgPSAn6I2J56i/Jw0KICAgIH0NCiAgICBuZXh0KCkNCiAgfSwNCiAgYXN5bmMgbW91bnRlZCgpIHsNCiAgICB0aGlzLmluaXRQcm9jZXNzTGlzdCgpDQogICAgdGhpcy50YkRhdGFNYXAgPSB7fQ0KICAgIHRoaXMucGFnZVR5cGUgPSB0aGlzLiRyb3V0ZS5xdWVyeS5wZ190eXBlDQogICAgdGhpcy5wYWdlU3RhdHVzID0gdGhpcy4kcm91dGUucXVlcnkuc3RhdHVzDQogICAgdGhpcy5tb2RlbCA9IHRoaXMuJHJvdXRlLnF1ZXJ5Lm1vZGVsDQogICAgdGhpcy5zY2hlZHVsZUlkID0gdGhpcy4kcm91dGUucXVlcnkucGlkIHx8ICcnDQogICAgLy8gdGhpcy5mb3JtSW5saW5lLkNyZWF0ZV9Vc2VyTmFtZSA9IHRoaXMuJHN0b3JlLmdldHRlcnMubmFtZQ0KICAgIC8vIOahhuaetumXrumimOW8lei1t3N0b3Jl5pWw5o2u5Lii5aSx77yM5bey5Y+N6aaI77yM57uT5p6c77ya5q2k5aSE5YWI5L2/55SobG9jYWxTdG9yYWdlDQogICAgdGhpcy5mb3JtSW5saW5lLkNyZWF0ZV9Vc2VyTmFtZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdVc2VyQWNjb3VudCcpDQogICAgaWYgKCF0aGlzLmlzQ29tKSB7DQogICAgICB0aGlzLmdldFR5cGUoKQ0KICAgIH0gZWxzZSB7DQoNCiAgICB9DQogICAgdGhpcy51bmlxdWUgPSB1bmlxdWVDb2RlKCkNCiAgICB0aGlzLmNoZWNrV29ya3Nob3BJc09wZW4oKQ0KICAgIHRoaXMuZ2V0QXJlYUluZm8oKQ0KICAgIHRoaXMuc2VhcmNoID0gZGVib3VuY2UodGhpcy5mZXRjaERhdGEsIDgwMCwgdHJ1ZSkNCiAgICBhd2FpdCB0aGlzLm1lcmdlQ29uZmlnKCkNCiAgICBpZiAodGhpcy5pc1ZpZXcgfHwgdGhpcy5pc0VkaXQpIHsNCiAgICAgIHRoaXMuZmV0Y2hEYXRhKCkNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAuLi5tYXBBY3Rpb25zKCdzY2hlZHVsZScsIFsnY2hhbmdlUHJvY2Vzc0xpc3QnLCAnaW5pdFByb2Nlc3NMaXN0J10pLA0KICAgIGNoZWNrT3duZXIoKSB7DQogICAgICBpZiAodGhpcy5pc0NvbSkgcmV0dXJuDQogICAgICB0aGlzLmlzT3duZXJOdWxsID0gdGhpcy50YkRhdGEuZXZlcnkodiA9PiAhdi5Db21wX0ltcG9ydF9EZXRhaWxfSWQpDQogICAgICBjb25zdCBpZHggPSB0aGlzLmNvbHVtbnMuZmluZEluZGV4KHYgPT4gdi5Db2RlID09PSAnUGFydF9Vc2VkX1Byb2Nlc3MnKQ0KICAgICAgaWYgKHRoaXMuaXNPd25lck51bGwpIHsNCiAgICAgICAgaWR4ICE9PSAtMSAmJiB0aGlzLmNvbHVtbnMuc3BsaWNlKGlkeCwgMSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGlmIChpZHggPT09IC0xKSB7DQogICAgICAgICAgaWYgKCF0aGlzLm93bmVyQ29sdW1uKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIl+ihqOmFjee9ruWtl+autee8uuWwkembtuS7tumihueUqOW3peW6j+Wtl+autScsDQogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHJldHVybg0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLmNvbHVtbnMucHVzaCh0aGlzLm93bmVyQ29sdW1uKQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBtZXJnZUNvbmZpZygpIHsNCiAgICAgIGF3YWl0IHRoaXMuZ2V0Q29uZmlnKCkNCiAgICAgIGF3YWl0IHRoaXMuZ2V0V29ya1RlYW0oKQ0KICAgIH0sDQogICAgYXN5bmMgZ2V0Q29uZmlnKCkgew0KICAgICAgY29uc3QgY29uZmlnQ29kZSA9IHRoaXMuaXNDb20gPyAodGhpcy5pc1ZpZXcgPyAnUFJPQ29tVmlld1BhZ2VUYkNvbmZpZycgOiAnUFJPQ29tRHJhZnRQYWdlVGJDb25maWcnKSA6ICh0aGlzLmlzVmlldyA/ICdQUk9QYXJ0Vmlld1BhZ2VUYkNvbmZpZycgOiAnUFJPUGFydERyYWZ0UGFnZVRiQ29uZmlnJykNCg0KICAgICAgYXdhaXQgdGhpcy5nZXRUYWJsZUNvbmZpZyhjb25maWdDb2RlKQ0KICAgICAgaWYgKCF0aGlzLndvcmtzaG9wRW5hYmxlZCkgew0KICAgICAgICB0aGlzLmNvbHVtbnMgPSB0aGlzLmNvbHVtbnMuZmlsdGVyKHYgPT4gdi5Db2RlICE9PSAnV29ya3Nob3BfTmFtZScpDQogICAgICB9DQogICAgICB0aGlzLmNoZWNrT3duZXIoKQ0KICAgIH0sDQogICAgYXN5bmMgZmV0Y2hEYXRhKCkgew0KICAgICAgdGhpcy50YkxvYWRpbmcgPSB0cnVlDQogICAgICBsZXQgcmVzRGF0YSA9IG51bGwNCiAgICAgIGlmICh0aGlzLmlzQ29tKSB7DQogICAgICAgIHJlc0RhdGEgPSBhd2FpdCB0aGlzLmdldENvbVBhZ2VMaXN0KCkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJlc0RhdGEgPSBhd2FpdCB0aGlzLmdldFBhcnRQYWdlTGlzdCgpDQogICAgICB9DQogICAgICB0aGlzLmluaXRUYkRhdGEocmVzRGF0YSkNCiAgICAgIHRoaXMudGJMb2FkaW5nID0gZmFsc2UNCiAgICB9LA0KICAgIGNsb3NlVmlldygpIHsNCiAgICAgIGNsb3NlVGFnVmlldyh0aGlzLiRzdG9yZSwgdGhpcy4kcm91dGUpDQogICAgfSwNCiAgICBjaGVja1dvcmtzaG9wSXNPcGVuKCkgew0KICAgICAgdGhpcy53b3JrU2hvcElzT3BlbiA9IHRydWUNCiAgICB9LA0KICAgIHRiU2VsZWN0Q2hhbmdlKGFycmF5KSB7DQogICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gYXJyYXkucmVjb3Jkcw0KICAgIH0sDQogICAgZ2V0QXJlYUluZm8oKSB7DQogICAgICBBcmVhR2V0RW50aXR5KHsNCiAgICAgICAgaWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LmFyZWFJZA0KICAgICAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIGlmICghcmVzLkRhdGEpIHsNCiAgICAgICAgICAgIHJldHVybiBbXQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGNvbnN0IHN0YXJ0ID0gbW9tZW50KHJlcy5EYXRhPy5EZW1hbmRfQmVnaW5fRGF0ZSkNCiAgICAgICAgICBjb25zdCBlbmQgPSBtb21lbnQocmVzLkRhdGE/LkRlbWFuZF9FbmRfRGF0ZSkNCiAgICAgICAgICB0aGlzLnBpY2tlck9wdGlvbnMuZGlzYWJsZWREYXRlID0gKHRpbWUpID0+IHsNCiAgICAgICAgICAgIHJldHVybiB0aW1lLmdldFRpbWUoKSA8IHN0YXJ0IHx8IHRpbWUuZ2V0VGltZSgpID4gZW5kDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZUNsb3NlKCkgew0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICAgIHRoaXMub3BlbkFkZERyYWZ0ID0gZmFsc2UNCiAgICB9LA0KICAgIGdldENvbVBhZ2VMaXN0KCkgew0KICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHsNCiAgICAgICAgY29uc3Qgew0KICAgICAgICAgIC8vIGluc3RhbGwsDQogICAgICAgICAgLy8gcHJvamVjdElkLA0KICAgICAgICAgIHBpZA0KICAgICAgICAgIC8vIGFyZWFJZA0KICAgICAgICB9ID0gdGhpcy4kcm91dGUucXVlcnkNCiAgICAgICAgR2V0Q29tcFNjaGR1bGluZ0luZm9EZXRhaWwoew0KICAgICAgICAgIFNjaGR1bGluZ19QbGFuX0lkOiBwaWQNCiAgICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIGNvbnN0IHsgU2NoZHVsaW5nX1BsYW4sIFNjaGR1bGluZ19Db21wcywgUHJvY2Vzc19MaXN0IH0gPSByZXMuRGF0YQ0KICAgICAgICAgICAgdGhpcy5mb3JtSW5saW5lID0gT2JqZWN0LmFzc2lnbih0aGlzLmZvcm1JbmxpbmUsIFNjaGR1bGluZ19QbGFuKQ0KICAgICAgICAgICAgUHJvY2Vzc19MaXN0LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgICAgIGNvbnN0IHBsaXN0ID0gew0KICAgICAgICAgICAgICAgIGtleTogaXRlbS5Qcm9jZXNzX0NvZGUsDQogICAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB0aGlzLmNoYW5nZVByb2Nlc3NMaXN0KHBsaXN0KQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHJlc29sdmUoU2NoZHVsaW5nX0NvbXBzIHx8IFtdKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICByZWplY3QoKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfSwNCiAgICBnZXRQYXJ0UGFnZUxpc3QoKSB7DQogICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4gew0KICAgICAgICBjb25zdCB7DQogICAgICAgICAgcGlkDQogICAgICAgIH0gPSB0aGlzLiRyb3V0ZS5xdWVyeQ0KICAgICAgICBHZXRQYXJ0U2NoZHVsaW5nSW5mb0RldGFpbCh7DQogICAgICAgICAgU2NoZHVsaW5nX1BsYW5fSWQ6IHBpZA0KICAgICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgY29uc3QgU2FyZVBhcnRzTW9kZWwgPSByZXMuRGF0YT8uU2FyZVBhcnRzTW9kZWwubWFwKHYgPT4gew0KICAgICAgICAgICAgICBpZiAodi5TY2hlZHVsZWRfVXNlZF9Qcm9jZXNzKSB7DQogICAgICAgICAgICAgICAgLy8g5bey5a2Y5Zyo5pON5L2c6L+H5pWw5o2uDQogICAgICAgICAgICAgICAgdi5QYXJ0X1VzZWRfUHJvY2VzcyA9IHYuU2NoZWR1bGVkX1VzZWRfUHJvY2Vzcw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIHJldHVybiB2DQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgdGhpcy5mb3JtSW5saW5lID0gT2JqZWN0LmFzc2lnbih0aGlzLmZvcm1JbmxpbmUsIHJlcy5EYXRhPy5TY2hkdWxpbmdfUGxhbikNCiAgICAgICAgICAgIHJlc29sdmUoU2FyZVBhcnRzTW9kZWwgfHwgW10pDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHJlamVjdCgpDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGluaXRUYkRhdGEobGlzdCwgdGVhbUtleSA9ICdBbGxvY2F0aW9uX1RlYW1zJykgew0KICAgICAgdGhpcy50YkRhdGEgPSBsaXN0Lm1hcChyb3cgPT4gew0KICAgICAgICBjb25zdCBwcm9jZXNzTGlzdCA9IHJvdy5UZWNobm9sb2d5X1BhdGg/LnNwbGl0KCcvJykgfHwgW10NCiAgICAgICAgcm93LnV1aWQgPSB1dWlkdjQoKQ0KICAgICAgICB0aGlzLmFkZEVsZW1lbnRUb1RiRGF0YShyb3cpDQogICAgICAgIGNvbnN0IG5ld0RhdGEgPSByb3dbdGVhbUtleV0uZmlsdGVyKChyKSA9PiBwcm9jZXNzTGlzdC5maW5kSW5kZXgoKHApID0+IHIuUHJvY2Vzc19Db2RlID09PSBwKSAhPT0gLTEpDQogICAgICAgIG5ld0RhdGEuZm9yRWFjaCgoZWxlLCBpbmRleCkgPT4gew0KICAgICAgICAgIGNvbnN0IGNvZGUgPSB0aGlzLmdldFJvd1VuaXF1ZShyb3cudXVpZCwgZWxlLlByb2Nlc3NfQ29kZSwgZWxlLldvcmtpbmdfVGVhbV9JZCkNCiAgICAgICAgICBjb25zdCBtYXggPSB0aGlzLmdldFJvd1VuaXF1ZU1heChyb3cudXVpZCwgZWxlLlByb2Nlc3NfQ29kZSwgZWxlLldvcmtpbmdfVGVhbV9JZCkNCiAgICAgICAgICByb3dbY29kZV0gPSBlbGUuQ291bnQNCiAgICAgICAgICByb3dbbWF4XSA9IDANCiAgICAgICAgfSkNCiAgICAgICAgdGhpcy5zZXRJbnB1dE1heChyb3cpDQogICAgICAgIHJldHVybiByb3cNCiAgICAgIH0pDQogICAgfSwNCiAgICBtZXJnZVNlbGVjdExpc3QobmV3TGlzdCkgew0KICAgICAgY29uc29sZS50aW1lKCdmZmYnKQ0KICAgICAgbmV3TGlzdC5mb3JFYWNoKChlbGVtZW50LCBpbmRleCkgPT4gew0KICAgICAgICBjb25zdCBjdXIgPSB0aGlzLmdldE1lcmdlVW5pcXVlUm93KGVsZW1lbnQpDQoNCiAgICAgICAgaWYgKCFjdXIpIHsNCiAgICAgICAgICBlbGVtZW50LnB1dWlkID0gZWxlbWVudC51dWlkDQogICAgICAgICAgZWxlbWVudC5TY2hkdWxlZF9Db3VudCA9IGVsZW1lbnQuY2hvb3NlQ291bnQNCiAgICAgICAgICBlbGVtZW50LlNjaGR1bGVkX1dlaWdodCA9IG51bWVyYWwoZWxlbWVudC5jaG9vc2VDb3VudCAqIGVsZW1lbnQuV2VpZ2h0KS5mb3JtYXQoJzAuWzAwXScpDQogICAgICAgICAgdGhpcy50YkRhdGEucHVzaChlbGVtZW50KQ0KICAgICAgICAgIHRoaXMuYWRkRWxlbWVudFRvVGJEYXRhKGVsZW1lbnQpDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCg0KICAgICAgICBjdXIucHV1aWQgPSBlbGVtZW50LnV1aWQNCg0KICAgICAgICBjdXIuU2NoZHVsZWRfQ291bnQgKz0gZWxlbWVudC5jaG9vc2VDb3VudA0KICAgICAgICBjdXIuU2NoZHVsZWRfV2VpZ2h0ID0gbnVtZXJhbChjdXIuU2NoZHVsZWRfV2VpZ2h0KS5hZGQoZWxlbWVudC5jaG9vc2VDb3VudCAqIGVsZW1lbnQuV2VpZ2h0KS5mb3JtYXQoJzAuWzAwXScpDQogICAgICAgIGlmICghY3VyLlRlY2hub2xvZ3lfUGF0aCkgew0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICAgIHRoaXMuc2V0SW5wdXRNYXgoY3VyKQ0KICAgICAgfSkNCg0KICAgICAgLy8gaWYgKHRoaXMuaXNDb20pIHsNCiAgICAgIC8vICAgdGhpcy50YkRhdGEuc29ydCgoYSwgYikgPT4gYS5pbml0Um93SW5kZXggLSBiLmluaXRSb3dJbmRleCkNCiAgICAgIC8vIH0gZWxzZSB7DQogICAgICAvLyAgIHRoaXMudGJEYXRhLnNvcnQoKGEsIGIpID0+IGEuUGFydF9Db2RlLmxvY2FsZUNvbXBhcmUoYi5QYXJ0X0NvZGUpKQ0KICAgICAgLy8gfQ0KICAgICAgdGhpcy50YkRhdGEuc29ydCgoYSwgYikgPT4gYS5pbml0Um93SW5kZXggLSBiLmluaXRSb3dJbmRleCkNCiAgICAgIGNvbnNvbGUudGltZUVuZCgnZmZmJykNCiAgICB9LA0KICAgIGFkZEVsZW1lbnRUb1RiRGF0YShlbGVtZW50KSB7DQogICAgICBjb25zdCBrZXkgPSB0aGlzLmlzQ29tID8gZWxlbWVudC5Db21wX0NvZGUgOiAoZWxlbWVudC5Db21wb25lbnRfQ29kZSA/PyAnJykgKyBlbGVtZW50LlBhcnRfQ29kZQ0KICAgICAgdGhpcy50YkRhdGFNYXBba2V5XSA9IGVsZW1lbnQNCiAgICB9LA0KICAgIGdldE1lcmdlVW5pcXVlUm93KGVsZW1lbnQpIHsNCiAgICAgIGNvbnN0IGtleSA9IHRoaXMuaXNDb20gPyBlbGVtZW50LkNvbXBfQ29kZSA6IChlbGVtZW50LkNvbXBvbmVudF9Db2RlID8/ICcnKSArIGVsZW1lbnQuUGFydF9Db2RlDQogICAgICByZXR1cm4gdGhpcy50YkRhdGFNYXBba2V5XQ0KICAgIH0sDQogICAgY2hlY2tGb3JtKCkgew0KICAgICAgbGV0IGlzVmFsaWRhdGUgPSB0cnVlDQogICAgICB0aGlzLiRyZWZzWydmb3JtSW5saW5lJ10udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICghdmFsaWQpIGlzVmFsaWRhdGUgPSBmYWxzZQ0KICAgICAgfSkNCiAgICAgIHJldHVybiBpc1ZhbGlkYXRlDQogICAgfSwNCiAgICBhc3luYyBzYXZlRHJhZnQoaXNPcmRlciA9IGZhbHNlKSB7DQogICAgICBjb25zdCBjaGVja1N1Y2Nlc3MgPSB0aGlzLmNoZWNrRm9ybSgpDQogICAgICBpZiAoIWNoZWNrU3VjY2VzcykgcmV0dXJuIGZhbHNlDQogICAgICBjb25zdCB7IHRhYmxlRGF0YSwgc3RhdHVzIH0gPSB0aGlzLmdldFN1Ym1pdFRiSW5mbygpDQogICAgICBpZiAoIXN0YXR1cykgcmV0dXJuIGZhbHNlDQogICAgICBpZiAoIWlzT3JkZXIpIHsNCiAgICAgICAgdGhpcy5zYXZlTG9hZGluZyA9IHRydWUNCiAgICAgIH0NCiAgICAgIGNvbnN0IGlzU3VjY2VzcyA9IGF3YWl0IHRoaXMuaGFuZGxlU2F2ZURyYWZ0KHRhYmxlRGF0YSwgaXNPcmRlcikNCiAgICAgIGNvbnNvbGUubG9nKCdpc1N1Y2Nlc3MnLCBpc1N1Y2Nlc3MpDQogICAgICBpZiAoIWlzU3VjY2VzcykgcmV0dXJuIGZhbHNlDQogICAgICBpZiAoaXNPcmRlcikgcmV0dXJuIGlzU3VjY2Vzcw0KICAgICAgdGhpcy4kcmVmc1snZHJhZnQnXT8uZmV0Y2hEYXRhKCkNCiAgICAgIHRoaXMuc2F2ZUxvYWRpbmcgPSBmYWxzZQ0KICAgIH0sDQogICAgYXN5bmMgc2F2ZVdvcmtTaG9wKCkgew0KICAgICAgY29uc3QgY2hlY2tTdWNjZXNzID0gdGhpcy5jaGVja0Zvcm0oKQ0KICAgICAgaWYgKCFjaGVja1N1Y2Nlc3MpIHJldHVybiBmYWxzZQ0KICAgICAgY29uc3Qgb2JqID0ge30NCiAgICAgIGlmICghdGhpcy50YkRhdGEubGVuZ3RoKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICfmlbDmja7kuI3og73kuLrnqbonLA0KICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICB9KQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmlzQ29tKSB7DQogICAgICAgIG9iai5TY2hkdWxpbmdfQ29tcHMgPSB0aGlzLnRiRGF0YQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgb2JqLlNhcmVQYXJ0c01vZGVsID0gdGhpcy50YkRhdGENCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmlzRWRpdCkgew0KICAgICAgICBvYmouU2NoZHVsaW5nX1BsYW4gPSB0aGlzLmZvcm1JbmxpbmUNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNvbnN0IHsNCiAgICAgICAgICBpbnN0YWxsLA0KICAgICAgICAgIHByb2plY3RJZCwNCiAgICAgICAgICBhcmVhSWQNCiAgICAgICAgfSA9IHRoaXMuJHJvdXRlLnF1ZXJ5DQogICAgICAgIG9iai5TY2hkdWxpbmdfUGxhbiA9IHsNCiAgICAgICAgICAuLi50aGlzLmZvcm1JbmxpbmUsDQogICAgICAgICAgUHJvamVjdF9JZDogcHJvamVjdElkLA0KICAgICAgICAgIEluc3RhbGxVbml0X0lkOiBpbnN0YWxsLA0KICAgICAgICAgIEFyZWFfSWQ6IGFyZWFJZCwNCiAgICAgICAgICBTY2hkdWxpbmdfTW9kZWw6IHRoaXMubW9kZWwgLy8gMeaehOS7tuWNleeLrOaOkuS6p++8jDLpm7bku7bljZXni6zmjpLkuqfvvIwz5p6EL+mbtuS7tuS4gOi1t+aOkuS6pw0KICAgICAgICB9DQogICAgICB9DQogICAgICB0aGlzLnBnTG9hZGluZyA9IHRydWUNCiAgICAgIGNvbnN0IF9mdW4gPSB0aGlzLmlzQ29tID8gU2F2ZUNvbXBvbmVudFNjaGVkdWxpbmdXb3Jrc2hvcCA6IFNhdmVQYXJ0U2NoZWR1bGluZ1dvcmtzaG9wDQogICAgICBfZnVuKG9iaikudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMucGdMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6ICfkv53lrZjmiJDlip8nLA0KICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLmNsb3NlVmlldygpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgZ2V0U3VibWl0VGJJbmZvKCkgew0KICAgICAgLy8g5aSE55CG5LiK5Lyg55qE5pWw5o2uDQogICAgICBjb25zdCB0YWJsZURhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMudGJEYXRhKSkNCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGFibGVEYXRhLmxlbmd0aDsgaSsrKSB7DQogICAgICAgIGNvbnN0IGVsZW1lbnQgPSB0YWJsZURhdGFbaV0NCiAgICAgICAgY29uc3QgbGlzdCA9IFtdDQogICAgICAgIGlmICghZWxlbWVudC5UZWNobm9sb2d5X1BhdGgpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6ICflt6Xluo/kuI3og73kuLrnqbonLA0KICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgICAgfSkNCiAgICAgICAgICByZXR1cm4geyBzdGF0dXM6IGZhbHNlIH0NCiAgICAgICAgfQ0KICAgICAgICAvLyBpZiAoIXRoaXMuaXNDb20gJiYgZWxlbWVudC5Db21wX0ltcG9ydF9EZXRhaWxfSWQgJiYgIWVsZW1lbnQuUGFydF9Vc2VkX1Byb2Nlc3MpIHsNCiAgICAgICAgLy8gICAvLyDpm7bmnoTku7Yg6Zu25Lu25Y2V54us5o6S5LqnDQogICAgICAgIC8vICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgIC8vICAgICBtZXNzYWdlOiAn6Zu25Lu26aKG55So5bel5bqP5LiN6IO95Li656m6JywNCiAgICAgICAgLy8gICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICAvLyAgIH0pDQogICAgICAgIC8vICAgcmV0dXJuIHsgc3RhdHVzOiBmYWxzZSB9DQogICAgICAgIC8vIH0NCiAgICAgICAgaWYgKGVsZW1lbnQuU2NoZWR1bGVkX1RlY2hub2xvZ3lfUGF0aCAmJiBlbGVtZW50LlNjaGVkdWxlZF9UZWNobm9sb2d5X1BhdGggIT09IGVsZW1lbnQuVGVjaG5vbG9neV9QYXRoKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiBg6K+35ZKM6K+l5Yy65Z+f5om55qyh5LiL5bey5o6S5Lqn5ZCMJHt0aGlzLmlzQ29tID8gJ+aehOS7ticgOiAn6Zu25Lu2J33kv53mjIHlt6Xluo/kuIDoh7RgLA0KICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgICAgfSkNCiAgICAgICAgICByZXR1cm4geyBzdGF0dXM6IGZhbHNlIH0NCiAgICAgICAgfQ0KICAgICAgICBpZiAoZWxlbWVudC5TY2hlZHVsZWRfVXNlZF9Qcm9jZXNzICYmIGVsZW1lbnQuU2NoZWR1bGVkX1VzZWRfUHJvY2VzcyAhPT0gZWxlbWVudC5QYXJ0X1VzZWRfUHJvY2Vzcykgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogYOivt+WSjOivpeWMuuWfn+aJueasoeS4i+W3suaOkuS6p+WQjOmbtuS7tumihueUqOW3peW6j+S/neaMgeS4gOiHtGAsDQogICAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgICB9KQ0KICAgICAgICAgIHJldHVybiB7IHN0YXR1czogZmFsc2UgfQ0KICAgICAgICB9DQogICAgICAgIGNvbnN0IHByb2Nlc3NMaXN0ID0gQXJyYXkuZnJvbShuZXcgU2V0KGVsZW1lbnQuVGVjaG5vbG9neV9QYXRoLnNwbGl0KCcvJykpKQ0KICAgICAgICBwcm9jZXNzTGlzdC5mb3JFYWNoKGNvZGUgPT4gew0KICAgICAgICAgIGNvbnN0IGdyb3VwcyA9IHRoaXMud29ya2luZ1RlYW0uZmlsdGVyKHYgPT4gdi5Qcm9jZXNzX0NvZGUgPT09IGNvZGUpDQogICAgICAgICAgY29uc3QgZ3JvdXBzTGlzdCA9IGdyb3Vwcy5tYXAoZ3JvdXAgPT4gew0KICAgICAgICAgICAgY29uc3QgdUNvZGUgPSB0aGlzLmdldFJvd1VuaXF1ZShlbGVtZW50LnV1aWQsIGNvZGUsIGdyb3VwLldvcmtpbmdfVGVhbV9JZCkNCiAgICAgICAgICAgIGNvbnN0IHVNYXggPSB0aGlzLmdldFJvd1VuaXF1ZU1heChlbGVtZW50LnV1aWQsIGNvZGUsIGdyb3VwLldvcmtpbmdfVGVhbV9JZCkNCiAgICAgICAgICAgIGNvbnN0IG9iaiA9IHsNCiAgICAgICAgICAgICAgVGVhbV9UYXNrX0lkOiBlbGVtZW50LlRlYW1fVGFza19JZCwNCiAgICAgICAgICAgICAgQ29tcF9Db2RlOiBlbGVtZW50LkNvbXBfQ29kZSwNCiAgICAgICAgICAgICAgQWdhaW5fQ291bnQ6ICtlbGVtZW50W3VDb2RlXSB8fCAwLCAvLyDkuI3loavvvIzlkI7lj7DorqnkvKAwDQogICAgICAgICAgICAgIFBhcnRfQ29kZTogdGhpcy5pc0NvbSA/IG51bGwgOiAnJywNCiAgICAgICAgICAgICAgUHJvY2Vzc19Db2RlOiBjb2RlLA0KICAgICAgICAgICAgICBUZWNobm9sb2d5X1BhdGg6IGVsZW1lbnQuVGVjaG5vbG9neV9QYXRoLA0KICAgICAgICAgICAgICBXb3JraW5nX1RlYW1fSWQ6IGdyb3VwLldvcmtpbmdfVGVhbV9JZCwNCiAgICAgICAgICAgICAgV29ya2luZ19UZWFtX05hbWU6IGdyb3VwLldvcmtpbmdfVGVhbV9OYW1lDQogICAgICAgICAgICB9DQogICAgICAgICAgICBkZWxldGUgZWxlbWVudFt1Q29kZV0NCiAgICAgICAgICAgIGRlbGV0ZSBlbGVtZW50W3VNYXhdDQogICAgICAgICAgICByZXR1cm4gb2JqDQogICAgICAgICAgfSkNCiAgICAgICAgICBsaXN0LnB1c2goLi4uZ3JvdXBzTGlzdCkNCiAgICAgICAgfSkNCiAgICAgICAgY29uc3QgaGFzSW5wdXQgPSBPYmplY3Qua2V5cyhlbGVtZW50KS5maWx0ZXIoXyA9PiBfLnN0YXJ0c1dpdGgoZWxlbWVudFsndXVpZCddKSkNCiAgICAgICAgaGFzSW5wdXQuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgIGRlbGV0ZSBlbGVtZW50W2l0ZW1dDQogICAgICAgIH0pDQogICAgICAgIGRlbGV0ZSBlbGVtZW50Wyd1dWlkJ10NCiAgICAgICAgZGVsZXRlIGVsZW1lbnRbJ19YX1JPV19LRVknXQ0KICAgICAgICBkZWxldGUgZWxlbWVudFsncHV1aWQnXQ0KICAgICAgICBlbGVtZW50LkFsbG9jYXRpb25fVGVhbXMgPSBsaXN0DQogICAgICB9DQogICAgICByZXR1cm4geyB0YWJsZURhdGEsIHN0YXR1czogdHJ1ZSB9DQogICAgfSwNCiAgICBhc3luYyBoYW5kbGVTYXZlRHJhZnQodGFibGVEYXRhLCBpc09yZGVyKSB7DQogICAgICBjb25zb2xlLmxvZygn5L+d5a2Y6I2J56i/JykNCiAgICAgIGNvbnN0IF9mdW4gPSB0aGlzLmlzQ29tID8gU2F2ZUNvbXBTY2hkdWxpbmdEcmFmdCA6IFNhdmVQYXJ0U2NoZHVsaW5nRHJhZnQNCiAgICAgIGNvbnN0IG9iaiA9IHt9DQogICAgICBpZiAodGhpcy5pc0NvbSkgew0KICAgICAgICBvYmouU2NoZHVsaW5nX0NvbXBzID0gdGFibGVEYXRhDQogICAgICAgIGNvbnN0IHAgPSBbXQ0KICAgICAgICBmb3IgKGNvbnN0IG9iaktleSBpbiB0aGlzLnByb2Nlc3NMaXN0KSB7DQogICAgICAgICAgaWYgKHRoaXMucHJvY2Vzc0xpc3QuaGFzT3duUHJvcGVydHkob2JqS2V5KSkgew0KICAgICAgICAgICAgcC5wdXNoKHRoaXMucHJvY2Vzc0xpc3Rbb2JqS2V5XSkNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgb2JqLlByb2Nlc3NfTGlzdCA9IHANCiAgICAgIH0gZWxzZSB7DQogICAgICAgIG9iai5TYXJlUGFydHNNb2RlbCA9IHRhYmxlRGF0YQ0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuaXNFZGl0KSB7DQogICAgICAgIG9iai5TY2hkdWxpbmdfUGxhbiA9IHRoaXMuZm9ybUlubGluZQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgY29uc3Qgew0KICAgICAgICAgIGluc3RhbGwsDQogICAgICAgICAgcHJvamVjdElkLA0KICAgICAgICAgIGFyZWFJZA0KICAgICAgICB9ID0gdGhpcy4kcm91dGUucXVlcnkNCiAgICAgICAgb2JqLlNjaGR1bGluZ19QbGFuID0gew0KICAgICAgICAgIC4uLnRoaXMuZm9ybUlubGluZSwNCiAgICAgICAgICBQcm9qZWN0X0lkOiBwcm9qZWN0SWQsDQogICAgICAgICAgSW5zdGFsbFVuaXRfSWQ6IGluc3RhbGwsDQogICAgICAgICAgQXJlYV9JZDogYXJlYUlkLA0KICAgICAgICAgIFNjaGR1bGluZ19Nb2RlbDogdGhpcy5tb2RlbCAvLyAx5p6E5Lu25Y2V54us5o6S5Lqn77yMMumbtuS7tuWNleeLrOaOkuS6p++8jDPmnoQv6Zu25Lu25LiA6LW35o6S5LqnDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIGxldCBvcmRlclN1Y2Nlc3MgPSBmYWxzZQ0KICAgICAgY29uc29sZS5sb2coJ29iaicsIG9iaikNCg0KICAgICAgYXdhaXQgX2Z1bihvYmopLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICBpZiAoIWlzT3JkZXIpIHsNCiAgICAgICAgICAgIHRoaXMucGdMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiAn5L+d5a2Y5oiQ5YqfJywNCiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgdGhpcy5jbG9zZVZpZXcoKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLnRlbXBsYXRlU2NoZWR1bGVDb2RlID0gcmVzLkRhdGENCiAgICAgICAgICAgIG9yZGVyU3VjY2VzcyA9IHRydWUNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfkv53lrZjojYnnqL/miJDlip8gJykNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5zYXZlTG9hZGluZyA9IGZhbHNlDQogICAgICAgICAgdGhpcy5wZ0xvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIGNvbnNvbGUubG9nKCfnu5PmnZ8gJykNCiAgICAgIHJldHVybiBvcmRlclN1Y2Nlc3MNCiAgICB9LA0KICAgIGhhbmRsZURlbGV0ZSgpIHsNCiAgICAgIHRoaXMuZGVsZXRlTG9hZGluZyA9IHRydWUNCiAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICBjb25zdCBzZWxlY3RlZFV1aWRzID0gbmV3IFNldCh0aGlzLm11bHRpcGxlU2VsZWN0aW9uLm1hcCh2ID0+IHYudXVpZCkpDQogICAgICAgIHRoaXMudGJEYXRhID0gdGhpcy50YkRhdGEuZmlsdGVyKGl0ZW0gPT4gew0KICAgICAgICAgIGNvbnN0IGlzU2VsZWN0ZWQgPSBzZWxlY3RlZFV1aWRzLmhhcyhpdGVtLnV1aWQpDQogICAgICAgICAgaWYgKGlzU2VsZWN0ZWQpIHsNCiAgICAgICAgICAgIGNvbnN0IGtleSA9IHRoaXMuaXNDb20gPyBpdGVtLkNvbXBfQ29kZSA6IChpdGVtLkNvbXBvbmVudF9Db2RlID8/ICcnKSArIGl0ZW0uUGFydF9Db2RlDQogICAgICAgICAgICBkZWxldGUgdGhpcy50YkRhdGFNYXBba2V5XQ0KICAgICAgICAgIH0NCiAgICAgICAgICByZXR1cm4gIWlzU2VsZWN0ZWQNCiAgICAgICAgfSkNCiAgICAgICAgdGhpcy4kbmV4dFRpY2soXyA9PiB7DQogICAgICAgICAgdGhpcy4kcmVmc1snZHJhZnQnXT8ubWVyZ2VEYXRhKHRoaXMubXVsdGlwbGVTZWxlY3Rpb24pDQogICAgICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbiA9IFtdDQogICAgICAgIH0pDQogICAgICAgIHRoaXMuZGVsZXRlTG9hZGluZyA9IGZhbHNlDQogICAgICB9LCAwKQ0KICAgIH0sDQogICAgYXN5bmMgZ2V0V29ya1RlYW0oKSB7DQogICAgICBhd2FpdCBHZXRTY2hkdWxpbmdXb3JraW5nVGVhbXMoew0KICAgICAgICB0eXBlOiB0aGlzLmlzQ29tID8gMSA6IDINCiAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLndvcmtpbmdUZWFtID0gcmVzLkRhdGENCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVTdWJtaXQoKSB7DQogICAgICB0aGlzLiRyZWZzWydmb3JtSW5saW5lJ10udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICghdmFsaWQpIHJldHVybg0KICAgICAgICBjb25zdCB7IHRhYmxlRGF0YSwgc3RhdHVzIH0gPSB0aGlzLmdldFN1Ym1pdFRiSW5mbygpDQogICAgICAgIGlmICghc3RhdHVzKSByZXR1cm4NCiAgICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm5o+Q5Lqk5b2T5YmN5pWw5o2uPycsICfmj5DnpLonLCB7DQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLnNhdmVEcmFmdERvU3VibWl0KHRhYmxlRGF0YSkNCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgdHlwZTogJ2luZm8nLA0KICAgICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iCcNCiAgICAgICAgICB9KQ0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFzeW5jIHNhdmVEcmFmdERvU3VibWl0KCkgew0KICAgICAgdGhpcy5wZ0xvYWRpbmcgPSB0cnVlDQogICAgICBpZiAodGhpcy5mb3JtSW5saW5lPy5TY2hkdWxpbmdfQ29kZSkgew0KICAgICAgICBjb25zdCBpc1N1Y2Nlc3MgPSBhd2FpdCB0aGlzLnNhdmVEcmFmdCh0cnVlKQ0KICAgICAgICBjb25zb2xlLmxvZygnc2F2ZURyYWZ0RG9TdWJtaXQnLCBpc1N1Y2Nlc3MpDQogICAgICAgIGlzU3VjY2VzcyAmJiB0aGlzLmRvU3VibWl0KHRoaXMuZm9ybUlubGluZS5JZCkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNvbnN0IGlzU3VjY2VzcyA9IGF3YWl0IHRoaXMuc2F2ZURyYWZ0KHRydWUpDQogICAgICAgIGlzU3VjY2VzcyAmJiB0aGlzLmRvU3VibWl0KHRoaXMudGVtcGxhdGVTY2hlZHVsZUNvZGUpDQogICAgICB9DQogICAgfSwNCiAgICBkb1N1Ym1pdChzY2hlZHVsZUNvZGUpIHsNCiAgICAgIFNhdmVTY2hkdWxpbmdUYXNrQnlJZCh7DQogICAgICAgIHNjaGR1bGluZ1BsYW5JZDogc2NoZWR1bGVDb2RlDQogICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiAn5LiL6L6+5oiQ5YqfJywNCiAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy5jbG9zZVZpZXcoKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkuZmluYWxseShfID0+IHsNCiAgICAgICAgdGhpcy5wZ0xvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkuY2F0Y2goXyA9PiB7DQogICAgICAgIHRoaXMucGdMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICBnZXRXb3JrU2hvcCh2YWx1ZSkgew0KICAgICAgY29uc3Qgew0KICAgICAgICBvcmlnaW4sDQogICAgICAgIHJvdywNCiAgICAgICAgd29ya1Nob3A6IHsNCiAgICAgICAgICBJZCwNCiAgICAgICAgICBEaXNwbGF5X05hbWUNCiAgICAgICAgfQ0KICAgICAgfSA9IHZhbHVlDQogICAgICBpZiAob3JpZ2luID09PSAyKSB7DQogICAgICAgIGlmICh2YWx1ZS53b3JrU2hvcD8uSWQpIHsNCiAgICAgICAgICByb3cuV29ya3Nob3BfTmFtZSA9IERpc3BsYXlfTmFtZQ0KICAgICAgICAgIHJvdy5Xb3Jrc2hvcF9JZCA9IElkDQogICAgICAgICAgdGhpcy5zZXRQYXRoKHJvdywgSWQpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgcm93LldvcmtzaG9wX05hbWUgPSAnJw0KICAgICAgICAgIHJvdy5Xb3Jrc2hvcF9JZCA9ICcnDQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24uZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICBpZiAodmFsdWUud29ya1Nob3A/LklkKSB7DQogICAgICAgICAgICBpdGVtLldvcmtzaG9wX05hbWUgPSBEaXNwbGF5X05hbWUNCiAgICAgICAgICAgIGl0ZW0uV29ya3Nob3BfSWQgPSBJZA0KICAgICAgICAgICAgdGhpcy5zZXRQYXRoKGl0ZW0sIElkKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBpdGVtLldvcmtzaG9wX05hbWUgPSAnJw0KICAgICAgICAgICAgaXRlbS5Xb3Jrc2hvcF9JZCA9ICcnDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQogICAgc2V0UGF0aChyb3csIElkKSB7DQogICAgICBpZiAocm93Py5TY2hlZHVsZWRfV29ya3Nob3BfSWQpIHsNCiAgICAgICAgaWYgKHJvdy5TY2hlZHVsZWRfV29ya3Nob3BfSWQgIT09IElkKSB7DQogICAgICAgICAgcm93LlRlY2hub2xvZ3lfUGF0aCA9ICcnDQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJvdy5UZWNobm9sb2d5X1BhdGggPSAnJw0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlQmF0Y2hXb3Jrc2hvcChvcmlnaW4sIHJvdykgew0KICAgICAgdGhpcy50aXRsZSA9IG9yaWdpbiA9PT0gMSA/ICfmibnph4/liIbphY3ovabpl7QnIDogJ+WIhumFjei9pumXtCcNCiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9ICdXb3Jrc2hvcCcNCiAgICAgIHRoaXMuZFdpZHRoID0gJzMwJScNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuJG5leHRUaWNrKF8gPT4gew0KICAgICAgICB0aGlzLiRyZWZzWydjb250ZW50J10uZmV0Y2hEYXRhKG9yaWdpbiwgcm93KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFzeW5jIGhhbmRsZUF1dG9EZWFsKCkgew0KICAgICAgLyogICAgICBpZiAodGhpcy53b3Jrc2hvcEVuYWJsZWQpIHsNCiAgICAgICAgY29uc3QgaGFzV29ya1Nob3AgPSB0aGlzLmNoZWNrSGFzV29ya1Nob3AoMSwgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbikNCiAgICAgICAgaWYgKCFoYXNXb3JrU2hvcCkgcmV0dXJuDQogICAgICB9Ki8NCg0KICAgICAgdGhpcy4kY29uZmlybShg5piv5ZCm5bCG6YCJ5Lit5pWw5o2u5oyJ5p6E5Lu257G75Z6L6Ieq5Yqo5YiG6YWNYCwgJ+aPkOekuicsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgaWYgKHRoaXMud29ya3Nob3BFbmFibGVkKSB7DQogICAgICAgICAgY29uc3QgcCA9IHRoaXMubXVsdGlwbGVTZWxlY3Rpb24ubWFwKGl0ZW0gPT4gew0KICAgICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgICAgdW5pcXVlVHlwZTogYCR7aXRlbS5UeXBlfSRfJCR7aXRlbS5Xb3Jrc2hvcF9JZH1gDQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCiAgICAgICAgICBjb25zdCBjb2RlcyA9IEFycmF5LmZyb20obmV3IFNldChwLm1hcCh2ID0+IHYudW5pcXVlVHlwZSkpKQ0KICAgICAgICAgIGNvbnN0IG9iaktleSA9IHt9DQogICAgICAgICAgUHJvbWlzZS5hbGwoY29kZXMubWFwKHYgPT4gew0KICAgICAgICAgICAgY29uc3QgaW5mbyA9IHYuc3BsaXQoJyRfJCcpDQogICAgICAgICAgICByZXR1cm4gdGhpcy5zZXRMaWJUeXBlKGluZm9bMF0sIGluZm9bMV0pDQogICAgICAgICAgfSkNCiAgICAgICAgICApLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICAgIGNvbnN0IGhhc1VuZGVmaW5lZCA9IHJlcy5zb21lKGl0ZW0gPT4gaXRlbSA9PSB1bmRlZmluZWQpDQogICAgICAgICAgICBpZiAoaGFzVW5kZWZpbmVkKSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICfmiYDpgInovabpl7TlhoXlt6Xluo/nj63nu4TkuI7mnoTku7bnsbvlnovlt6Xluo/kuI3ljLnphY3vvIzor7fmiYvliqjliIbphY3lt6Xluo8nLA0KICAgICAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICByZXMuZm9yRWFjaCgoZWxlbWVudCwgaWR4KSA9PiB7DQogICAgICAgICAgICAgIG9iaktleVtjb2Rlc1tpZHhdXSA9IGVsZW1lbnQNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uLmZvckVhY2goKGVsZW1lbnQpID0+IHsNCiAgICAgICAgICAgICAgZWxlbWVudC5UZWNobm9sb2d5X1BhdGggPSBvYmpLZXlbYCR7ZWxlbWVudC5UeXBlfSRfJCR7ZWxlbWVudC5Xb3Jrc2hvcF9JZH1gXQ0KICAgICAgICAgICAgICB0aGlzLnJlc2V0V29ya1RlYW1NYXgoZWxlbWVudCwgZWxlbWVudC5UZWNobm9sb2d5X1BhdGgpDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0pDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc3QgcCA9IHRoaXMubXVsdGlwbGVTZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5UeXBlKQ0KICAgICAgICAgIGNvbnN0IGNvZGVzID0gQXJyYXkuZnJvbShuZXcgU2V0KHApKQ0KICAgICAgICAgIGNvbnN0IG9iaktleSA9IHt9DQoNCiAgICAgICAgICBQcm9taXNlLmFsbChjb2Rlcy5tYXAodiA9PiB7DQogICAgICAgICAgICByZXR1cm4gdGhpcy5zZXRMaWJUeXBlKHYpDQogICAgICAgICAgfSkpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICAgIHJlcy5mb3JFYWNoKChlbGVtZW50LCBpZHgpID0+IHsNCiAgICAgICAgICAgICAgb2JqS2V5W2NvZGVzW2lkeF1dID0gZWxlbWVudA0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24uZm9yRWFjaCgoZWxlbWVudCkgPT4gew0KICAgICAgICAgICAgICBlbGVtZW50LlRlY2hub2xvZ3lfUGF0aCA9IG9iaktleVtlbGVtZW50LlR5cGVdDQogICAgICAgICAgICAgIHRoaXMucmVzZXRXb3JrVGVhbU1heChlbGVtZW50LCBlbGVtZW50LlRlY2hub2xvZ3lfUGF0aCkNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICB0eXBlOiAnaW5mbycsDQogICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iCcNCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfSwNCiAgICBnZXRQcm9jZXNzT3B0aW9uKHdvcmtzaG9wSWQpIHsNCiAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7DQogICAgICAgIEdldFByb2Nlc3NMaXN0QmFzZSh7DQogICAgICAgICAgd29ya3Nob3BJZDogd29ya3Nob3BJZCwNCiAgICAgICAgICB0eXBlOiB0aGlzLmlzQ29tID8gMSA6IDIgLy8gMDrlhajpg6jvvIzlt6Xoibrnsbvlnosx77ya5p6E5Lu25bel6Im677yMMu+8mumbtuS7tuW3peiJug0KICAgICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIGNvbnN0IHByb2Nlc3MgPSByZXMuRGF0YS5tYXAodiA9PiB2LkNvZGUpDQogICAgICAgICAgICByZXNvbHZlKHByb2Nlc3MpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0sDQogICAgc2V0TGliVHlwZShjb2RlLCB3b3Jrc2hvcElkKSB7DQogICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHsNCiAgICAgICAgY29uc3Qgb2JqID0gew0KICAgICAgICAgIENvbXBvbmVudF90eXBlOiBjb2RlLA0KICAgICAgICAgIHR5cGU6IDENCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy53b3Jrc2hvcEVuYWJsZWQpIHsNCiAgICAgICAgICBvYmoud29ya3Nob3BJZCA9IHdvcmtzaG9wSWQNCiAgICAgICAgfQ0KICAgICAgICBHZXRMaWJMaXN0VHlwZShvYmopLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgaWYgKHJlcy5EYXRhLkRhdGEgJiYgcmVzLkRhdGEuRGF0YS5sZW5ndGgpIHsNCiAgICAgICAgICAgICAgY29uc3QgaW5mbyA9IHJlcy5EYXRhLkRhdGFbMF0NCiAgICAgICAgICAgICAgY29uc3Qgd29ya0NvZGUgPSBpbmZvLldvcmtDb2RlICYmIGluZm8uV29ya0NvZGUucmVwbGFjZSgvXFwvZywgJy8nKQ0KICAgICAgICAgICAgICByZXNvbHZlKHdvcmtDb2RlKQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgcmVzb2x2ZSh1bmRlZmluZWQpDQogICAgICAgICAgICB9DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0sDQogICAgaW5wdXRDaGFuZ2Uocm93KSB7DQogICAgICB0aGlzLnNldElucHV0TWF4KHJvdykNCiAgICB9LA0KICAgIHNldElucHV0TWF4KHJvdykgew0KICAgICAgY29uc3QgaW5wdXRWYWx1ZXNLZXlzID0gT2JqZWN0LmtleXMocm93KQ0KICAgICAgICAuZmlsdGVyKHYgPT4gIXYuZW5kc1dpdGgoJ21heCcpICYmIHYuc3RhcnRzV2l0aChyb3cudXVpZCkgJiYgdi5sZW5ndGggPiByb3cudXVpZC5sZW5ndGgpDQogICAgICBpbnB1dFZhbHVlc0tleXMuZm9yRWFjaCgodmFsKSA9PiB7DQogICAgICAgIGNvbnN0IGN1ckNvZGUgPSB2YWwuc3BsaXQoU1BMSVRfU1lNQk9MKVsxXQ0KICAgICAgICBjb25zdCBvdGhlclRvdGFsID0gaW5wdXRWYWx1ZXNLZXlzLmZpbHRlcih4ID0+IHsNCiAgICAgICAgICBjb25zdCBjb2RlID0geC5zcGxpdChTUExJVF9TWU1CT0wpWzFdDQogICAgICAgICAgcmV0dXJuIHggIT09IHZhbCAmJiBjb2RlID09PSBjdXJDb2RlDQogICAgICAgIH0pLnJlZHVjZSgoYWNjLCBpdGVtKSA9PiB7DQogICAgICAgICAgcmV0dXJuIGFjYyArIG51bWVyYWwocm93W2l0ZW1dKS52YWx1ZSgpDQogICAgICAgIH0sIDApDQogICAgICAgIHJvd1t2YWwgKyBTUExJVF9TWU1CT0wgKyAnbWF4J10gPSByb3cuU2NoZHVsZWRfQ291bnQgLSBvdGhlclRvdGFsDQogICAgICB9KQ0KICAgIH0sDQogICAgc2VuZFByb2Nlc3MoeyBhcnIsIHN0ciB9KSB7DQogICAgICBsZXQgaXNTdWNjZXNzID0gdHJ1ZQ0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBhcnIubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgY29uc3QgaXRlbSA9IGFycltpXQ0KICAgICAgICBpZiAoaXRlbS5vcmlnaW5hbFBhdGggJiYgaXRlbS5vcmlnaW5hbFBhdGggIT09IHN0cikgew0KICAgICAgICAgIGlzU3VjY2VzcyA9IGZhbHNlDQogICAgICAgICAgYnJlYWsNCiAgICAgICAgfQ0KICAgICAgICBpdGVtLlRlY2hub2xvZ3lfUGF0aCA9IHN0cg0KICAgICAgfQ0KICAgICAgaWYgKCFpc1N1Y2Nlc3MpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogJ+ivt+WSjOivpeWMuuWfn+aJueasoeS4i+W3suaOkuS6p+WQjOaehOS7tuS/neaMgeW3peW6j+S4gOiHtCcsDQogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICByZXNldFdvcmtUZWFtTWF4KHJvdywgc3RyKSB7DQogICAgICBpZiAoc3RyKSB7DQogICAgICAgIHJvdy5UZWNobm9sb2d5X1BhdGggPSBzdHINCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHN0ciA9IHJvdy5UZWNobm9sb2d5X1BhdGgNCiAgICAgIH0NCiAgICAgIGNvbnN0IGxpc3QgPSBzdHI/LnNwbGl0KCcvJykgfHwgW10NCiAgICAgIHRoaXMud29ya2luZ1RlYW0uZm9yRWFjaCgoZWxlbWVudCwgaWR4KSA9PiB7DQogICAgICAgIGNvbnN0IGN1ciA9IGxpc3Quc29tZShrID0+IGsgPT09IGVsZW1lbnQuUHJvY2Vzc19Db2RlKQ0KICAgICAgICBjb25zdCBjb2RlID0gdGhpcy5nZXRSb3dVbmlxdWUocm93LnV1aWQsIGVsZW1lbnQuUHJvY2Vzc19Db2RlLCBlbGVtZW50LldvcmtpbmdfVGVhbV9JZCkNCiAgICAgICAgY29uc3QgbWF4ID0gdGhpcy5nZXRSb3dVbmlxdWVNYXgocm93LnV1aWQsIGVsZW1lbnQuUHJvY2Vzc19Db2RlLCBlbGVtZW50LldvcmtpbmdfVGVhbV9JZCkNCiAgICAgICAgaWYgKGN1cikgew0KICAgICAgICAgIGlmICghcm93W2NvZGVdKSB7DQogICAgICAgICAgICB0aGlzLiRzZXQocm93LCBjb2RlLCAwKQ0KICAgICAgICAgICAgdGhpcy4kc2V0KHJvdywgbWF4LCByb3cuU2NoZHVsZWRfQ291bnQpDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJGRlbGV0ZShyb3csIGNvZGUpDQogICAgICAgICAgdGhpcy4kZGVsZXRlKHJvdywgbWF4KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgY2hlY2tQZXJtaXNzaW9uVGVhbShwcm9jZXNzU3RyLCBwcm9jZXNzQ29kZSkgew0KICAgICAgaWYgKCFwcm9jZXNzU3RyKSByZXR1cm4gZmFsc2UNCiAgICAgIGNvbnN0IGxpc3QgPSBwcm9jZXNzU3RyPy5zcGxpdCgnLycpIHx8IFtdDQogICAgICByZXR1cm4gISFsaXN0LnNvbWUodiA9PiB2ID09PSBwcm9jZXNzQ29kZSkNCiAgICB9LA0KDQogICAgYXN5bmMgZ2V0VGFibGVDb25maWcoY29kZSkgew0KICAgICAgYXdhaXQgR2V0R3JpZEJ5Q29kZSh7DQogICAgICAgIGNvZGUNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBjb25zdCB7IElzU3VjY2VlZCwgRGF0YSwgTWVzc2FnZSB9ID0gcmVzDQogICAgICAgIGlmIChJc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLnRiQ29uZmlnID0gT2JqZWN0LmFzc2lnbih7fSwgdGhpcy50YkNvbmZpZywgRGF0YS5HcmlkKQ0KICAgICAgICAgIGNvbnN0IGxpc3QgPSBEYXRhLkNvbHVtbkxpc3QgfHwgW10NCiAgICAgICAgICB0aGlzLm93bmVyQ29sdW1uID0gbGlzdC5maW5kKGl0ZW0gPT4gaXRlbS5Db2RlID09PSAnUGFydF9Vc2VkX1Byb2Nlc3MnKQ0KICAgICAgICAgIHRoaXMub3duZXJDb2x1bW4yID0gbGlzdC5maW5kKGl0ZW0gPT4gaXRlbS5Db2RlID09PSAnSXNfTWFpbl9QYXJ0JykNCiAgICAgICAgICB0aGlzLmNvbHVtbnMgPSB0aGlzLnNldENvbHVtbkRpc3BsYXkobGlzdCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IE1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHNldENvbHVtbkRpc3BsYXkobGlzdCkgew0KICAgICAgcmV0dXJuIGxpc3QuZmlsdGVyKHYgPT4gdi5Jc19EaXNwbGF5KS5tYXAoaXRlbSA9PiB7DQogICAgICAgIGlmIChGSVhfQ09MVU1OLmluY2x1ZGVzKGl0ZW0uQ29kZSkpIHsNCiAgICAgICAgICBpdGVtLmZpeGVkID0gJ2xlZnQnDQogICAgICAgIH0NCiAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgIH0pDQogICAgfSwNCiAgICBhY3RpdmVDZWxsTWV0aG9kKHsgcm93LCBjb2x1bW4sIGNvbHVtbkluZGV4IH0pIHsNCiAgICAgIGlmICh0aGlzLmlzVmlldykgcmV0dXJuIGZhbHNlDQogICAgICBjb25zdCBwcm9jZXNzQ29kZSA9IGNvbHVtbi5maWVsZD8uc3BsaXQoJyRfJCcpWzFdDQogICAgICByZXR1cm4gdGhpcy5jaGVja1Blcm1pc3Npb25UZWFtKHJvdy5UZWNobm9sb2d5X1BhdGgsIHByb2Nlc3NDb2RlKQ0KICAgIH0sDQogICAgb3BlbkJQQURpYWxvZyh0eXBlLCByb3cpIHsNCiAgICAgIGlmICh0aGlzLndvcmtzaG9wRW5hYmxlZCkgew0KICAgICAgICBpZiAodHlwZSA9PT0gMSkgew0KICAgICAgICAgIGNvbnN0IElzVW5pcXVlID0gdGhpcy5jaGVja0lzVW5pcXVlV29ya3Nob3AoKQ0KICAgICAgICAgIGlmICghSXNVbmlxdWUpIHJldHVybg0KICAgICAgICB9DQogICAgICB9DQogICAgICB0aGlzLnRpdGxlID0gdHlwZSA9PT0gMiA/ICflt6Xluo/osIPmlbQnIDogJ+aJuemHj+W3peW6j+iwg+aVtCcNCiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9ICdCYXRjaFByb2Nlc3NBZGp1c3QnDQogICAgICB0aGlzLmRXaWR0aCA9IHRoaXMuaXNDb20gPyAnNjAlJyA6ICczMCUnDQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgICB0aGlzLiRuZXh0VGljayhfID0+IHsNCiAgICAgICAgdGhpcy4kcmVmc1snY29udGVudCddLnNldERhdGEodHlwZSA9PT0gMiA/IFtyb3ddIDogdGhpcy5tdWx0aXBsZVNlbGVjdGlvbiwgdHlwZSA9PT0gMiA/IHJvdy5UZWNobm9sb2d5X1BhdGggOiAnJykNCiAgICAgIH0pDQogICAgfSwNCiAgICBjaGVja0lzVW5pcXVlV29ya3Nob3AoKSB7DQogICAgICBsZXQgaXNVbmlxdWUgPSB0cnVlDQogICAgICBjb25zdCBmaXJzdFYgPSB0aGlzLm11bHRpcGxlU2VsZWN0aW9uWzBdLldvcmtzaG9wX05hbWUNCiAgICAgIGZvciAobGV0IGkgPSAxOyBpIDwgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5sZW5ndGg7IGkrKykgew0KICAgICAgICBjb25zdCBpdGVtID0gdGhpcy5tdWx0aXBsZVNlbGVjdGlvbltpXQ0KICAgICAgICBpZiAoaXRlbS5Xb3Jrc2hvcF9OYW1lICE9PSBmaXJzdFYpIHsNCiAgICAgICAgICBpc1VuaXF1ZSA9IGZhbHNlDQogICAgICAgICAgYnJlYWsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgaWYgKCFpc1VuaXF1ZSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAn5om56YeP5YiG6YWN5bel5bqP5pe25Y+q5pyJ55u45ZCM6L2m6Ze05LiL55qE5omN5Y+v5LiA6LW35om56YeP5YiG6YWNJywNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICAgIHJldHVybiBpc1VuaXF1ZQ0KICAgIH0sDQogICAgY2hlY2tIYXNXb3JrU2hvcCh0eXBlLCBhcnIpIHsNCiAgICAgIGxldCBoYXNXb3JrU2hvcCA9IHRydWUNCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgYXJyLmxlbmd0aDsgaSsrKSB7DQogICAgICAgIGNvbnN0IGl0ZW0gPSBhcnJbaV0NCiAgICAgICAgaWYgKCFpdGVtLldvcmtzaG9wX05hbWUpIHsNCiAgICAgICAgICBoYXNXb3JrU2hvcCA9IGZhbHNlDQogICAgICAgICAgYnJlYWsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgaWYgKCFoYXNXb3JrU2hvcCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAn6K+35YWI6YCJ5oup6L2m6Ze05ZCO5YaN6L+b6KGM5bel5bqP5YiG6YWNJywNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICAgIHJldHVybiBoYXNXb3JrU2hvcA0KICAgIH0sDQogICAgaGFuZGxlQWRkRGlhbG9nKHR5cGUgPSAnYWRkJykgew0KICAgICAgaWYgKHRoaXMuaXNDb20pIHsNCiAgICAgICAgdGhpcy50aXRsZSA9ICfmnoTku7bmjpLkuqcnDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnRpdGxlID0gJ+a3u+WKoOmbtuS7ticNCiAgICAgIH0NCiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9ICdBZGREcmFmdCcNCiAgICAgIHRoaXMuZFdpZHRoID0gJzgwJScNCiAgICAgIHRoaXMub3BlbkFkZERyYWZ0ID0gdHJ1ZQ0KICAgICAgdGhpcy4kbmV4dFRpY2soXyA9PiB7DQogICAgICAgIHRoaXMuJHJlZnNbJ2RyYWZ0J10uc2V0UGFnZURhdGEoKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGdldFJvd1VuaXF1ZSh1dWlkLCBwcm9jZXNzQ29kZSwgd29ya2luZ0lkKSB7DQogICAgICByZXR1cm4gYCR7dXVpZH0ke1NQTElUX1NZTUJPTH0ke3Byb2Nlc3NDb2RlfSR7U1BMSVRfU1lNQk9MfSR7d29ya2luZ0lkfWANCiAgICB9LA0KICAgIGdldFJvd1VuaXF1ZU1heCh1dWlkLCBwcm9jZXNzQ29kZSwgd29ya2luZ0lkKSB7DQogICAgICByZXR1cm4gdGhpcy5nZXRSb3dVbmlxdWUodXVpZCwgcHJvY2Vzc0NvZGUsIHdvcmtpbmdJZCkgKyBgJHtTUExJVF9TWU1CT0x9bWF4YA0KICAgIH0sDQogICAgaGFuZGxlU2VsZWN0TWVudSh2KSB7DQogICAgICBpZiAodiA9PT0gJ3Byb2Nlc3MnKSB7DQogICAgICAgIHRoaXMub3BlbkJQQURpYWxvZygxKQ0KICAgICAgfSBlbHNlIGlmICh2ID09PSAnZGVhbCcpIHsNCiAgICAgICAgdGhpcy5oYW5kbGVBdXRvRGVhbCgxKQ0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlQmF0Y2hPd25lcih0eXBlLCByb3cpIHsNCiAgICAgIHRoaXMudGl0bGUgPSAn5om56YeP5YiG6YWN6aKG55So5bel5bqPJw0KICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ093bmVyUHJvY2VzcycNCiAgICAgIHRoaXMuZFdpZHRoID0gJzMwJScNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuJG5leHRUaWNrKF8gPT4gew0KICAgICAgICB0aGlzLiRyZWZzWydjb250ZW50J10uc2V0T3B0aW9uKHR5cGUgPT09IDIsIHR5cGUgPT09IDIgPyBbcm93XSA6IHRoaXMubXVsdGlwbGVTZWxlY3Rpb24pDQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlUmV2ZXJzZSgpIHsNCiAgICAgIGNvbnN0IGN1ciA9IFtdDQogICAgICB0aGlzLnRiRGF0YS5mb3JFYWNoKChlbGVtZW50LCBpZHgpID0+IHsNCiAgICAgICAgZWxlbWVudC5jaGVja2VkID0gIWVsZW1lbnQuY2hlY2tlZA0KICAgICAgICBpZiAoZWxlbWVudC5jaGVja2VkKSB7DQogICAgICAgICAgY3VyLnB1c2goZWxlbWVudCkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24gPSBjdXINCiAgICAgIGlmICh0aGlzLm11bHRpcGxlU2VsZWN0aW9uLmxlbmd0aCA9PT0gdGhpcy50YkRhdGEubGVuZ3RoKSB7DQogICAgICAgIHRoaXMuJHJlZnNbJ3hUYWJsZSddLnNldEFsbENoZWNrYm94Um93KHRydWUpDQogICAgICB9DQogICAgICBpZiAodGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kcmVmc1sneFRhYmxlJ10uc2V0QWxsQ2hlY2tib3hSb3coZmFsc2UpDQogICAgICB9DQogICAgfSwNCiAgICB0YkZpbHRlckNoYW5nZSgpIHsNCiAgICAgIGNvbnN0IHhUYWJsZSA9IHRoaXMuJHJlZnMueFRhYmxlDQogICAgICBjb25zdCBjb2x1bW4gPSB4VGFibGUuZ2V0Q29sdW1uQnlGaWVsZCgnVHlwZV9OYW1lJykNCiAgICAgIGlmICghY29sdW1uPy5maWx0ZXJzPy5sZW5ndGgpIHJldHVybg0KICAgICAgY29sdW1uLmZpbHRlcnMuZm9yRWFjaChkID0+IHsNCiAgICAgICAgZC5jaGVja2VkID0gZC52YWx1ZSA9PT0gdGhpcy5zZWFyY2hUeXBlDQogICAgICB9KQ0KICAgICAgeFRhYmxlLnVwZGF0ZURhdGEoKQ0KICAgIH0sDQogICAgZ2V0VHlwZSgpIHsNCiAgICAgIEdldFBhcnRUeXBlTGlzdCh7IFBhcnRfR3JhZGU6IDAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMudHlwZU9wdGlvbiA9IHJlcy5EYXRhLm1hcCh2ID0+IHsNCiAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgIGxhYmVsOiB2Lk5hbWUsDQogICAgICAgICAgICAgIHZhbHVlOiB2Lk5hbWUsDQogICAgICAgICAgICAgIGNvZGU6IHYuQ29kZQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgc2V0UHJvY2Vzc0xpc3QoaW5mbykgew0KICAgICAgdGhpcy5jaGFuZ2VQcm9jZXNzTGlzdChpbmZvKQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["draft.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAo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file": "draft.vue", "sourceRoot": "src/views/PRO/plan-production/schedule-production", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <el-card v-loading=\"pgLoading\" class=\"box-card h100\" element-loading-text=\"正在处理...\">\r\n      <h4 class=\"topTitle\"><span />基本信息</h4>\r\n      <el-form\r\n        ref=\"formInline\"\r\n        :inline=\"true\"\r\n        :model=\"formInline\"\r\n        class=\"demo-form-inline\"\r\n      >\r\n        <el-form-item v-if=\"!isAdd\" label=\"排产单号\" prop=\"Schduling_Code\">\r\n          <span v-if=\"isView\">{{ formInline.Status === 0 ? '' : formInline.Schduling_Code }}</span>\r\n          <el-input v-else v-model=\"formInline.Schduling_Code\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"计划员\" prop=\"Create_UserName\">\r\n          <span v-if=\"isView\">{{ formInline.Create_UserName }}</span>\r\n          <el-input\r\n            v-else\r\n            v-model=\"formInline.Create_UserName\"\r\n            disabled\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"要求完成时间\"\r\n          prop=\"Finish_Date\"\r\n          :rules=\"{ required: true, message: '请选择', trigger: 'change' }\"\r\n        >\r\n          <span v-if=\"isView\">{{ formInline.Finish_Date | timeFormat }}</span>\r\n          <el-date-picker\r\n            v-else\r\n            v-model=\"formInline.Finish_Date\"\r\n            :picker-options=\"pickerOptions\"\r\n            :disabled=\"isView\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            placeholder=\"选择日期\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"Remark\">\r\n          <span v-if=\"isView\">{{ formInline.Remark }}</span>\r\n          <el-input\r\n            v-else\r\n            v-model=\"formInline.Remark\"\r\n            :disabled=\"isView\"\r\n            style=\"width: 320px\"\r\n            placeholder=\"请输入\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-divider class=\"elDivder\" />\r\n      <div class=\"btn-x\">\r\n        <template v-if=\"!isView\">\r\n          <div>\r\n            <el-button type=\"primary\" @click=\"handleAddDialog()\">添加</el-button>\r\n            <el-button\r\n              v-if=\"workshopEnabled\"\r\n              :disabled=\"!multipleSelection.length\"\r\n              @click=\"handleBatchWorkshop(1)\"\r\n            >批量分配车间\r\n            </el-button>\r\n            <el-button\r\n              v-if=\"!isCom\"\r\n              :disabled=\"!multipleSelection.length\"\r\n              @click=\"handleSelectMenu('process')\"\r\n            >批量分配工序\r\n            </el-button>\r\n            <el-dropdown v-if=\"isCom\" style=\"margin:0 10px\" @command=\"handleSelectMenu\">\r\n              <el-button :disabled=\"!multipleSelection.length\" type=\"primary\" plain style=\"width: 140px\">\r\n                分配工序<i class=\"el-icon-arrow-down el-icon--right\" />\r\n              </el-button>\r\n              <el-dropdown-menu slot=\"dropdown\">\r\n                <el-dropdown-item\r\n                  command=\"process\"\r\n                >批量分配工序\r\n                </el-dropdown-item>\r\n                <el-dropdown-item\r\n                  v-if=\"isCom\"\r\n                  command=\"deal\"\r\n                >构件类型自动分配\r\n                </el-dropdown-item>\r\n              </el-dropdown-menu>\r\n            </el-dropdown>\r\n            <el-button\r\n              v-if=\"!isCom && !isOwnerNull\"\r\n              :disabled=\"!multipleSelection.length\"\r\n              @click=\"handleBatchOwner(1)\"\r\n            >批量分配领用工序\r\n            </el-button>\r\n            <el-button\r\n              plain\r\n              :disabled=\"!tbData.length\"\r\n              :loading=\"false\"\r\n              @click=\"handleReverse\"\r\n            >反选\r\n            </el-button>\r\n            <el-button\r\n              type=\"danger\"\r\n              plain\r\n              :loading=\"deleteLoading\"\r\n              :disabled=\"!multipleSelection.length\"\r\n              @click=\"handleDelete\"\r\n            >删除\r\n            </el-button>\r\n          </div>\r\n          <div v-if=\"!isCom\">\r\n            <el-select\r\n              v-model=\"searchType\"\r\n              placeholder=\"请选择\"\r\n              clearable\r\n              @change=\"tbFilterChange\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in typeOption\"\r\n                :key=\"item.value\"\r\n                :label=\"item.label\"\r\n                :value=\"item.label\"\r\n              />\r\n            </el-select>\r\n          </div>\r\n        </template>\r\n      </div>\r\n      <div class=\"tb-x\">\r\n        <vxe-table\r\n          ref=\"xTable\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          :checkbox-config=\"{checkField: 'checked'}\"\r\n          class=\"cs-vxe-table\"\r\n          :row-config=\"{isCurrent: true, isHover: true}\"\r\n          align=\"left\"\r\n          height=\"100%\"\r\n          :filter-config=\"{showIcon:false}\"\r\n          show-overflow\r\n          :loading=\"tbLoading\"\r\n          stripe\r\n          :scroll-y=\"{enabled: true, gt: 20}\"\r\n          size=\"medium\"\r\n          :edit-config=\"{\r\n            trigger: 'click',\r\n            mode: 'cell',\r\n            showIcon: !isView,\r\n            activeMethod: activeCellMethod,\r\n          }\"\r\n          :data=\"tbData\"\r\n          resizable\r\n          :tooltip-config=\"{ enterable: true }\"\r\n          @checkbox-all=\"tbSelectChange\"\r\n          @checkbox-change=\"tbSelectChange\"\r\n        >\r\n          <vxe-column v-if=\"!isView\" fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n          <template v-for=\"item in columns\">\r\n            <vxe-column\r\n              v-if=\"item.Code === 'Is_Component'\"\r\n              :key=\"item.Code\"\r\n              :align=\"item.Align\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              sortable\r\n              :width=\"item.Width\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                <el-tag\r\n                  :type=\"row.Is_Component ? 'danger' : 'success'\"\r\n                >{{ row.Is_Component ? '否' : '是' }}\r\n                </el-tag>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column\r\n              v-else-if=\"item.Code === 'Type_Name'\"\r\n              :key=\"item.Code\"\r\n              :align=\"item.Align\"\r\n              :field=\"item.Code\"\r\n              :filters=\"typeOption\"\r\n              :title=\"item.Display_Name\"\r\n              sortable\r\n              :width=\"item.Width\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                {{ row.Type_Name | displayValue }}\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column\r\n              v-else-if=\"item.Code === 'Technology_Path'\"\r\n              :key=\"item.Code\"\r\n              :align=\"item.Align\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              sortable\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                {{ row.Technology_Path | displayValue }}\r\n                <i\r\n                  v-if=\"!isView\"\r\n                  class=\"el-icon-edit\"\r\n                  @click=\"openBPADialog(2, row)\"\r\n                />\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column\r\n              v-else-if=\"item.Code === 'Part_Used_Process'\"\r\n              :key=\"item.Code\"\r\n              :align=\"item.Align\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              sortable\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                {{ row.Part_Used_Process | displayValue }}\r\n                <i\r\n                  v-if=\"!isView\"\r\n                  class=\"el-icon-edit\"\r\n                  @click=\"handleBatchOwner(2, row)\"\r\n                />\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column\r\n              v-else-if=\"item.Code === 'Workshop_Name'\"\r\n              :key=\"item.Code\"\r\n              :align=\"item.Align\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              sortable\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                {{ row.Workshop_Name | displayValue }}\r\n                <i\r\n                  v-if=\"!isView\"\r\n                  class=\"el-icon-edit\"\r\n                  @click=\"handleBatchWorkshop(2, row)\"\r\n                />\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column\r\n              v-else\r\n              :key=\"item.Id\"\r\n              :align=\"item.Align\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n            />\r\n          </template>\r\n\r\n        </vxe-table>\r\n      </div>\r\n      <el-divider v-if=\"!isView\" class=\"elDivder\" />\r\n      <footer v-if=\"!isView\">\r\n        <div class=\"data-info\">\r\n          <el-tag\r\n            size=\"medium\"\r\n            class=\"info-x\"\r\n          >已选 {{ multipleSelection.length }} 条数据\r\n          </el-tag>\r\n          <el-tag v-if=\"tipLabel\" size=\"medium\" class=\"info-x\">{{\r\n            tipLabel\r\n          }}\r\n          </el-tag>\r\n        </div>\r\n        <div>\r\n          <el-button v-if=\"workshopEnabled\" type=\"primary\" @click=\"saveWorkShop\">保存车间分配</el-button>\r\n          <el-button\r\n            type=\"primary\"\r\n            :loading=\"saveLoading\"\r\n            @click=\"saveDraft(false)\"\r\n          >保存草稿\r\n          </el-button>\r\n          <el-button :disabled=\"deleteLoading\" @click=\"handleSubmit\">下发任务</el-button>\r\n        </div>\r\n      </footer>\r\n    </el-card>\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"dWidth\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :process-list=\"processList\"\r\n        :page-type=\"pageType\"\r\n        :part-type-option=\"typeOption\"\r\n        @close=\"handleClose\"\r\n        @sendProcess=\"sendProcess\"\r\n        @workShop=\"getWorkShop\"\r\n        @refresh=\"fetchData\"\r\n        @setProcessList=\"setProcessList\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"openAddDraft\"\r\n      :width=\"dWidth\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <keep-alive>\r\n        <add-draft\r\n          ref=\"draft\"\r\n          :schedule-id=\"scheduleId\"\r\n          :show-dialog=\"openAddDraft\"\r\n          :page-type=\"pageType\"\r\n          @sendSelectList=\"mergeSelectList\"\r\n          @close=\"handleClose\"\r\n        />\r\n      </keep-alive>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { closeTagView, debounce } from '@/utils'\r\nimport BatchProcessAdjust from './components/BatchProcessAdjust'\r\nimport {\r\n  GetCompSchdulingInfoDetail,\r\n  GetPartSchdulingInfoDetail,\r\n  GetSchdulingWorkingTeams,\r\n  SaveComponentSchedulingWorkshop,\r\n  SaveCompSchdulingDraft,\r\n  SavePartSchdulingDraft,\r\n  SavePartSchedulingWorkshop,\r\n  SaveSchdulingTaskById\r\n} from '@/api/PRO/production-task'\r\nimport AddDraft from './components/addDraft'\r\nimport OwnerProcess from './components/OwnerProcess'\r\nimport Workshop from './components/Workshop.vue'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { FIX_COLUMN, uniqueCode } from '@/views/PRO/plan-production/schedule-production/constant'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport numeral from 'numeral'\r\nimport { GetLibListType, GetProcessListBase } from '@/api/PRO/technology-lib'\r\nimport { AreaGetEntity } from '@/api/plm/projects'\r\nimport { mapActions, mapGetters } from 'vuex'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\nimport * as moment from 'moment/moment'\r\n\r\nconst SPLIT_SYMBOL = '$_$'\r\nexport default {\r\n  components: { BatchProcessAdjust, AddDraft, Workshop, OwnerProcess },\r\n  data() {\r\n    return {\r\n      pickerOptions: {\r\n        disabledDate(time) {\r\n        }\r\n      },\r\n      searchType: '',\r\n      formInline: {\r\n        Schduling_Code: '',\r\n        Create_UserName: '',\r\n        Finish_Date: '',\r\n        Remark: ''\r\n      },\r\n      total: 0,\r\n      columns: [],\r\n      tbData: [],\r\n      tbConfig: {},\r\n      TotalCount: 0,\r\n      multipleSelection: [],\r\n      pgLoading: false,\r\n      deleteLoading: false,\r\n      workShopIsOpen: false,\r\n      isOwnerNull: false,\r\n      dialogVisible: false,\r\n      openAddDraft: false,\r\n      saveLoading: false,\r\n      tbLoading: false,\r\n      isCheckAll: false,\r\n      currentComponent: '',\r\n      dWidth: '25%',\r\n      title: '',\r\n      search: () => ({}),\r\n      pageType: undefined,\r\n      tipLabel: '',\r\n      technologyOption: [],\r\n      typeOption: [],\r\n      workingTeam: [],\r\n      pageStatus: undefined,\r\n      scheduleId: '',\r\n      partComOwnerColumn: null\r\n    }\r\n  },\r\n  watch: {\r\n    'tbData.length': {\r\n      handler(n, o) {\r\n        this.checkOwner()\r\n      },\r\n      immediate: false\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    },\r\n    isView() {\r\n      return this.pageStatus === 'view'\r\n    },\r\n    isEdit() {\r\n      return this.pageStatus === 'edit'\r\n    },\r\n    isAdd() {\r\n      return this.pageStatus === 'add'\r\n    },\r\n    ...mapGetters('factoryInfo', ['workshopEnabled']),\r\n    ...mapGetters('schedule', ['processList'])\r\n  },\r\n  beforeRouteEnter(to, from, next) {\r\n    if (to.query.status === 'view') {\r\n      to.meta.title = '查看'\r\n    } else {\r\n      to.meta.title = '草稿'\r\n    }\r\n    next()\r\n  },\r\n  async mounted() {\r\n    this.initProcessList()\r\n    this.tbDataMap = {}\r\n    this.pageType = this.$route.query.pg_type\r\n    this.pageStatus = this.$route.query.status\r\n    this.model = this.$route.query.model\r\n    this.scheduleId = this.$route.query.pid || ''\r\n    // this.formInline.Create_UserName = this.$store.getters.name\r\n    // 框架问题引起store数据丢失，已反馈，结果：此处先使用localStorage\r\n    this.formInline.Create_UserName = localStorage.getItem('UserAccount')\r\n    if (!this.isCom) {\r\n      this.getType()\r\n    } else {\r\n\r\n    }\r\n    this.unique = uniqueCode()\r\n    this.checkWorkshopIsOpen()\r\n    this.getAreaInfo()\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    await this.mergeConfig()\r\n    if (this.isView || this.isEdit) {\r\n      this.fetchData()\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions('schedule', ['changeProcessList', 'initProcessList']),\r\n    checkOwner() {\r\n      if (this.isCom) return\r\n      this.isOwnerNull = this.tbData.every(v => !v.Comp_Import_Detail_Id)\r\n      const idx = this.columns.findIndex(v => v.Code === 'Part_Used_Process')\r\n      if (this.isOwnerNull) {\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n      } else {\r\n        if (idx === -1) {\r\n          if (!this.ownerColumn) {\r\n            this.$message({\r\n              message: '列表配置字段缺少零件领用工序字段',\r\n              type: 'success'\r\n            })\r\n            return\r\n          }\r\n          this.columns.push(this.ownerColumn)\r\n        }\r\n      }\r\n    },\r\n    async mergeConfig() {\r\n      await this.getConfig()\r\n      await this.getWorkTeam()\r\n    },\r\n    async getConfig() {\r\n      const configCode = this.isCom ? (this.isView ? 'PROComViewPageTbConfig' : 'PROComDraftPageTbConfig') : (this.isView ? 'PROPartViewPageTbConfig' : 'PROPartDraftPageTbConfig')\r\n\r\n      await this.getTableConfig(configCode)\r\n      if (!this.workshopEnabled) {\r\n        this.columns = this.columns.filter(v => v.Code !== 'Workshop_Name')\r\n      }\r\n      this.checkOwner()\r\n    },\r\n    async fetchData() {\r\n      this.tbLoading = true\r\n      let resData = null\r\n      if (this.isCom) {\r\n        resData = await this.getComPageList()\r\n      } else {\r\n        resData = await this.getPartPageList()\r\n      }\r\n      this.initTbData(resData)\r\n      this.tbLoading = false\r\n    },\r\n    closeView() {\r\n      closeTagView(this.$store, this.$route)\r\n    },\r\n    checkWorkshopIsOpen() {\r\n      this.workShopIsOpen = true\r\n    },\r\n    tbSelectChange(array) {\r\n      this.multipleSelection = array.records\r\n    },\r\n    getAreaInfo() {\r\n      AreaGetEntity({\r\n        id: this.$route.query.areaId\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (!res.Data) {\r\n            return []\r\n          }\r\n\r\n          const start = moment(res.Data?.Demand_Begin_Date)\r\n          const end = moment(res.Data?.Demand_End_Date)\r\n          this.pickerOptions.disabledDate = (time) => {\r\n            return time.getTime() < start || time.getTime() > end\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n      this.openAddDraft = false\r\n    },\r\n    getComPageList() {\r\n      return new Promise((resolve, reject) => {\r\n        const {\r\n          // install,\r\n          // projectId,\r\n          pid\r\n          // areaId\r\n        } = this.$route.query\r\n        GetCompSchdulingInfoDetail({\r\n          Schduling_Plan_Id: pid\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            const { Schduling_Plan, Schduling_Comps, Process_List } = res.Data\r\n            this.formInline = Object.assign(this.formInline, Schduling_Plan)\r\n            Process_List.forEach(item => {\r\n              const plist = {\r\n                key: item.Process_Code,\r\n                value: item\r\n              }\r\n              this.changeProcessList(plist)\r\n            })\r\n            resolve(Schduling_Comps || [])\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    getPartPageList() {\r\n      return new Promise((resolve, reject) => {\r\n        const {\r\n          pid\r\n        } = this.$route.query\r\n        GetPartSchdulingInfoDetail({\r\n          Schduling_Plan_Id: pid\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            const SarePartsModel = res.Data?.SarePartsModel.map(v => {\r\n              if (v.Scheduled_Used_Process) {\r\n                // 已存在操作过数据\r\n                v.Part_Used_Process = v.Scheduled_Used_Process\r\n              }\r\n              return v\r\n            })\r\n            this.formInline = Object.assign(this.formInline, res.Data?.Schduling_Plan)\r\n            resolve(SarePartsModel || [])\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    initTbData(list, teamKey = 'Allocation_Teams') {\r\n      this.tbData = list.map(row => {\r\n        const processList = row.Technology_Path?.split('/') || []\r\n        row.uuid = uuidv4()\r\n        this.addElementToTbData(row)\r\n        const newData = row[teamKey].filter((r) => processList.findIndex((p) => r.Process_Code === p) !== -1)\r\n        newData.forEach((ele, index) => {\r\n          const code = this.getRowUnique(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n          const max = this.getRowUniqueMax(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n          row[code] = ele.Count\r\n          row[max] = 0\r\n        })\r\n        this.setInputMax(row)\r\n        return row\r\n      })\r\n    },\r\n    mergeSelectList(newList) {\r\n      console.time('fff')\r\n      newList.forEach((element, index) => {\r\n        const cur = this.getMergeUniqueRow(element)\r\n\r\n        if (!cur) {\r\n          element.puuid = element.uuid\r\n          element.Schduled_Count = element.chooseCount\r\n          element.Schduled_Weight = numeral(element.chooseCount * element.Weight).format('0.[00]')\r\n          this.tbData.push(element)\r\n          this.addElementToTbData(element)\r\n          return\r\n        }\r\n\r\n        cur.puuid = element.uuid\r\n\r\n        cur.Schduled_Count += element.chooseCount\r\n        cur.Schduled_Weight = numeral(cur.Schduled_Weight).add(element.chooseCount * element.Weight).format('0.[00]')\r\n        if (!cur.Technology_Path) {\r\n          return\r\n        }\r\n        this.setInputMax(cur)\r\n      })\r\n\r\n      // if (this.isCom) {\r\n      //   this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n      // } else {\r\n      //   this.tbData.sort((a, b) => a.Part_Code.localeCompare(b.Part_Code))\r\n      // }\r\n      this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n      console.timeEnd('fff')\r\n    },\r\n    addElementToTbData(element) {\r\n      const key = this.isCom ? element.Comp_Code : (element.Component_Code ?? '') + element.Part_Code\r\n      this.tbDataMap[key] = element\r\n    },\r\n    getMergeUniqueRow(element) {\r\n      const key = this.isCom ? element.Comp_Code : (element.Component_Code ?? '') + element.Part_Code\r\n      return this.tbDataMap[key]\r\n    },\r\n    checkForm() {\r\n      let isValidate = true\r\n      this.$refs['formInline'].validate((valid) => {\r\n        if (!valid) isValidate = false\r\n      })\r\n      return isValidate\r\n    },\r\n    async saveDraft(isOrder = false) {\r\n      const checkSuccess = this.checkForm()\r\n      if (!checkSuccess) return false\r\n      const { tableData, status } = this.getSubmitTbInfo()\r\n      if (!status) return false\r\n      if (!isOrder) {\r\n        this.saveLoading = true\r\n      }\r\n      const isSuccess = await this.handleSaveDraft(tableData, isOrder)\r\n      console.log('isSuccess', isSuccess)\r\n      if (!isSuccess) return false\r\n      if (isOrder) return isSuccess\r\n      this.$refs['draft']?.fetchData()\r\n      this.saveLoading = false\r\n    },\r\n    async saveWorkShop() {\r\n      const checkSuccess = this.checkForm()\r\n      if (!checkSuccess) return false\r\n      const obj = {}\r\n      if (!this.tbData.length) {\r\n        this.$message({\r\n          message: '数据不能为空',\r\n          type: 'success'\r\n        })\r\n        return\r\n      }\r\n      if (this.isCom) {\r\n        obj.Schduling_Comps = this.tbData\r\n      } else {\r\n        obj.SarePartsModel = this.tbData\r\n      }\r\n      if (this.isEdit) {\r\n        obj.Schduling_Plan = this.formInline\r\n      } else {\r\n        const {\r\n          install,\r\n          projectId,\r\n          areaId\r\n        } = this.$route.query\r\n        obj.Schduling_Plan = {\r\n          ...this.formInline,\r\n          Project_Id: projectId,\r\n          InstallUnit_Id: install,\r\n          Area_Id: areaId,\r\n          Schduling_Model: this.model // 1构件单独排产，2零件单独排产，3构/零件一起排产\r\n        }\r\n      }\r\n      this.pgLoading = true\r\n      const _fun = this.isCom ? SaveComponentSchedulingWorkshop : SavePartSchedulingWorkshop\r\n      _fun(obj).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.pgLoading = false\r\n          this.$message({\r\n            message: '保存成功',\r\n            type: 'success'\r\n          })\r\n          this.closeView()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getSubmitTbInfo() {\r\n      // 处理上传的数据\r\n      const tableData = JSON.parse(JSON.stringify(this.tbData))\r\n      for (let i = 0; i < tableData.length; i++) {\r\n        const element = tableData[i]\r\n        const list = []\r\n        if (!element.Technology_Path) {\r\n          this.$message({\r\n            message: '工序不能为空',\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        // if (!this.isCom && element.Comp_Import_Detail_Id && !element.Part_Used_Process) {\r\n        //   // 零构件 零件单独排产\r\n        //   this.$message({\r\n        //     message: '零件领用工序不能为空',\r\n        //     type: 'warning'\r\n        //   })\r\n        //   return { status: false }\r\n        // }\r\n        if (element.Scheduled_Technology_Path && element.Scheduled_Technology_Path !== element.Technology_Path) {\r\n          this.$message({\r\n            message: `请和该区域批次下已排产同${this.isCom ? '构件' : '零件'}保持工序一致`,\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        if (element.Scheduled_Used_Process && element.Scheduled_Used_Process !== element.Part_Used_Process) {\r\n          this.$message({\r\n            message: `请和该区域批次下已排产同零件领用工序保持一致`,\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        const processList = Array.from(new Set(element.Technology_Path.split('/')))\r\n        processList.forEach(code => {\r\n          const groups = this.workingTeam.filter(v => v.Process_Code === code)\r\n          const groupsList = groups.map(group => {\r\n            const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)\r\n            const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)\r\n            const obj = {\r\n              Team_Task_Id: element.Team_Task_Id,\r\n              Comp_Code: element.Comp_Code,\r\n              Again_Count: +element[uCode] || 0, // 不填，后台让传0\r\n              Part_Code: this.isCom ? null : '',\r\n              Process_Code: code,\r\n              Technology_Path: element.Technology_Path,\r\n              Working_Team_Id: group.Working_Team_Id,\r\n              Working_Team_Name: group.Working_Team_Name\r\n            }\r\n            delete element[uCode]\r\n            delete element[uMax]\r\n            return obj\r\n          })\r\n          list.push(...groupsList)\r\n        })\r\n        const hasInput = Object.keys(element).filter(_ => _.startsWith(element['uuid']))\r\n        hasInput.forEach((item) => {\r\n          delete element[item]\r\n        })\r\n        delete element['uuid']\r\n        delete element['_X_ROW_KEY']\r\n        delete element['puuid']\r\n        element.Allocation_Teams = list\r\n      }\r\n      return { tableData, status: true }\r\n    },\r\n    async handleSaveDraft(tableData, isOrder) {\r\n      console.log('保存草稿')\r\n      const _fun = this.isCom ? SaveCompSchdulingDraft : SavePartSchdulingDraft\r\n      const obj = {}\r\n      if (this.isCom) {\r\n        obj.Schduling_Comps = tableData\r\n        const p = []\r\n        for (const objKey in this.processList) {\r\n          if (this.processList.hasOwnProperty(objKey)) {\r\n            p.push(this.processList[objKey])\r\n          }\r\n        }\r\n        obj.Process_List = p\r\n      } else {\r\n        obj.SarePartsModel = tableData\r\n      }\r\n      if (this.isEdit) {\r\n        obj.Schduling_Plan = this.formInline\r\n      } else {\r\n        const {\r\n          install,\r\n          projectId,\r\n          areaId\r\n        } = this.$route.query\r\n        obj.Schduling_Plan = {\r\n          ...this.formInline,\r\n          Project_Id: projectId,\r\n          InstallUnit_Id: install,\r\n          Area_Id: areaId,\r\n          Schduling_Model: this.model // 1构件单独排产，2零件单独排产，3构/零件一起排产\r\n        }\r\n      }\r\n      let orderSuccess = false\r\n      console.log('obj', obj)\r\n\r\n      await _fun(obj).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (!isOrder) {\r\n            this.pgLoading = false\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            this.closeView()\r\n          } else {\r\n            this.templateScheduleCode = res.Data\r\n            orderSuccess = true\r\n            console.log('保存草稿成功 ')\r\n          }\r\n        } else {\r\n          this.saveLoading = false\r\n          this.pgLoading = false\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      console.log('结束 ')\r\n      return orderSuccess\r\n    },\r\n    handleDelete() {\r\n      this.deleteLoading = true\r\n      setTimeout(() => {\r\n        const selectedUuids = new Set(this.multipleSelection.map(v => v.uuid))\r\n        this.tbData = this.tbData.filter(item => {\r\n          const isSelected = selectedUuids.has(item.uuid)\r\n          if (isSelected) {\r\n            const key = this.isCom ? item.Comp_Code : (item.Component_Code ?? '') + item.Part_Code\r\n            delete this.tbDataMap[key]\r\n          }\r\n          return !isSelected\r\n        })\r\n        this.$nextTick(_ => {\r\n          this.$refs['draft']?.mergeData(this.multipleSelection)\r\n          this.multipleSelection = []\r\n        })\r\n        this.deleteLoading = false\r\n      }, 0)\r\n    },\r\n    async getWorkTeam() {\r\n      await GetSchdulingWorkingTeams({\r\n        type: this.isCom ? 1 : 2\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.workingTeam = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      this.$refs['formInline'].validate((valid) => {\r\n        if (!valid) return\r\n        const { tableData, status } = this.getSubmitTbInfo()\r\n        if (!status) return\r\n        this.$confirm('是否提交当前数据?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.saveDraftDoSubmit(tableData)\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n      })\r\n    },\r\n    async saveDraftDoSubmit() {\r\n      this.pgLoading = true\r\n      if (this.formInline?.Schduling_Code) {\r\n        const isSuccess = await this.saveDraft(true)\r\n        console.log('saveDraftDoSubmit', isSuccess)\r\n        isSuccess && this.doSubmit(this.formInline.Id)\r\n      } else {\r\n        const isSuccess = await this.saveDraft(true)\r\n        isSuccess && this.doSubmit(this.templateScheduleCode)\r\n      }\r\n    },\r\n    doSubmit(scheduleCode) {\r\n      SaveSchdulingTaskById({\r\n        schdulingPlanId: scheduleCode\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '下达成功',\r\n            type: 'success'\r\n          })\r\n          this.closeView()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.pgLoading = false\r\n      }).catch(_ => {\r\n        this.pgLoading = false\r\n      })\r\n    },\r\n    getWorkShop(value) {\r\n      const {\r\n        origin,\r\n        row,\r\n        workShop: {\r\n          Id,\r\n          Display_Name\r\n        }\r\n      } = value\r\n      if (origin === 2) {\r\n        if (value.workShop?.Id) {\r\n          row.Workshop_Name = Display_Name\r\n          row.Workshop_Id = Id\r\n          this.setPath(row, Id)\r\n        } else {\r\n          row.Workshop_Name = ''\r\n          row.Workshop_Id = ''\r\n        }\r\n      } else {\r\n        this.multipleSelection.forEach(item => {\r\n          if (value.workShop?.Id) {\r\n            item.Workshop_Name = Display_Name\r\n            item.Workshop_Id = Id\r\n            this.setPath(item, Id)\r\n          } else {\r\n            item.Workshop_Name = ''\r\n            item.Workshop_Id = ''\r\n          }\r\n        })\r\n      }\r\n    },\r\n    setPath(row, Id) {\r\n      if (row?.Scheduled_Workshop_Id) {\r\n        if (row.Scheduled_Workshop_Id !== Id) {\r\n          row.Technology_Path = ''\r\n        }\r\n      } else {\r\n        row.Technology_Path = ''\r\n      }\r\n    },\r\n    handleBatchWorkshop(origin, row) {\r\n      this.title = origin === 1 ? '批量分配车间' : '分配车间'\r\n      this.currentComponent = 'Workshop'\r\n      this.dWidth = '30%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].fetchData(origin, row)\r\n      })\r\n    },\r\n    async handleAutoDeal() {\r\n      /*      if (this.workshopEnabled) {\r\n        const hasWorkShop = this.checkHasWorkShop(1, this.multipleSelection)\r\n        if (!hasWorkShop) return\r\n      }*/\r\n\r\n      this.$confirm(`是否将选中数据按构件类型自动分配`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        if (this.workshopEnabled) {\r\n          const p = this.multipleSelection.map(item => {\r\n            return {\r\n              uniqueType: `${item.Type}$_$${item.Workshop_Id}`\r\n            }\r\n          })\r\n          const codes = Array.from(new Set(p.map(v => v.uniqueType)))\r\n          const objKey = {}\r\n          Promise.all(codes.map(v => {\r\n            const info = v.split('$_$')\r\n            return this.setLibType(info[0], info[1])\r\n          })\r\n          ).then(res => {\r\n            const hasUndefined = res.some(item => item == undefined)\r\n            if (hasUndefined) {\r\n              this.$message({\r\n                message: '所选车间内工序班组与构件类型工序不匹配，请手动分配工序',\r\n                type: 'warning'\r\n              })\r\n            }\r\n\r\n            res.forEach((element, idx) => {\r\n              objKey[codes[idx]] = element\r\n            })\r\n            this.multipleSelection.forEach((element) => {\r\n              element.Technology_Path = objKey[`${element.Type}$_$${element.Workshop_Id}`]\r\n              this.resetWorkTeamMax(element, element.Technology_Path)\r\n            })\r\n          })\r\n        } else {\r\n          const p = this.multipleSelection.map(item => item.Type)\r\n          const codes = Array.from(new Set(p))\r\n          const objKey = {}\r\n\r\n          Promise.all(codes.map(v => {\r\n            return this.setLibType(v)\r\n          })).then(res => {\r\n            res.forEach((element, idx) => {\r\n              objKey[codes[idx]] = element\r\n            })\r\n            this.multipleSelection.forEach((element) => {\r\n              element.Technology_Path = objKey[element.Type]\r\n              this.resetWorkTeamMax(element, element.Technology_Path)\r\n            })\r\n          })\r\n        }\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    getProcessOption(workshopId) {\r\n      return new Promise((resolve, reject) => {\r\n        GetProcessListBase({\r\n          workshopId: workshopId,\r\n          type: this.isCom ? 1 : 2 // 0:全部，工艺类型1：构件工艺，2：零件工艺\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            const process = res.Data.map(v => v.Code)\r\n            resolve(process)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    setLibType(code, workshopId) {\r\n      return new Promise((resolve) => {\r\n        const obj = {\r\n          Component_type: code,\r\n          type: 1\r\n        }\r\n        if (this.workshopEnabled) {\r\n          obj.workshopId = workshopId\r\n        }\r\n        GetLibListType(obj).then(res => {\r\n          if (res.IsSucceed) {\r\n            if (res.Data.Data && res.Data.Data.length) {\r\n              const info = res.Data.Data[0]\r\n              const workCode = info.WorkCode && info.WorkCode.replace(/\\\\/g, '/')\r\n              resolve(workCode)\r\n            } else {\r\n              resolve(undefined)\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    inputChange(row) {\r\n      this.setInputMax(row)\r\n    },\r\n    setInputMax(row) {\r\n      const inputValuesKeys = Object.keys(row)\r\n        .filter(v => !v.endsWith('max') && v.startsWith(row.uuid) && v.length > row.uuid.length)\r\n      inputValuesKeys.forEach((val) => {\r\n        const curCode = val.split(SPLIT_SYMBOL)[1]\r\n        const otherTotal = inputValuesKeys.filter(x => {\r\n          const code = x.split(SPLIT_SYMBOL)[1]\r\n          return x !== val && code === curCode\r\n        }).reduce((acc, item) => {\r\n          return acc + numeral(row[item]).value()\r\n        }, 0)\r\n        row[val + SPLIT_SYMBOL + 'max'] = row.Schduled_Count - otherTotal\r\n      })\r\n    },\r\n    sendProcess({ arr, str }) {\r\n      let isSuccess = true\r\n      for (let i = 0; i < arr.length; i++) {\r\n        const item = arr[i]\r\n        if (item.originalPath && item.originalPath !== str) {\r\n          isSuccess = false\r\n          break\r\n        }\r\n        item.Technology_Path = str\r\n      }\r\n      if (!isSuccess) {\r\n        this.$message({\r\n          message: '请和该区域批次下已排产同构件保持工序一致',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    resetWorkTeamMax(row, str) {\r\n      if (str) {\r\n        row.Technology_Path = str\r\n      } else {\r\n        str = row.Technology_Path\r\n      }\r\n      const list = str?.split('/') || []\r\n      this.workingTeam.forEach((element, idx) => {\r\n        const cur = list.some(k => k === element.Process_Code)\r\n        const code = this.getRowUnique(row.uuid, element.Process_Code, element.Working_Team_Id)\r\n        const max = this.getRowUniqueMax(row.uuid, element.Process_Code, element.Working_Team_Id)\r\n        if (cur) {\r\n          if (!row[code]) {\r\n            this.$set(row, code, 0)\r\n            this.$set(row, max, row.Schduled_Count)\r\n          }\r\n        } else {\r\n          this.$delete(row, code)\r\n          this.$delete(row, max)\r\n        }\r\n      })\r\n    },\r\n    checkPermissionTeam(processStr, processCode) {\r\n      if (!processStr) return false\r\n      const list = processStr?.split('/') || []\r\n      return !!list.some(v => v === processCode)\r\n    },\r\n\r\n    async getTableConfig(code) {\r\n      await GetGridByCode({\r\n        code\r\n      }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n          const list = Data.ColumnList || []\r\n          this.ownerColumn = list.find(item => item.Code === 'Part_Used_Process')\r\n          this.ownerColumn2 = list.find(item => item.Code === 'Is_Main_Part')\r\n          this.columns = this.setColumnDisplay(list)\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setColumnDisplay(list) {\r\n      return list.filter(v => v.Is_Display).map(item => {\r\n        if (FIX_COLUMN.includes(item.Code)) {\r\n          item.fixed = 'left'\r\n        }\r\n        return item\r\n      })\r\n    },\r\n    activeCellMethod({ row, column, columnIndex }) {\r\n      if (this.isView) return false\r\n      const processCode = column.field?.split('$_$')[1]\r\n      return this.checkPermissionTeam(row.Technology_Path, processCode)\r\n    },\r\n    openBPADialog(type, row) {\r\n      if (this.workshopEnabled) {\r\n        if (type === 1) {\r\n          const IsUnique = this.checkIsUniqueWorkshop()\r\n          if (!IsUnique) return\r\n        }\r\n      }\r\n      this.title = type === 2 ? '工序调整' : '批量工序调整'\r\n      this.currentComponent = 'BatchProcessAdjust'\r\n      this.dWidth = this.isCom ? '60%' : '30%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].setData(type === 2 ? [row] : this.multipleSelection, type === 2 ? row.Technology_Path : '')\r\n      })\r\n    },\r\n    checkIsUniqueWorkshop() {\r\n      let isUnique = true\r\n      const firstV = this.multipleSelection[0].Workshop_Name\r\n      for (let i = 1; i < this.multipleSelection.length; i++) {\r\n        const item = this.multipleSelection[i]\r\n        if (item.Workshop_Name !== firstV) {\r\n          isUnique = false\r\n          break\r\n        }\r\n      }\r\n      if (!isUnique) {\r\n        this.$message({\r\n          message: '批量分配工序时只有相同车间下的才可一起批量分配',\r\n          type: 'warning'\r\n        })\r\n      }\r\n      return isUnique\r\n    },\r\n    checkHasWorkShop(type, arr) {\r\n      let hasWorkShop = true\r\n      for (let i = 0; i < arr.length; i++) {\r\n        const item = arr[i]\r\n        if (!item.Workshop_Name) {\r\n          hasWorkShop = false\r\n          break\r\n        }\r\n      }\r\n      if (!hasWorkShop) {\r\n        this.$message({\r\n          message: '请先选择车间后再进行工序分配',\r\n          type: 'warning'\r\n        })\r\n      }\r\n      return hasWorkShop\r\n    },\r\n    handleAddDialog(type = 'add') {\r\n      if (this.isCom) {\r\n        this.title = '构件排产'\r\n      } else {\r\n        this.title = '添加零件'\r\n      }\r\n      this.currentComponent = 'AddDraft'\r\n      this.dWidth = '80%'\r\n      this.openAddDraft = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['draft'].setPageData()\r\n      })\r\n    },\r\n    getRowUnique(uuid, processCode, workingId) {\r\n      return `${uuid}${SPLIT_SYMBOL}${processCode}${SPLIT_SYMBOL}${workingId}`\r\n    },\r\n    getRowUniqueMax(uuid, processCode, workingId) {\r\n      return this.getRowUnique(uuid, processCode, workingId) + `${SPLIT_SYMBOL}max`\r\n    },\r\n    handleSelectMenu(v) {\r\n      if (v === 'process') {\r\n        this.openBPADialog(1)\r\n      } else if (v === 'deal') {\r\n        this.handleAutoDeal(1)\r\n      }\r\n    },\r\n    handleBatchOwner(type, row) {\r\n      this.title = '批量分配领用工序'\r\n      this.currentComponent = 'OwnerProcess'\r\n      this.dWidth = '30%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].setOption(type === 2, type === 2 ? [row] : this.multipleSelection)\r\n      })\r\n    },\r\n    handleReverse() {\r\n      const cur = []\r\n      this.tbData.forEach((element, idx) => {\r\n        element.checked = !element.checked\r\n        if (element.checked) {\r\n          cur.push(element)\r\n        }\r\n      })\r\n      this.multipleSelection = cur\r\n      if (this.multipleSelection.length === this.tbData.length) {\r\n        this.$refs['xTable'].setAllCheckboxRow(true)\r\n      }\r\n      if (this.multipleSelection.length === 0) {\r\n        this.$refs['xTable'].setAllCheckboxRow(false)\r\n      }\r\n    },\r\n    tbFilterChange() {\r\n      const xTable = this.$refs.xTable\r\n      const column = xTable.getColumnByField('Type_Name')\r\n      if (!column?.filters?.length) return\r\n      column.filters.forEach(d => {\r\n        d.checked = d.value === this.searchType\r\n      })\r\n      xTable.updateData()\r\n    },\r\n    getType() {\r\n      GetPartTypeList({ Part_Grade: 0 }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.typeOption = res.Data.map(v => {\r\n            return {\r\n              label: v.Name,\r\n              value: v.Name,\r\n              code: v.Code\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setProcessList(info) {\r\n      this.changeProcessList(info)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.pagination-container {\r\n  padding: 0;\r\n  text-align: right;\r\n}\r\n\r\n::v-deep .el-card__body {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.tb-x {\r\n  flex: 1;\r\n  height: 0;\r\n  margin-bottom: 10px;\r\n  overflow: auto;\r\n}\r\n\r\n.topTitle {\r\n  font-size: 14px;\r\n  margin: 0 0 16px;\r\n\r\n  span {\r\n    display: inline-block;\r\n    width: 2px;\r\n    height: 14px;\r\n    background: #009dff;\r\n    vertical-align: middle;\r\n    margin-right: 6px;\r\n  }\r\n}\r\n\r\n::v-deep .elDivder {\r\n  margin: 10px;\r\n}\r\n\r\n.btn-x {\r\n  margin-bottom: 10px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.el-icon-edit {\r\n  cursor: pointer;\r\n}\r\n\r\nfooter {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.cs-bottom {\r\n  position: relative;\r\n  height: 40px;\r\n  line-height: 40px;\r\n\r\n  .data-info {\r\n    position: absolute;\r\n    bottom: 0;\r\n\r\n    .info-x {\r\n      margin-right: 20px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
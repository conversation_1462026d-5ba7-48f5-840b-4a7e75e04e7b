{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\ship\\component\\AddEdit.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\ship\\component\\AddEdit.vue", "mtime": 1758677034218}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["AddEdit.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "AddEdit.vue", "sourceRoot": "src/views/PRO/basic-information/ship/component", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\" style=\"width: 100%\">\r\n      <!--      <el-form-item label=\"服务工厂\" prop=\"Factory_Id\">\r\n        <el-select v-model=\"form.Factory_Id\" class=\"w100\" multiple placeholder=\"请选择\" clearable=\"\">\r\n          <el-option\r\n            v-for=\"item in factory\"\r\n            :key=\"item.Id\"\r\n            :label=\"item.Name\"\r\n            :value=\"item.Id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>-->\r\n      <el-form-item label=\"船号\" prop=\"Shipnumber\">\r\n        <el-input v-model=\"form.Shipnumber\" clearable @change=\"getLicense\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"船长\" prop=\"Captain\">\r\n        <el-input v-model=\"form.Captain\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"电话\" prop=\"Mobile\">\r\n        <el-input v-model=\"form.Mobile\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"运输单位\" prop=\"Unit\">\r\n        <el-input v-model=\"form.Unit\" clearable />\r\n      </el-form-item>\r\n      <el-form-item style=\"text-align: right\">\r\n        <el-button @click=\"$emit('close')\">取 消</el-button>\r\n        <!-- <el-button v-if=\"showDelete\" type=\"danger\" @click=\"handleDelete\">删 除</el-button> -->\r\n        <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { DeleteCar, SaveBoat } from '@/api/PRO/car'\r\nimport { GetFactoryList } from '@/api/PRO/factory'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      factory: [],\r\n      form: {\r\n       Shipnumber: '',\r\n       Captain: '',\r\n       Mobile: '',\r\n       Unit: '',\r\n\r\n      },\r\n      rules: {\r\n  \r\n        Shipnumber: [\r\n          { required: true, message: '请输入', trigger: 'blur' }\r\n        ],\r\n        Captain:[\r\n          { required: true, message: '请输入', trigger: 'blur' }\r\n        ],\r\n        Mobile: [\r\n          { required: true, message: '请输入', trigger: 'blur' }\r\n        ],\r\n       \r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    // showDelete() {\r\n    //   return this.form.Id !== undefined\r\n    // }\r\n  },\r\n  mounted() {\r\n    this.init()\r\n    console.log(this.form.Id)\r\n  },\r\n  methods: {\r\n    init() {\r\n      GetFactoryList({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.factory = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getLicense(v) {\r\n      this.form.License = v.replace(/([a-zA-Z])/g, function(v) {\r\n        return v.toUpperCase()\r\n      })\r\n    },\r\n    editInit(row) {\r\n      console.log(row)\r\n      const { Shipnumber,Captain, Mobile, Id ,Unit} = row\r\n      // this.form.Factory_Id = Factory_Id\r\n      this.form.Shipnumber = Shipnumber\r\n      this.form.Captain = Captain\r\n      this.form.Unit = Unit\r\n      this.form.Mobile = Mobile\r\n      this.$set(this.form, 'Id', Id)\r\n    },\r\n    handleSubmit() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (!valid) return\r\n        this.btnLoading = true\r\n        SaveBoat(this.form).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '操作成功',\r\n              type: 'success'\r\n            })\r\n            this.$emit('close')\r\n            this.$emit('refresh')\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.btnLoading = false\r\n        })\r\n      })\r\n    }\r\n    // handleDelete() {\r\n    //   this.$confirm('是否删除该车辆?', '提示', {\r\n    //     confirmButtonText: '确定',\r\n    //     cancelButtonText: '取消',\r\n    //     type: 'warning'\r\n    //   }).then(() => {\r\n    //     DeleteCar({\r\n    //       id: this.form.Id\r\n    //     }).then(res => {\r\n    //       if (res.IsSucceed) {\r\n    //         this.$message({\r\n    //           type: 'success',\r\n    //           message: '删除成功!'\r\n    //         })\r\n    //         this.$emit('close')\r\n    //         this.$emit('refresh')\r\n    //       } else {\r\n    //         this.$message({\r\n    //           message: res.Message,\r\n    //           type: 'error'\r\n    //         })\r\n    //       }\r\n    //     })\r\n    //   }).catch(() => {\r\n    //     this.$message({\r\n    //       type: 'info',\r\n    //       message: '已取消删除'\r\n    //     })\r\n    //   })\r\n    // }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"]}]}
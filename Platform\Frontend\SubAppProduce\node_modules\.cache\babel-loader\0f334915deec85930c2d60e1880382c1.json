{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\addDraft.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\addDraft.vue", "mtime": 1757926768436}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetGridByCode", "GetCanSchdulingComps", "GetCanSchdulingParts", "GetPartList", "v4", "uuidv4", "debounce", "deepClone", "tablePageSize", "GetCompTypeTree", "GetPartTypeList", "TreeDetail", "ExpandableSection", "GetInstallUnitIdNameList", "GetProjectAreaTreeList", "getUnique", "mapGetters", "findAllParentNode", "GetStopList", "SPLIT_SYMBOL", "components", "props", "scheduleId", "type", "String", "default", "pageType", "showDialog", "Boolean", "installId", "currentIds", "isPartPrepare", "comName", "partName", "data", "pageInfo", "page", "pageSize", "pageSizes", "total", "form", "Comp_Code", "Comp_CodeBlur", "Part_CodeBlur", "Part_Code", "Unit_Part_CodeBlur", "Unit_Part_Code", "Type_Name", "InstallUnit_Id", "Spec", "Type", "cur<PERSON><PERSON>ch", "cur<PERSON><PERSON><PERSON><PERSON><PERSON>", "curUnitPartSearch", "showExpand", "searchContent", "searchUnitPartContent", "searchPartContent", "statusType", "projectName", "expandedKey", "statusCode", "isOwnerNull", "tbLoading", "treeLoading", "addLoading", "saveLoading", "showSc", "installUnitIdList", "columns", "fTable", "tbConfig", "TotalCount", "Page", "totalSelection", "treeData", "search", "treeSelectParams", "placeholder", "clearable", "ObjectTypeList", "clickParent", "children", "label", "value", "areaId", "typeOption", "computed", "_objectSpread", "isCom", "filterText", "watch", "newValue", "mounted", "methods", "initData", "console", "log", "tbData", "getConfig", "fetchTreeData", "getObjectTypeList", "getType", "fetchData", "setPageData", "handleNodeClick", "_data$Children", "_this", "Id", "ParentNodes", "Children", "length", "Data", "$message", "message", "setData", "_ref", "projectId", "Project_Id", "_arr", "nodeLabels", "filter", "v", "map", "p", "Label", "getInstallUnitIdNameList", "_this2", "MenuId", "$route", "meta", "then", "res", "IsSucceed", "resData", "item", "Is_Directory", "<PERSON><PERSON><PERSON>", "Message", "catch", "_this3", "deepFilter", "tree", "i", "ParentId", "customFilterFun", "node", "arr", "split", "labelVal", "statusVal", "parentNode", "parent", "labels", "status", "level", "concat", "_toConsumableArray", "resultLabel", "resultStatus", "some", "s", "indexOf", "_this4", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "code", "wrap", "_callee$", "_context", "prev", "next", "getTableConfig", "stop", "filterData", "_this5", "splitAndClean", "input", "trim", "replace", "f", "formKey", "push", "setPage", "checkMatch", "origin", "comp", "_comp", "_code$split", "_code$split2", "_slicedToArray", "key", "_origin", "_code$split3", "_code$split4", "includes", "checkExactMatch", "_code$split5", "_code$split6", "_code$split7", "_code$split8", "temTbData", "checked", "compCode", "Component_Codes", "compCodeArray", "flag", "cName", "unitPartCodeBlurArray", "Belong_To_Component", "name", "unitPartCodeArray", "partCodeBlurArray", "partCodeArray", "specArray", "spec", "csCount", "componentMap", "reduce", "acc", "_code$split9", "_code$split0", "parseInt", "$set", "Math", "min", "Can_Schduling_Count", "Can_Schduling_Weight", "Weight", "searchcount", "count", "searchcountMax", "maxCount", "handleSearch", "_this$tbData", "clearSelect", "for<PERSON>ach", "tbSelectChange", "array", "$refs", "xTable1", "clearCheckboxRow", "_this6", "_callee2", "_callee2$", "_context2", "handleReset", "getComTbData", "getPartTbData", "initTbData", "_this$tbData2", "handleSave", "_this7", "arguments", "undefined", "setTimeout", "intCount", "<PERSON><PERSON><PERSON><PERSON>_Count", "chooseCount", "cp", "$emit", "_this$tbData3", "_this8", "obj<PERSON><PERSON>", "JSON", "parse", "stringify", "uuid", "addTbKeys", "_this9", "_callee3", "_this9$form", "Comp_Codes", "obj", "codes", "_callee3$", "_context3", "_objectWithoutProperties", "_excluded", "Object", "prototype", "toString", "call", "Ids", "Schduling_Plan_Id", "Area_Id", "idx", "originalPath", "Scheduled_Technology_Path", "Workshop_Id", "Scheduled_Workshop_Id", "Workshop_Name", "Scheduled_Workshop_Name", "Technology_Path", "initRowIndex", "Area_Name", "join", "handlePageChange", "_ref2", "currentPage", "tb", "slice", "_this0", "_callee4", "submitObj", "_callee4$", "_context4", "Comp_Import_Detail_Id", "Part_Used_Process", "getPartUsedProcess", "Temp_Part_Used_Process", "setPartColumn", "Part_Aggregate_Id", "stopMap", "Is_Stop", "row", "hasOwnProperty", "checkCheckboxMethod", "_ref3", "stopFlag", "Scheduled_Used_Process", "Component_Technology_Path", "list", "Part_Type_Used_Process", "every", "findIndex", "Code", "splice", "mergeData", "handleClose", "_this1", "_callee5", "_callee5$", "_context5", "assign", "Grid", "Number", "Row_Number", "ColumnList", "Is_Display", "Is_Frozen", "fixed", "_this10", "professional", "$nextTick", "_", "treeSelectObjectType", "treeDataUpdateFun", "_this11", "Part_Grade", "addToList", "id", "_this12"], "sources": ["src/views/PRO/plan-production/schedule-production-new-part/components/addDraft.vue"], "sourcesContent": ["<template>\r\n  <div class=\"contentBox\">\r\n    <div class=\"main-info\">\r\n      <div class=\"left\">\r\n        <ExpandableSection v-model=\"showExpand\" v-loading=\"tbLoading\" class=\"fff\" :width=\"300\">\r\n          <div class=\"inner-wrapper\">\r\n            <div class=\"tree-search\">\r\n              <el-select\r\n                v-model=\"statusType\"\r\n                clearable\r\n                class=\"search-select\"\r\n                placeholder=\"请选择\"\r\n              >\r\n                <el-option label=\"可排产\" value=\"可排产\" />\r\n                <el-option label=\"排产完成\" value=\"排产完成\" />\r\n                <el-option label=\"未导入\" value=\"未导入\" />\r\n              </el-select>\r\n              <el-input\r\n                v-model.trim=\"projectName\"\r\n                placeholder=\"搜索...\"\r\n                size=\"small\"\r\n                clearable\r\n                suffix-icon=\"el-icon-search\"\r\n              />\r\n            </div>\r\n            <el-divider class=\"cs-divider\" />\r\n            <div class=\"tree-x cs-scroll\">\r\n              <tree-detail\r\n                ref=\"tree\"\r\n                icon=\"icon-folder\"\r\n                is-custom-filter\r\n                :custom-filter-fun=\"customFilterFun\"\r\n                :loading=\"treeLoading\"\r\n                :tree-data=\"treeData\"\r\n                show-status\r\n                show-detail\r\n                :filter-text=\"filterText\"\r\n                :expanded-key=\"expandedKey\"\r\n                @handleNodeClick=\"handleNodeClick\"\r\n              >\r\n                <template #csLabel=\"{showStatus,data}\">\r\n                  <span v-if=\"!data.ParentNodes\" class=\"cs-blue\">({{ data.Code }})</span>{{ data.Label }}\r\n                  <template v-if=\"showStatus\">\r\n                    <span :class=\"['cs-tag',data.Data[statusCode]=='可排产' ? 'greenBg' : data.Data[statusCode]=='排产完成' ?'orangeBg':data.Data[statusCode]=='未导入'?'redBg':'']\">\r\n                      <i\r\n                        v-if=\"data.Data[statusCode]\"\r\n                        :class=\"[data.Data[statusCode]=='可排产' ? 'fourGreen' : data.Data[statusCode]=='排产完成' ?'fourOrange':data.Data[statusCode]=='未导入'?'fourRed':'']\"\r\n                      >\r\n                        {{ data.Data[statusCode] }}\r\n                      </i>\r\n\r\n                    </span>\r\n                  </template>\r\n                </template>\r\n\r\n              </tree-detail>\r\n            </div>\r\n          </div>\r\n        </ExpandableSection>\r\n      </div>\r\n      <div class=\"right\">\r\n\r\n        <el-form ref=\"form\" :model=\"form\" label-width=\"90px\">\r\n          <el-row>\r\n            <template >\r\n              <el-col :span=\"12\">\r\n                <!--                <el-form-item label-width=\"70px\" label=\"零件名称\" prop=\"Part_Code\">\r\n                  <div class=\"cs-input-x\">\r\n                    <el-input\r\n                      v-model=\"form.Part_Code\"\r\n                      placeholder=\"请输入(空格区分/多个搜索)\"\r\n                      clearable\r\n                      class=\"w100\"\r\n                    />\r\n                    <el-input\r\n                      v-model=\"form.Part_CodeBlur\"\r\n                      clearable\r\n                      class=\"w100\"\r\n                      style=\"margin-left: 10px;\"\r\n                      placeholder=\"模糊查找(请输入关键字)\"\r\n                      type=\"text\"\r\n                    />\r\n                  </div>\r\n                </el-form-item>\r\n             -->\r\n                <el-form-item prop=\"searchContent\" :label=\"`${comName}名称`\">\r\n                  <el-input\r\n                    v-model=\"searchContent\"\r\n                    clearable\r\n                    class=\"input-with-select w100\"\r\n                    placeholder=\"请输入(空格区分/多个搜索)\"\r\n                    size=\"small\"\r\n                  >\r\n                    <el-select\r\n                      slot=\"prepend\"\r\n                      v-model=\"curSearch\"\r\n                      placeholder=\"请选择\"\r\n                      style=\"width: 100px\"\r\n                    >\r\n                      <el-option label=\"精准查询\" :value=\"1\" />\r\n                      <el-option label=\"模糊查询\" :value=\"0\" />\r\n                    </el-select>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item prop=\"searchUnitPartContent\" label=\"部件名称\">\r\n                  <el-input\r\n                    v-model=\"searchUnitPartContent\"\r\n                    clearable\r\n                    class=\"input-with-select w100\"\r\n                    placeholder=\"请输入(空格区分/多个搜索)\"\r\n                    size=\"small\"\r\n                  >\r\n                    <el-select\r\n                      slot=\"prepend\"\r\n                      v-model=\"curUnitPartSearch\"\r\n                      placeholder=\"请选择\"\r\n                      style=\"width: 100px\"\r\n                    >\r\n                      <el-option label=\"精准查询\" :value=\"1\" />\r\n                      <el-option label=\"模糊查询\" :value=\"0\" />\r\n                    </el-select>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item prop=\"searchPartContent\" :label=\"`${partName}名称`\">\r\n                  <el-input\r\n                    v-model=\"searchPartContent\"\r\n                    clearable\r\n                    class=\"input-with-select w100\"\r\n                    placeholder=\"请输入(空格区分/多个搜索)\"\r\n                    size=\"small\"\r\n                  >\r\n                    <el-select\r\n                      slot=\"prepend\"\r\n                      v-model=\"curPartSearch\"\r\n                      placeholder=\"请选择\"\r\n                      style=\"width: 100px\"\r\n                    >\r\n                      <el-option label=\"精准查询\" :value=\"1\" />\r\n                      <el-option label=\"模糊查询\" :value=\"0\" />\r\n                    </el-select>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"5\">\r\n                <el-form-item label=\"规格\" prop=\"Spec\">\r\n                  <el-input\r\n                    v-model=\"form.Spec\"\r\n                    placeholder=\"请输入\"\r\n                    clearable\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"5\">\r\n                <el-form-item :label=\"`${partName}种类`\" prop=\"Type_Name\">\r\n                  <el-select\r\n                    v-model=\"form.Type_Name\"\r\n                    placeholder=\"请选择\"\r\n                    clearable\r\n                  >\r\n                    <el-option\r\n                      v-for=\"item in typeOption\"\r\n                      :key=\"item.Code\"\r\n                      :label=\"item.Name\"\r\n                      :value=\"item.Name\"\r\n                    />\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n            </template>\r\n            <el-col :span=\"5\">\r\n              <el-form-item label=\"批次\" label-width=\"50px\" prop=\"Create_UserName\">\r\n                <el-select\r\n                  v-model=\"form.InstallUnit_Id\"\r\n                  filterable\r\n                  clearable\r\n                  multiple\r\n                  style=\"width: 100%\"\r\n                  placeholder=\"请选择\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in installUnitIdList\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Name\"\r\n                    :value=\"item.Id\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"9\">\r\n              <el-form-item label-width=\"0\">\r\n                <el-button style=\"margin-left: 10px\" @click=\"handleReset\">重置</el-button>\r\n                <el-button style=\"margin-left: 10px\" type=\"primary\" @click=\"handleSearch()\">查询</el-button>\r\n                <el-button :loading=\"addLoading\" style=\"margin-left: 10px\" type=\"primary\" @click=\"addToList()\">加入列表</el-button>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n          </el-row>\r\n        </el-form>\r\n\r\n        <div class=\"tb-wrapper\">\r\n          <vxe-table\r\n            ref=\"xTable1\"\r\n            :empty-render=\"{name: 'NotData'}\"\r\n            show-header-overflow\r\n            empty-text=\"暂无数据\"\r\n            height=\"auto\"\r\n            show-overflow\r\n            :checkbox-config=\"{checkField: 'checked', checkMethod: checkCheckboxMethod}\"\r\n            :loading=\"tbLoading\"\r\n            :row-config=\"{isCurrent: true, isHover: true }\"\r\n            class=\"cs-vxe-table\"\r\n            align=\"left\"\r\n            stripe\r\n            :data=\"fTable\"\r\n            resizable\r\n            :edit-config=\"{trigger: 'click', mode: 'cell'}\"\r\n            :tooltip-config=\"{ enterable: true }\"\r\n            @checkbox-all=\"tbSelectChange\"\r\n            @checkbox-change=\"tbSelectChange\"\r\n          >\r\n            <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n            <template v-for=\"item in columns\">\r\n              <vxe-column\r\n                v-if=\"item.Code === 'Is_Component'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <el-tag :type=\"row.Is_Component ? 'danger' : 'success'\">{{\r\n                    row.Is_Component ? \"否\" : \"是\"\r\n                  }}</el-tag>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"['Part_Code','Comp_Code'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                  <el-tag v-if=\"row.Is_Change\" style=\"margin-right: 8px;\" type=\"danger\">变</el-tag>\r\n                  {{ row[item.Code] | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"['Can_Schduling_Count'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <span v-if=\"showSc\">{{ row.csCount||'' }}</span>\r\n                  <span v-else>{{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"['Can_Schduling_Weight'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <span v-if=\"showSc\">{{ row.csCountWeight||'' }}</span>\r\n                  <span v-else>{{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                show-overflow=\"tooltip\"\r\n                sortable\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                :min-width=\"item.Width\"\r\n              />\r\n            </template>\r\n          </vxe-table>\r\n        </div>\r\n        <div class=\"data-info\">\r\n          <el-tag\r\n            size=\"medium\"\r\n            class=\"info-x\"\r\n          >已选 {{ totalSelection.length }} 条数据\r\n          </el-tag>\r\n          <vxe-pager\r\n            border\r\n            background\r\n            :loading=\"tbLoading\"\r\n            :current-page.sync=\"pageInfo.page\"\r\n            :page-size.sync=\"pageInfo.pageSize\"\r\n            :page-sizes=\"pageInfo.pageSizes\"\r\n            :total=\"pageInfo.total\"\r\n            :layouts=\"['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']\"\r\n            size=\"small\"\r\n            @page-change=\"handlePageChange\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"button\">\r\n      <el-button @click=\"handleClose\">取消</el-button>\r\n      <el-button\r\n        type=\"primary\"\r\n        :disabled=\"!totalSelection.length\"\r\n        :loading=\"saveLoading\"\r\n        @click=\"handleSave(2)\"\r\n      >保存</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { GetCanSchdulingComps } from '@/api/PRO/production-task'\r\nimport { GetCanSchdulingParts, GetPartList } from '@/api/PRO/production-part'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { debounce, deepClone } from '@/utils'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport { GetCompTypeTree } from '@/api/PRO/professionalType'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\nimport TreeDetail from '@/components/TreeDetail/index.vue'\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport { GetInstallUnitIdNameList, GetProjectAreaTreeList } from '@/api/PRO/project'\r\nimport { getUnique } from '../constant'\r\nimport { mapGetters } from 'vuex'\r\nimport { findAllParentNode } from '@/utils/tree'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nconst SPLIT_SYMBOL = '$_$'\r\n\r\nexport default {\r\n  components: { ExpandableSection, TreeDetail },\r\n  props: {\r\n    scheduleId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    pageType: {\r\n      type: String,\r\n      default: 'com'\r\n    },\r\n    showDialog: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n\r\n    installId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    currentIds: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n\r\n    isPartPrepare: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    comName: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    partName: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      pageInfo: {\r\n        page: 1,\r\n        pageSize: 500,\r\n        pageSizes: tablePageSize,\r\n        total: 0\r\n      },\r\n      form: {\r\n        Comp_Code: '',\r\n        Comp_CodeBlur: '',\r\n        Part_CodeBlur: '',\r\n        Part_Code: '',\r\n        Unit_Part_CodeBlur: '',\r\n        Unit_Part_Code: '',\r\n        Type_Name: '',\r\n        InstallUnit_Id: [],\r\n        Spec: '',\r\n        Type: ''\r\n      },\r\n      curSearch: 1,\r\n      curPartSearch: 1,\r\n      curUnitPartSearch: 1,\r\n      showExpand: true,\r\n      searchContent: '',\r\n      searchUnitPartContent: '',\r\n      searchPartContent: '',\r\n      statusType: '',\r\n      projectName: '',\r\n      expandedKey: '',\r\n      statusCode: 'Part_Schdule_Status',\r\n      isOwnerNull: true,\r\n      tbLoading: false,\r\n      treeLoading: false,\r\n      addLoading: false,\r\n      saveLoading: false,\r\n      showSc: false,\r\n      installUnitIdList: [],\r\n      columns: [],\r\n      fTable: [],\r\n      tbConfig: {},\r\n      TotalCount: 0,\r\n      Page: 0,\r\n      totalSelection: [],\r\n      treeData: [],\r\n      search: () => ({}),\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        clearable: true\r\n      },\r\n      ObjectTypeList: {\r\n        // 构件类型\r\n        'check-strictly': true,\r\n        'default-expand-all': true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      },\r\n      areaId: '',\r\n      typeOption: []\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    },\r\n    filterText() {\r\n      return this.projectName + SPLIT_SYMBOL + this.statusType\r\n    },\r\n    ...mapGetters('schedule', ['addTbKeys'])\r\n  },\r\n  watch: {\r\n    showDialog(newValue) {\r\n      newValue && (this.saveLoading = false)\r\n    }\r\n  },\r\n  mounted() {\r\n\r\n  },\r\n  methods: {\r\n    initData() {\r\n      console.log('initData')\r\n      this.tbData = []\r\n      this.getConfig()\r\n      this.fetchTreeData()\r\n      if (this.isCom) {\r\n        this.getObjectTypeList()\r\n      } else {\r\n        this.getType()\r\n      }\r\n      this.search = debounce(this.fetchData, 800, true)\r\n      this.setPageData()\r\n    },\r\n    handleNodeClick(data) {\r\n      if (this.areaId === data.Id) {\r\n        return\r\n      }\r\n      if (!data.ParentNodes || data.Children?.length > 0) {\r\n        return\r\n      }\r\n      if (data?.Data[this.statusCode] === '未导入') {\r\n        this.$message({\r\n          message: '清单未导入，请联系深化人员导入清单',\r\n          type: 'warning'\r\n        })\r\n        this.expandedKey = data.Id\r\n        return\r\n      }\r\n\r\n      const setData = ({ Data }) => {\r\n        this.areaId = Data.Id\r\n        this.projectId = Data.Project_Id\r\n        this.expandedKey = this.areaId\r\n\r\n        const _arr = findAllParentNode(this.treeData, data.Id, true)\r\n        this.nodeLabels = _arr.filter(v => !!v.ParentNodes).map(p => p.Label)\r\n\r\n        // this.formInline.Finish_Date = ''\r\n        // this.formInline.InstallUnit_Id = ''\r\n        // this.formInline.Remark = ''\r\n        this.fetchData()\r\n        // this.getAreaInfo()\r\n        this.getInstallUnitIdNameList()\r\n      }\r\n\r\n      setData(data)\r\n    },\r\n    fetchTreeData() {\r\n      this.treeLoading = true\r\n      GetProjectAreaTreeList({ MenuId: this.$route.meta.Id, projectName: this.projectName, type: this.isCom ? 1 : 2 }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          if (res.Data.length === 0) {\r\n            this.treeData = []\r\n            this.treeLoading = false\r\n            return\r\n          }\r\n          const resData = res.Data.map(item => {\r\n            item.Is_Directory = true\r\n            return item\r\n          })\r\n          this.treeData = resData\r\n          console.log('setKey')\r\n          this.setKey()\r\n          this.treeLoading = false\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.treeData = []\r\n          this.treeLoading = false\r\n        }\r\n      }).catch(() => {\r\n        this.treeLoading = false\r\n        this.treeData = []\r\n      })\r\n    },\r\n    setKey() {\r\n      const deepFilter = (tree) => {\r\n        console.log('tree', tree)\r\n        for (let i = 0; i < tree.length; i++) {\r\n          const item = tree[i]\r\n          const { Data, Children } = item\r\n          console.log(Data)\r\n          if (Data.ParentId && !Children?.length) {\r\n            this.handleNodeClick(item)\r\n            return\r\n          } else {\r\n            if (Children && Children?.length > 0) {\r\n              return deepFilter(Children)\r\n            }\r\n          }\r\n        }\r\n      }\r\n      return deepFilter(this.treeData)\r\n    },\r\n    customFilterFun(value, data, node) {\r\n      const arr = value.split(SPLIT_SYMBOL)\r\n      const labelVal = arr[0]\r\n      const statusVal = arr[1]\r\n      if (!value) return true\r\n      let parentNode = node.parent\r\n      let labels = [node.label]\r\n      let status = [data.Data[this.statusCode]]\r\n      let level = 1\r\n      while (level < node.level) {\r\n        labels = [...labels, parentNode.label]\r\n        status = [...status, data.Data[this.statusCode]]\r\n        parentNode = parentNode.parent\r\n        level++\r\n      }\r\n      labels = labels.filter(v => !!v)\r\n      status = status.filter(v => !!v)\r\n      let resultLabel = true\r\n      let resultStatus = true\r\n      if (this.statusType) {\r\n        resultStatus = status.some(s => s.indexOf(statusVal) !== -1)\r\n      }\r\n      if (this.projectName) {\r\n        resultLabel = labels.some(s => s.indexOf(labelVal) !== -1)\r\n      }\r\n      return resultLabel && resultStatus\r\n    },\r\n    async getConfig() {\r\n      let code = ''\r\n      code = this.isCom\r\n        ? 'PROComDraftEditTbConfig'\r\n        : 'PROPartDraftEditTbConfig_new'\r\n      await this.getTableConfig(code)\r\n      // this.fetchData()\r\n    },\r\n    filterData(page) {\r\n      console.log(22)\r\n      const splitAndClean = (input) => input.trim().replace(/\\s+/g, ' ').split(' ')\r\n\r\n      if (this.curSearch === 1) {\r\n        this.form.Comp_Code = this.searchContent\r\n        this.form.Comp_CodeBlur = ''\r\n      }\r\n      if (this.curSearch === 0) {\r\n        this.form.Comp_CodeBlur = this.searchContent\r\n        this.form.Comp_Code = ''\r\n      }\r\n      if (this.curPartSearch === 1) {\r\n        this.form.Part_CodeBlur = ''\r\n        this.form.Part_Code = this.searchPartContent\r\n      }\r\n      if (this.curPartSearch === 0) {\r\n        this.form.Part_Code = ''\r\n        this.form.Part_CodeBlur = this.searchPartContent\r\n      }\r\n      if (this.curUnitPartSearch === 1) {\r\n        this.form.Unit_Part_CodeBlur = ''\r\n        this.form.Unit_Part_Code = this.searchUnitPartContent\r\n      }\r\n      if (this.curUnitPartSearch === 0) {\r\n        this.form.Unit_Part_Code = ''\r\n        this.form.Unit_Part_CodeBlur = this.searchUnitPartContent\r\n      }\r\n\r\n      const f = []\r\n      for (const formKey in this.form) {\r\n        if (this.form[formKey] || this.form[formKey] === false) {\r\n          f.push(formKey)\r\n        }\r\n      }\r\n      if (!f.length) {\r\n        this.setPage()\r\n        !page && (this.pageInfo.page = 1)\r\n        this.pageInfo.total = this.tbData.length\r\n        return\r\n      }\r\n\r\n      const checkMatch = (origin, comp) => {\r\n        const _comp = comp.map(code => {\r\n          const [key, value] = code.split('&&&')\r\n          return key\r\n        })\r\n        const _origin = origin.map(code => {\r\n          const [key, value] = code.split('&&&')\r\n          return key\r\n        })\r\n        return _origin.some(item => {\r\n          return _comp.some(value => item.includes(value))\r\n        })\r\n      }\r\n      const checkExactMatch = (origin, comp) => {\r\n        const _comp = comp.map(code => {\r\n          const [key, value] = code.split('&&&')\r\n          return key\r\n        })\r\n        const _origin = origin.map(code => {\r\n          const [key, value] = code.split('&&&')\r\n          return key\r\n        })\r\n\r\n        return _origin.some(item => _comp.includes(item))\r\n      }\r\n\r\n      const temTbData = this.tbData.filter(v => {\r\n        v.checked = false\r\n        const compCode = v.Component_Codes || []\r\n\r\n        if (this.form.Comp_Code.trim()) {\r\n          const compCodeArray = splitAndClean(this.form.Comp_Code)\r\n          if (compCodeArray.length) {\r\n            const flag = checkExactMatch(compCode, compCodeArray)\r\n            console.log(887, compCode, compCodeArray, flag)\r\n            if (!flag) return false\r\n          }\r\n        }\r\n\r\n        if (this.form.Comp_CodeBlur.trim()) {\r\n          const compCodeArray = splitAndClean(this.form.Comp_CodeBlur)\r\n          if (compCodeArray.length) {\r\n            const flag = checkMatch(compCode, compCodeArray)\r\n            if (!flag) return false\r\n          }\r\n        }\r\n\r\n        const cName = (v['Component_Code'] || '').split(',')\r\n        console.log('cName', cName)\r\n        if (this.form.Unit_Part_CodeBlur.trim()) {\r\n          const unitPartCodeBlurArray = splitAndClean(this.form.Unit_Part_CodeBlur)\r\n          console.log('unitPartCodeBlurArray', unitPartCodeBlurArray)\r\n          if (v.Belong_To_Component || !unitPartCodeBlurArray.some(code =>\r\n            cName.some(name => name.includes(code))\r\n          )) {\r\n            return false\r\n          }\r\n        }\r\n\r\n        if (this.form.Unit_Part_Code.trim()) {\r\n          const unitPartCodeArray = splitAndClean(this.form.Unit_Part_Code)\r\n          if (v.Belong_To_Component || !unitPartCodeArray.some(code => cName.includes(code))) {\r\n            return false\r\n          }\r\n        }\r\n\r\n        if (this.form.Type && v.Type !== this.form.Type) {\r\n          return false\r\n        }\r\n\r\n        if (this.form.Part_CodeBlur.trim()) {\r\n          const partCodeBlurArray = splitAndClean(this.form.Part_CodeBlur)\r\n          if (!partCodeBlurArray.some(code => v['Part_Code'].includes(code))) {\r\n            return false\r\n          }\r\n        }\r\n\r\n        if (this.form.Part_Code.trim()) {\r\n          const partCodeArray = splitAndClean(this.form.Part_Code)\r\n          if (!partCodeArray.includes(v['Part_Code'])) {\r\n            return false\r\n          }\r\n        }\r\n\r\n        if (this.form.InstallUnit_Id.length && !this.form.InstallUnit_Id.includes(v.InstallUnit_Id)) {\r\n          return false\r\n        }\r\n\r\n        if (this.form.Type_Name !== '' && v.Type_Name !== this.form.Type_Name) {\r\n          return false\r\n        }\r\n\r\n        if (this.form.Spec.trim() !== '') {\r\n          const specArray = splitAndClean(this.form.Spec)\r\n          if (!specArray.some(spec => v.Spec.includes(spec))) {\r\n            return false\r\n          }\r\n        }\r\n        if (this.searchContent.trim().length) {\r\n          let csCount = 0\r\n\r\n          v.componentMap = (v.Component_Codes || []).reduce((acc, code) => {\r\n            const [key, value] = code.split('&&&')\r\n            acc[key] = parseInt(value)\r\n            if (this.curSearch === 1) {\r\n              const compCodeArray = splitAndClean(this.form.Comp_Code)\r\n              if (compCodeArray.length) {\r\n                const flag = checkExactMatch([key], compCodeArray)\r\n                if (flag) {\r\n                  csCount += parseInt(value)\r\n                }\r\n              }\r\n            } else {\r\n              const compCodeArray = splitAndClean(this.form.Comp_CodeBlur)\r\n              if (compCodeArray.length) {\r\n                const flag = checkMatch([key], compCodeArray)\r\n                console.log('pflag', key, compCodeArray, flag, value)\r\n                if (flag) {\r\n                  csCount += parseInt(value)\r\n                }\r\n              }\r\n            }\r\n            return acc\r\n          }, {})\r\n          this.$set(v, 'csCount', Math.min(csCount, v.Can_Schduling_Count))\r\n          this.$set(v, 'csCountWeight', Math.min(v.Can_Schduling_Weight, v.csCount * v.Weight))\r\n\r\n          v.searchcount = v.count\r\n          v.searchcountMax = v.maxCount\r\n          // const cs = v.Component_Codes || []\r\n          // let min = 0\r\n          // cs.forEach((element, idx) => {\r\n          //   const [key, value] = element.split('&&&')\r\n          //   min = v.componentMap[key]\r\n          // })\r\n\r\n          v.count = v.csCount\r\n        } else {\r\n          v.count = v.Can_Schduling_Count\r\n        }\r\n\r\n        // v.Can_Schduling_Count = v.csCount\r\n        // v.Can_Schduling_Weight = v.csCountWeight\r\n\r\n        return true\r\n      })\r\n\r\n      !page && (this.pageInfo.page = 1)\r\n      this.pageInfo.total = temTbData.length\r\n      this.setPage(temTbData)\r\n      if (this.searchContent.trim().length) {\r\n        this.showSc = true\r\n      }\r\n    },\r\n    handleSearch() {\r\n      this.totalSelection = []\r\n      this.clearSelect()\r\n      if (this.tbData?.length) {\r\n        this.tbData.forEach(item => item.checked = false)\r\n        this.filterData()\r\n      }\r\n      this.showSc = !!this.searchContent.trim().length\r\n    },\r\n    tbSelectChange(array) {\r\n      this.totalSelection = this.tbData.filter(v => v.checked)\r\n    },\r\n    clearSelect() {\r\n      this.$refs.xTable1.clearCheckboxRow()\r\n      this.totalSelection = []\r\n    },\r\n    async fetchData() {\r\n      this.handleReset()\r\n      this.tbLoading = true\r\n      if (this.isCom) {\r\n        await this.getComTbData()\r\n      } else {\r\n        await this.getPartTbData()\r\n      }\r\n      this.initTbData()\r\n      this.filterData()\r\n      this.tbLoading = false\r\n    },\r\n    setPageData() {\r\n      if (this.tbData?.length) {\r\n        this.pageInfo.page = 1\r\n        this.tbData = this.tbData.filter(v => v.Can_Schduling_Count > 0)\r\n        this.filterData()\r\n      }\r\n    },\r\n    handleSave(type = 2) {\r\n      if (type === 1) {\r\n        this.addLoading = true\r\n      } else {\r\n        this.saveLoading = true\r\n      }\r\n      setTimeout(() => {\r\n        this.totalSelection.forEach((item) => {\r\n          const intCount = parseInt(item.count)\r\n          if (this.searchContent.trim().length) {\r\n            item.Schduled_Count = item.Can_Schduling_Count\r\n\r\n            item.maxCount = item.Can_Schduling_Count\r\n            item.chooseCount = intCount\r\n            item.count = item.Can_Schduling_Count\r\n\r\n            item.Can_Schduling_Count = 0\r\n            item.Can_Schduling_Weight = item.Can_Schduling_Count * item.Weight\r\n          } else {\r\n            item.Schduled_Count += intCount\r\n            item.Can_Schduling_Count -= intCount\r\n            item.Can_Schduling_Weight = item.Can_Schduling_Count * item.Weight\r\n            item.maxCount = intCount\r\n            item.chooseCount = intCount\r\n            item.count = item.Can_Schduling_Count\r\n          }\r\n\r\n          item.checked = false\r\n        })\r\n        const cp = deepClone(this.totalSelection)\r\n\r\n        // this.$emit('sendSelectList', cp)\r\n        this.addLoading = false\r\n        this.clearSelect()\r\n        // this.setPage()\r\n        this.setPageData()\r\n        if (type === 2) {\r\n          this.$emit('sendSelectList', cp)\r\n          this.$emit('close')\r\n          this.fTable = []\r\n          this.tbData = []\r\n        } else {\r\n          this.$emit('addToTbList', cp)\r\n        }\r\n      }, 0)\r\n    },\r\n    initTbData() {\r\n      // 设置文本框选择的排产数量,设置自定义唯一码\r\n      const objKey = {}\r\n      if (!this.tbData?.length) {\r\n        this.tbData = []\r\n        // this.backendTb = []\r\n        return\r\n      }\r\n      console.log(998, JSON.parse(JSON.stringify(this.tbData)))\r\n      // this.backendTb = deepClone(this.tbData)\r\n      this.tbData = this.tbData.filter(item => {\r\n        this.$set(item, 'count', item.Can_Schduling_Count)\r\n        this.$set(item, 'maxCount', item.Can_Schduling_Count)\r\n        item.uuid = getUnique(this.isCom, item)\r\n        objKey[item.Type] = true\r\n        // let csCount = 0\r\n        // item.componentMap = (item.Component_Codes || []).reduce((acc, code) => {\r\n        //   const [key, value] = code.split('&&&')\r\n        //   acc[key] = parseInt(value)\r\n        //   csCount += parseInt(value)\r\n        //   return acc\r\n        // }, {})\r\n        // this.$set(item, 'csCount', csCount)\r\n        // Object.keys(item.componentMap).forEach(key => {\r\n        //   this.$set(item, key, item.componentMap[key])\r\n        // })\r\n\r\n        return !this.addTbKeys.includes(item.uuid)\r\n      })\r\n      //   .map((item) => {\r\n      //   this.$set(item, 'count', item.Can_Schduling_Count)\r\n      //   this.$set(item, 'maxCount', item.Can_Schduling_Count)\r\n      //   // item.uuid = uuidv4()\r\n      //   item.uuid = item.InstallUnit_Id + item.Part_Aggregate_Id\r\n      //   objKey[item.Type] = true\r\n      //\r\n      //   const _selectList = this.selectTbData.filter(v => v.puuid)\r\n      //   console.log('_selectList', _selectList)\r\n      //   // _selectList.forEach((element, idx) => {\r\n      //   //   if(element.puuid === item.uuid){\r\n      //   //\r\n      //   //   }\r\n      //   // })\r\n      //   return item\r\n      // })\r\n\r\n      // this.backendTb = deepClone(this.tbData)\r\n    },\r\n    async getComTbData() {\r\n      // const { install, areaId } = this.$route.query\r\n      const { Comp_Codes, ...obj } = this.form\r\n      let codes = []\r\n      if (Object.prototype.toString.call(Comp_Codes) === '[object String]') {\r\n        codes = Comp_Codes && Comp_Codes.split(' ').filter(v => !!v)\r\n      }\r\n      await GetCanSchdulingComps({\r\n        Ids: this.currentIds,\r\n        ...obj,\r\n        Schduling_Plan_Id: this.scheduleId,\r\n        Comp_Codes: codes,\r\n        InstallUnit_Id: this.installId,\r\n        Area_Id: this.areaId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.pageInfo.total = res.Data.length\r\n          this.tbData = res.Data.map((v, idx) => {\r\n            // 已排产赋值\r\n            v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n            v.Workshop_Id = v.Scheduled_Workshop_Id\r\n            v.Workshop_Name = v.Scheduled_Workshop_Name\r\n            v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n            // if (v.originalPath) {\r\n            // v.isDisabled = true\r\n            // }\r\n            v.checked = false\r\n            v.initRowIndex = idx\r\n            v.Area_Name = this.nodeLabels.join('/')\r\n\r\n            // v.technologyPathDisabled = !!v.Technology_Path\r\n            return v\r\n          })\r\n          this.setPage()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 分页\r\n     */\r\n    handlePageChange({ currentPage, pageSize }) {\r\n      if (this.tbLoading) return\r\n      this.pageInfo.page = currentPage\r\n      this.pageInfo.pageSize = pageSize\r\n      this.setPage()\r\n      this.filterData(currentPage)\r\n    },\r\n\r\n    setPage(tb = this.tbData) {\r\n      this.fTable = tb.slice((this.pageInfo.page - 1) * this.pageInfo.pageSize, this.pageInfo.page * this.pageInfo.pageSize)\r\n    },\r\n\r\n    async getPartTbData() {\r\n      // const { install, areaId } = this.$route.query\r\n      await GetCanSchdulingParts({\r\n        Ids: this.currentIds,\r\n        ...this.form,\r\n        Schduling_Plan_Id: this.scheduleId,\r\n        InstallUnit_Id: this.installId,\r\n        Area_Id: this.areaId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.pageInfo.total = res.Data.length\r\n          this.tbData = res.Data.map((v, idx) => {\r\n            v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n            v.Workshop_Id = v.Scheduled_Workshop_Id\r\n            v.Workshop_Name = v.Scheduled_Workshop_Name\r\n            if (v.Comp_Import_Detail_Id) {\r\n              v.Part_Used_Process = this.getPartUsedProcess(v)\r\n            }\r\n            v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n            // v.isDisabled = !!v.originalPath\r\n            v.checked = false\r\n            v.initRowIndex = idx\r\n            // v.partUsedProcessDisabled = this.isPartPrepare ? !!v.Part_Used_Process : false\r\n            // v.technologyPathDisabled = !!v.Technology_Path\r\n            if (!this.isPartPrepare) {\r\n              v.Temp_Part_Used_Process = v.Part_Used_Process\r\n            }\r\n            return v\r\n          })\r\n          this.setPartColumn()\r\n          this.setPage()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n\r\n      const submitObj = this.tbData.map(item => {\r\n        return {\r\n          Id: item.Part_Aggregate_Id,\r\n          Type: 1\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = !!item.Is_Stop\r\n          })\r\n          this.tbData.forEach(row => {\r\n            if (stopMap.hasOwnProperty(row.Part_Aggregate_Id)) {\r\n              this.$set(row, 'stopFlag', stopMap[row.Part_Aggregate_Id])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    checkCheckboxMethod({ row }) {\r\n      return !row.stopFlag\r\n    },\r\n    getPartUsedProcess(item) {\r\n      if (item.Scheduled_Used_Process) {\r\n        return item.Scheduled_Used_Process\r\n      }\r\n      if (item.Component_Technology_Path) {\r\n        const list = item.Component_Technology_Path.split('/')\r\n        if (list.includes(item.Part_Used_Process)) {\r\n          return item.Part_Used_Process\r\n        } else if (list.includes(item.Part_Type_Used_Process)) {\r\n          return item.Part_Type_Used_Process\r\n        }\r\n      } else {\r\n        if (item.Part_Used_Process) {\r\n          return item.Part_Used_Process\r\n        } else if (item.Part_Type_Used_Process) {\r\n          return item.Part_Type_Used_Process\r\n        }\r\n      }\r\n\r\n      return ''\r\n    },\r\n    setPartColumn() {\r\n      // 纯零件\r\n      this.isOwnerNull = this.tbData.every(v => !v.Comp_Import_Detail_Id)\r\n      console.log('this.isOwnerNull', this.isOwnerNull)\r\n      if (this.isOwnerNull) {\r\n        const idx = this.columns.findIndex(v => v.Code === 'Component_Code')\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n      }\r\n    },\r\n    mergeData(list) {\r\n      /*      console.log('list', list)\r\n      console.log('this.backendTb', this.backendTb)\r\n      list\r\n        .forEach((element, index) => {\r\n          const idx = this.backendTb.findIndex(\r\n            (item) => element.puuid && item.uuid === element.puuid\r\n          )\r\n          console.log('idx', idx, this.backendTb[idx])\r\n          console.log('index', index)\r\n          if (idx !== -1) {\r\n            this.tbData.splice(idx, 0, deepClone(this.backendTb[idx]))\r\n          }\r\n        })\r\n\r\n      this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n      console.log('this.tbData', JSON.parse(JSON.stringify(this.tbData)))\r\n\r\n      this.filterData()*/\r\n    },\r\n    handleClose() {\r\n      this.$emit('close')\r\n    },\r\n    // activeCellMethod({ row, column, columnIndex }) {\r\n    //   return column.field === 'Schduling_Count'\r\n    // },\r\n    async getTableConfig(code) {\r\n      await GetGridByCode({\r\n        code\r\n      }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n          this.pageInfo.pageSize = Number(this.tbConfig.Row_Number)\r\n          const list = Data.ColumnList || []\r\n          this.columns = list.filter(v => v.Is_Display)\r\n            .map(item => {\r\n              if (item.Is_Frozen) {\r\n                item.fixed = 'left'\r\n              }\r\n              return item\r\n            })\r\n          // this.columns.push({\r\n          //   Display_Name: '排产数量',\r\n          //   Code: 'Schduling_Count'\r\n          // })\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getObjectTypeList() {\r\n      GetCompTypeTree({ professional: 'Steel' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          // this.ObjectTypeList.data = res.Data\r\n          this.$nextTick((_) => {\r\n            this.$refs.treeSelectObjectType.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.form.Type_Name = ''\r\n      this.form.Comp_Code = ''\r\n      this.form.Comp_CodeBlur = ''\r\n      this.form.Type = ''\r\n      this.form.Spec = ''\r\n      this.form.InstallUnit_Id = []\r\n      this.form.Part_CodeBlur = ''\r\n      this.form.Part_Code = ''\r\n      this.form.Unit_Part_CodeBlur = ''\r\n      this.form.Unit_Part_Code = ''\r\n      this.searchContent = ''\r\n      this.searchPartContent = ''\r\n      this.searchUnitPartContent = ''\r\n      this.handleSearch()\r\n    },\r\n    getType() {\r\n      GetPartTypeList({ Part_Grade: 0 }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.typeOption = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    addToList() {\r\n      if (!this.totalSelection.length) return\r\n      this.handleSave(1)\r\n    },\r\n    getInstallUnitIdNameList(id) {\r\n      if (!this.areaId) {\r\n        this.installUnitIdList = []\r\n      } else {\r\n        GetInstallUnitIdNameList({ Area_Id: this.areaId }).then(res => {\r\n          this.installUnitIdList = res.Data || []\r\n          // if (this.installUnitIdList.length) {\r\n          //   this.form.InstallUnit_Id = [this.installUnitIdList[0].Id]\r\n          // }\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n@import \"~@/styles/mixin.scss\";\r\n.cs-divider{\r\n  margin:16px 0 0 0;\r\n}\r\n.contentBox {\r\n  height: 75vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .main-info{\r\n    display: flex;\r\n    overflow: hidden;\r\n    flex: 1;\r\n    .left{\r\n      height: 100%;\r\n      margin-right: 16px;\r\n      border: 1px solid #eee;\r\n      .cs-tag{\r\n        margin-left: 8px;\r\n        font-size: 12px;\r\n        padding:2px 4px;\r\n        border-radius: 1px;\r\n      }\r\n\r\n      .inner-wrapper {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n        padding: 16px;\r\n        border-radius: 4px;\r\n        overflow: hidden;\r\n\r\n        .tree-search {\r\n          display: flex;\r\n\r\n          .search-select {\r\n            margin-right: 8px;\r\n          }\r\n        }\r\n\r\n        .tree-x {\r\n          overflow: hidden;\r\n          margin-top: 16px;\r\n          flex: 1;\r\n\r\n          .el-tree {\r\n            height: 100%;\r\n          }\r\n        }\r\n\r\n        .cs-scroll {\r\n          overflow-y: auto;\r\n          @include scrollBar;\r\n        }\r\n\r\n      }\r\n    }\r\n    .right{\r\n      overflow: hidden;\r\n      flex: 1;\r\n      display: flex;\r\n      flex-direction: column;\r\n      border: 1px solid #eee;\r\n      padding:16px;\r\n    }\r\n\r\n  }\r\n\r\n  .button {\r\n    margin-top: 16px;\r\n    display: flex;\r\n    justify-content: end;\r\n  }\r\n\r\n  .tb-wrapper {\r\n    flex: 1 1 auto;\r\n  }\r\n\r\n  .data-info{\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-top: 16px;\r\n  }\r\n}\r\n.fourGreen {\r\n  color: #00C361;\r\n  font-style: normal;\r\n}\r\n\r\n.fourOrange {\r\n  color: #FF9400;\r\n  font-style: normal;\r\n}\r\n\r\n.fourRed {\r\n  color: #FF0000;\r\n  font-style: normal;\r\n}\r\n\r\n.cs-blue {\r\n  color: #5AC8FA;\r\n}\r\n\r\n.orangeBg{\r\n  background: rgba(255,148,0,0.1);\r\n}\r\n\r\n.redBg{\r\n  background: rgba(252,107,127,0.1);\r\n}\r\n.greenBg{\r\n  background: rgba(0, 195, 97, 0.10);\r\n}\r\n.cs-input-x{\r\n  display: flex;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4UA,SAAAA,aAAA;AACA,SAAAC,oBAAA;AACA,SAAAC,oBAAA,EAAAC,WAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AACA,SAAAC,QAAA,EAAAC,SAAA;AACA,SAAAC,aAAA;AACA,SAAAC,eAAA;AACA,SAAAC,eAAA;AACA,OAAAC,UAAA;AACA,OAAAC,iBAAA;AACA,SAAAC,wBAAA,EAAAC,sBAAA;AACA,SAAAC,SAAA;AACA,SAAAC,UAAA;AACA,SAAAC,iBAAA;AACA,SAAAC,WAAA;AACA,IAAAC,YAAA;AAEA;EACAC,UAAA;IAAAR,iBAAA,EAAAA,iBAAA;IAAAD,UAAA,EAAAA;EAAA;EACAU,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,QAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAE,UAAA;MACAJ,IAAA,EAAAK,OAAA;MACAH,OAAA;IACA;IAEAI,SAAA;MACAN,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAK,UAAA;MACAP,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IAEAM,aAAA;MACAR,IAAA,EAAAK,OAAA;MACAH,OAAA;IACA;IACAO,OAAA;MACAT,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAQ,QAAA;MACAV,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAS,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;QACAC,IAAA;QACAC,QAAA;QACAC,SAAA,EAAA9B,aAAA;QACA+B,KAAA;MACA;MACAC,IAAA;QACAC,SAAA;QACAC,aAAA;QACAC,aAAA;QACAC,SAAA;QACAC,kBAAA;QACAC,cAAA;QACAC,SAAA;QACAC,cAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACAC,SAAA;MACAC,aAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,aAAA;MACAC,qBAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,WAAA;MACAC,WAAA;MACAC,UAAA;MACAC,WAAA;MACAC,SAAA;MACAC,WAAA;MACAC,UAAA;MACAC,WAAA;MACAC,MAAA;MACAC,iBAAA;MACAC,OAAA;MACAC,MAAA;MACAC,QAAA;MACAC,UAAA;MACAC,IAAA;MACAC,cAAA;MACAC,QAAA;MACAC,MAAA,WAAAA,OAAA;QAAA;MAAA;MACAC,gBAAA;QACAC,WAAA;QACAC,SAAA;MACA;MACAC,cAAA;QACA;QACA;QACA;QACAC,WAAA;QACA/C,IAAA;QACAb,KAAA;UACA6D,QAAA;UACAC,KAAA;UACAC,KAAA;QACA;MACA;MACAC,MAAA;MACAC,UAAA;IACA;EACA;EACAC,QAAA,EAAAC,aAAA;IACAC,KAAA,WAAAA,MAAA;MACA,YAAA/D,QAAA;IACA;IACAgE,UAAA,WAAAA,WAAA;MACA,YAAA/B,WAAA,GAAAxC,YAAA,QAAAuC,UAAA;IACA;EAAA,GACA1C,UAAA,4BACA;EACA2E,KAAA;IACAhE,UAAA,WAAAA,WAAAiE,QAAA;MACAA,QAAA,UAAA1B,WAAA;IACA;EACA;EACA2B,OAAA,WAAAA,QAAA,GAEA;EACAC,OAAA;IACAC,QAAA,WAAAA,SAAA;MACAC,OAAA,CAAAC,GAAA;MACA,KAAAC,MAAA;MACA,KAAAC,SAAA;MACA,KAAAC,aAAA;MACA,SAAAX,KAAA;QACA,KAAAY,iBAAA;MACA;QACA,KAAAC,OAAA;MACA;MACA,KAAA1B,MAAA,GAAAtE,QAAA,MAAAiG,SAAA;MACA,KAAAC,WAAA;IACA;IACAC,eAAA,WAAAA,gBAAAvE,IAAA;MAAA,IAAAwE,cAAA;QAAAC,KAAA;MACA,SAAAtB,MAAA,KAAAnD,IAAA,CAAA0E,EAAA;QACA;MACA;MACA,KAAA1E,IAAA,CAAA2E,WAAA,MAAAH,cAAA,GAAAxE,IAAA,CAAA4E,QAAA,cAAAJ,cAAA,uBAAAA,cAAA,CAAAK,MAAA;QACA;MACA;MACA,KAAA7E,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAA8E,IAAA,MAAAnD,UAAA;QACA,KAAAoD,QAAA;UACAC,OAAA;UACA3F,IAAA;QACA;QACA,KAAAqC,WAAA,GAAA1B,IAAA,CAAA0E,EAAA;QACA;MACA;MAEA,IAAAO,OAAA,YAAAA,QAAAC,IAAA;QAAA,IAAAJ,IAAA,GAAAI,IAAA,CAAAJ,IAAA;QACAL,KAAA,CAAAtB,MAAA,GAAA2B,IAAA,CAAAJ,EAAA;QACAD,KAAA,CAAAU,SAAA,GAAAL,IAAA,CAAAM,UAAA;QACAX,KAAA,CAAA/C,WAAA,GAAA+C,KAAA,CAAAtB,MAAA;QAEA,IAAAkC,IAAA,GAAAtG,iBAAA,CAAA0F,KAAA,CAAAhC,QAAA,EAAAzC,IAAA,CAAA0E,EAAA;QACAD,KAAA,CAAAa,UAAA,GAAAD,IAAA,CAAAE,MAAA,WAAAC,CAAA;UAAA,SAAAA,CAAA,CAAAb,WAAA;QAAA,GAAAc,GAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,KAAA;QAAA;;QAEA;QACA;QACA;QACAlB,KAAA,CAAAJ,SAAA;QACA;QACAI,KAAA,CAAAmB,wBAAA;MACA;MAEAX,OAAA,CAAAjF,IAAA;IACA;IACAkE,aAAA,WAAAA,cAAA;MAAA,IAAA2B,MAAA;MACA,KAAA/D,WAAA;MACAlD,sBAAA;QAAAkH,MAAA,OAAAC,MAAA,CAAAC,IAAA,CAAAtB,EAAA;QAAAjD,WAAA,OAAAA,WAAA;QAAApC,IAAA,OAAAkE,KAAA;MAAA,GAAA0C,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA,IAAAD,GAAA,CAAApB,IAAA,CAAAD,MAAA;YACAgB,MAAA,CAAApD,QAAA;YACAoD,MAAA,CAAA/D,WAAA;YACA;UACA;UACA,IAAAsE,OAAA,GAAAF,GAAA,CAAApB,IAAA,CAAAW,GAAA,WAAAY,IAAA;YACAA,IAAA,CAAAC,YAAA;YACA,OAAAD,IAAA;UACA;UACAR,MAAA,CAAApD,QAAA,GAAA2D,OAAA;UACAtC,OAAA,CAAAC,GAAA;UACA8B,MAAA,CAAAU,MAAA;UACAV,MAAA,CAAA/D,WAAA;QACA;UACA+D,MAAA,CAAAd,QAAA;YACAC,OAAA,EAAAkB,GAAA,CAAAM,OAAA;YACAnH,IAAA;UACA;UACAwG,MAAA,CAAApD,QAAA;UACAoD,MAAA,CAAA/D,WAAA;QACA;MACA,GAAA2E,KAAA;QACAZ,MAAA,CAAA/D,WAAA;QACA+D,MAAA,CAAApD,QAAA;MACA;IACA;IACA8D,MAAA,WAAAA,OAAA;MAAA,IAAAG,MAAA;MACA,IAAAC,WAAA,YAAAA,WAAAC,IAAA;QACA9C,OAAA,CAAAC,GAAA,SAAA6C,IAAA;QACA,SAAAC,CAAA,MAAAA,CAAA,GAAAD,IAAA,CAAA/B,MAAA,EAAAgC,CAAA;UACA,IAAAR,IAAA,GAAAO,IAAA,CAAAC,CAAA;UACA,IAAA/B,IAAA,GAAAuB,IAAA,CAAAvB,IAAA;YAAAF,QAAA,GAAAyB,IAAA,CAAAzB,QAAA;UACAd,OAAA,CAAAC,GAAA,CAAAe,IAAA;UACA,IAAAA,IAAA,CAAAgC,QAAA,MAAAlC,QAAA,aAAAA,QAAA,eAAAA,QAAA,CAAAC,MAAA;YACA6B,MAAA,CAAAnC,eAAA,CAAA8B,IAAA;YACA;UACA;YACA,IAAAzB,QAAA,KAAAA,QAAA,aAAAA,QAAA,uBAAAA,QAAA,CAAAC,MAAA;cACA,OAAA8B,WAAA,CAAA/B,QAAA;YACA;UACA;QACA;MACA;MACA,OAAA+B,WAAA,MAAAlE,QAAA;IACA;IACAsE,eAAA,WAAAA,gBAAA7D,KAAA,EAAAlD,IAAA,EAAAgH,IAAA;MACA,IAAAC,GAAA,GAAA/D,KAAA,CAAAgE,KAAA,CAAAjI,YAAA;MACA,IAAAkI,QAAA,GAAAF,GAAA;MACA,IAAAG,SAAA,GAAAH,GAAA;MACA,KAAA/D,KAAA;MACA,IAAAmE,UAAA,GAAAL,IAAA,CAAAM,MAAA;MACA,IAAAC,MAAA,IAAAP,IAAA,CAAA/D,KAAA;MACA,IAAAuE,MAAA,IAAAxH,IAAA,CAAA8E,IAAA,MAAAnD,UAAA;MACA,IAAA8F,KAAA;MACA,OAAAA,KAAA,GAAAT,IAAA,CAAAS,KAAA;QACAF,MAAA,MAAAG,MAAA,CAAAC,kBAAA,CAAAJ,MAAA,IAAAF,UAAA,CAAApE,KAAA;QACAuE,MAAA,MAAAE,MAAA,CAAAC,kBAAA,CAAAH,MAAA,IAAAxH,IAAA,CAAA8E,IAAA,MAAAnD,UAAA;QACA0F,UAAA,GAAAA,UAAA,CAAAC,MAAA;QACAG,KAAA;MACA;MACAF,MAAA,GAAAA,MAAA,CAAAhC,MAAA,WAAAC,CAAA;QAAA,SAAAA,CAAA;MAAA;MACAgC,MAAA,GAAAA,MAAA,CAAAjC,MAAA,WAAAC,CAAA;QAAA,SAAAA,CAAA;MAAA;MACA,IAAAoC,WAAA;MACA,IAAAC,YAAA;MACA,SAAArG,UAAA;QACAqG,YAAA,GAAAL,MAAA,CAAAM,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,OAAA,CAAAZ,SAAA;QAAA;MACA;MACA,SAAA3F,WAAA;QACAmG,WAAA,GAAAL,MAAA,CAAAO,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,OAAA,CAAAb,QAAA;QAAA;MACA;MACA,OAAAS,WAAA,IAAAC,YAAA;IACA;IACA5D,SAAA,WAAAA,UAAA;MAAA,IAAAgE,MAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,IAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAL,IAAA;cACAA,IAAA,GAAAL,MAAA,CAAA1E,KAAA,GACA,4BACA;cAAAkF,QAAA,CAAAE,IAAA;cAAA,OACAV,MAAA,CAAAW,cAAA,CAAAN,IAAA;YAAA;YAAA;cAAA,OAAAG,QAAA,CAAAI,IAAA;UAAA;QAAA,GAAAR,OAAA;MAAA;IAEA;IACAS,UAAA,WAAAA,WAAA5I,IAAA;MAAA,IAAA6I,MAAA;MACAjF,OAAA,CAAAC,GAAA;MACA,IAAAiF,aAAA,YAAAA,cAAAC,KAAA;QAAA,OAAAA,KAAA,CAAAC,IAAA,GAAAC,OAAA,cAAAjC,KAAA;MAAA;MAEA,SAAAjG,SAAA;QACA,KAAAX,IAAA,CAAAC,SAAA,QAAAc,aAAA;QACA,KAAAf,IAAA,CAAAE,aAAA;MACA;MACA,SAAAS,SAAA;QACA,KAAAX,IAAA,CAAAE,aAAA,QAAAa,aAAA;QACA,KAAAf,IAAA,CAAAC,SAAA;MACA;MACA,SAAAW,aAAA;QACA,KAAAZ,IAAA,CAAAG,aAAA;QACA,KAAAH,IAAA,CAAAI,SAAA,QAAAa,iBAAA;MACA;MACA,SAAAL,aAAA;QACA,KAAAZ,IAAA,CAAAI,SAAA;QACA,KAAAJ,IAAA,CAAAG,aAAA,QAAAc,iBAAA;MACA;MACA,SAAAJ,iBAAA;QACA,KAAAb,IAAA,CAAAK,kBAAA;QACA,KAAAL,IAAA,CAAAM,cAAA,QAAAU,qBAAA;MACA;MACA,SAAAH,iBAAA;QACA,KAAAb,IAAA,CAAAM,cAAA;QACA,KAAAN,IAAA,CAAAK,kBAAA,QAAAW,qBAAA;MACA;MAEA,IAAA8H,CAAA;MACA,SAAAC,OAAA,SAAA/I,IAAA;QACA,SAAAA,IAAA,CAAA+I,OAAA,UAAA/I,IAAA,CAAA+I,OAAA;UACAD,CAAA,CAAAE,IAAA,CAAAD,OAAA;QACA;MACA;MACA,KAAAD,CAAA,CAAAvE,MAAA;QACA,KAAA0E,OAAA;QACA,CAAArJ,IAAA,UAAAD,QAAA,CAAAC,IAAA;QACA,KAAAD,QAAA,CAAAI,KAAA,QAAA2D,MAAA,CAAAa,MAAA;QACA;MACA;MAEA,IAAA2E,UAAA,YAAAA,WAAAC,MAAA,EAAAC,IAAA;QACA,IAAAC,KAAA,GAAAD,IAAA,CAAAjE,GAAA,WAAA6C,IAAA;UACA,IAAAsB,WAAA,GAAAtB,IAAA,CAAApB,KAAA;YAAA2C,YAAA,GAAAC,cAAA,CAAAF,WAAA;YAAAG,GAAA,GAAAF,YAAA;YAAA3G,KAAA,GAAA2G,YAAA;UACA,OAAAE,GAAA;QACA;QACA,IAAAC,OAAA,GAAAP,MAAA,CAAAhE,GAAA,WAAA6C,IAAA;UACA,IAAA2B,YAAA,GAAA3B,IAAA,CAAApB,KAAA;YAAAgD,YAAA,GAAAJ,cAAA,CAAAG,YAAA;YAAAF,GAAA,GAAAG,YAAA;YAAAhH,KAAA,GAAAgH,YAAA;UACA,OAAAH,GAAA;QACA;QACA,OAAAC,OAAA,CAAAlC,IAAA,WAAAzB,IAAA;UACA,OAAAsD,KAAA,CAAA7B,IAAA,WAAA5E,KAAA;YAAA,OAAAmD,IAAA,CAAA8D,QAAA,CAAAjH,KAAA;UAAA;QACA;MACA;MACA,IAAAkH,eAAA,YAAAA,gBAAAX,MAAA,EAAAC,IAAA;QACA,IAAAC,KAAA,GAAAD,IAAA,CAAAjE,GAAA,WAAA6C,IAAA;UACA,IAAA+B,YAAA,GAAA/B,IAAA,CAAApB,KAAA;YAAAoD,YAAA,GAAAR,cAAA,CAAAO,YAAA;YAAAN,GAAA,GAAAO,YAAA;YAAApH,KAAA,GAAAoH,YAAA;UACA,OAAAP,GAAA;QACA;QACA,IAAAC,OAAA,GAAAP,MAAA,CAAAhE,GAAA,WAAA6C,IAAA;UACA,IAAAiC,YAAA,GAAAjC,IAAA,CAAApB,KAAA;YAAAsD,YAAA,GAAAV,cAAA,CAAAS,YAAA;YAAAR,GAAA,GAAAS,YAAA;YAAAtH,KAAA,GAAAsH,YAAA;UACA,OAAAT,GAAA;QACA;QAEA,OAAAC,OAAA,CAAAlC,IAAA,WAAAzB,IAAA;UAAA,OAAAsD,KAAA,CAAAQ,QAAA,CAAA9D,IAAA;QAAA;MACA;MAEA,IAAAoE,SAAA,QAAAzG,MAAA,CAAAuB,MAAA,WAAAC,CAAA;QACAA,CAAA,CAAAkF,OAAA;QACA,IAAAC,QAAA,GAAAnF,CAAA,CAAAoF,eAAA;QAEA,IAAA7B,MAAA,CAAAzI,IAAA,CAAAC,SAAA,CAAA2I,IAAA;UACA,IAAA2B,aAAA,GAAA7B,aAAA,CAAAD,MAAA,CAAAzI,IAAA,CAAAC,SAAA;UACA,IAAAsK,aAAA,CAAAhG,MAAA;YACA,IAAAiG,IAAA,GAAAV,eAAA,CAAAO,QAAA,EAAAE,aAAA;YACA/G,OAAA,CAAAC,GAAA,MAAA4G,QAAA,EAAAE,aAAA,EAAAC,IAAA;YACA,KAAAA,IAAA;UACA;QACA;QAEA,IAAA/B,MAAA,CAAAzI,IAAA,CAAAE,aAAA,CAAA0I,IAAA;UACA,IAAA2B,cAAA,GAAA7B,aAAA,CAAAD,MAAA,CAAAzI,IAAA,CAAAE,aAAA;UACA,IAAAqK,cAAA,CAAAhG,MAAA;YACA,IAAAiG,KAAA,GAAAtB,UAAA,CAAAmB,QAAA,EAAAE,cAAA;YACA,KAAAC,KAAA;UACA;QACA;QAEA,IAAAC,KAAA,IAAAvF,CAAA,0BAAA0B,KAAA;QACApD,OAAA,CAAAC,GAAA,UAAAgH,KAAA;QACA,IAAAhC,MAAA,CAAAzI,IAAA,CAAAK,kBAAA,CAAAuI,IAAA;UACA,IAAA8B,qBAAA,GAAAhC,aAAA,CAAAD,MAAA,CAAAzI,IAAA,CAAAK,kBAAA;UACAmD,OAAA,CAAAC,GAAA,0BAAAiH,qBAAA;UACA,IAAAxF,CAAA,CAAAyF,mBAAA,KAAAD,qBAAA,CAAAlD,IAAA,WAAAQ,IAAA;YAAA,OACAyC,KAAA,CAAAjD,IAAA,WAAAoD,IAAA;cAAA,OAAAA,IAAA,CAAAf,QAAA,CAAA7B,IAAA;YAAA;UAAA,CACA;YACA;UACA;QACA;QAEA,IAAAS,MAAA,CAAAzI,IAAA,CAAAM,cAAA,CAAAsI,IAAA;UACA,IAAAiC,iBAAA,GAAAnC,aAAA,CAAAD,MAAA,CAAAzI,IAAA,CAAAM,cAAA;UACA,IAAA4E,CAAA,CAAAyF,mBAAA,KAAAE,iBAAA,CAAArD,IAAA,WAAAQ,IAAA;YAAA,OAAAyC,KAAA,CAAAZ,QAAA,CAAA7B,IAAA;UAAA;YACA;UACA;QACA;QAEA,IAAAS,MAAA,CAAAzI,IAAA,CAAAU,IAAA,IAAAwE,CAAA,CAAAxE,IAAA,KAAA+H,MAAA,CAAAzI,IAAA,CAAAU,IAAA;UACA;QACA;QAEA,IAAA+H,MAAA,CAAAzI,IAAA,CAAAG,aAAA,CAAAyI,IAAA;UACA,IAAAkC,iBAAA,GAAApC,aAAA,CAAAD,MAAA,CAAAzI,IAAA,CAAAG,aAAA;UACA,KAAA2K,iBAAA,CAAAtD,IAAA,WAAAQ,IAAA;YAAA,OAAA9C,CAAA,cAAA2E,QAAA,CAAA7B,IAAA;UAAA;YACA;UACA;QACA;QAEA,IAAAS,MAAA,CAAAzI,IAAA,CAAAI,SAAA,CAAAwI,IAAA;UACA,IAAAmC,aAAA,GAAArC,aAAA,CAAAD,MAAA,CAAAzI,IAAA,CAAAI,SAAA;UACA,KAAA2K,aAAA,CAAAlB,QAAA,CAAA3E,CAAA;YACA;UACA;QACA;QAEA,IAAAuD,MAAA,CAAAzI,IAAA,CAAAQ,cAAA,CAAA+D,MAAA,KAAAkE,MAAA,CAAAzI,IAAA,CAAAQ,cAAA,CAAAqJ,QAAA,CAAA3E,CAAA,CAAA1E,cAAA;UACA;QACA;QAEA,IAAAiI,MAAA,CAAAzI,IAAA,CAAAO,SAAA,WAAA2E,CAAA,CAAA3E,SAAA,KAAAkI,MAAA,CAAAzI,IAAA,CAAAO,SAAA;UACA;QACA;QAEA,IAAAkI,MAAA,CAAAzI,IAAA,CAAAS,IAAA,CAAAmI,IAAA;UACA,IAAAoC,SAAA,GAAAtC,aAAA,CAAAD,MAAA,CAAAzI,IAAA,CAAAS,IAAA;UACA,KAAAuK,SAAA,CAAAxD,IAAA,WAAAyD,IAAA;YAAA,OAAA/F,CAAA,CAAAzE,IAAA,CAAAoJ,QAAA,CAAAoB,IAAA;UAAA;YACA;UACA;QACA;QACA,IAAAxC,MAAA,CAAA1H,aAAA,CAAA6H,IAAA,GAAArE,MAAA;UACA,IAAA2G,OAAA;UAEAhG,CAAA,CAAAiG,YAAA,IAAAjG,CAAA,CAAAoF,eAAA,QAAAc,MAAA,WAAAC,GAAA,EAAArD,IAAA;YACA,IAAAsD,YAAA,GAAAtD,IAAA,CAAApB,KAAA;cAAA2E,YAAA,GAAA/B,cAAA,CAAA8B,YAAA;cAAA7B,GAAA,GAAA8B,YAAA;cAAA3I,KAAA,GAAA2I,YAAA;YACAF,GAAA,CAAA5B,GAAA,IAAA+B,QAAA,CAAA5I,KAAA;YACA,IAAA6F,MAAA,CAAA9H,SAAA;cACA,IAAA4J,eAAA,GAAA7B,aAAA,CAAAD,MAAA,CAAAzI,IAAA,CAAAC,SAAA;cACA,IAAAsK,eAAA,CAAAhG,MAAA;gBACA,IAAAiG,MAAA,GAAAV,eAAA,EAAAL,GAAA,GAAAc,eAAA;gBACA,IAAAC,MAAA;kBACAU,OAAA,IAAAM,QAAA,CAAA5I,KAAA;gBACA;cACA;YACA;cACA,IAAA2H,eAAA,GAAA7B,aAAA,CAAAD,MAAA,CAAAzI,IAAA,CAAAE,aAAA;cACA,IAAAqK,eAAA,CAAAhG,MAAA;gBACA,IAAAiG,MAAA,GAAAtB,UAAA,EAAAO,GAAA,GAAAc,eAAA;gBACA/G,OAAA,CAAAC,GAAA,UAAAgG,GAAA,EAAAc,eAAA,EAAAC,MAAA,EAAA5H,KAAA;gBACA,IAAA4H,MAAA;kBACAU,OAAA,IAAAM,QAAA,CAAA5I,KAAA;gBACA;cACA;YACA;YACA,OAAAyI,GAAA;UACA;UACA5C,MAAA,CAAAgD,IAAA,CAAAvG,CAAA,aAAAwG,IAAA,CAAAC,GAAA,CAAAT,OAAA,EAAAhG,CAAA,CAAA0G,mBAAA;UACAnD,MAAA,CAAAgD,IAAA,CAAAvG,CAAA,mBAAAwG,IAAA,CAAAC,GAAA,CAAAzG,CAAA,CAAA2G,oBAAA,EAAA3G,CAAA,CAAAgG,OAAA,GAAAhG,CAAA,CAAA4G,MAAA;UAEA5G,CAAA,CAAA6G,WAAA,GAAA7G,CAAA,CAAA8G,KAAA;UACA9G,CAAA,CAAA+G,cAAA,GAAA/G,CAAA,CAAAgH,QAAA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEAhH,CAAA,CAAA8G,KAAA,GAAA9G,CAAA,CAAAgG,OAAA;QACA;UACAhG,CAAA,CAAA8G,KAAA,GAAA9G,CAAA,CAAA0G,mBAAA;QACA;;QAEA;QACA;;QAEA;MACA;MAEA,CAAAhM,IAAA,UAAAD,QAAA,CAAAC,IAAA;MACA,KAAAD,QAAA,CAAAI,KAAA,GAAAoK,SAAA,CAAA5F,MAAA;MACA,KAAA0E,OAAA,CAAAkB,SAAA;MACA,SAAApJ,aAAA,CAAA6H,IAAA,GAAArE,MAAA;QACA,KAAA5C,MAAA;MACA;IACA;IACAwK,YAAA,WAAAA,aAAA;MAAA,IAAAC,YAAA;MACA,KAAAlK,cAAA;MACA,KAAAmK,WAAA;MACA,KAAAD,YAAA,QAAA1I,MAAA,cAAA0I,YAAA,eAAAA,YAAA,CAAA7H,MAAA;QACA,KAAAb,MAAA,CAAA4I,OAAA,WAAAvG,IAAA;UAAA,OAAAA,IAAA,CAAAqE,OAAA;QAAA;QACA,KAAA5B,UAAA;MACA;MACA,KAAA7G,MAAA,UAAAZ,aAAA,CAAA6H,IAAA,GAAArE,MAAA;IACA;IACAgI,cAAA,WAAAA,eAAAC,KAAA;MACA,KAAAtK,cAAA,QAAAwB,MAAA,CAAAuB,MAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAkF,OAAA;MAAA;IACA;IACAiC,WAAA,WAAAA,YAAA;MACA,KAAAI,KAAA,CAAAC,OAAA,CAAAC,gBAAA;MACA,KAAAzK,cAAA;IACA;IACA6B,SAAA,WAAAA,UAAA;MAAA,IAAA6I,MAAA;MAAA,OAAAhF,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA+E,SAAA;QAAA,OAAAhF,mBAAA,GAAAI,IAAA,UAAA6E,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3E,IAAA,GAAA2E,SAAA,CAAA1E,IAAA;YAAA;cACAuE,MAAA,CAAAI,WAAA;cACAJ,MAAA,CAAArL,SAAA;cAAA,KACAqL,MAAA,CAAA3J,KAAA;gBAAA8J,SAAA,CAAA1E,IAAA;gBAAA;cAAA;cAAA0E,SAAA,CAAA1E,IAAA;cAAA,OACAuE,MAAA,CAAAK,YAAA;YAAA;cAAAF,SAAA,CAAA1E,IAAA;cAAA;YAAA;cAAA0E,SAAA,CAAA1E,IAAA;cAAA,OAEAuE,MAAA,CAAAM,aAAA;YAAA;cAEAN,MAAA,CAAAO,UAAA;cACAP,MAAA,CAAApE,UAAA;cACAoE,MAAA,CAAArL,SAAA;YAAA;YAAA;cAAA,OAAAwL,SAAA,CAAAxE,IAAA;UAAA;QAAA,GAAAsE,QAAA;MAAA;IACA;IACA7I,WAAA,WAAAA,YAAA;MAAA,IAAAoJ,aAAA;MACA,KAAAA,aAAA,QAAA1J,MAAA,cAAA0J,aAAA,eAAAA,aAAA,CAAA7I,MAAA;QACA,KAAA5E,QAAA,CAAAC,IAAA;QACA,KAAA8D,MAAA,QAAAA,MAAA,CAAAuB,MAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAA0G,mBAAA;QAAA;QACA,KAAApD,UAAA;MACA;IACA;IACA6E,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MAAA,IAAAvO,IAAA,GAAAwO,SAAA,CAAAhJ,MAAA,QAAAgJ,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,IAAAxO,IAAA;QACA,KAAA0C,UAAA;MACA;QACA,KAAAC,WAAA;MACA;MACA+L,UAAA;QACAH,MAAA,CAAApL,cAAA,CAAAoK,OAAA,WAAAvG,IAAA;UACA,IAAA2H,QAAA,GAAAlC,QAAA,CAAAzF,IAAA,CAAAiG,KAAA;UACA,IAAAsB,MAAA,CAAAvM,aAAA,CAAA6H,IAAA,GAAArE,MAAA;YACAwB,IAAA,CAAA4H,cAAA,GAAA5H,IAAA,CAAA6F,mBAAA;YAEA7F,IAAA,CAAAmG,QAAA,GAAAnG,IAAA,CAAA6F,mBAAA;YACA7F,IAAA,CAAA6H,WAAA,GAAAF,QAAA;YACA3H,IAAA,CAAAiG,KAAA,GAAAjG,IAAA,CAAA6F,mBAAA;YAEA7F,IAAA,CAAA6F,mBAAA;YACA7F,IAAA,CAAA8F,oBAAA,GAAA9F,IAAA,CAAA6F,mBAAA,GAAA7F,IAAA,CAAA+F,MAAA;UACA;YACA/F,IAAA,CAAA4H,cAAA,IAAAD,QAAA;YACA3H,IAAA,CAAA6F,mBAAA,IAAA8B,QAAA;YACA3H,IAAA,CAAA8F,oBAAA,GAAA9F,IAAA,CAAA6F,mBAAA,GAAA7F,IAAA,CAAA+F,MAAA;YACA/F,IAAA,CAAAmG,QAAA,GAAAwB,QAAA;YACA3H,IAAA,CAAA6H,WAAA,GAAAF,QAAA;YACA3H,IAAA,CAAAiG,KAAA,GAAAjG,IAAA,CAAA6F,mBAAA;UACA;UAEA7F,IAAA,CAAAqE,OAAA;QACA;QACA,IAAAyD,EAAA,GAAA9P,SAAA,CAAAuP,MAAA,CAAApL,cAAA;;QAEA;QACAoL,MAAA,CAAA7L,UAAA;QACA6L,MAAA,CAAAjB,WAAA;QACA;QACAiB,MAAA,CAAAtJ,WAAA;QACA,IAAAjF,IAAA;UACAuO,MAAA,CAAAQ,KAAA,mBAAAD,EAAA;UACAP,MAAA,CAAAQ,KAAA;UACAR,MAAA,CAAAxL,MAAA;UACAwL,MAAA,CAAA5J,MAAA;QACA;UACA4J,MAAA,CAAAQ,KAAA,gBAAAD,EAAA;QACA;MACA;IACA;IACAV,UAAA,WAAAA,WAAA;MAAA,IAAAY,aAAA;QAAAC,MAAA;MACA;MACA,IAAAC,MAAA;MACA,OAAAF,aAAA,QAAArK,MAAA,cAAAqK,aAAA,eAAAA,aAAA,CAAAxJ,MAAA;QACA,KAAAb,MAAA;QACA;QACA;MACA;MACAF,OAAA,CAAAC,GAAA,MAAAyK,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAA1K,MAAA;MACA;MACA,KAAAA,MAAA,QAAAA,MAAA,CAAAuB,MAAA,WAAAc,IAAA;QACAiI,MAAA,CAAAvC,IAAA,CAAA1F,IAAA,WAAAA,IAAA,CAAA6F,mBAAA;QACAoC,MAAA,CAAAvC,IAAA,CAAA1F,IAAA,cAAAA,IAAA,CAAA6F,mBAAA;QACA7F,IAAA,CAAAsI,IAAA,GAAA9P,SAAA,CAAAyP,MAAA,CAAA/K,KAAA,EAAA8C,IAAA;QACAkI,MAAA,CAAAlI,IAAA,CAAArF,IAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA,QAAAsN,MAAA,CAAAM,SAAA,CAAAzE,QAAA,CAAA9D,IAAA,CAAAsI,IAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;IACA;IACApB,YAAA,WAAAA,aAAA;MAAA,IAAAsB,MAAA;MAAA,OAAA3G,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA0G,SAAA;QAAA,IAAAC,WAAA,EAAAC,UAAA,EAAAC,GAAA,EAAAC,KAAA;QAAA,OAAA/G,mBAAA,GAAAI,IAAA,UAAA4G,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1G,IAAA,GAAA0G,SAAA,CAAAzG,IAAA;YAAA;cACA;cAAAoG,WAAA,GACAF,MAAA,CAAAvO,IAAA,EAAA0O,UAAA,GAAAD,WAAA,CAAAC,UAAA,EAAAC,GAAA,GAAAI,wBAAA,CAAAN,WAAA,EAAAO,SAAA;cACAJ,KAAA;cACA,IAAAK,MAAA,CAAAC,SAAA,CAAAC,QAAA,CAAAC,IAAA,CAAAV,UAAA;gBACAE,KAAA,GAAAF,UAAA,IAAAA,UAAA,CAAA9H,KAAA,MAAA3B,MAAA,WAAAC,CAAA;kBAAA,SAAAA,CAAA;gBAAA;cACA;cAAA4J,SAAA,CAAAzG,IAAA;cAAA,OACA5K,oBAAA,CAAAuF,aAAA,CAAAA,aAAA;gBACAqM,GAAA,EAAAd,MAAA,CAAAjP;cAAA,GACAqP,GAAA;gBACAW,iBAAA,EAAAf,MAAA,CAAAzP,UAAA;gBACA4P,UAAA,EAAAE,KAAA;gBACApO,cAAA,EAAA+N,MAAA,CAAAlP,SAAA;gBACAkQ,OAAA,EAAAhB,MAAA,CAAA1L;cAAA,EACA,EAAA8C,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACA0I,MAAA,CAAA5O,QAAA,CAAAI,KAAA,GAAA6F,GAAA,CAAApB,IAAA,CAAAD,MAAA;kBACAgK,MAAA,CAAA7K,MAAA,GAAAkC,GAAA,CAAApB,IAAA,CAAAW,GAAA,WAAAD,CAAA,EAAAsK,GAAA;oBACA;oBACAtK,CAAA,CAAAuK,YAAA,GAAAvK,CAAA,CAAAwK,yBAAA,GAAAxK,CAAA,CAAAwK,yBAAA;oBACAxK,CAAA,CAAAyK,WAAA,GAAAzK,CAAA,CAAA0K,qBAAA;oBACA1K,CAAA,CAAA2K,aAAA,GAAA3K,CAAA,CAAA4K,uBAAA;oBACA5K,CAAA,CAAA6K,eAAA,GAAA7K,CAAA,CAAAwK,yBAAA,IAAAxK,CAAA,CAAA6K,eAAA;oBACA;oBACA;oBACA;oBACA7K,CAAA,CAAAkF,OAAA;oBACAlF,CAAA,CAAA8K,YAAA,GAAAR,GAAA;oBACAtK,CAAA,CAAA+K,SAAA,GAAA1B,MAAA,CAAAvJ,UAAA,CAAAkL,IAAA;;oBAEA;oBACA,OAAAhL,CAAA;kBACA;kBACAqJ,MAAA,CAAAtF,OAAA;gBACA;kBACAsF,MAAA,CAAA9J,QAAA;oBACAC,OAAA,EAAAkB,GAAA,CAAAM,OAAA;oBACAnH,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA+P,SAAA,CAAAvG,IAAA;UAAA;QAAA,GAAAiG,QAAA;MAAA;IACA;IACA;AACA;AACA;IACA2B,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAC,WAAA,GAAAD,KAAA,CAAAC,WAAA;QAAAxQ,QAAA,GAAAuQ,KAAA,CAAAvQ,QAAA;MACA,SAAA0B,SAAA;MACA,KAAA5B,QAAA,CAAAC,IAAA,GAAAyQ,WAAA;MACA,KAAA1Q,QAAA,CAAAE,QAAA,GAAAA,QAAA;MACA,KAAAoJ,OAAA;MACA,KAAAT,UAAA,CAAA6H,WAAA;IACA;IAEApH,OAAA,WAAAA,QAAA;MAAA,IAAAqH,EAAA,GAAA/C,SAAA,CAAAhJ,MAAA,QAAAgJ,SAAA,QAAAC,SAAA,GAAAD,SAAA,WAAA7J,MAAA;MACA,KAAA5B,MAAA,GAAAwO,EAAA,CAAAC,KAAA,OAAA5Q,QAAA,CAAAC,IAAA,aAAAD,QAAA,CAAAE,QAAA,OAAAF,QAAA,CAAAC,IAAA,QAAAD,QAAA,CAAAE,QAAA;IACA;IAEAqN,aAAA,WAAAA,cAAA;MAAA,IAAAsD,MAAA;MAAA,OAAA5I,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA2I,SAAA;QAAA,IAAAC,SAAA;QAAA,OAAA7I,mBAAA,GAAAI,IAAA,UAAA0I,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxI,IAAA,GAAAwI,SAAA,CAAAvI,IAAA;YAAA;cAAAuI,SAAA,CAAAvI,IAAA;cAAA,OAEA3K,oBAAA,CAAAsF,aAAA,CAAAA,aAAA;gBACAqM,GAAA,EAAAmB,MAAA,CAAAlR;cAAA,GACAkR,MAAA,CAAAxQ,IAAA;gBACAsP,iBAAA,EAAAkB,MAAA,CAAA1R,UAAA;gBACA0B,cAAA,EAAAgQ,MAAA,CAAAnR,SAAA;gBACAkQ,OAAA,EAAAiB,MAAA,CAAA3N;cAAA,EACA,EAAA8C,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACA2K,MAAA,CAAA7Q,QAAA,CAAAI,KAAA,GAAA6F,GAAA,CAAApB,IAAA,CAAAD,MAAA;kBACAiM,MAAA,CAAA9M,MAAA,GAAAkC,GAAA,CAAApB,IAAA,CAAAW,GAAA,WAAAD,CAAA,EAAAsK,GAAA;oBACAtK,CAAA,CAAAuK,YAAA,GAAAvK,CAAA,CAAAwK,yBAAA,GAAAxK,CAAA,CAAAwK,yBAAA;oBACAxK,CAAA,CAAAyK,WAAA,GAAAzK,CAAA,CAAA0K,qBAAA;oBACA1K,CAAA,CAAA2K,aAAA,GAAA3K,CAAA,CAAA4K,uBAAA;oBACA,IAAA5K,CAAA,CAAA2L,qBAAA;sBACA3L,CAAA,CAAA4L,iBAAA,GAAAN,MAAA,CAAAO,kBAAA,CAAA7L,CAAA;oBACA;oBACAA,CAAA,CAAA6K,eAAA,GAAA7K,CAAA,CAAAwK,yBAAA,IAAAxK,CAAA,CAAA6K,eAAA;oBACA;oBACA7K,CAAA,CAAAkF,OAAA;oBACAlF,CAAA,CAAA8K,YAAA,GAAAR,GAAA;oBACA;oBACA;oBACA,KAAAgB,MAAA,CAAAjR,aAAA;sBACA2F,CAAA,CAAA8L,sBAAA,GAAA9L,CAAA,CAAA4L,iBAAA;oBACA;oBACA,OAAA5L,CAAA;kBACA;kBACAsL,MAAA,CAAAS,aAAA;kBACAT,MAAA,CAAAvH,OAAA;gBACA;kBACAuH,MAAA,CAAA/L,QAAA;oBACAC,OAAA,EAAAkB,GAAA,CAAAM,OAAA;oBACAnH,IAAA;kBACA;gBACA;cACA;YAAA;cAEA2R,SAAA,GAAAF,MAAA,CAAA9M,MAAA,CAAAyB,GAAA,WAAAY,IAAA;gBACA;kBACA3B,EAAA,EAAA2B,IAAA,CAAAmL,iBAAA;kBACAxQ,IAAA;gBACA;cACA;cAAAkQ,SAAA,CAAAvI,IAAA;cAAA,OACA3J,WAAA,CAAAgS,SAAA,EAAA/K,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACA,IAAAsL,OAAA;kBACAvL,GAAA,CAAApB,IAAA,CAAA8H,OAAA,WAAAvG,IAAA;oBACAoL,OAAA,CAAApL,IAAA,CAAA3B,EAAA,MAAA2B,IAAA,CAAAqL,OAAA;kBACA;kBACAZ,MAAA,CAAA9M,MAAA,CAAA4I,OAAA,WAAA+E,GAAA;oBACA,IAAAF,OAAA,CAAAG,cAAA,CAAAD,GAAA,CAAAH,iBAAA;sBACAV,MAAA,CAAA/E,IAAA,CAAA4F,GAAA,cAAAF,OAAA,CAAAE,GAAA,CAAAH,iBAAA;oBACA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAN,SAAA,CAAArI,IAAA;UAAA;QAAA,GAAAkI,QAAA;MAAA;IACA;IACAc,mBAAA,WAAAA,oBAAAC,KAAA;MAAA,IAAAH,GAAA,GAAAG,KAAA,CAAAH,GAAA;MACA,QAAAA,GAAA,CAAAI,QAAA;IACA;IACAV,kBAAA,WAAAA,mBAAAhL,IAAA;MACA,IAAAA,IAAA,CAAA2L,sBAAA;QACA,OAAA3L,IAAA,CAAA2L,sBAAA;MACA;MACA,IAAA3L,IAAA,CAAA4L,yBAAA;QACA,IAAAC,IAAA,GAAA7L,IAAA,CAAA4L,yBAAA,CAAA/K,KAAA;QACA,IAAAgL,IAAA,CAAA/H,QAAA,CAAA9D,IAAA,CAAA+K,iBAAA;UACA,OAAA/K,IAAA,CAAA+K,iBAAA;QACA,WAAAc,IAAA,CAAA/H,QAAA,CAAA9D,IAAA,CAAA8L,sBAAA;UACA,OAAA9L,IAAA,CAAA8L,sBAAA;QACA;MACA;QACA,IAAA9L,IAAA,CAAA+K,iBAAA;UACA,OAAA/K,IAAA,CAAA+K,iBAAA;QACA,WAAA/K,IAAA,CAAA8L,sBAAA;UACA,OAAA9L,IAAA,CAAA8L,sBAAA;QACA;MACA;MAEA;IACA;IACAZ,aAAA,WAAAA,cAAA;MACA;MACA,KAAA3P,WAAA,QAAAoC,MAAA,CAAAoO,KAAA,WAAA5M,CAAA;QAAA,QAAAA,CAAA,CAAA2L,qBAAA;MAAA;MACArN,OAAA,CAAAC,GAAA,0BAAAnC,WAAA;MACA,SAAAA,WAAA;QACA,IAAAkO,GAAA,QAAA3N,OAAA,CAAAkQ,SAAA,WAAA7M,CAAA;UAAA,OAAAA,CAAA,CAAA8M,IAAA;QAAA;QACAxC,GAAA,gBAAA3N,OAAA,CAAAoQ,MAAA,CAAAzC,GAAA;MACA;IACA;IACA0C,SAAA,WAAAA,UAAAN,IAAA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;IAfA,CAkBA;IACAO,WAAA,WAAAA,YAAA;MACA,KAAArE,KAAA;IACA;IACA;IACA;IACA;IACAxF,cAAA,WAAAA,eAAAN,IAAA;MAAA,IAAAoK,MAAA;MAAA,OAAAxK,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAuK,SAAA;QAAA,OAAAxK,mBAAA,GAAAI,IAAA,UAAAqK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnK,IAAA,GAAAmK,SAAA,CAAAlK,IAAA;YAAA;cAAAkK,SAAA,CAAAlK,IAAA;cAAA,OACA7K,aAAA;gBACAwK,IAAA,EAAAA;cACA,GAAArC,IAAA,WAAAC,GAAA;gBACA,IAAAC,SAAA,GAAAD,GAAA,CAAAC,SAAA;kBAAArB,IAAA,GAAAoB,GAAA,CAAApB,IAAA;kBAAA0B,OAAA,GAAAN,GAAA,CAAAM,OAAA;gBACA,IAAAL,SAAA;kBACAuM,MAAA,CAAArQ,QAAA,GAAAkN,MAAA,CAAAuD,MAAA,KAAAJ,MAAA,CAAArQ,QAAA,EAAAyC,IAAA,CAAAiO,IAAA;kBACAL,MAAA,CAAAzS,QAAA,CAAAE,QAAA,GAAA6S,MAAA,CAAAN,MAAA,CAAArQ,QAAA,CAAA4Q,UAAA;kBACA,IAAAf,IAAA,GAAApN,IAAA,CAAAoO,UAAA;kBACAR,MAAA,CAAAvQ,OAAA,GAAA+P,IAAA,CAAA3M,MAAA,WAAAC,CAAA;oBAAA,OAAAA,CAAA,CAAA2N,UAAA;kBAAA,GACA1N,GAAA,WAAAY,IAAA;oBACA,IAAAA,IAAA,CAAA+M,SAAA;sBACA/M,IAAA,CAAAgN,KAAA;oBACA;oBACA,OAAAhN,IAAA;kBACA;kBACA;kBACA;kBACA;kBACA;gBACA;kBACAqM,MAAA,CAAA3N,QAAA;oBACAC,OAAA,EAAAwB,OAAA;oBACAnH,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAwT,SAAA,CAAAhK,IAAA;UAAA;QAAA,GAAA8J,QAAA;MAAA;IACA;IACAxO,iBAAA,WAAAA,kBAAA;MAAA,IAAAmP,OAAA;MACA/U,eAAA;QAAAgV,YAAA;MAAA,GAAAtN,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA;UACAmN,OAAA,CAAAE,SAAA,WAAAC,CAAA;YACAH,OAAA,CAAAvG,KAAA,CAAA2G,oBAAA,CAAAC,iBAAA,CAAAzN,GAAA,CAAApB,IAAA;UACA;QACA;UACAwO,OAAA,CAAAvO,QAAA;YACA1F,IAAA;YACA2F,OAAA,EAAAkB,GAAA,CAAAM;UACA;QACA;MACA;IACA;IACA8G,WAAA,WAAAA,YAAA;MACA,KAAAhN,IAAA,CAAAO,SAAA;MACA,KAAAP,IAAA,CAAAC,SAAA;MACA,KAAAD,IAAA,CAAAE,aAAA;MACA,KAAAF,IAAA,CAAAU,IAAA;MACA,KAAAV,IAAA,CAAAS,IAAA;MACA,KAAAT,IAAA,CAAAQ,cAAA;MACA,KAAAR,IAAA,CAAAG,aAAA;MACA,KAAAH,IAAA,CAAAI,SAAA;MACA,KAAAJ,IAAA,CAAAK,kBAAA;MACA,KAAAL,IAAA,CAAAM,cAAA;MACA,KAAAS,aAAA;MACA,KAAAE,iBAAA;MACA,KAAAD,qBAAA;MACA,KAAAmL,YAAA;IACA;IACArI,OAAA,WAAAA,QAAA;MAAA,IAAAwP,OAAA;MACApV,eAAA;QAAAqV,UAAA;MAAA,GAAA5N,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAyN,OAAA,CAAAxQ,UAAA,GAAA8C,GAAA,CAAApB,IAAA;QACA;UACA8O,OAAA,CAAA7O,QAAA;YACAC,OAAA,EAAAkB,GAAA,CAAAM,OAAA;YACAnH,IAAA;UACA;QACA;MACA;IACA;IACAyU,SAAA,WAAAA,UAAA;MACA,UAAAtR,cAAA,CAAAqC,MAAA;MACA,KAAA8I,UAAA;IACA;IACA/H,wBAAA,WAAAA,yBAAAmO,EAAA;MAAA,IAAAC,OAAA;MACA,UAAA7Q,MAAA;QACA,KAAAjB,iBAAA;MACA;QACAvD,wBAAA;UAAAkR,OAAA,OAAA1M;QAAA,GAAA8C,IAAA,WAAAC,GAAA;UACA8N,OAAA,CAAA9R,iBAAA,GAAAgE,GAAA,CAAApB,IAAA;UACA;UACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
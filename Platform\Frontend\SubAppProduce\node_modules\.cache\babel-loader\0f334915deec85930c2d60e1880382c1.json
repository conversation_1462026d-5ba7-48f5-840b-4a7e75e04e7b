{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\addDraft.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\addDraft.vue", "mtime": 1757468127991}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetGridByCode", "GetCanSchdulingComps", "GetCanSchdulingParts", "GetPartList", "v4", "uuidv4", "debounce", "deepClone", "tablePageSize", "GetCompTypeTree", "GetPartTypeList", "TreeDetail", "ExpandableSection", "GetInstallUnitIdNameList", "GetProjectAreaTreeList", "getUnique", "mapGetters", "findAllParentNode", "GetStopList", "SPLIT_SYMBOL", "components", "props", "scheduleId", "type", "String", "default", "pageType", "showDialog", "Boolean", "installId", "currentIds", "isPartPrepare", "comName", "partName", "data", "pageInfo", "page", "pageSize", "pageSizes", "total", "form", "Comp_Code", "Comp_CodeBlur", "Part_CodeBlur", "Part_Code", "Type_Name", "InstallUnit_Id", "Spec", "Type", "cur<PERSON><PERSON>ch", "cur<PERSON><PERSON><PERSON><PERSON><PERSON>", "showExpand", "searchContent", "searchPartContent", "statusType", "projectName", "expandedKey", "statusCode", "isOwnerNull", "tbLoading", "treeLoading", "addLoading", "saveLoading", "showSc", "installUnitIdList", "columns", "fTable", "tbConfig", "TotalCount", "Page", "totalSelection", "treeData", "search", "treeSelectParams", "placeholder", "clearable", "ObjectTypeList", "clickParent", "children", "label", "value", "areaId", "typeOption", "computed", "_objectSpread", "isCom", "filterText", "watch", "newValue", "mounted", "methods", "initData", "console", "log", "tbData", "getConfig", "fetchTreeData", "getObjectTypeList", "getType", "fetchData", "setPageData", "handleNodeClick", "_data$Children", "_this", "Id", "ParentNodes", "Children", "length", "Data", "$message", "message", "setData", "_ref", "projectId", "Project_Id", "_arr", "nodeLabels", "filter", "v", "map", "p", "Label", "getInstallUnitIdNameList", "_this2", "MenuId", "$route", "meta", "then", "res", "IsSucceed", "resData", "item", "Is_Directory", "<PERSON><PERSON><PERSON>", "Message", "catch", "_this3", "deepFilter", "tree", "i", "ParentId", "customFilterFun", "node", "arr", "split", "labelVal", "statusVal", "parentNode", "parent", "labels", "status", "level", "concat", "_toConsumableArray", "resultLabel", "resultStatus", "some", "s", "indexOf", "_this4", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "code", "wrap", "_callee$", "_context", "prev", "next", "getTableConfig", "stop", "filterData", "_this5", "splitAndClean", "input", "trim", "replace", "f", "formKey", "push", "setPage", "checkMatch", "origin", "comp", "_comp", "_code$split", "_code$split2", "_slicedToArray", "key", "_origin", "_code$split3", "_code$split4", "includes", "checkExactMatch", "_code$split5", "_code$split6", "_code$split7", "_code$split8", "temTbData", "checked", "compCode", "Component_Codes", "compCodeArray", "flag", "partCodeBlurArray", "partCodeArray", "specArray", "spec", "csCount", "componentMap", "reduce", "acc", "_code$split9", "_code$split0", "parseInt", "$set", "Math", "min", "Can_Schduling_Count", "Can_Schduling_Weight", "Weight", "searchcount", "count", "searchcountMax", "maxCount", "handleSearch", "_this$tbData", "clearSelect", "for<PERSON>ach", "tbSelectChange", "array", "$refs", "xTable1", "clearCheckboxRow", "_this6", "_callee2", "_callee2$", "_context2", "handleReset", "getComTbData", "getPartTbData", "initTbData", "_this$tbData2", "handleSave", "_this7", "arguments", "undefined", "setTimeout", "intCount", "<PERSON><PERSON><PERSON><PERSON>_Count", "chooseCount", "cp", "$emit", "_this$tbData3", "_this8", "obj<PERSON><PERSON>", "JSON", "parse", "stringify", "uuid", "addTbKeys", "_this9", "_callee3", "_this9$form", "Comp_Codes", "obj", "codes", "_callee3$", "_context3", "_objectWithoutProperties", "_excluded", "Object", "prototype", "toString", "call", "Ids", "Schduling_Plan_Id", "Area_Id", "idx", "originalPath", "Scheduled_Technology_Path", "Workshop_Id", "Scheduled_Workshop_Id", "Workshop_Name", "Scheduled_Workshop_Name", "Technology_Path", "initRowIndex", "Area_Name", "join", "handlePageChange", "_ref2", "currentPage", "tb", "slice", "_this0", "_callee4", "submitObj", "_callee4$", "_context4", "Comp_Import_Detail_Id", "Part_Used_Process", "getPartUsedProcess", "Temp_Part_Used_Process", "setPartColumn", "Part_Aggregate_Id", "stopMap", "Is_Stop", "row", "hasOwnProperty", "checkCheckboxMethod", "_ref3", "stopFlag", "Scheduled_Used_Process", "Component_Technology_Path", "list", "Part_Type_Used_Process", "every", "findIndex", "Code", "splice", "mergeData", "handleClose", "_this1", "_callee5", "_callee5$", "_context5", "assign", "Grid", "Number", "Row_Number", "ColumnList", "Is_Display", "Is_Frozen", "fixed", "_this10", "professional", "$nextTick", "_", "treeSelectObjectType", "treeDataUpdateFun", "_this11", "Part_Grade", "addToList", "id", "_this12"], "sources": ["src/views/PRO/plan-production/schedule-production-new-part/components/addDraft.vue"], "sourcesContent": ["<template>\r\n  <div class=\"contentBox\">\r\n    <div class=\"main-info\">\r\n      <div class=\"left\">\r\n        <ExpandableSection v-model=\"showExpand\" v-loading=\"tbLoading\" class=\"fff\" :width=\"300\">\r\n          <div class=\"inner-wrapper\">\r\n            <div class=\"tree-search\">\r\n              <el-select\r\n                v-model=\"statusType\"\r\n                clearable\r\n                class=\"search-select\"\r\n                placeholder=\"请选择\"\r\n              >\r\n                <el-option label=\"可排产\" value=\"可排产\" />\r\n                <el-option label=\"排产完成\" value=\"排产完成\" />\r\n                <el-option label=\"未导入\" value=\"未导入\" />\r\n              </el-select>\r\n              <el-input\r\n                v-model.trim=\"projectName\"\r\n                placeholder=\"搜索...\"\r\n                size=\"small\"\r\n                clearable\r\n                suffix-icon=\"el-icon-search\"\r\n              />\r\n            </div>\r\n            <el-divider class=\"cs-divider\" />\r\n            <div class=\"tree-x cs-scroll\">\r\n              <tree-detail\r\n                ref=\"tree\"\r\n                icon=\"icon-folder\"\r\n                is-custom-filter\r\n                :custom-filter-fun=\"customFilterFun\"\r\n                :loading=\"treeLoading\"\r\n                :tree-data=\"treeData\"\r\n                show-status\r\n                show-detail\r\n                :filter-text=\"filterText\"\r\n                :expanded-key=\"expandedKey\"\r\n                @handleNodeClick=\"handleNodeClick\"\r\n              >\r\n                <template #csLabel=\"{showStatus,data}\">\r\n                  <span v-if=\"!data.ParentNodes\" class=\"cs-blue\">({{ data.Code }})</span>{{ data.Label }}\r\n                  <template v-if=\"showStatus\">\r\n                    <span :class=\"['cs-tag',data.Data[statusCode]=='可排产' ? 'greenBg' : data.Data[statusCode]=='排产完成' ?'orangeBg':data.Data[statusCode]=='未导入'?'redBg':'']\">\r\n                      <i\r\n                        v-if=\"data.Data[statusCode]\"\r\n                        :class=\"[data.Data[statusCode]=='可排产' ? 'fourGreen' : data.Data[statusCode]=='排产完成' ?'fourOrange':data.Data[statusCode]=='未导入'?'fourRed':'']\"\r\n                      >\r\n                        {{ data.Data[statusCode] }}\r\n                      </i>\r\n\r\n                    </span>\r\n                  </template>\r\n                </template>\r\n\r\n              </tree-detail>\r\n            </div>\r\n          </div>\r\n        </ExpandableSection>\r\n      </div>\r\n      <div class=\"right\">\r\n\r\n        <el-form ref=\"form\" :model=\"form\" label-width=\"90px\">\r\n          <el-row>\r\n            <template >\r\n              <el-col :span=\"12\">\r\n                <!--                <el-form-item label-width=\"70px\" label=\"零件名称\" prop=\"Part_Code\">\r\n                  <div class=\"cs-input-x\">\r\n                    <el-input\r\n                      v-model=\"form.Part_Code\"\r\n                      placeholder=\"请输入(空格区分/多个搜索)\"\r\n                      clearable\r\n                      class=\"w100\"\r\n                    />\r\n                    <el-input\r\n                      v-model=\"form.Part_CodeBlur\"\r\n                      clearable\r\n                      class=\"w100\"\r\n                      style=\"margin-left: 10px;\"\r\n                      placeholder=\"模糊查找(请输入关键字)\"\r\n                      type=\"text\"\r\n                    />\r\n                  </div>\r\n                </el-form-item>\r\n             -->\r\n                <el-form-item prop=\"searchContent\" :label=\"`${comName}名称`\">\r\n                  <el-input\r\n                    v-model=\"searchContent\"\r\n                    clearable\r\n                    class=\"input-with-select w100\"\r\n                    placeholder=\"请输入(空格区分/多个搜索)\"\r\n                    size=\"small\"\r\n                  >\r\n                    <el-select\r\n                      slot=\"prepend\"\r\n                      v-model=\"curSearch\"\r\n                      placeholder=\"请选择\"\r\n                      style=\"width: 100px\"\r\n                    >\r\n                      <el-option label=\"精准查询\" :value=\"1\" />\r\n                      <el-option label=\"模糊查询\" :value=\"0\" />\r\n                    </el-select>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item prop=\"searchPartContent\" :label=\"`${partName}名称`\">\r\n                  <el-input\r\n                    v-model=\"searchPartContent\"\r\n                    clearable\r\n                    class=\"input-with-select w100\"\r\n                    placeholder=\"请输入(空格区分/多个搜索)\"\r\n                    size=\"small\"\r\n                  >\r\n                    <el-select\r\n                      slot=\"prepend\"\r\n                      v-model=\"curPartSearch\"\r\n                      placeholder=\"请选择\"\r\n                      style=\"width: 100px\"\r\n                    >\r\n                      <el-option label=\"精准查询\" :value=\"1\" />\r\n                      <el-option label=\"模糊查询\" :value=\"0\" />\r\n                    </el-select>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"5\">\r\n                <el-form-item label=\"规格\" prop=\"Spec\">\r\n                  <el-input\r\n                    v-model=\"form.Spec\"\r\n                    placeholder=\"请输入\"\r\n                    clearable\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"5\">\r\n                <el-form-item :label=\"`${partName}种类`\" prop=\"Type_Name\">\r\n                  <el-select\r\n                    v-model=\"form.Type_Name\"\r\n                    placeholder=\"请选择\"\r\n                    clearable\r\n                  >\r\n                    <el-option\r\n                      v-for=\"item in typeOption\"\r\n                      :key=\"item.Code\"\r\n                      :label=\"item.Name\"\r\n                      :value=\"item.Name\"\r\n                    />\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n            </template>\r\n            <el-col :span=\"5\">\r\n              <el-form-item label=\"批次\" label-width=\"50px\" prop=\"Create_UserName\">\r\n                <el-select\r\n                  v-model=\"form.InstallUnit_Id\"\r\n                  filterable\r\n                  clearable\r\n                  multiple\r\n                  style=\"width: 100%\"\r\n                  placeholder=\"请选择\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in installUnitIdList\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Name\"\r\n                    :value=\"item.Id\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"9\">\r\n              <el-form-item label-width=\"0\">\r\n                <el-button style=\"margin-left: 10px\" @click=\"handleReset\">重置</el-button>\r\n                <el-button style=\"margin-left: 10px\" type=\"primary\" @click=\"handleSearch()\">查询</el-button>\r\n                <el-button :loading=\"addLoading\" style=\"margin-left: 10px\" type=\"primary\" @click=\"addToList()\">加入列表</el-button>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n          </el-row>\r\n        </el-form>\r\n\r\n        <div class=\"tb-wrapper\">\r\n          <vxe-table\r\n            ref=\"xTable1\"\r\n            :empty-render=\"{name: 'NotData'}\"\r\n            show-header-overflow\r\n            empty-text=\"暂无数据\"\r\n            height=\"auto\"\r\n            show-overflow\r\n            :checkbox-config=\"{checkField: 'checked', checkMethod: checkCheckboxMethod}\"\r\n            :loading=\"tbLoading\"\r\n            :row-config=\"{isCurrent: true, isHover: true }\"\r\n            class=\"cs-vxe-table\"\r\n            align=\"left\"\r\n            stripe\r\n            :data=\"fTable\"\r\n            resizable\r\n            :edit-config=\"{trigger: 'click', mode: 'cell'}\"\r\n            :tooltip-config=\"{ enterable: true }\"\r\n            @checkbox-all=\"tbSelectChange\"\r\n            @checkbox-change=\"tbSelectChange\"\r\n          >\r\n            <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n            <template v-for=\"item in columns\">\r\n              <vxe-column\r\n                v-if=\"item.Code === 'Is_Component'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <el-tag :type=\"row.Is_Component ? 'danger' : 'success'\">{{\r\n                    row.Is_Component ? \"否\" : \"是\"\r\n                  }}</el-tag>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"['Part_Code','Comp_Code'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                  <el-tag v-if=\"row.Is_Change\" style=\"margin-right: 8px;\" type=\"danger\">变</el-tag>\r\n                  {{ row[item.Code] | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"['Can_Schduling_Count'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <span v-if=\"showSc\">{{ row.csCount||'' }}</span>\r\n                  <span v-else>{{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"['Can_Schduling_Weight'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <span v-if=\"showSc\">{{ row.csCountWeight||'' }}</span>\r\n                  <span v-else>{{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                show-overflow=\"tooltip\"\r\n                sortable\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                :min-width=\"item.Width\"\r\n              />\r\n            </template>\r\n          </vxe-table>\r\n        </div>\r\n        <div class=\"data-info\">\r\n          <el-tag\r\n            size=\"medium\"\r\n            class=\"info-x\"\r\n          >已选 {{ totalSelection.length }} 条数据\r\n          </el-tag>\r\n          <vxe-pager\r\n            border\r\n            background\r\n            :loading=\"tbLoading\"\r\n            :current-page.sync=\"pageInfo.page\"\r\n            :page-size.sync=\"pageInfo.pageSize\"\r\n            :page-sizes=\"pageInfo.pageSizes\"\r\n            :total=\"pageInfo.total\"\r\n            :layouts=\"['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']\"\r\n            size=\"small\"\r\n            @page-change=\"handlePageChange\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"button\">\r\n      <el-button @click=\"handleClose\">取消</el-button>\r\n      <el-button\r\n        type=\"primary\"\r\n        :disabled=\"!totalSelection.length\"\r\n        :loading=\"saveLoading\"\r\n        @click=\"handleSave(2)\"\r\n      >保存</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { GetCanSchdulingComps } from '@/api/PRO/production-task'\r\nimport { GetCanSchdulingParts, GetPartList } from '@/api/PRO/production-part'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { debounce, deepClone } from '@/utils'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport { GetCompTypeTree } from '@/api/PRO/professionalType'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\nimport TreeDetail from '@/components/TreeDetail/index.vue'\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport { GetInstallUnitIdNameList, GetProjectAreaTreeList } from '@/api/PRO/project'\r\nimport { getUnique } from '../constant'\r\nimport { mapGetters } from 'vuex'\r\nimport { findAllParentNode } from '@/utils/tree'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nconst SPLIT_SYMBOL = '$_$'\r\n\r\nexport default {\r\n  components: { ExpandableSection, TreeDetail },\r\n  props: {\r\n    scheduleId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    pageType: {\r\n      type: String,\r\n      default: 'com'\r\n    },\r\n    showDialog: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n\r\n    installId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    currentIds: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n\r\n    isPartPrepare: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    comName: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    partName: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      pageInfo: {\r\n        page: 1,\r\n        pageSize: 500,\r\n        pageSizes: tablePageSize,\r\n        total: 0\r\n      },\r\n      form: {\r\n        Comp_Code: '',\r\n        Comp_CodeBlur: '',\r\n        Part_CodeBlur: '',\r\n        Part_Code: '',\r\n        Type_Name: '',\r\n        InstallUnit_Id: [],\r\n        Spec: '',\r\n        Type: ''\r\n      },\r\n      curSearch: 1,\r\n      curPartSearch: 1,\r\n      showExpand: true,\r\n      searchContent: '',\r\n      searchPartContent: '',\r\n      statusType: '',\r\n      projectName: '',\r\n      expandedKey: '',\r\n      statusCode: 'Part_Schdule_Status',\r\n      isOwnerNull: true,\r\n      tbLoading: false,\r\n      treeLoading: false,\r\n      addLoading: false,\r\n      saveLoading: false,\r\n      showSc: false,\r\n      installUnitIdList: [],\r\n      columns: [],\r\n      fTable: [],\r\n      tbConfig: {},\r\n      TotalCount: 0,\r\n      Page: 0,\r\n      totalSelection: [],\r\n      treeData: [],\r\n      search: () => ({}),\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        clearable: true\r\n      },\r\n      ObjectTypeList: {\r\n        // 构件类型\r\n        'check-strictly': true,\r\n        'default-expand-all': true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      },\r\n      areaId: '',\r\n      typeOption: []\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    },\r\n    filterText() {\r\n      return this.projectName + SPLIT_SYMBOL + this.statusType\r\n    },\r\n    ...mapGetters('schedule', ['addTbKeys'])\r\n  },\r\n  watch: {\r\n    showDialog(newValue) {\r\n      newValue && (this.saveLoading = false)\r\n    }\r\n  },\r\n  mounted() {\r\n\r\n  },\r\n  methods: {\r\n    initData() {\r\n      console.log('initData')\r\n      this.tbData = []\r\n      this.getConfig()\r\n      this.fetchTreeData()\r\n      if (this.isCom) {\r\n        this.getObjectTypeList()\r\n      } else {\r\n        this.getType()\r\n      }\r\n      this.search = debounce(this.fetchData, 800, true)\r\n      this.setPageData()\r\n    },\r\n    handleNodeClick(data) {\r\n      if (this.areaId === data.Id) {\r\n        return\r\n      }\r\n      if (!data.ParentNodes || data.Children?.length > 0) {\r\n        return\r\n      }\r\n      if (data?.Data[this.statusCode] === '未导入') {\r\n        this.$message({\r\n          message: '清单未导入，请联系深化人员导入清单',\r\n          type: 'warning'\r\n        })\r\n        this.expandedKey = data.Id\r\n        return\r\n      }\r\n\r\n      const setData = ({ Data }) => {\r\n        this.areaId = Data.Id\r\n        this.projectId = Data.Project_Id\r\n        this.expandedKey = this.areaId\r\n\r\n        const _arr = findAllParentNode(this.treeData, data.Id, true)\r\n        this.nodeLabels = _arr.filter(v => !!v.ParentNodes).map(p => p.Label)\r\n\r\n        // this.formInline.Finish_Date = ''\r\n        // this.formInline.InstallUnit_Id = ''\r\n        // this.formInline.Remark = ''\r\n        this.fetchData()\r\n        // this.getAreaInfo()\r\n        this.getInstallUnitIdNameList()\r\n      }\r\n\r\n      setData(data)\r\n    },\r\n    fetchTreeData() {\r\n      this.treeLoading = true\r\n      GetProjectAreaTreeList({ MenuId: this.$route.meta.Id, projectName: this.projectName, type: this.isCom ? 1 : 2 }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          if (res.Data.length === 0) {\r\n            this.treeData = []\r\n            this.treeLoading = false\r\n            return\r\n          }\r\n          const resData = res.Data.map(item => {\r\n            item.Is_Directory = true\r\n            return item\r\n          })\r\n          this.treeData = resData\r\n          console.log('setKey')\r\n          this.setKey()\r\n          this.treeLoading = false\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.treeData = []\r\n          this.treeLoading = false\r\n        }\r\n      }).catch(() => {\r\n        this.treeLoading = false\r\n        this.treeData = []\r\n      })\r\n    },\r\n    setKey() {\r\n      const deepFilter = (tree) => {\r\n        console.log('tree', tree)\r\n        for (let i = 0; i < tree.length; i++) {\r\n          const item = tree[i]\r\n          const { Data, Children } = item\r\n          console.log(Data)\r\n          if (Data.ParentId && !Children?.length) {\r\n            this.handleNodeClick(item)\r\n            return\r\n          } else {\r\n            if (Children && Children?.length > 0) {\r\n              return deepFilter(Children)\r\n            }\r\n          }\r\n        }\r\n      }\r\n      return deepFilter(this.treeData)\r\n    },\r\n    customFilterFun(value, data, node) {\r\n      const arr = value.split(SPLIT_SYMBOL)\r\n      const labelVal = arr[0]\r\n      const statusVal = arr[1]\r\n      if (!value) return true\r\n      let parentNode = node.parent\r\n      let labels = [node.label]\r\n      let status = [data.Data[this.statusCode]]\r\n      let level = 1\r\n      while (level < node.level) {\r\n        labels = [...labels, parentNode.label]\r\n        status = [...status, data.Data[this.statusCode]]\r\n        parentNode = parentNode.parent\r\n        level++\r\n      }\r\n      labels = labels.filter(v => !!v)\r\n      status = status.filter(v => !!v)\r\n      let resultLabel = true\r\n      let resultStatus = true\r\n      if (this.statusType) {\r\n        resultStatus = status.some(s => s.indexOf(statusVal) !== -1)\r\n      }\r\n      if (this.projectName) {\r\n        resultLabel = labels.some(s => s.indexOf(labelVal) !== -1)\r\n      }\r\n      return resultLabel && resultStatus\r\n    },\r\n    async getConfig() {\r\n      let code = ''\r\n      code = this.isCom\r\n        ? 'PROComDraftEditTbConfig'\r\n        : 'PROPartDraftEditTbConfig_new'\r\n      await this.getTableConfig(code)\r\n      // this.fetchData()\r\n    },\r\n    filterData(page) {\r\n      console.log(22)\r\n      const splitAndClean = (input) => input.trim().replace(/\\s+/g, ' ').split(' ')\r\n\r\n      if (this.curSearch === 1) {\r\n        this.form.Comp_Code = this.searchContent\r\n        this.form.Comp_CodeBlur = ''\r\n      }\r\n      if (this.curSearch === 0) {\r\n        this.form.Comp_CodeBlur = this.searchContent\r\n        this.form.Comp_Code = ''\r\n      }\r\n      if (this.curPartSearch === 1) {\r\n        this.form.Part_CodeBlur = ''\r\n        this.form.Part_Code = this.searchPartContent\r\n      }\r\n      if (this.curPartSearch === 0) {\r\n        this.form.Part_Code = ''\r\n        this.form.Part_CodeBlur = this.searchPartContent\r\n      }\r\n\r\n      const f = []\r\n      for (const formKey in this.form) {\r\n        if (this.form[formKey] || this.form[formKey] === false) {\r\n          f.push(formKey)\r\n        }\r\n      }\r\n      if (!f.length) {\r\n        this.setPage()\r\n        !page && (this.pageInfo.page = 1)\r\n        this.pageInfo.total = this.tbData.length\r\n        return\r\n      }\r\n\r\n      const checkMatch = (origin, comp) => {\r\n        const _comp = comp.map(code => {\r\n          const [key, value] = code.split('&&&')\r\n          return key\r\n        })\r\n        const _origin = origin.map(code => {\r\n          const [key, value] = code.split('&&&')\r\n          return key\r\n        })\r\n        return _origin.some(item => {\r\n          return _comp.some(value => item.includes(value))\r\n        })\r\n      }\r\n      const checkExactMatch = (origin, comp) => {\r\n        const _comp = comp.map(code => {\r\n          const [key, value] = code.split('&&&')\r\n          return key\r\n        })\r\n        const _origin = origin.map(code => {\r\n          const [key, value] = code.split('&&&')\r\n          return key\r\n        })\r\n\r\n        return _origin.some(item => _comp.includes(item))\r\n      }\r\n\r\n      const temTbData = this.tbData.filter(v => {\r\n        v.checked = false\r\n        const compCode = v.Component_Codes || []\r\n\r\n        if (this.form.Comp_Code.trim()) {\r\n          const compCodeArray = splitAndClean(this.form.Comp_Code)\r\n          if (compCodeArray.length) {\r\n            const flag = checkExactMatch(compCode, compCodeArray)\r\n            console.log(887, compCode, compCodeArray, flag)\r\n            if (!flag) return false\r\n          }\r\n        }\r\n\r\n        if (this.form.Comp_CodeBlur.trim()) {\r\n          const compCodeArray = splitAndClean(this.form.Comp_CodeBlur)\r\n          if (compCodeArray.length) {\r\n            const flag = checkMatch(compCode, compCodeArray)\r\n            if (!flag) return false\r\n          }\r\n        }\r\n\r\n        if (this.form.Type && v.Type !== this.form.Type) {\r\n          return false\r\n        }\r\n\r\n        if (this.form.Part_CodeBlur.trim()) {\r\n          const partCodeBlurArray = splitAndClean(this.form.Part_CodeBlur)\r\n          if (!partCodeBlurArray.some(code => v['Part_Code'].includes(code))) {\r\n            return false\r\n          }\r\n        }\r\n\r\n        if (this.form.Part_Code.trim()) {\r\n          const partCodeArray = splitAndClean(this.form.Part_Code)\r\n          if (!partCodeArray.includes(v['Part_Code'])) {\r\n            return false\r\n          }\r\n        }\r\n\r\n        if (this.form.InstallUnit_Id.length && !this.form.InstallUnit_Id.includes(v.InstallUnit_Id)) {\r\n          return false\r\n        }\r\n\r\n        if (this.form.Type_Name !== '' && v.Type_Name !== this.form.Type_Name) {\r\n          return false\r\n        }\r\n\r\n        if (this.form.Spec.trim() !== '') {\r\n          const specArray = splitAndClean(this.form.Spec)\r\n          if (!specArray.some(spec => v.Spec.includes(spec))) {\r\n            return false\r\n          }\r\n        }\r\n        if (this.searchContent.trim().length) {\r\n          let csCount = 0\r\n\r\n          v.componentMap = (v.Component_Codes || []).reduce((acc, code) => {\r\n            const [key, value] = code.split('&&&')\r\n            acc[key] = parseInt(value)\r\n            if (this.curSearch === 1) {\r\n              const compCodeArray = splitAndClean(this.form.Comp_Code)\r\n              if (compCodeArray.length) {\r\n                const flag = checkExactMatch([key], compCodeArray)\r\n                if (flag) {\r\n                  csCount += parseInt(value)\r\n                }\r\n              }\r\n            } else {\r\n              const compCodeArray = splitAndClean(this.form.Comp_CodeBlur)\r\n              if (compCodeArray.length) {\r\n                const flag = checkMatch([key], compCodeArray)\r\n                console.log('pflag', key, compCodeArray, flag, value)\r\n                if (flag) {\r\n                  csCount += parseInt(value)\r\n                }\r\n              }\r\n            }\r\n            return acc\r\n          }, {})\r\n          this.$set(v, 'csCount', Math.min(csCount, v.Can_Schduling_Count))\r\n          this.$set(v, 'csCountWeight', Math.min(v.Can_Schduling_Weight, v.csCount * v.Weight))\r\n\r\n          v.searchcount = v.count\r\n          v.searchcountMax = v.maxCount\r\n          // const cs = v.Component_Codes || []\r\n          // let min = 0\r\n          // cs.forEach((element, idx) => {\r\n          //   const [key, value] = element.split('&&&')\r\n          //   min = v.componentMap[key]\r\n          // })\r\n\r\n          v.count = v.csCount\r\n        } else {\r\n          v.count = v.Can_Schduling_Count\r\n        }\r\n\r\n        // v.Can_Schduling_Count = v.csCount\r\n        // v.Can_Schduling_Weight = v.csCountWeight\r\n\r\n        return true\r\n      })\r\n\r\n      !page && (this.pageInfo.page = 1)\r\n      this.pageInfo.total = temTbData.length\r\n      this.setPage(temTbData)\r\n      if (this.searchContent.trim().length) {\r\n        this.showSc = true\r\n      }\r\n    },\r\n    handleSearch() {\r\n      this.totalSelection = []\r\n      this.clearSelect()\r\n      if (this.tbData?.length) {\r\n        this.tbData.forEach(item => item.checked = false)\r\n        this.filterData()\r\n      }\r\n      this.showSc = !!this.searchContent.trim().length\r\n    },\r\n    tbSelectChange(array) {\r\n      this.totalSelection = this.tbData.filter(v => v.checked)\r\n    },\r\n    clearSelect() {\r\n      this.$refs.xTable1.clearCheckboxRow()\r\n      this.totalSelection = []\r\n    },\r\n    async fetchData() {\r\n      this.handleReset()\r\n      this.tbLoading = true\r\n      if (this.isCom) {\r\n        await this.getComTbData()\r\n      } else {\r\n        await this.getPartTbData()\r\n      }\r\n      this.initTbData()\r\n      this.filterData()\r\n      this.tbLoading = false\r\n    },\r\n    setPageData() {\r\n      if (this.tbData?.length) {\r\n        this.pageInfo.page = 1\r\n        this.tbData = this.tbData.filter(v => v.Can_Schduling_Count > 0)\r\n        this.filterData()\r\n      }\r\n    },\r\n    handleSave(type = 2) {\r\n      if (type === 1) {\r\n        this.addLoading = true\r\n      } else {\r\n        this.saveLoading = true\r\n      }\r\n      setTimeout(() => {\r\n        this.totalSelection.forEach((item) => {\r\n          const intCount = parseInt(item.count)\r\n          if (this.searchContent.trim().length) {\r\n            item.Schduled_Count = item.Can_Schduling_Count\r\n\r\n            item.maxCount = item.Can_Schduling_Count\r\n            item.chooseCount = intCount\r\n            item.count = item.Can_Schduling_Count\r\n\r\n            item.Can_Schduling_Count = 0\r\n            item.Can_Schduling_Weight = item.Can_Schduling_Count * item.Weight\r\n          } else {\r\n            item.Schduled_Count += intCount\r\n            item.Can_Schduling_Count -= intCount\r\n            item.Can_Schduling_Weight = item.Can_Schduling_Count * item.Weight\r\n            item.maxCount = intCount\r\n            item.chooseCount = intCount\r\n            item.count = item.Can_Schduling_Count\r\n          }\r\n\r\n          item.checked = false\r\n        })\r\n        const cp = deepClone(this.totalSelection)\r\n\r\n        // this.$emit('sendSelectList', cp)\r\n        this.addLoading = false\r\n        this.clearSelect()\r\n        // this.setPage()\r\n        this.setPageData()\r\n        if (type === 2) {\r\n          this.$emit('sendSelectList', cp)\r\n          this.$emit('close')\r\n          this.fTable = []\r\n          this.tbData = []\r\n        } else {\r\n          this.$emit('addToTbList', cp)\r\n        }\r\n      }, 0)\r\n    },\r\n    initTbData() {\r\n      // 设置文本框选择的排产数量,设置自定义唯一码\r\n      const objKey = {}\r\n      if (!this.tbData?.length) {\r\n        this.tbData = []\r\n        // this.backendTb = []\r\n        return\r\n      }\r\n      console.log(998, JSON.parse(JSON.stringify(this.tbData)))\r\n      // this.backendTb = deepClone(this.tbData)\r\n      this.tbData = this.tbData.filter(item => {\r\n        this.$set(item, 'count', item.Can_Schduling_Count)\r\n        this.$set(item, 'maxCount', item.Can_Schduling_Count)\r\n        item.uuid = getUnique(this.isCom, item)\r\n        objKey[item.Type] = true\r\n        // let csCount = 0\r\n        // item.componentMap = (item.Component_Codes || []).reduce((acc, code) => {\r\n        //   const [key, value] = code.split('&&&')\r\n        //   acc[key] = parseInt(value)\r\n        //   csCount += parseInt(value)\r\n        //   return acc\r\n        // }, {})\r\n        // this.$set(item, 'csCount', csCount)\r\n        // Object.keys(item.componentMap).forEach(key => {\r\n        //   this.$set(item, key, item.componentMap[key])\r\n        // })\r\n\r\n        return !this.addTbKeys.includes(item.uuid)\r\n      })\r\n      //   .map((item) => {\r\n      //   this.$set(item, 'count', item.Can_Schduling_Count)\r\n      //   this.$set(item, 'maxCount', item.Can_Schduling_Count)\r\n      //   // item.uuid = uuidv4()\r\n      //   item.uuid = item.InstallUnit_Id + item.Part_Aggregate_Id\r\n      //   objKey[item.Type] = true\r\n      //\r\n      //   const _selectList = this.selectTbData.filter(v => v.puuid)\r\n      //   console.log('_selectList', _selectList)\r\n      //   // _selectList.forEach((element, idx) => {\r\n      //   //   if(element.puuid === item.uuid){\r\n      //   //\r\n      //   //   }\r\n      //   // })\r\n      //   return item\r\n      // })\r\n\r\n      // this.backendTb = deepClone(this.tbData)\r\n    },\r\n    async getComTbData() {\r\n      // const { install, areaId } = this.$route.query\r\n      const { Comp_Codes, ...obj } = this.form\r\n      let codes = []\r\n      if (Object.prototype.toString.call(Comp_Codes) === '[object String]') {\r\n        codes = Comp_Codes && Comp_Codes.split(' ').filter(v => !!v)\r\n      }\r\n      await GetCanSchdulingComps({\r\n        Ids: this.currentIds,\r\n        ...obj,\r\n        Schduling_Plan_Id: this.scheduleId,\r\n        Comp_Codes: codes,\r\n        InstallUnit_Id: this.installId,\r\n        Area_Id: this.areaId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.pageInfo.total = res.Data.length\r\n          this.tbData = res.Data.map((v, idx) => {\r\n            // 已排产赋值\r\n            v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n            v.Workshop_Id = v.Scheduled_Workshop_Id\r\n            v.Workshop_Name = v.Scheduled_Workshop_Name\r\n            v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n            // if (v.originalPath) {\r\n            // v.isDisabled = true\r\n            // }\r\n            v.checked = false\r\n            v.initRowIndex = idx\r\n            v.Area_Name = this.nodeLabels.join('/')\r\n\r\n            // v.technologyPathDisabled = !!v.Technology_Path\r\n            return v\r\n          })\r\n          this.setPage()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 分页\r\n     */\r\n    handlePageChange({ currentPage, pageSize }) {\r\n      if (this.tbLoading) return\r\n      this.pageInfo.page = currentPage\r\n      this.pageInfo.pageSize = pageSize\r\n      this.setPage()\r\n      this.filterData(currentPage)\r\n    },\r\n\r\n    setPage(tb = this.tbData) {\r\n      this.fTable = tb.slice((this.pageInfo.page - 1) * this.pageInfo.pageSize, this.pageInfo.page * this.pageInfo.pageSize)\r\n    },\r\n\r\n    async getPartTbData() {\r\n      // const { install, areaId } = this.$route.query\r\n      await GetCanSchdulingParts({\r\n        Ids: this.currentIds,\r\n        ...this.form,\r\n        Schduling_Plan_Id: this.scheduleId,\r\n        InstallUnit_Id: this.installId,\r\n        Area_Id: this.areaId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.pageInfo.total = res.Data.length\r\n          this.tbData = res.Data.map((v, idx) => {\r\n            v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n            v.Workshop_Id = v.Scheduled_Workshop_Id\r\n            v.Workshop_Name = v.Scheduled_Workshop_Name\r\n            if (v.Comp_Import_Detail_Id) {\r\n              v.Part_Used_Process = this.getPartUsedProcess(v)\r\n            }\r\n            v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n            // v.isDisabled = !!v.originalPath\r\n            v.checked = false\r\n            v.initRowIndex = idx\r\n            // v.partUsedProcessDisabled = this.isPartPrepare ? !!v.Part_Used_Process : false\r\n            // v.technologyPathDisabled = !!v.Technology_Path\r\n            if (!this.isPartPrepare) {\r\n              v.Temp_Part_Used_Process = v.Part_Used_Process\r\n            }\r\n            return v\r\n          })\r\n          this.setPartColumn()\r\n          this.setPage()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n\r\n      const submitObj = this.tbData.map(item => {\r\n        return {\r\n          Id: item.Part_Aggregate_Id,\r\n          Type: 1\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = !!item.Is_Stop\r\n          })\r\n          this.tbData.forEach(row => {\r\n            if (stopMap.hasOwnProperty(row.Part_Aggregate_Id)) {\r\n              this.$set(row, 'stopFlag', stopMap[row.Part_Aggregate_Id])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    checkCheckboxMethod({ row }) {\r\n      return !row.stopFlag\r\n    },\r\n    getPartUsedProcess(item) {\r\n      if (item.Scheduled_Used_Process) {\r\n        return item.Scheduled_Used_Process\r\n      }\r\n      if (item.Component_Technology_Path) {\r\n        const list = item.Component_Technology_Path.split('/')\r\n        if (list.includes(item.Part_Used_Process)) {\r\n          return item.Part_Used_Process\r\n        } else if (list.includes(item.Part_Type_Used_Process)) {\r\n          return item.Part_Type_Used_Process\r\n        }\r\n      } else {\r\n        if (item.Part_Used_Process) {\r\n          return item.Part_Used_Process\r\n        } else if (item.Part_Type_Used_Process) {\r\n          return item.Part_Type_Used_Process\r\n        }\r\n      }\r\n\r\n      return ''\r\n    },\r\n    setPartColumn() {\r\n      // 纯零件\r\n      this.isOwnerNull = this.tbData.every(v => !v.Comp_Import_Detail_Id)\r\n      console.log('this.isOwnerNull', this.isOwnerNull)\r\n      if (this.isOwnerNull) {\r\n        const idx = this.columns.findIndex(v => v.Code === 'Component_Code')\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n      }\r\n    },\r\n    mergeData(list) {\r\n      /*      console.log('list', list)\r\n      console.log('this.backendTb', this.backendTb)\r\n      list\r\n        .forEach((element, index) => {\r\n          const idx = this.backendTb.findIndex(\r\n            (item) => element.puuid && item.uuid === element.puuid\r\n          )\r\n          console.log('idx', idx, this.backendTb[idx])\r\n          console.log('index', index)\r\n          if (idx !== -1) {\r\n            this.tbData.splice(idx, 0, deepClone(this.backendTb[idx]))\r\n          }\r\n        })\r\n\r\n      this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n      console.log('this.tbData', JSON.parse(JSON.stringify(this.tbData)))\r\n\r\n      this.filterData()*/\r\n    },\r\n    handleClose() {\r\n      this.$emit('close')\r\n    },\r\n    // activeCellMethod({ row, column, columnIndex }) {\r\n    //   return column.field === 'Schduling_Count'\r\n    // },\r\n    async getTableConfig(code) {\r\n      await GetGridByCode({\r\n        code\r\n      }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n          this.pageInfo.pageSize = Number(this.tbConfig.Row_Number)\r\n          const list = Data.ColumnList || []\r\n          this.columns = list.filter(v => v.Is_Display)\r\n            .map(item => {\r\n              if (item.Is_Frozen) {\r\n                item.fixed = 'left'\r\n              }\r\n              return item\r\n            })\r\n          // this.columns.push({\r\n          //   Display_Name: '排产数量',\r\n          //   Code: 'Schduling_Count'\r\n          // })\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getObjectTypeList() {\r\n      GetCompTypeTree({ professional: 'Steel' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          // this.ObjectTypeList.data = res.Data\r\n          this.$nextTick((_) => {\r\n            this.$refs.treeSelectObjectType.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.form.Type_Name = ''\r\n      this.form.Comp_Code = ''\r\n      this.form.Comp_CodeBlur = ''\r\n      this.form.Type = ''\r\n      this.form.Spec = ''\r\n      this.form.InstallUnit_Id = []\r\n      this.form.Part_CodeBlur = ''\r\n      this.form.Part_Code = ''\r\n      this.searchContent = ''\r\n      this.searchPartContent = ''\r\n      this.handleSearch()\r\n    },\r\n    getType() {\r\n      GetPartTypeList({ Part_Grade: 0 }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.typeOption = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    addToList() {\r\n      if (!this.totalSelection.length) return\r\n      this.handleSave(1)\r\n    },\r\n    getInstallUnitIdNameList(id) {\r\n      if (!this.areaId) {\r\n        this.installUnitIdList = []\r\n      } else {\r\n        GetInstallUnitIdNameList({ Area_Id: this.areaId }).then(res => {\r\n          this.installUnitIdList = res.Data || []\r\n          // if (this.installUnitIdList.length) {\r\n          //   this.form.InstallUnit_Id = [this.installUnitIdList[0].Id]\r\n          // }\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n@import \"~@/styles/mixin.scss\";\r\n.cs-divider{\r\n  margin:16px 0 0 0;\r\n}\r\n.contentBox {\r\n  height: 75vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .main-info{\r\n    display: flex;\r\n    overflow: hidden;\r\n    flex: 1;\r\n    .left{\r\n      height: 100%;\r\n      margin-right: 16px;\r\n      border: 1px solid #eee;\r\n      .cs-tag{\r\n        margin-left: 8px;\r\n        font-size: 12px;\r\n        padding:2px 4px;\r\n        border-radius: 1px;\r\n      }\r\n\r\n      .inner-wrapper {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n        padding: 16px;\r\n        border-radius: 4px;\r\n        overflow: hidden;\r\n\r\n        .tree-search {\r\n          display: flex;\r\n\r\n          .search-select {\r\n            margin-right: 8px;\r\n          }\r\n        }\r\n\r\n        .tree-x {\r\n          overflow: hidden;\r\n          margin-top: 16px;\r\n          flex: 1;\r\n\r\n          .el-tree {\r\n            height: 100%;\r\n          }\r\n        }\r\n\r\n        .cs-scroll {\r\n          overflow-y: auto;\r\n          @include scrollBar;\r\n        }\r\n\r\n      }\r\n    }\r\n    .right{\r\n      overflow: hidden;\r\n      flex: 1;\r\n      display: flex;\r\n      flex-direction: column;\r\n      border: 1px solid #eee;\r\n      padding:16px;\r\n    }\r\n\r\n  }\r\n\r\n  .button {\r\n    margin-top: 16px;\r\n    display: flex;\r\n    justify-content: end;\r\n  }\r\n\r\n  .tb-wrapper {\r\n    flex: 1 1 auto;\r\n  }\r\n\r\n  .data-info{\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-top: 16px;\r\n  }\r\n}\r\n.fourGreen {\r\n  color: #00C361;\r\n  font-style: normal;\r\n}\r\n\r\n.fourOrange {\r\n  color: #FF9400;\r\n  font-style: normal;\r\n}\r\n\r\n.fourRed {\r\n  color: #FF0000;\r\n  font-style: normal;\r\n}\r\n\r\n.cs-blue {\r\n  color: #5AC8FA;\r\n}\r\n\r\n.orangeBg{\r\n  background: rgba(255,148,0,0.1);\r\n}\r\n\r\n.redBg{\r\n  background: rgba(252,107,127,0.1);\r\n}\r\n.greenBg{\r\n  background: rgba(0, 195, 97, 0.10);\r\n}\r\n.cs-input-x{\r\n  display: flex;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuTA,SAAAA,aAAA;AACA,SAAAC,oBAAA;AACA,SAAAC,oBAAA,EAAAC,WAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AACA,SAAAC,QAAA,EAAAC,SAAA;AACA,SAAAC,aAAA;AACA,SAAAC,eAAA;AACA,SAAAC,eAAA;AACA,OAAAC,UAAA;AACA,OAAAC,iBAAA;AACA,SAAAC,wBAAA,EAAAC,sBAAA;AACA,SAAAC,SAAA;AACA,SAAAC,UAAA;AACA,SAAAC,iBAAA;AACA,SAAAC,WAAA;AACA,IAAAC,YAAA;AAEA;EACAC,UAAA;IAAAR,iBAAA,EAAAA,iBAAA;IAAAD,UAAA,EAAAA;EAAA;EACAU,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,QAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAE,UAAA;MACAJ,IAAA,EAAAK,OAAA;MACAH,OAAA;IACA;IAEAI,SAAA;MACAN,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAK,UAAA;MACAP,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IAEAM,aAAA;MACAR,IAAA,EAAAK,OAAA;MACAH,OAAA;IACA;IACAO,OAAA;MACAT,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAQ,QAAA;MACAV,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAS,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;QACAC,IAAA;QACAC,QAAA;QACAC,SAAA,EAAA9B,aAAA;QACA+B,KAAA;MACA;MACAC,IAAA;QACAC,SAAA;QACAC,aAAA;QACAC,aAAA;QACAC,SAAA;QACAC,SAAA;QACAC,cAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACAC,SAAA;MACAC,aAAA;MACAC,UAAA;MACAC,aAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,WAAA;MACAC,WAAA;MACAC,UAAA;MACAC,WAAA;MACAC,SAAA;MACAC,WAAA;MACAC,UAAA;MACAC,WAAA;MACAC,MAAA;MACAC,iBAAA;MACAC,OAAA;MACAC,MAAA;MACAC,QAAA;MACAC,UAAA;MACAC,IAAA;MACAC,cAAA;MACAC,QAAA;MACAC,MAAA,WAAAA,OAAA;QAAA;MAAA;MACAC,gBAAA;QACAC,WAAA;QACAC,SAAA;MACA;MACAC,cAAA;QACA;QACA;QACA;QACAC,WAAA;QACA3C,IAAA;QACAb,KAAA;UACAyD,QAAA;UACAC,KAAA;UACAC,KAAA;QACA;MACA;MACAC,MAAA;MACAC,UAAA;IACA;EACA;EACAC,QAAA,EAAAC,aAAA;IACAC,KAAA,WAAAA,MAAA;MACA,YAAA3D,QAAA;IACA;IACA4D,UAAA,WAAAA,WAAA;MACA,YAAA/B,WAAA,GAAApC,YAAA,QAAAmC,UAAA;IACA;EAAA,GACAtC,UAAA,4BACA;EACAuE,KAAA;IACA5D,UAAA,WAAAA,WAAA6D,QAAA;MACAA,QAAA,UAAA1B,WAAA;IACA;EACA;EACA2B,OAAA,WAAAA,QAAA,GAEA;EACAC,OAAA;IACAC,QAAA,WAAAA,SAAA;MACAC,OAAA,CAAAC,GAAA;MACA,KAAAC,MAAA;MACA,KAAAC,SAAA;MACA,KAAAC,aAAA;MACA,SAAAX,KAAA;QACA,KAAAY,iBAAA;MACA;QACA,KAAAC,OAAA;MACA;MACA,KAAA1B,MAAA,GAAAlE,QAAA,MAAA6F,SAAA;MACA,KAAAC,WAAA;IACA;IACAC,eAAA,WAAAA,gBAAAnE,IAAA;MAAA,IAAAoE,cAAA;QAAAC,KAAA;MACA,SAAAtB,MAAA,KAAA/C,IAAA,CAAAsE,EAAA;QACA;MACA;MACA,KAAAtE,IAAA,CAAAuE,WAAA,MAAAH,cAAA,GAAApE,IAAA,CAAAwE,QAAA,cAAAJ,cAAA,uBAAAA,cAAA,CAAAK,MAAA;QACA;MACA;MACA,KAAAzE,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAA0E,IAAA,MAAAnD,UAAA;QACA,KAAAoD,QAAA;UACAC,OAAA;UACAvF,IAAA;QACA;QACA,KAAAiC,WAAA,GAAAtB,IAAA,CAAAsE,EAAA;QACA;MACA;MAEA,IAAAO,OAAA,YAAAA,QAAAC,IAAA;QAAA,IAAAJ,IAAA,GAAAI,IAAA,CAAAJ,IAAA;QACAL,KAAA,CAAAtB,MAAA,GAAA2B,IAAA,CAAAJ,EAAA;QACAD,KAAA,CAAAU,SAAA,GAAAL,IAAA,CAAAM,UAAA;QACAX,KAAA,CAAA/C,WAAA,GAAA+C,KAAA,CAAAtB,MAAA;QAEA,IAAAkC,IAAA,GAAAlG,iBAAA,CAAAsF,KAAA,CAAAhC,QAAA,EAAArC,IAAA,CAAAsE,EAAA;QACAD,KAAA,CAAAa,UAAA,GAAAD,IAAA,CAAAE,MAAA,WAAAC,CAAA;UAAA,SAAAA,CAAA,CAAAb,WAAA;QAAA,GAAAc,GAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,KAAA;QAAA;;QAEA;QACA;QACA;QACAlB,KAAA,CAAAJ,SAAA;QACA;QACAI,KAAA,CAAAmB,wBAAA;MACA;MAEAX,OAAA,CAAA7E,IAAA;IACA;IACA8D,aAAA,WAAAA,cAAA;MAAA,IAAA2B,MAAA;MACA,KAAA/D,WAAA;MACA9C,sBAAA;QAAA8G,MAAA,OAAAC,MAAA,CAAAC,IAAA,CAAAtB,EAAA;QAAAjD,WAAA,OAAAA,WAAA;QAAAhC,IAAA,OAAA8D,KAAA;MAAA,GAAA0C,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA,IAAAD,GAAA,CAAApB,IAAA,CAAAD,MAAA;YACAgB,MAAA,CAAApD,QAAA;YACAoD,MAAA,CAAA/D,WAAA;YACA;UACA;UACA,IAAAsE,OAAA,GAAAF,GAAA,CAAApB,IAAA,CAAAW,GAAA,WAAAY,IAAA;YACAA,IAAA,CAAAC,YAAA;YACA,OAAAD,IAAA;UACA;UACAR,MAAA,CAAApD,QAAA,GAAA2D,OAAA;UACAtC,OAAA,CAAAC,GAAA;UACA8B,MAAA,CAAAU,MAAA;UACAV,MAAA,CAAA/D,WAAA;QACA;UACA+D,MAAA,CAAAd,QAAA;YACAC,OAAA,EAAAkB,GAAA,CAAAM,OAAA;YACA/G,IAAA;UACA;UACAoG,MAAA,CAAApD,QAAA;UACAoD,MAAA,CAAA/D,WAAA;QACA;MACA,GAAA2E,KAAA;QACAZ,MAAA,CAAA/D,WAAA;QACA+D,MAAA,CAAApD,QAAA;MACA;IACA;IACA8D,MAAA,WAAAA,OAAA;MAAA,IAAAG,MAAA;MACA,IAAAC,WAAA,YAAAA,WAAAC,IAAA;QACA9C,OAAA,CAAAC,GAAA,SAAA6C,IAAA;QACA,SAAAC,CAAA,MAAAA,CAAA,GAAAD,IAAA,CAAA/B,MAAA,EAAAgC,CAAA;UACA,IAAAR,IAAA,GAAAO,IAAA,CAAAC,CAAA;UACA,IAAA/B,IAAA,GAAAuB,IAAA,CAAAvB,IAAA;YAAAF,QAAA,GAAAyB,IAAA,CAAAzB,QAAA;UACAd,OAAA,CAAAC,GAAA,CAAAe,IAAA;UACA,IAAAA,IAAA,CAAAgC,QAAA,MAAAlC,QAAA,aAAAA,QAAA,eAAAA,QAAA,CAAAC,MAAA;YACA6B,MAAA,CAAAnC,eAAA,CAAA8B,IAAA;YACA;UACA;YACA,IAAAzB,QAAA,KAAAA,QAAA,aAAAA,QAAA,uBAAAA,QAAA,CAAAC,MAAA;cACA,OAAA8B,WAAA,CAAA/B,QAAA;YACA;UACA;QACA;MACA;MACA,OAAA+B,WAAA,MAAAlE,QAAA;IACA;IACAsE,eAAA,WAAAA,gBAAA7D,KAAA,EAAA9C,IAAA,EAAA4G,IAAA;MACA,IAAAC,GAAA,GAAA/D,KAAA,CAAAgE,KAAA,CAAA7H,YAAA;MACA,IAAA8H,QAAA,GAAAF,GAAA;MACA,IAAAG,SAAA,GAAAH,GAAA;MACA,KAAA/D,KAAA;MACA,IAAAmE,UAAA,GAAAL,IAAA,CAAAM,MAAA;MACA,IAAAC,MAAA,IAAAP,IAAA,CAAA/D,KAAA;MACA,IAAAuE,MAAA,IAAApH,IAAA,CAAA0E,IAAA,MAAAnD,UAAA;MACA,IAAA8F,KAAA;MACA,OAAAA,KAAA,GAAAT,IAAA,CAAAS,KAAA;QACAF,MAAA,MAAAG,MAAA,CAAAC,kBAAA,CAAAJ,MAAA,IAAAF,UAAA,CAAApE,KAAA;QACAuE,MAAA,MAAAE,MAAA,CAAAC,kBAAA,CAAAH,MAAA,IAAApH,IAAA,CAAA0E,IAAA,MAAAnD,UAAA;QACA0F,UAAA,GAAAA,UAAA,CAAAC,MAAA;QACAG,KAAA;MACA;MACAF,MAAA,GAAAA,MAAA,CAAAhC,MAAA,WAAAC,CAAA;QAAA,SAAAA,CAAA;MAAA;MACAgC,MAAA,GAAAA,MAAA,CAAAjC,MAAA,WAAAC,CAAA;QAAA,SAAAA,CAAA;MAAA;MACA,IAAAoC,WAAA;MACA,IAAAC,YAAA;MACA,SAAArG,UAAA;QACAqG,YAAA,GAAAL,MAAA,CAAAM,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,OAAA,CAAAZ,SAAA;QAAA;MACA;MACA,SAAA3F,WAAA;QACAmG,WAAA,GAAAL,MAAA,CAAAO,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,OAAA,CAAAb,QAAA;QAAA;MACA;MACA,OAAAS,WAAA,IAAAC,YAAA;IACA;IACA5D,SAAA,WAAAA,UAAA;MAAA,IAAAgE,MAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,IAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAL,IAAA;cACAA,IAAA,GAAAL,MAAA,CAAA1E,KAAA,GACA,4BACA;cAAAkF,QAAA,CAAAE,IAAA;cAAA,OACAV,MAAA,CAAAW,cAAA,CAAAN,IAAA;YAAA;YAAA;cAAA,OAAAG,QAAA,CAAAI,IAAA;UAAA;QAAA,GAAAR,OAAA;MAAA;IAEA;IACAS,UAAA,WAAAA,WAAAxI,IAAA;MAAA,IAAAyI,MAAA;MACAjF,OAAA,CAAAC,GAAA;MACA,IAAAiF,aAAA,YAAAA,cAAAC,KAAA;QAAA,OAAAA,KAAA,CAAAC,IAAA,GAAAC,OAAA,cAAAjC,KAAA;MAAA;MAEA,SAAA/F,SAAA;QACA,KAAAT,IAAA,CAAAC,SAAA,QAAAW,aAAA;QACA,KAAAZ,IAAA,CAAAE,aAAA;MACA;MACA,SAAAO,SAAA;QACA,KAAAT,IAAA,CAAAE,aAAA,QAAAU,aAAA;QACA,KAAAZ,IAAA,CAAAC,SAAA;MACA;MACA,SAAAS,aAAA;QACA,KAAAV,IAAA,CAAAG,aAAA;QACA,KAAAH,IAAA,CAAAI,SAAA,QAAAS,iBAAA;MACA;MACA,SAAAH,aAAA;QACA,KAAAV,IAAA,CAAAI,SAAA;QACA,KAAAJ,IAAA,CAAAG,aAAA,QAAAU,iBAAA;MACA;MAEA,IAAA6H,CAAA;MACA,SAAAC,OAAA,SAAA3I,IAAA;QACA,SAAAA,IAAA,CAAA2I,OAAA,UAAA3I,IAAA,CAAA2I,OAAA;UACAD,CAAA,CAAAE,IAAA,CAAAD,OAAA;QACA;MACA;MACA,KAAAD,CAAA,CAAAvE,MAAA;QACA,KAAA0E,OAAA;QACA,CAAAjJ,IAAA,UAAAD,QAAA,CAAAC,IAAA;QACA,KAAAD,QAAA,CAAAI,KAAA,QAAAuD,MAAA,CAAAa,MAAA;QACA;MACA;MAEA,IAAA2E,UAAA,YAAAA,WAAAC,MAAA,EAAAC,IAAA;QACA,IAAAC,KAAA,GAAAD,IAAA,CAAAjE,GAAA,WAAA6C,IAAA;UACA,IAAAsB,WAAA,GAAAtB,IAAA,CAAApB,KAAA;YAAA2C,YAAA,GAAAC,cAAA,CAAAF,WAAA;YAAAG,GAAA,GAAAF,YAAA;YAAA3G,KAAA,GAAA2G,YAAA;UACA,OAAAE,GAAA;QACA;QACA,IAAAC,OAAA,GAAAP,MAAA,CAAAhE,GAAA,WAAA6C,IAAA;UACA,IAAA2B,YAAA,GAAA3B,IAAA,CAAApB,KAAA;YAAAgD,YAAA,GAAAJ,cAAA,CAAAG,YAAA;YAAAF,GAAA,GAAAG,YAAA;YAAAhH,KAAA,GAAAgH,YAAA;UACA,OAAAH,GAAA;QACA;QACA,OAAAC,OAAA,CAAAlC,IAAA,WAAAzB,IAAA;UACA,OAAAsD,KAAA,CAAA7B,IAAA,WAAA5E,KAAA;YAAA,OAAAmD,IAAA,CAAA8D,QAAA,CAAAjH,KAAA;UAAA;QACA;MACA;MACA,IAAAkH,eAAA,YAAAA,gBAAAX,MAAA,EAAAC,IAAA;QACA,IAAAC,KAAA,GAAAD,IAAA,CAAAjE,GAAA,WAAA6C,IAAA;UACA,IAAA+B,YAAA,GAAA/B,IAAA,CAAApB,KAAA;YAAAoD,YAAA,GAAAR,cAAA,CAAAO,YAAA;YAAAN,GAAA,GAAAO,YAAA;YAAApH,KAAA,GAAAoH,YAAA;UACA,OAAAP,GAAA;QACA;QACA,IAAAC,OAAA,GAAAP,MAAA,CAAAhE,GAAA,WAAA6C,IAAA;UACA,IAAAiC,YAAA,GAAAjC,IAAA,CAAApB,KAAA;YAAAsD,YAAA,GAAAV,cAAA,CAAAS,YAAA;YAAAR,GAAA,GAAAS,YAAA;YAAAtH,KAAA,GAAAsH,YAAA;UACA,OAAAT,GAAA;QACA;QAEA,OAAAC,OAAA,CAAAlC,IAAA,WAAAzB,IAAA;UAAA,OAAAsD,KAAA,CAAAQ,QAAA,CAAA9D,IAAA;QAAA;MACA;MAEA,IAAAoE,SAAA,QAAAzG,MAAA,CAAAuB,MAAA,WAAAC,CAAA;QACAA,CAAA,CAAAkF,OAAA;QACA,IAAAC,QAAA,GAAAnF,CAAA,CAAAoF,eAAA;QAEA,IAAA7B,MAAA,CAAArI,IAAA,CAAAC,SAAA,CAAAuI,IAAA;UACA,IAAA2B,aAAA,GAAA7B,aAAA,CAAAD,MAAA,CAAArI,IAAA,CAAAC,SAAA;UACA,IAAAkK,aAAA,CAAAhG,MAAA;YACA,IAAAiG,IAAA,GAAAV,eAAA,CAAAO,QAAA,EAAAE,aAAA;YACA/G,OAAA,CAAAC,GAAA,MAAA4G,QAAA,EAAAE,aAAA,EAAAC,IAAA;YACA,KAAAA,IAAA;UACA;QACA;QAEA,IAAA/B,MAAA,CAAArI,IAAA,CAAAE,aAAA,CAAAsI,IAAA;UACA,IAAA2B,cAAA,GAAA7B,aAAA,CAAAD,MAAA,CAAArI,IAAA,CAAAE,aAAA;UACA,IAAAiK,cAAA,CAAAhG,MAAA;YACA,IAAAiG,KAAA,GAAAtB,UAAA,CAAAmB,QAAA,EAAAE,cAAA;YACA,KAAAC,KAAA;UACA;QACA;QAEA,IAAA/B,MAAA,CAAArI,IAAA,CAAAQ,IAAA,IAAAsE,CAAA,CAAAtE,IAAA,KAAA6H,MAAA,CAAArI,IAAA,CAAAQ,IAAA;UACA;QACA;QAEA,IAAA6H,MAAA,CAAArI,IAAA,CAAAG,aAAA,CAAAqI,IAAA;UACA,IAAA6B,iBAAA,GAAA/B,aAAA,CAAAD,MAAA,CAAArI,IAAA,CAAAG,aAAA;UACA,KAAAkK,iBAAA,CAAAjD,IAAA,WAAAQ,IAAA;YAAA,OAAA9C,CAAA,cAAA2E,QAAA,CAAA7B,IAAA;UAAA;YACA;UACA;QACA;QAEA,IAAAS,MAAA,CAAArI,IAAA,CAAAI,SAAA,CAAAoI,IAAA;UACA,IAAA8B,aAAA,GAAAhC,aAAA,CAAAD,MAAA,CAAArI,IAAA,CAAAI,SAAA;UACA,KAAAkK,aAAA,CAAAb,QAAA,CAAA3E,CAAA;YACA;UACA;QACA;QAEA,IAAAuD,MAAA,CAAArI,IAAA,CAAAM,cAAA,CAAA6D,MAAA,KAAAkE,MAAA,CAAArI,IAAA,CAAAM,cAAA,CAAAmJ,QAAA,CAAA3E,CAAA,CAAAxE,cAAA;UACA;QACA;QAEA,IAAA+H,MAAA,CAAArI,IAAA,CAAAK,SAAA,WAAAyE,CAAA,CAAAzE,SAAA,KAAAgI,MAAA,CAAArI,IAAA,CAAAK,SAAA;UACA;QACA;QAEA,IAAAgI,MAAA,CAAArI,IAAA,CAAAO,IAAA,CAAAiI,IAAA;UACA,IAAA+B,SAAA,GAAAjC,aAAA,CAAAD,MAAA,CAAArI,IAAA,CAAAO,IAAA;UACA,KAAAgK,SAAA,CAAAnD,IAAA,WAAAoD,IAAA;YAAA,OAAA1F,CAAA,CAAAvE,IAAA,CAAAkJ,QAAA,CAAAe,IAAA;UAAA;YACA;UACA;QACA;QACA,IAAAnC,MAAA,CAAAzH,aAAA,CAAA4H,IAAA,GAAArE,MAAA;UACA,IAAAsG,OAAA;UAEA3F,CAAA,CAAA4F,YAAA,IAAA5F,CAAA,CAAAoF,eAAA,QAAAS,MAAA,WAAAC,GAAA,EAAAhD,IAAA;YACA,IAAAiD,YAAA,GAAAjD,IAAA,CAAApB,KAAA;cAAAsE,YAAA,GAAA1B,cAAA,CAAAyB,YAAA;cAAAxB,GAAA,GAAAyB,YAAA;cAAAtI,KAAA,GAAAsI,YAAA;YACAF,GAAA,CAAAvB,GAAA,IAAA0B,QAAA,CAAAvI,KAAA;YACA,IAAA6F,MAAA,CAAA5H,SAAA;cACA,IAAA0J,eAAA,GAAA7B,aAAA,CAAAD,MAAA,CAAArI,IAAA,CAAAC,SAAA;cACA,IAAAkK,eAAA,CAAAhG,MAAA;gBACA,IAAAiG,MAAA,GAAAV,eAAA,EAAAL,GAAA,GAAAc,eAAA;gBACA,IAAAC,MAAA;kBACAK,OAAA,IAAAM,QAAA,CAAAvI,KAAA;gBACA;cACA;YACA;cACA,IAAA2H,eAAA,GAAA7B,aAAA,CAAAD,MAAA,CAAArI,IAAA,CAAAE,aAAA;cACA,IAAAiK,eAAA,CAAAhG,MAAA;gBACA,IAAAiG,MAAA,GAAAtB,UAAA,EAAAO,GAAA,GAAAc,eAAA;gBACA/G,OAAA,CAAAC,GAAA,UAAAgG,GAAA,EAAAc,eAAA,EAAAC,MAAA,EAAA5H,KAAA;gBACA,IAAA4H,MAAA;kBACAK,OAAA,IAAAM,QAAA,CAAAvI,KAAA;gBACA;cACA;YACA;YACA,OAAAoI,GAAA;UACA;UACAvC,MAAA,CAAA2C,IAAA,CAAAlG,CAAA,aAAAmG,IAAA,CAAAC,GAAA,CAAAT,OAAA,EAAA3F,CAAA,CAAAqG,mBAAA;UACA9C,MAAA,CAAA2C,IAAA,CAAAlG,CAAA,mBAAAmG,IAAA,CAAAC,GAAA,CAAApG,CAAA,CAAAsG,oBAAA,EAAAtG,CAAA,CAAA2F,OAAA,GAAA3F,CAAA,CAAAuG,MAAA;UAEAvG,CAAA,CAAAwG,WAAA,GAAAxG,CAAA,CAAAyG,KAAA;UACAzG,CAAA,CAAA0G,cAAA,GAAA1G,CAAA,CAAA2G,QAAA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA3G,CAAA,CAAAyG,KAAA,GAAAzG,CAAA,CAAA2F,OAAA;QACA;UACA3F,CAAA,CAAAyG,KAAA,GAAAzG,CAAA,CAAAqG,mBAAA;QACA;;QAEA;QACA;;QAEA;MACA;MAEA,CAAAvL,IAAA,UAAAD,QAAA,CAAAC,IAAA;MACA,KAAAD,QAAA,CAAAI,KAAA,GAAAgK,SAAA,CAAA5F,MAAA;MACA,KAAA0E,OAAA,CAAAkB,SAAA;MACA,SAAAnJ,aAAA,CAAA4H,IAAA,GAAArE,MAAA;QACA,KAAA5C,MAAA;MACA;IACA;IACAmK,YAAA,WAAAA,aAAA;MAAA,IAAAC,YAAA;MACA,KAAA7J,cAAA;MACA,KAAA8J,WAAA;MACA,KAAAD,YAAA,QAAArI,MAAA,cAAAqI,YAAA,eAAAA,YAAA,CAAAxH,MAAA;QACA,KAAAb,MAAA,CAAAuI,OAAA,WAAAlG,IAAA;UAAA,OAAAA,IAAA,CAAAqE,OAAA;QAAA;QACA,KAAA5B,UAAA;MACA;MACA,KAAA7G,MAAA,UAAAX,aAAA,CAAA4H,IAAA,GAAArE,MAAA;IACA;IACA2H,cAAA,WAAAA,eAAAC,KAAA;MACA,KAAAjK,cAAA,QAAAwB,MAAA,CAAAuB,MAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAkF,OAAA;MAAA;IACA;IACA4B,WAAA,WAAAA,YAAA;MACA,KAAAI,KAAA,CAAAC,OAAA,CAAAC,gBAAA;MACA,KAAApK,cAAA;IACA;IACA6B,SAAA,WAAAA,UAAA;MAAA,IAAAwI,MAAA;MAAA,OAAA3E,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA0E,SAAA;QAAA,OAAA3E,mBAAA,GAAAI,IAAA,UAAAwE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtE,IAAA,GAAAsE,SAAA,CAAArE,IAAA;YAAA;cACAkE,MAAA,CAAAI,WAAA;cACAJ,MAAA,CAAAhL,SAAA;cAAA,KACAgL,MAAA,CAAAtJ,KAAA;gBAAAyJ,SAAA,CAAArE,IAAA;gBAAA;cAAA;cAAAqE,SAAA,CAAArE,IAAA;cAAA,OACAkE,MAAA,CAAAK,YAAA;YAAA;cAAAF,SAAA,CAAArE,IAAA;cAAA;YAAA;cAAAqE,SAAA,CAAArE,IAAA;cAAA,OAEAkE,MAAA,CAAAM,aAAA;YAAA;cAEAN,MAAA,CAAAO,UAAA;cACAP,MAAA,CAAA/D,UAAA;cACA+D,MAAA,CAAAhL,SAAA;YAAA;YAAA;cAAA,OAAAmL,SAAA,CAAAnE,IAAA;UAAA;QAAA,GAAAiE,QAAA;MAAA;IACA;IACAxI,WAAA,WAAAA,YAAA;MAAA,IAAA+I,aAAA;MACA,KAAAA,aAAA,QAAArJ,MAAA,cAAAqJ,aAAA,eAAAA,aAAA,CAAAxI,MAAA;QACA,KAAAxE,QAAA,CAAAC,IAAA;QACA,KAAA0D,MAAA,QAAAA,MAAA,CAAAuB,MAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAqG,mBAAA;QAAA;QACA,KAAA/C,UAAA;MACA;IACA;IACAwE,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MAAA,IAAA9N,IAAA,GAAA+N,SAAA,CAAA3I,MAAA,QAAA2I,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,IAAA/N,IAAA;QACA,KAAAsC,UAAA;MACA;QACA,KAAAC,WAAA;MACA;MACA0L,UAAA;QACAH,MAAA,CAAA/K,cAAA,CAAA+J,OAAA,WAAAlG,IAAA;UACA,IAAAsH,QAAA,GAAAlC,QAAA,CAAApF,IAAA,CAAA4F,KAAA;UACA,IAAAsB,MAAA,CAAAjM,aAAA,CAAA4H,IAAA,GAAArE,MAAA;YACAwB,IAAA,CAAAuH,cAAA,GAAAvH,IAAA,CAAAwF,mBAAA;YAEAxF,IAAA,CAAA8F,QAAA,GAAA9F,IAAA,CAAAwF,mBAAA;YACAxF,IAAA,CAAAwH,WAAA,GAAAF,QAAA;YACAtH,IAAA,CAAA4F,KAAA,GAAA5F,IAAA,CAAAwF,mBAAA;YAEAxF,IAAA,CAAAwF,mBAAA;YACAxF,IAAA,CAAAyF,oBAAA,GAAAzF,IAAA,CAAAwF,mBAAA,GAAAxF,IAAA,CAAA0F,MAAA;UACA;YACA1F,IAAA,CAAAuH,cAAA,IAAAD,QAAA;YACAtH,IAAA,CAAAwF,mBAAA,IAAA8B,QAAA;YACAtH,IAAA,CAAAyF,oBAAA,GAAAzF,IAAA,CAAAwF,mBAAA,GAAAxF,IAAA,CAAA0F,MAAA;YACA1F,IAAA,CAAA8F,QAAA,GAAAwB,QAAA;YACAtH,IAAA,CAAAwH,WAAA,GAAAF,QAAA;YACAtH,IAAA,CAAA4F,KAAA,GAAA5F,IAAA,CAAAwF,mBAAA;UACA;UAEAxF,IAAA,CAAAqE,OAAA;QACA;QACA,IAAAoD,EAAA,GAAArP,SAAA,CAAA8O,MAAA,CAAA/K,cAAA;;QAEA;QACA+K,MAAA,CAAAxL,UAAA;QACAwL,MAAA,CAAAjB,WAAA;QACA;QACAiB,MAAA,CAAAjJ,WAAA;QACA,IAAA7E,IAAA;UACA8N,MAAA,CAAAQ,KAAA,mBAAAD,EAAA;UACAP,MAAA,CAAAQ,KAAA;UACAR,MAAA,CAAAnL,MAAA;UACAmL,MAAA,CAAAvJ,MAAA;QACA;UACAuJ,MAAA,CAAAQ,KAAA,gBAAAD,EAAA;QACA;MACA;IACA;IACAV,UAAA,WAAAA,WAAA;MAAA,IAAAY,aAAA;QAAAC,MAAA;MACA;MACA,IAAAC,MAAA;MACA,OAAAF,aAAA,QAAAhK,MAAA,cAAAgK,aAAA,eAAAA,aAAA,CAAAnJ,MAAA;QACA,KAAAb,MAAA;QACA;QACA;MACA;MACAF,OAAA,CAAAC,GAAA,MAAAoK,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAArK,MAAA;MACA;MACA,KAAAA,MAAA,QAAAA,MAAA,CAAAuB,MAAA,WAAAc,IAAA;QACA4H,MAAA,CAAAvC,IAAA,CAAArF,IAAA,WAAAA,IAAA,CAAAwF,mBAAA;QACAoC,MAAA,CAAAvC,IAAA,CAAArF,IAAA,cAAAA,IAAA,CAAAwF,mBAAA;QACAxF,IAAA,CAAAiI,IAAA,GAAArP,SAAA,CAAAgP,MAAA,CAAA1K,KAAA,EAAA8C,IAAA;QACA6H,MAAA,CAAA7H,IAAA,CAAAnF,IAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA,QAAA+M,MAAA,CAAAM,SAAA,CAAApE,QAAA,CAAA9D,IAAA,CAAAiI,IAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;IACA;IACApB,YAAA,WAAAA,aAAA;MAAA,IAAAsB,MAAA;MAAA,OAAAtG,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAqG,SAAA;QAAA,IAAAC,WAAA,EAAAC,UAAA,EAAAC,GAAA,EAAAC,KAAA;QAAA,OAAA1G,mBAAA,GAAAI,IAAA,UAAAuG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArG,IAAA,GAAAqG,SAAA,CAAApG,IAAA;YAAA;cACA;cAAA+F,WAAA,GACAF,MAAA,CAAA9N,IAAA,EAAAiO,UAAA,GAAAD,WAAA,CAAAC,UAAA,EAAAC,GAAA,GAAAI,wBAAA,CAAAN,WAAA,EAAAO,SAAA;cACAJ,KAAA;cACA,IAAAK,MAAA,CAAAC,SAAA,CAAAC,QAAA,CAAAC,IAAA,CAAAV,UAAA;gBACAE,KAAA,GAAAF,UAAA,IAAAA,UAAA,CAAAzH,KAAA,MAAA3B,MAAA,WAAAC,CAAA;kBAAA,SAAAA,CAAA;gBAAA;cACA;cAAAuJ,SAAA,CAAApG,IAAA;cAAA,OACAxK,oBAAA,CAAAmF,aAAA,CAAAA,aAAA;gBACAgM,GAAA,EAAAd,MAAA,CAAAxO;cAAA,GACA4O,GAAA;gBACAW,iBAAA,EAAAf,MAAA,CAAAhP,UAAA;gBACAmP,UAAA,EAAAE,KAAA;gBACA7N,cAAA,EAAAwN,MAAA,CAAAzO,SAAA;gBACAyP,OAAA,EAAAhB,MAAA,CAAArL;cAAA,EACA,EAAA8C,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAqI,MAAA,CAAAnO,QAAA,CAAAI,KAAA,GAAAyF,GAAA,CAAApB,IAAA,CAAAD,MAAA;kBACA2J,MAAA,CAAAxK,MAAA,GAAAkC,GAAA,CAAApB,IAAA,CAAAW,GAAA,WAAAD,CAAA,EAAAiK,GAAA;oBACA;oBACAjK,CAAA,CAAAkK,YAAA,GAAAlK,CAAA,CAAAmK,yBAAA,GAAAnK,CAAA,CAAAmK,yBAAA;oBACAnK,CAAA,CAAAoK,WAAA,GAAApK,CAAA,CAAAqK,qBAAA;oBACArK,CAAA,CAAAsK,aAAA,GAAAtK,CAAA,CAAAuK,uBAAA;oBACAvK,CAAA,CAAAwK,eAAA,GAAAxK,CAAA,CAAAmK,yBAAA,IAAAnK,CAAA,CAAAwK,eAAA;oBACA;oBACA;oBACA;oBACAxK,CAAA,CAAAkF,OAAA;oBACAlF,CAAA,CAAAyK,YAAA,GAAAR,GAAA;oBACAjK,CAAA,CAAA0K,SAAA,GAAA1B,MAAA,CAAAlJ,UAAA,CAAA6K,IAAA;;oBAEA;oBACA,OAAA3K,CAAA;kBACA;kBACAgJ,MAAA,CAAAjF,OAAA;gBACA;kBACAiF,MAAA,CAAAzJ,QAAA;oBACAC,OAAA,EAAAkB,GAAA,CAAAM,OAAA;oBACA/G,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAsP,SAAA,CAAAlG,IAAA;UAAA;QAAA,GAAA4F,QAAA;MAAA;IACA;IACA;AACA;AACA;IACA2B,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAC,WAAA,GAAAD,KAAA,CAAAC,WAAA;QAAA/P,QAAA,GAAA8P,KAAA,CAAA9P,QAAA;MACA,SAAAsB,SAAA;MACA,KAAAxB,QAAA,CAAAC,IAAA,GAAAgQ,WAAA;MACA,KAAAjQ,QAAA,CAAAE,QAAA,GAAAA,QAAA;MACA,KAAAgJ,OAAA;MACA,KAAAT,UAAA,CAAAwH,WAAA;IACA;IAEA/G,OAAA,WAAAA,QAAA;MAAA,IAAAgH,EAAA,GAAA/C,SAAA,CAAA3I,MAAA,QAAA2I,SAAA,QAAAC,SAAA,GAAAD,SAAA,WAAAxJ,MAAA;MACA,KAAA5B,MAAA,GAAAmO,EAAA,CAAAC,KAAA,OAAAnQ,QAAA,CAAAC,IAAA,aAAAD,QAAA,CAAAE,QAAA,OAAAF,QAAA,CAAAC,IAAA,QAAAD,QAAA,CAAAE,QAAA;IACA;IAEA4M,aAAA,WAAAA,cAAA;MAAA,IAAAsD,MAAA;MAAA,OAAAvI,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAsI,SAAA;QAAA,IAAAC,SAAA;QAAA,OAAAxI,mBAAA,GAAAI,IAAA,UAAAqI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnI,IAAA,GAAAmI,SAAA,CAAAlI,IAAA;YAAA;cAAAkI,SAAA,CAAAlI,IAAA;cAAA,OAEAvK,oBAAA,CAAAkF,aAAA,CAAAA,aAAA;gBACAgM,GAAA,EAAAmB,MAAA,CAAAzQ;cAAA,GACAyQ,MAAA,CAAA/P,IAAA;gBACA6O,iBAAA,EAAAkB,MAAA,CAAAjR,UAAA;gBACAwB,cAAA,EAAAyP,MAAA,CAAA1Q,SAAA;gBACAyP,OAAA,EAAAiB,MAAA,CAAAtN;cAAA,EACA,EAAA8C,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAsK,MAAA,CAAApQ,QAAA,CAAAI,KAAA,GAAAyF,GAAA,CAAApB,IAAA,CAAAD,MAAA;kBACA4L,MAAA,CAAAzM,MAAA,GAAAkC,GAAA,CAAApB,IAAA,CAAAW,GAAA,WAAAD,CAAA,EAAAiK,GAAA;oBACAjK,CAAA,CAAAkK,YAAA,GAAAlK,CAAA,CAAAmK,yBAAA,GAAAnK,CAAA,CAAAmK,yBAAA;oBACAnK,CAAA,CAAAoK,WAAA,GAAApK,CAAA,CAAAqK,qBAAA;oBACArK,CAAA,CAAAsK,aAAA,GAAAtK,CAAA,CAAAuK,uBAAA;oBACA,IAAAvK,CAAA,CAAAsL,qBAAA;sBACAtL,CAAA,CAAAuL,iBAAA,GAAAN,MAAA,CAAAO,kBAAA,CAAAxL,CAAA;oBACA;oBACAA,CAAA,CAAAwK,eAAA,GAAAxK,CAAA,CAAAmK,yBAAA,IAAAnK,CAAA,CAAAwK,eAAA;oBACA;oBACAxK,CAAA,CAAAkF,OAAA;oBACAlF,CAAA,CAAAyK,YAAA,GAAAR,GAAA;oBACA;oBACA;oBACA,KAAAgB,MAAA,CAAAxQ,aAAA;sBACAuF,CAAA,CAAAyL,sBAAA,GAAAzL,CAAA,CAAAuL,iBAAA;oBACA;oBACA,OAAAvL,CAAA;kBACA;kBACAiL,MAAA,CAAAS,aAAA;kBACAT,MAAA,CAAAlH,OAAA;gBACA;kBACAkH,MAAA,CAAA1L,QAAA;oBACAC,OAAA,EAAAkB,GAAA,CAAAM,OAAA;oBACA/G,IAAA;kBACA;gBACA;cACA;YAAA;cAEAkR,SAAA,GAAAF,MAAA,CAAAzM,MAAA,CAAAyB,GAAA,WAAAY,IAAA;gBACA;kBACA3B,EAAA,EAAA2B,IAAA,CAAA8K,iBAAA;kBACAjQ,IAAA;gBACA;cACA;cAAA2P,SAAA,CAAAlI,IAAA;cAAA,OACAvJ,WAAA,CAAAuR,SAAA,EAAA1K,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACA,IAAAiL,OAAA;kBACAlL,GAAA,CAAApB,IAAA,CAAAyH,OAAA,WAAAlG,IAAA;oBACA+K,OAAA,CAAA/K,IAAA,CAAA3B,EAAA,MAAA2B,IAAA,CAAAgL,OAAA;kBACA;kBACAZ,MAAA,CAAAzM,MAAA,CAAAuI,OAAA,WAAA+E,GAAA;oBACA,IAAAF,OAAA,CAAAG,cAAA,CAAAD,GAAA,CAAAH,iBAAA;sBACAV,MAAA,CAAA/E,IAAA,CAAA4F,GAAA,cAAAF,OAAA,CAAAE,GAAA,CAAAH,iBAAA;oBACA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAN,SAAA,CAAAhI,IAAA;UAAA;QAAA,GAAA6H,QAAA;MAAA;IACA;IACAc,mBAAA,WAAAA,oBAAAC,KAAA;MAAA,IAAAH,GAAA,GAAAG,KAAA,CAAAH,GAAA;MACA,QAAAA,GAAA,CAAAI,QAAA;IACA;IACAV,kBAAA,WAAAA,mBAAA3K,IAAA;MACA,IAAAA,IAAA,CAAAsL,sBAAA;QACA,OAAAtL,IAAA,CAAAsL,sBAAA;MACA;MACA,IAAAtL,IAAA,CAAAuL,yBAAA;QACA,IAAAC,IAAA,GAAAxL,IAAA,CAAAuL,yBAAA,CAAA1K,KAAA;QACA,IAAA2K,IAAA,CAAA1H,QAAA,CAAA9D,IAAA,CAAA0K,iBAAA;UACA,OAAA1K,IAAA,CAAA0K,iBAAA;QACA,WAAAc,IAAA,CAAA1H,QAAA,CAAA9D,IAAA,CAAAyL,sBAAA;UACA,OAAAzL,IAAA,CAAAyL,sBAAA;QACA;MACA;QACA,IAAAzL,IAAA,CAAA0K,iBAAA;UACA,OAAA1K,IAAA,CAAA0K,iBAAA;QACA,WAAA1K,IAAA,CAAAyL,sBAAA;UACA,OAAAzL,IAAA,CAAAyL,sBAAA;QACA;MACA;MAEA;IACA;IACAZ,aAAA,WAAAA,cAAA;MACA;MACA,KAAAtP,WAAA,QAAAoC,MAAA,CAAA+N,KAAA,WAAAvM,CAAA;QAAA,QAAAA,CAAA,CAAAsL,qBAAA;MAAA;MACAhN,OAAA,CAAAC,GAAA,0BAAAnC,WAAA;MACA,SAAAA,WAAA;QACA,IAAA6N,GAAA,QAAAtN,OAAA,CAAA6P,SAAA,WAAAxM,CAAA;UAAA,OAAAA,CAAA,CAAAyM,IAAA;QAAA;QACAxC,GAAA,gBAAAtN,OAAA,CAAA+P,MAAA,CAAAzC,GAAA;MACA;IACA;IACA0C,SAAA,WAAAA,UAAAN,IAAA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;IAfA,CAkBA;IACAO,WAAA,WAAAA,YAAA;MACA,KAAArE,KAAA;IACA;IACA;IACA;IACA;IACAnF,cAAA,WAAAA,eAAAN,IAAA;MAAA,IAAA+J,MAAA;MAAA,OAAAnK,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkK,SAAA;QAAA,OAAAnK,mBAAA,GAAAI,IAAA,UAAAgK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9J,IAAA,GAAA8J,SAAA,CAAA7J,IAAA;YAAA;cAAA6J,SAAA,CAAA7J,IAAA;cAAA,OACAzK,aAAA;gBACAoK,IAAA,EAAAA;cACA,GAAArC,IAAA,WAAAC,GAAA;gBACA,IAAAC,SAAA,GAAAD,GAAA,CAAAC,SAAA;kBAAArB,IAAA,GAAAoB,GAAA,CAAApB,IAAA;kBAAA0B,OAAA,GAAAN,GAAA,CAAAM,OAAA;gBACA,IAAAL,SAAA;kBACAkM,MAAA,CAAAhQ,QAAA,GAAA6M,MAAA,CAAAuD,MAAA,KAAAJ,MAAA,CAAAhQ,QAAA,EAAAyC,IAAA,CAAA4N,IAAA;kBACAL,MAAA,CAAAhS,QAAA,CAAAE,QAAA,GAAAoS,MAAA,CAAAN,MAAA,CAAAhQ,QAAA,CAAAuQ,UAAA;kBACA,IAAAf,IAAA,GAAA/M,IAAA,CAAA+N,UAAA;kBACAR,MAAA,CAAAlQ,OAAA,GAAA0P,IAAA,CAAAtM,MAAA,WAAAC,CAAA;oBAAA,OAAAA,CAAA,CAAAsN,UAAA;kBAAA,GACArN,GAAA,WAAAY,IAAA;oBACA,IAAAA,IAAA,CAAA0M,SAAA;sBACA1M,IAAA,CAAA2M,KAAA;oBACA;oBACA,OAAA3M,IAAA;kBACA;kBACA;kBACA;kBACA;kBACA;gBACA;kBACAgM,MAAA,CAAAtN,QAAA;oBACAC,OAAA,EAAAwB,OAAA;oBACA/G,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA+S,SAAA,CAAA3J,IAAA;UAAA;QAAA,GAAAyJ,QAAA;MAAA;IACA;IACAnO,iBAAA,WAAAA,kBAAA;MAAA,IAAA8O,OAAA;MACAtU,eAAA;QAAAuU,YAAA;MAAA,GAAAjN,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA;UACA8M,OAAA,CAAAE,SAAA,WAAAC,CAAA;YACAH,OAAA,CAAAvG,KAAA,CAAA2G,oBAAA,CAAAC,iBAAA,CAAApN,GAAA,CAAApB,IAAA;UACA;QACA;UACAmO,OAAA,CAAAlO,QAAA;YACAtF,IAAA;YACAuF,OAAA,EAAAkB,GAAA,CAAAM;UACA;QACA;MACA;IACA;IACAyG,WAAA,WAAAA,YAAA;MACA,KAAAvM,IAAA,CAAAK,SAAA;MACA,KAAAL,IAAA,CAAAC,SAAA;MACA,KAAAD,IAAA,CAAAE,aAAA;MACA,KAAAF,IAAA,CAAAQ,IAAA;MACA,KAAAR,IAAA,CAAAO,IAAA;MACA,KAAAP,IAAA,CAAAM,cAAA;MACA,KAAAN,IAAA,CAAAG,aAAA;MACA,KAAAH,IAAA,CAAAI,SAAA;MACA,KAAAQ,aAAA;MACA,KAAAC,iBAAA;MACA,KAAA6K,YAAA;IACA;IACAhI,OAAA,WAAAA,QAAA;MAAA,IAAAmP,OAAA;MACA3U,eAAA;QAAA4U,UAAA;MAAA,GAAAvN,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAoN,OAAA,CAAAnQ,UAAA,GAAA8C,GAAA,CAAApB,IAAA;QACA;UACAyO,OAAA,CAAAxO,QAAA;YACAC,OAAA,EAAAkB,GAAA,CAAAM,OAAA;YACA/G,IAAA;UACA;QACA;MACA;IACA;IACAgU,SAAA,WAAAA,UAAA;MACA,UAAAjR,cAAA,CAAAqC,MAAA;MACA,KAAAyI,UAAA;IACA;IACA1H,wBAAA,WAAAA,yBAAA8N,EAAA;MAAA,IAAAC,OAAA;MACA,UAAAxQ,MAAA;QACA,KAAAjB,iBAAA;MACA;QACAnD,wBAAA;UAAAyQ,OAAA,OAAArM;QAAA,GAAA8C,IAAA,WAAAC,GAAA;UACAyN,OAAA,CAAAzR,iBAAA,GAAAgE,GAAA,CAAApB,IAAA;UACA;UACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
import request from '@/utils/request'

// 质检类型
export function GetDictionaryDetailListByCode(data) {
  return request({
    url: '/SYS/Dictionary/GetDictionaryDetailListByCode',
    method: 'post',
    data
  })
}
// 获取检查类型列表
export function EntityCheckType(data) {
  return request({
    url: '/PRO/Inspection/EntityCheckType',
    method: 'post',
    data
  })
}

// 获取检查类型列表
export function GetCheckTypeList(data) {
  return request({
    url: '/PRO/Inspection/GetCheckTypeList',
    method: 'post',
    data
  })
}

// 新增检查类型
export function AddCheckType(data) {
  return request({
    url: '/PRO/Inspection/AddCheckType',
    method: 'post',
    data
  })
}

// 删除检查类型
export function DeleteCheckType(data) {
  return request({
    url: '/PRO/Inspection/DeleteCheckType',
    method: 'post',
    data
  })
}

// 编辑检查类型
export function SaveCheckType(data) {
  return request({
    url: '/PRO/Inspection/SaveCheckType',
    method: 'post',
    data
  })
}

// 获取检查项列表
export function GetCheckItemList(data) {
  return request({
    url: '/PRO/Inspection/GetCheckItemList',
    method: 'post',
    data
  })
}

// 删除检查项信息
export function DeleteCheckItem(data) {
  return request({
    url: '/PRO/Inspection/DeleteCheckItem',
    method: 'post',
    data
  })
}

// 新增检查项信息
export function AddCheckItem(data) {
  return request({
    url: '/PRO/Inspection/AddCheckItem',
    method: 'post',
    data
  })
}

// 获取检查项详情
export function EntityCheckItem(data) {
  return request({
    url: '/PRO/Inspection/EntityCheckItem',
    method: 'post',
    data
  })
}

// 编辑检查项信息
export function SaveCheckItem(data) {
  return request({
    url: '/PRO/Inspection/SaveCheckItem',
    method: 'post',
    data
  })
}

// 获取检查项组合
export function GetCheckGroupList(data) {
  return request({
    url: '/PRO/Inspection/GetCheckGroupList',
    method: 'post',
    data
  })
}

// 新增检查项组合
export function AddCheckItemCombination(data) {
  return request({
    url: '/PRO/Inspection/AddCheckItemCombination',
    method: 'post',
    data
  })
}

// 获取项目专业类别
export function GetProEntities(data) {
  return request({
    url: '/PLM/Plm_Professional_Type/GetEntities',
    method: 'post',
    data
  })
}

// 获取人员列表
export function GetFactoryPeoplelist(data) {
  return request({
    url: '/PRO/Factory/GetFactoryPeoplelist',
    method: 'post',
    data
  })
}

// 获取工序节点
export function GetProcessCodeList(data) {
  return request({
    url: '/PRO/TechnologyLib/GetProcessCodeList',
    method: 'post',
    data
  })
}

// 获取检查项组合列表
export function QualityList(data) {
  return request({
    url: '/PRO/Inspection/QualityList',
    method: 'post',
    data
  })
}

// 通过Id获取检查项
export function EntityQualityList(data) {
  return request({
    url: '/PRO/Inspection/EntityQualityList',
    method: 'post',
    data
  })
}

// 删除检查项
export function DelQualityList(data) {
  return request({
    url: '/PRO/Inspection/DelQualityList',
    method: 'post',
    data
  })
}

// 获取质检节点列表
export function GetNodeList(data) {
  return request({
    url: '/PRO/Inspection/GetNodeList',
    method: 'post',
    data
  })
}

// 根据Id获取质检节点
export function GetEntityNode(data) {
  return request({
    url: '/PRO/Inspection/EntityNode',
    method: 'post',
    data
  })
}

// 删除质检节点
export function DelNode(data) {
  return request({
    url: '/PRO/Inspection/DelNode',
    method: 'post',
    data
  })
}

// 保存质检节点配置
export function SaveNode(data) {
  return request({
    url: '/PRO/Inspection/SaveNode',
    method: 'post',
    data
  })
}

// 获取对象类型列表
export function GetCompTypeTree(data) {
  return request({
    url: '/PRO/ComponentType/GetCompTypeTree',
    method: 'post',
    data
  })
}

// 获取专业类别
export function GetFactoryProfessionalByCode(data) {
  return request({
    url: '/PRO/ProfessionalType/GetFactoryProfessionalByCode',
    method: 'post',
    data
  })
}

// 获取物料对象类型
export function GetMaterialType(data) {
  return request({
    url: '/PRO/Inspection/GetMaterialType',
    method: 'post',
    data
  })
}

export function ExportInspsectionSummaryInfo(data) {
  return request({
    url: '/PRO/Inspection/ExportInspsectionSummaryInfo',
    method: 'post',
    data
  })
}

// 恢复工厂默认质检配置
export function RestoreFactoryQualityFromProject(data) {
  return request({
    url: '/PRO/Inspection/RestoreFactoryQualityFromProject',
    method: 'post',
    data
  })
}

<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-width="80px" style="width: 100%">
      <!--      <el-form-item label="服务工厂" prop="Factory_Id">
        <el-select v-model="form.Factory_Id" class="w100" multiple placeholder="请选择" clearable="">
          <el-option
            v-for="item in factory"
            :key="item.Id"
            :label="item.Name"
            :value="item.Id"
          />
        </el-select>
      </el-form-item>-->
      <el-form-item label="船号" prop="Shipnumber">
        <el-input v-model="form.Shipnumber" clearable @change="getLicense" />
      </el-form-item>
      <el-form-item label="船长" prop="Captain">
        <el-input v-model="form.Captain" clearable />
      </el-form-item>
      <el-form-item label="电话" prop="Mobile">
        <el-input v-model="form.Mobile" clearable />
      </el-form-item>
      <el-form-item label="运输单位" prop="Unit">
        <el-input v-model="form.Unit" clearable />
      </el-form-item>
      <el-form-item style="text-align: right">
        <el-button @click="$emit('close')">取 消</el-button>
        <!-- <el-button v-if="showDelete" type="danger" @click="handleDelete">删 除</el-button> -->
        <el-button type="primary" :loading="btnLoading" @click="handleSubmit">确 定</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { DeleteCar, SaveBoat } from '@/api/PRO/car'
import { GetFactoryList } from '@/api/PRO/factory'

export default {
  data() {
    return {
      btnLoading: false,
      factory: [],
      form: {
       Shipnumber: '',
       Captain: '',
       Mobile: '',
       Unit: '',

      },
      rules: {
  
        Shipnumber: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        Captain:[
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        Mobile: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
       
      }
    }
  },
  computed: {
    // showDelete() {
    //   return this.form.Id !== undefined
    // }
  },
  mounted() {
    this.init()
    console.log(this.form.Id)
  },
  methods: {
    init() {
      GetFactoryList({}).then(res => {
        if (res.IsSucceed) {
          this.factory = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    getLicense(v) {
      this.form.License = v.replace(/([a-zA-Z])/g, function(v) {
        return v.toUpperCase()
      })
    },
    editInit(row) {
      console.log(row)
      const { Shipnumber,Captain, Mobile, Id ,Unit} = row
      // this.form.Factory_Id = Factory_Id
      this.form.Shipnumber = Shipnumber
      this.form.Captain = Captain
      this.form.Unit = Unit
      this.form.Mobile = Mobile
      this.$set(this.form, 'Id', Id)
    },
    handleSubmit() {
      this.$refs['form'].validate((valid) => {
        if (!valid) return
        this.btnLoading = true
        SaveBoat(this.form).then(res => {
          if (res.IsSucceed) {
            this.$message({
              message: '操作成功',
              type: 'success'
            })
            this.$emit('close')
            this.$emit('refresh')
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
          this.btnLoading = false
        })
      })
    }
    // handleDelete() {
    //   this.$confirm('是否删除该车辆?', '提示', {
    //     confirmButtonText: '确定',
    //     cancelButtonText: '取消',
    //     type: 'warning'
    //   }).then(() => {
    //     DeleteCar({
    //       id: this.form.Id
    //     }).then(res => {
    //       if (res.IsSucceed) {
    //         this.$message({
    //           type: 'success',
    //           message: '删除成功!'
    //         })
    //         this.$emit('close')
    //         this.$emit('refresh')
    //       } else {
    //         this.$message({
    //           message: res.Message,
    //           type: 'error'
    //         })
    //       }
    //     })
    //   }).catch(() => {
    //     this.$message({
    //       type: 'info',
    //       message: '已取消删除'
    //     })
    //   })
    // }
  }
}
</script>

<style scoped>

</style>

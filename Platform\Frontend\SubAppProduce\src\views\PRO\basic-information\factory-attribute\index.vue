<template>
  <div class="abs100 cs-z-flex-pd16-wrap">
    <div class="cs-z-page-main-content">
      <div class="fff cs-z-tb-wrapper">
        <div class="basic-information">
          <header>基本信息</header>
          <el-form
            ref="form"
            :inline="true"
            :model="form"
            class="demo-form-inline"
            :rules="rules"
            style="padding-left: 20px"
            label-width="140px"
          >
            <el-form-item label="工厂名称" prop="Short_Name">
              <el-input
                v-model="form.Short_Name"
                placeholder="请输入"
              />
            </el-form-item>
            <el-form-item label="工厂全称" prop="Name">
              <el-input v-model="form.Name" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="厂长" prop="Manager">
              <el-autocomplete
                ref="autocomplete"
                v-model="form.Manager"
                :fetch-suggestions="querySearchAsync"
                clearable
                placeholder="请选择"
                style="width: 100%"
                @clear="clear"
                @select="handleSelect"
                @blur="blur"
              />
            </el-form-item>
            <el-form-item label="工厂位置" prop="Address">
              <el-input v-model="form.Address" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="工厂专业类别" prop="Category">
              <!-- <el-radio-group
                v-model="form.Category"
                style="width: 250px; margin-right: 30px"
              >
                <el-radio
                  v-for="item in comType"
                  :key="item.Id"
                  @change="changeCategory(item.Code)"
                  :label="item.Name"
                />
              </el-radio-group> -->
              <el-select
                v-model="form.Category"
                placeholder="请选择"
                disabled
                @change="changeCategory"
              >
                <el-option
                  v-for="item in comType"
                  :key="item.Id"
                  :value="item.Name"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="产能" prop="Productivity">
              <el-input
                v-model="form.Productivity"
                placeholder="请输入"
              />
            </el-form-item>

            <el-form-item label="工厂图片" class="factory-img">
              <OSSUpload
                class="upload-demo"
                action="alioss"
                accept="image/*"
                :on-success="
                  (response, file, fileList) => {
                    uploadSuccess(response, file, fileList);
                  }
                "
                :on-remove="uploadRemove"
                :on-preview="handlePreview"
                :show-file-list="false"
              >
                <el-button type="primary">上传图片</el-button>
              </OSSUpload>
              <div style="position: relative; width:200px">
                <el-image
                  style="
                    width: 200px;
                    height: 120px;
                    background-color: #eee;
                    color: #999;
                    font-size: 40px;
                    margin-top: 10px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                  "
                  :src="src"
                  :preview-src-list="srcList"
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture" />
                  </div>
                </el-image>
                <i
                  v-show="src"
                  class="el-icon-circle-close img-item-icon"
                  @click="deleteItem()"
                />
              </div>
            </el-form-item>
            <!--          </el-form>-->

            <el-divider />
            <div class="year-batch-production">
              <header>工厂年度产量目标</header>
              <div class="radio-box">
                <el-radio-group v-model="AllYear" size="small" @change="changeYear">
                  <el-radio-button :label="NewYear">{{ NewYear }}</el-radio-button>
                  <el-radio-button :label="NewYear - 1">{{
                    NewYear - 1
                  }}</el-radio-button>
                  <el-date-picker
                    v-model="AllYearPicker"
                    type="year"
                    placeholder="其他年份"
                    :editable="false"
                    size="small"
                    style="width: 120px"
                    value-format="yyyy"
                    @change="changeYear"
                  />
                </el-radio-group>
              </div>
              <div style="margin: 20px" />
              <p>年度产量目标：{{ AllTargetValue }}</p>
              <el-form
                ref="form2"
                :inline="true"
                class="demo-form-inline"
                style="padding-left: 20px"
                label-width="70px"
                :model="form2"
              >
                <el-form-item
                  v-for="(item, index) of form2.formArr"
                  :key="index"
                  :label="`${item.Month}月目标`"
                  :prop="`formArr[${index}].Target_value`"
                  :rules="[
                    {
                      validator: (rule, value, callback) => {
                        targetValueStatus(rule, value, callback);
                      }, //后面的这几个是传的自定义参数
                      trigger: 'blur',
                      required: false,
                    },
                  ]"
                >
                  <el-input
                    v-model="item.Target_value"
                    class="input-number"
                    type="number"
                    min="0"
                    @input="TargetValueInput(item.Target_value, index)"
                  />
                </el-form-item>
              </el-form>
            </div>
            <div class="year-batch-production">
              <header>工厂月均产能</header>
              <div v-if="Ablity_List.length !== 0">
                <el-form
                  v-for="(item, index) of Ablity_List"
                  :key="index"
                  ref="form3"
                  :inline="true"
                  class="demo-form-inline"
                  style="padding-left: 20px; display: inline-block;"
                  label-width="100px"
                  :model="item"
                >
                  <el-form-item
                    :label="item.Component_Type_Name + '(t)'"
                    prop="Production_Capacity"
                  >
                    <el-input
                      v-model="item.Production_Capacity"
                      class="input-number"
                      type="number"
                      min="0"
                    />
                  </el-form-item>
                </el-form>
              </div>
              <div v-else style="padding-left: 40px;">暂无数据</div>
            </div>

            <el-divider />

            <!--        <el-form ref="form" :model="form" label-width="80px">-->
            <el-form-item
              label="财务结算组织"
              prop="Financial_Settlement_Organization"
            >
              <el-input
                v-model="form.Financial_Settlement_Organization"
                placeholder="请输入"
              />
            </el-form-item>
            <el-form-item
              label="财务结算组织编号"
              prop="Financial_Settlement_Organization_Code"
            >
              <el-input
                v-model="form.Financial_Settlement_Organization_Code"
                placeholder="请输入"
              />
            </el-form-item>
            <el-form-item label="过磅预警阈值" prop="Weigh_Warning_Threshold">
              <el-input-number v-model.number="form.Weigh_Warning_Threshold" :min="0" style="width: 80%" class="cs-number-btn-hidden cs-input" placeholder="" clearable />
              <span class="ml-8">kg</span>
              <el-tooltip class="item" effect="dark" content="当货物磅重与理重上下浮动超过该值时进行预警提示" placement="top-start">
                <i class="el-icon-question" />
              </el-tooltip>
            </el-form-item>
            <el-form-item label="发货员" prop="Shipper">
              <el-select
                v-model="form.Shipper"
                placeholder="请选择"
                clearable=""
              >
                <el-option
                  v-for="item in factoryPeoplelist"
                  :key="item.Id"
                  :value="item.Id"
                  :label="item.Display_Name"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              v-if="form.Component_Shipping_Approval"
              label="项目发货总量大于"
              prop="Shipping_Approval_LowerLimit"
            >

              <el-input-number v-model="form.Shipping_Approval_LowerLimit" class="cs-number-btn-hidden w80" placeholder="请输入" clearable="" />t
            </el-form-item>
            <el-divider />
            <el-form-item label="是否开启车间管理" prop="Is_Workshop_Enabled">
              <div :class="form.Is_Workshop_Enabled ? 'is-workshop' : ''">
                <el-switch
                  v-model="form.Is_Workshop_Enabled"
                  disabled
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  @click.native="changeWorkshop(form.Is_Workshop_Enabled)"
                />
              </div>
            </el-form-item>
            <el-form-item label="物料重复判定" prop="Is_Mat_Duplicate">
              <el-switch
                v-model="form.Is_Mat_Duplicate"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />
              <el-tooltip class="item" effect="dark" content="原料、辅料入库、退库时相同属性的原料、辅料数量自动合并" placement="top-start">
                <i class="el-icon-question" style="margin-left: 8px;" />
              </el-tooltip>
            </el-form-item>
            <el-form-item label="末道工序直接入库" prop="Is_Skip_Warehousing_Operation">
              <el-switch
                v-model="form.Is_Skip_Warehousing_Operation"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />
            </el-form-item>
            <el-form-item label="是否唯一码管理" prop="Is_Workshop_Enabled">
              <el-switch
                v-model="form.Scan_UniqueCode_Enabled"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />
            </el-form-item>
            <el-form-item label="统计周期" prop="Manage_Cycle_Enabled">
              <div :class="form.Manage_Cycle_Enabled ? 'is-workshop' : ''">
                <el-switch
                  v-model="form.Manage_Cycle_Enabled"
                  disabled
                  @click.native="changeManageCycleEnabled(form.Manage_Cycle_Enabled)"
                />
                <template v-if="form.Manage_Cycle_Enabled">
                  <el-input v-model.number="form.Manage_Cycle_Begin_Date" type="number" :min="1" :max="31" class="manage-cycle-input input-number" step="any" placeholder="请输入日期" @change="changeCheckNum($event,1)">
                    <el-select slot="prepend" v-model="form.Manage_Cycle_Begin_Type" class="manage-cycle-select" placeholder="请选择">
                      <el-option label="上月" :value="1" />
                      <el-option label="当月" :value="2" />
                      <el-option label="下月" :value="3" />
                    </el-select>
                  </el-input>
                  <div class="text">至</div>
                  <el-input v-model.number="form.Manage_Cycle_End_Date" type="number" :min="1" :max="31" class="manage-cycle-input input-number" step="any" placeholder="请输入日期" @change="changeCheckNum($event,2)">
                    <el-select slot="prepend" v-model="form.Manage_Cycle_End_Type" class="manage-cycle-select" placeholder="请选择">
                      <el-option label="上月" :value="1" />
                      <el-option label="当月" :value="2" />
                      <el-option label="下月" :value="3" />
                    </el-select>
                  </el-input>
                </template>
              </div>
            </el-form-item>
            <el-form-item label="发货单审核" prop="Component_Shipping_Approval">
              <el-switch
                v-model="form.Component_Shipping_Approval"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />
            </el-form-item>
            <el-form-item label="零件齐套管理" prop="Is_Part_Prepare">
              <div :class="form.Is_Part_Prepare ? '' : 'is-workshop'">
                <el-switch
                  v-model="form.Is_Part_Prepare"
                  disabled
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  @click.native="changePartPrepareEnabled(form.Is_Part_Prepare)"
                />
              </div>
            </el-form-item>
            <el-form-item label="喷码直接入库" prop="Is_Part_Prepare">
              <el-switch
                v-model="form.Is_Spraying_With_StockIn"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />
            </el-form-item>
            <el-form-item label="清单导入构件重量" prop="Comp_Compute_With_Part">
              <el-radio-group v-model="form.Comp_Compute_With_Part" size="small" @change="computedChange">
                <el-radio :label="false">按清单导入</el-radio>
                <el-radio :label="true">按零件导入</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="发货过磅" prop="Shipping_Weigh_Enabled">
              <el-switch
                v-model="form.Shipping_Weigh_Enabled"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />
            </el-form-item>
            <el-form-item label="原料入库是否质检" prop="Is_Raw_Instore_Check">
              <el-switch
                v-model="form.Is_Raw_Instore_Check"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />
            </el-form-item>
            <el-form-item label="辅料入库是否质检" prop="Is_Aux_Instore_Check">
              <el-switch
                v-model="form.Is_Aux_Instore_Check"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />
            </el-form-item>
            <el-form-item label="发货单号自动生成" prop="Shipping_Order_Number_Auto_Generate">
              <el-switch
                v-model="form.Shipping_Order_Number_Auto_Generate"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />
            </el-form-item>
            <el-form-item label="唯一码管理" prop="Materiel_Unique_Types">
              <el-checkbox-group v-model="form.Materiel_Unique_Types">
                <el-checkbox label="1">板材</el-checkbox>
                <el-checkbox label="2">型材</el-checkbox>
                <el-checkbox label="3">钢卷</el-checkbox>
                <el-checkbox label="99">其他原料</el-checkbox>
                <el-checkbox label="100">辅料</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label-width="160px" label="套料工序允许直接报工" prop="allowDirectReportingAfterNesting">
              <el-switch
                v-model="form.allowDirectReportingAfterNesting"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />
            </el-form-item>
            <el-form-item label-width="220px" label="是否发货计划通过审批才能发货" prop="Is_Shipping_Plan_Approval">
              <el-switch
                v-model="form.Is_Shipping_Plan_Approval"
                active-color="#13ce66"
                inactive-color="#ff4949"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div class="submit-btn">
      <el-button
        type="primary"
        :loading="btnLoading"
        @click="handleSubmit('form', 'form2')"
      >保存</el-button>
      <el-button plain @click="resetForm()">重置</el-button>
    </div>
  </div>
</template>

<script>
import { GetUserPage } from '@/api/sys'
import { GetFactoryEntity, SupplyFactoryInfo } from '@/api/PRO/factory'
import { GetProfessionalType } from '@/api/plm/material'
import OSSUpload from '@/views/plm/components/ossupload'
import getCommonData from '@/mixins/PRO/get-common-data'
export default {
  components: {
    OSSUpload
  },
  mixins: [getCommonData],
  data() {
    return {
      btnLoading: false,
      userOptions: [],
      src: '',
      srcList: [''],
      num: 12,
      form: {
        Scan_UniqueCode_Enabled: false,
        Is_Mat_Duplicate: false,
        Component_Shipping_Approval: false,
        Shipping_Approval_LowerLimit: undefined,
        Short_Name: '',
        Name: '',
        Manager: '',
        Manager_Id: '',
        Address: '',
        Financial_Settlement_Organization_Code: '',
        Financial_Settlement_Organization: '',
        Productivity: '',
        Category: '',
        Professional_Codes: [''],
        Pic_Path: '',
        Id: '',
        Company_Id: '',
        Weigh_Warning_Threshold: undefined,
        Is_Spraying_With_StockIn: false,
        Is_Workshop_Enabled: false, // 是否开启车间管理
        Manage_Cycle_Enabled: false, // 是否开启统计周期
        Is_Part_Prepare: true, // 是否开启零件齐套管理
        Shipping_Weigh_Enabled: true,
        allowDirectReportingAfterNesting: true,
        Is_Shipping_Plan_Approval: true, // 是否发货计划通过审批才能发货
        Manage_Cycle_Begin_Type: '',
        Manage_Cycle_Begin_Date: '',
        Manage_Cycle_End_Type: '',
        Manage_Cycle_End_Date: '',
        Is_Skip_Warehousing_Operation: false,
        Shipper: '',
        Comp_Compute_With_Part: false,
        Is_Raw_Instore_Check: false,
        Is_Aux_Instore_Check: false,
        Shipping_Order_Number_Auto_Generate: true,
        Materiel_Unique_Types: []
      },
      form2: {
        formArr: []
      },
      formArrCopy: [],
      changeForm2: [],
      AllTargetValue: 0,
      AllYear: '',
      AllYearPicker: '',
      NewYear: new Date().getFullYear(),
      rules: {
        Short_Name: [
          { required: true, message: '请输入工厂名称', trigger: 'blur' }
        ],
        Shipping_Approval_LowerLimit: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        Category: [{ required: true, message: '请选择', trigger: 'change' }],
        Productivity: [
          {
            required: true,
            validator: this.productivityStatus,
            trigger: 'blur'
          }
        ]
      },
      rules2: {
        Target_value: [
          {
            required: false,
            validator: this.targetValueStatus,
            trigger: 'blur'
          }
        ]
      },
      comType: '',
      Ablity_List: [] // 产能平衡数据
    }
  },
  created() {
    this.getFactoryEntityForm()
    this.getFactoryPeoplelist()
  },
  methods: {
    // 统计周期开关
    changeManageCycleEnabled(e) {
      // this.form.Manage_Cycle_Enabled = !e
      if (!e) {
        this.$confirm(
          '统计周期开启后无法关闭，请确认后开启',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(() => {
            this.form.Manage_Cycle_Enabled = true
          })
          .catch(() => {
            this.form.Manage_Cycle_Enabled = false
            this.$message({
              type: 'info',
              message: '已取消'
            })
          })
      }
    },
    // 零件齐套管理开关
    changePartPrepareEnabled(e) {
      if (e) {
        this.$confirm(
          '零件齐套管理按钮关闭后不可再开启，请确认后关闭',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(() => {
            this.form.Is_Part_Prepare = false
          })
          .catch(() => {
            this.form.Is_Part_Prepare = true
            this.$message({
              type: 'info',
              message: '已取消'
            })
          })
      }
    },
    // 统计日期校验
    changeCheckNum(e, type) {
      if (Number(e) < 1 || Number(e) > 31) {
        if (type === 1) {
          this.form.Manage_Cycle_Begin_Date = ''
        } else if (type === 2) {
          this.form.Manage_Cycle_End_Date = ''
        }
      }
    },
    // 车间管理开关
    changeWorkshop(e) {
      // console.log(e, "eee");
      if (!e) {
        this.$confirm(
          '车间管理开启后无法关闭，请确认您的业务管理方式中涉及车间层级',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(() => {
            this.form.Is_Workshop_Enabled = true
          })
          .catch(() => {
            this.form.Is_Workshop_Enabled = false
            this.$message({
              type: 'info',
              message: '已取消'
            })
          })
      }
      // console.log(this.form.Is_Workshop_Enabled, "this.form.Is_Workshop_Enabled");
    },
    productivityStatus(rule, value, callback) {
      if (value === '') {
        callback(new Error('请输入'))
      } else if (!Number(value) && Number(value) !== 0) {
        callback(new Error('只能为数字'))
      } else {
        callback()
      }
    },
    targetValueStatus(rule, value, callback) {
      if (!value) {
        callback()
      } else if (!Number(value) && Number(value) !== 0) {
        callback(new Error('只能为数字'))
      } else {
        callback()
      }
    },
    // targetValueStatus2(rule, value, callback) {
    //   if (!value) {
    //     callback();
    //   } else if (!Boolean(Number(value)) && Number(value) !== 0) {
    //     callback(new Error("只能为数字"));
    //   } else {
    //     callback();
    //   }
    // },

    getFactoryEntityForm() {
      GetFactoryEntity({
        id: localStorage.getItem('CurReferenceId')
      }).then((res) => {
        if (res.IsSucceed) {
          const {
            Name,
            Short_Name,
            Category,
            Professional_Codes,
            Id,
            Company_Id,
            Manager,
            Manager_Id,
            Address,
            Financial_Settlement_Organization_Code,
            Financial_Settlement_Organization,
            Productivity,
            Pic_Path,
            Weigh_Warning_Threshold,
            Is_Skip_Warehousing_Operation,
            Is_Workshop_Enabled,
            Nested_Must_Before_Processing,
            Manage_Cycle_Enabled,
            Is_Part_Prepare,
            Is_Spraying_With_StockIn,
            Manage_Cycle_Begin_Type,
            Manage_Cycle_Begin_Date,
            Manage_Cycle_End_Type,
            Manage_Cycle_End_Date,
            Component_Shipping_Approval,
            Is_Mat_Duplicate,
            Scan_UniqueCode_Enabled,
            Shipping_Weigh_Enabled,
            Shipping_Approval_LowerLimit,
            Shipper,
            Comp_Compute_With_Part,
            Is_Raw_Instore_Check,
            Is_Aux_Instore_Check,
            Shipping_Order_Number_Auto_Generate,
            Materiel_Unique_Types,
            Is_Shipping_Plan_Approval
          } = res.Data.entity
          this.form.Is_Mat_Duplicate = Is_Mat_Duplicate
          this.form.Scan_UniqueCode_Enabled = Scan_UniqueCode_Enabled
          this.form.Shipping_Weigh_Enabled = Shipping_Weigh_Enabled
          this.form.Component_Shipping_Approval = Component_Shipping_Approval
          this.form.Shipping_Approval_LowerLimit = Shipping_Approval_LowerLimit
          this.form.Short_Name = Short_Name
          this.form.Name = Name
          this.form.Category = Category
          this.form.Professional_Codes = Professional_Codes
          this.form.Id = Id
          this.form.Company_Id = Company_Id
          this.form.Manager = Manager
          this.form.Manager_Id = Manager_Id
          this.form.Address = Address
          this.form.Financial_Settlement_Organization_Code =
            Financial_Settlement_Organization_Code
          this.form.Financial_Settlement_Organization =
            Financial_Settlement_Organization
          this.form.Productivity = Productivity
          this.form.Pic_Path = Pic_Path
          this.form.Is_Workshop_Enabled = Is_Workshop_Enabled
          this.form.allowDirectReportingAfterNesting = !Nested_Must_Before_Processing
          this.form.Is_Shipping_Plan_Approval = Is_Shipping_Plan_Approval
          this.form.Is_Skip_Warehousing_Operation = Is_Skip_Warehousing_Operation
          this.form.Weigh_Warning_Threshold = Weigh_Warning_Threshold || undefined
          this.form.Manage_Cycle_Enabled = Manage_Cycle_Enabled
          this.form.Is_Part_Prepare = Is_Part_Prepare
          this.form.Is_Spraying_With_StockIn = Is_Spraying_With_StockIn
          this.form.Manage_Cycle_Begin_Type = Manage_Cycle_Begin_Type || ''
          this.form.Manage_Cycle_Begin_Date = Manage_Cycle_Begin_Date || ''
          this.form.Manage_Cycle_End_Type = Manage_Cycle_End_Type || ''
          this.form.Manage_Cycle_End_Date = Manage_Cycle_End_Date || ''
          this.form.Comp_Compute_With_Part = Comp_Compute_With_Part
          this.form.Is_Raw_Instore_Check = Is_Raw_Instore_Check
          this.form.Is_Aux_Instore_Check = Is_Aux_Instore_Check
          this.form.Shipping_Order_Number_Auto_Generate = Shipping_Order_Number_Auto_Generate
          this.form.Materiel_Unique_Types = Materiel_Unique_Types.split(',')
          this.form.Shipper = Shipper || ''
          this.src = Pic_Path
          this.srcList[0] = Pic_Path
          this.formArrCopy = res.Data.list
          this.AllYear = this.NewYear
          this.AllYearPicker = ''
          this.AllTargetValue = 0
          this.form2.formArr = res.Data.list.filter((item) => {
            return item.Year == this.NewYear
          })
          this.form2.formArr.forEach((item) => {
            this.AllTargetValue =
              this.AllTargetValue + Number(item.Target_value)
          })
          if (this.form2.formArr.length < 12) {
            // this.form2.formArr = res.Data.list;
            for (let i = 1; i <= 12; i++) {
              const temp = this.form2.formArr.find((item) => {
                return item.Month == i
              })
              if (!temp) {
                const month = {
                  Factory_id: Id,
                  Professional_Code: Professional_Codes[0],
                  Year: this.NewYear,
                  Month: i,
                  Target_value: ''
                }
                this.form2.formArr.push(month)
              }
            }
          }
          this.form2.formArr.sort(function(a, b) {
            return a.Month - b.Month
          })
          this.Ablity_List = res.Data.Ablity_List
          this.getProfessionalTypeList()
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    getProfessionalTypeList() {
      GetProfessionalType({
        is_System: false,
        pagesize: -1,
        companyId: this.form.Company_Id
      }).then((res) => {
        this.comType = res.Data.Data
      })
    },
    changeCategory(e) {
      const temp = this.comType.find((item) => {
        return item.Name == e
      })
      this.form.Professional_Codes[0] = temp.Code
      this.form2.formArr.forEach((item) => {
        item.Professional_Code = temp.Code
      })
      this.formArrCopy.forEach((item) => {
        item.Professional_Code = temp.Code
      })
    },
    changeYear(e) {
      if (e) {
        this.AllYear = e
      } else {
        this.AllYear = this.NewYear
      }
      if (this.AllYear == this.NewYear || this.AllYear == this.NewYear - 1) {
        this.AllYearPicker = ''
      }
      this.AllTargetValue = 0
      this.form2.formArr = this.formArrCopy.filter((item) => {
        return item.Year == this.AllYear
      })
      if (this.form2.formArr.length < 12) {
        for (let i = 1; i <= 12; i++) {
          const temp = this.form2.formArr.find((item) => {
            return item.Month == i
          })
          if (!temp) {
            const month = {
              Factory_id: this.form.Id,
              Professional_Code: this.form.Professional_Codes[0],
              Year: this.AllYear,
              Month: i,
              Target_value: ''
            }
            this.form2.formArr.push(month)
          }
        }
      }
      this.form2.formArr.sort(function(a, b) {
        return a.Month - b.Month
      })
      this.form2.formArr.forEach((item) => {
        this.AllTargetValue = this.AllTargetValue + Number(item.Target_value)
      })
    },
    TargetValueInput(e, index) {
      if (this.formArrCopy.length != 0) {
        const temp = this.formArrCopy.find((item) => {
          return (
            item.Year == this.form2.formArr[index].Year &&
            item.Month == this.form2.formArr[index].Month
          )
        })
        if (temp) {
          temp.Target_value = e
        } else {
          this.formArrCopy.push(this.form2.formArr[index])
        }
      } else {
        this.formArrCopy.push(this.form2.formArr[index])
      }
      this.AllTargetValue = 0
      this.form2.formArr.forEach((item) => {
        this.AllTargetValue = this.AllTargetValue + Number(item.Target_value)
      })
    },
    async querySearchAsync(queryString, cb) {
      let results = []
      results = await this.getUserList(queryString)
      cb(results)
    },
    getUserList(queryString = '') {
      return new Promise((resolve) => {
        GetUserPage({
          Page: 1,
          pageSize: 20,
          Search: queryString
        }).then((res) => {
          this.userOptions = res.Data.Data.map((v) => {
            this.$set(v, 'value', v.Display_Name)
            return v
          })
          resolve(this.userOptions)
        })
      })
    },
    blur() {
      const temp = this.userOptions.find((item) => {
        return item.Display_Name == this.form.Manager
      })
      if (temp) {
        this.form.Manager_Id = temp.Id
      } else {
        this.form.Manager_Id = ''
      }
    },
    clear() {
      this.form.Manager_Id = ''
      this.form.Manager = ''
      this.$refs.autocomplete.activated = true
    },
    handleSelect(item) {
      this.form.Manager_Id = item.Id
      this.form.Manager = item.Display_Name
    },
    uploadSuccess(response, file, fileList) {
      const imgObj = { File_Name: '', File_Url: '' }
      if (file.hasOwnProperty('response')) {
        imgObj.File_Url = file.response.Data.split('*')[0]
        imgObj.File_Name = file.response.Data.split('*')[3]
      } else {
        imgObj.File_Url = file.url
      }
      this.form.Pic_Path = imgObj.File_Url
      this.src = file.response.encryptionUrl
      this.srcList[0] = file.response.encryptionUrl
      // let temp = this.srcList;
      // this.$set(temp, "0", this.srcList[0]);
    },
    uploadExceed(files, fileList) {
      this.$message({
        type: 'warning',
        message: '已超过文件上传最大数量'
      })
    },
    uploadRemove(file, fileList) {},
    handlePreview(file) {},
    deleteItem() {
      this.form.Pic_Path = ''
      this.src = ''
      this.srcList[0] = ''
    },
    handleSubmit(formName1, formName2) {
      this.$confirm('确认保存', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const form = { ...this.form }
          const form2 = [...this.formArrCopy]
          const form3 = JSON.parse(JSON.stringify(this.Ablity_List))
          console.log(this.Ablity_List, 'Ablity_List')
          form3.map(item => {
            delete item['Component_Type_Name']
            item.Production_Capacity = item.Production_Capacity ? Number(item.Production_Capacity) : item.Production_Capacity
          })
          form.Materiel_Unique_Types = form.Materiel_Unique_Types.join(',')
          form.Nested_Must_Before_Processing = !form.allowDirectReportingAfterNesting
          console.log(form, 'form')
          this.$refs[formName1].validate((valid) => {
            if (valid) {
              this.$refs[formName2].validate((valid) => {
                if (valid) {
                  const obj = {
                    entity: form,
                    list: form2,
                    Ability_List: form3
                  }
                  if (form.Manage_Cycle_Enabled) {
                    if (!form.Manage_Cycle_Begin_Type || !form.Manage_Cycle_Begin_Date || !form.Manage_Cycle_End_Type || !form.Manage_Cycle_End_Date) {
                      this.$message.warning('请补全统计周期')
                      return
                    }
                  }
                  this.btnLoading = true
                  SupplyFactoryInfo(obj).then((res) => {
                    if (res.IsSucceed) {
                      this.$message({
                        message: '保存成功',
                        type: 'success'
                      })
                      this.getFactoryEntityForm()
                    } else {
                      this.$message({
                        message: res.Message,
                        type: 'error'
                      })
                    }
                  })
                  this.btnLoading = false
                } else {
                  return false
                }
              })
            } else {
              return false
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
    },
    resetForm() {
      this.getFactoryEntityForm()
    },
    computedChange(val) {
      console.log(val)
    }
  }
}
</script>

<style scoped lang="scss">
header {
  padding: 20px;
  font-size: 20px;
}

.cs-z-page-main-content{
  padding-bottom: 62px;
}

.manage-cycle-input{
  ::v-deep{
    .el-select .el-input {
      width: 120px !important;
    }
  }
}

.basic-information {
  ::v-deep {
    .el-form-item__content {
      min-width: 250px;
      margin-right: 30px;
      .cs-input{

        .el-input {
          width: unset;
        }
      }
      .el-input {
        width: 250px;
      }
    }

    .el-switch.is-disabled .el-switch__core,
    .el-switch.is-disabled .el-switch__label {
      cursor: pointer;
    }
    .is-workshop {
      .el-switch.is-disabled .el-switch__core,
      .el-switch.is-disabled .el-switch__label {
        cursor: not-allowed;
      }
    }
    .el-switch.is-disabled {
      opacity: 1;
      .el-switch.is-checked .el-switch__core {
        border-color: #298dff;
        background-color: #298dff;
      }
    }
  }
  .manage-cycle{
    display: block;
    .text {
      margin: 12px 0;
      text-align: center;
    }
    ::v-deep {
      .manage-cycle-select {
        .el-input{
          width: 80px;
        }
      }
    }
  }
  .factory-img {
    //display: block;
  }

  .img-item-icon {
    position: absolute;
    top: 0;
    right: 0;
    color: #333;
    font-size: 20px;
    cursor: pointer;
  }
}

.year-batch-production {
  .radio-box {
    .el-radio-group {
      padding-left: 30px;
    }
    .el-radio-button {
      font-size: 20px;
    }
    ::v-deep .el-input--small .el-input__inner {
      height: 30px;
      line-height: 30px;
      border-radius: 0 4px 4px 0;
      border-left: none;
    }
    ::v-deep .el-icon-circle-close {
      color: #d0d3db;
    }
  }

  p {
    padding-left: 30px;
  }
}
.submit-btn {
  padding: 16px 0 16px 32px;
  position: absolute;
  z-index: 99;
  bottom: 16px;
  background-color: #fff;
  width: calc(100% - 48px);
}

::v-deep .input-number{
      input{
        padding-right: 2px;
      }
    }

.ml-8{
  margin: 0 8px;
}
.w80{
  ::v-deep .el-input{
    width: 80% !important;

  }
}
</style>

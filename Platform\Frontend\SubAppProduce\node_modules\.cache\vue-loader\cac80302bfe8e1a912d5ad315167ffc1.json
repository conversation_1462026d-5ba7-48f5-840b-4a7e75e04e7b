{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-path\\compoments\\Add.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-path\\compoments\\Add.vue", "mtime": 1757492056372}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Add.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwFA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Add.vue", "sourceRoot": "src/views/PRO/process-path/compoments", "sourcesContent": ["<template>\r\n  <el-dialog\r\n    v-dialogDrag\r\n    :title=\"isEdit?'编辑':'新增'\"\r\n    :visible.sync=\"dialogVisible\"\r\n    width=\"30%\"\r\n    class=\"plm-custom-dialog\"\r\n    @close=\"handleClose\"\r\n  >\r\n    <div class=\"cs-wrap\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"工艺代码\" prop=\"Code\">\r\n          <el-input v-model=\"form.Code\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"类型\" prop=\"Bom_Level\">\r\n          <template>\r\n            <el-radio-group v-model=\"form.Bom_Level\" @change=\"radioChange\">\r\n              <!-- <el-radio :label=\"1\">构件工艺</el-radio>\r\n            <el-radio :label=\"3\">部件工艺</el-radio>\r\n            <el-radio :label=\"2\">零件工艺</el-radio> -->\r\n              <el-radio v-for=\"item in bomList\" :key=\"item.Code\" :label=\"item.Code\">{{ item.Display_Name }}工艺</el-radio>\r\n            </el-radio-group>\r\n          </template>\r\n        </el-form-item>\r\n        <draggable v-model=\"list\" handle=\".icon-drag\" @change=\"changeDraggable\">\r\n          <el-row v-for=\"(element,index) in list\" :key=\"element.key\" class=\"cs-row\">\r\n            <el-col class=\"cs-col\" :span=\"2\"> <i class=\"iconfont icon-drag cs-drag\" /> </el-col>\r\n            <el-col :span=\"19\">\r\n              <el-form-item :label=\"`工序${index+1}`\" label-width=\"50px\">\r\n                <el-select :key=\"element.key\" v-model=\"element.value\" style=\"width:90%\" :disabled=\"!form.Bom_Level\" placeholder=\"请选择\" clearable @change=\"selectChange($event,element)\">\r\n                  <el-option\r\n                    v-for=\"item in options\"\r\n                    :key=\"item.Code\"\r\n                    :label=\"item.Name\"\r\n                    :disabled=\"item.disabled\"\r\n                    :value=\"item.Code\"\r\n                  >\r\n                    <div class=\"cs-option\">\r\n                      <span class=\"cs-label\">{{ item.Name }}</span>\r\n                    </div>\r\n                  </el-option>\r\n                </el-select>\r\n\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col class=\"cs-col2\" :span=\"3\">\r\n              <span class=\"btn-x\">\r\n                <el-button v-if=\"index===0 && list.length<options.length\" type=\"primary\" icon=\"el-icon-plus\" circle @click=\"handleAdd\" />\r\n                <el-button v-if=\"index!==0\" type=\"danger\" icon=\"el-icon-delete\" circle @click=\"handleDelete(element)\" />\r\n              </span>\r\n            </el-col>\r\n          </el-row>\r\n\r\n        </draggable>\r\n        <el-form-item label=\"备注\" prop=\"Remark\">\r\n          <el-input\r\n            v-model=\"form.Remark\"\r\n            style=\"width: 90%\"\r\n            :autosize=\"{ minRows: 3, maxRows: 5}\"\r\n            show-word-limit\r\n            :maxlength=\"50\"\r\n            type=\"textarea\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"产品类型\" prop=\"Component_Type\">\r\n          <el-tree-select\r\n            ref=\"treeSelectComponentType\"\r\n            v-model=\"searchComTypeSearch\"\r\n            style=\"width: 90%\"\r\n            placeholder=\"请选择\"\r\n            :select-params=\"treeSelectParams\"\r\n            class=\"cs-tree-x\"\r\n            :disabled=\"!form.Bom_Level\"\r\n            :tree-params=\"treeParamsComponentType\"\r\n            @searchFun=\"componentTypeFilter\"\r\n            @check=\"componentTypeChange\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n    <span slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"submit\">确 定</el-button>\r\n    </span>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport Draggable from 'vuedraggable'\r\nimport { AddProessLib, GetProcessFlow, GetProcessListBase } from '@/api/PRO/technology-lib'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { GetCompTypeTree } from '@/api/PRO/component-type'\r\n\r\nimport { GetAllEntities } from '@/api/PRO/settings'\r\nimport { GetPartTypeTree } from '@/api/PRO/partType'\r\n\r\nexport default {\r\n  components: { Draggable },\r\n  props: {\r\n    bomList: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      btnLoading: false,\r\n      isEdit: false,\r\n      searchComTypeSearch: [],\r\n      productTypeList: [],\r\n      form: {\r\n        Component_Type_Codes: [],\r\n        Component_Type_Ids: [],\r\n        Code: '',\r\n        Remark: '',\r\n        Bom_Level: undefined\r\n      },\r\n      rules: {\r\n        Code: [\r\n          { required: true, message: '请输入 ', trigger: 'blur' }\r\n        ],\r\n        Bom_Level: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ]\r\n      },\r\n      list: [],\r\n      options: [],\r\n      treeParamsComponentType: {\r\n        'default-expand-all': true,\r\n        'check-strictly': true,\r\n        filterable: true,\r\n        clickParent: true,\r\n        collapseTags: true,\r\n        multiple: false,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data',\r\n          disabled: 'Is_Disabled'\r\n        }\r\n      },\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        clearable: true\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    searchComTypeSearch: {\r\n      handler(val) {\r\n        if (!val.length) {\r\n          this.form.Component_Type_Codes = []\r\n          this.form.Component_Type_Ids = []\r\n        }\r\n      }\r\n    }\r\n  },\r\n  async mounted() {\r\n    this.getProfession()\r\n  },\r\n  methods: {\r\n    async getProfession() {\r\n      const res = await GetAllEntities({\r\n        companyId: localStorage.getItem('Last_Working_Object_Id'),\r\n        is_System: false\r\n      })\r\n      if (res.IsSucceed) {\r\n        const {\r\n          Code,\r\n          Id\r\n        } = res.Data?.Data?.find(item => item.Code === 'Steel') || {}\r\n        this.typeCode = Code\r\n        this.typeId = Id\r\n        console.log(this.typeCode, this.typeId)\r\n      }\r\n    },\r\n    // componentTypeNodeClick(node) {\r\n    //   console.log(node)\r\n    //   const { Data, Id } = node\r\n    //   this.form.Component_Type_Codes = [Data]\r\n    //   this.form.Component_Type_Ids = [Id]\r\n    // },\r\n    componentTypeChange(vv, c) {\r\n      const _ids = []\r\n      const _codes = []\r\n      const nodes = c.checkedNodes\r\n      console.log(11, nodes)\r\n      nodes.forEach((element, idx) => {\r\n        if (this.form.Bom_Level === 1) {\r\n          const { Data, Id } = element\r\n          _ids.push(Id)\r\n          _codes.push(Data)\r\n        } else {\r\n          const { Data, Id } = element\r\n          _ids.push(Id)\r\n          _codes.push(Data)\r\n        }\r\n      })\r\n      console.log(_ids, _codes)\r\n      this.form.Component_Type_Codes = _codes\r\n      this.form.Component_Type_Ids = _ids\r\n      this.searchComTypeSearch = _ids\r\n    },\r\n    changeDraggable() {\r\n    },\r\n    init() {\r\n      this.list = [{\r\n        key: uuidv4(),\r\n        value: '',\r\n        id: ''\r\n      }]\r\n    },\r\n    async getProductType() {\r\n      let res\r\n      if (this.form.Bom_Level === '-1') {\r\n        res = await GetCompTypeTree({\r\n          professional: this.typeCode,\r\n          markAsOccupied: true,\r\n          technologyId: this.technologyId\r\n        })\r\n      } else {\r\n        res = await GetPartTypeTree({\r\n          professionalId: this.typeId,\r\n          markAsOccupied: true,\r\n          technologyId: this.technologyId,\r\n          partGrade: this.form.Bom_Level.toString()\r\n        })\r\n      }\r\n      if (res.IsSucceed) {\r\n        // this.setDisabledTree(tree)\r\n        this.treeParamsComponentType.data = res.Data\r\n        this.$nextTick(_ => {\r\n            this.$refs.treeSelectComponentType?.treeDataUpdateFun(res.Data)\r\n        })\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    _searchFun(value) {\r\n      this.$refs.treeSelectType.filterFun(value)\r\n    },\r\n    selectChange(val, item) {\r\n      const arr = this.list.map(i => i.value)\r\n      const idx = this.options.findIndex(v => v.Code === val)\r\n      if (idx !== -1) {\r\n        item.id = this.options[idx].Id\r\n      }\r\n      this.options.forEach((item, index) => {\r\n        item.disabled = arr.includes(item.Code)\r\n      })\r\n    },\r\n    handleOpen(row) {\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        if (row && row.Id) {\r\n          this.isEdit = true\r\n          this.technologyId = row.Id\r\n          this.form.Bom_Level = row.Bom_Level.toString()\r\n          console.log('row', row)\r\n          this.getInfo(row)\r\n          if (row.Component_Type_Codes?.length) {\r\n            this.searchComTypeSearch = row?.Component_Type_Codes || []\r\n          } else {\r\n            this.searchComTypeSearch = []\r\n          }\r\n          this.getProductType()\r\n        } else {\r\n          this.technologyId = undefined\r\n          this.isEdit = false\r\n          this.init()\r\n          this.form.Bom_Level = undefined\r\n          this.form.Code = ''\r\n          this.form.Remark = ''\r\n          this.searchComTypeSearch = []\r\n        }\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.$refs['form'].resetFields()\r\n      this.dialogVisible = false\r\n    },\r\n    getInfo(row) {\r\n      GetProcessFlow({\r\n        technologyId: row.Id\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const lr = res.Data.sort((a, b) => a.Step - b.Step)\r\n          if (lr.length) {\r\n            Object.assign(this.form, {\r\n              Code: lr[0].Technology_Code,\r\n              Remark: row.Remark,\r\n              Id: row.Id\r\n            })\r\n            this.getProcessOption()\r\n            this.list = lr.map(v => {\r\n              return {\r\n                key: uuidv4(),\r\n                value: v.Process_code,\r\n                id: v.Process_Id,\r\n                tId: row.Id\r\n              }\r\n            })\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    radioChange() {\r\n      this.init()\r\n      this.getProcessOption()\r\n      this.getProductType()\r\n      this.searchComTypeSearch = []\r\n    },\r\n    componentTypeFilter(e) {\r\n      this.$refs?.treeSelectComponentType.filterFun(e)\r\n    },\r\n    handleAdd() {\r\n      const arr = this.list.map(v => v.value)\r\n      this.options.forEach(v => {\r\n        if (arr.includes(v.Code)) {\r\n          v.disabled = true\r\n        }\r\n      })\r\n      this.list.push({\r\n        key: uuidv4(),\r\n        value: '',\r\n        id: ''\r\n      })\r\n    },\r\n    handleDelete(element) {\r\n      const idx = this.list.findIndex(v => v.value === element.value)\r\n      if (idx !== -1) {\r\n        this.list.splice(idx, 1)\r\n      }\r\n    },\r\n    submit() {\r\n      console.log(this.form, this.list)\r\n      this.$refs['form'].validate((valid) => {\r\n        if (!valid) return false\r\n        const p = this.list.filter(v => v.value)\r\n        if (!p.length) {\r\n          this.$message({\r\n            message: '请至少选择一个工序',\r\n            type: 'error'\r\n          })\r\n          return\r\n        }\r\n        this.btnLoading = true\r\n        if (!this.isEdit && this.form.Id) {\r\n          delete this.form.Id\r\n        }\r\n        // const { Type, ...other } = this.form\r\n        // if (this.form.Type === '-1') {\r\n        //   other.Type = 1\r\n        // } else if (this.form.Type === '0') {\r\n        //   other.Type = 2\r\n        // } else {\r\n        //   other.Type = 3\r\n\r\n        // }\r\n        const form = { ...this.form }\r\n\r\n        const submitObj = {\r\n          TechnologyLib: form,\r\n          ProcessFlow: p.map((v, idx) => {\r\n            return {\r\n              Technology_Id: v.tId || '',\r\n              Process_Id: v.id,\r\n              Step: idx + 1\r\n            }\r\n          })\r\n        }\r\n        AddProessLib(submitObj).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            this.$emit('refresh')\r\n            this.dialogVisible = false\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        }).finally(() => {\r\n          this.btnLoading = false\r\n        })\r\n      })\r\n    },\r\n    getProcessOption() {\r\n      if (!this.form.Bom_Level) return\r\n      return new Promise((resolve, reject) => {\r\n        this.pgLoading = true\r\n        GetProcessListBase({\r\n          bomLevel: this.form.Bom_Level\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.options = res.Data.filter(v => v.Is_Enable).map(v => {\r\n              this.$set(v, 'disabled', false)\r\n              return v\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          resolve()\r\n        }).finally(_ => {\r\n          this.pgLoading = false\r\n        })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.cs-row{\r\n  display: flex;\r\n}\r\n.cs-col{\r\n  text-align: right;\r\n  margin-top: 7px;\r\n}\r\n.cs-col2{\r\n}\r\n.cs-drag{\r\n  cursor: pointer;\r\n  display: inline-block;\r\n}\r\n.cs-wrap{\r\n  max-height: 60vh;\r\n  overflow-y: auto;\r\n}\r\n.cs-tree-x {\r\n  ::v-deep {\r\n    .el-select {\r\n      width: 90%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
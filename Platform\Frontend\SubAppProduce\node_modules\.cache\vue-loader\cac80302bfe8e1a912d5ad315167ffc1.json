{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-path\\compoments\\Add.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-path\\compoments\\Add.vue", "mtime": 1757926768437}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Add.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwFA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Add.vue", "sourceRoot": "src/views/PRO/process-path/compoments", "sourcesContent": ["<template>\r\n  <el-dialog\r\n    v-dialogDrag\r\n    :title=\"isEdit?'编辑':'新增'\"\r\n    :visible.sync=\"dialogVisible\"\r\n    width=\"30%\"\r\n    class=\"plm-custom-dialog\"\r\n    @close=\"handleClose\"\r\n  >\r\n    <div class=\"cs-wrap\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"工艺代码\" prop=\"Code\">\r\n          <el-input v-model=\"form.Code\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"类型\" prop=\"Bom_Level\">\r\n          <template>\r\n            <el-radio-group v-model=\"form.Bom_Level\" @change=\"radioChange\">\r\n              <!-- <el-radio :label=\"1\">构件工艺</el-radio>\r\n            <el-radio :label=\"3\">部件工艺</el-radio>\r\n            <el-radio :label=\"2\">零件工艺</el-radio> -->\r\n              <el-radio v-for=\"item in bomList\" :key=\"item.Code\" :label=\"item.Code\">{{ item.Display_Name }}工艺</el-radio>\r\n            </el-radio-group>\r\n          </template>\r\n        </el-form-item>\r\n        <draggable v-model=\"list\" handle=\".icon-drag\" @change=\"changeDraggable\">\r\n          <el-row v-for=\"(element,index) in list\" :key=\"element.key\" class=\"cs-row\">\r\n            <el-col class=\"cs-col\" :span=\"2\"> <i class=\"iconfont icon-drag cs-drag\" /> </el-col>\r\n            <el-col :span=\"19\">\r\n              <el-form-item :label=\"`工序${index+1}`\" label-width=\"50px\">\r\n                <el-select :key=\"element.key\" v-model=\"element.value\" style=\"width:90%\" :disabled=\"!form.Bom_Level\" placeholder=\"请选择\" clearable @change=\"selectChange($event,element)\">\r\n                  <el-option\r\n                    v-for=\"item in options\"\r\n                    :key=\"item.Code\"\r\n                    :label=\"item.Name\"\r\n                    :disabled=\"item.disabled\"\r\n                    :value=\"item.Code\"\r\n                  >\r\n                    <div class=\"cs-option\">\r\n                      <span class=\"cs-label\">{{ item.Name }}</span>\r\n                    </div>\r\n                  </el-option>\r\n                </el-select>\r\n\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col class=\"cs-col2\" :span=\"3\">\r\n              <span class=\"btn-x\">\r\n                <el-button v-if=\"index===0 && list.length<options.length\" type=\"primary\" icon=\"el-icon-plus\" circle @click=\"handleAdd\" />\r\n                <el-button v-if=\"index!==0\" type=\"danger\" icon=\"el-icon-delete\" circle @click=\"handleDelete(element)\" />\r\n              </span>\r\n            </el-col>\r\n          </el-row>\r\n\r\n        </draggable>\r\n        <el-form-item label=\"备注\" prop=\"Remark\">\r\n          <el-input\r\n            v-model=\"form.Remark\"\r\n            style=\"width: 90%\"\r\n            :autosize=\"{ minRows: 3, maxRows: 5}\"\r\n            show-word-limit\r\n            :maxlength=\"50\"\r\n            type=\"textarea\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"产品类型\" prop=\"Component_Type\">\r\n          <el-tree-select\r\n            ref=\"treeSelectComponentType\"\r\n            v-model=\"searchComTypeSearch\"\r\n            style=\"width: 90%\"\r\n            placeholder=\"请选择\"\r\n            :select-params=\"treeSelectParams\"\r\n            class=\"cs-tree-x\"\r\n            :disabled=\"!form.Bom_Level\"\r\n            :tree-params=\"treeParamsComponentType\"\r\n            @searchFun=\"componentTypeFilter\"\r\n            @check=\"componentTypeChange\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n    <span slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"submit\">确 定</el-button>\r\n    </span>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport Draggable from 'vuedraggable'\r\nimport { AddProessLib, GetProcessFlow, GetProcessListBase } from '@/api/PRO/technology-lib'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { GetCompTypeTree } from '@/api/PRO/component-type'\r\n\r\nimport { GetAllEntities } from '@/api/PRO/settings'\r\nimport { GetPartTypeTree } from '@/api/PRO/partType'\r\n\r\nexport default {\r\n  components: { Draggable },\r\n  props: {\r\n    bomList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    sysProjectId: {\r\n      type: String,\r\n      default: undefined\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      btnLoading: false,\r\n      isEdit: false,\r\n      searchComTypeSearch: [],\r\n      productTypeList: [],\r\n      form: {\r\n        Component_Type_Codes: [],\r\n        Component_Type_Ids: [],\r\n        Code: '',\r\n        Remark: '',\r\n        Bom_Level: undefined\r\n      },\r\n      rules: {\r\n        Code: [\r\n          { required: true, message: '请输入 ', trigger: 'blur' }\r\n        ],\r\n        Bom_Level: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ]\r\n      },\r\n      list: [],\r\n      options: [],\r\n      treeParamsComponentType: {\r\n        'default-expand-all': true,\r\n        'check-strictly': true,\r\n        filterable: true,\r\n        clickParent: true,\r\n        collapseTags: true,\r\n        multiple: false,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data',\r\n          disabled: 'Is_Disabled'\r\n        }\r\n      },\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        clearable: true\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    searchComTypeSearch: {\r\n      handler(val) {\r\n        if (!val.length) {\r\n          this.form.Component_Type_Codes = []\r\n          this.form.Component_Type_Ids = []\r\n        }\r\n      }\r\n    }\r\n  },\r\n  async mounted() {\r\n    this.getProfession()\r\n  },\r\n  methods: {\r\n    async getProfession() {\r\n      const res = await GetAllEntities({\r\n        companyId: localStorage.getItem('Last_Working_Object_Id'),\r\n        is_System: false\r\n      })\r\n      if (res.IsSucceed) {\r\n        const {\r\n          Code,\r\n          Id\r\n        } = res.Data?.Data?.find(item => item.Code === 'Steel') || {}\r\n        this.typeCode = Code\r\n        this.typeId = Id\r\n        console.log(this.typeCode, this.typeId)\r\n      }\r\n    },\r\n    // componentTypeNodeClick(node) {\r\n    //   console.log(node)\r\n    //   const { Data, Id } = node\r\n    //   this.form.Component_Type_Codes = [Data]\r\n    //   this.form.Component_Type_Ids = [Id]\r\n    // },\r\n    componentTypeChange(vv, c) {\r\n      const _ids = []\r\n      const _codes = []\r\n      const nodes = c.checkedNodes\r\n      console.log(11, nodes)\r\n      nodes.forEach((element, idx) => {\r\n        if (this.form.Bom_Level === 1) {\r\n          const { Data, Id } = element\r\n          _ids.push(Id)\r\n          _codes.push(Data)\r\n        } else {\r\n          const { Data, Id } = element\r\n          _ids.push(Id)\r\n          _codes.push(Data)\r\n        }\r\n      })\r\n      console.log(_ids, _codes)\r\n      this.form.Component_Type_Codes = _codes\r\n      this.form.Component_Type_Ids = _ids\r\n      this.searchComTypeSearch = _ids\r\n    },\r\n    changeDraggable() {\r\n    },\r\n    init() {\r\n      this.list = [{\r\n        key: uuidv4(),\r\n        value: '',\r\n        id: ''\r\n      }]\r\n    },\r\n    async getProductType() {\r\n      let res\r\n      if (this.form.Bom_Level === '-1') {\r\n        res = await GetCompTypeTree({\r\n          professional: this.typeCode,\r\n          markAsOccupied: true,\r\n          technologyId: this.technologyId\r\n        })\r\n      } else {\r\n        res = await GetPartTypeTree({\r\n          professionalId: this.typeId,\r\n          markAsOccupied: true,\r\n          technologyId: this.technologyId,\r\n          partGrade: this.form.Bom_Level.toString()\r\n        })\r\n      }\r\n      if (res.IsSucceed) {\r\n        // this.setDisabledTree(tree)\r\n        this.treeParamsComponentType.data = res.Data\r\n        this.$nextTick(_ => {\r\n            this.$refs.treeSelectComponentType?.treeDataUpdateFun(res.Data)\r\n        })\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    _searchFun(value) {\r\n      this.$refs.treeSelectType.filterFun(value)\r\n    },\r\n    selectChange(val, item) {\r\n      const arr = this.list.map(i => i.value)\r\n      const idx = this.options.findIndex(v => v.Code === val)\r\n      if (idx !== -1) {\r\n        item.id = this.options[idx].Id\r\n      }\r\n      this.options.forEach((item, index) => {\r\n        item.disabled = arr.includes(item.Code)\r\n      })\r\n    },\r\n    handleOpen(row) {\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        if (row && row.Id) {\r\n          this.isEdit = true\r\n          this.technologyId = row.Id\r\n          this.form.Bom_Level = row.Bom_Level.toString()\r\n          console.log('row', row)\r\n          this.getInfo(row)\r\n          if (row.Component_Type_Codes?.length) {\r\n            this.searchComTypeSearch = row?.Component_Type_Codes || []\r\n          } else {\r\n            this.searchComTypeSearch = []\r\n          }\r\n          this.getProductType()\r\n        } else {\r\n          this.technologyId = undefined\r\n          this.isEdit = false\r\n          this.init()\r\n          this.form.Bom_Level = undefined\r\n          this.form.Code = ''\r\n          this.form.Remark = ''\r\n          this.searchComTypeSearch = []\r\n        }\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.$refs['form'].resetFields()\r\n      this.dialogVisible = false\r\n    },\r\n    getInfo(row) {\r\n      GetProcessFlow({\r\n        technologyId: row.Id\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const lr = res.Data.sort((a, b) => a.Step - b.Step)\r\n          if (lr.length) {\r\n            Object.assign(this.form, {\r\n              Code: lr[0].Technology_Code,\r\n              Remark: row.Remark,\r\n              Id: row.Id\r\n            })\r\n            this.getProcessOption()\r\n            this.list = lr.map(v => {\r\n              return {\r\n                key: uuidv4(),\r\n                value: v.Process_code,\r\n                id: v.Process_Id,\r\n                tId: row.Id\r\n              }\r\n            })\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    radioChange() {\r\n      this.init()\r\n      this.getProcessOption()\r\n      this.getProductType()\r\n      this.searchComTypeSearch = []\r\n    },\r\n    componentTypeFilter(e) {\r\n      this.$refs?.treeSelectComponentType.filterFun(e)\r\n    },\r\n    handleAdd() {\r\n      const arr = this.list.map(v => v.value)\r\n      this.options.forEach(v => {\r\n        if (arr.includes(v.Code)) {\r\n          v.disabled = true\r\n        }\r\n      })\r\n      this.list.push({\r\n        key: uuidv4(),\r\n        value: '',\r\n        id: ''\r\n      })\r\n    },\r\n    handleDelete(element) {\r\n      const idx = this.list.findIndex(v => v.value === element.value)\r\n      if (idx !== -1) {\r\n        this.list.splice(idx, 1)\r\n      }\r\n    },\r\n    submit() {\r\n      console.log(this.form, this.list)\r\n      this.$refs['form'].validate((valid) => {\r\n        if (!valid) return false\r\n        const p = this.list.filter(v => v.value)\r\n        if (!p.length) {\r\n          this.$message({\r\n            message: '请至少选择一个工序',\r\n            type: 'error'\r\n          })\r\n          return\r\n        }\r\n        this.btnLoading = true\r\n        if (!this.isEdit && this.form.Id) {\r\n          delete this.form.Id\r\n        }\r\n        // const { Type, ...other } = this.form\r\n        // if (this.form.Type === '-1') {\r\n        //   other.Type = 1\r\n        // } else if (this.form.Type === '0') {\r\n        //   other.Type = 2\r\n        // } else {\r\n        //   other.Type = 3\r\n\r\n        // }\r\n        const form = { ...this.form, Sys_Project_Id: this.sysProjectId }\r\n\r\n        const submitObj = {\r\n          TechnologyLib: form,\r\n          ProcessFlow: p.map((v, idx) => {\r\n            return {\r\n              Technology_Id: v.tId || '',\r\n              Process_Id: v.id,\r\n              Step: idx + 1\r\n            }\r\n          })\r\n        }\r\n        AddProessLib(submitObj).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            this.$emit('refresh')\r\n            this.dialogVisible = false\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        }).finally(() => {\r\n          this.btnLoading = false\r\n        })\r\n      })\r\n    },\r\n    getProcessOption() {\r\n      if (!this.form.Bom_Level) return\r\n      return new Promise((resolve, reject) => {\r\n        this.pgLoading = true\r\n        GetProcessListBase({\r\n          bomLevel: this.form.Bom_Level\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.options = res.Data.filter(v => v.Is_Enable).map(v => {\r\n              this.$set(v, 'disabled', false)\r\n              return v\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          resolve()\r\n        }).finally(_ => {\r\n          this.pgLoading = false\r\n        })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.cs-row{\r\n  display: flex;\r\n}\r\n.cs-col{\r\n  text-align: right;\r\n  margin-top: 7px;\r\n}\r\n.cs-col2{\r\n}\r\n.cs-drag{\r\n  cursor: pointer;\r\n  display: inline-block;\r\n}\r\n.cs-wrap{\r\n  max-height: 60vh;\r\n  overflow-y: auto;\r\n}\r\n.cs-tree-x {\r\n  ::v-deep {\r\n    .el-select {\r\n      width: 90%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
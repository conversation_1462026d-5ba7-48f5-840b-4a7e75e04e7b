{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\bom-level-config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\bom-level-config\\index.vue", "mtime": 1757909680919}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/bom-setting/bom-level-config", "sourcesContent": ["<template>\r\n  <div class=\"bom-level-config abs100 cs-z-flex-pd16-wrap\">\r\n    <div class=\"cs-z-page-main-content\">\r\n      <div class=\"title\">\r\n        <i class=\"el-icon-warning\" />\r\n        自行配置BOM层级数，<span class=\"title-span\">最多可新增5层</span>\r\n      </div>\r\n      <div class=\"box-wrapper\">\r\n        <div\r\n          v-for=\"(layer, idx) in visibleLayers\"\r\n          :key=\"layer.key\"\r\n          class=\"box\"\r\n          :class=\"layer.color\"\r\n        >\r\n          <span v-if=\"idx !== 0 && idx !== visibleLayers.length - 1\" class=\"close-icon\" @click=\"handleClose(layer, idx)\" />\r\n          <div class=\"box-title\">BOM{{ numToHan(idx) }}层</div>\r\n          <div class=\"box-subtitle\">{{ layer.subtitle }}</div>\r\n          <div class=\"box-input\">\r\n            <el-input\r\n              v-model.trim=\"layer.title\"\r\n              maxlength=\"20\"\r\n              size=\"medium\"\r\n              class=\"cs-input\"\r\n              placeholder=\"请输入\"\r\n              @blur=\"handleInputBlur\"\r\n            />\r\n            <i class=\"el-icon-edit-outline\" />\r\n          </div>\r\n          <el-divider />\r\n          <div class=\"box-bottom\">\r\n            <div class=\"box-bottom-label\">\r\n              <span\r\n                class=\"cs-bom-btn cs-bom-btn-main\"\r\n                :style=\"{background: layer.color === 'color1' ? '#298DFF' : layer.color === 'color2' ? '#3ECC93' : layer.color === 'color3' ? '#F1B430' : layer.color === 'color4' ? '#426BD8' : '#FF7D23'}\"\r\n                @click=\"handleBottomLabelClick(layer, idx)\"\r\n              >清单配置</span>\r\n              <span\r\n                class=\"cs-bom-btn cs-bom-btn-model\"\r\n                :class=\"{selected: layer.Is_Default_Model}\"\r\n                :style=\"layer.Is_Default_Model ? {borderColor: getMainColor(layer), color: getMainColor(layer)} : {borderColor: 'transparent', color: '#999'}\"\r\n                @click=\"handleModelDefaultClick(layer)\"\r\n              >\r\n                模型默认层级\r\n                <span v-if=\"layer.Is_Default_Model\" class=\"cs-bom-btn-check\" :style=\"{color: getMainColor(layer)}\">\r\n                  <svg width=\"11\" height=\"11\" viewBox=\"0 0 11 11\">\r\n                    <rect width=\"11\" height=\"11\" rx=\"2\" fill=\"currentColor\" />\r\n                    <text x=\"1.5\" y=\"9\" font-size=\"10\" fill=\"#fff\">✓</text>\r\n                  </svg>\r\n                </span>\r\n              </span>\r\n            </div>\r\n            <span class=\"box-bottom-value\">{{ (idx+1).toString().padStart(2, '0') }}</span>\r\n          </div>\r\n        </div>\r\n        <div v-if=\"visibleLayers.length < 5\" class=\"box add-box\" @click=\"handleAddLayer\">\r\n          <div class=\"add-plus\">+</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { GetBomLevelList, SaveBomLevel } from '@/api/PRO/bom-level'\r\nimport { deepClone } from '@/utils'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nexport default {\r\n  mixins: [addRouterPage],\r\n  data() {\r\n    return {\r\n\r\n      apiData: [], // GetBomLevelList接口数据\r\n      tbLoading: false,\r\n      selectedType: 2\r\n    }\r\n  },\r\n  computed: {\r\n    visibleLayers() {\r\n      // 一层和五层始终显示，中间层根据Is_Enabled\r\n      if (!this.apiData || this.apiData.length === 0) return []\r\n      const arr = this.apiData.filter((item, idx) => {\r\n        if (idx === 0 || idx === 4) return true\r\n        return !!item.Is_Enabled\r\n      })\r\n      return arr\r\n    },\r\n    comName() {\r\n      if (!this.apiData || this.apiData.length === 0) return ''\r\n      return this.apiData[0].Display_Name + '清单'\r\n    },\r\n    partName() {\r\n      if (!this.apiData || this.apiData.length === 0) return ''\r\n      return this.apiData[4].Display_Name + '清单'\r\n    },\r\n    addPageArray() {\r\n      const unitPart = this.defaultData.filter(item => item.Is_Enabled && +item.Code > 0)\r\n      const route = [{\r\n        path: this.$route.path + '/ComponentConfig',\r\n        hidden: true,\r\n        component: () => import('@/views/PRO/bom-setting/com-config/index'),\r\n        name: 'PROComponentConfig',\r\n        meta: { title: this.comName }\r\n      },\r\n      {\r\n        path: this.$route.path + '/part-config',\r\n        hidden: true,\r\n        component: () => import('@/views/PRO/bom-setting/part-config/index'),\r\n        name: 'PROPartsConfig',\r\n        meta: { title: this.partName }\r\n      }]\r\n      const curList = []\r\n      if (unitPart.length > 0) {\r\n        unitPart.forEach(item => {\r\n          curList.push({\r\n            path: this.$route.path + `/half-part-config${item.Code}`,\r\n            hidden: true,\r\n            component: () => import('@/views/PRO/bom-setting/half-part-config/index'),\r\n            name: 'PROHalfPartConfig' + item.Code,\r\n            meta: { title: item.Display_Name + '清单' }\r\n          })\r\n        })\r\n      }\r\n      route.splice(1, 0, ...curList)\r\n      console.log('route', route)\r\n      return route\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.getBomLevelList()\r\n    this.handleInitPageRoute()\r\n    console.log('this.addPageArray', this.$router.getRoutes())\r\n  },\r\n  methods: {\r\n    initPage() {\r\n      console.log('hello word 存在即合理')\r\n    },\r\n    handleClose(layer, idx) {\r\n      this.$confirm('确定要关闭该层级吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        if (idx === 0 || idx === this.visibleLayers.length - 1) return\r\n        const target = this.apiData.find(l => l.key === layer.key)\r\n        if (target) target.Is_Enabled = false\r\n        this.saveBomLevelList()\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消关闭'\r\n        })\r\n      })\r\n    },\r\n    handleAddLayer() {\r\n      const canAdd = this.apiData.find((l, idx) => idx !== 0 && idx !== 4 && !l.Is_Enabled)\r\n      if (canAdd) {\r\n        canAdd.Is_Enabled = true\r\n        this.saveBomLevelList()\r\n      }\r\n    },\r\n    handleInputBlur() {\r\n      this.saveBomLevelList()\r\n    },\r\n    saveBomLevelList() {\r\n      // console.log('this.apiData', this.apiData)\r\n      let flag = true\r\n      const payload = this.apiData.map(item => {\r\n        const { key, title, bottomValue, subtitle, color, ...others } = item\r\n\r\n        others.Display_Name = title\r\n        if (title === '') {\r\n          flag = false\r\n        }\r\n        return others\r\n      })\r\n      if (!flag) {\r\n        this.$message.error('请输入BOM层级名称')\r\n        return\r\n      }\r\n      const data1 = JSON.stringify(this.defaultData)\r\n      const data2 = JSON.stringify(payload)\r\n      if (data1 === data2) {\r\n        console.log('没有变化')\r\n        return\r\n      }\r\n      console.log('payload', JSON.parse(JSON.stringify(payload)))\r\n\r\n      this.$confirm('确定要保存所有修改吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.btnLoading = true\r\n        SaveBomLevel(payload).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message.success('保存成功')\r\n            this.$store.dispatch('bomInfo/clearBomLevelCache')\r\n            this.getBomLevelList()\r\n          } else {\r\n            this.$message.error(res.Message || '保存失败')\r\n          }\r\n        }).catch(error => {\r\n          console.error('保存失败:', error)\r\n          this.$message.error('保存失败')\r\n        }).finally(() => {\r\n          this.btnLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('已取消保存')\r\n        this.apiData.forEach(item => {\r\n          const defaultItem = this.defaultData.find(d => d.Code === item.Code)\r\n          item.Is_Default_Model = defaultItem.Is_Default_Model\r\n        })\r\n      })\r\n\r\n      // this.handleInitPageRoute()\r\n      // 调用保存接口\r\n      // SaveBomLevelList(payload).then(...)\r\n    },\r\n    handleBottomLabelClick(layer, idx) {\r\n      const _name = layer.Code === '-1' ? 'PROComponentConfig' : layer.Code === '0' ? 'PROPartsConfig' : 'PROHalfPartConfig' + layer.Code\r\n      console.log('_name', _name)\r\n      const query = { pg_redirect: this.$route.name }\r\n      if (+layer.Code > 0) {\r\n        query.level = layer.Code\r\n      }\r\n      this.$router.push({ name: _name, query })\r\n      // 这里可加保存逻辑\r\n    },\r\n    handleModelDefaultClick(layer) {\r\n      this.apiData.forEach(l => { l.Is_Default_Model = false })\r\n      layer.Is_Default_Model = true\r\n      this.saveBomLevelList()\r\n    },\r\n    handleInputEditClick() {\r\n      this.inputEdit = true\r\n    },\r\n    getBomLevelList() {\r\n      this.tbLoading = true\r\n      return new Promise((resolve) => {\r\n        GetBomLevelList().then(res => {\r\n          const { IsSucceed, Message, Data } = res\r\n          if (IsSucceed) {\r\n            this.defaultData = deepClone(Data)\r\n            this.apiData = (Data || []).map((v, index) => {\r\n              v.key = v.Code\r\n              v.subtitle = index === 0 ? 'layer One' : index === 1 ? 'layer Two' : index === 2 ? 'layer Three' : index === 3 ? 'layer Four' : 'layer Five'\r\n              v.color = index === 0 ? 'color1' : index === 1 ? 'color2' : index === 2 ? 'color3' : index === 3 ? 'color4' : 'color5'\r\n              v.bottomValue = (index + 1).toString().padStart(2, '0')\r\n              v.title = v.Display_Name\r\n              return v\r\n            })\r\n            if (this.apiData && this.apiData.length > 0) {\r\n              const enabledCount = this.apiData.filter(item => !!item.Is_Enabled).length\r\n              if (enabledCount >= 2) {\r\n                this.selectedType = enabledCount\r\n              } else {\r\n                this.selectedType = 2\r\n              }\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: Message || '获取BOM层级列表失败',\r\n              type: 'error'\r\n            })\r\n          }\r\n          console.log('this.apiData', this.apiData)\r\n          resolve()\r\n        }).catch(error => {\r\n          console.error('获取BOM层级列表失败:', error)\r\n          this.$message.error('获取BOM层级列表失败')\r\n          resolve()\r\n        }).finally(() => {\r\n          this.tbLoading = false\r\n        })\r\n      })\r\n    },\r\n    numToHan(num) {\r\n      const hanArr = ['一', '二', '三', '四', '五']\r\n      return hanArr[num] || num\r\n    },\r\n    getMainColor(layer) {\r\n      return layer.color === 'color1' ? '#298DFF' : layer.color === 'color2' ? '#3ECC93' : layer.color === 'color3' ? '#F1B430' : layer.color === 'color4' ? '#426BD8' : '#FF7D23'\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.cs-z-page-main-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 50px 20px;\r\n  overflow: hidden;\r\n  width: 100%;\r\n\r\n}\r\n\r\n.title {\r\n  margin:100px 0 70px 0;\r\n  font-family: Microsoft YaHei, Microsoft YaHei;\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  color: #222834;\r\n\r\n  .title-span,\r\n  .el-icon-warning {\r\n    color: #FF7C19;\r\n  }\r\n}\r\n\r\n.box-wrapper {\r\n  display: flex;\r\n  overflow-x: auto;\r\n  width: 95%;\r\n}\r\n\r\n.box {\r\n  display: flex;\r\n  flex-direction: column;\r\n  box-sizing: border-box;\r\n  padding: 20px;\r\n  min-width: 280px;\r\n  height: 340px;\r\n  margin: 12px;\r\n  border-radius: 4px 4px 4px 4px;\r\n  position: relative;\r\n\r\n  .box-title {\r\n    display: flex;\r\n    justify-content: center;\r\n    height: 35px;\r\n    font-family: Microsoft YaHei, Microsoft YaHei;\r\n    font-weight: bold;\r\n    font-size: 26px;\r\n    color: #333333;\r\n    margin-top: 30px;\r\n  }\r\n\r\n  .box-subtitle {\r\n    display: flex;\r\n    justify-content: center;\r\n    height: 19px;\r\n    margin-top: 8px;\r\n    font-family: Microsoft YaHei, Microsoft YaHei;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #999999;\r\n  }\r\n\r\n  .box-input {\r\n    display: flex;\r\n    flex-wrap: nowrap;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-top: 56px;\r\n    gap: 8px;\r\n  }\r\n\r\n  .close-icon {\r\n    width: 27px;\r\n    height: 27px;\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    cursor: pointer;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    &::before {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 0;\r\n      right: 0;\r\n      width: 0;\r\n      height: 0;\r\n      border-top: 27px solid #ACD4FF;\r\n      border-right: 27px solid #ACD4FF;\r\n      border-bottom: 27px solid transparent;\r\n      border-left: 27px solid transparent;\r\n      z-index: 0;\r\n    }\r\n\r\n    &::after {\r\n      content: \"×\";\r\n      position: relative;\r\n      z-index: 1;\r\n      color: #ffffff;\r\n      font-size: 24px;\r\n      font-weight: bold;\r\n      left: 1px;\r\n      top: 1px;\r\n      pointer-events: none;\r\n    }\r\n  }\r\n  .el-icon-edit-outline{\r\n    font-size: 18px;\r\n  }\r\n\r\n  .box-bottom {\r\n    flex: 1;\r\n    margin-top: 8px;\r\n    display: flex;\r\n    align-items: center;\r\n    position: relative;\r\n\r\n    .box-bottom-label {\r\n      z-index: 1;\r\n      align-self: flex-end;\r\n      padding-bottom: 12px;\r\n\r\n    }\r\n\r\n    .box-bottom-value {\r\n      position: absolute;\r\n      top: 0;\r\n      right: 0;\r\n      z-index: 0;\r\n      font-size: 72px;\r\n      font-family: STHupo, STHupo;\r\n      font-weight: 400;\r\n      font-size: 72px;\r\n    }\r\n  }\r\n}\r\n\r\n.add-box {\r\n  background: #F4F5F6;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-width: 280px;\r\n  height: 323px;\r\n  margin: 12px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  .add-plus {\r\n    width: 100px;\r\n    height: 100px;\r\n    color: #C8C9CC;\r\n    font-size: 100px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    user-select: none;\r\n  }\r\n}\r\n\r\n.el-divider--horizontal {\r\n  color: #D9DBE2;\r\n  margin: 14px 17px;\r\n  width: unset;\r\n}\r\n\r\n.el-icon-edit-outline {\r\n  color: #8E95AA;\r\n  margin-left: 6px;\r\n}\r\n\r\n.cs-input {\r\n  border-color: unset;\r\n  outline: unset;\r\n  border: unset;\r\n  background: transparent;\r\n  box-shadow: unset;\r\n  width: 120px;\r\n  text-align: center;\r\n\r\n  &:focus {\r\n    border-color: unset;\r\n    outline: unset;\r\n    border: unset;\r\n    background: transparent;\r\n    box-shadow: unset;\r\n  }\r\n\r\n  ::v-deep .el-input__inner {\r\n    padding: 0;\r\n    font-size: 18px;\r\n    border: unset;\r\n    outline: unset;\r\n    background: transparent;\r\n    box-shadow: unset;\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n.color1 {\r\n  background: rgba(41, 141, 255, 0.12);\r\n  .close-icon::before {\r\n    border-top: 27px solid rgba(41, 141, 255, 0.11);\r\n    border-right: 27px solid rgba(41, 141, 255, 0.11);\r\n  }\r\n  .box-bottom-label {\r\n    color: #298DFF;\r\n  }\r\n  .box-bottom-value {\r\n    color: #D3E8FF;\r\n  }\r\n}\r\n\r\n.color2 {\r\n  background: rgba(62, 204, 147, 0.12);\r\n  .close-icon::before {\r\n    border-top: 27px solid #ABE9D0;\r\n    border-right: 27px solid #ABE9D0;\r\n  }\r\n  .box-bottom-label {\r\n    color: #3ECC93;\r\n  }\r\n  .box-bottom-value {\r\n    color: #D3F3E6;\r\n  }\r\n}\r\n\r\n.color3 {\r\n  background: rgba(255, 170, 0, 0.12);\r\n  .close-icon::before {\r\n    border-top: 27px solid #FEDA92;\r\n    border-right: 27px solid #FEDA92;\r\n  }\r\n  .box-bottom-label {\r\n    color: #F1B430;\r\n  }\r\n  .box-bottom-value {\r\n    color: #FFECC4;\r\n  }\r\n}\r\n\r\n.color4 {\r\n  background: rgba(66, 107, 216, 0.12);\r\n  .close-icon::before {\r\n    border-top: 27px solid rgba(66, 107, 216, 0.11);\r\n    border-right: 27px solid rgba(66, 107, 216, 0.11);\r\n  }\r\n  .box-bottom-label {\r\n    color: #426BD8;\r\n  }\r\n  .box-bottom-value {\r\n    color: rgba(66, 107, 216,0.12);\r\n  }\r\n}\r\n\r\n.color5 {\r\n  background: rgba(255, 125, 35, 0.12);\r\n  .close-icon::before {\r\n    border-top: 27px solid rgba(255, 125, 35, 0.11);\r\n    border-right: 27px solid rgba(255, 125, 35, 0.11);\r\n  }\r\n  .box-bottom-label {\r\n    color: #FF7D23;\r\n  }\r\n  .box-bottom-value {\r\n    color: rgba(255, 125, 35, 0.12);\r\n  }\r\n}\r\n\r\n.cs-bom-btn {\r\n  display: inline-block;\r\n  border-radius: 4px;\r\n  padding: 6px 12px;\r\n  font-size: 14px;\r\n  margin-right: 8px;\r\n  cursor: pointer;\r\n  user-select: none;\r\n  transition: border 0.2s;\r\n  &.cs-bom-btn-main {\r\n    color: #fff;\r\n    border: none;\r\n  }\r\n  &.cs-bom-btn-model {\r\n    background: #fff;\r\n    color: #999;\r\n    border: 1.5px solid transparent;\r\n    position: relative;\r\n    padding-right: 22px;\r\n    &.selected {\r\n      font-weight: bold;\r\n    }\r\n    .cs-bom-btn-check {\r\n      position: absolute;\r\n      top: 0px;\r\n      right: 0px;\r\n      width: 11px;\r\n      height: 11px;\r\n      background: transparent;\r\n      border-radius: 2px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      color: inherit;\r\n      z-index: 2;\r\n      svg {\r\n        display: block;\r\n      }\r\n    }\r\n  }\r\n}\r\n.cs-bom-btn:last-child {\r\n  margin-right: 0;\r\n}\r\n\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\bom-level-config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\bom-level-config\\index.vue", "mtime": 1757468112202}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuFA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/bom-setting/bom-level-config", "sourcesContent": ["<template>\r\n  <div class=\"bom-level-config abs100 cs-z-flex-pd16-wrap\">\r\n    <div class=\"cs-z-page-main-content\">\r\n      <div class=\"query-section\">\r\n        <div class=\"query-form\">\r\n          <div>\r\n            <span class=\"query-label\">请选择BOM层级数：</span>\r\n            <el-select\r\n              v-model=\"selectedType\"\r\n              placeholder=\"请选择\"\r\n              class=\"query-select\"\r\n              @change=\"handleTypeChange\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in typeOptions\"\r\n                :key=\"item.value\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"\r\n              />\r\n            </el-select>\r\n          </div>\r\n          <el-button type=\"primary\" class=\"submit-btn\" :loading=\"btnLoading\" @click=\"handleSubmit\">\r\n            提交\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"table-section\">\r\n        <vxe-table\r\n          ref=\"xTable1\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          empty-text=\"暂无数据\"\r\n          height=\"auto\"\r\n          show-overflow\r\n          :checkbox-config=\"{checkField: 'checked'}\"\r\n          :loading=\"tbLoading\"\r\n          :row-config=\"{isCurrent: true, isHover: true }\"\r\n          class=\"cs-vxe-table\"\r\n          align=\"left\"\r\n          keep-source\r\n          stripe\r\n          :data=\"tableData\"\r\n          resizable\r\n          :edit-config=\"{trigger: 'manual', mode: 'row', showStatus: true}\"\r\n        >\r\n          <vxe-column field=\"Sys_Name\" title=\"BOM层级\" align=\"center\">\r\n            <template #default=\"{ row }\">\r\n              {{ row.Sys_Name }}\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column field=\"Display_Name\" title=\"名称\" :edit-render=\"{}\" align=\"center\">\r\n            <template #edit=\"{ row }\">\r\n              <vxe-input v-model=\"row.Display_Name\" type=\"text\" />\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column field=\"Is_Default_Model\" title=\"模型默认层级\" align=\"center\" :edit-render=\"{}\">\r\n            <template #edit=\"{ row }\">\r\n              <el-switch\r\n                v-model=\"row.Is_Default_Model\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n                @change=\"handleDefaultModelChange(row, $event)\"\r\n              />\r\n            </template>\r\n            <template #default=\"{ row }\">\r\n              <el-tag v-if=\"row.Is_Default_Model\" type=\"success\">是</el-tag>\r\n              <el-tag v-else type=\"danger\">否</el-tag>\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column title=\"操作\" width=\"160\" align=\"center\">\r\n            <template #default=\"{ row }\">\r\n              <template v-if=\"$refs.xTable1.isActiveByRow(row)\">\r\n                <el-button type=\"text\" @click=\"saveRowEvent(row)\">保存</el-button>\r\n                <el-button type=\"text\" @click=\"cancelRowEvent(row)\">取消</el-button>\r\n              </template>\r\n              <template v-else>\r\n                <el-button type=\"text\" @click=\"editRowEvent(row)\">编辑</el-button>\r\n              </template>\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n      </div>\r\n    </div>\r\n  </div></template>\r\n\r\n<script>\r\nimport { GetBomLevelList, SaveBomLevel } from '@/api/PRO/bom-level'\r\n\r\nexport default {\r\n  name: 'PROBOMLevelConfig',\r\n  data() {\r\n    return {\r\n      selectedType: 2,\r\n      btnLoading: false,\r\n      loading: false,\r\n      apiData: [],\r\n      typeOptions: [\r\n        { label: '二层', value: 2 },\r\n        { label: '三层', value: 3 },\r\n        { label: '四层', value: 4 },\r\n        { label: '五层', value: 5 }\r\n      ],\r\n      tableData: [],\r\n      tbLoading: false\r\n    }\r\n  },\r\n  created() {\r\n    this.selectedType = 2\r\n    this.getBomLevelList()\r\n  },\r\n  methods: {\r\n    getBomLevelList() {\r\n      this.tbLoading = true\r\n      return new Promise((resolve) => {\r\n        GetBomLevelList().then(res => {\r\n          const { IsSucceed, Message, Data } = res\r\n          if (IsSucceed) {\r\n            this.apiData = (Data || []).map(v => {\r\n              v.isEditing = false\r\n              v.originalName = v.Display_Name\r\n              return v\r\n            })\r\n\r\n            if (this.apiData && this.apiData.length > 0) {\r\n              const enabledCount = this.apiData.filter(item => !!item.Is_Enabled).length\r\n              if (enabledCount >= 2) {\r\n                this.selectedType = enabledCount\r\n              } else {\r\n                this.selectedType = 2\r\n              }\r\n            }\r\n\r\n            this.generateTableData(this.selectedType || 2, this.apiData)\r\n          } else {\r\n            this.$message({\r\n              message: Message || '获取BOM层级列表失败',\r\n              type: 'error'\r\n            })\r\n          }\r\n          resolve()\r\n        }).catch(error => {\r\n          console.error('获取BOM层级列表失败:', error)\r\n          this.$message.error('获取BOM层级列表失败')\r\n          resolve()\r\n        }).finally(() => {\r\n          this.tbLoading = false\r\n        })\r\n      })\r\n    },\r\n\r\n    generateTableData(levelCount, apiData = []) {\r\n      const levelCodeMap = {\r\n        2: ['-1', '0'],\r\n        3: ['-1', '1', '0'],\r\n        4: ['-1', '1', '2', '0'],\r\n        5: ['-1', '1', '2', '3', '0']\r\n      }\r\n\r\n      const validCodes = levelCodeMap[levelCount] || levelCodeMap[2]\r\n\r\n      apiData.forEach(item => {\r\n        item.Is_Enabled = false\r\n      })\r\n\r\n      const filteredData = apiData.filter(v => validCodes.includes(v.Code))\r\n\r\n      filteredData.forEach(item => {\r\n        item.Is_Enabled = true\r\n      })\r\n\r\n      filteredData.sort((a, b) => parseInt(a.Sort) - parseInt(b.Sort))\r\n\r\n      this.tableData = filteredData\r\n    },\r\n\r\n    handleTypeChange(value) {\r\n      if (this.apiData) {\r\n        this.generateTableData(value, this.apiData)\r\n      }\r\n    },\r\n\r\n    handleSubmit() {\r\n      const hasDefault = this.tableData.every(item => item.Is_Default_Model === false)\r\n      if (hasDefault) {\r\n        this.$message.warning('至少需要一个默认层级')\r\n        return\r\n      }\r\n\r\n      const saveData = [...this.apiData]\r\n\r\n      this.$confirm('确定要保存所有修改吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.btnLoading = true\r\n        SaveBomLevel(saveData).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message.success('保存成功')\r\n            this.getBomLevelList()\r\n          } else {\r\n            this.$message.error(res.Message || '保存失败')\r\n          }\r\n        }).catch(error => {\r\n          console.error('保存失败:', error)\r\n          this.$message.error('保存失败')\r\n        }).finally(() => {\r\n          this.btnLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('已取消保存')\r\n      })\r\n    },\r\n\r\n    handleEdit(row, rowIndex) {\r\n      this.customTableConfig.tableData.forEach(item => {\r\n        if (item !== row) {\r\n          row.isEditing = false\r\n        }\r\n      })\r\n      row.isEditing = true\r\n    },\r\n\r\n    handleSaveName(row, rowIndex) {\r\n      row.isEditing = false\r\n    },\r\n\r\n    handleCancel(row, rowIndex) {\r\n      row.Display_Name = row.originalName\r\n      row.isEditing = false\r\n    },\r\n\r\n    handleDefaultModelChange(row, value) {\r\n      this.tableData.forEach(item => {\r\n        if (item !== row) {\r\n          item.Is_Default_Model = false\r\n        }\r\n      })\r\n    },\r\n    editRowEvent(row) {\r\n      const $table = this.$refs.xTable1\r\n      $table.setEditRow(row)\r\n    },\r\n    saveRowEvent() {\r\n      const $table = this.$refs.xTable1\r\n      $table.clearEdit().then(() => {\r\n        this.loading = true\r\n        setTimeout(() => {\r\n          this.loading = false\r\n        }, 300)\r\n      })\r\n    },\r\n    cancelRowEvent(row) {\r\n      const $table = this.$refs.xTable1\r\n      $table.clearEdit().then(() => {\r\n        $table.revertData(row)\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.bom-level-config {\r\n  .cs-z-page-main-content {\r\n    display: flex;\r\n    flex-direction: column;\r\n    flex: 1;\r\n    overflow: hidden;\r\n    height: 100%;\r\n\r\n    .query-section {\r\n      margin-bottom: 20px;\r\n      border-radius: 4px;\r\n\r\n      .query-form {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n\r\n        .query-label {\r\n          font-size: 14px;\r\n          color: #333;\r\n          white-space: nowrap;\r\n        }\r\n\r\n                 .query-select {\r\n           width: 200px;\r\n         }\r\n\r\n         .submit-btn {\r\n           margin-left: 8px;\r\n         }\r\n      }\r\n    }\r\n\r\n       .table-section {\r\n      flex: 1;\r\n      overflow: hidden;\r\n\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\TypeDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\TypeDialog.vue", "mtime": 1757921826136}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IEFkZENoZWNrVHlwZSB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snCmltcG9ydCB7IEVudGl0eUNoZWNrVHlwZSB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snCmltcG9ydCB7IFNhdmVDaGVja1R5cGUgfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJwpleHBvcnQgZGVmYXVsdCB7CiAgLy8gcHJvcHM6IHsKICAvLyAgIGRpYWxvZ0RhdGE6IHt9CiAgLy8gfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgQm9tX0xldmVsOiAnJywKICAgICAgY2hlY2tfb2JqZWN0X2lkOiAnJywKICAgICAgZm9ybToge30sCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgTmFtZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnYmx1cicgfV0KICAgICAgfSwKICAgICAgdGl0bGU6ICcnLAogICAgICBlZGl0SW5mbzoge30KICAgIH0KICB9LAogIC8vIHdhdGNoOiB7CiAgLy8gICBkaWFsb2dEYXRhOiB7CiAgLy8gICAgIGhhbmRsZXIobmV3TmFtZSwgb2xkTmFtZSkgewogIC8vICAgICAgIGNvbnNvbGUubG9nKCJuZXdOYW1lIixuZXdOYW1lKQogIC8vICAgICAgIGlmKG5ld05hbWUpIHsKICAvLyAgICAgICAgIHRoaXMuZm9ybSA9IE9iamVjdC5hc3NpZ24oe30sbmV3TmFtZSk7CiAgLy8gICAgICAgfQogIC8vICAgICB9LAogIC8vICAgICBkZWVwOiB0cnVlLAogIC8vICAgICBpbW1lZGlhdGU6IHRydWUKICAvLyAgIH0sCiAgLy8gfSwKICBtb3VudGVkKCkge30sCiAgbWV0aG9kczogewogICAgaW5pdCh0aXRsZSwgY2hlY2tUeXBlLCBkYXRhKSB7CiAgICAgIHRoaXMudGl0bGUgPSB0aXRsZQogICAgICB0aGlzLkNoZWNrX09iamVjdF9JZCA9IGNoZWNrVHlwZS5JZAogICAgICB0aGlzLkJvbV9MZXZlbCA9IGNoZWNrVHlwZS5Db2RlCiAgICAgIGlmICh0aXRsZSA9PT0gJ+e8lui+kScpIHsKICAgICAgICB0aGlzLmVkaXRJbmZvID0gZGF0YQogICAgICAgIGNvbnNvbGUubG9nKHRoaXMuZWRpdEluZm8pCiAgICAgICAgdGhpcy5nZXRFbnRpdHlDaGVja1R5cGUoZGF0YSkKICAgICAgfQogICAgfSwKICAgIGFzeW5jIGFkZENoZWNrVHlwZSgpIHsKICAgICAgYXdhaXQgQWRkQ2hlY2tUeXBlKHsKICAgICAgICBOYW1lOiB0aGlzLmZvcm0uTmFtZSwKICAgICAgICBDaGVja19PYmplY3RfSWQ6IHRoaXMuQ2hlY2tfT2JqZWN0X0lkLAogICAgICAgIEJvbV9MZXZlbDogdGhpcy5Cb21fTGV2ZWwKICAgICAgfSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfkv53lrZjmiJDlip8nCiAgICAgICAgICB9KQogICAgICAgICAgdGhpcy4kZW1pdCgncmVmcmVzaCcpCiAgICAgICAgICB0aGlzLiRlbWl0KCdjbG9zZScpCiAgICAgICAgICB0aGlzLmRpYWxvZ0RhdGEgPSB7fQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGdldEVudGl0eUNoZWNrVHlwZShkYXRhKSB7CiAgICAgIEVudGl0eUNoZWNrVHlwZSh7IGlkOiBkYXRhLklkIH0pLnRoZW4oKHJlcykgPT4gewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICB0aGlzLmZvcm0gPSByZXMuRGF0YVswXQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGVkaXRDaGVja1R5cGUoKSB7CiAgICAgIFNhdmVDaGVja1R5cGUoewogICAgICAgIElkOiB0aGlzLmVkaXRJbmZvLklkLAogICAgICAgIC4uLnRoaXMuZm9ybSwKICAgICAgICBDaGVja19PYmplY3RfSWQ6IHRoaXMuQ2hlY2tfT2JqZWN0X0lkLAogICAgICAgIEJvbV9MZXZlbDogdGhpcy5Cb21fTGV2ZWwKICAgICAgfSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfnvJbovpHmiJDlip8nCiAgICAgICAgICB9KQogICAgICAgICAgdGhpcy4kZW1pdCgncmVmcmVzaCcpCiAgICAgICAgICB0aGlzLiRlbWl0KCdjbG9zZScpCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZQogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlU3VibWl0KGZvcm0pIHsKICAgICAgdGhpcy4kcmVmc1tmb3JtXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHRoaXMudGl0bGUgPT09ICfmlrDlop4nID8gdGhpcy5hZGRDaGVja1R5cGUoKSA6IHRoaXMuZWRpdENoZWNrVHlwZSgpCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHJldHVybiBmYWxzZQogICAgICAgIH0KICAgICAgfSkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["TypeDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAwBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TypeDialog.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup/components/Dialog", "sourcesContent": ["<template>\n  <div>\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"80px\">\n      <el-row>\n        <el-col :span=\"24\">\n          <el-form-item label=\"检查类型\" prop=\"Name\">\n            <el-input v-model=\"form.Name\" />\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item style=\"text-align: right\">\n            <el-button @click=\"$emit('close')\">关 闭</el-button>\n            <el-button\n              type=\"primary\"\n              @click=\"handleSubmit('form')\"\n            >确 定</el-button>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n  </div>\n</template>\n\n<script>\nimport { AddCheckType } from '@/api/PRO/factorycheck'\nimport { EntityCheckType } from '@/api/PRO/factorycheck'\nimport { SaveCheckType } from '@/api/PRO/factorycheck'\nexport default {\n  // props: {\n  //   dialogData: {}\n  // },\n  data() {\n    return {\n      Bom_Level: '',\n      check_object_id: '',\n      form: {},\n      rules: {\n        Name: [{ required: true, message: '请填写完整表单', trigger: 'blur' }]\n      },\n      title: '',\n      editInfo: {}\n    }\n  },\n  // watch: {\n  //   dialogData: {\n  //     handler(newName, oldName) {\n  //       console.log(\"newName\",newName)\n  //       if(newName) {\n  //         this.form = Object.assign({},newName);\n  //       }\n  //     },\n  //     deep: true,\n  //     immediate: true\n  //   },\n  // },\n  mounted() {},\n  methods: {\n    init(title, checkType, data) {\n      this.title = title\n      this.Check_Object_Id = checkType.Id\n      this.Bom_Level = checkType.Code\n      if (title === '编辑') {\n        this.editInfo = data\n        console.log(this.editInfo)\n        this.getEntityCheckType(data)\n      }\n    },\n    async addCheckType() {\n      await AddCheckType({\n        Name: this.form.Name,\n        Check_Object_Id: this.Check_Object_Id,\n        Bom_Level: this.Bom_Level\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            type: 'success',\n            message: '保存成功'\n          })\n          this.$emit('refresh')\n          this.$emit('close')\n          this.dialogData = {}\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    getEntityCheckType(data) {\n      EntityCheckType({ id: data.Id }).then((res) => {\n        if (res.IsSucceed) {\n          this.form = res.Data[0]\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    editCheckType() {\n      SaveCheckType({\n        Id: this.editInfo.Id,\n        ...this.form,\n        Check_Object_Id: this.Check_Object_Id,\n        Bom_Level: this.Bom_Level\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            type: 'success',\n            message: '编辑成功'\n          })\n          this.$emit('refresh')\n          this.$emit('close')\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    handleSubmit(form) {\n      this.$refs[form].validate((valid) => {\n        if (valid) {\n          this.title === '新增' ? this.addCheckType() : this.editCheckType()\n        } else {\n          return false\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped></style>\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\partRecognitionConfig.vue?vue&type=style&index=0&id=585437ac&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\partRecognitionConfig.vue", "mtime": 1757468113429}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCkBpbXBvcnQgIn5AL3N0eWxlcy9taXhpbi5zY3NzIjsNCi5mb3JtLXdyYXBwZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KICBtYXgtaGVpZ2h0OiA3MHZoOw0KICAuZm9ybS14ew0KICAgIG92ZXJmbG93OiBhdXRvOw0KICAgIHBhZGRpbmctcmlnaHQ6IDE2cHg7DQogICAgQGluY2x1ZGUgc2Nyb2xsQmFyOw0KICB9DQogIC5idG4teCB7DQogICAgcGFkZGluZy10b3A6IDE2cHg7DQogICAgdGV4dC1hbGlnbjogcmlnaHQ7DQogIH0NCg0KfQ0K"}, {"version": 3, "sources": ["partRecognitionConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "partRecognitionConfig.vue", "sourceRoot": "src/views/PRO/process-settings/management/component", "sourcesContent": ["<template>\r\n  <div class=\"form-wrapper\">\r\n    <div class=\"form-x\">\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"120px\">\r\n        <el-form-item label=\"是否启用\" prop=\"enable\">\r\n          <el-radio-group v-model=\"form.enable\">\r\n            <el-radio :label=\"false\">否</el-radio>\r\n            <el-radio :label=\"true\">是</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <template v-if=\"form.enable\">\r\n          <el-form-item label=\"识别类型\" prop=\"identifyAttr\">\r\n            <el-radio-group v-model=\"form.identifyAttr\">\r\n              <el-radio :label=\"1\">{{ currentBomName }}名称前缀</el-radio>\r\n              <el-radio :label=\"2\">{{ currentBomName }}规格前缀</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item v-for=\"(element,index) in list\" :key=\"index\" :show-message=\"false\" :label=\"element.Part_Type_Name\" prop=\"mainPart\">\r\n            <el-input v-model.trim=\"form['item'+index]\" :placeholder=\"`请输入（多个使用'${splitSymbol}'隔开），单个配置不超过10个字符`\" clearable @blur=\"mainBlur\" />\r\n          </el-form-item>\r\n        </template>\r\n      </el-form>\r\n    </div>\r\n    <div class=\"btn-x\">\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { uniqueArr } from '@/utils'\r\nimport {\r\n  GetFactoryPartTypeIndentifySetting,\r\n  SavePartTypeIdentifySetting\r\n} from '@/api/PRO/partType'\r\n\r\nconst SPLITVALUE = '|'\r\n\r\nexport default {\r\n  props: {\r\n    bomList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    level: {\r\n      type: Number,\r\n      default: 0\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      form: {\r\n        enable: false,\r\n        identifyAttr: 1 // 默认为零件名称前缀\r\n      },\r\n      list: [],\r\n      splitSymbol: SPLITVALUE,\r\n      btnLoading: false\r\n    }\r\n  },\r\n  computed: {\r\n    currentBomName() {\r\n      return this.bomList.find(item => +item.Code === this.level)?.Display_Name\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getTypeList()\r\n  },\r\n  methods: {\r\n    async getTypeList() {\r\n      GetFactoryPartTypeIndentifySetting({\r\n        Part_Grade: 0\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const { Is_Enabled, Setting_List } = res.Data\r\n          this.form.enable = Is_Enabled\r\n          this.list = Setting_List.map((v, index) => {\r\n            this.$set(this.form, 'item' + index, v.Prefixs || '')\r\n            return v\r\n          })\r\n\r\n          // 获取Setting_List中的Identify_Attr，如果有效（值为1或2）则使用，否则默认为1\r\n          if (Setting_List.length > 0) {\r\n            const identifyAttr = Setting_List[0].Identify_Attr\r\n            this.form.identifyAttr = (identifyAttr === 1 || identifyAttr === 2) ? identifyAttr : 1\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      if (this.form.enable) {\r\n        const arr = []\r\n        console.log('this.form', this.form)\r\n        for (let i = 0; i < this.list.length; i++) {\r\n          const regex = /^(?!.*\\|\\|)(?!.*\\|$)(?!^\\|)[^|]{1,10}(?:\\|[^|]{1,10})*$/\r\n          if (!regex.test(this.form[`item${i}`])) {\r\n            this.$message({\r\n              message: `${this.list[i].Part_Type_Name}配置不符合要求`,\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n\r\n          const item = this.form[`item${i}`].split(this.splitSymbol).filter(v => !!v)\r\n\r\n          if (item.length === 0) {\r\n            this.$message({\r\n              message: `${this.list[i].Part_Type_Name}不能为空`,\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n\r\n          for (let j = 0; j < item.length; j++) {\r\n            const d = item[j]\r\n            if (d.length > 10) {\r\n              this.$message({\r\n                message: `${this.list[i].Part_Type_Name}单个配置，不能超过10个字符`,\r\n                type: 'warning'\r\n              })\r\n              return\r\n            }\r\n          }\r\n\r\n          arr.push(...item)\r\n        }\r\n        const uniArr = uniqueArr(arr)\r\n        if (uniArr.length !== arr.length) {\r\n          this.$message({\r\n            message: '配置不能相同',\r\n            type: 'warning'\r\n          })\r\n          return\r\n        }\r\n      }\r\n      this.btnLoading = true\r\n      SavePartTypeIdentifySetting({\r\n        Is_Enabled: this.form.enable,\r\n        Setting_List: this.list.map((v, i) => {\r\n          return {\r\n            ...v,\r\n            Prefixs: this.form[`item${i}`],\r\n            Identify_Attr: this.form.identifyAttr\r\n          }\r\n        })\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '操作成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('close')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.btnLoading = false\r\n      })\r\n    },\r\n    mainBlur(e) {\r\n\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"~@/styles/mixin.scss\";\r\n.form-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  max-height: 70vh;\r\n  .form-x{\r\n    overflow: auto;\r\n    padding-right: 16px;\r\n    @include scrollBar;\r\n  }\r\n  .btn-x {\r\n    padding-top: 16px;\r\n    text-align: right;\r\n  }\r\n\r\n}\r\n</style>\r\n"]}]}
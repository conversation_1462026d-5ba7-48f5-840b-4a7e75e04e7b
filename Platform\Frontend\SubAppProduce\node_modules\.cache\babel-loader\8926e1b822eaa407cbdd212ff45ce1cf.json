{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\suggestDevice.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\suggestDevice.vue", "mtime": 1758689267822}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBFbFRhYmxlRW1wdHkgZnJvbSAnQC9jb21wb25lbnRzL0VsVGFibGVFbXB0eS9pbmRleC52dWUnOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1N1Z2dlc3REZXZpY2UnLAogIGNvbXBvbmVudHM6IHsKICAgIEVsVGFibGVFbXB0eTogRWxUYWJsZUVtcHR5CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHt9OwogIH0sCiAgbWV0aG9kczoge30KfTs="}, {"version": 3, "names": ["ElTableEmpty", "name", "components", "data", "methods"], "sources": ["src/views/PRO/plan-production/task-list/suggestDevice.vue"], "sourcesContent": ["<template>\n  <div class=\"contentBox\">\n    <div class=\"device-total\">\n      <div class=\"first\">加工设备数量<span>2</span></div>\n      <div class=\"second\">累计加工时间<span>10h</span></div>\n      <div class=\"third\">累计切割米数<span>200.25</span></div>\n    </div>\n    <div class=\"device-list\">\n      <div class=\"device-info\">\n        <div class=\"image\"><ElTableEmpty :empty-content=\"暂无图片\" /></div>\n        <div class=\"info\">信息</div>\n      </div>\n    </div>\n  </div>\n</template>\n<script>\nimport ElTableEmpty from '@/components/ElTableEmpty/index.vue'\nexport default {\n  name: 'SuggestDevice',\n  components: {\n    ElTableEmpty\n  },\n  data() {\n    return {}\n  },\n  methods: {}\n}\n</script>\n<style scoped lang=\"scss\">\n@import \"~@/styles/mixin.scss\";\n.contentBox {\n  height: 70vh;\n\n  .device-total {\n    display: flex;\n    justify-content: center;\n    margin-bottom: 20px;\n    div {\n      height: 36px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      padding: 0 16px;\n      border-radius: 4px;\n      font-size: 14px;\n      span {\n        font-weight: bold;\n        font-size: 16px;\n        margin-left: 12px;\n      }\n    }\n    div:nth-child(1) {\n      margin-left: 8px;\n      margin-right: 8px;\n      background-color: rgba(0, 72, 152, .1);\n      color: rgba(0, 72, 152, 1);\n    }\n    div:nth-child(2) {\n      margin-left: 8px;\n      margin-right: 8px;\n      background-color: rgba(0, 141, 80, .1);\n      color: rgba(0, 141, 80, 1);\n    }\n    div:nth-child(3) {\n      margin-left: 8px;\n      margin-right: 8px;\n      background-color: rgba(33, 77, 194, .1);\n      color: rgba(33, 77, 194, 1);\n    }\n  }\n  .device-list {\n    width: 100%;\n    display: flex;\n    flex-direction: column;\n    .device-info {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 16px;\n      .image {\n        width: 50%;\n        height: 270px;\n        border-radius: 4px;\n        background-color: #f5f5f5;\n      }\n      .info {\n        flex: 1;\n        padding-left: 16px;\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;AAgBA,OAAAA,YAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAF,YAAA,EAAAA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;EACA;EACAC,OAAA;AACA", "ignoreList": []}]}
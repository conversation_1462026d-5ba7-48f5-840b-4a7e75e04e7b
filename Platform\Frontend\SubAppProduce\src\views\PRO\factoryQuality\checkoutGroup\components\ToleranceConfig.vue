<template>
  <div style="height: calc(100vh - 300px)">
    <vxe-table
      v-loading="tbLoading"
      :empty-render="{name: 'NotData'}"
      show-header-overflow
      element-loading-spinner="el-icon-loading"
      element-loading-text="拼命加载中"
      empty-text="暂无数据"
      height="100%"
      align="left"
      stripe
      :data="tbData"
      resizable
      :auto-resize="true"
      class="cs-vxe-table"
      :tooltip-config="{ enterable: true }"
    >
      <vxe-column
        show-overflow="tooltip"
        sortable
        field="Length"
        title="长度"
        min-width="150"
        align="center"
      >
        <template #default="{ row }">
          <span>
            {{ row.TypeTag }}
            {{ row.Length }}
          </span>
        </template>
      </vxe-column>
      <vxe-column
        show-overflow="tooltip"
        sortable
        min-width="150"
        align="left"
        field="Demand"
        title="公差要求"
      />
      <vxe-column
        show-overflow="tooltip"
        sortable
        field="Modify_Date"
        title="编辑时间"
        min-width="150"
        align="center"
      />
      <vxe-column fixed="right" title="操作" width="200" align="center" show-overflow>
        <template #default="{ row }">
          <el-button type="text" @click="editEvent(row)">编辑</el-button>
          <el-divider direction="vertical" />
          <el-button type="text" @click="removeEvent(row)">删除</el-button>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<script>

import {
  DeleteToleranceSetting,
  GetToleranceSettingList
} from '@/api/PRO/qualityInspect/quality-management'

export default {

  data() {
    return {
      tbLoading: false,
      tbData: [],
      dialogVisible: false,
      form: {
        Id: '',
        Length: 0,
        Demand: '',
        Type: 1
      }

    }
  },
  mounted() {
    this.getToleranceList()
  },
  methods: {
    getToleranceList() {
      this.tbLoading = true
      GetToleranceSettingList({}).then(res => {
        if (res.IsSucceed) {
          this.tbData = res.Data.map(item => {
            item.Modify_Date = item.Modify_Date || item.Create_Date
            item.TypeTag = item.Type === 1 ? '<' : item.Type === 2 ? '<=' : item.Type === 3 ? '>' : item.Type === 4 ? '>=' : '='
            return item
          })
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      }).finally(() => {
        this.tbLoading = false
      })
    },
    editEvent(row) {
      const form = JSON.parse(JSON.stringify(row))
      this.$emit('edit', form)
    },
    removeEvent(row) {
      this.$confirm('此操作将永久删除该公差配置, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          DeleteToleranceSetting({ id: row.Id }).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getToleranceList()
            } else {
              this.$message({
                type: 'error',
                message: res.Message
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }

  }
}
</script>

<style scoped lang="scss">
</style>

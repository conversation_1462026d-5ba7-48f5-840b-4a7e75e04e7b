{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\Add.vue?vue&type=style&index=0&id=31dd5805&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\Add.vue", "mtime": 1757468113389}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KQGltcG9ydCAifkAvc3R5bGVzL21peGluLnNjc3MiOw0KDQouYnRuLWRlbCB7DQogIG1hcmdpbi1sZWZ0OiAtMTAwcHg7DQp9DQoNCi5jdXN0b21SYWRpb0NsYXNzIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCn0NCg0KLmNoZWNrYm94RmxleCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5mb3JtLXdyYXBwZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KICBtaW4taGVpZ2h0OiA0MHZoOw0KDQogIC5mb3JtLXggew0KICAgIG1heC1oZWlnaHQ6IDcwdmg7DQogICAgb3ZlcmZsb3c6IGF1dG87DQogICAgcGFkZGluZy1yaWdodDogMTZweDsNCiAgICBAaW5jbHVkZSBzY3JvbGxCYXI7DQogIH0NCg0KICAuYnRuLXggew0KICAgIHBhZGRpbmctdG9wOiAxNnB4Ow0KICAgIHRleHQtYWxpZ246IHJpZ2h0Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["Add.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmjBA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "Add.vue", "sourceRoot": "src/views/PRO/process-settings/management/component", "sourcesContent": ["<template>\r\n  <div class=\"form-wrapper\">\r\n    <div class=\"form-x\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\" style=\"width: 100%\">\r\n        <el-form-item label=\"名称\" prop=\"Name\">\r\n          <el-input v-model=\"form.Name\" :maxlength=\"30\" placeholder=\"最多30个字\" show-word-limit />\r\n        </el-form-item>\r\n        <el-form-item label=\"代号\" prop=\"Code\">\r\n          <el-input v-model=\"form.Code\" :maxlength=\"30\" placeholder=\"字母+数字，30字符\" show-word-limit\r\n            @input=\"(e) => (form.Code = codeChange(e))\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"类型\" prop=\"Bom_Level\">\r\n          <el-radio-group v-for=\"(item, index) in radioList\" :key=\"index\" v-model=\"form.Bom_Level\" class=\"radio\"\r\n            @change=\"changeType\">\r\n            <el-radio style=\"margin-right: 8px;\" :label=\"item.Code\">{{ item.Display_Name }}</el-radio>\r\n          </el-radio-group>\r\n          <!-- <el-radio-group v-model=\"form.Type\" @change=\"changeType\" class=\"radio\">\r\n              <el-radio :label=\"1\">构件工序</el-radio>一层\r\n            <el-radio v-if=\"isVersionFour\" :label=\"3\">部件工序</el-radio>二三四层\r\n            <el-radio v-if=\"!hiddenPart\" :label=\"2\">零件工序</el-radio> 五层\r\n          </el-radio-group> -->\r\n        </el-form-item>\r\n        <el-form-item label=\"排序\" prop=\"Sort\">\r\n          <el-input-number v-model=\"form.Sort\" :min=\"0\" step-strictly :step=\"1\" class=\"cs-number-btn-hidden w100\"\r\n            placeholder=\"请输入\" clearable=\"\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"协调人\" prop=\"Coordinate_UserId\">\r\n          <el-select v-model=\"form.Coordinate_UserId\" class=\"w100\" clearable filterable placeholder=\"请选择\">\r\n            <el-option v-for=\"item in optionsUserList\" :key=\"item.Id\" :label=\"item.Display_Name\" :value=\"item.Id\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"工序月均负荷\" prop=\"Month_Avg_Load\">\r\n          <el-input v-model=\"form.Month_Avg_Load\" placeholder=\"请输入\">\r\n            <template slot=\"append\">吨</template>\r\n          </el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否外协\" prop=\"Is_External\">\r\n          <el-radio-group v-model=\"form.Is_External\">\r\n            <el-radio :label=\"true\">是</el-radio>\r\n            <el-radio :label=\"false\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否装焊工序\" prop=\"Is_Welding_Assembling\">\r\n          <el-radio-group v-model=\"form.Is_Welding_Assembling\">\r\n            <el-radio :label=\"true\">是</el-radio>\r\n            <el-radio :label=\"false\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否专检\" prop=\"Is_Need_Check\">\r\n          <el-radio-group v-model=\"form.Is_Need_Check\" @change=\"radioChange\">\r\n            <el-radio :label=\"true\">是</el-radio>\r\n            <el-radio :label=\"false\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否互检\" prop=\"Is_Inter_Check\">\r\n          <el-radio-group v-model=\"form.Is_Inter_Check\" @change=\"radioInterChange\">\r\n            <el-radio :label=\"true\">是</el-radio>\r\n            <el-radio :label=\"false\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否下料工序\" prop=\"Is_Cutting\">\r\n          <el-radio-group v-model=\"form.Is_Cutting\">\r\n            <el-radio :label=\"true\">是</el-radio>\r\n            <el-radio :label=\"false\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否套料工序\" prop=\"Is_Nest\">\r\n          <el-radio-group v-model=\"form.Is_Nest\">\r\n            <el-radio :label=\"true\">是</el-radio>\r\n            <el-radio :label=\"false\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <template v-if=\"form.Is_Need_Check\">\r\n          <el-form-item label=\"专检方式\" prop=\"Check_Style\">\r\n            <el-radio-group v-model=\"form.Check_Style\" @change=\"radioCheckStyleChange\">\r\n              <el-radio label=\"0\">抽检</el-radio>\r\n              <el-radio label=\"1\">全检</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"专检类型\" prop=\"\">\r\n            <div>\r\n              <div style=\"margin-bottom: 10px;\">\r\n                <el-checkbox v-model=\"form.Is_Need_TC\" @change=\"checkboxChange($event, 1)\">\r\n                  <span> 探伤</span>\r\n                </el-checkbox>\r\n                <span style=\"margin-left: 30px; \">\r\n                  <span style=\"color: rgba(34, 40, 52, 0.65)\">探伤员：</span>\r\n                  <el-select v-model=\"TC_Check_UserIds\" filterable clearable :disabled=\"!form.Is_Need_TC\" multiple\r\n                    placeholder=\"请选择探伤员\" @change=\"changeTc\">\r\n                    <el-option v-for=\"item in optionsUserList\" :key=\"item.Id\" :label=\"item.Display_Name\"\r\n                      :value=\"item.Id\" />\r\n                  </el-select>\r\n                </span>\r\n              </div>\r\n              <div>\r\n                <el-checkbox v-model=\"form.Is_Need_ZL\" @change=\"checkboxChange($event, 2)\">\r\n                  <span> 质量</span>\r\n                </el-checkbox>\r\n                <span style=\"margin-left: 30px\">\r\n                  <span style=\"color: rgba(34, 40, 52, 0.65)\">质检员：</span>\r\n                  <el-select v-model=\"ZL_Check_UserIds\" :disabled=\"!form.Is_Need_ZL\" filterable clearable multiple\r\n                    placeholder=\"请选择质检员\" @change=\"changeZL\">\r\n                    <el-option v-for=\"item in optionsUserList\" :key=\"item.Id\" :label=\"item.Display_Name\"\r\n                      :value=\"item.Id\" />\r\n                  </el-select>\r\n                </span>\r\n              </div>\r\n\r\n            </div>\r\n          </el-form-item>\r\n\r\n          <!-- <el-form-item label=\"检查项组合\" prop=\"Check_Group_List\">\r\n           <el-select\r\n             v-model=\"form.Check_Group_List\"\r\n             multiple\r\n             style=\"width: 100%\"\r\n             :disabled=\"!Boolean(form.Type)\"\r\n             placeholder=\"请选择检查项组合\"\r\n           >\r\n             <el-option\r\n               v-for=\"item in optionsGroupList\"\r\n               :key=\"item.Id\"\r\n               :label=\"item.Group_Name\"\r\n               :value=\"item.Id\"\r\n             />\r\n           </el-select>\r\n         </el-form-item> -->\r\n        </template>\r\n\r\n        <el-form-item label=\"是否启用\" prop=\"Is_Enable\">\r\n          <el-radio-group v-model=\"form.Is_Enable\">\r\n            <el-radio :label=\"true\">是</el-radio>\r\n            <el-radio :label=\"false\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"加工班组\" prop=\"Working_Team_Ids\">\r\n          <el-select v-model=\"form.Working_Team_Ids\" multiple style=\"width: 100%\" placeholder=\"请选择加工班组\">\r\n            <el-option v-for=\"item in optionsWorkingTeamsList\" :key=\"item.Id\" :label=\"item.Name\" :value=\"item.Id\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"form.Remark\" type=\"textarea\" />\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n    <div class=\"btn-x\">\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button :loading=\"btnLoading\" type=\"primary\" @click=\"handleSubmit\">确 定\r\n      </el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\nimport { GetUserList } from '@/api/sys'\r\nimport {\r\n  AddWorkingProcess,\r\n  GetFactoryPeoplelist,\r\n  GetCheckGroupList,\r\n  GetWorkingTeams\r\n} from '@/api/PRO/technology-lib'\r\nimport { mapGetters } from 'vuex'\r\nimport { GetDictionaryDetailListByCode } from '@/api/sys'\r\nexport default {\r\n  props: {\r\n    type: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    rowInfo: {\r\n      type: Object,\r\n      default() {\r\n        return {}\r\n      }\r\n    },\r\n    bomList: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      checkList: [],\r\n      radioList: [],\r\n      btnLoading: false,\r\n      hiddenPart: false,\r\n\r\n      form: {\r\n        Code: '',\r\n        Name: '',\r\n        Bom_Level: '',\r\n        Month_Avg_Load: '',\r\n        Coordinate_UserId: '',\r\n        Sort: undefined,\r\n        Is_Enable: true,\r\n        Is_External: false,\r\n        Is_Nest: false,\r\n        Is_Need_Check: true,\r\n        Is_Inter_Check: true,\r\n        Is_Need_TC: true,\r\n        Is_Welding_Assembling: false,\r\n        Is_Cutting: false,\r\n        TC_Check_UserId: '',\r\n        Is_Need_ZL: false,\r\n        ZL_Check_UserId: '',\r\n\r\n        Check_Style: '0',\r\n\r\n        Working_Team_Ids: [],\r\n        Remark: ''\r\n      },\r\n      ZL_Check_UserIds: [],\r\n      TC_Check_UserIds: [],\r\n      CheckChange: true,\r\n      userOptions: [],\r\n      optionsUserList: [],\r\n      optionsGroupList: [],\r\n      optionsWorkingTeamsList: [],\r\n      rules: {\r\n        Code: [\r\n          { required: true, message: '请输入代号', trigger: 'blur' },\r\n          { max: 30, message: '长度在 30 个字符内', trigger: 'blur' }\r\n        ],\r\n        Name: [\r\n          { required: true, message: '请输入名称', trigger: 'blur' },\r\n          { max: 30, message: '长度在 30 个字符内', trigger: 'blur' }\r\n        ],\r\n        Bom_Level: [{ required: true, message: '请选择类型', trigger: 'change' }],\r\n        Sort: [{ required: true, message: '请输入', trigger: 'blur' }],\r\n        Is_Need_Check: [\r\n          { required: true, message: '请选择是否质检', trigger: 'change' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters('tenant', ['isVersionFour'])\r\n\r\n  },\r\n  mounted() {\r\n    this.getUserList()\r\n    this.getFactoryPeoplelist()\r\n    // this.getCheckGroupList();\r\n    // this.SelectioncheckCombination()\r\n    this.getWorkingTeamsList()\r\n    console.log('type', this.rowInfo)\r\n    this.type === 'edit' && this.initForm()\r\n    this.getBOMInfo()\r\n  },\r\n  methods: {\r\n\r\n    radioInterChange(val) { },\r\n    getBOMInfo() {\r\n      this.radioList = this.bomList\r\n    },\r\n    // 获取设备类型\r\n    async getDictionaryDetailListByCode() {\r\n      await GetDictionaryDetailListByCode({ dictionaryCode: 'deviceType' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.deviceTypeList = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      console.log(' this.optionsGroupList', this.optionsGroupList)\r\n    },\r\n\r\n    initForm() {\r\n      this.getBOMInfo()\r\n      const { Is_Nest, ...others } = this.rowInfo\r\n      this.form = Object.assign({}, others, { Is_Nest: !!Is_Nest })\r\n      console.log('others', others)\r\n      this.form.Bom_Level = String(this.form.Bom_Level)\r\n      //  if(this.form.Type==2){\r\n      //   this.form.Types = '0'\r\n      //  }else if(this.form.Type==3){\r\n      //   let Types = this.radioList.find(v => ['1', '2','3'].includes(v.Code))?.Code\r\n      //   console.log('Types', Types)\r\n      //   console.log('this.radioList', this.radioList)\r\n      //   this.form.Types = Types\r\n      //  }else if(this.form.Type==1){\r\n      //   this.form.Types = '-1'\r\n      //  }\r\n      console.log('this.form', this.form)\r\n\r\n      // 处理历史数据多选问题\r\n      // if (this.form.Is_Need_Check) {\r\n      //   if (this.form.Check_Style === '1') {\r\n\r\n      //   } else {\r\n      //     this.CheckChange = !!this.form.Is_Need_TC\r\n      //     if (this.form.Is_Need_ZL && this.form.Is_Need_TC) {\r\n      //       this.form.Is_Need_TC = true\r\n      //       this.form.Is_Need_ZL = false\r\n      //     }\r\n      //   }\r\n      // }\r\n      this.ZL_Check_UserIds = this.form.ZL_Check_UserId\r\n        ? this.form.ZL_Check_UserId.split(',')\r\n        : []\r\n      this.TC_Check_UserIds = this.form.TC_Check_UserId\r\n        ? this.form.TC_Check_UserId.split(',')\r\n        : []\r\n      console.log('this.TC_Check_UserIds', this.TC_Check_UserIds)\r\n    },\r\n    getUserList() {\r\n      GetUserList({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.userOptions = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getFactoryPeoplelist() {\r\n      GetFactoryPeoplelist({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.optionsUserList = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async getCheckGroupList() {\r\n      await GetCheckGroupList({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.optionsGroupList = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      console.log(' this.optionsGroupList', this.optionsGroupList)\r\n    },\r\n    getWorkingTeamsList() {\r\n      GetWorkingTeams({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.optionsWorkingTeamsList = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 筛选检查项组合\r\n    // async  SelectioncheckCombination(){\r\n    //   this.form.Check_Group_List = []\r\n    //   this.optionsGroupList = []\r\n    //   await this.getCheckGroupList();\r\n    //   let optionsGroupListTemp = JSON.parse(JSON.stringify(this.optionsGroupList));\r\n\r\n    //   console.log(\"SelectioncheckCombination1\", optionsGroupListTemp)\r\n    //   this.form.Type == 1  && optionsGroupListTemp &&  (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Object_Id === \"1\").length > 0 ? optionsGroupListTemp.filter(v =>v.Check_Object_Id === \"1\") : optionsGroupListTemp) //通过工序筛选 Type= 1 Check_Type = 0 构件 2 Check_Type = 1 零件\r\n    //   console.log(\"1\", optionsGroupListTemp)\r\n    //   this.form.Type == 2  && optionsGroupListTemp &&  (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Object_Id === \"2\").length > 0 ? optionsGroupListTemp.filter(v => v.Check_Object_Id === \"2\") : optionsGroupListTemp)\r\n    //   if(this.form.Is_Need_TC && this.form.Is_Need_ZL){\r\n    //     optionsGroupListTemp =  optionsGroupListTemp.filter(v => v.Check_Type === -1).length > 0 ? optionsGroupListTemp.filter(v => v.Check_Type === -1) : optionsGroupListTemp\r\n    //     console.log(\"2\", optionsGroupListTemp,this.form.Is_Need_TC)\r\n\r\n    //   }else{\r\n    //     if(this.form.Is_Need_TC || this.form.Is_Need_ZL){\r\n    //       this.form.Is_Need_TC && optionsGroupListTemp && (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Type === 2 ).length > 0 ? optionsGroupListTemp.filter(v => v.Check_Type === 2 || v.Check_Type === -1) : optionsGroupListTemp.filter(v => v.Check_Type === -1))\r\n    //       this.form.Type == 1  && optionsGroupListTemp &&  (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Object_Id === \"1\").length > 0 ? optionsGroupListTemp.filter(v =>v.Check_Object_Id === \"1\") : optionsGroupListTemp) //通过工序筛选 Type= 1 Check_Type = 0 构件 2 Check_Type = 1 零件\r\n    //        console.log(\"3\", optionsGroupListTemp)\r\n    //        this.form.Type == 2  && optionsGroupListTemp &&  (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Object_Id === \"2\").length > 0 ? optionsGroupListTemp.filter(v => v.Check_Object_Id === \"2\") : optionsGroupListTemp)\r\n    //       console.log(\"4\", optionsGroupListTemp,this.form.Is_Need_TC)\r\n    //       this.form.Is_Need_ZL && optionsGroupListTemp && (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Type === 1 ).length > 0 ? optionsGroupListTemp.filter(v => v.Check_Type === 1 || v.Check_Type === -1) :  optionsGroupListTemp.filter(v => v.Check_Type === -1))  //通过质检类型筛选\r\n    //       this.form.Type == 1  && optionsGroupListTemp &&  (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Object_Id === \"1\").length > 0 ? optionsGroupListTemp.filter(v =>v.Check_Object_Id === \"1\") : optionsGroupListTemp) //通过工序筛选 Type= 1 Check_Type = 0 构件 2 Check_Type = 1 零件\r\n    //       console.log(\"5\", optionsGroupListTemp)\r\n    //       this.form.Type == 2  && optionsGroupListTemp &&  (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Object_Id === \"2\").length > 0 ? optionsGroupListTemp.filter(v => v.Check_Object_Id === \"2\") : optionsGroupListTemp)\r\n    //       console.log(\"6\", optionsGroupListTemp,this.form.Is_Need_ZL)\r\n    //      }\r\n    //   }\r\n\r\n    //   this.optionsGroupList = optionsGroupListTemp\r\n\r\n    //   console.log(\"optionsGroupListTemp\",optionsGroupListTemp)\r\n    //   console.log(\"SelectioncheckCombination\",this.optionsGroupList)\r\n\r\n    // },\r\n    radioCheckStyleChange(val) {\r\n      // if (val === '0') {\r\n      //   this.form.Is_Need_TC = true\r\n      //   this.form.Is_Need_ZL = false\r\n      // }\r\n      this.ZL_Check_UserIds = []\r\n      this.TC_Check_UserIds = []\r\n      this.form.ZL_Check_UserId = ''\r\n      this.form.TC_Check_UserId = ''\r\n    },\r\n    // radioCheckTypeChange(val) {\r\n    //   if (val) {\r\n    //     this.form.Is_Need_TC = true\r\n    //     this.form.Is_Need_ZL = false\r\n    //     this.ZL_Check_UserIds = []\r\n    //     this.form.ZL_Check_UserId = ''\r\n    //   } else {\r\n    //     this.form.Is_Need_ZL = true\r\n    //     this.form.Is_Need_TC = false\r\n    //     this.TC_Check_UserIds = []\r\n    //     this.form.TC_Check_UserId = ''\r\n    //   }\r\n    //   // this.ZL_Check_UserIds = [];\r\n    //   // this.TC_Check_UserIds = [];\r\n    //   // this.form.ZL_Check_UserId = \"\";\r\n    //   // this.form.TC_Check_UserId = \"\";\r\n    // },\r\n    radioChange(val) {\r\n      if (val == false) {\r\n        this.form.checkChange = false\r\n        this.form.Is_Need_TC = false\r\n        this.form.Is_Need_ZL = false\r\n        this.TC_Check_UserIds = []\r\n        this.ZL_Check_UserIds = []\r\n        this.form.ZL_Check_UserId = ''\r\n        this.form.TC_Check_UserId = ''\r\n        this.form.Check_Style = ''\r\n      } else {\r\n        // this.form.checkChange = true\r\n        // this.form.Is_Need_TC = true\r\n        // this.form.Is_Need_ZL = false\r\n        // this.CheckChange = !!this.form.Is_Need_TC\r\n        this.form.Check_Style = '0'\r\n      }\r\n    },\r\n    // 选择构件工序\r\n    changeType(val) {\r\n      // this.SelectioncheckCombination()\r\n      // const Code = val\r\n      // console.log(Code, 'Code');\r\n      // if (Code === '-1') {\r\n      //   this.form.Type = 1\r\n      // } else if (Code === '0') {\r\n      //   this.form.Type = 2\r\n      // } else if (Code === '1' || Code === '3'|| Code === '2') {\r\n      //   this.form.Type = 3\r\n      // }\r\n      // if (this.form.Type === 1 || this.form.Type === 3) {\r\n      //   this.form.Is_Cutting = undefined\r\n      // } else if (this.form.Type === 2) {\r\n      //   this.form.Is_Welding_Assembling = undefined\r\n      // }\r\n    },\r\n    typeChange() {\r\n      this.form.Task_Model = ''\r\n    },\r\n    changeTc(val) {\r\n      console.log(val)\r\n      this.form.TC_Check_UserId = ''\r\n      for (let i = 0; i < val.length; i++) {\r\n        if (i == val.length - 1) {\r\n          this.form.TC_Check_UserId += val[i]\r\n        } else {\r\n          this.form.TC_Check_UserId += val[i] + ','\r\n        }\r\n      }\r\n      console.log(this.form.TC_Check_UserId, 'this.form.TC_Check_UserId ')\r\n    },\r\n    changeZL(val) {\r\n      console.log(val)\r\n      this.form.ZL_Check_UserId = ''\r\n      for (let i = 0; i < val.length; i++) {\r\n        if (i == val.length - 1) {\r\n          this.form.ZL_Check_UserId += val[i]\r\n        } else {\r\n          this.form.ZL_Check_UserId += val[i] + ','\r\n        }\r\n      }\r\n    },\r\n    checkboxChange(val, type) {\r\n      if (type === 1) {\r\n        if (!val) {\r\n          this.TC_Check_UserIds = []\r\n        }\r\n      }\r\n      if (type === 2) {\r\n        if (!val) {\r\n          this.ZL_Check_UserIds = []\r\n        }\r\n      }\r\n    },\r\n    handleSubmit() {\r\n      // delete this.form.Types\r\n      console.log(this.form, 'this.form')\r\n      this.$refs.form.validate((valid) => {\r\n        if (!valid) return\r\n        this.btnLoading = true\r\n        const uItems = this.optionsUserList.find(\r\n          (v) => v.Id === this.form.Coordinate_UserId\r\n        )\r\n        if (uItems) {\r\n          this.form.Coordinate_UserName = uItems.Display_Name\r\n        }\r\n        if (this.form.Is_Need_Check) {\r\n          if (this.form.Is_Need_ZL == false && this.form.Is_Need_TC == false) {\r\n            this.$message.error('请选择质检类型')\r\n            this.btnLoading = false\r\n            return\r\n          }\r\n        } else {\r\n          this.form.Check_Style = null\r\n          this.form.Check_Group_List = []\r\n        }\r\n        const ZL = this.form.Is_Need_ZL ? this.form.ZL_Check_UserId : ''\r\n        const TC = this.form.Is_Need_TC ? this.form.TC_Check_UserId : ''\r\n        if (this.form.Is_Need_ZL && (ZL ?? '') == '') {\r\n          this.$message.error('请选择质检员')\r\n          this.btnLoading = false\r\n          return\r\n        }\r\n        if (this.form.Is_Need_TC && (TC ?? '') == '') {\r\n          this.$message.error('请选择探伤员')\r\n          this.btnLoading = false\r\n          return\r\n        }\r\n\r\n        AddWorkingProcess({\r\n          ...this.form,\r\n          ZL_Check_UserId: ZL,\r\n          TC_Check_UserId: TC\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            this.$emit('refresh')\r\n            this.$emit('close')\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.btnLoading = false\r\n        })\r\n      })\r\n    },\r\n\r\n    codeChange(e) {\r\n      return e.replace(/[^a-zA-Z0-9]/g, '')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/styles/mixin.scss\";\r\n\r\n.btn-del {\r\n  margin-left: -100px;\r\n}\r\n\r\n.customRadioClass {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.checkboxFlex {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.form-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  min-height: 40vh;\r\n\r\n  .form-x {\r\n    max-height: 70vh;\r\n    overflow: auto;\r\n    padding-right: 16px;\r\n    @include scrollBar;\r\n  }\r\n\r\n  .btn-x {\r\n    padding-top: 16px;\r\n    text-align: right;\r\n  }\r\n}\r\n</style>\r\n"]}]}
<template>
  <div style="height: 100%">
    <div class="cs-bottom-wapper">
      <div class="fff tb-x">
        <vxe-table
          :empty-render="{name: 'NotData'}"
          show-header-overflow
          :loading="loading"
          element-loading-spinner="el-icon-loading"
          element-loading-text="拼命加载中"
          empty-text="暂无数据"
          class="cs-vxe-table"
          height="100%"
          align="left"
          stripe
          :data="tbData"
          resizable
          :tooltip-config="{ enterable: true}"
          :checkbox-config="{checkField: 'checked', trigger: 'row'}"
          :row-config="{ isHover: true }"
          @checkbox-all="multiSelectedChange"
          @checkbox-change="multiSelectedChange"
        >
          <vxe-column fixed="left" type="checkbox" width="44" />
          <template v-for="item in columns">
            <vxe-column
              :key="item.Code"
              :min-width="item.Width"
              :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
              show-overflow="tooltip"
              sortable
              :align="item.Align"
              :field="item.Code"
              :title="item.Display_Name"
            >
              <template v-if="item.Code === 'Rectifier_Code' " #default="{ row }">
                <el-button
                  v-if="Boolean(row.Rectifier_Code)"
                  type="text"
                  @click="handelrectifiction(row)"
                >查看</el-button>
                <span v-else>-</span>
              </template>
              <template v-if="item.Code === 'Check_Result'" #default="{ row }">
                <span v-if="!row.Check_Result">-</span>
                <template v-else>
                  <el-tag v-if="row.Check_Result==='合格'" type="success">{{ row.Check_Result }}</el-tag>
                  <el-tag v-else type="danger">{{ row.Check_Result }}</el-tag>
                </template>
              </template>
              <template v-else-if="item.Code === 'Status'" #default="{ row }">
                <span v-if="row.Status === '已完成'" class="by-dot by-dot-success">
                  {{ row.Status || "-" }}
                </span>
                <span v-else-if="row.Status === '待复核' || row.Status === '待整改'" class="by-dot by-dot-primary">
                  {{ row.Status || "-" }}
                </span>
                <span v-else-if="row.Status === '待质检' || row.Status === '草稿'" class="by-dot by-dot-info">
                  {{ row.Status || "-" }}
                </span>
                <span v-else>
                  {{ row.Status || "-" }}
                </span>
              </template>
              <template v-else #default="{ row }">
                <span>{{ (row[item.Code]==='' || row[item.Code] == null) ? '-' : row[item.Code] }}</span>
              </template>
            </vxe-column>
          </template>
          <vxe-column fixed="right" title="操作" align="center" width="60">
            <template #default="{ row }">
              <el-button
                v-if="row.Status !== '待质检' && row.Status !== '草稿'"
                type="text"
                @click="handleInfo(row.SheetId)"
              >查看</el-button>
              <el-button
                v-if="row.Status === '待质检'"
                type="text"
                @click="handelWaitInspect(row)"
              >查看</el-button>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <div class="data-info">
        <el-tag
          size="medium"
          class="info-x"
        >已选 {{ selectList.length }} 条数据
        </el-tag>
        <Pagination
          :total="total"
          :page-sizes="tablePageSize"
          :page.sync="queryInfo.Page"
          :limit.sync="queryInfo.PageSize"
          @pagination="pageChange"
        />
      </div>
    </div>
    <el-dialog
      v-if="dialogVisible"
      ref="content"
      v-el-drag-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :width="width"
      class="plm-custom-dialog"
      @close="handleClose"
    >
      <component :is="currentComponent" ref="content" @close="handleClose" />
    </el-dialog>
  </div>
</template>

<script>
import addDialog from '../../start-inspect/components/add/addDialog.vue'
import rectificationDialog from './rectification/rectificationDialog'

import {
  GetPageQualitySummary,
  RectificationRecord
} from '@/api/PRO/qualityInspect/start-Inspect'
import DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'
import elDragDialog from '@/directive/el-drag-dialog'
import { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'
import { timeFormat } from '@/filters'
import addRouterPage from '@/mixins/add-router-page'
import { ExportInspsectionSummaryInfo } from '@/api/PRO/factorycheck'
import { combineURL } from '@/utils'
import Pagination from '@/components/Pagination/index.vue'
import { tablePageSize } from '@/views/PRO/setting'
import getTbInfo from '@/mixins/PRO/get-table-info'
export default {
  directives: { elDragDialog },
  components: {
    Pagination,
    DynamicDataTable,
    rectificationDialog,
    addDialog
  },
  mixins: [addRouterPage, getTbInfo],
  props: {
    searchDetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      selectList: [],
      width: '60%',
      code: '',
      TypeId: '',
      typeOption: '',
      dialogVisible: false,
      dialogVisible2: false,
      loading: false,
      dialogTitle: '',
      Ismodal: true,
      dialogData: {},
      currentComponent: '',
      tbConfig: {},
      Data: [],
      Date_Time: '',
      columns: [],
      tbData: [],
      queryInfo: {
        Page: 1,
        PageSize: tablePageSize[0]
      },
      total: 0,
      tablePageSize: tablePageSize,
      gridCode: 'Pro_Inpection_summary_list',
      searchHeight: 0,
      CheckResultData: [],
      CheckNodeList: [], // 质检节点
      CheckObjectData: [], // 质检对象
      check_object_id: '',
      ProjectNameData: [],
      check_object_Name: '',
      addPageArray: [
        {
          path: this.$route.path + '/check',
          hidden: true,
          component: () =>
            import(
              '@/views/PRO/quality_Inspection/quality_summary/components/Detail'
            ),
          name: 'PROQualityInfoSummary',
          meta: { title: '查看' }
        }
      ]
    }
  },
  mounted() {
    this.getTypeList()
  },
  methods: {
    handleSearch() {},
    async getTypeList() {
      let res, data
      res = await GetFactoryProfessionalByCode({
        factoryId: localStorage.getItem('CurReferenceId')
      })
      data = res.Data
      if (res.IsSucceed) {
        this.typeOption = Object.freeze(data)
        console.log(this.typeOption)
        if (this.typeOption.length > 0) {
          this.TypeId = this.typeOption[0]?.Id
          this.code = this.typeOption.find((i) => i.Id === this.TypeId).Code
          this.getTableConfig(this.gridCode + ',' + this.code)
        }
        this.fetchData()
      } else {
        this.$message({
          message: res.Message,
          type: 'error'
        })
      }
    },

    // 待质检查看
    handelWaitInspect(row) {
      this.generateComponent('查看', 'addDialog')
      this.width = '500px'
      this.$nextTick((_) => {
        this.$refs['content'].init('查看', row)
      })
    },
    fetchData(page) {
      page && (this.queryInfo.Page = page)
      const SeachParams = JSON.parse(JSON.stringify(this.searchDetail))
      const SteelName = SeachParams.SteelName.trim().replaceAll(' ', '\n')
      if (SeachParams.Pick_Date && SeachParams.Pick_Date.length === 2) {
        SeachParams.BeginDate = SeachParams.Pick_Date[0]
        SeachParams.EndDate = SeachParams.Pick_Date[1]
      } else {
        SeachParams.BeginDate = null
        SeachParams.EndDate = null
      }
      this.loading = true
      GetPageQualitySummary({
        pageInfo: this.queryInfo,
        ...SeachParams,
        SteelName,
        Check_Style: 1
      })
        .then((res) => {
          if (res.IsSucceed) {
            return this.setGridData(res.Data)
          }
        })
        .catch(console.error)
        .finally(() => {
          // 结束loading
          this.loading = false
        })
    },

    setGridData(data) {
      this.tbData = this.tbData = data.Data.map((v) => {
        v.Id = v.SheetId
        v.Rectify_Date = v.Rectify_Date
          ? timeFormat(v.Rectify_Date, '{y}-{m}-{d}')
          : '-'
        v.Pick_Date = v.Pick_Date
          ? timeFormat(v.Pick_Date, '{y}-{m}-{d}')
          : '-'
        return v
      })
      this.total = data.TotalCount
    },

    multiSelectedChange(array) {
      this.selectList = array.records
      this.$emit('selectChange', this.selectList)
    },
    generateComponent(title, component) {
      this.dialogTitle = title
      this.currentComponent = component
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false
      this.fetchData(1)
    },
    getrectificationRecord(row) {
      RectificationRecord({ sheetid: row.SheetId }).then((res) => {
        if (res.IsSucceed) {
          if (res.Data.length === 0) {
            this.$message({
              type: 'error',
              message: '暂无操作记录'
            })
          } else {
            this.generateComponent('整改记录', 'rectificationDialog')
            this.$nextTick((_) => {
              this.$refs['content'].init(row)
            })
          }
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    handelrectifiction(row) {
      this.getrectificationRecord(row)
    },
    handleInfo(sheetId) {
      this.$router.push({
        name: 'PROQualityInfoSummary',
        query: { pg_redirect: this.$route.name, sheetId, isCheck: true }
      })
    },
    exportTb() {
      const SheetIds = this.selectList.map((v) => v.SheetId)
      this.$emit('setExportLoading', true)
      const SeachParams = JSON.parse(JSON.stringify(this.searchDetail))
      const SteelName = SeachParams.SteelName.trim().replaceAll(' ', '\n')
      if (SeachParams.Pick_Date && SeachParams.Pick_Date.length === 2) {
        SeachParams.BeginDate = SeachParams.Pick_Date[0]
        SeachParams.EndDate = SeachParams.Pick_Date[1]
      } else {
        SeachParams.BeginDate = null
        SeachParams.EndDate = null
      }
      ExportInspsectionSummaryInfo({
        pageInfo: this.queryInfo,
        ...SeachParams,
        SteelName,
        Check_Style: 1,
        SheetIds
      }).then(res => {
        if (res.IsSucceed) {
          window.open(combineURL(this.$baseUrl, res.Data), '_blank')
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
        this.$emit('setExportLoading', false)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
@import "~@/styles/variables.scss";
.search_wrapper {
  padding: 16px 16px 0;
  box-sizing: border-box;
  ::v-deep .el-form-item {
    .el-form-item__content {
      & > .el-input {
        width: 220px;
      }
      & > .el-select {
        width: 220px;
      }
    }
  }
}

.cs-bottom-wapper {
  padding: 0 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  .tb-x {
    flex: 1;
    height: 0;
  }

  .pagination-container {
    text-align: right;
    padding: 16px;
    margin: 0;
  }

  .data-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.by-dot {
  display: flex;
  align-items: center;
  justify-content: center;
  &:before {
    content: "";
    display: inline-block;
    width: 5px;
    height: 5px;
    background: #f56c6c;
    border-radius: 50%;
    margin-right: 5px;
  }
}
.by-dot-success {
  color: #67c23a;
  &:before {
    background: #67c23a;
  }
}
.by-dot-primary {
  color: #409eff;
  &:before {
    background: #409eff;
  }
}
.by-dot-fail {
  color: #ff0000;
  &:before {
    background: #ff0000;
  }
}
.by-dot-info {
  &:before {
    background: #909399;
  }
}
</style>

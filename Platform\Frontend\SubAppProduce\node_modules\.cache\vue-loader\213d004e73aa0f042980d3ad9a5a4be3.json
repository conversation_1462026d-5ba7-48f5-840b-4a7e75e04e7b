{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-product-type\\component\\ProjectAdd.vue?vue&type=template&id=1aabd240&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-product-type\\component\\ProjectAdd.vue", "mtime": 1757468128033}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImFkZC1wcm9qZWN0LWNvbnRhaW5lciI+CiAgPCEtLSDmkJzntKLljLrln58gLS0+CiAgPGRpdiBjbGFzcz0ic2VhcmNoLXNlY3Rpb24iPgogICAgPGVsLWZvcm0gOm1vZGVsPSJzZWFyY2hGb3JtIiBpbmxpbmU+CiAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9Iumhueebrue8luWPt++8miI+CiAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICB2LW1vZGVsPSJzZWFyY2hGb3JtLlByb2plY3RDb2RlIgogICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpSIKICAgICAgICAgIGNsZWFyYWJsZQogICAgICAgICAgc3R5bGU9IndpZHRoOiAyMDBweCIKICAgICAgICAvPgogICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i6aG555uu566A56ew77yaIj4KICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgIHYtbW9kZWw9InNlYXJjaEZvcm0uUHJvamVjdEFiYnIiCiAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWlIgogICAgICAgICAgY2xlYXJhYmxlCiAgICAgICAgICBzdHlsZT0id2lkdGg6IDIwMHB4IgogICAgICAgIC8+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICA8ZWwtZm9ybS1pdGVtPgogICAgICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgQGNsaWNrPSJoYW5kbGVTZWFyY2giPuaQnOe0ojwvZWwtYnV0dG9uPgogICAgICAgIDxlbC1idXR0b24gQGNsaWNrPSJoYW5kbGVSZXNldCI+6YeN572uPC9lbC1idXR0b24+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgPC9lbC1mb3JtPgogIDwvZGl2PgoKICA8IS0tIOaPkOekuuS/oeaBryAtLT4KICA8ZGl2IGNsYXNzPSJpbnN0cnVjdGlvbiI+CiAgICDor7fpgInmi6npobnnm64s5re75Yqg5omA6YCJ6aG555uu55qE5omA5pyJ5Lqn5ZOB57G75Z6LCiAgPC9kaXY+CgogIDwhLS0g6aG555uu6KGo5qC8IC0tPgogIDxkaXYgY2xhc3M9InRhYmxlLXNlY3Rpb24iPgogICAgPGJ0LXRhYmxlCiAgICAgIHJlZj0icHJvamVjdFRhYmxlIgogICAgICBjb2RlPSJBZGRQcm9qZWN0TGlzdCIKICAgICAgOmN1c3RvbS10YWJsZS1jb25maWc9InRhYmxlQ29uZmlnIgogICAgICA6Z3JpZC1kYXRhLWhhbmRsZXI9ImhhbmRsZUdyaWREYXRhIgogICAgICA6bG9hZGluZz0ibG9hZGluZyIKICAgICAgQHNlbGVjdGlvbi1jaGFuZ2U9ImhhbmRsZVNlbGVjdGlvbkNoYW5nZSIKICAgICAgQHJvdy1jbGljaz0iaGFuZGxlUm93Q2xpY2siCiAgICAvPgogIDwvZGl2PgoKICA8IS0tIOW6lemDqOaMiemSriAtLT4KICA8ZGl2IGNsYXNzPSJmb290ZXItYWN0aW9ucyI+CiAgICA8ZWwtYnV0dG9uIEBjbGljaz0iaGFuZGxlQ2FuY2VsIj7lj5bmtog8L2VsLWJ1dHRvbj4KICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgOmRpc2FibGVkPSJzZWxlY3RlZFByb2plY3RzLmxlbmd0aCA9PT0gMCIgQGNsaWNrPSJoYW5kbGVDb25maXJtIj4KICAgICAg56Gu5a6aCiAgICA8L2VsLWJ1dHRvbj4KICA8L2Rpdj4KPC9kaXY+Cg=="}, null]}
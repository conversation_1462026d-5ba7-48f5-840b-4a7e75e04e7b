{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\OwnerProcess.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\OwnerProcess.vue", "mtime": 1758286724448}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetProcessListBase", "getBomName", "props", "pageType", "type", "String", "default", "undefined", "partTypeOption", "Array", "isPartPrepare", "Boolean", "hasUnitPart", "partName", "data", "itemOption", "key", "value", "form", "loading", "btnLoading", "OwnerOption", "rules", "methods", "submit", "_this", "$refs", "validate", "valid", "result", "list", "for<PERSON>ach", "item", "levelInfo", "curLevel", "level", "tType", "itemInfo", "find", "v", "console", "log", "Scheduled_Used_Process", "$message", "message", "concat", "push", "obj", "Use_Process_Id", "Bom_Level", "Use_Process_Code", "curProcess", "option", "Code", "Id", "Use_Process_List", "Part_Used_Process", "filter", "toString", "handleClose", "setOption", "isInline", "arr", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "levelMap", "_iterator", "_step", "lvl", "suffix", "wrap", "_callee$", "_context", "prev", "next", "levelList", "map", "Type", "Belong_To_Component", "Parent_Level", "_toConsumableArray", "Set", "split", "_createForOfIteratorHelper", "s", "n", "done", "sent", "t0", "e", "f", "finish", "reduce", "acc", "cur", "levelCode", "partOwnerName", "<PERSON><PERSON><PERSON>", "code", "label", "Type_Name", "$set", "fetchData", "length", "pUsedProcess", "includes", "stop", "getComOption", "_this3", "_listMap", "keyArr", "getProcessItem", "_option", "Is_Enable", "element", "parentPath", "componentProcess", "c", "findIndex", "partUsedProcess", "_fp", "flag", "Part_Type_Used_Process", "_loop", "i", "JSON", "parse", "stringify", "keys", "idx", "belongType", "_this4", "_callee3", "hasTrueCompPart", "hasFalseCompPart", "fetchDataForType", "levels", "tasks", "results", "_callee3$", "_context3", "some", "_ref", "_callee2", "bomLevel", "res", "_callee2$", "_context2", "IsSucceed", "abrupt", "Data", "Message", "_x", "apply", "arguments", "Promise", "all", "flat", "$emit"], "sources": ["src/views/PRO/plan-production/schedule-production-new-part/components/OwnerProcess.vue"], "sourcesContent": ["<template>\r\n  <div v-loading=\"loading\">\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"140px\">\r\n      <el-form-item v-for=\"(element) in itemOption\" :key=\"element.tType\" :label=\"element.label\" prop=\"ownerProcess\">\r\n        <el-select v-model=\"element.value\" clearable class=\"w100\" placeholder=\"请选择\">\r\n          <template>\r\n            <el-option\r\n              v-for=\"(item) in OwnerOption[element.tType]\"\r\n              :key=\"item.tType\"\r\n              :label=\"item.Name\"\r\n              :value=\"item.Code\"\r\n            />\r\n          </template>\r\n        </el-select>\r\n      </el-form-item>\r\n    </el-form>\r\n    <div class=\"dialog-footer\">\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"submit\">确 定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { GetProcessListBase } from '@/api/PRO/technology-lib'\r\nimport { getBomName } from '@/views/PRO/bom-setting/utils'\r\n\r\nexport default {\r\n  props: {\r\n    pageType: {\r\n      type: String,\r\n      default: undefined\r\n    },\r\n    partTypeOption: {\r\n      type: Array,\r\n      default: () => ([])\r\n    },\r\n    isPartPrepare: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    hasUnitPart: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    partName: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      itemOption: [{\r\n        key: '',\r\n        value: ''\r\n      }],\r\n      form: {\r\n      },\r\n      loading: false,\r\n      btnLoading: false,\r\n      OwnerOption: {},\r\n      rules: {\r\n        // ownerProcess: [\r\n        //   { required: true, message: '请选择车间', trigger: 'change' }\r\n        // ]\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    submit() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (valid) {\r\n          const result = []\r\n          this.list.forEach(item => {\r\n            const levelInfo = []\r\n            item.curLevel.forEach(level => {\r\n              const tType = item.tType + '_' + level\r\n              const itemInfo = this.itemOption.find(v => v.tType === tType)\r\n              console.log('submit item', item)\r\n              console.log('submit this.itemOption', this.itemOption)\r\n              console.log('submit itemInfo', itemInfo)\r\n              if (itemInfo) {\r\n                if (item.Scheduled_Used_Process && item.Scheduled_Used_Process !== itemInfo.value) {\r\n                  this.$message({\r\n                    message: `请和该区域批次下已排产同${this.partName}领用工序保持一致`,\r\n                    type: 'warning'\r\n                  })\r\n                } else {\r\n                  result.push(itemInfo.value)\r\n                  const obj = {\r\n                    Use_Process_Id: '',\r\n                    Bom_Level: '',\r\n                    Use_Process_Code: ''\r\n                  }\r\n                  const curProcess = this.option.find(v => v.Code === itemInfo.value)\r\n                  obj.Use_Process_Id = curProcess.Id\r\n                  obj.Bom_Level = level\r\n                  obj.Use_Process_Code = itemInfo.value\r\n                  levelInfo.push(obj)\r\n                }\r\n              }\r\n            })\r\n            item.Use_Process_List = levelInfo\r\n            console.log('submit item.levelInfo', item.Use_Process_List)\r\n            item.Part_Used_Process = result.filter(v => !!v).toString()\r\n          })\r\n          this.btnLoading = false\r\n          this.handleClose()\r\n        } else {\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    async setOption(isInline, arr) {\r\n      // console.log(7, this.isPartPrepare)\r\n      this.list = arr || []\r\n      this.levelList = []\r\n      this.list = this.list.map(item => {\r\n        item.tType = item.Type + '&' + item.Belong_To_Component\r\n        if (item.Parent_Level) {\r\n          item.curLevel = [...new Set(item.Parent_Level.split(','))]\r\n        }\r\n        this.levelList = this.levelList.concat(item.curLevel)\r\n        return item\r\n      })\r\n      this.levelList = [...new Set(this.levelList)]\r\n      const levelMap = {}\r\n      for (const lvl of this.levelList) {\r\n        levelMap[lvl] = await getBomName(lvl)\r\n      }\r\n      console.log('this.levelMap', levelMap)\r\n      console.log('this.levelList', this.levelList)\r\n      console.log('this.list', this.list)\r\n      console.log('this.hasUnitPart', this.hasUnitPart)\r\n      this.isInline = isInline\r\n      const suffix = '领用工序'\r\n      const obj = {}\r\n      this.itemOption = this.list.reduce((acc, cur) => {\r\n        cur.curLevel.forEach(levelCode => {\r\n          const partOwnerName = this.hasUnitPart ? (levelMap[levelCode]) : ''\r\n          console.log('partOwnerName', partOwnerName)\r\n          const uniqueKey = cur.tType + '_' + levelCode\r\n          if (!obj[uniqueKey] && cur.Type !== 'Direct') {\r\n            acc.push({\r\n              code: cur.Type,\r\n              label: (cur.Type_Name || '') + partOwnerName + suffix,\r\n              value: '',\r\n              tType: uniqueKey\r\n            })\r\n            this.$set(this.OwnerOption, uniqueKey, [])\r\n          }\r\n          obj[uniqueKey] = true\r\n        })\r\n\r\n        return acc\r\n      }, [])\r\n      console.log('obj', obj)\r\n      console.log('this.itemOption', this.itemOption)\r\n      await this.fetchData()\r\n      if (isInline && arr.length) {\r\n        arr.forEach(item => {\r\n          item.curLevel.forEach(level => {\r\n            const tType = item.tType + '_' + level\r\n            const itemInfo = this.itemOption.find(v => v.tType === tType)\r\n            if (itemInfo) {\r\n              const pUsedProcess = item.Part_Used_Process.split(',')\r\n              console.log('pUsedProcess', pUsedProcess)\r\n              console.log('this.OwnerOption[tType]', this.OwnerOption[tType])\r\n              const cur = this.OwnerOption[tType].find(v => pUsedProcess.includes(v.Code))\r\n              console.log('cur', cur)\r\n              itemInfo.value = cur?.Code || ''\r\n            }\r\n          })\r\n        })\r\n        // const cur = arr[0]\r\n        // const itemInfo = this.itemOption.find(v => v.tType === cur.tType)\r\n        // if (itemInfo) {\r\n        //   itemInfo.value = cur.Part_Used_Process\r\n        // }\r\n      }\r\n    },\r\n    getComOption() {\r\n      const _listMap = {}\r\n      const keyArr = []\r\n      const getProcessItem = (code) => this.option.find(v => v.Code === code)\r\n      const _option = this.option.filter(v => v.Is_Enable)\r\n      console.log('thi-s.list', this.list)\r\n      this.list.forEach((element) => {\r\n        element.curLevel.forEach(level => {\r\n          const key = element.tType + '_' + level\r\n          keyArr.push(key)\r\n          if (!_listMap[key]) {\r\n            _listMap[key] = []\r\n          }\r\n          let parentPath = 'Component_Technology_Path'\r\n          if (level === '-1') {\r\n            parentPath = 'Component_Technology_Path'\r\n          } else if (level === '1') {\r\n            parentPath = 'SubAssembly_Technology_Path'\r\n          } else if (level === '2') {\r\n            parentPath = 'SubAssembly_Technology_Path'\r\n          } else if (level === '3') {\r\n            parentPath = 'SubAssembly_Technology_Path'\r\n          }\r\n          // if (this.hasUnitPart) {\r\n          //   if (element.Belong_To_Component) {\r\n          //     parentPath = 'Component_Technology_Path'\r\n          //   } else {\r\n          //     parentPath = 'SubAssembly_Technology_Path'\r\n          //   }\r\n          // }\r\n          element[parentPath] = element[parentPath] || ''\r\n\r\n          if (element.Scheduled_Used_Process) {\r\n            const item = getProcessItem(element.Scheduled_Used_Process)\r\n            if (item) {\r\n              _listMap[key].push(item)\r\n            }\r\n          } else {\r\n            const componentProcess = element[parentPath].split('/').filter(v => !!v)\r\n            // const processItem = this.option.find(v => v.Code === element.Temp_Part_Used_Process)\r\n            console.log(666)\r\n\r\n            if (componentProcess.length) {\r\n            // if (element.Temp_Part_Used_Process && componentProcess.includes(element.Temp_Part_Used_Process)) {\r\n            //   _listMap[key].push(processItem)\r\n            // } else {\r\n            //   componentProcess.forEach(c => {\r\n            //     const cur = getProcessItem(c)\r\n            //     if (cur) {\r\n            //       _listMap[key].push(cur)\r\n            //     }\r\n            //   })\r\n            // }\r\n              componentProcess.forEach(c => {\r\n                const cur = getProcessItem(c)\r\n                console.log('cu2r', cur, _listMap[key])\r\n                if (cur) {\r\n                  if (_listMap[key].findIndex(v => v.Code === cur.Code) !== -1) {\r\n                    return\r\n                  }\r\n                  _listMap[key].push(cur)\r\n                }\r\n              })\r\n            } else {\r\n              console.log(777)\r\n              const partUsedProcess = element.Part_Used_Process\r\n              const _fp = this.option.filter(item => {\r\n                let flag = false\r\n                if (partUsedProcess && partUsedProcess === item.Code) {\r\n                  flag = true\r\n                }\r\n                if (element.Part_Type_Used_Process && element.Part_Type_Used_Process === item.Code) {\r\n                  flag = true\r\n                }\r\n                if (!flag) {\r\n                  flag = !!item.Is_Enable\r\n                }\r\n                console.log('item.Bom_Level', item.Bom_Level, level)\r\n                if (item.Bom_Level.toString() !== level.toString()) {\r\n                  flag = false\r\n                }\r\n                return flag\r\n              })\r\n              for (let i = 0; i < _fp.length; i++) {\r\n                const cur = _fp[i]\r\n                if (_listMap[key].findIndex(v => v.Code === cur.Code) !== -1) {\r\n                  continue\r\n                }\r\n                _listMap[key].push(cur)\r\n              }\r\n            }\r\n          }\r\n        })\r\n      })\r\n      console.log('this.OwnerOption', JSON.parse(JSON.stringify(_listMap)))\r\n      if (this.isInline) {\r\n        this.OwnerOption = _listMap\r\n      } else {\r\n        keyArr.forEach((keys, idx) => {\r\n          const belongType = keys.split('&')[1] === 'true' ? 1 : 3\r\n          this.OwnerOption[keys] = _option.filter(v => v.Type === belongType)\r\n        })\r\n      }\r\n    },\r\n    async fetchData() {\r\n      this.loading = true\r\n\r\n      let hasTrueCompPart\r\n      let hasFalseCompPart\r\n      if (this.hasUnitPart) {\r\n        hasTrueCompPart = this.list.some(v => !!v.Belong_To_Component)\r\n        hasFalseCompPart = this.list.some(v => !v.Belong_To_Component)\r\n      } else {\r\n        hasTrueCompPart = true\r\n        hasFalseCompPart = false\r\n      }\r\n      const fetchDataForType = async(bomLevel) => {\r\n        try {\r\n          const type = bomLevel === -1 ? 1 : 3\r\n          const res = await GetProcessListBase({ type, Bom_Level: bomLevel })\r\n          if (res.IsSucceed) {\r\n            return res.Data\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            return []\r\n          }\r\n        } catch (error) {\r\n          this.$message({\r\n            message: `请求失败`,\r\n            type: 'error'\r\n          })\r\n          return []\r\n        }\r\n      }\r\n\r\n      try {\r\n        const levels = [...new Set(this.list.reduce((acc, cur) => acc.concat(cur.curLevel), []))]\r\n        console.log('levels', levels)\r\n        const tasks = []\r\n        levels.forEach(level => {\r\n          tasks.push(fetchDataForType(level))\r\n        })\r\n        let results = []\r\n        results = await Promise.all(tasks)\r\n        console.log('results', results)\r\n        // let results = []\r\n\r\n        // if (hasTrueCompPart && hasFalseCompPart) {\r\n        //   results = await Promise.all([fetchDataForType(1), fetchDataForType(3)])\r\n        // } else if (hasTrueCompPart) {\r\n        //   results = [await fetchDataForType(1)]\r\n        // } else if (hasFalseCompPart) {\r\n        //   results = [await fetchDataForType(3)]\r\n        // }\r\n\r\n        this.option = results.filter(data => !!data.length).flat()\r\n        console.log('this.option', this.option)\r\n\r\n        this.getComOption()\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    handleClose() {\r\n      this.$emit('close')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.dialog-footer{\r\n  text-align: right;\r\n  margin-top: 30px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,SAAAA,kBAAA;AACA,SAAAC,UAAA;AAEA;EACAC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,EAAAC;IACA;IACAC,cAAA;MACAJ,IAAA,EAAAK,KAAA;MACAH,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAI,aAAA;MACAN,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAM,WAAA;MACAR,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAO,QAAA;MACAT,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAQ,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;QACAC,GAAA;QACAC,KAAA;MACA;MACAC,IAAA,GACA;MACAC,OAAA;MACAC,UAAA;MACAC,WAAA;MACAC,KAAA;QACA;QACA;QACA;MAAA;IAEA;EACA;EACAC,OAAA;IACAC,MAAA,WAAAA,OAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,MAAA;UACAJ,KAAA,CAAAK,IAAA,CAAAC,OAAA,WAAAC,IAAA;YACA,IAAAC,SAAA;YACAD,IAAA,CAAAE,QAAA,CAAAH,OAAA,WAAAI,KAAA;cACA,IAAAC,KAAA,GAAAJ,IAAA,CAAAI,KAAA,SAAAD,KAAA;cACA,IAAAE,QAAA,GAAAZ,KAAA,CAAAV,UAAA,CAAAuB,IAAA,WAAAC,CAAA;gBAAA,OAAAA,CAAA,CAAAH,KAAA,KAAAA,KAAA;cAAA;cACAI,OAAA,CAAAC,GAAA,gBAAAT,IAAA;cACAQ,OAAA,CAAAC,GAAA,2BAAAhB,KAAA,CAAAV,UAAA;cACAyB,OAAA,CAAAC,GAAA,oBAAAJ,QAAA;cACA,IAAAA,QAAA;gBACA,IAAAL,IAAA,CAAAU,sBAAA,IAAAV,IAAA,CAAAU,sBAAA,KAAAL,QAAA,CAAApB,KAAA;kBACAQ,KAAA,CAAAkB,QAAA;oBACAC,OAAA,6EAAAC,MAAA,CAAApB,KAAA,CAAAZ,QAAA;oBACAT,IAAA;kBACA;gBACA;kBACAyB,MAAA,CAAAiB,IAAA,CAAAT,QAAA,CAAApB,KAAA;kBACA,IAAA8B,GAAA;oBACAC,cAAA;oBACAC,SAAA;oBACAC,gBAAA;kBACA;kBACA,IAAAC,UAAA,GAAA1B,KAAA,CAAA2B,MAAA,CAAAd,IAAA,WAAAC,CAAA;oBAAA,OAAAA,CAAA,CAAAc,IAAA,KAAAhB,QAAA,CAAApB,KAAA;kBAAA;kBACA8B,GAAA,CAAAC,cAAA,GAAAG,UAAA,CAAAG,EAAA;kBACAP,GAAA,CAAAE,SAAA,GAAAd,KAAA;kBACAY,GAAA,CAAAG,gBAAA,GAAAb,QAAA,CAAApB,KAAA;kBACAgB,SAAA,CAAAa,IAAA,CAAAC,GAAA;gBACA;cACA;YACA;YACAf,IAAA,CAAAuB,gBAAA,GAAAtB,SAAA;YACAO,OAAA,CAAAC,GAAA,0BAAAT,IAAA,CAAAuB,gBAAA;YACAvB,IAAA,CAAAwB,iBAAA,GAAA3B,MAAA,CAAA4B,MAAA,WAAAlB,CAAA;cAAA,SAAAA,CAAA;YAAA,GAAAmB,QAAA;UACA;UACAjC,KAAA,CAAAL,UAAA;UACAK,KAAA,CAAAkC,WAAA;QACA;UACA;QACA;MACA;IACA;IACAC,SAAA,WAAAA,UAAAC,QAAA,EAAAC,GAAA;MAAA,IAAAC,MAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,QAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAC,GAAA,EAAAC,MAAA,EAAAzB,GAAA;QAAA,OAAAkB,mBAAA,GAAAQ,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACA;cACAd,MAAA,CAAAjC,IAAA,GAAAgC,GAAA;cACAC,MAAA,CAAAe,SAAA;cACAf,MAAA,CAAAjC,IAAA,GAAAiC,MAAA,CAAAjC,IAAA,CAAAiD,GAAA,WAAA/C,IAAA;gBACAA,IAAA,CAAAI,KAAA,GAAAJ,IAAA,CAAAgD,IAAA,SAAAhD,IAAA,CAAAiD,mBAAA;gBACA,IAAAjD,IAAA,CAAAkD,YAAA;kBACAlD,IAAA,CAAAE,QAAA,GAAAiD,kBAAA,KAAAC,GAAA,CAAApD,IAAA,CAAAkD,YAAA,CAAAG,KAAA;gBACA;gBACAtB,MAAA,CAAAe,SAAA,GAAAf,MAAA,CAAAe,SAAA,CAAAjC,MAAA,CAAAb,IAAA,CAAAE,QAAA;gBACA,OAAAF,IAAA;cACA;cACA+B,MAAA,CAAAe,SAAA,GAAAK,kBAAA,KAAAC,GAAA,CAAArB,MAAA,CAAAe,SAAA;cACAV,QAAA;cAAAC,SAAA,GAAAiB,0BAAA,CACAvB,MAAA,CAAAe,SAAA;cAAAH,QAAA,CAAAC,IAAA;cAAAP,SAAA,CAAAkB,CAAA;YAAA;cAAA,KAAAjB,KAAA,GAAAD,SAAA,CAAAmB,CAAA,IAAAC,IAAA;gBAAAd,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAAN,GAAA,GAAAD,KAAA,CAAArD,KAAA;cAAA0D,QAAA,CAAAE,IAAA;cAAA,OACA5E,UAAA,CAAAsE,GAAA;YAAA;cAAAH,QAAA,CAAAG,GAAA,IAAAI,QAAA,CAAAe,IAAA;YAAA;cAAAf,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAgB,EAAA,GAAAhB,QAAA;cAAAN,SAAA,CAAAuB,CAAA,CAAAjB,QAAA,CAAAgB,EAAA;YAAA;cAAAhB,QAAA,CAAAC,IAAA;cAAAP,SAAA,CAAAwB,CAAA;cAAA,OAAAlB,QAAA,CAAAmB,MAAA;YAAA;cAEAtD,OAAA,CAAAC,GAAA,kBAAA2B,QAAA;cACA5B,OAAA,CAAAC,GAAA,mBAAAsB,MAAA,CAAAe,SAAA;cACAtC,OAAA,CAAAC,GAAA,cAAAsB,MAAA,CAAAjC,IAAA;cACAU,OAAA,CAAAC,GAAA,qBAAAsB,MAAA,CAAAnD,WAAA;cACAmD,MAAA,CAAAF,QAAA,GAAAA,QAAA;cACAW,MAAA;cACAzB,GAAA;cACAgB,MAAA,CAAAhD,UAAA,GAAAgD,MAAA,CAAAjC,IAAA,CAAAiE,MAAA,WAAAC,GAAA,EAAAC,GAAA;gBACAA,GAAA,CAAA/D,QAAA,CAAAH,OAAA,WAAAmE,SAAA;kBACA,IAAAC,aAAA,GAAApC,MAAA,CAAAnD,WAAA,GAAAwD,QAAA,CAAA8B,SAAA;kBACA1D,OAAA,CAAAC,GAAA,kBAAA0D,aAAA;kBACA,IAAAC,SAAA,GAAAH,GAAA,CAAA7D,KAAA,SAAA8D,SAAA;kBACA,KAAAnD,GAAA,CAAAqD,SAAA,KAAAH,GAAA,CAAAjB,IAAA;oBACAgB,GAAA,CAAAlD,IAAA;sBACAuD,IAAA,EAAAJ,GAAA,CAAAjB,IAAA;sBACAsB,KAAA,GAAAL,GAAA,CAAAM,SAAA,UAAAJ,aAAA,GAAA3B,MAAA;sBACAvD,KAAA;sBACAmB,KAAA,EAAAgE;oBACA;oBACArC,MAAA,CAAAyC,IAAA,CAAAzC,MAAA,CAAA1C,WAAA,EAAA+E,SAAA;kBACA;kBACArD,GAAA,CAAAqD,SAAA;gBACA;gBAEA,OAAAJ,GAAA;cACA;cACAxD,OAAA,CAAAC,GAAA,QAAAM,GAAA;cACAP,OAAA,CAAAC,GAAA,oBAAAsB,MAAA,CAAAhD,UAAA;cAAA4D,QAAA,CAAAE,IAAA;cAAA,OACAd,MAAA,CAAA0C,SAAA;YAAA;cACA,IAAA5C,QAAA,IAAAC,GAAA,CAAA4C,MAAA;gBACA5C,GAAA,CAAA/B,OAAA,WAAAC,IAAA;kBACAA,IAAA,CAAAE,QAAA,CAAAH,OAAA,WAAAI,KAAA;oBACA,IAAAC,KAAA,GAAAJ,IAAA,CAAAI,KAAA,SAAAD,KAAA;oBACA,IAAAE,QAAA,GAAA0B,MAAA,CAAAhD,UAAA,CAAAuB,IAAA,WAAAC,CAAA;sBAAA,OAAAA,CAAA,CAAAH,KAAA,KAAAA,KAAA;oBAAA;oBACA,IAAAC,QAAA;sBACA,IAAAsE,YAAA,GAAA3E,IAAA,CAAAwB,iBAAA,CAAA6B,KAAA;sBACA7C,OAAA,CAAAC,GAAA,iBAAAkE,YAAA;sBACAnE,OAAA,CAAAC,GAAA,4BAAAsB,MAAA,CAAA1C,WAAA,CAAAe,KAAA;sBACA,IAAA6D,GAAA,GAAAlC,MAAA,CAAA1C,WAAA,CAAAe,KAAA,EAAAE,IAAA,WAAAC,CAAA;wBAAA,OAAAoE,YAAA,CAAAC,QAAA,CAAArE,CAAA,CAAAc,IAAA;sBAAA;sBACAb,OAAA,CAAAC,GAAA,QAAAwD,GAAA;sBACA5D,QAAA,CAAApB,KAAA,IAAAgF,GAAA,aAAAA,GAAA,uBAAAA,GAAA,CAAA5C,IAAA;oBACA;kBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAsB,QAAA,CAAAkC,IAAA;UAAA;QAAA,GAAA1C,OAAA;MAAA;IACA;IACA2C,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,QAAA;MACA,IAAAC,MAAA;MACA,IAAAC,cAAA,YAAAA,eAAAb,IAAA;QAAA,OAAAU,MAAA,CAAA3D,MAAA,CAAAd,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAc,IAAA,KAAAgD,IAAA;QAAA;MAAA;MACA,IAAAc,OAAA,QAAA/D,MAAA,CAAAK,MAAA,WAAAlB,CAAA;QAAA,OAAAA,CAAA,CAAA6E,SAAA;MAAA;MACA5E,OAAA,CAAAC,GAAA,oBAAAX,IAAA;MACA,KAAAA,IAAA,CAAAC,OAAA,WAAAsF,OAAA;QACAA,OAAA,CAAAnF,QAAA,CAAAH,OAAA,WAAAI,KAAA;UACA,IAAAnB,GAAA,GAAAqG,OAAA,CAAAjF,KAAA,SAAAD,KAAA;UACA8E,MAAA,CAAAnE,IAAA,CAAA9B,GAAA;UACA,KAAAgG,QAAA,CAAAhG,GAAA;YACAgG,QAAA,CAAAhG,GAAA;UACA;UACA,IAAAsG,UAAA;UACA,IAAAnF,KAAA;YACAmF,UAAA;UACA,WAAAnF,KAAA;YACAmF,UAAA;UACA,WAAAnF,KAAA;YACAmF,UAAA;UACA,WAAAnF,KAAA;YACAmF,UAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACAD,OAAA,CAAAC,UAAA,IAAAD,OAAA,CAAAC,UAAA;UAEA,IAAAD,OAAA,CAAA3E,sBAAA;YACA,IAAAV,IAAA,GAAAkF,cAAA,CAAAG,OAAA,CAAA3E,sBAAA;YACA,IAAAV,IAAA;cACAgF,QAAA,CAAAhG,GAAA,EAAA8B,IAAA,CAAAd,IAAA;YACA;UACA;YACA,IAAAuF,gBAAA,GAAAF,OAAA,CAAAC,UAAA,EAAAjC,KAAA,MAAA5B,MAAA,WAAAlB,CAAA;cAAA,SAAAA,CAAA;YAAA;YACA;YACAC,OAAA,CAAAC,GAAA;YAEA,IAAA8E,gBAAA,CAAAb,MAAA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACAa,gBAAA,CAAAxF,OAAA,WAAAyF,CAAA;gBACA,IAAAvB,GAAA,GAAAiB,cAAA,CAAAM,CAAA;gBACAhF,OAAA,CAAAC,GAAA,SAAAwD,GAAA,EAAAe,QAAA,CAAAhG,GAAA;gBACA,IAAAiF,GAAA;kBACA,IAAAe,QAAA,CAAAhG,GAAA,EAAAyG,SAAA,WAAAlF,CAAA;oBAAA,OAAAA,CAAA,CAAAc,IAAA,KAAA4C,GAAA,CAAA5C,IAAA;kBAAA;oBACA;kBACA;kBACA2D,QAAA,CAAAhG,GAAA,EAAA8B,IAAA,CAAAmD,GAAA;gBACA;cACA;YACA;cACAzD,OAAA,CAAAC,GAAA;cACA,IAAAiF,eAAA,GAAAL,OAAA,CAAA7D,iBAAA;cACA,IAAAmE,GAAA,GAAAZ,MAAA,CAAA3D,MAAA,CAAAK,MAAA,WAAAzB,IAAA;gBACA,IAAA4F,IAAA;gBACA,IAAAF,eAAA,IAAAA,eAAA,KAAA1F,IAAA,CAAAqB,IAAA;kBACAuE,IAAA;gBACA;gBACA,IAAAP,OAAA,CAAAQ,sBAAA,IAAAR,OAAA,CAAAQ,sBAAA,KAAA7F,IAAA,CAAAqB,IAAA;kBACAuE,IAAA;gBACA;gBACA,KAAAA,IAAA;kBACAA,IAAA,KAAA5F,IAAA,CAAAoF,SAAA;gBACA;gBACA5E,OAAA,CAAAC,GAAA,mBAAAT,IAAA,CAAAiB,SAAA,EAAAd,KAAA;gBACA,IAAAH,IAAA,CAAAiB,SAAA,CAAAS,QAAA,OAAAvB,KAAA,CAAAuB,QAAA;kBACAkE,IAAA;gBACA;gBACA,OAAAA,IAAA;cACA;cAAA,IAAAE,KAAA,YAAAA,MAAA,EACA;gBACA,IAAA7B,GAAA,GAAA0B,GAAA,CAAAI,CAAA;gBACA,IAAAf,QAAA,CAAAhG,GAAA,EAAAyG,SAAA,WAAAlF,CAAA;kBAAA,OAAAA,CAAA,CAAAc,IAAA,KAAA4C,GAAA,CAAA5C,IAAA;gBAAA;kBAAA;gBAEA;gBACA2D,QAAA,CAAAhG,GAAA,EAAA8B,IAAA,CAAAmD,GAAA;cACA;cANA,SAAA8B,CAAA,MAAAA,CAAA,GAAAJ,GAAA,CAAAjB,MAAA,EAAAqB,CAAA;gBAAA,IAAAD,KAAA,IAGA;cAAA;YAIA;UACA;QACA;MACA;MACAtF,OAAA,CAAAC,GAAA,qBAAAuF,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAlB,QAAA;MACA,SAAAnD,QAAA;QACA,KAAAxC,WAAA,GAAA2F,QAAA;MACA;QACAC,MAAA,CAAAlF,OAAA,WAAAoG,IAAA,EAAAC,GAAA;UACA,IAAAC,UAAA,GAAAF,IAAA,CAAA9C,KAAA;UACA0B,MAAA,CAAA1F,WAAA,CAAA8G,IAAA,IAAAhB,OAAA,CAAA1D,MAAA,WAAAlB,CAAA;YAAA,OAAAA,CAAA,CAAAyC,IAAA,KAAAqD,UAAA;UAAA;QACA;MACA;IACA;IACA5B,SAAA,WAAAA,UAAA;MAAA,IAAA6B,MAAA;MAAA,OAAAtE,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAqE,SAAA;QAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,OAAA;QAAA,OAAA5E,mBAAA,GAAAQ,IAAA,UAAAqE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnE,IAAA,GAAAmE,SAAA,CAAAlE,IAAA;YAAA;cACAyD,MAAA,CAAAnH,OAAA;cAIA,IAAAmH,MAAA,CAAA1H,WAAA;gBACA4H,eAAA,GAAAF,MAAA,CAAAxG,IAAA,CAAAkH,IAAA,WAAAzG,CAAA;kBAAA,SAAAA,CAAA,CAAA0C,mBAAA;gBAAA;gBACAwD,gBAAA,GAAAH,MAAA,CAAAxG,IAAA,CAAAkH,IAAA,WAAAzG,CAAA;kBAAA,QAAAA,CAAA,CAAA0C,mBAAA;gBAAA;cACA;gBACAuD,eAAA;gBACAC,gBAAA;cACA;cACAC,gBAAA;gBAAA,IAAAO,IAAA,GAAAjF,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAgF,SAAAC,QAAA;kBAAA,IAAA/I,IAAA,EAAAgJ,GAAA;kBAAA,OAAAnF,mBAAA,GAAAQ,IAAA,UAAA4E,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAA1E,IAAA,GAAA0E,SAAA,CAAAzE,IAAA;sBAAA;wBAAAyE,SAAA,CAAA1E,IAAA;wBAEAxE,IAAA,GAAA+I,QAAA;wBAAAG,SAAA,CAAAzE,IAAA;wBAAA,OACA7E,kBAAA;0BAAAI,IAAA,EAAAA,IAAA;0BAAA6C,SAAA,EAAAkG;wBAAA;sBAAA;wBAAAC,GAAA,GAAAE,SAAA,CAAA5D,IAAA;wBAAA,KACA0D,GAAA,CAAAG,SAAA;0BAAAD,SAAA,CAAAzE,IAAA;0BAAA;wBAAA;wBAAA,OAAAyE,SAAA,CAAAE,MAAA,WACAJ,GAAA,CAAAK,IAAA;sBAAA;wBAEAnB,MAAA,CAAA3F,QAAA;0BACAC,OAAA,EAAAwG,GAAA,CAAAM,OAAA;0BACAtJ,IAAA;wBACA;wBAAA,OAAAkJ,SAAA,CAAAE,MAAA,WACA;sBAAA;wBAAAF,SAAA,CAAAzE,IAAA;wBAAA;sBAAA;wBAAAyE,SAAA,CAAA1E,IAAA;wBAAA0E,SAAA,CAAA3D,EAAA,GAAA2D,SAAA;wBAGAhB,MAAA,CAAA3F,QAAA;0BACAC,OAAA;0BACAxC,IAAA;wBACA;wBAAA,OAAAkJ,SAAA,CAAAE,MAAA,WACA;sBAAA;sBAAA;wBAAA,OAAAF,SAAA,CAAAzC,IAAA;oBAAA;kBAAA,GAAAqC,QAAA;gBAAA,CAEA;gBAAA,gBApBAR,iBAAAiB,EAAA;kBAAA,OAAAV,IAAA,CAAAW,KAAA,OAAAC,SAAA;gBAAA;cAAA;cAAAd,SAAA,CAAAnE,IAAA;cAuBA+D,MAAA,GAAAxD,kBAAA,KAAAC,GAAA,CAAAkD,MAAA,CAAAxG,IAAA,CAAAiE,MAAA,WAAAC,GAAA,EAAAC,GAAA;gBAAA,OAAAD,GAAA,CAAAnD,MAAA,CAAAoD,GAAA,CAAA/D,QAAA;cAAA;cACAM,OAAA,CAAAC,GAAA,WAAAkG,MAAA;cACAC,KAAA;cACAD,MAAA,CAAA5G,OAAA,WAAAI,KAAA;gBACAyG,KAAA,CAAA9F,IAAA,CAAA4F,gBAAA,CAAAvG,KAAA;cACA;cACA0G,OAAA;cAAAE,SAAA,CAAAlE,IAAA;cAAA,OACAiF,OAAA,CAAAC,GAAA,CAAAnB,KAAA;YAAA;cAAAC,OAAA,GAAAE,SAAA,CAAArD,IAAA;cACAlD,OAAA,CAAAC,GAAA,YAAAoG,OAAA;cACA;;cAEA;cACA;cACA;cACA;cACA;cACA;cACA;;cAEAP,MAAA,CAAAlF,MAAA,GAAAyF,OAAA,CAAApF,MAAA,WAAA3C,IAAA;gBAAA,SAAAA,IAAA,CAAA4F,MAAA;cAAA,GAAAsD,IAAA;cACAxH,OAAA,CAAAC,GAAA,gBAAA6F,MAAA,CAAAlF,MAAA;cAEAkF,MAAA,CAAAxB,YAAA;YAAA;cAAAiC,SAAA,CAAAnE,IAAA;cAEA0D,MAAA,CAAAnH,OAAA;cAAA,OAAA4H,SAAA,CAAAjD,MAAA;YAAA;YAAA;cAAA,OAAAiD,SAAA,CAAAlC,IAAA;UAAA;QAAA,GAAA0B,QAAA;MAAA;IAEA;IACA5E,WAAA,WAAAA,YAAA;MACA,KAAAsG,KAAA;IACA;EACA;AACA", "ignoreList": []}]}
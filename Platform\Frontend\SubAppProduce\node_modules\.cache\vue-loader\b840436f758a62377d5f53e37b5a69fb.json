{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckType.vue?vue&type=template&id=0743ce1a&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckType.vue", "mtime": 1758085989932}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uICgpIHsKICB2YXIgX3ZtID0gdGhpcwogIHZhciBfaCA9IF92bS4kY3JlYXRlRWxlbWVudAogIHZhciBfYyA9IF92bS5fc2VsZi5fYyB8fCBfaAogIHJldHVybiBfYygKICAgICJkaXYiLAogICAgeyBzdGF0aWNTdHlsZTogeyBoZWlnaHQ6ICJjYWxjKDEwMHZoIC0gMzAwcHgpIiB9IH0sCiAgICBbCiAgICAgIF9jKAogICAgICAgICJ2eGUtdGFibGUiLAogICAgICAgIHsKICAgICAgICAgIGRpcmVjdGl2ZXM6IFsKICAgICAgICAgICAgewogICAgICAgICAgICAgIG5hbWU6ICJsb2FkaW5nIiwKICAgICAgICAgICAgICByYXdOYW1lOiAidi1sb2FkaW5nIiwKICAgICAgICAgICAgICB2YWx1ZTogX3ZtLnRiTG9hZGluZywKICAgICAgICAgICAgICBleHByZXNzaW9uOiAidGJMb2FkaW5nIiwKICAgICAgICAgICAgfSwKICAgICAgICAgIF0sCiAgICAgICAgICBzdGF0aWNDbGFzczogImNzLXZ4ZS10YWJsZSIsCiAgICAgICAgICBhdHRyczogewogICAgICAgICAgICAiZW1wdHktcmVuZGVyIjogeyBuYW1lOiAiTm90RGF0YSIgfSwKICAgICAgICAgICAgInNob3ctaGVhZGVyLW92ZXJmbG93IjogIiIsCiAgICAgICAgICAgICJlbGVtZW50LWxvYWRpbmctc3Bpbm5lciI6ICJlbC1pY29uLWxvYWRpbmciLAogICAgICAgICAgICAiZWxlbWVudC1sb2FkaW5nLXRleHQiOiAi5ou85ZG95Yqg6L295LitIiwKICAgICAgICAgICAgImVtcHR5LXRleHQiOiAi5pqC5peg5pWw5o2uIiwKICAgICAgICAgICAgaGVpZ2h0OiAiMTAwJSIsCiAgICAgICAgICAgIGFsaWduOiAibGVmdCIsCiAgICAgICAgICAgIHN0cmlwZTogIiIsCiAgICAgICAgICAgIGRhdGE6IF92bS50YkRhdGEsCiAgICAgICAgICAgIHJlc2l6YWJsZTogIiIsCiAgICAgICAgICAgICJhdXRvLXJlc2l6ZSI6IHRydWUsCiAgICAgICAgICAgICJ0b29sdGlwLWNvbmZpZyI6IHsgZW50ZXJhYmxlOiB0cnVlIH0sCiAgICAgICAgICB9LAogICAgICAgIH0sCiAgICAgICAgWwogICAgICAgICAgX2MoInZ4ZS1jb2x1bW4iLCB7CiAgICAgICAgICAgIGF0dHJzOiB7CiAgICAgICAgICAgICAgInNob3ctb3ZlcmZsb3ciOiAidG9vbHRpcCIsCiAgICAgICAgICAgICAgc29ydGFibGU6ICIiLAogICAgICAgICAgICAgIGZpZWxkOiAiTmFtZSIsCiAgICAgICAgICAgICAgdGl0bGU6ICLmo4Dmn6XnsbvlnosiLAogICAgICAgICAgICAgIHdpZHRoOiAiY2FsYygxMDB2aC0yMDBweCkiLAogICAgICAgICAgICB9LAogICAgICAgICAgfSksCiAgICAgICAgXSwKICAgICAgICAxCiAgICAgICksCiAgICBdLAogICAgMQogICkKfQp2YXIgc3RhdGljUmVuZGVyRm5zID0gW10KcmVuZGVyLl93aXRoU3RyaXBwZWQgPSB0cnVlCgpleHBvcnQgeyByZW5kZXIsIHN0YXRpY1JlbmRlckZucyB9"}]}
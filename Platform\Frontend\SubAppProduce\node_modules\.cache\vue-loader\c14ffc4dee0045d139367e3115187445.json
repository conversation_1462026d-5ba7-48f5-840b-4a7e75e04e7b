{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\ToleranceDialog.vue?vue&type=template&id=4db41446&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\ToleranceDialog.vue", "mtime": 1757468112827}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxlbC1mb3JtIHJlZj0iZm9ybVJlZiIgOm1vZGVsPSJmb3JtIiBsYWJlbC13aWR0aD0iMTIwcHgiIDpydWxlcz0icnVsZXMiPgogIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IumVv+W6piIgcHJvcD0iTGVuZ3RoIj4KICAgIDxkaXYgY2xhc3M9ImNzLWZsZXgiPgogICAgICA8ZWwtc2VsZWN0IHYtbW9kZWw9ImZvcm0uVHlwZSIgc3R5bGU9Im1hcmdpbi1yaWdodDogMTBweDt3aWR0aDogMTAwcHg7IiBwbGFjZWhvbGRlcj0i6K+36YCJ5oupIj4KICAgICAgICA8ZWwtb3B0aW9uIHYtZm9yPSJpdGVtIGluIHR5cGVPcHRpb25zIiA6a2V5PSJpdGVtLnZhbHVlIiA6bGFiZWw9Iml0ZW0ubGFiZWwiIDp2YWx1ZT0iaXRlbS52YWx1ZSIgLz4KICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgIDxlbC1pbnB1dC1udW1iZXIgdi1tb2RlbC5udW1iZXI9ImZvcm0uTGVuZ3RoIiA6bWluPSIwIiBjbGFzcz0iY3MtbnVtYmVyLWJ0bi1oaWRkZW4gdzEwMCIgY2xlYXJibGUgLz4KICAgIDwvZGl2PgoKICA8L2VsLWZvcm0taXRlbT4KICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLlhazlt67opoHmsYIiIHByb3A9IkRlbWFuZCI+CiAgICA8ZWwtaW5wdXQgdi1tb2RlbD0iZm9ybS5EZW1hbmQiIHBsYWNlaG9sZGVyPSLor7fovpPlhaXlhazlt67opoHmsYIiIC8+CiAgPC9lbC1mb3JtLWl0ZW0+CiAgPGVsLWZvcm0taXRlbSBzdHlsZT0idGV4dC1hbGlnbjogcmlnaHQiPgogICAgPGVsLWJ1dHRvbiBAY2xpY2s9ImhhbmRsZUNsb3NlIj7lj5bmtog8L2VsLWJ1dHRvbj4KICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgOmxvYWRpbmc9ImxvYWRpbmciIEBjbGljaz0ic3VibWl0Rm9ybSI+56Gu5a6aPC9lbC1idXR0b24+CiAgPC9lbC1mb3JtLWl0ZW0+Cgo8L2VsLWZvcm0+Cgo="}, null]}
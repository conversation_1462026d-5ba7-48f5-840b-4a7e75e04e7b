{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\components\\ProjectData.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\components\\ProjectData.vue", "mtime": 1757468113454}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ProjectData.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "ProjectData.vue", "sourceRoot": "src/views/PRO/project-config/components", "sourcesContent": ["<template>\r\n  <ExpandableSection v-model=\"showExpand\" :width=\"300\" class=\"cs-left fff\">\r\n    <div class=\"inner-wrapper\">\r\n      <div class=\"tree-search\">\r\n        <el-input\r\n          v-model.trim=\"projectName\"\r\n          placeholder=\"关键词搜索\"\r\n          size=\"small\"\r\n          clearable\r\n          suffix-icon=\"el-icon-search\"\r\n          @blur=\"fetchTreeDataLocal\"\r\n          @clear=\"fetchTreeDataLocal\"\r\n          @keydown.enter.native=\"fetchTreeDataLocal\"\r\n        />\r\n      </div>\r\n      <el-divider class=\"cs-divider\" />\r\n      <div class=\"tree-x cs-scroll\">\r\n        <div v-for=\"item in treeData\" :key=\"item.Sys_Project_Id\" class=\"project-list\" :class=\"{ active: item.Sys_Project_Id === Active_Sys_Project_Id }\" @click=\"handleNodeClick(item)\">\r\n          <el-tooltip class=\"item\" effect=\"dark\" :content=\"item.Short_Name\" :open-delay=\"200\" placement=\"top\">\r\n            <div class=\"project-inner\">\r\n              <div>\r\n                <svg-icon\r\n                  icon-class=\"icon-folder\"\r\n                  class-name=\"class-icon\"\r\n                />\r\n              </div>\r\n              <span class=\"code\">({{ item.Code }})</span>\r\n              <span class=\"name\">{{ item.Short_Name }}</span>\r\n            </div>\r\n          </el-tooltip>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </ExpandableSection>\r\n</template>\r\n\r\n<script>\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport {\r\n  GetProjectListForPlanTrace\r\n} from '@/api/PRO/project'\r\nexport default {\r\n  components: {\r\n    ExpandableSection\r\n  },\r\n  data() {\r\n    return {\r\n      Active_Sys_Project_Id: '',\r\n      showExpand: true,\r\n      treeLoading: true,\r\n      projectName: '',\r\n      treeData: [],\r\n      originalTreeData: [] // 保存原始数据\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchTreeData()\r\n  },\r\n  methods: {\r\n    // 项目数据集\r\n    fetchTreeData() {\r\n      GetProjectListForPlanTrace({ }).then((res) => {\r\n        if (!res.IsSucceed) {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.treeLoading = false\r\n          this.treeData = []\r\n          return\r\n        }\r\n        if (res.Data.length === 0) {\r\n          this.treeLoading = false\r\n          return\r\n        }\r\n        const resData = res.Data\r\n        this.originalTreeData = [...resData] // 保存原始数据\r\n        this.treeData = [...resData]\r\n        this.treeLoading = false\r\n        if (this.treeData.length > 0) {\r\n          this.handleNodeClick(resData[0])\r\n        }\r\n      })\r\n    },\r\n    // 选中左侧项目节点\r\n    handleNodeClick(data) {\r\n      this.Active_Sys_Project_Id = data.Sys_Project_Id\r\n      this.$emit('setProjectData', data)\r\n    },\r\n    fetchTreeDataLocal() {\r\n      // 如果搜索关键词为空，恢复原始数据\r\n      if (!this.projectName || this.projectName.trim() === '') {\r\n        this.treeData = [...this.originalTreeData]\r\n        // 恢复原始数据后，如果有数据则选中第一个\r\n        if (this.treeData.length > 0) {\r\n          this.handleNodeClick(this.treeData[0])\r\n        }\r\n        return\r\n      }\r\n\r\n      // 从原始数据中过滤，支持大小写不敏感搜索\r\n      const searchTerm = this.projectName.trim().toLowerCase()\r\n      this.treeData = this.originalTreeData.filter((item) => {\r\n        return item.Short_Name &&\r\n               item.Short_Name.toLowerCase().includes(searchTerm)\r\n      })\r\n\r\n      // 搜索后如果有数据，自动选中第一个项目\r\n      // if (this.treeData.length > 0) {\r\n      //   this.handleNodeClick(this.treeData[0])\r\n      // }\r\n    }\r\n  }\r\n\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n  @import \"~@/styles/mixin.scss\";\r\n  @import \"~@/styles/tabs.scss\";\r\n  .cs-left {\r\n    position: relative;\r\n    margin-right: 20px;\r\n\r\n  }\r\n  .cs-left-contract {\r\n    padding-left: 0;\r\n    position: relative;\r\n    width: 20px;\r\n    margin-right: 26px;\r\n  }\r\n  .inner-wrapper {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    padding: 16px 10px 16px 16px;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n\r\n    .tree-search {\r\n      display: flex;\r\n\r\n      .search-select {\r\n        margin-right: 8px;\r\n      }\r\n    }\r\n\r\n    .tree-x {\r\n      overflow: auto;\r\n      margin-top: 16px;\r\n      flex: 1;\r\n      .project-list {\r\n        height: 32px;\r\n        padding-left: 16px;\r\n        font-size: 14px;\r\n        .project-inner {\r\n          height: 100%;\r\n          cursor: pointer;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: start;\r\n          white-space: nowrap; /* 禁止换行 */\r\n          overflow: hidden; /* 隐藏溢出内容 */\r\n          text-overflow: ellipsis; /* 溢出显示省略号 */\r\n          .code {\r\n            color: #5ac8fa;\r\n            margin-left: 5px;\r\n            margin-right: 5px;\r\n            flex-shrink: 0; /* 禁止压缩 */\r\n          }\r\n          .name {\r\n            color: rgba(34, 40, 52, .65);\r\n            flex-shrink: 1; /* 允许适当压缩 */\r\n            min-width: 0; /* 允许文本截断 */\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n          }\r\n        }\r\n      }\r\n      .project-list:hover {\r\n        background-color: rgba(41, 141, 255, .04);\r\n      }\r\n      .project-list.active {\r\n        background-color: #eef6ff;\r\n      }\r\n    }\r\n    .cs-scroll {\r\n      overflow-y: auto;\r\n      @include scrollBar;\r\n    }\r\n  }\r\n  .cs-divider {\r\n    margin: 16px 0 0 0;\r\n  }\r\n</style>\r\n"]}]}
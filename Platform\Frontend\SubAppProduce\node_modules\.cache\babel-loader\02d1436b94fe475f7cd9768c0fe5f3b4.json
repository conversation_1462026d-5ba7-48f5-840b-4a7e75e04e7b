{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\AssociatedDevice.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\AssociatedDevice.vue", "mtime": 1757468113413}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getTbInfo", "DynamicDataTable", "TopHeader", "GetDictionaryDetailListByCode", "GetDictionaryDetailListByParentId", "GetEquipmentAssetPageList", "AddWorkingProcess", "name", "components", "mixins", "props", "rowData", "type", "Object", "default", "data", "tbConfig", "Pager_<PERSON>gn", "queryInfo", "Page", "PageSize", "deviceItemTypeOptions", "form", "deviceName", "deviceType", "Type_Detail_Id", "department", "deviceTypeOptions", "currentComponent", "title", "columns", "tbData", "total", "table<PERSON><PERSON>", "Math", "random", "tbLoading", "dialogVisible", "selectList", "keywords", "created", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getTableConfig", "getEquipmentAssetPageList", "getDictionaryDetailListByCode", "stop", "methods", "clearSelec", "$refs", "dyTable", "clearSelection", "deviceTypeChange", "e", "_this2", "then", "res", "Data", "_this3", "_callee2", "_callee2$", "_context2", "dictionaryCode", "IsSucceed", "$message", "message", "Message", "console", "log", "optionsGroupList", "_this4", "_callee3", "_callee3$", "_context3", "Display_Name", "Device_Type_Id", "Device_Type_Detail_Id", "Department", "TotalCount", "gridPageChange", "_ref", "page", "Number", "gridSizeChange", "_ref2", "size", "handleDevice", "_this5", "length", "Device_Ids", "$emit", "handleSelectionChange", "list", "map", "i", "Id", "handleAdd", "handleEdit", "row", "_this6", "$nextTick", "_", "initData", "handleSearch", "reset"], "sources": ["src/views/PRO/process-settings/management/component/AssociatedDevice.vue"], "sourcesContent": ["<template>\r\n\r\n  <div>\r\n\r\n    <el-form label-width=\"80px\" :inline=\"true\">\r\n      <el-form-item>\r\n        <el-button type=\"primary\" @click=\"handleDevice\">关联设备</el-button>\r\n      </el-form-item>\r\n      <el-form-item label=\"设备名称\" prop=\"deviceName\">\r\n        <el-input v-model.trim=\"form.deviceName\" :clearbale=\"true\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"设备类型\" prop=\"deviceType\">\r\n        <el-select v-model=\"form.deviceType\" clearable placeholder=\"请选择\" style=\"width: 100%\" @change=\"deviceTypeChange\">\r\n          <el-option v-for=\"item in deviceTypeOptions\" :key=\"item.Id\" :label=\"item.Display_Name\" :value=\"item.Id\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"设备子类\" prop=\"Type_Detail_Id\">\r\n        <el-select v-model=\"form.Type_Detail_Id\" clearable placeholder=\"请选择\" :disabled=\"!form.deviceType\"\r\n          style=\"width: 100%\">\r\n          <el-option v-for=\"item in deviceItemTypeOptions\" :key=\"item.Id\" :label=\"item.Display_Name\" :value=\"item.Id\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"所属部门\" prop=\"department\">\r\n        <el-input v-model.trim=\"form.department\" clearbale />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\r\n        <el-button @click=\"reset\">重置</el-button>\r\n      </el-form-item>\r\n\r\n    </el-form>\r\n\r\n\r\n\r\n\r\n\r\n\r\n    <div v-loading=\"tbLoading\" class=\"fff cs-z-tb-wrapper\" style=\"height: calc(100vh - 320px);\">\r\n      <dynamic-data-table ref=\"dyTable\" :key=\"tableKey\" :columns=\"columns\" :config=\"tbConfig\" :data=\"tbData\"\r\n        :page=\"queryInfo.Page\" :total=\"total\" border stripe class=\"cs-plm-dy-table\"\r\n        @multiSelectedChange=\"handleSelectionChange\" @gridPageChange=\"gridPageChange\" @gridSizeChange=\"gridSizeChange\"\r\n        @tableSearch=\"tableSearch\">\r\n        <template slot=\"Director_UserName\" slot-scope=\"{ row }\">\r\n          <div>{{ row.Director_UserName || \"-\" }}</div>\r\n        </template>\r\n        <template slot=\"Working_Team_Names\" slot-scope=\"{ row }\">\r\n          <div>{{ row.Working_Team_Names || \"-\" }}</div>\r\n        </template>\r\n\r\n      </dynamic-data-table>\r\n    </div>\r\n  </div>\r\n\r\n\r\n</template>\r\n\r\n<script>\r\nimport getTbInfo from \"@/mixins/PRO/get-table-info\";\r\nimport DynamicDataTable from \"@/components/DynamicDataTable/DynamicDataTable\";\r\nimport TopHeader from \"@/components/TopHeader\";\r\n// import detail from \"./component/detail\";\r\nimport { GetDictionaryDetailListByCode, GetDictionaryDetailListByParentId, } from '@/api/sys'\r\nimport { GetEquipmentAssetPageList, AddWorkingProcess } from '@/api/PRO/technology-lib'\r\n\r\n\r\n\r\nexport default {\r\n  name: \"PROGroup\",\r\n\r\n  components: {\r\n    DynamicDataTable,\r\n    TopHeader,\r\n    // detail,\r\n  },\r\n  mixins: [getTbInfo],\r\n  props: {\r\n    rowData: {\r\n      type: Object,\r\n      default() {\r\n        return {}\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      tbConfig: {\r\n        Pager_Align: \"center\",\r\n      },\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: 10,\r\n      },\r\n      deviceItemTypeOptions: [],\r\n      form: {\r\n        deviceName: \"\",\r\n        deviceType: \"\",\r\n        Type_Detail_Id: \"\",\r\n        department: \"\",\r\n      },\r\n      deviceTypeOptions: [],\r\n      currentComponent: \"\",\r\n      title: \"\",\r\n      columns: [],\r\n      tbData: [],\r\n      total: 0,\r\n      tableKey: Math.random(),\r\n      tbLoading: false,\r\n      dialogVisible: false,\r\n      selectList: [],\r\n      keywords: \"\",\r\n    };\r\n  },\r\n  async created() {\r\n    this.tbLoading = true;\r\n    this.selectList = []\r\n    await this.getTableConfig(\"plm_device_list\");\r\n    this.getEquipmentAssetPageList();\r\n    this.getDictionaryDetailListByCode();\r\n  },\r\n  methods: {\r\n    clearSelec() {\r\n      this.$refs.dyTable.clearSelection()\r\n    },\r\n    deviceTypeChange(e) {\r\n      this.form.Type_Detail_Id = ''\r\n      this.deviceItemTypeOptions = []\r\n      GetDictionaryDetailListByParentId(e).then((res) => {\r\n        this.deviceItemTypeOptions = res.Data;\r\n      });\r\n    },\r\n    // 获取设备类型\r\n    async getDictionaryDetailListByCode() {\r\n      await GetDictionaryDetailListByCode({ dictionaryCode: 'deviceType' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.deviceTypeOptions = res.Data || [];\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      console.log(' this.optionsGroupList', this.optionsGroupList)\r\n    },\r\n    async getEquipmentAssetPageList() {\r\n\r\n      await GetEquipmentAssetPageList({\r\n        Display_Name: this.form.deviceName,\r\n        Device_Type_Id: this.form.deviceType,\r\n        Device_Type_Detail_Id: this.form.Type_Detail_Id,\r\n        Department: this.form.department,\r\n        Page: this.queryInfo.Page,\r\n        PageSize: this.queryInfo.PageSize,\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.Data;\r\n          this.total = res.Data.TotalCount;\r\n          this.tbLoading = false;\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      console.log(' this.optionsGroupList', this.optionsGroupList)\r\n    },\r\n    gridPageChange({ page }) {\r\n      this.queryInfo.Page = Number(page);\r\n      this.getEquipmentAssetPageList();\r\n    },\r\n    gridSizeChange({ size }) {\r\n      this.queryInfo.PageSize = Number(size);\r\n      this.queryInfo.Page = 1;\r\n      this.getEquipmentAssetPageList();\r\n    },\r\n    handleDevice() {\r\n      if (this.selectList.length === 0) {\r\n        this.$message({\r\n          message: '请选择设备',\r\n          type: 'error'\r\n        })\r\n        return\r\n      } else {\r\n        \r\n        this.rowData.Device_Ids = this.selectList\r\n        console.log(this.rowData,'this.rowData');\r\n\r\n        AddWorkingProcess(this.rowData).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '关联成功',\r\n              type: 'success'\r\n            })\r\n            this.$emit(\"fetchData\");\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n\r\n    },\r\n\r\n\r\n\r\n\r\n    handleSelectionChange(list) {\r\n      this.selectList = list.map((i) => i.Id);\r\n      console.log(this.selectList, 'this.selectList')\r\n    },\r\n    handleAdd() {\r\n      this.currentComponent = \"detail\";\r\n      this.title = \"新增车间\";\r\n      this.dialogVisible = true;\r\n    },\r\n    handleEdit(row) {\r\n      this.currentComponent = \"detail\";\r\n      this.title = \"编辑车间\";\r\n      this.dialogVisible = true;\r\n      this.$nextTick((_) => {\r\n        this.$refs[\"content\"].initData(row);\r\n      });\r\n    },\r\n    // handleDetail(row) {\r\n    //   this.currentComponent = \"info\";\r\n    //   this.title = \"查看\";\r\n    //   this.dialogVisible = true;\r\n    //   this.$nextTick((_) => {\r\n    //     this.$refs[\"content\"].initData(row.Id);\r\n    //   });\r\n    // },\r\n\r\n    handleSearch() {\r\n      this.getEquipmentAssetPageList();\r\n      this.queryInfo.Page = 1;\r\n    },\r\n    reset() {\r\n      this.form = {}\r\n      this.getEquipmentAssetPageList();\r\n      this.queryInfo.Page = 1;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n// .cs-dialog {\r\n//   ::v-deep {\r\n//     .el-dialog__body {\r\n//       padding-top: 0;\r\n//     }\r\n//   }\r\n// }\r\n::v-deep {\r\n  .cs-top-header-box {\r\n    line-height: 0px;\r\n  }\r\n}\r\n\r\n::v-deep .pagination {\r\n  justify-content: flex-end !important;\r\n  margin-top: 12px !important;\r\n\r\n  .el-input--small .el-input__inner {\r\n    height: 28px;\r\n    line-height: 28px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDA,OAAAA,SAAA;AACA,OAAAC,gBAAA;AACA,OAAAC,SAAA;AACA;AACA,SAAAC,6BAAA,EAAAC,iCAAA;AACA,SAAAC,yBAAA,EAAAC,iBAAA;AAIA;EACAC,IAAA;EAEAC,UAAA;IACAP,gBAAA,EAAAA,gBAAA;IACAC,SAAA,EAAAA;IACA;EACA;EACAO,MAAA,GAAAT,SAAA;EACAU,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QACA;MACA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;QACAC,WAAA;MACA;MACAC,SAAA;QACAC,IAAA;QACAC,QAAA;MACA;MACAC,qBAAA;MACAC,IAAA;QACAC,UAAA;QACAC,UAAA;QACAC,cAAA;QACAC,UAAA;MACA;MACAC,iBAAA;MACAC,gBAAA;MACAC,KAAA;MACAC,OAAA;MACAC,MAAA;MACAC,KAAA;MACAC,QAAA,EAAAC,IAAA,CAAAC,MAAA;MACAC,SAAA;MACAC,aAAA;MACAC,UAAA;MACAC,QAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACAT,KAAA,CAAAL,SAAA;YACAK,KAAA,CAAAH,UAAA;YAAAU,QAAA,CAAAE,IAAA;YAAA,OACAT,KAAA,CAAAU,cAAA;UAAA;YACAV,KAAA,CAAAW,yBAAA;YACAX,KAAA,CAAAY,6BAAA;UAAA;UAAA;YAAA,OAAAL,QAAA,CAAAM,IAAA;QAAA;MAAA,GAAAT,OAAA;IAAA;EACA;EACAU,OAAA;IACAC,UAAA,WAAAA,WAAA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAC,cAAA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,CAAA;MAAA,IAAAC,MAAA;MACA,KAAAxC,IAAA,CAAAG,cAAA;MACA,KAAAJ,qBAAA;MACAjB,iCAAA,CAAAyD,CAAA,EAAAE,IAAA,WAAAC,GAAA;QACAF,MAAA,CAAAzC,qBAAA,GAAA2C,GAAA,CAAAC,IAAA;MACA;IACA;IACA;IACAZ,6BAAA,WAAAA,8BAAA;MAAA,IAAAa,MAAA;MAAA,OAAAxB,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAuB,SAAA;QAAA,OAAAxB,mBAAA,GAAAG,IAAA,UAAAsB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApB,IAAA,GAAAoB,SAAA,CAAAnB,IAAA;YAAA;cAAAmB,SAAA,CAAAnB,IAAA;cAAA,OACA/C,6BAAA;gBAAAmE,cAAA;cAAA,GAAAP,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAO,SAAA;kBACAL,MAAA,CAAAvC,iBAAA,GAAAqC,GAAA,CAAAC,IAAA;gBACA;kBACAC,MAAA,CAAAM,QAAA;oBACAC,OAAA,EAAAT,GAAA,CAAAU,OAAA;oBACA9D,IAAA;kBACA;gBACA;cACA;YAAA;cACA+D,OAAA,CAAAC,GAAA,2BAAAV,MAAA,CAAAW,gBAAA;YAAA;YAAA;cAAA,OAAAR,SAAA,CAAAf,IAAA;UAAA;QAAA,GAAAa,QAAA;MAAA;IACA;IACAf,yBAAA,WAAAA,0BAAA;MAAA,IAAA0B,MAAA;MAAA,OAAApC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAmC,SAAA;QAAA,OAAApC,mBAAA,GAAAG,IAAA,UAAAkC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhC,IAAA,GAAAgC,SAAA,CAAA/B,IAAA;YAAA;cAAA+B,SAAA,CAAA/B,IAAA;cAAA,OAEA7C,yBAAA;gBACA6E,YAAA,EAAAJ,MAAA,CAAAxD,IAAA,CAAAC,UAAA;gBACA4D,cAAA,EAAAL,MAAA,CAAAxD,IAAA,CAAAE,UAAA;gBACA4D,qBAAA,EAAAN,MAAA,CAAAxD,IAAA,CAAAG,cAAA;gBACA4D,UAAA,EAAAP,MAAA,CAAAxD,IAAA,CAAAI,UAAA;gBACAP,IAAA,EAAA2D,MAAA,CAAA5D,SAAA,CAAAC,IAAA;gBACAC,QAAA,EAAA0D,MAAA,CAAA5D,SAAA,CAAAE;cACA,GAAA2C,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAO,SAAA;kBACAO,MAAA,CAAA/C,MAAA,GAAAiC,GAAA,CAAAC,IAAA,CAAAA,IAAA;kBACAa,MAAA,CAAA9C,KAAA,GAAAgC,GAAA,CAAAC,IAAA,CAAAqB,UAAA;kBACAR,MAAA,CAAA1C,SAAA;gBACA;kBACA0C,MAAA,CAAAN,QAAA;oBACAC,OAAA,EAAAT,GAAA,CAAAU,OAAA;oBACA9D,IAAA;kBACA;gBACA;cACA;YAAA;cACA+D,OAAA,CAAAC,GAAA,2BAAAE,MAAA,CAAAD,gBAAA;YAAA;YAAA;cAAA,OAAAI,SAAA,CAAA3B,IAAA;UAAA;QAAA,GAAAyB,QAAA;MAAA;IACA;IACAQ,cAAA,WAAAA,eAAAC,IAAA;MAAA,IAAAC,IAAA,GAAAD,IAAA,CAAAC,IAAA;MACA,KAAAvE,SAAA,CAAAC,IAAA,GAAAuE,MAAA,CAAAD,IAAA;MACA,KAAArC,yBAAA;IACA;IACAuC,cAAA,WAAAA,eAAAC,KAAA;MAAA,IAAAC,IAAA,GAAAD,KAAA,CAAAC,IAAA;MACA,KAAA3E,SAAA,CAAAE,QAAA,GAAAsE,MAAA,CAAAG,IAAA;MACA,KAAA3E,SAAA,CAAAC,IAAA;MACA,KAAAiC,yBAAA;IACA;IACA0C,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,SAAAzD,UAAA,CAAA0D,MAAA;QACA,KAAAxB,QAAA;UACAC,OAAA;UACA7D,IAAA;QACA;QACA;MACA;QAEA,KAAAD,OAAA,CAAAsF,UAAA,QAAA3D,UAAA;QACAqC,OAAA,CAAAC,GAAA,MAAAjE,OAAA;QAEAL,iBAAA,MAAAK,OAAA,EAAAoD,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAO,SAAA;YACAwB,MAAA,CAAAvB,QAAA;cACAC,OAAA;cACA7D,IAAA;YACA;YACAmF,MAAA,CAAAG,KAAA;UACA;YACAH,MAAA,CAAAvB,QAAA;cACAC,OAAA,EAAAT,GAAA,CAAAU,OAAA;cACA9D,IAAA;YACA;UACA;QACA;MACA;IAEA;IAKAuF,qBAAA,WAAAA,sBAAAC,IAAA;MACA,KAAA9D,UAAA,GAAA8D,IAAA,CAAAC,GAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,EAAA;MAAA;MACA5B,OAAA,CAAAC,GAAA,MAAAtC,UAAA;IACA;IACAkE,SAAA,WAAAA,UAAA;MACA,KAAA5E,gBAAA;MACA,KAAAC,KAAA;MACA,KAAAQ,aAAA;IACA;IACAoE,UAAA,WAAAA,WAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAA/E,gBAAA;MACA,KAAAC,KAAA;MACA,KAAAQ,aAAA;MACA,KAAAuE,SAAA,WAAAC,CAAA;QACAF,MAAA,CAAAlD,KAAA,YAAAqD,QAAA,CAAAJ,GAAA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEAK,YAAA,WAAAA,aAAA;MACA,KAAA3D,yBAAA;MACA,KAAAlC,SAAA,CAAAC,IAAA;IACA;IACA6F,KAAA,WAAAA,MAAA;MACA,KAAA1F,IAAA;MACA,KAAA8B,yBAAA;MACA,KAAAlC,SAAA,CAAAC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}
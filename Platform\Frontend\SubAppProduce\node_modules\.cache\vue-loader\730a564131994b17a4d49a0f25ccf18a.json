{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-unit-part\\components\\BatchProcessAdjust.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-unit-part\\components\\BatchProcessAdjust.vue", "mtime": 1757572678815}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRQcm9jZXNzTGlzdEJhc2UsIEdldExpYkxpc3QgfSBmcm9tICdAL2FwaS9QUk8vdGVjaG5vbG9neS1saWInDQppbXBvcnQgRHJhZ2dhYmxlIGZyb20gJ3Z1ZWRyYWdnYWJsZScNCmltcG9ydCB7IHVuaXF1ZUFyciwgZGVlcENsb25lIH0gZnJvbSAnQC91dGlscycNCmltcG9ydCB7IG1hcEFjdGlvbnMgfSBmcm9tICd2dWV4Jw0KaW1wb3J0IHsgdjQgYXMgdXVpZHY0IH0gZnJvbSAndXVpZCcNCmV4cG9ydCBkZWZhdWx0IHsNCiAgY29tcG9uZW50czogew0KICAgIERyYWdnYWJsZQ0KICB9LA0KICBwcm9wczogew0KICAgIHBhZ2VUeXBlOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiB1bmRlZmluZWQNCiAgICB9LA0KICAgIGlzTmVzdDogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfSwNCiAgICBwcm9jZXNzTGlzdDogew0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgZGVmYXVsdDogKCkgPT4gKHt9KQ0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgbGlzdDogW10sDQogICAgICBvcHRpb25zOiBbXSwNCiAgICAgIC8vIGRlZmF1bHRPcHRpb25zOiBbXSwNCiAgICAgIGJ0bkxvYWRpbmc6IGZhbHNlLA0KICAgICAgcGdMb2FkaW5nOiBmYWxzZSwNCiAgICAgIGd5TGlzdDogW10sDQogICAgICBjcmFmdENvZGU6ICcnLA0KICAgICAgZm9ybToge30NCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgaXNDb20oKSB7DQogICAgICByZXR1cm4gdGhpcy5wYWdlVHlwZSA9PT0gJ2NvbScNCiAgICB9DQogIH0sDQogIHdhdGNoOiB7DQogICAgbGlzdDogew0KICAgICAgaGFuZGxlcihuZXdWYWwpIHsNCiAgICAgICAgaWYgKCF0aGlzLmNyYWZ0Q29kZSkgcmV0dXJuDQogICAgICAgIGNvbnN0IHdvcmtDb2RlID0gdGhpcy5neUxpc3QuZmluZCh2ID0+IHYuQ29kZSA9PT0gdGhpcy5jcmFmdENvZGUpPy5Xb3JrQ29kZQ0KICAgICAgICBjb25zdCBuZXdDb2RlID0gbmV3VmFsLm1hcCh2ID0+IHYudmFsdWUpLmZpbHRlcih2ID0+ICEhdikuam9pbignLycpDQogICAgICAgIGlmICh3b3JrQ29kZSAhPT0gbmV3Q29kZSkgew0KICAgICAgICAgIHRoaXMuY3JhZnRDb2RlID0gJycNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIGRlZXA6IHRydWUNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAuLi5tYXBBY3Rpb25zKCdzY2hlZHVsZScsIFsnaW5pdFByb2Nlc3NMaXN0J10pLA0KICAgIGdldENyYWZ0UHJvY2VzcygpIHsNCiAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7DQogICAgICAgIEdldExpYkxpc3Qoew0KICAgICAgICAgIElkOiAnJywNCiAgICAgICAgICBUeXBlOiAzDQogICAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgdGhpcy5neUxpc3QgPSAocmVzLkRhdGEgfHwgW10pDQogICAgICAgICAgICByZXNvbHZlKCkNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgcmVqZWN0KCkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0sDQogICAgZ2V0UHJvY2Vzc09wdGlvbih3b3Jrc2hvcElkKSB7DQogICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4gew0KICAgICAgICB0aGlzLnBnTG9hZGluZyA9IHRydWUNCiAgICAgICAgR2V0UHJvY2Vzc0xpc3RCYXNlKHsNCiAgICAgICAgICB3b3Jrc2hvcElkOiB3b3Jrc2hvcElkLA0KICAgICAgICAgIHR5cGU6IDMNCiAgICAgICAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICB0aGlzLm9wdGlvbnMgPSByZXMuRGF0YS5tYXAodiA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJHNldCh2LCAnZGlzYWJsZWQnLCBmYWxzZSkNCiAgICAgICAgICAgICAgcmV0dXJuIHYNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgICAgcmVzb2x2ZSgpDQogICAgICAgIH0pLmZpbmFsbHkoXyA9PiB7DQogICAgICAgICAgdGhpcy5wZ0xvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHNlbGVjdENoYW5nZSh2YWwsIGVsZW1lbnQpIHsNCiAgICAgIGNvbnNvbGUubG9nKCd2YWwnLCB2YWwpDQogICAgICBjb25zb2xlLmxvZygnZWxlbWVudCcsIGVsZW1lbnQpDQogICAgICBjb25zdCBhcnIgPSB0aGlzLmxpc3QubWFwKGkgPT4gaS52YWx1ZSkNCiAgICAgIHRoaXMub3B0aW9ucy5mb3JFYWNoKChpdGVtLCBpbmRleCkgPT4gew0KICAgICAgICBpdGVtLmRpc2FibGVkID0gYXJyLmluY2x1ZGVzKGl0ZW0uQ29kZSkNCiAgICAgICAgaWYgKGl0ZW0uQ29kZSA9PT0gdmFsKSB7DQogICAgICAgICAgZWxlbWVudC5UZWFtcyA9IGl0ZW0uVGVhbXMNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIC8vIGlmIChlbGVtZW50KSB7DQogICAgICAvLyAgIGVsZW1lbnQuZGF0ZSA9IHRoaXMucHJvY2Vzc0xpc3RbdmFsXT8uRmluaXNoX0RhdGUNCiAgICAgIC8vICAgaWYgKCF2YWwpIHsNCiAgICAgIC8vICAgICBlbGVtZW50LldvcmtpbmdfVGVhbV9JZCA9ICcnDQogICAgICAvLyAgICAgZWxlbWVudC5UZWFtcyA9IFtdDQogICAgICAvLyAgIH0NCiAgICAgIC8vIH0NCiAgICAgIGlmIChlbGVtZW50KSB7DQogICAgICAgIGlmICh2YWwpIHsNCiAgICAgICAgICBlbGVtZW50LldvcmtpbmdfVGVhbV9JZCA9IHRoaXMucHJvY2Vzc0xpc3RbdmFsXT8uV29ya2luZ19UZWFtX0lkDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgZWxlbWVudC5Xb3JraW5nX1RlYW1fSWQgPSAnJw0KICAgICAgICAgIGVsZW1lbnQuVGVhbXMgPSBbXQ0KICAgICAgICB9DQogICAgICB9DQogICAgICBpZiAoIWVsZW1lbnQ/LldvcmtpbmdfVGVhbV9JZCAmJiBlbGVtZW50Py5UZWFtcz8ubGVuZ3RoID09PSAxKSB7DQogICAgICAgIGVsZW1lbnQuV29ya2luZ19UZWFtX0lkID0gZWxlbWVudC5UZWFtc1swXS5JZA0KICAgICAgfQ0KICAgIH0sDQogICAgZGF0ZUNoYW5nZSh2YWwsIGVsZW1lbnQpIHsNCiAgICAgIGNvbnN0IGl0ZW0gPSB0aGlzLm9wdGlvbnMuZmluZCh2ID0+IHYuQ29kZSA9PT0gZWxlbWVudC52YWx1ZSkNCiAgICAgIGNvbnNvbGUubG9nKCdpdGVtJywgaXRlbSwgdGhpcy5saXN0KQ0KICAgICAgbGV0IG9iaiA9IHt9DQogICAgICBpZiAoaXRlbSkgew0KICAgICAgICBvYmogPSB7DQogICAgICAgICAgU2NoZHVsaW5nX0lkOiB0aGlzLmZvcm1JbmxpbmU/LlNjaGR1bGluZ19Db2RlLA0KICAgICAgICAgIFByb2Nlc3NfSWQ6IGl0ZW0uSWQsDQogICAgICAgICAgUHJvY2Vzc19Db2RlOiBpdGVtLkNvZGUsDQogICAgICAgICAgRmluaXNoX0RhdGU6IHZhbA0KICAgICAgICB9DQogICAgICB9DQogICAgICAvLyB0aGlzLiRlbWl0KCdzZXRQcm9jZXNzTGlzdCcsIHsga2V5OiBlbGVtZW50LnZhbHVlLCB2YWx1ZTogb2JqIH0pDQogICAgfSwNCiAgICBoYW5kbGVBZGQoaXRlbSkgew0KICAgICAgY29uc3QgYXJyID0gdGhpcy5saXN0Lm1hcCh2ID0+IHYudmFsdWUpDQogICAgICB0aGlzLm9wdGlvbnMuZm9yRWFjaCh2ID0+IHsNCiAgICAgICAgaWYgKGFyci5pbmNsdWRlcyh2LkNvZGUpKSB7DQogICAgICAgICAgdi5kaXNhYmxlZCA9IHRydWUNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIHRoaXMubGlzdC5wdXNoKHsNCiAgICAgICAga2V5OiB1dWlkdjQoKSwNCiAgICAgICAgdmFsdWU6ICcnLA0KICAgICAgICBXb3JraW5nX1RlYW1fSWQ6ICcnLA0KICAgICAgICBUZWFtczogW10sDQogICAgICAgIGRhdGU6ICcnDQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlRGVsZXRlKGVsZW1lbnQpIHsNCiAgICAgIGNvbnN0IGlkeCA9IHRoaXMubGlzdC5maW5kSW5kZXgodiA9PiB2LnZhbHVlID09PSBlbGVtZW50LnZhbHVlKQ0KICAgICAgaWYgKGlkeCAhPT0gLTEpIHsNCiAgICAgICAgdGhpcy5saXN0LnNwbGljZShpZHgsIDEpDQogICAgICAgIHRoaXMuc2VsZWN0Q2hhbmdlKCkNCiAgICAgIH0NCiAgICB9LA0KICAgIGdldFdvcmtpbmdUZWFtKHRlYW1zLCBjdXJJdGVtKSB7DQogICAgICBjb25zdCBuZXdUZWFtcyA9IHRlYW1zLmZpbHRlcih2ID0+IHsNCiAgICAgICAgaWYgKHRoaXMud29ya3Nob3BJZCkgew0KICAgICAgICAgIHJldHVybiB2LldvcmtzaG9wX0lkID09PSB0aGlzLndvcmtzaG9wSWQNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgfSkNCiAgICAgIGlmICghbmV3VGVhbXMubGVuZ3RoKSB7DQogICAgICAgIGN1ckl0ZW0uV29ya2luZ19UZWFtX0lkID0gJycNCiAgICAgICAgcmV0dXJuIFtdDQogICAgICB9DQogICAgICBpZiAobmV3VGVhbXMuZXZlcnkodiA9PiB2LklkICE9PSBjdXJJdGVtLldvcmtpbmdfVGVhbV9JZCkpIHsNCiAgICAgICAgY3VySXRlbS5Xb3JraW5nX1RlYW1fSWQgPSAnJw0KICAgICAgfQ0KICAgICAgcmV0dXJuIG5ld1RlYW1zDQogICAgfSwNCiAgICBhc3luYyBzZXREYXRhKGFyciwgdGVjaG5vbG9neVN0cikgew0KICAgICAgY29uc29sZS5sb2coJ2FycicsIEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkoYXJyKSkpDQogICAgICBjb25zb2xlLmxvZygndGVjaG5vbG9neVN0cicsIHRlY2hub2xvZ3lTdHIpDQogICAgICBjb25zb2xlLmxvZygncHJvY2Vzc0xpc3QnLCB0aGlzLnByb2Nlc3NMaXN0KQ0KICAgICAgYXdhaXQgdGhpcy5nZXRDcmFmdFByb2Nlc3MoKQ0KICAgICAgbGV0IHRlY2hub2xvZ3lBcnIgPSBbXQ0KICAgICAgaWYgKHRlY2hub2xvZ3lTdHIpIHsNCiAgICAgICAgdGVjaG5vbG9neUFyciA9IHRlY2hub2xvZ3lTdHIuc3BsaXQoJy8nKQ0KICAgICAgfQ0KICAgICAgY29uc3Qgd29ya3Nob3BJZCA9IGFyclswXS5Xb3Jrc2hvcF9JZA0KICAgICAgdGhpcy53b3Jrc2hvcElkID0gd29ya3Nob3BJZA0KDQogICAgICBjb25zdCBwYXJ0VXNlZFByb2Nlc3MgPSBhcnJbMF0uVW5pdF9QYXJ0X1VzZWRfUHJvY2Vzcw0KICAgICAgYXdhaXQgdGhpcy5nZXRQcm9jZXNzT3B0aW9uKHdvcmtzaG9wSWQpDQoNCiAgICAgIHRoaXMub3B0aW9ucyA9IHRoaXMub3B0aW9ucy5maWx0ZXIoaXRlbSA9PiB7DQogICAgICAgIGxldCBmbGFnID0gZmFsc2UNCiAgICAgICAgaWYgKHRlY2hub2xvZ3lBcnIubGVuZ3RoICYmIHRlY2hub2xvZ3lBcnIuaW5jbHVkZXMoaXRlbS5Db2RlKSkgew0KICAgICAgICAgIGZsYWcgPSB0cnVlDQogICAgICAgIH0NCiAgICAgICAgaWYgKHBhcnRVc2VkUHJvY2VzcyAmJiBwYXJ0VXNlZFByb2Nlc3MgPT09IGl0ZW0uQ29kZSkgew0KICAgICAgICAgIGZsYWcgPSB0cnVlDQogICAgICAgIH0NCiAgICAgICAgaWYgKCFmbGFnKSB7DQogICAgICAgICAgZmxhZyA9ICEhaXRlbS5Jc19FbmFibGUNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gZmxhZw0KICAgICAgfSkNCiAgICAgIC8vIHRoaXMuZGVmYXVsdE9wdGlvbnMgPSBkZWVwQ2xvbmUodGhpcy5vcHRpb25zKQ0KICAgICAgdGhpcy5hcnIgPSBhcnIgfHwgbnVsbA0KICAgICAgdGhpcy5saXN0ID0gW10NCiAgICAgIGxldCBjb2RlcyA9IFtdDQoNCiAgICAgIGNvbnN0IG9yaWdpbiA9IGFyci5tYXAodiA9PiAodj8uVW5pdF9QYXJ0X1VzZWRfUHJvY2VzcyB8fCAnJykuc3BsaXQoJywnKSkNCiAgICAgIGNvZGVzID0gdGhpcy5nZXRVbmlxdWUob3JpZ2luLmZsYXQoKSkuZmlsdGVyKHYgPT4gISF2KQ0KDQogICAgICBpZiAoY29kZXMubGVuZ3RoKSB7DQogICAgICAgIGNvbnN0IGNoZWNrT3B0aW9uID0gY29kZXMuZmlsdGVyKGMgPT4gew0KICAgICAgICAgIHJldHVybiAhIXRoaXMub3B0aW9ucy5maW5kKGsgPT4gay5Db2RlID09PSBjKQ0KICAgICAgICB9KQ0KICAgICAgICBjb25zb2xlLmxvZyhjb2RlcywgY2hlY2tPcHRpb24sIHRoaXMub3B0aW9ucykNCiAgICAgICAgaWYgKGNoZWNrT3B0aW9uLmxlbmd0aCA8IGNvZGVzLmxlbmd0aCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogJ+W9k+WJjemDqOS7tueUn+S6p+aJgOWxnui9pumXtOWGheayoeacieivpemDqOS7tuaJgOWxnumbtuS7tumihueUqOW3peW6j++8jOivt+iHs+i9pumXtOeuoeeQhuWGheWFs+iBlOebuOWFs+W3peW6j+ePree7hCcsDQogICAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgICB9KQ0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQoNCiAgICAgICAgY29kZXMuZm9yRWFjaCgodmFsdWUsIGlkeCkgPT4gew0KICAgICAgICAgIGNvbnN0IG9iaiA9IHsNCiAgICAgICAgICAgIHZhbHVlLA0KICAgICAgICAgICAgaXNQYXJ0OiB0cnVlLA0KICAgICAgICAgICAga2V5OiB1dWlkdjQoKSwNCiAgICAgICAgICAgIFdvcmtpbmdfVGVhbV9JZDogdGhpcy5wcm9jZXNzTGlzdFt2YWx1ZV0/LldvcmtpbmdfVGVhbV9JZCwNCiAgICAgICAgICAgIFRlYW1zOiB0aGlzLm9wdGlvbnMuZmluZChpdGVtID0+IGl0ZW0uQ29kZSA9PT0gdmFsdWUpPy5UZWFtcyB8fCBbXSwNCiAgICAgICAgICAgIGRhdGU6IHRoaXMucHJvY2Vzc0xpc3RbdmFsdWVdPy5GaW5pc2hfRGF0ZQ0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAob2JqLlRlYW1zLmxlbmd0aCA9PT0gMSAmJiAhb2JqLldvcmtpbmdfVGVhbV9JZCkgew0KICAgICAgICAgICAgb2JqLldvcmtpbmdfVGVhbV9JZCA9IG9iai5UZWFtc1swXS5JZA0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLmxpc3QucHVzaChvYmopDQogICAgICAgIH0pDQogICAgICB9DQoNCiAgICAgIGlmICh0ZWNobm9sb2d5QXJyLmxlbmd0aCkgew0KICAgICAgICBjb25zdCB0ZWNoQXJyID0gdGVjaG5vbG9neUFyci5tYXAodiA9PiB7DQogICAgICAgICAgY29uc3Qgb2JqID0gew0KICAgICAgICAgICAga2V5OiB1dWlkdjQoKSwNCiAgICAgICAgICAgIHZhbHVlOiB2LA0KICAgICAgICAgICAgV29ya2luZ19UZWFtX0lkOiB0aGlzLnByb2Nlc3NMaXN0W3ZdPy5Xb3JraW5nX1RlYW1fSWQsDQogICAgICAgICAgICBUZWFtczogdGhpcy5vcHRpb25zLmZpbmQoaXRlbSA9PiBpdGVtLkNvZGUgPT09IHYpPy5UZWFtcyB8fCBbXSwNCiAgICAgICAgICAgIGRhdGU6IHRoaXMucHJvY2Vzc0xpc3Rbdl0/LkZpbmlzaF9EYXRlDQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChvYmouVGVhbXMubGVuZ3RoID09PSAxICYmICFvYmouV29ya2luZ19UZWFtX0lkKSB7DQogICAgICAgICAgICBvYmouV29ya2luZ19UZWFtX0lkID0gb2JqLlRlYW1zWzBdLklkDQogICAgICAgICAgfQ0KICAgICAgICAgIHJldHVybiBvYmoNCiAgICAgICAgfSkNCiAgICAgICAgY29uc29sZS5sb2coJ3RlY2hBcnInLCB0ZWNoQXJyKQ0KICAgICAgICB0ZWNoQXJyLmZvckVhY2goKGVsZW1lbnQsIGlkeCkgPT4gew0KICAgICAgICAgIGlmICghY29kZXMuaW5jbHVkZXMoZWxlbWVudC52YWx1ZSkpIHsNCiAgICAgICAgICAgIHRoaXMubGlzdC5wdXNoKGVsZW1lbnQpDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgICAgaWYgKCF0aGlzLmxpc3QubGVuZ3RoKSB7DQogICAgICAgIHRoaXMubGlzdC5wdXNoKHsNCiAgICAgICAgICB2YWx1ZTogJycsDQogICAgICAgICAga2V5OiB1dWlkdjQoKSwNCiAgICAgICAgICBXb3JraW5nX1RlYW1fSWQ6ICcnLA0KICAgICAgICAgIFRlYW1zOiBbXSwNCiAgICAgICAgICBkYXRlOiAnJw0KICAgICAgICB9KQ0KICAgICAgICBpZiAodGhpcy5pc05lc3QpIHsNCiAgICAgICAgICBjb25zdCB4dXIgPSB0aGlzLm9wdGlvbnMuZmlsdGVyKGl0ZW0gPT4gaXRlbS5Jc19OZXN0KQ0KICAgICAgICAgIGlmICh4dXIubGVuZ3RoID09PSAxKSB7DQogICAgICAgICAgICB0aGlzLmxpc3RbMF0udmFsdWUgPSB4dXJbMF0uQ29kZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgY29uc3QgaW5kZXhNYXAgPSB0ZWNobm9sb2d5QXJyLnJlZHVjZSgobWFwLCBpdGVtLCBpbmRleCkgPT4gew0KICAgICAgICBtYXBbaXRlbV0gPSBpbmRleA0KICAgICAgICByZXR1cm4gbWFwDQogICAgICB9LCB7fSkNCg0KICAgICAgdGhpcy5saXN0LnNvcnQoKGl0ZW0xLCBpdGVtMikgPT4gew0KICAgICAgICByZXR1cm4gaW5kZXhNYXBbaXRlbTEudmFsdWVdIC0gaW5kZXhNYXBbaXRlbTIudmFsdWVdDQogICAgICB9KQ0KDQogICAgICB0aGlzLnNlbGVjdENoYW5nZSgpDQogICAgfSwNCiAgICBnZXRVbmlxdWUoYXJyKSB7DQogICAgICByZXR1cm4gdW5pcXVlQXJyKGFycikNCiAgICB9LA0KICAgIGNyYWZ0Q2hhbmdlKHZhbCkgew0KICAgICAgdGhpcy5jcmFmdENvZGUgPSB2YWwNCiAgICAgIGlmICghdmFsKSB7DQogICAgICAgIC8vIHRoaXMub3B0aW9ucyA9IHRoaXMuZGVmYXVsdE9wdGlvbnMNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICBjb25zdCBpbmZvID0gdGhpcy5neUxpc3QuZmluZCh2ID0+IHYuQ29kZSA9PT0gdmFsKQ0KICAgICAgaWYgKGluZm8pIHsNCiAgICAgICAgY29uc3QgcGxpc3QgPSBpbmZvLldvcmtDb2RlLnNwbGl0KCcvJykNCiAgICAgICAgLy8gdGhpcy5vcHRpb25zID0gdGhpcy5kZWZhdWx0T3B0aW9ucy5maWx0ZXIodiA9PiBwbGlzdC5pbmNsdWRlcyh2LkNvZGUpKQ0KICAgICAgICB0aGlzLm9wdGlvbnMuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgIGlmIChwbGlzdC5pbmNsdWRlcyhpdGVtLkNvZGUpKSB7DQogICAgICAgICAgICBpdGVtLmRpc2FibGVkID0gdHJ1ZQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBpdGVtLmRpc2FibGVkID0gZmFsc2UNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICAgIGNvbnN0IG5ld0xpc3QgPSBbXQ0KICAgICAgICBwbGlzdC5mb3JFYWNoKChsaXN0VmFsLCBpZHgpID0+IHsNCiAgICAgICAgICBjb25zdCBpdGVtID0gdGhpcy5saXN0LmZpbmQodiA9PiB2LnZhbHVlID09PSBsaXN0VmFsKQ0KICAgICAgICAgIGlmIChpdGVtKSB7DQogICAgICAgICAgICBpZiAoaXRlbS5UZWFtcy5sZW5ndGggPT09IDEgJiYgIWl0ZW0uV29ya2luZ19UZWFtX0lkKSB7DQogICAgICAgICAgICAgIGl0ZW0uV29ya2luZ19UZWFtX0lkID0gaXRlbS5UZWFtc1swXS5JZA0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgbmV3TGlzdC5wdXNoKGl0ZW0pDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGNvbnN0IGl0ZW0yID0gdGhpcy5vcHRpb25zLmZpbmQodiA9PiB2LkNvZGUgPT09IGxpc3RWYWwpDQogICAgICAgICAgICBpZiAoaXRlbTIpIHsNCiAgICAgICAgICAgICAgY29uc3Qgb2JqID0gew0KICAgICAgICAgICAgICAgIGtleTogdXVpZHY0KCksDQogICAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0yLkNvZGUsDQogICAgICAgICAgICAgICAgV29ya2luZ19UZWFtX0lkOiAnJywNCiAgICAgICAgICAgICAgICBUZWFtczogaXRlbTIuVGVhbXMsDQogICAgICAgICAgICAgICAgZGF0ZTogJycNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBpZiAoaXRlbTIuVGVhbXMubGVuZ3RoID09PSAxICYmICFvYmouV29ya2luZ19UZWFtX0lkKSB7DQogICAgICAgICAgICAgICAgb2JqLldvcmtpbmdfVGVhbV9JZCA9IGl0ZW0yLlRlYW1zWzBdLklkDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgbmV3TGlzdC5wdXNoKG9iaikNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICAgIHRoaXMubGlzdCA9IG5ld0xpc3QNCiAgICAgIH0NCiAgICB9LA0KICAgIHN1Ym1pdCgpIHsNCiAgICAgIGNvbnN0IGxpc3QgPSB0aGlzLmxpc3QubWFwKGl0ZW0gPT4gaXRlbS52YWx1ZSkuZmlsdGVyKGsgPT4gISFrKQ0KICAgICAgY29uc3QgaXNUcnVlID0gdGhpcy5jaGVja0NvZGUobGlzdCkNCiAgICAgIGlmICghaXNUcnVlKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICfnm7jpgrvlt6Xluo/kuI3og73nm7jlkIwnLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIGlmICghbGlzdC5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogJ+W3peW6j+S4jeiDveWFqOS4uuepuicsDQogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgIH0pDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICBpZiAodGhpcy5pc05lc3QpIHsNCiAgICAgICAgY29uc3QgeHVyID0gdGhpcy5vcHRpb25zLmZpbHRlcihpdGVtID0+IGl0ZW0uSXNfTmVzdCkNCiAgICAgICAgaWYgKHh1ci5sZW5ndGgpIHsNCiAgICAgICAgICBjb25zdCBoYXNOZXN0ID0geHVyLnNvbWUob2JqID0+IGxpc3QuaW5jbHVkZXMob2JqLkNvZGUpKQ0KICAgICAgICAgIGlmICghaGFzTmVzdCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6ICfor7foh7PlsJHpgInmi6nkuIDkuKrlpZfmlpnlt6Xluo/vvIEnLA0KICAgICAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgdGhpcy5idG5Mb2FkaW5nID0gdHJ1ZQ0KICAgICAgY29uc3Qgc3RyID0gbGlzdC5qb2luKCcvJykNCiAgICAgIHRoaXMubGlzdC5mb3JFYWNoKChlbGVtZW50LCBpZHgpID0+IHsNCiAgICAgICAgY29uc3QgaXRlbSA9IHRoaXMub3B0aW9ucy5maW5kKHYgPT4gdi5Db2RlID09PSBlbGVtZW50LnZhbHVlKQ0KDQogICAgICAgIGxldCBvYmogPSB7fQ0KICAgICAgICBpZiAoaXRlbSkgew0KICAgICAgICAgIG9iaiA9IHsNCiAgICAgICAgICAgIFNjaGR1bGluZ19JZDogdGhpcy5mb3JtSW5saW5lPy5TY2hkdWxpbmdfQ29kZSwNCiAgICAgICAgICAgIFByb2Nlc3NfSWQ6IGl0ZW0uSWQsDQogICAgICAgICAgICBQcm9jZXNzX0NvZGU6IGl0ZW0uQ29kZSwNCiAgICAgICAgICAgIEZpbmlzaF9EYXRlOiBlbGVtZW50LmRhdGUsDQogICAgICAgICAgICBXb3JraW5nX1RlYW1fSWQ6IGVsZW1lbnQuV29ya2luZ19UZWFtX0lkDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHRoaXMuJGVtaXQoJ3NldFByb2Nlc3NMaXN0JywgeyBrZXk6IGVsZW1lbnQudmFsdWUsIHZhbHVlOiBvYmogfSkNCiAgICAgIH0pDQoNCiAgICAgIHRoaXMuJGVtaXQoJ3NlbmRQcm9jZXNzJywgeyBhcnI6IHRoaXMuYXJyLCBzdHIgfSkNCiAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlDQogICAgICB0aGlzLmhhbmRsZUNsb3NlKCkNCiAgICB9LA0KICAgIGhhbmRsZUNsb3NlKCkgew0KICAgICAgdGhpcy4kZW1pdCgnY2xvc2UnKQ0KICAgIH0sDQogICAgY2hlY2tDb2RlKGxpc3QpIHsNCiAgICAgIGxldCBmbGFnID0gdHJ1ZQ0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBsaXN0Lmxlbmd0aDsgaSsrKSB7DQogICAgICAgIGlmIChpICE9PSBsaXN0Lmxlbmd0aCAtIDEgJiYgbGlzdFtpXSA9PT0gbGlzdFtpICsgMV0pIHsNCiAgICAgICAgICBmbGFnID0gZmFsc2UNCiAgICAgICAgICBicmVhaw0KICAgICAgICB9DQogICAgICB9DQogICAgICByZXR1cm4gZmxhZw0KICAgIH0sDQogICAgY2hhbmdlRHJhZ2dhYmxlKCkgew0KICAgICAgdGhpcy5saXN0LmZvckVhY2godiA9PiB7DQogICAgICAgIHRoaXMuJHNldCh2LCAnZGF0ZScsICcnKQ0KICAgICAgfSkNCiAgICAgIHRoaXMuaW5pdFByb2Nlc3NMaXN0KCkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["BatchProcessAdjust.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BatchProcessAdjust.vue", "sourceRoot": "src/views/PRO/plan-production/schedule-production-new-unit-part/components", "sourcesContent": ["<template>\r\n  <div v-loading=\"pgLoading\" class=\"cs-container\">\r\n    <el-form ref=\"form\" :model=\"form\" label-width=\"100px\">\r\n      <el-form-item label=\"工艺代码\">\r\n        <el-select v-model=\"craftCode\" filterable placeholder=\"下拉选择支持搜索\" clearable=\"\" @change=\"craftChange\">\r\n          <el-option\r\n            v-for=\"item in gyList\"\r\n            :key=\"item.Code\"\r\n            :label=\"item.Code\"\r\n            :value=\"item.Code\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-divider />\r\n      <draggable v-model=\"list\" handle=\".icon-drag\" @change=\"changeDraggable\">\r\n        <transition-group>\r\n          <el-row v-for=\"(element,index) in list\" :key=\"element.key\">\r\n            <el-col :span=\"1\"> <i class=\"iconfont icon-drag cs-drag\" /> </el-col>\r\n            <el-col :span=\"10\">\r\n              <el-form-item :label=\"`排产工序${index+1}`\">\r\n                <el-select :key=\"element.key\" v-model=\"element.value\" style=\"width:90%\" :disabled=\"element.isPart\" placeholder=\"请选择\" clearable @change=\"selectChange($event,element)\">\r\n                  <el-option\r\n                    v-for=\"item in options\"\r\n                    :key=\"item.Code\"\r\n                    :label=\"item.Name\"\r\n                    :disabled=\"item.disabled\"\r\n                    :value=\"item.Code\"\r\n                  >\r\n                    <div class=\"cs-option\">\r\n                      <span class=\"cs-label\">{{ item.Name }}</span>\r\n                      <span v-if=\"item.Is_Nest && isNest\" class=\"cs-tip\">(套)</span>\r\n                    </div>\r\n                  </el-option>\r\n                </el-select>\r\n\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"10\">\r\n              <el-form-item label=\"班组\" label-width=\"60px\">\r\n                <el-select v-model=\"element.Working_Team_Id\" clearable placeholder=\"请选择\">\r\n                  <el-option v-for=\"item in getWorkingTeam(element.Teams,element)\" :key=\"item.Id\" :label=\"item.Name\" :value=\"item.Id\" />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"3\">\r\n              <span class=\"btn-x\">\r\n                <el-button v-if=\"index===0 && list.length<options.length\" type=\"primary\" icon=\"el-icon-plus\" circle @click=\"handleAdd\" />\r\n                <el-button v-if=\"index!==0&&!element.isPart\" type=\"danger\" icon=\"el-icon-delete\" circle @click=\"handleDelete(element)\" />\r\n              </span>\r\n            </el-col>\r\n\r\n          </el-row>\r\n        </transition-group>\r\n      </draggable>\r\n    </el-form>\r\n    <div class=\"dialog-footer\">\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n      <el-button v-if=\"list.length\" type=\"primary\" :loading=\"btnLoading\" @click=\"submit\">确 定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetProcessListBase, GetLibList } from '@/api/PRO/technology-lib'\r\nimport Draggable from 'vuedraggable'\r\nimport { uniqueArr, deepClone } from '@/utils'\r\nimport { mapActions } from 'vuex'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nexport default {\r\n  components: {\r\n    Draggable\r\n  },\r\n  props: {\r\n    pageType: {\r\n      type: String,\r\n      default: undefined\r\n    },\r\n    isNest: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    processList: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      list: [],\r\n      options: [],\r\n      // defaultOptions: [],\r\n      btnLoading: false,\r\n      pgLoading: false,\r\n      gyList: [],\r\n      craftCode: '',\r\n      form: {}\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    }\r\n  },\r\n  watch: {\r\n    list: {\r\n      handler(newVal) {\r\n        if (!this.craftCode) return\r\n        const workCode = this.gyList.find(v => v.Code === this.craftCode)?.WorkCode\r\n        const newCode = newVal.map(v => v.value).filter(v => !!v).join('/')\r\n        if (workCode !== newCode) {\r\n          this.craftCode = ''\r\n        }\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions('schedule', ['initProcessList']),\r\n    getCraftProcess() {\r\n      return new Promise((resolve, reject) => {\r\n        GetLibList({\r\n          Id: '',\r\n          Type: 3\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.gyList = (res.Data || [])\r\n            resolve()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    getProcessOption(workshopId) {\r\n      return new Promise((resolve, reject) => {\r\n        this.pgLoading = true\r\n        GetProcessListBase({\r\n          workshopId: workshopId,\r\n          type: 3\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.options = res.Data.map(v => {\r\n              this.$set(v, 'disabled', false)\r\n              return v\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          resolve()\r\n        }).finally(_ => {\r\n          this.pgLoading = false\r\n        })\r\n      })\r\n    },\r\n    selectChange(val, element) {\r\n      console.log('val', val)\r\n      console.log('element', element)\r\n      const arr = this.list.map(i => i.value)\r\n      this.options.forEach((item, index) => {\r\n        item.disabled = arr.includes(item.Code)\r\n        if (item.Code === val) {\r\n          element.Teams = item.Teams\r\n        }\r\n      })\r\n      // if (element) {\r\n      //   element.date = this.processList[val]?.Finish_Date\r\n      //   if (!val) {\r\n      //     element.Working_Team_Id = ''\r\n      //     element.Teams = []\r\n      //   }\r\n      // }\r\n      if (element) {\r\n        if (val) {\r\n          element.Working_Team_Id = this.processList[val]?.Working_Team_Id\r\n        } else {\r\n          element.Working_Team_Id = ''\r\n          element.Teams = []\r\n        }\r\n      }\r\n      if (!element?.Working_Team_Id && element?.Teams?.length === 1) {\r\n        element.Working_Team_Id = element.Teams[0].Id\r\n      }\r\n    },\r\n    dateChange(val, element) {\r\n      const item = this.options.find(v => v.Code === element.value)\r\n      console.log('item', item, this.list)\r\n      let obj = {}\r\n      if (item) {\r\n        obj = {\r\n          Schduling_Id: this.formInline?.Schduling_Code,\r\n          Process_Id: item.Id,\r\n          Process_Code: item.Code,\r\n          Finish_Date: val\r\n        }\r\n      }\r\n      // this.$emit('setProcessList', { key: element.value, value: obj })\r\n    },\r\n    handleAdd(item) {\r\n      const arr = this.list.map(v => v.value)\r\n      this.options.forEach(v => {\r\n        if (arr.includes(v.Code)) {\r\n          v.disabled = true\r\n        }\r\n      })\r\n      this.list.push({\r\n        key: uuidv4(),\r\n        value: '',\r\n        Working_Team_Id: '',\r\n        Teams: [],\r\n        date: ''\r\n      })\r\n    },\r\n    handleDelete(element) {\r\n      const idx = this.list.findIndex(v => v.value === element.value)\r\n      if (idx !== -1) {\r\n        this.list.splice(idx, 1)\r\n        this.selectChange()\r\n      }\r\n    },\r\n    getWorkingTeam(teams, curItem) {\r\n      const newTeams = teams.filter(v => {\r\n        if (this.workshopId) {\r\n          return v.Workshop_Id === this.workshopId\r\n        }\r\n        return true\r\n      })\r\n      if (!newTeams.length) {\r\n        curItem.Working_Team_Id = ''\r\n        return []\r\n      }\r\n      if (newTeams.every(v => v.Id !== curItem.Working_Team_Id)) {\r\n        curItem.Working_Team_Id = ''\r\n      }\r\n      return newTeams\r\n    },\r\n    async setData(arr, technologyStr) {\r\n      console.log('arr', JSON.parse(JSON.stringify(arr)))\r\n      console.log('technologyStr', technologyStr)\r\n      console.log('processList', this.processList)\r\n      await this.getCraftProcess()\r\n      let technologyArr = []\r\n      if (technologyStr) {\r\n        technologyArr = technologyStr.split('/')\r\n      }\r\n      const workshopId = arr[0].Workshop_Id\r\n      this.workshopId = workshopId\r\n\r\n      const partUsedProcess = arr[0].Unit_Part_Used_Process\r\n      await this.getProcessOption(workshopId)\r\n\r\n      this.options = this.options.filter(item => {\r\n        let flag = false\r\n        if (technologyArr.length && technologyArr.includes(item.Code)) {\r\n          flag = true\r\n        }\r\n        if (partUsedProcess && partUsedProcess === item.Code) {\r\n          flag = true\r\n        }\r\n        if (!flag) {\r\n          flag = !!item.Is_Enable\r\n        }\r\n        return flag\r\n      })\r\n      // this.defaultOptions = deepClone(this.options)\r\n      this.arr = arr || null\r\n      this.list = []\r\n      let codes = []\r\n\r\n      const origin = arr.map(v => (v?.Unit_Part_Used_Process || '').split(','))\r\n      codes = this.getUnique(origin.flat()).filter(v => !!v)\r\n\r\n      if (codes.length) {\r\n        const checkOption = codes.filter(c => {\r\n          return !!this.options.find(k => k.Code === c)\r\n        })\r\n        console.log(codes, checkOption, this.options)\r\n        if (checkOption.length < codes.length) {\r\n          this.$message({\r\n            message: '当前部件生产所属车间内没有该部件所属零件领用工序，请至车间管理内关联相关工序班组',\r\n            type: 'warning'\r\n          })\r\n          return\r\n        }\r\n\r\n        codes.forEach((value, idx) => {\r\n          const obj = {\r\n            value,\r\n            isPart: true,\r\n            key: uuidv4(),\r\n            Working_Team_Id: this.processList[value]?.Working_Team_Id,\r\n            Teams: this.options.find(item => item.Code === value)?.Teams || [],\r\n            date: this.processList[value]?.Finish_Date\r\n          }\r\n          if (obj.Teams.length === 1 && !obj.Working_Team_Id) {\r\n            obj.Working_Team_Id = obj.Teams[0].Id\r\n          }\r\n          this.list.push(obj)\r\n        })\r\n      }\r\n\r\n      if (technologyArr.length) {\r\n        const techArr = technologyArr.map(v => {\r\n          const obj = {\r\n            key: uuidv4(),\r\n            value: v,\r\n            Working_Team_Id: this.processList[v]?.Working_Team_Id,\r\n            Teams: this.options.find(item => item.Code === v)?.Teams || [],\r\n            date: this.processList[v]?.Finish_Date\r\n          }\r\n          if (obj.Teams.length === 1 && !obj.Working_Team_Id) {\r\n            obj.Working_Team_Id = obj.Teams[0].Id\r\n          }\r\n          return obj\r\n        })\r\n        console.log('techArr', techArr)\r\n        techArr.forEach((element, idx) => {\r\n          if (!codes.includes(element.value)) {\r\n            this.list.push(element)\r\n          }\r\n        })\r\n      }\r\n      if (!this.list.length) {\r\n        this.list.push({\r\n          value: '',\r\n          key: uuidv4(),\r\n          Working_Team_Id: '',\r\n          Teams: [],\r\n          date: ''\r\n        })\r\n        if (this.isNest) {\r\n          const xur = this.options.filter(item => item.Is_Nest)\r\n          if (xur.length === 1) {\r\n            this.list[0].value = xur[0].Code\r\n          }\r\n        }\r\n      }\r\n      const indexMap = technologyArr.reduce((map, item, index) => {\r\n        map[item] = index\r\n        return map\r\n      }, {})\r\n\r\n      this.list.sort((item1, item2) => {\r\n        return indexMap[item1.value] - indexMap[item2.value]\r\n      })\r\n\r\n      this.selectChange()\r\n    },\r\n    getUnique(arr) {\r\n      return uniqueArr(arr)\r\n    },\r\n    craftChange(val) {\r\n      this.craftCode = val\r\n      if (!val) {\r\n        // this.options = this.defaultOptions\r\n        return\r\n      }\r\n      const info = this.gyList.find(v => v.Code === val)\r\n      if (info) {\r\n        const plist = info.WorkCode.split('/')\r\n        // this.options = this.defaultOptions.filter(v => plist.includes(v.Code))\r\n        this.options.forEach((item) => {\r\n          if (plist.includes(item.Code)) {\r\n            item.disabled = true\r\n          } else {\r\n            item.disabled = false\r\n          }\r\n        })\r\n        const newList = []\r\n        plist.forEach((listVal, idx) => {\r\n          const item = this.list.find(v => v.value === listVal)\r\n          if (item) {\r\n            if (item.Teams.length === 1 && !item.Working_Team_Id) {\r\n              item.Working_Team_Id = item.Teams[0].Id\r\n            }\r\n            newList.push(item)\r\n          } else {\r\n            const item2 = this.options.find(v => v.Code === listVal)\r\n            if (item2) {\r\n              const obj = {\r\n                key: uuidv4(),\r\n                value: item2.Code,\r\n                Working_Team_Id: '',\r\n                Teams: item2.Teams,\r\n                date: ''\r\n              }\r\n              if (item2.Teams.length === 1 && !obj.Working_Team_Id) {\r\n                obj.Working_Team_Id = item2.Teams[0].Id\r\n              }\r\n              newList.push(obj)\r\n            }\r\n          }\r\n        })\r\n        this.list = newList\r\n      }\r\n    },\r\n    submit() {\r\n      const list = this.list.map(item => item.value).filter(k => !!k)\r\n      const isTrue = this.checkCode(list)\r\n      if (!isTrue) {\r\n        this.$message({\r\n          message: '相邻工序不能相同',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      if (!list.length) {\r\n        this.$message({\r\n          message: '工序不能全为空',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n\r\n      if (this.isNest) {\r\n        const xur = this.options.filter(item => item.Is_Nest)\r\n        if (xur.length) {\r\n          const hasNest = xur.some(obj => list.includes(obj.Code))\r\n          if (!hasNest) {\r\n            this.$message({\r\n              message: '请至少选择一个套料工序！',\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n        }\r\n      }\r\n\r\n      this.btnLoading = true\r\n      const str = list.join('/')\r\n      this.list.forEach((element, idx) => {\r\n        const item = this.options.find(v => v.Code === element.value)\r\n\r\n        let obj = {}\r\n        if (item) {\r\n          obj = {\r\n            Schduling_Id: this.formInline?.Schduling_Code,\r\n            Process_Id: item.Id,\r\n            Process_Code: item.Code,\r\n            Finish_Date: element.date,\r\n            Working_Team_Id: element.Working_Team_Id\r\n          }\r\n        }\r\n        this.$emit('setProcessList', { key: element.value, value: obj })\r\n      })\r\n\r\n      this.$emit('sendProcess', { arr: this.arr, str })\r\n      this.btnLoading = false\r\n      this.handleClose()\r\n    },\r\n    handleClose() {\r\n      this.$emit('close')\r\n    },\r\n    checkCode(list) {\r\n      let flag = true\r\n      for (let i = 0; i < list.length; i++) {\r\n        if (i !== list.length - 1 && list[i] === list[i + 1]) {\r\n          flag = false\r\n          break\r\n        }\r\n      }\r\n      return flag\r\n    },\r\n    changeDraggable() {\r\n      this.list.forEach(v => {\r\n        this.$set(v, 'date', '')\r\n      })\r\n      this.initProcessList()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.btn-x {\r\n  margin-left: 20px;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n  margin-top: 30px;\r\n}\r\n\r\n.cs-drag {\r\n  line-height: 32px;\r\n  cursor: move;\r\n}\r\n\r\n.cs-option {\r\n  display: flex;\r\n  justify-content: space-between;\r\n\r\n  .cs-label {\r\n\r\n  }\r\n\r\n  .cs-tip {\r\n    color: #409EFF;\r\n  }\r\n}\r\n\r\n</style>\r\n"]}]}
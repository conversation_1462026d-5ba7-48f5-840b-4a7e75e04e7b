{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\index.vue?vue&type=style&index=0&id=12f4a10b&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\index.vue", "mtime": 1758333327000}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouY29udGFpbmVyIHsKICBwYWRkaW5nOiAxNnB4OwogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKCiAgLnRvcC14IHsKICAgIHBhZGRpbmc6IDAgMTZweDsKICAgIG1hcmdpbi1ib3R0b206IDE2cHg7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmOwoKICAgIC50b3AtaW5uZXIgewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgICAgIG92ZXJmbG93OiBoaWRkZW47CgogICAgICAmOmFmdGVyIHsKICAgICAgICBjb250ZW50OiAiIjsKICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICAgICAgbGVmdDogMDsKICAgICAgICBib3R0b206IDA7CiAgICAgICAgd2lkdGg6IDEwMCU7CiAgICAgICAgaGVpZ2h0OiAycHg7CiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2U0ZTdlZDsKICAgICAgICB6LWluZGV4OiAxOwogICAgICB9CgogICAgICAuaXRlbSB7CiAgICAgICAgY29sb3I6ICM5OTk5OTk7CiAgICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogICAgICAgIHBhZGRpbmc6IDIwcHggMzhweDsKCiAgICAgICAgJjpob3ZlciB7CiAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAgICAgfQogICAgICB9CgogICAgICAuY3MtaXRlbS1iYXIgewogICAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICAgICAgYm90dG9tOiAwOwogICAgICAgIGxlZnQ6IDEwcHg7CiAgICAgICAgaGVpZ2h0OiAycHg7CiAgICAgICAgd2lkdGg6IDE0MHB4OwogICAgICAgIGJhY2tncm91bmQtY29sb3I6ICM0MDllZmY7CiAgICAgICAgei1pbmRleDogMjsKICAgICAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBjdWJpYy1iZXppZXIoMC42NDUsIDAuMDQ1LCAwLjM1NSwgMSk7CiAgICAgIH0KCiAgICAgIC5hY3RpdmUgewogICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7CiAgICAgICAgY29sb3I6ICMyOThkZmY7CiAgICAgIH0KCiAgICAgIDo6di1kZWVwIHsKICAgICAgICAuZWwtYmFkZ2VfX2NvbnRlbnQuaXMtZml4ZWQgewogICAgICAgICAgcmlnaHQ6IDc4cHg7CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgfQoKICAubWFpbi13cmFwcGVyewogICAgZmxleDogMTsKICB9CiAgLmVsLXRhYnN7CiAgICBtYXJnaW4tYm90dG9tOiAxNnB4OwogICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsKICAgIHBhZGRpbmctbGVmdDogMTZweDsKICB9Cn0KCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/plan-production/task-list", "sourcesContent": ["<template>\n  <div class=\"container abs100\">\n    <el-tabs v-model=\"activeName\" @tab-click=\"changeTab\">\n      <!-- <el-tab-pane label=\"构件任务单\" name=\"com\" />\n      <el-tab-pane label=\"部件任务单\" name=\"unitPart\" />\n      <el-tab-pane label=\"零件任务单\" name=\"part\" /> -->\n      <el-tab-pane v-for=\"item in bomList\" :key=\"item.Code\" :label=\"`${item.Display_Name}任务单`\" :name=\"item.Code\" />\n    </el-tabs>\n    <div class=\"main-wrapper\">\n      <component :is=\"'MainPage'\" :key=\"activeName\" :has-unit-part=\"hasUnitPart\" :page-type=\"activeName\" />\n    </div>\n  </div>\n</template>\n\n<script>\nimport MainPage from './mainPage'\nimport { mapGetters } from 'vuex'\nimport { GetBOMInfo, getBomCode } from '@/views/PRO/bom-setting/utils'\nexport default {\n  name: 'PROTaskList',\n  components: {\n    MainPage\n  },\n  data() {\n    return {\n      bomList: [],\n      pgLoading: false,\n      activeName: getBomCode('-1')\n    }\n  },\n  computed: {\n\n    isCom() {\n      return this.activeName === getBomCode('-1')\n    },\n    ...mapGetters('tenant', ['isVersionFour']),\n    hasUnitPart() {\n      return this.isVersionFour\n    }\n  },\n  async created() {\n    const { list } = await GetBOMInfo()\n    this.bomList = list || []\n  },\n  methods: {\n    handleClick(tab, event) {\n      console.log(tab, event)\n    },\n    async changeTab(tab) {\n      this.pgLoading = true\n      this.activeName = tab.name\n      await this.getTbConfigInfo()\n      this.fetchData(1)\n    },\n    getTbConfigInfo() {\n\n    },\n    fetchData() {\n\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.container {\n  padding: 16px;\n  display: flex;\n  flex-direction: column;\n\n  .top-x {\n    padding: 0 16px;\n    margin-bottom: 16px;\n    background-color: #ffffff;\n\n    .top-inner {\n      display: flex;\n      position: relative;\n      overflow: hidden;\n\n      &:after {\n        content: \"\";\n        position: absolute;\n        left: 0;\n        bottom: 0;\n        width: 100%;\n        height: 2px;\n        background-color: #e4e7ed;\n        z-index: 1;\n      }\n\n      .item {\n        color: #999999;\n        display: inline-block;\n        padding: 20px 38px;\n\n        &:hover {\n          cursor: pointer;\n        }\n      }\n\n      .cs-item-bar {\n        display: inline-block;\n        position: absolute;\n        bottom: 0;\n        left: 10px;\n        height: 2px;\n        width: 140px;\n        background-color: #409eff;\n        z-index: 2;\n        transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);\n      }\n\n      .active {\n        font-weight: 500;\n        color: #298dff;\n      }\n\n      ::v-deep {\n        .el-badge__content.is-fixed {\n          right: 78px;\n        }\n      }\n    }\n  }\n\n  .main-wrapper{\n    flex: 1;\n  }\n  .el-tabs{\n    margin-bottom: 16px;\n    background-color: #fff;\n    padding-left: 16px;\n  }\n}\n\n</style>\n"]}]}
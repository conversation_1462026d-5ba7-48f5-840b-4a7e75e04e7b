{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\index.vue?vue&type=style&index=0&id=12f4a10b&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\index.vue", "mtime": 1757909680924}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5jb250YWluZXIgew0KICBwYWRkaW5nOiAxNnB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KDQogIC50b3AteCB7DQogICAgcGFkZGluZzogMCAxNnB4Ow0KICAgIG1hcmdpbi1ib3R0b206IDE2cHg7DQogICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZjsNCg0KICAgIC50b3AtaW5uZXIgew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICAgIG92ZXJmbG93OiBoaWRkZW47DQoNCiAgICAgICY6YWZ0ZXIgew0KICAgICAgICBjb250ZW50OiAiIjsNCiAgICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgICBsZWZ0OiAwOw0KICAgICAgICBib3R0b206IDA7DQogICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICBoZWlnaHQ6IDJweDsNCiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2U0ZTdlZDsNCiAgICAgICAgei1pbmRleDogMTsNCiAgICAgIH0NCg0KICAgICAgLml0ZW0gew0KICAgICAgICBjb2xvcjogIzk5OTk5OTsNCiAgICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICAgICAgICBwYWRkaW5nOiAyMHB4IDM4cHg7DQoNCiAgICAgICAgJjpob3ZlciB7DQogICAgICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC5jcy1pdGVtLWJhciB7DQogICAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgICBib3R0b206IDA7DQogICAgICAgIGxlZnQ6IDEwcHg7DQogICAgICAgIGhlaWdodDogMnB4Ow0KICAgICAgICB3aWR0aDogMTQwcHg7DQogICAgICAgIGJhY2tncm91bmQtY29sb3I6ICM0MDllZmY7DQogICAgICAgIHotaW5kZXg6IDI7DQogICAgICAgIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjNzIGN1YmljLWJlemllcigwLjY0NSwgMC4wNDUsIDAuMzU1LCAxKTsNCiAgICAgIH0NCg0KICAgICAgLmFjdGl2ZSB7DQogICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgICAgIGNvbG9yOiAjMjk4ZGZmOw0KICAgICAgfQ0KDQogICAgICA6OnYtZGVlcCB7DQogICAgICAgIC5lbC1iYWRnZV9fY29udGVudC5pcy1maXhlZCB7DQogICAgICAgICAgcmlnaHQ6IDc4cHg7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAubWFpbi13cmFwcGVyew0KICAgIGZsZXg6IDE7DQogIH0NCiAgLmVsLXRhYnN7DQogICAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICAgIHBhZGRpbmctbGVmdDogMTZweDsNCiAgfQ0KfQ0KDQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/plan-production/task-list", "sourcesContent": ["<template>\r\n  <div class=\"container abs100\">\r\n    <el-tabs v-model=\"activeName\" @tab-click=\"changeTab\">\r\n      <!-- <el-tab-pane label=\"构件任务单\" name=\"com\" />\r\n      <el-tab-pane label=\"部件任务单\" name=\"unitPart\" />\r\n      <el-tab-pane label=\"零件任务单\" name=\"part\" /> -->\r\n      <el-tab-pane v-for=\"item in bomList\" :key=\"item.Code\" :label=\"item.Display_Name\" :name=\"item.Code\" />\r\n    </el-tabs>\r\n\r\n    <div class=\"main-wrapper\">\r\n      <component :is=\"'MainPage'\" :key=\"activeName\" :has-unit-part=\"hasUnitPart\" :page-type=\"activeName\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport MainPage from './mainPage'\r\nimport { mapGetters } from 'vuex'\r\nimport { GetBOMInfo, getBomCode } from '@/views/PRO/bom-setting/utils'\r\nexport default {\r\n  name: 'PROTaskList',\r\n  components: {\r\n    MainPage\r\n  },\r\n  data() {\r\n    return {\r\n      bomList: [],\r\n      pgLoading: false,\r\n      activeName: getBomCode('-1')\r\n    }\r\n  },\r\n  computed: {\r\n\r\n    isCom() {\r\n      return this.activeName === getBomCode('-1')\r\n    },\r\n    ...mapGetters('tenant', ['isVersionFour']),\r\n    hasUnitPart() {\r\n      return this.isVersionFour\r\n    }\r\n  },\r\n  async created() {\r\n    const { list } = await GetBOMInfo()\r\n    this.bomList = list || []\r\n  },\r\n  methods: {\r\n    handleClick(tab, event) {\r\n      console.log(tab, event)\r\n    },\r\n    async changeTab(tab) {\r\n      this.pgLoading = true\r\n      this.activeName = tab.name\r\n      await this.getTbConfigInfo()\r\n      this.fetchData(1)\r\n    },\r\n    getTbConfigInfo() {\r\n\r\n    },\r\n    fetchData() {\r\n\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.container {\r\n  padding: 16px;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .top-x {\r\n    padding: 0 16px;\r\n    margin-bottom: 16px;\r\n    background-color: #ffffff;\r\n\r\n    .top-inner {\r\n      display: flex;\r\n      position: relative;\r\n      overflow: hidden;\r\n\r\n      &:after {\r\n        content: \"\";\r\n        position: absolute;\r\n        left: 0;\r\n        bottom: 0;\r\n        width: 100%;\r\n        height: 2px;\r\n        background-color: #e4e7ed;\r\n        z-index: 1;\r\n      }\r\n\r\n      .item {\r\n        color: #999999;\r\n        display: inline-block;\r\n        padding: 20px 38px;\r\n\r\n        &:hover {\r\n          cursor: pointer;\r\n        }\r\n      }\r\n\r\n      .cs-item-bar {\r\n        display: inline-block;\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 10px;\r\n        height: 2px;\r\n        width: 140px;\r\n        background-color: #409eff;\r\n        z-index: 2;\r\n        transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);\r\n      }\r\n\r\n      .active {\r\n        font-weight: 500;\r\n        color: #298dff;\r\n      }\r\n\r\n      ::v-deep {\r\n        .el-badge__content.is-fixed {\r\n          right: 78px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .main-wrapper{\r\n    flex: 1;\r\n  }\r\n  .el-tabs{\r\n    margin-bottom: 16px;\r\n    background-color: #fff;\r\n    padding-left: 16px;\r\n  }\r\n}\r\n\r\n</style>\r\n"]}]}
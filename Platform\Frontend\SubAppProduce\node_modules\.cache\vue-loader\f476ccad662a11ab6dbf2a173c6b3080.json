{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\index.vue?vue&type=style&index=0&id=12f4a10b&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\index.vue", "mtime": 1758266753125}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmNvbnRhaW5lciB7DQogIHBhZGRpbmc6IDE2cHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQoNCiAgLnRvcC14IHsNCiAgICBwYWRkaW5nOiAwIDE2cHg7DQogICAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmOw0KDQogICAgLnRvcC1pbm5lciB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsNCg0KICAgICAgJjphZnRlciB7DQogICAgICAgIGNvbnRlbnQ6ICIiOw0KICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgICAgIGxlZnQ6IDA7DQogICAgICAgIGJvdHRvbTogMDsNCiAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgIGhlaWdodDogMnB4Ow0KICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTRlN2VkOw0KICAgICAgICB6LWluZGV4OiAxOw0KICAgICAgfQ0KDQogICAgICAuaXRlbSB7DQogICAgICAgIGNvbG9yOiAjOTk5OTk5Ow0KICAgICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogICAgICAgIHBhZGRpbmc6IDIwcHggMzhweDsNCg0KICAgICAgICAmOmhvdmVyIHsNCiAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLmNzLWl0ZW0tYmFyIHsNCiAgICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgICAgIGJvdHRvbTogMDsNCiAgICAgICAgbGVmdDogMTBweDsNCiAgICAgICAgaGVpZ2h0OiAycHg7DQogICAgICAgIHdpZHRoOiAxNDBweDsNCiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzQwOWVmZjsNCiAgICAgICAgei1pbmRleDogMjsNCiAgICAgICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuM3MgY3ViaWMtYmV6aWVyKDAuNjQ1LCAwLjA0NSwgMC4zNTUsIDEpOw0KICAgICAgfQ0KDQogICAgICAuYWN0aXZlIHsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgY29sb3I6ICMyOThkZmY7DQogICAgICB9DQoNCiAgICAgIDo6di1kZWVwIHsNCiAgICAgICAgLmVsLWJhZGdlX19jb250ZW50LmlzLWZpeGVkIHsNCiAgICAgICAgICByaWdodDogNzhweDsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC5tYWluLXdyYXBwZXJ7DQogICAgZmxleDogMTsNCiAgfQ0KICAuZWwtdGFic3sNCiAgICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogICAgcGFkZGluZy1sZWZ0OiAxNnB4Ow0KICB9DQp9DQoNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/plan-production/task-list", "sourcesContent": ["<template>\r\n  <div class=\"container abs100\">\r\n    <el-tabs v-model=\"activeName\" @tab-click=\"changeTab\">\r\n      <!-- <el-tab-pane label=\"构件任务单\" name=\"com\" />\r\n      <el-tab-pane label=\"部件任务单\" name=\"unitPart\" />\r\n      <el-tab-pane label=\"零件任务单\" name=\"part\" /> -->\r\n      <el-tab-pane v-for=\"item in bomList\" :key=\"item.Code\" :label=\"item.Display_Name\" :name=\"item.Code\" />\r\n    </el-tabs>\r\n    <div class=\"main-wrapper\">\r\n      <component :is=\"'MainPage'\" :key=\"activeName\" :has-unit-part=\"hasUnitPart\" :page-type=\"activeName\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport MainPage from './mainPage'\r\nimport { mapGetters } from 'vuex'\r\nimport { GetBOMInfo, getBomCode } from '@/views/PRO/bom-setting/utils'\r\nexport default {\r\n  name: 'PROTaskList',\r\n  components: {\r\n    MainPage\r\n  },\r\n  data() {\r\n    return {\r\n      bomList: [],\r\n      pgLoading: false,\r\n      activeName: getBomCode('-1')\r\n    }\r\n  },\r\n  computed: {\r\n\r\n    isCom() {\r\n      return this.activeName === getBomCode('-1')\r\n    },\r\n    ...mapGetters('tenant', ['isVersionFour']),\r\n    hasUnitPart() {\r\n      return this.isVersionFour\r\n    }\r\n  },\r\n  async created() {\r\n    const { list } = await GetBOMInfo()\r\n    this.bomList = list || []\r\n  },\r\n  methods: {\r\n    handleClick(tab, event) {\r\n      console.log(tab, event)\r\n    },\r\n    async changeTab(tab) {\r\n      this.pgLoading = true\r\n      this.activeName = tab.name\r\n      await this.getTbConfigInfo()\r\n      this.fetchData(1)\r\n    },\r\n    getTbConfigInfo() {\r\n\r\n    },\r\n    fetchData() {\r\n\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.container {\r\n  padding: 16px;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .top-x {\r\n    padding: 0 16px;\r\n    margin-bottom: 16px;\r\n    background-color: #ffffff;\r\n\r\n    .top-inner {\r\n      display: flex;\r\n      position: relative;\r\n      overflow: hidden;\r\n\r\n      &:after {\r\n        content: \"\";\r\n        position: absolute;\r\n        left: 0;\r\n        bottom: 0;\r\n        width: 100%;\r\n        height: 2px;\r\n        background-color: #e4e7ed;\r\n        z-index: 1;\r\n      }\r\n\r\n      .item {\r\n        color: #999999;\r\n        display: inline-block;\r\n        padding: 20px 38px;\r\n\r\n        &:hover {\r\n          cursor: pointer;\r\n        }\r\n      }\r\n\r\n      .cs-item-bar {\r\n        display: inline-block;\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 10px;\r\n        height: 2px;\r\n        width: 140px;\r\n        background-color: #409eff;\r\n        z-index: 2;\r\n        transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);\r\n      }\r\n\r\n      .active {\r\n        font-weight: 500;\r\n        color: #298dff;\r\n      }\r\n\r\n      ::v-deep {\r\n        .el-badge__content.is-fixed {\r\n          right: 78px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .main-wrapper{\r\n    flex: 1;\r\n  }\r\n  .el-tabs{\r\n    margin-bottom: 16px;\r\n    background-color: #fff;\r\n    padding-left: 16px;\r\n  }\r\n}\r\n\r\n</style>\r\n"]}]}
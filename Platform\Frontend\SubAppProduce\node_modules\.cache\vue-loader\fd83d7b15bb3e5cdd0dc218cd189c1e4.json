{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\ship\\component\\AddEdit.vue?vue&type=template&id=0c35ada4&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\ship\\component\\AddEdit.vue", "mtime": 1758677034218}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXY+CiAgPGVsLWZvcm0gcmVmPSJmb3JtIiA6bW9kZWw9ImZvcm0iIDpydWxlcz0icnVsZXMiIGxhYmVsLXdpZHRoPSI4MHB4IiBzdHlsZT0id2lkdGg6IDEwMCUiPgogICAgPCEtLSAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuacjeWKoeW3peWOgiIgcHJvcD0iRmFjdG9yeV9JZCI+CiAgICAgIDxlbC1zZWxlY3Qgdi1tb2RlbD0iZm9ybS5GYWN0b3J5X0lkIiBjbGFzcz0idzEwMCIgbXVsdGlwbGUgcGxhY2Vob2xkZXI9Iuivt+mAieaLqSIgY2xlYXJhYmxlPSIiPgogICAgICAgIDxlbC1vcHRpb24KICAgICAgICAgIHYtZm9yPSJpdGVtIGluIGZhY3RvcnkiCiAgICAgICAgICA6a2V5PSJpdGVtLklkIgogICAgICAgICAgOmxhYmVsPSJpdGVtLk5hbWUiCiAgICAgICAgICA6dmFsdWU9Iml0ZW0uSWQiCiAgICAgICAgLz4KICAgICAgPC9lbC1zZWxlY3Q+CiAgICA8L2VsLWZvcm0taXRlbT4tLT4KICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuiIueWPtyIgcHJvcD0iU2hpcG51bWJlciI+CiAgICAgIDxlbC1pbnB1dCB2LW1vZGVsPSJmb3JtLlNoaXBudW1iZXIiIGNsZWFyYWJsZSBAY2hhbmdlPSJnZXRMaWNlbnNlIiAvPgogICAgPC9lbC1mb3JtLWl0ZW0+CiAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLoiLnplb8iIHByb3A9IkNhcHRhaW4iPgogICAgICA8ZWwtaW5wdXQgdi1tb2RlbD0iZm9ybS5DYXB0YWluIiBjbGVhcmFibGUgLz4KICAgIDwvZWwtZm9ybS1pdGVtPgogICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i55S16K+dIiBwcm9wPSJNb2JpbGUiPgogICAgICA8ZWwtaW5wdXQgdi1tb2RlbD0iZm9ybS5Nb2JpbGUiIGNsZWFyYWJsZSAvPgogICAgPC9lbC1mb3JtLWl0ZW0+CiAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLov5DovpPljZXkvY0iIHByb3A9IlVuaXQiPgogICAgICA8ZWwtaW5wdXQgdi1tb2RlbD0iZm9ybS5Vbml0IiBjbGVhcmFibGUgLz4KICAgIDwvZWwtZm9ybS1pdGVtPgogICAgPGVsLWZvcm0taXRlbSBzdHlsZT0idGV4dC1hbGlnbjogcmlnaHQiPgogICAgICA8ZWwtYnV0dG9uIEBjbGljaz0iJGVtaXQoJ2Nsb3NlJykiPuWPliDmtog8L2VsLWJ1dHRvbj4KICAgICAgPCEtLSA8ZWwtYnV0dG9uIHYtaWY9InNob3dEZWxldGUiIHR5cGU9ImRhbmdlciIgQGNsaWNrPSJoYW5kbGVEZWxldGUiPuWIoCDpmaQ8L2VsLWJ1dHRvbj4gLS0+CiAgICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgOmxvYWRpbmc9ImJ0bkxvYWRpbmciIEBjbGljaz0iaGFuZGxlU3VibWl0Ij7noa4g5a6aPC9lbC1idXR0b24+CiAgICA8L2VsLWZvcm0taXRlbT4KICA8L2VsLWZvcm0+CjwvZGl2Pgo="}, null]}
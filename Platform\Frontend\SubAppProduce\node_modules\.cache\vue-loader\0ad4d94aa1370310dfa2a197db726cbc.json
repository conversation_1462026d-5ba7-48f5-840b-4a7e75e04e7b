{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\template-print\\detail.vue?vue&type=template&id=0e6d778d&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\template-print\\detail.vue", "mtime": 1757468128158}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}
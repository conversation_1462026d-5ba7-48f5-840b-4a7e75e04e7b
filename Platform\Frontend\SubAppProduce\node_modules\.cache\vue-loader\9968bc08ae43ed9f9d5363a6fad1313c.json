{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production\\components\\addDraft.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production\\components\\addDraft.vue", "mtime": 1757468113335}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRHcmlkQnlDb2RlIH0gZnJvbSAnQC9hcGkvc3lzJw0KaW1wb3J0IHsgR2V0Q2FuU2NoZHVsaW5nQ29tcHMgfSBmcm9tICdAL2FwaS9QUk8vcHJvZHVjdGlvbi10YXNrJw0KaW1wb3J0IHsgR2V0UGFydExpc3QgfSBmcm9tICdAL2FwaS9QUk8vcHJvZHVjdGlvbi1wYXJ0Jw0KaW1wb3J0IHsgdjQgYXMgdXVpZHY0IH0gZnJvbSAndXVpZCcNCmltcG9ydCB7IEZJWF9DT0xVTU4gfSBmcm9tICdAL3ZpZXdzL1BSTy9wbGFuLXByb2R1Y3Rpb24vc2NoZWR1bGUtcHJvZHVjdGlvbi9jb25zdGFudCcNCmltcG9ydCB7IGRlYm91bmNlLCBkZWVwQ2xvbmUgfSBmcm9tICdAL3V0aWxzJw0KaW1wb3J0IHsgdGFibGVQYWdlU2l6ZSB9IGZyb20gJ0Avdmlld3MvUFJPL3NldHRpbmcnDQppbXBvcnQgeyBHZXRDb21wVHlwZVRyZWUgfSBmcm9tICdAL2FwaS9QUk8vcHJvZmVzc2lvbmFsVHlwZScNCmltcG9ydCB7IEdldFBhcnRUeXBlTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9wYXJ0VHlwZScNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBwcm9wczogew0KICAgIHNjaGVkdWxlSWQ6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICcnDQogICAgfSwNCiAgICBwYWdlVHlwZTogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJ2NvbScNCiAgICB9LA0KICAgIHNob3dEaWFsb2c6IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiBmYWxzZQ0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgcGFnZUluZm86IHsNCiAgICAgICAgcGFnZTogMSwNCiAgICAgICAgcGFnZVNpemU6IDUwMCwNCiAgICAgICAgcGFnZVNpemVzOiB0YWJsZVBhZ2VTaXplLA0KICAgICAgICB0b3RhbDogMA0KICAgICAgfSwNCiAgICAgIGZvcm06IHsNCiAgICAgICAgQ29tcF9Db2RlOiAnJywNCiAgICAgICAgQ29tcF9Db2RlQmx1cjogJycsDQogICAgICAgIFBhcnRfQ29kZUJsdXI6ICcnLA0KICAgICAgICBQYXJ0X0NvZGU6ICcnLA0KICAgICAgICBUeXBlX05hbWU6ICcnLA0KICAgICAgICBTcGVjOiAnJywNCiAgICAgICAgVHlwZTogJycNCiAgICAgIH0sDQogICAgICBpc093bmVyTnVsbDogdHJ1ZSwNCiAgICAgIHRiTG9hZGluZzogZmFsc2UsDQogICAgICBzYXZlTG9hZGluZzogZmFsc2UsDQogICAgICBjb2x1bW5zOiBbXSwNCiAgICAgIGZUYWJsZTogW10sDQogICAgICB0YkNvbmZpZzoge30sDQogICAgICBUb3RhbENvdW50OiAwLA0KICAgICAgUGFnZTogMCwNCiAgICAgIG11bHRpcGxlU2VsZWN0aW9uOiBbXSwNCiAgICAgIHRvdGFsU2VsZWN0aW9uOiBbXSwNCiAgICAgIHNlYXJjaDogKCkgPT4gKHt9KSwNCiAgICAgIHRyZWVTZWxlY3RQYXJhbXM6IHsNCiAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fpgInmi6knLA0KICAgICAgICBjbGVhcmFibGU6IHRydWUNCiAgICAgIH0sDQogICAgICBPYmplY3RUeXBlTGlzdDogew0KICAgICAgICAvLyDmnoTku7bnsbvlnosNCiAgICAgICAgJ2NoZWNrLXN0cmljdGx5JzogdHJ1ZSwNCiAgICAgICAgJ2RlZmF1bHQtZXhwYW5kLWFsbCc6IHRydWUsDQogICAgICAgIGNsaWNrUGFyZW50OiB0cnVlLA0KICAgICAgICBkYXRhOiBbXSwNCiAgICAgICAgcHJvcHM6IHsNCiAgICAgICAgICBjaGlsZHJlbjogJ0NoaWxkcmVuJywNCiAgICAgICAgICBsYWJlbDogJ0xhYmVsJywNCiAgICAgICAgICB2YWx1ZTogJ0RhdGEnDQogICAgICAgIH0NCiAgICAgIH0sDQogICAgICB0eXBlT3B0aW9uOiBbXQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICBpc0NvbSgpIHsNCiAgICAgIHJldHVybiB0aGlzLnBhZ2VUeXBlID09PSAnY29tJw0KICAgIH0NCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICBzaG93RGlhbG9nKG5ld1ZhbHVlKSB7DQogICAgICBuZXdWYWx1ZSAmJiAodGhpcy5zYXZlTG9hZGluZyA9IGZhbHNlKQ0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmdldENvbmZpZygpDQogICAgaWYgKHRoaXMuaXNDb20pIHsNCiAgICAgIHRoaXMuZ2V0T2JqZWN0VHlwZUxpc3QoKQ0KICAgIH0gZWxzZSB7DQogICAgICB0aGlzLmdldFR5cGUoKQ0KICAgIH0NCiAgICB0aGlzLnNlYXJjaCA9IGRlYm91bmNlKHRoaXMuZmV0Y2hEYXRhLCA4MDAsIHRydWUpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBhc3luYyBnZXRDb25maWcoKSB7DQogICAgICBsZXQgY29kZSA9ICcnDQogICAgICBjb2RlID0gdGhpcy5pc0NvbQ0KICAgICAgICA/ICdQUk9Db21EcmFmdEVkaXRUYkNvbmZpZycNCiAgICAgICAgOiAnUFJPUGFydERyYWZ0RWRpdFRiQ29uZmlnJw0KICAgICAgYXdhaXQgdGhpcy5nZXRUYWJsZUNvbmZpZyhjb2RlKQ0KICAgICAgdGhpcy5mZXRjaERhdGEoKQ0KICAgIH0sDQogICAgZmlsdGVyRGF0YShwYWdlKSB7DQogICAgICBjb25zdCBmID0gW10NCiAgICAgIGZvciAoY29uc3QgZm9ybUtleSBpbiB0aGlzLmZvcm0pIHsNCiAgICAgICAgaWYgKHRoaXMuZm9ybVtmb3JtS2V5XSB8fCB0aGlzLmZvcm1bZm9ybUtleV0gPT09IGZhbHNlKSB7DQogICAgICAgICAgZi5wdXNoKGZvcm1LZXkpDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIGlmICghZi5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy5zZXRQYWdlKCkNCiAgICAgICAgIXBhZ2UgJiYgKHRoaXMucGFnZUluZm8ucGFnZSA9IDEpDQogICAgICAgIHRoaXMucGFnZUluZm8udG90YWwgPSB0aGlzLnRiRGF0YS5sZW5ndGgNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICBjb25zdCB0ZW1UYkRhdGEgPSB0aGlzLnRiRGF0YS5maWx0ZXIodiA9PiB7DQogICAgICAgIHYuY2hlY2tlZCA9IGZhbHNlDQogICAgICAgIGlmICh0aGlzLmZvcm0uQ29tcF9Db2RlLnRyaW0oKSAmJiAhdGhpcy5mb3JtWydDb21wX0NvZGUnXS5zcGxpdCgnICcpLmluY2x1ZGVzKHZbJ0NvbXBfQ29kZSddKSkgew0KICAgICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgICB9DQogICAgICAgIGlmICh0aGlzLmZvcm0uQ29tcF9Db2RlQmx1ci50cmltKCkgJiYgIXYuQ29tcF9Db2RlLmluY2x1ZGVzKHRoaXMuZm9ybS5Db21wX0NvZGVCbHVyKSkgew0KICAgICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgICB9DQogICAgICAgIGlmICh0aGlzLmZvcm0uVHlwZSAmJiB2LlR5cGUgIT09IHRoaXMuZm9ybS5UeXBlKSB7DQogICAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICAgIH0NCiAgICAgICAgaWYgKHRoaXMuZm9ybS5QYXJ0X0NvZGVCbHVyLnRyaW0oKSAmJiAhdi5QYXJ0X0NvZGUuaW5jbHVkZXModGhpcy5mb3JtLlBhcnRfQ29kZUJsdXIpKSB7DQogICAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICAgIH0NCiAgICAgICAgaWYgKHRoaXMuZm9ybS5QYXJ0X0NvZGUudHJpbSgpICYmICF0aGlzLmZvcm1bJ1BhcnRfQ29kZSddLnNwbGl0KCcgJykuaW5jbHVkZXModlsnUGFydF9Db2RlJ10pKSB7DQogICAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICAgIH0NCiAgICAgICAgaWYgKHRoaXMuZm9ybS5UeXBlX05hbWUgIT09ICcnICYmIHYuVHlwZV9OYW1lICE9PSB0aGlzLmZvcm0uVHlwZV9OYW1lKSB7DQogICAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICAgIH0NCiAgICAgICAgaWYgKHRoaXMuZm9ybS5TcGVjLnRyaW0oKSAhPT0gJycgJiYgIXYuU3BlYy5pbmNsdWRlcyh0aGlzLmZvcm0uU3BlYykpIHsNCiAgICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgfSkNCg0KICAgICAgY29uc29sZS5sb2coJ3BhZ2UnLCBwYWdlKQ0KICAgICAgIXBhZ2UgJiYgKHRoaXMucGFnZUluZm8ucGFnZSA9IDEpDQogICAgICB0aGlzLnBhZ2VJbmZvLnRvdGFsID0gdGVtVGJEYXRhLmxlbmd0aA0KICAgICAgdGhpcy5zZXRQYWdlKHRlbVRiRGF0YSkNCiAgICB9LA0KICAgIGhhbmRsZVNlYXJjaCgpIHsNCiAgICAgIHRoaXMudG90YWxTZWxlY3Rpb24gPSBbXQ0KICAgICAgdGhpcy5jbGVhclNlbGVjdCgpDQogICAgICBpZiAodGhpcy50YkRhdGE/Lmxlbmd0aCkgew0KICAgICAgICB0aGlzLnRiRGF0YS5mb3JFYWNoKGl0ZW0gPT4gaXRlbS5jaGVja2VkID0gZmFsc2UpDQogICAgICAgIHRoaXMuZmlsdGVyRGF0YSgpDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVTZWxlY3QoZGF0YSkgew0KICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbiA9IGRhdGENCiAgICB9LA0KICAgIHRiU2VsZWN0Q2hhbmdlKGFycmF5KSB7DQogICAgICBjb25zb2xlLmxvZygnYXJyYXknLCBhcnJheSkNCiAgICAgIHRoaXMudG90YWxTZWxlY3Rpb24gPSB0aGlzLnRiRGF0YS5maWx0ZXIodiA9PiB2LmNoZWNrZWQpDQogICAgfSwNCiAgICBjbGVhclNlbGVjdCgpIHsNCiAgICAgIHRoaXMuJHJlZnMueFRhYmxlMS5jbGVhckNoZWNrYm94Um93KCkNCiAgICAgIHRoaXMudG90YWxTZWxlY3Rpb24gPSBbXQ0KICAgIH0sDQogICAgYXN5bmMgZmV0Y2hEYXRhKCkgew0KICAgICAgdGhpcy50YkxvYWRpbmcgPSB0cnVlDQogICAgICBpZiAodGhpcy5pc0NvbSkgew0KICAgICAgICBhd2FpdCB0aGlzLmdldENvbVRiRGF0YSgpDQogICAgICB9IGVsc2Ugew0KICAgICAgICBhd2FpdCB0aGlzLmdldFBhcnRUYkRhdGEoKQ0KICAgICAgfQ0KICAgICAgdGhpcy5pbml0VGJEYXRhKCkNCiAgICAgIHRoaXMuZmlsdGVyRGF0YSgpDQogICAgICB0aGlzLnRiTG9hZGluZyA9IGZhbHNlDQogICAgfSwNCiAgICBzZXRQYWdlRGF0YSgpIHsNCiAgICAgIGlmICh0aGlzLnRiRGF0YT8ubGVuZ3RoKSB7DQogICAgICAgIHRoaXMucGFnZUluZm8ucGFnZSA9IDENCiAgICAgICAgdGhpcy50YkRhdGEgPSB0aGlzLnRiRGF0YS5maWx0ZXIodiA9PiB2LkNhbl9TY2hkdWxpbmdfQ291bnQgPiAwKQ0KICAgICAgICB0aGlzLmZpbHRlckRhdGEoKQ0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlU2F2ZSgpIHsNCiAgICAgIHRoaXMuc2F2ZUxvYWRpbmcgPSB0cnVlDQogICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgdGhpcy50b3RhbFNlbGVjdGlvbi5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgY29uc3QgaW50Q291bnQgPSBwYXJzZUludChpdGVtLmNvdW50KQ0KICAgICAgICAgIGl0ZW0uU2NoZHVsZWRfQ291bnQgKz0gaW50Q291bnQNCiAgICAgICAgICBpdGVtLkNhbl9TY2hkdWxpbmdfQ291bnQgLT0gaW50Q291bnQNCiAgICAgICAgICBpdGVtLkNhbl9TY2hkdWxpbmdfV2VpZ2h0ID0gaXRlbS5DYW5fU2NoZHVsaW5nX0NvdW50ICogaXRlbS5XZWlnaHQNCiAgICAgICAgICBpdGVtLm1heENvdW50ID0gaXRlbS5DYW5fU2NoZHVsaW5nX0NvdW50DQogICAgICAgICAgaXRlbS5jaG9vc2VDb3VudCA9IGludENvdW50DQogICAgICAgICAgaXRlbS5jb3VudCA9IGl0ZW0uQ2FuX1NjaGR1bGluZ19Db3VudA0KICAgICAgICAgIGl0ZW0uY2hlY2tlZCA9IGZhbHNlDQogICAgICAgIH0pDQogICAgICAgIGNvbnN0IGNwID0gZGVlcENsb25lKHRoaXMudG90YWxTZWxlY3Rpb24pDQoNCiAgICAgICAgdGhpcy4kZW1pdCgnc2VuZFNlbGVjdExpc3QnLCBjcCkNCiAgICAgICAgdGhpcy4kZW1pdCgnY2xvc2UnKQ0KICAgICAgICB0aGlzLmNsZWFyU2VsZWN0KCkNCiAgICAgICAgdGhpcy5zZXRQYWdlKCkNCiAgICAgIH0sIDApDQogICAgfSwNCiAgICBpbml0VGJEYXRhKCkgew0KICAgICAgaWYgKCF0aGlzLnRiRGF0YT8ubGVuZ3RoKSB7DQogICAgICAgIHRoaXMudGJEYXRhID0gW10NCiAgICAgICAgdGhpcy5iYWNrZW5kVGIgPSBbXQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIC8vIOiuvue9ruaWh+acrOahhumAieaLqeeahOaOkuS6p+aVsOmHjyzorr7nva7oh6rlrprkuYnllK/kuIDnoIENCiAgICAgIGNvbnN0IG9iaktleSA9IHt9DQogICAgICB0aGlzLnRiRGF0YS5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgIHRoaXMuJHNldChpdGVtLCAnY291bnQnLCBpdGVtLkNhbl9TY2hkdWxpbmdfQ291bnQpDQogICAgICAgIHRoaXMuJHNldChpdGVtLCAnbWF4Q291bnQnLCBpdGVtLkNhbl9TY2hkdWxpbmdfQ291bnQpDQogICAgICAgIGl0ZW0udXVpZCA9IHV1aWR2NCgpDQogICAgICAgIG9iaktleVtpdGVtLlR5cGVdID0gdHJ1ZQ0KICAgICAgfSkNCiAgICAgIHRoaXMuYmFja2VuZFRiID0gZGVlcENsb25lKHRoaXMudGJEYXRhKQ0KICAgIH0sDQogICAgYXN5bmMgZ2V0Q29tVGJEYXRhKCkgew0KICAgICAgY29uc3QgeyBpbnN0YWxsLCBhcmVhSWQgfSA9IHRoaXMuJHJvdXRlLnF1ZXJ5DQogICAgICBjb25zdCB7IENvbXBfQ29kZXMsIC4uLm9iaiB9ID0gdGhpcy5mb3JtDQogICAgICBsZXQgY29kZXMgPSBbXQ0KICAgICAgaWYgKE9iamVjdC5wcm90b3R5cGUudG9TdHJpbmcuY2FsbChDb21wX0NvZGVzKSA9PT0gJ1tvYmplY3QgU3RyaW5nXScpIHsNCiAgICAgICAgY29kZXMgPSBDb21wX0NvZGVzICYmIENvbXBfQ29kZXMuc3BsaXQoJyAnKS5maWx0ZXIodiA9PiAhIXYpDQogICAgICB9DQogICAgICBhd2FpdCBHZXRDYW5TY2hkdWxpbmdDb21wcyh7DQogICAgICAgIC4uLm9iaiwNCiAgICAgICAgU2NoZHVsaW5nX1BsYW5fSWQ6IHRoaXMuc2NoZWR1bGVJZCwNCiAgICAgICAgQ29tcF9Db2RlczogY29kZXMsDQogICAgICAgIEluc3RhbGxVbml0X0lkOiBpbnN0YWxsLA0KICAgICAgICBBcmVhX0lkOiBhcmVhSWQNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMucGFnZUluZm8udG90YWwgPSByZXMuRGF0YS5sZW5ndGgNCiAgICAgICAgICB0aGlzLnRiRGF0YSA9IHJlcy5EYXRhLm1hcCgodiwgaWR4KSA9PiB7DQogICAgICAgICAgICAvLyDlt7LmjpLkuqfotYvlgLwNCiAgICAgICAgICAgIHYub3JpZ2luYWxQYXRoID0gdi5TY2hlZHVsZWRfVGVjaG5vbG9neV9QYXRoID8gdi5TY2hlZHVsZWRfVGVjaG5vbG9neV9QYXRoIDogJycNCiAgICAgICAgICAgIHYuV29ya3Nob3BfSWQgPSB2LlNjaGVkdWxlZF9Xb3Jrc2hvcF9JZA0KICAgICAgICAgICAgdi5Xb3Jrc2hvcF9OYW1lID0gdi5TY2hlZHVsZWRfV29ya3Nob3BfTmFtZQ0KICAgICAgICAgICAgdi5UZWNobm9sb2d5X1BhdGggPSB2LlNjaGVkdWxlZF9UZWNobm9sb2d5X1BhdGggfHwgdi5UZWNobm9sb2d5X1BhdGgNCiAgICAgICAgICAgIGlmICh2Lm9yaWdpbmFsUGF0aCkgew0KICAgICAgICAgICAgICB2LmlzRGlzYWJsZWQgPSB0cnVlDQogICAgICAgICAgICB9DQogICAgICAgICAgICB2LmNoZWNrZWQgPSBmYWxzZQ0KICAgICAgICAgICAgdi5pbml0Um93SW5kZXggPSBpZHgNCiAgICAgICAgICAgIHJldHVybiB2DQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLnNldFBhZ2UoKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKg0KICAgICAqIOWIhumhtQ0KICAgICAqLw0KICAgIGhhbmRsZVBhZ2VDaGFuZ2UoeyBjdXJyZW50UGFnZSwgcGFnZVNpemUgfSkgew0KICAgICAgY29uc29sZS5sb2coJyBjdXJyZW50UGFnZSwgcGFnZVNpemUnLCBjdXJyZW50UGFnZSwgcGFnZVNpemUpDQogICAgICBpZiAodGhpcy50YkxvYWRpbmcpIHJldHVybg0KICAgICAgdGhpcy5wYWdlSW5mby5wYWdlID0gY3VycmVudFBhZ2UNCiAgICAgIHRoaXMucGFnZUluZm8ucGFnZVNpemUgPSBwYWdlU2l6ZQ0KICAgICAgdGhpcy5zZXRQYWdlKCkNCiAgICAgIHRoaXMuZmlsdGVyRGF0YShjdXJyZW50UGFnZSkNCiAgICB9LA0KDQogICAgc2V0UGFnZSh0YiA9IHRoaXMudGJEYXRhKSB7DQogICAgICB0aGlzLmZUYWJsZSA9IHRiLnNsaWNlKCh0aGlzLnBhZ2VJbmZvLnBhZ2UgLSAxKSAqIHRoaXMucGFnZUluZm8ucGFnZVNpemUsIHRoaXMucGFnZUluZm8ucGFnZSAqIHRoaXMucGFnZUluZm8ucGFnZVNpemUpDQogICAgfSwNCg0KICAgIGFzeW5jIGdldFBhcnRUYkRhdGEoKSB7DQogICAgICBjb25zdCB7IGluc3RhbGwsIGFyZWFJZCB9ID0gdGhpcy4kcm91dGUucXVlcnkNCiAgICAgIGF3YWl0IEdldFBhcnRMaXN0KHsNCiAgICAgICAgLi4udGhpcy5mb3JtLA0KICAgICAgICBTY2hkdWxpbmdfUGxhbl9JZDogdGhpcy5zY2hlZHVsZUlkLA0KICAgICAgICBJbnN0YWxsVW5pdF9JZDogaW5zdGFsbCwNCiAgICAgICAgQXJlYV9JZDogYXJlYUlkDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLnBhZ2VJbmZvLnRvdGFsID0gcmVzLkRhdGEubGVuZ3RoDQogICAgICAgICAgdGhpcy50YkRhdGEgPSByZXMuRGF0YS5tYXAoKHYsIGlkeCkgPT4gew0KICAgICAgICAgICAgaWYgKHYuQ29tcG9uZW50X1RlY2hub2xvZ3lfUGF0aCkgew0KICAgICAgICAgICAgICBjb25zdCBsaXN0ID0gdi5Db21wb25lbnRfVGVjaG5vbG9neV9QYXRoLnNwbGl0KCcvJykNCiAgICAgICAgICAgICAgaWYgKGxpc3QubGVuZ3RoICYmIGxpc3Quc29tZSh4ID0+IHggPT09IHYuUGFydF9Vc2VkX1Byb2Nlc3MpKSB7DQogICAgICAgICAgICAgICAgdi5vcmlnaW5hbFVzZWRQcm9jZXNzID0gdi5QYXJ0X1VzZWRfUHJvY2Vzcw0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIHYub3JpZ2luYWxVc2VkUHJvY2VzcyA9ICcnDQogICAgICAgICAgICAgICAgdi5QYXJ0X1VzZWRfUHJvY2VzcyA9ICcnDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHYub3JpZ2luYWxQYXRoID0gdi5TY2hlZHVsZWRfVGVjaG5vbG9neV9QYXRoID8gdi5TY2hlZHVsZWRfVGVjaG5vbG9neV9QYXRoIDogJycNCiAgICAgICAgICAgIHYuV29ya3Nob3BfSWQgPSB2LlNjaGVkdWxlZF9Xb3Jrc2hvcF9JZA0KICAgICAgICAgICAgdi5Xb3Jrc2hvcF9OYW1lID0gdi5TY2hlZHVsZWRfV29ya3Nob3BfTmFtZQ0KICAgICAgICAgICAgdi5QYXJ0X1VzZWRfUHJvY2VzcyA9IHYuU2NoZWR1bGVkX1VzZWRfUHJvY2VzcyB8fCB2LlBhcnRfVXNlZF9Qcm9jZXNzLy8g5piv5ZCm5a2Y5Zyo5bey5L2/55So55qE6aKG55So5bel5bqPDQogICAgICAgICAgICB2LlRlY2hub2xvZ3lfUGF0aCA9IHYuU2NoZWR1bGVkX1RlY2hub2xvZ3lfUGF0aCB8fCB2LlRlY2hub2xvZ3lfUGF0aA0KICAgICAgICAgICAgdi5pc0Rpc2FibGVkID0gISF2Lm9yaWdpbmFsUGF0aA0KICAgICAgICAgICAgdi5jaGVja2VkID0gZmFsc2UNCiAgICAgICAgICAgIHYuaW5pdFJvd0luZGV4ID0gaWR4DQogICAgICAgICAgICByZXR1cm4gdg0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy5zZXRQYXJ0Q29sdW1uKCkNCiAgICAgICAgICB0aGlzLnNldFBhZ2UoKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHNldFBhcnRDb2x1bW4oKSB7DQogICAgICAvLyDnuq/pm7bku7YNCiAgICAgIHRoaXMuaXNPd25lck51bGwgPSB0aGlzLnRiRGF0YS5ldmVyeSh2ID0+ICF2LkNvbXBfSW1wb3J0X0RldGFpbF9JZCkNCiAgICAgIGNvbnNvbGUubG9nKCd0aGlzLmlzT3duZXJOdWxsJywgdGhpcy5pc093bmVyTnVsbCkNCiAgICAgIGlmICh0aGlzLmlzT3duZXJOdWxsKSB7DQogICAgICAgIGNvbnN0IGlkeCA9IHRoaXMuY29sdW1ucy5maW5kSW5kZXgodiA9PiB2LkNvZGUgPT09ICdDb21wb25lbnRfQ29kZScpDQogICAgICAgIGlkeCAhPT0gLTEgJiYgdGhpcy5jb2x1bW5zLnNwbGljZShpZHgsIDEpDQogICAgICB9DQogICAgfSwNCiAgICBtZXJnZURhdGEobGlzdCkgew0KICAgICAgbGlzdA0KICAgICAgICAuZm9yRWFjaCgoZWxlbWVudCkgPT4gew0KICAgICAgICAgIGNvbnN0IGlkeCA9IHRoaXMuYmFja2VuZFRiLmZpbmRJbmRleCgNCiAgICAgICAgICAgIChpdGVtKSA9PiBlbGVtZW50LnB1dWlkICYmIGl0ZW0udXVpZCA9PT0gZWxlbWVudC5wdXVpZA0KICAgICAgICAgICkNCiAgICAgICAgICBpZiAoaWR4ICE9PSAtMSkgew0KICAgICAgICAgICAgdGhpcy50YkRhdGEuc3BsaWNlKGlkeCwgMCwgZGVlcENsb25lKHRoaXMuYmFja2VuZFRiW2lkeF0pKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCg0KICAgICAgdGhpcy50YkRhdGEuc29ydCgoYSwgYikgPT4gYS5pbml0Um93SW5kZXggLSBiLmluaXRSb3dJbmRleCkNCg0KICAgICAgdGhpcy5maWx0ZXJEYXRhKCkNCiAgICB9LA0KICAgIGhhbmRsZUNsb3NlKCkgew0KICAgICAgdGhpcy4kZW1pdCgnY2xvc2UnKQ0KICAgIH0sDQogICAgYWN0aXZlQ2VsbE1ldGhvZCh7IHJvdywgY29sdW1uLCBjb2x1bW5JbmRleCB9KSB7DQogICAgICByZXR1cm4gY29sdW1uLmZpZWxkID09PSAnY3VzdG9tQ291bnRDb2x1bW4nDQogICAgfSwNCiAgICBhc3luYyBnZXRUYWJsZUNvbmZpZyhjb2RlKSB7DQogICAgICBhd2FpdCBHZXRHcmlkQnlDb2RlKHsNCiAgICAgICAgY29kZQ0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGNvbnN0IHsgSXNTdWNjZWVkLCBEYXRhLCBNZXNzYWdlIH0gPSByZXMNCiAgICAgICAgaWYgKElzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMudGJDb25maWcgPSBPYmplY3QuYXNzaWduKHt9LCB0aGlzLnRiQ29uZmlnLCBEYXRhLkdyaWQpDQogICAgICAgICAgdGhpcy5wYWdlSW5mby5wYWdlU2l6ZSA9IE51bWJlcih0aGlzLnRiQ29uZmlnLlJvd19OdW1iZXIpDQogICAgICAgICAgY29uc3QgbGlzdCA9IERhdGEuQ29sdW1uTGlzdCB8fCBbXQ0KICAgICAgICAgIHRoaXMuY29sdW1ucyA9IGxpc3QuZmlsdGVyKHYgPT4gdi5Jc19EaXNwbGF5KS5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgICBpZiAoRklYX0NPTFVNTi5pbmNsdWRlcyhpdGVtLkNvZGUpKSB7DQogICAgICAgICAgICAgIGl0ZW0uZml4ZWQgPSAnbGVmdCcNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHJldHVybiBpdGVtDQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLmNvbHVtbnMucHVzaCh7DQogICAgICAgICAgICBEaXNwbGF5X05hbWU6ICfmjpLkuqfmlbDph48nLA0KICAgICAgICAgICAgQ29kZTogJ2N1c3RvbUNvdW50Q29sdW1uJw0KICAgICAgICAgIH0pDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiBNZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBnZXRPYmplY3RUeXBlTGlzdCgpIHsNCiAgICAgIEdldENvbXBUeXBlVHJlZSh7IHByb2Zlc3Npb25hbDogJ1N0ZWVsJyB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLk9iamVjdFR5cGVMaXN0LmRhdGEgPSByZXMuRGF0YQ0KICAgICAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgICAgICB0aGlzLiRyZWZzLnRyZWVTZWxlY3RPYmplY3RUeXBlLnRyZWVEYXRhVXBkYXRlRnVuKHJlcy5EYXRhKQ0KICAgICAgICAgIH0pDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICB0eXBlOiAnZXJyb3InLA0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgZ2V0VHlwZSgpIHsNCiAgICAgIEdldFBhcnRUeXBlTGlzdCh7IFBhcnRfR3JhZGU6IDAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMudHlwZU9wdGlvbiA9IHJlcy5EYXRhDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["addDraft.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "addDraft.vue", "sourceRoot": "src/views/PRO/plan-production/schedule-production/components", "sourcesContent": ["<template>\r\n  <div class=\"contentBox\">\r\n    <el-form ref=\"form\" :model=\"form\" label-width=\"90px\">\r\n      <el-row>\r\n        <template v-if=\"isCom\">\r\n          <el-col :span=\"10\">\r\n            <el-form-item label=\"构件编号\" prop=\"Comp_Codes\">\r\n              <el-input\r\n                v-model=\"form.Comp_Code\"\r\n                clearable\r\n                style=\"width: 45%\"\r\n                placeholder=\"请输入(空格区分/多个搜索)\"\r\n                type=\"text\"\r\n              />\r\n              <el-input\r\n                v-model=\"form.Comp_CodeBlur\"\r\n                clearable\r\n                style=\"width: 45%;margin-left: 16px\"\r\n                placeholder=\"模糊查找(请输入关键字)\"\r\n                type=\"text\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"构件类型\" prop=\"Type\">\r\n              <el-tree-select\r\n                ref=\"treeSelectObjectType\"\r\n                v-model=\"form.Type\"\r\n                style=\"width: 100%\"\r\n                class=\"cs-tree-x\"\r\n                :select-params=\"treeSelectParams\"\r\n                :tree-params=\"ObjectTypeList\"\r\n                value-key=\"Id\"\r\n              />\r\n              <!--              <el-select v-model=\"form.Type\" placeholder=\"请选择\" clearable @clear=\"filterData\">\r\n                <el-option label=\"全部\" value=\"\" />\r\n                <el-option\r\n                  v-for=\"item in comTypeOptions\"\r\n                  :key=\"item.value\"\r\n                  :label=\"item.label\"\r\n                  :value=\"item.value\"\r\n                />\r\n              </el-select>-->\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"5\">\r\n            <el-form-item label=\"规格\" prop=\"Spec\">\r\n              <el-input v-model.trim=\"form.Spec\" placeholder=\"请输入\" clearable />\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n        </template>\r\n        <template v-else>\r\n          <el-col :span=\"7\">\r\n            <el-form-item label=\"所属构件\" prop=\"Comp_Code\">\r\n              <el-input\r\n                v-model=\"form.Comp_Code\"\r\n                style=\"width: 45%;\"\r\n                placeholder=\"请输入(空格区分/多个搜索)\"\r\n                clearable\r\n              />\r\n              <el-input\r\n                v-model=\"form.Comp_CodeBlur\"\r\n                clearable\r\n                style=\"width: 45%;margin-left: 16px\"\r\n                placeholder=\"模糊查找(请输入关键字)\"\r\n                type=\"text\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"7\">\r\n            <el-form-item label=\"零件名称\" prop=\"Part_Code\">\r\n              <el-input\r\n                v-model=\"form.Part_Code\"\r\n                style=\"width: 45%;\"\r\n                placeholder=\"请输入(空格区分/多个搜索)\"\r\n                clearable\r\n              />\r\n              <el-input\r\n                v-model=\"form.Part_CodeBlur\"\r\n                clearable\r\n                style=\"width: 45%;margin-left: 16px\"\r\n                placeholder=\"模糊查找(请输入关键字)\"\r\n                type=\"text\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-form-item label=\"规格\" prop=\"Spec\">\r\n              <el-input\r\n                v-model.trim=\"form.Spec\"\r\n                placeholder=\"请输入\"\r\n                clearable\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-form-item label=\"零件种类\" prop=\"Type_Name\">\r\n              <el-select\r\n                v-model=\"form.Type_Name\"\r\n                placeholder=\"请选择\"\r\n                clearable\r\n              >\r\n                <el-option\r\n                  v-for=\"item in typeOption\"\r\n                  :key=\"item.Code\"\r\n                  :label=\"item.Name\"\r\n                  :value=\"item.Name\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </template>\r\n        <el-col :span=\"2\">\r\n          <el-button style=\"margin-left: 10px\" type=\"primary\" @click=\"handleSearch()\">查询</el-button>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n    <div class=\"tb-wrapper\">\r\n      <vxe-table\r\n        ref=\"xTable1\"\r\n        :empty-render=\"{name: 'NotData'}\"\r\n        show-header-overflow\r\n        empty-text=\"暂无数据\"\r\n        height=\"auto\"\r\n        show-overflow\r\n        :checkbox-config=\"{checkField: 'checked'}\"\r\n        :loading=\"tbLoading\"\r\n        :row-config=\"{isCurrent: true, isHover: true }\"\r\n        class=\"cs-vxe-table\"\r\n        align=\"left\"\r\n        stripe\r\n        :data=\"fTable\"\r\n        resizable\r\n        :edit-config=\"{trigger: 'click', mode: 'cell', activeMethod: activeCellMethod}\"\r\n        :tooltip-config=\"{ enterable: true }\"\r\n        @checkbox-all=\"tbSelectChange\"\r\n        @checkbox-change=\"tbSelectChange\"\r\n      >\r\n        <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n        <template v-for=\"item in columns\">\r\n          <vxe-column\r\n            v-if=\"item.Code === 'customCountColumn'\"\r\n            :key=\"item.Code\"\r\n            :align=\"item.Align\"\r\n            :field=\"item.Code\"\r\n            :title=\"item.Display_Name\"\r\n            sortable\r\n            :edit-render=\"{}\"\r\n            min-width=\"120\"\r\n          >\r\n            <template #edit=\"{ row }\">\r\n              <vxe-input\r\n                v-model.number=\"row.count\"\r\n                type=\"integer\"\r\n                :min=\"1\"\r\n                :max=\"row.maxCount\"\r\n              />\r\n            </template>\r\n            <template #default=\"{ row }\">\r\n              {{ row.count | displayValue }}\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column\r\n            v-else-if=\"item.Code === 'Is_Component'\"\r\n            :key=\"item.Code\"\r\n            :align=\"item.Align\"\r\n            :field=\"item.Code\"\r\n            :title=\"item.Display_Name\"\r\n            sortable\r\n            :min-width=\"item.Width\"\r\n          >\r\n            <template #default=\"{ row }\">\r\n              <el-tag :type=\"row.Is_Component ? 'danger' : 'success'\">{{\r\n                row.Is_Component ? \"否\" : \"是\"\r\n              }}</el-tag>\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column\r\n            v-else\r\n            :key=\"item.Code\"\r\n            :align=\"item.Align\"\r\n            :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n            show-overflow=\"tooltip\"\r\n            sortable\r\n            :field=\"item.Code\"\r\n            :title=\"item.Display_Name\"\r\n            :min-width=\"item.Width\"\r\n          />\r\n        </template>\r\n      </vxe-table>\r\n    </div>\r\n    <div class=\"data-info\">\r\n      <el-tag\r\n        size=\"medium\"\r\n        class=\"info-x\"\r\n      >已选 {{ totalSelection.length }} 条数据\r\n      </el-tag>\r\n      <vxe-pager\r\n        border\r\n        background\r\n        :loading=\"tbLoading\"\r\n        :current-page.sync=\"pageInfo.page\"\r\n        :page-size.sync=\"pageInfo.pageSize\"\r\n        :page-sizes=\"pageInfo.pageSizes\"\r\n        :total=\"pageInfo.total\"\r\n        :layouts=\"['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']\"\r\n        size=\"small\"\r\n        @page-change=\"handlePageChange\"\r\n      />\r\n    </div>\r\n    <div class=\"button\">\r\n      <el-button @click=\"handleClose\">取消</el-button>\r\n      <el-button\r\n        type=\"primary\"\r\n        :disabled=\"!totalSelection.length\"\r\n        :loading=\"saveLoading\"\r\n        @click=\"handleSave\"\r\n      >保存</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { GetCanSchdulingComps } from '@/api/PRO/production-task'\r\nimport { GetPartList } from '@/api/PRO/production-part'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { FIX_COLUMN } from '@/views/PRO/plan-production/schedule-production/constant'\r\nimport { debounce, deepClone } from '@/utils'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport { GetCompTypeTree } from '@/api/PRO/professionalType'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\n\r\nexport default {\r\n  props: {\r\n    scheduleId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    pageType: {\r\n      type: String,\r\n      default: 'com'\r\n    },\r\n    showDialog: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      pageInfo: {\r\n        page: 1,\r\n        pageSize: 500,\r\n        pageSizes: tablePageSize,\r\n        total: 0\r\n      },\r\n      form: {\r\n        Comp_Code: '',\r\n        Comp_CodeBlur: '',\r\n        Part_CodeBlur: '',\r\n        Part_Code: '',\r\n        Type_Name: '',\r\n        Spec: '',\r\n        Type: ''\r\n      },\r\n      isOwnerNull: true,\r\n      tbLoading: false,\r\n      saveLoading: false,\r\n      columns: [],\r\n      fTable: [],\r\n      tbConfig: {},\r\n      TotalCount: 0,\r\n      Page: 0,\r\n      multipleSelection: [],\r\n      totalSelection: [],\r\n      search: () => ({}),\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        clearable: true\r\n      },\r\n      ObjectTypeList: {\r\n        // 构件类型\r\n        'check-strictly': true,\r\n        'default-expand-all': true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      },\r\n      typeOption: []\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    }\r\n  },\r\n  watch: {\r\n    showDialog(newValue) {\r\n      newValue && (this.saveLoading = false)\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getConfig()\r\n    if (this.isCom) {\r\n      this.getObjectTypeList()\r\n    } else {\r\n      this.getType()\r\n    }\r\n    this.search = debounce(this.fetchData, 800, true)\r\n  },\r\n  methods: {\r\n    async getConfig() {\r\n      let code = ''\r\n      code = this.isCom\r\n        ? 'PROComDraftEditTbConfig'\r\n        : 'PROPartDraftEditTbConfig'\r\n      await this.getTableConfig(code)\r\n      this.fetchData()\r\n    },\r\n    filterData(page) {\r\n      const f = []\r\n      for (const formKey in this.form) {\r\n        if (this.form[formKey] || this.form[formKey] === false) {\r\n          f.push(formKey)\r\n        }\r\n      }\r\n      if (!f.length) {\r\n        this.setPage()\r\n        !page && (this.pageInfo.page = 1)\r\n        this.pageInfo.total = this.tbData.length\r\n        return\r\n      }\r\n      const temTbData = this.tbData.filter(v => {\r\n        v.checked = false\r\n        if (this.form.Comp_Code.trim() && !this.form['Comp_Code'].split(' ').includes(v['Comp_Code'])) {\r\n          return false\r\n        }\r\n        if (this.form.Comp_CodeBlur.trim() && !v.Comp_Code.includes(this.form.Comp_CodeBlur)) {\r\n          return false\r\n        }\r\n        if (this.form.Type && v.Type !== this.form.Type) {\r\n          return false\r\n        }\r\n        if (this.form.Part_CodeBlur.trim() && !v.Part_Code.includes(this.form.Part_CodeBlur)) {\r\n          return false\r\n        }\r\n        if (this.form.Part_Code.trim() && !this.form['Part_Code'].split(' ').includes(v['Part_Code'])) {\r\n          return false\r\n        }\r\n        if (this.form.Type_Name !== '' && v.Type_Name !== this.form.Type_Name) {\r\n          return false\r\n        }\r\n        if (this.form.Spec.trim() !== '' && !v.Spec.includes(this.form.Spec)) {\r\n          return false\r\n        }\r\n        return true\r\n      })\r\n\r\n      console.log('page', page)\r\n      !page && (this.pageInfo.page = 1)\r\n      this.pageInfo.total = temTbData.length\r\n      this.setPage(temTbData)\r\n    },\r\n    handleSearch() {\r\n      this.totalSelection = []\r\n      this.clearSelect()\r\n      if (this.tbData?.length) {\r\n        this.tbData.forEach(item => item.checked = false)\r\n        this.filterData()\r\n      }\r\n    },\r\n    handleSelect(data) {\r\n      this.multipleSelection = data\r\n    },\r\n    tbSelectChange(array) {\r\n      console.log('array', array)\r\n      this.totalSelection = this.tbData.filter(v => v.checked)\r\n    },\r\n    clearSelect() {\r\n      this.$refs.xTable1.clearCheckboxRow()\r\n      this.totalSelection = []\r\n    },\r\n    async fetchData() {\r\n      this.tbLoading = true\r\n      if (this.isCom) {\r\n        await this.getComTbData()\r\n      } else {\r\n        await this.getPartTbData()\r\n      }\r\n      this.initTbData()\r\n      this.filterData()\r\n      this.tbLoading = false\r\n    },\r\n    setPageData() {\r\n      if (this.tbData?.length) {\r\n        this.pageInfo.page = 1\r\n        this.tbData = this.tbData.filter(v => v.Can_Schduling_Count > 0)\r\n        this.filterData()\r\n      }\r\n    },\r\n    handleSave() {\r\n      this.saveLoading = true\r\n      setTimeout(() => {\r\n        this.totalSelection.forEach((item) => {\r\n          const intCount = parseInt(item.count)\r\n          item.Schduled_Count += intCount\r\n          item.Can_Schduling_Count -= intCount\r\n          item.Can_Schduling_Weight = item.Can_Schduling_Count * item.Weight\r\n          item.maxCount = item.Can_Schduling_Count\r\n          item.chooseCount = intCount\r\n          item.count = item.Can_Schduling_Count\r\n          item.checked = false\r\n        })\r\n        const cp = deepClone(this.totalSelection)\r\n\r\n        this.$emit('sendSelectList', cp)\r\n        this.$emit('close')\r\n        this.clearSelect()\r\n        this.setPage()\r\n      }, 0)\r\n    },\r\n    initTbData() {\r\n      if (!this.tbData?.length) {\r\n        this.tbData = []\r\n        this.backendTb = []\r\n        return\r\n      }\r\n      // 设置文本框选择的排产数量,设置自定义唯一码\r\n      const objKey = {}\r\n      this.tbData.forEach((item) => {\r\n        this.$set(item, 'count', item.Can_Schduling_Count)\r\n        this.$set(item, 'maxCount', item.Can_Schduling_Count)\r\n        item.uuid = uuidv4()\r\n        objKey[item.Type] = true\r\n      })\r\n      this.backendTb = deepClone(this.tbData)\r\n    },\r\n    async getComTbData() {\r\n      const { install, areaId } = this.$route.query\r\n      const { Comp_Codes, ...obj } = this.form\r\n      let codes = []\r\n      if (Object.prototype.toString.call(Comp_Codes) === '[object String]') {\r\n        codes = Comp_Codes && Comp_Codes.split(' ').filter(v => !!v)\r\n      }\r\n      await GetCanSchdulingComps({\r\n        ...obj,\r\n        Schduling_Plan_Id: this.scheduleId,\r\n        Comp_Codes: codes,\r\n        InstallUnit_Id: install,\r\n        Area_Id: areaId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.pageInfo.total = res.Data.length\r\n          this.tbData = res.Data.map((v, idx) => {\r\n            // 已排产赋值\r\n            v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n            v.Workshop_Id = v.Scheduled_Workshop_Id\r\n            v.Workshop_Name = v.Scheduled_Workshop_Name\r\n            v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n            if (v.originalPath) {\r\n              v.isDisabled = true\r\n            }\r\n            v.checked = false\r\n            v.initRowIndex = idx\r\n            return v\r\n          })\r\n          this.setPage()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 分页\r\n     */\r\n    handlePageChange({ currentPage, pageSize }) {\r\n      console.log(' currentPage, pageSize', currentPage, pageSize)\r\n      if (this.tbLoading) return\r\n      this.pageInfo.page = currentPage\r\n      this.pageInfo.pageSize = pageSize\r\n      this.setPage()\r\n      this.filterData(currentPage)\r\n    },\r\n\r\n    setPage(tb = this.tbData) {\r\n      this.fTable = tb.slice((this.pageInfo.page - 1) * this.pageInfo.pageSize, this.pageInfo.page * this.pageInfo.pageSize)\r\n    },\r\n\r\n    async getPartTbData() {\r\n      const { install, areaId } = this.$route.query\r\n      await GetPartList({\r\n        ...this.form,\r\n        Schduling_Plan_Id: this.scheduleId,\r\n        InstallUnit_Id: install,\r\n        Area_Id: areaId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.pageInfo.total = res.Data.length\r\n          this.tbData = res.Data.map((v, idx) => {\r\n            if (v.Component_Technology_Path) {\r\n              const list = v.Component_Technology_Path.split('/')\r\n              if (list.length && list.some(x => x === v.Part_Used_Process)) {\r\n                v.originalUsedProcess = v.Part_Used_Process\r\n              } else {\r\n                v.originalUsedProcess = ''\r\n                v.Part_Used_Process = ''\r\n              }\r\n            }\r\n            v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n            v.Workshop_Id = v.Scheduled_Workshop_Id\r\n            v.Workshop_Name = v.Scheduled_Workshop_Name\r\n            v.Part_Used_Process = v.Scheduled_Used_Process || v.Part_Used_Process// 是否存在已使用的领用工序\r\n            v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n            v.isDisabled = !!v.originalPath\r\n            v.checked = false\r\n            v.initRowIndex = idx\r\n            return v\r\n          })\r\n          this.setPartColumn()\r\n          this.setPage()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setPartColumn() {\r\n      // 纯零件\r\n      this.isOwnerNull = this.tbData.every(v => !v.Comp_Import_Detail_Id)\r\n      console.log('this.isOwnerNull', this.isOwnerNull)\r\n      if (this.isOwnerNull) {\r\n        const idx = this.columns.findIndex(v => v.Code === 'Component_Code')\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n      }\r\n    },\r\n    mergeData(list) {\r\n      list\r\n        .forEach((element) => {\r\n          const idx = this.backendTb.findIndex(\r\n            (item) => element.puuid && item.uuid === element.puuid\r\n          )\r\n          if (idx !== -1) {\r\n            this.tbData.splice(idx, 0, deepClone(this.backendTb[idx]))\r\n          }\r\n        })\r\n\r\n      this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n\r\n      this.filterData()\r\n    },\r\n    handleClose() {\r\n      this.$emit('close')\r\n    },\r\n    activeCellMethod({ row, column, columnIndex }) {\r\n      return column.field === 'customCountColumn'\r\n    },\r\n    async getTableConfig(code) {\r\n      await GetGridByCode({\r\n        code\r\n      }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n          this.pageInfo.pageSize = Number(this.tbConfig.Row_Number)\r\n          const list = Data.ColumnList || []\r\n          this.columns = list.filter(v => v.Is_Display).map(item => {\r\n            if (FIX_COLUMN.includes(item.Code)) {\r\n              item.fixed = 'left'\r\n            }\r\n            return item\r\n          })\r\n          this.columns.push({\r\n            Display_Name: '排产数量',\r\n            Code: 'customCountColumn'\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getObjectTypeList() {\r\n      GetCompTypeTree({ professional: 'Steel' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.ObjectTypeList.data = res.Data\r\n          this.$nextTick((_) => {\r\n            this.$refs.treeSelectObjectType.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getType() {\r\n      GetPartTypeList({ Part_Grade: 0 }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.typeOption = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.contentBox {\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .button {\r\n    margin-top: 16px;\r\n    display: flex;\r\n    justify-content: end;\r\n  }\r\n\r\n  .tb-wrapper {\r\n    flex: 1 1 auto;\r\n    height: 50vh;\r\n  }\r\n\r\n  .data-info{\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-top: 16px;\r\n  }\r\n}\r\n</style>\r\n"]}]}
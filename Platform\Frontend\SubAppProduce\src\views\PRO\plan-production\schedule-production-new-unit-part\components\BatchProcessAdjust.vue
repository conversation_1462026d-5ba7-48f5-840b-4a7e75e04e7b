<template>
  <div v-loading="pgLoading" class="cs-container">
    <el-form ref="form" :model="form" label-width="100px">
      <el-form-item label="工艺代码">
        <el-select v-model="craftCode" filterable placeholder="下拉选择支持搜索" clearable="" @change="craftChange">
          <el-option
            v-for="item in gyList"
            :key="item.Code"
            :label="item.Code"
            :value="item.Code"
          />
        </el-select>
      </el-form-item>
      <el-divider />
      <draggable v-model="list" handle=".icon-drag" @change="changeDraggable">
        <transition-group>
          <el-row v-for="(element,index) in list" :key="element.key">
            <el-col :span="1"> <i class="iconfont icon-drag cs-drag" /> </el-col>
            <el-col :span="10">
              <el-form-item :label="`排产工序${index+1}`">
                <el-select :key="element.key" v-model="element.value" style="width:90%" :disabled="element.isPart" placeholder="请选择" clearable @change="selectChange($event,element)">
                  <el-option
                    v-for="item in options"
                    :key="item.Code"
                    :label="item.Name"
                    :disabled="item.disabled"
                    :value="item.Code"
                  >
                    <div class="cs-option">
                      <span class="cs-label">{{ item.Name }}</span>
                      <span v-if="item.Is_Nest && isNest" class="cs-tip">(套)</span>
                    </div>
                  </el-option>
                </el-select>

              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="班组" label-width="60px">
                <el-select v-model="element.Working_Team_Id" clearable placeholder="请选择">
                  <el-option v-for="item in getWorkingTeam(element.Teams,element)" :key="item.Id" :label="item.Name" :value="item.Id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <span class="btn-x">
                <el-button v-if="index===0 && list.length<options.length" type="primary" icon="el-icon-plus" circle @click="handleAdd" />
                <el-button v-if="index!==0&&!element.isPart" type="danger" icon="el-icon-delete" circle @click="handleDelete(element)" />
              </span>
            </el-col>

          </el-row>
        </transition-group>
      </draggable>
    </el-form>
    <div class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button v-if="list.length" type="primary" :loading="btnLoading" @click="submit">确 定</el-button>
    </div>
  </div>
</template>

<script>
import { GetProcessListBase, GetLibList } from '@/api/PRO/technology-lib'
import Draggable from 'vuedraggable'
import { uniqueArr, deepClone } from '@/utils'
import { mapActions } from 'vuex'
import { v4 as uuidv4 } from 'uuid'
export default {
  components: {
    Draggable
  },
  props: {
    pageType: {
      type: String,
      default: undefined
    },
    isNest: {
      type: Boolean,
      default: false
    },
    processList: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      list: [],
      options: [],
      // defaultOptions: [],
      btnLoading: false,
      pgLoading: false,
      gyList: [],
      craftCode: '',
      form: {}
    }
  },
  computed: {
    isCom() {
      return this.pageType === 'com'
    }
  },
  watch: {
    list: {
      handler(newVal) {
        if (!this.craftCode) return
        const workCode = this.gyList.find(v => v.Code === this.craftCode)?.WorkCode
        const newCode = newVal.map(v => v.value).filter(v => !!v).join('/')
        if (workCode !== newCode) {
          this.craftCode = ''
        }
      },
      deep: true
    }
  },
  methods: {
    ...mapActions('schedule', ['initProcessList']),
    getCraftProcess() {
      return new Promise((resolve, reject) => {
        GetLibList({
          Id: '',
          Type: 3
        }).then(res => {
          if (res.IsSucceed) {
            this.gyList = (res.Data || [])
            resolve()
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
            reject()
          }
        })
      })
    },
    getProcessOption(workshopId) {
      return new Promise((resolve, reject) => {
        this.pgLoading = true
        GetProcessListBase({
          workshopId: workshopId,
          type: 3
        }).then(res => {
          if (res.IsSucceed) {
            this.options = res.Data.map(v => {
              this.$set(v, 'disabled', false)
              return v
            })
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
          resolve()
        }).finally(_ => {
          this.pgLoading = false
        })
      })
    },
    selectChange(val, element) {
      console.log('val', val)
      console.log('element', element)
      const arr = this.list.map(i => i.value)
      this.options.forEach((item, index) => {
        item.disabled = arr.includes(item.Code)
        if (item.Code === val) {
          element.Teams = item.Teams
        }
      })
      // if (element) {
      //   element.date = this.processList[val]?.Finish_Date
      //   if (!val) {
      //     element.Working_Team_Id = ''
      //     element.Teams = []
      //   }
      // }
      if (element) {
        if (val) {
          element.Working_Team_Id = this.processList[val]?.Working_Team_Id
        } else {
          element.Working_Team_Id = ''
          element.Teams = []
        }
      }
      if (!element?.Working_Team_Id && element?.Teams?.length === 1) {
        element.Working_Team_Id = element.Teams[0].Id
      }
    },
    dateChange(val, element) {
      const item = this.options.find(v => v.Code === element.value)
      console.log('item', item, this.list)
      let obj = {}
      if (item) {
        obj = {
          Schduling_Id: this.formInline?.Schduling_Code,
          Process_Id: item.Id,
          Process_Code: item.Code,
          Finish_Date: val
        }
      }
      // this.$emit('setProcessList', { key: element.value, value: obj })
    },
    handleAdd(item) {
      const arr = this.list.map(v => v.value)
      this.options.forEach(v => {
        if (arr.includes(v.Code)) {
          v.disabled = true
        }
      })
      this.list.push({
        key: uuidv4(),
        value: '',
        Working_Team_Id: '',
        Teams: [],
        date: ''
      })
    },
    handleDelete(element) {
      const idx = this.list.findIndex(v => v.value === element.value)
      if (idx !== -1) {
        this.list.splice(idx, 1)
        this.selectChange()
      }
    },
    getWorkingTeam(teams, curItem) {
      const newTeams = teams.filter(v => {
        if (this.workshopId) {
          return v.Workshop_Id === this.workshopId
        }
        return true
      })
      if (!newTeams.length) {
        curItem.Working_Team_Id = ''
        return []
      }
      if (newTeams.every(v => v.Id !== curItem.Working_Team_Id)) {
        curItem.Working_Team_Id = ''
      }
      return newTeams
    },
    async setData(arr, technologyStr) {
      console.log('arr', JSON.parse(JSON.stringify(arr)))
      console.log('technologyStr', technologyStr)
      console.log('processList', this.processList)
      await this.getCraftProcess()
      let technologyArr = []
      if (technologyStr) {
        technologyArr = technologyStr.split('/')
      }
      const workshopId = arr[0].Workshop_Id
      this.workshopId = workshopId

      const partUsedProcess = arr[0].Unit_Part_Used_Process
      await this.getProcessOption(workshopId)

      this.options = this.options.filter(item => {
        let flag = false
        if (technologyArr.length && technologyArr.includes(item.Code)) {
          flag = true
        }
        if (partUsedProcess && partUsedProcess === item.Code) {
          flag = true
        }
        if (!flag) {
          flag = !!item.Is_Enable
        }
        return flag
      })
      // this.defaultOptions = deepClone(this.options)
      this.arr = arr || null
      this.list = []
      let codes = []

      const origin = arr.map(v => (v?.Unit_Part_Used_Process || '').split(','))
      codes = this.getUnique(origin.flat()).filter(v => !!v)

      if (codes.length) {
        const checkOption = codes.filter(c => {
          return !!this.options.find(k => k.Code === c)
        })
        console.log(codes, checkOption, this.options)
        if (checkOption.length < codes.length) {
          this.$message({
            message: '当前部件生产所属车间内没有该部件所属零件领用工序，请至车间管理内关联相关工序班组',
            type: 'warning'
          })
          return
        }

        codes.forEach((value, idx) => {
          const obj = {
            value,
            isPart: true,
            key: uuidv4(),
            Working_Team_Id: this.processList[value]?.Working_Team_Id,
            Teams: this.options.find(item => item.Code === value)?.Teams || [],
            date: this.processList[value]?.Finish_Date
          }
          if (obj.Teams.length === 1 && !obj.Working_Team_Id) {
            obj.Working_Team_Id = obj.Teams[0].Id
          }
          this.list.push(obj)
        })
      }

      if (technologyArr.length) {
        const techArr = technologyArr.map(v => {
          const obj = {
            key: uuidv4(),
            value: v,
            Working_Team_Id: this.processList[v]?.Working_Team_Id,
            Teams: this.options.find(item => item.Code === v)?.Teams || [],
            date: this.processList[v]?.Finish_Date
          }
          if (obj.Teams.length === 1 && !obj.Working_Team_Id) {
            obj.Working_Team_Id = obj.Teams[0].Id
          }
          return obj
        })
        console.log('techArr', techArr)
        techArr.forEach((element, idx) => {
          if (!codes.includes(element.value)) {
            this.list.push(element)
          }
        })
      }
      if (!this.list.length) {
        this.list.push({
          value: '',
          key: uuidv4(),
          Working_Team_Id: '',
          Teams: [],
          date: ''
        })
        if (this.isNest) {
          const xur = this.options.filter(item => item.Is_Nest)
          if (xur.length === 1) {
            this.list[0].value = xur[0].Code
          }
        }
      }
      const indexMap = technologyArr.reduce((map, item, index) => {
        map[item] = index
        return map
      }, {})

      this.list.sort((item1, item2) => {
        return indexMap[item1.value] - indexMap[item2.value]
      })

      this.selectChange()
    },
    getUnique(arr) {
      return uniqueArr(arr)
    },
    craftChange(val) {
      this.craftCode = val
      if (!val) {
        // this.options = this.defaultOptions
        return
      }
      const info = this.gyList.find(v => v.Code === val)
      if (info) {
        const plist = info.WorkCode.split('/')
        // this.options = this.defaultOptions.filter(v => plist.includes(v.Code))
        const newList = []
        plist.forEach((listVal, idx) => {
          const item = this.list.find(v => v.value === listVal)
          if (item) {
            if (item.Teams.length === 1 && !item.Working_Team_Id) {
              item.Working_Team_Id = item.Teams[0].Id
            }
            newList.push(item)
          } else {
            const item2 = this.options.find(v => v.Code === listVal)
            if (item2) {
              const obj = {
                key: uuidv4(),
                value: item2.Code,
                Working_Team_Id: '',
                Teams: item2.Teams,
                date: ''
              }
              if (item2.Teams.length === 1 && !obj.Working_Team_Id) {
                obj.Working_Team_Id = item2.Teams[0].Id
              }
              newList.push(obj)
            }
          }
        })
        this.list = newList
      }
    },
    submit() {
      const list = this.list.map(item => item.value).filter(k => !!k)
      const isTrue = this.checkCode(list)
      if (!isTrue) {
        this.$message({
          message: '相邻工序不能相同',
          type: 'warning'
        })
        return
      }
      if (!list.length) {
        this.$message({
          message: '工序不能全为空',
          type: 'warning'
        })
        return
      }

      if (this.isNest) {
        const xur = this.options.filter(item => item.Is_Nest)
        if (xur.length) {
          const hasNest = xur.some(obj => list.includes(obj.Code))
          if (!hasNest) {
            this.$message({
              message: '请至少选择一个套料工序！',
              type: 'warning'
            })
            return
          }
        }
      }

      this.btnLoading = true
      const str = list.join('/')
      this.list.forEach((element, idx) => {
        const item = this.options.find(v => v.Code === element.value)

        let obj = {}
        if (item) {
          obj = {
            Schduling_Id: this.formInline?.Schduling_Code,
            Process_Id: item.Id,
            Process_Code: item.Code,
            Finish_Date: element.date,
            Working_Team_Id: element.Working_Team_Id
          }
        }
        this.$emit('setProcessList', { key: element.value, value: obj })
      })

      this.$emit('sendProcess', { arr: this.arr, str })
      this.btnLoading = false
      this.handleClose()
    },
    handleClose() {
      this.$emit('close')
    },
    checkCode(list) {
      let flag = true
      for (let i = 0; i < list.length; i++) {
        if (i !== list.length - 1 && list[i] === list[i + 1]) {
          flag = false
          break
        }
      }
      return flag
    },
    changeDraggable() {
      this.list.forEach(v => {
        this.$set(v, 'date', '')
      })
      this.initProcessList()
    }
  }
}
</script>

<style scoped>
.btn-x {
  margin-left: 20px;
}

.dialog-footer {
  text-align: right;
  margin-top: 30px;
}

.cs-drag {
  line-height: 32px;
  cursor: move;
}

.cs-option {
  display: flex;
  justify-content: space-between;

  .cs-label {

  }

  .cs-tip {
    color: #409EFF;
  }
}

</style>

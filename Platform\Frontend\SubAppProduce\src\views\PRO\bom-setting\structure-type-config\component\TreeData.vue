<template>
  <div class="tree-container">
    <div class="title">
      <el-radio-group v-model="activeType" @change="changeType">
        <el-radio-button label="comp">构件大类</el-radio-button>
        <el-radio-button label="part">零件大类</el-radio-button>
      </el-radio-group>
      <div class="btn-x">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button>
      </div>
    </div>

    <div class="tree-wrapper">
      <el-tree
        ref="tree"
        v-loading="loading"
        :current-node-key="currentNodeKey"
        element-loading-text="加载中"
        element-loading-spinner="el-icon-loading"
        empty-text="暂无数据"
        highlight-current
        node-key="Id"
        default-expand-all
        :expand-on-click-node="false"
        :data="treeData"
        :props="{
          label:'Label',
          children:'Children'
        }"
        @node-click="handleNodeClick"
      >
        <span slot-scope="{ node, data }" class="custom-tree-node">
          <svg-icon
            :icon-class="
              node.expanded ? 'icon-folder-open' : 'icon-folder'
            "
            class-name="class-icon"
          />
          <span class="cs-label" :title="node.label">{{ node.label }}</span>
        </span>
      </el-tree>
    </div>
  </div>
</template>

<script>
import { GetCompTypeTree } from '@/api/PRO/component-type'
import { GetPartTypeTree } from '@/api/PRO/partType'
import { GetBOMInfo } from '@/views/PRO/bom-setting/utils'
export default {
  props: {
    typeCode: {
      type: String,
      default: ''
    },
    typeId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      treeData: [],
      loading: true,
      currentNodeKey: '',
      activeType: 'comp'
    }
  },
  watch: {
    currentNodeKey(newValue) {
      this.$emit('showRight', newValue !== '')
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    changeType() {
      this.$emit('changeType', this.activeType)
      this.currentNodeKey = ''
      this.fetchData()
    },
    async fetchData() {
      this.loading = true
      let res
      if (this.activeType === 'comp') {
        res = await GetCompTypeTree({ professional: this.typeCode })
      } else {
        res = await GetPartTypeTree({ professionalId: this.typeId })
      }
      if (res.IsSucceed) {
        this.treeData = res.Data
      } else {
        this.$message({
          message: res.Message,
          type: 'error'
        })
        this.treeData = []
      }
      this.loading = false
      this.currentNodeKey && this.setTreeNode()
    },
    setTreeNode() {
      this.$emit('nodeClick', this.$refs['tree'].getNode(this.currentNodeKey))
      this.$nextTick(_ => {
        this.$refs['tree'].setCurrentKey(this.currentNodeKey)
      })
    },
    handleAdd() {
      this.$emit('AddFirst')
    },
    handleNodeClick(data, node) {
      console.log(data, node)
      this.currentNodeKey = data.Id
      this.$emit('nodeClick', node)
    },
    resetKey(id) {
      console.log(id, this.currentNodeKey, id === this.currentNodeKey, 'dsd')
      if (id === this.currentNodeKey) {
        this.currentNodeKey = ''
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/styles/mixin.scss';

.tree-container{
  margin-right: 16px;
  flex-basis: 18%;
  background-color: #FFFFFF;
  display: flex;
  flex-direction: column;
  .title{
    border-bottom: 1px solid #EEEEEE;
    font-weight: 500;
    padding: 16px;
    color: #333333;
    display: flex;
    justify-content: space-between;
    .title-name {
      height: 30px;
      line-height: 30px;
    }
    .btn-x{
      padding: 0 0;
      text-align: center;
    }
  }

  .tree-wrapper{
    padding: 16px;
    flex: 1;
    overflow: hidden;
    .el-tree{
      @include scrollBar;
      height: 100%;
      overflow: auto;
    }
    .cs-label{
      font-size: 14px;
      margin-left: 4px;
    }
  }
}
</style>

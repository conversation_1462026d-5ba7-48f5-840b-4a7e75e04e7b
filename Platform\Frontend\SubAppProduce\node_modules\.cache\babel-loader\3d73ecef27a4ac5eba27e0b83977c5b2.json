{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\TypeDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\TypeDialog.vue", "mtime": 1757921826136}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["AddCheckType", "EntityCheckType", "SaveCheckType", "data", "Bom_Level", "check_object_id", "form", "rules", "Name", "required", "message", "trigger", "title", "editInfo", "mounted", "methods", "init", "checkType", "Check_Object_Id", "Id", "Code", "console", "log", "getEntityCheckType", "addCheckType", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "then", "res", "IsSucceed", "$message", "type", "$emit", "dialogData", "Message", "stop", "_this2", "id", "Data", "editCheckType", "_this3", "_objectSpread", "handleSubmit", "_this4", "$refs", "validate", "valid"], "sources": ["src/views/PRO/factoryQuality/checkoutGroup/components/Dialog/TypeDialog.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"80px\">\n      <el-row>\n        <el-col :span=\"24\">\n          <el-form-item label=\"检查类型\" prop=\"Name\">\n            <el-input v-model=\"form.Name\" />\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item style=\"text-align: right\">\n            <el-button @click=\"$emit('close')\">关 闭</el-button>\n            <el-button\n              type=\"primary\"\n              @click=\"handleSubmit('form')\"\n            >确 定</el-button>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n  </div>\n</template>\n\n<script>\nimport { AddCheckType } from '@/api/PRO/factorycheck'\nimport { EntityCheckType } from '@/api/PRO/factorycheck'\nimport { SaveCheckType } from '@/api/PRO/factorycheck'\nexport default {\n  // props: {\n  //   dialogData: {}\n  // },\n  data() {\n    return {\n      Bom_Level: '',\n      check_object_id: '',\n      form: {},\n      rules: {\n        Name: [{ required: true, message: '请填写完整表单', trigger: 'blur' }]\n      },\n      title: '',\n      editInfo: {}\n    }\n  },\n  // watch: {\n  //   dialogData: {\n  //     handler(newName, oldName) {\n  //       console.log(\"newName\",newName)\n  //       if(newName) {\n  //         this.form = Object.assign({},newName);\n  //       }\n  //     },\n  //     deep: true,\n  //     immediate: true\n  //   },\n  // },\n  mounted() {},\n  methods: {\n    init(title, checkType, data) {\n      this.title = title\n      this.Check_Object_Id = checkType.Id\n      this.Bom_Level = checkType.Code\n      if (title === '编辑') {\n        this.editInfo = data\n        console.log(this.editInfo)\n        this.getEntityCheckType(data)\n      }\n    },\n    async addCheckType() {\n      await AddCheckType({\n        Name: this.form.Name,\n        Check_Object_Id: this.Check_Object_Id,\n        Bom_Level: this.Bom_Level\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            type: 'success',\n            message: '保存成功'\n          })\n          this.$emit('refresh')\n          this.$emit('close')\n          this.dialogData = {}\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    getEntityCheckType(data) {\n      EntityCheckType({ id: data.Id }).then((res) => {\n        if (res.IsSucceed) {\n          this.form = res.Data[0]\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    editCheckType() {\n      SaveCheckType({\n        Id: this.editInfo.Id,\n        ...this.form,\n        Check_Object_Id: this.Check_Object_Id,\n        Bom_Level: this.Bom_Level\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            type: 'success',\n            message: '编辑成功'\n          })\n          this.$emit('refresh')\n          this.$emit('close')\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    handleSubmit(form) {\n      this.$refs[form].validate((valid) => {\n        if (valid) {\n          this.title === '新增' ? this.addCheckType() : this.editCheckType()\n        } else {\n          return false\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped></style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,SAAAA,YAAA;AACA,SAAAC,eAAA;AACA,SAAAC,aAAA;AACA;EACA;EACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,eAAA;MACAC,IAAA;MACAC,KAAA;QACAC,IAAA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAC,KAAA;MACAC,QAAA;IACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,OAAA,WAAAA,QAAA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAAJ,KAAA,EAAAK,SAAA,EAAAd,IAAA;MACA,KAAAS,KAAA,GAAAA,KAAA;MACA,KAAAM,eAAA,GAAAD,SAAA,CAAAE,EAAA;MACA,KAAAf,SAAA,GAAAa,SAAA,CAAAG,IAAA;MACA,IAAAR,KAAA;QACA,KAAAC,QAAA,GAAAV,IAAA;QACAkB,OAAA,CAAAC,GAAA,MAAAT,QAAA;QACA,KAAAU,kBAAA,CAAApB,IAAA;MACA;IACA;IACAqB,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACAlC,YAAA;gBACAQ,IAAA,EAAAiB,KAAA,CAAAnB,IAAA,CAAAE,IAAA;gBACAU,eAAA,EAAAO,KAAA,CAAAP,eAAA;gBACAd,SAAA,EAAAqB,KAAA,CAAArB;cACA,GAAA+B,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAZ,KAAA,CAAAa,QAAA;oBACAC,IAAA;oBACA7B,OAAA;kBACA;kBACAe,KAAA,CAAAe,KAAA;kBACAf,KAAA,CAAAe,KAAA;kBACAf,KAAA,CAAAgB,UAAA;gBACA;kBACAhB,KAAA,CAAAa,QAAA;oBACAC,IAAA;oBACA7B,OAAA,EAAA0B,GAAA,CAAAM;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAV,QAAA,CAAAW,IAAA;UAAA;QAAA,GAAAd,OAAA;MAAA;IACA;IACAN,kBAAA,WAAAA,mBAAApB,IAAA;MAAA,IAAAyC,MAAA;MACA3C,eAAA;QAAA4C,EAAA,EAAA1C,IAAA,CAAAgB;MAAA,GAAAgB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAO,MAAA,CAAAtC,IAAA,GAAA8B,GAAA,CAAAU,IAAA;QACA;UACAF,MAAA,CAAAN,QAAA;YACAC,IAAA;YACA7B,OAAA,EAAA0B,GAAA,CAAAM;UACA;QACA;MACA;IACA;IACAK,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA9C,aAAA,CAAA+C,aAAA,CAAAA,aAAA;QACA9B,EAAA,OAAAN,QAAA,CAAAM;MAAA,GACA,KAAAb,IAAA;QACAY,eAAA,OAAAA,eAAA;QACAd,SAAA,OAAAA;MAAA,EACA,EAAA+B,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAW,MAAA,CAAAV,QAAA;YACAC,IAAA;YACA7B,OAAA;UACA;UACAsC,MAAA,CAAAR,KAAA;UACAQ,MAAA,CAAAR,KAAA;QACA;UACAQ,MAAA,CAAAV,QAAA;YACAC,IAAA;YACA7B,OAAA,EAAA0B,GAAA,CAAAM;UACA;QACA;MACA;IACA;IACAQ,YAAA,WAAAA,aAAA5C,IAAA;MAAA,IAAA6C,MAAA;MACA,KAAAC,KAAA,CAAA9C,IAAA,EAAA+C,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAAvC,KAAA,YAAAuC,MAAA,CAAA3B,YAAA,KAAA2B,MAAA,CAAAJ,aAAA;QACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
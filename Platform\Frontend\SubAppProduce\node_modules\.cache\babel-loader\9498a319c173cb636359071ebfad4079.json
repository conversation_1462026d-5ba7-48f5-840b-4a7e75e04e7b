{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\api\\PRO\\partType.js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\api\\PRO\\partType.js", "mtime": 1757571526678}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "qs", "GetPartTypePageList", "data", "url", "method", "GetPartTypeList", "Set<PERSON>Default", "stringify", "SavePartType", "DeletePartType", "GetFactoryPartTypeIndentifySetting", "SavePartTypeIdentifySetting", "GetPartTypeTree", "GetPartTypeEntity", "GetConsumingProcessAllList", "SaveConsumingProcessAllList", "GetConsumingAllList", "SaveConsumingProcessAllList2"], "sources": ["D:/project/platform_framework_master/platform_framework/Platform/Frontend/SubAppProduce/src/api/PRO/partType.js"], "sourcesContent": ["import request from '@/utils/request'\nimport qs from 'qs'\n\n// 获取零件类型分页列表 (Auth\nexport function GetPartTypePageList(data) {\n  return request({\n    url: '/PRO/PartType/GetPartTypePageList',\n    method: 'post',\n    data\n  })\n}\n// 获取构件类型列表 (Auth\nexport function GetPartTypeList(data) {\n  return request({\n    url: '/PRO/PartType/GetPartTypeList',\n    method: 'post',\n    data\n  })\n}\n\n// 设置零件的默认类型 (Auth)\nexport function SettingDefault(data) {\n  return request({\n    url: '/PRO/PartType/SettingDefault',\n    method: 'post',\n    data: qs.stringify(data)\n  })\n}\n\n// 保存零件(Auth)\nexport function SavePartType(data) {\n  return request({\n    url: '/PRO/PartType/SavePartType',\n    method: 'post',\n    data\n  })\n}\n\n// 删除零件类型 (Auth)\nexport function DeletePartType(data) {\n  return request({\n    url: '/PRO/PartType/DeletePartType',\n    method: 'post',\n    data: qs.stringify(data)\n  })\n}\nexport function GetFactoryPartTypeIndentifySetting(data) {\n  return request({\n    url: '/PRO/PartType/GetFactoryPartTypeIndentifySetting',\n    method: 'post',\n    data\n  })\n}\n\nexport function SavePartTypeIdentifySetting(data) {\n  return request({\n    url: '/PRO/PartType/SavePartTypeIdentifySetting',\n    method: 'post',\n    data\n  })\n}\n\n// 零件类型列表\nexport function GetPartTypeTree(data) {\n  return request({\n    url: '/pro/parttype/GetPartTypeTree',\n    method: 'post',\n    data: qs.stringify(data)\n  })\n}\n\n// 获取零件的信息及配置的零件名称前缀\nexport function GetPartTypeEntity(data) {\n  return request({\n    url: '/pro/parttype/GetPartTypeEntity',\n    method: 'post',\n    data: qs.stringify(data)\n  })\n}\n\n// 获取零件类型配置的领用工序列表\nexport function GetConsumingProcessAllList(data) {\n  return request({\n    url: '/PRO/PartType/GetConsumingProcessAllList',\n    method: 'post',\n    data\n  })\n}\n\n// 保存零件类型配置的领用工序列表\nexport function SaveConsumingProcessAllList(data) {\n  return request({\n    url: '/PRO/PartType/SaveConsumingProcessAllList',\n    method: 'post',\n    data: data\n  })\n}\n\n// 获取零件类型配置的领用工序列表 新\nexport function GetConsumingAllList(data) {\n  return request({\n    url: '/PRO/PartType/GetConsumingAllList',\n    method: 'post',\n    data\n  })\n}\n\n// 保存零件类型配置的领用工序列表 新\nexport function SaveConsumingProcessAllList2(data) {\n  return request({\n    url: '/PRO/PartType/SaveConsumingProcessAllList2',\n    method: 'post',\n    data: data\n  })\n}\n\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;AACrC,OAAOC,EAAE,MAAM,IAAI;;AAEnB;AACA,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACxC,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,mCAAmC;IACxCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASG,eAAeA,CAACH,IAAI,EAAE;EACpC,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,cAAcA,CAACJ,IAAI,EAAE;EACnC,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAEF,EAAE,CAACO,SAAS,CAACL,IAAI;EACzB,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASM,YAAYA,CAACN,IAAI,EAAE;EACjC,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASO,cAAcA,CAACP,IAAI,EAAE;EACnC,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAEF,EAAE,CAACO,SAAS,CAACL,IAAI;EACzB,CAAC,CAAC;AACJ;AACA,OAAO,SAASQ,kCAAkCA,CAACR,IAAI,EAAE;EACvD,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,kDAAkD;IACvDC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASS,2BAA2BA,CAACT,IAAI,EAAE;EAChD,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,2CAA2C;IAChDC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASU,eAAeA,CAACV,IAAI,EAAE;EACpC,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAEF,EAAE,CAACO,SAAS,CAACL,IAAI;EACzB,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASW,iBAAiBA,CAACX,IAAI,EAAE;EACtC,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAEF,EAAE,CAACO,SAAS,CAACL,IAAI;EACzB,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASY,0BAA0BA,CAACZ,IAAI,EAAE;EAC/C,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,0CAA0C;IAC/CC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASa,2BAA2BA,CAACb,IAAI,EAAE;EAChD,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,2CAA2C;IAChDC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASc,mBAAmBA,CAACd,IAAI,EAAE;EACxC,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,mCAAmC;IACxCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASe,4BAA4BA,CAACf,IAAI,EAAE;EACjD,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,4CAA4C;IACjDC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}
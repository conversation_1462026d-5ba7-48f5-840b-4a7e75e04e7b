<template>
  <div class="app-container abs100">
    <div class="wrapper-c">
      <div class="header_tab">
        <el-tabs v-model="activeName">
          <el-tab-pane label="全检" name="全检" />
          <el-tab-pane label="抽检" name="抽检" />
        </el-tabs>
      </div>
      <div class="search_wrapper">
        <el-form ref="form" :model="form" label-width="80px">
          <el-row>
            <el-col :span="5" :lg="5" :xl="5">
              <el-form-item label="质检对象" prop="Check_Object_Type">
                <el-select
                  v-model="form.Check_Object_Type"
                  filterable
                  clearable
                  placeholder="请选择"
                  @change="changeObject"
                >
                  <el-option
                    v-for="item in CheckObjectData"
                    :key="item.Id"
                    :label="item.Display_Name"
                    :value="item.Display_Name"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5" :lg="5" :xl="5">
              <el-form-item label="质检节点" prop="Check_Node_Id">
                <el-select
                  v-model="form.Check_Node_Id"
                  filterable
                  clearable
                  placeholder="请选择"
                  :disabled="!form.Check_Object_Type"
                >
                  <el-option
                    v-for="item in CheckNodeList"
                    :key="item.Id"
                    :label="item.Display_Name"
                    :value="item.Id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4" :lg="4" :xl="4">
              <el-form-item label="质检结果" prop="Check_Result">
                <el-select
                  v-model="form.Check_Result"
                  filterable
                  clearable
                  placeholder="请选择"
                >
                  <el-option label="合格" :value="'合格'" />
                  <el-option label="不合格" :value="'不合格'" />
                  <el-option v-if="activeName === '全检'" label="未一次合格" :value="'未一次合格'" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5" :lg="5" :xl="5">
              <el-form-item label="质检时间" prop="Pick_Date">
                <el-date-picker
                  v-model="form.Pick_Date"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5" :lg="5" :xl="5">
              <el-form-item label="质检人" prop="Check_UserIds">
                <SelectUser v-model="form.Check_UserIds" multiple collapse-tags />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="5" :lg="5" :xl="5">
              <el-form-item label="质检单号" prop="Number_Like">
                <el-input
                  v-model="form.Number_Like "
                  type="text"
                  placeholder="请输入质检单号"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5" :lg="5" :xl="5">
              <el-form-item
                v-if="activeName == '全检'"
                label="名称"
                prop="SteelName"
              >
                <el-input
                  v-model="form.SteelName"
                  type="text"
                  placeholder="请输入（空格间隔筛选多个）"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5" :lg="4" :xl="4">
              <el-form-item label="单据状态" prop="Status">
                <el-select
                  v-model="form.Status"
                  filterable
                  clearable
                  placeholder="请选择"
                >
                  <el-option label="草稿" :value="'草稿'" />
                  <el-option label="待整改" :value="'待整改'" />
                  <el-option label="待复核" :value="'待复核'" />
                  <el-option label="待质检" :value="'待质检'" />
                  <el-option label="已完成" :value="'已完成'" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5" :lg="5" :xl="5">
              <el-form-item
                v-if="activeName == '全检'"
                label="项目名称"
                prop="Project_Id"
              >
                <el-select
                  v-model="form.Project_Id"
                  filterable
                  clearable
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in ProjectNameData"
                    :key="item.Id"
                    :label="item.Short_Name"
                    :value="item.Sys_Project_Id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4" :lg="5" :xl="5">
              <el-form-item label-width="16px">
                <el-button type="primary" @click="handleSearch">搜索</el-button>
                <el-button
                  @click="
                    $refs['form'].resetFields();
                    handleSearch();
                  "
                >重置</el-button>
                <el-button type="success" :loading="exportLoading" @click="handleExport">导出</el-button>
                <el-button
                  type="primary"
                  :disabled="selectList.length == 0"
                  @click="handleQualityAssign"
                >质检分配</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div class="main-wrapper">
        <!--        <el-button style="margin: 10px 0 0 10px" @click="">导出</el-button>-->

        <full-check
          v-if="activeName == '全检'"
          ref="fullCheckRef"
          :search-detail="form"
          @setExportLoading="setExportLoading"
          @selectChange="(val)=>selectList = val"
        />
        <spot-check
          v-if="activeName == '抽检'"
          ref="spotCheckRef"
          :search-detail="form"
          @setExportLoading="setExportLoading"
          @selectChange="(val)=>selectList = val"
        />
      </div>
    </div>

    <!-- 质检分配弹窗 -->
    <el-dialog
      title="质检分配"
      :visible.sync="assignDialogVisible"
      width="400px"
      class="plm-custom-dialog"
      @close="handleAssignClose"
    >
      <el-form :model="assignForm" label-width="80px">
        <el-form-item label="选择人员" required>
          <SelectUser
            v-model="assignForm.userIds"
            placeholder="请选择质检人员"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleAssignClose">取消</el-button>
        <el-button type="primary" :loading="assignLoading" @click="handleAssignSave">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import fullCheck from './components/fullCheck.vue'
import spotCheck from './components/spotCheck.vue'
import { GetDictionaryDetailListByCode, GetNodeList } from '@/api/PRO/factorycheck'
import { GetProjectPageList } from '@/api/PRO/project'
import SelectUser from '@/components/Select/SelectUser/index.vue'
import { ChangeCheckUser } from '@/api/PRO/qualityInspect/quality-management'

export default {
  name: 'PROStartInspect',
  components: {
    SelectUser,
    fullCheck,
    spotCheck
  },
  data() {
    return {
      exportLoading: false,
      activeName: '全检',
      form: {
        Status: '', // 单据状态
        Check_Result: '', // 质检结果
        Project_Id: '', // 项目名称
        Check_Object_Type: '', // 质检对象
        SteelName: '', // 名称
        Check_Node_Id: '', // 质检节点
        Number_Like: '', // 质检单号
        Pick_Date: [], // 质检时间
        BeginDate: null,
        EndDate: null,
        Check_UserIds: []
      },
      CheckNodeList: [], // 质检节点
      CheckObjectData: [], // 质检对象
      check_object_id: null,
      ProjectNameData: [],
      Check_Style: '1',
      selectList: [],
      assignLoading: false,
      assignDialogVisible: false,
      assignForm: {
        userIds: []
      } // 当前要分配的行数据
    }
  },
  watch: {
    activeName: {
      handler(newName, oldName) {
        this.selectList = []
        this.form = {
          Status: '', // 单据状态
          Check_Result: '', // 质检结果
          Project_Id: '', // 项目名称
          Check_Object_Type: '', // 质检对象
          SteelName: '', // 名称
          Check_Node_Id: '', // 质检节点
          Number_Like: '' // 质检单号
        }
        if (newName === '全检') {
          this.Check_Style = '1'
        } else if (newName === '抽检') {
          this.Check_Style = '0'
        }
        this.exportLoading = false
      },

      deep: true
    }
  },
  mounted() {
    this.getCheckType()
    this.getProjectOption()
  },
  methods: {
    // 获取项目
    getProjectOption() {
      GetProjectPageList({
        Page: 1,
        PageSize: -1
      }).then((res) => {
        if (res.IsSucceed) {
          this.ProjectNameData = res.Data.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    getCheckType() {
      GetDictionaryDetailListByCode({ dictionaryCode: 'Quality_Code' })
        .then((res) => {
          if (res.IsSucceed) {
            this.CheckObjectData = res.Data
          } else {
            this.$message({
              type: 'error',
              message: 'res.Message'
            })
          }
        })
        .catch(() => {
          console.log('sdfd')
        })
    },
    changeObject(val) {
      console.log('val', this.form.Check_Object_Type)
      this.form.Check_Node_Id = ''
      const checkObj = this.CheckObjectData.find((v) => {
        return v.Display_Name === val
      })?.Id
      console.log(this.check_object_id)

      GetNodeList({ check_object_id: checkObj, Check_Style: this.Check_Style }).then((res) => {
        if (res.IsSucceed) {
          this.CheckNodeList = res.Data
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    handleSearch() {
      console.log('this.form.Code', this.form.Check_Object_Type)
      // this.form.Code = this.form.SearchCode.trim().replaceAll(" ", "\n");
      if (this.activeName === '全检') {
        this.$refs.fullCheckRef.fetchData(1)
      } else if (this.activeName === '抽检') {
        this.$refs.spotCheckRef.fetchData(1)
      }
    },
    handleExport() {
      if (this.activeName === '全检') {
        this.$refs.fullCheckRef.exportTb()
      } else if (this.activeName === '抽检') {
        this.$refs.spotCheckRef.exportTb()
      }
    },
    setExportLoading(val) {
      console.log('v', val)
      this.exportLoading = val
    },
    // 质检分配相关方法
    handleQualityAssign() {
      if (this.selectList.length === 0) {
        this.$message.warning('请先选择要分配的数据')
        return
      }
      this.assignForm.userIds = []
      this.assignDialogVisible = true
    },
    handleAssignClose() {
      this.assignDialogVisible = false
      this.assignForm.userIds = []
    },
    handleAssignSave() {
      if (!this.assignForm.userIds || this.assignForm.userIds.length === 0) {
        this.$message.warning('请选择质检人员')
        return
      }

      this.assignLoading = true

      // 调用质检分配接口
      const sheetIds = this.selectList.map(item => item.SheetId)
      ChangeCheckUser({
        ids: sheetIds,
        userIds: this.assignForm.userIds
      }).then(res => {
        if (res.IsSucceed) {
          this.$message.success('质检分配成功')
          this.handleAssignClose()
          this.handleSearch() // 刷新列表数据
        } else {
          this.$message.error(res.Message || '质检分配失败')
        }
      }).catch(error => {
        this.$message.error('质检分配失败')
        console.error('质检分配错误:', error)
      }).finally(() => {
        this.assignLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.wrapper-c {
  height: 100%;
  background: #fff;
  display: flex;
  flex-direction: column;
  .main-wrapper {
    background: #ffffff;
    // height: 0;
    flex: 1;
    // display: flex;
    // flex-direction: column;
  }
  .header_tab {
    padding: 0 16px 0 16px;
    box-sizing: border-box;
  }
}
.search_wrapper {
  padding: 16px 16px 0;
  box-sizing: border-box;
  ::v-deep .el-form-item {
    .el-form-item__content {
      & > .el-input {
        width: 100%;
      }
      & > .el-select {
        width: 100%;
      }
    }
    .el-date-editor--daterange.el-input__inner {
      width: 100%;
    }
  }
}

::v-deep .el-tabs__header {
  margin: 0 !important;
}
</style>

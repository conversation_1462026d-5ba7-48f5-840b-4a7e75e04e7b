{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\index.vue", "mtime": 1757926768439}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["ProjectData", "GetLibList", "DeleteTechnology", "RestoreFactoryTechnologyFromProject", "ProjectAdd", "AddDialog", "GetBOMInfo", "name", "components", "data", "dialogVisible", "currentComponent", "bomList", "title", "sysProjectId", "tableConfig", "tableColumns", "tableActions", "tableData", "operateOptions", "width", "align", "isShow", "loading", "selectedProjects", "mounted", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "_yield$GetBOMInfo", "list", "wrap", "_callee$", "_context", "prev", "next", "sent", "console", "log", "stop", "methods", "fetchData", "_this2", "_callee2", "params", "res", "_callee2$", "_context2", "abrupt", "Type", "Sys_Project_Id", "IsSucceed", "Data", "map", "v", "isSysDefault", "t0", "finish", "handleGridData", "handleAdd", "_this3", "$nextTick", "$refs", "handleOpen", "handleEdit", "row", "_this4", "handleDelete", "_this5", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "tbLoading", "technologyId", "Id", "$message", "message", "Message", "catch", "handleReset", "_this6", "handleAddProject", "handleClose", "setProjectData"], "sources": ["src/views/PRO/project-config/product-mfg-path/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <ProjectData @setProjectData=\"setProjectData\" />\r\n    <div class=\"card-x\">\r\n      <div class=\"card-x-top\">\r\n        <el-button type=\"success\" :disabled=\"!sysProjectId\" @click=\"handleAdd\">新增</el-button>\r\n\r\n        <el-button type=\"primary\" :disabled=\"!sysProjectId\" @click=\"handleAddProject\">同步项目配置</el-button>\r\n        <el-button type=\"primary\" :disabled=\"!sysProjectId\" @click=\"handleReset\">恢复默认配置</el-button>\r\n      </div>\r\n      <div class=\"table-section\">\r\n        <bt-table\r\n          ref=\"projectTable\"\r\n          code=\"ProductMftPathConfig\"\r\n          :custom-table-config=\"tableConfig\"\r\n          :grid-data-handler=\"handleGridData\"\r\n          :loading=\"loading\"\r\n        >\r\n          <template #actions=\"{row}\">\r\n            <div>\r\n              <el-button v-if=\"!row.isSysDefault\" type=\"text\" @click=\"handleEdit(row)\">编辑</el-button>\r\n              <el-button type=\"text\" style=\"color: red\" @click=\"handleDelete(row)\">删除</el-button>\r\n            </div>\r\n          </template>\r\n        </bt-table>\r\n\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"title\"\r\n      class=\"plm-custom-dialog\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"30%\"\r\n      top=\"5vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        ref=\"content\"\r\n        :sys-project-id=\"sysProjectId\"\r\n        @refresh=\"fetchData\"\r\n        @close=\"handleClose\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <AddDialog ref=\"dialog\" :bom-list=\"bomList\" :sys-project-id=\"sysProjectId\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ProjectData from '../components/ProjectData.vue'\r\nimport { GetLibList, DeleteTechnology, RestoreFactoryTechnologyFromProject } from '@/api/PRO/technology-lib'\r\nimport ProjectAdd from './component/ProjectAddDialog.vue'\r\nimport AddDialog from '@/views/PRO/process-path/compoments/Add.vue'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\n\r\nexport default {\r\n  name: 'PROProductMfgPath',\r\n  components: {\r\n    ProjectData,\r\n    ProjectAdd,\r\n    AddDialog\r\n  },\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      currentComponent: '',\r\n      bomList: [],\r\n      title: '',\r\n      sysProjectId: '',\r\n      tableConfig: {\r\n        tableColumns: [],\r\n        tableActions: [],\r\n        tableData: [],\r\n        operateOptions: {\r\n          width: 120,\r\n          align: 'center',\r\n          isShow: false\r\n        }\r\n      },\r\n      loading: false,\r\n      selectedProjects: []\r\n    }\r\n  },\r\n  async mounted() {\r\n    // this.fetchData()\r\n    const { list } = await GetBOMInfo()\r\n    this.bomList = list\r\n    console.log('bomList', this.bomList)\r\n  },\r\n  methods: {\r\n\r\n    async fetchData() {\r\n      if (!this.sysProjectId) {\r\n        this.tableConfig.tableData = []\r\n        return\r\n      }\r\n      this.loading = true\r\n      try {\r\n        const params = {\r\n          // Bom_Level: 1,\r\n          Type: 0,\r\n          Sys_Project_Id: this.sysProjectId\r\n        }\r\n        const res = await GetLibList(params)\r\n        if (res.IsSucceed) {\r\n          this.tableConfig.tableData = res.Data.map(v => {\r\n            v.isSysDefault = !v.Sys_Project_Id\r\n            return v\r\n          })\r\n        }\r\n      } catch (error) {\r\n        console.log('error', error)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    handleGridData(data) {\r\n      console.log('data', data)\r\n      return data\r\n    },\r\n    handleAdd() {\r\n      console.log('新增')\r\n      this.$nextTick(() => {\r\n        this.$refs['dialog'].handleOpen()\r\n      })\r\n    },\r\n    handleEdit(row) {\r\n      console.log(row)\r\n      this.$nextTick(() => {\r\n        this.$refs['dialog'].handleOpen(row)\r\n      })\r\n    },\r\n    handleDelete(row) {\r\n      this.$confirm('是否删除该工艺', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbLoading = true\r\n        DeleteTechnology({\r\n          technologyId: row.Id,\r\n          sysProjectId: this.sysProjectId\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.$confirm('是否恢复默认配置', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        RestoreFactoryTechnologyFromProject({\r\n          Sys_Project_Id: this.sysProjectId\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '恢复成功!'\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    handleAddProject() {\r\n      console.log('从项目添加')\r\n      this.dialogVisible = true\r\n      this.currentComponent = 'ProjectAdd'\r\n      this.title = '从项目添加'\r\n    },\r\n\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n      this.currentComponent = ''\r\n      this.title = ''\r\n    },\r\n    setProjectData(data) {\r\n      this.selectedProjects = data\r\n      this.sysProjectId = data?.Sys_Project_Id || ''\r\n      console.log('selectedProjects', this.selectedProjects)\r\n      this.fetchData()\r\n    }\r\n  }\r\n\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container{\r\n  display: flex;\r\n  flex-direction: row;\r\n  height: 100%;\r\n  .card-x{\r\n    padding: 16px;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    background-color: #ffffff;\r\n    .card-x-top{\r\n      margin-bottom: 16px;\r\n    }\r\n    .table-section {\r\n      flex: 1;\r\n      background: #fff;\r\n      border-radius: 4px;\r\n      overflow: hidden;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDA,OAAAA,WAAA;AACA,SAAAC,UAAA,EAAAC,gBAAA,EAAAC,mCAAA;AACA,OAAAC,UAAA;AACA,OAAAC,SAAA;AACA,SAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAR,WAAA,EAAAA,WAAA;IACAI,UAAA,EAAAA,UAAA;IACAC,SAAA,EAAAA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,gBAAA;MACAC,OAAA;MACAC,KAAA;MACAC,YAAA;MACAC,WAAA;QACAC,YAAA;QACAC,YAAA;QACAC,SAAA;QACAC,cAAA;UACAC,KAAA;UACAC,KAAA;UACAC,MAAA;QACA;MACA;MACAC,OAAA;MACAC,gBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAAC,iBAAA,EAAAC,IAAA;MAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OAEA/B,UAAA;UAAA;YAAAyB,iBAAA,GAAAI,QAAA,CAAAG,IAAA;YAAAN,IAAA,GAAAD,iBAAA,CAAAC,IAAA;YACAN,KAAA,CAAAd,OAAA,GAAAoB,IAAA;YACAO,OAAA,CAAAC,GAAA,YAAAd,KAAA,CAAAd,OAAA;UAAA;UAAA;YAAA,OAAAuB,QAAA,CAAAM,IAAA;QAAA;MAAA,GAAAX,OAAA;IAAA;EACA;EACAY,OAAA;IAEAC,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MAAA,OAAAjB,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAgB,SAAA;QAAA,IAAAC,MAAA,EAAAC,GAAA;QAAA,OAAAnB,mBAAA,GAAAK,IAAA,UAAAe,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAb,IAAA,GAAAa,SAAA,CAAAZ,IAAA;YAAA;cAAA,IACAO,MAAA,CAAA9B,YAAA;gBAAAmC,SAAA,CAAAZ,IAAA;gBAAA;cAAA;cACAO,MAAA,CAAA7B,WAAA,CAAAG,SAAA;cAAA,OAAA+B,SAAA,CAAAC,MAAA;YAAA;cAGAN,MAAA,CAAArB,OAAA;cAAA0B,SAAA,CAAAb,IAAA;cAEAU,MAAA;gBACA;gBACAK,IAAA;gBACAC,cAAA,EAAAR,MAAA,CAAA9B;cACA;cAAAmC,SAAA,CAAAZ,IAAA;cAAA,OACApC,UAAA,CAAA6C,MAAA;YAAA;cAAAC,GAAA,GAAAE,SAAA,CAAAX,IAAA;cACA,IAAAS,GAAA,CAAAM,SAAA;gBACAT,MAAA,CAAA7B,WAAA,CAAAG,SAAA,GAAA6B,GAAA,CAAAO,IAAA,CAAAC,GAAA,WAAAC,CAAA;kBACAA,CAAA,CAAAC,YAAA,IAAAD,CAAA,CAAAJ,cAAA;kBACA,OAAAI,CAAA;gBACA;cACA;cAAAP,SAAA,CAAAZ,IAAA;cAAA;YAAA;cAAAY,SAAA,CAAAb,IAAA;cAAAa,SAAA,CAAAS,EAAA,GAAAT,SAAA;cAEAV,OAAA,CAAAC,GAAA,UAAAS,SAAA,CAAAS,EAAA;YAAA;cAAAT,SAAA,CAAAb,IAAA;cAEAQ,MAAA,CAAArB,OAAA;cAAA,OAAA0B,SAAA,CAAAU,MAAA;YAAA;YAAA;cAAA,OAAAV,SAAA,CAAAR,IAAA;UAAA;QAAA,GAAAI,QAAA;MAAA;IAEA;IACAe,cAAA,WAAAA,eAAAnD,IAAA;MACA8B,OAAA,CAAAC,GAAA,SAAA/B,IAAA;MACA,OAAAA,IAAA;IACA;IACAoD,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACAvB,OAAA,CAAAC,GAAA;MACA,KAAAuB,SAAA;QACAD,MAAA,CAAAE,KAAA,WAAAC,UAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAC,GAAA;MAAA,IAAAC,MAAA;MACA7B,OAAA,CAAAC,GAAA,CAAA2B,GAAA;MACA,KAAAJ,SAAA;QACAK,MAAA,CAAAJ,KAAA,WAAAC,UAAA,CAAAE,GAAA;MACA;IACA;IACAE,YAAA,WAAAA,aAAAF,GAAA;MAAA,IAAAG,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACAL,MAAA,CAAAM,SAAA;QACA1E,gBAAA;UACA2E,YAAA,EAAAV,GAAA,CAAAW,EAAA;UACAhE,YAAA,EAAAwD,MAAA,CAAAxD;QACA,GAAA6D,IAAA,WAAA5B,GAAA;UACA,IAAAA,GAAA,CAAAM,SAAA;YACAiB,MAAA,CAAAS,QAAA;cACAL,IAAA;cACAM,OAAA;YACA;YACAV,MAAA,CAAA3B,SAAA;UACA;YACA2B,MAAA,CAAAS,QAAA;cACAC,OAAA,EAAAjC,GAAA,CAAAkC,OAAA;cACAP,IAAA;YACA;UACA;QACA;MACA,GAAAQ,KAAA;QACAZ,MAAA,CAAAS,QAAA;UACAL,IAAA;UACAM,OAAA;QACA;MACA;IACA;IACAG,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAb,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACAxE,mCAAA;UACAiD,cAAA,EAAAgC,MAAA,CAAAtE;QACA,GAAA6D,IAAA,WAAA5B,GAAA;UACA,IAAAA,GAAA,CAAAM,SAAA;YACA+B,MAAA,CAAAL,QAAA;cACAL,IAAA;cACAM,OAAA;YACA;UACA;YACAI,MAAA,CAAAL,QAAA;cACAC,OAAA,EAAAjC,GAAA,CAAAkC,OAAA;cACAP,IAAA;YACA;UACA;QACA;MACA;IACA;IACAW,gBAAA,WAAAA,iBAAA;MACA9C,OAAA,CAAAC,GAAA;MACA,KAAA9B,aAAA;MACA,KAAAC,gBAAA;MACA,KAAAE,KAAA;IACA;IAEAyE,WAAA,WAAAA,YAAA;MACA,KAAA5E,aAAA;MACA,KAAAC,gBAAA;MACA,KAAAE,KAAA;IACA;IACA0E,cAAA,WAAAA,eAAA9E,IAAA;MACA,KAAAe,gBAAA,GAAAf,IAAA;MACA,KAAAK,YAAA,IAAAL,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAA2C,cAAA;MACAb,OAAA,CAAAC,GAAA,0BAAAhB,gBAAA;MACA,KAAAmB,SAAA;IACA;EACA;AAEA", "ignoreList": []}]}
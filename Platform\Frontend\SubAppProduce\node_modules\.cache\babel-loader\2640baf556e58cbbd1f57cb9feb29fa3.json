{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\index.vue", "mtime": 1758266768055}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["ProjectData", "GetLibList", "DeleteTechnology", "RestoreFactoryTechnologyFromProject", "ProjectAdd", "AddDialog", "GetBOMInfo", "getBomName", "name", "components", "data", "dialogVisible", "currentComponent", "bomList", "title", "sysProjectId", "tableConfig", "tableColumns", "tableActions", "tableData", "operateOptions", "width", "align", "isShow", "loading", "selectedProjects", "mounted", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "_yield$GetBOMInfo", "list", "wrap", "_callee$", "_context", "prev", "next", "sent", "console", "log", "stop", "methods", "getCurBomName", "code", "currentBomInfo", "find", "item", "Code", "toString", "Display_Name", "fetchData", "_this2", "_callee3", "params", "res", "_callee3$", "_context3", "abrupt", "Type", "Sys_Project_Id", "IsSucceed", "Promise", "all", "Data", "map", "_ref", "_callee2", "v", "_callee2$", "_context2", "isSysDefault", "bom<PERSON>ame", "_x", "apply", "arguments", "t0", "finish", "handleGridData", "handleAdd", "_this3", "$nextTick", "$refs", "handleOpen", "handleEdit", "row", "_this4", "handleDelete", "_this5", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "tbLoading", "technologyId", "Id", "$message", "message", "Message", "catch", "handleReset", "_this6", "handleAddProject", "handleClose", "setProjectData"], "sources": ["src/views/PRO/project-config/product-mfg-path/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <ProjectData @setProjectData=\"setProjectData\" />\r\n    <div class=\"card-x\">\r\n      <div class=\"card-x-top\">\r\n        <el-button type=\"success\" :disabled=\"!sysProjectId\" @click=\"handleAdd\">新增</el-button>\r\n\r\n        <el-button type=\"primary\" :disabled=\"!sysProjectId\" @click=\"handleAddProject\">同步项目配置</el-button>\r\n        <el-button type=\"primary\" :disabled=\"!sysProjectId\" @click=\"handleReset\">恢复工厂默认配置</el-button>\r\n      </div>\r\n      <div class=\"table-section\">\r\n        <bt-table\r\n          ref=\"projectTable\"\r\n          code=\"ProductMftPathConfig\"\r\n          :custom-table-config=\"tableConfig\"\r\n          :grid-data-handler=\"handleGridData\"\r\n          :loading=\"loading\"\r\n        >\r\n          <template #Type=\"{row}\">\r\n            <div>\r\n              <span :style=\"{color:row.Type===1 ?'#d29730': row.Type===2?'#20bbc7':'#de85e4'}\">\r\n                {{ getCurBomName(row.Bom_Level) }}工艺\r\n              </span>\r\n            </div>\r\n          </template>\r\n          <template #Type1=\"{row}\">\r\n            {{ row.Component_Type }}\r\n          </template>\r\n          <template #actions=\"{row}\">\r\n            <div>\r\n              <el-button v-if=\"!row.isSysDefault\" type=\"text\" @click=\"handleEdit(row)\">编辑</el-button>\r\n              <el-button type=\"text\" style=\"color: red\" @click=\"handleDelete(row)\">删除</el-button>\r\n            </div>\r\n          </template>\r\n        </bt-table>\r\n\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"title\"\r\n      class=\"plm-custom-dialog\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"30%\"\r\n      top=\"5vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        ref=\"content\"\r\n        :sys-project-id=\"sysProjectId\"\r\n        @refresh=\"fetchData\"\r\n        @close=\"handleClose\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <AddDialog ref=\"dialog\" :bom-list=\"bomList\" :sys-project-id=\"sysProjectId\" @refresh=\"fetchData\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ProjectData from '../components/ProjectData.vue'\r\nimport { GetLibList, DeleteTechnology, RestoreFactoryTechnologyFromProject } from '@/api/PRO/technology-lib'\r\nimport ProjectAdd from './component/ProjectAddDialog.vue'\r\nimport AddDialog from '@/views/PRO/process-path/compoments/Add.vue'\r\nimport { GetBOMInfo, getBomName } from '@/views/PRO/bom-setting/utils'\r\n\r\nexport default {\r\n  name: 'PROProductMfgPath',\r\n  components: {\r\n    ProjectData,\r\n    ProjectAdd,\r\n    AddDialog\r\n  },\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      currentComponent: '',\r\n      bomList: [],\r\n      title: '',\r\n      sysProjectId: '',\r\n      tableConfig: {\r\n        tableColumns: [],\r\n        tableActions: [],\r\n        tableData: [],\r\n        operateOptions: {\r\n          width: 120,\r\n          align: 'center',\r\n          isShow: false\r\n        }\r\n      },\r\n      loading: false,\r\n      selectedProjects: []\r\n    }\r\n  },\r\n  async mounted() {\r\n    // this.fetchData()\r\n    const { list } = await GetBOMInfo()\r\n    this.bomList = list\r\n    console.log('bomList', this.bomList)\r\n  },\r\n  methods: {\r\n    getCurBomName(code) {\r\n      const currentBomInfo = this.bomList.find(item => {\r\n        return item.Code.toString() === code.toString()\r\n      })\r\n      return currentBomInfo?.Display_Name || ''\r\n    },\r\n    async fetchData() {\r\n      if (!this.sysProjectId) {\r\n        this.tableConfig.tableData = []\r\n        return\r\n      }\r\n      this.loading = true\r\n      try {\r\n        const params = {\r\n          // Bom_Level: 1,\r\n          Type: 0,\r\n          Sys_Project_Id: this.sysProjectId\r\n        }\r\n        const res = await GetLibList(params)\r\n        if (res.IsSucceed) {\r\n          this.tableConfig.tableData = await Promise.all(\r\n            res.Data.map(async v => {\r\n              v.isSysDefault = !v.Sys_Project_Id\r\n              v.bomName = await getBomName(v.Type)\r\n              return v\r\n            })\r\n          )\r\n        }\r\n      } catch (error) {\r\n        console.log('error', error)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    handleGridData(data) {\r\n      console.log('data', data)\r\n      return data\r\n    },\r\n    handleAdd() {\r\n      console.log('新增')\r\n      this.$nextTick(() => {\r\n        this.$refs['dialog'].handleOpen()\r\n      })\r\n    },\r\n    handleEdit(row) {\r\n      console.log(row)\r\n      this.$nextTick(() => {\r\n        this.$refs['dialog'].handleOpen(row)\r\n      })\r\n    },\r\n    handleDelete(row) {\r\n      this.$confirm('是否删除该工艺', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbLoading = true\r\n        DeleteTechnology({\r\n          technologyId: row.Id,\r\n          sysProjectId: this.sysProjectId\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.$confirm('此操作将会恢复到工厂的产品生产路径, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        RestoreFactoryTechnologyFromProject({\r\n          Sys_Project_Id: this.sysProjectId\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '恢复成功!'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    handleAddProject() {\r\n      console.log('同步项目配置')\r\n      this.dialogVisible = true\r\n      this.currentComponent = 'ProjectAdd'\r\n      this.title = '同步项目配置'\r\n    },\r\n\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n      this.currentComponent = ''\r\n      this.title = ''\r\n    },\r\n    setProjectData(data) {\r\n      this.selectedProjects = data\r\n      this.sysProjectId = data?.Sys_Project_Id || ''\r\n      console.log('selectedProjects', this.selectedProjects)\r\n      this.fetchData()\r\n    }\r\n  }\r\n\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container{\r\n  display: flex;\r\n  flex-direction: row;\r\n  height: 100%;\r\n  .card-x{\r\n    padding: 16px;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    background-color: #ffffff;\r\n    .card-x-top{\r\n      margin-bottom: 16px;\r\n    }\r\n    .table-section {\r\n      flex: 1;\r\n      background: #fff;\r\n      border-radius: 4px;\r\n      overflow: hidden;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA,OAAAA,WAAA;AACA,SAAAC,UAAA,EAAAC,gBAAA,EAAAC,mCAAA;AACA,OAAAC,UAAA;AACA,OAAAC,SAAA;AACA,SAAAC,UAAA,EAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAT,WAAA,EAAAA,WAAA;IACAI,UAAA,EAAAA,UAAA;IACAC,SAAA,EAAAA;EACA;EACAK,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,gBAAA;MACAC,OAAA;MACAC,KAAA;MACAC,YAAA;MACAC,WAAA;QACAC,YAAA;QACAC,YAAA;QACAC,SAAA;QACAC,cAAA;UACAC,KAAA;UACAC,KAAA;UACAC,MAAA;QACA;MACA;MACAC,OAAA;MACAC,gBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAAC,iBAAA,EAAAC,IAAA;MAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OAEAhC,UAAA;UAAA;YAAA0B,iBAAA,GAAAI,QAAA,CAAAG,IAAA;YAAAN,IAAA,GAAAD,iBAAA,CAAAC,IAAA;YACAN,KAAA,CAAAd,OAAA,GAAAoB,IAAA;YACAO,OAAA,CAAAC,GAAA,YAAAd,KAAA,CAAAd,OAAA;UAAA;UAAA;YAAA,OAAAuB,QAAA,CAAAM,IAAA;QAAA;MAAA,GAAAX,OAAA;IAAA;EACA;EACAY,OAAA;IACAC,aAAA,WAAAA,cAAAC,IAAA;MACA,IAAAC,cAAA,QAAAjC,OAAA,CAAAkC,IAAA,WAAAC,IAAA;QACA,OAAAA,IAAA,CAAAC,IAAA,CAAAC,QAAA,OAAAL,IAAA,CAAAK,QAAA;MACA;MACA,QAAAJ,cAAA,aAAAA,cAAA,uBAAAA,cAAA,CAAAK,YAAA;IACA;IACAC,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MAAA,OAAAzB,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAwB,SAAA;QAAA,IAAAC,MAAA,EAAAC,GAAA;QAAA,OAAA3B,mBAAA,GAAAK,IAAA,UAAAuB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArB,IAAA,GAAAqB,SAAA,CAAApB,IAAA;YAAA;cAAA,IACAe,MAAA,CAAAtC,YAAA;gBAAA2C,SAAA,CAAApB,IAAA;gBAAA;cAAA;cACAe,MAAA,CAAArC,WAAA,CAAAG,SAAA;cAAA,OAAAuC,SAAA,CAAAC,MAAA;YAAA;cAGAN,MAAA,CAAA7B,OAAA;cAAAkC,SAAA,CAAArB,IAAA;cAEAkB,MAAA;gBACA;gBACAK,IAAA;gBACAC,cAAA,EAAAR,MAAA,CAAAtC;cACA;cAAA2C,SAAA,CAAApB,IAAA;cAAA,OACArC,UAAA,CAAAsD,MAAA;YAAA;cAAAC,GAAA,GAAAE,SAAA,CAAAnB,IAAA;cAAA,KACAiB,GAAA,CAAAM,SAAA;gBAAAJ,SAAA,CAAApB,IAAA;gBAAA;cAAA;cAAAoB,SAAA,CAAApB,IAAA;cAAA,OACAyB,OAAA,CAAAC,GAAA,CACAR,GAAA,CAAAS,IAAA,CAAAC,GAAA;gBAAA,IAAAC,IAAA,GAAAvC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAsC,SAAAC,CAAA;kBAAA,OAAAxC,mBAAA,GAAAK,IAAA,UAAAoC,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAAlC,IAAA,GAAAkC,SAAA,CAAAjC,IAAA;sBAAA;wBACA+B,CAAA,CAAAG,YAAA,IAAAH,CAAA,CAAAR,cAAA;wBAAAU,SAAA,CAAAjC,IAAA;wBAAA,OACA/B,UAAA,CAAA8D,CAAA,CAAAT,IAAA;sBAAA;wBAAAS,CAAA,CAAAI,OAAA,GAAAF,SAAA,CAAAhC,IAAA;wBAAA,OAAAgC,SAAA,CAAAZ,MAAA,WACAU,CAAA;sBAAA;sBAAA;wBAAA,OAAAE,SAAA,CAAA7B,IAAA;oBAAA;kBAAA,GAAA0B,QAAA;gBAAA,CACA;gBAAA,iBAAAM,EAAA;kBAAA,OAAAP,IAAA,CAAAQ,KAAA,OAAAC,SAAA;gBAAA;cAAA,IACA;YAAA;cANAvB,MAAA,CAAArC,WAAA,CAAAG,SAAA,GAAAuC,SAAA,CAAAnB,IAAA;YAAA;cAAAmB,SAAA,CAAApB,IAAA;cAAA;YAAA;cAAAoB,SAAA,CAAArB,IAAA;cAAAqB,SAAA,CAAAmB,EAAA,GAAAnB,SAAA;cASAlB,OAAA,CAAAC,GAAA,UAAAiB,SAAA,CAAAmB,EAAA;YAAA;cAAAnB,SAAA,CAAArB,IAAA;cAEAgB,MAAA,CAAA7B,OAAA;cAAA,OAAAkC,SAAA,CAAAoB,MAAA;YAAA;YAAA;cAAA,OAAApB,SAAA,CAAAhB,IAAA;UAAA;QAAA,GAAAY,QAAA;MAAA;IAEA;IACAyB,cAAA,WAAAA,eAAArE,IAAA;MACA8B,OAAA,CAAAC,GAAA,SAAA/B,IAAA;MACA,OAAAA,IAAA;IACA;IACAsE,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACAzC,OAAA,CAAAC,GAAA;MACA,KAAAyC,SAAA;QACAD,MAAA,CAAAE,KAAA,WAAAC,UAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAC,GAAA;MAAA,IAAAC,MAAA;MACA/C,OAAA,CAAAC,GAAA,CAAA6C,GAAA;MACA,KAAAJ,SAAA;QACAK,MAAA,CAAAJ,KAAA,WAAAC,UAAA,CAAAE,GAAA;MACA;IACA;IACAE,YAAA,WAAAA,aAAAF,GAAA;MAAA,IAAAG,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACAL,MAAA,CAAAM,SAAA;QACA7F,gBAAA;UACA8F,YAAA,EAAAV,GAAA,CAAAW,EAAA;UACAlF,YAAA,EAAA0E,MAAA,CAAA1E;QACA,GAAA+E,IAAA,WAAAtC,GAAA;UACA,IAAAA,GAAA,CAAAM,SAAA;YACA2B,MAAA,CAAAS,QAAA;cACAL,IAAA;cACAM,OAAA;YACA;YACAV,MAAA,CAAArC,SAAA;UACA;YACAqC,MAAA,CAAAS,QAAA;cACAC,OAAA,EAAA3C,GAAA,CAAA4C,OAAA;cACAP,IAAA;YACA;UACA;QACA;MACA,GAAAQ,KAAA;QACAZ,MAAA,CAAAS,QAAA;UACAL,IAAA;UACAM,OAAA;QACA;MACA;IACA;IACAG,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAb,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACA3F,mCAAA;UACA0D,cAAA,EAAA0C,MAAA,CAAAxF;QACA,GAAA+E,IAAA,WAAAtC,GAAA;UACA,IAAAA,GAAA,CAAAM,SAAA;YACAyC,MAAA,CAAAL,QAAA;cACAL,IAAA;cACAM,OAAA;YACA;YACAI,MAAA,CAAAnD,SAAA;UACA;YACAmD,MAAA,CAAAL,QAAA;cACAC,OAAA,EAAA3C,GAAA,CAAA4C,OAAA;cACAP,IAAA;YACA;UACA;QACA;MACA;IACA;IACAW,gBAAA,WAAAA,iBAAA;MACAhE,OAAA,CAAAC,GAAA;MACA,KAAA9B,aAAA;MACA,KAAAC,gBAAA;MACA,KAAAE,KAAA;IACA;IAEA2F,WAAA,WAAAA,YAAA;MACA,KAAA9F,aAAA;MACA,KAAAC,gBAAA;MACA,KAAAE,KAAA;IACA;IACA4F,cAAA,WAAAA,eAAAhG,IAAA;MACA,KAAAe,gBAAA,GAAAf,IAAA;MACA,KAAAK,YAAA,IAAAL,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAAmD,cAAA;MACArB,OAAA,CAAAC,GAAA,0BAAAhB,gBAAA;MACA,KAAA2B,SAAA;IACA;EACA;AAEA", "ignoreList": []}]}
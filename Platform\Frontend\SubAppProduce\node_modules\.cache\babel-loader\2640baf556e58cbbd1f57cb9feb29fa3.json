{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\index.vue", "mtime": 1757468128031}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON><PERSON>brua3u+WKoCcpOwogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSAnUHJvamVjdEFkZCc7CiAgICAgIHRoaXMudGl0bGUgPSAn5LuO6aG555uu5re75YqgJzsKICAgIH0sCiAgICBoYW5kbGVBZGRGYWN0b3J5OiBmdW5jdGlvbiBoYW5kbGVBZGRGYWN0b3J5KCkgewogICAgICBjb25zb2xlLmxvZygn5LuO5bel5Y6C5re75YqgJyk7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9ICdGYWN0b3J5QWRkJzsKICAgICAgdGhpcy50aXRsZSA9ICfku47lt6XljoLmt7vliqAnOwogICAgfSwKICAgIGhhbmRsZUNsb3NlOiBmdW5jdGlvbiBoYW5kbGVDbG9zZSgpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9ICcnOwogICAgICB0aGlzLnRpdGxlID0gJyc7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["ProjectData", "GetLibList", "ProjectAdd", "FactoryAdd", "name", "components", "data", "dialogVisible", "currentComponent", "title", "tableConfig", "tableColumns", "tableActions", "tableData", "checkbox", "operateOptions", "width", "align", "isShow", "loading", "selectedProjects", "created", "fetchData", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "params", "res", "wrap", "_callee$", "_context", "prev", "next", "Id", "Type", "sent", "IsSucceed", "Data", "t0", "console", "log", "finish", "stop", "handleGridData", "handleSelectionChange", "selection", "handleAdd", "handleDelete", "handleAddProject", "handleAddFactory", "handleClose"], "sources": ["src/views/PRO/project-config/product-mfg-path/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <ProjectData />\r\n    <div class=\"card-x\">\r\n      <div class=\"card-x-top\">\r\n        <el-button type=\"primary\" @click=\"handleAdd\">新增</el-button>\r\n        <el-button type=\"danger\" @click=\"handleDelete\">删除</el-button>\r\n\r\n        <el-button type=\"primary\" @click=\"handleAddProject\">从项目添加</el-button>\r\n        <el-button type=\"primary\" @click=\"handleAddFactory\">从工厂添加</el-button>\r\n      </div>\r\n      <div class=\"table-section\">\r\n        <bt-table\r\n          ref=\"projectTable\"\r\n          code=\"ProductMftPathConfig\"\r\n          :custom-table-config=\"tableConfig\"\r\n          :grid-data-handler=\"handleGridData\"\r\n          :loading=\"loading\"\r\n          @selection-change=\"handleSelectionChange\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"title\"\r\n      class=\"plm-custom-dialog\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"80%\"\r\n      top=\"5vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        ref=\"content\"\r\n        @close=\"handleClose\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ProjectData from '../components/ProjectData.vue'\r\nimport { GetLibList } from '@/api/PRO/technology-lib'\r\nimport ProjectAdd from './component/ProjectAddDialog.vue'\r\nimport FactoryAdd from './component/FactoryAddDialog.vue'\r\nexport default {\r\n  name: 'PROProductMfgPath',\r\n  components: {\r\n    ProjectData,\r\n    ProjectAdd,\r\n    FactoryAdd\r\n  },\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      currentComponent: '',\r\n      title: '',\r\n      tableConfig: {\r\n        tableColumns: [],\r\n        tableActions: [],\r\n        tableData: [],\r\n        checkbox: true,\r\n        operateOptions: {\r\n          width: 120,\r\n          align: 'center',\r\n          isShow: false\r\n        }\r\n      },\r\n      loading: false,\r\n      selectedProjects: []\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    async fetchData() {\r\n      this.loading = true\r\n      try {\r\n        const params = {\r\n          Id: '',\r\n          Type: 0\r\n        }\r\n        const res = await GetLibList(params)\r\n        if (res.IsSucceed) {\r\n          this.tableConfig.tableData = res.Data\r\n        }\r\n      } catch (error) {\r\n        console.log('error', error)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    handleGridData(data) {\r\n      console.log('data', data)\r\n      return data\r\n    },\r\n    handleSelectionChange(selection) {\r\n      console.log('selection', selection)\r\n    },\r\n    handleAdd() {\r\n      console.log('新增')\r\n    },\r\n    handleDelete() {\r\n      console.log('删除')\r\n    },\r\n    handleAddProject() {\r\n      console.log('从项目添加')\r\n      this.dialogVisible = true\r\n      this.currentComponent = 'ProjectAdd'\r\n      this.title = '从项目添加'\r\n    },\r\n    handleAddFactory() {\r\n      console.log('从工厂添加')\r\n      this.dialogVisible = true\r\n      this.currentComponent = 'FactoryAdd'\r\n      this.title = '从工厂添加'\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n      this.currentComponent = ''\r\n      this.title = ''\r\n    }\r\n  }\r\n\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container{\r\n  display: flex;\r\n  flex-direction: row;\r\n  height: 100%;\r\n  .card-x{\r\n    padding: 16px;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    background-color: #ffffff;\r\n    .card-x-top{\r\n      margin-bottom: 16px;\r\n    }\r\n    .table-section {\r\n      flex: 1;\r\n      background: #fff;\r\n      border-radius: 4px;\r\n      overflow: hidden;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA,OAAAA,WAAA;AACA,SAAAC,UAAA;AACA,OAAAC,UAAA;AACA,OAAAC,UAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAL,WAAA,EAAAA,WAAA;IACAE,UAAA,EAAAA,UAAA;IACAC,UAAA,EAAAA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,gBAAA;MACAC,KAAA;MACAC,WAAA;QACAC,YAAA;QACAC,YAAA;QACAC,SAAA;QACAC,QAAA;QACAC,cAAA;UACAC,KAAA;UACAC,KAAA;UACAC,MAAA;QACA;MACA;MACAC,OAAA;MACAC,gBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACAD,SAAA,WAAAA,UAAA;MAAA,IAAAE,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,MAAA,EAAAC,GAAA;QAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAX,KAAA,CAAAL,OAAA;cAAAc,QAAA,CAAAC,IAAA;cAEAL,MAAA;gBACAO,EAAA;gBACAC,IAAA;cACA;cAAAJ,QAAA,CAAAE,IAAA;cAAA,OACAlC,UAAA,CAAA4B,MAAA;YAAA;cAAAC,GAAA,GAAAG,QAAA,CAAAK,IAAA;cACA,IAAAR,GAAA,CAAAS,SAAA;gBACAf,KAAA,CAAAd,WAAA,CAAAG,SAAA,GAAAiB,GAAA,CAAAU,IAAA;cACA;cAAAP,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAQ,EAAA,GAAAR,QAAA;cAEAS,OAAA,CAAAC,GAAA,UAAAV,QAAA,CAAAQ,EAAA;YAAA;cAAAR,QAAA,CAAAC,IAAA;cAEAV,KAAA,CAAAL,OAAA;cAAA,OAAAc,QAAA,CAAAW,MAAA;YAAA;YAAA;cAAA,OAAAX,QAAA,CAAAY,IAAA;UAAA;QAAA,GAAAjB,OAAA;MAAA;IAEA;IACAkB,cAAA,WAAAA,eAAAxC,IAAA;MACAoC,OAAA,CAAAC,GAAA,SAAArC,IAAA;MACA,OAAAA,IAAA;IACA;IACAyC,qBAAA,WAAAA,sBAAAC,SAAA;MACAN,OAAA,CAAAC,GAAA,cAAAK,SAAA;IACA;IACAC,SAAA,WAAAA,UAAA;MACAP,OAAA,CAAAC,GAAA;IACA;IACAO,YAAA,WAAAA,aAAA;MACAR,OAAA,CAAAC,GAAA;IACA;IACAQ,gBAAA,WAAAA,iBAAA;MACAT,OAAA,CAAAC,GAAA;MACA,KAAApC,aAAA;MACA,KAAAC,gBAAA;MACA,KAAAC,KAAA;IACA;IACA2C,gBAAA,WAAAA,iBAAA;MACAV,OAAA,CAAAC,GAAA;MACA,KAAApC,aAAA;MACA,KAAAC,gBAAA;MACA,KAAAC,KAAA;IACA;IACA4C,WAAA,WAAAA,YAAA;MACA,KAAA9C,aAAA;MACA,KAAAC,gBAAA;MACA,KAAAC,KAAA;IACA;EACA;AAEA", "ignoreList": []}]}
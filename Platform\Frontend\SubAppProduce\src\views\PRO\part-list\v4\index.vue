<template>
  <div class="app-container abs100">
    <div
      v-loading="pgLoading"
      style="display: flex"
      class="h100"
      element-loading-text="加载中"
    >
      <ExpandableSection v-model="showExpand" :width="300" class="cs-left fff">
        <div class="inner-wrapper">
          <div class="tree-search">
            <el-select
              v-model="statusType"
              clearable
              class="search-select"
              placeholder="导入状态选择"
            >
              <el-option label="已导入" value="已导入" />
              <el-option label="未导入" value="未导入" />
              <el-option label="已变更" value="已变更" />
            </el-select>
            <el-input
              v-model.trim="projectName"
              placeholder="关键词搜索"
              size="small"
              clearable
              suffix-icon="el-icon-search"
              @blur="fetchTreeDataLocal"
              @clear="fetchTreeDataLocal"
              @keydown.enter.native="fetchTreeDataLocal"
            />
          </div>
          <el-divider class="cs-divider" />
          <div class="tree-x cs-scroll">
            <tree-detail
              ref="tree"
              icon="icon-folder"
              is-custom-filter
              :custom-filter-fun="customFilterFun"
              :loading="treeLoading"
              :tree-data="treeData"
              show-status
              show-detail
              :filter-text="filterText"
              :expanded-key="expandedKey"
              @handleNodeClick="handleNodeClick"
            >
              <template #csLabel="{ showStatus, data }">
                <span
                  v-if="!data.ParentNodes"
                  class="cs-blue"
                >({{ data.Code }})</span>{{ data.Label }}
                <template v-if="showStatus && data.Label != '全部'">
                  <span v-if="data.Data.Is_Deepen_Change" class="cs-tag redBg">
                    <i class="fourRed">已变更</i></span>
                  <span
                    v-else
                    :class="[
                      'cs-tag',
                      data.Data.Is_Imported == true ? 'greenBg' : 'orangeBg',
                    ]"
                  >
                    <i
                      :class="[
                        data.Data.Is_Imported == true
                          ? 'fourGreen'
                          : 'fourOrange',
                      ]"
                    >{{
                      data.Data.Is_Imported == true ? "已导入" : "未导入"
                    }}</i>
                  </span>
                </template>
              </template></tree-detail>
          </div>
        </div>
      </ExpandableSection>
      <div class="cs-right" style="padding-right: 0">
        <div class="container">
          <div ref="searchDom" class="cs-from">
            <div class="cs-search">
              <el-form
                ref="customParams"
                :model="customParams"
                label-width="80px"
                class="demo-form-inline"
              >
                <el-row>
                  <el-col :span="6">
                    <el-form-item
                      :label="levelName + '名称'"
                      prop="Names"
                    >
                      <el-input
                        v-model="names"
                        clearable
                        style="width: 100%"
                        class="input-with-select"
                        placeholder="请输入内容"
                        size="small"
                      >
                        <el-select
                          slot="prepend"
                          v-model="nameMode"
                          placeholder="请选择"
                          style="width: 100px"
                        >
                          <el-option label="模糊搜索" :value="1" />
                          <el-option label="精确搜索" :value="2" />
                        </el-select>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item
                      :label="levelName + '种类'"
                      prop="Part_Type_Id"
                    >
                      <el-select
                        v-model="customParams.Part_Type_Id"
                        style="width: 100%"
                        placeholder="请选择"
                        clearable
                      >
                        <el-option
                          v-for="item in partTypeOption"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="规格" prop="Spec">
                      <el-input
                        v-model="customParams.Spec"
                        placeholder="请输入"
                        clearable
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item
                      label="材质"
                      prop="Texture"
                    >
                      <el-input
                        v-model="customParams.Texture"
                        placeholder="请输入"
                        clearable
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="6">
                    <el-form-item
                      label="操作人"
                      prop="DateName"
                    >
                      <el-input
                        v-model="customParams.DateName"
                        style="width: 100%"
                        placeholder="请输入"
                        clearable
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item
                      class="mb0"
                      label="批次"
                      prop="InstallUnit_Id"
                    >
                      <el-select
                        v-model="customParams.InstallUnit_Id"
                        multiple
                        filterable
                        clearable
                        placeholder="请选择"
                        style="width: 100%"
                        :disabled="!Boolean(customParams.Area_Id)"
                      >
                        <el-option
                          v-for="item in installUnitIdNameList"
                          :key="item.Id"
                          :label="item.Name"
                          :value="item.Id"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item
                      label="是否拼接"
                      prop="isMontage"
                    >
                      <el-select
                        v-model="customParams.isMontage"
                        style="width: 100%"
                        placeholder="请选择"
                        clearable
                      >
                        <el-option
                          v-for="item in montageOption"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item class="mb0" label-width="16px">
                      <el-button
                        type="primary"
                        @click="handelsearch()"
                      >搜索
                      </el-button>
                      <el-button @click="handelsearch('reset')">重置</el-button>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </div>
          <div class="fff cs-z-tb-wrapper">
            <div class="cs-button-box">
              <template>
                <!-- <el-dropdown trigger="click" placement="bottom-start" @command="handleCommand">
                  <el-button
                    type="primary"
                    :disabled="!currentLastLevel"
                  >零件导入
                    <i class="el-icon-arrow-down el-icon--right" />
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="cover">覆盖导入</el-dropdown-item>
                    <el-dropdown-item command="add">新增导入</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown> -->
                <!-- <el-button
                  type="primary"
                  @click="deepListImport"
                >零件导入</el-button>
                <el-button
                  :disabled="!selectList.length"
                  @click="modelListImport"
                  >导出零件排产单模板</el-button
                > -->
                <el-button
                  type="primary"
                  :disabled="!selectList.length || selectList.length !== 1 || selectList.some(item=>item.stopFlag)"
                  @click="partSplit"
                >{{ levelName }}拆分</el-button>
                <el-button
                  :disabled="!selectList.length"
                  @click="handleExport"
                >导出{{ levelName }}</el-button>
                <el-button
                  :disabled="!selectList.length || selectList.some(item=>item.stopFlag)"
                  type="primary"
                  plain
                  @click="handleBatchEdit"
                >批量编辑</el-button>
                <!-- <el-button
                  type="danger"
                  plain
                  :disabled="!selectList.length"
                  @click="handleDelete"
                >删除选中</el-button> -->
                <el-button
                  type="success"
                  plain
                  :disabled="!Boolean(customParams.Sys_Project_Id)"
                  @click="handelImport"
                >图纸导入
                </el-button>
              </template>
            </div>
            <div v-loading="countLoading" class="info-box">
              <div class="cs-col">
                <span><span class="info-label">深化总数</span><i>{{ SteelAmountTotal }} 件</i></span>
                <span><span class="info-label">深化总量</span><i>{{ SteelAllWeightTotal }}t</i></span>
              </div>
              <div class="cs-col">
                <span><span class="info-label">排产总数</span><i>{{ SchedulingNumTotal }} 件</i></span>
                <span><span class="info-label">排产总量</span><i>{{ SchedulingAllWeightTotal }} t</i></span>
              </div>
              <div class="cs-col" style="cursor: pointer;" @click="getProcessData()">
                <span><span class="info-label">完成总数</span><i>{{ FinishCountTotal }} 件</i></span>
                <span><span class="info-label">完成总量</span><i>{{ FinishWeightTotal }} t</i></span>
              </div>
            </div>
            <div class="tb-container">
              <vxe-table
                v-loading="tbLoading"
                :empty-render="{name: 'NotData'}"
                show-header-overflow
                element-loading-spinner="el-icon-loading"
                element-loading-text="拼命加载中"
                empty-text="暂无数据"
                class="cs-vxe-table"
                height="100%"
                align="left"
                stripe
                :data="tbData"
                resizable
                :tooltip-config="{ enterable: true }"
                @checkbox-all="tbSelectChange"
                @checkbox-change="tbSelectChange"
              >
                <vxe-column fixed="left" type="checkbox" width="44" />
                <vxe-column
                  v-for="(item, index) in columns"
                  :key="index"
                  :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
                  show-overflow="tooltip"
                  sortable
                  :align="item.Align"
                  :field="item.Code"
                  :title="item.Display_Name"
                  :width="item.Width ? item.Width : 120"
                >
                  <template #default="{ row }">
                    <div v-if="item.Code == 'Code'">
                      <el-tag v-if="row.Is_Change" style="margin-right: 8px;" type="danger">变</el-tag>
                      <el-tag v-if="row.stopFlag" style="margin-right: 8px;" type="danger">停</el-tag>
                      <el-link type="primary" @click="getPartInfo(row)"> {{ row[item.Code] | displayValue }}</el-link>
                    </div>
                    <div v-else-if="item.Code == 'Is_Component'">
                      <span>
                        <!--                      这列表叫是否非直发件 -->
                        <el-tag
                          v-if="row.Is_Component === 'True'"
                          type="danger"
                        >是</el-tag>
                        <el-tag v-else type="success">否</el-tag>
                      </span>
                    </div>
                    <div v-else-if="item.Code == 'Is_Split'">
                      <el-tag v-if="row.Is_Split === true">是</el-tag>
                      <el-tag v-else type="danger">否</el-tag>
                    </div>
                    <div v-else-if="item.Code == 'Deep_Material'">
                      <el-link
                        type="primary"
                        @click="handleDeepMaterial(row)"
                      >查看</el-link>
                    </div>
                    <div v-else-if="item.Code == 'Num' && row[item.Code] > 0">
                      <span v-if="row[item.Code]"> {{ row[item.Code] | displayValue }}件</span>
                      <span v-else>-</span>
                    </div>
                    <div
                      v-else-if="
                        item.Code == 'Schduling_Count' && row[item.Code] > 0
                      "
                    >
                      <el-link
                        v-if="row[item.Code]"
                        type="primary"
                        @click="handelSchduling(row)"
                      > {{ row[item.Code] | displayValue }}件</el-link>
                    </div>
                    <div v-else-if="item.Code == 'Is_Trace'">
                      <span>
                        <el-tag
                          v-if="row.Is_Trace"
                          type="success"
                        >是</el-tag>
                        <el-tag v-else type="danger">否</el-tag>
                      </span>
                    </div>
                    <div v-else-if="item.Code == 'Launch_Time'">
                      {{ row[item.Code] | timeFormat }}
                    </div>
                    <div v-else-if="item.Code == 'Drawing'">
                      <span
                        v-if="row.Drawing !== '暂无'"
                        style="color: #298dff; cursor: pointer"
                        @click="getPartInfo(row)"
                      > {{ row[item.Code] | displayValue }}
                      </span>
                      <span v-else> {{ row[item.Code] | displayValue }}</span>
                    </div>
                    <div v-else>
                      <span>{{ row[item.Code] !== undefined && row[item.Code] !== null ? row[item.Code] : "-" }}</span>
                    </div>
                  </template>
                </vxe-column>
                <vxe-column
                  fixed="right"
                  title="操作"
                  width="150"
                  show-overflow
                >
                  <template #default="{ row }">
                    <el-button
                      type="text"
                      @click="handleView(row)"
                    >详情</el-button>
                    <el-button
                      :disabled="row.stopFlag"
                      type="text"
                      @click="handleEdit(row)"
                    >编辑</el-button>
                    <el-button
                      type="text"
                      @click="handleTrack(row)"
                    >轨迹图
                    </el-button>
                  </template>
                </vxe-column>
              </vxe-table>
            </div>
            <div class="cs-bottom">
              <Pagination
                class="cs-table-pagination"
                :total="total"
                max-height="100%"
                :page-sizes="tablePageSize"
                :page.sync="queryInfo.Page"
                :limit.sync="queryInfo.PageSize"
                layout="total, sizes, prev, pager, next, jumper"
                @pagination="changePage"
              >
                <!--                <span class="pg-input">
                  <el-select
                    v-model.number="queryInfo.PageSize"
                    allow-create
                    filterable
                    default-first-option
                    @change="changePage"
                  >
                    <el-option
                      v-for="(item, index) in customPageSize"
                      :key="index"
                      :label="`${item}条/页`"
                      :value="item"
                    />
                  </el-select>
                </span>-->
              </Pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="card" />
    <el-dialog
      v-if="dialogVisible"
      ref="content"
      v-el-drag-dialog
      :title="title"
      :visible.sync="dialogVisible"
      :width="width"
      class="z-dialog"
      @close="handleClose"
    >
      <component
        :is="currentComponent"
        ref="content"
        :level-code="levelCode"
        :level-name="levelName"
        :select-list="selectList"
        :custom-params="customDialogParams"
        :type-id="customParams.TypeId"
        :type-entity="typeEntity"
        :project-id="customParams.Project_Id"
        :sys-project-id="customParams.Project_Id"
        @close="handleClose"
        @refresh="fetchData"
      />
    </el-dialog>
    <bimdialog
      ref="dialog"
      :type-entity="typeEntity"
      :area-id="customParams.Area_Id"
      :project-id="customParams.Project_Id"
      @getData="fetchData"
      @getTreeData="fetchTreeData"
    />

    <el-drawer
      :visible.sync="drawersull"
      direction="btt"
      size="100%"
      destroy-on-close
    >
      <iframe
        v-if="templateUrl"
        id="fullFrame"
        :src="templateUrl"
        frameborder="0"
        style="width: 96%; margin-left: 2%; height: 70vh; margin-top: 2%"
      />
    </el-drawer>

    <el-drawer
      :visible.sync="trackDrawer"
      direction="rtl"
      size="30%"
      destroy-on-close
      custom-class="trackDrawerClass"
    >
      <template #title>
        <div>
          <span>{{ trackDrawerTitle }}</span>
          <span style="margin-left: 24px">{{ trackDrawerData.Num }}</span>
        </div>
      </template>
      <TracePlot :track-drawer-data="trackDrawerData" />
    </el-drawer>

    <comDrawdialog ref="comDrawdialogRef" @getData="fetchData" />
    <modelDrawing ref="modelDrawingRef" type="零件" />
  </div>
</template>

<script>
import {
  Deletepart,
  GetPartWeightList,
  ExportPlanpartInfo,
  ExportPlanpartcountInfo,
  DeletepartByfindkeywodes
} from '@/api/plm/production'
import { GetPartPageList } from '@/api/plm/component'
import { GetGridByCode } from '@/api/sys'
import { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'
import {
  GetProjectAreaTreeList,
  GetInstallUnitIdNameList
} from '@/api/PRO/project'

import TreeDetail from '@/components/TreeDetail'
import TopHeader from '@/components/TopHeader'
import comImport from './component/Import'
import ComponentsHistory from './component/ComponentsHistory'
import comImportByFactory from './component/ImportByFactory'
import HistoryExport from './component/HistoryExport'
import BatchEdit from './component/BatchEditor'
import ComponentPack from './component/ComponentPack/index'
import Edit from './component/Edit'
import OneClickGeneratePack from './component/OneClickGeneratePack'
import GeneratePack from './component/GeneratePack'
import DeepMaterial from './component/DeepMaterial'
import Schduling from './component/Schduling'
import PartSplit from './component/PartSplit'
import ProcessData from './component/ProcessData.vue'

import elDragDialog from '@/directive/el-drag-dialog'
import Pagination from '@/components/Pagination'
import { timeFormat } from '@/filters'
// import { Column, Header, Table, Tooltip } from 'vxe-table'
// import Vue from 'vue'
import AuthButtons from '@/mixins/auth-buttons'
import bimdialog from './component/bimdialog'
import sysUseType from '@/directive/sys-use-type/index.js'
import { promptBox } from './component/messageBox'

import { combineURL } from '@/utils'
import { tablePageSize } from '@/views/PRO/setting'
import { parseOssUrl } from '@/utils/file'
import { GetPartTypeList } from '@/api/PRO/partType'
import { baseUrl } from '@/utils/baseurl'
import { v4 as uuidv4 } from 'uuid'
import { GetSteelCadAndBimId } from '@/api/PRO/component'
import { getConfigure } from '@/api/user'
import { GetFileType } from '@/api/sys'
import ExpandableSection from '@/components/ExpandableSection/index.vue'
import comDrawdialog from '@/views/PRO/production-order/deepen-files/dialog' // 深化文件-零件详图导入
import TracePlot from './component/TracePlot'
import { GetStopList } from '@/api/PRO/production-task'
import modelDrawing from '@/views/PRO/components/modelDrawing.vue'
import { GetBOMInfo } from '@/views/PRO/bom-setting/utils'
// Vue.use(Header).use(Column).use(Tooltip).use(Table)
const SPLIT_SYMBOL = '$_$'
export default {
  name: 'PROPartList',
  directives: { elDragDialog, sysUseType },
  components: {
    ExpandableSection,
    TreeDetail,
    TopHeader,
    comImport,
    comImportByFactory,
    BatchEdit,
    HistoryExport,
    GeneratePack,
    Edit,
    ComponentPack,
    OneClickGeneratePack,
    Pagination,
    bimdialog,
    ComponentsHistory,
    DeepMaterial,
    Schduling,
    comDrawdialog,
    TracePlot,
    PartSplit,
    modelDrawing,
    ProcessData
  },
  mixins: [AuthButtons],
  data() {
    return {
      allStopFlag: false,
      showExpand: true,
      drawer: false,
      drawersull: false,
      iframeKey: '',
      fullscreenid: '',
      iframeUrl: '',
      fullbimid: '',
      expandedKey: '', // -1是全部
      tablePageSize: tablePageSize,
      partTypeOption: [],
      treeData: [],
      treeLoading: true,
      projectName: '',
      statusType: '',
      searchHeight: 0,
      tbData: [],
      total: 0,
      tbLoading: false,
      pgLoading: false,
      countLoading: false,
      queryInfo: {
        Page: 1,
        PageSize: 10,
        ParameterJson: []
      },
      customPageSize: [10, 20, 50, 100],
      installUnitIdNameList: [], // 批次数组
      nameMode: 1,
      montageOption: [
        { value: true, label: '是' },
        { value: false, label: '否' }
      ],
      customParams: {
        TypeId: '',
        Type_Name: '',
        Code: '',
        Code_Like: '',
        Spec: '',
        DateName: '',
        Texture: '',
        // Keywords01: 'Code',
        // Keywords01Value: '',
        // Keywords02: 'Spec',
        // Keywords02Value: '',
        // Keywords03: 'Length',
        // Keywords03Value: '',
        // Keywords04: 'Texture',
        // Keywords04Value: '',
        isMontage: null,
        InstallUnit_Id: [],
        Part_Type_Id: '',
        InstallUnit_Name: '',
        Sys_Project_Id: '',
        Project_Id: '',
        Area_Id: '',
        Project_Name: '',
        Area_Name: ''
      },
      names: '',
      customDialogParams: {},
      dialogVisible: false,
      currentComponent: '',
      selectList: [],
      factoryOption: [],
      projectList: [],
      typeOption: [],
      columns: [],
      columnsOption: [
        // { Display_Name: '零件名称', Code: 'Code' },
        // { Display_Name: '规格', Code: 'Spec' },
        // { Display_Name: '长度', Code: 'Length' },
        // { Display_Name: '材质', Code: 'Texture' },
        // { Display_Name: '深化数量', Code: 'Num' },
        // { Display_Name: '排产数量', Code: 'Schduling_Count' },
        // { Display_Name: '单重', Code: 'Weight' },
        // { Display_Name: '总重', Code: 'Total_Weight' },
        // { Display_Name: '形状', Code: 'Shape' },
        // { Display_Name: '构件名称', Code: 'Component_Code' },
        // { Display_Name: '操作人', Code: 'datename' },
        // { Display_Name: '操作时间', Code: 'Exdate' }
      ],
      title: '',
      width: '60%',
      tipLabel: '',
      monomerList: [],
      mode: '',
      isMonomer: true,
      historyVisible: false,
      sysUseType: undefined,
      deleteContent: true,
      SteelAmountTotal: 0, // 深化总量
      SchedulingNumTotal: 0, // 排产总量
      SteelAllWeightTotal: 0, // 深化总重
      SchedulingAllWeightTotal: 0, // 排产总重
      FinishCountTotal: 0, // 完成数量
      FinishWeightTotal: 0, // 完成重量
      Unit: '',
      Proportion: 0, // 专业的单位换算
      command: 'cover',
      currentLastLevel: false,
      templateUrl: '',
      currentNode: {},
      comDrawData: {},
      trackDrawer: false,
      trackDrawerTitle: '',
      trackDrawerData: {},
      levelName: '',
      levelCode: ''
    }
  },
  computed: {
    showP9Btn() {
      return this.AuthButtons.buttons.some((item) => item.Code === 'p9BtnAdd')
    },
    typeEntity() {
      return this.typeOption.find((i) => i.Id === this.customParams.TypeId)
    },
    PID() {
      return this.projectList.find(
        (i) => i.Sys_Project_Id === this.customParams.Project_Id
      )?.Id
    },
    filterText() {
      return this.projectName + SPLIT_SYMBOL + this.statusType
    }
  },
  watch: {
    'customParams.TypeId': function(newValue, oldValue) {
      console.log({ oldValue })
      if (oldValue && oldValue !== '0') {
        this.fetchData()
      }
    },
    names(n, o) {
      this.changeMode()
    },
    nameMode(n, o) {
      this.changeMode()
    }
  },
  mounted() {

  },
  async created() {
    const { currentBOMInfo } = await GetBOMInfo(0)
    console.log('list', currentBOMInfo)
    this.levelName = currentBOMInfo?.Display_Name
    this.levelCode = currentBOMInfo?.Code
    await this.getTypeList()
    // await this.fetchData()
    await this.getTableConfig('plm_parts_page_list')

    this.fetchTreeData()
    this.getFileType()
    if (this.Keywords01Value === '是') {
      console.log('this.Keywords01Value', this.Keywords01Value)
    }
    this.$nextTick(() => {
      this.pgLoading = true
      console.log(this.columns)
      this.getPartWeightList()
      this.getPartType()
      this.searchHeight = this.$refs.searchDom.offsetHeight + 327
    })
  },
  methods: {
    changeMode() {
      if (this.nameMode === 1) {
        this.customParams.Code_Like = this.names
        this.customParams.Code = ''
      } else {
        this.customParams.Code_Like = ''
        this.customParams.Code = this.names.replace(/\s+/g, '\n')
      }
    },
    // 项目区域数据集
    fetchTreeData() {
      GetProjectAreaTreeList({ MenuId: this.$route.meta.Id, Type: 0, projectName: this.projectName, Level: this.levelCode }).then((res) => {
        // const resAll = [
        //   {
        //     ParentNodes: null,
        //     Id: '-1',
        //     Code: '全部',
        //     Label: '全部',
        //     Level: null,
        //     Data: {},
        //     Children: []
        //   }
        // ]
        // const resData = resAll.concat(res.Data)
        if (res.Data.length === 0) {
          this.treeLoading = false
          this.pgLoading = false
          return
        }
        const resData = res.Data
        resData.map((item) => {
          if (item.Children.length === 0) {
            item.Is_Imported = false
          } else {
            item.Data.Is_Imported = item.Children.some((ich) => {
              return ich.Data.Is_Imported === true
            })
            item.Is_Directory = true
            item.Children.map((it) => {
              if (it.Children.length > 0) {
                it.Is_Directory = true
              }
            })
          }
        })
        this.treeData = resData
        if (Object.keys(this.currentNode).length === 0) {
          // this.fetchData()
          this.setKey()
        } else {
          this.handleNodeClick(this.currentNode)
        }
        this.treeLoading = false
        // this.expandedKey = this.customParams.Area_Id ? this.customParams.Area_Id : this.customParams.Project_Id ? this.customParams.Project_Id : resData[0].Id // '-1'
        // this.customParams.Sys_Project_Id = this.customParams.Sys_Project_Id || resData[0].Data.Sys_Project_Id
        // this.customParams.Project_Id = this.customParams.Project_Id || resData[0].Data.Id
        // this.customParams.Area_Name = ''
        // this.treeLoading = false
        // this.fetchData()
      })
    },
    // 设置默认选中第一个区域末级节点
    setKey() {
      const deepFilter = (tree) => {
        for (let i = 0; i < tree.length; i++) {
          const item = tree[i]
          const { Data, Children } = item
          console.log(Data)
          if (Data.ParentId && !Children?.length) {
            console.log(Data, '????')
            this.currentNode = Data
            this.handleNodeClick(item)
            return
          } else {
            if (Children && Children.length > 0) {
              return deepFilter(Children)
            } else {
              this.handleNodeClick(item)
              return
            }
          }
        }
      }
      return deepFilter(this.treeData)
    },
    // 选中左侧项目节点
    handleNodeClick(data) {
      this.handelsearch('reset', false)
      this.currentNode = data
      this.expandedKey = data.Id
      const dataId = data.Id === '-1' ? '' : data.Id
      console.log('nodeData', data)
      if (data.ParentNodes) {
        this.customParams.Project_Id = data.Data.Project_Id
        this.customParams.Area_Id = data.Id
        this.customParams.Area_Name = data.Data.Name
        this.customParams.Sys_Project_Id = data.Data.Sys_Project_Id
      } else {
        this.customParams.Project_Id = dataId
        this.customParams.Area_Id = ''
        this.customParams.Area_Name = data.Data.Name
        this.customParams.Sys_Project_Id = data.Data.Sys_Project_Id
      }
      console.log(
        this.customParams.Sys_Project_Id,
        'this.customParams.Sys_Project_Id============11111'
      )
      console.log(
        this.customParams.Area_Id,
        'this.customParams.Area_Id============11111'
      )
      this.currentLastLevel = !!(data.Data.Level && data.Children.length === 0)
      if (this.currentLastLevel) {
        this.customParams.Project_Name = data.Data?.Project_Name
        this.customParams.Area_Name = data.Label
      }
      this.queryInfo.Page = 1
      this.pgLoading = true
      this.fetchList()
      console.log(this.customParams.Area_Id)
      this.getInstallUnitIdNameList(dataId, data)
      this.getPartWeightList()
    },

    // 获取批次
    getInstallUnitIdNameList(id, data) {
      if (id === '' || data.Children.length > 0) {
        this.installUnitIdNameList = []
      } else {
        GetInstallUnitIdNameList({ Area_Id: id }).then((res) => {
          this.installUnitIdNameList = res.Data
        })
      }
    },
    // 工序完成量
    getProcessData() {
      const customParamsData = JSON.parse(JSON.stringify(this.customParams))
      const InstallUnit_Ids = customParamsData.InstallUnit_Id.join(',')
      delete customParamsData.InstallUnit_Id

      this.width = '40%'
      this.generateComponent(`${this.levelName}工序完成量`, 'ProcessData')
      this.$nextTick((_) => {
        this.$refs['content'].init(customParamsData, InstallUnit_Ids, this.selectList.map((v) => v.Part_Aggregate_Id).toString())
      })
    },
    getTableConfig(code) {
      return new Promise((resolve) => {
        GetGridByCode({
          code:
            code +
            ',' +
            this.typeOption.find((i) => i.Id === this.customParams.TypeId).Code
        }).then((res) => {
          const { IsSucceed, Data, Message } = res
          if (IsSucceed) {
            if (!Data) {
              this.$message.error('当前专业没有配置相对应表格')
              this.tbLoading = true
              return
            }
            this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)
            const list = Data.ColumnList || []
            const sortList = list.sort((a, b) => a.Sort - b.Sort)
            this.columns = sortList
              .filter((v) => v.Is_Display)
              .map((item) => {
                if (item.Code === 'Code') {
                  item.fixed = 'left'
                }

                return item
              })
            this.queryInfo.PageSize = +Data.Grid.Row_Number || 20
            resolve(this.columns)
            console.log(this.columns)
            const selectOption = JSON.parse(JSON.stringify(this.columns))
            console.log(selectOption)
            this.columnsOption = selectOption.filter((v) => {
              return (
                v.Display_Name !== '操作时间' &&
                v.Display_Name !== '模型ID' &&
                v.Display_Name !== '深化资料' &&
                v.Display_Name !== '备注' &&
                v.Display_Name !== '排产数量' &&
                v.Code.indexOf('Attr') === -1 &&
                v.Display_Name !== '批次'
              )
            })
          } else {
            this.$message({
              message: Message,
              type: 'error'
            })
          }
        })
      })
    },
    async fetchList() {
      const customParamsData = JSON.parse(JSON.stringify(this.customParams))
      const InstallUnit_Ids = customParamsData.InstallUnit_Id.join(',')
      delete customParamsData.InstallUnit_Id
      await GetPartPageList({
        ...this.queryInfo,
        ...customParamsData,
        Code: customParamsData.Code.trim().replaceAll(' ', '\n'),
        InstallUnit_Ids: InstallUnit_Ids
      })
        .then((res) => {
          if (res.IsSucceed) {
            this.queryInfo.PageSize = res.Data.PageSize
            this.total = res.Data.TotalCount
            this.tbData = res.Data.Data.map((v) => {
              v.Is_Main = v.Is_Main ? '是' : '否'
              v.Exdate = timeFormat(v.Exdate, '{y}-{m}-{d} {h}:{i}:{s}')
              // console.log(v)
              return v
            })
            this.selectList = []
            this.getStopList()
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
        .finally(() => {
          this.tbLoading = false
          this.pgLoading = false
        })
    },
    async getStopList() {
      const submitObj = this.tbData.map(item => {
        return {
          Id: item.Part_Aggregate_Id,
          Type: 1,
          Bom_Level: this.levelCode
        }
      })
      await GetStopList(submitObj).then(res => {
        if (res.IsSucceed) {
          const stopMap = {}
          res.Data.forEach(item => {
            stopMap[item.Id] = item.Is_Stop !== null
          })
          this.tbData.forEach(row => {
            if (stopMap[row.Part_Aggregate_Id]) {
              this.$set(row, 'stopFlag', stopMap[row.Part_Aggregate_Id])
            }
          })
        }
      })
    },
    async fetchData() {
      console.log('更新列表')
      // 分开获取，提高接口速度
      await this.getTableConfig('plm_parts_page_list')
      this.tbLoading = true
      this.fetchList().then((res) => {
        this.tbLoading = false
      })
    },
    async changePage() {
      this.tbLoading = true
      if (
        typeof this.queryInfo.PageSize !== 'number' ||
        this.queryInfo.PageSize < 1
      ) {
        this.queryInfo.PageSize = 10
      }
      this.fetchList().then((res) => {
        this.tbLoading = false
      })
    },
    // tbSelectChange(array) {
    //   console.log('array', array)
    //   this.selectList = array.records
    //   console.log('this.selectList', this.selectList)
    // },
    getTbData(data) {
      const { YearAllWeight, YearSteel, CountInfo } = data
      // this.tipLabel = `累计上传构件${YearSteel}件，总重${YearAllWeight}t。`
      this.tipLabel = CountInfo
    },
    async getTypeList() {
      let res = null
      let data = null
      res = await GetFactoryProfessionalByCode({
        factoryId: localStorage.getItem('CurReferenceId')
      })
      data = res.Data
      if (res.IsSucceed) {
        this.typeOption = Object.freeze(data)
        if (this.typeOption.length > 0) {
          this.Proportion = data[0].Proportion
          this.Unit = data[0].Unit
          this.customParams.TypeId = this.typeOption[0]?.Id
          this.customParams.Type_Name = this.typeOption[0]?.Name
        }
      } else {
        this.$message({
          message: res.Message,
          type: 'error'
        })
      }
    },
    handleDelete() {
      this.$confirm('此操作将删除选择数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          Deletepart({
            ids: this.selectList.map((v) => v.Part_Aggregate_Id).toString()
          }).then((res) => {
            if (res.IsSucceed) {
              this.fetchData()
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.getPartWeightList()
              this.fetchTreeData()
            } else {
              this.$message({
                message: res.Message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    handleEdit(row) {
      this.width = '45%'
      this.generateComponent(`编辑${this.levelName}`, 'Edit')
      this.$nextTick((_) => {
        row.isReadOnly = false
        this.$refs['content'].init(row)
      })
    },
    handleBatchEdit() {
      const SchedulArr = this.selectList.filter((item) => {
        return item.Schduling_Count != null && item.Schduling_Count > 0
      })
      if (SchedulArr.length > 0) {
        this.$message({
          type: 'error',
          message: `选中行包含已排产的${this.levelName},编辑信息需要进行变更操作`
        })
      } else {
        this.width = '40%'
        this.generateComponent('批量编辑', 'BatchEdit')
        this.$nextTick((_) => {
          this.$refs['content'].init(this.selectList, this.columnsOption)
        })
      }
    },
    handleView(row) {
      this.width = '45%'
      this.generateComponent('详情', 'Edit')
      this.$nextTick((_) => {
        row.isReadOnly = true
        this.$refs['content'].init(row)
      })
    },
    async handleExport() {
      const obj = {
        Part_Aggregate_Ids: this.selectList
          .map((v) => v.Part_Aggregate_Id)
          .toString(),
        ProfessionalCode: this.typeEntity.Code
      }
      ExportPlanpartInfo(obj).then((res) => {
        if (res.IsSucceed) {
          window.open(combineURL(this.$baseUrl, res.Data), '_blank')
        } else {
          this.$message.error(res.Message)
        }
      })
    },
    // 零件拆分
    partSplit() {
      const Type_Name = this.selectList[0].Type_Name
      const Schduling_Count = this.selectList[0].Schduling_Count
      if (Type_Name === '直发件' || Schduling_Count > 0) {
        this.$message({
          type: 'error',
          message: `${this.levelName}已排产或是直发件,无法拆分`
        })
        return
      }
      this.width = '45%'
      this.generateComponent(`${this.levelName}拆分`, 'PartSplit')
      this.$nextTick((_) => {
        this.$refs['content'].init(this.selectList)
      })
    },
    modelListImport() {
      const obj = {
        Part_Aggregate_Ids: this.selectList
          .map((v) => v.Part_Aggregate_Id)
          .toString(),
        ProfessionalCode: this.typeEntity.Code
      }
      ExportPlanpartcountInfo(obj).then((res) => {
        if (res.IsSucceed) {
          window.open(combineURL(this.$baseUrl, res.Data), '_blank')
          if (res.Message) {
            this.$alert(res.Message, '导出通知', {
              confirmButtonText: '我知道了'
            })
          }
        } else {
          this.$message.error(res.Message)
        }
      })
    },
    // 覆盖导入 or 新增导入
    handleCommand(command) {
      console.log(command, 'command')
      this.command = command
      this.deepListImport()
    },
    deepListImport() {
      const fileType = {
        Catalog_Code: 'PLMDeepenFiles',
        Code: this.typeEntity.Code,
        name: this.typeEntity.Name
      }
      this.$refs.dialog.handleOpen(
        'add',
        fileType,
        null,
        true,
        this.PID,
        this.command,
        this.customParams
      )
    },
    async handleAllDelete() {
      console.log(this.customParams.Project_Id)
      if (this.customParams.Project_Id) {
        await promptBox({ title: '删除' })
        await DeletepartByfindkeywodes({
          ...this.customParams,
          ...this.queryInfo
        }).then((res) => {
          if (res.IsSucceed) {
            this.$message.success('删除成功')
            this.fetchData()
            this.fetchTreeData()
          } else {
            this.$message.error(res.Message)
          }
        })
      } else {
        this.$message.warning('请先选择项目')
      }
    },
    handleClose() {
      this.dialogVisible = false
    },
    generateComponent(title, component) {
      this.title = title
      this.currentComponent = component
      this.dialogVisible = true
    },
    // 点击搜索
    handelsearch(reset, hasSearch = true) {
      this.deleteContent = false
      if (reset) {
        this.$refs.customParams.resetFields()
        this.deleteContent = true
        this.names = ''
      }
      hasSearch && this.fetchData()
      this.getPartWeightList()
    },
    // 深化资料查看
    handleDeepMaterial(row) {
      console.log('handleDeepMaterial')
      this.width = '45%'
      this.generateComponent('查看深化资料', 'DeepMaterial')
      this.$nextTick((_) => {
        row.isReadOnly = false
        this.$refs['content'].init(row)
      })
    },
    // 排产数量
    handelSchduling(row) {
      this.width = '45%'
      this.generateComponent('生产详情', 'Schduling')
      this.$nextTick((_) => {
        row.isReadOnly = false
        this.$refs['content'].init(row)
      })
    },
    // 零件排产信息
    getPartWeightList() {
      this.countLoading = true
      const customParamsData = JSON.parse(JSON.stringify(this.customParams))
      const InstallUnit_Ids = customParamsData.InstallUnit_Id.join(',')
      delete customParamsData.InstallUnit_Id
      GetPartWeightList({
        ...this.queryInfo,
        ...customParamsData,
        InstallUnit_Ids
      }).then((res) => {
        if (res.IsSucceed) {
          this.SteelAmountTotal = Math.round(res.Data.DeepenNum * 1000) / 1000 // 深化总量
          this.SchedulingNumTotal =
            Math.round(res.Data.SchedulingNum * 1000) / 1000 // 排产总量
          this.SteelAllWeightTotal =
            Math.round(res.Data.DeepenWeight * 1000) / 1000 // 深化总重
          this.SchedulingAllWeightTotal =
            Math.round(res.Data.SchedulingWeight * 1000) / 1000 // 排产总重
          this.FinishCountTotal =
            Math.round(res.Data.Finish_Count * 1000) / 1000 // 完成总数
          this.FinishWeightTotal =
            Math.round(res.Data.Finish_Weight * 1000) / 1000 // 完成总重
          console.log(' this.SteelAllWeightTotal', this.SteelAllWeightTotal)
        } else {
          this.$message.error(res.Message)
        }
        this.countLoading = false
      })
    },
    tbSelectChange(array) {
      this.selectList = array.records
      this.SteelAmountTotal = 0
      this.SchedulingNumTotal = 0
      this.SteelAllWeightTotal = 0
      this.SchedulingAllWeightTotal = 0
      this.FinishCountTotal = 0
      this.FinishWeightTotal = 0
      let SteelAllWeightTotalTemp = 0
      let SchedulingAllWeightTotalTemp = 0
      let FinishWeightTotalTemp = 0
      if (this.selectList.length > 0) {
        this.selectList.forEach((item) => {
          const schedulingNum =
            item.Schduling_Count == null ? 0 : item.Schduling_Count
          this.SteelAmountTotal += item.Num
          this.SchedulingNumTotal += Number(item.Schduling_Count)
          this.FinishCountTotal += item.Finish_Count
          SteelAllWeightTotalTemp += item.Total_Weight
          SchedulingAllWeightTotalTemp += item.Weight * schedulingNum
          FinishWeightTotalTemp += item.Finish_Weight
        })
        this.SteelAllWeightTotal =
          Math.round((SteelAllWeightTotalTemp / this.Proportion) * 1000) / 1000
        this.SchedulingAllWeightTotal =
          Math.round((SchedulingAllWeightTotalTemp / this.Proportion) * 1000) /
          1000
        this.FinishWeightTotal =
          Math.round((FinishWeightTotalTemp / this.Proportion) * 1000) / 1000
      } else {
        this.getPartWeightList()
      }
    },
    fetchTreeDataLocal() {
      // this.filterText = this.projectName
    },
    getPartInfo(row) {
      const drawingData = row.Drawing ? row.Drawing.split(',') : [] // 图纸数据
      const fileUrlData = row.File_Url ? row.File_Url.split(',') : [] // 图纸数据文件地址数据
      if (fileUrlData.length === 0) {
        this.$message({
          message: `当前${this.levelName}无图纸`,
          type: 'warning'
        })
        return
      }
      if (drawingData.length > 0 && fileUrlData.length > 0) {
        this.drawingActive = drawingData[0]
      }
      if (drawingData.length > 0 && fileUrlData.length > 0) {
        this.drawingDataList = drawingData.map((item, index) => ({
          name: item,
          label: item,
          url: fileUrlData[index]
        }))
      }
      this.getPartInfoDrawing(row)
    },

    getPartInfoDrawing(row) {
      const importDetailId = row.Part_Aggregate_Id
      GetSteelCadAndBimId({ importDetailId: importDetailId }).then((res) => {
        if (res.IsSucceed) {
          const drawingData = {
            'extensionName': res.Data[0].ExtensionName,
            'fileBim': res.Data[0].fileBim,
            'IsUpload': res.Data[0].IsUpload,
            'Code': row.Code,
            'Sys_Project_Id': row.Sys_Project_Id
          }
          this.$refs.modelDrawingRef.dwgInit(drawingData)
        }
      })
    },

    /*    handleViewDwg(row) {
      if (!row.File_Url) {
        this.$message({
          message: '当前零件无图纸',
          type: 'warning'
        })
        return
      }
      window.open('http://dwgv1.bimtk.com:5432/?CadUrl=' + parseOssUrl(row.File_Url), '_blank')
    },*/
    customFilterFun(value, data, node) {
      const arr = value.split(SPLIT_SYMBOL)
      const labelVal = arr[0]
      const statusVal = arr[1]
      if (!value) return true
      let parentNode = node.parent
      let labels = [node.label]
      let status = [
        data.Data.Is_Deepen_Change
          ? '已变更'
          : data.Data.Is_Imported
            ? '已导入'
            : '未导入'
      ]
      let level = 1
      while (level < node.level) {
        labels = [...labels, parentNode.label]
        status = [
          ...status,
          data.Data.Is_Deepen_Change
            ? '已变更'
            : data.Data.Is_Imported
              ? '已导入'
              : '未导入'
        ]
        parentNode = parentNode.parent
        level++
      }
      labels = labels.filter((v) => !!v)
      status = status.filter((v) => !!v)
      let resultLabel = true
      let resultStatus = true
      if (this.statusType) {
        resultStatus = status.some((s) => s.indexOf(statusVal) !== -1)
      }
      if (this.projectName) {
        resultLabel = labels.some((s) => s.indexOf(labelVal) !== -1)
      }
      return resultLabel && resultStatus
    },
    getPartType() {
      GetPartTypeList({ Part_Grade: 0 }).then((res) => {
        if (res.IsSucceed) {
          this.partTypeOption = res.Data.map((v) => {
            return {
              label: v.Name,
              value: v.Id
            }
          })
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    async getFileType() {
      const params = {
        catalogCode: 'PLMDeepenFiles'
      }
      const res = await GetFileType(params)
      // 获取构件详图
      const data = res.Data.find((v) => v.Label === '零件详图')

      this.comDrawData = {
        isSHQD: false,
        Id: data.Id,
        name: data.Label,
        Catalog_Code: data.Code,
        Code: data.Data?.English_Name
      }

      console.log(this.comDrawData, 'comDrawData')
    },
    // 图纸导入
    handelImport() {
      this.$refs.comDrawdialogRef.handleOpen(
        'add',
        this.comDrawData,
        '',
        false,
        this.customParams.Sys_Project_Id,
        false
      )
    },
    // 轨迹图
    handleTrack(row) {
      console.log(row, 'row')
      this.trackDrawer = true
      this.trackDrawerTitle = row.Code
      this.trackDrawerData = row
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
@import "~@/styles/tabs.scss";
.min900 {
  min-width: 900px;
  overflow: auto;
}
.z-dialog {
  ::v-deep {
    .el-dialog__header {
      background-color: #298dff;

      .el-dialog__title,
      .el-dialog__close {
        color: #ffffff;
      }
    }

    .el-dialog__body {
      // max-height: 750px;
      overflow: auto;
      @include scrollBar;

      &::-webkit-scrollbar {
        width: 8px;
      }
    }
  }
}

.container {
  padding: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.tb-container {
  padding: 0 16px 0 16px;
  flex: 1;
  height: 0; //解决溢出问题
  // .vxe-table {
  //   height: calc(100%);
  // }
}

.cs-z-tb-wrapper {
  display: flex;
  flex-direction: column;
  height: 0; //解决溢出问题
}

.cs-bottom {
  padding: 8px 16px 8px 16px;
  position: relative;
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;

  .data-info {
    .info-x {
      margin-right: 20px;
    }
  }
  .pg-input {
    width: 100px;
    margin-right: 20px;
  }
}

.pagination-container {
  text-align: right;
  margin: 0;
  padding: 0;
  ::v-deep .el-input--small .el-input__inner {
    height: 28px;
    line-height: 28px;
  }
}

.cs-from {
  background-color: #ffffff;
  border-radius: 4px;
  margin-bottom: 16px;
  padding: 16px 16px 0 16px;
  display: flex;
  font-size: 14px;
  color: rgba(34, 40, 52, 0.65);
  label {
    display: inline-block;
    margin-right: 20px;
    white-space: nowrap;
    vertical-align: top;
  }
  .cs-from-title {
    flex: 1;
  }

  .mb0 {
    margin-bottom: 0;

    ::v-deep {
      .el-form-item {
        margin-bottom: 0
      }
    }
  }

  .cs-search {
    width: 100%;
    label {
      margin-bottom: 10px;
    }
    button {
      margin-right: 10px;
      margin-left: 0;
      margin-bottom: 10px;
    }
  }
}

.input-with-select {
  width: 250px;
}

.cs-button-box {
  padding: 16px 16px 6px 16px;
  position: relative;
  background-color: #ffffff;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;

  ::v-deep .el-button {
    margin-left: 0 !important;
    margin-right: 10px !important;
    margin-bottom: 10px !important;
  }
}
.info-box {
  margin: 0 16px 16px 16px;
  display: flex;
  justify-content: center;
  font-size: 14px;
  height: 64px;
  background: rgba(41, 141, 255, 0.05);

  .cs-col {
    display: flex;
    justify-content: space-evenly;
    flex-direction: column;
    margin-right: 64px;
  }

  .info-label {
    color: #999999;
  }

  i {
    color: #00c361;
    font-style: normal;
    font-weight: 600;
    margin-left: 10px;
  }
}

::v-deep .el-tree-node {
  min-width: 240px;
  width: min-content;
}
::v-deep .el-tree-node > .el-tree-node__children {
  overflow: inherit;
}

.stretch-btn {
  position: absolute;
  width: 20px;
  height: 130px;
  top: calc((100% - 130px) / 2);

  display: flex;
  align-items: center;
  background: #eff1f3;
  cursor: pointer;
  .center-btn {
    width: 14px;
    height: 100px;
    border-radius: 0 9px 9px 0;
    background-color: #8c95a8;
    > i {
      line-height: 100px;
      text-align: center;
      color: #fff;
    }
  }
}
.cs-left {
  position: relative;
  margin-right: 20px;
  .inner-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px 10px 16px 16px;
    border-radius: 4px;
    overflow: hidden;

    .tree-search {
      display: flex;

      .search-select {
        margin-right: 8px;
      }
    }

    .tree-x {
      overflow: hidden;
      margin-top: 16px;
      flex: 1;

      .cs-scroll {
        overflow-y: auto;
        @include scrollBar;
      }

      .el-tree {
        height: 100%;

        //::v-deep {
        //  .el-tree-node {
        //    min-width: 240px;
        //    width: min-content;
        //
        //    .el-tree-node__children {
        //      overflow: inherit;
        //    }
        //  }
        //}
      }
    }
  }
}
.cs-left-contract {
  padding-left: 0;
  position: relative;
  width: 20px;
  margin-right: 26px;
}
.cs-right {
  padding-right: 0;
  flex: 1;
  width: 0;
}
* {
  box-sizing: border-box;
}
.fourGreen {
  color: #00c361;
  font-style: normal;
}

.fourOrange {
  color: #ff9400;
  font-style: normal;
}

.fourRed {
  color: #ff0000;
  font-style: normal;
}

.cs-blue {
  color: #5ac8fa;
}

.orangeBg {
  background: rgba(255, 148, 0, 0.1);
}

.redBg {
  background: rgba(252, 107, 127, 0.1);
}
.greenBg {
  background: rgba(0, 195, 97, 0.1);
}

.cs-tag {
  margin-left: 8px;
  font-size: 12px;
  padding: 2px 4px;
  border-radius: 1px;
}
.cs-tree-x {
  ::v-deep {
    .el-select {
      width: 100%;
    }
  }
}
.cs-divider {
  margin: 16px 0 0 0;
}
</style>

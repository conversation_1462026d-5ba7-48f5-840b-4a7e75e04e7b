{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\component-list\\v4\\component\\BatchEditor.vue?vue&type=style&index=0&id=74c80ace&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\component-list\\v4\\component\\BatchEditor.vue", "mtime": 1758266753081}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCltjbGFzc149ImVsLWljb24iXSB7DQogIGZvbnQtc2l6ZTogMjRweDsNCiAgdmVydGljYWwtYWxpZ246IG1pZGRsZTsNCiAgY3Vyc29yOiBwb2ludGVyOw0KICBtYXJnaW4tbGVmdDogMTVweDsNCn0NCg0KLml0ZW0teCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQogIGZsZXg6IDAgMSA1MCU7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCg0KICAuaXRlbSB7DQogICAgd2lkdGg6IDQ1JTsNCiAgICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KICAgICY6bm90KDpmaXJzdC1vZi10eXBlKSB7DQogICAgICBtYXJnaW4tbGVmdDogMjBweDsNCiAgICAgIC5jcy1udW1iZXItYnRuLWhpZGRlbiwuZWwtaW5wdXQsLmVsLXNlbGVjdHsNCiAgICAgICAgd2lkdGg6IDgwJTsNCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAuaXRlbS1zcGFuIHsNCiAgICB3aWR0aDogOTBweDsNCiAgICBwYWRkaW5nLXRvcDogNXB4Ow0KICB9DQoNCn0NCiAgOjp2LWRlZXB7DQogICAgLmVsLXRyZWUtc2VsZWN0LWlucHV0ew0KICAgICAgd2lkdGg6IDgwJSFpbXBvcnRhbnQ7DQogICAgfQ0KICB9DQo="}, {"version": 3, "sources": ["BatchEditor.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiUA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "BatchEditor.vue", "sourceRoot": "src/views/PRO/component-list/v4/component", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-row v-for=\"(info,index) in list\" :key=\"info.id\" class=\"item-x\">\r\n      <div class=\"item\">\r\n        <label>\r\n          属性名称\r\n          <el-select\r\n            v-model=\"info.key\"\r\n            style=\"width: calc(100% - 65px)\"\r\n            clearable\r\n            placeholder=\"请选择\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in filterOption(info.key)\"\r\n              :key=\"item.key\"\r\n              :label=\"item.label\"\r\n              :value=\"item.key \"\r\n            />\r\n          </el-select>\r\n        </label>\r\n      </div>\r\n      <div class=\"item\" style=\"line-height: 32px;\">\r\n        <label>请输入值\r\n          <el-input-number v-if=\"checkType(info.key,'number')\" v-model=\"info.val\" :min=\"0\" class=\"cs-number-btn-hidden\" />\r\n          <el-input v-if=\"checkType(info.key,'string')\" v-model=\"info.val\" />\r\n          <el-select\r\n            v-if=\"checkType(info.key,'array') && info.key==='Is_Component'\"\r\n            v-model=\"info.val\"\r\n            clearable\r\n            placeholder=\"请选择\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in Is_Component_Data\"\r\n              :key=\"item.Id\"\r\n              :label=\"item.Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n          <el-tree-select\r\n            v-show=\"checkType(info.key,'array') && info.key==='SteelType'\"\r\n            ref=\"treeSelect\"\r\n            v-model=\"info.val\"\r\n            :tree-params=\"treeParams\"\r\n            style=\"width: 100%;display: inline-block\"\r\n            @node-click=\"steelTypeChange\"\r\n          />\r\n        </label>\r\n      </div>\r\n      <span v-if=\"index === 0\" class=\"item-span\">\r\n        <i class=\"el-icon-circle-plus-outline\" @click=\"handleAdd\" />\r\n      </span>\r\n      <span v-else class=\"item-span\">\r\n        <i class=\"el-icon-circle-plus-outline\" @click=\"handleAdd\" />\r\n        <i class=\"el-icon-remove-outline txt-red\" @click=\"handleDelete(index)\" />\r\n      </span>\r\n    </el-row>\r\n    <div style=\"text-align: right;width: 100%;padding: 20px 2% 0 0\">\r\n      <el-button @click=\"$emit('close')\">取消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"onSubmit\">确定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { BatchUpdateComponentImportInfo } from '@/api/PRO/component'\r\nimport { GetCompTypeTree } from '@/api/PRO/component-type'\r\nimport { GetUserableAttr } from '@/api/PRO/professionalType'\r\n\r\nexport default {\r\n  props: {\r\n    typeEntity: {\r\n      type: Object,\r\n      default: () => {}\r\n    },\r\n    projectId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    sysProjectId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      treeParams: {\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      },\r\n      Is_Component_Data: [{ Name: '是', Id: '否' }, { Name: '否', Id: '是' }], // 是否是直发件数据\r\n      Is_Component: '',\r\n      value: '',\r\n      options: [\r\n        {\r\n          key: 'SetupPosition',\r\n          label: '批次',\r\n          type: 'string'\r\n        },\r\n        {\r\n          key: 'SteelSpec',\r\n          label: '规格',\r\n          type: 'string'\r\n        }, {\r\n          key: 'SteelWeight',\r\n          label: '单重',\r\n          type: 'number'\r\n        }, {\r\n          key: 'SteelLength',\r\n          label: '长度',\r\n          type: 'number'\r\n        }, {\r\n          key: 'SteelAmount',\r\n          label: '深化数量',\r\n          type: 'number'\r\n        }, {\r\n          key: 'SteelMaterial',\r\n          label: '材质 ',\r\n          type: 'string'\r\n        }, {\r\n          key: 'Is_Component',\r\n          label: '是否直发件',\r\n          type: 'array'\r\n        }, {\r\n          key: 'Remark',\r\n          label: '备注',\r\n          type: 'string'\r\n        },\r\n        {\r\n          key: 'GrossWeight',\r\n          label: '单毛重',\r\n          type: 'number'\r\n        }\r\n      ],\r\n      list: [{\r\n        id: uuidv4(),\r\n        val: undefined,\r\n        key: ''\r\n      }]\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.getCompTypeTree()\r\n    await this.getUserableAttr()\r\n    const codeArr = this.options.filter((item, index) => index).map(i => i.key)\r\n    const columns = await this.convertCode(this.typeEntity.Code, codeArr)\r\n    this.options = this.options.map((item, index) => {\r\n      if (index) {\r\n        item.label = columns.filter((v) => v.Is_Display).find(i => i.Code === item.key)?.Display_Name\r\n      }\r\n      return item\r\n    })\r\n  },\r\n  methods: {\r\n    // 获取拓展字段\r\n    async getUserableAttr() {\r\n      await GetUserableAttr({\r\n        IsComponent: true,\r\n        Bom_Level: -1\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const resData = res.Data\r\n          const expandData = []\r\n          resData.forEach(item => {\r\n            const expandJson = {}\r\n            expandJson.key = item.Code\r\n            expandJson.lable = item.Display_Name\r\n            expandJson.type = 'string'\r\n            expandData.push(expandJson)\r\n          })\r\n          this.options = this.options.concat(expandData)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 获取构件类型\r\n    async getCompTypeTree() {\r\n      await GetCompTypeTree({\r\n        professional: this.typeEntity.Code\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$refs.treeSelect.forEach(item => {\r\n            item.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.treeData = []\r\n        }\r\n      }).finally(_ => {\r\n      })\r\n    },\r\n\r\n    handleAdd() {\r\n      this.list.push({\r\n        id: uuidv4(),\r\n        val: undefined,\r\n        key: ''\r\n      })\r\n      this.getCompTypeTree()\r\n    },\r\n\r\n    handleDelete(index) {\r\n      this.list.splice(index, 1)\r\n    },\r\n\r\n    async onSubmit() {\r\n      this.btnLoading = true\r\n      const obj = {}\r\n      for (let i = 0; i < this.list.length; i++) {\r\n        const element = this.list[i]\r\n        if (!element.val) {\r\n          if (element.key === 'SteelWeight' || element.key === 'SteelLength' || element.key === 'SteelAmount' || element.key === 'GrossWeight') {\r\n            element.val === 0 ? this.$message({ message: '值不能为0', type: 'warning' }) : this.$message({ message: '值不能为空', type: 'warning' })\r\n          } else {\r\n            this.$message({\r\n              message: '值不能为空',\r\n              type: 'warning'\r\n            })\r\n          }\r\n          this.btnLoading = false\r\n          return\r\n        }\r\n        // obj[element.key] = element.key=='Is_Component' ? eval(element.val.toLowerCase()) : element.val; // \"true\"转true\r\n        obj[element.key] = element.val\r\n      }\r\n      // if (!obj.hasOwnProperty('Is_Component') && obj.hasOwnProperty('SteelType')) {\r\n      //   obj['Is_Component'] = this.Is_Component\r\n      // }\r\n      await BatchUpdateComponentImportInfo({\r\n        Ids: this.selectList.map(v => v.Id).toString(),\r\n        EditInfo: obj\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '修改成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('close')\r\n          this.$emit('refresh')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.btnLoading = false\r\n      })\r\n    },\r\n\r\n    filterOption(currentValue) {\r\n      console.log(currentValue)\r\n      return this.options.filter(k => {\r\n        return (!this.list.map(v => v.key).includes(k.key) || k.key === currentValue) && k.label\r\n      })\r\n    },\r\n\r\n    checkType(key, type) {\r\n      if (!key) return false\r\n      return this.options.find(v => v.key === key).type === type\r\n    },\r\n\r\n    init(list, columnsOption) {\r\n      console.log(list)\r\n      this.selectList = list\r\n\r\n    //   let filterarr = columnsOption.filter(v=> {\r\n    //   return v.Display_Name != \"项目名称\" && v.Display_Name != \"区域\" && v.Display_Name != \"批次\" && v.Code != \"SteelAllWeight\" &&  v.Code != \"SteelName\" && v.Code != \"TotalGrossWeight\" && v.Code != \"SteelType\" && v.Code != \"Part\" && v.Code != \"SchedulingNum\"  && v.Code != \"Create_UserName\" && v.Code === \"Is_Component_Status\"\r\n    //  })\r\n    //  this.options = filterarr?.map(item => ({ key: item.Code, label: item.Display_Name, type: item.Code === \"Is_Component\" ?\"array\": item.Code === \"SteelWeight\" || item.Code === \"GrossWeight\" || item.Code === \"SteelLength\" || item.Code === \"SchedulingNum\"  ? \"number\" : \"string\"}))\r\n      // this.options = columnsOption?.map(v => ({ key: v.Code, label: v.Display_Name, type: v.Code === \"Is_Component\" ? \"array\": v.Code === \"SteelWeight\" ||  v.Code === \"SteelAllWeight\" ? 'number' : 'string' }))\r\n    },\r\n\r\n    // 获取配置数据\r\n    async getColumnConfiguration(code, mainType = 'plm_component_page_list') {\r\n      const res = await GetGridByCode({ code: mainType + ',' + code })\r\n      return res.Data.ColumnList\r\n    },\r\n\r\n    // 根据Code（数据）获取名称\r\n    async convertCode(typeCode, propsArr = [], mainType) {\r\n      console.log(propsArr)\r\n      const SchedulArr = this.selectList.filter((item) => {\r\n        return item.SteelType !== null && item.SteelType !== ''\r\n      })\r\n      const propsArrfilter = SchedulArr.length > 0 ? propsArr.filter((v) => v !== 'Is_Component') : propsArr\r\n      const props = await this.getColumnConfiguration(typeCode, mainType)\r\n      console.log(props)\r\n      const columns = props.filter(i => {\r\n        const arr = propsArrfilter.map(i => i.toLowerCase())\r\n        return arr.includes(i.Code.toLowerCase())\r\n      })\r\n      console.log(columns)\r\n      return columns\r\n    },\r\n\r\n    // 选择构件类型\r\n    steelTypeChange(e) {\r\n      this.Is_Component = e.Code == 'true' ? '是' : '否'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n[class^=\"el-icon\"] {\r\n  font-size: 24px;\r\n  vertical-align: middle;\r\n  cursor: pointer;\r\n  margin-left: 15px;\r\n}\r\n\r\n.item-x {\r\n  display: flex;\r\n  margin-bottom: 20px;\r\n  flex: 0 1 50%;\r\n  justify-content: space-between;\r\n\r\n  .item {\r\n    width: 45%;\r\n    white-space: nowrap;\r\n    &:not(:first-of-type) {\r\n      margin-left: 20px;\r\n      .cs-number-btn-hidden,.el-input,.el-select{\r\n        width: 80%;\r\n      }\r\n    }\r\n  }\r\n\r\n  .item-span {\r\n    width: 90px;\r\n    padding-top: 5px;\r\n  }\r\n\r\n}\r\n  ::v-deep{\r\n    .el-tree-select-input{\r\n      width: 80%!important;\r\n    }\r\n  }\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\bom-import-temp-config\\index.vue?vue&type=style&index=0&id=218a2d8b&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\bom-import-temp-config\\index.vue", "mtime": 1757468112191}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoQA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/bom-setting/bom-import-temp-config", "sourcesContent": ["\r\n<template>\r\n  <div v-loading=\"pgLoading\" class=\"page-container\">\r\n    <!-- <el-button style=\"margin-bottom: 16px\" @click=\"backPage\">返回</el-button>-->\r\n    <div class=\"top-wrapper\">\r\n      <div class=\"info\">\r\n        <template v-if=\"!!majorName\">\r\n          <div class=\"title\">当前专业：</div>\r\n          <div class=\"value\">{{ majorName }}</div>\r\n        </template>\r\n        <template v-if=\"!!unit\">\r\n          <div class=\"title\">统计单位：</div>\r\n          <div class=\"value\">{{ unit }}</div>\r\n        </template>\r\n        <template v-if=\"!!steelUnit\">\r\n          <div class=\"title\">构件单位：</div>\r\n          <div class=\"value\">{{ steelUnit }}</div>\r\n        </template>\r\n        <template>\r\n          <div class=\"title\">单位统计字段：</div>\r\n          {{ unitInfo }}\r\n        </template>\r\n      </div>\r\n      <el-tabs v-model=\"activeName\">\r\n        <el-tab-pane v-for=\"(item,index) in tabList\" :key=\"index\" :label=\"item.label\" :name=\"item.value\" />\r\n      </el-tabs>\r\n    </div>\r\n\r\n    <div class=\"cs-content-wrapper\">\r\n      <div class=\"content-top\">\r\n        <span class=\"content-title\">系统字段</span>\r\n        <div class=\"content-top-right\">\r\n          <label class=\"cs-label\">\r\n            <span>字段名称：</span>\r\n            <el-input v-model=\"searchValue\" placeholder=\"请输入\" clearable=\"\" />\r\n          </label>\r\n          <div>\r\n            <el-button type=\"primary\" @click=\"filterList\">查询</el-button>\r\n            <el-button type=\"primary\" :loading=\"saveLoading\" @click=\"save\">保存设置</el-button>\r\n            <el-button type=\"success\" :loading=\"restoreLoading1\" @click=\"restore(1)\">恢复默认二级清单</el-button>\r\n            <el-button type=\"success\" :loading=\"restoreLoading2\" @click=\"restore(2)\">恢复默认三级清单</el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"100px\">\r\n        <el-row v-for=\"item in list\" :key=\"item.uuid\">\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"字段名：\">\r\n              <el-input v-model=\"item.Display_Name\" clearble :class=\"['w100',{'showRed':item.showRed}]\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"Code：\">\r\n              <el-input v-model=\"item.Code\" disabled clearble class=\"w100\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"备注说明：\">\r\n              <el-input v-model=\"item.Remark\" disabled clearble class=\"w100\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"3\">\r\n            <el-form-item label=\"排序：\">\r\n              <el-input-number v-model.number=\"item.Sort\" :min=\"0\" class=\"w100 cs-number-btn-hidden\" clearble />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col v-if=\"!item.showRed\" :span=\"3\">\r\n            <el-form-item label=\"是否启用：\">\r\n              <el-switch v-model=\"item.Is_Enabled\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { closeTagView, deepClone } from '@/utils'\r\nimport { GetTableSettingList, RestoreTemplateType, SavDeepenTemplateSetting } from '@/api/PRO/component-type'\r\nimport { v4 as uuidv4 } from 'uuid'\r\n\r\nexport default {\r\n  name: 'PROBomImportTemplateConfig',\r\n  data() {\r\n    return {\r\n      activeName: 'pz',\r\n      tabList: [{\r\n        label: '深化清单导入配置',\r\n        value: 'pz'\r\n      }],\r\n      searchValue: '',\r\n      majorName: '',\r\n      unit: '',\r\n      steelUnit: '',\r\n      templateListNew: [],\r\n      list: [],\r\n      form: {},\r\n      pgLoading: false,\r\n      saveLoading: false,\r\n      restoreLoading1: false,\r\n      restoreLoading2: false,\r\n      unitInfo: ''\r\n\r\n    }\r\n  },\r\n  mounted() {\r\n    this.majorName = this.$route.query.name || ''\r\n    this.unit = this.$route.query.unit || ''\r\n    this.steelUnit = this.$route.query.steel_unit || ''\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n\r\n    fetchData() {\r\n      this.pgLoading = true\r\n      GetTableSettingList({\r\n        ProfessionalCode: 'Steel'\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          let result = ''\r\n          this.defaultList = res.Data.map(item => {\r\n            item.uuid = uuidv4()\r\n            if (item.Code === 'SteelAmount') {\r\n              result += `${item.Display_Name || ''}*`\r\n            }\r\n            if (item.Code === 'SteelWeight') {\r\n              result += `${item.Display_Name || ''}`\r\n            }\r\n            item.showRed = item.Column_Type === 0\r\n            return item\r\n          })\r\n          this.list = deepClone(this.defaultList)\r\n          this.unitInfo = result\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.pgLoading = false\r\n      })\r\n    },\r\n    filterList() {\r\n      if (!this.searchValue) {\r\n        this.list = deepClone(this.defaultList)\r\n        return\r\n      }\r\n      this.list = this.defaultList.filter(item => {\r\n        return item.Display_Name.includes(this.searchValue)\r\n      })\r\n    },\r\n    save() {\r\n      const hasEmpty = this.list.some(item =>\r\n        !item.Display_Name || item.Display_Name.trim() === ''\r\n      )\r\n      if (hasEmpty) {\r\n        this.$message.error('字段名不能为空')\r\n        return\r\n      }\r\n\r\n      const nameSet = new Set()\r\n      const duplicates = this.list.filter(item => {\r\n        if (nameSet.has(item.Display_Name)) {\r\n          return true\r\n        }\r\n        nameSet.add(item.Display_Name)\r\n        return false\r\n      })\r\n\r\n      if (duplicates.length > 0) {\r\n        this.$message.error(`存在重复的字段名 : ${duplicates.map(d => d.Display_Name).join(', ')}`)\r\n        return []\r\n      }\r\n\r\n      this.$confirm('是否保存当前配置?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.saveLoading = true\r\n        const submitList = this.list.map(item => {\r\n          return {\r\n            Professional_Code: 'Steel',\r\n            Is_Component: '',\r\n            Code: item.Code,\r\n            Display_Name: item.Display_Name,\r\n            Column_Type: item.Column_Type,\r\n            Sort: item.Sort,\r\n            Remark: item.Remark,\r\n            Is_Enabled: item.Is_Enabled\r\n          }\r\n        })\r\n        SavDeepenTemplateSetting(submitList).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.saveLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n        this.saveLoading = false\r\n      })\r\n    },\r\n    restore(type) {\r\n      const label = type === 1 ? '二级' : '三级'\r\n      if (type === 1) {\r\n        this.restoreLoading1 = true\r\n      } else {\r\n        this.restoreLoading2 = true\r\n      }\r\n      this.$confirm(`此是否恢复默认${label}清单?`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        RestoreTemplateType({\r\n          ProfessionalCode: 'Steel',\r\n          Type: type\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.fetchData()\r\n            this.$message({\r\n              type: 'success',\r\n              message: '恢复成功!'\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.restoreLoading1 = false\r\n          this.restoreLoading2 = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n        this.restoreLoading1 = false\r\n        this.restoreLoading2 = false\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-container {\r\n  margin: 16px;\r\n  box-sizing: border-box;\r\n\r\n  .top-wrapper {\r\n    background: #fff;\r\n    padding: 16px 16px 0 16px;\r\n    box-sizing: border-box;\r\n\r\n    .title {\r\n      font-size: 16px;\r\n      font-weight: 500;\r\n      color: #333333;\r\n    }\r\n    .info{\r\n      font-size: 14px;\r\n      display: flex;\r\n      flex-direction: row;\r\n      margin-bottom: 16px;\r\n      .title{\r\n        font-size: 14px;\r\n        color: #999999;\r\n      }\r\n      .value{\r\n        color: #333333;\r\n        margin-right: 24px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .cs-content-wrapper{\r\n    background-color: #ffffff;\r\n    margin-top: 16px;\r\n    padding: 16px;\r\n    .content-top{\r\n      margin-bottom: 32px;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      .content-title{\r\n        font-weight: 400;\r\n        color: #1f2f3d;\r\n        font-size: 22px;\r\n      }\r\n      .cs-label{\r\n        font-size: 14px;\r\n        font-family: \"Microsoft YaHei\", \"微软雅黑\", \"PingFang SC\", \"Hiragino Sans GB\", \"Helvetica Neue\", Arial, sans-serif;\r\n        font-weight: normal;\r\n        display: flex;\r\n        white-space: nowrap;\r\n        align-items: center;\r\n        margin-right: 8px;\r\n\r\n        span{\r\n          margin-right: 16px;\r\n        }\r\n      }\r\n      .content-top-right{\r\n        display: flex;\r\n      }\r\n    }\r\n    .showRed{\r\n      ::v-deep{\r\n        .el-input__inner {\r\n          color:red !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
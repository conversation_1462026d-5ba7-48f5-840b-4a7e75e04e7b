{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\ship\\component\\Card.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\ship\\component\\Card.vue", "mtime": 1758677034218}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIHByb3BzOiB7DQogICAgaXRlbTogew0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgZGVmYXVsdDogKCkgPT4ge30NCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBoYW5kbGVFZGl0KCkgew0KICAgICAgdGhpcy4kZW1pdCgnZWRpdCcsIHRoaXMuaXRlbSkNCiAgICB9LA0KICAgIGhhbmRsZURlbGV0ZSgpew0KICAgICAgdGhpcy4kZW1pdCgnZGVsZXRlJywgdGhpcy5pdGVtKQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["Card.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAsBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Card.vue", "sourceRoot": "src/views/PRO/basic-information/ship/component", "sourcesContent": ["<template>\r\n  <div class=\"card-x\">\r\n    <i class=\"el-icon-ship\" style=\"font-size: 32px;color: #298DFF;margin-right: 12px\" />\r\n    <main class=\"cs-main\">\r\n      <div class=\"clearfix\">\r\n        <strong class=\"number\">{{ item.Shipnumber }}</strong>\r\n        <el-button style=\"float: right\" @click=\"handleEdit(item)\">编 辑</el-button>\r\n        <el-button style=\"float: right;margin-right:10px\" type=\"danger\"  @click=\"handleDelete(item)\">删 除</el-button>\r\n      </div>\r\n      <span class=\"info\">\r\n        {{ item.Captain }}\r\n        <el-divider direction=\"vertical\" />\r\n        {{ item.Mobile }}\r\n      </span>\r\n      <div class=\"tag-x\">\r\n        <span v-for=\"i in item.Factory_Name\" :key=\"i\" class=\"tag-w\">{{ i }}</span>\r\n      </div>\r\n    </main>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    item: {\r\n      type: Object,\r\n      default: () => {}\r\n    }\r\n  },\r\n  methods: {\r\n    handleEdit() {\r\n      this.$emit('edit', this.item)\r\n    },\r\n    handleDelete(){\r\n      this.$emit('delete', this.item)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.card-x{\r\n  display: flex;\r\n  background-color: #FFFFFF;\r\n  width: calc(25% - 16px);\r\n  min-width: 387px;\r\n  min-height: 148px;\r\n  margin: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0px 1px 4px rgba(20, 35, 78, 0.08);\r\n  border-radius: 8px;\r\n\r\n  .icon-steeringwheel{\r\n    display: inline-block;\r\n    margin-right: 12px;\r\n    font-size: 32px;\r\n    color: #298DFF;\r\n  }\r\n  .cs-main{\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n    padding-top: 3px;\r\n\r\n    .number{\r\n      line-height: 30px;\r\n    }\r\n\r\n    .info{\r\n      margin-bottom: 20px;\r\n      display: inline-block;\r\n      font-size: 14px;\r\n      color: rgba(34, 40, 52, 0.65);\r\n    }\r\n    .tag-x{\r\n      color: #818FB7;\r\n      .tag-w{\r\n        display: inline-block;\r\n        background: rgba(129, 143, 183, 0.12);\r\n        border-radius: 4px;\r\n        font-size: 14px;\r\n        padding:3px 6px;\r\n        margin: 4px;\r\n      }\r\n    }\r\n\r\n  }\r\n}\r\n</style>\r\n"]}]}
<template>
  <div v-loading="loading">
    <el-form ref="form" :model="form" :rules="rules" label-width="140px">
      <el-form-item v-for="(element) in itemOption" :key="element.tType" :label="element.label" prop="ownerProcess">
        <el-select v-model="element.value" clearable class="w100" placeholder="请选择">
          <template>
            <el-option
              v-for="(item) in OwnerOption[element.tType]"
              :key="item.tType"
              :label="item.Name"
              :value="item.Code"
            />
          </template>
        </el-select>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" :loading="btnLoading" @click="submit">确 定</el-button>
    </div>
  </div>
</template>

<script>

import { GetProcessListBase } from '@/api/PRO/technology-lib'

export default {
  props: {
    pageType: {
      type: String,
      default: undefined
    },
    partTypeOption: {
      type: Array,
      default: () => ([])
    },
    isPartPrepare: {
      type: Boolean,
      default: false
    },
    hasUnitPart: {
      type: Boolean,
      default: false
    },
    partName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      itemOption: [{
        key: '',
        value: ''
      }],
      form: {
      },
      loading: false,
      btnLoading: false,
      OwnerOption: {},
      rules: {
        // ownerProcess: [
        //   { required: true, message: '请选择车间', trigger: 'change' }
        // ]
      }
    }
  },
  methods: {
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.list.forEach(item => {
            const itemInfo = this.itemOption.find(v => v.tType === item.tType)
            if (itemInfo) {
              if (item.Scheduled_Used_Process && item.Scheduled_Used_Process !== itemInfo.value) {
                this.$message({
                  message: `请和该区域批次下已排产同${this.partName}领用工序保持一致`,
                  type: 'warning'
                })
              } else {
                item.Part_Used_Process = itemInfo.value
              }
            }
          })
          this.btnLoading = false
          this.handleClose()
        } else {
          return false
        }
      })
    },
    setOption(isInline, arr) {
      // console.log(7, this.isPartPrepare)
      this.list = arr || []
      this.list = this.list.map(item => {
        item.tType = item.Type + '&' + item.Belong_To_Component
        return item
      })
      console.log('this.list', this.list)
      this.isInline = isInline
      const suffix = '领用工序'
      const obj = {}
      this.itemOption = this.list.reduce((acc, cur) => {
        const partOwnerName = this.hasUnitPart ? (cur.Belong_To_Component ? '构件' : '部件') : ''
        if (!obj[cur.tType] && cur.Type !== 'Direct') {
          acc.push({
            code: cur.Type,
            label: cur.Type_Name + partOwnerName + suffix,
            value: '',
            tType: cur.tType
          })
          this.$set(this.OwnerOption, cur.tType, [])
        }
        obj[cur.tType] = true
        return acc
      }, [])
      this.fetchData()
      if (isInline && arr.length) {
        const cur = arr[0]
        const itemInfo = this.itemOption.find(v => v.tType === cur.tType)
        if (itemInfo) {
          itemInfo.value = cur.Part_Used_Process
        }
      }
    },
    getComOption() {
      const _listMap = {}
      const keyArr = []
      const getProcessItem = (code) => this.option.find(v => v.Code === code)
      const _option = this.option.filter(v => v.Is_Enable)
      this.list.forEach((element) => {
        const key = element.tType
        keyArr.push(key)
        if (!_listMap[key]) {
          _listMap[key] = []
        }
        let parentPath = 'Component_Technology_Path'
        if (this.hasUnitPart) {
          if (element.Belong_To_Component) {
            parentPath = 'Component_Technology_Path'
          } else {
            parentPath = 'SubAssembly_Technology_Path'
          }
        }
        element[parentPath] = element[parentPath] || ''

        if (element.Scheduled_Used_Process) {
          const item = getProcessItem(element.Scheduled_Used_Process)
          if (item) {
            _listMap[key].push(item)
          }
        } else {
          const componentProcess = element[parentPath].split('/').filter(v => !!v)
          // const processItem = this.option.find(v => v.Code === element.Temp_Part_Used_Process)

          if (componentProcess.length) {
            // if (element.Temp_Part_Used_Process && componentProcess.includes(element.Temp_Part_Used_Process)) {
            //   _listMap[key].push(processItem)
            // } else {
            //   componentProcess.forEach(c => {
            //     const cur = getProcessItem(c)
            //     if (cur) {
            //       _listMap[key].push(cur)
            //     }
            //   })
            // }
            componentProcess.forEach(c => {
              const cur = getProcessItem(c)
              if (cur) {
                if (_listMap[key].findIndex(v => v.Code === cur.Code) !== -1) {
                  return
                }
                _listMap[key].push(cur)
              }
            })
          } else {
            const partUsedProcess = element.Part_Used_Process
            const _fp = this.option.filter(item => {
              let flag = false
              if (partUsedProcess && partUsedProcess === item.Code) {
                flag = true
              }
              if (element.Part_Type_Used_Process && element.Part_Type_Used_Process === item.Code) {
                flag = true
              }
              if (!flag) {
                flag = !!item.Is_Enable
              }
              return flag
            })
            for (let i = 0; i < _fp.length; i++) {
              const cur = _fp[i]
              if (_listMap[key].findIndex(v => v.Code === cur.Code) !== -1) {
                continue
              }
              _listMap[key].push(cur)
            }
          }
        }
      })
      if (this.isInline) {
        this.OwnerOption = _listMap
      } else {
        keyArr.forEach((keys, idx) => {
          const belongType = keys.split('&')[1] === 'true' ? 1 : 3
          this.OwnerOption[keys] = _option.filter(v => v.Type === belongType)
        })
      }
    },
    async fetchData() {
      this.loading = true

      let hasTrueCompPart
      let hasFalseCompPart
      if (this.hasUnitPart) {
        hasTrueCompPart = this.list.some(v => !!v.Belong_To_Component)
        hasFalseCompPart = this.list.some(v => !v.Belong_To_Component)
      } else {
        hasTrueCompPart = true
        hasFalseCompPart = false
      }
      const fetchDataForType = async(type) => {
        try {
          const res = await GetProcessListBase({ type })
          if (res.IsSucceed) {
            return res.Data
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
            return []
          }
        } catch (error) {
          console.error(`Error fetching data for type ${type}:`, error)
          this.$message({
            message: `请求失败`,
            type: 'error'
          })
          return []
        }
      }

      try {
        let results = []

        if (hasTrueCompPart && hasFalseCompPart) {
          results = await Promise.all([fetchDataForType(1), fetchDataForType(3)])
        } else if (hasTrueCompPart) {
          results = [await fetchDataForType(1)]
        } else if (hasFalseCompPart) {
          results = [await fetchDataForType(3)]
        }

        this.option = results.filter(data => !!data.length).flat()
        this.getComOption()
      } finally {
        this.loading = false
      }
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.dialog-footer{
  text-align: right;
  margin-top: 30px;
}
</style>

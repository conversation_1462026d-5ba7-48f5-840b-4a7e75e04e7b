{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\detail.vue?vue&type=template&id=381ae030&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\detail.vue", "mtime": 1758683411838}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\index.vue", "mtime": 1758080281592}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/project-config/project-quality", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <ProjectData @setProjectData=\"setProjectData\" />\n    <div v-loading=\"pageLoading\" class=\"cs-z-page-main-content\">\n      <div class=\"bom-list\">\n        <div class=\"title\">\n          <span\n            v-for=\"(item, index) in title\"\n            :key=\"index\"\n            style=\"cursor: pointer\"\n            :class=\"spanCurr == index ? 'clickindex' : 'index'\"\n            @click=\"handelIndex(index, item)\"\n          >{{ item.Display_Name }}</span>\n        </div>\n        <div class=\"btns\">\n          <el-button type=\"primary\" @click=\"handleAddProject\">同步项目配置</el-button>\n          <el-button type=\"primary\" @click=\"handleAddFactory\">恢复工厂默认配置</el-button>\n        </div>\n      </div>\n      <div class=\"detail\">\n        <template>\n          <el-tabs\n            v-model=\"activeName\"\n            type=\"card\"\n            style=\"width: 100%; height: 100%\"\n          >\n            <el-tab-pane label=\"检查类型\" name=\"检查类型\">\n              <CheckType\n                ref=\"checkTypeRef\"\n                :check-type=\"checkType\"\n                :sys-project-id=\"sysProjectId\"\n                @optionFn=\"optionEdit\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"检查项\" name=\"检查项\">\n              <CheckItem\n                ref=\"checkItemRef\"\n                :check-type=\"checkType\"\n                :sys-project-id=\"sysProjectId\"\n                @ItemEdit=\"ItemEdit\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"检查项组合\" name=\"检查项组合\">\n              <CheckCombination\n                ref=\"checkCombinationRef\"\n                :check-type=\"checkType\"\n                :sys-project-id=\"sysProjectId\"\n                @CombinationEdit=\"CombinationEdit\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"质检节点配置\" name=\"质检节点配置\">\n              <CheckNode\n                ref=\"checkNodeRef\"\n                :check-type=\"checkType\"\n                :sys-project-id=\"sysProjectId\"\n                @NodeEdit=\"NodeEdit\"\n              />\n            </el-tab-pane>\n            <el-tab-pane v-if=\"isCom\" label=\"公差配置\" name=\"公差配置\">\n              <ToleranceConfig\n                ref=\"toleranceConfigRef\"\n                :check-type=\"checkType\"\n                :sys-project-id=\"sysProjectId\"\n                @edit=\"addToleranceConfig\"\n              />\n            </el-tab-pane>\n            <el-button\n              v-if=\"activeName==='检查项组合'\"\n              type=\"primary\"\n              class=\"addbtn\"\n              @click=\"addData\"\n            >新增</el-button>\n          </el-tabs>\n        </template>\n      </div>\n    </div>\n    <el-dialog\n      v-if=\"dialogVisible\"\n      ref=\"content\"\n      v-el-drag-dialog\n      :title=\"dialogTitle\"\n      :visible.sync=\"dialogVisible\"\n      :close-on-click-modal=\"false\"\n      :width=\"width\"\n      class=\"z-dialog\"\n      @close=\"handleClose\"\n    >\n      <component\n        :is=\"currentComponent\"\n        ref=\"content\"\n        :dialog-data=\"dialogData\"\n        :sys-project-id=\"sysProjectId\"\n        @ToleranceRefresh=\"ToleranceRefresh\"\n        @refresh=\"handleRefresh\"\n        @close=\"handleClose\"\n      />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport ProjectData from '../components/ProjectData.vue'\nimport CheckType from './components/CheckType' // 检查类型\nimport CheckCombination from './components/CheckCombination' // 检查项组合\nimport CheckNode from './components/CheckNode' // 质检节点配置\nimport CheckItem from './components/CheckItem' // 检查项\nimport TypeDialog from './components/Dialog/TypeDialog' // 检查类型弹窗\nimport ItemDialog from './components/Dialog/ItemDialog' // 检查项弹窗\nimport CombinationDialog from './components/Dialog/CombinationDialog' // 检查项组合弹窗\nimport NodeDialog from './components/Dialog/NodeDialog' // 质检节点组合弹窗\nimport ToleranceConfig from './components/ToleranceConfig.vue' // 公差配置\nimport ProjectAdd from './components/Dialog/ProjectAddDialog.vue'\n// import { GetDictionaryDetailListByCode } from '@/api/PRO/factorycheck'\nimport elDragDialog from '@/directive/el-drag-dialog'\nimport ToleranceDialog from './components/Dialog/ToleranceDialog' // 公差配置弹窗\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nexport default {\n  name: 'PLMFactoryGroupList',\n  directives: { elDragDialog },\n  components: {\n    ProjectData,\n    ProjectAdd,\n    CheckType,\n    ToleranceConfig,\n    CheckCombination,\n    CheckNode,\n    CheckItem,\n    TypeDialog,\n    ItemDialog,\n    CombinationDialog,\n    NodeDialog,\n    ToleranceDialog\n  },\n  data() {\n    return {\n      spanCurr: 0,\n      title: [],\n      activeName: '检查类型',\n      checkType: {},\n      tbLoading: false,\n      tbData: [],\n      dialogVisible: false,\n      currentComponent: '',\n      dialogTitle: '',\n      isCom: false,\n      width: '60%',\n      dialogData: {},\n      sysProjectId: '',\n      pageLoading: false\n    }\n  },\n  created() {},\n  async mounted() {\n    await this.getCheckType()\n  },\n  methods: {\n    async getCheckType() {\n      const bomLevel = await GetBOMInfo()\n      this.title = bomLevel.list\n      this.checkType = bomLevel.list[0]\n      this.isCom = bomLevel.list.find(v => v.Code === '-1')\n      // GetDictionaryDetailListByCode({ dictionaryCode: 'Quality_Code' }).then(\n      //   (res) => {\n      //     if (res.IsSucceed) {\n      //       this.title = res.Data // wtf\n      //       this.checkType = this.title[0]// wtf\n      //       this.isCom = res.Data.find(v => v.Value === '0')\n      //     } else {\n      //       this.$message({\n      //         type: 'error',\n      //         message: 'res.Message'\n      //       })\n      //     }\n      //   }\n      // )\n    },\n    handelIndex(index, item) {\n      this.pageLoading = true\n      this.isCom = item.Code === '-1'\n      if (!this.isCom && this.activeName === '公差配置') {\n        this.activeName = '检查类型'\n      }\n      this.checkType = item\n      this.spanCurr = index\n      setTimeout(() => {\n        this.pageLoading = false\n      }, 500)\n    },\n    addData() {\n      console.log(this.activeName)\n      switch (this.activeName) {\n        case '检查类型':\n          this.addCheckType()\n          break\n        case '检查项':\n          this.addCheckItem()\n          break\n        case '检查项组合':\n          this.addCheckCombination()\n          break\n        case '质检节点配置':\n          this.addCheckNode()\n          break\n        case '公差配置':\n          this.addToleranceConfig()\n          break\n        default:\n          this.addCheckType()\n      }\n    },\n    addCheckType() {\n      this.width = '30%'\n      this.generateComponent('新增检查类型', 'TypeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType)\n      })\n    },\n    editCheckType(data) {\n      this.width = '30%'\n      this.generateComponent('编辑检查类型', 'TypeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType, data)\n      })\n    },\n    addCheckItem() {\n      this.width = '30%'\n      this.generateComponent('新增检查项', 'ItemDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType)\n      })\n    },\n    editCheckItem(data) {\n      this.width = '30%'\n      this.generateComponent('编辑检查项', 'ItemDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType, data)\n      })\n    },\n    addCheckCombination() {\n      this.width = '40%'\n      this.generateComponent('新增检查项组合', 'CombinationDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType)\n      })\n    },\n    editCheckCombination(data) {\n      this.width = '40%'\n      this.generateComponent('编辑检查项组合', 'CombinationDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType, data)\n      })\n    },\n    addCheckNode() {\n      this.width = '45%'\n      this.generateComponent('新增质检节点配置', 'NodeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType)\n      })\n    },\n    editCheckNode(data) {\n      this.width = '45%'\n      this.generateComponent('编辑质检节点配置', 'NodeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType, data)\n      })\n    },\n    addToleranceConfig(data) {\n      this.width = '45%'\n      this.generateComponent(data?.Id ? '编辑公差配置' : '新增公差配置', 'ToleranceDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init(data?.Id ? '编辑' : '新增', this.checkType.Id, data)\n      })\n    },\n    handleAddProject() {\n      this.width = '580px'\n      this.generateComponent('同步项目配置', 'ProjectAdd')\n    },\n    handleRefresh() {\n      switch (this.activeName) {\n        case '检查类型':\n          this.$refs.checkTypeRef.getCheckTypeList()\n          break\n        case '检查项':\n          this.$refs.checkItemRef.getCheckItemList()\n          break\n        case '检查项组合':\n          this.$refs.checkCombinationRef.getQualityList()\n          break\n        case '质检节点配置':\n          this.$refs.checkNodeRef.getNodeList()\n          break\n      }\n    },\n    handleClose() {\n      this.dialogVisible = false\n    },\n    generateComponent(title, component) {\n      this.dialogTitle = title\n      this.currentComponent = component\n      this.dialogVisible = true\n    },\n    optionEdit(data) {\n      // this.dialogData = Object.assign({},data)\n      this.editCheckType(data)\n    },\n    ItemEdit(data) {\n      this.editCheckItem(data)\n    },\n    CombinationEdit(data) {\n      this.editCheckCombination(data)\n    },\n    NodeEdit(data) {\n      this.editCheckNode(data)\n    },\n    ToleranceRefresh(data) {\n      this.$refs.toleranceConfigRef.getToleranceList()\n    },\n    setProjectData(data) {\n      this.sysProjectId = data.Sys_Project_Id\n    },\n    // 从工厂级同步\n    handleAddFactory() {\n      this.$confirm('此操作将会恢复到工厂质检配置, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.restoreFactoryProcessFromProject()\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消恢复'\n        })\n      })\n    },\n    restoreFactoryProcessFromProject() {\n\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/mixin.scss\";\n@import \"~@/styles/variables.scss\";\n.app-container{\n  display: flex;\n  flex-direction: row;\n  height: 100%;\n  .cs-z-page-main-content {\n    flex: 1;\n  }\n}\n\n.bom-list {\n  display: flex;\n  justify-content: space-between;\n  border-bottom: 1px solid #efefef;\n  margin-bottom: 16px;\n  align-items: center;\n  .title {\n    width: 100%;\n    height: 48px;\n    background-color: #ffffff;\n    .index {\n      font-size: 16px;\n      line-height: 48px;\n      margin-right: 16px;\n      padding: 0 16px;\n      display: inline-block;\n      text-align: center;\n      color: #999999;\n    }\n    .clickindex {\n      border-bottom: 2px solid #298dff;\n      font-size: 16px;\n      line-height: 46px;\n      margin-right: 16px;\n      padding: 0 16px;\n      display: inline-block;\n      text-align: center;\n      color: #298dff;\n    }\n  }\n  .btns {\n    display: flex;\n    height: 32px;\n  }\n}\n\n::v-deep {\n  .el-tabs {\n    display: inline-block;\n  }\n  .el-tabs--card .el-tabs__header {\n    border: 0;\n    margin: 0;\n  }\n  .el-tabs--card > .el-tabs__header .el-tabs__nav {\n    border-bottom: 1px solid #dfe4ed;\n  }\n  .el-tabs__content {\n    margin-top: 16px !important;\n  }\n}\n.detail {\n  height: calc(100vh - 240px);\n  box-sizing: border-box;\n}\n.addbtn {\n  position: fixed;\n  right: 38px;\n  top: 205px;\n}\n.z-dialog {\n  ::v-deep {\n    .el-dialog__header {\n      background-color: #298dff;\n\n      .el-dialog__title,\n      .el-dialog__close {\n        color: #ffffff;\n      }\n    }\n\n    .el-dialog__body {\n      max-height: 700px;\n      overflow: auto;\n      @include scrollBar;\n\n      &::-webkit-scrollbar {\n        width: 8px;\n      }\n    }\n  }\n}\n</style>\n"]}]}
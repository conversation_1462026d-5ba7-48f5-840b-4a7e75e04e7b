{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\index.vue", "mtime": 1758095444832}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/project-config/project-quality", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <ProjectData @setProjectData=\"setProjectData\" />\n    <div v-loading=\"pageLoading\" class=\"cs-z-page-main-content\">\n      <div class=\"bom-list\">\n        <div class=\"title\">\n          <span\n            v-for=\"(item, index) in title\"\n            :key=\"index\"\n            style=\"cursor: pointer\"\n            :class=\"spanCurr == index ? 'clickindex' : 'index'\"\n            @click=\"handelIndex(index, item)\"\n          >{{ item.Display_Name }}</span>\n        </div>\n        <div class=\"btns\">\n          <el-button type=\"primary\" @click=\"handleAddProject\">同步项目配置</el-button>\n          <el-button type=\"primary\" @click=\"handleAddFactory\">恢复工厂默认配置</el-button>\n        </div>\n      </div>\n      <div class=\"detail\">\n        <template>\n          <el-tabs\n            v-model=\"activeName\"\n            type=\"card\"\n            style=\"width: 100%; height: 100%\"\n          >\n            <el-tab-pane label=\"检查类型\" name=\"检查类型\">\n              <CheckType\n                ref=\"checkTypeRef\"\n                :check-type=\"checkType\"\n                :sys-project-id=\"sysProjectId\"\n                @optionFn=\"optionEdit\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"检查项\" name=\"检查项\">\n              <CheckItem\n                ref=\"checkItemRef\"\n                :check-type=\"checkType\"\n                :sys-project-id=\"sysProjectId\"\n                @ItemEdit=\"ItemEdit\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"检查项组合\" name=\"检查项组合\">\n              <CheckCombination\n                ref=\"checkCombinationRef\"\n                :check-type=\"checkType\"\n                :sys-project-id=\"sysProjectId\"\n                @CombinationEdit=\"CombinationEdit\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"质检节点配置\" name=\"质检节点配置\">\n              <CheckNode\n                ref=\"checkNodeRef\"\n                :check-type=\"checkType\"\n                :sys-project-id=\"sysProjectId\"\n                @NodeEdit=\"NodeEdit\"\n              />\n            </el-tab-pane>\n            <el-tab-pane v-if=\"isCom\" label=\"公差配置\" name=\"公差配置\">\n              <ToleranceConfig\n                ref=\"toleranceConfigRef\"\n                :check-type=\"checkType\"\n                :sys-project-id=\"sysProjectId\"\n                @edit=\"addToleranceConfig\"\n              />\n            </el-tab-pane>\n            <el-button\n              v-if=\"activeName==='检查项组合'\"\n              type=\"primary\"\n              class=\"addbtn\"\n              @click=\"addData\"\n            >新增</el-button>\n          </el-tabs>\n        </template>\n      </div>\n    </div>\n    <el-dialog\n      v-if=\"dialogVisible\"\n      ref=\"content\"\n      v-el-drag-dialog\n      :title=\"dialogTitle\"\n      :visible.sync=\"dialogVisible\"\n      :close-on-click-modal=\"false\"\n      :width=\"width\"\n      class=\"z-dialog\"\n      @close=\"handleClose\"\n    >\n      <component\n        :is=\"currentComponent\"\n        ref=\"content\"\n        :dialog-data=\"dialogData\"\n        :sys-project-id=\"sysProjectId\"\n        @ToleranceRefresh=\"ToleranceRefresh\"\n        @refresh=\"handleRefresh\"\n        @close=\"handleClose\"\n      />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport ProjectData from '../components/ProjectData.vue'\nimport CheckType from './components/CheckType' // 检查类型\nimport CheckCombination from './components/CheckCombination' // 检查项组合\nimport CheckNode from './components/CheckNode' // 质检节点配置\nimport CheckItem from './components/CheckItem' // 检查项\nimport TypeDialog from './components/Dialog/TypeDialog' // 检查类型弹窗\nimport ItemDialog from './components/Dialog/ItemDialog' // 检查项弹窗\nimport CombinationDialog from './components/Dialog/CombinationDialog' // 检查项组合弹窗\nimport NodeDialog from './components/Dialog/NodeDialog' // 质检节点组合弹窗\nimport ToleranceConfig from './components/ToleranceConfig.vue' // 公差配置\nimport ProjectAdd from './components/Dialog/ProjectAddDialog.vue'\n// import { GetDictionaryDetailListByCode } from '@/api/PRO/factorycheck'\nimport elDragDialog from '@/directive/el-drag-dialog'\nimport ToleranceDialog from './components/Dialog/ToleranceDialog' // 公差配置弹窗\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport { RestoreFactoryQualityFromProject } from '@/api/PRO/factorycheck'\nexport default {\n  name: 'PLMFactoryGroupList',\n  directives: { elDragDialog },\n  components: {\n    ProjectData,\n    ProjectAdd,\n    CheckType,\n    ToleranceConfig,\n    CheckCombination,\n    CheckNode,\n    CheckItem,\n    TypeDialog,\n    ItemDialog,\n    CombinationDialog,\n    NodeDialog,\n    ToleranceDialog\n  },\n  data() {\n    return {\n      spanCurr: 0,\n      title: [],\n      activeName: '检查类型',\n      checkType: {},\n      tbLoading: false,\n      tbData: [],\n      dialogVisible: false,\n      currentComponent: '',\n      dialogTitle: '',\n      isCom: false,\n      width: '60%',\n      dialogData: {},\n      sysProjectId: '',\n      pageLoading: false\n    }\n  },\n  created() {},\n  async mounted() {\n    await this.getCheckType()\n  },\n  methods: {\n    async getCheckType() {\n      const bomLevel = await GetBOMInfo()\n      this.title = bomLevel.list\n      this.checkType = bomLevel.list[0]\n      this.isCom = bomLevel.list.find(v => v.Code === '-1')\n      // GetDictionaryDetailListByCode({ dictionaryCode: 'Quality_Code' }).then(\n      //   (res) => {\n      //     if (res.IsSucceed) {\n      //       this.title = res.Data // wtf\n      //       this.checkType = this.title[0]// wtf\n      //       this.isCom = res.Data.find(v => v.Value === '0')\n      //     } else {\n      //       this.$message({\n      //         type: 'error',\n      //         message: 'res.Message'\n      //       })\n      //     }\n      //   }\n      // )\n    },\n    handelIndex(index, item) {\n      this.pageLoading = true\n      this.isCom = item.Code === '-1'\n      if (!this.isCom && this.activeName === '公差配置') {\n        this.activeName = '检查类型'\n      }\n      this.checkType = item\n      this.spanCurr = index\n      setTimeout(() => {\n        this.pageLoading = false\n      }, 500)\n    },\n    addData() {\n      console.log(this.activeName)\n      switch (this.activeName) {\n        case '检查类型':\n          this.addCheckType()\n          break\n        case '检查项':\n          this.addCheckItem()\n          break\n        case '检查项组合':\n          this.addCheckCombination()\n          break\n        case '质检节点配置':\n          this.addCheckNode()\n          break\n        case '公差配置':\n          this.addToleranceConfig()\n          break\n        default:\n          this.addCheckType()\n      }\n    },\n    addCheckType() {\n      this.width = '30%'\n      this.generateComponent('新增检查类型', 'TypeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType)\n      })\n    },\n    editCheckType(data) {\n      this.width = '30%'\n      this.generateComponent('编辑检查类型', 'TypeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType, data)\n      })\n    },\n    addCheckItem() {\n      this.width = '30%'\n      this.generateComponent('新增检查项', 'ItemDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType)\n      })\n    },\n    editCheckItem(data) {\n      this.width = '30%'\n      this.generateComponent('编辑检查项', 'ItemDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType, data, this.sysProjectId)\n      })\n    },\n    addCheckCombination() {\n      this.width = '40%'\n      this.generateComponent('新增检查项组合', 'CombinationDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType)\n      })\n    },\n    editCheckCombination(data) {\n      this.width = '40%'\n      this.generateComponent('编辑检查项组合', 'CombinationDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType, data)\n      })\n    },\n    addCheckNode() {\n      this.width = '45%'\n      this.generateComponent('新增质检节点配置', 'NodeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType, this.sysProjectId)\n      })\n    },\n    editCheckNode(data) {\n      this.width = '45%'\n      this.generateComponent('编辑质检节点配置', 'NodeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType, data)\n      })\n    },\n    addToleranceConfig(data) {\n      this.width = '45%'\n      this.generateComponent(data?.Id ? '编辑公差配置' : '新增公差配置', 'ToleranceDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init(data?.Id ? '编辑' : '新增', this.checkType.Id, data)\n      })\n    },\n    handleAddProject() {\n      this.width = '580px'\n      this.generateComponent('同步项目配置', 'ProjectAdd')\n    },\n    handleRefresh() {\n      switch (this.activeName) {\n        case '检查类型':\n          this.$refs.checkTypeRef.getCheckTypeList()\n          break\n        case '检查项':\n          this.$refs.checkItemRef.getCheckItemList()\n          break\n        case '检查项组合':\n          this.$refs.checkCombinationRef.getQualityList()\n          break\n        case '质检节点配置':\n          this.$refs.checkNodeRef.getNodeList()\n          break\n      }\n    },\n    handleClose() {\n      this.dialogVisible = false\n    },\n    generateComponent(title, component) {\n      this.dialogTitle = title\n      this.currentComponent = component\n      this.dialogVisible = true\n    },\n    optionEdit(data) {\n      // this.dialogData = Object.assign({},data)\n      this.editCheckType(data)\n    },\n    ItemEdit(data) {\n      this.editCheckItem(data)\n    },\n    CombinationEdit(data) {\n      this.editCheckCombination(data)\n    },\n    NodeEdit(data) {\n      this.editCheckNode(data)\n    },\n    ToleranceRefresh(data) {\n      this.$refs.toleranceConfigRef.getToleranceList()\n    },\n    setProjectData(data) {\n      this.sysProjectId = data.Sys_Project_Id\n    },\n    // 从工厂级同步\n    handleAddFactory() {\n      this.$confirm('此操作将会恢复到工厂质检配置, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.restoreFactoryQualityFromProject()\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消恢复'\n        })\n      })\n    },\n    restoreFactoryQualityFromProject() {\n      RestoreFactoryQualityFromProject({\n        sysProjectId: this.sysProjectId\n      }).then((res) => {\n        if (res.Code === 0) {\n          this.$message({\n            type: 'success',\n            message: res.Message\n          })\n          this.handleRefresh()\n        } else {\n          this.$message.error(res.Message)\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/mixin.scss\";\n@import \"~@/styles/variables.scss\";\n.app-container{\n  display: flex;\n  flex-direction: row;\n  height: 100%;\n  .cs-z-page-main-content {\n    flex: 1;\n  }\n}\n\n.bom-list {\n  display: flex;\n  justify-content: space-between;\n  border-bottom: 1px solid #efefef;\n  margin-bottom: 16px;\n  align-items: center;\n  .title {\n    width: 100%;\n    height: 48px;\n    background-color: #ffffff;\n    .index {\n      font-size: 16px;\n      line-height: 48px;\n      margin-right: 16px;\n      padding: 0 16px;\n      display: inline-block;\n      text-align: center;\n      color: #999999;\n    }\n    .clickindex {\n      border-bottom: 2px solid #298dff;\n      font-size: 16px;\n      line-height: 46px;\n      margin-right: 16px;\n      padding: 0 16px;\n      display: inline-block;\n      text-align: center;\n      color: #298dff;\n    }\n  }\n  .btns {\n    display: flex;\n    height: 32px;\n  }\n}\n\n::v-deep {\n  .el-tabs {\n    display: inline-block;\n  }\n  .el-tabs--card .el-tabs__header {\n    border: 0;\n    margin: 0;\n  }\n  .el-tabs--card > .el-tabs__header .el-tabs__nav {\n    border-bottom: 1px solid #dfe4ed;\n  }\n  .el-tabs__content {\n    margin-top: 16px !important;\n  }\n}\n.detail {\n  height: calc(100vh - 240px);\n  box-sizing: border-box;\n}\n.addbtn {\n  position: fixed;\n  right: 38px;\n  top: 205px;\n}\n.z-dialog {\n  ::v-deep {\n    .el-dialog__header {\n      background-color: #298dff;\n\n      .el-dialog__title,\n      .el-dialog__close {\n        color: #ffffff;\n      }\n    }\n\n    .el-dialog__body {\n      max-height: 700px;\n      overflow: auto;\n      @include scrollBar;\n\n      &::-webkit-scrollbar {\n        width: 8px;\n      }\n    }\n  }\n}\n</style>\n"]}]}
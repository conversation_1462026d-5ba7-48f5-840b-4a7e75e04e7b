{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\components\\ProjectData.vue?vue&type=template&id=649f8757&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\components\\ProjectData.vue", "mtime": 1757468113454}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxFeHBhbmRhYmxlU2VjdGlvbiB2LW1vZGVsPSJzaG93RXhwYW5kIiA6d2lkdGg9IjMwMCIgY2xhc3M9ImNzLWxlZnQgZmZmIj4KICA8ZGl2IGNsYXNzPSJpbm5lci13cmFwcGVyIj4KICAgIDxkaXYgY2xhc3M9InRyZWUtc2VhcmNoIj4KICAgICAgPGVsLWlucHV0CiAgICAgICAgdi1tb2RlbC50cmltPSJwcm9qZWN0TmFtZSIKICAgICAgICBwbGFjZWhvbGRlcj0i5YWz6ZSu6K+N5pCc57SiIgogICAgICAgIHNpemU9InNtYWxsIgogICAgICAgIGNsZWFyYWJsZQogICAgICAgIHN1ZmZpeC1pY29uPSJlbC1pY29uLXNlYXJjaCIKICAgICAgICBAYmx1cj0iZmV0Y2hUcmVlRGF0YUxvY2FsIgogICAgICAgIEBjbGVhcj0iZmV0Y2hUcmVlRGF0YUxvY2FsIgogICAgICAgIEBrZXlkb3duLmVudGVyLm5hdGl2ZT0iZmV0Y2hUcmVlRGF0YUxvY2FsIgogICAgICAvPgogICAgPC9kaXY+CiAgICA8ZWwtZGl2aWRlciBjbGFzcz0iY3MtZGl2aWRlciIgLz4KICAgIDxkaXYgY2xhc3M9InRyZWUteCBjcy1zY3JvbGwiPgogICAgICA8ZGl2IHYtZm9yPSJpdGVtIGluIHRyZWVEYXRhIiA6a2V5PSJpdGVtLlN5c19Qcm9qZWN0X0lkIiBjbGFzcz0icHJvamVjdC1saXN0IiA6Y2xhc3M9InsgYWN0aXZlOiBpdGVtLlN5c19Qcm9qZWN0X0lkID09PSBBY3RpdmVfU3lzX1Byb2plY3RfSWQgfSIgQGNsaWNrPSJoYW5kbGVOb2RlQ2xpY2soaXRlbSkiPgogICAgICAgIDxlbC10b29sdGlwIGNsYXNzPSJpdGVtIiBlZmZlY3Q9ImRhcmsiIDpjb250ZW50PSJpdGVtLlNob3J0X05hbWUiIDpvcGVuLWRlbGF5PSIyMDAiIHBsYWNlbWVudD0idG9wIj4KICAgICAgICAgIDxkaXYgY2xhc3M9InByb2plY3QtaW5uZXIiPgogICAgICAgICAgICA8ZGl2PgogICAgICAgICAgICAgIDxzdmctaWNvbgogICAgICAgICAgICAgICAgaWNvbi1jbGFzcz0iaWNvbi1mb2xkZXIiCiAgICAgICAgICAgICAgICBjbGFzcy1uYW1lPSJjbGFzcy1pY29uIgogICAgICAgICAgICAgIC8+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8c3BhbiBjbGFzcz0iY29kZSI+KHt7IGl0ZW0uQ29kZSB9fSk8L3NwYW4+CiAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJuYW1lIj57eyBpdGVtLlNob3J0X05hbWUgfX08L3NwYW4+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2VsLXRvb2x0aXA+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CiAgPC9kaXY+CjwvRXhwYW5kYWJsZVNlY3Rpb24+Cg=="}, null]}
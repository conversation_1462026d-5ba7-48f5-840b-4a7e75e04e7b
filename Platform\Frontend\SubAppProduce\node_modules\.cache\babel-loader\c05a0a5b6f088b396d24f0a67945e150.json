{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\plan\\components\\OverallControlPlanContent.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\plan\\components\\OverallControlPlanContent.vue", "mtime": 1757926768460}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["ExportTotalControlPlan", "GenerateProjectPlanBusinessData", "GetConfigs", "GetMonomerListByProjectId", "GetTotalControlPlanEntity", "GetTotalControlPlanList", "SaveTotalControlPlanEntity", "SelectArea", "moment", "combineURL", "getToken", "isRouteNameExists", "name", "components", "props", "curProject", "type", "Object", "default", "data", "queryModel", "Areas", "Range", "totalProjectDuration", "remainingDuration", "config", "pageSizeOptions", "pageSize", "total", "loading", "currentPage", "tableColumns", "tableData", "keyField", "monomerList", "nodeList", "dialogVisible", "currentRow", "planForm", "Area_Id", "Deepen_Begin_Date", "Deepen_End_Date", "Deepen_Date_Range", "Deepen_Duration", "Deepen_Engineer_Quantity", "Purchase_Begin_Date", "Purchase_End_Date", "Purchase_Date_Range", "Purchase_Duration", "Purchase_Engineer_Quantity", "Product_Begin_Date", "Product_End_Date", "Product_Date_Range", "Product_Duration", "Product_Engineer_Quantity", "Install_Begin_Date", "Install_End_Date", "Install_Date_Range", "Install_Duration", "Install_Engineer_Quantity", "cloneTableData", "exportLoading", "updateLoading", "saveLoading", "headers", "Authorization", "Last_Working_Object_Id", "localStorage", "getItem", "computed", "uploadData", "_this$curProject", "JSON", "stringify", "ProjectId", "Sys_Project_Id", "CompanyId", "watch", "deep", "handler", "getMonomerList", "getTableData", "created", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getConfig", "stop", "methods", "cellStyle", "_ref", "column", "style", "field", "startsWith", "backgroundColor", "_this2", "_callee2", "res", "_callee2$", "_context2", "sent", "Data", "map", "item", "_objectSpread", "Plan_Name", "code", "Plan_Type", "icon", "require", "concat", "color", "route", "visible", "getRenderColumns", "_this3", "columns", "filter", "i", "width", "label", "otherOptions", "align", "fixed", "key", "children", "render", "row", "value", "format", "$createElement", "_toConsumableArray", "search", "_this4", "for<PERSON>ach", "length", "j", "includes", "parse", "handleTableData", "_this5", "then", "id", "finally", "_this6", "allBeginDates", "allEndDates", "beginDates", "date", "trim", "endDates", "push", "apply", "earliestDate", "sort", "latestDate", "a", "b", "Date", "begin", "end", "Math", "floor", "today", "remainingDays", "planSummary", "reduce", "sum", "parseFloat", "finishSummary", "finishPercent", "beginDate", "endDate", "duration", "toFixed", "_this7", "_callee3", "_callee3$", "_context3", "abrupt", "projectId", "openDialog", "_this8", "$set", "calculateDuration", "saveAreaPlan", "_this9", "Company_Id", "Project_Id", "$message", "success", "handleDateRangeChange", "date<PERSON><PERSON><PERSON>", "$forceUpdate", "handleClose", "uploadSuccess", "IsSucceed", "error", "Message", "window", "open", "$baseUrl", "exportFile", "_this0", "FilterTypes", "indexOf", "AreaIds", "updateData", "_this1", "tenantIds", "toDetail", "$router"], "sources": ["src/views/plan/components/OverallControlPlanContent.vue"], "sourcesContent": ["<template>\r\n  <el-card class=\"card\">\r\n    <div class=\"content\">\r\n      <div class=\"header\">\r\n        <span class=\"project-name\">{{ curProject && curProject.Short_Name }}</span>\r\n        <span>\r\n          <i class=\"el-icon-time\" />\r\n          <span class=\"label\">总工期</span>\r\n          <span class=\"value\">{{ totalProjectDuration }}天</span>\r\n        </span>\r\n        <span>\r\n          <i class=\"el-icon-time\" />\r\n          <span class=\"label\">剩余工期</span>\r\n          <span class=\"value\">{{ remainingDuration }}天</span>\r\n        </span>\r\n      </div>\r\n      <div class=\"summary\">\r\n        <div v-for=\"(item) in nodeList\" :key=\"item.Id\" class=\"block\" @click=\"toDetail(item)\">\r\n          <div class=\"top\" :style=\"{background: `linear-gradient( 135deg, ${item.color}33 0%, #D6EAFF33 100%)`}\">\r\n            <div class=\"block-name-wrap\">\r\n              <img :src=\"item.icon\" class=\"icon\">\r\n              <div class=\"block-name\">{{ item.name }}</div>\r\n            </div>\r\n            <div style=\"flex:1;height: 100%;display: flex;flex-direction: column;justify-content: center;margin-top: 8px\">\r\n              <div class=\"progress-container\">\r\n                <div class=\"progress-bar\">\r\n                  <div class=\"progress-bg\">\r\n                    <div\r\n                      class=\"progress-fill\"\r\n                      :style=\"{\r\n                        width: parseFloat(item.finishPercent) > 100 ? '100%' : item.finishPercent,\r\n                        backgroundColor: item.color\r\n                      }\"\r\n                    >\r\n                      <div\r\n                        class=\"progress-dot\"\r\n                        :style=\"{\r\n                          backgroundColor: item.color\r\n                        }\"\r\n                      />\r\n                    </div>\r\n                    <div\r\n                      class=\"progress-data\"\r\n                      :style=\"{\r\n                        left: parseFloat(item.finishPercent) > 100 ? '100%' : item.finishPercent,\r\n                        color: item.color,\r\n                        transform: parseFloat(item.finishPercent) > 80 ? 'translateX(-100%)' : 'translateX(0%)'\r\n                      }\"\r\n                    >\r\n                      <span class=\"finish-value\">完成:{{ item.finishSummary }}t</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"plan-percent\">\r\n                <div class=\"plan\">\r\n                  计划：{{ item.planSummary }}t\r\n                </div>\r\n                <div :style=\"{color:item.color,fontWeight:'bold'}\">\r\n                  {{ item.finishPercent }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"bottom\">\r\n            <span>总：{{ item.duration }}</span>\r\n            <span style=\"margin-left: 14px\"> <i class=\"el-icon-date\" /> {{ item.beginDate }} - {{ item.endDate }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <el-form ref=\"form\" inline style=\"display: flex;align-items: center\">\r\n        <el-form-item>\r\n          <el-upload\r\n            :action=\"$baseUrl + 'PRO/ControlPlan/ImportTotalPlan'\"\r\n            :show-file-list=\"false\"\r\n            :on-success=\"uploadSuccess\"\r\n            :headers=\"headers\"\r\n            :data=\"uploadData\"\r\n          >\r\n            <el-button type=\"primary\">导入总控计划</el-button>\r\n          </el-upload>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" :loading=\"exportLoading\" @click=\"exportFile()\">导出总控计划</el-button>\r\n        </el-form-item>\r\n        <el-form-item style=\"margin-right: auto\">\r\n          <el-button type=\"primary\" :loading=\"updateLoading\" @click=\"updateData()\">手动更新计划</el-button>\r\n        </el-form-item>\r\n        <el-form-item label=\"区域\">\r\n          <SelectArea v-model=\"queryModel.Areas\" :project-id=\"curProject.Sys_Project_Id\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"显示范围\">\r\n          <el-select v-model=\"queryModel.Range\" multiple clearable collapse-tags>\r\n            <el-option v-for=\"item in nodeList\" :key=\"item.code\" :label=\"`过滤${item.name}完成`\" :value=\"item.code\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"search\">搜索</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <bt-table\r\n        v-if=\"nodeList && nodeList.length\"\r\n        class=\"bt-table\"\r\n        :config=\"config\"\r\n        :header-cell-style=\"cellStyle\"\r\n      >\r\n        <template #actions=\"{row}\">\r\n          <el-button type=\"text\" @click=\"openDialog(row)\">编辑</el-button>\r\n        </template>\r\n      </bt-table>\r\n    </div>\r\n\r\n    <!-- 编辑计划弹窗 -->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      title=\"编辑计划\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"1200px\"\r\n      :before-close=\"handleClose\"\r\n    >\r\n      <el-form ref=\"planForm\" :model=\"planForm\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"单体/分区\">\r\n              <span>{{ currentRow.FullAreaName }}</span>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 深化设计 -->\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <div class=\"phase-label\">{{ nodeList[0] && nodeList[0].name }}</div>\r\n          </el-col>\r\n          <el-col :span=\"10\">\r\n            <el-form-item label=\"计划时间\">\r\n              <el-date-picker\r\n                v-model=\"planForm.Deepen_Date_Range\"\r\n                type=\"daterange\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始日期\"\r\n                end-placeholder=\"结束日期\"\r\n                format=\"yyyy-MM-dd\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                @change=\"(val) => handleDateRangeChange('Deepen', val)\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"总工程量(t)\">\r\n              <el-input v-model=\"planForm.Deepen_Engineer_Quantity\" type=\"number\" placeholder=\"请输入\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-form-item label=\"总工期\">\r\n              <span>{{ planForm.Deepen_Duration }}天</span>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 材料采购 -->\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <div class=\"phase-label\">{{ nodeList[1] && nodeList[1].name }}</div>\r\n          </el-col>\r\n          <el-col :span=\"10\">\r\n            <el-form-item label=\"计划时间\">\r\n              <el-date-picker\r\n                v-model=\"planForm.Purchase_Date_Range\"\r\n                type=\"daterange\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始日期\"\r\n                end-placeholder=\"结束日期\"\r\n                format=\"yyyy-MM-dd\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                @change=\"(val) => handleDateRangeChange('Purchase', val)\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"总工程量(t)\">\r\n              <el-input v-model=\"planForm.Purchase_Engineer_Quantity\" placeholder=\"请输入\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-form-item label=\"总工期\">\r\n              <span>{{ planForm.Purchase_Duration }}天</span>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 生产加工 -->\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <div class=\"phase-label\">{{ nodeList[2] && nodeList[2].name }}</div>\r\n          </el-col>\r\n          <el-col :span=\"10\">\r\n            <el-form-item label=\"计划时间\">\r\n              <el-date-picker\r\n                v-model=\"planForm.Product_Date_Range\"\r\n                type=\"daterange\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始日期\"\r\n                end-placeholder=\"结束日期\"\r\n                format=\"yyyy-MM-dd\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                @change=\"(val) => handleDateRangeChange('Product', val)\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"总工程量(t)\">\r\n              <el-input v-model=\"planForm.Product_Engineer_Quantity\" placeholder=\"请输入\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-form-item label=\"总工期\">\r\n              <span>{{ planForm.Product_Duration }}天</span>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 施工安装 -->\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <div class=\"phase-label\">{{ nodeList[3] && nodeList[3].name }}</div>\r\n          </el-col>\r\n          <el-col :span=\"10\">\r\n            <el-form-item label=\"计划时间\">\r\n              <el-date-picker\r\n                v-model=\"planForm.Install_Date_Range\"\r\n                type=\"daterange\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始日期\"\r\n                end-placeholder=\"结束日期\"\r\n                format=\"yyyy-MM-dd\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                @change=\"(val) => handleDateRangeChange('Install', val)\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"总工程量(t)\">\r\n              <el-input v-model=\"planForm.Install_Engineer_Quantity\" placeholder=\"请输入\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-form-item label=\"总工期\">\r\n              <span>{{ planForm.Install_Duration }}天</span>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" :loading=\"saveLoading\" @click=\"saveAreaPlan\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </el-card>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  ExportTotalControlPlan, GenerateProjectPlanBusinessData,\r\n  GetConfigs,\r\n  GetMonomerListByProjectId,\r\n  GetTotalControlPlanEntity,\r\n  GetTotalControlPlanList,\r\n  SaveTotalControlPlanEntity\r\n} from '@/api/plm/projects'\r\nimport SelectArea from '@/components/Select/SelectArea/index.vue'\r\nimport moment from 'moment'\r\nimport { combineURL } from '@/utils'\r\nimport { getToken } from '@/utils/auth'\r\nimport { isRouteNameExists } from '@/utils/router'\r\n\r\nexport default {\r\n  name: 'OverallControlPlanContent',\r\n  components: { SelectArea },\r\n  props: {\r\n    curProject: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      queryModel: {\r\n        Areas: [],\r\n        Range: []\r\n      },\r\n      // 总工期和剩余工期\r\n      totalProjectDuration: 0,\r\n      remainingDuration: 0,\r\n      config: {\r\n        pageSizeOptions: [10, 20, 50, 100],\r\n        pageSize: 20,\r\n        total: 0,\r\n        loading: false,\r\n        currentPage: 1,\r\n        tableColumns: [],\r\n        tableData: [],\r\n        keyField: 'Area_Id'\r\n      },\r\n      monomerList: [],\r\n      nodeList: [],\r\n      // 弹窗相关数据\r\n      dialogVisible: false,\r\n      currentRow: {},\r\n      planForm: {\r\n        Area_Id: '',\r\n        Deepen_Begin_Date: '',\r\n        Deepen_End_Date: '',\r\n        Deepen_Date_Range: [],\r\n        Deepen_Duration: 0,\r\n        Deepen_Engineer_Quantity: 0,\r\n        Purchase_Begin_Date: '',\r\n        Purchase_End_Date: '',\r\n        Purchase_Date_Range: [],\r\n        Purchase_Duration: 0,\r\n        Purchase_Engineer_Quantity: 0,\r\n        Product_Begin_Date: '',\r\n        Product_End_Date: '',\r\n        Product_Date_Range: [],\r\n        Product_Duration: 0,\r\n        Product_Engineer_Quantity: 0,\r\n        Install_Begin_Date: '',\r\n        Install_End_Date: '',\r\n        Install_Date_Range: [],\r\n        Install_Duration: 0,\r\n        Install_Engineer_Quantity: 0\r\n      },\r\n      cloneTableData: [],\r\n      exportLoading: false,\r\n      updateLoading: false,\r\n      saveLoading: false,\r\n      headers: {\r\n        Authorization: getToken(),\r\n        Last_Working_Object_Id: localStorage.getItem('Last_Working_Object_Id')\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    uploadData() {\r\n      return { data: JSON.stringify({ ProjectId: this.curProject?.Sys_Project_Id, CompanyId: localStorage.getItem('CurReferenceId') }) }\r\n    }\r\n  },\r\n  watch: {\r\n    curProject: {\r\n      deep: true,\r\n      handler: function() {\r\n        if (this.curProject && this.curProject.Sys_Project_Id) {\r\n          this.queryModel.Areas = []\r\n          this.getMonomerList()\r\n          this.getTableData()\r\n        }\r\n      }\r\n    }\r\n  },\r\n  async created() {\r\n    await this.getConfig()\r\n    if (this.curProject && this.curProject.Sys_Project_Id) {\r\n      this.getMonomerList()\r\n      this.getTableData()\r\n    }\r\n  },\r\n  methods: {\r\n    cellStyle({ column }) {\r\n      const style = {}\r\n      if (column.field && column.field.startsWith('Deepen')) {\r\n        style.backgroundColor = `rgba(41, 141, 255, 0.1)!important`\r\n      }\r\n      if (column.field && column.field.startsWith('Purchase')) {\r\n        style.backgroundColor = `rgba(189, 109, 246, 0.1)!important`\r\n      }\r\n      if (column.field && column.field.startsWith('Product')) {\r\n        style.backgroundColor = `rgba(0, 195, 97, 0.1)!important`\r\n      }\r\n      if (column.field && column.field.startsWith('Install')) {\r\n        style.backgroundColor = `rgba(0, 177, 191, 0.1)!important`\r\n      }\r\n      return style\r\n    },\r\n    async getConfig() {\r\n      const res = await GetConfigs({\r\n        CompanyId: localStorage.getItem('CurReferenceId')\r\n      })\r\n      this.nodeList = res.Data.map(item => {\r\n        return {\r\n          ...item,\r\n          name: item.Plan_Name,\r\n          code: ['Deepen', 'Purchase', 'Product', 'Install'][item.Plan_Type - 1],\r\n          icon: require(`@/assets/PLM/plan_summary_${item.Plan_Type}.png`),\r\n          color: ['#298DFF', '#BD6DF6', '#00C361', '#00B1BF'][item.Plan_Type - 1],\r\n          route: ['PRODeepenPlanTrackCom', 'PROPurchasePlanTrackCom', 'PROProducePlanTrackCom', 'PROConstructionPlanTrackCom'][item.Plan_Type - 1],\r\n          visible: true\r\n        }\r\n      })\r\n      this.getRenderColumns()\r\n    },\r\n    getRenderColumns() {\r\n      const columns = this.nodeList.filter(i => i.visible).map(item => {\r\n        return {\r\n          width: 150,\r\n          label: item.name,\r\n          otherOptions: { align: 'center', fixed: '' },\r\n          key: item.code,\r\n          children: [\r\n            { key: item.code + '_Begin_Date', width: '160',\r\n              label: '开始时间', otherOptions: { align: 'center' },\r\n              render: (row) => {\r\n                if (!row[item.code + '_Begin_Date']) return '-'\r\n                const value = moment(row[item.code + '_Begin_Date']).format('YYYY-MM-DD')\r\n                return this.$createElement('div', {}, value)\r\n              }\r\n            },\r\n            { key: item.code + '_End_Date', width: '160',\r\n              label: '结束时间', otherOptions: { align: 'center' },\r\n              render: (row) => {\r\n                if (!row[item.code + '_End_Date']) return '-'\r\n                const value = moment(row[item.code + '_End_Date']).format('YYYY-MM-DD')\r\n                return this.$createElement('div', { }, value)\r\n              }\r\n            },\r\n            { key: item.code + '_Duration', width: '160',\r\n              label: '总工期(天)', otherOptions: { align: 'center' }\r\n            },\r\n            { key: item.code + '_Engineer_Quantity', width: '160',\r\n              label: '计划工程量(t)', otherOptions: { align: 'center' }\r\n            },\r\n            { key: item.code + '_Finish_Quantity', width: '160',\r\n              label: '实际量(t)', otherOptions: { align: 'center' }\r\n            },\r\n            { key: item.code + '_Finish_State', width: '160',\r\n              label: '完成状态', otherOptions: { align: 'center' },\r\n              render: (row) => {\r\n                const value = row[item.code + '_Finish_State']\r\n                return this.$createElement('div', {\r\n                  style: {\r\n                    color: value === '已完成' ? '#67C23A' : value === '未完成' ? '#F56C6C' : ''\r\n                  }\r\n                }, value)\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      })\r\n      this.config.tableColumns = [{\r\n        width: 150, label: '单体/分区', key: 'FullAreaName', otherOptions: { align: 'left', fixed: 'left' }\r\n      }, ...columns]\r\n    },\r\n    search() {\r\n      // 过滤渲染列\r\n      this.nodeList.forEach(i => i.visible = true)\r\n      if (this.queryModel.Range && this.queryModel.Range.length > 0) {\r\n        this.queryModel.Range.forEach(i => {\r\n          this.nodeList.forEach(j => {\r\n            if (i === j.code) {\r\n              j.visible = false\r\n            }\r\n          })\r\n        })\r\n      }\r\n      this.getRenderColumns()\r\n      // 过滤区域\r\n      if (this.queryModel.Areas && this.queryModel.Areas.length > 0) {\r\n        this.config.tableData = this.cloneTableData.filter(item => {\r\n          return this.queryModel.Areas.includes(item.Area_Id)\r\n        })\r\n      } else {\r\n        this.config.tableData = JSON.parse(JSON.stringify(this.cloneTableData))\r\n      }\r\n      this.handleTableData()\r\n    },\r\n    getTableData() {\r\n      if (!this.curProject || !this.curProject.Sys_Project_Id) return\r\n      this.config.loading = true\r\n      GetTotalControlPlanList({\r\n        ProjectId: this.curProject.Sys_Project_Id,\r\n        CompanyId: localStorage.getItem('CurReferenceId')\r\n      }).then(res => {\r\n        this.config.tableData = res.Data.map(item => {\r\n          item.id = item.Area_Id\r\n          return item\r\n        })\r\n        this.cloneTableData = JSON.parse(JSON.stringify(res.Data))\r\n        this.handleTableData()\r\n      }).finally(() => {\r\n        this.config.loading = false\r\n      })\r\n    },\r\n    handleTableData() {\r\n      // 收集所有日期用于计算总工期和剩余工期\r\n      const allBeginDates = []\r\n      const allEndDates = []\r\n      this.totalProjectDuration = 0\r\n      this.remainingDuration = 0\r\n      this.nodeList.forEach(item => {\r\n        const beginDates = this.config.tableData\r\n          .map(row => row[item.code + '_Begin_Date'])\r\n          .filter(date => date && date.trim() !== '')\r\n        const endDates = this.config.tableData\r\n          .map(row => row[item.code + '_End_Date'])\r\n          .filter(date => date && date.trim() !== '')\r\n\r\n        allBeginDates.push(...beginDates)\r\n        allEndDates.push(...endDates)\r\n      })\r\n      // 计算项目总工期：最晚日期 - 最早日期 + 1\r\n      if (allBeginDates.length > 0 && allEndDates.length > 0) {\r\n        const earliestDate = allBeginDates.sort()[0]\r\n        const latestDate = allEndDates.sort((a, b) => new Date(b) - new Date(a))[0]\r\n\r\n        if (earliestDate && latestDate) {\r\n          const begin = new Date(earliestDate)\r\n          const end = new Date(latestDate)\r\n          this.totalProjectDuration = Math.floor((end - begin) / (1000 * 60 * 60 * 24)) + 1\r\n          // 计算剩余工期：最晚日期 - 当前日期 + 1\r\n          const today = new Date()\r\n          const remainingDays = Math.floor((end - today) / (1000 * 60 * 60 * 24)) + 1\r\n          this.remainingDuration = remainingDays > 0 ? remainingDays : 0\r\n        }\r\n      }\r\n\r\n      this.nodeList = this.nodeList.map(item => {\r\n        // 计算计划工程量汇总\r\n        const planSummary = this.config.tableData.reduce((sum, row) => {\r\n          const value = parseFloat(row[item.code + '_Engineer_Quantity']) || 0\r\n          return sum + value\r\n        }, 0)\r\n\r\n        // 计算实际完成量汇总\r\n        const finishSummary = this.config.tableData.reduce((sum, row) => {\r\n          const value = parseFloat(row[item.code + '_Finish_Quantity']) || 0\r\n          return sum + value\r\n        }, 0)\r\n\r\n        // 计算完成百分比\r\n        const finishPercent = planSummary > 0 ? (finishSummary / planSummary * 100) : 0\r\n\r\n        // 获取最早开始日期（排除空值）\r\n        const beginDates = this.config.tableData\r\n          .map(row => row[item.code + '_Begin_Date'])\r\n          .filter(date => date && date.trim() !== '')\r\n          .sort()\r\n        const beginDate = beginDates.length > 0 ? moment(beginDates[0]).format('YYYY/MM/DD') : ''\r\n\r\n        // 获取最晚结束日期（排除空值）\r\n        const endDates = this.config.tableData\r\n          .map(row => row[item.code + '_End_Date'])\r\n          .filter(date => date && date.trim() !== '')\r\n          .sort((a, b) => new Date(b) - new Date(a))\r\n        const endDate = endDates.length > 0 ? moment(endDates[0]).format('YYYY/MM/DD') : ''\r\n\r\n        // 计算总工期\r\n        let duration = 0\r\n        if (beginDate && endDate) {\r\n          const begin = new Date(beginDate)\r\n          const end = new Date(endDate)\r\n          duration = Math.floor((end - begin) / (1000 * 60 * 60 * 24)) + 1\r\n        }\r\n\r\n        return {\r\n          ...item,\r\n          planSummary: planSummary.toFixed(2),\r\n          finishSummary: finishSummary.toFixed(2),\r\n          finishPercent: finishPercent.toFixed(2) + '%',\r\n          beginDate: beginDate,\r\n          endDate: endDate,\r\n          duration: duration > 0 ? duration + '天' : ''\r\n        }\r\n      })\r\n    },\r\n    async getMonomerList() {\r\n      if (!this.curProject || !this.curProject.Sys_Project_Id) return\r\n      const res = await GetMonomerListByProjectId({\r\n        projectId: this.curProject.Sys_Project_Id\r\n      })\r\n      this.monomerList = res.Data\r\n    },\r\n    openDialog(row) {\r\n      this.currentRow = row\r\n      this.dialogVisible = true\r\n      GetTotalControlPlanEntity({\r\n        Area_Id: row.Area_Id\r\n      }).then(res => {\r\n        if (res.Data) {\r\n          this.planForm = { ...res.Data }\r\n          // 将开始和结束日期组合成日期范围\r\n          this.$set(this.planForm, 'Deepen_Date_Range', [res.Data.Deepen_Begin_Date || '', res.Data.Deepen_End_Date || ''])\r\n          this.$set(this.planForm, 'Purchase_Date_Range', [res.Data.Purchase_Begin_Date || '', res.Data.Purchase_End_Date || ''])\r\n          this.$set(this.planForm, 'Product_Date_Range', [res.Data.Product_Begin_Date || '', res.Data.Product_End_Date || ''])\r\n          this.$set(this.planForm, 'Install_Date_Range', [res.Data.Install_Begin_Date || '', res.Data.Install_End_Date || ''])\r\n          // 重新计算各阶段工期\r\n          this.calculateDuration('Deepen')\r\n          this.calculateDuration('Purchase')\r\n          this.calculateDuration('Product')\r\n          this.calculateDuration('Install')\r\n        }\r\n      })\r\n    },\r\n    saveAreaPlan() {\r\n      this.saveLoading = true\r\n      SaveTotalControlPlanEntity({\r\n        ...this.planForm,\r\n        Area_Id: this.currentRow.Area_Id,\r\n        Company_Id: localStorage.getItem('CurReferenceId'),\r\n        Project_Id: this.curProject.Sys_Project_Id\r\n      }).then(res => {\r\n        this.$message.success('保存成功')\r\n        this.dialogVisible = false\r\n        this.getTableData()\r\n      }).finally(() => {\r\n        this.saveLoading = false\r\n      })\r\n    },\r\n    handleDateRangeChange(type, dateRange) {\r\n      if (dateRange && dateRange.length === 2) {\r\n        this.$set(this.planForm, `${type}_Begin_Date`, dateRange[0])\r\n        this.$set(this.planForm, `${type}_End_Date`, dateRange[1])\r\n        this.$set(this.planForm, `${type}_Date_Range`, dateRange)\r\n        this.calculateDuration(type)\r\n      } else {\r\n        this.$set(this.planForm, `${type}_Begin_Date`, '')\r\n        this.$set(this.planForm, `${type}_End_Date`, '')\r\n        this.$set(this.planForm, `${type}_Date_Range`, [])\r\n        this.$set(this.planForm, `${type}_Duration`, 0)\r\n      }\r\n      // 强制更新视图\r\n      this.$forceUpdate()\r\n    },\r\n    calculateDuration(type) {\r\n      const beginDate = this.planForm[`${type}_Begin_Date`]\r\n      const endDate = this.planForm[`${type}_End_Date`]\r\n      if (beginDate && endDate) {\r\n        const begin = new Date(beginDate)\r\n        const end = new Date(endDate)\r\n        // 计算天数差并加1（包含开始和结束当天）\r\n        const duration = Math.floor((end - begin) / (1000 * 60 * 60 * 24)) + 1\r\n        this.$set(this.planForm, `${type}_Duration`, duration > 0 ? duration : 0)\r\n      } else {\r\n        this.$set(this.planForm, `${type}_Duration`, 0)\r\n      }\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n      this.planForm = {\r\n        Area_Id: '',\r\n        Deepen_Begin_Date: '',\r\n        Deepen_End_Date: '',\r\n        Deepen_Date_Range: [],\r\n        Deepen_Duration: 0,\r\n        Deepen_Engineer_Quantity: 0,\r\n        Purchase_Begin_Date: '',\r\n        Purchase_End_Date: '',\r\n        Purchase_Date_Range: [],\r\n        Purchase_Duration: 0,\r\n        Purchase_Engineer_Quantity: 0,\r\n        Product_Begin_Date: '',\r\n        Product_End_Date: '',\r\n        Product_Date_Range: [],\r\n        Product_Duration: 0,\r\n        Product_Engineer_Quantity: 0,\r\n        Install_Begin_Date: '',\r\n        Install_End_Date: '',\r\n        Install_Date_Range: [],\r\n        Install_Duration: 0,\r\n        Install_Engineer_Quantity: 0\r\n      }\r\n    },\r\n    uploadSuccess(res) {\r\n      if (res.IsSucceed) {\r\n        this.$message.success('导入成功')\r\n        this.getTableData()\r\n      } else {\r\n        this.$message.error(res.Message)\r\n        if (res.Data) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        }\r\n      }\r\n    },\r\n    exportFile() {\r\n      this.exportLoading = true\r\n      ExportTotalControlPlan({\r\n        CompanyId: localStorage.getItem('CurReferenceId'),\r\n        ProjectId: this.curProject.Sys_Project_Id,\r\n        FilterTypes: this.queryModel.Range.map(item => {\r\n          return ['Deepen', 'Purchase', 'Product', 'Install'].indexOf(item) + 1\r\n        }),\r\n        AreaIds: this.queryModel.Areas\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      }).finally(() => {\r\n        this.exportLoading = false\r\n      })\r\n    },\r\n    updateData() {\r\n      this.updateLoading = true\r\n      GenerateProjectPlanBusinessData({\r\n        tenantIds: localStorage.getItem('tenant')\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message.success('更新成功')\r\n          this.getTableData()\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      }).finally(() => {\r\n        this.updateLoading = false\r\n      })\r\n    },\r\n    toDetail(item) {\r\n      if (isRouteNameExists(item.route)) {\r\n        this.$router.push({\r\n          name: item.route\r\n        })\r\n      } else {\r\n        this.$message.error('当前账户无访问权限')\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.card{\r\n  flex:1;\r\n  height: 100%;\r\n  margin-left: 16px;\r\n  .content{\r\n    display: flex;\r\n    flex-direction: column;\r\n    height: 100%;\r\n  }\r\n  .bt-table{\r\n    flex:1;\r\n  }\r\n}\r\n.header{\r\n  display: flex;\r\n  align-items: flex-end;\r\n  .project-name{\r\n    font-size: 16px;\r\n    color: #333333;\r\n    font-weight: bold;\r\n    margin-right: 8px;\r\n  }\r\n  .el-icon-time{\r\n    margin-left: 8px;\r\n    margin-right: 4px;\r\n    font-size: 14px;\r\n  }\r\n  .label{\r\n    color: #333333;\r\n    font-size: 12px;\r\n  }\r\n  .value{\r\n    font-size: 14px;\r\n    color: #333333;\r\n    font-weight: bold;\r\n    margin-left: 7px;\r\n  }\r\n}\r\n\r\n.phase-label {\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  color: #333;\r\n  line-height: 40px;\r\n  text-align: center;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n.el-row {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.el-form-item {\r\n  margin-bottom: 0;\r\n}\r\n.summary{\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  column-gap: 12px;\r\n  margin: 12px 0;\r\n  .block{\r\n    height: 105px;\r\n    background: #FFFFFF;\r\n    border-radius: 4px 4px 4px 4px;\r\n    border: 1px solid #E2E4E9;\r\n    flex: 1;\r\n    cursor: pointer;\r\n    .icon{\r\n      width: 48px;\r\n      height: 48px;\r\n    }\r\n    .finish{\r\n      font-weight: bold;\r\n      font-size: 12px;\r\n      text-align: center;\r\n    }\r\n    .progress-container {\r\n       margin-bottom: 8px;\r\n     }\r\n    .progress-bar {\r\n       width: 100%;\r\n       height: 2px;\r\n     }\r\n     .progress-bg {\r\n       width: 100%;\r\n       height: 100%;\r\n       background-color: #f0f0f0;\r\n       border-radius: 1px;\r\n       overflow: visible;\r\n       position: relative;\r\n     }\r\n     .progress-fill {\r\n       height: 100%;\r\n       border-radius: 1px;\r\n       transition: width 0.3s ease;\r\n       min-width: 2px;\r\n       position: relative;\r\n     }\r\n     .progress-dot {\r\n        position: absolute;\r\n        right: -3px;\r\n        top: -2px;\r\n        width: 6px;\r\n        height: 6px;\r\n        border-radius: 50%;\r\n        transition: all 0.3s ease;\r\n      }\r\n      .progress-data {\r\n        position: absolute;\r\n        top: -20px;\r\n        font-size: 14px;\r\n        font-weight: bold;\r\n        white-space: nowrap;\r\n        transition: all 0.3s ease;\r\n        pointer-events: none;\r\n      }\r\n    .top{\r\n      height: 76px;\r\n      display: flex;\r\n      padding: 0 15px 0 8px;\r\n      .block-name-wrap{\r\n        width: 72px;\r\n        text-align: center;\r\n        margin-right: 8px;\r\n        height: 100%;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n      }\r\n      .block-name{\r\n        color: #666666;\r\n        font-size: 14px;\r\n        font-weight: bold;\r\n        position: relative;\r\n        top: -6px;\r\n        // 超出省略号\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n      .plan-percent{\r\n        font-size: 14px;\r\n        display: flex;\r\n        justify-content: space-between;\r\n      }\r\n      .plan{\r\n        color: #666666;\r\n      }\r\n    }\r\n    .bottom{\r\n      height: 29px;\r\n      line-height: 29px;\r\n      color: #666666;\r\n      font-size: 14px;\r\n      padding: 0 12px;\r\n    }\r\n  }\r\n}\r\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2QA,SACAA,sBAAA,EAAAC,+BAAA,EACAC,UAAA,EACAC,yBAAA,EACAC,yBAAA,EACAC,uBAAA,EACAC,0BAAA,QACA;AACA,OAAAC,UAAA;AACA,OAAAC,MAAA;AACA,SAAAC,UAAA;AACA,SAAAC,QAAA;AACA,SAAAC,iBAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAN,UAAA,EAAAA;EAAA;EACAO,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;QACAC,KAAA;QACAC,KAAA;MACA;MACA;MACAC,oBAAA;MACAC,iBAAA;MACAC,MAAA;QACAC,eAAA;QACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,WAAA;QACAC,YAAA;QACAC,SAAA;QACAC,QAAA;MACA;MACAC,WAAA;MACAC,QAAA;MACA;MACAC,aAAA;MACAC,UAAA;MACAC,QAAA;QACAC,OAAA;QACAC,iBAAA;QACAC,eAAA;QACAC,iBAAA;QACAC,eAAA;QACAC,wBAAA;QACAC,mBAAA;QACAC,iBAAA;QACAC,mBAAA;QACAC,iBAAA;QACAC,0BAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,yBAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,yBAAA;MACA;MACAC,cAAA;MACAC,aAAA;MACAC,aAAA;MACAC,WAAA;MACAC,OAAA;QACAC,aAAA,EAAAvD,QAAA;QACAwD,sBAAA,EAAAC,YAAA,CAAAC,OAAA;MACA;IACA;EACA;EACAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,gBAAA;MACA;QAAApD,IAAA,EAAAqD,IAAA,CAAAC,SAAA;UAAAC,SAAA,GAAAH,gBAAA,QAAAxD,UAAA,cAAAwD,gBAAA,uBAAAA,gBAAA,CAAAI,cAAA;UAAAC,SAAA,EAAAT,YAAA,CAAAC,OAAA;QAAA;MAAA;IACA;EACA;EACAS,KAAA;IACA9D,UAAA;MACA+D,IAAA;MACAC,OAAA,WAAAA,QAAA;QACA,SAAAhE,UAAA,SAAAA,UAAA,CAAA4D,cAAA;UACA,KAAAvD,UAAA,CAAAC,KAAA;UACA,KAAA2D,cAAA;UACA,KAAAC,YAAA;QACA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACAT,KAAA,CAAAU,SAAA;UAAA;YACA,IAAAV,KAAA,CAAApE,UAAA,IAAAoE,KAAA,CAAApE,UAAA,CAAA4D,cAAA;cACAQ,KAAA,CAAAH,cAAA;cACAG,KAAA,CAAAF,YAAA;YACA;UAAA;UAAA;YAAA,OAAAS,QAAA,CAAAI,IAAA;QAAA;MAAA,GAAAP,OAAA;IAAA;EACA;EACAQ,OAAA;IACAC,SAAA,WAAAA,UAAAC,IAAA;MAAA,IAAAC,MAAA,GAAAD,IAAA,CAAAC,MAAA;MACA,IAAAC,KAAA;MACA,IAAAD,MAAA,CAAAE,KAAA,IAAAF,MAAA,CAAAE,KAAA,CAAAC,UAAA;QACAF,KAAA,CAAAG,eAAA;MACA;MACA,IAAAJ,MAAA,CAAAE,KAAA,IAAAF,MAAA,CAAAE,KAAA,CAAAC,UAAA;QACAF,KAAA,CAAAG,eAAA;MACA;MACA,IAAAJ,MAAA,CAAAE,KAAA,IAAAF,MAAA,CAAAE,KAAA,CAAAC,UAAA;QACAF,KAAA,CAAAG,eAAA;MACA;MACA,IAAAJ,MAAA,CAAAE,KAAA,IAAAF,MAAA,CAAAE,KAAA,CAAAC,UAAA;QACAF,KAAA,CAAAG,eAAA;MACA;MACA,OAAAH,KAAA;IACA;IACAN,SAAA,WAAAA,UAAA;MAAA,IAAAU,MAAA;MAAA,OAAAnB,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkB,SAAA;QAAA,IAAAC,GAAA;QAAA,OAAApB,mBAAA,GAAAG,IAAA,UAAAkB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhB,IAAA,GAAAgB,SAAA,CAAAf,IAAA;YAAA;cAAAe,SAAA,CAAAf,IAAA;cAAA,OACA1F,UAAA;gBACA0E,SAAA,EAAAT,YAAA,CAAAC,OAAA;cACA;YAAA;cAFAqC,GAAA,GAAAE,SAAA,CAAAC,IAAA;cAGAL,MAAA,CAAApE,QAAA,GAAAsE,GAAA,CAAAI,IAAA,CAAAC,GAAA,WAAAC,IAAA;gBACA,OAAAC,aAAA,CAAAA,aAAA,KACAD,IAAA;kBACAnG,IAAA,EAAAmG,IAAA,CAAAE,SAAA;kBACAC,IAAA,+CAAAH,IAAA,CAAAI,SAAA;kBACAC,IAAA,EAAAC,OAAA,8BAAAC,MAAA,CAAAP,IAAA,CAAAI,SAAA;kBACAI,KAAA,+CAAAR,IAAA,CAAAI,SAAA;kBACAK,KAAA,gHAAAT,IAAA,CAAAI,SAAA;kBACAM,OAAA;gBAAA;cAEA;cACAlB,MAAA,CAAAmB,gBAAA;YAAA;YAAA;cAAA,OAAAf,SAAA,CAAAb,IAAA;UAAA;QAAA,GAAAU,QAAA;MAAA;IACA;IACAkB,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,OAAA,QAAAzF,QAAA,CAAA0F,MAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAL,OAAA;MAAA,GAAAX,GAAA,WAAAC,IAAA;QACA;UACAgB,KAAA;UACAC,KAAA,EAAAjB,IAAA,CAAAnG,IAAA;UACAqH,YAAA;YAAAC,KAAA;YAAAC,KAAA;UAAA;UACAC,GAAA,EAAArB,IAAA,CAAAG,IAAA;UACAmB,QAAA,GACA;YAAAD,GAAA,EAAArB,IAAA,CAAAG,IAAA;YAAAa,KAAA;YACAC,KAAA;YAAAC,YAAA;cAAAC,KAAA;YAAA;YACAI,MAAA,WAAAA,OAAAC,GAAA;cACA,KAAAA,GAAA,CAAAxB,IAAA,CAAAG,IAAA;cACA,IAAAsB,KAAA,GAAAhI,MAAA,CAAA+H,GAAA,CAAAxB,IAAA,CAAAG,IAAA,mBAAAuB,MAAA;cACA,OAAAd,MAAA,CAAAe,cAAA,YAAAF,KAAA;YACA;UACA,GACA;YAAAJ,GAAA,EAAArB,IAAA,CAAAG,IAAA;YAAAa,KAAA;YACAC,KAAA;YAAAC,YAAA;cAAAC,KAAA;YAAA;YACAI,MAAA,WAAAA,OAAAC,GAAA;cACA,KAAAA,GAAA,CAAAxB,IAAA,CAAAG,IAAA;cACA,IAAAsB,KAAA,GAAAhI,MAAA,CAAA+H,GAAA,CAAAxB,IAAA,CAAAG,IAAA,iBAAAuB,MAAA;cACA,OAAAd,MAAA,CAAAe,cAAA,YAAAF,KAAA;YACA;UACA,GACA;YAAAJ,GAAA,EAAArB,IAAA,CAAAG,IAAA;YAAAa,KAAA;YACAC,KAAA;YAAAC,YAAA;cAAAC,KAAA;YAAA;UACA,GACA;YAAAE,GAAA,EAAArB,IAAA,CAAAG,IAAA;YAAAa,KAAA;YACAC,KAAA;YAAAC,YAAA;cAAAC,KAAA;YAAA;UACA,GACA;YAAAE,GAAA,EAAArB,IAAA,CAAAG,IAAA;YAAAa,KAAA;YACAC,KAAA;YAAAC,YAAA;cAAAC,KAAA;YAAA;UACA,GACA;YAAAE,GAAA,EAAArB,IAAA,CAAAG,IAAA;YAAAa,KAAA;YACAC,KAAA;YAAAC,YAAA;cAAAC,KAAA;YAAA;YACAI,MAAA,WAAAA,OAAAC,GAAA;cACA,IAAAC,KAAA,GAAAD,GAAA,CAAAxB,IAAA,CAAAG,IAAA;cACA,OAAAS,MAAA,CAAAe,cAAA;gBACAvC,KAAA;kBACAoB,KAAA,EAAAiB,KAAA,yBAAAA,KAAA;gBACA;cACA,GAAAA,KAAA;YACA;UACA;QAEA;MACA;MACA,KAAA/G,MAAA,CAAAM,YAAA;QACAgG,KAAA;QAAAC,KAAA;QAAAI,GAAA;QAAAH,YAAA;UAAAC,KAAA;UAAAC,KAAA;QAAA;MACA,GAAAb,MAAA,CAAAqB,kBAAA,CAAAf,OAAA;IACA;IACAgB,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAA1G,QAAA,CAAA2G,OAAA,WAAAhB,CAAA;QAAA,OAAAA,CAAA,CAAAL,OAAA;MAAA;MACA,SAAArG,UAAA,CAAAE,KAAA,SAAAF,UAAA,CAAAE,KAAA,CAAAyH,MAAA;QACA,KAAA3H,UAAA,CAAAE,KAAA,CAAAwH,OAAA,WAAAhB,CAAA;UACAe,MAAA,CAAA1G,QAAA,CAAA2G,OAAA,WAAAE,CAAA;YACA,IAAAlB,CAAA,KAAAkB,CAAA,CAAA9B,IAAA;cACA8B,CAAA,CAAAvB,OAAA;YACA;UACA;QACA;MACA;MACA,KAAAC,gBAAA;MACA;MACA,SAAAtG,UAAA,CAAAC,KAAA,SAAAD,UAAA,CAAAC,KAAA,CAAA0H,MAAA;QACA,KAAAtH,MAAA,CAAAO,SAAA,QAAA4B,cAAA,CAAAiE,MAAA,WAAAd,IAAA;UACA,OAAA8B,MAAA,CAAAzH,UAAA,CAAAC,KAAA,CAAA4H,QAAA,CAAAlC,IAAA,CAAAxE,OAAA;QACA;MACA;QACA,KAAAd,MAAA,CAAAO,SAAA,GAAAwC,IAAA,CAAA0E,KAAA,CAAA1E,IAAA,CAAAC,SAAA,MAAAb,cAAA;MACA;MACA,KAAAuF,eAAA;IACA;IACAlE,YAAA,WAAAA,aAAA;MAAA,IAAAmE,MAAA;MACA,UAAArI,UAAA,UAAAA,UAAA,CAAA4D,cAAA;MACA,KAAAlD,MAAA,CAAAI,OAAA;MACAxB,uBAAA;QACAqE,SAAA,OAAA3D,UAAA,CAAA4D,cAAA;QACAC,SAAA,EAAAT,YAAA,CAAAC,OAAA;MACA,GAAAiF,IAAA,WAAA5C,GAAA;QACA2C,MAAA,CAAA3H,MAAA,CAAAO,SAAA,GAAAyE,GAAA,CAAAI,IAAA,CAAAC,GAAA,WAAAC,IAAA;UACAA,IAAA,CAAAuC,EAAA,GAAAvC,IAAA,CAAAxE,OAAA;UACA,OAAAwE,IAAA;QACA;QACAqC,MAAA,CAAAxF,cAAA,GAAAY,IAAA,CAAA0E,KAAA,CAAA1E,IAAA,CAAAC,SAAA,CAAAgC,GAAA,CAAAI,IAAA;QACAuC,MAAA,CAAAD,eAAA;MACA,GAAAI,OAAA;QACAH,MAAA,CAAA3H,MAAA,CAAAI,OAAA;MACA;IACA;IACAsH,eAAA,WAAAA,gBAAA;MAAA,IAAAK,MAAA;MACA;MACA,IAAAC,aAAA;MACA,IAAAC,WAAA;MACA,KAAAnI,oBAAA;MACA,KAAAC,iBAAA;MACA,KAAAW,QAAA,CAAA2G,OAAA,WAAA/B,IAAA;QACA,IAAA4C,UAAA,GAAAH,MAAA,CAAA/H,MAAA,CAAAO,SAAA,CACA8E,GAAA,WAAAyB,GAAA;UAAA,OAAAA,GAAA,CAAAxB,IAAA,CAAAG,IAAA;QAAA,GACAW,MAAA,WAAA+B,IAAA;UAAA,OAAAA,IAAA,IAAAA,IAAA,CAAAC,IAAA;QAAA;QACA,IAAAC,QAAA,GAAAN,MAAA,CAAA/H,MAAA,CAAAO,SAAA,CACA8E,GAAA,WAAAyB,GAAA;UAAA,OAAAA,GAAA,CAAAxB,IAAA,CAAAG,IAAA;QAAA,GACAW,MAAA,WAAA+B,IAAA;UAAA,OAAAA,IAAA,IAAAA,IAAA,CAAAC,IAAA;QAAA;QAEAJ,aAAA,CAAAM,IAAA,CAAAC,KAAA,CAAAP,aAAA,EAAAd,kBAAA,CAAAgB,UAAA;QACAD,WAAA,CAAAK,IAAA,CAAAC,KAAA,CAAAN,WAAA,EAAAf,kBAAA,CAAAmB,QAAA;MACA;MACA;MACA,IAAAL,aAAA,CAAAV,MAAA,QAAAW,WAAA,CAAAX,MAAA;QACA,IAAAkB,YAAA,GAAAR,aAAA,CAAAS,IAAA;QACA,IAAAC,UAAA,GAAAT,WAAA,CAAAQ,IAAA,WAAAE,CAAA,EAAAC,CAAA;UAAA,WAAAC,IAAA,CAAAD,CAAA,QAAAC,IAAA,CAAAF,CAAA;QAAA;QAEA,IAAAH,YAAA,IAAAE,UAAA;UACA,IAAAI,KAAA,OAAAD,IAAA,CAAAL,YAAA;UACA,IAAAO,GAAA,OAAAF,IAAA,CAAAH,UAAA;UACA,KAAA5I,oBAAA,GAAAkJ,IAAA,CAAAC,KAAA,EAAAF,GAAA,GAAAD,KAAA;UACA;UACA,IAAAI,KAAA,OAAAL,IAAA;UACA,IAAAM,aAAA,GAAAH,IAAA,CAAAC,KAAA,EAAAF,GAAA,GAAAG,KAAA;UACA,KAAAnJ,iBAAA,GAAAoJ,aAAA,OAAAA,aAAA;QACA;MACA;MAEA,KAAAzI,QAAA,QAAAA,QAAA,CAAA2E,GAAA,WAAAC,IAAA;QACA;QACA,IAAA8D,WAAA,GAAArB,MAAA,CAAA/H,MAAA,CAAAO,SAAA,CAAA8I,MAAA,WAAAC,GAAA,EAAAxC,GAAA;UACA,IAAAC,KAAA,GAAAwC,UAAA,CAAAzC,GAAA,CAAAxB,IAAA,CAAAG,IAAA;UACA,OAAA6D,GAAA,GAAAvC,KAAA;QACA;;QAEA;QACA,IAAAyC,aAAA,GAAAzB,MAAA,CAAA/H,MAAA,CAAAO,SAAA,CAAA8I,MAAA,WAAAC,GAAA,EAAAxC,GAAA;UACA,IAAAC,KAAA,GAAAwC,UAAA,CAAAzC,GAAA,CAAAxB,IAAA,CAAAG,IAAA;UACA,OAAA6D,GAAA,GAAAvC,KAAA;QACA;;QAEA;QACA,IAAA0C,aAAA,GAAAL,WAAA,OAAAI,aAAA,GAAAJ,WAAA;;QAEA;QACA,IAAAlB,UAAA,GAAAH,MAAA,CAAA/H,MAAA,CAAAO,SAAA,CACA8E,GAAA,WAAAyB,GAAA;UAAA,OAAAA,GAAA,CAAAxB,IAAA,CAAAG,IAAA;QAAA,GACAW,MAAA,WAAA+B,IAAA;UAAA,OAAAA,IAAA,IAAAA,IAAA,CAAAC,IAAA;QAAA,GACAK,IAAA;QACA,IAAAiB,SAAA,GAAAxB,UAAA,CAAAZ,MAAA,OAAAvI,MAAA,CAAAmJ,UAAA,KAAAlB,MAAA;;QAEA;QACA,IAAAqB,QAAA,GAAAN,MAAA,CAAA/H,MAAA,CAAAO,SAAA,CACA8E,GAAA,WAAAyB,GAAA;UAAA,OAAAA,GAAA,CAAAxB,IAAA,CAAAG,IAAA;QAAA,GACAW,MAAA,WAAA+B,IAAA;UAAA,OAAAA,IAAA,IAAAA,IAAA,CAAAC,IAAA;QAAA,GACAK,IAAA,WAAAE,CAAA,EAAAC,CAAA;UAAA,WAAAC,IAAA,CAAAD,CAAA,QAAAC,IAAA,CAAAF,CAAA;QAAA;QACA,IAAAgB,OAAA,GAAAtB,QAAA,CAAAf,MAAA,OAAAvI,MAAA,CAAAsJ,QAAA,KAAArB,MAAA;;QAEA;QACA,IAAA4C,QAAA;QACA,IAAAF,SAAA,IAAAC,OAAA;UACA,IAAAb,MAAA,OAAAD,IAAA,CAAAa,SAAA;UACA,IAAAX,IAAA,OAAAF,IAAA,CAAAc,OAAA;UACAC,QAAA,GAAAZ,IAAA,CAAAC,KAAA,EAAAF,IAAA,GAAAD,MAAA;QACA;QAEA,OAAAvD,aAAA,CAAAA,aAAA,KACAD,IAAA;UACA8D,WAAA,EAAAA,WAAA,CAAAS,OAAA;UACAL,aAAA,EAAAA,aAAA,CAAAK,OAAA;UACAJ,aAAA,EAAAA,aAAA,CAAAI,OAAA;UACAH,SAAA,EAAAA,SAAA;UACAC,OAAA,EAAAA,OAAA;UACAC,QAAA,EAAAA,QAAA,OAAAA,QAAA;QAAA;MAEA;IACA;IACArG,cAAA,WAAAA,eAAA;MAAA,IAAAuG,MAAA;MAAA,OAAAnG,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkG,SAAA;QAAA,IAAA/E,GAAA;QAAA,OAAApB,mBAAA,GAAAG,IAAA,UAAAiG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/F,IAAA,GAAA+F,SAAA,CAAA9F,IAAA;YAAA;cAAA,MACA,CAAA2F,MAAA,CAAAxK,UAAA,KAAAwK,MAAA,CAAAxK,UAAA,CAAA4D,cAAA;gBAAA+G,SAAA,CAAA9F,IAAA;gBAAA;cAAA;cAAA,OAAA8F,SAAA,CAAAC,MAAA;YAAA;cAAAD,SAAA,CAAA9F,IAAA;cAAA,OACAzF,yBAAA;gBACAyL,SAAA,EAAAL,MAAA,CAAAxK,UAAA,CAAA4D;cACA;YAAA;cAFA8B,GAAA,GAAAiF,SAAA,CAAA9E,IAAA;cAGA2E,MAAA,CAAArJ,WAAA,GAAAuE,GAAA,CAAAI,IAAA;YAAA;YAAA;cAAA,OAAA6E,SAAA,CAAA5F,IAAA;UAAA;QAAA,GAAA0F,QAAA;MAAA;IACA;IACAK,UAAA,WAAAA,WAAAtD,GAAA;MAAA,IAAAuD,MAAA;MACA,KAAAzJ,UAAA,GAAAkG,GAAA;MACA,KAAAnG,aAAA;MACAhC,yBAAA;QACAmC,OAAA,EAAAgG,GAAA,CAAAhG;MACA,GAAA8G,IAAA,WAAA5C,GAAA;QACA,IAAAA,GAAA,CAAAI,IAAA;UACAiF,MAAA,CAAAxJ,QAAA,GAAA0E,aAAA,KAAAP,GAAA,CAAAI,IAAA;UACA;UACAiF,MAAA,CAAAC,IAAA,CAAAD,MAAA,CAAAxJ,QAAA,wBAAAmE,GAAA,CAAAI,IAAA,CAAArE,iBAAA,QAAAiE,GAAA,CAAAI,IAAA,CAAApE,eAAA;UACAqJ,MAAA,CAAAC,IAAA,CAAAD,MAAA,CAAAxJ,QAAA,0BAAAmE,GAAA,CAAAI,IAAA,CAAAhE,mBAAA,QAAA4D,GAAA,CAAAI,IAAA,CAAA/D,iBAAA;UACAgJ,MAAA,CAAAC,IAAA,CAAAD,MAAA,CAAAxJ,QAAA,yBAAAmE,GAAA,CAAAI,IAAA,CAAA3D,kBAAA,QAAAuD,GAAA,CAAAI,IAAA,CAAA1D,gBAAA;UACA2I,MAAA,CAAAC,IAAA,CAAAD,MAAA,CAAAxJ,QAAA,yBAAAmE,GAAA,CAAAI,IAAA,CAAAtD,kBAAA,QAAAkD,GAAA,CAAAI,IAAA,CAAArD,gBAAA;UACA;UACAsI,MAAA,CAAAE,iBAAA;UACAF,MAAA,CAAAE,iBAAA;UACAF,MAAA,CAAAE,iBAAA;UACAF,MAAA,CAAAE,iBAAA;QACA;MACA;IACA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAnI,WAAA;MACAzD,0BAAA,CAAA0G,aAAA,CAAAA,aAAA,KACA,KAAA1E,QAAA;QACAC,OAAA,OAAAF,UAAA,CAAAE,OAAA;QACA4J,UAAA,EAAAhI,YAAA,CAAAC,OAAA;QACAgI,UAAA,OAAArL,UAAA,CAAA4D;MAAA,EACA,EAAA0E,IAAA,WAAA5C,GAAA;QACAyF,MAAA,CAAAG,QAAA,CAAAC,OAAA;QACAJ,MAAA,CAAA9J,aAAA;QACA8J,MAAA,CAAAjH,YAAA;MACA,GAAAsE,OAAA;QACA2C,MAAA,CAAAnI,WAAA;MACA;IACA;IACAwI,qBAAA,WAAAA,sBAAAvL,IAAA,EAAAwL,SAAA;MACA,IAAAA,SAAA,IAAAA,SAAA,CAAAzD,MAAA;QACA,KAAAgD,IAAA,MAAAzJ,QAAA,KAAAgF,MAAA,CAAAtG,IAAA,kBAAAwL,SAAA;QACA,KAAAT,IAAA,MAAAzJ,QAAA,KAAAgF,MAAA,CAAAtG,IAAA,gBAAAwL,SAAA;QACA,KAAAT,IAAA,MAAAzJ,QAAA,KAAAgF,MAAA,CAAAtG,IAAA,kBAAAwL,SAAA;QACA,KAAAR,iBAAA,CAAAhL,IAAA;MACA;QACA,KAAA+K,IAAA,MAAAzJ,QAAA,KAAAgF,MAAA,CAAAtG,IAAA;QACA,KAAA+K,IAAA,MAAAzJ,QAAA,KAAAgF,MAAA,CAAAtG,IAAA;QACA,KAAA+K,IAAA,MAAAzJ,QAAA,KAAAgF,MAAA,CAAAtG,IAAA;QACA,KAAA+K,IAAA,MAAAzJ,QAAA,KAAAgF,MAAA,CAAAtG,IAAA;MACA;MACA;MACA,KAAAyL,YAAA;IACA;IACAT,iBAAA,WAAAA,kBAAAhL,IAAA;MACA,IAAAmK,SAAA,QAAA7I,QAAA,IAAAgF,MAAA,CAAAtG,IAAA;MACA,IAAAoK,OAAA,QAAA9I,QAAA,IAAAgF,MAAA,CAAAtG,IAAA;MACA,IAAAmK,SAAA,IAAAC,OAAA;QACA,IAAAb,KAAA,OAAAD,IAAA,CAAAa,SAAA;QACA,IAAAX,GAAA,OAAAF,IAAA,CAAAc,OAAA;QACA;QACA,IAAAC,QAAA,GAAAZ,IAAA,CAAAC,KAAA,EAAAF,GAAA,GAAAD,KAAA;QACA,KAAAwB,IAAA,MAAAzJ,QAAA,KAAAgF,MAAA,CAAAtG,IAAA,gBAAAqK,QAAA,OAAAA,QAAA;MACA;QACA,KAAAU,IAAA,MAAAzJ,QAAA,KAAAgF,MAAA,CAAAtG,IAAA;MACA;IACA;IACA0L,WAAA,WAAAA,YAAA;MACA,KAAAtK,aAAA;MACA,KAAAE,QAAA;QACAC,OAAA;QACAC,iBAAA;QACAC,eAAA;QACAC,iBAAA;QACAC,eAAA;QACAC,wBAAA;QACAC,mBAAA;QACAC,iBAAA;QACAC,mBAAA;QACAC,iBAAA;QACAC,0BAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,yBAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,yBAAA;MACA;IACA;IACAgJ,aAAA,WAAAA,cAAAlG,GAAA;MACA,IAAAA,GAAA,CAAAmG,SAAA;QACA,KAAAP,QAAA,CAAAC,OAAA;QACA,KAAArH,YAAA;MACA;QACA,KAAAoH,QAAA,CAAAQ,KAAA,CAAApG,GAAA,CAAAqG,OAAA;QACA,IAAArG,GAAA,CAAAI,IAAA;UACAkG,MAAA,CAAAC,IAAA,CAAAvM,UAAA,MAAAwM,QAAA,EAAAxG,GAAA,CAAAI,IAAA;QACA;MACA;IACA;IACAqG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAtJ,aAAA;MACA7D,sBAAA;QACA4E,SAAA,EAAAT,YAAA,CAAAC,OAAA;QACAM,SAAA,OAAA3D,UAAA,CAAA4D,cAAA;QACAyI,WAAA,OAAAhM,UAAA,CAAAE,KAAA,CAAAwF,GAAA,WAAAC,IAAA;UACA,oDAAAsG,OAAA,CAAAtG,IAAA;QACA;QACAuG,OAAA,OAAAlM,UAAA,CAAAC;MACA,GAAAgI,IAAA,WAAA5C,GAAA;QACA,IAAAA,GAAA,CAAAmG,SAAA;UACAG,MAAA,CAAAC,IAAA,CAAAvM,UAAA,CAAA0M,MAAA,CAAAF,QAAA,EAAAxG,GAAA,CAAAI,IAAA;QACA;UACAsG,MAAA,CAAAd,QAAA,CAAAQ,KAAA,CAAApG,GAAA,CAAAqG,OAAA;QACA;MACA,GAAAvD,OAAA;QACA4D,MAAA,CAAAtJ,aAAA;MACA;IACA;IACA0J,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAA1J,aAAA;MACA7D,+BAAA;QACAwN,SAAA,EAAAtJ,YAAA,CAAAC,OAAA;MACA,GAAAiF,IAAA,WAAA5C,GAAA;QACA,IAAAA,GAAA,CAAAmG,SAAA;UACAY,MAAA,CAAAnB,QAAA,CAAAC,OAAA;UACAkB,MAAA,CAAAvI,YAAA;QACA;UACAuI,MAAA,CAAAnB,QAAA,CAAAQ,KAAA,CAAApG,GAAA,CAAAqG,OAAA;QACA;MACA,GAAAvD,OAAA;QACAiE,MAAA,CAAA1J,aAAA;MACA;IACA;IACA4J,QAAA,WAAAA,SAAA3G,IAAA;MACA,IAAApG,iBAAA,CAAAoG,IAAA,CAAAS,KAAA;QACA,KAAAmG,OAAA,CAAA5D,IAAA;UACAnJ,IAAA,EAAAmG,IAAA,CAAAS;QACA;MACA;QACA,KAAA6E,QAAA,CAAAQ,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
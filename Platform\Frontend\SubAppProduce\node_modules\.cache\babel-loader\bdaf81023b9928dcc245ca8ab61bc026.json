{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\plan\\overallControlPlan.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\plan\\overallControlPlan.vue", "mtime": 1757926768489}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5tYXAuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IEdldEN1cnJDb21wYW55UHJvamVjdExpc3QgfSBmcm9tICdAL2FwaS9wbG0vcHJvamVjdHMnOwppbXBvcnQgT3ZlcmFsbENvbnRyb2xQbGFuQ29udGVudCBmcm9tICcuL2NvbXBvbmVudHMvT3ZlcmFsbENvbnRyb2xQbGFuQ29udGVudC52dWUnOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ092ZXJhbGxDb250cm9sUGxhbicsCiAgY29tcG9uZW50czogewogICAgT3ZlcmFsbENvbnRyb2xQbGFuQ29udGVudDogT3ZlcmFsbENvbnRyb2xQbGFuQ29udGVudAogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHRyZWVEYXRhOiBbXSwKICAgICAgdHJlZVByb3BzOiB7CiAgICAgICAgbGFiZWw6ICdTaG9ydF9OYW1lJywKICAgICAgICBpZDogJ1N5c19Qcm9qZWN0X0lkJwogICAgICB9LAogICAgICB0cmVlRGVmYXVsdFNlbGVjdGVkS2V5OiAnJywKICAgICAgY3VyUHJvamVjdDogJycKICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRUcmVlRGF0YSgpOwogIH0sCiAgbWV0aG9kczogewogICAgZ2V0VHJlZURhdGE6IGZ1bmN0aW9uIGdldFRyZWVEYXRhKCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICBHZXRDdXJyQ29tcGFueVByb2plY3RMaXN0KHsKICAgICAgICBjb21wYW5JZDogbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ0N1clJlZmVyZW5jZUlkJyksCiAgICAgICAgSXNDYXNjYWRlOiBmYWxzZQogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpcy50cmVlRGF0YSA9IHJlcy5EYXRhLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgaXRlbS5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICByZXR1cm4gaXRlbTsKICAgICAgICB9KTsKICAgICAgICBpZiAoX3RoaXMudHJlZURhdGEubGVuZ3RoKSB7CiAgICAgICAgICBfdGhpcy50cmVlRGVmYXVsdFNlbGVjdGVkS2V5ID0gX3RoaXMudHJlZURhdGFbMF0uU3lzX1Byb2plY3RfSWQ7CiAgICAgICAgICBfdGhpcy5jdXJQcm9qZWN0ID0gX3RoaXMudHJlZURhdGFbMF07CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBub2RlQ2xpY2s6IGZ1bmN0aW9uIG5vZGVDbGljayhub2RlKSB7CiAgICAgIHRoaXMuY3VyUHJvamVjdCA9IG5vZGU7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["GetCurrCompanyProjectList", "OverallControlPlanContent", "name", "components", "data", "treeData", "treeProps", "label", "id", "treeDefaultSelectedKey", "curProject", "created", "getTreeData", "methods", "_this", "companId", "localStorage", "getItem", "IsCascade", "then", "res", "Data", "map", "item", "loading", "length", "Sys_Project_Id", "nodeClick", "node"], "sources": ["src/views/plan/overallControlPlan.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <bt-tree :data=\"treeData\" :props=\"treeProps\" :default-selected-key=\"treeDefaultSelectedKey\" node-key=\"Sys_Project_Id\" @node-click=\"nodeClick\">\r\n      <template #default=\"{ data }\">\r\n        <span style=\"color: #5ac8fa!important;\">({{ data.Code }})</span>\r\n        <span>{{ data.Short_Name }}</span>\r\n      </template>\r\n    </bt-tree>\r\n    <OverallControlPlanContent :cur-project=\"curProject\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetCurrCompanyProjectList } from '@/api/plm/projects'\r\nimport OverallControlPlanContent from './components/OverallControlPlanContent.vue'\r\n\r\nexport default {\r\n  name: 'OverallControlPlan',\r\n  components: { OverallControlPlanContent },\r\n  data() {\r\n    return {\r\n      treeData: [],\r\n      treeProps: {\r\n        label: 'Short_Name',\r\n        id: 'Sys_Project_Id'\r\n      },\r\n      treeDefaultSelectedKey: '',\r\n      curProject: ''\r\n    }\r\n  },\r\n  created() {\r\n    this.getTreeData()\r\n  },\r\n  methods: {\r\n    getTreeData() {\r\n      GetCurrCompanyProjectList({\r\n        companId: localStorage.getItem('CurReferenceId'),\r\n        IsCascade: false\r\n      }).then(res => {\r\n        this.treeData = res.Data.map(item => {\r\n          item.loading = false\r\n          return item\r\n        })\r\n        if (this.treeData.length) {\r\n          this.treeDefaultSelectedKey = this.treeData[0].Sys_Project_Id\r\n          this.curProject = this.treeData[0]\r\n        }\r\n      })\r\n    },\r\n    nodeClick(node) {\r\n      this.curProject = node\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  font-family: PingFang SC, PingFang SC;\r\n  display: flex;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;AAaA,SAAAA,yBAAA;AACA,OAAAC,yBAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAF,yBAAA,EAAAA;EAAA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;QACAC,KAAA;QACAC,EAAA;MACA;MACAC,sBAAA;MACAC,UAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACAD,WAAA,WAAAA,YAAA;MAAA,IAAAE,KAAA;MACAd,yBAAA;QACAe,QAAA,EAAAC,YAAA,CAAAC,OAAA;QACAC,SAAA;MACA,GAAAC,IAAA,WAAAC,GAAA;QACAN,KAAA,CAAAT,QAAA,GAAAe,GAAA,CAAAC,IAAA,CAAAC,GAAA,WAAAC,IAAA;UACAA,IAAA,CAAAC,OAAA;UACA,OAAAD,IAAA;QACA;QACA,IAAAT,KAAA,CAAAT,QAAA,CAAAoB,MAAA;UACAX,KAAA,CAAAL,sBAAA,GAAAK,KAAA,CAAAT,QAAA,IAAAqB,cAAA;UACAZ,KAAA,CAAAJ,UAAA,GAAAI,KAAA,CAAAT,QAAA;QACA;MACA;IACA;IACAsB,SAAA,WAAAA,UAAAC,IAAA;MACA,KAAAlB,UAAA,GAAAkB,IAAA;IACA;EACA;AACA", "ignoreList": []}]}
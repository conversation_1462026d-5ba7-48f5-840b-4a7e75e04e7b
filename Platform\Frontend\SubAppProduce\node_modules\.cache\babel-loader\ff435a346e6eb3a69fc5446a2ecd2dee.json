{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\router\\modules\\PRO\\project-config\\index.js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\router\\modules\\PRO\\project-config\\index.js", "mtime": 1757468111963}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkIGZyb20gIkQ6L3Byb2plY3QvcGxhdGZvcm1fZnJhbWV3b3JrX21hc3Rlci9wbGF0Zm9ybV9mcmFtZXdvcmsvUGxhdGZvcm0vRnJvbnRlbmQvU3ViQXBwUHJvZHVjZS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vaW50ZXJvcFJlcXVpcmVXaWxkY2FyZC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaXRlcmF0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLml0ZXJhdG9yLmpzIjsKZXhwb3J0IGRlZmF1bHQgewogIFBST1Byb2plY3RQcm9kdWN0VHlwZTogZnVuY3Rpb24gUFJPUHJvamVjdFByb2R1Y3RUeXBlKCkgewogICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZSgpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICByZXR1cm4gX2ludGVyb3BSZXF1aXJlV2lsZGNhcmQocmVxdWlyZSgnQC92aWV3cy9QUk8vcHJvamVjdC1jb25maWcvcHJvamVjdC1wcm9kdWN0LXR5cGUvaW5kZXgudnVlJykpOwogICAgfSk7CiAgfSwKICBQUk9Qcm9kdWN0TWZnUGF0aDogZnVuY3Rpb24gUFJPUHJvZHVjdE1mZ1BhdGgoKSB7CiAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKCkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgIHJldHVybiBfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZChyZXF1aXJlKCdAL3ZpZXdzL1BSTy9wcm9qZWN0LWNvbmZpZy9wcm9kdWN0LW1mZy1wYXRoL2luZGV4LnZ1ZScpKTsKICAgIH0pOwogIH0KfTs="}, {"version": 3, "names": ["PROProjectProductType", "Promise", "resolve", "then", "_interopRequireWildcard", "require", "PROProductMfgPath"], "sources": ["D:/project/platform_framework_master/platform_framework/Platform/Frontend/SubAppProduce/src/router/modules/PRO/project-config/index.js"], "sourcesContent": ["export default {\r\n  PROProjectProductType: () => import('@/views/PRO/project-config/project-product-type/index.vue'),\r\n  PROProductMfgPath: () => import('@/views/PRO/project-config/product-mfg-path/index.vue')\r\n}\r\n"], "mappings": ";;;;AAAA,eAAe;EACbA,qBAAqB,EAAE,SAAvBA,qBAAqBA,CAAA;IAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,2DAA2D;IAAA;EAAA,CAAC;EAChGC,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAA;IAAA,OAAAL,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,uDAAuD;IAAA;EAAA;AACzF,CAAC", "ignoreList": []}]}
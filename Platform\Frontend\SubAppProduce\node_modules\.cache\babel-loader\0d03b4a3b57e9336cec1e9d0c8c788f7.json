{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\change-management\\contact-list\\add.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\change-management\\contact-list\\add.vue", "mtime": 1757468112511}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GeAreaTrees", "GetInstallUnitIdNameList", "GetProjectPageList", "GetFactoryPeoplelist", "GetMocOrderInfo", "GetMocOrderTypeList", "ImportChangFile", "SaveMocOrder", "SubmitMocOrder", "GetCompanyDepartTree", "GetOssUrl", "ImportFile", "OSSUpload", "closeTagView", "combineURL", "debounce", "deepClone", "StatusDialog", "numeral", "GetTableSettingList", "HandleEdit", "AddHandle", "changeType", "changeTypeReverse", "getAllCodesByType", "getFileNameFromUrl", "isArray", "SteelComponentManager", "GetCurFactory", "name", "components", "mixins", "data", "curStatus", "del", "change", "add", "increase", "decrease", "unChange", "dialogVisible", "title", "width", "currentComponent", "filePath", "finishFee", "pageLoading", "saveLoading", "submitLoading", "uploadLoading", "tbLoading", "tbData", "activities", "fileList", "multipleSelection", "changeRowContentList", "filterCodeOptions", "columns", "changeMethod", "searchForm", "component_name", "part_name", "assembly_name", "component_search_mode", "part_search_mode", "assembly_search_mode", "content", "form", "Sys_Project_Id", "Area_Id", "InstallUnit_Ids", "Handle_UserId", "Moc_Type_Id", "Fee", "undefined", "Hours", "Urgency", "Demand_Date", "Fee_DepartId", "Remark", "AttachmentList", "showImport", "treeParams", "filterable", "clickParent", "props", "disabled", "children", "label", "value", "peopleList", "projectList", "changeTypeList", "installUnitList", "treeParamsArea", "rules", "required", "message", "trigger", "toolbarButtons", "code", "computed", "disableSave", "<PERSON><PERSON><PERSON><PERSON>", "$route", "query", "type", "isEdit", "showDetail", "_this", "zbtk", "find", "item", "Id", "Is_Deepen_Change", "getFileName", "watch", "handler", "newVal", "_this$$refs", "$refs", "installUnitRef", "clearValidate", "immediate", "length", "created", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "id", "wrap", "_callee$", "_context", "prev", "next", "$store", "dispatch", "getProjectData", "getFactoryPeople", "getDepTree", "getFactoryChangeTypeList", "t0", "getInfo", "getTableConfig", "stop", "methods", "_this3", "_callee2", "res", "filteredColumns", "codeColumn", "_customColumns", "_columns", "columnCode", "_callee2$", "_context2", "ProfessionalCode", "sent", "IsSucceed", "allCodes", "JSON", "parse", "stringify", "Data", "filter", "includes", "Code", "Display_Name", "Is_Frozen", "Frozen_Dirction", "<PERSON><PERSON><PERSON>", "Align", "unshift", "push", "apply", "rootColumns", "map", "_item$Display_Name", "displayNameLength", "Math", "max", "_objectSpread", "_this4", "_callee4", "_callee4$", "_context4", "then", "_ref", "_callee3", "_res$Data", "Deepen_File_Url", "Change_Type", "FeeHistory", "Status", "OrderDetail", "_FeeHistory$slice", "_FeeHistory$slice2", "last", "idx", "_callee3$", "_context3", "slice", "_slicedToArray", "format", "findIndex", "splice", "for<PERSON>ach", "element", "obj", "File_Name", "url", "File_Url", "getAreaList", "getInstallUnitPageList", "Object", "assign", "split", "Hour", "Number", "setTbData", "$message", "Message", "_x", "arguments", "mocTypeChange", "handleReset", "handleExceed", "files", "warning", "concat", "handleProgress", "event", "handleError", "err", "console", "log", "checkUploading", "flag", "every", "v", "status", "handleImport", "_this5", "validate", "valid", "handleOpen", "handleAdd", "_this6", "$nextTick", "_", "row", "_this7", "PageSize", "setDisabledTree", "root", "_this8", "Children", "Pid", "_this9", "_callee5", "_callee5$", "_context5", "sysProjectId", "tree", "treeSelectArea", "treeDataUpdateFun", "areaId", "_this0", "_callee6", "_callee6$", "_context6", "Page", "projectChange", "_this1", "_callee7", "_callee7$", "_context7", "clearTb", "areaChange", "_this10", "_callee8", "_callee8$", "_context8", "areaClear", "handleClose", "_this11", "freeze", "curId", "localStorage", "getItem", "cur", "_this12", "_this13", "_callee1", "getFactoryDeptId", "getDept", "depId", "_callee1$", "_context1", "_ref2", "_callee9", "_callee9$", "_context9", "_res$Data$", "Dept_Id", "abrupt", "_ref3", "_callee0", "_callee0$", "_context0", "_res$Data2", "origin", "disableDirectory", "treeArray", "treeSelect", "_x2", "getTableInfo", "fileObj", "_this14", "ImportType", "toString", "isImportFile", "_res$Data3", "MocOrderDetailList", "Deepen_File_Url_List", "ErrorFileUrl", "window", "open", "$baseUrl", "setAllWeight", "SteelAllWeight", "SteelWeight", "multiply", "SteelAmount", "list", "_this15", "setItem", "isAfterValue", "updatedItem", "CodeType", "Type", "parentChildrenId", "uuid", "MocIdBefore", "checked", "changeContent", "MocType", "setItemMocContent", "childrenItems", "findChildItems", "childItem", "isDisabled", "defaultTbData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "m", "ChangeFieldCode", "_list", "_codes", "AfterValue", "BeforeValue", "Field_Type", "IsCoreField", "Name", "ChangeFieldName", "Value", "NewValue", "beforeUpload", "file", "handleChange", "handlePreview", "_callee10", "arr", "isDwg", "_yield$GetOssUrl", "_callee10$", "_context10", "handleRemove", "response", "fileInfo", "File_Size", "File_Type", "uploadSuccess", "handleSave", "_this16", "_ref4", "_callee11", "_callee11$", "_context11", "submit", "_x3", "handleSubmit", "_this17", "_callee13", "_callee13$", "_context13", "_ref5", "_callee12", "_callee12$", "_context12", "_x4", "isDraft", "_this18", "_callee15", "_this18$changeTypeLis", "_form", "submitTb", "isReNew", "subObj", "_callee15$", "_context15", "isShow", "others", "_objectWithoutProperties", "_excluded", "changeMap", "state", "contactList", "changeCode", "IsNewImportFile", "Handle_UserName", "Moc_Type_Name", "Array", "join", "Is_Draft", "_ref6", "_callee14", "_callee14$", "_context14", "submit<PERSON>heck", "Path", "_x5", "_this19", "_callee16", "_callee16$", "_context16", "_this$$refs2", "_this$$refs3", "tableRef", "setAllTreeExpand", "clearFilter", "handleFilter", "_this20", "nameMapping", "ComponentName", "SteelName", "PartName", "changeMaps", "keys", "changeList", "xTable", "getColumnByField", "option", "filters", "updateData", "clearCheckboxRow", "getFeeGap", "activity", "index", "result", "subtract", "isRed", "isBlue", "handleEdit", "_this21", "getCpCode", "_this21$$refs$content", "defaultRow", "init", "handleDelete", "_this22", "$confirm", "confirmButtonText", "cancelButtonText", "deleteTableItem", "catch", "handleRestore", "restoreTableItem", "changeMethodFun", "val", "_this23", "setTbCloumn", "refreshColumn", "getMocModelList", "_this24", "existingUuids", "Set", "has", "updateItemChangeStatus", "getChangeTypeText", "_toConsumableArray", "sort", "a", "b", "_defaultTbData", "setSameItems", "_this25", "changeInfos", "mocBeforeItems", "isDeleteItems", "unitPart", "unitP", "findParentItem", "similarUnitPartItems", "findSimilarItems", "similarItem", "isSame", "isSameParent", "$set", "k", "_excluded2", "filteredList", "editInfo", "_ref7", "_changeMaps", "existingChanges", "existingChangeCodes", "removedChangeCodes", "some", "batchUpdateTableItem", "handleCancelChange", "_this26", "selected<PERSON><PERSON><PERSON><PERSON>", "getIds", "array", "multiSelectedChange", "getCheckboxRecords", "records", "handelFilePath", "getChangeStyle", "changeName", "rusult", "isAdd", "isAdjust", "isDecrease", "isIncrease", "isDelete", "handleShow", "filterNameMethod", "_ref8", "values", "cellValue", "column", "filterCustom", "_this$searchForm", "_ComponentName", "_SteelName", "_PartName", "partMatch", "assemblyMatch", "componentMatch", "installChange", "InstallUnit_Id"], "sources": ["src/views/PRO/change-management/contact-list/add.vue"], "sourcesContent": ["<template>\r\n  <div class=\"page-container\">\r\n    <div class=\"form-x\">\r\n      <div>\r\n        <span class=\"cs-title\">工程联系单</span>\r\n      </div>\r\n      <el-divider />\r\n      <el-row v-loading=\"pageLoading\" element-loading-text=\"加载中\">\r\n        <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"120px\">\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"项目名称\" prop=\"Sys_Project_Id\">\r\n              <el-select\r\n                v-model=\"form.Sys_Project_Id\"\r\n                :disabled=\"isView||tbLoading\"\r\n                clearable\r\n                class=\"w100\"\r\n                placeholder=\"请选择\"\r\n                filterable\r\n                @change=\"projectChange()\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in projectList\"\r\n                  :key=\"item.Sys_Project_Id\"\r\n                  :label=\"item.Short_Name\"\r\n                  :value=\"item.Sys_Project_Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"区域名称\" prop=\"Area_Id\">\r\n              <el-tree-select\r\n                ref=\"treeSelectArea\"\r\n                v-model=\"form.Area_Id\"\r\n                :disabled=\"!form.Sys_Project_Id||isView||tbLoading\"\r\n                :select-params=\"{\r\n                  clearable: true,\r\n\r\n                }\"\r\n                :tree-params=\"treeParamsArea\"\r\n                @select-clear=\"areaClear\"\r\n                @node-click=\"areaChange\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item ref=\"installUnitRef\" label=\"批次\" prop=\"InstallUnit_Ids\">\r\n              <el-select\r\n                v-model=\"form.InstallUnit_Ids\"\r\n                filterable\r\n                multiple\r\n                class=\"w100\"\r\n                :disabled=\"isView||!form.Area_Id||tbLoading\"\r\n                placeholder=\"请选择\"\r\n                @change=\"installChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in installUnitList\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"变更人\" prop=\"Handle_UserId\">\r\n              <el-select\r\n                v-model=\"form.Handle_UserId\"\r\n                :disabled=\"isView\"\r\n                filterable\r\n                class=\"w100\"\r\n                clearable\r\n                placeholder=\"请选择\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in peopleList\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"变更类型\" prop=\"Moc_Type_Id\">\r\n              <el-select\r\n                v-model=\"form.Moc_Type_Id\"\r\n                class=\"w100\"\r\n                :disabled=\"isView\"\r\n                filterable\r\n                placeholder=\"请选择\"\r\n                @change=\"mocTypeChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in changeTypeList\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"变更费用\" prop=\"Fee\">\r\n              <el-input-number v-model=\"form.Fee\" placeholder=\"请输入\" style=\"width: 100%\" clearable :disabled=\"isView\" class=\"cs-number-btn-hidden\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"变更工时\" prop=\"Hours\">\r\n              <el-input-number v-model=\"form.Hours\" placeholder=\"请输入\" style=\"width: 100%\" clearable :disabled=\"isView\" class=\"cs-number-btn-hidden\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"紧急程度\" prop=\"Urgency\">\r\n              <el-select v-model=\"form.Urgency\" placeholder=\"请选择\" style=\"width: 100%\" :disabled=\"isView\">\r\n                <el-option label=\"普通\" :value=\"1\" />\r\n                <el-option label=\"紧急\" :value=\"2\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"要求完成时间\" prop=\"Demand_Date\">\r\n              <el-date-picker\r\n                v-model=\"form.Demand_Date\"\r\n                :disabled=\"isView\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                type=\"date\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"费用部门\" prop=\"Fee_DepartId\">\r\n              <el-tree-select\r\n                ref=\"treeSelect\"\r\n                v-model=\"form.Fee_DepartId\"\r\n                :disabled=\"isView\"\r\n                :select-params=\"{\r\n                  clearable: true,\r\n                }\"\r\n                :tree-params=\"treeParams\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"变更说明\" prop=\"Remark\">\r\n              <el-input\r\n                v-model=\"form.Remark\"\r\n                type=\"textarea\"\r\n                :disabled=\"isView\"\r\n                :autosize=\"{ minRows: 3, maxRows: 3}\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"附件\" prop=\"region\">\r\n              <OSSUpload\r\n                ref=\"upload\"\r\n                :disabled=\"isView\"\r\n                action=\"\"\r\n                :before-upload=\"beforeUpload\"\r\n                :limit=\"5\"\r\n                multiple\r\n                :file-list=\"fileList\"\r\n                :on-progress=\"handleProgress\"\r\n                :on-exceed=\"handleExceed\"\r\n                :on-error=\"handleError\"\r\n                :on-success=\"uploadSuccess\"\r\n                :on-change=\"handleChange\"\r\n                :on-remove=\"handleRemove\"\r\n                :on-preview=\"handlePreview\"\r\n                show-file-list\r\n                accept=\".xls, .xlsx,.pdf,.jpg,.png,.dwg,.doc,.docx\"\r\n                btn-icon=\"el-icon-upload\"\r\n                :class=\"isView ? 'z-upload hiddenBtn' : 'z-upload'\"\r\n              >\r\n                <el-button v-if=\"!isView\" :loading=\"uploadLoading\" type=\"primary\">上传文件</el-button>\r\n              </OSSUpload>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-form>\r\n\r\n      </el-row>\r\n    </div>\r\n    <div v-if=\"isView\" class=\"cs-fee\">\r\n      <span class=\"cs-title\">费用变动</span>\r\n      <span class=\"cs-label\">当前金额：<span class=\"fw cs-blue\">{{ finishFee }}元</span></span>\r\n      <el-divider />\r\n      <el-timeline>\r\n        <el-timeline-item\r\n          v-for=\"(activity, index) in activities\"\r\n          :key=\"index\"\r\n          hide-timestamp\r\n        >\r\n          <div class=\"line-content\">\r\n            <span class=\"fee-name\">{{ activity.Create_UserName }}</span>\r\n            <span :class=\"['fee-num',{'txt-red':activity.isRed,'txt-blue':activity.isBlue}]\">{{ getFeeGap(activity,index) }}</span>\r\n            <span class=\"fee-time\">{{ activity.Create_Date }}</span>\r\n            <span class=\"fee-remark\">备注：{{ activity.Remark || '-' }}</span>\r\n          </div>\r\n          <template #dot>\r\n            <span class=\"circle\" />\r\n          </template>\r\n        </el-timeline-item>\r\n      </el-timeline>\r\n    </div>\r\n    <div class=\"cs-main\">\r\n      <template v-if=\"showDetail\">\r\n        <span class=\"cs-title\">变更明细</span>\r\n        <el-divider />\r\n\r\n        <el-form inline class=\"change-method-form\">\r\n          <el-form-item label=\"变更方式：\">\r\n            <template v-if=\"!isView\">\r\n              <el-radio :value=\"changeMethod\" :label=\"1\" @input=\"changeMethodFun(1)\">完整清单导入</el-radio>\r\n              <el-radio :value=\"changeMethod\" :label=\"2\" @input=\"changeMethodFun(2)\">部分清单导入</el-radio>\r\n              <el-radio :value=\"changeMethod\" :label=\"3\" @input=\"changeMethodFun(3)\">手动修改</el-radio>\r\n            </template>\r\n            <template v-else>\r\n              {{ changeMethod === 1 ? '完整清单导入' : changeMethod === 2 ? '部分清单导入' : '手动修改' }}\r\n            </template>\r\n          </el-form-item>\r\n        </el-form>\r\n        <div v-if=\"filePath\">\r\n          <i style=\"color:#409EFF\" class=\"el-icon-download\" />\r\n          <el-link type=\"primary\" :underline=\"false\" @click=\"handelFilePath\">{{ getFileName }}</el-link>\r\n        </div>\r\n        <vxe-toolbar ref=\"xToolbar1\" class=\"cs-toolBar\">\r\n          <template #buttons>\r\n            <el-button v-if=\"!isView && changeMethod !== 3\" type=\"primary\" @click=\"handleImport\">导入变更清单</el-button>\r\n            <el-button v-if=\"!isView && changeMethod === 3\" type=\"primary\" @click=\"handleAdd\">添加变更内容</el-button>\r\n            <el-button v-if=\"!isView && changeMethod === 3\" type=\"danger\" :disabled=\"!multipleSelection.length\" plain @click=\"handleCancelChange\">取消变更</el-button>\r\n          </template>\r\n          <template #tools>\r\n            <el-form ref=\"form2\" inline :model=\"searchForm\" label-width=\"70px\">\r\n              <el-form-item label=\"构件名称\">\r\n                <el-input\r\n                  v-model=\"searchForm.component_name\"\r\n                  clearable\r\n                  style=\"width: 260px;\"\r\n                  placeholder=\"请输入\"\r\n                  class=\"input-with-select\"\r\n                >\r\n                  <el-select\r\n                    slot=\"prepend\"\r\n                    v-model=\"searchForm.component_search_mode\"\r\n                    placeholder=\"请选择\"\r\n                    style=\"width: 100px\"\r\n                  >\r\n                    <el-option\r\n                      label=\"模糊搜索\"\r\n                      :value=\"1\"\r\n                    />\r\n                    <el-option\r\n                      label=\"精确搜索\"\r\n                      :value=\"2\"\r\n                    />\r\n                  </el-select>\r\n                </el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"部件名称\">\r\n                <el-input\r\n                  v-model=\"searchForm.assembly_name\"\r\n                  style=\"width: 260px;\"\r\n                  clearable\r\n                  placeholder=\"请输入\"\r\n                  class=\"input-with-select\"\r\n                >\r\n                  <el-select\r\n                    slot=\"prepend\"\r\n                    v-model=\"searchForm.assembly_search_mode\"\r\n                    placeholder=\"请选择\"\r\n                    style=\"width: 100px\"\r\n                  >\r\n                    <el-option\r\n                      label=\"模糊搜索\"\r\n                      :value=\"1\"\r\n                    />\r\n                    <el-option\r\n                      label=\"精确搜索\"\r\n                      :value=\"2\"\r\n                    />\r\n                  </el-select>\r\n                </el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"零件名称\">\r\n                <el-input\r\n                  v-model=\"searchForm.part_name\"\r\n                  clearable\r\n                  style=\"width: 260px;\"\r\n                  placeholder=\"请输入\"\r\n                  class=\"input-with-select\"\r\n                >\r\n                  <el-select\r\n                    slot=\"prepend\"\r\n                    v-model=\"searchForm.part_search_mode\"\r\n                    placeholder=\"请选择\"\r\n                    style=\"width: 100px\"\r\n                  >\r\n                    <el-option\r\n                      label=\"模糊搜索\"\r\n                      :value=\"1\"\r\n                    />\r\n                    <el-option\r\n                      label=\"精确搜索\"\r\n                      :value=\"2\"\r\n                    />\r\n                  </el-select>\r\n                </el-input>\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button @click=\"handleReset\">重置</el-button>\r\n                <el-button type=\"primary\" @click=\"handleFilter\">搜索</el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n          </template>\r\n        </vxe-toolbar>\r\n        <div class=\"fff tb-x\">\r\n          <vxe-table\r\n            id=\"uuid\"\r\n            ref=\"tableRef\"\r\n            :empty-render=\"{name: 'NotData'}\"\r\n            show-header-overflow\r\n            :loading=\"tbLoading\"\r\n            element-loading-spinner=\"el-icon-loading\"\r\n            element-loading-text=\"拼命加载中\"\r\n            empty-text=\"暂无数据\"\r\n            class=\"cs-vxe-table cs-tree-table\"\r\n            height=\"500\"\r\n            stripe\r\n            :filter-config=\"{showIcon: false}\"\r\n            :row-config=\"{keyField:'uuid', 'isHover': true, 'isCurrent': true}\"\r\n            :data=\"tbData\"\r\n            resizable\r\n            :checkbox-config=\"{checkField: 'checked',labelField: 'CPCode', highlight: true}\"\r\n            :tree-config=\"{transform: true, showIcon: true, rowField: 'parentChildrenId', parentField: 'ParentId'}\"\r\n            :tooltip-config=\"{ enterable: true}\"\r\n            @checkbox-all=\"multiSelectedChange\"\r\n            @checkbox-change=\"multiSelectedChange\"\r\n          >\r\n            <vxe-column v-if=\"changeMethod === 3 && !isView\" fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n            <!-- :type=\"index===0 && changeMethod === 3 && !isView? 'checkbox':''\" -->\r\n\r\n            <template v-for=\"(item,index) in columns\">\r\n              <vxe-column\r\n                :key=\"item.Code+changeMethod\"\r\n                :tree-node=\"index===0\"\r\n                :fixed=\"item.Is_Frozen ? (item.Frozen_Dirction || 'left') : ''\"\r\n                :min-width=\"item.Width\"\r\n                show-overflow=\"tooltip\"\r\n                sortable\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :filters=\"item.Code==='CPCode' ? filterCodeOptions : null\"\r\n                :filter-method=\"item.Code==='CPCode' ? filterNameMethod : null\"\r\n                :title=\"item.Display_Name\"\r\n              >\r\n                <template v-if=\"item.Code==='CPCode'\" #filter=\"{ $panel, column }\">\r\n                  <input v-for=\"(option, index1) in column.filters\" :key=\"index1\" v-model=\"option.data\" type=\"type\" @input=\"$panel.changeOption($event, !!option.data, option)\">\r\n                </template>\r\n                <template v-if=\"item.Code==='CPCode'\" #default=\"{ row }\">\r\n                  {{ getCpCode(row) }}\r\n                  <el-tag v-if=\"row.Type===0\" size=\"mini\">构</el-tag>\r\n                  <el-tag v-else-if=\"row.Type===1\" type=\"warning\" size=\"mini\">部</el-tag>\r\n                  <el-tag v-else type=\"success\" size=\"mini\">零</el-tag>\r\n                </template>\r\n                <template v-else-if=\"item.Code === 'changeContent'\" #default=\"{ row }\">\r\n\r\n                  <el-popover\r\n                    placement=\"left-start\"\r\n                    width=\"400\"\r\n                    trigger=\"click\"\r\n                    @show=\"handleShow(row)\"\r\n                  >\r\n                    <el-table max-height=\"300\" stripe resizable class=\"cs-custom-table\" :data=\"changeRowContentList\">\r\n                      <el-table-column align=\"center\" property=\"Name\" width=\"100\" label=\"变更字段\" />\r\n                      <el-table-column align=\"center\" property=\"Value\" label=\"变更前\" />\r\n                      <el-table-column align=\"center\" property=\"NewValue\" label=\"变更后\" />\r\n                    </el-table>\r\n                    <span slot=\"reference\" style=\"cursor: pointer;\" :class=\"getChangeStyle(row.changeContent)\">\r\n                      {{ row.changeContent }}\r\n                    </span>\r\n                  </el-popover>\r\n\r\n                </template>\r\n                <template v-else-if=\"item.Code === 'Production_Status'\" #default=\"{ row }\">\r\n                  <el-link v-if=\"row.MocIdBefore&& row[item.Code]\" :type=\"row.Production_Status === '未生产' ? 'info' : 'primary'\" @click=\"handleOpen(row)\"> {{ row[item.Code] | displayValue }}</el-link>\r\n                  <span v-else>-</span>\r\n                </template>\r\n                <template v-else-if=\"item.Code === 'Is_Component_Mark'\" #default=\"{ row }\">\r\n                  <!--             是否直发件 是：是直发件；否：非直发件；-->\r\n                  <template v-if=\"row.Type===0\">\r\n                    <el-tag v-if=\"row[item.Code]==='是'\" type=\"primary\">是</el-tag>\r\n                    <el-tag v-else type=\"danger\" >否</el-tag>\r\n                  </template>\r\n                  <template v-else>\r\n                    <el-tag v-if=\"row.PartType==='直发件'\" type=\"primary\">是</el-tag>\r\n                    <el-tag v-else type=\"danger\">否</el-tag>\r\n                  </template>\r\n\r\n                </template>\r\n                <template v-else #default=\"{ row }\">\r\n                  <span> {{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n            </template>\r\n\r\n            <!-- Operations column for manual edit mode -->\r\n            <vxe-column v-if=\"changeMethod === 3 && !isView\" fixed=\"right\" title=\"操作\" width=\"160\">\r\n              <template #default=\"{ row }\">\r\n                <el-button\r\n                  v-if=\"row.changeType !== 'isDelete'\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  @click=\"handleEdit(row)\"\r\n                >\r\n                  编辑\r\n                </el-button>\r\n                <el-button\r\n                  v-if=\"row.changeType !== 'isDelete'\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  style=\"color: #FB6B7F;\"\r\n                  @click=\"handleDelete(row)\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n                <el-button\r\n                  v-if=\"row.changeType === 'isDelete' && !row.isDisabled\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  style=\"color: #298DFF;\"\r\n                  @click=\"handleRestore(row)\"\r\n                >\r\n                  撤销删除\r\n                </el-button>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-table>\r\n        </div>\r\n      </template>\r\n      <footer v-if=\"!isView\">\r\n        <el-button :disabled=\"disableSave\" :loading=\"saveLoading\" @click=\"handleSave\">保存草稿</el-button>\r\n        <el-button :disabled=\"disableSave || uploadLoading\" :loading=\"submitLoading\" type=\"primary\" @click=\"handleSubmit\">提交审核</el-button>\r\n      </footer>\r\n    </div>\r\n    <ImportFile ref=\"dialog\" @refresh=\"getTableInfo\" />\r\n    <StatusDialog ref=\"statusDialog\" />\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      v-dialogDrag\r\n      top=\"6vh\"\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"width\"\r\n      class=\"plm-custom-dialog\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        @getMocModelList=\"getMocModelList\"\r\n        @editInfo=\"editInfo\"\r\n        @close=\"handleClose\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GeAreaTrees, GetInstallUnitIdNameList, GetProjectPageList } from '@/api/PRO/project'\r\nimport { GetFactoryPeoplelist } from '@/api/PRO/basic-information/workshop'\r\nimport {\r\n  GetMocOrderInfo,\r\n  GetMocOrderTypeList,\r\n  ImportChangFile,\r\n  SaveMocOrder,\r\n  SubmitMocOrder\r\n} from '@/api/PRO/changeManagement'\r\nimport { GetCompanyDepartTree, GetOssUrl } from '@/api/sys'\r\nimport ImportFile from './components/importFile.vue'\r\nimport OSSUpload from '@/views/plm/components/ossupload.vue'\r\nimport { closeTagView, combineURL, debounce, deepClone } from '@/utils'\r\nimport StatusDialog from './components/dialog.vue'\r\nimport numeral from 'numeral'\r\nimport { GetTableSettingList } from '@/api/PRO/component-type'\r\nimport HandleEdit from './components/HandleEdit.vue'\r\nimport AddHandle from './components/addHandle.vue'\r\nimport { changeType, changeTypeReverse, getAllCodesByType } from './utils'\r\nimport { getFileNameFromUrl } from '@/utils/file'\r\nimport { isArray } from 'ali-oss/lib/common/utils/isArray'\r\nimport SteelComponentManager from '@/views/PRO/change-management/contact-list/info'\r\nimport { GetCurFactory } from '@/api/PRO/factory'\r\n\r\nexport default {\r\n  name: 'PROEngineeringChangeOrderAdd',\r\n  components: {\r\n    OSSUpload,\r\n    StatusDialog,\r\n    ImportFile,\r\n    HandleEdit,\r\n    AddHandle\r\n  },\r\n  mixins: [SteelComponentManager],\r\n  data() {\r\n    return {\r\n      curStatus: {\r\n        del: '已删',\r\n        change: '变更',\r\n        add: '新增',\r\n        increase: '数量增加',\r\n        decrease: '数量减少',\r\n        unChange: '无变更'\r\n      },\r\n\r\n      dialogVisible: false,\r\n      title: '',\r\n      width: '50%',\r\n      currentComponent: '',\r\n      filePath: '',\r\n      finishFee: 0,\r\n      pageLoading: false,\r\n      saveLoading: false,\r\n      submitLoading: false,\r\n      uploadLoading: false,\r\n      tbLoading: false,\r\n      tbData: [],\r\n      activities: [],\r\n      fileList: [],\r\n      multipleSelection: [],\r\n      changeRowContentList: [],\r\n      filterCodeOptions: [{ data: '' }],\r\n      columns: [],\r\n      changeMethod: 1,\r\n      searchForm: {\r\n        component_name: '',\r\n        part_name: '',\r\n        assembly_name: '',\r\n        component_search_mode: 1,\r\n        part_search_mode: 1,\r\n        assembly_search_mode: 1,\r\n        content: ''\r\n      },\r\n      form: {\r\n        Sys_Project_Id: '',\r\n        Area_Id: '',\r\n        InstallUnit_Ids: [],\r\n        Handle_UserId: '',\r\n        Moc_Type_Id: '',\r\n        Fee: undefined,\r\n        Hours: undefined,\r\n        Urgency: 1,\r\n        Demand_Date: '',\r\n        Fee_DepartId: '',\r\n        Remark: '',\r\n        AttachmentList: []\r\n      },\r\n      showImport: false,\r\n      treeParams: {\r\n        data: [],\r\n        filterable: false,\r\n        clickParent: true,\r\n        props: {\r\n          disabled: 'disabled',\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      peopleList: [],\r\n      projectList: [],\r\n      changeTypeList: [],\r\n      installUnitList: [],\r\n      treeParamsArea: {\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          disabled: 'disabled',\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      rules: {\r\n        Sys_Project_Id: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        Area_Id: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        Handle_UserId: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        Moc_Type_Id: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        InstallUnit_Ids: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ]\r\n      },\r\n      toolbarButtons: [\r\n        { code: 'myToolbarExport', name: '点击导出' },\r\n        { code: 'myToolbarLink', name: '点击跳转' },\r\n        { code: 'myToolbarCustom', name: '打开自定义列' }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    disableSave() {\r\n      return this.submitLoading || this.uploadLoading\r\n    },\r\n    isView() {\r\n      return this.$route.query.type == '2'\r\n    },\r\n    isEdit() {\r\n      return this.$route.query.type == '1'\r\n    },\r\n    showDetail() {\r\n      const zbtk = this.changeTypeList.find(item => {\r\n        return item.Id === this.form.Moc_Type_Id\r\n      })\r\n      return zbtk?.Is_Deepen_Change || false\r\n    },\r\n    getFileName() {\r\n      return getFileNameFromUrl(this.filePath)\r\n    }\r\n  },\r\n  watch: {\r\n    'form.Area_Id': {\r\n      handler(newVal) {\r\n        if (!newVal) {\r\n          this.rules.InstallUnit_Ids[0].required = false\r\n          this.$refs?.installUnitRef?.clearValidate()\r\n        }\r\n      },\r\n      immediate: true\r\n    },\r\n    'form.InstallUnit_Ids': {\r\n      handler(newVal) {\r\n        if (!this.Area_Id) return\r\n        if (this.installUnitList.length) {\r\n          this.rules.InstallUnit_Ids[0].required = true\r\n          this.$refs.installUnitRef.clearValidate()\r\n        } else {\r\n          this.rules.InstallUnit_Ids[0].required = false\r\n          this.$refs.installUnitRef.clearValidate()\r\n        }\r\n      }\r\n    }\r\n  },\r\n  async created() {\r\n    // await this.getTableConfig('PROEngChangeOrderAdd') ProfessionalCode\r\n    this.$store.dispatch('contactList/resetChangeCode')\r\n    this.getProjectData()\r\n    this.getFactoryPeople()\r\n    await this.getDepTree()\r\n    this.getFactoryChangeTypeList()\r\n\r\n    const id = this.$route.query.id\r\n    id && await this.getInfo(id)\r\n    this.getTableConfig()\r\n    // this.getTableInfo()\r\n  },\r\n  methods: {\r\n    async getTableConfig() {\r\n      const res = await GetTableSettingList({ ProfessionalCode: 'Steel' })\r\n      if (res.IsSucceed) {\r\n        this.allCodes = JSON.parse(JSON.stringify(res.Data))\r\n        // Filter out the three name columns\r\n        const filteredColumns = res.Data.filter(item =>\r\n          !['SteelName', 'ComponentName', 'PartName'].includes(item.Code)\r\n        )\r\n\r\n        // Add the new CPCode column\r\n        const codeColumn = {\r\n          Code: 'CPCode',\r\n          Display_Name: '构件/部件/零件名称',\r\n          Is_Frozen: true,\r\n          Frozen_Dirction: 'left',\r\n          Width: 180,\r\n          Align: 'left'\r\n        }\r\n\r\n        // Insert the CPCode column at the beginning\r\n        filteredColumns.unshift(codeColumn)\r\n        const _customColumns = [{\r\n          Code: 'Production_Status',\r\n          Display_Name: '生产情况',\r\n          Align: 'center',\r\n          Is_Frozen: true,\r\n          Frozen_Dirction: 'right',\r\n          Width: 120\r\n        }, {\r\n          Code: 'changeContent',\r\n          Display_Name: '变更内容',\r\n          Align: 'center',\r\n          Is_Frozen: true,\r\n          Frozen_Dirction: 'right',\r\n          Width: 120\r\n        }]\r\n\r\n        filteredColumns.push(..._customColumns)\r\n        let _columns = []\r\n\r\n        this.rootColumns = deepClone(filteredColumns.map(item => {\r\n          const displayNameLength = item.Display_Name?.length || 0\r\n          const width = Math.max(120, 120 + Math.max(0, displayNameLength - 4) * 10)\r\n          return {\r\n            ...item,\r\n            Width: width,\r\n            Align: 'center'\r\n          }\r\n        }))\r\n\r\n        if (this.changeMethod === 3) {\r\n          const columnCode = ['CPCode', 'SetupPosition', 'Production_Status', 'changeContent']\r\n          _columns = this.rootColumns.filter(item => columnCode.includes(item.Code))\r\n        } else {\r\n          _columns = this.rootColumns\r\n        }\r\n        this.columns = _columns\r\n      }\r\n    },\r\n    async getInfo(id) {\r\n      this.pageLoading = true\r\n      await GetMocOrderInfo({\r\n        Id: id\r\n      }).then(async res => {\r\n        if (res.IsSucceed) {\r\n          const {\r\n            Deepen_File_Url,\r\n            Sys_Project_Id,\r\n            Area_Id,\r\n            InstallUnit_Ids,\r\n            Handle_UserId,\r\n            Moc_Type_Id,\r\n            Change_Type,\r\n            Fee,\r\n            Hours,\r\n            Urgency,\r\n            Demand_Date,\r\n            Fee_DepartId,\r\n            Remark,\r\n            FeeHistory,\r\n            Id,\r\n            Status,\r\n            AttachmentList,\r\n            OrderDetail\r\n          } = res.Data\r\n          this.activities = FeeHistory\r\n          if (FeeHistory.length) {\r\n            const [last] = FeeHistory.slice(-1)\r\n            this.finishFee = numeral(last?.Fee || 0).format('0.[00]')\r\n          }\r\n          if (Status === 3) {\r\n            const idx = this.columns.findIndex(item => item.Code === 'Production_Status')\r\n            if (idx !== -1) {\r\n              this.columns.splice(idx, 1)\r\n            }\r\n          }\r\n\r\n          if (AttachmentList?.length) {\r\n            AttachmentList.forEach((element, idx) => {\r\n              const obj = {\r\n                name: element.File_Name,\r\n                url: element.File_Url\r\n              }\r\n              this.fileList.push(obj)\r\n              this.form.AttachmentList.push({\r\n                File_Url: element.File_Url,\r\n                File_Name: element.File_Name\r\n              })\r\n            })\r\n          }\r\n          await this.getAreaList(Sys_Project_Id)\r\n          await this.getInstallUnitPageList(Area_Id)\r\n\r\n          Object.assign(this.form, {\r\n            ...res.Data,\r\n            Sys_Project_Id,\r\n            Area_Id,\r\n            Deepen_File_Url,\r\n            Id,\r\n            InstallUnit_Ids: InstallUnit_Ids ? (typeof InstallUnit_Ids === 'string' ? InstallUnit_Ids.split(',') : InstallUnit_Ids) : [],\r\n            Handle_UserId,\r\n            Moc_Type_Id,\r\n            Fee: Fee || undefined,\r\n            Hour: Hours || undefined,\r\n            Urgency: Number(Urgency),\r\n            Demand_Date,\r\n            Fee_DepartId,\r\n            Remark\r\n          })\r\n\r\n          this.setTbData(OrderDetail)\r\n          this.filePath = Deepen_File_Url\r\n          this.changeMethod = Change_Type === 0 ? 1 : Change_Type === 1 ? 2 : 3\r\n          // Deepen_File_Url\r\n          // setTimeout(() => {\r\n          // Deepen_File_Url && this.getTableInfo({ File_Url: Deepen_File_Url })\r\n          // }, 0)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.pageLoading = false\r\n      })\r\n    },\r\n    mocTypeChange() {\r\n      this.tbData = []\r\n      this.handleReset()\r\n    },\r\n\r\n    handleExceed(files, fileList) {\r\n      this.$message.warning(`当前限制选择 5 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)\r\n    },\r\n    handleProgress(event, files, fileList) {\r\n    },\r\n    handleError(err, files, fileList) {\r\n      console.log('err3', err, files, fileList)\r\n      this.checkUploading(fileList)\r\n    },\r\n    checkUploading(fileList) {\r\n      const flag = fileList.every(v => v.status === 'success')\r\n      flag && (this.uploadLoading = false)\r\n    },\r\n    handleImport() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (valid) {\r\n          console.log('valid', valid)\r\n          this.$refs['dialog'].handleOpen()\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    handleAdd() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (valid) {\r\n          this.title = '添加变更内容'\r\n          this.width = '70%'\r\n          this.currentComponent = 'addHandle'\r\n          this.dialogVisible = true\r\n          this.$nextTick(_ => {\r\n            this.$refs['content'].handleOpen(this.form, this.tbData)\r\n          })\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    handleOpen(row) {\r\n      this.$refs['statusDialog'].handleOpen(row)\r\n    },\r\n    getProjectData() {\r\n      GetProjectPageList({ PageSize: -1 }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.projectList = res.Data.Data\r\n        }\r\n      })\r\n    },\r\n    setDisabledTree(root) {\r\n      if (!root) return\r\n      root.forEach((element) => {\r\n        const { Children } = element\r\n        if (Children && Children.length) {\r\n          element.disabled = true\r\n        } else {\r\n          element.disabled = false\r\n          this.setDisabledTree(Children)\r\n        }\r\n      })\r\n    },\r\n    async getAreaList(Pid) {\r\n      await GeAreaTrees({\r\n        sysProjectId: Pid\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const tree = res.Data\r\n          this.setDisabledTree(tree)\r\n          this.treeParamsArea.data = res.Data\r\n          this.$nextTick(_ => {\r\n            this.$refs.treeSelectArea.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async getInstallUnitPageList(areaId) {\r\n      await GetInstallUnitIdNameList({\r\n        Area_Id: areaId,\r\n        Page: 1,\r\n        PageSize: -1\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.installUnitList = res.Data\r\n          if (this.installUnitList.length) {\r\n            this.rules.InstallUnit_Ids[0].required = true\r\n          } else {\r\n            this.rules.InstallUnit_Ids[0].required = false\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async projectChange() {\r\n      const Sys_Project_Id = this.form.Sys_Project_Id\r\n      this.clearTb()\r\n      this.form.Area_Id = ''\r\n      this.form.InstallUnit_Ids = []\r\n      this.treeParamsArea.data = []\r\n      this.$nextTick(_ => {\r\n        this.$refs.treeSelectArea.treeDataUpdateFun([])\r\n      })\r\n      if (Sys_Project_Id) {\r\n        await this.getAreaList(Sys_Project_Id)\r\n      }\r\n    },\r\n    async areaChange() {\r\n      this.clearTb()\r\n      await this.getInstallUnitPageList(this.form.Area_Id)\r\n      if (this.installUnitList.length && this.form.Area_Id) {\r\n        this.form.InstallUnit_Ids = [this.installUnitList[0].Id]\r\n        this.rules.InstallUnit_Ids[0].required = true\r\n      } else {\r\n        this.rules.InstallUnit_Ids[0].required = false\r\n        this.$refs.installUnitRef.clearValidate()\r\n      }\r\n    },\r\n    areaClear() {\r\n      this.form.Area_Id = ''\r\n      this.form.InstallUnit_Ids = []\r\n      this.clearTb()\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    getFactoryPeople() {\r\n      GetFactoryPeoplelist({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.peopleList = Object.freeze(res.Data)\r\n\r\n          const curId = localStorage.getItem('UserId')\r\n          const cur = this.peopleList.find(v => v.Id === curId)\r\n          if (cur) {\r\n            this.form.Handle_UserId = cur.Id\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getFactoryChangeTypeList() {\r\n      GetMocOrderTypeList({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.changeTypeList = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async getDepTree() {\r\n      const getFactoryDeptId = async() => {\r\n        return await GetCurFactory({}).then((res) => {\r\n          if (res.IsSucceed) {\r\n            return res?.Data[0]?.Dept_Id\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n\r\n      const getDept = async(depId) => {\r\n        await GetCompanyDepartTree({}).then(res => {\r\n          if (res.IsSucceed) {\r\n            const origin = res.Data?.[0]\r\n            if (origin.Children.length) {\r\n              const tree = origin.Children.filter(v => v.Id === depId)\r\n\r\n              const disableDirectory = (treeArray) => {\r\n                treeArray.map(element => {\r\n                  if (element.Children && element.Children.length > 0) {\r\n                    element.disabled = true\r\n                    disableDirectory(element.Children)\r\n                  }\r\n                })\r\n              }\r\n              disableDirectory(tree)\r\n              this.$refs.treeSelect.treeDataUpdateFun(tree || [])\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n      const depId = await getFactoryDeptId()\r\n      await getDept(depId)\r\n    },\r\n    getTableInfo(fileObj) {\r\n      this.tbLoading = true\r\n      const form = { ...this.form }\r\n      ImportChangFile({\r\n        ...form,\r\n        ImportType: this.changeMethod,\r\n        InstallUnit_Ids: this.form.InstallUnit_Ids.toString(),\r\n        AttachmentList: [fileObj]\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.isImportFile = true\r\n          const { AttachmentList, MocOrderDetailList } = res.Data\r\n          // this.getTbList(Orignal_Deepen_List, Import_Deepen_List)\r\n          // filePath\r\n          if (AttachmentList.length) {\r\n            this.filePath = AttachmentList[0].File_Url\r\n            this.form.Deepen_File_Url = this.filePath\r\n            this.form.Deepen_File_Url_List = [fileObj]\r\n          }\r\n          this.setTbData(MocOrderDetailList)\r\n        } else {\r\n          if (res.Data && res.Data.ErrorFileUrl) {\r\n            window.open(combineURL(this.$baseUrl, res.Data.ErrorFileUrl), '_blank')\r\n          }\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    setAllWeight(row) {\r\n      return {\r\n        SteelAllWeight: numeral(row.SteelWeight).multiply(row.SteelAmount).format('0.[000]')\r\n      }\r\n    },\r\n    setTbData(list) {\r\n      const setItem = (list, item, isAfterValue = false) => {\r\n        const updatedItem = {\r\n          ...item,\r\n          CodeType: item.Type === 0 ? 1 : item.Type === 1 ? 2 : 3,\r\n          parentChildrenId: item.Id,\r\n          uuid: item.MocIdBefore,\r\n          checked: false,\r\n          changeContent: item.MocType,\r\n          changeType: changeTypeReverse[item.MocType],\r\n          ...this.setAllWeight(item)\r\n        }\r\n        this.setItemMocContent(updatedItem, isAfterValue)\r\n        if (updatedItem.changeType === 'isDelete') {\r\n          const childrenItems = this.findChildItems(updatedItem, list)\r\n          if (childrenItems.length) {\r\n            childrenItems.forEach(childItem => {\r\n              childItem.isDisabled = true\r\n            })\r\n          }\r\n        }\r\n        return updatedItem\r\n      }\r\n\r\n      this.defaultTbData = list.map(item => setItem(list, item))\r\n\r\n      this.tbData = list.map(item => setItem(list, item, true))\r\n    },\r\n    setItemMocContent(item, isAfterValue = false) {\r\n      if (item.MocContent) {\r\n        let _MocContent = JSON.parse(item.MocContent)\r\n        if (isArray(_MocContent) && _MocContent.length) {\r\n          _MocContent = _MocContent.filter(m => m.ChangeFieldCode !== 'PartNum')\r\n          const _list = _MocContent.map(m => {\r\n            const _codes = getAllCodesByType(item.CodeType)\r\n            const cur = _codes.find(v => v.Code === m.ChangeFieldCode)\r\n            item[m.ChangeFieldCode] = isAfterValue ? m.AfterValue : m.BeforeValue\r\n\r\n            return {\r\n              Field_Type: cur?.Field_Type || 'string',\r\n              IsCoreField: cur?.IsCoreField || false,\r\n              Code: m.ChangeFieldCode,\r\n              Name: m.ChangeFieldName,\r\n              Value: m.BeforeValue,\r\n              NewValue: m.AfterValue\r\n            }\r\n          })\r\n          this.$store.dispatch('contactList/addChangeCode', { uuid: item.uuid, list: _list })\r\n        }\r\n      }\r\n    },\r\n    beforeUpload(file) {\r\n      this.uploadLoading = true\r\n    },\r\n    handleChange(file, fileList) {\r\n      this.fileList = fileList\r\n    },\r\n    async handlePreview(file) {\r\n      console.log(file)\r\n      const arr = file.name.split('.')\r\n      const isDwg = arr[arr.length - 1] === 'dwg'\r\n      const { Data } = await GetOssUrl({ url: file.url })\r\n      if (isDwg) {\r\n        window.open('http://dwgv1.bimtk.com:5432/?CadUrl=' + Data, '_blank')\r\n      } else {\r\n        window.open(Data)\r\n      }\r\n    },\r\n    handleRemove(file, fileList) {\r\n      this.fileList = fileList\r\n      this.checkUploading(fileList)\r\n      this.form.AttachmentList = this.fileList.map(item => {\r\n        if (item.url) {\r\n          return {\r\n            File_Url: item.url,\r\n            File_Name: item.name\r\n          }\r\n        } else {\r\n          const url = item.response.Data\r\n          const fileInfo = url.split('*')\r\n          const fileObj = {\r\n            File_Url: fileInfo[0],\r\n            File_Size: fileInfo[1],\r\n            File_Type: fileInfo[2],\r\n            File_Name: fileInfo[3]\r\n          }\r\n          return {\r\n            File_Url: fileObj.File_Url,\r\n            File_Name: fileObj.File_Name\r\n          }\r\n        }\r\n      })\r\n    },\r\n    uploadSuccess(response, file, fileList) {\r\n      if (!response || !response.Data) {\r\n        return\r\n      }\r\n      this.checkUploading(fileList)\r\n      this.form.AttachmentList = this.fileList.map(item => {\r\n        if (item.url) {\r\n          return {\r\n            File_Url: item.url,\r\n            File_Name: item.name\r\n          }\r\n        } else {\r\n          if (item.status !== 'success') return\r\n          const url = item.response.Data\r\n          const fileInfo = url.split('*')\r\n          const fileObj = {\r\n            File_Url: fileInfo[0],\r\n            File_Size: fileInfo[1],\r\n            File_Type: fileInfo[2],\r\n            File_Name: fileInfo[3]\r\n          }\r\n          return {\r\n            File_Url: fileObj.File_Url,\r\n            File_Name: fileObj.File_Name\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    handleSave() {\r\n      this.$refs['form'].validate(async(valid) => {\r\n        if (valid) {\r\n          this.saveLoading = true\r\n          await this.submit(true)\r\n          this.saveLoading = false\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    async handleSubmit() {\r\n      this.$refs['form'].validate(async(valid) => {\r\n        if (valid) {\r\n          this.submitLoading = true\r\n          await this.submit(false)\r\n          this.submitLoading = false\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    async submit(isDraft) {\r\n      console.log('this.form', this.form)\r\n      const _form = { ...this.form }\r\n      let submitTb = []\r\n      if (this.changeMethod === 3) {\r\n        submitTb = this.tbData.map((item) => {\r\n          const { children, uuid, changeContent, checked, changeType, isShow, ...others } = item\r\n          const changeMap = this.$store.state.contactList.changeCode\r\n          const _list = (changeMap[uuid] || []).map(v => {\r\n            others[v.Code] = v.NewValue\r\n            return {\r\n              ChangeFieldCode: v.Code,\r\n              ChangeFieldName: v.Name,\r\n              BeforeValue: v.Value,\r\n              AfterValue: v.NewValue\r\n            }\r\n          })\r\n          others.MocContent = JSON.stringify(_list)\r\n          others.MocType = changeContent\r\n          return others\r\n        })\r\n        console.log(JSON.parse(JSON.stringify(submitTb)))\r\n        _form.Deepen_File_Url = null\r\n        _form.Deepen_File_Url_List = null\r\n      } else {\r\n        submitTb = this.tbData\r\n      }\r\n      const isReNew = this.isImportFile && this.changeMethod !== 3 && this.isEdit\r\n      const subObj = {\r\n        ..._form,\r\n        IsNewImportFile: isReNew,\r\n        Handle_UserName: localStorage.getItem('UserName'),\r\n        Moc_Type_Name: this.changeTypeList.find(item => item.Id === _form.Moc_Type_Id)?.Display_Name,\r\n        Change_Type: this.changeMethod === 1 ? 0 : this.changeMethod === 2 ? 1 : 2,\r\n        InstallUnit_Ids: Array.isArray(_form.InstallUnit_Ids) ? _form.InstallUnit_Ids.join(',') : _form.InstallUnit_Ids,\r\n        Is_Draft: isDraft,\r\n        OrderDetail: submitTb\r\n      }\r\n      if (this.changeMethod !== 3) {\r\n        subObj.Deepen_File_Url = this.filePath\r\n      }\r\n      await SaveMocOrder(subObj).then(async res => {\r\n        if (res.IsSucceed) {\r\n          if (isDraft) {\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            closeTagView(this.$store, this.$route)\r\n          } else {\r\n            if (!res.Data) {\r\n              this.$message({\r\n                message: '提交失败',\r\n                type: 'wrarning'\r\n              })\r\n              return\r\n            }\r\n            await this.submitCheck(res.Data)\r\n          }\r\n        } else {\r\n          if (res.Data && res.Data.Path) {\r\n            window.open(combineURL(this.$baseUrl, res.Data.Path), '_blank')\r\n          }\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async submitCheck(Id) {\r\n      await SubmitMocOrder({\r\n        Id\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '提交成功',\r\n            type: 'success'\r\n          })\r\n          closeTagView(this.$store, this.$route)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.searchForm.component_name = ''\r\n      this.searchForm.part_name = ''\r\n      this.searchForm.assembly_name = ''\r\n      this.searchForm.component_search_mode = 1\r\n      this.searchForm.part_search_mode = 1\r\n      this.searchForm.assembly_search_mode = 1\r\n      this.$refs?.tableRef?.setAllTreeExpand(false)\r\n      this.$refs?.tableRef?.clearFilter()\r\n    },\r\n    clearTb() {\r\n      this.tbData = []\r\n      this.defaultTbData = []\r\n      this.handleReset()\r\n    },\r\n    handleFilter() {\r\n      this.nameMapping = {\r\n        ComponentName: {},\r\n        SteelName: {},\r\n        PartName: {}\r\n      }\r\n      const changeMaps = this.$store.state.contactList.changeCode\r\n      Object.keys(changeMaps).forEach(uuid => {\r\n        const changeList = changeMaps[uuid]\r\n        changeList.forEach(item => {\r\n          if (item.Code === 'ComponentName') {\r\n            this.nameMapping.ComponentName[item.Value] = item.NewValue\r\n          } else if (item.Code === 'SteelName') {\r\n            this.nameMapping.SteelName[item.Value] = item.NewValue\r\n          } else if (item.Code === 'PartName') {\r\n            this.nameMapping.PartName[item.Value] = item.NewValue\r\n          }\r\n        })\r\n      })\r\n\r\n      const xTable = this.$refs.tableRef\r\n      const codeColumn = xTable.getColumnByField('CPCode')\r\n      const option = codeColumn.filters[0]\r\n      option.data = [this.searchForm.component_name, this.searchForm.assembly_name, this.searchForm.part_name]\r\n      option.checked = true\r\n      xTable.updateData()\r\n      this.$refs.tableRef.setAllTreeExpand(true)\r\n      this.$refs.tableRef.clearCheckboxRow()\r\n    },\r\n\r\n    getFeeGap(activity, index) {\r\n      if (index === 0) {\r\n        return activity.Fee || 0\r\n      } else {\r\n        const result = numeral(activity.Fee || 0)\r\n          .subtract(this.activities[index - 1].Fee || 0)\r\n\r\n        if (result.value() < 0) {\r\n          activity.isRed = true\r\n        } else if (result.value() > 0) {\r\n          activity.isBlue = true\r\n        }\r\n        return result.value() === 0 ? 0 : result.format('+0.[00]')\r\n      }\r\n    },\r\n    handleEdit(row) {\r\n      console.log(row, 'row')\r\n      this.dialogVisible = true\r\n      this.currentComponent = 'HandleEdit'\r\n      this.width = '50%'\r\n      this.title = `编辑（${this.getCpCode(row)}）`\r\n      this.$nextTick(() => {\r\n        const defaultRow = this.defaultTbData.find(item => item.uuid === row.uuid)\r\n        console.log(defaultRow, 'defaultRow')\r\n        this.$refs.content?.init(row, defaultRow, this.isEdit, this.tbData, this.allCodes)\r\n      })\r\n    },\r\n    handleDelete(row) {\r\n      console.log('row', row)\r\n      this.$confirm('确认要删除这条记录吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.deleteTableItem(row.uuid)\r\n      }).catch(() => {\r\n        // User canceled\r\n      })\r\n    },\r\n    handleRestore(row) {\r\n      this.restoreTableItem(row.uuid)\r\n    },\r\n    changeMethodFun(val) {\r\n      console.log('val', val)\r\n      const setTbCloumn = () => {\r\n        if (val === 3) {\r\n          const columnCode = ['CPCode', 'SetupPosition', 'Production_Status', 'changeContent']\r\n          const _columns = this.columns.filter(item => columnCode.includes(item.Code))\r\n          this.columns = _columns\r\n          this.$nextTick(_ => {\r\n            this.$refs.tableRef.refreshColumn()\r\n          })\r\n        } else if (val === 1) {\r\n          this.columns = deepClone(this.rootColumns)\r\n        } else {\r\n          this.columns = deepClone(this.rootColumns)\r\n        }\r\n        this.$store.dispatch('contactList/resetChangeCode')\r\n        this.changeMethod = val\r\n      }\r\n\r\n      if (this.tbData && this.tbData.length > 0) {\r\n        return this.$confirm('切换变更方式会清空当前已添加的变更明细，是否继续？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.tbData = []\r\n          setTbCloumn()\r\n          this.filePath = ''\r\n        }).catch(() => {\r\n\r\n        })\r\n      } else {\r\n        this.filePath = ''\r\n        setTbCloumn()\r\n      }\r\n    },\r\n    getMocModelList(list) {\r\n      const existingUuids = new Set(this.tbData.map(item => item.uuid))\r\n      list = list.filter(item => !existingUuids.has(item.MocIdBefore))\r\n\r\n      if (!list.length) {\r\n        return\r\n      }\r\n\r\n      list = list.map(item => {\r\n        // const curParent = this.findParentItem(item)\r\n        // if (curParent && curParent.changeType === 'isDelete') {\r\n        //   item.changeType = 'isDelete'\r\n        //   item.changeContent = this.getChangeTypeText(item.changeType)\r\n        //   item.isDisabled = true\r\n        //   this.deleteTableItem(item.uuid)\r\n        //   return {\r\n        //     ...item,\r\n        //     parentChildrenId: item.Id,\r\n        //     uuid: item.MocIdBefore,\r\n        //     checked: false,\r\n        //     CodeType: item.Type === 0 ? 1 : item.Type === 1 ? 2 : 3,\r\n        //     ...this.setAllWeight(item)\r\n        //   }\r\n        // } else {\r\n        this.updateItemChangeStatus(item, [])\r\n        return {\r\n          changeType: 'isNoChange',\r\n          changeContent: this.getChangeTypeText('isNoChange'),\r\n          ...item,\r\n          parentChildrenId: item.Id,\r\n          uuid: item.MocIdBefore,\r\n          checked: false,\r\n          CodeType: item.Type === 0 ? 1 : item.Type === 1 ? 2 : 3,\r\n          ...this.setAllWeight(item)\r\n        }\r\n        // }\r\n      })\r\n\r\n      this.tbData = [...this.tbData, ...list].sort((a, b) => b.Type - a.Type)\r\n\r\n      const _defaultTbData = this.defaultTbData || []\r\n      this.defaultTbData = JSON.parse(JSON.stringify([..._defaultTbData, ...list]))\r\n      this.setSameItems(this.tbData)\r\n    },\r\n    setSameItems(tbData) {\r\n      const changeInfos = { ...this.$store.state.contactList.changeCode }\r\n      const mocBeforeItems = tbData.filter(item => {\r\n        return !!changeInfos[item.uuid]\r\n      })\r\n      const isDeleteItems = tbData.filter(item => item.changeType === 'isDelete')\r\n      if (isDeleteItems.length) {\r\n        console.log(isDeleteItems, 'isDeleteItems')\r\n        const unitPart = isDeleteItems.filter(item => item.Type === 3)\r\n        if (unitPart.length) {\r\n          unitPart.forEach(item => {\r\n            const unitP = this.findParentItem(item)\r\n            if (unitP && unitP.changeType !== 'isDelete') {\r\n              const similarUnitPartItems = this.findSimilarItems(item)\r\n              if (similarUnitPartItems.length) {\r\n                similarUnitPartItems.forEach(similarItem => {\r\n                  const isSame = this.isSameParent(item, similarItem)\r\n                  if (!isSame) return\r\n                  this.$set(similarItem, 'changeType', 'isDelete')\r\n                  this.$set(similarItem, 'changeContent', this.getChangeTypeText('isDelete'))\r\n                })\r\n              }\r\n            }\r\n          })\r\n        }\r\n        // isDeleteItems.forEach(item => {\r\n        //   const similarItems = this.findSimilarItems(item)\r\n        //   if (similarItems.length) {\r\n        //     similarItems.forEach(similarItem => {\r\n        //       console.log(item.Code, 'similarItems')\r\n        //       this.$set(similarItem, 'changeType', item.changeType)\r\n        //       this.$set(similarItem, 'changeContent', item.changeContent)\r\n        //       // const isDisabled = this.isSameParent(item, similarItem)\r\n        //       // this.$set(similarItem, 'isDisabled', !isDisabled)\r\n        //       // if (isDisabled) {\r\n        // this.$set(similarItem, 'changeType', item.changeType)\r\n        // this.$set(similarItem, 'changeContent', item.changeContent)\r\n        //       // }\r\n        //     })\r\n        //   }\r\n        // })\r\n      }\r\n      if (!mocBeforeItems.length) return\r\n      mocBeforeItems.forEach(item => {\r\n        let _list = this.findSimilarItems(item)\r\n        _list = _list.filter(k => !changeInfos[k.uuid])\r\n        if (_list.length) {\r\n          _list.forEach(cur => {\r\n            if (this.isSameParent(item, cur)) {\r\n              const changeList = this.$store.state.contactList.changeCode[item.uuid]\r\n              changeList.forEach(change => {\r\n                cur[change.Code] = item[change.Code]\r\n              })\r\n\r\n              this.$store.dispatch('contactList/addChangeCode', {\r\n                uuid: cur.uuid,\r\n                list: changeList\r\n              })\r\n              console.log('cur', item.isDisabled)\r\n              if (item.changeType === 'isDelete') {\r\n                // this.$set(cur, 'isDisabled', item.isDisabled)\r\n\r\n              } else {\r\n                this.updateItemChangeStatus(cur, changeList)\r\n              }\r\n            } else {\r\n              const { SteelAmount, ...others } = item\r\n              const filteredList = (this.$store.state.contactList.changeCode[item.uuid] || []).filter(change => change.Code !== 'SteelAmount')\r\n              filteredList.forEach(change => {\r\n                cur[change.Code] = item[change.Code]\r\n              })\r\n              // cur.CPCode = item.CPCode\r\n              this.$store.dispatch('contactList/addChangeCode', {\r\n                uuid: cur.uuid,\r\n                list: filteredList\r\n              })\r\n              this.updateItemChangeStatus(cur, filteredList)\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    editInfo({ row, list }) {\r\n      console.log('editInfo row, list', row, list)\r\n      const _changeMaps = {}\r\n      list.forEach(item => {\r\n        _changeMaps[item.Code] = item.NewValue\r\n      })\r\n      // this.resetDefaultVal()\r\n\r\n      const existingChanges = this.$store.state.contactList.changeCode[row.uuid] || []\r\n      const existingChangeCodes = existingChanges.map(change => change.Code)\r\n\r\n      const removedChangeCodes = existingChangeCodes.filter(code => !list.some(item => item.Code === code))\r\n      console.log('已移除的字段', removedChangeCodes)\r\n\r\n      if (removedChangeCodes.length) {\r\n        const defaultRow = this.defaultTbData.find(item => item.uuid === row.uuid)\r\n        removedChangeCodes.forEach(code => {\r\n          console.log(`重置字段 ${code} 为原始值:`, defaultRow[code])\r\n          _changeMaps[code] = defaultRow[code]\r\n        })\r\n      }\r\n      console.log('_changeMaps', JSON.parse(JSON.stringify(_changeMaps)))\r\n\r\n      // 批量更新表格项\r\n      this.batchUpdateTableItem(row.uuid, _changeMaps)\r\n      // this.updateCodesName(row, _changeMaps)\r\n    },\r\n    // updateCodesName(targetItem, _changeMaps) {\r\n    //   if (_changeMaps.SteelName) {\r\n    //     targetItem.SteelName = _changeMaps.SteelName\r\n    //     targetItem.CPCode = _changeMaps.SteelName\r\n    //   } else if (_changeMaps.ComponentName) {\r\n    //     targetItem.ComponentName = _changeMaps.ComponentName\r\n    //     targetItem.CPCode = _changeMaps.ComponentName\r\n    //   } else if (_changeMaps.PartName) {\r\n    //     targetItem.PartName = _changeMaps.PartName\r\n    //     targetItem.CPCode = _changeMaps.PartName\r\n    //   } else {\r\n    //     const defaultRow = this.defaultTbData.find(item => item.uuid === targetItem.uuid)\r\n    //     console.log('defaultRow', JSON.parse(JSON.stringify(defaultRow)))\r\n    //     if (defaultRow) {\r\n    //       targetItem.SteelName = defaultRow.SteelName\r\n    //       targetItem.CPCode = defaultRow.CPCode\r\n    //     }\r\n    //   }\r\n    //   console.log('targetItem', JSON.parse(JSON.stringify(targetItem)))\r\n    //   const _list = this.findSimilarItems(targetItem)\r\n    //   if (_list.length) {\r\n    //     _list.forEach(item => {\r\n    //       item.SteelName = targetItem.SteelName\r\n    //       item.CPCode = targetItem.CPCode\r\n    //     })\r\n    //   }\r\n    // },\r\n    handleCancelChange() {\r\n      const selectedRecords = []\r\n      const getIds = (array) => {\r\n        if (!array || !array.length) return\r\n        array.forEach(item => {\r\n          selectedRecords.push(item.uuid)\r\n          if (item.children && item.children.length) {\r\n            getIds(item.children)\r\n          }\r\n        })\r\n      }\r\n      getIds(this.multipleSelection)\r\n      console.log('selectedRecords', selectedRecords)\r\n      selectedRecords.forEach(item => {\r\n        this.$store.dispatch('contactList/delChangeCode', item)\r\n      })\r\n      this.tbData = this.tbData.filter(item => !selectedRecords.includes(item.uuid))\r\n      this.defaultTbData = this.defaultTbData.filter(item => !selectedRecords.includes(item.uuid))\r\n      this.multipleSelection = []\r\n    },\r\n    multiSelectedChange(array) {\r\n      console.log('array', array)\r\n      console.log('array.records', this.$refs.tableRef.getCheckboxRecords(true))\r\n      const { records } = array\r\n      this.multipleSelection = array.records\r\n    },\r\n    handelFilePath() {\r\n      GetOssUrl({\r\n        url: this.filePath\r\n      }).then(res => {\r\n        window.open(res.Data)\r\n      })\r\n    },\r\n    getChangeStyle(changeName) {\r\n      const arr = changeName.split(',')\r\n      const rusult = ['cs-c-box']\r\n      if (arr.includes(changeType.isAdd)) {\r\n        rusult.push('cs-change-green')\r\n      } else if (arr.includes(changeType.isAdjust)) {\r\n        rusult.push('cs-change-yellow')\r\n      } else if (arr.includes(changeType.isDecrease) || arr.includes(changeType.isIncrease)) {\r\n        rusult.push('cs-change-blue')\r\n      } else if (arr.includes(changeType.isDelete)) {\r\n        rusult.push('cs-change-red')\r\n      } else {\r\n        rusult.push('cs-default')\r\n      }\r\n      return rusult\r\n    },\r\n    getCpCode(row) {\r\n      if (row.Type === 0) {\r\n        return row.SteelName\r\n      } else if (row.Type === 1) {\r\n        return row.ComponentName\r\n      } else {\r\n        return row.PartName\r\n      }\r\n    },\r\n    handleShow(row) {\r\n      const changeList = this.$store.state.contactList.changeCode[row.uuid]\r\n      this.changeRowContentList = changeList || []\r\n    },\r\n    filterNameMethod({ option, values, cellValue, row, column }) {\r\n      const result = this.filterCustom(row)\r\n      return result\r\n    },\r\n    filterCustom(row) {\r\n      const { component_name, component_search_mode, assembly_name,\r\n        assembly_search_mode, part_name, part_search_mode } = this.searchForm\r\n\r\n      const _ComponentName = this.nameMapping.ComponentName[row.ComponentName] || row.ComponentName || ''\r\n      const _SteelName = this.nameMapping.SteelName[row.SteelName] || row.SteelName || ''\r\n      const _PartName = this.nameMapping.PartName[row.PartName] || row.PartName || ''\r\n\r\n      let partMatch = true\r\n\r\n      if (part_name) {\r\n        if (part_search_mode === 1) {\r\n          partMatch = _PartName.includes(part_name)\r\n        } else {\r\n          partMatch = _PartName === part_name\r\n        }\r\n      }\r\n      let assemblyMatch = true\r\n      if (assembly_name) {\r\n        if (assembly_search_mode === 1) {\r\n          assemblyMatch = _ComponentName.includes(assembly_name)\r\n        } else {\r\n          assemblyMatch = _ComponentName === assembly_name\r\n        }\r\n      }\r\n      let componentMatch = true\r\n      if (component_name) {\r\n        if (component_search_mode === 1) {\r\n          componentMatch = _SteelName.includes(component_name)\r\n        } else {\r\n          componentMatch = _SteelName === component_name\r\n        }\r\n      }\r\n      // console.log(componentMatch, assemblyMatch, partMatch)\r\n\r\n      const result = componentMatch && assemblyMatch && partMatch\r\n      if (result) {\r\n        return true\r\n      } else {\r\n        return false\r\n      }\r\n    },\r\n    installChange(arr) {\r\n      if (!arr || !arr.length) {\r\n        this.clearTb()\r\n        return\r\n      }\r\n      this.tbData = this.tbData.filter(item => arr.some(id => id === item.InstallUnit_Id))\r\n      this.defaultTbData = this.defaultTbData.filter(item => arr.some(id => id === item.InstallUnit_Id))\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.cs-type {\r\n  padding: 2px;\r\n  font-weight: 400;\r\n  font-size: 12px;\r\n  color: #146EB4;\r\n  border-radius: 4px;\r\n  margin-right: 4px;\r\n  background-color: rgba(66, 107, 216, .1);\r\n}\r\n\r\n.cs-change {\r\n  padding: 2px 4px;\r\n  font-weight: 400;\r\n  font-size: 12px;\r\n  color: #298DFF;\r\n  border-radius: 4px;\r\n  background-color: rgba(41, 141, 255, .1)\r\n}\r\n\r\n.cs-c-box {\r\n  padding: 2px 4px;\r\n  font-weight: 400;\r\n  font-size: 12px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.cs-change-green {\r\n  color: #ffffff;\r\n  background-color: #3ECC93\r\n}\r\n\r\n.cs-change-green-p {\r\n  color: #3ECC93;\r\n  background-color: rgba(62, 204, 147, .1);\r\n}\r\n\r\n.cs-change-blue {\r\n  color: #ffffff;\r\n  background-color: #298DFF\r\n}\r\n\r\n.cs-change-blue-p {\r\n  color: #298DFF;\r\n  background-color: rgba(41, 141, 255, .1)\r\n}\r\n\r\n.cs-change-red {\r\n  color: #ffffff;\r\n  background-color: #FB6B7F\r\n}\r\n\r\n.cs-change-red-p {\r\n  color: #FB6B7F;\r\n  background-color: rgba(251, 107, 127, .1)\r\n}\r\n\r\n.cs-default {\r\n  color: #ffffff;\r\n  background-color: #8E95AA\r\n}\r\n\r\n.cs-default-p {\r\n  color: #8E95AA;\r\n  background-color: rgba(142, 149, 170, .1)\r\n}\r\n\r\n.cs-change-yellow {\r\n  color: #ffffff;\r\n  background-color:  #FB8F00;\r\n}\r\n.cs-green {\r\n  background: rgba(62, 204, 147, .1);\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  color: #3ECC93;\r\n  padding: 3px 10px;\r\n}\r\n\r\n.cs-yellow {\r\n  background: rgba(241, 180, 48, .1);\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  color: #F1B430;\r\n  padding: 3px 10px;\r\n}\r\n\r\n.cs-red {\r\n  background: rgba(251, 107, 127, .1);\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  color: #FB6B7F;\r\n  padding: 3px 10px;\r\n}\r\n\r\n.m-4 {\r\n  margin: 0 4px;\r\n}\r\n\r\n.cs-title {\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  color: rgba(34, 40, 52, 0.85);\r\n}\r\n\r\n.page-container {\r\n  margin: 16px;\r\n\r\n  .form-x {\r\n    background-color: #FFFFFF;\r\n    padding: 16px;\r\n  }\r\n\r\n  .cs-main {\r\n    margin-top: 16px;\r\n    background-color: #FFFFFF;\r\n    padding: 16px;\r\n  }\r\n}\r\n\r\n.cs-fee{\r\n  margin-top: 16px;\r\n  background-color: #FFFFFF;\r\n  padding: 16px 16px 0 16px;\r\n  .line-content{\r\n    align-items: center;\r\n    display: flex;\r\n  }\r\n  .cs-title{\r\n    margin-right: 8px;\r\n  }\r\n  .cs-label{\r\n    font-size: 14px;\r\n  }\r\n  .fw{\r\n    font-weight: bold;\r\n  }\r\n  .cs-blue{\r\n    color: #298DFF\r\n  }\r\n  .cs-red{\r\n    color: #FB6B7F\r\n  }\r\n  .fee-name{\r\n    font-weight: bold;\r\n    font-size: 14px;\r\n    color: rgba(34,40,52,0.85);\r\n    margin-right: 32px;\r\n  }\r\n  .fee-sub{\r\n    font-weight: bold;\r\n    font-size: 12px;\r\n    color: rgba(34,40,52,0.65);\r\n    margin-right: 32px;\r\n  }\r\n  .fee-num{\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    margin-right: 32px;\r\n  }\r\n  .fee-time{\r\n    font-weight: 400;\r\n    margin-right: 32px;\r\n    font-size: 12px;\r\n    color: rgba(34,40,52,0.65);\r\n  }\r\n  .fee-remark{\r\n    font-weight: bold;\r\n    font-size: 12px;\r\n    color: rgba(34,40,52,0.65);\r\n  }\r\n  .circle {\r\n    width: 14px;\r\n    height: 14px;\r\n    border-radius: 50%;\r\n    border: 4px solid #458CF7;\r\n    background-color: white;\r\n  }\r\n  ::v-deep{\r\n    .el-timeline-item__tail{\r\n      height: 37%;\r\n      margin: 18px 0;\r\n      width: 1px;\r\n      left: 5px;\r\n      border: 1px solid rgba(41, 141, 255, 0.3);\r\n    }\r\n  }\r\n}\r\n\r\n.el-divider--horizontal {\r\n  margin: 16px 0;\r\n}\r\n\r\nfooter {\r\n  margin-top: 16px;\r\n  text-align: right;\r\n}\r\n\r\n.cs-toolBar {\r\n  ::v-deep {\r\n    .vxe-button--icon.vxe-icon-custom-column{\r\n      display: none;\r\n    }\r\n\r\n    .vxe-button.type--button.is--circle {\r\n      width: 97px;\r\n      z-index: 0;\r\n      border-radius: 4px;\r\n    }\r\n\r\n    .el-form-item {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n.el-tree-select{\r\n  ::v-deep{\r\n    .el-select{\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n\r\n.change-method-form {\r\n  margin-bottom: 0px;\r\n}\r\n.cs-tree-table{\r\n ::v-deep{\r\n   .vxe-tree-cell{\r\n     text-align: left !important;\r\n   }\r\n   .vxe-checkbox--label{\r\n     color:#333333;\r\n   }\r\n   .col--checkbox{\r\n    .vxe-cell{\r\n      padding-left: 10px !important;\r\n    }\r\n   }\r\n }\r\n}\r\n\r\n.z-upload.hiddenBtn{\r\n  ::v-deep{\r\n    .el-upload{\r\n      display: none;\r\n    }\r\n    .el-upload-list__item:first-child{\r\n      margin-top: 4px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwdA,SAAAA,WAAA,EAAAC,wBAAA,EAAAC,kBAAA;AACA,SAAAC,oBAAA;AACA,SACAC,eAAA,EACAC,mBAAA,EACAC,eAAA,EACAC,YAAA,EACAC,cAAA,QACA;AACA,SAAAC,oBAAA,EAAAC,SAAA;AACA,OAAAC,UAAA;AACA,OAAAC,SAAA;AACA,SAAAC,YAAA,EAAAC,UAAA,EAAAC,QAAA,EAAAC,SAAA;AACA,OAAAC,YAAA;AACA,OAAAC,OAAA;AACA,SAAAC,mBAAA;AACA,OAAAC,UAAA;AACA,OAAAC,SAAA;AACA,SAAAC,UAAA,EAAAC,iBAAA,EAAAC,iBAAA;AACA,SAAAC,kBAAA;AACA,SAAAC,OAAA;AACA,OAAAC,qBAAA;AACA,SAAAC,aAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAlB,SAAA,EAAAA,SAAA;IACAK,YAAA,EAAAA,YAAA;IACAN,UAAA,EAAAA,UAAA;IACAS,UAAA,EAAAA,UAAA;IACAC,SAAA,EAAAA;EACA;EACAU,MAAA,GAAAJ,qBAAA;EACAK,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;QACAC,GAAA;QACAC,MAAA;QACAC,GAAA;QACAC,QAAA;QACAC,QAAA;QACAC,QAAA;MACA;MAEAC,aAAA;MACAC,KAAA;MACAC,KAAA;MACAC,gBAAA;MACAC,QAAA;MACAC,SAAA;MACAC,WAAA;MACAC,WAAA;MACAC,aAAA;MACAC,aAAA;MACAC,SAAA;MACAC,MAAA;MACAC,UAAA;MACAC,QAAA;MACAC,iBAAA;MACAC,oBAAA;MACAC,iBAAA;QAAAxB,IAAA;MAAA;MACAyB,OAAA;MACAC,YAAA;MACAC,UAAA;QACAC,cAAA;QACAC,SAAA;QACAC,aAAA;QACAC,qBAAA;QACAC,gBAAA;QACAC,oBAAA;QACAC,OAAA;MACA;MACAC,IAAA;QACAC,cAAA;QACAC,OAAA;QACAC,eAAA;QACAC,aAAA;QACAC,WAAA;QACAC,GAAA,EAAAC,SAAA;QACAC,KAAA,EAAAD,SAAA;QACAE,OAAA;QACAC,WAAA;QACAC,YAAA;QACAC,MAAA;QACAC,cAAA;MACA;MACAC,UAAA;MACAC,UAAA;QACAlD,IAAA;QACAmD,UAAA;QACAC,WAAA;QACAC,KAAA;UACAC,QAAA;UACAC,QAAA;UACAC,KAAA;UACAC,KAAA;QACA;MACA;MACAC,UAAA;MACAC,WAAA;MACAC,cAAA;MACAC,eAAA;MACAC,cAAA;QACA;QACAX,UAAA;QACAC,WAAA;QACApD,IAAA;QACAqD,KAAA;UACAC,QAAA;UACAC,QAAA;UACAC,KAAA;UACAC,KAAA;QACA;MACA;MACAM,KAAA;QACA3B,cAAA,GACA;UAAA4B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA7B,OAAA,GACA;UAAA2B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA3B,aAAA,GACA;UAAAyB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA1B,WAAA,GACA;UAAAwB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA5B,eAAA,GACA;UAAA0B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,cAAA,GACA;QAAAC,IAAA;QAAAvE,IAAA;MAAA,GACA;QAAAuE,IAAA;QAAAvE,IAAA;MAAA,GACA;QAAAuE,IAAA;QAAAvE,IAAA;MAAA;IAEA;EACA;EACAwE,QAAA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAAtD,aAAA,SAAAC,aAAA;IACA;IACAsD,MAAA,WAAAA,OAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,YAAAH,MAAA,CAAAC,KAAA,CAAAC,IAAA;IACA;IACAE,UAAA,WAAAA,WAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,IAAA,QAAAlB,cAAA,CAAAmB,IAAA,WAAAC,IAAA;QACA,OAAAA,IAAA,CAAAC,EAAA,KAAAJ,KAAA,CAAA1C,IAAA,CAAAK,WAAA;MACA;MACA,QAAAsC,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAAI,gBAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,OAAA1F,kBAAA,MAAAmB,QAAA;IACA;EACA;EACAwE,KAAA;IACA;MACAC,OAAA,WAAAA,QAAAC,MAAA;QACA,KAAAA,MAAA;UAAA,IAAAC,WAAA;UACA,KAAAxB,KAAA,CAAAzB,eAAA,IAAA0B,QAAA;UACA,CAAAuB,WAAA,QAAAC,KAAA,cAAAD,WAAA,gBAAAA,WAAA,GAAAA,WAAA,CAAAE,cAAA,cAAAF,WAAA,eAAAA,WAAA,CAAAG,aAAA;QACA;MACA;MACAC,SAAA;IACA;IACA;MACAN,OAAA,WAAAA,QAAAC,MAAA;QACA,UAAAjD,OAAA;QACA,SAAAwB,eAAA,CAAA+B,MAAA;UACA,KAAA7B,KAAA,CAAAzB,eAAA,IAAA0B,QAAA;UACA,KAAAwB,KAAA,CAAAC,cAAA,CAAAC,aAAA;QACA;UACA,KAAA3B,KAAA,CAAAzB,eAAA,IAAA0B,QAAA;UACA,KAAAwB,KAAA,CAAAC,cAAA,CAAAC,aAAA;QACA;MACA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAAC,EAAA;MAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACA;YACAV,MAAA,CAAAW,MAAA,CAAAC,QAAA;YACAZ,MAAA,CAAAa,cAAA;YACAb,MAAA,CAAAc,gBAAA;YAAAN,QAAA,CAAAE,IAAA;YAAA,OACAV,MAAA,CAAAe,UAAA;UAAA;YACAf,MAAA,CAAAgB,wBAAA;YAEAX,EAAA,GAAAL,MAAA,CAAAtB,MAAA,CAAAC,KAAA,CAAA0B,EAAA;YAAAG,QAAA,CAAAS,EAAA,GACAZ,EAAA;YAAA,KAAAG,QAAA,CAAAS,EAAA;cAAAT,QAAA,CAAAE,IAAA;cAAA;YAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OAAAV,MAAA,CAAAkB,OAAA,CAAAb,EAAA;UAAA;YACAL,MAAA,CAAAmB,cAAA;YACA;UAAA;UAAA;YAAA,OAAAX,QAAA,CAAAY,IAAA;QAAA;MAAA,GAAAhB,OAAA;IAAA;EACA;EACAiB,OAAA;IACAF,cAAA,WAAAA,eAAA;MAAA,IAAAG,MAAA;MAAA,OAAArB,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAoB,SAAA;QAAA,IAAAC,GAAA,EAAAC,eAAA,EAAAC,UAAA,EAAAC,cAAA,EAAAC,QAAA,EAAAC,UAAA;QAAA,OAAA3B,mBAAA,GAAAI,IAAA,UAAAwB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtB,IAAA,GAAAsB,SAAA,CAAArB,IAAA;YAAA;cAAAqB,SAAA,CAAArB,IAAA;cAAA,OACArH,mBAAA;gBAAA2I,gBAAA;cAAA;YAAA;cAAAR,GAAA,GAAAO,SAAA,CAAAE,IAAA;cACA,IAAAT,GAAA,CAAAU,SAAA;gBACAZ,MAAA,CAAAa,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAd,GAAA,CAAAe,IAAA;gBACA;gBACAd,eAAA,GAAAD,GAAA,CAAAe,IAAA,CAAAC,MAAA,WAAAtD,IAAA;kBAAA,OACA,4CAAAuD,QAAA,CAAAvD,IAAA,CAAAwD,IAAA;gBAAA,CACA,GAEA;gBACAhB,UAAA;kBACAgB,IAAA;kBACAC,YAAA;kBACAC,SAAA;kBACAC,eAAA;kBACAC,KAAA;kBACAC,KAAA;gBACA,GAEA;gBACAtB,eAAA,CAAAuB,OAAA,CAAAtB,UAAA;gBACAC,cAAA;kBACAe,IAAA;kBACAC,YAAA;kBACAI,KAAA;kBACAH,SAAA;kBACAC,eAAA;kBACAC,KAAA;gBACA;kBACAJ,IAAA;kBACAC,YAAA;kBACAI,KAAA;kBACAH,SAAA;kBACAC,eAAA;kBACAC,KAAA;gBACA;gBAEArB,eAAA,CAAAwB,IAAA,CAAAC,KAAA,CAAAzB,eAAA,EAAAE,cAAA;gBACAC,QAAA;gBAEAN,MAAA,CAAA6B,WAAA,GAAAjK,SAAA,CAAAuI,eAAA,CAAA2B,GAAA,WAAAlE,IAAA;kBAAA,IAAAmE,kBAAA;kBACA,IAAAC,iBAAA,KAAAD,kBAAA,GAAAnE,IAAA,CAAAyD,YAAA,cAAAU,kBAAA,uBAAAA,kBAAA,CAAAvD,MAAA;kBACA,IAAAlF,KAAA,GAAA2I,IAAA,CAAAC,GAAA,YAAAD,IAAA,CAAAC,GAAA,IAAAF,iBAAA;kBACA,OAAAG,aAAA,CAAAA,aAAA,KACAvE,IAAA;oBACA4D,KAAA,EAAAlI,KAAA;oBACAmI,KAAA;kBAAA;gBAEA;gBAEA,IAAAzB,MAAA,CAAA1F,YAAA;kBACAiG,UAAA;kBACAD,QAAA,GAAAN,MAAA,CAAA6B,WAAA,CAAAX,MAAA,WAAAtD,IAAA;oBAAA,OAAA2C,UAAA,CAAAY,QAAA,CAAAvD,IAAA,CAAAwD,IAAA;kBAAA;gBACA;kBACAd,QAAA,GAAAN,MAAA,CAAA6B,WAAA;gBACA;gBACA7B,MAAA,CAAA3F,OAAA,GAAAiG,QAAA;cACA;YAAA;YAAA;cAAA,OAAAG,SAAA,CAAAX,IAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IACA;IACAL,OAAA,WAAAA,QAAAb,EAAA;MAAA,IAAAqD,MAAA;MAAA,OAAAzD,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAwD,SAAA;QAAA,OAAAzD,mBAAA,GAAAI,IAAA,UAAAsD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApD,IAAA,GAAAoD,SAAA,CAAAnD,IAAA;YAAA;cACAgD,MAAA,CAAA1I,WAAA;cAAA6I,SAAA,CAAAnD,IAAA;cAAA,OACApI,eAAA;gBACA6G,EAAA,EAAAkB;cACA,GAAAyD,IAAA;gBAAA,IAAAC,IAAA,GAAA9D,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6D,SAAAxC,GAAA;kBAAA,IAAAyC,SAAA,EAAAC,eAAA,EAAA5H,cAAA,EAAAC,OAAA,EAAAC,eAAA,EAAAC,aAAA,EAAAC,WAAA,EAAAyH,WAAA,EAAAxH,GAAA,EAAAE,KAAA,EAAAC,OAAA,EAAAC,WAAA,EAAAC,YAAA,EAAAC,MAAA,EAAAmH,UAAA,EAAAjF,EAAA,EAAAkF,MAAA,EAAAnH,cAAA,EAAAoH,WAAA,EAAAC,iBAAA,EAAAC,kBAAA,EAAAC,IAAA,EAAAC,GAAA;kBAAA,OAAAxE,mBAAA,GAAAI,IAAA,UAAAqE,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAAnE,IAAA,GAAAmE,SAAA,CAAAlE,IAAA;sBAAA;wBAAA,KACAc,GAAA,CAAAU,SAAA;0BAAA0C,SAAA,CAAAlE,IAAA;0BAAA;wBAAA;wBAAAuD,SAAA,GAoBAzC,GAAA,CAAAe,IAAA,EAlBA2B,eAAA,GAAAD,SAAA,CAAAC,eAAA,EACA5H,cAAA,GAAA2H,SAAA,CAAA3H,cAAA,EACAC,OAAA,GAAA0H,SAAA,CAAA1H,OAAA,EACAC,eAAA,GAAAyH,SAAA,CAAAzH,eAAA,EACAC,aAAA,GAAAwH,SAAA,CAAAxH,aAAA,EACAC,WAAA,GAAAuH,SAAA,CAAAvH,WAAA,EACAyH,WAAA,GAAAF,SAAA,CAAAE,WAAA,EACAxH,GAAA,GAAAsH,SAAA,CAAAtH,GAAA,EACAE,KAAA,GAAAoH,SAAA,CAAApH,KAAA,EACAC,OAAA,GAAAmH,SAAA,CAAAnH,OAAA,EACAC,WAAA,GAAAkH,SAAA,CAAAlH,WAAA,EACAC,YAAA,GAAAiH,SAAA,CAAAjH,YAAA,EACAC,MAAA,GAAAgH,SAAA,CAAAhH,MAAA,EACAmH,UAAA,GAAAH,SAAA,CAAAG,UAAA,EACAjF,EAAA,GAAA8E,SAAA,CAAA9E,EAAA,EACAkF,MAAA,GAAAJ,SAAA,CAAAI,MAAA,EACAnH,cAAA,GAAA+G,SAAA,CAAA/G,cAAA,EACAoH,WAAA,GAAAL,SAAA,CAAAK,WAAA;wBAEAZ,MAAA,CAAApI,UAAA,GAAA8I,UAAA;wBACA,IAAAA,UAAA,CAAAtE,MAAA;0BAAAyE,iBAAA,GACAH,UAAA,CAAAS,KAAA,MAAAL,kBAAA,GAAAM,cAAA,CAAAP,iBAAA,MAAAE,IAAA,GAAAD,kBAAA;0BACAd,MAAA,CAAA3I,SAAA,GAAA3B,OAAA,EAAAqL,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAA9H,GAAA,QAAAoI,MAAA;wBACA;wBACA,IAAAV,MAAA;0BACAK,GAAA,GAAAhB,MAAA,CAAA/H,OAAA,CAAAqJ,SAAA,WAAA9F,IAAA;4BAAA,OAAAA,IAAA,CAAAwD,IAAA;0BAAA;0BACA,IAAAgC,GAAA;4BACAhB,MAAA,CAAA/H,OAAA,CAAAsJ,MAAA,CAAAP,GAAA;0BACA;wBACA;wBAEA,IAAAxH,cAAA,aAAAA,cAAA,eAAAA,cAAA,CAAA4C,MAAA;0BACA5C,cAAA,CAAAgI,OAAA,WAAAC,OAAA,EAAAT,GAAA;4BACA,IAAAU,GAAA;8BACArL,IAAA,EAAAoL,OAAA,CAAAE,SAAA;8BACAC,GAAA,EAAAH,OAAA,CAAAI;4BACA;4BACA7B,MAAA,CAAAnI,QAAA,CAAA0H,IAAA,CAAAmC,GAAA;4BACA1B,MAAA,CAAArH,IAAA,CAAAa,cAAA,CAAA+F,IAAA;8BACAsC,QAAA,EAAAJ,OAAA,CAAAI,QAAA;8BACAF,SAAA,EAAAF,OAAA,CAAAE;4BACA;0BACA;wBACA;wBAAAT,SAAA,CAAAlE,IAAA;wBAAA,OACAgD,MAAA,CAAA8B,WAAA,CAAAlJ,cAAA;sBAAA;wBAAAsI,SAAA,CAAAlE,IAAA;wBAAA,OACAgD,MAAA,CAAA+B,sBAAA,CAAAlJ,OAAA;sBAAA;wBAEAmJ,MAAA,CAAAC,MAAA,CAAAjC,MAAA,CAAArH,IAAA,EAAAoH,aAAA,CAAAA,aAAA,KACAjC,GAAA,CAAAe,IAAA;0BACAjG,cAAA,EAAAA,cAAA;0BACAC,OAAA,EAAAA,OAAA;0BACA2H,eAAA,EAAAA,eAAA;0BACA/E,EAAA,EAAAA,EAAA;0BACA3C,eAAA,EAAAA,eAAA,UAAAA,eAAA,gBAAAA,eAAA,CAAAoJ,KAAA,QAAApJ,eAAA;0BACAC,aAAA,EAAAA,aAAA;0BACAC,WAAA,EAAAA,WAAA;0BACAC,GAAA,EAAAA,GAAA,IAAAC,SAAA;0BACAiJ,IAAA,EAAAhJ,KAAA,IAAAD,SAAA;0BACAE,OAAA,EAAAgJ,MAAA,CAAAhJ,OAAA;0BACAC,WAAA,EAAAA,WAAA;0BACAC,YAAA,EAAAA,YAAA;0BACAC,MAAA,EAAAA;wBAAA,EACA;wBAEAyG,MAAA,CAAAqC,SAAA,CAAAzB,WAAA;wBACAZ,MAAA,CAAA5I,QAAA,GAAAoJ,eAAA;wBACAR,MAAA,CAAA9H,YAAA,GAAAuI,WAAA,aAAAA,WAAA;wBACA;wBACA;wBACA;wBACA;wBAAAS,SAAA,CAAAlE,IAAA;wBAAA;sBAAA;wBAEAgD,MAAA,CAAAsC,QAAA;0BACA7H,OAAA,EAAAqD,GAAA,CAAAyE,OAAA;0BACArH,IAAA;wBACA;sBAAA;wBAEA8E,MAAA,CAAA1I,WAAA;sBAAA;sBAAA;wBAAA,OAAA4J,SAAA,CAAAxD,IAAA;oBAAA;kBAAA,GAAA4C,QAAA;gBAAA,CACA;gBAAA,iBAAAkC,EAAA;kBAAA,OAAAnC,IAAA,CAAAb,KAAA,OAAAiD,SAAA;gBAAA;cAAA;YAAA;YAAA;cAAA,OAAAtC,SAAA,CAAAzC,IAAA;UAAA;QAAA,GAAAuC,QAAA;MAAA;IACA;IACAyC,aAAA,WAAAA,cAAA;MACA,KAAA/K,MAAA;MACA,KAAAgL,WAAA;IACA;IAEAC,YAAA,WAAAA,aAAAC,KAAA,EAAAhL,QAAA;MACA,KAAAyK,QAAA,CAAAQ,OAAA,kGAAAC,MAAA,CAAAF,KAAA,CAAAzG,MAAA,wDAAA2G,MAAA,CAAAF,KAAA,CAAAzG,MAAA,GAAAvE,QAAA,CAAAuE,MAAA;IACA;IACA4G,cAAA,WAAAA,eAAAC,KAAA,EAAAJ,KAAA,EAAAhL,QAAA,GACA;IACAqL,WAAA,WAAAA,YAAAC,GAAA,EAAAN,KAAA,EAAAhL,QAAA;MACAuL,OAAA,CAAAC,GAAA,SAAAF,GAAA,EAAAN,KAAA,EAAAhL,QAAA;MACA,KAAAyL,cAAA,CAAAzL,QAAA;IACA;IACAyL,cAAA,WAAAA,eAAAzL,QAAA;MACA,IAAA0L,IAAA,GAAA1L,QAAA,CAAA2L,KAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,MAAA;MAAA;MACAH,IAAA,UAAA9L,aAAA;IACA;IACAkM,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAA5H,KAAA,SAAA6H,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAV,OAAA,CAAAC,GAAA,UAAAS,KAAA;UACAF,MAAA,CAAA5H,KAAA,WAAA+H,UAAA;QACA;UACAX,OAAA,CAAAC,GAAA;UACA;QACA;MACA;IACA;IACAW,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,KAAAjI,KAAA,SAAA6H,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAG,MAAA,CAAAhN,KAAA;UACAgN,MAAA,CAAA/M,KAAA;UACA+M,MAAA,CAAA9M,gBAAA;UACA8M,MAAA,CAAAjN,aAAA;UACAiN,MAAA,CAAAC,SAAA,WAAAC,CAAA;YACAF,MAAA,CAAAjI,KAAA,YAAA+H,UAAA,CAAAE,MAAA,CAAAtL,IAAA,EAAAsL,MAAA,CAAAtM,MAAA;UACA;QACA;UACAyL,OAAA,CAAAC,GAAA;UACA;QACA;MACA;IACA;IACAU,UAAA,WAAAA,WAAAK,GAAA;MACA,KAAApI,KAAA,iBAAA+H,UAAA,CAAAK,GAAA;IACA;IACAjH,cAAA,WAAAA,eAAA;MAAA,IAAAkH,MAAA;MACA3P,kBAAA;QAAA4P,QAAA;MAAA,GAAAlE,IAAA,WAAAtC,GAAA;QACA,IAAAA,GAAA,CAAAU,SAAA;UACA6F,MAAA,CAAAlK,WAAA,GAAA2D,GAAA,CAAAe,IAAA,CAAAA,IAAA;QACA;MACA;IACA;IACA0F,eAAA,WAAAA,gBAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,KAAAD,IAAA;MACAA,IAAA,CAAAhD,OAAA,WAAAC,OAAA;QACA,IAAAiD,QAAA,GAAAjD,OAAA,CAAAiD,QAAA;QACA,IAAAA,QAAA,IAAAA,QAAA,CAAAtI,MAAA;UACAqF,OAAA,CAAA3H,QAAA;QACA;UACA2H,OAAA,CAAA3H,QAAA;UACA2K,MAAA,CAAAF,eAAA,CAAAG,QAAA;QACA;MACA;IACA;IACA5C,WAAA,WAAAA,YAAA6C,GAAA;MAAA,IAAAC,MAAA;MAAA,OAAArI,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAoI,SAAA;QAAA,OAAArI,mBAAA,GAAAI,IAAA,UAAAkI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhI,IAAA,GAAAgI,SAAA,CAAA/H,IAAA;YAAA;cAAA+H,SAAA,CAAA/H,IAAA;cAAA,OACAxI,WAAA;gBACAwQ,YAAA,EAAAL;cACA,GAAAvE,IAAA,WAAAtC,GAAA;gBACA,IAAAA,GAAA,CAAAU,SAAA;kBACA,IAAAyG,IAAA,GAAAnH,GAAA,CAAAe,IAAA;kBACA+F,MAAA,CAAAL,eAAA,CAAAU,IAAA;kBACAL,MAAA,CAAAtK,cAAA,CAAA9D,IAAA,GAAAsH,GAAA,CAAAe,IAAA;kBACA+F,MAAA,CAAAV,SAAA,WAAAC,CAAA;oBACAS,MAAA,CAAA5I,KAAA,CAAAkJ,cAAA,CAAAC,iBAAA,CAAArH,GAAA,CAAAe,IAAA;kBACA;gBACA;kBACA+F,MAAA,CAAAtC,QAAA;oBACA7H,OAAA,EAAAqD,GAAA,CAAAyE,OAAA;oBACArH,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA6J,SAAA,CAAArH,IAAA;UAAA;QAAA,GAAAmH,QAAA;MAAA;IACA;IACA9C,sBAAA,WAAAA,uBAAAqD,MAAA;MAAA,IAAAC,MAAA;MAAA,OAAA9I,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6I,SAAA;QAAA,OAAA9I,mBAAA,GAAAI,IAAA,UAAA2I,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzI,IAAA,GAAAyI,SAAA,CAAAxI,IAAA;YAAA;cAAAwI,SAAA,CAAAxI,IAAA;cAAA,OACAvI,wBAAA;gBACAoE,OAAA,EAAAuM,MAAA;gBACAK,IAAA;gBACAnB,QAAA;cACA,GAAAlE,IAAA,WAAAtC,GAAA;gBACA,IAAAA,GAAA,CAAAU,SAAA;kBACA6G,MAAA,CAAAhL,eAAA,GAAAyD,GAAA,CAAAe,IAAA;kBACA,IAAAwG,MAAA,CAAAhL,eAAA,CAAA+B,MAAA;oBACAiJ,MAAA,CAAA9K,KAAA,CAAAzB,eAAA,IAAA0B,QAAA;kBACA;oBACA6K,MAAA,CAAA9K,KAAA,CAAAzB,eAAA,IAAA0B,QAAA;kBACA;gBACA;kBACA6K,MAAA,CAAA/C,QAAA;oBACA7H,OAAA,EAAAqD,GAAA,CAAAyE,OAAA;oBACArH,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAsK,SAAA,CAAA9H,IAAA;UAAA;QAAA,GAAA4H,QAAA;MAAA;IACA;IACAI,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MAAA,OAAApJ,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAmJ,SAAA;QAAA,IAAAhN,cAAA;QAAA,OAAA4D,mBAAA,GAAAI,IAAA,UAAAiJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/I,IAAA,GAAA+I,SAAA,CAAA9I,IAAA;YAAA;cACApE,cAAA,GAAA+M,MAAA,CAAAhN,IAAA,CAAAC,cAAA;cACA+M,MAAA,CAAAI,OAAA;cACAJ,MAAA,CAAAhN,IAAA,CAAAE,OAAA;cACA8M,MAAA,CAAAhN,IAAA,CAAAG,eAAA;cACA6M,MAAA,CAAArL,cAAA,CAAA9D,IAAA;cACAmP,MAAA,CAAAzB,SAAA,WAAAC,CAAA;gBACAwB,MAAA,CAAA3J,KAAA,CAAAkJ,cAAA,CAAAC,iBAAA;cACA;cAAA,KACAvM,cAAA;gBAAAkN,SAAA,CAAA9I,IAAA;gBAAA;cAAA;cAAA8I,SAAA,CAAA9I,IAAA;cAAA,OACA2I,MAAA,CAAA7D,WAAA,CAAAlJ,cAAA;YAAA;YAAA;cAAA,OAAAkN,SAAA,CAAApI,IAAA;UAAA;QAAA,GAAAkI,QAAA;MAAA;IAEA;IACAI,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MAAA,OAAA1J,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAyJ,SAAA;QAAA,OAAA1J,mBAAA,GAAAI,IAAA,UAAAuJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArJ,IAAA,GAAAqJ,SAAA,CAAApJ,IAAA;YAAA;cACAiJ,OAAA,CAAAF,OAAA;cAAAK,SAAA,CAAApJ,IAAA;cAAA,OACAiJ,OAAA,CAAAlE,sBAAA,CAAAkE,OAAA,CAAAtN,IAAA,CAAAE,OAAA;YAAA;cACA,IAAAoN,OAAA,CAAA5L,eAAA,CAAA+B,MAAA,IAAA6J,OAAA,CAAAtN,IAAA,CAAAE,OAAA;gBACAoN,OAAA,CAAAtN,IAAA,CAAAG,eAAA,IAAAmN,OAAA,CAAA5L,eAAA,IAAAoB,EAAA;gBACAwK,OAAA,CAAA1L,KAAA,CAAAzB,eAAA,IAAA0B,QAAA;cACA;gBACAyL,OAAA,CAAA1L,KAAA,CAAAzB,eAAA,IAAA0B,QAAA;gBACAyL,OAAA,CAAAjK,KAAA,CAAAC,cAAA,CAAAC,aAAA;cACA;YAAA;YAAA;cAAA,OAAAkK,SAAA,CAAA1I,IAAA;UAAA;QAAA,GAAAwI,QAAA;MAAA;IACA;IACAG,SAAA,WAAAA,UAAA;MACA,KAAA1N,IAAA,CAAAE,OAAA;MACA,KAAAF,IAAA,CAAAG,eAAA;MACA,KAAAiN,OAAA;IACA;IACAO,WAAA,WAAAA,YAAA;MACA,KAAAtP,aAAA;IACA;IACAoG,gBAAA,WAAAA,iBAAA;MAAA,IAAAmJ,OAAA;MACA5R,oBAAA,KAAAyL,IAAA,WAAAtC,GAAA;QACA,IAAAA,GAAA,CAAAU,SAAA;UACA+H,OAAA,CAAArM,UAAA,GAAA8H,MAAA,CAAAwE,MAAA,CAAA1I,GAAA,CAAAe,IAAA;UAEA,IAAA4H,KAAA,GAAAC,YAAA,CAAAC,OAAA;UACA,IAAAC,GAAA,GAAAL,OAAA,CAAArM,UAAA,CAAAqB,IAAA,WAAAkI,CAAA;YAAA,OAAAA,CAAA,CAAAhI,EAAA,KAAAgL,KAAA;UAAA;UACA,IAAAG,GAAA;YACAL,OAAA,CAAA5N,IAAA,CAAAI,aAAA,GAAA6N,GAAA,CAAAnL,EAAA;UACA;QACA;UACA8K,OAAA,CAAAjE,QAAA;YACA7H,OAAA,EAAAqD,GAAA,CAAAyE,OAAA;YACArH,IAAA;UACA;QACA;MACA;IACA;IACAoC,wBAAA,WAAAA,yBAAA;MAAA,IAAAuJ,OAAA;MACAhS,mBAAA,KAAAuL,IAAA,WAAAtC,GAAA;QACA,IAAAA,GAAA,CAAAU,SAAA;UACAqI,OAAA,CAAAzM,cAAA,GAAA0D,GAAA,CAAAe,IAAA;QACA;UACAgI,OAAA,CAAAvE,QAAA;YACA7H,OAAA,EAAAqD,GAAA,CAAAyE,OAAA;YACArH,IAAA;UACA;QACA;MACA;IACA;IACAmC,UAAA,WAAAA,WAAA;MAAA,IAAAyJ,OAAA;MAAA,OAAAvK,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAsK,SAAA;QAAA,IAAAC,gBAAA,EAAAC,OAAA,EAAAC,KAAA;QAAA,OAAA1K,mBAAA,GAAAI,IAAA,UAAAuK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArK,IAAA,GAAAqK,SAAA,CAAApK,IAAA;YAAA;cACAgK,gBAAA;gBAAA,IAAAK,KAAA,GAAA9K,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6K,SAAA;kBAAA,OAAA9K,mBAAA,GAAAI,IAAA,UAAA2K,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAAzK,IAAA,GAAAyK,SAAA,CAAAxK,IAAA;sBAAA;wBAAAwK,SAAA,CAAAxK,IAAA;wBAAA,OACA5G,aAAA,KAAAgK,IAAA,WAAAtC,GAAA;0BACA,IAAAA,GAAA,CAAAU,SAAA;4BAAA,IAAAiJ,UAAA;4BACA,OAAA3J,GAAA,aAAAA,GAAA,gBAAA2J,UAAA,GAAA3J,GAAA,CAAAe,IAAA,iBAAA4I,UAAA,uBAAAA,UAAA,CAAAC,OAAA;0BACA;4BACAZ,OAAA,CAAAxE,QAAA;8BACA7H,OAAA,EAAAqD,GAAA,CAAAyE,OAAA;8BACArH,IAAA;4BACA;0BACA;wBACA;sBAAA;wBAAA,OAAAsM,SAAA,CAAAG,MAAA,WAAAH,SAAA,CAAAjJ,IAAA;sBAAA;sBAAA;wBAAA,OAAAiJ,SAAA,CAAA9J,IAAA;oBAAA;kBAAA,GAAA4J,QAAA;gBAAA,CACA;gBAAA,gBAXAN,iBAAA;kBAAA,OAAAK,KAAA,CAAA7H,KAAA,OAAAiD,SAAA;gBAAA;cAAA;cAaAwE,OAAA;gBAAA,IAAAW,KAAA,GAAArL,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAoL,SAAAX,KAAA;kBAAA,OAAA1K,mBAAA,GAAAI,IAAA,UAAAkL,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAAhL,IAAA,GAAAgL,SAAA,CAAA/K,IAAA;sBAAA;wBAAA+K,SAAA,CAAA/K,IAAA;wBAAA,OACA/H,oBAAA,KAAAmL,IAAA,WAAAtC,GAAA;0BACA,IAAAA,GAAA,CAAAU,SAAA;4BAAA,IAAAwJ,UAAA;4BACA,IAAAC,MAAA,IAAAD,UAAA,GAAAlK,GAAA,CAAAe,IAAA,cAAAmJ,UAAA,uBAAAA,UAAA;4BACA,IAAAC,MAAA,CAAAvD,QAAA,CAAAtI,MAAA;8BACA,IAAA6I,IAAA,GAAAgD,MAAA,CAAAvD,QAAA,CAAA5F,MAAA,WAAA2E,CAAA;gCAAA,OAAAA,CAAA,CAAAhI,EAAA,KAAAyL,KAAA;8BAAA;8BAEA,IAAAgB,iBAAA,YAAAA,iBAAAC,SAAA;gCACAA,SAAA,CAAAzI,GAAA,WAAA+B,OAAA;kCACA,IAAAA,OAAA,CAAAiD,QAAA,IAAAjD,OAAA,CAAAiD,QAAA,CAAAtI,MAAA;oCACAqF,OAAA,CAAA3H,QAAA;oCACAoO,iBAAA,CAAAzG,OAAA,CAAAiD,QAAA;kCACA;gCACA;8BACA;8BACAwD,iBAAA,CAAAjD,IAAA;8BACA6B,OAAA,CAAA9K,KAAA,CAAAoM,UAAA,CAAAjD,iBAAA,CAAAF,IAAA;4BACA;0BACA;4BACA6B,OAAA,CAAAxE,QAAA;8BACA7H,OAAA,EAAAqD,GAAA,CAAAyE,OAAA;8BACArH,IAAA;4BACA;0BACA;wBACA;sBAAA;sBAAA;wBAAA,OAAA6M,SAAA,CAAArK,IAAA;oBAAA;kBAAA,GAAAmK,QAAA;gBAAA,CACA;gBAAA,gBAzBAZ,QAAAoB,GAAA;kBAAA,OAAAT,KAAA,CAAApI,KAAA,OAAAiD,SAAA;gBAAA;cAAA;cAAA2E,SAAA,CAAApK,IAAA;cAAA,OA0BAgK,gBAAA;YAAA;cAAAE,KAAA,GAAAE,SAAA,CAAA7I,IAAA;cAAA6I,SAAA,CAAApK,IAAA;cAAA,OACAiK,OAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAAE,SAAA,CAAA1J,IAAA;UAAA;QAAA,GAAAqJ,QAAA;MAAA;IACA;IACAuB,YAAA,WAAAA,aAAAC,OAAA;MAAA,IAAAC,OAAA;MACA,KAAA9Q,SAAA;MACA,IAAAiB,IAAA,GAAAoH,aAAA,UAAApH,IAAA;MACA7D,eAAA,CAAAiL,aAAA,CAAAA,aAAA,KACApH,IAAA;QACA8P,UAAA,OAAAvQ,YAAA;QACAY,eAAA,OAAAH,IAAA,CAAAG,eAAA,CAAA4P,QAAA;QACAlP,cAAA,GAAA+O,OAAA;MAAA,EACA,EAAAnI,IAAA,WAAAtC,GAAA;QACA,IAAAA,GAAA,CAAAU,SAAA;UACAgK,OAAA,CAAAG,YAAA;UACA,IAAAC,UAAA,GAAA9K,GAAA,CAAAe,IAAA;YAAArF,cAAA,GAAAoP,UAAA,CAAApP,cAAA;YAAAqP,kBAAA,GAAAD,UAAA,CAAAC,kBAAA;UACA;UACA;UACA,IAAArP,cAAA,CAAA4C,MAAA;YACAoM,OAAA,CAAApR,QAAA,GAAAoC,cAAA,IAAAqI,QAAA;YACA2G,OAAA,CAAA7P,IAAA,CAAA6H,eAAA,GAAAgI,OAAA,CAAApR,QAAA;YACAoR,OAAA,CAAA7P,IAAA,CAAAmQ,oBAAA,IAAAP,OAAA;UACA;UACAC,OAAA,CAAAnG,SAAA,CAAAwG,kBAAA;QACA;UACA,IAAA/K,GAAA,CAAAe,IAAA,IAAAf,GAAA,CAAAe,IAAA,CAAAkK,YAAA;YACAC,MAAA,CAAAC,IAAA,CAAA3T,UAAA,CAAAkT,OAAA,CAAAU,QAAA,EAAApL,GAAA,CAAAe,IAAA,CAAAkK,YAAA;UACA;UACAP,OAAA,CAAAlG,QAAA;YACA7H,OAAA,EAAAqD,GAAA,CAAAyE,OAAA;YACArH,IAAA;UACA;QACA;QACAsN,OAAA,CAAA9Q,SAAA;MACA;IACA;IACAyR,YAAA,WAAAA,aAAA/E,GAAA;MACA;QACAgF,cAAA,EAAA1T,OAAA,CAAA0O,GAAA,CAAAiF,WAAA,EAAAC,QAAA,CAAAlF,GAAA,CAAAmF,WAAA,EAAAlI,MAAA;MACA;IACA;IACAgB,SAAA,WAAAA,UAAAmH,IAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,OAAA,YAAAA,QAAAF,IAAA,EAAAhO,IAAA;QAAA,IAAAmO,YAAA,GAAAlH,SAAA,CAAArG,MAAA,QAAAqG,SAAA,QAAAvJ,SAAA,GAAAuJ,SAAA;QACA,IAAAmH,WAAA,GAAA7J,aAAA,CAAAA,aAAA,KACAvE,IAAA;UACAqO,QAAA,EAAArO,IAAA,CAAAsO,IAAA,aAAAtO,IAAA,CAAAsO,IAAA;UACAC,gBAAA,EAAAvO,IAAA,CAAAC,EAAA;UACAuO,IAAA,EAAAxO,IAAA,CAAAyO,WAAA;UACAC,OAAA;UACAC,aAAA,EAAA3O,IAAA,CAAA4O,OAAA;UACAtU,UAAA,EAAAC,iBAAA,CAAAyF,IAAA,CAAA4O,OAAA;QAAA,GACAX,OAAA,CAAAN,YAAA,CAAA3N,IAAA,EACA;QACAiO,OAAA,CAAAY,iBAAA,CAAAT,WAAA,EAAAD,YAAA;QACA,IAAAC,WAAA,CAAA9T,UAAA;UACA,IAAAwU,aAAA,GAAAb,OAAA,CAAAc,cAAA,CAAAX,WAAA,EAAAJ,IAAA;UACA,IAAAc,aAAA,CAAAlO,MAAA;YACAkO,aAAA,CAAA9I,OAAA,WAAAgJ,SAAA;cACAA,SAAA,CAAAC,UAAA;YACA;UACA;QACA;QACA,OAAAb,WAAA;MACA;MAEA,KAAAc,aAAA,GAAAlB,IAAA,CAAA9J,GAAA,WAAAlE,IAAA;QAAA,OAAAkO,OAAA,CAAAF,IAAA,EAAAhO,IAAA;MAAA;MAEA,KAAA7D,MAAA,GAAA6R,IAAA,CAAA9J,GAAA,WAAAlE,IAAA;QAAA,OAAAkO,OAAA,CAAAF,IAAA,EAAAhO,IAAA;MAAA;IACA;IACA6O,iBAAA,WAAAA,kBAAA7O,IAAA;MAAA,IAAAmO,YAAA,GAAAlH,SAAA,CAAArG,MAAA,QAAAqG,SAAA,QAAAvJ,SAAA,GAAAuJ,SAAA;MACA,IAAAjH,IAAA,CAAAmP,UAAA;QACA,IAAAC,WAAA,GAAAlM,IAAA,CAAAC,KAAA,CAAAnD,IAAA,CAAAmP,UAAA;QACA,IAAAzU,OAAA,CAAA0U,WAAA,KAAAA,WAAA,CAAAxO,MAAA;UACAwO,WAAA,GAAAA,WAAA,CAAA9L,MAAA,WAAA+L,CAAA;YAAA,OAAAA,CAAA,CAAAC,eAAA;UAAA;UACA,IAAAC,KAAA,GAAAH,WAAA,CAAAlL,GAAA,WAAAmL,CAAA;YACA,IAAAG,MAAA,GAAAhV,iBAAA,CAAAwF,IAAA,CAAAqO,QAAA;YACA,IAAAjD,GAAA,GAAAoE,MAAA,CAAAzP,IAAA,WAAAkI,CAAA;cAAA,OAAAA,CAAA,CAAAzE,IAAA,KAAA6L,CAAA,CAAAC,eAAA;YAAA;YACAtP,IAAA,CAAAqP,CAAA,CAAAC,eAAA,IAAAnB,YAAA,GAAAkB,CAAA,CAAAI,UAAA,GAAAJ,CAAA,CAAAK,WAAA;YAEA;cACAC,UAAA,GAAAvE,GAAA,aAAAA,GAAA,uBAAAA,GAAA,CAAAuE,UAAA;cACAC,WAAA,GAAAxE,GAAA,aAAAA,GAAA,uBAAAA,GAAA,CAAAwE,WAAA;cACApM,IAAA,EAAA6L,CAAA,CAAAC,eAAA;cACAO,IAAA,EAAAR,CAAA,CAAAS,eAAA;cACAC,KAAA,EAAAV,CAAA,CAAAK,WAAA;cACAM,QAAA,EAAAX,CAAA,CAAAI;YACA;UACA;UACA,KAAAhO,MAAA,CAAAC,QAAA;YAAA8M,IAAA,EAAAxO,IAAA,CAAAwO,IAAA;YAAAR,IAAA,EAAAuB;UAAA;QACA;MACA;IACA;IACAU,YAAA,WAAAA,aAAAC,IAAA;MACA,KAAAjU,aAAA;IACA;IACAkU,YAAA,WAAAA,aAAAD,IAAA,EAAA7T,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;IACA;IACA+T,aAAA,WAAAA,cAAAF,IAAA;MAAA,OAAAnP,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAoP,UAAA;QAAA,IAAAC,GAAA,EAAAC,KAAA,EAAAC,gBAAA,EAAAnN,IAAA;QAAA,OAAArC,mBAAA,GAAAI,IAAA,UAAAqP,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAnP,IAAA,GAAAmP,UAAA,CAAAlP,IAAA;YAAA;cACAoG,OAAA,CAAAC,GAAA,CAAAqI,IAAA;cACAI,GAAA,GAAAJ,IAAA,CAAArV,IAAA,CAAA6L,KAAA;cACA6J,KAAA,GAAAD,GAAA,CAAAA,GAAA,CAAA1P,MAAA;cAAA8P,UAAA,CAAAlP,IAAA;cAAA,OACA9H,SAAA;gBAAA0M,GAAA,EAAA8J,IAAA,CAAA9J;cAAA;YAAA;cAAAoK,gBAAA,GAAAE,UAAA,CAAA3N,IAAA;cAAAM,IAAA,GAAAmN,gBAAA,CAAAnN,IAAA;cACA,IAAAkN,KAAA;gBACA/C,MAAA,CAAAC,IAAA,0CAAApK,IAAA;cACA;gBACAmK,MAAA,CAAAC,IAAA,CAAApK,IAAA;cACA;YAAA;YAAA;cAAA,OAAAqN,UAAA,CAAAxO,IAAA;UAAA;QAAA,GAAAmO,SAAA;MAAA;IACA;IACAM,YAAA,WAAAA,aAAAT,IAAA,EAAA7T,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;MACA,KAAAyL,cAAA,CAAAzL,QAAA;MACA,KAAAc,IAAA,CAAAa,cAAA,QAAA3B,QAAA,CAAA6H,GAAA,WAAAlE,IAAA;QACA,IAAAA,IAAA,CAAAoG,GAAA;UACA;YACAC,QAAA,EAAArG,IAAA,CAAAoG,GAAA;YACAD,SAAA,EAAAnG,IAAA,CAAAnF;UACA;QACA;UACA,IAAAuL,GAAA,GAAApG,IAAA,CAAA4Q,QAAA,CAAAvN,IAAA;UACA,IAAAwN,QAAA,GAAAzK,GAAA,CAAAM,KAAA;UACA,IAAAqG,OAAA;YACA1G,QAAA,EAAAwK,QAAA;YACAC,SAAA,EAAAD,QAAA;YACAE,SAAA,EAAAF,QAAA;YACA1K,SAAA,EAAA0K,QAAA;UACA;UACA;YACAxK,QAAA,EAAA0G,OAAA,CAAA1G,QAAA;YACAF,SAAA,EAAA4G,OAAA,CAAA5G;UACA;QACA;MACA;IACA;IACA6K,aAAA,WAAAA,cAAAJ,QAAA,EAAAV,IAAA,EAAA7T,QAAA;MACA,KAAAuU,QAAA,KAAAA,QAAA,CAAAvN,IAAA;QACA;MACA;MACA,KAAAyE,cAAA,CAAAzL,QAAA;MACA,KAAAc,IAAA,CAAAa,cAAA,QAAA3B,QAAA,CAAA6H,GAAA,WAAAlE,IAAA;QACA,IAAAA,IAAA,CAAAoG,GAAA;UACA;YACAC,QAAA,EAAArG,IAAA,CAAAoG,GAAA;YACAD,SAAA,EAAAnG,IAAA,CAAAnF;UACA;QACA;UACA,IAAAmF,IAAA,CAAAkI,MAAA;UACA,IAAA9B,GAAA,GAAApG,IAAA,CAAA4Q,QAAA,CAAAvN,IAAA;UACA,IAAAwN,QAAA,GAAAzK,GAAA,CAAAM,KAAA;UACA,IAAAqG,OAAA;YACA1G,QAAA,EAAAwK,QAAA;YACAC,SAAA,EAAAD,QAAA;YACAE,SAAA,EAAAF,QAAA;YACA1K,SAAA,EAAA0K,QAAA;UACA;UACA;YACAxK,QAAA,EAAA0G,OAAA,CAAA1G,QAAA;YACAF,SAAA,EAAA4G,OAAA,CAAA5G;UACA;QACA;MACA;IACA;IAEA8K,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,KAAA1Q,KAAA,SAAA6H,QAAA;QAAA,IAAA8I,KAAA,GAAApQ,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAmQ,UAAA9I,KAAA;UAAA,OAAAtH,mBAAA,GAAAI,IAAA,UAAAiQ,WAAAC,UAAA;YAAA,kBAAAA,UAAA,CAAA/P,IAAA,GAAA+P,UAAA,CAAA9P,IAAA;cAAA;gBAAA,KACA8G,KAAA;kBAAAgJ,UAAA,CAAA9P,IAAA;kBAAA;gBAAA;gBACA0P,OAAA,CAAAnV,WAAA;gBAAAuV,UAAA,CAAA9P,IAAA;gBAAA,OACA0P,OAAA,CAAAK,MAAA;cAAA;gBACAL,OAAA,CAAAnV,WAAA;gBAAAuV,UAAA,CAAA9P,IAAA;gBAAA;cAAA;gBAEAoG,OAAA,CAAAC,GAAA;gBAAA,OAAAyJ,UAAA,CAAAnF,MAAA,WACA;cAAA;cAAA;gBAAA,OAAAmF,UAAA,CAAApP,IAAA;YAAA;UAAA,GAAAkP,SAAA;QAAA,CAEA;QAAA,iBAAAI,GAAA;UAAA,OAAAL,KAAA,CAAAnN,KAAA,OAAAiD,SAAA;QAAA;MAAA;IACA;IACAwK,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,OAAA3Q,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA0Q,UAAA;QAAA,OAAA3Q,mBAAA,GAAAI,IAAA,UAAAwQ,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAtQ,IAAA,GAAAsQ,UAAA,CAAArQ,IAAA;YAAA;cACAkQ,OAAA,CAAAlR,KAAA,SAAA6H,QAAA;gBAAA,IAAAyJ,KAAA,GAAA/Q,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA8Q,UAAAzJ,KAAA;kBAAA,OAAAtH,mBAAA,GAAAI,IAAA,UAAA4Q,WAAAC,UAAA;oBAAA,kBAAAA,UAAA,CAAA1Q,IAAA,GAAA0Q,UAAA,CAAAzQ,IAAA;sBAAA;wBAAA,KACA8G,KAAA;0BAAA2J,UAAA,CAAAzQ,IAAA;0BAAA;wBAAA;wBACAkQ,OAAA,CAAA1V,aAAA;wBAAAiW,UAAA,CAAAzQ,IAAA;wBAAA,OACAkQ,OAAA,CAAAH,MAAA;sBAAA;wBACAG,OAAA,CAAA1V,aAAA;wBAAAiW,UAAA,CAAAzQ,IAAA;wBAAA;sBAAA;wBAEAoG,OAAA,CAAAC,GAAA;wBAAA,OAAAoK,UAAA,CAAA9F,MAAA,WACA;sBAAA;sBAAA;wBAAA,OAAA8F,UAAA,CAAA/P,IAAA;oBAAA;kBAAA,GAAA6P,SAAA;gBAAA,CAEA;gBAAA,iBAAAG,GAAA;kBAAA,OAAAJ,KAAA,CAAA9N,KAAA,OAAAiD,SAAA;gBAAA;cAAA;YAAA;YAAA;cAAA,OAAA4K,UAAA,CAAA3P,IAAA;UAAA;QAAA,GAAAyP,SAAA;MAAA;IACA;IACAJ,MAAA,WAAAA,OAAAY,OAAA;MAAA,IAAAC,OAAA;MAAA,OAAArR,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAoR,UAAA;QAAA,IAAAC,qBAAA;QAAA,IAAAC,KAAA,EAAAC,QAAA,EAAAC,OAAA,EAAAC,MAAA;QAAA,OAAA1R,mBAAA,GAAAI,IAAA,UAAAuR,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAArR,IAAA,GAAAqR,UAAA,CAAApR,IAAA;YAAA;cACAoG,OAAA,CAAAC,GAAA,cAAAuK,OAAA,CAAAjV,IAAA;cACAoV,KAAA,GAAAhO,aAAA,KAAA6N,OAAA,CAAAjV,IAAA;cACAqV,QAAA;cACA,IAAAJ,OAAA,CAAA1V,YAAA;gBACA8V,QAAA,GAAAJ,OAAA,CAAAjW,MAAA,CAAA+H,GAAA,WAAAlE,IAAA;kBACA,IAAAzB,QAAA,GAAAyB,IAAA,CAAAzB,QAAA;oBAAAiQ,IAAA,GAAAxO,IAAA,CAAAwO,IAAA;oBAAAG,aAAA,GAAA3O,IAAA,CAAA2O,aAAA;oBAAAD,OAAA,GAAA1O,IAAA,CAAA0O,OAAA;oBAAApU,UAAA,GAAA0F,IAAA,CAAA1F,UAAA;oBAAAuY,MAAA,GAAA7S,IAAA,CAAA6S,MAAA;oBAAAC,MAAA,GAAAC,wBAAA,CAAA/S,IAAA,EAAAgT,SAAA;kBACA,IAAAC,SAAA,GAAAb,OAAA,CAAA3Q,MAAA,CAAAyR,KAAA,CAAAC,WAAA,CAAAC,UAAA;kBACA,IAAA7D,KAAA,IAAA0D,SAAA,CAAAzE,IAAA,SAAAtK,GAAA,WAAA+D,CAAA;oBACA6K,MAAA,CAAA7K,CAAA,CAAAzE,IAAA,IAAAyE,CAAA,CAAA+H,QAAA;oBACA;sBACAV,eAAA,EAAArH,CAAA,CAAAzE,IAAA;sBACAsM,eAAA,EAAA7H,CAAA,CAAA4H,IAAA;sBACAH,WAAA,EAAAzH,CAAA,CAAA8H,KAAA;sBACAN,UAAA,EAAAxH,CAAA,CAAA+H;oBACA;kBACA;kBACA8C,MAAA,CAAA3D,UAAA,GAAAjM,IAAA,CAAAE,SAAA,CAAAmM,KAAA;kBACAuD,MAAA,CAAAlE,OAAA,GAAAD,aAAA;kBACA,OAAAmE,MAAA;gBACA;gBACAlL,OAAA,CAAAC,GAAA,CAAA3E,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAoP,QAAA;gBACAD,KAAA,CAAAvN,eAAA;gBACAuN,KAAA,CAAAjF,oBAAA;cACA;gBACAkF,QAAA,GAAAJ,OAAA,CAAAjW,MAAA;cACA;cACAsW,OAAA,GAAAL,OAAA,CAAAjF,YAAA,IAAAiF,OAAA,CAAA1V,YAAA,UAAA0V,OAAA,CAAAzS,MAAA;cACA+S,MAAA,GAAAnO,aAAA,CAAAA,aAAA,KACAgO,KAAA;gBACAc,eAAA,EAAAZ,OAAA;gBACAa,eAAA,EAAApI,YAAA,CAAAC,OAAA;gBACAoI,aAAA,GAAAjB,qBAAA,GAAAF,OAAA,CAAAxT,cAAA,CAAAmB,IAAA,WAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAAC,EAAA,KAAAsS,KAAA,CAAA/U,WAAA;gBAAA,gBAAA8U,qBAAA,uBAAAA,qBAAA,CAAA7O,YAAA;gBACAwB,WAAA,EAAAmN,OAAA,CAAA1V,YAAA,aAAA0V,OAAA,CAAA1V,YAAA;gBACAY,eAAA,EAAAkW,KAAA,CAAA9Y,OAAA,CAAA6X,KAAA,CAAAjV,eAAA,IAAAiV,KAAA,CAAAjV,eAAA,CAAAmW,IAAA,QAAAlB,KAAA,CAAAjV,eAAA;gBACAoW,QAAA,EAAAvB,OAAA;gBACA/M,WAAA,EAAAoN;cAAA;cAEA,IAAAJ,OAAA,CAAA1V,YAAA;gBACAgW,MAAA,CAAA1N,eAAA,GAAAoN,OAAA,CAAAxW,QAAA;cACA;cAAAgX,UAAA,CAAApR,IAAA;cAAA,OACAjI,YAAA,CAAAmZ,MAAA,EAAA9N,IAAA;gBAAA,IAAA+O,KAAA,GAAA5S,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA2S,UAAAtR,GAAA;kBAAA,OAAAtB,mBAAA,GAAAI,IAAA,UAAAyS,WAAAC,UAAA;oBAAA,kBAAAA,UAAA,CAAAvS,IAAA,GAAAuS,UAAA,CAAAtS,IAAA;sBAAA;wBAAA,KACAc,GAAA,CAAAU,SAAA;0BAAA8Q,UAAA,CAAAtS,IAAA;0BAAA;wBAAA;wBAAA,KACA2Q,OAAA;0BAAA2B,UAAA,CAAAtS,IAAA;0BAAA;wBAAA;wBACA4Q,OAAA,CAAAtL,QAAA;0BACA7H,OAAA;0BACAS,IAAA;wBACA;wBACA7F,YAAA,CAAAuY,OAAA,CAAA3Q,MAAA,EAAA2Q,OAAA,CAAA5S,MAAA;wBAAAsU,UAAA,CAAAtS,IAAA;wBAAA;sBAAA;wBAAA,IAEAc,GAAA,CAAAe,IAAA;0BAAAyQ,UAAA,CAAAtS,IAAA;0BAAA;wBAAA;wBACA4Q,OAAA,CAAAtL,QAAA;0BACA7H,OAAA;0BACAS,IAAA;wBACA;wBAAA,OAAAoU,UAAA,CAAA3H,MAAA;sBAAA;wBAAA2H,UAAA,CAAAtS,IAAA;wBAAA,OAGA4Q,OAAA,CAAA2B,WAAA,CAAAzR,GAAA,CAAAe,IAAA;sBAAA;wBAAAyQ,UAAA,CAAAtS,IAAA;wBAAA;sBAAA;wBAGA,IAAAc,GAAA,CAAAe,IAAA,IAAAf,GAAA,CAAAe,IAAA,CAAA2Q,IAAA;0BACAxG,MAAA,CAAAC,IAAA,CAAA3T,UAAA,CAAAsY,OAAA,CAAA1E,QAAA,EAAApL,GAAA,CAAAe,IAAA,CAAA2Q,IAAA;wBACA;wBACA5B,OAAA,CAAAtL,QAAA;0BACA7H,OAAA,EAAAqD,GAAA,CAAAyE,OAAA;0BACArH,IAAA;wBACA;sBAAA;sBAAA;wBAAA,OAAAoU,UAAA,CAAA5R,IAAA;oBAAA;kBAAA,GAAA0R,SAAA;gBAAA,CAEA;gBAAA,iBAAAK,GAAA;kBAAA,OAAAN,KAAA,CAAA3P,KAAA,OAAAiD,SAAA;gBAAA;cAAA;YAAA;YAAA;cAAA,OAAA2L,UAAA,CAAA1Q,IAAA;UAAA;QAAA,GAAAmQ,SAAA;MAAA;IACA;IACA0B,WAAA,WAAAA,YAAA9T,EAAA;MAAA,IAAAiU,OAAA;MAAA,OAAAnT,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkT,UAAA;QAAA,OAAAnT,mBAAA,GAAAI,IAAA,UAAAgT,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA9S,IAAA,GAAA8S,UAAA,CAAA7S,IAAA;YAAA;cAAA6S,UAAA,CAAA7S,IAAA;cAAA,OACAhI,cAAA;gBACAyG,EAAA,EAAAA;cACA,GAAA2E,IAAA,WAAAtC,GAAA;gBACA,IAAAA,GAAA,CAAAU,SAAA;kBACAkR,OAAA,CAAApN,QAAA;oBACA7H,OAAA;oBACAS,IAAA;kBACA;kBACA7F,YAAA,CAAAqa,OAAA,CAAAzS,MAAA,EAAAyS,OAAA,CAAA1U,MAAA;gBACA;kBACA0U,OAAA,CAAApN,QAAA;oBACA7H,OAAA,EAAAqD,GAAA,CAAAyE,OAAA;oBACArH,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA2U,UAAA,CAAAnS,IAAA;UAAA;QAAA,GAAAiS,SAAA;MAAA;IACA;IACAhN,WAAA,WAAAA,YAAA;MAAA,IAAAmN,YAAA,EAAAC,YAAA;MACA,KAAA5X,UAAA,CAAAC,cAAA;MACA,KAAAD,UAAA,CAAAE,SAAA;MACA,KAAAF,UAAA,CAAAG,aAAA;MACA,KAAAH,UAAA,CAAAI,qBAAA;MACA,KAAAJ,UAAA,CAAAK,gBAAA;MACA,KAAAL,UAAA,CAAAM,oBAAA;MACA,CAAAqX,YAAA,QAAA9T,KAAA,cAAA8T,YAAA,gBAAAA,YAAA,GAAAA,YAAA,CAAAE,QAAA,cAAAF,YAAA,eAAAA,YAAA,CAAAG,gBAAA;MACA,CAAAF,YAAA,QAAA/T,KAAA,cAAA+T,YAAA,gBAAAA,YAAA,GAAAA,YAAA,CAAAC,QAAA,cAAAD,YAAA,eAAAA,YAAA,CAAAG,WAAA;IACA;IACAnK,OAAA,WAAAA,QAAA;MACA,KAAApO,MAAA;MACA,KAAA+S,aAAA;MACA,KAAA/H,WAAA;IACA;IACAwN,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,KAAAC,WAAA;QACAC,aAAA;QACAC,SAAA;QACAC,QAAA;MACA;MACA,IAAAC,UAAA,QAAAxT,MAAA,CAAAyR,KAAA,CAAAC,WAAA,CAAAC,UAAA;MACA5M,MAAA,CAAA0O,IAAA,CAAAD,UAAA,EAAAjP,OAAA,WAAAwI,IAAA;QACA,IAAA2G,UAAA,GAAAF,UAAA,CAAAzG,IAAA;QACA2G,UAAA,CAAAnP,OAAA,WAAAhG,IAAA;UACA,IAAAA,IAAA,CAAAwD,IAAA;YACAoR,OAAA,CAAAC,WAAA,CAAAC,aAAA,CAAA9U,IAAA,CAAA+P,KAAA,IAAA/P,IAAA,CAAAgQ,QAAA;UACA,WAAAhQ,IAAA,CAAAwD,IAAA;YACAoR,OAAA,CAAAC,WAAA,CAAAE,SAAA,CAAA/U,IAAA,CAAA+P,KAAA,IAAA/P,IAAA,CAAAgQ,QAAA;UACA,WAAAhQ,IAAA,CAAAwD,IAAA;YACAoR,OAAA,CAAAC,WAAA,CAAAG,QAAA,CAAAhV,IAAA,CAAA+P,KAAA,IAAA/P,IAAA,CAAAgQ,QAAA;UACA;QACA;MACA;MAEA,IAAAoF,MAAA,QAAA5U,KAAA,CAAAgU,QAAA;MACA,IAAAhS,UAAA,GAAA4S,MAAA,CAAAC,gBAAA;MACA,IAAAC,MAAA,GAAA9S,UAAA,CAAA+S,OAAA;MACAD,MAAA,CAAAta,IAAA,SAAA2B,UAAA,CAAAC,cAAA,OAAAD,UAAA,CAAAG,aAAA,OAAAH,UAAA,CAAAE,SAAA;MACAyY,MAAA,CAAA5G,OAAA;MACA0G,MAAA,CAAAI,UAAA;MACA,KAAAhV,KAAA,CAAAgU,QAAA,CAAAC,gBAAA;MACA,KAAAjU,KAAA,CAAAgU,QAAA,CAAAiB,gBAAA;IACA;IAEAC,SAAA,WAAAA,UAAAC,QAAA,EAAAC,KAAA;MACA,IAAAA,KAAA;QACA,OAAAD,QAAA,CAAAlY,GAAA;MACA;QACA,IAAAoY,MAAA,GAAA3b,OAAA,CAAAyb,QAAA,CAAAlY,GAAA,OACAqY,QAAA,MAAA1Z,UAAA,CAAAwZ,KAAA,MAAAnY,GAAA;QAEA,IAAAoY,MAAA,CAAApX,KAAA;UACAkX,QAAA,CAAAI,KAAA;QACA,WAAAF,MAAA,CAAApX,KAAA;UACAkX,QAAA,CAAAK,MAAA;QACA;QACA,OAAAH,MAAA,CAAApX,KAAA,eAAAoX,MAAA,CAAAhQ,MAAA;MACA;IACA;IACAoQ,UAAA,WAAAA,WAAArN,GAAA;MAAA,IAAAsN,OAAA;MACAtO,OAAA,CAAAC,GAAA,CAAAe,GAAA;MACA,KAAApN,aAAA;MACA,KAAAG,gBAAA;MACA,KAAAD,KAAA;MACA,KAAAD,KAAA,wBAAA8L,MAAA,MAAA4O,SAAA,CAAAvN,GAAA;MACA,KAAAF,SAAA;QAAA,IAAA0N,qBAAA;QACA,IAAAC,UAAA,GAAAH,OAAA,CAAAhH,aAAA,CAAAnP,IAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAwO,IAAA,KAAA5F,GAAA,CAAA4F,IAAA;QAAA;QACA5G,OAAA,CAAAC,GAAA,CAAAwO,UAAA;QACA,CAAAD,qBAAA,GAAAF,OAAA,CAAA1V,KAAA,CAAAtD,OAAA,cAAAkZ,qBAAA,eAAAA,qBAAA,CAAAE,IAAA,CAAA1N,GAAA,EAAAyN,UAAA,EAAAH,OAAA,CAAAvW,MAAA,EAAAuW,OAAA,CAAA/Z,MAAA,EAAA+Z,OAAA,CAAAjT,QAAA;MACA;IACA;IACAsT,YAAA,WAAAA,aAAA3N,GAAA;MAAA,IAAA4N,OAAA;MACA5O,OAAA,CAAAC,GAAA,QAAAe,GAAA;MACA,KAAA6N,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAjX,IAAA;MACA,GAAAkF,IAAA;QACA4R,OAAA,CAAAI,eAAA,CAAAhO,GAAA,CAAA4F,IAAA;MACA,GAAAqI,KAAA;QACA;MAAA,CACA;IACA;IACAC,aAAA,WAAAA,cAAAlO,GAAA;MACA,KAAAmO,gBAAA,CAAAnO,GAAA,CAAA4F,IAAA;IACA;IACAwI,eAAA,WAAAA,gBAAAC,GAAA;MAAA,IAAAC,OAAA;MACAtP,OAAA,CAAAC,GAAA,QAAAoP,GAAA;MACA,IAAAE,WAAA,YAAAA,YAAA;QACA,IAAAF,GAAA;UACA,IAAAtU,UAAA;UACA,IAAAD,QAAA,GAAAwU,OAAA,CAAAza,OAAA,CAAA6G,MAAA,WAAAtD,IAAA;YAAA,OAAA2C,UAAA,CAAAY,QAAA,CAAAvD,IAAA,CAAAwD,IAAA;UAAA;UACA0T,OAAA,CAAAza,OAAA,GAAAiG,QAAA;UACAwU,OAAA,CAAAxO,SAAA,WAAAC,CAAA;YACAuO,OAAA,CAAA1W,KAAA,CAAAgU,QAAA,CAAA4C,aAAA;UACA;QACA,WAAAH,GAAA;UACAC,OAAA,CAAAza,OAAA,GAAAzC,SAAA,CAAAkd,OAAA,CAAAjT,WAAA;QACA;UACAiT,OAAA,CAAAza,OAAA,GAAAzC,SAAA,CAAAkd,OAAA,CAAAjT,WAAA;QACA;QACAiT,OAAA,CAAAzV,MAAA,CAAAC,QAAA;QACAwV,OAAA,CAAAxa,YAAA,GAAAua,GAAA;MACA;MAEA,SAAA9a,MAAA,SAAAA,MAAA,CAAAyE,MAAA;QACA,YAAA6V,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAjX,IAAA;QACA,GAAAkF,IAAA;UACAsS,OAAA,CAAA/a,MAAA;UACAgb,WAAA;UACAD,OAAA,CAAAtb,QAAA;QACA,GAAAib,KAAA,cAEA;MACA;QACA,KAAAjb,QAAA;QACAub,WAAA;MACA;IACA;IACAE,eAAA,WAAAA,gBAAArJ,IAAA;MAAA,IAAAsJ,OAAA;MACA,IAAAC,aAAA,OAAAC,GAAA,MAAArb,MAAA,CAAA+H,GAAA,WAAAlE,IAAA;QAAA,OAAAA,IAAA,CAAAwO,IAAA;MAAA;MACAR,IAAA,GAAAA,IAAA,CAAA1K,MAAA,WAAAtD,IAAA;QAAA,QAAAuX,aAAA,CAAAE,GAAA,CAAAzX,IAAA,CAAAyO,WAAA;MAAA;MAEA,KAAAT,IAAA,CAAApN,MAAA;QACA;MACA;MAEAoN,IAAA,GAAAA,IAAA,CAAA9J,GAAA,WAAAlE,IAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAsX,OAAA,CAAAI,sBAAA,CAAA1X,IAAA;QACA,OAAAuE,aAAA,CAAAA,aAAA;UACAjK,UAAA;UACAqU,aAAA,EAAA2I,OAAA,CAAAK,iBAAA;QAAA,GACA3X,IAAA;UACAuO,gBAAA,EAAAvO,IAAA,CAAAC,EAAA;UACAuO,IAAA,EAAAxO,IAAA,CAAAyO,WAAA;UACAC,OAAA;UACAL,QAAA,EAAArO,IAAA,CAAAsO,IAAA,aAAAtO,IAAA,CAAAsO,IAAA;QAAA,GACAgJ,OAAA,CAAA3J,YAAA,CAAA3N,IAAA;QAEA;MACA;MAEA,KAAA7D,MAAA,MAAAoL,MAAA,CAAAqQ,kBAAA,MAAAzb,MAAA,GAAAyb,kBAAA,CAAA5J,IAAA,GAAA6J,IAAA,WAAAC,CAAA,EAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAzJ,IAAA,GAAAwJ,CAAA,CAAAxJ,IAAA;MAAA;MAEA,IAAA0J,cAAA,QAAA9I,aAAA;MACA,KAAAA,aAAA,GAAAhM,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,IAAAmE,MAAA,CAAAqQ,kBAAA,CAAAI,cAAA,GAAAJ,kBAAA,CAAA5J,IAAA;MACA,KAAAiK,YAAA,MAAA9b,MAAA;IACA;IACA8b,YAAA,WAAAA,aAAA9b,MAAA;MAAA,IAAA+b,OAAA;MACA,IAAAC,WAAA,GAAA5T,aAAA,UAAA9C,MAAA,CAAAyR,KAAA,CAAAC,WAAA,CAAAC,UAAA;MACA,IAAAgF,cAAA,GAAAjc,MAAA,CAAAmH,MAAA,WAAAtD,IAAA;QACA,SAAAmY,WAAA,CAAAnY,IAAA,CAAAwO,IAAA;MACA;MACA,IAAA6J,aAAA,GAAAlc,MAAA,CAAAmH,MAAA,WAAAtD,IAAA;QAAA,OAAAA,IAAA,CAAA1F,UAAA;MAAA;MACA,IAAA+d,aAAA,CAAAzX,MAAA;QACAgH,OAAA,CAAAC,GAAA,CAAAwQ,aAAA;QACA,IAAAC,QAAA,GAAAD,aAAA,CAAA/U,MAAA,WAAAtD,IAAA;UAAA,OAAAA,IAAA,CAAAsO,IAAA;QAAA;QACA,IAAAgK,QAAA,CAAA1X,MAAA;UACA0X,QAAA,CAAAtS,OAAA,WAAAhG,IAAA;YACA,IAAAuY,KAAA,GAAAL,OAAA,CAAAM,cAAA,CAAAxY,IAAA;YACA,IAAAuY,KAAA,IAAAA,KAAA,CAAAje,UAAA;cACA,IAAAme,oBAAA,GAAAP,OAAA,CAAAQ,gBAAA,CAAA1Y,IAAA;cACA,IAAAyY,oBAAA,CAAA7X,MAAA;gBACA6X,oBAAA,CAAAzS,OAAA,WAAA2S,WAAA;kBACA,IAAAC,MAAA,GAAAV,OAAA,CAAAW,YAAA,CAAA7Y,IAAA,EAAA2Y,WAAA;kBACA,KAAAC,MAAA;kBACAV,OAAA,CAAAY,IAAA,CAAAH,WAAA;kBACAT,OAAA,CAAAY,IAAA,CAAAH,WAAA,mBAAAT,OAAA,CAAAP,iBAAA;gBACA;cACA;YACA;UACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,KAAAS,cAAA,CAAAxX,MAAA;MACAwX,cAAA,CAAApS,OAAA,WAAAhG,IAAA;QACA,IAAAuP,KAAA,GAAA2I,OAAA,CAAAQ,gBAAA,CAAA1Y,IAAA;QACAuP,KAAA,GAAAA,KAAA,CAAAjM,MAAA,WAAAyV,CAAA;UAAA,QAAAZ,WAAA,CAAAY,CAAA,CAAAvK,IAAA;QAAA;QACA,IAAAe,KAAA,CAAA3O,MAAA;UACA2O,KAAA,CAAAvJ,OAAA,WAAAoF,GAAA;YACA,IAAA8M,OAAA,CAAAW,YAAA,CAAA7Y,IAAA,EAAAoL,GAAA;cACA,IAAA+J,UAAA,GAAA+C,OAAA,CAAAzW,MAAA,CAAAyR,KAAA,CAAAC,WAAA,CAAAC,UAAA,CAAApT,IAAA,CAAAwO,IAAA;cACA2G,UAAA,CAAAnP,OAAA,WAAA7K,MAAA;gBACAiQ,GAAA,CAAAjQ,MAAA,CAAAqI,IAAA,IAAAxD,IAAA,CAAA7E,MAAA,CAAAqI,IAAA;cACA;cAEA0U,OAAA,CAAAzW,MAAA,CAAAC,QAAA;gBACA8M,IAAA,EAAApD,GAAA,CAAAoD,IAAA;gBACAR,IAAA,EAAAmH;cACA;cACAvN,OAAA,CAAAC,GAAA,QAAA7H,IAAA,CAAAiP,UAAA;cACA,IAAAjP,IAAA,CAAA1F,UAAA;gBACA;cAAA,CAEA;gBACA4d,OAAA,CAAAR,sBAAA,CAAAtM,GAAA,EAAA+J,UAAA;cACA;YACA;cACA,IAAApH,WAAA,GAAA/N,IAAA,CAAA+N,WAAA;gBAAA+E,MAAA,GAAAC,wBAAA,CAAA/S,IAAA,EAAAgZ,UAAA;cACA,IAAAC,YAAA,IAAAf,OAAA,CAAAzW,MAAA,CAAAyR,KAAA,CAAAC,WAAA,CAAAC,UAAA,CAAApT,IAAA,CAAAwO,IAAA,SAAAlL,MAAA,WAAAnI,MAAA;gBAAA,OAAAA,MAAA,CAAAqI,IAAA;cAAA;cACAyV,YAAA,CAAAjT,OAAA,WAAA7K,MAAA;gBACAiQ,GAAA,CAAAjQ,MAAA,CAAAqI,IAAA,IAAAxD,IAAA,CAAA7E,MAAA,CAAAqI,IAAA;cACA;cACA;cACA0U,OAAA,CAAAzW,MAAA,CAAAC,QAAA;gBACA8M,IAAA,EAAApD,GAAA,CAAAoD,IAAA;gBACAR,IAAA,EAAAiL;cACA;cACAf,OAAA,CAAAR,sBAAA,CAAAtM,GAAA,EAAA6N,YAAA;YACA;UACA;QACA;MACA;IACA;IACAC,QAAA,WAAAA,SAAAC,KAAA;MAAA,IAAAvQ,GAAA,GAAAuQ,KAAA,CAAAvQ,GAAA;QAAAoF,IAAA,GAAAmL,KAAA,CAAAnL,IAAA;MACApG,OAAA,CAAAC,GAAA,uBAAAe,GAAA,EAAAoF,IAAA;MACA,IAAAoL,WAAA;MACApL,IAAA,CAAAhI,OAAA,WAAAhG,IAAA;QACAoZ,WAAA,CAAApZ,IAAA,CAAAwD,IAAA,IAAAxD,IAAA,CAAAgQ,QAAA;MACA;MACA;;MAEA,IAAAqJ,eAAA,QAAA5X,MAAA,CAAAyR,KAAA,CAAAC,WAAA,CAAAC,UAAA,CAAAxK,GAAA,CAAA4F,IAAA;MACA,IAAA8K,mBAAA,GAAAD,eAAA,CAAAnV,GAAA,WAAA/I,MAAA;QAAA,OAAAA,MAAA,CAAAqI,IAAA;MAAA;MAEA,IAAA+V,kBAAA,GAAAD,mBAAA,CAAAhW,MAAA,WAAAlE,IAAA;QAAA,QAAA4O,IAAA,CAAAwL,IAAA,WAAAxZ,IAAA;UAAA,OAAAA,IAAA,CAAAwD,IAAA,KAAApE,IAAA;QAAA;MAAA;MACAwI,OAAA,CAAAC,GAAA,WAAA0R,kBAAA;MAEA,IAAAA,kBAAA,CAAA3Y,MAAA;QACA,IAAAyV,UAAA,QAAAnH,aAAA,CAAAnP,IAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAwO,IAAA,KAAA5F,GAAA,CAAA4F,IAAA;QAAA;QACA+K,kBAAA,CAAAvT,OAAA,WAAA5G,IAAA;UACAwI,OAAA,CAAAC,GAAA,6BAAAN,MAAA,CAAAnI,IAAA,iCAAAiX,UAAA,CAAAjX,IAAA;UACAga,WAAA,CAAAha,IAAA,IAAAiX,UAAA,CAAAjX,IAAA;QACA;MACA;MACAwI,OAAA,CAAAC,GAAA,gBAAA3E,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAgW,WAAA;;MAEA;MACA,KAAAK,oBAAA,CAAA7Q,GAAA,CAAA4F,IAAA,EAAA4K,WAAA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAM,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,eAAA;MACA,IAAAC,OAAA,YAAAA,OAAAC,KAAA;QACA,KAAAA,KAAA,KAAAA,KAAA,CAAAlZ,MAAA;QACAkZ,KAAA,CAAA9T,OAAA,WAAAhG,IAAA;UACA4Z,eAAA,CAAA7V,IAAA,CAAA/D,IAAA,CAAAwO,IAAA;UACA,IAAAxO,IAAA,CAAAzB,QAAA,IAAAyB,IAAA,CAAAzB,QAAA,CAAAqC,MAAA;YACAiZ,OAAA,CAAA7Z,IAAA,CAAAzB,QAAA;UACA;QACA;MACA;MACAsb,OAAA,MAAAvd,iBAAA;MACAsL,OAAA,CAAAC,GAAA,oBAAA+R,eAAA;MACAA,eAAA,CAAA5T,OAAA,WAAAhG,IAAA;QACA2Z,OAAA,CAAAlY,MAAA,CAAAC,QAAA,8BAAA1B,IAAA;MACA;MACA,KAAA7D,MAAA,QAAAA,MAAA,CAAAmH,MAAA,WAAAtD,IAAA;QAAA,QAAA4Z,eAAA,CAAArW,QAAA,CAAAvD,IAAA,CAAAwO,IAAA;MAAA;MACA,KAAAU,aAAA,QAAAA,aAAA,CAAA5L,MAAA,WAAAtD,IAAA;QAAA,QAAA4Z,eAAA,CAAArW,QAAA,CAAAvD,IAAA,CAAAwO,IAAA;MAAA;MACA,KAAAlS,iBAAA;IACA;IACAyd,mBAAA,WAAAA,oBAAAD,KAAA;MACAlS,OAAA,CAAAC,GAAA,UAAAiS,KAAA;MACAlS,OAAA,CAAAC,GAAA,uBAAArH,KAAA,CAAAgU,QAAA,CAAAwF,kBAAA;MACA,IAAAC,OAAA,GAAAH,KAAA,CAAAG,OAAA;MACA,KAAA3d,iBAAA,GAAAwd,KAAA,CAAAG,OAAA;IACA;IACAC,cAAA,WAAAA,eAAA;MACAxgB,SAAA;QACA0M,GAAA,OAAAxK;MACA,GAAAgJ,IAAA,WAAAtC,GAAA;QACAkL,MAAA,CAAAC,IAAA,CAAAnL,GAAA,CAAAe,IAAA;MACA;IACA;IACA8W,cAAA,WAAAA,eAAAC,UAAA;MACA,IAAA9J,GAAA,GAAA8J,UAAA,CAAA1T,KAAA;MACA,IAAA2T,MAAA;MACA,IAAA/J,GAAA,CAAA/M,QAAA,CAAAjJ,UAAA,CAAAggB,KAAA;QACAD,MAAA,CAAAtW,IAAA;MACA,WAAAuM,GAAA,CAAA/M,QAAA,CAAAjJ,UAAA,CAAAigB,QAAA;QACAF,MAAA,CAAAtW,IAAA;MACA,WAAAuM,GAAA,CAAA/M,QAAA,CAAAjJ,UAAA,CAAAkgB,UAAA,KAAAlK,GAAA,CAAA/M,QAAA,CAAAjJ,UAAA,CAAAmgB,UAAA;QACAJ,MAAA,CAAAtW,IAAA;MACA,WAAAuM,GAAA,CAAA/M,QAAA,CAAAjJ,UAAA,CAAAogB,QAAA;QACAL,MAAA,CAAAtW,IAAA;MACA;QACAsW,MAAA,CAAAtW,IAAA;MACA;MACA,OAAAsW,MAAA;IACA;IACAlE,SAAA,WAAAA,UAAAvN,GAAA;MACA,IAAAA,GAAA,CAAA0F,IAAA;QACA,OAAA1F,GAAA,CAAAmM,SAAA;MACA,WAAAnM,GAAA,CAAA0F,IAAA;QACA,OAAA1F,GAAA,CAAAkM,aAAA;MACA;QACA,OAAAlM,GAAA,CAAAoM,QAAA;MACA;IACA;IACA2F,UAAA,WAAAA,WAAA/R,GAAA;MACA,IAAAuM,UAAA,QAAA1T,MAAA,CAAAyR,KAAA,CAAAC,WAAA,CAAAC,UAAA,CAAAxK,GAAA,CAAA4F,IAAA;MACA,KAAAjS,oBAAA,GAAA4Y,UAAA;IACA;IACAyF,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAvF,MAAA,GAAAuF,KAAA,CAAAvF,MAAA;QAAAwF,MAAA,GAAAD,KAAA,CAAAC,MAAA;QAAAC,SAAA,GAAAF,KAAA,CAAAE,SAAA;QAAAnS,GAAA,GAAAiS,KAAA,CAAAjS,GAAA;QAAAoS,MAAA,GAAAH,KAAA,CAAAG,MAAA;MACA,IAAAnF,MAAA,QAAAoF,YAAA,CAAArS,GAAA;MACA,OAAAiN,MAAA;IACA;IACAoF,YAAA,WAAAA,aAAArS,GAAA;MACA,IAAAsS,gBAAA,GACA,KAAAve,UAAA;QADAC,cAAA,GAAAse,gBAAA,CAAAte,cAAA;QAAAG,qBAAA,GAAAme,gBAAA,CAAAne,qBAAA;QAAAD,aAAA,GAAAoe,gBAAA,CAAApe,aAAA;QACAG,oBAAA,GAAAie,gBAAA,CAAAje,oBAAA;QAAAJ,SAAA,GAAAqe,gBAAA,CAAAre,SAAA;QAAAG,gBAAA,GAAAke,gBAAA,CAAAle,gBAAA;MAEA,IAAAme,cAAA,QAAAtG,WAAA,CAAAC,aAAA,CAAAlM,GAAA,CAAAkM,aAAA,KAAAlM,GAAA,CAAAkM,aAAA;MACA,IAAAsG,UAAA,QAAAvG,WAAA,CAAAE,SAAA,CAAAnM,GAAA,CAAAmM,SAAA,KAAAnM,GAAA,CAAAmM,SAAA;MACA,IAAAsG,SAAA,QAAAxG,WAAA,CAAAG,QAAA,CAAApM,GAAA,CAAAoM,QAAA,KAAApM,GAAA,CAAAoM,QAAA;MAEA,IAAAsG,SAAA;MAEA,IAAAze,SAAA;QACA,IAAAG,gBAAA;UACAse,SAAA,GAAAD,SAAA,CAAA9X,QAAA,CAAA1G,SAAA;QACA;UACAye,SAAA,GAAAD,SAAA,KAAAxe,SAAA;QACA;MACA;MACA,IAAA0e,aAAA;MACA,IAAAze,aAAA;QACA,IAAAG,oBAAA;UACAse,aAAA,GAAAJ,cAAA,CAAA5X,QAAA,CAAAzG,aAAA;QACA;UACAye,aAAA,GAAAJ,cAAA,KAAAre,aAAA;QACA;MACA;MACA,IAAA0e,cAAA;MACA,IAAA5e,cAAA;QACA,IAAAG,qBAAA;UACAye,cAAA,GAAAJ,UAAA,CAAA7X,QAAA,CAAA3G,cAAA;QACA;UACA4e,cAAA,GAAAJ,UAAA,KAAAxe,cAAA;QACA;MACA;MACA;;MAEA,IAAAiZ,MAAA,GAAA2F,cAAA,IAAAD,aAAA,IAAAD,SAAA;MACA,IAAAzF,MAAA;QACA;MACA;QACA;MACA;IACA;IACA4F,aAAA,WAAAA,cAAAnL,GAAA;MACA,KAAAA,GAAA,KAAAA,GAAA,CAAA1P,MAAA;QACA,KAAA2J,OAAA;QACA;MACA;MACA,KAAApO,MAAA,QAAAA,MAAA,CAAAmH,MAAA,WAAAtD,IAAA;QAAA,OAAAsQ,GAAA,CAAAkJ,IAAA,WAAArY,EAAA;UAAA,OAAAA,EAAA,KAAAnB,IAAA,CAAA0b,cAAA;QAAA;MAAA;MACA,KAAAxM,aAAA,QAAAA,aAAA,CAAA5L,MAAA,WAAAtD,IAAA;QAAA,OAAAsQ,GAAA,CAAAkJ,IAAA,WAAArY,EAAA;UAAA,OAAAA,EAAA,KAAAnB,IAAA,CAAA0b,cAAA;QAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}
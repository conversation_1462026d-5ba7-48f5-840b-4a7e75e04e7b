{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\change-management\\contact-list\\add.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\change-management\\contact-list\\add.vue", "mtime": 1757572678775}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GeAreaTrees", "GetInstallUnitIdNameList", "GetProjectPageList", "GetFactoryPeoplelist", "GetMocOrderInfo", "GetMocOrderTypeList", "ImportChangFile", "SaveMocOrder", "SubmitMocOrder", "GetCompanyDepartTree", "GetOssUrl", "ImportFile", "OSSUpload", "closeTagView", "combineURL", "debounce", "deepClone", "StatusDialog", "numeral", "GetTableSettingList", "HandleEdit", "AddHandle", "changeType", "changeTypeReverse", "getAllCodesByType", "getFileNameFromUrl", "isArray", "SteelComponentManager", "GetCurFactory", "processHead", "name", "components", "mixins", "data", "factoryReferenceId", "curStatus", "del", "change", "add", "increase", "decrease", "unChange", "dialogVisible", "title", "width", "currentComponent", "filePath", "finishFee", "pageLoading", "saveLoading", "submitLoading", "uploadLoading", "tbLoading", "tbData", "activities", "fileList", "multipleSelection", "changeRowContentList", "filterCodeOptions", "columns", "changeMethod", "searchForm", "component_name", "part_name", "assembly_name", "component_search_mode", "part_search_mode", "assembly_search_mode", "content", "form", "Sys_Project_Id", "Area_Id", "InstallUnit_Ids", "Handle_UserId", "Moc_Type_Id", "Fee", "undefined", "Hours", "Urgency", "Demand_Date", "Fee_DepartId", "Remark", "AttachmentList", "showImport", "treeParams", "filterable", "clickParent", "props", "disabled", "children", "label", "value", "peopleList", "projectList", "changeTypeList", "installUnitList", "treeParamsArea", "rules", "required", "message", "trigger", "toolbarButtons", "code", "computed", "disableSave", "<PERSON><PERSON><PERSON><PERSON>", "$route", "query", "type", "isEdit", "showDetail", "_this", "zbtk", "find", "item", "Id", "Is_Deepen_Change", "getFileName", "watch", "handler", "newVal", "_this$$refs", "$refs", "installUnitRef", "clearValidate", "immediate", "length", "created", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "_PermittedFactoryList", "PermittedFactoryList", "id", "wrap", "_callee$", "_context", "prev", "next", "JSON", "parse", "localStorage", "getItem", "factoryId", "Reference_Id", "$store", "dispatch", "getProjectData", "getFactoryPeople", "getDepTree", "getFactoryChangeTypeList", "t0", "getInfo", "getTableConfig", "stop", "methods", "_this3", "_callee2", "res", "filteredColumns", "codeColumn", "_customColumns", "_columns", "columnCode", "_callee2$", "_context2", "ProfessionalCode", "sent", "IsSucceed", "allCodes", "stringify", "Data", "filter", "includes", "Code", "Display_Name", "Is_Frozen", "Frozen_Dirction", "<PERSON><PERSON><PERSON>", "Align", "unshift", "push", "apply", "rootColumns", "map", "_item$Display_Name", "displayNameLength", "Math", "max", "_objectSpread", "_this4", "_callee4", "_callee4$", "_context4", "then", "_ref", "_callee3", "_res$Data", "Deepen_File_Url", "Change_Type", "FeeHistory", "Status", "OrderDetail", "_FeeHistory$slice", "_FeeHistory$slice2", "last", "idx", "_callee3$", "_context3", "slice", "_slicedToArray", "format", "findIndex", "splice", "for<PERSON>ach", "element", "obj", "File_Name", "url", "File_Url", "getAreaList", "getInstallUnitPageList", "Object", "assign", "split", "Hour", "Number", "setTbData", "$message", "Message", "_x", "arguments", "mocTypeChange", "handleReset", "handleExceed", "files", "warning", "concat", "handleProgress", "event", "handleError", "err", "console", "log", "checkUploading", "flag", "every", "v", "status", "handleImport", "_this5", "validate", "valid", "handleOpen", "handleAdd", "_this6", "$nextTick", "_", "row", "_this7", "PageSize", "setDisabledTree", "root", "_this8", "Children", "Pid", "_this9", "_callee5", "_callee5$", "_context5", "sysProjectId", "tree", "treeSelectArea", "treeDataUpdateFun", "areaId", "_this0", "_callee6", "_callee6$", "_context6", "Page", "projectChange", "_this1", "_callee7", "_callee7$", "_context7", "clearTb", "areaChange", "_this10", "_callee8", "_callee8$", "_context8", "areaClear", "handleClose", "_this11", "freeze", "curId", "cur", "_this12", "_this13", "_callee1", "getFactoryDeptId", "getDept", "depId", "_callee1$", "_context1", "_ref2", "_callee9", "_callee9$", "_context9", "_res$Data$", "Dept_Id", "abrupt", "_ref3", "_callee0", "_callee0$", "_context0", "_res$Data2", "origin", "disableDirectory", "treeArray", "treeSelect", "_x2", "getTableInfo", "fileObj", "_this14", "ImportType", "toString", "isImportFile", "_res$Data3", "MocOrderDetailList", "Deepen_File_Url_List", "ErrorFileUrl", "window", "open", "$baseUrl", "setAllWeight", "SteelAllWeight", "SteelWeight", "multiply", "SteelAmount", "list", "_this15", "setItem", "isAfterValue", "updatedItem", "CodeType", "Type", "parentChildrenId", "uuid", "MocIdBefore", "checked", "changeContent", "MocType", "setItemMocContent", "childrenItems", "findChildItems", "childItem", "isDisabled", "defaultTbData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "m", "ChangeFieldCode", "_list", "_codes", "AfterValue", "BeforeValue", "Field_Type", "IsCoreField", "Name", "ChangeFieldName", "Value", "NewValue", "beforeUpload", "file", "handleChange", "handlePreview", "_callee10", "arr", "isDwg", "_yield$GetOssUrl", "_callee10$", "_context10", "handleRemove", "response", "fileInfo", "File_Size", "File_Type", "uploadSuccess", "handleSave", "_this16", "_ref4", "_callee11", "_callee11$", "_context11", "submit", "_x3", "handleSubmit", "_this17", "_callee13", "_callee13$", "_context13", "_ref5", "_callee12", "_callee12$", "_context12", "_x4", "isDraft", "_this18", "_callee15", "_this18$changeTypeLis", "_form", "submitTb", "isReNew", "subObj", "_callee15$", "_context15", "isShow", "others", "_objectWithoutProperties", "_excluded", "changeMap", "state", "contactList", "changeCode", "IsNewImportFile", "Handle_UserName", "Moc_Type_Name", "Array", "join", "Is_Draft", "_ref6", "_callee14", "_callee14$", "_context14", "submit<PERSON>heck", "Path", "_x5", "_this19", "_callee16", "_callee16$", "_context16", "_this$$refs2", "_this$$refs3", "tableRef", "setAllTreeExpand", "clearFilter", "handleFilter", "_this20", "nameMapping", "ComponentName", "SteelName", "PartName", "changeMaps", "keys", "changeList", "xTable", "getColumnByField", "option", "filters", "updateData", "clearCheckboxRow", "getFeeGap", "activity", "index", "result", "subtract", "isRed", "isBlue", "handleEdit", "_this21", "getCpCode", "_this21$$refs$content", "defaultRow", "init", "handleDelete", "_this22", "$confirm", "confirmButtonText", "cancelButtonText", "deleteTableItem", "catch", "handleRestore", "restoreTableItem", "changeMethodFun", "val", "_this23", "setTbCloumn", "refreshColumn", "getMocModelList", "_this24", "existingUuids", "Set", "has", "updateItemChangeStatus", "getChangeTypeText", "_toConsumableArray", "sort", "a", "b", "_defaultTbData", "setSameItems", "_this25", "changeInfos", "mocBeforeItems", "isDeleteItems", "unitPart", "unitP", "findParentItem", "similarUnitPartItems", "findSimilarItems", "similarItem", "isSame", "isSameParent", "$set", "k", "_excluded2", "filteredList", "editInfo", "_ref7", "_changeMaps", "existingChanges", "existingChangeCodes", "removedChangeCodes", "some", "batchUpdateTableItem", "handleCancelChange", "_this26", "selected<PERSON><PERSON><PERSON><PERSON>", "getIds", "array", "multiSelectedChange", "getCheckboxRecords", "records", "handelFilePath", "getChangeStyle", "changeName", "rusult", "isAdd", "isAdjust", "isDecrease", "isIncrease", "isDelete", "handleShow", "filterNameMethod", "_ref8", "values", "cellValue", "column", "filterCustom", "_this$searchForm", "_ComponentName", "_SteelName", "_PartName", "partMatch", "assemblyMatch", "componentMatch", "installChange", "InstallUnit_Id", "afterApproval"], "sources": ["src/views/PRO/change-management/contact-list/add.vue"], "sourcesContent": ["<template>\r\n  <div class=\"page-container\">\r\n    <div class=\"form-x\">\r\n      <div>\r\n        <span class=\"cs-title\">工程联系单</span>\r\n      </div>\r\n      <el-divider />\r\n      <el-row v-loading=\"pageLoading\" element-loading-text=\"加载中\">\r\n        <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"120px\">\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"项目名称\" prop=\"Sys_Project_Id\">\r\n              <el-select\r\n                v-model=\"form.Sys_Project_Id\"\r\n                :disabled=\"isView||tbLoading\"\r\n                clearable\r\n                class=\"w100\"\r\n                placeholder=\"请选择\"\r\n                filterable\r\n                @change=\"projectChange()\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in projectList\"\r\n                  :key=\"item.Sys_Project_Id\"\r\n                  :label=\"item.Short_Name\"\r\n                  :value=\"item.Sys_Project_Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"区域名称\" prop=\"Area_Id\">\r\n              <el-tree-select\r\n                ref=\"treeSelectArea\"\r\n                v-model=\"form.Area_Id\"\r\n                :disabled=\"!form.Sys_Project_Id||isView||tbLoading\"\r\n                :select-params=\"{\r\n                  clearable: true,\r\n\r\n                }\"\r\n                :tree-params=\"treeParamsArea\"\r\n                @select-clear=\"areaClear\"\r\n                @node-click=\"areaChange\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item ref=\"installUnitRef\" label=\"批次\" prop=\"InstallUnit_Ids\">\r\n              <el-select\r\n                v-model=\"form.InstallUnit_Ids\"\r\n                filterable\r\n                multiple\r\n                class=\"w100\"\r\n                :disabled=\"isView||!form.Area_Id||tbLoading\"\r\n                placeholder=\"请选择\"\r\n                @change=\"installChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in installUnitList\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"变更人\" prop=\"Handle_UserId\">\r\n              <el-select\r\n                v-model=\"form.Handle_UserId\"\r\n                :disabled=\"isView\"\r\n                filterable\r\n                class=\"w100\"\r\n                clearable\r\n                placeholder=\"请选择\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in peopleList\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"变更类型\" prop=\"Moc_Type_Id\">\r\n              <el-select\r\n                v-model=\"form.Moc_Type_Id\"\r\n                class=\"w100\"\r\n                :disabled=\"isView\"\r\n                filterable\r\n                placeholder=\"请选择\"\r\n                @change=\"mocTypeChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in changeTypeList\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"变更费用\" prop=\"Fee\">\r\n              <el-input-number v-model=\"form.Fee\" placeholder=\"请输入\" style=\"width: 100%\" clearable :disabled=\"isView\" class=\"cs-number-btn-hidden\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"变更工时\" prop=\"Hours\">\r\n              <el-input-number v-model=\"form.Hours\" placeholder=\"请输入\" style=\"width: 100%\" clearable :disabled=\"isView\" class=\"cs-number-btn-hidden\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"紧急程度\" prop=\"Urgency\">\r\n              <el-select v-model=\"form.Urgency\" placeholder=\"请选择\" style=\"width: 100%\" :disabled=\"isView\">\r\n                <el-option label=\"普通\" :value=\"1\" />\r\n                <el-option label=\"紧急\" :value=\"2\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"要求完成时间\" prop=\"Demand_Date\">\r\n              <el-date-picker\r\n                v-model=\"form.Demand_Date\"\r\n                :disabled=\"isView\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                type=\"date\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"费用部门\" prop=\"Fee_DepartId\">\r\n              <el-tree-select\r\n                ref=\"treeSelect\"\r\n                v-model=\"form.Fee_DepartId\"\r\n                :disabled=\"isView\"\r\n                :select-params=\"{\r\n                  clearable: true,\r\n                }\"\r\n                :tree-params=\"treeParams\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"变更说明\" prop=\"Remark\">\r\n              <el-input\r\n                v-model=\"form.Remark\"\r\n                type=\"textarea\"\r\n                :disabled=\"isView\"\r\n                :autosize=\"{ minRows: 3, maxRows: 3}\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"附件\" prop=\"region\">\r\n              <OSSUpload\r\n                ref=\"upload\"\r\n                :disabled=\"isView\"\r\n                action=\"\"\r\n                :before-upload=\"beforeUpload\"\r\n                :limit=\"5\"\r\n                multiple\r\n                :file-list=\"fileList\"\r\n                :on-progress=\"handleProgress\"\r\n                :on-exceed=\"handleExceed\"\r\n                :on-error=\"handleError\"\r\n                :on-success=\"uploadSuccess\"\r\n                :on-change=\"handleChange\"\r\n                :on-remove=\"handleRemove\"\r\n                :on-preview=\"handlePreview\"\r\n                show-file-list\r\n                accept=\".xls, .xlsx,.pdf,.jpg,.png,.dwg,.doc,.docx\"\r\n                btn-icon=\"el-icon-upload\"\r\n                :class=\"isView ? 'z-upload hiddenBtn' : 'z-upload'\"\r\n              >\r\n                <el-button v-if=\"!isView\" :loading=\"uploadLoading\" type=\"primary\">上传文件</el-button>\r\n              </OSSUpload>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-form>\r\n\r\n      </el-row>\r\n    </div>\r\n    <div v-if=\"isView\" class=\"cs-fee\">\r\n      <span class=\"cs-title\">费用变动</span>\r\n      <span class=\"cs-label\">当前金额：<span class=\"fw cs-blue\">{{ finishFee }}元</span></span>\r\n      <el-divider />\r\n      <el-timeline>\r\n        <el-timeline-item\r\n          v-for=\"(activity, index) in activities\"\r\n          :key=\"index\"\r\n          hide-timestamp\r\n        >\r\n          <div class=\"line-content\">\r\n            <span class=\"fee-name\">{{ activity.Create_UserName }}</span>\r\n            <span :class=\"['fee-num',{'txt-red':activity.isRed,'txt-blue':activity.isBlue}]\">{{ getFeeGap(activity,index) }}</span>\r\n            <span class=\"fee-time\">{{ activity.Create_Date }}</span>\r\n            <span class=\"fee-remark\">备注：{{ activity.Remark || '-' }}</span>\r\n          </div>\r\n          <template #dot>\r\n            <span class=\"circle\" />\r\n          </template>\r\n        </el-timeline-item>\r\n      </el-timeline>\r\n    </div>\r\n    <div class=\"cs-main\">\r\n      <template v-if=\"showDetail\">\r\n        <span class=\"cs-title\">变更明细</span>\r\n        <el-divider />\r\n\r\n        <el-form inline class=\"change-method-form\">\r\n          <el-form-item label=\"变更方式：\">\r\n            <template v-if=\"!isView\">\r\n              <el-radio :value=\"changeMethod\" :label=\"1\" @input=\"changeMethodFun(1)\">完整清单导入</el-radio>\r\n              <el-radio :value=\"changeMethod\" :label=\"2\" @input=\"changeMethodFun(2)\">部分清单导入</el-radio>\r\n              <el-radio :value=\"changeMethod\" :label=\"3\" @input=\"changeMethodFun(3)\">手动修改</el-radio>\r\n            </template>\r\n            <template v-else>\r\n              {{ changeMethod === 1 ? '完整清单导入' : changeMethod === 2 ? '部分清单导入' : '手动修改' }}\r\n            </template>\r\n          </el-form-item>\r\n        </el-form>\r\n        <div v-if=\"filePath\">\r\n          <i style=\"color:#409EFF\" class=\"el-icon-download\" />\r\n          <el-link type=\"primary\" :underline=\"false\" @click=\"handelFilePath\">{{ getFileName }}</el-link>\r\n        </div>\r\n        <vxe-toolbar ref=\"xToolbar1\" class=\"cs-toolBar\">\r\n          <template #buttons>\r\n            <el-button v-if=\"!isView && changeMethod !== 3\" type=\"primary\" @click=\"handleImport\">导入变更清单</el-button>\r\n            <el-button v-if=\"!isView && changeMethod === 3\" type=\"primary\" @click=\"handleAdd\">添加变更内容</el-button>\r\n            <el-button v-if=\"!isView && changeMethod === 3\" type=\"danger\" :disabled=\"!multipleSelection.length\" plain @click=\"handleCancelChange\">取消变更</el-button>\r\n          </template>\r\n          <template #tools>\r\n            <el-form ref=\"form2\" inline :model=\"searchForm\" label-width=\"70px\">\r\n              <el-form-item label=\"构件名称\">\r\n                <el-input\r\n                  v-model=\"searchForm.component_name\"\r\n                  clearable\r\n                  style=\"width: 260px;\"\r\n                  placeholder=\"请输入\"\r\n                  class=\"input-with-select\"\r\n                >\r\n                  <el-select\r\n                    slot=\"prepend\"\r\n                    v-model=\"searchForm.component_search_mode\"\r\n                    placeholder=\"请选择\"\r\n                    style=\"width: 100px\"\r\n                  >\r\n                    <el-option\r\n                      label=\"模糊搜索\"\r\n                      :value=\"1\"\r\n                    />\r\n                    <el-option\r\n                      label=\"精确搜索\"\r\n                      :value=\"2\"\r\n                    />\r\n                  </el-select>\r\n                </el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"部件名称\">\r\n                <el-input\r\n                  v-model=\"searchForm.assembly_name\"\r\n                  style=\"width: 260px;\"\r\n                  clearable\r\n                  placeholder=\"请输入\"\r\n                  class=\"input-with-select\"\r\n                >\r\n                  <el-select\r\n                    slot=\"prepend\"\r\n                    v-model=\"searchForm.assembly_search_mode\"\r\n                    placeholder=\"请选择\"\r\n                    style=\"width: 100px\"\r\n                  >\r\n                    <el-option\r\n                      label=\"模糊搜索\"\r\n                      :value=\"1\"\r\n                    />\r\n                    <el-option\r\n                      label=\"精确搜索\"\r\n                      :value=\"2\"\r\n                    />\r\n                  </el-select>\r\n                </el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"零件名称\">\r\n                <el-input\r\n                  v-model=\"searchForm.part_name\"\r\n                  clearable\r\n                  style=\"width: 260px;\"\r\n                  placeholder=\"请输入\"\r\n                  class=\"input-with-select\"\r\n                >\r\n                  <el-select\r\n                    slot=\"prepend\"\r\n                    v-model=\"searchForm.part_search_mode\"\r\n                    placeholder=\"请选择\"\r\n                    style=\"width: 100px\"\r\n                  >\r\n                    <el-option\r\n                      label=\"模糊搜索\"\r\n                      :value=\"1\"\r\n                    />\r\n                    <el-option\r\n                      label=\"精确搜索\"\r\n                      :value=\"2\"\r\n                    />\r\n                  </el-select>\r\n                </el-input>\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button @click=\"handleReset\">重置</el-button>\r\n                <el-button type=\"primary\" @click=\"handleFilter\">搜索</el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n          </template>\r\n        </vxe-toolbar>\r\n        <div class=\"fff tb-x\">\r\n          <vxe-table\r\n            id=\"uuid\"\r\n            ref=\"tableRef\"\r\n            :empty-render=\"{name: 'NotData'}\"\r\n            show-header-overflow\r\n            :loading=\"tbLoading\"\r\n            element-loading-spinner=\"el-icon-loading\"\r\n            element-loading-text=\"拼命加载中\"\r\n            empty-text=\"暂无数据\"\r\n            class=\"cs-vxe-table cs-tree-table\"\r\n            height=\"500\"\r\n            stripe\r\n            :filter-config=\"{showIcon: false}\"\r\n            :row-config=\"{keyField:'uuid', 'isHover': true, 'isCurrent': true}\"\r\n            :data=\"tbData\"\r\n            resizable\r\n            :checkbox-config=\"{checkField: 'checked',labelField: 'CPCode', highlight: true}\"\r\n            :tree-config=\"{transform: true, showIcon: true, rowField: 'parentChildrenId', parentField: 'ParentId'}\"\r\n            :tooltip-config=\"{ enterable: true}\"\r\n            @checkbox-all=\"multiSelectedChange\"\r\n            @checkbox-change=\"multiSelectedChange\"\r\n          >\r\n            <vxe-column v-if=\"changeMethod === 3 && !isView\" fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n            <!-- :type=\"index===0 && changeMethod === 3 && !isView? 'checkbox':''\" -->\r\n\r\n            <template v-for=\"(item,index) in columns\">\r\n              <vxe-column\r\n                :key=\"item.Code+changeMethod\"\r\n                :tree-node=\"index===0\"\r\n                :fixed=\"item.Is_Frozen ? (item.Frozen_Dirction || 'left') : ''\"\r\n                :min-width=\"item.Width\"\r\n                show-overflow=\"tooltip\"\r\n                sortable\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :filters=\"item.Code==='CPCode' ? filterCodeOptions : null\"\r\n                :filter-method=\"item.Code==='CPCode' ? filterNameMethod : null\"\r\n                :title=\"item.Display_Name\"\r\n              >\r\n                <template v-if=\"item.Code==='CPCode'\" #filter=\"{ $panel, column }\">\r\n                  <input v-for=\"(option, index1) in column.filters\" :key=\"index1\" v-model=\"option.data\" type=\"type\" @input=\"$panel.changeOption($event, !!option.data, option)\">\r\n                </template>\r\n                <template v-if=\"item.Code==='CPCode'\" #default=\"{ row }\">\r\n                  {{ getCpCode(row) }}\r\n                  <el-tag v-if=\"row.Type===0\" size=\"mini\">构</el-tag>\r\n                  <el-tag v-else-if=\"row.Type===1\" type=\"warning\" size=\"mini\">部</el-tag>\r\n                  <el-tag v-else type=\"success\" size=\"mini\">零</el-tag>\r\n                </template>\r\n                <template v-else-if=\"item.Code === 'changeContent'\" #default=\"{ row }\">\r\n\r\n                  <el-popover\r\n                    placement=\"left-start\"\r\n                    width=\"400\"\r\n                    trigger=\"click\"\r\n                    @show=\"handleShow(row)\"\r\n                  >\r\n                    <el-table max-height=\"300\" stripe resizable class=\"cs-custom-table\" :data=\"changeRowContentList\">\r\n                      <el-table-column align=\"center\" property=\"Name\" width=\"100\" label=\"变更字段\" />\r\n                      <el-table-column align=\"center\" property=\"Value\" label=\"变更前\" />\r\n                      <el-table-column align=\"center\" property=\"NewValue\" label=\"变更后\" />\r\n                    </el-table>\r\n                    <span slot=\"reference\" style=\"cursor: pointer;\" :class=\"getChangeStyle(row.changeContent)\">\r\n                      {{ row.changeContent }}\r\n                    </span>\r\n                  </el-popover>\r\n\r\n                </template>\r\n                <template v-else-if=\"item.Code === 'Production_Status'\" #default=\"{ row }\">\r\n                  <el-link v-if=\"row.MocIdBefore&& row[item.Code]\" :type=\"row.Production_Status === '未生产' ? 'info' : 'primary'\" @click=\"handleOpen(row)\"> {{ row[item.Code] | displayValue }}</el-link>\r\n                  <span v-else>-</span>\r\n                </template>\r\n                <template v-else-if=\"item.Code === 'Is_Component_Mark'\" #default=\"{ row }\">\r\n                  <!--             是否直发件 是：是直发件；否：非直发件；-->\r\n                  <template v-if=\"row.Type===0\">\r\n                    <el-tag v-if=\"row[item.Code]==='是'\" type=\"primary\">是</el-tag>\r\n                    <el-tag v-else type=\"danger\">否</el-tag>\r\n                  </template>\r\n                  <template v-else>\r\n                    <el-tag v-if=\"row.PartType==='直发件'\" type=\"primary\">是</el-tag>\r\n                    <el-tag v-else type=\"danger\">否</el-tag>\r\n                  </template>\r\n\r\n                </template>\r\n                <template v-else #default=\"{ row }\">\r\n                  <span> {{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n            </template>\r\n\r\n            <!-- Operations column for manual edit mode -->\r\n            <vxe-column v-if=\"changeMethod === 3 && !isView\" fixed=\"right\" title=\"操作\" width=\"160\">\r\n              <template #default=\"{ row }\">\r\n                <el-button\r\n                  v-if=\"row.changeType !== 'isDelete'\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  @click=\"handleEdit(row)\"\r\n                >\r\n                  编辑\r\n                </el-button>\r\n                <el-button\r\n                  v-if=\"row.changeType !== 'isDelete'\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  style=\"color: #FB6B7F;\"\r\n                  @click=\"handleDelete(row)\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n                <el-button\r\n                  v-if=\"row.changeType === 'isDelete' && !row.isDisabled\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  style=\"color: #298DFF;\"\r\n                  @click=\"handleRestore(row)\"\r\n                >\r\n                  撤销删除\r\n                </el-button>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-table>\r\n        </div>\r\n      </template>\r\n      <footer v-if=\"!isView\">\r\n        <el-button :disabled=\"disableSave\" :loading=\"saveLoading\" @click=\"handleSave\">保存草稿</el-button>\r\n        <el-button :disabled=\"disableSave || uploadLoading\" :loading=\"submitLoading\" type=\"primary\" @click=\"handleSubmit\">提交审核</el-button>\r\n      </footer>\r\n      <footer v-if=\"$route.query.operate === 'audit' && $route.query.type==='2'\">\r\n        <processHead :no-style=\"true\" :process-id=\"$route.query.processId\" :business-id=\"$route.query.id\" :web-id=\"$route.query.webId\" @afterapproval=\"afterApproval\" />\r\n      </footer>\r\n    </div>\r\n    <ImportFile ref=\"dialog\" @refresh=\"getTableInfo\" />\r\n    <StatusDialog ref=\"statusDialog\" />\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      v-dialogDrag\r\n      top=\"6vh\"\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"width\"\r\n      class=\"plm-custom-dialog\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        @getMocModelList=\"getMocModelList\"\r\n        @editInfo=\"editInfo\"\r\n        @close=\"handleClose\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GeAreaTrees, GetInstallUnitIdNameList, GetProjectPageList } from '@/api/PRO/project'\r\nimport { GetFactoryPeoplelist } from '@/api/PRO/basic-information/workshop'\r\nimport {\r\n  GetMocOrderInfo,\r\n  GetMocOrderTypeList,\r\n  ImportChangFile,\r\n  SaveMocOrder,\r\n  SubmitMocOrder\r\n} from '@/api/PRO/changeManagement'\r\nimport { GetCompanyDepartTree, GetOssUrl } from '@/api/sys'\r\nimport ImportFile from './components/importFile.vue'\r\nimport OSSUpload from '@/views/plm/components/ossupload.vue'\r\nimport { closeTagView, combineURL, debounce, deepClone } from '@/utils'\r\nimport StatusDialog from './components/dialog.vue'\r\nimport numeral from 'numeral'\r\nimport { GetTableSettingList } from '@/api/PRO/component-type'\r\nimport HandleEdit from './components/HandleEdit.vue'\r\nimport AddHandle from './components/addHandle.vue'\r\nimport { changeType, changeTypeReverse, getAllCodesByType } from './utils'\r\nimport { getFileNameFromUrl } from '@/utils/file'\r\nimport { isArray } from 'ali-oss/lib/common/utils/isArray'\r\nimport SteelComponentManager from '@/views/PRO/change-management/contact-list/info'\r\nimport { GetCurFactory } from '@/api/PRO/factory'\r\nimport processHead from '@/views/PRO/components/processHead'\r\n\r\nexport default {\r\n  name: 'PROEngineeringChangeOrderAdd',\r\n  components: {\r\n    OSSUpload,\r\n    StatusDialog,\r\n    ImportFile,\r\n    HandleEdit,\r\n    AddHandle,\r\n    processHead\r\n  },\r\n  mixins: [SteelComponentManager],\r\n  data() {\r\n    return {\r\n      factoryReferenceId: '',\r\n      curStatus: {\r\n        del: '已删',\r\n        change: '变更',\r\n        add: '新增',\r\n        increase: '数量增加',\r\n        decrease: '数量减少',\r\n        unChange: '无变更'\r\n      },\r\n\r\n      dialogVisible: false,\r\n      title: '',\r\n      width: '50%',\r\n      currentComponent: '',\r\n      filePath: '',\r\n      finishFee: 0,\r\n      pageLoading: false,\r\n      saveLoading: false,\r\n      submitLoading: false,\r\n      uploadLoading: false,\r\n      tbLoading: false,\r\n      tbData: [],\r\n      activities: [],\r\n      fileList: [],\r\n      multipleSelection: [],\r\n      changeRowContentList: [],\r\n      filterCodeOptions: [{ data: '' }],\r\n      columns: [],\r\n      changeMethod: 1,\r\n      searchForm: {\r\n        component_name: '',\r\n        part_name: '',\r\n        assembly_name: '',\r\n        component_search_mode: 1,\r\n        part_search_mode: 1,\r\n        assembly_search_mode: 1,\r\n        content: ''\r\n      },\r\n      form: {\r\n        Sys_Project_Id: '',\r\n        Area_Id: '',\r\n        InstallUnit_Ids: [],\r\n        Handle_UserId: '',\r\n        Moc_Type_Id: '',\r\n        Fee: undefined,\r\n        Hours: undefined,\r\n        Urgency: 1,\r\n        Demand_Date: '',\r\n        Fee_DepartId: '',\r\n        Remark: '',\r\n        AttachmentList: []\r\n      },\r\n      showImport: false,\r\n      treeParams: {\r\n        data: [],\r\n        filterable: false,\r\n        clickParent: true,\r\n        props: {\r\n          disabled: 'disabled',\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      peopleList: [],\r\n      projectList: [],\r\n      changeTypeList: [],\r\n      installUnitList: [],\r\n      treeParamsArea: {\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          disabled: 'disabled',\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      rules: {\r\n        Sys_Project_Id: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        Area_Id: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        Handle_UserId: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        Moc_Type_Id: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        InstallUnit_Ids: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ]\r\n      },\r\n      toolbarButtons: [\r\n        { code: 'myToolbarExport', name: '点击导出' },\r\n        { code: 'myToolbarLink', name: '点击跳转' },\r\n        { code: 'myToolbarCustom', name: '打开自定义列' }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    disableSave() {\r\n      return this.submitLoading || this.uploadLoading\r\n    },\r\n    isView() {\r\n      return this.$route.query.type == '2'\r\n    },\r\n    isEdit() {\r\n      return this.$route.query.type == '1'\r\n    },\r\n    showDetail() {\r\n      const zbtk = this.changeTypeList.find(item => {\r\n        return item.Id === this.form.Moc_Type_Id\r\n      })\r\n      return zbtk?.Is_Deepen_Change || false\r\n    },\r\n    getFileName() {\r\n      return getFileNameFromUrl(this.filePath)\r\n    }\r\n  },\r\n  watch: {\r\n    'form.Area_Id': {\r\n      handler(newVal) {\r\n        if (!newVal) {\r\n          this.rules.InstallUnit_Ids[0].required = false\r\n          this.$refs?.installUnitRef?.clearValidate()\r\n        }\r\n      },\r\n      immediate: true\r\n    },\r\n    'form.InstallUnit_Ids': {\r\n      handler(newVal) {\r\n        if (!this.Area_Id) return\r\n        if (this.installUnitList.length) {\r\n          this.rules.InstallUnit_Ids[0].required = true\r\n          this.$refs.installUnitRef.clearValidate()\r\n        } else {\r\n          this.rules.InstallUnit_Ids[0].required = false\r\n          this.$refs.installUnitRef.clearValidate()\r\n        }\r\n      }\r\n    }\r\n  },\r\n  async created() {\r\n    const PermittedFactoryList = JSON.parse(localStorage.getItem('PermittedFactoryList')) || []\r\n    this.factoryReferenceId = PermittedFactoryList.find(item => item.Id === this.$route.query.factoryId)?.Reference_Id || ''\r\n    // await this.getTableConfig('PROEngChangeOrderAdd') ProfessionalCode\r\n    this.$store.dispatch('contactList/resetChangeCode')\r\n    this.getProjectData()\r\n    this.getFactoryPeople()\r\n    await this.getDepTree()\r\n    this.getFactoryChangeTypeList()\r\n\r\n    const id = this.$route.query.id\r\n    id && await this.getInfo(id)\r\n    this.getTableConfig()\r\n    // this.getTableInfo()\r\n  },\r\n  methods: {\r\n    async getTableConfig() {\r\n      const res = await GetTableSettingList({ ProfessionalCode: 'Steel' })\r\n      if (res.IsSucceed) {\r\n        this.allCodes = JSON.parse(JSON.stringify(res.Data))\r\n        // Filter out the three name columns\r\n        const filteredColumns = res.Data.filter(item =>\r\n          !['SteelName', 'ComponentName', 'PartName'].includes(item.Code)\r\n        )\r\n\r\n        // Add the new CPCode column\r\n        const codeColumn = {\r\n          Code: 'CPCode',\r\n          Display_Name: '构件/部件/零件名称',\r\n          Is_Frozen: true,\r\n          Frozen_Dirction: 'left',\r\n          Width: 180,\r\n          Align: 'left'\r\n        }\r\n\r\n        // Insert the CPCode column at the beginning\r\n        filteredColumns.unshift(codeColumn)\r\n        const _customColumns = [{\r\n          Code: 'Production_Status',\r\n          Display_Name: '生产情况',\r\n          Align: 'center',\r\n          Is_Frozen: true,\r\n          Frozen_Dirction: 'right',\r\n          Width: 120\r\n        }, {\r\n          Code: 'changeContent',\r\n          Display_Name: '变更内容',\r\n          Align: 'center',\r\n          Is_Frozen: true,\r\n          Frozen_Dirction: 'right',\r\n          Width: 120\r\n        }]\r\n\r\n        filteredColumns.push(..._customColumns)\r\n        let _columns = []\r\n\r\n        this.rootColumns = deepClone(filteredColumns.map(item => {\r\n          const displayNameLength = item.Display_Name?.length || 0\r\n          const width = Math.max(120, 120 + Math.max(0, displayNameLength - 4) * 10)\r\n          return {\r\n            ...item,\r\n            Width: width,\r\n            Align: 'center'\r\n          }\r\n        }))\r\n\r\n        if (this.changeMethod === 3) {\r\n          const columnCode = ['CPCode', 'SetupPosition', 'Production_Status', 'changeContent']\r\n          _columns = this.rootColumns.filter(item => columnCode.includes(item.Code))\r\n        } else {\r\n          _columns = this.rootColumns\r\n        }\r\n        this.columns = _columns\r\n      }\r\n    },\r\n    async getInfo(id) {\r\n      this.pageLoading = true\r\n      await GetMocOrderInfo({\r\n        Id: id,\r\n        factoryReferenceId: this.factoryReferenceId\r\n      }).then(async res => {\r\n        if (res.IsSucceed) {\r\n          const {\r\n            Deepen_File_Url,\r\n            Sys_Project_Id,\r\n            Area_Id,\r\n            InstallUnit_Ids,\r\n            Handle_UserId,\r\n            Moc_Type_Id,\r\n            Change_Type,\r\n            Fee,\r\n            Hours,\r\n            Urgency,\r\n            Demand_Date,\r\n            Fee_DepartId,\r\n            Remark,\r\n            FeeHistory,\r\n            Id,\r\n            Status,\r\n            AttachmentList,\r\n            OrderDetail\r\n          } = res.Data\r\n          this.activities = FeeHistory\r\n          if (FeeHistory.length) {\r\n            const [last] = FeeHistory.slice(-1)\r\n            this.finishFee = numeral(last?.Fee || 0).format('0.[00]')\r\n          }\r\n          if (Status === 3) {\r\n            const idx = this.columns.findIndex(item => item.Code === 'Production_Status')\r\n            if (idx !== -1) {\r\n              this.columns.splice(idx, 1)\r\n            }\r\n          }\r\n\r\n          if (AttachmentList?.length) {\r\n            AttachmentList.forEach((element, idx) => {\r\n              const obj = {\r\n                name: element.File_Name,\r\n                url: element.File_Url\r\n              }\r\n              this.fileList.push(obj)\r\n              this.form.AttachmentList.push({\r\n                File_Url: element.File_Url,\r\n                File_Name: element.File_Name\r\n              })\r\n            })\r\n          }\r\n          await this.getAreaList(Sys_Project_Id)\r\n          await this.getInstallUnitPageList(Area_Id)\r\n\r\n          Object.assign(this.form, {\r\n            ...res.Data,\r\n            Sys_Project_Id,\r\n            Area_Id,\r\n            Deepen_File_Url,\r\n            Id,\r\n            InstallUnit_Ids: InstallUnit_Ids ? (typeof InstallUnit_Ids === 'string' ? InstallUnit_Ids.split(',') : InstallUnit_Ids) : [],\r\n            Handle_UserId,\r\n            Moc_Type_Id,\r\n            Fee: Fee || undefined,\r\n            Hour: Hours || undefined,\r\n            Urgency: Number(Urgency),\r\n            Demand_Date,\r\n            Fee_DepartId,\r\n            Remark\r\n          })\r\n\r\n          this.setTbData(OrderDetail)\r\n          this.filePath = Deepen_File_Url\r\n          this.changeMethod = Change_Type === 0 ? 1 : Change_Type === 1 ? 2 : 3\r\n          // Deepen_File_Url\r\n          // setTimeout(() => {\r\n          // Deepen_File_Url && this.getTableInfo({ File_Url: Deepen_File_Url })\r\n          // }, 0)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.pageLoading = false\r\n      })\r\n    },\r\n    mocTypeChange() {\r\n      this.tbData = []\r\n      this.handleReset()\r\n    },\r\n\r\n    handleExceed(files, fileList) {\r\n      this.$message.warning(`当前限制选择 5 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)\r\n    },\r\n    handleProgress(event, files, fileList) {\r\n    },\r\n    handleError(err, files, fileList) {\r\n      console.log('err3', err, files, fileList)\r\n      this.checkUploading(fileList)\r\n    },\r\n    checkUploading(fileList) {\r\n      const flag = fileList.every(v => v.status === 'success')\r\n      flag && (this.uploadLoading = false)\r\n    },\r\n    handleImport() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (valid) {\r\n          console.log('valid', valid)\r\n          this.$refs['dialog'].handleOpen()\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    handleAdd() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (valid) {\r\n          this.title = '添加变更内容'\r\n          this.width = '70%'\r\n          this.currentComponent = 'addHandle'\r\n          this.dialogVisible = true\r\n          this.$nextTick(_ => {\r\n            this.$refs['content'].handleOpen(this.form, this.tbData)\r\n          })\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    handleOpen(row) {\r\n      this.$refs['statusDialog'].handleOpen(row)\r\n    },\r\n    getProjectData() {\r\n      GetProjectPageList({ PageSize: -1, factoryReferenceId: this.factoryReferenceId }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.projectList = res.Data.Data\r\n        }\r\n      })\r\n    },\r\n    setDisabledTree(root) {\r\n      if (!root) return\r\n\r\n      root.forEach((element) => {\r\n        const { Children } = element\r\n        if (Children && Children.length) {\r\n          element.disabled = true\r\n        } else {\r\n          element.disabled = false\r\n          this.setDisabledTree(Children)\r\n        }\r\n      })\r\n    },\r\n    async getAreaList(Pid) {\r\n      await GeAreaTrees({\r\n        sysProjectId: Pid,\r\n        factoryReferenceId: this.factoryReferenceId\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const tree = res.Data\r\n          this.setDisabledTree(tree)\r\n          this.treeParamsArea.data = res.Data\r\n          this.$nextTick(_ => {\r\n            this.$refs.treeSelectArea.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async getInstallUnitPageList(areaId) {\r\n      await GetInstallUnitIdNameList({\r\n        Area_Id: areaId,\r\n        Page: 1,\r\n        PageSize: -1\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.installUnitList = res.Data\r\n          if (this.installUnitList.length) {\r\n            this.rules.InstallUnit_Ids[0].required = true\r\n          } else {\r\n            this.rules.InstallUnit_Ids[0].required = false\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async projectChange() {\r\n      const Sys_Project_Id = this.form.Sys_Project_Id\r\n      this.clearTb()\r\n      this.form.Area_Id = ''\r\n      this.form.InstallUnit_Ids = []\r\n      this.treeParamsArea.data = []\r\n      this.$nextTick(_ => {\r\n        this.$refs.treeSelectArea.treeDataUpdateFun([])\r\n      })\r\n      if (Sys_Project_Id) {\r\n        await this.getAreaList(Sys_Project_Id)\r\n      }\r\n    },\r\n    async areaChange() {\r\n      this.clearTb()\r\n      await this.getInstallUnitPageList(this.form.Area_Id)\r\n      if (this.installUnitList.length && this.form.Area_Id) {\r\n        this.form.InstallUnit_Ids = [this.installUnitList[0].Id]\r\n        this.rules.InstallUnit_Ids[0].required = true\r\n      } else {\r\n        this.rules.InstallUnit_Ids[0].required = false\r\n        this.$refs.installUnitRef.clearValidate()\r\n      }\r\n    },\r\n    areaClear() {\r\n      this.form.Area_Id = ''\r\n      this.form.InstallUnit_Ids = []\r\n      this.clearTb()\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    getFactoryPeople() {\r\n      GetFactoryPeoplelist({ factoryReferenceId: this.factoryReferenceId }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.peopleList = Object.freeze(res.Data || [])\r\n\r\n          const curId = localStorage.getItem('UserId')\r\n          const cur = this.peopleList.find(v => v.Id === curId)\r\n          if (cur) {\r\n            this.form.Handle_UserId = cur.Id\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getFactoryChangeTypeList() {\r\n      GetMocOrderTypeList({ factoryReferenceId: this.factoryReferenceId }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.changeTypeList = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async getDepTree() {\r\n      const getFactoryDeptId = async() => {\r\n        return await GetCurFactory({ factoryReferenceId: this.factoryReferenceId }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            return res?.Data[0]?.Dept_Id\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n\r\n      const getDept = async(depId) => {\r\n        await GetCompanyDepartTree({}).then(res => {\r\n          if (res.IsSucceed) {\r\n            const origin = res.Data?.[0]\r\n            if (origin.Children.length) {\r\n              const tree = origin.Children.filter(v => v.Id === depId)\r\n\r\n              const disableDirectory = (treeArray) => {\r\n                treeArray.map(element => {\r\n                  if (element.Children && element.Children.length > 0) {\r\n                    element.disabled = true\r\n                    disableDirectory(element.Children)\r\n                  }\r\n                })\r\n              }\r\n              disableDirectory(tree)\r\n              this.$refs.treeSelect.treeDataUpdateFun(tree || [])\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n      const depId = await getFactoryDeptId()\r\n      await getDept(depId)\r\n    },\r\n    getTableInfo(fileObj) {\r\n      this.tbLoading = true\r\n      const form = { ...this.form }\r\n      ImportChangFile({\r\n        ...form,\r\n        ImportType: this.changeMethod,\r\n        InstallUnit_Ids: this.form.InstallUnit_Ids.toString(),\r\n        AttachmentList: [fileObj]\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.isImportFile = true\r\n          const { AttachmentList, MocOrderDetailList } = res.Data\r\n          // this.getTbList(Orignal_Deepen_List, Import_Deepen_List)\r\n          // filePath\r\n          if (AttachmentList.length) {\r\n            this.filePath = AttachmentList[0].File_Url\r\n            this.form.Deepen_File_Url = this.filePath\r\n            this.form.Deepen_File_Url_List = [fileObj]\r\n          }\r\n          this.setTbData(MocOrderDetailList)\r\n        } else {\r\n          if (res.Data && res.Data.ErrorFileUrl) {\r\n            window.open(combineURL(this.$baseUrl, res.Data.ErrorFileUrl), '_blank')\r\n          }\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    setAllWeight(row) {\r\n      return {\r\n        SteelAllWeight: numeral(row.SteelWeight).multiply(row.SteelAmount).format('0.[000]')\r\n      }\r\n    },\r\n    setTbData(list) {\r\n      const setItem = (list, item, isAfterValue = false) => {\r\n        const updatedItem = {\r\n          ...item,\r\n          CodeType: item.Type === 0 ? 1 : item.Type === 1 ? 2 : 3,\r\n          parentChildrenId: item.Id,\r\n          uuid: item.MocIdBefore,\r\n          checked: false,\r\n          changeContent: item.MocType,\r\n          changeType: changeTypeReverse[item.MocType],\r\n          ...this.setAllWeight(item)\r\n        }\r\n        this.setItemMocContent(updatedItem, isAfterValue)\r\n        if (updatedItem.changeType === 'isDelete') {\r\n          const childrenItems = this.findChildItems(updatedItem, list)\r\n          if (childrenItems.length) {\r\n            childrenItems.forEach(childItem => {\r\n              childItem.isDisabled = true\r\n            })\r\n          }\r\n        }\r\n        return updatedItem\r\n      }\r\n\r\n      this.defaultTbData = list.map(item => setItem(list, item))\r\n\r\n      this.tbData = list.map(item => setItem(list, item, true))\r\n    },\r\n    setItemMocContent(item, isAfterValue = false) {\r\n      if (item.MocContent) {\r\n        let _MocContent = JSON.parse(item.MocContent)\r\n        if (isArray(_MocContent) && _MocContent.length) {\r\n          _MocContent = _MocContent.filter(m => m.ChangeFieldCode !== 'PartNum')\r\n          const _list = _MocContent.map(m => {\r\n            const _codes = getAllCodesByType(item.CodeType)\r\n            const cur = _codes.find(v => v.Code === m.ChangeFieldCode)\r\n            item[m.ChangeFieldCode] = isAfterValue ? m.AfterValue : m.BeforeValue\r\n\r\n            return {\r\n              Field_Type: cur?.Field_Type || 'string',\r\n              IsCoreField: cur?.IsCoreField || false,\r\n              Code: m.ChangeFieldCode,\r\n              Name: m.ChangeFieldName,\r\n              Value: m.BeforeValue,\r\n              NewValue: m.AfterValue\r\n            }\r\n          })\r\n          this.$store.dispatch('contactList/addChangeCode', { uuid: item.uuid, list: _list })\r\n        }\r\n      }\r\n    },\r\n    beforeUpload(file) {\r\n      this.uploadLoading = true\r\n    },\r\n    handleChange(file, fileList) {\r\n      this.fileList = fileList\r\n    },\r\n    async handlePreview(file) {\r\n      console.log(file)\r\n      const arr = file.name.split('.')\r\n      const isDwg = arr[arr.length - 1] === 'dwg'\r\n      const { Data } = await GetOssUrl({ url: file.url })\r\n      if (isDwg) {\r\n        window.open('http://dwgv1.bimtk.com:5432/?CadUrl=' + Data, '_blank')\r\n      } else {\r\n        window.open(Data)\r\n      }\r\n    },\r\n    handleRemove(file, fileList) {\r\n      this.fileList = fileList\r\n      this.checkUploading(fileList)\r\n      this.form.AttachmentList = this.fileList.map(item => {\r\n        if (item.url) {\r\n          return {\r\n            File_Url: item.url,\r\n            File_Name: item.name\r\n          }\r\n        } else {\r\n          const url = item.response.Data\r\n          const fileInfo = url.split('*')\r\n          const fileObj = {\r\n            File_Url: fileInfo[0],\r\n            File_Size: fileInfo[1],\r\n            File_Type: fileInfo[2],\r\n            File_Name: fileInfo[3]\r\n          }\r\n          return {\r\n            File_Url: fileObj.File_Url,\r\n            File_Name: fileObj.File_Name\r\n          }\r\n        }\r\n      })\r\n    },\r\n    uploadSuccess(response, file, fileList) {\r\n      if (!response || !response.Data) {\r\n        return\r\n      }\r\n      this.checkUploading(fileList)\r\n      this.form.AttachmentList = this.fileList.map(item => {\r\n        if (item.url) {\r\n          return {\r\n            File_Url: item.url,\r\n            File_Name: item.name\r\n          }\r\n        } else {\r\n          if (item.status !== 'success') return\r\n          const url = item.response.Data\r\n          const fileInfo = url.split('*')\r\n          const fileObj = {\r\n            File_Url: fileInfo[0],\r\n            File_Size: fileInfo[1],\r\n            File_Type: fileInfo[2],\r\n            File_Name: fileInfo[3]\r\n          }\r\n          return {\r\n            File_Url: fileObj.File_Url,\r\n            File_Name: fileObj.File_Name\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    handleSave() {\r\n      this.$refs['form'].validate(async(valid) => {\r\n        if (valid) {\r\n          this.saveLoading = true\r\n          await this.submit(true)\r\n          this.saveLoading = false\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    async handleSubmit() {\r\n      this.$refs['form'].validate(async(valid) => {\r\n        if (valid) {\r\n          this.submitLoading = true\r\n          await this.submit(false)\r\n          this.submitLoading = false\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    async submit(isDraft) {\r\n      console.log('this.form', this.form)\r\n      const _form = { ...this.form }\r\n      let submitTb = []\r\n      if (this.changeMethod === 3) {\r\n        submitTb = this.tbData.map((item) => {\r\n          const { children, uuid, changeContent, checked, changeType, isShow, ...others } = item\r\n          const changeMap = this.$store.state.contactList.changeCode\r\n          const _list = (changeMap[uuid] || []).map(v => {\r\n            others[v.Code] = v.NewValue\r\n            return {\r\n              ChangeFieldCode: v.Code,\r\n              ChangeFieldName: v.Name,\r\n              BeforeValue: v.Value,\r\n              AfterValue: v.NewValue\r\n            }\r\n          })\r\n          others.MocContent = JSON.stringify(_list)\r\n          others.MocType = changeContent\r\n          return others\r\n        })\r\n        console.log(JSON.parse(JSON.stringify(submitTb)))\r\n        _form.Deepen_File_Url = null\r\n        _form.Deepen_File_Url_List = null\r\n      } else {\r\n        submitTb = this.tbData\r\n      }\r\n      const isReNew = this.isImportFile && this.changeMethod !== 3 && this.isEdit\r\n      const subObj = {\r\n        ..._form,\r\n        IsNewImportFile: isReNew,\r\n        Handle_UserName: localStorage.getItem('UserName'),\r\n        Moc_Type_Name: this.changeTypeList.find(item => item.Id === _form.Moc_Type_Id)?.Display_Name,\r\n        Change_Type: this.changeMethod === 1 ? 0 : this.changeMethod === 2 ? 1 : 2,\r\n        InstallUnit_Ids: Array.isArray(_form.InstallUnit_Ids) ? _form.InstallUnit_Ids.join(',') : _form.InstallUnit_Ids,\r\n        Is_Draft: isDraft,\r\n        OrderDetail: submitTb\r\n      }\r\n      if (this.changeMethod !== 3) {\r\n        subObj.Deepen_File_Url = this.filePath\r\n      }\r\n      await SaveMocOrder(subObj).then(async res => {\r\n        if (res.IsSucceed) {\r\n          if (isDraft) {\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            closeTagView(this.$store, this.$route)\r\n          } else {\r\n            if (!res.Data) {\r\n              this.$message({\r\n                message: '提交失败',\r\n                type: 'wrarning'\r\n              })\r\n              return\r\n            }\r\n            await this.submitCheck(res.Data)\r\n          }\r\n        } else {\r\n          if (res.Data && res.Data.Path) {\r\n            window.open(combineURL(this.$baseUrl, res.Data.Path), '_blank')\r\n          }\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async submitCheck(Id) {\r\n      await SubmitMocOrder({\r\n        Id\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '提交成功',\r\n            type: 'success'\r\n          })\r\n          closeTagView(this.$store, this.$route)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.searchForm.component_name = ''\r\n      this.searchForm.part_name = ''\r\n      this.searchForm.assembly_name = ''\r\n      this.searchForm.component_search_mode = 1\r\n      this.searchForm.part_search_mode = 1\r\n      this.searchForm.assembly_search_mode = 1\r\n      this.$refs?.tableRef?.setAllTreeExpand(false)\r\n      this.$refs?.tableRef?.clearFilter()\r\n    },\r\n    clearTb() {\r\n      this.tbData = []\r\n      this.defaultTbData = []\r\n      this.handleReset()\r\n    },\r\n    handleFilter() {\r\n      this.nameMapping = {\r\n        ComponentName: {},\r\n        SteelName: {},\r\n        PartName: {}\r\n      }\r\n      const changeMaps = this.$store.state.contactList.changeCode\r\n      Object.keys(changeMaps).forEach(uuid => {\r\n        const changeList = changeMaps[uuid]\r\n        changeList.forEach(item => {\r\n          if (item.Code === 'ComponentName') {\r\n            this.nameMapping.ComponentName[item.Value] = item.NewValue\r\n          } else if (item.Code === 'SteelName') {\r\n            this.nameMapping.SteelName[item.Value] = item.NewValue\r\n          } else if (item.Code === 'PartName') {\r\n            this.nameMapping.PartName[item.Value] = item.NewValue\r\n          }\r\n        })\r\n      })\r\n\r\n      const xTable = this.$refs.tableRef\r\n      const codeColumn = xTable.getColumnByField('CPCode')\r\n      const option = codeColumn.filters[0]\r\n      option.data = [this.searchForm.component_name, this.searchForm.assembly_name, this.searchForm.part_name]\r\n      option.checked = true\r\n      xTable.updateData()\r\n      this.$refs.tableRef.setAllTreeExpand(true)\r\n      this.$refs.tableRef.clearCheckboxRow()\r\n    },\r\n\r\n    getFeeGap(activity, index) {\r\n      if (index === 0) {\r\n        return activity.Fee || 0\r\n      } else {\r\n        const result = numeral(activity.Fee || 0)\r\n          .subtract(this.activities[index - 1].Fee || 0)\r\n\r\n        if (result.value() < 0) {\r\n          activity.isRed = true\r\n        } else if (result.value() > 0) {\r\n          activity.isBlue = true\r\n        }\r\n        return result.value() === 0 ? 0 : result.format('+0.[00]')\r\n      }\r\n    },\r\n    handleEdit(row) {\r\n      console.log(row, 'row')\r\n      this.dialogVisible = true\r\n      this.currentComponent = 'HandleEdit'\r\n      this.width = '50%'\r\n      this.title = `编辑（${this.getCpCode(row)}）`\r\n      this.$nextTick(() => {\r\n        const defaultRow = this.defaultTbData.find(item => item.uuid === row.uuid)\r\n        console.log(defaultRow, 'defaultRow')\r\n        this.$refs.content?.init(row, defaultRow, this.isEdit, this.tbData, this.allCodes)\r\n      })\r\n    },\r\n    handleDelete(row) {\r\n      console.log('row', row)\r\n      this.$confirm('确认要删除这条记录吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.deleteTableItem(row.uuid)\r\n      }).catch(() => {\r\n        // User canceled\r\n      })\r\n    },\r\n    handleRestore(row) {\r\n      this.restoreTableItem(row.uuid)\r\n    },\r\n    changeMethodFun(val) {\r\n      console.log('val', val)\r\n      const setTbCloumn = () => {\r\n        if (val === 3) {\r\n          const columnCode = ['CPCode', 'SetupPosition', 'Production_Status', 'changeContent']\r\n          const _columns = this.columns.filter(item => columnCode.includes(item.Code))\r\n          this.columns = _columns\r\n          this.$nextTick(_ => {\r\n            this.$refs.tableRef.refreshColumn()\r\n          })\r\n        } else if (val === 1) {\r\n          this.columns = deepClone(this.rootColumns)\r\n        } else {\r\n          this.columns = deepClone(this.rootColumns)\r\n        }\r\n        this.$store.dispatch('contactList/resetChangeCode')\r\n        this.changeMethod = val\r\n      }\r\n\r\n      if (this.tbData && this.tbData.length > 0) {\r\n        return this.$confirm('切换变更方式会清空当前已添加的变更明细，是否继续？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.tbData = []\r\n          setTbCloumn()\r\n          this.filePath = ''\r\n        }).catch(() => {\r\n\r\n        })\r\n      } else {\r\n        this.filePath = ''\r\n        setTbCloumn()\r\n      }\r\n    },\r\n    getMocModelList(list) {\r\n      const existingUuids = new Set(this.tbData.map(item => item.uuid))\r\n      list = list.filter(item => !existingUuids.has(item.MocIdBefore))\r\n\r\n      if (!list.length) {\r\n        return\r\n      }\r\n\r\n      list = list.map(item => {\r\n        // const curParent = this.findParentItem(item)\r\n        // if (curParent && curParent.changeType === 'isDelete') {\r\n        //   item.changeType = 'isDelete'\r\n        //   item.changeContent = this.getChangeTypeText(item.changeType)\r\n        //   item.isDisabled = true\r\n        //   this.deleteTableItem(item.uuid)\r\n        //   return {\r\n        //     ...item,\r\n        //     parentChildrenId: item.Id,\r\n        //     uuid: item.MocIdBefore,\r\n        //     checked: false,\r\n        //     CodeType: item.Type === 0 ? 1 : item.Type === 1 ? 2 : 3,\r\n        //     ...this.setAllWeight(item)\r\n        //   }\r\n        // } else {\r\n        this.updateItemChangeStatus(item, [])\r\n        return {\r\n          changeType: 'isNoChange',\r\n          changeContent: this.getChangeTypeText('isNoChange'),\r\n          ...item,\r\n          parentChildrenId: item.Id,\r\n          uuid: item.MocIdBefore,\r\n          checked: false,\r\n          CodeType: item.Type === 0 ? 1 : item.Type === 1 ? 2 : 3,\r\n          ...this.setAllWeight(item)\r\n        }\r\n        // }\r\n      })\r\n\r\n      this.tbData = [...this.tbData, ...list].sort((a, b) => b.Type - a.Type)\r\n\r\n      const _defaultTbData = this.defaultTbData || []\r\n      this.defaultTbData = JSON.parse(JSON.stringify([..._defaultTbData, ...list]))\r\n      this.setSameItems(this.tbData)\r\n    },\r\n    setSameItems(tbData) {\r\n      const changeInfos = { ...this.$store.state.contactList.changeCode }\r\n      const mocBeforeItems = tbData.filter(item => {\r\n        return !!changeInfos[item.uuid]\r\n      })\r\n      const isDeleteItems = tbData.filter(item => item.changeType === 'isDelete')\r\n      if (isDeleteItems.length) {\r\n        console.log(isDeleteItems, 'isDeleteItems')\r\n        const unitPart = isDeleteItems.filter(item => item.Type === 3)\r\n        if (unitPart.length) {\r\n          unitPart.forEach(item => {\r\n            const unitP = this.findParentItem(item)\r\n            if (unitP && unitP.changeType !== 'isDelete') {\r\n              const similarUnitPartItems = this.findSimilarItems(item)\r\n              if (similarUnitPartItems.length) {\r\n                similarUnitPartItems.forEach(similarItem => {\r\n                  const isSame = this.isSameParent(item, similarItem)\r\n                  if (!isSame) return\r\n                  this.$set(similarItem, 'changeType', 'isDelete')\r\n                  this.$set(similarItem, 'changeContent', this.getChangeTypeText('isDelete'))\r\n                })\r\n              }\r\n            }\r\n          })\r\n        }\r\n        // isDeleteItems.forEach(item => {\r\n        //   const similarItems = this.findSimilarItems(item)\r\n        //   if (similarItems.length) {\r\n        //     similarItems.forEach(similarItem => {\r\n        //       console.log(item.Code, 'similarItems')\r\n        //       this.$set(similarItem, 'changeType', item.changeType)\r\n        //       this.$set(similarItem, 'changeContent', item.changeContent)\r\n        //       // const isDisabled = this.isSameParent(item, similarItem)\r\n        //       // this.$set(similarItem, 'isDisabled', !isDisabled)\r\n        //       // if (isDisabled) {\r\n        // this.$set(similarItem, 'changeType', item.changeType)\r\n        // this.$set(similarItem, 'changeContent', item.changeContent)\r\n        //       // }\r\n        //     })\r\n        //   }\r\n        // })\r\n      }\r\n      if (!mocBeforeItems.length) return\r\n      mocBeforeItems.forEach(item => {\r\n        let _list = this.findSimilarItems(item)\r\n        _list = _list.filter(k => !changeInfos[k.uuid])\r\n        if (_list.length) {\r\n          _list.forEach(cur => {\r\n            if (this.isSameParent(item, cur)) {\r\n              const changeList = this.$store.state.contactList.changeCode[item.uuid]\r\n              changeList.forEach(change => {\r\n                cur[change.Code] = item[change.Code]\r\n              })\r\n\r\n              this.$store.dispatch('contactList/addChangeCode', {\r\n                uuid: cur.uuid,\r\n                list: changeList\r\n              })\r\n              console.log('cur', item.isDisabled)\r\n              if (item.changeType === 'isDelete') {\r\n                // this.$set(cur, 'isDisabled', item.isDisabled)\r\n\r\n              } else {\r\n                this.updateItemChangeStatus(cur, changeList)\r\n              }\r\n            } else {\r\n              const { SteelAmount, ...others } = item\r\n              const filteredList = (this.$store.state.contactList.changeCode[item.uuid] || []).filter(change => change.Code !== 'SteelAmount')\r\n              filteredList.forEach(change => {\r\n                cur[change.Code] = item[change.Code]\r\n              })\r\n              // cur.CPCode = item.CPCode\r\n              this.$store.dispatch('contactList/addChangeCode', {\r\n                uuid: cur.uuid,\r\n                list: filteredList\r\n              })\r\n              this.updateItemChangeStatus(cur, filteredList)\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    editInfo({ row, list }) {\r\n      console.log('editInfo row, list', row, list)\r\n      const _changeMaps = {}\r\n      list.forEach(item => {\r\n        _changeMaps[item.Code] = item.NewValue\r\n      })\r\n      // this.resetDefaultVal()\r\n\r\n      const existingChanges = this.$store.state.contactList.changeCode[row.uuid] || []\r\n      const existingChangeCodes = existingChanges.map(change => change.Code)\r\n\r\n      const removedChangeCodes = existingChangeCodes.filter(code => !list.some(item => item.Code === code))\r\n      console.log('已移除的字段', removedChangeCodes)\r\n\r\n      if (removedChangeCodes.length) {\r\n        const defaultRow = this.defaultTbData.find(item => item.uuid === row.uuid)\r\n        removedChangeCodes.forEach(code => {\r\n          console.log(`重置字段 ${code} 为原始值:`, defaultRow[code])\r\n          _changeMaps[code] = defaultRow[code]\r\n        })\r\n      }\r\n      console.log('_changeMaps', JSON.parse(JSON.stringify(_changeMaps)))\r\n\r\n      // 批量更新表格项\r\n      this.batchUpdateTableItem(row.uuid, _changeMaps)\r\n      // this.updateCodesName(row, _changeMaps)\r\n    },\r\n    // updateCodesName(targetItem, _changeMaps) {\r\n    //   if (_changeMaps.SteelName) {\r\n    //     targetItem.SteelName = _changeMaps.SteelName\r\n    //     targetItem.CPCode = _changeMaps.SteelName\r\n    //   } else if (_changeMaps.ComponentName) {\r\n    //     targetItem.ComponentName = _changeMaps.ComponentName\r\n    //     targetItem.CPCode = _changeMaps.ComponentName\r\n    //   } else if (_changeMaps.PartName) {\r\n    //     targetItem.PartName = _changeMaps.PartName\r\n    //     targetItem.CPCode = _changeMaps.PartName\r\n    //   } else {\r\n    //     const defaultRow = this.defaultTbData.find(item => item.uuid === targetItem.uuid)\r\n    //     console.log('defaultRow', JSON.parse(JSON.stringify(defaultRow)))\r\n    //     if (defaultRow) {\r\n    //       targetItem.SteelName = defaultRow.SteelName\r\n    //       targetItem.CPCode = defaultRow.CPCode\r\n    //     }\r\n    //   }\r\n    //   console.log('targetItem', JSON.parse(JSON.stringify(targetItem)))\r\n    //   const _list = this.findSimilarItems(targetItem)\r\n    //   if (_list.length) {\r\n    //     _list.forEach(item => {\r\n    //       item.SteelName = targetItem.SteelName\r\n    //       item.CPCode = targetItem.CPCode\r\n    //     })\r\n    //   }\r\n    // },\r\n    handleCancelChange() {\r\n      const selectedRecords = []\r\n      const getIds = (array) => {\r\n        if (!array || !array.length) return\r\n        array.forEach(item => {\r\n          selectedRecords.push(item.uuid)\r\n          if (item.children && item.children.length) {\r\n            getIds(item.children)\r\n          }\r\n        })\r\n      }\r\n      getIds(this.multipleSelection)\r\n      console.log('selectedRecords', selectedRecords)\r\n      selectedRecords.forEach(item => {\r\n        this.$store.dispatch('contactList/delChangeCode', item)\r\n      })\r\n      this.tbData = this.tbData.filter(item => !selectedRecords.includes(item.uuid))\r\n      this.defaultTbData = this.defaultTbData.filter(item => !selectedRecords.includes(item.uuid))\r\n      this.multipleSelection = []\r\n    },\r\n    multiSelectedChange(array) {\r\n      console.log('array', array)\r\n      console.log('array.records', this.$refs.tableRef.getCheckboxRecords(true))\r\n      const { records } = array\r\n      this.multipleSelection = array.records\r\n    },\r\n    handelFilePath() {\r\n      GetOssUrl({\r\n        url: this.filePath\r\n      }).then(res => {\r\n        window.open(res.Data)\r\n      })\r\n    },\r\n    getChangeStyle(changeName) {\r\n      const arr = changeName.split(',')\r\n      const rusult = ['cs-c-box']\r\n      if (arr.includes(changeType.isAdd)) {\r\n        rusult.push('cs-change-green')\r\n      } else if (arr.includes(changeType.isAdjust)) {\r\n        rusult.push('cs-change-yellow')\r\n      } else if (arr.includes(changeType.isDecrease) || arr.includes(changeType.isIncrease)) {\r\n        rusult.push('cs-change-blue')\r\n      } else if (arr.includes(changeType.isDelete)) {\r\n        rusult.push('cs-change-red')\r\n      } else {\r\n        rusult.push('cs-default')\r\n      }\r\n      return rusult\r\n    },\r\n    getCpCode(row) {\r\n      if (row.Type === 0) {\r\n        return row.SteelName\r\n      } else if (row.Type === 1) {\r\n        return row.ComponentName\r\n      } else {\r\n        return row.PartName\r\n      }\r\n    },\r\n    handleShow(row) {\r\n      const changeList = this.$store.state.contactList.changeCode[row.uuid]\r\n      this.changeRowContentList = changeList || []\r\n    },\r\n    filterNameMethod({ option, values, cellValue, row, column }) {\r\n      const result = this.filterCustom(row)\r\n      return result\r\n    },\r\n    filterCustom(row) {\r\n      const { component_name, component_search_mode, assembly_name,\r\n        assembly_search_mode, part_name, part_search_mode } = this.searchForm\r\n\r\n      const _ComponentName = this.nameMapping.ComponentName[row.ComponentName] || row.ComponentName || ''\r\n      const _SteelName = this.nameMapping.SteelName[row.SteelName] || row.SteelName || ''\r\n      const _PartName = this.nameMapping.PartName[row.PartName] || row.PartName || ''\r\n\r\n      let partMatch = true\r\n\r\n      if (part_name) {\r\n        if (part_search_mode === 1) {\r\n          partMatch = _PartName.includes(part_name)\r\n        } else {\r\n          partMatch = _PartName === part_name\r\n        }\r\n      }\r\n      let assemblyMatch = true\r\n      if (assembly_name) {\r\n        if (assembly_search_mode === 1) {\r\n          assemblyMatch = _ComponentName.includes(assembly_name)\r\n        } else {\r\n          assemblyMatch = _ComponentName === assembly_name\r\n        }\r\n      }\r\n      let componentMatch = true\r\n      if (component_name) {\r\n        if (component_search_mode === 1) {\r\n          componentMatch = _SteelName.includes(component_name)\r\n        } else {\r\n          componentMatch = _SteelName === component_name\r\n        }\r\n      }\r\n      // console.log(componentMatch, assemblyMatch, partMatch)\r\n\r\n      const result = componentMatch && assemblyMatch && partMatch\r\n      if (result) {\r\n        return true\r\n      } else {\r\n        return false\r\n      }\r\n    },\r\n    installChange(arr) {\r\n      if (!arr || !arr.length) {\r\n        this.clearTb()\r\n        return\r\n      }\r\n      this.tbData = this.tbData.filter(item => arr.some(id => id === item.InstallUnit_Id))\r\n      this.defaultTbData = this.defaultTbData.filter(item => arr.some(id => id === item.InstallUnit_Id))\r\n    },\r\n    afterApproval() {\r\n      closeTagView(this.$store, this.$route)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.cs-type {\r\n  padding: 2px;\r\n  font-weight: 400;\r\n  font-size: 12px;\r\n  color: #146EB4;\r\n  border-radius: 4px;\r\n  margin-right: 4px;\r\n  background-color: rgba(66, 107, 216, .1);\r\n}\r\n\r\n.cs-change {\r\n  padding: 2px 4px;\r\n  font-weight: 400;\r\n  font-size: 12px;\r\n  color: #298DFF;\r\n  border-radius: 4px;\r\n  background-color: rgba(41, 141, 255, .1)\r\n}\r\n\r\n.cs-c-box {\r\n  padding: 2px 4px;\r\n  font-weight: 400;\r\n  font-size: 12px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.cs-change-green {\r\n  color: #ffffff;\r\n  background-color: #3ECC93\r\n}\r\n\r\n.cs-change-green-p {\r\n  color: #3ECC93;\r\n  background-color: rgba(62, 204, 147, .1);\r\n}\r\n\r\n.cs-change-blue {\r\n  color: #ffffff;\r\n  background-color: #298DFF\r\n}\r\n\r\n.cs-change-blue-p {\r\n  color: #298DFF;\r\n  background-color: rgba(41, 141, 255, .1)\r\n}\r\n\r\n.cs-change-red {\r\n  color: #ffffff;\r\n  background-color: #FB6B7F\r\n}\r\n\r\n.cs-change-red-p {\r\n  color: #FB6B7F;\r\n  background-color: rgba(251, 107, 127, .1)\r\n}\r\n\r\n.cs-default {\r\n  color: #ffffff;\r\n  background-color: #8E95AA\r\n}\r\n\r\n.cs-default-p {\r\n  color: #8E95AA;\r\n  background-color: rgba(142, 149, 170, .1)\r\n}\r\n\r\n.cs-change-yellow {\r\n  color: #ffffff;\r\n  background-color:  #FB8F00;\r\n}\r\n.cs-green {\r\n  background: rgba(62, 204, 147, .1);\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  color: #3ECC93;\r\n  padding: 3px 10px;\r\n}\r\n\r\n.cs-yellow {\r\n  background: rgba(241, 180, 48, .1);\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  color: #F1B430;\r\n  padding: 3px 10px;\r\n}\r\n\r\n.cs-red {\r\n  background: rgba(251, 107, 127, .1);\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  color: #FB6B7F;\r\n  padding: 3px 10px;\r\n}\r\n\r\n.m-4 {\r\n  margin: 0 4px;\r\n}\r\n\r\n.cs-title {\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  color: rgba(34, 40, 52, 0.85);\r\n}\r\n\r\n.page-container {\r\n  margin: 16px;\r\n\r\n  .form-x {\r\n    background-color: #FFFFFF;\r\n    padding: 16px;\r\n  }\r\n\r\n  .cs-main {\r\n    margin-top: 16px;\r\n    background-color: #FFFFFF;\r\n    padding: 16px;\r\n  }\r\n}\r\n\r\n.cs-fee{\r\n  margin-top: 16px;\r\n  background-color: #FFFFFF;\r\n  padding: 16px 16px 0 16px;\r\n  .line-content{\r\n    align-items: center;\r\n    display: flex;\r\n  }\r\n  .cs-title{\r\n    margin-right: 8px;\r\n  }\r\n  .cs-label{\r\n    font-size: 14px;\r\n  }\r\n  .fw{\r\n    font-weight: bold;\r\n  }\r\n  .cs-blue{\r\n    color: #298DFF\r\n  }\r\n  .cs-red{\r\n    color: #FB6B7F\r\n  }\r\n  .fee-name{\r\n    font-weight: bold;\r\n    font-size: 14px;\r\n    color: rgba(34,40,52,0.85);\r\n    margin-right: 32px;\r\n  }\r\n  .fee-sub{\r\n    font-weight: bold;\r\n    font-size: 12px;\r\n    color: rgba(34,40,52,0.65);\r\n    margin-right: 32px;\r\n  }\r\n  .fee-num{\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    margin-right: 32px;\r\n  }\r\n  .fee-time{\r\n    font-weight: 400;\r\n    margin-right: 32px;\r\n    font-size: 12px;\r\n    color: rgba(34,40,52,0.65);\r\n  }\r\n  .fee-remark{\r\n    font-weight: bold;\r\n    font-size: 12px;\r\n    color: rgba(34,40,52,0.65);\r\n  }\r\n  .circle {\r\n    width: 14px;\r\n    height: 14px;\r\n    border-radius: 50%;\r\n    border: 4px solid #458CF7;\r\n    background-color: white;\r\n  }\r\n  ::v-deep{\r\n    .el-timeline-item__tail{\r\n      height: 37%;\r\n      margin: 18px 0;\r\n      width: 1px;\r\n      left: 5px;\r\n      border: 1px solid rgba(41, 141, 255, 0.3);\r\n    }\r\n  }\r\n}\r\n\r\n.el-divider--horizontal {\r\n  margin: 16px 0;\r\n}\r\n\r\nfooter {\r\n  margin-top: 16px;\r\n  text-align: right;\r\n}\r\n\r\n.cs-toolBar {\r\n  ::v-deep {\r\n    .vxe-button--icon.vxe-icon-custom-column{\r\n      display: none;\r\n    }\r\n\r\n    .vxe-button.type--button.is--circle {\r\n      width: 97px;\r\n      z-index: 0;\r\n      border-radius: 4px;\r\n    }\r\n\r\n    .el-form-item {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n.el-tree-select{\r\n  ::v-deep{\r\n    .el-select{\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n\r\n.change-method-form {\r\n  margin-bottom: 0px;\r\n}\r\n.cs-tree-table{\r\n ::v-deep{\r\n   .vxe-tree-cell{\r\n     text-align: left !important;\r\n   }\r\n   .vxe-checkbox--label{\r\n     color:#333333;\r\n   }\r\n   .col--checkbox{\r\n    .vxe-cell{\r\n      padding-left: 10px !important;\r\n    }\r\n   }\r\n }\r\n}\r\n\r\n.z-upload.hiddenBtn{\r\n  ::v-deep{\r\n    .el-upload{\r\n      display: none;\r\n    }\r\n    .el-upload-list__item:first-child{\r\n      margin-top: 4px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2dA,SAAAA,WAAA,EAAAC,wBAAA,EAAAC,kBAAA;AACA,SAAAC,oBAAA;AACA,SACAC,eAAA,EACAC,mBAAA,EACAC,eAAA,EACAC,YAAA,EACAC,cAAA,QACA;AACA,SAAAC,oBAAA,EAAAC,SAAA;AACA,OAAAC,UAAA;AACA,OAAAC,SAAA;AACA,SAAAC,YAAA,EAAAC,UAAA,EAAAC,QAAA,EAAAC,SAAA;AACA,OAAAC,YAAA;AACA,OAAAC,OAAA;AACA,SAAAC,mBAAA;AACA,OAAAC,UAAA;AACA,OAAAC,SAAA;AACA,SAAAC,UAAA,EAAAC,iBAAA,EAAAC,iBAAA;AACA,SAAAC,kBAAA;AACA,SAAAC,OAAA;AACA,OAAAC,qBAAA;AACA,SAAAC,aAAA;AACA,OAAAC,WAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAnB,SAAA,EAAAA,SAAA;IACAK,YAAA,EAAAA,YAAA;IACAN,UAAA,EAAAA,UAAA;IACAS,UAAA,EAAAA,UAAA;IACAC,SAAA,EAAAA,SAAA;IACAQ,WAAA,EAAAA;EACA;EACAG,MAAA,GAAAL,qBAAA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,kBAAA;MACAC,SAAA;QACAC,GAAA;QACAC,MAAA;QACAC,GAAA;QACAC,QAAA;QACAC,QAAA;QACAC,QAAA;MACA;MAEAC,aAAA;MACAC,KAAA;MACAC,KAAA;MACAC,gBAAA;MACAC,QAAA;MACAC,SAAA;MACAC,WAAA;MACAC,WAAA;MACAC,aAAA;MACAC,aAAA;MACAC,SAAA;MACAC,MAAA;MACAC,UAAA;MACAC,QAAA;MACAC,iBAAA;MACAC,oBAAA;MACAC,iBAAA;QAAAzB,IAAA;MAAA;MACA0B,OAAA;MACAC,YAAA;MACAC,UAAA;QACAC,cAAA;QACAC,SAAA;QACAC,aAAA;QACAC,qBAAA;QACAC,gBAAA;QACAC,oBAAA;QACAC,OAAA;MACA;MACAC,IAAA;QACAC,cAAA;QACAC,OAAA;QACAC,eAAA;QACAC,aAAA;QACAC,WAAA;QACAC,GAAA,EAAAC,SAAA;QACAC,KAAA,EAAAD,SAAA;QACAE,OAAA;QACAC,WAAA;QACAC,YAAA;QACAC,MAAA;QACAC,cAAA;MACA;MACAC,UAAA;MACAC,UAAA;QACAnD,IAAA;QACAoD,UAAA;QACAC,WAAA;QACAC,KAAA;UACAC,QAAA;UACAC,QAAA;UACAC,KAAA;UACAC,KAAA;QACA;MACA;MACAC,UAAA;MACAC,WAAA;MACAC,cAAA;MACAC,eAAA;MACAC,cAAA;QACA;QACAX,UAAA;QACAC,WAAA;QACArD,IAAA;QACAsD,KAAA;UACAC,QAAA;UACAC,QAAA;UACAC,KAAA;UACAC,KAAA;QACA;MACA;MACAM,KAAA;QACA3B,cAAA,GACA;UAAA4B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA7B,OAAA,GACA;UAAA2B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA3B,aAAA,GACA;UAAAyB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA1B,WAAA,GACA;UAAAwB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA5B,eAAA,GACA;UAAA0B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,cAAA,GACA;QAAAC,IAAA;QAAAxE,IAAA;MAAA,GACA;QAAAwE,IAAA;QAAAxE,IAAA;MAAA,GACA;QAAAwE,IAAA;QAAAxE,IAAA;MAAA;IAEA;EACA;EACAyE,QAAA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAAtD,aAAA,SAAAC,aAAA;IACA;IACAsD,MAAA,WAAAA,OAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,YAAAH,MAAA,CAAAC,KAAA,CAAAC,IAAA;IACA;IACAE,UAAA,WAAAA,WAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,IAAA,QAAAlB,cAAA,CAAAmB,IAAA,WAAAC,IAAA;QACA,OAAAA,IAAA,CAAAC,EAAA,KAAAJ,KAAA,CAAA1C,IAAA,CAAAK,WAAA;MACA;MACA,QAAAsC,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAAI,gBAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,OAAA5F,kBAAA,MAAAqB,QAAA;IACA;EACA;EACAwE,KAAA;IACA;MACAC,OAAA,WAAAA,QAAAC,MAAA;QACA,KAAAA,MAAA;UAAA,IAAAC,WAAA;UACA,KAAAxB,KAAA,CAAAzB,eAAA,IAAA0B,QAAA;UACA,CAAAuB,WAAA,QAAAC,KAAA,cAAAD,WAAA,gBAAAA,WAAA,GAAAA,WAAA,CAAAE,cAAA,cAAAF,WAAA,eAAAA,WAAA,CAAAG,aAAA;QACA;MACA;MACAC,SAAA;IACA;IACA;MACAN,OAAA,WAAAA,QAAAC,MAAA;QACA,UAAAjD,OAAA;QACA,SAAAwB,eAAA,CAAA+B,MAAA;UACA,KAAA7B,KAAA,CAAAzB,eAAA,IAAA0B,QAAA;UACA,KAAAwB,KAAA,CAAAC,cAAA,CAAAC,aAAA;QACA;UACA,KAAA3B,KAAA,CAAAzB,eAAA,IAAA0B,QAAA;UACA,KAAAwB,KAAA,CAAAC,cAAA,CAAAC,aAAA;QACA;MACA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAAC,qBAAA;MAAA,IAAAC,oBAAA,EAAAC,EAAA;MAAA,OAAAL,mBAAA,GAAAM,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACAN,oBAAA,GAAAO,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;YACAhB,MAAA,CAAA9F,kBAAA,KAAAmG,qBAAA,GAAAC,oBAAA,CAAArB,IAAA,WAAAC,IAAA;cAAA,OAAAA,IAAA,CAAAC,EAAA,KAAAa,MAAA,CAAAtB,MAAA,CAAAC,KAAA,CAAAsC,SAAA;YAAA,gBAAAZ,qBAAA,uBAAAA,qBAAA,CAAAa,YAAA;YACA;YACAlB,MAAA,CAAAmB,MAAA,CAAAC,QAAA;YACApB,MAAA,CAAAqB,cAAA;YACArB,MAAA,CAAAsB,gBAAA;YAAAZ,QAAA,CAAAE,IAAA;YAAA,OACAZ,MAAA,CAAAuB,UAAA;UAAA;YACAvB,MAAA,CAAAwB,wBAAA;YAEAjB,EAAA,GAAAP,MAAA,CAAAtB,MAAA,CAAAC,KAAA,CAAA4B,EAAA;YAAAG,QAAA,CAAAe,EAAA,GACAlB,EAAA;YAAA,KAAAG,QAAA,CAAAe,EAAA;cAAAf,QAAA,CAAAE,IAAA;cAAA;YAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OAAAZ,MAAA,CAAA0B,OAAA,CAAAnB,EAAA;UAAA;YACAP,MAAA,CAAA2B,cAAA;YACA;UAAA;UAAA;YAAA,OAAAjB,QAAA,CAAAkB,IAAA;QAAA;MAAA,GAAAxB,OAAA;IAAA;EACA;EACAyB,OAAA;IACAF,cAAA,WAAAA,eAAA;MAAA,IAAAG,MAAA;MAAA,OAAA7B,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA4B,SAAA;QAAA,IAAAC,GAAA,EAAAC,eAAA,EAAAC,UAAA,EAAAC,cAAA,EAAAC,QAAA,EAAAC,UAAA;QAAA,OAAAnC,mBAAA,GAAAM,IAAA,UAAA8B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5B,IAAA,GAAA4B,SAAA,CAAA3B,IAAA;YAAA;cAAA2B,SAAA,CAAA3B,IAAA;cAAA,OACAzH,mBAAA;gBAAAqJ,gBAAA;cAAA;YAAA;cAAAR,GAAA,GAAAO,SAAA,CAAAE,IAAA;cACA,IAAAT,GAAA,CAAAU,SAAA;gBACAZ,MAAA,CAAAa,QAAA,GAAA9B,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAA+B,SAAA,CAAAZ,GAAA,CAAAa,IAAA;gBACA;gBACAZ,eAAA,GAAAD,GAAA,CAAAa,IAAA,CAAAC,MAAA,WAAA5D,IAAA;kBAAA,OACA,4CAAA6D,QAAA,CAAA7D,IAAA,CAAA8D,IAAA;gBAAA,CACA,GAEA;gBACAd,UAAA;kBACAc,IAAA;kBACAC,YAAA;kBACAC,SAAA;kBACAC,eAAA;kBACAC,KAAA;kBACAC,KAAA;gBACA,GAEA;gBACApB,eAAA,CAAAqB,OAAA,CAAApB,UAAA;gBACAC,cAAA;kBACAa,IAAA;kBACAC,YAAA;kBACAI,KAAA;kBACAH,SAAA;kBACAC,eAAA;kBACAC,KAAA;gBACA;kBACAJ,IAAA;kBACAC,YAAA;kBACAI,KAAA;kBACAH,SAAA;kBACAC,eAAA;kBACAC,KAAA;gBACA;gBAEAnB,eAAA,CAAAsB,IAAA,CAAAC,KAAA,CAAAvB,eAAA,EAAAE,cAAA;gBACAC,QAAA;gBAEAN,MAAA,CAAA2B,WAAA,GAAAzK,SAAA,CAAAiJ,eAAA,CAAAyB,GAAA,WAAAxE,IAAA;kBAAA,IAAAyE,kBAAA;kBACA,IAAAC,iBAAA,KAAAD,kBAAA,GAAAzE,IAAA,CAAA+D,YAAA,cAAAU,kBAAA,uBAAAA,kBAAA,CAAA7D,MAAA;kBACA,IAAAlF,KAAA,GAAAiJ,IAAA,CAAAC,GAAA,YAAAD,IAAA,CAAAC,GAAA,IAAAF,iBAAA;kBACA,OAAAG,aAAA,CAAAA,aAAA,KACA7E,IAAA;oBACAkE,KAAA,EAAAxI,KAAA;oBACAyI,KAAA;kBAAA;gBAEA;gBAEA,IAAAvB,MAAA,CAAAlG,YAAA;kBACAyG,UAAA;kBACAD,QAAA,GAAAN,MAAA,CAAA2B,WAAA,CAAAX,MAAA,WAAA5D,IAAA;oBAAA,OAAAmD,UAAA,CAAAU,QAAA,CAAA7D,IAAA,CAAA8D,IAAA;kBAAA;gBACA;kBACAZ,QAAA,GAAAN,MAAA,CAAA2B,WAAA;gBACA;gBACA3B,MAAA,CAAAnG,OAAA,GAAAyG,QAAA;cACA;YAAA;YAAA;cAAA,OAAAG,SAAA,CAAAX,IAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IACA;IACAL,OAAA,WAAAA,QAAAnB,EAAA;MAAA,IAAAyD,MAAA;MAAA,OAAA/D,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA8D,SAAA;QAAA,OAAA/D,mBAAA,GAAAM,IAAA,UAAA0D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxD,IAAA,GAAAwD,SAAA,CAAAvD,IAAA;YAAA;cACAoD,MAAA,CAAAhJ,WAAA;cAAAmJ,SAAA,CAAAvD,IAAA;cAAA,OACAxI,eAAA;gBACA+G,EAAA,EAAAoB,EAAA;gBACArG,kBAAA,EAAA8J,MAAA,CAAA9J;cACA,GAAAkK,IAAA;gBAAA,IAAAC,IAAA,GAAApE,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAmE,SAAAtC,GAAA;kBAAA,IAAAuC,SAAA,EAAAC,eAAA,EAAAlI,cAAA,EAAAC,OAAA,EAAAC,eAAA,EAAAC,aAAA,EAAAC,WAAA,EAAA+H,WAAA,EAAA9H,GAAA,EAAAE,KAAA,EAAAC,OAAA,EAAAC,WAAA,EAAAC,YAAA,EAAAC,MAAA,EAAAyH,UAAA,EAAAvF,EAAA,EAAAwF,MAAA,EAAAzH,cAAA,EAAA0H,WAAA,EAAAC,iBAAA,EAAAC,kBAAA,EAAAC,IAAA,EAAAC,GAAA;kBAAA,OAAA9E,mBAAA,GAAAM,IAAA,UAAAyE,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAAvE,IAAA,GAAAuE,SAAA,CAAAtE,IAAA;sBAAA;wBAAA,KACAoB,GAAA,CAAAU,SAAA;0BAAAwC,SAAA,CAAAtE,IAAA;0BAAA;wBAAA;wBAAA2D,SAAA,GAoBAvC,GAAA,CAAAa,IAAA,EAlBA2B,eAAA,GAAAD,SAAA,CAAAC,eAAA,EACAlI,cAAA,GAAAiI,SAAA,CAAAjI,cAAA,EACAC,OAAA,GAAAgI,SAAA,CAAAhI,OAAA,EACAC,eAAA,GAAA+H,SAAA,CAAA/H,eAAA,EACAC,aAAA,GAAA8H,SAAA,CAAA9H,aAAA,EACAC,WAAA,GAAA6H,SAAA,CAAA7H,WAAA,EACA+H,WAAA,GAAAF,SAAA,CAAAE,WAAA,EACA9H,GAAA,GAAA4H,SAAA,CAAA5H,GAAA,EACAE,KAAA,GAAA0H,SAAA,CAAA1H,KAAA,EACAC,OAAA,GAAAyH,SAAA,CAAAzH,OAAA,EACAC,WAAA,GAAAwH,SAAA,CAAAxH,WAAA,EACAC,YAAA,GAAAuH,SAAA,CAAAvH,YAAA,EACAC,MAAA,GAAAsH,SAAA,CAAAtH,MAAA,EACAyH,UAAA,GAAAH,SAAA,CAAAG,UAAA,EACAvF,EAAA,GAAAoF,SAAA,CAAApF,EAAA,EACAwF,MAAA,GAAAJ,SAAA,CAAAI,MAAA,EACAzH,cAAA,GAAAqH,SAAA,CAAArH,cAAA,EACA0H,WAAA,GAAAL,SAAA,CAAAK,WAAA;wBAEAZ,MAAA,CAAA1I,UAAA,GAAAoJ,UAAA;wBACA,IAAAA,UAAA,CAAA5E,MAAA;0BAAA+E,iBAAA,GACAH,UAAA,CAAAS,KAAA,MAAAL,kBAAA,GAAAM,cAAA,CAAAP,iBAAA,MAAAE,IAAA,GAAAD,kBAAA;0BACAd,MAAA,CAAAjJ,SAAA,GAAA7B,OAAA,EAAA6L,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAApI,GAAA,QAAA0I,MAAA;wBACA;wBACA,IAAAV,MAAA;0BACAK,GAAA,GAAAhB,MAAA,CAAArI,OAAA,CAAA2J,SAAA,WAAApG,IAAA;4BAAA,OAAAA,IAAA,CAAA8D,IAAA;0BAAA;0BACA,IAAAgC,GAAA;4BACAhB,MAAA,CAAArI,OAAA,CAAA4J,MAAA,CAAAP,GAAA;0BACA;wBACA;wBAEA,IAAA9H,cAAA,aAAAA,cAAA,eAAAA,cAAA,CAAA4C,MAAA;0BACA5C,cAAA,CAAAsI,OAAA,WAAAC,OAAA,EAAAT,GAAA;4BACA,IAAAU,GAAA;8BACA5L,IAAA,EAAA2L,OAAA,CAAAE,SAAA;8BACAC,GAAA,EAAAH,OAAA,CAAAI;4BACA;4BACA7B,MAAA,CAAAzI,QAAA,CAAAgI,IAAA,CAAAmC,GAAA;4BACA1B,MAAA,CAAA3H,IAAA,CAAAa,cAAA,CAAAqG,IAAA;8BACAsC,QAAA,EAAAJ,OAAA,CAAAI,QAAA;8BACAF,SAAA,EAAAF,OAAA,CAAAE;4BACA;0BACA;wBACA;wBAAAT,SAAA,CAAAtE,IAAA;wBAAA,OACAoD,MAAA,CAAA8B,WAAA,CAAAxJ,cAAA;sBAAA;wBAAA4I,SAAA,CAAAtE,IAAA;wBAAA,OACAoD,MAAA,CAAA+B,sBAAA,CAAAxJ,OAAA;sBAAA;wBAEAyJ,MAAA,CAAAC,MAAA,CAAAjC,MAAA,CAAA3H,IAAA,EAAA0H,aAAA,CAAAA,aAAA,KACA/B,GAAA,CAAAa,IAAA;0BACAvG,cAAA,EAAAA,cAAA;0BACAC,OAAA,EAAAA,OAAA;0BACAiI,eAAA,EAAAA,eAAA;0BACArF,EAAA,EAAAA,EAAA;0BACA3C,eAAA,EAAAA,eAAA,UAAAA,eAAA,gBAAAA,eAAA,CAAA0J,KAAA,QAAA1J,eAAA;0BACAC,aAAA,EAAAA,aAAA;0BACAC,WAAA,EAAAA,WAAA;0BACAC,GAAA,EAAAA,GAAA,IAAAC,SAAA;0BACAuJ,IAAA,EAAAtJ,KAAA,IAAAD,SAAA;0BACAE,OAAA,EAAAsJ,MAAA,CAAAtJ,OAAA;0BACAC,WAAA,EAAAA,WAAA;0BACAC,YAAA,EAAAA,YAAA;0BACAC,MAAA,EAAAA;wBAAA,EACA;wBAEA+G,MAAA,CAAAqC,SAAA,CAAAzB,WAAA;wBACAZ,MAAA,CAAAlJ,QAAA,GAAA0J,eAAA;wBACAR,MAAA,CAAApI,YAAA,GAAA6I,WAAA,aAAAA,WAAA;wBACA;wBACA;wBACA;wBACA;wBAAAS,SAAA,CAAAtE,IAAA;wBAAA;sBAAA;wBAEAoD,MAAA,CAAAsC,QAAA;0BACAnI,OAAA,EAAA6D,GAAA,CAAAuE,OAAA;0BACA3H,IAAA;wBACA;sBAAA;wBAEAoF,MAAA,CAAAhJ,WAAA;sBAAA;sBAAA;wBAAA,OAAAkK,SAAA,CAAAtD,IAAA;oBAAA;kBAAA,GAAA0C,QAAA;gBAAA,CACA;gBAAA,iBAAAkC,EAAA;kBAAA,OAAAnC,IAAA,CAAAb,KAAA,OAAAiD,SAAA;gBAAA;cAAA;YAAA;YAAA;cAAA,OAAAtC,SAAA,CAAAvC,IAAA;UAAA;QAAA,GAAAqC,QAAA;MAAA;IACA;IACAyC,aAAA,WAAAA,cAAA;MACA,KAAArL,MAAA;MACA,KAAAsL,WAAA;IACA;IAEAC,YAAA,WAAAA,aAAAC,KAAA,EAAAtL,QAAA;MACA,KAAA+K,QAAA,CAAAQ,OAAA,kGAAAC,MAAA,CAAAF,KAAA,CAAA/G,MAAA,wDAAAiH,MAAA,CAAAF,KAAA,CAAA/G,MAAA,GAAAvE,QAAA,CAAAuE,MAAA;IACA;IACAkH,cAAA,WAAAA,eAAAC,KAAA,EAAAJ,KAAA,EAAAtL,QAAA,GACA;IACA2L,WAAA,WAAAA,YAAAC,GAAA,EAAAN,KAAA,EAAAtL,QAAA;MACA6L,OAAA,CAAAC,GAAA,SAAAF,GAAA,EAAAN,KAAA,EAAAtL,QAAA;MACA,KAAA+L,cAAA,CAAA/L,QAAA;IACA;IACA+L,cAAA,WAAAA,eAAA/L,QAAA;MACA,IAAAgM,IAAA,GAAAhM,QAAA,CAAAiM,KAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,MAAA;MAAA;MACAH,IAAA,UAAApM,aAAA;IACA;IACAwM,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAlI,KAAA,SAAAmI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAV,OAAA,CAAAC,GAAA,UAAAS,KAAA;UACAF,MAAA,CAAAlI,KAAA,WAAAqI,UAAA;QACA;UACAX,OAAA,CAAAC,GAAA;UACA;QACA;MACA;IACA;IACAW,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,KAAAvI,KAAA,SAAAmI,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAG,MAAA,CAAAtN,KAAA;UACAsN,MAAA,CAAArN,KAAA;UACAqN,MAAA,CAAApN,gBAAA;UACAoN,MAAA,CAAAvN,aAAA;UACAuN,MAAA,CAAAC,SAAA,WAAAC,CAAA;YACAF,MAAA,CAAAvI,KAAA,YAAAqI,UAAA,CAAAE,MAAA,CAAA5L,IAAA,EAAA4L,MAAA,CAAA5M,MAAA;UACA;QACA;UACA+L,OAAA,CAAAC,GAAA;UACA;QACA;MACA;IACA;IACAU,UAAA,WAAAA,WAAAK,GAAA;MACA,KAAA1I,KAAA,iBAAAqI,UAAA,CAAAK,GAAA;IACA;IACA/G,cAAA,WAAAA,eAAA;MAAA,IAAAgH,MAAA;MACAnQ,kBAAA;QAAAoQ,QAAA;QAAApO,kBAAA,OAAAA;MAAA,GAAAkK,IAAA,WAAApC,GAAA;QACA,IAAAA,GAAA,CAAAU,SAAA;UACA2F,MAAA,CAAAxK,WAAA,GAAAmE,GAAA,CAAAa,IAAA,CAAAA,IAAA;QACA;MACA;IACA;IACA0F,eAAA,WAAAA,gBAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,KAAAD,IAAA;MAEAA,IAAA,CAAAhD,OAAA,WAAAC,OAAA;QACA,IAAAiD,QAAA,GAAAjD,OAAA,CAAAiD,QAAA;QACA,IAAAA,QAAA,IAAAA,QAAA,CAAA5I,MAAA;UACA2F,OAAA,CAAAjI,QAAA;QACA;UACAiI,OAAA,CAAAjI,QAAA;UACAiL,MAAA,CAAAF,eAAA,CAAAG,QAAA;QACA;MACA;IACA;IACA5C,WAAA,WAAAA,YAAA6C,GAAA;MAAA,IAAAC,MAAA;MAAA,OAAA3I,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA0I,SAAA;QAAA,OAAA3I,mBAAA,GAAAM,IAAA,UAAAsI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApI,IAAA,GAAAoI,SAAA,CAAAnI,IAAA;YAAA;cAAAmI,SAAA,CAAAnI,IAAA;cAAA,OACA5I,WAAA;gBACAgR,YAAA,EAAAL,GAAA;gBACAzO,kBAAA,EAAA0O,MAAA,CAAA1O;cACA,GAAAkK,IAAA,WAAApC,GAAA;gBACA,IAAAA,GAAA,CAAAU,SAAA;kBACA,IAAAuG,IAAA,GAAAjH,GAAA,CAAAa,IAAA;kBACA+F,MAAA,CAAAL,eAAA,CAAAU,IAAA;kBACAL,MAAA,CAAA5K,cAAA,CAAA/D,IAAA,GAAA+H,GAAA,CAAAa,IAAA;kBACA+F,MAAA,CAAAV,SAAA,WAAAC,CAAA;oBACAS,MAAA,CAAAlJ,KAAA,CAAAwJ,cAAA,CAAAC,iBAAA,CAAAnH,GAAA,CAAAa,IAAA;kBACA;gBACA;kBACA+F,MAAA,CAAAtC,QAAA;oBACAnI,OAAA,EAAA6D,GAAA,CAAAuE,OAAA;oBACA3H,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAmK,SAAA,CAAAnH,IAAA;UAAA;QAAA,GAAAiH,QAAA;MAAA;IACA;IACA9C,sBAAA,WAAAA,uBAAAqD,MAAA;MAAA,IAAAC,MAAA;MAAA,OAAApJ,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAmJ,SAAA;QAAA,OAAApJ,mBAAA,GAAAM,IAAA,UAAA+I,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7I,IAAA,GAAA6I,SAAA,CAAA5I,IAAA;YAAA;cAAA4I,SAAA,CAAA5I,IAAA;cAAA,OACA3I,wBAAA;gBACAsE,OAAA,EAAA6M,MAAA;gBACAK,IAAA;gBACAnB,QAAA;cACA,GAAAlE,IAAA,WAAApC,GAAA;gBACA,IAAAA,GAAA,CAAAU,SAAA;kBACA2G,MAAA,CAAAtL,eAAA,GAAAiE,GAAA,CAAAa,IAAA;kBACA,IAAAwG,MAAA,CAAAtL,eAAA,CAAA+B,MAAA;oBACAuJ,MAAA,CAAApL,KAAA,CAAAzB,eAAA,IAAA0B,QAAA;kBACA;oBACAmL,MAAA,CAAApL,KAAA,CAAAzB,eAAA,IAAA0B,QAAA;kBACA;gBACA;kBACAmL,MAAA,CAAA/C,QAAA;oBACAnI,OAAA,EAAA6D,GAAA,CAAAuE,OAAA;oBACA3H,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA4K,SAAA,CAAA5H,IAAA;UAAA;QAAA,GAAA0H,QAAA;MAAA;IACA;IACAI,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MAAA,OAAA1J,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAyJ,SAAA;QAAA,IAAAtN,cAAA;QAAA,OAAA4D,mBAAA,GAAAM,IAAA,UAAAqJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnJ,IAAA,GAAAmJ,SAAA,CAAAlJ,IAAA;YAAA;cACAtE,cAAA,GAAAqN,MAAA,CAAAtN,IAAA,CAAAC,cAAA;cACAqN,MAAA,CAAAI,OAAA;cACAJ,MAAA,CAAAtN,IAAA,CAAAE,OAAA;cACAoN,MAAA,CAAAtN,IAAA,CAAAG,eAAA;cACAmN,MAAA,CAAA3L,cAAA,CAAA/D,IAAA;cACA0P,MAAA,CAAAzB,SAAA,WAAAC,CAAA;gBACAwB,MAAA,CAAAjK,KAAA,CAAAwJ,cAAA,CAAAC,iBAAA;cACA;cAAA,KACA7M,cAAA;gBAAAwN,SAAA,CAAAlJ,IAAA;gBAAA;cAAA;cAAAkJ,SAAA,CAAAlJ,IAAA;cAAA,OACA+I,MAAA,CAAA7D,WAAA,CAAAxJ,cAAA;YAAA;YAAA;cAAA,OAAAwN,SAAA,CAAAlI,IAAA;UAAA;QAAA,GAAAgI,QAAA;MAAA;IAEA;IACAI,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MAAA,OAAAhK,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA+J,SAAA;QAAA,OAAAhK,mBAAA,GAAAM,IAAA,UAAA2J,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzJ,IAAA,GAAAyJ,SAAA,CAAAxJ,IAAA;YAAA;cACAqJ,OAAA,CAAAF,OAAA;cAAAK,SAAA,CAAAxJ,IAAA;cAAA,OACAqJ,OAAA,CAAAlE,sBAAA,CAAAkE,OAAA,CAAA5N,IAAA,CAAAE,OAAA;YAAA;cACA,IAAA0N,OAAA,CAAAlM,eAAA,CAAA+B,MAAA,IAAAmK,OAAA,CAAA5N,IAAA,CAAAE,OAAA;gBACA0N,OAAA,CAAA5N,IAAA,CAAAG,eAAA,IAAAyN,OAAA,CAAAlM,eAAA,IAAAoB,EAAA;gBACA8K,OAAA,CAAAhM,KAAA,CAAAzB,eAAA,IAAA0B,QAAA;cACA;gBACA+L,OAAA,CAAAhM,KAAA,CAAAzB,eAAA,IAAA0B,QAAA;gBACA+L,OAAA,CAAAvK,KAAA,CAAAC,cAAA,CAAAC,aAAA;cACA;YAAA;YAAA;cAAA,OAAAwK,SAAA,CAAAxI,IAAA;UAAA;QAAA,GAAAsI,QAAA;MAAA;IACA;IACAG,SAAA,WAAAA,UAAA;MACA,KAAAhO,IAAA,CAAAE,OAAA;MACA,KAAAF,IAAA,CAAAG,eAAA;MACA,KAAAuN,OAAA;IACA;IACAO,WAAA,WAAAA,YAAA;MACA,KAAA5P,aAAA;IACA;IACA4G,gBAAA,WAAAA,iBAAA;MAAA,IAAAiJ,OAAA;MACApS,oBAAA;QAAA+B,kBAAA,OAAAA;MAAA,GAAAkK,IAAA,WAAApC,GAAA;QACA,IAAAA,GAAA,CAAAU,SAAA;UACA6H,OAAA,CAAA3M,UAAA,GAAAoI,MAAA,CAAAwE,MAAA,CAAAxI,GAAA,CAAAa,IAAA;UAEA,IAAA4H,KAAA,GAAA1J,YAAA,CAAAC,OAAA;UACA,IAAA0J,GAAA,GAAAH,OAAA,CAAA3M,UAAA,CAAAqB,IAAA,WAAAwI,CAAA;YAAA,OAAAA,CAAA,CAAAtI,EAAA,KAAAsL,KAAA;UAAA;UACA,IAAAC,GAAA;YACAH,OAAA,CAAAlO,IAAA,CAAAI,aAAA,GAAAiO,GAAA,CAAAvL,EAAA;UACA;QACA;UACAoL,OAAA,CAAAjE,QAAA;YACAnI,OAAA,EAAA6D,GAAA,CAAAuE,OAAA;YACA3H,IAAA;UACA;QACA;MACA;IACA;IACA4C,wBAAA,WAAAA,yBAAA;MAAA,IAAAmJ,OAAA;MACAtS,mBAAA;QAAA6B,kBAAA,OAAAA;MAAA,GAAAkK,IAAA,WAAApC,GAAA;QACA,IAAAA,GAAA,CAAAU,SAAA;UACAiI,OAAA,CAAA7M,cAAA,GAAAkE,GAAA,CAAAa,IAAA;QACA;UACA8H,OAAA,CAAArE,QAAA;YACAnI,OAAA,EAAA6D,GAAA,CAAAuE,OAAA;YACA3H,IAAA;UACA;QACA;MACA;IACA;IACA2C,UAAA,WAAAA,WAAA;MAAA,IAAAqJ,OAAA;MAAA,OAAA3K,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA0K,SAAA;QAAA,IAAAC,gBAAA,EAAAC,OAAA,EAAAC,KAAA;QAAA,OAAA9K,mBAAA,GAAAM,IAAA,UAAAyK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvK,IAAA,GAAAuK,SAAA,CAAAtK,IAAA;YAAA;cACAkK,gBAAA;gBAAA,IAAAK,KAAA,GAAAlL,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAiL,SAAA;kBAAA,OAAAlL,mBAAA,GAAAM,IAAA,UAAA6K,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAA3K,IAAA,GAAA2K,SAAA,CAAA1K,IAAA;sBAAA;wBAAA0K,SAAA,CAAA1K,IAAA;wBAAA,OACAhH,aAAA;0BAAAM,kBAAA,EAAA0Q,OAAA,CAAA1Q;wBAAA,GAAAkK,IAAA,WAAApC,GAAA;0BACA,IAAAA,GAAA,CAAAU,SAAA;4BAAA,IAAA6I,UAAA;4BACA,OAAAvJ,GAAA,aAAAA,GAAA,gBAAAuJ,UAAA,GAAAvJ,GAAA,CAAAa,IAAA,iBAAA0I,UAAA,uBAAAA,UAAA,CAAAC,OAAA;0BACA;4BACAZ,OAAA,CAAAtE,QAAA;8BACAnI,OAAA,EAAA6D,GAAA,CAAAuE,OAAA;8BACA3H,IAAA;4BACA;0BACA;wBACA;sBAAA;wBAAA,OAAA0M,SAAA,CAAAG,MAAA,WAAAH,SAAA,CAAA7I,IAAA;sBAAA;sBAAA;wBAAA,OAAA6I,SAAA,CAAA1J,IAAA;oBAAA;kBAAA,GAAAwJ,QAAA;gBAAA,CACA;gBAAA,gBAXAN,iBAAA;kBAAA,OAAAK,KAAA,CAAA3H,KAAA,OAAAiD,SAAA;gBAAA;cAAA;cAaAsE,OAAA;gBAAA,IAAAW,KAAA,GAAAzL,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAwL,SAAAX,KAAA;kBAAA,OAAA9K,mBAAA,GAAAM,IAAA,UAAAoL,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAAlL,IAAA,GAAAkL,SAAA,CAAAjL,IAAA;sBAAA;wBAAAiL,SAAA,CAAAjL,IAAA;wBAAA,OACAnI,oBAAA,KAAA2L,IAAA,WAAApC,GAAA;0BACA,IAAAA,GAAA,CAAAU,SAAA;4BAAA,IAAAoJ,UAAA;4BACA,IAAAC,MAAA,IAAAD,UAAA,GAAA9J,GAAA,CAAAa,IAAA,cAAAiJ,UAAA,uBAAAA,UAAA;4BACA,IAAAC,MAAA,CAAArD,QAAA,CAAA5I,MAAA;8BACA,IAAAmJ,IAAA,GAAA8C,MAAA,CAAArD,QAAA,CAAA5F,MAAA,WAAA2E,CAAA;gCAAA,OAAAA,CAAA,CAAAtI,EAAA,KAAA6L,KAAA;8BAAA;8BAEA,IAAAgB,iBAAA,YAAAA,iBAAAC,SAAA;gCACAA,SAAA,CAAAvI,GAAA,WAAA+B,OAAA;kCACA,IAAAA,OAAA,CAAAiD,QAAA,IAAAjD,OAAA,CAAAiD,QAAA,CAAA5I,MAAA;oCACA2F,OAAA,CAAAjI,QAAA;oCACAwO,iBAAA,CAAAvG,OAAA,CAAAiD,QAAA;kCACA;gCACA;8BACA;8BACAsD,iBAAA,CAAA/C,IAAA;8BACA2B,OAAA,CAAAlL,KAAA,CAAAwM,UAAA,CAAA/C,iBAAA,CAAAF,IAAA;4BACA;0BACA;4BACA2B,OAAA,CAAAtE,QAAA;8BACAnI,OAAA,EAAA6D,GAAA,CAAAuE,OAAA;8BACA3H,IAAA;4BACA;0BACA;wBACA;sBAAA;sBAAA;wBAAA,OAAAiN,SAAA,CAAAjK,IAAA;oBAAA;kBAAA,GAAA+J,QAAA;gBAAA,CACA;gBAAA,gBAzBAZ,QAAAoB,GAAA;kBAAA,OAAAT,KAAA,CAAAlI,KAAA,OAAAiD,SAAA;gBAAA;cAAA;cAAAyE,SAAA,CAAAtK,IAAA;cAAA,OA0BAkK,gBAAA;YAAA;cAAAE,KAAA,GAAAE,SAAA,CAAAzI,IAAA;cAAAyI,SAAA,CAAAtK,IAAA;cAAA,OACAmK,OAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAAE,SAAA,CAAAtJ,IAAA;UAAA;QAAA,GAAAiJ,QAAA;MAAA;IACA;IACAuB,YAAA,WAAAA,aAAAC,OAAA;MAAA,IAAAC,OAAA;MACA,KAAAlR,SAAA;MACA,IAAAiB,IAAA,GAAA0H,aAAA,UAAA1H,IAAA;MACA/D,eAAA,CAAAyL,aAAA,CAAAA,aAAA,KACA1H,IAAA;QACAkQ,UAAA,OAAA3Q,YAAA;QACAY,eAAA,OAAAH,IAAA,CAAAG,eAAA,CAAAgQ,QAAA;QACAtP,cAAA,GAAAmP,OAAA;MAAA,EACA,EAAAjI,IAAA,WAAApC,GAAA;QACA,IAAAA,GAAA,CAAAU,SAAA;UACA4J,OAAA,CAAAG,YAAA;UACA,IAAAC,UAAA,GAAA1K,GAAA,CAAAa,IAAA;YAAA3F,cAAA,GAAAwP,UAAA,CAAAxP,cAAA;YAAAyP,kBAAA,GAAAD,UAAA,CAAAC,kBAAA;UACA;UACA;UACA,IAAAzP,cAAA,CAAA4C,MAAA;YACAwM,OAAA,CAAAxR,QAAA,GAAAoC,cAAA,IAAA2I,QAAA;YACAyG,OAAA,CAAAjQ,IAAA,CAAAmI,eAAA,GAAA8H,OAAA,CAAAxR,QAAA;YACAwR,OAAA,CAAAjQ,IAAA,CAAAuQ,oBAAA,IAAAP,OAAA;UACA;UACAC,OAAA,CAAAjG,SAAA,CAAAsG,kBAAA;QACA;UACA,IAAA3K,GAAA,CAAAa,IAAA,IAAAb,GAAA,CAAAa,IAAA,CAAAgK,YAAA;YACAC,MAAA,CAAAC,IAAA,CAAAjU,UAAA,CAAAwT,OAAA,CAAAU,QAAA,EAAAhL,GAAA,CAAAa,IAAA,CAAAgK,YAAA;UACA;UACAP,OAAA,CAAAhG,QAAA;YACAnI,OAAA,EAAA6D,GAAA,CAAAuE,OAAA;YACA3H,IAAA;UACA;QACA;QACA0N,OAAA,CAAAlR,SAAA;MACA;IACA;IACA6R,YAAA,WAAAA,aAAA7E,GAAA;MACA;QACA8E,cAAA,EAAAhU,OAAA,CAAAkP,GAAA,CAAA+E,WAAA,EAAAC,QAAA,CAAAhF,GAAA,CAAAiF,WAAA,EAAAhI,MAAA;MACA;IACA;IACAgB,SAAA,WAAAA,UAAAiH,IAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,OAAA,YAAAA,QAAAF,IAAA,EAAApO,IAAA;QAAA,IAAAuO,YAAA,GAAAhH,SAAA,CAAA3G,MAAA,QAAA2G,SAAA,QAAA7J,SAAA,GAAA6J,SAAA;QACA,IAAAiH,WAAA,GAAA3J,aAAA,CAAAA,aAAA,KACA7E,IAAA;UACAyO,QAAA,EAAAzO,IAAA,CAAA0O,IAAA,aAAA1O,IAAA,CAAA0O,IAAA;UACAC,gBAAA,EAAA3O,IAAA,CAAAC,EAAA;UACA2O,IAAA,EAAA5O,IAAA,CAAA6O,WAAA;UACAC,OAAA;UACAC,aAAA,EAAA/O,IAAA,CAAAgP,OAAA;UACA5U,UAAA,EAAAC,iBAAA,CAAA2F,IAAA,CAAAgP,OAAA;QAAA,GACAX,OAAA,CAAAN,YAAA,CAAA/N,IAAA,EACA;QACAqO,OAAA,CAAAY,iBAAA,CAAAT,WAAA,EAAAD,YAAA;QACA,IAAAC,WAAA,CAAApU,UAAA;UACA,IAAA8U,aAAA,GAAAb,OAAA,CAAAc,cAAA,CAAAX,WAAA,EAAAJ,IAAA;UACA,IAAAc,aAAA,CAAAtO,MAAA;YACAsO,aAAA,CAAA5I,OAAA,WAAA8I,SAAA;cACAA,SAAA,CAAAC,UAAA;YACA;UACA;QACA;QACA,OAAAb,WAAA;MACA;MAEA,KAAAc,aAAA,GAAAlB,IAAA,CAAA5J,GAAA,WAAAxE,IAAA;QAAA,OAAAsO,OAAA,CAAAF,IAAA,EAAApO,IAAA;MAAA;MAEA,KAAA7D,MAAA,GAAAiS,IAAA,CAAA5J,GAAA,WAAAxE,IAAA;QAAA,OAAAsO,OAAA,CAAAF,IAAA,EAAApO,IAAA;MAAA;IACA;IACAiP,iBAAA,WAAAA,kBAAAjP,IAAA;MAAA,IAAAuO,YAAA,GAAAhH,SAAA,CAAA3G,MAAA,QAAA2G,SAAA,QAAA7J,SAAA,GAAA6J,SAAA;MACA,IAAAvH,IAAA,CAAAuP,UAAA;QACA,IAAAC,WAAA,GAAA7N,IAAA,CAAAC,KAAA,CAAA5B,IAAA,CAAAuP,UAAA;QACA,IAAA/U,OAAA,CAAAgV,WAAA,KAAAA,WAAA,CAAA5O,MAAA;UACA4O,WAAA,GAAAA,WAAA,CAAA5L,MAAA,WAAA6L,CAAA;YAAA,OAAAA,CAAA,CAAAC,eAAA;UAAA;UACA,IAAAC,KAAA,GAAAH,WAAA,CAAAhL,GAAA,WAAAiL,CAAA;YACA,IAAAG,MAAA,GAAAtV,iBAAA,CAAA0F,IAAA,CAAAyO,QAAA;YACA,IAAAjD,GAAA,GAAAoE,MAAA,CAAA7P,IAAA,WAAAwI,CAAA;cAAA,OAAAA,CAAA,CAAAzE,IAAA,KAAA2L,CAAA,CAAAC,eAAA;YAAA;YACA1P,IAAA,CAAAyP,CAAA,CAAAC,eAAA,IAAAnB,YAAA,GAAAkB,CAAA,CAAAI,UAAA,GAAAJ,CAAA,CAAAK,WAAA;YAEA;cACAC,UAAA,GAAAvE,GAAA,aAAAA,GAAA,uBAAAA,GAAA,CAAAuE,UAAA;cACAC,WAAA,GAAAxE,GAAA,aAAAA,GAAA,uBAAAA,GAAA,CAAAwE,WAAA;cACAlM,IAAA,EAAA2L,CAAA,CAAAC,eAAA;cACAO,IAAA,EAAAR,CAAA,CAAAS,eAAA;cACAC,KAAA,EAAAV,CAAA,CAAAK,WAAA;cACAM,QAAA,EAAAX,CAAA,CAAAI;YACA;UACA;UACA,KAAA5N,MAAA,CAAAC,QAAA;YAAA0M,IAAA,EAAA5O,IAAA,CAAA4O,IAAA;YAAAR,IAAA,EAAAuB;UAAA;QACA;MACA;IACA;IACAU,YAAA,WAAAA,aAAAC,IAAA;MACA,KAAArU,aAAA;IACA;IACAsU,YAAA,WAAAA,aAAAD,IAAA,EAAAjU,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;IACA;IACAmU,aAAA,WAAAA,cAAAF,IAAA;MAAA,OAAAvP,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAwP,UAAA;QAAA,IAAAC,GAAA,EAAAC,KAAA,EAAAC,gBAAA,EAAAjN,IAAA;QAAA,OAAA3C,mBAAA,GAAAM,IAAA,UAAAuP,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAArP,IAAA,GAAAqP,UAAA,CAAApP,IAAA;YAAA;cACAwG,OAAA,CAAAC,GAAA,CAAAmI,IAAA;cACAI,GAAA,GAAAJ,IAAA,CAAA1V,IAAA,CAAAoM,KAAA;cACA2J,KAAA,GAAAD,GAAA,CAAAA,GAAA,CAAA9P,MAAA;cAAAkQ,UAAA,CAAApP,IAAA;cAAA,OACAlI,SAAA;gBAAAkN,GAAA,EAAA4J,IAAA,CAAA5J;cAAA;YAAA;cAAAkK,gBAAA,GAAAE,UAAA,CAAAvN,IAAA;cAAAI,IAAA,GAAAiN,gBAAA,CAAAjN,IAAA;cACA,IAAAgN,KAAA;gBACA/C,MAAA,CAAAC,IAAA,0CAAAlK,IAAA;cACA;gBACAiK,MAAA,CAAAC,IAAA,CAAAlK,IAAA;cACA;YAAA;YAAA;cAAA,OAAAmN,UAAA,CAAApO,IAAA;UAAA;QAAA,GAAA+N,SAAA;MAAA;IACA;IACAM,YAAA,WAAAA,aAAAT,IAAA,EAAAjU,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;MACA,KAAA+L,cAAA,CAAA/L,QAAA;MACA,KAAAc,IAAA,CAAAa,cAAA,QAAA3B,QAAA,CAAAmI,GAAA,WAAAxE,IAAA;QACA,IAAAA,IAAA,CAAA0G,GAAA;UACA;YACAC,QAAA,EAAA3G,IAAA,CAAA0G,GAAA;YACAD,SAAA,EAAAzG,IAAA,CAAApF;UACA;QACA;UACA,IAAA8L,GAAA,GAAA1G,IAAA,CAAAgR,QAAA,CAAArN,IAAA;UACA,IAAAsN,QAAA,GAAAvK,GAAA,CAAAM,KAAA;UACA,IAAAmG,OAAA;YACAxG,QAAA,EAAAsK,QAAA;YACAC,SAAA,EAAAD,QAAA;YACAE,SAAA,EAAAF,QAAA;YACAxK,SAAA,EAAAwK,QAAA;UACA;UACA;YACAtK,QAAA,EAAAwG,OAAA,CAAAxG,QAAA;YACAF,SAAA,EAAA0G,OAAA,CAAA1G;UACA;QACA;MACA;IACA;IACA2K,aAAA,WAAAA,cAAAJ,QAAA,EAAAV,IAAA,EAAAjU,QAAA;MACA,KAAA2U,QAAA,KAAAA,QAAA,CAAArN,IAAA;QACA;MACA;MACA,KAAAyE,cAAA,CAAA/L,QAAA;MACA,KAAAc,IAAA,CAAAa,cAAA,QAAA3B,QAAA,CAAAmI,GAAA,WAAAxE,IAAA;QACA,IAAAA,IAAA,CAAA0G,GAAA;UACA;YACAC,QAAA,EAAA3G,IAAA,CAAA0G,GAAA;YACAD,SAAA,EAAAzG,IAAA,CAAApF;UACA;QACA;UACA,IAAAoF,IAAA,CAAAwI,MAAA;UACA,IAAA9B,GAAA,GAAA1G,IAAA,CAAAgR,QAAA,CAAArN,IAAA;UACA,IAAAsN,QAAA,GAAAvK,GAAA,CAAAM,KAAA;UACA,IAAAmG,OAAA;YACAxG,QAAA,EAAAsK,QAAA;YACAC,SAAA,EAAAD,QAAA;YACAE,SAAA,EAAAF,QAAA;YACAxK,SAAA,EAAAwK,QAAA;UACA;UACA;YACAtK,QAAA,EAAAwG,OAAA,CAAAxG,QAAA;YACAF,SAAA,EAAA0G,OAAA,CAAA1G;UACA;QACA;MACA;IACA;IAEA4K,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,KAAA9Q,KAAA,SAAAmI,QAAA;QAAA,IAAA4I,KAAA,GAAAxQ,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAuQ,UAAA5I,KAAA;UAAA,OAAA5H,mBAAA,GAAAM,IAAA,UAAAmQ,WAAAC,UAAA;YAAA,kBAAAA,UAAA,CAAAjQ,IAAA,GAAAiQ,UAAA,CAAAhQ,IAAA;cAAA;gBAAA,KACAkH,KAAA;kBAAA8I,UAAA,CAAAhQ,IAAA;kBAAA;gBAAA;gBACA4P,OAAA,CAAAvV,WAAA;gBAAA2V,UAAA,CAAAhQ,IAAA;gBAAA,OACA4P,OAAA,CAAAK,MAAA;cAAA;gBACAL,OAAA,CAAAvV,WAAA;gBAAA2V,UAAA,CAAAhQ,IAAA;gBAAA;cAAA;gBAEAwG,OAAA,CAAAC,GAAA;gBAAA,OAAAuJ,UAAA,CAAAnF,MAAA,WACA;cAAA;cAAA;gBAAA,OAAAmF,UAAA,CAAAhP,IAAA;YAAA;UAAA,GAAA8O,SAAA;QAAA,CAEA;QAAA,iBAAAI,GAAA;UAAA,OAAAL,KAAA,CAAAjN,KAAA,OAAAiD,SAAA;QAAA;MAAA;IACA;IACAsK,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,OAAA/Q,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA8Q,UAAA;QAAA,OAAA/Q,mBAAA,GAAAM,IAAA,UAAA0Q,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAxQ,IAAA,GAAAwQ,UAAA,CAAAvQ,IAAA;YAAA;cACAoQ,OAAA,CAAAtR,KAAA,SAAAmI,QAAA;gBAAA,IAAAuJ,KAAA,GAAAnR,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkR,UAAAvJ,KAAA;kBAAA,OAAA5H,mBAAA,GAAAM,IAAA,UAAA8Q,WAAAC,UAAA;oBAAA,kBAAAA,UAAA,CAAA5Q,IAAA,GAAA4Q,UAAA,CAAA3Q,IAAA;sBAAA;wBAAA,KACAkH,KAAA;0BAAAyJ,UAAA,CAAA3Q,IAAA;0BAAA;wBAAA;wBACAoQ,OAAA,CAAA9V,aAAA;wBAAAqW,UAAA,CAAA3Q,IAAA;wBAAA,OACAoQ,OAAA,CAAAH,MAAA;sBAAA;wBACAG,OAAA,CAAA9V,aAAA;wBAAAqW,UAAA,CAAA3Q,IAAA;wBAAA;sBAAA;wBAEAwG,OAAA,CAAAC,GAAA;wBAAA,OAAAkK,UAAA,CAAA9F,MAAA,WACA;sBAAA;sBAAA;wBAAA,OAAA8F,UAAA,CAAA3P,IAAA;oBAAA;kBAAA,GAAAyP,SAAA;gBAAA,CAEA;gBAAA,iBAAAG,GAAA;kBAAA,OAAAJ,KAAA,CAAA5N,KAAA,OAAAiD,SAAA;gBAAA;cAAA;YAAA;YAAA;cAAA,OAAA0K,UAAA,CAAAvP,IAAA;UAAA;QAAA,GAAAqP,SAAA;MAAA;IACA;IACAJ,MAAA,WAAAA,OAAAY,OAAA;MAAA,IAAAC,OAAA;MAAA,OAAAzR,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAwR,UAAA;QAAA,IAAAC,qBAAA;QAAA,IAAAC,KAAA,EAAAC,QAAA,EAAAC,OAAA,EAAAC,MAAA;QAAA,OAAA9R,mBAAA,GAAAM,IAAA,UAAAyR,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAvR,IAAA,GAAAuR,UAAA,CAAAtR,IAAA;YAAA;cACAwG,OAAA,CAAAC,GAAA,cAAAqK,OAAA,CAAArV,IAAA;cACAwV,KAAA,GAAA9N,aAAA,KAAA2N,OAAA,CAAArV,IAAA;cACAyV,QAAA;cACA,IAAAJ,OAAA,CAAA9V,YAAA;gBACAkW,QAAA,GAAAJ,OAAA,CAAArW,MAAA,CAAAqI,GAAA,WAAAxE,IAAA;kBACA,IAAAzB,QAAA,GAAAyB,IAAA,CAAAzB,QAAA;oBAAAqQ,IAAA,GAAA5O,IAAA,CAAA4O,IAAA;oBAAAG,aAAA,GAAA/O,IAAA,CAAA+O,aAAA;oBAAAD,OAAA,GAAA9O,IAAA,CAAA8O,OAAA;oBAAA1U,UAAA,GAAA4F,IAAA,CAAA5F,UAAA;oBAAA6Y,MAAA,GAAAjT,IAAA,CAAAiT,MAAA;oBAAAC,MAAA,GAAAC,wBAAA,CAAAnT,IAAA,EAAAoT,SAAA;kBACA,IAAAC,SAAA,GAAAb,OAAA,CAAAvQ,MAAA,CAAAqR,KAAA,CAAAC,WAAA,CAAAC,UAAA;kBACA,IAAA7D,KAAA,IAAA0D,SAAA,CAAAzE,IAAA,SAAApK,GAAA,WAAA+D,CAAA;oBACA2K,MAAA,CAAA3K,CAAA,CAAAzE,IAAA,IAAAyE,CAAA,CAAA6H,QAAA;oBACA;sBACAV,eAAA,EAAAnH,CAAA,CAAAzE,IAAA;sBACAoM,eAAA,EAAA3H,CAAA,CAAA0H,IAAA;sBACAH,WAAA,EAAAvH,CAAA,CAAA4H,KAAA;sBACAN,UAAA,EAAAtH,CAAA,CAAA6H;oBACA;kBACA;kBACA8C,MAAA,CAAA3D,UAAA,GAAA5N,IAAA,CAAA+B,SAAA,CAAAiM,KAAA;kBACAuD,MAAA,CAAAlE,OAAA,GAAAD,aAAA;kBACA,OAAAmE,MAAA;gBACA;gBACAhL,OAAA,CAAAC,GAAA,CAAAxG,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAA+B,SAAA,CAAAkP,QAAA;gBACAD,KAAA,CAAArN,eAAA;gBACAqN,KAAA,CAAAjF,oBAAA;cACA;gBACAkF,QAAA,GAAAJ,OAAA,CAAArW,MAAA;cACA;cACA0W,OAAA,GAAAL,OAAA,CAAAjF,YAAA,IAAAiF,OAAA,CAAA9V,YAAA,UAAA8V,OAAA,CAAA7S,MAAA;cACAmT,MAAA,GAAAjO,aAAA,CAAAA,aAAA,KACA8N,KAAA;gBACAc,eAAA,EAAAZ,OAAA;gBACAa,eAAA,EAAA7R,YAAA,CAAAC,OAAA;gBACA6R,aAAA,GAAAjB,qBAAA,GAAAF,OAAA,CAAA5T,cAAA,CAAAmB,IAAA,WAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAAC,EAAA,KAAA0S,KAAA,CAAAnV,WAAA;gBAAA,gBAAAkV,qBAAA,uBAAAA,qBAAA,CAAA3O,YAAA;gBACAwB,WAAA,EAAAiN,OAAA,CAAA9V,YAAA,aAAA8V,OAAA,CAAA9V,YAAA;gBACAY,eAAA,EAAAsW,KAAA,CAAApZ,OAAA,CAAAmY,KAAA,CAAArV,eAAA,IAAAqV,KAAA,CAAArV,eAAA,CAAAuW,IAAA,QAAAlB,KAAA,CAAArV,eAAA;gBACAwW,QAAA,EAAAvB,OAAA;gBACA7M,WAAA,EAAAkN;cAAA;cAEA,IAAAJ,OAAA,CAAA9V,YAAA;gBACAoW,MAAA,CAAAxN,eAAA,GAAAkN,OAAA,CAAA5W,QAAA;cACA;cAAAoX,UAAA,CAAAtR,IAAA;cAAA,OACArI,YAAA,CAAAyZ,MAAA,EAAA5N,IAAA;gBAAA,IAAA6O,KAAA,GAAAhT,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA+S,UAAAlR,GAAA;kBAAA,OAAA9B,mBAAA,GAAAM,IAAA,UAAA2S,WAAAC,UAAA;oBAAA,kBAAAA,UAAA,CAAAzS,IAAA,GAAAyS,UAAA,CAAAxS,IAAA;sBAAA;wBAAA,KACAoB,GAAA,CAAAU,SAAA;0BAAA0Q,UAAA,CAAAxS,IAAA;0BAAA;wBAAA;wBAAA,KACA6Q,OAAA;0BAAA2B,UAAA,CAAAxS,IAAA;0BAAA;wBAAA;wBACA8Q,OAAA,CAAApL,QAAA;0BACAnI,OAAA;0BACAS,IAAA;wBACA;wBACA/F,YAAA,CAAA6Y,OAAA,CAAAvQ,MAAA,EAAAuQ,OAAA,CAAAhT,MAAA;wBAAA0U,UAAA,CAAAxS,IAAA;wBAAA;sBAAA;wBAAA,IAEAoB,GAAA,CAAAa,IAAA;0BAAAuQ,UAAA,CAAAxS,IAAA;0BAAA;wBAAA;wBACA8Q,OAAA,CAAApL,QAAA;0BACAnI,OAAA;0BACAS,IAAA;wBACA;wBAAA,OAAAwU,UAAA,CAAA3H,MAAA;sBAAA;wBAAA2H,UAAA,CAAAxS,IAAA;wBAAA,OAGA8Q,OAAA,CAAA2B,WAAA,CAAArR,GAAA,CAAAa,IAAA;sBAAA;wBAAAuQ,UAAA,CAAAxS,IAAA;wBAAA;sBAAA;wBAGA,IAAAoB,GAAA,CAAAa,IAAA,IAAAb,GAAA,CAAAa,IAAA,CAAAyQ,IAAA;0BACAxG,MAAA,CAAAC,IAAA,CAAAjU,UAAA,CAAA4Y,OAAA,CAAA1E,QAAA,EAAAhL,GAAA,CAAAa,IAAA,CAAAyQ,IAAA;wBACA;wBACA5B,OAAA,CAAApL,QAAA;0BACAnI,OAAA,EAAA6D,GAAA,CAAAuE,OAAA;0BACA3H,IAAA;wBACA;sBAAA;sBAAA;wBAAA,OAAAwU,UAAA,CAAAxR,IAAA;oBAAA;kBAAA,GAAAsR,SAAA;gBAAA,CAEA;gBAAA,iBAAAK,GAAA;kBAAA,OAAAN,KAAA,CAAAzP,KAAA,OAAAiD,SAAA;gBAAA;cAAA;YAAA;YAAA;cAAA,OAAAyL,UAAA,CAAAtQ,IAAA;UAAA;QAAA,GAAA+P,SAAA;MAAA;IACA;IACA0B,WAAA,WAAAA,YAAAlU,EAAA;MAAA,IAAAqU,OAAA;MAAA,OAAAvT,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAsT,UAAA;QAAA,OAAAvT,mBAAA,GAAAM,IAAA,UAAAkT,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAhT,IAAA,GAAAgT,UAAA,CAAA/S,IAAA;YAAA;cAAA+S,UAAA,CAAA/S,IAAA;cAAA,OACApI,cAAA;gBACA2G,EAAA,EAAAA;cACA,GAAAiF,IAAA,WAAApC,GAAA;gBACA,IAAAA,GAAA,CAAAU,SAAA;kBACA8Q,OAAA,CAAAlN,QAAA;oBACAnI,OAAA;oBACAS,IAAA;kBACA;kBACA/F,YAAA,CAAA2a,OAAA,CAAArS,MAAA,EAAAqS,OAAA,CAAA9U,MAAA;gBACA;kBACA8U,OAAA,CAAAlN,QAAA;oBACAnI,OAAA,EAAA6D,GAAA,CAAAuE,OAAA;oBACA3H,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA+U,UAAA,CAAA/R,IAAA;UAAA;QAAA,GAAA6R,SAAA;MAAA;IACA;IACA9M,WAAA,WAAAA,YAAA;MAAA,IAAAiN,YAAA,EAAAC,YAAA;MACA,KAAAhY,UAAA,CAAAC,cAAA;MACA,KAAAD,UAAA,CAAAE,SAAA;MACA,KAAAF,UAAA,CAAAG,aAAA;MACA,KAAAH,UAAA,CAAAI,qBAAA;MACA,KAAAJ,UAAA,CAAAK,gBAAA;MACA,KAAAL,UAAA,CAAAM,oBAAA;MACA,CAAAyX,YAAA,QAAAlU,KAAA,cAAAkU,YAAA,gBAAAA,YAAA,GAAAA,YAAA,CAAAE,QAAA,cAAAF,YAAA,eAAAA,YAAA,CAAAG,gBAAA;MACA,CAAAF,YAAA,QAAAnU,KAAA,cAAAmU,YAAA,gBAAAA,YAAA,GAAAA,YAAA,CAAAC,QAAA,cAAAD,YAAA,eAAAA,YAAA,CAAAG,WAAA;IACA;IACAjK,OAAA,WAAAA,QAAA;MACA,KAAA1O,MAAA;MACA,KAAAmT,aAAA;MACA,KAAA7H,WAAA;IACA;IACAsN,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,KAAAC,WAAA;QACAC,aAAA;QACAC,SAAA;QACAC,QAAA;MACA;MACA,IAAAC,UAAA,QAAApT,MAAA,CAAAqR,KAAA,CAAAC,WAAA,CAAAC,UAAA;MACA1M,MAAA,CAAAwO,IAAA,CAAAD,UAAA,EAAA/O,OAAA,WAAAsI,IAAA;QACA,IAAA2G,UAAA,GAAAF,UAAA,CAAAzG,IAAA;QACA2G,UAAA,CAAAjP,OAAA,WAAAtG,IAAA;UACA,IAAAA,IAAA,CAAA8D,IAAA;YACAkR,OAAA,CAAAC,WAAA,CAAAC,aAAA,CAAAlV,IAAA,CAAAmQ,KAAA,IAAAnQ,IAAA,CAAAoQ,QAAA;UACA,WAAApQ,IAAA,CAAA8D,IAAA;YACAkR,OAAA,CAAAC,WAAA,CAAAE,SAAA,CAAAnV,IAAA,CAAAmQ,KAAA,IAAAnQ,IAAA,CAAAoQ,QAAA;UACA,WAAApQ,IAAA,CAAA8D,IAAA;YACAkR,OAAA,CAAAC,WAAA,CAAAG,QAAA,CAAApV,IAAA,CAAAmQ,KAAA,IAAAnQ,IAAA,CAAAoQ,QAAA;UACA;QACA;MACA;MAEA,IAAAoF,MAAA,QAAAhV,KAAA,CAAAoU,QAAA;MACA,IAAA5R,UAAA,GAAAwS,MAAA,CAAAC,gBAAA;MACA,IAAAC,MAAA,GAAA1S,UAAA,CAAA2S,OAAA;MACAD,MAAA,CAAA3a,IAAA,SAAA4B,UAAA,CAAAC,cAAA,OAAAD,UAAA,CAAAG,aAAA,OAAAH,UAAA,CAAAE,SAAA;MACA6Y,MAAA,CAAA5G,OAAA;MACA0G,MAAA,CAAAI,UAAA;MACA,KAAApV,KAAA,CAAAoU,QAAA,CAAAC,gBAAA;MACA,KAAArU,KAAA,CAAAoU,QAAA,CAAAiB,gBAAA;IACA;IAEAC,SAAA,WAAAA,UAAAC,QAAA,EAAAC,KAAA;MACA,IAAAA,KAAA;QACA,OAAAD,QAAA,CAAAtY,GAAA;MACA;QACA,IAAAwY,MAAA,GAAAjc,OAAA,CAAA+b,QAAA,CAAAtY,GAAA,OACAyY,QAAA,MAAA9Z,UAAA,CAAA4Z,KAAA,MAAAvY,GAAA;QAEA,IAAAwY,MAAA,CAAAxX,KAAA;UACAsX,QAAA,CAAAI,KAAA;QACA,WAAAF,MAAA,CAAAxX,KAAA;UACAsX,QAAA,CAAAK,MAAA;QACA;QACA,OAAAH,MAAA,CAAAxX,KAAA,eAAAwX,MAAA,CAAA9P,MAAA;MACA;IACA;IACAkQ,UAAA,WAAAA,WAAAnN,GAAA;MAAA,IAAAoN,OAAA;MACApO,OAAA,CAAAC,GAAA,CAAAe,GAAA;MACA,KAAA1N,aAAA;MACA,KAAAG,gBAAA;MACA,KAAAD,KAAA;MACA,KAAAD,KAAA,wBAAAoM,MAAA,MAAA0O,SAAA,CAAArN,GAAA;MACA,KAAAF,SAAA;QAAA,IAAAwN,qBAAA;QACA,IAAAC,UAAA,GAAAH,OAAA,CAAAhH,aAAA,CAAAvP,IAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAA4O,IAAA,KAAA1F,GAAA,CAAA0F,IAAA;QAAA;QACA1G,OAAA,CAAAC,GAAA,CAAAsO,UAAA;QACA,CAAAD,qBAAA,GAAAF,OAAA,CAAA9V,KAAA,CAAAtD,OAAA,cAAAsZ,qBAAA,eAAAA,qBAAA,CAAAE,IAAA,CAAAxN,GAAA,EAAAuN,UAAA,EAAAH,OAAA,CAAA3W,MAAA,EAAA2W,OAAA,CAAAna,MAAA,EAAAma,OAAA,CAAA7S,QAAA;MACA;IACA;IACAkT,YAAA,WAAAA,aAAAzN,GAAA;MAAA,IAAA0N,OAAA;MACA1O,OAAA,CAAAC,GAAA,QAAAe,GAAA;MACA,KAAA2N,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACArX,IAAA;MACA,GAAAwF,IAAA;QACA0R,OAAA,CAAAI,eAAA,CAAA9N,GAAA,CAAA0F,IAAA;MACA,GAAAqI,KAAA;QACA;MAAA,CACA;IACA;IACAC,aAAA,WAAAA,cAAAhO,GAAA;MACA,KAAAiO,gBAAA,CAAAjO,GAAA,CAAA0F,IAAA;IACA;IACAwI,eAAA,WAAAA,gBAAAC,GAAA;MAAA,IAAAC,OAAA;MACApP,OAAA,CAAAC,GAAA,QAAAkP,GAAA;MACA,IAAAE,WAAA,YAAAA,YAAA;QACA,IAAAF,GAAA;UACA,IAAAlU,UAAA;UACA,IAAAD,QAAA,GAAAoU,OAAA,CAAA7a,OAAA,CAAAmH,MAAA,WAAA5D,IAAA;YAAA,OAAAmD,UAAA,CAAAU,QAAA,CAAA7D,IAAA,CAAA8D,IAAA;UAAA;UACAwT,OAAA,CAAA7a,OAAA,GAAAyG,QAAA;UACAoU,OAAA,CAAAtO,SAAA,WAAAC,CAAA;YACAqO,OAAA,CAAA9W,KAAA,CAAAoU,QAAA,CAAA4C,aAAA;UACA;QACA,WAAAH,GAAA;UACAC,OAAA,CAAA7a,OAAA,GAAA3C,SAAA,CAAAwd,OAAA,CAAA/S,WAAA;QACA;UACA+S,OAAA,CAAA7a,OAAA,GAAA3C,SAAA,CAAAwd,OAAA,CAAA/S,WAAA;QACA;QACA+S,OAAA,CAAArV,MAAA,CAAAC,QAAA;QACAoV,OAAA,CAAA5a,YAAA,GAAA2a,GAAA;MACA;MAEA,SAAAlb,MAAA,SAAAA,MAAA,CAAAyE,MAAA;QACA,YAAAiW,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACArX,IAAA;QACA,GAAAwF,IAAA;UACAoS,OAAA,CAAAnb,MAAA;UACAob,WAAA;UACAD,OAAA,CAAA1b,QAAA;QACA,GAAAqb,KAAA,cAEA;MACA;QACA,KAAArb,QAAA;QACA2b,WAAA;MACA;IACA;IACAE,eAAA,WAAAA,gBAAArJ,IAAA;MAAA,IAAAsJ,OAAA;MACA,IAAAC,aAAA,OAAAC,GAAA,MAAAzb,MAAA,CAAAqI,GAAA,WAAAxE,IAAA;QAAA,OAAAA,IAAA,CAAA4O,IAAA;MAAA;MACAR,IAAA,GAAAA,IAAA,CAAAxK,MAAA,WAAA5D,IAAA;QAAA,QAAA2X,aAAA,CAAAE,GAAA,CAAA7X,IAAA,CAAA6O,WAAA;MAAA;MAEA,KAAAT,IAAA,CAAAxN,MAAA;QACA;MACA;MAEAwN,IAAA,GAAAA,IAAA,CAAA5J,GAAA,WAAAxE,IAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA0X,OAAA,CAAAI,sBAAA,CAAA9X,IAAA;QACA,OAAA6E,aAAA,CAAAA,aAAA;UACAzK,UAAA;UACA2U,aAAA,EAAA2I,OAAA,CAAAK,iBAAA;QAAA,GACA/X,IAAA;UACA2O,gBAAA,EAAA3O,IAAA,CAAAC,EAAA;UACA2O,IAAA,EAAA5O,IAAA,CAAA6O,WAAA;UACAC,OAAA;UACAL,QAAA,EAAAzO,IAAA,CAAA0O,IAAA,aAAA1O,IAAA,CAAA0O,IAAA;QAAA,GACAgJ,OAAA,CAAA3J,YAAA,CAAA/N,IAAA;QAEA;MACA;MAEA,KAAA7D,MAAA,MAAA0L,MAAA,CAAAmQ,kBAAA,MAAA7b,MAAA,GAAA6b,kBAAA,CAAA5J,IAAA,GAAA6J,IAAA,WAAAC,CAAA,EAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAzJ,IAAA,GAAAwJ,CAAA,CAAAxJ,IAAA;MAAA;MAEA,IAAA0J,cAAA,QAAA9I,aAAA;MACA,KAAAA,aAAA,GAAA3N,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAA+B,SAAA,IAAAmE,MAAA,CAAAmQ,kBAAA,CAAAI,cAAA,GAAAJ,kBAAA,CAAA5J,IAAA;MACA,KAAAiK,YAAA,MAAAlc,MAAA;IACA;IACAkc,YAAA,WAAAA,aAAAlc,MAAA;MAAA,IAAAmc,OAAA;MACA,IAAAC,WAAA,GAAA1T,aAAA,UAAA5C,MAAA,CAAAqR,KAAA,CAAAC,WAAA,CAAAC,UAAA;MACA,IAAAgF,cAAA,GAAArc,MAAA,CAAAyH,MAAA,WAAA5D,IAAA;QACA,SAAAuY,WAAA,CAAAvY,IAAA,CAAA4O,IAAA;MACA;MACA,IAAA6J,aAAA,GAAAtc,MAAA,CAAAyH,MAAA,WAAA5D,IAAA;QAAA,OAAAA,IAAA,CAAA5F,UAAA;MAAA;MACA,IAAAqe,aAAA,CAAA7X,MAAA;QACAsH,OAAA,CAAAC,GAAA,CAAAsQ,aAAA;QACA,IAAAC,QAAA,GAAAD,aAAA,CAAA7U,MAAA,WAAA5D,IAAA;UAAA,OAAAA,IAAA,CAAA0O,IAAA;QAAA;QACA,IAAAgK,QAAA,CAAA9X,MAAA;UACA8X,QAAA,CAAApS,OAAA,WAAAtG,IAAA;YACA,IAAA2Y,KAAA,GAAAL,OAAA,CAAAM,cAAA,CAAA5Y,IAAA;YACA,IAAA2Y,KAAA,IAAAA,KAAA,CAAAve,UAAA;cACA,IAAAye,oBAAA,GAAAP,OAAA,CAAAQ,gBAAA,CAAA9Y,IAAA;cACA,IAAA6Y,oBAAA,CAAAjY,MAAA;gBACAiY,oBAAA,CAAAvS,OAAA,WAAAyS,WAAA;kBACA,IAAAC,MAAA,GAAAV,OAAA,CAAAW,YAAA,CAAAjZ,IAAA,EAAA+Y,WAAA;kBACA,KAAAC,MAAA;kBACAV,OAAA,CAAAY,IAAA,CAAAH,WAAA;kBACAT,OAAA,CAAAY,IAAA,CAAAH,WAAA,mBAAAT,OAAA,CAAAP,iBAAA;gBACA;cACA;YACA;UACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,KAAAS,cAAA,CAAA5X,MAAA;MACA4X,cAAA,CAAAlS,OAAA,WAAAtG,IAAA;QACA,IAAA2P,KAAA,GAAA2I,OAAA,CAAAQ,gBAAA,CAAA9Y,IAAA;QACA2P,KAAA,GAAAA,KAAA,CAAA/L,MAAA,WAAAuV,CAAA;UAAA,QAAAZ,WAAA,CAAAY,CAAA,CAAAvK,IAAA;QAAA;QACA,IAAAe,KAAA,CAAA/O,MAAA;UACA+O,KAAA,CAAArJ,OAAA,WAAAkF,GAAA;YACA,IAAA8M,OAAA,CAAAW,YAAA,CAAAjZ,IAAA,EAAAwL,GAAA;cACA,IAAA+J,UAAA,GAAA+C,OAAA,CAAArW,MAAA,CAAAqR,KAAA,CAAAC,WAAA,CAAAC,UAAA,CAAAxT,IAAA,CAAA4O,IAAA;cACA2G,UAAA,CAAAjP,OAAA,WAAAnL,MAAA;gBACAqQ,GAAA,CAAArQ,MAAA,CAAA2I,IAAA,IAAA9D,IAAA,CAAA7E,MAAA,CAAA2I,IAAA;cACA;cAEAwU,OAAA,CAAArW,MAAA,CAAAC,QAAA;gBACA0M,IAAA,EAAApD,GAAA,CAAAoD,IAAA;gBACAR,IAAA,EAAAmH;cACA;cACArN,OAAA,CAAAC,GAAA,QAAAnI,IAAA,CAAAqP,UAAA;cACA,IAAArP,IAAA,CAAA5F,UAAA;gBACA;cAAA,CAEA;gBACAke,OAAA,CAAAR,sBAAA,CAAAtM,GAAA,EAAA+J,UAAA;cACA;YACA;cACA,IAAApH,WAAA,GAAAnO,IAAA,CAAAmO,WAAA;gBAAA+E,MAAA,GAAAC,wBAAA,CAAAnT,IAAA,EAAAoZ,UAAA;cACA,IAAAC,YAAA,IAAAf,OAAA,CAAArW,MAAA,CAAAqR,KAAA,CAAAC,WAAA,CAAAC,UAAA,CAAAxT,IAAA,CAAA4O,IAAA,SAAAhL,MAAA,WAAAzI,MAAA;gBAAA,OAAAA,MAAA,CAAA2I,IAAA;cAAA;cACAuV,YAAA,CAAA/S,OAAA,WAAAnL,MAAA;gBACAqQ,GAAA,CAAArQ,MAAA,CAAA2I,IAAA,IAAA9D,IAAA,CAAA7E,MAAA,CAAA2I,IAAA;cACA;cACA;cACAwU,OAAA,CAAArW,MAAA,CAAAC,QAAA;gBACA0M,IAAA,EAAApD,GAAA,CAAAoD,IAAA;gBACAR,IAAA,EAAAiL;cACA;cACAf,OAAA,CAAAR,sBAAA,CAAAtM,GAAA,EAAA6N,YAAA;YACA;UACA;QACA;MACA;IACA;IACAC,QAAA,WAAAA,SAAAC,KAAA;MAAA,IAAArQ,GAAA,GAAAqQ,KAAA,CAAArQ,GAAA;QAAAkF,IAAA,GAAAmL,KAAA,CAAAnL,IAAA;MACAlG,OAAA,CAAAC,GAAA,uBAAAe,GAAA,EAAAkF,IAAA;MACA,IAAAoL,WAAA;MACApL,IAAA,CAAA9H,OAAA,WAAAtG,IAAA;QACAwZ,WAAA,CAAAxZ,IAAA,CAAA8D,IAAA,IAAA9D,IAAA,CAAAoQ,QAAA;MACA;MACA;;MAEA,IAAAqJ,eAAA,QAAAxX,MAAA,CAAAqR,KAAA,CAAAC,WAAA,CAAAC,UAAA,CAAAtK,GAAA,CAAA0F,IAAA;MACA,IAAA8K,mBAAA,GAAAD,eAAA,CAAAjV,GAAA,WAAArJ,MAAA;QAAA,OAAAA,MAAA,CAAA2I,IAAA;MAAA;MAEA,IAAA6V,kBAAA,GAAAD,mBAAA,CAAA9V,MAAA,WAAAxE,IAAA;QAAA,QAAAgP,IAAA,CAAAwL,IAAA,WAAA5Z,IAAA;UAAA,OAAAA,IAAA,CAAA8D,IAAA,KAAA1E,IAAA;QAAA;MAAA;MACA8I,OAAA,CAAAC,GAAA,WAAAwR,kBAAA;MAEA,IAAAA,kBAAA,CAAA/Y,MAAA;QACA,IAAA6V,UAAA,QAAAnH,aAAA,CAAAvP,IAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAA4O,IAAA,KAAA1F,GAAA,CAAA0F,IAAA;QAAA;QACA+K,kBAAA,CAAArT,OAAA,WAAAlH,IAAA;UACA8I,OAAA,CAAAC,GAAA,6BAAAN,MAAA,CAAAzI,IAAA,iCAAAqX,UAAA,CAAArX,IAAA;UACAoa,WAAA,CAAApa,IAAA,IAAAqX,UAAA,CAAArX,IAAA;QACA;MACA;MACA8I,OAAA,CAAAC,GAAA,gBAAAxG,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAA+B,SAAA,CAAA8V,WAAA;;MAEA;MACA,KAAAK,oBAAA,CAAA3Q,GAAA,CAAA0F,IAAA,EAAA4K,WAAA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAM,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,eAAA;MACA,IAAAC,OAAA,YAAAA,OAAAC,KAAA;QACA,KAAAA,KAAA,KAAAA,KAAA,CAAAtZ,MAAA;QACAsZ,KAAA,CAAA5T,OAAA,WAAAtG,IAAA;UACAga,eAAA,CAAA3V,IAAA,CAAArE,IAAA,CAAA4O,IAAA;UACA,IAAA5O,IAAA,CAAAzB,QAAA,IAAAyB,IAAA,CAAAzB,QAAA,CAAAqC,MAAA;YACAqZ,OAAA,CAAAja,IAAA,CAAAzB,QAAA;UACA;QACA;MACA;MACA0b,OAAA,MAAA3d,iBAAA;MACA4L,OAAA,CAAAC,GAAA,oBAAA6R,eAAA;MACAA,eAAA,CAAA1T,OAAA,WAAAtG,IAAA;QACA+Z,OAAA,CAAA9X,MAAA,CAAAC,QAAA,8BAAAlC,IAAA;MACA;MACA,KAAA7D,MAAA,QAAAA,MAAA,CAAAyH,MAAA,WAAA5D,IAAA;QAAA,QAAAga,eAAA,CAAAnW,QAAA,CAAA7D,IAAA,CAAA4O,IAAA;MAAA;MACA,KAAAU,aAAA,QAAAA,aAAA,CAAA1L,MAAA,WAAA5D,IAAA;QAAA,QAAAga,eAAA,CAAAnW,QAAA,CAAA7D,IAAA,CAAA4O,IAAA;MAAA;MACA,KAAAtS,iBAAA;IACA;IACA6d,mBAAA,WAAAA,oBAAAD,KAAA;MACAhS,OAAA,CAAAC,GAAA,UAAA+R,KAAA;MACAhS,OAAA,CAAAC,GAAA,uBAAA3H,KAAA,CAAAoU,QAAA,CAAAwF,kBAAA;MACA,IAAAC,OAAA,GAAAH,KAAA,CAAAG,OAAA;MACA,KAAA/d,iBAAA,GAAA4d,KAAA,CAAAG,OAAA;IACA;IACAC,cAAA,WAAAA,eAAA;MACA9gB,SAAA;QACAkN,GAAA,OAAA9K;MACA,GAAAsJ,IAAA,WAAApC,GAAA;QACA8K,MAAA,CAAAC,IAAA,CAAA/K,GAAA,CAAAa,IAAA;MACA;IACA;IACA4W,cAAA,WAAAA,eAAAC,UAAA;MACA,IAAA9J,GAAA,GAAA8J,UAAA,CAAAxT,KAAA;MACA,IAAAyT,MAAA;MACA,IAAA/J,GAAA,CAAA7M,QAAA,CAAAzJ,UAAA,CAAAsgB,KAAA;QACAD,MAAA,CAAApW,IAAA;MACA,WAAAqM,GAAA,CAAA7M,QAAA,CAAAzJ,UAAA,CAAAugB,QAAA;QACAF,MAAA,CAAApW,IAAA;MACA,WAAAqM,GAAA,CAAA7M,QAAA,CAAAzJ,UAAA,CAAAwgB,UAAA,KAAAlK,GAAA,CAAA7M,QAAA,CAAAzJ,UAAA,CAAAygB,UAAA;QACAJ,MAAA,CAAApW,IAAA;MACA,WAAAqM,GAAA,CAAA7M,QAAA,CAAAzJ,UAAA,CAAA0gB,QAAA;QACAL,MAAA,CAAApW,IAAA;MACA;QACAoW,MAAA,CAAApW,IAAA;MACA;MACA,OAAAoW,MAAA;IACA;IACAlE,SAAA,WAAAA,UAAArN,GAAA;MACA,IAAAA,GAAA,CAAAwF,IAAA;QACA,OAAAxF,GAAA,CAAAiM,SAAA;MACA,WAAAjM,GAAA,CAAAwF,IAAA;QACA,OAAAxF,GAAA,CAAAgM,aAAA;MACA;QACA,OAAAhM,GAAA,CAAAkM,QAAA;MACA;IACA;IACA2F,UAAA,WAAAA,WAAA7R,GAAA;MACA,IAAAqM,UAAA,QAAAtT,MAAA,CAAAqR,KAAA,CAAAC,WAAA,CAAAC,UAAA,CAAAtK,GAAA,CAAA0F,IAAA;MACA,KAAArS,oBAAA,GAAAgZ,UAAA;IACA;IACAyF,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAvF,MAAA,GAAAuF,KAAA,CAAAvF,MAAA;QAAAwF,MAAA,GAAAD,KAAA,CAAAC,MAAA;QAAAC,SAAA,GAAAF,KAAA,CAAAE,SAAA;QAAAjS,GAAA,GAAA+R,KAAA,CAAA/R,GAAA;QAAAkS,MAAA,GAAAH,KAAA,CAAAG,MAAA;MACA,IAAAnF,MAAA,QAAAoF,YAAA,CAAAnS,GAAA;MACA,OAAA+M,MAAA;IACA;IACAoF,YAAA,WAAAA,aAAAnS,GAAA;MACA,IAAAoS,gBAAA,GACA,KAAA3e,UAAA;QADAC,cAAA,GAAA0e,gBAAA,CAAA1e,cAAA;QAAAG,qBAAA,GAAAue,gBAAA,CAAAve,qBAAA;QAAAD,aAAA,GAAAwe,gBAAA,CAAAxe,aAAA;QACAG,oBAAA,GAAAqe,gBAAA,CAAAre,oBAAA;QAAAJ,SAAA,GAAAye,gBAAA,CAAAze,SAAA;QAAAG,gBAAA,GAAAse,gBAAA,CAAAte,gBAAA;MAEA,IAAAue,cAAA,QAAAtG,WAAA,CAAAC,aAAA,CAAAhM,GAAA,CAAAgM,aAAA,KAAAhM,GAAA,CAAAgM,aAAA;MACA,IAAAsG,UAAA,QAAAvG,WAAA,CAAAE,SAAA,CAAAjM,GAAA,CAAAiM,SAAA,KAAAjM,GAAA,CAAAiM,SAAA;MACA,IAAAsG,SAAA,QAAAxG,WAAA,CAAAG,QAAA,CAAAlM,GAAA,CAAAkM,QAAA,KAAAlM,GAAA,CAAAkM,QAAA;MAEA,IAAAsG,SAAA;MAEA,IAAA7e,SAAA;QACA,IAAAG,gBAAA;UACA0e,SAAA,GAAAD,SAAA,CAAA5X,QAAA,CAAAhH,SAAA;QACA;UACA6e,SAAA,GAAAD,SAAA,KAAA5e,SAAA;QACA;MACA;MACA,IAAA8e,aAAA;MACA,IAAA7e,aAAA;QACA,IAAAG,oBAAA;UACA0e,aAAA,GAAAJ,cAAA,CAAA1X,QAAA,CAAA/G,aAAA;QACA;UACA6e,aAAA,GAAAJ,cAAA,KAAAze,aAAA;QACA;MACA;MACA,IAAA8e,cAAA;MACA,IAAAhf,cAAA;QACA,IAAAG,qBAAA;UACA6e,cAAA,GAAAJ,UAAA,CAAA3X,QAAA,CAAAjH,cAAA;QACA;UACAgf,cAAA,GAAAJ,UAAA,KAAA5e,cAAA;QACA;MACA;MACA;;MAEA,IAAAqZ,MAAA,GAAA2F,cAAA,IAAAD,aAAA,IAAAD,SAAA;MACA,IAAAzF,MAAA;QACA;MACA;QACA;MACA;IACA;IACA4F,aAAA,WAAAA,cAAAnL,GAAA;MACA,KAAAA,GAAA,KAAAA,GAAA,CAAA9P,MAAA;QACA,KAAAiK,OAAA;QACA;MACA;MACA,KAAA1O,MAAA,QAAAA,MAAA,CAAAyH,MAAA,WAAA5D,IAAA;QAAA,OAAA0Q,GAAA,CAAAkJ,IAAA,WAAAvY,EAAA;UAAA,OAAAA,EAAA,KAAArB,IAAA,CAAA8b,cAAA;QAAA;MAAA;MACA,KAAAxM,aAAA,QAAAA,aAAA,CAAA1L,MAAA,WAAA5D,IAAA;QAAA,OAAA0Q,GAAA,CAAAkJ,IAAA,WAAAvY,EAAA;UAAA,OAAAA,EAAA,KAAArB,IAAA,CAAA8b,cAAA;QAAA;MAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MACApiB,YAAA,MAAAsI,MAAA,OAAAzC,MAAA;IACA;EACA;AACA", "ignoreList": []}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\recognitionConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\recognitionConfig.vue", "mtime": 1745557754679}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQoNCmltcG9ydCB7IHVuaXF1ZUFyciB9IGZyb20gJ0AvdXRpbHMnDQppbXBvcnQgew0KICBHZXRGYWN0b3J5Q29tcFR5cGVJbmRlbnRpZnlTZXR0aW5nLA0KICBTYXZlQ29tcFR5cGVJZGVudGlmeVNldHRpbmcNCn0gZnJvbSAnQC9hcGkvUFJPL2NvbXBvbmVudC10eXBlJw0KDQpjb25zdCBTUExJVFZBTFVFID0gJ3wnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgZm9ybTogew0KICAgICAgICBlbmFibGU6IGZhbHNlDQogICAgICB9LA0KICAgICAgbGlzdDogW10sDQogICAgICBzcGxpdFN5bWJvbDogU1BMSVRWQUxVRSwNCiAgICAgIGJ0bkxvYWRpbmc6IGZhbHNlDQogICAgfQ0KICB9LA0KICBtb3VudGVkKCkgew0KICAgIHRoaXMuZ2V0VHlwZUxpc3QoKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgZ2V0VHlwZUxpc3QoKSB7DQogICAgICBHZXRGYWN0b3J5Q29tcFR5cGVJbmRlbnRpZnlTZXR0aW5nKHt9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgY29uc3QgeyBJc19FbmFibGVkLCBTZXR0aW5nX0xpc3QgfSA9IHJlcy5EYXRhDQogICAgICAgICAgdGhpcy5mb3JtLmVuYWJsZSA9IElzX0VuYWJsZWQNCiAgICAgICAgICB0aGlzLmxpc3QgPSBTZXR0aW5nX0xpc3QubWFwKCh2LCBpbmRleCkgPT4gew0KICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgJ2l0ZW0nICsgaW5kZXgsIHYuUHJlZml4cyB8fCAnJykNCiAgICAgICAgICAgIHJldHVybiB2DQogICAgICAgICAgfSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVTdWJtaXQoKSB7DQogICAgICBpZiAodGhpcy5mb3JtLmVuYWJsZSkgew0KICAgICAgICBjb25zdCBhcnIgPSBbXQ0KICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMubGlzdC5sZW5ndGg7IGkrKykgew0KICAgICAgICAgIGNvbnN0IHJlZ2V4ID0gL14oPyEuKlx8XHwpKD8hLipcfCQpKD8hXlx8KVtefF17MCwxMH0oPzpcfFtefF17MCwxMH0pKiQvDQogICAgICAgICAgaWYgKCFyZWdleC50ZXN0KHRoaXMuZm9ybVtgaXRlbSR7aX1gXSkpIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiBgJHt0aGlzLmxpc3RbaV0uQ29tcF9UeXBlX05hbWV96YWN572u5LiN56ym5ZCI6KaB5rGCYCwNCiAgICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgcmV0dXJuDQogICAgICAgICAgfQ0KDQogICAgICAgICAgY29uc3QgaXRlbSA9IHRoaXMuZm9ybVtgaXRlbSR7aX1gXS5zcGxpdCh0aGlzLnNwbGl0U3ltYm9sKS5maWx0ZXIodiA9PiAhIXYpDQoNCiAgICAgICAgICAvLyBpZiAoaXRlbS5sZW5ndGggPT09IDApIHsNCiAgICAgICAgICAvLyAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIC8vICAgICBtZXNzYWdlOiBgJHt0aGlzLmxpc3RbaV0uQ29tcF9UeXBlX05hbWV95LiN6IO95Li656m6YCwNCiAgICAgICAgICAvLyAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgICAgLy8gICB9KQ0KICAgICAgICAgIC8vICAgcmV0dXJuDQogICAgICAgICAgLy8gfQ0KDQogICAgICAgICAgZm9yIChsZXQgaiA9IDA7IGogPCBpdGVtLmxlbmd0aDsgaisrKSB7DQogICAgICAgICAgICBjb25zdCBkID0gaXRlbVtqXQ0KICAgICAgICAgICAgaWYgKGQubGVuZ3RoID4gMTApIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgbWVzc2FnZTogYCR7dGhpcy5saXN0W2ldLkNvbXBfVHlwZV9OYW1lfeWNleS4qumFjee9ru+8jOS4jeiDvei2hei/hzEw5Liq5a2X56ymYCwNCiAgICAgICAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgcmV0dXJuDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KDQogICAgICAgICAgYXJyLnB1c2goLi4uaXRlbSkNCiAgICAgICAgfQ0KICAgICAgICBjb25zdCB1bmlBcnIgPSB1bmlxdWVBcnIoYXJyKQ0KICAgICAgICBpZiAodW5pQXJyLmxlbmd0aCAhPT0gYXJyLmxlbmd0aCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogJ+mFjee9ruS4jeiDveebuOWQjCcsDQogICAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgICB9KQ0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICB9DQogICAgICB0aGlzLmJ0bkxvYWRpbmcgPSB0cnVlDQogICAgICBTYXZlQ29tcFR5cGVJZGVudGlmeVNldHRpbmcoew0KICAgICAgICBJc19FbmFibGVkOiB0aGlzLmZvcm0uZW5hYmxlLA0KICAgICAgICBTZXR0aW5nX0xpc3Q6IHRoaXMubGlzdC5tYXAoKHYsIGkpID0+IHsNCiAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgLi4udiwNCiAgICAgICAgICAgIFByZWZpeHM6IHRoaXMuZm9ybVtgaXRlbSR7aX1gXQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6ICfmk43kvZzmiJDlip8nLA0KICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLiRlbWl0KCdjbG9zZScpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KS5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICBtYWluQmx1cihlKSB7DQoNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["recognitionConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "recognitionConfig.vue", "sourceRoot": "src/views/PRO/process-settings/management/component", "sourcesContent": ["<template>\r\n  <div class=\"form-wrapper\">\r\n    <div class=\"form-x\">\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"120px\">\r\n        <el-form-item label=\"是否启用\" prop=\"enable\">\r\n          <el-radio-group v-model=\"form.enable\">\r\n            <el-radio :label=\"false\">否</el-radio>\r\n            <el-radio :label=\"true\">是</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <template v-if=\"form.enable\">\r\n          <el-form-item\r\n            v-for=\"(element,index) in list\"\r\n            :key=\"index\"\r\n            :show-message=\"false\"\r\n            :label=\"element.Comp_Type_Name\"\r\n            prop=\"mainPart\"\r\n          >\r\n            <el-input\r\n              v-model.trim=\"form['item'+index]\"\r\n              :placeholder=\"`请输入（多个使用'${splitSymbol}'隔开），单个配置不超过10个字符`\"\r\n              clearable\r\n              @blur=\"mainBlur\"\r\n            />\r\n          </el-form-item>\r\n        </template>\r\n\r\n      </el-form>\r\n    </div>\r\n    <div class=\"btn-x\">\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { uniqueArr } from '@/utils'\r\nimport {\r\n  GetFactoryCompTypeIndentifySetting,\r\n  SaveCompTypeIdentifySetting\r\n} from '@/api/PRO/component-type'\r\n\r\nconst SPLITVALUE = '|'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      form: {\r\n        enable: false\r\n      },\r\n      list: [],\r\n      splitSymbol: SPLITVALUE,\r\n      btnLoading: false\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getTypeList()\r\n  },\r\n  methods: {\r\n    getTypeList() {\r\n      GetFactoryCompTypeIndentifySetting({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          const { Is_Enabled, Setting_List } = res.Data\r\n          this.form.enable = Is_Enabled\r\n          this.list = Setting_List.map((v, index) => {\r\n            this.$set(this.form, 'item' + index, v.Prefixs || '')\r\n            return v\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      if (this.form.enable) {\r\n        const arr = []\r\n        for (let i = 0; i < this.list.length; i++) {\r\n          const regex = /^(?!.*\\|\\|)(?!.*\\|$)(?!^\\|)[^|]{0,10}(?:\\|[^|]{0,10})*$/\r\n          if (!regex.test(this.form[`item${i}`])) {\r\n            this.$message({\r\n              message: `${this.list[i].Comp_Type_Name}配置不符合要求`,\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n\r\n          const item = this.form[`item${i}`].split(this.splitSymbol).filter(v => !!v)\r\n\r\n          // if (item.length === 0) {\r\n          //   this.$message({\r\n          //     message: `${this.list[i].Comp_Type_Name}不能为空`,\r\n          //     type: 'warning'\r\n          //   })\r\n          //   return\r\n          // }\r\n\r\n          for (let j = 0; j < item.length; j++) {\r\n            const d = item[j]\r\n            if (d.length > 10) {\r\n              this.$message({\r\n                message: `${this.list[i].Comp_Type_Name}单个配置，不能超过10个字符`,\r\n                type: 'warning'\r\n              })\r\n              return\r\n            }\r\n          }\r\n\r\n          arr.push(...item)\r\n        }\r\n        const uniArr = uniqueArr(arr)\r\n        if (uniArr.length !== arr.length) {\r\n          this.$message({\r\n            message: '配置不能相同',\r\n            type: 'warning'\r\n          })\r\n          return\r\n        }\r\n      }\r\n      this.btnLoading = true\r\n      SaveCompTypeIdentifySetting({\r\n        Is_Enabled: this.form.enable,\r\n        Setting_List: this.list.map((v, i) => {\r\n          return {\r\n            ...v,\r\n            Prefixs: this.form[`item${i}`]\r\n          }\r\n        })\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '操作成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('close')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.btnLoading = false\r\n      })\r\n    },\r\n    mainBlur(e) {\r\n\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"~@/styles/mixin.scss\";\r\n.form-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  max-height: 70vh;\r\n  .form-x{\r\n    overflow: auto;\r\n    padding-right: 16px;\r\n    @include scrollBar;\r\n  }\r\n  .btn-x {\r\n    padding-top: 16px;\r\n    text-align: right;\r\n  }\r\n\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\ToleranceConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\ToleranceConfig.vue", "mtime": 1758085667851}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ToleranceConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "ToleranceConfig.vue", "sourceRoot": "src/views/PRO/project-config/project-quality/components", "sourcesContent": ["<template>\n  <div style=\"height: calc(100vh - 300px)\">\n    <vxe-table\n      v-loading=\"tbLoading\"\n      :empty-render=\"{name: 'NotData'}\"\n      show-header-overflow\n      element-loading-spinner=\"el-icon-loading\"\n      element-loading-text=\"拼命加载中\"\n      empty-text=\"暂无数据\"\n      height=\"100%\"\n      align=\"left\"\n      stripe\n      :data=\"tbData\"\n      resizable\n      :auto-resize=\"true\"\n      class=\"cs-vxe-table\"\n      :tooltip-config=\"{ enterable: true }\"\n    >\n      <vxe-column\n        show-overflow=\"tooltip\"\n        sortable\n        field=\"Length\"\n        title=\"长度\"\n        min-width=\"150\"\n        align=\"center\"\n      >\n        <template #default=\"{ row }\">\n          <span>\n            {{ row.TypeTag }}\n            {{ row.Length }}\n          </span>\n        </template>\n      </vxe-column>\n      <vxe-column\n        show-overflow=\"tooltip\"\n        sortable\n        min-width=\"150\"\n        align=\"left\"\n        field=\"Demand\"\n        title=\"公差要求\"\n      />\n      <vxe-column\n        show-overflow=\"tooltip\"\n        sortable\n        field=\"Modify_Date\"\n        title=\"编辑时间\"\n        min-width=\"150\"\n        align=\"center\"\n      />\n      <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" align=\"center\" show-overflow>\n        <template #default=\"{ row }\">\n          <el-button type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\n          <el-divider direction=\"vertical\" />\n          <el-button type=\"text\" @click=\"removeEvent(row)\">删除</el-button>\n        </template>\n      </vxe-column>\n    </vxe-table>\n  </div>\n</template>\n\n<script>\n\nimport {\n  DeleteToleranceSetting,\n  GetToleranceSettingList\n} from '@/api/PRO/qualityInspect/quality-management'\n\nexport default {\n  props: {\n    sysProjectId: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      tbLoading: false,\n      tbData: [],\n      dialogVisible: false,\n      form: {\n        Id: '',\n        Length: 0,\n        Demand: '',\n        Type: 1\n      }\n\n    }\n  },\n  mounted() {\n    this.getToleranceList()\n  },\n  methods: {\n    getToleranceList() {\n      this.tbLoading = true\n      GetToleranceSettingList({ sysProjectId: this.sysProjectId }).then(res => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data.map(item => {\n            item.Modify_Date = item.Modify_Date || item.Create_Date\n            item.TypeTag = item.Type === 1 ? '<' : item.Type === 2 ? '<=' : item.Type === 3 ? '>' : item.Type === 4 ? '>=' : '='\n            return item\n          })\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      }).finally(() => {\n        this.tbLoading = false\n      })\n    },\n    editEvent(row) {\n      const form = JSON.parse(JSON.stringify(row))\n      this.$emit('edit', form)\n    },\n    removeEvent(row) {\n      this.$confirm('此操作将永久删除该公差配置, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DeleteToleranceSetting({ id: row.Id }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getToleranceList()\n            } else {\n              this.$message({\n                type: 'error',\n                message: res.Message\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    }\n\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n</style>\n"]}]}
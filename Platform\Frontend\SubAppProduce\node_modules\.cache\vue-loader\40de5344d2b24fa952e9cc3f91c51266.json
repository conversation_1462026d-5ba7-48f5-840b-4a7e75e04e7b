{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\ToleranceConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\ToleranceConfig.vue", "mtime": 1758099106750}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ToleranceConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "ToleranceConfig.vue", "sourceRoot": "src/views/PRO/project-config/project-quality/components", "sourcesContent": ["<template>\n  <div style=\"height: calc(100vh - 300px)\">\n    <vxe-table\n      v-loading=\"tbLoading\"\n      :empty-render=\"{name: 'NotData'}\"\n      show-header-overflow\n      element-loading-spinner=\"el-icon-loading\"\n      element-loading-text=\"拼命加载中\"\n      empty-text=\"暂无数据\"\n      height=\"100%\"\n      align=\"left\"\n      stripe\n      :data=\"tbData\"\n      resizable\n      :auto-resize=\"true\"\n      class=\"cs-vxe-table\"\n      :tooltip-config=\"{ enterable: true }\"\n    >\n      <vxe-column\n        show-overflow=\"tooltip\"\n        sortable\n        field=\"Length\"\n        title=\"长度\"\n        min-width=\"150\"\n        align=\"center\"\n      >\n        <template #default=\"{ row }\">\n          <span>\n            {{ row.TypeTag }}\n            {{ row.Length }}\n          </span>\n        </template>\n      </vxe-column>\n      <vxe-column\n        show-overflow=\"tooltip\"\n        sortable\n        min-width=\"150\"\n        align=\"left\"\n        field=\"Demand\"\n        title=\"公差要求\"\n      />\n      <vxe-column\n        show-overflow=\"tooltip\"\n        sortable\n        field=\"Modify_Date\"\n        title=\"编辑时间\"\n        min-width=\"150\"\n        align=\"center\"\n      />\n      <!-- <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" align=\"center\" show-overflow>\n        <template #default=\"{ row }\">\n          <el-button type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\n          <el-divider direction=\"vertical\" />\n          <el-button type=\"text\" @click=\"removeEvent(row)\">删除</el-button>\n        </template>\n      </vxe-column> -->\n    </vxe-table>\n  </div>\n</template>\n\n<script>\n\nimport {\n  DeleteToleranceSetting,\n  GetToleranceSettingList\n} from '@/api/PRO/qualityInspect/quality-management'\n\nexport default {\n  props: {\n    sysProjectId: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      tbLoading: false,\n      tbData: [],\n      dialogVisible: false,\n      form: {\n        Id: '',\n        Length: 0,\n        Demand: '',\n        Type: 1\n      }\n\n    }\n  },\n  watch: {\n    sysProjectId: {\n      handler(newVal) {\n        if (newVal) {\n          this.getToleranceList()\n        }\n      },\n      immediate: true\n    }\n  },\n  mounted() {\n    // 移除直接调用，改为通过 watch 监听 sysProjectId\n  },\n  methods: {\n    getToleranceList() {\n      this.tbLoading = true\n      GetToleranceSettingList({ sysProjectId: this.sysProjectId }).then(res => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data.map(item => {\n            item.Modify_Date = item.Modify_Date || item.Create_Date\n            item.TypeTag = item.Type === 1 ? '<' : item.Type === 2 ? '<=' : item.Type === 3 ? '>' : item.Type === 4 ? '>=' : '='\n            return item\n          })\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      }).finally(() => {\n        this.tbLoading = false\n      })\n    },\n    editEvent(row) {\n      const form = JSON.parse(JSON.stringify(row))\n      this.$emit('edit', form)\n    },\n    removeEvent(row) {\n      this.$confirm('此操作将永久删除该公差配置, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DeleteToleranceSetting({ id: row.Id }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getToleranceList()\n            } else {\n              this.$message({\n                type: 'error',\n                message: res.Message\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    }\n\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n</style>\n"]}]}
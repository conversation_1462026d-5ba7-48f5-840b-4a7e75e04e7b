import request from '@/utils/request'
import qs from 'qs'

// 获取零件类型分页列表 (Auth
export function GetPartTypePageList(data) {
  return request({
    url: '/PRO/PartType/GetPartTypePageList',
    method: 'post',
    data
  })
}
// 获取构件类型列表 (Auth
export function GetPartTypeList(data) {
  return request({
    url: '/PRO/PartType/GetPartTypeList',
    method: 'post',
    data
  })
}

// 设置零件的默认类型 (Auth)
export function SettingDefault(data) {
  return request({
    url: '/PRO/PartType/SettingDefault',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 保存零件(Auth)
export function SavePartType(data) {
  return request({
    url: '/PRO/PartType/SavePartType',
    method: 'post',
    data
  })
}

// 删除零件类型 (Auth)
export function DeletePartType(data) {
  return request({
    url: '/PRO/PartType/DeletePartType',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function GetFactoryPartTypeIndentifySetting(data) {
  return request({
    url: '/PRO/PartType/GetFactoryPartTypeIndentifySetting',
    method: 'post',
    data
  })
}

export function SavePartTypeIdentifySetting(data) {
  return request({
    url: '/PRO/PartType/SavePartTypeIdentifySetting',
    method: 'post',
    data
  })
}

// 零件类型列表
export function GetPartTypeTree(data) {
  return request({
    url: '/pro/parttype/GetPartTypeTree',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 获取零件的信息及配置的零件名称前缀
export function GetPartTypeEntity(data) {
  return request({
    url: '/pro/parttype/GetPartTypeEntity',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 获取零件类型配置的领用工序列表
export function GetConsumingProcessAllList(data) {
  return request({
    url: '/PRO/PartType/GetConsumingProcessAllList',
    method: 'post',
    data
  })
}

// 保存零件类型配置的领用工序列表
export function SaveConsumingProcessAllList(data) {
  return request({
    url: '/PRO/PartType/SaveConsumingProcessAllList',
    method: 'post',
    data: data
  })
}

// 获取零件类型配置的领用工序列表 新
export function GetConsumingAllList(data) {
  return request({
    url: '/PRO/PartType/GetConsumingAllList',
    method: 'post',
    data
  })
}

// 保存零件类型配置的领用工序列表 新
export function SaveConsumingProcessAllList2(data) {
  return request({
    url: '/PRO/PartType/SaveConsumingProcessAllList2',
    method: 'post',
    data: data
  })
}


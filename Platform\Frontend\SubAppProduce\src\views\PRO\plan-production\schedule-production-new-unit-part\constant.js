/**
 * @Description: 获取页面路由跳转
 * @author: yingzy
 * @date 2022/9/13
 * @param {!string} name 路由名称
 * @param {('add'|'edit'|'view')} status 页面状态新增/编辑
 * @param {('com'|'part')} pageType 零构件类型
 * @param {Object} [query] 额外query参数
 * @returns {{name:string,query:Object}}
 **/

export const getDraftQuery = (name, status, pageType, query, $route) => {
  return {
    name,
    query: { status, pg_type: pageType, pg_redirect: $route.name, ...query }
  }
}

// query: { status, pg_type: pageType, pg_redirect: pageType === 'com' ? 'PRO2ComScheduleNew' : pageType === 'part' ? 'PRO2PartScheduleNew' : 'PRO2UnitPartScheduleNew', ...query }

// 零构建构件合并唯一码,防止更换
export const uniqueCode = (pageType) => {
  return 'uuid'
}

export const FIX_COLUMN = ['Comp_Code', 'Part_Code']

export const getUnique = (isCom, element) => {
  return element.InstallUnit_Id + element.Part_Code + element.Part_Aggregate_Id
}

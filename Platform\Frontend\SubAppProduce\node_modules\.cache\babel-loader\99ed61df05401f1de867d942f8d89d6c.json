{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\utils\\index.js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\utils\\index.js", "mtime": 1757468112030}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON>", "router", "moment", "packageName", "require", "name", "parseTime", "time", "cFormat", "arguments", "length", "format", "date", "_typeof", "test", "parseInt", "replace", "RegExp", "toString", "Date", "formatObj", "y", "getFullYear", "m", "getMonth", "d", "getDate", "h", "getHours", "i", "getMinutes", "s", "getSeconds", "a", "getDay", "time_str", "result", "key", "value", "padStart", "formatTime", "option", "now", "diff", "Math", "ceil", "getQueryObject", "url", "window", "location", "href", "search", "substring", "lastIndexOf", "obj", "reg", "rs", "$1", "$2", "decodeURIComponent", "val", "String", "byteLength", "str", "code", "charCodeAt", "cleanArray", "actual", "newArray", "push", "param", "json", "Object", "keys", "map", "undefined", "encodeURIComponent", "join", "param2Obj", "split", "searchArr", "for<PERSON>ach", "v", "index", "indexOf", "html2Text", "div", "document", "createElement", "innerHTML", "textContent", "innerText", "objectMerge", "target", "source", "Array", "isArray", "slice", "property", "sourceProperty", "toggleClass", "element", "className", "classString", "nameIndex", "substr", "getTime", "type", "toDateString", "debounce", "func", "wait", "immediate", "timeout", "args", "context", "timestamp", "later", "last", "setTimeout", "apply", "_len", "_key", "callNow", "deepClone", "Error", "targetObj", "constructor", "uniqueArr", "arr", "from", "Set", "createUniqueString", "randomNum", "random", "hasClass", "ele", "cls", "match", "addClass", "removeClass", "loadFile", "xhr", "XMLHttpRequest", "okStatus", "protocol", "open", "overrideMimeType", "send", "status", "responseText", "handleAddRouterPage", "addPage", "parentName", "currentPath", "item", "path", "startsWith", "idx", "getRoutes", "findIndex", "meta", "activeMenu", "noCache", "addRoute", "DateDiff", "start", "end", "days", "abs", "combineURL", "baseURL", "relativeURL", "closeTagView", "$store", "$route", "qiankun", "prototype", "$qiankun", "query", "pg_redirect", "switchMicroAppRouteTagClose", "fullPath", "getSomeDate", "dayNumber", "date1", "date2", "setDate", "fmtDate", "unit", "getDaysBetween", "startDate", "enDate", "sDate", "eDate"], "sources": ["D:/project/platform_framework_master/platform_framework/Platform/Frontend/SubAppProduce/src/utils/index.js"], "sourcesContent": ["/**\r\n * Created by PanJiaChen on 16/11/18.\r\n */\r\nimport Vue from 'vue'\r\nimport router from '@/router'\r\nimport moment from 'moment'\r\nconst packageName = require('../../package').name\r\n\r\n/**\r\n * Parse the time to string\r\n * @param {(Object|string|number)} time\r\n * @param {string} cFormat\r\n * @returns {string | null}\r\n */\r\nexport function parseTime(time, cFormat) {\r\n  if (arguments.length === 0 || !time) {\r\n    return null\r\n  }\r\n  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'\r\n  let date\r\n  if (typeof time === 'object') {\r\n    date = time\r\n  } else {\r\n    if ((typeof time === 'string')) {\r\n      if ((/^[0-9]+$/.test(time))) {\r\n        // support \"1548221490638\"\r\n        time = parseInt(time)\r\n      } else {\r\n        // support safari\r\n        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari\r\n        time = time.replace(new RegExp(/-/gm), '/')\r\n      }\r\n    }\r\n\r\n    if ((typeof time === 'number') && (time.toString().length === 10)) {\r\n      time = time * 1000\r\n    }\r\n    date = new Date(time)\r\n  }\r\n  const formatObj = {\r\n    y: date.getFullYear(),\r\n    m: date.getMonth() + 1,\r\n    d: date.getDate(),\r\n    h: date.getHours(),\r\n    i: date.getMinutes(),\r\n    s: date.getSeconds(),\r\n    a: date.getDay()\r\n  }\r\n  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {\r\n    const value = formatObj[key]\r\n    // Note: getDay() returns 0 on Sunday\r\n    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value ] }\r\n    return value.toString().padStart(2, '0')\r\n  })\r\n  return time_str\r\n}\r\n\r\n/**\r\n * @param {number} time\r\n * @param {string} option\r\n * @returns {string}\r\n */\r\nexport function formatTime(time, option) {\r\n  if (('' + time).length === 10) {\r\n    time = parseInt(time) * 1000\r\n  } else {\r\n    time = +time\r\n  }\r\n  const d = new Date(time)\r\n  const now = Date.now()\r\n\r\n  const diff = (now - d) / 1000\r\n\r\n  if (diff < 30) {\r\n    return '刚刚'\r\n  } else if (diff < 3600) {\r\n    // less 1 hour\r\n    return Math.ceil(diff / 60) + '分钟前'\r\n  } else if (diff < 3600 * 24) {\r\n    return Math.ceil(diff / 3600) + '小时前'\r\n  } else if (diff < 3600 * 24 * 2) {\r\n    return '1天前'\r\n  }\r\n  if (option) {\r\n    return parseTime(time, option)\r\n  } else {\r\n    return (\r\n      d.getMonth() +\r\n      1 +\r\n      '月' +\r\n      d.getDate() +\r\n      '日' +\r\n      d.getHours() +\r\n      '时' +\r\n      d.getMinutes() +\r\n      '分'\r\n    )\r\n  }\r\n}\r\n\r\n/**\r\n * @param {string} url\r\n * @returns {Object}\r\n */\r\nexport function getQueryObject(url) {\r\n  url = url == null ? window.location.href : url\r\n  const search = url.substring(url.lastIndexOf('?') + 1)\r\n  const obj = {}\r\n  const reg = /([^?&=]+)=([^?&=]*)/g\r\n  search.replace(reg, (rs, $1, $2) => {\r\n    const name = decodeURIComponent($1)\r\n    let val = decodeURIComponent($2)\r\n    val = String(val)\r\n    obj[name] = val\r\n    return rs\r\n  })\r\n  return obj\r\n}\r\n\r\n/**\r\n * @param {string} input value\r\n * @returns {number} output value\r\n */\r\nexport function byteLength(str) {\r\n  // returns the byte length of an utf8 string\r\n  let s = str.length\r\n  for (var i = str.length - 1; i >= 0; i--) {\r\n    const code = str.charCodeAt(i)\r\n    if (code > 0x7f && code <= 0x7ff) s++\r\n    else if (code > 0x7ff && code <= 0xffff) s += 2\r\n    if (code >= 0xDC00 && code <= 0xDFFF) i--\r\n  }\r\n  return s\r\n}\r\n\r\n/**\r\n * @param {Array} actual\r\n * @returns {Array}\r\n */\r\nexport function cleanArray(actual) {\r\n  const newArray = []\r\n  for (let i = 0; i < actual.length; i++) {\r\n    if (actual[i]) {\r\n      newArray.push(actual[i])\r\n    }\r\n  }\r\n  return newArray\r\n}\r\n\r\n/**\r\n * @param {Object} json\r\n * @returns {Array}\r\n */\r\nexport function param(json) {\r\n  if (!json) return ''\r\n  return cleanArray(\r\n    Object.keys(json).map(key => {\r\n      if (json[key] === undefined) return ''\r\n      return encodeURIComponent(key) + '=' + encodeURIComponent(json[key])\r\n    })\r\n  ).join('&')\r\n}\r\n\r\n/**\r\n * @param {string} url\r\n * @returns {Object}\r\n */\r\nexport function param2Obj(url) {\r\n  const search = decodeURIComponent(url.split('?')[1]).replace(/\\+/g, ' ')\r\n  if (!search) {\r\n    return {}\r\n  }\r\n  const obj = {}\r\n  const searchArr = search.split('&')\r\n  searchArr.forEach(v => {\r\n    const index = v.indexOf('=')\r\n    if (index !== -1) {\r\n      const name = v.substring(0, index)\r\n      const val = v.substring(index + 1, v.length)\r\n      obj[name] = val\r\n    }\r\n  })\r\n  return obj\r\n}\r\n\r\n/**\r\n * @param {string} val\r\n * @returns {string}\r\n */\r\nexport function html2Text(val) {\r\n  const div = document.createElement('div')\r\n  div.innerHTML = val\r\n  return div.textContent || div.innerText\r\n}\r\n\r\n/**\r\n * Merges two objects, giving the last one precedence\r\n * @param {Object} target\r\n * @param {(Object|Array)} source\r\n * @returns {Object}\r\n */\r\nexport function objectMerge(target, source) {\r\n  if (typeof target !== 'object') {\r\n    target = {}\r\n  }\r\n  if (Array.isArray(source)) {\r\n    return source.slice()\r\n  }\r\n  Object.keys(source).forEach(property => {\r\n    const sourceProperty = source[property]\r\n    if (typeof sourceProperty === 'object') {\r\n      target[property] = objectMerge(target[property], sourceProperty)\r\n    } else {\r\n      target[property] = sourceProperty\r\n    }\r\n  })\r\n  return target\r\n}\r\n\r\n/**\r\n * @param {HTMLElement} element\r\n * @param {string} className\r\n */\r\nexport function toggleClass(element, className) {\r\n  if (!element || !className) {\r\n    return\r\n  }\r\n  let classString = element.className\r\n  const nameIndex = classString.indexOf(className)\r\n  if (nameIndex === -1) {\r\n    classString += '' + className\r\n  } else {\r\n    classString =\r\n      classString.substr(0, nameIndex) +\r\n      classString.substr(nameIndex + className.length)\r\n  }\r\n  element.className = classString\r\n}\r\n\r\n/**\r\n * @param {string} type\r\n * @returns {Date}\r\n */\r\nexport function getTime(type) {\r\n  if (type === 'start') {\r\n    return new Date().getTime() - 3600 * 1000 * 24 * 90\r\n  } else {\r\n    return new Date(new Date().toDateString())\r\n  }\r\n}\r\n\r\n/**\r\n * @param {Function} func\r\n * @param {number} wait\r\n * @param {boolean} immediate\r\n * @return {*}\r\n */\r\nexport function debounce(func, wait, immediate) {\r\n  let timeout, args, context, timestamp, result\r\n\r\n  const later = function() {\r\n    // 据上一次触发时间间隔\r\n    const last = +new Date() - timestamp\r\n\r\n    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait\r\n    if (last < wait && last > 0) {\r\n      timeout = setTimeout(later, wait - last)\r\n    } else {\r\n      timeout = null\r\n      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用\r\n      if (!immediate) {\r\n        result = func.apply(context, args)\r\n        if (!timeout) context = args = null\r\n      }\r\n    }\r\n  }\r\n\r\n  return function(...args) {\r\n    context = this\r\n    timestamp = +new Date()\r\n    const callNow = immediate && !timeout\r\n    // 如果延时不存在，重新设定延时\r\n    if (!timeout) timeout = setTimeout(later, wait)\r\n    if (callNow) {\r\n      result = func.apply(context, args)\r\n      context = args = null\r\n    }\r\n\r\n    return result\r\n  }\r\n}\r\n\r\n/**\r\n * This is just a simple version of deep copy\r\n * Has a lot of edge cases bug\r\n * If you want to use a perfect deep copy, use lodash's _.cloneDeep\r\n * @param {Object} source\r\n * @returns {Object}\r\n */\r\nexport function deepClone(source) {\r\n  if (!source && typeof source !== 'object') {\r\n    throw new Error('error arguments', 'deepClone')\r\n  }\r\n  const targetObj = source.constructor === Array ? [] : {}\r\n  Object.keys(source).forEach(keys => {\r\n    if (source[keys] && typeof source[keys] === 'object') {\r\n      targetObj[keys] = deepClone(source[keys])\r\n    } else {\r\n      targetObj[keys] = source[keys]\r\n    }\r\n  })\r\n  return targetObj\r\n}\r\n\r\n/**\r\n * @param {Array} arr\r\n * @returns {Array}\r\n */\r\nexport function uniqueArr(arr) {\r\n  return Array.from(new Set(arr))\r\n}\r\n\r\n/**\r\n * @returns {string}\r\n */\r\nexport function createUniqueString() {\r\n  const timestamp = +new Date() + ''\r\n  const randomNum = parseInt((1 + Math.random()) * 65536) + ''\r\n  return (+(randomNum + timestamp)).toString(32)\r\n}\r\n\r\n/**\r\n * Check if an element has a class\r\n * @param {HTMLElement} elm\r\n * @param {string} cls\r\n * @returns {boolean}\r\n */\r\nexport function hasClass(ele, cls) {\r\n  return !!ele.className.match(new RegExp('(\\\\s|^)' + cls + '(\\\\s|$)'))\r\n}\r\n\r\n/**\r\n * Add class to element\r\n * @param {HTMLElement} elm\r\n * @param {string} cls\r\n */\r\nexport function addClass(ele, cls) {\r\n  if (!hasClass(ele, cls)) ele.className += ' ' + cls\r\n}\r\n\r\n/**\r\n * Remove class from element\r\n * @param {HTMLElement} elm\r\n * @param {string} cls\r\n */\r\nexport function removeClass(ele, cls) {\r\n  if (hasClass(ele, cls)) {\r\n    const reg = new RegExp('(\\\\s|^)' + cls + '(\\\\s|$)')\r\n    ele.className = ele.className.replace(reg, ' ')\r\n  }\r\n}\r\n\r\nexport function loadFile(url) {\r\n  const xhr = new XMLHttpRequest()\r\n  const okStatus = document.location.protocol === 'file:' ? 0 : 200\r\n  xhr.open('GET', url, false)\r\n  xhr.overrideMimeType('text/html;charset=utf-8')// 默认为utf-8\r\n  xhr.send(null)\r\n  return xhr.status === okStatus ? xhr.responseText : null\r\n}\r\n\r\n/**\r\n * 动态添加路由\r\n * @param {Array} addPage\r\n * @param {String} parentName\r\n * @param {String} currentPath\r\n */\r\nexport function handleAddRouterPage(addPage, parentName, currentPath) {\r\n  // addPage.map((item) => {\r\n  //   if (item.path.includes(packageName)) {\r\n  //     return item\r\n  //   } else {\r\n  //     item.path = '/' + packageName + (item.path.startsWith('/') ? '' : '/') + item.path\r\n  //     return item\r\n  //   }\r\n  // })\r\n  // addPage.forEach((element, idx) => {\r\n  //   if (router.getRoutes().findIndex(v => v.name === element.name) === -1) {\r\n  //     console.log('路由',element.meta.activeMenu)\r\n  //     element.meta.activeMenu = element.meta.activeMenu || currentPath\r\n  //     element.meta.noCache = true\r\n  //     router.addRoute(parentName, element)\r\n  //   }\r\n  // })\r\n\r\n  addPage.map((item) => {\r\n    item.path = item.path.startsWith('/') ? item.path : '/' + item.path\r\n    return item\r\n  })\r\n  addPage.forEach((element, idx) => {\r\n    if (router.getRoutes().findIndex(v => v.name === element.name) === -1) {\r\n      element.meta.activeMenu = currentPath\r\n      element.meta.noCache = true\r\n      router.addRoute(parentName, element)\r\n    }\r\n  })\r\n}\r\n\r\n/**\r\n * 时间差\r\n * @param {String} start\r\n * @param {String} end\r\n */\r\nexport function DateDiff(start, end) {\r\n  start = new Date(start)\r\n  end = new Date(end)\r\n  let days = ''\r\n  if (end > start) {\r\n    days = parseInt(Math.abs(end - start) / 1000 / 60 / 60 / 24)\r\n  } else {\r\n    days = '0'\r\n  }\r\n  return days + 1\r\n}\r\n\r\nexport function combineURL(baseURL, relativeURL) {\r\n  return relativeURL ? baseURL.replace(/\\/+$/, '') + '/' + relativeURL.replace(/^\\/+/, '') : baseURL\r\n}\r\n/**\r\n * Parse the time to string\r\n * @param {(Object)} $store\r\n * @param {(Object)} $route\r\n * @param {Object} $router\r\n * @returns null\r\n */\r\nexport function closeTagView($store, $route) {\r\n  const qiankun = Vue.prototype.$qiankun\r\n  if ($route && $route.query && $route.query.pg_redirect) {\r\n    qiankun.switchMicroAppRouteTagClose($route.query.pg_redirect, $route.name)\r\n  } else {\r\n    if ($route.name === 'Dashboard') {\r\n      router.replace({ path: '/redirect' + $route.fullPath })\r\n    } else {\r\n      router.push('/')\r\n    }\r\n  }\r\n  // $store.dispatch('tagsView/delView', $route).then(({ visitedViews }) => {\r\n  //   const latestView = visitedViews.slice(-1)[0]\r\n  //   if (latestView) {\r\n  //     router.push(latestView.fullPath)\r\n  //   } else {\r\n  //     if ($route.name === 'Dashboard') {\r\n  //       router.replace({ path: '/redirect' + $route.fullPath })\r\n  //     } else {\r\n  //       router.push('/')\r\n  //     }\r\n  //   }\r\n  // })\r\n}\r\n\r\n/**\r\n * 获取某天日期，dayNumber = 7，表示获取7天后日期，-7代表7天前\r\n * @param dayNumber\r\n * @returns {Date}\r\n */\r\nexport function getSomeDate(dayNumber, date1 = new Date()) {\r\n  const date2 = new Date(date1)\r\n  date2.setDate(date1.getDate() + dayNumber)\r\n  return date2\r\n}\r\n\r\nexport function fmtDate(v, unit) {\r\n  let str = 'YYYY/MM/DD'\r\n  if (unit) {\r\n    str = unit\r\n  }\r\n  return v ? moment(v).format(str) : ''\r\n}\r\n\r\n/**\r\n * 计算两个日期相差的天数\r\n * @param startDate\r\n * @param enDate\r\n * @returns {number}\r\n */\r\nexport function getDaysBetween(startDate, enDate) {\r\n  const sDate = new Date(startDate).getTime()\r\n  const eDate = new Date(enDate).getTime()\r\n  if (sDate > eDate) return 0 // 开始日期大于结束日期，返回0\r\n  if (sDate === eDate) return 1 // 如果日期相同 返回一天\r\n  return Math.ceil((eDate - sDate) / (1 * 24 * 60 * 60 * 1000))\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,MAAM,MAAM,QAAQ;AAC3B,IAAMC,WAAW,GAAGC,OAAO,CAAC,eAAe,CAAC,CAACC,IAAI;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACvC,IAAIC,SAAS,CAACC,MAAM,KAAK,CAAC,IAAI,CAACH,IAAI,EAAE;IACnC,OAAO,IAAI;EACb;EACA,IAAMI,MAAM,GAAGH,OAAO,IAAI,yBAAyB;EACnD,IAAII,IAAI;EACR,IAAIC,OAAA,CAAON,IAAI,MAAK,QAAQ,EAAE;IAC5BK,IAAI,GAAGL,IAAI;EACb,CAAC,MAAM;IACL,IAAK,OAAOA,IAAI,KAAK,QAAQ,EAAG;MAC9B,IAAK,UAAU,CAACO,IAAI,CAACP,IAAI,CAAC,EAAG;QAC3B;QACAA,IAAI,GAAGQ,QAAQ,CAACR,IAAI,CAAC;MACvB,CAAC,MAAM;QACL;QACA;QACAA,IAAI,GAAGA,IAAI,CAACS,OAAO,CAAC,IAAIC,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC;MAC7C;IACF;IAEA,IAAK,OAAOV,IAAI,KAAK,QAAQ,IAAMA,IAAI,CAACW,QAAQ,CAAC,CAAC,CAACR,MAAM,KAAK,EAAG,EAAE;MACjEH,IAAI,GAAGA,IAAI,GAAG,IAAI;IACpB;IACAK,IAAI,GAAG,IAAIO,IAAI,CAACZ,IAAI,CAAC;EACvB;EACA,IAAMa,SAAS,GAAG;IAChBC,CAAC,EAAET,IAAI,CAACU,WAAW,CAAC,CAAC;IACrBC,CAAC,EAAEX,IAAI,CAACY,QAAQ,CAAC,CAAC,GAAG,CAAC;IACtBC,CAAC,EAAEb,IAAI,CAACc,OAAO,CAAC,CAAC;IACjBC,CAAC,EAAEf,IAAI,CAACgB,QAAQ,CAAC,CAAC;IAClBC,CAAC,EAAEjB,IAAI,CAACkB,UAAU,CAAC,CAAC;IACpBC,CAAC,EAAEnB,IAAI,CAACoB,UAAU,CAAC,CAAC;IACpBC,CAAC,EAAErB,IAAI,CAACsB,MAAM,CAAC;EACjB,CAAC;EACD,IAAMC,QAAQ,GAAGxB,MAAM,CAACK,OAAO,CAAC,iBAAiB,EAAE,UAACoB,MAAM,EAAEC,GAAG,EAAK;IAClE,IAAMC,KAAK,GAAGlB,SAAS,CAACiB,GAAG,CAAC;IAC5B;IACA,IAAIA,GAAG,KAAK,GAAG,EAAE;MAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAACC,KAAK,CAAE;IAAC;IACtE,OAAOA,KAAK,CAACpB,QAAQ,CAAC,CAAC,CAACqB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAC1C,CAAC,CAAC;EACF,OAAOJ,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,UAAUA,CAACjC,IAAI,EAAEkC,MAAM,EAAE;EACvC,IAAI,CAAC,EAAE,GAAGlC,IAAI,EAAEG,MAAM,KAAK,EAAE,EAAE;IAC7BH,IAAI,GAAGQ,QAAQ,CAACR,IAAI,CAAC,GAAG,IAAI;EAC9B,CAAC,MAAM;IACLA,IAAI,GAAG,CAACA,IAAI;EACd;EACA,IAAMkB,CAAC,GAAG,IAAIN,IAAI,CAACZ,IAAI,CAAC;EACxB,IAAMmC,GAAG,GAAGvB,IAAI,CAACuB,GAAG,CAAC,CAAC;EAEtB,IAAMC,IAAI,GAAG,CAACD,GAAG,GAAGjB,CAAC,IAAI,IAAI;EAE7B,IAAIkB,IAAI,GAAG,EAAE,EAAE;IACb,OAAO,IAAI;EACb,CAAC,MAAM,IAAIA,IAAI,GAAG,IAAI,EAAE;IACtB;IACA,OAAOC,IAAI,CAACC,IAAI,CAACF,IAAI,GAAG,EAAE,CAAC,GAAG,KAAK;EACrC,CAAC,MAAM,IAAIA,IAAI,GAAG,IAAI,GAAG,EAAE,EAAE;IAC3B,OAAOC,IAAI,CAACC,IAAI,CAACF,IAAI,GAAG,IAAI,CAAC,GAAG,KAAK;EACvC,CAAC,MAAM,IAAIA,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,CAAC,EAAE;IAC/B,OAAO,KAAK;EACd;EACA,IAAIF,MAAM,EAAE;IACV,OAAOnC,SAAS,CAACC,IAAI,EAAEkC,MAAM,CAAC;EAChC,CAAC,MAAM;IACL,OACEhB,CAAC,CAACD,QAAQ,CAAC,CAAC,GACZ,CAAC,GACD,GAAG,GACHC,CAAC,CAACC,OAAO,CAAC,CAAC,GACX,GAAG,GACHD,CAAC,CAACG,QAAQ,CAAC,CAAC,GACZ,GAAG,GACHH,CAAC,CAACK,UAAU,CAAC,CAAC,GACd,GAAG;EAEP;AACF;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASgB,cAAcA,CAACC,GAAG,EAAE;EAClCA,GAAG,GAAGA,GAAG,IAAI,IAAI,GAAGC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGH,GAAG;EAC9C,IAAMI,MAAM,GAAGJ,GAAG,CAACK,SAAS,CAACL,GAAG,CAACM,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACtD,IAAMC,GAAG,GAAG,CAAC,CAAC;EACd,IAAMC,GAAG,GAAG,sBAAsB;EAClCJ,MAAM,CAACnC,OAAO,CAACuC,GAAG,EAAE,UAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAK;IAClC,IAAMrD,IAAI,GAAGsD,kBAAkB,CAACF,EAAE,CAAC;IACnC,IAAIG,GAAG,GAAGD,kBAAkB,CAACD,EAAE,CAAC;IAChCE,GAAG,GAAGC,MAAM,CAACD,GAAG,CAAC;IACjBN,GAAG,CAACjD,IAAI,CAAC,GAAGuD,GAAG;IACf,OAAOJ,EAAE;EACX,CAAC,CAAC;EACF,OAAOF,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASQ,UAAUA,CAACC,GAAG,EAAE;EAC9B;EACA,IAAIhC,CAAC,GAAGgC,GAAG,CAACrD,MAAM;EAClB,KAAK,IAAImB,CAAC,GAAGkC,GAAG,CAACrD,MAAM,GAAG,CAAC,EAAEmB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACxC,IAAMmC,IAAI,GAAGD,GAAG,CAACE,UAAU,CAACpC,CAAC,CAAC;IAC9B,IAAImC,IAAI,GAAG,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAEjC,CAAC,EAAE,MAChC,IAAIiC,IAAI,GAAG,KAAK,IAAIA,IAAI,IAAI,MAAM,EAAEjC,CAAC,IAAI,CAAC;IAC/C,IAAIiC,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM,EAAEnC,CAAC,EAAE;EAC3C;EACA,OAAOE,CAAC;AACV;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASmC,UAAUA,CAACC,MAAM,EAAE;EACjC,IAAMC,QAAQ,GAAG,EAAE;EACnB,KAAK,IAAIvC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsC,MAAM,CAACzD,MAAM,EAAEmB,CAAC,EAAE,EAAE;IACtC,IAAIsC,MAAM,CAACtC,CAAC,CAAC,EAAE;MACbuC,QAAQ,CAACC,IAAI,CAACF,MAAM,CAACtC,CAAC,CAAC,CAAC;IAC1B;EACF;EACA,OAAOuC,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,KAAKA,CAACC,IAAI,EAAE;EAC1B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB,OAAOL,UAAU,CACfM,MAAM,CAACC,IAAI,CAACF,IAAI,CAAC,CAACG,GAAG,CAAC,UAAArC,GAAG,EAAI;IAC3B,IAAIkC,IAAI,CAAClC,GAAG,CAAC,KAAKsC,SAAS,EAAE,OAAO,EAAE;IACtC,OAAOC,kBAAkB,CAACvC,GAAG,CAAC,GAAG,GAAG,GAAGuC,kBAAkB,CAACL,IAAI,CAAClC,GAAG,CAAC,CAAC;EACtE,CAAC,CACH,CAAC,CAACwC,IAAI,CAAC,GAAG,CAAC;AACb;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAAC/B,GAAG,EAAE;EAC7B,IAAMI,MAAM,GAAGQ,kBAAkB,CAACZ,GAAG,CAACgC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC/D,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EACxE,IAAI,CAACmC,MAAM,EAAE;IACX,OAAO,CAAC,CAAC;EACX;EACA,IAAMG,GAAG,GAAG,CAAC,CAAC;EACd,IAAM0B,SAAS,GAAG7B,MAAM,CAAC4B,KAAK,CAAC,GAAG,CAAC;EACnCC,SAAS,CAACC,OAAO,CAAC,UAAAC,CAAC,EAAI;IACrB,IAAMC,KAAK,GAAGD,CAAC,CAACE,OAAO,CAAC,GAAG,CAAC;IAC5B,IAAID,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAM9E,IAAI,GAAG6E,CAAC,CAAC9B,SAAS,CAAC,CAAC,EAAE+B,KAAK,CAAC;MAClC,IAAMvB,GAAG,GAAGsB,CAAC,CAAC9B,SAAS,CAAC+B,KAAK,GAAG,CAAC,EAAED,CAAC,CAACxE,MAAM,CAAC;MAC5C4C,GAAG,CAACjD,IAAI,CAAC,GAAGuD,GAAG;IACjB;EACF,CAAC,CAAC;EACF,OAAON,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAAS+B,SAASA,CAACzB,GAAG,EAAE;EAC7B,IAAM0B,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EACzCF,GAAG,CAACG,SAAS,GAAG7B,GAAG;EACnB,OAAO0B,GAAG,CAACI,WAAW,IAAIJ,GAAG,CAACK,SAAS;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC1C,IAAIjF,OAAA,CAAOgF,MAAM,MAAK,QAAQ,EAAE;IAC9BA,MAAM,GAAG,CAAC,CAAC;EACb;EACA,IAAIE,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;IACzB,OAAOA,MAAM,CAACG,KAAK,CAAC,CAAC;EACvB;EACAzB,MAAM,CAACC,IAAI,CAACqB,MAAM,CAAC,CAACb,OAAO,CAAC,UAAAiB,QAAQ,EAAI;IACtC,IAAMC,cAAc,GAAGL,MAAM,CAACI,QAAQ,CAAC;IACvC,IAAIrF,OAAA,CAAOsF,cAAc,MAAK,QAAQ,EAAE;MACtCN,MAAM,CAACK,QAAQ,CAAC,GAAGN,WAAW,CAACC,MAAM,CAACK,QAAQ,CAAC,EAAEC,cAAc,CAAC;IAClE,CAAC,MAAM;MACLN,MAAM,CAACK,QAAQ,CAAC,GAAGC,cAAc;IACnC;EACF,CAAC,CAAC;EACF,OAAON,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASO,WAAWA,CAACC,OAAO,EAAEC,SAAS,EAAE;EAC9C,IAAI,CAACD,OAAO,IAAI,CAACC,SAAS,EAAE;IAC1B;EACF;EACA,IAAIC,WAAW,GAAGF,OAAO,CAACC,SAAS;EACnC,IAAME,SAAS,GAAGD,WAAW,CAACnB,OAAO,CAACkB,SAAS,CAAC;EAChD,IAAIE,SAAS,KAAK,CAAC,CAAC,EAAE;IACpBD,WAAW,IAAI,EAAE,GAAGD,SAAS;EAC/B,CAAC,MAAM;IACLC,WAAW,GACTA,WAAW,CAACE,MAAM,CAAC,CAAC,EAAED,SAAS,CAAC,GAChCD,WAAW,CAACE,MAAM,CAACD,SAAS,GAAGF,SAAS,CAAC5F,MAAM,CAAC;EACpD;EACA2F,OAAO,CAACC,SAAS,GAAGC,WAAW;AACjC;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASG,OAAOA,CAACC,IAAI,EAAE;EAC5B,IAAIA,IAAI,KAAK,OAAO,EAAE;IACpB,OAAO,IAAIxF,IAAI,CAAC,CAAC,CAACuF,OAAO,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE;EACrD,CAAC,MAAM;IACL,OAAO,IAAIvF,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACyF,YAAY,CAAC,CAAC,CAAC;EAC5C;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAEC,SAAS,EAAE;EAC9C,IAAIC,OAAO,EAAEC,IAAI,EAAEC,OAAO,EAAEC,SAAS,EAAEhF,MAAM;EAE7C,IAAMiF,MAAK,GAAG,SAARA,KAAKA,CAAA,EAAc;IACvB;IACA,IAAMC,IAAI,GAAG,CAAC,IAAInG,IAAI,CAAC,CAAC,GAAGiG,SAAS;;IAEpC;IACA,IAAIE,IAAI,GAAGP,IAAI,IAAIO,IAAI,GAAG,CAAC,EAAE;MAC3BL,OAAO,GAAGM,UAAU,CAACF,MAAK,EAAEN,IAAI,GAAGO,IAAI,CAAC;IAC1C,CAAC,MAAM;MACLL,OAAO,GAAG,IAAI;MACd;MACA,IAAI,CAACD,SAAS,EAAE;QACd5E,MAAM,GAAG0E,IAAI,CAACU,KAAK,CAACL,OAAO,EAAED,IAAI,CAAC;QAClC,IAAI,CAACD,OAAO,EAAEE,OAAO,GAAGD,IAAI,GAAG,IAAI;MACrC;IACF;EACF,CAAC;EAED,OAAO,YAAkB;IAAA,SAAAO,IAAA,GAAAhH,SAAA,CAAAC,MAAA,EAANwG,IAAI,OAAAnB,KAAA,CAAA0B,IAAA,GAAAC,IAAA,MAAAA,IAAA,GAAAD,IAAA,EAAAC,IAAA;MAAJR,IAAI,CAAAQ,IAAA,IAAAjH,SAAA,CAAAiH,IAAA;IAAA;IACrBP,OAAO,GAAG,IAAI;IACdC,SAAS,GAAG,CAAC,IAAIjG,IAAI,CAAC,CAAC;IACvB,IAAMwG,OAAO,GAAGX,SAAS,IAAI,CAACC,OAAO;IACrC;IACA,IAAI,CAACA,OAAO,EAAEA,OAAO,GAAGM,UAAU,CAACF,MAAK,EAAEN,IAAI,CAAC;IAC/C,IAAIY,OAAO,EAAE;MACXvF,MAAM,GAAG0E,IAAI,CAACU,KAAK,CAACL,OAAO,EAAED,IAAI,CAAC;MAClCC,OAAO,GAAGD,IAAI,GAAG,IAAI;IACvB;IAEA,OAAO9E,MAAM;EACf,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASwF,SAASA,CAAC9B,MAAM,EAAE;EAChC,IAAI,CAACA,MAAM,IAAIjF,OAAA,CAAOiF,MAAM,MAAK,QAAQ,EAAE;IACzC,MAAM,IAAI+B,KAAK,CAAC,iBAAiB,EAAE,WAAW,CAAC;EACjD;EACA,IAAMC,SAAS,GAAGhC,MAAM,CAACiC,WAAW,KAAKhC,KAAK,GAAG,EAAE,GAAG,CAAC,CAAC;EACxDvB,MAAM,CAACC,IAAI,CAACqB,MAAM,CAAC,CAACb,OAAO,CAAC,UAAAR,IAAI,EAAI;IAClC,IAAIqB,MAAM,CAACrB,IAAI,CAAC,IAAI5D,OAAA,CAAOiF,MAAM,CAACrB,IAAI,CAAC,MAAK,QAAQ,EAAE;MACpDqD,SAAS,CAACrD,IAAI,CAAC,GAAGmD,SAAS,CAAC9B,MAAM,CAACrB,IAAI,CAAC,CAAC;IAC3C,CAAC,MAAM;MACLqD,SAAS,CAACrD,IAAI,CAAC,GAAGqB,MAAM,CAACrB,IAAI,CAAC;IAChC;EACF,CAAC,CAAC;EACF,OAAOqD,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,SAASA,CAACC,GAAG,EAAE;EAC7B,OAAOlC,KAAK,CAACmC,IAAI,CAAC,IAAIC,GAAG,CAACF,GAAG,CAAC,CAAC;AACjC;;AAEA;AACA;AACA;AACA,OAAO,SAASG,kBAAkBA,CAAA,EAAG;EACnC,IAAMhB,SAAS,GAAG,CAAC,IAAIjG,IAAI,CAAC,CAAC,GAAG,EAAE;EAClC,IAAMkH,SAAS,GAAGtH,QAAQ,CAAC,CAAC,CAAC,GAAG6B,IAAI,CAAC0F,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,EAAE;EAC5D,OAAO,CAAC,EAAED,SAAS,GAAGjB,SAAS,CAAC,EAAElG,QAAQ,CAAC,EAAE,CAAC;AAChD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASqH,QAAQA,CAACC,GAAG,EAAEC,GAAG,EAAE;EACjC,OAAO,CAAC,CAACD,GAAG,CAAClC,SAAS,CAACoC,KAAK,CAAC,IAAIzH,MAAM,CAAC,SAAS,GAAGwH,GAAG,GAAG,SAAS,CAAC,CAAC;AACvE;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,QAAQA,CAACH,GAAG,EAAEC,GAAG,EAAE;EACjC,IAAI,CAACF,QAAQ,CAACC,GAAG,EAAEC,GAAG,CAAC,EAAED,GAAG,CAAClC,SAAS,IAAI,GAAG,GAAGmC,GAAG;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,WAAWA,CAACJ,GAAG,EAAEC,GAAG,EAAE;EACpC,IAAIF,QAAQ,CAACC,GAAG,EAAEC,GAAG,CAAC,EAAE;IACtB,IAAMlF,GAAG,GAAG,IAAItC,MAAM,CAAC,SAAS,GAAGwH,GAAG,GAAG,SAAS,CAAC;IACnDD,GAAG,CAAClC,SAAS,GAAGkC,GAAG,CAAClC,SAAS,CAACtF,OAAO,CAACuC,GAAG,EAAE,GAAG,CAAC;EACjD;AACF;AAEA,OAAO,SAASsF,QAAQA,CAAC9F,GAAG,EAAE;EAC5B,IAAM+F,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;EAChC,IAAMC,QAAQ,GAAGzD,QAAQ,CAACtC,QAAQ,CAACgG,QAAQ,KAAK,OAAO,GAAG,CAAC,GAAG,GAAG;EACjEH,GAAG,CAACI,IAAI,CAAC,KAAK,EAAEnG,GAAG,EAAE,KAAK,CAAC;EAC3B+F,GAAG,CAACK,gBAAgB,CAAC,yBAAyB,CAAC;EAC/CL,GAAG,CAACM,IAAI,CAAC,IAAI,CAAC;EACd,OAAON,GAAG,CAACO,MAAM,KAAKL,QAAQ,GAAGF,GAAG,CAACQ,YAAY,GAAG,IAAI;AAC1D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CAACC,OAAO,EAAEC,UAAU,EAAEC,WAAW,EAAE;EACpE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAF,OAAO,CAAC9E,GAAG,CAAC,UAACiF,IAAI,EAAK;IACpBA,IAAI,CAACC,IAAI,GAAGD,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC,GAAG,CAAC,GAAGF,IAAI,CAACC,IAAI,GAAG,GAAG,GAAGD,IAAI,CAACC,IAAI;IACnE,OAAOD,IAAI;EACb,CAAC,CAAC;EACFH,OAAO,CAACvE,OAAO,CAAC,UAACoB,OAAO,EAAEyD,GAAG,EAAK;IAChC,IAAI7J,MAAM,CAAC8J,SAAS,CAAC,CAAC,CAACC,SAAS,CAAC,UAAA9E,CAAC;MAAA,OAAIA,CAAC,CAAC7E,IAAI,KAAKgG,OAAO,CAAChG,IAAI;IAAA,EAAC,KAAK,CAAC,CAAC,EAAE;MACrEgG,OAAO,CAAC4D,IAAI,CAACC,UAAU,GAAGR,WAAW;MACrCrD,OAAO,CAAC4D,IAAI,CAACE,OAAO,GAAG,IAAI;MAC3BlK,MAAM,CAACmK,QAAQ,CAACX,UAAU,EAAEpD,OAAO,CAAC;IACtC;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASgE,QAAQA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACnCD,KAAK,GAAG,IAAInJ,IAAI,CAACmJ,KAAK,CAAC;EACvBC,GAAG,GAAG,IAAIpJ,IAAI,CAACoJ,GAAG,CAAC;EACnB,IAAIC,IAAI,GAAG,EAAE;EACb,IAAID,GAAG,GAAGD,KAAK,EAAE;IACfE,IAAI,GAAGzJ,QAAQ,CAAC6B,IAAI,CAAC6H,GAAG,CAACF,GAAG,GAAGD,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EAC9D,CAAC,MAAM;IACLE,IAAI,GAAG,GAAG;EACZ;EACA,OAAOA,IAAI,GAAG,CAAC;AACjB;AAEA,OAAO,SAASE,UAAUA,CAACC,OAAO,EAAEC,WAAW,EAAE;EAC/C,OAAOA,WAAW,GAAGD,OAAO,CAAC3J,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG4J,WAAW,CAAC5J,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG2J,OAAO;AACpG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,YAAYA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC3C,IAAMC,OAAO,GAAGhL,GAAG,CAACiL,SAAS,CAACC,QAAQ;EACtC,IAAIH,MAAM,IAAIA,MAAM,CAACI,KAAK,IAAIJ,MAAM,CAACI,KAAK,CAACC,WAAW,EAAE;IACtDJ,OAAO,CAACK,2BAA2B,CAACN,MAAM,CAACI,KAAK,CAACC,WAAW,EAAEL,MAAM,CAAC1K,IAAI,CAAC;EAC5E,CAAC,MAAM;IACL,IAAI0K,MAAM,CAAC1K,IAAI,KAAK,WAAW,EAAE;MAC/BJ,MAAM,CAACe,OAAO,CAAC;QAAE4I,IAAI,EAAE,WAAW,GAAGmB,MAAM,CAACO;MAAS,CAAC,CAAC;IACzD,CAAC,MAAM;MACLrL,MAAM,CAACoE,IAAI,CAAC,GAAG,CAAC;IAClB;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASkH,WAAWA,CAACC,SAAS,EAAsB;EAAA,IAApBC,KAAK,GAAAhL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAkE,SAAA,GAAAlE,SAAA,MAAG,IAAIU,IAAI,CAAC,CAAC;EACvD,IAAMuK,KAAK,GAAG,IAAIvK,IAAI,CAACsK,KAAK,CAAC;EAC7BC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC/J,OAAO,CAAC,CAAC,GAAG8J,SAAS,CAAC;EAC1C,OAAOE,KAAK;AACd;AAEA,OAAO,SAASE,OAAOA,CAAC1G,CAAC,EAAE2G,IAAI,EAAE;EAC/B,IAAI9H,GAAG,GAAG,YAAY;EACtB,IAAI8H,IAAI,EAAE;IACR9H,GAAG,GAAG8H,IAAI;EACZ;EACA,OAAO3G,CAAC,GAAGhF,MAAM,CAACgF,CAAC,CAAC,CAACvE,MAAM,CAACoD,GAAG,CAAC,GAAG,EAAE;AACvC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS+H,cAAcA,CAACC,SAAS,EAAEC,MAAM,EAAE;EAChD,IAAMC,KAAK,GAAG,IAAI9K,IAAI,CAAC4K,SAAS,CAAC,CAACrF,OAAO,CAAC,CAAC;EAC3C,IAAMwF,KAAK,GAAG,IAAI/K,IAAI,CAAC6K,MAAM,CAAC,CAACtF,OAAO,CAAC,CAAC;EACxC,IAAIuF,KAAK,GAAGC,KAAK,EAAE,OAAO,CAAC,EAAC;EAC5B,IAAID,KAAK,KAAKC,KAAK,EAAE,OAAO,CAAC,EAAC;EAC9B,OAAOtJ,IAAI,CAACC,IAAI,CAAC,CAACqJ,KAAK,GAAGD,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;AAC/D", "ignoreList": []}]}
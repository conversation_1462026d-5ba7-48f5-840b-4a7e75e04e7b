{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production\\draft.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production\\draft.vue", "mtime": 1757468113354}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["closeTagView", "debounce", "BatchProcessAdjust", "GetCompSchdulingInfoDetail", "GetPartSchdulingInfoDetail", "GetSchdulingWorkingTeams", "SaveComponentSchedulingWorkshop", "SaveCompSchdulingDraft", "SavePartSchdulingDraft", "SavePartSchedulingWorkshop", "SaveSchdulingTaskById", "AddDraft", "OwnerProcess", "Workshop", "GetGridByCode", "FIX_COLUMN", "uniqueCode", "v4", "uuidv4", "numeral", "GetLibListType", "GetProcessListBase", "AreaGetEntity", "mapActions", "mapGetters", "GetPartTypeList", "moment", "SPLIT_SYMBOL", "components", "data", "pickerOptions", "disabledDate", "time", "searchType", "formInline", "Schduling_Code", "Create_UserName", "Finish_Date", "Remark", "total", "columns", "tbData", "tbConfig", "TotalCount", "multipleSelection", "pgLoading", "deleteLoading", "workShopIsOpen", "isOwnerNull", "dialogVisible", "openAddDraft", "saveLoading", "tbLoading", "isCheckAll", "currentComponent", "dWidth", "title", "search", "pageType", "undefined", "tipLabel", "technologyOption", "typeOption", "workingTeam", "pageStatus", "scheduleId", "partComOwnerColumn", "watch", "handler", "n", "o", "checkOwner", "immediate", "computed", "_objectSpread", "isCom", "<PERSON><PERSON><PERSON><PERSON>", "isEdit", "isAdd", "beforeRouteEnter", "to", "from", "next", "query", "status", "meta", "mounted", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "initProcessList", "tbDataMap", "$route", "pg_type", "model", "pid", "localStorage", "getItem", "getType", "unique", "checkWorkshopIsOpen", "getAreaInfo", "fetchData", "mergeConfig", "stop", "methods", "every", "v", "Comp_Import_Detail_Id", "idx", "findIndex", "Code", "splice", "ownerColumn", "$message", "message", "type", "push", "_this2", "_callee2", "_callee2$", "_context2", "getConfig", "getWorkTeam", "_this3", "_callee3", "configCode", "_callee3$", "_context3", "getTableConfig", "workshopEnabled", "filter", "_this4", "_callee4", "resData", "_callee4$", "_context4", "getComPageList", "sent", "getPartPageList", "initTbData", "<PERSON><PERSON><PERSON><PERSON>", "$store", "tbSelectChange", "array", "records", "_this5", "id", "areaId", "then", "res", "IsSucceed", "_res$Data", "_res$Data2", "Data", "start", "Demand_Begin_Date", "end", "Demand_End_Date", "getTime", "Message", "handleClose", "_this6", "Promise", "resolve", "reject", "Schduling_Plan_Id", "_res$Data3", "Schduling_Plan", "Sch<PERSON>ling_Comps", "Process_List", "Object", "assign", "for<PERSON>ach", "item", "plist", "key", "Process_Code", "value", "changeProcessList", "_this7", "_res$Data4", "_res$Data5", "SarePartsModel", "map", "Scheduled_Used_Process", "Part_Used_Process", "list", "_this8", "teamKey", "arguments", "length", "row", "_row$Technology_Path", "processList", "Technology_Path", "split", "uuid", "addElementToTbData", "newData", "r", "p", "ele", "index", "code", "getRowUnique", "Working_Team_Id", "max", "getRowUniqueMax", "Count", "setInputMax", "mergeSelectList", "newList", "_this9", "console", "element", "cur", "getMergeUniqueRow", "pu<PERSON>", "<PERSON><PERSON><PERSON><PERSON>_Count", "chooseCount", "Schduled_Weight", "Weight", "format", "add", "sort", "a", "b", "initRowIndex", "timeEnd", "_element$Component_Co", "Comp_Code", "Component_Code", "Part_Code", "_element$Component_Co2", "checkForm", "isValidate", "$refs", "validate", "valid", "saveDraft", "_arguments", "_this0", "_callee5", "_this0$$refs$draft", "isOrder", "checkSuccess", "_this0$getSubmitTbInf", "tableData", "isSuccess", "_callee5$", "_context5", "abrupt", "getSubmitTbInfo", "handleSaveDraft", "log", "saveWorkShop", "_this1", "_callee6", "obj", "_this1$$route$query", "install", "projectId", "_fun", "_callee6$", "_context6", "Project_Id", "InstallUnit_Id", "Area_Id", "Schduling_Model", "_this10", "JSON", "parse", "stringify", "_loop", "i", "Scheduled_Technology_Path", "concat", "Array", "Set", "groups", "groupsList", "group", "uCode", "uMax", "Team_Task_Id", "Again_Count", "Working_Team_Name", "apply", "_toConsumableArray", "hasInput", "keys", "_", "startsWith", "Allocation_Teams", "_ret", "_this11", "_callee7", "obj<PERSON><PERSON>", "_this11$$route$query", "orderSuccess", "_callee7$", "_context7", "hasOwnProperty", "templateScheduleCode", "handleDelete", "_this12", "setTimeout", "selectedUuids", "isSelected", "has", "_item$Component_Code", "$nextTick", "_this12$$refs$draft", "mergeData", "_this13", "_callee8", "_callee8$", "_context8", "handleSubmit", "_this14", "_this14$getSubmitTbIn", "$confirm", "confirmButtonText", "cancelButtonText", "saveDraftDoSubmit", "catch", "_this15", "_callee9", "_this15$formInline", "_isSuccess", "_callee9$", "_context9", "doSubmit", "Id", "scheduleCode", "_this16", "schdulingPlanId", "finally", "getWorkShop", "_this17", "origin", "_value$workShop", "workShop", "Display_Name", "_value$workShop2", "Workshop_Name", "Workshop_Id", "set<PERSON>ath", "_value$workShop3", "Scheduled_Workshop_Id", "handleBatchWorkshop", "_this18", "handleAutoDeal", "_this19", "_callee0", "_callee0$", "_context0", "uniqueType", "Type", "codes", "all", "info", "setLibType", "hasUndefined", "some", "resetWorkTeamMax", "getProcessOption", "workshopId", "_this20", "process", "_this21", "Component_type", "workCode", "WorkCode", "replace", "inputChange", "inputValuesKeys", "endsWith", "val", "curCode", "otherTotal", "x", "reduce", "acc", "sendProcess", "_ref", "arr", "str", "originalPath", "_str", "_this22", "k", "$set", "$delete", "checkPermissionTeam", "processStr", "processCode", "_this23", "_callee1", "_callee1$", "_context1", "Grid", "ColumnList", "find", "ownerColumn2", "setColumnDisplay", "Is_Display", "includes", "fixed", "activeCellMethod", "_ref2", "_column$field", "column", "columnIndex", "field", "openBPADialog", "_this24", "IsUnique", "checkIsUniqueWorkshop", "setData", "isUnique", "firstV", "checkHasWorkShop", "hasWorkShop", "handleAddDialog", "_this25", "setPageData", "workingId", "handleSelectMenu", "handleBatchOwner", "_this26", "setOption", "handleReverse", "checked", "setAllCheckboxRow", "tbFilterChange", "_column$filters", "_this27", "xTable", "getColumnByField", "filters", "d", "updateData", "_this28", "Part_Grade", "label", "Name", "setProcessList"], "sources": ["src/views/PRO/plan-production/schedule-production/draft.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <el-card v-loading=\"pgLoading\" class=\"box-card h100\" element-loading-text=\"正在处理...\">\r\n      <h4 class=\"topTitle\"><span />基本信息</h4>\r\n      <el-form\r\n        ref=\"formInline\"\r\n        :inline=\"true\"\r\n        :model=\"formInline\"\r\n        class=\"demo-form-inline\"\r\n      >\r\n        <el-form-item v-if=\"!isAdd\" label=\"排产单号\" prop=\"Schduling_Code\">\r\n          <span v-if=\"isView\">{{ formInline.Status === 0 ? '' : formInline.Schduling_Code }}</span>\r\n          <el-input v-else v-model=\"formInline.Schduling_Code\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"计划员\" prop=\"Create_UserName\">\r\n          <span v-if=\"isView\">{{ formInline.Create_UserName }}</span>\r\n          <el-input\r\n            v-else\r\n            v-model=\"formInline.Create_UserName\"\r\n            disabled\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"要求完成时间\"\r\n          prop=\"Finish_Date\"\r\n          :rules=\"{ required: true, message: '请选择', trigger: 'change' }\"\r\n        >\r\n          <span v-if=\"isView\">{{ formInline.Finish_Date | timeFormat }}</span>\r\n          <el-date-picker\r\n            v-else\r\n            v-model=\"formInline.Finish_Date\"\r\n            :picker-options=\"pickerOptions\"\r\n            :disabled=\"isView\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            placeholder=\"选择日期\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"Remark\">\r\n          <span v-if=\"isView\">{{ formInline.Remark }}</span>\r\n          <el-input\r\n            v-else\r\n            v-model=\"formInline.Remark\"\r\n            :disabled=\"isView\"\r\n            style=\"width: 320px\"\r\n            placeholder=\"请输入\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-divider class=\"elDivder\" />\r\n      <div class=\"btn-x\">\r\n        <template v-if=\"!isView\">\r\n          <div>\r\n            <el-button type=\"primary\" @click=\"handleAddDialog()\">添加</el-button>\r\n            <el-button\r\n              v-if=\"workshopEnabled\"\r\n              :disabled=\"!multipleSelection.length\"\r\n              @click=\"handleBatchWorkshop(1)\"\r\n            >批量分配车间\r\n            </el-button>\r\n            <el-button\r\n              v-if=\"!isCom\"\r\n              :disabled=\"!multipleSelection.length\"\r\n              @click=\"handleSelectMenu('process')\"\r\n            >批量分配工序\r\n            </el-button>\r\n            <el-dropdown v-if=\"isCom\" style=\"margin:0 10px\" @command=\"handleSelectMenu\">\r\n              <el-button :disabled=\"!multipleSelection.length\" type=\"primary\" plain style=\"width: 140px\">\r\n                分配工序<i class=\"el-icon-arrow-down el-icon--right\" />\r\n              </el-button>\r\n              <el-dropdown-menu slot=\"dropdown\">\r\n                <el-dropdown-item\r\n                  command=\"process\"\r\n                >批量分配工序\r\n                </el-dropdown-item>\r\n                <el-dropdown-item\r\n                  v-if=\"isCom\"\r\n                  command=\"deal\"\r\n                >构件类型自动分配\r\n                </el-dropdown-item>\r\n              </el-dropdown-menu>\r\n            </el-dropdown>\r\n            <el-button\r\n              v-if=\"!isCom && !isOwnerNull\"\r\n              :disabled=\"!multipleSelection.length\"\r\n              @click=\"handleBatchOwner(1)\"\r\n            >批量分配领用工序\r\n            </el-button>\r\n            <el-button\r\n              plain\r\n              :disabled=\"!tbData.length\"\r\n              :loading=\"false\"\r\n              @click=\"handleReverse\"\r\n            >反选\r\n            </el-button>\r\n            <el-button\r\n              type=\"danger\"\r\n              plain\r\n              :loading=\"deleteLoading\"\r\n              :disabled=\"!multipleSelection.length\"\r\n              @click=\"handleDelete\"\r\n            >删除\r\n            </el-button>\r\n          </div>\r\n          <div v-if=\"!isCom\">\r\n            <el-select\r\n              v-model=\"searchType\"\r\n              placeholder=\"请选择\"\r\n              clearable\r\n              @change=\"tbFilterChange\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in typeOption\"\r\n                :key=\"item.value\"\r\n                :label=\"item.label\"\r\n                :value=\"item.label\"\r\n              />\r\n            </el-select>\r\n          </div>\r\n        </template>\r\n      </div>\r\n      <div class=\"tb-x\">\r\n        <vxe-table\r\n          ref=\"xTable\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          :checkbox-config=\"{checkField: 'checked'}\"\r\n          class=\"cs-vxe-table\"\r\n          :row-config=\"{isCurrent: true, isHover: true}\"\r\n          align=\"left\"\r\n          height=\"100%\"\r\n          :filter-config=\"{showIcon:false}\"\r\n          show-overflow\r\n          :loading=\"tbLoading\"\r\n          stripe\r\n          :scroll-y=\"{enabled: true, gt: 20}\"\r\n          size=\"medium\"\r\n          :edit-config=\"{\r\n            trigger: 'click',\r\n            mode: 'cell',\r\n            showIcon: !isView,\r\n            activeMethod: activeCellMethod,\r\n          }\"\r\n          :data=\"tbData\"\r\n          resizable\r\n          :tooltip-config=\"{ enterable: true }\"\r\n          @checkbox-all=\"tbSelectChange\"\r\n          @checkbox-change=\"tbSelectChange\"\r\n        >\r\n          <vxe-column v-if=\"!isView\" fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n          <template v-for=\"item in columns\">\r\n            <vxe-column\r\n              v-if=\"item.Code === 'Is_Component'\"\r\n              :key=\"item.Code\"\r\n              :align=\"item.Align\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              sortable\r\n              :width=\"item.Width\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                <el-tag\r\n                  :type=\"row.Is_Component ? 'danger' : 'success'\"\r\n                >{{ row.Is_Component ? '否' : '是' }}\r\n                </el-tag>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column\r\n              v-else-if=\"item.Code === 'Type_Name'\"\r\n              :key=\"item.Code\"\r\n              :align=\"item.Align\"\r\n              :field=\"item.Code\"\r\n              :filters=\"typeOption\"\r\n              :title=\"item.Display_Name\"\r\n              sortable\r\n              :width=\"item.Width\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                {{ row.Type_Name | displayValue }}\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column\r\n              v-else-if=\"item.Code === 'Technology_Path'\"\r\n              :key=\"item.Code\"\r\n              :align=\"item.Align\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              sortable\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                {{ row.Technology_Path | displayValue }}\r\n                <i\r\n                  v-if=\"!isView\"\r\n                  class=\"el-icon-edit\"\r\n                  @click=\"openBPADialog(2, row)\"\r\n                />\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column\r\n              v-else-if=\"item.Code === 'Part_Used_Process'\"\r\n              :key=\"item.Code\"\r\n              :align=\"item.Align\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              sortable\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                {{ row.Part_Used_Process | displayValue }}\r\n                <i\r\n                  v-if=\"!isView\"\r\n                  class=\"el-icon-edit\"\r\n                  @click=\"handleBatchOwner(2, row)\"\r\n                />\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column\r\n              v-else-if=\"item.Code === 'Workshop_Name'\"\r\n              :key=\"item.Code\"\r\n              :align=\"item.Align\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              sortable\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                {{ row.Workshop_Name | displayValue }}\r\n                <i\r\n                  v-if=\"!isView\"\r\n                  class=\"el-icon-edit\"\r\n                  @click=\"handleBatchWorkshop(2, row)\"\r\n                />\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column\r\n              v-else\r\n              :key=\"item.Id\"\r\n              :align=\"item.Align\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n            />\r\n          </template>\r\n\r\n        </vxe-table>\r\n      </div>\r\n      <el-divider v-if=\"!isView\" class=\"elDivder\" />\r\n      <footer v-if=\"!isView\">\r\n        <div class=\"data-info\">\r\n          <el-tag\r\n            size=\"medium\"\r\n            class=\"info-x\"\r\n          >已选 {{ multipleSelection.length }} 条数据\r\n          </el-tag>\r\n          <el-tag v-if=\"tipLabel\" size=\"medium\" class=\"info-x\">{{\r\n            tipLabel\r\n          }}\r\n          </el-tag>\r\n        </div>\r\n        <div>\r\n          <el-button v-if=\"workshopEnabled\" type=\"primary\" @click=\"saveWorkShop\">保存车间分配</el-button>\r\n          <el-button\r\n            type=\"primary\"\r\n            :loading=\"saveLoading\"\r\n            @click=\"saveDraft(false)\"\r\n          >保存草稿\r\n          </el-button>\r\n          <el-button :disabled=\"deleteLoading\" @click=\"handleSubmit\">下发任务</el-button>\r\n        </div>\r\n      </footer>\r\n    </el-card>\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"dWidth\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :process-list=\"processList\"\r\n        :page-type=\"pageType\"\r\n        :part-type-option=\"typeOption\"\r\n        @close=\"handleClose\"\r\n        @sendProcess=\"sendProcess\"\r\n        @workShop=\"getWorkShop\"\r\n        @refresh=\"fetchData\"\r\n        @setProcessList=\"setProcessList\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"openAddDraft\"\r\n      :width=\"dWidth\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <keep-alive>\r\n        <add-draft\r\n          ref=\"draft\"\r\n          :schedule-id=\"scheduleId\"\r\n          :show-dialog=\"openAddDraft\"\r\n          :page-type=\"pageType\"\r\n          @sendSelectList=\"mergeSelectList\"\r\n          @close=\"handleClose\"\r\n        />\r\n      </keep-alive>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { closeTagView, debounce } from '@/utils'\r\nimport BatchProcessAdjust from './components/BatchProcessAdjust'\r\nimport {\r\n  GetCompSchdulingInfoDetail,\r\n  GetPartSchdulingInfoDetail,\r\n  GetSchdulingWorkingTeams,\r\n  SaveComponentSchedulingWorkshop,\r\n  SaveCompSchdulingDraft,\r\n  SavePartSchdulingDraft,\r\n  SavePartSchedulingWorkshop,\r\n  SaveSchdulingTaskById\r\n} from '@/api/PRO/production-task'\r\nimport AddDraft from './components/addDraft'\r\nimport OwnerProcess from './components/OwnerProcess'\r\nimport Workshop from './components/Workshop.vue'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { FIX_COLUMN, uniqueCode } from '@/views/PRO/plan-production/schedule-production/constant'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport numeral from 'numeral'\r\nimport { GetLibListType, GetProcessListBase } from '@/api/PRO/technology-lib'\r\nimport { AreaGetEntity } from '@/api/plm/projects'\r\nimport { mapActions, mapGetters } from 'vuex'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\nimport * as moment from 'moment/moment'\r\n\r\nconst SPLIT_SYMBOL = '$_$'\r\nexport default {\r\n  components: { BatchProcessAdjust, AddDraft, Workshop, OwnerProcess },\r\n  data() {\r\n    return {\r\n      pickerOptions: {\r\n        disabledDate(time) {\r\n        }\r\n      },\r\n      searchType: '',\r\n      formInline: {\r\n        Schduling_Code: '',\r\n        Create_UserName: '',\r\n        Finish_Date: '',\r\n        Remark: ''\r\n      },\r\n      total: 0,\r\n      columns: [],\r\n      tbData: [],\r\n      tbConfig: {},\r\n      TotalCount: 0,\r\n      multipleSelection: [],\r\n      pgLoading: false,\r\n      deleteLoading: false,\r\n      workShopIsOpen: false,\r\n      isOwnerNull: false,\r\n      dialogVisible: false,\r\n      openAddDraft: false,\r\n      saveLoading: false,\r\n      tbLoading: false,\r\n      isCheckAll: false,\r\n      currentComponent: '',\r\n      dWidth: '25%',\r\n      title: '',\r\n      search: () => ({}),\r\n      pageType: undefined,\r\n      tipLabel: '',\r\n      technologyOption: [],\r\n      typeOption: [],\r\n      workingTeam: [],\r\n      pageStatus: undefined,\r\n      scheduleId: '',\r\n      partComOwnerColumn: null\r\n    }\r\n  },\r\n  watch: {\r\n    'tbData.length': {\r\n      handler(n, o) {\r\n        this.checkOwner()\r\n      },\r\n      immediate: false\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    },\r\n    isView() {\r\n      return this.pageStatus === 'view'\r\n    },\r\n    isEdit() {\r\n      return this.pageStatus === 'edit'\r\n    },\r\n    isAdd() {\r\n      return this.pageStatus === 'add'\r\n    },\r\n    ...mapGetters('factoryInfo', ['workshopEnabled']),\r\n    ...mapGetters('schedule', ['processList'])\r\n  },\r\n  beforeRouteEnter(to, from, next) {\r\n    if (to.query.status === 'view') {\r\n      to.meta.title = '查看'\r\n    } else {\r\n      to.meta.title = '草稿'\r\n    }\r\n    next()\r\n  },\r\n  async mounted() {\r\n    this.initProcessList()\r\n    this.tbDataMap = {}\r\n    this.pageType = this.$route.query.pg_type\r\n    this.pageStatus = this.$route.query.status\r\n    this.model = this.$route.query.model\r\n    this.scheduleId = this.$route.query.pid || ''\r\n    // this.formInline.Create_UserName = this.$store.getters.name\r\n    // 框架问题引起store数据丢失，已反馈，结果：此处先使用localStorage\r\n    this.formInline.Create_UserName = localStorage.getItem('UserAccount')\r\n    if (!this.isCom) {\r\n      this.getType()\r\n    } else {\r\n\r\n    }\r\n    this.unique = uniqueCode()\r\n    this.checkWorkshopIsOpen()\r\n    this.getAreaInfo()\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    await this.mergeConfig()\r\n    if (this.isView || this.isEdit) {\r\n      this.fetchData()\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions('schedule', ['changeProcessList', 'initProcessList']),\r\n    checkOwner() {\r\n      if (this.isCom) return\r\n      this.isOwnerNull = this.tbData.every(v => !v.Comp_Import_Detail_Id)\r\n      const idx = this.columns.findIndex(v => v.Code === 'Part_Used_Process')\r\n      if (this.isOwnerNull) {\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n      } else {\r\n        if (idx === -1) {\r\n          if (!this.ownerColumn) {\r\n            this.$message({\r\n              message: '列表配置字段缺少零件领用工序字段',\r\n              type: 'success'\r\n            })\r\n            return\r\n          }\r\n          this.columns.push(this.ownerColumn)\r\n        }\r\n      }\r\n    },\r\n    async mergeConfig() {\r\n      await this.getConfig()\r\n      await this.getWorkTeam()\r\n    },\r\n    async getConfig() {\r\n      const configCode = this.isCom ? (this.isView ? 'PROComViewPageTbConfig' : 'PROComDraftPageTbConfig') : (this.isView ? 'PROPartViewPageTbConfig' : 'PROPartDraftPageTbConfig')\r\n\r\n      await this.getTableConfig(configCode)\r\n      if (!this.workshopEnabled) {\r\n        this.columns = this.columns.filter(v => v.Code !== 'Workshop_Name')\r\n      }\r\n      this.checkOwner()\r\n    },\r\n    async fetchData() {\r\n      this.tbLoading = true\r\n      let resData = null\r\n      if (this.isCom) {\r\n        resData = await this.getComPageList()\r\n      } else {\r\n        resData = await this.getPartPageList()\r\n      }\r\n      this.initTbData(resData)\r\n      this.tbLoading = false\r\n    },\r\n    closeView() {\r\n      closeTagView(this.$store, this.$route)\r\n    },\r\n    checkWorkshopIsOpen() {\r\n      this.workShopIsOpen = true\r\n    },\r\n    tbSelectChange(array) {\r\n      this.multipleSelection = array.records\r\n    },\r\n    getAreaInfo() {\r\n      AreaGetEntity({\r\n        id: this.$route.query.areaId\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (!res.Data) {\r\n            return []\r\n          }\r\n\r\n          const start = moment(res.Data?.Demand_Begin_Date)\r\n          const end = moment(res.Data?.Demand_End_Date)\r\n          this.pickerOptions.disabledDate = (time) => {\r\n            return time.getTime() < start || time.getTime() > end\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n      this.openAddDraft = false\r\n    },\r\n    getComPageList() {\r\n      return new Promise((resolve, reject) => {\r\n        const {\r\n          // install,\r\n          // projectId,\r\n          pid\r\n          // areaId\r\n        } = this.$route.query\r\n        GetCompSchdulingInfoDetail({\r\n          Schduling_Plan_Id: pid\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            const { Schduling_Plan, Schduling_Comps, Process_List } = res.Data\r\n            this.formInline = Object.assign(this.formInline, Schduling_Plan)\r\n            Process_List.forEach(item => {\r\n              const plist = {\r\n                key: item.Process_Code,\r\n                value: item\r\n              }\r\n              this.changeProcessList(plist)\r\n            })\r\n            resolve(Schduling_Comps || [])\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    getPartPageList() {\r\n      return new Promise((resolve, reject) => {\r\n        const {\r\n          pid\r\n        } = this.$route.query\r\n        GetPartSchdulingInfoDetail({\r\n          Schduling_Plan_Id: pid\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            const SarePartsModel = res.Data?.SarePartsModel.map(v => {\r\n              if (v.Scheduled_Used_Process) {\r\n                // 已存在操作过数据\r\n                v.Part_Used_Process = v.Scheduled_Used_Process\r\n              }\r\n              return v\r\n            })\r\n            this.formInline = Object.assign(this.formInline, res.Data?.Schduling_Plan)\r\n            resolve(SarePartsModel || [])\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    initTbData(list, teamKey = 'Allocation_Teams') {\r\n      this.tbData = list.map(row => {\r\n        const processList = row.Technology_Path?.split('/') || []\r\n        row.uuid = uuidv4()\r\n        this.addElementToTbData(row)\r\n        const newData = row[teamKey].filter((r) => processList.findIndex((p) => r.Process_Code === p) !== -1)\r\n        newData.forEach((ele, index) => {\r\n          const code = this.getRowUnique(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n          const max = this.getRowUniqueMax(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n          row[code] = ele.Count\r\n          row[max] = 0\r\n        })\r\n        this.setInputMax(row)\r\n        return row\r\n      })\r\n    },\r\n    mergeSelectList(newList) {\r\n      console.time('fff')\r\n      newList.forEach((element, index) => {\r\n        const cur = this.getMergeUniqueRow(element)\r\n\r\n        if (!cur) {\r\n          element.puuid = element.uuid\r\n          element.Schduled_Count = element.chooseCount\r\n          element.Schduled_Weight = numeral(element.chooseCount * element.Weight).format('0.[00]')\r\n          this.tbData.push(element)\r\n          this.addElementToTbData(element)\r\n          return\r\n        }\r\n\r\n        cur.puuid = element.uuid\r\n\r\n        cur.Schduled_Count += element.chooseCount\r\n        cur.Schduled_Weight = numeral(cur.Schduled_Weight).add(element.chooseCount * element.Weight).format('0.[00]')\r\n        if (!cur.Technology_Path) {\r\n          return\r\n        }\r\n        this.setInputMax(cur)\r\n      })\r\n\r\n      // if (this.isCom) {\r\n      //   this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n      // } else {\r\n      //   this.tbData.sort((a, b) => a.Part_Code.localeCompare(b.Part_Code))\r\n      // }\r\n      this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n      console.timeEnd('fff')\r\n    },\r\n    addElementToTbData(element) {\r\n      const key = this.isCom ? element.Comp_Code : (element.Component_Code ?? '') + element.Part_Code\r\n      this.tbDataMap[key] = element\r\n    },\r\n    getMergeUniqueRow(element) {\r\n      const key = this.isCom ? element.Comp_Code : (element.Component_Code ?? '') + element.Part_Code\r\n      return this.tbDataMap[key]\r\n    },\r\n    checkForm() {\r\n      let isValidate = true\r\n      this.$refs['formInline'].validate((valid) => {\r\n        if (!valid) isValidate = false\r\n      })\r\n      return isValidate\r\n    },\r\n    async saveDraft(isOrder = false) {\r\n      const checkSuccess = this.checkForm()\r\n      if (!checkSuccess) return false\r\n      const { tableData, status } = this.getSubmitTbInfo()\r\n      if (!status) return false\r\n      if (!isOrder) {\r\n        this.saveLoading = true\r\n      }\r\n      const isSuccess = await this.handleSaveDraft(tableData, isOrder)\r\n      console.log('isSuccess', isSuccess)\r\n      if (!isSuccess) return false\r\n      if (isOrder) return isSuccess\r\n      this.$refs['draft']?.fetchData()\r\n      this.saveLoading = false\r\n    },\r\n    async saveWorkShop() {\r\n      const checkSuccess = this.checkForm()\r\n      if (!checkSuccess) return false\r\n      const obj = {}\r\n      if (!this.tbData.length) {\r\n        this.$message({\r\n          message: '数据不能为空',\r\n          type: 'success'\r\n        })\r\n        return\r\n      }\r\n      if (this.isCom) {\r\n        obj.Schduling_Comps = this.tbData\r\n      } else {\r\n        obj.SarePartsModel = this.tbData\r\n      }\r\n      if (this.isEdit) {\r\n        obj.Schduling_Plan = this.formInline\r\n      } else {\r\n        const {\r\n          install,\r\n          projectId,\r\n          areaId\r\n        } = this.$route.query\r\n        obj.Schduling_Plan = {\r\n          ...this.formInline,\r\n          Project_Id: projectId,\r\n          InstallUnit_Id: install,\r\n          Area_Id: areaId,\r\n          Schduling_Model: this.model // 1构件单独排产，2零件单独排产，3构/零件一起排产\r\n        }\r\n      }\r\n      this.pgLoading = true\r\n      const _fun = this.isCom ? SaveComponentSchedulingWorkshop : SavePartSchedulingWorkshop\r\n      _fun(obj).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.pgLoading = false\r\n          this.$message({\r\n            message: '保存成功',\r\n            type: 'success'\r\n          })\r\n          this.closeView()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getSubmitTbInfo() {\r\n      // 处理上传的数据\r\n      const tableData = JSON.parse(JSON.stringify(this.tbData))\r\n      for (let i = 0; i < tableData.length; i++) {\r\n        const element = tableData[i]\r\n        const list = []\r\n        if (!element.Technology_Path) {\r\n          this.$message({\r\n            message: '工序不能为空',\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        // if (!this.isCom && element.Comp_Import_Detail_Id && !element.Part_Used_Process) {\r\n        //   // 零构件 零件单独排产\r\n        //   this.$message({\r\n        //     message: '零件领用工序不能为空',\r\n        //     type: 'warning'\r\n        //   })\r\n        //   return { status: false }\r\n        // }\r\n        if (element.Scheduled_Technology_Path && element.Scheduled_Technology_Path !== element.Technology_Path) {\r\n          this.$message({\r\n            message: `请和该区域批次下已排产同${this.isCom ? '构件' : '零件'}保持工序一致`,\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        if (element.Scheduled_Used_Process && element.Scheduled_Used_Process !== element.Part_Used_Process) {\r\n          this.$message({\r\n            message: `请和该区域批次下已排产同零件领用工序保持一致`,\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        const processList = Array.from(new Set(element.Technology_Path.split('/')))\r\n        processList.forEach(code => {\r\n          const groups = this.workingTeam.filter(v => v.Process_Code === code)\r\n          const groupsList = groups.map(group => {\r\n            const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)\r\n            const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)\r\n            const obj = {\r\n              Team_Task_Id: element.Team_Task_Id,\r\n              Comp_Code: element.Comp_Code,\r\n              Again_Count: +element[uCode] || 0, // 不填，后台让传0\r\n              Part_Code: this.isCom ? null : '',\r\n              Process_Code: code,\r\n              Technology_Path: element.Technology_Path,\r\n              Working_Team_Id: group.Working_Team_Id,\r\n              Working_Team_Name: group.Working_Team_Name\r\n            }\r\n            delete element[uCode]\r\n            delete element[uMax]\r\n            return obj\r\n          })\r\n          list.push(...groupsList)\r\n        })\r\n        const hasInput = Object.keys(element).filter(_ => _.startsWith(element['uuid']))\r\n        hasInput.forEach((item) => {\r\n          delete element[item]\r\n        })\r\n        delete element['uuid']\r\n        delete element['_X_ROW_KEY']\r\n        delete element['puuid']\r\n        element.Allocation_Teams = list\r\n      }\r\n      return { tableData, status: true }\r\n    },\r\n    async handleSaveDraft(tableData, isOrder) {\r\n      console.log('保存草稿')\r\n      const _fun = this.isCom ? SaveCompSchdulingDraft : SavePartSchdulingDraft\r\n      const obj = {}\r\n      if (this.isCom) {\r\n        obj.Schduling_Comps = tableData\r\n        const p = []\r\n        for (const objKey in this.processList) {\r\n          if (this.processList.hasOwnProperty(objKey)) {\r\n            p.push(this.processList[objKey])\r\n          }\r\n        }\r\n        obj.Process_List = p\r\n      } else {\r\n        obj.SarePartsModel = tableData\r\n      }\r\n      if (this.isEdit) {\r\n        obj.Schduling_Plan = this.formInline\r\n      } else {\r\n        const {\r\n          install,\r\n          projectId,\r\n          areaId\r\n        } = this.$route.query\r\n        obj.Schduling_Plan = {\r\n          ...this.formInline,\r\n          Project_Id: projectId,\r\n          InstallUnit_Id: install,\r\n          Area_Id: areaId,\r\n          Schduling_Model: this.model // 1构件单独排产，2零件单独排产，3构/零件一起排产\r\n        }\r\n      }\r\n      let orderSuccess = false\r\n      console.log('obj', obj)\r\n\r\n      await _fun(obj).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (!isOrder) {\r\n            this.pgLoading = false\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            this.closeView()\r\n          } else {\r\n            this.templateScheduleCode = res.Data\r\n            orderSuccess = true\r\n            console.log('保存草稿成功 ')\r\n          }\r\n        } else {\r\n          this.saveLoading = false\r\n          this.pgLoading = false\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      console.log('结束 ')\r\n      return orderSuccess\r\n    },\r\n    handleDelete() {\r\n      this.deleteLoading = true\r\n      setTimeout(() => {\r\n        const selectedUuids = new Set(this.multipleSelection.map(v => v.uuid))\r\n        this.tbData = this.tbData.filter(item => {\r\n          const isSelected = selectedUuids.has(item.uuid)\r\n          if (isSelected) {\r\n            const key = this.isCom ? item.Comp_Code : (item.Component_Code ?? '') + item.Part_Code\r\n            delete this.tbDataMap[key]\r\n          }\r\n          return !isSelected\r\n        })\r\n        this.$nextTick(_ => {\r\n          this.$refs['draft']?.mergeData(this.multipleSelection)\r\n          this.multipleSelection = []\r\n        })\r\n        this.deleteLoading = false\r\n      }, 0)\r\n    },\r\n    async getWorkTeam() {\r\n      await GetSchdulingWorkingTeams({\r\n        type: this.isCom ? 1 : 2\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.workingTeam = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      this.$refs['formInline'].validate((valid) => {\r\n        if (!valid) return\r\n        const { tableData, status } = this.getSubmitTbInfo()\r\n        if (!status) return\r\n        this.$confirm('是否提交当前数据?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.saveDraftDoSubmit(tableData)\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n      })\r\n    },\r\n    async saveDraftDoSubmit() {\r\n      this.pgLoading = true\r\n      if (this.formInline?.Schduling_Code) {\r\n        const isSuccess = await this.saveDraft(true)\r\n        console.log('saveDraftDoSubmit', isSuccess)\r\n        isSuccess && this.doSubmit(this.formInline.Id)\r\n      } else {\r\n        const isSuccess = await this.saveDraft(true)\r\n        isSuccess && this.doSubmit(this.templateScheduleCode)\r\n      }\r\n    },\r\n    doSubmit(scheduleCode) {\r\n      SaveSchdulingTaskById({\r\n        schdulingPlanId: scheduleCode\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '下达成功',\r\n            type: 'success'\r\n          })\r\n          this.closeView()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.pgLoading = false\r\n      }).catch(_ => {\r\n        this.pgLoading = false\r\n      })\r\n    },\r\n    getWorkShop(value) {\r\n      const {\r\n        origin,\r\n        row,\r\n        workShop: {\r\n          Id,\r\n          Display_Name\r\n        }\r\n      } = value\r\n      if (origin === 2) {\r\n        if (value.workShop?.Id) {\r\n          row.Workshop_Name = Display_Name\r\n          row.Workshop_Id = Id\r\n          this.setPath(row, Id)\r\n        } else {\r\n          row.Workshop_Name = ''\r\n          row.Workshop_Id = ''\r\n        }\r\n      } else {\r\n        this.multipleSelection.forEach(item => {\r\n          if (value.workShop?.Id) {\r\n            item.Workshop_Name = Display_Name\r\n            item.Workshop_Id = Id\r\n            this.setPath(item, Id)\r\n          } else {\r\n            item.Workshop_Name = ''\r\n            item.Workshop_Id = ''\r\n          }\r\n        })\r\n      }\r\n    },\r\n    setPath(row, Id) {\r\n      if (row?.Scheduled_Workshop_Id) {\r\n        if (row.Scheduled_Workshop_Id !== Id) {\r\n          row.Technology_Path = ''\r\n        }\r\n      } else {\r\n        row.Technology_Path = ''\r\n      }\r\n    },\r\n    handleBatchWorkshop(origin, row) {\r\n      this.title = origin === 1 ? '批量分配车间' : '分配车间'\r\n      this.currentComponent = 'Workshop'\r\n      this.dWidth = '30%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].fetchData(origin, row)\r\n      })\r\n    },\r\n    async handleAutoDeal() {\r\n      /*      if (this.workshopEnabled) {\r\n        const hasWorkShop = this.checkHasWorkShop(1, this.multipleSelection)\r\n        if (!hasWorkShop) return\r\n      }*/\r\n\r\n      this.$confirm(`是否将选中数据按构件类型自动分配`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        if (this.workshopEnabled) {\r\n          const p = this.multipleSelection.map(item => {\r\n            return {\r\n              uniqueType: `${item.Type}$_$${item.Workshop_Id}`\r\n            }\r\n          })\r\n          const codes = Array.from(new Set(p.map(v => v.uniqueType)))\r\n          const objKey = {}\r\n          Promise.all(codes.map(v => {\r\n            const info = v.split('$_$')\r\n            return this.setLibType(info[0], info[1])\r\n          })\r\n          ).then(res => {\r\n            const hasUndefined = res.some(item => item == undefined)\r\n            if (hasUndefined) {\r\n              this.$message({\r\n                message: '所选车间内工序班组与构件类型工序不匹配，请手动分配工序',\r\n                type: 'warning'\r\n              })\r\n            }\r\n\r\n            res.forEach((element, idx) => {\r\n              objKey[codes[idx]] = element\r\n            })\r\n            this.multipleSelection.forEach((element) => {\r\n              element.Technology_Path = objKey[`${element.Type}$_$${element.Workshop_Id}`]\r\n              this.resetWorkTeamMax(element, element.Technology_Path)\r\n            })\r\n          })\r\n        } else {\r\n          const p = this.multipleSelection.map(item => item.Type)\r\n          const codes = Array.from(new Set(p))\r\n          const objKey = {}\r\n\r\n          Promise.all(codes.map(v => {\r\n            return this.setLibType(v)\r\n          })).then(res => {\r\n            res.forEach((element, idx) => {\r\n              objKey[codes[idx]] = element\r\n            })\r\n            this.multipleSelection.forEach((element) => {\r\n              element.Technology_Path = objKey[element.Type]\r\n              this.resetWorkTeamMax(element, element.Technology_Path)\r\n            })\r\n          })\r\n        }\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    getProcessOption(workshopId) {\r\n      return new Promise((resolve, reject) => {\r\n        GetProcessListBase({\r\n          workshopId: workshopId,\r\n          type: this.isCom ? 1 : 2 // 0:全部，工艺类型1：构件工艺，2：零件工艺\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            const process = res.Data.map(v => v.Code)\r\n            resolve(process)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    setLibType(code, workshopId) {\r\n      return new Promise((resolve) => {\r\n        const obj = {\r\n          Component_type: code,\r\n          type: 1\r\n        }\r\n        if (this.workshopEnabled) {\r\n          obj.workshopId = workshopId\r\n        }\r\n        GetLibListType(obj).then(res => {\r\n          if (res.IsSucceed) {\r\n            if (res.Data.Data && res.Data.Data.length) {\r\n              const info = res.Data.Data[0]\r\n              const workCode = info.WorkCode && info.WorkCode.replace(/\\\\/g, '/')\r\n              resolve(workCode)\r\n            } else {\r\n              resolve(undefined)\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    inputChange(row) {\r\n      this.setInputMax(row)\r\n    },\r\n    setInputMax(row) {\r\n      const inputValuesKeys = Object.keys(row)\r\n        .filter(v => !v.endsWith('max') && v.startsWith(row.uuid) && v.length > row.uuid.length)\r\n      inputValuesKeys.forEach((val) => {\r\n        const curCode = val.split(SPLIT_SYMBOL)[1]\r\n        const otherTotal = inputValuesKeys.filter(x => {\r\n          const code = x.split(SPLIT_SYMBOL)[1]\r\n          return x !== val && code === curCode\r\n        }).reduce((acc, item) => {\r\n          return acc + numeral(row[item]).value()\r\n        }, 0)\r\n        row[val + SPLIT_SYMBOL + 'max'] = row.Schduled_Count - otherTotal\r\n      })\r\n    },\r\n    sendProcess({ arr, str }) {\r\n      let isSuccess = true\r\n      for (let i = 0; i < arr.length; i++) {\r\n        const item = arr[i]\r\n        if (item.originalPath && item.originalPath !== str) {\r\n          isSuccess = false\r\n          break\r\n        }\r\n        item.Technology_Path = str\r\n      }\r\n      if (!isSuccess) {\r\n        this.$message({\r\n          message: '请和该区域批次下已排产同构件保持工序一致',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    resetWorkTeamMax(row, str) {\r\n      if (str) {\r\n        row.Technology_Path = str\r\n      } else {\r\n        str = row.Technology_Path\r\n      }\r\n      const list = str?.split('/') || []\r\n      this.workingTeam.forEach((element, idx) => {\r\n        const cur = list.some(k => k === element.Process_Code)\r\n        const code = this.getRowUnique(row.uuid, element.Process_Code, element.Working_Team_Id)\r\n        const max = this.getRowUniqueMax(row.uuid, element.Process_Code, element.Working_Team_Id)\r\n        if (cur) {\r\n          if (!row[code]) {\r\n            this.$set(row, code, 0)\r\n            this.$set(row, max, row.Schduled_Count)\r\n          }\r\n        } else {\r\n          this.$delete(row, code)\r\n          this.$delete(row, max)\r\n        }\r\n      })\r\n    },\r\n    checkPermissionTeam(processStr, processCode) {\r\n      if (!processStr) return false\r\n      const list = processStr?.split('/') || []\r\n      return !!list.some(v => v === processCode)\r\n    },\r\n\r\n    async getTableConfig(code) {\r\n      await GetGridByCode({\r\n        code\r\n      }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n          const list = Data.ColumnList || []\r\n          this.ownerColumn = list.find(item => item.Code === 'Part_Used_Process')\r\n          this.ownerColumn2 = list.find(item => item.Code === 'Is_Main_Part')\r\n          this.columns = this.setColumnDisplay(list)\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setColumnDisplay(list) {\r\n      return list.filter(v => v.Is_Display).map(item => {\r\n        if (FIX_COLUMN.includes(item.Code)) {\r\n          item.fixed = 'left'\r\n        }\r\n        return item\r\n      })\r\n    },\r\n    activeCellMethod({ row, column, columnIndex }) {\r\n      if (this.isView) return false\r\n      const processCode = column.field?.split('$_$')[1]\r\n      return this.checkPermissionTeam(row.Technology_Path, processCode)\r\n    },\r\n    openBPADialog(type, row) {\r\n      if (this.workshopEnabled) {\r\n        if (type === 1) {\r\n          const IsUnique = this.checkIsUniqueWorkshop()\r\n          if (!IsUnique) return\r\n        }\r\n      }\r\n      this.title = type === 2 ? '工序调整' : '批量工序调整'\r\n      this.currentComponent = 'BatchProcessAdjust'\r\n      this.dWidth = this.isCom ? '60%' : '30%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].setData(type === 2 ? [row] : this.multipleSelection, type === 2 ? row.Technology_Path : '')\r\n      })\r\n    },\r\n    checkIsUniqueWorkshop() {\r\n      let isUnique = true\r\n      const firstV = this.multipleSelection[0].Workshop_Name\r\n      for (let i = 1; i < this.multipleSelection.length; i++) {\r\n        const item = this.multipleSelection[i]\r\n        if (item.Workshop_Name !== firstV) {\r\n          isUnique = false\r\n          break\r\n        }\r\n      }\r\n      if (!isUnique) {\r\n        this.$message({\r\n          message: '批量分配工序时只有相同车间下的才可一起批量分配',\r\n          type: 'warning'\r\n        })\r\n      }\r\n      return isUnique\r\n    },\r\n    checkHasWorkShop(type, arr) {\r\n      let hasWorkShop = true\r\n      for (let i = 0; i < arr.length; i++) {\r\n        const item = arr[i]\r\n        if (!item.Workshop_Name) {\r\n          hasWorkShop = false\r\n          break\r\n        }\r\n      }\r\n      if (!hasWorkShop) {\r\n        this.$message({\r\n          message: '请先选择车间后再进行工序分配',\r\n          type: 'warning'\r\n        })\r\n      }\r\n      return hasWorkShop\r\n    },\r\n    handleAddDialog(type = 'add') {\r\n      if (this.isCom) {\r\n        this.title = '构件排产'\r\n      } else {\r\n        this.title = '添加零件'\r\n      }\r\n      this.currentComponent = 'AddDraft'\r\n      this.dWidth = '80%'\r\n      this.openAddDraft = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['draft'].setPageData()\r\n      })\r\n    },\r\n    getRowUnique(uuid, processCode, workingId) {\r\n      return `${uuid}${SPLIT_SYMBOL}${processCode}${SPLIT_SYMBOL}${workingId}`\r\n    },\r\n    getRowUniqueMax(uuid, processCode, workingId) {\r\n      return this.getRowUnique(uuid, processCode, workingId) + `${SPLIT_SYMBOL}max`\r\n    },\r\n    handleSelectMenu(v) {\r\n      if (v === 'process') {\r\n        this.openBPADialog(1)\r\n      } else if (v === 'deal') {\r\n        this.handleAutoDeal(1)\r\n      }\r\n    },\r\n    handleBatchOwner(type, row) {\r\n      this.title = '批量分配领用工序'\r\n      this.currentComponent = 'OwnerProcess'\r\n      this.dWidth = '30%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].setOption(type === 2, type === 2 ? [row] : this.multipleSelection)\r\n      })\r\n    },\r\n    handleReverse() {\r\n      const cur = []\r\n      this.tbData.forEach((element, idx) => {\r\n        element.checked = !element.checked\r\n        if (element.checked) {\r\n          cur.push(element)\r\n        }\r\n      })\r\n      this.multipleSelection = cur\r\n      if (this.multipleSelection.length === this.tbData.length) {\r\n        this.$refs['xTable'].setAllCheckboxRow(true)\r\n      }\r\n      if (this.multipleSelection.length === 0) {\r\n        this.$refs['xTable'].setAllCheckboxRow(false)\r\n      }\r\n    },\r\n    tbFilterChange() {\r\n      const xTable = this.$refs.xTable\r\n      const column = xTable.getColumnByField('Type_Name')\r\n      if (!column?.filters?.length) return\r\n      column.filters.forEach(d => {\r\n        d.checked = d.value === this.searchType\r\n      })\r\n      xTable.updateData()\r\n    },\r\n    getType() {\r\n      GetPartTypeList({ Part_Grade: 0 }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.typeOption = res.Data.map(v => {\r\n            return {\r\n              label: v.Name,\r\n              value: v.Name,\r\n              code: v.Code\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setProcessList(info) {\r\n      this.changeProcessList(info)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.pagination-container {\r\n  padding: 0;\r\n  text-align: right;\r\n}\r\n\r\n::v-deep .el-card__body {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.tb-x {\r\n  flex: 1;\r\n  height: 0;\r\n  margin-bottom: 10px;\r\n  overflow: auto;\r\n}\r\n\r\n.topTitle {\r\n  font-size: 14px;\r\n  margin: 0 0 16px;\r\n\r\n  span {\r\n    display: inline-block;\r\n    width: 2px;\r\n    height: 14px;\r\n    background: #009dff;\r\n    vertical-align: middle;\r\n    margin-right: 6px;\r\n  }\r\n}\r\n\r\n::v-deep .elDivder {\r\n  margin: 10px;\r\n}\r\n\r\n.btn-x {\r\n  margin-bottom: 10px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.el-icon-edit {\r\n  cursor: pointer;\r\n}\r\n\r\nfooter {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.cs-bottom {\r\n  position: relative;\r\n  height: 40px;\r\n  line-height: 40px;\r\n\r\n  .data-info {\r\n    position: absolute;\r\n    bottom: 0;\r\n\r\n    .info-x {\r\n      margin-right: 20px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoUA,SAAAA,YAAA,EAAAC,QAAA;AACA,OAAAC,kBAAA;AACA,SACAC,0BAAA,EACAC,0BAAA,EACAC,wBAAA,EACAC,+BAAA,EACAC,sBAAA,EACAC,sBAAA,EACAC,0BAAA,EACAC,qBAAA,QACA;AACA,OAAAC,QAAA;AACA,OAAAC,YAAA;AACA,OAAAC,QAAA;AACA,SAAAC,aAAA;AACA,SAAAC,UAAA,EAAAC,UAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AACA,OAAAC,OAAA;AACA,SAAAC,cAAA,EAAAC,kBAAA;AACA,SAAAC,aAAA;AACA,SAAAC,UAAA,EAAAC,UAAA;AACA,SAAAC,eAAA;AACA,YAAAC,MAAA;AAEA,IAAAC,YAAA;AACA;EACAC,UAAA;IAAA1B,kBAAA,EAAAA,kBAAA;IAAAS,QAAA,EAAAA,QAAA;IAAAE,QAAA,EAAAA,QAAA;IAAAD,YAAA,EAAAA;EAAA;EACAiB,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;QACAC,YAAA,WAAAA,aAAAC,IAAA,GACA;MACA;MACAC,UAAA;MACAC,UAAA;QACAC,cAAA;QACAC,eAAA;QACAC,WAAA;QACAC,MAAA;MACA;MACAC,KAAA;MACAC,OAAA;MACAC,MAAA;MACAC,QAAA;MACAC,UAAA;MACAC,iBAAA;MACAC,SAAA;MACAC,aAAA;MACAC,cAAA;MACAC,WAAA;MACAC,aAAA;MACAC,YAAA;MACAC,WAAA;MACAC,SAAA;MACAC,UAAA;MACAC,gBAAA;MACAC,MAAA;MACAC,KAAA;MACAC,MAAA,WAAAA,OAAA;QAAA;MAAA;MACAC,QAAA,EAAAC,SAAA;MACAC,QAAA;MACAC,gBAAA;MACAC,UAAA;MACAC,WAAA;MACAC,UAAA,EAAAL,SAAA;MACAM,UAAA;MACAC,kBAAA;IACA;EACA;EACAC,KAAA;IACA;MACAC,OAAA,WAAAA,QAAAC,CAAA,EAAAC,CAAA;QACA,KAAAC,UAAA;MACA;MACAC,SAAA;IACA;EACA;EACAC,QAAA,EAAAC,aAAA,CAAAA,aAAA;IACAC,KAAA,WAAAA,MAAA;MACA,YAAAjB,QAAA;IACA;IACAkB,MAAA,WAAAA,OAAA;MACA,YAAAZ,UAAA;IACA;IACAa,MAAA,WAAAA,OAAA;MACA,YAAAb,UAAA;IACA;IACAc,KAAA,WAAAA,MAAA;MACA,YAAAd,UAAA;IACA;EAAA,GACAxC,UAAA,uCACAA,UAAA,8BACA;EACAuD,gBAAA,WAAAA,iBAAAC,EAAA,EAAAC,IAAA,EAAAC,IAAA;IACA,IAAAF,EAAA,CAAAG,KAAA,CAAAC,MAAA;MACAJ,EAAA,CAAAK,IAAA,CAAA7B,KAAA;IACA;MACAwB,EAAA,CAAAK,IAAA,CAAA7B,KAAA;IACA;IACA0B,IAAA;EACA;EACAI,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAZ,IAAA;UAAA;YACAK,KAAA,CAAAS,eAAA;YACAT,KAAA,CAAAU,SAAA;YACAV,KAAA,CAAA7B,QAAA,GAAA6B,KAAA,CAAAW,MAAA,CAAAf,KAAA,CAAAgB,OAAA;YACAZ,KAAA,CAAAvB,UAAA,GAAAuB,KAAA,CAAAW,MAAA,CAAAf,KAAA,CAAAC,MAAA;YACAG,KAAA,CAAAa,KAAA,GAAAb,KAAA,CAAAW,MAAA,CAAAf,KAAA,CAAAiB,KAAA;YACAb,KAAA,CAAAtB,UAAA,GAAAsB,KAAA,CAAAW,MAAA,CAAAf,KAAA,CAAAkB,GAAA;YACA;YACA;YACAd,KAAA,CAAArD,UAAA,CAAAE,eAAA,GAAAkE,YAAA,CAAAC,OAAA;YACA,KAAAhB,KAAA,CAAAZ,KAAA;cACAY,KAAA,CAAAiB,OAAA;YACA,QAEA;YACAjB,KAAA,CAAAkB,MAAA,GAAAzF,UAAA;YACAuE,KAAA,CAAAmB,mBAAA;YACAnB,KAAA,CAAAoB,WAAA;YACApB,KAAA,CAAA9B,MAAA,GAAAxD,QAAA,CAAAsF,KAAA,CAAAqB,SAAA;YAAAd,QAAA,CAAAZ,IAAA;YAAA,OACAK,KAAA,CAAAsB,WAAA;UAAA;YACA,IAAAtB,KAAA,CAAAX,MAAA,IAAAW,KAAA,CAAAV,MAAA;cACAU,KAAA,CAAAqB,SAAA;YACA;UAAA;UAAA;YAAA,OAAAd,QAAA,CAAAgB,IAAA;QAAA;MAAA,GAAAnB,OAAA;IAAA;EACA;EACAoB,OAAA,EAAArC,aAAA,CAAAA,aAAA,KACAnD,UAAA;IACAgD,UAAA,WAAAA,WAAA;MACA,SAAAI,KAAA;MACA,KAAA3B,WAAA,QAAAP,MAAA,CAAAuE,KAAA,WAAAC,CAAA;QAAA,QAAAA,CAAA,CAAAC,qBAAA;MAAA;MACA,IAAAC,GAAA,QAAA3E,OAAA,CAAA4E,SAAA,WAAAH,CAAA;QAAA,OAAAA,CAAA,CAAAI,IAAA;MAAA;MACA,SAAArE,WAAA;QACAmE,GAAA,gBAAA3E,OAAA,CAAA8E,MAAA,CAAAH,GAAA;MACA;QACA,IAAAA,GAAA;UACA,UAAAI,WAAA;YACA,KAAAC,QAAA;cACAC,OAAA;cACAC,IAAA;YACA;YACA;UACA;UACA,KAAAlF,OAAA,CAAAmF,IAAA,MAAAJ,WAAA;QACA;MACA;IACA;IACAV,WAAA,WAAAA,YAAA;MAAA,IAAAe,MAAA;MAAA,OAAApC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAmC,SAAA;QAAA,OAAApC,mBAAA,GAAAG,IAAA,UAAAkC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhC,IAAA,GAAAgC,SAAA,CAAA7C,IAAA;YAAA;cAAA6C,SAAA,CAAA7C,IAAA;cAAA,OACA0C,MAAA,CAAAI,SAAA;YAAA;cAAAD,SAAA,CAAA7C,IAAA;cAAA,OACA0C,MAAA,CAAAK,WAAA;YAAA;YAAA;cAAA,OAAAF,SAAA,CAAAjB,IAAA;UAAA;QAAA,GAAAe,QAAA;MAAA;IACA;IACAG,SAAA,WAAAA,UAAA;MAAA,IAAAE,MAAA;MAAA,OAAA1C,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAyC,SAAA;QAAA,IAAAC,UAAA;QAAA,OAAA3C,mBAAA,GAAAG,IAAA,UAAAyC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvC,IAAA,GAAAuC,SAAA,CAAApD,IAAA;YAAA;cACAkD,UAAA,GAAAF,MAAA,CAAAvD,KAAA,GAAAuD,MAAA,CAAAtD,MAAA,0DAAAsD,MAAA,CAAAtD,MAAA;cAAA0D,SAAA,CAAApD,IAAA;cAAA,OAEAgD,MAAA,CAAAK,cAAA,CAAAH,UAAA;YAAA;cACA,KAAAF,MAAA,CAAAM,eAAA;gBACAN,MAAA,CAAA1F,OAAA,GAAA0F,MAAA,CAAA1F,OAAA,CAAAiG,MAAA,WAAAxB,CAAA;kBAAA,OAAAA,CAAA,CAAAI,IAAA;gBAAA;cACA;cACAa,MAAA,CAAA3D,UAAA;YAAA;YAAA;cAAA,OAAA+D,SAAA,CAAAxB,IAAA;UAAA;QAAA,GAAAqB,QAAA;MAAA;IACA;IACAvB,SAAA,WAAAA,UAAA;MAAA,IAAA8B,MAAA;MAAA,OAAAlD,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAiD,SAAA;QAAA,IAAAC,OAAA;QAAA,OAAAnD,mBAAA,GAAAG,IAAA,UAAAiD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/C,IAAA,GAAA+C,SAAA,CAAA5D,IAAA;YAAA;cACAwD,MAAA,CAAAtF,SAAA;cACAwF,OAAA;cAAA,KACAF,MAAA,CAAA/D,KAAA;gBAAAmE,SAAA,CAAA5D,IAAA;gBAAA;cAAA;cAAA4D,SAAA,CAAA5D,IAAA;cAAA,OACAwD,MAAA,CAAAK,cAAA;YAAA;cAAAH,OAAA,GAAAE,SAAA,CAAAE,IAAA;cAAAF,SAAA,CAAA5D,IAAA;cAAA;YAAA;cAAA4D,SAAA,CAAA5D,IAAA;cAAA,OAEAwD,MAAA,CAAAO,eAAA;YAAA;cAAAL,OAAA,GAAAE,SAAA,CAAAE,IAAA;YAAA;cAEAN,MAAA,CAAAQ,UAAA,CAAAN,OAAA;cACAF,MAAA,CAAAtF,SAAA;YAAA;YAAA;cAAA,OAAA0F,SAAA,CAAAhC,IAAA;UAAA;QAAA,GAAA6B,QAAA;MAAA;IACA;IACAQ,SAAA,WAAAA,UAAA;MACAnJ,YAAA,MAAAoJ,MAAA,OAAAlD,MAAA;IACA;IACAQ,mBAAA,WAAAA,oBAAA;MACA,KAAA3D,cAAA;IACA;IACAsG,cAAA,WAAAA,eAAAC,KAAA;MACA,KAAA1G,iBAAA,GAAA0G,KAAA,CAAAC,OAAA;IACA;IACA5C,WAAA,WAAAA,YAAA;MAAA,IAAA6C,MAAA;MACAlI,aAAA;QACAmI,EAAA,OAAAvD,MAAA,CAAAf,KAAA,CAAAuE;MACA,GAAAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UAAA,IAAAC,SAAA,EAAAC,UAAA;UACA,KAAAH,GAAA,CAAAI,IAAA;YACA;UACA;UAEA,IAAAC,KAAA,GAAAvI,MAAA,EAAAoI,SAAA,GAAAF,GAAA,CAAAI,IAAA,cAAAF,SAAA,uBAAAA,SAAA,CAAAI,iBAAA;UACA,IAAAC,GAAA,GAAAzI,MAAA,EAAAqI,UAAA,GAAAH,GAAA,CAAAI,IAAA,cAAAD,UAAA,uBAAAA,UAAA,CAAAK,eAAA;UACAZ,MAAA,CAAA1H,aAAA,CAAAC,YAAA,aAAAC,IAAA;YACA,OAAAA,IAAA,CAAAqI,OAAA,KAAAJ,KAAA,IAAAjI,IAAA,CAAAqI,OAAA,KAAAF,GAAA;UACA;QACA;UACAX,MAAA,CAAAhC,QAAA;YACAC,OAAA,EAAAmC,GAAA,CAAAU,OAAA;YACA5C,IAAA;UACA;QACA;MACA;IACA;IACA6C,WAAA,WAAAA,YAAA;MACA,KAAAtH,aAAA;MACA,KAAAC,YAAA;IACA;IACA6F,cAAA,WAAAA,eAAA;MAAA,IAAAyB,MAAA;MACA,WAAAC,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACA,IAGAtE,GAAA,GAEAmE,MAAA,CAAAtE,MAAA,CAAAf,KAAA,CAFAkB,GAAA;QAGAlG,0BAAA;UACAyK,iBAAA,EAAAvE;QACA,GAAAsD,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACA,IAAAgB,UAAA,GAAAjB,GAAA,CAAAI,IAAA;cAAAc,cAAA,GAAAD,UAAA,CAAAC,cAAA;cAAAC,eAAA,GAAAF,UAAA,CAAAE,eAAA;cAAAC,YAAA,GAAAH,UAAA,CAAAG,YAAA;YACAR,MAAA,CAAAtI,UAAA,GAAA+I,MAAA,CAAAC,MAAA,CAAAV,MAAA,CAAAtI,UAAA,EAAA4I,cAAA;YACAE,YAAA,CAAAG,OAAA,WAAAC,IAAA;cACA,IAAAC,KAAA;gBACAC,GAAA,EAAAF,IAAA,CAAAG,YAAA;gBACAC,KAAA,EAAAJ;cACA;cACAZ,MAAA,CAAAiB,iBAAA,CAAAJ,KAAA;YACA;YACAX,OAAA,CAAAK,eAAA;UACA;YACAP,MAAA,CAAAhD,QAAA;cACAC,OAAA,EAAAmC,GAAA,CAAAU,OAAA;cACA5C,IAAA;YACA;YACAiD,MAAA;UACA;QACA;MACA;IACA;IACA1B,eAAA,WAAAA,gBAAA;MAAA,IAAAyC,MAAA;MACA,WAAAjB,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACA,IACAtE,GAAA,GACAqF,MAAA,CAAAxF,MAAA,CAAAf,KAAA,CADAkB,GAAA;QAEAjG,0BAAA;UACAwK,iBAAA,EAAAvE;QACA,GAAAsD,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YAAA,IAAA8B,UAAA,EAAAC,UAAA;YACA,IAAAC,cAAA,IAAAF,UAAA,GAAA/B,GAAA,CAAAI,IAAA,cAAA2B,UAAA,uBAAAA,UAAA,CAAAE,cAAA,CAAAC,GAAA,WAAA7E,CAAA;cACA,IAAAA,CAAA,CAAA8E,sBAAA;gBACA;gBACA9E,CAAA,CAAA+E,iBAAA,GAAA/E,CAAA,CAAA8E,sBAAA;cACA;cACA,OAAA9E,CAAA;YACA;YACAyE,MAAA,CAAAxJ,UAAA,GAAA+I,MAAA,CAAAC,MAAA,CAAAQ,MAAA,CAAAxJ,UAAA,GAAA0J,UAAA,GAAAhC,GAAA,CAAAI,IAAA,cAAA4B,UAAA,uBAAAA,UAAA,CAAAd,cAAA;YACAJ,OAAA,CAAAmB,cAAA;UACA;YACAH,MAAA,CAAAlE,QAAA;cACAC,OAAA,EAAAmC,GAAA,CAAAU,OAAA;cACA5C,IAAA;YACA;YACAiD,MAAA;UACA;QACA;MACA;IACA;IACAzB,UAAA,WAAAA,WAAA+C,IAAA;MAAA,IAAAC,MAAA;MAAA,IAAAC,OAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzI,SAAA,GAAAyI,SAAA;MACA,KAAA3J,MAAA,GAAAwJ,IAAA,CAAAH,GAAA,WAAAQ,GAAA;QAAA,IAAAC,oBAAA;QACA,IAAAC,WAAA,KAAAD,oBAAA,GAAAD,GAAA,CAAAG,eAAA,cAAAF,oBAAA,uBAAAA,oBAAA,CAAAG,KAAA;QACAJ,GAAA,CAAAK,IAAA,GAAAzL,MAAA;QACAgL,MAAA,CAAAU,kBAAA,CAAAN,GAAA;QACA,IAAAO,OAAA,GAAAP,GAAA,CAAAH,OAAA,EAAA1D,MAAA,WAAAqE,CAAA;UAAA,OAAAN,WAAA,CAAApF,SAAA,WAAA2F,CAAA;YAAA,OAAAD,CAAA,CAAAvB,YAAA,KAAAwB,CAAA;UAAA;QAAA;QACAF,OAAA,CAAA1B,OAAA,WAAA6B,GAAA,EAAAC,KAAA;UACA,IAAAC,IAAA,GAAAhB,MAAA,CAAAiB,YAAA,CAAAb,GAAA,CAAAK,IAAA,EAAAK,GAAA,CAAAzB,YAAA,EAAAyB,GAAA,CAAAI,eAAA;UACA,IAAAC,GAAA,GAAAnB,MAAA,CAAAoB,eAAA,CAAAhB,GAAA,CAAAK,IAAA,EAAAK,GAAA,CAAAzB,YAAA,EAAAyB,GAAA,CAAAI,eAAA;UACAd,GAAA,CAAAY,IAAA,IAAAF,GAAA,CAAAO,KAAA;UACAjB,GAAA,CAAAe,GAAA;QACA;QACAnB,MAAA,CAAAsB,WAAA,CAAAlB,GAAA;QACA,OAAAA,GAAA;MACA;IACA;IACAmB,eAAA,WAAAA,gBAAAC,OAAA;MAAA,IAAAC,MAAA;MACAC,OAAA,CAAA5L,IAAA;MACA0L,OAAA,CAAAvC,OAAA,WAAA0C,OAAA,EAAAZ,KAAA;QACA,IAAAa,GAAA,GAAAH,MAAA,CAAAI,iBAAA,CAAAF,OAAA;QAEA,KAAAC,GAAA;UACAD,OAAA,CAAAG,KAAA,GAAAH,OAAA,CAAAlB,IAAA;UACAkB,OAAA,CAAAI,cAAA,GAAAJ,OAAA,CAAAK,WAAA;UACAL,OAAA,CAAAM,eAAA,GAAAhN,OAAA,CAAA0M,OAAA,CAAAK,WAAA,GAAAL,OAAA,CAAAO,MAAA,EAAAC,MAAA;UACAV,MAAA,CAAAlL,MAAA,CAAAkF,IAAA,CAAAkG,OAAA;UACAF,MAAA,CAAAf,kBAAA,CAAAiB,OAAA;UACA;QACA;QAEAC,GAAA,CAAAE,KAAA,GAAAH,OAAA,CAAAlB,IAAA;QAEAmB,GAAA,CAAAG,cAAA,IAAAJ,OAAA,CAAAK,WAAA;QACAJ,GAAA,CAAAK,eAAA,GAAAhN,OAAA,CAAA2M,GAAA,CAAAK,eAAA,EAAAG,GAAA,CAAAT,OAAA,CAAAK,WAAA,GAAAL,OAAA,CAAAO,MAAA,EAAAC,MAAA;QACA,KAAAP,GAAA,CAAArB,eAAA;UACA;QACA;QACAkB,MAAA,CAAAH,WAAA,CAAAM,GAAA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA,KAAArL,MAAA,CAAA8L,IAAA,WAAAC,CAAA,EAAAC,CAAA;QAAA,OAAAD,CAAA,CAAAE,YAAA,GAAAD,CAAA,CAAAC,YAAA;MAAA;MACAd,OAAA,CAAAe,OAAA;IACA;IACA/B,kBAAA,WAAAA,mBAAAiB,OAAA;MAAA,IAAAe,qBAAA;MACA,IAAAtD,GAAA,QAAA3G,KAAA,GAAAkJ,OAAA,CAAAgB,SAAA,KAAAD,qBAAA,GAAAf,OAAA,CAAAiB,cAAA,cAAAF,qBAAA,cAAAA,qBAAA,SAAAf,OAAA,CAAAkB,SAAA;MACA,KAAA9I,SAAA,CAAAqF,GAAA,IAAAuC,OAAA;IACA;IACAE,iBAAA,WAAAA,kBAAAF,OAAA;MAAA,IAAAmB,sBAAA;MACA,IAAA1D,GAAA,QAAA3G,KAAA,GAAAkJ,OAAA,CAAAgB,SAAA,KAAAG,sBAAA,GAAAnB,OAAA,CAAAiB,cAAA,cAAAE,sBAAA,cAAAA,sBAAA,SAAAnB,OAAA,CAAAkB,SAAA;MACA,YAAA9I,SAAA,CAAAqF,GAAA;IACA;IACA2D,SAAA,WAAAA,UAAA;MACA,IAAAC,UAAA;MACA,KAAAC,KAAA,eAAAC,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA,EAAAH,UAAA;MACA;MACA,OAAAA,UAAA;IACA;IACAI,SAAA,WAAAA,UAAA;MAAA,IAAAC,UAAA,GAAAnD,SAAA;QAAAoD,MAAA;MAAA,OAAAhK,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA+J,SAAA;QAAA,IAAAC,kBAAA;QAAA,IAAAC,OAAA,EAAAC,YAAA,EAAAC,qBAAA,EAAAC,SAAA,EAAA1K,MAAA,EAAA2K,SAAA;QAAA,OAAAtK,mBAAA,GAAAG,IAAA,UAAAoK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlK,IAAA,GAAAkK,SAAA,CAAA/K,IAAA;YAAA;cAAAyK,OAAA,GAAAJ,UAAA,CAAAlD,MAAA,QAAAkD,UAAA,QAAA5L,SAAA,GAAA4L,UAAA;cACAK,YAAA,GAAAJ,MAAA,CAAAP,SAAA;cAAA,IACAW,YAAA;gBAAAK,SAAA,CAAA/K,IAAA;gBAAA;cAAA;cAAA,OAAA+K,SAAA,CAAAC,MAAA;YAAA;cAAAL,qBAAA,GACAL,MAAA,CAAAW,eAAA,IAAAL,SAAA,GAAAD,qBAAA,CAAAC,SAAA,EAAA1K,MAAA,GAAAyK,qBAAA,CAAAzK,MAAA;cAAA,IACAA,MAAA;gBAAA6K,SAAA,CAAA/K,IAAA;gBAAA;cAAA;cAAA,OAAA+K,SAAA,CAAAC,MAAA;YAAA;cACA,KAAAP,OAAA;gBACAH,MAAA,CAAArM,WAAA;cACA;cAAA8M,SAAA,CAAA/K,IAAA;cAAA,OACAsK,MAAA,CAAAY,eAAA,CAAAN,SAAA,EAAAH,OAAA;YAAA;cAAAI,SAAA,GAAAE,SAAA,CAAAjH,IAAA;cACA4E,OAAA,CAAAyC,GAAA,cAAAN,SAAA;cAAA,IACAA,SAAA;gBAAAE,SAAA,CAAA/K,IAAA;gBAAA;cAAA;cAAA,OAAA+K,SAAA,CAAAC,MAAA;YAAA;cAAA,KACAP,OAAA;gBAAAM,SAAA,CAAA/K,IAAA;gBAAA;cAAA;cAAA,OAAA+K,SAAA,CAAAC,MAAA,WAAAH,SAAA;YAAA;cACA,CAAAL,kBAAA,GAAAF,MAAA,CAAAL,KAAA,uBAAAO,kBAAA,eAAAA,kBAAA,CAAA9I,SAAA;cACA4I,MAAA,CAAArM,WAAA;YAAA;YAAA;cAAA,OAAA8M,SAAA,CAAAnJ,IAAA;UAAA;QAAA,GAAA2I,QAAA;MAAA;IACA;IACAa,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MAAA,OAAA/K,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA8K,SAAA;QAAA,IAAAZ,YAAA,EAAAa,GAAA,EAAAC,mBAAA,EAAAC,OAAA,EAAAC,SAAA,EAAAlH,MAAA,EAAAmH,IAAA;QAAA,OAAApL,mBAAA,GAAAG,IAAA,UAAAkL,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhL,IAAA,GAAAgL,SAAA,CAAA7L,IAAA;YAAA;cACA0K,YAAA,GAAAW,MAAA,CAAAtB,SAAA;cAAA,IACAW,YAAA;gBAAAmB,SAAA,CAAA7L,IAAA;gBAAA;cAAA;cAAA,OAAA6L,SAAA,CAAAb,MAAA;YAAA;cACAO,GAAA;cAAA,IACAF,MAAA,CAAA9N,MAAA,CAAA4J,MAAA;gBAAA0E,SAAA,CAAA7L,IAAA;gBAAA;cAAA;cACAqL,MAAA,CAAA/I,QAAA;gBACAC,OAAA;gBACAC,IAAA;cACA;cAAA,OAAAqJ,SAAA,CAAAb,MAAA;YAAA;cAGA,IAAAK,MAAA,CAAA5L,KAAA;gBACA8L,GAAA,CAAA1F,eAAA,GAAAwF,MAAA,CAAA9N,MAAA;cACA;gBACAgO,GAAA,CAAA5E,cAAA,GAAA0E,MAAA,CAAA9N,MAAA;cACA;cACA,IAAA8N,MAAA,CAAA1L,MAAA;gBACA4L,GAAA,CAAA3F,cAAA,GAAAyF,MAAA,CAAArO,UAAA;cACA;gBAAAwO,mBAAA,GAKAH,MAAA,CAAArK,MAAA,CAAAf,KAAA,EAHAwL,OAAA,GAAAD,mBAAA,CAAAC,OAAA,EACAC,SAAA,GAAAF,mBAAA,CAAAE,SAAA,EACAlH,MAAA,GAAAgH,mBAAA,CAAAhH,MAAA;gBAEA+G,GAAA,CAAA3F,cAAA,GAAApG,aAAA,CAAAA,aAAA,KACA6L,MAAA,CAAArO,UAAA;kBACA8O,UAAA,EAAAJ,SAAA;kBACAK,cAAA,EAAAN,OAAA;kBACAO,OAAA,EAAAxH,MAAA;kBACAyH,eAAA,EAAAZ,MAAA,CAAAnK,KAAA;gBAAA,EACA;cACA;cACAmK,MAAA,CAAA1N,SAAA;cACAgO,IAAA,GAAAN,MAAA,CAAA5L,KAAA,GAAArE,+BAAA,GAAAG,0BAAA;cACAoQ,IAAA,CAAAJ,GAAA,EAAA9G,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACA0G,MAAA,CAAA1N,SAAA;kBACA0N,MAAA,CAAA/I,QAAA;oBACAC,OAAA;oBACAC,IAAA;kBACA;kBACA6I,MAAA,CAAApH,SAAA;gBACA;kBACAoH,MAAA,CAAA/I,QAAA;oBACAC,OAAA,EAAAmC,GAAA,CAAAU,OAAA;oBACA5C,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAqJ,SAAA,CAAAjK,IAAA;UAAA;QAAA,GAAA0J,QAAA;MAAA;IACA;IACAL,eAAA,WAAAA,gBAAA;MAAA,IAAAiB,OAAA;MACA;MACA,IAAAtB,SAAA,GAAAuB,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAA9O,MAAA;MAAA,IAAA+O,KAAA,YAAAA,MAAA,EACA;UACA,IAAA3D,OAAA,GAAAiC,SAAA,CAAA2B,CAAA;UACA,IAAAxF,IAAA;UACA,KAAA4B,OAAA,CAAApB,eAAA;YACA2E,OAAA,CAAA5J,QAAA;cACAC,OAAA;cACAC,IAAA;YACA;YAAA;cAAAT,CAAA,EACA;gBAAA7B,MAAA;cAAA;YAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAAyI,OAAA,CAAA6D,yBAAA,IAAA7D,OAAA,CAAA6D,yBAAA,KAAA7D,OAAA,CAAApB,eAAA;YACA2E,OAAA,CAAA5J,QAAA;cACAC,OAAA,6EAAAkK,MAAA,CAAAP,OAAA,CAAAzM,KAAA;cACA+C,IAAA;YACA;YAAA;cAAAT,CAAA,EACA;gBAAA7B,MAAA;cAAA;YAAA;UACA;UACA,IAAAyI,OAAA,CAAA9B,sBAAA,IAAA8B,OAAA,CAAA9B,sBAAA,KAAA8B,OAAA,CAAA7B,iBAAA;YACAoF,OAAA,CAAA5J,QAAA;cACAC,OAAA;cACAC,IAAA;YACA;YAAA;cAAAT,CAAA,EACA;gBAAA7B,MAAA;cAAA;YAAA;UACA;UACA,IAAAoH,WAAA,GAAAoF,KAAA,CAAA3M,IAAA,KAAA4M,GAAA,CAAAhE,OAAA,CAAApB,eAAA,CAAAC,KAAA;UACAF,WAAA,CAAArB,OAAA,WAAA+B,IAAA;YACA,IAAA4E,MAAA,GAAAV,OAAA,CAAArN,WAAA,CAAA0E,MAAA,WAAAxB,CAAA;cAAA,OAAAA,CAAA,CAAAsE,YAAA,KAAA2B,IAAA;YAAA;YACA,IAAA6E,UAAA,GAAAD,MAAA,CAAAhG,GAAA,WAAAkG,KAAA;cACA,IAAAC,KAAA,GAAAb,OAAA,CAAAjE,YAAA,CAAAU,OAAA,CAAAlB,IAAA,EAAAO,IAAA,EAAA8E,KAAA,CAAA5E,eAAA;cACA,IAAA8E,IAAA,GAAAd,OAAA,CAAA9D,eAAA,CAAAO,OAAA,CAAAlB,IAAA,EAAAO,IAAA,EAAA8E,KAAA,CAAA5E,eAAA;cACA,IAAAqD,GAAA;gBACA0B,YAAA,EAAAtE,OAAA,CAAAsE,YAAA;gBACAtD,SAAA,EAAAhB,OAAA,CAAAgB,SAAA;gBACAuD,WAAA,GAAAvE,OAAA,CAAAoE,KAAA;gBAAA;gBACAlD,SAAA,EAAAqC,OAAA,CAAAzM,KAAA;gBACA4G,YAAA,EAAA2B,IAAA;gBACAT,eAAA,EAAAoB,OAAA,CAAApB,eAAA;gBACAW,eAAA,EAAA4E,KAAA,CAAA5E,eAAA;gBACAiF,iBAAA,EAAAL,KAAA,CAAAK;cACA;cACA,OAAAxE,OAAA,CAAAoE,KAAA;cACA,OAAApE,OAAA,CAAAqE,IAAA;cACA,OAAAzB,GAAA;YACA;YACAxE,IAAA,CAAAtE,IAAA,CAAA2K,KAAA,CAAArG,IAAA,EAAAsG,kBAAA,CAAAR,UAAA;UACA;UACA,IAAAS,QAAA,GAAAvH,MAAA,CAAAwH,IAAA,CAAA5E,OAAA,EAAApF,MAAA,WAAAiK,CAAA;YAAA,OAAAA,CAAA,CAAAC,UAAA,CAAA9E,OAAA;UAAA;UACA2E,QAAA,CAAArH,OAAA,WAAAC,IAAA;YACA,OAAAyC,OAAA,CAAAzC,IAAA;UACA;UACA,OAAAyC,OAAA;UACA,OAAAA,OAAA;UACA,OAAAA,OAAA;UACAA,OAAA,CAAA+E,gBAAA,GAAA3G,IAAA;QACA;QAAA4G,IAAA;MA9DA,SAAApB,CAAA,MAAAA,CAAA,GAAA3B,SAAA,CAAAzD,MAAA,EAAAoF,CAAA;QAAAoB,IAAA,GAAArB,KAAA;QAAA,IAAAqB,IAAA,SAAAA,IAAA,CAAA5L,CAAA;MAAA;MA+DA;QAAA6I,SAAA,EAAAA,SAAA;QAAA1K,MAAA;MAAA;IACA;IACAgL,eAAA,WAAAA,gBAAAN,SAAA,EAAAH,OAAA;MAAA,IAAAmD,OAAA;MAAA,OAAAtN,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAqN,SAAA;QAAA,IAAAlC,IAAA,EAAAJ,GAAA,EAAA1D,CAAA,EAAAiG,MAAA,EAAAC,oBAAA,EAAAtC,OAAA,EAAAC,SAAA,EAAAlH,MAAA,EAAAwJ,YAAA;QAAA,OAAAzN,mBAAA,GAAAG,IAAA,UAAAuN,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArN,IAAA,GAAAqN,SAAA,CAAAlO,IAAA;YAAA;cACA0I,OAAA,CAAAyC,GAAA;cACAQ,IAAA,GAAAiC,OAAA,CAAAnO,KAAA,GAAApE,sBAAA,GAAAC,sBAAA;cACAiQ,GAAA;cACA,IAAAqC,OAAA,CAAAnO,KAAA;gBACA8L,GAAA,CAAA1F,eAAA,GAAA+E,SAAA;gBACA/C,CAAA;gBACA,KAAAiG,MAAA,IAAAF,OAAA,CAAAtG,WAAA;kBACA,IAAAsG,OAAA,CAAAtG,WAAA,CAAA6G,cAAA,CAAAL,MAAA;oBACAjG,CAAA,CAAApF,IAAA,CAAAmL,OAAA,CAAAtG,WAAA,CAAAwG,MAAA;kBACA;gBACA;gBACAvC,GAAA,CAAAzF,YAAA,GAAA+B,CAAA;cACA;gBACA0D,GAAA,CAAA5E,cAAA,GAAAiE,SAAA;cACA;cACA,IAAAgD,OAAA,CAAAjO,MAAA;gBACA4L,GAAA,CAAA3F,cAAA,GAAAgI,OAAA,CAAA5Q,UAAA;cACA;gBAAA+Q,oBAAA,GAKAH,OAAA,CAAA5M,MAAA,CAAAf,KAAA,EAHAwL,OAAA,GAAAsC,oBAAA,CAAAtC,OAAA,EACAC,SAAA,GAAAqC,oBAAA,CAAArC,SAAA,EACAlH,MAAA,GAAAuJ,oBAAA,CAAAvJ,MAAA;gBAEA+G,GAAA,CAAA3F,cAAA,GAAApG,aAAA,CAAAA,aAAA,KACAoO,OAAA,CAAA5Q,UAAA;kBACA8O,UAAA,EAAAJ,SAAA;kBACAK,cAAA,EAAAN,OAAA;kBACAO,OAAA,EAAAxH,MAAA;kBACAyH,eAAA,EAAA2B,OAAA,CAAA1M,KAAA;gBAAA,EACA;cACA;cACA8M,YAAA;cACAtF,OAAA,CAAAyC,GAAA,QAAAI,GAAA;cAAA2C,SAAA,CAAAlO,IAAA;cAAA,OAEA2L,IAAA,CAAAJ,GAAA,EAAA9G,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACA,KAAA8F,OAAA;oBACAmD,OAAA,CAAAjQ,SAAA;oBACAiQ,OAAA,CAAAtL,QAAA;sBACAC,OAAA;sBACAC,IAAA;oBACA;oBACAoL,OAAA,CAAA3J,SAAA;kBACA;oBACA2J,OAAA,CAAAQ,oBAAA,GAAA1J,GAAA,CAAAI,IAAA;oBACAkJ,YAAA;oBACAtF,OAAA,CAAAyC,GAAA;kBACA;gBACA;kBACAyC,OAAA,CAAA3P,WAAA;kBACA2P,OAAA,CAAAjQ,SAAA;kBACAiQ,OAAA,CAAAtL,QAAA;oBACAC,OAAA,EAAAmC,GAAA,CAAAU,OAAA;oBACA5C,IAAA;kBACA;gBACA;cACA;YAAA;cACAkG,OAAA,CAAAyC,GAAA;cAAA,OAAA+C,SAAA,CAAAlD,MAAA,WACAgD,YAAA;YAAA;YAAA;cAAA,OAAAE,SAAA,CAAAtM,IAAA;UAAA;QAAA,GAAAiM,QAAA;MAAA;IACA;IACAQ,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,KAAA1Q,aAAA;MACA2Q,UAAA;QACA,IAAAC,aAAA,OAAA7B,GAAA,CAAA2B,OAAA,CAAA5Q,iBAAA,CAAAkJ,GAAA,WAAA7E,CAAA;UAAA,OAAAA,CAAA,CAAA0F,IAAA;QAAA;QACA6G,OAAA,CAAA/Q,MAAA,GAAA+Q,OAAA,CAAA/Q,MAAA,CAAAgG,MAAA,WAAA2C,IAAA;UACA,IAAAuI,UAAA,GAAAD,aAAA,CAAAE,GAAA,CAAAxI,IAAA,CAAAuB,IAAA;UACA,IAAAgH,UAAA;YAAA,IAAAE,oBAAA;YACA,IAAAvI,GAAA,GAAAkI,OAAA,CAAA7O,KAAA,GAAAyG,IAAA,CAAAyD,SAAA,KAAAgF,oBAAA,GAAAzI,IAAA,CAAA0D,cAAA,cAAA+E,oBAAA,cAAAA,oBAAA,SAAAzI,IAAA,CAAA2D,SAAA;YACA,OAAAyE,OAAA,CAAAvN,SAAA,CAAAqF,GAAA;UACA;UACA,QAAAqI,UAAA;QACA;QACAH,OAAA,CAAAM,SAAA,WAAApB,CAAA;UAAA,IAAAqB,mBAAA;UACA,CAAAA,mBAAA,GAAAP,OAAA,CAAArE,KAAA,uBAAA4E,mBAAA,eAAAA,mBAAA,CAAAC,SAAA,CAAAR,OAAA,CAAA5Q,iBAAA;UACA4Q,OAAA,CAAA5Q,iBAAA;QACA;QACA4Q,OAAA,CAAA1Q,aAAA;MACA;IACA;IACAmF,WAAA,WAAAA,YAAA;MAAA,IAAAgM,OAAA;MAAA,OAAAzO,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAwO,SAAA;QAAA,OAAAzO,mBAAA,GAAAG,IAAA,UAAAuO,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArO,IAAA,GAAAqO,SAAA,CAAAlP,IAAA;YAAA;cAAAkP,SAAA,CAAAlP,IAAA;cAAA,OACA7E,wBAAA;gBACAqH,IAAA,EAAAuM,OAAA,CAAAtP,KAAA;cACA,GAAAgF,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAoK,OAAA,CAAAlQ,WAAA,GAAA6F,GAAA,CAAAI,IAAA;gBACA;kBACAiK,OAAA,CAAAzM,QAAA;oBACAC,OAAA,EAAAmC,GAAA,CAAAU,OAAA;oBACA5C,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA0M,SAAA,CAAAtN,IAAA;UAAA;QAAA,GAAAoN,QAAA;MAAA;IACA;IACAG,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,KAAAnF,KAAA,eAAAC,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;QACA,IAAAkF,qBAAA,GAAAD,OAAA,CAAAnE,eAAA;UAAAL,SAAA,GAAAyE,qBAAA,CAAAzE,SAAA;UAAA1K,MAAA,GAAAmP,qBAAA,CAAAnP,MAAA;QACA,KAAAA,MAAA;QACAkP,OAAA,CAAAE,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAhN,IAAA;QACA,GAAAiC,IAAA;UACA2K,OAAA,CAAAK,iBAAA,CAAA7E,SAAA;QACA,GAAA8E,KAAA;UACAN,OAAA,CAAA9M,QAAA;YACAE,IAAA;YACAD,OAAA;UACA;QACA;MACA;IACA;IACAkN,iBAAA,WAAAA,kBAAA;MAAA,IAAAE,OAAA;MAAA,OAAArP,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAoP,SAAA;QAAA,IAAAC,kBAAA;QAAA,IAAAhF,SAAA,EAAAiF,UAAA;QAAA,OAAAvP,mBAAA,GAAAG,IAAA,UAAAqP,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnP,IAAA,GAAAmP,SAAA,CAAAhQ,IAAA;YAAA;cACA2P,OAAA,CAAAhS,SAAA;cAAA,OAAAkS,kBAAA,GACAF,OAAA,CAAA3S,UAAA,cAAA6S,kBAAA,eAAAA,kBAAA,CAAA5S,cAAA;gBAAA+S,SAAA,CAAAhQ,IAAA;gBAAA;cAAA;cAAAgQ,SAAA,CAAAhQ,IAAA;cAAA,OACA2P,OAAA,CAAAvF,SAAA;YAAA;cAAAS,SAAA,GAAAmF,SAAA,CAAAlM,IAAA;cACA4E,OAAA,CAAAyC,GAAA,sBAAAN,SAAA;cACAA,SAAA,IAAA8E,OAAA,CAAAM,QAAA,CAAAN,OAAA,CAAA3S,UAAA,CAAAkT,EAAA;cAAAF,SAAA,CAAAhQ,IAAA;cAAA;YAAA;cAAAgQ,SAAA,CAAAhQ,IAAA;cAAA,OAEA2P,OAAA,CAAAvF,SAAA;YAAA;cAAAS,UAAA,GAAAmF,SAAA,CAAAlM,IAAA;cACA+G,UAAA,IAAA8E,OAAA,CAAAM,QAAA,CAAAN,OAAA,CAAAvB,oBAAA;YAAA;YAAA;cAAA,OAAA4B,SAAA,CAAApO,IAAA;UAAA;QAAA,GAAAgO,QAAA;MAAA;IAEA;IACAK,QAAA,WAAAA,SAAAE,YAAA;MAAA,IAAAC,OAAA;MACA5U,qBAAA;QACA6U,eAAA,EAAAF;MACA,GAAA1L,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAyL,OAAA,CAAA9N,QAAA;YACAC,OAAA;YACAC,IAAA;UACA;UACA4N,OAAA,CAAAnM,SAAA;QACA;UACAmM,OAAA,CAAA9N,QAAA;YACAC,OAAA,EAAAmC,GAAA,CAAAU,OAAA;YACA5C,IAAA;UACA;QACA;MACA,GAAA8N,OAAA,WAAA9C,CAAA;QACA4C,OAAA,CAAAzS,SAAA;MACA,GAAA+R,KAAA,WAAAlC,CAAA;QACA4C,OAAA,CAAAzS,SAAA;MACA;IACA;IACA4S,WAAA,WAAAA,YAAAjK,KAAA;MAAA,IAAAkK,OAAA;MACA,IACAC,MAAA,GAMAnK,KAAA,CANAmK,MAAA;QACArJ,GAAA,GAKAd,KAAA,CALAc,GAAA;QAAAsJ,eAAA,GAKApK,KAAA,CAJAqK,QAAA;QACAT,EAAA,GAAAQ,eAAA,CAAAR,EAAA;QACAU,YAAA,GAAAF,eAAA,CAAAE,YAAA;MAGA,IAAAH,MAAA;QAAA,IAAAI,gBAAA;QACA,KAAAA,gBAAA,GAAAvK,KAAA,CAAAqK,QAAA,cAAAE,gBAAA,eAAAA,gBAAA,CAAAX,EAAA;UACA9I,GAAA,CAAA0J,aAAA,GAAAF,YAAA;UACAxJ,GAAA,CAAA2J,WAAA,GAAAb,EAAA;UACA,KAAAc,OAAA,CAAA5J,GAAA,EAAA8I,EAAA;QACA;UACA9I,GAAA,CAAA0J,aAAA;UACA1J,GAAA,CAAA2J,WAAA;QACA;MACA;QACA,KAAArT,iBAAA,CAAAuI,OAAA,WAAAC,IAAA;UAAA,IAAA+K,gBAAA;UACA,KAAAA,gBAAA,GAAA3K,KAAA,CAAAqK,QAAA,cAAAM,gBAAA,eAAAA,gBAAA,CAAAf,EAAA;YACAhK,IAAA,CAAA4K,aAAA,GAAAF,YAAA;YACA1K,IAAA,CAAA6K,WAAA,GAAAb,EAAA;YACAM,OAAA,CAAAQ,OAAA,CAAA9K,IAAA,EAAAgK,EAAA;UACA;YACAhK,IAAA,CAAA4K,aAAA;YACA5K,IAAA,CAAA6K,WAAA;UACA;QACA;MACA;IACA;IACAC,OAAA,WAAAA,QAAA5J,GAAA,EAAA8I,EAAA;MACA,IAAA9I,GAAA,aAAAA,GAAA,eAAAA,GAAA,CAAA8J,qBAAA;QACA,IAAA9J,GAAA,CAAA8J,qBAAA,KAAAhB,EAAA;UACA9I,GAAA,CAAAG,eAAA;QACA;MACA;QACAH,GAAA,CAAAG,eAAA;MACA;IACA;IACA4J,mBAAA,WAAAA,oBAAAV,MAAA,EAAArJ,GAAA;MAAA,IAAAgK,OAAA;MACA,KAAA9S,KAAA,GAAAmS,MAAA;MACA,KAAArS,gBAAA;MACA,KAAAC,MAAA;MACA,KAAAN,aAAA;MACA,KAAA6Q,SAAA,WAAApB,CAAA;QACA4D,OAAA,CAAAnH,KAAA,YAAAvI,SAAA,CAAA+O,MAAA,EAAArJ,GAAA;MACA;IACA;IACAiK,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MAAA,OAAAhR,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA+Q,SAAA;QAAA,OAAAhR,mBAAA,GAAAG,IAAA,UAAA8Q,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5Q,IAAA,GAAA4Q,SAAA,CAAAzR,IAAA;YAAA;cACA;AACA;AACA;AACA;;cAEAsR,OAAA,CAAAhC,QAAA;gBACAC,iBAAA;gBACAC,gBAAA;gBACAhN,IAAA;cACA,GAAAiC,IAAA;gBACA,IAAA6M,OAAA,CAAAhO,eAAA;kBACA,IAAAuE,CAAA,GAAAyJ,OAAA,CAAA5T,iBAAA,CAAAkJ,GAAA,WAAAV,IAAA;oBACA;sBACAwL,UAAA,KAAAjF,MAAA,CAAAvG,IAAA,CAAAyL,IAAA,SAAAlF,MAAA,CAAAvG,IAAA,CAAA6K,WAAA;oBACA;kBACA;kBACA,IAAAa,KAAA,GAAAlF,KAAA,CAAA3M,IAAA,KAAA4M,GAAA,CAAA9E,CAAA,CAAAjB,GAAA,WAAA7E,CAAA;oBAAA,OAAAA,CAAA,CAAA2P,UAAA;kBAAA;kBACA,IAAA5D,MAAA;kBACAvI,OAAA,CAAAsM,GAAA,CAAAD,KAAA,CAAAhL,GAAA,WAAA7E,CAAA;oBACA,IAAA+P,IAAA,GAAA/P,CAAA,CAAAyF,KAAA;oBACA,OAAA8J,OAAA,CAAAS,UAAA,CAAAD,IAAA,KAAAA,IAAA;kBACA,EACA,EAAArN,IAAA,WAAAC,GAAA;oBACA,IAAAsN,YAAA,GAAAtN,GAAA,CAAAuN,IAAA,WAAA/L,IAAA;sBAAA,OAAAA,IAAA,IAAAzH,SAAA;oBAAA;oBACA,IAAAuT,YAAA;sBACAV,OAAA,CAAAhP,QAAA;wBACAC,OAAA;wBACAC,IAAA;sBACA;oBACA;oBAEAkC,GAAA,CAAAuB,OAAA,WAAA0C,OAAA,EAAA1G,GAAA;sBACA6L,MAAA,CAAA8D,KAAA,CAAA3P,GAAA,KAAA0G,OAAA;oBACA;oBACA2I,OAAA,CAAA5T,iBAAA,CAAAuI,OAAA,WAAA0C,OAAA;sBACAA,OAAA,CAAApB,eAAA,GAAAuG,MAAA,IAAArB,MAAA,CAAA9D,OAAA,CAAAgJ,IAAA,SAAAlF,MAAA,CAAA9D,OAAA,CAAAoI,WAAA;sBACAO,OAAA,CAAAY,gBAAA,CAAAvJ,OAAA,EAAAA,OAAA,CAAApB,eAAA;oBACA;kBACA;gBACA;kBACA,IAAAM,EAAA,GAAAyJ,OAAA,CAAA5T,iBAAA,CAAAkJ,GAAA,WAAAV,IAAA;oBAAA,OAAAA,IAAA,CAAAyL,IAAA;kBAAA;kBACA,IAAAC,MAAA,GAAAlF,KAAA,CAAA3M,IAAA,KAAA4M,GAAA,CAAA9E,EAAA;kBACA,IAAAiG,OAAA;kBAEAvI,OAAA,CAAAsM,GAAA,CAAAD,MAAA,CAAAhL,GAAA,WAAA7E,CAAA;oBACA,OAAAuP,OAAA,CAAAS,UAAA,CAAAhQ,CAAA;kBACA,IAAA0C,IAAA,WAAAC,GAAA;oBACAA,GAAA,CAAAuB,OAAA,WAAA0C,OAAA,EAAA1G,GAAA;sBACA6L,OAAA,CAAA8D,MAAA,CAAA3P,GAAA,KAAA0G,OAAA;oBACA;oBACA2I,OAAA,CAAA5T,iBAAA,CAAAuI,OAAA,WAAA0C,OAAA;sBACAA,OAAA,CAAApB,eAAA,GAAAuG,OAAA,CAAAnF,OAAA,CAAAgJ,IAAA;sBACAL,OAAA,CAAAY,gBAAA,CAAAvJ,OAAA,EAAAA,OAAA,CAAApB,eAAA;oBACA;kBACA;gBACA;cACA,GAAAmI,KAAA;gBACA4B,OAAA,CAAAhP,QAAA;kBACAE,IAAA;kBACAD,OAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAkP,SAAA,CAAA7P,IAAA;UAAA;QAAA,GAAA2P,QAAA;MAAA;IACA;IACAY,gBAAA,WAAAA,iBAAAC,UAAA;MAAA,IAAAC,OAAA;MACA,WAAA9M,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACAtJ,kBAAA;UACAiW,UAAA,EAAAA,UAAA;UACA5P,IAAA,EAAA6P,OAAA,CAAA5S,KAAA;QACA,GAAAgF,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACA,IAAA2N,OAAA,GAAA5N,GAAA,CAAAI,IAAA,CAAA8B,GAAA,WAAA7E,CAAA;cAAA,OAAAA,CAAA,CAAAI,IAAA;YAAA;YACAqD,OAAA,CAAA8M,OAAA;UACA;YACAD,OAAA,CAAA/P,QAAA;cACAC,OAAA,EAAAmC,GAAA,CAAAU,OAAA;cACA5C,IAAA;YACA;UACA;QACA;MACA;IACA;IACAuP,UAAA,WAAAA,WAAA/J,IAAA,EAAAoK,UAAA;MAAA,IAAAG,OAAA;MACA,WAAAhN,OAAA,WAAAC,OAAA;QACA,IAAA+F,GAAA;UACAiH,cAAA,EAAAxK,IAAA;UACAxF,IAAA;QACA;QACA,IAAA+P,OAAA,CAAAjP,eAAA;UACAiI,GAAA,CAAA6G,UAAA,GAAAA,UAAA;QACA;QACAlW,cAAA,CAAAqP,GAAA,EAAA9G,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACA,IAAAD,GAAA,CAAAI,IAAA,CAAAA,IAAA,IAAAJ,GAAA,CAAAI,IAAA,CAAAA,IAAA,CAAAqC,MAAA;cACA,IAAA2K,IAAA,GAAApN,GAAA,CAAAI,IAAA,CAAAA,IAAA;cACA,IAAA2N,QAAA,GAAAX,IAAA,CAAAY,QAAA,IAAAZ,IAAA,CAAAY,QAAA,CAAAC,OAAA;cACAnN,OAAA,CAAAiN,QAAA;YACA;cACAjN,OAAA,CAAA/G,SAAA;YACA;UACA;YACA8T,OAAA,CAAAjQ,QAAA;cACAC,OAAA,EAAAmC,GAAA,CAAAU,OAAA;cACA5C,IAAA;YACA;UACA;QACA;MACA;IACA;IACAoQ,WAAA,WAAAA,YAAAxL,GAAA;MACA,KAAAkB,WAAA,CAAAlB,GAAA;IACA;IACAkB,WAAA,WAAAA,YAAAlB,GAAA;MACA,IAAAyL,eAAA,GAAA9M,MAAA,CAAAwH,IAAA,CAAAnG,GAAA,EACA7D,MAAA,WAAAxB,CAAA;QAAA,QAAAA,CAAA,CAAA+Q,QAAA,WAAA/Q,CAAA,CAAA0L,UAAA,CAAArG,GAAA,CAAAK,IAAA,KAAA1F,CAAA,CAAAoF,MAAA,GAAAC,GAAA,CAAAK,IAAA,CAAAN,MAAA;MAAA;MACA0L,eAAA,CAAA5M,OAAA,WAAA8M,GAAA;QACA,IAAAC,OAAA,GAAAD,GAAA,CAAAvL,KAAA,CAAA/K,YAAA;QACA,IAAAwW,UAAA,GAAAJ,eAAA,CAAAtP,MAAA,WAAA2P,CAAA;UACA,IAAAlL,IAAA,GAAAkL,CAAA,CAAA1L,KAAA,CAAA/K,YAAA;UACA,OAAAyW,CAAA,KAAAH,GAAA,IAAA/K,IAAA,KAAAgL,OAAA;QACA,GAAAG,MAAA,WAAAC,GAAA,EAAAlN,IAAA;UACA,OAAAkN,GAAA,GAAAnX,OAAA,CAAAmL,GAAA,CAAAlB,IAAA,GAAAI,KAAA;QACA;QACAc,GAAA,CAAA2L,GAAA,GAAAtW,YAAA,YAAA2K,GAAA,CAAA2B,cAAA,GAAAkK,UAAA;MACA;IACA;IACAI,WAAA,WAAAA,YAAAC,IAAA;MAAA,IAAAC,GAAA,GAAAD,IAAA,CAAAC,GAAA;QAAAC,GAAA,GAAAF,IAAA,CAAAE,GAAA;MACA,IAAA3I,SAAA;MACA,SAAA0B,CAAA,MAAAA,CAAA,GAAAgH,GAAA,CAAApM,MAAA,EAAAoF,CAAA;QACA,IAAArG,IAAA,GAAAqN,GAAA,CAAAhH,CAAA;QACA,IAAArG,IAAA,CAAAuN,YAAA,IAAAvN,IAAA,CAAAuN,YAAA,KAAAD,GAAA;UACA3I,SAAA;UACA;QACA;QACA3E,IAAA,CAAAqB,eAAA,GAAAiM,GAAA;MACA;MACA,KAAA3I,SAAA;QACA,KAAAvI,QAAA;UACAC,OAAA;UACAC,IAAA;QACA;MACA;IACA;IACA0P,gBAAA,WAAAA,iBAAA9K,GAAA,EAAAoM,GAAA;MAAA,IAAAE,IAAA;QAAAC,OAAA;MACA,IAAAH,GAAA;QACApM,GAAA,CAAAG,eAAA,GAAAiM,GAAA;MACA;QACAA,GAAA,GAAApM,GAAA,CAAAG,eAAA;MACA;MACA,IAAAR,IAAA,KAAA2M,IAAA,GAAAF,GAAA,cAAAE,IAAA,uBAAAA,IAAA,CAAAlM,KAAA;MACA,KAAA3I,WAAA,CAAAoH,OAAA,WAAA0C,OAAA,EAAA1G,GAAA;QACA,IAAA2G,GAAA,GAAA7B,IAAA,CAAAkL,IAAA,WAAA2B,CAAA;UAAA,OAAAA,CAAA,KAAAjL,OAAA,CAAAtC,YAAA;QAAA;QACA,IAAA2B,IAAA,GAAA2L,OAAA,CAAA1L,YAAA,CAAAb,GAAA,CAAAK,IAAA,EAAAkB,OAAA,CAAAtC,YAAA,EAAAsC,OAAA,CAAAT,eAAA;QACA,IAAAC,GAAA,GAAAwL,OAAA,CAAAvL,eAAA,CAAAhB,GAAA,CAAAK,IAAA,EAAAkB,OAAA,CAAAtC,YAAA,EAAAsC,OAAA,CAAAT,eAAA;QACA,IAAAU,GAAA;UACA,KAAAxB,GAAA,CAAAY,IAAA;YACA2L,OAAA,CAAAE,IAAA,CAAAzM,GAAA,EAAAY,IAAA;YACA2L,OAAA,CAAAE,IAAA,CAAAzM,GAAA,EAAAe,GAAA,EAAAf,GAAA,CAAA2B,cAAA;UACA;QACA;UACA4K,OAAA,CAAAG,OAAA,CAAA1M,GAAA,EAAAY,IAAA;UACA2L,OAAA,CAAAG,OAAA,CAAA1M,GAAA,EAAAe,GAAA;QACA;MACA;IACA;IACA4L,mBAAA,WAAAA,oBAAAC,UAAA,EAAAC,WAAA;MACA,KAAAD,UAAA;MACA,IAAAjN,IAAA,IAAAiN,UAAA,aAAAA,UAAA,uBAAAA,UAAA,CAAAxM,KAAA;MACA,SAAAT,IAAA,CAAAkL,IAAA,WAAAlQ,CAAA;QAAA,OAAAA,CAAA,KAAAkS,WAAA;MAAA;IACA;IAEA5Q,cAAA,WAAAA,eAAA2E,IAAA;MAAA,IAAAkM,OAAA;MAAA,OAAA5T,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA2T,SAAA;QAAA,OAAA5T,mBAAA,GAAAG,IAAA,UAAA0T,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxT,IAAA,GAAAwT,SAAA,CAAArU,IAAA;YAAA;cAAAqU,SAAA,CAAArU,IAAA;cAAA,OACApE,aAAA;gBACAoM,IAAA,EAAAA;cACA,GAAAvD,IAAA,WAAAC,GAAA;gBACA,IAAAC,SAAA,GAAAD,GAAA,CAAAC,SAAA;kBAAAG,IAAA,GAAAJ,GAAA,CAAAI,IAAA;kBAAAM,OAAA,GAAAV,GAAA,CAAAU,OAAA;gBACA,IAAAT,SAAA;kBACAuP,OAAA,CAAA1W,QAAA,GAAAuI,MAAA,CAAAC,MAAA,KAAAkO,OAAA,CAAA1W,QAAA,EAAAsH,IAAA,CAAAwP,IAAA;kBACA,IAAAvN,IAAA,GAAAjC,IAAA,CAAAyP,UAAA;kBACAL,OAAA,CAAA7R,WAAA,GAAA0E,IAAA,CAAAyN,IAAA,WAAAtO,IAAA;oBAAA,OAAAA,IAAA,CAAA/D,IAAA;kBAAA;kBACA+R,OAAA,CAAAO,YAAA,GAAA1N,IAAA,CAAAyN,IAAA,WAAAtO,IAAA;oBAAA,OAAAA,IAAA,CAAA/D,IAAA;kBAAA;kBACA+R,OAAA,CAAA5W,OAAA,GAAA4W,OAAA,CAAAQ,gBAAA,CAAA3N,IAAA;gBACA;kBACAmN,OAAA,CAAA5R,QAAA;oBACAC,OAAA,EAAA6C,OAAA;oBACA5C,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA6R,SAAA,CAAAzS,IAAA;UAAA;QAAA,GAAAuS,QAAA;MAAA;IACA;IACAO,gBAAA,WAAAA,iBAAA3N,IAAA;MACA,OAAAA,IAAA,CAAAxD,MAAA,WAAAxB,CAAA;QAAA,OAAAA,CAAA,CAAA4S,UAAA;MAAA,GAAA/N,GAAA,WAAAV,IAAA;QACA,IAAArK,UAAA,CAAA+Y,QAAA,CAAA1O,IAAA,CAAA/D,IAAA;UACA+D,IAAA,CAAA2O,KAAA;QACA;QACA,OAAA3O,IAAA;MACA;IACA;IACA4O,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAC,aAAA;MAAA,IAAA5N,GAAA,GAAA2N,KAAA,CAAA3N,GAAA;QAAA6N,MAAA,GAAAF,KAAA,CAAAE,MAAA;QAAAC,WAAA,GAAAH,KAAA,CAAAG,WAAA;MACA,SAAAxV,MAAA;MACA,IAAAuU,WAAA,IAAAe,aAAA,GAAAC,MAAA,CAAAE,KAAA,cAAAH,aAAA,uBAAAA,aAAA,CAAAxN,KAAA;MACA,YAAAuM,mBAAA,CAAA3M,GAAA,CAAAG,eAAA,EAAA0M,WAAA;IACA;IACAmB,aAAA,WAAAA,cAAA5S,IAAA,EAAA4E,GAAA;MAAA,IAAAiO,OAAA;MACA,SAAA/R,eAAA;QACA,IAAAd,IAAA;UACA,IAAA8S,QAAA,QAAAC,qBAAA;UACA,KAAAD,QAAA;QACA;MACA;MACA,KAAAhX,KAAA,GAAAkE,IAAA;MACA,KAAApE,gBAAA;MACA,KAAAC,MAAA,QAAAoB,KAAA;MACA,KAAA1B,aAAA;MACA,KAAA6Q,SAAA,WAAApB,CAAA;QACA6H,OAAA,CAAApL,KAAA,YAAAuL,OAAA,CAAAhT,IAAA,UAAA4E,GAAA,IAAAiO,OAAA,CAAA3X,iBAAA,EAAA8E,IAAA,SAAA4E,GAAA,CAAAG,eAAA;MACA;IACA;IACAgO,qBAAA,WAAAA,sBAAA;MACA,IAAAE,QAAA;MACA,IAAAC,MAAA,QAAAhY,iBAAA,IAAAoT,aAAA;MACA,SAAAvE,CAAA,MAAAA,CAAA,QAAA7O,iBAAA,CAAAyJ,MAAA,EAAAoF,CAAA;QACA,IAAArG,IAAA,QAAAxI,iBAAA,CAAA6O,CAAA;QACA,IAAArG,IAAA,CAAA4K,aAAA,KAAA4E,MAAA;UACAD,QAAA;UACA;QACA;MACA;MACA,KAAAA,QAAA;QACA,KAAAnT,QAAA;UACAC,OAAA;UACAC,IAAA;QACA;MACA;MACA,OAAAiT,QAAA;IACA;IACAE,gBAAA,WAAAA,iBAAAnT,IAAA,EAAA+Q,GAAA;MACA,IAAAqC,WAAA;MACA,SAAArJ,CAAA,MAAAA,CAAA,GAAAgH,GAAA,CAAApM,MAAA,EAAAoF,CAAA;QACA,IAAArG,IAAA,GAAAqN,GAAA,CAAAhH,CAAA;QACA,KAAArG,IAAA,CAAA4K,aAAA;UACA8E,WAAA;UACA;QACA;MACA;MACA,KAAAA,WAAA;QACA,KAAAtT,QAAA;UACAC,OAAA;UACAC,IAAA;QACA;MACA;MACA,OAAAoT,WAAA;IACA;IACAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MAAA,IAAAtT,IAAA,GAAA0E,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAzI,SAAA,GAAAyI,SAAA;MACA,SAAAzH,KAAA;QACA,KAAAnB,KAAA;MACA;QACA,KAAAA,KAAA;MACA;MACA,KAAAF,gBAAA;MACA,KAAAC,MAAA;MACA,KAAAL,YAAA;MACA,KAAA4Q,SAAA,WAAApB,CAAA;QACAsI,OAAA,CAAA7L,KAAA,UAAA8L,WAAA;MACA;IACA;IACA9N,YAAA,WAAAA,aAAAR,IAAA,EAAAwM,WAAA,EAAA+B,SAAA;MACA,UAAAvJ,MAAA,CAAAhF,IAAA,EAAAgF,MAAA,CAAAhQ,YAAA,EAAAgQ,MAAA,CAAAwH,WAAA,EAAAxH,MAAA,CAAAhQ,YAAA,EAAAgQ,MAAA,CAAAuJ,SAAA;IACA;IACA5N,eAAA,WAAAA,gBAAAX,IAAA,EAAAwM,WAAA,EAAA+B,SAAA;MACA,YAAA/N,YAAA,CAAAR,IAAA,EAAAwM,WAAA,EAAA+B,SAAA,OAAAvJ,MAAA,CAAAhQ,YAAA;IACA;IACAwZ,gBAAA,WAAAA,iBAAAlU,CAAA;MACA,IAAAA,CAAA;QACA,KAAAqT,aAAA;MACA,WAAArT,CAAA;QACA,KAAAsP,cAAA;MACA;IACA;IACA6E,gBAAA,WAAAA,iBAAA1T,IAAA,EAAA4E,GAAA;MAAA,IAAA+O,OAAA;MACA,KAAA7X,KAAA;MACA,KAAAF,gBAAA;MACA,KAAAC,MAAA;MACA,KAAAN,aAAA;MACA,KAAA6Q,SAAA,WAAApB,CAAA;QACA2I,OAAA,CAAAlM,KAAA,YAAAmM,SAAA,CAAA5T,IAAA,QAAAA,IAAA,UAAA4E,GAAA,IAAA+O,OAAA,CAAAzY,iBAAA;MACA;IACA;IACA2Y,aAAA,WAAAA,cAAA;MACA,IAAAzN,GAAA;MACA,KAAArL,MAAA,CAAA0I,OAAA,WAAA0C,OAAA,EAAA1G,GAAA;QACA0G,OAAA,CAAA2N,OAAA,IAAA3N,OAAA,CAAA2N,OAAA;QACA,IAAA3N,OAAA,CAAA2N,OAAA;UACA1N,GAAA,CAAAnG,IAAA,CAAAkG,OAAA;QACA;MACA;MACA,KAAAjL,iBAAA,GAAAkL,GAAA;MACA,SAAAlL,iBAAA,CAAAyJ,MAAA,UAAA5J,MAAA,CAAA4J,MAAA;QACA,KAAA8C,KAAA,WAAAsM,iBAAA;MACA;MACA,SAAA7Y,iBAAA,CAAAyJ,MAAA;QACA,KAAA8C,KAAA,WAAAsM,iBAAA;MACA;IACA;IACAC,cAAA,WAAAA,eAAA;MAAA,IAAAC,eAAA;QAAAC,OAAA;MACA,IAAAC,MAAA,QAAA1M,KAAA,CAAA0M,MAAA;MACA,IAAA1B,MAAA,GAAA0B,MAAA,CAAAC,gBAAA;MACA,MAAA3B,MAAA,aAAAA,MAAA,gBAAAwB,eAAA,GAAAxB,MAAA,CAAA4B,OAAA,cAAAJ,eAAA,eAAAA,eAAA,CAAAtP,MAAA;MACA8N,MAAA,CAAA4B,OAAA,CAAA5Q,OAAA,WAAA6Q,CAAA;QACAA,CAAA,CAAAR,OAAA,GAAAQ,CAAA,CAAAxQ,KAAA,KAAAoQ,OAAA,CAAA3Z,UAAA;MACA;MACA4Z,MAAA,CAAAI,UAAA;IACA;IACAzV,OAAA,WAAAA,QAAA;MAAA,IAAA0V,OAAA;MACAza,eAAA;QAAA0a,UAAA;MAAA,GAAAxS,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAqS,OAAA,CAAApY,UAAA,GAAA8F,GAAA,CAAAI,IAAA,CAAA8B,GAAA,WAAA7E,CAAA;YACA;cACAmV,KAAA,EAAAnV,CAAA,CAAAoV,IAAA;cACA7Q,KAAA,EAAAvE,CAAA,CAAAoV,IAAA;cACAnP,IAAA,EAAAjG,CAAA,CAAAI;YACA;UACA;QACA;UACA6U,OAAA,CAAA1U,QAAA;YACAC,OAAA,EAAAmC,GAAA,CAAAU,OAAA;YACA5C,IAAA;UACA;QACA;MACA;IACA;IACA4U,cAAA,WAAAA,eAAAtF,IAAA;MACA,KAAAvL,iBAAA,CAAAuL,IAAA;IACA;EAAA;AAEA", "ignoreList": []}]}
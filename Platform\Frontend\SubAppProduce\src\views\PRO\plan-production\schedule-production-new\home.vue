<template>
  <div class="container abs100">
    <div class="cs-tabs">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="进行中" :name="statusMap.ordered" />
        <el-tab-pane label="已完成" :name="statusMap.finish" />
        <el-tab-pane label="未下达" :name="statusMap.unOrdered" />
        <!--        <el-tab-pane label="已下达" :name="statusMap.ordered" />-->
      </el-tabs>
    </div>
    <div class="search-wrapper">
      <el-form ref="form" :model="queryForm" inline label-width="100px">
        <el-form-item>
          <div class="btn-wrapper">
            <el-button
              v-if="getRoles(isCom?'ComAddSchedule':'PartAddScheduleNew')"
              type="primary"
              @click="handleAdd"
            >新增排产单</el-button>
            <template v-if="isCom">
              <!--              <el-button-->
              <!--                v-if="getRoles('ImportComPartsSchedule')"-->
              <!--                @click="handleComImport(null, 3)"-->
              <!--              >导入构/零件排产</el-button>-->
              <el-button
                v-if="getRoles('ImportComUnitPartsSchedule')"
                @click="handleComImportNew(null, 3)"
              >导入下级排产</el-button>
              <!--              <el-button v-if="getRoles('ImportComSchedule')" @click="handleComImport(null, 1)">导入构件排产</el-button>-->
              <el-button v-if="getRoles('ImportComScheduleNew')" @click="handleComImportNew(null, 1)">导入{{ comName }}排产</el-button>
            </template>
            <template v-else>
              <el-button v-if="getRoles('ImportPartSchedule')" @click="handlePartImport">导入{{ partName }}排产</el-button>
            </template>
          </div>
        </el-form-item>
        <el-form-item label="项目名称" prop="projectId">
          <el-select
            v-model="queryForm.projectId"
            filterable
            clearable
            placeholder="请选择"
            style="width: 100%"
            @change="projectChange"
          >
            <el-option
              v-for="item in projectOption"
              :key="item.Id"
              :label="item.Short_Name"
              :value="item.Id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="区域名称" prop="areaId">
          <el-tree-select
            ref="treeSelect"
            v-model="queryForm.areaId"
            :disabled="!queryForm.projectId"
            :select-params="{
              clearable: true,
            }"
            class="cs-tree-x"
            :tree-params="treeParams"
            @select-clear="areaClear"
            @node-click="areaChange"
          />
        </el-form-item>
        <el-form-item label="批次" prop="install">
          <el-select
            v-model="queryForm.install"
            :disabled="!queryForm.areaId"
            clearable
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in installOption"
              :key="item.Id"
              :label="item.Name"
              :value="item.Id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="workshopEnabled"
          label="所属车间"
          prop="Workshop_Id"
        >
          <el-select
            v-model="queryForm.Workshop_Id"
            filterable
            clearable
            placeholder="请选择"
            style="width: 100%"
            @change="projectChange"
          >
            <el-option
              v-for="item in workShopOption"
              :key="item.Id"
              :label="item.Display_Name"
              :value="item.Id"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-show="activeName!==statusMap.unOrdered" label="排产单号" prop="Schduling_Code">
          <el-input v-model="queryForm.Schduling_Code" clearable type="text" />
        </el-form-item>
        <el-form-item label="要求完成时间" prop="finishTime">
          <el-date-picker
            v-model="finishTime"
            value-format="yyyy-MM-dd"
            style="width: 100%"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="primary" @click="search(1)">搜索</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="main-wrapper">
      <div class="tb-wrapper">
        <vxe-table
          :empty-render="{name: 'NotData'}"
          show-header-overflow
          :loading="pgLoading"
          element-loading-spinner="el-icon-loading"
          element-loading-text="拼命加载中"
          empty-text="暂无数据"
          class="cs-vxe-table"
          height="100%"
          align="left"
          stripe
          :data="tbData"
          :row-config="{ isCurrent: true, isHover: true }"
          resizable
          :tooltip-config="{ enterable: true}"
        >
          <template v-for="item in columns">
            <vxe-column
              :key="item.Code"
              :min-width="item.Width"
              :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
              show-overflow="tooltip"
              sortable
              :align="item.Align"
              :field="item.Code"
              :title="item.Display_Name"
            >
              <template v-if="item.Code === 'Schduling_Code'" #default="{ row }">
                <el-link type="primary" @click="handleView(row)">{{ row.Schduling_Code }}</el-link>
              </template>
              <template v-else-if="['Finish_Date','Operator_Date','Order_Date'].includes(item.Code) " #default="{ row }">
                {{ moment(row[item.Code]) }}
              </template>
              <template v-else-if="item.Code === 'Status'" #default="{ row }">
                {{ row.Status === 0 ? "草稿" : "已下达" }}
              </template>
              <template v-else-if="item.Code === 'Cancel_Count'" #default="{ row }">
                <el-link
                  v-if="row.Cancel_Count"
                  type="primary"
                  @click="handleCanCelDetail(row)"
                >{{ row.Cancel_Count }}</el-link>
                <span v-else>{{ row.Cancel_Count }}</span>
              </template>
            </vxe-column>

          </template>
          <vxe-column v-if="statusMap.finish!==activeName" fixed="right" title="操作" :width="activeName === statusMap.ordered ? 170 : 220" :min-width="activeName === statusMap.ordered ? 170 : 220" show-overflow>
            <template #default="{ row }">
              <el-button
                v-if="canEditBtn(row)"
                type="text"
                @click="handleEdit(row)"
              >修改
              </el-button>
              <!--              <el-button
                v-if="canImportBtn(row)"
                type="text"
                @click="handleRowImport(row)"
              >导入
              </el-button>-->
              <el-button
                v-if="canOrderBtn(row)"
                type="text"
                @click="handleSave(row)"
              >下达
              </el-button>
              <el-button
                v-if="statusMap.unOrdered===activeName"
                type="text"
                @click="handleView(row)"
              >查看</el-button>
              <el-button
                v-if="canWithdrawBtn(row)"
                type="text"
                @click="handleWithdraw(row)"
              >撤销排产
              </el-button>
              <el-button
                v-if="canWithdrawDraftBtn(row)"
                type="text"
                @click="handleWithdrawAll(row)"
              >撤回草稿
              </el-button>
              <el-button
                v-if="canDeleteBtn(row)"
                type="text"
                style="color: red"
                @click="handleDelete(row)"
              >删除
              </el-button>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <div class="data-info">
        <Pagination
          :total="total"
          max-height="100%"
          :page-sizes="tablePageSize"
          :page.sync="queryInfo.Page"
          :limit.sync="queryInfo.PageSize"
          layout="total, sizes, prev, pager, next, jumper"
          @pagination="pageChange"
        />
      </div>

      <!--      <div
              v-loading="pgLoading"
              style="height: 0; flex: 1"
              class="cs-z-tb-wrapper"
              element-loading-text="加载中"
              element-loading-spinner="el-icon-loading"
            >
              <dynamic-data-table
                ref="dyTable"
                :columns="columns"
                :data="tbData"
                :config="tbConfig"
                :page="queryInfo.Page"
                :total="total"
                border
                class="cs-plm-dy-table"
                stripe
                @gridPageChange="handlePageChange"
                @gridSizeChange="handlePageChange"
              >
                <template slot="Schduling_Code" slot-scope="{ row }">
                  <el-link type="primary" @click="handleView(row)">{{ row.Schduling_Code }}</el-link>
                </template>
                <template slot="Finish_Date" slot-scope="{ row }">
                  {{ moment(row.Finish_Date) }}
                </template>
                <template slot="Operator_Date" slot-scope="{ row }">
                  {{ moment(row.Operator_Date) }}
                </template>
                <template slot="Order_Date" slot-scope="{ row }">
                  {{ moment(row.Order_Date) }}
                </template>
                <template slot="Status" slot-scope="{ row }">
                  {{ row.Status === 0 ? "草稿" : "已下达" }}
                </template>
                <template slot="Cancel_Count" slot-scope="{ row }">
                  <el-link
                    v-if="row.Cancel_Count"
                    type="primary"
                    @click="handleCanCelDetail(row)"
                  >{{ row.Cancel_Count }}</el-link>
                  <span v-else>{{ row.Cancel_Count }}</span>
                </template>

                <template v-if="activeName!==statusMap.finish" slot="op" slot-scope="{ row }">
                  <el-button
                    v-if="canEditBtn(row)"
                    type="text"
                    @click="handleEdit(row)"
                  >修改
                  </el-button>
                  <el-button
                    v-if="canImportBtn(row)"
                    type="text"
                    @click="handleRowImport(row)"
                  >导入
                  </el-button>
                  <el-button
                    v-if="canOrderBtn(row)"
                    type="text"
                    @click="handleSave(row)"
                  >下达
                  </el-button>
                  <el-button v-if="statusMap.unOrdered===activeName" type="text" @click="handleView(row)">查看</el-button>
                  &lt;!&ndash; row.Cancel_Count 暂时不加撤回数量为0判断&ndash;&gt;
                  <el-button
                    v-if="canWithdrawBtn(row)"
                    type="text"
                    @click="handleWithdraw(row)"
                  >撤销排产
                  </el-button>
                  <el-button
                    v-if="canWithdrawDraftBtn(row)"
                    type="text"
                    @click="handleWithdrawAll(row)"
                  >撤回草稿
                  </el-button>
                  <el-button
                    v-if="canDeleteBtn(row)"
                    type="text"
                    style="color: red"
                    @click="handleDelete(row)"
                  >删除
                  </el-button>
                </template>

              </dynamic-data-table>
            </div>-->
    </div>

    <el-dialog
      v-if="dialogVisible"
      v-dialogDrag
      class="plm-custom-dialog"
      :title="title"
      :visible.sync="dialogVisible"
      :width="dWidth"
      @close="handleClose"
    >
      <component
        :is="currentComponent"
        ref="content"
        :com-name="comName"
        @close="handleClose"
        @refresh="fetchData(1)"
      />
    </el-dialog>
  </div>
</template>

<script>
import { debounce } from '@/utils'
import AddSchedule from './components/AddSchedule'
import DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'
import getTbInfo from '@/mixins/PRO/get-table-info'
import { getDraftQuery } from './constant'
import {
  DelSchdulingPlanById,
  GetCompSchdulingPageList,
  SaveSchdulingTaskById, WithdrawScheduling
} from '@/api/PRO/production-task'
import { GetPartSchdulingPageList } from '@/api/PRO/production-part'
import ComImport from './components/ComImport'
import Withdraw from './components/Withdraw'
import PartImport from './components/partImport'
import getQueryInfo from './mixin/index'
import moment from 'moment'
import WithdrawHistory from './components/WithdrawHistory'
import { timeFormat } from '@/filters'
import { mapGetters } from 'vuex'
import { GetWorkshopPageList } from '@/api/PRO/basic-information/workshop'
import Pagination from '@/components/Pagination/index.vue'
import { tablePageSize } from '@/views/PRO/setting'
import { RoleAuthorization } from '@/api/user'
import { GetBOMInfo } from '@/views/PRO/bom-setting/utils'

export default {
  inject: ['pageType'],
  components: {
    Pagination,
    WithdrawHistory,
    AddSchedule,
    DynamicDataTable,
    Withdraw,
    ComImport,
    PartImport
  },
  mixins: [getTbInfo, getQueryInfo],
  data() {
    return {
      bomList: [],
      statusMap: {
        finish: '9', // 已完成
        unOrdered: '0', // 未下达
        ordered: '1' // 进行中
      },
      scheduleType: {
        comp: 1,
        part: 2,
        comp_part: 3
      },
      activeName: '1',
      pgLoading: false,
      dialogVisible: false,
      currentComponent: '',
      title: '',
      dWidth: '40%',
      queryForm: {
        Finish_Date_Begin: '',
        Finish_Date_End: '',
        Status: 0,
        Workshop_Id: '',
        Schduling_Code: ''
      },
      tablePageSize: tablePageSize,
      queryInfo: {
        Page: 1,
        PageSize: tablePageSize[0]
      },
      workShopOption: [],
      columns: [],
      tbData: [],
      total: 0,
      search: () => ({}),
      roleList: [],
      comName: '',
      partName: ''
    }
  },
  computed: {
    isCom() {
      return this.pageType === 'com'
    },
    finishTime: {
      get() {
        return [
          timeFormat(this.queryForm.Finish_Date_Begin),
          timeFormat(this.queryForm.Finish_Date_End)
        ]
      },
      set(v) {
        if (!v) {
          this.queryForm.Finish_Date_Begin = ''
          this.queryForm.Finish_Date_End = ''
        } else {
          const start = v[0]
          const end = v[1]
          this.queryForm.Finish_Date_Begin = timeFormat(start)
          this.queryForm.Finish_Date_End = timeFormat(end)
        }
      }
    },
    ...mapGetters('factoryInfo', ['workshopEnabled'])
  },
  watch: {
    activeName(newValue, oldValue) {
      this.queryForm.Status = +newValue
      this.pgLoading = true
      this.getPageInfo()
    }
  },
  activated() {
    console.log('activated')
    !this.isUpdate && this.fetchData(1)
  },
  async mounted() {
    const { list, comName, partName } = await GetBOMInfo(-1)
    this.bomList = list || []
    this.comName = comName
    this.partName = partName
    this.isUpdate = true
    this.getRoleAuthorization()
    await this.getFactoryInfo()
    this.workshopEnabled && this.getWorkshop()
    this.search = debounce(this.fetchData, 800, true)
    await this.getPageInfo()
  },
  methods: {
    async getPageInfo() {
      const tab = this.activeName === '0' ? 'PROScheduleUnOrder' : (this.activeName === '1' ? 'PROScheduleIsOrder' : 'PROScheduleFinish')
      await this.getTableConfig(tab)
      if (!this.workshopEnabled) {
        this.columns = this.columns.filter(v => v.Code !== 'Workshop_Name')
      }
      this.fetchData()
    },
    handleClick() {

    },
    async getFactoryInfo() {
      await this.$store.dispatch('factoryInfo/getWorkshop')
    },
    canEditBtn({ Status, Schduling_Model }) {
      if (Schduling_Model === this.scheduleType.comp_part) {
        return false
      }
      return Status === +this.statusMap.unOrdered
    },
    canImportBtn({ Status, Schduling_Model }) {
      if (Schduling_Model === this.scheduleType.comp_part && !this.isCom) {
        return false
      }
      return Status === +this.statusMap.unOrdered
    },
    canOrderBtn({ Status, Schduling_Model }) {
      if (Schduling_Model === this.scheduleType.comp_part && !this.isCom) {
        return false
      }
      return Status === +this.statusMap.unOrdered
    },
    canWithdrawBtn({ Generate_Source, Status, Schduling_Model }) {
      // if (Generate_Source === 1) return false
      if (Schduling_Model === this.scheduleType.comp_part && !this.isCom) {
        return false
      }
      return Status === +this.statusMap.ordered
    },
    canWithdrawDraftBtn({ Generate_Source, Status, Schduling_Model, Receive_Count, Cancel_Count, Total_Change_Count }) {
      if (Generate_Source === 1) return false
      if (
        (Schduling_Model === this.scheduleType.comp_part && !this.isCom) ||
        Receive_Count > 0 || Cancel_Count > 0 || Total_Change_Count > 0) {
        return false
      }
      return Status === +this.statusMap.ordered
    },
    canDeleteBtn({ Status, Schduling_Model }) {
      if (Schduling_Model === this.scheduleType.comp_part && !this.isCom) {
        return false
      }
      return Status === +this.statusMap.unOrdered
    },
    handleAdd() {
      // this.dWidth = '40%'
      // this.currentComponent = 'AddSchedule'
      // this.title = '选择项目'
      // this.dialogVisible = true
      const info = getDraftQuery('PRO2ComScheduleDraftNew', 'add', this.isCom ? 'com' : 'part', {}, this.$route)
      this.$router.push({
        ...info
      })
    },
    handleRowImport(row) {
      if (this.isCom) {
        this.handleComImport(row)
      } else {
        this.dWidth = '40%'
        this.currentComponent = 'PartImport'
        this.title = '导入'
        this.dialogVisible = true
        this.$nextTick(_ => {
          this.$refs['content'].setRow(row)
        })
      }
    },
    handleComImportNew(row, type) {
      this.handleComImport(row, type)
    },
    handleComImport(row, type) {
      this.dWidth = '40%'
      this.currentComponent = 'ComImport'
      this.title = '导入'
      this.dialogVisible = true
      this.$nextTick(_ => {
        if (row) {
          this.$refs['content'].setRow(row)
        } else {
          this.$refs['content'].setRow(null, type)
        }
      })
    },
    handlePartImport() {
      this.dWidth = '40%'
      this.currentComponent = 'PartImport'
      this.title = '导入'
      this.dialogVisible = true
    },
    fetchData(page) {
      this.pgLoading = true
      page && (this.queryInfo.Page = page)
      let fun = null
      const {
        projectId,
        areaId,
        install,
        Status,
        Schduling_Code,
        Workshop_Id,
        Finish_Date_Begin,
        Finish_Date_End
      } = this.queryForm
      const obj = {
        ...this.queryInfo,
        Project_Id: projectId,
        Area_Id: areaId,
        InstallUnit_Id: install,
        Status: +this.activeName,
        Schduling_Code,
        Workshop_Id,
        Finish_Date_Begin,
        Finish_Date_End,
        Is_New_Schduling: true,
        Bom_Level: -1
      }
      fun = GetCompSchdulingPageList

      fun(obj).then(res => {
        if (res.IsSucceed) {
          this.tbData = res.Data.Data
          this.total = res.Data.TotalCount
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
          this.tbData = []
          this.total = 0
        }
      }).finally(_ => {
        this.isUpdate = false
        this.pgLoading = false
      })
    },
    handleDelete(row) {
      this.$confirm('是否删除该排产单?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        DelSchdulingPlanById({ schdulingPlanId: row.Schduling_Id }).then(res => {
          if (res.IsSucceed) {
            this.$message({
              message: '删除成功',
              type: 'success'
            })
            this.fetchData(1)
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handleSave(row) {
      this.$confirm('是否下达该任务?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.pgLoading = true
        SaveSchdulingTaskById({
          schdulingPlanId: row.Schduling_Id
        }).then(res => {
          if (res.IsSucceed) {
            this.fetchData(1)
            this.$message({
              message: '下达成功',
              type: 'success'
            })
            this.pgLoading = false
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
            this.pgLoading = false
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    handleCanCelDetail(row) {
      this.dWidth = '80%'
      this.currentComponent = 'WithdrawHistory'
      this.title = '撤回历史'
      this.dialogVisible = true
      this.$nextTick(_ => {
        this.$refs['content'].init(row)
      })
    },
    handleEdit(row) {
      const name = 'PRO2ComScheduleDraftNew'
      const info = getDraftQuery(name, 'edit', this.pageType, {
        pid: row.Schduling_Id,
        areaId: row.Area_Id,
        install: row.InstallUnit_Id,
        model: row.Schduling_Model
      }, this.$route)
      this.$router.push({
        ...info
      })
    },
    handleView(row) {
      const name = 'PRO2ComScheduleDetailNew'
      const info = getDraftQuery(name, 'view', this.pageType, {
        pid: row.Schduling_Id,
        areaId: row.Area_Id,
        install: row.InstallUnit_Id,
        type: row.Generate_Source === 1 ? '1' : undefined
      }, this.$route)
      this.$router.push({
        ...info
      })
    },
    handleWithdraw(row, isWithdrawDraft) {
      this.dWidth = '80%'
      this.currentComponent = 'Withdraw'
      this.title = '撤回'
      this.dialogVisible = true
      this.$nextTick(_ => {
        this.$refs['content'].init(row)
      })
    },
    handleWithdrawAll(row) {
      this.$confirm('是否撤销排产单回草稿?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.pgLoading = true
        WithdrawScheduling({
          schedulingId: row.Schduling_Id
        }).then(res => {
          if (res.IsSucceed) {
            this.$message({
              message: '撤回草稿成功',
              type: 'success'
            })
            this.fetchData(1)
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
            this.pgLoading = false
          }
        }).catch(e => {
          this.pgLoading = false
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleReset() {
      this.$refs['form'].resetFields()
      this.finishTime = ''
      this.search(1)
    },
    moment(v, format = 'YYYY-MM-DD') {
      if ((v ?? '') == '') {
        return ''
      } else {
        return moment(v).format(format)
      }
    },
    getWorkshop() {
      GetWorkshopPageList({ Page: 1, PageSize: -1 }).then(res => {
        if (res.IsSucceed) {
          if (!res?.Data?.Data) {
            this.workShopOption = []
          }
          this.workShopOption = res.Data.Data.map(item => {
            return {
              Id: item.Id,
              Display_Name: item.Display_Name
            }
          })
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    getRoles(code) {
      return this.roleList.includes(code)
    },
    async getRoleAuthorization() {
      const res = await RoleAuthorization({
        roleType: 3,
        menuType: 1,
        menuId: this.$route.meta.Id
      })
      if (res.IsSucceed) {
        this.roleList = res.Data.map((v) => v.Code)
      } else {
        this.$message({
          type: 'warning',
          message: res.Message
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  padding: 16px;
  display: flex;
  flex-direction: column;

  .cs-tabs{
    padding: 0 16px 0;
    background: #ffffff;
  }
  .search-wrapper {
    margin-top: 16px;
    padding: 16px 16px 0;
    background: #ffffff;
    border-radius: 4px 4px 4px 4px;
  }
}

.main-wrapper {
  background: #ffffff;
  //margin-top: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  border-radius: 4px 4px 4px 4px;
  padding: 0px 16px 0;
  overflow: hidden;

  .btn-wrapper {
    padding-bottom: 16px;
  }
  .tb-wrapper{
    flex: 1;
    height: 0;
  }
  .pagination-container {
    text-align: right;
    padding: 16px;
    margin: 0;
  }
}

.plm-custom-dialog {
  ::v-deep {
    .el-dialog .el-dialog__body {
      max-height: 70vh;
      overflow: auto;
    }
  }
}
</style>

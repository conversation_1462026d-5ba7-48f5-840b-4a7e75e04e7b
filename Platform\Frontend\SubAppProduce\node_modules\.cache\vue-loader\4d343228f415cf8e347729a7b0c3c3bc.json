{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\index.vue?vue&type=template&id=3a7a9256&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\index.vue", "mtime": 1757909680922}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}
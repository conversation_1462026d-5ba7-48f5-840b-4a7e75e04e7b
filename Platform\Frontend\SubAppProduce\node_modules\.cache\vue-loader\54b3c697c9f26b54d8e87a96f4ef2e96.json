{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\RecognitionConfig.vue?vue&type=template&id=4d274f86&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\RecognitionConfig.vue", "mtime": 1758157160305}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImZvcm0tcmVjb2duaXRpb24td3JhcHBlciI+CiAgPGRpdiBjbGFzcz0iZm9ybS1yZWNvZ25pdGlvbi10YWJzIj4KICAgIDxlbC10YWJzIHYtbW9kZWw9ImJvbUFjdGl2ZU5hbWUiPgogICAgICA8ZWwtdGFiLXBhbmUgdi1mb3I9IihpdGVtLCBpbmRleCkgaW4gYm9tTGlzdCIgOmtleT0iaW5kZXgiIDpsYWJlbD0iaXRlbS5EaXNwbGF5X05hbWUiIDpuYW1lPSJpdGVtLkNvZGUiIC8+CiAgICA8L2VsLXRhYnM+CiAgPC9kaXY+CiAgPGNvbXAtcmVjb2duaXRpb24tY29uZmlnIHYtaWY9ImJvbUFjdGl2ZU5hbWUgPT09ICctMSciIDpib20tbGlzdD0iYm9tTGlzdCIgOmxldmVsPSJOdW1iZXIoYm9tQWN0aXZlTmFtZSkiIEBjbG9zZT0iaGFuZGxlQ2xvc2UiIC8+CiAgPHBhcnQtcmVjb2duaXRpb24tY29uZmlnIHYtaWY9ImJvbUFjdGl2ZU5hbWUgPT09ICcwJyIgOmJvbS1saXN0PSJib21MaXN0IiA6bGV2ZWw9Ik51bWJlcihib21BY3RpdmVOYW1lKSIgQGNsb3NlPSJoYW5kbGVDbG9zZSIgLz4KICA8dW5pdC1wYXJ0LXJlY29nbml0aW9uLWNvbmZpZyB2LWlmPSJib21BY3RpdmVOYW1lICE9PSAnLTEnICYmIGJvbUFjdGl2ZU5hbWUgIT09ICcwJyIgOmJvbS1saXN0PSJib21MaXN0IiA6bGV2ZWw9Ik51bWJlcihib21BY3RpdmVOYW1lKSIgQGNsb3NlPSJoYW5kbGVDbG9zZSIgLz4KPC9kaXY+Cg=="}, null]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\components\\ProjectData.vue?vue&type=style&index=0&id=649f8757&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\components\\ProjectData.vue", "mtime": 1757468113454}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKQGltcG9ydCAifkAvc3R5bGVzL21peGluLnNjc3MiOwpAaW1wb3J0ICJ+QC9zdHlsZXMvdGFicy5zY3NzIjsKLmNzLWxlZnQgewogIHBvc2l0aW9uOiByZWxhdGl2ZTsKICBtYXJnaW4tcmlnaHQ6IDIwcHg7Cgp9Ci5jcy1sZWZ0LWNvbnRyYWN0IHsKICBwYWRkaW5nLWxlZnQ6IDA7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIHdpZHRoOiAyMHB4OwogIG1hcmdpbi1yaWdodDogMjZweDsKfQouaW5uZXItd3JhcHBlciB7CiAgZmxleDogMTsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgcGFkZGluZzogMTZweCAxMHB4IDE2cHggMTZweDsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKCiAgLnRyZWUtc2VhcmNoIHsKICAgIGRpc3BsYXk6IGZsZXg7CgogICAgLnNlYXJjaC1zZWxlY3QgewogICAgICBtYXJnaW4tcmlnaHQ6IDhweDsKICAgIH0KICB9CgogIC50cmVlLXggewogICAgb3ZlcmZsb3c6IGF1dG87CiAgICBtYXJnaW4tdG9wOiAxNnB4OwogICAgZmxleDogMTsKICAgIC5wcm9qZWN0LWxpc3QgewogICAgICBoZWlnaHQ6IDMycHg7CiAgICAgIHBhZGRpbmctbGVmdDogMTZweDsKICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAucHJvamVjdC1pbm5lciB7CiAgICAgICAgaGVpZ2h0OiAxMDAlOwogICAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAganVzdGlmeS1jb250ZW50OiBzdGFydDsKICAgICAgICB3aGl0ZS1zcGFjZTogbm93cmFwOyAvKiDnpoHmraLmjaLooYwgKi8KICAgICAgICBvdmVyZmxvdzogaGlkZGVuOyAvKiDpmpDol4/muqLlh7rlhoXlrrkgKi8KICAgICAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsgLyog5rqi5Ye65pi+56S655yB55Wl5Y+3ICovCiAgICAgICAgLmNvZGUgewogICAgICAgICAgY29sb3I6ICM1YWM4ZmE7CiAgICAgICAgICBtYXJnaW4tbGVmdDogNXB4OwogICAgICAgICAgbWFyZ2luLXJpZ2h0OiA1cHg7CiAgICAgICAgICBmbGV4LXNocmluazogMDsgLyog56aB5q2i5Y6L57ypICovCiAgICAgICAgfQogICAgICAgIC5uYW1lIHsKICAgICAgICAgIGNvbG9yOiByZ2JhKDM0LCA0MCwgNTIsIC42NSk7CiAgICAgICAgICBmbGV4LXNocmluazogMTsgLyog5YWB6K646YCC5b2T5Y6L57ypICovCiAgICAgICAgICBtaW4td2lkdGg6IDA7IC8qIOWFgeiuuOaWh+acrOaIquaWrSAqLwogICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsKICAgICAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOwogICAgICAgIH0KICAgICAgfQogICAgfQogICAgLnByb2plY3QtbGlzdDpob3ZlciB7CiAgICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoNDEsIDE0MSwgMjU1LCAuMDQpOwogICAgfQogICAgLnByb2plY3QtbGlzdC5hY3RpdmUgewogICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZWVmNmZmOwogICAgfQogIH0KICAuY3Mtc2Nyb2xsIHsKICAgIG92ZXJmbG93LXk6IGF1dG87CiAgICBAaW5jbHVkZSBzY3JvbGxCYXI7CiAgfQp9Ci5jcy1kaXZpZGVyIHsKICBtYXJnaW46IDE2cHggMCAwIDA7Cn0K"}, {"version": 3, "sources": ["ProjectData.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqHA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ProjectData.vue", "sourceRoot": "src/views/PRO/project-config/components", "sourcesContent": ["<template>\r\n  <ExpandableSection v-model=\"showExpand\" :width=\"300\" class=\"cs-left fff\">\r\n    <div class=\"inner-wrapper\">\r\n      <div class=\"tree-search\">\r\n        <el-input\r\n          v-model.trim=\"projectName\"\r\n          placeholder=\"关键词搜索\"\r\n          size=\"small\"\r\n          clearable\r\n          suffix-icon=\"el-icon-search\"\r\n          @blur=\"fetchTreeDataLocal\"\r\n          @clear=\"fetchTreeDataLocal\"\r\n          @keydown.enter.native=\"fetchTreeDataLocal\"\r\n        />\r\n      </div>\r\n      <el-divider class=\"cs-divider\" />\r\n      <div class=\"tree-x cs-scroll\">\r\n        <div v-for=\"item in treeData\" :key=\"item.Sys_Project_Id\" class=\"project-list\" :class=\"{ active: item.Sys_Project_Id === Active_Sys_Project_Id }\" @click=\"handleNodeClick(item)\">\r\n          <el-tooltip class=\"item\" effect=\"dark\" :content=\"item.Short_Name\" :open-delay=\"200\" placement=\"top\">\r\n            <div class=\"project-inner\">\r\n              <div>\r\n                <svg-icon\r\n                  icon-class=\"icon-folder\"\r\n                  class-name=\"class-icon\"\r\n                />\r\n              </div>\r\n              <span class=\"code\">({{ item.Code }})</span>\r\n              <span class=\"name\">{{ item.Short_Name }}</span>\r\n            </div>\r\n          </el-tooltip>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </ExpandableSection>\r\n</template>\r\n\r\n<script>\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport {\r\n  GetProjectListForPlanTrace\r\n} from '@/api/PRO/project'\r\nexport default {\r\n  components: {\r\n    ExpandableSection\r\n  },\r\n  data() {\r\n    return {\r\n      Active_Sys_Project_Id: '',\r\n      showExpand: true,\r\n      treeLoading: true,\r\n      projectName: '',\r\n      treeData: [],\r\n      originalTreeData: [] // 保存原始数据\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchTreeData()\r\n  },\r\n  methods: {\r\n    // 项目数据集\r\n    fetchTreeData() {\r\n      GetProjectListForPlanTrace({ }).then((res) => {\r\n        if (!res.IsSucceed) {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.treeLoading = false\r\n          this.treeData = []\r\n          return\r\n        }\r\n        if (res.Data.length === 0) {\r\n          this.treeLoading = false\r\n          return\r\n        }\r\n        const resData = res.Data\r\n        this.originalTreeData = [...resData] // 保存原始数据\r\n        this.treeData = [...resData]\r\n        this.treeLoading = false\r\n        if (this.treeData.length > 0) {\r\n          this.handleNodeClick(resData[0])\r\n        }\r\n      })\r\n    },\r\n    // 选中左侧项目节点\r\n    handleNodeClick(data) {\r\n      this.Active_Sys_Project_Id = data.Sys_Project_Id\r\n      this.$emit('setProjectData', data)\r\n    },\r\n    fetchTreeDataLocal() {\r\n      // 如果搜索关键词为空，恢复原始数据\r\n      if (!this.projectName || this.projectName.trim() === '') {\r\n        this.treeData = [...this.originalTreeData]\r\n        // 恢复原始数据后，如果有数据则选中第一个\r\n        if (this.treeData.length > 0) {\r\n          this.handleNodeClick(this.treeData[0])\r\n        }\r\n        return\r\n      }\r\n\r\n      // 从原始数据中过滤，支持大小写不敏感搜索\r\n      const searchTerm = this.projectName.trim().toLowerCase()\r\n      this.treeData = this.originalTreeData.filter((item) => {\r\n        return item.Short_Name &&\r\n               item.Short_Name.toLowerCase().includes(searchTerm)\r\n      })\r\n\r\n      // 搜索后如果有数据，自动选中第一个项目\r\n      // if (this.treeData.length > 0) {\r\n      //   this.handleNodeClick(this.treeData[0])\r\n      // }\r\n    }\r\n  }\r\n\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n  @import \"~@/styles/mixin.scss\";\r\n  @import \"~@/styles/tabs.scss\";\r\n  .cs-left {\r\n    position: relative;\r\n    margin-right: 20px;\r\n\r\n  }\r\n  .cs-left-contract {\r\n    padding-left: 0;\r\n    position: relative;\r\n    width: 20px;\r\n    margin-right: 26px;\r\n  }\r\n  .inner-wrapper {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    padding: 16px 10px 16px 16px;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n\r\n    .tree-search {\r\n      display: flex;\r\n\r\n      .search-select {\r\n        margin-right: 8px;\r\n      }\r\n    }\r\n\r\n    .tree-x {\r\n      overflow: auto;\r\n      margin-top: 16px;\r\n      flex: 1;\r\n      .project-list {\r\n        height: 32px;\r\n        padding-left: 16px;\r\n        font-size: 14px;\r\n        .project-inner {\r\n          height: 100%;\r\n          cursor: pointer;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: start;\r\n          white-space: nowrap; /* 禁止换行 */\r\n          overflow: hidden; /* 隐藏溢出内容 */\r\n          text-overflow: ellipsis; /* 溢出显示省略号 */\r\n          .code {\r\n            color: #5ac8fa;\r\n            margin-left: 5px;\r\n            margin-right: 5px;\r\n            flex-shrink: 0; /* 禁止压缩 */\r\n          }\r\n          .name {\r\n            color: rgba(34, 40, 52, .65);\r\n            flex-shrink: 1; /* 允许适当压缩 */\r\n            min-width: 0; /* 允许文本截断 */\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n          }\r\n        }\r\n      }\r\n      .project-list:hover {\r\n        background-color: rgba(41, 141, 255, .04);\r\n      }\r\n      .project-list.active {\r\n        background-color: #eef6ff;\r\n      }\r\n    }\r\n    .cs-scroll {\r\n      overflow-y: auto;\r\n      @include scrollBar;\r\n    }\r\n  }\r\n  .cs-divider {\r\n    margin: 16px 0 0 0;\r\n  }\r\n</style>\r\n"]}]}
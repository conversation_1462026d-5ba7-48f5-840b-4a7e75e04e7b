{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\section-list\\index.vue?vue&type=template&id=2b45efe5&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\section-list\\index.vue", "mtime": 1757468113549}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}
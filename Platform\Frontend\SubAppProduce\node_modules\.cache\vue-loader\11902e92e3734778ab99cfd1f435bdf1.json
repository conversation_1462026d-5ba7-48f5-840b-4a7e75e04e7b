{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\section-list\\index.vue?vue&type=template&id=2b45efe5&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\section-list\\index.vue", "mtime": 1757915717328}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}
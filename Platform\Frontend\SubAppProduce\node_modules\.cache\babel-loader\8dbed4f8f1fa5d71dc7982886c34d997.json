{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\ItemDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\ItemDialog.vue", "mtime": 1758095276370}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["AddCheckItem", "EntityCheckItem", "SaveCheckItem", "data", "Bom_Level", "check_object_id", "form", "rules", "Check_Content", "required", "message", "trigger", "Eligibility_Criteria", "title", "editInfo", "sysProjectId", "mounted", "methods", "init", "checkType", "Check_Object_Id", "Id", "Code", "console", "log", "getEntityCheckType", "addCheckType", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "_objectSpread", "then", "res", "IsSucceed", "$message", "type", "$emit", "dialogData", "Message", "stop", "_this2", "id", "Data", "editCheckType", "_this3", "handleSubmit", "_this4", "$refs", "validate", "valid"], "sources": ["src/views/PRO/project-config/project-quality/components/Dialog/ItemDialog.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"120px\">\n      <el-row>\n        <el-col :span=\"24\">\n          <el-form-item label=\"检查项内容\" prop=\"Check_Content\">\n            <el-input v-model=\"form.Check_Content\" :disabled=\"true\" />\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item label=\"合格标准\" prop=\"Eligibility_Criteria\">\n            <el-input v-model=\"form.Eligibility_Criteria\" maxlength=\"100\" />\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item style=\"text-align: right\">\n            <el-button @click=\"$emit('close')\">关 闭</el-button>\n            <el-button\n              type=\"primary\"\n              @click=\"handleSubmit('form')\"\n            >确 定</el-button>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n  </div>\n</template>\n\n<script>\nimport { AddCheckItem } from '@/api/PRO/factorycheck'\nimport { EntityCheckItem } from '@/api/PRO/factorycheck'\nimport { SaveCheckItem } from '@/api/PRO/factorycheck'\nexport default {\n  data() {\n    return {\n      Bom_Level: '',\n      check_object_id: '',\n      form: {},\n      rules: {\n        Check_Content: [{ required: true, message: '请填写完整表单', trigger: 'blur' }],\n        Eligibility_Criteria: [{ required: true, message: '请填写完整表单', trigger: 'blur' }]\n      },\n      title: '',\n      editInfo: {},\n      sysProjectId: ''\n    }\n  },\n  mounted() {},\n  methods: {\n    init(title, checkType, data, sysProjectId) {\n      this.title = title\n      this.Check_Object_Id = checkType.Id\n      this.Bom_Level = checkType.Code\n      this.sysProjectId = sysProjectId\n      if (title === '编辑') {\n        this.editInfo = data\n        console.log(this.editInfo)\n        this.getEntityCheckType(data)\n      }\n    },\n    async addCheckType() {\n      await AddCheckItem({\n        ...this.form,\n        Check_Object_Id: this.Check_Object_Id,\n        Bom_Level: this.Bom_Level\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            type: 'success',\n            message: '保存成功'\n          })\n          this.$emit('refresh')\n          this.$emit('close')\n          this.dialogData = {}\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    getEntityCheckType(data) {\n      EntityCheckItem({ id: data.Id, sysProjectId: this.sysProjectId }).then((res) => {\n        if (res.IsSucceed) {\n          this.form = res.Data[0]\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    editCheckType() {\n      SaveCheckItem({\n        sysProjectId: this.sysProjectId,\n        Id: this.editInfo.Id,\n        ...this.form,\n        Check_Object_Id: this.Check_Object_Id,\n        Bom_Level: this.Bom_Level\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            type: 'success',\n            message: '编辑成功'\n          })\n          this.$emit('refresh')\n          this.$emit('close')\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    handleSubmit(form) {\n      this.$refs[form].validate((valid) => {\n        if (valid) {\n          this.title === '新增' ? this.addCheckType() : this.editCheckType()\n        } else {\n          return false\n        }\n      })\n    }\n  }\n}\n</script>\n\n  <style scoped></style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA,SAAAA,YAAA;AACA,SAAAC,eAAA;AACA,SAAAC,aAAA;AACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,eAAA;MACAC,IAAA;MACAC,KAAA;QACAC,aAAA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAC,oBAAA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAE,KAAA;MACAC,QAAA;MACAC,YAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAAL,KAAA,EAAAM,SAAA,EAAAhB,IAAA,EAAAY,YAAA;MACA,KAAAF,KAAA,GAAAA,KAAA;MACA,KAAAO,eAAA,GAAAD,SAAA,CAAAE,EAAA;MACA,KAAAjB,SAAA,GAAAe,SAAA,CAAAG,IAAA;MACA,KAAAP,YAAA,GAAAA,YAAA;MACA,IAAAF,KAAA;QACA,KAAAC,QAAA,GAAAX,IAAA;QACAoB,OAAA,CAAAC,GAAA,MAAAV,QAAA;QACA,KAAAW,kBAAA,CAAAtB,IAAA;MACA;IACA;IACAuB,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACApC,YAAA,CAAAqC,aAAA,CAAAA,aAAA,KACAV,KAAA,CAAArB,IAAA;gBACAc,eAAA,EAAAO,KAAA,CAAAP,eAAA;gBACAhB,SAAA,EAAAuB,KAAA,CAAAvB;cAAA,EACA,EAAAkC,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAb,KAAA,CAAAc,QAAA;oBACAC,IAAA;oBACAhC,OAAA;kBACA;kBACAiB,KAAA,CAAAgB,KAAA;kBACAhB,KAAA,CAAAgB,KAAA;kBACAhB,KAAA,CAAAiB,UAAA;gBACA;kBACAjB,KAAA,CAAAc,QAAA;oBACAC,IAAA;oBACAhC,OAAA,EAAA6B,GAAA,CAAAM;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAX,QAAA,CAAAY,IAAA;UAAA;QAAA,GAAAf,OAAA;MAAA;IACA;IACAN,kBAAA,WAAAA,mBAAAtB,IAAA;MAAA,IAAA4C,MAAA;MACA9C,eAAA;QAAA+C,EAAA,EAAA7C,IAAA,CAAAkB,EAAA;QAAAN,YAAA,OAAAA;MAAA,GAAAuB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAO,MAAA,CAAAzC,IAAA,GAAAiC,GAAA,CAAAU,IAAA;QACA;UACAF,MAAA,CAAAN,QAAA;YACAC,IAAA;YACAhC,OAAA,EAAA6B,GAAA,CAAAM;UACA;QACA;MACA;IACA;IACAK,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACAjD,aAAA,CAAAmC,aAAA,CAAAA,aAAA;QACAtB,YAAA,OAAAA,YAAA;QACAM,EAAA,OAAAP,QAAA,CAAAO;MAAA,GACA,KAAAf,IAAA;QACAc,eAAA,OAAAA,eAAA;QACAhB,SAAA,OAAAA;MAAA,EACA,EAAAkC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAW,MAAA,CAAAV,QAAA;YACAC,IAAA;YACAhC,OAAA;UACA;UACAyC,MAAA,CAAAR,KAAA;UACAQ,MAAA,CAAAR,KAAA;QACA;UACAQ,MAAA,CAAAV,QAAA;YACAC,IAAA;YACAhC,OAAA,EAAA6B,GAAA,CAAAM;UACA;QACA;MACA;IACA;IACAO,YAAA,WAAAA,aAAA9C,IAAA;MAAA,IAAA+C,MAAA;MACA,KAAAC,KAAA,CAAAhD,IAAA,EAAAiD,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAAxC,KAAA,YAAAwC,MAAA,CAAA3B,YAAA,KAAA2B,MAAA,CAAAH,aAAA;QACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
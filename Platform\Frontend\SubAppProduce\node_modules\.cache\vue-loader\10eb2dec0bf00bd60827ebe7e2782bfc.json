{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\draft.vue?vue&type=template&id=1b701904&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\draft.vue", "mtime": 1758242836212}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImFwcC1jb250YWluZXIgYWJzMTAwIGZsZXgtcm93Ij4KICA8ZGl2IGNsYXNzPSJjcy1yaWdodCI+CiAgICA8ZWwtY2FyZCB2LWxvYWRpbmc9InBnTG9hZGluZyIgY2xhc3M9ImJveC1jYXJkIGgxMDAiIGVsZW1lbnQtbG9hZGluZy10ZXh0PSLmraPlnKjlpITnkIYuLi4iPgogICAgICA8aDQgY2xhc3M9InRvcFRpdGxlIj48c3BhbiAvPuWfuuacrOS/oeaBrzwvaDQ+CiAgICAgIDxlbC1mb3JtCiAgICAgICAgcmVmPSJmb3JtSW5saW5lIgogICAgICAgIDppbmxpbmU9InRydWUiCiAgICAgICAgOm1vZGVsPSJmb3JtSW5saW5lIgogICAgICAgIGNsYXNzPSJkZW1vLWZvcm0taW5saW5lIgogICAgICA+CiAgICAgICAgPGVsLWZvcm0taXRlbSB2LWlmPSIhaXNBZGQmJiFpc05lc3QiIGxhYmVsPSLmjpLkuqfljZXlj7ciIHByb3A9IlNjaGR1bGluZ19Db2RlIj4KICAgICAgICAgIDxzcGFuIHYtaWY9ImlzVmlldyI+e3sgZm9ybUlubGluZS5TdGF0dXMgPT09IDAgPyAnJyA6IGZvcm1JbmxpbmUuU2NoZHVsaW5nX0NvZGUgfX08L3NwYW4+CiAgICAgICAgICA8ZWwtaW5wdXQgdi1lbHNlIHYtbW9kZWw9ImZvcm1JbmxpbmUuU2NoZHVsaW5nX0NvZGUiIGRpc2FibGVkIC8+CiAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i6K6h5YiS5ZGYIiBwcm9wPSJDcmVhdGVfVXNlck5hbWUiPgogICAgICAgICAgPHNwYW4gdi1pZj0iaXNWaWV3Ij57eyBmb3JtSW5saW5lLkNyZWF0ZV9Vc2VyTmFtZSB9fTwvc3Bhbj4KICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICB2LWVsc2UKICAgICAgICAgICAgdi1tb2RlbD0iZm9ybUlubGluZS5DcmVhdGVfVXNlck5hbWUiCiAgICAgICAgICAgIGRpc2FibGVkCiAgICAgICAgICAvPgogICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgIDxlbC1mb3JtLWl0ZW0KICAgICAgICAgIGxhYmVsPSLopoHmsYLlrozmiJDml7bpl7QiCiAgICAgICAgICBwcm9wPSJGaW5pc2hfRGF0ZSIKICAgICAgICAgIDpydWxlcz0ieyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqScsIHRyaWdnZXI6ICdjaGFuZ2UnIH0iCiAgICAgICAgPgogICAgICAgICAgPHNwYW4gdi1pZj0iaXNWaWV3Ij57eyBmb3JtSW5saW5lLkZpbmlzaF9EYXRlIHwgdGltZUZvcm1hdCB9fTwvc3Bhbj4KICAgICAgICAgIDxlbC1kYXRlLXBpY2tlcgogICAgICAgICAgICB2LWVsc2UKICAgICAgICAgICAgdi1tb2RlbD0iZm9ybUlubGluZS5GaW5pc2hfRGF0ZSIKICAgICAgICAgICAgOnBpY2tlci1vcHRpb25zPSJwaWNrZXJPcHRpb25zIgogICAgICAgICAgICA6ZGlzYWJsZWQ9ImlzVmlldyIKICAgICAgICAgICAgdmFsdWUtZm9ybWF0PSJ5eXl5LU1NLWRkIgogICAgICAgICAgICB0eXBlPSJkYXRlIgogICAgICAgICAgICBwbGFjZWhvbGRlcj0i6YCJ5oup5pel5pyfIgogICAgICAgICAgLz4KICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICA8IS0tICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gdi1pZj0iIWlzTmVzdCIgbGFiZWw9IuaJueasoSIgcHJvcD0iQ3JlYXRlX1VzZXJOYW1lIj4KICAgICAgICAgIDxzcGFuIHYtaWY9ImlzVmlldyI+e3sgaW5zdGFsbE5hbWUgfX08L3NwYW4+CiAgICAgICAgICA8ZWwtc2VsZWN0CiAgICAgICAgICAgIHYtZWxzZQogICAgICAgICAgICB2LW1vZGVsPSJmb3JtSW5saW5lLkluc3RhbGxVbml0X0lkIgogICAgICAgICAgICA6ZGlzYWJsZWQ9IiFpc0FkZCIKICAgICAgICAgICAgZmlsdGVyYWJsZQogICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36YCJ5oupIgogICAgICAgICAgICBAY2hhbmdlPSJpbnN0YWxsQ2hhbmdlIgogICAgICAgICAgPgogICAgICAgICAgICA8ZWwtb3B0aW9uCiAgICAgICAgICAgICAgdi1mb3I9Iml0ZW0gaW4gaW5zdGFsbFVuaXRJZExpc3QiCiAgICAgICAgICAgICAgOmtleT0iaXRlbS5JZCIKICAgICAgICAgICAgICA6bGFiZWw9Iml0ZW0uTmFtZSIKICAgICAgICAgICAgICA6dmFsdWU9Iml0ZW0uSWQiCiAgICAgICAgICAgIC8+CiAgICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgICA8L2VsLWZvcm0taXRlbT4tLT4KICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLlpIfms6giIHByb3A9IlJlbWFyayI+CiAgICAgICAgICA8c3BhbiB2LWlmPSJpc1ZpZXciPnt7IGZvcm1JbmxpbmUuUmVtYXJrIH19PC9zcGFuPgogICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgIHYtZWxzZQogICAgICAgICAgICB2LW1vZGVsPSJmb3JtSW5saW5lLlJlbWFyayIKICAgICAgICAgICAgOmRpc2FibGVkPSJpc1ZpZXciCiAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMzIwcHgiCiAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaUiCiAgICAgICAgICAvPgogICAgICAgIDwvZWwtZm9ybS1pdGVtPgoKICAgICAgPC9lbC1mb3JtPgogICAgICA8ZWwtZGl2aWRlciBjbGFzcz0iZWxEaXZkZXIiIC8+CiAgICAgIDxkaXYgdi1pZj0iIWlzVmlldyI+CiAgICAgICAgPGRpdiByZWY9InNlYXJjaERvbSIgY2xhc3M9InNlYXJjaC1jb250YWluZXIiPgoKICAgICAgICAgIDxlbC1mb3JtIHJlZj0ic2VhcmNoRm9ybSIgOm1vZGVsPSJpbm5lckZvcm0iPgogICAgICAgICAgICA8ZWwtcm93PgogICAgICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjYiPgogICAgICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbC13aWR0aD0iNzBweCIgbGFiZWw9IumhueebruWQjeensCIgcHJvcD0icHJvamVjdE5hbWUiPgogICAgICAgICAgICAgICAgICA8ZWwtc2VsZWN0CiAgICAgICAgICAgICAgICAgICAgdi1tb2RlbD0iaW5uZXJGb3JtLnByb2plY3ROYW1lIgogICAgICAgICAgICAgICAgICAgIGZpbHRlcmFibGUKICAgICAgICAgICAgICAgICAgICBjbGVhcmFibGUKICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36YCJ5oupIgogICAgICAgICAgICAgICAgICAgIGNsYXNzPSJ3MTAwIgogICAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICAgICAgPGVsLW9wdGlvbgogICAgICAgICAgICAgICAgICAgICAgdi1mb3I9IihpdGVtLGlkeCkgaW4gcHJvamVjdExpc3QiCiAgICAgICAgICAgICAgICAgICAgICA6a2V5PSJpZHgiCiAgICAgICAgICAgICAgICAgICAgICA6bGFiZWw9Iml0ZW0iCiAgICAgICAgICAgICAgICAgICAgICA6dmFsdWU9Iml0ZW0iCiAgICAgICAgICAgICAgICAgICAgLz4KICAgICAgICAgICAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgICAgICA8L2VsLWNvbD4KICAgICAgICAgICAgICA8ZWwtY29sIDpzcGFuPSI2Ij4KICAgICAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWwtd2lkdGg9IjcwcHgiIGxhYmVsPSLljLrln58iIHByb3A9ImFyZWFOYW1lIj4KICAgICAgICAgICAgICAgICAgPGVsLXNlbGVjdAogICAgICAgICAgICAgICAgICAgIHYtbW9kZWw9ImlubmVyRm9ybS5hcmVhTmFtZSIKICAgICAgICAgICAgICAgICAgICBmaWx0ZXJhYmxlCiAgICAgICAgICAgICAgICAgICAgY2xlYXJhYmxlCiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+mAieaLqSIKICAgICAgICAgICAgICAgICAgICBjbGFzcz0idzEwMCIKICAgICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgICAgIDxlbC1vcHRpb24KICAgICAgICAgICAgICAgICAgICAgIHYtZm9yPSIoaXRlbSxpZHgpIGluIGFyZWFMaXN0IgogICAgICAgICAgICAgICAgICAgICAgOmtleT0iaWR4IgogICAgICAgICAgICAgICAgICAgICAgOmxhYmVsPSJpdGVtIgogICAgICAgICAgICAgICAgICAgICAgOnZhbHVlPSJpdGVtIgogICAgICAgICAgICAgICAgICAgIC8+CiAgICAgICAgICAgICAgICAgIDwvZWwtc2VsZWN0PgogICAgICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iNiI+CiAgICAgICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsLXdpZHRoPSI3MHB4IiBsYWJlbD0i5om55qyhIiBwcm9wPSJpbnN0YWxsTmFtZSI+CiAgICAgICAgICAgICAgICAgIDxlbC1zZWxlY3QKICAgICAgICAgICAgICAgICAgICB2LW1vZGVsPSJpbm5lckZvcm0uaW5zdGFsbE5hbWUiCiAgICAgICAgICAgICAgICAgICAgZmlsdGVyYWJsZQogICAgICAgICAgICAgICAgICAgIGNsZWFyYWJsZQogICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fpgInmi6kiCiAgICAgICAgICAgICAgICAgICAgY2xhc3M9IncxMDAiCiAgICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgICAgICA8ZWwtb3B0aW9uCiAgICAgICAgICAgICAgICAgICAgICB2LWZvcj0iKGl0ZW0saWR4KSBpbiBpbnN0YWxsTGlzdCIKICAgICAgICAgICAgICAgICAgICAgIDprZXk9ImlkeCIKICAgICAgICAgICAgICAgICAgICAgIDpsYWJlbD0iaXRlbSIKICAgICAgICAgICAgICAgICAgICAgIDp2YWx1ZT0iaXRlbSIKICAgICAgICAgICAgICAgICAgICAvPgogICAgICAgICAgICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgICAgIDwvZWwtY29sPgogICAgICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjYiPgogICAgICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbC13aWR0aD0iOTBweCIgcHJvcD0ic2VhcmNoQ29udGVudCIgOmxhYmVsPSJgJHtwYXJ0TmFtZX3lkI3np7BgICI+CiAgICAgICAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgICAgICAgIHYtbW9kZWw9ImlubmVyRm9ybS5zZWFyY2hDb250ZW50IgogICAgICAgICAgICAgICAgICAgIGNsZWFyYWJsZQogICAgICAgICAgICAgICAgICAgIGNsYXNzPSJpbnB1dC13aXRoLXNlbGVjdCB3MTAwIgogICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaUo56m65qC85Yy65YiGL+WkmuS4quaQnOe0oikiCiAgICAgICAgICAgICAgICAgICAgc2l6ZT0ic21hbGwiCiAgICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgICAgICA8ZWwtc2VsZWN0CiAgICAgICAgICAgICAgICAgICAgICBzbG90PSJwcmVwZW5kIgogICAgICAgICAgICAgICAgICAgICAgdi1tb2RlbD0iY3VyU2VhcmNoIgogICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+mAieaLqSIKICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMTAwcHgiCiAgICAgICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i57K+5YeG5p+l6K+iIiA6dmFsdWU9IjEiIC8+CiAgICAgICAgICAgICAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLmqKHns4rmn6Xor6IiIDp2YWx1ZT0iMCIgLz4KICAgICAgICAgICAgICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgICAgICAgICAgICAgPC9lbC1pbnB1dD4KICAgICAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgICAgIDwvZWwtY29sPgoKICAgICAgICAgICAgPC9lbC1yb3c+CiAgICAgICAgICAgIDxlbC1yb3c+CiAgICAgICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iNiI+CiAgICAgICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsLXdpZHRoPSI3MHB4IiA6bGFiZWw9ImAke3BhcnROYW1lfeexu+Wei2AiIHByb3A9InNlYXJjaENvbVR5cGVTZWFyY2giPgogICAgICAgICAgICAgICAgICA8ZWwtdHJlZS1zZWxlY3QKICAgICAgICAgICAgICAgICAgICB2LWlmPSIkcm91dGUucXVlcnkuc3RhdHVzIT09J3ZpZXcnIgogICAgICAgICAgICAgICAgICAgIHJlZj0idHJlZVNlbGVjdENvbXBvbmVudFR5cGUiCiAgICAgICAgICAgICAgICAgICAgdi1tb2RlbD0iaW5uZXJGb3JtLnNlYXJjaENvbVR5cGVTZWFyY2giCiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+mAieaLqSIKICAgICAgICAgICAgICAgICAgICA6c2VsZWN0LXBhcmFtcz0idHJlZVNlbGVjdFBhcmFtcyIKICAgICAgICAgICAgICAgICAgICBjbGFzcz0iY3MtdHJlZS14IgogICAgICAgICAgICAgICAgICAgIDp0cmVlLXBhcmFtcz0idHJlZVBhcmFtc0NvbXBvbmVudFR5cGUiCiAgICAgICAgICAgICAgICAgICAgQHNlYXJjaEZ1bj0iY29tcG9uZW50VHlwZUZpbHRlciIKICAgICAgICAgICAgICAgICAgLz4KICAgICAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgICAgIDwvZWwtY29sPgogICAgICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjYiPgogICAgICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbC13aWR0aD0iNzBweCIgbGFiZWw9IuinhOagvCIgcHJvcD0ic2VhcmNoU3BlY1NlYXJjaCI+CiAgICAgICAgICAgICAgICAgIDxlbC1pbnB1dCB2LW1vZGVsPSJpbm5lckZvcm0uc2VhcmNoU3BlY1NlYXJjaCIgY2xhc3M9IncxMDAiIHBsYWNlaG9sZGVyPSLor7fovpPlhaUiIGNsZWFyYWJsZT0iIiAvPgogICAgICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iNiI+CiAgICAgICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIHYtaWY9ImlzQ29tIiBsYWJlbC13aWR0aD0iODBweCIgbGFiZWw9IuaYr+WQpuebtOWPkeS7tiIgcHJvcD0ic2VhcmNoRGlyZWN0Ij4KICAgICAgICAgICAgICAgICAgPGVsLXNlbGVjdCB2LW1vZGVsPSJpbm5lckZvcm0uc2VhcmNoRGlyZWN0IiBjbGFzcz0idzEwMCIgcGxhY2Vob2xkZXI9Iuivt+mAieaLqSIgY2xlYXJhYmxlPgogICAgICAgICAgICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuaYryIgOnZhbHVlPSJ0cnVlIiAvPgogICAgICAgICAgICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuWQpiIgOnZhbHVlPSJmYWxzZSIgLz4KICAgICAgICAgICAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgICAgICA8L2VsLWNvbD4KICAgICAgICAgICAgICA8ZWwtY29sIDpzcGFuPSI2Ij4KICAgICAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWwtd2lkdGg9IjIwcHgiPgogICAgICAgICAgICAgICAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIEBjbGljaz0iaW5uZXJGaWx0ZXIiPuaQnOe0ojwvZWwtYnV0dG9uPgogICAgICAgICAgICAgICAgICA8ZWwtYnV0dG9uIEBjbGljaz0icmVzZXRJbm5lckZvcm0iPumHjee9rjwvZWwtYnV0dG9uPgogICAgICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgICAgIDwvZWwtcm93PgogICAgICAgICAgPC9lbC1mb3JtPgoKICAgICAgICA8L2Rpdj4KICAgICAgPC9kaXY+CgogICAgICA8dnhlLXRvb2xiYXIKICAgICAgICByZWY9InhUb29sYmFyMSIKICAgICAgPgogICAgICAgIDx0ZW1wbGF0ZSAjYnV0dG9ucz4KICAgICAgICAgIDxkaXYgdi1pZj0iIWlzVmlldyIgY2xhc3M9ImJ0bi14Ij4KICAgICAgICAgICAgPGVsLWJ1dHRvbiB2LWlmPSIhaXNOZXN0IiB0eXBlPSJwcmltYXJ5IiBAY2xpY2s9ImhhbmRsZUFkZERpYWxvZygpIj7mt7vliqA8L2VsLWJ1dHRvbj4KCiAgICAgICAgICAgIDxlbC1idXR0b24KICAgICAgICAgICAgICB2LWlmPSJ3b3Jrc2hvcEVuYWJsZWQiCiAgICAgICAgICAgICAgOmRpc2FibGVkPSIhbXVsdGlwbGVTZWxlY3Rpb24ubGVuZ3RoIgogICAgICAgICAgICAgIEBjbGljaz0iaGFuZGxlQmF0Y2hXb3Jrc2hvcCgxKSIKICAgICAgICAgICAgPuWIhumFjei9pumXtAogICAgICAgICAgICA8L2VsLWJ1dHRvbj4KCiAgICAgICAgICAgIDxlbC1kcm9wZG93biB2LWlmPSJoYXNDcmFmdCIgc3R5bGU9Im1hcmdpbjowIDEwcHgiIEBjb21tYW5kPSJoYW5kbGVTZWxlY3RNZW51Ij4KICAgICAgICAgICAgICA8ZWwtYnV0dG9uIDpkaXNhYmxlZD0iIW11bHRpcGxlU2VsZWN0aW9uLmxlbmd0aCIgdHlwZT0icHJpbWFyeSIgcGxhaW4+CiAgICAgICAgICAgICAgICDliIbphY3lt6Xluo88aSBjbGFzcz0iZWwtaWNvbi1hcnJvdy1kb3duIGVsLWljb24tLXJpZ2h0IiAvPgogICAgICAgICAgICAgIDwvZWwtYnV0dG9uPgogICAgICAgICAgICAgIDxlbC1kcm9wZG93bi1tZW51IHNsb3Q9ImRyb3Bkb3duIj4KICAgICAgICAgICAgICAgIDxlbC1kcm9wZG93bi1pdGVtCiAgICAgICAgICAgICAgICAgIGNvbW1hbmQ9InByb2Nlc3MiCiAgICAgICAgICAgICAgICA+5om56YeP5YiG6YWN5bel5bqPCiAgICAgICAgICAgICAgICA8L2VsLWRyb3Bkb3duLWl0ZW0+CiAgICAgICAgICAgICAgICA8ZWwtZHJvcGRvd24taXRlbQogICAgICAgICAgICAgICAgICBjb21tYW5kPSJjcmFmdCIKICAgICAgICAgICAgICAgID7lt6Xoibrku6PnoIHliIbphY0KICAgICAgICAgICAgICAgIDwvZWwtZHJvcGRvd24taXRlbT4KICAgICAgICAgICAgICA8L2VsLWRyb3Bkb3duLW1lbnU+CiAgICAgICAgICAgIDwvZWwtZHJvcGRvd24+CiAgICAgICAgICAgIDxlbC1idXR0b24KICAgICAgICAgICAgICB2LWVsc2UKICAgICAgICAgICAgICA6ZGlzYWJsZWQ9IiFtdWx0aXBsZVNlbGVjdGlvbi5sZW5ndGgiCiAgICAgICAgICAgICAgQGNsaWNrPSJoYW5kbGVTZWxlY3RNZW51KCdwcm9jZXNzJykiCiAgICAgICAgICAgID7liIbphY3lt6Xluo8KICAgICAgICAgICAgPC9lbC1idXR0b24+CiAgICAgICAgICAgIDxlbC1idXR0b24KICAgICAgICAgICAgICB2LWlmPSIhaXNDb20gJiYgIWlzT3duZXJOdWxsIgogICAgICAgICAgICAgIDpkaXNhYmxlZD0iIW11bHRpcGxlU2VsZWN0aW9uLmxlbmd0aCIKICAgICAgICAgICAgICBAY2xpY2s9ImhhbmRsZUJhdGNoT3duZXIoMSkiCiAgICAgICAgICAgID7mibnph4/liIbphY3poobnlKjlt6Xluo8KICAgICAgICAgICAgPC9lbC1idXR0b24+CiAgICAgICAgICAgIDxlbC1idXR0b24KICAgICAgICAgICAgICBwbGFpbgogICAgICAgICAgICAgIDpkaXNhYmxlZD0iIXRiRGF0YS5sZW5ndGgiCiAgICAgICAgICAgICAgOmxvYWRpbmc9ImZhbHNlIgogICAgICAgICAgICAgIEBjbGljaz0iaGFuZGxlUmV2ZXJzZSIKICAgICAgICAgICAgPuWPjemAiQogICAgICAgICAgICA8L2VsLWJ1dHRvbj4KICAgICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICAgIHR5cGU9ImRhbmdlciIKICAgICAgICAgICAgICBwbGFpbgogICAgICAgICAgICAgIDpsb2FkaW5nPSJkZWxldGVMb2FkaW5nIgogICAgICAgICAgICAgIDpkaXNhYmxlZD0iIW11bHRpcGxlU2VsZWN0aW9uLmxlbmd0aCB8fCBtdWx0aXBsZVNlbGVjdGlvbi5zb21lKGl0ZW09Pml0ZW0uc3RvcEZsYWcpIgogICAgICAgICAgICAgIEBjbGljaz0iaGFuZGxlRGVsZXRlIgogICAgICAgICAgICA+5Yig6ZmkCiAgICAgICAgICAgIDwvZWwtYnV0dG9uPgogICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8ZGl2IHYtZWxzZT4KICAgICAgICAgICAgPGVsLWJ1dHRvbiBzdHlsZT0ibWFyZ2luLWJvdHRvbTogOHB4OyIgOmRpc2FibGVkPSIhdGJEYXRhLmxlbmd0aCIgQGNsaWNrPSJoYW5kbGVFeHBvcnQiPuWvvOWHujwvZWwtYnV0dG9uPgogICAgICAgICAgPC9kaXY+CiAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICA8dGVtcGxhdGUgI3Rvb2xzPgogICAgICAgICAgPER5bmFtaWNUYWJsZUZpZWxkcwogICAgICAgICAgICB0aXRsZT0i6KGo5qC86YWN572uIgogICAgICAgICAgICA6dGFibGUtY29uZmlnLWNvZGU9ImdyaWRDb2RlIgogICAgICAgICAgICBAdXBkYXRlQ29sdW1uPSJjaGFuZ2VDb2x1bW4iCiAgICAgICAgICAvPgogICAgICAgIDwvdGVtcGxhdGU+CiAgICAgIDwvdnhlLXRvb2xiYXI+CiAgICAgIDxkaXYgY2xhc3M9InRiLXgiPgogICAgICAgIDwhLS0gICAgICAgICAgYWN0aXZlTWV0aG9kOiBhY3RpdmVDZWxsTWV0aG9kLC0tPgogICAgICAgIDx2eGUtdGFibGUKICAgICAgICAgIHJlZj0ieFRhYmxlIgogICAgICAgICAgOmtleT0idGJLZXkiCiAgICAgICAgICA6ZW1wdHktcmVuZGVyPSJ7bmFtZTogJ05vdERhdGEnfSIKICAgICAgICAgIHNob3ctaGVhZGVyLW92ZXJmbG93CiAgICAgICAgICA6Y2hlY2tib3gtY29uZmlnPSJ7Y2hlY2tGaWVsZDogJ2NoZWNrZWQnfSIKICAgICAgICAgIGNsYXNzPSJjcy12eGUtdGFibGUiCiAgICAgICAgICA6cm93LWNvbmZpZz0ie2lzQ3VycmVudDogdHJ1ZSwgaXNIb3ZlcjogdHJ1ZX0iCiAgICAgICAgICBhbGlnbj0ibGVmdCIKICAgICAgICAgIGhlaWdodD0iMTAwJSIKICAgICAgICAgIDpmaWx0ZXItY29uZmlnPSJ7c2hvd0ljb246ZmFsc2V9IgogICAgICAgICAgc2hvdy1vdmVyZmxvdwogICAgICAgICAgOmxvYWRpbmc9InRiTG9hZGluZyIKICAgICAgICAgIHN0cmlwZQogICAgICAgICAgOnNjcm9sbC15PSJ7ZW5hYmxlZDogdHJ1ZSwgZ3Q6IDIwfSIKICAgICAgICAgIHNpemU9Im1lZGl1bSIKICAgICAgICAgIDplZGl0LWNvbmZpZz0iewogICAgICAgICAgICB0cmlnZ2VyOiAnY2xpY2snLAogICAgICAgICAgICBtb2RlOiAnY2VsbCcsCiAgICAgICAgICAgIHNob3dJY29uOiAhaXNWaWV3LAoKICAgICAgICAgIH0iCiAgICAgICAgICA6ZGF0YT0idGJEYXRhIgogICAgICAgICAgcmVzaXphYmxlCiAgICAgICAgICA6dG9vbHRpcC1jb25maWc9InsgZW50ZXJhYmxlOiB0cnVlIH0iCiAgICAgICAgICBAY2hlY2tib3gtYWxsPSJ0YlNlbGVjdENoYW5nZSIKICAgICAgICAgIEBjaGVja2JveC1jaGFuZ2U9InRiU2VsZWN0Q2hhbmdlIgogICAgICAgID4KICAgICAgICAgIDx2eGUtY29sdW1uIHYtaWY9IiFpc1ZpZXciIGZpeGVkPSJsZWZ0IiB0eXBlPSJjaGVja2JveCIgd2lkdGg9IjYwIiAvPgogICAgICAgICAgPHRlbXBsYXRlIHYtZm9yPSJpdGVtIGluIGNvbHVtbnMiPgogICAgICAgICAgICA8dnhlLWNvbHVtbgogICAgICAgICAgICAgIHYtaWY9Iml0ZW0uQ29kZSA9PT0gJ0lzX0NvbXBvbmVudCciCiAgICAgICAgICAgICAgOmtleT0iaXRlbS5Db2RlIgogICAgICAgICAgICAgIDphbGlnbj0iaXRlbS5BbGlnbiIKICAgICAgICAgICAgICA6ZmllbGQ9Iml0ZW0uQ29kZSIKICAgICAgICAgICAgICA6dGl0bGU9Iml0ZW0uRGlzcGxheV9OYW1lIgogICAgICAgICAgICAgIHNvcnRhYmxlCiAgICAgICAgICAgICAgOmZpeGVkPSJpdGVtLklzX0Zyb3plbj9pdGVtLkZyb3plbl9EaXJjdGlvbjonJyIKICAgICAgICAgICAgICA6ZmlsdGVycz0iaXNDb21wb25lbnRPcHRpb25zIgogICAgICAgICAgICAgIDpmaWx0ZXItbWV0aG9kPSJmaWx0ZXJDb21wb25lbnRNZXRob2QiCiAgICAgICAgICAgICAgOndpZHRoPSJpdGVtLldpZHRoIgogICAgICAgICAgICA+CiAgICAgICAgICAgICAgPHRlbXBsYXRlICNkZWZhdWx0PSJ7IHJvdyB9Ij4KICAgICAgICAgICAgICAgIDxlbC10YWcKICAgICAgICAgICAgICAgICAgOnR5cGU9InJvdy5Jc19Db21wb25lbnQgPyAnZGFuZ2VyJyA6ICdzdWNjZXNzJyIKICAgICAgICAgICAgICAgID57eyByb3cuSXNfQ29tcG9uZW50ID8gJ+WQpicgOiAn5pivJyB9fQogICAgICAgICAgICAgICAgPC9lbC10YWc+CiAgICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgICAgPC92eGUtY29sdW1uPgoKICAgICAgICAgICAgPHZ4ZS1jb2x1bW4KICAgICAgICAgICAgICB2LWVsc2UtaWY9IlsnVHlwZScsJ1R5cGVfTmFtZSddLmluY2x1ZGVzKGl0ZW0uQ29kZSkiCiAgICAgICAgICAgICAgOmtleT0iaXRlbS5Db2RlIgogICAgICAgICAgICAgIDphbGlnbj0iaXRlbS5BbGlnbiIKICAgICAgICAgICAgICA6Zml4ZWQ9Iml0ZW0uSXNfRnJvemVuP2l0ZW0uRnJvemVuX0RpcmN0aW9uOicnIgogICAgICAgICAgICAgIDpmaWx0ZXItbWV0aG9kPSJmaWx0ZXJUeXBlTWV0aG9kIgogICAgICAgICAgICAgIDpmaWVsZD0iaXRlbS5Db2RlIgogICAgICAgICAgICAgIDpmaWx0ZXJzPSJmaWx0ZXJUeXBlT3B0aW9uIgogICAgICAgICAgICAgIDp0aXRsZT0iaXRlbS5EaXNwbGF5X05hbWUiCiAgICAgICAgICAgICAgc29ydGFibGUKICAgICAgICAgICAgICA6d2lkdGg9Iml0ZW0uV2lkdGgiCiAgICAgICAgICAgID4KICAgICAgICAgICAgICA8dGVtcGxhdGUgI2ZpbHRlcj0ieyAkcGFuZWwsIGNvbHVtbiB9Ij4KICAgICAgICAgICAgICAgIDxpbnB1dAogICAgICAgICAgICAgICAgICB2LWZvcj0iKG9wdGlvbiwgaW5kZXgpIGluIGNvbHVtbi5maWx0ZXJzIgogICAgICAgICAgICAgICAgICA6a2V5PSJpbmRleCIKICAgICAgICAgICAgICAgICAgdi1tb2RlbD0ib3B0aW9uLmRhdGEiCiAgICAgICAgICAgICAgICAgIHR5cGU9InR5cGUiCiAgICAgICAgICAgICAgICAgIEBpbnB1dD0iJHBhbmVsLmNoYW5nZU9wdGlvbigkZXZlbnQsICEhb3B0aW9uLmRhdGEsIG9wdGlvbikiCiAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgICAgICA8dGVtcGxhdGUgI2RlZmF1bHQ9Insgcm93IH0iPgogICAgICAgICAgICAgICAge3sgcm93W2l0ZW0uQ29kZV0gfCBkaXNwbGF5VmFsdWUgfX0KICAgICAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgICAgICA8L3Z4ZS1jb2x1bW4+CiAgICAgICAgICAgIDx2eGUtY29sdW1uCiAgICAgICAgICAgICAgdi1lbHNlLWlmPSJbJ0NvbXBfQ29kZScsJ1BhcnRfQ29kZSddLmluY2x1ZGVzKGl0ZW0uQ29kZSkiCiAgICAgICAgICAgICAgOmtleT0iaXRlbS5Db2RlIgogICAgICAgICAgICAgIDphbGlnbj0iaXRlbS5BbGlnbiIKICAgICAgICAgICAgICA6ZmlsdGVyLW1ldGhvZD0iZmlsdGVyQ29kZU1ldGhvZCIKICAgICAgICAgICAgICA6ZmllbGQ9Iml0ZW0uQ29kZSIKICAgICAgICAgICAgICA6Zml4ZWQ9Iml0ZW0uSXNfRnJvemVuP2l0ZW0uRnJvemVuX0RpcmN0aW9uOicnIgogICAgICAgICAgICAgIDpmaWx0ZXJzPSJmaWx0ZXJDb2RlT3B0aW9uIgogICAgICAgICAgICAgIDp0aXRsZT0iaXRlbS5EaXNwbGF5X05hbWUiCiAgICAgICAgICAgICAgc29ydGFibGUKICAgICAgICAgICAgICA6d2lkdGg9Iml0ZW0uV2lkdGgiCiAgICAgICAgICAgID4KICAgICAgICAgICAgICA8dGVtcGxhdGUgI2ZpbHRlcj0ieyAkcGFuZWwsIGNvbHVtbiB9Ij4KICAgICAgICAgICAgICAgIDxpbnB1dAogICAgICAgICAgICAgICAgICB2LWZvcj0iKG9wdGlvbiwgaW5kZXgpIGluIGNvbHVtbi5maWx0ZXJzIgogICAgICAgICAgICAgICAgICA6a2V5PSJpbmRleCIKICAgICAgICAgICAgICAgICAgdi1tb2RlbD0ib3B0aW9uLmRhdGEiCiAgICAgICAgICAgICAgICAgIHR5cGU9InR5cGUiCiAgICAgICAgICAgICAgICAgIEBpbnB1dD0iJHBhbmVsLmNoYW5nZU9wdGlvbigkZXZlbnQsICEhb3B0aW9uLmRhdGEsIG9wdGlvbikiCiAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgICAgICA8dGVtcGxhdGUgI2RlZmF1bHQ9Insgcm93IH0iPgogICAgICAgICAgICAgICAgPGVsLXRhZyB2LWlmPSJyb3cuSXNfQ2hhbmdlIiBzdHlsZT0ibWFyZ2luOiA4cHg7IiB0eXBlPSJkYW5nZXIiPuWPmDwvZWwtdGFnPgogICAgICAgICAgICAgICAgPGVsLXRhZyB2LWlmPSJyb3cuc3RvcEZsYWciIHN0eWxlPSJtYXJnaW4tcmlnaHQ6IDhweDsiIHR5cGU9ImRhbmdlciI+5YGcPC9lbC10YWc+CiAgICAgICAgICAgICAgICA8ZWwtbGluayB2LWlmPSJyb3cuRHdnQ291bnQ+MCIgdHlwZT0icHJpbWFyeSIgQGNsaWNrLnN0b3A9ImhhbmRsZUR3Zyhyb3cpIj4ge3sgcm93W2l0ZW0uQ29kZV0gfCBkaXNwbGF5VmFsdWUgfX0KICAgICAgICAgICAgICAgIDwvZWwtbGluaz4KICAgICAgICAgICAgICAgIDxzcGFuIHYtZWxzZT57eyByb3dbaXRlbS5Db2RlXSB8IGRpc3BsYXlWYWx1ZSB9fTwvc3Bhbj4KICAgICAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgICAgICA8L3Z4ZS1jb2x1bW4+CiAgICAgICAgICAgIDx2eGUtY29sdW1uCiAgICAgICAgICAgICAgdi1lbHNlLWlmPSJpdGVtLkNvZGUgPT09ICdTcGVjJyIKICAgICAgICAgICAgICA6a2V5PSJpdGVtLkNvZGUiCiAgICAgICAgICAgICAgOmFsaWduPSJpdGVtLkFsaWduIgogICAgICAgICAgICAgIDpmaWVsZD0iaXRlbS5Db2RlIgogICAgICAgICAgICAgIDpmaXhlZD0iaXRlbS5Jc19Gcm96ZW4/aXRlbS5Gcm96ZW5fRGlyY3Rpb246JyciCiAgICAgICAgICAgICAgOmZpbHRlcnM9InNwZWNPcHRpb25zIgogICAgICAgICAgICAgIDpmaWx0ZXItbWV0aG9kPSJmaWx0ZXJTcGVjTWV0aG9kIgogICAgICAgICAgICAgIDp0aXRsZT0iaXRlbS5EaXNwbGF5X05hbWUiCiAgICAgICAgICAgICAgc29ydGFibGUKICAgICAgICAgICAgICA6d2lkdGg9Iml0ZW0uV2lkdGgiCiAgICAgICAgICAgID4KICAgICAgICAgICAgICA8dGVtcGxhdGUgI2ZpbHRlcj0ieyAkcGFuZWwsIGNvbHVtbiB9Ij4KICAgICAgICAgICAgICAgIDxpbnB1dAogICAgICAgICAgICAgICAgICB2LWZvcj0iKG9wdGlvbiwgaW5kZXgpIGluIGNvbHVtbi5maWx0ZXJzIgogICAgICAgICAgICAgICAgICA6a2V5PSJpbmRleCIKICAgICAgICAgICAgICAgICAgdi1tb2RlbD0ib3B0aW9uLmRhdGEiCiAgICAgICAgICAgICAgICAgIHR5cGU9InR5cGUiCiAgICAgICAgICAgICAgICAgIEBpbnB1dD0iJHBhbmVsLmNoYW5nZU9wdGlvbigkZXZlbnQsICEhb3B0aW9uLmRhdGEsIG9wdGlvbikiCiAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgICAgICA8dGVtcGxhdGUgI2RlZmF1bHQ9Insgcm93IH0iPgogICAgICAgICAgICAgICAge3sgcm93LlNwZWMgfCBkaXNwbGF5VmFsdWUgfX0KICAgICAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgICAgICA8L3Z4ZS1jb2x1bW4+CiAgICAgICAgICAgIDx2eGUtY29sdW1uCiAgICAgICAgICAgICAgdi1lbHNlLWlmPSJpdGVtLkNvZGUgPT09ICdQcm9qZWN0X05hbWUnIgogICAgICAgICAgICAgIDprZXk9Iml0ZW0uQ29kZSIKICAgICAgICAgICAgICA6YWxpZ249Iml0ZW0uQWxpZ24iCiAgICAgICAgICAgICAgOmZpZWxkPSJpdGVtLkNvZGUiCiAgICAgICAgICAgICAgOmZpeGVkPSJpdGVtLklzX0Zyb3plbj9pdGVtLkZyb3plbl9EaXJjdGlvbjonJyIKICAgICAgICAgICAgICA6ZmlsdGVycz0icHJvamVjdE9wdGlvbnMiCiAgICAgICAgICAgICAgOmZpbHRlci1tZXRob2Q9ImZpbHRlclByb2plY3RNZXRob2QiCiAgICAgICAgICAgICAgOnRpdGxlPSJpdGVtLkRpc3BsYXlfTmFtZSIKICAgICAgICAgICAgICBzb3J0YWJsZQogICAgICAgICAgICAgIDp3aWR0aD0iaXRlbS5XaWR0aCIKICAgICAgICAgICAgPgogICAgICAgICAgICAgIDx0ZW1wbGF0ZSAjZmlsdGVyPSJ7ICRwYW5lbCwgY29sdW1uIH0iPgogICAgICAgICAgICAgICAgPHNlbGVjdCB2LWZvcj0iKG9wdGlvbiwgaW5kZXgpIGluIGNvbHVtbi5maWx0ZXJzIiA6a2V5PSJpbmRleCIgdi1tb2RlbD0ib3B0aW9uLmRhdGEiIGNsYXNzPSJteS1zZWxlY3QiIEBjaGFuZ2U9IiRwYW5lbC5jaGFuZ2VPcHRpb24oJGV2ZW50LCAhIW9wdGlvbi5kYXRhLCBvcHRpb24pIj4KICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2LWZvcj0iKGxhYmVsLCBjSW5kZXgpIGluIHByb2plY3RMaXN0IiA6a2V5PSJjSW5kZXgiIDp2YWx1ZT0ibGFiZWwiPnt7IGxhYmVsIH19PC9vcHRpb24+CiAgICAgICAgICAgICAgICA8L3NlbGVjdD4KICAgICAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgICAgICAgIDx0ZW1wbGF0ZSAjZGVmYXVsdD0ieyByb3cgfSI+CiAgICAgICAgICAgICAgICB7eyByb3cuUHJvamVjdF9OYW1lIHwgZGlzcGxheVZhbHVlIH19CiAgICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgICAgPC92eGUtY29sdW1uPgogICAgICAgICAgICA8dnhlLWNvbHVtbgogICAgICAgICAgICAgIHYtZWxzZS1pZj0iaXRlbS5Db2RlID09PSAnQXJlYV9OYW1lJyIKICAgICAgICAgICAgICA6a2V5PSJpdGVtLkNvZGUiCiAgICAgICAgICAgICAgOmFsaWduPSJpdGVtLkFsaWduIgogICAgICAgICAgICAgIDpmaWVsZD0iaXRlbS5Db2RlIgogICAgICAgICAgICAgIDpmaXhlZD0iaXRlbS5Jc19Gcm96ZW4/aXRlbS5Gcm96ZW5fRGlyY3Rpb246JyciCiAgICAgICAgICAgICAgOmZpbHRlcnM9ImFyZWFPcHRpb25zIgogICAgICAgICAgICAgIDpmaWx0ZXItbWV0aG9kPSJmaWx0ZXJBcmVhTWV0aG9kIgogICAgICAgICAgICAgIDp0aXRsZT0iaXRlbS5EaXNwbGF5X05hbWUiCiAgICAgICAgICAgICAgc29ydGFibGUKICAgICAgICAgICAgICA6d2lkdGg9Iml0ZW0uV2lkdGgiCiAgICAgICAgICAgID4KICAgICAgICAgICAgICA8dGVtcGxhdGUgI2ZpbHRlcj0ieyAkcGFuZWwsIGNvbHVtbiB9Ij4KICAgICAgICAgICAgICAgIDxzZWxlY3Qgdi1mb3I9IihvcHRpb24sIGluZGV4KSBpbiBjb2x1bW4uZmlsdGVycyIgOmtleT0iaW5kZXgiIHYtbW9kZWw9Im9wdGlvbi5kYXRhIiBjbGFzcz0ibXktc2VsZWN0IiBAY2hhbmdlPSIkcGFuZWwuY2hhbmdlT3B0aW9uKCRldmVudCwgISFvcHRpb24uZGF0YSwgb3B0aW9uKSI+CiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdi1mb3I9IihsYWJlbCwgY0luZGV4KSBpbiBhcmVhTGlzdCIgOmtleT0iY0luZGV4IiA6dmFsdWU9ImxhYmVsIj57eyBsYWJlbCB9fTwvb3B0aW9uPgogICAgICAgICAgICAgICAgPC9zZWxlY3Q+CiAgICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgICAgICA8dGVtcGxhdGUgI2RlZmF1bHQ9Insgcm93IH0iPgogICAgICAgICAgICAgICAge3sgcm93LkFyZWFfTmFtZSB8IGRpc3BsYXlWYWx1ZSB9fQogICAgICAgICAgICAgIDwvdGVtcGxhdGU+CiAgICAgICAgICAgIDwvdnhlLWNvbHVtbj4KICAgICAgICAgICAgPHZ4ZS1jb2x1bW4KICAgICAgICAgICAgICB2LWVsc2UtaWY9Iml0ZW0uQ29kZSA9PT0gJ0luc3RhbGxVbml0X05hbWUnIgogICAgICAgICAgICAgIDprZXk9Iml0ZW0uQ29kZSIKICAgICAgICAgICAgICA6YWxpZ249Iml0ZW0uQWxpZ24iCiAgICAgICAgICAgICAgOmZpZWxkPSJpdGVtLkNvZGUiCiAgICAgICAgICAgICAgOmZpeGVkPSJpdGVtLklzX0Zyb3plbj9pdGVtLkZyb3plbl9EaXJjdGlvbjonJyIKICAgICAgICAgICAgICA6ZmlsdGVycz0iaW5zdGFsbE9wdGlvbnMiCiAgICAgICAgICAgICAgOmZpbHRlci1tZXRob2Q9ImZpbHRlckluc3RhbGxNZXRob2QiCiAgICAgICAgICAgICAgOnRpdGxlPSJpdGVtLkRpc3BsYXlfTmFtZSIKICAgICAgICAgICAgICBzb3J0YWJsZQogICAgICAgICAgICAgIDp3aWR0aD0iaXRlbS5XaWR0aCIKICAgICAgICAgICAgPgogICAgICAgICAgICAgIDx0ZW1wbGF0ZSAjZmlsdGVyPSJ7ICRwYW5lbCwgY29sdW1uIH0iPgogICAgICAgICAgICAgICAgPHNlbGVjdCB2LWZvcj0iKG9wdGlvbiwgaW5kZXgpIGluIGNvbHVtbi5maWx0ZXJzIiA6a2V5PSJpbmRleCIgdi1tb2RlbD0ib3B0aW9uLmRhdGEiIGNsYXNzPSJteS1zZWxlY3QiIEBjaGFuZ2U9IiRwYW5lbC5jaGFuZ2VPcHRpb24oJGV2ZW50LCAhIW9wdGlvbi5kYXRhLCBvcHRpb24pIj4KICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2LWZvcj0iKGxhYmVsLCBjSW5kZXgpIGluIGluc3RhbGxMaXN0IiA6a2V5PSJjSW5kZXgiIDp2YWx1ZT0ibGFiZWwiPnt7IGxhYmVsIH19PC9vcHRpb24+CiAgICAgICAgICAgICAgICA8L3NlbGVjdD4KICAgICAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgICAgICAgIDx0ZW1wbGF0ZSAjZGVmYXVsdD0ieyByb3cgfSI+CiAgICAgICAgICAgICAgICB7eyByb3cuSW5zdGFsbFVuaXRfTmFtZSB8IGRpc3BsYXlWYWx1ZSB9fQogICAgICAgICAgICAgIDwvdGVtcGxhdGU+CiAgICAgICAgICAgIDwvdnhlLWNvbHVtbj4KICAgICAgICAgICAgPHZ4ZS1jb2x1bW4KICAgICAgICAgICAgICB2LWVsc2UtaWY9Iml0ZW0uQ29kZSA9PT0gJ1NjaGR1bGVkX1dlaWdodCciCiAgICAgICAgICAgICAgOmtleT0iaXRlbS5Db2RlIgogICAgICAgICAgICAgIDphbGlnbj0iaXRlbS5BbGlnbiIKICAgICAgICAgICAgICA6Zml4ZWQ9Iml0ZW0uSXNfRnJvemVuP2l0ZW0uRnJvemVuX0RpcmN0aW9uOicnIgogICAgICAgICAgICAgIDpmaWVsZD0iaXRlbS5Db2RlIgogICAgICAgICAgICAgIDp0aXRsZT0iaXRlbS5EaXNwbGF5X05hbWUiCiAgICAgICAgICAgICAgc29ydGFibGUKICAgICAgICAgICAgICA6d2lkdGg9Iml0ZW0uV2lkdGgiCiAgICAgICAgICAgID4KICAgICAgICAgICAgICA8dGVtcGxhdGUgI2RlZmF1bHQ9Insgcm93IH0iPgogICAgICAgICAgICAgICAge3sgKHJvdy5TY2hkdWxlZF9Db3VudCAqIHJvdy5XZWlnaHQpLnRvRml4ZWQoMikgLyAxIH19CiAgICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgICAgPC92eGUtY29sdW1uPgogICAgICAgICAgICA8dnhlLWNvbHVtbgogICAgICAgICAgICAgIHYtZWxzZS1pZj0iaXRlbS5Db2RlID09PSAnVGVjaG5vbG9neV9QYXRoJyIKICAgICAgICAgICAgICA6a2V5PSJpdGVtLkNvZGUiCiAgICAgICAgICAgICAgOmFsaWduPSJpdGVtLkFsaWduIgogICAgICAgICAgICAgIDpmaWVsZD0iaXRlbS5Db2RlIgogICAgICAgICAgICAgIDpzaG93LW92ZXJmbG93PSJmYWxzZSIKICAgICAgICAgICAgICA6Zml4ZWQ9Iml0ZW0uSXNfRnJvemVuP2l0ZW0uRnJvemVuX0RpcmN0aW9uOicnIgogICAgICAgICAgICAgIDp0aXRsZT0iaXRlbS5EaXNwbGF5X05hbWUiCiAgICAgICAgICAgICAgc29ydGFibGUKICAgICAgICAgICAgICA6bWluLXdpZHRoPSJpdGVtLldpZHRoIgogICAgICAgICAgICA+CiAgICAgICAgICAgICAgPHRlbXBsYXRlICNkZWZhdWx0PSJ7IHJvdyB9Ij4KICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNzLWNvbHVtbi1yb3ciPgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJjcy1lbGwiPgogICAgICAgICAgICAgICAgICAgIDxlbC10b29sdGlwIGNsYXNzPSJpdGVtIiBlZmZlY3Q9ImRhcmsiIDpjb250ZW50PSJyb3cuVGVjaG5vbG9neV9QYXRoIiBwbGFjZW1lbnQ9InRvcCI+CiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57eyByb3cuVGVjaG5vbG9neV9QYXRoIHwgZGlzcGxheVZhbHVlIH19PC9zcGFuPgogICAgICAgICAgICAgICAgICAgIDwvZWwtdG9vbHRpcD4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgIDxpCiAgICAgICAgICAgICAgICAgICAgdi1pZj0iIWlzVmlldyIKICAgICAgICAgICAgICAgICAgICBjbGFzcz0iZWwtaWNvbi1lZGl0IgogICAgICAgICAgICAgICAgICAgIEBjbGljaz0ib3BlbkJQQURpYWxvZygyLCByb3cpIgogICAgICAgICAgICAgICAgICAvPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgICAgPC92eGUtY29sdW1uPgogICAgICAgICAgICA8dnhlLWNvbHVtbgogICAgICAgICAgICAgIHYtZWxzZS1pZj0iaXRlbS5Db2RlID09PSAnUGFydF9Vc2VkX1Byb2Nlc3MnIgogICAgICAgICAgICAgIDprZXk9Iml0ZW0uQ29kZSIKICAgICAgICAgICAgICA6YWxpZ249Iml0ZW0uQWxpZ24iCiAgICAgICAgICAgICAgOmZpZWxkPSJpdGVtLkNvZGUiCiAgICAgICAgICAgICAgOmZpeGVkPSJpdGVtLklzX0Zyb3plbj9pdGVtLkZyb3plbl9EaXJjdGlvbjonJyIKICAgICAgICAgICAgICA6dGl0bGU9Iml0ZW0uRGlzcGxheV9OYW1lIgogICAgICAgICAgICAgIHNvcnRhYmxlCiAgICAgICAgICAgICAgOnNob3ctb3ZlcmZsb3c9ImZhbHNlIgogICAgICAgICAgICAgIDptaW4td2lkdGg9Iml0ZW0uV2lkdGgiCiAgICAgICAgICAgID4KICAgICAgICAgICAgICA8dGVtcGxhdGUgI2RlZmF1bHQ9Insgcm93IH0iPgogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iY3MtY29sdW1uLXJvdyI+CiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNzLWVsbCI+CiAgICAgICAgICAgICAgICAgICAgPGVsLXRvb2x0aXAgY2xhc3M9Iml0ZW0iIGVmZmVjdD0iZGFyayIgOmNvbnRlbnQ9InJvdy5QYXJ0X1VzZWRfUHJvY2VzcyIgcGxhY2VtZW50PSJ0b3AiPgogICAgICAgICAgICAgICAgICAgICAgPHNwYW4+e3sgcm93LlBhcnRfVXNlZF9Qcm9jZXNzIHwgZGlzcGxheVZhbHVlIH19PC9zcGFuPgogICAgICAgICAgICAgICAgICAgIDwvZWwtdG9vbHRpcD4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgIDxpCiAgICAgICAgICAgICAgICAgICAgdi1pZj0ic2hvd1BhcnRVc2VkUHJvY2Vzcyhyb3cpIgogICAgICAgICAgICAgICAgICAgIGNsYXNzPSJlbC1pY29uLWVkaXQiCiAgICAgICAgICAgICAgICAgICAgQGNsaWNrPSJoYW5kbGVCYXRjaE93bmVyKDIsIHJvdykiCiAgICAgICAgICAgICAgICAgIC8+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgICAgICA8L3Z4ZS1jb2x1bW4+CiAgICAgICAgICAgIDx2eGUtY29sdW1uCiAgICAgICAgICAgICAgdi1lbHNlLWlmPSJpdGVtLkNvZGUgPT09ICdXb3Jrc2hvcF9OYW1lJyIKICAgICAgICAgICAgICA6a2V5PSJpdGVtLkNvZGUiCiAgICAgICAgICAgICAgOmFsaWduPSJpdGVtLkFsaWduIgogICAgICAgICAgICAgIDpmaWVsZD0iaXRlbS5Db2RlIgogICAgICAgICAgICAgIDpmaXhlZD0iaXRlbS5Jc19Gcm96ZW4/aXRlbS5Gcm96ZW5fRGlyY3Rpb246JyciCiAgICAgICAgICAgICAgOnRpdGxlPSJpdGVtLkRpc3BsYXlfTmFtZSIKICAgICAgICAgICAgICBzb3J0YWJsZQogICAgICAgICAgICAgIDpzaG93LW92ZXJmbG93PSJmYWxzZSIKICAgICAgICAgICAgICA6bWluLXdpZHRoPSJpdGVtLldpZHRoIgogICAgICAgICAgICA+CiAgICAgICAgICAgICAgPHRlbXBsYXRlICNkZWZhdWx0PSJ7IHJvdyB9Ij4KICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNzLWNvbHVtbi1yb3ciPgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJjcy1lbGwiPgogICAgICAgICAgICAgICAgICAgIDxlbC10b29sdGlwIGNsYXNzPSJpdGVtIiBlZmZlY3Q9ImRhcmsiIDpjb250ZW50PSJyb3cuV29ya3Nob3BfTmFtZSIgcGxhY2VtZW50PSJ0b3AiPgogICAgICAgICAgICAgICAgICAgICAgPHNwYW4+e3sgcm93LldvcmtzaG9wX05hbWUgfCBkaXNwbGF5VmFsdWUgfX08L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgPC9lbC10b29sdGlwPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgPGkKICAgICAgICAgICAgICAgICAgICB2LWlmPSIhaXNWaWV3IgogICAgICAgICAgICAgICAgICAgIGNsYXNzPSJlbC1pY29uLWVkaXQiCiAgICAgICAgICAgICAgICAgICAgQGNsaWNrPSJoYW5kbGVCYXRjaFdvcmtzaG9wKDIsIHJvdykiCiAgICAgICAgICAgICAgICAgIC8+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgICAgICA8L3Z4ZS1jb2x1bW4+CiAgICAgICAgICAgIDx2eGUtY29sdW1uCiAgICAgICAgICAgICAgdi1lbHNlLWlmPSJpdGVtLkNvZGUgPT09ICdTY2hkdWxlZF9Db3VudCciCiAgICAgICAgICAgICAgOmtleT0iaXRlbS5Db2RlIgogICAgICAgICAgICAgIDphbGlnbj0iaXRlbS5BbGlnbiIKICAgICAgICAgICAgICA6ZmllbGQ9Iml0ZW0uQ29kZSIKICAgICAgICAgICAgICA6Zml4ZWQ9Iml0ZW0uSXNfRnJvemVuP2l0ZW0uRnJvemVuX0RpcmN0aW9uOicnIgogICAgICAgICAgICAgIDp0aXRsZT0iaXRlbS5EaXNwbGF5X05hbWUiCiAgICAgICAgICAgICAgc29ydGFibGUKICAgICAgICAgICAgICA6ZWRpdC1yZW5kZXI9IntlbmFibGVkOiFpc1ZpZXd9IgogICAgICAgICAgICAgIDptaW4td2lkdGg9Iml0ZW0uV2lkdGgiCiAgICAgICAgICAgID4KICAgICAgICAgICAgICA8dGVtcGxhdGUgI2VkaXQ9Insgcm93IH0iPgogICAgICAgICAgICAgICAgPHZ4ZS1pbnB1dAogICAgICAgICAgICAgICAgICB2LW1vZGVsLm51bWJlcj0icm93LlNjaGR1bGVkX0NvdW50IgogICAgICAgICAgICAgICAgICB0eXBlPSJpbnRlZ2VyIgogICAgICAgICAgICAgICAgICBtaW49IjAiCiAgICAgICAgICAgICAgICAgIDptYXg9InJvdy5jaG9vc2VDb3VudCIKICAgICAgICAgICAgICAgIC8+CiAgICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgICAgICA8dGVtcGxhdGUgI2RlZmF1bHQ9Insgcm93IH0iPgogICAgICAgICAgICAgICAge3sgcm93LlNjaGR1bGVkX0NvdW50IHwgZGlzcGxheVZhbHVlIH19CiAgICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgICAgPC92eGUtY29sdW1uPgogICAgICAgICAgICA8dnhlLWNvbHVtbgogICAgICAgICAgICAgIHYtZWxzZQogICAgICAgICAgICAgIDprZXk9Iml0ZW0uSWQiCiAgICAgICAgICAgICAgOmFsaWduPSJpdGVtLkFsaWduIgogICAgICAgICAgICAgIDpmaXhlZD0iaXRlbS5Jc19Gcm96ZW4/aXRlbS5Gcm96ZW5fRGlyY3Rpb246JyciCiAgICAgICAgICAgICAgc2hvdy1vdmVyZmxvdz0idG9vbHRpcCIKICAgICAgICAgICAgICBzb3J0YWJsZQogICAgICAgICAgICAgIDpmaWVsZD0iaXRlbS5Db2RlIgogICAgICAgICAgICAgIDp0aXRsZT0iaXRlbS5EaXNwbGF5X05hbWUiCiAgICAgICAgICAgICAgOm1pbi13aWR0aD0iaXRlbS5XaWR0aCIKICAgICAgICAgICAgLz4KICAgICAgICAgIDwvdGVtcGxhdGU+CgogICAgICAgIDwvdnhlLXRhYmxlPgogICAgICA8L2Rpdj4KICAgICAgPGVsLWRpdmlkZXIgdi1pZj0iIWlzVmlldyIgY2xhc3M9ImVsRGl2ZGVyIiAvPgogICAgICA8Zm9vdGVyIHYtaWY9IiFpc1ZpZXciPgogICAgICAgIDxkaXYgY2xhc3M9ImRhdGEtaW5mbyI+CiAgICAgICAgICA8ZWwtdGFnCiAgICAgICAgICAgIHNpemU9Im1lZGl1bSIKICAgICAgICAgICAgY2xhc3M9ImluZm8teCIKICAgICAgICAgID7lt7LpgIkge3sgbXVsdGlwbGVTZWxlY3Rpb24ubGVuZ3RoIH19IOadoeaVsOaNrgogICAgICAgICAgPC9lbC10YWc+CiAgICAgICAgICA8ZWwtdGFnIHYtaWY9InRpcExhYmVsIiBzaXplPSJtZWRpdW0iIGNsYXNzPSJpbmZvLXgiPnt7CiAgICAgICAgICAgIHRpcExhYmVsCiAgICAgICAgICB9fQogICAgICAgICAgPC9lbC10YWc+CiAgICAgICAgPC9kaXY+CiAgICAgICAgPGRpdj4KICAgICAgICAgIDxlbC1idXR0b24gdi1pZj0id29ya3Nob3BFbmFibGVkJiYhaXNOZXN0IiB0eXBlPSJwcmltYXJ5IiA6ZGlzYWJsZWQ9InRiRGF0YS5zb21lKGl0ZW09Pml0ZW0uc3RvcEZsYWcpIiBAY2xpY2s9InNhdmVXb3JrU2hvcCI+5L+d5a2Y6L2m6Ze05YiG6YWNPC9lbC1idXR0b24+CiAgICAgICAgICA8ZWwtYnV0dG9uCiAgICAgICAgICAgIHYtaWY9IiFpc05lc3QiCiAgICAgICAgICAgIHR5cGU9InByaW1hcnkiCiAgICAgICAgICAgIDpkaXNhYmxlZD0idGJEYXRhLnNvbWUoaXRlbT0+aXRlbS5zdG9wRmxhZykiCiAgICAgICAgICAgIDpsb2FkaW5nPSJzYXZlTG9hZGluZyIKICAgICAgICAgICAgQGNsaWNrPSJzYXZlRHJhZnQoZmFsc2UpIgogICAgICAgICAgPuS/neWtmOiNieeovwogICAgICAgICAgPC9lbC1idXR0b24+CiAgICAgICAgICA8ZWwtYnV0dG9uIDpkaXNhYmxlZD0iZGVsZXRlTG9hZGluZyB8fCB0YkRhdGEuc29tZShpdGVtPT5pdGVtLnN0b3BGbGFnKSIgQGNsaWNrPSJoYW5kbGVTdWJtaXQiPuS4i+WPkeS7u+WKoTwvZWwtYnV0dG9uPgogICAgICAgIDwvZGl2PgogICAgICA8L2Zvb3Rlcj4KICAgIDwvZWwtY2FyZD4KICA8L2Rpdj4KCiAgPGVsLWRpYWxvZwogICAgdi1pZj0iZGlhbG9nVmlzaWJsZSIKICAgIHYtZGlhbG9nRHJhZwogICAgY2xhc3M9InBsbS1jdXN0b20tZGlhbG9nIgogICAgOnRpdGxlPSJ0aXRsZSIKICAgIDp2aXNpYmxlLnN5bmM9ImRpYWxvZ1Zpc2libGUiCiAgICA6d2lkdGg9ImRXaWR0aCIKICAgIHRvcD0iMTB2aCIKICAgIEBjbG9zZT0iaGFuZGxlQ2xvc2UiCiAgPgogICAgPGNvbXBvbmVudAogICAgICA6aXM9ImN1cnJlbnRDb21wb25lbnQiCiAgICAgIHJlZj0iY29udGVudCIKICAgICAgOmJvbS1saXN0PSJib21MaXN0IgogICAgICA6aXMtbmVzdD0iaXNOZXN0IgogICAgICA6cGFydC1uYW1lPSJwYXJ0TmFtZSIKICAgICAgOmlzLXBhcnQtcHJlcGFyZT0iaXNQYXJ0UHJlcGFyZSIKICAgICAgOnByb2Nlc3MtbGlzdD0icHJvY2Vzc0xpc3QiCiAgICAgIDpwYWdlLXR5cGU9InBhZ2VUeXBlIgogICAgICA6aGFzLXVuaXQtcGFydD0iaGFzVW5pdFBhcnQiCiAgICAgIDpwYXJ0LXR5cGUtb3B0aW9uPSJ0eXBlT3B0aW9uIgogICAgICBAY2xvc2U9ImhhbmRsZUNsb3NlIgogICAgICBAc2VuZFByb2Nlc3M9InNlbmRQcm9jZXNzIgogICAgICBAd29ya1Nob3A9ImdldFdvcmtTaG9wIgogICAgICBAcmVmcmVzaD0iZmV0Y2hEYXRhIgogICAgICBAc2V0UHJvY2Vzc0xpc3Q9InNldFByb2Nlc3NMaXN0IgogICAgLz4KICA8L2VsLWRpYWxvZz4KCiAgPGVsLWRpYWxvZwogICAgOmtleT0iYWRkRHJhZnRLZXkiCiAgICB2LWRpYWxvZ0RyYWcKICAgIGNsYXNzPSJwbG0tY3VzdG9tLWRpYWxvZyIKICAgIDp0aXRsZT0idGl0bGUiCiAgICA6dmlzaWJsZS5zeW5jPSJvcGVuQWRkRHJhZnQiCiAgICA6d2lkdGg9ImRXaWR0aCIKICAgIHRvcD0iN3ZoIgogICAgQGNsb3NlPSJoYW5kbGVDbG9zZSIKICA+CiAgICA8YWRkLWRyYWZ0CiAgICAgIHYtaWY9Im9wZW5BZGREcmFmdCIKICAgICAgcmVmPSJkcmFmdCIKICAgICAgOmNvbS1uYW1lPSJjb21OYW1lIgogICAgICA6cGFydC1uYW1lPSJwYXJ0TmFtZSIKICAgICAgOmN1cnJlbnQtaWRzPSJjdXJyZW50SWRzIgogICAgICA6aXMtcGFydC1wcmVwYXJlPSJpc1BhcnRQcmVwYXJlIgogICAgICA6aW5zdGFsbC1pZD0iZm9ybUlubGluZS5JbnN0YWxsVW5pdF9JZCIKICAgICAgOnNjaGVkdWxlLWlkPSJzY2hlZHVsZUlkIgogICAgICA6c2hvdy1kaWFsb2c9Im9wZW5BZGREcmFmdCIKICAgICAgOnBhZ2UtdHlwZT0icGFnZVR5cGUiCiAgICAgIEBhZGRUb1RiTGlzdD0iYWRkVG9UYkxpc3QiCiAgICAgIEBzZW5kU2VsZWN0TGlzdD0ibWVyZ2VTZWxlY3RMaXN0IgogICAgICBAc2V0QWRkVGJLZXk9InNldEFkZFRiS2V5IgogICAgICBAY2xvc2U9ImhhbmRsZUNsb3NlIgogICAgLz4KICA8L2VsLWRpYWxvZz4KCiAgPGVsLWRyYXdlcgogICAgOnZpc2libGUuc3luYz0iZHJhd2VyIgogICAgZGlyZWN0aW9uPSJidHQiCiAgICBzaXplPSI2MCUiCiAgICBkZXN0cm95LW9uLWNsb3NlCiAgICA6YmVmb3JlLWNsb3NlPSJoYW5kbGVDbG9zZURyYXdlciIKICAgIEBvcGVuZWQ9InJlbmRlcklmcmFtZSIKICA+CiAgICA8ZGl2IHN0eWxlPSJ3aWR0aDogMTAwJTsgZGlzcGxheTogZmxleCI+CiAgICAgIDxkaXYgc3R5bGU9Im1hcmdpbi1sZWZ0OiAyMHB4Ij4KICAgICAgICA8c3BhbiBzdHlsZT0iZGlzcGxheTogaW5saW5lLWJsb2NrOyB3aWR0aDogMTAwcHgiPnt7IHBhcnROYW1lIH195Zu+57q4PC9zcGFuPgogICAgICA8L2Rpdj4KICAgICAgPGVsLWJ1dHRvbgogICAgICAgIHYtaWY9ImZpbGVCaW0iCiAgICAgICAgc3R5bGU9Im1hcmdpbi1sZWZ0OiA0MiUiCiAgICAgICAgQGNsaWNrPSJmdWxsc2NyZWVuKDEpIgogICAgICA+5YWo5bGPPC9lbC1idXR0b24+CiAgICA8L2Rpdj4KICAgIDxpZnJhbWUKICAgICAgaWQ9ImZyYW1lIgogICAgICA6a2V5PSJpZnJhbWVLZXkiCiAgICAgIDpzcmM9ImlmcmFtZVVybCIKICAgICAgc3R5bGU9IndpZHRoOiAxMDAlOyBib3JkZXI6IDBweDsgbWFyZ2luOiAwOyBoZWlnaHQ6IDYwdmgiCiAgICAvPgogIDwvZWwtZHJhd2VyPgo8L2Rpdj4K"}, null]}
<template>
  <el-dialog
    v-dialogDrag
    :title="isEdit?'编辑':'新增'"
    :visible.sync="dialogVisible"
    width="30%"
    class="plm-custom-dialog"
    @close="handleClose"
  >
    <div class="cs-wrap">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="工艺代码" prop="Code">
          <el-input v-model="form.Code" />
        </el-form-item>
        <el-form-item label="类型" prop="Bom_Level">
          <template>
            <el-radio-group v-model="form.Bom_Level" @change="radioChange">
              <!-- <el-radio :label="1">构件工艺</el-radio>
            <el-radio :label="3">部件工艺</el-radio>
            <el-radio :label="2">零件工艺</el-radio> -->
              <el-radio v-for="item in bomList" :key="item.Code" :label="item.Code">{{ item.Display_Name }}工艺</el-radio>
            </el-radio-group>
          </template>
        </el-form-item>
        <draggable v-model="list" handle=".icon-drag" @change="changeDraggable">
          <el-row v-for="(element,index) in list" :key="element.key" class="cs-row">
            <el-col class="cs-col" :span="2"> <i class="iconfont icon-drag cs-drag" /> </el-col>
            <el-col :span="19">
              <el-form-item :label="`工序${index+1}`" label-width="50px">
                <el-select :key="element.key" v-model="element.value" style="width:90%" :disabled="!form.Bom_Level" placeholder="请选择" clearable @change="selectChange($event,element)">
                  <el-option
                    v-for="item in options"
                    :key="item.Code"
                    :label="item.Name"
                    :disabled="item.disabled"
                    :value="item.Code"
                  >
                    <div class="cs-option">
                      <span class="cs-label">{{ item.Name }}</span>
                    </div>
                  </el-option>
                </el-select>

              </el-form-item>
            </el-col>
            <el-col class="cs-col2" :span="3">
              <span class="btn-x">
                <el-button v-if="index===0 && list.length<options.length" type="primary" icon="el-icon-plus" circle @click="handleAdd" />
                <el-button v-if="index!==0" type="danger" icon="el-icon-delete" circle @click="handleDelete(element)" />
              </span>
            </el-col>
          </el-row>

        </draggable>
        <el-form-item label="备注" prop="Remark">
          <el-input
            v-model="form.Remark"
            style="width: 90%"
            :autosize="{ minRows: 3, maxRows: 5}"
            show-word-limit
            :maxlength="50"
            type="textarea"
          />
        </el-form-item>
        <el-form-item label="产品类型" prop="Component_Type">
          <el-tree-select
            ref="treeSelectComponentType"
            v-model="searchComTypeSearch"
            style="width: 90%"
            placeholder="请选择"
            :select-params="treeSelectParams"
            class="cs-tree-x"
            :disabled="!form.Bom_Level"
            :tree-params="treeParamsComponentType"
            @searchFun="componentTypeFilter"
            @check="componentTypeChange"
          />
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" :loading="btnLoading" @click="submit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Draggable from 'vuedraggable'
import { AddProessLib, GetProcessFlow, GetProcessListBase } from '@/api/PRO/technology-lib'
import { v4 as uuidv4 } from 'uuid'
import { GetCompTypeTree } from '@/api/PRO/component-type'

import { GetAllEntities } from '@/api/PRO/settings'
import { GetPartTypeTree } from '@/api/PRO/partType'

export default {
  components: { Draggable },
  props: {
    bomList: {
      type: Array,
      default: () => []
    },
    sysProjectId: {
      type: String,
      default: undefined
    }
  },
  data() {
    return {
      dialogVisible: false,
      btnLoading: false,
      isEdit: false,
      searchComTypeSearch: [],
      productTypeList: [],
      form: {
        Component_Type_Codes: [],
        Component_Type_Ids: [],
        Code: '',
        Remark: '',
        Bom_Level: undefined
      },
      rules: {
        Code: [
          { required: true, message: '请输入 ', trigger: 'blur' }
        ],
        Bom_Level: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      },
      list: [],
      options: [],
      treeParamsComponentType: {
        'default-expand-all': true,
        'check-strictly': true,
        filterable: true,
        clickParent: true,
        collapseTags: true,
        multiple: false,
        data: [],
        props: {
          children: 'Children',
          label: 'Label',
          value: 'Data',
          disabled: 'Is_Disabled'
        }
      },
      treeSelectParams: {
        placeholder: '请选择',
        clearable: true
      }
    }
  },
  watch: {
    searchComTypeSearch: {
      handler(val) {
        if (!val.length) {
          this.form.Component_Type_Codes = []
          this.form.Component_Type_Ids = []
        }
      }
    }
  },
  async mounted() {
    this.getProfession()
  },
  methods: {
    async getProfession() {
      const res = await GetAllEntities({
        companyId: localStorage.getItem('Last_Working_Object_Id'),
        is_System: false
      })
      if (res.IsSucceed) {
        const {
          Code,
          Id
        } = res.Data?.Data?.find(item => item.Code === 'Steel') || {}
        this.typeCode = Code
        this.typeId = Id
        console.log(this.typeCode, this.typeId)
      }
    },
    // componentTypeNodeClick(node) {
    //   console.log(node)
    //   const { Data, Id } = node
    //   this.form.Component_Type_Codes = [Data]
    //   this.form.Component_Type_Ids = [Id]
    // },
    componentTypeChange(vv, c) {
      // console.log(999, vv, c)
      // const _ids = []
      // const _codes = []
      // const nodes = c.checkedNodes
      // console.log(11, nodes)
      // nodes.forEach((element, idx) => {
      //   if (this.form.Bom_Level === 1) {
      //     const { Data, Id } = element
      //     _ids.push(Id)
      //     _codes.push(Data)
      //   } else {
      //     const { Data, Id } = element
      //     _ids.push(Id)
      //     _codes.push(Data)
      //   }
      // })
      // console.log(_ids, _codes)
      // this.form.Component_Type_Codes = _codes
      // this.form.Component_Type_Ids = _ids
      // this.searchComTypeSearch = _ids
    },
    changeDraggable() {
    },
    init() {
      this.list = [{
        key: uuidv4(),
        value: '',
        id: ''
      }]
    },
    async getProductType() {
      let res
      if (this.form.Bom_Level === '-1') {
        res = await GetCompTypeTree({
          professional: this.typeCode,
          markAsOccupied: true,
          technologyId: this.technologyId
        })
      } else {
        res = await GetPartTypeTree({
          professionalId: this.typeId,
          markAsOccupied: true,
          technologyId: this.technologyId,
          partGrade: this.form.Bom_Level.toString()
        })
      }
      if (res.IsSucceed) {
        // this.setDisabledTree(tree)
        this.treeParamsComponentType.data = res.Data
        this.$nextTick(_ => {
            this.$refs.treeSelectComponentType?.treeDataUpdateFun(res.Data)
        })
      } else {
        this.$message({
          message: res.Message,
          type: 'error'
        })
      }
    },
    _searchFun(value) {
      this.$refs.treeSelectType.filterFun(value)
    },
    selectChange(val, item) {
      const arr = this.list.map(i => i.value)
      const idx = this.options.findIndex(v => v.Code === val)
      if (idx !== -1) {
        item.id = this.options[idx].Id
      }
      this.options.forEach((item, index) => {
        item.disabled = arr.includes(item.Code)
      })
    },
    handleOpen(row) {
      this.dialogVisible = true
      this.$nextTick(_ => {
        if (row && row.Id) {
          this.isEdit = true
          this.technologyId = row.Id
          this.form.Bom_Level = row.Bom_Level.toString()
          console.log('row', row)
          this.getInfo(row)
          if (row.Component_Type_Codes?.length) {
            this.searchComTypeSearch = row?.Component_Type_Codes || []
          } else {
            this.searchComTypeSearch = []
          }
          this.getProductType()
        } else {
          this.technologyId = undefined
          this.isEdit = false
          this.init()
          this.form.Bom_Level = undefined
          this.form.Code = ''
          this.form.Remark = ''
          this.searchComTypeSearch = []
        }
      })
    },
    handleClose() {
      this.$refs['form'].resetFields()
      this.dialogVisible = false
    },
    getInfo(row) {
      GetProcessFlow({
        technologyId: row.Id
      }).then(res => {
        if (res.IsSucceed) {
          const lr = res.Data.sort((a, b) => a.Step - b.Step)
          if (lr.length) {
            Object.assign(this.form, {
              Code: lr[0].Technology_Code,
              Remark: row.Remark,
              Id: row.Id
            })
            this.getProcessOption()
            this.list = lr.map(v => {
              return {
                key: uuidv4(),
                value: v.Process_code,
                id: v.Process_Id,
                tId: row.Id
              }
            })
          }
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    radioChange() {
      this.init()
      this.getProcessOption()
      this.getProductType()
      this.searchComTypeSearch = []
    },
    componentTypeFilter(e) {
      this.$refs?.treeSelectComponentType.filterFun(e)
    },
    handleAdd() {
      const arr = this.list.map(v => v.value)
      this.options.forEach(v => {
        if (arr.includes(v.Code)) {
          v.disabled = true
        }
      })
      this.list.push({
        key: uuidv4(),
        value: '',
        id: ''
      })
    },
    handleDelete(element) {
      const idx = this.list.findIndex(v => v.value === element.value)
      if (idx !== -1) {
        this.list.splice(idx, 1)
      }
    },
    handleCheckNode() {
      const _ids = []
      const _codes = []
      const nodes = this.$refs.treeSelectComponentType?.$refs?.tree.getCheckedNodes()
      nodes.forEach((element, idx) => {
        if (this.form.Bom_Level === 1) {
          const { Data, Id } = element
          _ids.push(Id)
          _codes.push(Data)
        } else {
          const { Data, Id } = element
          _ids.push(Id)
          _codes.push(Data)
        }
      })
      // console.log(_ids, _codes)
      // this.form.Component_Type_Codes = _codes
      // this.form.Component_Type_Ids = _ids
      // this.searchComTypeSearch = _ids
      return {
        _ids,
        _codes
      }
    },

    submit() {
      console.log(this.form, this.list)
      this.$refs['form'].validate((valid) => {
        if (!valid) return false
        const p = this.list.filter(v => v.value)
        if (!p.length) {
          this.$message({
            message: '请至少选择一个工序',
            type: 'error'
          })
          return
        }
        this.btnLoading = true
        if (!this.isEdit && this.form.Id) {
          delete this.form.Id
        }
        // const { Type, ...other } = this.form
        // if (this.form.Type === '-1') {
        //   other.Type = 1
        // } else if (this.form.Type === '0') {
        //   other.Type = 2
        // } else {
        //   other.Type = 3

        // }
        const { _ids, _codes } = this.handleCheckNode()

        const form = { ...this.form, Sys_Project_Id: this.sysProjectId }
        form.Component_Type_Codes = _codes
        form.Component_Type_Ids = _ids
        const submitObj = {
          TechnologyLib: form,
          ProcessFlow: p.map((v, idx) => {
            return {
              Technology_Id: v.tId || '',
              Process_Id: v.id,
              Step: idx + 1
            }
          })
        }
        AddProessLib(submitObj).then(res => {
          if (res.IsSucceed) {
            this.$message({
              message: '保存成功',
              type: 'success'
            })
            this.$emit('refresh')
            this.dialogVisible = false
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        }).finally(() => {
          this.btnLoading = false
        })
      })
    },
    getProcessOption() {
      if (!this.form.Bom_Level) return
      return new Promise((resolve, reject) => {
        this.pgLoading = true
        GetProcessListBase({
          bomLevel: this.form.Bom_Level
        }).then(res => {
          if (res.IsSucceed) {
            this.options = res.Data.filter(v => v.Is_Enable).map(v => {
              this.$set(v, 'disabled', false)
              return v
            })
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
          resolve()
        }).finally(_ => {
          this.pgLoading = false
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.cs-row{
  display: flex;
}
.cs-col{
  text-align: right;
  margin-top: 7px;
}
.cs-col2{
}
.cs-drag{
  cursor: pointer;
  display: inline-block;
}
.cs-wrap{
  max-height: 60vh;
  overflow-y: auto;
}
.cs-tree-x {
  ::v-deep {
    .el-select {
      width: 90%;
    }
  }
}
</style>

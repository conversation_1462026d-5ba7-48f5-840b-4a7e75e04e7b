{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-product-type\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-product-type\\index.vue", "mtime": 1757468128035}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON>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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/project-config/project-product-type", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <ProjectData />\r\n    <div class=\"card-x\">\r\n      <div class=\"card-x-top\">\r\n        <el-button type=\"primary\" @click=\"openAddDialog\">从项目添加</el-button>\r\n        <el-button type=\"primary\" @click=\"openCompanyDialog\">从公司添加</el-button>\r\n      </div>\r\n      <el-tabs v-model=\"activeType\" type=\"card\" @tab-click=\"handleClick\">\r\n        <el-tab-pane v-for=\"item in bomList\" :key=\"item.Code\" :label=\"item.Display_Name\" :name=\"item.Code\" />\r\n      </el-tabs>\r\n      <div class=\"card-x-content\">\r\n        <tree-data ref=\"tree\" :key=\"activeType\" :active-type=\"activeType\" :type-code=\"typeCode\" :type-id=\"typeId\" @nodeClick=\"nodeClick\" />\r\n        <div class=\"right-card\">\r\n          <el-form v-if=\"showForm\" ref=\"form\" :model=\"form\" label-width=\"120px\">\r\n            <el-form-item :label=\"`${levelName}大类名称：`\" prop=\"Name\">\r\n              <!-- <el-input v-model.trim=\"form.Name\" clearable maxlength=\"50\" /> -->\r\n              {{ form.Name }}\r\n            </el-form-item>\r\n            <el-form-item :label=\"`${levelName}大类编号：`\" prop=\"Code\">\r\n              <!-- <el-input v-model=\"form.Code\" disabled /> -->\r\n              {{ form.Code }}\r\n            </el-form-item>\r\n            <el-form-item label=\"生产周期：\" prop=\"Lead_Time\">\r\n              <!-- <el-input-number v-model.number=\"form.Lead_Time\" class=\"cs-number-btn-hidden w100\" clearable /> -->\r\n              {{ form.Lead_Time }}\r\n            </el-form-item>\r\n            <el-form-item v-if=\"showDirect\" label=\"直发件：\" prop=\"Is_Component\">\r\n              <!-- <el-radio-group v-model=\"form.Is_Component\">\r\n                  <el-radio :label=\"true\">否</el-radio>\r\n                  <el-radio :label=\"false\">是</el-radio>\r\n                </el-radio-group> -->\r\n              <el-tag :type=\"form.Is_Component ? 'danger' : 'success' \">\r\n                {{ form.Is_Component ? '否' : '是' }}\r\n              </el-tag>\r\n              <!-- {{ form.Is_Component }} -->\r\n            </el-form-item>\r\n            <!-- <el-form-item>\r\n              <el-button v-if=\"level<3\" type=\"text\" icon=\"el-icon-plus\" @click=\"addNext\">新增下一级</el-button>\r\n            </el-form-item>\r\n            <el-form-item>\r\n              <el-button type=\"primary\" :loading=\"submitLoading\" :disabled=\"isDefault\" @click=\"submit\">保存</el-button>\r\n              <el-button type=\"danger\" :loading=\"deleteLoading\" :disabled=\"hasChildrenNode || isDefault\" @click=\"handleDelete\">删除</el-button>\r\n            </el-form-item> -->\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"title\"\r\n      class=\"plm-custom-dialog\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"width\"\r\n      top=\"5vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        ref=\"content\"\r\n        :type-id=\"typeId\"\r\n        :add-level=\"addLevel\"\r\n        :parent-id=\"parentId\"\r\n        :active-type=\"activeType\"\r\n        :type-code=\"typeCode\"\r\n        :is-comp=\"isComp\"\r\n        :show-direct=\"showDirect\"\r\n        @close=\"handleClose\"\r\n        @getTreeList=\"getTreeData\"\r\n      />\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport TreeData from './component/TreeData'\r\nimport ProjectAdd from './component/ProjectAdd'\r\nimport { DeleteComponentType, GetComponentTypeEntity, SaveProBimComponentType } from '@/api/PRO/component-type'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\nimport { DeletePartType, GetPartTypeEntity, SavePartType } from '@/api/PRO/partType'\r\nimport { GetAllEntities } from '@/api/PRO/settings'\r\nimport ProjectData from '../components/ProjectData.vue'\r\nimport CompanyAdd from './component/CompanyAdd'\r\nexport default {\r\n  name: 'PROProjectProductType',\r\n  components: {\r\n    TreeData,\r\n    ProjectAdd,\r\n    ProjectData,\r\n    CompanyAdd\r\n  },\r\n  data() {\r\n    return {\r\n      bomList: [],\r\n      width: '30%',\r\n      typeCode: '',\r\n      typeId: '',\r\n      level: 1,\r\n      addLevel: undefined,\r\n      dialogVisible: false,\r\n      submitLoading: false,\r\n      deleteLoading: false,\r\n      showForm: false,\r\n      isDefault: false,\r\n      hasChildrenNode: true,\r\n      currentComponent: '',\r\n      activeType: '-1',\r\n      parentId: '',\r\n      title: '',\r\n      form: {\r\n        Name: '',\r\n        Code: '',\r\n        Is_Component: '',\r\n        Lead_Time: 0\r\n      },\r\n      // rules: {\r\n      //   Name: [\r\n      //     { required: true, message: '请输入名称', trigger: 'blur' }\r\n      //   ],\r\n      //   Code: [\r\n      //     { required: true, message: '请输入编码', trigger: 'blur' }\r\n      //   ],\r\n      //   Is_Component: [\r\n      //     { required: true, message: '请选择是否直发件', trigger: 'change' }\r\n      //   ],\r\n      //   Lead_Time: [\r\n      //     { required: true, message: '请输入周期', trigger: 'blur' }\r\n      //   ]\r\n      // },\r\n      Is_Component: ''\r\n    }\r\n  },\r\n  computed: {\r\n    levelName() {\r\n      return this.level === 1 ? '一级' : (this.level === 2 ? '二级' : (this.level === 3 ? '三级' : ''))\r\n    },\r\n    isComp() {\r\n      return this.activeType === '-1'\r\n    },\r\n    showDirect() {\r\n      return this.activeType !== '0'\r\n    }\r\n  },\r\n  async created() {\r\n    await this.getProfession()\r\n    const { list } = await GetBOMInfo()\r\n    this.bomList = list\r\n    // TreeData 组件会在自己的 mounted 中自动调用 fetchData()\r\n  },\r\n  methods: {\r\n    openAddDialog() {\r\n      this.currentComponent = 'ProjectAdd'\r\n      this.title = '添加'\r\n      this.dialogVisible = true\r\n      this.width = '80%'\r\n    },\r\n    openCompanyDialog() {\r\n      this.currentComponent = 'CompanyAdd'\r\n      this.title = '添加'\r\n      this.dialogVisible = true\r\n      this.width = '50%'\r\n    },\r\n    // addNext() {\r\n    //   this.currentComponent = 'Add'\r\n    //   this.addLevel = this.level + 1\r\n    //   this.title = `新增下一级`\r\n    //   this.parentId = this.form.Id\r\n    //   this.dialogVisible = true\r\n    // },\r\n    async getProfession() {\r\n      const res = await GetAllEntities({\r\n        companyId: localStorage.getItem('Last_Working_Object_Id'),\r\n        is_System: false\r\n      })\r\n      if (res.IsSucceed) {\r\n        const {\r\n          Code,\r\n          Id\r\n        } = res.Data?.Data?.find(item => item.Code === 'Steel') || {}\r\n        this.typeCode = Code\r\n        this.typeId = Id\r\n        console.log(this.typeCode, this.typeId)\r\n      }\r\n    },\r\n    // showRight(v) {\r\n    //   this.showForm = v\r\n    // },\r\n    handleClick(tab, event) {\r\n      this.showForm = false\r\n      console.log(tab, event)\r\n      // 由于使用了 key，组件会重新创建并在 mounted 中自动调用 fetchData()\r\n    },\r\n    submit() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (!valid) {\r\n          return false\r\n        }\r\n        if (this.Is_Component !== this.form.Is_Component) {\r\n          this.$confirm('直发件属性不会同步到已导入构件清单中，确认修改？', '提示', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n            this.submitConfirm()\r\n          }).catch(() => {\r\n            this.$message({\r\n              type: 'info',\r\n              message: '已取消修改'\r\n            })\r\n          })\r\n        } else {\r\n          this.submitConfirm()\r\n        }\r\n      })\r\n    },\r\n    submitConfirm() {\r\n      this.submitLoading = true\r\n      const submitObj = { ...this.form }\r\n      submitObj.Is_Direct = !submitObj.Is_Component\r\n      submitObj.Professional_Id = this.typeId\r\n      const postFn = this.isComp ? SaveProBimComponentType : SavePartType\r\n\r\n      postFn(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '修改成功',\r\n            type: 'success'\r\n          })\r\n          this.getTreeData()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.submitLoading = false\r\n      })\r\n    },\r\n    getTreeData() {\r\n      this.$refs['tree'].fetchData()\r\n    },\r\n\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    nodeClick(node) {\r\n      this.showForm = true\r\n      this.level = node.level\r\n      this.hasChildrenNode = node.childNodes.length > 0\r\n      this.getInfo(node.data.Id)\r\n    },\r\n    async getInfo(id) {\r\n      const postFn = this.isComp ? GetComponentTypeEntity : GetPartTypeEntity\r\n      const res = await postFn({ id })\r\n      if (res.IsSucceed) {\r\n        Object.assign(this.form, res.Data)\r\n        if (this.isComp) {\r\n          this.isDefault = false\r\n          this.Is_Component = res.Data.Is_Component\r\n        } else {\r\n          this.isDefault = !!res.Data.Is_Default\r\n          this.form.Is_Component = !res.Data.Is_Direct\r\n        }\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    handleDelete() {\r\n      this.$confirm('是否删除当前类别?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async() => {\r\n        this.deleteLoading = true\r\n        let postFn\r\n        let obj = {}\r\n        if (this.isComp) {\r\n          postFn = DeleteComponentType\r\n          obj = {\r\n            ids: this.form.Id\r\n          }\r\n        } else {\r\n          postFn = DeletePartType\r\n          obj = {\r\n            id: this.form.Id\r\n          }\r\n        }\r\n        postFn(obj).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.getTreeData()\r\n            this.$refs['tree'].resetKey(this.form.Id)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        }).finally(_ => {\r\n          this.deleteLoading = false\r\n          this.showForm = false\r\n        })\r\n      }).catch((e) => {\r\n        console.log(e, 3313)\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.app-container {\r\n  display: flex;\r\n  min-width: 998px;\r\n  overflow: hidden;\r\n\r\n  .top-x {\r\n    line-height: 48px;\r\n    height: 48px;\r\n  }\r\n  .card-x-top {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 16px 16px 0 16px;\r\n    background-color: #FFFFFF;\r\n  }\r\n\r\n  .card-x {\r\n    overflow: hidden;\r\n    // background-color: #FFFFFF;\r\n    height: 100%;\r\n    width: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    .el-tabs{\r\n      width: 100%;\r\n      padding: 16px 16px 0 16px;\r\n      background-color: #FFFFFF;\r\n    }\r\n    .card-x-content{\r\n      display: flex;\r\n      flex: 1;\r\n      overflow: hidden;\r\n    }\r\n\r\n    .right-card {\r\n      display: flex;\r\n      flex-direction: column;\r\n      flex: 1;\r\n      border-radius: 4px;\r\n      background-color: #FFFFFF;\r\n      .el-form{\r\n        width: 50%;\r\n        margin:  auto;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
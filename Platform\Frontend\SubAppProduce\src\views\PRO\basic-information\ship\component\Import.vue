<template>
  <div>
    <div class="box">
      <span>1.下载模板</span>
      <el-button size="large" @click="handleDownload">
        <svg-icon icon-class="document_form_icon" />
        船舶模板
      </el-button>
    </div>

    <div class="upload-box">
      <span style="margin-bottom: 20px;display: inline-block">
        2. 完善内容，重新上传。
      </span>
      <upload-excel ref="upload" :before-upload="beforeUpload" :limit="2" :on-change="handleChange" :file-list = "fileList"/>
    </div>

    <!--    <div class="box">
      <span style="display: inline-block;">3. 选择导入工厂</span>
      <el-select v-model="ProjectId" filterable clearable style="width: 70%" placeholder="请选择">
        <el-option
          v-for="item in proOption"
          :key="item.Id"
          :label="item.Name"
          :value="item.Id"
        />
      </el-select>
    </div>-->

    <footer class="cs-footer">
      <el-button @click="$emit('close')">取 消</el-button>
      <el-button type="primary" :loading="btnLoading" @click="handleSubmit">确 定</el-button>
    </footer>
  </div>
</template>
<script>
import UploadExcel from '@/components/UploadExcel'
import { ImportBoat, BoatDataTemplate } from '@/api/PRO/car'
import { combineURL } from '@/utils'
import { GetFactoryList } from '@/api/PRO/pro-schedules'

export default {
  name: 'Import',
  components: {
    UploadExcel
  },
  data() {
    return {
      options: [],
      btnLoading: false,
      // ProjectId: '',
      proOption: [],
      fileList:[]
    }
  },
  mounted() {

  },
  methods: {
    getFactory() {
      GetFactoryList({}).then(res => {
        this.proOption = res?.Data
      })
    },
    handleDownload() {
      BoatDataTemplate({}).then(res => {
        if (res.IsSucceed) {
          window.open(combineURL(this.$baseUrl, res.Data), '_blank')
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    beforeUpload(file) {
      console.log(file)
      const fileFormData = new FormData()
      // fileFormData.append('factoryId', this.ProjectId)
      fileFormData.append('files', file)
      this.btnLoading = true
      ImportBoat(fileFormData).then(res => {
        if (res.IsSucceed) {
          this.btnLoading = false
          this.$message({
            message: '导入成功',
            type: 'success'
          })
          this.$emit('refresh')
          this.$emit('close')
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
          this.btnLoading = false
        }
      })
    },
    handleSubmit() {
      /*      if (!this.ProjectId) {
        this.$message({
          message: '请选择工厂',
          type: 'info'
        })
        return
      }*/
      console.log(this.fileList)
      this.$refs.upload.handleSubmit()
    },
    handleChange(file, fileList) {
      console.log(file,fileList)
      // this.fileList = fileList.slice(-1);
      this.fileList = fileList;
      console.log(this.fileList)
      if(fileList.length>1) {
        this.fileList.shift()
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.box{
  border: 1px dashed #D9DBE2;
  padding: 0 16px;
  display: flex;
  height: 64px;
  justify-content: space-between;
  align-items: center;
  background-color: #F7F8F9;
  & ~ .box{
    margin-top: 20px;
  }

}
.upload-box{
  background-color:  #F7F8F9;
  border: 1px dashed #D9DBE2;
  margin-top: 16px;
  padding: 16px;
}
.cs-footer{
  margin-top: 10px;
  text-align: center;
}
</style>

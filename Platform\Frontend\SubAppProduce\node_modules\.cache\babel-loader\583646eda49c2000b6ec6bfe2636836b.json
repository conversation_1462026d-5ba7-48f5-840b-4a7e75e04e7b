{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckNode.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckNode.vue", "mtime": 1757919060941}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetGridByCode", "DelNode", "GetNodeList", "GetFactoryProfessionalByCode", "timeFormat", "props", "checkType", "type", "Object", "default", "data", "tbData", "columns", "tbLoading", "watch", "handler", "newName", "old<PERSON>ame", "getNodeList", "deep", "mounted", "getTypeList", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "_this$typeOption$", "wrap", "_callee$", "_context", "prev", "next", "factoryId", "localStorage", "getItem", "sent", "Data", "IsSucceed", "typeOption", "freeze", "console", "log", "length", "TypeId", "Id", "fetchData", "$message", "message", "Message", "stop", "getTableConfig", "_this2", "check_object_id", "Bom_Level", "Code", "then", "map", "v", "Check_Style", "Check_Type", "Create_Date", "code", "_this3", "find", "i", "error", "list", "ColumnList", "removeEvent", "row", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "id", "catch", "editEvent", "$emit"], "sources": ["src/views/PRO/factoryQuality/checkoutGroup/components/CheckNode.vue"], "sourcesContent": ["<template>\n  <div style=\"height: calc(100vh - 300px)\">\n    <vxe-table\n      v-loading=\"tbLoading\"\n      :empty-render=\"{name: 'NotData'}\"\n      show-header-overflow\n      element-loading-spinner=\"el-icon-loading\"\n      element-loading-text=\"拼命加载中\"\n      empty-text=\"暂无数据\"\n      height=\"100%\"\n      :data=\"tbData\"\n      stripe\n      resizable\n      :auto-resize=\"true\"\n      class=\"cs-vxe-table\"\n      :tooltip-config=\"{ enterable: true }\"\n    >\n      <!-- <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" /> -->\n      <vxe-column\n        v-for=\"(item, index) in columns\"\n        :key=\"index\"\n        :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n        show-overflow=\"tooltip\"\n        sortable\n        :align=\"item.Align\"\n        :field=\"item.Code\"\n        :title=\"item.Display_Name\"\n      >\n        <template #default=\"{ row }\">\n          <span v-if=\"item.Code === 'Is_Special_Check'\">\n            <el-tag v-if=\"row.Is_Special_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n          </span>\n          <span v-else-if=\"item.Code === 'Is_Inter_Check'\">\n            <el-tag v-if=\"row.Is_Inter_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n          </span>\n          <span v-else-if=\"item.Code === 'Is_Self_Check'\">\n            <el-tag v-if=\"row.Is_Self_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n          </span>\n          <span v-else>{{ row[item.Code] | displayValue }}</span>\n        </template>\n      </vxe-column>\n      <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" show-overflow align=\"center\">\n        <template #default=\"{ row }\">\n          <el-button v-if=\"!row.Node_Code||(row.Node_Code&&row.Check_Style === '抽检')\" type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\n          <el-divider v-if=\"!row.Node_Code\" direction=\"vertical\" />\n          <el-button v-if=\"!row.Node_Code\" type=\"text\" @click=\"removeEvent(row)\">删除</el-button>\n        </template>\n      </vxe-column>\n    </vxe-table>\n  </div>\n</template>\n\n<script>\nimport { GetGridByCode } from '@/api/sys'\nimport { DelNode } from '@/api/PRO/factorycheck'\nimport { GetNodeList } from '@/api/PRO/factorycheck'\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\nimport { timeFormat } from '@/filters'\nexport default {\n  props: {\n    checkType: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      tbData: [],\n      columns: [],\n      tbLoading: false\n    }\n  },\n  watch: {\n    checkType: {\n      handler(newName, oldName) {\n        this.checkType = newName\n        this.getNodeList()\n      },\n      deep: true\n    }\n  },\n  mounted() {\n    // this.getNodeList()\n    this.getTypeList()\n  },\n  methods: {\n    async getTypeList() {\n      const res = await GetFactoryProfessionalByCode({\n        factoryId: localStorage.getItem('CurReferenceId')\n      })\n      const data = res.Data\n      if (res.IsSucceed) {\n        this.typeOption = Object.freeze(data)\n        console.log(this.typeOption)\n        if (this.typeOption.length > 0) {\n          this.TypeId = this.typeOption[0]?.Id\n          this.fetchData()\n        }\n      } else {\n        this.$message({\n          message: res.Message,\n          type: 'error'\n        })\n      }\n    },\n    fetchData() {\n      this.getTableConfig('Quality_Inspection_Node')\n      //   this.tbLoading = true;\n    },\n    getNodeList() {\n      this.tbLoading = true\n      GetNodeList({ check_object_id: this.checkType.Id, Bom_Level: this.checkType.Code }).then((res) => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data.map(v => {\n            switch (v.Check_Style) {\n              case 0 : v.Check_Style = '抽检'; break // 谁写的，坑死了\n              case 1 : v.Check_Style = '全检'; break\n              default: v.Check_Style = ''\n            }\n            switch (v.Check_Type) {\n              case 1 : v.Check_Type = '质量'; break\n              case 2 : v.Check_Type = '探伤'; break\n              case -1 : v.Check_Type = '质量/探伤'; break\n              default: v.Check_Type = ''\n            }\n            v.Create_Date = timeFormat(v.Create_Date, '{y}-{m}-{d} {h}:{i}:{s}')\n            return v\n          })\n          console.log(res.Data)\n          this.tbLoading = false\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n          this.tbLoading = false\n        }\n      })\n    },\n    getTableConfig(code) {\n      GetGridByCode({ code: code + ',' + this.typeOption.find((i) => i.Id === this.TypeId).Code }).then((res) => {\n        const { IsSucceed, Data, Message } = res\n        if (IsSucceed) {\n          if (!Data) {\n            this.$message.error('当前专业没有配置相对应表格')\n            this.tbLoading = true\n            return\n          }\n          const list = Data.ColumnList || []\n          this.columns = list\n          console.log(this.columns)\n          this.tbLoading = false\n        } else {\n          this.$message({\n            message: Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    // 删除单个检查项组合\n    removeEvent(row) {\n      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DelNode({ id: row.Id }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getNodeList()\n            } else {\n              this.$message({\n                type: 'error',\n                message: res.Message\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    },\n\n    // 编辑每行信息\n    editEvent(row) {\n      // 获取每行内容\n      console.log('row', row)\n      this.$emit('NodeEdit', row)\n    }\n  }\n}\n</script>\n\n<style scoped></style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqDA,SAAAA,aAAA;AACA,SAAAC,OAAA;AACA,SAAAC,WAAA;AACA,SAAAC,4BAAA;AACA,SAAAC,UAAA;AACA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA;MACAC,OAAA;MACAC,SAAA;IACA;EACA;EACAC,KAAA;IACAR,SAAA;MACAS,OAAA,WAAAA,QAAAC,OAAA,EAAAC,OAAA;QACA,KAAAX,SAAA,GAAAU,OAAA;QACA,KAAAE,WAAA;MACA;MACAC,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACAD,WAAA,WAAAA,YAAA;MAAA,IAAAE,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA,EAAAlB,IAAA,EAAAmB,iBAAA;QAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACA/B,4BAAA;gBACAgC,SAAA,EAAAC,YAAA,CAAAC,OAAA;cACA;YAAA;cAFAT,GAAA,GAAAI,QAAA,CAAAM,IAAA;cAGA5B,IAAA,GAAAkB,GAAA,CAAAW,IAAA;cACA,IAAAX,GAAA,CAAAY,SAAA;gBACAjB,KAAA,CAAAkB,UAAA,GAAAjC,MAAA,CAAAkC,MAAA,CAAAhC,IAAA;gBACAiC,OAAA,CAAAC,GAAA,CAAArB,KAAA,CAAAkB,UAAA;gBACA,IAAAlB,KAAA,CAAAkB,UAAA,CAAAI,MAAA;kBACAtB,KAAA,CAAAuB,MAAA,IAAAjB,iBAAA,GAAAN,KAAA,CAAAkB,UAAA,iBAAAZ,iBAAA,uBAAAA,iBAAA,CAAAkB,EAAA;kBACAxB,KAAA,CAAAyB,SAAA;gBACA;cACA;gBACAzB,KAAA,CAAA0B,QAAA;kBACAC,OAAA,EAAAtB,GAAA,CAAAuB,OAAA;kBACA5C,IAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAyB,QAAA,CAAAoB,IAAA;UAAA;QAAA,GAAAzB,OAAA;MAAA;IACA;IACAqB,SAAA,WAAAA,UAAA;MACA,KAAAK,cAAA;MACA;IACA;IACAnC,WAAA,WAAAA,YAAA;MAAA,IAAAoC,MAAA;MACA,KAAAzC,SAAA;MACAX,WAAA;QAAAqD,eAAA,OAAAjD,SAAA,CAAAyC,EAAA;QAAAS,SAAA,OAAAlD,SAAA,CAAAmD;MAAA,GAAAC,IAAA,WAAA9B,GAAA;QACA,IAAAA,GAAA,CAAAY,SAAA;UACAc,MAAA,CAAA3C,MAAA,GAAAiB,GAAA,CAAAW,IAAA,CAAAoB,GAAA,WAAAC,CAAA;YACA,QAAAA,CAAA,CAAAC,WAAA;cACA;gBAAAD,CAAA,CAAAC,WAAA;gBAAA;cAAA;cACA;gBAAAD,CAAA,CAAAC,WAAA;gBAAA;cACA;gBAAAD,CAAA,CAAAC,WAAA;YACA;YACA,QAAAD,CAAA,CAAAE,UAAA;cACA;gBAAAF,CAAA,CAAAE,UAAA;gBAAA;cACA;gBAAAF,CAAA,CAAAE,UAAA;gBAAA;cACA;gBAAAF,CAAA,CAAAE,UAAA;gBAAA;cACA;gBAAAF,CAAA,CAAAE,UAAA;YACA;YACAF,CAAA,CAAAG,WAAA,GAAA3D,UAAA,CAAAwD,CAAA,CAAAG,WAAA;YACA,OAAAH,CAAA;UACA;UACAjB,OAAA,CAAAC,GAAA,CAAAhB,GAAA,CAAAW,IAAA;UACAe,MAAA,CAAAzC,SAAA;QACA;UACAyC,MAAA,CAAAL,QAAA;YACA1C,IAAA;YACA2C,OAAA,EAAAtB,GAAA,CAAAuB;UACA;UACAG,MAAA,CAAAzC,SAAA;QACA;MACA;IACA;IACAwC,cAAA,WAAAA,eAAAW,IAAA;MAAA,IAAAC,MAAA;MACAjE,aAAA;QAAAgE,IAAA,EAAAA,IAAA,cAAAvB,UAAA,CAAAyB,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAApB,EAAA,KAAAkB,MAAA,CAAAnB,MAAA;QAAA,GAAAW;MAAA,GAAAC,IAAA,WAAA9B,GAAA;QACA,IAAAY,SAAA,GAAAZ,GAAA,CAAAY,SAAA;UAAAD,IAAA,GAAAX,GAAA,CAAAW,IAAA;UAAAY,OAAA,GAAAvB,GAAA,CAAAuB,OAAA;QACA,IAAAX,SAAA;UACA,KAAAD,IAAA;YACA0B,MAAA,CAAAhB,QAAA,CAAAmB,KAAA;YACAH,MAAA,CAAApD,SAAA;YACA;UACA;UACA,IAAAwD,IAAA,GAAA9B,IAAA,CAAA+B,UAAA;UACAL,MAAA,CAAArD,OAAA,GAAAyD,IAAA;UACA1B,OAAA,CAAAC,GAAA,CAAAqB,MAAA,CAAArD,OAAA;UACAqD,MAAA,CAAApD,SAAA;QACA;UACAoD,MAAA,CAAAhB,QAAA;YACAC,OAAA,EAAAC,OAAA;YACA5C,IAAA;UACA;QACA;MACA;IACA;IACA;IACAgE,WAAA,WAAAA,YAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACArE,IAAA;MACA,GACAmD,IAAA;QACAzD,OAAA;UAAA4E,EAAA,EAAAL,GAAA,CAAAzB;QAAA,GAAAW,IAAA,WAAA9B,GAAA;UACA,IAAAA,GAAA,CAAAY,SAAA;YACAiC,MAAA,CAAAxB,QAAA;cACA1C,IAAA;cACA2C,OAAA;YACA;YACAuB,MAAA,CAAAvD,WAAA;UACA;YACAuD,MAAA,CAAAxB,QAAA;cACA1C,IAAA;cACA2C,OAAA,EAAAtB,GAAA,CAAAuB;YACA;UACA;QACA;MACA,GACA2B,KAAA;QACAL,MAAA,CAAAxB,QAAA;UACA1C,IAAA;UACA2C,OAAA;QACA;MACA;IACA;IAEA;IACA6B,SAAA,WAAAA,UAAAP,GAAA;MACA;MACA7B,OAAA,CAAAC,GAAA,QAAA4B,GAAA;MACA,KAAAQ,KAAA,aAAAR,GAAA;IACA;EACA;AACA", "ignoreList": []}]}
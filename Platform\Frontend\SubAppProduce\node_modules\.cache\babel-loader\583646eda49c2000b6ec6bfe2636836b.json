{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckNode.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckNode.vue", "mtime": 1758099078953}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetGridByCode", "DelNode", "GetNodeList", "GetFactoryProfessionalByCode", "timeFormat", "props", "checkType", "type", "Object", "default", "sysProjectId", "String", "data", "tbData", "columns", "tbLoading", "watch", "handler", "newName", "old<PERSON>ame", "getNodeList", "deep", "newVal", "Id", "immediate", "mounted", "getTypeList", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "_this$typeOption$", "wrap", "_callee$", "_context", "prev", "next", "factoryId", "localStorage", "getItem", "sent", "Data", "IsSucceed", "typeOption", "freeze", "console", "log", "length", "TypeId", "fetchData", "$message", "message", "Message", "stop", "getTableConfig", "_this2", "check_object_id", "Bom_Level", "Code", "then", "map", "v", "Check_Style", "Check_Type", "Create_Date", "code", "_this3", "find", "i", "error", "list", "ColumnList", "removeEvent", "row", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "id", "catch", "editEvent", "$emit"], "sources": ["src/views/PRO/project-config/project-quality/components/CheckNode.vue"], "sourcesContent": ["<template>\n  <div style=\"height: calc(100vh - 300px)\">\n    <vxe-table\n      v-loading=\"tbLoading\"\n      :empty-render=\"{name: 'NotData'}\"\n      show-header-overflow\n      element-loading-spinner=\"el-icon-loading\"\n      element-loading-text=\"拼命加载中\"\n      empty-text=\"暂无数据\"\n      height=\"100%\"\n      :data=\"tbData\"\n      stripe\n      resizable\n      :auto-resize=\"true\"\n      class=\"cs-vxe-table\"\n      :tooltip-config=\"{ enterable: true }\"\n    >\n      <!-- <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" /> -->\n      <vxe-column\n        v-for=\"(item, index) in columns\"\n        :key=\"index\"\n        :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n        show-overflow=\"tooltip\"\n        sortable\n        :align=\"item.Align\"\n        :field=\"item.Code\"\n        :title=\"item.Display_Name\"\n      >\n        <template #default=\"{ row }\">\n          <span v-if=\"item.Code === 'Is_Special_Check'\">\n            <el-tag v-if=\"row.Is_Special_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n          </span>\n          <span v-else-if=\"item.Code === 'Is_Inter_Check'\">\n            <el-tag v-if=\"row.Is_Inter_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n          </span>\n          <span v-else-if=\"item.Code === 'Is_Self_Check'\">\n            <el-tag v-if=\"row.Is_Self_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n          </span>\n          <span v-else>{{ row[item.Code] | displayValue }}</span>\n        </template>\n      </vxe-column>\n      <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" show-overflow align=\"center\">\n        <template #default=\"{ row }\">\n          <el-button type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\n          <!-- <el-button v-if=\"!row.Node_Code||(row.Node_Code&&row.Check_Style === '抽检')\" type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\n          <el-divider v-if=\"!row.Node_Code\" direction=\"vertical\" />\n          <el-button v-if=\"!row.Node_Code\" type=\"text\" @click=\"removeEvent(row)\">删除</el-button> -->\n        </template>\n      </vxe-column>\n    </vxe-table>\n  </div>\n</template>\n\n<script>\nimport { GetGridByCode } from '@/api/sys'\nimport { DelNode } from '@/api/PRO/factorycheck'\nimport { GetNodeList } from '@/api/PRO/factorycheck'\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\nimport { timeFormat } from '@/filters'\nexport default {\n  props: {\n    checkType: {\n      type: Object,\n      default: () => ({})\n    },\n    sysProjectId: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      tbData: [],\n      columns: [],\n      tbLoading: false\n    }\n  },\n  watch: {\n    checkType: {\n      handler(newName, oldName) {\n        this.checkType = newName\n        if (this.sysProjectId) {\n          this.getNodeList()\n        }\n      },\n      deep: true\n    },\n    sysProjectId: {\n      handler(newVal) {\n        if (newVal && this.checkType && this.checkType.Id) {\n          this.getNodeList()\n        }\n      },\n      immediate: true\n    }\n  },\n  mounted() {\n    // this.getNodeList()\n    this.getTypeList()\n  },\n  methods: {\n    async getTypeList() {\n      const res = await GetFactoryProfessionalByCode({\n        factoryId: localStorage.getItem('CurReferenceId')\n      })\n      const data = res.Data\n      if (res.IsSucceed) {\n        this.typeOption = Object.freeze(data)\n        console.log(this.typeOption)\n        if (this.typeOption.length > 0) {\n          this.TypeId = this.typeOption[0]?.Id\n          this.fetchData()\n        }\n      } else {\n        this.$message({\n          message: res.Message,\n          type: 'error'\n        })\n      }\n    },\n    fetchData() {\n      this.getTableConfig('Quality_Inspection_Node')\n      //   this.tbLoading = true;\n    },\n    getNodeList() {\n      this.tbLoading = true\n      GetNodeList({ check_object_id: this.checkType.Id, Bom_Level: this.checkType.Code, sysProjectId: this.sysProjectId }).then((res) => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data.map(v => {\n            switch (v.Check_Style) {\n              case 0 : v.Check_Style = '抽检'; break // 谁写的，坑死了\n              case 1 : v.Check_Style = '全检'; break\n              default: v.Check_Style = ''\n            }\n            switch (v.Check_Type) {\n              case 1 : v.Check_Type = '质量'; break\n              case 2 : v.Check_Type = '探伤'; break\n              case -1 : v.Check_Type = '质量/探伤'; break\n              default: v.Check_Type = ''\n            }\n            v.Create_Date = timeFormat(v.Create_Date, '{y}-{m}-{d} {h}:{i}:{s}')\n            return v\n          })\n          console.log(res.Data)\n          this.tbLoading = false\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n          this.tbLoading = false\n        }\n      })\n    },\n    getTableConfig(code) {\n      GetGridByCode({ code: code + ',' + this.typeOption.find((i) => i.Id === this.TypeId).Code }).then((res) => {\n        const { IsSucceed, Data, Message } = res\n        if (IsSucceed) {\n          if (!Data) {\n            this.$message.error('当前专业没有配置相对应表格')\n            this.tbLoading = true\n            return\n          }\n          const list = Data.ColumnList || []\n          this.columns = list\n          console.log(this.columns)\n          this.tbLoading = false\n        } else {\n          this.$message({\n            message: Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    // 删除单个检查项组合\n    removeEvent(row) {\n      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DelNode({ id: row.Id }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getNodeList()\n            } else {\n              this.$message({\n                type: 'error',\n                message: res.Message\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    },\n\n    // 编辑每行信息\n    editEvent(row) {\n      // 获取每行内容\n      console.log('row', row)\n      this.$emit('NodeEdit', row)\n    }\n  }\n}\n</script>\n\n<style scoped></style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA,SAAAA,aAAA;AACA,SAAAC,OAAA;AACA,SAAAC,WAAA;AACA,SAAAC,4BAAA;AACA,SAAAC,UAAA;AACA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAC,YAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA;MACAC,OAAA;MACAC,SAAA;IACA;EACA;EACAC,KAAA;IACAV,SAAA;MACAW,OAAA,WAAAA,QAAAC,OAAA,EAAAC,OAAA;QACA,KAAAb,SAAA,GAAAY,OAAA;QACA,SAAAR,YAAA;UACA,KAAAU,WAAA;QACA;MACA;MACAC,IAAA;IACA;IACAX,YAAA;MACAO,OAAA,WAAAA,QAAAK,MAAA;QACA,IAAAA,MAAA,SAAAhB,SAAA,SAAAA,SAAA,CAAAiB,EAAA;UACA,KAAAH,WAAA;QACA;MACA;MACAI,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACAD,WAAA,WAAAA,YAAA;MAAA,IAAAE,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA,EAAArB,IAAA,EAAAsB,iBAAA;QAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACApC,4BAAA;gBACAqC,SAAA,EAAAC,YAAA,CAAAC,OAAA;cACA;YAAA;cAFAT,GAAA,GAAAI,QAAA,CAAAM,IAAA;cAGA/B,IAAA,GAAAqB,GAAA,CAAAW,IAAA;cACA,IAAAX,GAAA,CAAAY,SAAA;gBACAjB,KAAA,CAAAkB,UAAA,GAAAtC,MAAA,CAAAuC,MAAA,CAAAnC,IAAA;gBACAoC,OAAA,CAAAC,GAAA,CAAArB,KAAA,CAAAkB,UAAA;gBACA,IAAAlB,KAAA,CAAAkB,UAAA,CAAAI,MAAA;kBACAtB,KAAA,CAAAuB,MAAA,IAAAjB,iBAAA,GAAAN,KAAA,CAAAkB,UAAA,iBAAAZ,iBAAA,uBAAAA,iBAAA,CAAAX,EAAA;kBACAK,KAAA,CAAAwB,SAAA;gBACA;cACA;gBACAxB,KAAA,CAAAyB,QAAA;kBACAC,OAAA,EAAArB,GAAA,CAAAsB,OAAA;kBACAhD,IAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA8B,QAAA,CAAAmB,IAAA;UAAA;QAAA,GAAAxB,OAAA;MAAA;IACA;IACAoB,SAAA,WAAAA,UAAA;MACA,KAAAK,cAAA;MACA;IACA;IACArC,WAAA,WAAAA,YAAA;MAAA,IAAAsC,MAAA;MACA,KAAA3C,SAAA;MACAb,WAAA;QAAAyD,eAAA,OAAArD,SAAA,CAAAiB,EAAA;QAAAqC,SAAA,OAAAtD,SAAA,CAAAuD,IAAA;QAAAnD,YAAA,OAAAA;MAAA,GAAAoD,IAAA,WAAA7B,GAAA;QACA,IAAAA,GAAA,CAAAY,SAAA;UACAa,MAAA,CAAA7C,MAAA,GAAAoB,GAAA,CAAAW,IAAA,CAAAmB,GAAA,WAAAC,CAAA;YACA,QAAAA,CAAA,CAAAC,WAAA;cACA;gBAAAD,CAAA,CAAAC,WAAA;gBAAA;cAAA;cACA;gBAAAD,CAAA,CAAAC,WAAA;gBAAA;cACA;gBAAAD,CAAA,CAAAC,WAAA;YACA;YACA,QAAAD,CAAA,CAAAE,UAAA;cACA;gBAAAF,CAAA,CAAAE,UAAA;gBAAA;cACA;gBAAAF,CAAA,CAAAE,UAAA;gBAAA;cACA;gBAAAF,CAAA,CAAAE,UAAA;gBAAA;cACA;gBAAAF,CAAA,CAAAE,UAAA;YACA;YACAF,CAAA,CAAAG,WAAA,GAAA/D,UAAA,CAAA4D,CAAA,CAAAG,WAAA;YACA,OAAAH,CAAA;UACA;UACAhB,OAAA,CAAAC,GAAA,CAAAhB,GAAA,CAAAW,IAAA;UACAc,MAAA,CAAA3C,SAAA;QACA;UACA2C,MAAA,CAAAL,QAAA;YACA9C,IAAA;YACA+C,OAAA,EAAArB,GAAA,CAAAsB;UACA;UACAG,MAAA,CAAA3C,SAAA;QACA;MACA;IACA;IACA0C,cAAA,WAAAA,eAAAW,IAAA;MAAA,IAAAC,MAAA;MACArE,aAAA;QAAAoE,IAAA,EAAAA,IAAA,cAAAtB,UAAA,CAAAwB,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAhD,EAAA,KAAA8C,MAAA,CAAAlB,MAAA;QAAA,GAAAU;MAAA,GAAAC,IAAA,WAAA7B,GAAA;QACA,IAAAY,SAAA,GAAAZ,GAAA,CAAAY,SAAA;UAAAD,IAAA,GAAAX,GAAA,CAAAW,IAAA;UAAAW,OAAA,GAAAtB,GAAA,CAAAsB,OAAA;QACA,IAAAV,SAAA;UACA,KAAAD,IAAA;YACAyB,MAAA,CAAAhB,QAAA,CAAAmB,KAAA;YACAH,MAAA,CAAAtD,SAAA;YACA;UACA;UACA,IAAA0D,IAAA,GAAA7B,IAAA,CAAA8B,UAAA;UACAL,MAAA,CAAAvD,OAAA,GAAA2D,IAAA;UACAzB,OAAA,CAAAC,GAAA,CAAAoB,MAAA,CAAAvD,OAAA;UACAuD,MAAA,CAAAtD,SAAA;QACA;UACAsD,MAAA,CAAAhB,QAAA;YACAC,OAAA,EAAAC,OAAA;YACAhD,IAAA;UACA;QACA;MACA;IACA;IACA;IACAoE,WAAA,WAAAA,YAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAzE,IAAA;MACA,GACAuD,IAAA;QACA7D,OAAA;UAAAgF,EAAA,EAAAL,GAAA,CAAArD;QAAA,GAAAuC,IAAA,WAAA7B,GAAA;UACA,IAAAA,GAAA,CAAAY,SAAA;YACAgC,MAAA,CAAAxB,QAAA;cACA9C,IAAA;cACA+C,OAAA;YACA;YACAuB,MAAA,CAAAzD,WAAA;UACA;YACAyD,MAAA,CAAAxB,QAAA;cACA9C,IAAA;cACA+C,OAAA,EAAArB,GAAA,CAAAsB;YACA;UACA;QACA;MACA,GACA2B,KAAA;QACAL,MAAA,CAAAxB,QAAA;UACA9C,IAAA;UACA+C,OAAA;QACA;MACA;IACA;IAEA;IACA6B,SAAA,WAAAA,UAAAP,GAAA;MACA;MACA5B,OAAA,CAAAC,GAAA,QAAA2B,GAAA;MACA,KAAAQ,KAAA,aAAAR,GAAA;IACA;EACA;AACA", "ignoreList": []}]}
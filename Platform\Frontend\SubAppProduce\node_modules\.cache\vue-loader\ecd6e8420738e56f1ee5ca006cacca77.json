{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\component\\Add.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\component\\Add.vue", "mtime": 1756109946517}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Add.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAyBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Add.vue", "sourceRoot": "src/views/PRO/bom-setting/structure-type-config/component", "sourcesContent": ["<template>\r\n  <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n    <el-form-item :label=\"`${levelName}大类名称`\" prop=\"Name\">\r\n      <el-input v-model.trim=\"form.Name\" clearable maxlength=\"50\" />\r\n    </el-form-item>\r\n    <el-form-item :label=\"`${levelName}大类编号`\" prop=\"Code\">\r\n      <el-input v-model.trim=\"form.Code\" clearable maxlength=\"50\" />\r\n    </el-form-item>\r\n    <el-form-item label=\"生产周期\" prop=\"Lead_Time\">\r\n      <el-input-number v-model.number=\"form.Lead_Time\" class=\"cs-number-btn-hidden w100\" clearable />\r\n    </el-form-item>\r\n    <el-form-item v-if=\"isComp\" label=\"直发件\" prop=\"Is_Component\">\r\n      <el-radio-group v-model=\"form.Is_Component\">\r\n        <el-radio :label=\"true\">否</el-radio>\r\n        <el-radio :label=\"false\">是</el-radio>\r\n      </el-radio-group>\r\n    </el-form-item>\r\n    <el-form-item style=\"text-align: right\">\r\n      <el-button @click=\"$emit('close')\">取消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"submit\">保存</el-button>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n\r\n<script>\r\nimport { SaveProBimComponentType } from '@/api/PRO/component-type'\r\nimport { SavePartType } from '@/api/PRO/partType'\r\n\r\nexport default {\r\n  props: {\r\n    addLevel: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    parentId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    // true=构件；false=零件\r\n    isComp: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      form: {\r\n        Code: '',\r\n        Name: '',\r\n        Is_Component: '',\r\n        Lead_Time: 0\r\n      },\r\n      rules: {\r\n        Name: [\r\n          { required: true, message: '请输入名称', trigger: 'blur' }\r\n        ],\r\n        Code: [\r\n          { required: true, message: '请输入编码', trigger: 'blur' }\r\n        ],\r\n        Is_Component: [\r\n          { required: true, message: '请选择是否直发件', trigger: 'change' }\r\n        ],\r\n        Lead_Time: [\r\n          { required: true, message: '请输入周期', trigger: 'blur' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    levelName() {\r\n      return this.addLevel === 1 ? '一级' : (this.addLevel === 2 ? '二级' : (this.addLevel === 3 ? '三级' : ''))\r\n    }\r\n  },\r\n  watch: {\r\n    // 零件没有直发件字段，不需要必填\r\n    isComp(value) {\r\n      this.rules.Is_Component[0].required = value\r\n    }\r\n  },\r\n  methods: {\r\n    submit() {\r\n      this.$refs['form'].validate(async(valid) => {\r\n        if (!valid) {\r\n          return false\r\n        }\r\n        this.form.Category = this.$route.query.typeCode\r\n        this.form.Professional_Id = this.$route.query.typeId\r\n        this.form.Level = this.addLevel\r\n        if (this.addLevel > 1) {\r\n          this.form.Parent_Id = this.parentId\r\n        }\r\n        this.btnLoading = true\r\n        const postFN = this.isComp ? SaveProBimComponentType : SavePartType\r\n        const res = await postFN(this.form)\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '操作成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('close')\r\n          this.$emit('getTreeList')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.btnLoading = false\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\component\\Add.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\component\\Add.vue", "mtime": 1757468112469}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Add.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAyBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Add.vue", "sourceRoot": "src/views/PRO/bom-setting/structure-type-config/component", "sourcesContent": ["<template>\r\n  <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n    <el-form-item :label=\"`${levelName}大类名称`\" prop=\"Name\">\r\n      <el-input v-model.trim=\"form.Name\" clearable maxlength=\"50\" />\r\n    </el-form-item>\r\n    <el-form-item :label=\"`${levelName}大类编号`\" prop=\"Code\">\r\n      <el-input v-model.trim=\"form.Code\" clearable maxlength=\"50\" />\r\n    </el-form-item>\r\n    <el-form-item label=\"生产周期\" prop=\"Lead_Time\">\r\n      <el-input-number v-model.number=\"form.Lead_Time\" class=\"cs-number-btn-hidden w100\" clearable />\r\n    </el-form-item>\r\n    <el-form-item v-if=\"showDirect\" label=\"直发件\" prop=\"Is_Component\">\r\n      <el-radio-group v-model=\"form.Is_Component\">\r\n        <el-radio :label=\"true\">否</el-radio>\r\n        <el-radio :label=\"false\">是</el-radio>\r\n      </el-radio-group>\r\n    </el-form-item>\r\n    <el-form-item style=\"text-align: right\">\r\n      <el-button @click=\"$emit('close')\">取消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"submit\">保存</el-button>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n\r\n<script>\r\nimport { SaveProBimComponentType } from '@/api/PRO/component-type'\r\nimport { SavePartType } from '@/api/PRO/partType'\r\n\r\nexport default {\r\n  props: {\r\n    addLevel: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    typeId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    parentId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    activeType: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    typeCode: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    // true=构件；false=零件\r\n    isComp: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    showDirect: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      form: {\r\n        Code: '',\r\n        Name: '',\r\n        Is_Component: '',\r\n        Lead_Time: 0\r\n      },\r\n      rules: {\r\n        Name: [\r\n          { required: true, message: '请输入名称', trigger: 'blur' }\r\n        ],\r\n        Code: [\r\n          { required: true, message: '请输入编码', trigger: 'blur' }\r\n        ],\r\n        Is_Component: [\r\n          { required: true, message: '请选择是否直发件', trigger: 'change' }\r\n        ],\r\n        Lead_Time: [\r\n          { required: true, message: '请输入周期', trigger: 'blur' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    levelName() {\r\n      return this.addLevel === 1 ? '一级' : (this.addLevel === 2 ? '二级' : (this.addLevel === 3 ? '三级' : ''))\r\n    }\r\n  },\r\n  methods: {\r\n    submit() {\r\n      this.$refs['form'].validate(async(valid) => {\r\n        if (!valid) {\r\n          return false\r\n        }\r\n        this.form.Category = this.typeCode\r\n        this.form.Professional_Id = this.typeId\r\n        this.form.Level = this.addLevel\r\n        if (this.addLevel > 1) {\r\n          this.form.Parent_Id = this.parentId\r\n        }\r\n        this.btnLoading = true\r\n        let postFN\r\n        const submitObj = { ...this.form }\r\n        if (this.isComp) {\r\n          postFN = SaveProBimComponentType\r\n        } else {\r\n          submitObj.Part_Grade = this.activeType.toString()\r\n          submitObj.Is_Direct = !this.form.Is_Component\r\n          postFN = SavePartType\r\n        }\r\n        console.log(submitObj, 'submitObj')\r\n        const res = await postFN(submitObj)\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '操作成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('close')\r\n          this.$emit('getTreeList')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.btnLoading = false\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"]}]}
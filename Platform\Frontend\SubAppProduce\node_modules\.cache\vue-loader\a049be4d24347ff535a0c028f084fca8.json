{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-unit-part\\components\\addDraft.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-unit-part\\components\\addDraft.vue", "mtime": 1758266753117}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRHcmlkQnlDb2RlIH0gZnJvbSAnQC9hcGkvc3lzJw0KaW1wb3J0IHsgR2V0Q2FuU2NoZHVsaW5nQ29tcHMgfSBmcm9tICdAL2FwaS9QUk8vcHJvZHVjdGlvbi10YXNrJw0KaW1wb3J0IHsgR2V0Q2FuU2NoZHVsaW5nVW5pdHMgfSBmcm9tICdAL2FwaS9QUk8vcHJvZHVjdGlvbi1wYXJ0Jw0KaW1wb3J0IHsgdjQgYXMgdXVpZHY0IH0gZnJvbSAndXVpZCcNCmltcG9ydCB7IGRlYm91bmNlLCBkZWVwQ2xvbmUgfSBmcm9tICdAL3V0aWxzJw0KaW1wb3J0IHsgdGFibGVQYWdlU2l6ZSB9IGZyb20gJ0Avdmlld3MvUFJPL3NldHRpbmcnDQppbXBvcnQgeyBHZXRDb21wVHlwZVRyZWUgfSBmcm9tICdAL2FwaS9QUk8vcHJvZmVzc2lvbmFsVHlwZScNCmltcG9ydCB7IEdldFBhcnRUeXBlTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9wYXJ0VHlwZScNCmltcG9ydCBUcmVlRGV0YWlsIGZyb20gJ0AvY29tcG9uZW50cy9UcmVlRGV0YWlsL2luZGV4LnZ1ZScNCmltcG9ydCBFeHBhbmRhYmxlU2VjdGlvbiBmcm9tICdAL2NvbXBvbmVudHMvRXhwYW5kYWJsZVNlY3Rpb24vaW5kZXgudnVlJw0KaW1wb3J0IHsgR2V0SW5zdGFsbFVuaXRJZE5hbWVMaXN0LCBHZXRQcm9qZWN0QXJlYVRyZWVMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL3Byb2plY3QnDQppbXBvcnQgeyBnZXRVbmlxdWUgfSBmcm9tICcuLi9jb25zdGFudCcNCmltcG9ydCB7IG1hcEdldHRlcnMgfSBmcm9tICd2dWV4Jw0KaW1wb3J0IHsgZmluZEFsbFBhcmVudE5vZGUgfSBmcm9tICdAL3V0aWxzL3RyZWUnDQppbXBvcnQgeyBHZXRTdG9wTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9wcm9kdWN0aW9uLXRhc2snDQpjb25zdCBTUExJVF9TWU1CT0wgPSAnJF8kJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIGNvbXBvbmVudHM6IHsgRXhwYW5kYWJsZVNlY3Rpb24sIFRyZWVEZXRhaWwgfSwNCiAgcHJvcHM6IHsNCiAgICBzY2hlZHVsZUlkOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnJw0KICAgIH0sDQogICAgbGV2ZWxOYW1lOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnJw0KICAgIH0sDQogICAgcGFnZVR5cGU6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICdjb20nDQogICAgfSwNCiAgICBzaG93RGlhbG9nOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UNCiAgICB9LA0KDQogICAgaW5zdGFsbElkOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnJw0KICAgIH0sDQogICAgY3VycmVudElkczogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJycNCiAgICB9LA0KDQogICAgaXNQYXJ0UHJlcGFyZTogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBwYWdlSW5mbzogew0KICAgICAgICBwYWdlOiAxLA0KICAgICAgICBwYWdlU2l6ZTogNTAwLA0KICAgICAgICBwYWdlU2l6ZXM6IHRhYmxlUGFnZVNpemUsDQogICAgICAgIHRvdGFsOiAwDQogICAgICB9LA0KICAgICAgZm9ybTogew0KICAgICAgICBDb21wX0NvZGU6ICcnLA0KICAgICAgICBDb21wX0NvZGVCbHVyOiAnJywNCiAgICAgICAgUGFydF9Db2RlQmx1cjogJycsDQogICAgICAgIFBhcnRfQ29kZTogJycsDQogICAgICAgIEluc3RhbGxVbml0X0lkOiBbXSwNCiAgICAgICAgU3BlYzogJycsDQogICAgICAgIFR5cGU6ICcnDQogICAgICB9LA0KICAgICAgY3VyU2VhcmNoOiAxLA0KICAgICAgY3VyUGFydFNlYXJjaDogMSwNCiAgICAgIHNob3dFeHBhbmQ6IHRydWUsDQogICAgICBzZWFyY2hDb250ZW50OiAnJywNCiAgICAgIHNlYXJjaFBhcnRDb250ZW50OiAnJywNCiAgICAgIHN0YXR1c1R5cGU6ICcnLA0KICAgICAgcHJvamVjdE5hbWU6ICcnLA0KICAgICAgZXhwYW5kZWRLZXk6ICcnLA0KICAgICAgc3RhdHVzQ29kZTogJ1BhcnRfU2NoZHVsZV9TdGF0dXMnLA0KICAgICAgaXNPd25lck51bGw6IHRydWUsDQogICAgICB0YkxvYWRpbmc6IGZhbHNlLA0KICAgICAgdHJlZUxvYWRpbmc6IGZhbHNlLA0KICAgICAgYWRkTG9hZGluZzogZmFsc2UsDQogICAgICBzYXZlTG9hZGluZzogZmFsc2UsDQogICAgICBzaG93U2M6IGZhbHNlLA0KICAgICAgaW5zdGFsbFVuaXRJZExpc3Q6IFtdLA0KICAgICAgY29sdW1uczogW10sDQogICAgICBmVGFibGU6IFtdLA0KICAgICAgdGJDb25maWc6IHt9LA0KICAgICAgVG90YWxDb3VudDogMCwNCiAgICAgIFBhZ2U6IDAsDQogICAgICB0b3RhbFNlbGVjdGlvbjogW10sDQogICAgICB0cmVlRGF0YTogW10sDQogICAgICBzZWFyY2g6ICgpID0+ICh7fSksDQogICAgICB0cmVlU2VsZWN0UGFyYW1zOiB7DQogICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36YCJ5oupJywNCiAgICAgICAgY2xlYXJhYmxlOiB0cnVlDQogICAgICB9LA0KICAgICAgT2JqZWN0VHlwZUxpc3Q6IHsNCiAgICAgICAgLy8g5p6E5Lu257G75Z6LDQogICAgICAgICdjaGVjay1zdHJpY3RseSc6IHRydWUsDQogICAgICAgICdkZWZhdWx0LWV4cGFuZC1hbGwnOiB0cnVlLA0KICAgICAgICBjbGlja1BhcmVudDogdHJ1ZSwNCiAgICAgICAgZGF0YTogW10sDQogICAgICAgIHByb3BzOiB7DQogICAgICAgICAgY2hpbGRyZW46ICdDaGlsZHJlbicsDQogICAgICAgICAgbGFiZWw6ICdMYWJlbCcsDQogICAgICAgICAgdmFsdWU6ICdEYXRhJw0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgYXJlYUlkOiAnJw0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICBpc0NvbSgpIHsNCiAgICAgIHJldHVybiB0aGlzLnBhZ2VUeXBlID09PSAnY29tJw0KICAgIH0sDQogICAgZmlsdGVyVGV4dCgpIHsNCiAgICAgIHJldHVybiB0aGlzLnByb2plY3ROYW1lICsgU1BMSVRfU1lNQk9MICsgdGhpcy5zdGF0dXNUeXBlDQogICAgfSwNCiAgICAuLi5tYXBHZXR0ZXJzKCdzY2hlZHVsZScsIFsnYWRkVGJLZXlzJ10pDQogIH0sDQogIHdhdGNoOiB7DQogICAgc2hvd0RpYWxvZyhuZXdWYWx1ZSkgew0KICAgICAgbmV3VmFsdWUgJiYgKHRoaXMuc2F2ZUxvYWRpbmcgPSBmYWxzZSkNCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQoNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGluaXREYXRhKCkgew0KICAgICAgY29uc29sZS5sb2coJ2luaXREYXRhJykNCiAgICAgIHRoaXMudGJEYXRhID0gW10NCiAgICAgIHRoaXMuZ2V0Q29uZmlnKCkNCiAgICAgIHRoaXMuZmV0Y2hUcmVlRGF0YSgpDQogICAgICB0aGlzLnNlYXJjaCA9IGRlYm91bmNlKHRoaXMuZmV0Y2hEYXRhLCA4MDAsIHRydWUpDQogICAgICB0aGlzLnNldFBhZ2VEYXRhKCkNCiAgICB9LA0KICAgIGhhbmRsZU5vZGVDbGljayhkYXRhKSB7DQogICAgICBpZiAodGhpcy5hcmVhSWQgPT09IGRhdGEuSWQpIHsNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICBpZiAoIWRhdGEuUGFyZW50Tm9kZXMgfHwgZGF0YS5DaGlsZHJlbj8ubGVuZ3RoID4gMCkgew0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIGlmIChkYXRhPy5EYXRhW3RoaXMuc3RhdHVzQ29kZV0gPT09ICfmnKrlr7zlhaUnKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICfmuIXljZXmnKrlr7zlhaXvvIzor7fogZTns7vmt7HljJbkurrlkZjlr7zlhaXmuIXljZUnLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KQ0KICAgICAgICB0aGlzLmV4cGFuZGVkS2V5ID0gZGF0YS5JZA0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgY29uc3Qgc2V0RGF0YSA9ICh7IERhdGEgfSkgPT4gew0KICAgICAgICB0aGlzLmFyZWFJZCA9IERhdGEuSWQNCiAgICAgICAgdGhpcy5wcm9qZWN0SWQgPSBEYXRhLlByb2plY3RfSWQNCiAgICAgICAgdGhpcy5leHBhbmRlZEtleSA9IHRoaXMuYXJlYUlkDQogICAgICAgIC8vIHRoaXMuZm9ybUlubGluZS5GaW5pc2hfRGF0ZSA9ICcnDQogICAgICAgIC8vIHRoaXMuZm9ybUlubGluZS5JbnN0YWxsVW5pdF9JZCA9ICcnDQogICAgICAgIC8vIHRoaXMuZm9ybUlubGluZS5SZW1hcmsgPSAnJw0KICAgICAgICBjb25zdCBfYXJyID0gZmluZEFsbFBhcmVudE5vZGUodGhpcy50cmVlRGF0YSwgZGF0YS5JZCwgdHJ1ZSkNCiAgICAgICAgdGhpcy5ub2RlTGFiZWxzID0gX2Fyci5maWx0ZXIodiA9PiAhIXYuUGFyZW50Tm9kZXMpLm1hcChwID0+IHAuTGFiZWwpDQogICAgICAgIHRoaXMuZmV0Y2hEYXRhKCkNCiAgICAgICAgLy8gdGhpcy5nZXRBcmVhSW5mbygpDQogICAgICAgIHRoaXMuZ2V0SW5zdGFsbFVuaXRJZE5hbWVMaXN0KCkNCiAgICAgIH0NCg0KICAgICAgc2V0RGF0YShkYXRhKQ0KICAgIH0sDQogICAgZmV0Y2hUcmVlRGF0YSgpIHsNCiAgICAgIHRoaXMudHJlZUxvYWRpbmcgPSB0cnVlDQogICAgICBHZXRQcm9qZWN0QXJlYVRyZWVMaXN0KHsgTWVudUlkOiB0aGlzLiRyb3V0ZS5tZXRhLklkLCBwcm9qZWN0TmFtZTogdGhpcy5wcm9qZWN0TmFtZSwgdHlwZTogNiB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKCFyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICAgIHRoaXMudHJlZURhdGEgPSBbXQ0KICAgICAgICAgIHRoaXMudHJlZUxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICAgIGlmIChyZXMuRGF0YS5sZW5ndGggPT09IDApIHsNCiAgICAgICAgICB0aGlzLnRyZWVMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KICAgICAgICBjb25zdCByZXNEYXRhID0gcmVzLkRhdGEubWFwKGl0ZW0gPT4gew0KICAgICAgICAgIGl0ZW0uSXNfRGlyZWN0b3J5ID0gdHJ1ZQ0KICAgICAgICAgIHJldHVybiBpdGVtDQogICAgICAgIH0pDQogICAgICAgIHRoaXMudHJlZURhdGEgPSByZXNEYXRhDQogICAgICAgIGNvbnNvbGUubG9nKCdzZXRLZXknKQ0KICAgICAgICB0aGlzLnNldEtleSgpDQogICAgICAgIHRoaXMudHJlZUxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLnRyZWVMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgdGhpcy50cmVlRGF0YSA9IFtdDQogICAgICB9KQ0KICAgIH0sDQogICAgc2V0S2V5KCkgew0KICAgICAgY29uc3QgZGVlcEZpbHRlciA9ICh0cmVlKSA9PiB7DQogICAgICAgIGNvbnNvbGUubG9nKCd0cmVlJywgdHJlZSkNCiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0cmVlLmxlbmd0aDsgaSsrKSB7DQogICAgICAgICAgY29uc3QgaXRlbSA9IHRyZWVbaV0NCiAgICAgICAgICBjb25zdCB7IERhdGEsIENoaWxkcmVuIH0gPSBpdGVtDQogICAgICAgICAgY29uc29sZS5sb2coRGF0YSkNCiAgICAgICAgICBpZiAoRGF0YS5QYXJlbnRJZCAmJiAhQ2hpbGRyZW4/Lmxlbmd0aCkgew0KICAgICAgICAgICAgdGhpcy5oYW5kbGVOb2RlQ2xpY2soaXRlbSkNCiAgICAgICAgICAgIHJldHVybg0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBpZiAoQ2hpbGRyZW4gJiYgQ2hpbGRyZW4/Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgcmV0dXJuIGRlZXBGaWx0ZXIoQ2hpbGRyZW4pDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgICByZXR1cm4gZGVlcEZpbHRlcih0aGlzLnRyZWVEYXRhKQ0KICAgIH0sDQogICAgY3VzdG9tRmlsdGVyRnVuKHZhbHVlLCBkYXRhLCBub2RlKSB7DQogICAgICBjb25zdCBhcnIgPSB2YWx1ZS5zcGxpdChTUExJVF9TWU1CT0wpDQogICAgICBjb25zdCBsYWJlbFZhbCA9IGFyclswXQ0KICAgICAgY29uc3Qgc3RhdHVzVmFsID0gYXJyWzFdDQogICAgICBpZiAoIXZhbHVlKSByZXR1cm4gdHJ1ZQ0KICAgICAgbGV0IHBhcmVudE5vZGUgPSBub2RlLnBhcmVudA0KICAgICAgbGV0IGxhYmVscyA9IFtub2RlLmxhYmVsXQ0KICAgICAgbGV0IHN0YXR1cyA9IFtkYXRhLkRhdGFbdGhpcy5zdGF0dXNDb2RlXV0NCiAgICAgIGxldCBsZXZlbCA9IDENCiAgICAgIHdoaWxlIChsZXZlbCA8IG5vZGUubGV2ZWwpIHsNCiAgICAgICAgbGFiZWxzID0gWy4uLmxhYmVscywgcGFyZW50Tm9kZS5sYWJlbF0NCiAgICAgICAgc3RhdHVzID0gWy4uLnN0YXR1cywgZGF0YS5EYXRhW3RoaXMuc3RhdHVzQ29kZV1dDQogICAgICAgIHBhcmVudE5vZGUgPSBwYXJlbnROb2RlLnBhcmVudA0KICAgICAgICBsZXZlbCsrDQogICAgICB9DQogICAgICBsYWJlbHMgPSBsYWJlbHMuZmlsdGVyKHYgPT4gISF2KQ0KICAgICAgc3RhdHVzID0gc3RhdHVzLmZpbHRlcih2ID0+ICEhdikNCiAgICAgIGxldCByZXN1bHRMYWJlbCA9IHRydWUNCiAgICAgIGxldCByZXN1bHRTdGF0dXMgPSB0cnVlDQogICAgICBpZiAodGhpcy5zdGF0dXNUeXBlKSB7DQogICAgICAgIHJlc3VsdFN0YXR1cyA9IHN0YXR1cy5zb21lKHMgPT4gcy5pbmRleE9mKHN0YXR1c1ZhbCkgIT09IC0xKQ0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMucHJvamVjdE5hbWUpIHsNCiAgICAgICAgcmVzdWx0TGFiZWwgPSBsYWJlbHMuc29tZShzID0+IHMuaW5kZXhPZihsYWJlbFZhbCkgIT09IC0xKQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIHJlc3VsdExhYmVsICYmIHJlc3VsdFN0YXR1cw0KICAgIH0sDQogICAgYXN5bmMgZ2V0Q29uZmlnKCkgew0KICAgICAgYXdhaXQgdGhpcy5nZXRUYWJsZUNvbmZpZygnUFJPVW5pdFBhcnREcmFmdEVkaXRUYkNvbmZpZycpDQogICAgfSwNCiAgICBmaWx0ZXJEYXRhKHBhZ2UpIHsNCiAgICAgIGNvbnN0IHNwbGl0QW5kQ2xlYW4gPSAoaW5wdXQpID0+IGlucHV0LnRyaW0oKS5yZXBsYWNlKC9ccysvZywgJyAnKS5zcGxpdCgnICcpDQoNCiAgICAgIGlmICh0aGlzLmN1clNlYXJjaCA9PT0gMSkgew0KICAgICAgICB0aGlzLmZvcm0uQ29tcF9Db2RlID0gdGhpcy5zZWFyY2hDb250ZW50DQogICAgICAgIHRoaXMuZm9ybS5Db21wX0NvZGVCbHVyID0gJycNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmN1clNlYXJjaCA9PT0gMCkgew0KICAgICAgICB0aGlzLmZvcm0uQ29tcF9Db2RlQmx1ciA9IHRoaXMuc2VhcmNoQ29udGVudA0KICAgICAgICB0aGlzLmZvcm0uQ29tcF9Db2RlID0gJycNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmN1clBhcnRTZWFyY2ggPT09IDEpIHsNCiAgICAgICAgdGhpcy5mb3JtLlBhcnRfQ29kZUJsdXIgPSAnJw0KICAgICAgICB0aGlzLmZvcm0uUGFydF9Db2RlID0gdGhpcy5zZWFyY2hQYXJ0Q29udGVudA0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuY3VyUGFydFNlYXJjaCA9PT0gMCkgew0KICAgICAgICB0aGlzLmZvcm0uUGFydF9Db2RlID0gJycNCiAgICAgICAgdGhpcy5mb3JtLlBhcnRfQ29kZUJsdXIgPSB0aGlzLnNlYXJjaFBhcnRDb250ZW50DQogICAgICB9DQoNCiAgICAgIGNvbnN0IGYgPSBbXQ0KICAgICAgZm9yIChjb25zdCBmb3JtS2V5IGluIHRoaXMuZm9ybSkgew0KICAgICAgICBpZiAodGhpcy5mb3JtW2Zvcm1LZXldIHx8IHRoaXMuZm9ybVtmb3JtS2V5XSA9PT0gZmFsc2UpIHsNCiAgICAgICAgICBmLnB1c2goZm9ybUtleSkNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgaWYgKCFmLmxlbmd0aCkgew0KICAgICAgICB0aGlzLnNldFBhZ2UoKQ0KICAgICAgICAhcGFnZSAmJiAodGhpcy5wYWdlSW5mby5wYWdlID0gMSkNCiAgICAgICAgdGhpcy5wYWdlSW5mby50b3RhbCA9IHRoaXMudGJEYXRhLmxlbmd0aA0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgY29uc3QgY2hlY2tNYXRjaCA9IChvcmlnaW4sIGNvbXApID0+IHsNCiAgICAgICAgY29uc3QgX2NvbXAgPSBjb21wLm1hcChjb2RlID0+IHsNCiAgICAgICAgICBjb25zdCBba2V5LCB2YWx1ZV0gPSBjb2RlLnNwbGl0KCcmJiYnKQ0KICAgICAgICAgIHJldHVybiBrZXkNCiAgICAgICAgfSkNCiAgICAgICAgY29uc3QgX29yaWdpbiA9IG9yaWdpbi5tYXAoY29kZSA9PiB7DQogICAgICAgICAgY29uc3QgW2tleSwgdmFsdWVdID0gY29kZS5zcGxpdCgnJiYmJykNCiAgICAgICAgICByZXR1cm4ga2V5DQogICAgICAgIH0pDQogICAgICAgIHJldHVybiBfb3JpZ2luLnNvbWUoaXRlbSA9PiB7DQogICAgICAgICAgcmV0dXJuIF9jb21wLnNvbWUodmFsdWUgPT4gaXRlbS5pbmNsdWRlcyh2YWx1ZSkpDQogICAgICAgIH0pDQogICAgICB9DQogICAgICBjb25zdCBjaGVja0V4YWN0TWF0Y2ggPSAob3JpZ2luLCBjb21wKSA9PiB7DQogICAgICAgIGNvbnN0IF9jb21wID0gY29tcC5tYXAoY29kZSA9PiB7DQogICAgICAgICAgY29uc3QgW2tleSwgdmFsdWVdID0gY29kZS5zcGxpdCgnJiYmJykNCiAgICAgICAgICByZXR1cm4ga2V5DQogICAgICAgIH0pDQogICAgICAgIGNvbnN0IF9vcmlnaW4gPSBvcmlnaW4ubWFwKGNvZGUgPT4gew0KICAgICAgICAgIGNvbnN0IFtrZXksIHZhbHVlXSA9IGNvZGUuc3BsaXQoJyYmJicpDQogICAgICAgICAgcmV0dXJuIGtleQ0KICAgICAgICB9KQ0KDQogICAgICAgIHJldHVybiBfb3JpZ2luLnNvbWUoaXRlbSA9PiBfY29tcC5pbmNsdWRlcyhpdGVtKSkNCiAgICAgIH0NCg0KICAgICAgY29uc3QgdGVtVGJEYXRhID0gdGhpcy50YkRhdGEuZmlsdGVyKHYgPT4gew0KICAgICAgICB2LmNoZWNrZWQgPSBmYWxzZQ0KICAgICAgICBjb25zdCBjb21wQ29kZSA9IHYuQ29tcG9uZW50X0NvZGVzIHx8IFtdDQoNCiAgICAgICAgaWYgKHRoaXMuZm9ybS5Db21wX0NvZGUudHJpbSgpKSB7DQogICAgICAgICAgY29uc3QgY29tcENvZGVBcnJheSA9IHNwbGl0QW5kQ2xlYW4odGhpcy5mb3JtLkNvbXBfQ29kZSkNCiAgICAgICAgICBpZiAoY29tcENvZGVBcnJheS5sZW5ndGgpIHsNCiAgICAgICAgICAgIGNvbnN0IGZsYWcgPSBjaGVja0V4YWN0TWF0Y2goY29tcENvZGUsIGNvbXBDb2RlQXJyYXkpDQogICAgICAgICAgICBjb25zb2xlLmxvZyg4ODcsIGNvbXBDb2RlLCBjb21wQ29kZUFycmF5LCBmbGFnKQ0KICAgICAgICAgICAgaWYgKCFmbGFnKSByZXR1cm4gZmFsc2UNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICBpZiAodGhpcy5mb3JtLkNvbXBfQ29kZUJsdXIudHJpbSgpKSB7DQogICAgICAgICAgY29uc3QgY29tcENvZGVBcnJheSA9IHNwbGl0QW5kQ2xlYW4odGhpcy5mb3JtLkNvbXBfQ29kZUJsdXIpDQogICAgICAgICAgaWYgKGNvbXBDb2RlQXJyYXkubGVuZ3RoKSB7DQogICAgICAgICAgICBjb25zdCBmbGFnID0gY2hlY2tNYXRjaChjb21wQ29kZSwgY29tcENvZGVBcnJheSkNCiAgICAgICAgICAgIGlmICghZmxhZykgcmV0dXJuIGZhbHNlDQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgaWYgKHRoaXMuZm9ybS5UeXBlICYmIHYuVHlwZSAhPT0gdGhpcy5mb3JtLlR5cGUpIHsNCiAgICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgICAgfQ0KDQogICAgICAgIGlmICh0aGlzLmZvcm0uUGFydF9Db2RlQmx1ci50cmltKCkpIHsNCiAgICAgICAgICBjb25zdCBwYXJ0Q29kZUJsdXJBcnJheSA9IHNwbGl0QW5kQ2xlYW4odGhpcy5mb3JtLlBhcnRfQ29kZUJsdXIpDQogICAgICAgICAgaWYgKCFwYXJ0Q29kZUJsdXJBcnJheS5zb21lKGNvZGUgPT4gdlsnUGFydF9Db2RlJ10uaW5jbHVkZXMoY29kZSkpKSB7DQogICAgICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICBpZiAodGhpcy5mb3JtLlBhcnRfQ29kZS50cmltKCkpIHsNCiAgICAgICAgICBjb25zdCBwYXJ0Q29kZUFycmF5ID0gc3BsaXRBbmRDbGVhbih0aGlzLmZvcm0uUGFydF9Db2RlKQ0KICAgICAgICAgIGlmICghcGFydENvZGVBcnJheS5pbmNsdWRlcyh2WydQYXJ0X0NvZGUnXSkpIHsNCiAgICAgICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIGlmICh0aGlzLmZvcm0uSW5zdGFsbFVuaXRfSWQubGVuZ3RoICYmICF0aGlzLmZvcm0uSW5zdGFsbFVuaXRfSWQuaW5jbHVkZXModi5JbnN0YWxsVW5pdF9JZCkpIHsNCiAgICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgICAgfQ0KDQogICAgICAgIGlmICh0aGlzLmZvcm0uU3BlYy50cmltKCkgIT09ICcnKSB7DQogICAgICAgICAgY29uc3Qgc3BlY0FycmF5ID0gc3BsaXRBbmRDbGVhbih0aGlzLmZvcm0uU3BlYykNCiAgICAgICAgICBpZiAoIXNwZWNBcnJheS5zb21lKHNwZWMgPT4gdi5TcGVjLmluY2x1ZGVzKHNwZWMpKSkgew0KICAgICAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIGlmICh0aGlzLnNlYXJjaENvbnRlbnQudHJpbSgpLmxlbmd0aCkgew0KICAgICAgICAgIGxldCBjc0NvdW50ID0gMA0KDQogICAgICAgICAgdi5jb21wb25lbnRNYXAgPSAodi5Db21wb25lbnRfQ29kZXMgfHwgW10pLnJlZHVjZSgoYWNjLCBjb2RlKSA9PiB7DQogICAgICAgICAgICBjb25zdCBba2V5LCB2YWx1ZV0gPSBjb2RlLnNwbGl0KCcmJiYnKQ0KICAgICAgICAgICAgYWNjW2tleV0gPSBwYXJzZUludCh2YWx1ZSkNCiAgICAgICAgICAgIGlmICh0aGlzLmN1clNlYXJjaCA9PT0gMSkgew0KICAgICAgICAgICAgICBjb25zdCBjb21wQ29kZUFycmF5ID0gc3BsaXRBbmRDbGVhbih0aGlzLmZvcm0uQ29tcF9Db2RlKQ0KICAgICAgICAgICAgICBpZiAoY29tcENvZGVBcnJheS5sZW5ndGgpIHsNCiAgICAgICAgICAgICAgICBjb25zdCBmbGFnID0gY2hlY2tFeGFjdE1hdGNoKFtrZXldLCBjb21wQ29kZUFycmF5KQ0KICAgICAgICAgICAgICAgIGlmIChmbGFnKSB7DQogICAgICAgICAgICAgICAgICBjc0NvdW50ICs9IHBhcnNlSW50KHZhbHVlKQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgY29uc3QgY29tcENvZGVBcnJheSA9IHNwbGl0QW5kQ2xlYW4odGhpcy5mb3JtLkNvbXBfQ29kZUJsdXIpDQogICAgICAgICAgICAgIGlmIChjb21wQ29kZUFycmF5Lmxlbmd0aCkgew0KICAgICAgICAgICAgICAgIGNvbnN0IGZsYWcgPSBjaGVja01hdGNoKFtrZXldLCBjb21wQ29kZUFycmF5KQ0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdwZmxhZycsIGtleSwgY29tcENvZGVBcnJheSwgZmxhZywgdmFsdWUpDQogICAgICAgICAgICAgICAgaWYgKGZsYWcpIHsNCiAgICAgICAgICAgICAgICAgIGNzQ291bnQgKz0gcGFyc2VJbnQodmFsdWUpDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgICByZXR1cm4gYWNjDQogICAgICAgICAgfSwge30pDQogICAgICAgICAgdGhpcy4kc2V0KHYsICdjc0NvdW50JywgTWF0aC5taW4oY3NDb3VudCwgdi5DYW5fU2NoZHVsaW5nX0NvdW50KSkNCiAgICAgICAgICB0aGlzLiRzZXQodiwgJ2NzQ291bnRXZWlnaHQnLCBNYXRoLm1pbih2LkNhbl9TY2hkdWxpbmdfV2VpZ2h0LCB2LmNzQ291bnQgKiB2LldlaWdodCkpDQoNCiAgICAgICAgICB2LnNlYXJjaGNvdW50ID0gdi5jb3VudA0KICAgICAgICAgIHYuc2VhcmNoY291bnRNYXggPSB2Lm1heENvdW50DQogICAgICAgICAgLy8gY29uc3QgY3MgPSB2LkNvbXBvbmVudF9Db2RlcyB8fCBbXQ0KICAgICAgICAgIC8vIGxldCBtaW4gPSAwDQogICAgICAgICAgLy8gY3MuZm9yRWFjaCgoZWxlbWVudCwgaWR4KSA9PiB7DQogICAgICAgICAgLy8gICBjb25zdCBba2V5LCB2YWx1ZV0gPSBlbGVtZW50LnNwbGl0KCcmJiYnKQ0KICAgICAgICAgIC8vICAgbWluID0gdi5jb21wb25lbnRNYXBba2V5XQ0KICAgICAgICAgIC8vIH0pDQoNCiAgICAgICAgICB2LmNvdW50ID0gdi5jc0NvdW50DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdi5jb3VudCA9IHYuQ2FuX1NjaGR1bGluZ19Db3VudA0KICAgICAgICB9DQoNCiAgICAgICAgLy8gdi5DYW5fU2NoZHVsaW5nX0NvdW50ID0gdi5jc0NvdW50DQogICAgICAgIC8vIHYuQ2FuX1NjaGR1bGluZ19XZWlnaHQgPSB2LmNzQ291bnRXZWlnaHQNCg0KICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgfSkNCg0KICAgICAgIXBhZ2UgJiYgKHRoaXMucGFnZUluZm8ucGFnZSA9IDEpDQogICAgICB0aGlzLnBhZ2VJbmZvLnRvdGFsID0gdGVtVGJEYXRhLmxlbmd0aA0KICAgICAgdGhpcy5zZXRQYWdlKHRlbVRiRGF0YSkNCiAgICAgIGlmICh0aGlzLnNlYXJjaENvbnRlbnQudHJpbSgpLmxlbmd0aCkgew0KICAgICAgICB0aGlzLnNob3dTYyA9IHRydWUNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZVNlYXJjaCgpIHsNCiAgICAgIHRoaXMudG90YWxTZWxlY3Rpb24gPSBbXQ0KICAgICAgdGhpcy5jbGVhclNlbGVjdCgpDQogICAgICBpZiAodGhpcy50YkRhdGE/Lmxlbmd0aCkgew0KICAgICAgICB0aGlzLnRiRGF0YS5mb3JFYWNoKGl0ZW0gPT4gaXRlbS5jaGVja2VkID0gZmFsc2UpDQogICAgICAgIHRoaXMuZmlsdGVyRGF0YSgpDQogICAgICB9DQogICAgICB0aGlzLnNob3dTYyA9ICEhdGhpcy5zZWFyY2hDb250ZW50LnRyaW0oKS5sZW5ndGgNCiAgICB9LA0KICAgIHRiU2VsZWN0Q2hhbmdlKGFycmF5KSB7DQogICAgICB0aGlzLnRvdGFsU2VsZWN0aW9uID0gdGhpcy50YkRhdGEuZmlsdGVyKHYgPT4gdi5jaGVja2VkKQ0KICAgIH0sDQogICAgY2xlYXJTZWxlY3QoKSB7DQogICAgICB0aGlzLiRyZWZzLnhUYWJsZTEuY2xlYXJDaGVja2JveFJvdygpDQogICAgICB0aGlzLnRvdGFsU2VsZWN0aW9uID0gW10NCiAgICB9LA0KICAgIGFzeW5jIGZldGNoRGF0YSgpIHsNCiAgICAgIHRoaXMuaGFuZGxlUmVzZXQoKQ0KICAgICAgdGhpcy50YkxvYWRpbmcgPSB0cnVlDQogICAgICBpZiAodGhpcy5pc0NvbSkgew0KICAgICAgICBhd2FpdCB0aGlzLmdldENvbVRiRGF0YSgpDQogICAgICB9IGVsc2Ugew0KICAgICAgICBhd2FpdCB0aGlzLmdldFBhcnRUYkRhdGEoKQ0KICAgICAgfQ0KICAgICAgdGhpcy5pbml0VGJEYXRhKCkNCiAgICAgIHRoaXMuZmlsdGVyRGF0YSgpDQogICAgICB0aGlzLnRiTG9hZGluZyA9IGZhbHNlDQogICAgfSwNCiAgICBzZXRQYWdlRGF0YSgpIHsNCiAgICAgIGlmICh0aGlzLnRiRGF0YT8ubGVuZ3RoKSB7DQogICAgICAgIHRoaXMucGFnZUluZm8ucGFnZSA9IDENCiAgICAgICAgdGhpcy50YkRhdGEgPSB0aGlzLnRiRGF0YS5maWx0ZXIodiA9PiB2LkNhbl9TY2hkdWxpbmdfQ291bnQgPiAwKQ0KICAgICAgICB0aGlzLmZpbHRlckRhdGEoKQ0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlU2F2ZSh0eXBlID0gMikgew0KICAgICAgaWYgKHR5cGUgPT09IDEpIHsNCiAgICAgICAgdGhpcy5hZGRMb2FkaW5nID0gdHJ1ZQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5zYXZlTG9hZGluZyA9IHRydWUNCiAgICAgIH0NCiAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICB0aGlzLnRvdGFsU2VsZWN0aW9uLmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICBjb25zdCBpbnRDb3VudCA9IHBhcnNlSW50KGl0ZW0uY291bnQpDQogICAgICAgICAgaWYgKHRoaXMuc2VhcmNoQ29udGVudC50cmltKCkubGVuZ3RoKSB7DQogICAgICAgICAgICBpdGVtLlNjaGR1bGVkX0NvdW50ID0gaXRlbS5DYW5fU2NoZHVsaW5nX0NvdW50DQoNCiAgICAgICAgICAgIGl0ZW0ubWF4Q291bnQgPSBpdGVtLkNhbl9TY2hkdWxpbmdfQ291bnQNCiAgICAgICAgICAgIGl0ZW0uY2hvb3NlQ291bnQgPSBpbnRDb3VudA0KICAgICAgICAgICAgaXRlbS5jb3VudCA9IGl0ZW0uQ2FuX1NjaGR1bGluZ19Db3VudA0KDQogICAgICAgICAgICBpdGVtLkNhbl9TY2hkdWxpbmdfQ291bnQgPSAwDQogICAgICAgICAgICBpdGVtLkNhbl9TY2hkdWxpbmdfV2VpZ2h0ID0gaXRlbS5DYW5fU2NoZHVsaW5nX0NvdW50ICogaXRlbS5XZWlnaHQNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgaXRlbS5TY2hkdWxlZF9Db3VudCArPSBpbnRDb3VudA0KICAgICAgICAgICAgaXRlbS5DYW5fU2NoZHVsaW5nX0NvdW50IC09IGludENvdW50DQogICAgICAgICAgICBpdGVtLkNhbl9TY2hkdWxpbmdfV2VpZ2h0ID0gaXRlbS5DYW5fU2NoZHVsaW5nX0NvdW50ICogaXRlbS5XZWlnaHQNCiAgICAgICAgICAgIGl0ZW0ubWF4Q291bnQgPSBpbnRDb3VudA0KICAgICAgICAgICAgaXRlbS5jaG9vc2VDb3VudCA9IGludENvdW50DQogICAgICAgICAgICBpdGVtLmNvdW50ID0gaXRlbS5DYW5fU2NoZHVsaW5nX0NvdW50DQogICAgICAgICAgfQ0KDQogICAgICAgICAgaXRlbS5jaGVja2VkID0gZmFsc2UNCiAgICAgICAgfSkNCiAgICAgICAgY29uc3QgY3AgPSBkZWVwQ2xvbmUodGhpcy50b3RhbFNlbGVjdGlvbikNCg0KICAgICAgICAvLyB0aGlzLiRlbWl0KCdzZW5kU2VsZWN0TGlzdCcsIGNwKQ0KICAgICAgICB0aGlzLmFkZExvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB0aGlzLmNsZWFyU2VsZWN0KCkNCiAgICAgICAgLy8gdGhpcy5zZXRQYWdlKCkNCiAgICAgICAgdGhpcy5zZXRQYWdlRGF0YSgpDQogICAgICAgIGlmICh0eXBlID09PSAyKSB7DQogICAgICAgICAgdGhpcy4kZW1pdCgnc2VuZFNlbGVjdExpc3QnLCBjcCkNCiAgICAgICAgICB0aGlzLiRlbWl0KCdjbG9zZScpDQogICAgICAgICAgdGhpcy5mVGFibGUgPSBbXQ0KICAgICAgICAgIHRoaXMudGJEYXRhID0gW10NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRlbWl0KCdhZGRUb1RiTGlzdCcsIGNwKQ0KICAgICAgICB9DQogICAgICB9LCAwKQ0KICAgIH0sDQogICAgaW5pdFRiRGF0YSgpIHsNCiAgICAgIC8vIOiuvue9ruaWh+acrOahhumAieaLqeeahOaOkuS6p+aVsOmHjyzorr7nva7oh6rlrprkuYnllK/kuIDnoIENCiAgICAgIGNvbnN0IG9iaktleSA9IHt9DQogICAgICBpZiAoIXRoaXMudGJEYXRhPy5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy50YkRhdGEgPSBbXQ0KICAgICAgICAvLyB0aGlzLmJhY2tlbmRUYiA9IFtdDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgY29uc29sZS5sb2coOTk4LCBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMudGJEYXRhKSkpDQogICAgICAvLyB0aGlzLmJhY2tlbmRUYiA9IGRlZXBDbG9uZSh0aGlzLnRiRGF0YSkNCiAgICAgIHRoaXMudGJEYXRhID0gdGhpcy50YkRhdGEuZmlsdGVyKGl0ZW0gPT4gew0KICAgICAgICB0aGlzLiRzZXQoaXRlbSwgJ2NvdW50JywgaXRlbS5DYW5fU2NoZHVsaW5nX0NvdW50KQ0KICAgICAgICB0aGlzLiRzZXQoaXRlbSwgJ21heENvdW50JywgaXRlbS5DYW5fU2NoZHVsaW5nX0NvdW50KQ0KICAgICAgICBpdGVtLnV1aWQgPSBnZXRVbmlxdWUodGhpcy5pc0NvbSwgaXRlbSkNCiAgICAgICAgb2JqS2V5W2l0ZW0uVHlwZV0gPSB0cnVlDQogICAgICAgIC8vIGxldCBjc0NvdW50ID0gMA0KICAgICAgICAvLyBpdGVtLmNvbXBvbmVudE1hcCA9IChpdGVtLkNvbXBvbmVudF9Db2RlcyB8fCBbXSkucmVkdWNlKChhY2MsIGNvZGUpID0+IHsNCiAgICAgICAgLy8gICBjb25zdCBba2V5LCB2YWx1ZV0gPSBjb2RlLnNwbGl0KCcmJiYnKQ0KICAgICAgICAvLyAgIGFjY1trZXldID0gcGFyc2VJbnQodmFsdWUpDQogICAgICAgIC8vICAgY3NDb3VudCArPSBwYXJzZUludCh2YWx1ZSkNCiAgICAgICAgLy8gICByZXR1cm4gYWNjDQogICAgICAgIC8vIH0sIHt9KQ0KICAgICAgICAvLyB0aGlzLiRzZXQoaXRlbSwgJ2NzQ291bnQnLCBjc0NvdW50KQ0KICAgICAgICAvLyBPYmplY3Qua2V5cyhpdGVtLmNvbXBvbmVudE1hcCkuZm9yRWFjaChrZXkgPT4gew0KICAgICAgICAvLyAgIHRoaXMuJHNldChpdGVtLCBrZXksIGl0ZW0uY29tcG9uZW50TWFwW2tleV0pDQogICAgICAgIC8vIH0pDQoNCiAgICAgICAgcmV0dXJuICF0aGlzLmFkZFRiS2V5cy5pbmNsdWRlcyhpdGVtLnV1aWQpDQogICAgICB9KQ0KICAgICAgLy8gICAubWFwKChpdGVtKSA9PiB7DQogICAgICAvLyAgIHRoaXMuJHNldChpdGVtLCAnY291bnQnLCBpdGVtLkNhbl9TY2hkdWxpbmdfQ291bnQpDQogICAgICAvLyAgIHRoaXMuJHNldChpdGVtLCAnbWF4Q291bnQnLCBpdGVtLkNhbl9TY2hkdWxpbmdfQ291bnQpDQogICAgICAvLyAgIC8vIGl0ZW0udXVpZCA9IHV1aWR2NCgpDQogICAgICAvLyAgIGl0ZW0udXVpZCA9IGl0ZW0uSW5zdGFsbFVuaXRfSWQgKyBpdGVtLlBhcnRfQWdncmVnYXRlX0lkDQogICAgICAvLyAgIG9iaktleVtpdGVtLlR5cGVdID0gdHJ1ZQ0KICAgICAgLy8NCiAgICAgIC8vICAgY29uc3QgX3NlbGVjdExpc3QgPSB0aGlzLnNlbGVjdFRiRGF0YS5maWx0ZXIodiA9PiB2LnB1dWlkKQ0KICAgICAgLy8gICBjb25zb2xlLmxvZygnX3NlbGVjdExpc3QnLCBfc2VsZWN0TGlzdCkNCiAgICAgIC8vICAgLy8gX3NlbGVjdExpc3QuZm9yRWFjaCgoZWxlbWVudCwgaWR4KSA9PiB7DQogICAgICAvLyAgIC8vICAgaWYoZWxlbWVudC5wdXVpZCA9PT0gaXRlbS51dWlkKXsNCiAgICAgIC8vICAgLy8NCiAgICAgIC8vICAgLy8gICB9DQogICAgICAvLyAgIC8vIH0pDQogICAgICAvLyAgIHJldHVybiBpdGVtDQogICAgICAvLyB9KQ0KDQogICAgICAvLyB0aGlzLmJhY2tlbmRUYiA9IGRlZXBDbG9uZSh0aGlzLnRiRGF0YSkNCiAgICB9LA0KICAgIGFzeW5jIGdldENvbVRiRGF0YSgpIHsNCiAgICAgIC8vIGNvbnN0IHsgaW5zdGFsbCwgYXJlYUlkIH0gPSB0aGlzLiRyb3V0ZS5xdWVyeQ0KICAgICAgY29uc3QgeyBDb21wX0NvZGVzLCAuLi5vYmogfSA9IHRoaXMuZm9ybQ0KICAgICAgbGV0IGNvZGVzID0gW10NCiAgICAgIGlmIChPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nLmNhbGwoQ29tcF9Db2RlcykgPT09ICdbb2JqZWN0IFN0cmluZ10nKSB7DQogICAgICAgIGNvZGVzID0gQ29tcF9Db2RlcyAmJiBDb21wX0NvZGVzLnNwbGl0KCcgJykuZmlsdGVyKHYgPT4gISF2KQ0KICAgICAgfQ0KICAgICAgYXdhaXQgR2V0Q2FuU2NoZHVsaW5nQ29tcHMoew0KICAgICAgICBJZHM6IHRoaXMuY3VycmVudElkcywNCiAgICAgICAgLi4ub2JqLA0KICAgICAgICBTY2hkdWxpbmdfUGxhbl9JZDogdGhpcy5zY2hlZHVsZUlkLA0KICAgICAgICBDb21wX0NvZGVzOiBjb2RlcywNCiAgICAgICAgSW5zdGFsbFVuaXRfSWQ6IHRoaXMuaW5zdGFsbElkLA0KICAgICAgICBBcmVhX0lkOiB0aGlzLmFyZWFJZA0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy5wYWdlSW5mby50b3RhbCA9IHJlcy5EYXRhLmxlbmd0aA0KICAgICAgICAgIHRoaXMudGJEYXRhID0gcmVzLkRhdGEubWFwKCh2LCBpZHgpID0+IHsNCiAgICAgICAgICAgIC8vIOW3suaOkuS6p+i1i+WAvA0KICAgICAgICAgICAgdi5vcmlnaW5hbFBhdGggPSB2LlNjaGVkdWxlZF9UZWNobm9sb2d5X1BhdGggPyB2LlNjaGVkdWxlZF9UZWNobm9sb2d5X1BhdGggOiAnJw0KICAgICAgICAgICAgdi5Xb3Jrc2hvcF9JZCA9IHYuU2NoZWR1bGVkX1dvcmtzaG9wX0lkDQogICAgICAgICAgICB2LldvcmtzaG9wX05hbWUgPSB2LlNjaGVkdWxlZF9Xb3Jrc2hvcF9OYW1lDQogICAgICAgICAgICB2LlRlY2hub2xvZ3lfUGF0aCA9IHYuU2NoZWR1bGVkX1RlY2hub2xvZ3lfUGF0aCB8fCB2LlRlY2hub2xvZ3lfUGF0aA0KICAgICAgICAgICAgLy8gaWYgKHYub3JpZ2luYWxQYXRoKSB7DQogICAgICAgICAgICAvLyB2LmlzRGlzYWJsZWQgPSB0cnVlDQogICAgICAgICAgICAvLyB9DQogICAgICAgICAgICB2LmNoZWNrZWQgPSBmYWxzZQ0KICAgICAgICAgICAgdi5pbml0Um93SW5kZXggPSBpZHgNCiAgICAgICAgICAgIC8vIHYudGVjaG5vbG9neVBhdGhEaXNhYmxlZCA9ICEhdi5UZWNobm9sb2d5X1BhdGgNCiAgICAgICAgICAgIHJldHVybiB2DQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLnNldFBhZ2UoKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKg0KICAgICAqIOWIhumhtQ0KICAgICAqLw0KICAgIGhhbmRsZVBhZ2VDaGFuZ2UoeyBjdXJyZW50UGFnZSwgcGFnZVNpemUgfSkgew0KICAgICAgaWYgKHRoaXMudGJMb2FkaW5nKSByZXR1cm4NCiAgICAgIHRoaXMucGFnZUluZm8ucGFnZSA9IGN1cnJlbnRQYWdlDQogICAgICB0aGlzLnBhZ2VJbmZvLnBhZ2VTaXplID0gcGFnZVNpemUNCiAgICAgIHRoaXMuc2V0UGFnZSgpDQogICAgICB0aGlzLmZpbHRlckRhdGEoY3VycmVudFBhZ2UpDQogICAgfSwNCg0KICAgIHNldFBhZ2UodGIgPSB0aGlzLnRiRGF0YSkgew0KICAgICAgdGhpcy5mVGFibGUgPSB0Yi5zbGljZSgodGhpcy5wYWdlSW5mby5wYWdlIC0gMSkgKiB0aGlzLnBhZ2VJbmZvLnBhZ2VTaXplLCB0aGlzLnBhZ2VJbmZvLnBhZ2UgKiB0aGlzLnBhZ2VJbmZvLnBhZ2VTaXplKQ0KICAgIH0sDQoNCiAgICBhc3luYyBnZXRQYXJ0VGJEYXRhKCkgew0KICAgICAgLy8gY29uc3QgeyBpbnN0YWxsLCBhcmVhSWQgfSA9IHRoaXMuJHJvdXRlLnF1ZXJ5DQogICAgICBhd2FpdCBHZXRDYW5TY2hkdWxpbmdVbml0cyh7DQogICAgICAgIElkczogdGhpcy5jdXJyZW50SWRzLA0KICAgICAgICAuLi50aGlzLmZvcm0sDQogICAgICAgIFNjaGR1bGluZ19QbGFuX0lkOiB0aGlzLnNjaGVkdWxlSWQsDQogICAgICAgIEluc3RhbGxVbml0X0lkOiB0aGlzLmluc3RhbGxJZCwNCiAgICAgICAgQXJlYV9JZDogdGhpcy5hcmVhSWQNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMucGFnZUluZm8udG90YWwgPSByZXMuRGF0YS5sZW5ndGgNCiAgICAgICAgICB0aGlzLnRiRGF0YSA9IHJlcy5EYXRhLm1hcCgodiwgaWR4KSA9PiB7DQogICAgICAgICAgICB2Lm9yaWdpbmFsUGF0aCA9IHYuU2NoZWR1bGVkX1RlY2hub2xvZ3lfUGF0aCA/IHYuU2NoZWR1bGVkX1RlY2hub2xvZ3lfUGF0aCA6ICcnDQogICAgICAgICAgICB2LldvcmtzaG9wX0lkID0gdi5TY2hlZHVsZWRfV29ya3Nob3BfSWQNCiAgICAgICAgICAgIHYuV29ya3Nob3BfTmFtZSA9IHYuU2NoZWR1bGVkX1dvcmtzaG9wX05hbWUNCiAgICAgICAgICAgIGlmICh2LkNvbXBfSW1wb3J0X0RldGFpbF9JZCkgew0KICAgICAgICAgICAgICB2LlBhcnRfVXNlZF9Qcm9jZXNzID0gdGhpcy5nZXRQYXJ0VXNlZFByb2Nlc3ModikNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHYuVGVjaG5vbG9neV9QYXRoID0gdi5TY2hlZHVsZWRfVGVjaG5vbG9neV9QYXRoIHx8IHYuVGVjaG5vbG9neV9QYXRoDQogICAgICAgICAgICAvLyB2LmlzRGlzYWJsZWQgPSAhIXYub3JpZ2luYWxQYXRoDQogICAgICAgICAgICB2LmNoZWNrZWQgPSBmYWxzZQ0KICAgICAgICAgICAgdi5pbml0Um93SW5kZXggPSBpZHgNCiAgICAgICAgICAgIHYuQXJlYV9OYW1lID0gdGhpcy5ub2RlTGFiZWxzLmpvaW4oJy8nKQ0KICAgICAgICAgICAgLy8gdi5wYXJ0VXNlZFByb2Nlc3NEaXNhYmxlZCA9IHRoaXMuaXNQYXJ0UHJlcGFyZSA/ICEhdi5QYXJ0X1VzZWRfUHJvY2VzcyA6IGZhbHNlDQogICAgICAgICAgICAvLyB2LnRlY2hub2xvZ3lQYXRoRGlzYWJsZWQgPSAhIXYuVGVjaG5vbG9neV9QYXRoDQogICAgICAgICAgICBpZiAoIXRoaXMuaXNQYXJ0UHJlcGFyZSkgew0KICAgICAgICAgICAgICB2LlRlbXBfUGFydF9Vc2VkX1Byb2Nlc3MgPSB2LlBhcnRfVXNlZF9Qcm9jZXNzDQogICAgICAgICAgICB9DQogICAgICAgICAgICByZXR1cm4gdg0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy5zZXRQYXJ0Q29sdW1uKCkNCiAgICAgICAgICB0aGlzLnNldFBhZ2UoKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIGNvbnN0IHN1Ym1pdE9iaiA9IHRoaXMudGJEYXRhLm1hcChpdGVtID0+IHsNCiAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICBJZDogaXRlbS5QYXJ0X0FnZ3JlZ2F0ZV9JZCwNCiAgICAgICAgICBUeXBlOiAzDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICBhd2FpdCBHZXRTdG9wTGlzdChzdWJtaXRPYmopLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICBjb25zdCBzdG9wTWFwID0ge30NCiAgICAgICAgICByZXMuRGF0YS5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgICAgc3RvcE1hcFtpdGVtLklkXSA9ICEhaXRlbS5Jc19TdG9wDQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLnRiRGF0YS5mb3JFYWNoKHJvdyA9PiB7DQogICAgICAgICAgICBpZiAoc3RvcE1hcC5oYXNPd25Qcm9wZXJ0eShyb3cuUGFydF9BZ2dyZWdhdGVfSWQpKSB7DQogICAgICAgICAgICAgIHRoaXMuJHNldChyb3csICdzdG9wRmxhZycsIHN0b3BNYXBbcm93LlBhcnRfQWdncmVnYXRlX0lkXSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgY2hlY2tDaGVja2JveE1ldGhvZCh7IHJvdyB9KSB7DQogICAgICByZXR1cm4gIXJvdy5zdG9wRmxhZw0KICAgIH0sDQogICAgZ2V0UGFydFVzZWRQcm9jZXNzKGl0ZW0pIHsNCiAgICAgIGlmIChpdGVtLlNjaGVkdWxlZF9Vc2VkX1Byb2Nlc3MpIHsNCiAgICAgICAgcmV0dXJuIGl0ZW0uU2NoZWR1bGVkX1VzZWRfUHJvY2Vzcw0KICAgICAgfQ0KICAgICAgaWYgKGl0ZW0uQ29tcG9uZW50X1RlY2hub2xvZ3lfUGF0aCkgew0KICAgICAgICBjb25zdCBsaXN0ID0gaXRlbS5Db21wb25lbnRfVGVjaG5vbG9neV9QYXRoLnNwbGl0KCcvJykNCiAgICAgICAgaWYgKGxpc3QuaW5jbHVkZXMoaXRlbS5QYXJ0X1VzZWRfUHJvY2VzcykpIHsNCiAgICAgICAgICByZXR1cm4gaXRlbS5QYXJ0X1VzZWRfUHJvY2Vzcw0KICAgICAgICB9IGVsc2UgaWYgKGxpc3QuaW5jbHVkZXMoaXRlbS5QYXJ0X1R5cGVfVXNlZF9Qcm9jZXNzKSkgew0KICAgICAgICAgIHJldHVybiBpdGVtLlBhcnRfVHlwZV9Vc2VkX1Byb2Nlc3MNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgaWYgKGl0ZW0uUGFydF9Vc2VkX1Byb2Nlc3MpIHsNCiAgICAgICAgICByZXR1cm4gaXRlbS5QYXJ0X1VzZWRfUHJvY2Vzcw0KICAgICAgICB9IGVsc2UgaWYgKGl0ZW0uUGFydF9UeXBlX1VzZWRfUHJvY2Vzcykgew0KICAgICAgICAgIHJldHVybiBpdGVtLlBhcnRfVHlwZV9Vc2VkX1Byb2Nlc3MNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICByZXR1cm4gJycNCiAgICB9LA0KICAgIHNldFBhcnRDb2x1bW4oKSB7DQogICAgICAvLyDnuq/pm7bku7YNCiAgICAgIHRoaXMuaXNPd25lck51bGwgPSB0aGlzLnRiRGF0YS5ldmVyeSh2ID0+ICF2LkNvbXBfSW1wb3J0X0RldGFpbF9JZCkNCiAgICAgIGNvbnNvbGUubG9nKCd0aGlzLmlzT3duZXJOdWxsJywgdGhpcy5pc093bmVyTnVsbCkNCiAgICAgIGlmICh0aGlzLmlzT3duZXJOdWxsKSB7DQogICAgICAgIGNvbnN0IGlkeCA9IHRoaXMuY29sdW1ucy5maW5kSW5kZXgodiA9PiB2LkNvZGUgPT09ICdDb21wb25lbnRfQ29kZScpDQogICAgICAgIGlkeCAhPT0gLTEgJiYgdGhpcy5jb2x1bW5zLnNwbGljZShpZHgsIDEpDQogICAgICB9DQogICAgfSwNCiAgICBtZXJnZURhdGEobGlzdCkgew0KICAgICAgLyogICAgICBjb25zb2xlLmxvZygnbGlzdCcsIGxpc3QpDQogICAgICBjb25zb2xlLmxvZygndGhpcy5iYWNrZW5kVGInLCB0aGlzLmJhY2tlbmRUYikNCiAgICAgIGxpc3QNCiAgICAgICAgLmZvckVhY2goKGVsZW1lbnQsIGluZGV4KSA9PiB7DQogICAgICAgICAgY29uc3QgaWR4ID0gdGhpcy5iYWNrZW5kVGIuZmluZEluZGV4KA0KICAgICAgICAgICAgKGl0ZW0pID0+IGVsZW1lbnQucHV1aWQgJiYgaXRlbS51dWlkID09PSBlbGVtZW50LnB1dWlkDQogICAgICAgICAgKQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCdpZHgnLCBpZHgsIHRoaXMuYmFja2VuZFRiW2lkeF0pDQogICAgICAgICAgY29uc29sZS5sb2coJ2luZGV4JywgaW5kZXgpDQogICAgICAgICAgaWYgKGlkeCAhPT0gLTEpIHsNCiAgICAgICAgICAgIHRoaXMudGJEYXRhLnNwbGljZShpZHgsIDAsIGRlZXBDbG9uZSh0aGlzLmJhY2tlbmRUYltpZHhdKSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQoNCiAgICAgIHRoaXMudGJEYXRhLnNvcnQoKGEsIGIpID0+IGEuaW5pdFJvd0luZGV4IC0gYi5pbml0Um93SW5kZXgpDQogICAgICBjb25zb2xlLmxvZygndGhpcy50YkRhdGEnLCBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMudGJEYXRhKSkpDQoNCiAgICAgIHRoaXMuZmlsdGVyRGF0YSgpKi8NCiAgICB9LA0KICAgIGhhbmRsZUNsb3NlKCkgew0KICAgICAgdGhpcy4kZW1pdCgnY2xvc2UnKQ0KICAgIH0sDQogICAgLy8gYWN0aXZlQ2VsbE1ldGhvZCh7IHJvdywgY29sdW1uLCBjb2x1bW5JbmRleCB9KSB7DQogICAgLy8gICByZXR1cm4gY29sdW1uLmZpZWxkID09PSAnU2NoZHVsaW5nX0NvdW50Jw0KICAgIC8vIH0sDQogICAgYXN5bmMgZ2V0VGFibGVDb25maWcoY29kZSkgew0KICAgICAgYXdhaXQgR2V0R3JpZEJ5Q29kZSh7DQogICAgICAgIGNvZGUNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBjb25zdCB7IElzU3VjY2VlZCwgRGF0YSwgTWVzc2FnZSB9ID0gcmVzDQogICAgICAgIGlmIChJc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLnRiQ29uZmlnID0gT2JqZWN0LmFzc2lnbih7fSwgdGhpcy50YkNvbmZpZywgRGF0YS5HcmlkKQ0KICAgICAgICAgIHRoaXMucGFnZUluZm8ucGFnZVNpemUgPSBOdW1iZXIodGhpcy50YkNvbmZpZy5Sb3dfTnVtYmVyKQ0KICAgICAgICAgIGNvbnN0IGxpc3QgPSBEYXRhLkNvbHVtbkxpc3QgfHwgW10NCiAgICAgICAgICB0aGlzLmNvbHVtbnMgPSBsaXN0LmZpbHRlcih2ID0+IHYuSXNfRGlzcGxheSkNCiAgICAgICAgICAgIC5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgICAgIGlmIChpdGVtLklzX0Zyb3plbikgew0KICAgICAgICAgICAgICAgIGl0ZW0uZml4ZWQgPSAnbGVmdCcNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAvLyB0aGlzLmNvbHVtbnMucHVzaCh7DQogICAgICAgICAgLy8gICBEaXNwbGF5X05hbWU6ICfmjpLkuqfmlbDph48nLA0KICAgICAgICAgIC8vICAgQ29kZTogJ1NjaGR1bGluZ19Db3VudCcNCiAgICAgICAgICAvLyB9KQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlUmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0uQ29tcF9Db2RlID0gJycNCiAgICAgIHRoaXMuZm9ybS5Db21wX0NvZGVCbHVyID0gJycNCiAgICAgIHRoaXMuZm9ybS5UeXBlID0gJycNCiAgICAgIHRoaXMuZm9ybS5TcGVjID0gJycNCiAgICAgIHRoaXMuZm9ybS5JbnN0YWxsVW5pdF9JZCA9IFtdDQogICAgICB0aGlzLmZvcm0uUGFydF9Db2RlQmx1ciA9ICcnDQogICAgICB0aGlzLmZvcm0uUGFydF9Db2RlID0gJycNCiAgICAgIHRoaXMuc2VhcmNoQ29udGVudCA9ICcnDQogICAgICB0aGlzLnNlYXJjaFBhcnRDb250ZW50ID0gJycNCiAgICAgIHRoaXMuaGFuZGxlU2VhcmNoKCkNCiAgICB9LA0KICAgIGFkZFRvTGlzdCgpIHsNCiAgICAgIGlmICghdGhpcy50b3RhbFNlbGVjdGlvbi5sZW5ndGgpIHJldHVybg0KICAgICAgdGhpcy5oYW5kbGVTYXZlKDEpDQogICAgfSwNCiAgICBnZXRJbnN0YWxsVW5pdElkTmFtZUxpc3QoaWQpIHsNCiAgICAgIGlmICghdGhpcy5hcmVhSWQpIHsNCiAgICAgICAgdGhpcy5pbnN0YWxsVW5pdElkTGlzdCA9IFtdDQogICAgICB9IGVsc2Ugew0KICAgICAgICBHZXRJbnN0YWxsVW5pdElkTmFtZUxpc3QoeyBBcmVhX0lkOiB0aGlzLmFyZWFJZCB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgdGhpcy5pbnN0YWxsVW5pdElkTGlzdCA9IHJlcy5EYXRhIHx8IFtdDQogICAgICAgICAgLy8gaWYgKHRoaXMuaW5zdGFsbFVuaXRJZExpc3QubGVuZ3RoKSB7DQogICAgICAgICAgLy8gICB0aGlzLmZvcm0uSW5zdGFsbFVuaXRfSWQgPSBbdGhpcy5pbnN0YWxsVW5pdElkTGlzdFswXS5JZF0NCiAgICAgICAgICAvLyB9DQogICAgICAgIH0pDQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["addDraft.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "addDraft.vue", "sourceRoot": "src/views/PRO/plan-production/schedule-production-new-unit-part/components", "sourcesContent": ["<template>\r\n  <div class=\"contentBox\">\r\n    <div class=\"main-info\">\r\n      <div class=\"left\">\r\n        <ExpandableSection v-model=\"showExpand\" class=\"fff\" :width=\"300\">\r\n          <div class=\"inner-wrapper\">\r\n            <div class=\"tree-search\">\r\n              <el-select\r\n                v-model=\"statusType\"\r\n                clearable\r\n                class=\"search-select\"\r\n                placeholder=\"请选择\"\r\n              >\r\n                <el-option label=\"可排产\" value=\"可排产\" />\r\n                <el-option label=\"排产完成\" value=\"排产完成\" />\r\n                <el-option label=\"未导入\" value=\"未导入\" />\r\n              </el-select>\r\n              <el-input\r\n                v-model.trim=\"projectName\"\r\n                placeholder=\"搜索...\"\r\n                size=\"small\"\r\n                clearable\r\n                suffix-icon=\"el-icon-search\"\r\n              />\r\n            </div>\r\n            <el-divider class=\"cs-divider\" />\r\n            <div class=\"tree-x cs-scroll\">\r\n              <tree-detail\r\n                ref=\"tree\"\r\n                icon=\"icon-folder\"\r\n                is-custom-filter\r\n                :custom-filter-fun=\"customFilterFun\"\r\n                :loading=\"treeLoading\"\r\n                :tree-data=\"treeData\"\r\n                show-status\r\n                show-detail\r\n                :filter-text=\"filterText\"\r\n                :expanded-key=\"expandedKey\"\r\n                @handleNodeClick=\"handleNodeClick\"\r\n              >\r\n                <template #csLabel=\"{showStatus,data}\">\r\n                  <span v-if=\"!data.ParentNodes\" class=\"cs-blue\">({{ data.Code }})</span>{{ data.Label }}\r\n                  <template v-if=\"showStatus\">\r\n                    <span :class=\"['cs-tag',data.Data[statusCode]=='可排产' ? 'greenBg' : data.Data[statusCode]=='排产完成' ?'orangeBg':data.Data[statusCode]=='未导入'?'redBg':'']\">\r\n                      <i\r\n                        v-if=\"data.Data[statusCode]\"\r\n                        :class=\"[data.Data[statusCode]=='可排产' ? 'fourGreen' : data.Data[statusCode]=='排产完成' ?'fourOrange':data.Data[statusCode]=='未导入'?'fourRed':'']\"\r\n                      >\r\n                        {{ data.Data[statusCode] }}\r\n                      </i>\r\n\r\n                    </span>\r\n                  </template>\r\n                </template>\r\n\r\n              </tree-detail>\r\n            </div>\r\n          </div>\r\n        </ExpandableSection>\r\n      </div>\r\n      <div class=\"right\">\r\n\r\n        <el-form ref=\"form\" :model=\"form\" label-width=\"90px\">\r\n          <el-row>\r\n            <!--              <el-col :span=\"12\">\r\n                <el-form-item prop=\"searchContent\" label=\"构件名称\">\r\n                  <el-input\r\n                    v-model=\"searchContent\"\r\n                    clearable\r\n                    class=\"input-with-select w100\"\r\n                    placeholder=\"请输入内容\"\r\n                    size=\"small\"\r\n                  >\r\n                    <el-select\r\n                      slot=\"prepend\"\r\n                      v-model=\"curSearch\"\r\n                      placeholder=\"请选择\"\r\n                      style=\"width: 100px\"\r\n                    >\r\n                      <el-option label=\"精准查询\" :value=\"1\" />\r\n                      <el-option label=\"模糊查询\" :value=\"0\" />\r\n                    </el-select>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>-->\r\n            <el-col :span=\"8\">\r\n              <el-form-item prop=\"searchPartContent\" :label=\"`${levelName}名称`\">\r\n                <el-input\r\n                  v-model=\"searchPartContent\"\r\n                  clearable\r\n                  class=\"input-with-select w100\"\r\n                  placeholder=\"请输入内容\"\r\n                  size=\"small\"\r\n                >\r\n                  <el-select\r\n                    slot=\"prepend\"\r\n                    v-model=\"curPartSearch\"\r\n                    placeholder=\"请选择\"\r\n                    style=\"width: 100px\"\r\n                  >\r\n                    <el-option label=\"精准查询\" :value=\"1\" />\r\n                    <el-option label=\"模糊查询\" :value=\"0\" />\r\n                  </el-select>\r\n                </el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item label=\"规格\" prop=\"Spec\" label-width=\"50px\">\r\n                <el-input\r\n                  v-model=\"form.Spec\"\r\n                  placeholder=\"请输入\"\r\n                  clearable\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item label=\"批次\" label-width=\"50px\" prop=\"Create_UserName\">\r\n                <el-select\r\n                  v-model=\"form.InstallUnit_Id\"\r\n                  filterable\r\n                  clearable\r\n                  multiple\r\n                  style=\"width: 100%\"\r\n                  placeholder=\"请选择\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in installUnitIdList\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Name\"\r\n                    :value=\"item.Id\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label-width=\"0\">\r\n                <el-button style=\"margin-left: 10px\" @click=\"handleReset\">重置</el-button>\r\n                <el-button style=\"margin-left: 10px\" type=\"primary\" @click=\"handleSearch()\">查询</el-button>\r\n                <el-button :loading=\"addLoading\" style=\"margin-left: 10px\" type=\"primary\" @click=\"addToList()\">加入列表</el-button>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n          </el-row>\r\n        </el-form>\r\n\r\n        <div class=\"tb-wrapper\">\r\n          <vxe-table\r\n            ref=\"xTable1\"\r\n            :empty-render=\"{name: 'NotData'}\"\r\n            show-header-overflow\r\n            empty-text=\"暂无数据\"\r\n            height=\"auto\"\r\n            show-overflow\r\n            :checkbox-config=\"{checkField: 'checked', checkMethod: checkCheckboxMethod}\"\r\n            :loading=\"tbLoading\"\r\n            :row-config=\"{isCurrent: true, isHover: true }\"\r\n            class=\"cs-vxe-table\"\r\n            align=\"left\"\r\n            stripe\r\n            :data=\"fTable\"\r\n            resizable\r\n            :edit-config=\"{trigger: 'click', mode: 'cell'}\"\r\n            :tooltip-config=\"{ enterable: true }\"\r\n            @checkbox-all=\"tbSelectChange\"\r\n            @checkbox-change=\"tbSelectChange\"\r\n          >\r\n            <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n            <template v-for=\"item in columns\">\r\n              <vxe-column\r\n                v-if=\"item.Code === 'Is_Component'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <el-tag :type=\"row.Is_Component ? 'danger' : 'success'\">{{\r\n                    row.Is_Component ? \"否\" : \"是\"\r\n                  }}</el-tag>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"['Part_Code','Comp_Code'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                  <el-tag v-if=\"row.Is_Change\" style=\"margin-right: 8px;\" type=\"danger\">变</el-tag>\r\n                  {{ row[item.Code] | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"['Can_Schduling_Count'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <span v-if=\"showSc\">{{ row.csCount | displayValue }}</span>\r\n                  <span v-else>{{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"['Can_Schduling_Weight'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <span v-if=\"showSc\">{{ row.csCountWeight | displayValue }}</span>\r\n                  <span v-else>{{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                show-overflow=\"tooltip\"\r\n                sortable\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                :min-width=\"item.Width\"\r\n              />\r\n            </template>\r\n          </vxe-table>\r\n        </div>\r\n        <div class=\"data-info\">\r\n          <el-tag\r\n            size=\"medium\"\r\n            class=\"info-x\"\r\n          >已选 {{ totalSelection.length }} 条数据\r\n          </el-tag>\r\n          <vxe-pager\r\n            border\r\n            background\r\n            :loading=\"tbLoading\"\r\n            :current-page.sync=\"pageInfo.page\"\r\n            :page-size.sync=\"pageInfo.pageSize\"\r\n            :page-sizes=\"pageInfo.pageSizes\"\r\n            :total=\"pageInfo.total\"\r\n            :layouts=\"['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']\"\r\n            size=\"small\"\r\n            @page-change=\"handlePageChange\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"button\">\r\n      <el-button @click=\"handleClose\">取消</el-button>\r\n      <el-button\r\n        type=\"primary\"\r\n        :disabled=\"!totalSelection.length\"\r\n        :loading=\"saveLoading\"\r\n        @click=\"handleSave(2)\"\r\n      >保存</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { GetCanSchdulingComps } from '@/api/PRO/production-task'\r\nimport { GetCanSchdulingUnits } from '@/api/PRO/production-part'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { debounce, deepClone } from '@/utils'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport { GetCompTypeTree } from '@/api/PRO/professionalType'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\nimport TreeDetail from '@/components/TreeDetail/index.vue'\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport { GetInstallUnitIdNameList, GetProjectAreaTreeList } from '@/api/PRO/project'\r\nimport { getUnique } from '../constant'\r\nimport { mapGetters } from 'vuex'\r\nimport { findAllParentNode } from '@/utils/tree'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nconst SPLIT_SYMBOL = '$_$'\r\n\r\nexport default {\r\n  components: { ExpandableSection, TreeDetail },\r\n  props: {\r\n    scheduleId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    levelName: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    pageType: {\r\n      type: String,\r\n      default: 'com'\r\n    },\r\n    showDialog: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n\r\n    installId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    currentIds: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n\r\n    isPartPrepare: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      pageInfo: {\r\n        page: 1,\r\n        pageSize: 500,\r\n        pageSizes: tablePageSize,\r\n        total: 0\r\n      },\r\n      form: {\r\n        Comp_Code: '',\r\n        Comp_CodeBlur: '',\r\n        Part_CodeBlur: '',\r\n        Part_Code: '',\r\n        InstallUnit_Id: [],\r\n        Spec: '',\r\n        Type: ''\r\n      },\r\n      curSearch: 1,\r\n      curPartSearch: 1,\r\n      showExpand: true,\r\n      searchContent: '',\r\n      searchPartContent: '',\r\n      statusType: '',\r\n      projectName: '',\r\n      expandedKey: '',\r\n      statusCode: 'Part_Schdule_Status',\r\n      isOwnerNull: true,\r\n      tbLoading: false,\r\n      treeLoading: false,\r\n      addLoading: false,\r\n      saveLoading: false,\r\n      showSc: false,\r\n      installUnitIdList: [],\r\n      columns: [],\r\n      fTable: [],\r\n      tbConfig: {},\r\n      TotalCount: 0,\r\n      Page: 0,\r\n      totalSelection: [],\r\n      treeData: [],\r\n      search: () => ({}),\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        clearable: true\r\n      },\r\n      ObjectTypeList: {\r\n        // 构件类型\r\n        'check-strictly': true,\r\n        'default-expand-all': true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      },\r\n      areaId: ''\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    },\r\n    filterText() {\r\n      return this.projectName + SPLIT_SYMBOL + this.statusType\r\n    },\r\n    ...mapGetters('schedule', ['addTbKeys'])\r\n  },\r\n  watch: {\r\n    showDialog(newValue) {\r\n      newValue && (this.saveLoading = false)\r\n    }\r\n  },\r\n  mounted() {\r\n\r\n  },\r\n  methods: {\r\n    initData() {\r\n      console.log('initData')\r\n      this.tbData = []\r\n      this.getConfig()\r\n      this.fetchTreeData()\r\n      this.search = debounce(this.fetchData, 800, true)\r\n      this.setPageData()\r\n    },\r\n    handleNodeClick(data) {\r\n      if (this.areaId === data.Id) {\r\n        return\r\n      }\r\n      if (!data.ParentNodes || data.Children?.length > 0) {\r\n        return\r\n      }\r\n      if (data?.Data[this.statusCode] === '未导入') {\r\n        this.$message({\r\n          message: '清单未导入，请联系深化人员导入清单',\r\n          type: 'warning'\r\n        })\r\n        this.expandedKey = data.Id\r\n        return\r\n      }\r\n\r\n      const setData = ({ Data }) => {\r\n        this.areaId = Data.Id\r\n        this.projectId = Data.Project_Id\r\n        this.expandedKey = this.areaId\r\n        // this.formInline.Finish_Date = ''\r\n        // this.formInline.InstallUnit_Id = ''\r\n        // this.formInline.Remark = ''\r\n        const _arr = findAllParentNode(this.treeData, data.Id, true)\r\n        this.nodeLabels = _arr.filter(v => !!v.ParentNodes).map(p => p.Label)\r\n        this.fetchData()\r\n        // this.getAreaInfo()\r\n        this.getInstallUnitIdNameList()\r\n      }\r\n\r\n      setData(data)\r\n    },\r\n    fetchTreeData() {\r\n      this.treeLoading = true\r\n      GetProjectAreaTreeList({ MenuId: this.$route.meta.Id, projectName: this.projectName, type: 6 }).then((res) => {\r\n        if (!res.IsSucceed) {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.treeData = []\r\n          this.treeLoading = false\r\n          return\r\n        }\r\n        if (res.Data.length === 0) {\r\n          this.treeLoading = false\r\n          return\r\n        }\r\n        const resData = res.Data.map(item => {\r\n          item.Is_Directory = true\r\n          return item\r\n        })\r\n        this.treeData = resData\r\n        console.log('setKey')\r\n        this.setKey()\r\n        this.treeLoading = false\r\n      }).catch(() => {\r\n        this.treeLoading = false\r\n        this.treeData = []\r\n      })\r\n    },\r\n    setKey() {\r\n      const deepFilter = (tree) => {\r\n        console.log('tree', tree)\r\n        for (let i = 0; i < tree.length; i++) {\r\n          const item = tree[i]\r\n          const { Data, Children } = item\r\n          console.log(Data)\r\n          if (Data.ParentId && !Children?.length) {\r\n            this.handleNodeClick(item)\r\n            return\r\n          } else {\r\n            if (Children && Children?.length > 0) {\r\n              return deepFilter(Children)\r\n            }\r\n          }\r\n        }\r\n      }\r\n      return deepFilter(this.treeData)\r\n    },\r\n    customFilterFun(value, data, node) {\r\n      const arr = value.split(SPLIT_SYMBOL)\r\n      const labelVal = arr[0]\r\n      const statusVal = arr[1]\r\n      if (!value) return true\r\n      let parentNode = node.parent\r\n      let labels = [node.label]\r\n      let status = [data.Data[this.statusCode]]\r\n      let level = 1\r\n      while (level < node.level) {\r\n        labels = [...labels, parentNode.label]\r\n        status = [...status, data.Data[this.statusCode]]\r\n        parentNode = parentNode.parent\r\n        level++\r\n      }\r\n      labels = labels.filter(v => !!v)\r\n      status = status.filter(v => !!v)\r\n      let resultLabel = true\r\n      let resultStatus = true\r\n      if (this.statusType) {\r\n        resultStatus = status.some(s => s.indexOf(statusVal) !== -1)\r\n      }\r\n      if (this.projectName) {\r\n        resultLabel = labels.some(s => s.indexOf(labelVal) !== -1)\r\n      }\r\n      return resultLabel && resultStatus\r\n    },\r\n    async getConfig() {\r\n      await this.getTableConfig('PROUnitPartDraftEditTbConfig')\r\n    },\r\n    filterData(page) {\r\n      const splitAndClean = (input) => input.trim().replace(/\\s+/g, ' ').split(' ')\r\n\r\n      if (this.curSearch === 1) {\r\n        this.form.Comp_Code = this.searchContent\r\n        this.form.Comp_CodeBlur = ''\r\n      }\r\n      if (this.curSearch === 0) {\r\n        this.form.Comp_CodeBlur = this.searchContent\r\n        this.form.Comp_Code = ''\r\n      }\r\n      if (this.curPartSearch === 1) {\r\n        this.form.Part_CodeBlur = ''\r\n        this.form.Part_Code = this.searchPartContent\r\n      }\r\n      if (this.curPartSearch === 0) {\r\n        this.form.Part_Code = ''\r\n        this.form.Part_CodeBlur = this.searchPartContent\r\n      }\r\n\r\n      const f = []\r\n      for (const formKey in this.form) {\r\n        if (this.form[formKey] || this.form[formKey] === false) {\r\n          f.push(formKey)\r\n        }\r\n      }\r\n      if (!f.length) {\r\n        this.setPage()\r\n        !page && (this.pageInfo.page = 1)\r\n        this.pageInfo.total = this.tbData.length\r\n        return\r\n      }\r\n\r\n      const checkMatch = (origin, comp) => {\r\n        const _comp = comp.map(code => {\r\n          const [key, value] = code.split('&&&')\r\n          return key\r\n        })\r\n        const _origin = origin.map(code => {\r\n          const [key, value] = code.split('&&&')\r\n          return key\r\n        })\r\n        return _origin.some(item => {\r\n          return _comp.some(value => item.includes(value))\r\n        })\r\n      }\r\n      const checkExactMatch = (origin, comp) => {\r\n        const _comp = comp.map(code => {\r\n          const [key, value] = code.split('&&&')\r\n          return key\r\n        })\r\n        const _origin = origin.map(code => {\r\n          const [key, value] = code.split('&&&')\r\n          return key\r\n        })\r\n\r\n        return _origin.some(item => _comp.includes(item))\r\n      }\r\n\r\n      const temTbData = this.tbData.filter(v => {\r\n        v.checked = false\r\n        const compCode = v.Component_Codes || []\r\n\r\n        if (this.form.Comp_Code.trim()) {\r\n          const compCodeArray = splitAndClean(this.form.Comp_Code)\r\n          if (compCodeArray.length) {\r\n            const flag = checkExactMatch(compCode, compCodeArray)\r\n            console.log(887, compCode, compCodeArray, flag)\r\n            if (!flag) return false\r\n          }\r\n        }\r\n\r\n        if (this.form.Comp_CodeBlur.trim()) {\r\n          const compCodeArray = splitAndClean(this.form.Comp_CodeBlur)\r\n          if (compCodeArray.length) {\r\n            const flag = checkMatch(compCode, compCodeArray)\r\n            if (!flag) return false\r\n          }\r\n        }\r\n\r\n        if (this.form.Type && v.Type !== this.form.Type) {\r\n          return false\r\n        }\r\n\r\n        if (this.form.Part_CodeBlur.trim()) {\r\n          const partCodeBlurArray = splitAndClean(this.form.Part_CodeBlur)\r\n          if (!partCodeBlurArray.some(code => v['Part_Code'].includes(code))) {\r\n            return false\r\n          }\r\n        }\r\n\r\n        if (this.form.Part_Code.trim()) {\r\n          const partCodeArray = splitAndClean(this.form.Part_Code)\r\n          if (!partCodeArray.includes(v['Part_Code'])) {\r\n            return false\r\n          }\r\n        }\r\n\r\n        if (this.form.InstallUnit_Id.length && !this.form.InstallUnit_Id.includes(v.InstallUnit_Id)) {\r\n          return false\r\n        }\r\n\r\n        if (this.form.Spec.trim() !== '') {\r\n          const specArray = splitAndClean(this.form.Spec)\r\n          if (!specArray.some(spec => v.Spec.includes(spec))) {\r\n            return false\r\n          }\r\n        }\r\n        if (this.searchContent.trim().length) {\r\n          let csCount = 0\r\n\r\n          v.componentMap = (v.Component_Codes || []).reduce((acc, code) => {\r\n            const [key, value] = code.split('&&&')\r\n            acc[key] = parseInt(value)\r\n            if (this.curSearch === 1) {\r\n              const compCodeArray = splitAndClean(this.form.Comp_Code)\r\n              if (compCodeArray.length) {\r\n                const flag = checkExactMatch([key], compCodeArray)\r\n                if (flag) {\r\n                  csCount += parseInt(value)\r\n                }\r\n              }\r\n            } else {\r\n              const compCodeArray = splitAndClean(this.form.Comp_CodeBlur)\r\n              if (compCodeArray.length) {\r\n                const flag = checkMatch([key], compCodeArray)\r\n                console.log('pflag', key, compCodeArray, flag, value)\r\n                if (flag) {\r\n                  csCount += parseInt(value)\r\n                }\r\n              }\r\n            }\r\n            return acc\r\n          }, {})\r\n          this.$set(v, 'csCount', Math.min(csCount, v.Can_Schduling_Count))\r\n          this.$set(v, 'csCountWeight', Math.min(v.Can_Schduling_Weight, v.csCount * v.Weight))\r\n\r\n          v.searchcount = v.count\r\n          v.searchcountMax = v.maxCount\r\n          // const cs = v.Component_Codes || []\r\n          // let min = 0\r\n          // cs.forEach((element, idx) => {\r\n          //   const [key, value] = element.split('&&&')\r\n          //   min = v.componentMap[key]\r\n          // })\r\n\r\n          v.count = v.csCount\r\n        } else {\r\n          v.count = v.Can_Schduling_Count\r\n        }\r\n\r\n        // v.Can_Schduling_Count = v.csCount\r\n        // v.Can_Schduling_Weight = v.csCountWeight\r\n\r\n        return true\r\n      })\r\n\r\n      !page && (this.pageInfo.page = 1)\r\n      this.pageInfo.total = temTbData.length\r\n      this.setPage(temTbData)\r\n      if (this.searchContent.trim().length) {\r\n        this.showSc = true\r\n      }\r\n    },\r\n    handleSearch() {\r\n      this.totalSelection = []\r\n      this.clearSelect()\r\n      if (this.tbData?.length) {\r\n        this.tbData.forEach(item => item.checked = false)\r\n        this.filterData()\r\n      }\r\n      this.showSc = !!this.searchContent.trim().length\r\n    },\r\n    tbSelectChange(array) {\r\n      this.totalSelection = this.tbData.filter(v => v.checked)\r\n    },\r\n    clearSelect() {\r\n      this.$refs.xTable1.clearCheckboxRow()\r\n      this.totalSelection = []\r\n    },\r\n    async fetchData() {\r\n      this.handleReset()\r\n      this.tbLoading = true\r\n      if (this.isCom) {\r\n        await this.getComTbData()\r\n      } else {\r\n        await this.getPartTbData()\r\n      }\r\n      this.initTbData()\r\n      this.filterData()\r\n      this.tbLoading = false\r\n    },\r\n    setPageData() {\r\n      if (this.tbData?.length) {\r\n        this.pageInfo.page = 1\r\n        this.tbData = this.tbData.filter(v => v.Can_Schduling_Count > 0)\r\n        this.filterData()\r\n      }\r\n    },\r\n    handleSave(type = 2) {\r\n      if (type === 1) {\r\n        this.addLoading = true\r\n      } else {\r\n        this.saveLoading = true\r\n      }\r\n      setTimeout(() => {\r\n        this.totalSelection.forEach((item) => {\r\n          const intCount = parseInt(item.count)\r\n          if (this.searchContent.trim().length) {\r\n            item.Schduled_Count = item.Can_Schduling_Count\r\n\r\n            item.maxCount = item.Can_Schduling_Count\r\n            item.chooseCount = intCount\r\n            item.count = item.Can_Schduling_Count\r\n\r\n            item.Can_Schduling_Count = 0\r\n            item.Can_Schduling_Weight = item.Can_Schduling_Count * item.Weight\r\n          } else {\r\n            item.Schduled_Count += intCount\r\n            item.Can_Schduling_Count -= intCount\r\n            item.Can_Schduling_Weight = item.Can_Schduling_Count * item.Weight\r\n            item.maxCount = intCount\r\n            item.chooseCount = intCount\r\n            item.count = item.Can_Schduling_Count\r\n          }\r\n\r\n          item.checked = false\r\n        })\r\n        const cp = deepClone(this.totalSelection)\r\n\r\n        // this.$emit('sendSelectList', cp)\r\n        this.addLoading = false\r\n        this.clearSelect()\r\n        // this.setPage()\r\n        this.setPageData()\r\n        if (type === 2) {\r\n          this.$emit('sendSelectList', cp)\r\n          this.$emit('close')\r\n          this.fTable = []\r\n          this.tbData = []\r\n        } else {\r\n          this.$emit('addToTbList', cp)\r\n        }\r\n      }, 0)\r\n    },\r\n    initTbData() {\r\n      // 设置文本框选择的排产数量,设置自定义唯一码\r\n      const objKey = {}\r\n      if (!this.tbData?.length) {\r\n        this.tbData = []\r\n        // this.backendTb = []\r\n        return\r\n      }\r\n      console.log(998, JSON.parse(JSON.stringify(this.tbData)))\r\n      // this.backendTb = deepClone(this.tbData)\r\n      this.tbData = this.tbData.filter(item => {\r\n        this.$set(item, 'count', item.Can_Schduling_Count)\r\n        this.$set(item, 'maxCount', item.Can_Schduling_Count)\r\n        item.uuid = getUnique(this.isCom, item)\r\n        objKey[item.Type] = true\r\n        // let csCount = 0\r\n        // item.componentMap = (item.Component_Codes || []).reduce((acc, code) => {\r\n        //   const [key, value] = code.split('&&&')\r\n        //   acc[key] = parseInt(value)\r\n        //   csCount += parseInt(value)\r\n        //   return acc\r\n        // }, {})\r\n        // this.$set(item, 'csCount', csCount)\r\n        // Object.keys(item.componentMap).forEach(key => {\r\n        //   this.$set(item, key, item.componentMap[key])\r\n        // })\r\n\r\n        return !this.addTbKeys.includes(item.uuid)\r\n      })\r\n      //   .map((item) => {\r\n      //   this.$set(item, 'count', item.Can_Schduling_Count)\r\n      //   this.$set(item, 'maxCount', item.Can_Schduling_Count)\r\n      //   // item.uuid = uuidv4()\r\n      //   item.uuid = item.InstallUnit_Id + item.Part_Aggregate_Id\r\n      //   objKey[item.Type] = true\r\n      //\r\n      //   const _selectList = this.selectTbData.filter(v => v.puuid)\r\n      //   console.log('_selectList', _selectList)\r\n      //   // _selectList.forEach((element, idx) => {\r\n      //   //   if(element.puuid === item.uuid){\r\n      //   //\r\n      //   //   }\r\n      //   // })\r\n      //   return item\r\n      // })\r\n\r\n      // this.backendTb = deepClone(this.tbData)\r\n    },\r\n    async getComTbData() {\r\n      // const { install, areaId } = this.$route.query\r\n      const { Comp_Codes, ...obj } = this.form\r\n      let codes = []\r\n      if (Object.prototype.toString.call(Comp_Codes) === '[object String]') {\r\n        codes = Comp_Codes && Comp_Codes.split(' ').filter(v => !!v)\r\n      }\r\n      await GetCanSchdulingComps({\r\n        Ids: this.currentIds,\r\n        ...obj,\r\n        Schduling_Plan_Id: this.scheduleId,\r\n        Comp_Codes: codes,\r\n        InstallUnit_Id: this.installId,\r\n        Area_Id: this.areaId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.pageInfo.total = res.Data.length\r\n          this.tbData = res.Data.map((v, idx) => {\r\n            // 已排产赋值\r\n            v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n            v.Workshop_Id = v.Scheduled_Workshop_Id\r\n            v.Workshop_Name = v.Scheduled_Workshop_Name\r\n            v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n            // if (v.originalPath) {\r\n            // v.isDisabled = true\r\n            // }\r\n            v.checked = false\r\n            v.initRowIndex = idx\r\n            // v.technologyPathDisabled = !!v.Technology_Path\r\n            return v\r\n          })\r\n          this.setPage()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 分页\r\n     */\r\n    handlePageChange({ currentPage, pageSize }) {\r\n      if (this.tbLoading) return\r\n      this.pageInfo.page = currentPage\r\n      this.pageInfo.pageSize = pageSize\r\n      this.setPage()\r\n      this.filterData(currentPage)\r\n    },\r\n\r\n    setPage(tb = this.tbData) {\r\n      this.fTable = tb.slice((this.pageInfo.page - 1) * this.pageInfo.pageSize, this.pageInfo.page * this.pageInfo.pageSize)\r\n    },\r\n\r\n    async getPartTbData() {\r\n      // const { install, areaId } = this.$route.query\r\n      await GetCanSchdulingUnits({\r\n        Ids: this.currentIds,\r\n        ...this.form,\r\n        Schduling_Plan_Id: this.scheduleId,\r\n        InstallUnit_Id: this.installId,\r\n        Area_Id: this.areaId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.pageInfo.total = res.Data.length\r\n          this.tbData = res.Data.map((v, idx) => {\r\n            v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n            v.Workshop_Id = v.Scheduled_Workshop_Id\r\n            v.Workshop_Name = v.Scheduled_Workshop_Name\r\n            if (v.Comp_Import_Detail_Id) {\r\n              v.Part_Used_Process = this.getPartUsedProcess(v)\r\n            }\r\n            v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n            // v.isDisabled = !!v.originalPath\r\n            v.checked = false\r\n            v.initRowIndex = idx\r\n            v.Area_Name = this.nodeLabels.join('/')\r\n            // v.partUsedProcessDisabled = this.isPartPrepare ? !!v.Part_Used_Process : false\r\n            // v.technologyPathDisabled = !!v.Technology_Path\r\n            if (!this.isPartPrepare) {\r\n              v.Temp_Part_Used_Process = v.Part_Used_Process\r\n            }\r\n            return v\r\n          })\r\n          this.setPartColumn()\r\n          this.setPage()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      const submitObj = this.tbData.map(item => {\r\n        return {\r\n          Id: item.Part_Aggregate_Id,\r\n          Type: 3\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = !!item.Is_Stop\r\n          })\r\n          this.tbData.forEach(row => {\r\n            if (stopMap.hasOwnProperty(row.Part_Aggregate_Id)) {\r\n              this.$set(row, 'stopFlag', stopMap[row.Part_Aggregate_Id])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    checkCheckboxMethod({ row }) {\r\n      return !row.stopFlag\r\n    },\r\n    getPartUsedProcess(item) {\r\n      if (item.Scheduled_Used_Process) {\r\n        return item.Scheduled_Used_Process\r\n      }\r\n      if (item.Component_Technology_Path) {\r\n        const list = item.Component_Technology_Path.split('/')\r\n        if (list.includes(item.Part_Used_Process)) {\r\n          return item.Part_Used_Process\r\n        } else if (list.includes(item.Part_Type_Used_Process)) {\r\n          return item.Part_Type_Used_Process\r\n        }\r\n      } else {\r\n        if (item.Part_Used_Process) {\r\n          return item.Part_Used_Process\r\n        } else if (item.Part_Type_Used_Process) {\r\n          return item.Part_Type_Used_Process\r\n        }\r\n      }\r\n\r\n      return ''\r\n    },\r\n    setPartColumn() {\r\n      // 纯零件\r\n      this.isOwnerNull = this.tbData.every(v => !v.Comp_Import_Detail_Id)\r\n      console.log('this.isOwnerNull', this.isOwnerNull)\r\n      if (this.isOwnerNull) {\r\n        const idx = this.columns.findIndex(v => v.Code === 'Component_Code')\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n      }\r\n    },\r\n    mergeData(list) {\r\n      /*      console.log('list', list)\r\n      console.log('this.backendTb', this.backendTb)\r\n      list\r\n        .forEach((element, index) => {\r\n          const idx = this.backendTb.findIndex(\r\n            (item) => element.puuid && item.uuid === element.puuid\r\n          )\r\n          console.log('idx', idx, this.backendTb[idx])\r\n          console.log('index', index)\r\n          if (idx !== -1) {\r\n            this.tbData.splice(idx, 0, deepClone(this.backendTb[idx]))\r\n          }\r\n        })\r\n\r\n      this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n      console.log('this.tbData', JSON.parse(JSON.stringify(this.tbData)))\r\n\r\n      this.filterData()*/\r\n    },\r\n    handleClose() {\r\n      this.$emit('close')\r\n    },\r\n    // activeCellMethod({ row, column, columnIndex }) {\r\n    //   return column.field === 'Schduling_Count'\r\n    // },\r\n    async getTableConfig(code) {\r\n      await GetGridByCode({\r\n        code\r\n      }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n          this.pageInfo.pageSize = Number(this.tbConfig.Row_Number)\r\n          const list = Data.ColumnList || []\r\n          this.columns = list.filter(v => v.Is_Display)\r\n            .map(item => {\r\n              if (item.Is_Frozen) {\r\n                item.fixed = 'left'\r\n              }\r\n              return item\r\n            })\r\n          // this.columns.push({\r\n          //   Display_Name: '排产数量',\r\n          //   Code: 'Schduling_Count'\r\n          // })\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.form.Comp_Code = ''\r\n      this.form.Comp_CodeBlur = ''\r\n      this.form.Type = ''\r\n      this.form.Spec = ''\r\n      this.form.InstallUnit_Id = []\r\n      this.form.Part_CodeBlur = ''\r\n      this.form.Part_Code = ''\r\n      this.searchContent = ''\r\n      this.searchPartContent = ''\r\n      this.handleSearch()\r\n    },\r\n    addToList() {\r\n      if (!this.totalSelection.length) return\r\n      this.handleSave(1)\r\n    },\r\n    getInstallUnitIdNameList(id) {\r\n      if (!this.areaId) {\r\n        this.installUnitIdList = []\r\n      } else {\r\n        GetInstallUnitIdNameList({ Area_Id: this.areaId }).then(res => {\r\n          this.installUnitIdList = res.Data || []\r\n          // if (this.installUnitIdList.length) {\r\n          //   this.form.InstallUnit_Id = [this.installUnitIdList[0].Id]\r\n          // }\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n@import \"~@/styles/mixin.scss\";\r\n.cs-divider{\r\n  margin:16px 0 0 0;\r\n}\r\n.contentBox {\r\n  height: 75vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .main-info{\r\n    display: flex;\r\n    overflow: hidden;\r\n    flex: 1;\r\n    .left{\r\n      height: 100%;\r\n      margin-right: 16px;\r\n      border: 1px solid #eee;\r\n      .cs-tag{\r\n        margin-left: 8px;\r\n        font-size: 12px;\r\n        padding:2px 4px;\r\n        border-radius: 1px;\r\n      }\r\n\r\n      .inner-wrapper {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n        padding: 16px;\r\n        border-radius: 4px;\r\n        overflow: hidden;\r\n\r\n        .tree-search {\r\n          display: flex;\r\n\r\n          .search-select {\r\n            margin-right: 8px;\r\n          }\r\n        }\r\n\r\n        .tree-x {\r\n          overflow: hidden;\r\n          margin-top: 16px;\r\n          flex: 1;\r\n\r\n          .el-tree {\r\n            height: 100%;\r\n          }\r\n        }\r\n\r\n        .cs-scroll {\r\n          overflow-y: auto;\r\n          @include scrollBar;\r\n        }\r\n\r\n      }\r\n    }\r\n    .right{\r\n      overflow: hidden;\r\n      flex: 1;\r\n      display: flex;\r\n      flex-direction: column;\r\n      border: 1px solid #eee;\r\n      padding:16px;\r\n    }\r\n\r\n  }\r\n\r\n  .button {\r\n    margin-top: 16px;\r\n    display: flex;\r\n    justify-content: end;\r\n  }\r\n\r\n  .tb-wrapper {\r\n    flex: 1 1 auto;\r\n  }\r\n\r\n  .data-info{\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-top: 16px;\r\n  }\r\n}\r\n.fourGreen {\r\n  color: #00C361;\r\n  font-style: normal;\r\n}\r\n\r\n.fourOrange {\r\n  color: #FF9400;\r\n  font-style: normal;\r\n}\r\n\r\n.fourRed {\r\n  color: #FF0000;\r\n  font-style: normal;\r\n}\r\n\r\n.cs-blue {\r\n  color: #5AC8FA;\r\n}\r\n\r\n.orangeBg{\r\n  background: rgba(255,148,0,0.1);\r\n}\r\n\r\n.redBg{\r\n  background: rgba(252,107,127,0.1);\r\n}\r\n.greenBg{\r\n  background: rgba(0, 195, 97, 0.10);\r\n}\r\n.cs-input-x{\r\n  display: flex;\r\n}\r\n</style>\r\n"]}]}
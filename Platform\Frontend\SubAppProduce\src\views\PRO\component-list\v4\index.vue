<template>
  <div class="app-container abs100">
    <div
      v-loading="pgLoading"
      class="h100 app-wrapper"
      element-loading-text="加载中"
    >
      <ExpandableSection v-model="showExpand" :width="300" class="cs-left fff">
        <div class="inner-wrapper">
          <div class="tree-search">
            <el-select
              v-model="statusType"
              clearable
              class="search-select"
              placeholder="导入状态选择"
            >
              <el-option label="已导入" value="已导入" />
              <el-option label="未导入" value="未导入" />
              <el-option label="已变更" value="已变更" />
            </el-select>
            <el-input
              v-model.trim="projectName"
              placeholder="关键词搜索"
              size="small"
              clearable
              suffix-icon="el-icon-search"
              @blur="fetchTreeDataLocal"
              @clear="fetchTreeDataLocal"
              @keydown.enter.native="fetchTreeDataLocal"
            />
          </div>
          <el-divider class="cs-divider" />
          <div class="tree-x cs-scroll">
            <tree-detail
              ref="tree"
              icon="icon-folder"
              is-custom-filter
              :custom-filter-fun="customFilterFun"
              :loading="treeLoading"
              :tree-data="treeData"
              show-status
              show-detail
              :filter-text="filterText"
              :expanded-key="expandedKey"
              @handleNodeClick="handleNodeClick"
            >
              <template #csLabel="{ showStatus, data }">
                <span
                  v-if="!data.ParentNodes"
                  class="cs-blue"
                >({{ data.Code }})</span>{{ data.Label }}
                <template v-if="showStatus && data.Label != '全部'">
                  <span v-if="data.Data.Is_Deepen_Change" class="cs-tag redBg">
                    <i class="fourRed">已变更</i></span>
                  <span
                    v-else
                    :class="[
                      'cs-tag',
                      data.Data.Is_Imported == true ? 'greenBg' : 'orangeBg',
                    ]"
                  >
                    <i
                      :class="[
                        data.Data.Is_Imported == true
                          ? 'fourGreen'
                          : 'fourOrange',
                      ]"
                    >{{
                      data.Data.Is_Imported == true ? "已导入" : "未导入"
                    }}</i>
                  </span>
                </template>
              </template>
            </tree-detail>
          </div>
        </div>
      </ExpandableSection>
      <div class="cs-right">
        <div ref="searchDom" class="cs-from">
          <div class="cs-search">
            <el-form
              ref="customParams"
              :model="customParams"
              label-width="80px"
              class="demo-form-inline"
            >
              <el-row>
                <el-col :span="6" :lg="6" :xl="6">
                  <el-form-item :label="levelName + '名称'" prop="Names">
                    <el-input
                      v-model="names"
                      clearable
                      style="width: 100%"
                      class="input-with-select"
                      placeholder="请输入内容"
                      size="small"
                    >
                      <el-select
                        slot="prepend"
                        v-model="nameMode"
                        placeholder="请选择"
                        style="width: 100px"
                      >
                        <el-option label="模糊搜索" :value="1" />
                        <el-option label="精确搜索" :value="2" />
                      </el-select>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="5" :lg="5" :xl="6">
                  <el-form-item
                    label-width="60px"
                    class="mb0"
                    label="批次"
                    prop="InstallUnit_Ids"
                  >
                    <el-select
                      v-model="customParams.InstallUnit_Ids"
                      filterable
                      clearable
                      multiple
                      placeholder="请选择"
                      style="width: 100%"
                      :disabled="!Boolean(customParams.Area_Id)"
                    >
                      <el-option
                        v-for="item in installUnitIdNameList"
                        :key="item.Id"
                        :label="item.Name"
                        :value="item.Id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4" :lg="5" :xl="4">
                  <el-form-item label-width="92px" prop="SteelType">
                    <template #label><span>{{ levelName + '类型' }}</span></template>
                    <el-tree-select
                      ref="treeSelectObjectType"
                      v-model="customParams.SteelType"
                      class="cs-tree-x"
                      :select-params="treeSelectParams"
                      :tree-params="ObjectTypeList"
                      value-key="Id"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="4" :lg="4" :xl="4">
                  <el-form-item :label="levelName + '号'" prop="SteelNumber">
                    <el-input
                      v-model="customParams.SteelNumber"
                      placeholder="请输入"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="4" :lg="4" :xl="4">
                  <el-form-item :label="levelName + '序号'" prop="SteelCode">
                    <el-input
                      v-model="customParams.SteelCode"
                      placeholder="请输入"
                      clearable
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="6" :lg="6" :xl="6">
                  <el-form-item label="规格" prop="Spec">
                    <el-input
                      v-model="customParams.Spec"
                      placeholder="请输入"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="5" :lg="5" :xl="6">
                  <el-form-item label-width="60px" label="材质" prop="Texture">
                    <el-input
                      v-model="customParams.Texture"
                      placeholder="请输入"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="4" :lg="5" :xl="4">
                  <el-form-item
                    label-width="92px"
                    class="mb0"
                    label="是否直发件"
                    prop="Is_Direct"
                  >
                    <el-select
                      v-model="customParams.Is_Direct"
                      style="width: 100%"
                      placeholder="请选择"
                      clearable
                    >
                      <el-option label="全部" value="" />
                      <el-option label="是" :value="true" />
                      <el-option label="否" :value="false" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4" :lg="4" :xl="4">
                  <el-form-item label="操作人" prop="Create_UserName">
                    <el-input
                      v-model="customParams.Create_UserName"
                      style="width: 100%"
                      placeholder="请输入"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="4" :lg="4" :xl="4">
                  <el-form-item class="mb0" label-width="16px">
                    <el-button
                      type="primary"
                      @click="handleSearch()"
                    >搜索
                    </el-button>
                    <el-button @click="handleSearch('reset')">重置</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </div>

        <div class="fff cs-z-tb-wrapper">
          <div class="cs-button-box">
            <div>
              <el-dropdown
                trigger="click"
                placement="bottom-start"
                @command="handleCommand($event, 1)"
              >
                <el-button
                  type="primary"
                  :disabled="!currentLastLevel"
                >多级清单导入
                  <i class="el-icon-arrow-down el-icon--right" />
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    :disabled="allStopFlag"
                    command="add"
                  >新增导入</el-dropdown-item>
                  <el-dropdown-item
                    :disabled="allStopFlag"
                    command="cover"
                  >覆盖导入</el-dropdown-item>
                  <el-dropdown-item
                    :disabled="allStopFlag"
                    command="halfcover"
                  >部分覆盖导入</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <!-- <el-button
                  type="primary"
                  @click="deepListImport(1)"
                >构件/零件导入</el-button>
                <el-button
                  type="primary"
                  @click="deepListImport(0)"
                >构件导入</el-button> -->
              <el-button
                type="primary"
                @click="modelListImport"
              >导入模型清单
              </el-button>
              <el-button
                type="primary"
                @click="LocationImport"
              >位置信息导入
              </el-button>
              <el-button
                v-if="!isVersionFour"
                :disabled="!selectList.length"
                @click="handleSchedulingInfoExport"
              >导出排产单模板
              </el-button>
              <el-button
                v-if="!isVersionFour"
                @click="handleSteelExport(2)"
              >导出构件</el-button>
              <el-dropdown
                v-else
                trigger="click"
                placement="bottom-start"
                @command="handleExport"
              >
                <el-button
                  type="primary"
                  :disabled="!currentLastLevel"
                >导出
                  <i class="el-icon-arrow-down el-icon--right" />
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="com">纯构件</el-dropdown-item>
                  <el-dropdown-item command="all">完整清单</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-button @click="handleHistoryExport">历史清单导出</el-button>
              <el-button
                :loading="scheduleLoading"
                :disabled="!selectList.length"
                @click="handleScheduleExport"
              >排产单导出</el-button>
              <el-button
                :disabled="
                  !selectList.length || selectList.some((item) => item.stopFlag)
                "
                type="primary"
                plain
                @click="handleBatchEdit"
              >批量编辑
              </el-button>
              <el-button
                type="danger"
                plain
                :disabled="
                  !selectList.length || selectList.some((item) => item.stopFlag)
                "
                @click="handleDelete"
              >删除选中
              </el-button>
              <el-button
                type="success"
                plain
                :disabled="!Boolean(customParams.Sys_Project_Id)"
                @click="handelImport"
              >图纸导入
              </el-button>
            </div>
            <div v-if="showTotalLength" class="cs-length">
              <span class="txt-green">累计长度：{{ deepenTotalLength }}</span>
            </div>
          </div>
          <div class="info-box">
            <div class="cs-col">
              <span>
                <span class="info-label">深化总数</span>
                <i>{{ SteelAmountTotal }} 件</i>
              </span>
              <span><span class="info-label">深化总量</span>
                <i>{{ SteelAllWeightTotal }} t</i></span>
            </div>
            <div class="cs-col">
              <span><span class="info-label">排产总数</span>
                <i>{{ SchedulingNumTotal }} 件</i></span>
              <span><span class="info-label">排产总量</span>
                <i>{{ SchedulingAllWeightTotal }} t</i></span>
            </div>
            <div class="cs-col" style="cursor: pointer;" @click="getProcessData()">
              <span><span class="info-label">完成总数</span>
                <i>{{ FinishCountTotal }} 件</i></span>
              <span><span class="info-label">完成总量</span>
                <i>{{ FinishWeightTotal }} t</i></span>
            </div>
            <div class="cs-col">
              <span><span class="info-label">直发件总数</span>
                <i>{{ IsComponentTotal }} 件</i></span>
              <span><span class="info-label">直发件总量</span>
                <i>{{ IsComponentTotalSteelAllWeight }} t</i></span>
            </div>
            <div class="cs-col">
              <span><span class="info-label">毛重合计</span>
                <i>{{ TotalGrossWeightT }} t</i></span>
            </div>
          </div>
          <div class="tb-container">
            <vxe-table
              v-loading="tbLoading"
              :empty-render="{ name: 'NotData' }"
              show-header-overflow
              element-loading-spinner="el-icon-loading"
              element-loading-text="拼命加载中"
              empty-text="暂无数据"
              class="cs-vxe-table"
              height="auto"
              auto-resize
              align="left"
              stripe
              :data="tbData"
              resizable
              :tooltip-config="{ enterable: true }"
              :row-config="{ isHover: true }"
              @checkbox-all="tbSelectChange"
              @checkbox-change="tbSelectChange"
            >
              <vxe-column fixed="left" type="checkbox" width="44" />
              <vxe-column
                v-for="(item, index) in columns"
                :key="index"
                :fixed="item.Is_Frozen ? item.Frozen_Dirction : ''"
                show-overflow="tooltip"
                sortable
                :align="item.Align"
                :field="item.Code"
                :title="item.Display_Name"
                :min-width="item.Width ? item.Width : 120"
              >
                <!-- <template #default="{ row }">
                  <div v-if="item.Code == 'Is_Component'">
                      <span v-if="row.Is_Component === 'True'">否</span>
                      <span v-else-if="row.Is_Component === 'False'">是</span>
                      <span v-else>-</span>
                    </div>
                </template> -->
                <template #default="{ row }">
                  <div v-if="item.Code == 'SteelName'">
                    <el-tag
                      v-if="row.Is_Change"
                      style="margin-right: 8px"
                      type="danger"
                    >变</el-tag>
                    <el-tag
                      v-if="row.stopFlag"
                      style="margin-right: 8px"
                      type="danger"
                    >停</el-tag>
                    <!-- :class="[{ isPicActive: row.Drawing !== '暂无' }]" -->
                    <span
                      class="isPicActive"
                      @click="getComponentInfo(row)"
                    >
                      {{ row[item.Code] | displayValue }}</span>
                  </div>
                  <div v-else-if="item.Code == 'SteelAmount'">
                    <span
                      v-if="row.Is_Component_Status == true"
                      style="color: #298dff"
                    >
                      {{ row[item.Code] | displayValue }} 件</span>
                    <span
                      v-else
                      style="color: #298dff; cursor: pointer"
                      @click="handleViewModel(row)"
                    >
                      {{ row[item.Code] | displayValue }} 件</span>
                  </div>
                  <div v-else-if="item.Code == 'SchedulingNum'">
                    <span
                      v-if="row[item.Code]"
                      style="color: #298dff; cursor: pointer"
                      @click="handleViewScheduling(row)"
                    >{{ row[item.Code] + " 件" }}</span>
                    <span v-else>-</span>
                  </div>
                  <div v-else-if="item.Code == 'SH'">
                    <el-link
                      type="primary"
                      @click="handleViewSH(row, 0)"
                    >查看
                    </el-link>
                  </div>
                  <div v-else-if="item.Code == 'Part'">
                    <el-link
                      type="primary"
                      @click="handleViewPart(row)"
                    >查看
                    </el-link>
                  </div>
                  <div v-else-if="item.Code == 'Is_Component'">
                    <span>
                      <!--                      这玩意叫 是否是直发件 -->
                      <el-tag
                        v-if="row.Is_Component === 'True'"
                        type="danger"
                      >否</el-tag>
                      <el-tag v-else type="success">是</el-tag>
                    </span>
                  </div>
                  <div v-else-if="item.Code == 'Is_Component_Status'">
                    <span>
                      <el-tag
                        v-if="row.Is_Component === 'True'"
                        type="danger"
                      >否</el-tag>
                      <el-tag v-else type="success">是</el-tag>
                    </span>
                  </div>
                  <div v-else-if="item.Code == 'Drawing'">
                    <span
                      v-if="row.Drawing !== '暂无'"
                      style="color: #298dff; cursor: pointer"
                      @click="getComponentInfo(row)"
                    >
                      {{ row[item.Code] | displayValue }}
                    </span>
                    <span v-else> {{ row[item.Code] | displayValue }}</span>
                  </div>

                  <div v-else>
                    <span>{{ row[item.Code] || "-" }}</span>
                  </div>
                </template>
              </vxe-column>

              <vxe-column
                fixed="right"
                align="left"
                title="操作"
                width="150"
                show-overflow
              >
                <template #default="{ row }">
                  <el-button
                    type="text"
                    @click="handleView(row)"
                  >详情
                  </el-button>
                  <el-button
                    :disabled="row.stopFlag"
                    type="text"
                    @click="handleEdit(row)"
                  >编辑
                  </el-button>
                  <el-button
                    type="text"
                    @click="handleTrack(row)"
                  >轨迹图
                  </el-button>
                </template>
              </vxe-column>
            </vxe-table>
          </div>
          <div class="cs-bottom">
            <Pagination
              class="cs-table-pagination"
              :total="total"
              max-height="100%"
              :page-sizes="tablePageSize"
              :page.sync="queryInfo.Page"
              :limit.sync="queryInfo.PageSize"
              layout="total, sizes, prev, pager, next, jumper"
              @pagination="changePage"
            >
              <!--                  <span class="pg-input">
                      <el-select
                        v-model.number="queryInfo.PageSize"
                        allow-create
                        filterable
                        default-first-option
                        @change="changePage"
                      >
                        <el-option v-for="(item,index) in customPageSize" :key="index" :label="`${item}条/页`" :value="item" />
                      </el-select>
                    </span>-->
            </Pagination>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      v-if="dialogVisible"
      ref="content"
      v-el-drag-dialog
      :title="title"
      :visible.sync="dialogVisible"
      :width="width"
      class="z-dialog"
      @close="handleClose"
    >
      <component
        :is="currentComponent"
        ref="content"
        :select-list="selectList"
        :custom-params="customDialogParams"
        :type-id="customParams.TypeId"
        :type-entity="typeEntity"
        :params-steel="treeParamsSteel"
        :project-id="customParams.Project_Id"
        :sys-project-id="customParams.Sys_Project_Id"
        @close="handleClose"
        @refresh="fetchData"
        @checkPackage="handleComponentPack"
        @checkSteelMeans="handleSteelMeans"
        @checkModelList="handleSteelExport"
        @locationExport="locationExport"
      />
    </el-dialog>
    <bimdialog
      ref="dialog"
      :is-auto-split="isAutoSplit"
      :type-entity="typeEntity"
      @getData="fetchData"
      @getProjectAreaData="fetchTreeData"
    />
    <el-drawer
      :visible.sync="trackDrawer"
      direction="rtl"
      size="30%"
      destroy-on-close
      custom-class="trackDrawerClass"
    >
      <template #title>
        <div>
          <span>{{ trackDrawerTitle }}</span>
          <span style="margin-left: 24px">{{
            trackDrawerData.SteelAmount
          }}</span>
        </div>
      </template>
      <TracePlot :track-drawer-data="trackDrawerData" />
    </el-drawer>

    <comDrawdialog ref="comDrawdialogRef" @getData="fetchData" />
    <modelDrawing ref="modelDrawingRef" type="构件" />
  </div>
</template>

<script>
import { GetPreferenceSettingValue } from '@/api/sys/system-setting'
import { GetGridByCode } from '@/api/sys'
import { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'
import {
  GetComponentImportDetailPageList,
  DeleteComponents,
  DeleteAllComponentWithQuery,
  GetComponentSummaryInfo,
  ExportComponentInfo,
  ExportComponentSchedulingInfo,
  ExportThreeBom,
  ExportDeepenFullSchedulingInfo
} from '@/api/PRO/component'
import {
  GetProjectAreaTreeList,
  GetInstallUnitIdNameList
} from '@/api/PRO/project'
import { getConfigure } from '@/api/user'
import { GetCompTypeTree } from '@/api/PRO/component-type'
import { GetSteelCadAndBimId } from '@/api/PRO/component'
import { GetFileType } from '@/api/sys'

import TreeDetail from '@/components/TreeDetail/index.vue'
import TopHeader from '@/components/TopHeader/index.vue'
import comImport from '@/views/PRO/component-list/v4/component/Import.vue'
import ComponentsHistory from '@/views/PRO/component-list/v4/component/ComponentsHistory.vue'
import comImportByFactory from '@/views/PRO/component-list/v4/component/ImportByFactory.vue'
import HistoryExport from '@/views/PRO/component-list/v4/component/HistoryExport.vue'
import BatchEdit from '@/views/PRO/component-list/v4/component/BatchEditor.vue'
import ComponentPack from '@/views/PRO/component-list/v4/component/ComponentPack/index.vue'
import Edit from '@/views/PRO/component-list/v4/component/Edit.vue'
import OneClickGeneratePack from '@/views/PRO/component-list/v4/component/OneClickGeneratePack.vue'
import GeneratePack from '@/views/PRO/component-list/v4/component/GeneratePack.vue'
import ProductionConfirm from '@/views/PRO/component-list/v4/component/ProductionConfirm.vue'
import PartList from '@/views/PRO/component-list/v4/component/PartList.vue'
import SteelMeans from '@/views/PRO/component-list/v4/component/SteelMeans.vue'
import ProcessData from '@/views/PRO/component-list/v4/component/ProcessData.vue'
import ModelComponentCode from '@/views/PRO/component-list/v4/component/ModelComponentCode.vue'
import ProductionDetails from '@/views/PRO/component-list/v4/component/ProductionDetails.vue'
import ModelListImport from '@/views/PRO/component-list/v4/component/ModelListImport.vue'
import comDrawdialog from '@/views/PRO/production-order/deepen-files/dialog.vue' // 深化文件-构件详图导入

import elDragDialog from '@/directive/el-drag-dialog'
import Pagination from '@/components/Pagination/index.vue'
import { timeFormat } from '@/filters'
// import { Column, Header, Table, Tooltip } from 'vxe-table'
// import Vue from 'vue'
import AuthButtons from '@/mixins/auth-buttons'
import bimdialog from '@/views/PRO/component-list/v4/component/bimdialog.vue'
import axios from 'axios'

import sysUseType from '@/directive/sys-use-type'
import { combineURL } from '@/utils'
import { tablePageSize } from '@/views/PRO/setting'
import { v4 as uuidv4 } from 'uuid'
import { baseUrl } from '@/utils/baseurl'
import { findFirstNode } from '@/utils/tree'
import { GetStopList } from '@/api/PRO/production-task'
import LocationImport from '@/views/PRO/component-list/v4/component/LocationImport.vue'
import ExpandableSection from '@/components/ExpandableSection/index.vue'
import TracePlot from '@/views/PRO/component-list/v4/component/TracePlot.vue'
import numeral from 'numeral'
import { GetBOMInfo } from '@/views/PRO/bom-setting/utils'
import { mapGetters } from 'vuex'

import modelDrawing from '@/views/PRO/components/modelDrawing.vue'
// Vue.use(Header).use(Column).use(Tooltip).use(Table)
const SPLIT_SYMBOL = '$_$'
export default {
  directives: { elDragDialog, sysUseType },
  components: {
    ExpandableSection,
    LocationImport,
    TreeDetail,
    TopHeader,
    comImport,
    comImportByFactory,
    BatchEdit,
    HistoryExport,
    GeneratePack,
    Edit,
    ComponentPack,
    OneClickGeneratePack,
    Pagination,
    bimdialog,
    ComponentsHistory,
    ProductionConfirm,
    PartList,
    SteelMeans,
    ModelComponentCode,
    ProductionDetails,
    ModelListImport,
    comDrawdialog,
    TracePlot,
    modelDrawing,
    ProcessData
  },
  mixins: [AuthButtons],
  data() {
    return {
      allStopFlag: false,
      showExpand: true,
      isAutoSplit: undefined,
      tablePageSize: tablePageSize,
      syncVisible: false,
      syncForm: {
        Is_Sync_To_Part: null
      },
      syncRules: {
        Is_Sync_To_Part: {
          required: true,
          message: '请选择是否同步到相关零件',
          trigger: 'change'
        }
      },
      treeSelectParams: {
        placeholder: '请选择',
        clearable: true
      },
      ObjectTypeList: {
        // 构件类型
        'check-strictly': true,
        'default-expand-all': true,
        clickParent: true,
        data: [],
        props: {
          children: 'Children',
          label: 'Label',
          value: 'Data'
        }
      },

      treeData: [],
      treeLoading: true,
      expandedKey: '', // -1是全部
      projectName: '',
      statusType: '',
      searchHeight: 0,
      searchStatus: true,
      tbData: [],
      total: 0,
      tbLoading: false,
      pgLoading: false,
      queryInfo: {
        Page: 1,
        PageSize: 10,
        ParameterJson: []
      },
      customPageSize: [10, 20, 50, 100],
      installUnitIdNameList: [], // 批次数组
      nameMode: 1,
      names: '',
      customParams: {
        Code_Like: '',
        Spec: '',
        Texture: '',
        Is_Direct: '',
        Create_UserName: '',
        InstallUnit_Ids: [],
        SteelNames: '',
        TypeId: '',
        Sys_Project_Id: '',
        Project_Id: '',
        Area_Id: '',
        Project_Name: '',
        SteelCode: '',
        SteelNumber: '',
        Area_Name: ''
      },
      Unit: '',
      Proportion: 0, // 专业的单位换算
      customDialogParams: {},
      dialogVisible: false,
      currentComponent: '',
      selectList: [],
      factoryOption: [],
      projectList: [],
      typeOption: [],
      treeParamsSteel: [],
      columns: [],
      columnsOption: [
        // { Display_Name: "构件名称", Code: "SteelName" },
        // { Display_Name: "规格", Code: "SteelSpec" },
        // { Display_Name: "长度", Code: "SteelLength" },
        // { Display_Name: "构件类型", Code: "SteelType" },
        // { Display_Name: "材质", Code: "SteelMaterial" },
        // { Display_Name: "深化数量", Code: "SteelAmount" },
        // { Display_Name: "排产数量", Code: "SchedulingNum" },
        // { Display_Name: "单重", Code: "SteelWeight" },
        // { Display_Name: "总重", Code: "SteelAllWeight" },
        // { Display_Name: "直发件", Code: "Is_Component_Status" },
        // { Display_Name: "操作人", Code: "Create_UserName" },
        // { Display_Name: "操作时间", Code: "Create_Date" },
      ],
      title: '',
      width: '60%',
      tipLabel: '',
      monomerList: [],
      mode: '',
      isMonomer: true,
      historyVisible: false,
      sysUseType: undefined,
      productionConfirm: '',
      SteelFormEditData: {},
      deepenTotalLength: 0, // 深化总量
      SteelAmountTotal: 0, // 深化总量
      SchedulingNumTotal: 0, // 排产总量
      SteelAllWeightTotal: 0, // 深化总重
      SchedulingAllWeightTotal: 0, // 排产总重
      FinishCountTotal: 0, // 完成数量
      FinishWeightTotal: 0, // 完成重量
      IsComponentTotal: 0,
      TotalGrossWeight: 0,
      IsComponentTotalSteelAllWeight: 0,
      leftCol: 4,
      rightCol: 40,
      leftWidth: 320,
      drawer: false,
      scheduleLoading: false,
      command: 'cover', //  cover覆盖导入  add新增导入
      currentLastLevel: false, //  当前区域是否是最内层
      cadRowCode: '',
      cadRowProjectId: '',
      IsUploadCad: false,
      comDrawData: {},
      currentNode: {},
      trackDrawer: false,
      trackDrawerTitle: '',
      trackDrawerData: {},
      levelName: '',
      levelCode: ''
    }
  },
  computed: {
    ...mapGetters('tenant', ['isVersionFour']),
    typeEntity() {
      return this.typeOption.find((i) => i.Id === this.customParams.TypeId)
    },
    showTotalLength() {
      const arr = [
        this.customParams.Fuzzy_Search_Col,
        this.customParams.Fuzzy_Search_Col2,
        this.customParams.Fuzzy_Search_Col3,
        this.customParams.Fuzzy_Search_Col4
      ]
      return arr.includes('SteelLength') && arr.includes('SteelSpec')
    },
    filterText() {
      return this.projectName + SPLIT_SYMBOL + this.statusType
    },
    TotalGrossWeightT() {
      return numeral(this.TotalGrossWeight || 0).format('0.[000]')
    }
  },
  watch: {
    'customParams.TypeId': function(newValue, oldValue) {
      console.log({ oldValue })
      if (oldValue && oldValue !== '0') {
        this.fetchData()
      }
    },
    names(n, o) {
      this.changeMode()
    },
    nameMode(n, o) {
      this.changeMode()
    }
  },
  async created() {
    const { currentBOMInfo } = await GetBOMInfo(-1)
    console.log('list', currentBOMInfo)
    this.levelName = currentBOMInfo?.Display_Name
    this.levelCode = currentBOMInfo?.Code
    await this.getPreferenceSettingValue()
    await this.getTypeList()
    // await this.fetchData()
    // await this.getComponentSummaryInfo()
    this.fetchTreeData()
    this.getFileType()
  },
  mounted() {
  },
  activated() {
  },
  methods: {
    changeMode(n) {
      if (this.nameMode === 1) {
        this.customParams.Code_Like = this.names
        this.customParams.SteelNames = ''
      } else {
        this.customParams.Code_Like = ''
        this.customParams.SteelNames = this.names.replace(/\s+/g, '\n')
      }
    },

    getComponentInfo(row) {
      const drawingData = row.Drawing ? row.Drawing.split(',') : [] // 图纸数据
      const fileUrlData = row.File_Url ? row.File_Url.split(',') : [] // 图纸数据文件地址数据
      if (drawingData.length > 0 && fileUrlData.length > 0) {
        this.drawingActive = drawingData[0]
      }
      if (drawingData.length > 0 && fileUrlData.length > 0) {
        this.drawingDataList = drawingData.map((item, index) => ({
          name: item,
          label: item,
          url: fileUrlData[index]
        }))
      }
      this.getComponentInfoDrawing(row)
    },

    /**
     * 获取featureId 模型构件id   cadId CAD图纸id
     */
    getComponentInfoDrawing(row) {
      const importDetailId = row.Id
      GetSteelCadAndBimId({ importDetailId: importDetailId }).then((res) => {
        if (res.IsSucceed) {
          const _data = res.Data?.[0]
          if (!row.File_Url && !_data.ExtensionName) {
            this.$message({
              message: '当前构件无图纸和模型',
              type: 'warning'
            })
            return
          }

          const drawingData = {
            'extensionName': _data.ExtensionName,
            'fileBim': _data.fileBim,
            'IsUpload': _data.IsUpload,
            'Code': row.SteelName,
            'Sys_Project_Id': row.Sys_Project_Id
          }

          this.$nextTick((_) => {
            this.$refs.modelDrawingRef.dwgInit(drawingData)
          })
        }
      })
    },

    // 项目区域数据集
    fetchTreeData() {
      GetProjectAreaTreeList({
        Type: 0,
        Bom_Level: this.levelCode,
        MenuId: this.$route.meta.Id,
        projectName: this.projectName
      }).then((res) => {
        // const resAll = [
        //   {
        //     ParentNodes: null,
        //     Id: '-1',
        //     Code: '全部',
        //     Label: '全部',
        //     Level: null,
        //     Data: {},
        //     Children: []
        //   }
        // ]
        // const resData = resAll.concat(res.Data)
        if (res.Data.length === 0) {
          this.treeLoading = false
          return
        }
        const resData = res.Data
        resData.map((item) => {
          if (item.Children.length === 0) {
            item.Data.Is_Imported = false
          } else {
            item.Data.Is_Imported = item.Children.some((ich) => {
              return ich.Data.Is_Imported === true
            })

            item.Is_Directory = true
            item.Children.map((it) => {
              if (it.Children.length > 0) {
                it.Is_Directory = true
              }
            })
          }
          return item
        })
        this.treeData = resData
        if (Object.keys(this.currentNode).length === 0) {
          this.setKey()
        } else {
          this.handleNodeClick(this.currentNode)
        }
        this.treeLoading = false
      })
    },
    // 设置默认选中第一个区域末级节点
    setKey() {
      const deepFilter = (tree) => {
        for (let i = 0; i < tree.length; i++) {
          const item = tree[i]
          const { Data, Children } = item
          console.log(Data)
          if (Data.ParentId && !Children?.length) {
            console.log(Data, '????')
            this.currentNode = Data
            this.handleNodeClick(item)
            return
          } else {
            if (Children && Children.length > 0) {
              return deepFilter(Children)
            } else {
              this.handleNodeClick(item)
              return
            }
          }
        }
      }
      return deepFilter(this.treeData)
    },
    // 选中左侧项目节点
    handleNodeClick(data) {
      this.handleSearch('reset', false, 'default')
      this.currentNode = data
      this.expandedKey = data.Id
      this.$nextTick((_) => {
        const cur = this.$refs['tree'].$refs.tree.getNode(this.expandedKey)
        if (cur) {
          this.isAutoSplit = cur?.data.Data.Is_Auto_Split
        }
      })
      console.log(data, 'data2============')
      this.InstallUnit_Id = ''
      if (data.ParentNodes === null && data.Code !== '全部') {
        this.customParams.Sys_Project_Id = data.Data.Sys_Project_Id
        this.customParams.Project_Id = data.Data.Id
        this.customParams.Area_Name = ''
        this.customParams.Area_Id = ''
      } else {
        this.customParams.Sys_Project_Id = data.Data.Sys_Project_Id
        this.customParams.Project_Id = data.Data.Project_Id
        this.customParams.Area_Id = data.Data.Id
      }
      this.isAutoSplit = data.Data?.Is_Auto_Split
      this.currentLastLevel = !!(data.Data.Level && data.Children.length === 0)
      if (this.currentLastLevel) {
        this.customParams.Project_Name = data.Data?.Project_Name
        this.customParams.Area_Name = data.Label
      }
      const dataID = data.Id === -1 ? '' : data.Id
      console.log(
        this.customParams.Sys_Project_Id,
        'this.customParams.Sys_Project_Id============11111'
      )
      console.log(
        this.customParams.Area_Id,
        'this.customParams.Area_Id============11111'
      )
      console.log(
        this.customParams.Project_Id,
        'this.customParams.Project_Id============11111'
      )
      this.pgLoading = true
      this.getInstallUnitIdNameList(dataID, data)
      this.fetchData()
      this.getComponentSummaryInfo()
    },

    // 获取批次
    getInstallUnitIdNameList(id, data) {
      console.log(data, '???????????')
      if (id === '' || data.Children.length > 0) {
        this.installUnitIdNameList = []
      } else {
        GetInstallUnitIdNameList({ Area_Id: id }).then((res) => {
          this.installUnitIdNameList = res.Data
        })
      }
    },

    // 搜索
    handleSearch(reset, hasSearch = true, type = '') {
      this.searchStatus = false
      if (reset) {
        this.$refs.customParams.resetFields()
        this.names = ''
        // this.customParams.Fuzzy_Search_Col = 'SteelName'
        // this.customParams.Fuzzy_Search_Col2 = 'SteelMaterial'
        // this.customParams.Fuzzy_Search_Col3 = 'SteelSpec'
        // this.customParams.Fuzzy_Search_Col4 = 'SteelWeight'
        this.searchStatus = true
      }
      // let SteelNames = this.customParams.SteelNamesFormat.trim()
      // SteelNames = SteelNames.replace(/\s+/g, '\n')
      // this.customParams.SteelNames = SteelNames

      hasSearch && this.fetchData()
      if (type === '') {
        this.getComponentSummaryInfo()
      }
    },

    // 获取系统偏好，是否弹出生产管理过程的弹框
    async getPreferenceSettingValue() {
      GetPreferenceSettingValue({ Code: 'Production_Confirm' }).then((res) => {
        this.productionConfirm = res.Data
      })
    },

    // 构件统计
    getComponentSummaryInfo() {
      GetComponentSummaryInfo({
        ...this.customParams
      }).then((res) => {
        if (res.IsSucceed) {
          this.SteelAmountTotal = Math.round(res.Data.DeepenNum * 1000) / 1000 // 深化总量
          this.SchedulingNumTotal =
            Math.round(res.Data.SchedulingNum * 1000) / 1000 // 排产总量
          this.SteelAllWeightTotal =
            Math.round(res.Data.DeepenWeight * 1000) / 1000 // 深化总重
          this.SchedulingAllWeightTotal =
            Math.round(res.Data.SchedulingWeight * 1000) / 1000 // 排产总重
          this.FinishCountTotal =
            Math.round(res.Data.Finish_Count * 1000) / 1000 // 完成总数
          this.FinishWeightTotal =
            Math.round(res.Data.Finish_Weight * 1000) / 1000 // 完成总重
          this.IsComponentTotal = res.Data.Direct_Count || 0
          this.TotalGrossWeight = res.Data.TotalGrossWeight || 0
          this.IsComponentTotalSteelAllWeight =
            Math.round((res.Data.Direct_Weight || 0) * 1000) / 1000
          this.allStopFlag = !!res.Data.Is_Stop
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },

    // 工序完成量
    getProcessData() {
      this.width = '40%'
      this.generateComponent('构件工序完成量', 'ProcessData')
      this.$nextTick((_) => {
        this.$refs['content'].init(this.customParams, this.selectList.map((v) => v.Id).toString())
      })
    },

    // 获取表格配置
    getTableConfig(code) {
      return new Promise((resolve) => {
        GetGridByCode({
          code:
            code +
            ',' +
            this.typeOption.find((i) => i.Id === this.customParams.TypeId).Code
        }).then((res) => {
          const { IsSucceed, Data, Message } = res
          if (IsSucceed) {
            if (!Data) {
              this.$message.error('当前专业没有配置相对应表格')
              this.tbLoading = true
              return
            }
            this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)
            const list = Data.ColumnList || []
            this.columns = list
              .filter((v) => v.Is_Display)
              .map((item) => {
                if (item.Code === 'SteelName') {
                  item.fixed = 'left'
                }
                return item
              })
            this.queryInfo.PageSize = +Data.Grid.Row_Number || 20

            const selectOption = JSON.parse(JSON.stringify(this.columns))

            console.log(selectOption)
            this.columnsOption = selectOption.filter((v) => {
              return (
                v.Display_Name !== '操作时间' &&
                v.Display_Name !== '安装位置' &&
                v.Display_Name !== '模型ID' &&
                v.Display_Name !== '深化资料' &&
                v.Display_Name !== '备注' &&
                v.Display_Name !== '零件' &&
                v.Display_Name !== '排产数量' &&
                v.Code.indexOf('Attr') === -1 &&
                v.Display_Name !== '构件类型' &&
                v.Display_Name !== '批次'
              )
            })
            resolve(this.columns)
          } else {
            this.$message({
              message: Message,
              type: 'error'
            })
          }
        })
      })
    },
    // 构件列表
    async getComponentImportDetailPageList() {
      try {
        const res = await GetComponentImportDetailPageList({
          ...this.queryInfo,
          ...this.customParams
        })
        if (res.IsSucceed) {
          this.tbData = (res.Data.Data || []).map((v) => {
            v.Create_Date = timeFormat(
              v.Create_Date,
              '{y}-{m}-{d} {h}:{i}:{s}'
            )
            return v
          })
          this.deepenTotalLength = res.Data.DeepenTotalLength || 0
          this.queryInfo.PageSize = res.Data.PageSize
          this.total = res.Data.TotalCount
          this.selectList = []
          await this.getStopList()
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      } catch (e) {
        this.$message({
          message: '获取构件列表失败',
          type: 'error'
        })
      }
    },
    async getStopList() {
      if (!this.tbData || !this.tbData.length) return
      const submitObj = this.tbData.map((item) => ({
        Id: item.Id,
        Type: 2,
        Bom_Level: this.levelCode
      }))
      try {
        const res = await GetStopList(submitObj)
        if (res.IsSucceed) {
          const stopMap = {}
          res.Data.forEach((item) => {
            stopMap[item.Id] = item.Is_Stop !== null
          })
          this.tbData.forEach(row => {
            if (stopMap[row.Id]) {
              this.$set(row, 'stopFlag', stopMap[row.Id])
            }
          })
        }
      } catch (e) {}
    },

    // 获取表格数据
    async fetchData() {
      console.log('列表更新成功')
      // 分开获取，提高接口速度
      await this.getTableConfig('plm_component_page_list')
      this.tbLoading = true
      this.getComponentImportDetailPageList().then((res) => {
        this.tbLoading = false
        this.pgLoading = false
      })
    },

    async changePage() {
      this.tbLoading = true
      if (
        typeof this.queryInfo.PageSize !== 'number' ||
        this.queryInfo.PageSize < 1
      ) {
        this.queryInfo.PageSize = 10
      }
      this.getComponentImportDetailPageList().then((res) => {
        this.tbLoading = false
      })
    },

    tbSelectChange(array) {
      console.log('array', array)
      this.selectList = array.records
      this.SteelAmountTotal = 0
      this.SchedulingNumTotal = 0
      this.SteelAllWeightTotal = 0
      this.SchedulingAllWeightTotal = 0
      this.FinishCountTotal = 0
      this.FinishWeightTotal = 0
      this.IsComponentTotal = 0
      this.TotalGrossWeight = 0
      this.IsComponentTotalSteelAllWeight = 0
      let SteelAllWeightTotalTemp = 0
      let SchedulingAllWeightTotalTemp = 0
      let FinishWeightTotalTemp = 0
      let IsComponentTotalSteelAllWeightTemp = 0
      if (this.selectList.length > 0) {
        this.selectList.forEach((item) => {
          const schedulingNum =
            item.SchedulingNum == null ? 0 : item.SchedulingNum
          this.SteelAmountTotal += item.SteelAmount
          this.SchedulingNumTotal += item.SchedulingNum
          this.FinishCountTotal += item.Finish_Count
          this.TotalGrossWeight += item.TotalGrossWeight / 1000
          SteelAllWeightTotalTemp += item.SteelAllWeight
          SchedulingAllWeightTotalTemp += item.SteelWeight * schedulingNum
          FinishWeightTotalTemp += item.Finish_Weight
          this.IsComponentTotal +=
            item.Is_Component === 'False' ? item.SteelAmount : 0
          IsComponentTotalSteelAllWeightTemp +=
            item.Is_Component === 'False' ? item.SteelAllWeight : 0
        })
        this.SteelAllWeightTotal =
          Math.round((SteelAllWeightTotalTemp / this.Proportion) * 1000) / 1000
        this.SchedulingAllWeightTotal =
          Math.round((SchedulingAllWeightTotalTemp / this.Proportion) * 1000) /
          1000
        this.FinishWeightTotal =
          Math.round((FinishWeightTotalTemp / this.Proportion) * 1000) / 1000
        this.IsComponentTotalSteelAllWeight =
          Math.round(
            (IsComponentTotalSteelAllWeightTemp / this.Proportion) * 1000
          ) / 1000
      } else {
        this.getComponentSummaryInfo()
      }
    },

    getTbData(data) {
      const { CountInfo } = data
      // this.tipLabel = `累计上传构件${YearSteel}件，总重${YearAllWeight}t。`
      this.tipLabel = CountInfo
    },

    async getTypeList() {
      const res = await GetFactoryProfessionalByCode({
        factoryId: localStorage.getItem('CurReferenceId')
      })
      const data = res.Data
      if (res.IsSucceed) {
        this.Proportion = data[0].Proportion
        this.Unit = data[0].Unit
        this.typeOption = Object.freeze(data)
        if (this.typeOption.length > 0) {
          this.customParams.TypeId = this.typeOption[0]?.Id
        }
        this.getCompTypeTree(this.typeOption[0].Code)
      } else {
        this.$message({
          message: res.Message,
          type: 'error'
        })
      }
    },

    getCompTypeTree(Code) {
      this.loading = true
      GetCompTypeTree({
        professional: Code
      })
        .then((res) => {
          if (res.IsSucceed) {
            this.treeParamsSteel = res.Data
            this.ObjectTypeList.data = res.Data
            this.$nextTick((_) => {
              this.$refs.treeSelectObjectType.treeDataUpdateFun(res.Data)
            })
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
            this.treeData = []
          }
        })
        .finally((_) => {
          this.loading = false
        })
    },

    // 删除查询结果
    handleSearchDelete() {
      if (this.customParams.Project_Id === '') {
        this.$message({
          type: 'warning',
          message: '请选择项目'
        })
        return false
      }
      this.$confirm('此操作将删除搜索的数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          DeleteAllComponentWithQuery({
            ...this.customParams
          }).then((res) => {
            if (res.IsSucceed) {
              this.fetchData()
              this.$message({
                message: '删除成功',
                type: 'success'
              })
            } else {
              this.$message({
                message: res.Message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },

    // 删除选中
    handleDelete() {
      this.$confirm('此操作将删除选择数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.tbLoading = true
          DeleteComponents({
            ids: this.selectList.map((v) => v.Id).toString()
          }).then((res) => {
            if (res.IsSucceed) {
              this.fetchData()
              this.fetchTreeData()
              this.$message({
                message: '删除成功',
                type: 'success'
              })
            } else {
              this.$message({
                message: res.Message,
                type: 'error'
              })
            }
          })
            .finally(() => {
              this.tbLoading = false
            })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },

    handleEdit(row) {
      this.width = '45%'
      this.generateComponent('编辑构件', 'Edit')
      this.$nextTick((_) => {
        row.isReadOnly = false
        this.$refs['content'].init(row)
      })
    },

    handleBatchEdit() {
      const SchedulArr = this.selectList.filter((item) => {
        return item.SchedulingNum != null && item.SchedulingNum > 0
      })
      if (SchedulArr.length > 0) {
        this.$message({
          type: 'error',
          message: '选中行包含已排产的构件,编辑信息需要进行变更操作'
        })
      } else {
        this.width = '40%'
        this.generateComponent('批量编辑', 'BatchEdit')
        this.$nextTick((_) => {
          this.$refs['content'].init(this.selectList, this.columnsOption)
        })
      }
    },

    handleView(row) {
      this.width = '45%'
      this.generateComponent('查看构件', 'Edit')
      this.$nextTick((_) => {
        row.isReadOnly = true
        this.$refs['content'].init(row)
      })
    },

    // 查看构件的零件
    handleViewPart(row) {
      this.width = '60%'
      this.generateComponent('零部件清单', 'PartList')
      this.$nextTick((_) => {
        this.$refs['content'].init(row)
      })
    },

    // 查看深化资料 type 0构件  1零件
    handleViewSH(row, type) {
      this.width = '40%'
      this.generateComponent('查看深化资料', 'SteelMeans')
      this.$nextTick((_) => {
        this.$refs['content'].init(row, type)
      })
    },

    // 回调查看零件的深化资料
    handleSteelMeans(row) {
      this.handleViewSH(row, 1)
    },

    // 深化模型唯一码
    handleViewModel(row) {
      this.width = '40%'
      this.generateComponent('模型构件唯一码列表', 'ModelComponentCode')
      this.$nextTick((_) => {
        this.$refs['content'].init(row)
      })
    },

    // 排产数量点击的生产详情
    handleViewScheduling(row) {
      this.width = '30%'
      this.generateComponent('生产详情', 'ProductionDetails')
      this.$nextTick((_) => {
        this.$refs['content'].init(row)
      })
    },

    handleHistory(row) {
      console.log({ row })
      this.generateComponent('构件变更历史', 'ComponentsHistory')
      this.customDialogParams = {
        steelUnique: row.SteelUnique
      }
    },

    locationExport() {
      this.handleSteelExport(3)
    },
    handleExport(v) {
      if (v === 'com') {
        this.handleSteelExport(2)
      } else {
        this.handleExportAll()
      }
    },
    handleExportAll() {
      ExportThreeBom({
        // model: {
        //   Project_Id: this.customParams.Project_Id,
        //   Area_Id: this.customParams.Area_Id
        // }
        ...this.queryInfo,
        ...this.customParams,
        Ids: this.selectList.map((v) => v.Id).toString()
      }).then((res) => {
        if (res.IsSucceed) {
          window.open(combineURL(this.$baseUrl, res.Data), '_blank')
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    // 导出构件  type 0导出未绑定的构件   1导出已绑定的构件    2导出构件
    async handleSteelExport(type) {
      if (
        this.customParams.Sys_Project_Id === '' &&
        this.selectList.length === 0
      ) {
        this.$message({
          type: 'warning',
          message: '请选择项目'
        })
        return false
      }
      const obj = {
        Bom_Level: this.levelCode,
        Type: type,
        Import_Detail_Ids: this.selectList.map((v) => v.Id),
        ...this.customParams,
        Sys_Project_Id: this.customParams.Sys_Project_Id
      }
      const res = await ExportComponentInfo(obj)

      if (!res.IsSucceed) {
        this.$message({
          message: res.Message,
          type: 'error'
        })
        return
      }
      // eslint-disable-next-line no-unused-vars
      let fileName = localStorage.getItem('ProjectName') + '_构件导出明细'
      if (res.type === 'application/octet-stream') {
        fileName += '.rar'
      } else {
        fileName += '.xls'
      }
      window.open(combineURL(this.$baseUrl, res.Data), '_blank')
      // downloadBlobFile(res.Data, fileName, ' ')
    },

    // 获取文件的arraybuffer格式并传入进行打包准备
    getFile(url) {
      return new Promise((resolve, reject) => {
        axios({
          method: 'get',
          url,
          responseType: 'arraybuffer'
        })
          .then((res) => {
            resolve(res.data)
          })
          .catch((error) => {
            reject(error.toString())
          })
      })
    },

    // 模型清单导入
    modelListImport() {
      this.width = '30%'
      this.generateComponent('模型清单导入', 'ModelListImport')
    },

    LocationImport() {
      this.width = '30%'
      this.generateComponent('位置信息导入', 'LocationImport')
    },

    // 新增导入 or 覆盖导入
    handleCommand(command, type) {
      // console.log(command, 'command')
      // console.log(type, 'type')
      this.command = command
      if (type === 1) {
        this.deepListImport(1)
      } else if (type === 0) {
        this.deepListImport(0)
      }
    },

    // 打开导入弹框 importType 1零构件 0 构件
    deepListImport(importType) {
      console.log(importType, 'importType')
      const fileType = {
        Catalog_Code: 'PLMDeepenFiles',
        Code: this.typeEntity.Code,
        name: this.typeEntity.Name
      }
      if (this.productionConfirm === 'true' && importType === 0) {
        this.width = '30%'
        this.generateComponent('导入构件', 'ProductionConfirm')
      } else {
        this.$refs.dialog.handleOpen(
          'add',
          fileType,
          null,
          true,
          '',
          importType,
          '',
          this.command,
          this.customParams
        )
      }
    },

    // 回调是否有生产过程
    deepListImportAgin(productionConfirmData) {
      const fileType = {
        Catalog_Code: 'PLMDeepenFiles',
        Code: this.typeEntity.Code,
        name: this.typeEntity.Name
      }
      this.$refs.dialog.handleOpen(
        'add',
        fileType,
        null,
        true,
        '',
        0,
        productionConfirmData,
        this.command,
        this.customParams
      )
    },

    // 导出排产单
    handleSchedulingInfoExport() {
      ExportComponentSchedulingInfo({
        ids: this.selectList.map((v) => v.Id).toString()
      }).then((res) => {
        if (res.IsSucceed) {
          window.open(combineURL(this.$baseUrl, res.Data), '_blank')
          if (res.Message) {
            this.$alert(res.Message, '导出通知', {
              confirmButtonText: '我知道了'
            })
          }
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },

    handleHistoryExport() {
      if (this.customParams.Project_Id === '') {
        this.$message({
          type: 'warning',
          message: '请选择项目'
        })
        return false
      }
      this.width = '60%'
      this.generateComponent('历史清单导出', 'HistoryExport')
    },

    handleScheduleExport() {
      this.scheduleLoading = true
      const ids = this.selectList.map((v) => v.Id).toString()
      ExportDeepenFullSchedulingInfo({
        Ids: ids
      })
        .then((res) => {
          if (res.IsSucceed) {
            this.$message({
              message: '导出成功',
              type: 'success'
            })
            window.open(combineURL(this.$baseUrl, res.Data), '_blank')
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
        .finally((_) => {
          this.scheduleLoading = false
        })
    },

    handleComponentPack({ data, type = 2 }) {
      console.log('index', data, type)
      this.width = '80%'
      this.generateComponent('查看构件包', 'ComponentPack')
      this.$nextTick((_) => {
        if (data) {
          this.$refs['content'].getSubmitObj(data)
        }
        this.$refs['content'].handlePackage(type)
      })
    },

    handleAllPack() {
      this.width = '30%'
      this.generateComponent('查询结果一键打包', 'OneClickGeneratePack')
      this.customDialogParams = this.customParams
    },

    handleGenerate() {
      this.width = '30%'
      this.generateComponent('生成构件包', 'GeneratePack')
      this.$nextTick((_) => {
        this.$refs['content'].init(this.selectList)
      })
    },

    handleClose(data) {
      this.dialogVisible = false

      // 选择是否需要生产管理过程后回调再次弹框 importType肯定是0
      if (data === true || data === false) {
        this.deepListImportAgin(data)
      }
    },

    generateComponent(title, component) {
      this.title = title
      this.currentComponent = component
      this.dialogVisible = true
    },
    fetchTreeDataLocal() {
      // this.filterText = this.projectName
    },
    customFilterFun(value, data, node) {
      const arr = value.split(SPLIT_SYMBOL)
      const labelVal = arr[0]
      const statusVal = arr[1]
      if (!value) return true
      let parentNode = node.parent
      let labels = [node.label]
      let status = [
        data.Data.Is_Deepen_Change
          ? '已变更'
          : data.Data.Is_Imported
            ? '已导入'
            : '未导入'
      ]
      let level = 1
      while (level < node.level) {
        labels = [...labels, parentNode.label]
        status = [
          ...status,
          data.Data.Is_Deepen_Change
            ? '已变更'
            : data.Data.Is_Imported
              ? '已导入'
              : '未导入'
        ]
        parentNode = parentNode.parent
        level++
      }
      labels = labels.filter((v) => !!v)
      status = status.filter((v) => !!v)
      let resultLabel = true
      let resultStatus = true
      if (this.statusType) {
        resultStatus = status.some((s) => s.indexOf(statusVal) !== -1)
      }
      if (this.projectName) {
        resultLabel = labels.some((s) => s.indexOf(labelVal) !== -1)
      }
      return resultLabel && resultStatus
    },
    //
    async getFileType() {
      const params = {
        catalogCode: 'PLMDeepenFiles'
      }
      const res = await GetFileType(params)
      // 获取构件详图
      const data = res.Data.find((v) => v.Label === '构件详图')

      this.comDrawData = {
        isSHQD: false,
        Id: data.Id,
        name: data.Label,
        Catalog_Code: data.Code,
        Code: data.Data?.English_Name
      }

      console.log(this.comDrawData, 'comDrawData')
    },
    // 图纸导入
    handelImport() {
      this.$refs.comDrawdialogRef.handleOpen(
        'add',
        this.comDrawData,
        '',
        false,
        this.customParams.Sys_Project_Id,
        false
      )
    },

    // 轨迹图
    handleTrack(row) {
      console.log(row, 'row')
      this.trackDrawer = true
      this.trackDrawerTitle = row.SteelName
      this.trackDrawerData = row
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
@import "~@/styles/tabs.scss";

.min900 {
  min-width: 900px;
  overflow: auto;
}

.app-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;

  .cs-left {
    display: flex;
    flex-direction: column;
    margin-right: 20px;

    .inner-wrapper {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 16px 10px 16px 16px;
      border-radius: 4px;
      overflow: hidden;

      .tree-search {
        display: flex;

        .search-select {
          margin-right: 8px;
        }
      }

      .tree-x {
        overflow: hidden;
        margin-top: 16px;
        flex: 1;

        .cs-scroll {
          overflow-y: auto;
          @include scrollBar;
        }

        .el-tree {
          height: 100%;

          //::v-deep {
          //  .el-tree-node {
          //    min-width: 240px;
          //    width: min-content;
          //
          //    .el-tree-node__children {
          //      overflow: inherit;
          //    }
          //  }
          //}
        }
      }
    }
  }

  .cs-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;

    .cs-z-tb-wrapper {
      overflow: hidden;
      display: flex;
      flex-direction: column;
      flex: 1;
      height: 0;

      .tb-container {
        overflow: hidden;
        padding: 0 16px;
        flex: 1;
        height: 0;
      }
    }

    .cs-bottom {
      padding: 8px 16px 8px 16px;
      position: relative;
      display: flex;
      flex-direction: row-reverse;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;

      .data-info {
        .info-x {
          margin-right: 20px;
        }
      }

      .pg-input {
        width: 100px;
        margin-right: 20px;
      }

      .pagination-container {
        text-align: right;
        margin: 0;
        padding: 0;

        ::v-deep .el-input--small .el-input__inner {
          height: 28px;
          line-height: 28px;
        }
      }
    }
  }
}

.z-dialog {
  ::v-deep {
    .el-dialog__header {
      background-color: #298dff;

      .el-dialog__title,
      .el-dialog__close {
        color: #ffffff;
      }
    }

    .el-dialog__body {
      // max-height: 740px;
      overflow: auto;
      @include scrollBar;

      &::-webkit-scrollbar {
        width: 8px;
      }
    }
  }
}

.cs-from {
  background-color: #ffffff;
  border-radius: 4px;
  margin-bottom: 16px;
  padding: 16px 16px 0 16px;
  display: flex;
  font-size: 14px;
  color: rgba(34, 40, 52, 0.65);

  label {
    display: inline-block;
    margin-right: 20px;
    white-space: nowrap;
    vertical-align: top;
  }

  .cs-from-title {
    flex: 1;
  }

  .mb0 {
    margin-bottom: 0;

    ::v-deep {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }

  .cs-search {
    width: 100%;
  }
}

.input-with-select {
  //width: 250px;
}

.cs-button-box {
  padding: 16px 16px 6px 16px;
  box-sizing: border-box;
  position: relative;
  background-color: #ffffff;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;

  ::v-deep .el-button {
    margin-left: 0 !important;
    margin-right: 10px !important;
    margin-bottom: 10px !important;
  }

  .cs-length {
    flex: 1;
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
  }
}

.info-box {
  margin: 0 16px 16px 16px;
  display: flex;
  justify-content: center;
  font-size: 14px;
  height: 64px;
  background: rgba(41, 141, 255, 0.05);

  .cs-col {
    display: flex;
    justify-content: space-evenly;
    flex-direction: column;
    margin-right: 64px;
  }

  .info-label {
    color: #999999;
  }

  i {
    color: #00c361;
    font-style: normal;
    font-weight: 600;
    margin-left: 10px;
  }
}

::v-deep .el-tree-node {
  min-width: 240px;
  width: min-content;
}

::v-deep .el-tree-node > .el-tree-node__children {
  overflow: inherit;
}

.stretch-btn {
  position: absolute;
  width: 20px;
  height: 130px;
  top: calc((100% - 130px) / 2);
  right: -20px;
  display: flex;
  align-items: center;
  background: #eff1f3;
  cursor: pointer;

  .center-btn {
    width: 14px;
    height: 100px;
    border-radius: 0 9px 9px 0;
    background-color: #8c95a8;

    > i {
      line-height: 100px;
      text-align: center;
      color: #fff;
    }
  }
}

* {
  box-sizing: border-box;
}

.fourGreen {
  color: #00c361;
  font-style: normal;
}

.fourOrange {
  color: #ff9400;
  font-style: normal;
}

.fourRed {
  color: #ff0000;
  font-style: normal;
}

.cs-blue {
  color: #5ac8fa;
}

.orangeBg {
  background: rgba(255, 148, 0, 0.1);
}

.redBg {
  background: rgba(252, 107, 127, 0.1);
}
.greenBg {
  background: rgba(0, 195, 97, 0.1);
}

.cs-tag {
  margin-left: 8px;
  font-size: 12px;
  padding: 2px 4px;
  border-radius: 1px;
}

.cs-tree-x {
  ::v-deep {
    .el-select {
      width: 100%;
    }
  }
}
.cs-divider {
  margin: 16px 0 0 0;
}
.isPicActive {
  color: #298dff;
  cursor: pointer;
}
</style>

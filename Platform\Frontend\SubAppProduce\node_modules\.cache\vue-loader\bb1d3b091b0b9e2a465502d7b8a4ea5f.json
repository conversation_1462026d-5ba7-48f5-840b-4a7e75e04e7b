{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-path\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-path\\index.vue", "mtime": 1757468113387}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/process-path", "sourcesContent": ["<template>\r\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\r\n    <div class=\"cs-z-page-main-content\">\r\n      <top-header padding=\"0\">\r\n        <template #left>\r\n          <el-button type=\"primary\" @click=\"handleAdd\">新增</el-button>\r\n        </template>\r\n      </top-header>\r\n      <div class=\"tb-x\">\r\n        <vxe-table\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          element-loading-spinner=\"el-icon-loading\"\r\n          element-loading-text=\"拼命加载中\"\r\n          empty-text=\"暂无数据\"\r\n          class=\"cs-vxe-table\"\r\n          height=\"auto\"\r\n          align=\"left\"\r\n          stripe\r\n          :loading=\"tbLoading\"\r\n          :row-config=\"{ isCurrent: true, isHover: true }\"\r\n          :data=\"tbData\"\r\n          resizable\r\n          :tooltip-config=\"{ enterable: true }\"\r\n        >\r\n          <template>\r\n            <vxe-column\r\n              v-for=\"(item, index) in columns\"\r\n              :key=\"index\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              align=\"left\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width ? item.Width : 120\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                <div v-if=\"item.Code === 'Type'\">\r\n                  <span :style=\"{color:row[item.Code]===1 ?'#d29730': row[item.Code]===2?'#20bbc7':'#de85e4'}\">\r\n                    <!-- {{ row[item.Code]===1 ?'构件工艺':row[item.Code]===2?'零件工艺':'部件工艺' }} -->\r\n                    <!-- {{ row.Bom_Level === '-1' ? '构件工艺' : row.Bom_Level === '0' ? '零件工艺' : '部件工艺' }} -->\r\n                    {{ getBomName(row.Bom_Level) }}工艺\r\n                  </span>\r\n                </div>\r\n                <div v-else>\r\n                  {{ row[item.Code] | displayValue }}\r\n                </div>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-table-column title=\"操作\" :min-width=\"120\">\r\n              <template #default=\"{ row }\">\r\n                <el-button type=\"text\" @click=\"handleEdit(row)\">编辑</el-button>\r\n                <el-button type=\"text\" class=\"txt-red\" @click=\"handleDelete(row)\">删除</el-button>\r\n              </template>\r\n            </vxe-table-column>\r\n          </template>\r\n        </vxe-table>\r\n      </div>\r\n      <Dialog ref=\"dialog\" :bom-list=\"bomList\" @refresh=\"fetchData\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport TopHeader from '@/components/TopHeader/index.vue'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport { DeleteTechnology, GetLibList } from '@/api/PRO/technology-lib'\r\nimport { mapGetters } from 'vuex'\r\nimport Dialog from './compoments/Add.vue'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\n\r\nexport default {\r\n  name: 'PROProcessPath',\r\n  components: { TopHeader, Dialog },\r\n  mixins: [getTbInfo],\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      tbLoading: false,\r\n      total: 0,\r\n      columns: [],\r\n      tbData: [],\r\n      bomList: [],\r\n      tbConfig: {}\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters('tenant', ['isVersionFour'])\r\n  },\r\n  async mounted() {\r\n    await this.getTableConfig('ProcessPathList')\r\n    const { list } = await GetBOMInfo()\r\n    this.bomList = list || []\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    getBomName(code) {\r\n      const currentBomInfo = this.bomList.find(item => {\r\n        return item.Code.toString() === code.toString()\r\n      })\r\n      return currentBomInfo?.Display_Name || ''\r\n    },\r\n    fetchData() {\r\n      this.tbLoading = true\r\n      GetLibList({\r\n        Id: '',\r\n        Type: 0\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const bomCode = this.bomList.map(item => +item.Code)\r\n          this.tbData = (res.Data || []).filter(item => bomCode.includes(item.Bom_Level))\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    handleAdd(row) {\r\n      this.$refs['dialog'].handleOpen()\r\n    },\r\n    handleEdit(row) {\r\n      this.$refs['dialog'].handleOpen(row)\r\n    },\r\n    handleDelete(row) {\r\n      this.$confirm('是否删除该工艺', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbLoading = true\r\n        DeleteTechnology({\r\n          technologyId: row.Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.tb-x{\r\n  flex: 1;\r\n}\r\n</style>\r\n"]}]}
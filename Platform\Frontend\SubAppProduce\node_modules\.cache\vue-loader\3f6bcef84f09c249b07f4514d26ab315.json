{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\part-list\\v4\\component\\BatchEditor.vue?vue&type=style&index=0&id=7b7d95d2&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\part-list\\v4\\component\\BatchEditor.vue", "mtime": 1758266753107}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KW2NsYXNzXj0iZWwtaWNvbiJdIHsNCiAgZm9udC1zaXplOiAyNHB4Ow0KICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlOw0KICBjdXJzb3I6IHBvaW50ZXI7DQogIG1hcmdpbi1sZWZ0OiAxNXB4Ow0KfQ0KDQouaXRlbS14IHsNCiAgZGlzcGxheTogZmxleDsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgZmxleDogMCAxIDUwJTsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KDQogIC5pdGVtIHsNCiAgICB3aWR0aDogNDUlOw0KICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7DQogICAgJjpub3QoOmZpcnN0LW9mLXR5cGUpIHsNCiAgICAgIG1hcmdpbi1sZWZ0OiAyMHB4Ow0KICAgICAgLmNzLW51bWJlci1idG4taGlkZGVuLA0KICAgICAgLmVsLWlucHV0LA0KICAgICAgLmVsLXNlbGVjdCB7DQogICAgICAgIHdpZHRoOiA4MCU7DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLml0ZW0tc3BhbiB7DQogICAgd2lkdGg6IDkwcHg7DQogICAgcGFkZGluZy10b3A6IDVweDsNCiAgfQ0KfQ0KOjp2LWRlZXAgew0KICAuZWwtdHJlZS1zZWxlY3QtaW5wdXQgew0KICAgIHdpZHRoOiA4MCUgIWltcG9ydGFudDsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["BatchEditor.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgUA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BatchEditor.vue", "sourceRoot": "src/views/PRO/part-list/v4/component", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-row v-for=\"(info, index) in list\" :key=\"info.id\" class=\"item-x\">\r\n      <div class=\"item\">\r\n        <label>\r\n          属性名称\r\n          <el-select\r\n            v-model=\"info.key\"\r\n            style=\"width: calc(100% - 65px)\"\r\n            clearable\r\n            placeholder=\"请选择\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in filterOption(info.key)\"\r\n              :key=\"item.key\"\r\n              :label=\"item.label\"\r\n              :value=\"item.key\"\r\n            />\r\n          </el-select>\r\n        </label>\r\n      </div>\r\n      <div class=\"item\" style=\"line-height: 32px\">\r\n        <label>请输入值\r\n          <el-input-number\r\n            v-if=\"checkType(info.key, 'number')\"\r\n            v-model=\"info.val\"\r\n            :min=\"0\"\r\n            class=\"cs-number-btn-hidden\"\r\n          />\r\n          <el-input v-if=\"checkType(info.key, 'string')\" v-model=\"info.val\" />\r\n          <el-select\r\n            v-if=\"checkType(info.key, 'array') && info.key === 'Is_Main'\"\r\n            v-model=\"info.val\"\r\n            clearable\r\n            placeholder=\"请选择\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in Is_Main_Data\"\r\n              :key=\"item.Id\"\r\n              :label=\"item.Name\"\r\n              :value=\"item.Name\"\r\n            />\r\n          </el-select>\r\n          <!-- <el-tree-select\r\n            v-show=\"checkType(info.key, 'array') && info.key === 'AreaPosition'\"\r\n            ref=\"treeSelect\"\r\n            v-model=\"info.val\"\r\n            :tree-params=\"treeParams\"\r\n            style=\"width: 100%; display: inline-block\"\r\n          /> -->\r\n        </label>\r\n      </div>\r\n      <span v-if=\"index === 0\" class=\"item-span\">\r\n        <i class=\"el-icon-circle-plus-outline\" @click=\"handleAdd\" />\r\n      </span>\r\n      <span v-else class=\"item-span\">\r\n        <i class=\"el-icon-circle-plus-outline\" @click=\"handleAdd\" />\r\n        <i\r\n          class=\"el-icon-remove-outline txt-red\"\r\n          @click=\"handleDelete(index)\"\r\n        />\r\n      </span>\r\n    </el-row>\r\n    <div style=\"text-align: right; width: 100%; padding: 20px 2% 0 0\">\r\n      <el-button @click=\"$emit('close')\">取消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"onSubmit\">确定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { EditPartpagelist } from '@/api/plm/production'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { convertCode } from '@/utils/multi-specialty'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { GetUserableAttr } from '@/api/PRO/professionalType'\r\nexport default {\r\n  props: {\r\n    typeEntity: {\r\n      type: Object,\r\n      default: () => {}\r\n    },\r\n    AreaId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    ProjectId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      treeParams: {\r\n        'default-expand-all': true,\r\n        filterable: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      value: '',\r\n      options: [\r\n        {\r\n          key: 'Spec',\r\n          label: '规格',\r\n          type: 'string'\r\n        },\r\n        {\r\n          key: 'Length',\r\n          label: '长度',\r\n          type: 'number'\r\n        },\r\n        {\r\n          key: 'Texture',\r\n          label: '材质',\r\n          type: 'string'\r\n        },\r\n        // {\r\n        //   key: 'Num',\r\n        //   label: '深化数量',\r\n        //   type: 'number'\r\n        // },\r\n        {\r\n          key: 'Weight',\r\n          label: '单重',\r\n          type: 'number'\r\n        },\r\n        {\r\n          key: 'Shape',\r\n          label: '形状',\r\n          type: 'string'\r\n        },\r\n        // {\r\n        //   key: \"Component_Code\",\r\n        //   label: \"所属构件 \",\r\n        //   type: \"string\",\r\n        // },\r\n        {\r\n          key: 'Is_Main',\r\n          label: '是否主零件',\r\n          type: 'array'\r\n        },\r\n        {\r\n          key: 'Times',\r\n          label: '单数',\r\n          type: 'number'\r\n        },\r\n        {\r\n          key: 'Remark',\r\n          label: '备注',\r\n          type: 'string'\r\n        }\r\n      ],\r\n      list: [\r\n        {\r\n          id: uuidv4(),\r\n          val: undefined,\r\n          key: ''\r\n        }\r\n      ],\r\n      Is_Main_Data: [{ Name: '是', Id: true }, { Name: '否', Id: false }]\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.getUserableAttr()\r\n    const codeArr = this.options.filter((item, index) => index).map(i => i.key)\r\n    const columns = await this.convertCode(\r\n      this.typeEntity.Code,\r\n      codeArr,\r\n      'plm_parts_page_list'\r\n    )\r\n    console.log(columns)\r\n    this.options = this.options.map((item, index) => {\r\n      if (index) {\r\n        item.label = columns.filter((v) => v.Is_Display).find((i) => i.Code === item.key)?.Display_Name\r\n      }\r\n      return item\r\n    })\r\n\r\n    this.options = [...this.options]\r\n    console.log({ columns })\r\n    console.log(this.AreaId)\r\n  },\r\n  methods: {\r\n    // 获取拓展字段\r\n    async getUserableAttr() {\r\n      await GetUserableAttr({\r\n        IsComponent: false,\r\n        Bom_Level: 0\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const resData = res.Data\r\n          const expandData = []\r\n          resData.forEach(item => {\r\n            const expandJson = {}\r\n            expandJson.key = item.Code\r\n            expandJson.lable = item.Display_Name\r\n            expandJson.type = 'string'\r\n            expandData.push(expandJson)\r\n          })\r\n          this.options = this.options.concat(expandData)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    init(list, columnsOption) {\r\n      this.selectList = list\r\n      console.log(list)\r\n      const arr = list.filter(item => item.Component_Code !== null && item.Component_Code !== '')\r\n      console.log(arr)\r\n      this.options = arr.length > 0 ? this.options.filter(v => v.key != 'Num') : this.options\r\n      //  let filterarr = columnsOption.filter(v=> {\r\n      //   return v.Display_Name != \"项目名称\" && v.Display_Name != \"区域\" && v.Display_Name != \"批次\" && v.Code != \"Total_Weight\" &&  v.Code != \"Code\" && v.Code != \"Times\" && v.Code != \"Schduling_Count\"\r\n      //  })\r\n      //  this.options = filterarr?.map(item => ({ key: item.Code, label: item.Display_Name, type: item.Code === \"Is_Main\"?\"array\": item.Code === \"Num\" || item.Code === \"Schduling_Count\" || item.Code === \"Weight\" || item.Code === \"Times\" || item.Code === \"Length\" ? \"number\" : \"string\"}))\r\n    },\r\n    handleAdd() {\r\n      this.list.push({\r\n        id: uuidv4(),\r\n        val: undefined,\r\n        key: ''\r\n      })\r\n    },\r\n    handleDelete(index) {\r\n      this.list.splice(index, 1)\r\n    },\r\n    async onSubmit() {\r\n      this.btnLoading = true\r\n      const Keysmodel = []\r\n      for (let i = 0; i < this.list.length; i++) {\r\n        console.log(this.list)\r\n        const obj = {}\r\n        const element = this.list[i]\r\n        console.log(element)\r\n        if (!element.val) {\r\n          if (element.key === 'Length' || element.key === 'Num' || element.key === 'Weight' || element.key === 'Times') {\r\n            element.val === 0 ? this.$message({ message: '值不能为0', type: 'warning' }) : this.$message({ message: '值不能为空', type: 'warning' })\r\n          } else {\r\n            this.$message({\r\n              message: '值不能为空',\r\n              type: 'warning'\r\n            })\r\n          }\r\n          this.btnLoading = false\r\n          return\r\n        }\r\n        obj.code = element.key\r\n        obj.value = element.val\r\n        Keysmodel.push(obj)\r\n        console.log(Keysmodel)\r\n      }\r\n      await EditPartpagelist({\r\n        Ids: this.selectList.map((v) => v.Part_Aggregate_Id).toString(),\r\n        Keysmodel,\r\n        Area_Id: this.AreaId,\r\n        Project_Id: this.ProjectId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '修改成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('close')\r\n          this.$emit('refresh')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.btnLoading = false\r\n      })\r\n    },\r\n    filterOption(currentValue) {\r\n      console.log(currentValue)\r\n      return this.options.filter((k) => {\r\n        return (\r\n          (!this.list.map((v) => v.key).includes(k.key) ||\r\n            k.key === currentValue) &&\r\n          k.label\r\n        )\r\n      })\r\n    },\r\n\r\n    checkType(key, type) {\r\n      if (!key) return false\r\n      return this.options.find((v) => v.key === key).type === type\r\n    },\r\n\r\n    // 获取配置数据\r\n    async getColumnConfiguration(code, mainType = 'plm_parts_page_list') {\r\n      const res = await GetGridByCode({ code: mainType + ',' + code })\r\n      return res.Data.ColumnList\r\n    },\r\n\r\n    // 根据Code（数据）获取名称\r\n    async convertCode(typeCode, propsArr = [], mainType) {\r\n      const props = await this.getColumnConfiguration(typeCode, mainType)\r\n      console.log(props)\r\n      const columns = props.filter(i => {\r\n        const arr = propsArr.map(i => i.toLowerCase())\r\n        return arr.includes(i.Code.toLowerCase())\r\n      })\r\n      console.log(columns)\r\n      return columns\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n[class^=\"el-icon\"] {\r\n  font-size: 24px;\r\n  vertical-align: middle;\r\n  cursor: pointer;\r\n  margin-left: 15px;\r\n}\r\n\r\n.item-x {\r\n  display: flex;\r\n  margin-bottom: 20px;\r\n  flex: 0 1 50%;\r\n  justify-content: space-between;\r\n\r\n  .item {\r\n    width: 45%;\r\n    white-space: nowrap;\r\n    &:not(:first-of-type) {\r\n      margin-left: 20px;\r\n      .cs-number-btn-hidden,\r\n      .el-input,\r\n      .el-select {\r\n        width: 80%;\r\n      }\r\n    }\r\n  }\r\n\r\n  .item-span {\r\n    width: 90px;\r\n    padding-top: 5px;\r\n  }\r\n}\r\n::v-deep {\r\n  .el-tree-select-input {\r\n    width: 80% !important;\r\n  }\r\n}\r\n</style>\r\n"]}]}
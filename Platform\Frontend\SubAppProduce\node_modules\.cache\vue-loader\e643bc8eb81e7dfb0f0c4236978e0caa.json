{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\OwnerProcess.vue?vue&type=style&index=0&id=4081395c&scoped=true&lang=css", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\OwnerProcess.vue", "mtime": 1758078511171}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5kaWFsb2ctZm9vdGVyew0KICB0ZXh0LWFsaWduOiByaWdodDsNCiAgbWFyZ2luLXRvcDogMzBweDsNCn0NCg=="}, {"version": 3, "sources": ["OwnerProcess.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAySA;AACA;AACA;AACA", "file": "OwnerProcess.vue", "sourceRoot": "src/views/PRO/plan-production/schedule-production-new-part/components", "sourcesContent": ["<template>\r\n  <div v-loading=\"loading\">\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"140px\">\r\n      <el-form-item v-for=\"(element) in itemOption\" :key=\"element.tType\" :label=\"element.label\" prop=\"ownerProcess\">\r\n        <el-select v-model=\"element.value\" clearable class=\"w100\" placeholder=\"请选择\">\r\n          <template>\r\n            <el-option\r\n              v-for=\"(item) in OwnerOption[element.tType]\"\r\n              :key=\"item.tType\"\r\n              :label=\"item.Name\"\r\n              :value=\"item.Code\"\r\n            />\r\n          </template>\r\n        </el-select>\r\n      </el-form-item>\r\n    </el-form>\r\n    <div class=\"dialog-footer\">\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"submit\">确 定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { GetProcessListBase } from '@/api/PRO/technology-lib'\r\nimport { getBomName } from '@/views/PRO/bom-setting/utils'\r\n\r\nexport default {\r\n  props: {\r\n    pageType: {\r\n      type: String,\r\n      default: undefined\r\n    },\r\n    partTypeOption: {\r\n      type: Array,\r\n      default: () => ([])\r\n    },\r\n    isPartPrepare: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    hasUnitPart: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    partName: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      itemOption: [{\r\n        key: '',\r\n        value: ''\r\n      }],\r\n      form: {\r\n      },\r\n      loading: false,\r\n      btnLoading: false,\r\n      OwnerOption: {},\r\n      rules: {\r\n        // ownerProcess: [\r\n        //   { required: true, message: '请选择车间', trigger: 'change' }\r\n        // ]\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    submit() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (valid) {\r\n          this.list.forEach(item => {\r\n            const itemInfo = this.itemOption.find(v => v.tType === item.tType)\r\n            if (itemInfo) {\r\n              if (item.Scheduled_Used_Process && item.Scheduled_Used_Process !== itemInfo.value) {\r\n                this.$message({\r\n                  message: `请和该区域批次下已排产同${this.partName}领用工序保持一致`,\r\n                  type: 'warning'\r\n                })\r\n              } else {\r\n                item.Part_Used_Process = itemInfo.value\r\n              }\r\n            }\r\n          })\r\n          this.btnLoading = false\r\n          this.handleClose()\r\n        } else {\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    async setOption(isInline, arr) {\r\n      // console.log(7, this.isPartPrepare)\r\n      this.list = arr || []\r\n      this.levelList = []\r\n      this.list = this.list.map(item => {\r\n        item.tType = item.Type + '&' + item.Belong_To_Component\r\n        let curLevel = ''\r\n        if (item.Parent_Level) {\r\n          curLevel = item.Parent_Level.split(',')\r\n        }\r\n        this.levelList = this.levelList.concat(curLevel)\r\n        return item\r\n      })\r\n      this.levelList = [...new Set(this.levelList)]\r\n      const levelMap = {}\r\n      for (const lvl of this.levelList) {\r\n        levelMap[lvl] = await getBomName(3)\r\n      }\r\n      console.log('this.levelMap', levelMap)\r\n      console.log('this.levelList', this.levelList)\r\n      console.log('this.list', this.list)\r\n      console.log('this.hasUnitPart', this.hasUnitPart)\r\n      this.isInline = isInline\r\n      const suffix = '领用工序'\r\n      const obj = {}\r\n      this.itemOption = this.list.reduce((acc, cur) => {\r\n        let curLevel = ''\r\n        if (cur.Parent_Level) {\r\n          curLevel = cur.Parent_Level.split(',')\r\n        }\r\n        const curLevelCode = [...new Set(curLevel)]\r\n        console.log('curLevelCode', curLevelCode)\r\n\r\n        curLevelCode.forEach(levelCode => {\r\n          const partOwnerName = this.hasUnitPart ? (levelMap[levelCode]) : ''\r\n          console.log('partOwnerName', partOwnerName)\r\n          if (!obj[cur.tType] && cur.Type !== 'Direct') {\r\n            acc.push({\r\n              code: cur.Type,\r\n              label: (cur.Type_Name || '') + partOwnerName + suffix,\r\n              value: '',\r\n              tType: cur.tType\r\n            })\r\n            this.$set(this.OwnerOption, cur.tType, [])\r\n          }\r\n          obj[cur.tType] = true\r\n        })\r\n\r\n        return acc\r\n      }, [])\r\n      console.log('obj', obj)\r\n      console.log('this.itemOption', this.itemOption)\r\n      this.fetchData()\r\n      if (isInline && arr.length) {\r\n        const cur = arr[0]\r\n        const itemInfo = this.itemOption.find(v => v.tType === cur.tType)\r\n        if (itemInfo) {\r\n          itemInfo.value = cur.Part_Used_Process\r\n        }\r\n      }\r\n    },\r\n    getComOption() {\r\n      const _listMap = {}\r\n      const keyArr = []\r\n      const getProcessItem = (code) => this.option.find(v => v.Code === code)\r\n      const _option = this.option.filter(v => v.Is_Enable)\r\n      this.list.forEach((element) => {\r\n        const key = element.tType\r\n        keyArr.push(key)\r\n        if (!_listMap[key]) {\r\n          _listMap[key] = []\r\n        }\r\n        let parentPath = 'Component_Technology_Path'\r\n        if (this.hasUnitPart) {\r\n          if (element.Belong_To_Component) {\r\n            parentPath = 'Component_Technology_Path'\r\n          } else {\r\n            parentPath = 'SubAssembly_Technology_Path'\r\n          }\r\n        }\r\n        element[parentPath] = element[parentPath] || ''\r\n\r\n        if (element.Scheduled_Used_Process) {\r\n          const item = getProcessItem(element.Scheduled_Used_Process)\r\n          if (item) {\r\n            _listMap[key].push(item)\r\n          }\r\n        } else {\r\n          const componentProcess = element[parentPath].split('/').filter(v => !!v)\r\n          // const processItem = this.option.find(v => v.Code === element.Temp_Part_Used_Process)\r\n\r\n          if (componentProcess.length) {\r\n            // if (element.Temp_Part_Used_Process && componentProcess.includes(element.Temp_Part_Used_Process)) {\r\n            //   _listMap[key].push(processItem)\r\n            // } else {\r\n            //   componentProcess.forEach(c => {\r\n            //     const cur = getProcessItem(c)\r\n            //     if (cur) {\r\n            //       _listMap[key].push(cur)\r\n            //     }\r\n            //   })\r\n            // }\r\n            componentProcess.forEach(c => {\r\n              const cur = getProcessItem(c)\r\n              if (cur) {\r\n                if (_listMap[key].findIndex(v => v.Code === cur.Code) !== -1) {\r\n                  return\r\n                }\r\n                _listMap[key].push(cur)\r\n              }\r\n            })\r\n          } else {\r\n            const partUsedProcess = element.Part_Used_Process\r\n            const _fp = this.option.filter(item => {\r\n              let flag = false\r\n              if (partUsedProcess && partUsedProcess === item.Code) {\r\n                flag = true\r\n              }\r\n              if (element.Part_Type_Used_Process && element.Part_Type_Used_Process === item.Code) {\r\n                flag = true\r\n              }\r\n              if (!flag) {\r\n                flag = !!item.Is_Enable\r\n              }\r\n              return flag\r\n            })\r\n            for (let i = 0; i < _fp.length; i++) {\r\n              const cur = _fp[i]\r\n              if (_listMap[key].findIndex(v => v.Code === cur.Code) !== -1) {\r\n                continue\r\n              }\r\n              _listMap[key].push(cur)\r\n            }\r\n          }\r\n        }\r\n      })\r\n      if (this.isInline) {\r\n        this.OwnerOption = _listMap\r\n      } else {\r\n        keyArr.forEach((keys, idx) => {\r\n          const belongType = keys.split('&')[1] === 'true' ? 1 : 3\r\n          this.OwnerOption[keys] = _option.filter(v => v.Type === belongType)\r\n        })\r\n      }\r\n    },\r\n    async fetchData() {\r\n      this.loading = true\r\n\r\n      let hasTrueCompPart\r\n      let hasFalseCompPart\r\n      if (this.hasUnitPart) {\r\n        hasTrueCompPart = this.list.some(v => !!v.Belong_To_Component)\r\n        hasFalseCompPart = this.list.some(v => !v.Belong_To_Component)\r\n      } else {\r\n        hasTrueCompPart = true\r\n        hasFalseCompPart = false\r\n      }\r\n      const fetchDataForType = async(type) => {\r\n        try {\r\n          const res = await GetProcessListBase({ type })\r\n          if (res.IsSucceed) {\r\n            return res.Data\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            return []\r\n          }\r\n        } catch (error) {\r\n          console.error(`Error fetching data for type ${type}:`, error)\r\n          this.$message({\r\n            message: `请求失败`,\r\n            type: 'error'\r\n          })\r\n          return []\r\n        }\r\n      }\r\n\r\n      try {\r\n        let results = []\r\n\r\n        if (hasTrueCompPart && hasFalseCompPart) {\r\n          results = await Promise.all([fetchDataForType(1), fetchDataForType(3)])\r\n        } else if (hasTrueCompPart) {\r\n          results = [await fetchDataForType(1)]\r\n        } else if (hasFalseCompPart) {\r\n          results = [await fetchDataForType(3)]\r\n        }\r\n\r\n        this.option = results.filter(data => !!data.length).flat()\r\n        this.getComOption()\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    handleClose() {\r\n      this.$emit('close')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.dialog-footer{\r\n  text-align: right;\r\n  margin-top: 30px;\r\n}\r\n</style>\r\n"]}]}
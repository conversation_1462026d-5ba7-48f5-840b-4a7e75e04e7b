<template>
  <div style="height: calc(100vh - 300px)">
    <vxe-table
      v-loading="tbLoading"
      :empty-render="{name: 'NotData'}"
      show-header-overflow
      element-loading-spinner="el-icon-loading"
      element-loading-text="拼命加载中"
      empty-text="暂无数据"
      class="cs-vxe-table"
      height="100%"
      align="left"
      stripe
      :data="tbData"
      resizable
      :auto-resize="true"
      :tooltip-config="{ enterable: true }"
    >
      <vxe-column
        show-overflow="tooltip"
        sortable
        field="Name"
        title="检查类型"
        width="calc(100vh-200px)"
      />
      <vxe-column fixed="right" title="操作" width="200" align="center" show-overflow>
        <template #default="{ row }">
          <el-button type="text" @click="editEvent(row)">编辑</el-button>
          <el-divider direction="vertical" />
          <el-button type="text" @click="removeEvent(row)">删除</el-button>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<script>
import { GetCheckTypeList } from '@/api/PRO/factorycheck'
import { DeleteCheckType } from '@/api/PRO/factorycheck'

export default {
  props: {
    checkType: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tbLoading: false,
      tbData: []
    }
  },
  watch: {
    checkType: {
      handler(newName) {
        this.checkType = newName
        this.getCheckTypeList()
      },
      deep: true
    }
  },
  mounted() {
    // this.getCheckTypeList()
  },
  methods: {
    getCheckTypeList() {
      this.tbLoading = true
      GetCheckTypeList({ check_object_id: this.checkType.Id, Bom_Level: this.checkType.Code }).then((res) => {
        if (res.IsSucceed) {
          this.tbData = res.Data
          this.tbLoading = false
          console.log(res.Data)
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
          this.tbLoading = false
        }
      })
    },
    removeEvent(row) {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          DeleteCheckType({ id: row.Id }).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getCheckTypeList()
            } else {
              this.$message({
                type: 'error',
                message: res.Message
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    editEvent(row) {
      console.log('row', row)
      this.$emit('optionFn', row)
    }
  }
}
</script>

<style lang="scss" scoped>
/* .vxe-table {
  max-height: 100%;
} */
</style>

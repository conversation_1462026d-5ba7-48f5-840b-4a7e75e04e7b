{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\ProjectAddDialog.vue?vue&type=template&id=55c7b122&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\ProjectAddDialog.vue", "mtime": 1757991914477}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImZvcm0td3JhcHBlciI+CiAgPGRpdiBjbGFzcz0iaW5zdHJ1Y3Rpb24iPuivt+mAieaLqemhueebru+8jOa3u+WKoOaJgOmAiemhueebrueahDxzcGFuPuaJgOaciei0qOajgOmFjee9rjwvc3Bhbj48L2Rpdj4KICA8ZGl2PgogICAgPGVsLWZvcm0gcmVmPSJmb3JtIiA6bW9kZWw9ImZvcm0iIGxhYmVsLXdpZHRoPSI4MnB4Ij4KICAgICAgPGVsLWZvcm0taXRlbSB2LWlmPSIhcHJvamVjdExpc3QubGVuZ3RoIiBsYWJlbD0i6aG555uu5ZCN56ew77yaIj4KICAgICAgICA8ZGl2PuaaguaXoOWPr+WQjOatpeeahOmhueebrjwvZGl2PgogICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgPGVsLWZvcm0taXRlbSB2LWVsc2UgbGFiZWw9IumhueebruWQjeensO+8miI+CiAgICAgICAgPGVsLXNlbGVjdCB2LW1vZGVsPSJmb3JtLkZyb21fU3lzX1Byb2plY3RfSWQiIHBsYWNlaG9sZGVyPSLor7fpgInmi6npobnnm64iIHN0eWxlPSJ3aWR0aDogMzAwcHgiPgogICAgICAgICAgPGVsLW9wdGlvbiB2LWZvcj0iKGl0ZW0sIGluZGV4KSBpbiBwcm9qZWN0TGlzdCIgOmtleT0iaW5kZXgiIDpsYWJlbD0iaXRlbS5TaG9ydF9OYW1lIiA6dmFsdWU9Iml0ZW0uU3lzX1Byb2plY3RfSWQiIDpkaXNhYmxlZD0iaXRlbS5TeXNfUHJvamVjdF9JZD09PXN5c1Byb2plY3RJZCIgLz4KICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICA8L2VsLWZvcm0+CiAgPC9kaXY+CiAgPGRpdiBjbGFzcz0iYnRuLXgiPgogICAgPGVsLWJ1dHRvbiBAY2xpY2s9IiRlbWl0KCdjbG9zZScpIj7lj5Yg5raIPC9lbC1idXR0b24+CiAgICA8ZWwtYnV0dG9uIHYtaWY9InByb2plY3RMaXN0Lmxlbmd0aCIgdHlwZT0icHJpbWFyeSIgOmxvYWRpbmc9ImJ0bkxvYWRpbmciIEBjbGljaz0iaGFuZGxlU3VibWl0Ij7noa4g5a6aPC9lbC1idXR0b24+CiAgPC9kaXY+CjwvZGl2Pgo="}, null]}
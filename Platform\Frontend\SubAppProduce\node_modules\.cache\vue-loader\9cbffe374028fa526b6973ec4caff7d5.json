{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\quality_Inspection\\quality_summary\\index.vue?vue&type=style&index=0&id=778dcd4a&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\quality_Inspection\\quality_summary\\index.vue", "mtime": 1757572678842}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi53cmFwcGVyLWMgew0KICBoZWlnaHQ6IDEwMCU7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIC5tYWluLXdyYXBwZXIgew0KICAgIGJhY2tncm91bmQ6ICNmZmZmZmY7DQogICAgLy8gaGVpZ2h0OiAwOw0KICAgIGZsZXg6IDE7DQogICAgLy8gZGlzcGxheTogZmxleDsNCiAgICAvLyBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICB9DQogIC5oZWFkZXJfdGFiIHsNCiAgICBwYWRkaW5nOiAwIDE2cHggMCAxNnB4Ow0KICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7DQogIH0NCn0NCi5zZWFyY2hfd3JhcHBlciB7DQogIHBhZGRpbmc6IDE2cHggMTZweCAwOw0KICBib3gtc2l6aW5nOiBib3JkZXItYm94Ow0KICA6OnYtZGVlcCAuZWwtZm9ybS1pdGVtIHsNCiAgICAuZWwtZm9ybS1pdGVtX19jb250ZW50IHsNCiAgICAgICYgPiAuZWwtaW5wdXQgew0KICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgIH0NCiAgICAgICYgPiAuZWwtc2VsZWN0IHsNCiAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICB9DQogICAgfQ0KICAgIC5lbC1kYXRlLWVkaXRvci0tZGF0ZXJhbmdlLmVsLWlucHV0X19pbm5lciB7DQogICAgICB3aWR0aDogMTAwJTsNCiAgICB9DQogIH0NCn0NCg0KOjp2LWRlZXAgLmVsLXRhYnNfX2hlYWRlciB7DQogIG1hcmdpbjogMCAhaW1wb3J0YW50Ow0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4YA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/quality_Inspection/quality_summary", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <div class=\"wrapper-c\">\r\n      <div class=\"header_tab\">\r\n        <el-tabs v-model=\"activeName\">\r\n          <el-tab-pane label=\"全检\" name=\"全检\" />\r\n          <el-tab-pane label=\"抽检\" name=\"抽检\" />\r\n        </el-tabs>\r\n      </div>\r\n      <div class=\"search_wrapper\">\r\n        <el-form ref=\"form\" :model=\"form\" label-width=\"80px\">\r\n          <el-row>\r\n            <el-col :span=\"5\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item label=\"质检对象\" prop=\"Check_Object_Type\">\r\n                <el-select\r\n                  v-model=\"form.Check_Object_Type\"\r\n                  filterable\r\n                  clearable\r\n                  placeholder=\"请选择\"\r\n                  @change=\"changeObject\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in CheckObjectData\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Display_Name\"\r\n                    :value=\"item.Display_Name\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item label=\"质检节点\" prop=\"Check_Node_Id\">\r\n                <el-select\r\n                  v-model=\"form.Check_Node_Id\"\r\n                  filterable\r\n                  clearable\r\n                  placeholder=\"请选择\"\r\n                  :disabled=\"!form.Check_Object_Type\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in CheckNodeList\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Display_Name\"\r\n                    :value=\"item.Id\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\" :lg=\"4\" :xl=\"4\">\r\n              <el-form-item label=\"质检结果\" prop=\"Check_Result\">\r\n                <el-select\r\n                  v-model=\"form.Check_Result\"\r\n                  filterable\r\n                  clearable\r\n                  placeholder=\"请选择\"\r\n                >\r\n                  <el-option label=\"合格\" :value=\"'合格'\" />\r\n                  <el-option label=\"不合格\" :value=\"'不合格'\" />\r\n                  <el-option v-if=\"activeName === '全检'\" label=\"未一次合格\" :value=\"'未一次合格'\" />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item label=\"质检时间\" prop=\"Pick_Date\">\r\n                <el-date-picker\r\n                  v-model=\"form.Pick_Date\"\r\n                  type=\"daterange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item label=\"质检人\" prop=\"Check_UserIds\">\r\n                <SelectUser v-model=\"form.Check_UserIds\" multiple collapse-tags />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row>\r\n            <el-col :span=\"5\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item label=\"质检单号\" prop=\"Number_Like\">\r\n                <el-input\r\n                  v-model=\"form.Number_Like \"\r\n                  type=\"text\"\r\n                  placeholder=\"请输入质检单号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item\r\n                v-if=\"activeName == '全检'\"\r\n                label=\"名称\"\r\n                prop=\"SteelName\"\r\n              >\r\n                <el-input\r\n                  v-model=\"form.SteelName\"\r\n                  type=\"text\"\r\n                  placeholder=\"请输入（空格间隔筛选多个）\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\" :lg=\"4\" :xl=\"4\">\r\n              <el-form-item label=\"单据状态\" prop=\"Status\">\r\n                <el-select\r\n                  v-model=\"form.Status\"\r\n                  filterable\r\n                  clearable\r\n                  placeholder=\"请选择\"\r\n                >\r\n                  <el-option label=\"草稿\" :value=\"'草稿'\" />\r\n                  <el-option label=\"待整改\" :value=\"'待整改'\" />\r\n                  <el-option label=\"待复核\" :value=\"'待复核'\" />\r\n                  <el-option label=\"待质检\" :value=\"'待质检'\" />\r\n                  <el-option label=\"已完成\" :value=\"'已完成'\" />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item\r\n                v-if=\"activeName == '全检'\"\r\n                label=\"项目名称\"\r\n                prop=\"Project_Id\"\r\n              >\r\n                <el-select\r\n                  v-model=\"form.Project_Id\"\r\n                  filterable\r\n                  clearable\r\n                  placeholder=\"请选择\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in ProjectNameData\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Short_Name\"\r\n                    :value=\"item.Sys_Project_Id\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item label-width=\"16px\">\r\n                <el-button type=\"primary\" @click=\"handleSearch\">搜索</el-button>\r\n                <el-button\r\n                  @click=\"\r\n                    $refs['form'].resetFields();\r\n                    handleSearch();\r\n                  \"\r\n                >重置</el-button>\r\n                <el-button type=\"success\" :loading=\"exportLoading\" @click=\"handleExport\">导出</el-button>\r\n                <el-button\r\n                  type=\"primary\"\r\n                  :disabled=\"selectList.length == 0\"\r\n                  @click=\"handleQualityAssign\"\r\n                >质检分配</el-button>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form>\r\n      </div>\r\n\r\n      <div class=\"main-wrapper\">\r\n        <!--        <el-button style=\"margin: 10px 0 0 10px\" @click=\"\">导出</el-button>-->\r\n\r\n        <full-check\r\n          v-if=\"activeName == '全检'\"\r\n          ref=\"fullCheckRef\"\r\n          :search-detail=\"form\"\r\n          @setExportLoading=\"setExportLoading\"\r\n          @selectChange=\"(val)=>selectList = val\"\r\n        />\r\n        <spot-check\r\n          v-if=\"activeName == '抽检'\"\r\n          ref=\"spotCheckRef\"\r\n          :search-detail=\"form\"\r\n          @setExportLoading=\"setExportLoading\"\r\n          @selectChange=\"(val)=>selectList = val\"\r\n        />\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 质检分配弹窗 -->\r\n    <el-dialog\r\n      title=\"质检分配\"\r\n      :visible.sync=\"assignDialogVisible\"\r\n      width=\"400px\"\r\n      class=\"plm-custom-dialog\"\r\n      @close=\"handleAssignClose\"\r\n    >\r\n      <el-form :model=\"assignForm\" label-width=\"80px\">\r\n        <el-form-item label=\"选择人员\" required>\r\n          <SelectUser\r\n            v-model=\"assignForm.userIds\"\r\n            placeholder=\"请选择质检人员\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"handleAssignClose\">取消</el-button>\r\n        <el-button type=\"primary\" :loading=\"assignLoading\" @click=\"handleAssignSave\">保存</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport fullCheck from './components/fullCheck.vue'\r\nimport spotCheck from './components/spotCheck.vue'\r\nimport { GetDictionaryDetailListByCode, GetNodeList } from '@/api/PRO/factorycheck'\r\nimport { GetProjectPageList } from '@/api/PRO/project'\r\nimport SelectUser from '@/components/Select/SelectUser/index.vue'\r\nimport { ChangeCheckUser } from '@/api/PRO/qualityInspect/quality-management'\r\n\r\nexport default {\r\n  name: 'PROStartInspect',\r\n  components: {\r\n    SelectUser,\r\n    fullCheck,\r\n    spotCheck\r\n  },\r\n  data() {\r\n    return {\r\n      exportLoading: false,\r\n      activeName: '全检',\r\n      form: {\r\n        Status: '', // 单据状态\r\n        Check_Result: '', // 质检结果\r\n        Project_Id: '', // 项目名称\r\n        Check_Object_Type: '', // 质检对象\r\n        SteelName: '', // 名称\r\n        Check_Node_Id: '', // 质检节点\r\n        Number_Like: '', // 质检单号\r\n        Pick_Date: [], // 质检时间\r\n        BeginDate: null,\r\n        EndDate: null,\r\n        Check_UserIds: []\r\n      },\r\n      CheckNodeList: [], // 质检节点\r\n      CheckObjectData: [], // 质检对象\r\n      check_object_id: null,\r\n      ProjectNameData: [],\r\n      Check_Style: '1',\r\n      selectList: [],\r\n      assignLoading: false,\r\n      assignDialogVisible: false,\r\n      assignForm: {\r\n        userIds: []\r\n      } // 当前要分配的行数据\r\n    }\r\n  },\r\n  watch: {\r\n    activeName: {\r\n      handler(newName, oldName) {\r\n        this.selectList = []\r\n        this.form = {\r\n          Status: '', // 单据状态\r\n          Check_Result: '', // 质检结果\r\n          Project_Id: '', // 项目名称\r\n          Check_Object_Type: '', // 质检对象\r\n          SteelName: '', // 名称\r\n          Check_Node_Id: '', // 质检节点\r\n          Number_Like: '' // 质检单号\r\n        }\r\n        if (newName === '全检') {\r\n          this.Check_Style = '1'\r\n        } else if (newName === '抽检') {\r\n          this.Check_Style = '0'\r\n        }\r\n        this.exportLoading = false\r\n      },\r\n\r\n      deep: true\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getCheckType()\r\n    this.getProjectOption()\r\n  },\r\n  methods: {\r\n    // 获取项目\r\n    getProjectOption() {\r\n      GetProjectPageList({\r\n        Page: 1,\r\n        PageSize: -1\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.ProjectNameData = res.Data.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getCheckType() {\r\n      GetDictionaryDetailListByCode({ dictionaryCode: 'Quality_Code' })\r\n        .then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.CheckObjectData = res.Data\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: 'res.Message'\r\n            })\r\n          }\r\n        })\r\n        .catch(() => {\r\n          console.log('sdfd')\r\n        })\r\n    },\r\n    changeObject(val) {\r\n      console.log('val', this.form.Check_Object_Type)\r\n      this.form.Check_Node_Id = ''\r\n      const checkObj = this.CheckObjectData.find((v) => {\r\n        return v.Display_Name === val\r\n      })?.Id\r\n      console.log(this.check_object_id)\r\n\r\n      GetNodeList({ check_object_id: checkObj, Check_Style: this.Check_Style }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.CheckNodeList = res.Data\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleSearch() {\r\n      console.log('this.form.Code', this.form.Check_Object_Type)\r\n      // this.form.Code = this.form.SearchCode.trim().replaceAll(\" \", \"\\n\");\r\n      if (this.activeName === '全检') {\r\n        this.$refs.fullCheckRef.fetchData(1)\r\n      } else if (this.activeName === '抽检') {\r\n        this.$refs.spotCheckRef.fetchData(1)\r\n      }\r\n    },\r\n    handleExport() {\r\n      if (this.activeName === '全检') {\r\n        this.$refs.fullCheckRef.exportTb()\r\n      } else if (this.activeName === '抽检') {\r\n        this.$refs.spotCheckRef.exportTb()\r\n      }\r\n    },\r\n    setExportLoading(val) {\r\n      console.log('v', val)\r\n      this.exportLoading = val\r\n    },\r\n    // 质检分配相关方法\r\n    handleQualityAssign() {\r\n      if (this.selectList.length === 0) {\r\n        this.$message.warning('请先选择要分配的数据')\r\n        return\r\n      }\r\n      this.assignForm.userIds = []\r\n      this.assignDialogVisible = true\r\n    },\r\n    handleAssignClose() {\r\n      this.assignDialogVisible = false\r\n      this.assignForm.userIds = []\r\n    },\r\n    handleAssignSave() {\r\n      if (!this.assignForm.userIds || this.assignForm.userIds.length === 0) {\r\n        this.$message.warning('请选择质检人员')\r\n        return\r\n      }\r\n\r\n      this.assignLoading = true\r\n\r\n      // 调用质检分配接口\r\n      const sheetIds = this.selectList.map(item => item.SheetId)\r\n      ChangeCheckUser({\r\n        ids: sheetIds,\r\n        userIds: this.assignForm.userIds\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message.success('质检分配成功')\r\n          this.handleAssignClose()\r\n          this.handleSearch() // 刷新列表数据\r\n        } else {\r\n          this.$message.error(res.Message || '质检分配失败')\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('质检分配失败')\r\n        console.error('质检分配错误:', error)\r\n      }).finally(() => {\r\n        this.assignLoading = false\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.wrapper-c {\r\n  height: 100%;\r\n  background: #fff;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .main-wrapper {\r\n    background: #ffffff;\r\n    // height: 0;\r\n    flex: 1;\r\n    // display: flex;\r\n    // flex-direction: column;\r\n  }\r\n  .header_tab {\r\n    padding: 0 16px 0 16px;\r\n    box-sizing: border-box;\r\n  }\r\n}\r\n.search_wrapper {\r\n  padding: 16px 16px 0;\r\n  box-sizing: border-box;\r\n  ::v-deep .el-form-item {\r\n    .el-form-item__content {\r\n      & > .el-input {\r\n        width: 100%;\r\n      }\r\n      & > .el-select {\r\n        width: 100%;\r\n      }\r\n    }\r\n    .el-date-editor--daterange.el-input__inner {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .el-tabs__header {\r\n  margin: 0 !important;\r\n}\r\n</style>\r\n"]}]}
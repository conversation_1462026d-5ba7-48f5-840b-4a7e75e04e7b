{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\RecognitionConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\RecognitionConfig.vue", "mtime": 1757473454985}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetBOMInfo", "compRecognitionConfig", "partRecognitionConfig", "unitPartRecognitionConfig", "number", "components", "data", "bomLevel", "bomActiveName", "btnLoading", "mounted", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "sent", "list", "Code", "console", "log", "stop", "methods", "handleClose", "$emit"], "sources": ["src/views/PRO/process-settings/management/component/RecognitionConfig.vue"], "sourcesContent": ["<template>\n  <div class=\"form-recognition-wrapper\">\n    <div class=\"form-recognition-tabs\">\n      <el-tabs v-model=\"bomActiveName\">\n        <el-tab-pane v-for=\"(item, index) in bomLevel.list\" :key=\"index\" :label=\"item.Display_Name\" :name=\"item.Code\" />\n      </el-tabs>\n    </div>\n    <div>\n      <comp-recognition-config v-if=\"bomActiveName === '-1'\" @close=\"handleClose\" />\n      <part-recognition-config v-else-if=\"bomActiveName === '0'\" @close=\"handleClose\" />\n      <unit-part-recognition-config v-else :level=\"number(bomActiveName)\" @close=\"handleClose\" />\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport compRecognitionConfig from './compRecognitionConfig'\nimport partRecognitionConfig from './partRecognitionConfig'\nimport unitPartRecognitionConfig from './unitPartRecognitionConfig'\nimport { number } from 'echarts'\n\nexport default {\n  components: {\n    compRecognitionConfig,\n    partRecognitionConfig,\n    unitPartRecognitionConfig\n  },\n  data() {\n    return {\n      bomLevel: [],\n      bomActiveName: '',\n      btnLoading: false\n    }\n  },\n  async mounted() {\n    this.bomLevel = await GetBOMInfo()\n    this.bomActiveName = this.bomLevel.list[0].Code\n    console.log('this.bomLevel', this.bomLevel)\n  },\n  methods: {\n    handleClose() {\n      this.$emit('close')\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"~@/styles/mixin.scss\";\n.form-recognition-wrapper {\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  max-height: 70vh;\n  .form-recognition-tabs {\n\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAgBA,SAAAA,UAAA;AACA,OAAAC,qBAAA;AACA,OAAAC,qBAAA;AACA,OAAAC,yBAAA;AACA,SAAAC,MAAA;AAEA;EACAC,UAAA;IACAJ,qBAAA,EAAAA,qBAAA;IACAC,qBAAA,EAAAA,qBAAA;IACAC,yBAAA,EAAAA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,aAAA;MACAC,UAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACApB,UAAA;UAAA;YAAAW,KAAA,CAAAJ,QAAA,GAAAW,QAAA,CAAAG,IAAA;YACAV,KAAA,CAAAH,aAAA,GAAAG,KAAA,CAAAJ,QAAA,CAAAe,IAAA,IAAAC,IAAA;YACAC,OAAA,CAAAC,GAAA,kBAAAd,KAAA,CAAAJ,QAAA;UAAA;UAAA;YAAA,OAAAW,QAAA,CAAAQ,IAAA;QAAA;MAAA,GAAAX,OAAA;IAAA;EACA;EACAY,OAAA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAC,KAAA;IACA;EACA;AACA", "ignoreList": []}]}
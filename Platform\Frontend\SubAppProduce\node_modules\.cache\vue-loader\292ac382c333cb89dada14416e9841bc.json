{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\plan\\index.vue?vue&type=style&index=0&id=664056d5&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\plan\\index.vue", "mtime": 1757468128085}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5hcHAtY29udGFpbmVyIHsKICAuY3Mtd3JhcHBlciB7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjRkZGRkZGOwogICAgaGVpZ2h0OiAxMDAlOwogICAgcGFkZGluZzogMjBweDsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwoKICAgIC5zZWFyY2gteCB7CiAgICAgIHdpZHRoOiAxMDAlOwogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBhbGlnbi1pdGVtczogY2VudGVyOwoKICAgICAgLmNzLWxhYmVsIHsKICAgICAgICB3aGl0ZS1zcGFjZTogbm93cmFwOwogICAgICB9CiAgICB9CgogICAgLnRiLXggewogICAgICBmbGV4OiAxOwogICAgICBtYXJnaW4tYm90dG9tOiAxMHB4OwogICAgICBvdmVyZmxvdzogYXV0bzsKICAgIH0KICB9CgogIC5jcy11bml0IHsKICAgIG1hcmdpbjogMCAxMHB4OwogIH0KCn0KCi5jcy1yZWQgewogIGNvbG9yOiByZWQ7Cn0KCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+lBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/shipment/plan", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <div class=\"cs-wrapper\">\r\n      <div class=\"search-x\">\r\n        <el-form inline style=\"display: flex;width: 100%\">\r\n          <el-form-item>\r\n            <el-button type=\"primary\" @click=\"dialogVisible = true\">新建</el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              :loading=\"exportLoading\"\r\n              @click=\"exportExcel\"\r\n            >导出</el-button>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <ExportCustomReport code=\"Shipping_plan_template\" name=\"导出派工单\" :ids=\"selections.map(i=>i.Id)\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"发货计划单号\" style=\"margin-left: auto\">\r\n            <el-input v-model=\"form.Code\" placeholder=\"请输入\" clearable style=\"width: 180px\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"项目名称\">\r\n            <el-select v-model=\"form.Sys_Project_Id\" placeholder=\"请选择\" filterable clearable style=\"width: 180px\">\r\n              <el-option\r\n                v-for=\"item in projects\"\r\n                :key=\"item.Sys_Project_Id\"\r\n                :label=\"item.Short_Name\"\r\n                :value=\"item.Sys_Project_Id\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"计划发货日期\">\r\n            <el-date-picker\r\n              v-model=\"form.dateRange\"\r\n              style=\"width: 300px\"\r\n              type=\"daterange\"\r\n              align=\"right\"\r\n              unlink-panels\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              :picker-options=\"pickerOptions\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\">\r\n            <el-select v-model=\"form.Status\" filterable clearable style=\"width: 100px\">\r\n              <el-option v-for=\"item in statusDict\" :key=\"item.label\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" @click=\"search\">搜索</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n      </div>\r\n      <div\r\n        v-loading=\"tbLoading\"\r\n        class=\"fff cs-z-tb-wrapper\"\r\n        style=\"flex: 1 1 auto\"\r\n      >\r\n        <dynamic-data-table\r\n          ref=\"dyTable\"\r\n          class=\"cs-plm-dy-table\"\r\n          :columns=\"columns\"\r\n          :config=\"tbConfig\"\r\n          :data=\"tbData\"\r\n          :page=\"pageInfo.Page\"\r\n          :total=\"total\"\r\n          border\r\n          stripe\r\n          @gridPageChange=\"handlePageChange\"\r\n          @gridSizeChange=\"handleSizeChange\"\r\n          @select=\"selectChange\"\r\n          @selectAll=\"handleSelectAll\"\r\n        >\r\n          <template slot=\"op\" slot-scope=\"{ row, index }\">\r\n            <el-button\r\n              v-if=\"[1,-1].includes(row.Status)\"\r\n              :index=\"index\"\r\n              type=\"text\"\r\n              @click=\"handleEdit(row.Id, row)\"\r\n            >编辑</el-button>\r\n            <el-button\r\n              v-if=\"[1,-1].includes(row.Status)\"\r\n              :index=\"index\"\r\n              type=\"text\"\r\n              @click=\"handleSub(row.Id)\"\r\n            >提交</el-button>\r\n            <el-button\r\n              v-if=\"[2,3,4].includes(row.Status)\"\r\n              :index=\"index\"\r\n              type=\"text\"\r\n              @click=\"handleInfo(row.Id,row)\"\r\n            >查看</el-button>\r\n            <el-button\r\n              v-if=\"[2].includes(row.Status)\"\r\n              :index=\"index\"\r\n              type=\"text\"\r\n              @click=\"handleWithdraw(row.Id)\"\r\n            >撤回</el-button>\r\n            <el-button\r\n              v-if=\"[1,-1].includes(row.Status)\"\r\n              :index=\"index\"\r\n              type=\"text\"\r\n              style=\"color:red\"\r\n              @click=\"handleDel(row.Id)\"\r\n            >删除</el-button>\r\n          </template>\r\n        </dynamic-data-table>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      title=\"新增发货计划\"\r\n      class=\"plm-custom-dialog\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"30%\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <el-form\r\n        ref=\"form2\"\r\n        :model=\"form2\"\r\n        :rules=\"rules\"\r\n        label-width=\"70px\"\r\n        class=\"demo-ruleForm\"\r\n      >\r\n        <el-form-item label=\"项目\" prop=\"ProjectId\">\r\n          <el-select\r\n            v-model=\"form2.ProjectId\"\r\n            class=\"w100\"\r\n            placeholder=\"请选择\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"item in projects\"\r\n              :key=\"item.Id\"\r\n              :label=\"item.Short_Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item style=\"text-align: right\">\r\n          <el-button @click=\"resetForm2('form2')\">取 消</el-button>\r\n          <el-button\r\n            type=\"primary\"\r\n            @click=\"submitForm2('form2')\"\r\n          >确 定</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetProjectPageList } from '@/api/PRO/pro-schedules'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nimport {\r\n  CancelFlow,\r\n  GetProjectSendingAllCount,\r\n  SubmitProjectSending,\r\n  WithdrawDraft\r\n} from '@/api/PRO/component-stock-out'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { parseTime } from '@/utils'\r\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'\r\nimport { DeleteOutPlan, ExportOutPlanList, GetOutPlanPageList, SubmitOutPlan } from '@/api/PRO/ship-plan'\r\nimport ExportCustomReport from '@/components/ExportCustomReport/index.vue'\r\n\r\nexport default {\r\n  name: 'ShipPlan',\r\n  components: { ExportCustomReport, DynamicDataTable },\r\n  mixins: [addRouterPage],\r\n  data() {\r\n    return {\r\n      form: {\r\n        dateRange: [],\r\n        Sys_Project_Id: '',\r\n        Code: '',\r\n        Status: ''\r\n      },\r\n      pickerOptions: {\r\n        shortcuts: [\r\n          {\r\n            text: '今天',\r\n            onClick(picker) {\r\n              const end = new Date()\r\n              const start = new Date()\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 1)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          },\r\n          {\r\n            text: '最近一周',\r\n            onClick(picker) {\r\n              const end = new Date()\r\n              const start = new Date()\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          },\r\n          {\r\n            text: '最近一个月',\r\n            onClick(picker) {\r\n              const end = new Date()\r\n              const start = new Date()\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      projects: [],\r\n      statusDict: [\r\n        {\r\n          label: '草稿',\r\n          value: '1'\r\n        },\r\n        {\r\n          label: '进行中',\r\n          value: '3'\r\n        },\r\n        {\r\n          label: '已完成',\r\n          value: '4'\r\n        }\r\n      ],\r\n      form2: {\r\n        ProjectId: ''\r\n      },\r\n      dialogVisible: false,\r\n      rules: {\r\n        ProjectId: [{ required: true, message: '请选择', trigger: 'change' }]\r\n      },\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/add',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/shipment/plan/add'),\r\n          name: 'PROShipPlanAdd',\r\n          meta: { title: '新建发货计划单' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/edit',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/shipment/plan/add'),\r\n          name: 'PROShipPlanEdit',\r\n          meta: { title: '编辑发货计划单' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/detail',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/shipment/plan/add'),\r\n          name: 'PROShipPlanDetail',\r\n          meta: { title: '发货计划详情' }\r\n        }\r\n      ],\r\n      tbData: [],\r\n      total: 0,\r\n      tbLoading: false,\r\n      selections: [],\r\n      totalData: {\r\n        Allsteelamount: 0,\r\n        Allsteelweight: 0\r\n      },\r\n      pageInfo: {\r\n        Page: 1,\r\n        PageSize: 20\r\n      },\r\n      tbConfig: {\r\n        Pager_Align: 'right',\r\n        Op_Width: 240\r\n      },\r\n      columns: [],\r\n      exportLoading: false\r\n    }\r\n  },\r\n  created() {\r\n    this.getProjectList()\r\n    this.getTableConfig()\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    selectChange({ selection, row }) {\r\n      this.selections = selection\r\n    },\r\n    handleSelectAll(selection) {\r\n      this.selections = selection\r\n    },\r\n    search() {\r\n      this.pageInfo.Page = 1\r\n      this.fetchData()\r\n    },\r\n    exportExcel() {\r\n      this.exportLoading = true\r\n      const form = {\r\n        ...this.form,\r\n        ...this.pageInfo\r\n      }\r\n      delete form['dateRange']\r\n      this.form.dateRange = this.form.dateRange || []\r\n      form.Plan_Date_Begin = parseTime(this.form.dateRange[0])\r\n        ? parseTime(this.form.dateRange[0])\r\n        : ''\r\n      form.Plan_Date_End = parseTime(this.form.dateRange[1])\r\n        ? parseTime(this.form.dateRange[1])\r\n        : ''\r\n      ExportOutPlanList(form).then(res => {\r\n        if (res.IsSucceed) {\r\n          window.open(this.$baseUrl + res.Data)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.exportLoading = false\r\n      })\r\n    },\r\n    async getProjectList() {\r\n      this.treeLoading = true\r\n      this.tableLoading = true\r\n      const res = await GetProjectPageList({ PageSize: -1 })\r\n      this.projects = res.Data.Data\r\n      if (!res.Data.Data.length) {\r\n        this.$message.error('暂无项目')\r\n        this.treeLoading = false\r\n        this.tableLoading = false\r\n      } else {\r\n        this.projectId = res.Data.Data[0].Sys_Project_Id\r\n      }\r\n    },\r\n    handleClose() {\r\n      this.$refs.form2.resetFields()\r\n      this.dialogVisible = false\r\n    },\r\n    resetForm2(formName) {\r\n      this.dialogVisible = false\r\n      this.$refs[formName].resetFields()\r\n    },\r\n    submitForm2(formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          const { ProjectId } = this.form2\r\n          const {\r\n            Name,\r\n            Id,\r\n            Code,\r\n            Address,\r\n            Receiver,\r\n            Receiver_Tel,\r\n            Sys_Project_Id,\r\n            Receive_UserName\r\n          } = this.projects.find((v) => v.Id === this.form2.ProjectId)\r\n          const data = {\r\n            ProjectId,\r\n            Id,\r\n            Name,\r\n            Code,\r\n            Address,\r\n            Receiver,\r\n            Receiver_Tel,\r\n            Sys_Project_Id,\r\n            Receive_UserName,\r\n            ProfessionalType: this.ProfessionalType\r\n          }\r\n          this.$router.push({\r\n            name: 'PROShipPlanAdd',\r\n            query: {\r\n              pg_redirect: this.$route.name,\r\n              p: encodeURIComponent(JSON.stringify(data))\r\n            }\r\n          })\r\n          this.dialogVisible = false\r\n          this.$refs.form2.resetFields()\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    handleEdit(id, { Project_Name }) {\r\n      this.$router.push({\r\n        name: 'PROShipPlanEdit',\r\n        query: { pg_redirect: this.$route.name, id, type: 'edit', p: JSON.stringify({ Name: Project_Name }) }\r\n      })\r\n    },\r\n    // 撤回至草稿\r\n    handleWithdraw(id) {\r\n      this.$confirm('撤回至草稿, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          WithdrawDraft({\r\n            id: id\r\n          }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                message: '撤销成功',\r\n                type: 'success'\r\n              })\r\n              this.fetchData()\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => { })\r\n    },\r\n    handleSub(id) {\r\n      console.log(id, 'id')\r\n      this.$confirm('提交该发货计划, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          SubmitOutPlan({\r\n            id\r\n          }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                message: '提交成功',\r\n                type: 'success'\r\n              })\r\n              this.fetchData()\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => { })\r\n    },\r\n    handleDel(id) {\r\n      this.$confirm('是否删除该发货计划?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        DeleteOutPlan({\r\n          id\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '删除成功',\r\n              type: 'success'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    },\r\n    handleInfo(id, { Project_Name }) {\r\n      this.$router.push({\r\n        name: 'PROShipPlanDetail',\r\n        query: { pg_redirect: this.$route.name, id, type: 'view', p: JSON.stringify({ Name: Project_Name }) }\r\n      })\r\n    },\r\n    handleCancelFlow(instanceId) {\r\n      this.$confirm('是否撤回?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        CancelFlow({\r\n          instanceId\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '操作成功',\r\n              type: 'success'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    fetchData() {\r\n      this.tbLoading = true\r\n      const form = {\r\n        ...this.form,\r\n        ...this.pageInfo\r\n      }\r\n      delete form['dateRange']\r\n      this.form.dateRange = this.form.dateRange || []\r\n      form.Plan_Date_Begin = parseTime(this.form.dateRange[0])\r\n        ? parseTime(this.form.dateRange[0])\r\n        : ''\r\n      form.Plan_Date_End = parseTime(this.form.dateRange[1])\r\n        ? parseTime(this.form.dateRange[1])\r\n        : ''\r\n      GetOutPlanPageList(form).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.Data.map((v) => {\r\n            v.Plan_Date = v.Plan_Date\r\n              ? parseTime(new Date(v.Plan_Date), '{y}-{m}-{d}')\r\n              : v.Plan_Date\r\n            return v\r\n          })\r\n          this.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.tbLoading = false\r\n      })\r\n      GetProjectSendingAllCount({ ...form }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          // console.log(res.Data,\"res.Data\");\r\n          this.totalData = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getTableConfig() {\r\n      return new Promise((resolve) => {\r\n        GetGridByCode({\r\n          code: 'ProShipPlanList'\r\n        }).then(res => {\r\n          const { IsSucceed, Data, Message } = res\r\n          if (IsSucceed) {\r\n            if (!Data) {\r\n              this.$message({\r\n                message: '表格配置不存在',\r\n                type: 'error'\r\n              })\r\n              return\r\n            }\r\n            this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n            this.columns = (Data.ColumnList.filter(v => v.Is_Display) || []).map(item => {\r\n              item.Is_Resizable = true\r\n              return item\r\n            })\r\n            if (this.pageInfo) {\r\n              this.pageInfo.PageSize = +Data.Grid.Row_Number\r\n            } else {\r\n              this.form.PageSize = +Data.Grid.Row_Number\r\n            }\r\n            resolve(this.columns)\r\n            // this.fetchData();\r\n          } else {\r\n            this.$message({\r\n              message: Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    handlePageChange(e) {\r\n      if (this.pageInfo) {\r\n        this.pageInfo.Page = e.page\r\n      } else {\r\n        this.form.Page = e.page\r\n      }\r\n      // console.log(this.pageInfo.Page);\r\n      this.fetchData()\r\n    },\r\n    handleSizeChange(e) {\r\n      if (this.pageInfo) {\r\n        this.pageInfo.Page = 1\r\n        this.pageInfo.PageSize = e.size\r\n      } else {\r\n        this.form.Page = 1\r\n        this.form.PageSize = e.size\r\n      }\r\n      // console.log(this.pageInfo);\r\n      this.fetchData()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n  .app-container {\r\n    .cs-wrapper {\r\n      background-color: #FFFFFF;\r\n      height: 100%;\r\n      padding: 20px;\r\n      display: flex;\r\n      flex-direction: column;\r\n\r\n      .search-x {\r\n        width: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .cs-label {\r\n          white-space: nowrap;\r\n        }\r\n      }\r\n\r\n      .tb-x {\r\n        flex: 1;\r\n        margin-bottom: 10px;\r\n        overflow: auto;\r\n      }\r\n    }\r\n\r\n    .cs-unit {\r\n      margin: 0 10px;\r\n    }\r\n\r\n  }\r\n\r\n  .cs-red {\r\n    color: red;\r\n  }\r\n\r\n</style>\r\n"]}]}
<template>
  <div v-loading="pgLoading" class="container abs100">
    <el-tabs v-model="activeName" @tab-click="handleTabsClick">
      <!-- <el-tab-pane label="构件" name="2" />
      <el-tab-pane label="部件" name="3" />
      <el-tab-pane label="零件" name="1" /> -->
      <el-tab-pane v-for="item in bomList" :key="item.Code" :label="item.Display_Name" :name="item.Code" />
    </el-tabs>
    <div class="search-wrapper">
      <el-form ref="form" :model="form" label-width="80px" class="demo-form-inline">
        <el-row>
          <el-col :span="6" :lg="6" :xl="6">
            <el-form-item label="项目名称" prop="Project_Id">
              <el-select
                ref="ProjectName"
                v-model="form.Project_Id"
                filterable
                clearable
                placeholder="请选择"
                @change="projectChange"
              >
                <el-option
                  v-for="item in ProjectNameData"
                  :key="item.Id"
                  :label="item.Short_Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" :lg="6" :xl="6">
            <el-form-item label="区域" prop="Area_Id">
              <el-tree-select
                ref="treeSelectArea"
                v-model="form.Area_Id"
                :disabled="!form.Project_Id"
                :select-params="{
                  clearable: true,
                }"
                class="cs-tree-x"
                :tree-params="treeParamsArea"
                @select-clear="areaClear"
                @node-click="areaChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6" :lg="6" :xl="6">
            <template>
              <el-form-item label="批次" prop="InstallUnit_Id">
                <el-select
                  ref="SetupPosition"
                  v-model="form.InstallUnit_Id"
                  :disabled="!form.Area_Id"
                  clearable
                  placeholder="请选择"
                  @change="setupPositionChange"
                >
                  <el-option
                    v-for="item in SetupPositionData"
                    :key="item.Id"
                    :label="item.Name"
                    :value="item.Id"
                  />
                </el-select>
              </el-form-item>
            </template>
          </el-col>
          <el-col :span="6" :lg="6" :xl="6">
            <el-form-item label="排产单号" prop="Schduling_Code">
              <el-input v-model="form.Schduling_Code" type="text" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="4" :lg="4" :xl="4">
            <el-form-item label="任务工序" prop="Process_Code">
              <el-select
                v-model="form.Process_Code"
                clearable
                placeholder="请选择"
                style="width: 100%"
                @change="processChange"
              >
                <el-option
                  v-for="item in processOption"
                  :key="item.Id"
                  :label="item.Name"
                  :value="item.Code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4" :lg="4" :xl="4">
            <el-form-item label="加工班组" prop="Working_Team_Id">
              <el-select
                v-model="form.Working_Team_Id"
                :disabled="!form.Process_Code"
                clearable
                placeholder="请选择"
                style="width: 100%"
              >
                <el-option
                  v-for="item in groupOption"
                  :key="item.Id"
                  :label="item.Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4" :lg="4" :xl="4">
            <el-form-item
              v-if="Is_Workshop_Enabled"
              label="所属车间"
              prop="Workshop_Name"
            >
              <el-select
                v-model="form.Workshop_Id"
                clearable
                placeholder="请选择"
                style="width: 100%"
              >
                <el-option
                  v-for="item in workShopOption"
                  :key="item.Id"
                  :label="item.Display_Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" :lg="6" :xl="6">
            <el-form-item label="是否分配" prop="Allocate_Status">
              <el-select v-model="form.Allocate_Status" multiple placeholder="请选择" clearable="">
                <!--            <el-option label="全部" value="" />-->
                <el-option label="未分配" :value="1" />
                <el-option label="已分配" :value="2" />
                <el-option label="分配完成" :value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" :lg="6" :xl="6">
            <el-form-item label-width="16px">
              <el-button @click="handleReset">重置</el-button>
              <el-button type="primary" @click="search(1)">搜索</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div>
        <el-button type="primary" @click="drawerOpen">班组负荷</el-button>
        <el-button type="primary" :disabled="!selectList.length" @click="batchAllocationWithPreStepTask">上道工序同步</el-button>
      </div>
    </div>
    <div class="main-wrapper">
      <div class="tb-x">
        <vxe-table
          :key="activeName"
          :empty-render="{name: 'NotData'}"
          show-header-overflow
          empty-text="暂无数据"
          height="auto"
          show-overflow
          :row-config="{isCurrent: true, isHover: true}"
          :loading="pgLoading"
          class="cs-vxe-table"
          align="left"
          stripe
          :data="tbData"
          resizable
          :tooltip-config="{ enterable: true }"
          @checkbox-all="handleSelectionChange"
          @checkbox-change="handleSelectionChange"
        >
          <vxe-column fixed="left" type="checkbox" width="60" />
          <vxe-column
            v-for="(item, index) in columns"
            :key="item.Code"
            :align="item.Align"
            :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
            show-overflow="tooltip"
            sortable
            :field="item.Code"
            :title="item.Display_Name"
            :min-width="item.Width||120"
            :visible="item.visible"
          >
            <template v-if="item.Code==='Total_Allocation_Count'" #default="{ row }">
              {{ row.Total_Allocation_Count - row.Total_Receive_Count === row.Can_Allocation_Count ?'分配完成' :row.Total_Allocation_Count > 0? '已分配':'未分配' }}
            </template>
            <template v-else-if="item.Code==='Finish_Date'||item.Code==='Order_Date'" #default="{ row }">
              {{ row[item.Code] | timeFormat }}
            </template>
            <template v-else #default="{ row }">
              <span>{{ (row[item.Code] ===0 ?0 : row[item.Code]) | displayValue }}</span>
            </template>
          </vxe-column>
          <vxe-column title="操作" fixed="right" width="125">
            <template #default="{ row , rowIndex }">
              <el-button type="text" @click="handleView(row)">查看</el-button>
              <el-button
                v-if="row.Can_Allocation_Count !== 0"
                type="text"
                @click="handleDetail(row)"
              >任务分配
              </el-button>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <Pagination
        :total="total"
        :page-sizes="tablePageSize"
        :page.sync="queryInfo.Page"
        :limit.sync="queryInfo.PageSize"
        layout="total, sizes, prev, pager, next, jumper"
        @pagination="pageChange"
      />
    </div>

    <el-dialog
      v-if="dialogVisible"
      v-dialogDrag
      class="plm-custom-dialog"
      :title="title"
      :visible.sync="dialogVisible"
      :width="dWidth"
      @close="handleClose"
    >
      <component
        :is="currentComponent"
        ref="content"
        @close="handleClose"
        @refresh="fetchData(1)"
      />
    </el-dialog>
    <el-drawer
      size="60%"
      custom-class="drawerBox"
      :visible.sync="drawer"
      direction="btt"
      :with-header="false"
      append-to-body
      wrapper-closable
    >
      <div class="chartWrapper">
        <div ref="chartDom" style="width: 100%; height: 100%" />
      </div>
    </el-drawer>
  </div>
</template>

<script>

import { debounce } from '@/utils'
import { GetTeamTaskAllocationPageList, GetWorkingTeamLoadRealTime, BatchAllocationWithPreStepTask } from '@/api/PRO/production-task'
import { GetProcessList, GetWorkingTeamBase } from '@/api/PRO/technology-lib'
import { GetWorkshopPageList } from '@/api/PRO/basic-information/workshop'
import { GetCurFactory } from '@/api/PRO/factory.js' // 获取是否开启车间接口
import getTbInfo from '@/mixins/PRO/get-table-info'
import addRouterPage from '@/mixins/add-router-page'
import getProjectAreaUnit from '@/views/PRO/inventory/package/mixins/mixinsProject.js'
import * as echarts from 'echarts'
import Pagination from '@/components/Pagination/index.vue'
import { tablePageSize } from '@/views/PRO/setting'
import { GetBOMInfo, getBomCode, checkIsUnitPart } from '@/views/PRO/bom-setting/utils'

export default {
  name: 'PROTaskAllocationList',
  components: {
    Pagination
  },
  mixins: [getTbInfo, addRouterPage, getProjectAreaUnit],
  data() {
    return {
      bomList: [],
      bomName: '',
      selectList: [],
      tablePageSize: tablePageSize,
      addPageArray: [
        {
          path: this.$route.path + '/detail',
          hidden: true,
          component: () =>
            import('@/views/PRO/plan-production/task-allocation/v4/detail.vue'),
          name: 'PROTaskAllocationInfo',
          meta: { title: '调整分配' }
        },
        {
          path: this.$route.path + '/view',
          hidden: true,
          component: () =>
            import('@/views/PRO/plan-production/task-allocation/v4/detail.vue'),
          name: 'PROTaskAllocationView',
          meta: { title: '查看分配' }
        }
      ],
      activeName: getBomCode('-1'), // 1零件  2构件
      dialogVisible: false,
      pgLoading: false,
      tipLabel: '',
      title: '',
      currentComponent: '',
      dWidth: '40%',
      form: {
        InstallUnit_Id: '', // 批次ID
        Project_Id: '', // 项目名称
        Area_Id: '', // 区域ID
        Workshop_Id: '',
        Schduling_Code: '',
        Process_Code: '',
        Working_Team_Id: '',
        Allocate_Status: [1, 2]
      },
      queryInfo: {
        Page: 1,
        PageSize: tablePageSize[0]
      },
      tbConfig: {
        Op_Width: 180
      },
      columns: [],
      tbData: [],
      processOption: [],
      groupOption: [],
      total: 0,
      search: () => ({}),
      workShopOption: [],
      Is_Workshop_Enabled: false,
      drawer: false,
      myChart: null
    }
  },
  computed: {
    isCom() {
      return this.activeName === getBomCode('-1')
    },
    isUnitPart() {
      return checkIsUnitPart(this.activeName)
    }
  },
  activated() {
    console.log('activatedactivatedactivated')
    this.fetchData()
  },
  async mounted() {
    const { list } = await GetBOMInfo()
    this.bomList = list || []

    await this.getIsWorkShop()
    await this.getCurColumns()
    this.search = debounce(this.fetchData, 800, true)
    this.getProcessOption()
    this.getWorkshopOption()
  },
  methods: {
    async getCurColumns() {
      this.columns = await this.getTableConfig(this.isUnitPart ? 'PROTaskUnitAllocationList' : 'PROTaskAllocationList')
      this.columns = this.columns.map((item, index) => {
        if (index === this.columns.length - 1) {
          item.Min_Width = 140
        }
        if (item.Code === 'Workshop_Name') {
          item.Is_Display = this.Is_Workshop_Enabled
        }
        return item
      })
    },

    /**
     * 获取任务分配类别
     * Page 分页
     * PageSize
     * form 筛选
     * form.Process_Type   2构件  1零件
     */
    fetchData(page) {
      this.form.Process_Type = this.isCom ? 2 : this.isUnitPart ? 3 : 1
      this.form.Bom_Level = this.activeName
      page && (this.queryInfo.Page = page)
      this.pgLoading = true
      GetTeamTaskAllocationPageList({
        ...this.queryInfo,
        ...this.form
      }).then((res) => {
        if (res.IsSucceed) {
          this.tbData = res.Data.Data
          this.total = res.Data.TotalCount
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
        this.pgLoading = false
      })
    },

    /**
     * 获取工序
     * type  1构件  2零件 3部件
     */
    getProcessOption() {
      // const _type = typeMap[+this.activeName]
      const _type = this.isCom ? 1 : this.isUnitPart ? 3 : 2
      GetProcessList({
        type: _type,
        Bom_Level: this.activeName
      }).then((res) => {
        if (res.IsSucceed) {
          this.processOption = res.Data
          /* if (this.processOption.length) {
            this.form.Process_Id = this.processOption[0]?.Id
            this.getTeamOption()
          } */
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },

    handleSelectionChange(array) {
      this.selectList = array.records
    },

    taskChange() {
      this.form.Process_Code = ''
      this.form.Working_Team_Id = ''
      this.getProcessOption()
    },

    getTeamOption() {
      let processId = ''
      const cur = this.processOption.find(
        (item) => item.Code === this.form.Process_Code
      )
      if (cur) {
        processId = cur.Id
      }
      GetWorkingTeamBase({
        processId: processId
      }).then((res) => {
        if (res.IsSucceed) {
          this.groupOption = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    getWorkshopOption() {
      GetWorkshopPageList({ page: 1, pagesize: -1 }).then(
        (res) => {
          if (res.IsSucceed) {
            this.workShopOption = res.Data.Data
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
    },
    async getIsWorkShop() {
      await GetCurFactory({}).then(res => {
        if (res.IsSucceed) {
          this.Is_Workshop_Enabled = res.Data[0].Is_Workshop_Enabled
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    processChange(v) {
      this.form.Working_Team_Id = ''
      if (!v) {
        return
      }
      this.getTeamOption()
    },

    tbSelectChange(array) { },

    handleClose() {
      this.dialogVisible = false
    },

    handleReset() {
      this.$refs['form'].resetFields()
      this.search(1)
    },

    handleDetail(row) {
      this.$router.push({
        name: 'PROTaskAllocationInfo',
        query: {
          bomLevel: this.activeName,
          pg_type: this.isCom ? 'com' : this.isUnitPart ? 'unitPart' : 'part',
          pg_redirect: 'PROTaskAllocationList',
          Is_Workshop_Enabled: this.Is_Workshop_Enabled,
          other: encodeURIComponent(JSON.stringify(row))
        }
      })
    },

    handleView(row) {
      this.$router.push({
        name: 'PROTaskAllocationView',
        query: {
          type: 'view',
          bomLevel: this.activeName,
          pg_type: this.isCom ? 'com' : this.isUnitPart ? 'unitPart' : 'part',
          pg_redirect: 'PROTaskAllocationList',
          Is_Workshop_Enabled: this.Is_Workshop_Enabled,
          other: encodeURIComponent(JSON.stringify(row))
        }
      })
    },

    // 批量上道工序同步
    batchAllocationWithPreStepTask() {
      const List = this.selectList.map(item => {
        return {
          Area_Id: item.Area_Id,
          InstallUnit_Id: item.InstallUnit_Id,
          Process_Code: item.Process_Code,
          Schduling_Code: item.Schduling_Code,
          Workshop_Name: item.Workshop_Name
        }
      })
      BatchAllocationWithPreStepTask({
        Process_Type: this.isCom ? 2 : this.isUnitPart ? 3 : 1,
        Bom_Level: this.activeName,
        List
      }).then(res => {
        if (res.IsSucceed) {
          this.$message({
            message: '操作成功',
            type: 'success'
          })
          this.fetchData()
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },

    // 切换tab
    handleTabsClick(tab, event) {
      this.form = {
        InstallUnit_Id: '',
        Project_Id: '',
        Area_Id: '',
        Allocate_Status: [1, 2],
        Schduling_Code: '',
        Process_Code: '',
        Working_Team_Id: ''
      }
      this.getCurColumns()
      this.getProcessOption()
      this.fetchData()
    },
    // drawer
    drawerOpen() {
      this.drawer = true
      const xAxisData = []
      const data1 = []
      const data2 = []
      GetWorkingTeamLoadRealTime({

        type: this.isCom ? 1 : this.isUnitPart ? 3 : 2,
        Bom_Level: this.activeName
      }).then(res => {
        if (res.IsSucceed) {
          console.log(res)
          if (res.Data && res.Data.length > 0) {
            res.Data.map(i => {
              xAxisData.push(i.Name)
              data1.push(i.Load ?? 0)
              data2.push(i.Real_Time_Load ?? 0)
              this.$nextTick(() => {
                console.log('in')
                const chartDom = this.$refs.chartDom
                if (this.myChart == null) {
                  this.myChart = echarts.init(chartDom)
                }

                const emphasisStyle = {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowColor: 'rgba(0,0,0,0.3)'
                  }
                }
                const echartOption = {
                  title: {
                    text: '班组负荷实时情况',
                    textStyle: {
                      fontSize: 16,
                      color: '#222834'
                    }
                  },
                  tooltip: {
                    show: true,
                    trigger: 'axis'
                  },
                  legend: {
                    icon: 'rect',
                    itemWidth: 8,
                    itemHeight: 4,
                    data: [],
                    textStyle: {
                      fontSize: 12,
                      color: '#999999 '
                    }
                  },
                  grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                  },
                  xAxis: {
                    data: xAxisData,
                    axisLine: { onZero: true },
                    splitLine: { show: false },
                    splitArea: { show: false }
                  },
                  yAxis: {

                  },
                  series: [
                    {
                      name: '负荷提醒线',
                      type: 'bar',
                      barGap: '-100%',
                      emphasis: emphasisStyle,
                      data: data1,
                      itemStyle: { color: '#91cc75' }
                    }, {
                      name: '当前负荷',
                      type: 'bar',
                      barGap: '-100%',
                      emphasis: emphasisStyle,
                      data: data2,
                      itemStyle: { color: '#5470C6' }
                    }
                  ]
                }
                this.myChart.setOption(echartOption)
              })
            })
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  padding: 16px;
  display: flex;
  flex-direction: column;

  .search-wrapper {
    padding: 16px 16px 16px 16px;
    background: #ffffff;
    border-radius: 4px 4px 4px 4px;

    // ::v-deep .el-form-item__content {
    //   width: 197px;
    // }
    ::v-deep .el-form-item {
      .el-form-item__content {
        & > .el-input {
          width: 100%;
        }

        & > .el-select {
          width: 100%;
        }
        & > .el-date-editor {
          width: 100%;
        }

        .el-tree-select-input {
          width: 100% !important;
        }
      }
    }
  }
  .el-tabs{
    margin-bottom: 16px;
    background-color: #ffffff;
    padding-left: 16px;
    width: 100%;
  }
  ::v-deep .pagination {
    justify-content: flex-end !important;
    margin-top: 12px !important;

    .el-input--small .el-input__inner {
      height: 28px;
      line-height: 28px;
    }
  }
}

.main-wrapper {
  background: #ffffff;
  margin-top: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  border-radius: 4px 4px 4px 4px;
  padding: 16px 16px 0;
  overflow:hidden;

  .tb-x{
    flex: 1;
    height: 0;
  }
  .btn-wrapper {
    padding-bottom: 16px;
  }
}

.plm-custom-dialog {
  ::v-deep {
    .el-dialog .el-dialog__body {
      height: 70vh;
    }
  }
}
.drawerBox {
  .chartWrapper {
    padding: 20px;
    width: 100%;
    height: 100%;
  }
}
.pagination-container{
  padding: 0;
  margin: 10px 0;
  text-align: right;
}
</style>

{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\draft.vue?vue&type=style&index=0&id=1b701904&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\draft.vue", "mtime": 1758242836212}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouZmxleC1yb3cgew0KICBkaXNwbGF5OiBmbGV4Ow0KDQogIC5jcy1sZWZ0IHsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmOw0KICAgIG1hcmdpbi1yaWdodDogMjBweDsNCiAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgYm94LXNoYWRvdzogMCAycHggMTJweCAwIHJnYmEoMCwgMCwgMCwgMC4xKTsNCg0KICAgIC5jcy10cmVlLXdyYXBwZXIgew0KICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgICAgcGFkZGluZzogMTZweDsNCg0KICAgICAgLnRyZWUtc2VhcmNoIHsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCg0KICAgICAgICAuc2VhcmNoLXNlbGVjdCB7DQogICAgICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLmVsLXRyZWUgew0KICAgICAgICBmbGV4OiAxOw0KICAgICAgICBvdmVyZmxvdzogYXV0bzsNCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAuY3MtcmlnaHQgew0KICAgIGZsZXg6IDE7DQogICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgfQ0KfQ0KDQoucGFnaW5hdGlvbi1jb250YWluZXIgew0KICBwYWRkaW5nOiAwOw0KICB0ZXh0LWFsaWduOiByaWdodDsNCn0NCg0KOjp2LWRlZXAgLmVsLWNhcmRfX2JvZHkgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KfQ0KDQoudGIteCB7DQogIGZsZXg6IDE7DQogIGhlaWdodDogMDsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgb3ZlcmZsb3c6IGF1dG87DQp9DQoNCi50b3BUaXRsZSB7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgbWFyZ2luOiAwIDAgMTZweDsNCg0KICBzcGFuIHsNCiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogICAgd2lkdGg6IDJweDsNCiAgICBoZWlnaHQ6IDE0cHg7DQogICAgYmFja2dyb3VuZDogIzAwOWRmZjsNCiAgICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlOw0KICAgIG1hcmdpbi1yaWdodDogNnB4Ow0KICB9DQp9DQoNCjo6di1kZWVwIC5lbERpdmRlciB7DQogIG1hcmdpbjogMTBweDsNCn0NCg0KLmJ0bi14IHsNCiAgLy9tYXJnaW4tYm90dG9tOiAxNnB4Ow0KDQp9DQoNCi5lbC1pY29uLWVkaXQgew0KICBjdXJzb3I6IHBvaW50ZXI7DQp9DQoNCmZvb3RlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCn0NCg0KLmNzLWJvdHRvbSB7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgaGVpZ2h0OiA0MHB4Ow0KICBsaW5lLWhlaWdodDogNDBweDsNCg0KICAuZGF0YS1pbmZvIHsNCiAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgYm90dG9tOiAwOw0KDQogICAgLmluZm8teCB7DQogICAgICBtYXJnaW4tcmlnaHQ6IDIwcHg7DQogICAgfQ0KICB9DQp9DQoNCi5kZW1vLWZvcm0taW5saW5lIHsNCiAgOjp2LWRlZXAgew0KICAgIC5lbC1mb3JtLWl0ZW0gew0KICAgICAgbWFyZ2luLWJvdHRvbTogMDsNCiAgICB9DQogIH0NCn0NCg0KLmNzLXRyZWUteCB7DQogIDo6di1kZWVwIHsNCiAgICAuZWwtc2VsZWN0IHsNCiAgICAgIHdpZHRoOiAxMDAlOw0KICAgIH0NCiAgfQ0KfQ0KLmNzLWNvbHVtbi1yb3d7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQoNCiAgLmNzLWVsbHsNCiAgICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7DQoNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["draft.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA05EA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA", "file": "draft.vue", "sourceRoot": "src/views/PRO/plan-production/schedule-production-new-part", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 flex-row\">\r\n    <div class=\"cs-right\">\r\n      <el-card v-loading=\"pgLoading\" class=\"box-card h100\" element-loading-text=\"正在处理...\">\r\n        <h4 class=\"topTitle\"><span />基本信息</h4>\r\n        <el-form\r\n          ref=\"formInline\"\r\n          :inline=\"true\"\r\n          :model=\"formInline\"\r\n          class=\"demo-form-inline\"\r\n        >\r\n          <el-form-item v-if=\"!isAdd&&!isNest\" label=\"排产单号\" prop=\"Schduling_Code\">\r\n            <span v-if=\"isView\">{{ formInline.Status === 0 ? '' : formInline.Schduling_Code }}</span>\r\n            <el-input v-else v-model=\"formInline.Schduling_Code\" disabled />\r\n          </el-form-item>\r\n          <el-form-item label=\"计划员\" prop=\"Create_UserName\">\r\n            <span v-if=\"isView\">{{ formInline.Create_UserName }}</span>\r\n            <el-input\r\n              v-else\r\n              v-model=\"formInline.Create_UserName\"\r\n              disabled\r\n            />\r\n          </el-form-item>\r\n          <el-form-item\r\n            label=\"要求完成时间\"\r\n            prop=\"Finish_Date\"\r\n            :rules=\"{ required: true, message: '请选择', trigger: 'change' }\"\r\n          >\r\n            <span v-if=\"isView\">{{ formInline.Finish_Date | timeFormat }}</span>\r\n            <el-date-picker\r\n              v-else\r\n              v-model=\"formInline.Finish_Date\"\r\n              :picker-options=\"pickerOptions\"\r\n              :disabled=\"isView\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              type=\"date\"\r\n              placeholder=\"选择日期\"\r\n            />\r\n          </el-form-item>\r\n          <!--          <el-form-item v-if=\"!isNest\" label=\"批次\" prop=\"Create_UserName\">\r\n            <span v-if=\"isView\">{{ installName }}</span>\r\n            <el-select\r\n              v-else\r\n              v-model=\"formInline.InstallUnit_Id\"\r\n              :disabled=\"!isAdd\"\r\n              filterable\r\n              placeholder=\"请选择\"\r\n              @change=\"installChange\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in installUnitIdList\"\r\n                :key=\"item.Id\"\r\n                :label=\"item.Name\"\r\n                :value=\"item.Id\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>-->\r\n          <el-form-item label=\"备注\" prop=\"Remark\">\r\n            <span v-if=\"isView\">{{ formInline.Remark }}</span>\r\n            <el-input\r\n              v-else\r\n              v-model=\"formInline.Remark\"\r\n              :disabled=\"isView\"\r\n              style=\"width: 320px\"\r\n              placeholder=\"请输入\"\r\n            />\r\n          </el-form-item>\r\n\r\n        </el-form>\r\n        <el-divider class=\"elDivder\" />\r\n        <div v-if=\"!isView\">\r\n          <div ref=\"searchDom\" class=\"search-container\">\r\n\r\n            <el-form ref=\"searchForm\" :model=\"innerForm\">\r\n              <el-row>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"70px\" label=\"项目名称\" prop=\"projectName\">\r\n                    <el-select\r\n                      v-model=\"innerForm.projectName\"\r\n                      filterable\r\n                      clearable\r\n                      placeholder=\"请选择\"\r\n                      class=\"w100\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"(item,idx) in projectList\"\r\n                        :key=\"idx\"\r\n                        :label=\"item\"\r\n                        :value=\"item\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"70px\" label=\"区域\" prop=\"areaName\">\r\n                    <el-select\r\n                      v-model=\"innerForm.areaName\"\r\n                      filterable\r\n                      clearable\r\n                      placeholder=\"请选择\"\r\n                      class=\"w100\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"(item,idx) in areaList\"\r\n                        :key=\"idx\"\r\n                        :label=\"item\"\r\n                        :value=\"item\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"70px\" label=\"批次\" prop=\"installName\">\r\n                    <el-select\r\n                      v-model=\"innerForm.installName\"\r\n                      filterable\r\n                      clearable\r\n                      placeholder=\"请选择\"\r\n                      class=\"w100\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"(item,idx) in installList\"\r\n                        :key=\"idx\"\r\n                        :label=\"item\"\r\n                        :value=\"item\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"90px\" prop=\"searchContent\" :label=\"`${partName}名称` \">\r\n                    <el-input\r\n                      v-model=\"innerForm.searchContent\"\r\n                      clearable\r\n                      class=\"input-with-select w100\"\r\n                      placeholder=\"请输入(空格区分/多个搜索)\"\r\n                      size=\"small\"\r\n                    >\r\n                      <el-select\r\n                        slot=\"prepend\"\r\n                        v-model=\"curSearch\"\r\n                        placeholder=\"请选择\"\r\n                        style=\"width: 100px\"\r\n                      >\r\n                        <el-option label=\"精准查询\" :value=\"1\" />\r\n                        <el-option label=\"模糊查询\" :value=\"0\" />\r\n                      </el-select>\r\n                    </el-input>\r\n                  </el-form-item>\r\n                </el-col>\r\n\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"70px\" :label=\"`${partName}类型`\" prop=\"searchComTypeSearch\">\r\n                    <el-tree-select\r\n                      v-if=\"$route.query.status!=='view'\"\r\n                      ref=\"treeSelectComponentType\"\r\n                      v-model=\"innerForm.searchComTypeSearch\"\r\n                      placeholder=\"请选择\"\r\n                      :select-params=\"treeSelectParams\"\r\n                      class=\"cs-tree-x\"\r\n                      :tree-params=\"treeParamsComponentType\"\r\n                      @searchFun=\"componentTypeFilter\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"70px\" label=\"规格\" prop=\"searchSpecSearch\">\r\n                    <el-input v-model=\"innerForm.searchSpecSearch\" class=\"w100\" placeholder=\"请输入\" clearable=\"\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item v-if=\"isCom\" label-width=\"80px\" label=\"是否直发件\" prop=\"searchDirect\">\r\n                    <el-select v-model=\"innerForm.searchDirect\" class=\"w100\" placeholder=\"请选择\" clearable>\r\n                      <el-option label=\"是\" :value=\"true\" />\r\n                      <el-option label=\"否\" :value=\"false\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"20px\">\r\n                    <el-button type=\"primary\" @click=\"innerFilter\">搜索</el-button>\r\n                    <el-button @click=\"resetInnerForm\">重置</el-button>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form>\r\n\r\n          </div>\r\n        </div>\r\n\r\n        <vxe-toolbar\r\n          ref=\"xToolbar1\"\r\n        >\r\n          <template #buttons>\r\n            <div v-if=\"!isView\" class=\"btn-x\">\r\n              <el-button v-if=\"!isNest\" type=\"primary\" @click=\"handleAddDialog()\">添加</el-button>\r\n\r\n              <el-button\r\n                v-if=\"workshopEnabled\"\r\n                :disabled=\"!multipleSelection.length\"\r\n                @click=\"handleBatchWorkshop(1)\"\r\n              >分配车间\r\n              </el-button>\r\n\r\n              <el-dropdown v-if=\"hasCraft\" style=\"margin:0 10px\" @command=\"handleSelectMenu\">\r\n                <el-button :disabled=\"!multipleSelection.length\" type=\"primary\" plain>\r\n                  分配工序<i class=\"el-icon-arrow-down el-icon--right\" />\r\n                </el-button>\r\n                <el-dropdown-menu slot=\"dropdown\">\r\n                  <el-dropdown-item\r\n                    command=\"process\"\r\n                  >批量分配工序\r\n                  </el-dropdown-item>\r\n                  <el-dropdown-item\r\n                    command=\"craft\"\r\n                  >工艺代码分配\r\n                  </el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </el-dropdown>\r\n              <el-button\r\n                v-else\r\n                :disabled=\"!multipleSelection.length\"\r\n                @click=\"handleSelectMenu('process')\"\r\n              >分配工序\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"!isCom && !isOwnerNull\"\r\n                :disabled=\"!multipleSelection.length\"\r\n                @click=\"handleBatchOwner(1)\"\r\n              >批量分配领用工序\r\n              </el-button>\r\n              <el-button\r\n                plain\r\n                :disabled=\"!tbData.length\"\r\n                :loading=\"false\"\r\n                @click=\"handleReverse\"\r\n              >反选\r\n              </el-button>\r\n              <el-button\r\n                type=\"danger\"\r\n                plain\r\n                :loading=\"deleteLoading\"\r\n                :disabled=\"!multipleSelection.length || multipleSelection.some(item=>item.stopFlag)\"\r\n                @click=\"handleDelete\"\r\n              >删除\r\n              </el-button>\r\n            </div>\r\n            <div v-else>\r\n              <el-button style=\"margin-bottom: 8px;\" :disabled=\"!tbData.length\" @click=\"handleExport\">导出</el-button>\r\n            </div>\r\n          </template>\r\n          <template #tools>\r\n            <DynamicTableFields\r\n              title=\"表格配置\"\r\n              :table-config-code=\"gridCode\"\r\n              @updateColumn=\"changeColumn\"\r\n            />\r\n          </template>\r\n        </vxe-toolbar>\r\n        <div class=\"tb-x\">\r\n          <!--          activeMethod: activeCellMethod,-->\r\n          <vxe-table\r\n            ref=\"xTable\"\r\n            :key=\"tbKey\"\r\n            :empty-render=\"{name: 'NotData'}\"\r\n            show-header-overflow\r\n            :checkbox-config=\"{checkField: 'checked'}\"\r\n            class=\"cs-vxe-table\"\r\n            :row-config=\"{isCurrent: true, isHover: true}\"\r\n            align=\"left\"\r\n            height=\"100%\"\r\n            :filter-config=\"{showIcon:false}\"\r\n            show-overflow\r\n            :loading=\"tbLoading\"\r\n            stripe\r\n            :scroll-y=\"{enabled: true, gt: 20}\"\r\n            size=\"medium\"\r\n            :edit-config=\"{\r\n              trigger: 'click',\r\n              mode: 'cell',\r\n              showIcon: !isView,\r\n\r\n            }\"\r\n            :data=\"tbData\"\r\n            resizable\r\n            :tooltip-config=\"{ enterable: true }\"\r\n            @checkbox-all=\"tbSelectChange\"\r\n            @checkbox-change=\"tbSelectChange\"\r\n          >\r\n            <vxe-column v-if=\"!isView\" fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n            <template v-for=\"item in columns\">\r\n              <vxe-column\r\n                v-if=\"item.Code === 'Is_Component'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"isComponentOptions\"\r\n                :filter-method=\"filterComponentMethod\"\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <el-tag\r\n                    :type=\"row.Is_Component ? 'danger' : 'success'\"\r\n                  >{{ row.Is_Component ? '否' : '是' }}\r\n                  </el-tag>\r\n                </template>\r\n              </vxe-column>\r\n\r\n              <vxe-column\r\n                v-else-if=\"['Type','Type_Name'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filter-method=\"filterTypeMethod\"\r\n                :field=\"item.Code\"\r\n                :filters=\"filterTypeOption\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <input\r\n                    v-for=\"(option, index) in column.filters\"\r\n                    :key=\"index\"\r\n                    v-model=\"option.data\"\r\n                    type=\"type\"\r\n                    @input=\"$panel.changeOption($event, !!option.data, option)\"\r\n                  >\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row[item.Code] | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"['Comp_Code','Part_Code'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :filter-method=\"filterCodeMethod\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"filterCodeOption\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <input\r\n                    v-for=\"(option, index) in column.filters\"\r\n                    :key=\"index\"\r\n                    v-model=\"option.data\"\r\n                    type=\"type\"\r\n                    @input=\"$panel.changeOption($event, !!option.data, option)\"\r\n                  >\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  <el-tag v-if=\"row.Is_Change\" style=\"margin: 8px;\" type=\"danger\">变</el-tag>\r\n                  <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                  <el-link v-if=\"row.DwgCount>0\" type=\"primary\" @click.stop=\"handleDwg(row)\"> {{ row[item.Code] | displayValue }}\r\n                  </el-link>\r\n                  <span v-else>{{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Spec'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"specOptions\"\r\n                :filter-method=\"filterSpecMethod\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <input\r\n                    v-for=\"(option, index) in column.filters\"\r\n                    :key=\"index\"\r\n                    v-model=\"option.data\"\r\n                    type=\"type\"\r\n                    @input=\"$panel.changeOption($event, !!option.data, option)\"\r\n                  >\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Spec | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Project_Name'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"projectOptions\"\r\n                :filter-method=\"filterProjectMethod\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <select v-for=\"(option, index) in column.filters\" :key=\"index\" v-model=\"option.data\" class=\"my-select\" @change=\"$panel.changeOption($event, !!option.data, option)\">\r\n                    <option v-for=\"(label, cIndex) in projectList\" :key=\"cIndex\" :value=\"label\">{{ label }}</option>\r\n                  </select>\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Project_Name | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Area_Name'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"areaOptions\"\r\n                :filter-method=\"filterAreaMethod\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <select v-for=\"(option, index) in column.filters\" :key=\"index\" v-model=\"option.data\" class=\"my-select\" @change=\"$panel.changeOption($event, !!option.data, option)\">\r\n                    <option v-for=\"(label, cIndex) in areaList\" :key=\"cIndex\" :value=\"label\">{{ label }}</option>\r\n                  </select>\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Area_Name | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'InstallUnit_Name'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"installOptions\"\r\n                :filter-method=\"filterInstallMethod\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <select v-for=\"(option, index) in column.filters\" :key=\"index\" v-model=\"option.data\" class=\"my-select\" @change=\"$panel.changeOption($event, !!option.data, option)\">\r\n                    <option v-for=\"(label, cIndex) in installList\" :key=\"cIndex\" :value=\"label\">{{ label }}</option>\r\n                  </select>\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.InstallUnit_Name | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Schduled_Weight'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  {{ (row.Schduled_Count * row.Weight).toFixed(2) / 1 }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Technology_Path'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :show-overflow=\"false\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <div class=\"cs-column-row\">\r\n                    <div class=\"cs-ell\">\r\n                      <el-tooltip class=\"item\" effect=\"dark\" :content=\"row.Technology_Path\" placement=\"top\">\r\n                        <span>{{ row.Technology_Path | displayValue }}</span>\r\n                      </el-tooltip>\r\n                    </div>\r\n                    <i\r\n                      v-if=\"!isView\"\r\n                      class=\"el-icon-edit\"\r\n                      @click=\"openBPADialog(2, row)\"\r\n                    />\r\n                  </div>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Part_Used_Process'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :show-overflow=\"false\"\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <div class=\"cs-column-row\">\r\n                    <div class=\"cs-ell\">\r\n                      <el-tooltip class=\"item\" effect=\"dark\" :content=\"row.Part_Used_Process\" placement=\"top\">\r\n                        <span>{{ row.Part_Used_Process | displayValue }}</span>\r\n                      </el-tooltip>\r\n                    </div>\r\n                    <i\r\n                      v-if=\"showPartUsedProcess(row)\"\r\n                      class=\"el-icon-edit\"\r\n                      @click=\"handleBatchOwner(2, row)\"\r\n                    />\r\n                  </div>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Workshop_Name'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :show-overflow=\"false\"\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <div class=\"cs-column-row\">\r\n                    <div class=\"cs-ell\">\r\n                      <el-tooltip class=\"item\" effect=\"dark\" :content=\"row.Workshop_Name\" placement=\"top\">\r\n                        <span>{{ row.Workshop_Name | displayValue }}</span>\r\n                      </el-tooltip>\r\n                    </div>\r\n                    <i\r\n                      v-if=\"!isView\"\r\n                      class=\"el-icon-edit\"\r\n                      @click=\"handleBatchWorkshop(2, row)\"\r\n                    />\r\n                  </div>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Schduled_Count'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :edit-render=\"{enabled:!isView}\"\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #edit=\"{ row }\">\r\n                  <vxe-input\r\n                    v-model.number=\"row.Schduled_Count\"\r\n                    type=\"integer\"\r\n                    min=\"0\"\r\n                    :max=\"row.chooseCount\"\r\n                  />\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Schduled_Count | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else\r\n                :key=\"item.Id\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                show-overflow=\"tooltip\"\r\n                sortable\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                :min-width=\"item.Width\"\r\n              />\r\n            </template>\r\n\r\n          </vxe-table>\r\n        </div>\r\n        <el-divider v-if=\"!isView\" class=\"elDivder\" />\r\n        <footer v-if=\"!isView\">\r\n          <div class=\"data-info\">\r\n            <el-tag\r\n              size=\"medium\"\r\n              class=\"info-x\"\r\n            >已选 {{ multipleSelection.length }} 条数据\r\n            </el-tag>\r\n            <el-tag v-if=\"tipLabel\" size=\"medium\" class=\"info-x\">{{\r\n              tipLabel\r\n            }}\r\n            </el-tag>\r\n          </div>\r\n          <div>\r\n            <el-button v-if=\"workshopEnabled&&!isNest\" type=\"primary\" :disabled=\"tbData.some(item=>item.stopFlag)\" @click=\"saveWorkShop\">保存车间分配</el-button>\r\n            <el-button\r\n              v-if=\"!isNest\"\r\n              type=\"primary\"\r\n              :disabled=\"tbData.some(item=>item.stopFlag)\"\r\n              :loading=\"saveLoading\"\r\n              @click=\"saveDraft(false)\"\r\n            >保存草稿\r\n            </el-button>\r\n            <el-button :disabled=\"deleteLoading || tbData.some(item=>item.stopFlag)\" @click=\"handleSubmit\">下发任务</el-button>\r\n          </div>\r\n        </footer>\r\n      </el-card>\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"dWidth\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :bom-list=\"bomList\"\r\n        :is-nest=\"isNest\"\r\n        :part-name=\"partName\"\r\n        :is-part-prepare=\"isPartPrepare\"\r\n        :process-list=\"processList\"\r\n        :page-type=\"pageType\"\r\n        :has-unit-part=\"hasUnitPart\"\r\n        :part-type-option=\"typeOption\"\r\n        @close=\"handleClose\"\r\n        @sendProcess=\"sendProcess\"\r\n        @workShop=\"getWorkShop\"\r\n        @refresh=\"fetchData\"\r\n        @setProcessList=\"setProcessList\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      :key=\"addDraftKey\"\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"openAddDraft\"\r\n      :width=\"dWidth\"\r\n      top=\"7vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <add-draft\r\n        v-if=\"openAddDraft\"\r\n        ref=\"draft\"\r\n        :com-name=\"comName\"\r\n        :part-name=\"partName\"\r\n        :current-ids=\"currentIds\"\r\n        :is-part-prepare=\"isPartPrepare\"\r\n        :install-id=\"formInline.InstallUnit_Id\"\r\n        :schedule-id=\"scheduleId\"\r\n        :show-dialog=\"openAddDraft\"\r\n        :page-type=\"pageType\"\r\n        @addToTbList=\"addToTbList\"\r\n        @sendSelectList=\"mergeSelectList\"\r\n        @setAddTbKey=\"setAddTbKey\"\r\n        @close=\"handleClose\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-drawer\r\n      :visible.sync=\"drawer\"\r\n      direction=\"btt\"\r\n      size=\"60%\"\r\n      destroy-on-close\r\n      :before-close=\"handleCloseDrawer\"\r\n      @opened=\"renderIframe\"\r\n    >\r\n      <div style=\"width: 100%; display: flex\">\r\n        <div style=\"margin-left: 20px\">\r\n          <span style=\"display: inline-block; width: 100px\">{{ partName }}图纸</span>\r\n        </div>\r\n        <el-button\r\n          v-if=\"fileBim\"\r\n          style=\"margin-left: 42%\"\r\n          @click=\"fullscreen(1)\"\r\n        >全屏</el-button>\r\n      </div>\r\n      <iframe\r\n        id=\"frame\"\r\n        :key=\"iframeKey\"\r\n        :src=\"iframeUrl\"\r\n        style=\"width: 100%; border: 0px; margin: 0; height: 60vh\"\r\n      />\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { closeTagView, debounce } from '@/utils'\r\nimport BatchProcessAdjust from './components/BatchProcessAdjust'\r\nimport {\r\n  GetCanSchdulingPartList,\r\n  GetCompSchdulingInfoDetail, GetDwg,\r\n  GetPartSchdulingInfoDetail,\r\n  GetSchdulingWorkingTeams,\r\n  SaveComponentSchedulingWorkshop,\r\n  SaveCompSchdulingDraft,\r\n  SavePartSchdulingDraftNew,\r\n  SavePartSchedulingWorkshopNew,\r\n  SaveSchdulingTaskById\r\n} from '@/api/PRO/production-task'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nimport AddDraft from './components/addDraft'\r\nimport OwnerProcess from './components/OwnerProcess'\r\nimport Workshop from './components/Workshop.vue'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { getUnique, uniqueCode } from './constant'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport numeral from 'numeral'\r\nimport { GetLibListType, GetProcessFlowListWithTechnology, GetProcessListBase } from '@/api/PRO/technology-lib'\r\nimport { AreaGetEntity } from '@/api/plm/projects'\r\nimport { mapActions, mapGetters } from 'vuex'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\nimport moment from 'moment'\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport TreeDetail from '@/components/TreeDetail/index.vue'\r\nimport { GetInstallUnitIdNameList, GetProjectAreaTreeList } from '@/api/PRO/project'\r\n\r\nimport { GetCompTypeTree } from '@/api/PRO/factorycheck'\r\nimport { parseOssUrl } from '@/utils/file'\r\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\r\n\r\nimport { getConfigure } from '@/api/user'\r\nimport { baseUrl } from '@/utils/baseurl'\r\nimport { GetSteelCadAndBimId } from '@/api/PRO/component'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\nconst SPLIT_SYMBOL = '$_$'\r\nexport default {\r\n  components: { DynamicTableFields, TreeDetail, ExpandableSection, BatchProcessAdjust, AddDraft, Workshop, OwnerProcess },\r\n  data() {\r\n    return {\r\n      drawer: false,\r\n      drawersull: false,\r\n      iframeKey: '',\r\n      fullscreenid: '',\r\n      iframeUrl: '',\r\n      fullbimid: '',\r\n      fileBim: '',\r\n      IsUploadCad: false,\r\n      cadRowCode: '',\r\n      cadRowProjectId: '',\r\n      tbKey: 100,\r\n      isComponentOptions: [\r\n        { label: '是', value: false },\r\n        { label: '否', value: true }\r\n      ],\r\n      specOptions: [{ data: '' }],\r\n      filterTypeOption: [{ data: '' }],\r\n      filterCodeOption: [{ data: '' }],\r\n      projectOptions: [{ data: '' }],\r\n      areaOptions: [{ data: '' }],\r\n      installOptions: [{ data: '' }],\r\n      projectList: [],\r\n      installList: [],\r\n      areaList: [],\r\n      pickerOptions: {\r\n        disabledDate(time) {\r\n        }\r\n      },\r\n      innerForm: {\r\n        projectName: '',\r\n        areaName: '',\r\n        installName: '',\r\n        searchContent: '',\r\n        searchComTypeSearch: '',\r\n        searchSpecSearch: '',\r\n        searchDirect: ''\r\n      },\r\n      curSearch: 1,\r\n      searchType: '',\r\n      formInline: {\r\n        Schduling_Code: '',\r\n        Create_UserName: '',\r\n        Finish_Date: '',\r\n        InstallUnit_Id: '',\r\n        Remark: ''\r\n      },\r\n      total: 0,\r\n      currentIds: '',\r\n      columns: [],\r\n      tbData: [],\r\n      tbConfig: {},\r\n      TotalCount: 0,\r\n      multipleSelection: [],\r\n      showExpand: true,\r\n      pgLoading: false,\r\n      deleteLoading: false,\r\n      workShopIsOpen: false,\r\n      isOwnerNull: false,\r\n      dialogVisible: false,\r\n      openAddDraft: false,\r\n      saveLoading: false,\r\n      tbLoading: false,\r\n      isCheckAll: false,\r\n      currentComponent: '',\r\n      gridCode: '',\r\n      dWidth: '25%',\r\n      title: '',\r\n      search: () => ({}),\r\n      pageType: undefined,\r\n      tipLabel: '',\r\n      technologyOption: [],\r\n      typeOption: [],\r\n      workingTeam: [],\r\n      pageStatus: undefined,\r\n      scheduleId: '',\r\n      partComOwnerColumn: null,\r\n\r\n      installUnitIdList: [],\r\n      projectId: '',\r\n      areaId: '',\r\n      projectName: '',\r\n      statusType: '',\r\n      expandedKey: '',\r\n      // treeLoading: false,\r\n      treeData: [],\r\n      treeParamsComponentType: {\r\n        'default-expand-all': true,\r\n        'check-strictly': true,\r\n        filterable: true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      },\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        collapseTags: true,\r\n        clearable: true\r\n      },\r\n      disabledAdd: true,\r\n      projectOption: [],\r\n      comName: '',\r\n      partName: '',\r\n      bomList: []\r\n    }\r\n  },\r\n  watch: {\r\n    'tbData.length': {\r\n      handler(n, o) {\r\n        this.checkOwner()\r\n        this.doFilter()\r\n      },\r\n      immediate: false\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    },\r\n    isView() {\r\n      return this.pageStatus === 'view'\r\n    },\r\n    isEdit() {\r\n      return this.pageStatus === 'edit'\r\n    },\r\n    isAdd() {\r\n      return this.pageStatus === 'add'\r\n    },\r\n    addDraftKey() {\r\n      return this.expandedKey + this.formInline.InstallUnit_Id\r\n    },\r\n    // filterText() {\r\n    //   return this.projectName + SPLIT_SYMBOL + this.statusType\r\n    // },\r\n    statusCode() {\r\n      return this.isCom ? 'Comp_Schdule_Status' : 'Part_Schdule_Status'\r\n    },\r\n    installName() {\r\n      const item = this.installUnitIdList.find(v => v.Id === this.formInline.InstallUnit_Id)\r\n      if (item) {\r\n        return item.Name\r\n      } else {\r\n        return ''\r\n      }\r\n    },\r\n    isPartPrepare() {\r\n      return this.getIsPartPrepare && !this.isCom\r\n    },\r\n    isNest() {\r\n      return this.$route.query.type === '1'\r\n    },\r\n    hasCraft() {\r\n      return !!this.isVersionFour\r\n    },\r\n    hasUnitPart() {\r\n      return !!this.isVersionFour\r\n    },\r\n    ...mapGetters('tenant', ['isVersionFour']),\r\n    ...mapGetters('factoryInfo', ['workshopEnabled', 'getIsPartPrepare']),\r\n    ...mapGetters('schedule', ['processList', 'nestIds'])\r\n  },\r\n  async created() {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      // this.baseCadUrl = 'http://localhost:9529'\r\n      // this.baseCadUrl = 'http://glendale-model.bimtk.com'\r\n      this.baseCadUrl = 'http://glendale-model-dev.bimtk.tech'\r\n    } else {\r\n      getConfigure({ code: 'glendale_url' }).then((res) => {\r\n        this.baseCadUrl = res.Data\r\n      })\r\n    }\r\n  },\r\n  async mounted() {\r\n    const { list, partName, comName } = await GetBOMInfo(0)\r\n    this.bomList = list || []\r\n    this.partName = partName\r\n    this.comName = comName\r\n    this.initProcessList()\r\n    this.tbDataMap = {}\r\n    this.craftCodeMap = {}\r\n    this.pageType = this.$route.query.pg_type\r\n    this.pageStatus = this.$route.query.status\r\n    this.model = this.$route.query.model\r\n    this.scheduleId = this.$route.query.pid || ''\r\n    // // this.formInline.Create_UserName = this.$store.getters.name\r\n    // // 框架问题引起store数据丢失，已反馈，结果：此处先使用localStorage\r\n    this.formInline.Create_UserName = localStorage.getItem('UserAccount')\r\n    // if (!this.isCom) {\r\n    //   this.getPartType()\r\n    // } else {\r\n    // }\r\n\r\n    this.unique = uniqueCode()\r\n    this.checkWorkshopIsOpen()\r\n\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    await this.mergeConfig()\r\n    if (this.isView || this.isEdit) {\r\n      const { areaId, install } = this.$route.query\r\n      // this.areaId = areaId\r\n      // this.formInline.InstallUnit_Id = install\r\n      // this.getInstallUnitIdNameList()\r\n      this.fetchData()\r\n    }\r\n\r\n    if (this.isAdd) {\r\n      // this.fetchTreeData()\r\n      this.getType()\r\n    }\r\n    if (this.isEdit) {\r\n      this.getType()\r\n    }\r\n\r\n    window.addEventListener('message', this.frameListener)\r\n    this.$once('hook:beforeDestroy', () => {\r\n      console.log('deactivated')\r\n      window.removeEventListener('message', this.frameListener)\r\n    })\r\n  },\r\n  activated() {\r\n    window.addEventListener('message', this.frameListener)\r\n    this.$once('hook:deactivated', () => {\r\n      window.removeEventListener('message', this.frameListener)\r\n    })\r\n  },\r\n  methods: {\r\n    ...mapActions('schedule', ['changeProcessList', 'initProcessList', 'changeAddTbKeys']),\r\n    checkOwner() {\r\n      if (this.isCom) return\r\n      this.isOwnerNull = this.tbData.every(v => !v.Comp_Import_Detail_Id) && !this.isNest\r\n      const idx = this.columns.findIndex(v => v.Code === 'Part_Used_Process')\r\n      if (this.isOwnerNull) {\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n      } else {\r\n        if (idx === -1) {\r\n          if (!this.ownerColumn) {\r\n            this.$message({\r\n              message: `列表配置字段缺少${this.partName}领用工序字段`,\r\n              type: 'success'\r\n            })\r\n            return\r\n          }\r\n          this.columns.push(this.ownerColumn)\r\n        }\r\n        this.comPart = true\r\n      }\r\n    },\r\n    async mergeConfig() {\r\n      await this.getConfig()\r\n      await this.getWorkTeam()\r\n    },\r\n    doFilter() {\r\n      this.projectList = []\r\n      this.installList = []\r\n      this.areaList = []\r\n      this.tbData.forEach(cur => {\r\n        if (cur.Project_Name && !this.projectList.includes(cur.Project_Name)) {\r\n          this.projectList.push(cur.Project_Name)\r\n        }\r\n        if (cur.InstallUnit_Name && !this.installList.includes(cur.InstallUnit_Name)) {\r\n          this.installList.push(cur.InstallUnit_Name)\r\n        }\r\n        if (cur.Area_Name && !this.areaList.includes(cur.Area_Name)) {\r\n          this.areaList.push(cur.Area_Name)\r\n        }\r\n      })\r\n    },\r\n    async getConfig() {\r\n      let configCode = ''\r\n      if (this.isNest) {\r\n        if (this.isView) {\r\n          configCode = 'PRONestingScheduleDetail'\r\n        } else {\r\n          configCode = 'PRONestingScheduleConfig'\r\n        }\r\n      } else {\r\n        configCode = this.isCom\r\n          ? (this.isView ? 'PROComViewPageTbConfig' : 'PROComDraftPageTbConfig')\r\n          : (this.isView ? 'PROPartViewPageTbConfig_new' : 'PROPartDraftPageTbConfig_new')\r\n      }\r\n      this.gridCode = configCode\r\n      await this.getTableConfig(configCode)\r\n      if (!this.workshopEnabled) {\r\n        this.columns = this.columns.filter(v => v.Code !== 'Workshop_Name')\r\n      }\r\n      if (!this.hasCraft) {\r\n        this.columns = this.columns.filter(v => v.Code !== 'Technology_Code')\r\n      }\r\n      this.checkOwner()\r\n    },\r\n    async changeColumn() {\r\n      await this.getTableConfig(this.gridCode)\r\n      this.tbKey++\r\n    },\r\n    /*    handleNodeClick(data) {\r\n      console.log('data', data)\r\n      if (this.areaId === data.Id) {\r\n        return\r\n      }\r\n      this.\r\n       = true\r\n      if (!data.ParentNodes || data.Children?.length > 0) {\r\n        return\r\n      }\r\n      if (data?.Data[this.statusCode] === '未导入') {\r\n        this.$message({\r\n          message: '清单未导入，请联系深化人员导入清单',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n\r\n      const initData = ({ Data }) => {\r\n        this.areaId = Data.Id\r\n        this.projectId = Data.Project_Id\r\n        this.expandedKey = this.areaId\r\n        this.formInline.Finish_Date = ''\r\n        this.formInline.InstallUnit_Id = ''\r\n        this.formInline.Remark = ''\r\n        this.tbData = []\r\n        this.getAreaInfo()\r\n        this.getInstallUnitIdNameList()\r\n      }\r\n\r\n      if (this.tbData.length) {\r\n        this.$confirm('切换区域右侧数据清空，是否确认?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          initData(data)\r\n          this.disabledAdd = false\r\n          this.tbDataMap = {}\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n      } else {\r\n        this.disabledAdd = false\r\n        initData(data)\r\n      }\r\n    },*/\r\n\r\n    /* customFilterFun(value, data, node) {\r\n      const arr = value.split(SPLIT_SYMBOL)\r\n      const labelVal = arr[0]\r\n      const statusVal = arr[1]\r\n      if (!value) return true\r\n      let parentNode = node.parent\r\n      let labels = [node.label]\r\n      let status = [data.Data[this.statusCode]]\r\n      let level = 1\r\n      while (level < node.level) {\r\n        labels = [...labels, parentNode.label]\r\n        status = [...status, data.Data[this.statusCode]]\r\n        parentNode = parentNode.parent\r\n        level++\r\n      }\r\n      labels = labels.filter(v => !!v)\r\n      status = status.filter(v => !!v)\r\n      let resultLabel = true\r\n      let resultStatus = true\r\n      if (this.statusType) {\r\n        resultStatus = status.some(s => s.indexOf(statusVal) !== -1)\r\n      }\r\n      if (this.projectName) {\r\n        resultLabel = labels.some(s => s.indexOf(labelVal) !== -1)\r\n      }\r\n      return resultLabel && resultStatus\r\n    },*/\r\n    async fetchData() {\r\n      this.tbLoading = true\r\n      let resData = []\r\n      if (this.isNest) {\r\n        if (this.isView) {\r\n          resData = await this.getPartPageList()\r\n        } else {\r\n          resData = await this.getNestPageList()\r\n        }\r\n      } else {\r\n        resData = await this.getPartPageList()\r\n      }\r\n\r\n      this.initTbData(resData)\r\n      this.tbLoading = false\r\n    },\r\n    fetchTreeDataLocal() {\r\n      // this.filterText = this.projectName\r\n    },\r\n    fetchTreeStatus() {\r\n      // this.filterText = this.statusType\r\n    },\r\n    /*    fetchTreeData() {\r\n      this.treeLoading = true\r\n      GetProjectAreaTreeList({ projectName: this.projectName, type: this.isCom ? 1 : 2 }).then((res) => {\r\n        if (res.Data.length === 0) {\r\n          this.treeLoading = false\r\n          return\r\n        }\r\n        const resData = res.Data.map(item => {\r\n          item.Is_Directory = true\r\n          return item\r\n        })\r\n        this.treeData = resData\r\n        this.setKey()\r\n        this.treeLoading = false\r\n      })\r\n    },*/\r\n    // setKey() {\r\n    //   const deepFilter = (tree) => {\r\n    //     for (let i = 0; i < tree.length; i++) {\r\n    //       const item = tree[i]\r\n    //       const { Data, Children } = item\r\n    //       console.log(Data)\r\n    //       if (Data.ParentId && !Children?.length) {\r\n    //         this.handleNodeClick(item)\r\n    //         return\r\n    //       } else {\r\n    //         if (Children && Children.length > 0) {\r\n    //           return deepFilter(Children)\r\n    //         }\r\n    //       }\r\n    //     }\r\n    //   }\r\n    //   return deepFilter(this.treeData)\r\n    // },\r\n    closeView() {\r\n      closeTagView(this.$store, this.$route)\r\n    },\r\n    checkWorkshopIsOpen() {\r\n      this.workShopIsOpen = true\r\n    },\r\n    tbSelectChange(array) {\r\n      this.multipleSelection = array.records\r\n    },\r\n    getAreaInfo() {\r\n      this.formInline.Finish_Date = ''\r\n      AreaGetEntity({\r\n        id: this.areaId\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (!res.Data) {\r\n            return []\r\n          }\r\n\r\n          const start = moment(res.Data?.Demand_Begin_Date)\r\n          const end = moment(res.Data?.Demand_End_Date)\r\n          this.pickerOptions.disabledDate = (time) => {\r\n            return time.getTime() < start || time.getTime() > end\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n      this.openAddDraft = false\r\n    },\r\n    getNestPageList() {\r\n      return new Promise((resolve, reject) => {\r\n        GetCanSchdulingPartList({\r\n          Ids: this.nestIds\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            const _list = res?.Data || []\r\n            const list = _list.map(v => {\r\n              v.Part_Used_Process = v.Scheduled_Used_Process || v.Part_Type_Used_Process\r\n              // v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n              v.Workshop_Id = v.Scheduled_Workshop_Id\r\n              v.Workshop_Name = v.Scheduled_Workshop_Name\r\n              v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n              v.chooseCount = v.Can_Schduling_Count\r\n\r\n              return v\r\n            })\r\n\r\n            resolve(list)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    getComPageList() {\r\n      return new Promise((resolve, reject) => {\r\n        const {\r\n          pid\r\n        } = this.$route.query\r\n        GetCompSchdulingInfoDetail({\r\n          Schduling_Plan_Id: pid\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            const { Schduling_Plan, Schduling_Comps, Process_List } = res.Data\r\n            this.formInline = Object.assign(this.formInline, Schduling_Plan)\r\n            Process_List.forEach(item => {\r\n              const plist = {\r\n                key: item.Process_Code,\r\n                value: item\r\n              }\r\n              this.changeProcessList(plist)\r\n            })\r\n            const list = Schduling_Comps.map(v => {\r\n              v.chooseCount = v.Can_Schduling_Count\r\n              return v\r\n            })\r\n            resolve(list || [])\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    async getPartPageList() {\r\n      const {\r\n        pid\r\n      } = this.$route.query\r\n      const result = await GetPartSchdulingInfoDetail({\r\n        Schduling_Plan_Id: pid\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const SarePartsModel = res.Data?.SarePartsModel.map(v => {\r\n            if (v.Scheduled_Used_Process) {\r\n              // 已存在操作过数据\r\n              v.Part_Used_Process = v.Scheduled_Used_Process\r\n            }\r\n            v.chooseCount = v.Can_Schduling_Count\r\n            return v\r\n          })\r\n          this.formInline = Object.assign(this.formInline, res.Data?.Schduling_Plan)\r\n          this.getStopList(SarePartsModel)\r\n          return SarePartsModel || []\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      return result || []\r\n    },\r\n    async getStopList(list) {\r\n      const submitObj = list.map(item => {\r\n        return {\r\n          Id: item.Part_Aggregate_Id,\r\n          Type: 1\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = !!item.Is_Stop\r\n          })\r\n          list.forEach(row => {\r\n            if (stopMap[row.Part_Aggregate_Id]) {\r\n              this.$set(row, 'stopFlag', stopMap[row.Part_Aggregate_Id])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    initTbData(list, teamKey = 'Allocation_Teams') {\r\n      console.log(5, JSON.parse(JSON.stringify(list)))\r\n      this.tbData = list.map(row => {\r\n        const processList = row.Technology_Path?.split('/') || []\r\n        row.uuid = uuidv4()\r\n        this.addElementToTbData(row)\r\n        if (row[teamKey]) {\r\n          const newData = row[teamKey].filter((r) => processList.findIndex((p) => r.Process_Code === p) !== -1)\r\n          newData.forEach((ele, index) => {\r\n            const code = this.getRowUnique(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n            const max = this.getRowUniqueMax(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n            row[code] = ele.Count\r\n            row[max] = 0\r\n          })\r\n        }\r\n        this.setInputMax(row)\r\n        return row\r\n      })\r\n      let ids = ''\r\n      if (this.isCom) {\r\n        ids = this.tbData.map(v => v.Comp_Import_Detail_Id).toString()\r\n      } else {\r\n        ids = this.tbData.map(v => v.Part_Aggregate_Id).toString()\r\n      }\r\n      this.currentIds = ids\r\n    },\r\n    async mergeCraftProcess(list) {\r\n      let codes = [...new Set(list.map(v => v.Technology_Code))]\r\n      for (const key in this.craftCodeMap) {\r\n        if (this.craftCodeMap.hasOwnProperty(key)) {\r\n          codes = codes.filter(code => code !== key)\r\n        }\r\n      }\r\n      const _craftCodeMap = await this.getCraftProcess(codes)\r\n      Object.assign(this.craftCodeMap, _craftCodeMap)\r\n    },\r\n    getCraftProcess(gyGroup = []) {\r\n      gyGroup = gyGroup.filter(v => !!v)\r\n      if (!gyGroup.length) return Promise.resolve({})\r\n      return new Promise((resolve, reject) => {\r\n        GetProcessFlowListWithTechnology({\r\n          TechnologyCodes: gyGroup,\r\n          Type: 2\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            const gyList = res.Data || []\r\n            const gyMap = gyList.reduce((acc, item) => {\r\n              acc[item.Code] = item.Technology_Path\r\n              return acc\r\n            }, {})\r\n            console.log('gyMap', gyMap)\r\n            resolve(gyMap)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    async mergeSelectList(newList) {\r\n      if (this.hasCraft) {\r\n        await this.mergeCraftProcess(newList)\r\n      }\r\n      console.time('mergeSelectListTime')\r\n      let hasUsedPartFlag = true\r\n\r\n      newList.forEach((element, index) => {\r\n        const cur = this.getMergeUniqueRow(element)\r\n\r\n        if (!cur) {\r\n          console.log('element', element)\r\n          element.puuid = element.uuid\r\n          element.Schduled_Count = element.chooseCount\r\n          element.Schduled_Weight = numeral(element.chooseCount * element.Weight).format('0.[00]')\r\n\r\n          if (this.hasCraft && !element.Technology_Path) {\r\n            if (this.craftCodeMap[element.Technology_Code] && this.craftCodeMap[element.Technology_Code] instanceof Array) {\r\n              const curPathArr = this.craftCodeMap[element.Technology_Code]\r\n              if (element.Part_Used_Process && !curPathArr.includes(element.Part_Used_Process)) {\r\n                hasUsedPartFlag = false\r\n              } else {\r\n                element.Technology_Path = curPathArr.join('/')\r\n              }\r\n            }\r\n          }\r\n\r\n          this.tbData.push(element)\r\n          this.addElementToTbData(element)\r\n          return\r\n        }\r\n\r\n        cur.puuid = element.uuid\r\n\r\n        cur.Schduled_Count += element.chooseCount\r\n        cur.Schduled_Weight = numeral(cur.Schduled_Weight).add(element.chooseCount * element.Weight).format('0.[00]')\r\n        if (!cur.Technology_Path) {\r\n          return\r\n        }\r\n        this.setInputMax(cur)\r\n      })\r\n\r\n      // if (this.isCom) {\r\n      //   this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n      // } else {\r\n      //   this.tbData.sort((a, b) => a.Part_Code.localeCompare(b.Part_Code))\r\n      // }\r\n      this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n      console.timeEnd('fff')\r\n      console.log('this.tbDataMap', this.tbDataMap, this.tbData)\r\n    },\r\n    addElementToTbData(element) {\r\n      const key = this.getUniKey(element)\r\n      this.tbDataMap[key] = element\r\n    },\r\n    getMergeUniqueRow(element) {\r\n      const key = this.getUniKey(element)\r\n      return this.tbDataMap[key]\r\n    },\r\n    getUniKey(element) {\r\n      return getUnique(this.isCom, element)\r\n    },\r\n    checkForm() {\r\n      let isValidate = true\r\n      this.$refs['formInline'].validate((valid) => {\r\n        if (!valid) isValidate = false\r\n      })\r\n      return isValidate\r\n    },\r\n    async saveDraft(isOrder = false) {\r\n      const checkSuccess = this.checkForm()\r\n      if (!checkSuccess) return false\r\n      const { tableData, status } = this.getSubmitTbInfo()\r\n      if (!status) return false\r\n      if (!isOrder) {\r\n        this.saveLoading = true\r\n      }\r\n\r\n      const isSuccess = await this.handleSaveDraft(tableData, isOrder)\r\n      console.log('isSuccess', isSuccess)\r\n      if (!isSuccess) return false\r\n      if (isOrder) return isSuccess\r\n      this.$refs['draft']?.fetchData()\r\n      this.saveLoading = false\r\n    },\r\n    async saveWorkShop() {\r\n      const checkSuccess = this.checkForm()\r\n      if (!checkSuccess) return false\r\n      const obj = {}\r\n      if (!this.tbData.length) {\r\n        this.$message({\r\n          message: '数据不能为空',\r\n          type: 'success'\r\n        })\r\n        return\r\n      }\r\n      if (this.isCom) {\r\n        obj.Schduling_Comps = this.tbData\r\n      } else {\r\n        obj.SarePartsModel = this.tbData\r\n      }\r\n      if (this.isEdit) {\r\n        obj.Schduling_Plan = this.formInline\r\n      } else {\r\n        obj.Schduling_Plan = {\r\n          ...this.formInline,\r\n          Project_Id: this.projectId,\r\n          Area_Id: this.areaId,\r\n          Schduling_Model: this.model // 1构件单独排产，2零件单独排产，3构/零件一起排产\r\n        }\r\n      }\r\n      this.pgLoading = true\r\n      const _fun = this.isCom ? SaveComponentSchedulingWorkshop : SavePartSchedulingWorkshopNew\r\n      _fun(obj).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.pgLoading = false\r\n          this.$message({\r\n            message: '保存成功',\r\n            type: 'success'\r\n          })\r\n          this.closeView()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.pgLoading = false\r\n        }\r\n      })\r\n    },\r\n    getSubmitTbInfo() {\r\n      // 处理上传的数据\r\n      let tableData = JSON.parse(JSON.stringify(this.tbData))\r\n      tableData = tableData.filter(item => item.Schduled_Count > 0)\r\n      for (let i = 0; i < tableData.length; i++) {\r\n        const element = tableData[i]\r\n        let list = []\r\n        if (!element.Technology_Path) {\r\n          this.$message({\r\n            message: '工序不能为空',\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        if (this.isPartPrepare && !element.Part_Used_Process && element.Type !== 'Direct' && this.comPart) {\r\n          const msg = '领用工序不能为空'\r\n          if (this.isNest) {\r\n            if (element.Comp_Import_Detail_Id) {\r\n              this.$message({\r\n                message: msg,\r\n                type: 'warning'\r\n              })\r\n              return { status: false }\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: msg,\r\n              type: 'warning'\r\n            })\r\n            return { status: false }\r\n          }\r\n        }\r\n        // if (!this.isCom && element.Comp_Import_Detail_Id && !element.Part_Used_Process) {\r\n        //   // 零构件 零件单独排产\r\n        //   this.$message({\r\n        //     message: '零件领用工序不能为空',\r\n        //     type: 'warning'\r\n        //   })\r\n        //   return { status: false }\r\n        // }\r\n        if (element.Scheduled_Technology_Path && element.Scheduled_Technology_Path !== element.Technology_Path) {\r\n          this.$message({\r\n            message: `请和该区域批次下已排产同${this.partName}保持工序一致`,\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        if (element.Scheduled_Used_Process && element.Scheduled_Used_Process !== element.Part_Used_Process) {\r\n          this.$message({\r\n            message: `请和该区域批次下已排产同${this.partName}领用工序保持一致`,\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        const processList = Array.from(new Set(element.Technology_Path.split('/')))\r\n        // processList.forEach(code => {\r\n        // const groups = this.workingTeam.filter(v => v.Process_Code === code)\r\n        // const groupsList = groups.map(group => {\r\n        //   const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)\r\n        //   const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)\r\n        //   const obj = {\r\n        //     Team_Task_Id: element.Team_Task_Id,\r\n        //     Comp_Code: element.Comp_Code,\r\n        //     Again_Count: +element[uCode] || 0, // 不填，后台让传0\r\n        //     Part_Code: this.isCom ? null : '',\r\n        //     Process_Code: code,\r\n        //     Technology_Path: element.Technology_Path,\r\n        //     Working_Team_Id: group.Working_Team_Id,\r\n        //     Working_Team_Name: group.Working_Team_Name\r\n        //   }\r\n        //   delete element[uCode]\r\n        //   delete element[uMax]\r\n        //   return obj\r\n        // })\r\n        // list.push(...groupsList)\r\n        // })\r\n        for (let j = 0; j < processList.length; j++) {\r\n          const code = processList[j]\r\n          const schduledCount = element.Schduled_Count || 0\r\n          let groups = []\r\n          if (element.Allocation_Teams) {\r\n            groups = element.Allocation_Teams.filter(v => v.Process_Code === code)\r\n          }\r\n          const againCount = groups.reduce((acc, cur) => {\r\n            return acc + (cur.Again_Count || 0)\r\n          }, 0)\r\n          if (againCount > schduledCount) {\r\n            list = []\r\n            break\r\n          } else {\r\n            list.push(...groups)\r\n          }\r\n        }\r\n        const hasInput = Object.keys(element).filter(_ => _.startsWith(element['uuid']))\r\n        hasInput.forEach((item) => {\r\n          delete element[item]\r\n        })\r\n        delete element['uuid']\r\n        delete element['_X_ROW_KEY']\r\n        delete element['puuid']\r\n        element.Allocation_Teams = list\r\n      }\r\n      return { tableData, status: true }\r\n    },\r\n    async handleSaveDraft(tableData, isOrder) {\r\n      console.log('保存草稿')\r\n      const _fun = this.isCom ? SaveCompSchdulingDraft : SavePartSchdulingDraftNew\r\n      const obj = {}\r\n      // if (this.isCom) {\r\n      // obj.Schduling_Comps = tableData\r\n      const p = []\r\n      for (const objKey in this.processList) {\r\n        if (this.processList.hasOwnProperty(objKey)) {\r\n          p.push(this.processList[objKey])\r\n        }\r\n      }\r\n      obj.Process_List = p\r\n      // } else {\r\n      obj.SarePartsModel = tableData\r\n      // }\r\n      if (this.isEdit) {\r\n        obj.Schduling_Plan = this.formInline\r\n      } else {\r\n        obj.Schduling_Plan = {\r\n          ...this.formInline,\r\n          Project_Id: this.projectId,\r\n          Area_Id: this.areaId,\r\n          Schduling_Model: this.model // 1构件单独排产，2零件单独排产，3构/零件一起排产\r\n        }\r\n      }\r\n      let orderSuccess = false\r\n      console.log('obj', obj)\r\n\r\n      await _fun(obj).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (!isOrder) {\r\n            this.pgLoading = false\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            this.closeView()\r\n          } else {\r\n            this.templateScheduleCode = res.Data\r\n            orderSuccess = true\r\n            console.log('保存草稿成功 ')\r\n          }\r\n        } else {\r\n          this.saveLoading = false\r\n          this.pgLoading = false\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      console.log('结束 ')\r\n      return orderSuccess\r\n    },\r\n    handleDelete() {\r\n      this.deleteLoading = true\r\n      setTimeout(() => {\r\n        const selectedUuids = new Set(this.multipleSelection.map(v => v.uuid))\r\n        this.tbData = this.tbData.filter(item => {\r\n          const isSelected = selectedUuids.has(item.uuid)\r\n          if (isSelected) {\r\n            const key = this.getUniKey(item)\r\n            delete this.tbDataMap[key]\r\n          }\r\n          return !isSelected\r\n        })\r\n        // this.$nextTick(_ => {\r\n        //   const _list = this.multipleSelection.filter(v => v.puuid)\r\n        //   this.$refs['draft']?.mergeData(_list)\r\n        //   this.multipleSelection = []\r\n        // })\r\n        this.deleteLoading = false\r\n      }, 0)\r\n    },\r\n    async getWorkTeam() {\r\n      await GetSchdulingWorkingTeams({\r\n        type: this.isCom ? 1 : 2\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.workingTeam = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      this.$refs['formInline'].validate((valid) => {\r\n        if (!valid) return\r\n        const { tableData, status } = this.getSubmitTbInfo()\r\n        if (!status) return\r\n        this.$confirm('是否提交当前数据?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.saveDraftDoSubmit(tableData)\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n      })\r\n    },\r\n    async saveDraftDoSubmit() {\r\n      this.pgLoading = true\r\n      if (this.formInline?.Schduling_Code) {\r\n        const isSuccess = await this.saveDraft(true)\r\n        console.log('saveDraftDoSubmit', isSuccess)\r\n        isSuccess && this.doSubmit(this.formInline.Id)\r\n      } else {\r\n        const isSuccess = await this.saveDraft(true)\r\n        isSuccess && this.doSubmit(this.templateScheduleCode)\r\n      }\r\n    },\r\n    doSubmit(scheduleCode) {\r\n      SaveSchdulingTaskById({\r\n        schdulingPlanId: scheduleCode\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '下达成功',\r\n            type: 'success'\r\n          })\r\n          this.closeView()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.pgLoading = false\r\n      }).catch(_ => {\r\n        this.pgLoading = false\r\n      })\r\n    },\r\n    getWorkShop(value) {\r\n      console.log('value', value)\r\n      const {\r\n        origin,\r\n        row,\r\n        workShop: {\r\n          Id,\r\n          Display_Name\r\n        }\r\n      } = value\r\n      if (origin === 2) {\r\n        if (value.workShop?.Id) {\r\n          row.Workshop_Name = Display_Name\r\n          row.Workshop_Id = Id\r\n          this.setPath(row, Id)\r\n        } else {\r\n          row.Workshop_Name = ''\r\n          row.Workshop_Id = ''\r\n        }\r\n      } else {\r\n        this.multipleSelection.forEach(item => {\r\n          if (value.workShop?.Id) {\r\n            item.Workshop_Name = Display_Name\r\n            item.Workshop_Id = Id\r\n            this.setPath(item, Id)\r\n          } else {\r\n            item.Workshop_Name = ''\r\n            item.Workshop_Id = ''\r\n          }\r\n        })\r\n      }\r\n    },\r\n    setPath(row, Id) {\r\n      if (row?.Scheduled_Workshop_Id) {\r\n        if (row.Scheduled_Workshop_Id !== Id) {\r\n          row.Technology_Path = ''\r\n        }\r\n      } else {\r\n        row.Technology_Path = ''\r\n      }\r\n    },\r\n    handleBatchWorkshop(origin, row) {\r\n      this.title = origin === 1 ? '批量分配车间' : '分配车间'\r\n      this.currentComponent = 'Workshop'\r\n      this.dWidth = '30%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].fetchData(origin, row)\r\n      })\r\n    },\r\n\r\n    getProcessOption(workshopId) {\r\n      return new Promise((resolve, reject) => {\r\n        GetProcessListBase({\r\n          workshopId: workshopId,\r\n          type: 2 // 0:全部，工艺类型1：构件工艺，2：零件工艺\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            const process = res.Data.map(v => v.Code)\r\n            resolve(process)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    setLibType(code, workshopId) {\r\n      return new Promise((resolve) => {\r\n        const obj = {\r\n          Component_type: code,\r\n          type: 1\r\n        }\r\n        if (this.workshopEnabled) {\r\n          obj.workshopId = workshopId\r\n        }\r\n        GetLibListType(obj).then(res => {\r\n          if (res.IsSucceed) {\r\n            if (res.Data.Data && res.Data.Data.length) {\r\n              const info = res.Data.Data[0]\r\n              const workCode = info.WorkCode && info.WorkCode.replace(/\\\\/g, '/')\r\n              resolve(workCode)\r\n            } else {\r\n              resolve(undefined)\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    inputChange(row) {\r\n      this.setInputMax(row)\r\n    },\r\n    setInputMax(row) {\r\n      const inputValuesKeys = Object.keys(row)\r\n        .filter(v => !v.endsWith('max') && v.startsWith(row.uuid) && v.length > row.uuid.length)\r\n      inputValuesKeys.forEach((val) => {\r\n        const curCode = val.split(SPLIT_SYMBOL)[1]\r\n        const otherTotal = inputValuesKeys.filter(x => {\r\n          const code = x.split(SPLIT_SYMBOL)[1]\r\n          return x !== val && code === curCode\r\n        }).reduce((acc, item) => {\r\n          return acc + numeral(row[item]).value()\r\n        }, 0)\r\n        row[val + SPLIT_SYMBOL + 'max'] = row.Schduled_Count - otherTotal\r\n      })\r\n    },\r\n    sendProcess({ arr, str }) {\r\n      let isSuccess = true\r\n      for (let i = 0; i < arr.length; i++) {\r\n        const item = arr[i]\r\n        if (item.originalPath && item.originalPath !== str) {\r\n          isSuccess = false\r\n          break\r\n        }\r\n        item.Technology_Path = str\r\n      }\r\n      if (!isSuccess) {\r\n        this.$message({\r\n          message: '请和该区域批次下已排产同构件保持工序一致',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    resetWorkTeamMax(row, str) {\r\n      if (str) {\r\n        row.Technology_Path = str\r\n      } else {\r\n        str = row.Technology_Path\r\n      }\r\n      const list = str?.split('/') || []\r\n      this.workingTeam.forEach((element, idx) => {\r\n        const cur = list.some(k => k === element.Process_Code)\r\n        const code = this.getRowUnique(row.uuid, element.Process_Code, element.Working_Team_Id)\r\n        const max = this.getRowUniqueMax(row.uuid, element.Process_Code, element.Working_Team_Id)\r\n        if (cur) {\r\n          if (!row[code]) {\r\n            this.$set(row, code, 0)\r\n            this.$set(row, max, row.Schduled_Count)\r\n          }\r\n        } else {\r\n          this.$delete(row, code)\r\n          this.$delete(row, max)\r\n        }\r\n      })\r\n    },\r\n    checkPermissionTeam(processStr, processCode) {\r\n      if (!processStr) return false\r\n      const list = processStr?.split('/') || []\r\n      return !!list.some(v => v === processCode)\r\n    },\r\n\r\n    async getTableConfig(code) {\r\n      await GetGridByCode({\r\n        code\r\n      }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n          const list = Data.ColumnList || []\r\n          this.ownerColumn = list.find(item => item.Code === 'Part_Used_Process')\r\n          this.ownerColumn2 = list.find(item => item.Code === 'Is_Main_Part')\r\n          this.columns = this.setColumnDisplay(list)\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setColumnDisplay(list) {\r\n      return list.filter(v => v.Is_Display)\r\n      // .map(item => {\r\n      //   if (FIX_COLUMN.includes(item.Code)) {\r\n      //     item.fixed = 'left'\r\n      //   }\r\n      //   return item\r\n      // })\r\n    },\r\n    activeCellMethod({ row, column, columnIndex }) {\r\n      if (this.isView) return false\r\n      const processCode = column.field?.split('$_$')[1]\r\n      return this.checkPermissionTeam(row.Technology_Path, processCode)\r\n    },\r\n    openBPADialog(type, row) {\r\n      if (this.workshopEnabled) {\r\n        if (type === 1) {\r\n          const IsUnique = this.checkIsUniqueWorkshop()\r\n          if (!IsUnique) return\r\n        }\r\n      }\r\n      this.title = type === 2 ? '工序调整' : '批量工序调整'\r\n      this.currentComponent = 'BatchProcessAdjust'\r\n      this.dWidth = '50%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].setData(type === 2 ? [row] : this.multipleSelection, type === 2 ? row.Technology_Path : '')\r\n      })\r\n    },\r\n    checkIsUniqueWorkshop() {\r\n      let isUnique = true\r\n      const firstV = this.multipleSelection[0].Workshop_Name\r\n      for (let i = 1; i < this.multipleSelection.length; i++) {\r\n        const item = this.multipleSelection[i]\r\n        if (item.Workshop_Name !== firstV) {\r\n          isUnique = false\r\n          break\r\n        }\r\n      }\r\n      if (!isUnique) {\r\n        this.$message({\r\n          message: '批量分配工序时只有相同车间下的才可一起批量分配',\r\n          type: 'warning'\r\n        })\r\n      }\r\n      return isUnique\r\n    },\r\n    checkHasWorkShop(type, arr) {\r\n      let hasWorkShop = true\r\n      for (let i = 0; i < arr.length; i++) {\r\n        const item = arr[i]\r\n        if (!item.Workshop_Name) {\r\n          hasWorkShop = false\r\n          break\r\n        }\r\n      }\r\n      if (!hasWorkShop) {\r\n        this.$message({\r\n          message: '请先选择车间后再进行工序分配',\r\n          type: 'warning'\r\n        })\r\n      }\r\n      return hasWorkShop\r\n    },\r\n    handleAddDialog(type = 'add') {\r\n      this.title = `添加${this.partName}`\r\n\r\n      this.currentComponent = 'AddDraft'\r\n      this.dWidth = '96%'\r\n      this.openAddDraft = true\r\n\r\n      this.setAddTbKey()\r\n\r\n      this.$nextTick(_ => {\r\n        this.$refs['draft'].initData()\r\n      })\r\n    },\r\n    async addToTbList(newList) {\r\n      await this.mergeSelectList(newList)\r\n      this.setAddTbKey()\r\n    },\r\n    setAddTbKey() {\r\n      const selectKeys = this.tbData.filter(cur => cur.puuid).map(v => v.puuid)\r\n      this.changeAddTbKeys(selectKeys)\r\n    },\r\n    getRowUnique(uuid, processCode, workingId) {\r\n      return `${uuid}${SPLIT_SYMBOL}${processCode}${SPLIT_SYMBOL}${workingId}`\r\n    },\r\n    getRowUniqueMax(uuid, processCode, workingId) {\r\n      return this.getRowUnique(uuid, processCode, workingId) + `${SPLIT_SYMBOL}max`\r\n    },\r\n    handleSelectMenu(v) {\r\n      if (v === 'process') {\r\n        this.openBPADialog(1)\r\n      } else if (v === 'craft') {\r\n        this.handleSetCraftProcess()\r\n      }\r\n    },\r\n    async handleSetCraftProcess() {\r\n      const showSuccess = () => {\r\n        this.$message({\r\n          message: '已分配成功',\r\n          type: 'success'\r\n        })\r\n      }\r\n      const rowList = this.multipleSelection.map(v => v.Technology_Code).filter(v => !!v)\r\n      if (!rowList.length) {\r\n        this.$message({\r\n          message: '工艺代码不存在',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      await this.mergeCraftProcess(this.multipleSelection)\r\n      const workshopIds = Array.from(new Set(this.multipleSelection.map(v => v.Workshop_Id).filter(v => !!v)))\r\n      const w_process = []\r\n      if (workshopIds.length) {\r\n        workshopIds.forEach(workshopId => {\r\n          w_process.push(this.getProcessOption(workshopId).then(result => ({\r\n            [workshopId]: result\r\n          })))\r\n        })\r\n        const workshopPromise = Promise.all(w_process).then((values) => {\r\n          return Object.assign({}, ...values)\r\n        })\r\n        workshopPromise.then(workshop => {\r\n          let flag = true\r\n          for (let i = 0; i < this.multipleSelection.length; i++) {\r\n            const curRow = this.multipleSelection[i]\r\n            const workshopProcess = workshop[curRow.Workshop_Id]\r\n            const craftArray = this.craftCodeMap[curRow.Technology_Code]\r\n            if (craftArray) {\r\n              const isIncluded = craftArray.every(process => workshopProcess.includes(process))\r\n              if (!isIncluded) {\r\n                flag = false\r\n                continue\r\n              }\r\n              curRow.Technology_Path = craftArray.join('/')\r\n            }\r\n          }\r\n          if (!flag) {\r\n            setTimeout(() => {\r\n              this.$alert('所选车间下班组加工工序不包含工艺代码工序请手动排产', '提示', {\r\n                confirmButtonText: '确定'\r\n              })\r\n            }, 200)\r\n          }\r\n\r\n          flag && showSuccess()\r\n        })\r\n      } else {\r\n        this.multipleSelection.forEach((curRow) => {\r\n          const craftArray = this.craftCodeMap[curRow.Technology_Code]\r\n          if (craftArray) {\r\n            curRow.Technology_Path = craftArray.join('/')\r\n          }\r\n        })\r\n        showSuccess()\r\n      }\r\n    },\r\n\r\n    handleBatchOwner(type, row) {\r\n      this.title = `${type === 1 ? '批量' : ''}分配领用工序`\r\n      this.currentComponent = 'OwnerProcess'\r\n      this.dWidth = '30%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].setOption(type === 2, type === 2 ? [row] : this.multipleSelection)\r\n      })\r\n    },\r\n    handleReverse() {\r\n      const cur = []\r\n      this.tbData.forEach((element, idx) => {\r\n        element.checked = !element.checked\r\n        if (element.checked) {\r\n          cur.push(element)\r\n        }\r\n      })\r\n      this.multipleSelection = cur\r\n      if (this.multipleSelection.length === this.tbData.length) {\r\n        this.$refs['xTable'].setAllCheckboxRow(true)\r\n      }\r\n      if (this.multipleSelection.length === 0) {\r\n        this.$refs['xTable'].setAllCheckboxRow(false)\r\n      }\r\n    },\r\n    // tbFilterChange() {\r\n    //   const xTable = this.$refs.xTable\r\n    //   const column = xTable.getColumnByField('Type_Name')\r\n    //   if (!column?.filters?.length) return\r\n    //   column.filters.forEach(d => {\r\n    //     d.checked = d.value === this.searchType\r\n    //   })\r\n    //   xTable.updateData()\r\n    // },\r\n    getType() {\r\n      const getCompTree = () => {\r\n        const fun = this.isCom ? GetCompTypeTree : GetPartTypeList\r\n        fun({}).then(res => {\r\n          if (res.IsSucceed) {\r\n            let result = res.Data\r\n            if (!this.isCom) {\r\n              result = result\r\n                .map((v, idx) => {\r\n                  return {\r\n                    Data: v.Name,\r\n                    Label: v.Name\r\n                  }\r\n                })\r\n            }\r\n            this.treeParamsComponentType.data = result\r\n            this.$nextTick((_) => {\r\n              this.$refs.treeSelectComponentType?.treeDataUpdateFun(result)\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n\r\n      getCompTree()\r\n    },\r\n    // 查看图纸\r\n    handleDwg(row) {\r\n      const obj = {}\r\n      if (this.isCom) {\r\n        obj.Comp_Id = row.Comp_Import_Detail_Id\r\n      } else {\r\n        obj.Part_Id = row.Part_Aggregate_Id\r\n      }\r\n\r\n      GetSteelCadAndBimId({ importDetailId: obj.Part_Id }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.extensionName = res.Data[0].ExtensionName\r\n          this.fileBim = res.Data[0].fileBim\r\n          this.IsUploadCad = res.Data[0].IsUpload\r\n          this.cadRowCode = row.Part_Code\r\n          this.cadRowProjectId = row.Sys_Project_Id\r\n          this.fileView()\r\n        }\r\n      })\r\n      // GetDwg(obj).then(res => {\r\n      //   if (res.IsSucceed) {\r\n      //     const fileurl = res?.Data?.length && res.Data[0].File_Url\r\n      //     window.open('http://dwgv1.bimtk.com:5432/?CadUrl=' + parseOssUrl(fileurl), '_blank')\r\n      //   } else {\r\n      //     this.$message({\r\n      //       message: res.Message,\r\n      //       type: 'error'\r\n      //     })\r\n      //   }\r\n      // })\r\n    },\r\n    setProcessList(info) {\r\n      this.changeProcessList(info)\r\n    },\r\n    resetInnerForm() {\r\n      this.$refs['searchForm'].resetFields()\r\n      this.$refs.xTable.clearFilter()\r\n    },\r\n    innerFilter() {\r\n      this.multipleSelection = []\r\n      const arr = []\r\n      if (this.isCom) {\r\n        arr.push('Type', 'Comp_Code', 'Spec', 'Is_Component')\r\n      } else {\r\n        arr.push('Part_Code', 'Spec', 'Type_Name', 'Project_Name', 'Area_Name', 'InstallUnit_Name')\r\n      }\r\n\r\n      const xTable = this.$refs.xTable\r\n      xTable.clearCheckboxRow()\r\n      arr.forEach((element, idx) => {\r\n        const column = xTable.getColumnByField(element)\r\n        if (element === 'Is_Component') {\r\n          column.filters.forEach((option, idx) => {\r\n            option.checked = idx === (this.innerForm.searchDirect ? 0 : 1)\r\n          })\r\n        }\r\n        if (element === 'Spec') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.searchSpecSearch\r\n          option.checked = true\r\n        }\r\n        if (element === 'Type' || element === 'Type_Name') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.searchComTypeSearch\r\n          option.checked = true\r\n        }\r\n        if (element === 'Comp_Code' || element === 'Part_Code') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.searchContent\r\n          option.checked = true\r\n        }\r\n        if (element === 'Project_Name') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.projectName\r\n          option.checked = true\r\n        }\r\n        if (element === 'Area_Name') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.areaName\r\n          option.checked = true\r\n        }\r\n        if (element === 'InstallUnit_Name') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.installName\r\n          option.checked = true\r\n        }\r\n      })\r\n      xTable.updateData()\r\n    },\r\n    filterComponentMethod({ option, row }) {\r\n      if (this.innerForm.searchDirect === '') {\r\n        return true\r\n      }\r\n      return row.Is_Component === !this.innerForm.searchDirect\r\n    },\r\n    filterSpecMethod({ option, row }) {\r\n      if (this.innerForm.searchSpecSearch.trim() === '') {\r\n        return true\r\n      }\r\n      const splitAndClean = (input) => input.trim().replace(/\\s+/g, ' ').split(' ')\r\n      const specArray = splitAndClean(this.innerForm.searchSpecSearch)\r\n      return specArray.some(code => (row.Spec || '').includes(code))\r\n    },\r\n\r\n    filterTypeMethod({ option, row }) {\r\n      if (this.innerForm.searchComTypeSearch === '') {\r\n        return true\r\n      }\r\n      const cur = this.isCom ? 'Type' : 'Type_Name'\r\n      return row[cur] === this.innerForm.searchComTypeSearch\r\n    },\r\n    filterCodeMethod({ option, row }) {\r\n      if (this.innerForm.searchContent.trim() === '') {\r\n        return true\r\n      }\r\n\r\n      const splitAndClean = (input) => input.trim().replace(/\\s+/g, ' ').split(' ')\r\n\r\n      const cur = this.isCom ? 'Comp_Code' : 'Part_Code'\r\n\r\n      const arr = splitAndClean(this.innerForm.searchContent)\r\n\r\n      if (this.curSearch === 1) {\r\n        return arr.some(code => row[cur] === code)\r\n      } else {\r\n        for (let i = 0; i < arr.length; i++) {\r\n          const item = arr[i]\r\n          if (row[cur].includes(item)) {\r\n            return true\r\n          }\r\n        }\r\n        return false\r\n      }\r\n    },\r\n    filterProjectMethod({ option, row }) {\r\n      if (option.data === '') {\r\n        return true\r\n      }\r\n      return row.Project_Name === option.data\r\n    },\r\n    filterAreaMethod({ option, row }) {\r\n      if (option.data === '') {\r\n        return true\r\n      }\r\n      return row.Area_Name === option.data\r\n    },\r\n    filterInstallMethod({ option, row }) {\r\n      if (option.data === '') {\r\n        return true\r\n      }\r\n      return row.InstallUnit_Name === option.data\r\n    },\r\n    componentTypeFilter(e) {\r\n      this.$refs?.treeSelectComponentType.filterFun(e)\r\n    },\r\n    getInstallUnitIdNameList(id) {\r\n      if (!this.areaId) {\r\n        this.installUnitIdList = []\r\n        this.disabledAdd = false\r\n      } else {\r\n        this.disabledAdd = true\r\n        GetInstallUnitIdNameList({ Area_Id: this.areaId }).then(res => {\r\n          this.installUnitIdList = res.Data\r\n          if (this.installUnitIdList.length) {\r\n            this.formInline.InstallUnit_Id = this.installUnitIdList[0].Id\r\n          }\r\n          this.disabledAdd = false\r\n        })\r\n      }\r\n    },\r\n    installChange() {\r\n      if (!this.tbData.length) {\r\n        this.$refs['searchForm'].resetFields()\r\n        this.$refs.xTable.clearFilter()\r\n        return\r\n      }\r\n      this.$confirm('切换区域右侧数据清空, 是否确认?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbData = []\r\n        this.resetInnerForm()\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    showPartUsedProcess(row) {\r\n      if (this.isNest) {\r\n        return !!row.Comp_Import_Detail_Id\r\n      } else {\r\n        return !this.isView && row.Type !== 'Direct'\r\n      }\r\n    },\r\n    handleExport() {\r\n      if (!this.tbData.length) {\r\n        this.$message({\r\n          message: '暂无数据',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      this.$refs.xTable.exportData({\r\n        filename: `${this.partName}排产-${this.formInline.Schduling_Code}(${this.partName})`,\r\n        type: 'xlsx',\r\n        data: this.tbData\r\n      })\r\n    },\r\n    handleCloseDrawer() {\r\n      this.drawer = false\r\n    },\r\n    fileView() {\r\n      this.iframeKey = uuidv4()\r\n      this.iframeUrl = `${\r\n        this.baseCadUrl\r\n      }?router=1&iframeId=11&baseUrl=${baseUrl()}&token=${localStorage.getItem(\r\n        'Token'\r\n      )}&auth_id=${localStorage.getItem('Last_Working_Object_Id')}`\r\n      this.drawer = true\r\n    },\r\n    renderIframe() {\r\n      const ExtensionName = this.extensionName\r\n      const fileBim = this.fileBim\r\n      this.iframeUrl = `${\r\n        this.baseCadUrl\r\n      }?router=1&iframeId=11&baseUrl=${baseUrl()}&token=${localStorage.getItem(\r\n        'Token'\r\n      )}&auth_id=${localStorage.getItem('Last_Working_Object_Id')}`\r\n      this.fullscreenid = ExtensionName\r\n      this.fullbimid = fileBim\r\n    },\r\n    fullscreen(v) {\r\n      this.templateUrl = `${\r\n        this.baseCadUrl\r\n      }?router=1&iframeId=13&baseUrl=${baseUrl()}&token=${localStorage.getItem(\r\n        'Token'\r\n      )}&auth_id=${localStorage.getItem('Last_Working_Object_Id')}`\r\n      this.drawersull = true\r\n    },\r\n    frameListener({ data }) {\r\n      if (data.type === 'loaded') {\r\n        console.log('data', data)\r\n        console.error(\r\n          'data.data.iframeId',\r\n          data.data.iframeId,\r\n          typeof data.data.iframeId\r\n        )\r\n        if (data.data.iframeId === '11') {\r\n          document.getElementById('frame').contentWindow.postMessage(\r\n            {\r\n              type: 'router',\r\n              path: '/modelCad',\r\n              query: {\r\n                // baseUrl: baseUrl(),\r\n                cadId: this.fileBim,\r\n                projectId: this.cadRowProjectId,\r\n                steelName: this.cadRowCode,\r\n                showCad: this.IsUploadCad,\r\n                isSubAssembly: false,\r\n                isPart: true\r\n                // cadId: this.fileBim\r\n                // token: localStorage.getItem('Token'),\r\n                // auth_id: localStorage.getItem('Last_Working_Object_Id')\r\n              }\r\n            },\r\n            '*'\r\n          )\r\n        } else if (data.data.iframeId === '13') {\r\n          document.getElementById('fullFrame').contentWindow.postMessage(\r\n            {\r\n              type: 'router',\r\n              path: '/modelCad',\r\n              query: {\r\n                // baseUrl: baseUrl(),\r\n                cadId: this.fileBim,\r\n                projectId: this.cadRowProjectId,\r\n                steelName: this.cadRowCode,\r\n                showCad: this.IsUploadCad,\r\n                isSubAssembly: false,\r\n                isPart: true\r\n                // token: localStorage.getItem('Token'),\r\n                // auth_id: localStorage.getItem('Last_Working_Object_Id')\r\n              }\r\n            },\r\n            '*'\r\n          )\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.flex-row {\r\n  display: flex;\r\n\r\n  .cs-left {\r\n    background-color: #ffffff;\r\n    margin-right: 20px;\r\n    border-radius: 4px;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n\r\n    .cs-tree-wrapper {\r\n      height: 100%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      overflow: hidden;\r\n      padding: 16px;\r\n\r\n      .tree-search {\r\n        display: flex;\r\n\r\n        .search-select {\r\n          margin-right: 8px;\r\n        }\r\n      }\r\n\r\n      .el-tree {\r\n        flex: 1;\r\n        overflow: auto;\r\n      }\r\n    }\r\n  }\r\n\r\n  .cs-right {\r\n    flex: 1;\r\n    overflow: hidden;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  padding: 0;\r\n  text-align: right;\r\n}\r\n\r\n::v-deep .el-card__body {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.tb-x {\r\n  flex: 1;\r\n  height: 0;\r\n  margin-bottom: 10px;\r\n  overflow: auto;\r\n}\r\n\r\n.topTitle {\r\n  font-size: 14px;\r\n  margin: 0 0 16px;\r\n\r\n  span {\r\n    display: inline-block;\r\n    width: 2px;\r\n    height: 14px;\r\n    background: #009dff;\r\n    vertical-align: middle;\r\n    margin-right: 6px;\r\n  }\r\n}\r\n\r\n::v-deep .elDivder {\r\n  margin: 10px;\r\n}\r\n\r\n.btn-x {\r\n  //margin-bottom: 16px;\r\n\r\n}\r\n\r\n.el-icon-edit {\r\n  cursor: pointer;\r\n}\r\n\r\nfooter {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.cs-bottom {\r\n  position: relative;\r\n  height: 40px;\r\n  line-height: 40px;\r\n\r\n  .data-info {\r\n    position: absolute;\r\n    bottom: 0;\r\n\r\n    .info-x {\r\n      margin-right: 20px;\r\n    }\r\n  }\r\n}\r\n\r\n.demo-form-inline {\r\n  ::v-deep {\r\n    .el-form-item {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.cs-tree-x {\r\n  ::v-deep {\r\n    .el-select {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n.cs-column-row{\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .cs-ell{\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n\r\n  }\r\n}\r\n</style>\r\n"]}]}
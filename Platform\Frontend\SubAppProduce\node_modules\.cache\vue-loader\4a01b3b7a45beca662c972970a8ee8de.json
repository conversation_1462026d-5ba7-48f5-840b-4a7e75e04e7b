{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\index.vue?vue&type=style&index=0&id=a018dede&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\index.vue", "mtime": 1758266768050}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCkBpbXBvcnQgIn5AL3N0eWxlcy9taXhpbi5zY3NzIjsNCi5hcHAtY29udGFpbmVyew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogcm93Ow0KICBoZWlnaHQ6IDEwMCU7DQogIC5jcy16LXBhZ2UtbWFpbi1jb250ZW50IHsNCiAgICBmbGV4OiAxOw0KICB9DQp9DQoNCi5jcy16LXRiLXdyYXBwZXIgew0KICBoZWlnaHQ6IDA7DQogIGZsZXg6IDE7DQp9DQoNCjo6di1kZWVwIHsNCiAgLmNzLXRvcC1oZWFkZXItYm94IHsNCiAgICBsaW5lLWhlaWdodDogMHB4Ow0KICB9DQp9DQoNCi50YiB7DQogIDo6di1kZWVwIHsNCiAgICBAaW5jbHVkZSBzY3JvbGxCYXI7DQoNCiAgICAmOjotd2Via2l0LXNjcm9sbGJhciB7DQogICAgICB3aWR0aDogOHB4Ow0KICAgIH0NCiAgfQ0KfQ0KDQouY3MtZGlhbG9nIHsNCiAgOjp2LWRlZXAgew0KICAgIC5lbC1kaWFsb2dfX2JvZHkgew0KICAgICAgcGFkZGluZzogMjBweCAyMHB4ICFpbXBvcnRhbnQ7DQogICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgIH0NCiAgfQ0KfQ0KDQouY3MtdGItaWNvbiB7DQogIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7DQp9DQoNCi5jcy10YWcgew0KICAmOm50aC1jaGlsZCgybikgew0KICAgIG1hcmdpbi1sZWZ0OiA0cHg7DQogIH0NCg0KICAmOm50aC1jaGlsZChuICsgMykgew0KICAgIG1hcmdpbi10b3A6IDRweDsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2WA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/project-config/process-settings", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <ProjectData @setProjectData=\"setProjectData\" />\r\n    <div class=\"cs-z-page-main-content\">\r\n      <top-header padding=\"0\">\r\n        <template #right>\r\n          <div style=\"display: flex;\">\r\n            <el-form label-width=\"80px\" :inline=\"true\">\r\n              <el-form-item label=\"工序名称\">\r\n                <el-input v-model=\"formInline.name\" clearable placeholder=\"请输入工序名称\" @keyup.enter.native=\"handleSearch\" />\r\n              </el-form-item>\r\n              <el-form-item label=\"代号\">\r\n                <el-input v-model=\"formInline.code\" clearable placeholder=\"请输入代号\" @keyup.enter.native=\"handleSearch\" />\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\r\n                <el-button @click=\"reset\">重置</el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n            <DynamicTableFields\r\n              title=\"表格配置\"\r\n              :table-config-code=\"gridCode\"\r\n              @updateColumn=\"changeColumn\"\r\n            />\r\n          </div>\r\n        </template>\r\n        <template #left>\r\n          <el-button type=\"primary\" @click=\"handleAddProject\">同步项目配置</el-button>\r\n          <el-button type=\"primary\" @click=\"handleAddFactory\">恢复工厂默认配置</el-button>\r\n        </template>\r\n      </top-header>\r\n      <div v-loading=\"tbLoading\" class=\"fff cs-z-tb-wrapper\">\r\n        <vxe-table\r\n          ref=\"xTable\"\r\n          :key=\"tbKey\"\r\n          v-loading=\"tbLoading\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          element-loading-spinner=\"el-icon-loading\"\r\n          element-loading-text=\"拼命加载中\"\r\n          empty-text=\"暂无数据\"\r\n          height=\"100%\"\r\n          :data=\"tbData\"\r\n          stripe\r\n          resizable\r\n          :auto-resize=\"true\"\r\n          class=\"cs-vxe-table\"\r\n          :tooltip-config=\"{ enterable: true }\"\r\n        >\r\n          <vxe-column\r\n            v-for=\"item in columns\"\r\n            :key=\"item.Code\"\r\n            :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n            show-overflow=\"tooltip\"\r\n            sortable\r\n            :align=\"item.Align\"\r\n            :field=\"item.Code\"\r\n            :title=\"item.Display_Name\"\r\n            :visible=\"item.Is_Display\"\r\n            :width=\"item.Width\"\r\n          >\r\n            <template #default=\"{ row }\">\r\n              <span v-if=\"item.Code === 'Workload_Proportion'\">\r\n                {{ row.Workload_Proportion ? row.Workload_Proportion + '%' : \"-\" }}\r\n              </span>\r\n              <span v-else-if=\"item.Code === 'Is_Need_Check'\">\r\n                <el-tag v-if=\"row.Is_Need_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\r\n              </span>\r\n              <span v-else-if=\"item.Code === 'Is_Self_Check'\">\r\n                <el-tag v-if=\"row.Is_Self_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\r\n              </span>\r\n              <span v-else-if=\"item.Code === 'Is_Inter_Check'\">\r\n                <el-tag v-if=\"row.Is_Inter_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\r\n              </span>\r\n              <span v-else-if=\"item.Code === 'Is_Enable'\">\r\n                <el-tag v-if=\"row.Is_Enable\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\r\n              </span>\r\n              <span v-else-if=\"item.Code === 'Is_Nest'\">\r\n                <el-tag v-if=\"row.Is_Nest\" type=\"success\">是</el-tag><el-tag v-else-if=\"row.Is_Nest===false\" type=\"danger\">否</el-tag><span v-else>-</span>\r\n              </span>\r\n              <span v-else-if=\"item.Code === 'Is_Cutting'\">\r\n                <el-tag v-if=\"row.Is_Cutting\" type=\"success\">是</el-tag><el-tag v-else-if=\"row.Is_Cutting===false\" type=\"danger\">否</el-tag><span v-else>-</span>\r\n              </span>\r\n              <span v-else-if=\"item.Code === 'Is_Pick_Material'\">\r\n                <el-tag v-if=\"row.Is_Pick_Material\" type=\"success\">是</el-tag><el-tag v-else-if=\"row.Is_Pick_Material===false\" type=\"danger\">否</el-tag><span v-else>-</span>\r\n              </span>\r\n              <span v-else>{{ row[item.Code] || \"-\" }}</span>\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column fixed=\"right\" title=\"操作\" width=\"100\" show-overflow align=\"center\">\r\n            <template #default=\"{ row }\">\r\n              <el-button type=\"text\" size=\"small\" @click=\"handleDialog('edit', row)\">编辑</el-button>\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n      </div>\r\n      <div style=\"height:50px; line-height: 50px; font-size: 14px; color: #298DFF;\">\r\n        工作量占比合计：{{ totalWorkloadProportion }}%\r\n      </div>\r\n      <el-dialog\r\n        v-if=\"dialogVisible\"\r\n        v-dialog-drag\r\n        class=\"cs-dialog\"\r\n        :close-on-click-modal=\"false\"\r\n        :title=\"title\"\r\n        :visible.sync=\"dialogVisible\"\r\n        custom-class=\"dialogCustomClass\"\r\n        width=\"580px\"\r\n        top=\"10vh\"\r\n        @close=\"handleClose\"\r\n      >\r\n        <component\r\n          :is=\"currentComponent\"\r\n          ref=\"content\"\r\n          :total-workload-proportion=\"totalWorkloadProportion\"\r\n          :row-info=\"rowInfo\"\r\n          :type=\"type\"\r\n          :level=\"level\"\r\n          :bom-list=\"bomList\"\r\n          :sys-project-id=\"sysProjectId\"\r\n          :dialog-visible=\"dialogVisible\"\r\n          @close=\"handleClose\"\r\n          @refresh=\"fetchData\"\r\n        />\r\n      </el-dialog>\r\n      <el-dialog\r\n        v-dialog-drag\r\n        class=\"cs-dialog\"\r\n        title=\"关联设备\"\r\n        :close-on-click-modal=\"false\"\r\n        :visible.sync=\"dialogVisible1\"\r\n        custom-class=\"dialogCustomClass\"\r\n        width=\"86%\"\r\n        top=\"5vh\"\r\n        @close=\"handleClose1\"\r\n      >\r\n        <AssociatedDevice ref=\"Device\" :row-data=\"rowData\" @fetchData=\"fetchData\" />\r\n      </el-dialog>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ProjectData from '../components/ProjectData.vue'\r\nimport TopHeader from '@/components/TopHeader'\r\nimport Add from './component/Add'\r\nimport ZClass from './component/Group'\r\nimport ProjectAdd from './component/ProjectAddDialog.vue'\r\nimport AssociatedDevice from './component/AssociatedDevice'\r\nimport { GetProcessListBase, DeleteProcess, RestoreFactoryProcessFromProject } from '@/api/PRO/technology-lib'\r\nimport ElTableEmpty from '@/components/ElTableEmpty/index.vue'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\n\r\nexport default {\r\n  name: 'PROProcessManagement',\r\n  components: {\r\n    ProjectData,\r\n    ElTableEmpty,\r\n    TopHeader,\r\n    Add,\r\n    ZClass,\r\n    AssociatedDevice,\r\n    DynamicTableFields,\r\n    ProjectAdd\r\n  },\r\n  mixins: [addRouterPage, getTbInfo],\r\n  data() {\r\n    return {\r\n      tbLoading: false,\r\n      level: 0,\r\n      bomList: [],\r\n      addPageArray: [\r\n        {\r\n          path: '/AssociatedDevice',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/process-settings/management/component/AssociatedDevice.vue'),\r\n          name: 'AssociatedDevice',\r\n          meta: { title: '关联设备' }\r\n        }\r\n\r\n      ],\r\n      columns: [],\r\n      tbData: [],\r\n      currentComponent: '',\r\n      title: '',\r\n      comName: '',\r\n      partName: '',\r\n      rowInfo: null,\r\n      rowData: {},\r\n      type: '',\r\n      dialogVisible: false,\r\n      dialogVisible1: false,\r\n      formInline: { name: '', code: '' },\r\n      sysProjectId: '',\r\n      totalWorkloadProportion: 0,\r\n      tbKey: 100,\r\n      gridCode: 'processSettingsList'\r\n    }\r\n  },\r\n  async created() {\r\n    await this.getTableInfo()\r\n  },\r\n  mounted() {\r\n    this.getBOMInfo()\r\n  },\r\n  methods: {\r\n    async getTableInfo() {\r\n      await this.getTableConfig(this.gridCode)\r\n    },\r\n    async getBOMInfo() {\r\n      const { comName, partName, list } = await GetBOMInfo()\r\n      this.comName = comName\r\n      this.partName = partName\r\n      this.bomList = list\r\n    },\r\n    handleOpenDevice(row) {\r\n      this.rowData = row\r\n      this.dialogVisible1 = true\r\n      this.$nextTick(() => {\r\n        this.$refs.Device.clearSelec()\r\n      })\r\n      //  this.$router.push({ path: 'http://localhost:3000/produce/pro/nesting/index' })\r\n    },\r\n\r\n    fetchData() {\r\n      this.tbLoading = true\r\n      GetProcessListBase({ ...this.formInline, sysProjectId: this.sysProjectId }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data\r\n\r\n          // 计算所有 Workload_Proportion 的总和\r\n          this.totalWorkloadProportion = parseFloat(res.Data.reduce((total, item) => {\r\n            const proportion = parseFloat(item.Workload_Proportion) || 0\r\n            return total + proportion\r\n          }, 0).toFixed(2))\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.tbLoading = false\r\n        this.dialogVisible1 = false\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    handleClose1() {\r\n      this.dialogVisible1 = false\r\n    },\r\n    handleAddProject() {\r\n      this.dialogVisible = true\r\n      this.currentComponent = 'ProjectAdd'\r\n      this.title = '同步项目配置'\r\n    },\r\n    handleDialog(type, row) {\r\n      this.currentComponent = 'Add'\r\n      this.type = type\r\n      if (type === 'add') {\r\n        this.title = '新建'\r\n      } else {\r\n        this.title = '编辑'\r\n        this.rowInfo = row\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    handleManage(row) {\r\n      this.currentComponent = 'ZClass'\r\n      this.title = '班组管理'\r\n      this.dialogVisible = true\r\n      this.$nextTick((_) => {\r\n        this.$refs.content.init(row)\r\n      })\r\n    },\r\n    // 从工厂级同步\r\n    handleAddFactory() {\r\n      this.$confirm('此操作将会恢复到工厂的工序配置, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.restoreFactoryProcessFromProject()\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消恢复'\r\n        })\r\n      })\r\n    },\r\n    restoreFactoryProcessFromProject() {\r\n      RestoreFactoryProcessFromProject({\r\n        Sys_Project_Id: this.sysProjectId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '恢复成功',\r\n            type: 'success'\r\n          })\r\n          this.fetchData()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleDelete(processId) {\r\n      this.$confirm('是否删除当前工序?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          DeleteProcess({\r\n            processId\r\n          }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                message: '删除成功',\r\n                type: 'success'\r\n              })\r\n              this.fetchData()\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n    async changeColumn() {\r\n      await this.getTableConfig(this.gridCode)\r\n      this.tbKey++\r\n    },\r\n    handleSearch() {\r\n      this.fetchData()\r\n    },\r\n    reset() {\r\n      this.formInline.name = ''\r\n      this.formInline.code = ''\r\n      this.fetchData()\r\n    },\r\n    setProjectData(data) {\r\n      this.sysProjectId = data.Sys_Project_Id\r\n      this.fetchData()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/styles/mixin.scss\";\r\n.app-container{\r\n  display: flex;\r\n  flex-direction: row;\r\n  height: 100%;\r\n  .cs-z-page-main-content {\r\n    flex: 1;\r\n  }\r\n}\r\n\r\n.cs-z-tb-wrapper {\r\n  height: 0;\r\n  flex: 1;\r\n}\r\n\r\n::v-deep {\r\n  .cs-top-header-box {\r\n    line-height: 0px;\r\n  }\r\n}\r\n\r\n.tb {\r\n  ::v-deep {\r\n    @include scrollBar;\r\n\r\n    &::-webkit-scrollbar {\r\n      width: 8px;\r\n    }\r\n  }\r\n}\r\n\r\n.cs-dialog {\r\n  ::v-deep {\r\n    .el-dialog__body {\r\n      padding: 20px 20px !important;\r\n      overflow: hidden;\r\n    }\r\n  }\r\n}\r\n\r\n.cs-tb-icon {\r\n  vertical-align: middle;\r\n}\r\n\r\n.cs-tag {\r\n  &:nth-child(2n) {\r\n    margin-left: 4px;\r\n  }\r\n\r\n  &:nth-child(n + 3) {\r\n    margin-top: 4px;\r\n  }\r\n}\r\n</style>\r\n"]}]}
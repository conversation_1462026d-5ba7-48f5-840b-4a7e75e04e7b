{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\actually-sent\\v4\\component\\Add.vue?vue&type=style&index=0&id=27ec9428&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\actually-sent\\v4\\component\\Add.vue", "mtime": 1757468128055}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoudGItc3RhdHVzIHsNCiAgYmFja2dyb3VuZDogI2ZhZTZiYjsNCiAgcGFkZGluZzogMTZweCAyMHB4Ow0KICBmb250LXNpemU6IDEuMmVtOw0KICBmb250LXdlaWdodDogYm9sZDsNCiAgZGlzcGxheTogZmxleDsNCg0KICAqIHsNCiAgICBtYXJnaW4tcmlnaHQ6IDEycHg7DQogIH0NCn0NCg0KLmVsLWZvcm0gew0KICBtYXJnaW46IDE2cHggMTBweDsNCn0NCg0KLnRpdGxlIHsNCiAgbWFyZ2luLWxlZnQ6IDEwcHg7DQp9DQoNCi5jcy1yZWR7DQogIGNvbG9yOnJlZA0KfQ0KDQouY3MtdHJlZS14IHsNCiAgOjp2LWRlZXAgew0KICAgIC5lbC1zZWxlY3Qgew0KICAgICAgd2lkdGg6IDEwMCU7DQogICAgfQ0KICB9DQp9DQoNCi5zdGF0aXN0aWNzLWNvbnRhaW5lciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIC5zdGF0aXN0aWNzLWl0ZW0gew0KICAgIG1hcmdpbi1yaWdodDogMzJweDsNCiAgICBzcGFuOmZpcnN0LWNoaWxkIHsNCiAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgIGxpbmUtaGVpZ2h0OiAxOHB4Ow0KICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgIGNvbG9yOiAjOTk5OTk5Ow0KICAgICAgLy8gbWFyZ2luLXJpZ2h0OiAxNnB4Ow0KICAgIH0NCiAgICBzcGFuOmxhc3QtY2hpbGQgew0KICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICAgIGNvbG9yOiAjMDBjMzYxOw0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["Add.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6jDA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Add.vue", "sourceRoot": "src/views/PRO/shipment/actually-sent/v4/component", "sourcesContent": ["<template>\r\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\r\n    <div class=\"cs-z-page-main-content\">\r\n      <top-header>\r\n        <template #left>\r\n          <div class=\"cs-header\">\r\n            <!-- <el-button\r\n              circle\r\n              icon=\"el-icon-arrow-left\"\r\n              size=\"mini\"\r\n              @click=\"toBack\"\r\n            /> -->\r\n            <el-button @click=\"toBack\">返回</el-button>\r\n            <!-- <strong class=\"title\">{{\r\n              isEdit === true ? \"编辑发货单\" : \"新增发货单\"\r\n            }}</strong> -->\r\n          </div>\r\n        </template>\r\n        <template #right>\r\n          <!-- <el-button type=\"primary\" :loading=\"btnLoading\">打印发货单</el-button> -->\r\n          <el-button type=\"primary\" :loading=\"loading\" @click=\"handleSubmit\">保存</el-button>\r\n        </template>\r\n      </top-header>\r\n      <!-- <title-info :title=\"projectName\" /> -->\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"发货单号\" prop=\"receiveNum\">\r\n              <el-input\r\n                v-model=\"form.receiveNum\"\r\n                :disabled=\"autoGenerate || (!isDraft && isEdit)\"\r\n                :placeholder=\"autoGenerate ? '自动生成':'请输入'\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"项目名称\" prop=\"projectName\">\r\n              <el-input v-model=\"form.projectName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"收货人\" prop=\"receiveName\">\r\n              <el-input\r\n                v-model=\"form.receiveName\"\r\n                clearable=\"\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"收货人电话\" prop=\"Receiver_Tel\">\r\n              <el-input\r\n                v-model=\"form.Receiver_Tel\"\r\n                clearable=\"\"\r\n                placeholder=\"请输入\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"车辆信息\" prop=\"License\">\r\n              <el-select\r\n                v-model=\"form.License\"\r\n                clearable\r\n                :disabled=\"((pageStatus>=2)||pageStatus===1)&&isEdit\"\r\n                placeholder=\"请选择\"\r\n                style=\"width: 70%\"\r\n                filterable\r\n                @change=\"carChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in carOptions\"\r\n                  :key=\"item.License\"\r\n                  :label=\"item.detail\"\r\n                  :value=\"item.License\"\r\n                />\r\n              </el-select>\r\n              <el-button\r\n                style=\"margin-left: 10px\"\r\n                type=\"text\"\r\n                @click=\"handleEditCar\"\r\n              >新增车辆</el-button>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"发货部门\" prop=\"Depart_Id\">\r\n              <el-select\r\n                v-if=\"isProductweight===true\"\r\n                v-model=\"form.Depart_Id\"\r\n                clearable\r\n                placeholder=\"请选择\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in pickDepartmentList\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n              <el-tree-select\r\n                v-else\r\n                ref=\"treeSelectDepart\"\r\n                v-model=\"form.Depart_Id\"\r\n                :select-params=\"{\r\n                  clearable: true,\r\n                }\"\r\n                class=\"cs-tree-x\"\r\n                :tree-params=\"treeParamsDepart\"\r\n                @select-clear=\"departClear\"\r\n                @node-click=\"departChange\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"发货人\" prop=\"issueName\">\r\n              <el-input v-model=\"form.issueName\" clearable=\"\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"发货时间\" prop=\"Out_Date\">\r\n              <el-date-picker\r\n                v-model=\"form.Out_Date\"\r\n                style=\"width: 100%\"\r\n                type=\"datetime\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"装车班\" prop=\"Loadings\">\r\n              <SelectDepartment v-model=\"form.Loadings\" @change=\"form.LoadingsPersonnel = ''\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"装车班人员\" prop=\"LoadingsPersonnel \">\r\n              <SelectDepartmentUser v-model=\"form.LoadingsPersonnel\" :department-id=\"form.Loadings\" :disabled=\"!form.Loadings\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"车次\" prop=\"Trips \">\r\n              <el-input v-model=\"form.Trips\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"专业\" prop=\"ProfessionalTypeName\">\r\n              <el-input v-model=\"form.ProfessionalTypeName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"收货地址\" prop=\"Address\">\r\n              <el-input v-model=\"form.Address\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"收货单位\" prop=\"ReceivingUnit\">\r\n              <el-input v-model=\"form.ReceivingUnit\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"物流费\" prop=\"Logistics_Fee\">\r\n              <el-input-number\r\n                v-model=\"form.Logistics_Fee\"\r\n                :min=\"0\"\r\n                :precision=\"2\"\r\n                style=\"width: 100%\"\r\n                class=\"cs-number-btn-hidden\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"关联合同\" prop=\"Contract_Id\">\r\n              <el-select\r\n                v-model=\"form.Contract_Id\"\r\n                clearable\r\n                filterable\r\n                placeholder=\"请选择\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in contractOptions\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.ContractName\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"备注\">\r\n              <el-input\r\n                v-model=\"form.Remarks\"\r\n                :autosize=\"{ minRows: 2, maxRows: 2 }\"\r\n                :maxlength=\"1000\"\r\n                show-word-limit\r\n                type=\"textarea\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"附件\" class=\"factory-img\">\r\n              <OSSUpload\r\n                class=\"upload-demo\"\r\n                action=\"alioss\"\r\n                :limit=\"10\"\r\n                :multiple=\"true\"\r\n                :on-success=\"\r\n                  (response, file, fileList) => {\r\n                    uploadSuccess(response, file, fileList)\r\n                  }\r\n                \"\r\n                :on-remove=\"uploadRemove\"\r\n                :on-preview=\"handlePreview\"\r\n                :on-exceed=\"handleExceed\"\r\n                :file-list=\"fileListData\"\r\n                :show-file-list=\"true\"\r\n                :disabled=\"false\"\r\n              >\r\n                <el-button\r\n                  type=\"primary\"\r\n                  :disabled=\"false\"\r\n                >上传文件</el-button>\r\n              </OSSUpload>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n\r\n      <div v-if=\"isEdit\">\r\n        <h4> 过磅信息</h4>\r\n        <el-form ref=\"form\" inline :model=\"weightform\" label-width=\"80px\">\r\n          <el-form-item label=\"皮重\">\r\n            {{ weightform.Tare_Weight }}kg\r\n          </el-form-item>\r\n          <el-form-item label=\"理重\">\r\n            {{ weightform.Reason_Weight }}kg\r\n          </el-form-item>\r\n          <el-form-item label=\"磅重\">\r\n            {{ weightform.Pound_Weight }}kg\r\n          </el-form-item>\r\n          <el-form-item label=\"净重\" prop=\"region\">\r\n            <span :class=\"{'cs-red':showRed}\">{{ netWeight }}\r\n              <span v-if=\"showRed\">（{{ getNum(netWeight,weightform.Reason_Weight)>0 ?'高于':'低于' }}理重{{ Math.abs(+getNum(netWeight, weightform.Reason_Weight)) }}kg）</span>\r\n            </span>\r\n          </el-form-item>\r\n          <el-form-item label=\"过磅备注\" prop=\"region\">\r\n            {{ plm_ProjectSendingInfo.Pound_Remark }}\r\n          </el-form-item>\r\n          <el-form-item label=\"附件\">\r\n            <template v-for=\"(item,idx) in weightFileInfo\">\r\n              <el-link\r\n                :key=\"idx\"\r\n                :href=\"item.url\"\r\n                target=\"_blank\"\r\n              >{{ item.name }}</el-link>\r\n              <el-divider v-if=\"idx!==weightFileInfo.length -1\" :key=\"idx\" direction=\"vertical\" />\r\n            </template>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <top-header style=\"margin-bottom: 10px\">\r\n        <template #left>\r\n          <div class=\"cs-header\" style=\"margin-bottom: 20px\">\r\n            <el-radio-group v-model=\"radio\" size=\"small\" @change=\"radioChange\">\r\n              <el-radio-button\r\n                label=\"pro_component_out_detail_list\"\r\n              >构件</el-radio-button>\r\n              <el-radio-button\r\n                label=\"pro_package_out_detail_list\"\r\n              >打包件</el-radio-button>\r\n              <el-radio-button\r\n                label=\"PROShipUnitPart\"\r\n              >部件</el-radio-button>\r\n              <el-radio-button\r\n                label=\"PROShipPart\"\r\n              >零件</el-radio-button>\r\n            </el-radio-group>\r\n          </div>\r\n        </template>\r\n        <template #right>\r\n          <div v-if=\"sendNumber\" class=\"statistics-container\">\r\n            <div class=\"statistics-item\" style=\"margin-right: 0\">\r\n              <span>发货序号：</span>\r\n              <span>{{ sendNumber }}</span>\r\n            </div>\r\n          </div>\r\n          <el-button\r\n            v-if=\"!isSub\"\r\n            :disabled=\"!selectList.length\"\r\n            size=\"mini\"\r\n            type=\"danger\"\r\n            @click=\"handleDelete\"\r\n          >删除</el-button>\r\n          <el-button\r\n            v-if=\"!isSub\"\r\n            size=\"mini\"\r\n            type=\"primary\"\r\n            @click=\"handleAdd\"\r\n          >添加</el-button>\r\n        </template>\r\n      </top-header>\r\n\r\n      <div v-loading=\"tbLoading\" class=\"fff cs-z-tb-wrapper\">\r\n        <dynamic-data-table\r\n          ref=\"dyTable\"\r\n          class=\"cs-plm-dy-table\"\r\n          :columns=\"columns\"\r\n          :config=\"tbConfig\"\r\n          :data=\"tabTypeCode === 2 ? tbData2 : tabTypeCode === 1 ? tbData :tabTypeCode === 3 ? tbData3 :tbData4\"\r\n          :page=\"form.PageInfo.Page\"\r\n          :sum-values=\"sums\"\r\n          :select-width=\"70\"\r\n          :total=\"total\"\r\n          border\r\n          stripe\r\n          @checkSelectable=\"checkSelectable\"\r\n          @multiSelectedChange=\"multiSelectedChange\"\r\n        >\r\n          <template slot=\"Code\" slot-scope=\"{ row }\">\r\n            <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n            <span>{{ row.Code }}</span>\r\n          </template>\r\n          <template slot=\"Part_Code\" slot-scope=\"{ row }\">\r\n            <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n            <span>{{ row.Part_Code }}</span>\r\n          </template>\r\n          <template slot=\"S_Count\" slot-scope=\"{ row }\">\r\n            <div v-if=\"!Is_Pack && !isSub\">\r\n              <el-input\r\n                v-model=\"row.S_Count\"\r\n                type=\"text\"\r\n                :readonly=\"row.Wait_Stock_Count == 1\"\r\n                style=\"width: 50px; border: 1px solid #eee; border-radius: 4px\"\r\n                @blur=\"\r\n                  (e) => {\r\n                    inputBlur(e, row.S_Count, row);\r\n                  }\r\n                \"\r\n              />\r\n            </div>\r\n            <div v-else>{{ row.S_Count }}</div>\r\n          </template>\r\n          <template slot=\"PackageSn\" slot-scope=\"{ row }\">\r\n            <div style=\"color: #298dff; cursor: pointer;\" @click=\"handleDetail(row)\">{{ row.PackageSn }}</div>\r\n          </template>\r\n          <!--          <template slot=\"AllWeight\" slot-scope=\"{ row }\">-->\r\n          <!--            {{ row.S_Count * row.Netweight }}-->\r\n          <!--          </template>-->\r\n          <!--  <template slot=\"Unique_Code\" slot-scope=\"{ row }\">\r\n            {{ row.C_Type === \"打包件\" ? row.Unique_Code : \"-\" }}\r\n          </template>\r\n          <template slot=\"op\" slot-scope=\"{ row, index }\">\r\n            <el-button\r\n              v-if=\"row.C_Type === '打包件'\"\r\n              :index=\"index\"\r\n              type=\"text\"\r\n              @click=\"handleInfo(row)\"\r\n              >查看</el-button\r\n            >\r\n          </template> -->\r\n        </dynamic-data-table>\r\n      </div>\r\n\r\n      <el-dialog\r\n        v-if=\"dialogVisible\"\r\n        v-dialogDrag\r\n        class=\"plm-custom-dialog\"\r\n        :title=\"title\"\r\n        :visible.sync=\"dialogVisible\"\r\n        :width=\"width\"\r\n        :top=\"topDialog\"\r\n        @close=\"close\"\r\n      >\r\n        <component\r\n          :is=\"currentComponent\"\r\n          ref=\"content\"\r\n          :dialog-visible=\"dialogVisible\"\r\n          :project-id=\"projectId\"\r\n          :sys-project-id=\"form.ProjectId\"\r\n          :add-radio=\"addradio\"\r\n          :tab-type-code=\"tabTypeCode\"\r\n          :is-pack=\"Is_Pack\"\r\n          @addCarData=\"addCarData\"\r\n          @close=\"close\"\r\n          @reCount=\"getTotal\"\r\n          @refresh=\"fetchData\"\r\n          @selectList=\"addSelectList\"\r\n        />\r\n      </el-dialog>\r\n      <check-info ref=\"info\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport TitleInfo from './TitleInfo.vue'\r\nimport AddDialog from './AddDialog.vue'\r\nimport packDetail from './packDetail.vue'\r\nimport {\r\n  AddProjectSendingInfo,\r\n  GetProjectsendinginEntity,\r\n  EditProjectSendingInfo\r\n} from '@/api/PRO/component-stock-out'\r\nimport { GetFirstLevelDepartsUnderFactory } from '@/api/PRO/material-warehouse/material-inventory-reconfig.js'\r\nimport { GetPreferenceSettingValue } from '@/api/sys/system-setting'\r\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable.vue'\r\nimport CarDialog from './CarDialog.vue'\r\nimport { GetCurCarPageList } from '@/api/PRO/car'\r\nimport TopHeader from '@/components/TopHeader/index.vue'\r\nimport CheckInfo from '@/views/PRO/Component/GetPackingDetail/index.vue'\r\nimport { closeTagView, parseTime } from '@/utils'\r\nimport { GetProjectPageList, GetProjectEntity } from '@/api/PRO/pro-schedules'\r\nimport { GetGridByCode, GetOssUrl } from '@/api/sys'\r\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\r\nimport { GetCompanyDepartTree } from '@/api/sys'\r\nimport numeral from 'numeral'\r\nimport OSSUpload from '@/views/PRO/components/ossupload.vue'\r\nimport { getFileNameFromUrl } from '@/utils/file'\r\nimport { mapGetters } from 'vuex'\r\nimport { GetContractList } from '@/api/plm/production'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nimport SelectDepartmentUser from '@/components/Select/SelectDepartmentUser/index.vue'\r\nimport SelectDepartment from '@/components/Select/SelectDepartment/index.vue'\r\n\r\nconst TAB_TYPE = {\r\n  com: 1,\r\n  package: 2,\r\n  unitPart: 3,\r\n  part: 4\r\n}\r\nexport default {\r\n  components: {\r\n    SelectDepartment, SelectDepartmentUser,\r\n    OSSUpload,\r\n    TitleInfo,\r\n    AddDialog,\r\n    TopHeader,\r\n    DynamicDataTable,\r\n    CarDialog,\r\n    CheckInfo,\r\n    packDetail\r\n  },\r\n  props: {\r\n    isEdit: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      pageStatus: undefined,\r\n      radio: 'pro_component_out_detail_list',\r\n      addradio: 'pro_waiting_out_list',\r\n      Is_Pack: false,\r\n      isClicked: false,\r\n      isSub: false, // 是否提交过\r\n      width: '40%',\r\n      topDialog: '1vh',\r\n      btnLoading: false,\r\n      currentComponent: '',\r\n      title: '',\r\n      loading: false,\r\n      dialogVisible: false,\r\n      sendNumber: '',\r\n      form: {\r\n        ProjectId: '',\r\n        Out_Date: new Date(),\r\n        Remarks: '',\r\n        Contact_UserName: '',\r\n        Mobile: '',\r\n        License: '',\r\n        Address: '',\r\n        receiveName: '',\r\n        issueName: '',\r\n        Receiver_Tel: '',\r\n        Area_Id: '',\r\n        projectName: '',\r\n        receiveNum: '',\r\n        ProfessionalTypeName: '',\r\n        Depart_Id: '',\r\n        Logistics_Fee: undefined,\r\n        Contract_Id: '',\r\n        PageInfo: {\r\n          ParameterJson: [],\r\n          Page: 1,\r\n          PageSize: 20\r\n        },\r\n        Loadings: '',\r\n        LoadingsPersonnel: '',\r\n        Trips: '',\r\n        ReceivingUnit: ''\r\n      },\r\n      // 发货部门\r\n      treeParamsDepart: {\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          disabled: 'disabled',\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      pickDepartmentList: [], // 品重发货部门\r\n      plm_ProjectSendingInfo: {},\r\n      produced_Components: [],\r\n      weightFileInfo: [],\r\n      projectSendingInfo_Item: [],\r\n      Itemdetail: [],\r\n      PackagesList: [],\r\n      ProfessionalType: [],\r\n      fileListArr: [],\r\n      carOptions: [],\r\n      projects: '',\r\n      Id: '',\r\n      projectId: '',\r\n      planTime: '',\r\n      showDialog: false,\r\n      tbConfig: {\r\n        Pager_Align: 'center'\r\n      },\r\n      columns: [],\r\n      tbData: [],\r\n      tbData2: [],\r\n      tbData3: [],\r\n      tbData4: [],\r\n      total: 0,\r\n      tbLoading: false,\r\n      autoGenerate: true,\r\n      selectList: [],\r\n      fileListData: [],\r\n      sums: [],\r\n      rules: {\r\n        Out_Date: [\r\n          { required: true, message: '请选择发货时间', trigger: 'change' }\r\n        ],\r\n        receiveNum: [{ required: false, message: '请输入发货单号', trigger: 'blur' }],\r\n        // receiveName: [{ required: true, message: '请输入', trigger: 'blur' }],\r\n        // Receiver_Tel: [{ required: true, message: '请输入', trigger: 'blur' }],\r\n        Depart_Id: [{ required: true, message: '请选择', trigger: 'change' }],\r\n        issueName: [{ required: true, message: '请输入', trigger: 'blur' }]\r\n      },\r\n      old_Component_Ids: [],\r\n      weightform: {\r\n        Tare_Weight: 0, // 皮重\r\n        Reason_Weight: 0, // 理重\r\n        Pound_Weight: 0, // 磅重\r\n        Net_Weight: 0, // 净重\r\n        Weigh_Warning_Threshold: 0\r\n      },\r\n      isProductweight: null,\r\n      tabTypeCode: 1,\r\n      isDraft: false,\r\n      contractOptions: []\r\n    }\r\n  },\r\n  provide() {\r\n    return {\r\n      isVersionFour: this.isVersionFour\r\n    }\r\n  },\r\n  computed: {\r\n    netWeight() {\r\n      if (!this.weightform.Pound_Weight || !this.weightform.Tare_Weight) return 0\r\n      return this.weightform.Pound_Weight - this.weightform.Tare_Weight\r\n    },\r\n    showRed({ netWeight }) {\r\n      return Math.abs(netWeight - this.weightform.Reason_Weight) >= this.weightform.Weigh_Warning_Threshold\r\n    },\r\n    ...mapGetters('tenant', ['isVersionFour'])\r\n  },\r\n  watch: {\r\n    'tbData.length': {\r\n      handler() {\r\n        this.getTotal()\r\n      }\r\n    }\r\n\r\n  },\r\n  async created() {\r\n    this.tabTypeCode = TAB_TYPE.com\r\n    this.isSub = this.$route.query.isSub === '1' || false\r\n    await this.getSettingProductweight()\r\n    this.getFactoryDepartmentData()\r\n    this.getDepartmentTree()\r\n    this.getFactoryTypeOption()\r\n    this.getAllCarList()\r\n  },\r\n  async mounted() {\r\n    if (this.isEdit) {\r\n      const {\r\n        autoGenerate\r\n      } = JSON.parse(decodeURIComponent(this.$route.query.p))\r\n      this.autoGenerate = autoGenerate\r\n      await this.getInfo()\r\n    } else {\r\n      const {\r\n        Name,\r\n        Id,\r\n        Code,\r\n        Address,\r\n        autoGenerate,\r\n        Sys_Project_Id\r\n      } = JSON.parse(decodeURIComponent(this.$route.query.p))\r\n      // this.projectName = Name;\r\n      this.autoGenerate = autoGenerate\r\n      this.projectId = Id\r\n      this.Project_Code = Code\r\n      this.form.projectName = Name\r\n      this.form.Address = Address\r\n      this.form.ProjectId = Sys_Project_Id\r\n      this.form.issueName = this.$store.state.user.name\r\n      this.getProjectEntity(this.projectId)\r\n      this.rules.receiveNum[0].required = !autoGenerate\r\n    }\r\n    this.getContractList()\r\n  },\r\n  methods: {\r\n    async getSettingProductweight() {\r\n      const res = await GetPreferenceSettingValue({ code: 'Productweight' })\r\n      if (res.Data === 'true') {\r\n        this.isProductweight = true\r\n      }\r\n    },\r\n\r\n    // 获取工厂数据\r\n    getFactoryDepartmentData() {\r\n      GetFirstLevelDepartsUnderFactory({ FactoryId: localStorage.getItem('CurReferenceId') }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.pickDepartmentList = res.Data\r\n          const depId = localStorage.getItem('DepartmentId')\r\n          const cur = this.pickDepartmentList.find((item) => item.Id === depId)\r\n          if (cur) {\r\n            this.form.Depart_Id = cur.Id\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    getDepartmentTree() {\r\n      GetCompanyDepartTree({ isAll: true }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const tree = res.Data\r\n          this.setDisabledTree(tree)\r\n          this.treeParamsDepart.data = tree\r\n          this.$nextTick(_ => {\r\n            this.$refs.treeSelectDepart?.treeDataUpdateFun(tree)\r\n            const arr = this.getFlattenedSelectableItems(tree)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getFlattenedSelectableItems(root) {\r\n      if (!root || !root.length) return []\r\n      const result = []\r\n      const flatten = (items) => {\r\n        if (!items || !items.length) return\r\n        items.forEach((item) => {\r\n          const { Children } = item\r\n          if (item.Data?.Is_Company !== true && item.Data?.Type !== '1') {\r\n            result.push(item)\r\n          }\r\n          if (Children && Children.length > 0) {\r\n            flatten(Children)\r\n          }\r\n        })\r\n      }\r\n      flatten(root)\r\n      const cur = result.find((item) => item.Id === localStorage.getItem('DepartmentId'))\r\n      if (cur && !this.isEdit) {\r\n        this.form.Depart_Id = cur.Id\r\n      }\r\n    },\r\n    setDisabledTree(root) {\r\n      if (!root) return\r\n      root.forEach((element) => {\r\n        const { Children } = element\r\n        if (element.Data.Is_Company === true || element.Data.Type === '1') {\r\n          element.disabled = true\r\n        } else {\r\n          element.disabled = false\r\n        }\r\n        if (Children.length > 0) {\r\n          this.setDisabledTree(Children)\r\n        }\r\n        // if (Children && Children.length) {\r\n        //   element.disabled = true\r\n        // } else {\r\n        //   element.disabled = false\r\n        //   this.setDisabledTree(Children)\r\n        // }\r\n      })\r\n    },\r\n    departClear() {\r\n\r\n    },\r\n    departChange() {\r\n\r\n    },\r\n    getNum(a, b) {\r\n      return numeral(a).subtract(b).format('0.[000]')\r\n    },\r\n    async getFactoryTypeOption() {\r\n      await GetFactoryProfessionalByCode({\r\n        factoryId: localStorage.getItem('CurReferenceId')\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.ProfessionalType = res.Data\r\n          this.form.ProfessionalTypeName = this.ProfessionalType[0].Name\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      await this.getTableConfig(\r\n        `pro_component_out_detail_list,${this.ProfessionalType[0].Code}`\r\n      )\r\n    },\r\n    getProjectEntity(Id) {\r\n      GetProjectEntity({ Id }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const Consignee = res.Data.Contacts.find((item) => {\r\n            return item.Type == 'Consignee'\r\n          })\r\n          this.form.receiveName = Consignee?.Name || ''\r\n          this.form.Receiver_Tel = Consignee?.Tel || ''\r\n          this.form.Trips = res.Data.Trips || ''\r\n        }\r\n      })\r\n    },\r\n    getProjectPageList() {\r\n      GetProjectPageList({ PageSize: -1 }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.projects = res.Data.Data\r\n        }\r\n      })\r\n    },\r\n    toBack() {\r\n      this.$confirm('此操作不会保存编辑数据，是否退出？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          closeTagView(this.$store, this.$route)\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n    },\r\n    radioChange(e) {\r\n      if (e === 'pro_component_out_detail_list') {\r\n        this.addradio = 'pro_waiting_out_list'\r\n        this.Is_Pack = false\r\n        this.tabTypeCode = TAB_TYPE.com\r\n        this.getTableConfig(`${e},${this.ProfessionalType[0].Code}`)\r\n      } else if (e === 'pro_package_out_detail_list') {\r\n        this.addradio = 'pro_waiting_out_list_package'\r\n        this.Is_Pack = true\r\n        this.tabTypeCode = TAB_TYPE.package\r\n        this.getTableConfig(`${e},${this.ProfessionalType[0].Code}`)\r\n      } else if (e === 'PROShipUnitPart') {\r\n        this.addradio = 'PROShipAddUnitPart'\r\n        this.tabTypeCode = TAB_TYPE.unitPart\r\n        this.getTableConfig(`${e}`)\r\n        this.Is_Pack = false\r\n      } else if (e === 'PROShipPart') {\r\n        this.addradio = 'PROShipAddPart'\r\n        this.Is_Pack = false\r\n        this.tabTypeCode = TAB_TYPE.part\r\n        this.getTableConfig(`${e}`)\r\n      }\r\n    },\r\n    inputBlur(e, e1, row) {\r\n      console.log('blur', e1, row, row.Wait_Stock_Count)\r\n      if (e1 < 1 || e1 > row.Wait_Stock_Count) {\r\n        row.S_Count = row.Wait_Stock_Count\r\n      } else {\r\n        row.S_Count = Number(e1)\r\n      }\r\n      row.AllWeight = this.getAllWeight(row)\r\n      this.Itemdetail.find((item) => {\r\n        if (item.Component_Id == row.Id) {\r\n          item.SteelAmount = row.S_Count\r\n        }\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (!valid) {\r\n          return\r\n        }\r\n        this.isClicked = true\r\n\r\n        const formAttachment = []\r\n        if (this.fileListArr.length > 0) {\r\n          this.fileListArr.forEach(item => {\r\n            formAttachment.push(\r\n              item.response && item.response.encryptionUrl\r\n                ? item.response.encryptionUrl\r\n                : item.encryptionUrl\r\n            )\r\n          })\r\n        }\r\n\r\n        const submitObj = {\r\n          plm_ProjectSendingInfo: {\r\n            // Id:this.Id,\r\n            Code: this.form.receiveNum,\r\n            Attachment: formAttachment.toString(),\r\n            ProjectId: this.projectId,\r\n            Consignee: this.form.receiveName,\r\n            ConsigneeTel: this.form.Receiver_Tel,\r\n            Depart_Id: this.form.Depart_Id,\r\n            MakerName: this.form.issueName,\r\n            VehicleNo: this.form.License,\r\n            DriverName: this.form.Contact_UserName,\r\n            Telephone: this.form.Mobile,\r\n            SendDate: parseTime(this.form.Out_Date, '{y}-{m}-{d} {h}:{i}:{s}'),\r\n            Remarks: this.form.Remarks,\r\n            ProjectName: this.form.projectName,\r\n            TypeId: this.ProfessionalType[0].Code,\r\n            Address: this.form.Address,\r\n            Logistics_Fee: this.form.Logistics_Fee,\r\n            Contract_Id: this.form.Contract_Id,\r\n            Loadings: this.form.Loadings,\r\n            LoadingsPersonnel: this.form.LoadingsPersonnel,\r\n            Trips: this.form.Trips,\r\n            ReceivingUnit: this.form.ReceivingUnit,\r\n          },\r\n          projectSendingInfo_Item: [],\r\n          PartList: []\r\n        }\r\n        // 只获取新的表格数据\r\n        const tempData = [...this.tbData, ...this.tbData2, ...this.tbData3, ...this.tbData4]\r\n        tempData.filter((item) => {\r\n          if (item.PackageSn) {\r\n            const {\r\n              Id,\r\n              Stock_Count,\r\n              PackageSn,\r\n              Warehouse_Id,\r\n              Location_Id,\r\n              Netweight,\r\n              AllWeight,\r\n              DIM,\r\n              Volume,\r\n              AllAmount,\r\n              Import_Detail_Id,\r\n              Model_Ids,\r\n              isOld\r\n            } = item\r\n            const tempItem = {\r\n              PackageSn: PackageSn || '',\r\n              SteelAmount: Stock_Count || 1,\r\n              Warehouse_Id: Warehouse_Id || '',\r\n              Location_Id: Location_Id || '',\r\n              SteelWeight: Netweight || '',\r\n              Import_Detail_Id,\r\n              Model_Ids,\r\n              DIM,\r\n              Volume,\r\n              AllWeight,\r\n              AllAmount,\r\n              isOld\r\n            }\r\n            if (!isOld) {\r\n              submitObj.projectSendingInfo_Item.push(tempItem)\r\n            }\r\n          } else if (item.Code) {\r\n            const {\r\n              Id,\r\n              S_Count,\r\n              Stock_Count,\r\n              Warehouse_Id,\r\n              Import_Detail_Id,\r\n              Model_Ids,\r\n              Location_Id,\r\n              Netweight,\r\n              AllWeight,\r\n              isOld\r\n            } = item\r\n            const tempItem = {\r\n              Component_Id: Id || '',\r\n              SteelAmount: S_Count || '',\r\n              Warehouse_Id: Warehouse_Id || '',\r\n              Location_Id: Location_Id || '',\r\n              SteelWeight: Netweight || '',\r\n              Import_Detail_Id,\r\n              Model_Ids,\r\n              isOld,\r\n              AllWeight\r\n            }\r\n            if (!isOld) {\r\n              delete tempItem.isOld\r\n              submitObj.projectSendingInfo_Item.push(tempItem)\r\n            }\r\n          } else if (item.Part_Code) {\r\n            const {\r\n              Part_Produced_Id,\r\n              Part_Code,\r\n              Area_Name,\r\n              InstallUnit_Name,\r\n              S_Count,\r\n              Spec,\r\n              Length,\r\n              Weight,\r\n              Part_Grade\r\n            } = item\r\n            const tempItem = {\r\n              Part_Produced_Id: Part_Produced_Id,\r\n              Part_Code,\r\n              Area_Name,\r\n              InstallUnit_Name,\r\n              Amount: S_Count,\r\n              Spec,\r\n              Length,\r\n              Weight,\r\n              Part_Grade\r\n            }\r\n            submitObj.PartList.push(tempItem)\r\n          }\r\n        })\r\n\r\n        this.btnLoading = true\r\n        if (this.isEdit) {\r\n          // 获取更新后的表单数据\r\n          // submitObj.entity.Id = this.$route.query.id;\r\n          this.plm_ProjectSendingInfo.Code = this.form.receiveNum\r\n          this.plm_ProjectSendingInfo.Consignee = this.form.receiveName\r\n          this.plm_ProjectSendingInfo.ConsigneeTel = this.form.Receiver_Tel\r\n          this.plm_ProjectSendingInfo.VehicleNo = this.form.License\r\n          this.plm_ProjectSendingInfo.DriverName = this.form.Contact_UserName\r\n          this.plm_ProjectSendingInfo.Telephone = this.form.Mobile\r\n          this.plm_ProjectSendingInfo.Depart_Id = this.form.Depart_Id\r\n          this.plm_ProjectSendingInfo.MakerName = this.form.issueName\r\n          this.plm_ProjectSendingInfo.Address = this.form.Address\r\n          this.plm_ProjectSendingInfo.Contract_Id = this.form.Contract_Id\r\n          this.plm_ProjectSendingInfo.Logistics_Fee = this.form.Logistics_Fee\r\n          this.plm_ProjectSendingInfo.Loadings = this.form.Loadings\r\n          this.plm_ProjectSendingInfo.LoadingsPersonnel = this.form.LoadingsPersonnel\r\n          this.plm_ProjectSendingInfo.Trips = this.form.Trips\r\n          this.plm_ProjectSendingInfo.ReceivingUnit = this.form.ReceivingUnit\r\n          this.plm_ProjectSendingInfo.SendDate = parseTime(\r\n            this.form.Out_Date,\r\n            '{y}-{m}-{d} {h}:{i}:{s}'\r\n          )\r\n          this.plm_ProjectSendingInfo.Remarks = this.form.Remarks\r\n\r\n          const formAttachment = []\r\n          if (this.fileListArr.length > 0) {\r\n            this.fileListArr.forEach(item => {\r\n              formAttachment.push(\r\n                item.response && item.response.encryptionUrl\r\n                  ? item.response.encryptionUrl\r\n                  : item.encryptionUrl\r\n              )\r\n            })\r\n          }\r\n          this.plm_ProjectSendingInfo.Attachment = formAttachment.toString()\r\n          submitObj.plm_ProjectSendingInfo = this.plm_ProjectSendingInfo\r\n          // 获取新的表格数据\r\n          // submitObj.projectSendingInfo_Item =\r\n          //   submitObj.projectSendingInfo_Item.filter((item) => {\r\n          //     return !item.isOld;\r\n          //   });\r\n          // 添加新老表格数据\r\n          submitObj.projectSendingInfo_Item = [\r\n            ...this.Itemdetail,\r\n            ...this.PackagesList,\r\n            ...submitObj.projectSendingInfo_Item\r\n          ]\r\n          if (submitObj.projectSendingInfo_Item.length === 0 && submitObj.PartList.length === 0) {\r\n            this.$message({\r\n              message: '不能保存空发货单',\r\n              type: 'error'\r\n            })\r\n          } else {\r\n            this.loading = true\r\n            EditProjectSendingInfo(submitObj).then((res) => {\r\n              if (res.IsSucceed) {\r\n                this.$message({\r\n                  message: '编辑成功',\r\n                  type: 'success'\r\n                })\r\n                closeTagView(this.$store, this.$route)\r\n                this.$router.replace({\r\n                  name: 'PROShipSent',\r\n                  query: {\r\n                    refresh: 1\r\n                  }\r\n                })\r\n              } else {\r\n                this.isClicked = false\r\n                this.$message({\r\n                  message: res.Message,\r\n                  type: 'error'\r\n                })\r\n              }\r\n              this.loading = false\r\n              this.btnLoading = false\r\n            })\r\n          }\r\n        } else {\r\n          if (submitObj.projectSendingInfo_Item.length === 0 && submitObj.PartList.length === 0) {\r\n            this.$message({\r\n              message: '不能保存空发货单',\r\n              type: 'error'\r\n            })\r\n          } else {\r\n            console.log('submitObj', submitObj)\r\n            this.loading = true\r\n            AddProjectSendingInfo(submitObj).then((res) => {\r\n              if (res.IsSucceed) {\r\n                this.$message({\r\n                  message: '添加成功',\r\n                  type: 'success'\r\n                })\r\n                closeTagView(this.$store, this.$route)\r\n                this.$router.replace({\r\n                  name: 'PROShipSent',\r\n                  query: {\r\n                    refresh: 1\r\n                  }\r\n                })\r\n              } else {\r\n                this.isClicked = false\r\n                this.$message({\r\n                  message: res.Message,\r\n                  type: 'error'\r\n                })\r\n              }\r\n              this.btnLoading = false\r\n              this.loading = false\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    async getStopList(list, key) {\r\n      const submitObj = list.map(item => {\r\n        return {\r\n          Id: item[key],\r\n          Type: this.tabTypeCode === TAB_TYPE.com ? 2 : this.tabTypeCode === TAB_TYPE.unitPart ? 3 : 1 // 1：零件，3：部件，2：构件\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = !!item.Is_Stop\r\n          })\r\n          list.forEach(row => {\r\n            if (stopMap[row[key]]) {\r\n              this.$set(row, 'stopFlag', stopMap[row[key]])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async getInfo() {\r\n      await GetProjectsendinginEntity({\r\n        Id: this.$route.query.id\r\n      }).then(async(res) => {\r\n        if (res.IsSucceed) {\r\n          this.plm_ProjectSendingInfo = res.Data.Plm_ProjectSendingInfo\r\n          this.Itemdetail = res.Data.Itemdetail\r\n          this.PackagesList = res.Data.PackagesList\r\n          this.PartList = res.Data.PartList\r\n          this.weightform = res.Data.WeightInfo\r\n          this.pageStatus = this.plm_ProjectSendingInfo.Status\r\n          this.isDraft = this.plm_ProjectSendingInfo?.Status === 0\r\n\r\n          const {\r\n            Id,\r\n            Code,\r\n            ProjectId,\r\n            ProjectName,\r\n            Consignee,\r\n            ConsigneeTel,\r\n            Depart_Id,\r\n            MakerName,\r\n            VehicleNo,\r\n            DriverName,\r\n            Telephone,\r\n            Address,\r\n            Attachment,\r\n            SendDate,\r\n            Remarks,\r\n            Number,\r\n            Logistics_Fee,\r\n            Contract_Id,\r\n            Loadings,\r\n            LoadingsPersonnel,\r\n            Trips,\r\n            ReceivingUnit,\r\n\r\n          } = this.plm_ProjectSendingInfo\r\n\r\n          this.form.ProjectId = ProjectId\r\n          this.form.receiveNum = Code\r\n          this.form.projectName = ProjectName\r\n          this.form.receiveName = Consignee\r\n          this.form.Receiver_Tel = ConsigneeTel\r\n          this.form.Depart_Id = Depart_Id\r\n          this.form.issueName = MakerName\r\n          this.form.License = VehicleNo\r\n          this.form.Contact_UserName = DriverName\r\n          this.form.Mobile = Telephone\r\n          this.form.Address = Address\r\n          this.form.Out_Date = new Date(SendDate)\r\n          this.form.Remarks = Remarks\r\n          this.form.Logistics_Fee = Logistics_Fee || undefined\r\n          this.form.Contract_Id = Contract_Id\r\n          this.form.Loadings = Loadings\r\n          this.form.LoadingsPersonnel = LoadingsPersonnel\r\n          this.form.Trips = Trips\r\n          this.form.ReceivingUnit = ReceivingUnit\r\n          this.sendNumber = Number\r\n\r\n          if (VehicleNo && this.carOptions.every((v) => v.License !== VehicleNo)) {\r\n            this.carOptions.push({\r\n              License: VehicleNo,\r\n              Contact_UserName: DriverName,\r\n              Mobile: Telephone,\r\n              detail: VehicleNo ? `${VehicleNo}(${DriverName} ${Telephone})` : ''\r\n            })\r\n          }\r\n\r\n          if (Attachment) {\r\n            const AttachmentArr = Attachment.split(',')\r\n            AttachmentArr.forEach(item => {\r\n              const fileUrl =\r\n                item.indexOf('?Expires=') > -1\r\n                  ? item.substring(0, item.lastIndexOf('?Expires='))\r\n                  : item\r\n              const fileName = decodeURI(fileUrl.substring(fileUrl.lastIndexOf('/') + 1))\r\n              const AttachmentJson = {}\r\n              AttachmentJson.name = decodeURIComponent(fileName)\r\n              AttachmentJson.url = fileUrl\r\n              AttachmentJson.encryptionUrl = fileUrl\r\n              this.fileListData.push(AttachmentJson)\r\n              this.fileListArr.push(AttachmentJson)\r\n            })\r\n          }\r\n\r\n          if (this.weightform.Attachment_Weight) {\r\n            const imgPromiseAll2 = this.weightform.Attachment_Weight.split(',').map(async url => {\r\n              const fileUrl = url.split('?')[0]\r\n              return {\r\n                url: await this.handleUrl(fileUrl),\r\n                name: getFileNameFromUrl(fileUrl)\r\n              }\r\n            })\r\n            this.weightFileInfo = await Promise.all(imgPromiseAll2)\r\n          }\r\n\r\n          this.Itemdetail.forEach((item, index) => {\r\n            const {\r\n              Component_Id,\r\n              S_Count,\r\n              SteelWeight,\r\n              AllWeight,\r\n              Name,\r\n              Spec,\r\n              Length,\r\n              WarehouseName,\r\n              Code,\r\n              LocationName,\r\n              Area_Name,\r\n              Wait_Stock_Count,\r\n              Import_Detail_Id,\r\n              Location_Id,\r\n              SerialNumber\r\n            } = item\r\n            const tempItem = {\r\n              Id: Component_Id,\r\n              Area_Name: Area_Name,\r\n              Name,\r\n              Spec,\r\n              Length,\r\n              WarehouseName,\r\n              Code,\r\n              LocationName,\r\n              Import_Detail_Id,\r\n              Location_Id,\r\n              S_Count: S_Count,\r\n              Wait_Stock_Count: Wait_Stock_Count,\r\n              Netweight: SteelWeight,\r\n              AllWeight: AllWeight,\r\n              isOld: true,\r\n              SerialNumber: SerialNumber\r\n            }\r\n            this.tbData.push(tempItem)\r\n            this.old_Component_Ids.push(Component_Id)\r\n            this.getStopList(this.tbData, 'Component_Id')\r\n          })\r\n          this.PartList.forEach((element, idx) => {\r\n            const tempItem = { ...element }\r\n            tempItem.S_Count = tempItem.Amount\r\n            tempItem.Netweight = tempItem.Weight\r\n            tempItem.AllWeight = this.getAllWeight(tempItem)\r\n            tempItem.Total_Weight = numeral(tempItem.Stock_Count).multiply(tempItem.Weight).value()\r\n            if (tempItem.Part_Grade > 0) {\r\n              this.tbData3.push(tempItem)\r\n              this.getStopList(this.tbData3, 'Part_Aggregate_Id')\r\n              console.log('this.tbData3', this.tbData3)\r\n            } else {\r\n              this.tbData4.push(tempItem)\r\n              this.getStopList(this.tbData4, 'Part_Aggregate_Id')\r\n              console.log('this.tbData4', this.tbData4)\r\n            }\r\n          })\r\n\r\n          this.PackagesList.forEach((item, index) => {\r\n            const {\r\n              PkgNO,\r\n              PackageSn,\r\n              AllWeight,\r\n              Volume,\r\n              AllAmount,\r\n              WarehouseName,\r\n              LocationName,\r\n              DIM,\r\n              PackageId\r\n            } = item\r\n            const tempItem = {\r\n              PkgNO: PkgNO,\r\n              PackageSn: PackageSn,\r\n              AllWeight,\r\n              AllAmount,\r\n              Volume,\r\n              WarehouseName,\r\n              LocationName,\r\n              isOld: true,\r\n              DIM: DIM,\r\n              PackageId\r\n            }\r\n            this.tbData2.push(tempItem)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    checkSelectable(row) {\r\n      return !row.stopFlag\r\n    },\r\n    async handleUrl(url) {\r\n      const { Data } = await GetOssUrl({ url })\r\n      return Data\r\n    },\r\n    carChange(val) {\r\n      if (!val) {\r\n        this.form.Contact_UserName = ''\r\n        this.form.Mobile = ''\r\n        this.form.License = ''\r\n        return\r\n      }\r\n      const item = this.carOptions.find((v) => v.License === this.form.License)\r\n      this.form.Contact_UserName = item.Contact_UserName\r\n      this.form.Mobile = item.Mobile\r\n      this.form.License = item.License\r\n    },\r\n    projectIdChange(e) {\r\n      // if (e) {\r\n      //   this.getAreaList();\r\n      // }\r\n    },\r\n    projectIdClear(e) {\r\n      // this.$refs.form2.resetFields();\r\n    },\r\n    getAllWeight(item) {\r\n      return Number(item.S_Count * item.Netweight).toFixed(2) / 1\r\n    },\r\n    addSelectList(list) {\r\n      console.log(list, 'list')\r\n      console.log(this.tabTypeCode)\r\n      if (this.tabTypeCode === TAB_TYPE.com) {\r\n        console.log(11)\r\n        list.forEach((item) => {\r\n          item.AllWeight = this.getAllWeight(item)\r\n\r\n          this.tbData.push(item)\r\n        })\r\n        this.tbData = JSON.parse(JSON.stringify(this.tbData))\r\n        this.total = this.tbData.length\r\n      } else if (this.tabTypeCode === TAB_TYPE.unitPart) {\r\n        list.forEach((item) => {\r\n          item.AllWeight = this.getAllWeight(item)\r\n          this.tbData3.push(item)\r\n        })\r\n        // dont ask why just cv\r\n        this.tbData3 = JSON.parse(JSON.stringify(this.tbData3))\r\n        this.total = this.tbData3.length\r\n      } else if (this.tabTypeCode === TAB_TYPE.part) {\r\n        list.forEach((item) => {\r\n          item.AllWeight = this.getAllWeight(item)\r\n          this.tbData4.push(item)\r\n        })\r\n        this.tbData4 = JSON.parse(JSON.stringify(this.tbData4))\r\n        this.total = this.tbData4.length\r\n      } else if (this.tabTypeCode === TAB_TYPE.package) {\r\n        list.forEach((item) => {\r\n          this.tbData2.push(item)\r\n        })\r\n        this.total = this.tbData2.length\r\n      }\r\n    },\r\n    handleDelete() {\r\n      this.$confirm('删除该数据, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          if (this.Is_Pack) {\r\n            this.selectList.forEach((item) => {\r\n              const index = this.tbData2.findIndex(\r\n                (v) => v.PackageSn === item.PackageSn\r\n              )\r\n              index !== -1 && this.tbData2.splice(index, 1)\r\n              if (this.isEdit) {\r\n                const index = this.PackagesList.findIndex(\r\n                  (v) => v.PackageSn === item.PackageSn\r\n                )\r\n                index !== -1 && this.PackagesList.splice(index, 1)\r\n              }\r\n            })\r\n          } else {\r\n            let _table = null\r\n            if (this.tabTypeCode === TAB_TYPE.com) {\r\n              _table = this.tbData\r\n              this.selectList.forEach((item) => {\r\n                const index = _table.findIndex((v) => v.Import_Detail_Id + v.Location_Id === item.Import_Detail_Id + item.Location_Id)\r\n                index !== -1 && _table.splice(index, 1)\r\n                if (this.isEdit) {\r\n                  const index = this.Itemdetail.findIndex(\r\n                    (v) => v.Component_Id === item.Id\r\n                  )\r\n                  index !== -1 && this.Itemdetail.splice(index, 1)\r\n                }\r\n              })\r\n            } else if (this.tabTypeCode === TAB_TYPE.unitPart) {\r\n              _table = this.tbData3\r\n              this.selectList.forEach((item) => {\r\n                const index = _table.findIndex((v) => v.Part_Produced_Id === item.Part_Produced_Id)\r\n                index !== -1 && _table.splice(index, 1)\r\n              })\r\n            } else if (this.tabTypeCode === TAB_TYPE.part) {\r\n              _table = this.tbData4\r\n              this.selectList.forEach((item) => {\r\n                const index = _table.findIndex((v) => v.Part_Produced_Id === item.Part_Produced_Id)\r\n                index !== -1 && _table.splice(index, 1)\r\n              })\r\n            }\r\n          }\r\n          this.$message({\r\n            type: 'success',\r\n            message: '删除成功!'\r\n          })\r\n        })\r\n        .catch(() => {})\r\n    },\r\n    multiSelectedChange(v) {\r\n      this.selectList = v\r\n    },\r\n    fetchData() {\r\n      // GetStockOutDetailList({ stockOutId: this.$route.query.id }).then(res => {\r\n      //   if (res.IsSucceed) {\r\n      //     this.tbData = res.Data\r\n      //     this.tbData.forEach((element, idx) => {\r\n      //       this.$set(element, 'UniqueCodesArray', element.UniqueCodes.split(','))\r\n      //     })\r\n      //   } else {\r\n      //     this.$message({\r\n      //       message: res.Message,\r\n      //       type: 'error'\r\n      //     })\r\n      //   }\r\n      //   this.tbLoading = false\r\n      // })\r\n    },\r\n    getTotal() {\r\n      // this.$nextTick(_ => {\r\n      //   const columns = this.$refs.dyTable.$refs.dtable.columns\r\n      //   columns.forEach((element, idx) => {\r\n      //     if (idx === 0) {\r\n      //       this.sums[0] = '合计'\r\n      //     } else if (element.property === 'Out_Count') {\r\n      //       const v = this.tbData.reduce((acc, cur) => {\r\n      //         return this.highPrecisionAdd(acc, cur[element.property])\r\n      //       }, 0)\r\n      //       this.$set(this.sums, idx, v)\r\n      //     } else if (element.property === 'NetWeight') {\r\n      //       const v = this.tbData.reduce((acc, cur) => {\r\n      //         return this.highPrecisionAdd(acc, cur[element.property] * cur['Out_Count'])\r\n      //       }, 0)\r\n      //       this.$set(this.sums, idx, numeral(v).divide(1000).format('0.[00]') + '（t）')\r\n      //     } else {\r\n      //       this.$set(this.sums, idx, '')\r\n      //     }\r\n      //   })\r\n      // })\r\n    },\r\n    getAllCarList() {\r\n      return new Promise((resolve) => {\r\n        GetCurCarPageList({}).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.carOptions = res.Data\r\n            this.carOptions.forEach((element, idx) => {\r\n              this.$set(\r\n                element,\r\n                'detail',\r\n                `${element.License}(${element.Contact_UserName} ${element.Mobile})`\r\n              )\r\n            })\r\n            resolve()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    addCarData(data) {\r\n      this.getAllCarList()\r\n      // this.carOptions.push(data);\r\n    },\r\n    handleAdd() {\r\n      this.currentComponent = 'AddDialog'\r\n      this.width = '80%'\r\n      this.topDialog = '1vh'\r\n      this.dialogVisible = true\r\n      if (this.Is_Pack === false) {\r\n        let _table = null\r\n        if (this.tabTypeCode === TAB_TYPE.com) {\r\n          _table = this.tbData\r\n          this.title = '添加构件'\r\n        } else if (this.tabTypeCode === TAB_TYPE.unitPart) {\r\n          _table = this.tbData3\r\n          this.title = '添加部件'\r\n        } else if (this.tabTypeCode === TAB_TYPE.part) {\r\n          _table = this.tbData4\r\n          this.title = '添加零件'\r\n        }\r\n\r\n        const tempData1 = _table.filter((item) => {\r\n          return !item.isOld\r\n        })\r\n        this.$nextTick((_) => {\r\n          this.$refs.content.init(tempData1, _table)\r\n        })\r\n      } else if (this.Is_Pack === true) {\r\n        this.title = '添加打包件'\r\n        const tempData2 = this.tbData2.filter((item) => {\r\n          return !item.isOld\r\n        })\r\n        this.$nextTick((_) => {\r\n          this.$refs.content.init(tempData2, this.tbData2)\r\n        })\r\n      }\r\n    },\r\n    // 打包件详情\r\n    handleDetail(row) {\r\n      console.log(row, 'id===')\r\n      this.currentComponent = 'packDetail'\r\n      this.width = '60%'\r\n      this.title = '打包件详情'\r\n      this.topDialog = '10vh'\r\n      this.dialogVisible = true\r\n      this.$nextTick((_) => {\r\n        this.$refs.content.init(row.PackageId || row.Id)\r\n      })\r\n    },\r\n    handleEditCar() {\r\n      this.currentComponent = 'CarDialog'\r\n      this.title = '新增车辆'\r\n      this.topDialog = '10vh'\r\n      this.dialogVisible = true\r\n    },\r\n    close() {\r\n      this.dialogVisible = false\r\n    },\r\n    handleInfo(row) {\r\n      this.$refs.info.handleOpen(row)\r\n    },\r\n    getTableConfig(code) {\r\n      return new Promise((resolve) => {\r\n        GetGridByCode({\r\n          code\r\n        }).then((res) => {\r\n          const { IsSucceed, Data, Message } = res\r\n          if (IsSucceed) {\r\n            if (!Data) {\r\n              this.$message({\r\n                message: '表格配置不存在',\r\n                type: 'error'\r\n              })\r\n              return\r\n            }\r\n            this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n            this.columns = (\r\n              Data.ColumnList.filter((v) => v.Is_Display) || []\r\n            ).map((item) => {\r\n              item.Is_Resizable = true\r\n              item.Is_Sortable = true\r\n              return item\r\n            })\r\n            this.form.PageInfo.PageSize = +Data.Grid.Row_Number\r\n            if (this.isSub) {\r\n              this.tbConfig.Is_Select = false\r\n              this.tbConfig.Is_Row_Number = true\r\n            }\r\n            resolve(this.columns)\r\n          } else {\r\n            this.$message({\r\n              message: Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    uploadSuccess(response, file, fileList) {\r\n      console.log('fileList', fileList)\r\n      this.fileListArr = JSON.parse(JSON.stringify(fileList))\r\n    },\r\n    uploadRemove(file, fileList) {\r\n      this.fileListArr = JSON.parse(JSON.stringify(fileList))\r\n    },\r\n    async handlePreview(file) {\r\n      console.log('file', file)\r\n      let encryptionUrl = ''\r\n      if (file.response && file.response.encryptionUrl) {\r\n        encryptionUrl = file.response.encryptionUrl\r\n      } else {\r\n        encryptionUrl = await GetOssUrl({ url: file.encryptionUrl })\r\n        encryptionUrl = encryptionUrl.Data\r\n      }\r\n      window.open(encryptionUrl)\r\n    },\r\n    handleExceed() {\r\n      this.$message({\r\n        type: 'warning',\r\n        message: '附件数量不能超过10个'\r\n      })\r\n    },\r\n    async getContractList() {\r\n      try {\r\n        const submitData = {\r\n          ContractTypeCode: 3,\r\n          ProjectIds: []\r\n        }\r\n        if (this.form.ProjectId) {\r\n          submitData.ProjectIds.push(this.form.ProjectId)\r\n        }\r\n        const res = await GetContractList(submitData)\r\n        if (res.IsSucceed) {\r\n          this.contractOptions = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      } catch (error) {\r\n        console.error('获取合同列表失败:', error)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.tb-status {\r\n  background: #fae6bb;\r\n  padding: 16px 20px;\r\n  font-size: 1.2em;\r\n  font-weight: bold;\r\n  display: flex;\r\n\r\n  * {\r\n    margin-right: 12px;\r\n  }\r\n}\r\n\r\n.el-form {\r\n  margin: 16px 10px;\r\n}\r\n\r\n.title {\r\n  margin-left: 10px;\r\n}\r\n\r\n.cs-red{\r\n  color:red\r\n}\r\n\r\n.cs-tree-x {\r\n  ::v-deep {\r\n    .el-select {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n\r\n.statistics-container {\r\n  display: flex;\r\n  .statistics-item {\r\n    margin-right: 32px;\r\n    span:first-child {\r\n      display: inline-block;\r\n      font-size: 14px;\r\n      line-height: 18px;\r\n      font-weight: 500;\r\n      color: #999999;\r\n      // margin-right: 16px;\r\n    }\r\n    span:last-child {\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n      color: #00c361;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
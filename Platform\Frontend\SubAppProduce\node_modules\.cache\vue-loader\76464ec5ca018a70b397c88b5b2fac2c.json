{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\index.vue?vue&type=style&index=0&id=06dae726&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\index.vue", "mtime": 1756109946518}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmNvbnRhaW5lciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIHBhZGRpbmc6IDAgMTZweCAxNnB4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBtaW4td2lkdGg6IDk5OHB4Ow0KICBvdmVyZmxvdzogaGlkZGVuOw0KDQogIC50b3AteCB7DQogICAgbGluZS1oZWlnaHQ6IDQ4cHg7DQogICAgaGVpZ2h0OiA0OHB4Ow0KICB9DQoNCiAgLmNhcmQteCB7DQogICAgaGVpZ2h0OiBjYWxjKDEwMCUgLSA0OHB4KTsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KDQogICAgLnJpZ2h0LWNhcmQgew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICBmbGV4OiAxOw0KICAgICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgICAgYmFja2dyb3VuZC1jb2xvcjogI0ZGRkZGRjsNCiAgICAgIC5lbC1mb3Jtew0KICAgICAgICB3aWR0aDogNTAlOw0KICAgICAgICBtYXJnaW46ICBhdXRvOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyOA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/bom-setting/structure-type-config", "sourcesContent": ["<template>\r\n  <div class=\"container abs100\">\r\n\r\n    <div class=\"card-x\">\r\n      <tree-data ref=\"tree\" :type-code=\"typeCode\" :type-id=\"typeId\" @nodeClick=\"nodeClick\" @AddFirst=\"addFirst\" @showRight=\"showRight\" />\r\n      <div class=\"right-card\">\r\n        <el-form v-if=\"showForm\" ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n          <el-form-item :label=\"`${levelName}大类名称`\" prop=\"Name\">\r\n            <el-input v-model.trim=\"form.Name\" clearable maxlength=\"50\" />\r\n          </el-form-item>\r\n          <el-form-item :label=\"`${levelName}大类编号`\" prop=\"Code\">\r\n            <el-input v-model=\"form.Code\" disabled />\r\n          </el-form-item>\r\n          <el-form-item label=\"生产周期\" prop=\"Lead_Time\">\r\n            <el-input-number v-model.number=\"form.Lead_Time\" class=\"cs-number-btn-hidden w100\" clearable />\r\n          </el-form-item>\r\n          <el-form-item label=\"直发件\" prop=\"Is_Component\">\r\n            <el-radio-group v-model=\"form.Is_Component\">\r\n              <el-radio :label=\"true\">否</el-radio>\r\n              <el-radio :label=\"false\">是</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button v-if=\"level<3\" type=\"text\" icon=\"el-icon-plus\" @click=\"addNext\">新增下一级</el-button>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" :loading=\"submitLoading\" @click=\"submit\">保存</el-button>\r\n            <el-button type=\"danger\" :loading=\"deleteLoading\" :disabled=\"hasChildrenNode\" @click=\"handleDelete\">删除</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"title\"\r\n      class=\"plm-custom-dialog\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"30%\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        ref=\"content\"\r\n        :add-level=\"addLevel\"\r\n        :parent-id=\"parentId\"\r\n        @close=\"handleClose\"\r\n        @getTreeList=\"getTreeData\"\r\n      />\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport TreeData from './component/TreeData'\r\nimport Add from './component/Add'\r\nimport { DeleteComponentType, GetComponentTypeEntity, SaveProBimComponentType } from '@/api/PRO/component-type'\r\n\r\nexport default {\r\n  name: 'ProfessionalCategoryListInfo',\r\n  components: {\r\n    TreeData,\r\n    Add\r\n  },\r\n  data() {\r\n    return {\r\n      typeCode: 'Steel',\r\n      typeId: 'd28a81a0-ce31-4b56-8e22-b86922668894',\r\n      level: 1,\r\n      addLevel: undefined,\r\n      dialogVisible: false,\r\n      submitLoading: false,\r\n      deleteLoading: false,\r\n      showForm: false,\r\n      hasChildrenNode: true,\r\n      currentComponent: '',\r\n      parentId: '',\r\n      title: '',\r\n      form: {\r\n        Name: '',\r\n        Code: '',\r\n        Is_Component: '',\r\n        Lead_Time: 0\r\n      },\r\n      rules: {\r\n        Name: [\r\n          { required: true, message: '请输入名称', trigger: 'blur' }\r\n        ],\r\n        Code: [\r\n          { required: true, message: '请输入编码', trigger: 'blur' }\r\n        ],\r\n        Is_Component: [\r\n          { required: true, message: '请选择是否直发件', trigger: 'change' }\r\n        ],\r\n        Lead_Time: [\r\n          { required: true, message: '请输入周期', trigger: 'blur' }\r\n        ]\r\n      },\r\n      Is_Component: ''\r\n    }\r\n  },\r\n  computed: {\r\n    levelName() {\r\n      return this.level === 1 ? '一级' : (this.level === 2 ? '二级' : (this.level === 3 ? '三级' : ''))\r\n    }\r\n  },\r\n  methods: {\r\n    addNext() {\r\n      this.currentComponent = 'Add'\r\n      this.addLevel = this.level + 1\r\n      this.title = `新增下一级`\r\n      this.parentId = this.form.Id\r\n      this.dialogVisible = true\r\n    },\r\n    showRight(v) {\r\n      this.showForm = v\r\n    },\r\n    submit() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (!valid) {\r\n          return false\r\n        }\r\n        if (this.Is_Component != this.form.Is_Component) {\r\n          this.$confirm('直发件属性不会同步到已导入构件清单中，确认修改？', '提示', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n            this.submitConfirm()\r\n          }).catch(() => {\r\n            this.$message({\r\n              type: 'info',\r\n              message: '已取消修改'\r\n            })\r\n          })\r\n        } else {\r\n          this.submitConfirm()\r\n        }\r\n      })\r\n    },\r\n    submitConfirm() {\r\n      this.submitLoading = true\r\n      SaveProBimComponentType(this.form).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '修改成功',\r\n            type: 'success'\r\n          })\r\n          this.getTreeData()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.submitLoading = false\r\n      })\r\n    },\r\n    getTreeData() {\r\n      this.$refs['tree'].fetchData()\r\n    },\r\n    addFirst() {\r\n      this.currentComponent = 'Add'\r\n      this.title = '新增类别'\r\n      this.addLevel = 1\r\n      this.dialogVisible = true\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    nodeClick(node) {\r\n      this.level = node.level\r\n      this.hasChildrenNode = node.childNodes.length > 0\r\n      this.getInfo(node.data.Id)\r\n    },\r\n    getInfo(id) {\r\n      GetComponentTypeEntity({\r\n        id\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          Object.assign(this.form, res.Data)\r\n          this.Is_Component = res.Data.Is_Component\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    handleDelete() {\r\n      this.$confirm('是否删除当前类别?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.deleteLoading = true\r\n        DeleteComponentType({\r\n          ids: this.form.Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.getTreeData()\r\n            this.$refs['tree'].resetKey(this.form.Id)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        }).finally(_ => {\r\n          this.deleteLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.container {\r\n  display: flex;\r\n  padding: 0 16px 16px;\r\n  flex-direction: column;\r\n  min-width: 998px;\r\n  overflow: hidden;\r\n\r\n  .top-x {\r\n    line-height: 48px;\r\n    height: 48px;\r\n  }\r\n\r\n  .card-x {\r\n    height: calc(100% - 48px);\r\n    display: flex;\r\n\r\n    .right-card {\r\n      display: flex;\r\n      flex-direction: column;\r\n      flex: 1;\r\n      border-radius: 4px;\r\n      background-color: #FFFFFF;\r\n      .el-form{\r\n        width: 50%;\r\n        margin:  auto;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
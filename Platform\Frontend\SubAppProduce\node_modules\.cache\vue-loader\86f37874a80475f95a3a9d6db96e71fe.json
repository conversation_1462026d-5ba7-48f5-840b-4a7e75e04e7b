{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\index.vue", "mtime": 1757470958558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBUb3BIZWFkZXIgZnJvbSAnQC9jb21wb25lbnRzL1RvcEhlYWRlcicKaW1wb3J0IEFkZCBmcm9tICcuL2NvbXBvbmVudC9BZGQnCmltcG9ydCBaQ2xhc3MgZnJvbSAnLi9jb21wb25lbnQvR3JvdXAnCmltcG9ydCBBc3NvY2lhdGVkRGV2aWNlIGZyb20gJy4vY29tcG9uZW50L0Fzc29jaWF0ZWREZXZpY2UnCmltcG9ydCBSZWNvZ25pdGlvbkNvbmZpZyBmcm9tICcuL2NvbXBvbmVudC9SZWNvZ25pdGlvbkNvbmZpZycKaW1wb3J0IHBhcnRSZWNvZ25pdGlvbkNvbmZpZyBmcm9tICcuL2NvbXBvbmVudC9wYXJ0UmVjb2duaXRpb25Db25maWcnCmltcG9ydCBjb21wUmVjb2duaXRpb25Db25maWcgZnJvbSAnLi9jb21wb25lbnQvY29tcFJlY29nbml0aW9uQ29uZmlnJwppbXBvcnQgdW5pdFBhcnRSZWNvZ25pdGlvbkNvbmZpZyBmcm9tICcuL2NvbXBvbmVudC91bml0UGFydFJlY29nbml0aW9uQ29uZmlnJwppbXBvcnQgUGFydFRha2VDb25maWcgZnJvbSAnLi9jb21wb25lbnQvUGFydFRha2VDb25maWcnCmltcG9ydCB7IEdldFByb2Nlc3NMaXN0QmFzZSwgRGVsZXRlUHJvY2VzcyB9IGZyb20gJ0AvYXBpL1BSTy90ZWNobm9sb2d5LWxpYicKaW1wb3J0IEVsVGFibGVFbXB0eSBmcm9tICdAL2NvbXBvbmVudHMvRWxUYWJsZUVtcHR5L2luZGV4LnZ1ZScKaW1wb3J0IGFkZFJvdXRlclBhZ2UgZnJvbSAnQC9taXhpbnMvYWRkLXJvdXRlci1wYWdlJwppbXBvcnQgeyBHZXRCT01JbmZvIH0gZnJvbSAnQC92aWV3cy9QUk8vYm9tLXNldHRpbmcvdXRpbHMnCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1BST1Byb2Nlc3NNYW5hZ2VtZW50JywKICBjb21wb25lbnRzOiB7CiAgICBFbFRhYmxlRW1wdHksCiAgICBUb3BIZWFkZXIsCiAgICBBZGQsCiAgICBwYXJ0UmVjb2duaXRpb25Db25maWcsCiAgICBjb21wUmVjb2duaXRpb25Db25maWcsCiAgICBQYXJ0VGFrZUNvbmZpZywKICAgIFpDbGFzcywKICAgIEFzc29jaWF0ZWREZXZpY2UsCiAgICB1bml0UGFydFJlY29nbml0aW9uQ29uZmlnLAogICAgUmVjb2duaXRpb25Db25maWcKICB9LAogIG1peGluczogW2FkZFJvdXRlclBhZ2VdLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBsZXZlbDogMCwKICAgICAgYm9tTGlzdDogW10sCiAgICAgIGFkZFBhZ2VBcnJheTogWwogICAgICAgIHsKICAgICAgICAgIHBhdGg6ICcvQXNzb2NpYXRlZERldmljZScsCiAgICAgICAgICBoaWRkZW46IHRydWUsCiAgICAgICAgICBjb21wb25lbnQ6ICgpID0+IGltcG9ydCgnQC92aWV3cy9QUk8vcHJvY2Vzcy1zZXR0aW5ncy9tYW5hZ2VtZW50L2NvbXBvbmVudC9Bc3NvY2lhdGVkRGV2aWNlLnZ1ZScpLAogICAgICAgICAgbmFtZTogJ0Fzc29jaWF0ZWREZXZpY2UnLAogICAgICAgICAgbWV0YTogeyB0aXRsZTogJ+WFs+iBlOiuvuWkhycgfQogICAgICAgIH0KCiAgICAgIF0sCiAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgIGN1cnJlbnRDb21wb25lbnQ6ICcnLAogICAgICB0aXRsZTogJycsCiAgICAgIGNvbU5hbWU6ICcnLAogICAgICBwYXJ0TmFtZTogJycsCiAgICAgIHJvd0luZm86IG51bGwsCiAgICAgIHJvd0RhdGE6IHt9LAogICAgICB0eXBlOiAnJywKICAgICAgcGFnZUxvYWRpbmc6IGZhbHNlLAogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgZGlhbG9nVmlzaWJsZTE6IGZhbHNlLAogICAgICB1bml0UGFydExpc3Q6IFtdLAogICAgICBmb3JtSW5saW5lOiB7IG5hbWU6ICcnLCBjb2RlOiAnJyB9CiAgICB9CiAgfSwKCiAgbW91bnRlZCgpIHsKICAgIHRoaXMuZ2V0Qk9NSW5mbygpCiAgICB0aGlzLmZldGNoRGF0YSgpCiAgfSwKICBtZXRob2RzOiB7CiAgICBhc3luYyBnZXRCT01JbmZvKCkgewogICAgICBjb25zdCB7IGNvbU5hbWUsIHBhcnROYW1lLCBsaXN0IH0gPSBhd2FpdCBHZXRCT01JbmZvKCkKICAgICAgdGhpcy5jb21OYW1lID0gY29tTmFtZQogICAgICB0aGlzLnBhcnROYW1lID0gcGFydE5hbWUKICAgICAgdGhpcy5ib21MaXN0ID0gbGlzdAogICAgICB0aGlzLnVuaXRQYXJ0TGlzdCA9IGxpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbS5Db2RlICE9PSAnMCcgJiYgaXRlbS5Db2RlICE9PSAnLTEnKQogICAgfSwKICAgIGhhbmRsZU9wZW5EZXZpY2Uocm93KSB7CiAgICAgIHRoaXMucm93RGF0YSA9IHJvdwogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUxID0gdHJ1ZQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgdGhpcy4kcmVmcy5EZXZpY2UuY2xlYXJTZWxlYygpCiAgICAgIH0pCiAgICAgIC8vICB0aGlzLiRyb3V0ZXIucHVzaCh7IHBhdGg6ICdodHRwOi8vbG9jYWxob3N0OjMwMDAvcHJvZHVjZS9wcm8vbmVzdGluZy9pbmRleCcgfSkKICAgIH0sCgogICAgZmV0Y2hEYXRhKCkgewogICAgICB0aGlzLnBhZ2VMb2FkaW5nID0gdHJ1ZQogICAgICBHZXRQcm9jZXNzTGlzdEJhc2UodGhpcy5mb3JtSW5saW5lKS50aGVuKChyZXMpID0+IHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgdGhpcy50YWJsZURhdGEgPSByZXMuRGF0YQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICAgIHRoaXMucGFnZUxvYWRpbmcgPSBmYWxzZQogICAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZTEgPSBmYWxzZQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZUNsb3NlKCkgewogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZQogICAgfSwKICAgIGhhbmRsZUNsb3NlMSgpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlMSA9IGZhbHNlCiAgICB9LAogICAgaGFuZGxlUmVjb2duaXRpb25Db25maWcoKSB7CiAgICAgIHRoaXMudGl0bGUgPSBg5a+85YWl6K+G5Yir6YWN572uYAogICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSAnUmVjb2duaXRpb25Db25maWcnCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUKICAgIH0sCiAgICBoYW5kbGVVbml0UGFydENvbmZpZyhpdGVtKSB7CiAgICAgIHRoaXMubGV2ZWwgPSAraXRlbS5Db2RlCiAgICAgIHRoaXMudGl0bGUgPSBgJHtpdGVtLkRpc3BsYXlfTmFtZX3or4bliKvphY3nva5gCiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9ICd1bml0UGFydFJlY29nbml0aW9uQ29uZmlnJwogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlCiAgICB9LAogICAgaGFuZGxlQ29uZmlnKCkgewogICAgICB0aGlzLnRpdGxlID0gYCR7dGhpcy5wYXJ0TmFtZX3or4bliKvphY3nva5gCiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9ICdwYXJ0UmVjb2duaXRpb25Db25maWcnCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUKICAgIH0sCiAgICBoYW5kbGVDb25maWdDb21wKCkgewogICAgICB0aGlzLnRpdGxlID0gYCR7dGhpcy5jb21OYW1lfeivhuWIq+mFjee9rmAKICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ2NvbXBSZWNvZ25pdGlvbkNvbmZpZycKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQogICAgfSwKICAgIGhhbmRsZVRha2VDb25maWcoKSB7CiAgICAgIHRoaXMudGl0bGUgPSBgJHt0aGlzLnBhcnROYW1lfemihueUqOmFjee9rmAKICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ1BhcnRUYWtlQ29uZmlnJwogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlCiAgICB9LAogICAgaGFuZGxlRGlhbG9nKHR5cGUsIHJvdykgewogICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSAnQWRkJwogICAgICB0aGlzLnR5cGUgPSB0eXBlCiAgICAgIGlmICh0eXBlID09PSAnYWRkJykgewogICAgICAgIHRoaXMudGl0bGUgPSAn5paw5bu6JwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMudGl0bGUgPSAn57yW6L6RJwogICAgICAgIHRoaXMucm93SW5mbyA9IHJvdwogICAgICB9CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUKICAgIH0sCiAgICBoYW5kbGVNYW5hZ2Uocm93KSB7CiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9ICdaQ2xhc3MnCiAgICAgIHRoaXMudGl0bGUgPSAn54+t57uE566h55CGJwogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlCiAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7CiAgICAgICAgdGhpcy4kcmVmcy5jb250ZW50LmluaXQocm93KQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZURlbGV0ZShwcm9jZXNzSWQpIHsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm5Yig6Zmk5b2T5YmN5bel5bqPPycsICfmj5DnpLonLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KQogICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgIERlbGV0ZVByb2Nlc3MoewogICAgICAgICAgICBwcm9jZXNzSWQKICAgICAgICAgIH0pLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKnycsCiAgICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycKICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgIHRoaXMuZmV0Y2hEYXRhKCkKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgICAgIH0pCiAgICAgICAgICAgIH0KICAgICAgICAgIH0pCiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goKCkgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdpbmZvJywKICAgICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iOWIoOmZpCcKICAgICAgICAgIH0pCiAgICAgICAgfSkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/process-settings/management", "sourcesContent": ["<template>\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\n    <div v-loading=\"pageLoading\" class=\"cs-z-page-main-content\">\n      <div class=\"page-header\">\n        <div class=\"header-left\">\n          <el-button icon=\"el-icon-plus\" type=\"primary\" size=\"small\" @click=\"handleDialog('add')\">新增</el-button>\n          <el-button type=\"success\" size=\"small\" @click=\"handleRecognitionConfig\">导入识别配置</el-button>\n          <el-button type=\"success\" size=\"small\" @click=\"handleConfig\">{{ partName }}识别配置</el-button>\n          <el-button type=\"success\" size=\"small\" @click=\"handleConfigComp\">{{ comName }}识别配置</el-button>\n          <el-button v-for=\"item in unitPartList\" :key=\"item.Code\" type=\"success\" size=\"small\" @click=\"handleUnitPartConfig(item)\">\n            {{ item.Display_Name }}识别配置</el-button>\n          <el-button type=\"primary\" size=\"small\" @click=\"handleTakeConfig\">{{ partName }}领用配置</el-button>\n        </div>\n        <div class=\"header-right\">\n          <el-form :inline=\"true\" :model=\"formInline\" class=\"demo-form-inline\" style=\"line-height: 32px\">\n            <el-form-item label=\"工序名称\">\n              <el-input v-model=\"formInline.name\" clearable placeholder=\"请输入工序名称\" />\n            </el-form-item>\n            <el-form-item label=\"代号\">\n              <el-input v-model=\"formInline.code\" clearable placeholder=\"请输入代号\" />\n            </el-form-item>\n            <el-form-item>\n              <el-button type=\"primary\" @click=\"fetchData\">查询</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n      </div>\n      <el-table class=\"cs-custom-table tb\" border stripe height=\"100%\" :data=\"tableData\" style=\"width: 100%\">\n        <template #empty>\n          <ElTableEmpty />\n        </template>\n        <el-table-column prop=\"Professional_Code\" label=\"专业\">\n          <template slot-scope=\"{ row }\">\n            {{ row.Professional_Code || '-' }}\n          </template>\n        </el-table-column>\n        <el-table-column min-width=\"150px\" prop=\"Name\" label=\"工序名称\">\n          <template slot-scope=\"{ row }\">\n            {{ row.Name || '-' }}\n          </template>\n        </el-table-column>\n        <el-table-column min-width=\"120px\" prop=\"Code\" label=\"代号\">\n          <template slot-scope=\"{ row }\">\n            {{ row.Code || '-' }}\n          </template>\n        </el-table-column>\n        <el-table-column min-width=\"150px\" prop=\"Type_Name\" label=\"类型\">\n          <template slot-scope=\"{ row }\">\n            {{ row.Type_Name || '-' }}\n            <!-- <span v-if=\"row.Type === 1\" style=\"color: #d29730\">\n              <i class=\"iconfont icon-steel cs-tb-icon\" />\n              构件工序\n            </span>\n            <span v-if=\"row.Type === 2\" style=\"color: #20bbc7\">\n              <i class=\"iconfont icon-material-filled cs-tb-icon\" />\n              零件工序\n            </span>\n            <span v-if=\"row.Type === 3\" style=\"color: #de85e4\">\n              <i class=\"iconfont icon-material-filled cs-tb-icon\" />\n              部件工序\n            </span> -->\n          </template>\n        </el-table-column>\n        <!-- <el-table-column  prop=\"Code\" label=\"工序代码\" /> -->\n\n        <el-table-column min-width=\"150px\" prop=\"Coordinate_UserName\" label=\"工序协调人\" align=\"center\">\n          <template slot-scope=\"{ row }\">\n            {{ row.Coordinate_UserName || '-' }}\n          </template>\n        </el-table-column>\n        <el-table-column min-width=\"150px\" prop=\"Is_Need_Check\" label=\"是否专检\" align=\"center\">\n          <template slot-scope=\"{ row }\">\n            <el-tag v-if=\"row.Is_Need_Check\" type=\"success\">是</el-tag>\n            <el-tag v-else type=\"danger\">否</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column min-width=\"150px\" prop=\"Check_Style\" label=\"专检方式\">\n          <template slot-scope=\"{ row }\">\n            {{\n              row.Check_Style == 0 ? \"抽检\" : row.Check_Style == 1 ? \"全检\" : \"-\"\n            }}\n          </template>\n        </el-table-column>\n        <el-table-column min-width=\"150px\" prop=\"Is_Inter_Check\" label=\"是否互检\">\n          <template slot-scope=\"{ row }\">\n            <el-tag v-if=\"row.Is_Inter_Check\" type=\"success\">是</el-tag>\n            <el-tag v-else type=\"danger\">否</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column min-width=\"150px\" prop=\"Team_Names\" label=\"加工班组\" show-overflow-tooltip>\n          <template slot-scope=\"{ row }\">\n            <div v-if=\"row.Team_Names.length\">\n              <el-tag v-for=\"(item, index) in row.Team_Names.split(';')\" :key=\"index\" class=\"cs-tag\">\n                {{ item }}\n              </el-tag>\n            </div>\n            <div v-else>{{ '-' }}</div>\n          </template>\n        </el-table-column>\n        <el-table-column min-width=\"120px\" prop=\"Is_Enable\" label=\"是否启用\" align=\"center\">\n          <template slot-scope=\"{ row }\">\n            <el-tag v-if=\"row.Is_Enable\" type=\"success\">是</el-tag>\n            <el-tag v-else type=\"danger\">否</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column min-width=\"150px\" prop=\"Is_Nest\" label=\"是否套料工序\" align=\"center\">\n          <template slot-scope=\"{ row }\">\n            <div v-if=\"row.Bom_Level === 0\">\n              <el-tag v-if=\"row.Is_Nest\" type=\"success\">是</el-tag>\n              <el-tag v-else type=\"danger\">否</el-tag>\n            </div>\n            <div v-else>\n              {{ '-' }}\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column min-width=\"150px\" prop=\"Is_Cutting\" label=\"是否下料工序\" align=\"center\">\n          <template slot-scope=\"{ row }\">\n            <div v-if=\"row.Bom_Level===0\">\n              <el-tag v-if=\"row.Is_Cutting\" type=\"success\">是</el-tag>\n              <el-tag v-else type=\"danger\">否</el-tag>\n            </div>\n            <div v-else>\n              {{ '-' }}\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column min-width=\"120px\" prop=\"Device_Name\" label=\"关联设备\">\n          <template slot-scope=\"{ row }\">\n            {{ row.Device_Name || '-' }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"Remark\" label=\"备注\" width=\"300\">\n          <template slot-scope=\"{ row }\">\n            {{ row.Remark || '-' }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" width=\"170\" fixed=\"right\">\n          <template slot-scope=\"{ row }\">\n            <el-button type=\"text\" size=\"small\" @click=\"handleDialog('edit', row)\">编辑</el-button>\n            <el-button type=\"text\" size=\"small\" @click=\"handleOpenDevice(row)\">关联设备</el-button>\n            <el-button class=\"txt-red\" type=\"text\" size=\"small\" @click=\"handleDelete(row.Id)\">删除</el-button>\n\n            <!--<el-divider direction=\"vertical\" />\n             <el-button type=\"text\" size=\"small\" @click=\"handleManage(row)\"\n              >班组管理</el-button\n            > -->\n          </template>\n        </el-table-column>\n      </el-table>\n      <el-dialog\n        v-if=\"dialogVisible\"\n        v-dialog-drag\n        class=\"cs-dialog\"\n        :close-on-click-modal=\"false\"\n        :title=\"title\"\n        :visible.sync=\"dialogVisible\"\n        custom-class=\"dialogCustomClass\"\n        width=\"580px\"\n        top=\"5vh\"\n        @close=\"handleClose\"\n      >\n        <component\n          :is=\"currentComponent\"\n          ref=\"content\"\n          :row-info=\"rowInfo\"\n          :type=\"type\"\n          :level=\"level\"\n          :bom-list=\"bomList\"\n          :dialog-visible=\"dialogVisible\"\n          @close=\"handleClose\"\n          @refresh=\"fetchData\"\n        />\n      </el-dialog>\n      <el-dialog\n        v-dialog-drag\n        class=\"cs-dialog\"\n        title=\"关联设备\"\n        :close-on-click-modal=\"false\"\n        :visible.sync=\"dialogVisible1\"\n        custom-class=\"dialogCustomClass\"\n        width=\"86%\"\n        top=\"5vh\"\n        @close=\"handleClose1\"\n      >\n        <AssociatedDevice ref=\"Device\" :row-data=\"rowData\" @fetchData=\"fetchData\" />\n        <!-- <span slot=\"footer\" class=\"dialog-footer\">\n          <el-button @click=\"dialogVisible1 = false\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"dialogVisible1 = false\">确 定</el-button>\n        </span> -->\n      </el-dialog>\n    </div>\n  </div>\n</template>\n\n<script>\nimport TopHeader from '@/components/TopHeader'\nimport Add from './component/Add'\nimport ZClass from './component/Group'\nimport AssociatedDevice from './component/AssociatedDevice'\nimport RecognitionConfig from './component/RecognitionConfig'\nimport partRecognitionConfig from './component/partRecognitionConfig'\nimport compRecognitionConfig from './component/compRecognitionConfig'\nimport unitPartRecognitionConfig from './component/unitPartRecognitionConfig'\nimport PartTakeConfig from './component/PartTakeConfig'\nimport { GetProcessListBase, DeleteProcess } from '@/api/PRO/technology-lib'\nimport ElTableEmpty from '@/components/ElTableEmpty/index.vue'\nimport addRouterPage from '@/mixins/add-router-page'\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\n\nexport default {\n  name: 'PROProcessManagement',\n  components: {\n    ElTableEmpty,\n    TopHeader,\n    Add,\n    partRecognitionConfig,\n    compRecognitionConfig,\n    PartTakeConfig,\n    ZClass,\n    AssociatedDevice,\n    unitPartRecognitionConfig,\n    RecognitionConfig\n  },\n  mixins: [addRouterPage],\n  data() {\n    return {\n      level: 0,\n      bomList: [],\n      addPageArray: [\n        {\n          path: '/AssociatedDevice',\n          hidden: true,\n          component: () => import('@/views/PRO/process-settings/management/component/AssociatedDevice.vue'),\n          name: 'AssociatedDevice',\n          meta: { title: '关联设备' }\n        }\n\n      ],\n      tableData: [],\n      currentComponent: '',\n      title: '',\n      comName: '',\n      partName: '',\n      rowInfo: null,\n      rowData: {},\n      type: '',\n      pageLoading: false,\n      dialogVisible: false,\n      dialogVisible1: false,\n      unitPartList: [],\n      formInline: { name: '', code: '' }\n    }\n  },\n\n  mounted() {\n    this.getBOMInfo()\n    this.fetchData()\n  },\n  methods: {\n    async getBOMInfo() {\n      const { comName, partName, list } = await GetBOMInfo()\n      this.comName = comName\n      this.partName = partName\n      this.bomList = list\n      this.unitPartList = list.filter(item => item.Code !== '0' && item.Code !== '-1')\n    },\n    handleOpenDevice(row) {\n      this.rowData = row\n      this.dialogVisible1 = true\n      this.$nextTick(() => {\n        this.$refs.Device.clearSelec()\n      })\n      //  this.$router.push({ path: 'http://localhost:3000/produce/pro/nesting/index' })\n    },\n\n    fetchData() {\n      this.pageLoading = true\n      GetProcessListBase(this.formInline).then((res) => {\n        if (res.IsSucceed) {\n          this.tableData = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n        this.pageLoading = false\n        this.dialogVisible1 = false\n      })\n    },\n    handleClose() {\n      this.dialogVisible = false\n    },\n    handleClose1() {\n      this.dialogVisible1 = false\n    },\n    handleRecognitionConfig() {\n      this.title = `导入识别配置`\n      this.currentComponent = 'RecognitionConfig'\n      this.dialogVisible = true\n    },\n    handleUnitPartConfig(item) {\n      this.level = +item.Code\n      this.title = `${item.Display_Name}识别配置`\n      this.currentComponent = 'unitPartRecognitionConfig'\n      this.dialogVisible = true\n    },\n    handleConfig() {\n      this.title = `${this.partName}识别配置`\n      this.currentComponent = 'partRecognitionConfig'\n      this.dialogVisible = true\n    },\n    handleConfigComp() {\n      this.title = `${this.comName}识别配置`\n      this.currentComponent = 'compRecognitionConfig'\n      this.dialogVisible = true\n    },\n    handleTakeConfig() {\n      this.title = `${this.partName}领用配置`\n      this.currentComponent = 'PartTakeConfig'\n      this.dialogVisible = true\n    },\n    handleDialog(type, row) {\n      this.currentComponent = 'Add'\n      this.type = type\n      if (type === 'add') {\n        this.title = '新建'\n      } else {\n        this.title = '编辑'\n        this.rowInfo = row\n      }\n      this.dialogVisible = true\n    },\n    handleManage(row) {\n      this.currentComponent = 'ZClass'\n      this.title = '班组管理'\n      this.dialogVisible = true\n      this.$nextTick((_) => {\n        this.$refs.content.init(row)\n      })\n    },\n    handleDelete(processId) {\n      this.$confirm('是否删除当前工序?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DeleteProcess({\n            processId\n          }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                message: '删除成功',\n                type: 'success'\n              })\n              this.fetchData()\n            } else {\n              this.$message({\n                message: res.Message,\n                type: 'error'\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/mixin.scss\";\n.cs-z-page-main-content{\n  min-width: 1000px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding:  0;\n  margin-bottom: 8px;\n\n  .header-left {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    flex-wrap: wrap;\n  }\n\n  .header-right {\n    display: flex;\n    align-items: center;\n  }\n}\n\n.tb {\n  ::v-deep {\n    @include scrollBar;\n\n    &::-webkit-scrollbar {\n      width: 8px;\n    }\n  }\n}\n\n.cs-dialog {\n  ::v-deep {\n    .el-dialog__body {\n      overflow: hidden;\n    }\n  }\n}\n\n.cs-tb-icon {\n  vertical-align: middle;\n}\n\n.cs-tag {\n  &:nth-child(2n) {\n    margin-left: 4px;\n  }\n\n  &:nth-child(n + 3) {\n    margin-top: 4px;\n  }\n}\n</style>\n"]}]}
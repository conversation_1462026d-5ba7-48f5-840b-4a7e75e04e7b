{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\CombinationDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\CombinationDialog.vue", "mtime": 1757922150916}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IEFkZENoZWNrSXRlbUNvbWJpbmF0aW9uIH0gZnJvbSAnQC9hcGkvUFJPL2ZhY3RvcnljaGVjaycKaW1wb3J0IHsgRW50aXR5UXVhbGl0eUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJwoKaW1wb3J0IHsgR2V0Q2hlY2tUeXBlTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snCmltcG9ydCB7IEdldENoZWNrSXRlbUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJwppbXBvcnQgeyBHZXROb2RlTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snCmltcG9ydCB7IEdldENvbXBUeXBlVHJlZSB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snCmltcG9ydCB7CiAgR2V0RmFjdG9yeVByb2Zlc3Npb25hbEJ5Q29kZSwKICBHZXRNYXRlcmlhbFR5cGUKfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJwppbXBvcnQgeyBHZXRQYXJ0VHlwZVRyZWUgfSBmcm9tICdAL2FwaS9QUk8vcGFydFR5cGUnCgpleHBvcnQgZGVmYXVsdCB7CiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIG1vZGU6ICcnLCAvLyDljLrliIbpobnnm67lkozlt6XljoIKICAgICAgUHJvamVjdElkOiAnJywgLy8g6aG555uuSWQKICAgICAgQ2hlY2tfT2JqZWN0X0lkOiAnJywKICAgICAgY2hlY2tUeXBlOiB7fSwgLy8g5Yy65YiG5p6E5Lu244CB6Zu25Lu244CB54mp5paZCiAgICAgIGZvcm06IHsKICAgICAgICBPYmplY3RfVHlwZV9JZHM6IFtdCiAgICAgIH0sCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgQ2hlY2tfQ29udGVudDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOihqOWNlScsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXSwKICAgICAgICBFbGlnaWJpbGl0eV9Dcml0ZXJpYTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOihqOWNlScsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXSwKICAgICAgICBHcm91cF9OYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+35aGr5YaZ5a6M5pW06KGo5Y2VJywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICAgIENoZWNrX1R5cGU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgICAgXSwKICAgICAgICBRdWVzdGlvbmxhYl9JZHM6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgdGl0bGU6ICcnLAogICAgICBvcHRpb25zOiBbXSwKICAgICAgUHJvY2Vzc0Zsb3c6IFtdLAogICAgICBDaGVja1R5cGVMaXN0OiBbXSwgLy8g5qOA5p+l57G75Z6L5LiL5ouJCiAgICAgIENoZWNrSXRlbUxpc3Q6IFtdLCAvLyDmo4Dmn6XpobnkuIvmi4kKICAgICAgQ2hhbmdlX0NoZWNrX1R5cGU6IFtdLAogICAgICBRdWFsaXR5VHlwZUxpc3Q6IFsKICAgICAgICB7CiAgICAgICAgICBOYW1lOiAn6LSo6YePJywKICAgICAgICAgIElkOiAxCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBOYW1lOiAn5o6i5LykJywKICAgICAgICAgIElkOiAyCiAgICAgICAgfQogICAgICBdLCAvLyDotKjmo4DnsbvlnosKICAgICAgUHJvQ2F0ZWdvcnlMaXN0OiBbXSwgLy8g5LiT5Lia57G75Yir5LiL5ouJCiAgICAgIENoZWNrTm9kZUxpc3Q6IFtdLCAvLyDotKjmo4DoioLngrnkuIvmi4kKICAgICAgdmVyaWZpY2F0aW9uOiBmYWxzZSwKICAgICAgUHJvQ2F0ZWdvcnlDb2RlOiAnJywgLy8g5LiT5Lia57G75YirQ29kZQogICAgICBFbGlnaWJpbGl0eV9Dcml0ZXJpYTogJycsCiAgICAgIE9iamVjdFR5cGVMaXN0OiB7CiAgICAgICAgLy8g5a+56LGh57G75Z6LCiAgICAgICAgJ2NoZWNrLXN0cmljdGx5JzogdHJ1ZSwKICAgICAgICAnZGVmYXVsdC1leHBhbmQtYWxsJzogdHJ1ZSwKICAgICAgICBmaWx0ZXJhYmxlOiBmYWxzZSwKICAgICAgICBjbGlja1BhcmVudDogdHJ1ZSwKICAgICAgICBkYXRhOiBbXSwKICAgICAgICBwcm9wczogewogICAgICAgICAgY2hpbGRyZW46ICdDaGlsZHJlbicsCiAgICAgICAgICBsYWJlbDogJ0xhYmVsJywKICAgICAgICAgIHZhbHVlOiAnSWQnCiAgICAgICAgfQogICAgICB9LAogICAgICBJc2Rpc2FibGU6IGZhbHNlLAogICAgICB0eXBlQ29kZTogJycsCiAgICAgIHR5cGVJZDogJycsCiAgICAgIHBhcnRHcmFkZTogJycKICAgIH0KICB9LAogIHdhdGNoOiB7CiAgICBQcm9jZXNzRmxvdzogewogICAgICBoYW5kbGVyKG5ld05hbWUsIG9sZE5hbWUpIHsKICAgICAgICBjb25zb2xlLmxvZyhuZXdOYW1lKQogICAgICAgIHRoaXMuZm9ybS5RdWVzdGlvbmxhYl9JZHMgPSBbXQogICAgICAgIHRoaXMuUHJvY2Vzc0Zsb3cuZm9yRWFjaCgoaXRlbSkgPT4gewogICAgICAgICAgaWYgKAogICAgICAgICAgICBpdGVtLlF1ZXN0aW9ubGFiX0lkICYmCiAgICAgICAgICAgICF0aGlzLmZvcm0uUXVlc3Rpb25sYWJfSWRzLmluY2x1ZGVzKGl0ZW0uUXVlc3Rpb25sYWJfSWQpCiAgICAgICAgICApIHsKICAgICAgICAgICAgdGhpcy5mb3JtLlF1ZXN0aW9ubGFiX0lkcy5wdXNoKGl0ZW0uUXVlc3Rpb25sYWJfSWQpCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfSwKICAgICAgZGVlcDogdHJ1ZQogICAgfQogIH0sCiAgbW91bnRlZCgpIHt9LAogIG1ldGhvZHM6IHsKICAgIGFzeW5jIGluaXQodGl0bGUsIGNoZWNrVHlwZSwgZGF0YSkgewogICAgICB0aGlzLnBhcnRHcmFkZSA9IGNoZWNrVHlwZS5Db2RlCiAgICAgIHRoaXMuQ2hlY2tfT2JqZWN0X0lkID0gY2hlY2tUeXBlLklkCiAgICAgIHRoaXMuY2hlY2tUeXBlID0gY2hlY2tUeXBlCiAgICAgIHRoaXMudGl0bGUgPSB0aXRsZQogICAgICB0aGlzLmZvcm0uQ2hlY2tfT2JqZWN0X0lkID0gY2hlY2tUeXBlLklkCiAgICAgIHRoaXMuZm9ybS5Cb21fTGV2ZWwgPSBjaGVja1R5cGUuQ29kZQogICAgICBhd2FpdCB0aGlzLmdldFByb2Zlc3Npb25hbFR5cGUoKSAvLyDkuJPkuJrnsbvliKsKICAgICAgYXdhaXQgdGhpcy5nZXRDaGVja1R5cGVMaXN0KCkgLy8g5qOA5p+l57G75Z6LCiAgICAgIGF3YWl0IHRoaXMuZ2V0Q2hlY2tJdGVtTGlzdCgpCiAgICAgIGF3YWl0IHRoaXMuZ2V0Tm9kZUxpc3QoZGF0YSkgLy8g6LSo5qOA6IqC54K5CiAgICB9LAogICAgYXN5bmMgYWRkQ2hlY2tJdGVtQ29tYmluYXRpb24oKSB7CiAgICAgIGF3YWl0IEFkZENoZWNrSXRlbUNvbWJpbmF0aW9uKHsKICAgICAgICBHcm91cDogdGhpcy5mb3JtLAogICAgICAgIEl0ZW1zOiB0aGlzLlByb2Nlc3NGbG93CiAgICAgIH0pLnRoZW4oKHJlcykgPT4gewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICBtZXNzYWdlOiAn5L+d5a2Y5oiQ5YqfJwogICAgICAgICAgfSkKICAgICAgICAgIHRoaXMuJGVtaXQoJ3JlZnJlc2gnKQogICAgICAgICAgdGhpcy4kZW1pdCgnY2xvc2UnKQogICAgICAgICAgdGhpcy5kaWFsb2dEYXRhID0ge30KICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICByZW1vdmVUYWdGbihpZHMsIHRhZykgewogICAgICBjb25zb2xlLmxvZygnaWRzJywgaWRzKQogICAgICBjb25zb2xlLmxvZygndGFnJywgdGFnKQogICAgfSwKICAgIFNlbGVjdFR5cGUoaXRlbSkgewogICAgICBjb25zb2xlLmxvZygnaXRlbScsIGl0ZW0pCgogICAgICBpZiAoaXRlbS5sZW5ndGggPT09IDEpIHsKICAgICAgICB0aGlzLmZvcm0uQ2hlY2tfVHlwZSA9IGl0ZW1bMF0KICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmZvcm0uQ2hlY2tfVHlwZSA9IC0xCiAgICAgIH0KICAgICAgY29uc29sZS5sb2coJ3RoaXMuZm9ybS5DaGVja19UeXBlJywgdGhpcy5mb3JtLkNoZWNrX1R5cGUpCiAgICB9LAogICAgY2hhbmdlTm9kZSh2YWwpIHsKICAgICAgY29uc29sZS5sb2codmFsKQogICAgICBjb25zb2xlLmxvZyh0aGlzLkNoZWNrTm9kZUxpc3QpCiAgICAgIGlmICh2YWwpIHsKICAgICAgICB0aGlzLmZvcm0uQ2hlY2tfVHlwZSA9IHRoaXMuQ2hlY2tOb2RlTGlzdC5maW5kKCh2KSA9PiB7CiAgICAgICAgICByZXR1cm4gdi5JZCA9PT0gdmFsCiAgICAgICAgfSkuQ2hlY2tfVHlwZQogICAgICAgIC8vIOWkhOeQhui0qOajgOexu+Wei+aVsOaNrgoKICAgICAgICB0aGlzLkNoYW5nZV9DaGVja19UeXBlID0gW10KICAgICAgICBpZiAodGhpcy5mb3JtLkNoZWNrX1R5cGUgPT09IDEgfHwgdGhpcy5mb3JtLkNoZWNrX1R5cGUgPT09IDIpIHsKICAgICAgICAgIHRoaXMuSXNkaXNhYmxlID0gdHJ1ZQogICAgICAgICAgdGhpcy5DaGFuZ2VfQ2hlY2tfVHlwZS5wdXNoKHRoaXMuZm9ybS5DaGVja19UeXBlKQogICAgICAgIH0gZWxzZSBpZiAodGhpcy5mb3JtLkNoZWNrX1R5cGUgPT09IC0xKSB7CiAgICAgICAgICB0aGlzLklzZGlzYWJsZSA9IGZhbHNlIC8vIOi0qOajgOexu+Wei+WPr+e8lui+kQogICAgICAgICAgdGhpcy5DaGFuZ2VfQ2hlY2tfVHlwZSA9IFtdCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuQ2hhbmdlX0NoZWNrX1R5cGUgPSBbXQogICAgICAgICAgdGhpcy5Jc2Rpc2FibGUgPSBmYWxzZQogICAgICAgIH0KICAgICAgICBjb25zb2xlLmxvZygnIHRoaXMuSXNkaXNhYmxlJywgdGhpcy5Jc2Rpc2FibGUpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5DaGFuZ2VfQ2hlY2tfVHlwZSA9IFtdCiAgICAgIH0KICAgIH0sCiAgICBnZXRFbnRpdHlDaGVja1R5cGUoZGF0YSkgewogICAgICBjb25zb2xlLmxvZyhkYXRhKQogICAgICBFbnRpdHlRdWFsaXR5TGlzdCh7CiAgICAgICAgaWQ6IGRhdGEuSWQsCiAgICAgICAgY2hlY2tfb2JqZWN0X2lkOiB0aGlzLkNoZWNrX09iamVjdF9JZCwKICAgICAgICBCb21fTGV2ZWw6IHRoaXMuZm9ybS5Cb21fTGV2ZWwKICAgICAgfSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIHRoaXMuZm9ybSA9IHJlcy5EYXRhWzBdLkdyb3VwCiAgICAgICAgICBjb25zb2xlLmxvZyh0aGlzLmZvcm0uT2JqZWN0X1R5cGVfSWRzLCAnT2JqZWN0X1R5cGVfSWRzJykKCiAgICAgICAgICB0aGlzLlByb2Nlc3NGbG93ID0gcmVzLkRhdGFbMF0uSXRlbXMKICAgICAgICAgIHRoaXMuQ2hhbmdlX0NoZWNrX1R5cGUgPSBbXQogICAgICAgICAgLy8g5aSE55CG6LSo5qOA57G75Z6L5pWw5o2uCiAgICAgICAgICBpZiAodGhpcy5mb3JtLkNoZWNrX1R5cGUgPT09IDEgfHwgdGhpcy5mb3JtLkNoZWNrX1R5cGUgPT09IDIpIHsKICAgICAgICAgICAgdGhpcy5DaGFuZ2VfQ2hlY2tfVHlwZS5wdXNoKHRoaXMuZm9ybS5DaGVja19UeXBlKQogICAgICAgICAgICBpZiAocmVzLkRhdGFbMF0uQ2hlY2tOb2RlX1R5cGUgPT09IC0xKSB7CiAgICAgICAgICAgICAgdGhpcy5Jc2Rpc2FibGUgPSBmYWxzZQogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuSXNkaXNhYmxlID0gdHJ1ZQogICAgICAgICAgICB9CiAgICAgICAgICB9IGVsc2UgaWYgKHRoaXMuZm9ybS5DaGVja19UeXBlID09PSAtMSkgewogICAgICAgICAgICB0aGlzLkNoYW5nZV9DaGVja19UeXBlID0gWzEsIDJdCiAgICAgICAgICAgIHRoaXMuSXNkaXNhYmxlID0gdHJ1ZSAvLyDotKjmo4DnsbvlnovkuI3lj6/nvJbovpEKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuQ2hhbmdlX0NoZWNrX1R5cGUgPSBbXQogICAgICAgICAgICB0aGlzLklzZGlzYWJsZSA9IGZhbHNlCiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZQogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlU3VibWl0KGZvcm0pIHsKICAgICAgaWYgKHRoaXMuQ2hhbmdlX0NoZWNrX1R5cGUubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+mAieaLqeajgOafpeexu+WeiycKICAgICAgICB9KQogICAgICAgIHJldHVybgogICAgICB9CiAgICAgIGxldCB2ZXJpZmljYXRpb24gPSB0cnVlCiAgICAgIGlmICh0aGlzLlByb2Nlc3NGbG93Lmxlbmd0aCA9PT0gMCkgewogICAgICAgIHZlcmlmaWNhdGlvbiA9IGZhbHNlCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5Qcm9jZXNzRmxvdy5mb3JFYWNoKCh2YWwpID0+IHsKICAgICAgICAgIGZvciAoY29uc3Qga2V5IGluIHZhbCkgewogICAgICAgICAgICBpZiAodmFsW2tleV0gPT09ICcnKSB7CiAgICAgICAgICAgICAgdmVyaWZpY2F0aW9uID0gZmFsc2UKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgIH0KICAgICAgaWYgKCF2ZXJpZmljYXRpb24pIHsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICBtZXNzYWdlOiAn6K+35aGr5YaZ5a6M5pW05qOA5p+l6aG56K6+572u5YaF5a65JwogICAgICAgIH0pCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIGNvbnN0IHByb2Nlc3NGbG93Q29weSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy5Qcm9jZXNzRmxvdykpCiAgICAgIGNvbnN0IHByb2Nlc3NGbG93TmV3ID0gW10KICAgICAgcHJvY2Vzc0Zsb3dDb3B5LmZvckVhY2goKGl0ZW0pID0+IHsKICAgICAgICBjb25zdCBwcm9jZXNzRmxvd0pzb24gPSB7fQogICAgICAgIHByb2Nlc3NGbG93SnNvbi5DaGVja19JdGVtX0lkID0gaXRlbS5DaGVja19JdGVtX0lkCiAgICAgICAgcHJvY2Vzc0Zsb3dKc29uLkVsaWdpYmlsaXR5X0NyaXRlcmlhID0gaXRlbS5FbGlnaWJpbGl0eV9Dcml0ZXJpYQogICAgICAgIHByb2Nlc3NGbG93SnNvbi5RdWVzdGlvbmxhYl9JZCA9IGl0ZW0uUXVlc3Rpb25sYWJfSWQKICAgICAgICBwcm9jZXNzRmxvd05ldy5wdXNoKHByb2Nlc3NGbG93SnNvbikKICAgICAgfSkKICAgICAgY29uc3QgcHJvY2Vzc0Zsb3dUZW1wID0gcHJvY2Vzc0Zsb3dOZXcubWFwKChpdGVtKSA9PiB7CiAgICAgICAgcmV0dXJuIEpTT04uc3RyaW5naWZ5KGl0ZW0pCiAgICAgIH0pCiAgICAgIGlmIChuZXcgU2V0KHByb2Nlc3NGbG93VGVtcCkuc2l6ZSAhPT0gcHJvY2Vzc0Zsb3dUZW1wLmxlbmd0aCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgIG1lc3NhZ2U6ICfmo4Dmn6Xpobnorr7nva7lhoXlrrnkuI3og73lrozlhajnm7jlkIwnCiAgICAgICAgfSkKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgY29uc3QgcHJvY2Vzc0Zsb3dBcnIgPSB0aGlzLlByb2Nlc3NGbG93Lm1hcCgodikgPT4gewogICAgICAgIHJldHVybiB2LlF1ZXN0aW9ubGFiX0lkCiAgICAgIH0pCgogICAgICBjb25zdCBpc0luY2x1ZGVzID0gdGhpcy5mb3JtLlF1ZXN0aW9ubGFiX0lkcy5ldmVyeSgoaXRlbSkgPT4KICAgICAgICBwcm9jZXNzRmxvd0Fyci5pbmNsdWRlcyhpdGVtKQogICAgICApCiAgICAgIGlmICghaXNJbmNsdWRlcykgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgIG1lc3NhZ2U6ICfmo4Dmn6Xpobnorr7nva7lv4XpobvljIXlkKvlt7LpgInmo4Dmn6XnsbvlnosnCiAgICAgICAgfSkKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgdGhpcy4kcmVmc1tmb3JtXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHRoaXMuYWRkQ2hlY2tJdGVtQ29tYmluYXRpb24oKQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICByZXR1cm4gZmFsc2UKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgLy8g6I635Y+W5LiT5Lia57G75YirCiAgICBhc3luYyBnZXRQcm9mZXNzaW9uYWxUeXBlKCkgewogICAgICBjb25zdCBQbGF0Zm9ybSA9CiAgICAgICAgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ1BsYXRmb3JtJykgfHwgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ0N1clBsYXRmb3JtJykKICAgICAgaWYgKFBsYXRmb3JtID09PSAnMicpIHsKICAgICAgICB0aGlzLm1vZGUgPSAnZmFjdG9yeScKICAgICAgICBhd2FpdCBHZXRGYWN0b3J5UHJvZmVzc2lvbmFsQnlDb2RlKCkudGhlbigocmVzKSA9PiB7CiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICB0aGlzLlByb0NhdGVnb3J5TGlzdCA9IHJlcy5EYXRhCiAgICAgICAgICAgIGNvbnN0IHsKICAgICAgICAgICAgICBDb2RlLAogICAgICAgICAgICAgIElkCiAgICAgICAgICAgIH0gPSByZXMuRGF0YT8uZmluZChpdGVtID0+IGl0ZW0uQ29kZSA9PT0gJ1N0ZWVsJykgfHwge30KICAgICAgICAgICAgdGhpcy50eXBlQ29kZSA9IENvZGUKICAgICAgICAgICAgdGhpcy50eXBlSWQgPSBJZAogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZQogICAgICAgICAgICB9KQogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgIH0KCiAgICAgIC8vIOiOt+WPlumhueebri/lt6XljoJpZAogICAgICB0aGlzLlByb2plY3RJZCA9CiAgICAgICAgdGhpcy5tb2RlID09PSAnZmFjdG9yeScKICAgICAgICAgID8gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ0N1clJlZmVyZW5jZUlkJykKICAgICAgICAgIDogdGhpcy5Qcm9qZWN0SWQKICAgIH0sCgogICAgLy8g6I635Y+W5qOA5p+l57G75Z6L5LiL5ouJ5qGGCiAgICBhc3luYyBnZXRDaGVja1R5cGVMaXN0KCkgewogICAgICBhd2FpdCBHZXRDaGVja1R5cGVMaXN0KHsgY2hlY2tfb2JqZWN0X2lkOiB0aGlzLkNoZWNrX09iamVjdF9JZCwgQm9tX0xldmVsOiB0aGlzLmZvcm0uQm9tX0xldmVsIH0pLnRoZW4oCiAgICAgICAgKHJlcykgPT4gewogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgICAgdGhpcy5DaGVja1R5cGVMaXN0ID0gcmVzLkRhdGEKICAgICAgICAgICAgY29uc29sZS5sb2cocmVzLkRhdGEpCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlCiAgICAgICAgICAgIH0pCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICApCiAgICB9LAogICAgLy8g5qOA5p+l6aG55YaF5a65CiAgICBhc3luYyBnZXRDaGVja0l0ZW1MaXN0KCkgewogICAgICBhd2FpdCBHZXRDaGVja0l0ZW1MaXN0KHsgY2hlY2tfb2JqZWN0X2lkOiB0aGlzLkNoZWNrX09iamVjdF9JZCB9KS50aGVuKAogICAgICAgIChyZXMpID0+IHsKICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICAgIHRoaXMuQ2hlY2tJdGVtTGlzdCA9IHJlcy5EYXRhCiAgICAgICAgICAgIGNvbnNvbGUubG9nKHJlcy5EYXRhKQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZQogICAgICAgICAgICB9KQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgKQogICAgfSwKICAgIC8vIOmAmui/h+S4k+S4muexu+WIq+mAieaLqeWvueixoeexu+WeiwogICAgY2hhbmdlQ2F0ZWdvcnkodmFsKSB7CiAgICAgIHRoaXMuZm9ybS5PYmplY3RfVHlwZV9JZHMgPSBbXQogICAgICB0aGlzLmNob29zZVR5cGUodmFsKQogICAgfSwKICAgIC8vIOmAmui/h+S4k+S4muexu+WIq+mAieaLqeWvueixoeexu+WeiwogICAgY2hvb3NlVHlwZSh2YWwpIHsKICAgICAgY29uc29sZS5sb2codGhpcy5Qcm9DYXRlZ29yeUxpc3QpCiAgICAgIHRoaXMuUHJvQ2F0ZWdvcnlDb2RlID0gdGhpcy5Qcm9DYXRlZ29yeUxpc3QuZmluZCgodikgPT4gewogICAgICAgIHJldHVybiB2LklkID09PSB2YWwKICAgICAgfSkuQ29kZQogICAgICB0aGlzLmdldE9iamVjdFR5cGVMaXN0KHRoaXMuUHJvQ2F0ZWdvcnlDb2RlKSAvLyDlr7nosaHnsbvlnosKICAgIH0sCiAgICAvLyDpgInkuK3ooajmoLzlpJbmo4Dmn6XnsbvlnosKICAgIENoYW5nZUNoZWNrVHlwZSh2YWwpIHsKICAgICAgY29uc3QgYXJySnNvbiA9IE9iamVjdC5hc3NpZ24oW10sIHZhbCkKICAgICAgLy8gbGV0IGluZGV4ID0gYXJySnNvbi5pbmRleE9mKElzZXhpc3QpOwogICAgICAvLyB0aGlzLlByb2Nlc3NGbG93LnNwbGljZShpbmRleCwgMSk7CiAgICAgIGNvbnNvbGUubG9nKGFyckpzb24pCiAgICAgIGlmICh0aGlzLlByb2Nlc3NGbG93Lmxlbmd0aCA+IGFyckpzb24ubGVuZ3RoKSB7CiAgICAgICAgY29uc3QgYXJySnNvblRlbXAgPSBhcnJKc29uLm1hcCgoaXRlbSkgPT4gewogICAgICAgICAgY29uc3QgaXRlbUZpZWxkID0gewogICAgICAgICAgICBDaGVja19JdGVtX0lkOiAnJywKICAgICAgICAgICAgRWxpZ2liaWxpdHlfQ3JpdGVyaWE6ICcnLAogICAgICAgICAgICBRdWVzdGlvbmxhYl9JZDogaXRlbQogICAgICAgICAgfQogICAgICAgICAgdGhpcy5Qcm9jZXNzRmxvdy5mb3JFYWNoKChpdGVtcykgPT4gewogICAgICAgICAgICBpZiAoaXRlbXMuUXVlc3Rpb25sYWJfSWQgPT09IGl0ZW0pIHsKICAgICAgICAgICAgICBpdGVtRmllbGQuQ2hlY2tfSXRlbV9JZCA9IGl0ZW1zLkNoZWNrX0l0ZW1fSWQKICAgICAgICAgICAgICBpdGVtRmllbGQuRWxpZ2liaWxpdHlfQ3JpdGVyaWEgPSBpdGVtcy5FbGlnaWJpbGl0eV9Dcml0ZXJpYQogICAgICAgICAgICB9CiAgICAgICAgICB9KQoKICAgICAgICAgIHJldHVybiBpdGVtRmllbGQKICAgICAgICB9KQogICAgICAgIHRoaXMuUHJvY2Vzc0Zsb3cgPSBbXS5jb25jYXQoYXJySnNvblRlbXApCiAgICAgIH0gZWxzZSB7CiAgICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCBhcnJKc29uLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgICBjb25zdCBJc2V4aXN0ID0gdGhpcy5Qcm9jZXNzRmxvdy5maW5kKCh2KSA9PiB7CiAgICAgICAgICAgIHJldHVybiB2LlF1ZXN0aW9ubGFiX0lkID09PSBhcnJKc29uW2ldCiAgICAgICAgICB9KQogICAgICAgICAgaWYgKCFJc2V4aXN0KSB7CiAgICAgICAgICAgIHRoaXMuUHJvY2Vzc0Zsb3cucHVzaCh7CiAgICAgICAgICAgICAgUXVlc3Rpb25sYWJfSWQ6IGFyckpzb25baV0sCiAgICAgICAgICAgICAgQ2hlY2tfSXRlbV9JZDogJycsCiAgICAgICAgICAgICAgRWxpZ2liaWxpdHlfQ3JpdGVyaWE6ICcnCiAgICAgICAgICAgIH0pCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CgogICAgICBjb25zb2xlLmxvZygnQ2hhbmdlQ2hlY2tUeXBlKCknLCB0aGlzLlByb2Nlc3NGbG93KQogICAgfSwKCiAgICByZW1vdmVDaGVja1R5cGUodmFsKSB7CiAgICAgIGNvbnN0IElzZXhpc3QgPSB0aGlzLlByb2Nlc3NGbG93LmZpbmQoKHYpID0+IHsKICAgICAgICByZXR1cm4gdi5RdWVzdGlvbmxhYl9JZCA9PT0gdmFsCiAgICAgIH0pCiAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5Qcm9jZXNzRmxvdy5pbmRleE9mKElzZXhpc3QpCiAgICAgIGlmIChJc2V4aXN0KSB7CiAgICAgICAgdGhpcy5Qcm9jZXNzRmxvdy5zcGxpY2UoaW5kZXgsIDEpCiAgICAgIH0KICAgIH0sCiAgICAvLyDpgInkuK3mo4Dmn6XpobnlhoXlrrkKICAgIENoYW5nZUl0ZW0oZGF0YSwgaW5kZXgsIHJvdykgewogICAgICAvLyBjb25zb2xlLmxvZyhkYXRhKTsKICAgICAgLy8gY29uc29sZS5sb2coaW5kZXgpOwogICAgICAvLyBjb25zb2xlLmxvZyhyb3cpCiAgICAgIC8vIGNvbnNvbGUubG9nKHRoaXMuQ2hlY2tJdGVtTGlzdCk7CiAgICAgIHJvdy5FbGlnaWJpbGl0eV9Dcml0ZXJpYSA9ICcnCiAgICAgIHRoaXMuRWxpZ2liaWxpdHlfQ3JpdGVyaWEgPSAnJwogICAgICB0aGlzLkVsaWdpYmlsaXR5X0NyaXRlcmlhID0gdGhpcy5DaGVja0l0ZW1MaXN0LmZpbmQoKHYpID0+IHsKICAgICAgICByZXR1cm4gdi5JZCA9PT0gZGF0YQogICAgICB9KT8uRWxpZ2liaWxpdHlfQ3JpdGVyaWEKICAgICAgdGhpcy4kc2V0KAogICAgICAgIHRoaXMuUHJvY2Vzc0Zsb3dbaW5kZXhdLAogICAgICAgICdFbGlnaWJpbGl0eV9Dcml0ZXJpYScsCiAgICAgICAgdGhpcy5FbGlnaWJpbGl0eV9Dcml0ZXJpYQogICAgICApCiAgICAgIHRoaXMuJHNldCh0aGlzLlByb2Nlc3NGbG93W2luZGV4XSwgJ3NvcnQnLCBpbmRleCkKICAgICAgY29uc29sZS5sb2codGhpcy5Qcm9jZXNzRmxvdykKICAgIH0sCgogICAgYXN5bmMgZWRpdEhhbmRsZURhdGEoZGF0YSkgewogICAgICBpZiAodGhpcy50aXRsZSA9PT0gJ+e8lui+kScpIHsKICAgICAgICBjb25zb2xlLmxvZygnZGF0YScsIGRhdGEpCiAgICAgICAgdGhpcy5mb3JtLklkID0gZGF0YS5JZAogICAgICAgIHRoaXMuZ2V0RW50aXR5Q2hlY2tUeXBlKGRhdGEpCiAgICAgICAgYXdhaXQgdGhpcy5jaG9vc2VUeXBlKGRhdGEuUHJvX0NhdGVnb3J5X0lkKQogICAgICB9CiAgICB9LAoKICAgIC8vIOi0qOajgOiKgueCueS4i+aLieiPnOWNlQogICAgYXN5bmMgZ2V0Tm9kZUxpc3QoZGF0YSkgewogICAgICBhd2FpdCBHZXROb2RlTGlzdCh7IGNoZWNrX29iamVjdF9pZDogdGhpcy5DaGVja19PYmplY3RfSWQsIEJvbV9MZXZlbDogdGhpcy5mb3JtLkJvbV9MZXZlbCB9KS50aGVuKAogICAgICAgIChyZXMpID0+IHsKICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICAgIHRoaXMuQ2hlY2tOb2RlTGlzdCA9IHJlcy5EYXRhCiAgICAgICAgICAgIHRoaXMuZWRpdEhhbmRsZURhdGEoZGF0YSkKICAgICAgICAgICAgY29uc29sZS5sb2cocmVzLkRhdGEpCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlCiAgICAgICAgICAgIH0pCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICApCiAgICB9LAogICAgLy8g5a+56LGh57G75Z6L5LiL5ouJCiAgICBhc3luYyBnZXRPYmplY3RUeXBlTGlzdChjb2RlKSB7CiAgICAgIGlmICh0aGlzLmNoZWNrVHlwZS5EaXNwbGF5X05hbWUgPT09ICfnianmlpknKSB7CiAgICAgICAgR2V0TWF0ZXJpYWxUeXBlKHt9KS50aGVuKChyZXMpID0+IHsKICAgICAgICAgIHRoaXMuT2JqZWN0VHlwZUxpc3QgPSByZXMuRGF0YQogICAgICAgIH0pCiAgICAgIH0gZWxzZSB7CiAgICAgICAgbGV0IHJlcwogICAgICAgIGlmICh0aGlzLnBhcnRHcmFkZSA9PT0gJy0xJykgewogICAgICAgICAgcmVzID0gYXdhaXQgR2V0Q29tcFR5cGVUcmVlKHsgcHJvZmVzc2lvbmFsOiBjb2RlIH0pCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHJlcyA9IGF3YWl0IEdldFBhcnRUeXBlVHJlZSh7IHByb2Zlc3Npb25hbElkOiB0aGlzLnR5cGVJZCwgcGFydEdyYWRlOiB0aGlzLnBhcnRHcmFkZSB9KQogICAgICAgIH0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgdGhpcy5PYmplY3RUeXBlTGlzdC5kYXRhID0gcmVzLkRhdGEKICAgICAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7CiAgICAgICAgICAgIHRoaXMuJHJlZnMudHJlZVNlbGVjdE9iamVjdFR5cGUudHJlZURhdGFVcGRhdGVGdW4ocmVzLkRhdGEpCiAgICAgICAgICB9KQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pCiAgICAgICAgICB0aGlzLk9iamVjdFR5cGVMaXN0LmRhdGEgPSBbXQogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKF8pID0+IHsKICAgICAgICAgICAgdGhpcy4kcmVmcy50cmVlU2VsZWN0T2JqZWN0VHlwZS50cmVlRGF0YVVwZGF0ZUZ1bihbXSkKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9CiAgICB9LAoKICAgIC8vIOajgOafpemhueiuvue9rumDqOWIhgogICAgYWRkVGFibGVEYXRhKCkgewogICAgICB0aGlzLlByb2Nlc3NGbG93LnB1c2goewogICAgICAgIENoZWNrX0l0ZW1fSWQ6ICcnLAogICAgICAgIEVsaWdpYmlsaXR5X0NyaXRlcmlhOiAnJywKICAgICAgICBRdWVzdGlvbmxhYl9JZDogJycKICAgICAgfSkKICAgICAgY29uc29sZS5sb2coJ2FkZFRhYmxlRGF0YSgpJywgdGhpcy5Qcm9jZXNzRmxvdykKICAgIH0sCiAgICBkZWxldGVSb3coaW5kZXgsIHJvd3MpIHsKICAgICAgY29uc29sZS5sb2coaW5kZXgpCiAgICAgIHJvd3Muc3BsaWNlKGluZGV4LCAxKQogICAgICBjb25zb2xlLmxvZyh0aGlzLlByb2Nlc3NGbG93KQogICAgICBpZiAodGhpcy5Qcm9jZXNzRmxvdy5sZW5ndGggPiAwICYmIGluZGV4ICE9PSB0aGlzLlByb2Nlc3NGbG93Lmxlbmd0aCkgewogICAgICAgIHRoaXMuJHNldCh0aGlzLlByb2Nlc3NGbG93W2luZGV4XSwgJ3NvcnQnLCBpbmRleCkKICAgICAgfQogICAgfSwKICAgIG1vdmVVcHdhcmQocm93LCBpbmRleCkgewogICAgICBjb25zb2xlLmxvZyhpbmRleCkKICAgICAgY29uc3QgdXBEYXRhID0gdGhpcy5Qcm9jZXNzRmxvd1tpbmRleCAtIDFdCiAgICAgIHRoaXMuUHJvY2Vzc0Zsb3cuc3BsaWNlKGluZGV4IC0gMSwgMSkKICAgICAgdGhpcy5Qcm9jZXNzRmxvdy5zcGxpY2UoaW5kZXgsIDAsIHVwRGF0YSkKICAgICAgdGhpcy4kc2V0KHRoaXMuUHJvY2Vzc0Zsb3dbaW5kZXggLSAxXSwgJ3NvcnQnLCBpbmRleCAtIDEpCiAgICAgIHRoaXMuJHNldCh0aGlzLlByb2Nlc3NGbG93W2luZGV4XSwgJ3NvcnQnLCBpbmRleCkKICAgICAgY29uc29sZS5sb2codGhpcy5Qcm9jZXNzRmxvdykKICAgIH0sCiAgICBtb3ZlRG93bihyb3csIGluZGV4KSB7CiAgICAgIGNvbnNvbGUubG9nKGluZGV4KQogICAgICBjb25zdCBkb3duRGF0YSA9IHRoaXMuUHJvY2Vzc0Zsb3dbaW5kZXggKyAxXQogICAgICB0aGlzLlByb2Nlc3NGbG93LnNwbGljZShpbmRleCArIDEsIDEpCiAgICAgIHRoaXMuUHJvY2Vzc0Zsb3cuc3BsaWNlKGluZGV4LCAwLCBkb3duRGF0YSkKICAgICAgY29uc29sZS5sb2codGhpcy5Qcm9jZXNzRmxvdykKICAgICAgdGhpcy4kc2V0KHRoaXMuUHJvY2Vzc0Zsb3dbaW5kZXhdLCAnc29ydCcsIGluZGV4KQogICAgICB0aGlzLiRzZXQodGhpcy5Qcm9jZXNzRmxvd1tpbmRleCArIDFdLCAnc29ydCcsIGluZGV4ICsgMSkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["CombinationDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAk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file": "CombinationDialog.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup/components/Dialog", "sourcesContent": ["<template>\n  <div>\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"130px\">\n      <el-row>\n        <el-form-item label=\"检查项组合名称\" prop=\"Group_Name\">\n          <el-input v-model=\"form.Group_Name\" />\n        </el-form-item>\n\n        <el-form-item label=\"质检节点\" prop=\"Check_Node_Id\">\n          <el-select\n            v-model=\"form.Check_Node_Id\"\n            clearable\n            style=\"width: 100%\"\n            placeholder=\"请选择质检节点\"\n            @change=\"changeNode\"\n          >\n            <el-option\n              v-for=\"(item, index) in CheckNodeList\"\n              :key=\"index\"\n              :label=\"item.Display_Name\"\n              :value=\"item.Id\"\n            />\n          </el-select>\n        </el-form-item>\n\n        <el-form-item label=\"质检类型\" prop=\"Check_Type\">\n          <el-select\n            v-model=\"Change_Check_Type\"\n            clearable\n            multiple\n            :multiple-limit=\"1\"\n            style=\"width: 100%\"\n            :disabled=\"Isdisable\"\n            placeholder=\"请选择质检类型\"\n            @change=\"SelectType\"\n          >\n            <el-option\n              v-for=\"(item, index) in QualityTypeList\"\n              :key=\"index\"\n              :label=\"item.Name\"\n              :value=\"item.Id\"\n            />\n          </el-select>\n        </el-form-item>\n\n        <el-form-item label=\"专业类别\" prop=\"Pro_Category_Id\">\n          <el-select\n            v-model=\"form.Pro_Category_Id\"\n            clearable\n            style=\"width: 100%\"\n            placeholder=\"请选择专业类别\"\n            @change=\"changeCategory\"\n          >\n            <el-option\n              v-for=\"(item, index) in ProCategoryList\"\n              :key=\"index\"\n              :label=\"item.Name\"\n              :value=\"item.Id\"\n            />\n          </el-select>\n        </el-form-item>\n\n        <el-form-item label=\"检查类型\" prop=\"Questionlab_Ids\">\n          <el-select\n            v-model=\"form.Questionlab_Ids\"\n            style=\"width: 100%\"\n            multiple\n            placeholder=\"请选择检查类型\"\n            @change=\"ChangeCheckType\"\n            @remove-tag=\"removeCheckType\"\n          >\n            <el-option\n              v-for=\"(item, index) in CheckTypeList\"\n              :key=\"index\"\n              :label=\"item.Name\"\n              :value=\"item.Id\"\n            />\n          </el-select>\n        </el-form-item>\n\n        <el-form-item\n          label=\"产品类型\"\n          prop=\"Object_Type_Ids\"\n        >\n          <el-tree-select\n            ref=\"treeSelectObjectType\"\n            v-model=\"form.Object_Type_Ids\"\n            :disabled=\"!Boolean(form.Pro_Category_Id)\"\n            class=\"cs-tree-x\"\n            :tree-params=\"ObjectTypeList\"\n            value-key=\"Id\"\n            @removeTag=\"removeTagFn\"\n          />\n        </el-form-item>\n\n        <el-col :span=\"24\">\n          <h3>检查项设置</h3>\n          <el-form-item label=\"\" prop=\"\" class=\"checkItem\">\n            <el-table :data=\"ProcessFlow\" border style=\"width: 100%\">\n              <el-table-column prop=\"\" label=\"*检查类型\" align=\"center\">\n                <template slot-scope=\"{ row }\">\n                  <el-select\n                    v-model=\"row.Questionlab_Id\"\n                    style=\"width: 100%\"\n                    clearable\n                    placeholder=\"请选择\"\n                  >\n                    <el-option\n                      v-for=\"(item, index) in CheckTypeList\"\n                      :key=\"index\"\n                      :label=\"item.Name\"\n                      :value=\"item.Id\"\n                    />\n                  </el-select>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"\" label=\"*检查项内容\" align=\"center\">\n                <template slot-scope=\"{ row, $index }\">\n                  <el-select\n                    v-model=\"row.Check_Item_Id\"\n                    style=\"width: 100%\"\n                    clearable\n                    placeholder=\"请选择\"\n                    @change=\"ChangeItem($event, $index, row)\"\n                  >\n                    <el-option\n                      v-for=\"(item, index) in CheckItemList\"\n                      :key=\"index\"\n                      :label=\"item.Check_Content\"\n                      :value=\"item.Id\"\n                    />\n                  </el-select>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"\" label=\"*合格标准\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  <el-input\n                    v-model=\"scope.row.Eligibility_Criteria\"\n                    disabled\n                  />\n                </template>\n              </el-table-column>\n              <el-table-column\n                prop=\"address\"\n                label=\"操作\"\n                width=\"140\"\n                align=\"center\"\n              >\n                <template slot-scope=\"{ row, $index }\">\n                  <el-button\n                    type=\"text\"\n                    icon=\"el-icon-top\"\n                    :disabled=\"$index == 0\"\n                    @click=\"moveUpward(row, $index)\"\n                  />\n                  <el-button\n                    type=\"text\"\n                    icon=\"el-icon-bottom\"\n                    :disabled=\"$index == ProcessFlow.length - 1\"\n                    @click=\"moveDown(row, $index)\"\n                  />\n                  <el-button\n                    type=\"text\"\n                    icon=\"el-icon-delete\"\n                    style=\"color: #f56c6c\"\n                    @click.native.prevent=\"deleteRow($index, ProcessFlow)\"\n                  />\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-button\n            type=\"text\"\n            class=\"addcheckItem\"\n            @click=\"addTableData\"\n          >+ 新增检查项</el-button>\n        </el-col>\n        <el-col :span=\"24\" style=\"text-align: right\">\n          <el-form-item style=\"text-align: right\">\n            <el-button @click=\"$emit('close')\">关 闭</el-button>\n            <el-button\n              type=\"primary\"\n              @click=\"handleSubmit('form')\"\n            >确 定</el-button>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n  </div>\n</template>\n\n<script>\nimport { AddCheckItemCombination } from '@/api/PRO/factorycheck'\nimport { EntityQualityList } from '@/api/PRO/factorycheck'\n\nimport { GetCheckTypeList } from '@/api/PRO/factorycheck'\nimport { GetCheckItemList } from '@/api/PRO/factorycheck'\nimport { GetNodeList } from '@/api/PRO/factorycheck'\nimport { GetCompTypeTree } from '@/api/PRO/factorycheck'\nimport {\n  GetFactoryProfessionalByCode,\n  GetMaterialType\n} from '@/api/PRO/factorycheck'\nimport { GetPartTypeTree } from '@/api/PRO/partType'\n\nexport default {\n  data() {\n    return {\n      mode: '', // 区分项目和工厂\n      ProjectId: '', // 项目Id\n      Check_Object_Id: '',\n      checkType: {}, // 区分构件、零件、物料\n      form: {\n        Object_Type_Ids: []\n      },\n      rules: {\n        Check_Content: [\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\n        ],\n        Eligibility_Criteria: [\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\n        ],\n        Group_Name: [\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\n        ],\n        Check_Type: [\n          { required: true, message: '请填写完整表单', trigger: 'change' }\n        ],\n        Questionlab_Ids: [\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\n        ]\n      },\n      title: '',\n      options: [],\n      ProcessFlow: [],\n      CheckTypeList: [], // 检查类型下拉\n      CheckItemList: [], // 检查项下拉\n      Change_Check_Type: [],\n      QualityTypeList: [\n        {\n          Name: '质量',\n          Id: 1\n        },\n        {\n          Name: '探伤',\n          Id: 2\n        }\n      ], // 质检类型\n      ProCategoryList: [], // 专业类别下拉\n      CheckNodeList: [], // 质检节点下拉\n      verification: false,\n      ProCategoryCode: '', // 专业类别Code\n      Eligibility_Criteria: '',\n      ObjectTypeList: {\n        // 对象类型\n        'check-strictly': true,\n        'default-expand-all': true,\n        filterable: false,\n        clickParent: true,\n        data: [],\n        props: {\n          children: 'Children',\n          label: 'Label',\n          value: 'Id'\n        }\n      },\n      Isdisable: false,\n      typeCode: '',\n      typeId: '',\n      partGrade: ''\n    }\n  },\n  watch: {\n    ProcessFlow: {\n      handler(newName, oldName) {\n        console.log(newName)\n        this.form.Questionlab_Ids = []\n        this.ProcessFlow.forEach((item) => {\n          if (\n            item.Questionlab_Id &&\n            !this.form.Questionlab_Ids.includes(item.Questionlab_Id)\n          ) {\n            this.form.Questionlab_Ids.push(item.Questionlab_Id)\n          }\n        })\n      },\n      deep: true\n    }\n  },\n  mounted() {},\n  methods: {\n    async init(title, checkType, data) {\n      this.partGrade = checkType.Code\n      this.Check_Object_Id = checkType.Id\n      this.checkType = checkType\n      this.title = title\n      this.form.Check_Object_Id = checkType.Id\n      this.form.Bom_Level = checkType.Code\n      await this.getProfessionalType() // 专业类别\n      await this.getCheckTypeList() // 检查类型\n      await this.getCheckItemList()\n      await this.getNodeList(data) // 质检节点\n    },\n    async addCheckItemCombination() {\n      await AddCheckItemCombination({\n        Group: this.form,\n        Items: this.ProcessFlow\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            type: 'success',\n            message: '保存成功'\n          })\n          this.$emit('refresh')\n          this.$emit('close')\n          this.dialogData = {}\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    removeTagFn(ids, tag) {\n      console.log('ids', ids)\n      console.log('tag', tag)\n    },\n    SelectType(item) {\n      console.log('item', item)\n\n      if (item.length === 1) {\n        this.form.Check_Type = item[0]\n      } else {\n        this.form.Check_Type = -1\n      }\n      console.log('this.form.Check_Type', this.form.Check_Type)\n    },\n    changeNode(val) {\n      console.log(val)\n      console.log(this.CheckNodeList)\n      if (val) {\n        this.form.Check_Type = this.CheckNodeList.find((v) => {\n          return v.Id === val\n        }).Check_Type\n        // 处理质检类型数据\n\n        this.Change_Check_Type = []\n        if (this.form.Check_Type === 1 || this.form.Check_Type === 2) {\n          this.Isdisable = true\n          this.Change_Check_Type.push(this.form.Check_Type)\n        } else if (this.form.Check_Type === -1) {\n          this.Isdisable = false // 质检类型可编辑\n          this.Change_Check_Type = []\n        } else {\n          this.Change_Check_Type = []\n          this.Isdisable = false\n        }\n        console.log(' this.Isdisable', this.Isdisable)\n      } else {\n        this.Change_Check_Type = []\n      }\n    },\n    getEntityCheckType(data) {\n      console.log(data)\n      EntityQualityList({\n        id: data.Id,\n        check_object_id: this.Check_Object_Id,\n        Bom_Level: this.form.Bom_Level\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.form = res.Data[0].Group\n          console.log(this.form.Object_Type_Ids, 'Object_Type_Ids')\n\n          this.ProcessFlow = res.Data[0].Items\n          this.Change_Check_Type = []\n          // 处理质检类型数据\n          if (this.form.Check_Type === 1 || this.form.Check_Type === 2) {\n            this.Change_Check_Type.push(this.form.Check_Type)\n            if (res.Data[0].CheckNode_Type === -1) {\n              this.Isdisable = false\n            } else {\n              this.Isdisable = true\n            }\n          } else if (this.form.Check_Type === -1) {\n            this.Change_Check_Type = [1, 2]\n            this.Isdisable = true // 质检类型不可编辑\n          } else {\n            this.Change_Check_Type = []\n            this.Isdisable = false\n          }\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    handleSubmit(form) {\n      if (this.Change_Check_Type.length === 0) {\n        this.$message({\n          type: 'error',\n          message: '请选择检查类型'\n        })\n        return\n      }\n      let verification = true\n      if (this.ProcessFlow.length === 0) {\n        verification = false\n      } else {\n        this.ProcessFlow.forEach((val) => {\n          for (const key in val) {\n            if (val[key] === '') {\n              verification = false\n            }\n          }\n        })\n      }\n      if (!verification) {\n        this.$message({\n          type: 'error',\n          message: '请填写完整检查项设置内容'\n        })\n        return\n      }\n\n      const processFlowCopy = JSON.parse(JSON.stringify(this.ProcessFlow))\n      const processFlowNew = []\n      processFlowCopy.forEach((item) => {\n        const processFlowJson = {}\n        processFlowJson.Check_Item_Id = item.Check_Item_Id\n        processFlowJson.Eligibility_Criteria = item.Eligibility_Criteria\n        processFlowJson.Questionlab_Id = item.Questionlab_Id\n        processFlowNew.push(processFlowJson)\n      })\n      const processFlowTemp = processFlowNew.map((item) => {\n        return JSON.stringify(item)\n      })\n      if (new Set(processFlowTemp).size !== processFlowTemp.length) {\n        this.$message({\n          type: 'error',\n          message: '检查项设置内容不能完全相同'\n        })\n        return\n      }\n\n      const processFlowArr = this.ProcessFlow.map((v) => {\n        return v.Questionlab_Id\n      })\n\n      const isIncludes = this.form.Questionlab_Ids.every((item) =>\n        processFlowArr.includes(item)\n      )\n      if (!isIncludes) {\n        this.$message({\n          type: 'error',\n          message: '检查项设置必须包含已选检查类型'\n        })\n        return\n      }\n\n      this.$refs[form].validate((valid) => {\n        if (valid) {\n          this.addCheckItemCombination()\n        } else {\n          return false\n        }\n      })\n    },\n    // 获取专业类别\n    async getProfessionalType() {\n      const Platform =\n        localStorage.getItem('Platform') || localStorage.getItem('CurPlatform')\n      if (Platform === '2') {\n        this.mode = 'factory'\n        await GetFactoryProfessionalByCode().then((res) => {\n          if (res.IsSucceed) {\n            this.ProCategoryList = res.Data\n            const {\n              Code,\n              Id\n            } = res.Data?.find(item => item.Code === 'Steel') || {}\n            this.typeCode = Code\n            this.typeId = Id\n          } else {\n            this.$message({\n              type: 'error',\n              message: res.Message\n            })\n          }\n        })\n      }\n\n      // 获取项目/工厂id\n      this.ProjectId =\n        this.mode === 'factory'\n          ? localStorage.getItem('CurReferenceId')\n          : this.ProjectId\n    },\n\n    // 获取检查类型下拉框\n    async getCheckTypeList() {\n      await GetCheckTypeList({ check_object_id: this.Check_Object_Id, Bom_Level: this.form.Bom_Level }).then(\n        (res) => {\n          if (res.IsSucceed) {\n            this.CheckTypeList = res.Data\n            console.log(res.Data)\n          } else {\n            this.$message({\n              type: 'error',\n              message: res.Message\n            })\n          }\n        }\n      )\n    },\n    // 检查项内容\n    async getCheckItemList() {\n      await GetCheckItemList({ check_object_id: this.Check_Object_Id }).then(\n        (res) => {\n          if (res.IsSucceed) {\n            this.CheckItemList = res.Data\n            console.log(res.Data)\n          } else {\n            this.$message({\n              type: 'error',\n              message: res.Message\n            })\n          }\n        }\n      )\n    },\n    // 通过专业类别选择对象类型\n    changeCategory(val) {\n      this.form.Object_Type_Ids = []\n      this.chooseType(val)\n    },\n    // 通过专业类别选择对象类型\n    chooseType(val) {\n      console.log(this.ProCategoryList)\n      this.ProCategoryCode = this.ProCategoryList.find((v) => {\n        return v.Id === val\n      }).Code\n      this.getObjectTypeList(this.ProCategoryCode) // 对象类型\n    },\n    // 选中表格外检查类型\n    ChangeCheckType(val) {\n      const arrJson = Object.assign([], val)\n      // let index = arrJson.indexOf(Isexist);\n      // this.ProcessFlow.splice(index, 1);\n      console.log(arrJson)\n      if (this.ProcessFlow.length > arrJson.length) {\n        const arrJsonTemp = arrJson.map((item) => {\n          const itemField = {\n            Check_Item_Id: '',\n            Eligibility_Criteria: '',\n            Questionlab_Id: item\n          }\n          this.ProcessFlow.forEach((items) => {\n            if (items.Questionlab_Id === item) {\n              itemField.Check_Item_Id = items.Check_Item_Id\n              itemField.Eligibility_Criteria = items.Eligibility_Criteria\n            }\n          })\n\n          return itemField\n        })\n        this.ProcessFlow = [].concat(arrJsonTemp)\n      } else {\n        for (var i = 0; i < arrJson.length; i++) {\n          const Isexist = this.ProcessFlow.find((v) => {\n            return v.Questionlab_Id === arrJson[i]\n          })\n          if (!Isexist) {\n            this.ProcessFlow.push({\n              Questionlab_Id: arrJson[i],\n              Check_Item_Id: '',\n              Eligibility_Criteria: ''\n            })\n          }\n        }\n      }\n\n      console.log('ChangeCheckType()', this.ProcessFlow)\n    },\n\n    removeCheckType(val) {\n      const Isexist = this.ProcessFlow.find((v) => {\n        return v.Questionlab_Id === val\n      })\n      const index = this.ProcessFlow.indexOf(Isexist)\n      if (Isexist) {\n        this.ProcessFlow.splice(index, 1)\n      }\n    },\n    // 选中检查项内容\n    ChangeItem(data, index, row) {\n      // console.log(data);\n      // console.log(index);\n      // console.log(row)\n      // console.log(this.CheckItemList);\n      row.Eligibility_Criteria = ''\n      this.Eligibility_Criteria = ''\n      this.Eligibility_Criteria = this.CheckItemList.find((v) => {\n        return v.Id === data\n      })?.Eligibility_Criteria\n      this.$set(\n        this.ProcessFlow[index],\n        'Eligibility_Criteria',\n        this.Eligibility_Criteria\n      )\n      this.$set(this.ProcessFlow[index], 'sort', index)\n      console.log(this.ProcessFlow)\n    },\n\n    async editHandleData(data) {\n      if (this.title === '编辑') {\n        console.log('data', data)\n        this.form.Id = data.Id\n        this.getEntityCheckType(data)\n        await this.chooseType(data.Pro_Category_Id)\n      }\n    },\n\n    // 质检节点下拉菜单\n    async getNodeList(data) {\n      await GetNodeList({ check_object_id: this.Check_Object_Id, Bom_Level: this.form.Bom_Level }).then(\n        (res) => {\n          if (res.IsSucceed) {\n            this.CheckNodeList = res.Data\n            this.editHandleData(data)\n            console.log(res.Data)\n          } else {\n            this.$message({\n              type: 'error',\n              message: res.Message\n            })\n          }\n        }\n      )\n    },\n    // 对象类型下拉\n    async getObjectTypeList(code) {\n      if (this.checkType.Display_Name === '物料') {\n        GetMaterialType({}).then((res) => {\n          this.ObjectTypeList = res.Data\n        })\n      } else {\n        let res\n        if (this.partGrade === '-1') {\n          res = await GetCompTypeTree({ professional: code })\n        } else {\n          res = await GetPartTypeTree({ professionalId: this.typeId, partGrade: this.partGrade })\n        }\n        if (res.IsSucceed) {\n          this.ObjectTypeList.data = res.Data\n          this.$nextTick((_) => {\n            this.$refs.treeSelectObjectType.treeDataUpdateFun(res.Data)\n          })\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n          this.ObjectTypeList.data = []\n          this.$nextTick((_) => {\n            this.$refs.treeSelectObjectType.treeDataUpdateFun([])\n          })\n        }\n      }\n    },\n\n    // 检查项设置部分\n    addTableData() {\n      this.ProcessFlow.push({\n        Check_Item_Id: '',\n        Eligibility_Criteria: '',\n        Questionlab_Id: ''\n      })\n      console.log('addTableData()', this.ProcessFlow)\n    },\n    deleteRow(index, rows) {\n      console.log(index)\n      rows.splice(index, 1)\n      console.log(this.ProcessFlow)\n      if (this.ProcessFlow.length > 0 && index !== this.ProcessFlow.length) {\n        this.$set(this.ProcessFlow[index], 'sort', index)\n      }\n    },\n    moveUpward(row, index) {\n      console.log(index)\n      const upData = this.ProcessFlow[index - 1]\n      this.ProcessFlow.splice(index - 1, 1)\n      this.ProcessFlow.splice(index, 0, upData)\n      this.$set(this.ProcessFlow[index - 1], 'sort', index - 1)\n      this.$set(this.ProcessFlow[index], 'sort', index)\n      console.log(this.ProcessFlow)\n    },\n    moveDown(row, index) {\n      console.log(index)\n      const downData = this.ProcessFlow[index + 1]\n      this.ProcessFlow.splice(index + 1, 1)\n      this.ProcessFlow.splice(index, 0, downData)\n      console.log(this.ProcessFlow)\n      this.$set(this.ProcessFlow[index], 'sort', index)\n      this.$set(this.ProcessFlow[index + 1], 'sort', index + 1)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n::v-deep {\n  .checkItem {\n    width: 100%;\n    .el-form-item__content {\n      margin-left: 0 !important;\n    }\n  }\n  .addcheckItem {\n    font-size: 16px;\n    margin-bottom: 10px;\n  }\n}\n::v-deep .el-form-item {\n  display: inline-block;\n  .el-form-item__content {\n    & > .el-input {\n      width: 220px !important;\n    }\n    & > .el-select {\n      width: 220px !important;\n    }\n    .el-tree-select-input {\n      width: 220px !important;\n    }\n  }\n}\n</style>\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\CombinationDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\CombinationDialog.vue", "mtime": 1757923583405}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IEFkZENoZWNrSXRlbUNvbWJpbmF0aW9uIH0gZnJvbSAnQC9hcGkvUFJPL2ZhY3RvcnljaGVjaycKaW1wb3J0IHsgRW50aXR5UXVhbGl0eUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJwoKaW1wb3J0IHsgR2V0Q2hlY2tUeXBlTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snCmltcG9ydCB7IEdldENoZWNrSXRlbUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJwppbXBvcnQgeyBHZXROb2RlTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snCmltcG9ydCB7IEdldENvbXBUeXBlVHJlZSB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snCmltcG9ydCB7CiAgR2V0RmFjdG9yeVByb2Zlc3Npb25hbEJ5Q29kZSwKICBHZXRNYXRlcmlhbFR5cGUKfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJwppbXBvcnQgeyBHZXRQYXJ0VHlwZVRyZWUgfSBmcm9tICdAL2FwaS9QUk8vcGFydFR5cGUnCgpleHBvcnQgZGVmYXVsdCB7CiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIG1vZGU6ICcnLCAvLyDljLrliIbpobnnm67lkozlt6XljoIKICAgICAgUHJvamVjdElkOiAnJywgLy8g6aG555uuSWQKICAgICAgQ2hlY2tfT2JqZWN0X0lkOiAnJywKICAgICAgY2hlY2tUeXBlOiB7fSwgLy8g5Yy65YiG5p6E5Lu244CB6Zu25Lu244CB54mp5paZCiAgICAgIGZvcm06IHsKICAgICAgICBPYmplY3RfVHlwZV9JZHM6IFtdCiAgICAgIH0sCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgQ2hlY2tfQ29udGVudDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOihqOWNlScsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXSwKICAgICAgICBFbGlnaWJpbGl0eV9Dcml0ZXJpYTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOihqOWNlScsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXSwKICAgICAgICBHcm91cF9OYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+35aGr5YaZ5a6M5pW06KGo5Y2VJywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICAgIENoZWNrX1R5cGU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgICAgXSwKICAgICAgICBRdWVzdGlvbmxhYl9JZHM6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgdGl0bGU6ICcnLAogICAgICBvcHRpb25zOiBbXSwKICAgICAgUHJvY2Vzc0Zsb3c6IFtdLAogICAgICBDaGVja1R5cGVMaXN0OiBbXSwgLy8g5qOA5p+l57G75Z6L5LiL5ouJCiAgICAgIENoZWNrSXRlbUxpc3Q6IFtdLCAvLyDmo4Dmn6XpobnkuIvmi4kKICAgICAgQ2hhbmdlX0NoZWNrX1R5cGU6IFtdLAogICAgICBRdWFsaXR5VHlwZUxpc3Q6IFsKICAgICAgICB7CiAgICAgICAgICBOYW1lOiAn6LSo6YePJywKICAgICAgICAgIElkOiAxCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBOYW1lOiAn5o6i5LykJywKICAgICAgICAgIElkOiAyCiAgICAgICAgfQogICAgICBdLCAvLyDotKjmo4DnsbvlnosKICAgICAgUHJvQ2F0ZWdvcnlMaXN0OiBbXSwgLy8g5LiT5Lia57G75Yir5LiL5ouJCiAgICAgIENoZWNrTm9kZUxpc3Q6IFtdLCAvLyDotKjmo4DoioLngrnkuIvmi4kKICAgICAgdmVyaWZpY2F0aW9uOiBmYWxzZSwKICAgICAgUHJvQ2F0ZWdvcnlDb2RlOiAnJywgLy8g5LiT5Lia57G75YirQ29kZQogICAgICBFbGlnaWJpbGl0eV9Dcml0ZXJpYTogJycsCiAgICAgIE9iamVjdFR5cGVMaXN0OiB7CiAgICAgICAgLy8g5a+56LGh57G75Z6LCiAgICAgICAgJ2NoZWNrLXN0cmljdGx5JzogdHJ1ZSwKICAgICAgICAnZGVmYXVsdC1leHBhbmQtYWxsJzogdHJ1ZSwKICAgICAgICBmaWx0ZXJhYmxlOiBmYWxzZSwKICAgICAgICBjbGlja1BhcmVudDogdHJ1ZSwKICAgICAgICBkYXRhOiBbXSwKICAgICAgICBwcm9wczogewogICAgICAgICAgY2hpbGRyZW46ICdDaGlsZHJlbicsCiAgICAgICAgICBsYWJlbDogJ0xhYmVsJywKICAgICAgICAgIHZhbHVlOiAnSWQnCiAgICAgICAgfQogICAgICB9LAogICAgICBJc2Rpc2FibGU6IGZhbHNlLAogICAgICB0eXBlQ29kZTogJycsCiAgICAgIHR5cGVJZDogJycsCiAgICAgIHBhcnRHcmFkZTogJycKICAgIH0KICB9LAogIHdhdGNoOiB7CiAgICBQcm9jZXNzRmxvdzogewogICAgICBoYW5kbGVyKG5ld05hbWUsIG9sZE5hbWUpIHsKICAgICAgICBjb25zb2xlLmxvZyhuZXdOYW1lKQogICAgICAgIHRoaXMuZm9ybS5RdWVzdGlvbmxhYl9JZHMgPSBbXQogICAgICAgIHRoaXMuUHJvY2Vzc0Zsb3cuZm9yRWFjaCgoaXRlbSkgPT4gewogICAgICAgICAgaWYgKAogICAgICAgICAgICBpdGVtLlF1ZXN0aW9ubGFiX0lkICYmCiAgICAgICAgICAgICF0aGlzLmZvcm0uUXVlc3Rpb25sYWJfSWRzLmluY2x1ZGVzKGl0ZW0uUXVlc3Rpb25sYWJfSWQpCiAgICAgICAgICApIHsKICAgICAgICAgICAgdGhpcy5mb3JtLlF1ZXN0aW9ubGFiX0lkcy5wdXNoKGl0ZW0uUXVlc3Rpb25sYWJfSWQpCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfSwKICAgICAgZGVlcDogdHJ1ZQogICAgfQogIH0sCiAgbW91bnRlZCgpIHt9LAogIG1ldGhvZHM6IHsKICAgIGFzeW5jIGluaXQodGl0bGUsIGNoZWNrVHlwZSwgZGF0YSkgewogICAgICB0aGlzLnBhcnRHcmFkZSA9IGNoZWNrVHlwZS5Db2RlCiAgICAgIHRoaXMuQ2hlY2tfT2JqZWN0X0lkID0gY2hlY2tUeXBlLklkCiAgICAgIHRoaXMuY2hlY2tUeXBlID0gY2hlY2tUeXBlCiAgICAgIHRoaXMudGl0bGUgPSB0aXRsZQogICAgICB0aGlzLmZvcm0uQ2hlY2tfT2JqZWN0X0lkID0gY2hlY2tUeXBlLklkCiAgICAgIHRoaXMuZm9ybS5Cb21fTGV2ZWwgPSBjaGVja1R5cGUuQ29kZQogICAgICBhd2FpdCB0aGlzLmdldFByb2Zlc3Npb25hbFR5cGUoKSAvLyDkuJPkuJrnsbvliKsKICAgICAgYXdhaXQgdGhpcy5nZXRDaGVja1R5cGVMaXN0KCkgLy8g5qOA5p+l57G75Z6LCiAgICAgIGF3YWl0IHRoaXMuZ2V0Q2hlY2tJdGVtTGlzdCgpCiAgICAgIGF3YWl0IHRoaXMuZ2V0Tm9kZUxpc3QoZGF0YSkgLy8g6LSo5qOA6IqC54K5CiAgICB9LAogICAgYXN5bmMgYWRkQ2hlY2tJdGVtQ29tYmluYXRpb24oKSB7CiAgICAgIGF3YWl0IEFkZENoZWNrSXRlbUNvbWJpbmF0aW9uKHsKICAgICAgICBHcm91cDogdGhpcy5mb3JtLAogICAgICAgIEl0ZW1zOiB0aGlzLlByb2Nlc3NGbG93CiAgICAgIH0pLnRoZW4oKHJlcykgPT4gewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICBtZXNzYWdlOiAn5L+d5a2Y5oiQ5YqfJwogICAgICAgICAgfSkKICAgICAgICAgIHRoaXMuJGVtaXQoJ3JlZnJlc2gnKQogICAgICAgICAgdGhpcy4kZW1pdCgnY2xvc2UnKQogICAgICAgICAgdGhpcy5kaWFsb2dEYXRhID0ge30KICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICByZW1vdmVUYWdGbihpZHMsIHRhZykgewogICAgICBjb25zb2xlLmxvZygnaWRzJywgaWRzKQogICAgICBjb25zb2xlLmxvZygndGFnJywgdGFnKQogICAgfSwKICAgIFNlbGVjdFR5cGUoaXRlbSkgewogICAgICBjb25zb2xlLmxvZygnaXRlbScsIGl0ZW0pCgogICAgICBpZiAoaXRlbS5sZW5ndGggPT09IDEpIHsKICAgICAgICB0aGlzLmZvcm0uQ2hlY2tfVHlwZSA9IGl0ZW1bMF0KICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmZvcm0uQ2hlY2tfVHlwZSA9IC0xCiAgICAgIH0KICAgICAgY29uc29sZS5sb2coJ3RoaXMuZm9ybS5DaGVja19UeXBlJywgdGhpcy5mb3JtLkNoZWNrX1R5cGUpCiAgICB9LAogICAgY2hhbmdlTm9kZSh2YWwpIHsKICAgICAgY29uc29sZS5sb2codmFsKQogICAgICBjb25zb2xlLmxvZyh0aGlzLkNoZWNrTm9kZUxpc3QpCiAgICAgIGlmICh2YWwpIHsKICAgICAgICB0aGlzLmZvcm0uQ2hlY2tfVHlwZSA9IHRoaXMuQ2hlY2tOb2RlTGlzdC5maW5kKCh2KSA9PiB7CiAgICAgICAgICByZXR1cm4gdi5JZCA9PT0gdmFsCiAgICAgICAgfSkuQ2hlY2tfVHlwZQogICAgICAgIC8vIOWkhOeQhui0qOajgOexu+Wei+aVsOaNrgoKICAgICAgICB0aGlzLkNoYW5nZV9DaGVja19UeXBlID0gW10KICAgICAgICBpZiAodGhpcy5mb3JtLkNoZWNrX1R5cGUgPT09IDEgfHwgdGhpcy5mb3JtLkNoZWNrX1R5cGUgPT09IDIpIHsKICAgICAgICAgIHRoaXMuSXNkaXNhYmxlID0gdHJ1ZQogICAgICAgICAgdGhpcy5DaGFuZ2VfQ2hlY2tfVHlwZS5wdXNoKHRoaXMuZm9ybS5DaGVja19UeXBlKQogICAgICAgIH0gZWxzZSBpZiAodGhpcy5mb3JtLkNoZWNrX1R5cGUgPT09IC0xKSB7CiAgICAgICAgICB0aGlzLklzZGlzYWJsZSA9IGZhbHNlIC8vIOi0qOajgOexu+Wei+WPr+e8lui+kQogICAgICAgICAgdGhpcy5DaGFuZ2VfQ2hlY2tfVHlwZSA9IFtdCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuQ2hhbmdlX0NoZWNrX1R5cGUgPSBbXQogICAgICAgICAgdGhpcy5Jc2Rpc2FibGUgPSBmYWxzZQogICAgICAgIH0KICAgICAgICBjb25zb2xlLmxvZygnIHRoaXMuSXNkaXNhYmxlJywgdGhpcy5Jc2Rpc2FibGUpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5DaGFuZ2VfQ2hlY2tfVHlwZSA9IFtdCiAgICAgIH0KICAgIH0sCiAgICBnZXRFbnRpdHlDaGVja1R5cGUoZGF0YSkgewogICAgICBjb25zb2xlLmxvZyhkYXRhKQogICAgICBFbnRpdHlRdWFsaXR5TGlzdCh7CiAgICAgICAgaWQ6IGRhdGEuSWQsCiAgICAgICAgY2hlY2tfb2JqZWN0X2lkOiB0aGlzLkNoZWNrX09iamVjdF9JZAogICAgICB9KS50aGVuKChyZXMpID0+IHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgdGhpcy5mb3JtID0gcmVzLkRhdGFbMF0uR3JvdXAKICAgICAgICAgIGNvbnNvbGUubG9nKHRoaXMuZm9ybS5PYmplY3RfVHlwZV9JZHMsICdPYmplY3RfVHlwZV9JZHMnKQoKICAgICAgICAgIHRoaXMuUHJvY2Vzc0Zsb3cgPSByZXMuRGF0YVswXS5JdGVtcwogICAgICAgICAgdGhpcy5DaGFuZ2VfQ2hlY2tfVHlwZSA9IFtdCiAgICAgICAgICAvLyDlpITnkIbotKjmo4DnsbvlnovmlbDmja4KICAgICAgICAgIGlmICh0aGlzLmZvcm0uQ2hlY2tfVHlwZSA9PT0gMSB8fCB0aGlzLmZvcm0uQ2hlY2tfVHlwZSA9PT0gMikgewogICAgICAgICAgICB0aGlzLkNoYW5nZV9DaGVja19UeXBlLnB1c2godGhpcy5mb3JtLkNoZWNrX1R5cGUpCiAgICAgICAgICAgIGlmIChyZXMuRGF0YVswXS5DaGVja05vZGVfVHlwZSA9PT0gLTEpIHsKICAgICAgICAgICAgICB0aGlzLklzZGlzYWJsZSA9IGZhbHNlCiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgdGhpcy5Jc2Rpc2FibGUgPSB0cnVlCiAgICAgICAgICAgIH0KICAgICAgICAgIH0gZWxzZSBpZiAodGhpcy5mb3JtLkNoZWNrX1R5cGUgPT09IC0xKSB7CiAgICAgICAgICAgIHRoaXMuQ2hhbmdlX0NoZWNrX1R5cGUgPSBbMSwgMl0KICAgICAgICAgICAgdGhpcy5Jc2Rpc2FibGUgPSB0cnVlIC8vIOi0qOajgOexu+Wei+S4jeWPr+e8lui+kQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy5DaGFuZ2VfQ2hlY2tfVHlwZSA9IFtdCiAgICAgICAgICAgIHRoaXMuSXNkaXNhYmxlID0gZmFsc2UKICAgICAgICAgIH0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICBoYW5kbGVTdWJtaXQoZm9ybSkgewogICAgICBpZiAodGhpcy5DaGFuZ2VfQ2hlY2tfVHlwZS5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36YCJ5oup5qOA5p+l57G75Z6LJwogICAgICAgIH0pCiAgICAgICAgcmV0dXJuCiAgICAgIH0KICAgICAgbGV0IHZlcmlmaWNhdGlvbiA9IHRydWUKICAgICAgaWYgKHRoaXMuUHJvY2Vzc0Zsb3cubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdmVyaWZpY2F0aW9uID0gZmFsc2UKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLlByb2Nlc3NGbG93LmZvckVhY2goKHZhbCkgPT4gewogICAgICAgICAgZm9yIChjb25zdCBrZXkgaW4gdmFsKSB7CiAgICAgICAgICAgIGlmICh2YWxba2V5XSA9PT0gJycpIHsKICAgICAgICAgICAgICB2ZXJpZmljYXRpb24gPSBmYWxzZQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfQogICAgICBpZiAoIXZlcmlmaWNhdGlvbikgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTmo4Dmn6Xpobnorr7nva7lhoXlrrknCiAgICAgICAgfSkKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgY29uc3QgcHJvY2Vzc0Zsb3dDb3B5ID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLlByb2Nlc3NGbG93KSkKICAgICAgY29uc3QgcHJvY2Vzc0Zsb3dOZXcgPSBbXQogICAgICBwcm9jZXNzRmxvd0NvcHkuZm9yRWFjaCgoaXRlbSkgPT4gewogICAgICAgIGNvbnN0IHByb2Nlc3NGbG93SnNvbiA9IHt9CiAgICAgICAgcHJvY2Vzc0Zsb3dKc29uLkNoZWNrX0l0ZW1fSWQgPSBpdGVtLkNoZWNrX0l0ZW1fSWQKICAgICAgICBwcm9jZXNzRmxvd0pzb24uRWxpZ2liaWxpdHlfQ3JpdGVyaWEgPSBpdGVtLkVsaWdpYmlsaXR5X0NyaXRlcmlhCiAgICAgICAgcHJvY2Vzc0Zsb3dKc29uLlF1ZXN0aW9ubGFiX0lkID0gaXRlbS5RdWVzdGlvbmxhYl9JZAogICAgICAgIHByb2Nlc3NGbG93TmV3LnB1c2gocHJvY2Vzc0Zsb3dKc29uKQogICAgICB9KQogICAgICBjb25zdCBwcm9jZXNzRmxvd1RlbXAgPSBwcm9jZXNzRmxvd05ldy5tYXAoKGl0ZW0pID0+IHsKICAgICAgICByZXR1cm4gSlNPTi5zdHJpbmdpZnkoaXRlbSkKICAgICAgfSkKICAgICAgaWYgKG5ldyBTZXQocHJvY2Vzc0Zsb3dUZW1wKS5zaXplICE9PSBwcm9jZXNzRmxvd1RlbXAubGVuZ3RoKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgbWVzc2FnZTogJ+ajgOafpemhueiuvue9ruWGheWuueS4jeiDveWujOWFqOebuOWQjCcKICAgICAgICB9KQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICBjb25zdCBwcm9jZXNzRmxvd0FyciA9IHRoaXMuUHJvY2Vzc0Zsb3cubWFwKCh2KSA9PiB7CiAgICAgICAgcmV0dXJuIHYuUXVlc3Rpb25sYWJfSWQKICAgICAgfSkKCiAgICAgIGNvbnN0IGlzSW5jbHVkZXMgPSB0aGlzLmZvcm0uUXVlc3Rpb25sYWJfSWRzLmV2ZXJ5KChpdGVtKSA9PgogICAgICAgIHByb2Nlc3NGbG93QXJyLmluY2x1ZGVzKGl0ZW0pCiAgICAgICkKICAgICAgaWYgKCFpc0luY2x1ZGVzKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgbWVzc2FnZTogJ+ajgOafpemhueiuvue9ruW/hemhu+WMheWQq+W3sumAieajgOafpeexu+WeiycKICAgICAgICB9KQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICB0aGlzLiRyZWZzW2Zvcm1dLnZhbGlkYXRlKCh2YWxpZCkgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgdGhpcy5hZGRDaGVja0l0ZW1Db21iaW5hdGlvbigpCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHJldHVybiBmYWxzZQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICAvLyDojrflj5bkuJPkuJrnsbvliKsKICAgIGFzeW5jIGdldFByb2Zlc3Npb25hbFR5cGUoKSB7CiAgICAgIGNvbnN0IFBsYXRmb3JtID0KICAgICAgICBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnUGxhdGZvcm0nKSB8fCBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnQ3VyUGxhdGZvcm0nKQogICAgICBpZiAoUGxhdGZvcm0gPT09ICcyJykgewogICAgICAgIHRoaXMubW9kZSA9ICdmYWN0b3J5JwogICAgICAgIGF3YWl0IEdldEZhY3RvcnlQcm9mZXNzaW9uYWxCeUNvZGUoKS50aGVuKChyZXMpID0+IHsKICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICAgIHRoaXMuUHJvQ2F0ZWdvcnlMaXN0ID0gcmVzLkRhdGEKICAgICAgICAgICAgY29uc3QgewogICAgICAgICAgICAgIENvZGUsCiAgICAgICAgICAgICAgSWQKICAgICAgICAgICAgfSA9IHJlcy5EYXRhPy5maW5kKGl0ZW0gPT4gaXRlbS5Db2RlID09PSAnU3RlZWwnKSB8fCB7fQogICAgICAgICAgICB0aGlzLnR5cGVDb2RlID0gQ29kZQogICAgICAgICAgICB0aGlzLnR5cGVJZCA9IElkCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlCiAgICAgICAgICAgIH0pCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfQoKICAgICAgLy8g6I635Y+W6aG555uuL+W3peWOgmlkCiAgICAgIHRoaXMuUHJvamVjdElkID0KICAgICAgICB0aGlzLm1vZGUgPT09ICdmYWN0b3J5JwogICAgICAgICAgPyBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnQ3VyUmVmZXJlbmNlSWQnKQogICAgICAgICAgOiB0aGlzLlByb2plY3RJZAogICAgfSwKCiAgICAvLyDojrflj5bmo4Dmn6XnsbvlnovkuIvmi4nmoYYKICAgIGFzeW5jIGdldENoZWNrVHlwZUxpc3QoKSB7CiAgICAgIGF3YWl0IEdldENoZWNrVHlwZUxpc3QoeyBjaGVja19vYmplY3RfaWQ6IHRoaXMuQ2hlY2tfT2JqZWN0X0lkLCBCb21fTGV2ZWw6IHRoaXMuZm9ybS5Cb21fTGV2ZWwgfSkudGhlbigKICAgICAgICAocmVzKSA9PiB7CiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICB0aGlzLkNoZWNrVHlwZUxpc3QgPSByZXMuRGF0YQogICAgICAgICAgICBjb25zb2xlLmxvZyhyZXMuRGF0YSkKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UKICAgICAgICAgICAgfSkKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgICkKICAgIH0sCiAgICAvLyDmo4Dmn6XpobnlhoXlrrkKICAgIGFzeW5jIGdldENoZWNrSXRlbUxpc3QoKSB7CiAgICAgIGF3YWl0IEdldENoZWNrSXRlbUxpc3QoeyBjaGVja19vYmplY3RfaWQ6IHRoaXMuQ2hlY2tfT2JqZWN0X0lkIH0pLnRoZW4oCiAgICAgICAgKHJlcykgPT4gewogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgICAgdGhpcy5DaGVja0l0ZW1MaXN0ID0gcmVzLkRhdGEKICAgICAgICAgICAgY29uc29sZS5sb2cocmVzLkRhdGEpCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlCiAgICAgICAgICAgIH0pCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICApCiAgICB9LAogICAgLy8g6YCa6L+H5LiT5Lia57G75Yir6YCJ5oup5a+56LGh57G75Z6LCiAgICBjaGFuZ2VDYXRlZ29yeSh2YWwpIHsKICAgICAgdGhpcy5mb3JtLk9iamVjdF9UeXBlX0lkcyA9IFtdCiAgICAgIHRoaXMuY2hvb3NlVHlwZSh2YWwpCiAgICB9LAogICAgLy8g6YCa6L+H5LiT5Lia57G75Yir6YCJ5oup5a+56LGh57G75Z6LCiAgICBjaG9vc2VUeXBlKHZhbCkgewogICAgICBjb25zb2xlLmxvZyh0aGlzLlByb0NhdGVnb3J5TGlzdCkKICAgICAgdGhpcy5Qcm9DYXRlZ29yeUNvZGUgPSB0aGlzLlByb0NhdGVnb3J5TGlzdC5maW5kKCh2KSA9PiB7CiAgICAgICAgcmV0dXJuIHYuSWQgPT09IHZhbAogICAgICB9KS5Db2RlCiAgICAgIHRoaXMuZ2V0T2JqZWN0VHlwZUxpc3QodGhpcy5Qcm9DYXRlZ29yeUNvZGUpIC8vIOWvueixoeexu+WeiwogICAgfSwKICAgIC8vIOmAieS4reihqOagvOWkluajgOafpeexu+WeiwogICAgQ2hhbmdlQ2hlY2tUeXBlKHZhbCkgewogICAgICBjb25zdCBhcnJKc29uID0gT2JqZWN0LmFzc2lnbihbXSwgdmFsKQogICAgICAvLyBsZXQgaW5kZXggPSBhcnJKc29uLmluZGV4T2YoSXNleGlzdCk7CiAgICAgIC8vIHRoaXMuUHJvY2Vzc0Zsb3cuc3BsaWNlKGluZGV4LCAxKTsKICAgICAgY29uc29sZS5sb2coYXJySnNvbikKICAgICAgaWYgKHRoaXMuUHJvY2Vzc0Zsb3cubGVuZ3RoID4gYXJySnNvbi5sZW5ndGgpIHsKICAgICAgICBjb25zdCBhcnJKc29uVGVtcCA9IGFyckpzb24ubWFwKChpdGVtKSA9PiB7CiAgICAgICAgICBjb25zdCBpdGVtRmllbGQgPSB7CiAgICAgICAgICAgIENoZWNrX0l0ZW1fSWQ6ICcnLAogICAgICAgICAgICBFbGlnaWJpbGl0eV9Dcml0ZXJpYTogJycsCiAgICAgICAgICAgIFF1ZXN0aW9ubGFiX0lkOiBpdGVtCiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLlByb2Nlc3NGbG93LmZvckVhY2goKGl0ZW1zKSA9PiB7CiAgICAgICAgICAgIGlmIChpdGVtcy5RdWVzdGlvbmxhYl9JZCA9PT0gaXRlbSkgewogICAgICAgICAgICAgIGl0ZW1GaWVsZC5DaGVja19JdGVtX0lkID0gaXRlbXMuQ2hlY2tfSXRlbV9JZAogICAgICAgICAgICAgIGl0ZW1GaWVsZC5FbGlnaWJpbGl0eV9Dcml0ZXJpYSA9IGl0ZW1zLkVsaWdpYmlsaXR5X0NyaXRlcmlhCiAgICAgICAgICAgIH0KICAgICAgICAgIH0pCgogICAgICAgICAgcmV0dXJuIGl0ZW1GaWVsZAogICAgICAgIH0pCiAgICAgICAgdGhpcy5Qcm9jZXNzRmxvdyA9IFtdLmNvbmNhdChhcnJKc29uVGVtcCkKICAgICAgfSBlbHNlIHsKICAgICAgICBmb3IgKHZhciBpID0gMDsgaSA8IGFyckpzb24ubGVuZ3RoOyBpKyspIHsKICAgICAgICAgIGNvbnN0IElzZXhpc3QgPSB0aGlzLlByb2Nlc3NGbG93LmZpbmQoKHYpID0+IHsKICAgICAgICAgICAgcmV0dXJuIHYuUXVlc3Rpb25sYWJfSWQgPT09IGFyckpzb25baV0KICAgICAgICAgIH0pCiAgICAgICAgICBpZiAoIUlzZXhpc3QpIHsKICAgICAgICAgICAgdGhpcy5Qcm9jZXNzRmxvdy5wdXNoKHsKICAgICAgICAgICAgICBRdWVzdGlvbmxhYl9JZDogYXJySnNvbltpXSwKICAgICAgICAgICAgICBDaGVja19JdGVtX0lkOiAnJywKICAgICAgICAgICAgICBFbGlnaWJpbGl0eV9Dcml0ZXJpYTogJycKICAgICAgICAgICAgfSkKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KCiAgICAgIGNvbnNvbGUubG9nKCdDaGFuZ2VDaGVja1R5cGUoKScsIHRoaXMuUHJvY2Vzc0Zsb3cpCiAgICB9LAoKICAgIHJlbW92ZUNoZWNrVHlwZSh2YWwpIHsKICAgICAgY29uc3QgSXNleGlzdCA9IHRoaXMuUHJvY2Vzc0Zsb3cuZmluZCgodikgPT4gewogICAgICAgIHJldHVybiB2LlF1ZXN0aW9ubGFiX0lkID09PSB2YWwKICAgICAgfSkKICAgICAgY29uc3QgaW5kZXggPSB0aGlzLlByb2Nlc3NGbG93LmluZGV4T2YoSXNleGlzdCkKICAgICAgaWYgKElzZXhpc3QpIHsKICAgICAgICB0aGlzLlByb2Nlc3NGbG93LnNwbGljZShpbmRleCwgMSkKICAgICAgfQogICAgfSwKICAgIC8vIOmAieS4reajgOafpemhueWGheWuuQogICAgQ2hhbmdlSXRlbShkYXRhLCBpbmRleCwgcm93KSB7CiAgICAgIC8vIGNvbnNvbGUubG9nKGRhdGEpOwogICAgICAvLyBjb25zb2xlLmxvZyhpbmRleCk7CiAgICAgIC8vIGNvbnNvbGUubG9nKHJvdykKICAgICAgLy8gY29uc29sZS5sb2codGhpcy5DaGVja0l0ZW1MaXN0KTsKICAgICAgcm93LkVsaWdpYmlsaXR5X0NyaXRlcmlhID0gJycKICAgICAgdGhpcy5FbGlnaWJpbGl0eV9Dcml0ZXJpYSA9ICcnCiAgICAgIHRoaXMuRWxpZ2liaWxpdHlfQ3JpdGVyaWEgPSB0aGlzLkNoZWNrSXRlbUxpc3QuZmluZCgodikgPT4gewogICAgICAgIHJldHVybiB2LklkID09PSBkYXRhCiAgICAgIH0pPy5FbGlnaWJpbGl0eV9Dcml0ZXJpYQogICAgICB0aGlzLiRzZXQoCiAgICAgICAgdGhpcy5Qcm9jZXNzRmxvd1tpbmRleF0sCiAgICAgICAgJ0VsaWdpYmlsaXR5X0NyaXRlcmlhJywKICAgICAgICB0aGlzLkVsaWdpYmlsaXR5X0NyaXRlcmlhCiAgICAgICkKICAgICAgdGhpcy4kc2V0KHRoaXMuUHJvY2Vzc0Zsb3dbaW5kZXhdLCAnc29ydCcsIGluZGV4KQogICAgICBjb25zb2xlLmxvZyh0aGlzLlByb2Nlc3NGbG93KQogICAgfSwKCiAgICBhc3luYyBlZGl0SGFuZGxlRGF0YShkYXRhKSB7CiAgICAgIGlmICh0aGlzLnRpdGxlID09PSAn57yW6L6RJykgewogICAgICAgIGNvbnNvbGUubG9nKCdkYXRhJywgZGF0YSkKICAgICAgICB0aGlzLmZvcm0uSWQgPSBkYXRhLklkCiAgICAgICAgdGhpcy5nZXRFbnRpdHlDaGVja1R5cGUoZGF0YSkKICAgICAgICBhd2FpdCB0aGlzLmNob29zZVR5cGUoZGF0YS5Qcm9fQ2F0ZWdvcnlfSWQpCiAgICAgIH0KICAgIH0sCgogICAgLy8g6LSo5qOA6IqC54K55LiL5ouJ6I+c5Y2VCiAgICBhc3luYyBnZXROb2RlTGlzdChkYXRhKSB7CiAgICAgIGF3YWl0IEdldE5vZGVMaXN0KHsgY2hlY2tfb2JqZWN0X2lkOiB0aGlzLkNoZWNrX09iamVjdF9JZCwgQm9tX0xldmVsOiB0aGlzLmZvcm0uQm9tX0xldmVsIH0pLnRoZW4oCiAgICAgICAgKHJlcykgPT4gewogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgICAgdGhpcy5DaGVja05vZGVMaXN0ID0gcmVzLkRhdGEKICAgICAgICAgICAgdGhpcy5lZGl0SGFuZGxlRGF0YShkYXRhKQogICAgICAgICAgICBjb25zb2xlLmxvZyhyZXMuRGF0YSkKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UKICAgICAgICAgICAgfSkKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgICkKICAgIH0sCiAgICAvLyDlr7nosaHnsbvlnovkuIvmi4kKICAgIGFzeW5jIGdldE9iamVjdFR5cGVMaXN0KGNvZGUpIHsKICAgICAgaWYgKHRoaXMuY2hlY2tUeXBlLkRpc3BsYXlfTmFtZSA9PT0gJ+eJqeaWmScpIHsKICAgICAgICBHZXRNYXRlcmlhbFR5cGUoe30pLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgdGhpcy5PYmplY3RUeXBlTGlzdCA9IHJlcy5EYXRhCiAgICAgICAgfSkKICAgICAgfSBlbHNlIHsKICAgICAgICBsZXQgcmVzCiAgICAgICAgaWYgKHRoaXMucGFydEdyYWRlID09PSAnLTEnKSB7CiAgICAgICAgICByZXMgPSBhd2FpdCBHZXRDb21wVHlwZVRyZWUoeyBwcm9mZXNzaW9uYWw6IGNvZGUgfSkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgcmVzID0gYXdhaXQgR2V0UGFydFR5cGVUcmVlKHsgcHJvZmVzc2lvbmFsSWQ6IHRoaXMudHlwZUlkLCBwYXJ0R3JhZGU6IHRoaXMucGFydEdyYWRlIH0pCiAgICAgICAgfQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICB0aGlzLk9iamVjdFR5cGVMaXN0LmRhdGEgPSByZXMuRGF0YQogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKF8pID0+IHsKICAgICAgICAgICAgdGhpcy4kcmVmcy50cmVlU2VsZWN0T2JqZWN0VHlwZS50cmVlRGF0YVVwZGF0ZUZ1bihyZXMuRGF0YSkKICAgICAgICAgIH0pCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSkKICAgICAgICAgIHRoaXMuT2JqZWN0VHlwZUxpc3QuZGF0YSA9IFtdCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoXykgPT4gewogICAgICAgICAgICB0aGlzLiRyZWZzLnRyZWVTZWxlY3RPYmplY3RUeXBlLnRyZWVEYXRhVXBkYXRlRnVuKFtdKQogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0KICAgIH0sCgogICAgLy8g5qOA5p+l6aG56K6+572u6YOo5YiGCiAgICBhZGRUYWJsZURhdGEoKSB7CiAgICAgIHRoaXMuUHJvY2Vzc0Zsb3cucHVzaCh7CiAgICAgICAgQ2hlY2tfSXRlbV9JZDogJycsCiAgICAgICAgRWxpZ2liaWxpdHlfQ3JpdGVyaWE6ICcnLAogICAgICAgIFF1ZXN0aW9ubGFiX0lkOiAnJwogICAgICB9KQogICAgICBjb25zb2xlLmxvZygnYWRkVGFibGVEYXRhKCknLCB0aGlzLlByb2Nlc3NGbG93KQogICAgfSwKICAgIGRlbGV0ZVJvdyhpbmRleCwgcm93cykgewogICAgICBjb25zb2xlLmxvZyhpbmRleCkKICAgICAgcm93cy5zcGxpY2UoaW5kZXgsIDEpCiAgICAgIGNvbnNvbGUubG9nKHRoaXMuUHJvY2Vzc0Zsb3cpCiAgICAgIGlmICh0aGlzLlByb2Nlc3NGbG93Lmxlbmd0aCA+IDAgJiYgaW5kZXggIT09IHRoaXMuUHJvY2Vzc0Zsb3cubGVuZ3RoKSB7CiAgICAgICAgdGhpcy4kc2V0KHRoaXMuUHJvY2Vzc0Zsb3dbaW5kZXhdLCAnc29ydCcsIGluZGV4KQogICAgICB9CiAgICB9LAogICAgbW92ZVVwd2FyZChyb3csIGluZGV4KSB7CiAgICAgIGNvbnNvbGUubG9nKGluZGV4KQogICAgICBjb25zdCB1cERhdGEgPSB0aGlzLlByb2Nlc3NGbG93W2luZGV4IC0gMV0KICAgICAgdGhpcy5Qcm9jZXNzRmxvdy5zcGxpY2UoaW5kZXggLSAxLCAxKQogICAgICB0aGlzLlByb2Nlc3NGbG93LnNwbGljZShpbmRleCwgMCwgdXBEYXRhKQogICAgICB0aGlzLiRzZXQodGhpcy5Qcm9jZXNzRmxvd1tpbmRleCAtIDFdLCAnc29ydCcsIGluZGV4IC0gMSkKICAgICAgdGhpcy4kc2V0KHRoaXMuUHJvY2Vzc0Zsb3dbaW5kZXhdLCAnc29ydCcsIGluZGV4KQogICAgICBjb25zb2xlLmxvZyh0aGlzLlByb2Nlc3NGbG93KQogICAgfSwKICAgIG1vdmVEb3duKHJvdywgaW5kZXgpIHsKICAgICAgY29uc29sZS5sb2coaW5kZXgpCiAgICAgIGNvbnN0IGRvd25EYXRhID0gdGhpcy5Qcm9jZXNzRmxvd1tpbmRleCArIDFdCiAgICAgIHRoaXMuUHJvY2Vzc0Zsb3cuc3BsaWNlKGluZGV4ICsgMSwgMSkKICAgICAgdGhpcy5Qcm9jZXNzRmxvdy5zcGxpY2UoaW5kZXgsIDAsIGRvd25EYXRhKQogICAgICBjb25zb2xlLmxvZyh0aGlzLlByb2Nlc3NGbG93KQogICAgICB0aGlzLiRzZXQodGhpcy5Qcm9jZXNzRmxvd1tpbmRleF0sICdzb3J0JywgaW5kZXgpCiAgICAgIHRoaXMuJHNldCh0aGlzLlByb2Nlc3NGbG93W2luZGV4ICsgMV0sICdzb3J0JywgaW5kZXggKyAxKQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["CombinationDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAk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file": "CombinationDialog.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup/components/Dialog", "sourcesContent": ["<template>\n  <div>\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"130px\">\n      <el-row>\n        <el-form-item label=\"检查项组合名称\" prop=\"Group_Name\">\n          <el-input v-model=\"form.Group_Name\" />\n        </el-form-item>\n\n        <el-form-item label=\"质检节点\" prop=\"Check_Node_Id\">\n          <el-select\n            v-model=\"form.Check_Node_Id\"\n            clearable\n            style=\"width: 100%\"\n            placeholder=\"请选择质检节点\"\n            @change=\"changeNode\"\n          >\n            <el-option\n              v-for=\"(item, index) in CheckNodeList\"\n              :key=\"index\"\n              :label=\"item.Display_Name\"\n              :value=\"item.Id\"\n            />\n          </el-select>\n        </el-form-item>\n\n        <el-form-item label=\"质检类型\" prop=\"Check_Type\">\n          <el-select\n            v-model=\"Change_Check_Type\"\n            clearable\n            multiple\n            :multiple-limit=\"1\"\n            style=\"width: 100%\"\n            :disabled=\"Isdisable\"\n            placeholder=\"请选择质检类型\"\n            @change=\"SelectType\"\n          >\n            <el-option\n              v-for=\"(item, index) in QualityTypeList\"\n              :key=\"index\"\n              :label=\"item.Name\"\n              :value=\"item.Id\"\n            />\n          </el-select>\n        </el-form-item>\n\n        <el-form-item label=\"专业类别\" prop=\"Pro_Category_Id\">\n          <el-select\n            v-model=\"form.Pro_Category_Id\"\n            clearable\n            style=\"width: 100%\"\n            placeholder=\"请选择专业类别\"\n            @change=\"changeCategory\"\n          >\n            <el-option\n              v-for=\"(item, index) in ProCategoryList\"\n              :key=\"index\"\n              :label=\"item.Name\"\n              :value=\"item.Id\"\n            />\n          </el-select>\n        </el-form-item>\n\n        <el-form-item label=\"检查类型\" prop=\"Questionlab_Ids\">\n          <el-select\n            v-model=\"form.Questionlab_Ids\"\n            style=\"width: 100%\"\n            multiple\n            placeholder=\"请选择检查类型\"\n            @change=\"ChangeCheckType\"\n            @remove-tag=\"removeCheckType\"\n          >\n            <el-option\n              v-for=\"(item, index) in CheckTypeList\"\n              :key=\"index\"\n              :label=\"item.Name\"\n              :value=\"item.Id\"\n            />\n          </el-select>\n        </el-form-item>\n\n        <el-form-item\n          label=\"产品类型\"\n          prop=\"Object_Type_Ids\"\n        >\n          <el-tree-select\n            ref=\"treeSelectObjectType\"\n            v-model=\"form.Object_Type_Ids\"\n            :disabled=\"!Boolean(form.Pro_Category_Id)\"\n            class=\"cs-tree-x\"\n            :tree-params=\"ObjectTypeList\"\n            value-key=\"Id\"\n            @removeTag=\"removeTagFn\"\n          />\n        </el-form-item>\n\n        <el-col :span=\"24\">\n          <h3>检查项设置</h3>\n          <el-form-item label=\"\" prop=\"\" class=\"checkItem\">\n            <el-table :data=\"ProcessFlow\" border style=\"width: 100%\">\n              <el-table-column prop=\"\" label=\"*检查类型\" align=\"center\">\n                <template slot-scope=\"{ row }\">\n                  <el-select\n                    v-model=\"row.Questionlab_Id\"\n                    style=\"width: 100%\"\n                    clearable\n                    placeholder=\"请选择\"\n                  >\n                    <el-option\n                      v-for=\"(item, index) in CheckTypeList\"\n                      :key=\"index\"\n                      :label=\"item.Name\"\n                      :value=\"item.Id\"\n                    />\n                  </el-select>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"\" label=\"*检查项内容\" align=\"center\">\n                <template slot-scope=\"{ row, $index }\">\n                  <el-select\n                    v-model=\"row.Check_Item_Id\"\n                    style=\"width: 100%\"\n                    clearable\n                    placeholder=\"请选择\"\n                    @change=\"ChangeItem($event, $index, row)\"\n                  >\n                    <el-option\n                      v-for=\"(item, index) in CheckItemList\"\n                      :key=\"index\"\n                      :label=\"item.Check_Content\"\n                      :value=\"item.Id\"\n                    />\n                  </el-select>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"\" label=\"*合格标准\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  <el-input\n                    v-model=\"scope.row.Eligibility_Criteria\"\n                    disabled\n                  />\n                </template>\n              </el-table-column>\n              <el-table-column\n                prop=\"address\"\n                label=\"操作\"\n                width=\"140\"\n                align=\"center\"\n              >\n                <template slot-scope=\"{ row, $index }\">\n                  <el-button\n                    type=\"text\"\n                    icon=\"el-icon-top\"\n                    :disabled=\"$index == 0\"\n                    @click=\"moveUpward(row, $index)\"\n                  />\n                  <el-button\n                    type=\"text\"\n                    icon=\"el-icon-bottom\"\n                    :disabled=\"$index == ProcessFlow.length - 1\"\n                    @click=\"moveDown(row, $index)\"\n                  />\n                  <el-button\n                    type=\"text\"\n                    icon=\"el-icon-delete\"\n                    style=\"color: #f56c6c\"\n                    @click.native.prevent=\"deleteRow($index, ProcessFlow)\"\n                  />\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-button\n            type=\"text\"\n            class=\"addcheckItem\"\n            @click=\"addTableData\"\n          >+ 新增检查项</el-button>\n        </el-col>\n        <el-col :span=\"24\" style=\"text-align: right\">\n          <el-form-item style=\"text-align: right\">\n            <el-button @click=\"$emit('close')\">关 闭</el-button>\n            <el-button\n              type=\"primary\"\n              @click=\"handleSubmit('form')\"\n            >确 定</el-button>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n  </div>\n</template>\n\n<script>\nimport { AddCheckItemCombination } from '@/api/PRO/factorycheck'\nimport { EntityQualityList } from '@/api/PRO/factorycheck'\n\nimport { GetCheckTypeList } from '@/api/PRO/factorycheck'\nimport { GetCheckItemList } from '@/api/PRO/factorycheck'\nimport { GetNodeList } from '@/api/PRO/factorycheck'\nimport { GetCompTypeTree } from '@/api/PRO/factorycheck'\nimport {\n  GetFactoryProfessionalByCode,\n  GetMaterialType\n} from '@/api/PRO/factorycheck'\nimport { GetPartTypeTree } from '@/api/PRO/partType'\n\nexport default {\n  data() {\n    return {\n      mode: '', // 区分项目和工厂\n      ProjectId: '', // 项目Id\n      Check_Object_Id: '',\n      checkType: {}, // 区分构件、零件、物料\n      form: {\n        Object_Type_Ids: []\n      },\n      rules: {\n        Check_Content: [\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\n        ],\n        Eligibility_Criteria: [\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\n        ],\n        Group_Name: [\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\n        ],\n        Check_Type: [\n          { required: true, message: '请填写完整表单', trigger: 'change' }\n        ],\n        Questionlab_Ids: [\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\n        ]\n      },\n      title: '',\n      options: [],\n      ProcessFlow: [],\n      CheckTypeList: [], // 检查类型下拉\n      CheckItemList: [], // 检查项下拉\n      Change_Check_Type: [],\n      QualityTypeList: [\n        {\n          Name: '质量',\n          Id: 1\n        },\n        {\n          Name: '探伤',\n          Id: 2\n        }\n      ], // 质检类型\n      ProCategoryList: [], // 专业类别下拉\n      CheckNodeList: [], // 质检节点下拉\n      verification: false,\n      ProCategoryCode: '', // 专业类别Code\n      Eligibility_Criteria: '',\n      ObjectTypeList: {\n        // 对象类型\n        'check-strictly': true,\n        'default-expand-all': true,\n        filterable: false,\n        clickParent: true,\n        data: [],\n        props: {\n          children: 'Children',\n          label: 'Label',\n          value: 'Id'\n        }\n      },\n      Isdisable: false,\n      typeCode: '',\n      typeId: '',\n      partGrade: ''\n    }\n  },\n  watch: {\n    ProcessFlow: {\n      handler(newName, oldName) {\n        console.log(newName)\n        this.form.Questionlab_Ids = []\n        this.ProcessFlow.forEach((item) => {\n          if (\n            item.Questionlab_Id &&\n            !this.form.Questionlab_Ids.includes(item.Questionlab_Id)\n          ) {\n            this.form.Questionlab_Ids.push(item.Questionlab_Id)\n          }\n        })\n      },\n      deep: true\n    }\n  },\n  mounted() {},\n  methods: {\n    async init(title, checkType, data) {\n      this.partGrade = checkType.Code\n      this.Check_Object_Id = checkType.Id\n      this.checkType = checkType\n      this.title = title\n      this.form.Check_Object_Id = checkType.Id\n      this.form.Bom_Level = checkType.Code\n      await this.getProfessionalType() // 专业类别\n      await this.getCheckTypeList() // 检查类型\n      await this.getCheckItemList()\n      await this.getNodeList(data) // 质检节点\n    },\n    async addCheckItemCombination() {\n      await AddCheckItemCombination({\n        Group: this.form,\n        Items: this.ProcessFlow\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            type: 'success',\n            message: '保存成功'\n          })\n          this.$emit('refresh')\n          this.$emit('close')\n          this.dialogData = {}\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    removeTagFn(ids, tag) {\n      console.log('ids', ids)\n      console.log('tag', tag)\n    },\n    SelectType(item) {\n      console.log('item', item)\n\n      if (item.length === 1) {\n        this.form.Check_Type = item[0]\n      } else {\n        this.form.Check_Type = -1\n      }\n      console.log('this.form.Check_Type', this.form.Check_Type)\n    },\n    changeNode(val) {\n      console.log(val)\n      console.log(this.CheckNodeList)\n      if (val) {\n        this.form.Check_Type = this.CheckNodeList.find((v) => {\n          return v.Id === val\n        }).Check_Type\n        // 处理质检类型数据\n\n        this.Change_Check_Type = []\n        if (this.form.Check_Type === 1 || this.form.Check_Type === 2) {\n          this.Isdisable = true\n          this.Change_Check_Type.push(this.form.Check_Type)\n        } else if (this.form.Check_Type === -1) {\n          this.Isdisable = false // 质检类型可编辑\n          this.Change_Check_Type = []\n        } else {\n          this.Change_Check_Type = []\n          this.Isdisable = false\n        }\n        console.log(' this.Isdisable', this.Isdisable)\n      } else {\n        this.Change_Check_Type = []\n      }\n    },\n    getEntityCheckType(data) {\n      console.log(data)\n      EntityQualityList({\n        id: data.Id,\n        check_object_id: this.Check_Object_Id\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.form = res.Data[0].Group\n          console.log(this.form.Object_Type_Ids, 'Object_Type_Ids')\n\n          this.ProcessFlow = res.Data[0].Items\n          this.Change_Check_Type = []\n          // 处理质检类型数据\n          if (this.form.Check_Type === 1 || this.form.Check_Type === 2) {\n            this.Change_Check_Type.push(this.form.Check_Type)\n            if (res.Data[0].CheckNode_Type === -1) {\n              this.Isdisable = false\n            } else {\n              this.Isdisable = true\n            }\n          } else if (this.form.Check_Type === -1) {\n            this.Change_Check_Type = [1, 2]\n            this.Isdisable = true // 质检类型不可编辑\n          } else {\n            this.Change_Check_Type = []\n            this.Isdisable = false\n          }\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    handleSubmit(form) {\n      if (this.Change_Check_Type.length === 0) {\n        this.$message({\n          type: 'error',\n          message: '请选择检查类型'\n        })\n        return\n      }\n      let verification = true\n      if (this.ProcessFlow.length === 0) {\n        verification = false\n      } else {\n        this.ProcessFlow.forEach((val) => {\n          for (const key in val) {\n            if (val[key] === '') {\n              verification = false\n            }\n          }\n        })\n      }\n      if (!verification) {\n        this.$message({\n          type: 'error',\n          message: '请填写完整检查项设置内容'\n        })\n        return\n      }\n\n      const processFlowCopy = JSON.parse(JSON.stringify(this.ProcessFlow))\n      const processFlowNew = []\n      processFlowCopy.forEach((item) => {\n        const processFlowJson = {}\n        processFlowJson.Check_Item_Id = item.Check_Item_Id\n        processFlowJson.Eligibility_Criteria = item.Eligibility_Criteria\n        processFlowJson.Questionlab_Id = item.Questionlab_Id\n        processFlowNew.push(processFlowJson)\n      })\n      const processFlowTemp = processFlowNew.map((item) => {\n        return JSON.stringify(item)\n      })\n      if (new Set(processFlowTemp).size !== processFlowTemp.length) {\n        this.$message({\n          type: 'error',\n          message: '检查项设置内容不能完全相同'\n        })\n        return\n      }\n\n      const processFlowArr = this.ProcessFlow.map((v) => {\n        return v.Questionlab_Id\n      })\n\n      const isIncludes = this.form.Questionlab_Ids.every((item) =>\n        processFlowArr.includes(item)\n      )\n      if (!isIncludes) {\n        this.$message({\n          type: 'error',\n          message: '检查项设置必须包含已选检查类型'\n        })\n        return\n      }\n\n      this.$refs[form].validate((valid) => {\n        if (valid) {\n          this.addCheckItemCombination()\n        } else {\n          return false\n        }\n      })\n    },\n    // 获取专业类别\n    async getProfessionalType() {\n      const Platform =\n        localStorage.getItem('Platform') || localStorage.getItem('CurPlatform')\n      if (Platform === '2') {\n        this.mode = 'factory'\n        await GetFactoryProfessionalByCode().then((res) => {\n          if (res.IsSucceed) {\n            this.ProCategoryList = res.Data\n            const {\n              Code,\n              Id\n            } = res.Data?.find(item => item.Code === 'Steel') || {}\n            this.typeCode = Code\n            this.typeId = Id\n          } else {\n            this.$message({\n              type: 'error',\n              message: res.Message\n            })\n          }\n        })\n      }\n\n      // 获取项目/工厂id\n      this.ProjectId =\n        this.mode === 'factory'\n          ? localStorage.getItem('CurReferenceId')\n          : this.ProjectId\n    },\n\n    // 获取检查类型下拉框\n    async getCheckTypeList() {\n      await GetCheckTypeList({ check_object_id: this.Check_Object_Id, Bom_Level: this.form.Bom_Level }).then(\n        (res) => {\n          if (res.IsSucceed) {\n            this.CheckTypeList = res.Data\n            console.log(res.Data)\n          } else {\n            this.$message({\n              type: 'error',\n              message: res.Message\n            })\n          }\n        }\n      )\n    },\n    // 检查项内容\n    async getCheckItemList() {\n      await GetCheckItemList({ check_object_id: this.Check_Object_Id }).then(\n        (res) => {\n          if (res.IsSucceed) {\n            this.CheckItemList = res.Data\n            console.log(res.Data)\n          } else {\n            this.$message({\n              type: 'error',\n              message: res.Message\n            })\n          }\n        }\n      )\n    },\n    // 通过专业类别选择对象类型\n    changeCategory(val) {\n      this.form.Object_Type_Ids = []\n      this.chooseType(val)\n    },\n    // 通过专业类别选择对象类型\n    chooseType(val) {\n      console.log(this.ProCategoryList)\n      this.ProCategoryCode = this.ProCategoryList.find((v) => {\n        return v.Id === val\n      }).Code\n      this.getObjectTypeList(this.ProCategoryCode) // 对象类型\n    },\n    // 选中表格外检查类型\n    ChangeCheckType(val) {\n      const arrJson = Object.assign([], val)\n      // let index = arrJson.indexOf(Isexist);\n      // this.ProcessFlow.splice(index, 1);\n      console.log(arrJson)\n      if (this.ProcessFlow.length > arrJson.length) {\n        const arrJsonTemp = arrJson.map((item) => {\n          const itemField = {\n            Check_Item_Id: '',\n            Eligibility_Criteria: '',\n            Questionlab_Id: item\n          }\n          this.ProcessFlow.forEach((items) => {\n            if (items.Questionlab_Id === item) {\n              itemField.Check_Item_Id = items.Check_Item_Id\n              itemField.Eligibility_Criteria = items.Eligibility_Criteria\n            }\n          })\n\n          return itemField\n        })\n        this.ProcessFlow = [].concat(arrJsonTemp)\n      } else {\n        for (var i = 0; i < arrJson.length; i++) {\n          const Isexist = this.ProcessFlow.find((v) => {\n            return v.Questionlab_Id === arrJson[i]\n          })\n          if (!Isexist) {\n            this.ProcessFlow.push({\n              Questionlab_Id: arrJson[i],\n              Check_Item_Id: '',\n              Eligibility_Criteria: ''\n            })\n          }\n        }\n      }\n\n      console.log('ChangeCheckType()', this.ProcessFlow)\n    },\n\n    removeCheckType(val) {\n      const Isexist = this.ProcessFlow.find((v) => {\n        return v.Questionlab_Id === val\n      })\n      const index = this.ProcessFlow.indexOf(Isexist)\n      if (Isexist) {\n        this.ProcessFlow.splice(index, 1)\n      }\n    },\n    // 选中检查项内容\n    ChangeItem(data, index, row) {\n      // console.log(data);\n      // console.log(index);\n      // console.log(row)\n      // console.log(this.CheckItemList);\n      row.Eligibility_Criteria = ''\n      this.Eligibility_Criteria = ''\n      this.Eligibility_Criteria = this.CheckItemList.find((v) => {\n        return v.Id === data\n      })?.Eligibility_Criteria\n      this.$set(\n        this.ProcessFlow[index],\n        'Eligibility_Criteria',\n        this.Eligibility_Criteria\n      )\n      this.$set(this.ProcessFlow[index], 'sort', index)\n      console.log(this.ProcessFlow)\n    },\n\n    async editHandleData(data) {\n      if (this.title === '编辑') {\n        console.log('data', data)\n        this.form.Id = data.Id\n        this.getEntityCheckType(data)\n        await this.chooseType(data.Pro_Category_Id)\n      }\n    },\n\n    // 质检节点下拉菜单\n    async getNodeList(data) {\n      await GetNodeList({ check_object_id: this.Check_Object_Id, Bom_Level: this.form.Bom_Level }).then(\n        (res) => {\n          if (res.IsSucceed) {\n            this.CheckNodeList = res.Data\n            this.editHandleData(data)\n            console.log(res.Data)\n          } else {\n            this.$message({\n              type: 'error',\n              message: res.Message\n            })\n          }\n        }\n      )\n    },\n    // 对象类型下拉\n    async getObjectTypeList(code) {\n      if (this.checkType.Display_Name === '物料') {\n        GetMaterialType({}).then((res) => {\n          this.ObjectTypeList = res.Data\n        })\n      } else {\n        let res\n        if (this.partGrade === '-1') {\n          res = await GetCompTypeTree({ professional: code })\n        } else {\n          res = await GetPartTypeTree({ professionalId: this.typeId, partGrade: this.partGrade })\n        }\n        if (res.IsSucceed) {\n          this.ObjectTypeList.data = res.Data\n          this.$nextTick((_) => {\n            this.$refs.treeSelectObjectType.treeDataUpdateFun(res.Data)\n          })\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n          this.ObjectTypeList.data = []\n          this.$nextTick((_) => {\n            this.$refs.treeSelectObjectType.treeDataUpdateFun([])\n          })\n        }\n      }\n    },\n\n    // 检查项设置部分\n    addTableData() {\n      this.ProcessFlow.push({\n        Check_Item_Id: '',\n        Eligibility_Criteria: '',\n        Questionlab_Id: ''\n      })\n      console.log('addTableData()', this.ProcessFlow)\n    },\n    deleteRow(index, rows) {\n      console.log(index)\n      rows.splice(index, 1)\n      console.log(this.ProcessFlow)\n      if (this.ProcessFlow.length > 0 && index !== this.ProcessFlow.length) {\n        this.$set(this.ProcessFlow[index], 'sort', index)\n      }\n    },\n    moveUpward(row, index) {\n      console.log(index)\n      const upData = this.ProcessFlow[index - 1]\n      this.ProcessFlow.splice(index - 1, 1)\n      this.ProcessFlow.splice(index, 0, upData)\n      this.$set(this.ProcessFlow[index - 1], 'sort', index - 1)\n      this.$set(this.ProcessFlow[index], 'sort', index)\n      console.log(this.ProcessFlow)\n    },\n    moveDown(row, index) {\n      console.log(index)\n      const downData = this.ProcessFlow[index + 1]\n      this.ProcessFlow.splice(index + 1, 1)\n      this.ProcessFlow.splice(index, 0, downData)\n      console.log(this.ProcessFlow)\n      this.$set(this.ProcessFlow[index], 'sort', index)\n      this.$set(this.ProcessFlow[index + 1], 'sort', index + 1)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n::v-deep {\n  .checkItem {\n    width: 100%;\n    .el-form-item__content {\n      margin-left: 0 !important;\n    }\n  }\n  .addcheckItem {\n    font-size: 16px;\n    margin-bottom: 10px;\n  }\n}\n::v-deep .el-form-item {\n  display: inline-block;\n  .el-form-item__content {\n    & > .el-input {\n      width: 220px !important;\n    }\n    & > .el-select {\n      width: 220px !important;\n    }\n    .el-tree-select-input {\n      width: 220px !important;\n    }\n  }\n}\n</style>\n"]}]}
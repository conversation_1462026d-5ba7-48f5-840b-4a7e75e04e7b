{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\CombinationDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\CombinationDialog.vue", "mtime": 1757468112748}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBBZGRDaGVja0l0ZW1Db21iaW5hdGlvbiB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snDQppbXBvcnQgeyBFbnRpdHlRdWFsaXR5TGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snDQoNCmltcG9ydCB7IEdldENoZWNrVHlwZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJw0KaW1wb3J0IHsgR2V0Q2hlY2tJdGVtTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snDQppbXBvcnQgeyBHZXROb2RlTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snDQppbXBvcnQgeyBHZXRDb21wVHlwZVRyZWUgfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJw0KaW1wb3J0IHsNCiAgR2V0RmFjdG9yeVByb2Zlc3Npb25hbEJ5Q29kZSwNCiAgR2V0TWF0ZXJpYWxUeXBlDQp9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snDQppbXBvcnQgeyBHZXRQYXJ0VHlwZVRyZWUgfSBmcm9tICdAL2FwaS9QUk8vcGFydFR5cGUnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgbW9kZTogJycsIC8vIOWMuuWIhumhueebruWSjOW3peWOgg0KICAgICAgUHJvamVjdElkOiAnJywgLy8g6aG555uuSWQNCiAgICAgIENoZWNrX09iamVjdF9JZDogJycsDQogICAgICBjaGVja1R5cGU6IHt9LCAvLyDljLrliIbmnoTku7bjgIHpm7bku7bjgIHnianmlpkNCiAgICAgIGZvcm06IHsNCiAgICAgICAgT2JqZWN0X1R5cGVfSWRzOiBbXQ0KICAgICAgfSwNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIENoZWNrX0NvbnRlbnQ6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+35aGr5YaZ5a6M5pW06KGo5Y2VJywgdHJpZ2dlcjogJ2JsdXInIH0NCiAgICAgICAgXSwNCiAgICAgICAgRWxpZ2liaWxpdHlfQ3JpdGVyaWE6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+35aGr5YaZ5a6M5pW06KGo5Y2VJywgdHJpZ2dlcjogJ2JsdXInIH0NCiAgICAgICAgXSwNCiAgICAgICAgR3JvdXBfTmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICBDaGVja19UeXBlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOihqOWNlScsIHRyaWdnZXI6ICdjaGFuZ2UnIH0NCiAgICAgICAgXSwNCiAgICAgICAgUXVlc3Rpb25sYWJfSWRzOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOihqOWNlScsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0NCiAgICAgIH0sDQogICAgICB0aXRsZTogJycsDQogICAgICBvcHRpb25zOiBbXSwNCiAgICAgIFByb2Nlc3NGbG93OiBbXSwNCiAgICAgIENoZWNrVHlwZUxpc3Q6IFtdLCAvLyDmo4Dmn6XnsbvlnovkuIvmi4kNCiAgICAgIENoZWNrSXRlbUxpc3Q6IFtdLCAvLyDmo4Dmn6XpobnkuIvmi4kNCiAgICAgIENoYW5nZV9DaGVja19UeXBlOiBbXSwNCiAgICAgIFF1YWxpdHlUeXBlTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgTmFtZTogJ+i0qOmHjycsDQogICAgICAgICAgSWQ6IDENCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIE5hbWU6ICfmjqLkvKQnLA0KICAgICAgICAgIElkOiAyDQogICAgICAgIH0NCiAgICAgIF0sIC8vIOi0qOajgOexu+Weiw0KICAgICAgUHJvQ2F0ZWdvcnlMaXN0OiBbXSwgLy8g5LiT5Lia57G75Yir5LiL5ouJDQogICAgICBDaGVja05vZGVMaXN0OiBbXSwgLy8g6LSo5qOA6IqC54K55LiL5ouJDQogICAgICB2ZXJpZmljYXRpb246IGZhbHNlLA0KICAgICAgUHJvQ2F0ZWdvcnlDb2RlOiAnJywgLy8g5LiT5Lia57G75YirQ29kZQ0KICAgICAgRWxpZ2liaWxpdHlfQ3JpdGVyaWE6ICcnLA0KICAgICAgT2JqZWN0VHlwZUxpc3Q6IHsNCiAgICAgICAgLy8g5a+56LGh57G75Z6LDQogICAgICAgICdjaGVjay1zdHJpY3RseSc6IHRydWUsDQogICAgICAgICdkZWZhdWx0LWV4cGFuZC1hbGwnOiB0cnVlLA0KICAgICAgICBmaWx0ZXJhYmxlOiBmYWxzZSwNCiAgICAgICAgY2xpY2tQYXJlbnQ6IHRydWUsDQogICAgICAgIGRhdGE6IFtdLA0KICAgICAgICBwcm9wczogew0KICAgICAgICAgIGNoaWxkcmVuOiAnQ2hpbGRyZW4nLA0KICAgICAgICAgIGxhYmVsOiAnTGFiZWwnLA0KICAgICAgICAgIHZhbHVlOiAnSWQnDQogICAgICAgIH0NCiAgICAgIH0sDQogICAgICBJc2Rpc2FibGU6IGZhbHNlLA0KICAgICAgdHlwZUNvZGU6ICcnLA0KICAgICAgdHlwZUlkOiAnJywNCiAgICAgIHBhcnRHcmFkZTogJycNCiAgICB9DQogIH0sDQogIHdhdGNoOiB7DQogICAgUHJvY2Vzc0Zsb3c6IHsNCiAgICAgIGhhbmRsZXIobmV3TmFtZSwgb2xkTmFtZSkgew0KICAgICAgICBjb25zb2xlLmxvZyhuZXdOYW1lKQ0KICAgICAgICB0aGlzLmZvcm0uUXVlc3Rpb25sYWJfSWRzID0gW10NCiAgICAgICAgdGhpcy5Qcm9jZXNzRmxvdy5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgaWYgKA0KICAgICAgICAgICAgaXRlbS5RdWVzdGlvbmxhYl9JZCAmJg0KICAgICAgICAgICAgIXRoaXMuZm9ybS5RdWVzdGlvbmxhYl9JZHMuaW5jbHVkZXMoaXRlbS5RdWVzdGlvbmxhYl9JZCkNCiAgICAgICAgICApIHsNCiAgICAgICAgICAgIHRoaXMuZm9ybS5RdWVzdGlvbmxhYl9JZHMucHVzaChpdGVtLlF1ZXN0aW9ubGFiX0lkKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0sDQogICAgICBkZWVwOiB0cnVlDQogICAgfQ0KICB9LA0KICBtb3VudGVkKCkge30sDQogIG1ldGhvZHM6IHsNCiAgICBhc3luYyBpbml0KHRpdGxlLCBjaGVja1R5cGUsIGRhdGEpIHsNCiAgICAgIHRoaXMucGFydEdyYWRlID0gY2hlY2tUeXBlLkNvZGUNCiAgICAgIHRoaXMuQ2hlY2tfT2JqZWN0X0lkID0gY2hlY2tUeXBlLklkDQogICAgICB0aGlzLmNoZWNrVHlwZSA9IGNoZWNrVHlwZQ0KICAgICAgdGhpcy50aXRsZSA9IHRpdGxlDQogICAgICB0aGlzLmZvcm0uQ2hlY2tfT2JqZWN0X0lkID0gY2hlY2tUeXBlLklkDQogICAgICB0aGlzLmZvcm0uQm9tX0xldmVsID0gY2hlY2tUeXBlLkNvZGUNCiAgICAgIGF3YWl0IHRoaXMuZ2V0UHJvZmVzc2lvbmFsVHlwZSgpIC8vIOS4k+S4muexu+WIqw0KICAgICAgYXdhaXQgdGhpcy5nZXRDaGVja1R5cGVMaXN0KCkgLy8g5qOA5p+l57G75Z6LDQogICAgICBhd2FpdCB0aGlzLmdldENoZWNrSXRlbUxpc3QoKQ0KICAgICAgYXdhaXQgdGhpcy5nZXROb2RlTGlzdChkYXRhKSAvLyDotKjmo4DoioLngrkNCiAgICB9LA0KICAgIGFzeW5jIGFkZENoZWNrSXRlbUNvbWJpbmF0aW9uKCkgew0KICAgICAgYXdhaXQgQWRkQ2hlY2tJdGVtQ29tYmluYXRpb24oew0KICAgICAgICBHcm91cDogdGhpcy5mb3JtLA0KICAgICAgICBJdGVtczogdGhpcy5Qcm9jZXNzRmxvdw0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsDQogICAgICAgICAgICBtZXNzYWdlOiAn5L+d5a2Y5oiQ5YqfJw0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy4kZW1pdCgnY2xvc2UnKQ0KICAgICAgICAgIHRoaXMuZGlhbG9nRGF0YSA9IHt9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICB0eXBlOiAnZXJyb3InLA0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgcmVtb3ZlVGFnRm4oaWRzLCB0YWcpIHsNCiAgICAgIGNvbnNvbGUubG9nKCdpZHMnLCBpZHMpDQogICAgICBjb25zb2xlLmxvZygndGFnJywgdGFnKQ0KICAgIH0sDQogICAgU2VsZWN0VHlwZShpdGVtKSB7DQogICAgICBjb25zb2xlLmxvZygnaXRlbScsIGl0ZW0pDQoNCiAgICAgIGlmIChpdGVtLmxlbmd0aCA9PT0gMSkgew0KICAgICAgICB0aGlzLmZvcm0uQ2hlY2tfVHlwZSA9IGl0ZW1bMF0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuZm9ybS5DaGVja19UeXBlID0gLTENCiAgICAgIH0NCiAgICAgIGNvbnNvbGUubG9nKCd0aGlzLmZvcm0uQ2hlY2tfVHlwZScsIHRoaXMuZm9ybS5DaGVja19UeXBlKQ0KICAgIH0sDQogICAgY2hhbmdlTm9kZSh2YWwpIHsNCiAgICAgIGNvbnNvbGUubG9nKHZhbCkNCiAgICAgIGNvbnNvbGUubG9nKHRoaXMuQ2hlY2tOb2RlTGlzdCkNCiAgICAgIGlmICh2YWwpIHsNCiAgICAgICAgdGhpcy5mb3JtLkNoZWNrX1R5cGUgPSB0aGlzLkNoZWNrTm9kZUxpc3QuZmluZCgodikgPT4gew0KICAgICAgICAgIHJldHVybiB2LklkID09PSB2YWwNCiAgICAgICAgfSkuQ2hlY2tfVHlwZQ0KICAgICAgICAvLyDlpITnkIbotKjmo4DnsbvlnovmlbDmja4NCg0KICAgICAgICB0aGlzLkNoYW5nZV9DaGVja19UeXBlID0gW10NCiAgICAgICAgaWYgKHRoaXMuZm9ybS5DaGVja19UeXBlID09PSAxIHx8IHRoaXMuZm9ybS5DaGVja19UeXBlID09PSAyKSB7DQogICAgICAgICAgdGhpcy5Jc2Rpc2FibGUgPSB0cnVlDQogICAgICAgICAgdGhpcy5DaGFuZ2VfQ2hlY2tfVHlwZS5wdXNoKHRoaXMuZm9ybS5DaGVja19UeXBlKQ0KICAgICAgICB9IGVsc2UgaWYgKHRoaXMuZm9ybS5DaGVja19UeXBlID09PSAtMSkgew0KICAgICAgICAgIHRoaXMuSXNkaXNhYmxlID0gZmFsc2UgLy8g6LSo5qOA57G75Z6L5Y+v57yW6L6RDQogICAgICAgICAgdGhpcy5DaGFuZ2VfQ2hlY2tfVHlwZSA9IFtdDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5DaGFuZ2VfQ2hlY2tfVHlwZSA9IFtdDQogICAgICAgICAgdGhpcy5Jc2Rpc2FibGUgPSBmYWxzZQ0KICAgICAgICB9DQogICAgICAgIGNvbnNvbGUubG9nKCcgdGhpcy5Jc2Rpc2FibGUnLCB0aGlzLklzZGlzYWJsZSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuQ2hhbmdlX0NoZWNrX1R5cGUgPSBbXQ0KICAgICAgfQ0KICAgIH0sDQogICAgZ2V0RW50aXR5Q2hlY2tUeXBlKGRhdGEpIHsNCiAgICAgIGNvbnNvbGUubG9nKGRhdGEpDQogICAgICBFbnRpdHlRdWFsaXR5TGlzdCh7DQogICAgICAgIGlkOiBkYXRhLklkLA0KICAgICAgICBjaGVja19vYmplY3RfaWQ6IHRoaXMuQ2hlY2tfT2JqZWN0X0lkDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLmZvcm0gPSByZXMuRGF0YVswXS5Hcm91cA0KICAgICAgICAgIGNvbnNvbGUubG9nKHRoaXMuZm9ybS5PYmplY3RfVHlwZV9JZHMsICdPYmplY3RfVHlwZV9JZHMnKQ0KDQogICAgICAgICAgdGhpcy5Qcm9jZXNzRmxvdyA9IHJlcy5EYXRhWzBdLkl0ZW1zDQogICAgICAgICAgdGhpcy5DaGFuZ2VfQ2hlY2tfVHlwZSA9IFtdDQogICAgICAgICAgLy8g5aSE55CG6LSo5qOA57G75Z6L5pWw5o2uDQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5DaGVja19UeXBlID09PSAxIHx8IHRoaXMuZm9ybS5DaGVja19UeXBlID09PSAyKSB7DQogICAgICAgICAgICB0aGlzLkNoYW5nZV9DaGVja19UeXBlLnB1c2godGhpcy5mb3JtLkNoZWNrX1R5cGUpDQogICAgICAgICAgICBpZiAocmVzLkRhdGFbMF0uQ2hlY2tOb2RlX1R5cGUgPT09IC0xKSB7DQogICAgICAgICAgICAgIHRoaXMuSXNkaXNhYmxlID0gZmFsc2UNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHRoaXMuSXNkaXNhYmxlID0gdHJ1ZQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gZWxzZSBpZiAodGhpcy5mb3JtLkNoZWNrX1R5cGUgPT09IC0xKSB7DQogICAgICAgICAgICB0aGlzLkNoYW5nZV9DaGVja19UeXBlID0gWzEsIDJdDQogICAgICAgICAgICB0aGlzLklzZGlzYWJsZSA9IHRydWUgLy8g6LSo5qOA57G75Z6L5LiN5Y+v57yW6L6RDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuQ2hhbmdlX0NoZWNrX1R5cGUgPSBbXQ0KICAgICAgICAgICAgdGhpcy5Jc2Rpc2FibGUgPSBmYWxzZQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsDQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVTdWJtaXQoZm9ybSkgew0KICAgICAgaWYgKHRoaXMuQ2hhbmdlX0NoZWNrX1R5cGUubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIHR5cGU6ICdlcnJvcicsDQogICAgICAgICAgbWVzc2FnZTogJ+ivt+mAieaLqeajgOafpeexu+WeiycNCiAgICAgICAgfSkNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICBsZXQgdmVyaWZpY2F0aW9uID0gdHJ1ZQ0KICAgICAgaWYgKHRoaXMuUHJvY2Vzc0Zsb3cubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHZlcmlmaWNhdGlvbiA9IGZhbHNlDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLlByb2Nlc3NGbG93LmZvckVhY2goKHZhbCkgPT4gew0KICAgICAgICAgIGZvciAoY29uc3Qga2V5IGluIHZhbCkgew0KICAgICAgICAgICAgaWYgKHZhbFtrZXldID09PSAnJykgew0KICAgICAgICAgICAgICB2ZXJpZmljYXRpb24gPSBmYWxzZQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICAgIGlmICghdmVyaWZpY2F0aW9uKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIHR5cGU6ICdlcnJvcicsDQogICAgICAgICAgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOajgOafpemhueiuvue9ruWGheWuuScNCiAgICAgICAgfSkNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIGNvbnN0IHByb2Nlc3NGbG93Q29weSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy5Qcm9jZXNzRmxvdykpDQogICAgICBjb25zdCBwcm9jZXNzRmxvd05ldyA9IFtdDQogICAgICBwcm9jZXNzRmxvd0NvcHkuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICBjb25zdCBwcm9jZXNzRmxvd0pzb24gPSB7fQ0KICAgICAgICBwcm9jZXNzRmxvd0pzb24uQ2hlY2tfSXRlbV9JZCA9IGl0ZW0uQ2hlY2tfSXRlbV9JZA0KICAgICAgICBwcm9jZXNzRmxvd0pzb24uRWxpZ2liaWxpdHlfQ3JpdGVyaWEgPSBpdGVtLkVsaWdpYmlsaXR5X0NyaXRlcmlhDQogICAgICAgIHByb2Nlc3NGbG93SnNvbi5RdWVzdGlvbmxhYl9JZCA9IGl0ZW0uUXVlc3Rpb25sYWJfSWQNCiAgICAgICAgcHJvY2Vzc0Zsb3dOZXcucHVzaChwcm9jZXNzRmxvd0pzb24pDQogICAgICB9KQ0KICAgICAgY29uc3QgcHJvY2Vzc0Zsb3dUZW1wID0gcHJvY2Vzc0Zsb3dOZXcubWFwKChpdGVtKSA9PiB7DQogICAgICAgIHJldHVybiBKU09OLnN0cmluZ2lmeShpdGVtKQ0KICAgICAgfSkNCiAgICAgIGlmIChuZXcgU2V0KHByb2Nlc3NGbG93VGVtcCkuc2l6ZSAhPT0gcHJvY2Vzc0Zsb3dUZW1wLmxlbmd0aCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICB0eXBlOiAnZXJyb3InLA0KICAgICAgICAgIG1lc3NhZ2U6ICfmo4Dmn6Xpobnorr7nva7lhoXlrrnkuI3og73lrozlhajnm7jlkIwnDQogICAgICAgIH0pDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICBjb25zdCBwcm9jZXNzRmxvd0FyciA9IHRoaXMuUHJvY2Vzc0Zsb3cubWFwKCh2KSA9PiB7DQogICAgICAgIHJldHVybiB2LlF1ZXN0aW9ubGFiX0lkDQogICAgICB9KQ0KDQogICAgICBjb25zdCBpc0luY2x1ZGVzID0gdGhpcy5mb3JtLlF1ZXN0aW9ubGFiX0lkcy5ldmVyeSgoaXRlbSkgPT4NCiAgICAgICAgcHJvY2Vzc0Zsb3dBcnIuaW5jbHVkZXMoaXRlbSkNCiAgICAgICkNCiAgICAgIGlmICghaXNJbmNsdWRlcykgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICB0eXBlOiAnZXJyb3InLA0KICAgICAgICAgIG1lc3NhZ2U6ICfmo4Dmn6Xpobnorr7nva7lv4XpobvljIXlkKvlt7LpgInmo4Dmn6XnsbvlnosnDQogICAgICAgIH0pDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICB0aGlzLiRyZWZzW2Zvcm1dLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICB0aGlzLmFkZENoZWNrSXRlbUNvbWJpbmF0aW9uKCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOiOt+WPluS4k+S4muexu+WIqw0KICAgIGFzeW5jIGdldFByb2Zlc3Npb25hbFR5cGUoKSB7DQogICAgICBjb25zdCBQbGF0Zm9ybSA9DQogICAgICAgIGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdQbGF0Zm9ybScpIHx8IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdDdXJQbGF0Zm9ybScpDQogICAgICBpZiAoUGxhdGZvcm0gPT09ICcyJykgew0KICAgICAgICB0aGlzLm1vZGUgPSAnZmFjdG9yeScNCiAgICAgICAgYXdhaXQgR2V0RmFjdG9yeVByb2Zlc3Npb25hbEJ5Q29kZSgpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICB0aGlzLlByb0NhdGVnb3J5TGlzdCA9IHJlcy5EYXRhDQogICAgICAgICAgICBjb25zdCB7DQogICAgICAgICAgICAgIENvZGUsDQogICAgICAgICAgICAgIElkDQogICAgICAgICAgICB9ID0gcmVzLkRhdGE/LmZpbmQoaXRlbSA9PiBpdGVtLkNvZGUgPT09ICdTdGVlbCcpIHx8IHt9DQogICAgICAgICAgICB0aGlzLnR5cGVDb2RlID0gQ29kZQ0KICAgICAgICAgICAgdGhpcy50eXBlSWQgPSBJZA0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfQ0KDQogICAgICAvLyDojrflj5bpobnnm64v5bel5Y6CaWQNCiAgICAgIHRoaXMuUHJvamVjdElkID0NCiAgICAgICAgdGhpcy5tb2RlID09PSAnZmFjdG9yeScNCiAgICAgICAgICA/IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdDdXJSZWZlcmVuY2VJZCcpDQogICAgICAgICAgOiB0aGlzLlByb2plY3RJZA0KICAgIH0sDQoNCiAgICAvLyDojrflj5bmo4Dmn6XnsbvlnovkuIvmi4nmoYYNCiAgICBhc3luYyBnZXRDaGVja1R5cGVMaXN0KCkgew0KICAgICAgYXdhaXQgR2V0Q2hlY2tUeXBlTGlzdCh7IGNoZWNrX29iamVjdF9pZDogdGhpcy5DaGVja19PYmplY3RfSWQsIEJvbV9MZXZlbDogdGhpcy5mb3JtLkJvbV9MZXZlbCB9KS50aGVuKA0KICAgICAgICAocmVzKSA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIHRoaXMuQ2hlY2tUeXBlTGlzdCA9IHJlcy5EYXRhDQogICAgICAgICAgICBjb25zb2xlLmxvZyhyZXMuRGF0YSkNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsDQogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgKQ0KICAgIH0sDQogICAgLy8g5qOA5p+l6aG55YaF5a65DQogICAgYXN5bmMgZ2V0Q2hlY2tJdGVtTGlzdCgpIHsNCiAgICAgIGF3YWl0IEdldENoZWNrSXRlbUxpc3QoeyBjaGVja19vYmplY3RfaWQ6IHRoaXMuQ2hlY2tfT2JqZWN0X0lkIH0pLnRoZW4oDQogICAgICAgIChyZXMpID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgdGhpcy5DaGVja0l0ZW1MaXN0ID0gcmVzLkRhdGENCiAgICAgICAgICAgIGNvbnNvbGUubG9nKHJlcy5EYXRhKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICApDQogICAgfSwNCiAgICAvLyDpgJrov4fkuJPkuJrnsbvliKvpgInmi6nlr7nosaHnsbvlnosNCiAgICBjaGFuZ2VDYXRlZ29yeSh2YWwpIHsNCiAgICAgIHRoaXMuZm9ybS5PYmplY3RfVHlwZV9JZHMgPSBbXQ0KICAgICAgdGhpcy5jaG9vc2VUeXBlKHZhbCkNCiAgICB9LA0KICAgIC8vIOmAmui/h+S4k+S4muexu+WIq+mAieaLqeWvueixoeexu+Weiw0KICAgIGNob29zZVR5cGUodmFsKSB7DQogICAgICBjb25zb2xlLmxvZyh0aGlzLlByb0NhdGVnb3J5TGlzdCkNCiAgICAgIHRoaXMuUHJvQ2F0ZWdvcnlDb2RlID0gdGhpcy5Qcm9DYXRlZ29yeUxpc3QuZmluZCgodikgPT4gew0KICAgICAgICByZXR1cm4gdi5JZCA9PT0gdmFsDQogICAgICB9KS5Db2RlDQogICAgICB0aGlzLmdldE9iamVjdFR5cGVMaXN0KHRoaXMuUHJvQ2F0ZWdvcnlDb2RlKSAvLyDlr7nosaHnsbvlnosNCiAgICB9LA0KICAgIC8vIOmAieS4reihqOagvOWkluajgOafpeexu+Weiw0KICAgIENoYW5nZUNoZWNrVHlwZSh2YWwpIHsNCiAgICAgIGNvbnN0IGFyckpzb24gPSBPYmplY3QuYXNzaWduKFtdLCB2YWwpDQogICAgICAvLyBsZXQgaW5kZXggPSBhcnJKc29uLmluZGV4T2YoSXNleGlzdCk7DQogICAgICAvLyB0aGlzLlByb2Nlc3NGbG93LnNwbGljZShpbmRleCwgMSk7DQogICAgICBjb25zb2xlLmxvZyhhcnJKc29uKQ0KICAgICAgaWYgKHRoaXMuUHJvY2Vzc0Zsb3cubGVuZ3RoID4gYXJySnNvbi5sZW5ndGgpIHsNCiAgICAgICAgY29uc3QgYXJySnNvblRlbXAgPSBhcnJKc29uLm1hcCgoaXRlbSkgPT4gew0KICAgICAgICAgIGNvbnN0IGl0ZW1GaWVsZCA9IHsNCiAgICAgICAgICAgIENoZWNrX0l0ZW1fSWQ6ICcnLA0KICAgICAgICAgICAgRWxpZ2liaWxpdHlfQ3JpdGVyaWE6ICcnLA0KICAgICAgICAgICAgUXVlc3Rpb25sYWJfSWQ6IGl0ZW0NCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy5Qcm9jZXNzRmxvdy5mb3JFYWNoKChpdGVtcykgPT4gew0KICAgICAgICAgICAgaWYgKGl0ZW1zLlF1ZXN0aW9ubGFiX0lkID09PSBpdGVtKSB7DQogICAgICAgICAgICAgIGl0ZW1GaWVsZC5DaGVja19JdGVtX0lkID0gaXRlbXMuQ2hlY2tfSXRlbV9JZA0KICAgICAgICAgICAgICBpdGVtRmllbGQuRWxpZ2liaWxpdHlfQ3JpdGVyaWEgPSBpdGVtcy5FbGlnaWJpbGl0eV9Dcml0ZXJpYQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQoNCiAgICAgICAgICByZXR1cm4gaXRlbUZpZWxkDQogICAgICAgIH0pDQogICAgICAgIHRoaXMuUHJvY2Vzc0Zsb3cgPSBbXS5jb25jYXQoYXJySnNvblRlbXApDQogICAgICB9IGVsc2Ugew0KICAgICAgICBmb3IgKHZhciBpID0gMDsgaSA8IGFyckpzb24ubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgICBjb25zdCBJc2V4aXN0ID0gdGhpcy5Qcm9jZXNzRmxvdy5maW5kKCh2KSA9PiB7DQogICAgICAgICAgICByZXR1cm4gdi5RdWVzdGlvbmxhYl9JZCA9PT0gYXJySnNvbltpXQ0KICAgICAgICAgIH0pDQogICAgICAgICAgaWYgKCFJc2V4aXN0KSB7DQogICAgICAgICAgICB0aGlzLlByb2Nlc3NGbG93LnB1c2goew0KICAgICAgICAgICAgICBRdWVzdGlvbmxhYl9JZDogYXJySnNvbltpXSwNCiAgICAgICAgICAgICAgQ2hlY2tfSXRlbV9JZDogJycsDQogICAgICAgICAgICAgIEVsaWdpYmlsaXR5X0NyaXRlcmlhOiAnJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgY29uc29sZS5sb2coJ0NoYW5nZUNoZWNrVHlwZSgpJywgdGhpcy5Qcm9jZXNzRmxvdykNCiAgICB9LA0KDQogICAgcmVtb3ZlQ2hlY2tUeXBlKHZhbCkgew0KICAgICAgY29uc3QgSXNleGlzdCA9IHRoaXMuUHJvY2Vzc0Zsb3cuZmluZCgodikgPT4gew0KICAgICAgICByZXR1cm4gdi5RdWVzdGlvbmxhYl9JZCA9PT0gdmFsDQogICAgICB9KQ0KICAgICAgY29uc3QgaW5kZXggPSB0aGlzLlByb2Nlc3NGbG93LmluZGV4T2YoSXNleGlzdCkNCiAgICAgIGlmIChJc2V4aXN0KSB7DQogICAgICAgIHRoaXMuUHJvY2Vzc0Zsb3cuc3BsaWNlKGluZGV4LCAxKQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g6YCJ5Lit5qOA5p+l6aG55YaF5a65DQogICAgQ2hhbmdlSXRlbShkYXRhLCBpbmRleCwgcm93KSB7DQogICAgICAvLyBjb25zb2xlLmxvZyhkYXRhKTsNCiAgICAgIC8vIGNvbnNvbGUubG9nKGluZGV4KTsNCiAgICAgIC8vIGNvbnNvbGUubG9nKHJvdykNCiAgICAgIC8vIGNvbnNvbGUubG9nKHRoaXMuQ2hlY2tJdGVtTGlzdCk7DQogICAgICByb3cuRWxpZ2liaWxpdHlfQ3JpdGVyaWEgPSAnJw0KICAgICAgdGhpcy5FbGlnaWJpbGl0eV9Dcml0ZXJpYSA9ICcnDQogICAgICB0aGlzLkVsaWdpYmlsaXR5X0NyaXRlcmlhID0gdGhpcy5DaGVja0l0ZW1MaXN0LmZpbmQoKHYpID0+IHsNCiAgICAgICAgcmV0dXJuIHYuSWQgPT09IGRhdGENCiAgICAgIH0pPy5FbGlnaWJpbGl0eV9Dcml0ZXJpYQ0KICAgICAgdGhpcy4kc2V0KA0KICAgICAgICB0aGlzLlByb2Nlc3NGbG93W2luZGV4XSwNCiAgICAgICAgJ0VsaWdpYmlsaXR5X0NyaXRlcmlhJywNCiAgICAgICAgdGhpcy5FbGlnaWJpbGl0eV9Dcml0ZXJpYQ0KICAgICAgKQ0KICAgICAgdGhpcy4kc2V0KHRoaXMuUHJvY2Vzc0Zsb3dbaW5kZXhdLCAnc29ydCcsIGluZGV4KQ0KICAgICAgY29uc29sZS5sb2codGhpcy5Qcm9jZXNzRmxvdykNCiAgICB9LA0KDQogICAgYXN5bmMgZWRpdEhhbmRsZURhdGEoZGF0YSkgew0KICAgICAgaWYgKHRoaXMudGl0bGUgPT09ICfnvJbovpEnKSB7DQogICAgICAgIGNvbnNvbGUubG9nKCdkYXRhJywgZGF0YSkNCiAgICAgICAgdGhpcy5mb3JtLklkID0gZGF0YS5JZA0KICAgICAgICB0aGlzLmdldEVudGl0eUNoZWNrVHlwZShkYXRhKQ0KICAgICAgICBhd2FpdCB0aGlzLmNob29zZVR5cGUoZGF0YS5Qcm9fQ2F0ZWdvcnlfSWQpDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOi0qOajgOiKgueCueS4i+aLieiPnOWNlQ0KICAgIGFzeW5jIGdldE5vZGVMaXN0KGRhdGEpIHsNCiAgICAgIGF3YWl0IEdldE5vZGVMaXN0KHsgY2hlY2tfb2JqZWN0X2lkOiB0aGlzLkNoZWNrX09iamVjdF9JZCwgQm9tX0xldmVsOiB0aGlzLmZvcm0uQm9tX0xldmVsIH0pLnRoZW4oDQogICAgICAgIChyZXMpID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgdGhpcy5DaGVja05vZGVMaXN0ID0gcmVzLkRhdGENCiAgICAgICAgICAgIHRoaXMuZWRpdEhhbmRsZURhdGEoZGF0YSkNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKHJlcy5EYXRhKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICApDQogICAgfSwNCiAgICAvLyDlr7nosaHnsbvlnovkuIvmi4kNCiAgICBhc3luYyBnZXRPYmplY3RUeXBlTGlzdChjb2RlKSB7DQogICAgICBpZiAodGhpcy5jaGVja1R5cGUuRGlzcGxheV9OYW1lID09PSAn54mp5paZJykgew0KICAgICAgICBHZXRNYXRlcmlhbFR5cGUoe30pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIHRoaXMuT2JqZWN0VHlwZUxpc3QgPSByZXMuRGF0YQ0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgbGV0IHJlcw0KICAgICAgICBpZiAodGhpcy5wYXJ0R3JhZGUgPT09ICctMScpIHsNCiAgICAgICAgICByZXMgPSBhd2FpdCBHZXRDb21wVHlwZVRyZWUoeyBwcm9mZXNzaW9uYWw6IGNvZGUgfSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICByZXMgPSBhd2FpdCBHZXRQYXJ0VHlwZVRyZWUoeyBwcm9mZXNzaW9uYWxJZDogdGhpcy50eXBlSWQsIHBhcnRHcmFkZTogdGhpcy5wYXJ0R3JhZGUgfSkNCiAgICAgICAgfQ0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMuT2JqZWN0VHlwZUxpc3QuZGF0YSA9IHJlcy5EYXRhDQogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKF8pID0+IHsNCiAgICAgICAgICAgIHRoaXMuJHJlZnMudHJlZVNlbGVjdE9iamVjdFR5cGUudHJlZURhdGFVcGRhdGVGdW4ocmVzLkRhdGEpDQogICAgICAgICAgfSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy5PYmplY3RUeXBlTGlzdC5kYXRhID0gW10NCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoXykgPT4gew0KICAgICAgICAgICAgdGhpcy4kcmVmcy50cmVlU2VsZWN0T2JqZWN0VHlwZS50cmVlRGF0YVVwZGF0ZUZ1bihbXSkNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOajgOafpemhueiuvue9rumDqOWIhg0KICAgIGFkZFRhYmxlRGF0YSgpIHsNCiAgICAgIHRoaXMuUHJvY2Vzc0Zsb3cucHVzaCh7DQogICAgICAgIENoZWNrX0l0ZW1fSWQ6ICcnLA0KICAgICAgICBFbGlnaWJpbGl0eV9Dcml0ZXJpYTogJycsDQogICAgICAgIFF1ZXN0aW9ubGFiX0lkOiAnJw0KICAgICAgfSkNCiAgICAgIGNvbnNvbGUubG9nKCdhZGRUYWJsZURhdGEoKScsIHRoaXMuUHJvY2Vzc0Zsb3cpDQogICAgfSwNCiAgICBkZWxldGVSb3coaW5kZXgsIHJvd3MpIHsNCiAgICAgIGNvbnNvbGUubG9nKGluZGV4KQ0KICAgICAgcm93cy5zcGxpY2UoaW5kZXgsIDEpDQogICAgICBjb25zb2xlLmxvZyh0aGlzLlByb2Nlc3NGbG93KQ0KICAgICAgaWYgKHRoaXMuUHJvY2Vzc0Zsb3cubGVuZ3RoID4gMCAmJiBpbmRleCAhPT0gdGhpcy5Qcm9jZXNzRmxvdy5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuUHJvY2Vzc0Zsb3dbaW5kZXhdLCAnc29ydCcsIGluZGV4KQ0KICAgICAgfQ0KICAgIH0sDQogICAgbW92ZVVwd2FyZChyb3csIGluZGV4KSB7DQogICAgICBjb25zb2xlLmxvZyhpbmRleCkNCiAgICAgIGNvbnN0IHVwRGF0YSA9IHRoaXMuUHJvY2Vzc0Zsb3dbaW5kZXggLSAxXQ0KICAgICAgdGhpcy5Qcm9jZXNzRmxvdy5zcGxpY2UoaW5kZXggLSAxLCAxKQ0KICAgICAgdGhpcy5Qcm9jZXNzRmxvdy5zcGxpY2UoaW5kZXgsIDAsIHVwRGF0YSkNCiAgICAgIHRoaXMuJHNldCh0aGlzLlByb2Nlc3NGbG93W2luZGV4IC0gMV0sICdzb3J0JywgaW5kZXggLSAxKQ0KICAgICAgdGhpcy4kc2V0KHRoaXMuUHJvY2Vzc0Zsb3dbaW5kZXhdLCAnc29ydCcsIGluZGV4KQ0KICAgICAgY29uc29sZS5sb2codGhpcy5Qcm9jZXNzRmxvdykNCiAgICB9LA0KICAgIG1vdmVEb3duKHJvdywgaW5kZXgpIHsNCiAgICAgIGNvbnNvbGUubG9nKGluZGV4KQ0KICAgICAgY29uc3QgZG93bkRhdGEgPSB0aGlzLlByb2Nlc3NGbG93W2luZGV4ICsgMV0NCiAgICAgIHRoaXMuUHJvY2Vzc0Zsb3cuc3BsaWNlKGluZGV4ICsgMSwgMSkNCiAgICAgIHRoaXMuUHJvY2Vzc0Zsb3cuc3BsaWNlKGluZGV4LCAwLCBkb3duRGF0YSkNCiAgICAgIGNvbnNvbGUubG9nKHRoaXMuUHJvY2Vzc0Zsb3cpDQogICAgICB0aGlzLiRzZXQodGhpcy5Qcm9jZXNzRmxvd1tpbmRleF0sICdzb3J0JywgaW5kZXgpDQogICAgICB0aGlzLiRzZXQodGhpcy5Qcm9jZXNzRmxvd1tpbmRleCArIDFdLCAnc29ydCcsIGluZGV4ICsgMSkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["CombinationDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAk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file": "CombinationDialog.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup/components/Dialog", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"130px\">\r\n      <el-row>\r\n        <el-form-item label=\"检查项组合名称\" prop=\"Group_Name\">\r\n          <el-input v-model=\"form.Group_Name\" />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"质检节点\" prop=\"Check_Node_Id\">\r\n          <el-select\r\n            v-model=\"form.Check_Node_Id\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n            placeholder=\"请选择质检节点\"\r\n            @change=\"changeNode\"\r\n          >\r\n            <el-option\r\n              v-for=\"(item, index) in CheckNodeList\"\r\n              :key=\"index\"\r\n              :label=\"item.Display_Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"质检类型\" prop=\"Check_Type\">\r\n          <el-select\r\n            v-model=\"Change_Check_Type\"\r\n            clearable\r\n            multiple\r\n            :multiple-limit=\"1\"\r\n            style=\"width: 100%\"\r\n            :disabled=\"Isdisable\"\r\n            placeholder=\"请选择质检类型\"\r\n            @change=\"SelectType\"\r\n          >\r\n            <el-option\r\n              v-for=\"(item, index) in QualityTypeList\"\r\n              :key=\"index\"\r\n              :label=\"item.Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"专业类别\" prop=\"Pro_Category_Id\">\r\n          <el-select\r\n            v-model=\"form.Pro_Category_Id\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n            placeholder=\"请选择专业类别\"\r\n            @change=\"changeCategory\"\r\n          >\r\n            <el-option\r\n              v-for=\"(item, index) in ProCategoryList\"\r\n              :key=\"index\"\r\n              :label=\"item.Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"检查类型\" prop=\"Questionlab_Ids\">\r\n          <el-select\r\n            v-model=\"form.Questionlab_Ids\"\r\n            style=\"width: 100%\"\r\n            multiple\r\n            placeholder=\"请选择检查类型\"\r\n            @change=\"ChangeCheckType\"\r\n            @remove-tag=\"removeCheckType\"\r\n          >\r\n            <el-option\r\n              v-for=\"(item, index) in CheckTypeList\"\r\n              :key=\"index\"\r\n              :label=\"item.Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item\r\n          label=\"产品类型\"\r\n          prop=\"Object_Type_Ids\"\r\n        >\r\n          <el-tree-select\r\n            ref=\"treeSelectObjectType\"\r\n            v-model=\"form.Object_Type_Ids\"\r\n            :disabled=\"!Boolean(form.Pro_Category_Id)\"\r\n            class=\"cs-tree-x\"\r\n            :tree-params=\"ObjectTypeList\"\r\n            value-key=\"Id\"\r\n            @removeTag=\"removeTagFn\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-col :span=\"24\">\r\n          <h3>检查项设置</h3>\r\n          <el-form-item label=\"\" prop=\"\" class=\"checkItem\">\r\n            <el-table :data=\"ProcessFlow\" border style=\"width: 100%\">\r\n              <el-table-column prop=\"\" label=\"*检查类型\" align=\"center\">\r\n                <template slot-scope=\"{ row }\">\r\n                  <el-select\r\n                    v-model=\"row.Questionlab_Id\"\r\n                    style=\"width: 100%\"\r\n                    clearable\r\n                    placeholder=\"请选择\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"(item, index) in CheckTypeList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.Name\"\r\n                      :value=\"item.Id\"\r\n                    />\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"\" label=\"*检查项内容\" align=\"center\">\r\n                <template slot-scope=\"{ row, $index }\">\r\n                  <el-select\r\n                    v-model=\"row.Check_Item_Id\"\r\n                    style=\"width: 100%\"\r\n                    clearable\r\n                    placeholder=\"请选择\"\r\n                    @change=\"ChangeItem($event, $index, row)\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"(item, index) in CheckItemList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.Check_Content\"\r\n                      :value=\"item.Id\"\r\n                    />\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"\" label=\"*合格标准\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-input\r\n                    v-model=\"scope.row.Eligibility_Criteria\"\r\n                    disabled\r\n                  />\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"address\"\r\n                label=\"操作\"\r\n                width=\"140\"\r\n                align=\"center\"\r\n              >\r\n                <template slot-scope=\"{ row, $index }\">\r\n                  <el-button\r\n                    type=\"text\"\r\n                    icon=\"el-icon-top\"\r\n                    :disabled=\"$index == 0\"\r\n                    @click=\"moveUpward(row, $index)\"\r\n                  />\r\n                  <el-button\r\n                    type=\"text\"\r\n                    icon=\"el-icon-bottom\"\r\n                    :disabled=\"$index == ProcessFlow.length - 1\"\r\n                    @click=\"moveDown(row, $index)\"\r\n                  />\r\n                  <el-button\r\n                    type=\"text\"\r\n                    icon=\"el-icon-delete\"\r\n                    style=\"color: #f56c6c\"\r\n                    @click.native.prevent=\"deleteRow($index, ProcessFlow)\"\r\n                  />\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"24\">\r\n          <el-button\r\n            type=\"text\"\r\n            class=\"addcheckItem\"\r\n            @click=\"addTableData\"\r\n          >+ 新增检查项</el-button>\r\n        </el-col>\r\n        <el-col :span=\"24\" style=\"text-align: right\">\r\n          <el-form-item style=\"text-align: right\">\r\n            <el-button @click=\"$emit('close')\">关 闭</el-button>\r\n            <el-button\r\n              type=\"primary\"\r\n              @click=\"handleSubmit('form')\"\r\n            >确 定</el-button>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { AddCheckItemCombination } from '@/api/PRO/factorycheck'\r\nimport { EntityQualityList } from '@/api/PRO/factorycheck'\r\n\r\nimport { GetCheckTypeList } from '@/api/PRO/factorycheck'\r\nimport { GetCheckItemList } from '@/api/PRO/factorycheck'\r\nimport { GetNodeList } from '@/api/PRO/factorycheck'\r\nimport { GetCompTypeTree } from '@/api/PRO/factorycheck'\r\nimport {\r\n  GetFactoryProfessionalByCode,\r\n  GetMaterialType\r\n} from '@/api/PRO/factorycheck'\r\nimport { GetPartTypeTree } from '@/api/PRO/partType'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      mode: '', // 区分项目和工厂\r\n      ProjectId: '', // 项目Id\r\n      Check_Object_Id: '',\r\n      checkType: {}, // 区分构件、零件、物料\r\n      form: {\r\n        Object_Type_Ids: []\r\n      },\r\n      rules: {\r\n        Check_Content: [\r\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\r\n        ],\r\n        Eligibility_Criteria: [\r\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\r\n        ],\r\n        Group_Name: [\r\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\r\n        ],\r\n        Check_Type: [\r\n          { required: true, message: '请填写完整表单', trigger: 'change' }\r\n        ],\r\n        Questionlab_Ids: [\r\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\r\n        ]\r\n      },\r\n      title: '',\r\n      options: [],\r\n      ProcessFlow: [],\r\n      CheckTypeList: [], // 检查类型下拉\r\n      CheckItemList: [], // 检查项下拉\r\n      Change_Check_Type: [],\r\n      QualityTypeList: [\r\n        {\r\n          Name: '质量',\r\n          Id: 1\r\n        },\r\n        {\r\n          Name: '探伤',\r\n          Id: 2\r\n        }\r\n      ], // 质检类型\r\n      ProCategoryList: [], // 专业类别下拉\r\n      CheckNodeList: [], // 质检节点下拉\r\n      verification: false,\r\n      ProCategoryCode: '', // 专业类别Code\r\n      Eligibility_Criteria: '',\r\n      ObjectTypeList: {\r\n        // 对象类型\r\n        'check-strictly': true,\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      Isdisable: false,\r\n      typeCode: '',\r\n      typeId: '',\r\n      partGrade: ''\r\n    }\r\n  },\r\n  watch: {\r\n    ProcessFlow: {\r\n      handler(newName, oldName) {\r\n        console.log(newName)\r\n        this.form.Questionlab_Ids = []\r\n        this.ProcessFlow.forEach((item) => {\r\n          if (\r\n            item.Questionlab_Id &&\r\n            !this.form.Questionlab_Ids.includes(item.Questionlab_Id)\r\n          ) {\r\n            this.form.Questionlab_Ids.push(item.Questionlab_Id)\r\n          }\r\n        })\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  mounted() {},\r\n  methods: {\r\n    async init(title, checkType, data) {\r\n      this.partGrade = checkType.Code\r\n      this.Check_Object_Id = checkType.Id\r\n      this.checkType = checkType\r\n      this.title = title\r\n      this.form.Check_Object_Id = checkType.Id\r\n      this.form.Bom_Level = checkType.Code\r\n      await this.getProfessionalType() // 专业类别\r\n      await this.getCheckTypeList() // 检查类型\r\n      await this.getCheckItemList()\r\n      await this.getNodeList(data) // 质检节点\r\n    },\r\n    async addCheckItemCombination() {\r\n      await AddCheckItemCombination({\r\n        Group: this.form,\r\n        Items: this.ProcessFlow\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            type: 'success',\r\n            message: '保存成功'\r\n          })\r\n          this.$emit('close')\r\n          this.dialogData = {}\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    removeTagFn(ids, tag) {\r\n      console.log('ids', ids)\r\n      console.log('tag', tag)\r\n    },\r\n    SelectType(item) {\r\n      console.log('item', item)\r\n\r\n      if (item.length === 1) {\r\n        this.form.Check_Type = item[0]\r\n      } else {\r\n        this.form.Check_Type = -1\r\n      }\r\n      console.log('this.form.Check_Type', this.form.Check_Type)\r\n    },\r\n    changeNode(val) {\r\n      console.log(val)\r\n      console.log(this.CheckNodeList)\r\n      if (val) {\r\n        this.form.Check_Type = this.CheckNodeList.find((v) => {\r\n          return v.Id === val\r\n        }).Check_Type\r\n        // 处理质检类型数据\r\n\r\n        this.Change_Check_Type = []\r\n        if (this.form.Check_Type === 1 || this.form.Check_Type === 2) {\r\n          this.Isdisable = true\r\n          this.Change_Check_Type.push(this.form.Check_Type)\r\n        } else if (this.form.Check_Type === -1) {\r\n          this.Isdisable = false // 质检类型可编辑\r\n          this.Change_Check_Type = []\r\n        } else {\r\n          this.Change_Check_Type = []\r\n          this.Isdisable = false\r\n        }\r\n        console.log(' this.Isdisable', this.Isdisable)\r\n      } else {\r\n        this.Change_Check_Type = []\r\n      }\r\n    },\r\n    getEntityCheckType(data) {\r\n      console.log(data)\r\n      EntityQualityList({\r\n        id: data.Id,\r\n        check_object_id: this.Check_Object_Id\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.form = res.Data[0].Group\r\n          console.log(this.form.Object_Type_Ids, 'Object_Type_Ids')\r\n\r\n          this.ProcessFlow = res.Data[0].Items\r\n          this.Change_Check_Type = []\r\n          // 处理质检类型数据\r\n          if (this.form.Check_Type === 1 || this.form.Check_Type === 2) {\r\n            this.Change_Check_Type.push(this.form.Check_Type)\r\n            if (res.Data[0].CheckNode_Type === -1) {\r\n              this.Isdisable = false\r\n            } else {\r\n              this.Isdisable = true\r\n            }\r\n          } else if (this.form.Check_Type === -1) {\r\n            this.Change_Check_Type = [1, 2]\r\n            this.Isdisable = true // 质检类型不可编辑\r\n          } else {\r\n            this.Change_Check_Type = []\r\n            this.Isdisable = false\r\n          }\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleSubmit(form) {\r\n      if (this.Change_Check_Type.length === 0) {\r\n        this.$message({\r\n          type: 'error',\r\n          message: '请选择检查类型'\r\n        })\r\n        return\r\n      }\r\n      let verification = true\r\n      if (this.ProcessFlow.length === 0) {\r\n        verification = false\r\n      } else {\r\n        this.ProcessFlow.forEach((val) => {\r\n          for (const key in val) {\r\n            if (val[key] === '') {\r\n              verification = false\r\n            }\r\n          }\r\n        })\r\n      }\r\n      if (!verification) {\r\n        this.$message({\r\n          type: 'error',\r\n          message: '请填写完整检查项设置内容'\r\n        })\r\n        return\r\n      }\r\n\r\n      const processFlowCopy = JSON.parse(JSON.stringify(this.ProcessFlow))\r\n      const processFlowNew = []\r\n      processFlowCopy.forEach((item) => {\r\n        const processFlowJson = {}\r\n        processFlowJson.Check_Item_Id = item.Check_Item_Id\r\n        processFlowJson.Eligibility_Criteria = item.Eligibility_Criteria\r\n        processFlowJson.Questionlab_Id = item.Questionlab_Id\r\n        processFlowNew.push(processFlowJson)\r\n      })\r\n      const processFlowTemp = processFlowNew.map((item) => {\r\n        return JSON.stringify(item)\r\n      })\r\n      if (new Set(processFlowTemp).size !== processFlowTemp.length) {\r\n        this.$message({\r\n          type: 'error',\r\n          message: '检查项设置内容不能完全相同'\r\n        })\r\n        return\r\n      }\r\n\r\n      const processFlowArr = this.ProcessFlow.map((v) => {\r\n        return v.Questionlab_Id\r\n      })\r\n\r\n      const isIncludes = this.form.Questionlab_Ids.every((item) =>\r\n        processFlowArr.includes(item)\r\n      )\r\n      if (!isIncludes) {\r\n        this.$message({\r\n          type: 'error',\r\n          message: '检查项设置必须包含已选检查类型'\r\n        })\r\n        return\r\n      }\r\n\r\n      this.$refs[form].validate((valid) => {\r\n        if (valid) {\r\n          this.addCheckItemCombination()\r\n        } else {\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    // 获取专业类别\r\n    async getProfessionalType() {\r\n      const Platform =\r\n        localStorage.getItem('Platform') || localStorage.getItem('CurPlatform')\r\n      if (Platform === '2') {\r\n        this.mode = 'factory'\r\n        await GetFactoryProfessionalByCode().then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.ProCategoryList = res.Data\r\n            const {\r\n              Code,\r\n              Id\r\n            } = res.Data?.find(item => item.Code === 'Steel') || {}\r\n            this.typeCode = Code\r\n            this.typeId = Id\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        })\r\n      }\r\n\r\n      // 获取项目/工厂id\r\n      this.ProjectId =\r\n        this.mode === 'factory'\r\n          ? localStorage.getItem('CurReferenceId')\r\n          : this.ProjectId\r\n    },\r\n\r\n    // 获取检查类型下拉框\r\n    async getCheckTypeList() {\r\n      await GetCheckTypeList({ check_object_id: this.Check_Object_Id, Bom_Level: this.form.Bom_Level }).then(\r\n        (res) => {\r\n          if (res.IsSucceed) {\r\n            this.CheckTypeList = res.Data\r\n            console.log(res.Data)\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        }\r\n      )\r\n    },\r\n    // 检查项内容\r\n    async getCheckItemList() {\r\n      await GetCheckItemList({ check_object_id: this.Check_Object_Id }).then(\r\n        (res) => {\r\n          if (res.IsSucceed) {\r\n            this.CheckItemList = res.Data\r\n            console.log(res.Data)\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        }\r\n      )\r\n    },\r\n    // 通过专业类别选择对象类型\r\n    changeCategory(val) {\r\n      this.form.Object_Type_Ids = []\r\n      this.chooseType(val)\r\n    },\r\n    // 通过专业类别选择对象类型\r\n    chooseType(val) {\r\n      console.log(this.ProCategoryList)\r\n      this.ProCategoryCode = this.ProCategoryList.find((v) => {\r\n        return v.Id === val\r\n      }).Code\r\n      this.getObjectTypeList(this.ProCategoryCode) // 对象类型\r\n    },\r\n    // 选中表格外检查类型\r\n    ChangeCheckType(val) {\r\n      const arrJson = Object.assign([], val)\r\n      // let index = arrJson.indexOf(Isexist);\r\n      // this.ProcessFlow.splice(index, 1);\r\n      console.log(arrJson)\r\n      if (this.ProcessFlow.length > arrJson.length) {\r\n        const arrJsonTemp = arrJson.map((item) => {\r\n          const itemField = {\r\n            Check_Item_Id: '',\r\n            Eligibility_Criteria: '',\r\n            Questionlab_Id: item\r\n          }\r\n          this.ProcessFlow.forEach((items) => {\r\n            if (items.Questionlab_Id === item) {\r\n              itemField.Check_Item_Id = items.Check_Item_Id\r\n              itemField.Eligibility_Criteria = items.Eligibility_Criteria\r\n            }\r\n          })\r\n\r\n          return itemField\r\n        })\r\n        this.ProcessFlow = [].concat(arrJsonTemp)\r\n      } else {\r\n        for (var i = 0; i < arrJson.length; i++) {\r\n          const Isexist = this.ProcessFlow.find((v) => {\r\n            return v.Questionlab_Id === arrJson[i]\r\n          })\r\n          if (!Isexist) {\r\n            this.ProcessFlow.push({\r\n              Questionlab_Id: arrJson[i],\r\n              Check_Item_Id: '',\r\n              Eligibility_Criteria: ''\r\n            })\r\n          }\r\n        }\r\n      }\r\n\r\n      console.log('ChangeCheckType()', this.ProcessFlow)\r\n    },\r\n\r\n    removeCheckType(val) {\r\n      const Isexist = this.ProcessFlow.find((v) => {\r\n        return v.Questionlab_Id === val\r\n      })\r\n      const index = this.ProcessFlow.indexOf(Isexist)\r\n      if (Isexist) {\r\n        this.ProcessFlow.splice(index, 1)\r\n      }\r\n    },\r\n    // 选中检查项内容\r\n    ChangeItem(data, index, row) {\r\n      // console.log(data);\r\n      // console.log(index);\r\n      // console.log(row)\r\n      // console.log(this.CheckItemList);\r\n      row.Eligibility_Criteria = ''\r\n      this.Eligibility_Criteria = ''\r\n      this.Eligibility_Criteria = this.CheckItemList.find((v) => {\r\n        return v.Id === data\r\n      })?.Eligibility_Criteria\r\n      this.$set(\r\n        this.ProcessFlow[index],\r\n        'Eligibility_Criteria',\r\n        this.Eligibility_Criteria\r\n      )\r\n      this.$set(this.ProcessFlow[index], 'sort', index)\r\n      console.log(this.ProcessFlow)\r\n    },\r\n\r\n    async editHandleData(data) {\r\n      if (this.title === '编辑') {\r\n        console.log('data', data)\r\n        this.form.Id = data.Id\r\n        this.getEntityCheckType(data)\r\n        await this.chooseType(data.Pro_Category_Id)\r\n      }\r\n    },\r\n\r\n    // 质检节点下拉菜单\r\n    async getNodeList(data) {\r\n      await GetNodeList({ check_object_id: this.Check_Object_Id, Bom_Level: this.form.Bom_Level }).then(\r\n        (res) => {\r\n          if (res.IsSucceed) {\r\n            this.CheckNodeList = res.Data\r\n            this.editHandleData(data)\r\n            console.log(res.Data)\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        }\r\n      )\r\n    },\r\n    // 对象类型下拉\r\n    async getObjectTypeList(code) {\r\n      if (this.checkType.Display_Name === '物料') {\r\n        GetMaterialType({}).then((res) => {\r\n          this.ObjectTypeList = res.Data\r\n        })\r\n      } else {\r\n        let res\r\n        if (this.partGrade === '-1') {\r\n          res = await GetCompTypeTree({ professional: code })\r\n        } else {\r\n          res = await GetPartTypeTree({ professionalId: this.typeId, partGrade: this.partGrade })\r\n        }\r\n        if (res.IsSucceed) {\r\n          this.ObjectTypeList.data = res.Data\r\n          this.$nextTick((_) => {\r\n            this.$refs.treeSelectObjectType.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.ObjectTypeList.data = []\r\n          this.$nextTick((_) => {\r\n            this.$refs.treeSelectObjectType.treeDataUpdateFun([])\r\n          })\r\n        }\r\n      }\r\n    },\r\n\r\n    // 检查项设置部分\r\n    addTableData() {\r\n      this.ProcessFlow.push({\r\n        Check_Item_Id: '',\r\n        Eligibility_Criteria: '',\r\n        Questionlab_Id: ''\r\n      })\r\n      console.log('addTableData()', this.ProcessFlow)\r\n    },\r\n    deleteRow(index, rows) {\r\n      console.log(index)\r\n      rows.splice(index, 1)\r\n      console.log(this.ProcessFlow)\r\n      if (this.ProcessFlow.length > 0 && index !== this.ProcessFlow.length) {\r\n        this.$set(this.ProcessFlow[index], 'sort', index)\r\n      }\r\n    },\r\n    moveUpward(row, index) {\r\n      console.log(index)\r\n      const upData = this.ProcessFlow[index - 1]\r\n      this.ProcessFlow.splice(index - 1, 1)\r\n      this.ProcessFlow.splice(index, 0, upData)\r\n      this.$set(this.ProcessFlow[index - 1], 'sort', index - 1)\r\n      this.$set(this.ProcessFlow[index], 'sort', index)\r\n      console.log(this.ProcessFlow)\r\n    },\r\n    moveDown(row, index) {\r\n      console.log(index)\r\n      const downData = this.ProcessFlow[index + 1]\r\n      this.ProcessFlow.splice(index + 1, 1)\r\n      this.ProcessFlow.splice(index, 0, downData)\r\n      console.log(this.ProcessFlow)\r\n      this.$set(this.ProcessFlow[index], 'sort', index)\r\n      this.$set(this.ProcessFlow[index + 1], 'sort', index + 1)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n::v-deep {\r\n  .checkItem {\r\n    width: 100%;\r\n    .el-form-item__content {\r\n      margin-left: 0 !important;\r\n    }\r\n  }\r\n  .addcheckItem {\r\n    font-size: 16px;\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n::v-deep .el-form-item {\r\n  display: inline-block;\r\n  .el-form-item__content {\r\n    & > .el-input {\r\n      width: 220px !important;\r\n    }\r\n    & > .el-select {\r\n      width: 220px !important;\r\n    }\r\n    .el-tree-select-input {\r\n      width: 220px !important;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
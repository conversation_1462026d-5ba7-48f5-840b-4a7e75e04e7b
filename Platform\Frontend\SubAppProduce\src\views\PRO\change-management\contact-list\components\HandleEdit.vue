<template>
  <div class="handle-edit-container">
    <div class="handle-edit">
      <el-button v-if="!list.length" type="primary" icon="el-icon-plus" @click="handleAdd(-1)">添加</el-button>
      <div v-for="(item, index) in list" :key="index" class="flex-row">
        <div class="flex-item flex-item-1">
          <el-input
            v-model="item.Value"
            style="width: 100%;"
            clearable
            readonly
            placeholder="请输入"
            class="input-with-select"
          >
            <el-select
              slot="prepend"
              v-model="item.Code"
              placeholder="请选择"
              style="width: 160px"
              @change="selectChange($event,item)"
            >
              <el-option v-for="option in getAvailableOptions(index)" :key="option.Code" :label="option.Name" :value="option.Code" />
            </el-select>

          </el-input>
        </div>
        <div class="flxe-item2">
          <span>变更后：</span>
        </div>
        <div class="flex-item">

          <el-tree-select
            v-if="item.Code === 'SteelType'"
            ref="treeSelectObjectType1"
            v-model="item.NewValue"
            class="cs-tree-x"
            style="width: 100%;"
            :select-params="treeSelectParams"
            :tree-params="ObjectTypeList"
            value-key="Id"
          />
          <el-select v-else-if="item.Code==='PartType'" v-model="item.NewValue" placeholder="请选择" clearable="">
            <el-option
              v-for="cur in partTypeOption"
              :key="cur.Code"
              :label="cur.Name"
              :value="cur.Code"
            />
          </el-select>
          <el-select v-else-if="item.Code==='Technology_Code'" v-model="item.NewValue" placeholder="请选择" clearable="">
            <el-option
              v-for="cur in processOption"
              :key="cur.Code"
              :label="cur.Code"
              :value="cur.Code"
            />
          </el-select>
          <el-input-number
            v-else-if="item.Field_Type==='number'"
            v-model="item.NewValue"
            :min="0"
            :precision="item.precision || 0"
            style="width: 100%"
            class="cs-number-btn-hidden"
          />
          <el-input v-else v-model.trim="item.NewValue" style="width: 100%;" clearable placeholder="请输入" />

        </div>
        <div class="flex-item3 btn-x">
          <el-button
            v-if="(list.length < options.length)"
            type="primary"
            icon="el-icon-plus"
            circle
            @click="handleAdd(index)"
          />
          <el-button
            type="danger"
            icon="el-icon-delete"
            circle
            @click="handleDelete(item, index)"
          />
        </div>
      </div>
    </div>
    <div class="btn-group">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :disabled="isSaveDisabled" @click="handleSave">保存</el-button>
    </div>
  </div>
</template>

<script>
import { allCodes, defaultPrefix, filterByCodeType, generateAllCodes, getAllCodesByType } from '../utils'
import { GetCompTypeTree } from '@/api/PRO/component-type'
import { GetPartTypeList } from '@/api/PRO/partType'
import { GetLibList } from '@/api/PRO/technology-lib'
import { deepClone } from '@/utils'
import { CheckCanMocName } from '@/api/PRO/changeManagement'

export default {

  data() {
    return {
      list: [{
        Value: '',
        NewValue: '',
        Field_Type: '',
        IsCoreField: '',
        precision: '',
        Code: ''
      }],
      options: [],
      partTypeOption: [],
      isSaveDisabled: false,
      processOption: [],
      treeSelectParams: {
        placeholder: '请选择',
        clearable: true
      },
      ObjectTypeList: {
        // 构件类型
        'check-strictly': true,
        'default-expand-all': true,
        clickParent: true,
        data: [],
        props: {
          children: 'Children',
          label: 'Label',
          value: 'Data'
        }
      }
    }
  },
  methods: {
    init(row, defaultRow, isEdit, tbData, allCodes) {
      const tbCode = tbData.map(item => item.CPCode)
      this._tbData = JSON.parse(JSON.stringify(tbData))
      generateAllCodes(allCodes)
      this.row = row
      this.tbCode = tbCode
      this.defaultRow = defaultRow
      console.log('isEdit', isEdit)
      console.log('row1', row)
      const _columns = filterByCodeType(row.CodeType)
      this.options = _columns.map(v => ({
        Code: v.Code,
        Name: v.Display_Name,
        disabled: false,
        Field_Type: v.Field_Type,
        precision: v.precision,
        IsCoreField: v.IsCoreField
      }))
      console.log('this.options', JSON.parse(JSON.stringify(this.options)))

      const changeCode = deepClone(this.$store.state.contactList.changeCode)
      if (changeCode[this.row.uuid]) {
        this.list = changeCode[this.row.uuid]
        const selectItem = this.list.filter(v => v.Field_Type === 'select')
        if (selectItem.length) {
          if (selectItem.some(v => v.Code === 'SteelType')) {
            this.getObjectTypeList()
          }
          if (selectItem.some(v => v.Code === 'PartType')) {
            this.getPartTypeList()
          }
          if (selectItem.some(v => v.Code === 'Technology_Code')) {
            this.getLibList()
          }
        }
      }
    },

    handleClose() {
      this.$emit('close')
    },
    handleAdd(index) {
      this.list.splice(index + 1, 0, {
        Value: '',
        NewValue: '',
        Field_Type: '',
        IsCoreField: '',
        precision: 0,
        Code: ''
      })
    },
    getObjectTypeList() {
      if (this.row.Type === 0) {
        GetCompTypeTree({ professional: 'Steel' }).then((res) => {
          if (res.IsSucceed) {
            this.ObjectTypeList.data = res.Data
            this.$nextTick((_) => {
              this.$refs.treeSelectObjectType1[0].treeDataUpdateFun(res.Data)
            })
          } else {
            this.$message({
              type: 'error',
              message: res.Message
            })
          }
        })
      } else {
        GetPartTypeList({ Part_Grade: 0 }).then(res => {
          if (res.IsSucceed) {
            this.partTypeOption = res.Data
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      }
    },
    selectChange(code, item) {
      console.log('code', code, item)
      const cur = this.options.find(v => v.Code === code)
      console.log(cur, 'cur')
      console.log(this.defaultRow, 'this.defaultRow')
      item.Field_Type = cur.Field_Type
      item.IsCoreField = cur.IsCoreField
      item.precision = cur.precision
      item.Name = cur.Name
      item.Value = this.defaultRow[code]
      item.NewValue = undefined
      if (code === 'SteelType' || code === 'PartType') {
        this.getObjectTypeList()
      }
      if (code === 'Technology_Code') {
        this.getLibList()
      }
    },
    handleDelete(element, index) {
      const idx = this.list.findIndex(v => v.Code === element.Code)
      console.log(idx, 'omd')
      if (idx !== -1) {
        this.list.splice(idx, 1)
      }
    },
    getAvailableOptions(currentIndex) {
      const selectedCodes = this.list
        .filter((item, idx) => idx !== currentIndex)
        .map(item => item.Code)
      return this.options.filter(option => !selectedCodes.includes(option.Code))
    },
    async handleSave() {
      let success = true
      const list = this.list.filter(item => !!item.Code)
      console.log(list, 'list')

      const isMustInputs = allCodes.filter(item => item.isMustInput).map(item => item.Code)
      console.log(isMustInputs, 'isMustInputs')
      list.forEach(item => {
        if (isMustInputs.includes(item.Code)) {
          if (!item.NewValue) {
            this.$message({
              message: '请输入' + item.Name,
              type: 'error'
            })
            success = false
            return
          }
        }
      })

      const isValid = await this.checkName()
      console.log('isValid', isValid)
      if (!isValid) {
        success = false
        return
      }

      // 根据Type判断唯一性字段：0-构件(SteelName), 1-部件(ComponentName), 2/3-零件(PartName)
      // let hasRepeat = false
      // let nameItem = null
      // let nameField = ''
      // if (this.row.Type === 0) {
      //   nameField = 'SteelName'
      // } else if (this.row.Type === 1) {
      //   nameField = 'ComponentName'
      // } else if (this.row.Type === 2 || this.row.Type === 3) {
      //   nameField = 'PartName'
      // }
      // nameItem = list.find(v => v.Code === nameField)
      // if (nameItem) {
      //   const newName = nameItem.NewValue?.trim()
      //   for (let i = 0; i < this._tbData.length; i++) {
      //     const item = this._tbData[i]
      //     if (item.CodeType === 3 || item.CodeType === 2) {
      //       if (item.Part_Aggregate_Id === this.row.Part_Aggregate_Id) {
      //         continue
      //       }
      //     } else if (item.CodeType === 1 && item.uuid === this.row.uuid) {
      //       continue
      //     }
      //     // 只比较同类型的唯一性字段
      //     if (item[nameField]?.trim() === newName && this.row.Type === item.Type) {
      //       hasRepeat = true
      //       break
      //     }
      //   }
      // }

      if (!success) return

      // this.$store.dispatch('contactList/addChangeCode', { uuid: this.row.uuid, list: list })
      this.$emit('editInfo', { row: this.row, list: list })
      this.$emit('close')
    },
    async checkName() {
      const item = this.list.find(v => v.Code === 'SteelName' || v.Code === 'ComponentName' || v.Code === 'PartName')
      if (!item) return true
      let flag = true
      await CheckCanMocName({
        Type: this.row.Type,
        Id: this.row.Type === 0 ? this.row.MocIdBefore : this.row.MocAggregateIdBefore,
        NewName: item.NewValue
      }).then(res => {
        if (res.IsSucceed) {
          flag = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
          flag = false
        }
      })
      if (!flag) {
        return
      }
      const key = this.row.Type === 0 ? 'SteelName' : this.row.Type === 1 ? 'ComponentName' : 'PartName'
      const hasSimilar = this.findSimilarTypeItems(this.row, key, item.NewValue)
      console.log('hasSimilar', hasSimilar, item.NewValue)
      if (hasSimilar) {
        this.$message({
          message: `${this.row.Type === 0 ? '构件' : this.row.Type === 1 ? '部件' : '零件'}名称已存在，请修改`,
          type: 'error'
        })
        flag = false
      }
      return flag
    },
    findSimilarTypeItems(targetItem, key, newName) {
      let flag = false
      for (let i = 0; i < this._tbData.length; i++) {
        const item = this._tbData[i]
        if (item.uuid === targetItem.uuid) continue
        if (item.CodeType !== targetItem.CodeType) continue
        if (item.Type === 1 || item.Type === 2 || item.Type === 3) {
          if ((targetItem.MocAggregateIdBefore !== item.MocAggregateIdBefore) && (item[key] === newName)) {
            flag = true
          }
        } else if (item.Type === 0) {
          if (item[key] === newName) {
            flag = true
          }
        }
      }
      return flag
    },
    handleCancel() {
      this.$emit('close')
    },
    getLibList() {
      GetLibList({
        type: this.row.Type === 0 ? 1 : this.row.Type === 1 ? 3 : 2
      }).then(res => {
        if (res.IsSucceed) {
          this.processOption = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.handle-edit-container {
  position: relative;
  .handle-edit {
    max-height: 50vh;
    overflow-y: auto;
    .flex-row {
      display: flex;
      align-items: center;
    justify-content: space-between;
    .flex-item {
      flex: 2;
      margin: 10px 0;
    }
    .flex-item-1 {
      flex: 3;
    }
    .flxe-item2 {
      margin:0 10px;
    }
    .flex-item3 {
      flex: 1;
    }
    .btn-x {
      margin-left: 10px;

    }
  }

}
.btn-group {
    display: flex;
    justify-content: flex-end;
    padding-top: 10px;
    background: #fff;
  }
}
.cs-tree-x {
  ::v-deep {
    .el-select {
      width: 100%;
    }
  }
}
</style>

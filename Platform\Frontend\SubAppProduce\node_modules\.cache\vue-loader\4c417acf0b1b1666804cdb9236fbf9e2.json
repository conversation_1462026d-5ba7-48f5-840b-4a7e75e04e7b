{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\plan\\setting.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\plan\\setting.vue", "mtime": 1757572678863}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRDb25maWdzLCBHZXRDdXJyQ29tcGFueVByb2plY3RMaXN0LCBTYXZlQ29uZmlncyB9IGZyb20gJ0AvYXBpL3BsbS9wcm9qZWN0cycNCmltcG9ydCB7IEdldFVzZXJQYWdlIH0gZnJvbSAnQC9hcGkvc3lzJw0KaW1wb3J0IHsgR2V0V2FybkNvbmZpZ3MsIFNhdmVXYXJuQ29uZmlncywgUmVzZXRXYXJuQ29uZmlnLCBTeW5jQXJlYVdhcm5Db25maWcgfSBmcm9tICdAL2FwaS9QUk8vY29udHJvbC1wbGFuJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHVzZXJMaXN0OiBbXSwNCiAgICAgIG5vZGVEYXRhOiBbXSwNCiAgICAgIGNvbXBhbnlJZDogbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ0N1clJlZmVyZW5jZUlkJyksDQogICAgICB0cmVlRGF0YTogW10sDQogICAgICB0cmVlUHJvcHM6IHsNCiAgICAgICAgbGFiZWw6ICdTaG9ydF9OYW1lJywNCiAgICAgICAgaWQ6ICdTeXNfUHJvamVjdF9JZCcNCiAgICAgIH0sDQogICAgICB0cmVlRGVmYXVsdFNlbGVjdGVkS2V5OiAnJywNCiAgICAgIHBhcmFtczogew0KICAgICAgICBDb21wYW55SWQ6IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdDdXJSZWZlcmVuY2VJZCcpLA0KICAgICAgICBQcm9qZWN0SWQ6ICcnLA0KICAgICAgICBBcmVhSWQ6ICcnDQogICAgICB9LA0KICAgICAgcmVzZXRMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHN5bmNMb2FkaW5nOiBmYWxzZSwNCiAgICAgIGFjdGl2ZVRhYjogJycsDQogICAgICB3YXJuUnVsZXM6IHt9IC8vIOWtmOWCqOWQhOS4quiuoeWIkuexu+Wei+eahOmihOitpuinhOWImQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICBzaG93UHJvamVjdEJ1dHRvbnMoKSB7DQogICAgICAvLyDlj6rmnInlnKjpgInkuK3pobnnm67nuqfoioLngrnvvIjpnZ7pu5jorqTphY3nva7kuJTpnZ7ljLrln5/vvInml7bmiY3mmL7npLrmjInpkq4NCiAgICAgIHJldHVybiB0aGlzLnBhcmFtcy5Qcm9qZWN0SWQgIT09ICcnICYmDQogICAgICAgICAgICAgIHRoaXMucGFyYW1zLlByb2plY3RJZCAhPT0gJ2RlZmF1bHRfY29uZmlnJyAmJg0KICAgICAgICAgICAgICB0aGlzLnBhcmFtcy5BcmVhSWQgPT09ICcnDQogICAgfSwNCiAgICBzaG93UGxhbkNvbmZpZygpIHsNCiAgICAgIC8vIOWPquacieWcqOm7mOiupOmFjee9ruaXtuaJjeaYvuekuuiuoeWIkumFjee9rg0KICAgICAgcmV0dXJuIHRoaXMucGFyYW1zLlByb2plY3RJZCA9PT0gJycgJiYgdGhpcy5wYXJhbXMuQXJlYUlkID09PSAnJw0KICAgIH0sDQogICAgc2hvd1dhcm5Db25maWcoKSB7DQogICAgICAvLyDpooTorabop4TliJnml6DorrrkvZXnp43mg4XlhrXpg73mmL7npLoNCiAgICAgIHJldHVybiB0cnVlDQogICAgfSwNCiAgICBjdXJyZW50V2FyblJ1bGVzKCkgew0KICAgICAgLy8g6I635Y+W5b2T5YmN6YCJ5LitdGFi55qE6aKE6K2m6KeE5YiZDQogICAgICBpZiAoIXRoaXMuYWN0aXZlVGFiIHx8ICF0aGlzLndhcm5SdWxlc1t0aGlzLmFjdGl2ZVRhYl0pIHsNCiAgICAgICAgcmV0dXJuIFtdDQogICAgICB9DQogICAgICByZXR1cm4gdGhpcy53YXJuUnVsZXNbdGhpcy5hY3RpdmVUYWJdDQogICAgfQ0KICB9LA0KICBhc3luYyBjcmVhdGVkKCkgew0KICAgIGF3YWl0IHRoaXMuZ2V0VHJlZURhdGEoKQ0KICAgIGF3YWl0IHRoaXMuZ2V0TGlzdCgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBub2RlQ2xpY2sobm9kZSkgew0KICAgICAgdGhpcy5jdXJQcm9qZWN0ID0gbm9kZQ0KDQogICAgICAvLyDliKTmlq3mmK/lkKbkuLrpu5jorqTphY3nva7oioLngrkNCiAgICAgIGlmIChub2RlLmlzRGVmYXVsdCkgew0KICAgICAgICB0aGlzLnBhcmFtcy5Qcm9qZWN0SWQgPSAnJw0KICAgICAgICB0aGlzLnBhcmFtcy5BcmVhSWQgPSAnJw0KICAgICAgICAvLyDpu5jorqTphY3nva7ml7bkuZ/pnIDopoHor7fmsYLpooTorabop4TliJnmlbDmja4NCiAgICAgICAgaWYgKHRoaXMubm9kZURhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoaXMuYWN0aXZlVGFiID0gdGhpcy5ub2RlRGF0YVswXS5QbGFuX1R5cGUudG9TdHJpbmcoKQ0KICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuZ2V0V2FyblJ1bGVzKCkNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICAvLyDliKTmlq3oioLngrnlsYLnuqcNCiAgICAgIC8vIOWmguaenOiKgueCueayoeacieeItue6p0lE5oiW54i257qnSUTkuLrnqbrvvIzor7TmmI7mmK/nrKzkuIDnuqfvvIjpobnnm67vvIkNCiAgICAgIGlmICghbm9kZS5QYXJlbnRfSWQgfHwgbm9kZS5QYXJlbnRfSWQgPT09ICcnIHx8IG5vZGUuUGFyZW50X0lkID09PSBudWxsKSB7DQogICAgICAgIC8vIOesrOS4gOe6p+iKgueCue+8muiuvue9rlByb2plY3RJZOS4uuW9k+WJjeiKgueCuUlE77yMQXJlYUlk5Li656m6DQogICAgICAgIHRoaXMucGFyYW1zLlByb2plY3RJZCA9IG5vZGUuU3lzX1Byb2plY3RfSWQNCiAgICAgICAgdGhpcy5wYXJhbXMuQXJlYUlkID0gJycNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOWFtuS7lue6p+WIq+iKgueCue+8iOWMuuWfn++8ie+8muiuvue9rkFyZWFJZOS4uuW9k+WJjeiKgueCuUlE77yMUHJvamVjdElk5Li65omA5bGe56ys5LiA57qn6IqC54K5SUQNCiAgICAgICAgdGhpcy5wYXJhbXMuQXJlYUlkID0gbm9kZS5TeXNfUHJvamVjdF9JZA0KICAgICAgICAvLyDmn6Xmib7miYDlsZ7nmoTnrKzkuIDnuqfoioLngrlJRA0KICAgICAgICB0aGlzLnBhcmFtcy5Qcm9qZWN0SWQgPSB0aGlzLmZpbmRUb3BMZXZlbFBhcmVudChub2RlKQ0KICAgICAgfQ0KDQogICAgICAvLyDliJ3lp4vljJZhY3RpdmVUYWLlubbojrflj5bpooTorabop4TliJnmlbDmja4NCiAgICAgIGlmICh0aGlzLm5vZGVEYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgdGhpcy5hY3RpdmVUYWIgPSB0aGlzLm5vZGVEYXRhWzBdLlBsYW5fVHlwZS50b1N0cmluZygpDQogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAvLyDlvZPoioLngrnlj5jljJbml7bvvIzph43mlrDor7fmsYLpooTorabop4TliJnmlbDmja4NCiAgICAgICAgICB0aGlzLmdldFdhcm5SdWxlcygpDQogICAgICAgIH0pDQogICAgICB9DQoNCiAgICAgIGNvbnNvbGUubG9nKHRoaXMucGFyYW1zKQ0KICAgIH0sDQogICAgYXN5bmMgZ2V0VHJlZURhdGEoKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBHZXRDdXJyQ29tcGFueVByb2plY3RMaXN0KHsNCiAgICAgICAgICBjb21wYW5JZDogbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ0N1clJlZmVyZW5jZUlkJyksDQogICAgICAgICAgSXNDYXNjYWRlOiBmYWxzZQ0KICAgICAgICB9KQ0KDQogICAgICAgIC8vIOWcqOacgOWJjemdoua3u+WKoOOAkOm7mOiupOmFjee9ruOAkeiKgueCuQ0KICAgICAgICBjb25zdCBkZWZhdWx0Tm9kZSA9IHsNCiAgICAgICAgICBTeXNfUHJvamVjdF9JZDogJycsDQogICAgICAgICAgU2hvcnRfTmFtZTogJ+OAkOm7mOiupOmFjee9ruOAkScsDQogICAgICAgICAgQ29kZTogJycsDQogICAgICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICAgICAgaXNEZWZhdWx0OiB0cnVlIC8vIOagh+iusOS4uum7mOiupOmFjee9ruiKgueCuQ0KICAgICAgICB9DQoNCiAgICAgICAgdGhpcy50cmVlRGF0YSA9IFtkZWZhdWx0Tm9kZSwgLi4ucmVzLkRhdGEubWFwKGl0ZW0gPT4gew0KICAgICAgICAgIGl0ZW0ubG9hZGluZyA9IGZhbHNlDQogICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgfSldDQoNCiAgICAgICAgLy8g6buY6K6k6YCJ5Lit44CQ6buY6K6k6YWN572u44CR6IqC54K5DQogICAgICAgIGlmICh0aGlzLnRyZWVEYXRhLmxlbmd0aCkgew0KICAgICAgICAgIHRoaXMudHJlZURlZmF1bHRTZWxlY3RlZEtleSA9ICcnDQogICAgICAgICAgdGhpcy5jdXJQcm9qZWN0ID0gZGVmYXVsdE5vZGUNCiAgICAgICAgICAvLyDorr7nva7pu5jorqTphY3nva7nmoTlj4LmlbANCiAgICAgICAgICB0aGlzLnBhcmFtcy5Qcm9qZWN0SWQgPSAnJw0KICAgICAgICAgIHRoaXMucGFyYW1zLkFyZWFJZCA9ICcnDQoNCiAgICAgICAgICAvLyDlhYjojrflj5bphY3nva7liJfooajmlbDmja7vvIznhLblkI7lho3op6blj5Fub2RlQ2xpY2sNCiAgICAgICAgICBhd2FpdCB0aGlzLmdldENvbmZpZ0xpc3QoKQ0KICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMubm9kZUNsaWNrKGRlZmF1bHROb2RlKQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluagkeW9ouaVsOaNruWksei0pTonLCBlcnJvcikNCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIGdldExpc3QoKSB7DQogICAgICBjb25zdCByZXMgPSBhd2FpdCBHZXRVc2VyUGFnZSh7DQogICAgICAgIERlcGFydG1lbnRJZDogdGhpcy5kZXBhcnRtZW50SWQsDQogICAgICAgIFBhZ2VTaXplOiAxMDAwMA0KICAgICAgfSkNCiAgICAgIHRoaXMudXNlckxpc3QgPSByZXMuRGF0YS5EYXRhLmZpbHRlcihpdGVtID0+IGl0ZW0uVXNlclN0YXR1c05hbWUgPT09ICfmraPluLgnKQ0KICAgIH0sDQogICAgYXN5bmMgZ2V0Q29uZmlnTGlzdCgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IEdldENvbmZpZ3Moew0KICAgICAgICAgIENvbXBhbnlJZDogdGhpcy5jb21wYW55SWQNCiAgICAgICAgfSkNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMsICdub2RlRGF0YScsIHJlcy5EYXRhKQ0KICAgICAgICByZXR1cm4gcmVzLkRhdGENCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlumFjee9ruWIl+ihqOWksei0pTonLCBlcnJvcikNCiAgICAgICAgcmV0dXJuIFtdDQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBzYXZlQ29uZmlnKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgU2F2ZUNvbmZpZ3ModGhpcy5ub2RlRGF0YSkNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+iuoeWIkumFjee9ruS/neWtmOaIkOWKnycpDQogICAgICAgICAgdGhpcy5nZXRDb25maWdMaXN0KCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5NZXNzYWdlKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCforqHliJLphY3nva7kv53lrZjlpLHotKUnKQ0KICAgICAgICBjb25zb2xlLmVycm9yKCfkv53lrZjorqHliJLphY3nva7plJnor686JywgZXJyb3IpDQogICAgICB9DQogICAgfSwNCiAgICAvLyDmn6Xmib7pobbnuqfniLboioLngrlJRA0KICAgIGZpbmRUb3BMZXZlbFBhcmVudChub2RlKSB7DQogICAgICAvLyDpgJLlvZLmn6Xmib7nm7TliLDmib7liLDmsqHmnInniLbnuqfnmoToioLngrkNCiAgICAgIGNvbnN0IGZpbmRQYXJlbnQgPSAoY3VycmVudE5vZGUpID0+IHsNCiAgICAgICAgaWYgKCFjdXJyZW50Tm9kZS5QYXJlbnRfSWQgfHwgY3VycmVudE5vZGUuUGFyZW50X0lkID09PSAnJyB8fCBjdXJyZW50Tm9kZS5QYXJlbnRfSWQgPT09IG51bGwpIHsNCiAgICAgICAgICByZXR1cm4gY3VycmVudE5vZGUuU3lzX1Byb2plY3RfSWQNCiAgICAgICAgfQ0KICAgICAgICAvLyDlnKjmoJHmlbDmja7kuK3mn6Xmib7niLboioLngrkNCiAgICAgICAgY29uc3QgcGFyZW50Tm9kZSA9IHRoaXMuZmluZE5vZGVCeUlkKGN1cnJlbnROb2RlLlBhcmVudF9JZCkNCiAgICAgICAgaWYgKHBhcmVudE5vZGUpIHsNCiAgICAgICAgICByZXR1cm4gZmluZFBhcmVudChwYXJlbnROb2RlKQ0KICAgICAgICB9DQogICAgICAgIHJldHVybiBjdXJyZW50Tm9kZS5TeXNfUHJvamVjdF9JZA0KICAgICAgfQ0KICAgICAgcmV0dXJuIGZpbmRQYXJlbnQobm9kZSkNCiAgICB9LA0KICAgIC8vIOagueaNrklE5p+l5om+6IqC54K5DQogICAgZmluZE5vZGVCeUlkKGlkKSB7DQogICAgICBjb25zdCBmaW5kSW5UcmVlID0gKG5vZGVzKSA9PiB7DQogICAgICAgIGZvciAoY29uc3Qgbm9kZSBvZiBub2Rlcykgew0KICAgICAgICAgIGlmIChub2RlLlN5c19Qcm9qZWN0X0lkID09PSBpZCkgew0KICAgICAgICAgICAgcmV0dXJuIG5vZGUNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKG5vZGUuY2hpbGRyZW4gJiYgbm9kZS5jaGlsZHJlbi5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICBjb25zdCBmb3VuZCA9IGZpbmRJblRyZWUobm9kZS5jaGlsZHJlbikNCiAgICAgICAgICAgIGlmIChmb3VuZCkgcmV0dXJuIGZvdW5kDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHJldHVybiBudWxsDQogICAgICB9DQogICAgICByZXR1cm4gZmluZEluVHJlZSh0aGlzLnRyZWVEYXRhKQ0KICAgIH0sDQogICAgLy8g6YeN572u6YWN572uDQogICAgYXN5bmMgcmVzZXRDb25maWcoKSB7DQogICAgICB0aGlzLnJlc2V0TG9hZGluZyA9IHRydWUNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IFJlc2V0V2FybkNvbmZpZyh7DQogICAgICAgICAgQ29tcGFueUlkOiB0aGlzLmNvbXBhbnlJZCwNCiAgICAgICAgICBQcm9qZWN0SWQ6IHRoaXMucGFyYW1zLlByb2plY3RJZA0KICAgICAgICB9KQ0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn6YeN572u6YWN572u5oiQ5YqfJykNCiAgICAgICAgICB0aGlzLmdldENvbmZpZ0xpc3QoKSAvLyDph43mlrDojrflj5bphY3nva7liJfooagNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5NZXNzYWdlIHx8ICfph43nva7phY3nva7lpLHotKUnKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfph43nva7phY3nva7lpLHotKUnKQ0KICAgICAgICBjb25zb2xlLmVycm9yKCfph43nva7phY3nva7plJnor686JywgZXJyb3IpDQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLnJlc2V0TG9hZGluZyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCiAgICAvLyDlkIzmraXoh7Ppobnnm67liIbljLoNCiAgICBhc3luYyBzeW5jVG9BcmVhKCkgew0KICAgICAgdGhpcy5zeW5jTG9hZGluZyA9IHRydWUNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IFN5bmNBcmVhV2FybkNvbmZpZyh7DQogICAgICAgICAgQ29tcGFueUlkOiB0aGlzLmNvbXBhbnlJZCwNCiAgICAgICAgICBQcm9qZWN0SWQ6IHRoaXMucGFyYW1zLlByb2plY3RJZA0KICAgICAgICB9KQ0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5ZCM5q2l6Iez6aG555uu5YiG5Yy65oiQ5YqfJykNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5NZXNzYWdlIHx8ICflkIzmraXoh7Ppobnnm67liIbljLrlpLHotKUnKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflkIzmraXoh7Ppobnnm67liIbljLrlpLHotKUnKQ0KICAgICAgICBjb25zb2xlLmVycm9yKCflkIzmraXoh7Ppobnnm67liIbljLrplJnor686JywgZXJyb3IpDQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLnN5bmNMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIFRhYuWIh+aNouWkhOeQhg0KICAgIGhhbmRsZVRhYkNsaWNrKHRhYikgew0KICAgICAgdGhpcy5hY3RpdmVUYWIgPSB0YWIubmFtZQ0KICAgICAgLy8g5LiN6YeN5paw6K+35rGC5pWw5o2u77yM6YG/5YWN5pWw5o2u5Lii5aSxDQogICAgfSwNCiAgICAvLyDmlrDlop7pooTorabop4TliJkNCiAgICBhZGRXYXJuUnVsZSgpIHsNCiAgICAgIGlmICghdGhpcy53YXJuUnVsZXNbdGhpcy5hY3RpdmVUYWJdKSB7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLndhcm5SdWxlcywgdGhpcy5hY3RpdmVUYWIsIFtdKQ0KICAgICAgfQ0KICAgICAgY29uc3QgbmV3UnVsZSA9IHsNCiAgICAgICAgUGxhbl9UeXBlOiBwYXJzZUludCh0aGlzLmFjdGl2ZVRhYiksDQogICAgICAgIFByb2plY3RfSWQ6IHRoaXMucGFyYW1zLlByb2plY3RJZCwNCiAgICAgICAgQXJlYV9JZDogdGhpcy5wYXJhbXMuQXJlYUlkLA0KICAgICAgICBQcm9ncmVzc19QZXJjZW50OiAnJywNCiAgICAgICAgRmluaXNoX1BlcmNlbnQ6ICcnLA0KICAgICAgICBQbGFuX05vdGljZV9Vc2VyaWRzOiBbXQ0KICAgICAgfQ0KICAgICAgdGhpcy53YXJuUnVsZXNbdGhpcy5hY3RpdmVUYWJdLnB1c2gobmV3UnVsZSkNCiAgICB9LA0KICAgIC8vIOWIoOmZpOmihOitpuinhOWImQ0KICAgIGRlbGV0ZVdhcm5SdWxlKGluZGV4KSB7DQogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrpropoHliKDpmaTov5nmnaHpooTorabop4TliJnlkJfvvJ8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLndhcm5SdWxlc1t0aGlzLmFjdGl2ZVRhYl0uc3BsaWNlKGluZGV4LCAxKQ0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpDQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSkNCiAgICB9LA0KICAgIC8vIOiOt+WPlumihOitpuinhOWImQ0KICAgIGFzeW5jIGdldFdhcm5SdWxlcygpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IEdldFdhcm5Db25maWdzKHsNCiAgICAgICAgICBDb21wYW55SWQ6IHRoaXMuY29tcGFueUlkLA0KICAgICAgICAgIFByb2plY3RJZDogdGhpcy5wYXJhbXMuUHJvamVjdElkLA0KICAgICAgICAgIEFyZWFJZDogdGhpcy5wYXJhbXMuQXJlYUlkLA0KICAgICAgICAgIFBsYW5fVHlwZTogcGFyc2VJbnQodGhpcy5hY3RpdmVUYWIpDQogICAgICAgIH0pDQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy4kc2V0KHRoaXMud2FyblJ1bGVzLCB0aGlzLmFjdGl2ZVRhYiwgcmVzLkRhdGEgfHwgW10pDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlumihOitpuinhOWImeWksei0pTonLCBlcnJvcikNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOS/neWtmOaJgOaciemFjee9rg0KICAgIGFzeW5jIHNhdmVBbGxDb25maWcoKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDlpoLmnpzmmL7npLrorqHliJLphY3nva7vvIzkv53lrZjorqHliJLphY3nva4NCiAgICAgICAgaWYgKHRoaXMuc2hvd1BsYW5Db25maWcgJiYgdGhpcy5ub2RlRGF0YSAmJiB0aGlzLm5vZGVEYXRhLmxlbmd0aCkgew0KICAgICAgICAgIGF3YWl0IHRoaXMuc2F2ZUNvbmZpZygpDQogICAgICAgIH0NCg0KICAgICAgICAvLyDlpoLmnpzmmL7npLrpooTorabphY3nva7vvIzkv53lrZjpooTorabphY3nva4NCiAgICAgICAgaWYgKHRoaXMuc2hvd1dhcm5Db25maWcpIHsNCiAgICAgICAgICBhd2FpdCB0aGlzLnNhdmVXYXJuQ29uZmlnKCkNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOWmguaenOS4pOS4qumDveS4jeaYvuekuuaIlumDveayoeacieaVsOaNru+8jOaPkOekuueUqOaItw0KICAgICAgICBpZiAoIXRoaXMuc2hvd1BsYW5Db25maWcgJiYgIXRoaXMuc2hvd1dhcm5Db25maWcpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+aaguaXoOWPr+S/neWtmOeahOmFjee9ricpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+S/neWtmOmFjee9ruWksei0pTonLCBlcnJvcikNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOS/neWtmOmihOitpumFjee9rg0KICAgIGFzeW5jIHNhdmVXYXJuQ29uZmlnKCkgew0KICAgICAgLy8g5qCh6aqM5b+F5aGr6aG5DQogICAgICBjb25zdCBydWxlcyA9IHRoaXMuY3VycmVudFdhcm5SdWxlcw0KICAgICAgaWYgKHJ1bGVzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBydWxlcy5sZW5ndGg7IGkrKykgew0KICAgICAgICAgIGNvbnN0IHJ1bGUgPSBydWxlc1tpXQ0KICAgICAgICAgIGlmICghcnVsZS5Qcm9ncmVzc19QZXJjZW50IHx8ICFydWxlLkZpbmlzaF9QZXJjZW50IHx8ICFydWxlLlBsYW5fTm90aWNlX1VzZXJpZHMgfHwgcnVsZS5QbGFuX05vdGljZV9Vc2VyaWRzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihg56ysJHtpICsgMX3mnaHpooTorabop4TliJnlrZjlnKjlv4XloavpobnmnKrloavlhplgKQ0KICAgICAgICAgICAgcmV0dXJuDQogICAgICAgICAgfQ0KICAgICAgICAgIC8vIOmqjOivgeeZvuWIhuavlOagvOW8jw0KICAgICAgICAgIGlmIChpc05hTihydWxlLlByb2dyZXNzX1BlcmNlbnQpIHx8IGlzTmFOKHJ1bGUuRmluaXNoX1BlcmNlbnQpKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGDnrKwke2kgKyAxfeadoemihOitpuinhOWImeeahOeZvuWIhuavlOW/hemhu+S4uuaVsOWtl2ApDQogICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgU2F2ZVdhcm5Db25maWdzKHsNCiAgICAgICAgICBDb21wYW55SWQ6IHRoaXMuY29tcGFueUlkLA0KICAgICAgICAgIFByb2plY3RJZDogdGhpcy5wYXJhbXMuUHJvamVjdElkLA0KICAgICAgICAgIEFyZWFJZDogdGhpcy5wYXJhbXMuQXJlYUlkLA0KICAgICAgICAgIEl0ZW1zOiBydWxlcw0KICAgICAgICB9KQ0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn6aKE6K2m6KeE5YiZ5L+d5a2Y5oiQ5YqfJykNCiAgICAgICAgICB0aGlzLmdldFdhcm5SdWxlcygpIC8vIOmHjeaWsOiOt+WPluaVsOaNrg0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLk1lc3NhZ2UgfHwgJ+mihOitpuinhOWImeS/neWtmOWksei0pScpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S/neWtmOWksei0pScpDQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+S/neWtmOmihOitpumFjee9rumUmeivrzonLCBlcnJvcikNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["setting.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4JA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "setting.vue", "sourceRoot": "src/views/plan", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <bt-tree :data=\"treeData\" :props=\"treeProps\" :default-selected-key=\"treeDefaultSelectedKey\" node-key=\"Sys_Project_Id\" @node-click=\"nodeClick\">\r\n      <template #default=\"{ data }\">\r\n        <span v-if=\"data.Code\" style=\"color: #5ac8fa!important;\">({{ data.Code }})</span>\r\n        <span>{{ data.Short_Name }}</span>\r\n      </template>\r\n    </bt-tree>\r\n    <!-- 计划配置和预警规则 -->\r\n    <el-card class=\"card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <span>计划配置</span>\r\n        <div class=\"header-buttons\">\r\n          <div v-if=\"showProjectButtons\" class=\"project-buttons\">\r\n            <el-button\r\n              :loading=\"resetLoading\"\r\n              size=\"small\"\r\n              type=\"primary\"\r\n              @click=\"resetConfig\"\r\n            >\r\n              重置配置\r\n            </el-button>\r\n            <el-button\r\n              :loading=\"syncLoading\"\r\n              size=\"small\"\r\n              type=\"primary\"\r\n              @click=\"syncToArea\"\r\n            >\r\n              同步至项目分区\r\n            </el-button>\r\n          </div>\r\n          <el-button\r\n            type=\"primary\"\r\n            size=\"small\"\r\n            @click=\"saveAllConfig\"\r\n          >\r\n            保存\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      <div class=\"content\">\r\n        <!-- 计划配置 - 只在默认配置时显示 -->\r\n        <div v-if=\"showPlanConfig\" class=\"plan-config-section\">\r\n          <el-form style=\"width: 782px\" inline>\r\n            <el-row v-for=\"item in nodeData\" :key=\"item.Plan_Type\" :gutter=\"50\">\r\n              <el-form-item :label=\"item.Plan_Name||' '\" label-width=\"100px\">\r\n                <el-input v-model=\"item.Plan_Name\" style=\"width:150px\" clearable />\r\n              </el-form-item>\r\n              <el-form-item label=\"计划下发通知人\" label-width=\"150px\">\r\n                <el-select\r\n                  v-model=\"item.Plan_Notice_Userids\"\r\n                  placeholder=\"请选择\"\r\n                  style=\"width: 400px\"\r\n                  clearable\r\n                  filterable\r\n                  multiple\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in userList\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Display_Name\"\r\n                    :value=\"item.Id\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-row>\r\n\r\n          </el-form>\r\n        </div>\r\n\r\n        <!-- 预警规则 - 在非默认配置时显示 -->\r\n        <div v-if=\"showWarnConfig\" class=\"warn-config-section\">\r\n          <h3>预警规则</h3>\r\n          <!-- Tab切换 -->\r\n          <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\r\n            <el-tab-pane\r\n              v-for=\"item in nodeData\"\r\n              :key=\"item.Plan_Type\"\r\n              :label=\"item.Plan_Name\"\r\n              :name=\"item.Plan_Type.toString()\"\r\n            >\r\n              <!-- 预警规则表单 -->\r\n              <div class=\"warn-rules\">\r\n                <div class=\"rule-header\">\r\n                  <el-button size=\"small\" type=\"primary\" @click=\"addWarnRule\">新增预警规则</el-button>\r\n                </div>\r\n                <div v-if=\"currentWarnRules.length === 0\" class=\"empty-rules\">\r\n                  暂无预警规则\r\n                </div>\r\n                <el-form v-else>\r\n                  <div class=\"rules-list\">\r\n                    <div v-for=\"(rule, index) in currentWarnRules\" :key=\"index\" class=\"rule-item\">\r\n                      <el-row :gutter=\"20\">\r\n                        <el-col :span=\"4\">\r\n                          <el-form-item label=\"工期进度达到\" required>\r\n                            <el-input\r\n                              v-model=\"rule.Progress_Percent\"\r\n                              placeholder=\"70%\"\r\n                              style=\"width: 100%\"\r\n                            >\r\n                              <template slot=\"append\">%</template>\r\n                            </el-input>\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"4\">\r\n                          <el-form-item label=\"完成进度达到\" required>\r\n                            <el-input\r\n                              v-model=\"rule.Finish_Percent\"\r\n                              placeholder=\"70%\"\r\n                              style=\"width: 100%\"\r\n                            >\r\n                              <template slot=\"append\">%</template>\r\n                            </el-input>\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"14\">\r\n                          <el-form-item label=\"计划预警通知人\" required>\r\n                            <el-select\r\n                              v-model=\"rule.Plan_Notice_Userids\"\r\n                              placeholder=\"下拉选择当前公司下标准人，支持搜索\"\r\n                              style=\"width: 100%\"\r\n                              clearable\r\n                              filterable\r\n                              multiple\r\n                            >\r\n                              <el-option\r\n                                v-for=\"user in userList\"\r\n                                :key=\"user.Id\"\r\n                                :label=\"user.Display_Name\"\r\n                                :value=\"user.Id\"\r\n                              />\r\n                            </el-select>\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"2\">\r\n                          <el-button\r\n                            type=\"danger\"\r\n                            icon=\"el-icon-delete\"\r\n                            size=\"small\"\r\n                            @click=\"deleteWarnRule(index)\"\r\n                          />\r\n                        </el-col>\r\n                      </el-row>\r\n                    </div>\r\n                  </div>\r\n\r\n                </el-form>\r\n              </div>\r\n            </el-tab-pane>\r\n          </el-tabs>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { GetConfigs, GetCurrCompanyProjectList, SaveConfigs } from '@/api/plm/projects'\r\nimport { GetUserPage } from '@/api/sys'\r\nimport { GetWarnConfigs, SaveWarnConfigs, ResetWarnConfig, SyncAreaWarnConfig } from '@/api/PRO/control-plan'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      userList: [],\r\n      nodeData: [],\r\n      companyId: localStorage.getItem('CurReferenceId'),\r\n      treeData: [],\r\n      treeProps: {\r\n        label: 'Short_Name',\r\n        id: 'Sys_Project_Id'\r\n      },\r\n      treeDefaultSelectedKey: '',\r\n      params: {\r\n        CompanyId: localStorage.getItem('CurReferenceId'),\r\n        ProjectId: '',\r\n        AreaId: ''\r\n      },\r\n      resetLoading: false,\r\n      syncLoading: false,\r\n      activeTab: '',\r\n      warnRules: {} // 存储各个计划类型的预警规则\r\n    }\r\n  },\r\n  computed: {\r\n    showProjectButtons() {\r\n      // 只有在选中项目级节点（非默认配置且非区域）时才显示按钮\r\n      return this.params.ProjectId !== '' &&\r\n              this.params.ProjectId !== 'default_config' &&\r\n              this.params.AreaId === ''\r\n    },\r\n    showPlanConfig() {\r\n      // 只有在默认配置时才显示计划配置\r\n      return this.params.ProjectId === '' && this.params.AreaId === ''\r\n    },\r\n    showWarnConfig() {\r\n      // 预警规则无论何种情况都显示\r\n      return true\r\n    },\r\n    currentWarnRules() {\r\n      // 获取当前选中tab的预警规则\r\n      if (!this.activeTab || !this.warnRules[this.activeTab]) {\r\n        return []\r\n      }\r\n      return this.warnRules[this.activeTab]\r\n    }\r\n  },\r\n  async created() {\r\n    await this.getTreeData()\r\n    await this.getList()\r\n  },\r\n  methods: {\r\n    nodeClick(node) {\r\n      this.curProject = node\r\n\r\n      // 判断是否为默认配置节点\r\n      if (node.isDefault) {\r\n        this.params.ProjectId = ''\r\n        this.params.AreaId = ''\r\n        // 默认配置时也需要请求预警规则数据\r\n        if (this.nodeData.length > 0) {\r\n          this.activeTab = this.nodeData[0].Plan_Type.toString()\r\n          this.$nextTick(() => {\r\n            this.getWarnRules()\r\n          })\r\n        }\r\n        return\r\n      }\r\n\r\n      // 判断节点层级\r\n      // 如果节点没有父级ID或父级ID为空，说明是第一级（项目）\r\n      if (!node.Parent_Id || node.Parent_Id === '' || node.Parent_Id === null) {\r\n        // 第一级节点：设置ProjectId为当前节点ID，AreaId为空\r\n        this.params.ProjectId = node.Sys_Project_Id\r\n        this.params.AreaId = ''\r\n      } else {\r\n        // 其他级别节点（区域）：设置AreaId为当前节点ID，ProjectId为所属第一级节点ID\r\n        this.params.AreaId = node.Sys_Project_Id\r\n        // 查找所属的第一级节点ID\r\n        this.params.ProjectId = this.findTopLevelParent(node)\r\n      }\r\n\r\n      // 初始化activeTab并获取预警规则数据\r\n      if (this.nodeData.length > 0) {\r\n        this.activeTab = this.nodeData[0].Plan_Type.toString()\r\n        this.$nextTick(() => {\r\n          // 当节点变化时，重新请求预警规则数据\r\n          this.getWarnRules()\r\n        })\r\n      }\r\n\r\n      console.log(this.params)\r\n    },\r\n    async getTreeData() {\r\n      try {\r\n        const res = await GetCurrCompanyProjectList({\r\n          companId: localStorage.getItem('CurReferenceId'),\r\n          IsCascade: false\r\n        })\r\n\r\n        // 在最前面添加【默认配置】节点\r\n        const defaultNode = {\r\n          Sys_Project_Id: '',\r\n          Short_Name: '【默认配置】',\r\n          Code: '',\r\n          loading: false,\r\n          isDefault: true // 标记为默认配置节点\r\n        }\r\n\r\n        this.treeData = [defaultNode, ...res.Data.map(item => {\r\n          item.loading = false\r\n          return item\r\n        })]\r\n\r\n        // 默认选中【默认配置】节点\r\n        if (this.treeData.length) {\r\n          this.treeDefaultSelectedKey = ''\r\n          this.curProject = defaultNode\r\n          // 设置默认配置的参数\r\n          this.params.ProjectId = ''\r\n          this.params.AreaId = ''\r\n\r\n          // 先获取配置列表数据，然后再触发nodeClick\r\n          await this.getConfigList()\r\n          this.$nextTick(() => {\r\n            this.nodeClick(defaultNode)\r\n          })\r\n        }\r\n      } catch (error) {\r\n        console.error('获取树形数据失败:', error)\r\n      }\r\n    },\r\n    async getList() {\r\n      const res = await GetUserPage({\r\n        DepartmentId: this.departmentId,\r\n        PageSize: 10000\r\n      })\r\n      this.userList = res.Data.Data.filter(item => item.UserStatusName === '正常')\r\n    },\r\n    async getConfigList() {\r\n      try {\r\n        const res = await GetConfigs({\r\n          CompanyId: this.companyId\r\n        })\r\n        this.$set(this, 'nodeData', res.Data)\r\n        return res.Data\r\n      } catch (error) {\r\n        console.error('获取配置列表失败:', error)\r\n        return []\r\n      }\r\n    },\r\n    async saveConfig() {\r\n      try {\r\n        const res = await SaveConfigs(this.nodeData)\r\n        if (res.IsSucceed) {\r\n          this.$message.success('计划配置保存成功')\r\n          this.getConfigList()\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('计划配置保存失败')\r\n        console.error('保存计划配置错误:', error)\r\n      }\r\n    },\r\n    // 查找顶级父节点ID\r\n    findTopLevelParent(node) {\r\n      // 递归查找直到找到没有父级的节点\r\n      const findParent = (currentNode) => {\r\n        if (!currentNode.Parent_Id || currentNode.Parent_Id === '' || currentNode.Parent_Id === null) {\r\n          return currentNode.Sys_Project_Id\r\n        }\r\n        // 在树数据中查找父节点\r\n        const parentNode = this.findNodeById(currentNode.Parent_Id)\r\n        if (parentNode) {\r\n          return findParent(parentNode)\r\n        }\r\n        return currentNode.Sys_Project_Id\r\n      }\r\n      return findParent(node)\r\n    },\r\n    // 根据ID查找节点\r\n    findNodeById(id) {\r\n      const findInTree = (nodes) => {\r\n        for (const node of nodes) {\r\n          if (node.Sys_Project_Id === id) {\r\n            return node\r\n          }\r\n          if (node.children && node.children.length > 0) {\r\n            const found = findInTree(node.children)\r\n            if (found) return found\r\n          }\r\n        }\r\n        return null\r\n      }\r\n      return findInTree(this.treeData)\r\n    },\r\n    // 重置配置\r\n    async resetConfig() {\r\n      this.resetLoading = true\r\n      try {\r\n        const res = await ResetWarnConfig({\r\n          CompanyId: this.companyId,\r\n          ProjectId: this.params.ProjectId\r\n        })\r\n        if (res.IsSucceed) {\r\n          this.$message.success('重置配置成功')\r\n          this.getConfigList() // 重新获取配置列表\r\n        } else {\r\n          this.$message.error(res.Message || '重置配置失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('重置配置失败')\r\n        console.error('重置配置错误:', error)\r\n      } finally {\r\n        this.resetLoading = false\r\n      }\r\n    },\r\n    // 同步至项目分区\r\n    async syncToArea() {\r\n      this.syncLoading = true\r\n      try {\r\n        const res = await SyncAreaWarnConfig({\r\n          CompanyId: this.companyId,\r\n          ProjectId: this.params.ProjectId\r\n        })\r\n        if (res.IsSucceed) {\r\n          this.$message.success('同步至项目分区成功')\r\n        } else {\r\n          this.$message.error(res.Message || '同步至项目分区失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('同步至项目分区失败')\r\n        console.error('同步至项目分区错误:', error)\r\n      } finally {\r\n        this.syncLoading = false\r\n      }\r\n    },\r\n    // Tab切换处理\r\n    handleTabClick(tab) {\r\n      this.activeTab = tab.name\r\n      // 不重新请求数据，避免数据丢失\r\n    },\r\n    // 新增预警规则\r\n    addWarnRule() {\r\n      if (!this.warnRules[this.activeTab]) {\r\n        this.$set(this.warnRules, this.activeTab, [])\r\n      }\r\n      const newRule = {\r\n        Plan_Type: parseInt(this.activeTab),\r\n        Project_Id: this.params.ProjectId,\r\n        Area_Id: this.params.AreaId,\r\n        Progress_Percent: '',\r\n        Finish_Percent: '',\r\n        Plan_Notice_Userids: []\r\n      }\r\n      this.warnRules[this.activeTab].push(newRule)\r\n    },\r\n    // 删除预警规则\r\n    deleteWarnRule(index) {\r\n      this.$confirm('确定要删除这条预警规则吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.warnRules[this.activeTab].splice(index, 1)\r\n        this.$message.success('删除成功')\r\n      }).catch(() => {})\r\n    },\r\n    // 获取预警规则\r\n    async getWarnRules() {\r\n      try {\r\n        const res = await GetWarnConfigs({\r\n          CompanyId: this.companyId,\r\n          ProjectId: this.params.ProjectId,\r\n          AreaId: this.params.AreaId,\r\n          Plan_Type: parseInt(this.activeTab)\r\n        })\r\n        if (res.IsSucceed) {\r\n          this.$set(this.warnRules, this.activeTab, res.Data || [])\r\n        }\r\n      } catch (error) {\r\n        console.error('获取预警规则失败:', error)\r\n      }\r\n    },\r\n    // 保存所有配置\r\n    async saveAllConfig() {\r\n      try {\r\n        // 如果显示计划配置，保存计划配置\r\n        if (this.showPlanConfig && this.nodeData && this.nodeData.length) {\r\n          await this.saveConfig()\r\n        }\r\n\r\n        // 如果显示预警配置，保存预警配置\r\n        if (this.showWarnConfig) {\r\n          await this.saveWarnConfig()\r\n        }\r\n\r\n        // 如果两个都不显示或都没有数据，提示用户\r\n        if (!this.showPlanConfig && !this.showWarnConfig) {\r\n          this.$message.warning('暂无可保存的配置')\r\n        }\r\n      } catch (error) {\r\n        console.error('保存配置失败:', error)\r\n      }\r\n    },\r\n    // 保存预警配置\r\n    async saveWarnConfig() {\r\n      // 校验必填项\r\n      const rules = this.currentWarnRules\r\n      if (rules.length > 0) {\r\n        for (let i = 0; i < rules.length; i++) {\r\n          const rule = rules[i]\r\n          if (!rule.Progress_Percent || !rule.Finish_Percent || !rule.Plan_Notice_Userids || rule.Plan_Notice_Userids.length === 0) {\r\n            this.$message.error(`第${i + 1}条预警规则存在必填项未填写`)\r\n            return\r\n          }\r\n          // 验证百分比格式\r\n          if (isNaN(rule.Progress_Percent) || isNaN(rule.Finish_Percent)) {\r\n            this.$message.error(`第${i + 1}条预警规则的百分比必须为数字`)\r\n            return\r\n          }\r\n        }\r\n      }\r\n\r\n      try {\r\n        const res = await SaveWarnConfigs({\r\n          CompanyId: this.companyId,\r\n          ProjectId: this.params.ProjectId,\r\n          AreaId: this.params.AreaId,\r\n          Items: rules\r\n        })\r\n        if (res.IsSucceed) {\r\n          this.$message.success('预警规则保存成功')\r\n          this.getWarnRules() // 重新获取数据\r\n        } else {\r\n          this.$message.error(res.Message || '预警规则保存失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('保存失败')\r\n        console.error('保存预警配置错误:', error)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container{\r\n  font-family: PingFang SC, PingFang SC;\r\n  display: flex;\r\n  .card{\r\n    flex:1;\r\n    height: 100%;\r\n    margin-left: 16px;\r\n    .card-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      .header-buttons {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 8px;\r\n\r\n        .project-buttons {\r\n          display: flex;\r\n          gap: 8px;\r\n        }\r\n\r\n        .el-button {\r\n          margin: 0;\r\n        }\r\n      }\r\n    }\r\n    .content{\r\n      display: flex;\r\n      flex-direction: column;\r\n      height: 100%;\r\n    }\r\n    .bt-table{\r\n      flex:1;\r\n    }\r\n\r\n    // 计划配置和预警规则section样式\r\n    .plan-config-section {\r\n      margin-bottom: 30px;\r\n    }\r\n\r\n    .warn-config-section {\r\n      h3 {\r\n        margin: 0 0 20px 0;\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        color: #303133;\r\n        padding-bottom: 8px;\r\n      }\r\n    }\r\n\r\n    // 预警规则样式\r\n    .warn-rules {\r\n      .rule-header {\r\n        margin-bottom: 20px;\r\n      }\r\n\r\n      .empty-rules {\r\n        text-align: center;\r\n        color: #999;\r\n        padding: 40px 0;\r\n        font-size: 14px;\r\n      }\r\n\r\n      .rules-list {\r\n        .rule-item {\r\n          margin-bottom: 20px;\r\n          padding: 20px;\r\n          border: 1px solid #e4e7ed;\r\n          border-radius: 4px;\r\n          background-color: #fafafa;\r\n\r\n          .el-form-item {\r\n            margin-bottom: 0;\r\n          }\r\n\r\n          .el-col:last-child {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            padding-top: 30px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .header{\r\n    display: flex;\r\n    align-items: flex-end;\r\n    .project-name{\r\n      font-size: 16px;\r\n      color: #333333;\r\n      font-weight: bold;\r\n      margin-right: 8px;\r\n    }\r\n    .el-icon-time{\r\n      margin-left: 8px;\r\n      margin-right: 4px;\r\n      font-size: 14px;\r\n    }\r\n    .label{\r\n      color: #333333;\r\n      font-size: 12px;\r\n    }\r\n    .value{\r\n      font-size: 14px;\r\n      color: #333333;\r\n      font-weight: bold;\r\n      margin-left: 7px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
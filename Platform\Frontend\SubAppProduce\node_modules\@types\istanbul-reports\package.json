{"name": "@types/istanbul-reports", "version": "1.1.2", "description": "TypeScript definitions for istanbul-reports", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/jason0x43", "githubUsername": "jason0x43"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/istanbul-reports"}, "scripts": {}, "dependencies": {"@types/istanbul-lib-coverage": "*", "@types/istanbul-lib-report": "*"}, "typesPublisherContentHash": "c13cd090c027208710520a039ec004ef0045ea12516dc4c71d648e4fce9ff9f7", "typeScriptVersion": "3.0"}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\components\\Select\\SelectUser\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\components\\Select\\SelectUser\\index.vue", "mtime": 1757572678709}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9yZWdlbmVyYXRvclJ1bnRpbWUgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9yZWdlbmVyYXRvclJ1bnRpbWUuanMiOwppbXBvcnQgX2FzeW5jVG9HZW5lcmF0b3IgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9hc3luY1RvR2VuZXJhdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMubnVtYmVyLmNvbnN0cnVjdG9yLmpzIjsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCi8qKg0KICog6YCJ5oup5b2T5YmN5bel5Y6C5LiL55qE5Lq65ZGYDQogKi8KaW1wb3J0IHsgZGVlcENsb25lIH0gZnJvbSAnQC91dGlscyc7CmltcG9ydCB7IEdldEZhY3RvcnlQZW9wbGVsaXN0IH0gZnJvbSAnQC9hcGkvUFJPL3NhbGFyeSc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnU2VsZWN0VXNlcicsCiAgcHJvcHM6IHsKICAgIHZhbHVlOiB7CiAgICAgIHR5cGU6IFtBcnJheSwgTnVtYmVyLCBTdHJpbmddLAogICAgICBkZWZhdWx0OiAnJwogICAgfSwKICAgIG11bHRpcGxlOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlCiAgICB9LAogICAgY29sbGFwc2VUYWdzOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlCiAgICB9CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbGlzdDogW10sCiAgICAgIHNlbGVjdGVkVmFsdWU6IHRoaXMudmFsdWUKICAgIH07CiAgfSwKICB3YXRjaDogewogICAgdmFsdWU6IHsKICAgICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcih2YWwpIHsKICAgICAgICB0aGlzLnNlbGVjdGVkVmFsdWUgPSBBcnJheS5pc0FycmF5KHZhbCkgPyBkZWVwQ2xvbmUodmFsKSA6IHZhbDsKICAgICAgfSwKICAgICAgaW1tZWRpYXRlOiB0cnVlCiAgICB9CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBoYW5kbGVDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZUNoYW5nZSgpIHsKICAgICAgdGhpcy4kZW1pdCgnaW5wdXQnLCB0aGlzLnNlbGVjdGVkVmFsdWUpOwogICAgICB0aGlzLiRlbWl0KCdjaGFuZ2UnLCB0aGlzLnNlbGVjdGVkVmFsdWUpOwogICAgfSwKICAgIGdldExpc3Q6IGZ1bmN0aW9uIGdldExpc3QoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvcigvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yUnVudGltZSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZSgpIHsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZSQoX2NvbnRleHQpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0LnByZXYgPSBfY29udGV4dC5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBHZXRGYWN0b3J5UGVvcGxlbGlzdCgpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICAgICAgX3RoaXMubGlzdCA9IHJlcy5EYXRhOwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICBjYXNlIDE6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlKTsKICAgICAgfSkpKCk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["deepClone", "GetFactoryPeoplelist", "name", "props", "value", "type", "Array", "Number", "String", "default", "multiple", "Boolean", "collapseTags", "data", "list", "selected<PERSON><PERSON><PERSON>", "watch", "handler", "val", "isArray", "immediate", "created", "getList", "methods", "handleChange", "$emit", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "then", "res", "Data", "stop"], "sources": ["src/components/Select/SelectUser/index.vue"], "sourcesContent": ["<template>\r\n  <el-select\r\n    v-model=\"selectedValue\"\r\n    placeholder=\"请选择\"\r\n    style=\"width: 100%\"\r\n    clearable\r\n    filterable\r\n    :multiple=\"multiple\"\r\n    @change=\"handleChange\"\r\n    :collapse-tags=\"collapseTags\"\r\n  >\r\n    <el-option\r\n      v-for=\"item in list\"\r\n      :key=\"item.Id\"\r\n      :label=\"item.Display_Name\"\r\n      :value=\"item.Id\"\r\n    />\r\n  </el-select>\r\n</template>\r\n<script>\r\n/**\r\n * 选择当前工厂下的人员\r\n */\r\nimport { deepClone } from '@/utils'\r\nimport { GetFactoryPeoplelist } from '@/api/PRO/salary'\r\n\r\nexport default {\r\n  name: 'SelectUser',\r\n  props: {\r\n    value: {\r\n      type: [Array, Number, String],\r\n      default: ''\r\n    },\r\n    multiple: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    collapseTags: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      list: [],\r\n      selectedValue: this.value\r\n    }\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(val) {\r\n        this.selectedValue = Array.isArray(val) ? deepClone(val) : val\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    handleChange() {\r\n      this.$emit('input', this.selectedValue)\r\n      this.$emit('change', this.selectedValue)\r\n    },\r\n    async getList() {\r\n      GetFactoryPeoplelist().then(res => {\r\n        this.list = res.Data\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAoBA;AACA;AACA;AACA,SAAAA,SAAA;AACA,SAAAC,oBAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,GAAAC,KAAA,EAAAC,MAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,QAAA;MACAL,IAAA,EAAAM,OAAA;MACAF,OAAA;IACA;IACAG,YAAA;MACAP,IAAA,EAAAM,OAAA;MACAF,OAAA;IACA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;MACAC,aAAA,OAAAX;IACA;EACA;EACAY,KAAA;IACAZ,KAAA;MACAa,OAAA,WAAAA,QAAAC,GAAA;QACA,KAAAH,aAAA,GAAAT,KAAA,CAAAa,OAAA,CAAAD,GAAA,IAAAlB,SAAA,CAAAkB,GAAA,IAAAA,GAAA;MACA;MACAE,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,KAAA,eAAAV,aAAA;MACA,KAAAU,KAAA,gBAAAV,aAAA;IACA;IACAO,OAAA,WAAAA,QAAA;MAAA,IAAAI,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAlC,oBAAA,GAAAmC,IAAA,WAAAC,GAAA;gBACAX,KAAA,CAAAZ,IAAA,GAAAuB,GAAA,CAAAC,IAAA;cACA;YAAA;YAAA;cAAA,OAAAL,QAAA,CAAAM,IAAA;UAAA;QAAA,GAAAT,OAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}
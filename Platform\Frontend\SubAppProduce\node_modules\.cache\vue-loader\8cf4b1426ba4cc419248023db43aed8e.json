{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\plan\\components\\OverallControlPlanContent.vue?vue&type=style&index=0&id=35cafcc3&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\plan\\components\\OverallControlPlanContent.vue", "mtime": 1757926768460}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmNhcmR7DQogIGZsZXg6MTsNCiAgaGVpZ2h0OiAxMDAlOw0KICBtYXJnaW4tbGVmdDogMTZweDsNCiAgLmNvbnRlbnR7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGhlaWdodDogMTAwJTsNCiAgfQ0KICAuYnQtdGFibGV7DQogICAgZmxleDoxOw0KICB9DQp9DQouaGVhZGVyew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogZmxleC1lbmQ7DQogIC5wcm9qZWN0LW5hbWV7DQogICAgZm9udC1zaXplOiAxNnB4Ow0KICAgIGNvbG9yOiAjMzMzMzMzOw0KICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgIG1hcmdpbi1yaWdodDogOHB4Ow0KICB9DQogIC5lbC1pY29uLXRpbWV7DQogICAgbWFyZ2luLWxlZnQ6IDhweDsNCiAgICBtYXJnaW4tcmlnaHQ6IDRweDsNCiAgICBmb250LXNpemU6IDE0cHg7DQogIH0NCiAgLmxhYmVsew0KICAgIGNvbG9yOiAjMzMzMzMzOw0KICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgfQ0KICAudmFsdWV7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KICAgIGNvbG9yOiAjMzMzMzMzOw0KICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgIG1hcmdpbi1sZWZ0OiA3cHg7DQogIH0NCn0NCg0KLnBoYXNlLWxhYmVsIHsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgY29sb3I6ICMzMzM7DQogIGxpbmUtaGVpZ2h0OiA0MHB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgbWFyZ2luLXJpZ2h0OiAxMHB4Ow0KfQ0KDQouZGlhbG9nLWZvb3RlciB7DQogIHRleHQtYWxpZ246IHJpZ2h0Ow0KfQ0KDQouZWwtcm93IHsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCn0NCg0KLmVsLWZvcm0taXRlbSB7DQogIG1hcmdpbi1ib3R0b206IDA7DQp9DQouc3VtbWFyeXsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBjb2x1bW4tZ2FwOiAxMnB4Ow0KICBtYXJnaW46IDEycHggMDsNCiAgLmJsb2Nrew0KICAgIGhlaWdodDogMTA1cHg7DQogICAgYmFja2dyb3VuZDogI0ZGRkZGRjsNCiAgICBib3JkZXItcmFkaXVzOiA0cHggNHB4IDRweCA0cHg7DQogICAgYm9yZGVyOiAxcHggc29saWQgI0UyRTRFOTsNCiAgICBmbGV4OiAxOw0KICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICAuaWNvbnsNCiAgICAgIHdpZHRoOiA0OHB4Ow0KICAgICAgaGVpZ2h0OiA0OHB4Ow0KICAgIH0NCiAgICAuZmluaXNoew0KICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICBmb250LXNpemU6IDEycHg7DQogICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgfQ0KICAgIC5wcm9ncmVzcy1jb250YWluZXIgew0KICAgICAgIG1hcmdpbi1ib3R0b206IDhweDsNCiAgICAgfQ0KICAgIC5wcm9ncmVzcy1iYXIgew0KICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgIGhlaWdodDogMnB4Ow0KICAgICB9DQogICAgIC5wcm9ncmVzcy1iZyB7DQogICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmMGYwZjA7DQogICAgICAgYm9yZGVyLXJhZGl1czogMXB4Ow0KICAgICAgIG92ZXJmbG93OiB2aXNpYmxlOw0KICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICAgfQ0KICAgICAucHJvZ3Jlc3MtZmlsbCB7DQogICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgIGJvcmRlci1yYWRpdXM6IDFweDsNCiAgICAgICB0cmFuc2l0aW9uOiB3aWR0aCAwLjNzIGVhc2U7DQogICAgICAgbWluLXdpZHRoOiAycHg7DQogICAgICAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAgICB9DQogICAgIC5wcm9ncmVzcy1kb3Qgew0KICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgICAgIHJpZ2h0OiAtM3B4Ow0KICAgICAgICB0b3A6IC0ycHg7DQogICAgICAgIHdpZHRoOiA2cHg7DQogICAgICAgIGhlaWdodDogNnB4Ow0KICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7DQogICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7DQogICAgICB9DQogICAgICAucHJvZ3Jlc3MtZGF0YSB7DQogICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICAgICAgdG9wOiAtMjBweDsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCiAgICAgICAgcG9pbnRlci1ldmVudHM6IG5vbmU7DQogICAgICB9DQogICAgLnRvcHsNCiAgICAgIGhlaWdodDogNzZweDsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBwYWRkaW5nOiAwIDE1cHggMCA4cHg7DQogICAgICAuYmxvY2stbmFtZS13cmFwew0KICAgICAgICB3aWR0aDogNzJweDsNCiAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgICAgICBtYXJnaW4tcmlnaHQ6IDhweDsNCiAgICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICAgIH0NCiAgICAgIC5ibG9jay1uYW1lew0KICAgICAgICBjb2xvcjogIzY2NjY2NjsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAgICAgICB0b3A6IC02cHg7DQogICAgICAgIC8vIOi2heWHuuecgeeVpeWPtw0KICAgICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgICAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsNCiAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCiAgICAgIH0NCiAgICAgIC5wbGFuLXBlcmNlbnR7DQogICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgICAgfQ0KICAgICAgLnBsYW57DQogICAgICAgIGNvbG9yOiAjNjY2NjY2Ow0KICAgICAgfQ0KICAgIH0NCiAgICAuYm90dG9tew0KICAgICAgaGVpZ2h0OiAyOXB4Ow0KICAgICAgbGluZS1oZWlnaHQ6IDI5cHg7DQogICAgICBjb2xvcjogIzY2NjY2NjsNCiAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgIHBhZGRpbmc6IDAgMTJweDsNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["OverallControlPlanContent.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8tBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "OverallControlPlanContent.vue", "sourceRoot": "src/views/plan/components", "sourcesContent": ["<template>\r\n  <el-card class=\"card\">\r\n    <div class=\"content\">\r\n      <div class=\"header\">\r\n        <span class=\"project-name\">{{ curProject && curProject.Short_Name }}</span>\r\n        <span>\r\n          <i class=\"el-icon-time\" />\r\n          <span class=\"label\">总工期</span>\r\n          <span class=\"value\">{{ totalProjectDuration }}天</span>\r\n        </span>\r\n        <span>\r\n          <i class=\"el-icon-time\" />\r\n          <span class=\"label\">剩余工期</span>\r\n          <span class=\"value\">{{ remainingDuration }}天</span>\r\n        </span>\r\n      </div>\r\n      <div class=\"summary\">\r\n        <div v-for=\"(item) in nodeList\" :key=\"item.Id\" class=\"block\" @click=\"toDetail(item)\">\r\n          <div class=\"top\" :style=\"{background: `linear-gradient( 135deg, ${item.color}33 0%, #D6EAFF33 100%)`}\">\r\n            <div class=\"block-name-wrap\">\r\n              <img :src=\"item.icon\" class=\"icon\">\r\n              <div class=\"block-name\">{{ item.name }}</div>\r\n            </div>\r\n            <div style=\"flex:1;height: 100%;display: flex;flex-direction: column;justify-content: center;margin-top: 8px\">\r\n              <div class=\"progress-container\">\r\n                <div class=\"progress-bar\">\r\n                  <div class=\"progress-bg\">\r\n                    <div\r\n                      class=\"progress-fill\"\r\n                      :style=\"{\r\n                        width: parseFloat(item.finishPercent) > 100 ? '100%' : item.finishPercent,\r\n                        backgroundColor: item.color\r\n                      }\"\r\n                    >\r\n                      <div\r\n                        class=\"progress-dot\"\r\n                        :style=\"{\r\n                          backgroundColor: item.color\r\n                        }\"\r\n                      />\r\n                    </div>\r\n                    <div\r\n                      class=\"progress-data\"\r\n                      :style=\"{\r\n                        left: parseFloat(item.finishPercent) > 100 ? '100%' : item.finishPercent,\r\n                        color: item.color,\r\n                        transform: parseFloat(item.finishPercent) > 80 ? 'translateX(-100%)' : 'translateX(0%)'\r\n                      }\"\r\n                    >\r\n                      <span class=\"finish-value\">完成:{{ item.finishSummary }}t</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"plan-percent\">\r\n                <div class=\"plan\">\r\n                  计划：{{ item.planSummary }}t\r\n                </div>\r\n                <div :style=\"{color:item.color,fontWeight:'bold'}\">\r\n                  {{ item.finishPercent }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"bottom\">\r\n            <span>总：{{ item.duration }}</span>\r\n            <span style=\"margin-left: 14px\"> <i class=\"el-icon-date\" /> {{ item.beginDate }} - {{ item.endDate }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <el-form ref=\"form\" inline style=\"display: flex;align-items: center\">\r\n        <el-form-item>\r\n          <el-upload\r\n            :action=\"$baseUrl + 'PRO/ControlPlan/ImportTotalPlan'\"\r\n            :show-file-list=\"false\"\r\n            :on-success=\"uploadSuccess\"\r\n            :headers=\"headers\"\r\n            :data=\"uploadData\"\r\n          >\r\n            <el-button type=\"primary\">导入总控计划</el-button>\r\n          </el-upload>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" :loading=\"exportLoading\" @click=\"exportFile()\">导出总控计划</el-button>\r\n        </el-form-item>\r\n        <el-form-item style=\"margin-right: auto\">\r\n          <el-button type=\"primary\" :loading=\"updateLoading\" @click=\"updateData()\">手动更新计划</el-button>\r\n        </el-form-item>\r\n        <el-form-item label=\"区域\">\r\n          <SelectArea v-model=\"queryModel.Areas\" :project-id=\"curProject.Sys_Project_Id\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"显示范围\">\r\n          <el-select v-model=\"queryModel.Range\" multiple clearable collapse-tags>\r\n            <el-option v-for=\"item in nodeList\" :key=\"item.code\" :label=\"`过滤${item.name}完成`\" :value=\"item.code\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"search\">搜索</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <bt-table\r\n        v-if=\"nodeList && nodeList.length\"\r\n        class=\"bt-table\"\r\n        :config=\"config\"\r\n        :header-cell-style=\"cellStyle\"\r\n      >\r\n        <template #actions=\"{row}\">\r\n          <el-button type=\"text\" @click=\"openDialog(row)\">编辑</el-button>\r\n        </template>\r\n      </bt-table>\r\n    </div>\r\n\r\n    <!-- 编辑计划弹窗 -->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      title=\"编辑计划\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"1200px\"\r\n      :before-close=\"handleClose\"\r\n    >\r\n      <el-form ref=\"planForm\" :model=\"planForm\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"单体/分区\">\r\n              <span>{{ currentRow.FullAreaName }}</span>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 深化设计 -->\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <div class=\"phase-label\">{{ nodeList[0] && nodeList[0].name }}</div>\r\n          </el-col>\r\n          <el-col :span=\"10\">\r\n            <el-form-item label=\"计划时间\">\r\n              <el-date-picker\r\n                v-model=\"planForm.Deepen_Date_Range\"\r\n                type=\"daterange\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始日期\"\r\n                end-placeholder=\"结束日期\"\r\n                format=\"yyyy-MM-dd\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                @change=\"(val) => handleDateRangeChange('Deepen', val)\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"总工程量(t)\">\r\n              <el-input v-model=\"planForm.Deepen_Engineer_Quantity\" type=\"number\" placeholder=\"请输入\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-form-item label=\"总工期\">\r\n              <span>{{ planForm.Deepen_Duration }}天</span>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 材料采购 -->\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <div class=\"phase-label\">{{ nodeList[1] && nodeList[1].name }}</div>\r\n          </el-col>\r\n          <el-col :span=\"10\">\r\n            <el-form-item label=\"计划时间\">\r\n              <el-date-picker\r\n                v-model=\"planForm.Purchase_Date_Range\"\r\n                type=\"daterange\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始日期\"\r\n                end-placeholder=\"结束日期\"\r\n                format=\"yyyy-MM-dd\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                @change=\"(val) => handleDateRangeChange('Purchase', val)\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"总工程量(t)\">\r\n              <el-input v-model=\"planForm.Purchase_Engineer_Quantity\" placeholder=\"请输入\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-form-item label=\"总工期\">\r\n              <span>{{ planForm.Purchase_Duration }}天</span>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 生产加工 -->\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <div class=\"phase-label\">{{ nodeList[2] && nodeList[2].name }}</div>\r\n          </el-col>\r\n          <el-col :span=\"10\">\r\n            <el-form-item label=\"计划时间\">\r\n              <el-date-picker\r\n                v-model=\"planForm.Product_Date_Range\"\r\n                type=\"daterange\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始日期\"\r\n                end-placeholder=\"结束日期\"\r\n                format=\"yyyy-MM-dd\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                @change=\"(val) => handleDateRangeChange('Product', val)\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"总工程量(t)\">\r\n              <el-input v-model=\"planForm.Product_Engineer_Quantity\" placeholder=\"请输入\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-form-item label=\"总工期\">\r\n              <span>{{ planForm.Product_Duration }}天</span>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 施工安装 -->\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <div class=\"phase-label\">{{ nodeList[3] && nodeList[3].name }}</div>\r\n          </el-col>\r\n          <el-col :span=\"10\">\r\n            <el-form-item label=\"计划时间\">\r\n              <el-date-picker\r\n                v-model=\"planForm.Install_Date_Range\"\r\n                type=\"daterange\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始日期\"\r\n                end-placeholder=\"结束日期\"\r\n                format=\"yyyy-MM-dd\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                @change=\"(val) => handleDateRangeChange('Install', val)\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"总工程量(t)\">\r\n              <el-input v-model=\"planForm.Install_Engineer_Quantity\" placeholder=\"请输入\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-form-item label=\"总工期\">\r\n              <span>{{ planForm.Install_Duration }}天</span>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" :loading=\"saveLoading\" @click=\"saveAreaPlan\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </el-card>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  ExportTotalControlPlan, GenerateProjectPlanBusinessData,\r\n  GetConfigs,\r\n  GetMonomerListByProjectId,\r\n  GetTotalControlPlanEntity,\r\n  GetTotalControlPlanList,\r\n  SaveTotalControlPlanEntity\r\n} from '@/api/plm/projects'\r\nimport SelectArea from '@/components/Select/SelectArea/index.vue'\r\nimport moment from 'moment'\r\nimport { combineURL } from '@/utils'\r\nimport { getToken } from '@/utils/auth'\r\nimport { isRouteNameExists } from '@/utils/router'\r\n\r\nexport default {\r\n  name: 'OverallControlPlanContent',\r\n  components: { SelectArea },\r\n  props: {\r\n    curProject: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      queryModel: {\r\n        Areas: [],\r\n        Range: []\r\n      },\r\n      // 总工期和剩余工期\r\n      totalProjectDuration: 0,\r\n      remainingDuration: 0,\r\n      config: {\r\n        pageSizeOptions: [10, 20, 50, 100],\r\n        pageSize: 20,\r\n        total: 0,\r\n        loading: false,\r\n        currentPage: 1,\r\n        tableColumns: [],\r\n        tableData: [],\r\n        keyField: 'Area_Id'\r\n      },\r\n      monomerList: [],\r\n      nodeList: [],\r\n      // 弹窗相关数据\r\n      dialogVisible: false,\r\n      currentRow: {},\r\n      planForm: {\r\n        Area_Id: '',\r\n        Deepen_Begin_Date: '',\r\n        Deepen_End_Date: '',\r\n        Deepen_Date_Range: [],\r\n        Deepen_Duration: 0,\r\n        Deepen_Engineer_Quantity: 0,\r\n        Purchase_Begin_Date: '',\r\n        Purchase_End_Date: '',\r\n        Purchase_Date_Range: [],\r\n        Purchase_Duration: 0,\r\n        Purchase_Engineer_Quantity: 0,\r\n        Product_Begin_Date: '',\r\n        Product_End_Date: '',\r\n        Product_Date_Range: [],\r\n        Product_Duration: 0,\r\n        Product_Engineer_Quantity: 0,\r\n        Install_Begin_Date: '',\r\n        Install_End_Date: '',\r\n        Install_Date_Range: [],\r\n        Install_Duration: 0,\r\n        Install_Engineer_Quantity: 0\r\n      },\r\n      cloneTableData: [],\r\n      exportLoading: false,\r\n      updateLoading: false,\r\n      saveLoading: false,\r\n      headers: {\r\n        Authorization: getToken(),\r\n        Last_Working_Object_Id: localStorage.getItem('Last_Working_Object_Id')\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    uploadData() {\r\n      return { data: JSON.stringify({ ProjectId: this.curProject?.Sys_Project_Id, CompanyId: localStorage.getItem('CurReferenceId') }) }\r\n    }\r\n  },\r\n  watch: {\r\n    curProject: {\r\n      deep: true,\r\n      handler: function() {\r\n        if (this.curProject && this.curProject.Sys_Project_Id) {\r\n          this.queryModel.Areas = []\r\n          this.getMonomerList()\r\n          this.getTableData()\r\n        }\r\n      }\r\n    }\r\n  },\r\n  async created() {\r\n    await this.getConfig()\r\n    if (this.curProject && this.curProject.Sys_Project_Id) {\r\n      this.getMonomerList()\r\n      this.getTableData()\r\n    }\r\n  },\r\n  methods: {\r\n    cellStyle({ column }) {\r\n      const style = {}\r\n      if (column.field && column.field.startsWith('Deepen')) {\r\n        style.backgroundColor = `rgba(41, 141, 255, 0.1)!important`\r\n      }\r\n      if (column.field && column.field.startsWith('Purchase')) {\r\n        style.backgroundColor = `rgba(189, 109, 246, 0.1)!important`\r\n      }\r\n      if (column.field && column.field.startsWith('Product')) {\r\n        style.backgroundColor = `rgba(0, 195, 97, 0.1)!important`\r\n      }\r\n      if (column.field && column.field.startsWith('Install')) {\r\n        style.backgroundColor = `rgba(0, 177, 191, 0.1)!important`\r\n      }\r\n      return style\r\n    },\r\n    async getConfig() {\r\n      const res = await GetConfigs({\r\n        CompanyId: localStorage.getItem('CurReferenceId')\r\n      })\r\n      this.nodeList = res.Data.map(item => {\r\n        return {\r\n          ...item,\r\n          name: item.Plan_Name,\r\n          code: ['Deepen', 'Purchase', 'Product', 'Install'][item.Plan_Type - 1],\r\n          icon: require(`@/assets/PLM/plan_summary_${item.Plan_Type}.png`),\r\n          color: ['#298DFF', '#BD6DF6', '#00C361', '#00B1BF'][item.Plan_Type - 1],\r\n          route: ['PRODeepenPlanTrackCom', 'PROPurchasePlanTrackCom', 'PROProducePlanTrackCom', 'PROConstructionPlanTrackCom'][item.Plan_Type - 1],\r\n          visible: true\r\n        }\r\n      })\r\n      this.getRenderColumns()\r\n    },\r\n    getRenderColumns() {\r\n      const columns = this.nodeList.filter(i => i.visible).map(item => {\r\n        return {\r\n          width: 150,\r\n          label: item.name,\r\n          otherOptions: { align: 'center', fixed: '' },\r\n          key: item.code,\r\n          children: [\r\n            { key: item.code + '_Begin_Date', width: '160',\r\n              label: '开始时间', otherOptions: { align: 'center' },\r\n              render: (row) => {\r\n                if (!row[item.code + '_Begin_Date']) return '-'\r\n                const value = moment(row[item.code + '_Begin_Date']).format('YYYY-MM-DD')\r\n                return this.$createElement('div', {}, value)\r\n              }\r\n            },\r\n            { key: item.code + '_End_Date', width: '160',\r\n              label: '结束时间', otherOptions: { align: 'center' },\r\n              render: (row) => {\r\n                if (!row[item.code + '_End_Date']) return '-'\r\n                const value = moment(row[item.code + '_End_Date']).format('YYYY-MM-DD')\r\n                return this.$createElement('div', { }, value)\r\n              }\r\n            },\r\n            { key: item.code + '_Duration', width: '160',\r\n              label: '总工期(天)', otherOptions: { align: 'center' }\r\n            },\r\n            { key: item.code + '_Engineer_Quantity', width: '160',\r\n              label: '计划工程量(t)', otherOptions: { align: 'center' }\r\n            },\r\n            { key: item.code + '_Finish_Quantity', width: '160',\r\n              label: '实际量(t)', otherOptions: { align: 'center' }\r\n            },\r\n            { key: item.code + '_Finish_State', width: '160',\r\n              label: '完成状态', otherOptions: { align: 'center' },\r\n              render: (row) => {\r\n                const value = row[item.code + '_Finish_State']\r\n                return this.$createElement('div', {\r\n                  style: {\r\n                    color: value === '已完成' ? '#67C23A' : value === '未完成' ? '#F56C6C' : ''\r\n                  }\r\n                }, value)\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      })\r\n      this.config.tableColumns = [{\r\n        width: 150, label: '单体/分区', key: 'FullAreaName', otherOptions: { align: 'left', fixed: 'left' }\r\n      }, ...columns]\r\n    },\r\n    search() {\r\n      // 过滤渲染列\r\n      this.nodeList.forEach(i => i.visible = true)\r\n      if (this.queryModel.Range && this.queryModel.Range.length > 0) {\r\n        this.queryModel.Range.forEach(i => {\r\n          this.nodeList.forEach(j => {\r\n            if (i === j.code) {\r\n              j.visible = false\r\n            }\r\n          })\r\n        })\r\n      }\r\n      this.getRenderColumns()\r\n      // 过滤区域\r\n      if (this.queryModel.Areas && this.queryModel.Areas.length > 0) {\r\n        this.config.tableData = this.cloneTableData.filter(item => {\r\n          return this.queryModel.Areas.includes(item.Area_Id)\r\n        })\r\n      } else {\r\n        this.config.tableData = JSON.parse(JSON.stringify(this.cloneTableData))\r\n      }\r\n      this.handleTableData()\r\n    },\r\n    getTableData() {\r\n      if (!this.curProject || !this.curProject.Sys_Project_Id) return\r\n      this.config.loading = true\r\n      GetTotalControlPlanList({\r\n        ProjectId: this.curProject.Sys_Project_Id,\r\n        CompanyId: localStorage.getItem('CurReferenceId')\r\n      }).then(res => {\r\n        this.config.tableData = res.Data.map(item => {\r\n          item.id = item.Area_Id\r\n          return item\r\n        })\r\n        this.cloneTableData = JSON.parse(JSON.stringify(res.Data))\r\n        this.handleTableData()\r\n      }).finally(() => {\r\n        this.config.loading = false\r\n      })\r\n    },\r\n    handleTableData() {\r\n      // 收集所有日期用于计算总工期和剩余工期\r\n      const allBeginDates = []\r\n      const allEndDates = []\r\n      this.totalProjectDuration = 0\r\n      this.remainingDuration = 0\r\n      this.nodeList.forEach(item => {\r\n        const beginDates = this.config.tableData\r\n          .map(row => row[item.code + '_Begin_Date'])\r\n          .filter(date => date && date.trim() !== '')\r\n        const endDates = this.config.tableData\r\n          .map(row => row[item.code + '_End_Date'])\r\n          .filter(date => date && date.trim() !== '')\r\n\r\n        allBeginDates.push(...beginDates)\r\n        allEndDates.push(...endDates)\r\n      })\r\n      // 计算项目总工期：最晚日期 - 最早日期 + 1\r\n      if (allBeginDates.length > 0 && allEndDates.length > 0) {\r\n        const earliestDate = allBeginDates.sort()[0]\r\n        const latestDate = allEndDates.sort((a, b) => new Date(b) - new Date(a))[0]\r\n\r\n        if (earliestDate && latestDate) {\r\n          const begin = new Date(earliestDate)\r\n          const end = new Date(latestDate)\r\n          this.totalProjectDuration = Math.floor((end - begin) / (1000 * 60 * 60 * 24)) + 1\r\n          // 计算剩余工期：最晚日期 - 当前日期 + 1\r\n          const today = new Date()\r\n          const remainingDays = Math.floor((end - today) / (1000 * 60 * 60 * 24)) + 1\r\n          this.remainingDuration = remainingDays > 0 ? remainingDays : 0\r\n        }\r\n      }\r\n\r\n      this.nodeList = this.nodeList.map(item => {\r\n        // 计算计划工程量汇总\r\n        const planSummary = this.config.tableData.reduce((sum, row) => {\r\n          const value = parseFloat(row[item.code + '_Engineer_Quantity']) || 0\r\n          return sum + value\r\n        }, 0)\r\n\r\n        // 计算实际完成量汇总\r\n        const finishSummary = this.config.tableData.reduce((sum, row) => {\r\n          const value = parseFloat(row[item.code + '_Finish_Quantity']) || 0\r\n          return sum + value\r\n        }, 0)\r\n\r\n        // 计算完成百分比\r\n        const finishPercent = planSummary > 0 ? (finishSummary / planSummary * 100) : 0\r\n\r\n        // 获取最早开始日期（排除空值）\r\n        const beginDates = this.config.tableData\r\n          .map(row => row[item.code + '_Begin_Date'])\r\n          .filter(date => date && date.trim() !== '')\r\n          .sort()\r\n        const beginDate = beginDates.length > 0 ? moment(beginDates[0]).format('YYYY/MM/DD') : ''\r\n\r\n        // 获取最晚结束日期（排除空值）\r\n        const endDates = this.config.tableData\r\n          .map(row => row[item.code + '_End_Date'])\r\n          .filter(date => date && date.trim() !== '')\r\n          .sort((a, b) => new Date(b) - new Date(a))\r\n        const endDate = endDates.length > 0 ? moment(endDates[0]).format('YYYY/MM/DD') : ''\r\n\r\n        // 计算总工期\r\n        let duration = 0\r\n        if (beginDate && endDate) {\r\n          const begin = new Date(beginDate)\r\n          const end = new Date(endDate)\r\n          duration = Math.floor((end - begin) / (1000 * 60 * 60 * 24)) + 1\r\n        }\r\n\r\n        return {\r\n          ...item,\r\n          planSummary: planSummary.toFixed(2),\r\n          finishSummary: finishSummary.toFixed(2),\r\n          finishPercent: finishPercent.toFixed(2) + '%',\r\n          beginDate: beginDate,\r\n          endDate: endDate,\r\n          duration: duration > 0 ? duration + '天' : ''\r\n        }\r\n      })\r\n    },\r\n    async getMonomerList() {\r\n      if (!this.curProject || !this.curProject.Sys_Project_Id) return\r\n      const res = await GetMonomerListByProjectId({\r\n        projectId: this.curProject.Sys_Project_Id\r\n      })\r\n      this.monomerList = res.Data\r\n    },\r\n    openDialog(row) {\r\n      this.currentRow = row\r\n      this.dialogVisible = true\r\n      GetTotalControlPlanEntity({\r\n        Area_Id: row.Area_Id\r\n      }).then(res => {\r\n        if (res.Data) {\r\n          this.planForm = { ...res.Data }\r\n          // 将开始和结束日期组合成日期范围\r\n          this.$set(this.planForm, 'Deepen_Date_Range', [res.Data.Deepen_Begin_Date || '', res.Data.Deepen_End_Date || ''])\r\n          this.$set(this.planForm, 'Purchase_Date_Range', [res.Data.Purchase_Begin_Date || '', res.Data.Purchase_End_Date || ''])\r\n          this.$set(this.planForm, 'Product_Date_Range', [res.Data.Product_Begin_Date || '', res.Data.Product_End_Date || ''])\r\n          this.$set(this.planForm, 'Install_Date_Range', [res.Data.Install_Begin_Date || '', res.Data.Install_End_Date || ''])\r\n          // 重新计算各阶段工期\r\n          this.calculateDuration('Deepen')\r\n          this.calculateDuration('Purchase')\r\n          this.calculateDuration('Product')\r\n          this.calculateDuration('Install')\r\n        }\r\n      })\r\n    },\r\n    saveAreaPlan() {\r\n      this.saveLoading = true\r\n      SaveTotalControlPlanEntity({\r\n        ...this.planForm,\r\n        Area_Id: this.currentRow.Area_Id,\r\n        Company_Id: localStorage.getItem('CurReferenceId'),\r\n        Project_Id: this.curProject.Sys_Project_Id\r\n      }).then(res => {\r\n        this.$message.success('保存成功')\r\n        this.dialogVisible = false\r\n        this.getTableData()\r\n      }).finally(() => {\r\n        this.saveLoading = false\r\n      })\r\n    },\r\n    handleDateRangeChange(type, dateRange) {\r\n      if (dateRange && dateRange.length === 2) {\r\n        this.$set(this.planForm, `${type}_Begin_Date`, dateRange[0])\r\n        this.$set(this.planForm, `${type}_End_Date`, dateRange[1])\r\n        this.$set(this.planForm, `${type}_Date_Range`, dateRange)\r\n        this.calculateDuration(type)\r\n      } else {\r\n        this.$set(this.planForm, `${type}_Begin_Date`, '')\r\n        this.$set(this.planForm, `${type}_End_Date`, '')\r\n        this.$set(this.planForm, `${type}_Date_Range`, [])\r\n        this.$set(this.planForm, `${type}_Duration`, 0)\r\n      }\r\n      // 强制更新视图\r\n      this.$forceUpdate()\r\n    },\r\n    calculateDuration(type) {\r\n      const beginDate = this.planForm[`${type}_Begin_Date`]\r\n      const endDate = this.planForm[`${type}_End_Date`]\r\n      if (beginDate && endDate) {\r\n        const begin = new Date(beginDate)\r\n        const end = new Date(endDate)\r\n        // 计算天数差并加1（包含开始和结束当天）\r\n        const duration = Math.floor((end - begin) / (1000 * 60 * 60 * 24)) + 1\r\n        this.$set(this.planForm, `${type}_Duration`, duration > 0 ? duration : 0)\r\n      } else {\r\n        this.$set(this.planForm, `${type}_Duration`, 0)\r\n      }\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n      this.planForm = {\r\n        Area_Id: '',\r\n        Deepen_Begin_Date: '',\r\n        Deepen_End_Date: '',\r\n        Deepen_Date_Range: [],\r\n        Deepen_Duration: 0,\r\n        Deepen_Engineer_Quantity: 0,\r\n        Purchase_Begin_Date: '',\r\n        Purchase_End_Date: '',\r\n        Purchase_Date_Range: [],\r\n        Purchase_Duration: 0,\r\n        Purchase_Engineer_Quantity: 0,\r\n        Product_Begin_Date: '',\r\n        Product_End_Date: '',\r\n        Product_Date_Range: [],\r\n        Product_Duration: 0,\r\n        Product_Engineer_Quantity: 0,\r\n        Install_Begin_Date: '',\r\n        Install_End_Date: '',\r\n        Install_Date_Range: [],\r\n        Install_Duration: 0,\r\n        Install_Engineer_Quantity: 0\r\n      }\r\n    },\r\n    uploadSuccess(res) {\r\n      if (res.IsSucceed) {\r\n        this.$message.success('导入成功')\r\n        this.getTableData()\r\n      } else {\r\n        this.$message.error(res.Message)\r\n        if (res.Data) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        }\r\n      }\r\n    },\r\n    exportFile() {\r\n      this.exportLoading = true\r\n      ExportTotalControlPlan({\r\n        CompanyId: localStorage.getItem('CurReferenceId'),\r\n        ProjectId: this.curProject.Sys_Project_Id,\r\n        FilterTypes: this.queryModel.Range.map(item => {\r\n          return ['Deepen', 'Purchase', 'Product', 'Install'].indexOf(item) + 1\r\n        }),\r\n        AreaIds: this.queryModel.Areas\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      }).finally(() => {\r\n        this.exportLoading = false\r\n      })\r\n    },\r\n    updateData() {\r\n      this.updateLoading = true\r\n      GenerateProjectPlanBusinessData({\r\n        tenantIds: localStorage.getItem('tenant')\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message.success('更新成功')\r\n          this.getTableData()\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      }).finally(() => {\r\n        this.updateLoading = false\r\n      })\r\n    },\r\n    toDetail(item) {\r\n      if (isRouteNameExists(item.route)) {\r\n        this.$router.push({\r\n          name: item.route\r\n        })\r\n      } else {\r\n        this.$message.error('当前账户无访问权限')\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.card{\r\n  flex:1;\r\n  height: 100%;\r\n  margin-left: 16px;\r\n  .content{\r\n    display: flex;\r\n    flex-direction: column;\r\n    height: 100%;\r\n  }\r\n  .bt-table{\r\n    flex:1;\r\n  }\r\n}\r\n.header{\r\n  display: flex;\r\n  align-items: flex-end;\r\n  .project-name{\r\n    font-size: 16px;\r\n    color: #333333;\r\n    font-weight: bold;\r\n    margin-right: 8px;\r\n  }\r\n  .el-icon-time{\r\n    margin-left: 8px;\r\n    margin-right: 4px;\r\n    font-size: 14px;\r\n  }\r\n  .label{\r\n    color: #333333;\r\n    font-size: 12px;\r\n  }\r\n  .value{\r\n    font-size: 14px;\r\n    color: #333333;\r\n    font-weight: bold;\r\n    margin-left: 7px;\r\n  }\r\n}\r\n\r\n.phase-label {\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  color: #333;\r\n  line-height: 40px;\r\n  text-align: center;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n.el-row {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.el-form-item {\r\n  margin-bottom: 0;\r\n}\r\n.summary{\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  column-gap: 12px;\r\n  margin: 12px 0;\r\n  .block{\r\n    height: 105px;\r\n    background: #FFFFFF;\r\n    border-radius: 4px 4px 4px 4px;\r\n    border: 1px solid #E2E4E9;\r\n    flex: 1;\r\n    cursor: pointer;\r\n    .icon{\r\n      width: 48px;\r\n      height: 48px;\r\n    }\r\n    .finish{\r\n      font-weight: bold;\r\n      font-size: 12px;\r\n      text-align: center;\r\n    }\r\n    .progress-container {\r\n       margin-bottom: 8px;\r\n     }\r\n    .progress-bar {\r\n       width: 100%;\r\n       height: 2px;\r\n     }\r\n     .progress-bg {\r\n       width: 100%;\r\n       height: 100%;\r\n       background-color: #f0f0f0;\r\n       border-radius: 1px;\r\n       overflow: visible;\r\n       position: relative;\r\n     }\r\n     .progress-fill {\r\n       height: 100%;\r\n       border-radius: 1px;\r\n       transition: width 0.3s ease;\r\n       min-width: 2px;\r\n       position: relative;\r\n     }\r\n     .progress-dot {\r\n        position: absolute;\r\n        right: -3px;\r\n        top: -2px;\r\n        width: 6px;\r\n        height: 6px;\r\n        border-radius: 50%;\r\n        transition: all 0.3s ease;\r\n      }\r\n      .progress-data {\r\n        position: absolute;\r\n        top: -20px;\r\n        font-size: 14px;\r\n        font-weight: bold;\r\n        white-space: nowrap;\r\n        transition: all 0.3s ease;\r\n        pointer-events: none;\r\n      }\r\n    .top{\r\n      height: 76px;\r\n      display: flex;\r\n      padding: 0 15px 0 8px;\r\n      .block-name-wrap{\r\n        width: 72px;\r\n        text-align: center;\r\n        margin-right: 8px;\r\n        height: 100%;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n      }\r\n      .block-name{\r\n        color: #666666;\r\n        font-size: 14px;\r\n        font-weight: bold;\r\n        position: relative;\r\n        top: -6px;\r\n        // 超出省略号\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n      .plan-percent{\r\n        font-size: 14px;\r\n        display: flex;\r\n        justify-content: space-between;\r\n      }\r\n      .plan{\r\n        color: #666666;\r\n      }\r\n    }\r\n    .bottom{\r\n      height: 29px;\r\n      line-height: 29px;\r\n      color: #666666;\r\n      font-size: 14px;\r\n      padding: 0 12px;\r\n    }\r\n  }\r\n}\r\n</style>"]}]}
<template>
  <div class="container abs100">
    <el-tabs v-model="activeName" @tab-click="changeTab">
      <!-- <el-tab-pane label="构件任务单" name="com" />
      <el-tab-pane label="部件任务单" name="unitPart" />
      <el-tab-pane label="零件任务单" name="part" /> -->
      <el-tab-pane v-for="item in bomList" :key="item.Code" :label="item.Display_Name" :name="item.Code" />
    </el-tabs>

    <div class="main-wrapper">
      <component :is="'MainPage'" :key="activeName" :has-unit-part="hasUnitPart" :page-type="activeName" />
    </div>
  </div>
</template>

<script>
import MainPage from './mainPage'
import { mapGetters } from 'vuex'
import { GetBOMInfo, getBomCode } from '@/views/PRO/bom-setting/utils'
export default {
  name: 'PROTaskList',
  components: {
    MainPage
  },
  data() {
    return {
      bomList: [],
      pgLoading: false,
      activeName: getBomCode('-1')
    }
  },
  computed: {

    isCom() {
      return this.activeName === getBomCode('-1')
    },
    ...mapGetters('tenant', ['isVersionFour']),
    hasUnitPart() {
      return this.isVersionFour
    }
  },
  async created() {
    const { list } = await GetBOMInfo()
    this.bomList = list || []
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event)
    },
    async changeTab(tab) {
      this.pgLoading = true
      this.activeName = tab.name
      await this.getTbConfigInfo()
      this.fetchData(1)
    },
    getTbConfigInfo() {

    },
    fetchData() {

    }
  }
}
</script>

<style scoped lang="scss">
.container {
  padding: 16px;
  display: flex;
  flex-direction: column;

  .top-x {
    padding: 0 16px;
    margin-bottom: 16px;
    background-color: #ffffff;

    .top-inner {
      display: flex;
      position: relative;
      overflow: hidden;

      &:after {
        content: "";
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 2px;
        background-color: #e4e7ed;
        z-index: 1;
      }

      .item {
        color: #999999;
        display: inline-block;
        padding: 20px 38px;

        &:hover {
          cursor: pointer;
        }
      }

      .cs-item-bar {
        display: inline-block;
        position: absolute;
        bottom: 0;
        left: 10px;
        height: 2px;
        width: 140px;
        background-color: #409eff;
        z-index: 2;
        transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      }

      .active {
        font-weight: 500;
        color: #298dff;
      }

      ::v-deep {
        .el-badge__content.is-fixed {
          right: 78px;
        }
      }
    }
  }

  .main-wrapper{
    flex: 1;
  }
  .el-tabs{
    margin-bottom: 16px;
    background-color: #fff;
    padding-left: 16px;
  }
}

</style>

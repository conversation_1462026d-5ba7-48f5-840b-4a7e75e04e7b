{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\plan\\setting.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\plan\\setting.vue", "mtime": 1757572678863}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetConfigs", "GetCurrCompanyProjectList", "SaveConfigs", "GetUserPage", "GetWarnConfigs", "SaveWarnConfigs", "ResetWarnConfig", "SyncAreaWarnConfig", "data", "userList", "nodeData", "companyId", "localStorage", "getItem", "treeData", "treeProps", "label", "id", "treeDefaultSelectedKey", "params", "CompanyId", "ProjectId", "AreaId", "resetLoading", "syncLoading", "activeTab", "warnRules", "computed", "showProjectButtons", "showPlanConfig", "showWarnConfig", "currentWarnRules", "created", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getTreeData", "getList", "stop", "methods", "nodeClick", "node", "_this2", "curProject", "isDefault", "length", "Plan_Type", "toString", "$nextTick", "getWarnRules", "Parent_Id", "Sys_Project_Id", "findTopLevelParent", "console", "log", "_this3", "_callee2", "res", "defaultNode", "_callee2$", "_context2", "companId", "IsCascade", "sent", "Short_Name", "Code", "loading", "concat", "_toConsumableArray", "Data", "map", "item", "getConfigList", "t0", "error", "_this4", "_callee3", "_callee3$", "_context3", "DepartmentId", "departmentId", "PageSize", "filter", "UserStatusName", "_this5", "_callee4", "_callee4$", "_context4", "$set", "abrupt", "saveConfig", "_this6", "_callee5", "_callee5$", "_context5", "IsSucceed", "$message", "success", "Message", "_this7", "findParent", "currentNode", "parentNode", "findNodeById", "findInTree", "nodes", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "value", "children", "found", "err", "e", "f", "resetConfig", "_this8", "_callee6", "_callee6$", "_context6", "finish", "syncToArea", "_this9", "_callee7", "_callee7$", "_context7", "handleTabClick", "tab", "name", "addWarnRule", "newRule", "parseInt", "Project_Id", "Area_Id", "Progress_Percent", "Finish_Percent", "Plan_Notice_Userids", "push", "deleteWarnRule", "index", "_this0", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "splice", "catch", "_this1", "_callee8", "_callee8$", "_context8", "saveAllConfig", "_this10", "_callee9", "_callee9$", "_context9", "saveWarnConfig", "warning", "_this11", "_callee0", "rules", "i", "rule", "_callee0$", "_context0", "isNaN", "Items"], "sources": ["src/views/plan/setting.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <bt-tree :data=\"treeData\" :props=\"treeProps\" :default-selected-key=\"treeDefaultSelectedKey\" node-key=\"Sys_Project_Id\" @node-click=\"nodeClick\">\r\n      <template #default=\"{ data }\">\r\n        <span v-if=\"data.Code\" style=\"color: #5ac8fa!important;\">({{ data.Code }})</span>\r\n        <span>{{ data.Short_Name }}</span>\r\n      </template>\r\n    </bt-tree>\r\n    <!-- 计划配置和预警规则 -->\r\n    <el-card class=\"card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <span>计划配置</span>\r\n        <div class=\"header-buttons\">\r\n          <div v-if=\"showProjectButtons\" class=\"project-buttons\">\r\n            <el-button\r\n              :loading=\"resetLoading\"\r\n              size=\"small\"\r\n              type=\"primary\"\r\n              @click=\"resetConfig\"\r\n            >\r\n              重置配置\r\n            </el-button>\r\n            <el-button\r\n              :loading=\"syncLoading\"\r\n              size=\"small\"\r\n              type=\"primary\"\r\n              @click=\"syncToArea\"\r\n            >\r\n              同步至项目分区\r\n            </el-button>\r\n          </div>\r\n          <el-button\r\n            type=\"primary\"\r\n            size=\"small\"\r\n            @click=\"saveAllConfig\"\r\n          >\r\n            保存\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      <div class=\"content\">\r\n        <!-- 计划配置 - 只在默认配置时显示 -->\r\n        <div v-if=\"showPlanConfig\" class=\"plan-config-section\">\r\n          <el-form style=\"width: 782px\" inline>\r\n            <el-row v-for=\"item in nodeData\" :key=\"item.Plan_Type\" :gutter=\"50\">\r\n              <el-form-item :label=\"item.Plan_Name||' '\" label-width=\"100px\">\r\n                <el-input v-model=\"item.Plan_Name\" style=\"width:150px\" clearable />\r\n              </el-form-item>\r\n              <el-form-item label=\"计划下发通知人\" label-width=\"150px\">\r\n                <el-select\r\n                  v-model=\"item.Plan_Notice_Userids\"\r\n                  placeholder=\"请选择\"\r\n                  style=\"width: 400px\"\r\n                  clearable\r\n                  filterable\r\n                  multiple\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in userList\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Display_Name\"\r\n                    :value=\"item.Id\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-row>\r\n\r\n          </el-form>\r\n        </div>\r\n\r\n        <!-- 预警规则 - 在非默认配置时显示 -->\r\n        <div v-if=\"showWarnConfig\" class=\"warn-config-section\">\r\n          <h3>预警规则</h3>\r\n          <!-- Tab切换 -->\r\n          <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\r\n            <el-tab-pane\r\n              v-for=\"item in nodeData\"\r\n              :key=\"item.Plan_Type\"\r\n              :label=\"item.Plan_Name\"\r\n              :name=\"item.Plan_Type.toString()\"\r\n            >\r\n              <!-- 预警规则表单 -->\r\n              <div class=\"warn-rules\">\r\n                <div class=\"rule-header\">\r\n                  <el-button size=\"small\" type=\"primary\" @click=\"addWarnRule\">新增预警规则</el-button>\r\n                </div>\r\n                <div v-if=\"currentWarnRules.length === 0\" class=\"empty-rules\">\r\n                  暂无预警规则\r\n                </div>\r\n                <el-form v-else>\r\n                  <div class=\"rules-list\">\r\n                    <div v-for=\"(rule, index) in currentWarnRules\" :key=\"index\" class=\"rule-item\">\r\n                      <el-row :gutter=\"20\">\r\n                        <el-col :span=\"4\">\r\n                          <el-form-item label=\"工期进度达到\" required>\r\n                            <el-input\r\n                              v-model=\"rule.Progress_Percent\"\r\n                              placeholder=\"70%\"\r\n                              style=\"width: 100%\"\r\n                            >\r\n                              <template slot=\"append\">%</template>\r\n                            </el-input>\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"4\">\r\n                          <el-form-item label=\"完成进度达到\" required>\r\n                            <el-input\r\n                              v-model=\"rule.Finish_Percent\"\r\n                              placeholder=\"70%\"\r\n                              style=\"width: 100%\"\r\n                            >\r\n                              <template slot=\"append\">%</template>\r\n                            </el-input>\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"14\">\r\n                          <el-form-item label=\"计划预警通知人\" required>\r\n                            <el-select\r\n                              v-model=\"rule.Plan_Notice_Userids\"\r\n                              placeholder=\"下拉选择当前公司下标准人，支持搜索\"\r\n                              style=\"width: 100%\"\r\n                              clearable\r\n                              filterable\r\n                              multiple\r\n                            >\r\n                              <el-option\r\n                                v-for=\"user in userList\"\r\n                                :key=\"user.Id\"\r\n                                :label=\"user.Display_Name\"\r\n                                :value=\"user.Id\"\r\n                              />\r\n                            </el-select>\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"2\">\r\n                          <el-button\r\n                            type=\"danger\"\r\n                            icon=\"el-icon-delete\"\r\n                            size=\"small\"\r\n                            @click=\"deleteWarnRule(index)\"\r\n                          />\r\n                        </el-col>\r\n                      </el-row>\r\n                    </div>\r\n                  </div>\r\n\r\n                </el-form>\r\n              </div>\r\n            </el-tab-pane>\r\n          </el-tabs>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { GetConfigs, GetCurrCompanyProjectList, SaveConfigs } from '@/api/plm/projects'\r\nimport { GetUserPage } from '@/api/sys'\r\nimport { GetWarnConfigs, SaveWarnConfigs, ResetWarnConfig, SyncAreaWarnConfig } from '@/api/PRO/control-plan'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      userList: [],\r\n      nodeData: [],\r\n      companyId: localStorage.getItem('CurReferenceId'),\r\n      treeData: [],\r\n      treeProps: {\r\n        label: 'Short_Name',\r\n        id: 'Sys_Project_Id'\r\n      },\r\n      treeDefaultSelectedKey: '',\r\n      params: {\r\n        CompanyId: localStorage.getItem('CurReferenceId'),\r\n        ProjectId: '',\r\n        AreaId: ''\r\n      },\r\n      resetLoading: false,\r\n      syncLoading: false,\r\n      activeTab: '',\r\n      warnRules: {} // 存储各个计划类型的预警规则\r\n    }\r\n  },\r\n  computed: {\r\n    showProjectButtons() {\r\n      // 只有在选中项目级节点（非默认配置且非区域）时才显示按钮\r\n      return this.params.ProjectId !== '' &&\r\n              this.params.ProjectId !== 'default_config' &&\r\n              this.params.AreaId === ''\r\n    },\r\n    showPlanConfig() {\r\n      // 只有在默认配置时才显示计划配置\r\n      return this.params.ProjectId === '' && this.params.AreaId === ''\r\n    },\r\n    showWarnConfig() {\r\n      // 预警规则无论何种情况都显示\r\n      return true\r\n    },\r\n    currentWarnRules() {\r\n      // 获取当前选中tab的预警规则\r\n      if (!this.activeTab || !this.warnRules[this.activeTab]) {\r\n        return []\r\n      }\r\n      return this.warnRules[this.activeTab]\r\n    }\r\n  },\r\n  async created() {\r\n    await this.getTreeData()\r\n    await this.getList()\r\n  },\r\n  methods: {\r\n    nodeClick(node) {\r\n      this.curProject = node\r\n\r\n      // 判断是否为默认配置节点\r\n      if (node.isDefault) {\r\n        this.params.ProjectId = ''\r\n        this.params.AreaId = ''\r\n        // 默认配置时也需要请求预警规则数据\r\n        if (this.nodeData.length > 0) {\r\n          this.activeTab = this.nodeData[0].Plan_Type.toString()\r\n          this.$nextTick(() => {\r\n            this.getWarnRules()\r\n          })\r\n        }\r\n        return\r\n      }\r\n\r\n      // 判断节点层级\r\n      // 如果节点没有父级ID或父级ID为空，说明是第一级（项目）\r\n      if (!node.Parent_Id || node.Parent_Id === '' || node.Parent_Id === null) {\r\n        // 第一级节点：设置ProjectId为当前节点ID，AreaId为空\r\n        this.params.ProjectId = node.Sys_Project_Id\r\n        this.params.AreaId = ''\r\n      } else {\r\n        // 其他级别节点（区域）：设置AreaId为当前节点ID，ProjectId为所属第一级节点ID\r\n        this.params.AreaId = node.Sys_Project_Id\r\n        // 查找所属的第一级节点ID\r\n        this.params.ProjectId = this.findTopLevelParent(node)\r\n      }\r\n\r\n      // 初始化activeTab并获取预警规则数据\r\n      if (this.nodeData.length > 0) {\r\n        this.activeTab = this.nodeData[0].Plan_Type.toString()\r\n        this.$nextTick(() => {\r\n          // 当节点变化时，重新请求预警规则数据\r\n          this.getWarnRules()\r\n        })\r\n      }\r\n\r\n      console.log(this.params)\r\n    },\r\n    async getTreeData() {\r\n      try {\r\n        const res = await GetCurrCompanyProjectList({\r\n          companId: localStorage.getItem('CurReferenceId'),\r\n          IsCascade: false\r\n        })\r\n\r\n        // 在最前面添加【默认配置】节点\r\n        const defaultNode = {\r\n          Sys_Project_Id: '',\r\n          Short_Name: '【默认配置】',\r\n          Code: '',\r\n          loading: false,\r\n          isDefault: true // 标记为默认配置节点\r\n        }\r\n\r\n        this.treeData = [defaultNode, ...res.Data.map(item => {\r\n          item.loading = false\r\n          return item\r\n        })]\r\n\r\n        // 默认选中【默认配置】节点\r\n        if (this.treeData.length) {\r\n          this.treeDefaultSelectedKey = ''\r\n          this.curProject = defaultNode\r\n          // 设置默认配置的参数\r\n          this.params.ProjectId = ''\r\n          this.params.AreaId = ''\r\n\r\n          // 先获取配置列表数据，然后再触发nodeClick\r\n          await this.getConfigList()\r\n          this.$nextTick(() => {\r\n            this.nodeClick(defaultNode)\r\n          })\r\n        }\r\n      } catch (error) {\r\n        console.error('获取树形数据失败:', error)\r\n      }\r\n    },\r\n    async getList() {\r\n      const res = await GetUserPage({\r\n        DepartmentId: this.departmentId,\r\n        PageSize: 10000\r\n      })\r\n      this.userList = res.Data.Data.filter(item => item.UserStatusName === '正常')\r\n    },\r\n    async getConfigList() {\r\n      try {\r\n        const res = await GetConfigs({\r\n          CompanyId: this.companyId\r\n        })\r\n        this.$set(this, 'nodeData', res.Data)\r\n        return res.Data\r\n      } catch (error) {\r\n        console.error('获取配置列表失败:', error)\r\n        return []\r\n      }\r\n    },\r\n    async saveConfig() {\r\n      try {\r\n        const res = await SaveConfigs(this.nodeData)\r\n        if (res.IsSucceed) {\r\n          this.$message.success('计划配置保存成功')\r\n          this.getConfigList()\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('计划配置保存失败')\r\n        console.error('保存计划配置错误:', error)\r\n      }\r\n    },\r\n    // 查找顶级父节点ID\r\n    findTopLevelParent(node) {\r\n      // 递归查找直到找到没有父级的节点\r\n      const findParent = (currentNode) => {\r\n        if (!currentNode.Parent_Id || currentNode.Parent_Id === '' || currentNode.Parent_Id === null) {\r\n          return currentNode.Sys_Project_Id\r\n        }\r\n        // 在树数据中查找父节点\r\n        const parentNode = this.findNodeById(currentNode.Parent_Id)\r\n        if (parentNode) {\r\n          return findParent(parentNode)\r\n        }\r\n        return currentNode.Sys_Project_Id\r\n      }\r\n      return findParent(node)\r\n    },\r\n    // 根据ID查找节点\r\n    findNodeById(id) {\r\n      const findInTree = (nodes) => {\r\n        for (const node of nodes) {\r\n          if (node.Sys_Project_Id === id) {\r\n            return node\r\n          }\r\n          if (node.children && node.children.length > 0) {\r\n            const found = findInTree(node.children)\r\n            if (found) return found\r\n          }\r\n        }\r\n        return null\r\n      }\r\n      return findInTree(this.treeData)\r\n    },\r\n    // 重置配置\r\n    async resetConfig() {\r\n      this.resetLoading = true\r\n      try {\r\n        const res = await ResetWarnConfig({\r\n          CompanyId: this.companyId,\r\n          ProjectId: this.params.ProjectId\r\n        })\r\n        if (res.IsSucceed) {\r\n          this.$message.success('重置配置成功')\r\n          this.getConfigList() // 重新获取配置列表\r\n        } else {\r\n          this.$message.error(res.Message || '重置配置失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('重置配置失败')\r\n        console.error('重置配置错误:', error)\r\n      } finally {\r\n        this.resetLoading = false\r\n      }\r\n    },\r\n    // 同步至项目分区\r\n    async syncToArea() {\r\n      this.syncLoading = true\r\n      try {\r\n        const res = await SyncAreaWarnConfig({\r\n          CompanyId: this.companyId,\r\n          ProjectId: this.params.ProjectId\r\n        })\r\n        if (res.IsSucceed) {\r\n          this.$message.success('同步至项目分区成功')\r\n        } else {\r\n          this.$message.error(res.Message || '同步至项目分区失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('同步至项目分区失败')\r\n        console.error('同步至项目分区错误:', error)\r\n      } finally {\r\n        this.syncLoading = false\r\n      }\r\n    },\r\n    // Tab切换处理\r\n    handleTabClick(tab) {\r\n      this.activeTab = tab.name\r\n      // 不重新请求数据，避免数据丢失\r\n    },\r\n    // 新增预警规则\r\n    addWarnRule() {\r\n      if (!this.warnRules[this.activeTab]) {\r\n        this.$set(this.warnRules, this.activeTab, [])\r\n      }\r\n      const newRule = {\r\n        Plan_Type: parseInt(this.activeTab),\r\n        Project_Id: this.params.ProjectId,\r\n        Area_Id: this.params.AreaId,\r\n        Progress_Percent: '',\r\n        Finish_Percent: '',\r\n        Plan_Notice_Userids: []\r\n      }\r\n      this.warnRules[this.activeTab].push(newRule)\r\n    },\r\n    // 删除预警规则\r\n    deleteWarnRule(index) {\r\n      this.$confirm('确定要删除这条预警规则吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.warnRules[this.activeTab].splice(index, 1)\r\n        this.$message.success('删除成功')\r\n      }).catch(() => {})\r\n    },\r\n    // 获取预警规则\r\n    async getWarnRules() {\r\n      try {\r\n        const res = await GetWarnConfigs({\r\n          CompanyId: this.companyId,\r\n          ProjectId: this.params.ProjectId,\r\n          AreaId: this.params.AreaId,\r\n          Plan_Type: parseInt(this.activeTab)\r\n        })\r\n        if (res.IsSucceed) {\r\n          this.$set(this.warnRules, this.activeTab, res.Data || [])\r\n        }\r\n      } catch (error) {\r\n        console.error('获取预警规则失败:', error)\r\n      }\r\n    },\r\n    // 保存所有配置\r\n    async saveAllConfig() {\r\n      try {\r\n        // 如果显示计划配置，保存计划配置\r\n        if (this.showPlanConfig && this.nodeData && this.nodeData.length) {\r\n          await this.saveConfig()\r\n        }\r\n\r\n        // 如果显示预警配置，保存预警配置\r\n        if (this.showWarnConfig) {\r\n          await this.saveWarnConfig()\r\n        }\r\n\r\n        // 如果两个都不显示或都没有数据，提示用户\r\n        if (!this.showPlanConfig && !this.showWarnConfig) {\r\n          this.$message.warning('暂无可保存的配置')\r\n        }\r\n      } catch (error) {\r\n        console.error('保存配置失败:', error)\r\n      }\r\n    },\r\n    // 保存预警配置\r\n    async saveWarnConfig() {\r\n      // 校验必填项\r\n      const rules = this.currentWarnRules\r\n      if (rules.length > 0) {\r\n        for (let i = 0; i < rules.length; i++) {\r\n          const rule = rules[i]\r\n          if (!rule.Progress_Percent || !rule.Finish_Percent || !rule.Plan_Notice_Userids || rule.Plan_Notice_Userids.length === 0) {\r\n            this.$message.error(`第${i + 1}条预警规则存在必填项未填写`)\r\n            return\r\n          }\r\n          // 验证百分比格式\r\n          if (isNaN(rule.Progress_Percent) || isNaN(rule.Finish_Percent)) {\r\n            this.$message.error(`第${i + 1}条预警规则的百分比必须为数字`)\r\n            return\r\n          }\r\n        }\r\n      }\r\n\r\n      try {\r\n        const res = await SaveWarnConfigs({\r\n          CompanyId: this.companyId,\r\n          ProjectId: this.params.ProjectId,\r\n          AreaId: this.params.AreaId,\r\n          Items: rules\r\n        })\r\n        if (res.IsSucceed) {\r\n          this.$message.success('预警规则保存成功')\r\n          this.getWarnRules() // 重新获取数据\r\n        } else {\r\n          this.$message.error(res.Message || '预警规则保存失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('保存失败')\r\n        console.error('保存预警配置错误:', error)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container{\r\n  font-family: PingFang SC, PingFang SC;\r\n  display: flex;\r\n  .card{\r\n    flex:1;\r\n    height: 100%;\r\n    margin-left: 16px;\r\n    .card-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      .header-buttons {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 8px;\r\n\r\n        .project-buttons {\r\n          display: flex;\r\n          gap: 8px;\r\n        }\r\n\r\n        .el-button {\r\n          margin: 0;\r\n        }\r\n      }\r\n    }\r\n    .content{\r\n      display: flex;\r\n      flex-direction: column;\r\n      height: 100%;\r\n    }\r\n    .bt-table{\r\n      flex:1;\r\n    }\r\n\r\n    // 计划配置和预警规则section样式\r\n    .plan-config-section {\r\n      margin-bottom: 30px;\r\n    }\r\n\r\n    .warn-config-section {\r\n      h3 {\r\n        margin: 0 0 20px 0;\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        color: #303133;\r\n        padding-bottom: 8px;\r\n      }\r\n    }\r\n\r\n    // 预警规则样式\r\n    .warn-rules {\r\n      .rule-header {\r\n        margin-bottom: 20px;\r\n      }\r\n\r\n      .empty-rules {\r\n        text-align: center;\r\n        color: #999;\r\n        padding: 40px 0;\r\n        font-size: 14px;\r\n      }\r\n\r\n      .rules-list {\r\n        .rule-item {\r\n          margin-bottom: 20px;\r\n          padding: 20px;\r\n          border: 1px solid #e4e7ed;\r\n          border-radius: 4px;\r\n          background-color: #fafafa;\r\n\r\n          .el-form-item {\r\n            margin-bottom: 0;\r\n          }\r\n\r\n          .el-col:last-child {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            padding-top: 30px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .header{\r\n    display: flex;\r\n    align-items: flex-end;\r\n    .project-name{\r\n      font-size: 16px;\r\n      color: #333333;\r\n      font-weight: bold;\r\n      margin-right: 8px;\r\n    }\r\n    .el-icon-time{\r\n      margin-left: 8px;\r\n      margin-right: 4px;\r\n      font-size: 14px;\r\n    }\r\n    .label{\r\n      color: #333333;\r\n      font-size: 12px;\r\n    }\r\n    .value{\r\n      font-size: 14px;\r\n      color: #333333;\r\n      font-weight: bold;\r\n      margin-left: 7px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4JA,SAAAA,UAAA,EAAAC,yBAAA,EAAAC,WAAA;AACA,SAAAC,WAAA;AACA,SAAAC,cAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,kBAAA;AAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,QAAA;MACAC,SAAA,EAAAC,YAAA,CAAAC,OAAA;MACAC,QAAA;MACAC,SAAA;QACAC,KAAA;QACAC,EAAA;MACA;MACAC,sBAAA;MACAC,MAAA;QACAC,SAAA,EAAAR,YAAA,CAAAC,OAAA;QACAQ,SAAA;QACAC,MAAA;MACA;MACAC,YAAA;MACAC,WAAA;MACAC,SAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACAC,kBAAA,WAAAA,mBAAA;MACA;MACA,YAAAT,MAAA,CAAAE,SAAA,WACA,KAAAF,MAAA,CAAAE,SAAA,yBACA,KAAAF,MAAA,CAAAG,MAAA;IACA;IACAO,cAAA,WAAAA,eAAA;MACA;MACA,YAAAV,MAAA,CAAAE,SAAA,gBAAAF,MAAA,CAAAG,MAAA;IACA;IACAQ,cAAA,WAAAA,eAAA;MACA;MACA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MACA;MACA,UAAAN,SAAA,UAAAC,SAAA,MAAAD,SAAA;QACA;MACA;MACA,YAAAC,SAAA,MAAAD,SAAA;IACA;EACA;EACAO,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACAT,KAAA,CAAAU,WAAA;UAAA;YAAAH,QAAA,CAAAE,IAAA;YAAA,OACAT,KAAA,CAAAW,OAAA;UAAA;UAAA;YAAA,OAAAJ,QAAA,CAAAK,IAAA;QAAA;MAAA,GAAAR,OAAA;IAAA;EACA;EACAS,OAAA;IACAC,SAAA,WAAAA,UAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,UAAA,GAAAF,IAAA;;MAEA;MACA,IAAAA,IAAA,CAAAG,SAAA;QACA,KAAAhC,MAAA,CAAAE,SAAA;QACA,KAAAF,MAAA,CAAAG,MAAA;QACA;QACA,SAAAZ,QAAA,CAAA0C,MAAA;UACA,KAAA3B,SAAA,QAAAf,QAAA,IAAA2C,SAAA,CAAAC,QAAA;UACA,KAAAC,SAAA;YACAN,MAAA,CAAAO,YAAA;UACA;QACA;QACA;MACA;;MAEA;MACA;MACA,KAAAR,IAAA,CAAAS,SAAA,IAAAT,IAAA,CAAAS,SAAA,WAAAT,IAAA,CAAAS,SAAA;QACA;QACA,KAAAtC,MAAA,CAAAE,SAAA,GAAA2B,IAAA,CAAAU,cAAA;QACA,KAAAvC,MAAA,CAAAG,MAAA;MACA;QACA;QACA,KAAAH,MAAA,CAAAG,MAAA,GAAA0B,IAAA,CAAAU,cAAA;QACA;QACA,KAAAvC,MAAA,CAAAE,SAAA,QAAAsC,kBAAA,CAAAX,IAAA;MACA;;MAEA;MACA,SAAAtC,QAAA,CAAA0C,MAAA;QACA,KAAA3B,SAAA,QAAAf,QAAA,IAAA2C,SAAA,CAAAC,QAAA;QACA,KAAAC,SAAA;UACA;UACAN,MAAA,CAAAO,YAAA;QACA;MACA;MAEAI,OAAA,CAAAC,GAAA,MAAA1C,MAAA;IACA;IACAwB,WAAA,WAAAA,YAAA;MAAA,IAAAmB,MAAA;MAAA,OAAA5B,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA2B,SAAA;QAAA,IAAAC,GAAA,EAAAC,WAAA;QAAA,OAAA9B,mBAAA,GAAAG,IAAA,UAAA4B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1B,IAAA,GAAA0B,SAAA,CAAAzB,IAAA;YAAA;cAAAyB,SAAA,CAAA1B,IAAA;cAAA0B,SAAA,CAAAzB,IAAA;cAAA,OAEAzC,yBAAA;gBACAmE,QAAA,EAAAxD,YAAA,CAAAC,OAAA;gBACAwD,SAAA;cACA;YAAA;cAHAL,GAAA,GAAAG,SAAA,CAAAG,IAAA;cAKA;cACAL,WAAA;gBACAP,cAAA;gBACAa,UAAA;gBACAC,IAAA;gBACAC,OAAA;gBACAtB,SAAA;cACA;cAEAW,MAAA,CAAAhD,QAAA,IAAAmD,WAAA,EAAAS,MAAA,CAAAC,kBAAA,CAAAX,GAAA,CAAAY,IAAA,CAAAC,GAAA,WAAAC,IAAA;gBACAA,IAAA,CAAAL,OAAA;gBACA,OAAAK,IAAA;cACA;;cAEA;cAAA,KACAhB,MAAA,CAAAhD,QAAA,CAAAsC,MAAA;gBAAAe,SAAA,CAAAzB,IAAA;gBAAA;cAAA;cACAoB,MAAA,CAAA5C,sBAAA;cACA4C,MAAA,CAAAZ,UAAA,GAAAe,WAAA;cACA;cACAH,MAAA,CAAA3C,MAAA,CAAAE,SAAA;cACAyC,MAAA,CAAA3C,MAAA,CAAAG,MAAA;;cAEA;cAAA6C,SAAA,CAAAzB,IAAA;cAAA,OACAoB,MAAA,CAAAiB,aAAA;YAAA;cACAjB,MAAA,CAAAP,SAAA;gBACAO,MAAA,CAAAf,SAAA,CAAAkB,WAAA;cACA;YAAA;cAAAE,SAAA,CAAAzB,IAAA;cAAA;YAAA;cAAAyB,SAAA,CAAA1B,IAAA;cAAA0B,SAAA,CAAAa,EAAA,GAAAb,SAAA;cAGAP,OAAA,CAAAqB,KAAA,cAAAd,SAAA,CAAAa,EAAA;YAAA;YAAA;cAAA,OAAAb,SAAA,CAAAtB,IAAA;UAAA;QAAA,GAAAkB,QAAA;MAAA;IAEA;IACAnB,OAAA,WAAAA,QAAA;MAAA,IAAAsC,MAAA;MAAA,OAAAhD,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA+C,SAAA;QAAA,IAAAnB,GAAA;QAAA,OAAA7B,mBAAA,GAAAG,IAAA,UAAA8C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5C,IAAA,GAAA4C,SAAA,CAAA3C,IAAA;YAAA;cAAA2C,SAAA,CAAA3C,IAAA;cAAA,OACAvC,WAAA;gBACAmF,YAAA,EAAAJ,MAAA,CAAAK,YAAA;gBACAC,QAAA;cACA;YAAA;cAHAxB,GAAA,GAAAqB,SAAA,CAAAf,IAAA;cAIAY,MAAA,CAAAzE,QAAA,GAAAuD,GAAA,CAAAY,IAAA,CAAAA,IAAA,CAAAa,MAAA,WAAAX,IAAA;gBAAA,OAAAA,IAAA,CAAAY,cAAA;cAAA;YAAA;YAAA;cAAA,OAAAL,SAAA,CAAAxC,IAAA;UAAA;QAAA,GAAAsC,QAAA;MAAA;IACA;IACAJ,aAAA,WAAAA,cAAA;MAAA,IAAAY,MAAA;MAAA,OAAAzD,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAwD,SAAA;QAAA,IAAA5B,GAAA;QAAA,OAAA7B,mBAAA,GAAAG,IAAA,UAAAuD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArD,IAAA,GAAAqD,SAAA,CAAApD,IAAA;YAAA;cAAAoD,SAAA,CAAArD,IAAA;cAAAqD,SAAA,CAAApD,IAAA;cAAA,OAEA1C,UAAA;gBACAoB,SAAA,EAAAuE,MAAA,CAAAhF;cACA;YAAA;cAFAqD,GAAA,GAAA8B,SAAA,CAAAxB,IAAA;cAGAqB,MAAA,CAAAI,IAAA,CAAAJ,MAAA,cAAA3B,GAAA,CAAAY,IAAA;cAAA,OAAAkB,SAAA,CAAAE,MAAA,WACAhC,GAAA,CAAAY,IAAA;YAAA;cAAAkB,SAAA,CAAArD,IAAA;cAAAqD,SAAA,CAAAd,EAAA,GAAAc,SAAA;cAEAlC,OAAA,CAAAqB,KAAA,cAAAa,SAAA,CAAAd,EAAA;cAAA,OAAAc,SAAA,CAAAE,MAAA,WACA;YAAA;YAAA;cAAA,OAAAF,SAAA,CAAAjD,IAAA;UAAA;QAAA,GAAA+C,QAAA;MAAA;IAEA;IACAK,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MAAA,OAAAhE,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA+D,SAAA;QAAA,IAAAnC,GAAA;QAAA,OAAA7B,mBAAA,GAAAG,IAAA,UAAA8D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5D,IAAA,GAAA4D,SAAA,CAAA3D,IAAA;YAAA;cAAA2D,SAAA,CAAA5D,IAAA;cAAA4D,SAAA,CAAA3D,IAAA;cAAA,OAEAxC,WAAA,CAAAgG,MAAA,CAAAxF,QAAA;YAAA;cAAAsD,GAAA,GAAAqC,SAAA,CAAA/B,IAAA;cACA,IAAAN,GAAA,CAAAsC,SAAA;gBACAJ,MAAA,CAAAK,QAAA,CAAAC,OAAA;gBACAN,MAAA,CAAAnB,aAAA;cACA;gBACAmB,MAAA,CAAAK,QAAA,CAAAtB,KAAA,CAAAjB,GAAA,CAAAyC,OAAA;cACA;cAAAJ,SAAA,CAAA3D,IAAA;cAAA;YAAA;cAAA2D,SAAA,CAAA5D,IAAA;cAAA4D,SAAA,CAAArB,EAAA,GAAAqB,SAAA;cAEAH,MAAA,CAAAK,QAAA,CAAAtB,KAAA;cACArB,OAAA,CAAAqB,KAAA,cAAAoB,SAAA,CAAArB,EAAA;YAAA;YAAA;cAAA,OAAAqB,SAAA,CAAAxD,IAAA;UAAA;QAAA,GAAAsD,QAAA;MAAA;IAEA;IACA;IACAxC,kBAAA,WAAAA,mBAAAX,IAAA;MAAA,IAAA0D,MAAA;MACA;MACA,IAAAC,WAAA,YAAAA,WAAAC,WAAA;QACA,KAAAA,WAAA,CAAAnD,SAAA,IAAAmD,WAAA,CAAAnD,SAAA,WAAAmD,WAAA,CAAAnD,SAAA;UACA,OAAAmD,WAAA,CAAAlD,cAAA;QACA;QACA;QACA,IAAAmD,UAAA,GAAAH,MAAA,CAAAI,YAAA,CAAAF,WAAA,CAAAnD,SAAA;QACA,IAAAoD,UAAA;UACA,OAAAF,WAAA,CAAAE,UAAA;QACA;QACA,OAAAD,WAAA,CAAAlD,cAAA;MACA;MACA,OAAAiD,WAAA,CAAA3D,IAAA;IACA;IACA;IACA8D,YAAA,WAAAA,aAAA7F,EAAA;MACA,IAAA8F,WAAA,YAAAA,WAAAC,KAAA;QAAA,IAAAC,SAAA,GAAAC,0BAAA,CACAF,KAAA;UAAAG,KAAA;QAAA;UAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;YAAA,IAAAtE,IAAA,GAAAmE,KAAA,CAAAI,KAAA;YACA,IAAAvE,IAAA,CAAAU,cAAA,KAAAzC,EAAA;cACA,OAAA+B,IAAA;YACA;YACA,IAAAA,IAAA,CAAAwE,QAAA,IAAAxE,IAAA,CAAAwE,QAAA,CAAApE,MAAA;cACA,IAAAqE,KAAA,GAAAV,WAAA,CAAA/D,IAAA,CAAAwE,QAAA;cACA,IAAAC,KAAA,SAAAA,KAAA;YACA;UACA;QAAA,SAAAC,GAAA;UAAAT,SAAA,CAAAU,CAAA,CAAAD,GAAA;QAAA;UAAAT,SAAA,CAAAW,CAAA;QAAA;QACA;MACA;MACA,OAAAb,WAAA,MAAAjG,QAAA;IACA;IACA;IACA+G,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,OAAA5F,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA2F,SAAA;QAAA,IAAA/D,GAAA;QAAA,OAAA7B,mBAAA,GAAAG,IAAA,UAAA0F,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxF,IAAA,GAAAwF,SAAA,CAAAvF,IAAA;YAAA;cACAoF,MAAA,CAAAvG,YAAA;cAAA0G,SAAA,CAAAxF,IAAA;cAAAwF,SAAA,CAAAvF,IAAA;cAAA,OAEApC,eAAA;gBACAc,SAAA,EAAA0G,MAAA,CAAAnH,SAAA;gBACAU,SAAA,EAAAyG,MAAA,CAAA3G,MAAA,CAAAE;cACA;YAAA;cAHA2C,GAAA,GAAAiE,SAAA,CAAA3D,IAAA;cAIA,IAAAN,GAAA,CAAAsC,SAAA;gBACAwB,MAAA,CAAAvB,QAAA,CAAAC,OAAA;gBACAsB,MAAA,CAAA/C,aAAA;cACA;gBACA+C,MAAA,CAAAvB,QAAA,CAAAtB,KAAA,CAAAjB,GAAA,CAAAyC,OAAA;cACA;cAAAwB,SAAA,CAAAvF,IAAA;cAAA;YAAA;cAAAuF,SAAA,CAAAxF,IAAA;cAAAwF,SAAA,CAAAjD,EAAA,GAAAiD,SAAA;cAEAH,MAAA,CAAAvB,QAAA,CAAAtB,KAAA;cACArB,OAAA,CAAAqB,KAAA,YAAAgD,SAAA,CAAAjD,EAAA;YAAA;cAAAiD,SAAA,CAAAxF,IAAA;cAEAqF,MAAA,CAAAvG,YAAA;cAAA,OAAA0G,SAAA,CAAAC,MAAA;YAAA;YAAA;cAAA,OAAAD,SAAA,CAAApF,IAAA;UAAA;QAAA,GAAAkF,QAAA;MAAA;IAEA;IACA;IACAI,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MAAA,OAAAlG,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAiG,SAAA;QAAA,IAAArE,GAAA;QAAA,OAAA7B,mBAAA,GAAAG,IAAA,UAAAgG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9F,IAAA,GAAA8F,SAAA,CAAA7F,IAAA;YAAA;cACA0F,MAAA,CAAA5G,WAAA;cAAA+G,SAAA,CAAA9F,IAAA;cAAA8F,SAAA,CAAA7F,IAAA;cAAA,OAEAnC,kBAAA;gBACAa,SAAA,EAAAgH,MAAA,CAAAzH,SAAA;gBACAU,SAAA,EAAA+G,MAAA,CAAAjH,MAAA,CAAAE;cACA;YAAA;cAHA2C,GAAA,GAAAuE,SAAA,CAAAjE,IAAA;cAIA,IAAAN,GAAA,CAAAsC,SAAA;gBACA8B,MAAA,CAAA7B,QAAA,CAAAC,OAAA;cACA;gBACA4B,MAAA,CAAA7B,QAAA,CAAAtB,KAAA,CAAAjB,GAAA,CAAAyC,OAAA;cACA;cAAA8B,SAAA,CAAA7F,IAAA;cAAA;YAAA;cAAA6F,SAAA,CAAA9F,IAAA;cAAA8F,SAAA,CAAAvD,EAAA,GAAAuD,SAAA;cAEAH,MAAA,CAAA7B,QAAA,CAAAtB,KAAA;cACArB,OAAA,CAAAqB,KAAA,eAAAsD,SAAA,CAAAvD,EAAA;YAAA;cAAAuD,SAAA,CAAA9F,IAAA;cAEA2F,MAAA,CAAA5G,WAAA;cAAA,OAAA+G,SAAA,CAAAL,MAAA;YAAA;YAAA;cAAA,OAAAK,SAAA,CAAA1F,IAAA;UAAA;QAAA,GAAAwF,QAAA;MAAA;IAEA;IACA;IACAG,cAAA,WAAAA,eAAAC,GAAA;MACA,KAAAhH,SAAA,GAAAgH,GAAA,CAAAC,IAAA;MACA;IACA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,UAAAjH,SAAA,MAAAD,SAAA;QACA,KAAAsE,IAAA,MAAArE,SAAA,OAAAD,SAAA;MACA;MACA,IAAAmH,OAAA;QACAvF,SAAA,EAAAwF,QAAA,MAAApH,SAAA;QACAqH,UAAA,OAAA3H,MAAA,CAAAE,SAAA;QACA0H,OAAA,OAAA5H,MAAA,CAAAG,MAAA;QACA0H,gBAAA;QACAC,cAAA;QACAC,mBAAA;MACA;MACA,KAAAxH,SAAA,MAAAD,SAAA,EAAA0H,IAAA,CAAAP,OAAA;IACA;IACA;IACAQ,cAAA,WAAAA,eAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACAL,MAAA,CAAA5H,SAAA,CAAA4H,MAAA,CAAA7H,SAAA,EAAAmI,MAAA,CAAAP,KAAA;QACAC,MAAA,CAAA/C,QAAA,CAAAC,OAAA;MACA,GAAAqD,KAAA;IACA;IACA;IACArG,YAAA,WAAAA,aAAA;MAAA,IAAAsG,MAAA;MAAA,OAAA5H,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA2H,SAAA;QAAA,IAAA/F,GAAA;QAAA,OAAA7B,mBAAA,GAAAG,IAAA,UAAA0H,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxH,IAAA,GAAAwH,SAAA,CAAAvH,IAAA;YAAA;cAAAuH,SAAA,CAAAxH,IAAA;cAAAwH,SAAA,CAAAvH,IAAA;cAAA,OAEAtC,cAAA;gBACAgB,SAAA,EAAA0I,MAAA,CAAAnJ,SAAA;gBACAU,SAAA,EAAAyI,MAAA,CAAA3I,MAAA,CAAAE,SAAA;gBACAC,MAAA,EAAAwI,MAAA,CAAA3I,MAAA,CAAAG,MAAA;gBACA+B,SAAA,EAAAwF,QAAA,CAAAiB,MAAA,CAAArI,SAAA;cACA;YAAA;cALAuC,GAAA,GAAAiG,SAAA,CAAA3F,IAAA;cAMA,IAAAN,GAAA,CAAAsC,SAAA;gBACAwD,MAAA,CAAA/D,IAAA,CAAA+D,MAAA,CAAApI,SAAA,EAAAoI,MAAA,CAAArI,SAAA,EAAAuC,GAAA,CAAAY,IAAA;cACA;cAAAqF,SAAA,CAAAvH,IAAA;cAAA;YAAA;cAAAuH,SAAA,CAAAxH,IAAA;cAAAwH,SAAA,CAAAjF,EAAA,GAAAiF,SAAA;cAEArG,OAAA,CAAAqB,KAAA,cAAAgF,SAAA,CAAAjF,EAAA;YAAA;YAAA;cAAA,OAAAiF,SAAA,CAAApH,IAAA;UAAA;QAAA,GAAAkH,QAAA;MAAA;IAEA;IACA;IACAG,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MAAA,OAAAjI,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAgI,SAAA;QAAA,OAAAjI,mBAAA,GAAAG,IAAA,UAAA+H,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7H,IAAA,GAAA6H,SAAA,CAAA5H,IAAA;YAAA;cAAA4H,SAAA,CAAA7H,IAAA;cAAA,MAGA0H,OAAA,CAAAtI,cAAA,IAAAsI,OAAA,CAAAzJ,QAAA,IAAAyJ,OAAA,CAAAzJ,QAAA,CAAA0C,MAAA;gBAAAkH,SAAA,CAAA5H,IAAA;gBAAA;cAAA;cAAA4H,SAAA,CAAA5H,IAAA;cAAA,OACAyH,OAAA,CAAAlE,UAAA;YAAA;cAAA,KAIAkE,OAAA,CAAArI,cAAA;gBAAAwI,SAAA,CAAA5H,IAAA;gBAAA;cAAA;cAAA4H,SAAA,CAAA5H,IAAA;cAAA,OACAyH,OAAA,CAAAI,cAAA;YAAA;cAGA;cACA,KAAAJ,OAAA,CAAAtI,cAAA,KAAAsI,OAAA,CAAArI,cAAA;gBACAqI,OAAA,CAAA5D,QAAA,CAAAiE,OAAA;cACA;cAAAF,SAAA,CAAA5H,IAAA;cAAA;YAAA;cAAA4H,SAAA,CAAA7H,IAAA;cAAA6H,SAAA,CAAAtF,EAAA,GAAAsF,SAAA;cAEA1G,OAAA,CAAAqB,KAAA,YAAAqF,SAAA,CAAAtF,EAAA;YAAA;YAAA;cAAA,OAAAsF,SAAA,CAAAzH,IAAA;UAAA;QAAA,GAAAuH,QAAA;MAAA;IAEA;IACA;IACAG,cAAA,WAAAA,eAAA;MAAA,IAAAE,OAAA;MAAA,OAAAvI,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAsI,SAAA;QAAA,IAAAC,KAAA,EAAAC,CAAA,EAAAC,IAAA,EAAA7G,GAAA;QAAA,OAAA7B,mBAAA,GAAAG,IAAA,UAAAwI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtI,IAAA,GAAAsI,SAAA,CAAArI,IAAA;YAAA;cACA;cACAiI,KAAA,GAAAF,OAAA,CAAA1I,gBAAA;cAAA,MACA4I,KAAA,CAAAvH,MAAA;gBAAA2H,SAAA,CAAArI,IAAA;gBAAA;cAAA;cACAkI,CAAA;YAAA;cAAA,MAAAA,CAAA,GAAAD,KAAA,CAAAvH,MAAA;gBAAA2H,SAAA,CAAArI,IAAA;gBAAA;cAAA;cACAmI,IAAA,GAAAF,KAAA,CAAAC,CAAA;cAAA,MACA,CAAAC,IAAA,CAAA7B,gBAAA,KAAA6B,IAAA,CAAA5B,cAAA,KAAA4B,IAAA,CAAA3B,mBAAA,IAAA2B,IAAA,CAAA3B,mBAAA,CAAA9F,MAAA;gBAAA2H,SAAA,CAAArI,IAAA;gBAAA;cAAA;cACA+H,OAAA,CAAAlE,QAAA,CAAAtB,KAAA,UAAAP,MAAA,CAAAkG,CAAA;cAAA,OAAAG,SAAA,CAAA/E,MAAA;YAAA;cAAA,MAIAgF,KAAA,CAAAH,IAAA,CAAA7B,gBAAA,KAAAgC,KAAA,CAAAH,IAAA,CAAA5B,cAAA;gBAAA8B,SAAA,CAAArI,IAAA;gBAAA;cAAA;cACA+H,OAAA,CAAAlE,QAAA,CAAAtB,KAAA,UAAAP,MAAA,CAAAkG,CAAA;cAAA,OAAAG,SAAA,CAAA/E,MAAA;YAAA;cARA4E,CAAA;cAAAG,SAAA,CAAArI,IAAA;cAAA;YAAA;cAAAqI,SAAA,CAAAtI,IAAA;cAAAsI,SAAA,CAAArI,IAAA;cAAA,OAeArC,eAAA;gBACAe,SAAA,EAAAqJ,OAAA,CAAA9J,SAAA;gBACAU,SAAA,EAAAoJ,OAAA,CAAAtJ,MAAA,CAAAE,SAAA;gBACAC,MAAA,EAAAmJ,OAAA,CAAAtJ,MAAA,CAAAG,MAAA;gBACA2J,KAAA,EAAAN;cACA;YAAA;cALA3G,GAAA,GAAA+G,SAAA,CAAAzG,IAAA;cAMA,IAAAN,GAAA,CAAAsC,SAAA;gBACAmE,OAAA,CAAAlE,QAAA,CAAAC,OAAA;gBACAiE,OAAA,CAAAjH,YAAA;cACA;gBACAiH,OAAA,CAAAlE,QAAA,CAAAtB,KAAA,CAAAjB,GAAA,CAAAyC,OAAA;cACA;cAAAsE,SAAA,CAAArI,IAAA;cAAA;YAAA;cAAAqI,SAAA,CAAAtI,IAAA;cAAAsI,SAAA,CAAA/F,EAAA,GAAA+F,SAAA;cAEAN,OAAA,CAAAlE,QAAA,CAAAtB,KAAA;cACArB,OAAA,CAAAqB,KAAA,cAAA8F,SAAA,CAAA/F,EAAA;YAAA;YAAA;cAAA,OAAA+F,SAAA,CAAAlI,IAAA;UAAA;QAAA,GAAA6H,QAAA;MAAA;IAEA;EACA;AACA", "ignoreList": []}]}
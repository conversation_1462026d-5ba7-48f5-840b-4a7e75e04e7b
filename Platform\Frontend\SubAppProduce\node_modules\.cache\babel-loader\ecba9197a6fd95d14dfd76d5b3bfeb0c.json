{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\com-config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\com-config\\index.vue", "mtime": 1757468112228}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetTableSettingList", "UpdateComponentPartTableSetting", "UpdateColumnSetting", "closeTagView", "GetBOMInfo", "name", "components", "data", "activeNameApi", "activeName", "currentCode", "typeCode", "materialCode", "currentFinalTypeCode", "tabPosition", "searchVal", "majorName", "comName", "unit", "steelUnit", "templateList", "templateListNew", "loading", "systemField", "expandField", "businessField", "computed", "tabList", "label", "value", "created", "$route", "query", "mounted", "steel_unit", "GetTableSettingListFn", "methods", "changeStatus", "$event", "id", "displayName", "find", "item", "Id", "Display_Name", "$message", "type", "message", "map", "Is_Enabled", "handleClick", "tab", "event", "searchValue", "_this", "filterList", "search", "RegExp", "push", "saveModifyChangesFn", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "sent", "IsSucceed", "Message", "stop", "_this3", "_callee2", "_callee2$", "_context2", "_this4", "_callee3", "_yield$GetBOMInfo", "_callee3$", "_context3", "IsComponent", "ProfessionalCode", "TypeCode", "Level", "Data", "length", "for<PERSON>ach", "v", "Code", "some", "Column_Type"], "sources": ["src/views/PRO/bom-setting/com-config/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"page-container\">\r\n      <div class=\"top-wrapper\">\r\n        <!-- <div class=\"title\">专业模板配置：</div> -->\r\n        <div class=\"info\">\r\n          <template v-if=\"!!majorName\">\r\n            <div class=\"title\">当前专业：</div>\r\n            <div class=\"value\">{{ majorName }}</div>\r\n          </template>\r\n          <template v-if=\"!!unit\">\r\n            <div class=\"title\">统计单位：</div>\r\n            <div class=\"value\">{{ unit }}</div>\r\n          </template>\r\n          <template v-if=\"!!steelUnit\">\r\n            <div class=\"title\">构件单位：</div>\r\n            <div class=\"value\">{{ steelUnit }}</div>\r\n          </template>\r\n          <template>\r\n            <div class=\"title\">单位统计字段：</div>\r\n            <div v-for=\"(item,index) in templateListNew\" v-show=\"item.Code==='SteelAmount'\" :key=\"index\" style=\"display: flex;flex-direction: row\">\r\n              {{ item.Display_Name }}\r\n            </div>\r\n            <div v-for=\"(item,index) in templateListNew\" v-show=\"item.Code==='SteelWeight'\" :key=\"index+999\" style=\"display: flex;flex-direction: row\">\r\n              *{{ item.Display_Name }}\r\n            </div>\r\n          </template>\r\n        </div>\r\n        <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\r\n          <el-tab-pane v-for=\"(item,index) in tabList\" :key=\"comName+index\" :label=\"item.label\" :name=\"item.value\" />\r\n\r\n        </el-tabs>\r\n      </div>\r\n      <div class=\"content-wrapper\" style=\"min-height: calc(100vh - 340px)\">\r\n        <div class=\"right-c\">\r\n          <el-row type=\"flex\" justify=\"space-between\">\r\n            <div class=\"right-c-title\">\r\n              <div class=\"setting-title\">{{ systemField==true ? '系统字段' : businessField==true ? '业务字段' : expandField==true ? '拓展字段' : '' }}</div>\r\n            </div>\r\n            <div style=\"display: flex;flex-direction: row\">\r\n              <span style=\"width:140px;font-size:12px;height:32px; line-height: 32px;display: inline-block;\">字段名称：</span>\r\n              <el-input v-model=\"searchVal\" placeholder=\"请输入字段名称\" clearable />\r\n              <el-button type=\"primary\" style=\"margin-left: 10px\" @click=\"searchValue\">查询</el-button>\r\n              <el-button type=\"primary\" :loading=\"loading\" @click=\"saveModifyChangesFn\">保存设置</el-button>\r\n            </div>\r\n          </el-row>\r\n          <el-form label-width=\"120px\" style=\"margin-top: 24px\">\r\n            <template v-for=\"(item,index) in templateListNew\">\r\n              <el-row v-if=\"item.Column_Type==0\" :key=\"index\">\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"item.Code\" label-width=\"150px\">\r\n                    <el-input v-model=\"item.Display_Name\" :disabled=\"false\" style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"'备注说明'\">\r\n                    <el-input v-model=\"item.Remark\" disabled style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"排序\">\r\n                    <el-input v-model=\"item.Sort\" style=\"width: 100px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"是否显示\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </template>\r\n            <div v-show=\"businessField==true && systemField==true\" class=\"setting-title\">业务字段</div>\r\n            <template v-for=\"(item,index) in templateListNew\">\r\n              <el-row v-if=\"item.Column_Type==2\" :key=\"index\">\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"item.Code\" label-width=\"150px\">\r\n                    <el-input v-model=\"item.Display_Name\" style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"'备注说明'\">\r\n                    <el-input v-model=\"item.Remark\" disabled style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"排序\">\r\n                    <el-input v-model=\"item.Sort\" style=\"width: 100px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName==activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"是否启用\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-else :span=\"4\">\r\n                  <el-form-item label=\"是否显示\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </template>\r\n            <div v-show=\"expandField==true\" class=\"setting-title\">拓展字段</div>\r\n            <template v-for=\"(item,index) in templateListNew\">\r\n              <el-row v-if=\"item.Column_Type==1\" :key=\"index\">\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"item.Code\" label-width=\"150px\">\r\n                    <el-input v-model=\"item.Display_Name\" style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"'备注说明'\">\r\n                    <el-input v-model=\"item.Remark\" disabled style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"排序\">\r\n                    <el-input v-model=\"item.Sort\" style=\"width: 100px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName==activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"是否启用\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-else :span=\"4\">\r\n                  <el-form-item label=\"是否显示\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </template>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { GetTableSettingList, UpdateComponentPartTableSetting, UpdateColumnSetting } from '@/api/PRO/component-type'\r\nimport { closeTagView } from '@/utils'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\nexport default {\r\n  name: 'PROComponentConfig',\r\n  components: {\r\n  },\r\n  data() {\r\n    return {\r\n      activeNameApi: 'plm_component_field_page_list',\r\n      activeName: 'plm_component_field_page_list',\r\n      currentCode: 'plm_component_page_list',\r\n      typeCode: '',\r\n      materialCode: '',\r\n      currentFinalTypeCode: '',\r\n      tabPosition: 'left',\r\n      // tabList: [\r\n      //   {\r\n      //     label: '构件字段维护',\r\n      //     value: 'plm_component_field_page_list'\r\n      //   },\r\n      //   {\r\n      //     label: '构件管理列表',\r\n      //     value: 'plm_component_page_list'\r\n      //   },\r\n      //   {\r\n      //     label: '构件深化清单',\r\n      //     value: 'plm_component_detailImport'\r\n      //   },\r\n      //   {\r\n      //     label: '构件模型清单',\r\n      //     value: 'plm_component_modelImport'\r\n      //   },\r\n      //   {\r\n      //     label: '生产详情列表',\r\n      //     value: 'plm_component_produceDetail'\r\n      //   },\r\n      //   {\r\n      //     label: '打包模板',\r\n      //     value: 'plm_component_packageTemplate'\r\n      //   },\r\n      //   {\r\n      //     label: '模型字段对照表',\r\n      //     value: 'plm_component_modelField'\r\n      //   }\r\n      // ],\r\n      searchVal: '',\r\n      majorName: '',\r\n      comName: '构件',\r\n      unit: '',\r\n      steelUnit: '',\r\n      templateList: [],\r\n      templateListNew: [],\r\n      loading: false,\r\n      systemField: false,\r\n      expandField: false,\r\n      businessField: false\r\n    }\r\n  },\r\n  computed: {\r\n    tabList() {\r\n      return [\r\n        {\r\n          label: this.comName + '字段维护',\r\n          value: 'plm_component_field_page_list'\r\n        },\r\n        {\r\n          label: this.comName + '管理列表',\r\n          value: 'plm_component_page_list'\r\n        },\r\n        // {\r\n        //   label: this.comName + '深化清单',\r\n        //   value: 'plm_component_detailImport'\r\n        // },\r\n        // {\r\n        //   label: this.comName + '模型清单',\r\n        //   value: 'plm_component_modelImport'\r\n        // },\r\n        {\r\n          label: '生产详情列表',\r\n          value: 'plm_component_produceDetail'\r\n        },\r\n        {\r\n          label: '打包模板',\r\n          value: 'plm_component_packageTemplate'\r\n        }\r\n        // {\r\n        //   label: '模型字段对照表',\r\n        //   value: 'plm_component_modelField'\r\n        // }\r\n      ]\r\n    }\r\n  },\r\n  created() {\r\n    this.typeCode = this.$route.query.typeCode || 'Steel'\r\n    this.materialCode = this.$route.query.materialCode || 'StructuralAs'\r\n    this.currentFinalTypeCode = this.typeCode\r\n  },\r\n  mounted() {\r\n    this.majorName = this.$route.query.name || '钢结构'\r\n    this.unit = this.$route.query.unit || 't'\r\n    this.steelUnit = this.$route.query.steel_unit || 'kg'\r\n    this.GetTableSettingListFn()\r\n  },\r\n  methods: {\r\n    changeStatus($event, id) {\r\n      const displayName = this.templateList.find((item) => { return item.Id == id }).Display_Name\r\n      if (displayName == '' && $event == true) {\r\n        this.$message({ type: 'error', message: '请先填写字段名' })\r\n        this.templateList.map((item) => {\r\n          if (item.Id == id) {\r\n            item.Is_Enabled = false\r\n          }\r\n          return item\r\n        })\r\n      }\r\n    },\r\n    handleClick(tab, event) {\r\n      this.currentCode = tab.name\r\n      this.GetTableSettingListFn()\r\n    },\r\n\r\n    searchValue() {\r\n      if (!this.searchVal) {\r\n        this.templateListNew = this.templateList\r\n      } else {\r\n        const filterList = []\r\n        this.templateList.map(item => {\r\n          if (item.Display_Name.search(new RegExp(this.searchVal, 'ig')) !== -1) {\r\n            filterList.push(item)\r\n          }\r\n        })\r\n        this.templateListNew = filterList\r\n      }\r\n    },\r\n\r\n    saveModifyChangesFn() {\r\n      if (this.activeName == this.activeNameApi) {\r\n        this.UpdateComponentPartTableSetting()\r\n      } else {\r\n        this.UpdateColumnSetting()\r\n      }\r\n    },\r\n\r\n    async UpdateColumnSetting() {\r\n      this.loading = true\r\n      const res = await UpdateColumnSetting(this.templateList)\r\n      this.loading = false\r\n      if (res.IsSucceed) {\r\n        this.$message({ type: 'success', message: '保存成功' })\r\n      } else {\r\n        this.$message({ type: 'error', message: res.Message })\r\n      }\r\n    },\r\n\r\n    async UpdateComponentPartTableSetting() {\r\n      this.loading = true\r\n      const res = await UpdateComponentPartTableSetting(this.templateList)\r\n      this.loading = false\r\n      if (res.IsSucceed) {\r\n        this.$message({ type: 'success', message: '保存成功' })\r\n      } else {\r\n        this.$message({ type: 'error', message: res.Message })\r\n      }\r\n    },\r\n\r\n    async GetTableSettingListFn() {\r\n      let data = {}\r\n      if (this.activeName == this.activeNameApi) {\r\n        data = { IsComponent: true, ProfessionalCode: this.currentFinalTypeCode }\r\n      } else {\r\n        data = { IsComponent: true, ProfessionalCode: this.currentFinalTypeCode, TypeCode: this.currentCode + ',' + this.currentFinalTypeCode }\r\n      }\r\n      data.Level = -1\r\n      const { comName } = await GetBOMInfo()\r\n      this.comName = comName\r\n      const res = await GetTableSettingList(data)\r\n      if (res.IsSucceed) {\r\n        this.templateList = res.Data || []\r\n        if (this.templateList.length > 0) {\r\n          this.templateList.forEach(v => {\r\n            if (v.Code === 'SteelName') {\r\n              v.Display_Name = comName\r\n            }\r\n          })\r\n        }\r\n        this.templateListNew = this.templateList\r\n        this.systemField = this.templateList.some(item => { return item.Column_Type == 0 })\r\n        this.expandField = this.templateList.some(item => { return item.Column_Type == 1 })\r\n        this.businessField = this.templateList.some(item => { return item.Column_Type == 2 })\r\n      } else {\r\n        this.$message({ type: 'error', message: res.Message })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n  .page-container{\r\n    margin:16px;\r\n    box-sizing: border-box;\r\n    .top-wrapper{\r\n      background: #fff;\r\n      padding:16px;\r\n      box-sizing: border-box;\r\n      .title{\r\n        font-size: 16px;\r\n        font-weight: 500;\r\n        color:#333333;\r\n      }\r\n      .info{\r\n        font-size: 14px;\r\n        margin:8px 0 24px 0;\r\n        display: flex;\r\n        flex-direction: row;\r\n        .title{\r\n          font-size: 14px;\r\n          color: #999999;\r\n        }\r\n        .value{\r\n          color: #333333;\r\n          margin-right: 24px;\r\n        }\r\n      }\r\n    }\r\n    .content-wrapper{\r\n      margin-top:16px;\r\n      display: flex;\r\n      flex-direction: row;\r\n      .left-c{\r\n        width: 160px;\r\n        background: #fff;\r\n        margin-right: 16px;\r\n      }\r\n      .right-c{\r\n        background: #fff;\r\n        width: 100%;\r\n        padding: 16px 24px;\r\n        box-sizing: border-box;\r\n        .right-c-title .setting-title {\r\n          margin: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  // ::v-deep.el-tabs--left .el-tabs__nav-wrap.is-left::after{\r\n  //   left:0\r\n  // }\r\n  // ::v-deep.el-tabs--left .el-tabs__active-bar.is-left{\r\n  //   left: 0;\r\n  // }\r\n  .setting-title {\r\n    font-weight: 400;\r\n    color: #1f2f3d;\r\n    margin: 30px 0 20px;\r\n    font-size: 22px;\r\n  }\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkKA,SAAAA,mBAAA,EAAAC,+BAAA,IAAAA,gCAAA,EAAAC,mBAAA,IAAAA,oBAAA;AACA,SAAAC,YAAA;AACA,SAAAC,UAAA;AACA;EACAC,IAAA;EACAC,UAAA,GACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,UAAA;MACAC,WAAA;MACAC,QAAA;MACAC,YAAA;MACAC,oBAAA;MACAC,WAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC,SAAA;MACAC,SAAA;MACAC,OAAA;MACAC,IAAA;MACAC,SAAA;MACAC,YAAA;MACAC,eAAA;MACAC,OAAA;MACAC,WAAA;MACAC,WAAA;MACAC,aAAA;IACA;EACA;EACAC,QAAA;IACAC,OAAA,WAAAA,QAAA;MACA,QACA;QACAC,KAAA,OAAAX,OAAA;QACAY,KAAA;MACA,GACA;QACAD,KAAA,OAAAX,OAAA;QACAY,KAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA;MACA;MACA;MACA;MACA;MAAA,CACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAnB,QAAA,QAAAoB,MAAA,CAAAC,KAAA,CAAArB,QAAA;IACA,KAAAC,YAAA,QAAAmB,MAAA,CAAAC,KAAA,CAAApB,YAAA;IACA,KAAAC,oBAAA,QAAAF,QAAA;EACA;EACAsB,OAAA,WAAAA,QAAA;IACA,KAAAjB,SAAA,QAAAe,MAAA,CAAAC,KAAA,CAAA3B,IAAA;IACA,KAAAa,IAAA,QAAAa,MAAA,CAAAC,KAAA,CAAAd,IAAA;IACA,KAAAC,SAAA,QAAAY,MAAA,CAAAC,KAAA,CAAAE,UAAA;IACA,KAAAC,qBAAA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAAC,MAAA,EAAAC,EAAA;MACA,IAAAC,WAAA,QAAApB,YAAA,CAAAqB,IAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,EAAA,IAAAJ,EAAA;MAAA,GAAAK,YAAA;MACA,IAAAJ,WAAA,UAAAF,MAAA;QACA,KAAAO,QAAA;UAAAC,IAAA;UAAAC,OAAA;QAAA;QACA,KAAA3B,YAAA,CAAA4B,GAAA,WAAAN,IAAA;UACA,IAAAA,IAAA,CAAAC,EAAA,IAAAJ,EAAA;YACAG,IAAA,CAAAO,UAAA;UACA;UACA,OAAAP,IAAA;QACA;MACA;IACA;IACAQ,WAAA,WAAAA,YAAAC,GAAA,EAAAC,KAAA;MACA,KAAA1C,WAAA,GAAAyC,GAAA,CAAA9C,IAAA;MACA,KAAA8B,qBAAA;IACA;IAEAkB,WAAA,WAAAA,YAAA;MAAA,IAAAC,KAAA;MACA,UAAAvC,SAAA;QACA,KAAAM,eAAA,QAAAD,YAAA;MACA;QACA,IAAAmC,UAAA;QACA,KAAAnC,YAAA,CAAA4B,GAAA,WAAAN,IAAA;UACA,IAAAA,IAAA,CAAAE,YAAA,CAAAY,MAAA,KAAAC,MAAA,CAAAH,KAAA,CAAAvC,SAAA;YACAwC,UAAA,CAAAG,IAAA,CAAAhB,IAAA;UACA;QACA;QACA,KAAArB,eAAA,GAAAkC,UAAA;MACA;IACA;IAEAI,mBAAA,WAAAA,oBAAA;MACA,SAAAlD,UAAA,SAAAD,aAAA;QACA,KAAAP,+BAAA;MACA;QACA,KAAAC,mBAAA;MACA;IACA;IAEAA,mBAAA,WAAAA,oBAAA;MAAA,IAAA0D,MAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAV,MAAA,CAAAtC,OAAA;cAAA8C,QAAA,CAAAE,IAAA;cAAA,OACApE,oBAAA,CAAA0D,MAAA,CAAAxC,YAAA;YAAA;cAAA6C,GAAA,GAAAG,QAAA,CAAAG,IAAA;cACAX,MAAA,CAAAtC,OAAA;cACA,IAAA2C,GAAA,CAAAO,SAAA;gBACAZ,MAAA,CAAAf,QAAA;kBAAAC,IAAA;kBAAAC,OAAA;gBAAA;cACA;gBACAa,MAAA,CAAAf,QAAA;kBAAAC,IAAA;kBAAAC,OAAA,EAAAkB,GAAA,CAAAQ;gBAAA;cACA;YAAA;YAAA;cAAA,OAAAL,QAAA,CAAAM,IAAA;UAAA;QAAA,GAAAV,OAAA;MAAA;IACA;IAEA/D,+BAAA,WAAAA,gCAAA;MAAA,IAAA0E,MAAA;MAAA,OAAAd,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAa,SAAA;QAAA,IAAAX,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAW,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAT,IAAA,GAAAS,SAAA,CAAAR,IAAA;YAAA;cACAK,MAAA,CAAArD,OAAA;cAAAwD,SAAA,CAAAR,IAAA;cAAA,OACArE,gCAAA,CAAA0E,MAAA,CAAAvD,YAAA;YAAA;cAAA6C,GAAA,GAAAa,SAAA,CAAAP,IAAA;cACAI,MAAA,CAAArD,OAAA;cACA,IAAA2C,GAAA,CAAAO,SAAA;gBACAG,MAAA,CAAA9B,QAAA;kBAAAC,IAAA;kBAAAC,OAAA;gBAAA;cACA;gBACA4B,MAAA,CAAA9B,QAAA;kBAAAC,IAAA;kBAAAC,OAAA,EAAAkB,GAAA,CAAAQ;gBAAA;cACA;YAAA;YAAA;cAAA,OAAAK,SAAA,CAAAJ,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IACA;IAEAzC,qBAAA,WAAAA,sBAAA;MAAA,IAAA4C,MAAA;MAAA,OAAAlB,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAiB,SAAA;QAAA,IAAAzE,IAAA,EAAA0E,iBAAA,EAAAhE,OAAA,EAAAgD,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAgB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAd,IAAA,GAAAc,SAAA,CAAAb,IAAA;YAAA;cACA/D,IAAA;cACA,IAAAwE,MAAA,CAAAtE,UAAA,IAAAsE,MAAA,CAAAvE,aAAA;gBACAD,IAAA;kBAAA6E,WAAA;kBAAAC,gBAAA,EAAAN,MAAA,CAAAlE;gBAAA;cACA;gBACAN,IAAA;kBAAA6E,WAAA;kBAAAC,gBAAA,EAAAN,MAAA,CAAAlE,oBAAA;kBAAAyE,QAAA,EAAAP,MAAA,CAAArE,WAAA,SAAAqE,MAAA,CAAAlE;gBAAA;cACA;cACAN,IAAA,CAAAgF,KAAA;cAAAJ,SAAA,CAAAb,IAAA;cAAA,OACAlE,UAAA;YAAA;cAAA6E,iBAAA,GAAAE,SAAA,CAAAZ,IAAA;cAAAtD,OAAA,GAAAgE,iBAAA,CAAAhE,OAAA;cACA8D,MAAA,CAAA9D,OAAA,GAAAA,OAAA;cAAAkE,SAAA,CAAAb,IAAA;cAAA,OACAtE,mBAAA,CAAAO,IAAA;YAAA;cAAA0D,GAAA,GAAAkB,SAAA,CAAAZ,IAAA;cACA,IAAAN,GAAA,CAAAO,SAAA;gBACAO,MAAA,CAAA3D,YAAA,GAAA6C,GAAA,CAAAuB,IAAA;gBACA,IAAAT,MAAA,CAAA3D,YAAA,CAAAqE,MAAA;kBACAV,MAAA,CAAA3D,YAAA,CAAAsE,OAAA,WAAAC,CAAA;oBACA,IAAAA,CAAA,CAAAC,IAAA;sBACAD,CAAA,CAAA/C,YAAA,GAAA3B,OAAA;oBACA;kBACA;gBACA;gBACA8D,MAAA,CAAA1D,eAAA,GAAA0D,MAAA,CAAA3D,YAAA;gBACA2D,MAAA,CAAAxD,WAAA,GAAAwD,MAAA,CAAA3D,YAAA,CAAAyE,IAAA,WAAAnD,IAAA;kBAAA,OAAAA,IAAA,CAAAoD,WAAA;gBAAA;gBACAf,MAAA,CAAAvD,WAAA,GAAAuD,MAAA,CAAA3D,YAAA,CAAAyE,IAAA,WAAAnD,IAAA;kBAAA,OAAAA,IAAA,CAAAoD,WAAA;gBAAA;gBACAf,MAAA,CAAAtD,aAAA,GAAAsD,MAAA,CAAA3D,YAAA,CAAAyE,IAAA,WAAAnD,IAAA;kBAAA,OAAAA,IAAA,CAAAoD,WAAA;gBAAA;cACA;gBACAf,MAAA,CAAAlC,QAAA;kBAAAC,IAAA;kBAAAC,OAAA,EAAAkB,GAAA,CAAAQ;gBAAA;cACA;YAAA;YAAA;cAAA,OAAAU,SAAA,CAAAT,IAAA;UAAA;QAAA,GAAAM,QAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}
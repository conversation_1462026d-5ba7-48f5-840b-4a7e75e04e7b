{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\ToleranceDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\ToleranceDialog.vue", "mtime": 1757468112827}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ToleranceDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAwBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ToleranceDialog.vue", "sourceRoot": "src/views/PRO/project-config/project-quality/components/Dialog", "sourcesContent": ["<template>\r\n  <el-form ref=\"formRef\" :model=\"form\" label-width=\"120px\" :rules=\"rules\">\r\n    <el-form-item label=\"长度\" prop=\"Length\">\r\n      <div class=\"cs-flex\">\r\n        <el-select v-model=\"form.Type\" style=\"margin-right: 10px;width: 100px;\" placeholder=\"请选择\">\r\n          <el-option v-for=\"item in typeOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n        </el-select>\r\n        <el-input-number v-model.number=\"form.Length\" :min=\"0\" class=\"cs-number-btn-hidden w100\" clearble />\r\n      </div>\r\n\r\n    </el-form-item>\r\n    <el-form-item label=\"公差要求\" prop=\"Demand\">\r\n      <el-input v-model=\"form.Demand\" placeholder=\"请输入公差要求\" />\r\n    </el-form-item>\r\n    <el-form-item style=\"text-align: right\">\r\n      <el-button @click=\"handleClose\">取消</el-button>\r\n      <el-button type=\"primary\" :loading=\"loading\" @click=\"submitForm\">确定</el-button>\r\n    </el-form-item>\r\n\r\n  </el-form>\r\n\r\n</template>\r\n\r\n<script>\r\nimport { SaveToleranceSetting } from '@/api/PRO/qualityInspect/quality-management'\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      typeOptions: [\r\n        { label: '<', value: 1 },\r\n        { label: '<=', value: 2 },\r\n        { label: '>', value: 3 },\r\n        { label: '>=', value: 4 },\r\n        { label: '=', value: 5 }\r\n      ],\r\n      form: {\r\n        Length: '',\r\n        Demand: '',\r\n        Type: 1\r\n      },\r\n      rules: {\r\n        Length: [{ required: true, message: '请选择长度' }],\r\n        Demand: [{ required: true, message: '请输入公差要求' }]\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    init(type, typeId, data) {\r\n      if (type === '编辑' && data) {\r\n        this.form.Id = data.Id\r\n        this.form.Length = data.Length\r\n        this.form.Demand = data.Demand\r\n        this.form.Type = data.Type\r\n      }\r\n    },\r\n    handleClose() {\r\n      this.$emit('close')\r\n    },\r\n    submitForm() {\r\n      this.$refs.formRef.validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true\r\n\r\n          SaveToleranceSetting(this.form).then(res => {\r\n            if (res.IsSucceed) {\r\n              this.$message.success('保存成功')\r\n              this.$emit('ToleranceRefresh')\r\n              this.handleClose()\r\n            } else {\r\n              this.$message.error(res.Message)\r\n            }\r\n          }).finally(() => {\r\n            this.loading = false\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.cs-flex {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n</style>\r\n"]}]}
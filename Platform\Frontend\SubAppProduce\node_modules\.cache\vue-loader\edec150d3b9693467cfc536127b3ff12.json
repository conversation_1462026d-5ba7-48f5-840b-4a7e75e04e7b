{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\ProjectAddDialog.vue?vue&type=style&index=0&id=4cc3daf3&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\ProjectAddDialog.vue", "mtime": 1757926768438}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouZm9ybS13cmFwcGVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICBtYXgtaGVpZ2h0OiA3MHZoOwoKICAuYnRuLXggewogICAgcGFkZGluZy10b3A6IDE2cHg7CiAgICB0ZXh0LWFsaWduOiByaWdodDsKICB9CiAgLmluc3RydWN0aW9uIHsKICBiYWNrZ3JvdW5kOiAjZjBmOWZmOwogIGJvcmRlcjogMXB4IHNvbGlkICNiM2Q4ZmY7CiAgY29sb3I6ICMxODkwZmY7CiAgcGFkZGluZzogMTJweCAxNnB4OwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBtYXJnaW4tYm90dG9tOiAxNnB4OwogIGZvbnQtd2VpZ2h0OiA1MDA7Cn0KfQo="}, {"version": 3, "sources": ["ProjectAddDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0EA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ProjectAddDialog.vue", "sourceRoot": "src/views/PRO/project-config/process-settings/component", "sourcesContent": ["<template>\r\n  <div class=\"form-wrapper\">\r\n    <div class=\"instruction\">请选择项目，添加所选项目的<span>所有工序</span></div>\r\n    <div>\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"82px\">\r\n        <el-form-item v-if=\"!projectList.length\" label=\"项目名称：\">\r\n          <div>暂无可同步的项目</div>\r\n        </el-form-item>\r\n        <el-form-item v-else label=\"项目名称：\">\r\n          <el-select v-model=\"form.From_Sys_Project_Id\" placeholder=\"请选择项目\" style=\"width: 300px\">\r\n            <el-option v-for=\"(item, index) in projectList\" :key=\"index\" :label=\"item.Short_Name\" :value=\"item.Sys_Project_Id\" :disabled=\"item.Sys_Project_Id===sysProjectId\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n    <div class=\"btn-x\">\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button v-if=\"projectList.length\" type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetProcessOfProjectList, SyncProjectProcessFromProject } from '@/api/PRO/technology-lib'\r\nexport default {\r\n  components: {\r\n  },\r\n  props: {\r\n    sysProjectId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      projectList: [],\r\n      form: {\r\n        From_Sys_Project_Id: ''\r\n      }\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.getProcessOfProjectList()\r\n  },\r\n  methods: {\r\n    async getProcessOfProjectList() {\r\n      const res = await GetProcessOfProjectList({ })\r\n      if (res.IsSucceed) {\r\n        this.projectList = res.Data\r\n      }\r\n    },\r\n    handleSubmit() {\r\n      if (this.form.From_Sys_Project_Id === '') return this.$message.warning('请选择项目')\r\n      this.btnLoading = true\r\n      SyncProjectProcessFromProject({\r\n        From_Sys_Project_Id: this.form.From_Sys_Project_Id,\r\n        To_Sys_Project_Id: this.sysProjectId\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$emit('refresh')\r\n          this.$emit('close')\r\n          this.$message.success('同步成功！')\r\n        } else {\r\n          this.$message.error(res.msg)\r\n        }\r\n        this.btnLoading = false\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n  .form-wrapper {\r\n    display: flex;\r\n    flex-direction: column;\r\n    overflow: hidden;\r\n    max-height: 70vh;\r\n\r\n    .btn-x {\r\n      padding-top: 16px;\r\n      text-align: right;\r\n    }\r\n    .instruction {\r\n    background: #f0f9ff;\r\n    border: 1px solid #b3d8ff;\r\n    color: #1890ff;\r\n    padding: 12px 16px;\r\n    border-radius: 4px;\r\n    margin-bottom: 16px;\r\n    font-weight: 500;\r\n  }\r\n  }\r\n</style>\r\n"]}]}
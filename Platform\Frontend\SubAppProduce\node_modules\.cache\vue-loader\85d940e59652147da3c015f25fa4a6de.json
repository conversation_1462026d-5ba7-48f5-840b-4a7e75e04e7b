{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\Add.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\Add.vue", "mtime": 1757468113389}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRCT01JbmZvIH0gZnJvbSAnQC92aWV3cy9QUk8vYm9tLXNldHRpbmcvdXRpbHMnDQppbXBvcnQgeyBHZXRVc2VyTGlzdCB9IGZyb20gJ0AvYXBpL3N5cycNCmltcG9ydCB7DQogIEFkZFdvcmtpbmdQcm9jZXNzLA0KICBHZXRGYWN0b3J5UGVvcGxlbGlzdCwNCiAgR2V0Q2hlY2tHcm91cExpc3QsDQogIEdldFdvcmtpbmdUZWFtcw0KfSBmcm9tICdAL2FwaS9QUk8vdGVjaG5vbG9neS1saWInDQppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCcNCmltcG9ydCB7IEdldERpY3Rpb25hcnlEZXRhaWxMaXN0QnlDb2RlIH0gZnJvbSAnQC9hcGkvc3lzJw0KZXhwb3J0IGRlZmF1bHQgew0KICBwcm9wczogew0KICAgIHR5cGU6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICcnDQogICAgfSwNCiAgICByb3dJbmZvOiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgICBkZWZhdWx0KCkgew0KICAgICAgICByZXR1cm4ge30NCiAgICAgIH0NCiAgICB9LA0KICAgIGJvbUxpc3Q6IHsNCiAgICAgIHR5cGU6IEFycmF5LA0KICAgICAgZGVmYXVsdDogKCkgPT4gW10NCiAgICB9DQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGNoZWNrTGlzdDogW10sDQogICAgICByYWRpb0xpc3Q6IFtdLA0KICAgICAgYnRuTG9hZGluZzogZmFsc2UsDQogICAgICBoaWRkZW5QYXJ0OiBmYWxzZSwNCg0KICAgICAgZm9ybTogew0KICAgICAgICBDb2RlOiAnJywNCiAgICAgICAgTmFtZTogJycsDQogICAgICAgIEJvbV9MZXZlbDogJycsDQogICAgICAgIE1vbnRoX0F2Z19Mb2FkOiAnJywNCiAgICAgICAgQ29vcmRpbmF0ZV9Vc2VySWQ6ICcnLA0KICAgICAgICBTb3J0OiB1bmRlZmluZWQsDQogICAgICAgIElzX0VuYWJsZTogdHJ1ZSwNCiAgICAgICAgSXNfRXh0ZXJuYWw6IGZhbHNlLA0KICAgICAgICBJc19OZXN0OiBmYWxzZSwNCiAgICAgICAgSXNfTmVlZF9DaGVjazogdHJ1ZSwNCiAgICAgICAgSXNfSW50ZXJfQ2hlY2s6IHRydWUsDQogICAgICAgIElzX05lZWRfVEM6IHRydWUsDQogICAgICAgIElzX1dlbGRpbmdfQXNzZW1ibGluZzogZmFsc2UsDQogICAgICAgIElzX0N1dHRpbmc6IGZhbHNlLA0KICAgICAgICBUQ19DaGVja19Vc2VySWQ6ICcnLA0KICAgICAgICBJc19OZWVkX1pMOiBmYWxzZSwNCiAgICAgICAgWkxfQ2hlY2tfVXNlcklkOiAnJywNCg0KICAgICAgICBDaGVja19TdHlsZTogJzAnLA0KDQogICAgICAgIFdvcmtpbmdfVGVhbV9JZHM6IFtdLA0KICAgICAgICBSZW1hcms6ICcnDQogICAgICB9LA0KICAgICAgWkxfQ2hlY2tfVXNlcklkczogW10sDQogICAgICBUQ19DaGVja19Vc2VySWRzOiBbXSwNCiAgICAgIENoZWNrQ2hhbmdlOiB0cnVlLA0KICAgICAgdXNlck9wdGlvbnM6IFtdLA0KICAgICAgb3B0aW9uc1VzZXJMaXN0OiBbXSwNCiAgICAgIG9wdGlvbnNHcm91cExpc3Q6IFtdLA0KICAgICAgb3B0aW9uc1dvcmtpbmdUZWFtc0xpc3Q6IFtdLA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgQ29kZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXku6Plj7cnLCB0cmlnZ2VyOiAnYmx1cicgfSwNCiAgICAgICAgICB7IG1heDogMzAsIG1lc3NhZ2U6ICfplb/luqblnKggMzAg5Liq5a2X56ym5YaFJywgdHJpZ2dlcjogJ2JsdXInIH0NCiAgICAgICAgXSwNCiAgICAgICAgTmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXlkI3np7AnLCB0cmlnZ2VyOiAnYmx1cicgfSwNCiAgICAgICAgICB7IG1heDogMzAsIG1lc3NhZ2U6ICfplb/luqblnKggMzAg5Liq5a2X56ym5YaFJywgdHJpZ2dlcjogJ2JsdXInIH0NCiAgICAgICAgXSwNCiAgICAgICAgQm9tX0xldmVsOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeexu+WeiycsIHRyaWdnZXI6ICdjaGFuZ2UnIH1dLA0KICAgICAgICBTb3J0OiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpScsIHRyaWdnZXI6ICdibHVyJyB9XSwNCiAgICAgICAgSXNfTmVlZF9DaGVjazogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nmmK/lkKbotKjmo4AnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9DQogICAgICAgIF0NCiAgICAgIH0NCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgLi4ubWFwR2V0dGVycygndGVuYW50JywgWydpc1ZlcnNpb25Gb3VyJ10pDQoNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmdldFVzZXJMaXN0KCkNCiAgICB0aGlzLmdldEZhY3RvcnlQZW9wbGVsaXN0KCkNCiAgICAvLyB0aGlzLmdldENoZWNrR3JvdXBMaXN0KCk7DQogICAgLy8gdGhpcy5TZWxlY3Rpb25jaGVja0NvbWJpbmF0aW9uKCkNCiAgICB0aGlzLmdldFdvcmtpbmdUZWFtc0xpc3QoKQ0KICAgIGNvbnNvbGUubG9nKCd0eXBlJywgdGhpcy5yb3dJbmZvKQ0KICAgIHRoaXMudHlwZSA9PT0gJ2VkaXQnICYmIHRoaXMuaW5pdEZvcm0oKQ0KICAgIHRoaXMuZ2V0Qk9NSW5mbygpDQogIH0sDQogIG1ldGhvZHM6IHsNCg0KICAgIHJhZGlvSW50ZXJDaGFuZ2UodmFsKSB7IH0sDQogICAgZ2V0Qk9NSW5mbygpIHsNCiAgICAgIHRoaXMucmFkaW9MaXN0ID0gdGhpcy5ib21MaXN0DQogICAgfSwNCiAgICAvLyDojrflj5borr7lpIfnsbvlnosNCiAgICBhc3luYyBnZXREaWN0aW9uYXJ5RGV0YWlsTGlzdEJ5Q29kZSgpIHsNCiAgICAgIGF3YWl0IEdldERpY3Rpb25hcnlEZXRhaWxMaXN0QnlDb2RlKHsgZGljdGlvbmFyeUNvZGU6ICdkZXZpY2VUeXBlJyB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLmRldmljZVR5cGVMaXN0ID0gcmVzLkRhdGENCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICBjb25zb2xlLmxvZygnIHRoaXMub3B0aW9uc0dyb3VwTGlzdCcsIHRoaXMub3B0aW9uc0dyb3VwTGlzdCkNCiAgICB9LA0KDQogICAgaW5pdEZvcm0oKSB7DQogICAgICB0aGlzLmdldEJPTUluZm8oKQ0KICAgICAgY29uc3QgeyBJc19OZXN0LCAuLi5vdGhlcnMgfSA9IHRoaXMucm93SW5mbw0KICAgICAgdGhpcy5mb3JtID0gT2JqZWN0LmFzc2lnbih7fSwgb3RoZXJzLCB7IElzX05lc3Q6ICEhSXNfTmVzdCB9KQ0KICAgICAgY29uc29sZS5sb2coJ290aGVycycsIG90aGVycykNCiAgICAgIHRoaXMuZm9ybS5Cb21fTGV2ZWwgPSBTdHJpbmcodGhpcy5mb3JtLkJvbV9MZXZlbCkNCiAgICAgIC8vICBpZih0aGlzLmZvcm0uVHlwZT09Mil7DQogICAgICAvLyAgIHRoaXMuZm9ybS5UeXBlcyA9ICcwJw0KICAgICAgLy8gIH1lbHNlIGlmKHRoaXMuZm9ybS5UeXBlPT0zKXsNCiAgICAgIC8vICAgbGV0IFR5cGVzID0gdGhpcy5yYWRpb0xpc3QuZmluZCh2ID0+IFsnMScsICcyJywnMyddLmluY2x1ZGVzKHYuQ29kZSkpPy5Db2RlDQogICAgICAvLyAgIGNvbnNvbGUubG9nKCdUeXBlcycsIFR5cGVzKQ0KICAgICAgLy8gICBjb25zb2xlLmxvZygndGhpcy5yYWRpb0xpc3QnLCB0aGlzLnJhZGlvTGlzdCkNCiAgICAgIC8vICAgdGhpcy5mb3JtLlR5cGVzID0gVHlwZXMNCiAgICAgIC8vICB9ZWxzZSBpZih0aGlzLmZvcm0uVHlwZT09MSl7DQogICAgICAvLyAgIHRoaXMuZm9ybS5UeXBlcyA9ICctMScNCiAgICAgIC8vICB9DQogICAgICBjb25zb2xlLmxvZygndGhpcy5mb3JtJywgdGhpcy5mb3JtKQ0KDQogICAgICAvLyDlpITnkIbljoblj7LmlbDmja7lpJrpgInpl67popgNCiAgICAgIC8vIGlmICh0aGlzLmZvcm0uSXNfTmVlZF9DaGVjaykgew0KICAgICAgLy8gICBpZiAodGhpcy5mb3JtLkNoZWNrX1N0eWxlID09PSAnMScpIHsNCg0KICAgICAgLy8gICB9IGVsc2Ugew0KICAgICAgLy8gICAgIHRoaXMuQ2hlY2tDaGFuZ2UgPSAhIXRoaXMuZm9ybS5Jc19OZWVkX1RDDQogICAgICAvLyAgICAgaWYgKHRoaXMuZm9ybS5Jc19OZWVkX1pMICYmIHRoaXMuZm9ybS5Jc19OZWVkX1RDKSB7DQogICAgICAvLyAgICAgICB0aGlzLmZvcm0uSXNfTmVlZF9UQyA9IHRydWUNCiAgICAgIC8vICAgICAgIHRoaXMuZm9ybS5Jc19OZWVkX1pMID0gZmFsc2UNCiAgICAgIC8vICAgICB9DQogICAgICAvLyAgIH0NCiAgICAgIC8vIH0NCiAgICAgIHRoaXMuWkxfQ2hlY2tfVXNlcklkcyA9IHRoaXMuZm9ybS5aTF9DaGVja19Vc2VySWQNCiAgICAgICAgPyB0aGlzLmZvcm0uWkxfQ2hlY2tfVXNlcklkLnNwbGl0KCcsJykNCiAgICAgICAgOiBbXQ0KICAgICAgdGhpcy5UQ19DaGVja19Vc2VySWRzID0gdGhpcy5mb3JtLlRDX0NoZWNrX1VzZXJJZA0KICAgICAgICA/IHRoaXMuZm9ybS5UQ19DaGVja19Vc2VySWQuc3BsaXQoJywnKQ0KICAgICAgICA6IFtdDQogICAgICBjb25zb2xlLmxvZygndGhpcy5UQ19DaGVja19Vc2VySWRzJywgdGhpcy5UQ19DaGVja19Vc2VySWRzKQ0KICAgIH0sDQogICAgZ2V0VXNlckxpc3QoKSB7DQogICAgICBHZXRVc2VyTGlzdCh7fSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy51c2VyT3B0aW9ucyA9IHJlcy5EYXRhDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgZ2V0RmFjdG9yeVBlb3BsZWxpc3QoKSB7DQogICAgICBHZXRGYWN0b3J5UGVvcGxlbGlzdCh7fSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy5vcHRpb25zVXNlckxpc3QgPSByZXMuRGF0YQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFzeW5jIGdldENoZWNrR3JvdXBMaXN0KCkgew0KICAgICAgYXdhaXQgR2V0Q2hlY2tHcm91cExpc3Qoe30pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMub3B0aW9uc0dyb3VwTGlzdCA9IHJlcy5EYXRhDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgY29uc29sZS5sb2coJyB0aGlzLm9wdGlvbnNHcm91cExpc3QnLCB0aGlzLm9wdGlvbnNHcm91cExpc3QpDQogICAgfSwNCiAgICBnZXRXb3JraW5nVGVhbXNMaXN0KCkgew0KICAgICAgR2V0V29ya2luZ1RlYW1zKHt9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLm9wdGlvbnNXb3JraW5nVGVhbXNMaXN0ID0gcmVzLkRhdGENCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyDnrZvpgInmo4Dmn6Xpobnnu4TlkIgNCiAgICAvLyBhc3luYyAgU2VsZWN0aW9uY2hlY2tDb21iaW5hdGlvbigpew0KICAgIC8vICAgdGhpcy5mb3JtLkNoZWNrX0dyb3VwX0xpc3QgPSBbXQ0KICAgIC8vICAgdGhpcy5vcHRpb25zR3JvdXBMaXN0ID0gW10NCiAgICAvLyAgIGF3YWl0IHRoaXMuZ2V0Q2hlY2tHcm91cExpc3QoKTsNCiAgICAvLyAgIGxldCBvcHRpb25zR3JvdXBMaXN0VGVtcCA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy5vcHRpb25zR3JvdXBMaXN0KSk7DQoNCiAgICAvLyAgIGNvbnNvbGUubG9nKCJTZWxlY3Rpb25jaGVja0NvbWJpbmF0aW9uMSIsIG9wdGlvbnNHcm91cExpc3RUZW1wKQ0KICAgIC8vICAgdGhpcy5mb3JtLlR5cGUgPT0gMSAgJiYgb3B0aW9uc0dyb3VwTGlzdFRlbXAgJiYgIChvcHRpb25zR3JvdXBMaXN0VGVtcCA9IG9wdGlvbnNHcm91cExpc3RUZW1wLmZpbHRlcih2ID0+IHYuQ2hlY2tfT2JqZWN0X0lkID09PSAiMSIpLmxlbmd0aCA+IDAgPyBvcHRpb25zR3JvdXBMaXN0VGVtcC5maWx0ZXIodiA9PnYuQ2hlY2tfT2JqZWN0X0lkID09PSAiMSIpIDogb3B0aW9uc0dyb3VwTGlzdFRlbXApIC8v6YCa6L+H5bel5bqP562b6YCJIFR5cGU9IDEgQ2hlY2tfVHlwZSA9IDAg5p6E5Lu2IDIgQ2hlY2tfVHlwZSA9IDEg6Zu25Lu2DQogICAgLy8gICBjb25zb2xlLmxvZygiMSIsIG9wdGlvbnNHcm91cExpc3RUZW1wKQ0KICAgIC8vICAgdGhpcy5mb3JtLlR5cGUgPT0gMiAgJiYgb3B0aW9uc0dyb3VwTGlzdFRlbXAgJiYgIChvcHRpb25zR3JvdXBMaXN0VGVtcCA9IG9wdGlvbnNHcm91cExpc3RUZW1wLmZpbHRlcih2ID0+IHYuQ2hlY2tfT2JqZWN0X0lkID09PSAiMiIpLmxlbmd0aCA+IDAgPyBvcHRpb25zR3JvdXBMaXN0VGVtcC5maWx0ZXIodiA9PiB2LkNoZWNrX09iamVjdF9JZCA9PT0gIjIiKSA6IG9wdGlvbnNHcm91cExpc3RUZW1wKQ0KICAgIC8vICAgaWYodGhpcy5mb3JtLklzX05lZWRfVEMgJiYgdGhpcy5mb3JtLklzX05lZWRfWkwpew0KICAgIC8vICAgICBvcHRpb25zR3JvdXBMaXN0VGVtcCA9ICBvcHRpb25zR3JvdXBMaXN0VGVtcC5maWx0ZXIodiA9PiB2LkNoZWNrX1R5cGUgPT09IC0xKS5sZW5ndGggPiAwID8gb3B0aW9uc0dyb3VwTGlzdFRlbXAuZmlsdGVyKHYgPT4gdi5DaGVja19UeXBlID09PSAtMSkgOiBvcHRpb25zR3JvdXBMaXN0VGVtcA0KICAgIC8vICAgICBjb25zb2xlLmxvZygiMiIsIG9wdGlvbnNHcm91cExpc3RUZW1wLHRoaXMuZm9ybS5Jc19OZWVkX1RDKQ0KDQogICAgLy8gICB9ZWxzZXsNCiAgICAvLyAgICAgaWYodGhpcy5mb3JtLklzX05lZWRfVEMgfHwgdGhpcy5mb3JtLklzX05lZWRfWkwpew0KICAgIC8vICAgICAgIHRoaXMuZm9ybS5Jc19OZWVkX1RDICYmIG9wdGlvbnNHcm91cExpc3RUZW1wICYmIChvcHRpb25zR3JvdXBMaXN0VGVtcCA9IG9wdGlvbnNHcm91cExpc3RUZW1wLmZpbHRlcih2ID0+IHYuQ2hlY2tfVHlwZSA9PT0gMiApLmxlbmd0aCA+IDAgPyBvcHRpb25zR3JvdXBMaXN0VGVtcC5maWx0ZXIodiA9PiB2LkNoZWNrX1R5cGUgPT09IDIgfHwgdi5DaGVja19UeXBlID09PSAtMSkgOiBvcHRpb25zR3JvdXBMaXN0VGVtcC5maWx0ZXIodiA9PiB2LkNoZWNrX1R5cGUgPT09IC0xKSkNCiAgICAvLyAgICAgICB0aGlzLmZvcm0uVHlwZSA9PSAxICAmJiBvcHRpb25zR3JvdXBMaXN0VGVtcCAmJiAgKG9wdGlvbnNHcm91cExpc3RUZW1wID0gb3B0aW9uc0dyb3VwTGlzdFRlbXAuZmlsdGVyKHYgPT4gdi5DaGVja19PYmplY3RfSWQgPT09ICIxIikubGVuZ3RoID4gMCA/IG9wdGlvbnNHcm91cExpc3RUZW1wLmZpbHRlcih2ID0+di5DaGVja19PYmplY3RfSWQgPT09ICIxIikgOiBvcHRpb25zR3JvdXBMaXN0VGVtcCkgLy/pgJrov4flt6Xluo/nrZvpgIkgVHlwZT0gMSBDaGVja19UeXBlID0gMCDmnoTku7YgMiBDaGVja19UeXBlID0gMSDpm7bku7YNCiAgICAvLyAgICAgICAgY29uc29sZS5sb2coIjMiLCBvcHRpb25zR3JvdXBMaXN0VGVtcCkNCiAgICAvLyAgICAgICAgdGhpcy5mb3JtLlR5cGUgPT0gMiAgJiYgb3B0aW9uc0dyb3VwTGlzdFRlbXAgJiYgIChvcHRpb25zR3JvdXBMaXN0VGVtcCA9IG9wdGlvbnNHcm91cExpc3RUZW1wLmZpbHRlcih2ID0+IHYuQ2hlY2tfT2JqZWN0X0lkID09PSAiMiIpLmxlbmd0aCA+IDAgPyBvcHRpb25zR3JvdXBMaXN0VGVtcC5maWx0ZXIodiA9PiB2LkNoZWNrX09iamVjdF9JZCA9PT0gIjIiKSA6IG9wdGlvbnNHcm91cExpc3RUZW1wKQ0KICAgIC8vICAgICAgIGNvbnNvbGUubG9nKCI0Iiwgb3B0aW9uc0dyb3VwTGlzdFRlbXAsdGhpcy5mb3JtLklzX05lZWRfVEMpDQogICAgLy8gICAgICAgdGhpcy5mb3JtLklzX05lZWRfWkwgJiYgb3B0aW9uc0dyb3VwTGlzdFRlbXAgJiYgKG9wdGlvbnNHcm91cExpc3RUZW1wID0gb3B0aW9uc0dyb3VwTGlzdFRlbXAuZmlsdGVyKHYgPT4gdi5DaGVja19UeXBlID09PSAxICkubGVuZ3RoID4gMCA/IG9wdGlvbnNHcm91cExpc3RUZW1wLmZpbHRlcih2ID0+IHYuQ2hlY2tfVHlwZSA9PT0gMSB8fCB2LkNoZWNrX1R5cGUgPT09IC0xKSA6ICBvcHRpb25zR3JvdXBMaXN0VGVtcC5maWx0ZXIodiA9PiB2LkNoZWNrX1R5cGUgPT09IC0xKSkgIC8v6YCa6L+H6LSo5qOA57G75Z6L562b6YCJDQogICAgLy8gICAgICAgdGhpcy5mb3JtLlR5cGUgPT0gMSAgJiYgb3B0aW9uc0dyb3VwTGlzdFRlbXAgJiYgIChvcHRpb25zR3JvdXBMaXN0VGVtcCA9IG9wdGlvbnNHcm91cExpc3RUZW1wLmZpbHRlcih2ID0+IHYuQ2hlY2tfT2JqZWN0X0lkID09PSAiMSIpLmxlbmd0aCA+IDAgPyBvcHRpb25zR3JvdXBMaXN0VGVtcC5maWx0ZXIodiA9PnYuQ2hlY2tfT2JqZWN0X0lkID09PSAiMSIpIDogb3B0aW9uc0dyb3VwTGlzdFRlbXApIC8v6YCa6L+H5bel5bqP562b6YCJIFR5cGU9IDEgQ2hlY2tfVHlwZSA9IDAg5p6E5Lu2IDIgQ2hlY2tfVHlwZSA9IDEg6Zu25Lu2DQogICAgLy8gICAgICAgY29uc29sZS5sb2coIjUiLCBvcHRpb25zR3JvdXBMaXN0VGVtcCkNCiAgICAvLyAgICAgICB0aGlzLmZvcm0uVHlwZSA9PSAyICAmJiBvcHRpb25zR3JvdXBMaXN0VGVtcCAmJiAgKG9wdGlvbnNHcm91cExpc3RUZW1wID0gb3B0aW9uc0dyb3VwTGlzdFRlbXAuZmlsdGVyKHYgPT4gdi5DaGVja19PYmplY3RfSWQgPT09ICIyIikubGVuZ3RoID4gMCA/IG9wdGlvbnNHcm91cExpc3RUZW1wLmZpbHRlcih2ID0+IHYuQ2hlY2tfT2JqZWN0X0lkID09PSAiMiIpIDogb3B0aW9uc0dyb3VwTGlzdFRlbXApDQogICAgLy8gICAgICAgY29uc29sZS5sb2coIjYiLCBvcHRpb25zR3JvdXBMaXN0VGVtcCx0aGlzLmZvcm0uSXNfTmVlZF9aTCkNCiAgICAvLyAgICAgIH0NCiAgICAvLyAgIH0NCg0KICAgIC8vICAgdGhpcy5vcHRpb25zR3JvdXBMaXN0ID0gb3B0aW9uc0dyb3VwTGlzdFRlbXANCg0KICAgIC8vICAgY29uc29sZS5sb2coIm9wdGlvbnNHcm91cExpc3RUZW1wIixvcHRpb25zR3JvdXBMaXN0VGVtcCkNCiAgICAvLyAgIGNvbnNvbGUubG9nKCJTZWxlY3Rpb25jaGVja0NvbWJpbmF0aW9uIix0aGlzLm9wdGlvbnNHcm91cExpc3QpDQoNCiAgICAvLyB9LA0KICAgIHJhZGlvQ2hlY2tTdHlsZUNoYW5nZSh2YWwpIHsNCiAgICAgIC8vIGlmICh2YWwgPT09ICcwJykgew0KICAgICAgLy8gICB0aGlzLmZvcm0uSXNfTmVlZF9UQyA9IHRydWUNCiAgICAgIC8vICAgdGhpcy5mb3JtLklzX05lZWRfWkwgPSBmYWxzZQ0KICAgICAgLy8gfQ0KICAgICAgdGhpcy5aTF9DaGVja19Vc2VySWRzID0gW10NCiAgICAgIHRoaXMuVENfQ2hlY2tfVXNlcklkcyA9IFtdDQogICAgICB0aGlzLmZvcm0uWkxfQ2hlY2tfVXNlcklkID0gJycNCiAgICAgIHRoaXMuZm9ybS5UQ19DaGVja19Vc2VySWQgPSAnJw0KICAgIH0sDQogICAgLy8gcmFkaW9DaGVja1R5cGVDaGFuZ2UodmFsKSB7DQogICAgLy8gICBpZiAodmFsKSB7DQogICAgLy8gICAgIHRoaXMuZm9ybS5Jc19OZWVkX1RDID0gdHJ1ZQ0KICAgIC8vICAgICB0aGlzLmZvcm0uSXNfTmVlZF9aTCA9IGZhbHNlDQogICAgLy8gICAgIHRoaXMuWkxfQ2hlY2tfVXNlcklkcyA9IFtdDQogICAgLy8gICAgIHRoaXMuZm9ybS5aTF9DaGVja19Vc2VySWQgPSAnJw0KICAgIC8vICAgfSBlbHNlIHsNCiAgICAvLyAgICAgdGhpcy5mb3JtLklzX05lZWRfWkwgPSB0cnVlDQogICAgLy8gICAgIHRoaXMuZm9ybS5Jc19OZWVkX1RDID0gZmFsc2UNCiAgICAvLyAgICAgdGhpcy5UQ19DaGVja19Vc2VySWRzID0gW10NCiAgICAvLyAgICAgdGhpcy5mb3JtLlRDX0NoZWNrX1VzZXJJZCA9ICcnDQogICAgLy8gICB9DQogICAgLy8gICAvLyB0aGlzLlpMX0NoZWNrX1VzZXJJZHMgPSBbXTsNCiAgICAvLyAgIC8vIHRoaXMuVENfQ2hlY2tfVXNlcklkcyA9IFtdOw0KICAgIC8vICAgLy8gdGhpcy5mb3JtLlpMX0NoZWNrX1VzZXJJZCA9ICIiOw0KICAgIC8vICAgLy8gdGhpcy5mb3JtLlRDX0NoZWNrX1VzZXJJZCA9ICIiOw0KICAgIC8vIH0sDQogICAgcmFkaW9DaGFuZ2UodmFsKSB7DQogICAgICBpZiAodmFsID09IGZhbHNlKSB7DQogICAgICAgIHRoaXMuZm9ybS5jaGVja0NoYW5nZSA9IGZhbHNlDQogICAgICAgIHRoaXMuZm9ybS5Jc19OZWVkX1RDID0gZmFsc2UNCiAgICAgICAgdGhpcy5mb3JtLklzX05lZWRfWkwgPSBmYWxzZQ0KICAgICAgICB0aGlzLlRDX0NoZWNrX1VzZXJJZHMgPSBbXQ0KICAgICAgICB0aGlzLlpMX0NoZWNrX1VzZXJJZHMgPSBbXQ0KICAgICAgICB0aGlzLmZvcm0uWkxfQ2hlY2tfVXNlcklkID0gJycNCiAgICAgICAgdGhpcy5mb3JtLlRDX0NoZWNrX1VzZXJJZCA9ICcnDQogICAgICAgIHRoaXMuZm9ybS5DaGVja19TdHlsZSA9ICcnDQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyB0aGlzLmZvcm0uY2hlY2tDaGFuZ2UgPSB0cnVlDQogICAgICAgIC8vIHRoaXMuZm9ybS5Jc19OZWVkX1RDID0gdHJ1ZQ0KICAgICAgICAvLyB0aGlzLmZvcm0uSXNfTmVlZF9aTCA9IGZhbHNlDQogICAgICAgIC8vIHRoaXMuQ2hlY2tDaGFuZ2UgPSAhIXRoaXMuZm9ybS5Jc19OZWVkX1RDDQogICAgICAgIHRoaXMuZm9ybS5DaGVja19TdHlsZSA9ICcwJw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g6YCJ5oup5p6E5Lu25bel5bqPDQogICAgY2hhbmdlVHlwZSh2YWwpIHsNCiAgICAgIC8vIHRoaXMuU2VsZWN0aW9uY2hlY2tDb21iaW5hdGlvbigpDQogICAgICAvLyBjb25zdCBDb2RlID0gdmFsDQogICAgICAvLyBjb25zb2xlLmxvZyhDb2RlLCAnQ29kZScpOw0KICAgICAgLy8gaWYgKENvZGUgPT09ICctMScpIHsNCiAgICAgIC8vICAgdGhpcy5mb3JtLlR5cGUgPSAxDQogICAgICAvLyB9IGVsc2UgaWYgKENvZGUgPT09ICcwJykgew0KICAgICAgLy8gICB0aGlzLmZvcm0uVHlwZSA9IDINCiAgICAgIC8vIH0gZWxzZSBpZiAoQ29kZSA9PT0gJzEnIHx8IENvZGUgPT09ICczJ3x8IENvZGUgPT09ICcyJykgew0KICAgICAgLy8gICB0aGlzLmZvcm0uVHlwZSA9IDMNCiAgICAgIC8vIH0NCiAgICAgIC8vIGlmICh0aGlzLmZvcm0uVHlwZSA9PT0gMSB8fCB0aGlzLmZvcm0uVHlwZSA9PT0gMykgew0KICAgICAgLy8gICB0aGlzLmZvcm0uSXNfQ3V0dGluZyA9IHVuZGVmaW5lZA0KICAgICAgLy8gfSBlbHNlIGlmICh0aGlzLmZvcm0uVHlwZSA9PT0gMikgew0KICAgICAgLy8gICB0aGlzLmZvcm0uSXNfV2VsZGluZ19Bc3NlbWJsaW5nID0gdW5kZWZpbmVkDQogICAgICAvLyB9DQogICAgfSwNCiAgICB0eXBlQ2hhbmdlKCkgew0KICAgICAgdGhpcy5mb3JtLlRhc2tfTW9kZWwgPSAnJw0KICAgIH0sDQogICAgY2hhbmdlVGModmFsKSB7DQogICAgICBjb25zb2xlLmxvZyh2YWwpDQogICAgICB0aGlzLmZvcm0uVENfQ2hlY2tfVXNlcklkID0gJycNCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdmFsLmxlbmd0aDsgaSsrKSB7DQogICAgICAgIGlmIChpID09IHZhbC5sZW5ndGggLSAxKSB7DQogICAgICAgICAgdGhpcy5mb3JtLlRDX0NoZWNrX1VzZXJJZCArPSB2YWxbaV0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmZvcm0uVENfQ2hlY2tfVXNlcklkICs9IHZhbFtpXSArICcsJw0KICAgICAgICB9DQogICAgICB9DQogICAgICBjb25zb2xlLmxvZyh0aGlzLmZvcm0uVENfQ2hlY2tfVXNlcklkLCAndGhpcy5mb3JtLlRDX0NoZWNrX1VzZXJJZCAnKQ0KICAgIH0sDQogICAgY2hhbmdlWkwodmFsKSB7DQogICAgICBjb25zb2xlLmxvZyh2YWwpDQogICAgICB0aGlzLmZvcm0uWkxfQ2hlY2tfVXNlcklkID0gJycNCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdmFsLmxlbmd0aDsgaSsrKSB7DQogICAgICAgIGlmIChpID09IHZhbC5sZW5ndGggLSAxKSB7DQogICAgICAgICAgdGhpcy5mb3JtLlpMX0NoZWNrX1VzZXJJZCArPSB2YWxbaV0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmZvcm0uWkxfQ2hlY2tfVXNlcklkICs9IHZhbFtpXSArICcsJw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBjaGVja2JveENoYW5nZSh2YWwsIHR5cGUpIHsNCiAgICAgIGlmICh0eXBlID09PSAxKSB7DQogICAgICAgIGlmICghdmFsKSB7DQogICAgICAgICAgdGhpcy5UQ19DaGVja19Vc2VySWRzID0gW10NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgaWYgKHR5cGUgPT09IDIpIHsNCiAgICAgICAgaWYgKCF2YWwpIHsNCiAgICAgICAgICB0aGlzLlpMX0NoZWNrX1VzZXJJZHMgPSBbXQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVTdWJtaXQoKSB7DQogICAgICAvLyBkZWxldGUgdGhpcy5mb3JtLlR5cGVzDQogICAgICBjb25zb2xlLmxvZyh0aGlzLmZvcm0sICd0aGlzLmZvcm0nKQ0KICAgICAgdGhpcy4kcmVmcy5mb3JtLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAoIXZhbGlkKSByZXR1cm4NCiAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gdHJ1ZQ0KICAgICAgICBjb25zdCB1SXRlbXMgPSB0aGlzLm9wdGlvbnNVc2VyTGlzdC5maW5kKA0KICAgICAgICAgICh2KSA9PiB2LklkID09PSB0aGlzLmZvcm0uQ29vcmRpbmF0ZV9Vc2VySWQNCiAgICAgICAgKQ0KICAgICAgICBpZiAodUl0ZW1zKSB7DQogICAgICAgICAgdGhpcy5mb3JtLkNvb3JkaW5hdGVfVXNlck5hbWUgPSB1SXRlbXMuRGlzcGxheV9OYW1lDQogICAgICAgIH0NCiAgICAgICAgaWYgKHRoaXMuZm9ybS5Jc19OZWVkX0NoZWNrKSB7DQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5Jc19OZWVkX1pMID09IGZhbHNlICYmIHRoaXMuZm9ybS5Jc19OZWVkX1RDID09IGZhbHNlKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfor7fpgInmi6notKjmo4DnsbvlnosnKQ0KICAgICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICAgIHJldHVybg0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmZvcm0uQ2hlY2tfU3R5bGUgPSBudWxsDQogICAgICAgICAgdGhpcy5mb3JtLkNoZWNrX0dyb3VwX0xpc3QgPSBbXQ0KICAgICAgICB9DQogICAgICAgIGNvbnN0IFpMID0gdGhpcy5mb3JtLklzX05lZWRfWkwgPyB0aGlzLmZvcm0uWkxfQ2hlY2tfVXNlcklkIDogJycNCiAgICAgICAgY29uc3QgVEMgPSB0aGlzLmZvcm0uSXNfTmVlZF9UQyA/IHRoaXMuZm9ybS5UQ19DaGVja19Vc2VySWQgOiAnJw0KICAgICAgICBpZiAodGhpcy5mb3JtLklzX05lZWRfWkwgJiYgKFpMID8/ICcnKSA9PSAnJykgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+mAieaLqei0qOajgOWRmCcpDQogICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy5mb3JtLklzX05lZWRfVEMgJiYgKFRDID8/ICcnKSA9PSAnJykgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+mAieaLqeaOouS8pOWRmCcpDQogICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KDQogICAgICAgIEFkZFdvcmtpbmdQcm9jZXNzKHsNCiAgICAgICAgICAuLi50aGlzLmZvcm0sDQogICAgICAgICAgWkxfQ2hlY2tfVXNlcklkOiBaTCwNCiAgICAgICAgICBUQ19DaGVja19Vc2VySWQ6IFRDDQogICAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+S/neWtmOaIkOWKnycsDQogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHRoaXMuJGVtaXQoJ3JlZnJlc2gnKQ0KICAgICAgICAgICAgdGhpcy4kZW1pdCgnY2xvc2UnKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlDQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICBjb2RlQ2hhbmdlKGUpIHsNCiAgICAgIHJldHVybiBlLnJlcGxhY2UoL1teYS16QS1aMC05XS9nLCAnJykNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["Add.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4JA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "Add.vue", "sourceRoot": "src/views/PRO/process-settings/management/component", "sourcesContent": ["<template>\r\n  <div class=\"form-wrapper\">\r\n    <div class=\"form-x\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\" style=\"width: 100%\">\r\n        <el-form-item label=\"名称\" prop=\"Name\">\r\n          <el-input v-model=\"form.Name\" :maxlength=\"30\" placeholder=\"最多30个字\" show-word-limit />\r\n        </el-form-item>\r\n        <el-form-item label=\"代号\" prop=\"Code\">\r\n          <el-input v-model=\"form.Code\" :maxlength=\"30\" placeholder=\"字母+数字，30字符\" show-word-limit\r\n            @input=\"(e) => (form.Code = codeChange(e))\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"类型\" prop=\"Bom_Level\">\r\n          <el-radio-group v-for=\"(item, index) in radioList\" :key=\"index\" v-model=\"form.Bom_Level\" class=\"radio\"\r\n            @change=\"changeType\">\r\n            <el-radio style=\"margin-right: 8px;\" :label=\"item.Code\">{{ item.Display_Name }}</el-radio>\r\n          </el-radio-group>\r\n          <!-- <el-radio-group v-model=\"form.Type\" @change=\"changeType\" class=\"radio\">\r\n              <el-radio :label=\"1\">构件工序</el-radio>一层\r\n            <el-radio v-if=\"isVersionFour\" :label=\"3\">部件工序</el-radio>二三四层\r\n            <el-radio v-if=\"!hiddenPart\" :label=\"2\">零件工序</el-radio> 五层\r\n          </el-radio-group> -->\r\n        </el-form-item>\r\n        <el-form-item label=\"排序\" prop=\"Sort\">\r\n          <el-input-number v-model=\"form.Sort\" :min=\"0\" step-strictly :step=\"1\" class=\"cs-number-btn-hidden w100\"\r\n            placeholder=\"请输入\" clearable=\"\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"协调人\" prop=\"Coordinate_UserId\">\r\n          <el-select v-model=\"form.Coordinate_UserId\" class=\"w100\" clearable filterable placeholder=\"请选择\">\r\n            <el-option v-for=\"item in optionsUserList\" :key=\"item.Id\" :label=\"item.Display_Name\" :value=\"item.Id\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"工序月均负荷\" prop=\"Month_Avg_Load\">\r\n          <el-input v-model=\"form.Month_Avg_Load\" placeholder=\"请输入\">\r\n            <template slot=\"append\">吨</template>\r\n          </el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否外协\" prop=\"Is_External\">\r\n          <el-radio-group v-model=\"form.Is_External\">\r\n            <el-radio :label=\"true\">是</el-radio>\r\n            <el-radio :label=\"false\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否装焊工序\" prop=\"Is_Welding_Assembling\">\r\n          <el-radio-group v-model=\"form.Is_Welding_Assembling\">\r\n            <el-radio :label=\"true\">是</el-radio>\r\n            <el-radio :label=\"false\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否专检\" prop=\"Is_Need_Check\">\r\n          <el-radio-group v-model=\"form.Is_Need_Check\" @change=\"radioChange\">\r\n            <el-radio :label=\"true\">是</el-radio>\r\n            <el-radio :label=\"false\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否互检\" prop=\"Is_Inter_Check\">\r\n          <el-radio-group v-model=\"form.Is_Inter_Check\" @change=\"radioInterChange\">\r\n            <el-radio :label=\"true\">是</el-radio>\r\n            <el-radio :label=\"false\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否下料工序\" prop=\"Is_Cutting\">\r\n          <el-radio-group v-model=\"form.Is_Cutting\">\r\n            <el-radio :label=\"true\">是</el-radio>\r\n            <el-radio :label=\"false\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否套料工序\" prop=\"Is_Nest\">\r\n          <el-radio-group v-model=\"form.Is_Nest\">\r\n            <el-radio :label=\"true\">是</el-radio>\r\n            <el-radio :label=\"false\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <template v-if=\"form.Is_Need_Check\">\r\n          <el-form-item label=\"专检方式\" prop=\"Check_Style\">\r\n            <el-radio-group v-model=\"form.Check_Style\" @change=\"radioCheckStyleChange\">\r\n              <el-radio label=\"0\">抽检</el-radio>\r\n              <el-radio label=\"1\">全检</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"专检类型\" prop=\"\">\r\n            <div>\r\n              <div style=\"margin-bottom: 10px;\">\r\n                <el-checkbox v-model=\"form.Is_Need_TC\" @change=\"checkboxChange($event, 1)\">\r\n                  <span> 探伤</span>\r\n                </el-checkbox>\r\n                <span style=\"margin-left: 30px; \">\r\n                  <span style=\"color: rgba(34, 40, 52, 0.65)\">探伤员：</span>\r\n                  <el-select v-model=\"TC_Check_UserIds\" filterable clearable :disabled=\"!form.Is_Need_TC\" multiple\r\n                    placeholder=\"请选择探伤员\" @change=\"changeTc\">\r\n                    <el-option v-for=\"item in optionsUserList\" :key=\"item.Id\" :label=\"item.Display_Name\"\r\n                      :value=\"item.Id\" />\r\n                  </el-select>\r\n                </span>\r\n              </div>\r\n              <div>\r\n                <el-checkbox v-model=\"form.Is_Need_ZL\" @change=\"checkboxChange($event, 2)\">\r\n                  <span> 质量</span>\r\n                </el-checkbox>\r\n                <span style=\"margin-left: 30px\">\r\n                  <span style=\"color: rgba(34, 40, 52, 0.65)\">质检员：</span>\r\n                  <el-select v-model=\"ZL_Check_UserIds\" :disabled=\"!form.Is_Need_ZL\" filterable clearable multiple\r\n                    placeholder=\"请选择质检员\" @change=\"changeZL\">\r\n                    <el-option v-for=\"item in optionsUserList\" :key=\"item.Id\" :label=\"item.Display_Name\"\r\n                      :value=\"item.Id\" />\r\n                  </el-select>\r\n                </span>\r\n              </div>\r\n\r\n            </div>\r\n          </el-form-item>\r\n\r\n          <!-- <el-form-item label=\"检查项组合\" prop=\"Check_Group_List\">\r\n           <el-select\r\n             v-model=\"form.Check_Group_List\"\r\n             multiple\r\n             style=\"width: 100%\"\r\n             :disabled=\"!Boolean(form.Type)\"\r\n             placeholder=\"请选择检查项组合\"\r\n           >\r\n             <el-option\r\n               v-for=\"item in optionsGroupList\"\r\n               :key=\"item.Id\"\r\n               :label=\"item.Group_Name\"\r\n               :value=\"item.Id\"\r\n             />\r\n           </el-select>\r\n         </el-form-item> -->\r\n        </template>\r\n\r\n        <el-form-item label=\"是否启用\" prop=\"Is_Enable\">\r\n          <el-radio-group v-model=\"form.Is_Enable\">\r\n            <el-radio :label=\"true\">是</el-radio>\r\n            <el-radio :label=\"false\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"加工班组\" prop=\"Working_Team_Ids\">\r\n          <el-select v-model=\"form.Working_Team_Ids\" multiple style=\"width: 100%\" placeholder=\"请选择加工班组\">\r\n            <el-option v-for=\"item in optionsWorkingTeamsList\" :key=\"item.Id\" :label=\"item.Name\" :value=\"item.Id\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"form.Remark\" type=\"textarea\" />\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n    <div class=\"btn-x\">\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button :loading=\"btnLoading\" type=\"primary\" @click=\"handleSubmit\">确 定\r\n      </el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\nimport { GetUserList } from '@/api/sys'\r\nimport {\r\n  AddWorkingProcess,\r\n  GetFactoryPeoplelist,\r\n  GetCheckGroupList,\r\n  GetWorkingTeams\r\n} from '@/api/PRO/technology-lib'\r\nimport { mapGetters } from 'vuex'\r\nimport { GetDictionaryDetailListByCode } from '@/api/sys'\r\nexport default {\r\n  props: {\r\n    type: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    rowInfo: {\r\n      type: Object,\r\n      default() {\r\n        return {}\r\n      }\r\n    },\r\n    bomList: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      checkList: [],\r\n      radioList: [],\r\n      btnLoading: false,\r\n      hiddenPart: false,\r\n\r\n      form: {\r\n        Code: '',\r\n        Name: '',\r\n        Bom_Level: '',\r\n        Month_Avg_Load: '',\r\n        Coordinate_UserId: '',\r\n        Sort: undefined,\r\n        Is_Enable: true,\r\n        Is_External: false,\r\n        Is_Nest: false,\r\n        Is_Need_Check: true,\r\n        Is_Inter_Check: true,\r\n        Is_Need_TC: true,\r\n        Is_Welding_Assembling: false,\r\n        Is_Cutting: false,\r\n        TC_Check_UserId: '',\r\n        Is_Need_ZL: false,\r\n        ZL_Check_UserId: '',\r\n\r\n        Check_Style: '0',\r\n\r\n        Working_Team_Ids: [],\r\n        Remark: ''\r\n      },\r\n      ZL_Check_UserIds: [],\r\n      TC_Check_UserIds: [],\r\n      CheckChange: true,\r\n      userOptions: [],\r\n      optionsUserList: [],\r\n      optionsGroupList: [],\r\n      optionsWorkingTeamsList: [],\r\n      rules: {\r\n        Code: [\r\n          { required: true, message: '请输入代号', trigger: 'blur' },\r\n          { max: 30, message: '长度在 30 个字符内', trigger: 'blur' }\r\n        ],\r\n        Name: [\r\n          { required: true, message: '请输入名称', trigger: 'blur' },\r\n          { max: 30, message: '长度在 30 个字符内', trigger: 'blur' }\r\n        ],\r\n        Bom_Level: [{ required: true, message: '请选择类型', trigger: 'change' }],\r\n        Sort: [{ required: true, message: '请输入', trigger: 'blur' }],\r\n        Is_Need_Check: [\r\n          { required: true, message: '请选择是否质检', trigger: 'change' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters('tenant', ['isVersionFour'])\r\n\r\n  },\r\n  mounted() {\r\n    this.getUserList()\r\n    this.getFactoryPeoplelist()\r\n    // this.getCheckGroupList();\r\n    // this.SelectioncheckCombination()\r\n    this.getWorkingTeamsList()\r\n    console.log('type', this.rowInfo)\r\n    this.type === 'edit' && this.initForm()\r\n    this.getBOMInfo()\r\n  },\r\n  methods: {\r\n\r\n    radioInterChange(val) { },\r\n    getBOMInfo() {\r\n      this.radioList = this.bomList\r\n    },\r\n    // 获取设备类型\r\n    async getDictionaryDetailListByCode() {\r\n      await GetDictionaryDetailListByCode({ dictionaryCode: 'deviceType' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.deviceTypeList = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      console.log(' this.optionsGroupList', this.optionsGroupList)\r\n    },\r\n\r\n    initForm() {\r\n      this.getBOMInfo()\r\n      const { Is_Nest, ...others } = this.rowInfo\r\n      this.form = Object.assign({}, others, { Is_Nest: !!Is_Nest })\r\n      console.log('others', others)\r\n      this.form.Bom_Level = String(this.form.Bom_Level)\r\n      //  if(this.form.Type==2){\r\n      //   this.form.Types = '0'\r\n      //  }else if(this.form.Type==3){\r\n      //   let Types = this.radioList.find(v => ['1', '2','3'].includes(v.Code))?.Code\r\n      //   console.log('Types', Types)\r\n      //   console.log('this.radioList', this.radioList)\r\n      //   this.form.Types = Types\r\n      //  }else if(this.form.Type==1){\r\n      //   this.form.Types = '-1'\r\n      //  }\r\n      console.log('this.form', this.form)\r\n\r\n      // 处理历史数据多选问题\r\n      // if (this.form.Is_Need_Check) {\r\n      //   if (this.form.Check_Style === '1') {\r\n\r\n      //   } else {\r\n      //     this.CheckChange = !!this.form.Is_Need_TC\r\n      //     if (this.form.Is_Need_ZL && this.form.Is_Need_TC) {\r\n      //       this.form.Is_Need_TC = true\r\n      //       this.form.Is_Need_ZL = false\r\n      //     }\r\n      //   }\r\n      // }\r\n      this.ZL_Check_UserIds = this.form.ZL_Check_UserId\r\n        ? this.form.ZL_Check_UserId.split(',')\r\n        : []\r\n      this.TC_Check_UserIds = this.form.TC_Check_UserId\r\n        ? this.form.TC_Check_UserId.split(',')\r\n        : []\r\n      console.log('this.TC_Check_UserIds', this.TC_Check_UserIds)\r\n    },\r\n    getUserList() {\r\n      GetUserList({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.userOptions = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getFactoryPeoplelist() {\r\n      GetFactoryPeoplelist({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.optionsUserList = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async getCheckGroupList() {\r\n      await GetCheckGroupList({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.optionsGroupList = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      console.log(' this.optionsGroupList', this.optionsGroupList)\r\n    },\r\n    getWorkingTeamsList() {\r\n      GetWorkingTeams({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.optionsWorkingTeamsList = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 筛选检查项组合\r\n    // async  SelectioncheckCombination(){\r\n    //   this.form.Check_Group_List = []\r\n    //   this.optionsGroupList = []\r\n    //   await this.getCheckGroupList();\r\n    //   let optionsGroupListTemp = JSON.parse(JSON.stringify(this.optionsGroupList));\r\n\r\n    //   console.log(\"SelectioncheckCombination1\", optionsGroupListTemp)\r\n    //   this.form.Type == 1  && optionsGroupListTemp &&  (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Object_Id === \"1\").length > 0 ? optionsGroupListTemp.filter(v =>v.Check_Object_Id === \"1\") : optionsGroupListTemp) //通过工序筛选 Type= 1 Check_Type = 0 构件 2 Check_Type = 1 零件\r\n    //   console.log(\"1\", optionsGroupListTemp)\r\n    //   this.form.Type == 2  && optionsGroupListTemp &&  (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Object_Id === \"2\").length > 0 ? optionsGroupListTemp.filter(v => v.Check_Object_Id === \"2\") : optionsGroupListTemp)\r\n    //   if(this.form.Is_Need_TC && this.form.Is_Need_ZL){\r\n    //     optionsGroupListTemp =  optionsGroupListTemp.filter(v => v.Check_Type === -1).length > 0 ? optionsGroupListTemp.filter(v => v.Check_Type === -1) : optionsGroupListTemp\r\n    //     console.log(\"2\", optionsGroupListTemp,this.form.Is_Need_TC)\r\n\r\n    //   }else{\r\n    //     if(this.form.Is_Need_TC || this.form.Is_Need_ZL){\r\n    //       this.form.Is_Need_TC && optionsGroupListTemp && (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Type === 2 ).length > 0 ? optionsGroupListTemp.filter(v => v.Check_Type === 2 || v.Check_Type === -1) : optionsGroupListTemp.filter(v => v.Check_Type === -1))\r\n    //       this.form.Type == 1  && optionsGroupListTemp &&  (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Object_Id === \"1\").length > 0 ? optionsGroupListTemp.filter(v =>v.Check_Object_Id === \"1\") : optionsGroupListTemp) //通过工序筛选 Type= 1 Check_Type = 0 构件 2 Check_Type = 1 零件\r\n    //        console.log(\"3\", optionsGroupListTemp)\r\n    //        this.form.Type == 2  && optionsGroupListTemp &&  (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Object_Id === \"2\").length > 0 ? optionsGroupListTemp.filter(v => v.Check_Object_Id === \"2\") : optionsGroupListTemp)\r\n    //       console.log(\"4\", optionsGroupListTemp,this.form.Is_Need_TC)\r\n    //       this.form.Is_Need_ZL && optionsGroupListTemp && (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Type === 1 ).length > 0 ? optionsGroupListTemp.filter(v => v.Check_Type === 1 || v.Check_Type === -1) :  optionsGroupListTemp.filter(v => v.Check_Type === -1))  //通过质检类型筛选\r\n    //       this.form.Type == 1  && optionsGroupListTemp &&  (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Object_Id === \"1\").length > 0 ? optionsGroupListTemp.filter(v =>v.Check_Object_Id === \"1\") : optionsGroupListTemp) //通过工序筛选 Type= 1 Check_Type = 0 构件 2 Check_Type = 1 零件\r\n    //       console.log(\"5\", optionsGroupListTemp)\r\n    //       this.form.Type == 2  && optionsGroupListTemp &&  (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Object_Id === \"2\").length > 0 ? optionsGroupListTemp.filter(v => v.Check_Object_Id === \"2\") : optionsGroupListTemp)\r\n    //       console.log(\"6\", optionsGroupListTemp,this.form.Is_Need_ZL)\r\n    //      }\r\n    //   }\r\n\r\n    //   this.optionsGroupList = optionsGroupListTemp\r\n\r\n    //   console.log(\"optionsGroupListTemp\",optionsGroupListTemp)\r\n    //   console.log(\"SelectioncheckCombination\",this.optionsGroupList)\r\n\r\n    // },\r\n    radioCheckStyleChange(val) {\r\n      // if (val === '0') {\r\n      //   this.form.Is_Need_TC = true\r\n      //   this.form.Is_Need_ZL = false\r\n      // }\r\n      this.ZL_Check_UserIds = []\r\n      this.TC_Check_UserIds = []\r\n      this.form.ZL_Check_UserId = ''\r\n      this.form.TC_Check_UserId = ''\r\n    },\r\n    // radioCheckTypeChange(val) {\r\n    //   if (val) {\r\n    //     this.form.Is_Need_TC = true\r\n    //     this.form.Is_Need_ZL = false\r\n    //     this.ZL_Check_UserIds = []\r\n    //     this.form.ZL_Check_UserId = ''\r\n    //   } else {\r\n    //     this.form.Is_Need_ZL = true\r\n    //     this.form.Is_Need_TC = false\r\n    //     this.TC_Check_UserIds = []\r\n    //     this.form.TC_Check_UserId = ''\r\n    //   }\r\n    //   // this.ZL_Check_UserIds = [];\r\n    //   // this.TC_Check_UserIds = [];\r\n    //   // this.form.ZL_Check_UserId = \"\";\r\n    //   // this.form.TC_Check_UserId = \"\";\r\n    // },\r\n    radioChange(val) {\r\n      if (val == false) {\r\n        this.form.checkChange = false\r\n        this.form.Is_Need_TC = false\r\n        this.form.Is_Need_ZL = false\r\n        this.TC_Check_UserIds = []\r\n        this.ZL_Check_UserIds = []\r\n        this.form.ZL_Check_UserId = ''\r\n        this.form.TC_Check_UserId = ''\r\n        this.form.Check_Style = ''\r\n      } else {\r\n        // this.form.checkChange = true\r\n        // this.form.Is_Need_TC = true\r\n        // this.form.Is_Need_ZL = false\r\n        // this.CheckChange = !!this.form.Is_Need_TC\r\n        this.form.Check_Style = '0'\r\n      }\r\n    },\r\n    // 选择构件工序\r\n    changeType(val) {\r\n      // this.SelectioncheckCombination()\r\n      // const Code = val\r\n      // console.log(Code, 'Code');\r\n      // if (Code === '-1') {\r\n      //   this.form.Type = 1\r\n      // } else if (Code === '0') {\r\n      //   this.form.Type = 2\r\n      // } else if (Code === '1' || Code === '3'|| Code === '2') {\r\n      //   this.form.Type = 3\r\n      // }\r\n      // if (this.form.Type === 1 || this.form.Type === 3) {\r\n      //   this.form.Is_Cutting = undefined\r\n      // } else if (this.form.Type === 2) {\r\n      //   this.form.Is_Welding_Assembling = undefined\r\n      // }\r\n    },\r\n    typeChange() {\r\n      this.form.Task_Model = ''\r\n    },\r\n    changeTc(val) {\r\n      console.log(val)\r\n      this.form.TC_Check_UserId = ''\r\n      for (let i = 0; i < val.length; i++) {\r\n        if (i == val.length - 1) {\r\n          this.form.TC_Check_UserId += val[i]\r\n        } else {\r\n          this.form.TC_Check_UserId += val[i] + ','\r\n        }\r\n      }\r\n      console.log(this.form.TC_Check_UserId, 'this.form.TC_Check_UserId ')\r\n    },\r\n    changeZL(val) {\r\n      console.log(val)\r\n      this.form.ZL_Check_UserId = ''\r\n      for (let i = 0; i < val.length; i++) {\r\n        if (i == val.length - 1) {\r\n          this.form.ZL_Check_UserId += val[i]\r\n        } else {\r\n          this.form.ZL_Check_UserId += val[i] + ','\r\n        }\r\n      }\r\n    },\r\n    checkboxChange(val, type) {\r\n      if (type === 1) {\r\n        if (!val) {\r\n          this.TC_Check_UserIds = []\r\n        }\r\n      }\r\n      if (type === 2) {\r\n        if (!val) {\r\n          this.ZL_Check_UserIds = []\r\n        }\r\n      }\r\n    },\r\n    handleSubmit() {\r\n      // delete this.form.Types\r\n      console.log(this.form, 'this.form')\r\n      this.$refs.form.validate((valid) => {\r\n        if (!valid) return\r\n        this.btnLoading = true\r\n        const uItems = this.optionsUserList.find(\r\n          (v) => v.Id === this.form.Coordinate_UserId\r\n        )\r\n        if (uItems) {\r\n          this.form.Coordinate_UserName = uItems.Display_Name\r\n        }\r\n        if (this.form.Is_Need_Check) {\r\n          if (this.form.Is_Need_ZL == false && this.form.Is_Need_TC == false) {\r\n            this.$message.error('请选择质检类型')\r\n            this.btnLoading = false\r\n            return\r\n          }\r\n        } else {\r\n          this.form.Check_Style = null\r\n          this.form.Check_Group_List = []\r\n        }\r\n        const ZL = this.form.Is_Need_ZL ? this.form.ZL_Check_UserId : ''\r\n        const TC = this.form.Is_Need_TC ? this.form.TC_Check_UserId : ''\r\n        if (this.form.Is_Need_ZL && (ZL ?? '') == '') {\r\n          this.$message.error('请选择质检员')\r\n          this.btnLoading = false\r\n          return\r\n        }\r\n        if (this.form.Is_Need_TC && (TC ?? '') == '') {\r\n          this.$message.error('请选择探伤员')\r\n          this.btnLoading = false\r\n          return\r\n        }\r\n\r\n        AddWorkingProcess({\r\n          ...this.form,\r\n          ZL_Check_UserId: ZL,\r\n          TC_Check_UserId: TC\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            this.$emit('refresh')\r\n            this.$emit('close')\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.btnLoading = false\r\n        })\r\n      })\r\n    },\r\n\r\n    codeChange(e) {\r\n      return e.replace(/[^a-zA-Z0-9]/g, '')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/styles/mixin.scss\";\r\n\r\n.btn-del {\r\n  margin-left: -100px;\r\n}\r\n\r\n.customRadioClass {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.checkboxFlex {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.form-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  min-height: 40vh;\r\n\r\n  .form-x {\r\n    max-height: 70vh;\r\n    overflow: auto;\r\n    padding-right: 16px;\r\n    @include scrollBar;\r\n  }\r\n\r\n  .btn-x {\r\n    padding-top: 16px;\r\n    text-align: right;\r\n  }\r\n}\r\n</style>\r\n"]}]}
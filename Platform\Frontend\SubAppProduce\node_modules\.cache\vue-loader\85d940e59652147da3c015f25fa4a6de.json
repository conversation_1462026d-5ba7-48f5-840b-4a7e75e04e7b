{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\Add.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\Add.vue", "mtime": 1757654745549}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IEdldEJPTUluZm8gfSBmcm9tICdAL3ZpZXdzL1BSTy9ib20tc2V0dGluZy91dGlscycKaW1wb3J0IHsgR2V0VXNlckxpc3QgfSBmcm9tICdAL2FwaS9zeXMnCmltcG9ydCB7CiAgQWRkV29ya2luZ1Byb2Nlc3MsCiAgR2V0RmFjdG9yeVBlb3BsZWxpc3QsCiAgR2V0Q2hlY2tHcm91cExpc3QsCiAgR2V0V29ya2luZ1RlYW1zCn0gZnJvbSAnQC9hcGkvUFJPL3RlY2hub2xvZ3ktbGliJwppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCcKaW1wb3J0IHsgR2V0RGljdGlvbmFyeURldGFpbExpc3RCeUNvZGUgfSBmcm9tICdAL2FwaS9zeXMnCmV4cG9ydCBkZWZhdWx0IHsKICBwcm9wczogewogICAgdHlwZTogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcnCiAgICB9LAogICAgcm93SW5mbzogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIHt9CiAgICAgIH0KICAgIH0sCiAgICB0b3RhbFdvcmtsb2FkUHJvcG9ydGlvbjogewogICAgICB0eXBlOiBOdW1iZXIsCiAgICAgIGRlZmF1bHQ6IDAKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBjaGVja0xpc3Q6IFtdLAogICAgICBidG5Mb2FkaW5nOiBmYWxzZSwKICAgICAgaGlkZGVuUGFydDogZmFsc2UsCgogICAgICBmb3JtOiB7CiAgICAgICAgQ29kZTogJycsCiAgICAgICAgTmFtZTogJycsCiAgICAgICAgQm9tX0xldmVsOiAnJywKICAgICAgICBNb250aF9BdmdfTG9hZDogJycsCiAgICAgICAgQ29vcmRpbmF0ZV9Vc2VySWQ6ICcnLAogICAgICAgIFNvcnQ6IHVuZGVmaW5lZCwKICAgICAgICBJc19FbmFibGU6IHRydWUsCiAgICAgICAgSXNfRXh0ZXJuYWw6IGZhbHNlLAogICAgICAgIElzX05lc3Q6IGZhbHNlLAogICAgICAgIElzX05lZWRfQ2hlY2s6IHRydWUsCiAgICAgICAgSXNfU2VsZl9DaGVjazogdHJ1ZSwKICAgICAgICBJc19JbnRlcl9DaGVjazogdHJ1ZSwKICAgICAgICBJc19QaWNrX01hdGVyaWFsOiBmYWxzZSwKICAgICAgICBJc19OZWVkX1RDOiB0cnVlLAogICAgICAgIElzX1dlbGRpbmdfQXNzZW1ibGluZzogZmFsc2UsCiAgICAgICAgSXNfQ3V0dGluZzogZmFsc2UsCiAgICAgICAgVENfQ2hlY2tfVXNlcklkOiAnJywKICAgICAgICBJc19OZWVkX1pMOiBmYWxzZSwKICAgICAgICBaTF9DaGVja19Vc2VySWQ6ICcnLAogICAgICAgIFNob3dfTW9kZWw6IGZhbHNlLAoKICAgICAgICBDaGVja19TdHlsZTogJzAnLAoKICAgICAgICBXb3JraW5nX1RlYW1fSWRzOiBbXSwKICAgICAgICBSZW1hcms6ICcnLAogICAgICAgIFdvcmtsb2FkX1Byb3BvcnRpb246ICcnCiAgICAgIH0sCiAgICAgIFpMX0NoZWNrX1VzZXJJZHM6IFtdLAogICAgICBUQ19DaGVja19Vc2VySWRzOiBbXSwKICAgICAgQ2hlY2tDaGFuZ2U6IHRydWUsCiAgICAgIHVzZXJPcHRpb25zOiBbXSwKICAgICAgb3B0aW9uc1VzZXJMaXN0OiBbXSwKICAgICAgb3B0aW9uc0dyb3VwTGlzdDogW10sCiAgICAgIG9wdGlvbnNXb3JraW5nVGVhbXNMaXN0OiBbXSwKICAgICAgcnVsZXM6IHsKICAgICAgICBDb2RlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5Luj5Y+3JywgdHJpZ2dlcjogJ2JsdXInIH0sCiAgICAgICAgICB7IG1heDogMzAsIG1lc3NhZ2U6ICfplb/luqblnKggMzAg5Liq5a2X56ym5YaFJywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICAgIE5hbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXlkI3np7AnLCB0cmlnZ2VyOiAnYmx1cicgfSwKICAgICAgICAgIHsgbWF4OiAzMCwgbWVzc2FnZTogJ+mVv+W6puWcqCAzMCDkuKrlrZfnrKblhoUnLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIF0sCiAgICAgICAgQm9tX0xldmVsOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeexu+WeiycsIHRyaWdnZXI6ICdjaGFuZ2UnIH1dLAogICAgICAgIFNvcnQ6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWlJywgdHJpZ2dlcjogJ2JsdXInIH1dLAogICAgICAgIElzX05lZWRfQ2hlY2s6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nmmK/lkKbotKjmo4AnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgICAgXQogICAgICB9LAogICAgICBXb3JrbG9hZF9Qcm9wb3J0aW9uOiAwLAogICAgICBib21MaXN0OiBbXSwKICAgICAgY29tTmFtZTogJycsCiAgICAgIHBhcnROYW1lOiAnJwogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIC4uLm1hcEdldHRlcnMoJ3RlbmFudCcsIFsnaXNWZXJzaW9uRm91ciddKQogIH0sCiAgYXN5bmMgY3JlYXRlZCgpIHsKICAgIGNvbnN0IHsgY29tTmFtZSwgcGFydE5hbWUsIGxpc3QgfSA9IGF3YWl0IEdldEJPTUluZm8oKQogICAgdGhpcy5jb21OYW1lID0gY29tTmFtZQogICAgdGhpcy5wYXJ0TmFtZSA9IHBhcnROYW1lCiAgICB0aGlzLmJvbUxpc3QgPSBsaXN0CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5nZXRVc2VyTGlzdCgpCiAgICB0aGlzLmdldEZhY3RvcnlQZW9wbGVsaXN0KCkKICAgIC8vIHRoaXMuZ2V0Q2hlY2tHcm91cExpc3QoKTsKICAgIHRoaXMuZ2V0V29ya2luZ1RlYW1zTGlzdCgpCiAgICB0aGlzLnR5cGUgPT09ICdlZGl0JyAmJiB0aGlzLmluaXRGb3JtKCkKICB9LAogIG1ldGhvZHM6IHsKICAgIGluaXRGb3JtKCkgewogICAgICBjb25zdCB7IElzX05lc3QsIC4uLm90aGVycyB9ID0gdGhpcy5yb3dJbmZvCiAgICAgIHRoaXMuZm9ybSA9IE9iamVjdC5hc3NpZ24oe30sIG90aGVycywgeyBJc19OZXN0OiAhIUlzX05lc3QgfSkKICAgICAgdGhpcy5mb3JtLkJvbV9MZXZlbCA9IFN0cmluZyh0aGlzLmZvcm0uQm9tX0xldmVsKQogICAgICB0aGlzLldvcmtsb2FkX1Byb3BvcnRpb24gPSB0aGlzLnJvd0luZm8uV29ya2xvYWRfUHJvcG9ydGlvbgogICAgICAvLyAgaWYodGhpcy5mb3JtLlR5cGU9PTIpewogICAgICAvLyAgIHRoaXMuZm9ybS5UeXBlcyA9ICcwJwogICAgICAvLyAgfWVsc2UgaWYodGhpcy5mb3JtLlR5cGU9PTMpewogICAgICAvLyAgIGxldCBUeXBlcyA9IHRoaXMucmFkaW9MaXN0LmZpbmQodiA9PiBbJzEnLCAnMicsJzMnXS5pbmNsdWRlcyh2LkNvZGUpKT8uQ29kZQogICAgICAvLyAgIGNvbnNvbGUubG9nKCdUeXBlcycsIFR5cGVzKQogICAgICAvLyAgIGNvbnNvbGUubG9nKCd0aGlzLnJhZGlvTGlzdCcsIHRoaXMucmFkaW9MaXN0KQogICAgICAvLyAgIHRoaXMuZm9ybS5UeXBlcyA9IFR5cGVzCiAgICAgIC8vICB9ZWxzZSBpZih0aGlzLmZvcm0uVHlwZT09MSl7CiAgICAgIC8vICAgdGhpcy5mb3JtLlR5cGVzID0gJy0xJwogICAgICAvLyAgfQogICAgICBjb25zb2xlLmxvZygndGhpcy5mb3JtJywgdGhpcy5mb3JtKQoKICAgICAgLy8g5aSE55CG5Y6G5Y+y5pWw5o2u5aSa6YCJ6Zeu6aKYCiAgICAgIC8vIGlmICh0aGlzLmZvcm0uSXNfTmVlZF9DaGVjaykgewogICAgICAvLyAgIGlmICh0aGlzLmZvcm0uQ2hlY2tfU3R5bGUgPT09ICcxJykgewoKICAgICAgLy8gICB9IGVsc2UgewogICAgICAvLyAgICAgdGhpcy5DaGVja0NoYW5nZSA9ICEhdGhpcy5mb3JtLklzX05lZWRfVEMKICAgICAgLy8gICAgIGlmICh0aGlzLmZvcm0uSXNfTmVlZF9aTCAmJiB0aGlzLmZvcm0uSXNfTmVlZF9UQykgewogICAgICAvLyAgICAgICB0aGlzLmZvcm0uSXNfTmVlZF9UQyA9IHRydWUKICAgICAgLy8gICAgICAgdGhpcy5mb3JtLklzX05lZWRfWkwgPSBmYWxzZQogICAgICAvLyAgICAgfQogICAgICAvLyAgIH0KICAgICAgLy8gfQogICAgICB0aGlzLlpMX0NoZWNrX1VzZXJJZHMgPSB0aGlzLmZvcm0uWkxfQ2hlY2tfVXNlcklkCiAgICAgICAgPyB0aGlzLmZvcm0uWkxfQ2hlY2tfVXNlcklkLnNwbGl0KCcsJykKICAgICAgICA6IFtdCiAgICAgIHRoaXMuVENfQ2hlY2tfVXNlcklkcyA9IHRoaXMuZm9ybS5UQ19DaGVja19Vc2VySWQKICAgICAgICA/IHRoaXMuZm9ybS5UQ19DaGVja19Vc2VySWQuc3BsaXQoJywnKQogICAgICAgIDogW10KICAgIH0sCiAgICAvLyDmmK/lkKboh6rmo4AKICAgIHJhZGlvU2VsZkNoZWNrKHZhbCkgeyB9LAogICAgLy8g5piv5ZCm5LqS5qOACiAgICByYWRpb0ludGVyQ2hlY2sodmFsKSB7IH0sCiAgICAvLyDojrflj5borr7lpIfnsbvlnosKICAgIGFzeW5jIGdldERpY3Rpb25hcnlEZXRhaWxMaXN0QnlDb2RlKCkgewogICAgICBhd2FpdCBHZXREaWN0aW9uYXJ5RGV0YWlsTGlzdEJ5Q29kZSh7IGRpY3Rpb25hcnlDb2RlOiAnZGV2aWNlVHlwZScgfSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIHRoaXMuZGV2aWNlVHlwZUxpc3QgPSByZXMuRGF0YQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgICBjb25zb2xlLmxvZygnIHRoaXMub3B0aW9uc0dyb3VwTGlzdCcsIHRoaXMub3B0aW9uc0dyb3VwTGlzdCkKICAgIH0sCiAgICBnZXRVc2VyTGlzdCgpIHsKICAgICAgR2V0VXNlckxpc3Qoe30pLnRoZW4oKHJlcykgPT4gewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICB0aGlzLnVzZXJPcHRpb25zID0gcmVzLkRhdGEKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICBnZXRGYWN0b3J5UGVvcGxlbGlzdCgpIHsKICAgICAgR2V0RmFjdG9yeVBlb3BsZWxpc3Qoe30pLnRoZW4oKHJlcykgPT4gewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICB0aGlzLm9wdGlvbnNVc2VyTGlzdCA9IHJlcy5EYXRhCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgYXN5bmMgZ2V0Q2hlY2tHcm91cExpc3QoKSB7CiAgICAgIGF3YWl0IEdldENoZWNrR3JvdXBMaXN0KHt9KS50aGVuKChyZXMpID0+IHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgdGhpcy5vcHRpb25zR3JvdXBMaXN0ID0gcmVzLkRhdGEKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgICAgY29uc29sZS5sb2coJyB0aGlzLm9wdGlvbnNHcm91cExpc3QnLCB0aGlzLm9wdGlvbnNHcm91cExpc3QpCiAgICB9LAogICAgZ2V0V29ya2luZ1RlYW1zTGlzdCgpIHsKICAgICAgR2V0V29ya2luZ1RlYW1zKHt9KS50aGVuKChyZXMpID0+IHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgdGhpcy5vcHRpb25zV29ya2luZ1RlYW1zTGlzdCA9IHJlcy5EYXRhCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgLy8g6YCJ5oup5LiT5qOA5pa55byPIOaKveajgOOAgeWFqOajgAogICAgcmFkaW9DaGVja1N0eWxlQ2hhbmdlKHZhbCkgewogICAgICAvLyBpZiAodmFsID09PSAnMCcpIHsKICAgICAgLy8gICB0aGlzLmZvcm0uSXNfTmVlZF9UQyA9IHRydWUKICAgICAgLy8gICB0aGlzLmZvcm0uSXNfTmVlZF9aTCA9IGZhbHNlCiAgICAgIC8vIH0KICAgICAgdGhpcy5aTF9DaGVja19Vc2VySWRzID0gW10KICAgICAgdGhpcy5UQ19DaGVja19Vc2VySWRzID0gW10KICAgICAgdGhpcy5mb3JtLlpMX0NoZWNrX1VzZXJJZCA9ICcnCiAgICAgIHRoaXMuZm9ybS5UQ19DaGVja19Vc2VySWQgPSAnJwogICAgfSwKICAgIC8vIOaYr+WQpuS4k+ajgAogICAgcmFkaW9DaGFuZ2UodmFsKSB7CiAgICAgIGlmICh2YWwgPT09IGZhbHNlICYmIHRoaXMudHlwZSA9PT0gJ2FkZCcpIHsKICAgICAgICB0aGlzLmZvcm0uY2hlY2tDaGFuZ2UgPSBmYWxzZQogICAgICAgIHRoaXMuZm9ybS5Jc19OZWVkX1RDID0gZmFsc2UKICAgICAgICB0aGlzLmZvcm0uSXNfTmVlZF9aTCA9IGZhbHNlCiAgICAgICAgdGhpcy5UQ19DaGVja19Vc2VySWRzID0gW10KICAgICAgICB0aGlzLlpMX0NoZWNrX1VzZXJJZHMgPSBbXQogICAgICAgIHRoaXMuZm9ybS5aTF9DaGVja19Vc2VySWQgPSAnJwogICAgICAgIHRoaXMuZm9ybS5UQ19DaGVja19Vc2VySWQgPSAnJwogICAgICAgIHRoaXMuZm9ybS5DaGVja19TdHlsZSA9ICcnCiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8gdGhpcy5mb3JtLmNoZWNrQ2hhbmdlID0gdHJ1ZQogICAgICAgIC8vIHRoaXMuZm9ybS5Jc19OZWVkX1RDID0gdHJ1ZQogICAgICAgIC8vIHRoaXMuZm9ybS5Jc19OZWVkX1pMID0gZmFsc2UKICAgICAgICAvLyB0aGlzLkNoZWNrQ2hhbmdlID0gISF0aGlzLmZvcm0uSXNfTmVlZF9UQwogICAgICAgIHRoaXMuZm9ybS5DaGVja19TdHlsZSA9ICcwJwogICAgICB9CiAgICB9LAogICAgLy8g6YCJ5oupQk9N5bGC57qnCiAgICBjaGFuZ2VUeXBlKHZhbCkgewogICAgICAvLyBjb25zdCBDb2RlID0gdmFsCiAgICAgIC8vIGNvbnNvbGUubG9nKENvZGUsICdDb2RlJyk7CiAgICAgIC8vIGlmIChDb2RlID09PSAnLTEnKSB7CiAgICAgIC8vICAgdGhpcy5mb3JtLlR5cGUgPSAxCiAgICAgIC8vIH0gZWxzZSBpZiAoQ29kZSA9PT0gJzAnKSB7CiAgICAgIC8vICAgdGhpcy5mb3JtLlR5cGUgPSAyCiAgICAgIC8vIH0gZWxzZSBpZiAoQ29kZSA9PT0gJzEnIHx8IENvZGUgPT09ICczJ3x8IENvZGUgPT09ICcyJykgewogICAgICAvLyAgIHRoaXMuZm9ybS5UeXBlID0gMwogICAgICAvLyB9CiAgICAgIC8vIGlmICh0aGlzLmZvcm0uVHlwZSA9PT0gMSB8fCB0aGlzLmZvcm0uVHlwZSA9PT0gMykgewogICAgICAvLyAgIHRoaXMuZm9ybS5Jc19DdXR0aW5nID0gdW5kZWZpbmVkCiAgICAgIC8vIH0gZWxzZSBpZiAodGhpcy5mb3JtLlR5cGUgPT09IDIpIHsKICAgICAgLy8gICB0aGlzLmZvcm0uSXNfV2VsZGluZ19Bc3NlbWJsaW5nID0gdW5kZWZpbmVkCiAgICAgIC8vIH0KICAgIH0sCiAgICB0eXBlQ2hhbmdlKCkgewogICAgICB0aGlzLmZvcm0uVGFza19Nb2RlbCA9ICcnCiAgICB9LAogICAgY2hhbmdlVGModmFsKSB7CiAgICAgIGNvbnNvbGUubG9nKHZhbCkKICAgICAgdGhpcy5mb3JtLlRDX0NoZWNrX1VzZXJJZCA9ICcnCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdmFsLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgaWYgKGkgPT09IHZhbC5sZW5ndGggLSAxKSB7CiAgICAgICAgICB0aGlzLmZvcm0uVENfQ2hlY2tfVXNlcklkICs9IHZhbFtpXQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLmZvcm0uVENfQ2hlY2tfVXNlcklkICs9IHZhbFtpXSArICcsJwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIGNoYW5nZVpMKHZhbCkgewogICAgICBjb25zb2xlLmxvZyh2YWwpCiAgICAgIHRoaXMuZm9ybS5aTF9DaGVja19Vc2VySWQgPSAnJwogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHZhbC5sZW5ndGg7IGkrKykgewogICAgICAgIGlmIChpID09PSB2YWwubGVuZ3RoIC0gMSkgewogICAgICAgICAgdGhpcy5mb3JtLlpMX0NoZWNrX1VzZXJJZCArPSB2YWxbaV0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5mb3JtLlpMX0NoZWNrX1VzZXJJZCArPSB2YWxbaV0gKyAnLCcKICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICBjaGVja2JveENoYW5nZSh2YWwsIHR5cGUpIHsKICAgICAgaWYgKHR5cGUgPT09IDEpIHsKICAgICAgICBpZiAoIXZhbCkgewogICAgICAgICAgdGhpcy5UQ19DaGVja19Vc2VySWRzID0gW10KICAgICAgICB9CiAgICAgIH0KICAgICAgaWYgKHR5cGUgPT09IDIpIHsKICAgICAgICBpZiAoIXZhbCkgewogICAgICAgICAgdGhpcy5aTF9DaGVja19Vc2VySWRzID0gW10KICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICBoYW5kbGVTdWJtaXQoKSB7CiAgICAgIC8vIGRlbGV0ZSB0aGlzLmZvcm0uVHlwZXMKICAgICAgY29uc29sZS5sb2codGhpcy5mb3JtLCAndGhpcy5mb3JtJykKICAgICAgdGhpcy4kcmVmcy5mb3JtLnZhbGlkYXRlKCh2YWxpZCkgPT4gewogICAgICAgIGlmICghdmFsaWQpIHJldHVybgogICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IHRydWUKICAgICAgICBjb25zdCB1SXRlbXMgPSB0aGlzLm9wdGlvbnNVc2VyTGlzdC5maW5kKAogICAgICAgICAgKHYpID0+IHYuSWQgPT09IHRoaXMuZm9ybS5Db29yZGluYXRlX1VzZXJJZAogICAgICAgICkKICAgICAgICBpZiAodUl0ZW1zKSB7CiAgICAgICAgICB0aGlzLmZvcm0uQ29vcmRpbmF0ZV9Vc2VyTmFtZSA9IHVJdGVtcy5EaXNwbGF5X05hbWUKICAgICAgICB9CiAgICAgICAgaWYgKHRoaXMuZm9ybS5Jc19OZWVkX0NoZWNrKSB7IC8vIOWmguaenOmcgOimgeS4k+ajgAogICAgICAgICAgaWYgKHRoaXMuZm9ybS5Jc19OZWVkX1pMID09PSBmYWxzZSAmJiB0aGlzLmZvcm0uSXNfTmVlZF9UQyA9PT0gZmFsc2UpIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6K+36YCJ5oup6LSo5qOA57G75Z6LJykKICAgICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UKICAgICAgICAgICAgcmV0dXJuCiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsgLy8g5aaC5p6c5LiN6ZyA6KaB5LiT5qOA77yM5YiZ5LiN6ZyA6KaB5LiT5qOA5pa55byP44CB5LiT5qOA57G75Z6L44CB5LiT5qOA5Lq65ZGYICDlkI7nu63pnIDopoHkupLmo4DpgLvovpHov63ku6MKICAgICAgICAgIHRoaXMuZm9ybS5jaGVja0NoYW5nZSA9IGZhbHNlCiAgICAgICAgICB0aGlzLmZvcm0uSXNfTmVlZF9UQyA9IGZhbHNlCiAgICAgICAgICB0aGlzLmZvcm0uSXNfTmVlZF9aTCA9IGZhbHNlCiAgICAgICAgICB0aGlzLlRDX0NoZWNrX1VzZXJJZHMgPSBbXQogICAgICAgICAgdGhpcy5aTF9DaGVja19Vc2VySWRzID0gW10KICAgICAgICAgIHRoaXMuZm9ybS5aTF9DaGVja19Vc2VySWQgPSAnJwogICAgICAgICAgdGhpcy5mb3JtLlRDX0NoZWNrX1VzZXJJZCA9ICcnCiAgICAgICAgICB0aGlzLmZvcm0uQ2hlY2tfU3R5bGUgPSBudWxsCiAgICAgICAgfQogICAgICAgIGNvbnN0IFpMID0gdGhpcy5mb3JtLklzX05lZWRfWkwgPyB0aGlzLmZvcm0uWkxfQ2hlY2tfVXNlcklkIDogJycKICAgICAgICBjb25zdCBUQyA9IHRoaXMuZm9ybS5Jc19OZWVkX1RDID8gdGhpcy5mb3JtLlRDX0NoZWNrX1VzZXJJZCA6ICcnCiAgICAgICAgaWYgKHRoaXMuZm9ybS5Jc19OZWVkX1pMICYmIChaTCA/PyAnJykgPT09ICcnKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfor7fpgInmi6notKjmo4DlkZgnKQogICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UKICAgICAgICAgIHJldHVybgogICAgICAgIH0KICAgICAgICBpZiAodGhpcy5mb3JtLklzX05lZWRfVEMgJiYgKFRDID8/ICcnKSA9PT0gJycpIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+mAieaLqeaOouS8pOWRmCcpCiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQogICAgICAgICAgcmV0dXJuCiAgICAgICAgfQoKICAgICAgICBBZGRXb3JraW5nUHJvY2Vzcyh7CiAgICAgICAgICAuLi50aGlzLmZvcm0sCiAgICAgICAgICBaTF9DaGVja19Vc2VySWQ6IFpMLAogICAgICAgICAgVENfQ2hlY2tfVXNlcklkOiBUQwogICAgICAgIH0pLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgbWVzc2FnZTogJ+S/neWtmOaIkOWKnycsCiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIHRoaXMuJGVtaXQoJ3JlZnJlc2gnKQogICAgICAgICAgICB0aGlzLiRlbWl0KCdjbG9zZScpCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICAgIH0pCiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQogICAgICAgIH0pCiAgICAgIH0pCiAgICB9LAoKICAgIGNvZGVDaGFuZ2UoZSkgewogICAgICByZXR1cm4gZS5yZXBsYWNlKC9bXmEtekEtWjAtOV0vZywgJycpCiAgICB9LAoKICAgIGhhbmRsZVBlcmNlbnRhZ2VJbnB1dCh2YWx1ZSkgewogICAgICAvLyDlpoLmnpzovpPlhaXkuLrnqbrvvIznm7TmjqXov5Tlm54KICAgICAgaWYgKHZhbHVlID09PSAnJyB8fCB2YWx1ZSA9PT0gbnVsbCB8fCB2YWx1ZSA9PT0gdW5kZWZpbmVkKSB7CiAgICAgICAgdGhpcy5mb3JtLldvcmtsb2FkX1Byb3BvcnRpb24gPSAnJwogICAgICAgIHJldHVybgogICAgICB9CgogICAgICAvLyDovazmjaLkuLrlrZfnrKbkuLLov5vooYzlpITnkIYKICAgICAgbGV0IGlucHV0VmFsdWUgPSBTdHJpbmcodmFsdWUpCgogICAgICAvLyDlj6rlhYHorrjmlbDlrZflkozkuIDkuKrlsI/mlbDngrnvvIznp7vpmaTlhbbku5blrZfnrKbvvIjljIXmi6zotJ/lj7fvvIkKICAgICAgaW5wdXRWYWx1ZSA9IGlucHV0VmFsdWUucmVwbGFjZSgvW14wLTkuXS9nLCAnJykKCiAgICAgIC8vIOehruS/neWPquacieS4gOS4quWwj+aVsOeCuQogICAgICBjb25zdCBkb3RDb3VudCA9IChpbnB1dFZhbHVlLm1hdGNoKC9cLi9nKSB8fCBbXSkubGVuZ3RoCiAgICAgIGlmIChkb3RDb3VudCA+IDEpIHsKICAgICAgICAvLyDlpoLmnpzmnInlpJrkuKrlsI/mlbDngrnvvIzlj6rkv53nlZnnrKzkuIDkuKoKICAgICAgICBjb25zdCBmaXJzdERvdEluZGV4ID0gaW5wdXRWYWx1ZS5pbmRleE9mKCcuJykKICAgICAgICBpbnB1dFZhbHVlID0gaW5wdXRWYWx1ZS5zdWJzdHJpbmcoMCwgZmlyc3REb3RJbmRleCArIDEpICsgaW5wdXRWYWx1ZS5zdWJzdHJpbmcoZmlyc3REb3RJbmRleCArIDEpLnJlcGxhY2UoL1wuL2csICcnKQogICAgICB9CgogICAgICAvLyDlpoLmnpzlj6rmmK/lsI/mlbDngrnvvIzorr7nva7kuLrnqboKICAgICAgaWYgKGlucHV0VmFsdWUgPT09ICcuJykgewogICAgICAgIHRoaXMuZm9ybS5Xb3JrbG9hZF9Qcm9wb3J0aW9uID0gJycKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgLy8g5aaC5p6c5aSE55CG5ZCO5Li656m65a2X56ym5Liy77yM6K6+572u5Li656m6CiAgICAgIGlmIChpbnB1dFZhbHVlID09PSAnJykgewogICAgICAgIHRoaXMuZm9ybS5Xb3JrbG9hZF9Qcm9wb3J0aW9uID0gJycKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgLy8g6ZmQ5Yi25bCP5pWw5L2N5pWw5pyA5aSaMuS9jQogICAgICBpZiAoaW5wdXRWYWx1ZS5pbmNsdWRlcygnLicpKSB7CiAgICAgICAgY29uc3QgcGFydHMgPSBpbnB1dFZhbHVlLnNwbGl0KCcuJykKICAgICAgICBpZiAocGFydHNbMV0gJiYgcGFydHNbMV0ubGVuZ3RoID4gMikgewogICAgICAgICAgaW5wdXRWYWx1ZSA9IHBhcnRzWzBdICsgJy4nICsgcGFydHNbMV0uc3Vic3RyaW5nKDAsIDIpCiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDovazmjaLkuLrmlbDlrZfov5vooYzojIPlm7Tmo4Dmn6UKICAgICAgY29uc3QgbnVtVmFsdWUgPSBwYXJzZUZsb2F0KGlucHV0VmFsdWUpCgogICAgICAvLyDlpoLmnpzkuI3mmK/mnInmlYjmlbDlrZfvvIzorr7nva7kuLrnqboKICAgICAgaWYgKGlzTmFOKG51bVZhbHVlKSkgewogICAgICAgIHRoaXMuZm9ybS5Xb3JrbG9hZF9Qcm9wb3J0aW9uID0gJycKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgLy8g5qC55o2uIHRvdGFsV29ya2xvYWRQcm9wb3J0aW9uIOiuoeeul+acgOWkp+WAvAogICAgICBsZXQgbWF4VmFsdWUgPSAxMDAKICAgICAgbGV0IGN1cnJlbnRUb3RhbCA9IDAKCiAgICAgIGlmICh0aGlzLnRvdGFsV29ya2xvYWRQcm9wb3J0aW9uKSB7CiAgICAgICAgLy8g56Gu5L+d5omA5pyJ5pWw5YC86YO95piv5rWu54K55pWw5bm25L+d55WZMuS9jeWwj+aVsOi/m+ihjOiuoeeulwogICAgICAgIGNvbnN0IHRvdGFsID0gcGFyc2VGbG9hdCh0aGlzLnRvdGFsV29ya2xvYWRQcm9wb3J0aW9uLnRvRml4ZWQoMikpCiAgICAgICAgY29uc3QgY3VycmVudCA9IHBhcnNlRmxvYXQoKHRoaXMuV29ya2xvYWRfUHJvcG9ydGlvbiB8fCAwKS50b1N0cmluZygpKQoKICAgICAgICAvLyDorqHnrpflt67lgLzlubbkv53nlZky5L2N5bCP5pWwCiAgICAgICAgY29uc3QgZGlmZmVyZW5jZSA9IHRvdGFsIC0gY3VycmVudAogICAgICAgIGN1cnJlbnRUb3RhbCA9IHBhcnNlRmxvYXQoZGlmZmVyZW5jZS50b0ZpeGVkKDIpKQoKICAgICAgICAvLyDpqozor4HmlbDlrabkuIDoh7TmgKfvvJrnoa7kv50gY3VycmVudFRvdGFsICsgY3VycmVudCA9IHRvdGFsCiAgICAgICAgY29uc3QgdmVyaWZpY2F0aW9uID0gcGFyc2VGbG9hdCgoY3VycmVudFRvdGFsICsgY3VycmVudCkudG9GaXhlZCgyKSkKICAgICAgICBpZiAodmVyaWZpY2F0aW9uICE9PSB0b3RhbCkgewogICAgICAgICAgLy8g5aaC5p6c5LiN5LiA6Ie077yM6LCD5pW0IGN1cnJlbnRUb3RhbCDku6Xkv53or4HkuIDoh7TmgKcKICAgICAgICAgIGN1cnJlbnRUb3RhbCA9IHBhcnNlRmxvYXQoKHRvdGFsIC0gY3VycmVudCkudG9GaXhlZCgyKSkKICAgICAgICB9CiAgICAgIH0KCiAgICAgIGlmIChjdXJyZW50VG90YWwgPT09IDEwMCkgewogICAgICAgIC8vIOWmguaenOaAu+WSjOW3sue7j+aYrzEwMO+8jOacgOWkp+WPquiDvei+k+WFpTAKICAgICAgICBtYXhWYWx1ZSA9IDAKICAgICAgfSBlbHNlIGlmIChjdXJyZW50VG90YWwgPiAwICYmIGN1cnJlbnRUb3RhbCA8IDEwMCkgewogICAgICAgIC8vIOWmguaenOaAu+WSjOWcqDAtMTAw5LmL6Ze077yM5pyA5aSn5YC85pivMTAw5YeP5Y675b2T5YmN5oC75ZKMCiAgICAgICAgbWF4VmFsdWUgPSAxMDAgLSBjdXJyZW50VG90YWwKICAgICAgfQogICAgICAvLyDlpoLmnpzmgLvlkozmmK8w5oiW56m677yM5pyA5aSn5YC85L+d5oyBMTAwCgogICAgICAvLyDpmZDliLbojIPlm7TlnKggMC1tYXhWYWx1ZSDkuYvpl7QKICAgICAgaWYgKG51bVZhbHVlIDwgMCkgewogICAgICAgIHRoaXMuZm9ybS5Xb3JrbG9hZF9Qcm9wb3J0aW9uID0gJzAnCiAgICAgIH0gZWxzZSBpZiAobnVtVmFsdWUgPiBtYXhWYWx1ZSkgewogICAgICAgIC8vIOS/neeVmTLkvY3lsI/mlbAKICAgICAgICB0aGlzLmZvcm0uV29ya2xvYWRfUHJvcG9ydGlvbiA9IG1heFZhbHVlLnRvRml4ZWQoMikucmVwbGFjZSgvXC4/MCskLywgJycpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5L+d5oyB5Y6f5aeL6L6T5YWl5qC85byP77yI5YyF5ous5bCP5pWw54K577yJ77yM5L2G56Gu5L+d5LiN6LaF6L+HMuS9jeWwj+aVsAogICAgICAgIHRoaXMuZm9ybS5Xb3JrbG9hZF9Qcm9wb3J0aW9uID0gaW5wdXRWYWx1ZQogICAgICB9CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["Add.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAo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file": "Add.vue", "sourceRoot": "src/views/PRO/process-settings/management/component", "sourcesContent": ["<template>\n  <div class=\"form-wrapper\">\n    <div class=\"form-x\">\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\" style=\"width: 100%\">\n        <el-divider content-position=\"left\">基础信息</el-divider>\n        <el-form-item label=\"名称\" prop=\"Name\">\n          <el-input v-model=\"form.Name\" :maxlength=\"30\" placeholder=\"最多30个字\" show-word-limit />\n        </el-form-item>\n        <el-form-item label=\"代号\" prop=\"Code\">\n          <el-input\n            v-model=\"form.Code\"\n            :maxlength=\"30\"\n            placeholder=\"字母+数字，30字符\"\n            show-word-limit\n            @input=\"(e) => (form.Code = codeChange(e))\"\n          />\n        </el-form-item>\n        <el-form-item label=\"类型\" prop=\"Bom_Level\">\n          <el-radio-group\n            v-for=\"(item, index) in bomList\"\n            :key=\"index\"\n            v-model=\"form.Bom_Level\"\n            class=\"radio\"\n            @change=\"changeType\"\n          >\n            <el-radio style=\"margin-right: 8px;\" :label=\"item.Code\">{{ item.Display_Name }}</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"Sort\">\n          <el-input-number\n            v-model=\"form.Sort\"\n            :min=\"0\"\n            step-strictly\n            :step=\"1\"\n            class=\"cs-number-btn-hidden w100\"\n            placeholder=\"请输入\"\n            clearable=\"\"\n          />\n        </el-form-item>\n        <el-form-item label=\"协调人\" prop=\"Coordinate_UserId\">\n          <el-select v-model=\"form.Coordinate_UserId\" class=\"w100\" clearable filterable placeholder=\"请选择\">\n            <el-option v-for=\"item in optionsUserList\" :key=\"item.Id\" :label=\"item.Display_Name\" :value=\"item.Id\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"工序月均负荷\" prop=\"Month_Avg_Load\">\n          <el-input v-model=\"form.Month_Avg_Load\" placeholder=\"请输入\">\n            <template slot=\"append\">吨</template>\n          </el-input>\n        </el-form-item>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否启用\" prop=\"Is_Enable\">\n              <el-radio-group v-model=\"form.Is_Enable\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否外协\" prop=\"Is_External\">\n              <el-radio-group v-model=\"form.Is_External\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否装焊工序\" prop=\"Is_Welding_Assembling\">\n              <el-radio-group v-model=\"form.Is_Welding_Assembling\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否下料工序\" prop=\"Is_Cutting\">\n              <el-radio-group v-model=\"form.Is_Cutting\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否套料工序\" prop=\"Is_Nest\">\n              <el-radio-group v-model=\"form.Is_Nest\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否领料工序\" prop=\"Is_Pick_Material\">\n              <el-radio-group v-model=\"form.Is_Pick_Material\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"加工班组\" prop=\"Working_Team_Ids\">\n          <el-select v-model=\"form.Working_Team_Ids\" multiple style=\"width: 100%\" placeholder=\"请选择加工班组\">\n            <el-option v-for=\"item in optionsWorkingTeamsList\" :key=\"item.Id\" :label=\"item.Name\" :value=\"item.Id\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"备注\">\n          <el-input v-model=\"form.Remark\" type=\"textarea\" />\n        </el-form-item>\n        <el-divider content-position=\"left\">质检信息</el-divider>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否自检\" prop=\"Is_Self_Check\">\n              <el-radio-group v-model=\"form.Is_Self_Check\" @change=\"radioSelfCheck\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否互检\" prop=\"Is_Inter_Check\">\n              <el-radio-group v-model=\"form.Is_Inter_Check\" @change=\"radioInterCheck\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否专检\" prop=\"Is_Need_Check\">\n              <el-radio-group v-model=\"form.Is_Need_Check\" @change=\"radioChange\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <template v-if=\"form.Is_Need_Check\">\n              <el-form-item label=\"专检方式\" prop=\"Check_Style\">\n                <el-radio-group v-model=\"form.Check_Style\" @change=\"radioCheckStyleChange\">\n                  <el-radio label=\"0\">抽检</el-radio>\n                  <el-radio label=\"1\">全检</el-radio>\n                </el-radio-group>\n              </el-form-item>\n            </template>\n          </el-col>\n        </el-row>\n\n        <template v-if=\"form.Is_Need_Check\">\n          <el-form-item label=\"专检类型\" prop=\"\">\n            <div>\n              <div style=\"margin-bottom: 10px;\">\n                <el-checkbox v-model=\"form.Is_Need_TC\" @change=\"checkboxChange($event, 1)\">\n                  <span> 探伤</span>\n                </el-checkbox>\n                <span style=\"margin-left: 30px; \">\n                  <span style=\"color: rgba(34, 40, 52, 0.65)\">探伤员：</span>\n                  <el-select\n                    v-model=\"TC_Check_UserIds\"\n                    filterable\n                    clearable\n                    :disabled=\"!form.Is_Need_TC\"\n                    multiple\n                    placeholder=\"请选择探伤员\"\n                    @change=\"changeTc\"\n                  >\n                    <el-option\n                      v-for=\"item in optionsUserList\"\n                      :key=\"item.Id\"\n                      :label=\"item.Display_Name\"\n                      :value=\"item.Id\"\n                    />\n                  </el-select>\n                </span>\n              </div>\n              <div>\n                <el-checkbox v-model=\"form.Is_Need_ZL\" @change=\"checkboxChange($event, 2)\">\n                  <span> 质量</span>\n                </el-checkbox>\n                <span style=\"margin-left: 30px\">\n                  <span style=\"color: rgba(34, 40, 52, 0.65)\">质检员：</span>\n                  <el-select\n                    v-model=\"ZL_Check_UserIds\"\n                    :disabled=\"!form.Is_Need_ZL\"\n                    filterable\n                    clearable\n                    multiple\n                    placeholder=\"请选择质检员\"\n                    @change=\"changeZL\"\n                  >\n                    <el-option\n                      v-for=\"item in optionsUserList\"\n                      :key=\"item.Id\"\n                      :label=\"item.Display_Name\"\n                      :value=\"item.Id\"\n                    />\n                  </el-select>\n                </span>\n              </div>\n            </div>\n          </el-form-item>\n        </template>\n        <el-divider content-position=\"left\">其他信息</el-divider>\n        <el-form-item label=\"工作量占比\" prop=\"Workload_Proportion\">\n          <el-input v-model=\"form.Workload_Proportion\" placeholder=\"请输入\" @input=\"handlePercentageInput\">\n            <template slot=\"append\">%</template>\n          </el-input>\n        </el-form-item>\n        <el-form-item label=\"是否展示模型\" prop=\"Show_Model\">\n          <el-radio-group v-model=\"form.Show_Model\">\n            <el-radio :label=\"true\">是</el-radio>\n            <el-radio :label=\"false\">否</el-radio>\n          </el-radio-group>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"btn-x\">\n      <el-button @click=\"$emit('close')\">取 消</el-button>\n      <el-button :loading=\"btnLoading\" type=\"primary\" @click=\"handleSubmit\">确 定\n      </el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport { GetUserList } from '@/api/sys'\nimport {\n  AddWorkingProcess,\n  GetFactoryPeoplelist,\n  GetCheckGroupList,\n  GetWorkingTeams\n} from '@/api/PRO/technology-lib'\nimport { mapGetters } from 'vuex'\nimport { GetDictionaryDetailListByCode } from '@/api/sys'\nexport default {\n  props: {\n    type: {\n      type: String,\n      default: ''\n    },\n    rowInfo: {\n      type: Object,\n      default() {\n        return {}\n      }\n    },\n    totalWorkloadProportion: {\n      type: Number,\n      default: 0\n    }\n  },\n  data() {\n    return {\n      checkList: [],\n      btnLoading: false,\n      hiddenPart: false,\n\n      form: {\n        Code: '',\n        Name: '',\n        Bom_Level: '',\n        Month_Avg_Load: '',\n        Coordinate_UserId: '',\n        Sort: undefined,\n        Is_Enable: true,\n        Is_External: false,\n        Is_Nest: false,\n        Is_Need_Check: true,\n        Is_Self_Check: true,\n        Is_Inter_Check: true,\n        Is_Pick_Material: false,\n        Is_Need_TC: true,\n        Is_Welding_Assembling: false,\n        Is_Cutting: false,\n        TC_Check_UserId: '',\n        Is_Need_ZL: false,\n        ZL_Check_UserId: '',\n        Show_Model: false,\n\n        Check_Style: '0',\n\n        Working_Team_Ids: [],\n        Remark: '',\n        Workload_Proportion: ''\n      },\n      ZL_Check_UserIds: [],\n      TC_Check_UserIds: [],\n      CheckChange: true,\n      userOptions: [],\n      optionsUserList: [],\n      optionsGroupList: [],\n      optionsWorkingTeamsList: [],\n      rules: {\n        Code: [\n          { required: true, message: '请输入代号', trigger: 'blur' },\n          { max: 30, message: '长度在 30 个字符内', trigger: 'blur' }\n        ],\n        Name: [\n          { required: true, message: '请输入名称', trigger: 'blur' },\n          { max: 30, message: '长度在 30 个字符内', trigger: 'blur' }\n        ],\n        Bom_Level: [{ required: true, message: '请选择类型', trigger: 'change' }],\n        Sort: [{ required: true, message: '请输入', trigger: 'blur' }],\n        Is_Need_Check: [\n          { required: true, message: '请选择是否质检', trigger: 'change' }\n        ]\n      },\n      Workload_Proportion: 0,\n      bomList: [],\n      comName: '',\n      partName: ''\n    }\n  },\n  computed: {\n    ...mapGetters('tenant', ['isVersionFour'])\n  },\n  async created() {\n    const { comName, partName, list } = await GetBOMInfo()\n    this.comName = comName\n    this.partName = partName\n    this.bomList = list\n  },\n  mounted() {\n    this.getUserList()\n    this.getFactoryPeoplelist()\n    // this.getCheckGroupList();\n    this.getWorkingTeamsList()\n    this.type === 'edit' && this.initForm()\n  },\n  methods: {\n    initForm() {\n      const { Is_Nest, ...others } = this.rowInfo\n      this.form = Object.assign({}, others, { Is_Nest: !!Is_Nest })\n      this.form.Bom_Level = String(this.form.Bom_Level)\n      this.Workload_Proportion = this.rowInfo.Workload_Proportion\n      //  if(this.form.Type==2){\n      //   this.form.Types = '0'\n      //  }else if(this.form.Type==3){\n      //   let Types = this.radioList.find(v => ['1', '2','3'].includes(v.Code))?.Code\n      //   console.log('Types', Types)\n      //   console.log('this.radioList', this.radioList)\n      //   this.form.Types = Types\n      //  }else if(this.form.Type==1){\n      //   this.form.Types = '-1'\n      //  }\n      console.log('this.form', this.form)\n\n      // 处理历史数据多选问题\n      // if (this.form.Is_Need_Check) {\n      //   if (this.form.Check_Style === '1') {\n\n      //   } else {\n      //     this.CheckChange = !!this.form.Is_Need_TC\n      //     if (this.form.Is_Need_ZL && this.form.Is_Need_TC) {\n      //       this.form.Is_Need_TC = true\n      //       this.form.Is_Need_ZL = false\n      //     }\n      //   }\n      // }\n      this.ZL_Check_UserIds = this.form.ZL_Check_UserId\n        ? this.form.ZL_Check_UserId.split(',')\n        : []\n      this.TC_Check_UserIds = this.form.TC_Check_UserId\n        ? this.form.TC_Check_UserId.split(',')\n        : []\n    },\n    // 是否自检\n    radioSelfCheck(val) { },\n    // 是否互检\n    radioInterCheck(val) { },\n    // 获取设备类型\n    async getDictionaryDetailListByCode() {\n      await GetDictionaryDetailListByCode({ dictionaryCode: 'deviceType' }).then((res) => {\n        if (res.IsSucceed) {\n          this.deviceTypeList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n      console.log(' this.optionsGroupList', this.optionsGroupList)\n    },\n    getUserList() {\n      GetUserList({}).then((res) => {\n        if (res.IsSucceed) {\n          this.userOptions = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    getFactoryPeoplelist() {\n      GetFactoryPeoplelist({}).then((res) => {\n        if (res.IsSucceed) {\n          this.optionsUserList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    async getCheckGroupList() {\n      await GetCheckGroupList({}).then((res) => {\n        if (res.IsSucceed) {\n          this.optionsGroupList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n      console.log(' this.optionsGroupList', this.optionsGroupList)\n    },\n    getWorkingTeamsList() {\n      GetWorkingTeams({}).then((res) => {\n        if (res.IsSucceed) {\n          this.optionsWorkingTeamsList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    // 选择专检方式 抽检、全检\n    radioCheckStyleChange(val) {\n      // if (val === '0') {\n      //   this.form.Is_Need_TC = true\n      //   this.form.Is_Need_ZL = false\n      // }\n      this.ZL_Check_UserIds = []\n      this.TC_Check_UserIds = []\n      this.form.ZL_Check_UserId = ''\n      this.form.TC_Check_UserId = ''\n    },\n    // 是否专检\n    radioChange(val) {\n      if (val === false && this.type === 'add') {\n        this.form.checkChange = false\n        this.form.Is_Need_TC = false\n        this.form.Is_Need_ZL = false\n        this.TC_Check_UserIds = []\n        this.ZL_Check_UserIds = []\n        this.form.ZL_Check_UserId = ''\n        this.form.TC_Check_UserId = ''\n        this.form.Check_Style = ''\n      } else {\n        // this.form.checkChange = true\n        // this.form.Is_Need_TC = true\n        // this.form.Is_Need_ZL = false\n        // this.CheckChange = !!this.form.Is_Need_TC\n        this.form.Check_Style = '0'\n      }\n    },\n    // 选择BOM层级\n    changeType(val) {\n      // const Code = val\n      // console.log(Code, 'Code');\n      // if (Code === '-1') {\n      //   this.form.Type = 1\n      // } else if (Code === '0') {\n      //   this.form.Type = 2\n      // } else if (Code === '1' || Code === '3'|| Code === '2') {\n      //   this.form.Type = 3\n      // }\n      // if (this.form.Type === 1 || this.form.Type === 3) {\n      //   this.form.Is_Cutting = undefined\n      // } else if (this.form.Type === 2) {\n      //   this.form.Is_Welding_Assembling = undefined\n      // }\n    },\n    typeChange() {\n      this.form.Task_Model = ''\n    },\n    changeTc(val) {\n      console.log(val)\n      this.form.TC_Check_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i === val.length - 1) {\n          this.form.TC_Check_UserId += val[i]\n        } else {\n          this.form.TC_Check_UserId += val[i] + ','\n        }\n      }\n    },\n    changeZL(val) {\n      console.log(val)\n      this.form.ZL_Check_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i === val.length - 1) {\n          this.form.ZL_Check_UserId += val[i]\n        } else {\n          this.form.ZL_Check_UserId += val[i] + ','\n        }\n      }\n    },\n    checkboxChange(val, type) {\n      if (type === 1) {\n        if (!val) {\n          this.TC_Check_UserIds = []\n        }\n      }\n      if (type === 2) {\n        if (!val) {\n          this.ZL_Check_UserIds = []\n        }\n      }\n    },\n    handleSubmit() {\n      // delete this.form.Types\n      console.log(this.form, 'this.form')\n      this.$refs.form.validate((valid) => {\n        if (!valid) return\n        this.btnLoading = true\n        const uItems = this.optionsUserList.find(\n          (v) => v.Id === this.form.Coordinate_UserId\n        )\n        if (uItems) {\n          this.form.Coordinate_UserName = uItems.Display_Name\n        }\n        if (this.form.Is_Need_Check) { // 如果需要专检\n          if (this.form.Is_Need_ZL === false && this.form.Is_Need_TC === false) {\n            this.$message.error('请选择质检类型')\n            this.btnLoading = false\n            return\n          }\n        } else { // 如果不需要专检，则不需要专检方式、专检类型、专检人员  后续需要互检逻辑迭代\n          this.form.checkChange = false\n          this.form.Is_Need_TC = false\n          this.form.Is_Need_ZL = false\n          this.TC_Check_UserIds = []\n          this.ZL_Check_UserIds = []\n          this.form.ZL_Check_UserId = ''\n          this.form.TC_Check_UserId = ''\n          this.form.Check_Style = null\n        }\n        const ZL = this.form.Is_Need_ZL ? this.form.ZL_Check_UserId : ''\n        const TC = this.form.Is_Need_TC ? this.form.TC_Check_UserId : ''\n        if (this.form.Is_Need_ZL && (ZL ?? '') === '') {\n          this.$message.error('请选择质检员')\n          this.btnLoading = false\n          return\n        }\n        if (this.form.Is_Need_TC && (TC ?? '') === '') {\n          this.$message.error('请选择探伤员')\n          this.btnLoading = false\n          return\n        }\n\n        AddWorkingProcess({\n          ...this.form,\n          ZL_Check_UserId: ZL,\n          TC_Check_UserId: TC\n        }).then((res) => {\n          if (res.IsSucceed) {\n            this.$message({\n              message: '保存成功',\n              type: 'success'\n            })\n            this.$emit('refresh')\n            this.$emit('close')\n          } else {\n            this.$message({\n              message: res.Message,\n              type: 'error'\n            })\n          }\n          this.btnLoading = false\n        })\n      })\n    },\n\n    codeChange(e) {\n      return e.replace(/[^a-zA-Z0-9]/g, '')\n    },\n\n    handlePercentageInput(value) {\n      // 如果输入为空，直接返回\n      if (value === '' || value === null || value === undefined) {\n        this.form.Workload_Proportion = ''\n        return\n      }\n\n      // 转换为字符串进行处理\n      let inputValue = String(value)\n\n      // 只允许数字和一个小数点，移除其他字符（包括负号）\n      inputValue = inputValue.replace(/[^0-9.]/g, '')\n\n      // 确保只有一个小数点\n      const dotCount = (inputValue.match(/\\./g) || []).length\n      if (dotCount > 1) {\n        // 如果有多个小数点，只保留第一个\n        const firstDotIndex = inputValue.indexOf('.')\n        inputValue = inputValue.substring(0, firstDotIndex + 1) + inputValue.substring(firstDotIndex + 1).replace(/\\./g, '')\n      }\n\n      // 如果只是小数点，设置为空\n      if (inputValue === '.') {\n        this.form.Workload_Proportion = ''\n        return\n      }\n\n      // 如果处理后为空字符串，设置为空\n      if (inputValue === '') {\n        this.form.Workload_Proportion = ''\n        return\n      }\n\n      // 限制小数位数最多2位\n      if (inputValue.includes('.')) {\n        const parts = inputValue.split('.')\n        if (parts[1] && parts[1].length > 2) {\n          inputValue = parts[0] + '.' + parts[1].substring(0, 2)\n        }\n      }\n\n      // 转换为数字进行范围检查\n      const numValue = parseFloat(inputValue)\n\n      // 如果不是有效数字，设置为空\n      if (isNaN(numValue)) {\n        this.form.Workload_Proportion = ''\n        return\n      }\n\n      // 根据 totalWorkloadProportion 计算最大值\n      let maxValue = 100\n      let currentTotal = 0\n\n      if (this.totalWorkloadProportion) {\n        // 确保所有数值都是浮点数并保留2位小数进行计算\n        const total = parseFloat(this.totalWorkloadProportion.toFixed(2))\n        const current = parseFloat((this.Workload_Proportion || 0).toString())\n\n        // 计算差值并保留2位小数\n        const difference = total - current\n        currentTotal = parseFloat(difference.toFixed(2))\n\n        // 验证数学一致性：确保 currentTotal + current = total\n        const verification = parseFloat((currentTotal + current).toFixed(2))\n        if (verification !== total) {\n          // 如果不一致，调整 currentTotal 以保证一致性\n          currentTotal = parseFloat((total - current).toFixed(2))\n        }\n      }\n\n      if (currentTotal === 100) {\n        // 如果总和已经是100，最大只能输入0\n        maxValue = 0\n      } else if (currentTotal > 0 && currentTotal < 100) {\n        // 如果总和在0-100之间，最大值是100减去当前总和\n        maxValue = 100 - currentTotal\n      }\n      // 如果总和是0或空，最大值保持100\n\n      // 限制范围在 0-maxValue 之间\n      if (numValue < 0) {\n        this.form.Workload_Proportion = '0'\n      } else if (numValue > maxValue) {\n        // 保留2位小数\n        this.form.Workload_Proportion = maxValue.toFixed(2).replace(/\\.?0+$/, '')\n      } else {\n        // 保持原始输入格式（包括小数点），但确保不超过2位小数\n        this.form.Workload_Proportion = inputValue\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/mixin.scss\";\n\n.btn-del {\n  margin-left: -100px;\n}\n\n.customRadioClass {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.checkboxFlex {\n  display: flex;\n  align-items: center;\n}\n\n.form-wrapper {\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  min-height: 40vh;\n\n  .form-x {\n    max-height: 70vh;\n    overflow: auto;\n    padding-right: 16px;\n    @include scrollBar;\n  }\n\n  .btn-x {\n    padding-top: 16px;\n    text-align: right;\n  }\n}\n</style>\n"]}]}
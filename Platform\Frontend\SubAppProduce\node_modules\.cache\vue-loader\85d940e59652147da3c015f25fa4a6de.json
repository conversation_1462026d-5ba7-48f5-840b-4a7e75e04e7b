{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\Add.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\Add.vue", "mtime": 1757581469561}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IEdldEJPTUluZm8gfSBmcm9tICdAL3ZpZXdzL1BSTy9ib20tc2V0dGluZy91dGlscycKaW1wb3J0IHsgR2V0VXNlckxpc3QgfSBmcm9tICdAL2FwaS9zeXMnCmltcG9ydCB7CiAgQWRkV29ya2luZ1Byb2Nlc3MsCiAgR2V0RmFjdG9yeVBlb3BsZWxpc3QsCiAgR2V0Q2hlY2tHcm91cExpc3QsCiAgR2V0V29ya2luZ1RlYW1zCn0gZnJvbSAnQC9hcGkvUFJPL3RlY2hub2xvZ3ktbGliJwppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCcKaW1wb3J0IHsgR2V0RGljdGlvbmFyeURldGFpbExpc3RCeUNvZGUgfSBmcm9tICdAL2FwaS9zeXMnCmV4cG9ydCBkZWZhdWx0IHsKICBwcm9wczogewogICAgdHlwZTogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcnCiAgICB9LAogICAgcm93SW5mbzogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIHt9CiAgICAgIH0KICAgIH0sCiAgICB3b3JrbG9hZFByb3BvcnRpb25BbGw6IHsKICAgICAgdHlwZTogTnVtYmVyLAogICAgICBkZWZhdWx0OiAwCiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgY2hlY2tMaXN0OiBbXSwKICAgICAgYnRuTG9hZGluZzogZmFsc2UsCiAgICAgIGhpZGRlblBhcnQ6IGZhbHNlLAoKICAgICAgZm9ybTogewogICAgICAgIENvZGU6ICcnLAogICAgICAgIE5hbWU6ICcnLAogICAgICAgIEJvbV9MZXZlbDogJycsCiAgICAgICAgTW9udGhfQXZnX0xvYWQ6ICcnLAogICAgICAgIENvb3JkaW5hdGVfVXNlcklkOiAnJywKICAgICAgICBTb3J0OiB1bmRlZmluZWQsCiAgICAgICAgSXNfRW5hYmxlOiB0cnVlLAogICAgICAgIElzX0V4dGVybmFsOiBmYWxzZSwKICAgICAgICBJc19OZXN0OiBmYWxzZSwKICAgICAgICBJc19OZWVkX0NoZWNrOiB0cnVlLAogICAgICAgIElzX1NlbGZfQ2hlY2s6IHRydWUsCiAgICAgICAgSXNfSW50ZXJfQ2hlY2s6IHRydWUsCiAgICAgICAgSXNfUGlja19NYXRlcmlhbDogZmFsc2UsCiAgICAgICAgSXNfTmVlZF9UQzogdHJ1ZSwKICAgICAgICBJc19XZWxkaW5nX0Fzc2VtYmxpbmc6IGZhbHNlLAogICAgICAgIElzX0N1dHRpbmc6IGZhbHNlLAogICAgICAgIFRDX0NoZWNrX1VzZXJJZDogJycsCiAgICAgICAgSXNfTmVlZF9aTDogZmFsc2UsCiAgICAgICAgWkxfQ2hlY2tfVXNlcklkOiAnJywKICAgICAgICBTaG93X01vZGVsOiBmYWxzZSwKCiAgICAgICAgQ2hlY2tfU3R5bGU6ICcwJywKCiAgICAgICAgV29ya2luZ19UZWFtX0lkczogW10sCiAgICAgICAgUmVtYXJrOiAnJywKICAgICAgICBXb3JrbG9hZF9Qcm9wb3J0aW9uOiAnJwogICAgICB9LAogICAgICBaTF9DaGVja19Vc2VySWRzOiBbXSwKICAgICAgVENfQ2hlY2tfVXNlcklkczogW10sCiAgICAgIENoZWNrQ2hhbmdlOiB0cnVlLAogICAgICB1c2VyT3B0aW9uczogW10sCiAgICAgIG9wdGlvbnNVc2VyTGlzdDogW10sCiAgICAgIG9wdGlvbnNHcm91cExpc3Q6IFtdLAogICAgICBvcHRpb25zV29ya2luZ1RlYW1zTGlzdDogW10sCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgQ29kZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeS7o+WPtycsIHRyaWdnZXI6ICdibHVyJyB9LAogICAgICAgICAgeyBtYXg6IDMwLCBtZXNzYWdlOiAn6ZW/5bqm5ZyoIDMwIOS4quWtl+espuWGhScsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXSwKICAgICAgICBOYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5ZCN56ewJywgdHJpZ2dlcjogJ2JsdXInIH0sCiAgICAgICAgICB7IG1heDogMzAsIG1lc3NhZ2U6ICfplb/luqblnKggMzAg5Liq5a2X56ym5YaFJywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICAgIEJvbV9MZXZlbDogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nnsbvlnosnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9XSwKICAgICAgICBTb3J0OiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpScsIHRyaWdnZXI6ICdibHVyJyB9XSwKICAgICAgICBJc19OZWVkX0NoZWNrOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup5piv5ZCm6LSo5qOAJywgdHJpZ2dlcjogJ2NoYW5nZScgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgYm9tTGlzdDogW10sCiAgICAgIGNvbU5hbWU6ICcnLAogICAgICBwYXJ0TmFtZTogJycKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICAuLi5tYXBHZXR0ZXJzKCd0ZW5hbnQnLCBbJ2lzVmVyc2lvbkZvdXInXSkKICB9LAogIGFzeW5jIGNyZWF0ZWQoKSB7CiAgICBjb25zdCB7IGNvbU5hbWUsIHBhcnROYW1lLCBsaXN0IH0gPSBhd2FpdCBHZXRCT01JbmZvKCkKICAgIHRoaXMuY29tTmFtZSA9IGNvbU5hbWUKICAgIHRoaXMucGFydE5hbWUgPSBwYXJ0TmFtZQogICAgdGhpcy5ib21MaXN0ID0gbGlzdAogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMuZ2V0VXNlckxpc3QoKQogICAgdGhpcy5nZXRGYWN0b3J5UGVvcGxlbGlzdCgpCiAgICAvLyB0aGlzLmdldENoZWNrR3JvdXBMaXN0KCk7CiAgICB0aGlzLmdldFdvcmtpbmdUZWFtc0xpc3QoKQogICAgdGhpcy50eXBlID09PSAnZWRpdCcgJiYgdGhpcy5pbml0Rm9ybSgpCiAgfSwKICBtZXRob2RzOiB7CiAgICBpbml0Rm9ybSgpIHsKICAgICAgY29uc3QgeyBJc19OZXN0LCAuLi5vdGhlcnMgfSA9IHRoaXMucm93SW5mbwogICAgICB0aGlzLmZvcm0gPSBPYmplY3QuYXNzaWduKHt9LCBvdGhlcnMsIHsgSXNfTmVzdDogISFJc19OZXN0IH0pCiAgICAgIHRoaXMuZm9ybS5Cb21fTGV2ZWwgPSBTdHJpbmcodGhpcy5mb3JtLkJvbV9MZXZlbCkKICAgICAgLy8gIGlmKHRoaXMuZm9ybS5UeXBlPT0yKXsKICAgICAgLy8gICB0aGlzLmZvcm0uVHlwZXMgPSAnMCcKICAgICAgLy8gIH1lbHNlIGlmKHRoaXMuZm9ybS5UeXBlPT0zKXsKICAgICAgLy8gICBsZXQgVHlwZXMgPSB0aGlzLnJhZGlvTGlzdC5maW5kKHYgPT4gWycxJywgJzInLCczJ10uaW5jbHVkZXModi5Db2RlKSk/LkNvZGUKICAgICAgLy8gICBjb25zb2xlLmxvZygnVHlwZXMnLCBUeXBlcykKICAgICAgLy8gICBjb25zb2xlLmxvZygndGhpcy5yYWRpb0xpc3QnLCB0aGlzLnJhZGlvTGlzdCkKICAgICAgLy8gICB0aGlzLmZvcm0uVHlwZXMgPSBUeXBlcwogICAgICAvLyAgfWVsc2UgaWYodGhpcy5mb3JtLlR5cGU9PTEpewogICAgICAvLyAgIHRoaXMuZm9ybS5UeXBlcyA9ICctMScKICAgICAgLy8gIH0KICAgICAgY29uc29sZS5sb2coJ3RoaXMuZm9ybScsIHRoaXMuZm9ybSkKCiAgICAgIC8vIOWkhOeQhuWOhuWPsuaVsOaNruWkmumAiemXrumimAogICAgICAvLyBpZiAodGhpcy5mb3JtLklzX05lZWRfQ2hlY2spIHsKICAgICAgLy8gICBpZiAodGhpcy5mb3JtLkNoZWNrX1N0eWxlID09PSAnMScpIHsKCiAgICAgIC8vICAgfSBlbHNlIHsKICAgICAgLy8gICAgIHRoaXMuQ2hlY2tDaGFuZ2UgPSAhIXRoaXMuZm9ybS5Jc19OZWVkX1RDCiAgICAgIC8vICAgICBpZiAodGhpcy5mb3JtLklzX05lZWRfWkwgJiYgdGhpcy5mb3JtLklzX05lZWRfVEMpIHsKICAgICAgLy8gICAgICAgdGhpcy5mb3JtLklzX05lZWRfVEMgPSB0cnVlCiAgICAgIC8vICAgICAgIHRoaXMuZm9ybS5Jc19OZWVkX1pMID0gZmFsc2UKICAgICAgLy8gICAgIH0KICAgICAgLy8gICB9CiAgICAgIC8vIH0KICAgICAgdGhpcy5aTF9DaGVja19Vc2VySWRzID0gdGhpcy5mb3JtLlpMX0NoZWNrX1VzZXJJZAogICAgICAgID8gdGhpcy5mb3JtLlpMX0NoZWNrX1VzZXJJZC5zcGxpdCgnLCcpCiAgICAgICAgOiBbXQogICAgICB0aGlzLlRDX0NoZWNrX1VzZXJJZHMgPSB0aGlzLmZvcm0uVENfQ2hlY2tfVXNlcklkCiAgICAgICAgPyB0aGlzLmZvcm0uVENfQ2hlY2tfVXNlcklkLnNwbGl0KCcsJykKICAgICAgICA6IFtdCiAgICB9LAogICAgLy8g5piv5ZCm6Ieq5qOACiAgICByYWRpb1NlbGZDaGVjayh2YWwpIHsgfSwKICAgIC8vIOaYr+WQpuS6kuajgAogICAgcmFkaW9JbnRlckNoZWNrKHZhbCkgeyB9LAogICAgLy8g6I635Y+W6K6+5aSH57G75Z6LCiAgICBhc3luYyBnZXREaWN0aW9uYXJ5RGV0YWlsTGlzdEJ5Q29kZSgpIHsKICAgICAgYXdhaXQgR2V0RGljdGlvbmFyeURldGFpbExpc3RCeUNvZGUoeyBkaWN0aW9uYXJ5Q29kZTogJ2RldmljZVR5cGUnIH0pLnRoZW4oKHJlcykgPT4gewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICB0aGlzLmRldmljZVR5cGVMaXN0ID0gcmVzLkRhdGEKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgICAgY29uc29sZS5sb2coJyB0aGlzLm9wdGlvbnNHcm91cExpc3QnLCB0aGlzLm9wdGlvbnNHcm91cExpc3QpCiAgICB9LAogICAgZ2V0VXNlckxpc3QoKSB7CiAgICAgIEdldFVzZXJMaXN0KHt9KS50aGVuKChyZXMpID0+IHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgdGhpcy51c2VyT3B0aW9ucyA9IHJlcy5EYXRhCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgZ2V0RmFjdG9yeVBlb3BsZWxpc3QoKSB7CiAgICAgIEdldEZhY3RvcnlQZW9wbGVsaXN0KHt9KS50aGVuKChyZXMpID0+IHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgdGhpcy5vcHRpb25zVXNlckxpc3QgPSByZXMuRGF0YQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGFzeW5jIGdldENoZWNrR3JvdXBMaXN0KCkgewogICAgICBhd2FpdCBHZXRDaGVja0dyb3VwTGlzdCh7fSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIHRoaXMub3B0aW9uc0dyb3VwTGlzdCA9IHJlcy5EYXRhCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICAgIGNvbnNvbGUubG9nKCcgdGhpcy5vcHRpb25zR3JvdXBMaXN0JywgdGhpcy5vcHRpb25zR3JvdXBMaXN0KQogICAgfSwKICAgIGdldFdvcmtpbmdUZWFtc0xpc3QoKSB7CiAgICAgIEdldFdvcmtpbmdUZWFtcyh7fSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIHRoaXMub3B0aW9uc1dvcmtpbmdUZWFtc0xpc3QgPSByZXMuRGF0YQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIC8vIOmAieaLqeS4k+ajgOaWueW8jyDmir3mo4DjgIHlhajmo4AKICAgIHJhZGlvQ2hlY2tTdHlsZUNoYW5nZSh2YWwpIHsKICAgICAgLy8gaWYgKHZhbCA9PT0gJzAnKSB7CiAgICAgIC8vICAgdGhpcy5mb3JtLklzX05lZWRfVEMgPSB0cnVlCiAgICAgIC8vICAgdGhpcy5mb3JtLklzX05lZWRfWkwgPSBmYWxzZQogICAgICAvLyB9CiAgICAgIHRoaXMuWkxfQ2hlY2tfVXNlcklkcyA9IFtdCiAgICAgIHRoaXMuVENfQ2hlY2tfVXNlcklkcyA9IFtdCiAgICAgIHRoaXMuZm9ybS5aTF9DaGVja19Vc2VySWQgPSAnJwogICAgICB0aGlzLmZvcm0uVENfQ2hlY2tfVXNlcklkID0gJycKICAgIH0sCiAgICAvLyDmmK/lkKbkuJPmo4AKICAgIHJhZGlvQ2hhbmdlKHZhbCkgewogICAgICBpZiAodmFsID09PSBmYWxzZSkgewogICAgICAgIHRoaXMuZm9ybS5jaGVja0NoYW5nZSA9IGZhbHNlCiAgICAgICAgdGhpcy5mb3JtLklzX05lZWRfVEMgPSBmYWxzZQogICAgICAgIHRoaXMuZm9ybS5Jc19OZWVkX1pMID0gZmFsc2UKICAgICAgICB0aGlzLlRDX0NoZWNrX1VzZXJJZHMgPSBbXQogICAgICAgIHRoaXMuWkxfQ2hlY2tfVXNlcklkcyA9IFtdCiAgICAgICAgdGhpcy5mb3JtLlpMX0NoZWNrX1VzZXJJZCA9ICcnCiAgICAgICAgdGhpcy5mb3JtLlRDX0NoZWNrX1VzZXJJZCA9ICcnCiAgICAgICAgdGhpcy5mb3JtLkNoZWNrX1N0eWxlID0gJycKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyB0aGlzLmZvcm0uY2hlY2tDaGFuZ2UgPSB0cnVlCiAgICAgICAgLy8gdGhpcy5mb3JtLklzX05lZWRfVEMgPSB0cnVlCiAgICAgICAgLy8gdGhpcy5mb3JtLklzX05lZWRfWkwgPSBmYWxzZQogICAgICAgIC8vIHRoaXMuQ2hlY2tDaGFuZ2UgPSAhIXRoaXMuZm9ybS5Jc19OZWVkX1RDCiAgICAgICAgdGhpcy5mb3JtLkNoZWNrX1N0eWxlID0gJzAnCiAgICAgIH0KICAgIH0sCiAgICAvLyDpgInmi6lCT03lsYLnuqcKICAgIGNoYW5nZVR5cGUodmFsKSB7CiAgICAgIC8vIGNvbnN0IENvZGUgPSB2YWwKICAgICAgLy8gY29uc29sZS5sb2coQ29kZSwgJ0NvZGUnKTsKICAgICAgLy8gaWYgKENvZGUgPT09ICctMScpIHsKICAgICAgLy8gICB0aGlzLmZvcm0uVHlwZSA9IDEKICAgICAgLy8gfSBlbHNlIGlmIChDb2RlID09PSAnMCcpIHsKICAgICAgLy8gICB0aGlzLmZvcm0uVHlwZSA9IDIKICAgICAgLy8gfSBlbHNlIGlmIChDb2RlID09PSAnMScgfHwgQ29kZSA9PT0gJzMnfHwgQ29kZSA9PT0gJzInKSB7CiAgICAgIC8vICAgdGhpcy5mb3JtLlR5cGUgPSAzCiAgICAgIC8vIH0KICAgICAgLy8gaWYgKHRoaXMuZm9ybS5UeXBlID09PSAxIHx8IHRoaXMuZm9ybS5UeXBlID09PSAzKSB7CiAgICAgIC8vICAgdGhpcy5mb3JtLklzX0N1dHRpbmcgPSB1bmRlZmluZWQKICAgICAgLy8gfSBlbHNlIGlmICh0aGlzLmZvcm0uVHlwZSA9PT0gMikgewogICAgICAvLyAgIHRoaXMuZm9ybS5Jc19XZWxkaW5nX0Fzc2VtYmxpbmcgPSB1bmRlZmluZWQKICAgICAgLy8gfQogICAgfSwKICAgIHR5cGVDaGFuZ2UoKSB7CiAgICAgIHRoaXMuZm9ybS5UYXNrX01vZGVsID0gJycKICAgIH0sCiAgICBjaGFuZ2VUYyh2YWwpIHsKICAgICAgY29uc29sZS5sb2codmFsKQogICAgICB0aGlzLmZvcm0uVENfQ2hlY2tfVXNlcklkID0gJycKICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB2YWwubGVuZ3RoOyBpKyspIHsKICAgICAgICBpZiAoaSA9PT0gdmFsLmxlbmd0aCAtIDEpIHsKICAgICAgICAgIHRoaXMuZm9ybS5UQ19DaGVja19Vc2VySWQgKz0gdmFsW2ldCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuZm9ybS5UQ19DaGVja19Vc2VySWQgKz0gdmFsW2ldICsgJywnCiAgICAgICAgfQogICAgICB9CiAgICAgIGNvbnNvbGUubG9nKHRoaXMuZm9ybS5UQ19DaGVja19Vc2VySWQsICd0aGlzLmZvcm0uVENfQ2hlY2tfVXNlcklkICcpCiAgICB9LAogICAgY2hhbmdlWkwodmFsKSB7CiAgICAgIGNvbnNvbGUubG9nKHZhbCkKICAgICAgdGhpcy5mb3JtLlpMX0NoZWNrX1VzZXJJZCA9ICcnCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdmFsLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgaWYgKGkgPT09IHZhbC5sZW5ndGggLSAxKSB7CiAgICAgICAgICB0aGlzLmZvcm0uWkxfQ2hlY2tfVXNlcklkICs9IHZhbFtpXQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLmZvcm0uWkxfQ2hlY2tfVXNlcklkICs9IHZhbFtpXSArICcsJwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIGNoZWNrYm94Q2hhbmdlKHZhbCwgdHlwZSkgewogICAgICBpZiAodHlwZSA9PT0gMSkgewogICAgICAgIGlmICghdmFsKSB7CiAgICAgICAgICB0aGlzLlRDX0NoZWNrX1VzZXJJZHMgPSBbXQogICAgICAgIH0KICAgICAgfQogICAgICBpZiAodHlwZSA9PT0gMikgewogICAgICAgIGlmICghdmFsKSB7CiAgICAgICAgICB0aGlzLlpMX0NoZWNrX1VzZXJJZHMgPSBbXQogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIGhhbmRsZVN1Ym1pdCgpIHsKICAgICAgLy8gZGVsZXRlIHRoaXMuZm9ybS5UeXBlcwogICAgICBjb25zb2xlLmxvZyh0aGlzLmZvcm0sICd0aGlzLmZvcm0nKQogICAgICB0aGlzLiRyZWZzLmZvcm0udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKCF2YWxpZCkgcmV0dXJuCiAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gdHJ1ZQogICAgICAgIGNvbnN0IHVJdGVtcyA9IHRoaXMub3B0aW9uc1VzZXJMaXN0LmZpbmQoCiAgICAgICAgICAodikgPT4gdi5JZCA9PT0gdGhpcy5mb3JtLkNvb3JkaW5hdGVfVXNlcklkCiAgICAgICAgKQogICAgICAgIGlmICh1SXRlbXMpIHsKICAgICAgICAgIHRoaXMuZm9ybS5Db29yZGluYXRlX1VzZXJOYW1lID0gdUl0ZW1zLkRpc3BsYXlfTmFtZQogICAgICAgIH0KICAgICAgICBpZiAodGhpcy5mb3JtLklzX05lZWRfQ2hlY2spIHsKICAgICAgICAgIGlmICh0aGlzLmZvcm0uSXNfTmVlZF9aTCA9PT0gZmFsc2UgJiYgdGhpcy5mb3JtLklzX05lZWRfVEMgPT09IGZhbHNlKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+mAieaLqei0qOajgOexu+WeiycpCiAgICAgICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlCiAgICAgICAgICAgIHJldHVybgogICAgICAgICAgfQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLmZvcm0uQ2hlY2tfU3R5bGUgPSBudWxsCiAgICAgICAgICB0aGlzLmZvcm0uQ2hlY2tfR3JvdXBfTGlzdCA9IFtdCiAgICAgICAgfQogICAgICAgIGNvbnN0IFpMID0gdGhpcy5mb3JtLklzX05lZWRfWkwgPyB0aGlzLmZvcm0uWkxfQ2hlY2tfVXNlcklkIDogJycKICAgICAgICBjb25zdCBUQyA9IHRoaXMuZm9ybS5Jc19OZWVkX1RDID8gdGhpcy5mb3JtLlRDX0NoZWNrX1VzZXJJZCA6ICcnCiAgICAgICAgaWYgKHRoaXMuZm9ybS5Jc19OZWVkX1pMICYmIChaTCA/PyAnJykgPT09ICcnKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfor7fpgInmi6notKjmo4DlkZgnKQogICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UKICAgICAgICAgIHJldHVybgogICAgICAgIH0KICAgICAgICBpZiAodGhpcy5mb3JtLklzX05lZWRfVEMgJiYgKFRDID8/ICcnKSA9PT0gJycpIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+mAieaLqeaOouS8pOWRmCcpCiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQogICAgICAgICAgcmV0dXJuCiAgICAgICAgfQoKICAgICAgICBBZGRXb3JraW5nUHJvY2Vzcyh7CiAgICAgICAgICAuLi50aGlzLmZvcm0sCiAgICAgICAgICBaTF9DaGVja19Vc2VySWQ6IFpMLAogICAgICAgICAgVENfQ2hlY2tfVXNlcklkOiBUQwogICAgICAgIH0pLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgbWVzc2FnZTogJ+S/neWtmOaIkOWKnycsCiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIHRoaXMuJGVtaXQoJ3JlZnJlc2gnKQogICAgICAgICAgICB0aGlzLiRlbWl0KCdjbG9zZScpCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICAgIH0pCiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQogICAgICAgIH0pCiAgICAgIH0pCiAgICB9LAoKICAgIGNvZGVDaGFuZ2UoZSkgewogICAgICByZXR1cm4gZS5yZXBsYWNlKC9bXmEtekEtWjAtOV0vZywgJycpCiAgICB9LAoKICAgIGhhbmRsZVBlcmNlbnRhZ2VJbnB1dCh2YWx1ZSkgewogICAgICAvLyDlpoLmnpzovpPlhaXkuLrnqbrvvIznm7TmjqXov5Tlm54KICAgICAgaWYgKHZhbHVlID09PSAnJyB8fCB2YWx1ZSA9PT0gbnVsbCB8fCB2YWx1ZSA9PT0gdW5kZWZpbmVkKSB7CiAgICAgICAgdGhpcy5mb3JtLldvcmtsb2FkX1Byb3BvcnRpb24gPSAnJwogICAgICAgIHJldHVybgogICAgICB9CgogICAgICAvLyDovazmjaLkuLrlrZfnrKbkuLLov5vooYzlpITnkIYKICAgICAgbGV0IGlucHV0VmFsdWUgPSBTdHJpbmcodmFsdWUpCgogICAgICAvLyDlj6rlhYHorrjmlbDlrZflkozkuIDkuKrlsI/mlbDngrnvvIznp7vpmaTlhbbku5blrZfnrKbvvIjljIXmi6zotJ/lj7fvvIkKICAgICAgaW5wdXRWYWx1ZSA9IGlucHV0VmFsdWUucmVwbGFjZSgvW14wLTkuXS9nLCAnJykKCiAgICAgIC8vIOehruS/neWPquacieS4gOS4quWwj+aVsOeCuQogICAgICBjb25zdCBkb3RDb3VudCA9IChpbnB1dFZhbHVlLm1hdGNoKC9cLi9nKSB8fCBbXSkubGVuZ3RoCiAgICAgIGlmIChkb3RDb3VudCA+IDEpIHsKICAgICAgICAvLyDlpoLmnpzmnInlpJrkuKrlsI/mlbDngrnvvIzlj6rkv53nlZnnrKzkuIDkuKoKICAgICAgICBjb25zdCBmaXJzdERvdEluZGV4ID0gaW5wdXRWYWx1ZS5pbmRleE9mKCcuJykKICAgICAgICBpbnB1dFZhbHVlID0gaW5wdXRWYWx1ZS5zdWJzdHJpbmcoMCwgZmlyc3REb3RJbmRleCArIDEpICsgaW5wdXRWYWx1ZS5zdWJzdHJpbmcoZmlyc3REb3RJbmRleCArIDEpLnJlcGxhY2UoL1wuL2csICcnKQogICAgICB9CgogICAgICAvLyDlpoLmnpzlj6rmmK/lsI/mlbDngrnvvIzorr7nva7kuLrnqboKICAgICAgaWYgKGlucHV0VmFsdWUgPT09ICcuJykgewogICAgICAgIHRoaXMuZm9ybS5Xb3JrbG9hZF9Qcm9wb3J0aW9uID0gJycKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgLy8g5aaC5p6c5aSE55CG5ZCO5Li656m65a2X56ym5Liy77yM6K6+572u5Li656m6CiAgICAgIGlmIChpbnB1dFZhbHVlID09PSAnJykgewogICAgICAgIHRoaXMuZm9ybS5Xb3JrbG9hZF9Qcm9wb3J0aW9uID0gJycKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgLy8g6L2s5o2i5Li65pWw5a2X6L+b6KGM6IyD5Zu05qOA5p+lCiAgICAgIGNvbnN0IG51bVZhbHVlID0gcGFyc2VGbG9hdChpbnB1dFZhbHVlKQoKICAgICAgLy8g5aaC5p6c5LiN5piv5pyJ5pWI5pWw5a2X77yM6K6+572u5Li656m6CiAgICAgIGlmIChpc05hTihudW1WYWx1ZSkpIHsKICAgICAgICB0aGlzLmZvcm0uV29ya2xvYWRfUHJvcG9ydGlvbiA9ICcnCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIC8vIOmZkOWItuiMg+WbtOWcqCAwLTEwMCDkuYvpl7QKICAgICAgaWYgKG51bVZhbHVlIDwgMCkgewogICAgICAgIHRoaXMuZm9ybS5Xb3JrbG9hZF9Qcm9wb3J0aW9uID0gJzAnCiAgICAgIH0gZWxzZSBpZiAobnVtVmFsdWUgPiAxMDApIHsKICAgICAgICB0aGlzLmZvcm0uV29ya2xvYWRfUHJvcG9ydGlvbiA9ICcxMDAnCiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5L+d5oyB5Y6f5aeL6L6T5YWl5qC85byP77yI5YyF5ous5bCP5pWw54K577yJCiAgICAgICAgdGhpcy5mb3JtLldvcmtsb2FkX1Byb3BvcnRpb24gPSBpbnB1dFZhbHVlCiAgICAgIH0KICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["Add.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Add.vue", "sourceRoot": "src/views/PRO/process-settings/management/component", "sourcesContent": ["<template>\n  <div class=\"form-wrapper\">\n    <div class=\"form-x\">\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\" style=\"width: 100%\">\n        <el-divider content-position=\"left\">基础信息</el-divider>\n        <el-form-item label=\"名称\" prop=\"Name\">\n          <el-input v-model=\"form.Name\" :maxlength=\"30\" placeholder=\"最多30个字\" show-word-limit />\n        </el-form-item>\n        <el-form-item label=\"代号\" prop=\"Code\">\n          <el-input\n            v-model=\"form.Code\"\n            :maxlength=\"30\"\n            placeholder=\"字母+数字，30字符\"\n            show-word-limit\n            @input=\"(e) => (form.Code = codeChange(e))\"\n          />\n        </el-form-item>\n        <el-form-item label=\"类型\" prop=\"Bom_Level\">\n          <el-radio-group\n            v-for=\"(item, index) in bomList\"\n            :key=\"index\"\n            v-model=\"form.Bom_Level\"\n            class=\"radio\"\n            @change=\"changeType\"\n          >\n            <el-radio style=\"margin-right: 8px;\" :label=\"item.Code\">{{ item.Display_Name }}</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"Sort\">\n          <el-input-number\n            v-model=\"form.Sort\"\n            :min=\"0\"\n            step-strictly\n            :step=\"1\"\n            class=\"cs-number-btn-hidden w100\"\n            placeholder=\"请输入\"\n            clearable=\"\"\n          />\n        </el-form-item>\n        <el-form-item label=\"协调人\" prop=\"Coordinate_UserId\">\n          <el-select v-model=\"form.Coordinate_UserId\" class=\"w100\" clearable filterable placeholder=\"请选择\">\n            <el-option v-for=\"item in optionsUserList\" :key=\"item.Id\" :label=\"item.Display_Name\" :value=\"item.Id\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"工序月均负荷\" prop=\"Month_Avg_Load\">\n          <el-input v-model=\"form.Month_Avg_Load\" placeholder=\"请输入\">\n            <template slot=\"append\">吨</template>\n          </el-input>\n        </el-form-item>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否启用\" prop=\"Is_Enable\">\n              <el-radio-group v-model=\"form.Is_Enable\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否外协\" prop=\"Is_External\">\n              <el-radio-group v-model=\"form.Is_External\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否装焊工序\" prop=\"Is_Welding_Assembling\">\n              <el-radio-group v-model=\"form.Is_Welding_Assembling\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否下料工序\" prop=\"Is_Cutting\">\n              <el-radio-group v-model=\"form.Is_Cutting\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否套料工序\" prop=\"Is_Nest\">\n              <el-radio-group v-model=\"form.Is_Nest\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否领料工序\" prop=\"Is_Pick_Material\">\n              <el-radio-group v-model=\"form.Is_Pick_Material\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"加工班组\" prop=\"Working_Team_Ids\">\n          <el-select v-model=\"form.Working_Team_Ids\" multiple style=\"width: 100%\" placeholder=\"请选择加工班组\">\n            <el-option v-for=\"item in optionsWorkingTeamsList\" :key=\"item.Id\" :label=\"item.Name\" :value=\"item.Id\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"备注\">\n          <el-input v-model=\"form.Remark\" type=\"textarea\" />\n        </el-form-item>\n        <el-divider content-position=\"left\">质检信息</el-divider>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否自检\" prop=\"Is_Self_Check\">\n              <el-radio-group v-model=\"form.Is_Self_Check\" @change=\"radioSelfCheck\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否互检\" prop=\"Is_Inter_Check\">\n              <el-radio-group v-model=\"form.Is_Inter_Check\" @change=\"radioInterCheck\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否专检\" prop=\"Is_Need_Check\">\n              <el-radio-group v-model=\"form.Is_Need_Check\" @change=\"radioChange\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <template v-if=\"form.Is_Need_Check\">\n              <el-form-item label=\"专检方式\" prop=\"Check_Style\">\n                <el-radio-group v-model=\"form.Check_Style\" @change=\"radioCheckStyleChange\">\n                  <el-radio label=\"0\">抽检</el-radio>\n                  <el-radio label=\"1\">全检</el-radio>\n                </el-radio-group>\n              </el-form-item>\n            </template>\n          </el-col>\n        </el-row>\n\n        <template v-if=\"form.Is_Need_Check\">\n          <el-form-item label=\"专检类型\" prop=\"\">\n            <div>\n              <div style=\"margin-bottom: 10px;\">\n                <el-checkbox v-model=\"form.Is_Need_TC\" @change=\"checkboxChange($event, 1)\">\n                  <span> 探伤</span>\n                </el-checkbox>\n                <span style=\"margin-left: 30px; \">\n                  <span style=\"color: rgba(34, 40, 52, 0.65)\">探伤员：</span>\n                  <el-select\n                    v-model=\"TC_Check_UserIds\"\n                    filterable\n                    clearable\n                    :disabled=\"!form.Is_Need_TC\"\n                    multiple\n                    placeholder=\"请选择探伤员\"\n                    @change=\"changeTc\"\n                  >\n                    <el-option\n                      v-for=\"item in optionsUserList\"\n                      :key=\"item.Id\"\n                      :label=\"item.Display_Name\"\n                      :value=\"item.Id\"\n                    />\n                  </el-select>\n                </span>\n              </div>\n              <div>\n                <el-checkbox v-model=\"form.Is_Need_ZL\" @change=\"checkboxChange($event, 2)\">\n                  <span> 质量</span>\n                </el-checkbox>\n                <span style=\"margin-left: 30px\">\n                  <span style=\"color: rgba(34, 40, 52, 0.65)\">质检员：</span>\n                  <el-select\n                    v-model=\"ZL_Check_UserIds\"\n                    :disabled=\"!form.Is_Need_ZL\"\n                    filterable\n                    clearable\n                    multiple\n                    placeholder=\"请选择质检员\"\n                    @change=\"changeZL\"\n                  >\n                    <el-option\n                      v-for=\"item in optionsUserList\"\n                      :key=\"item.Id\"\n                      :label=\"item.Display_Name\"\n                      :value=\"item.Id\"\n                    />\n                  </el-select>\n                </span>\n              </div>\n            </div>\n          </el-form-item>\n        </template>\n        <el-divider content-position=\"left\">其他信息</el-divider>\n        <el-form-item label=\"工作量占比\" prop=\"Workload_Proportion\">\n          <el-input v-model=\"form.Workload_Proportion\" placeholder=\"请输入\" @input=\"handlePercentageInput\">\n            <template slot=\"append\">%</template>\n          </el-input>\n        </el-form-item>\n        <el-form-item label=\"是否展示模型\" prop=\"Show_Model\">\n          <el-radio-group v-model=\"form.Show_Model\">\n            <el-radio :label=\"true\">是</el-radio>\n            <el-radio :label=\"false\">否</el-radio>\n          </el-radio-group>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"btn-x\">\n      <el-button @click=\"$emit('close')\">取 消</el-button>\n      <el-button :loading=\"btnLoading\" type=\"primary\" @click=\"handleSubmit\">确 定\n      </el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport { GetUserList } from '@/api/sys'\nimport {\n  AddWorkingProcess,\n  GetFactoryPeoplelist,\n  GetCheckGroupList,\n  GetWorkingTeams\n} from '@/api/PRO/technology-lib'\nimport { mapGetters } from 'vuex'\nimport { GetDictionaryDetailListByCode } from '@/api/sys'\nexport default {\n  props: {\n    type: {\n      type: String,\n      default: ''\n    },\n    rowInfo: {\n      type: Object,\n      default() {\n        return {}\n      }\n    },\n    workloadProportionAll: {\n      type: Number,\n      default: 0\n    }\n  },\n  data() {\n    return {\n      checkList: [],\n      btnLoading: false,\n      hiddenPart: false,\n\n      form: {\n        Code: '',\n        Name: '',\n        Bom_Level: '',\n        Month_Avg_Load: '',\n        Coordinate_UserId: '',\n        Sort: undefined,\n        Is_Enable: true,\n        Is_External: false,\n        Is_Nest: false,\n        Is_Need_Check: true,\n        Is_Self_Check: true,\n        Is_Inter_Check: true,\n        Is_Pick_Material: false,\n        Is_Need_TC: true,\n        Is_Welding_Assembling: false,\n        Is_Cutting: false,\n        TC_Check_UserId: '',\n        Is_Need_ZL: false,\n        ZL_Check_UserId: '',\n        Show_Model: false,\n\n        Check_Style: '0',\n\n        Working_Team_Ids: [],\n        Remark: '',\n        Workload_Proportion: ''\n      },\n      ZL_Check_UserIds: [],\n      TC_Check_UserIds: [],\n      CheckChange: true,\n      userOptions: [],\n      optionsUserList: [],\n      optionsGroupList: [],\n      optionsWorkingTeamsList: [],\n      rules: {\n        Code: [\n          { required: true, message: '请输入代号', trigger: 'blur' },\n          { max: 30, message: '长度在 30 个字符内', trigger: 'blur' }\n        ],\n        Name: [\n          { required: true, message: '请输入名称', trigger: 'blur' },\n          { max: 30, message: '长度在 30 个字符内', trigger: 'blur' }\n        ],\n        Bom_Level: [{ required: true, message: '请选择类型', trigger: 'change' }],\n        Sort: [{ required: true, message: '请输入', trigger: 'blur' }],\n        Is_Need_Check: [\n          { required: true, message: '请选择是否质检', trigger: 'change' }\n        ]\n      },\n      bomList: [],\n      comName: '',\n      partName: ''\n    }\n  },\n  computed: {\n    ...mapGetters('tenant', ['isVersionFour'])\n  },\n  async created() {\n    const { comName, partName, list } = await GetBOMInfo()\n    this.comName = comName\n    this.partName = partName\n    this.bomList = list\n  },\n  mounted() {\n    this.getUserList()\n    this.getFactoryPeoplelist()\n    // this.getCheckGroupList();\n    this.getWorkingTeamsList()\n    this.type === 'edit' && this.initForm()\n  },\n  methods: {\n    initForm() {\n      const { Is_Nest, ...others } = this.rowInfo\n      this.form = Object.assign({}, others, { Is_Nest: !!Is_Nest })\n      this.form.Bom_Level = String(this.form.Bom_Level)\n      //  if(this.form.Type==2){\n      //   this.form.Types = '0'\n      //  }else if(this.form.Type==3){\n      //   let Types = this.radioList.find(v => ['1', '2','3'].includes(v.Code))?.Code\n      //   console.log('Types', Types)\n      //   console.log('this.radioList', this.radioList)\n      //   this.form.Types = Types\n      //  }else if(this.form.Type==1){\n      //   this.form.Types = '-1'\n      //  }\n      console.log('this.form', this.form)\n\n      // 处理历史数据多选问题\n      // if (this.form.Is_Need_Check) {\n      //   if (this.form.Check_Style === '1') {\n\n      //   } else {\n      //     this.CheckChange = !!this.form.Is_Need_TC\n      //     if (this.form.Is_Need_ZL && this.form.Is_Need_TC) {\n      //       this.form.Is_Need_TC = true\n      //       this.form.Is_Need_ZL = false\n      //     }\n      //   }\n      // }\n      this.ZL_Check_UserIds = this.form.ZL_Check_UserId\n        ? this.form.ZL_Check_UserId.split(',')\n        : []\n      this.TC_Check_UserIds = this.form.TC_Check_UserId\n        ? this.form.TC_Check_UserId.split(',')\n        : []\n    },\n    // 是否自检\n    radioSelfCheck(val) { },\n    // 是否互检\n    radioInterCheck(val) { },\n    // 获取设备类型\n    async getDictionaryDetailListByCode() {\n      await GetDictionaryDetailListByCode({ dictionaryCode: 'deviceType' }).then((res) => {\n        if (res.IsSucceed) {\n          this.deviceTypeList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n      console.log(' this.optionsGroupList', this.optionsGroupList)\n    },\n    getUserList() {\n      GetUserList({}).then((res) => {\n        if (res.IsSucceed) {\n          this.userOptions = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    getFactoryPeoplelist() {\n      GetFactoryPeoplelist({}).then((res) => {\n        if (res.IsSucceed) {\n          this.optionsUserList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    async getCheckGroupList() {\n      await GetCheckGroupList({}).then((res) => {\n        if (res.IsSucceed) {\n          this.optionsGroupList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n      console.log(' this.optionsGroupList', this.optionsGroupList)\n    },\n    getWorkingTeamsList() {\n      GetWorkingTeams({}).then((res) => {\n        if (res.IsSucceed) {\n          this.optionsWorkingTeamsList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    // 选择专检方式 抽检、全检\n    radioCheckStyleChange(val) {\n      // if (val === '0') {\n      //   this.form.Is_Need_TC = true\n      //   this.form.Is_Need_ZL = false\n      // }\n      this.ZL_Check_UserIds = []\n      this.TC_Check_UserIds = []\n      this.form.ZL_Check_UserId = ''\n      this.form.TC_Check_UserId = ''\n    },\n    // 是否专检\n    radioChange(val) {\n      if (val === false) {\n        this.form.checkChange = false\n        this.form.Is_Need_TC = false\n        this.form.Is_Need_ZL = false\n        this.TC_Check_UserIds = []\n        this.ZL_Check_UserIds = []\n        this.form.ZL_Check_UserId = ''\n        this.form.TC_Check_UserId = ''\n        this.form.Check_Style = ''\n      } else {\n        // this.form.checkChange = true\n        // this.form.Is_Need_TC = true\n        // this.form.Is_Need_ZL = false\n        // this.CheckChange = !!this.form.Is_Need_TC\n        this.form.Check_Style = '0'\n      }\n    },\n    // 选择BOM层级\n    changeType(val) {\n      // const Code = val\n      // console.log(Code, 'Code');\n      // if (Code === '-1') {\n      //   this.form.Type = 1\n      // } else if (Code === '0') {\n      //   this.form.Type = 2\n      // } else if (Code === '1' || Code === '3'|| Code === '2') {\n      //   this.form.Type = 3\n      // }\n      // if (this.form.Type === 1 || this.form.Type === 3) {\n      //   this.form.Is_Cutting = undefined\n      // } else if (this.form.Type === 2) {\n      //   this.form.Is_Welding_Assembling = undefined\n      // }\n    },\n    typeChange() {\n      this.form.Task_Model = ''\n    },\n    changeTc(val) {\n      console.log(val)\n      this.form.TC_Check_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i === val.length - 1) {\n          this.form.TC_Check_UserId += val[i]\n        } else {\n          this.form.TC_Check_UserId += val[i] + ','\n        }\n      }\n      console.log(this.form.TC_Check_UserId, 'this.form.TC_Check_UserId ')\n    },\n    changeZL(val) {\n      console.log(val)\n      this.form.ZL_Check_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i === val.length - 1) {\n          this.form.ZL_Check_UserId += val[i]\n        } else {\n          this.form.ZL_Check_UserId += val[i] + ','\n        }\n      }\n    },\n    checkboxChange(val, type) {\n      if (type === 1) {\n        if (!val) {\n          this.TC_Check_UserIds = []\n        }\n      }\n      if (type === 2) {\n        if (!val) {\n          this.ZL_Check_UserIds = []\n        }\n      }\n    },\n    handleSubmit() {\n      // delete this.form.Types\n      console.log(this.form, 'this.form')\n      this.$refs.form.validate((valid) => {\n        if (!valid) return\n        this.btnLoading = true\n        const uItems = this.optionsUserList.find(\n          (v) => v.Id === this.form.Coordinate_UserId\n        )\n        if (uItems) {\n          this.form.Coordinate_UserName = uItems.Display_Name\n        }\n        if (this.form.Is_Need_Check) {\n          if (this.form.Is_Need_ZL === false && this.form.Is_Need_TC === false) {\n            this.$message.error('请选择质检类型')\n            this.btnLoading = false\n            return\n          }\n        } else {\n          this.form.Check_Style = null\n          this.form.Check_Group_List = []\n        }\n        const ZL = this.form.Is_Need_ZL ? this.form.ZL_Check_UserId : ''\n        const TC = this.form.Is_Need_TC ? this.form.TC_Check_UserId : ''\n        if (this.form.Is_Need_ZL && (ZL ?? '') === '') {\n          this.$message.error('请选择质检员')\n          this.btnLoading = false\n          return\n        }\n        if (this.form.Is_Need_TC && (TC ?? '') === '') {\n          this.$message.error('请选择探伤员')\n          this.btnLoading = false\n          return\n        }\n\n        AddWorkingProcess({\n          ...this.form,\n          ZL_Check_UserId: ZL,\n          TC_Check_UserId: TC\n        }).then((res) => {\n          if (res.IsSucceed) {\n            this.$message({\n              message: '保存成功',\n              type: 'success'\n            })\n            this.$emit('refresh')\n            this.$emit('close')\n          } else {\n            this.$message({\n              message: res.Message,\n              type: 'error'\n            })\n          }\n          this.btnLoading = false\n        })\n      })\n    },\n\n    codeChange(e) {\n      return e.replace(/[^a-zA-Z0-9]/g, '')\n    },\n\n    handlePercentageInput(value) {\n      // 如果输入为空，直接返回\n      if (value === '' || value === null || value === undefined) {\n        this.form.Workload_Proportion = ''\n        return\n      }\n\n      // 转换为字符串进行处理\n      let inputValue = String(value)\n\n      // 只允许数字和一个小数点，移除其他字符（包括负号）\n      inputValue = inputValue.replace(/[^0-9.]/g, '')\n\n      // 确保只有一个小数点\n      const dotCount = (inputValue.match(/\\./g) || []).length\n      if (dotCount > 1) {\n        // 如果有多个小数点，只保留第一个\n        const firstDotIndex = inputValue.indexOf('.')\n        inputValue = inputValue.substring(0, firstDotIndex + 1) + inputValue.substring(firstDotIndex + 1).replace(/\\./g, '')\n      }\n\n      // 如果只是小数点，设置为空\n      if (inputValue === '.') {\n        this.form.Workload_Proportion = ''\n        return\n      }\n\n      // 如果处理后为空字符串，设置为空\n      if (inputValue === '') {\n        this.form.Workload_Proportion = ''\n        return\n      }\n\n      // 转换为数字进行范围检查\n      const numValue = parseFloat(inputValue)\n\n      // 如果不是有效数字，设置为空\n      if (isNaN(numValue)) {\n        this.form.Workload_Proportion = ''\n        return\n      }\n\n      // 限制范围在 0-100 之间\n      if (numValue < 0) {\n        this.form.Workload_Proportion = '0'\n      } else if (numValue > 100) {\n        this.form.Workload_Proportion = '100'\n      } else {\n        // 保持原始输入格式（包括小数点）\n        this.form.Workload_Proportion = inputValue\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/mixin.scss\";\n\n.btn-del {\n  margin-left: -100px;\n}\n\n.customRadioClass {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.checkboxFlex {\n  display: flex;\n  align-items: center;\n}\n\n.form-wrapper {\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  min-height: 40vh;\n\n  .form-x {\n    max-height: 70vh;\n    overflow: auto;\n    padding-right: 16px;\n    @include scrollBar;\n  }\n\n  .btn-x {\n    padding-top: 16px;\n    text-align: right;\n  }\n}\n</style>\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\Add.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\Add.vue", "mtime": 1757484138456}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IEdldEJPTUluZm8gfSBmcm9tICdAL3ZpZXdzL1BSTy9ib20tc2V0dGluZy91dGlscycKaW1wb3J0IHsgR2V0VXNlckxpc3QgfSBmcm9tICdAL2FwaS9zeXMnCmltcG9ydCB7CiAgQWRkV29ya2luZ1Byb2Nlc3MsCiAgR2V0RmFjdG9yeVBlb3BsZWxpc3QsCiAgR2V0Q2hlY2tHcm91cExpc3QsCiAgR2V0V29ya2luZ1RlYW1zCn0gZnJvbSAnQC9hcGkvUFJPL3RlY2hub2xvZ3ktbGliJwppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCcKaW1wb3J0IHsgR2V0RGljdGlvbmFyeURldGFpbExpc3RCeUNvZGUgfSBmcm9tICdAL2FwaS9zeXMnCmV4cG9ydCBkZWZhdWx0IHsKICBwcm9wczogewogICAgdHlwZTogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcnCiAgICB9LAogICAgcm93SW5mbzogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIHt9CiAgICAgIH0KICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBjaGVja0xpc3Q6IFtdLAogICAgICBidG5Mb2FkaW5nOiBmYWxzZSwKICAgICAgaGlkZGVuUGFydDogZmFsc2UsCgogICAgICBmb3JtOiB7CiAgICAgICAgQ29kZTogJycsCiAgICAgICAgTmFtZTogJycsCiAgICAgICAgQm9tX0xldmVsOiAnJywKICAgICAgICBNb250aF9BdmdfTG9hZDogJycsCiAgICAgICAgQ29vcmRpbmF0ZV9Vc2VySWQ6ICcnLAogICAgICAgIFNvcnQ6IHVuZGVmaW5lZCwKICAgICAgICBJc19FbmFibGU6IHRydWUsCiAgICAgICAgSXNfRXh0ZXJuYWw6IGZhbHNlLAogICAgICAgIElzX05lc3Q6IGZhbHNlLAogICAgICAgIElzX05lZWRfQ2hlY2s6IHRydWUsCiAgICAgICAgSXNfU2VsZl9DaGVjazogdHJ1ZSwKICAgICAgICBJc19JbnRlcl9DaGVjazogdHJ1ZSwKICAgICAgICBJc19QaWNrX01hdGVyaWFsOiBmYWxzZSwKICAgICAgICBJc19OZWVkX1RDOiB0cnVlLAogICAgICAgIElzX1dlbGRpbmdfQXNzZW1ibGluZzogZmFsc2UsCiAgICAgICAgSXNfQ3V0dGluZzogZmFsc2UsCiAgICAgICAgVENfQ2hlY2tfVXNlcklkOiAnJywKICAgICAgICBJc19OZWVkX1pMOiBmYWxzZSwKICAgICAgICBaTF9DaGVja19Vc2VySWQ6ICcnLAogICAgICAgIFNob3dfTW9kZWw6IGZhbHNlLAoKICAgICAgICBDaGVja19TdHlsZTogJzAnLAoKICAgICAgICBXb3JraW5nX1RlYW1fSWRzOiBbXSwKICAgICAgICBSZW1hcms6ICcnLAogICAgICAgIFdvcmtsb2FkX1Byb3BvcnRpb246ICcnCiAgICAgIH0sCiAgICAgIFpMX0NoZWNrX1VzZXJJZHM6IFtdLAogICAgICBUQ19DaGVja19Vc2VySWRzOiBbXSwKICAgICAgQ2hlY2tDaGFuZ2U6IHRydWUsCiAgICAgIHVzZXJPcHRpb25zOiBbXSwKICAgICAgb3B0aW9uc1VzZXJMaXN0OiBbXSwKICAgICAgb3B0aW9uc0dyb3VwTGlzdDogW10sCiAgICAgIG9wdGlvbnNXb3JraW5nVGVhbXNMaXN0OiBbXSwKICAgICAgcnVsZXM6IHsKICAgICAgICBDb2RlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5Luj5Y+3JywgdHJpZ2dlcjogJ2JsdXInIH0sCiAgICAgICAgICB7IG1heDogMzAsIG1lc3NhZ2U6ICfplb/luqblnKggMzAg5Liq5a2X56ym5YaFJywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICAgIE5hbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXlkI3np7AnLCB0cmlnZ2VyOiAnYmx1cicgfSwKICAgICAgICAgIHsgbWF4OiAzMCwgbWVzc2FnZTogJ+mVv+W6puWcqCAzMCDkuKrlrZfnrKblhoUnLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIF0sCiAgICAgICAgQm9tX0xldmVsOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeexu+WeiycsIHRyaWdnZXI6ICdjaGFuZ2UnIH1dLAogICAgICAgIFNvcnQ6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWlJywgdHJpZ2dlcjogJ2JsdXInIH1dLAogICAgICAgIElzX05lZWRfQ2hlY2s6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nmmK/lkKbotKjmo4AnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgICAgXQogICAgICB9LAogICAgICBib21MaXN0OiBbXSwKICAgICAgY29tTmFtZTogJycsCiAgICAgIHBhcnROYW1lOiAnJwogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIC4uLm1hcEdldHRlcnMoJ3RlbmFudCcsIFsnaXNWZXJzaW9uRm91ciddKQogIH0sCiAgYXN5bmMgY3JlYXRlZCgpIHsKICAgIGNvbnN0IHsgY29tTmFtZSwgcGFydE5hbWUsIGxpc3QgfSA9IGF3YWl0IEdldEJPTUluZm8oKQogICAgdGhpcy5jb21OYW1lID0gY29tTmFtZQogICAgdGhpcy5wYXJ0TmFtZSA9IHBhcnROYW1lCiAgICB0aGlzLmJvbUxpc3QgPSBsaXN0CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5nZXRVc2VyTGlzdCgpCiAgICB0aGlzLmdldEZhY3RvcnlQZW9wbGVsaXN0KCkKICAgIC8vIHRoaXMuZ2V0Q2hlY2tHcm91cExpc3QoKTsKICAgIHRoaXMuZ2V0V29ya2luZ1RlYW1zTGlzdCgpCiAgICB0aGlzLnR5cGUgPT09ICdlZGl0JyAmJiB0aGlzLmluaXRGb3JtKCkKICB9LAogIG1ldGhvZHM6IHsKICAgIGluaXRGb3JtKCkgewogICAgICBjb25zdCB7IElzX05lc3QsIC4uLm90aGVycyB9ID0gdGhpcy5yb3dJbmZvCiAgICAgIHRoaXMuZm9ybSA9IE9iamVjdC5hc3NpZ24oe30sIG90aGVycywgeyBJc19OZXN0OiAhIUlzX05lc3QgfSkKICAgICAgdGhpcy5mb3JtLkJvbV9MZXZlbCA9IFN0cmluZyh0aGlzLmZvcm0uQm9tX0xldmVsKQogICAgICAvLyAgaWYodGhpcy5mb3JtLlR5cGU9PTIpewogICAgICAvLyAgIHRoaXMuZm9ybS5UeXBlcyA9ICcwJwogICAgICAvLyAgfWVsc2UgaWYodGhpcy5mb3JtLlR5cGU9PTMpewogICAgICAvLyAgIGxldCBUeXBlcyA9IHRoaXMucmFkaW9MaXN0LmZpbmQodiA9PiBbJzEnLCAnMicsJzMnXS5pbmNsdWRlcyh2LkNvZGUpKT8uQ29kZQogICAgICAvLyAgIGNvbnNvbGUubG9nKCdUeXBlcycsIFR5cGVzKQogICAgICAvLyAgIGNvbnNvbGUubG9nKCd0aGlzLnJhZGlvTGlzdCcsIHRoaXMucmFkaW9MaXN0KQogICAgICAvLyAgIHRoaXMuZm9ybS5UeXBlcyA9IFR5cGVzCiAgICAgIC8vICB9ZWxzZSBpZih0aGlzLmZvcm0uVHlwZT09MSl7CiAgICAgIC8vICAgdGhpcy5mb3JtLlR5cGVzID0gJy0xJwogICAgICAvLyAgfQogICAgICBjb25zb2xlLmxvZygndGhpcy5mb3JtJywgdGhpcy5mb3JtKQoKICAgICAgLy8g5aSE55CG5Y6G5Y+y5pWw5o2u5aSa6YCJ6Zeu6aKYCiAgICAgIC8vIGlmICh0aGlzLmZvcm0uSXNfTmVlZF9DaGVjaykgewogICAgICAvLyAgIGlmICh0aGlzLmZvcm0uQ2hlY2tfU3R5bGUgPT09ICcxJykgewoKICAgICAgLy8gICB9IGVsc2UgewogICAgICAvLyAgICAgdGhpcy5DaGVja0NoYW5nZSA9ICEhdGhpcy5mb3JtLklzX05lZWRfVEMKICAgICAgLy8gICAgIGlmICh0aGlzLmZvcm0uSXNfTmVlZF9aTCAmJiB0aGlzLmZvcm0uSXNfTmVlZF9UQykgewogICAgICAvLyAgICAgICB0aGlzLmZvcm0uSXNfTmVlZF9UQyA9IHRydWUKICAgICAgLy8gICAgICAgdGhpcy5mb3JtLklzX05lZWRfWkwgPSBmYWxzZQogICAgICAvLyAgICAgfQogICAgICAvLyAgIH0KICAgICAgLy8gfQogICAgICB0aGlzLlpMX0NoZWNrX1VzZXJJZHMgPSB0aGlzLmZvcm0uWkxfQ2hlY2tfVXNlcklkCiAgICAgICAgPyB0aGlzLmZvcm0uWkxfQ2hlY2tfVXNlcklkLnNwbGl0KCcsJykKICAgICAgICA6IFtdCiAgICAgIHRoaXMuVENfQ2hlY2tfVXNlcklkcyA9IHRoaXMuZm9ybS5UQ19DaGVja19Vc2VySWQKICAgICAgICA/IHRoaXMuZm9ybS5UQ19DaGVja19Vc2VySWQuc3BsaXQoJywnKQogICAgICAgIDogW10KICAgIH0sCiAgICAvLyDmmK/lkKboh6rmo4AKICAgIHJhZGlvU2VsZkNoZWNrKHZhbCkgeyB9LAogICAgLy8g5piv5ZCm5LqS5qOACiAgICByYWRpb0ludGVyQ2hlY2sodmFsKSB7IH0sCiAgICAvLyDojrflj5borr7lpIfnsbvlnosKICAgIGFzeW5jIGdldERpY3Rpb25hcnlEZXRhaWxMaXN0QnlDb2RlKCkgewogICAgICBhd2FpdCBHZXREaWN0aW9uYXJ5RGV0YWlsTGlzdEJ5Q29kZSh7IGRpY3Rpb25hcnlDb2RlOiAnZGV2aWNlVHlwZScgfSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIHRoaXMuZGV2aWNlVHlwZUxpc3QgPSByZXMuRGF0YQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgICBjb25zb2xlLmxvZygnIHRoaXMub3B0aW9uc0dyb3VwTGlzdCcsIHRoaXMub3B0aW9uc0dyb3VwTGlzdCkKICAgIH0sCiAgICBnZXRVc2VyTGlzdCgpIHsKICAgICAgR2V0VXNlckxpc3Qoe30pLnRoZW4oKHJlcykgPT4gewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICB0aGlzLnVzZXJPcHRpb25zID0gcmVzLkRhdGEKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICBnZXRGYWN0b3J5UGVvcGxlbGlzdCgpIHsKICAgICAgR2V0RmFjdG9yeVBlb3BsZWxpc3Qoe30pLnRoZW4oKHJlcykgPT4gewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICB0aGlzLm9wdGlvbnNVc2VyTGlzdCA9IHJlcy5EYXRhCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgYXN5bmMgZ2V0Q2hlY2tHcm91cExpc3QoKSB7CiAgICAgIGF3YWl0IEdldENoZWNrR3JvdXBMaXN0KHt9KS50aGVuKChyZXMpID0+IHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgdGhpcy5vcHRpb25zR3JvdXBMaXN0ID0gcmVzLkRhdGEKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgICAgY29uc29sZS5sb2coJyB0aGlzLm9wdGlvbnNHcm91cExpc3QnLCB0aGlzLm9wdGlvbnNHcm91cExpc3QpCiAgICB9LAogICAgZ2V0V29ya2luZ1RlYW1zTGlzdCgpIHsKICAgICAgR2V0V29ya2luZ1RlYW1zKHt9KS50aGVuKChyZXMpID0+IHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgdGhpcy5vcHRpb25zV29ya2luZ1RlYW1zTGlzdCA9IHJlcy5EYXRhCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgLy8g6YCJ5oup5LiT5qOA5pa55byPIOaKveajgOOAgeWFqOajgAogICAgcmFkaW9DaGVja1N0eWxlQ2hhbmdlKHZhbCkgewogICAgICAvLyBpZiAodmFsID09PSAnMCcpIHsKICAgICAgLy8gICB0aGlzLmZvcm0uSXNfTmVlZF9UQyA9IHRydWUKICAgICAgLy8gICB0aGlzLmZvcm0uSXNfTmVlZF9aTCA9IGZhbHNlCiAgICAgIC8vIH0KICAgICAgdGhpcy5aTF9DaGVja19Vc2VySWRzID0gW10KICAgICAgdGhpcy5UQ19DaGVja19Vc2VySWRzID0gW10KICAgICAgdGhpcy5mb3JtLlpMX0NoZWNrX1VzZXJJZCA9ICcnCiAgICAgIHRoaXMuZm9ybS5UQ19DaGVja19Vc2VySWQgPSAnJwogICAgfSwKICAgIC8vIOaYr+WQpuS4k+ajgAogICAgcmFkaW9DaGFuZ2UodmFsKSB7CiAgICAgIGlmICh2YWwgPT09IGZhbHNlKSB7CiAgICAgICAgdGhpcy5mb3JtLmNoZWNrQ2hhbmdlID0gZmFsc2UKICAgICAgICB0aGlzLmZvcm0uSXNfTmVlZF9UQyA9IGZhbHNlCiAgICAgICAgdGhpcy5mb3JtLklzX05lZWRfWkwgPSBmYWxzZQogICAgICAgIHRoaXMuVENfQ2hlY2tfVXNlcklkcyA9IFtdCiAgICAgICAgdGhpcy5aTF9DaGVja19Vc2VySWRzID0gW10KICAgICAgICB0aGlzLmZvcm0uWkxfQ2hlY2tfVXNlcklkID0gJycKICAgICAgICB0aGlzLmZvcm0uVENfQ2hlY2tfVXNlcklkID0gJycKICAgICAgICB0aGlzLmZvcm0uQ2hlY2tfU3R5bGUgPSAnJwogICAgICB9IGVsc2UgewogICAgICAgIC8vIHRoaXMuZm9ybS5jaGVja0NoYW5nZSA9IHRydWUKICAgICAgICAvLyB0aGlzLmZvcm0uSXNfTmVlZF9UQyA9IHRydWUKICAgICAgICAvLyB0aGlzLmZvcm0uSXNfTmVlZF9aTCA9IGZhbHNlCiAgICAgICAgLy8gdGhpcy5DaGVja0NoYW5nZSA9ICEhdGhpcy5mb3JtLklzX05lZWRfVEMKICAgICAgICB0aGlzLmZvcm0uQ2hlY2tfU3R5bGUgPSAnMCcKICAgICAgfQogICAgfSwKICAgIC8vIOmAieaLqUJPTeWxgue6pwogICAgY2hhbmdlVHlwZSh2YWwpIHsKICAgICAgLy8gY29uc3QgQ29kZSA9IHZhbAogICAgICAvLyBjb25zb2xlLmxvZyhDb2RlLCAnQ29kZScpOwogICAgICAvLyBpZiAoQ29kZSA9PT0gJy0xJykgewogICAgICAvLyAgIHRoaXMuZm9ybS5UeXBlID0gMQogICAgICAvLyB9IGVsc2UgaWYgKENvZGUgPT09ICcwJykgewogICAgICAvLyAgIHRoaXMuZm9ybS5UeXBlID0gMgogICAgICAvLyB9IGVsc2UgaWYgKENvZGUgPT09ICcxJyB8fCBDb2RlID09PSAnMyd8fCBDb2RlID09PSAnMicpIHsKICAgICAgLy8gICB0aGlzLmZvcm0uVHlwZSA9IDMKICAgICAgLy8gfQogICAgICAvLyBpZiAodGhpcy5mb3JtLlR5cGUgPT09IDEgfHwgdGhpcy5mb3JtLlR5cGUgPT09IDMpIHsKICAgICAgLy8gICB0aGlzLmZvcm0uSXNfQ3V0dGluZyA9IHVuZGVmaW5lZAogICAgICAvLyB9IGVsc2UgaWYgKHRoaXMuZm9ybS5UeXBlID09PSAyKSB7CiAgICAgIC8vICAgdGhpcy5mb3JtLklzX1dlbGRpbmdfQXNzZW1ibGluZyA9IHVuZGVmaW5lZAogICAgICAvLyB9CiAgICB9LAogICAgdHlwZUNoYW5nZSgpIHsKICAgICAgdGhpcy5mb3JtLlRhc2tfTW9kZWwgPSAnJwogICAgfSwKICAgIGNoYW5nZVRjKHZhbCkgewogICAgICBjb25zb2xlLmxvZyh2YWwpCiAgICAgIHRoaXMuZm9ybS5UQ19DaGVja19Vc2VySWQgPSAnJwogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHZhbC5sZW5ndGg7IGkrKykgewogICAgICAgIGlmIChpID09PSB2YWwubGVuZ3RoIC0gMSkgewogICAgICAgICAgdGhpcy5mb3JtLlRDX0NoZWNrX1VzZXJJZCArPSB2YWxbaV0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5mb3JtLlRDX0NoZWNrX1VzZXJJZCArPSB2YWxbaV0gKyAnLCcKICAgICAgICB9CiAgICAgIH0KICAgICAgY29uc29sZS5sb2codGhpcy5mb3JtLlRDX0NoZWNrX1VzZXJJZCwgJ3RoaXMuZm9ybS5UQ19DaGVja19Vc2VySWQgJykKICAgIH0sCiAgICBjaGFuZ2VaTCh2YWwpIHsKICAgICAgY29uc29sZS5sb2codmFsKQogICAgICB0aGlzLmZvcm0uWkxfQ2hlY2tfVXNlcklkID0gJycKICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB2YWwubGVuZ3RoOyBpKyspIHsKICAgICAgICBpZiAoaSA9PT0gdmFsLmxlbmd0aCAtIDEpIHsKICAgICAgICAgIHRoaXMuZm9ybS5aTF9DaGVja19Vc2VySWQgKz0gdmFsW2ldCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuZm9ybS5aTF9DaGVja19Vc2VySWQgKz0gdmFsW2ldICsgJywnCiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgY2hlY2tib3hDaGFuZ2UodmFsLCB0eXBlKSB7CiAgICAgIGlmICh0eXBlID09PSAxKSB7CiAgICAgICAgaWYgKCF2YWwpIHsKICAgICAgICAgIHRoaXMuVENfQ2hlY2tfVXNlcklkcyA9IFtdCiAgICAgICAgfQogICAgICB9CiAgICAgIGlmICh0eXBlID09PSAyKSB7CiAgICAgICAgaWYgKCF2YWwpIHsKICAgICAgICAgIHRoaXMuWkxfQ2hlY2tfVXNlcklkcyA9IFtdCiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgaGFuZGxlU3VibWl0KCkgewogICAgICAvLyBkZWxldGUgdGhpcy5mb3JtLlR5cGVzCiAgICAgIGNvbnNvbGUubG9nKHRoaXMuZm9ybSwgJ3RoaXMuZm9ybScpCiAgICAgIHRoaXMuJHJlZnMuZm9ybS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICBpZiAoIXZhbGlkKSByZXR1cm4KICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSB0cnVlCiAgICAgICAgY29uc3QgdUl0ZW1zID0gdGhpcy5vcHRpb25zVXNlckxpc3QuZmluZCgKICAgICAgICAgICh2KSA9PiB2LklkID09PSB0aGlzLmZvcm0uQ29vcmRpbmF0ZV9Vc2VySWQKICAgICAgICApCiAgICAgICAgaWYgKHVJdGVtcykgewogICAgICAgICAgdGhpcy5mb3JtLkNvb3JkaW5hdGVfVXNlck5hbWUgPSB1SXRlbXMuRGlzcGxheV9OYW1lCiAgICAgICAgfQogICAgICAgIGlmICh0aGlzLmZvcm0uSXNfTmVlZF9DaGVjaykgewogICAgICAgICAgaWYgKHRoaXMuZm9ybS5Jc19OZWVkX1pMID09PSBmYWxzZSAmJiB0aGlzLmZvcm0uSXNfTmVlZF9UQyA9PT0gZmFsc2UpIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6K+36YCJ5oup6LSo5qOA57G75Z6LJykKICAgICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UKICAgICAgICAgICAgcmV0dXJuCiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuZm9ybS5DaGVja19TdHlsZSA9IG51bGwKICAgICAgICAgIHRoaXMuZm9ybS5DaGVja19Hcm91cF9MaXN0ID0gW10KICAgICAgICB9CiAgICAgICAgY29uc3QgWkwgPSB0aGlzLmZvcm0uSXNfTmVlZF9aTCA/IHRoaXMuZm9ybS5aTF9DaGVja19Vc2VySWQgOiAnJwogICAgICAgIGNvbnN0IFRDID0gdGhpcy5mb3JtLklzX05lZWRfVEMgPyB0aGlzLmZvcm0uVENfQ2hlY2tfVXNlcklkIDogJycKICAgICAgICBpZiAodGhpcy5mb3JtLklzX05lZWRfWkwgJiYgKFpMID8/ICcnKSA9PT0gJycpIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+mAieaLqei0qOajgOWRmCcpCiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQogICAgICAgICAgcmV0dXJuCiAgICAgICAgfQogICAgICAgIGlmICh0aGlzLmZvcm0uSXNfTmVlZF9UQyAmJiAoVEMgPz8gJycpID09PSAnJykgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6K+36YCJ5oup5o6i5Lyk5ZGYJykKICAgICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlCiAgICAgICAgICByZXR1cm4KICAgICAgICB9CgogICAgICAgIEFkZFdvcmtpbmdQcm9jZXNzKHsKICAgICAgICAgIC4uLnRoaXMuZm9ybSwKICAgICAgICAgIFpMX0NoZWNrX1VzZXJJZDogWkwsCiAgICAgICAgICBUQ19DaGVja19Vc2VySWQ6IFRDCiAgICAgICAgfSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICBtZXNzYWdlOiAn5L+d5a2Y5oiQ5YqfJywKICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycKICAgICAgICAgICAgfSkKICAgICAgICAgICAgdGhpcy4kZW1pdCgncmVmcmVzaCcpCiAgICAgICAgICAgIHRoaXMuJGVtaXQoJ2Nsb3NlJykKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgICAgfSkKICAgICAgICAgIH0KICAgICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlCiAgICAgICAgfSkKICAgICAgfSkKICAgIH0sCgogICAgY29kZUNoYW5nZShlKSB7CiAgICAgIHJldHVybiBlLnJlcGxhY2UoL1teYS16QS1aMC05XS9nLCAnJykKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["Add.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "Add.vue", "sourceRoot": "src/views/PRO/process-settings/management/component", "sourcesContent": ["<template>\n  <div class=\"form-wrapper\">\n    <div class=\"form-x\">\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\" style=\"width: 100%\">\n        <el-divider content-position=\"left\">基础信息</el-divider>\n        <el-form-item label=\"名称\" prop=\"Name\">\n          <el-input v-model=\"form.Name\" :maxlength=\"30\" placeholder=\"最多30个字\" show-word-limit />\n        </el-form-item>\n        <el-form-item label=\"代号\" prop=\"Code\">\n          <el-input\n            v-model=\"form.Code\"\n            :maxlength=\"30\"\n            placeholder=\"字母+数字，30字符\"\n            show-word-limit\n            @input=\"(e) => (form.Code = codeChange(e))\"\n          />\n        </el-form-item>\n        <el-form-item label=\"类型\" prop=\"Bom_Level\">\n          <el-radio-group\n            v-for=\"(item, index) in bomList\"\n            :key=\"index\"\n            v-model=\"form.Bom_Level\"\n            class=\"radio\"\n            @change=\"changeType\"\n          >\n            <el-radio style=\"margin-right: 8px;\" :label=\"item.Code\">{{ item.Display_Name }}</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"Sort\">\n          <el-input-number\n            v-model=\"form.Sort\"\n            :min=\"0\"\n            step-strictly\n            :step=\"1\"\n            class=\"cs-number-btn-hidden w100\"\n            placeholder=\"请输入\"\n            clearable=\"\"\n          />\n        </el-form-item>\n        <el-form-item label=\"协调人\" prop=\"Coordinate_UserId\">\n          <el-select v-model=\"form.Coordinate_UserId\" class=\"w100\" clearable filterable placeholder=\"请选择\">\n            <el-option v-for=\"item in optionsUserList\" :key=\"item.Id\" :label=\"item.Display_Name\" :value=\"item.Id\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"工序月均负荷\" prop=\"Month_Avg_Load\">\n          <el-input v-model=\"form.Month_Avg_Load\" placeholder=\"请输入\">\n            <template slot=\"append\">吨</template>\n          </el-input>\n        </el-form-item>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否启用\" prop=\"Is_Enable\">\n              <el-radio-group v-model=\"form.Is_Enable\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否外协\" prop=\"Is_External\">\n              <el-radio-group v-model=\"form.Is_External\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否装焊工序\" prop=\"Is_Welding_Assembling\">\n              <el-radio-group v-model=\"form.Is_Welding_Assembling\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否下料工序\" prop=\"Is_Cutting\">\n              <el-radio-group v-model=\"form.Is_Cutting\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否套料工序\" prop=\"Is_Nest\">\n              <el-radio-group v-model=\"form.Is_Nest\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否领料工序\" prop=\"Is_Pick_Material\">\n              <el-radio-group v-model=\"form.Is_Pick_Material\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"加工班组\" prop=\"Working_Team_Ids\">\n          <el-select v-model=\"form.Working_Team_Ids\" multiple style=\"width: 100%\" placeholder=\"请选择加工班组\">\n            <el-option v-for=\"item in optionsWorkingTeamsList\" :key=\"item.Id\" :label=\"item.Name\" :value=\"item.Id\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"备注\">\n          <el-input v-model=\"form.Remark\" type=\"textarea\" />\n        </el-form-item>\n        <el-divider content-position=\"left\">质检信息</el-divider>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否自检\" prop=\"Is_Self_Check\">\n              <el-radio-group v-model=\"form.Is_Self_Check\" @change=\"radioSelfCheck\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否互检\" prop=\"Is_Inter_Check\">\n              <el-radio-group v-model=\"form.Is_Inter_Check\" @change=\"radioInterCheck\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否专检\" prop=\"Is_Need_Check\">\n              <el-radio-group v-model=\"form.Is_Need_Check\" @change=\"radioChange\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <template v-if=\"form.Is_Need_Check\">\n              <el-form-item label=\"专检方式\" prop=\"Check_Style\">\n                <el-radio-group v-model=\"form.Check_Style\" @change=\"radioCheckStyleChange\">\n                  <el-radio label=\"0\">抽检</el-radio>\n                  <el-radio label=\"1\">全检</el-radio>\n                </el-radio-group>\n              </el-form-item>\n            </template>\n          </el-col>\n        </el-row>\n\n        <template v-if=\"form.Is_Need_Check\">\n          <el-form-item label=\"专检类型\" prop=\"\">\n            <div>\n              <div style=\"margin-bottom: 10px;\">\n                <el-checkbox v-model=\"form.Is_Need_TC\" @change=\"checkboxChange($event, 1)\">\n                  <span> 探伤</span>\n                </el-checkbox>\n                <span style=\"margin-left: 30px; \">\n                  <span style=\"color: rgba(34, 40, 52, 0.65)\">探伤员：</span>\n                  <el-select\n                    v-model=\"TC_Check_UserIds\"\n                    filterable\n                    clearable\n                    :disabled=\"!form.Is_Need_TC\"\n                    multiple\n                    placeholder=\"请选择探伤员\"\n                    @change=\"changeTc\"\n                  >\n                    <el-option\n                      v-for=\"item in optionsUserList\"\n                      :key=\"item.Id\"\n                      :label=\"item.Display_Name\"\n                      :value=\"item.Id\"\n                    />\n                  </el-select>\n                </span>\n              </div>\n              <div>\n                <el-checkbox v-model=\"form.Is_Need_ZL\" @change=\"checkboxChange($event, 2)\">\n                  <span> 质量</span>\n                </el-checkbox>\n                <span style=\"margin-left: 30px\">\n                  <span style=\"color: rgba(34, 40, 52, 0.65)\">质检员：</span>\n                  <el-select\n                    v-model=\"ZL_Check_UserIds\"\n                    :disabled=\"!form.Is_Need_ZL\"\n                    filterable\n                    clearable\n                    multiple\n                    placeholder=\"请选择质检员\"\n                    @change=\"changeZL\"\n                  >\n                    <el-option\n                      v-for=\"item in optionsUserList\"\n                      :key=\"item.Id\"\n                      :label=\"item.Display_Name\"\n                      :value=\"item.Id\"\n                    />\n                  </el-select>\n                </span>\n              </div>\n            </div>\n          </el-form-item>\n        </template>\n        <el-divider content-position=\"left\">其他信息</el-divider>\n        <el-form-item label=\"工作量占比\" prop=\"Workload_Proportion\">\n          <el-input v-model=\"form.Workload_Proportion\" placeholder=\"请输入\" type=\"number\">\n            <template slot=\"append\">%</template>\n          </el-input>\n        </el-form-item>\n        <el-form-item label=\"是否展示模型\" prop=\"Show_Model\">\n          <el-radio-group v-model=\"form.Show_Model\">\n            <el-radio :label=\"true\">是</el-radio>\n            <el-radio :label=\"false\">否</el-radio>\n          </el-radio-group>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"btn-x\">\n      <el-button @click=\"$emit('close')\">取 消</el-button>\n      <el-button :loading=\"btnLoading\" type=\"primary\" @click=\"handleSubmit\">确 定\n      </el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport { GetUserList } from '@/api/sys'\nimport {\n  AddWorkingProcess,\n  GetFactoryPeoplelist,\n  GetCheckGroupList,\n  GetWorkingTeams\n} from '@/api/PRO/technology-lib'\nimport { mapGetters } from 'vuex'\nimport { GetDictionaryDetailListByCode } from '@/api/sys'\nexport default {\n  props: {\n    type: {\n      type: String,\n      default: ''\n    },\n    rowInfo: {\n      type: Object,\n      default() {\n        return {}\n      }\n    }\n  },\n  data() {\n    return {\n      checkList: [],\n      btnLoading: false,\n      hiddenPart: false,\n\n      form: {\n        Code: '',\n        Name: '',\n        Bom_Level: '',\n        Month_Avg_Load: '',\n        Coordinate_UserId: '',\n        Sort: undefined,\n        Is_Enable: true,\n        Is_External: false,\n        Is_Nest: false,\n        Is_Need_Check: true,\n        Is_Self_Check: true,\n        Is_Inter_Check: true,\n        Is_Pick_Material: false,\n        Is_Need_TC: true,\n        Is_Welding_Assembling: false,\n        Is_Cutting: false,\n        TC_Check_UserId: '',\n        Is_Need_ZL: false,\n        ZL_Check_UserId: '',\n        Show_Model: false,\n\n        Check_Style: '0',\n\n        Working_Team_Ids: [],\n        Remark: '',\n        Workload_Proportion: ''\n      },\n      ZL_Check_UserIds: [],\n      TC_Check_UserIds: [],\n      CheckChange: true,\n      userOptions: [],\n      optionsUserList: [],\n      optionsGroupList: [],\n      optionsWorkingTeamsList: [],\n      rules: {\n        Code: [\n          { required: true, message: '请输入代号', trigger: 'blur' },\n          { max: 30, message: '长度在 30 个字符内', trigger: 'blur' }\n        ],\n        Name: [\n          { required: true, message: '请输入名称', trigger: 'blur' },\n          { max: 30, message: '长度在 30 个字符内', trigger: 'blur' }\n        ],\n        Bom_Level: [{ required: true, message: '请选择类型', trigger: 'change' }],\n        Sort: [{ required: true, message: '请输入', trigger: 'blur' }],\n        Is_Need_Check: [\n          { required: true, message: '请选择是否质检', trigger: 'change' }\n        ]\n      },\n      bomList: [],\n      comName: '',\n      partName: ''\n    }\n  },\n  computed: {\n    ...mapGetters('tenant', ['isVersionFour'])\n  },\n  async created() {\n    const { comName, partName, list } = await GetBOMInfo()\n    this.comName = comName\n    this.partName = partName\n    this.bomList = list\n  },\n  mounted() {\n    this.getUserList()\n    this.getFactoryPeoplelist()\n    // this.getCheckGroupList();\n    this.getWorkingTeamsList()\n    this.type === 'edit' && this.initForm()\n  },\n  methods: {\n    initForm() {\n      const { Is_Nest, ...others } = this.rowInfo\n      this.form = Object.assign({}, others, { Is_Nest: !!Is_Nest })\n      this.form.Bom_Level = String(this.form.Bom_Level)\n      //  if(this.form.Type==2){\n      //   this.form.Types = '0'\n      //  }else if(this.form.Type==3){\n      //   let Types = this.radioList.find(v => ['1', '2','3'].includes(v.Code))?.Code\n      //   console.log('Types', Types)\n      //   console.log('this.radioList', this.radioList)\n      //   this.form.Types = Types\n      //  }else if(this.form.Type==1){\n      //   this.form.Types = '-1'\n      //  }\n      console.log('this.form', this.form)\n\n      // 处理历史数据多选问题\n      // if (this.form.Is_Need_Check) {\n      //   if (this.form.Check_Style === '1') {\n\n      //   } else {\n      //     this.CheckChange = !!this.form.Is_Need_TC\n      //     if (this.form.Is_Need_ZL && this.form.Is_Need_TC) {\n      //       this.form.Is_Need_TC = true\n      //       this.form.Is_Need_ZL = false\n      //     }\n      //   }\n      // }\n      this.ZL_Check_UserIds = this.form.ZL_Check_UserId\n        ? this.form.ZL_Check_UserId.split(',')\n        : []\n      this.TC_Check_UserIds = this.form.TC_Check_UserId\n        ? this.form.TC_Check_UserId.split(',')\n        : []\n    },\n    // 是否自检\n    radioSelfCheck(val) { },\n    // 是否互检\n    radioInterCheck(val) { },\n    // 获取设备类型\n    async getDictionaryDetailListByCode() {\n      await GetDictionaryDetailListByCode({ dictionaryCode: 'deviceType' }).then((res) => {\n        if (res.IsSucceed) {\n          this.deviceTypeList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n      console.log(' this.optionsGroupList', this.optionsGroupList)\n    },\n    getUserList() {\n      GetUserList({}).then((res) => {\n        if (res.IsSucceed) {\n          this.userOptions = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    getFactoryPeoplelist() {\n      GetFactoryPeoplelist({}).then((res) => {\n        if (res.IsSucceed) {\n          this.optionsUserList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    async getCheckGroupList() {\n      await GetCheckGroupList({}).then((res) => {\n        if (res.IsSucceed) {\n          this.optionsGroupList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n      console.log(' this.optionsGroupList', this.optionsGroupList)\n    },\n    getWorkingTeamsList() {\n      GetWorkingTeams({}).then((res) => {\n        if (res.IsSucceed) {\n          this.optionsWorkingTeamsList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    // 选择专检方式 抽检、全检\n    radioCheckStyleChange(val) {\n      // if (val === '0') {\n      //   this.form.Is_Need_TC = true\n      //   this.form.Is_Need_ZL = false\n      // }\n      this.ZL_Check_UserIds = []\n      this.TC_Check_UserIds = []\n      this.form.ZL_Check_UserId = ''\n      this.form.TC_Check_UserId = ''\n    },\n    // 是否专检\n    radioChange(val) {\n      if (val === false) {\n        this.form.checkChange = false\n        this.form.Is_Need_TC = false\n        this.form.Is_Need_ZL = false\n        this.TC_Check_UserIds = []\n        this.ZL_Check_UserIds = []\n        this.form.ZL_Check_UserId = ''\n        this.form.TC_Check_UserId = ''\n        this.form.Check_Style = ''\n      } else {\n        // this.form.checkChange = true\n        // this.form.Is_Need_TC = true\n        // this.form.Is_Need_ZL = false\n        // this.CheckChange = !!this.form.Is_Need_TC\n        this.form.Check_Style = '0'\n      }\n    },\n    // 选择BOM层级\n    changeType(val) {\n      // const Code = val\n      // console.log(Code, 'Code');\n      // if (Code === '-1') {\n      //   this.form.Type = 1\n      // } else if (Code === '0') {\n      //   this.form.Type = 2\n      // } else if (Code === '1' || Code === '3'|| Code === '2') {\n      //   this.form.Type = 3\n      // }\n      // if (this.form.Type === 1 || this.form.Type === 3) {\n      //   this.form.Is_Cutting = undefined\n      // } else if (this.form.Type === 2) {\n      //   this.form.Is_Welding_Assembling = undefined\n      // }\n    },\n    typeChange() {\n      this.form.Task_Model = ''\n    },\n    changeTc(val) {\n      console.log(val)\n      this.form.TC_Check_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i === val.length - 1) {\n          this.form.TC_Check_UserId += val[i]\n        } else {\n          this.form.TC_Check_UserId += val[i] + ','\n        }\n      }\n      console.log(this.form.TC_Check_UserId, 'this.form.TC_Check_UserId ')\n    },\n    changeZL(val) {\n      console.log(val)\n      this.form.ZL_Check_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i === val.length - 1) {\n          this.form.ZL_Check_UserId += val[i]\n        } else {\n          this.form.ZL_Check_UserId += val[i] + ','\n        }\n      }\n    },\n    checkboxChange(val, type) {\n      if (type === 1) {\n        if (!val) {\n          this.TC_Check_UserIds = []\n        }\n      }\n      if (type === 2) {\n        if (!val) {\n          this.ZL_Check_UserIds = []\n        }\n      }\n    },\n    handleSubmit() {\n      // delete this.form.Types\n      console.log(this.form, 'this.form')\n      this.$refs.form.validate((valid) => {\n        if (!valid) return\n        this.btnLoading = true\n        const uItems = this.optionsUserList.find(\n          (v) => v.Id === this.form.Coordinate_UserId\n        )\n        if (uItems) {\n          this.form.Coordinate_UserName = uItems.Display_Name\n        }\n        if (this.form.Is_Need_Check) {\n          if (this.form.Is_Need_ZL === false && this.form.Is_Need_TC === false) {\n            this.$message.error('请选择质检类型')\n            this.btnLoading = false\n            return\n          }\n        } else {\n          this.form.Check_Style = null\n          this.form.Check_Group_List = []\n        }\n        const ZL = this.form.Is_Need_ZL ? this.form.ZL_Check_UserId : ''\n        const TC = this.form.Is_Need_TC ? this.form.TC_Check_UserId : ''\n        if (this.form.Is_Need_ZL && (ZL ?? '') === '') {\n          this.$message.error('请选择质检员')\n          this.btnLoading = false\n          return\n        }\n        if (this.form.Is_Need_TC && (TC ?? '') === '') {\n          this.$message.error('请选择探伤员')\n          this.btnLoading = false\n          return\n        }\n\n        AddWorkingProcess({\n          ...this.form,\n          ZL_Check_UserId: ZL,\n          TC_Check_UserId: TC\n        }).then((res) => {\n          if (res.IsSucceed) {\n            this.$message({\n              message: '保存成功',\n              type: 'success'\n            })\n            this.$emit('refresh')\n            this.$emit('close')\n          } else {\n            this.$message({\n              message: res.Message,\n              type: 'error'\n            })\n          }\n          this.btnLoading = false\n        })\n      })\n    },\n\n    codeChange(e) {\n      return e.replace(/[^a-zA-Z0-9]/g, '')\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/mixin.scss\";\n\n.btn-del {\n  margin-left: -100px;\n}\n\n.customRadioClass {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.checkboxFlex {\n  display: flex;\n  align-items: center;\n}\n\n.form-wrapper {\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  min-height: 40vh;\n\n  .form-x {\n    max-height: 70vh;\n    overflow: auto;\n    padding-right: 16px;\n    @include scrollBar;\n  }\n\n  .btn-x {\n    padding-top: 16px;\n    text-align: right;\n  }\n}\n</style>\n"]}]}
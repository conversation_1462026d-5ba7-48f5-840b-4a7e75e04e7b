{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\component\\TreeData.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\component\\TreeData.vue", "mtime": 1757468112480}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["TreeData.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TreeData.vue", "sourceRoot": "src/views/PRO/bom-setting/structure-type-config/component", "sourcesContent": ["<template>\r\n  <div :key=\"activeType\" class=\"tree-container\">\r\n    <div class=\"title\">\r\n      <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增</el-button>\r\n    </div>\r\n    <div class=\"tree-wrapper\">\r\n      <el-tree\r\n        ref=\"tree\"\r\n        v-loading=\"loading\"\r\n        :current-node-key=\"currentNodeKey\"\r\n        element-loading-text=\"加载中\"\r\n        element-loading-spinner=\"el-icon-loading\"\r\n        empty-text=\"暂无数据\"\r\n        highlight-current\r\n        node-key=\"Id\"\r\n        default-expand-all\r\n        :expand-on-click-node=\"false\"\r\n        :data=\"treeData\"\r\n        :props=\"{\r\n          label:'Label',\r\n          children:'Children'\r\n        }\"\r\n        @node-click=\"handleNodeClick\"\r\n      >\r\n        <span slot-scope=\"{ node, data }\" class=\"custom-tree-node\">\r\n          <svg-icon\r\n            :icon-class=\"\r\n              node.expanded ? 'icon-folder-open' : 'icon-folder'\r\n            \"\r\n            class-name=\"class-icon\"\r\n          />\r\n          <span class=\"cs-label\" :title=\"node.label\">{{ node.label }}</span>\r\n        </span>\r\n      </el-tree>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetCompTypeTree } from '@/api/PRO/component-type'\r\nimport { GetPartTypeTree } from '@/api/PRO/partType'\r\n\r\nexport default {\r\n  props: {\r\n    typeCode: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    typeId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    activeType: {\r\n      type: String,\r\n      default: '-1'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      treeData: [],\r\n      bomList: [],\r\n      loading: true,\r\n      currentNodeKey: ''\r\n\r\n    }\r\n  },\r\n  watch: {\r\n    // currentNodeKey(newValue) {\r\n    //   this.$emit('showRight', newValue !== '')\r\n    // }\r\n    typeId: {\r\n      handler() {\r\n        this.currentNodeKey = ''\r\n        this.fetchData()\r\n      },\r\n      immediate: false\r\n    },\r\n    activeType(newValue) {\r\n      this.currentNodeKey = ''\r\n      this.fetchData()\r\n    }\r\n  },\r\n  async mounted() {\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n\r\n    async fetchData() {\r\n      if (!this.typeCode || !this.typeId) {\r\n        return\r\n      }\r\n      this.loading = true\r\n      console.log(this.activeType, 3313)\r\n      let res\r\n      try {\r\n        if (this.activeType === '-1') {\r\n          res = await GetCompTypeTree({ professional: this.typeCode })\r\n        } else {\r\n          res = await GetPartTypeTree({ professionalId: this.typeId, partGrade: this.activeType.toString() })\r\n        }\r\n        if (res.IsSucceed) {\r\n          this.treeData = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.treeData = []\r\n        }\r\n      } catch (error) {\r\n        this.treeData = []\r\n      }\r\n      this.loading = false\r\n      this.currentNodeKey && this.setTreeNode()\r\n    },\r\n    setTreeNode() {\r\n      this.$emit('nodeClick', this.$refs['tree'].getNode(this.currentNodeKey))\r\n      this.$nextTick(_ => {\r\n        this.$refs['tree'].setCurrentKey(this.currentNodeKey)\r\n      })\r\n    },\r\n    handleAdd() {\r\n      this.$emit('AddFirst')\r\n    },\r\n    handleNodeClick(data, node) {\r\n      this.currentNodeKey = data.Id\r\n      this.$emit('nodeClick', node)\r\n    },\r\n    resetKey(id) {\r\n      if (id === this.currentNodeKey) {\r\n        this.currentNodeKey = ''\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import '~@/styles/mixin.scss';\r\n\r\n.tree-container{\r\n  height: 100%;\r\n  margin-right: 16px;\r\n  flex-basis: 20%;\r\n  background-color: #FFFFFF;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .title{\r\n    border-bottom: 1px solid #EEEEEE;\r\n    font-weight: 500;\r\n    padding: 0 16px 16px 16px;\r\n    color: #333333;\r\n    // display: flex;\r\n    // justify-content: space-between;\r\n    .title-name {\r\n      height: 30px;\r\n      line-height: 30px;\r\n    }\r\n    .btn-x{\r\n      padding: 0 0;\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  .tree-wrapper{\r\n    padding: 16px;\r\n    flex: 1;\r\n    overflow: hidden;\r\n    .el-tree{\r\n      @include scrollBar;\r\n      height: 100%;\r\n      overflow: auto;\r\n    }\r\n    .cs-label{\r\n      font-size: 14px;\r\n      margin-left: 4px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
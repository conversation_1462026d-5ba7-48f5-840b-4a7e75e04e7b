{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\part-list\\v4\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\part-list\\v4\\index.vue", "mtime": 1757583738731}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBEZWxldGVwYXJ0LA0KICBHZXRQYXJ0V2VpZ2h0TGlzdCwNCiAgRXhwb3J0UGxhbnBhcnRJbmZvLA0KICBFeHBvcnRQbGFucGFydGNvdW50SW5mbywNCiAgRGVsZXRlcGFydEJ5ZmluZGtleXdvZGVzDQp9IGZyb20gJ0AvYXBpL3BsbS9wcm9kdWN0aW9uJw0KaW1wb3J0IHsgR2V0UGFydFBhZ2VMaXN0IH0gZnJvbSAnQC9hcGkvcGxtL2NvbXBvbmVudCcNCmltcG9ydCB7IEdldEdyaWRCeUNvZGUgfSBmcm9tICdAL2FwaS9zeXMnDQppbXBvcnQgeyBHZXRGYWN0b3J5UHJvZmVzc2lvbmFsQnlDb2RlIH0gZnJvbSAnQC9hcGkvUFJPL3Byb2Zlc3Npb25hbFR5cGUnDQppbXBvcnQgew0KICBHZXRQcm9qZWN0QXJlYVRyZWVMaXN0LA0KICBHZXRJbnN0YWxsVW5pdElkTmFtZUxpc3QNCn0gZnJvbSAnQC9hcGkvUFJPL3Byb2plY3QnDQoNCmltcG9ydCBUcmVlRGV0YWlsIGZyb20gJ0AvY29tcG9uZW50cy9UcmVlRGV0YWlsJw0KaW1wb3J0IFRvcEhlYWRlciBmcm9tICdAL2NvbXBvbmVudHMvVG9wSGVhZGVyJw0KaW1wb3J0IGNvbUltcG9ydCBmcm9tICcuL2NvbXBvbmVudC9JbXBvcnQnDQppbXBvcnQgQ29tcG9uZW50c0hpc3RvcnkgZnJvbSAnLi9jb21wb25lbnQvQ29tcG9uZW50c0hpc3RvcnknDQppbXBvcnQgY29tSW1wb3J0QnlGYWN0b3J5IGZyb20gJy4vY29tcG9uZW50L0ltcG9ydEJ5RmFjdG9yeScNCmltcG9ydCBIaXN0b3J5RXhwb3J0IGZyb20gJy4vY29tcG9uZW50L0hpc3RvcnlFeHBvcnQnDQppbXBvcnQgQmF0Y2hFZGl0IGZyb20gJy4vY29tcG9uZW50L0JhdGNoRWRpdG9yJw0KaW1wb3J0IENvbXBvbmVudFBhY2sgZnJvbSAnLi9jb21wb25lbnQvQ29tcG9uZW50UGFjay9pbmRleCcNCmltcG9ydCBFZGl0IGZyb20gJy4vY29tcG9uZW50L0VkaXQnDQppbXBvcnQgT25lQ2xpY2tHZW5lcmF0ZVBhY2sgZnJvbSAnLi9jb21wb25lbnQvT25lQ2xpY2tHZW5lcmF0ZVBhY2snDQppbXBvcnQgR2VuZXJhdGVQYWNrIGZyb20gJy4vY29tcG9uZW50L0dlbmVyYXRlUGFjaycNCmltcG9ydCBEZWVwTWF0ZXJpYWwgZnJvbSAnLi9jb21wb25lbnQvRGVlcE1hdGVyaWFsJw0KaW1wb3J0IFNjaGR1bGluZyBmcm9tICcuL2NvbXBvbmVudC9TY2hkdWxpbmcnDQppbXBvcnQgUGFydFNwbGl0IGZyb20gJy4vY29tcG9uZW50L1BhcnRTcGxpdCcNCmltcG9ydCBQcm9jZXNzRGF0YSBmcm9tICcuL2NvbXBvbmVudC9Qcm9jZXNzRGF0YS52dWUnDQoNCmltcG9ydCBlbERyYWdEaWFsb2cgZnJvbSAnQC9kaXJlY3RpdmUvZWwtZHJhZy1kaWFsb2cnDQppbXBvcnQgUGFnaW5hdGlvbiBmcm9tICdAL2NvbXBvbmVudHMvUGFnaW5hdGlvbicNCmltcG9ydCB7IHRpbWVGb3JtYXQgfSBmcm9tICdAL2ZpbHRlcnMnDQovLyBpbXBvcnQgeyBDb2x1bW4sIEhlYWRlciwgVGFibGUsIFRvb2x0aXAgfSBmcm9tICd2eGUtdGFibGUnDQovLyBpbXBvcnQgVnVlIGZyb20gJ3Z1ZScNCmltcG9ydCBBdXRoQnV0dG9ucyBmcm9tICdAL21peGlucy9hdXRoLWJ1dHRvbnMnDQppbXBvcnQgYmltZGlhbG9nIGZyb20gJy4vY29tcG9uZW50L2JpbWRpYWxvZycNCmltcG9ydCBzeXNVc2VUeXBlIGZyb20gJ0AvZGlyZWN0aXZlL3N5cy11c2UtdHlwZS9pbmRleC5qcycNCmltcG9ydCB7IHByb21wdEJveCB9IGZyb20gJy4vY29tcG9uZW50L21lc3NhZ2VCb3gnDQoNCmltcG9ydCB7IGNvbWJpbmVVUkwgfSBmcm9tICdAL3V0aWxzJw0KaW1wb3J0IHsgdGFibGVQYWdlU2l6ZSB9IGZyb20gJ0Avdmlld3MvUFJPL3NldHRpbmcnDQppbXBvcnQgeyBwYXJzZU9zc1VybCB9IGZyb20gJ0AvdXRpbHMvZmlsZScNCmltcG9ydCB7IEdldFBhcnRUeXBlTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9wYXJ0VHlwZScNCmltcG9ydCB7IGJhc2VVcmwgfSBmcm9tICdAL3V0aWxzL2Jhc2V1cmwnDQppbXBvcnQgeyB2NCBhcyB1dWlkdjQgfSBmcm9tICd1dWlkJw0KaW1wb3J0IHsgR2V0U3RlZWxDYWRBbmRCaW1JZCB9IGZyb20gJ0AvYXBpL1BSTy9jb21wb25lbnQnDQppbXBvcnQgeyBnZXRDb25maWd1cmUgfSBmcm9tICdAL2FwaS91c2VyJw0KaW1wb3J0IHsgR2V0RmlsZVR5cGUgfSBmcm9tICdAL2FwaS9zeXMnDQppbXBvcnQgRXhwYW5kYWJsZVNlY3Rpb24gZnJvbSAnQC9jb21wb25lbnRzL0V4cGFuZGFibGVTZWN0aW9uL2luZGV4LnZ1ZScNCmltcG9ydCBjb21EcmF3ZGlhbG9nIGZyb20gJ0Avdmlld3MvUFJPL3Byb2R1Y3Rpb24tb3JkZXIvZGVlcGVuLWZpbGVzL2RpYWxvZycgLy8g5rex5YyW5paH5Lu2LembtuS7tuivpuWbvuWvvOWFpQ0KaW1wb3J0IFRyYWNlUGxvdCBmcm9tICcuL2NvbXBvbmVudC9UcmFjZVBsb3QnDQppbXBvcnQgeyBHZXRTdG9wTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9wcm9kdWN0aW9uLXRhc2snDQppbXBvcnQgbW9kZWxEcmF3aW5nIGZyb20gJ0Avdmlld3MvUFJPL2NvbXBvbmVudHMvbW9kZWxEcmF3aW5nLnZ1ZScNCmltcG9ydCB7IEdldEJPTUluZm8gfSBmcm9tICdAL3ZpZXdzL1BSTy9ib20tc2V0dGluZy91dGlscycNCi8vIFZ1ZS51c2UoSGVhZGVyKS51c2UoQ29sdW1uKS51c2UoVG9vbHRpcCkudXNlKFRhYmxlKQ0KY29uc3QgU1BMSVRfU1lNQk9MID0gJyRfJCcNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ1BST1BhcnRMaXN0JywNCiAgZGlyZWN0aXZlczogeyBlbERyYWdEaWFsb2csIHN5c1VzZVR5cGUgfSwNCiAgY29tcG9uZW50czogew0KICAgIEV4cGFuZGFibGVTZWN0aW9uLA0KICAgIFRyZWVEZXRhaWwsDQogICAgVG9wSGVhZGVyLA0KICAgIGNvbUltcG9ydCwNCiAgICBjb21JbXBvcnRCeUZhY3RvcnksDQogICAgQmF0Y2hFZGl0LA0KICAgIEhpc3RvcnlFeHBvcnQsDQogICAgR2VuZXJhdGVQYWNrLA0KICAgIEVkaXQsDQogICAgQ29tcG9uZW50UGFjaywNCiAgICBPbmVDbGlja0dlbmVyYXRlUGFjaywNCiAgICBQYWdpbmF0aW9uLA0KICAgIGJpbWRpYWxvZywNCiAgICBDb21wb25lbnRzSGlzdG9yeSwNCiAgICBEZWVwTWF0ZXJpYWwsDQogICAgU2NoZHVsaW5nLA0KICAgIGNvbURyYXdkaWFsb2csDQogICAgVHJhY2VQbG90LA0KICAgIFBhcnRTcGxpdCwNCiAgICBtb2RlbERyYXdpbmcsDQogICAgUHJvY2Vzc0RhdGENCiAgfSwNCiAgbWl4aW5zOiBbQXV0aEJ1dHRvbnNdLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBhbGxTdG9wRmxhZzogZmFsc2UsDQogICAgICBzaG93RXhwYW5kOiB0cnVlLA0KICAgICAgZHJhd2VyOiBmYWxzZSwNCiAgICAgIGRyYXdlcnN1bGw6IGZhbHNlLA0KICAgICAgaWZyYW1lS2V5OiAnJywNCiAgICAgIGZ1bGxzY3JlZW5pZDogJycsDQogICAgICBpZnJhbWVVcmw6ICcnLA0KICAgICAgZnVsbGJpbWlkOiAnJywNCiAgICAgIGV4cGFuZGVkS2V5OiAnJywgLy8gLTHmmK/lhajpg6gNCiAgICAgIHRhYmxlUGFnZVNpemU6IHRhYmxlUGFnZVNpemUsDQogICAgICBwYXJ0VHlwZU9wdGlvbjogW10sDQogICAgICB0cmVlRGF0YTogW10sDQogICAgICB0cmVlTG9hZGluZzogdHJ1ZSwNCiAgICAgIHByb2plY3ROYW1lOiAnJywNCiAgICAgIHN0YXR1c1R5cGU6ICcnLA0KICAgICAgc2VhcmNoSGVpZ2h0OiAwLA0KICAgICAgdGJEYXRhOiBbXSwNCiAgICAgIHRvdGFsOiAwLA0KICAgICAgdGJMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHBnTG9hZGluZzogZmFsc2UsDQogICAgICBjb3VudExvYWRpbmc6IGZhbHNlLA0KICAgICAgcXVlcnlJbmZvOiB7DQogICAgICAgIFBhZ2U6IDEsDQogICAgICAgIFBhZ2VTaXplOiAxMCwNCiAgICAgICAgUGFyYW1ldGVySnNvbjogW10NCiAgICAgIH0sDQogICAgICBjdXN0b21QYWdlU2l6ZTogWzEwLCAyMCwgNTAsIDEwMF0sDQogICAgICBpbnN0YWxsVW5pdElkTmFtZUxpc3Q6IFtdLCAvLyDmibnmrKHmlbDnu4QNCiAgICAgIG5hbWVNb2RlOiAxLA0KICAgICAgbW9udGFnZU9wdGlvbjogWw0KICAgICAgICB7IHZhbHVlOiB0cnVlLCBsYWJlbDogJ+aYrycgfSwNCiAgICAgICAgeyB2YWx1ZTogZmFsc2UsIGxhYmVsOiAn5ZCmJyB9DQogICAgICBdLA0KICAgICAgY3VzdG9tUGFyYW1zOiB7DQogICAgICAgIFR5cGVJZDogJycsDQogICAgICAgIFR5cGVfTmFtZTogJycsDQogICAgICAgIENvZGU6ICcnLA0KICAgICAgICBDb2RlX0xpa2U6ICcnLA0KICAgICAgICBTcGVjOiAnJywNCiAgICAgICAgRGF0ZU5hbWU6ICcnLA0KICAgICAgICBUZXh0dXJlOiAnJywNCiAgICAgICAgLy8gS2V5d29yZHMwMTogJ0NvZGUnLA0KICAgICAgICAvLyBLZXl3b3JkczAxVmFsdWU6ICcnLA0KICAgICAgICAvLyBLZXl3b3JkczAyOiAnU3BlYycsDQogICAgICAgIC8vIEtleXdvcmRzMDJWYWx1ZTogJycsDQogICAgICAgIC8vIEtleXdvcmRzMDM6ICdMZW5ndGgnLA0KICAgICAgICAvLyBLZXl3b3JkczAzVmFsdWU6ICcnLA0KICAgICAgICAvLyBLZXl3b3JkczA0OiAnVGV4dHVyZScsDQogICAgICAgIC8vIEtleXdvcmRzMDRWYWx1ZTogJycsDQogICAgICAgIGlzTW9udGFnZTogbnVsbCwNCiAgICAgICAgSW5zdGFsbFVuaXRfSWQ6IFtdLA0KICAgICAgICBQYXJ0X1R5cGVfSWQ6ICcnLA0KICAgICAgICBJbnN0YWxsVW5pdF9OYW1lOiAnJywNCiAgICAgICAgU3lzX1Byb2plY3RfSWQ6ICcnLA0KICAgICAgICBQcm9qZWN0X0lkOiAnJywNCiAgICAgICAgQXJlYV9JZDogJycsDQogICAgICAgIFByb2plY3RfTmFtZTogJycsDQogICAgICAgIEFyZWFfTmFtZTogJycNCiAgICAgIH0sDQogICAgICBuYW1lczogJycsDQogICAgICBjdXN0b21EaWFsb2dQYXJhbXM6IHt9LA0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBjdXJyZW50Q29tcG9uZW50OiAnJywNCiAgICAgIHNlbGVjdExpc3Q6IFtdLA0KICAgICAgZmFjdG9yeU9wdGlvbjogW10sDQogICAgICBwcm9qZWN0TGlzdDogW10sDQogICAgICB0eXBlT3B0aW9uOiBbXSwNCiAgICAgIGNvbHVtbnM6IFtdLA0KICAgICAgY29sdW1uc09wdGlvbjogWw0KICAgICAgICAvLyB7IERpc3BsYXlfTmFtZTogJ+mbtuS7tuWQjeensCcsIENvZGU6ICdDb2RlJyB9LA0KICAgICAgICAvLyB7IERpc3BsYXlfTmFtZTogJ+inhOagvCcsIENvZGU6ICdTcGVjJyB9LA0KICAgICAgICAvLyB7IERpc3BsYXlfTmFtZTogJ+mVv+W6picsIENvZGU6ICdMZW5ndGgnIH0sDQogICAgICAgIC8vIHsgRGlzcGxheV9OYW1lOiAn5p2Q6LSoJywgQ29kZTogJ1RleHR1cmUnIH0sDQogICAgICAgIC8vIHsgRGlzcGxheV9OYW1lOiAn5rex5YyW5pWw6YePJywgQ29kZTogJ051bScgfSwNCiAgICAgICAgLy8geyBEaXNwbGF5X05hbWU6ICfmjpLkuqfmlbDph48nLCBDb2RlOiAnU2NoZHVsaW5nX0NvdW50JyB9LA0KICAgICAgICAvLyB7IERpc3BsYXlfTmFtZTogJ+WNlemHjScsIENvZGU6ICdXZWlnaHQnIH0sDQogICAgICAgIC8vIHsgRGlzcGxheV9OYW1lOiAn5oC76YeNJywgQ29kZTogJ1RvdGFsX1dlaWdodCcgfSwNCiAgICAgICAgLy8geyBEaXNwbGF5X05hbWU6ICflvaLnirYnLCBDb2RlOiAnU2hhcGUnIH0sDQogICAgICAgIC8vIHsgRGlzcGxheV9OYW1lOiAn5p6E5Lu25ZCN56ewJywgQ29kZTogJ0NvbXBvbmVudF9Db2RlJyB9LA0KICAgICAgICAvLyB7IERpc3BsYXlfTmFtZTogJ+aTjeS9nOS6uicsIENvZGU6ICdkYXRlbmFtZScgfSwNCiAgICAgICAgLy8geyBEaXNwbGF5X05hbWU6ICfmk43kvZzml7bpl7QnLCBDb2RlOiAnRXhkYXRlJyB9DQogICAgICBdLA0KICAgICAgdGl0bGU6ICcnLA0KICAgICAgd2lkdGg6ICc2MCUnLA0KICAgICAgdGlwTGFiZWw6ICcnLA0KICAgICAgbW9ub21lckxpc3Q6IFtdLA0KICAgICAgbW9kZTogJycsDQogICAgICBpc01vbm9tZXI6IHRydWUsDQogICAgICBoaXN0b3J5VmlzaWJsZTogZmFsc2UsDQogICAgICBzeXNVc2VUeXBlOiB1bmRlZmluZWQsDQogICAgICBkZWxldGVDb250ZW50OiB0cnVlLA0KICAgICAgU3RlZWxBbW91bnRUb3RhbDogMCwgLy8g5rex5YyW5oC76YePDQogICAgICBTY2hlZHVsaW5nTnVtVG90YWw6IDAsIC8vIOaOkuS6p+aAu+mHjw0KICAgICAgU3RlZWxBbGxXZWlnaHRUb3RhbDogMCwgLy8g5rex5YyW5oC76YeNDQogICAgICBTY2hlZHVsaW5nQWxsV2VpZ2h0VG90YWw6IDAsIC8vIOaOkuS6p+aAu+mHjQ0KICAgICAgRmluaXNoQ291bnRUb3RhbDogMCwgLy8g5a6M5oiQ5pWw6YePDQogICAgICBGaW5pc2hXZWlnaHRUb3RhbDogMCwgLy8g5a6M5oiQ6YeN6YePDQogICAgICBVbml0OiAnJywNCiAgICAgIFByb3BvcnRpb246IDAsIC8vIOS4k+S4mueahOWNleS9jeaNoueulw0KICAgICAgY29tbWFuZDogJ2NvdmVyJywNCiAgICAgIGN1cnJlbnRMYXN0TGV2ZWw6IGZhbHNlLA0KICAgICAgdGVtcGxhdGVVcmw6ICcnLA0KICAgICAgY3VycmVudE5vZGU6IHt9LA0KICAgICAgY29tRHJhd0RhdGE6IHt9LA0KICAgICAgdHJhY2tEcmF3ZXI6IGZhbHNlLA0KICAgICAgdHJhY2tEcmF3ZXJUaXRsZTogJycsDQogICAgICB0cmFja0RyYXdlckRhdGE6IHt9LA0KICAgICAgbGV2ZWxOYW1lOiAnJywNCiAgICAgIGxldmVsQ29kZTogJycNCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgc2hvd1A5QnRuKCkgew0KICAgICAgcmV0dXJuIHRoaXMuQXV0aEJ1dHRvbnMuYnV0dG9ucy5zb21lKChpdGVtKSA9PiBpdGVtLkNvZGUgPT09ICdwOUJ0bkFkZCcpDQogICAgfSwNCiAgICB0eXBlRW50aXR5KCkgew0KICAgICAgcmV0dXJuIHRoaXMudHlwZU9wdGlvbi5maW5kKChpKSA9PiBpLklkID09PSB0aGlzLmN1c3RvbVBhcmFtcy5UeXBlSWQpDQogICAgfSwNCiAgICBQSUQoKSB7DQogICAgICByZXR1cm4gdGhpcy5wcm9qZWN0TGlzdC5maW5kKA0KICAgICAgICAoaSkgPT4gaS5TeXNfUHJvamVjdF9JZCA9PT0gdGhpcy5jdXN0b21QYXJhbXMuUHJvamVjdF9JZA0KICAgICAgKT8uSWQNCiAgICB9LA0KICAgIGZpbHRlclRleHQoKSB7DQogICAgICByZXR1cm4gdGhpcy5wcm9qZWN0TmFtZSArIFNQTElUX1NZTUJPTCArIHRoaXMuc3RhdHVzVHlwZQ0KICAgIH0NCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICAnY3VzdG9tUGFyYW1zLlR5cGVJZCc6IGZ1bmN0aW9uKG5ld1ZhbHVlLCBvbGRWYWx1ZSkgew0KICAgICAgY29uc29sZS5sb2coeyBvbGRWYWx1ZSB9KQ0KICAgICAgaWYgKG9sZFZhbHVlICYmIG9sZFZhbHVlICE9PSAnMCcpIHsNCiAgICAgICAgdGhpcy5mZXRjaERhdGEoKQ0KICAgICAgfQ0KICAgIH0sDQogICAgbmFtZXMobiwgbykgew0KICAgICAgdGhpcy5jaGFuZ2VNb2RlKCkNCiAgICB9LA0KICAgIG5hbWVNb2RlKG4sIG8pIHsNCiAgICAgIHRoaXMuY2hhbmdlTW9kZSgpDQogICAgfQ0KICB9LA0KICBtb3VudGVkKCkgew0KDQogIH0sDQogIGFzeW5jIGNyZWF0ZWQoKSB7DQogICAgY29uc3QgeyBjdXJyZW50Qk9NSW5mbyB9ID0gYXdhaXQgR2V0Qk9NSW5mbygwKQ0KICAgIGNvbnNvbGUubG9nKCdsaXN0JywgY3VycmVudEJPTUluZm8pDQogICAgdGhpcy5sZXZlbE5hbWUgPSBjdXJyZW50Qk9NSW5mbz8uRGlzcGxheV9OYW1lDQogICAgdGhpcy5sZXZlbENvZGUgPSBjdXJyZW50Qk9NSW5mbz8uQ29kZQ0KICAgIGF3YWl0IHRoaXMuZ2V0VHlwZUxpc3QoKQ0KICAgIC8vIGF3YWl0IHRoaXMuZmV0Y2hEYXRhKCkNCiAgICBhd2FpdCB0aGlzLmdldFRhYmxlQ29uZmlnKCdwbG1fcGFydHNfcGFnZV9saXN0JykNCg0KICAgIHRoaXMuZmV0Y2hUcmVlRGF0YSgpDQogICAgdGhpcy5nZXRGaWxlVHlwZSgpDQogICAgaWYgKHRoaXMuS2V5d29yZHMwMVZhbHVlID09PSAn5pivJykgew0KICAgICAgY29uc29sZS5sb2coJ3RoaXMuS2V5d29yZHMwMVZhbHVlJywgdGhpcy5LZXl3b3JkczAxVmFsdWUpDQogICAgfQ0KICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgIHRoaXMucGdMb2FkaW5nID0gdHJ1ZQ0KICAgICAgY29uc29sZS5sb2codGhpcy5jb2x1bW5zKQ0KICAgICAgdGhpcy5nZXRQYXJ0V2VpZ2h0TGlzdCgpDQogICAgICB0aGlzLmdldFBhcnRUeXBlKCkNCiAgICAgIHRoaXMuc2VhcmNoSGVpZ2h0ID0gdGhpcy4kcmVmcy5zZWFyY2hEb20ub2Zmc2V0SGVpZ2h0ICsgMzI3DQogICAgfSkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGNoYW5nZU1vZGUoKSB7DQogICAgICBpZiAodGhpcy5uYW1lTW9kZSA9PT0gMSkgew0KICAgICAgICB0aGlzLmN1c3RvbVBhcmFtcy5Db2RlX0xpa2UgPSB0aGlzLm5hbWVzDQogICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLkNvZGUgPSAnJw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5jdXN0b21QYXJhbXMuQ29kZV9MaWtlID0gJycNCiAgICAgICAgdGhpcy5jdXN0b21QYXJhbXMuQ29kZSA9IHRoaXMubmFtZXMucmVwbGFjZSgvXHMrL2csICdcbicpDQogICAgICB9DQogICAgfSwNCiAgICAvLyDpobnnm67ljLrln5/mlbDmja7pm4YNCiAgICBmZXRjaFRyZWVEYXRhKCkgew0KICAgICAgR2V0UHJvamVjdEFyZWFUcmVlTGlzdCh7IE1lbnVJZDogdGhpcy4kcm91dGUubWV0YS5JZCwgVHlwZTogMCwgcHJvamVjdE5hbWU6IHRoaXMucHJvamVjdE5hbWUsIExldmVsOiB0aGlzLmxldmVsQ29kZSB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgLy8gY29uc3QgcmVzQWxsID0gWw0KICAgICAgICAvLyAgIHsNCiAgICAgICAgLy8gICAgIFBhcmVudE5vZGVzOiBudWxsLA0KICAgICAgICAvLyAgICAgSWQ6ICctMScsDQogICAgICAgIC8vICAgICBDb2RlOiAn5YWo6YOoJywNCiAgICAgICAgLy8gICAgIExhYmVsOiAn5YWo6YOoJywNCiAgICAgICAgLy8gICAgIExldmVsOiBudWxsLA0KICAgICAgICAvLyAgICAgRGF0YToge30sDQogICAgICAgIC8vICAgICBDaGlsZHJlbjogW10NCiAgICAgICAgLy8gICB9DQogICAgICAgIC8vIF0NCiAgICAgICAgLy8gY29uc3QgcmVzRGF0YSA9IHJlc0FsbC5jb25jYXQocmVzLkRhdGEpDQogICAgICAgIGlmIChyZXMuRGF0YS5sZW5ndGggPT09IDApIHsNCiAgICAgICAgICB0aGlzLnRyZWVMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICB0aGlzLnBnTG9hZGluZyA9IGZhbHNlDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCiAgICAgICAgY29uc3QgcmVzRGF0YSA9IHJlcy5EYXRhDQogICAgICAgIHJlc0RhdGEubWFwKChpdGVtKSA9PiB7DQogICAgICAgICAgaWYgKGl0ZW0uQ2hpbGRyZW4ubGVuZ3RoID09PSAwKSB7DQogICAgICAgICAgICBpdGVtLklzX0ltcG9ydGVkID0gZmFsc2UNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgaXRlbS5EYXRhLklzX0ltcG9ydGVkID0gaXRlbS5DaGlsZHJlbi5zb21lKChpY2gpID0+IHsNCiAgICAgICAgICAgICAgcmV0dXJuIGljaC5EYXRhLklzX0ltcG9ydGVkID09PSB0cnVlDQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgaXRlbS5Jc19EaXJlY3RvcnkgPSB0cnVlDQogICAgICAgICAgICBpdGVtLkNoaWxkcmVuLm1hcCgoaXQpID0+IHsNCiAgICAgICAgICAgICAgaWYgKGl0LkNoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgICBpdC5Jc19EaXJlY3RvcnkgPSB0cnVlDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICB0aGlzLnRyZWVEYXRhID0gcmVzRGF0YQ0KICAgICAgICBpZiAoT2JqZWN0LmtleXModGhpcy5jdXJyZW50Tm9kZSkubGVuZ3RoID09PSAwKSB7DQogICAgICAgICAgLy8gdGhpcy5mZXRjaERhdGEoKQ0KICAgICAgICAgIHRoaXMuc2V0S2V5KCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmhhbmRsZU5vZGVDbGljayh0aGlzLmN1cnJlbnROb2RlKQ0KICAgICAgICB9DQogICAgICAgIHRoaXMudHJlZUxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAvLyB0aGlzLmV4cGFuZGVkS2V5ID0gdGhpcy5jdXN0b21QYXJhbXMuQXJlYV9JZCA/IHRoaXMuY3VzdG9tUGFyYW1zLkFyZWFfSWQgOiB0aGlzLmN1c3RvbVBhcmFtcy5Qcm9qZWN0X0lkID8gdGhpcy5jdXN0b21QYXJhbXMuUHJvamVjdF9JZCA6IHJlc0RhdGFbMF0uSWQgLy8gJy0xJw0KICAgICAgICAvLyB0aGlzLmN1c3RvbVBhcmFtcy5TeXNfUHJvamVjdF9JZCA9IHRoaXMuY3VzdG9tUGFyYW1zLlN5c19Qcm9qZWN0X0lkIHx8IHJlc0RhdGFbMF0uRGF0YS5TeXNfUHJvamVjdF9JZA0KICAgICAgICAvLyB0aGlzLmN1c3RvbVBhcmFtcy5Qcm9qZWN0X0lkID0gdGhpcy5jdXN0b21QYXJhbXMuUHJvamVjdF9JZCB8fCByZXNEYXRhWzBdLkRhdGEuSWQNCiAgICAgICAgLy8gdGhpcy5jdXN0b21QYXJhbXMuQXJlYV9OYW1lID0gJycNCiAgICAgICAgLy8gdGhpcy50cmVlTG9hZGluZyA9IGZhbHNlDQogICAgICAgIC8vIHRoaXMuZmV0Y2hEYXRhKCkNCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyDorr7nva7pu5jorqTpgInkuK3nrKzkuIDkuKrljLrln5/mnKvnuqfoioLngrkNCiAgICBzZXRLZXkoKSB7DQogICAgICBjb25zdCBkZWVwRmlsdGVyID0gKHRyZWUpID0+IHsNCiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0cmVlLmxlbmd0aDsgaSsrKSB7DQogICAgICAgICAgY29uc3QgaXRlbSA9IHRyZWVbaV0NCiAgICAgICAgICBjb25zdCB7IERhdGEsIENoaWxkcmVuIH0gPSBpdGVtDQogICAgICAgICAgY29uc29sZS5sb2coRGF0YSkNCiAgICAgICAgICBpZiAoRGF0YS5QYXJlbnRJZCAmJiAhQ2hpbGRyZW4/Lmxlbmd0aCkgew0KICAgICAgICAgICAgY29uc29sZS5sb2coRGF0YSwgJz8/Pz8nKQ0KICAgICAgICAgICAgdGhpcy5jdXJyZW50Tm9kZSA9IERhdGENCiAgICAgICAgICAgIHRoaXMuaGFuZGxlTm9kZUNsaWNrKGl0ZW0pDQogICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgaWYgKENoaWxkcmVuICYmIENoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgcmV0dXJuIGRlZXBGaWx0ZXIoQ2hpbGRyZW4pDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLmhhbmRsZU5vZGVDbGljayhpdGVtKQ0KICAgICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHJldHVybiBkZWVwRmlsdGVyKHRoaXMudHJlZURhdGEpDQogICAgfSwNCiAgICAvLyDpgInkuK3lt6bkvqfpobnnm67oioLngrkNCiAgICBoYW5kbGVOb2RlQ2xpY2soZGF0YSkgew0KICAgICAgdGhpcy5oYW5kZWxzZWFyY2goJ3Jlc2V0JywgZmFsc2UpDQogICAgICB0aGlzLmN1cnJlbnROb2RlID0gZGF0YQ0KICAgICAgdGhpcy5leHBhbmRlZEtleSA9IGRhdGEuSWQNCiAgICAgIGNvbnN0IGRhdGFJZCA9IGRhdGEuSWQgPT09ICctMScgPyAnJyA6IGRhdGEuSWQNCiAgICAgIGNvbnNvbGUubG9nKCdub2RlRGF0YScsIGRhdGEpDQogICAgICBpZiAoZGF0YS5QYXJlbnROb2Rlcykgew0KICAgICAgICB0aGlzLmN1c3RvbVBhcmFtcy5Qcm9qZWN0X0lkID0gZGF0YS5EYXRhLlByb2plY3RfSWQNCiAgICAgICAgdGhpcy5jdXN0b21QYXJhbXMuQXJlYV9JZCA9IGRhdGEuSWQNCiAgICAgICAgdGhpcy5jdXN0b21QYXJhbXMuQXJlYV9OYW1lID0gZGF0YS5EYXRhLk5hbWUNCiAgICAgICAgdGhpcy5jdXN0b21QYXJhbXMuU3lzX1Byb2plY3RfSWQgPSBkYXRhLkRhdGEuU3lzX1Byb2plY3RfSWQNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLlByb2plY3RfSWQgPSBkYXRhSWQNCiAgICAgICAgdGhpcy5jdXN0b21QYXJhbXMuQXJlYV9JZCA9ICcnDQogICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLkFyZWFfTmFtZSA9IGRhdGEuRGF0YS5OYW1lDQogICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLlN5c19Qcm9qZWN0X0lkID0gZGF0YS5EYXRhLlN5c19Qcm9qZWN0X0lkDQogICAgICB9DQogICAgICBjb25zb2xlLmxvZygNCiAgICAgICAgdGhpcy5jdXN0b21QYXJhbXMuU3lzX1Byb2plY3RfSWQsDQogICAgICAgICd0aGlzLmN1c3RvbVBhcmFtcy5TeXNfUHJvamVjdF9JZD09PT09PT09PT09PTExMTExJw0KICAgICAgKQ0KICAgICAgY29uc29sZS5sb2coDQogICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLkFyZWFfSWQsDQogICAgICAgICd0aGlzLmN1c3RvbVBhcmFtcy5BcmVhX0lkPT09PT09PT09PT09MTExMTEnDQogICAgICApDQogICAgICB0aGlzLmN1cnJlbnRMYXN0TGV2ZWwgPSAhIShkYXRhLkRhdGEuTGV2ZWwgJiYgZGF0YS5DaGlsZHJlbi5sZW5ndGggPT09IDApDQogICAgICBpZiAodGhpcy5jdXJyZW50TGFzdExldmVsKSB7DQogICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLlByb2plY3RfTmFtZSA9IGRhdGEuRGF0YT8uUHJvamVjdF9OYW1lDQogICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLkFyZWFfTmFtZSA9IGRhdGEuTGFiZWwNCiAgICAgIH0NCiAgICAgIHRoaXMucXVlcnlJbmZvLlBhZ2UgPSAxDQogICAgICB0aGlzLnBnTG9hZGluZyA9IHRydWUNCiAgICAgIHRoaXMuZmV0Y2hMaXN0KCkNCiAgICAgIGNvbnNvbGUubG9nKHRoaXMuY3VzdG9tUGFyYW1zLkFyZWFfSWQpDQogICAgICB0aGlzLmdldEluc3RhbGxVbml0SWROYW1lTGlzdChkYXRhSWQsIGRhdGEpDQogICAgICB0aGlzLmdldFBhcnRXZWlnaHRMaXN0KCkNCiAgICB9LA0KDQogICAgLy8g6I635Y+W5om55qyhDQogICAgZ2V0SW5zdGFsbFVuaXRJZE5hbWVMaXN0KGlkLCBkYXRhKSB7DQogICAgICBpZiAoaWQgPT09ICcnIHx8IGRhdGEuQ2hpbGRyZW4ubGVuZ3RoID4gMCkgew0KICAgICAgICB0aGlzLmluc3RhbGxVbml0SWROYW1lTGlzdCA9IFtdDQogICAgICB9IGVsc2Ugew0KICAgICAgICBHZXRJbnN0YWxsVW5pdElkTmFtZUxpc3QoeyBBcmVhX0lkOiBpZCB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICB0aGlzLmluc3RhbGxVbml0SWROYW1lTGlzdCA9IHJlcy5EYXRhDQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICAvLyDlt6Xluo/lrozmiJDph48NCiAgICBnZXRQcm9jZXNzRGF0YSgpIHsNCiAgICAgIGNvbnN0IGN1c3RvbVBhcmFtc0RhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMuY3VzdG9tUGFyYW1zKSkNCiAgICAgIGNvbnN0IEluc3RhbGxVbml0X0lkcyA9IGN1c3RvbVBhcmFtc0RhdGEuSW5zdGFsbFVuaXRfSWQuam9pbignLCcpDQogICAgICBkZWxldGUgY3VzdG9tUGFyYW1zRGF0YS5JbnN0YWxsVW5pdF9JZA0KDQogICAgICB0aGlzLndpZHRoID0gJzQwJScNCiAgICAgIHRoaXMuZ2VuZXJhdGVDb21wb25lbnQoYCR7dGhpcy5sZXZlbE5hbWV95bel5bqP5a6M5oiQ6YePYCwgJ1Byb2Nlc3NEYXRhJykNCiAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgIHRoaXMuJHJlZnNbJ2NvbnRlbnQnXS5pbml0KGN1c3RvbVBhcmFtc0RhdGEsIEluc3RhbGxVbml0X0lkcywgdGhpcy5zZWxlY3RMaXN0Lm1hcCgodikgPT4gdi5QYXJ0X0FnZ3JlZ2F0ZV9JZCkudG9TdHJpbmcoKSkNCiAgICAgIH0pDQogICAgfSwNCiAgICBnZXRUYWJsZUNvbmZpZyhjb2RlKSB7DQogICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHsNCiAgICAgICAgR2V0R3JpZEJ5Q29kZSh7DQogICAgICAgICAgY29kZToNCiAgICAgICAgICAgIGNvZGUgKw0KICAgICAgICAgICAgJywnICsNCiAgICAgICAgICAgIHRoaXMudHlwZU9wdGlvbi5maW5kKChpKSA9PiBpLklkID09PSB0aGlzLmN1c3RvbVBhcmFtcy5UeXBlSWQpLkNvZGUNCiAgICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgY29uc3QgeyBJc1N1Y2NlZWQsIERhdGEsIE1lc3NhZ2UgfSA9IHJlcw0KICAgICAgICAgIGlmIChJc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIGlmICghRGF0YSkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflvZPliY3kuJPkuJrmsqHmnInphY3nva7nm7jlr7nlupTooajmoLwnKQ0KICAgICAgICAgICAgICB0aGlzLnRiTG9hZGluZyA9IHRydWUNCiAgICAgICAgICAgICAgcmV0dXJuDQogICAgICAgICAgICB9DQogICAgICAgICAgICB0aGlzLnRiQ29uZmlnID0gT2JqZWN0LmFzc2lnbih7fSwgdGhpcy50YkNvbmZpZywgRGF0YS5HcmlkKQ0KICAgICAgICAgICAgY29uc3QgbGlzdCA9IERhdGEuQ29sdW1uTGlzdCB8fCBbXQ0KICAgICAgICAgICAgY29uc3Qgc29ydExpc3QgPSBsaXN0LnNvcnQoKGEsIGIpID0+IGEuU29ydCAtIGIuU29ydCkNCiAgICAgICAgICAgIHRoaXMuY29sdW1ucyA9IHNvcnRMaXN0DQogICAgICAgICAgICAgIC5maWx0ZXIoKHYpID0+IHYuSXNfRGlzcGxheSkNCiAgICAgICAgICAgICAgLm1hcCgoaXRlbSkgPT4gew0KICAgICAgICAgICAgICAgIGlmIChpdGVtLkNvZGUgPT09ICdDb2RlJykgew0KICAgICAgICAgICAgICAgICAgaXRlbS5maXhlZCA9ICdsZWZ0Jw0KICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgIHJldHVybiBpdGVtDQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICB0aGlzLnF1ZXJ5SW5mby5QYWdlU2l6ZSA9ICtEYXRhLkdyaWQuUm93X051bWJlciB8fCAyMA0KICAgICAgICAgICAgcmVzb2x2ZSh0aGlzLmNvbHVtbnMpDQogICAgICAgICAgICBjb25zb2xlLmxvZyh0aGlzLmNvbHVtbnMpDQogICAgICAgICAgICBjb25zdCBzZWxlY3RPcHRpb24gPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMuY29sdW1ucykpDQogICAgICAgICAgICBjb25zb2xlLmxvZyhzZWxlY3RPcHRpb24pDQogICAgICAgICAgICB0aGlzLmNvbHVtbnNPcHRpb24gPSBzZWxlY3RPcHRpb24uZmlsdGVyKCh2KSA9PiB7DQogICAgICAgICAgICAgIHJldHVybiAoDQogICAgICAgICAgICAgICAgdi5EaXNwbGF5X05hbWUgIT09ICfmk43kvZzml7bpl7QnICYmDQogICAgICAgICAgICAgICAgdi5EaXNwbGF5X05hbWUgIT09ICfmqKHlnotJRCcgJiYNCiAgICAgICAgICAgICAgICB2LkRpc3BsYXlfTmFtZSAhPT0gJ+a3seWMlui1hOaWmScgJiYNCiAgICAgICAgICAgICAgICB2LkRpc3BsYXlfTmFtZSAhPT0gJ+Wkh+azqCcgJiYNCiAgICAgICAgICAgICAgICB2LkRpc3BsYXlfTmFtZSAhPT0gJ+aOkuS6p+aVsOmHjycgJiYNCiAgICAgICAgICAgICAgICB2LkNvZGUuaW5kZXhPZignQXR0cicpID09PSAtMSAmJg0KICAgICAgICAgICAgICAgIHYuRGlzcGxheV9OYW1lICE9PSAn5om55qyhJw0KICAgICAgICAgICAgICApDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogTWVzc2FnZSwNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0sDQogICAgYXN5bmMgZmV0Y2hMaXN0KCkgew0KICAgICAgY29uc3QgY3VzdG9tUGFyYW1zRGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy5jdXN0b21QYXJhbXMpKQ0KICAgICAgY29uc3QgSW5zdGFsbFVuaXRfSWRzID0gY3VzdG9tUGFyYW1zRGF0YS5JbnN0YWxsVW5pdF9JZC5qb2luKCcsJykNCiAgICAgIGRlbGV0ZSBjdXN0b21QYXJhbXNEYXRhLkluc3RhbGxVbml0X0lkDQogICAgICBhd2FpdCBHZXRQYXJ0UGFnZUxpc3Qoew0KICAgICAgICAuLi50aGlzLnF1ZXJ5SW5mbywNCiAgICAgICAgLi4uY3VzdG9tUGFyYW1zRGF0YSwNCiAgICAgICAgQ29kZTogY3VzdG9tUGFyYW1zRGF0YS5Db2RlLnRyaW0oKS5yZXBsYWNlQWxsKCcgJywgJ1xuJyksDQogICAgICAgIEluc3RhbGxVbml0X0lkczogSW5zdGFsbFVuaXRfSWRzDQogICAgICB9KQ0KICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIHRoaXMucXVlcnlJbmZvLlBhZ2VTaXplID0gcmVzLkRhdGEuUGFnZVNpemUNCiAgICAgICAgICAgIHRoaXMudG90YWwgPSByZXMuRGF0YS5Ub3RhbENvdW50DQogICAgICAgICAgICB0aGlzLnRiRGF0YSA9IHJlcy5EYXRhLkRhdGEubWFwKCh2KSA9PiB7DQogICAgICAgICAgICAgIHYuSXNfTWFpbiA9IHYuSXNfTWFpbiA/ICfmmK8nIDogJ+WQpicNCiAgICAgICAgICAgICAgdi5FeGRhdGUgPSB0aW1lRm9ybWF0KHYuRXhkYXRlLCAne3l9LXttfS17ZH0ge2h9OntpfTp7c30nKQ0KICAgICAgICAgICAgICAvLyBjb25zb2xlLmxvZyh2KQ0KICAgICAgICAgICAgICByZXR1cm4gdg0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHRoaXMuc2VsZWN0TGlzdCA9IFtdDQogICAgICAgICAgICB0aGlzLmdldFN0b3BMaXN0KCkNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgLmZpbmFsbHkoKCkgPT4gew0KICAgICAgICAgIHRoaXMudGJMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICB0aGlzLnBnTG9hZGluZyA9IGZhbHNlDQogICAgICAgIH0pDQogICAgfSwNCiAgICBhc3luYyBnZXRTdG9wTGlzdCgpIHsNCiAgICAgIGNvbnN0IHN1Ym1pdE9iaiA9IHRoaXMudGJEYXRhLm1hcChpdGVtID0+IHsNCiAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICBJZDogaXRlbS5QYXJ0X0FnZ3JlZ2F0ZV9JZCwNCiAgICAgICAgICBUeXBlOiAxLA0KICAgICAgICAgIEJvbV9MZXZlbDogdGhpcy5sZXZlbENvZGUNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIGF3YWl0IEdldFN0b3BMaXN0KHN1Ym1pdE9iaikudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIGNvbnN0IHN0b3BNYXAgPSB7fQ0KICAgICAgICAgIHJlcy5EYXRhLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgICBzdG9wTWFwW2l0ZW0uSWRdID0gaXRlbS5Jc19TdG9wICE9PSBudWxsDQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLnRiRGF0YS5mb3JFYWNoKHJvdyA9PiB7DQogICAgICAgICAgICBpZiAoc3RvcE1hcFtyb3cuUGFydF9BZ2dyZWdhdGVfSWRdKSB7DQogICAgICAgICAgICAgIHRoaXMuJHNldChyb3csICdzdG9wRmxhZycsIHN0b3BNYXBbcm93LlBhcnRfQWdncmVnYXRlX0lkXSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgYXN5bmMgZmV0Y2hEYXRhKCkgew0KICAgICAgY29uc29sZS5sb2coJ+abtOaWsOWIl+ihqCcpDQogICAgICAvLyDliIblvIDojrflj5bvvIzmj5Dpq5jmjqXlj6PpgJ/luqYNCiAgICAgIGF3YWl0IHRoaXMuZ2V0VGFibGVDb25maWcoJ3BsbV9wYXJ0c19wYWdlX2xpc3QnKQ0KICAgICAgdGhpcy50YkxvYWRpbmcgPSB0cnVlDQogICAgICB0aGlzLmZldGNoTGlzdCgpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLnRiTG9hZGluZyA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQogICAgYXN5bmMgY2hhbmdlUGFnZSgpIHsNCiAgICAgIHRoaXMudGJMb2FkaW5nID0gdHJ1ZQ0KICAgICAgaWYgKA0KICAgICAgICB0eXBlb2YgdGhpcy5xdWVyeUluZm8uUGFnZVNpemUgIT09ICdudW1iZXInIHx8DQogICAgICAgIHRoaXMucXVlcnlJbmZvLlBhZ2VTaXplIDwgMQ0KICAgICAgKSB7DQogICAgICAgIHRoaXMucXVlcnlJbmZvLlBhZ2VTaXplID0gMTANCiAgICAgIH0NCiAgICAgIHRoaXMuZmV0Y2hMaXN0KCkudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMudGJMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyB0YlNlbGVjdENoYW5nZShhcnJheSkgew0KICAgIC8vICAgY29uc29sZS5sb2coJ2FycmF5JywgYXJyYXkpDQogICAgLy8gICB0aGlzLnNlbGVjdExpc3QgPSBhcnJheS5yZWNvcmRzDQogICAgLy8gICBjb25zb2xlLmxvZygndGhpcy5zZWxlY3RMaXN0JywgdGhpcy5zZWxlY3RMaXN0KQ0KICAgIC8vIH0sDQogICAgZ2V0VGJEYXRhKGRhdGEpIHsNCiAgICAgIGNvbnN0IHsgWWVhckFsbFdlaWdodCwgWWVhclN0ZWVsLCBDb3VudEluZm8gfSA9IGRhdGENCiAgICAgIC8vIHRoaXMudGlwTGFiZWwgPSBg57Sv6K6h5LiK5Lyg5p6E5Lu2JHtZZWFyU3RlZWx95Lu277yM5oC76YeNJHtZZWFyQWxsV2VpZ2h0fXTjgIJgDQogICAgICB0aGlzLnRpcExhYmVsID0gQ291bnRJbmZvDQogICAgfSwNCiAgICBhc3luYyBnZXRUeXBlTGlzdCgpIHsNCiAgICAgIGxldCByZXMgPSBudWxsDQogICAgICBsZXQgZGF0YSA9IG51bGwNCiAgICAgIHJlcyA9IGF3YWl0IEdldEZhY3RvcnlQcm9mZXNzaW9uYWxCeUNvZGUoew0KICAgICAgICBmYWN0b3J5SWQ6IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdDdXJSZWZlcmVuY2VJZCcpDQogICAgICB9KQ0KICAgICAgZGF0YSA9IHJlcy5EYXRhDQogICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICB0aGlzLnR5cGVPcHRpb24gPSBPYmplY3QuZnJlZXplKGRhdGEpDQogICAgICAgIGlmICh0aGlzLnR5cGVPcHRpb24ubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoaXMuUHJvcG9ydGlvbiA9IGRhdGFbMF0uUHJvcG9ydGlvbg0KICAgICAgICAgIHRoaXMuVW5pdCA9IGRhdGFbMF0uVW5pdA0KICAgICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLlR5cGVJZCA9IHRoaXMudHlwZU9wdGlvblswXT8uSWQNCiAgICAgICAgICB0aGlzLmN1c3RvbVBhcmFtcy5UeXBlX05hbWUgPSB0aGlzLnR5cGVPcHRpb25bMF0/Lk5hbWUNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlRGVsZXRlKCkgew0KICAgICAgdGhpcy4kY29uZmlybSgn5q2k5pON5L2c5bCG5Yig6Zmk6YCJ5oup5pWw5o2uLCDmmK/lkKbnu6fnu60/JywgJ+aPkOekuicsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgRGVsZXRlcGFydCh7DQogICAgICAgICAgICBpZHM6IHRoaXMuc2VsZWN0TGlzdC5tYXAoKHYpID0+IHYuUGFydF9BZ2dyZWdhdGVfSWQpLnRvU3RyaW5nKCkNCiAgICAgICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICAgIHRoaXMuZmV0Y2hEYXRhKCkNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKnycsDQogICAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgIHRoaXMuZ2V0UGFydFdlaWdodExpc3QoKQ0KICAgICAgICAgICAgICB0aGlzLmZldGNoVHJlZURhdGEoKQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICB0eXBlOiAnaW5mbycsDQogICAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raI5Yig6ZmkJw0KICAgICAgICAgIH0pDQogICAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVFZGl0KHJvdykgew0KICAgICAgdGhpcy53aWR0aCA9ICc0NSUnDQogICAgICB0aGlzLmdlbmVyYXRlQ29tcG9uZW50KGDnvJbovpEke3RoaXMubGV2ZWxOYW1lfWAsICdFZGl0JykNCiAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgIHJvdy5pc1JlYWRPbmx5ID0gZmFsc2UNCiAgICAgICAgdGhpcy4kcmVmc1snY29udGVudCddLmluaXQocm93KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZUJhdGNoRWRpdCgpIHsNCiAgICAgIGNvbnN0IFNjaGVkdWxBcnIgPSB0aGlzLnNlbGVjdExpc3QuZmlsdGVyKChpdGVtKSA9PiB7DQogICAgICAgIHJldHVybiBpdGVtLlNjaGR1bGluZ19Db3VudCAhPSBudWxsICYmIGl0ZW0uU2NoZHVsaW5nX0NvdW50ID4gMA0KICAgICAgfSkNCiAgICAgIGlmIChTY2hlZHVsQXJyLmxlbmd0aCA+IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgdHlwZTogJ2Vycm9yJywNCiAgICAgICAgICBtZXNzYWdlOiBg6YCJ5Lit6KGM5YyF5ZCr5bey5o6S5Lqn55qEJHt0aGlzLmxldmVsTmFtZX0s57yW6L6R5L+h5oGv6ZyA6KaB6L+b6KGM5Y+Y5pu05pON5L2cYA0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy53aWR0aCA9ICc0MCUnDQogICAgICAgIHRoaXMuZ2VuZXJhdGVDb21wb25lbnQoJ+aJuemHj+e8lui+kScsICdCYXRjaEVkaXQnKQ0KICAgICAgICB0aGlzLiRuZXh0VGljaygoXykgPT4gew0KICAgICAgICAgIHRoaXMuJHJlZnNbJ2NvbnRlbnQnXS5pbml0KHRoaXMuc2VsZWN0TGlzdCwgdGhpcy5jb2x1bW5zT3B0aW9uKQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlVmlldyhyb3cpIHsNCiAgICAgIHRoaXMud2lkdGggPSAnNDUlJw0KICAgICAgdGhpcy5nZW5lcmF0ZUNvbXBvbmVudCgn6K+m5oOFJywgJ0VkaXQnKQ0KICAgICAgdGhpcy4kbmV4dFRpY2soKF8pID0+IHsNCiAgICAgICAgcm93LmlzUmVhZE9ubHkgPSB0cnVlDQogICAgICAgIHRoaXMuJHJlZnNbJ2NvbnRlbnQnXS5pbml0KHJvdykNCiAgICAgIH0pDQogICAgfSwNCiAgICBhc3luYyBoYW5kbGVFeHBvcnQoKSB7DQogICAgICBjb25zdCBvYmogPSB7DQogICAgICAgIFBhcnRfQWdncmVnYXRlX0lkczogdGhpcy5zZWxlY3RMaXN0DQogICAgICAgICAgLm1hcCgodikgPT4gdi5QYXJ0X0FnZ3JlZ2F0ZV9JZCkNCiAgICAgICAgICAudG9TdHJpbmcoKSwNCiAgICAgICAgUHJvZmVzc2lvbmFsQ29kZTogdGhpcy50eXBlRW50aXR5LkNvZGUNCiAgICAgIH0NCiAgICAgIEV4cG9ydFBsYW5wYXJ0SW5mbyhvYmopLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHdpbmRvdy5vcGVuKGNvbWJpbmVVUkwodGhpcy4kYmFzZVVybCwgcmVzLkRhdGEpLCAnX2JsYW5rJykNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5NZXNzYWdlKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g6Zu25Lu25ouG5YiGDQogICAgcGFydFNwbGl0KCkgew0KICAgICAgY29uc3QgVHlwZV9OYW1lID0gdGhpcy5zZWxlY3RMaXN0WzBdLlR5cGVfTmFtZQ0KICAgICAgY29uc3QgU2NoZHVsaW5nX0NvdW50ID0gdGhpcy5zZWxlY3RMaXN0WzBdLlNjaGR1bGluZ19Db3VudA0KICAgICAgaWYgKFR5cGVfTmFtZSA9PT0gJ+ebtOWPkeS7ticgfHwgU2NoZHVsaW5nX0NvdW50ID4gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICB0eXBlOiAnZXJyb3InLA0KICAgICAgICAgIG1lc3NhZ2U6IGAke3RoaXMubGV2ZWxOYW1lfeW3suaOkuS6p+aIluaYr+ebtOWPkeS7tizml6Dms5Xmi4bliIZgDQogICAgICAgIH0pDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgdGhpcy53aWR0aCA9ICc0NSUnDQogICAgICB0aGlzLmdlbmVyYXRlQ29tcG9uZW50KGAke3RoaXMubGV2ZWxOYW1lfeaLhuWIhmAsICdQYXJ0U3BsaXQnKQ0KICAgICAgdGhpcy4kbmV4dFRpY2soKF8pID0+IHsNCiAgICAgICAgdGhpcy4kcmVmc1snY29udGVudCddLmluaXQodGhpcy5zZWxlY3RMaXN0KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIG1vZGVsTGlzdEltcG9ydCgpIHsNCiAgICAgIGNvbnN0IG9iaiA9IHsNCiAgICAgICAgUGFydF9BZ2dyZWdhdGVfSWRzOiB0aGlzLnNlbGVjdExpc3QNCiAgICAgICAgICAubWFwKCh2KSA9PiB2LlBhcnRfQWdncmVnYXRlX0lkKQ0KICAgICAgICAgIC50b1N0cmluZygpLA0KICAgICAgICBQcm9mZXNzaW9uYWxDb2RlOiB0aGlzLnR5cGVFbnRpdHkuQ29kZQ0KICAgICAgfQ0KICAgICAgRXhwb3J0UGxhbnBhcnRjb3VudEluZm8ob2JqKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB3aW5kb3cub3Blbihjb21iaW5lVVJMKHRoaXMuJGJhc2VVcmwsIHJlcy5EYXRhKSwgJ19ibGFuaycpDQogICAgICAgICAgaWYgKHJlcy5NZXNzYWdlKSB7DQogICAgICAgICAgICB0aGlzLiRhbGVydChyZXMuTWVzc2FnZSwgJ+WvvOWHuumAmuefpScsIHsNCiAgICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfmiJHnn6XpgZPkuoYnDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5NZXNzYWdlKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g6KaG55uW5a+85YWlIG9yIOaWsOWinuWvvOWFpQ0KICAgIGhhbmRsZUNvbW1hbmQoY29tbWFuZCkgew0KICAgICAgY29uc29sZS5sb2coY29tbWFuZCwgJ2NvbW1hbmQnKQ0KICAgICAgdGhpcy5jb21tYW5kID0gY29tbWFuZA0KICAgICAgdGhpcy5kZWVwTGlzdEltcG9ydCgpDQogICAgfSwNCiAgICBkZWVwTGlzdEltcG9ydCgpIHsNCiAgICAgIGNvbnN0IGZpbGVUeXBlID0gew0KICAgICAgICBDYXRhbG9nX0NvZGU6ICdQTE1EZWVwZW5GaWxlcycsDQogICAgICAgIENvZGU6IHRoaXMudHlwZUVudGl0eS5Db2RlLA0KICAgICAgICBuYW1lOiB0aGlzLnR5cGVFbnRpdHkuTmFtZQ0KICAgICAgfQ0KICAgICAgdGhpcy4kcmVmcy5kaWFsb2cuaGFuZGxlT3BlbigNCiAgICAgICAgJ2FkZCcsDQogICAgICAgIGZpbGVUeXBlLA0KICAgICAgICBudWxsLA0KICAgICAgICB0cnVlLA0KICAgICAgICB0aGlzLlBJRCwNCiAgICAgICAgdGhpcy5jb21tYW5kLA0KICAgICAgICB0aGlzLmN1c3RvbVBhcmFtcw0KICAgICAgKQ0KICAgIH0sDQogICAgYXN5bmMgaGFuZGxlQWxsRGVsZXRlKCkgew0KICAgICAgY29uc29sZS5sb2codGhpcy5jdXN0b21QYXJhbXMuUHJvamVjdF9JZCkNCiAgICAgIGlmICh0aGlzLmN1c3RvbVBhcmFtcy5Qcm9qZWN0X0lkKSB7DQogICAgICAgIGF3YWl0IHByb21wdEJveCh7IHRpdGxlOiAn5Yig6ZmkJyB9KQ0KICAgICAgICBhd2FpdCBEZWxldGVwYXJ0QnlmaW5ka2V5d29kZXMoew0KICAgICAgICAgIC4uLnRoaXMuY3VzdG9tUGFyYW1zLA0KICAgICAgICAgIC4uLnRoaXMucXVlcnlJbmZvDQogICAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpDQogICAgICAgICAgICB0aGlzLmZldGNoRGF0YSgpDQogICAgICAgICAgICB0aGlzLmZldGNoVHJlZURhdGEoKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5NZXNzYWdlKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI6YCJ5oup6aG555uuJykNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZUNsb3NlKCkgew0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICB9LA0KICAgIGdlbmVyYXRlQ29tcG9uZW50KHRpdGxlLCBjb21wb25lbnQpIHsNCiAgICAgIHRoaXMudGl0bGUgPSB0aXRsZQ0KICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gY29tcG9uZW50DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgfSwNCiAgICAvLyDngrnlh7vmkJzntKINCiAgICBoYW5kZWxzZWFyY2gocmVzZXQsIGhhc1NlYXJjaCA9IHRydWUpIHsNCiAgICAgIHRoaXMuZGVsZXRlQ29udGVudCA9IGZhbHNlDQogICAgICBpZiAocmVzZXQpIHsNCiAgICAgICAgdGhpcy4kcmVmcy5jdXN0b21QYXJhbXMucmVzZXRGaWVsZHMoKQ0KICAgICAgICB0aGlzLmRlbGV0ZUNvbnRlbnQgPSB0cnVlDQogICAgICAgIHRoaXMubmFtZXMgPSAnJw0KICAgICAgfQ0KICAgICAgaGFzU2VhcmNoICYmIHRoaXMuZmV0Y2hEYXRhKCkNCiAgICAgIHRoaXMuZ2V0UGFydFdlaWdodExpc3QoKQ0KICAgIH0sDQogICAgLy8g5rex5YyW6LWE5paZ5p+l55yLDQogICAgaGFuZGxlRGVlcE1hdGVyaWFsKHJvdykgew0KICAgICAgY29uc29sZS5sb2coJ2hhbmRsZURlZXBNYXRlcmlhbCcpDQogICAgICB0aGlzLndpZHRoID0gJzQ1JScNCiAgICAgIHRoaXMuZ2VuZXJhdGVDb21wb25lbnQoJ+afpeeci+a3seWMlui1hOaWmScsICdEZWVwTWF0ZXJpYWwnKQ0KICAgICAgdGhpcy4kbmV4dFRpY2soKF8pID0+IHsNCiAgICAgICAgcm93LmlzUmVhZE9ubHkgPSBmYWxzZQ0KICAgICAgICB0aGlzLiRyZWZzWydjb250ZW50J10uaW5pdChyb3cpDQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g5o6S5Lqn5pWw6YePDQogICAgaGFuZGVsU2NoZHVsaW5nKHJvdykgew0KICAgICAgdGhpcy53aWR0aCA9ICc0NSUnDQogICAgICB0aGlzLmdlbmVyYXRlQ29tcG9uZW50KCfnlJ/kuqfor6bmg4UnLCAnU2NoZHVsaW5nJykNCiAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgIHJvdy5pc1JlYWRPbmx5ID0gZmFsc2UNCiAgICAgICAgdGhpcy4kcmVmc1snY29udGVudCddLmluaXQocm93KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOmbtuS7tuaOkuS6p+S/oeaBrw0KICAgIGdldFBhcnRXZWlnaHRMaXN0KCkgew0KICAgICAgdGhpcy5jb3VudExvYWRpbmcgPSB0cnVlDQogICAgICBjb25zdCBjdXN0b21QYXJhbXNEYXRhID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLmN1c3RvbVBhcmFtcykpDQogICAgICBjb25zdCBJbnN0YWxsVW5pdF9JZHMgPSBjdXN0b21QYXJhbXNEYXRhLkluc3RhbGxVbml0X0lkLmpvaW4oJywnKQ0KICAgICAgZGVsZXRlIGN1c3RvbVBhcmFtc0RhdGEuSW5zdGFsbFVuaXRfSWQNCiAgICAgIEdldFBhcnRXZWlnaHRMaXN0KHsNCiAgICAgICAgLi4udGhpcy5xdWVyeUluZm8sDQogICAgICAgIC4uLmN1c3RvbVBhcmFtc0RhdGEsDQogICAgICAgIEluc3RhbGxVbml0X0lkcw0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy5TdGVlbEFtb3VudFRvdGFsID0gTWF0aC5yb3VuZChyZXMuRGF0YS5EZWVwZW5OdW0gKiAxMDAwKSAvIDEwMDAgLy8g5rex5YyW5oC76YePDQogICAgICAgICAgdGhpcy5TY2hlZHVsaW5nTnVtVG90YWwgPQ0KICAgICAgICAgICAgTWF0aC5yb3VuZChyZXMuRGF0YS5TY2hlZHVsaW5nTnVtICogMTAwMCkgLyAxMDAwIC8vIOaOkuS6p+aAu+mHjw0KICAgICAgICAgIHRoaXMuU3RlZWxBbGxXZWlnaHRUb3RhbCA9DQogICAgICAgICAgICBNYXRoLnJvdW5kKHJlcy5EYXRhLkRlZXBlbldlaWdodCAqIDEwMDApIC8gMTAwMCAvLyDmt7HljJbmgLvph40NCiAgICAgICAgICB0aGlzLlNjaGVkdWxpbmdBbGxXZWlnaHRUb3RhbCA9DQogICAgICAgICAgICBNYXRoLnJvdW5kKHJlcy5EYXRhLlNjaGVkdWxpbmdXZWlnaHQgKiAxMDAwKSAvIDEwMDAgLy8g5o6S5Lqn5oC76YeNDQogICAgICAgICAgdGhpcy5GaW5pc2hDb3VudFRvdGFsID0NCiAgICAgICAgICAgIE1hdGgucm91bmQocmVzLkRhdGEuRmluaXNoX0NvdW50ICogMTAwMCkgLyAxMDAwIC8vIOWujOaIkOaAu+aVsA0KICAgICAgICAgIHRoaXMuRmluaXNoV2VpZ2h0VG90YWwgPQ0KICAgICAgICAgICAgTWF0aC5yb3VuZChyZXMuRGF0YS5GaW5pc2hfV2VpZ2h0ICogMTAwMCkgLyAxMDAwIC8vIOWujOaIkOaAu+mHjQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCcgdGhpcy5TdGVlbEFsbFdlaWdodFRvdGFsJywgdGhpcy5TdGVlbEFsbFdlaWdodFRvdGFsKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLk1lc3NhZ2UpDQogICAgICAgIH0NCiAgICAgICAgdGhpcy5jb3VudExvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHRiU2VsZWN0Q2hhbmdlKGFycmF5KSB7DQogICAgICB0aGlzLnNlbGVjdExpc3QgPSBhcnJheS5yZWNvcmRzDQogICAgICB0aGlzLlN0ZWVsQW1vdW50VG90YWwgPSAwDQogICAgICB0aGlzLlNjaGVkdWxpbmdOdW1Ub3RhbCA9IDANCiAgICAgIHRoaXMuU3RlZWxBbGxXZWlnaHRUb3RhbCA9IDANCiAgICAgIHRoaXMuU2NoZWR1bGluZ0FsbFdlaWdodFRvdGFsID0gMA0KICAgICAgdGhpcy5GaW5pc2hDb3VudFRvdGFsID0gMA0KICAgICAgdGhpcy5GaW5pc2hXZWlnaHRUb3RhbCA9IDANCiAgICAgIGxldCBTdGVlbEFsbFdlaWdodFRvdGFsVGVtcCA9IDANCiAgICAgIGxldCBTY2hlZHVsaW5nQWxsV2VpZ2h0VG90YWxUZW1wID0gMA0KICAgICAgbGV0IEZpbmlzaFdlaWdodFRvdGFsVGVtcCA9IDANCiAgICAgIGlmICh0aGlzLnNlbGVjdExpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICB0aGlzLnNlbGVjdExpc3QuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgIGNvbnN0IHNjaGVkdWxpbmdOdW0gPQ0KICAgICAgICAgICAgaXRlbS5TY2hkdWxpbmdfQ291bnQgPT0gbnVsbCA/IDAgOiBpdGVtLlNjaGR1bGluZ19Db3VudA0KICAgICAgICAgIHRoaXMuU3RlZWxBbW91bnRUb3RhbCArPSBpdGVtLk51bQ0KICAgICAgICAgIHRoaXMuU2NoZWR1bGluZ051bVRvdGFsICs9IE51bWJlcihpdGVtLlNjaGR1bGluZ19Db3VudCkNCiAgICAgICAgICB0aGlzLkZpbmlzaENvdW50VG90YWwgKz0gaXRlbS5GaW5pc2hfQ291bnQNCiAgICAgICAgICBTdGVlbEFsbFdlaWdodFRvdGFsVGVtcCArPSBpdGVtLlRvdGFsX1dlaWdodA0KICAgICAgICAgIFNjaGVkdWxpbmdBbGxXZWlnaHRUb3RhbFRlbXAgKz0gaXRlbS5XZWlnaHQgKiBzY2hlZHVsaW5nTnVtDQogICAgICAgICAgRmluaXNoV2VpZ2h0VG90YWxUZW1wICs9IGl0ZW0uRmluaXNoX1dlaWdodA0KICAgICAgICB9KQ0KICAgICAgICB0aGlzLlN0ZWVsQWxsV2VpZ2h0VG90YWwgPQ0KICAgICAgICAgIE1hdGgucm91bmQoKFN0ZWVsQWxsV2VpZ2h0VG90YWxUZW1wIC8gdGhpcy5Qcm9wb3J0aW9uKSAqIDEwMDApIC8gMTAwMA0KICAgICAgICB0aGlzLlNjaGVkdWxpbmdBbGxXZWlnaHRUb3RhbCA9DQogICAgICAgICAgTWF0aC5yb3VuZCgoU2NoZWR1bGluZ0FsbFdlaWdodFRvdGFsVGVtcCAvIHRoaXMuUHJvcG9ydGlvbikgKiAxMDAwKSAvDQogICAgICAgICAgMTAwMA0KICAgICAgICB0aGlzLkZpbmlzaFdlaWdodFRvdGFsID0NCiAgICAgICAgICBNYXRoLnJvdW5kKChGaW5pc2hXZWlnaHRUb3RhbFRlbXAgLyB0aGlzLlByb3BvcnRpb24pICogMTAwMCkgLyAxMDAwDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmdldFBhcnRXZWlnaHRMaXN0KCkNCiAgICAgIH0NCiAgICB9LA0KICAgIGZldGNoVHJlZURhdGFMb2NhbCgpIHsNCiAgICAgIC8vIHRoaXMuZmlsdGVyVGV4dCA9IHRoaXMucHJvamVjdE5hbWUNCiAgICB9LA0KICAgIGdldFBhcnRJbmZvKHJvdykgew0KICAgICAgY29uc3QgZHJhd2luZ0RhdGEgPSByb3cuRHJhd2luZyA/IHJvdy5EcmF3aW5nLnNwbGl0KCcsJykgOiBbXSAvLyDlm77nurjmlbDmja4NCiAgICAgIGNvbnN0IGZpbGVVcmxEYXRhID0gcm93LkZpbGVfVXJsID8gcm93LkZpbGVfVXJsLnNwbGl0KCcsJykgOiBbXSAvLyDlm77nurjmlbDmja7mlofku7blnLDlnYDmlbDmja4NCiAgICAgIGlmIChmaWxlVXJsRGF0YS5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogYOW9k+WJjSR7dGhpcy5sZXZlbE5hbWV95peg5Zu+57q4YCwNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgfSkNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICBpZiAoZHJhd2luZ0RhdGEubGVuZ3RoID4gMCAmJiBmaWxlVXJsRGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMuZHJhd2luZ0FjdGl2ZSA9IGRyYXdpbmdEYXRhWzBdDQogICAgICB9DQogICAgICBpZiAoZHJhd2luZ0RhdGEubGVuZ3RoID4gMCAmJiBmaWxlVXJsRGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMuZHJhd2luZ0RhdGFMaXN0ID0gZHJhd2luZ0RhdGEubWFwKChpdGVtLCBpbmRleCkgPT4gKHsNCiAgICAgICAgICBuYW1lOiBpdGVtLA0KICAgICAgICAgIGxhYmVsOiBpdGVtLA0KICAgICAgICAgIHVybDogZmlsZVVybERhdGFbaW5kZXhdDQogICAgICAgIH0pKQ0KICAgICAgfQ0KICAgICAgdGhpcy5nZXRQYXJ0SW5mb0RyYXdpbmcocm93KQ0KICAgIH0sDQoNCiAgICBnZXRQYXJ0SW5mb0RyYXdpbmcocm93KSB7DQogICAgICBjb25zdCBpbXBvcnREZXRhaWxJZCA9IHJvdy5QYXJ0X0FnZ3JlZ2F0ZV9JZA0KICAgICAgR2V0U3RlZWxDYWRBbmRCaW1JZCh7IGltcG9ydERldGFpbElkOiBpbXBvcnREZXRhaWxJZCB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICBjb25zdCBkcmF3aW5nRGF0YSA9IHsNCiAgICAgICAgICAgICdleHRlbnNpb25OYW1lJzogcmVzLkRhdGFbMF0uRXh0ZW5zaW9uTmFtZSwNCiAgICAgICAgICAgICdmaWxlQmltJzogcmVzLkRhdGFbMF0uZmlsZUJpbSwNCiAgICAgICAgICAgICdJc1VwbG9hZCc6IHJlcy5EYXRhWzBdLklzVXBsb2FkLA0KICAgICAgICAgICAgJ0NvZGUnOiByb3cuQ29kZSwNCiAgICAgICAgICAgICdTeXNfUHJvamVjdF9JZCc6IHJvdy5TeXNfUHJvamVjdF9JZA0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLiRyZWZzLm1vZGVsRHJhd2luZ1JlZi5kd2dJbml0KGRyYXdpbmdEYXRhKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvKiAgICBoYW5kbGVWaWV3RHdnKHJvdykgew0KICAgICAgaWYgKCFyb3cuRmlsZV9VcmwpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogJ+W9k+WJjembtuS7tuaXoOWbvue6uCcsDQogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgIH0pDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgd2luZG93Lm9wZW4oJ2h0dHA6Ly9kd2d2MS5iaW10ay5jb206NTQzMi8/Q2FkVXJsPScgKyBwYXJzZU9zc1VybChyb3cuRmlsZV9VcmwpLCAnX2JsYW5rJykNCiAgICB9LCovDQogICAgY3VzdG9tRmlsdGVyRnVuKHZhbHVlLCBkYXRhLCBub2RlKSB7DQogICAgICBjb25zdCBhcnIgPSB2YWx1ZS5zcGxpdChTUExJVF9TWU1CT0wpDQogICAgICBjb25zdCBsYWJlbFZhbCA9IGFyclswXQ0KICAgICAgY29uc3Qgc3RhdHVzVmFsID0gYXJyWzFdDQogICAgICBpZiAoIXZhbHVlKSByZXR1cm4gdHJ1ZQ0KICAgICAgbGV0IHBhcmVudE5vZGUgPSBub2RlLnBhcmVudA0KICAgICAgbGV0IGxhYmVscyA9IFtub2RlLmxhYmVsXQ0KICAgICAgbGV0IHN0YXR1cyA9IFsNCiAgICAgICAgZGF0YS5EYXRhLklzX0RlZXBlbl9DaGFuZ2UNCiAgICAgICAgICA/ICflt7Llj5jmm7QnDQogICAgICAgICAgOiBkYXRhLkRhdGEuSXNfSW1wb3J0ZWQNCiAgICAgICAgICAgID8gJ+W3suWvvOWFpScNCiAgICAgICAgICAgIDogJ+acquWvvOWFpScNCiAgICAgIF0NCiAgICAgIGxldCBsZXZlbCA9IDENCiAgICAgIHdoaWxlIChsZXZlbCA8IG5vZGUubGV2ZWwpIHsNCiAgICAgICAgbGFiZWxzID0gWy4uLmxhYmVscywgcGFyZW50Tm9kZS5sYWJlbF0NCiAgICAgICAgc3RhdHVzID0gWw0KICAgICAgICAgIC4uLnN0YXR1cywNCiAgICAgICAgICBkYXRhLkRhdGEuSXNfRGVlcGVuX0NoYW5nZQ0KICAgICAgICAgICAgPyAn5bey5Y+Y5pu0Jw0KICAgICAgICAgICAgOiBkYXRhLkRhdGEuSXNfSW1wb3J0ZWQNCiAgICAgICAgICAgICAgPyAn5bey5a+85YWlJw0KICAgICAgICAgICAgICA6ICfmnKrlr7zlhaUnDQogICAgICAgIF0NCiAgICAgICAgcGFyZW50Tm9kZSA9IHBhcmVudE5vZGUucGFyZW50DQogICAgICAgIGxldmVsKysNCiAgICAgIH0NCiAgICAgIGxhYmVscyA9IGxhYmVscy5maWx0ZXIoKHYpID0+ICEhdikNCiAgICAgIHN0YXR1cyA9IHN0YXR1cy5maWx0ZXIoKHYpID0+ICEhdikNCiAgICAgIGxldCByZXN1bHRMYWJlbCA9IHRydWUNCiAgICAgIGxldCByZXN1bHRTdGF0dXMgPSB0cnVlDQogICAgICBpZiAodGhpcy5zdGF0dXNUeXBlKSB7DQogICAgICAgIHJlc3VsdFN0YXR1cyA9IHN0YXR1cy5zb21lKChzKSA9PiBzLmluZGV4T2Yoc3RhdHVzVmFsKSAhPT0gLTEpDQogICAgICB9DQogICAgICBpZiAodGhpcy5wcm9qZWN0TmFtZSkgew0KICAgICAgICByZXN1bHRMYWJlbCA9IGxhYmVscy5zb21lKChzKSA9PiBzLmluZGV4T2YobGFiZWxWYWwpICE9PSAtMSkNCiAgICAgIH0NCiAgICAgIHJldHVybiByZXN1bHRMYWJlbCAmJiByZXN1bHRTdGF0dXMNCiAgICB9LA0KICAgIGdldFBhcnRUeXBlKCkgew0KICAgICAgR2V0UGFydFR5cGVMaXN0KHsgUGFydF9HcmFkZTogMCB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLnBhcnRUeXBlT3B0aW9uID0gcmVzLkRhdGEubWFwKCh2KSA9PiB7DQogICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICBsYWJlbDogdi5OYW1lLA0KICAgICAgICAgICAgICB2YWx1ZTogdi5JZA0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgYXN5bmMgZ2V0RmlsZVR5cGUoKSB7DQogICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgIGNhdGFsb2dDb2RlOiAnUExNRGVlcGVuRmlsZXMnDQogICAgICB9DQogICAgICBjb25zdCByZXMgPSBhd2FpdCBHZXRGaWxlVHlwZShwYXJhbXMpDQogICAgICAvLyDojrflj5bmnoTku7bor6blm74NCiAgICAgIGNvbnN0IGRhdGEgPSByZXMuRGF0YS5maW5kKCh2KSA9PiB2LkxhYmVsID09PSAn6Zu25Lu26K+m5Zu+JykNCg0KICAgICAgdGhpcy5jb21EcmF3RGF0YSA9IHsNCiAgICAgICAgaXNTSFFEOiBmYWxzZSwNCiAgICAgICAgSWQ6IGRhdGEuSWQsDQogICAgICAgIG5hbWU6IGRhdGEuTGFiZWwsDQogICAgICAgIENhdGFsb2dfQ29kZTogZGF0YS5Db2RlLA0KICAgICAgICBDb2RlOiBkYXRhLkRhdGE/LkVuZ2xpc2hfTmFtZQ0KICAgICAgfQ0KDQogICAgICBjb25zb2xlLmxvZyh0aGlzLmNvbURyYXdEYXRhLCAnY29tRHJhd0RhdGEnKQ0KICAgIH0sDQogICAgLy8g5Zu+57q45a+85YWlDQogICAgaGFuZGVsSW1wb3J0KCkgew0KICAgICAgdGhpcy4kcmVmcy5jb21EcmF3ZGlhbG9nUmVmLmhhbmRsZU9wZW4oDQogICAgICAgICdhZGQnLA0KICAgICAgICB0aGlzLmNvbURyYXdEYXRhLA0KICAgICAgICAnJywNCiAgICAgICAgZmFsc2UsDQogICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLlN5c19Qcm9qZWN0X0lkLA0KICAgICAgICBmYWxzZQ0KICAgICAgKQ0KICAgIH0sDQogICAgLy8g6L2o6L+55Zu+DQogICAgaGFuZGxlVHJhY2socm93KSB7DQogICAgICBjb25zb2xlLmxvZyhyb3csICdyb3cnKQ0KICAgICAgdGhpcy50cmFja0RyYXdlciA9IHRydWUNCiAgICAgIHRoaXMudHJhY2tEcmF3ZXJUaXRsZSA9IHJvdy5Db2RlDQogICAgICB0aGlzLnRyYWNrRHJhd2VyRGF0YSA9IHJvdw0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8fA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/part-list/v4", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <div\r\n      v-loading=\"pgLoading\"\r\n      style=\"display: flex\"\r\n      class=\"h100\"\r\n      element-loading-text=\"加载中\"\r\n    >\r\n      <ExpandableSection v-model=\"showExpand\" :width=\"300\" class=\"cs-left fff\">\r\n        <div class=\"inner-wrapper\">\r\n          <div class=\"tree-search\">\r\n            <el-select\r\n              v-model=\"statusType\"\r\n              clearable\r\n              class=\"search-select\"\r\n              placeholder=\"导入状态选择\"\r\n            >\r\n              <el-option label=\"已导入\" value=\"已导入\" />\r\n              <el-option label=\"未导入\" value=\"未导入\" />\r\n              <el-option label=\"已变更\" value=\"已变更\" />\r\n            </el-select>\r\n            <el-input\r\n              v-model.trim=\"projectName\"\r\n              placeholder=\"关键词搜索\"\r\n              size=\"small\"\r\n              clearable\r\n              suffix-icon=\"el-icon-search\"\r\n              @blur=\"fetchTreeDataLocal\"\r\n              @clear=\"fetchTreeDataLocal\"\r\n              @keydown.enter.native=\"fetchTreeDataLocal\"\r\n            />\r\n          </div>\r\n          <el-divider class=\"cs-divider\" />\r\n          <div class=\"tree-x cs-scroll\">\r\n            <tree-detail\r\n              ref=\"tree\"\r\n              icon=\"icon-folder\"\r\n              is-custom-filter\r\n              :custom-filter-fun=\"customFilterFun\"\r\n              :loading=\"treeLoading\"\r\n              :tree-data=\"treeData\"\r\n              show-status\r\n              show-detail\r\n              :filter-text=\"filterText\"\r\n              :expanded-key=\"expandedKey\"\r\n              @handleNodeClick=\"handleNodeClick\"\r\n            >\r\n              <template #csLabel=\"{ showStatus, data }\">\r\n                <span\r\n                  v-if=\"!data.ParentNodes\"\r\n                  class=\"cs-blue\"\r\n                >({{ data.Code }})</span>{{ data.Label }}\r\n                <template v-if=\"showStatus && data.Label != '全部'\">\r\n                  <span v-if=\"data.Data.Is_Deepen_Change\" class=\"cs-tag redBg\">\r\n                    <i class=\"fourRed\">已变更</i></span>\r\n                  <span\r\n                    v-else\r\n                    :class=\"[\r\n                      'cs-tag',\r\n                      data.Data.Is_Imported == true ? 'greenBg' : 'orangeBg',\r\n                    ]\"\r\n                  >\r\n                    <i\r\n                      :class=\"[\r\n                        data.Data.Is_Imported == true\r\n                          ? 'fourGreen'\r\n                          : 'fourOrange',\r\n                      ]\"\r\n                    >{{\r\n                      data.Data.Is_Imported == true ? \"已导入\" : \"未导入\"\r\n                    }}</i>\r\n                  </span>\r\n                </template>\r\n              </template></tree-detail>\r\n          </div>\r\n        </div>\r\n      </ExpandableSection>\r\n      <div class=\"cs-right\" style=\"padding-right: 0\">\r\n        <div class=\"container\">\r\n          <div ref=\"searchDom\" class=\"cs-from\">\r\n            <div class=\"cs-search\">\r\n              <el-form\r\n                ref=\"customParams\"\r\n                :model=\"customParams\"\r\n                label-width=\"80px\"\r\n                class=\"demo-form-inline\"\r\n              >\r\n                <el-row>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item\r\n                      :label=\"levelName + '名称'\"\r\n                      prop=\"Names\"\r\n                    >\r\n                      <el-input\r\n                        v-model=\"names\"\r\n                        clearable\r\n                        style=\"width: 100%\"\r\n                        class=\"input-with-select\"\r\n                        placeholder=\"请输入内容\"\r\n                        size=\"small\"\r\n                      >\r\n                        <el-select\r\n                          slot=\"prepend\"\r\n                          v-model=\"nameMode\"\r\n                          placeholder=\"请选择\"\r\n                          style=\"width: 100px\"\r\n                        >\r\n                          <el-option label=\"模糊搜索\" :value=\"1\" />\r\n                          <el-option label=\"精确搜索\" :value=\"2\" />\r\n                        </el-select>\r\n                      </el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item\r\n                      :label=\"levelName + '种类'\"\r\n                      prop=\"Part_Type_Id\"\r\n                    >\r\n                      <el-select\r\n                        v-model=\"customParams.Part_Type_Id\"\r\n                        style=\"width: 100%\"\r\n                        placeholder=\"请选择\"\r\n                        clearable\r\n                      >\r\n                        <el-option\r\n                          v-for=\"item in partTypeOption\"\r\n                          :key=\"item.value\"\r\n                          :label=\"item.label\"\r\n                          :value=\"item.value\"\r\n                        />\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item label=\"规格\" prop=\"Spec\">\r\n                      <el-input\r\n                        v-model=\"customParams.Spec\"\r\n                        placeholder=\"请输入\"\r\n                        clearable\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item\r\n                      label=\"材质\"\r\n                      prop=\"Texture\"\r\n                    >\r\n                      <el-input\r\n                        v-model=\"customParams.Texture\"\r\n                        placeholder=\"请输入\"\r\n                        clearable\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item\r\n                      label=\"操作人\"\r\n                      prop=\"DateName\"\r\n                    >\r\n                      <el-input\r\n                        v-model=\"customParams.DateName\"\r\n                        style=\"width: 100%\"\r\n                        placeholder=\"请输入\"\r\n                        clearable\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item\r\n                      class=\"mb0\"\r\n                      label=\"批次\"\r\n                      prop=\"InstallUnit_Id\"\r\n                    >\r\n                      <el-select\r\n                        v-model=\"customParams.InstallUnit_Id\"\r\n                        multiple\r\n                        filterable\r\n                        clearable\r\n                        placeholder=\"请选择\"\r\n                        style=\"width: 100%\"\r\n                        :disabled=\"!Boolean(customParams.Area_Id)\"\r\n                      >\r\n                        <el-option\r\n                          v-for=\"item in installUnitIdNameList\"\r\n                          :key=\"item.Id\"\r\n                          :label=\"item.Name\"\r\n                          :value=\"item.Id\"\r\n                        />\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item\r\n                      label=\"是否拼接\"\r\n                      prop=\"isMontage\"\r\n                    >\r\n                      <el-select\r\n                        v-model=\"customParams.isMontage\"\r\n                        style=\"width: 100%\"\r\n                        placeholder=\"请选择\"\r\n                        clearable\r\n                      >\r\n                        <el-option\r\n                          v-for=\"item in montageOption\"\r\n                          :key=\"item.value\"\r\n                          :label=\"item.label\"\r\n                          :value=\"item.value\"\r\n                        />\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item class=\"mb0\" label-width=\"16px\">\r\n                      <el-button\r\n                        type=\"primary\"\r\n                        @click=\"handelsearch()\"\r\n                      >搜索\r\n                      </el-button>\r\n                      <el-button @click=\"handelsearch('reset')\">重置</el-button>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form>\r\n            </div>\r\n          </div>\r\n          <div class=\"fff cs-z-tb-wrapper\">\r\n            <div class=\"cs-button-box\">\r\n              <template>\r\n                <!-- <el-dropdown trigger=\"click\" placement=\"bottom-start\" @command=\"handleCommand\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    :disabled=\"!currentLastLevel\"\r\n                  >零件导入\r\n                    <i class=\"el-icon-arrow-down el-icon--right\" />\r\n                  </el-button>\r\n                  <el-dropdown-menu slot=\"dropdown\">\r\n                    <el-dropdown-item command=\"cover\">覆盖导入</el-dropdown-item>\r\n                    <el-dropdown-item command=\"add\">新增导入</el-dropdown-item>\r\n                  </el-dropdown-menu>\r\n                </el-dropdown> -->\r\n                <!-- <el-button\r\n                  type=\"primary\"\r\n                  @click=\"deepListImport\"\r\n                >零件导入</el-button>\r\n                <el-button\r\n                  :disabled=\"!selectList.length\"\r\n                  @click=\"modelListImport\"\r\n                  >导出零件排产单模板</el-button\r\n                > -->\r\n                <el-button\r\n                  type=\"primary\"\r\n                  :disabled=\"!selectList.length || selectList.length !== 1 || selectList.some(item=>item.stopFlag)\"\r\n                  @click=\"partSplit\"\r\n                >{{ levelName }}拆分</el-button>\r\n                <el-button\r\n                  :disabled=\"!selectList.length\"\r\n                  @click=\"handleExport\"\r\n                >导出{{ levelName }}</el-button>\r\n                <el-button\r\n                  :disabled=\"!selectList.length || selectList.some(item=>item.stopFlag)\"\r\n                  type=\"primary\"\r\n                  plain\r\n                  @click=\"handleBatchEdit\"\r\n                >批量编辑</el-button>\r\n                <!-- <el-button\r\n                  type=\"danger\"\r\n                  plain\r\n                  :disabled=\"!selectList.length\"\r\n                  @click=\"handleDelete\"\r\n                >删除选中</el-button> -->\r\n                <el-button\r\n                  type=\"success\"\r\n                  plain\r\n                  :disabled=\"!Boolean(customParams.Sys_Project_Id)\"\r\n                  @click=\"handelImport\"\r\n                >图纸导入\r\n                </el-button>\r\n              </template>\r\n            </div>\r\n            <div v-loading=\"countLoading\" class=\"info-box\">\r\n              <div class=\"cs-col\">\r\n                <span><span class=\"info-label\">深化总数</span><i>{{ SteelAmountTotal }} 件</i></span>\r\n                <span><span class=\"info-label\">深化总量</span><i>{{ SteelAllWeightTotal }}t</i></span>\r\n              </div>\r\n              <div class=\"cs-col\">\r\n                <span><span class=\"info-label\">排产总数</span><i>{{ SchedulingNumTotal }} 件</i></span>\r\n                <span><span class=\"info-label\">排产总量</span><i>{{ SchedulingAllWeightTotal }} t</i></span>\r\n              </div>\r\n              <div class=\"cs-col\" style=\"cursor: pointer;\" @click=\"getProcessData()\">\r\n                <span><span class=\"info-label\">完成总数</span><i>{{ FinishCountTotal }} 件</i></span>\r\n                <span><span class=\"info-label\">完成总量</span><i>{{ FinishWeightTotal }} t</i></span>\r\n              </div>\r\n            </div>\r\n            <div class=\"tb-container\">\r\n              <vxe-table\r\n                v-loading=\"tbLoading\"\r\n                :empty-render=\"{name: 'NotData'}\"\r\n                show-header-overflow\r\n                element-loading-spinner=\"el-icon-loading\"\r\n                element-loading-text=\"拼命加载中\"\r\n                empty-text=\"暂无数据\"\r\n                class=\"cs-vxe-table\"\r\n                height=\"100%\"\r\n                align=\"left\"\r\n                stripe\r\n                :data=\"tbData\"\r\n                resizable\r\n                :tooltip-config=\"{ enterable: true }\"\r\n                @checkbox-all=\"tbSelectChange\"\r\n                @checkbox-change=\"tbSelectChange\"\r\n              >\r\n                <vxe-column fixed=\"left\" type=\"checkbox\" width=\"44\" />\r\n                <vxe-column\r\n                  v-for=\"(item, index) in columns\"\r\n                  :key=\"index\"\r\n                  :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                  show-overflow=\"tooltip\"\r\n                  sortable\r\n                  :align=\"item.Align\"\r\n                  :field=\"item.Code\"\r\n                  :title=\"item.Display_Name\"\r\n                  :width=\"item.Width ? item.Width : 120\"\r\n                >\r\n                  <template #default=\"{ row }\">\r\n                    <div v-if=\"item.Code == 'Code'\">\r\n                      <el-tag v-if=\"row.Is_Change\" style=\"margin-right: 8px;\" type=\"danger\">变</el-tag>\r\n                      <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                      <el-link type=\"primary\" @click=\"getPartInfo(row)\"> {{ row[item.Code] | displayValue }}</el-link>\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'Is_Component'\">\r\n                      <span>\r\n                        <!--                      这列表叫是否非直发件 -->\r\n                        <el-tag\r\n                          v-if=\"row.Is_Component === 'True'\"\r\n                          type=\"danger\"\r\n                        >是</el-tag>\r\n                        <el-tag v-else type=\"success\">否</el-tag>\r\n                      </span>\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'Is_Split'\">\r\n                      <el-tag v-if=\"row.Is_Split === true\">是</el-tag>\r\n                      <el-tag v-else type=\"danger\">否</el-tag>\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'Deep_Material'\">\r\n                      <el-link\r\n                        type=\"primary\"\r\n                        @click=\"handleDeepMaterial(row)\"\r\n                      >查看</el-link>\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'Num' && row[item.Code] > 0\">\r\n                      <span v-if=\"row[item.Code]\"> {{ row[item.Code] | displayValue }}件</span>\r\n                      <span v-else>-</span>\r\n                    </div>\r\n                    <div\r\n                      v-else-if=\"\r\n                        item.Code == 'Schduling_Count' && row[item.Code] > 0\r\n                      \"\r\n                    >\r\n                      <el-link\r\n                        v-if=\"row[item.Code]\"\r\n                        type=\"primary\"\r\n                        @click=\"handelSchduling(row)\"\r\n                      > {{ row[item.Code] | displayValue }}件</el-link>\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'Drawing'\">\r\n                      <span\r\n                        v-if=\"row.Drawing !== '暂无'\"\r\n                        style=\"color: #298dff; cursor: pointer\"\r\n                        @click=\"getPartInfo(row)\"\r\n                      > {{ row[item.Code] | displayValue }}\r\n                      </span>\r\n                      <span v-else> {{ row[item.Code] | displayValue }}</span>\r\n                    </div>\r\n                    <div v-else>\r\n                      <span>{{ row[item.Code] !== undefined && row[item.Code] !== null ? row[item.Code] : \"-\" }}</span>\r\n                    </div>\r\n                  </template>\r\n                </vxe-column>\r\n                <vxe-column\r\n                  fixed=\"right\"\r\n                  title=\"操作\"\r\n                  width=\"150\"\r\n                  show-overflow\r\n                >\r\n                  <template #default=\"{ row }\">\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"handleView(row)\"\r\n                    >详情</el-button>\r\n                    <el-button\r\n                      :disabled=\"row.stopFlag\"\r\n                      type=\"text\"\r\n                      @click=\"handleEdit(row)\"\r\n                    >编辑</el-button>\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"handleTrack(row)\"\r\n                    >轨迹图\r\n                    </el-button>\r\n                  </template>\r\n                </vxe-column>\r\n              </vxe-table>\r\n            </div>\r\n            <div class=\"cs-bottom\">\r\n              <Pagination\r\n                class=\"cs-table-pagination\"\r\n                :total=\"total\"\r\n                max-height=\"100%\"\r\n                :page-sizes=\"tablePageSize\"\r\n                :page.sync=\"queryInfo.Page\"\r\n                :limit.sync=\"queryInfo.PageSize\"\r\n                layout=\"total, sizes, prev, pager, next, jumper\"\r\n                @pagination=\"changePage\"\r\n              >\r\n                <!--                <span class=\"pg-input\">\r\n                  <el-select\r\n                    v-model.number=\"queryInfo.PageSize\"\r\n                    allow-create\r\n                    filterable\r\n                    default-first-option\r\n                    @change=\"changePage\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"(item, index) in customPageSize\"\r\n                      :key=\"index\"\r\n                      :label=\"`${item}条/页`\"\r\n                      :value=\"item\"\r\n                    />\r\n                  </el-select>\r\n                </span>-->\r\n              </Pagination>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"card\" />\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      ref=\"content\"\r\n      v-el-drag-dialog\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"width\"\r\n      class=\"z-dialog\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :level-code=\"levelCode\"\r\n        :level-name=\"levelName\"\r\n        :select-list=\"selectList\"\r\n        :custom-params=\"customDialogParams\"\r\n        :type-id=\"customParams.TypeId\"\r\n        :type-entity=\"typeEntity\"\r\n        :project-id=\"customParams.Project_Id\"\r\n        :sys-project-id=\"customParams.Project_Id\"\r\n        @close=\"handleClose\"\r\n        @refresh=\"fetchData\"\r\n      />\r\n    </el-dialog>\r\n    <bimdialog\r\n      ref=\"dialog\"\r\n      :type-entity=\"typeEntity\"\r\n      :area-id=\"customParams.Area_Id\"\r\n      :project-id=\"customParams.Project_Id\"\r\n      @getData=\"fetchData\"\r\n      @getTreeData=\"fetchTreeData\"\r\n    />\r\n\r\n    <el-drawer\r\n      :visible.sync=\"drawersull\"\r\n      direction=\"btt\"\r\n      size=\"100%\"\r\n      destroy-on-close\r\n    >\r\n      <iframe\r\n        v-if=\"templateUrl\"\r\n        id=\"fullFrame\"\r\n        :src=\"templateUrl\"\r\n        frameborder=\"0\"\r\n        style=\"width: 96%; margin-left: 2%; height: 70vh; margin-top: 2%\"\r\n      />\r\n    </el-drawer>\r\n\r\n    <el-drawer\r\n      :visible.sync=\"trackDrawer\"\r\n      direction=\"rtl\"\r\n      size=\"30%\"\r\n      destroy-on-close\r\n      custom-class=\"trackDrawerClass\"\r\n    >\r\n      <template #title>\r\n        <div>\r\n          <span>{{ trackDrawerTitle }}</span>\r\n          <span style=\"margin-left: 24px\">{{ trackDrawerData.Num }}</span>\r\n        </div>\r\n      </template>\r\n      <TracePlot :track-drawer-data=\"trackDrawerData\" />\r\n    </el-drawer>\r\n\r\n    <comDrawdialog ref=\"comDrawdialogRef\" @getData=\"fetchData\" />\r\n    <modelDrawing ref=\"modelDrawingRef\" type=\"零件\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  Deletepart,\r\n  GetPartWeightList,\r\n  ExportPlanpartInfo,\r\n  ExportPlanpartcountInfo,\r\n  DeletepartByfindkeywodes\r\n} from '@/api/plm/production'\r\nimport { GetPartPageList } from '@/api/plm/component'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\r\nimport {\r\n  GetProjectAreaTreeList,\r\n  GetInstallUnitIdNameList\r\n} from '@/api/PRO/project'\r\n\r\nimport TreeDetail from '@/components/TreeDetail'\r\nimport TopHeader from '@/components/TopHeader'\r\nimport comImport from './component/Import'\r\nimport ComponentsHistory from './component/ComponentsHistory'\r\nimport comImportByFactory from './component/ImportByFactory'\r\nimport HistoryExport from './component/HistoryExport'\r\nimport BatchEdit from './component/BatchEditor'\r\nimport ComponentPack from './component/ComponentPack/index'\r\nimport Edit from './component/Edit'\r\nimport OneClickGeneratePack from './component/OneClickGeneratePack'\r\nimport GeneratePack from './component/GeneratePack'\r\nimport DeepMaterial from './component/DeepMaterial'\r\nimport Schduling from './component/Schduling'\r\nimport PartSplit from './component/PartSplit'\r\nimport ProcessData from './component/ProcessData.vue'\r\n\r\nimport elDragDialog from '@/directive/el-drag-dialog'\r\nimport Pagination from '@/components/Pagination'\r\nimport { timeFormat } from '@/filters'\r\n// import { Column, Header, Table, Tooltip } from 'vxe-table'\r\n// import Vue from 'vue'\r\nimport AuthButtons from '@/mixins/auth-buttons'\r\nimport bimdialog from './component/bimdialog'\r\nimport sysUseType from '@/directive/sys-use-type/index.js'\r\nimport { promptBox } from './component/messageBox'\r\n\r\nimport { combineURL } from '@/utils'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport { parseOssUrl } from '@/utils/file'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\nimport { baseUrl } from '@/utils/baseurl'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { GetSteelCadAndBimId } from '@/api/PRO/component'\r\nimport { getConfigure } from '@/api/user'\r\nimport { GetFileType } from '@/api/sys'\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport comDrawdialog from '@/views/PRO/production-order/deepen-files/dialog' // 深化文件-零件详图导入\r\nimport TracePlot from './component/TracePlot'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nimport modelDrawing from '@/views/PRO/components/modelDrawing.vue'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\n// Vue.use(Header).use(Column).use(Tooltip).use(Table)\r\nconst SPLIT_SYMBOL = '$_$'\r\nexport default {\r\n  name: 'PROPartList',\r\n  directives: { elDragDialog, sysUseType },\r\n  components: {\r\n    ExpandableSection,\r\n    TreeDetail,\r\n    TopHeader,\r\n    comImport,\r\n    comImportByFactory,\r\n    BatchEdit,\r\n    HistoryExport,\r\n    GeneratePack,\r\n    Edit,\r\n    ComponentPack,\r\n    OneClickGeneratePack,\r\n    Pagination,\r\n    bimdialog,\r\n    ComponentsHistory,\r\n    DeepMaterial,\r\n    Schduling,\r\n    comDrawdialog,\r\n    TracePlot,\r\n    PartSplit,\r\n    modelDrawing,\r\n    ProcessData\r\n  },\r\n  mixins: [AuthButtons],\r\n  data() {\r\n    return {\r\n      allStopFlag: false,\r\n      showExpand: true,\r\n      drawer: false,\r\n      drawersull: false,\r\n      iframeKey: '',\r\n      fullscreenid: '',\r\n      iframeUrl: '',\r\n      fullbimid: '',\r\n      expandedKey: '', // -1是全部\r\n      tablePageSize: tablePageSize,\r\n      partTypeOption: [],\r\n      treeData: [],\r\n      treeLoading: true,\r\n      projectName: '',\r\n      statusType: '',\r\n      searchHeight: 0,\r\n      tbData: [],\r\n      total: 0,\r\n      tbLoading: false,\r\n      pgLoading: false,\r\n      countLoading: false,\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: 10,\r\n        ParameterJson: []\r\n      },\r\n      customPageSize: [10, 20, 50, 100],\r\n      installUnitIdNameList: [], // 批次数组\r\n      nameMode: 1,\r\n      montageOption: [\r\n        { value: true, label: '是' },\r\n        { value: false, label: '否' }\r\n      ],\r\n      customParams: {\r\n        TypeId: '',\r\n        Type_Name: '',\r\n        Code: '',\r\n        Code_Like: '',\r\n        Spec: '',\r\n        DateName: '',\r\n        Texture: '',\r\n        // Keywords01: 'Code',\r\n        // Keywords01Value: '',\r\n        // Keywords02: 'Spec',\r\n        // Keywords02Value: '',\r\n        // Keywords03: 'Length',\r\n        // Keywords03Value: '',\r\n        // Keywords04: 'Texture',\r\n        // Keywords04Value: '',\r\n        isMontage: null,\r\n        InstallUnit_Id: [],\r\n        Part_Type_Id: '',\r\n        InstallUnit_Name: '',\r\n        Sys_Project_Id: '',\r\n        Project_Id: '',\r\n        Area_Id: '',\r\n        Project_Name: '',\r\n        Area_Name: ''\r\n      },\r\n      names: '',\r\n      customDialogParams: {},\r\n      dialogVisible: false,\r\n      currentComponent: '',\r\n      selectList: [],\r\n      factoryOption: [],\r\n      projectList: [],\r\n      typeOption: [],\r\n      columns: [],\r\n      columnsOption: [\r\n        // { Display_Name: '零件名称', Code: 'Code' },\r\n        // { Display_Name: '规格', Code: 'Spec' },\r\n        // { Display_Name: '长度', Code: 'Length' },\r\n        // { Display_Name: '材质', Code: 'Texture' },\r\n        // { Display_Name: '深化数量', Code: 'Num' },\r\n        // { Display_Name: '排产数量', Code: 'Schduling_Count' },\r\n        // { Display_Name: '单重', Code: 'Weight' },\r\n        // { Display_Name: '总重', Code: 'Total_Weight' },\r\n        // { Display_Name: '形状', Code: 'Shape' },\r\n        // { Display_Name: '构件名称', Code: 'Component_Code' },\r\n        // { Display_Name: '操作人', Code: 'datename' },\r\n        // { Display_Name: '操作时间', Code: 'Exdate' }\r\n      ],\r\n      title: '',\r\n      width: '60%',\r\n      tipLabel: '',\r\n      monomerList: [],\r\n      mode: '',\r\n      isMonomer: true,\r\n      historyVisible: false,\r\n      sysUseType: undefined,\r\n      deleteContent: true,\r\n      SteelAmountTotal: 0, // 深化总量\r\n      SchedulingNumTotal: 0, // 排产总量\r\n      SteelAllWeightTotal: 0, // 深化总重\r\n      SchedulingAllWeightTotal: 0, // 排产总重\r\n      FinishCountTotal: 0, // 完成数量\r\n      FinishWeightTotal: 0, // 完成重量\r\n      Unit: '',\r\n      Proportion: 0, // 专业的单位换算\r\n      command: 'cover',\r\n      currentLastLevel: false,\r\n      templateUrl: '',\r\n      currentNode: {},\r\n      comDrawData: {},\r\n      trackDrawer: false,\r\n      trackDrawerTitle: '',\r\n      trackDrawerData: {},\r\n      levelName: '',\r\n      levelCode: ''\r\n    }\r\n  },\r\n  computed: {\r\n    showP9Btn() {\r\n      return this.AuthButtons.buttons.some((item) => item.Code === 'p9BtnAdd')\r\n    },\r\n    typeEntity() {\r\n      return this.typeOption.find((i) => i.Id === this.customParams.TypeId)\r\n    },\r\n    PID() {\r\n      return this.projectList.find(\r\n        (i) => i.Sys_Project_Id === this.customParams.Project_Id\r\n      )?.Id\r\n    },\r\n    filterText() {\r\n      return this.projectName + SPLIT_SYMBOL + this.statusType\r\n    }\r\n  },\r\n  watch: {\r\n    'customParams.TypeId': function(newValue, oldValue) {\r\n      console.log({ oldValue })\r\n      if (oldValue && oldValue !== '0') {\r\n        this.fetchData()\r\n      }\r\n    },\r\n    names(n, o) {\r\n      this.changeMode()\r\n    },\r\n    nameMode(n, o) {\r\n      this.changeMode()\r\n    }\r\n  },\r\n  mounted() {\r\n\r\n  },\r\n  async created() {\r\n    const { currentBOMInfo } = await GetBOMInfo(0)\r\n    console.log('list', currentBOMInfo)\r\n    this.levelName = currentBOMInfo?.Display_Name\r\n    this.levelCode = currentBOMInfo?.Code\r\n    await this.getTypeList()\r\n    // await this.fetchData()\r\n    await this.getTableConfig('plm_parts_page_list')\r\n\r\n    this.fetchTreeData()\r\n    this.getFileType()\r\n    if (this.Keywords01Value === '是') {\r\n      console.log('this.Keywords01Value', this.Keywords01Value)\r\n    }\r\n    this.$nextTick(() => {\r\n      this.pgLoading = true\r\n      console.log(this.columns)\r\n      this.getPartWeightList()\r\n      this.getPartType()\r\n      this.searchHeight = this.$refs.searchDom.offsetHeight + 327\r\n    })\r\n  },\r\n  methods: {\r\n    changeMode() {\r\n      if (this.nameMode === 1) {\r\n        this.customParams.Code_Like = this.names\r\n        this.customParams.Code = ''\r\n      } else {\r\n        this.customParams.Code_Like = ''\r\n        this.customParams.Code = this.names.replace(/\\s+/g, '\\n')\r\n      }\r\n    },\r\n    // 项目区域数据集\r\n    fetchTreeData() {\r\n      GetProjectAreaTreeList({ MenuId: this.$route.meta.Id, Type: 0, projectName: this.projectName, Level: this.levelCode }).then((res) => {\r\n        // const resAll = [\r\n        //   {\r\n        //     ParentNodes: null,\r\n        //     Id: '-1',\r\n        //     Code: '全部',\r\n        //     Label: '全部',\r\n        //     Level: null,\r\n        //     Data: {},\r\n        //     Children: []\r\n        //   }\r\n        // ]\r\n        // const resData = resAll.concat(res.Data)\r\n        if (res.Data.length === 0) {\r\n          this.treeLoading = false\r\n          this.pgLoading = false\r\n          return\r\n        }\r\n        const resData = res.Data\r\n        resData.map((item) => {\r\n          if (item.Children.length === 0) {\r\n            item.Is_Imported = false\r\n          } else {\r\n            item.Data.Is_Imported = item.Children.some((ich) => {\r\n              return ich.Data.Is_Imported === true\r\n            })\r\n            item.Is_Directory = true\r\n            item.Children.map((it) => {\r\n              if (it.Children.length > 0) {\r\n                it.Is_Directory = true\r\n              }\r\n            })\r\n          }\r\n        })\r\n        this.treeData = resData\r\n        if (Object.keys(this.currentNode).length === 0) {\r\n          // this.fetchData()\r\n          this.setKey()\r\n        } else {\r\n          this.handleNodeClick(this.currentNode)\r\n        }\r\n        this.treeLoading = false\r\n        // this.expandedKey = this.customParams.Area_Id ? this.customParams.Area_Id : this.customParams.Project_Id ? this.customParams.Project_Id : resData[0].Id // '-1'\r\n        // this.customParams.Sys_Project_Id = this.customParams.Sys_Project_Id || resData[0].Data.Sys_Project_Id\r\n        // this.customParams.Project_Id = this.customParams.Project_Id || resData[0].Data.Id\r\n        // this.customParams.Area_Name = ''\r\n        // this.treeLoading = false\r\n        // this.fetchData()\r\n      })\r\n    },\r\n    // 设置默认选中第一个区域末级节点\r\n    setKey() {\r\n      const deepFilter = (tree) => {\r\n        for (let i = 0; i < tree.length; i++) {\r\n          const item = tree[i]\r\n          const { Data, Children } = item\r\n          console.log(Data)\r\n          if (Data.ParentId && !Children?.length) {\r\n            console.log(Data, '????')\r\n            this.currentNode = Data\r\n            this.handleNodeClick(item)\r\n            return\r\n          } else {\r\n            if (Children && Children.length > 0) {\r\n              return deepFilter(Children)\r\n            } else {\r\n              this.handleNodeClick(item)\r\n              return\r\n            }\r\n          }\r\n        }\r\n      }\r\n      return deepFilter(this.treeData)\r\n    },\r\n    // 选中左侧项目节点\r\n    handleNodeClick(data) {\r\n      this.handelsearch('reset', false)\r\n      this.currentNode = data\r\n      this.expandedKey = data.Id\r\n      const dataId = data.Id === '-1' ? '' : data.Id\r\n      console.log('nodeData', data)\r\n      if (data.ParentNodes) {\r\n        this.customParams.Project_Id = data.Data.Project_Id\r\n        this.customParams.Area_Id = data.Id\r\n        this.customParams.Area_Name = data.Data.Name\r\n        this.customParams.Sys_Project_Id = data.Data.Sys_Project_Id\r\n      } else {\r\n        this.customParams.Project_Id = dataId\r\n        this.customParams.Area_Id = ''\r\n        this.customParams.Area_Name = data.Data.Name\r\n        this.customParams.Sys_Project_Id = data.Data.Sys_Project_Id\r\n      }\r\n      console.log(\r\n        this.customParams.Sys_Project_Id,\r\n        'this.customParams.Sys_Project_Id============11111'\r\n      )\r\n      console.log(\r\n        this.customParams.Area_Id,\r\n        'this.customParams.Area_Id============11111'\r\n      )\r\n      this.currentLastLevel = !!(data.Data.Level && data.Children.length === 0)\r\n      if (this.currentLastLevel) {\r\n        this.customParams.Project_Name = data.Data?.Project_Name\r\n        this.customParams.Area_Name = data.Label\r\n      }\r\n      this.queryInfo.Page = 1\r\n      this.pgLoading = true\r\n      this.fetchList()\r\n      console.log(this.customParams.Area_Id)\r\n      this.getInstallUnitIdNameList(dataId, data)\r\n      this.getPartWeightList()\r\n    },\r\n\r\n    // 获取批次\r\n    getInstallUnitIdNameList(id, data) {\r\n      if (id === '' || data.Children.length > 0) {\r\n        this.installUnitIdNameList = []\r\n      } else {\r\n        GetInstallUnitIdNameList({ Area_Id: id }).then((res) => {\r\n          this.installUnitIdNameList = res.Data\r\n        })\r\n      }\r\n    },\r\n    // 工序完成量\r\n    getProcessData() {\r\n      const customParamsData = JSON.parse(JSON.stringify(this.customParams))\r\n      const InstallUnit_Ids = customParamsData.InstallUnit_Id.join(',')\r\n      delete customParamsData.InstallUnit_Id\r\n\r\n      this.width = '40%'\r\n      this.generateComponent(`${this.levelName}工序完成量`, 'ProcessData')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(customParamsData, InstallUnit_Ids, this.selectList.map((v) => v.Part_Aggregate_Id).toString())\r\n      })\r\n    },\r\n    getTableConfig(code) {\r\n      return new Promise((resolve) => {\r\n        GetGridByCode({\r\n          code:\r\n            code +\r\n            ',' +\r\n            this.typeOption.find((i) => i.Id === this.customParams.TypeId).Code\r\n        }).then((res) => {\r\n          const { IsSucceed, Data, Message } = res\r\n          if (IsSucceed) {\r\n            if (!Data) {\r\n              this.$message.error('当前专业没有配置相对应表格')\r\n              this.tbLoading = true\r\n              return\r\n            }\r\n            this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n            const list = Data.ColumnList || []\r\n            const sortList = list.sort((a, b) => a.Sort - b.Sort)\r\n            this.columns = sortList\r\n              .filter((v) => v.Is_Display)\r\n              .map((item) => {\r\n                if (item.Code === 'Code') {\r\n                  item.fixed = 'left'\r\n                }\r\n\r\n                return item\r\n              })\r\n            this.queryInfo.PageSize = +Data.Grid.Row_Number || 20\r\n            resolve(this.columns)\r\n            console.log(this.columns)\r\n            const selectOption = JSON.parse(JSON.stringify(this.columns))\r\n            console.log(selectOption)\r\n            this.columnsOption = selectOption.filter((v) => {\r\n              return (\r\n                v.Display_Name !== '操作时间' &&\r\n                v.Display_Name !== '模型ID' &&\r\n                v.Display_Name !== '深化资料' &&\r\n                v.Display_Name !== '备注' &&\r\n                v.Display_Name !== '排产数量' &&\r\n                v.Code.indexOf('Attr') === -1 &&\r\n                v.Display_Name !== '批次'\r\n              )\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    async fetchList() {\r\n      const customParamsData = JSON.parse(JSON.stringify(this.customParams))\r\n      const InstallUnit_Ids = customParamsData.InstallUnit_Id.join(',')\r\n      delete customParamsData.InstallUnit_Id\r\n      await GetPartPageList({\r\n        ...this.queryInfo,\r\n        ...customParamsData,\r\n        Code: customParamsData.Code.trim().replaceAll(' ', '\\n'),\r\n        InstallUnit_Ids: InstallUnit_Ids\r\n      })\r\n        .then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.queryInfo.PageSize = res.Data.PageSize\r\n            this.total = res.Data.TotalCount\r\n            this.tbData = res.Data.Data.map((v) => {\r\n              v.Is_Main = v.Is_Main ? '是' : '否'\r\n              v.Exdate = timeFormat(v.Exdate, '{y}-{m}-{d} {h}:{i}:{s}')\r\n              // console.log(v)\r\n              return v\r\n            })\r\n            this.selectList = []\r\n            this.getStopList()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n        .finally(() => {\r\n          this.tbLoading = false\r\n          this.pgLoading = false\r\n        })\r\n    },\r\n    async getStopList() {\r\n      const submitObj = this.tbData.map(item => {\r\n        return {\r\n          Id: item.Part_Aggregate_Id,\r\n          Type: 1,\r\n          Bom_Level: this.levelCode\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = item.Is_Stop !== null\r\n          })\r\n          this.tbData.forEach(row => {\r\n            if (stopMap[row.Part_Aggregate_Id]) {\r\n              this.$set(row, 'stopFlag', stopMap[row.Part_Aggregate_Id])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async fetchData() {\r\n      console.log('更新列表')\r\n      // 分开获取，提高接口速度\r\n      await this.getTableConfig('plm_parts_page_list')\r\n      this.tbLoading = true\r\n      this.fetchList().then((res) => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    async changePage() {\r\n      this.tbLoading = true\r\n      if (\r\n        typeof this.queryInfo.PageSize !== 'number' ||\r\n        this.queryInfo.PageSize < 1\r\n      ) {\r\n        this.queryInfo.PageSize = 10\r\n      }\r\n      this.fetchList().then((res) => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    // tbSelectChange(array) {\r\n    //   console.log('array', array)\r\n    //   this.selectList = array.records\r\n    //   console.log('this.selectList', this.selectList)\r\n    // },\r\n    getTbData(data) {\r\n      const { YearAllWeight, YearSteel, CountInfo } = data\r\n      // this.tipLabel = `累计上传构件${YearSteel}件，总重${YearAllWeight}t。`\r\n      this.tipLabel = CountInfo\r\n    },\r\n    async getTypeList() {\r\n      let res = null\r\n      let data = null\r\n      res = await GetFactoryProfessionalByCode({\r\n        factoryId: localStorage.getItem('CurReferenceId')\r\n      })\r\n      data = res.Data\r\n      if (res.IsSucceed) {\r\n        this.typeOption = Object.freeze(data)\r\n        if (this.typeOption.length > 0) {\r\n          this.Proportion = data[0].Proportion\r\n          this.Unit = data[0].Unit\r\n          this.customParams.TypeId = this.typeOption[0]?.Id\r\n          this.customParams.Type_Name = this.typeOption[0]?.Name\r\n        }\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    handleDelete() {\r\n      this.$confirm('此操作将删除选择数据, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          Deletepart({\r\n            ids: this.selectList.map((v) => v.Part_Aggregate_Id).toString()\r\n          }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.fetchData()\r\n              this.$message({\r\n                message: '删除成功',\r\n                type: 'success'\r\n              })\r\n              this.getPartWeightList()\r\n              this.fetchTreeData()\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n    handleEdit(row) {\r\n      this.width = '45%'\r\n      this.generateComponent(`编辑${this.levelName}`, 'Edit')\r\n      this.$nextTick((_) => {\r\n        row.isReadOnly = false\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n    handleBatchEdit() {\r\n      const SchedulArr = this.selectList.filter((item) => {\r\n        return item.Schduling_Count != null && item.Schduling_Count > 0\r\n      })\r\n      if (SchedulArr.length > 0) {\r\n        this.$message({\r\n          type: 'error',\r\n          message: `选中行包含已排产的${this.levelName},编辑信息需要进行变更操作`\r\n        })\r\n      } else {\r\n        this.width = '40%'\r\n        this.generateComponent('批量编辑', 'BatchEdit')\r\n        this.$nextTick((_) => {\r\n          this.$refs['content'].init(this.selectList, this.columnsOption)\r\n        })\r\n      }\r\n    },\r\n    handleView(row) {\r\n      this.width = '45%'\r\n      this.generateComponent('详情', 'Edit')\r\n      this.$nextTick((_) => {\r\n        row.isReadOnly = true\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n    async handleExport() {\r\n      const obj = {\r\n        Part_Aggregate_Ids: this.selectList\r\n          .map((v) => v.Part_Aggregate_Id)\r\n          .toString(),\r\n        ProfessionalCode: this.typeEntity.Code\r\n      }\r\n      ExportPlanpartInfo(obj).then((res) => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    // 零件拆分\r\n    partSplit() {\r\n      const Type_Name = this.selectList[0].Type_Name\r\n      const Schduling_Count = this.selectList[0].Schduling_Count\r\n      if (Type_Name === '直发件' || Schduling_Count > 0) {\r\n        this.$message({\r\n          type: 'error',\r\n          message: `${this.levelName}已排产或是直发件,无法拆分`\r\n        })\r\n        return\r\n      }\r\n      this.width = '45%'\r\n      this.generateComponent(`${this.levelName}拆分`, 'PartSplit')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(this.selectList)\r\n      })\r\n    },\r\n    modelListImport() {\r\n      const obj = {\r\n        Part_Aggregate_Ids: this.selectList\r\n          .map((v) => v.Part_Aggregate_Id)\r\n          .toString(),\r\n        ProfessionalCode: this.typeEntity.Code\r\n      }\r\n      ExportPlanpartcountInfo(obj).then((res) => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n          if (res.Message) {\r\n            this.$alert(res.Message, '导出通知', {\r\n              confirmButtonText: '我知道了'\r\n            })\r\n          }\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    // 覆盖导入 or 新增导入\r\n    handleCommand(command) {\r\n      console.log(command, 'command')\r\n      this.command = command\r\n      this.deepListImport()\r\n    },\r\n    deepListImport() {\r\n      const fileType = {\r\n        Catalog_Code: 'PLMDeepenFiles',\r\n        Code: this.typeEntity.Code,\r\n        name: this.typeEntity.Name\r\n      }\r\n      this.$refs.dialog.handleOpen(\r\n        'add',\r\n        fileType,\r\n        null,\r\n        true,\r\n        this.PID,\r\n        this.command,\r\n        this.customParams\r\n      )\r\n    },\r\n    async handleAllDelete() {\r\n      console.log(this.customParams.Project_Id)\r\n      if (this.customParams.Project_Id) {\r\n        await promptBox({ title: '删除' })\r\n        await DeletepartByfindkeywodes({\r\n          ...this.customParams,\r\n          ...this.queryInfo\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message.success('删除成功')\r\n            this.fetchData()\r\n            this.fetchTreeData()\r\n          } else {\r\n            this.$message.error(res.Message)\r\n          }\r\n        })\r\n      } else {\r\n        this.$message.warning('请先选择项目')\r\n      }\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    generateComponent(title, component) {\r\n      this.title = title\r\n      this.currentComponent = component\r\n      this.dialogVisible = true\r\n    },\r\n    // 点击搜索\r\n    handelsearch(reset, hasSearch = true) {\r\n      this.deleteContent = false\r\n      if (reset) {\r\n        this.$refs.customParams.resetFields()\r\n        this.deleteContent = true\r\n        this.names = ''\r\n      }\r\n      hasSearch && this.fetchData()\r\n      this.getPartWeightList()\r\n    },\r\n    // 深化资料查看\r\n    handleDeepMaterial(row) {\r\n      console.log('handleDeepMaterial')\r\n      this.width = '45%'\r\n      this.generateComponent('查看深化资料', 'DeepMaterial')\r\n      this.$nextTick((_) => {\r\n        row.isReadOnly = false\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n    // 排产数量\r\n    handelSchduling(row) {\r\n      this.width = '45%'\r\n      this.generateComponent('生产详情', 'Schduling')\r\n      this.$nextTick((_) => {\r\n        row.isReadOnly = false\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n    // 零件排产信息\r\n    getPartWeightList() {\r\n      this.countLoading = true\r\n      const customParamsData = JSON.parse(JSON.stringify(this.customParams))\r\n      const InstallUnit_Ids = customParamsData.InstallUnit_Id.join(',')\r\n      delete customParamsData.InstallUnit_Id\r\n      GetPartWeightList({\r\n        ...this.queryInfo,\r\n        ...customParamsData,\r\n        InstallUnit_Ids\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.SteelAmountTotal = Math.round(res.Data.DeepenNum * 1000) / 1000 // 深化总量\r\n          this.SchedulingNumTotal =\r\n            Math.round(res.Data.SchedulingNum * 1000) / 1000 // 排产总量\r\n          this.SteelAllWeightTotal =\r\n            Math.round(res.Data.DeepenWeight * 1000) / 1000 // 深化总重\r\n          this.SchedulingAllWeightTotal =\r\n            Math.round(res.Data.SchedulingWeight * 1000) / 1000 // 排产总重\r\n          this.FinishCountTotal =\r\n            Math.round(res.Data.Finish_Count * 1000) / 1000 // 完成总数\r\n          this.FinishWeightTotal =\r\n            Math.round(res.Data.Finish_Weight * 1000) / 1000 // 完成总重\r\n          console.log(' this.SteelAllWeightTotal', this.SteelAllWeightTotal)\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n        this.countLoading = false\r\n      })\r\n    },\r\n    tbSelectChange(array) {\r\n      this.selectList = array.records\r\n      this.SteelAmountTotal = 0\r\n      this.SchedulingNumTotal = 0\r\n      this.SteelAllWeightTotal = 0\r\n      this.SchedulingAllWeightTotal = 0\r\n      this.FinishCountTotal = 0\r\n      this.FinishWeightTotal = 0\r\n      let SteelAllWeightTotalTemp = 0\r\n      let SchedulingAllWeightTotalTemp = 0\r\n      let FinishWeightTotalTemp = 0\r\n      if (this.selectList.length > 0) {\r\n        this.selectList.forEach((item) => {\r\n          const schedulingNum =\r\n            item.Schduling_Count == null ? 0 : item.Schduling_Count\r\n          this.SteelAmountTotal += item.Num\r\n          this.SchedulingNumTotal += Number(item.Schduling_Count)\r\n          this.FinishCountTotal += item.Finish_Count\r\n          SteelAllWeightTotalTemp += item.Total_Weight\r\n          SchedulingAllWeightTotalTemp += item.Weight * schedulingNum\r\n          FinishWeightTotalTemp += item.Finish_Weight\r\n        })\r\n        this.SteelAllWeightTotal =\r\n          Math.round((SteelAllWeightTotalTemp / this.Proportion) * 1000) / 1000\r\n        this.SchedulingAllWeightTotal =\r\n          Math.round((SchedulingAllWeightTotalTemp / this.Proportion) * 1000) /\r\n          1000\r\n        this.FinishWeightTotal =\r\n          Math.round((FinishWeightTotalTemp / this.Proportion) * 1000) / 1000\r\n      } else {\r\n        this.getPartWeightList()\r\n      }\r\n    },\r\n    fetchTreeDataLocal() {\r\n      // this.filterText = this.projectName\r\n    },\r\n    getPartInfo(row) {\r\n      const drawingData = row.Drawing ? row.Drawing.split(',') : [] // 图纸数据\r\n      const fileUrlData = row.File_Url ? row.File_Url.split(',') : [] // 图纸数据文件地址数据\r\n      if (fileUrlData.length === 0) {\r\n        this.$message({\r\n          message: `当前${this.levelName}无图纸`,\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      if (drawingData.length > 0 && fileUrlData.length > 0) {\r\n        this.drawingActive = drawingData[0]\r\n      }\r\n      if (drawingData.length > 0 && fileUrlData.length > 0) {\r\n        this.drawingDataList = drawingData.map((item, index) => ({\r\n          name: item,\r\n          label: item,\r\n          url: fileUrlData[index]\r\n        }))\r\n      }\r\n      this.getPartInfoDrawing(row)\r\n    },\r\n\r\n    getPartInfoDrawing(row) {\r\n      const importDetailId = row.Part_Aggregate_Id\r\n      GetSteelCadAndBimId({ importDetailId: importDetailId }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const drawingData = {\r\n            'extensionName': res.Data[0].ExtensionName,\r\n            'fileBim': res.Data[0].fileBim,\r\n            'IsUpload': res.Data[0].IsUpload,\r\n            'Code': row.Code,\r\n            'Sys_Project_Id': row.Sys_Project_Id\r\n          }\r\n          this.$refs.modelDrawingRef.dwgInit(drawingData)\r\n        }\r\n      })\r\n    },\r\n\r\n    /*    handleViewDwg(row) {\r\n      if (!row.File_Url) {\r\n        this.$message({\r\n          message: '当前零件无图纸',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      window.open('http://dwgv1.bimtk.com:5432/?CadUrl=' + parseOssUrl(row.File_Url), '_blank')\r\n    },*/\r\n    customFilterFun(value, data, node) {\r\n      const arr = value.split(SPLIT_SYMBOL)\r\n      const labelVal = arr[0]\r\n      const statusVal = arr[1]\r\n      if (!value) return true\r\n      let parentNode = node.parent\r\n      let labels = [node.label]\r\n      let status = [\r\n        data.Data.Is_Deepen_Change\r\n          ? '已变更'\r\n          : data.Data.Is_Imported\r\n            ? '已导入'\r\n            : '未导入'\r\n      ]\r\n      let level = 1\r\n      while (level < node.level) {\r\n        labels = [...labels, parentNode.label]\r\n        status = [\r\n          ...status,\r\n          data.Data.Is_Deepen_Change\r\n            ? '已变更'\r\n            : data.Data.Is_Imported\r\n              ? '已导入'\r\n              : '未导入'\r\n        ]\r\n        parentNode = parentNode.parent\r\n        level++\r\n      }\r\n      labels = labels.filter((v) => !!v)\r\n      status = status.filter((v) => !!v)\r\n      let resultLabel = true\r\n      let resultStatus = true\r\n      if (this.statusType) {\r\n        resultStatus = status.some((s) => s.indexOf(statusVal) !== -1)\r\n      }\r\n      if (this.projectName) {\r\n        resultLabel = labels.some((s) => s.indexOf(labelVal) !== -1)\r\n      }\r\n      return resultLabel && resultStatus\r\n    },\r\n    getPartType() {\r\n      GetPartTypeList({ Part_Grade: 0 }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.partTypeOption = res.Data.map((v) => {\r\n            return {\r\n              label: v.Name,\r\n              value: v.Id\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async getFileType() {\r\n      const params = {\r\n        catalogCode: 'PLMDeepenFiles'\r\n      }\r\n      const res = await GetFileType(params)\r\n      // 获取构件详图\r\n      const data = res.Data.find((v) => v.Label === '零件详图')\r\n\r\n      this.comDrawData = {\r\n        isSHQD: false,\r\n        Id: data.Id,\r\n        name: data.Label,\r\n        Catalog_Code: data.Code,\r\n        Code: data.Data?.English_Name\r\n      }\r\n\r\n      console.log(this.comDrawData, 'comDrawData')\r\n    },\r\n    // 图纸导入\r\n    handelImport() {\r\n      this.$refs.comDrawdialogRef.handleOpen(\r\n        'add',\r\n        this.comDrawData,\r\n        '',\r\n        false,\r\n        this.customParams.Sys_Project_Id,\r\n        false\r\n      )\r\n    },\r\n    // 轨迹图\r\n    handleTrack(row) {\r\n      console.log(row, 'row')\r\n      this.trackDrawer = true\r\n      this.trackDrawerTitle = row.Code\r\n      this.trackDrawerData = row\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/styles/mixin.scss\";\r\n@import \"~@/styles/tabs.scss\";\r\n.min900 {\r\n  min-width: 900px;\r\n  overflow: auto;\r\n}\r\n.z-dialog {\r\n  ::v-deep {\r\n    .el-dialog__header {\r\n      background-color: #298dff;\r\n\r\n      .el-dialog__title,\r\n      .el-dialog__close {\r\n        color: #ffffff;\r\n      }\r\n    }\r\n\r\n    .el-dialog__body {\r\n      // max-height: 750px;\r\n      overflow: auto;\r\n      @include scrollBar;\r\n\r\n      &::-webkit-scrollbar {\r\n        width: 8px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.container {\r\n  padding: 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100%;\r\n}\r\n\r\n.tb-container {\r\n  padding: 0 16px 0 16px;\r\n  flex: 1;\r\n  height: 0; //解决溢出问题\r\n  // .vxe-table {\r\n  //   height: calc(100%);\r\n  // }\r\n}\r\n\r\n.cs-z-tb-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 0; //解决溢出问题\r\n}\r\n\r\n.cs-bottom {\r\n  padding: 8px 16px 8px 16px;\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: row-reverse;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  box-sizing: border-box;\r\n\r\n  .data-info {\r\n    .info-x {\r\n      margin-right: 20px;\r\n    }\r\n  }\r\n  .pg-input {\r\n    width: 100px;\r\n    margin-right: 20px;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  text-align: right;\r\n  margin: 0;\r\n  padding: 0;\r\n  ::v-deep .el-input--small .el-input__inner {\r\n    height: 28px;\r\n    line-height: 28px;\r\n  }\r\n}\r\n\r\n.cs-from {\r\n  background-color: #ffffff;\r\n  border-radius: 4px;\r\n  margin-bottom: 16px;\r\n  padding: 16px 16px 0 16px;\r\n  display: flex;\r\n  font-size: 14px;\r\n  color: rgba(34, 40, 52, 0.65);\r\n  label {\r\n    display: inline-block;\r\n    margin-right: 20px;\r\n    white-space: nowrap;\r\n    vertical-align: top;\r\n  }\r\n  .cs-from-title {\r\n    flex: 1;\r\n  }\r\n\r\n  .mb0 {\r\n    margin-bottom: 0;\r\n\r\n    ::v-deep {\r\n      .el-form-item {\r\n        margin-bottom: 0\r\n      }\r\n    }\r\n  }\r\n\r\n  .cs-search {\r\n    width: 100%;\r\n    label {\r\n      margin-bottom: 10px;\r\n    }\r\n    button {\r\n      margin-right: 10px;\r\n      margin-left: 0;\r\n      margin-bottom: 10px;\r\n    }\r\n  }\r\n}\r\n\r\n.input-with-select {\r\n  width: 250px;\r\n}\r\n\r\n.cs-button-box {\r\n  padding: 16px 16px 6px 16px;\r\n  position: relative;\r\n  background-color: #ffffff;\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  flex-wrap: wrap;\r\n\r\n  ::v-deep .el-button {\r\n    margin-left: 0 !important;\r\n    margin-right: 10px !important;\r\n    margin-bottom: 10px !important;\r\n  }\r\n}\r\n.info-box {\r\n  margin: 0 16px 16px 16px;\r\n  display: flex;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  height: 64px;\r\n  background: rgba(41, 141, 255, 0.05);\r\n\r\n  .cs-col {\r\n    display: flex;\r\n    justify-content: space-evenly;\r\n    flex-direction: column;\r\n    margin-right: 64px;\r\n  }\r\n\r\n  .info-label {\r\n    color: #999999;\r\n  }\r\n\r\n  i {\r\n    color: #00c361;\r\n    font-style: normal;\r\n    font-weight: 600;\r\n    margin-left: 10px;\r\n  }\r\n}\r\n\r\n::v-deep .el-tree-node {\r\n  min-width: 240px;\r\n  width: min-content;\r\n}\r\n::v-deep .el-tree-node > .el-tree-node__children {\r\n  overflow: inherit;\r\n}\r\n\r\n.stretch-btn {\r\n  position: absolute;\r\n  width: 20px;\r\n  height: 130px;\r\n  top: calc((100% - 130px) / 2);\r\n\r\n  display: flex;\r\n  align-items: center;\r\n  background: #eff1f3;\r\n  cursor: pointer;\r\n  .center-btn {\r\n    width: 14px;\r\n    height: 100px;\r\n    border-radius: 0 9px 9px 0;\r\n    background-color: #8c95a8;\r\n    > i {\r\n      line-height: 100px;\r\n      text-align: center;\r\n      color: #fff;\r\n    }\r\n  }\r\n}\r\n.cs-left {\r\n  position: relative;\r\n  margin-right: 20px;\r\n  .inner-wrapper {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    padding: 16px 10px 16px 16px;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n\r\n    .tree-search {\r\n      display: flex;\r\n\r\n      .search-select {\r\n        margin-right: 8px;\r\n      }\r\n    }\r\n\r\n    .tree-x {\r\n      overflow: hidden;\r\n      margin-top: 16px;\r\n      flex: 1;\r\n\r\n      .cs-scroll {\r\n        overflow-y: auto;\r\n        @include scrollBar;\r\n      }\r\n\r\n      .el-tree {\r\n        height: 100%;\r\n\r\n        //::v-deep {\r\n        //  .el-tree-node {\r\n        //    min-width: 240px;\r\n        //    width: min-content;\r\n        //\r\n        //    .el-tree-node__children {\r\n        //      overflow: inherit;\r\n        //    }\r\n        //  }\r\n        //}\r\n      }\r\n    }\r\n  }\r\n}\r\n.cs-left-contract {\r\n  padding-left: 0;\r\n  position: relative;\r\n  width: 20px;\r\n  margin-right: 26px;\r\n}\r\n.cs-right {\r\n  padding-right: 0;\r\n  flex: 1;\r\n  width: 0;\r\n}\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n.fourGreen {\r\n  color: #00c361;\r\n  font-style: normal;\r\n}\r\n\r\n.fourOrange {\r\n  color: #ff9400;\r\n  font-style: normal;\r\n}\r\n\r\n.fourRed {\r\n  color: #ff0000;\r\n  font-style: normal;\r\n}\r\n\r\n.cs-blue {\r\n  color: #5ac8fa;\r\n}\r\n\r\n.orangeBg {\r\n  background: rgba(255, 148, 0, 0.1);\r\n}\r\n\r\n.redBg {\r\n  background: rgba(252, 107, 127, 0.1);\r\n}\r\n.greenBg {\r\n  background: rgba(0, 195, 97, 0.1);\r\n}\r\n\r\n.cs-tag {\r\n  margin-left: 8px;\r\n  font-size: 12px;\r\n  padding: 2px 4px;\r\n  border-radius: 1px;\r\n}\r\n.cs-tree-x {\r\n  ::v-deep {\r\n    .el-select {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n.cs-divider {\r\n  margin: 16px 0 0 0;\r\n}\r\n</style>\r\n"]}]}
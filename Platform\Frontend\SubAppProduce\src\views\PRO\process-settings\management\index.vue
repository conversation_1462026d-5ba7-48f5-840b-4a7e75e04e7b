<template>
  <div class="abs100 cs-z-flex-pd16-wrap">
    <div v-loading="pageLoading" class="cs-z-page-main-content">
      <div class="page-header">
        <div class="header-left">
          <el-button icon="el-icon-plus" type="primary" size="small" @click="handleDialog('add')">新增</el-button>
          <el-button type="success" size="small" @click="handleRecognitionConfig">导入识别配置</el-button>
          <el-button type="success" size="small" @click="handleConfig">{{ partName }}识别配置</el-button>
          <el-button type="success" size="small" @click="handleConfigComp">{{ comName }}识别配置</el-button>
          <el-button v-for="item in unitPartList" :key="item.Code" type="success" size="small" @click="handleUnitPartConfig(item)">
            {{ item.Display_Name }}识别配置</el-button>
          <el-button type="primary" size="small" @click="handleTakeConfig">{{ partName }}领用配置</el-button>
        </div>
        <div class="header-right">
          <el-form :inline="true" :model="formInline" class="demo-form-inline" style="line-height: 32px">
            <el-form-item label="工序名称">
              <el-input v-model="formInline.name" clearable placeholder="请输入工序名称" />
            </el-form-item>
            <el-form-item label="代号">
              <el-input v-model="formInline.code" clearable placeholder="请输入代号" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="fetchData">查询</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <el-table class="cs-custom-table tb" border stripe height="100%" :data="tableData" style="width: 100%">
        <template #empty>
          <ElTableEmpty />
        </template>
        <el-table-column prop="Professional_Code" label="专业">
          <template slot-scope="{ row }">
            {{ row.Professional_Code || '-' }}
          </template>
        </el-table-column>
        <el-table-column min-width="150px" prop="Name" label="工序名称">
          <template slot-scope="{ row }">
            {{ row.Name || '-' }}
          </template>
        </el-table-column>
        <el-table-column min-width="120px" prop="Code" label="代号">
          <template slot-scope="{ row }">
            {{ row.Code || '-' }}
          </template>
        </el-table-column>
        <el-table-column min-width="150px" prop="Type_Name" label="类型">
          <template slot-scope="{ row }">
            {{ row.Type_Name || '-' }}
            <!-- <span v-if="row.Type === 1" style="color: #d29730">
              <i class="iconfont icon-steel cs-tb-icon" />
              构件工序
            </span>
            <span v-if="row.Type === 2" style="color: #20bbc7">
              <i class="iconfont icon-material-filled cs-tb-icon" />
              零件工序
            </span>
            <span v-if="row.Type === 3" style="color: #de85e4">
              <i class="iconfont icon-material-filled cs-tb-icon" />
              部件工序
            </span> -->
          </template>
        </el-table-column>
        <!-- <el-table-column  prop="Code" label="工序代码" /> -->

        <el-table-column min-width="150px" prop="Coordinate_UserName" label="工序协调人" align="center">
          <template slot-scope="{ row }">
            {{ row.Coordinate_UserName || '-' }}
          </template>
        </el-table-column>
        <el-table-column min-width="150px" prop="Is_Need_Check" label="是否专检" align="center">
          <template slot-scope="{ row }">
            <el-tag v-if="row.Is_Need_Check" type="success">是</el-tag>
            <el-tag v-else type="danger">否</el-tag>
          </template>
        </el-table-column>
        <el-table-column min-width="150px" prop="Check_Style" label="专检方式">
          <template slot-scope="{ row }">
            {{
              row.Check_Style == 0 ? "抽检" : row.Check_Style == 1 ? "全检" : "-"
            }}
          </template>
        </el-table-column>
        <el-table-column min-width="150px" prop="Is_Inter_Check" label="是否互检">
          <template slot-scope="{ row }">
            <el-tag v-if="row.Is_Inter_Check" type="success">是</el-tag>
            <el-tag v-else type="danger">否</el-tag>
          </template>
        </el-table-column>
        <el-table-column min-width="150px" prop="Team_Names" label="加工班组" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <div v-if="row.Team_Names.length">
              <el-tag v-for="(item, index) in row.Team_Names.split(';')" :key="index" class="cs-tag">
                {{ item }}
              </el-tag>
            </div>
            <div v-else>{{ '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column min-width="120px" prop="Is_Enable" label="是否启用" align="center">
          <template slot-scope="{ row }">
            <el-tag v-if="row.Is_Enable" type="success">是</el-tag>
            <el-tag v-else type="danger">否</el-tag>
          </template>
        </el-table-column>
        <el-table-column min-width="150px" prop="Is_Nest" label="是否套料工序" align="center">
          <template slot-scope="{ row }">
            <div v-if="row.Bom_Level === 0">
              <el-tag v-if="row.Is_Nest" type="success">是</el-tag>
              <el-tag v-else type="danger">否</el-tag>
            </div>
            <div v-else>
              {{ '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column min-width="150px" prop="Is_Cutting" label="是否下料工序" align="center">
          <template slot-scope="{ row }">
            <div v-if="row.Bom_Level===0">
              <el-tag v-if="row.Is_Cutting" type="success">是</el-tag>
              <el-tag v-else type="danger">否</el-tag>
            </div>
            <div v-else>
              {{ '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column min-width="120px" prop="Device_Name" label="关联设备">
          <template slot-scope="{ row }">
            {{ row.Device_Name || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="Remark" label="备注" width="300">
          <template slot-scope="{ row }">
            {{ row.Remark || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="170" fixed="right">
          <template slot-scope="{ row }">
            <el-button type="text" size="small" @click="handleDialog('edit', row)">编辑</el-button>
            <el-button type="text" size="small" @click="handleOpenDevice(row)">关联设备</el-button>
            <el-button class="txt-red" type="text" size="small" @click="handleDelete(row.Id)">删除</el-button>

            <!--<el-divider direction="vertical" />
             <el-button type="text" size="small" @click="handleManage(row)"
              >班组管理</el-button
            > -->
          </template>
        </el-table-column>
      </el-table>
      <el-dialog
        v-if="dialogVisible"
        v-dialog-drag
        class="cs-dialog"
        :close-on-click-modal="false"
        :title="title"
        :visible.sync="dialogVisible"
        custom-class="dialogCustomClass"
        width="580px"
        top="5vh"
        @close="handleClose"
      >
        <component
          :is="currentComponent"
          ref="content"
          :row-info="rowInfo"
          :type="type"
          :level="level"
          :bom-list="bomList"
          :dialog-visible="dialogVisible"
          @close="handleClose"
          @refresh="fetchData"
        />
      </el-dialog>
      <el-dialog
        v-dialog-drag
        class="cs-dialog"
        title="关联设备"
        :close-on-click-modal="false"
        :visible.sync="dialogVisible1"
        custom-class="dialogCustomClass"
        width="86%"
        top="5vh"
        @close="handleClose1"
      >
        <AssociatedDevice ref="Device" :row-data="rowData" @fetchData="fetchData" />
        <!-- <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible1 = false">取 消</el-button>
          <el-button type="primary" @click="dialogVisible1 = false">确 定</el-button>
        </span> -->
      </el-dialog>
    </div>
  </div>
</template>

<script>
import TopHeader from '@/components/TopHeader'
import Add from './component/Add'
import ZClass from './component/Group'
import AssociatedDevice from './component/AssociatedDevice'
import RecognitionConfig from './component/RecognitionConfig'
import partRecognitionConfig from './component/partRecognitionConfig'
import compRecognitionConfig from './component/compRecognitionConfig'
import unitPartRecognitionConfig from './component/unitPartRecognitionConfig'
import PartTakeConfig from './component/PartTakeConfig'
import { GetProcessListBase, DeleteProcess } from '@/api/PRO/technology-lib'
import ElTableEmpty from '@/components/ElTableEmpty/index.vue'
import addRouterPage from '@/mixins/add-router-page'
import { GetBOMInfo } from '@/views/PRO/bom-setting/utils'

export default {
  name: 'PROProcessManagement',
  components: {
    ElTableEmpty,
    TopHeader,
    Add,
    partRecognitionConfig,
    compRecognitionConfig,
    PartTakeConfig,
    ZClass,
    AssociatedDevice,
    unitPartRecognitionConfig,
    RecognitionConfig
  },
  mixins: [addRouterPage],
  data() {
    return {
      level: 0,
      bomList: [],
      addPageArray: [
        {
          path: '/AssociatedDevice',
          hidden: true,
          component: () => import('@/views/PRO/process-settings/management/component/AssociatedDevice.vue'),
          name: 'AssociatedDevice',
          meta: { title: '关联设备' }
        }

      ],
      tableData: [],
      currentComponent: '',
      title: '',
      comName: '',
      partName: '',
      rowInfo: null,
      rowData: {},
      type: '',
      pageLoading: false,
      dialogVisible: false,
      dialogVisible1: false,
      unitPartList: [],
      formInline: { name: '', code: '' }
    }
  },

  mounted() {
    this.getBOMInfo()
    this.fetchData()
  },
  methods: {
    async getBOMInfo() {
      const { comName, partName, list } = await GetBOMInfo()
      this.comName = comName
      this.partName = partName
      this.bomList = list
      this.unitPartList = list.filter(item => item.Code !== '0' && item.Code !== '-1')
    },
    handleOpenDevice(row) {
      this.rowData = row
      this.dialogVisible1 = true
      this.$nextTick(() => {
        this.$refs.Device.clearSelec()
      })
      //  this.$router.push({ path: 'http://localhost:3000/produce/pro/nesting/index' })
    },

    fetchData() {
      this.pageLoading = true
      GetProcessListBase(this.formInline).then((res) => {
        if (res.IsSucceed) {
          this.tableData = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
        this.pageLoading = false
        this.dialogVisible1 = false
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleClose1() {
      this.dialogVisible1 = false
    },
    handleRecognitionConfig() {
      this.title = `导入识别配置`
      this.currentComponent = 'RecognitionConfig'
      this.dialogVisible = true
    },
    handleUnitPartConfig(item) {
      this.level = +item.Code
      this.title = `${item.Display_Name}识别配置`
      this.currentComponent = 'unitPartRecognitionConfig'
      this.dialogVisible = true
    },
    handleConfig() {
      this.title = `${this.partName}识别配置`
      this.currentComponent = 'partRecognitionConfig'
      this.dialogVisible = true
    },
    handleConfigComp() {
      this.title = `${this.comName}识别配置`
      this.currentComponent = 'compRecognitionConfig'
      this.dialogVisible = true
    },
    handleTakeConfig() {
      this.title = `${this.partName}领用配置`
      this.currentComponent = 'PartTakeConfig'
      this.dialogVisible = true
    },
    handleDialog(type, row) {
      this.currentComponent = 'Add'
      this.type = type
      if (type === 'add') {
        this.title = '新建'
      } else {
        this.title = '编辑'
        this.rowInfo = row
      }
      this.dialogVisible = true
    },
    handleManage(row) {
      this.currentComponent = 'ZClass'
      this.title = '班组管理'
      this.dialogVisible = true
      this.$nextTick((_) => {
        this.$refs.content.init(row)
      })
    },
    handleDelete(processId) {
      this.$confirm('是否删除当前工序?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          DeleteProcess({
            processId
          }).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.fetchData()
            } else {
              this.$message({
                message: res.Message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
.cs-z-page-main-content{
  min-width: 1000px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding:  0;
  margin-bottom: 8px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
  }

  .header-right {
    display: flex;
    align-items: center;
  }
}

.tb {
  ::v-deep {
    @include scrollBar;

    &::-webkit-scrollbar {
      width: 8px;
    }
  }
}

.cs-dialog {
  ::v-deep {
    .el-dialog__body {
      padding: 20px 20px !important;
      overflow: hidden;
    }
  }
}

.cs-tb-icon {
  vertical-align: middle;
}

.cs-tag {
  &:nth-child(2n) {
    margin-left: 4px;
  }

  &:nth-child(n + 3) {
    margin-top: 4px;
  }
}
</style>

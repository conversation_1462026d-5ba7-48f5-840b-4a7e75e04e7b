<template>
  <div class="abs100 cs-z-flex-pd16-wrap">
    <div class="cs-z-page-main-content">
      <top-header padding="0">
        <template #right>
          <div style="display: flex;">
            <el-form label-width="80px" :inline="true">
              <el-form-item label="工序名称">
                <el-input v-model="formInline.name" clearable placeholder="请输入工序名称" @keyup.enter.native="handleSearch" />
              </el-form-item>
              <el-form-item label="代号">
                <el-input v-model="formInline.code" clearable placeholder="请输入代号" @keyup.enter.native="handleSearch" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button @click="reset">重置</el-button>
              </el-form-item>
            </el-form>
            <DynamicTableFields
              title="表格配置"
              :table-config-code="gridCode"
              @updateColumn="changeColumn"
            />
          </div>
        </template>
        <template #left>
          <el-button icon="el-icon-plus" type="primary" size="small" @click="handleDialog('add')">新增</el-button>
          <el-button type="success" size="small" @click="handleRecognitionConfig">导入识别配置</el-button>
          <el-button type="primary" size="small" @click="handleTakeConfig">{{ partName }}领用配置</el-button>
        </template>
      </top-header>
      <div v-loading="tbLoading" class="fff cs-z-tb-wrapper">
        <vxe-table
          ref="xTable"
          :key="tbKey"
          v-loading="tbLoading"
          :empty-render="{name: 'NotData'}"
          show-header-overflow
          element-loading-spinner="el-icon-loading"
          element-loading-text="拼命加载中"
          empty-text="暂无数据"
          height="100%"
          :data="tbData"
          stripe
          resizable
          :auto-resize="true"
          class="cs-vxe-table"
          :tooltip-config="{ enterable: true }"
        >
          <vxe-column
            v-for="item in columns"
            :key="item.Code"
            :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
            show-overflow="tooltip"
            sortable
            :align="item.Align"
            :field="item.Code"
            :title="item.Display_Name"
            :visible="item.Is_Display"
            :width="item.Width"
          >
            <template #default="{ row }">
              <span v-if="item.Code === 'Workload_Proportion'">
                {{ row.Workload_Proportion ? row.Workload_Proportion + '%' : "-" }}
              </span>
              <span v-else-if="item.Code === 'Is_Need_Check'">
                <el-tag v-if="row.Is_Need_Check" type="success">是</el-tag><el-tag v-else type="danger">否</el-tag>
              </span>
              <span v-else-if="item.Code === 'Is_Self_Check'">
                <el-tag v-if="row.Is_Self_Check" type="success">是</el-tag><el-tag v-else type="danger">否</el-tag>
              </span>
              <span v-else-if="item.Code === 'Is_Inter_Check'">
                <el-tag v-if="row.Is_Inter_Check" type="success">是</el-tag><el-tag v-else type="danger">否</el-tag>
              </span>
              <span v-else-if="item.Code === 'Is_Enable'">
                <el-tag v-if="row.Is_Enable" type="success">是</el-tag><el-tag v-else type="danger">否</el-tag>
              </span>
              <span v-else-if="item.Code === 'Is_Nest'">
                <el-tag v-if="row.Is_Nest" type="success">是</el-tag><el-tag v-else-if="row.Is_Nest===false" type="danger">否</el-tag><span v-else>-</span>
              </span>
              <span v-else-if="item.Code === 'Is_Cutting'">
                <el-tag v-if="row.Is_Cutting" type="success">是</el-tag><el-tag v-else-if="row.Is_Cutting===false" type="danger">否</el-tag><span v-else>-</span>
              </span>
              <span v-else-if="item.Code === 'Is_Pick_Material'">
                <el-tag v-if="row.Is_Pick_Material" type="success">是</el-tag><el-tag v-else-if="row.Is_Pick_Material===false" type="danger">否</el-tag><span v-else>-</span>
              </span>
              <span v-else>{{ row[item.Code] || "-" }}</span>
            </template>
          </vxe-column>
          <vxe-column fixed="right" title="操作" width="180" show-overflow align="center">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="handleDialog('edit', row)">编辑</el-button>
              <el-button type="text" size="small" @click="handleOpenDevice(row)">关联设备</el-button>
              <el-button class="txt-red" type="text" size="small" @click="handleDelete(row.Id)">删除</el-button>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <el-dialog
        v-if="dialogVisible"
        v-dialog-drag
        class="cs-dialog"
        :close-on-click-modal="false"
        :title="title"
        :visible.sync="dialogVisible"
        custom-class="dialogCustomClass"
        :width="currentComponent==='PartTakeConfig' ? '800px' : '580px'"
        top="5vh"
        @close="handleClose"
      >
        <component
          :is="currentComponent"
          ref="content"
          :workload-proportion-all="Workload_Proportion_All"
          :row-info="rowInfo"
          :type="type"
          :level="level"
          :bom-list="bomList"
          :dialog-visible="dialogVisible"
          @close="handleClose"
          @refresh="fetchData"
        />
      </el-dialog>
      <el-dialog
        v-dialog-drag
        class="cs-dialog"
        title="关联设备"
        :close-on-click-modal="false"
        :visible.sync="dialogVisible1"
        custom-class="dialogCustomClass"
        width="86%"
        top="5vh"
        @close="handleClose1"
      >
        <AssociatedDevice ref="Device" :row-data="rowData" @fetchData="fetchData" />
        <!-- <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible1 = false">取 消</el-button>
          <el-button type="primary" @click="dialogVisible1 = false">确 定</el-button>
        </span> -->
      </el-dialog>
    </div>
  </div>
</template>

<script>
import TopHeader from '@/components/TopHeader'
import Add from './component/Add'
import ZClass from './component/Group'
import AssociatedDevice from './component/AssociatedDevice'
import RecognitionConfig from './component/RecognitionConfig'
import PartTakeConfig from './component/PartTakeConfig'
import { GetProcessListBase, DeleteProcess } from '@/api/PRO/technology-lib'
import ElTableEmpty from '@/components/ElTableEmpty/index.vue'
import addRouterPage from '@/mixins/add-router-page'
import DynamicTableFields from '@/components/DynamicTableFields/index.vue'
import { GetBOMInfo } from '@/views/PRO/bom-setting/utils'
import getTbInfo from '@/mixins/PRO/get-table-info'

export default {
  name: 'PROProcessManagement',
  components: {
    ElTableEmpty,
    TopHeader,
    Add,
    PartTakeConfig,
    ZClass,
    AssociatedDevice,
    RecognitionConfig,
    DynamicTableFields
  },
  mixins: [addRouterPage, getTbInfo],
  data() {
    return {
      tbLoading: false,
      level: 0,
      bomList: [],
      addPageArray: [
        {
          path: '/AssociatedDevice',
          hidden: true,
          component: () => import('@/views/PRO/process-settings/management/component/AssociatedDevice.vue'),
          name: 'AssociatedDevice',
          meta: { title: '关联设备' }
        }

      ],
      columns: [],
      tbData: [],
      currentComponent: '',
      title: '',
      comName: '',
      partName: '',
      rowInfo: null,
      rowData: {},
      type: '',
      dialogVisible: false,
      dialogVisible1: false,
      formInline: { name: '', code: '' },
      Workload_Proportion_All: 0,
      tbKey: 100,
      gridCode: 'processSettingsList'
    }
  },
  async created() {
    await this.getTableInfo()
  },
  mounted() {
    this.getBOMInfo()
    this.fetchData()
  },
  methods: {
    async getTableInfo() {
      await this.getTableConfig(this.gridCode)
    },
    async getBOMInfo() {
      const { comName, partName, list } = await GetBOMInfo()
      this.comName = comName
      this.partName = partName
      this.bomList = list
    },
    handleOpenDevice(row) {
      this.rowData = row
      this.dialogVisible1 = true
      this.$nextTick(() => {
        this.$refs.Device.clearSelec()
      })
      //  this.$router.push({ path: 'http://localhost:3000/produce/pro/nesting/index' })
    },

    fetchData() {
      this.tbLoading = true
      GetProcessListBase(this.formInline).then((res) => {
        if (res.IsSucceed) {
          this.tbData = res.Data

          // 计算所有 Workload_Proportion 的总和
          this.Workload_Proportion_All = res.Data.reduce((total, item) => {
            const proportion = parseFloat(item.Workload_Proportion) || 0
            return total + proportion
          }, 0)
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
        this.tbLoading = false
        this.dialogVisible1 = false
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleClose1() {
      this.dialogVisible1 = false
    },
    handleRecognitionConfig() {
      this.title = `导入识别配置`
      this.currentComponent = 'RecognitionConfig'
      this.dialogVisible = true
    },
    handleTakeConfig() {
      this.title = `${this.partName}领用配置`
      this.currentComponent = 'PartTakeConfig'
      this.dialogVisible = true
    },
    handleDialog(type, row) {
      this.currentComponent = 'Add'
      this.type = type
      if (type === 'add') {
        this.title = '新建'
      } else {
        this.title = '编辑'
        this.rowInfo = row
      }
      this.dialogVisible = true
    },
    handleManage(row) {
      this.currentComponent = 'ZClass'
      this.title = '班组管理'
      this.dialogVisible = true
      this.$nextTick((_) => {
        this.$refs.content.init(row)
      })
    },
    handleDelete(processId) {
      this.$confirm('是否删除当前工序?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          DeleteProcess({
            processId
          }).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.fetchData()
            } else {
              this.$message({
                message: res.Message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    async changeColumn() {
      await this.getTableConfig(this.gridCode)
      this.tbKey++
    },
    handleSearch() {
      this.fetchData()
    },
    reset() {
      this.formInline.name = ''
      this.formInline.code = ''
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
.cs-z-page-main-content{
  min-width: 1000px;
}

.cs-z-tb-wrapper {
  height: 0;
  flex: 1;
}

::v-deep {
  .cs-top-header-box {
    line-height: 0px;
  }
}

.tb {
  ::v-deep {
    @include scrollBar;

    &::-webkit-scrollbar {
      width: 8px;
    }
  }
}

.cs-dialog {
  ::v-deep {
    .el-dialog__body {
      padding: 20px 20px !important;
      overflow: hidden;
    }
  }
}

.cs-tb-icon {
  vertical-align: middle;
}

.cs-tag {
  &:nth-child(2n) {
    margin-left: 4px;
  }

  &:nth-child(n + 3) {
    margin-top: 4px;
  }
}
</style>

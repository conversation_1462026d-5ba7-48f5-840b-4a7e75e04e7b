{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\actually-sent\\v4\\component\\Add.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\actually-sent\\v4\\component\\Add.vue", "mtime": 1757468128055}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["TitleInfo", "AddDialog", "packDetail", "AddProjectSendingInfo", "GetProjectsendinginEntity", "EditProjectSendingInfo", "GetFirstLevelDepartsUnderFactory", "GetPreferenceSettingValue", "DynamicDataTable", "CarDialog", "GetCurCarPageList", "TopHeader", "CheckInfo", "closeTagView", "parseTime", "GetProjectPageList", "GetProjectEntity", "GetGridByCode", "GetOssUrl", "GetFactoryProfessionalByCode", "GetCompanyDepartTree", "numeral", "OSSUpload", "getFileNameFromUrl", "mapGetters", "GetContractList", "GetStopList", "SelectDepartmentUser", "SelectDepartment", "TAB_TYPE", "com", "package", "unitPart", "part", "components", "props", "isEdit", "type", "Boolean", "default", "data", "pageStatus", "undefined", "radio", "addradio", "Is_Pack", "isClicked", "isSub", "width", "topDialog", "btnLoading", "currentComponent", "title", "loading", "dialogVisible", "sendNumber", "form", "ProjectId", "Out_Date", "Date", "Remarks", "Contact_UserName", "Mobile", "License", "Address", "receiveName", "issueName", "Receiver_Tel", "Area_Id", "projectName", "receiveNum", "ProfessionalTypeName", "Depart_Id", "Logistics_Fee", "Contract_Id", "PageInfo", "Parameter<PERSON>son", "Page", "PageSize", "Loadings", "LoadingsPersonnel", "Trips", "ReceivingUnit", "treeParamsDepart", "filterable", "clickParent", "disabled", "children", "label", "value", "pickDepartmentList", "plm_ProjectSendingInfo", "produced_Components", "weightFileInfo", "projectSendingInfo_Item", "Itemdetail", "PackagesList", "ProfessionalType", "fileListArr", "carOptions", "projects", "Id", "projectId", "planTime", "showDialog", "tbConfig", "Pager_<PERSON>gn", "columns", "tbData", "tbData2", "tbData3", "tbData4", "total", "tbLoading", "autoGenerate", "selectList", "fileListData", "sums", "rules", "required", "message", "trigger", "old_Component_Ids", "weightform", "Tare_Weight", "Reason_Weight", "Pound_Weight", "Net_Weight", "Weigh_Warning_Threshold", "isProductweight", "tabTypeCode", "isDraft", "contractOptions", "provide", "isVersionFour", "computed", "_objectSpread", "netWeight", "showRed", "_ref", "Math", "abs", "watch", "handler", "getTotal", "created", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "$route", "query", "getSettingProductweight", "getFactoryDepartmentData", "getDepartmentTree", "getFactoryTypeOption", "getAllCarList", "stop", "mounted", "_this2", "_callee2", "_JSON$parse", "_JSON$parse2", "Name", "Code", "_autoGenerate", "Sys_Project_Id", "_callee2$", "_context2", "JSON", "parse", "decodeURIComponent", "p", "getInfo", "Project_Code", "$store", "state", "user", "name", "getProjectEntity", "getContractList", "methods", "_this3", "_callee3", "res", "_callee3$", "_context3", "code", "sent", "Data", "_this4", "FactoryId", "localStorage", "getItem", "then", "IsSucceed", "depId", "cur", "find", "item", "$message", "Message", "_this5", "isAll", "tree", "setDisabledTree", "$nextTick", "_", "_this5$$refs$treeSele", "$refs", "treeSelectDepart", "treeDataUpdateFun", "arr", "getFlattenedSelectableItems", "root", "length", "result", "flatten", "items", "for<PERSON>ach", "_item$Data", "_item$Data2", "Children", "Is_Company", "Type", "push", "_this6", "element", "departClear", "departChange", "getNum", "a", "b", "subtract", "format", "_this7", "_callee4", "_callee4$", "_context4", "factoryId", "getTableConfig", "concat", "_this8", "Consignee", "Contacts", "Tel", "getProjectPageList", "_this9", "toBack", "_this0", "$confirm", "confirmButtonText", "cancelButtonText", "catch", "radioChange", "e", "inputBlur", "e1", "row", "console", "log", "Wait_Stock_Count", "S_Count", "Number", "AllWeight", "getAllWeight", "Component_Id", "SteelAmount", "handleSubmit", "_this1", "validate", "valid", "formAttachment", "response", "encryptionUrl", "submitObj", "Attachment", "toString", "Consignee<PERSON><PERSON>", "MakerName", "VehicleNo", "<PERSON><PERSON><PERSON>", "Telephone", "SendDate", "ProjectName", "TypeId", "PartList", "tempData", "_toConsumableArray", "filter", "PackageSn", "Stock_Count", "Warehouse_Id", "Location_Id", "Netweight", "DIM", "Volume", "AllAmount", "Import_Detail_Id", "Model_Ids", "isOld", "tempItem", "SteelWeight", "Part_Code", "Part_Produced_Id", "Area_Name", "InstallUnit_Name", "Spec", "Length", "Weight", "Part_Grade", "Amount", "$router", "replace", "refresh", "getStopList", "list", "key", "_this10", "_callee5", "_callee5$", "_context5", "map", "stopMap", "Is_Stop", "$set", "_this11", "_callee8", "_callee8$", "_context8", "id", "_ref2", "_callee7", "_this11$plm_ProjectSe", "_this11$plm_ProjectSe2", "_Number", "AttachmentArr", "imgPromiseAll2", "_callee7$", "_context7", "Plm_ProjectSendingInfo", "WeightInfo", "Status", "every", "v", "detail", "split", "fileUrl", "indexOf", "substring", "lastIndexOf", "fileName", "decodeURI", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url", "Attachment_Weight", "_ref3", "_callee6", "_callee6$", "_context6", "handleUrl", "t0", "t1", "abrupt", "_x2", "apply", "arguments", "Promise", "all", "index", "WarehouseName", "LocationName", "SerialNumber", "idx", "Total_Weight", "multiply", "PkgNO", "PackageId", "_x", "checkSelectable", "stopFlag", "_callee9", "_yield$GetOssUrl", "_callee9$", "_context9", "carChange", "val", "_this12", "projectIdChange", "projectIdClear", "toFixed", "addSelectList", "_this13", "stringify", "handleDelete", "_this14", "findIndex", "splice", "_table", "multiSelectedChange", "fetchData", "_this15", "resolve", "addCarData", "handleAdd", "_this16", "tempData1", "content", "init", "tempData2", "handleDetail", "_this17", "handleEditCar", "close", "handleInfo", "info", "handleOpen", "_this18", "Object", "assign", "Grid", "ColumnList", "Is_Display", "Is_Resizable", "Is_Sortable", "Row_Number", "Is_Select", "Is_Row_Number", "uploadSuccess", "file", "fileList", "uploadRemove", "handlePreview", "_callee0", "_callee0$", "_context0", "window", "open", "handleExceed", "_this19", "_callee1", "submitData", "_callee1$", "_context1", "ContractTypeCode", "ProjectIds", "error"], "sources": ["src/views/PRO/shipment/actually-sent/v4/component/Add.vue"], "sourcesContent": ["<template>\r\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\r\n    <div class=\"cs-z-page-main-content\">\r\n      <top-header>\r\n        <template #left>\r\n          <div class=\"cs-header\">\r\n            <!-- <el-button\r\n              circle\r\n              icon=\"el-icon-arrow-left\"\r\n              size=\"mini\"\r\n              @click=\"toBack\"\r\n            /> -->\r\n            <el-button @click=\"toBack\">返回</el-button>\r\n            <!-- <strong class=\"title\">{{\r\n              isEdit === true ? \"编辑发货单\" : \"新增发货单\"\r\n            }}</strong> -->\r\n          </div>\r\n        </template>\r\n        <template #right>\r\n          <!-- <el-button type=\"primary\" :loading=\"btnLoading\">打印发货单</el-button> -->\r\n          <el-button type=\"primary\" :loading=\"loading\" @click=\"handleSubmit\">保存</el-button>\r\n        </template>\r\n      </top-header>\r\n      <!-- <title-info :title=\"projectName\" /> -->\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"发货单号\" prop=\"receiveNum\">\r\n              <el-input\r\n                v-model=\"form.receiveNum\"\r\n                :disabled=\"autoGenerate || (!isDraft && isEdit)\"\r\n                :placeholder=\"autoGenerate ? '自动生成':'请输入'\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"项目名称\" prop=\"projectName\">\r\n              <el-input v-model=\"form.projectName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"收货人\" prop=\"receiveName\">\r\n              <el-input\r\n                v-model=\"form.receiveName\"\r\n                clearable=\"\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"收货人电话\" prop=\"Receiver_Tel\">\r\n              <el-input\r\n                v-model=\"form.Receiver_Tel\"\r\n                clearable=\"\"\r\n                placeholder=\"请输入\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"车辆信息\" prop=\"License\">\r\n              <el-select\r\n                v-model=\"form.License\"\r\n                clearable\r\n                :disabled=\"((pageStatus>=2)||pageStatus===1)&&isEdit\"\r\n                placeholder=\"请选择\"\r\n                style=\"width: 70%\"\r\n                filterable\r\n                @change=\"carChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in carOptions\"\r\n                  :key=\"item.License\"\r\n                  :label=\"item.detail\"\r\n                  :value=\"item.License\"\r\n                />\r\n              </el-select>\r\n              <el-button\r\n                style=\"margin-left: 10px\"\r\n                type=\"text\"\r\n                @click=\"handleEditCar\"\r\n              >新增车辆</el-button>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"发货部门\" prop=\"Depart_Id\">\r\n              <el-select\r\n                v-if=\"isProductweight===true\"\r\n                v-model=\"form.Depart_Id\"\r\n                clearable\r\n                placeholder=\"请选择\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in pickDepartmentList\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n              <el-tree-select\r\n                v-else\r\n                ref=\"treeSelectDepart\"\r\n                v-model=\"form.Depart_Id\"\r\n                :select-params=\"{\r\n                  clearable: true,\r\n                }\"\r\n                class=\"cs-tree-x\"\r\n                :tree-params=\"treeParamsDepart\"\r\n                @select-clear=\"departClear\"\r\n                @node-click=\"departChange\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"发货人\" prop=\"issueName\">\r\n              <el-input v-model=\"form.issueName\" clearable=\"\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"发货时间\" prop=\"Out_Date\">\r\n              <el-date-picker\r\n                v-model=\"form.Out_Date\"\r\n                style=\"width: 100%\"\r\n                type=\"datetime\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"装车班\" prop=\"Loadings\">\r\n              <SelectDepartment v-model=\"form.Loadings\" @change=\"form.LoadingsPersonnel = ''\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"装车班人员\" prop=\"LoadingsPersonnel \">\r\n              <SelectDepartmentUser v-model=\"form.LoadingsPersonnel\" :department-id=\"form.Loadings\" :disabled=\"!form.Loadings\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"车次\" prop=\"Trips \">\r\n              <el-input v-model=\"form.Trips\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"专业\" prop=\"ProfessionalTypeName\">\r\n              <el-input v-model=\"form.ProfessionalTypeName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"收货地址\" prop=\"Address\">\r\n              <el-input v-model=\"form.Address\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"收货单位\" prop=\"ReceivingUnit\">\r\n              <el-input v-model=\"form.ReceivingUnit\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"物流费\" prop=\"Logistics_Fee\">\r\n              <el-input-number\r\n                v-model=\"form.Logistics_Fee\"\r\n                :min=\"0\"\r\n                :precision=\"2\"\r\n                style=\"width: 100%\"\r\n                class=\"cs-number-btn-hidden\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"关联合同\" prop=\"Contract_Id\">\r\n              <el-select\r\n                v-model=\"form.Contract_Id\"\r\n                clearable\r\n                filterable\r\n                placeholder=\"请选择\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in contractOptions\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.ContractName\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"备注\">\r\n              <el-input\r\n                v-model=\"form.Remarks\"\r\n                :autosize=\"{ minRows: 2, maxRows: 2 }\"\r\n                :maxlength=\"1000\"\r\n                show-word-limit\r\n                type=\"textarea\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"附件\" class=\"factory-img\">\r\n              <OSSUpload\r\n                class=\"upload-demo\"\r\n                action=\"alioss\"\r\n                :limit=\"10\"\r\n                :multiple=\"true\"\r\n                :on-success=\"\r\n                  (response, file, fileList) => {\r\n                    uploadSuccess(response, file, fileList)\r\n                  }\r\n                \"\r\n                :on-remove=\"uploadRemove\"\r\n                :on-preview=\"handlePreview\"\r\n                :on-exceed=\"handleExceed\"\r\n                :file-list=\"fileListData\"\r\n                :show-file-list=\"true\"\r\n                :disabled=\"false\"\r\n              >\r\n                <el-button\r\n                  type=\"primary\"\r\n                  :disabled=\"false\"\r\n                >上传文件</el-button>\r\n              </OSSUpload>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n\r\n      <div v-if=\"isEdit\">\r\n        <h4> 过磅信息</h4>\r\n        <el-form ref=\"form\" inline :model=\"weightform\" label-width=\"80px\">\r\n          <el-form-item label=\"皮重\">\r\n            {{ weightform.Tare_Weight }}kg\r\n          </el-form-item>\r\n          <el-form-item label=\"理重\">\r\n            {{ weightform.Reason_Weight }}kg\r\n          </el-form-item>\r\n          <el-form-item label=\"磅重\">\r\n            {{ weightform.Pound_Weight }}kg\r\n          </el-form-item>\r\n          <el-form-item label=\"净重\" prop=\"region\">\r\n            <span :class=\"{'cs-red':showRed}\">{{ netWeight }}\r\n              <span v-if=\"showRed\">（{{ getNum(netWeight,weightform.Reason_Weight)>0 ?'高于':'低于' }}理重{{ Math.abs(+getNum(netWeight, weightform.Reason_Weight)) }}kg）</span>\r\n            </span>\r\n          </el-form-item>\r\n          <el-form-item label=\"过磅备注\" prop=\"region\">\r\n            {{ plm_ProjectSendingInfo.Pound_Remark }}\r\n          </el-form-item>\r\n          <el-form-item label=\"附件\">\r\n            <template v-for=\"(item,idx) in weightFileInfo\">\r\n              <el-link\r\n                :key=\"idx\"\r\n                :href=\"item.url\"\r\n                target=\"_blank\"\r\n              >{{ item.name }}</el-link>\r\n              <el-divider v-if=\"idx!==weightFileInfo.length -1\" :key=\"idx\" direction=\"vertical\" />\r\n            </template>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <top-header style=\"margin-bottom: 10px\">\r\n        <template #left>\r\n          <div class=\"cs-header\" style=\"margin-bottom: 20px\">\r\n            <el-radio-group v-model=\"radio\" size=\"small\" @change=\"radioChange\">\r\n              <el-radio-button\r\n                label=\"pro_component_out_detail_list\"\r\n              >构件</el-radio-button>\r\n              <el-radio-button\r\n                label=\"pro_package_out_detail_list\"\r\n              >打包件</el-radio-button>\r\n              <el-radio-button\r\n                label=\"PROShipUnitPart\"\r\n              >部件</el-radio-button>\r\n              <el-radio-button\r\n                label=\"PROShipPart\"\r\n              >零件</el-radio-button>\r\n            </el-radio-group>\r\n          </div>\r\n        </template>\r\n        <template #right>\r\n          <div v-if=\"sendNumber\" class=\"statistics-container\">\r\n            <div class=\"statistics-item\" style=\"margin-right: 0\">\r\n              <span>发货序号：</span>\r\n              <span>{{ sendNumber }}</span>\r\n            </div>\r\n          </div>\r\n          <el-button\r\n            v-if=\"!isSub\"\r\n            :disabled=\"!selectList.length\"\r\n            size=\"mini\"\r\n            type=\"danger\"\r\n            @click=\"handleDelete\"\r\n          >删除</el-button>\r\n          <el-button\r\n            v-if=\"!isSub\"\r\n            size=\"mini\"\r\n            type=\"primary\"\r\n            @click=\"handleAdd\"\r\n          >添加</el-button>\r\n        </template>\r\n      </top-header>\r\n\r\n      <div v-loading=\"tbLoading\" class=\"fff cs-z-tb-wrapper\">\r\n        <dynamic-data-table\r\n          ref=\"dyTable\"\r\n          class=\"cs-plm-dy-table\"\r\n          :columns=\"columns\"\r\n          :config=\"tbConfig\"\r\n          :data=\"tabTypeCode === 2 ? tbData2 : tabTypeCode === 1 ? tbData :tabTypeCode === 3 ? tbData3 :tbData4\"\r\n          :page=\"form.PageInfo.Page\"\r\n          :sum-values=\"sums\"\r\n          :select-width=\"70\"\r\n          :total=\"total\"\r\n          border\r\n          stripe\r\n          @checkSelectable=\"checkSelectable\"\r\n          @multiSelectedChange=\"multiSelectedChange\"\r\n        >\r\n          <template slot=\"Code\" slot-scope=\"{ row }\">\r\n            <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n            <span>{{ row.Code }}</span>\r\n          </template>\r\n          <template slot=\"Part_Code\" slot-scope=\"{ row }\">\r\n            <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n            <span>{{ row.Part_Code }}</span>\r\n          </template>\r\n          <template slot=\"S_Count\" slot-scope=\"{ row }\">\r\n            <div v-if=\"!Is_Pack && !isSub\">\r\n              <el-input\r\n                v-model=\"row.S_Count\"\r\n                type=\"text\"\r\n                :readonly=\"row.Wait_Stock_Count == 1\"\r\n                style=\"width: 50px; border: 1px solid #eee; border-radius: 4px\"\r\n                @blur=\"\r\n                  (e) => {\r\n                    inputBlur(e, row.S_Count, row);\r\n                  }\r\n                \"\r\n              />\r\n            </div>\r\n            <div v-else>{{ row.S_Count }}</div>\r\n          </template>\r\n          <template slot=\"PackageSn\" slot-scope=\"{ row }\">\r\n            <div style=\"color: #298dff; cursor: pointer;\" @click=\"handleDetail(row)\">{{ row.PackageSn }}</div>\r\n          </template>\r\n          <!--          <template slot=\"AllWeight\" slot-scope=\"{ row }\">-->\r\n          <!--            {{ row.S_Count * row.Netweight }}-->\r\n          <!--          </template>-->\r\n          <!--  <template slot=\"Unique_Code\" slot-scope=\"{ row }\">\r\n            {{ row.C_Type === \"打包件\" ? row.Unique_Code : \"-\" }}\r\n          </template>\r\n          <template slot=\"op\" slot-scope=\"{ row, index }\">\r\n            <el-button\r\n              v-if=\"row.C_Type === '打包件'\"\r\n              :index=\"index\"\r\n              type=\"text\"\r\n              @click=\"handleInfo(row)\"\r\n              >查看</el-button\r\n            >\r\n          </template> -->\r\n        </dynamic-data-table>\r\n      </div>\r\n\r\n      <el-dialog\r\n        v-if=\"dialogVisible\"\r\n        v-dialogDrag\r\n        class=\"plm-custom-dialog\"\r\n        :title=\"title\"\r\n        :visible.sync=\"dialogVisible\"\r\n        :width=\"width\"\r\n        :top=\"topDialog\"\r\n        @close=\"close\"\r\n      >\r\n        <component\r\n          :is=\"currentComponent\"\r\n          ref=\"content\"\r\n          :dialog-visible=\"dialogVisible\"\r\n          :project-id=\"projectId\"\r\n          :sys-project-id=\"form.ProjectId\"\r\n          :add-radio=\"addradio\"\r\n          :tab-type-code=\"tabTypeCode\"\r\n          :is-pack=\"Is_Pack\"\r\n          @addCarData=\"addCarData\"\r\n          @close=\"close\"\r\n          @reCount=\"getTotal\"\r\n          @refresh=\"fetchData\"\r\n          @selectList=\"addSelectList\"\r\n        />\r\n      </el-dialog>\r\n      <check-info ref=\"info\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport TitleInfo from './TitleInfo.vue'\r\nimport AddDialog from './AddDialog.vue'\r\nimport packDetail from './packDetail.vue'\r\nimport {\r\n  AddProjectSendingInfo,\r\n  GetProjectsendinginEntity,\r\n  EditProjectSendingInfo\r\n} from '@/api/PRO/component-stock-out'\r\nimport { GetFirstLevelDepartsUnderFactory } from '@/api/PRO/material-warehouse/material-inventory-reconfig.js'\r\nimport { GetPreferenceSettingValue } from '@/api/sys/system-setting'\r\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable.vue'\r\nimport CarDialog from './CarDialog.vue'\r\nimport { GetCurCarPageList } from '@/api/PRO/car'\r\nimport TopHeader from '@/components/TopHeader/index.vue'\r\nimport CheckInfo from '@/views/PRO/Component/GetPackingDetail/index.vue'\r\nimport { closeTagView, parseTime } from '@/utils'\r\nimport { GetProjectPageList, GetProjectEntity } from '@/api/PRO/pro-schedules'\r\nimport { GetGridByCode, GetOssUrl } from '@/api/sys'\r\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\r\nimport { GetCompanyDepartTree } from '@/api/sys'\r\nimport numeral from 'numeral'\r\nimport OSSUpload from '@/views/PRO/components/ossupload.vue'\r\nimport { getFileNameFromUrl } from '@/utils/file'\r\nimport { mapGetters } from 'vuex'\r\nimport { GetContractList } from '@/api/plm/production'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nimport SelectDepartmentUser from '@/components/Select/SelectDepartmentUser/index.vue'\r\nimport SelectDepartment from '@/components/Select/SelectDepartment/index.vue'\r\n\r\nconst TAB_TYPE = {\r\n  com: 1,\r\n  package: 2,\r\n  unitPart: 3,\r\n  part: 4\r\n}\r\nexport default {\r\n  components: {\r\n    SelectDepartment, SelectDepartmentUser,\r\n    OSSUpload,\r\n    TitleInfo,\r\n    AddDialog,\r\n    TopHeader,\r\n    DynamicDataTable,\r\n    CarDialog,\r\n    CheckInfo,\r\n    packDetail\r\n  },\r\n  props: {\r\n    isEdit: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      pageStatus: undefined,\r\n      radio: 'pro_component_out_detail_list',\r\n      addradio: 'pro_waiting_out_list',\r\n      Is_Pack: false,\r\n      isClicked: false,\r\n      isSub: false, // 是否提交过\r\n      width: '40%',\r\n      topDialog: '1vh',\r\n      btnLoading: false,\r\n      currentComponent: '',\r\n      title: '',\r\n      loading: false,\r\n      dialogVisible: false,\r\n      sendNumber: '',\r\n      form: {\r\n        ProjectId: '',\r\n        Out_Date: new Date(),\r\n        Remarks: '',\r\n        Contact_UserName: '',\r\n        Mobile: '',\r\n        License: '',\r\n        Address: '',\r\n        receiveName: '',\r\n        issueName: '',\r\n        Receiver_Tel: '',\r\n        Area_Id: '',\r\n        projectName: '',\r\n        receiveNum: '',\r\n        ProfessionalTypeName: '',\r\n        Depart_Id: '',\r\n        Logistics_Fee: undefined,\r\n        Contract_Id: '',\r\n        PageInfo: {\r\n          ParameterJson: [],\r\n          Page: 1,\r\n          PageSize: 20\r\n        },\r\n        Loadings: '',\r\n        LoadingsPersonnel: '',\r\n        Trips: '',\r\n        ReceivingUnit: ''\r\n      },\r\n      // 发货部门\r\n      treeParamsDepart: {\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          disabled: 'disabled',\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      pickDepartmentList: [], // 品重发货部门\r\n      plm_ProjectSendingInfo: {},\r\n      produced_Components: [],\r\n      weightFileInfo: [],\r\n      projectSendingInfo_Item: [],\r\n      Itemdetail: [],\r\n      PackagesList: [],\r\n      ProfessionalType: [],\r\n      fileListArr: [],\r\n      carOptions: [],\r\n      projects: '',\r\n      Id: '',\r\n      projectId: '',\r\n      planTime: '',\r\n      showDialog: false,\r\n      tbConfig: {\r\n        Pager_Align: 'center'\r\n      },\r\n      columns: [],\r\n      tbData: [],\r\n      tbData2: [],\r\n      tbData3: [],\r\n      tbData4: [],\r\n      total: 0,\r\n      tbLoading: false,\r\n      autoGenerate: true,\r\n      selectList: [],\r\n      fileListData: [],\r\n      sums: [],\r\n      rules: {\r\n        Out_Date: [\r\n          { required: true, message: '请选择发货时间', trigger: 'change' }\r\n        ],\r\n        receiveNum: [{ required: false, message: '请输入发货单号', trigger: 'blur' }],\r\n        // receiveName: [{ required: true, message: '请输入', trigger: 'blur' }],\r\n        // Receiver_Tel: [{ required: true, message: '请输入', trigger: 'blur' }],\r\n        Depart_Id: [{ required: true, message: '请选择', trigger: 'change' }],\r\n        issueName: [{ required: true, message: '请输入', trigger: 'blur' }]\r\n      },\r\n      old_Component_Ids: [],\r\n      weightform: {\r\n        Tare_Weight: 0, // 皮重\r\n        Reason_Weight: 0, // 理重\r\n        Pound_Weight: 0, // 磅重\r\n        Net_Weight: 0, // 净重\r\n        Weigh_Warning_Threshold: 0\r\n      },\r\n      isProductweight: null,\r\n      tabTypeCode: 1,\r\n      isDraft: false,\r\n      contractOptions: []\r\n    }\r\n  },\r\n  provide() {\r\n    return {\r\n      isVersionFour: this.isVersionFour\r\n    }\r\n  },\r\n  computed: {\r\n    netWeight() {\r\n      if (!this.weightform.Pound_Weight || !this.weightform.Tare_Weight) return 0\r\n      return this.weightform.Pound_Weight - this.weightform.Tare_Weight\r\n    },\r\n    showRed({ netWeight }) {\r\n      return Math.abs(netWeight - this.weightform.Reason_Weight) >= this.weightform.Weigh_Warning_Threshold\r\n    },\r\n    ...mapGetters('tenant', ['isVersionFour'])\r\n  },\r\n  watch: {\r\n    'tbData.length': {\r\n      handler() {\r\n        this.getTotal()\r\n      }\r\n    }\r\n\r\n  },\r\n  async created() {\r\n    this.tabTypeCode = TAB_TYPE.com\r\n    this.isSub = this.$route.query.isSub === '1' || false\r\n    await this.getSettingProductweight()\r\n    this.getFactoryDepartmentData()\r\n    this.getDepartmentTree()\r\n    this.getFactoryTypeOption()\r\n    this.getAllCarList()\r\n  },\r\n  async mounted() {\r\n    if (this.isEdit) {\r\n      const {\r\n        autoGenerate\r\n      } = JSON.parse(decodeURIComponent(this.$route.query.p))\r\n      this.autoGenerate = autoGenerate\r\n      await this.getInfo()\r\n    } else {\r\n      const {\r\n        Name,\r\n        Id,\r\n        Code,\r\n        Address,\r\n        autoGenerate,\r\n        Sys_Project_Id\r\n      } = JSON.parse(decodeURIComponent(this.$route.query.p))\r\n      // this.projectName = Name;\r\n      this.autoGenerate = autoGenerate\r\n      this.projectId = Id\r\n      this.Project_Code = Code\r\n      this.form.projectName = Name\r\n      this.form.Address = Address\r\n      this.form.ProjectId = Sys_Project_Id\r\n      this.form.issueName = this.$store.state.user.name\r\n      this.getProjectEntity(this.projectId)\r\n      this.rules.receiveNum[0].required = !autoGenerate\r\n    }\r\n    this.getContractList()\r\n  },\r\n  methods: {\r\n    async getSettingProductweight() {\r\n      const res = await GetPreferenceSettingValue({ code: 'Productweight' })\r\n      if (res.Data === 'true') {\r\n        this.isProductweight = true\r\n      }\r\n    },\r\n\r\n    // 获取工厂数据\r\n    getFactoryDepartmentData() {\r\n      GetFirstLevelDepartsUnderFactory({ FactoryId: localStorage.getItem('CurReferenceId') }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.pickDepartmentList = res.Data\r\n          const depId = localStorage.getItem('DepartmentId')\r\n          const cur = this.pickDepartmentList.find((item) => item.Id === depId)\r\n          if (cur) {\r\n            this.form.Depart_Id = cur.Id\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    getDepartmentTree() {\r\n      GetCompanyDepartTree({ isAll: true }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const tree = res.Data\r\n          this.setDisabledTree(tree)\r\n          this.treeParamsDepart.data = tree\r\n          this.$nextTick(_ => {\r\n            this.$refs.treeSelectDepart?.treeDataUpdateFun(tree)\r\n            const arr = this.getFlattenedSelectableItems(tree)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getFlattenedSelectableItems(root) {\r\n      if (!root || !root.length) return []\r\n      const result = []\r\n      const flatten = (items) => {\r\n        if (!items || !items.length) return\r\n        items.forEach((item) => {\r\n          const { Children } = item\r\n          if (item.Data?.Is_Company !== true && item.Data?.Type !== '1') {\r\n            result.push(item)\r\n          }\r\n          if (Children && Children.length > 0) {\r\n            flatten(Children)\r\n          }\r\n        })\r\n      }\r\n      flatten(root)\r\n      const cur = result.find((item) => item.Id === localStorage.getItem('DepartmentId'))\r\n      if (cur && !this.isEdit) {\r\n        this.form.Depart_Id = cur.Id\r\n      }\r\n    },\r\n    setDisabledTree(root) {\r\n      if (!root) return\r\n      root.forEach((element) => {\r\n        const { Children } = element\r\n        if (element.Data.Is_Company === true || element.Data.Type === '1') {\r\n          element.disabled = true\r\n        } else {\r\n          element.disabled = false\r\n        }\r\n        if (Children.length > 0) {\r\n          this.setDisabledTree(Children)\r\n        }\r\n        // if (Children && Children.length) {\r\n        //   element.disabled = true\r\n        // } else {\r\n        //   element.disabled = false\r\n        //   this.setDisabledTree(Children)\r\n        // }\r\n      })\r\n    },\r\n    departClear() {\r\n\r\n    },\r\n    departChange() {\r\n\r\n    },\r\n    getNum(a, b) {\r\n      return numeral(a).subtract(b).format('0.[000]')\r\n    },\r\n    async getFactoryTypeOption() {\r\n      await GetFactoryProfessionalByCode({\r\n        factoryId: localStorage.getItem('CurReferenceId')\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.ProfessionalType = res.Data\r\n          this.form.ProfessionalTypeName = this.ProfessionalType[0].Name\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      await this.getTableConfig(\r\n        `pro_component_out_detail_list,${this.ProfessionalType[0].Code}`\r\n      )\r\n    },\r\n    getProjectEntity(Id) {\r\n      GetProjectEntity({ Id }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const Consignee = res.Data.Contacts.find((item) => {\r\n            return item.Type == 'Consignee'\r\n          })\r\n          this.form.receiveName = Consignee?.Name || ''\r\n          this.form.Receiver_Tel = Consignee?.Tel || ''\r\n          this.form.Trips = res.Data.Trips || ''\r\n        }\r\n      })\r\n    },\r\n    getProjectPageList() {\r\n      GetProjectPageList({ PageSize: -1 }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.projects = res.Data.Data\r\n        }\r\n      })\r\n    },\r\n    toBack() {\r\n      this.$confirm('此操作不会保存编辑数据，是否退出？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          closeTagView(this.$store, this.$route)\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n    },\r\n    radioChange(e) {\r\n      if (e === 'pro_component_out_detail_list') {\r\n        this.addradio = 'pro_waiting_out_list'\r\n        this.Is_Pack = false\r\n        this.tabTypeCode = TAB_TYPE.com\r\n        this.getTableConfig(`${e},${this.ProfessionalType[0].Code}`)\r\n      } else if (e === 'pro_package_out_detail_list') {\r\n        this.addradio = 'pro_waiting_out_list_package'\r\n        this.Is_Pack = true\r\n        this.tabTypeCode = TAB_TYPE.package\r\n        this.getTableConfig(`${e},${this.ProfessionalType[0].Code}`)\r\n      } else if (e === 'PROShipUnitPart') {\r\n        this.addradio = 'PROShipAddUnitPart'\r\n        this.tabTypeCode = TAB_TYPE.unitPart\r\n        this.getTableConfig(`${e}`)\r\n        this.Is_Pack = false\r\n      } else if (e === 'PROShipPart') {\r\n        this.addradio = 'PROShipAddPart'\r\n        this.Is_Pack = false\r\n        this.tabTypeCode = TAB_TYPE.part\r\n        this.getTableConfig(`${e}`)\r\n      }\r\n    },\r\n    inputBlur(e, e1, row) {\r\n      console.log('blur', e1, row, row.Wait_Stock_Count)\r\n      if (e1 < 1 || e1 > row.Wait_Stock_Count) {\r\n        row.S_Count = row.Wait_Stock_Count\r\n      } else {\r\n        row.S_Count = Number(e1)\r\n      }\r\n      row.AllWeight = this.getAllWeight(row)\r\n      this.Itemdetail.find((item) => {\r\n        if (item.Component_Id == row.Id) {\r\n          item.SteelAmount = row.S_Count\r\n        }\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (!valid) {\r\n          return\r\n        }\r\n        this.isClicked = true\r\n\r\n        const formAttachment = []\r\n        if (this.fileListArr.length > 0) {\r\n          this.fileListArr.forEach(item => {\r\n            formAttachment.push(\r\n              item.response && item.response.encryptionUrl\r\n                ? item.response.encryptionUrl\r\n                : item.encryptionUrl\r\n            )\r\n          })\r\n        }\r\n\r\n        const submitObj = {\r\n          plm_ProjectSendingInfo: {\r\n            // Id:this.Id,\r\n            Code: this.form.receiveNum,\r\n            Attachment: formAttachment.toString(),\r\n            ProjectId: this.projectId,\r\n            Consignee: this.form.receiveName,\r\n            ConsigneeTel: this.form.Receiver_Tel,\r\n            Depart_Id: this.form.Depart_Id,\r\n            MakerName: this.form.issueName,\r\n            VehicleNo: this.form.License,\r\n            DriverName: this.form.Contact_UserName,\r\n            Telephone: this.form.Mobile,\r\n            SendDate: parseTime(this.form.Out_Date, '{y}-{m}-{d} {h}:{i}:{s}'),\r\n            Remarks: this.form.Remarks,\r\n            ProjectName: this.form.projectName,\r\n            TypeId: this.ProfessionalType[0].Code,\r\n            Address: this.form.Address,\r\n            Logistics_Fee: this.form.Logistics_Fee,\r\n            Contract_Id: this.form.Contract_Id,\r\n            Loadings: this.form.Loadings,\r\n            LoadingsPersonnel: this.form.LoadingsPersonnel,\r\n            Trips: this.form.Trips,\r\n            ReceivingUnit: this.form.ReceivingUnit,\r\n          },\r\n          projectSendingInfo_Item: [],\r\n          PartList: []\r\n        }\r\n        // 只获取新的表格数据\r\n        const tempData = [...this.tbData, ...this.tbData2, ...this.tbData3, ...this.tbData4]\r\n        tempData.filter((item) => {\r\n          if (item.PackageSn) {\r\n            const {\r\n              Id,\r\n              Stock_Count,\r\n              PackageSn,\r\n              Warehouse_Id,\r\n              Location_Id,\r\n              Netweight,\r\n              AllWeight,\r\n              DIM,\r\n              Volume,\r\n              AllAmount,\r\n              Import_Detail_Id,\r\n              Model_Ids,\r\n              isOld\r\n            } = item\r\n            const tempItem = {\r\n              PackageSn: PackageSn || '',\r\n              SteelAmount: Stock_Count || 1,\r\n              Warehouse_Id: Warehouse_Id || '',\r\n              Location_Id: Location_Id || '',\r\n              SteelWeight: Netweight || '',\r\n              Import_Detail_Id,\r\n              Model_Ids,\r\n              DIM,\r\n              Volume,\r\n              AllWeight,\r\n              AllAmount,\r\n              isOld\r\n            }\r\n            if (!isOld) {\r\n              submitObj.projectSendingInfo_Item.push(tempItem)\r\n            }\r\n          } else if (item.Code) {\r\n            const {\r\n              Id,\r\n              S_Count,\r\n              Stock_Count,\r\n              Warehouse_Id,\r\n              Import_Detail_Id,\r\n              Model_Ids,\r\n              Location_Id,\r\n              Netweight,\r\n              AllWeight,\r\n              isOld\r\n            } = item\r\n            const tempItem = {\r\n              Component_Id: Id || '',\r\n              SteelAmount: S_Count || '',\r\n              Warehouse_Id: Warehouse_Id || '',\r\n              Location_Id: Location_Id || '',\r\n              SteelWeight: Netweight || '',\r\n              Import_Detail_Id,\r\n              Model_Ids,\r\n              isOld,\r\n              AllWeight\r\n            }\r\n            if (!isOld) {\r\n              delete tempItem.isOld\r\n              submitObj.projectSendingInfo_Item.push(tempItem)\r\n            }\r\n          } else if (item.Part_Code) {\r\n            const {\r\n              Part_Produced_Id,\r\n              Part_Code,\r\n              Area_Name,\r\n              InstallUnit_Name,\r\n              S_Count,\r\n              Spec,\r\n              Length,\r\n              Weight,\r\n              Part_Grade\r\n            } = item\r\n            const tempItem = {\r\n              Part_Produced_Id: Part_Produced_Id,\r\n              Part_Code,\r\n              Area_Name,\r\n              InstallUnit_Name,\r\n              Amount: S_Count,\r\n              Spec,\r\n              Length,\r\n              Weight,\r\n              Part_Grade\r\n            }\r\n            submitObj.PartList.push(tempItem)\r\n          }\r\n        })\r\n\r\n        this.btnLoading = true\r\n        if (this.isEdit) {\r\n          // 获取更新后的表单数据\r\n          // submitObj.entity.Id = this.$route.query.id;\r\n          this.plm_ProjectSendingInfo.Code = this.form.receiveNum\r\n          this.plm_ProjectSendingInfo.Consignee = this.form.receiveName\r\n          this.plm_ProjectSendingInfo.ConsigneeTel = this.form.Receiver_Tel\r\n          this.plm_ProjectSendingInfo.VehicleNo = this.form.License\r\n          this.plm_ProjectSendingInfo.DriverName = this.form.Contact_UserName\r\n          this.plm_ProjectSendingInfo.Telephone = this.form.Mobile\r\n          this.plm_ProjectSendingInfo.Depart_Id = this.form.Depart_Id\r\n          this.plm_ProjectSendingInfo.MakerName = this.form.issueName\r\n          this.plm_ProjectSendingInfo.Address = this.form.Address\r\n          this.plm_ProjectSendingInfo.Contract_Id = this.form.Contract_Id\r\n          this.plm_ProjectSendingInfo.Logistics_Fee = this.form.Logistics_Fee\r\n          this.plm_ProjectSendingInfo.Loadings = this.form.Loadings\r\n          this.plm_ProjectSendingInfo.LoadingsPersonnel = this.form.LoadingsPersonnel\r\n          this.plm_ProjectSendingInfo.Trips = this.form.Trips\r\n          this.plm_ProjectSendingInfo.ReceivingUnit = this.form.ReceivingUnit\r\n          this.plm_ProjectSendingInfo.SendDate = parseTime(\r\n            this.form.Out_Date,\r\n            '{y}-{m}-{d} {h}:{i}:{s}'\r\n          )\r\n          this.plm_ProjectSendingInfo.Remarks = this.form.Remarks\r\n\r\n          const formAttachment = []\r\n          if (this.fileListArr.length > 0) {\r\n            this.fileListArr.forEach(item => {\r\n              formAttachment.push(\r\n                item.response && item.response.encryptionUrl\r\n                  ? item.response.encryptionUrl\r\n                  : item.encryptionUrl\r\n              )\r\n            })\r\n          }\r\n          this.plm_ProjectSendingInfo.Attachment = formAttachment.toString()\r\n          submitObj.plm_ProjectSendingInfo = this.plm_ProjectSendingInfo\r\n          // 获取新的表格数据\r\n          // submitObj.projectSendingInfo_Item =\r\n          //   submitObj.projectSendingInfo_Item.filter((item) => {\r\n          //     return !item.isOld;\r\n          //   });\r\n          // 添加新老表格数据\r\n          submitObj.projectSendingInfo_Item = [\r\n            ...this.Itemdetail,\r\n            ...this.PackagesList,\r\n            ...submitObj.projectSendingInfo_Item\r\n          ]\r\n          if (submitObj.projectSendingInfo_Item.length === 0 && submitObj.PartList.length === 0) {\r\n            this.$message({\r\n              message: '不能保存空发货单',\r\n              type: 'error'\r\n            })\r\n          } else {\r\n            this.loading = true\r\n            EditProjectSendingInfo(submitObj).then((res) => {\r\n              if (res.IsSucceed) {\r\n                this.$message({\r\n                  message: '编辑成功',\r\n                  type: 'success'\r\n                })\r\n                closeTagView(this.$store, this.$route)\r\n                this.$router.replace({\r\n                  name: 'PROShipSent',\r\n                  query: {\r\n                    refresh: 1\r\n                  }\r\n                })\r\n              } else {\r\n                this.isClicked = false\r\n                this.$message({\r\n                  message: res.Message,\r\n                  type: 'error'\r\n                })\r\n              }\r\n              this.loading = false\r\n              this.btnLoading = false\r\n            })\r\n          }\r\n        } else {\r\n          if (submitObj.projectSendingInfo_Item.length === 0 && submitObj.PartList.length === 0) {\r\n            this.$message({\r\n              message: '不能保存空发货单',\r\n              type: 'error'\r\n            })\r\n          } else {\r\n            console.log('submitObj', submitObj)\r\n            this.loading = true\r\n            AddProjectSendingInfo(submitObj).then((res) => {\r\n              if (res.IsSucceed) {\r\n                this.$message({\r\n                  message: '添加成功',\r\n                  type: 'success'\r\n                })\r\n                closeTagView(this.$store, this.$route)\r\n                this.$router.replace({\r\n                  name: 'PROShipSent',\r\n                  query: {\r\n                    refresh: 1\r\n                  }\r\n                })\r\n              } else {\r\n                this.isClicked = false\r\n                this.$message({\r\n                  message: res.Message,\r\n                  type: 'error'\r\n                })\r\n              }\r\n              this.btnLoading = false\r\n              this.loading = false\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    async getStopList(list, key) {\r\n      const submitObj = list.map(item => {\r\n        return {\r\n          Id: item[key],\r\n          Type: this.tabTypeCode === TAB_TYPE.com ? 2 : this.tabTypeCode === TAB_TYPE.unitPart ? 3 : 1 // 1：零件，3：部件，2：构件\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = !!item.Is_Stop\r\n          })\r\n          list.forEach(row => {\r\n            if (stopMap[row[key]]) {\r\n              this.$set(row, 'stopFlag', stopMap[row[key]])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async getInfo() {\r\n      await GetProjectsendinginEntity({\r\n        Id: this.$route.query.id\r\n      }).then(async(res) => {\r\n        if (res.IsSucceed) {\r\n          this.plm_ProjectSendingInfo = res.Data.Plm_ProjectSendingInfo\r\n          this.Itemdetail = res.Data.Itemdetail\r\n          this.PackagesList = res.Data.PackagesList\r\n          this.PartList = res.Data.PartList\r\n          this.weightform = res.Data.WeightInfo\r\n          this.pageStatus = this.plm_ProjectSendingInfo.Status\r\n          this.isDraft = this.plm_ProjectSendingInfo?.Status === 0\r\n\r\n          const {\r\n            Id,\r\n            Code,\r\n            ProjectId,\r\n            ProjectName,\r\n            Consignee,\r\n            ConsigneeTel,\r\n            Depart_Id,\r\n            MakerName,\r\n            VehicleNo,\r\n            DriverName,\r\n            Telephone,\r\n            Address,\r\n            Attachment,\r\n            SendDate,\r\n            Remarks,\r\n            Number,\r\n            Logistics_Fee,\r\n            Contract_Id,\r\n            Loadings,\r\n            LoadingsPersonnel,\r\n            Trips,\r\n            ReceivingUnit,\r\n\r\n          } = this.plm_ProjectSendingInfo\r\n\r\n          this.form.ProjectId = ProjectId\r\n          this.form.receiveNum = Code\r\n          this.form.projectName = ProjectName\r\n          this.form.receiveName = Consignee\r\n          this.form.Receiver_Tel = ConsigneeTel\r\n          this.form.Depart_Id = Depart_Id\r\n          this.form.issueName = MakerName\r\n          this.form.License = VehicleNo\r\n          this.form.Contact_UserName = DriverName\r\n          this.form.Mobile = Telephone\r\n          this.form.Address = Address\r\n          this.form.Out_Date = new Date(SendDate)\r\n          this.form.Remarks = Remarks\r\n          this.form.Logistics_Fee = Logistics_Fee || undefined\r\n          this.form.Contract_Id = Contract_Id\r\n          this.form.Loadings = Loadings\r\n          this.form.LoadingsPersonnel = LoadingsPersonnel\r\n          this.form.Trips = Trips\r\n          this.form.ReceivingUnit = ReceivingUnit\r\n          this.sendNumber = Number\r\n\r\n          if (VehicleNo && this.carOptions.every((v) => v.License !== VehicleNo)) {\r\n            this.carOptions.push({\r\n              License: VehicleNo,\r\n              Contact_UserName: DriverName,\r\n              Mobile: Telephone,\r\n              detail: VehicleNo ? `${VehicleNo}(${DriverName} ${Telephone})` : ''\r\n            })\r\n          }\r\n\r\n          if (Attachment) {\r\n            const AttachmentArr = Attachment.split(',')\r\n            AttachmentArr.forEach(item => {\r\n              const fileUrl =\r\n                item.indexOf('?Expires=') > -1\r\n                  ? item.substring(0, item.lastIndexOf('?Expires='))\r\n                  : item\r\n              const fileName = decodeURI(fileUrl.substring(fileUrl.lastIndexOf('/') + 1))\r\n              const AttachmentJson = {}\r\n              AttachmentJson.name = decodeURIComponent(fileName)\r\n              AttachmentJson.url = fileUrl\r\n              AttachmentJson.encryptionUrl = fileUrl\r\n              this.fileListData.push(AttachmentJson)\r\n              this.fileListArr.push(AttachmentJson)\r\n            })\r\n          }\r\n\r\n          if (this.weightform.Attachment_Weight) {\r\n            const imgPromiseAll2 = this.weightform.Attachment_Weight.split(',').map(async url => {\r\n              const fileUrl = url.split('?')[0]\r\n              return {\r\n                url: await this.handleUrl(fileUrl),\r\n                name: getFileNameFromUrl(fileUrl)\r\n              }\r\n            })\r\n            this.weightFileInfo = await Promise.all(imgPromiseAll2)\r\n          }\r\n\r\n          this.Itemdetail.forEach((item, index) => {\r\n            const {\r\n              Component_Id,\r\n              S_Count,\r\n              SteelWeight,\r\n              AllWeight,\r\n              Name,\r\n              Spec,\r\n              Length,\r\n              WarehouseName,\r\n              Code,\r\n              LocationName,\r\n              Area_Name,\r\n              Wait_Stock_Count,\r\n              Import_Detail_Id,\r\n              Location_Id,\r\n              SerialNumber\r\n            } = item\r\n            const tempItem = {\r\n              Id: Component_Id,\r\n              Area_Name: Area_Name,\r\n              Name,\r\n              Spec,\r\n              Length,\r\n              WarehouseName,\r\n              Code,\r\n              LocationName,\r\n              Import_Detail_Id,\r\n              Location_Id,\r\n              S_Count: S_Count,\r\n              Wait_Stock_Count: Wait_Stock_Count,\r\n              Netweight: SteelWeight,\r\n              AllWeight: AllWeight,\r\n              isOld: true,\r\n              SerialNumber: SerialNumber\r\n            }\r\n            this.tbData.push(tempItem)\r\n            this.old_Component_Ids.push(Component_Id)\r\n            this.getStopList(this.tbData, 'Component_Id')\r\n          })\r\n          this.PartList.forEach((element, idx) => {\r\n            const tempItem = { ...element }\r\n            tempItem.S_Count = tempItem.Amount\r\n            tempItem.Netweight = tempItem.Weight\r\n            tempItem.AllWeight = this.getAllWeight(tempItem)\r\n            tempItem.Total_Weight = numeral(tempItem.Stock_Count).multiply(tempItem.Weight).value()\r\n            if (tempItem.Part_Grade > 0) {\r\n              this.tbData3.push(tempItem)\r\n              this.getStopList(this.tbData3, 'Part_Aggregate_Id')\r\n              console.log('this.tbData3', this.tbData3)\r\n            } else {\r\n              this.tbData4.push(tempItem)\r\n              this.getStopList(this.tbData4, 'Part_Aggregate_Id')\r\n              console.log('this.tbData4', this.tbData4)\r\n            }\r\n          })\r\n\r\n          this.PackagesList.forEach((item, index) => {\r\n            const {\r\n              PkgNO,\r\n              PackageSn,\r\n              AllWeight,\r\n              Volume,\r\n              AllAmount,\r\n              WarehouseName,\r\n              LocationName,\r\n              DIM,\r\n              PackageId\r\n            } = item\r\n            const tempItem = {\r\n              PkgNO: PkgNO,\r\n              PackageSn: PackageSn,\r\n              AllWeight,\r\n              AllAmount,\r\n              Volume,\r\n              WarehouseName,\r\n              LocationName,\r\n              isOld: true,\r\n              DIM: DIM,\r\n              PackageId\r\n            }\r\n            this.tbData2.push(tempItem)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    checkSelectable(row) {\r\n      return !row.stopFlag\r\n    },\r\n    async handleUrl(url) {\r\n      const { Data } = await GetOssUrl({ url })\r\n      return Data\r\n    },\r\n    carChange(val) {\r\n      if (!val) {\r\n        this.form.Contact_UserName = ''\r\n        this.form.Mobile = ''\r\n        this.form.License = ''\r\n        return\r\n      }\r\n      const item = this.carOptions.find((v) => v.License === this.form.License)\r\n      this.form.Contact_UserName = item.Contact_UserName\r\n      this.form.Mobile = item.Mobile\r\n      this.form.License = item.License\r\n    },\r\n    projectIdChange(e) {\r\n      // if (e) {\r\n      //   this.getAreaList();\r\n      // }\r\n    },\r\n    projectIdClear(e) {\r\n      // this.$refs.form2.resetFields();\r\n    },\r\n    getAllWeight(item) {\r\n      return Number(item.S_Count * item.Netweight).toFixed(2) / 1\r\n    },\r\n    addSelectList(list) {\r\n      console.log(list, 'list')\r\n      console.log(this.tabTypeCode)\r\n      if (this.tabTypeCode === TAB_TYPE.com) {\r\n        console.log(11)\r\n        list.forEach((item) => {\r\n          item.AllWeight = this.getAllWeight(item)\r\n\r\n          this.tbData.push(item)\r\n        })\r\n        this.tbData = JSON.parse(JSON.stringify(this.tbData))\r\n        this.total = this.tbData.length\r\n      } else if (this.tabTypeCode === TAB_TYPE.unitPart) {\r\n        list.forEach((item) => {\r\n          item.AllWeight = this.getAllWeight(item)\r\n          this.tbData3.push(item)\r\n        })\r\n        // dont ask why just cv\r\n        this.tbData3 = JSON.parse(JSON.stringify(this.tbData3))\r\n        this.total = this.tbData3.length\r\n      } else if (this.tabTypeCode === TAB_TYPE.part) {\r\n        list.forEach((item) => {\r\n          item.AllWeight = this.getAllWeight(item)\r\n          this.tbData4.push(item)\r\n        })\r\n        this.tbData4 = JSON.parse(JSON.stringify(this.tbData4))\r\n        this.total = this.tbData4.length\r\n      } else if (this.tabTypeCode === TAB_TYPE.package) {\r\n        list.forEach((item) => {\r\n          this.tbData2.push(item)\r\n        })\r\n        this.total = this.tbData2.length\r\n      }\r\n    },\r\n    handleDelete() {\r\n      this.$confirm('删除该数据, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          if (this.Is_Pack) {\r\n            this.selectList.forEach((item) => {\r\n              const index = this.tbData2.findIndex(\r\n                (v) => v.PackageSn === item.PackageSn\r\n              )\r\n              index !== -1 && this.tbData2.splice(index, 1)\r\n              if (this.isEdit) {\r\n                const index = this.PackagesList.findIndex(\r\n                  (v) => v.PackageSn === item.PackageSn\r\n                )\r\n                index !== -1 && this.PackagesList.splice(index, 1)\r\n              }\r\n            })\r\n          } else {\r\n            let _table = null\r\n            if (this.tabTypeCode === TAB_TYPE.com) {\r\n              _table = this.tbData\r\n              this.selectList.forEach((item) => {\r\n                const index = _table.findIndex((v) => v.Import_Detail_Id + v.Location_Id === item.Import_Detail_Id + item.Location_Id)\r\n                index !== -1 && _table.splice(index, 1)\r\n                if (this.isEdit) {\r\n                  const index = this.Itemdetail.findIndex(\r\n                    (v) => v.Component_Id === item.Id\r\n                  )\r\n                  index !== -1 && this.Itemdetail.splice(index, 1)\r\n                }\r\n              })\r\n            } else if (this.tabTypeCode === TAB_TYPE.unitPart) {\r\n              _table = this.tbData3\r\n              this.selectList.forEach((item) => {\r\n                const index = _table.findIndex((v) => v.Part_Produced_Id === item.Part_Produced_Id)\r\n                index !== -1 && _table.splice(index, 1)\r\n              })\r\n            } else if (this.tabTypeCode === TAB_TYPE.part) {\r\n              _table = this.tbData4\r\n              this.selectList.forEach((item) => {\r\n                const index = _table.findIndex((v) => v.Part_Produced_Id === item.Part_Produced_Id)\r\n                index !== -1 && _table.splice(index, 1)\r\n              })\r\n            }\r\n          }\r\n          this.$message({\r\n            type: 'success',\r\n            message: '删除成功!'\r\n          })\r\n        })\r\n        .catch(() => {})\r\n    },\r\n    multiSelectedChange(v) {\r\n      this.selectList = v\r\n    },\r\n    fetchData() {\r\n      // GetStockOutDetailList({ stockOutId: this.$route.query.id }).then(res => {\r\n      //   if (res.IsSucceed) {\r\n      //     this.tbData = res.Data\r\n      //     this.tbData.forEach((element, idx) => {\r\n      //       this.$set(element, 'UniqueCodesArray', element.UniqueCodes.split(','))\r\n      //     })\r\n      //   } else {\r\n      //     this.$message({\r\n      //       message: res.Message,\r\n      //       type: 'error'\r\n      //     })\r\n      //   }\r\n      //   this.tbLoading = false\r\n      // })\r\n    },\r\n    getTotal() {\r\n      // this.$nextTick(_ => {\r\n      //   const columns = this.$refs.dyTable.$refs.dtable.columns\r\n      //   columns.forEach((element, idx) => {\r\n      //     if (idx === 0) {\r\n      //       this.sums[0] = '合计'\r\n      //     } else if (element.property === 'Out_Count') {\r\n      //       const v = this.tbData.reduce((acc, cur) => {\r\n      //         return this.highPrecisionAdd(acc, cur[element.property])\r\n      //       }, 0)\r\n      //       this.$set(this.sums, idx, v)\r\n      //     } else if (element.property === 'NetWeight') {\r\n      //       const v = this.tbData.reduce((acc, cur) => {\r\n      //         return this.highPrecisionAdd(acc, cur[element.property] * cur['Out_Count'])\r\n      //       }, 0)\r\n      //       this.$set(this.sums, idx, numeral(v).divide(1000).format('0.[00]') + '（t）')\r\n      //     } else {\r\n      //       this.$set(this.sums, idx, '')\r\n      //     }\r\n      //   })\r\n      // })\r\n    },\r\n    getAllCarList() {\r\n      return new Promise((resolve) => {\r\n        GetCurCarPageList({}).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.carOptions = res.Data\r\n            this.carOptions.forEach((element, idx) => {\r\n              this.$set(\r\n                element,\r\n                'detail',\r\n                `${element.License}(${element.Contact_UserName} ${element.Mobile})`\r\n              )\r\n            })\r\n            resolve()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    addCarData(data) {\r\n      this.getAllCarList()\r\n      // this.carOptions.push(data);\r\n    },\r\n    handleAdd() {\r\n      this.currentComponent = 'AddDialog'\r\n      this.width = '80%'\r\n      this.topDialog = '1vh'\r\n      this.dialogVisible = true\r\n      if (this.Is_Pack === false) {\r\n        let _table = null\r\n        if (this.tabTypeCode === TAB_TYPE.com) {\r\n          _table = this.tbData\r\n          this.title = '添加构件'\r\n        } else if (this.tabTypeCode === TAB_TYPE.unitPart) {\r\n          _table = this.tbData3\r\n          this.title = '添加部件'\r\n        } else if (this.tabTypeCode === TAB_TYPE.part) {\r\n          _table = this.tbData4\r\n          this.title = '添加零件'\r\n        }\r\n\r\n        const tempData1 = _table.filter((item) => {\r\n          return !item.isOld\r\n        })\r\n        this.$nextTick((_) => {\r\n          this.$refs.content.init(tempData1, _table)\r\n        })\r\n      } else if (this.Is_Pack === true) {\r\n        this.title = '添加打包件'\r\n        const tempData2 = this.tbData2.filter((item) => {\r\n          return !item.isOld\r\n        })\r\n        this.$nextTick((_) => {\r\n          this.$refs.content.init(tempData2, this.tbData2)\r\n        })\r\n      }\r\n    },\r\n    // 打包件详情\r\n    handleDetail(row) {\r\n      console.log(row, 'id===')\r\n      this.currentComponent = 'packDetail'\r\n      this.width = '60%'\r\n      this.title = '打包件详情'\r\n      this.topDialog = '10vh'\r\n      this.dialogVisible = true\r\n      this.$nextTick((_) => {\r\n        this.$refs.content.init(row.PackageId || row.Id)\r\n      })\r\n    },\r\n    handleEditCar() {\r\n      this.currentComponent = 'CarDialog'\r\n      this.title = '新增车辆'\r\n      this.topDialog = '10vh'\r\n      this.dialogVisible = true\r\n    },\r\n    close() {\r\n      this.dialogVisible = false\r\n    },\r\n    handleInfo(row) {\r\n      this.$refs.info.handleOpen(row)\r\n    },\r\n    getTableConfig(code) {\r\n      return new Promise((resolve) => {\r\n        GetGridByCode({\r\n          code\r\n        }).then((res) => {\r\n          const { IsSucceed, Data, Message } = res\r\n          if (IsSucceed) {\r\n            if (!Data) {\r\n              this.$message({\r\n                message: '表格配置不存在',\r\n                type: 'error'\r\n              })\r\n              return\r\n            }\r\n            this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n            this.columns = (\r\n              Data.ColumnList.filter((v) => v.Is_Display) || []\r\n            ).map((item) => {\r\n              item.Is_Resizable = true\r\n              item.Is_Sortable = true\r\n              return item\r\n            })\r\n            this.form.PageInfo.PageSize = +Data.Grid.Row_Number\r\n            if (this.isSub) {\r\n              this.tbConfig.Is_Select = false\r\n              this.tbConfig.Is_Row_Number = true\r\n            }\r\n            resolve(this.columns)\r\n          } else {\r\n            this.$message({\r\n              message: Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    uploadSuccess(response, file, fileList) {\r\n      console.log('fileList', fileList)\r\n      this.fileListArr = JSON.parse(JSON.stringify(fileList))\r\n    },\r\n    uploadRemove(file, fileList) {\r\n      this.fileListArr = JSON.parse(JSON.stringify(fileList))\r\n    },\r\n    async handlePreview(file) {\r\n      console.log('file', file)\r\n      let encryptionUrl = ''\r\n      if (file.response && file.response.encryptionUrl) {\r\n        encryptionUrl = file.response.encryptionUrl\r\n      } else {\r\n        encryptionUrl = await GetOssUrl({ url: file.encryptionUrl })\r\n        encryptionUrl = encryptionUrl.Data\r\n      }\r\n      window.open(encryptionUrl)\r\n    },\r\n    handleExceed() {\r\n      this.$message({\r\n        type: 'warning',\r\n        message: '附件数量不能超过10个'\r\n      })\r\n    },\r\n    async getContractList() {\r\n      try {\r\n        const submitData = {\r\n          ContractTypeCode: 3,\r\n          ProjectIds: []\r\n        }\r\n        if (this.form.ProjectId) {\r\n          submitData.ProjectIds.push(this.form.ProjectId)\r\n        }\r\n        const res = await GetContractList(submitData)\r\n        if (res.IsSucceed) {\r\n          this.contractOptions = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      } catch (error) {\r\n        console.error('获取合同列表失败:', error)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.tb-status {\r\n  background: #fae6bb;\r\n  padding: 16px 20px;\r\n  font-size: 1.2em;\r\n  font-weight: bold;\r\n  display: flex;\r\n\r\n  * {\r\n    margin-right: 12px;\r\n  }\r\n}\r\n\r\n.el-form {\r\n  margin: 16px 10px;\r\n}\r\n\r\n.title {\r\n  margin-left: 10px;\r\n}\r\n\r\n.cs-red{\r\n  color:red\r\n}\r\n\r\n.cs-tree-x {\r\n  ::v-deep {\r\n    .el-select {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n\r\n.statistics-container {\r\n  display: flex;\r\n  .statistics-item {\r\n    margin-right: 32px;\r\n    span:first-child {\r\n      display: inline-block;\r\n      font-size: 14px;\r\n      line-height: 18px;\r\n      font-weight: 500;\r\n      color: #999999;\r\n      // margin-right: 16px;\r\n    }\r\n    span:last-child {\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n      color: #00c361;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2YA,OAAAA,SAAA;AACA,OAAAC,SAAA;AACA,OAAAC,UAAA;AACA,SACAC,qBAAA,EACAC,yBAAA,EACAC,sBAAA,QACA;AACA,SAAAC,gCAAA;AACA,SAAAC,yBAAA;AACA,OAAAC,gBAAA;AACA,OAAAC,SAAA;AACA,SAAAC,iBAAA;AACA,OAAAC,SAAA;AACA,OAAAC,SAAA;AACA,SAAAC,YAAA,EAAAC,SAAA;AACA,SAAAC,kBAAA,EAAAC,gBAAA;AACA,SAAAC,aAAA,EAAAC,SAAA;AACA,SAAAC,4BAAA;AACA,SAAAC,oBAAA;AACA,OAAAC,OAAA;AACA,OAAAC,SAAA;AACA,SAAAC,kBAAA;AACA,SAAAC,UAAA;AACA,SAAAC,eAAA;AACA,SAAAC,WAAA;AACA,OAAAC,oBAAA;AACA,OAAAC,gBAAA;AAEA,IAAAC,QAAA;EACAC,GAAA;EACAC,OAAA;EACAC,QAAA;EACAC,IAAA;AACA;AACA;EACAC,UAAA;IACAN,gBAAA,EAAAA,gBAAA;IAAAD,oBAAA,EAAAA,oBAAA;IACAL,SAAA,EAAAA,SAAA;IACAtB,SAAA,EAAAA,SAAA;IACAC,SAAA,EAAAA,SAAA;IACAU,SAAA,EAAAA,SAAA;IACAH,gBAAA,EAAAA,gBAAA;IACAC,SAAA,EAAAA,SAAA;IACAG,SAAA,EAAAA,SAAA;IACAV,UAAA,EAAAA;EACA;EACAiC,KAAA;IACAC,MAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA,EAAAC,SAAA;MACAC,KAAA;MACAC,QAAA;MACAC,OAAA;MACAC,SAAA;MACAC,KAAA;MAAA;MACAC,KAAA;MACAC,SAAA;MACAC,UAAA;MACAC,gBAAA;MACAC,KAAA;MACAC,OAAA;MACAC,aAAA;MACAC,UAAA;MACAC,IAAA;QACAC,SAAA;QACAC,QAAA,MAAAC,IAAA;QACAC,OAAA;QACAC,gBAAA;QACAC,MAAA;QACAC,OAAA;QACAC,OAAA;QACAC,WAAA;QACAC,SAAA;QACAC,YAAA;QACAC,OAAA;QACAC,WAAA;QACAC,UAAA;QACAC,oBAAA;QACAC,SAAA;QACAC,aAAA,EAAA/B,SAAA;QACAgC,WAAA;QACAC,QAAA;UACAC,aAAA;UACAC,IAAA;UACAC,QAAA;QACA;QACAC,QAAA;QACAC,iBAAA;QACAC,KAAA;QACAC,aAAA;MACA;MACA;MACAC,gBAAA;QACA;QACAC,UAAA;QACAC,WAAA;QACA7C,IAAA;QACAL,KAAA;UACAmD,QAAA;UACAC,QAAA;UACAC,KAAA;UACAC,KAAA;QACA;MACA;MACAC,kBAAA;MAAA;MACAC,sBAAA;MACAC,mBAAA;MACAC,cAAA;MACAC,uBAAA;MACAC,UAAA;MACAC,YAAA;MACAC,gBAAA;MACAC,WAAA;MACAC,UAAA;MACAC,QAAA;MACAC,EAAA;MACAC,SAAA;MACAC,QAAA;MACAC,UAAA;MACAC,QAAA;QACAC,WAAA;MACA;MACAC,OAAA;MACAC,MAAA;MACAC,OAAA;MACAC,OAAA;MACAC,OAAA;MACAC,KAAA;MACAC,SAAA;MACAC,YAAA;MACAC,UAAA;MACAC,YAAA;MACAC,IAAA;MACAC,KAAA;QACA5D,QAAA,GACA;UAAA6D,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAnD,UAAA;UAAAiD,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACA;QACA;QACAjD,SAAA;UAAA+C,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAvD,SAAA;UAAAqD,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAC,iBAAA;MACAC,UAAA;QACAC,WAAA;QAAA;QACAC,aAAA;QAAA;QACAC,YAAA;QAAA;QACAC,UAAA;QAAA;QACAC,uBAAA;MACA;MACAC,eAAA;MACAC,WAAA;MACAC,OAAA;MACAC,eAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;MACAC,aAAA,OAAAA;IACA;EACA;EACAC,QAAA,EAAAC,aAAA;IACAC,SAAA,WAAAA,UAAA;MACA,UAAAd,UAAA,CAAAG,YAAA,UAAAH,UAAA,CAAAC,WAAA;MACA,YAAAD,UAAA,CAAAG,YAAA,QAAAH,UAAA,CAAAC,WAAA;IACA;IACAc,OAAA,WAAAA,QAAAC,IAAA;MAAA,IAAAF,SAAA,GAAAE,IAAA,CAAAF,SAAA;MACA,OAAAG,IAAA,CAAAC,GAAA,CAAAJ,SAAA,QAAAd,UAAA,CAAAE,aAAA,UAAAF,UAAA,CAAAK,uBAAA;IACA;EAAA,GACAxG,UAAA,8BACA;EACAsH,KAAA;IACA;MACAC,OAAA,WAAAA,QAAA;QACA,KAAAC,QAAA;MACA;IACA;EAEA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACAT,KAAA,CAAAhB,WAAA,GAAArG,QAAA,CAAAC,GAAA;YACAoH,KAAA,CAAAnG,KAAA,GAAAmG,KAAA,CAAAU,MAAA,CAAAC,KAAA,CAAA9G,KAAA;YAAA0G,QAAA,CAAAE,IAAA;YAAA,OACAT,KAAA,CAAAY,uBAAA;UAAA;YACAZ,KAAA,CAAAa,wBAAA;YACAb,KAAA,CAAAc,iBAAA;YACAd,KAAA,CAAAe,oBAAA;YACAf,KAAA,CAAAgB,aAAA;UAAA;UAAA;YAAA,OAAAT,QAAA,CAAAU,IAAA;QAAA;MAAA,GAAAb,OAAA;IAAA;EACA;EACAc,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAlB,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAiB,SAAA;MAAA,IAAAC,WAAA,EAAArD,YAAA,EAAAsD,YAAA,EAAAC,IAAA,EAAApE,EAAA,EAAAqE,IAAA,EAAA1G,OAAA,EAAA2G,aAAA,EAAAC,cAAA;MAAA,OAAAxB,mBAAA,GAAAG,IAAA,UAAAsB,UAAAC,SAAA;QAAA,kBAAAA,SAAA,CAAApB,IAAA,GAAAoB,SAAA,CAAAnB,IAAA;UAAA;YAAA,KACAU,MAAA,CAAAjI,MAAA;cAAA0I,SAAA,CAAAnB,IAAA;cAAA;YAAA;YAAAY,WAAA,GAGAQ,IAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAAZ,MAAA,CAAAT,MAAA,CAAAC,KAAA,CAAAqB,CAAA,IADAhE,YAAA,GAAAqD,WAAA,CAAArD,YAAA;YAEAmD,MAAA,CAAAnD,YAAA,GAAAA,YAAA;YAAA4D,SAAA,CAAAnB,IAAA;YAAA,OACAU,MAAA,CAAAc,OAAA;UAAA;YAAAL,SAAA,CAAAnB,IAAA;YAAA;UAAA;YAAAa,YAAA,GASAO,IAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAAZ,MAAA,CAAAT,MAAA,CAAAC,KAAA,CAAAqB,CAAA,IANAT,IAAA,GAAAD,YAAA,CAAAC,IAAA,EACApE,EAAA,GAAAmE,YAAA,CAAAnE,EAAA,EACAqE,IAAA,GAAAF,YAAA,CAAAE,IAAA,EACA1G,OAAA,GAAAwG,YAAA,CAAAxG,OAAA,EACAkD,aAAA,GAAAsD,YAAA,CAAAtD,YAAA,EACA0D,cAAA,GAAAJ,YAAA,CAAAI,cAAA,EAEA;YACAP,MAAA,CAAAnD,YAAA,GAAAA,aAAA;YACAmD,MAAA,CAAA/D,SAAA,GAAAD,EAAA;YACAgE,MAAA,CAAAe,YAAA,GAAAV,IAAA;YACAL,MAAA,CAAA7G,IAAA,CAAAa,WAAA,GAAAoG,IAAA;YACAJ,MAAA,CAAA7G,IAAA,CAAAQ,OAAA,GAAAA,OAAA;YACAqG,MAAA,CAAA7G,IAAA,CAAAC,SAAA,GAAAmH,cAAA;YACAP,MAAA,CAAA7G,IAAA,CAAAU,SAAA,GAAAmG,MAAA,CAAAgB,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,IAAA;YACAnB,MAAA,CAAAoB,gBAAA,CAAApB,MAAA,CAAA/D,SAAA;YACA+D,MAAA,CAAA/C,KAAA,CAAAhD,UAAA,IAAAiD,QAAA,IAAAL,aAAA;UAAA;YAEAmD,MAAA,CAAAqB,eAAA;UAAA;UAAA;YAAA,OAAAZ,SAAA,CAAAX,IAAA;QAAA;MAAA,GAAAG,QAAA;IAAA;EACA;EACAqB,OAAA;IACA7B,uBAAA,WAAAA,wBAAA;MAAA,IAAA8B,MAAA;MAAA,OAAAzC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAwC,SAAA;QAAA,IAAAC,GAAA;QAAA,OAAA1C,mBAAA,GAAAG,IAAA,UAAAwC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtC,IAAA,GAAAsC,SAAA,CAAArC,IAAA;YAAA;cAAAqC,SAAA,CAAArC,IAAA;cAAA,OACApJ,yBAAA;gBAAA0L,IAAA;cAAA;YAAA;cAAAH,GAAA,GAAAE,SAAA,CAAAE,IAAA;cACA,IAAAJ,GAAA,CAAAK,IAAA;gBACAP,MAAA,CAAA3D,eAAA;cACA;YAAA;YAAA;cAAA,OAAA+D,SAAA,CAAA7B,IAAA;UAAA;QAAA,GAAA0B,QAAA;MAAA;IACA;IAEA;IACA9B,wBAAA,WAAAA,yBAAA;MAAA,IAAAqC,MAAA;MACA9L,gCAAA;QAAA+L,SAAA,EAAAC,YAAA,CAAAC,OAAA;MAAA,GAAAC,IAAA,WAAAV,GAAA;QACA,IAAAA,GAAA,CAAAW,SAAA;UACAL,MAAA,CAAA1G,kBAAA,GAAAoG,GAAA,CAAAK,IAAA;UACA,IAAAO,KAAA,GAAAJ,YAAA,CAAAC,OAAA;UACA,IAAAI,GAAA,GAAAP,MAAA,CAAA1G,kBAAA,CAAAkH,IAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAxG,EAAA,KAAAqG,KAAA;UAAA;UACA,IAAAC,GAAA;YACAP,MAAA,CAAA5I,IAAA,CAAAgB,SAAA,GAAAmI,GAAA,CAAAtG,EAAA;UACA;QACA;UACA+F,MAAA,CAAAU,QAAA;YACAtF,OAAA,EAAAsE,GAAA,CAAAiB,OAAA;YACA1K,IAAA;UACA;QACA;MACA;IACA;IAEA2H,iBAAA,WAAAA,kBAAA;MAAA,IAAAgD,MAAA;MACA5L,oBAAA;QAAA6L,KAAA;MAAA,GAAAT,IAAA,WAAAV,GAAA;QACA,IAAAA,GAAA,CAAAW,SAAA;UACA,IAAAS,IAAA,GAAApB,GAAA,CAAAK,IAAA;UACAa,MAAA,CAAAG,eAAA,CAAAD,IAAA;UACAF,MAAA,CAAA7H,gBAAA,CAAA3C,IAAA,GAAA0K,IAAA;UACAF,MAAA,CAAAI,SAAA,WAAAC,CAAA;YAAA,IAAAC,qBAAA;YACA,CAAAA,qBAAA,GAAAN,MAAA,CAAAO,KAAA,CAAAC,gBAAA,cAAAF,qBAAA,eAAAA,qBAAA,CAAAG,iBAAA,CAAAP,IAAA;YACA,IAAAQ,GAAA,GAAAV,MAAA,CAAAW,2BAAA,CAAAT,IAAA;UACA;QACA;UACAF,MAAA,CAAAF,QAAA;YACAtF,OAAA,EAAAsE,GAAA,CAAAiB,OAAA;YACA1K,IAAA;UACA;QACA;MACA;IACA;IACAsL,2BAAA,WAAAA,4BAAAC,IAAA;MACA,KAAAA,IAAA,KAAAA,IAAA,CAAAC,MAAA;MACA,IAAAC,MAAA;MACA,IAAAC,QAAA,YAAAA,QAAAC,KAAA;QACA,KAAAA,KAAA,KAAAA,KAAA,CAAAH,MAAA;QACAG,KAAA,CAAAC,OAAA,WAAApB,IAAA;UAAA,IAAAqB,UAAA,EAAAC,WAAA;UACA,IAAAC,QAAA,GAAAvB,IAAA,CAAAuB,QAAA;UACA,MAAAF,UAAA,GAAArB,IAAA,CAAAV,IAAA,cAAA+B,UAAA,uBAAAA,UAAA,CAAAG,UAAA,gBAAAF,WAAA,GAAAtB,IAAA,CAAAV,IAAA,cAAAgC,WAAA,uBAAAA,WAAA,CAAAG,IAAA;YACAR,MAAA,CAAAS,IAAA,CAAA1B,IAAA;UACA;UACA,IAAAuB,QAAA,IAAAA,QAAA,CAAAP,MAAA;YACAE,QAAA,CAAAK,QAAA;UACA;QACA;MACA;MACAL,QAAA,CAAAH,IAAA;MACA,IAAAjB,GAAA,GAAAmB,MAAA,CAAAlB,IAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAxG,EAAA,KAAAiG,YAAA,CAAAC,OAAA;MAAA;MACA,IAAAI,GAAA,UAAAvK,MAAA;QACA,KAAAoB,IAAA,CAAAgB,SAAA,GAAAmI,GAAA,CAAAtG,EAAA;MACA;IACA;IACA8G,eAAA,WAAAA,gBAAAS,IAAA;MAAA,IAAAY,MAAA;MACA,KAAAZ,IAAA;MACAA,IAAA,CAAAK,OAAA,WAAAQ,OAAA;QACA,IAAAL,QAAA,GAAAK,OAAA,CAAAL,QAAA;QACA,IAAAK,OAAA,CAAAtC,IAAA,CAAAkC,UAAA,aAAAI,OAAA,CAAAtC,IAAA,CAAAmC,IAAA;UACAG,OAAA,CAAAnJ,QAAA;QACA;UACAmJ,OAAA,CAAAnJ,QAAA;QACA;QACA,IAAA8I,QAAA,CAAAP,MAAA;UACAW,MAAA,CAAArB,eAAA,CAAAiB,QAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACAM,WAAA,WAAAA,YAAA,GAEA;IACAC,YAAA,WAAAA,aAAA,GAEA;IACAC,MAAA,WAAAA,OAAAC,CAAA,EAAAC,CAAA;MACA,OAAAzN,OAAA,CAAAwN,CAAA,EAAAE,QAAA,CAAAD,CAAA,EAAAE,MAAA;IACA;IACA/E,oBAAA,WAAAA,qBAAA;MAAA,IAAAgF,MAAA;MAAA,OAAA9F,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6F,SAAA;QAAA,OAAA9F,mBAAA,GAAAG,IAAA,UAAA4F,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1F,IAAA,GAAA0F,SAAA,CAAAzF,IAAA;YAAA;cAAAyF,SAAA,CAAAzF,IAAA;cAAA,OACAxI,4BAAA;gBACAkO,SAAA,EAAA/C,YAAA,CAAAC,OAAA;cACA,GAAAC,IAAA,WAAAV,GAAA;gBACA,IAAAA,GAAA,CAAAW,SAAA;kBACAwC,MAAA,CAAAhJ,gBAAA,GAAA6F,GAAA,CAAAK,IAAA;kBACA8C,MAAA,CAAAzL,IAAA,CAAAe,oBAAA,GAAA0K,MAAA,CAAAhJ,gBAAA,IAAAwE,IAAA;gBACA;kBACAwE,MAAA,CAAAnC,QAAA;oBACAtF,OAAA,EAAAsE,GAAA,CAAAiB,OAAA;oBACA1K,IAAA;kBACA;gBACA;cACA;YAAA;cAAA+M,SAAA,CAAAzF,IAAA;cAAA,OACAsF,MAAA,CAAAK,cAAA,kCAAAC,MAAA,CACAN,MAAA,CAAAhJ,gBAAA,IAAAyE,IAAA,CACA;YAAA;YAAA;cAAA,OAAA0E,SAAA,CAAAjF,IAAA;UAAA;QAAA,GAAA+E,QAAA;MAAA;IACA;IACAzD,gBAAA,WAAAA,iBAAApF,EAAA;MAAA,IAAAmJ,MAAA;MACAxO,gBAAA;QAAAqF,EAAA,EAAAA;MAAA,GAAAmG,IAAA,WAAAV,GAAA;QACA,IAAAA,GAAA,CAAAW,SAAA;UACA,IAAAgD,SAAA,GAAA3D,GAAA,CAAAK,IAAA,CAAAuD,QAAA,CAAA9C,IAAA,WAAAC,IAAA;YACA,OAAAA,IAAA,CAAAyB,IAAA;UACA;UACAkB,MAAA,CAAAhM,IAAA,CAAAS,WAAA,IAAAwL,SAAA,aAAAA,SAAA,uBAAAA,SAAA,CAAAhF,IAAA;UACA+E,MAAA,CAAAhM,IAAA,CAAAW,YAAA,IAAAsL,SAAA,aAAAA,SAAA,uBAAAA,SAAA,CAAAE,GAAA;UACAH,MAAA,CAAAhM,IAAA,CAAAyB,KAAA,GAAA6G,GAAA,CAAAK,IAAA,CAAAlH,KAAA;QACA;MACA;IACA;IACA2K,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA9O,kBAAA;QAAA+D,QAAA;MAAA,GAAA0H,IAAA,WAAAV,GAAA;QACA,IAAAA,GAAA,CAAAW,SAAA;UACAoD,MAAA,CAAAzJ,QAAA,GAAA0F,GAAA,CAAAK,IAAA,CAAAA,IAAA;QACA;MACA;IACA;IACA2D,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACA7N,IAAA;MACA,GACAmK,IAAA;QACA3L,YAAA,CAAAkP,MAAA,CAAA1E,MAAA,EAAA0E,MAAA,CAAAnG,MAAA;MACA,GACAuG,KAAA;QACAJ,MAAA,CAAAjD,QAAA;UACAzK,IAAA;UACAmF,OAAA;QACA;MACA;IACA;IACA4I,WAAA,WAAAA,YAAAC,CAAA;MACA,IAAAA,CAAA;QACA,KAAAzN,QAAA;QACA,KAAAC,OAAA;QACA,KAAAqF,WAAA,GAAArG,QAAA,CAAAC,GAAA;QACA,KAAAwN,cAAA,IAAAC,MAAA,CAAAc,CAAA,OAAAd,MAAA,MAAAtJ,gBAAA,IAAAyE,IAAA;MACA,WAAA2F,CAAA;QACA,KAAAzN,QAAA;QACA,KAAAC,OAAA;QACA,KAAAqF,WAAA,GAAArG,QAAA,CAAAE,OAAA;QACA,KAAAuN,cAAA,IAAAC,MAAA,CAAAc,CAAA,OAAAd,MAAA,MAAAtJ,gBAAA,IAAAyE,IAAA;MACA,WAAA2F,CAAA;QACA,KAAAzN,QAAA;QACA,KAAAsF,WAAA,GAAArG,QAAA,CAAAG,QAAA;QACA,KAAAsN,cAAA,IAAAC,MAAA,CAAAc,CAAA;QACA,KAAAxN,OAAA;MACA,WAAAwN,CAAA;QACA,KAAAzN,QAAA;QACA,KAAAC,OAAA;QACA,KAAAqF,WAAA,GAAArG,QAAA,CAAAI,IAAA;QACA,KAAAqN,cAAA,IAAAC,MAAA,CAAAc,CAAA;MACA;IACA;IACAC,SAAA,WAAAA,UAAAD,CAAA,EAAAE,EAAA,EAAAC,GAAA;MACAC,OAAA,CAAAC,GAAA,SAAAH,EAAA,EAAAC,GAAA,EAAAA,GAAA,CAAAG,gBAAA;MACA,IAAAJ,EAAA,QAAAA,EAAA,GAAAC,GAAA,CAAAG,gBAAA;QACAH,GAAA,CAAAI,OAAA,GAAAJ,GAAA,CAAAG,gBAAA;MACA;QACAH,GAAA,CAAAI,OAAA,GAAAC,MAAA,CAAAN,EAAA;MACA;MACAC,GAAA,CAAAM,SAAA,QAAAC,YAAA,CAAAP,GAAA;MACA,KAAAzK,UAAA,CAAA6G,IAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAAmE,YAAA,IAAAR,GAAA,CAAAnK,EAAA;UACAwG,IAAA,CAAAoE,WAAA,GAAAT,GAAA,CAAAI,OAAA;QACA;MACA;IACA;IACAM,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAA5D,KAAA,SAAA6D,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;UACA;QACA;QACAF,MAAA,CAAArO,SAAA;QAEA,IAAAwO,cAAA;QACA,IAAAH,MAAA,CAAAjL,WAAA,CAAA2H,MAAA;UACAsD,MAAA,CAAAjL,WAAA,CAAA+H,OAAA,WAAApB,IAAA;YACAyE,cAAA,CAAA/C,IAAA,CACA1B,IAAA,CAAA0E,QAAA,IAAA1E,IAAA,CAAA0E,QAAA,CAAAC,aAAA,GACA3E,IAAA,CAAA0E,QAAA,CAAAC,aAAA,GACA3E,IAAA,CAAA2E,aACA;UACA;QACA;QAEA,IAAAC,SAAA;UACA9L,sBAAA;YACA;YACA+E,IAAA,EAAAyG,MAAA,CAAA3N,IAAA,CAAAc,UAAA;YACAoN,UAAA,EAAAJ,cAAA,CAAAK,QAAA;YACAlO,SAAA,EAAA0N,MAAA,CAAA7K,SAAA;YACAmJ,SAAA,EAAA0B,MAAA,CAAA3N,IAAA,CAAAS,WAAA;YACA2N,YAAA,EAAAT,MAAA,CAAA3N,IAAA,CAAAW,YAAA;YACAK,SAAA,EAAA2M,MAAA,CAAA3N,IAAA,CAAAgB,SAAA;YACAqN,SAAA,EAAAV,MAAA,CAAA3N,IAAA,CAAAU,SAAA;YACA4N,SAAA,EAAAX,MAAA,CAAA3N,IAAA,CAAAO,OAAA;YACAgO,UAAA,EAAAZ,MAAA,CAAA3N,IAAA,CAAAK,gBAAA;YACAmO,SAAA,EAAAb,MAAA,CAAA3N,IAAA,CAAAM,MAAA;YACAmO,QAAA,EAAAnR,SAAA,CAAAqQ,MAAA,CAAA3N,IAAA,CAAAE,QAAA;YACAE,OAAA,EAAAuN,MAAA,CAAA3N,IAAA,CAAAI,OAAA;YACAsO,WAAA,EAAAf,MAAA,CAAA3N,IAAA,CAAAa,WAAA;YACA8N,MAAA,EAAAhB,MAAA,CAAAlL,gBAAA,IAAAyE,IAAA;YACA1G,OAAA,EAAAmN,MAAA,CAAA3N,IAAA,CAAAQ,OAAA;YACAS,aAAA,EAAA0M,MAAA,CAAA3N,IAAA,CAAAiB,aAAA;YACAC,WAAA,EAAAyM,MAAA,CAAA3N,IAAA,CAAAkB,WAAA;YACAK,QAAA,EAAAoM,MAAA,CAAA3N,IAAA,CAAAuB,QAAA;YACAC,iBAAA,EAAAmM,MAAA,CAAA3N,IAAA,CAAAwB,iBAAA;YACAC,KAAA,EAAAkM,MAAA,CAAA3N,IAAA,CAAAyB,KAAA;YACAC,aAAA,EAAAiM,MAAA,CAAA3N,IAAA,CAAA0B;UACA;UACAY,uBAAA;UACAsM,QAAA;QACA;QACA;QACA,IAAAC,QAAA,MAAA9C,MAAA,CAAA+C,kBAAA,CAAAnB,MAAA,CAAAvK,MAAA,GAAA0L,kBAAA,CAAAnB,MAAA,CAAAtK,OAAA,GAAAyL,kBAAA,CAAAnB,MAAA,CAAArK,OAAA,GAAAwL,kBAAA,CAAAnB,MAAA,CAAApK,OAAA;QACAsL,QAAA,CAAAE,MAAA,WAAA1F,IAAA;UACA,IAAAA,IAAA,CAAA2F,SAAA;YACA,IACAnM,EAAA,GAaAwG,IAAA,CAbAxG,EAAA;cACAoM,WAAA,GAYA5F,IAAA,CAZA4F,WAAA;cACAD,SAAA,GAWA3F,IAAA,CAXA2F,SAAA;cACAE,YAAA,GAUA7F,IAAA,CAVA6F,YAAA;cACAC,WAAA,GASA9F,IAAA,CATA8F,WAAA;cACAC,SAAA,GAQA/F,IAAA,CARA+F,SAAA;cACA9B,SAAA,GAOAjE,IAAA,CAPAiE,SAAA;cACA+B,GAAA,GAMAhG,IAAA,CANAgG,GAAA;cACAC,MAAA,GAKAjG,IAAA,CALAiG,MAAA;cACAC,SAAA,GAIAlG,IAAA,CAJAkG,SAAA;cACAC,gBAAA,GAGAnG,IAAA,CAHAmG,gBAAA;cACAC,SAAA,GAEApG,IAAA,CAFAoG,SAAA;cACAC,KAAA,GACArG,IAAA,CADAqG,KAAA;YAEA,IAAAC,QAAA;cACAX,SAAA,EAAAA,SAAA;cACAvB,WAAA,EAAAwB,WAAA;cACAC,YAAA,EAAAA,YAAA;cACAC,WAAA,EAAAA,WAAA;cACAS,WAAA,EAAAR,SAAA;cACAI,gBAAA,EAAAA,gBAAA;cACAC,SAAA,EAAAA,SAAA;cACAJ,GAAA,EAAAA,GAAA;cACAC,MAAA,EAAAA,MAAA;cACAhC,SAAA,EAAAA,SAAA;cACAiC,SAAA,EAAAA,SAAA;cACAG,KAAA,EAAAA;YACA;YACA,KAAAA,KAAA;cACAzB,SAAA,CAAA3L,uBAAA,CAAAyI,IAAA,CAAA4E,QAAA;YACA;UACA,WAAAtG,IAAA,CAAAnC,IAAA;YACA,IACArE,GAAA,GAUAwG,IAAA,CAVAxG,EAAA;cACAuK,OAAA,GASA/D,IAAA,CATA+D,OAAA;cACA6B,YAAA,GAQA5F,IAAA,CARA4F,WAAA;cACAC,aAAA,GAOA7F,IAAA,CAPA6F,YAAA;cACAM,iBAAA,GAMAnG,IAAA,CANAmG,gBAAA;cACAC,UAAA,GAKApG,IAAA,CALAoG,SAAA;cACAN,YAAA,GAIA9F,IAAA,CAJA8F,WAAA;cACAC,UAAA,GAGA/F,IAAA,CAHA+F,SAAA;cACA9B,UAAA,GAEAjE,IAAA,CAFAiE,SAAA;cACAoC,MAAA,GACArG,IAAA,CADAqG,KAAA;YAEA,IAAAC,SAAA;cACAnC,YAAA,EAAA3K,GAAA;cACA4K,WAAA,EAAAL,OAAA;cACA8B,YAAA,EAAAA,aAAA;cACAC,WAAA,EAAAA,YAAA;cACAS,WAAA,EAAAR,UAAA;cACAI,gBAAA,EAAAA,iBAAA;cACAC,SAAA,EAAAA,UAAA;cACAC,KAAA,EAAAA,MAAA;cACApC,SAAA,EAAAA;YACA;YACA,KAAAoC,MAAA;cACA,OAAAC,SAAA,CAAAD,KAAA;cACAzB,SAAA,CAAA3L,uBAAA,CAAAyI,IAAA,CAAA4E,SAAA;YACA;UACA,WAAAtG,IAAA,CAAAwG,SAAA;YACA,IACAC,gBAAA,GASAzG,IAAA,CATAyG,gBAAA;cACAD,SAAA,GAQAxG,IAAA,CARAwG,SAAA;cACAE,SAAA,GAOA1G,IAAA,CAPA0G,SAAA;cACAC,gBAAA,GAMA3G,IAAA,CANA2G,gBAAA;cACA5C,QAAA,GAKA/D,IAAA,CALA+D,OAAA;cACA6C,IAAA,GAIA5G,IAAA,CAJA4G,IAAA;cACAC,MAAA,GAGA7G,IAAA,CAHA6G,MAAA;cACAC,MAAA,GAEA9G,IAAA,CAFA8G,MAAA;cACAC,UAAA,GACA/G,IAAA,CADA+G,UAAA;YAEA,IAAAT,UAAA;cACAG,gBAAA,EAAAA,gBAAA;cACAD,SAAA,EAAAA,SAAA;cACAE,SAAA,EAAAA,SAAA;cACAC,gBAAA,EAAAA,gBAAA;cACAK,MAAA,EAAAjD,QAAA;cACA6C,IAAA,EAAAA,IAAA;cACAC,MAAA,EAAAA,MAAA;cACAC,MAAA,EAAAA,MAAA;cACAC,UAAA,EAAAA;YACA;YACAnC,SAAA,CAAAW,QAAA,CAAA7D,IAAA,CAAA4E,UAAA;UACA;QACA;QAEAhC,MAAA,CAAAjO,UAAA;QACA,IAAAiO,MAAA,CAAA/O,MAAA;UACA;UACA;UACA+O,MAAA,CAAAxL,sBAAA,CAAA+E,IAAA,GAAAyG,MAAA,CAAA3N,IAAA,CAAAc,UAAA;UACA6M,MAAA,CAAAxL,sBAAA,CAAA8J,SAAA,GAAA0B,MAAA,CAAA3N,IAAA,CAAAS,WAAA;UACAkN,MAAA,CAAAxL,sBAAA,CAAAiM,YAAA,GAAAT,MAAA,CAAA3N,IAAA,CAAAW,YAAA;UACAgN,MAAA,CAAAxL,sBAAA,CAAAmM,SAAA,GAAAX,MAAA,CAAA3N,IAAA,CAAAO,OAAA;UACAoN,MAAA,CAAAxL,sBAAA,CAAAoM,UAAA,GAAAZ,MAAA,CAAA3N,IAAA,CAAAK,gBAAA;UACAsN,MAAA,CAAAxL,sBAAA,CAAAqM,SAAA,GAAAb,MAAA,CAAA3N,IAAA,CAAAM,MAAA;UACAqN,MAAA,CAAAxL,sBAAA,CAAAnB,SAAA,GAAA2M,MAAA,CAAA3N,IAAA,CAAAgB,SAAA;UACA2M,MAAA,CAAAxL,sBAAA,CAAAkM,SAAA,GAAAV,MAAA,CAAA3N,IAAA,CAAAU,SAAA;UACAiN,MAAA,CAAAxL,sBAAA,CAAA3B,OAAA,GAAAmN,MAAA,CAAA3N,IAAA,CAAAQ,OAAA;UACAmN,MAAA,CAAAxL,sBAAA,CAAAjB,WAAA,GAAAyM,MAAA,CAAA3N,IAAA,CAAAkB,WAAA;UACAyM,MAAA,CAAAxL,sBAAA,CAAAlB,aAAA,GAAA0M,MAAA,CAAA3N,IAAA,CAAAiB,aAAA;UACA0M,MAAA,CAAAxL,sBAAA,CAAAZ,QAAA,GAAAoM,MAAA,CAAA3N,IAAA,CAAAuB,QAAA;UACAoM,MAAA,CAAAxL,sBAAA,CAAAX,iBAAA,GAAAmM,MAAA,CAAA3N,IAAA,CAAAwB,iBAAA;UACAmM,MAAA,CAAAxL,sBAAA,CAAAV,KAAA,GAAAkM,MAAA,CAAA3N,IAAA,CAAAyB,KAAA;UACAkM,MAAA,CAAAxL,sBAAA,CAAAT,aAAA,GAAAiM,MAAA,CAAA3N,IAAA,CAAA0B,aAAA;UACAiM,MAAA,CAAAxL,sBAAA,CAAAsM,QAAA,GAAAnR,SAAA,CACAqQ,MAAA,CAAA3N,IAAA,CAAAE,QAAA,EACA,yBACA;UACAyN,MAAA,CAAAxL,sBAAA,CAAA/B,OAAA,GAAAuN,MAAA,CAAA3N,IAAA,CAAAI,OAAA;UAEA,IAAA0N,eAAA;UACA,IAAAH,MAAA,CAAAjL,WAAA,CAAA2H,MAAA;YACAsD,MAAA,CAAAjL,WAAA,CAAA+H,OAAA,WAAApB,IAAA;cACAyE,eAAA,CAAA/C,IAAA,CACA1B,IAAA,CAAA0E,QAAA,IAAA1E,IAAA,CAAA0E,QAAA,CAAAC,aAAA,GACA3E,IAAA,CAAA0E,QAAA,CAAAC,aAAA,GACA3E,IAAA,CAAA2E,aACA;YACA;UACA;UACAL,MAAA,CAAAxL,sBAAA,CAAA+L,UAAA,GAAAJ,eAAA,CAAAK,QAAA;UACAF,SAAA,CAAA9L,sBAAA,GAAAwL,MAAA,CAAAxL,sBAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA8L,SAAA,CAAA3L,uBAAA,MAAAyJ,MAAA,CAAA+C,kBAAA,CACAnB,MAAA,CAAApL,UAAA,GAAAuM,kBAAA,CACAnB,MAAA,CAAAnL,YAAA,GAAAsM,kBAAA,CACAb,SAAA,CAAA3L,uBAAA,EACA;UACA,IAAA2L,SAAA,CAAA3L,uBAAA,CAAA+H,MAAA,UAAA4D,SAAA,CAAAW,QAAA,CAAAvE,MAAA;YACAsD,MAAA,CAAArE,QAAA;cACAtF,OAAA;cACAnF,IAAA;YACA;UACA;YACA8O,MAAA,CAAA9N,OAAA;YACAhD,sBAAA,CAAAoR,SAAA,EAAAjF,IAAA,WAAAV,GAAA;cACA,IAAAA,GAAA,CAAAW,SAAA;gBACA0E,MAAA,CAAArE,QAAA;kBACAtF,OAAA;kBACAnF,IAAA;gBACA;gBACAxB,YAAA,CAAAsQ,MAAA,CAAA9F,MAAA,EAAA8F,MAAA,CAAAvH,MAAA;gBACAuH,MAAA,CAAA2C,OAAA,CAAAC,OAAA;kBACAvI,IAAA;kBACA3B,KAAA;oBACAmK,OAAA;kBACA;gBACA;cACA;gBACA7C,MAAA,CAAArO,SAAA;gBACAqO,MAAA,CAAArE,QAAA;kBACAtF,OAAA,EAAAsE,GAAA,CAAAiB,OAAA;kBACA1K,IAAA;gBACA;cACA;cACA8O,MAAA,CAAA9N,OAAA;cACA8N,MAAA,CAAAjO,UAAA;YACA;UACA;QACA;UACA,IAAAuO,SAAA,CAAA3L,uBAAA,CAAA+H,MAAA,UAAA4D,SAAA,CAAAW,QAAA,CAAAvE,MAAA;YACAsD,MAAA,CAAArE,QAAA;cACAtF,OAAA;cACAnF,IAAA;YACA;UACA;YACAoO,OAAA,CAAAC,GAAA,cAAAe,SAAA;YACAN,MAAA,CAAA9N,OAAA;YACAlD,qBAAA,CAAAsR,SAAA,EAAAjF,IAAA,WAAAV,GAAA;cACA,IAAAA,GAAA,CAAAW,SAAA;gBACA0E,MAAA,CAAArE,QAAA;kBACAtF,OAAA;kBACAnF,IAAA;gBACA;gBACAxB,YAAA,CAAAsQ,MAAA,CAAA9F,MAAA,EAAA8F,MAAA,CAAAvH,MAAA;gBACAuH,MAAA,CAAA2C,OAAA,CAAAC,OAAA;kBACAvI,IAAA;kBACA3B,KAAA;oBACAmK,OAAA;kBACA;gBACA;cACA;gBACA7C,MAAA,CAAArO,SAAA;gBACAqO,MAAA,CAAArE,QAAA;kBACAtF,OAAA,EAAAsE,GAAA,CAAAiB,OAAA;kBACA1K,IAAA;gBACA;cACA;cACA8O,MAAA,CAAAjO,UAAA;cACAiO,MAAA,CAAA9N,OAAA;YACA;UACA;QACA;MACA;IACA;IACA4Q,WAAA,WAAAA,YAAAC,IAAA,EAAAC,GAAA;MAAA,IAAAC,OAAA;MAAA,OAAAjL,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAgL,SAAA;QAAA,IAAA5C,SAAA;QAAA,OAAArI,mBAAA,GAAAG,IAAA,UAAA+K,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7K,IAAA,GAAA6K,SAAA,CAAA5K,IAAA;YAAA;cACA8H,SAAA,GAAAyC,IAAA,CAAAM,GAAA,WAAA3H,IAAA;gBACA;kBACAxG,EAAA,EAAAwG,IAAA,CAAAsH,GAAA;kBACA7F,IAAA,EAAA8F,OAAA,CAAAlM,WAAA,KAAArG,QAAA,CAAAC,GAAA,OAAAsS,OAAA,CAAAlM,WAAA,KAAArG,QAAA,CAAAG,QAAA;gBACA;cACA;cAAAuS,SAAA,CAAA5K,IAAA;cAAA,OACAjI,WAAA,CAAA+P,SAAA,EAAAjF,IAAA,WAAAV,GAAA;gBACA,IAAAA,GAAA,CAAAW,SAAA;kBACA,IAAAgI,OAAA;kBACA3I,GAAA,CAAAK,IAAA,CAAA8B,OAAA,WAAApB,IAAA;oBACA4H,OAAA,CAAA5H,IAAA,CAAAxG,EAAA,MAAAwG,IAAA,CAAA6H,OAAA;kBACA;kBACAR,IAAA,CAAAjG,OAAA,WAAAuC,GAAA;oBACA,IAAAiE,OAAA,CAAAjE,GAAA,CAAA2D,GAAA;sBACAC,OAAA,CAAAO,IAAA,CAAAnE,GAAA,cAAAiE,OAAA,CAAAjE,GAAA,CAAA2D,GAAA;oBACA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAI,SAAA,CAAApK,IAAA;UAAA;QAAA,GAAAkK,QAAA;MAAA;IACA;IACAlJ,OAAA,WAAAA,QAAA;MAAA,IAAAyJ,OAAA;MAAA,OAAAzL,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAwL,SAAA;QAAA,OAAAzL,mBAAA,GAAAG,IAAA,UAAAuL,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArL,IAAA,GAAAqL,SAAA,CAAApL,IAAA;YAAA;cAAAoL,SAAA,CAAApL,IAAA;cAAA,OACAvJ,yBAAA;gBACAiG,EAAA,EAAAuO,OAAA,CAAAhL,MAAA,CAAAC,KAAA,CAAAmL;cACA,GAAAxI,IAAA;gBAAA,IAAAyI,KAAA,GAAA9L,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6L,SAAApJ,GAAA;kBAAA,IAAAqJ,qBAAA,EAAAC,sBAAA,EAAA/O,EAAA,EAAAqE,IAAA,EAAAjH,SAAA,EAAAyO,WAAA,EAAAzC,SAAA,EAAAmC,YAAA,EAAApN,SAAA,EAAAqN,SAAA,EAAAC,SAAA,EAAAC,UAAA,EAAAC,SAAA,EAAAhO,OAAA,EAAA0N,UAAA,EAAAO,QAAA,EAAArO,OAAA,EAAAyR,OAAA,EAAA5Q,aAAA,EAAAC,WAAA,EAAAK,QAAA,EAAAC,iBAAA,EAAAC,KAAA,EAAAC,aAAA,EAAAoQ,aAAA,EAAAC,cAAA;kBAAA,OAAAnM,mBAAA,GAAAG,IAAA,UAAAiM,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAA/L,IAAA,GAAA+L,SAAA,CAAA9L,IAAA;sBAAA;wBAAA,KACAmC,GAAA,CAAAW,SAAA;0BAAAgJ,SAAA,CAAA9L,IAAA;0BAAA;wBAAA;wBACAiL,OAAA,CAAAjP,sBAAA,GAAAmG,GAAA,CAAAK,IAAA,CAAAuJ,sBAAA;wBACAd,OAAA,CAAA7O,UAAA,GAAA+F,GAAA,CAAAK,IAAA,CAAApG,UAAA;wBACA6O,OAAA,CAAA5O,YAAA,GAAA8F,GAAA,CAAAK,IAAA,CAAAnG,YAAA;wBACA4O,OAAA,CAAAxC,QAAA,GAAAtG,GAAA,CAAAK,IAAA,CAAAiG,QAAA;wBACAwC,OAAA,CAAAjN,UAAA,GAAAmE,GAAA,CAAAK,IAAA,CAAAwJ,UAAA;wBACAf,OAAA,CAAAnS,UAAA,GAAAmS,OAAA,CAAAjP,sBAAA,CAAAiQ,MAAA;wBACAhB,OAAA,CAAAzM,OAAA,KAAAgN,qBAAA,GAAAP,OAAA,CAAAjP,sBAAA,cAAAwP,qBAAA,uBAAAA,qBAAA,CAAAS,MAAA;wBAAAR,sBAAA,GA0BAR,OAAA,CAAAjP,sBAAA,EAvBAU,EAAA,GAAA+O,sBAAA,CAAA/O,EAAA,EACAqE,IAAA,GAAA0K,sBAAA,CAAA1K,IAAA,EACAjH,SAAA,GAAA2R,sBAAA,CAAA3R,SAAA,EACAyO,WAAA,GAAAkD,sBAAA,CAAAlD,WAAA,EACAzC,SAAA,GAAA2F,sBAAA,CAAA3F,SAAA,EACAmC,YAAA,GAAAwD,sBAAA,CAAAxD,YAAA,EACApN,SAAA,GAAA4Q,sBAAA,CAAA5Q,SAAA,EACAqN,SAAA,GAAAuD,sBAAA,CAAAvD,SAAA,EACAC,SAAA,GAAAsD,sBAAA,CAAAtD,SAAA,EACAC,UAAA,GAAAqD,sBAAA,CAAArD,UAAA,EACAC,SAAA,GAAAoD,sBAAA,CAAApD,SAAA,EACAhO,OAAA,GAAAoR,sBAAA,CAAApR,OAAA,EACA0N,UAAA,GAAA0D,sBAAA,CAAA1D,UAAA,EACAO,QAAA,GAAAmD,sBAAA,CAAAnD,QAAA,EACArO,OAAA,GAAAwR,sBAAA,CAAAxR,OAAA,EACAiN,OAAA,GAAAuE,sBAAA,CAAAvE,MAAA,EACApM,aAAA,GAAA2Q,sBAAA,CAAA3Q,aAAA,EACAC,WAAA,GAAA0Q,sBAAA,CAAA1Q,WAAA,EACAK,QAAA,GAAAqQ,sBAAA,CAAArQ,QAAA,EACAC,iBAAA,GAAAoQ,sBAAA,CAAApQ,iBAAA,EACAC,KAAA,GAAAmQ,sBAAA,CAAAnQ,KAAA,EACAC,aAAA,GAAAkQ,sBAAA,CAAAlQ,aAAA;wBAIA0P,OAAA,CAAApR,IAAA,CAAAC,SAAA,GAAAA,SAAA;wBACAmR,OAAA,CAAApR,IAAA,CAAAc,UAAA,GAAAoG,IAAA;wBACAkK,OAAA,CAAApR,IAAA,CAAAa,WAAA,GAAA6N,WAAA;wBACA0C,OAAA,CAAApR,IAAA,CAAAS,WAAA,GAAAwL,SAAA;wBACAmF,OAAA,CAAApR,IAAA,CAAAW,YAAA,GAAAyN,YAAA;wBACAgD,OAAA,CAAApR,IAAA,CAAAgB,SAAA,GAAAA,SAAA;wBACAoQ,OAAA,CAAApR,IAAA,CAAAU,SAAA,GAAA2N,SAAA;wBACA+C,OAAA,CAAApR,IAAA,CAAAO,OAAA,GAAA+N,SAAA;wBACA8C,OAAA,CAAApR,IAAA,CAAAK,gBAAA,GAAAkO,UAAA;wBACA6C,OAAA,CAAApR,IAAA,CAAAM,MAAA,GAAAkO,SAAA;wBACA4C,OAAA,CAAApR,IAAA,CAAAQ,OAAA,GAAAA,OAAA;wBACA4Q,OAAA,CAAApR,IAAA,CAAAE,QAAA,OAAAC,IAAA,CAAAsO,QAAA;wBACA2C,OAAA,CAAApR,IAAA,CAAAI,OAAA,GAAAA,OAAA;wBACAgR,OAAA,CAAApR,IAAA,CAAAiB,aAAA,GAAAA,aAAA,IAAA/B,SAAA;wBACAkS,OAAA,CAAApR,IAAA,CAAAkB,WAAA,GAAAA,WAAA;wBACAkQ,OAAA,CAAApR,IAAA,CAAAuB,QAAA,GAAAA,QAAA;wBACA6P,OAAA,CAAApR,IAAA,CAAAwB,iBAAA,GAAAA,iBAAA;wBACA4P,OAAA,CAAApR,IAAA,CAAAyB,KAAA,GAAAA,KAAA;wBACA2P,OAAA,CAAApR,IAAA,CAAA0B,aAAA,GAAAA,aAAA;wBACA0P,OAAA,CAAArR,UAAA,GAAAsN,OAAA;wBAEA,IAAAiB,SAAA,IAAA8C,OAAA,CAAAzO,UAAA,CAAA0P,KAAA,WAAAC,CAAA;0BAAA,OAAAA,CAAA,CAAA/R,OAAA,KAAA+N,SAAA;wBAAA;0BACA8C,OAAA,CAAAzO,UAAA,CAAAoI,IAAA;4BACAxK,OAAA,EAAA+N,SAAA;4BACAjO,gBAAA,EAAAkO,UAAA;4BACAjO,MAAA,EAAAkO,SAAA;4BACA+D,MAAA,EAAAjE,SAAA,MAAAvC,MAAA,CAAAuC,SAAA,OAAAvC,MAAA,CAAAwC,UAAA,OAAAxC,MAAA,CAAAyC,SAAA;0BACA;wBACA;wBAEA,IAAAN,UAAA;0BACA4D,aAAA,GAAA5D,UAAA,CAAAsE,KAAA;0BACAV,aAAA,CAAArH,OAAA,WAAApB,IAAA;4BACA,IAAAoJ,OAAA,GACApJ,IAAA,CAAAqJ,OAAA,qBACArJ,IAAA,CAAAsJ,SAAA,IAAAtJ,IAAA,CAAAuJ,WAAA,iBACAvJ,IAAA;4BACA,IAAAwJ,QAAA,GAAAC,SAAA,CAAAL,OAAA,CAAAE,SAAA,CAAAF,OAAA,CAAAG,WAAA;4BACA,IAAAG,cAAA;4BACAA,cAAA,CAAA/K,IAAA,GAAAP,kBAAA,CAAAoL,QAAA;4BACAE,cAAA,CAAAC,GAAA,GAAAP,OAAA;4BACAM,cAAA,CAAA/E,aAAA,GAAAyE,OAAA;4BACArB,OAAA,CAAAxN,YAAA,CAAAmH,IAAA,CAAAgI,cAAA;4BACA3B,OAAA,CAAA1O,WAAA,CAAAqI,IAAA,CAAAgI,cAAA;0BACA;wBACA;wBAAA,KAEA3B,OAAA,CAAAjN,UAAA,CAAA8O,iBAAA;0BAAAhB,SAAA,CAAA9L,IAAA;0BAAA;wBAAA;wBACA4L,cAAA,GAAAX,OAAA,CAAAjN,UAAA,CAAA8O,iBAAA,CAAAT,KAAA,MAAAxB,GAAA;0BAAA,IAAAkC,KAAA,GAAAvN,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAsN,SAAAH,GAAA;4BAAA,IAAAP,OAAA;4BAAA,OAAA7M,mBAAA,GAAAG,IAAA,UAAAqN,UAAAC,SAAA;8BAAA,kBAAAA,SAAA,CAAAnN,IAAA,GAAAmN,SAAA,CAAAlN,IAAA;gCAAA;kCACAsM,OAAA,GAAAO,GAAA,CAAAR,KAAA;kCAAAa,SAAA,CAAAlN,IAAA;kCAAA,OAEAiL,OAAA,CAAAkC,SAAA,CAAAb,OAAA;gCAAA;kCAAAY,SAAA,CAAAE,EAAA,GAAAF,SAAA,CAAA3K,IAAA;kCAAA2K,SAAA,CAAAG,EAAA,GACAzV,kBAAA,CAAA0U,OAAA;kCAAA,OAAAY,SAAA,CAAAI,MAAA;oCADAT,GAAA,EAAAK,SAAA,CAAAE,EAAA;oCACAvL,IAAA,EAAAqL,SAAA,CAAAG;kCAAA;gCAAA;gCAAA;kCAAA,OAAAH,SAAA,CAAA1M,IAAA;8BAAA;4BAAA,GAAAwM,QAAA;0BAAA,CAEA;0BAAA,iBAAAO,GAAA;4BAAA,OAAAR,KAAA,CAAAS,KAAA,OAAAC,SAAA;0BAAA;wBAAA;wBAAA3B,SAAA,CAAA9L,IAAA;wBAAA,OACA0N,OAAA,CAAAC,GAAA,CAAA/B,cAAA;sBAAA;wBAAAX,OAAA,CAAA/O,cAAA,GAAA4P,SAAA,CAAAvJ,IAAA;sBAAA;wBAGA0I,OAAA,CAAA7O,UAAA,CAAAkI,OAAA,WAAApB,IAAA,EAAA0K,KAAA;0BACA,IACAvG,YAAA,GAeAnE,IAAA,CAfAmE,YAAA;4BACAJ,OAAA,GAcA/D,IAAA,CAdA+D,OAAA;4BACAwC,WAAA,GAaAvG,IAAA,CAbAuG,WAAA;4BACAtC,SAAA,GAYAjE,IAAA,CAZAiE,SAAA;4BACArG,IAAA,GAWAoC,IAAA,CAXApC,IAAA;4BACAgJ,IAAA,GAUA5G,IAAA,CAVA4G,IAAA;4BACAC,MAAA,GASA7G,IAAA,CATA6G,MAAA;4BACA8D,aAAA,GAQA3K,IAAA,CARA2K,aAAA;4BACA9M,IAAA,GAOAmC,IAAA,CAPAnC,IAAA;4BACA+M,YAAA,GAMA5K,IAAA,CANA4K,YAAA;4BACAlE,SAAA,GAKA1G,IAAA,CALA0G,SAAA;4BACA5C,gBAAA,GAIA9D,IAAA,CAJA8D,gBAAA;4BACAqC,gBAAA,GAGAnG,IAAA,CAHAmG,gBAAA;4BACAL,WAAA,GAEA9F,IAAA,CAFA8F,WAAA;4BACA+E,YAAA,GACA7K,IAAA,CADA6K,YAAA;0BAEA,IAAAvE,QAAA;4BACA9M,EAAA,EAAA2K,YAAA;4BACAuC,SAAA,EAAAA,SAAA;4BACA9I,IAAA,EAAAA,IAAA;4BACAgJ,IAAA,EAAAA,IAAA;4BACAC,MAAA,EAAAA,MAAA;4BACA8D,aAAA,EAAAA,aAAA;4BACA9M,IAAA,EAAAA,IAAA;4BACA+M,YAAA,EAAAA,YAAA;4BACAzE,gBAAA,EAAAA,gBAAA;4BACAL,WAAA,EAAAA,WAAA;4BACA/B,OAAA,EAAAA,OAAA;4BACAD,gBAAA,EAAAA,gBAAA;4BACAiC,SAAA,EAAAQ,WAAA;4BACAtC,SAAA,EAAAA,SAAA;4BACAoC,KAAA;4BACAwE,YAAA,EAAAA;0BACA;0BACA9C,OAAA,CAAAhO,MAAA,CAAA2H,IAAA,CAAA4E,QAAA;0BACAyB,OAAA,CAAAlN,iBAAA,CAAA6G,IAAA,CAAAyC,YAAA;0BACA4D,OAAA,CAAAX,WAAA,CAAAW,OAAA,CAAAhO,MAAA;wBACA;wBACAgO,OAAA,CAAAxC,QAAA,CAAAnE,OAAA,WAAAQ,OAAA,EAAAkJ,GAAA;0BACA,IAAAxE,QAAA,GAAA3K,aAAA,KAAAiG,OAAA;0BACA0E,QAAA,CAAAvC,OAAA,GAAAuC,QAAA,CAAAU,MAAA;0BACAV,QAAA,CAAAP,SAAA,GAAAO,QAAA,CAAAQ,MAAA;0BACAR,QAAA,CAAArC,SAAA,GAAA8D,OAAA,CAAA7D,YAAA,CAAAoC,QAAA;0BACAA,QAAA,CAAAyE,YAAA,GAAAvW,OAAA,CAAA8R,QAAA,CAAAV,WAAA,EAAAoF,QAAA,CAAA1E,QAAA,CAAAQ,MAAA,EAAAlO,KAAA;0BACA,IAAA0N,QAAA,CAAAS,UAAA;4BACAgB,OAAA,CAAA9N,OAAA,CAAAyH,IAAA,CAAA4E,QAAA;4BACAyB,OAAA,CAAAX,WAAA,CAAAW,OAAA,CAAA9N,OAAA;4BACA2J,OAAA,CAAAC,GAAA,iBAAAkE,OAAA,CAAA9N,OAAA;0BACA;4BACA8N,OAAA,CAAA7N,OAAA,CAAAwH,IAAA,CAAA4E,QAAA;4BACAyB,OAAA,CAAAX,WAAA,CAAAW,OAAA,CAAA7N,OAAA;4BACA0J,OAAA,CAAAC,GAAA,iBAAAkE,OAAA,CAAA7N,OAAA;0BACA;wBACA;wBAEA6N,OAAA,CAAA5O,YAAA,CAAAiI,OAAA,WAAApB,IAAA,EAAA0K,KAAA;0BACA,IACAO,KAAA,GASAjL,IAAA,CATAiL,KAAA;4BACAtF,SAAA,GAQA3F,IAAA,CARA2F,SAAA;4BACA1B,SAAA,GAOAjE,IAAA,CAPAiE,SAAA;4BACAgC,MAAA,GAMAjG,IAAA,CANAiG,MAAA;4BACAC,SAAA,GAKAlG,IAAA,CALAkG,SAAA;4BACAyE,aAAA,GAIA3K,IAAA,CAJA2K,aAAA;4BACAC,YAAA,GAGA5K,IAAA,CAHA4K,YAAA;4BACA5E,GAAA,GAEAhG,IAAA,CAFAgG,GAAA;4BACAkF,SAAA,GACAlL,IAAA,CADAkL,SAAA;0BAEA,IAAA5E,QAAA;4BACA2E,KAAA,EAAAA,KAAA;4BACAtF,SAAA,EAAAA,SAAA;4BACA1B,SAAA,EAAAA,SAAA;4BACAiC,SAAA,EAAAA,SAAA;4BACAD,MAAA,EAAAA,MAAA;4BACA0E,aAAA,EAAAA,aAAA;4BACAC,YAAA,EAAAA,YAAA;4BACAvE,KAAA;4BACAL,GAAA,EAAAA,GAAA;4BACAkF,SAAA,EAAAA;0BACA;0BACAnD,OAAA,CAAA/N,OAAA,CAAA0H,IAAA,CAAA4E,QAAA;wBACA;wBAAAsC,SAAA,CAAA9L,IAAA;wBAAA;sBAAA;wBAEAiL,OAAA,CAAA9H,QAAA;0BACAtF,OAAA,EAAAsE,GAAA,CAAAiB,OAAA;0BACA1K,IAAA;wBACA;sBAAA;sBAAA;wBAAA,OAAAoT,SAAA,CAAAtL,IAAA;oBAAA;kBAAA,GAAA+K,QAAA;gBAAA,CAEA;gBAAA,iBAAA8C,EAAA;kBAAA,OAAA/C,KAAA,CAAAkC,KAAA,OAAAC,SAAA;gBAAA;cAAA;YAAA;YAAA;cAAA,OAAArC,SAAA,CAAA5K,IAAA;UAAA;QAAA,GAAA0K,QAAA;MAAA;IACA;IACAoD,eAAA,WAAAA,gBAAAzH,GAAA;MACA,QAAAA,GAAA,CAAA0H,QAAA;IACA;IACApB,SAAA,WAAAA,UAAAN,GAAA;MAAA,OAAArN,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA8O,SAAA;QAAA,IAAAC,gBAAA,EAAAjM,IAAA;QAAA,OAAA/C,mBAAA,GAAAG,IAAA,UAAA8O,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5O,IAAA,GAAA4O,SAAA,CAAA3O,IAAA;YAAA;cAAA2O,SAAA,CAAA3O,IAAA;cAAA,OACAzI,SAAA;gBAAAsV,GAAA,EAAAA;cAAA;YAAA;cAAA4B,gBAAA,GAAAE,SAAA,CAAApM,IAAA;cAAAC,IAAA,GAAAiM,gBAAA,CAAAjM,IAAA;cAAA,OAAAmM,SAAA,CAAArB,MAAA,WACA9K,IAAA;YAAA;YAAA;cAAA,OAAAmM,SAAA,CAAAnO,IAAA;UAAA;QAAA,GAAAgO,QAAA;MAAA;IACA;IACAI,SAAA,WAAAA,UAAAC,GAAA;MAAA,IAAAC,OAAA;MACA,KAAAD,GAAA;QACA,KAAAhV,IAAA,CAAAK,gBAAA;QACA,KAAAL,IAAA,CAAAM,MAAA;QACA,KAAAN,IAAA,CAAAO,OAAA;QACA;MACA;MACA,IAAA8I,IAAA,QAAA1G,UAAA,CAAAyG,IAAA,WAAAkJ,CAAA;QAAA,OAAAA,CAAA,CAAA/R,OAAA,KAAA0U,OAAA,CAAAjV,IAAA,CAAAO,OAAA;MAAA;MACA,KAAAP,IAAA,CAAAK,gBAAA,GAAAgJ,IAAA,CAAAhJ,gBAAA;MACA,KAAAL,IAAA,CAAAM,MAAA,GAAA+I,IAAA,CAAA/I,MAAA;MACA,KAAAN,IAAA,CAAAO,OAAA,GAAA8I,IAAA,CAAA9I,OAAA;IACA;IACA2U,eAAA,WAAAA,gBAAArI,CAAA;MACA;MACA;MACA;IAAA,CACA;IACAsI,cAAA,WAAAA,eAAAtI,CAAA;MACA;IAAA,CACA;IACAU,YAAA,WAAAA,aAAAlE,IAAA;MACA,OAAAgE,MAAA,CAAAhE,IAAA,CAAA+D,OAAA,GAAA/D,IAAA,CAAA+F,SAAA,EAAAgG,OAAA;IACA;IACAC,aAAA,WAAAA,cAAA3E,IAAA;MAAA,IAAA4E,OAAA;MACArI,OAAA,CAAAC,GAAA,CAAAwD,IAAA;MACAzD,OAAA,CAAAC,GAAA,MAAAxI,WAAA;MACA,SAAAA,WAAA,KAAArG,QAAA,CAAAC,GAAA;QACA2O,OAAA,CAAAC,GAAA;QACAwD,IAAA,CAAAjG,OAAA,WAAApB,IAAA;UACAA,IAAA,CAAAiE,SAAA,GAAAgI,OAAA,CAAA/H,YAAA,CAAAlE,IAAA;UAEAiM,OAAA,CAAAlS,MAAA,CAAA2H,IAAA,CAAA1B,IAAA;QACA;QACA,KAAAjG,MAAA,GAAAmE,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAgO,SAAA,MAAAnS,MAAA;QACA,KAAAI,KAAA,QAAAJ,MAAA,CAAAiH,MAAA;MACA,gBAAA3F,WAAA,KAAArG,QAAA,CAAAG,QAAA;QACAkS,IAAA,CAAAjG,OAAA,WAAApB,IAAA;UACAA,IAAA,CAAAiE,SAAA,GAAAgI,OAAA,CAAA/H,YAAA,CAAAlE,IAAA;UACAiM,OAAA,CAAAhS,OAAA,CAAAyH,IAAA,CAAA1B,IAAA;QACA;QACA;QACA,KAAA/F,OAAA,GAAAiE,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAgO,SAAA,MAAAjS,OAAA;QACA,KAAAE,KAAA,QAAAF,OAAA,CAAA+G,MAAA;MACA,gBAAA3F,WAAA,KAAArG,QAAA,CAAAI,IAAA;QACAiS,IAAA,CAAAjG,OAAA,WAAApB,IAAA;UACAA,IAAA,CAAAiE,SAAA,GAAAgI,OAAA,CAAA/H,YAAA,CAAAlE,IAAA;UACAiM,OAAA,CAAA/R,OAAA,CAAAwH,IAAA,CAAA1B,IAAA;QACA;QACA,KAAA9F,OAAA,GAAAgE,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAgO,SAAA,MAAAhS,OAAA;QACA,KAAAC,KAAA,QAAAD,OAAA,CAAA8G,MAAA;MACA,gBAAA3F,WAAA,KAAArG,QAAA,CAAAE,OAAA;QACAmS,IAAA,CAAAjG,OAAA,WAAApB,IAAA;UACAiM,OAAA,CAAAjS,OAAA,CAAA0H,IAAA,CAAA1B,IAAA;QACA;QACA,KAAA7F,KAAA,QAAAH,OAAA,CAAAgH,MAAA;MACA;IACA;IACAmL,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,KAAAjJ,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACA7N,IAAA;MACA,GACAmK,IAAA;QACA,IAAAyM,OAAA,CAAApW,OAAA;UACAoW,OAAA,CAAA9R,UAAA,CAAA8G,OAAA,WAAApB,IAAA;YACA,IAAA0K,KAAA,GAAA0B,OAAA,CAAApS,OAAA,CAAAqS,SAAA,CACA,UAAApD,CAAA;cAAA,OAAAA,CAAA,CAAAtD,SAAA,KAAA3F,IAAA,CAAA2F,SAAA;YAAA,CACA;YACA+E,KAAA,WAAA0B,OAAA,CAAApS,OAAA,CAAAsS,MAAA,CAAA5B,KAAA;YACA,IAAA0B,OAAA,CAAA7W,MAAA;cACA,IAAAmV,MAAA,GAAA0B,OAAA,CAAAjT,YAAA,CAAAkT,SAAA,CACA,UAAApD,CAAA;gBAAA,OAAAA,CAAA,CAAAtD,SAAA,KAAA3F,IAAA,CAAA2F,SAAA;cAAA,CACA;cACA+E,MAAA,WAAA0B,OAAA,CAAAjT,YAAA,CAAAmT,MAAA,CAAA5B,MAAA;YACA;UACA;QACA;UACA,IAAA6B,MAAA;UACA,IAAAH,OAAA,CAAA/Q,WAAA,KAAArG,QAAA,CAAAC,GAAA;YACAsX,MAAA,GAAAH,OAAA,CAAArS,MAAA;YACAqS,OAAA,CAAA9R,UAAA,CAAA8G,OAAA,WAAApB,IAAA;cACA,IAAA0K,KAAA,GAAA6B,MAAA,CAAAF,SAAA,WAAApD,CAAA;gBAAA,OAAAA,CAAA,CAAA9C,gBAAA,GAAA8C,CAAA,CAAAnD,WAAA,KAAA9F,IAAA,CAAAmG,gBAAA,GAAAnG,IAAA,CAAA8F,WAAA;cAAA;cACA4E,KAAA,WAAA6B,MAAA,CAAAD,MAAA,CAAA5B,KAAA;cACA,IAAA0B,OAAA,CAAA7W,MAAA;gBACA,IAAAmV,OAAA,GAAA0B,OAAA,CAAAlT,UAAA,CAAAmT,SAAA,CACA,UAAApD,CAAA;kBAAA,OAAAA,CAAA,CAAA9E,YAAA,KAAAnE,IAAA,CAAAxG,EAAA;gBAAA,CACA;gBACAkR,OAAA,WAAA0B,OAAA,CAAAlT,UAAA,CAAAoT,MAAA,CAAA5B,OAAA;cACA;YACA;UACA,WAAA0B,OAAA,CAAA/Q,WAAA,KAAArG,QAAA,CAAAG,QAAA;YACAoX,MAAA,GAAAH,OAAA,CAAAnS,OAAA;YACAmS,OAAA,CAAA9R,UAAA,CAAA8G,OAAA,WAAApB,IAAA;cACA,IAAA0K,KAAA,GAAA6B,MAAA,CAAAF,SAAA,WAAApD,CAAA;gBAAA,OAAAA,CAAA,CAAAxC,gBAAA,KAAAzG,IAAA,CAAAyG,gBAAA;cAAA;cACAiE,KAAA,WAAA6B,MAAA,CAAAD,MAAA,CAAA5B,KAAA;YACA;UACA,WAAA0B,OAAA,CAAA/Q,WAAA,KAAArG,QAAA,CAAAI,IAAA;YACAmX,MAAA,GAAAH,OAAA,CAAAlS,OAAA;YACAkS,OAAA,CAAA9R,UAAA,CAAA8G,OAAA,WAAApB,IAAA;cACA,IAAA0K,KAAA,GAAA6B,MAAA,CAAAF,SAAA,WAAApD,CAAA;gBAAA,OAAAA,CAAA,CAAAxC,gBAAA,KAAAzG,IAAA,CAAAyG,gBAAA;cAAA;cACAiE,KAAA,WAAA6B,MAAA,CAAAD,MAAA,CAAA5B,KAAA;YACA;UACA;QACA;QACA0B,OAAA,CAAAnM,QAAA;UACAzK,IAAA;UACAmF,OAAA;QACA;MACA,GACA2I,KAAA;IACA;IACAkJ,mBAAA,WAAAA,oBAAAvD,CAAA;MACA,KAAA3O,UAAA,GAAA2O,CAAA;IACA;IACAwD,SAAA,WAAAA,UAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA,CACA;IACAtQ,QAAA,WAAAA,SAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA,CACA;IACAkB,aAAA,WAAAA,cAAA;MAAA,IAAAqP,OAAA;MACA,WAAAlC,OAAA,WAAAmC,OAAA;QACA9Y,iBAAA,KAAA8L,IAAA,WAAAV,GAAA;UACA,IAAAA,GAAA,CAAAW,SAAA;YACA8M,OAAA,CAAApT,UAAA,GAAA2F,GAAA,CAAAK,IAAA;YACAoN,OAAA,CAAApT,UAAA,CAAA8H,OAAA,WAAAQ,OAAA,EAAAkJ,GAAA;cACA4B,OAAA,CAAA5E,IAAA,CACAlG,OAAA,EACA,aAAAc,MAAA,CACAd,OAAA,CAAA1K,OAAA,OAAAwL,MAAA,CAAAd,OAAA,CAAA5K,gBAAA,OAAA0L,MAAA,CAAAd,OAAA,CAAA3K,MAAA,MACA;YACA;YACA0V,OAAA;UACA;YACAD,OAAA,CAAAzM,QAAA;cACAtF,OAAA,EAAAsE,GAAA,CAAAiB,OAAA;cACA1K,IAAA;YACA;UACA;QACA;MACA;IACA;IACAoX,UAAA,WAAAA,WAAAjX,IAAA;MACA,KAAA0H,aAAA;MACA;IACA;IACAwP,SAAA,WAAAA,UAAA;MAAA,IAAAC,OAAA;MACA,KAAAxW,gBAAA;MACA,KAAAH,KAAA;MACA,KAAAC,SAAA;MACA,KAAAK,aAAA;MACA,SAAAT,OAAA;QACA,IAAAuW,MAAA;QACA,SAAAlR,WAAA,KAAArG,QAAA,CAAAC,GAAA;UACAsX,MAAA,QAAAxS,MAAA;UACA,KAAAxD,KAAA;QACA,gBAAA8E,WAAA,KAAArG,QAAA,CAAAG,QAAA;UACAoX,MAAA,QAAAtS,OAAA;UACA,KAAA1D,KAAA;QACA,gBAAA8E,WAAA,KAAArG,QAAA,CAAAI,IAAA;UACAmX,MAAA,QAAArS,OAAA;UACA,KAAA3D,KAAA;QACA;QAEA,IAAAwW,SAAA,GAAAR,MAAA,CAAA7G,MAAA,WAAA1F,IAAA;UACA,QAAAA,IAAA,CAAAqG,KAAA;QACA;QACA,KAAA9F,SAAA,WAAAC,CAAA;UACAsM,OAAA,CAAApM,KAAA,CAAAsM,OAAA,CAAAC,IAAA,CAAAF,SAAA,EAAAR,MAAA;QACA;MACA,gBAAAvW,OAAA;QACA,KAAAO,KAAA;QACA,IAAA2W,SAAA,QAAAlT,OAAA,CAAA0L,MAAA,WAAA1F,IAAA;UACA,QAAAA,IAAA,CAAAqG,KAAA;QACA;QACA,KAAA9F,SAAA,WAAAC,CAAA;UACAsM,OAAA,CAAApM,KAAA,CAAAsM,OAAA,CAAAC,IAAA,CAAAC,SAAA,EAAAJ,OAAA,CAAA9S,OAAA;QACA;MACA;IACA;IACA;IACAmT,YAAA,WAAAA,aAAAxJ,GAAA;MAAA,IAAAyJ,OAAA;MACAxJ,OAAA,CAAAC,GAAA,CAAAF,GAAA;MACA,KAAArN,gBAAA;MACA,KAAAH,KAAA;MACA,KAAAI,KAAA;MACA,KAAAH,SAAA;MACA,KAAAK,aAAA;MACA,KAAA8J,SAAA,WAAAC,CAAA;QACA4M,OAAA,CAAA1M,KAAA,CAAAsM,OAAA,CAAAC,IAAA,CAAAtJ,GAAA,CAAAuH,SAAA,IAAAvH,GAAA,CAAAnK,EAAA;MACA;IACA;IACA6T,aAAA,WAAAA,cAAA;MACA,KAAA/W,gBAAA;MACA,KAAAC,KAAA;MACA,KAAAH,SAAA;MACA,KAAAK,aAAA;IACA;IACA6W,KAAA,WAAAA,MAAA;MACA,KAAA7W,aAAA;IACA;IACA8W,UAAA,WAAAA,WAAA5J,GAAA;MACA,KAAAjD,KAAA,CAAA8M,IAAA,CAAAC,UAAA,CAAA9J,GAAA;IACA;IACAlB,cAAA,WAAAA,eAAArD,IAAA;MAAA,IAAAsO,OAAA;MACA,WAAAlD,OAAA,WAAAmC,OAAA;QACAvY,aAAA;UACAgL,IAAA,EAAAA;QACA,GAAAO,IAAA,WAAAV,GAAA;UACA,IAAAW,SAAA,GAAAX,GAAA,CAAAW,SAAA;YAAAN,IAAA,GAAAL,GAAA,CAAAK,IAAA;YAAAY,OAAA,GAAAjB,GAAA,CAAAiB,OAAA;UACA,IAAAN,SAAA;YACA,KAAAN,IAAA;cACAoO,OAAA,CAAAzN,QAAA;gBACAtF,OAAA;gBACAnF,IAAA;cACA;cACA;YACA;YACAkY,OAAA,CAAA9T,QAAA,GAAA+T,MAAA,CAAAC,MAAA,KAAAF,OAAA,CAAA9T,QAAA,EAAA0F,IAAA,CAAAuO,IAAA;YACAH,OAAA,CAAA5T,OAAA,IACAwF,IAAA,CAAAwO,UAAA,CAAApI,MAAA,WAAAuD,CAAA;cAAA,OAAAA,CAAA,CAAA8E,UAAA;YAAA,UACApG,GAAA,WAAA3H,IAAA;cACAA,IAAA,CAAAgO,YAAA;cACAhO,IAAA,CAAAiO,WAAA;cACA,OAAAjO,IAAA;YACA;YACA0N,OAAA,CAAA/W,IAAA,CAAAmB,QAAA,CAAAG,QAAA,IAAAqH,IAAA,CAAAuO,IAAA,CAAAK,UAAA;YACA,IAAAR,OAAA,CAAAxX,KAAA;cACAwX,OAAA,CAAA9T,QAAA,CAAAuU,SAAA;cACAT,OAAA,CAAA9T,QAAA,CAAAwU,aAAA;YACA;YACAzB,OAAA,CAAAe,OAAA,CAAA5T,OAAA;UACA;YACA4T,OAAA,CAAAzN,QAAA;cACAtF,OAAA,EAAAuF,OAAA;cACA1K,IAAA;YACA;UACA;QACA;MACA;IACA;IACA6Y,aAAA,WAAAA,cAAA3J,QAAA,EAAA4J,IAAA,EAAAC,QAAA;MACA3K,OAAA,CAAAC,GAAA,aAAA0K,QAAA;MACA,KAAAlV,WAAA,GAAA6E,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAgO,SAAA,CAAAqC,QAAA;IACA;IACAC,YAAA,WAAAA,aAAAF,IAAA,EAAAC,QAAA;MACA,KAAAlV,WAAA,GAAA6E,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAgO,SAAA,CAAAqC,QAAA;IACA;IACAE,aAAA,WAAAA,cAAAH,IAAA;MAAA,OAAAhS,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkS,SAAA;QAAA,IAAA/J,aAAA;QAAA,OAAApI,mBAAA,GAAAG,IAAA,UAAAiS,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/R,IAAA,GAAA+R,SAAA,CAAA9R,IAAA;YAAA;cACA8G,OAAA,CAAAC,GAAA,SAAAyK,IAAA;cACA3J,aAAA;cAAA,MACA2J,IAAA,CAAA5J,QAAA,IAAA4J,IAAA,CAAA5J,QAAA,CAAAC,aAAA;gBAAAiK,SAAA,CAAA9R,IAAA;gBAAA;cAAA;cACA6H,aAAA,GAAA2J,IAAA,CAAA5J,QAAA,CAAAC,aAAA;cAAAiK,SAAA,CAAA9R,IAAA;cAAA;YAAA;cAAA8R,SAAA,CAAA9R,IAAA;cAAA,OAEAzI,SAAA;gBAAAsV,GAAA,EAAA2E,IAAA,CAAA3J;cAAA;YAAA;cAAAA,aAAA,GAAAiK,SAAA,CAAAvP,IAAA;cACAsF,aAAA,GAAAA,aAAA,CAAArF,IAAA;YAAA;cAEAuP,MAAA,CAAAC,IAAA,CAAAnK,aAAA;YAAA;YAAA;cAAA,OAAAiK,SAAA,CAAAtR,IAAA;UAAA;QAAA,GAAAoR,QAAA;MAAA;IACA;IACAK,YAAA,WAAAA,aAAA;MACA,KAAA9O,QAAA;QACAzK,IAAA;QACAmF,OAAA;MACA;IACA;IACAkE,eAAA,WAAAA,gBAAA;MAAA,IAAAmQ,OAAA;MAAA,OAAA1S,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAyS,SAAA;QAAA,IAAAC,UAAA,EAAAjQ,GAAA;QAAA,OAAA1C,mBAAA,GAAAG,IAAA,UAAAyS,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvS,IAAA,GAAAuS,SAAA,CAAAtS,IAAA;YAAA;cAAAsS,SAAA,CAAAvS,IAAA;cAEAqS,UAAA;gBACAG,gBAAA;gBACAC,UAAA;cACA;cACA,IAAAN,OAAA,CAAArY,IAAA,CAAAC,SAAA;gBACAsY,UAAA,CAAAI,UAAA,CAAA5N,IAAA,CAAAsN,OAAA,CAAArY,IAAA,CAAAC,SAAA;cACA;cAAAwY,SAAA,CAAAtS,IAAA;cAAA,OACAlI,eAAA,CAAAsa,UAAA;YAAA;cAAAjQ,GAAA,GAAAmQ,SAAA,CAAA/P,IAAA;cACA,IAAAJ,GAAA,CAAAW,SAAA;gBACAoP,OAAA,CAAAzT,eAAA,GAAA0D,GAAA,CAAAK,IAAA;cACA;gBACA0P,OAAA,CAAA/O,QAAA;kBACAtF,OAAA,EAAAsE,GAAA,CAAAiB,OAAA;kBACA1K,IAAA;gBACA;cACA;cAAA4Z,SAAA,CAAAtS,IAAA;cAAA;YAAA;cAAAsS,SAAA,CAAAvS,IAAA;cAAAuS,SAAA,CAAAlF,EAAA,GAAAkF,SAAA;cAEAxL,OAAA,CAAA2L,KAAA,cAAAH,SAAA,CAAAlF,EAAA;YAAA;YAAA;cAAA,OAAAkF,SAAA,CAAA9R,IAAA;UAAA;QAAA,GAAA2R,QAAA;MAAA;IAEA;EACA;AACA", "ignoreList": []}]}
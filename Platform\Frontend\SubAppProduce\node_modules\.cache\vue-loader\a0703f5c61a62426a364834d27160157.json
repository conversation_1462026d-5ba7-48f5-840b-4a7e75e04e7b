{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\AssociatedDevice.vue?vue&type=style&index=0&id=bfd02470&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\AssociatedDevice.vue", "mtime": 1757468113413}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi8vIC5jcy1kaWFsb2cgew0KLy8gICA6OnYtZGVlcCB7DQovLyAgICAgLmVsLWRpYWxvZ19fYm9keSB7DQovLyAgICAgICBwYWRkaW5nLXRvcDogMDsNCi8vICAgICB9DQovLyAgIH0NCi8vIH0NCjo6di1kZWVwIHsNCiAgLmNzLXRvcC1oZWFkZXItYm94IHsNCiAgICBsaW5lLWhlaWdodDogMHB4Ow0KICB9DQp9DQoNCjo6di1kZWVwIC5wYWdpbmF0aW9uIHsNCiAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZCAhaW1wb3J0YW50Ow0KICBtYXJnaW4tdG9wOiAxMnB4ICFpbXBvcnRhbnQ7DQoNCiAgLmVsLWlucHV0LS1zbWFsbCAuZWwtaW5wdXRfX2lubmVyIHsNCiAgICBoZWlnaHQ6IDI4cHg7DQogICAgbGluZS1oZWlnaHQ6IDI4cHg7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["AssociatedDevice.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "AssociatedDevice.vue", "sourceRoot": "src/views/PRO/process-settings/management/component", "sourcesContent": ["<template>\r\n\r\n  <div>\r\n\r\n    <el-form label-width=\"80px\" :inline=\"true\">\r\n      <el-form-item>\r\n        <el-button type=\"primary\" @click=\"handleDevice\">关联设备</el-button>\r\n      </el-form-item>\r\n      <el-form-item label=\"设备名称\" prop=\"deviceName\">\r\n        <el-input v-model.trim=\"form.deviceName\" :clearbale=\"true\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"设备类型\" prop=\"deviceType\">\r\n        <el-select v-model=\"form.deviceType\" clearable placeholder=\"请选择\" style=\"width: 100%\" @change=\"deviceTypeChange\">\r\n          <el-option v-for=\"item in deviceTypeOptions\" :key=\"item.Id\" :label=\"item.Display_Name\" :value=\"item.Id\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"设备子类\" prop=\"Type_Detail_Id\">\r\n        <el-select v-model=\"form.Type_Detail_Id\" clearable placeholder=\"请选择\" :disabled=\"!form.deviceType\"\r\n          style=\"width: 100%\">\r\n          <el-option v-for=\"item in deviceItemTypeOptions\" :key=\"item.Id\" :label=\"item.Display_Name\" :value=\"item.Id\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"所属部门\" prop=\"department\">\r\n        <el-input v-model.trim=\"form.department\" clearbale />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\r\n        <el-button @click=\"reset\">重置</el-button>\r\n      </el-form-item>\r\n\r\n    </el-form>\r\n\r\n\r\n\r\n\r\n\r\n\r\n    <div v-loading=\"tbLoading\" class=\"fff cs-z-tb-wrapper\" style=\"height: calc(100vh - 320px);\">\r\n      <dynamic-data-table ref=\"dyTable\" :key=\"tableKey\" :columns=\"columns\" :config=\"tbConfig\" :data=\"tbData\"\r\n        :page=\"queryInfo.Page\" :total=\"total\" border stripe class=\"cs-plm-dy-table\"\r\n        @multiSelectedChange=\"handleSelectionChange\" @gridPageChange=\"gridPageChange\" @gridSizeChange=\"gridSizeChange\"\r\n        @tableSearch=\"tableSearch\">\r\n        <template slot=\"Director_UserName\" slot-scope=\"{ row }\">\r\n          <div>{{ row.Director_UserName || \"-\" }}</div>\r\n        </template>\r\n        <template slot=\"Working_Team_Names\" slot-scope=\"{ row }\">\r\n          <div>{{ row.Working_Team_Names || \"-\" }}</div>\r\n        </template>\r\n\r\n      </dynamic-data-table>\r\n    </div>\r\n  </div>\r\n\r\n\r\n</template>\r\n\r\n<script>\r\nimport getTbInfo from \"@/mixins/PRO/get-table-info\";\r\nimport DynamicDataTable from \"@/components/DynamicDataTable/DynamicDataTable\";\r\nimport TopHeader from \"@/components/TopHeader\";\r\n// import detail from \"./component/detail\";\r\nimport { GetDictionaryDetailListByCode, GetDictionaryDetailListByParentId, } from '@/api/sys'\r\nimport { GetEquipmentAssetPageList, AddWorkingProcess } from '@/api/PRO/technology-lib'\r\n\r\n\r\n\r\nexport default {\r\n  name: \"PROGroup\",\r\n\r\n  components: {\r\n    DynamicDataTable,\r\n    TopHeader,\r\n    // detail,\r\n  },\r\n  mixins: [getTbInfo],\r\n  props: {\r\n    rowData: {\r\n      type: Object,\r\n      default() {\r\n        return {}\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      tbConfig: {\r\n        Pager_Align: \"center\",\r\n      },\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: 10,\r\n      },\r\n      deviceItemTypeOptions: [],\r\n      form: {\r\n        deviceName: \"\",\r\n        deviceType: \"\",\r\n        Type_Detail_Id: \"\",\r\n        department: \"\",\r\n      },\r\n      deviceTypeOptions: [],\r\n      currentComponent: \"\",\r\n      title: \"\",\r\n      columns: [],\r\n      tbData: [],\r\n      total: 0,\r\n      tableKey: Math.random(),\r\n      tbLoading: false,\r\n      dialogVisible: false,\r\n      selectList: [],\r\n      keywords: \"\",\r\n    };\r\n  },\r\n  async created() {\r\n    this.tbLoading = true;\r\n    this.selectList = []\r\n    await this.getTableConfig(\"plm_device_list\");\r\n    this.getEquipmentAssetPageList();\r\n    this.getDictionaryDetailListByCode();\r\n  },\r\n  methods: {\r\n    clearSelec() {\r\n      this.$refs.dyTable.clearSelection()\r\n    },\r\n    deviceTypeChange(e) {\r\n      this.form.Type_Detail_Id = ''\r\n      this.deviceItemTypeOptions = []\r\n      GetDictionaryDetailListByParentId(e).then((res) => {\r\n        this.deviceItemTypeOptions = res.Data;\r\n      });\r\n    },\r\n    // 获取设备类型\r\n    async getDictionaryDetailListByCode() {\r\n      await GetDictionaryDetailListByCode({ dictionaryCode: 'deviceType' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.deviceTypeOptions = res.Data || [];\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      console.log(' this.optionsGroupList', this.optionsGroupList)\r\n    },\r\n    async getEquipmentAssetPageList() {\r\n\r\n      await GetEquipmentAssetPageList({\r\n        Display_Name: this.form.deviceName,\r\n        Device_Type_Id: this.form.deviceType,\r\n        Device_Type_Detail_Id: this.form.Type_Detail_Id,\r\n        Department: this.form.department,\r\n        Page: this.queryInfo.Page,\r\n        PageSize: this.queryInfo.PageSize,\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.Data;\r\n          this.total = res.Data.TotalCount;\r\n          this.tbLoading = false;\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      console.log(' this.optionsGroupList', this.optionsGroupList)\r\n    },\r\n    gridPageChange({ page }) {\r\n      this.queryInfo.Page = Number(page);\r\n      this.getEquipmentAssetPageList();\r\n    },\r\n    gridSizeChange({ size }) {\r\n      this.queryInfo.PageSize = Number(size);\r\n      this.queryInfo.Page = 1;\r\n      this.getEquipmentAssetPageList();\r\n    },\r\n    handleDevice() {\r\n      if (this.selectList.length === 0) {\r\n        this.$message({\r\n          message: '请选择设备',\r\n          type: 'error'\r\n        })\r\n        return\r\n      } else {\r\n        \r\n        this.rowData.Device_Ids = this.selectList\r\n        console.log(this.rowData,'this.rowData');\r\n\r\n        AddWorkingProcess(this.rowData).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '关联成功',\r\n              type: 'success'\r\n            })\r\n            this.$emit(\"fetchData\");\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n\r\n    },\r\n\r\n\r\n\r\n\r\n    handleSelectionChange(list) {\r\n      this.selectList = list.map((i) => i.Id);\r\n      console.log(this.selectList, 'this.selectList')\r\n    },\r\n    handleAdd() {\r\n      this.currentComponent = \"detail\";\r\n      this.title = \"新增车间\";\r\n      this.dialogVisible = true;\r\n    },\r\n    handleEdit(row) {\r\n      this.currentComponent = \"detail\";\r\n      this.title = \"编辑车间\";\r\n      this.dialogVisible = true;\r\n      this.$nextTick((_) => {\r\n        this.$refs[\"content\"].initData(row);\r\n      });\r\n    },\r\n    // handleDetail(row) {\r\n    //   this.currentComponent = \"info\";\r\n    //   this.title = \"查看\";\r\n    //   this.dialogVisible = true;\r\n    //   this.$nextTick((_) => {\r\n    //     this.$refs[\"content\"].initData(row.Id);\r\n    //   });\r\n    // },\r\n\r\n    handleSearch() {\r\n      this.getEquipmentAssetPageList();\r\n      this.queryInfo.Page = 1;\r\n    },\r\n    reset() {\r\n      this.form = {}\r\n      this.getEquipmentAssetPageList();\r\n      this.queryInfo.Page = 1;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n// .cs-dialog {\r\n//   ::v-deep {\r\n//     .el-dialog__body {\r\n//       padding-top: 0;\r\n//     }\r\n//   }\r\n// }\r\n::v-deep {\r\n  .cs-top-header-box {\r\n    line-height: 0px;\r\n  }\r\n}\r\n\r\n::v-deep .pagination {\r\n  justify-content: flex-end !important;\r\n  margin-top: 12px !important;\r\n\r\n  .el-input--small .el-input__inner {\r\n    height: 28px;\r\n    line-height: 28px;\r\n  }\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\AssociatedDevice.vue?vue&type=style&index=0&id=bfd02470&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\AssociatedDevice.vue", "mtime": 1757485729087}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLy8gLmNzLWRpYWxvZyB7Ci8vICAgOjp2LWRlZXAgewovLyAgICAgLmVsLWRpYWxvZ19fYm9keSB7Ci8vICAgICAgIHBhZGRpbmctdG9wOiAwOwovLyAgICAgfQovLyAgIH0KLy8gfQo6OnYtZGVlcCB7CiAgLmNzLXRvcC1oZWFkZXItYm94IHsKICAgIGxpbmUtaGVpZ2h0OiAwcHg7CiAgfQp9Cgo6OnYtZGVlcCAucGFnaW5hdGlvbiB7CiAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZCAhaW1wb3J0YW50OwogIG1hcmdpbi10b3A6IDEycHggIWltcG9ydGFudDsKCiAgLmVsLWlucHV0LS1zbWFsbCAuZWwtaW5wdXRfX2lubmVyIHsKICAgIGhlaWdodDogMjhweDsKICAgIGxpbmUtaGVpZ2h0OiAyOHB4OwogIH0KfQo="}, {"version": 3, "sources": ["AssociatedDevice.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4PA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "AssociatedDevice.vue", "sourceRoot": "src/views/PRO/process-settings/management/component", "sourcesContent": ["<template>\n\n  <div>\n\n    <el-form label-width=\"80px\" :inline=\"true\">\n      <el-form-item>\n        <el-button type=\"primary\" @click=\"handleDevice\">关联设备</el-button>\n      </el-form-item>\n      <el-form-item label=\"设备名称\" prop=\"deviceName\">\n        <el-input v-model.trim=\"form.deviceName\" :clearbale=\"true\" />\n      </el-form-item>\n      <el-form-item label=\"设备类型\" prop=\"deviceType\">\n        <el-select v-model=\"form.deviceType\" clearable placeholder=\"请选择\" style=\"width: 100%\" @change=\"deviceTypeChange\">\n          <el-option v-for=\"item in deviceTypeOptions\" :key=\"item.Id\" :label=\"item.Display_Name\" :value=\"item.Id\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"设备子类\" prop=\"Type_Detail_Id\">\n        <el-select\n          v-model=\"form.Type_Detail_Id\"\n          clearable\n          placeholder=\"请选择\"\n          :disabled=\"!form.deviceType\"\n          style=\"width: 100%\"\n        >\n          <el-option v-for=\"item in deviceItemTypeOptions\" :key=\"item.Id\" :label=\"item.Display_Name\" :value=\"item.Id\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"所属部门\" prop=\"department\">\n        <el-input v-model.trim=\"form.department\" clearbale />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\n        <el-button @click=\"reset\">重置</el-button>\n      </el-form-item>\n\n    </el-form>\n\n    <div v-loading=\"tbLoading\" class=\"fff cs-z-tb-wrapper\" style=\"height: calc(100vh - 320px);\">\n      <dynamic-data-table\n        ref=\"dyTable\"\n        :key=\"tableKey\"\n        :columns=\"columns\"\n        :config=\"tbConfig\"\n        :data=\"tbData\"\n        :page=\"queryInfo.Page\"\n        :total=\"total\"\n        border\n        stripe\n        class=\"cs-plm-dy-table\"\n        @multiSelectedChange=\"handleSelectionChange\"\n        @gridPageChange=\"gridPageChange\"\n        @gridSizeChange=\"gridSizeChange\"\n        @tableSearch=\"tableSearch\"\n      >\n        <template slot=\"Director_UserName\" slot-scope=\"{ row }\">\n          <div>{{ row.Director_UserName || \"-\" }}</div>\n        </template>\n        <template slot=\"Working_Team_Names\" slot-scope=\"{ row }\">\n          <div>{{ row.Working_Team_Names || \"-\" }}</div>\n        </template>\n\n      </dynamic-data-table>\n    </div>\n  </div>\n\n</template>\n\n<script>\nimport getTbInfo from '@/mixins/PRO/get-table-info'\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'\nimport TopHeader from '@/components/TopHeader'\n// import detail from \"./component/detail\";\nimport { GetDictionaryDetailListByCode, GetDictionaryDetailListByParentId } from '@/api/sys'\nimport { GetEquipmentAssetPageList, AddWorkingProcess } from '@/api/PRO/technology-lib'\n\nexport default {\n  name: 'PROGroup',\n\n  components: {\n    DynamicDataTable,\n    TopHeader\n    // detail,\n  },\n  mixins: [getTbInfo],\n  props: {\n    rowData: {\n      type: Object,\n      default() {\n        return {}\n      }\n    }\n  },\n  data() {\n    return {\n      tbConfig: {\n        Pager_Align: 'center'\n      },\n      queryInfo: {\n        Page: 1,\n        PageSize: 10\n      },\n      deviceItemTypeOptions: [],\n      form: {\n        deviceName: '',\n        deviceType: '',\n        Type_Detail_Id: '',\n        department: ''\n      },\n      deviceTypeOptions: [],\n      currentComponent: '',\n      title: '',\n      columns: [],\n      tbData: [],\n      total: 0,\n      tableKey: Math.random(),\n      tbLoading: false,\n      dialogVisible: false,\n      selectList: [],\n      keywords: ''\n    }\n  },\n  async created() {\n    this.tbLoading = true\n    this.selectList = []\n    await this.getTableConfig('plm_device_list')\n    this.getEquipmentAssetPageList()\n    this.getDictionaryDetailListByCode()\n  },\n  methods: {\n    clearSelec() {\n      this.$refs.dyTable.clearSelection()\n    },\n    deviceTypeChange(e) {\n      this.form.Type_Detail_Id = ''\n      this.deviceItemTypeOptions = []\n      GetDictionaryDetailListByParentId(e).then((res) => {\n        this.deviceItemTypeOptions = res.Data\n      })\n    },\n    // 获取设备类型\n    async getDictionaryDetailListByCode() {\n      await GetDictionaryDetailListByCode({ dictionaryCode: 'deviceType' }).then((res) => {\n        if (res.IsSucceed) {\n          this.deviceTypeOptions = res.Data || []\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n      console.log(' this.optionsGroupList', this.optionsGroupList)\n    },\n    async getEquipmentAssetPageList() {\n      await GetEquipmentAssetPageList({\n        Display_Name: this.form.deviceName,\n        Device_Type_Id: this.form.deviceType,\n        Device_Type_Detail_Id: this.form.Type_Detail_Id,\n        Department: this.form.department,\n        Page: this.queryInfo.Page,\n        PageSize: this.queryInfo.PageSize\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data.Data\n          this.total = res.Data.TotalCount\n          this.tbLoading = false\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n      console.log(' this.optionsGroupList', this.optionsGroupList)\n    },\n    gridPageChange({ page }) {\n      this.queryInfo.Page = Number(page)\n      this.getEquipmentAssetPageList()\n    },\n    gridSizeChange({ size }) {\n      this.queryInfo.PageSize = Number(size)\n      this.queryInfo.Page = 1\n      this.getEquipmentAssetPageList()\n    },\n    handleDevice() {\n      if (this.selectList.length === 0) {\n        this.$message({\n          message: '请选择设备',\n          type: 'error'\n        })\n        return\n      } else {\n        this.rowData.Device_Ids = this.selectList\n        console.log(this.rowData, 'this.rowData')\n\n        AddWorkingProcess(this.rowData).then((res) => {\n          if (res.IsSucceed) {\n            this.$message({\n              message: '关联成功',\n              type: 'success'\n            })\n            this.$emit('fetchData')\n          } else {\n            this.$message({\n              message: res.Message,\n              type: 'error'\n            })\n          }\n        })\n      }\n    },\n\n    handleSelectionChange(list) {\n      this.selectList = list.map((i) => i.Id)\n      console.log(this.selectList, 'this.selectList')\n    },\n    handleAdd() {\n      this.currentComponent = 'detail'\n      this.title = '新增车间'\n      this.dialogVisible = true\n    },\n    handleEdit(row) {\n      this.currentComponent = 'detail'\n      this.title = '编辑车间'\n      this.dialogVisible = true\n      this.$nextTick((_) => {\n        this.$refs['content'].initData(row)\n      })\n    },\n    // handleDetail(row) {\n    //   this.currentComponent = \"info\";\n    //   this.title = \"查看\";\n    //   this.dialogVisible = true;\n    //   this.$nextTick((_) => {\n    //     this.$refs[\"content\"].initData(row.Id);\n    //   });\n    // },\n\n    handleSearch() {\n      this.getEquipmentAssetPageList()\n      this.queryInfo.Page = 1\n    },\n    reset() {\n      this.form = {}\n      this.getEquipmentAssetPageList()\n      this.queryInfo.Page = 1\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n// .cs-dialog {\n//   ::v-deep {\n//     .el-dialog__body {\n//       padding-top: 0;\n//     }\n//   }\n// }\n::v-deep {\n  .cs-top-header-box {\n    line-height: 0px;\n  }\n}\n\n::v-deep .pagination {\n  justify-content: flex-end !important;\n  margin-top: 12px !important;\n\n  .el-input--small .el-input__inner {\n    height: 28px;\n    line-height: 28px;\n  }\n}\n</style>\n"]}]}
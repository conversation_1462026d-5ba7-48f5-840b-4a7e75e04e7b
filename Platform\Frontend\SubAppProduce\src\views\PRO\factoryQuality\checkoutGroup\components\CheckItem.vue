<template>
  <div style="height: calc(100vh - 300px)">
    <vxe-table
      v-loading="tbLoading"
      :empty-render="{name: 'NotData'}"
      show-header-overflow
      element-loading-spinner="el-icon-loading"
      element-loading-text="拼命加载中"
      empty-text="暂无数据"
      height="100%"
      align="left"
      stripe
      :data="tbData"
      resizable
      :auto-resize="true"
      class="cs-vxe-table"
      :tooltip-config="{ enterable: true }"
    >
      <vxe-column
        show-overflow="tooltip"
        sortable
        field="Check_Content"
        title="检查项内容"
        width="calc(100vh-200px)/2"
      />
      <vxe-column
        show-overflow="tooltip"
        sortable
        field="Eligibility_Criteria"
        title="合格标准"
        width="calc(100vh-200px)/2"
      />
      <vxe-column fixed="right" title="操作" width="200" align="center" show-overflow>
        <template #default="{ row }">
          <el-button type="text" @click="editEvent(row)">编辑</el-button>
          <el-divider direction="vertical" />
          <el-button type="text" @click="removeEvent(row)">删除</el-button>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<script>
import { GetCheckItemList } from '@/api/PRO/factorycheck'
import { DeleteCheckItem } from '@/api/PRO/factorycheck'

export default {
  props: {
    checkType: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tbLoading: false,
      tbData: []
    }
  },
  watch: {
    checkType: {
      handler(newName, oldName) {
        this.checkType = newName
        this.getCheckItemList()
      },
      deep: true
    }
  },
  mounted() {
    this.getCheckItemList()
  },
  methods: {
    getCheckItemList() {
      this.tbLoading = true
      GetCheckItemList({ check_object_id: this.checkType.Id }).then((res) => {
        if (res.IsSucceed) {
          this.tbData = res.Data
          console.log(res.Data)
          this.tbLoading = false
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
          this.tbLoading = false
        }
      })
    },
    removeEvent(row) {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          DeleteCheckItem({ id: row.Id }).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getCheckItemList()
            } else {
              this.$message({
                type: 'error',
                message: res.Message
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    editEvent(row) {
      // 获取每行内容
      console.log('row', row)
      this.$emit('ItemEdit', row)
    }
  }
}
</script>

<style scoped>

</style>

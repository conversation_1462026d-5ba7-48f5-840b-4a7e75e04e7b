<template>
  <div>
    <div class="app-container abs100">
      <div v-loading="pageLoading" class="h100 wrapper-c parent">
        <div class="title">
          <span
            v-for="(item, index) in title"
            :key="index"
            style="cursor: pointer"
            :class="spanCurr == index ? 'clickindex' : 'index'"
            @click="handelIndex(index, item)"
          >{{ item.Display_Name }}</span>
        </div>
        <div class="detail">
          <template>
            <el-tabs
              v-model="activeName"
              type="card"
              style="width: 100%; height: 100%"
            >
              <el-tab-pane label="检查类型" name="检查类型">
                <CheckType
                  ref="checkTypeRef"
                  :check-type="checkType"
                  @optionFn="optionEdit"
                />
              </el-tab-pane>
              <el-tab-pane label="检查项" name="检查项">
                <CheckItem
                  ref="checkItemRef"
                  :check-type="checkType"
                  @ItemEdit="ItemEdit"
                />
              </el-tab-pane>
              <el-tab-pane label="检查项组合" name="检查项组合">
                <CheckCombination
                  ref="checkCombinationRef"
                  :check-type="checkType"
                  @CombinationEdit="CombinationEdit"
                />
              </el-tab-pane>
              <el-tab-pane label="质检节点配置" name="质检节点配置">
                <CheckNode
                  ref="checkNodeRef"
                  :check-type="checkType"
                  @NodeEdit="NodeEdit"
                />
              </el-tab-pane>
              <el-tab-pane v-if="isCom" label="公差配置" name="公差配置">
                <ToleranceConfig
                  ref="toleranceConfigRef"
                  :check-type="checkType"
                  @edit="addToleranceConfig"
                />
              </el-tab-pane>
              <el-button
                type="primary"
                class="addbtn"
                @click="addData"
              >新增</el-button>
            </el-tabs>
          </template>
        </div>
      </div>
    </div>
    <el-dialog
      v-if="dialogVisible"
      ref="content"
      v-el-drag-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :width="width"
      class="z-dialog"
      @close="handleClose"
    >
      <component
        :is="currentComponent"
        ref="content"
        :dialog-data="dialogData"
        @ToleranceRefresh="ToleranceRefresh"
        @refresh="handleRefresh"
        @close="handleClose"
      />
    </el-dialog>
  </div>
</template>

<script>
import CheckType from './components/CheckType' // 检查类型
import CheckCombination from './components/CheckCombination' // 检查项组合
import CheckNode from './components/CheckNode' // 质检节点配置
import CheckItem from './components/CheckItem' // 检查项
import TypeDialog from './components/Dialog/TypeDialog' // 检查类型弹窗
import ItemDialog from './components/Dialog/ItemDialog' // 检查项弹窗
import CombinationDialog from './components/Dialog/CombinationDialog' // 检查项组合弹窗
import NodeDialog from './components/Dialog/NodeDialog' // 质检节点组合弹窗
import ToleranceConfig from './components/ToleranceConfig.vue' // 公差配置
// import { GetDictionaryDetailListByCode } from '@/api/PRO/factorycheck'
import elDragDialog from '@/directive/el-drag-dialog'
import ToleranceDialog from './components/Dialog/ToleranceDialog' // 公差配置弹窗
import { GetBOMInfo } from '@/views/PRO/bom-setting/utils'
export default {
  name: 'PLMFactoryGroupList',
  directives: { elDragDialog },
  components: {
    CheckType,
    ToleranceConfig,
    CheckCombination,
    CheckNode,
    CheckItem,
    TypeDialog,
    ItemDialog,
    CombinationDialog,
    NodeDialog,
    ToleranceDialog
  },
  data() {
    return {
      spanCurr: 0,
      title: [],
      activeName: '检查类型',
      checkType: {},
      tbLoading: false,
      tbData: [],
      dialogVisible: false,
      currentComponent: '',
      dialogTitle: '',
      isCom: false,
      width: '60%',
      dialogData: {},
      pageLoading: false
    }
  },
  created() {},
  async mounted() {
    this.getCheckType()
  },
  methods: {
    async getCheckType() {
      const bomLevel = await GetBOMInfo()
      this.title = bomLevel.list
      this.checkType = bomLevel.list[0]
      this.isCom = bomLevel.list.find(v => v.Code === '-1')
      // GetDictionaryDetailListByCode({ dictionaryCode: 'Quality_Code' }).then(
      //   (res) => {
      //     if (res.IsSucceed) {
      //       this.title = res.Data // wtf
      //       this.checkType = this.title[0]// wtf
      //       this.isCom = res.Data.find(v => v.Value === '0')
      //     } else {
      //       this.$message({
      //         type: 'error',
      //         message: 'res.Message'
      //       })
      //     }
      //   }
      // )
    },
    handelIndex(index, item) {
      this.pageLoading = true
      this.isCom = item.Code === '-1'
      if (!this.isCom && this.activeName === '公差配置') {
        this.activeName = '检查类型'
      }
      this.checkType = item
      this.spanCurr = index
      setTimeout(() => {
        this.pageLoading = false
      }, 500)
    },
    addData() {
      console.log(this.activeName)
      switch (this.activeName) {
        case '检查类型':
          this.addCheckType()
          break
        case '检查项':
          this.addCheckItem()
          break
        case '检查项组合':
          this.addCheckCombination()
          break
        case '质检节点配置':
          this.addCheckNode()
          break
        case '公差配置':
          this.addToleranceConfig()
          break
        default:
          this.addCheckType()
      }
    },
    addCheckType() {
      this.width = '30%'
      this.generateComponent('新增检查类型', 'TypeDialog')
      this.$nextTick((_) => {
        this.$refs['content'].init('新增', this.checkType)
      })
    },
    editCheckType(data) {
      this.width = '30%'
      this.generateComponent('编辑检查类型', 'TypeDialog')
      this.$nextTick((_) => {
        this.$refs['content'].init('编辑', this.checkType, data)
      })
    },
    addCheckItem() {
      this.width = '30%'
      this.generateComponent('新增检查项', 'ItemDialog')
      this.$nextTick((_) => {
        this.$refs['content'].init('新增', this.checkType)
      })
    },
    editCheckItem(data) {
      this.width = '30%'
      this.generateComponent('编辑检查项', 'ItemDialog')
      this.$nextTick((_) => {
        this.$refs['content'].init('编辑', this.checkType, data)
      })
    },
    addCheckCombination() {
      this.width = '40%'
      this.generateComponent('新增检查项组合', 'CombinationDialog')
      this.$nextTick((_) => {
        this.$refs['content'].init('新增', this.checkType)
      })
    },
    editCheckCombination(data) {
      this.width = '40%'
      this.generateComponent('编辑检查项组合', 'CombinationDialog')
      this.$nextTick((_) => {
        this.$refs['content'].init('编辑', this.checkType, data)
      })
    },
    addCheckNode() {
      this.width = '45%'
      this.generateComponent('新增质检节点配置', 'NodeDialog')
      this.$nextTick((_) => {
        this.$refs['content'].init('新增', this.checkType)
      })
    },
    editCheckNode(data) {
      this.width = '45%'
      this.generateComponent('编辑质检节点配置', 'NodeDialog')
      this.$nextTick((_) => {
        this.$refs['content'].init('编辑', this.checkType, data)
      })
    },
    addToleranceConfig(data) {
      this.width = '45%'
      this.generateComponent(data?.Id ? '编辑公差配置' : '新增公差配置', 'ToleranceDialog')
      this.$nextTick((_) => {
        this.$refs['content'].init(data?.Id ? '编辑' : '新增', this.checkType.Id, data)
      })
    },
    handleRefresh() {
      switch (this.activeName) {
        case '检查类型':
          this.$refs.checkTypeRef.getCheckTypeList()
          break
        case '检查项':
          this.$refs.checkItemRef.getCheckItemList()
          break
        case '检查项组合':
          this.$refs.checkCombinationRef.getQualityList()
          break
        case '质检节点配置':
          this.$refs.checkNodeRef.getNodeList()
          break
      }
    },
    handleClose() {
      this.dialogVisible = false
    },
    generateComponent(title, component) {
      this.dialogTitle = title
      this.currentComponent = component
      this.dialogVisible = true
    },
    optionEdit(data) {
      // this.dialogData = Object.assign({},data)
      this.editCheckType(data)
    },
    ItemEdit(data) {
      this.editCheckItem(data)
    },
    CombinationEdit(data) {
      this.editCheckCombination(data)
    },
    NodeEdit(data) {
      this.editCheckNode(data)
    },
    ToleranceRefresh(data) {
      this.$refs.toleranceConfigRef.getToleranceList()
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
@import "~@/styles/variables.scss";
.wrapper-c {
  background: #fff;
  box-sizing: border-box;
  // padding: 8px 16px 0;
}
.title {
  width: 100%;
  height: 48px;
  padding: 0 16px;
  background-color: #ffffff;

  .index {
    font-size: 16px;
    line-height: 48px;
    margin-right: 16px;
    padding: 0 16px;
    display: inline-block;
    text-align: center;
    color: #999999;
  }
  .clickindex {
    border-bottom: 2px solid #298dff;
    font-size: 16px;
    line-height: 46px;
    margin-right: 16px;
    padding: 0 16px;
    display: inline-block;
    text-align: center;
    color: #298dff;
  }
}
::v-deep {
  .el-tabs {
    display: inline-block;
  }
  .el-tabs--card .el-tabs__header {
    border: 0;
    margin: 0;
  }
  .el-tabs--card > .el-tabs__header .el-tabs__nav {
    border-bottom: 1px solid #dfe4ed;
  }
  .el-tabs__content {
    margin-top: 16px !important;
  }
}
.detail {
  height: calc(100vh - 240px);
  box-sizing: border-box;
  padding: 16px;
  border-top: 16px solid #f8f8f8;
}
.addbtn {
  position: fixed;
  right: 38px;
  top: 210px;
}
.z-dialog {
  ::v-deep {
    .el-dialog__header {
      background-color: #298dff;

      .el-dialog__title,
      .el-dialog__close {
        color: #ffffff;
      }
    }

    .el-dialog__body {
      max-height: 700px;
      overflow: auto;
      @include scrollBar;

      &::-webkit-scrollbar {
        width: 8px;
      }
    }
  }
}
</style>

<template>
  <div>
    <div class="page-container">
      <div class="top-wrapper">
        <!-- <div class="title">专业模板配置：</div> -->
        <div class="info">
          <template v-if="!!majorName">
            <div class="title">当前专业：</div>
            <div class="value">{{ majorName }}</div>
          </template>
          <template v-if="!!unit">
            <div class="title">统计单位：</div>
            <div class="value">{{ unit }}</div>
          </template>
          <template v-if="!!steelUnit">
            <div class="title">构件单位：</div>
            <div class="value">{{ steelUnit }}</div>
          </template>
          <template>
            <div class="title">单位统计字段：</div>
            <div v-for="(item,index) in templateListNew" v-show="item.Code==='SteelAmount'" :key="index" style="display: flex;flex-direction: row">
              {{ item.Display_Name }}
            </div>
            <div v-for="(item,index) in templateListNew" v-show="item.Code==='SteelWeight'" :key="index+999" style="display: flex;flex-direction: row">
              *{{ item.Display_Name }}
            </div>
          </template>
        </div>
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane v-for="(item,index) in tabList" :key="comName+index" :label="item.label" :name="item.value" />

        </el-tabs>
      </div>
      <div class="content-wrapper" style="min-height: calc(100vh - 340px)">
        <div class="right-c">
          <el-row type="flex" justify="space-between">
            <div class="right-c-title">
              <div class="setting-title">{{ systemField==true ? '系统字段' : businessField==true ? '业务字段' : expandField==true ? '拓展字段' : '' }}</div>
            </div>
            <div style="display: flex;flex-direction: row">
              <span style="width:140px;font-size:12px;height:32px; line-height: 32px;display: inline-block;">字段名称：</span>
              <el-input v-model="searchVal" placeholder="请输入字段名称" clearable />
              <el-button type="primary" style="margin-left: 10px" @click="searchValue">查询</el-button>
              <el-button type="primary" :loading="loading" @click="saveModifyChangesFn">保存设置</el-button>
            </div>
          </el-row>
          <el-form label-width="120px" style="margin-top: 24px">
            <template v-for="(item,index) in templateListNew">
              <el-row v-if="item.Column_Type==0" :key="index">
                <el-col :span="6">
                  <el-form-item :label="item.Code" label-width="150px">
                    <el-input v-model="item.Display_Name" :disabled="false" style="width: 200px" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :label="'备注说明'">
                    <el-input v-model="item.Remark" disabled style="width: 200px" />
                  </el-form-item>
                </el-col>
                <el-col v-if="activeName!=activeNameApi" :span="4">
                  <el-form-item label="排序">
                    <el-input v-model="item.Sort" style="width: 100px" />
                  </el-form-item>
                </el-col>
                <el-col v-if="activeName!=activeNameApi" :span="4">
                  <el-form-item label="是否显示">
                    <el-switch
                      v-model="item.Is_Enabled"
                      active-color="#388CFF"
                      inactive-color="#EEEEEE"
                      @change="changeStatus($event,item.Id)"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
            <div v-show="businessField==true && systemField==true" class="setting-title">业务字段</div>
            <template v-for="(item,index) in templateListNew">
              <el-row v-if="item.Column_Type==2" :key="index">
                <el-col :span="6">
                  <el-form-item :label="item.Code" label-width="150px">
                    <el-input v-model="item.Display_Name" style="width: 200px" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :label="'备注说明'">
                    <el-input v-model="item.Remark" disabled style="width: 200px" />
                  </el-form-item>
                </el-col>
                <el-col v-if="activeName!=activeNameApi" :span="4">
                  <el-form-item label="排序">
                    <el-input v-model="item.Sort" style="width: 100px" />
                  </el-form-item>
                </el-col>
                <el-col v-if="activeName==activeNameApi" :span="4">
                  <el-form-item label="是否启用">
                    <el-switch
                      v-model="item.Is_Enabled"
                      active-color="#388CFF"
                      inactive-color="#EEEEEE"
                    />
                  </el-form-item>
                </el-col>
                <el-col v-else :span="4">
                  <el-form-item label="是否显示">
                    <el-switch
                      v-model="item.Is_Enabled"
                      active-color="#388CFF"
                      inactive-color="#EEEEEE"
                      @change="changeStatus($event,item.Id)"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
            <div v-show="expandField==true" class="setting-title">拓展字段</div>
            <template v-for="(item,index) in templateListNew">
              <el-row v-if="item.Column_Type==1" :key="index">
                <el-col :span="6">
                  <el-form-item :label="item.Code" label-width="150px">
                    <el-input v-model="item.Display_Name" style="width: 200px" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :label="'备注说明'">
                    <el-input v-model="item.Remark" disabled style="width: 200px" />
                  </el-form-item>
                </el-col>
                <el-col v-if="activeName!=activeNameApi" :span="4">
                  <el-form-item label="排序">
                    <el-input v-model="item.Sort" style="width: 100px" />
                  </el-form-item>
                </el-col>
                <el-col v-if="activeName==activeNameApi" :span="4">
                  <el-form-item label="是否启用">
                    <el-switch
                      v-model="item.Is_Enabled"
                      active-color="#388CFF"
                      inactive-color="#EEEEEE"
                      @change="changeStatus($event,item.Id)"
                    />
                  </el-form-item>
                </el-col>
                <el-col v-else :span="4">
                  <el-form-item label="是否显示">
                    <el-switch
                      v-model="item.Is_Enabled"
                      active-color="#388CFF"
                      inactive-color="#EEEEEE"
                      @change="changeStatus($event,item.Id)"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { GetTableSettingList, UpdateComponentPartTableSetting, UpdateColumnSetting } from '@/api/PRO/component-type'
import { closeTagView } from '@/utils'
import { GetBOMInfo } from '@/views/PRO/bom-setting/utils'
export default {
  name: 'PROComponentConfig',
  components: {
  },
  data() {
    return {
      activeNameApi: 'plm_component_field_page_list',
      activeName: 'plm_component_field_page_list',
      currentCode: 'plm_component_page_list',
      typeCode: '',
      materialCode: '',
      currentFinalTypeCode: '',
      tabPosition: 'left',
      // tabList: [
      //   {
      //     label: '构件字段维护',
      //     value: 'plm_component_field_page_list'
      //   },
      //   {
      //     label: '构件管理列表',
      //     value: 'plm_component_page_list'
      //   },
      //   {
      //     label: '构件深化清单',
      //     value: 'plm_component_detailImport'
      //   },
      //   {
      //     label: '构件模型清单',
      //     value: 'plm_component_modelImport'
      //   },
      //   {
      //     label: '生产详情列表',
      //     value: 'plm_component_produceDetail'
      //   },
      //   {
      //     label: '打包模板',
      //     value: 'plm_component_packageTemplate'
      //   },
      //   {
      //     label: '模型字段对照表',
      //     value: 'plm_component_modelField'
      //   }
      // ],
      searchVal: '',
      majorName: '',
      comName: '构件',
      unit: '',
      steelUnit: '',
      templateList: [],
      templateListNew: [],
      loading: false,
      systemField: false,
      expandField: false,
      businessField: false
    }
  },
  computed: {
    tabList() {
      return [
        {
          label: this.comName + '字段维护',
          value: 'plm_component_field_page_list'
        },
        {
          label: this.comName + '管理列表',
          value: 'plm_component_page_list'
        },
        {
          label: this.comName + '深化清单',
          value: 'plm_component_detailImport'
        },
        {
          label: this.comName + '模型清单',
          value: 'plm_component_modelImport'
        },
        {
          label: '生产详情列表',
          value: 'plm_component_produceDetail'
        },
        {
          label: '打包模板',
          value: 'plm_component_packageTemplate'
        },
        {
          label: '模型字段对照表',
          value: 'plm_component_modelField'
        }
      ]
    }
  },
  created() {
    this.typeCode = this.$route.query.typeCode || 'Steel'
    this.materialCode = this.$route.query.materialCode || 'StructuralAs'
    this.currentFinalTypeCode = this.typeCode
  },
  mounted() {
    this.majorName = this.$route.query.name || '钢结构'
    this.unit = this.$route.query.unit || 't'
    this.steelUnit = this.$route.query.steel_unit || 'kg'
    this.GetTableSettingListFn()
  },
  methods: {
    changeStatus($event, id) {
      const displayName = this.templateList.find((item) => { return item.Id == id }).Display_Name
      if (displayName == '' && $event == true) {
        this.$message({ type: 'error', message: '请先填写字段名' })
        this.templateList.map((item) => {
          if (item.Id == id) {
            item.Is_Enabled = false
          }
          return item
        })
      }
    },
    handleClick(tab, event) {
      this.currentCode = tab.name
      this.GetTableSettingListFn()
    },

    searchValue() {
      if (!this.searchVal) {
        this.templateListNew = this.templateList
      } else {
        const filterList = []
        this.templateList.map(item => {
          if (item.Display_Name.search(new RegExp(this.searchVal, 'ig')) !== -1) {
            filterList.push(item)
          }
        })
        this.templateListNew = filterList
      }
    },

    saveModifyChangesFn() {
      if (this.activeName == this.activeNameApi) {
        this.UpdateComponentPartTableSetting()
      } else {
        this.UpdateColumnSetting()
      }
    },

    async UpdateColumnSetting() {
      this.loading = true
      const res = await UpdateColumnSetting(this.templateList)
      this.loading = false
      if (res.IsSucceed) {
        this.$message({ type: 'success', message: '保存成功' })
      } else {
        this.$message({ type: 'error', message: res.Message })
      }
    },

    async UpdateComponentPartTableSetting() {
      this.loading = true
      const res = await UpdateComponentPartTableSetting(this.templateList)
      this.loading = false
      if (res.IsSucceed) {
        this.$message({ type: 'success', message: '保存成功' })
      } else {
        this.$message({ type: 'error', message: res.Message })
      }
    },

    async GetTableSettingListFn() {
      let data = {}
      if (this.activeName == this.activeNameApi) {
        data = { IsComponent: true, ProfessionalCode: this.currentFinalTypeCode }
      } else {
        data = { IsComponent: true, ProfessionalCode: this.currentFinalTypeCode, TypeCode: this.currentCode + ',' + this.currentFinalTypeCode }
      }
      const { comName } = await GetBOMInfo()
      this.comName = comName
      const res = await GetTableSettingList(data)
      if (res.IsSucceed) {
        this.templateList = res.Data || []
        if (this.templateList.length > 0) {
          this.templateList.forEach(v => {
            if (v.Code === 'SteelName') {
              v.Display_Name = comName
            }
          })
        }
        this.templateListNew = this.templateList
        this.systemField = this.templateList.some(item => { return item.Column_Type == 0 })
        this.expandField = this.templateList.some(item => { return item.Column_Type == 1 })
        this.businessField = this.templateList.some(item => { return item.Column_Type == 2 })
      } else {
        this.$message({ type: 'error', message: res.Message })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  .page-container{
    margin:16px;
    box-sizing: border-box;
    .top-wrapper{
      background: #fff;
      padding:16px;
      box-sizing: border-box;
      .title{
        font-size: 16px;
        font-weight: 500;
        color:#333333;
      }
      .info{
        font-size: 14px;
        margin:8px 0 24px 0;
        display: flex;
        flex-direction: row;
        .title{
          font-size: 14px;
          color: #999999;
        }
        .value{
          color: #333333;
          margin-right: 24px;
        }
      }
    }
    .content-wrapper{
      margin-top:16px;
      display: flex;
      flex-direction: row;
      .left-c{
        width: 160px;
        background: #fff;
        margin-right: 16px;
      }
      .right-c{
        background: #fff;
        width: 100%;
        padding: 16px 24px;
        box-sizing: border-box;
        .right-c-title .setting-title {
          margin: 0;
        }
      }
    }
  }
  // ::v-deep.el-tabs--left .el-tabs__nav-wrap.is-left::after{
  //   left:0
  // }
  // ::v-deep.el-tabs--left .el-tabs__active-bar.is-left{
  //   left: 0;
  // }
  .setting-title {
    font-weight: 400;
    color: #1f2f3d;
    margin: 30px 0 20px;
    font-size: 22px;
  }
</style>

<template>
  <div style="padding: 16px 0;">
    <el-form
      ref="form"
      :model="form"
      inline
      :rules="rules"
      label-width="130px"
      style="width: 100%"
    >
      <h3>基本信息</h3>
      <el-row>
        <el-col :span="8">
          <el-form-item label="班组名称：">
            {{ form.Name || "-" }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="班组长：">
            {{ form.Manager_UserName || "-" }}
          </el-form-item>
        </el-col>
        <!-- <el-form-item label="负荷提醒线：">
        {{ form.Load || "-" }}
      </el-form-item> -->
        <el-col :span="8">
          <el-form-item label="排序号：">
            {{ form.Sort || "-" }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="班组月均负荷(t)：">
            {{ form.Month_Avg_Load || "-" }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否外协：">
            {{ form.Is_Outsource === true ? "是" : "否" }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否启用：">
            {{ form. Is_Enabled === true ? "是" : "否" }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="16">
          <el-form-item label="关联仓库/库位：">
            {{ form.Warehouse_Name ? form.Warehouse_Name + '/' + form.Location_Name : "-" }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item v-if="Is_Workshop_Enabled" label="所属车间：">
            {{ form.Workshop_Name || "-" }}
          </el-form-item>
        </el-col></el-row>
      <h3>班组成员</h3>
      <div class="tag-x">
        <div class="tag-wrapper">
          <el-tag
            v-for="tag in tags"
            :key="tag.User_Id"
            size="large"
            type="info"
          >
            {{ tag.User_Name }}
          </el-tag>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
import { GetWorkingTeamInfo } from '@/api/PRO/technology-lib'

export default {
  data() {
    return {
      tags: [],
      form: {
        Name: '',
        Manager_UserName: '',
        Manager_UserId: '',
        Load: '',
        Workshop_Name: '',
        Month_Avg_Load: null,
        Sort: 0,
        Is_Outsource: false,
        Is_Enabled: true,
        Warehouse_Id: '',
        Location_Id: '',
        Warehouse_Name: '',
        Location_Name: ''
      },
      rules: {},

      Is_Workshop_Enabled: ''
    }
  },
  created() {
  },
  methods: {
    initData(row, Is_Workshop_Enabled) {
      GetWorkingTeamInfo({
        id: row.Id
      }).then((res) => {
        if (res.IsSucceed) {
          const { Manager_UserName, Manager_UserId, Load, Name, Users, Month_Avg_Load, Sort, Is_Outsource, Is_Enabled, Warehouse_Name, Location_Name } =
            res.Data
          this.form.Manager_UserName = Manager_UserName
          this.form.Manager_UserId = Manager_UserId
          this.form.Load = Load
          this.form.Name = Name
          this.tags = Users
          this.form.Workshop_Name = row.Workshop_Name
          this.Is_Workshop_Enabled = Is_Workshop_Enabled
          this.form.Month_Avg_Load = Month_Avg_Load
          this.form.Sort = Sort
          this.form.Is_Outsource = Is_Outsource
          this.form.Is_Enabled = Is_Enabled
          this.form.Warehouse_Name = Warehouse_Name
          this.form.Location_Name = Location_Name
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import "~@/styles/mixin.scss";

h3 {
  color: #298dff;
}
.tag-x {
  text-align: left;
  .tag-wrapper {
    display: inline-block;
    flex-wrap: wrap;
    height: 160px;
    overflow: auto;
    @include scrollBar;
    .el-tag {
      margin: 8px 0 0 8px;
    }
  }
}
</style>

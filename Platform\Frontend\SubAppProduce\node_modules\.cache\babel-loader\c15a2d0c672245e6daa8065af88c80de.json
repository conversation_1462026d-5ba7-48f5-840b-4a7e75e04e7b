{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\part-list\\v4\\component\\BatchEditor.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\part-list\\v4\\component\\BatchEditor.vue", "mtime": 1758266753107}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["EditPartpagelist", "v4", "uuidv4", "convertCode", "GetGridByCode", "GetUserableAttr", "props", "typeEntity", "type", "Object", "default", "AreaId", "String", "ProjectId", "data", "btnLoading", "treeParams", "filterable", "children", "label", "value", "options", "key", "list", "id", "val", "undefined", "Is_Main_Data", "Name", "Id", "mounted", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "codeArr", "columns", "wrap", "_callee$", "_context", "prev", "next", "getUserableAttr", "filter", "item", "index", "map", "i", "Code", "sent", "console", "log", "_columns$filter$find", "v", "Is_Display", "find", "Display_Name", "_toConsumableArray", "stop", "methods", "_this2", "_callee2", "_callee2$", "_context2", "IsComponent", "Bom_Level", "then", "res", "IsSucceed", "resData", "Data", "expandData", "for<PERSON>ach", "expandJson", "lable", "push", "concat", "$message", "message", "Message", "init", "columnsOption", "selectList", "arr", "Component_Code", "length", "handleAdd", "handleDelete", "splice", "onSubmit", "_this3", "_callee3", "Keysmodel", "obj", "element", "_callee3$", "_context3", "abrupt", "code", "Ids", "Part_Aggregate_Id", "toString", "Area_Id", "Project_Id", "$emit", "finally", "filterOption", "currentValue", "_this4", "k", "includes", "checkType", "getColumnConfiguration", "_arguments", "arguments", "_callee4", "mainType", "_callee4$", "_context4", "ColumnList", "typeCode", "_arguments2", "_this5", "_callee5", "propsArr", "_callee5$", "_context5", "toLowerCase"], "sources": ["src/views/PRO/part-list/v4/component/BatchEditor.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-row v-for=\"(info, index) in list\" :key=\"info.id\" class=\"item-x\">\r\n      <div class=\"item\">\r\n        <label>\r\n          属性名称\r\n          <el-select\r\n            v-model=\"info.key\"\r\n            style=\"width: calc(100% - 65px)\"\r\n            clearable\r\n            placeholder=\"请选择\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in filterOption(info.key)\"\r\n              :key=\"item.key\"\r\n              :label=\"item.label\"\r\n              :value=\"item.key\"\r\n            />\r\n          </el-select>\r\n        </label>\r\n      </div>\r\n      <div class=\"item\" style=\"line-height: 32px\">\r\n        <label>请输入值\r\n          <el-input-number\r\n            v-if=\"checkType(info.key, 'number')\"\r\n            v-model=\"info.val\"\r\n            :min=\"0\"\r\n            class=\"cs-number-btn-hidden\"\r\n          />\r\n          <el-input v-if=\"checkType(info.key, 'string')\" v-model=\"info.val\" />\r\n          <el-select\r\n            v-if=\"checkType(info.key, 'array') && info.key === 'Is_Main'\"\r\n            v-model=\"info.val\"\r\n            clearable\r\n            placeholder=\"请选择\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in Is_Main_Data\"\r\n              :key=\"item.Id\"\r\n              :label=\"item.Name\"\r\n              :value=\"item.Name\"\r\n            />\r\n          </el-select>\r\n          <!-- <el-tree-select\r\n            v-show=\"checkType(info.key, 'array') && info.key === 'AreaPosition'\"\r\n            ref=\"treeSelect\"\r\n            v-model=\"info.val\"\r\n            :tree-params=\"treeParams\"\r\n            style=\"width: 100%; display: inline-block\"\r\n          /> -->\r\n        </label>\r\n      </div>\r\n      <span v-if=\"index === 0\" class=\"item-span\">\r\n        <i class=\"el-icon-circle-plus-outline\" @click=\"handleAdd\" />\r\n      </span>\r\n      <span v-else class=\"item-span\">\r\n        <i class=\"el-icon-circle-plus-outline\" @click=\"handleAdd\" />\r\n        <i\r\n          class=\"el-icon-remove-outline txt-red\"\r\n          @click=\"handleDelete(index)\"\r\n        />\r\n      </span>\r\n    </el-row>\r\n    <div style=\"text-align: right; width: 100%; padding: 20px 2% 0 0\">\r\n      <el-button @click=\"$emit('close')\">取消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"onSubmit\">确定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { EditPartpagelist } from '@/api/plm/production'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { convertCode } from '@/utils/multi-specialty'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { GetUserableAttr } from '@/api/PRO/professionalType'\r\nexport default {\r\n  props: {\r\n    typeEntity: {\r\n      type: Object,\r\n      default: () => {}\r\n    },\r\n    AreaId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    ProjectId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      treeParams: {\r\n        'default-expand-all': true,\r\n        filterable: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      value: '',\r\n      options: [\r\n        {\r\n          key: 'Spec',\r\n          label: '规格',\r\n          type: 'string'\r\n        },\r\n        {\r\n          key: 'Length',\r\n          label: '长度',\r\n          type: 'number'\r\n        },\r\n        {\r\n          key: 'Texture',\r\n          label: '材质',\r\n          type: 'string'\r\n        },\r\n        // {\r\n        //   key: 'Num',\r\n        //   label: '深化数量',\r\n        //   type: 'number'\r\n        // },\r\n        {\r\n          key: 'Weight',\r\n          label: '单重',\r\n          type: 'number'\r\n        },\r\n        {\r\n          key: 'Shape',\r\n          label: '形状',\r\n          type: 'string'\r\n        },\r\n        // {\r\n        //   key: \"Component_Code\",\r\n        //   label: \"所属构件 \",\r\n        //   type: \"string\",\r\n        // },\r\n        {\r\n          key: 'Is_Main',\r\n          label: '是否主零件',\r\n          type: 'array'\r\n        },\r\n        {\r\n          key: 'Times',\r\n          label: '单数',\r\n          type: 'number'\r\n        },\r\n        {\r\n          key: 'Remark',\r\n          label: '备注',\r\n          type: 'string'\r\n        }\r\n      ],\r\n      list: [\r\n        {\r\n          id: uuidv4(),\r\n          val: undefined,\r\n          key: ''\r\n        }\r\n      ],\r\n      Is_Main_Data: [{ Name: '是', Id: true }, { Name: '否', Id: false }]\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.getUserableAttr()\r\n    const codeArr = this.options.filter((item, index) => index).map(i => i.key)\r\n    const columns = await this.convertCode(\r\n      this.typeEntity.Code,\r\n      codeArr,\r\n      'plm_parts_page_list'\r\n    )\r\n    console.log(columns)\r\n    this.options = this.options.map((item, index) => {\r\n      if (index) {\r\n        item.label = columns.filter((v) => v.Is_Display).find((i) => i.Code === item.key)?.Display_Name\r\n      }\r\n      return item\r\n    })\r\n\r\n    this.options = [...this.options]\r\n    console.log({ columns })\r\n    console.log(this.AreaId)\r\n  },\r\n  methods: {\r\n    // 获取拓展字段\r\n    async getUserableAttr() {\r\n      await GetUserableAttr({\r\n        IsComponent: false,\r\n        Bom_Level: 0\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const resData = res.Data\r\n          const expandData = []\r\n          resData.forEach(item => {\r\n            const expandJson = {}\r\n            expandJson.key = item.Code\r\n            expandJson.lable = item.Display_Name\r\n            expandJson.type = 'string'\r\n            expandData.push(expandJson)\r\n          })\r\n          this.options = this.options.concat(expandData)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    init(list, columnsOption) {\r\n      this.selectList = list\r\n      console.log(list)\r\n      const arr = list.filter(item => item.Component_Code !== null && item.Component_Code !== '')\r\n      console.log(arr)\r\n      this.options = arr.length > 0 ? this.options.filter(v => v.key != 'Num') : this.options\r\n      //  let filterarr = columnsOption.filter(v=> {\r\n      //   return v.Display_Name != \"项目名称\" && v.Display_Name != \"区域\" && v.Display_Name != \"批次\" && v.Code != \"Total_Weight\" &&  v.Code != \"Code\" && v.Code != \"Times\" && v.Code != \"Schduling_Count\"\r\n      //  })\r\n      //  this.options = filterarr?.map(item => ({ key: item.Code, label: item.Display_Name, type: item.Code === \"Is_Main\"?\"array\": item.Code === \"Num\" || item.Code === \"Schduling_Count\" || item.Code === \"Weight\" || item.Code === \"Times\" || item.Code === \"Length\" ? \"number\" : \"string\"}))\r\n    },\r\n    handleAdd() {\r\n      this.list.push({\r\n        id: uuidv4(),\r\n        val: undefined,\r\n        key: ''\r\n      })\r\n    },\r\n    handleDelete(index) {\r\n      this.list.splice(index, 1)\r\n    },\r\n    async onSubmit() {\r\n      this.btnLoading = true\r\n      const Keysmodel = []\r\n      for (let i = 0; i < this.list.length; i++) {\r\n        console.log(this.list)\r\n        const obj = {}\r\n        const element = this.list[i]\r\n        console.log(element)\r\n        if (!element.val) {\r\n          if (element.key === 'Length' || element.key === 'Num' || element.key === 'Weight' || element.key === 'Times') {\r\n            element.val === 0 ? this.$message({ message: '值不能为0', type: 'warning' }) : this.$message({ message: '值不能为空', type: 'warning' })\r\n          } else {\r\n            this.$message({\r\n              message: '值不能为空',\r\n              type: 'warning'\r\n            })\r\n          }\r\n          this.btnLoading = false\r\n          return\r\n        }\r\n        obj.code = element.key\r\n        obj.value = element.val\r\n        Keysmodel.push(obj)\r\n        console.log(Keysmodel)\r\n      }\r\n      await EditPartpagelist({\r\n        Ids: this.selectList.map((v) => v.Part_Aggregate_Id).toString(),\r\n        Keysmodel,\r\n        Area_Id: this.AreaId,\r\n        Project_Id: this.ProjectId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '修改成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('close')\r\n          this.$emit('refresh')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.btnLoading = false\r\n      })\r\n    },\r\n    filterOption(currentValue) {\r\n      console.log(currentValue)\r\n      return this.options.filter((k) => {\r\n        return (\r\n          (!this.list.map((v) => v.key).includes(k.key) ||\r\n            k.key === currentValue) &&\r\n          k.label\r\n        )\r\n      })\r\n    },\r\n\r\n    checkType(key, type) {\r\n      if (!key) return false\r\n      return this.options.find((v) => v.key === key).type === type\r\n    },\r\n\r\n    // 获取配置数据\r\n    async getColumnConfiguration(code, mainType = 'plm_parts_page_list') {\r\n      const res = await GetGridByCode({ code: mainType + ',' + code })\r\n      return res.Data.ColumnList\r\n    },\r\n\r\n    // 根据Code（数据）获取名称\r\n    async convertCode(typeCode, propsArr = [], mainType) {\r\n      const props = await this.getColumnConfiguration(typeCode, mainType)\r\n      console.log(props)\r\n      const columns = props.filter(i => {\r\n        const arr = propsArr.map(i => i.toLowerCase())\r\n        return arr.includes(i.Code.toLowerCase())\r\n      })\r\n      console.log(columns)\r\n      return columns\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n[class^=\"el-icon\"] {\r\n  font-size: 24px;\r\n  vertical-align: middle;\r\n  cursor: pointer;\r\n  margin-left: 15px;\r\n}\r\n\r\n.item-x {\r\n  display: flex;\r\n  margin-bottom: 20px;\r\n  flex: 0 1 50%;\r\n  justify-content: space-between;\r\n\r\n  .item {\r\n    width: 45%;\r\n    white-space: nowrap;\r\n    &:not(:first-of-type) {\r\n      margin-left: 20px;\r\n      .cs-number-btn-hidden,\r\n      .el-input,\r\n      .el-select {\r\n        width: 80%;\r\n      }\r\n    }\r\n  }\r\n\r\n  .item-span {\r\n    width: 90px;\r\n    padding-top: 5px;\r\n  }\r\n}\r\n::v-deep {\r\n  .el-tree-select-input {\r\n    width: 80% !important;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuEA,SAAAA,gBAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AACA,SAAAC,WAAA;AACA,SAAAC,aAAA;AACA,SAAAC,eAAA;AACA;EACAC,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;IACA;IACAC,MAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACAG,SAAA;MACAL,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,UAAA;QACA;QACAC,UAAA;QACAH,IAAA;QACAR,KAAA;UACAY,QAAA;UACAC,KAAA;UACAC,KAAA;QACA;MACA;MACAA,KAAA;MACAC,OAAA,GACA;QACAC,GAAA;QACAH,KAAA;QACAX,IAAA;MACA,GACA;QACAc,GAAA;QACAH,KAAA;QACAX,IAAA;MACA,GACA;QACAc,GAAA;QACAH,KAAA;QACAX,IAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAc,GAAA;QACAH,KAAA;QACAX,IAAA;MACA,GACA;QACAc,GAAA;QACAH,KAAA;QACAX,IAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAc,GAAA;QACAH,KAAA;QACAX,IAAA;MACA,GACA;QACAc,GAAA;QACAH,KAAA;QACAX,IAAA;MACA,GACA;QACAc,GAAA;QACAH,KAAA;QACAX,IAAA;MACA,EACA;MACAe,IAAA,GACA;QACAC,EAAA,EAAAtB,MAAA;QACAuB,GAAA,EAAAC,SAAA;QACAJ,GAAA;MACA,EACA;MACAK,YAAA;QAAAC,IAAA;QAAAC,EAAA;MAAA;QAAAD,IAAA;QAAAC,EAAA;MAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAAC,OAAA,EAAAC,OAAA;MAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACAX,KAAA,CAAAY,eAAA;UAAA;YACAP,OAAA,GAAAL,KAAA,CAAAV,OAAA,CAAAuB,MAAA,WAAAC,IAAA,EAAAC,KAAA;cAAA,OAAAA,KAAA;YAAA,GAAAC,GAAA,WAAAC,CAAA;cAAA,OAAAA,CAAA,CAAA1B,GAAA;YAAA;YAAAkB,QAAA,CAAAE,IAAA;YAAA,OACAX,KAAA,CAAA5B,WAAA,CACA4B,KAAA,CAAAxB,UAAA,CAAA0C,IAAA,EACAb,OAAA,EACA,qBACA;UAAA;YAJAC,OAAA,GAAAG,QAAA,CAAAU,IAAA;YAKAC,OAAA,CAAAC,GAAA,CAAAf,OAAA;YACAN,KAAA,CAAAV,OAAA,GAAAU,KAAA,CAAAV,OAAA,CAAA0B,GAAA,WAAAF,IAAA,EAAAC,KAAA;cACA,IAAAA,KAAA;gBAAA,IAAAO,oBAAA;gBACAR,IAAA,CAAA1B,KAAA,IAAAkC,oBAAA,GAAAhB,OAAA,CAAAO,MAAA,WAAAU,CAAA;kBAAA,OAAAA,CAAA,CAAAC,UAAA;gBAAA,GAAAC,IAAA,WAAAR,CAAA;kBAAA,OAAAA,CAAA,CAAAC,IAAA,KAAAJ,IAAA,CAAAvB,GAAA;gBAAA,gBAAA+B,oBAAA,uBAAAA,oBAAA,CAAAI,YAAA;cACA;cACA,OAAAZ,IAAA;YACA;YAEAd,KAAA,CAAAV,OAAA,GAAAqC,kBAAA,CAAA3B,KAAA,CAAAV,OAAA;YACA8B,OAAA,CAAAC,GAAA;cAAAf,OAAA,EAAAA;YAAA;YACAc,OAAA,CAAAC,GAAA,CAAArB,KAAA,CAAApB,MAAA;UAAA;UAAA;YAAA,OAAA6B,QAAA,CAAAmB,IAAA;QAAA;MAAA,GAAAxB,OAAA;IAAA;EACA;EACAyB,OAAA;IACA;IACAjB,eAAA,WAAAA,gBAAA;MAAA,IAAAkB,MAAA;MAAA,OAAA7B,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA4B,SAAA;QAAA,OAAA7B,mBAAA,GAAAK,IAAA,UAAAyB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvB,IAAA,GAAAuB,SAAA,CAAAtB,IAAA;YAAA;cAAAsB,SAAA,CAAAtB,IAAA;cAAA,OACArC,eAAA;gBACA4D,WAAA;gBACAC,SAAA;cACA,GAAAC,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACA,IAAAC,OAAA,GAAAF,GAAA,CAAAG,IAAA;kBACA,IAAAC,UAAA;kBACAF,OAAA,CAAAG,OAAA,WAAA5B,IAAA;oBACA,IAAA6B,UAAA;oBACAA,UAAA,CAAApD,GAAA,GAAAuB,IAAA,CAAAI,IAAA;oBACAyB,UAAA,CAAAC,KAAA,GAAA9B,IAAA,CAAAY,YAAA;oBACAiB,UAAA,CAAAlE,IAAA;oBACAgE,UAAA,CAAAI,IAAA,CAAAF,UAAA;kBACA;kBACAb,MAAA,CAAAxC,OAAA,GAAAwC,MAAA,CAAAxC,OAAA,CAAAwD,MAAA,CAAAL,UAAA;gBACA;kBACAX,MAAA,CAAAiB,QAAA;oBACAC,OAAA,EAAAX,GAAA,CAAAY,OAAA;oBACAxE,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAwD,SAAA,CAAAL,IAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IACA;IACAmB,IAAA,WAAAA,KAAA1D,IAAA,EAAA2D,aAAA;MACA,KAAAC,UAAA,GAAA5D,IAAA;MACA4B,OAAA,CAAAC,GAAA,CAAA7B,IAAA;MACA,IAAA6D,GAAA,GAAA7D,IAAA,CAAAqB,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAwC,cAAA,aAAAxC,IAAA,CAAAwC,cAAA;MAAA;MACAlC,OAAA,CAAAC,GAAA,CAAAgC,GAAA;MACA,KAAA/D,OAAA,GAAA+D,GAAA,CAAAE,MAAA,YAAAjE,OAAA,CAAAuB,MAAA,WAAAU,CAAA;QAAA,OAAAA,CAAA,CAAAhC,GAAA;MAAA,UAAAD,OAAA;MACA;MACA;MACA;MACA;IACA;IACAkE,SAAA,WAAAA,UAAA;MACA,KAAAhE,IAAA,CAAAqD,IAAA;QACApD,EAAA,EAAAtB,MAAA;QACAuB,GAAA,EAAAC,SAAA;QACAJ,GAAA;MACA;IACA;IACAkE,YAAA,WAAAA,aAAA1C,KAAA;MACA,KAAAvB,IAAA,CAAAkE,MAAA,CAAA3C,KAAA;IACA;IACA4C,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MAAA,OAAA3D,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA0D,SAAA;QAAA,IAAAC,SAAA,EAAA7C,CAAA,EAAA8C,GAAA,EAAAC,OAAA;QAAA,OAAA9D,mBAAA,GAAAK,IAAA,UAAA0D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxD,IAAA,GAAAwD,SAAA,CAAAvD,IAAA;YAAA;cACAiD,MAAA,CAAA5E,UAAA;cACA8E,SAAA;cACA7C,CAAA;YAAA;cAAA,MAAAA,CAAA,GAAA2C,MAAA,CAAApE,IAAA,CAAA+D,MAAA;gBAAAW,SAAA,CAAAvD,IAAA;gBAAA;cAAA;cACAS,OAAA,CAAAC,GAAA,CAAAuC,MAAA,CAAApE,IAAA;cACAuE,GAAA;cACAC,OAAA,GAAAJ,MAAA,CAAApE,IAAA,CAAAyB,CAAA;cACAG,OAAA,CAAAC,GAAA,CAAA2C,OAAA;cAAA,IACAA,OAAA,CAAAtE,GAAA;gBAAAwE,SAAA,CAAAvD,IAAA;gBAAA;cAAA;cACA,IAAAqD,OAAA,CAAAzE,GAAA,iBAAAyE,OAAA,CAAAzE,GAAA,cAAAyE,OAAA,CAAAzE,GAAA,iBAAAyE,OAAA,CAAAzE,GAAA;gBACAyE,OAAA,CAAAtE,GAAA,SAAAkE,MAAA,CAAAb,QAAA;kBAAAC,OAAA;kBAAAvE,IAAA;gBAAA,KAAAmF,MAAA,CAAAb,QAAA;kBAAAC,OAAA;kBAAAvE,IAAA;gBAAA;cACA;gBACAmF,MAAA,CAAAb,QAAA;kBACAC,OAAA;kBACAvE,IAAA;gBACA;cACA;cACAmF,MAAA,CAAA5E,UAAA;cAAA,OAAAkF,SAAA,CAAAC,MAAA;YAAA;cAGAJ,GAAA,CAAAK,IAAA,GAAAJ,OAAA,CAAAzE,GAAA;cACAwE,GAAA,CAAA1E,KAAA,GAAA2E,OAAA,CAAAtE,GAAA;cACAoE,SAAA,CAAAjB,IAAA,CAAAkB,GAAA;cACA3C,OAAA,CAAAC,GAAA,CAAAyC,SAAA;YAAA;cApBA7C,CAAA;cAAAiD,SAAA,CAAAvD,IAAA;cAAA;YAAA;cAAAuD,SAAA,CAAAvD,IAAA;cAAA,OAsBA1C,gBAAA;gBACAoG,GAAA,EAAAT,MAAA,CAAAR,UAAA,CAAApC,GAAA,WAAAO,CAAA;kBAAA,OAAAA,CAAA,CAAA+C,iBAAA;gBAAA,GAAAC,QAAA;gBACAT,SAAA,EAAAA,SAAA;gBACAU,OAAA,EAAAZ,MAAA,CAAAhF,MAAA;gBACA6F,UAAA,EAAAb,MAAA,CAAA9E;cACA,GAAAsD,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAsB,MAAA,CAAAb,QAAA;oBACAC,OAAA;oBACAvE,IAAA;kBACA;kBACAmF,MAAA,CAAAc,KAAA;kBACAd,MAAA,CAAAc,KAAA;gBACA;kBACAd,MAAA,CAAAb,QAAA;oBACAC,OAAA,EAAAX,GAAA,CAAAY,OAAA;oBACAxE,IAAA;kBACA;gBACA;cACA,GAAAkG,OAAA;gBACAf,MAAA,CAAA5E,UAAA;cACA;YAAA;YAAA;cAAA,OAAAkF,SAAA,CAAAtC,IAAA;UAAA;QAAA,GAAAiC,QAAA;MAAA;IACA;IACAe,YAAA,WAAAA,aAAAC,YAAA;MAAA,IAAAC,MAAA;MACA1D,OAAA,CAAAC,GAAA,CAAAwD,YAAA;MACA,YAAAvF,OAAA,CAAAuB,MAAA,WAAAkE,CAAA;QACA,OACA,EAAAD,MAAA,CAAAtF,IAAA,CAAAwB,GAAA,WAAAO,CAAA;UAAA,OAAAA,CAAA,CAAAhC,GAAA;QAAA,GAAAyF,QAAA,CAAAD,CAAA,CAAAxF,GAAA,KACAwF,CAAA,CAAAxF,GAAA,KAAAsF,YAAA,KACAE,CAAA,CAAA3F,KAAA;MAEA;IACA;IAEA6F,SAAA,WAAAA,UAAA1F,GAAA,EAAAd,IAAA;MACA,KAAAc,GAAA;MACA,YAAAD,OAAA,CAAAmC,IAAA,WAAAF,CAAA;QAAA,OAAAA,CAAA,CAAAhC,GAAA,KAAAA,GAAA;MAAA,GAAAd,IAAA,KAAAA,IAAA;IACA;IAEA;IACAyG,sBAAA,WAAAA,uBAAAd,IAAA;MAAA,IAAAe,UAAA,GAAAC,SAAA;MAAA,OAAAnF,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkF,SAAA;QAAA,IAAAC,QAAA,EAAAjD,GAAA;QAAA,OAAAnC,mBAAA,GAAAK,IAAA,UAAAgF,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9E,IAAA,GAAA8E,SAAA,CAAA7E,IAAA;YAAA;cAAA2E,QAAA,GAAAH,UAAA,CAAA5B,MAAA,QAAA4B,UAAA,QAAAxF,SAAA,GAAAwF,UAAA;cAAAK,SAAA,CAAA7E,IAAA;cAAA,OACAtC,aAAA;gBAAA+F,IAAA,EAAAkB,QAAA,SAAAlB;cAAA;YAAA;cAAA/B,GAAA,GAAAmD,SAAA,CAAArE,IAAA;cAAA,OAAAqE,SAAA,CAAArB,MAAA,WACA9B,GAAA,CAAAG,IAAA,CAAAiD,UAAA;YAAA;YAAA;cAAA,OAAAD,SAAA,CAAA5D,IAAA;UAAA;QAAA,GAAAyD,QAAA;MAAA;IACA;IAEA;IACAjH,WAAA,WAAAA,YAAAsH,QAAA;MAAA,IAAAC,WAAA,GAAAP,SAAA;QAAAQ,MAAA;MAAA,OAAA3F,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA0F,SAAA;QAAA,IAAAC,QAAA,EAAAR,QAAA,EAAA/G,KAAA,EAAA+B,OAAA;QAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAwF,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtF,IAAA,GAAAsF,SAAA,CAAArF,IAAA;YAAA;cAAAmF,QAAA,GAAAH,WAAA,CAAApC,MAAA,QAAAoC,WAAA,QAAAhG,SAAA,GAAAgG,WAAA;cAAAL,QAAA,GAAAK,WAAA,CAAApC,MAAA,OAAAoC,WAAA,MAAAhG,SAAA;cAAAqG,SAAA,CAAArF,IAAA;cAAA,OACAiF,MAAA,CAAAV,sBAAA,CAAAQ,QAAA,EAAAJ,QAAA;YAAA;cAAA/G,KAAA,GAAAyH,SAAA,CAAA7E,IAAA;cACAC,OAAA,CAAAC,GAAA,CAAA9C,KAAA;cACA+B,OAAA,GAAA/B,KAAA,CAAAsC,MAAA,WAAAI,CAAA;gBACA,IAAAoC,GAAA,GAAAyC,QAAA,CAAA9E,GAAA,WAAAC,CAAA;kBAAA,OAAAA,CAAA,CAAAgF,WAAA;gBAAA;gBACA,OAAA5C,GAAA,CAAA2B,QAAA,CAAA/D,CAAA,CAAAC,IAAA,CAAA+E,WAAA;cACA;cACA7E,OAAA,CAAAC,GAAA,CAAAf,OAAA;cAAA,OAAA0F,SAAA,CAAA7B,MAAA,WACA7D,OAAA;YAAA;YAAA;cAAA,OAAA0F,SAAA,CAAApE,IAAA;UAAA;QAAA,GAAAiE,QAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}
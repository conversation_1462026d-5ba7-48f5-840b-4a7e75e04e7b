{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\plan\\add.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\plan\\add.vue", "mtime": 1757468128084}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgVGl0bGVJbmZvIGZyb20gJ0Avdmlld3MvUFJPL3NoaXBtZW50L2FjdHVhbGx5LXNlbnQvdjMvY29tcG9uZW50L1RpdGxlSW5mbycNCmltcG9ydCBEeW5hbWljRGF0YVRhYmxlIGZyb20gJ0AvY29tcG9uZW50cy9EeW5hbWljRGF0YVRhYmxlL0R5bmFtaWNEYXRhVGFibGUnDQppbXBvcnQgVG9wSGVhZGVyIGZyb20gJ0AvY29tcG9uZW50cy9Ub3BIZWFkZXInDQppbXBvcnQgQ2hlY2tJbmZvIGZyb20gJ0Avdmlld3MvUFJPL0NvbXBvbmVudC9HZXRQYWNraW5nRGV0YWlsJw0KaW1wb3J0IHsgY2xvc2VUYWdWaWV3LCBwYXJzZVRpbWUgfSBmcm9tICdAL3V0aWxzJw0KaW1wb3J0IHsgR2V0UHJvamVjdEVudGl0eSwgR2V0UHJvamVjdFBhZ2VMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL3Byby1zY2hlZHVsZXMnDQppbXBvcnQgeyBHZXRBbGxVc2VyUGFnZSwgR2V0R3JpZEJ5Q29kZSB9IGZyb20gJ0AvYXBpL3N5cycNCmltcG9ydCBudW1lcmFsIGZyb20gJ251bWVyYWwnDQppbXBvcnQgQWRkRGlhbG9nIGZyb20gJy4vY29tcG9uZW50L2FkZCcNCmltcG9ydCB7IEdldExhc3RVc2VyLCBHZXRPdXRQbGFuRW50aXR5LCBTYXZlT3V0UGxhbiB9IGZyb20gJ0AvYXBpL1BSTy9zaGlwLXBsYW4nDQppbXBvcnQgeyBHZUFyZWFUcmVlcyB9IGZyb20gJ0AvYXBpL1BSTy9wcm9qZWN0Jw0KaW1wb3J0IEV4cG9ydEN1c3RvbVJlcG9ydCBmcm9tICJAL2NvbXBvbmVudHMvRXhwb3J0Q3VzdG9tUmVwb3J0L2luZGV4LnZ1ZSI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ1NoaXBQbGFuRGV0YWlsJywNCiAgY29tcG9uZW50czogew0KICAgIEV4cG9ydEN1c3RvbVJlcG9ydCwNCiAgICBUaXRsZUluZm8sDQogICAgVG9wSGVhZGVyLA0KICAgIER5bmFtaWNEYXRhVGFibGUsDQogICAgQ2hlY2tJbmZvLA0KICAgIEFkZERpYWxvZw0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBxdWVyeTogew0KICAgICAgICBjb2RlOiAnJywNCiAgICAgICAgYXJlYTogJycsDQogICAgICAgIHN0YXR1czogJycNCiAgICAgIH0sDQogICAgICB3aWR0aDogJzgwdncnLA0KICAgICAgY3VycmVudENvbXBvbmVudDogJycsDQogICAgICB0aXRsZTogJycsDQogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHRvcERpYWxvZzogJzV2aCcsDQogICAgICBzZW5kTnVtYmVyOiAnJywNCiAgICAgIHRvdGFsTnVtOiAnJywNCiAgICAgIHRvdGFsV2VpZ2h0OiAnJywNCiAgICAgIGZvcm06IHsNCiAgICAgICAgU3lzX1Byb2plY3RfSWQ6ICcnLA0KICAgICAgICBSZW1hcms6ICcnLA0KICAgICAgICBMb2FkZXJfVXNlcklkOiAnJywNCiAgICAgICAgU2hpcHBlcl9Vc2VySWQ6ICcnLA0KICAgICAgICBQbGFuX1VzZXJJZDogbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ1VzZXJJZCcpLA0KICAgICAgICBQbGFuX0RhdGU6ICcnLA0KICAgICAgICBTdGF0dXM6ICcnLCAvLyDnirbmgIEsIDE66I2J56i/77yMMu+8muWuoeaJueS4re+8jDPvvJrnu5PmnZ/vvIwtMe+8muW3sumps+Wbng0KICAgICAgICBwcm9qZWN0TmFtZTogJycNCiAgICAgIH0sDQogICAgICBydWxlczogew0KICAgICAgICBwcm9qZWN0SWQ6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup6aG555uuJywgdHJpZ2dlcjogJ2NoYW5nZScgfV0sDQogICAgICAgIC8vIExvYWRlcl9Vc2VySWQ6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup6KOF6L2m5Lq6JywgdHJpZ2dlcjogJ2NoYW5nZScgfV0sDQogICAgICAgIC8vIFNoaXBwZXJfVXNlcklkOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeWPkei0p+S6uicsIHRyaWdnZXI6ICdjaGFuZ2UnIH1dLA0KICAgICAgICBQbGFuX0RhdGU6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ6K6h5YiS5Y+R6LSn5pel5pyfJywgdHJpZ2dlcjogJ2NoYW5nZScgfV0sDQogICAgICAgIFBsYW5fVXNlcklkOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieiuoeWIkue8luWItuS6uicsIHRyaWdnZXI6ICdjaGFuZ2UnIH1dDQogICAgICB9LA0KICAgICAgUGFnZUluZm86IHsNCiAgICAgICAgUGFyYW1ldGVySnNvbjogW10sDQogICAgICAgIFBhZ2U6IDEsDQogICAgICAgIFBhZ2VTaXplOiAyMA0KICAgICAgfSwNCiAgICAgIHBsbV9Qcm9qZWN0U2VuZGluZ0luZm86IHt9LA0KICAgICAgSXRlbWRldGFpbDogW10sDQogICAgICBwcm9qZWN0czogJycsDQogICAgICBJZDogJycsDQogICAgICBwcm9qZWN0SWQ6ICcnLA0KICAgICAgdGJDb25maWc6IHsNCiAgICAgICAgUGFnZXJfQWxpZ246ICdjZW50ZXInDQogICAgICB9LA0KICAgICAgY29sdW1uczogW10sDQogICAgICB0YkRhdGE6IFtdLA0KICAgICAgdG90YWw6IDAsDQogICAgICB0YkxvYWRpbmc6IGZhbHNlLA0KICAgICAgc2VsZWN0TGlzdDogW10sDQogICAgICBzdW1zOiBbXSwNCiAgICAgIGFsbFVzZXJzOiBbXSwNCiAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgZmlsdGVyRGF0YTogW10sDQogICAgICB0eXBlOiAndmlldycsDQogICAgICBzZWxlY3RQYXJhbXM6IHsNCiAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fpgInmi6knLA0KICAgICAgICBjbGVhcmFibGU6IHRydWUNCiAgICAgIH0sDQogICAgICAvLyDljLrln5/mlbDmja4NCiAgICAgIHRyZWVQYXJhbXNBcmVhOiB7DQogICAgICAgICdjaGVjay1zdHJpY3RseSc6IHRydWUsDQogICAgICAgICdleHBhbmQtb24tY2xpY2stbm9kZSc6IGZhbHNlLA0KICAgICAgICAnZGVmYXVsdC1leHBhbmQtYWxsJzogdHJ1ZSwNCiAgICAgICAgZmlsdGVyYWJsZTogZmFsc2UsDQogICAgICAgIGNsaWNrUGFyZW50OiB0cnVlLA0KICAgICAgICBkYXRhOiBbXSwNCiAgICAgICAgcHJvcHM6IHsNCiAgICAgICAgICBjaGlsZHJlbjogJ0NoaWxkcmVuJywNCiAgICAgICAgICBsYWJlbDogJ0xhYmVsJywNCiAgICAgICAgICB2YWx1ZTogJ0xhYmVsJw0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIHN1bVdlaWdodCgpIHsNCiAgICAgIGxldCBzdW0gPSAwDQogICAgICB0aGlzLmZpbHRlckRhdGEuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgc3VtICs9IE51bWJlcihpdGVtLlRvdGFsX1dlaWdodCkNCiAgICAgIH0pDQogICAgICByZXR1cm4gKHN1bSAvIDEwMDApLnRvRml4ZWQoNSkNCiAgICB9LA0KICAgIHJlYWRvbmx5KCkgew0KICAgICAgcmV0dXJuIHRoaXMudHlwZSA9PT0gJ3ZpZXcnDQogICAgfQ0KICB9LA0KICB3YXRjaDogew0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0QWxsVXNlcnMoKQ0KICAgIHRoaXMuZ2V0VGFibGVDb25maWcoKQ0KICB9LA0KICBhc3luYyBtb3VudGVkKCkgew0KICAgIHRoaXMudHlwZSA9IHRoaXMuJHJvdXRlLnF1ZXJ5LnR5cGUgfHwgJ2FkZCcNCiAgICBpZiAodGhpcy50eXBlID09PSAnYWRkJykgew0KICAgICAgY29uc3Qgew0KICAgICAgICBOYW1lLA0KICAgICAgICBJZCwNCiAgICAgICAgU3lzX1Byb2plY3RfSWQNCiAgICAgIH0gPSBKU09OLnBhcnNlKGRlY29kZVVSSUNvbXBvbmVudCh0aGlzLiRyb3V0ZS5xdWVyeS5wKSkNCiAgICAgIHRoaXMucHJvamVjdElkID0gSWQNCiAgICAgIHRoaXMuZm9ybS5wcm9qZWN0TmFtZSA9IE5hbWUNCiAgICAgIHRoaXMuZm9ybS5TeXNfUHJvamVjdF9JZCA9IFN5c19Qcm9qZWN0X0lkDQogICAgICB0aGlzLmdldFByb2plY3RFbnRpdHkodGhpcy5wcm9qZWN0SWQpDQogICAgICB0aGlzLmdldExhc3RVc2VyKCkNCiAgICB9IGVsc2Ugew0KICAgICAgY29uc3Qgew0KICAgICAgICBOYW1lDQogICAgICB9ID0gSlNPTi5wYXJzZShkZWNvZGVVUklDb21wb25lbnQodGhpcy4kcm91dGUucXVlcnkucCkpDQogICAgICBjb25zb2xlLmxvZyhKU09OLnBhcnNlKGRlY29kZVVSSUNvbXBvbmVudCh0aGlzLiRyb3V0ZS5xdWVyeS5wKSkpDQogICAgICB0aGlzLmZvcm0ucHJvamVjdE5hbWUgPSBOYW1lDQogICAgICB0aGlzLmdldEluZm8oKQ0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIHN1bUl0ZW0ocm93KSB7DQogICAgICByb3cuVG90YWxfV2VpZ2h0ID0gKHJvdy5TdGVlbFdlaWdodCAqIHJvdy5QbGFuX0NvdW50KS50b0ZpeGVkKDIpDQogICAgfSwNCiAgICBjaGVja2JveENoYW5nZSgpIHsNCiAgICAgIHRoaXMuc2VsZWN0TGlzdCA9IHRoaXMuJHJlZnMudnhlVGFibGUuZ2V0Q2hlY2tib3hSZWNvcmRzKCkNCiAgICB9LA0KICAgIHNlbGVjdEFsbENoZWNrYm94Q2hhbmdlKCkgew0KICAgICAgdGhpcy5zZWxlY3RMaXN0ID0gdGhpcy4kcmVmcy52eGVUYWJsZS5nZXRDaGVja2JveFJlY29yZHMoKQ0KICAgIH0sDQogICAgZmlsdGVyRnVuKHZhbCwgcmVmKSB7DQogICAgICB0aGlzLiRyZWZzW3JlZl0uZmlsdGVyRnVuKHZhbCkNCiAgICB9LA0KICAgIC8vIOa4heepuuWMuuWfnw0KICAgIGFyZWFDbGVhcigpIHsNCiAgICAgIHRoaXMucXVlcnkuQXJlYV9JZCA9ICcnDQogICAgfSwNCiAgICAvLyDojrflj5bljLrln58NCiAgICBnZXRBcmVhTGlzdCgpIHsNCiAgICAgIEdlQXJlYVRyZWVzKHsNCiAgICAgICAgc3lzUHJvamVjdElkOiB0aGlzLmZvcm0uU3lzX1Byb2plY3RfSWQNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMudHJlZVBhcmFtc0FyZWEuZGF0YSA9IHJlcy5EYXRhDQogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKF8pID0+IHsNCiAgICAgICAgICAgIHRoaXMuJHJlZnMudHJlZVNlbGVjdEFyZWEudHJlZURhdGFVcGRhdGVGdW4ocmVzLkRhdGEpDQogICAgICAgICAgfSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBzZWFyY2goKSB7DQogICAgICB0aGlzLmZpbHRlckRhdGEgPSB0aGlzLnRiRGF0YS5maWx0ZXIoaXRlbSA9PiB7DQogICAgICAgIHJldHVybiAoaXRlbS5Db21wb25lbnRfQ29kZSB8fCAnJykuaW5jbHVkZXModGhpcy5xdWVyeS5jb2RlKSAmJiAoaXRlbS5GdWxsQXJlYXBvc2l0aW9uIHx8ICcnKS5pbmNsdWRlcyh0aGlzLnF1ZXJ5LmFyZWEpICYmIChpdGVtLlN0YXR1cyB8fCAnJykuaW5jbHVkZXModGhpcy5xdWVyeS5zdGF0dXMpDQogICAgICB9KQ0KICAgIH0sDQogICAgZ2V0TGFzdFVzZXIoKSB7DQogICAgICBHZXRMYXN0VXNlcih7DQogICAgICAgIFN5c19Qcm9qZWN0X0lkOiB0aGlzLmZvcm0uU3lzX1Byb2plY3RfSWQNCiAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLmZvcm0uTG9hZGVyX1VzZXJJZCA9IHJlcy5EYXRhLkxvYWRlcl9Vc2VySWQNCiAgICAgICAgICB0aGlzLmZvcm0uU2hpcHBlcl9Vc2VySWQgPSByZXMuRGF0YS5TaGlwcGVyX1VzZXJJZA0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgYWN0aXZlQ2VsbE1ldGhvZCh7IHJvdywgY29sdW1uLCBjb2x1bW5JbmRleCB9KSB7DQogICAgICByZXR1cm4gY29sdW1uLmZpZWxkID09PSAnUGxhbl9Db3VudCcNCiAgICB9LA0KICAgIGFzeW5jIGhhbmRsZVN1Ym1pdChzdGF0dXMpIHsNCiAgICAgIGF3YWl0IHRoaXMuJHJlZnNbJ2Zvcm0nXS52YWxpZGF0ZSgpDQogICAgICBpZiAoIXRoaXMudGJEYXRhIHx8ICF0aGlzLnRiRGF0YS5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6K+35re75Yqg5piO57uGJykNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICBsZXQgZmxhZyA9IGZhbHNlDQogICAgICB0aGlzLnRiRGF0YS5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICBpZiAoaXRlbS5QbGFuX0NvdW50IDwgMSkgew0KICAgICAgICAgIGZsYWcgPSB0cnVlDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICBpZiAoZmxhZykgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflupTlj5HmlbDph4/kuI3og73lsI/kuo4xJykNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICB0aGlzLmlzQ2xpY2tlZCA9IHRydWUNCiAgICAgIGNvbnN0IHN1Ym1pdE9iaiA9IHsNCiAgICAgICAgTWFpbjogew0KICAgICAgICAgIC4uLnRoaXMuZm9ybSwNCiAgICAgICAgICBTdGF0dXM6IHN0YXR1cw0KICAgICAgICB9LA0KICAgICAgICBEZXRhaWxzOiB0aGlzLnRiRGF0YQ0KICAgICAgfQ0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgU2F2ZU91dFBsYW4oc3VibWl0T2JqKQ0KICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfkv53lrZjmiJDlip8nKQ0KICAgICAgICB0aGlzLnRvQmFjaygpDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5NZXNzYWdlKQ0KICAgICAgfQ0KICAgIH0sDQogICAgZ2V0TnVtKGEsIGIpIHsNCiAgICAgIHJldHVybiBudW1lcmFsKGEpLnN1YnRyYWN0KGIpLmZvcm1hdCgnMC5bMDAwXScpDQogICAgfSwNCiAgICBnZXRQcm9qZWN0RW50aXR5KElkKSB7DQogICAgICBHZXRQcm9qZWN0RW50aXR5KHsgSWQgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgY29uc3QgQ29uc2lnbmVlID0gcmVzLkRhdGEuQ29udGFjdHMuZmluZCgoaXRlbSkgPT4gew0KICAgICAgICAgICAgcmV0dXJuIGl0ZW0uVHlwZSA9PSAnQ29uc2lnbmVlJw0KICAgICAgICAgIH0pDQogICAgICAgICAgY29uc29sZS5sb2coQ29uc2lnbmVlLCAnQ29uc2lnbmVlJykNCiAgICAgICAgICB0aGlzLmZvcm0ucmVjZWl2ZU5hbWUgPSBDb25zaWduZWU/Lk5hbWUNCiAgICAgICAgICB0aGlzLmZvcm0uUmVjZWl2ZXJfVGVsID0gQ29uc2lnbmVlPy5UZWwNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGdldFByb2plY3RQYWdlTGlzdCgpIHsNCiAgICAgIEdldFByb2plY3RQYWdlTGlzdCh7IFBhZ2VTaXplOiAtMSB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLnByb2plY3RzID0gcmVzLkRhdGEuRGF0YQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgdG9CYWNrKCkgew0KICAgICAgY2xvc2VUYWdWaWV3KHRoaXMuJHN0b3JlLCB0aGlzLiRyb3V0ZSkNCiAgICB9LA0KICAgIGdldEluZm8oKSB7DQogICAgICBHZXRPdXRQbGFuRW50aXR5KHsNCiAgICAgICAgaWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LmlkDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICBjb25zdCBuYW1lID0gdGhpcy5mb3JtLnByb2plY3ROYW1lDQogICAgICAgICAgdGhpcy5mb3JtID0gcmVzLkRhdGEuTWFpbg0KICAgICAgICAgIHRoaXMuZm9ybS5wcm9qZWN0TmFtZSA9IG5hbWUNCiAgICAgICAgICB0aGlzLmZvcm0ucHJvamVjdElkID0gdGhpcy5mb3JtLlN5c19Qcm9qZWN0X0lkDQogICAgICAgICAgdGhpcy50YkRhdGEgPSByZXMuRGF0YS5EZXRhaWxzLm1hcChpdGVtID0+IHsNCiAgICAgICAgICAgIGl0ZW0uU3RhdHVzID0gKChpdGVtLkFjY2VwdF9Db3VudCAtIGl0ZW0uUGxhbl9Db3VudCA+PSAwKSAmJiBpdGVtLkFjY2VwdF9Db3VudCkgPyAn5bey5a6M5oiQJyA6ICfmnKrlrozmiJAnDQogICAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy5zZWFyY2goKQ0KICAgICAgICAgIHRoaXMuZ2V0QXJlYUxpc3QoKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFkZFNlbGVjdExpc3QobGlzdCkgew0KICAgICAgbGlzdC5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgIGl0ZW0uU3RhdHVzID0gJ+acquWujOaIkCcNCiAgICAgICAgdGhpcy50YkRhdGEucHVzaChpdGVtKQ0KICAgICAgfSkNCiAgICAgIHRoaXMudG90YWwgPSB0aGlzLnRiRGF0YS5sZW5ndGgNCiAgICAgIHRoaXMuc2VhcmNoKCkNCiAgICB9LA0KICAgIGdldFRvdGFsKCkge30sDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ0FkZERpYWxvZycNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMudGl0bGUgPSAn5paw5aKeJw0KICAgIH0sDQogICAgaGFuZGxlRGVsZXRlKCkgew0KICAgICAgdGhpcy4kY29uZmlybSgn5Yig6Zmk6K+l5pWw5o2uLCDmmK/lkKbnu6fnu60/JywgJ+aPkOekuicsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5zZWxlY3RMaXN0LmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy50YkRhdGEuZmluZEluZGV4KCh2KSA9PiB2LkNvbXBvbmVudF9JZCA9PT0gaXRlbS5Db21wb25lbnRfSWQpDQogICAgICAgICAgICBpbmRleCAhPT0gLTEgJiYgdGhpcy50YkRhdGEuc3BsaWNlKGluZGV4LCAxKQ0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy5zZWFyY2goKQ0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLA0KICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKnyEnDQogICAgICAgICAgfSkNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHt9KQ0KICAgIH0sDQogICAgY2xvc2UoKSB7DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgIH0sDQogICAgaGFuZGxlSW5mbyhyb3cpIHsNCiAgICAgIHRoaXMuJHJlZnMuaW5mby5oYW5kbGVPcGVuKHJvdykNCiAgICB9LA0KICAgIGdldFRhYmxlQ29uZmlnKCkgew0KICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlKSA9PiB7DQogICAgICAgIEdldEdyaWRCeUNvZGUoew0KICAgICAgICAgIGNvZGU6ICdQUk9TaGlwUGxhbkRldGFpbCcNCiAgICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgY29uc3QgeyBJc1N1Y2NlZWQsIERhdGEsIE1lc3NhZ2UgfSA9IHJlcw0KICAgICAgICAgIGlmIChJc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIGlmICghRGF0YSkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAn6KGo5qC86YWN572u5LiN5a2Y5ZyoJywNCiAgICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgIHJldHVybg0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgdGhpcy50YkNvbmZpZyA9IE9iamVjdC5hc3NpZ24oe30sIHRoaXMudGJDb25maWcsIERhdGEuR3JpZCkNCiAgICAgICAgICAgIHRoaXMuY29sdW1ucyA9IERhdGEuQ29sdW1uTGlzdC5tYXAoKGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgaXRlbS5Jc19SZXNpemFibGUgPSB0cnVlDQogICAgICAgICAgICAgIHJldHVybiBpdGVtDQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgdGhpcy5QYWdlSW5mby5QYWdlU2l6ZSA9ICtEYXRhLkdyaWQuUm93X051bWJlcg0KICAgICAgICAgICAgcmVzb2x2ZSh0aGlzLmNvbHVtbnMpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiBNZXNzYWdlLA0KICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfSwNCiAgICBnZXRBbGxVc2VycygpIHsNCiAgICAgIEdldEFsbFVzZXJQYWdlKCkudGhlbihyZXMgPT4gew0KICAgICAgICB0aGlzLmFsbFVzZXJzID0gcmVzLkRhdGEuRGF0YQ0KICAgICAgfSkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["add.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "add.vue", "sourceRoot": "src/views/PRO/shipment/plan", "sourcesContent": ["<template>\r\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\r\n    <div class=\"cs-z-page-main-content\">\r\n      <top-header>\r\n        <template #left>\r\n          <div class=\"cs-header\">\r\n            <el-button @click=\"toBack\">返回</el-button>\r\n          </div>\r\n        </template>\r\n        <template #right>\r\n          <ExportCustomReport v-if=\"form.Id\" code=\"Shipping_plan_template\" style=\"margin:0 10px\" name=\"导出派工单\" :ids=\"[form.Id]\" />\r\n          <template v-if=\"!readonly\">\r\n            <el-button type=\"primary\" :loading=\"loading\" @click=\"handleSubmit(1)\">保存草稿</el-button>\r\n            <el-button type=\"primary\" :loading=\"loading\" @click=\"handleSubmit(2)\">提交</el-button>\r\n          </template>\r\n        </template>\r\n      </top-header>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"110px\">\r\n        <el-row>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"发货计划单号\" prop=\"Code\">\r\n              <el-input\r\n                v-model=\"form.Code\"\r\n                :disabled=\"true\"\r\n                placeholder=\"自动生成\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"项目名称\" prop=\"projectName\">\r\n              <el-input v-model=\"form.projectName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"装车人\" prop=\"Loader_UserId\">\r\n              <el-select v-model=\"form.Loader_UserId\" :disabled=\"readonly\" placeholder=\"请选择\" clearable filterable style=\"width: 100%\">\r\n                <el-option v-for=\"item in allUsers\" :key=\"item.id\" :value=\"item.Id\" :label=\"item.Display_Name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"发货人\" prop=\"Shipper_UserId\">\r\n              <el-select v-model=\"form.Shipper_UserId\" :disabled=\"readonly\" placeholder=\"请选择\" clearable filterable style=\"width: 100%\">\r\n                <el-option v-for=\"item in allUsers\" :key=\"item.id\" :value=\"item.Id\" :label=\"item.Display_Name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"计划发货日期\" prop=\"Plan_Date\">\r\n              <el-date-picker\r\n                v-model=\"form.Plan_Date\"\r\n                :disabled=\"readonly\"\r\n                placeholder=\"请选择\"\r\n                style=\"width: 100%\"\r\n                type=\"date\"\r\n                value-format=\"yyyy-MM-dd\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"计划编制人\" prop=\"Plan_UserId\">\r\n              <el-select v-model=\"form.Plan_UserId\" :disabled=\"readonly\" placeholder=\"请选择\" clearable filterable style=\"width: 100%\">\r\n                <el-option v-for=\"item in allUsers\" :key=\"item.id\" :value=\"item.Id\" :label=\"item.Display_Name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"备注\">\r\n              <el-input\r\n                v-model=\"form.Remark\"\r\n                :disabled=\"readonly\"\r\n                :autosize=\"{ minRows: 2, maxRows: 2 }\"\r\n                :maxlength=\"500\"\r\n                show-word-limit\r\n                type=\"textarea\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n\r\n      <div class=\"header\">\r\n        <el-form inline label-width=\"70px\">\r\n          <el-form-item label=\"构件名称\"><el-input v-model=\"query.code\" clearable /></el-form-item>\r\n          <el-form-item label=\"区域\">\r\n            <el-tree-select\r\n              ref=\"treeSelectArea\"\r\n              v-model=\"query.area\"\r\n              class=\"treeselect\"\r\n              :select-params=\"selectParams\"\r\n              :tree-params=\"treeParamsArea\"\r\n              @searchFun=\"filterFun($event, 'treeSelectArea')\"\r\n              @select-clear=\"areaClear\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\">\r\n            <el-select v-model=\"query.status\" clearable>\r\n              <el-option label=\"未完成\" value=\"未完成\" />\r\n              <el-option label=\"已完成\" value=\"已完成\" />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item><el-button type=\"primary\" @click=\"search\">搜索</el-button></el-form-item>\r\n        </el-form>\r\n        <div class=\"right\">\r\n          <div style=\"margin-right: 10px\">理论总重：{{ sumWeight }}t</div>\r\n          <template v-if=\"!readonly\">\r\n            <el-button\r\n              :disabled=\"!selectList.length\"\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleDelete\"\r\n            >删除</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >添加</el-button>\r\n          </template>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-loading=\"tbLoading\" class=\"fff cs-z-tb-wrapper\">\r\n        <vxe-table\r\n          ref=\"vxeTable\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          empty-text=\"暂无数据\"\r\n          height=\"auto\"\r\n          show-overflow\r\n          :loading=\"tbLoading\"\r\n          class=\"cs-vxe-table\"\r\n          align=\"left\"\r\n          stripe\r\n          :data=\"filterData\"\r\n          resizable\r\n          :edit-config=\"{trigger: 'click', mode: 'cell', activeMethod: activeCellMethod}\"\r\n          :tooltip-config=\"{ enterable: true }\"\r\n          @checkbox-change=\"checkboxChange\"\r\n          @checkbox-all=\"selectAllCheckboxChange\"\r\n        >\r\n          <vxe-column v-if=\"!readonly\" type=\"checkbox\" width=\"60\" />\r\n          <template v-for=\"item in columns\">\r\n            <vxe-column\r\n              v-if=\"item.Code === 'Plan_Count' && !readonly\"\r\n              :key=\"item.Code\"\r\n              :field=\"item.Code\"\r\n              :align=\"item.Align\"\r\n              :title=\"item.Display_Name\"\r\n              sortable\r\n              :edit-render=\"{}\"\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #edit=\"{ row }\">\r\n                <vxe-input\r\n                  v-model=\"row.Plan_Count\"\r\n                  type=\"integer\"\r\n                  :min=\"1\"\r\n                  @change=\"(val)=>sumItem(row)\"\r\n                />\r\n              </template>\r\n              <template #default=\"{ row }\">\r\n                {{ row.Plan_Count }}\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column\r\n              v-else-if=\"item.Code === 'Is_Direct'\"\r\n              :key=\"item.Code\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              sortable\r\n              :width=\"item.Width\"\r\n              :align=\"item.Align\"\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                {{ row.Is_Direct==true ? \"是\" : \"否\" }}\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column\r\n              v-else\r\n              :key=\"item.Code\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :align=\"item.Align\"\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                {{ row[item.Code] ? row[item.Code] : \"-\" }}\r\n              </template>\r\n            </vxe-column>\r\n          </template>\r\n        </vxe-table>\r\n      </div>\r\n      <el-dialog\r\n        v-if=\"dialogVisible\"\r\n        v-dialogDrag\r\n        class=\"plm-custom-dialog\"\r\n        :title=\"title\"\r\n        :visible.sync=\"dialogVisible\"\r\n        :width=\"width\"\r\n        :top=\"topDialog\"\r\n        @close=\"close\"\r\n      >\r\n        <component\r\n          :is=\"currentComponent\"\r\n          ref=\"content\"\r\n          :dialog-visible=\"dialogVisible\"\r\n          :project-id=\"projectId\"\r\n          :sys-project-id=\"form.Sys_Project_Id\"\r\n          :checked-data=\"tbData\"\r\n          @close=\"close\"\r\n          @reCount=\"getTotal\"\r\n          @selectList=\"addSelectList\"\r\n        />\r\n      </el-dialog>\r\n      <check-info ref=\"info\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport TitleInfo from '@/views/PRO/shipment/actually-sent/v3/component/TitleInfo'\r\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'\r\nimport TopHeader from '@/components/TopHeader'\r\nimport CheckInfo from '@/views/PRO/Component/GetPackingDetail'\r\nimport { closeTagView, parseTime } from '@/utils'\r\nimport { GetProjectEntity, GetProjectPageList } from '@/api/PRO/pro-schedules'\r\nimport { GetAllUserPage, GetGridByCode } from '@/api/sys'\r\nimport numeral from 'numeral'\r\nimport AddDialog from './component/add'\r\nimport { GetLastUser, GetOutPlanEntity, SaveOutPlan } from '@/api/PRO/ship-plan'\r\nimport { GeAreaTrees } from '@/api/PRO/project'\r\nimport ExportCustomReport from \"@/components/ExportCustomReport/index.vue\";\r\n\r\nexport default {\r\n  name: 'ShipPlanDetail',\r\n  components: {\r\n    ExportCustomReport,\r\n    TitleInfo,\r\n    TopHeader,\r\n    DynamicDataTable,\r\n    CheckInfo,\r\n    AddDialog\r\n  },\r\n  data() {\r\n    return {\r\n      query: {\r\n        code: '',\r\n        area: '',\r\n        status: ''\r\n      },\r\n      width: '80vw',\r\n      currentComponent: '',\r\n      title: '',\r\n      dialogVisible: false,\r\n      topDialog: '5vh',\r\n      sendNumber: '',\r\n      totalNum: '',\r\n      totalWeight: '',\r\n      form: {\r\n        Sys_Project_Id: '',\r\n        Remark: '',\r\n        Loader_UserId: '',\r\n        Shipper_UserId: '',\r\n        Plan_UserId: localStorage.getItem('UserId'),\r\n        Plan_Date: '',\r\n        Status: '', // 状态, 1:草稿，2：审批中，3：结束，-1：已驳回\r\n        projectName: ''\r\n      },\r\n      rules: {\r\n        projectId: [{ required: true, message: '请选择项目', trigger: 'change' }],\r\n        // Loader_UserId: [{ required: true, message: '请选择装车人', trigger: 'change' }],\r\n        // Shipper_UserId: [{ required: true, message: '请选择发货人', trigger: 'change' }],\r\n        Plan_Date: [{ required: true, message: '请选计划发货日期', trigger: 'change' }],\r\n        Plan_UserId: [{ required: true, message: '请选计划编制人', trigger: 'change' }]\r\n      },\r\n      PageInfo: {\r\n        ParameterJson: [],\r\n        Page: 1,\r\n        PageSize: 20\r\n      },\r\n      plm_ProjectSendingInfo: {},\r\n      Itemdetail: [],\r\n      projects: '',\r\n      Id: '',\r\n      projectId: '',\r\n      tbConfig: {\r\n        Pager_Align: 'center'\r\n      },\r\n      columns: [],\r\n      tbData: [],\r\n      total: 0,\r\n      tbLoading: false,\r\n      selectList: [],\r\n      sums: [],\r\n      allUsers: [],\r\n      loading: false,\r\n      filterData: [],\r\n      type: 'view',\r\n      selectParams: {\r\n        placeholder: '请选择',\r\n        clearable: true\r\n      },\r\n      // 区域数据\r\n      treeParamsArea: {\r\n        'check-strictly': true,\r\n        'expand-on-click-node': false,\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Label'\r\n        }\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    sumWeight() {\r\n      let sum = 0\r\n      this.filterData.forEach(item => {\r\n        sum += Number(item.Total_Weight)\r\n      })\r\n      return (sum / 1000).toFixed(5)\r\n    },\r\n    readonly() {\r\n      return this.type === 'view'\r\n    }\r\n  },\r\n  watch: {\r\n  },\r\n  created() {\r\n    this.getAllUsers()\r\n    this.getTableConfig()\r\n  },\r\n  async mounted() {\r\n    this.type = this.$route.query.type || 'add'\r\n    if (this.type === 'add') {\r\n      const {\r\n        Name,\r\n        Id,\r\n        Sys_Project_Id\r\n      } = JSON.parse(decodeURIComponent(this.$route.query.p))\r\n      this.projectId = Id\r\n      this.form.projectName = Name\r\n      this.form.Sys_Project_Id = Sys_Project_Id\r\n      this.getProjectEntity(this.projectId)\r\n      this.getLastUser()\r\n    } else {\r\n      const {\r\n        Name\r\n      } = JSON.parse(decodeURIComponent(this.$route.query.p))\r\n      console.log(JSON.parse(decodeURIComponent(this.$route.query.p)))\r\n      this.form.projectName = Name\r\n      this.getInfo()\r\n    }\r\n  },\r\n  methods: {\r\n    sumItem(row) {\r\n      row.Total_Weight = (row.SteelWeight * row.Plan_Count).toFixed(2)\r\n    },\r\n    checkboxChange() {\r\n      this.selectList = this.$refs.vxeTable.getCheckboxRecords()\r\n    },\r\n    selectAllCheckboxChange() {\r\n      this.selectList = this.$refs.vxeTable.getCheckboxRecords()\r\n    },\r\n    filterFun(val, ref) {\r\n      this.$refs[ref].filterFun(val)\r\n    },\r\n    // 清空区域\r\n    areaClear() {\r\n      this.query.Area_Id = ''\r\n    },\r\n    // 获取区域\r\n    getAreaList() {\r\n      GeAreaTrees({\r\n        sysProjectId: this.form.Sys_Project_Id\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.treeParamsArea.data = res.Data\r\n          this.$nextTick((_) => {\r\n            this.$refs.treeSelectArea.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    search() {\r\n      this.filterData = this.tbData.filter(item => {\r\n        return (item.Component_Code || '').includes(this.query.code) && (item.FullAreaposition || '').includes(this.query.area) && (item.Status || '').includes(this.query.status)\r\n      })\r\n    },\r\n    getLastUser() {\r\n      GetLastUser({\r\n        Sys_Project_Id: this.form.Sys_Project_Id\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.form.Loader_UserId = res.Data.Loader_UserId\r\n          this.form.Shipper_UserId = res.Data.Shipper_UserId\r\n        }\r\n      })\r\n    },\r\n    activeCellMethod({ row, column, columnIndex }) {\r\n      return column.field === 'Plan_Count'\r\n    },\r\n    async handleSubmit(status) {\r\n      await this.$refs['form'].validate()\r\n      if (!this.tbData || !this.tbData.length) {\r\n        this.$message.error('请添加明细')\r\n        return\r\n      }\r\n      let flag = false\r\n      this.tbData.forEach(item => {\r\n        if (item.Plan_Count < 1) {\r\n          flag = true\r\n        }\r\n      })\r\n      if (flag) {\r\n        this.$message.error('应发数量不能小于1')\r\n        return\r\n      }\r\n      this.isClicked = true\r\n      const submitObj = {\r\n        Main: {\r\n          ...this.form,\r\n          Status: status\r\n        },\r\n        Details: this.tbData\r\n      }\r\n      const res = await SaveOutPlan(submitObj)\r\n      if (res.IsSucceed) {\r\n        this.$message.success('保存成功')\r\n        this.toBack()\r\n      } else {\r\n        this.$message.error(res.Message)\r\n      }\r\n    },\r\n    getNum(a, b) {\r\n      return numeral(a).subtract(b).format('0.[000]')\r\n    },\r\n    getProjectEntity(Id) {\r\n      GetProjectEntity({ Id }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const Consignee = res.Data.Contacts.find((item) => {\r\n            return item.Type == 'Consignee'\r\n          })\r\n          console.log(Consignee, 'Consignee')\r\n          this.form.receiveName = Consignee?.Name\r\n          this.form.Receiver_Tel = Consignee?.Tel\r\n        }\r\n      })\r\n    },\r\n    getProjectPageList() {\r\n      GetProjectPageList({ PageSize: -1 }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.projects = res.Data.Data\r\n        }\r\n      })\r\n    },\r\n    toBack() {\r\n      closeTagView(this.$store, this.$route)\r\n    },\r\n    getInfo() {\r\n      GetOutPlanEntity({\r\n        id: this.$route.query.id\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const name = this.form.projectName\r\n          this.form = res.Data.Main\r\n          this.form.projectName = name\r\n          this.form.projectId = this.form.Sys_Project_Id\r\n          this.tbData = res.Data.Details.map(item => {\r\n            item.Status = ((item.Accept_Count - item.Plan_Count >= 0) && item.Accept_Count) ? '已完成' : '未完成'\r\n            return item\r\n          })\r\n          this.search()\r\n          this.getAreaList()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    addSelectList(list) {\r\n      list.forEach((item) => {\r\n        item.Status = '未完成'\r\n        this.tbData.push(item)\r\n      })\r\n      this.total = this.tbData.length\r\n      this.search()\r\n    },\r\n    getTotal() {},\r\n    handleAdd() {\r\n      this.currentComponent = 'AddDialog'\r\n      this.dialogVisible = true\r\n      this.title = '新增'\r\n    },\r\n    handleDelete() {\r\n      this.$confirm('删除该数据, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          this.selectList.forEach((item) => {\r\n            const index = this.tbData.findIndex((v) => v.Component_Id === item.Component_Id)\r\n            index !== -1 && this.tbData.splice(index, 1)\r\n          })\r\n          this.search()\r\n          this.$message({\r\n            type: 'success',\r\n            message: '删除成功!'\r\n          })\r\n        })\r\n        .catch(() => {})\r\n    },\r\n    close() {\r\n      this.dialogVisible = false\r\n    },\r\n    handleInfo(row) {\r\n      this.$refs.info.handleOpen(row)\r\n    },\r\n    getTableConfig() {\r\n      return new Promise((resolve) => {\r\n        GetGridByCode({\r\n          code: 'PROShipPlanDetail'\r\n        }).then((res) => {\r\n          const { IsSucceed, Data, Message } = res\r\n          if (IsSucceed) {\r\n            if (!Data) {\r\n              this.$message({\r\n                message: '表格配置不存在',\r\n                type: 'error'\r\n              })\r\n              return\r\n            }\r\n            this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n            this.columns = Data.ColumnList.map((item) => {\r\n              item.Is_Resizable = true\r\n              return item\r\n            })\r\n            this.PageInfo.PageSize = +Data.Grid.Row_Number\r\n            resolve(this.columns)\r\n          } else {\r\n            this.$message({\r\n              message: Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    getAllUsers() {\r\n      GetAllUserPage().then(res => {\r\n        this.allUsers = res.Data.Data\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .tb-status {\r\n    background: #fae6bb;\r\n    padding: 16px 20px;\r\n    font-size: 1.2em;\r\n    font-weight: bold;\r\n    display: flex;\r\n\r\n    * {\r\n      margin-right: 12px;\r\n    }\r\n  }\r\n\r\n  .el-form {\r\n    margin: 16px 10px;\r\n  }\r\n\r\n  .title {\r\n    margin-left: 10px;\r\n  }\r\n\r\n  .cs-red{\r\n    color:red\r\n  }\r\n\r\n  .statistics-container {\r\n    display: flex;\r\n    .statistics-item {\r\n      margin-right: 32px;\r\n      .cs-label{\r\n        display: inline-block;\r\n        font-size: 14px;\r\n        line-height: 18px;\r\n        font-weight: 500;\r\n        color: #999999;\r\n        margin-left: 10px;\r\n        // margin-right: 16px;\r\n      }\r\n      .cs-num {\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        color: #00c361;\r\n      }\r\n    }\r\n  }\r\n  .header{\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    .right{\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n  }\r\n</style>\r\n"]}]}
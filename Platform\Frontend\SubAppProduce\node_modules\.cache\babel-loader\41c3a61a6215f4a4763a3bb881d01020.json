{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\router\\modules\\PRO\\index.js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\router\\modules\\PRO\\index.js", "mtime": 1757468111953}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["order", "task", "shedule", "means", "prepare", "stockin", "retruns", "shipment", "inventory", "process", "cockpit", "basic", "report", "count", "factoryModel", "general", "projectList", "component", "part", "planProduction", "proExecution", "checkList", "qualityInspect", "salary", "home", "change", "partReplace", "material", "bim", "operational", "materialRegister", "section", "management", "technology", "bom", "projectConfig", "_objectSpread"], "sources": ["D:/project/platform_framework_master/platform_framework/Platform/Frontend/SubAppProduce/src/router/modules/PRO/index.js"], "sourcesContent": ["import order from './pro-order'\r\nimport task from './pro-task'\r\nimport shedule from './pro-schedule'\r\nimport means from './pro-means'\r\nimport prepare from './pro-prepare'\r\nimport stockin from './pro-stockin'\r\nimport retruns from './pro-return'\r\nimport shipment from './pro-shipment'\r\nimport inventory from './pro-inventory'\r\nimport process from './pro-process'\r\nimport cockpit from './pro-cockpit'\r\nimport basic from './pro-basic'\r\nimport report from './pro-report'\r\nimport count from './pro-count'\r\nimport factoryModel from './pro-factoryModel'\r\nimport general from './pro-general'\r\nimport projectList from './project-list'\r\nimport component from './pro-component'\r\nimport part from './pro-part'\r\nimport planProduction from './plan-production'\r\nimport proExecution from './pro-execution'\r\nimport checkList from './pro-checkList'\r\nimport qualityInspect from './pro-quality'\r\nimport salary from './pro-salary'\r\nimport home from './pro-home'\r\nimport change from './pro-change'\r\nimport partReplace from './pro-partReplace'\r\nimport material from './pro-material'\r\nimport bim from './pro-bim'\r\nimport operational from './operational-sys'\r\nimport materialRegister from './pro-materialRegister'\r\nimport section from './pro-section'\r\nimport management from './pro-management'\r\nimport technology from './pro-technology'\r\nimport bom from './bom'\r\nimport projectConfig from './project-config'\r\n\r\nexport default {\r\n  // 生产订单\r\n  ...order,\r\n  // 生产任务\r\n  ...task,\r\n  // 生成运营计划\r\n  ...shedule,\r\n  // 生产材料\r\n  ...means,\r\n  // 齐套管理\r\n  ...prepare,\r\n  // 入库管理\r\n  ...stockin,\r\n  // 退换货\r\n  ...retruns,\r\n  // 发运管理\r\n  ...shipment,\r\n  // 库存管理\r\n  ...inventory,\r\n  // 工艺管理\r\n  ...process,\r\n  // 驾驶舱\r\n  ...cockpit,\r\n  // 基础信息\r\n  ...basic,\r\n  // 生产报表\r\n  ...report,\r\n  // 生产监控\r\n  ...count,\r\n  // 工厂建模\r\n  ...factoryModel,\r\n  // 通用字典\r\n  ...general,\r\n  // 项目管理\r\n  ...projectList,\r\n  // 构件\r\n  ...component,\r\n  // 零件\r\n  ...part,\r\n  ...planProduction,\r\n  ...proExecution,\r\n  // 工厂质检\r\n  ...checkList,\r\n  // 质检管理\r\n  ...qualityInspect,\r\n  // 工资管理\r\n  ...salary,\r\n  // 首页\r\n  ...home,\r\n  // 变更管理\r\n  ...change,\r\n  // 零件补换\r\n  ...partReplace,\r\n  // 原料管理\r\n  ...material,\r\n  // BIM应用\r\n  ...bim,\r\n  ...operational,\r\n  // 物料登记\r\n  ...materialRegister,\r\n  // 部件清单\r\n  ...section,\r\n  // 考勤管理\r\n  ...management,\r\n  // 工艺管理\r\n  ...technology,\r\n  ...bom,\r\n  ...projectConfig\r\n}\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,OAAO,MAAM,gBAAgB;AACpC,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,cAAc,MAAM,eAAe;AAC1C,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,gBAAgB,MAAM,wBAAwB;AACrD,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,aAAa,MAAM,kBAAkB;AAE5C,eAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAEKpC,KAAK,GAELC,IAAI,GAEJC,OAAO,GAEPC,KAAK,GAELC,OAAO,GAEPC,OAAO,GAEPC,OAAO,GAEPC,QAAQ,GAERC,SAAS,GAETC,OAAO,GAEPC,OAAO,GAEPC,KAAK,GAELC,MAAM,GAENC,KAAK,GAELC,YAAY,GAEZC,OAAO,GAEPC,WAAW,GAEXC,SAAS,GAETC,IAAI,GACJC,cAAc,GACdC,YAAY,GAEZC,SAAS,GAETC,cAAc,GAEdC,MAAM,GAENC,IAAI,GAEJC,MAAM,GAENC,WAAW,GAEXC,QAAQ,GAERC,GAAG,GACHC,WAAW,GAEXC,gBAAgB,GAEhBC,OAAO,GAEPC,UAAU,GAEVC,UAAU,GACVC,GAAG,GACHC,aAAa", "ignoreList": []}]}
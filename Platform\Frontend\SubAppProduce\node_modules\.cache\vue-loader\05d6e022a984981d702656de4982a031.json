{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\index.vue", "mtime": 1757909680922}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQoNCmltcG9ydCB7IGRlYm91bmNlIH0gZnJvbSAnQC91dGlscycNCmltcG9ydCB7IEdldFRlYW1UYXNrQWxsb2NhdGlvblBhZ2VMaXN0LCBHZXRXb3JraW5nVGVhbUxvYWRSZWFsVGltZSwgQmF0Y2hBbGxvY2F0aW9uV2l0aFByZVN0ZXBUYXNrIH0gZnJvbSAnQC9hcGkvUFJPL3Byb2R1Y3Rpb24tdGFzaycNCmltcG9ydCB7IEdldFByb2Nlc3NMaXN0LCBHZXRXb3JraW5nVGVhbUJhc2UgfSBmcm9tICdAL2FwaS9QUk8vdGVjaG5vbG9neS1saWInDQppbXBvcnQgeyBHZXRXb3Jrc2hvcFBhZ2VMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL2Jhc2ljLWluZm9ybWF0aW9uL3dvcmtzaG9wJw0KaW1wb3J0IHsgR2V0Q3VyRmFjdG9yeSB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5LmpzJyAvLyDojrflj5bmmK/lkKblvIDlkK/ovabpl7TmjqXlj6MNCmltcG9ydCBnZXRUYkluZm8gZnJvbSAnQC9taXhpbnMvUFJPL2dldC10YWJsZS1pbmZvJw0KaW1wb3J0IGFkZFJvdXRlclBhZ2UgZnJvbSAnQC9taXhpbnMvYWRkLXJvdXRlci1wYWdlJw0KaW1wb3J0IGdldFByb2plY3RBcmVhVW5pdCBmcm9tICdAL3ZpZXdzL1BSTy9pbnZlbnRvcnkvcGFja2FnZS9taXhpbnMvbWl4aW5zUHJvamVjdC5qcycNCmltcG9ydCAqIGFzIGVjaGFydHMgZnJvbSAnZWNoYXJ0cycNCmltcG9ydCBQYWdpbmF0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9QYWdpbmF0aW9uL2luZGV4LnZ1ZScNCmltcG9ydCB7IHRhYmxlUGFnZVNpemUgfSBmcm9tICdAL3ZpZXdzL1BSTy9zZXR0aW5nJw0KaW1wb3J0IHsgR2V0Qk9NSW5mbywgZ2V0Qm9tQ29kZSwgY2hlY2tJc1VuaXRQYXJ0IH0gZnJvbSAnQC92aWV3cy9QUk8vYm9tLXNldHRpbmcvdXRpbHMnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ1BST1Rhc2tBbGxvY2F0aW9uTGlzdCcsDQogIGNvbXBvbmVudHM6IHsNCiAgICBQYWdpbmF0aW9uDQogIH0sDQogIG1peGluczogW2dldFRiSW5mbywgYWRkUm91dGVyUGFnZSwgZ2V0UHJvamVjdEFyZWFVbml0XSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgYm9tTGlzdDogW10sDQogICAgICBib21OYW1lOiAnJywNCiAgICAgIHNlbGVjdExpc3Q6IFtdLA0KICAgICAgdGFibGVQYWdlU2l6ZTogdGFibGVQYWdlU2l6ZSwNCiAgICAgIGFkZFBhZ2VBcnJheTogWw0KICAgICAgICB7DQogICAgICAgICAgcGF0aDogdGhpcy4kcm91dGUucGF0aCArICcvZGV0YWlsJywNCiAgICAgICAgICBoaWRkZW46IHRydWUsDQogICAgICAgICAgY29tcG9uZW50OiAoKSA9Pg0KICAgICAgICAgICAgaW1wb3J0KCdAL3ZpZXdzL1BSTy9wbGFuLXByb2R1Y3Rpb24vdGFzay1hbGxvY2F0aW9uL3Y0L2RldGFpbC52dWUnKSwNCiAgICAgICAgICBuYW1lOiAnUFJPVGFza0FsbG9jYXRpb25JbmZvJywNCiAgICAgICAgICBtZXRhOiB7IHRpdGxlOiAn6LCD5pW05YiG6YWNJyB9DQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBwYXRoOiB0aGlzLiRyb3V0ZS5wYXRoICsgJy92aWV3JywNCiAgICAgICAgICBoaWRkZW46IHRydWUsDQogICAgICAgICAgY29tcG9uZW50OiAoKSA9Pg0KICAgICAgICAgICAgaW1wb3J0KCdAL3ZpZXdzL1BSTy9wbGFuLXByb2R1Y3Rpb24vdGFzay1hbGxvY2F0aW9uL3Y0L2RldGFpbC52dWUnKSwNCiAgICAgICAgICBuYW1lOiAnUFJPVGFza0FsbG9jYXRpb25WaWV3JywNCiAgICAgICAgICBtZXRhOiB7IHRpdGxlOiAn5p+l55yL5YiG6YWNJyB9DQogICAgICAgIH0NCiAgICAgIF0sDQogICAgICBhY3RpdmVOYW1lOiBnZXRCb21Db2RlKCctMScpLCAvLyAx6Zu25Lu2ICAy5p6E5Lu2DQogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHBnTG9hZGluZzogZmFsc2UsDQogICAgICB0aXBMYWJlbDogJycsDQogICAgICB0aXRsZTogJycsDQogICAgICBjdXJyZW50Q29tcG9uZW50OiAnJywNCiAgICAgIGRXaWR0aDogJzQwJScsDQogICAgICBmb3JtOiB7DQogICAgICAgIEluc3RhbGxVbml0X0lkOiAnJywgLy8g5om55qyhSUQNCiAgICAgICAgUHJvamVjdF9JZDogJycsIC8vIOmhueebruWQjeensA0KICAgICAgICBBcmVhX0lkOiAnJywgLy8g5Yy65Z+fSUQNCiAgICAgICAgV29ya3Nob3BfSWQ6ICcnLA0KICAgICAgICBTY2hkdWxpbmdfQ29kZTogJycsDQogICAgICAgIFByb2Nlc3NfQ29kZTogJycsDQogICAgICAgIFdvcmtpbmdfVGVhbV9JZDogJycsDQogICAgICAgIEFsbG9jYXRlX1N0YXR1czogWzEsIDJdDQogICAgICB9LA0KICAgICAgcXVlcnlJbmZvOiB7DQogICAgICAgIFBhZ2U6IDEsDQogICAgICAgIFBhZ2VTaXplOiB0YWJsZVBhZ2VTaXplWzBdDQogICAgICB9LA0KICAgICAgdGJDb25maWc6IHsNCiAgICAgICAgT3BfV2lkdGg6IDE4MA0KICAgICAgfSwNCiAgICAgIGNvbHVtbnM6IFtdLA0KICAgICAgdGJEYXRhOiBbXSwNCiAgICAgIHByb2Nlc3NPcHRpb246IFtdLA0KICAgICAgZ3JvdXBPcHRpb246IFtdLA0KICAgICAgdG90YWw6IDAsDQogICAgICBzZWFyY2g6ICgpID0+ICh7fSksDQogICAgICB3b3JrU2hvcE9wdGlvbjogW10sDQogICAgICBJc19Xb3Jrc2hvcF9FbmFibGVkOiBmYWxzZSwNCiAgICAgIGRyYXdlcjogZmFsc2UsDQogICAgICBteUNoYXJ0OiBudWxsDQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIGlzQ29tKCkgew0KICAgICAgcmV0dXJuIHRoaXMuYWN0aXZlTmFtZSA9PT0gZ2V0Qm9tQ29kZSgnLTEnKQ0KICAgIH0sDQogICAgaXNVbml0UGFydCgpIHsNCiAgICAgIHJldHVybiBjaGVja0lzVW5pdFBhcnQodGhpcy5hY3RpdmVOYW1lKQ0KICAgIH0NCiAgfSwNCiAgYWN0aXZhdGVkKCkgew0KICAgIGNvbnNvbGUubG9nKCdhY3RpdmF0ZWRhY3RpdmF0ZWRhY3RpdmF0ZWQnKQ0KICAgIHRoaXMuZmV0Y2hEYXRhKCkNCiAgfSwNCiAgYXN5bmMgbW91bnRlZCgpIHsNCiAgICBjb25zdCB7IGxpc3QgfSA9IGF3YWl0IEdldEJPTUluZm8oKQ0KICAgIHRoaXMuYm9tTGlzdCA9IGxpc3QgfHwgW10NCg0KICAgIGF3YWl0IHRoaXMuZ2V0SXNXb3JrU2hvcCgpDQogICAgYXdhaXQgdGhpcy5nZXRDdXJDb2x1bW5zKCkNCiAgICB0aGlzLnNlYXJjaCA9IGRlYm91bmNlKHRoaXMuZmV0Y2hEYXRhLCA4MDAsIHRydWUpDQogICAgdGhpcy5nZXRQcm9jZXNzT3B0aW9uKCkNCiAgICB0aGlzLmdldFdvcmtzaG9wT3B0aW9uKCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGFzeW5jIGdldEN1ckNvbHVtbnMoKSB7DQogICAgICB0aGlzLmNvbHVtbnMgPSBhd2FpdCB0aGlzLmdldFRhYmxlQ29uZmlnKHRoaXMuaXNVbml0UGFydCA/ICdQUk9UYXNrVW5pdEFsbG9jYXRpb25MaXN0JyA6ICdQUk9UYXNrQWxsb2NhdGlvbkxpc3QnKQ0KICAgICAgdGhpcy5jb2x1bW5zID0gdGhpcy5jb2x1bW5zLm1hcCgoaXRlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgaWYgKGluZGV4ID09PSB0aGlzLmNvbHVtbnMubGVuZ3RoIC0gMSkgew0KICAgICAgICAgIGl0ZW0uTWluX1dpZHRoID0gMTQwDQogICAgICAgIH0NCiAgICAgICAgaWYgKGl0ZW0uQ29kZSA9PT0gJ1dvcmtzaG9wX05hbWUnKSB7DQogICAgICAgICAgaXRlbS5Jc19EaXNwbGF5ID0gdGhpcy5Jc19Xb3Jrc2hvcF9FbmFibGVkDQogICAgICAgIH0NCiAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8qKg0KICAgICAqIOiOt+WPluS7u+WKoeWIhumFjeexu+WIqw0KICAgICAqIFBhZ2Ug5YiG6aG1DQogICAgICogUGFnZVNpemUNCiAgICAgKiBmb3JtIOetm+mAiQ0KICAgICAqIGZvcm0uUHJvY2Vzc19UeXBlICAgMuaehOS7tiAgMembtuS7tg0KICAgICAqLw0KICAgIGZldGNoRGF0YShwYWdlKSB7DQogICAgICB0aGlzLmZvcm0uUHJvY2Vzc19UeXBlID0gdGhpcy5pc0NvbSA/IDIgOiB0aGlzLmlzVW5pdFBhcnQgPyAzIDogMQ0KICAgICAgdGhpcy5mb3JtLkJvbV9MZXZlbCA9IHRoaXMuYWN0aXZlTmFtZQ0KICAgICAgcGFnZSAmJiAodGhpcy5xdWVyeUluZm8uUGFnZSA9IHBhZ2UpDQogICAgICB0aGlzLnBnTG9hZGluZyA9IHRydWUNCiAgICAgIEdldFRlYW1UYXNrQWxsb2NhdGlvblBhZ2VMaXN0KHsNCiAgICAgICAgLi4udGhpcy5xdWVyeUluZm8sDQogICAgICAgIC4uLnRoaXMuZm9ybQ0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy50YkRhdGEgPSByZXMuRGF0YS5EYXRhDQogICAgICAgICAgdGhpcy50b3RhbCA9IHJlcy5EYXRhLlRvdGFsQ291bnQNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgICAgdGhpcy5wZ0xvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLyoqDQogICAgICog6I635Y+W5bel5bqPDQogICAgICogdHlwZSAgMeaehOS7tiAgMumbtuS7tiAz6YOo5Lu2DQogICAgICovDQogICAgZ2V0UHJvY2Vzc09wdGlvbigpIHsNCiAgICAgIC8vIGNvbnN0IF90eXBlID0gdHlwZU1hcFsrdGhpcy5hY3RpdmVOYW1lXQ0KICAgICAgY29uc3QgX3R5cGUgPSB0aGlzLmlzQ29tID8gMSA6IHRoaXMuaXNVbml0UGFydCA/IDMgOiAyDQogICAgICBHZXRQcm9jZXNzTGlzdCh7DQogICAgICAgIHR5cGU6IF90eXBlLA0KICAgICAgICBCb21fTGV2ZWw6IHRoaXMuYWN0aXZlTmFtZQ0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy5wcm9jZXNzT3B0aW9uID0gcmVzLkRhdGENCiAgICAgICAgICAvKiBpZiAodGhpcy5wcm9jZXNzT3B0aW9uLmxlbmd0aCkgew0KICAgICAgICAgICAgdGhpcy5mb3JtLlByb2Nlc3NfSWQgPSB0aGlzLnByb2Nlc3NPcHRpb25bMF0/LklkDQogICAgICAgICAgICB0aGlzLmdldFRlYW1PcHRpb24oKQ0KICAgICAgICAgIH0gKi8NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShhcnJheSkgew0KICAgICAgdGhpcy5zZWxlY3RMaXN0ID0gYXJyYXkucmVjb3Jkcw0KICAgIH0sDQoNCiAgICB0YXNrQ2hhbmdlKCkgew0KICAgICAgdGhpcy5mb3JtLlByb2Nlc3NfQ29kZSA9ICcnDQogICAgICB0aGlzLmZvcm0uV29ya2luZ19UZWFtX0lkID0gJycNCiAgICAgIHRoaXMuZ2V0UHJvY2Vzc09wdGlvbigpDQogICAgfSwNCg0KICAgIGdldFRlYW1PcHRpb24oKSB7DQogICAgICBsZXQgcHJvY2Vzc0lkID0gJycNCiAgICAgIGNvbnN0IGN1ciA9IHRoaXMucHJvY2Vzc09wdGlvbi5maW5kKA0KICAgICAgICAoaXRlbSkgPT4gaXRlbS5Db2RlID09PSB0aGlzLmZvcm0uUHJvY2Vzc19Db2RlDQogICAgICApDQogICAgICBpZiAoY3VyKSB7DQogICAgICAgIHByb2Nlc3NJZCA9IGN1ci5JZA0KICAgICAgfQ0KICAgICAgR2V0V29ya2luZ1RlYW1CYXNlKHsNCiAgICAgICAgcHJvY2Vzc0lkOiBwcm9jZXNzSWQNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMuZ3JvdXBPcHRpb24gPSByZXMuRGF0YQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGdldFdvcmtzaG9wT3B0aW9uKCkgew0KICAgICAgR2V0V29ya3Nob3BQYWdlTGlzdCh7IHBhZ2U6IDEsIHBhZ2VzaXplOiAtMSB9KS50aGVuKA0KICAgICAgICAocmVzKSA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIHRoaXMud29ya1Nob3BPcHRpb24gPSByZXMuRGF0YS5EYXRhDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgfSwNCiAgICBhc3luYyBnZXRJc1dvcmtTaG9wKCkgew0KICAgICAgYXdhaXQgR2V0Q3VyRmFjdG9yeSh7fSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMuSXNfV29ya3Nob3BfRW5hYmxlZCA9IHJlcy5EYXRhWzBdLklzX1dvcmtzaG9wX0VuYWJsZWQNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBwcm9jZXNzQ2hhbmdlKHYpIHsNCiAgICAgIHRoaXMuZm9ybS5Xb3JraW5nX1RlYW1fSWQgPSAnJw0KICAgICAgaWYgKCF2KSB7DQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgdGhpcy5nZXRUZWFtT3B0aW9uKCkNCiAgICB9LA0KDQogICAgdGJTZWxlY3RDaGFuZ2UoYXJyYXkpIHsgfSwNCg0KICAgIGhhbmRsZUNsb3NlKCkgew0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICB9LA0KDQogICAgaGFuZGxlUmVzZXQoKSB7DQogICAgICB0aGlzLiRyZWZzWydmb3JtJ10ucmVzZXRGaWVsZHMoKQ0KICAgICAgdGhpcy5zZWFyY2goMSkNCiAgICB9LA0KDQogICAgaGFuZGxlRGV0YWlsKHJvdykgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICBuYW1lOiAnUFJPVGFza0FsbG9jYXRpb25JbmZvJywNCiAgICAgICAgcXVlcnk6IHsNCiAgICAgICAgICBib21MZXZlbDogdGhpcy5hY3RpdmVOYW1lLA0KICAgICAgICAgIHBnX3R5cGU6IHRoaXMuaXNDb20gPyAnY29tJyA6IHRoaXMuaXNVbml0UGFydCA/ICd1bml0UGFydCcgOiAncGFydCcsDQogICAgICAgICAgcGdfcmVkaXJlY3Q6ICdQUk9UYXNrQWxsb2NhdGlvbkxpc3QnLA0KICAgICAgICAgIElzX1dvcmtzaG9wX0VuYWJsZWQ6IHRoaXMuSXNfV29ya3Nob3BfRW5hYmxlZCwNCiAgICAgICAgICBvdGhlcjogZW5jb2RlVVJJQ29tcG9uZW50KEpTT04uc3RyaW5naWZ5KHJvdykpDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIGhhbmRsZVZpZXcocm93KSB7DQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7DQogICAgICAgIG5hbWU6ICdQUk9UYXNrQWxsb2NhdGlvblZpZXcnLA0KICAgICAgICBxdWVyeTogew0KICAgICAgICAgIHR5cGU6ICd2aWV3JywNCiAgICAgICAgICBib21MZXZlbDogdGhpcy5hY3RpdmVOYW1lLA0KICAgICAgICAgIHBnX3R5cGU6IHRoaXMuaXNDb20gPyAnY29tJyA6IHRoaXMuaXNVbml0UGFydCA/ICd1bml0UGFydCcgOiAncGFydCcsDQogICAgICAgICAgcGdfcmVkaXJlY3Q6ICdQUk9UYXNrQWxsb2NhdGlvbkxpc3QnLA0KICAgICAgICAgIElzX1dvcmtzaG9wX0VuYWJsZWQ6IHRoaXMuSXNfV29ya3Nob3BfRW5hYmxlZCwNCiAgICAgICAgICBvdGhlcjogZW5jb2RlVVJJQ29tcG9uZW50KEpTT04uc3RyaW5naWZ5KHJvdykpDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOaJuemHj+S4iumBk+W3peW6j+WQjOatpQ0KICAgIGJhdGNoQWxsb2NhdGlvbldpdGhQcmVTdGVwVGFzaygpIHsNCiAgICAgIGNvbnN0IExpc3QgPSB0aGlzLnNlbGVjdExpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIEFyZWFfSWQ6IGl0ZW0uQXJlYV9JZCwNCiAgICAgICAgICBJbnN0YWxsVW5pdF9JZDogaXRlbS5JbnN0YWxsVW5pdF9JZCwNCiAgICAgICAgICBQcm9jZXNzX0NvZGU6IGl0ZW0uUHJvY2Vzc19Db2RlLA0KICAgICAgICAgIFNjaGR1bGluZ19Db2RlOiBpdGVtLlNjaGR1bGluZ19Db2RlLA0KICAgICAgICAgIFdvcmtzaG9wX05hbWU6IGl0ZW0uV29ya3Nob3BfTmFtZQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgQmF0Y2hBbGxvY2F0aW9uV2l0aFByZVN0ZXBUYXNrKHsNCiAgICAgICAgUHJvY2Vzc19UeXBlOiB0aGlzLmlzQ29tID8gMiA6IHRoaXMuaXNVbml0UGFydCA/IDMgOiAxLA0KICAgICAgICBCb21fTGV2ZWw6IHRoaXMuYWN0aXZlTmFtZSwNCiAgICAgICAgTGlzdA0KICAgICAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogJ+aTjeS9nOaIkOWKnycsDQogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycNCiAgICAgICAgICB9KQ0KICAgICAgICAgIHRoaXMuZmV0Y2hEYXRhKCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOWIh+aNonRhYg0KICAgIGhhbmRsZVRhYnNDbGljayh0YWIsIGV2ZW50KSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIEluc3RhbGxVbml0X0lkOiAnJywNCiAgICAgICAgUHJvamVjdF9JZDogJycsDQogICAgICAgIEFyZWFfSWQ6ICcnLA0KICAgICAgICBBbGxvY2F0ZV9TdGF0dXM6IFsxLCAyXSwNCiAgICAgICAgU2NoZHVsaW5nX0NvZGU6ICcnLA0KICAgICAgICBQcm9jZXNzX0NvZGU6ICcnLA0KICAgICAgICBXb3JraW5nX1RlYW1fSWQ6ICcnDQogICAgICB9DQogICAgICB0aGlzLmdldEN1ckNvbHVtbnMoKQ0KICAgICAgdGhpcy5nZXRQcm9jZXNzT3B0aW9uKCkNCiAgICAgIHRoaXMuZmV0Y2hEYXRhKCkNCiAgICB9LA0KICAgIC8vIGRyYXdlcg0KICAgIGRyYXdlck9wZW4oKSB7DQogICAgICB0aGlzLmRyYXdlciA9IHRydWUNCiAgICAgIGNvbnN0IHhBeGlzRGF0YSA9IFtdDQogICAgICBjb25zdCBkYXRhMSA9IFtdDQogICAgICBjb25zdCBkYXRhMiA9IFtdDQogICAgICBHZXRXb3JraW5nVGVhbUxvYWRSZWFsVGltZSh7DQoNCiAgICAgICAgdHlwZTogdGhpcy5pc0NvbSA/IDEgOiB0aGlzLmlzVW5pdFBhcnQgPyAzIDogMiwNCiAgICAgICAgQm9tX0xldmVsOiB0aGlzLmFjdGl2ZU5hbWUNCiAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICBjb25zb2xlLmxvZyhyZXMpDQogICAgICAgICAgaWYgKHJlcy5EYXRhICYmIHJlcy5EYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIHJlcy5EYXRhLm1hcChpID0+IHsNCiAgICAgICAgICAgICAgeEF4aXNEYXRhLnB1c2goaS5OYW1lKQ0KICAgICAgICAgICAgICBkYXRhMS5wdXNoKGkuTG9hZCA/PyAwKQ0KICAgICAgICAgICAgICBkYXRhMi5wdXNoKGkuUmVhbF9UaW1lX0xvYWQgPz8gMCkNCiAgICAgICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdpbicpDQogICAgICAgICAgICAgICAgY29uc3QgY2hhcnREb20gPSB0aGlzLiRyZWZzLmNoYXJ0RG9tDQogICAgICAgICAgICAgICAgaWYgKHRoaXMubXlDaGFydCA9PSBudWxsKSB7DQogICAgICAgICAgICAgICAgICB0aGlzLm15Q2hhcnQgPSBlY2hhcnRzLmluaXQoY2hhcnREb20pDQogICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgY29uc3QgZW1waGFzaXNTdHlsZSA9IHsNCiAgICAgICAgICAgICAgICAgIGl0ZW1TdHlsZTogew0KICAgICAgICAgICAgICAgICAgICBzaGFkb3dCbHVyOiAxMCwNCiAgICAgICAgICAgICAgICAgICAgc2hhZG93Q29sb3I6ICdyZ2JhKDAsMCwwLDAuMyknDQogICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIGNvbnN0IGVjaGFydE9wdGlvbiA9IHsNCiAgICAgICAgICAgICAgICAgIHRpdGxlOiB7DQogICAgICAgICAgICAgICAgICAgIHRleHQ6ICfnj63nu4TotJ/ojbflrp7ml7bmg4XlhrUnLA0KICAgICAgICAgICAgICAgICAgICB0ZXh0U3R5bGU6IHsNCiAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogMTYsDQogICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICcjMjIyODM0Jw0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgICAgICAgICAgICBzaG93OiB0cnVlLA0KICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycNCiAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICBsZWdlbmQ6IHsNCiAgICAgICAgICAgICAgICAgICAgaWNvbjogJ3JlY3QnLA0KICAgICAgICAgICAgICAgICAgICBpdGVtV2lkdGg6IDgsDQogICAgICAgICAgICAgICAgICAgIGl0ZW1IZWlnaHQ6IDQsDQogICAgICAgICAgICAgICAgICAgIGRhdGE6IFtdLA0KICAgICAgICAgICAgICAgICAgICB0ZXh0U3R5bGU6IHsNCiAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogMTIsDQogICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICcjOTk5OTk5ICcNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgIGdyaWQ6IHsNCiAgICAgICAgICAgICAgICAgICAgbGVmdDogJzMlJywNCiAgICAgICAgICAgICAgICAgICAgcmlnaHQ6ICc0JScsDQogICAgICAgICAgICAgICAgICAgIGJvdHRvbTogJzMlJywNCiAgICAgICAgICAgICAgICAgICAgY29udGFpbkxhYmVsOiB0cnVlDQogICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgeEF4aXM6IHsNCiAgICAgICAgICAgICAgICAgICAgZGF0YTogeEF4aXNEYXRhLA0KICAgICAgICAgICAgICAgICAgICBheGlzTGluZTogeyBvblplcm86IHRydWUgfSwNCiAgICAgICAgICAgICAgICAgICAgc3BsaXRMaW5lOiB7IHNob3c6IGZhbHNlIH0sDQogICAgICAgICAgICAgICAgICAgIHNwbGl0QXJlYTogeyBzaG93OiBmYWxzZSB9DQogICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgeUF4aXM6IHsNCg0KICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgIHNlcmllczogWw0KICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgbmFtZTogJ+i0n+iNt+aPkOmGkue6vycsDQogICAgICAgICAgICAgICAgICAgICAgdHlwZTogJ2JhcicsDQogICAgICAgICAgICAgICAgICAgICAgYmFyR2FwOiAnLTEwMCUnLA0KICAgICAgICAgICAgICAgICAgICAgIGVtcGhhc2lzOiBlbXBoYXNpc1N0eWxlLA0KICAgICAgICAgICAgICAgICAgICAgIGRhdGE6IGRhdGExLA0KICAgICAgICAgICAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogJyM5MWNjNzUnIH0NCiAgICAgICAgICAgICAgICAgICAgfSwgew0KICAgICAgICAgICAgICAgICAgICAgIG5hbWU6ICflvZPliY3otJ/ojbcnLA0KICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICdiYXInLA0KICAgICAgICAgICAgICAgICAgICAgIGJhckdhcDogJy0xMDAlJywNCiAgICAgICAgICAgICAgICAgICAgICBlbXBoYXNpczogZW1waGFzaXNTdHlsZSwNCiAgICAgICAgICAgICAgICAgICAgICBkYXRhOiBkYXRhMiwNCiAgICAgICAgICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6ICcjNTQ3MEM2JyB9DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgIF0NCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgdGhpcy5teUNoYXJ0LnNldE9wdGlvbihlY2hhcnRPcHRpb24pDQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8PA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/plan-production/task-allocation/v4", "sourcesContent": ["<template>\r\n  <div v-loading=\"pgLoading\" class=\"container abs100\">\r\n    <el-tabs v-model=\"activeName\" @tab-click=\"handleTabsClick\">\r\n      <!-- <el-tab-pane label=\"构件\" name=\"2\" />\r\n      <el-tab-pane label=\"部件\" name=\"3\" />\r\n      <el-tab-pane label=\"零件\" name=\"1\" /> -->\r\n      <el-tab-pane v-for=\"item in bomList\" :key=\"item.Code\" :label=\"item.Display_Name\" :name=\"item.Code\" />\r\n    </el-tabs>\r\n    <div class=\"search-wrapper\">\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"80px\" class=\"demo-form-inline\">\r\n        <el-row>\r\n          <el-col :span=\"6\" :lg=\"6\" :xl=\"6\">\r\n            <el-form-item label=\"项目名称\" prop=\"Project_Id\">\r\n              <el-select\r\n                ref=\"ProjectName\"\r\n                v-model=\"form.Project_Id\"\r\n                filterable\r\n                clearable\r\n                placeholder=\"请选择\"\r\n                @change=\"projectChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in ProjectNameData\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Short_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\" :lg=\"6\" :xl=\"6\">\r\n            <el-form-item label=\"区域\" prop=\"Area_Id\">\r\n              <el-tree-select\r\n                ref=\"treeSelectArea\"\r\n                v-model=\"form.Area_Id\"\r\n                :disabled=\"!form.Project_Id\"\r\n                :select-params=\"{\r\n                  clearable: true,\r\n                }\"\r\n                class=\"cs-tree-x\"\r\n                :tree-params=\"treeParamsArea\"\r\n                @select-clear=\"areaClear\"\r\n                @node-click=\"areaChange\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\" :lg=\"6\" :xl=\"6\">\r\n            <template>\r\n              <el-form-item label=\"批次\" prop=\"InstallUnit_Id\">\r\n                <el-select\r\n                  ref=\"SetupPosition\"\r\n                  v-model=\"form.InstallUnit_Id\"\r\n                  :disabled=\"!form.Area_Id\"\r\n                  clearable\r\n                  placeholder=\"请选择\"\r\n                  @change=\"setupPositionChange\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in SetupPositionData\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Name\"\r\n                    :value=\"item.Id\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </template>\r\n          </el-col>\r\n          <el-col :span=\"6\" :lg=\"6\" :xl=\"6\">\r\n            <el-form-item label=\"排产单号\" prop=\"Schduling_Code\">\r\n              <el-input v-model=\"form.Schduling_Code\" type=\"text\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"4\" :lg=\"4\" :xl=\"4\">\r\n            <el-form-item label=\"任务工序\" prop=\"Process_Code\">\r\n              <el-select\r\n                v-model=\"form.Process_Code\"\r\n                clearable\r\n                placeholder=\"请选择\"\r\n                style=\"width: 100%\"\r\n                @change=\"processChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in processOption\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Name\"\r\n                  :value=\"item.Code\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\" :lg=\"4\" :xl=\"4\">\r\n            <el-form-item label=\"加工班组\" prop=\"Working_Team_Id\">\r\n              <el-select\r\n                v-model=\"form.Working_Team_Id\"\r\n                :disabled=\"!form.Process_Code\"\r\n                clearable\r\n                placeholder=\"请选择\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in groupOption\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\" :lg=\"4\" :xl=\"4\">\r\n            <el-form-item\r\n              v-if=\"Is_Workshop_Enabled\"\r\n              label=\"所属车间\"\r\n              prop=\"Workshop_Name\"\r\n            >\r\n              <el-select\r\n                v-model=\"form.Workshop_Id\"\r\n                clearable\r\n                placeholder=\"请选择\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in workShopOption\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\" :lg=\"6\" :xl=\"6\">\r\n            <el-form-item label=\"是否分配\" prop=\"Allocate_Status\">\r\n              <el-select v-model=\"form.Allocate_Status\" multiple placeholder=\"请选择\" clearable=\"\">\r\n                <!--            <el-option label=\"全部\" value=\"\" />-->\r\n                <el-option label=\"未分配\" :value=\"1\" />\r\n                <el-option label=\"已分配\" :value=\"2\" />\r\n                <el-option label=\"分配完成\" :value=\"3\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\" :lg=\"6\" :xl=\"6\">\r\n            <el-form-item label-width=\"16px\">\r\n              <el-button @click=\"handleReset\">重置</el-button>\r\n              <el-button type=\"primary\" @click=\"search(1)\">搜索</el-button>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div>\r\n        <el-button type=\"primary\" @click=\"drawerOpen\">班组负荷</el-button>\r\n        <el-button type=\"primary\" :disabled=\"!selectList.length\" @click=\"batchAllocationWithPreStepTask\">上道工序同步</el-button>\r\n      </div>\r\n    </div>\r\n    <div class=\"main-wrapper\">\r\n      <div class=\"tb-x\">\r\n        <vxe-table\r\n          :key=\"activeName\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          empty-text=\"暂无数据\"\r\n          height=\"auto\"\r\n          show-overflow\r\n          :row-config=\"{isCurrent: true, isHover: true}\"\r\n          :loading=\"pgLoading\"\r\n          class=\"cs-vxe-table\"\r\n          align=\"left\"\r\n          stripe\r\n          :data=\"tbData\"\r\n          resizable\r\n          :tooltip-config=\"{ enterable: true }\"\r\n          @checkbox-all=\"handleSelectionChange\"\r\n          @checkbox-change=\"handleSelectionChange\"\r\n        >\r\n          <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n          <vxe-column\r\n            v-for=\"(item, index) in columns\"\r\n            :key=\"item.Code\"\r\n            :align=\"item.Align\"\r\n            :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n            show-overflow=\"tooltip\"\r\n            sortable\r\n            :field=\"item.Code\"\r\n            :title=\"item.Display_Name\"\r\n            :min-width=\"item.Width||120\"\r\n            :visible=\"item.visible\"\r\n          >\r\n            <template v-if=\"item.Code==='Total_Allocation_Count'\" #default=\"{ row }\">\r\n              {{ row.Total_Allocation_Count - row.Total_Receive_Count === row.Can_Allocation_Count ?'分配完成' :row.Total_Allocation_Count > 0? '已分配':'未分配' }}\r\n            </template>\r\n            <template v-else-if=\"item.Code==='Finish_Date'||item.Code==='Order_Date'\" #default=\"{ row }\">\r\n              {{ row[item.Code] | timeFormat }}\r\n            </template>\r\n            <template v-else #default=\"{ row }\">\r\n              <span>{{ (row[item.Code] ===0 ?0 : row[item.Code]) | displayValue }}</span>\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column title=\"操作\" fixed=\"right\" width=\"125\">\r\n            <template #default=\"{ row , rowIndex }\">\r\n              <el-button type=\"text\" @click=\"handleView(row)\">查看</el-button>\r\n              <el-button\r\n                v-if=\"row.Can_Allocation_Count !== 0\"\r\n                type=\"text\"\r\n                @click=\"handleDetail(row)\"\r\n              >任务分配\r\n              </el-button>\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n      </div>\r\n      <Pagination\r\n        :total=\"total\"\r\n        :page-sizes=\"tablePageSize\"\r\n        :page.sync=\"queryInfo.Page\"\r\n        :limit.sync=\"queryInfo.PageSize\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\"\r\n        @pagination=\"pageChange\"\r\n      />\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"dWidth\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        @close=\"handleClose\"\r\n        @refresh=\"fetchData(1)\"\r\n      />\r\n    </el-dialog>\r\n    <el-drawer\r\n      size=\"60%\"\r\n      custom-class=\"drawerBox\"\r\n      :visible.sync=\"drawer\"\r\n      direction=\"btt\"\r\n      :with-header=\"false\"\r\n      append-to-body\r\n      wrapper-closable\r\n    >\r\n      <div class=\"chartWrapper\">\r\n        <div ref=\"chartDom\" style=\"width: 100%; height: 100%\" />\r\n      </div>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { debounce } from '@/utils'\r\nimport { GetTeamTaskAllocationPageList, GetWorkingTeamLoadRealTime, BatchAllocationWithPreStepTask } from '@/api/PRO/production-task'\r\nimport { GetProcessList, GetWorkingTeamBase } from '@/api/PRO/technology-lib'\r\nimport { GetWorkshopPageList } from '@/api/PRO/basic-information/workshop'\r\nimport { GetCurFactory } from '@/api/PRO/factory.js' // 获取是否开启车间接口\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nimport getProjectAreaUnit from '@/views/PRO/inventory/package/mixins/mixinsProject.js'\r\nimport * as echarts from 'echarts'\r\nimport Pagination from '@/components/Pagination/index.vue'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport { GetBOMInfo, getBomCode, checkIsUnitPart } from '@/views/PRO/bom-setting/utils'\r\n\r\nexport default {\r\n  name: 'PROTaskAllocationList',\r\n  components: {\r\n    Pagination\r\n  },\r\n  mixins: [getTbInfo, addRouterPage, getProjectAreaUnit],\r\n  data() {\r\n    return {\r\n      bomList: [],\r\n      bomName: '',\r\n      selectList: [],\r\n      tablePageSize: tablePageSize,\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/detail',\r\n          hidden: true,\r\n          component: () =>\r\n            import('@/views/PRO/plan-production/task-allocation/v4/detail.vue'),\r\n          name: 'PROTaskAllocationInfo',\r\n          meta: { title: '调整分配' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/view',\r\n          hidden: true,\r\n          component: () =>\r\n            import('@/views/PRO/plan-production/task-allocation/v4/detail.vue'),\r\n          name: 'PROTaskAllocationView',\r\n          meta: { title: '查看分配' }\r\n        }\r\n      ],\r\n      activeName: getBomCode('-1'), // 1零件  2构件\r\n      dialogVisible: false,\r\n      pgLoading: false,\r\n      tipLabel: '',\r\n      title: '',\r\n      currentComponent: '',\r\n      dWidth: '40%',\r\n      form: {\r\n        InstallUnit_Id: '', // 批次ID\r\n        Project_Id: '', // 项目名称\r\n        Area_Id: '', // 区域ID\r\n        Workshop_Id: '',\r\n        Schduling_Code: '',\r\n        Process_Code: '',\r\n        Working_Team_Id: '',\r\n        Allocate_Status: [1, 2]\r\n      },\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: tablePageSize[0]\r\n      },\r\n      tbConfig: {\r\n        Op_Width: 180\r\n      },\r\n      columns: [],\r\n      tbData: [],\r\n      processOption: [],\r\n      groupOption: [],\r\n      total: 0,\r\n      search: () => ({}),\r\n      workShopOption: [],\r\n      Is_Workshop_Enabled: false,\r\n      drawer: false,\r\n      myChart: null\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.activeName === getBomCode('-1')\r\n    },\r\n    isUnitPart() {\r\n      return checkIsUnitPart(this.activeName)\r\n    }\r\n  },\r\n  activated() {\r\n    console.log('activatedactivatedactivated')\r\n    this.fetchData()\r\n  },\r\n  async mounted() {\r\n    const { list } = await GetBOMInfo()\r\n    this.bomList = list || []\r\n\r\n    await this.getIsWorkShop()\r\n    await this.getCurColumns()\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    this.getProcessOption()\r\n    this.getWorkshopOption()\r\n  },\r\n  methods: {\r\n    async getCurColumns() {\r\n      this.columns = await this.getTableConfig(this.isUnitPart ? 'PROTaskUnitAllocationList' : 'PROTaskAllocationList')\r\n      this.columns = this.columns.map((item, index) => {\r\n        if (index === this.columns.length - 1) {\r\n          item.Min_Width = 140\r\n        }\r\n        if (item.Code === 'Workshop_Name') {\r\n          item.Is_Display = this.Is_Workshop_Enabled\r\n        }\r\n        return item\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 获取任务分配类别\r\n     * Page 分页\r\n     * PageSize\r\n     * form 筛选\r\n     * form.Process_Type   2构件  1零件\r\n     */\r\n    fetchData(page) {\r\n      this.form.Process_Type = this.isCom ? 2 : this.isUnitPart ? 3 : 1\r\n      this.form.Bom_Level = this.activeName\r\n      page && (this.queryInfo.Page = page)\r\n      this.pgLoading = true\r\n      GetTeamTaskAllocationPageList({\r\n        ...this.queryInfo,\r\n        ...this.form\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.Data\r\n          this.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.pgLoading = false\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 获取工序\r\n     * type  1构件  2零件 3部件\r\n     */\r\n    getProcessOption() {\r\n      // const _type = typeMap[+this.activeName]\r\n      const _type = this.isCom ? 1 : this.isUnitPart ? 3 : 2\r\n      GetProcessList({\r\n        type: _type,\r\n        Bom_Level: this.activeName\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.processOption = res.Data\r\n          /* if (this.processOption.length) {\r\n            this.form.Process_Id = this.processOption[0]?.Id\r\n            this.getTeamOption()\r\n          } */\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    handleSelectionChange(array) {\r\n      this.selectList = array.records\r\n    },\r\n\r\n    taskChange() {\r\n      this.form.Process_Code = ''\r\n      this.form.Working_Team_Id = ''\r\n      this.getProcessOption()\r\n    },\r\n\r\n    getTeamOption() {\r\n      let processId = ''\r\n      const cur = this.processOption.find(\r\n        (item) => item.Code === this.form.Process_Code\r\n      )\r\n      if (cur) {\r\n        processId = cur.Id\r\n      }\r\n      GetWorkingTeamBase({\r\n        processId: processId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.groupOption = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getWorkshopOption() {\r\n      GetWorkshopPageList({ page: 1, pagesize: -1 }).then(\r\n        (res) => {\r\n          if (res.IsSucceed) {\r\n            this.workShopOption = res.Data.Data\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n    },\r\n    async getIsWorkShop() {\r\n      await GetCurFactory({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.Is_Workshop_Enabled = res.Data[0].Is_Workshop_Enabled\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    processChange(v) {\r\n      this.form.Working_Team_Id = ''\r\n      if (!v) {\r\n        return\r\n      }\r\n      this.getTeamOption()\r\n    },\r\n\r\n    tbSelectChange(array) { },\r\n\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n\r\n    handleReset() {\r\n      this.$refs['form'].resetFields()\r\n      this.search(1)\r\n    },\r\n\r\n    handleDetail(row) {\r\n      this.$router.push({\r\n        name: 'PROTaskAllocationInfo',\r\n        query: {\r\n          bomLevel: this.activeName,\r\n          pg_type: this.isCom ? 'com' : this.isUnitPart ? 'unitPart' : 'part',\r\n          pg_redirect: 'PROTaskAllocationList',\r\n          Is_Workshop_Enabled: this.Is_Workshop_Enabled,\r\n          other: encodeURIComponent(JSON.stringify(row))\r\n        }\r\n      })\r\n    },\r\n\r\n    handleView(row) {\r\n      this.$router.push({\r\n        name: 'PROTaskAllocationView',\r\n        query: {\r\n          type: 'view',\r\n          bomLevel: this.activeName,\r\n          pg_type: this.isCom ? 'com' : this.isUnitPart ? 'unitPart' : 'part',\r\n          pg_redirect: 'PROTaskAllocationList',\r\n          Is_Workshop_Enabled: this.Is_Workshop_Enabled,\r\n          other: encodeURIComponent(JSON.stringify(row))\r\n        }\r\n      })\r\n    },\r\n\r\n    // 批量上道工序同步\r\n    batchAllocationWithPreStepTask() {\r\n      const List = this.selectList.map(item => {\r\n        return {\r\n          Area_Id: item.Area_Id,\r\n          InstallUnit_Id: item.InstallUnit_Id,\r\n          Process_Code: item.Process_Code,\r\n          Schduling_Code: item.Schduling_Code,\r\n          Workshop_Name: item.Workshop_Name\r\n        }\r\n      })\r\n      BatchAllocationWithPreStepTask({\r\n        Process_Type: this.isCom ? 2 : this.isUnitPart ? 3 : 1,\r\n        Bom_Level: this.activeName,\r\n        List\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '操作成功',\r\n            type: 'success'\r\n          })\r\n          this.fetchData()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 切换tab\r\n    handleTabsClick(tab, event) {\r\n      this.form = {\r\n        InstallUnit_Id: '',\r\n        Project_Id: '',\r\n        Area_Id: '',\r\n        Allocate_Status: [1, 2],\r\n        Schduling_Code: '',\r\n        Process_Code: '',\r\n        Working_Team_Id: ''\r\n      }\r\n      this.getCurColumns()\r\n      this.getProcessOption()\r\n      this.fetchData()\r\n    },\r\n    // drawer\r\n    drawerOpen() {\r\n      this.drawer = true\r\n      const xAxisData = []\r\n      const data1 = []\r\n      const data2 = []\r\n      GetWorkingTeamLoadRealTime({\r\n\r\n        type: this.isCom ? 1 : this.isUnitPart ? 3 : 2,\r\n        Bom_Level: this.activeName\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          console.log(res)\r\n          if (res.Data && res.Data.length > 0) {\r\n            res.Data.map(i => {\r\n              xAxisData.push(i.Name)\r\n              data1.push(i.Load ?? 0)\r\n              data2.push(i.Real_Time_Load ?? 0)\r\n              this.$nextTick(() => {\r\n                console.log('in')\r\n                const chartDom = this.$refs.chartDom\r\n                if (this.myChart == null) {\r\n                  this.myChart = echarts.init(chartDom)\r\n                }\r\n\r\n                const emphasisStyle = {\r\n                  itemStyle: {\r\n                    shadowBlur: 10,\r\n                    shadowColor: 'rgba(0,0,0,0.3)'\r\n                  }\r\n                }\r\n                const echartOption = {\r\n                  title: {\r\n                    text: '班组负荷实时情况',\r\n                    textStyle: {\r\n                      fontSize: 16,\r\n                      color: '#222834'\r\n                    }\r\n                  },\r\n                  tooltip: {\r\n                    show: true,\r\n                    trigger: 'axis'\r\n                  },\r\n                  legend: {\r\n                    icon: 'rect',\r\n                    itemWidth: 8,\r\n                    itemHeight: 4,\r\n                    data: [],\r\n                    textStyle: {\r\n                      fontSize: 12,\r\n                      color: '#999999 '\r\n                    }\r\n                  },\r\n                  grid: {\r\n                    left: '3%',\r\n                    right: '4%',\r\n                    bottom: '3%',\r\n                    containLabel: true\r\n                  },\r\n                  xAxis: {\r\n                    data: xAxisData,\r\n                    axisLine: { onZero: true },\r\n                    splitLine: { show: false },\r\n                    splitArea: { show: false }\r\n                  },\r\n                  yAxis: {\r\n\r\n                  },\r\n                  series: [\r\n                    {\r\n                      name: '负荷提醒线',\r\n                      type: 'bar',\r\n                      barGap: '-100%',\r\n                      emphasis: emphasisStyle,\r\n                      data: data1,\r\n                      itemStyle: { color: '#91cc75' }\r\n                    }, {\r\n                      name: '当前负荷',\r\n                      type: 'bar',\r\n                      barGap: '-100%',\r\n                      emphasis: emphasisStyle,\r\n                      data: data2,\r\n                      itemStyle: { color: '#5470C6' }\r\n                    }\r\n                  ]\r\n                }\r\n                this.myChart.setOption(echartOption)\r\n              })\r\n            })\r\n          }\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.container {\r\n  padding: 16px;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .search-wrapper {\r\n    padding: 16px 16px 16px 16px;\r\n    background: #ffffff;\r\n    border-radius: 4px 4px 4px 4px;\r\n\r\n    // ::v-deep .el-form-item__content {\r\n    //   width: 197px;\r\n    // }\r\n    ::v-deep .el-form-item {\r\n      .el-form-item__content {\r\n        & > .el-input {\r\n          width: 100%;\r\n        }\r\n\r\n        & > .el-select {\r\n          width: 100%;\r\n        }\r\n        & > .el-date-editor {\r\n          width: 100%;\r\n        }\r\n\r\n        .el-tree-select-input {\r\n          width: 100% !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .el-tabs{\r\n    margin-bottom: 16px;\r\n    background-color: #ffffff;\r\n    padding-left: 16px;\r\n    width: 100%;\r\n  }\r\n  ::v-deep .pagination {\r\n    justify-content: flex-end !important;\r\n    margin-top: 12px !important;\r\n\r\n    .el-input--small .el-input__inner {\r\n      height: 28px;\r\n      line-height: 28px;\r\n    }\r\n  }\r\n}\r\n\r\n.main-wrapper {\r\n  background: #ffffff;\r\n  margin-top: 16px;\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  border-radius: 4px 4px 4px 4px;\r\n  padding: 16px 16px 0;\r\n  overflow:hidden;\r\n\r\n  .tb-x{\r\n    flex: 1;\r\n    height: 0;\r\n  }\r\n  .btn-wrapper {\r\n    padding-bottom: 16px;\r\n  }\r\n}\r\n\r\n.plm-custom-dialog {\r\n  ::v-deep {\r\n    .el-dialog .el-dialog__body {\r\n      height: 70vh;\r\n    }\r\n  }\r\n}\r\n.drawerBox {\r\n  .chartWrapper {\r\n    padding: 20px;\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n}\r\n.pagination-container{\r\n  padding: 0;\r\n  margin: 10px 0;\r\n  text-align: right;\r\n}\r\n</style>\r\n"]}]}
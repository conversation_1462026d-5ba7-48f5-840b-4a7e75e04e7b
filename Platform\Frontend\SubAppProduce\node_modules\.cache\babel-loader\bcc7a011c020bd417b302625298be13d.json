{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-unit-part\\draft.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-unit-part\\draft.vue", "mtime": 1758266753120}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF90eXBlb2YgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS90eXBlb2YuanMiOwppbXBvcnQgX2RlZmluZVByb3BlcnR5IGZyb20gIkQ6L3Byb2plY3QvcGxhdGZvcm1fZnJhbWV3b3JrX21hc3Rlci9wbGF0Zm9ybV9mcmFtZXdvcmsvUGxhdGZvcm0vRnJvbnRlbmQvU3ViQXBwUHJvZHVjZS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZGVmaW5lUHJvcGVydHkuanMiOwppbXBvcnQgX3RvQ29uc3VtYWJsZUFycmF5IGZyb20gIkQ6L3Byb2plY3QvcGxhdGZvcm1fZnJhbWV3b3JrX21hc3Rlci9wbGF0Zm9ybV9mcmFtZXdvcmsvUGxhdGZvcm0vRnJvbnRlbmQvU3ViQXBwUHJvZHVjZS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vdG9Db25zdW1hYmxlQXJyYXkuanMiOwppbXBvcnQgX3JlZ2VuZXJhdG9yUnVudGltZSBmcm9tICJEOi9wcm9qZWN0L3BsYXRmb3JtX2ZyYW1ld29ya19tYXN0ZXIvcGxhdGZvcm1fZnJhbWV3b3JrL1BsYXRmb3JtL0Zyb250ZW5kL1N1YkFwcFByb2R1Y2Uvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3JlZ2VuZXJhdG9yUnVudGltZS5qcyI7CmltcG9ydCBfYXN5bmNUb0dlbmVyYXRvciBmcm9tICJEOi9wcm9qZWN0L3BsYXRmb3JtX2ZyYW1ld29ya19tYXN0ZXIvcGxhdGZvcm1fZnJhbWV3b3JrL1BsYXRmb3JtL0Zyb250ZW5kL1N1YkFwcFByb2R1Y2Uvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2FzeW5jVG9HZW5lcmF0b3IuanMiOwppbXBvcnQgX29iamVjdFNwcmVhZCBmcm9tICJEOi9wcm9qZWN0L3BsYXRmb3JtX2ZyYW1ld29ya19tYXN0ZXIvcGxhdGZvcm1fZnJhbWV3b3JrL1BsYXRmb3JtL0Zyb250ZW5kL1N1YkFwcFByb2R1Y2Uvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5jb25jYXQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maWx0ZXIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maW5kLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmluZC1pbmRleC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZyb20uanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5pbmNsdWRlcy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmpvaW4uanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5wdXNoLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucmVkdWNlLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuc29ydC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnNwbGljZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuZXZlcnkuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5maWx0ZXIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5maW5kLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuZm9yLWVhY2guanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5tYXAuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5yZWR1Y2UuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5zb21lLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuanNvbi5zdHJpbmdpZnkuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3Qua2V5cy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zZXQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zZXQuZGlmZmVyZW5jZS52Mi5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnNldC5pbnRlcnNlY3Rpb24udjIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zZXQuaXMtZGlzam9pbnQtZnJvbS52Mi5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnNldC5pcy1zdWJzZXQtb2YudjIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zZXQuaXMtc3VwZXJzZXQtb2YudjIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zZXQuc3ltbWV0cmljLWRpZmZlcmVuY2UudjIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zZXQudW5pb24udjIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuZW5kcy13aXRoLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLmluY2x1ZGVzLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLml0ZXJhdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnJlcGxhY2UuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuc2VhcmNoLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnNwbGl0LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnN0YXJ0cy13aXRoLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnRyaW0uanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5pdGVyYXRvci5qcyI7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCgppbXBvcnQgeyBjbG9zZVRhZ1ZpZXcsIGRlYm91bmNlIH0gZnJvbSAnQC91dGlscyc7CmltcG9ydCBCYXRjaFByb2Nlc3NBZGp1c3QgZnJvbSAnLi9jb21wb25lbnRzL0JhdGNoUHJvY2Vzc0FkanVzdCc7CmltcG9ydCB7IEdldENhblNjaGR1bGluZ1BhcnRMaXN0LCBHZXREd2csIEdldFNjaGR1bGluZ1dvcmtpbmdUZWFtcywgU2F2ZVVuaXRTY2hlZHVsaW5nV29ya3Nob3BOZXcsIFNhdmVTY2hkdWxpbmdUYXNrQnlJZCwgU2F2ZVVuaXRTY2hkdWxpbmdEcmFmdE5ldywgR2V0VW5pdFNjaGR1bGluZ0luZm9EZXRhaWwgfSBmcm9tICdAL2FwaS9QUk8vcHJvZHVjdGlvbi10YXNrJzsKaW1wb3J0IHsgR2V0U3RvcExpc3QgfSBmcm9tICdAL2FwaS9QUk8vcHJvZHVjdGlvbi10YXNrJzsKaW1wb3J0IEFkZERyYWZ0IGZyb20gJy4vY29tcG9uZW50cy9hZGREcmFmdCc7CmltcG9ydCBPd25lclByb2Nlc3MgZnJvbSAnLi9jb21wb25lbnRzL093bmVyUHJvY2Vzcyc7CmltcG9ydCBXb3Jrc2hvcCBmcm9tICcuL2NvbXBvbmVudHMvV29ya3Nob3AudnVlJzsKaW1wb3J0IHsgR2V0R3JpZEJ5Q29kZSB9IGZyb20gJ0AvYXBpL3N5cyc7CmltcG9ydCB7IGdldFVuaXF1ZSwgdW5pcXVlQ29kZSB9IGZyb20gJy4vY29uc3RhbnQnOwppbXBvcnQgeyB2NCBhcyB1dWlkdjQgfSBmcm9tICd1dWlkJzsKaW1wb3J0IG51bWVyYWwgZnJvbSAnbnVtZXJhbCc7CmltcG9ydCB7IEdldExpYkxpc3RUeXBlLCBHZXRQcm9jZXNzRmxvd0xpc3RXaXRoVGVjaG5vbG9neSwgR2V0UHJvY2Vzc0xpc3RCYXNlIH0gZnJvbSAnQC9hcGkvUFJPL3RlY2hub2xvZ3ktbGliJzsKaW1wb3J0IHsgQXJlYUdldEVudGl0eSB9IGZyb20gJ0AvYXBpL3BsbS9wcm9qZWN0cyc7CmltcG9ydCB7IG1hcEFjdGlvbnMsIG1hcEdldHRlcnMgfSBmcm9tICd2dWV4JzsKaW1wb3J0IHsgR2V0UGFydFR5cGVMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL3BhcnRUeXBlJzsKaW1wb3J0IG1vbWVudCBmcm9tICdtb21lbnQnOwppbXBvcnQgRXhwYW5kYWJsZVNlY3Rpb24gZnJvbSAnQC9jb21wb25lbnRzL0V4cGFuZGFibGVTZWN0aW9uL2luZGV4LnZ1ZSc7CmltcG9ydCBUcmVlRGV0YWlsIGZyb20gJ0AvY29tcG9uZW50cy9UcmVlRGV0YWlsL2luZGV4LnZ1ZSc7CmltcG9ydCB7IEdldEluc3RhbGxVbml0SWROYW1lTGlzdCwgR2V0UHJvamVjdEFyZWFUcmVlTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9wcm9qZWN0JzsKaW1wb3J0IHsgR2V0Q29tcFR5cGVUcmVlIH0gZnJvbSAnQC9hcGkvUFJPL2ZhY3RvcnljaGVjayc7CmltcG9ydCB7IHBhcnNlT3NzVXJsIH0gZnJvbSAnQC91dGlscy9maWxlJzsKaW1wb3J0IER5bmFtaWNUYWJsZUZpZWxkcyBmcm9tICdAL2NvbXBvbmVudHMvRHluYW1pY1RhYmxlRmllbGRzL2luZGV4LnZ1ZSc7CmltcG9ydCB7IGdldENvbmZpZ3VyZSB9IGZyb20gJ0AvYXBpL3VzZXInOwppbXBvcnQgeyBiYXNlVXJsIH0gZnJvbSAnQC91dGlscy9iYXNldXJsJzsKaW1wb3J0IHsgR2V0U3RlZWxDYWRBbmRCaW1JZCB9IGZyb20gJ0AvYXBpL1BSTy9jb21wb25lbnQnOwppbXBvcnQgeyBnZXRCb21OYW1lIH0gZnJvbSAnQC92aWV3cy9QUk8vYm9tLXNldHRpbmcvdXRpbHMnOwp2YXIgU1BMSVRfU1lNQk9MID0gJyRfJCc7CmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7CiAgICBEeW5hbWljVGFibGVGaWVsZHM6IER5bmFtaWNUYWJsZUZpZWxkcywKICAgIFRyZWVEZXRhaWw6IFRyZWVEZXRhaWwsCiAgICBFeHBhbmRhYmxlU2VjdGlvbjogRXhwYW5kYWJsZVNlY3Rpb24sCiAgICBCYXRjaFByb2Nlc3NBZGp1c3Q6IEJhdGNoUHJvY2Vzc0FkanVzdCwKICAgIEFkZERyYWZ0OiBBZGREcmFmdCwKICAgIFdvcmtzaG9wOiBXb3Jrc2hvcCwKICAgIE93bmVyUHJvY2VzczogT3duZXJQcm9jZXNzCiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZHJhd2VyOiBmYWxzZSwKICAgICAgZHJhd2Vyc3VsbDogZmFsc2UsCiAgICAgIGlmcmFtZUtleTogJycsCiAgICAgIGZ1bGxzY3JlZW5pZDogJycsCiAgICAgIGlmcmFtZVVybDogJycsCiAgICAgIGZ1bGxiaW1pZDogJycsCiAgICAgIGZpbGVCaW06ICcnLAogICAgICBJc1VwbG9hZENhZDogZmFsc2UsCiAgICAgIGNhZFJvd0NvZGU6ICcnLAogICAgICBjYWRSb3dQcm9qZWN0SWQ6ICcnLAogICAgICB0YktleTogMTAwLAogICAgICBpc0NvbXBvbmVudE9wdGlvbnM6IFt7CiAgICAgICAgbGFiZWw6ICfmmK8nLAogICAgICAgIHZhbHVlOiBmYWxzZQogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICflkKYnLAogICAgICAgIHZhbHVlOiB0cnVlCiAgICAgIH1dLAogICAgICBzcGVjT3B0aW9uczogW3sKICAgICAgICBkYXRhOiAnJwogICAgICB9XSwKICAgICAgZmlsdGVyVHlwZU9wdGlvbjogW3sKICAgICAgICBkYXRhOiAnJwogICAgICB9XSwKICAgICAgZmlsdGVyQ29kZU9wdGlvbjogW3sKICAgICAgICBkYXRhOiAnJwogICAgICB9XSwKICAgICAgcHJvamVjdE9wdGlvbnM6IFt7CiAgICAgICAgZGF0YTogJycKICAgICAgfV0sCiAgICAgIGFyZWFPcHRpb25zOiBbewogICAgICAgIGRhdGE6ICcnCiAgICAgIH1dLAogICAgICBpbnN0YWxsT3B0aW9uczogW3sKICAgICAgICBkYXRhOiAnJwogICAgICB9XSwKICAgICAgcHJvamVjdExpc3Q6IFtdLAogICAgICBpbnN0YWxsTGlzdDogW10sCiAgICAgIGFyZWFMaXN0OiBbXSwKICAgICAgcGlja2VyT3B0aW9uczogewogICAgICAgIGRpc2FibGVkRGF0ZTogZnVuY3Rpb24gZGlzYWJsZWREYXRlKHRpbWUpIHt9CiAgICAgIH0sCiAgICAgIGlubmVyRm9ybTogewogICAgICAgIHByb2plY3ROYW1lOiAnJywKICAgICAgICBhcmVhTmFtZTogJycsCiAgICAgICAgaW5zdGFsbE5hbWU6ICcnLAogICAgICAgIHNlYXJjaENvbnRlbnQ6ICcnLAogICAgICAgIHNlYXJjaFNwZWNTZWFyY2g6ICcnLAogICAgICAgIHNlYXJjaERpcmVjdDogJycKICAgICAgfSwKICAgICAgY3VyU2VhcmNoOiAxLAogICAgICBzZWFyY2hUeXBlOiAnJywKICAgICAgZm9ybUlubGluZTogewogICAgICAgIFNjaGR1bGluZ19Db2RlOiAnJywKICAgICAgICBDcmVhdGVfVXNlck5hbWU6ICcnLAogICAgICAgIEZpbmlzaF9EYXRlOiAnJywKICAgICAgICBJbnN0YWxsVW5pdF9JZDogJycsCiAgICAgICAgUmVtYXJrOiAnJwogICAgICB9LAogICAgICB0b3RhbDogMCwKICAgICAgY3VycmVudElkczogJycsCiAgICAgIGNvbHVtbnM6IFtdLAogICAgICB0YkRhdGE6IFtdLAogICAgICB0YkNvbmZpZzoge30sCiAgICAgIFRvdGFsQ291bnQ6IDAsCiAgICAgIG11bHRpcGxlU2VsZWN0aW9uOiBbXSwKICAgICAgc2hvd0V4cGFuZDogdHJ1ZSwKICAgICAgcGdMb2FkaW5nOiBmYWxzZSwKICAgICAgZGVsZXRlTG9hZGluZzogZmFsc2UsCiAgICAgIHdvcmtTaG9wSXNPcGVuOiBmYWxzZSwKICAgICAgaXNPd25lck51bGw6IGZhbHNlLAogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgb3BlbkFkZERyYWZ0OiBmYWxzZSwKICAgICAgc2F2ZUxvYWRpbmc6IGZhbHNlLAogICAgICB0YkxvYWRpbmc6IGZhbHNlLAogICAgICBpc0NoZWNrQWxsOiBmYWxzZSwKICAgICAgY3VycmVudENvbXBvbmVudDogJycsCiAgICAgIGdyaWRDb2RlOiAnJywKICAgICAgZFdpZHRoOiAnMjUlJywKICAgICAgdGl0bGU6ICcnLAogICAgICBzZWFyY2g6IGZ1bmN0aW9uIHNlYXJjaCgpIHsKICAgICAgICByZXR1cm4ge307CiAgICAgIH0sCiAgICAgIHBhZ2VUeXBlOiB1bmRlZmluZWQsCiAgICAgIHRpcExhYmVsOiAnJywKICAgICAgdGVjaG5vbG9neU9wdGlvbjogW10sCiAgICAgIHR5cGVPcHRpb246IFtdLAogICAgICB3b3JraW5nVGVhbTogW10sCiAgICAgIHBhZ2VTdGF0dXM6IHVuZGVmaW5lZCwKICAgICAgc2NoZWR1bGVJZDogJycsCiAgICAgIHBhcnRDb21Pd25lckNvbHVtbjogbnVsbCwKICAgICAgaW5zdGFsbFVuaXRJZExpc3Q6IFtdLAogICAgICBwcm9qZWN0SWQ6ICcnLAogICAgICBhcmVhSWQ6ICcnLAogICAgICBwcm9qZWN0TmFtZTogJycsCiAgICAgIHN0YXR1c1R5cGU6ICcnLAogICAgICBleHBhbmRlZEtleTogJycsCiAgICAgIC8vIHRyZWVMb2FkaW5nOiBmYWxzZSwKICAgICAgdHJlZURhdGE6IFtdLAogICAgICB0cmVlUGFyYW1zQ29tcG9uZW50VHlwZTogewogICAgICAgICdkZWZhdWx0LWV4cGFuZC1hbGwnOiB0cnVlLAogICAgICAgICdjaGVjay1zdHJpY3RseSc6IHRydWUsCiAgICAgICAgZmlsdGVyYWJsZTogdHJ1ZSwKICAgICAgICBjbGlja1BhcmVudDogdHJ1ZSwKICAgICAgICBkYXRhOiBbXSwKICAgICAgICBwcm9wczogewogICAgICAgICAgY2hpbGRyZW46ICdDaGlsZHJlbicsCiAgICAgICAgICBsYWJlbDogJ0xhYmVsJywKICAgICAgICAgIHZhbHVlOiAnRGF0YScKICAgICAgICB9CiAgICAgIH0sCiAgICAgIHRyZWVTZWxlY3RQYXJhbXM6IHsKICAgICAgICBwbGFjZWhvbGRlcjogJ+ivt+mAieaLqScsCiAgICAgICAgY29sbGFwc2VUYWdzOiB0cnVlLAogICAgICAgIGNsZWFyYWJsZTogdHJ1ZQogICAgICB9LAogICAgICBkaXNhYmxlZEFkZDogdHJ1ZSwKICAgICAgbGV2ZWxOYW1lOiAnJywKICAgICAgcHJvamVjdE9wdGlvbjogW10KICAgIH07CiAgfSwKICB3YXRjaDogewogICAgJ3RiRGF0YS5sZW5ndGgnOiB7CiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIobiwgbykgewogICAgICAgIHRoaXMuY2hlY2tPd25lcigpOwogICAgICAgIHRoaXMuZG9GaWx0ZXIoKTsKICAgICAgfSwKICAgICAgaW1tZWRpYXRlOiBmYWxzZQogICAgfQogIH0sCiAgY29tcHV0ZWQ6IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7CiAgICBpc0NvbTogZnVuY3Rpb24gaXNDb20oKSB7CiAgICAgIHJldHVybiB0aGlzLnBhZ2VUeXBlID09PSAnY29tJzsKICAgIH0sCiAgICBpc1ZpZXc6IGZ1bmN0aW9uIGlzVmlldygpIHsKICAgICAgcmV0dXJuIHRoaXMucGFnZVN0YXR1cyA9PT0gJ3ZpZXcnOwogICAgfSwKICAgIGlzRWRpdDogZnVuY3Rpb24gaXNFZGl0KCkgewogICAgICByZXR1cm4gdGhpcy5wYWdlU3RhdHVzID09PSAnZWRpdCc7CiAgICB9LAogICAgaXNBZGQ6IGZ1bmN0aW9uIGlzQWRkKCkgewogICAgICByZXR1cm4gdGhpcy5wYWdlU3RhdHVzID09PSAnYWRkJzsKICAgIH0sCiAgICBhZGREcmFmdEtleTogZnVuY3Rpb24gYWRkRHJhZnRLZXkoKSB7CiAgICAgIHJldHVybiB0aGlzLmV4cGFuZGVkS2V5ICsgdGhpcy5mb3JtSW5saW5lLkluc3RhbGxVbml0X0lkOwogICAgfSwKICAgIC8vIGZpbHRlclRleHQoKSB7CiAgICAvLyAgIHJldHVybiB0aGlzLnByb2plY3ROYW1lICsgU1BMSVRfU1lNQk9MICsgdGhpcy5zdGF0dXNUeXBlCiAgICAvLyB9LAogICAgc3RhdHVzQ29kZTogZnVuY3Rpb24gc3RhdHVzQ29kZSgpIHsKICAgICAgcmV0dXJuIHRoaXMuaXNDb20gPyAnQ29tcF9TY2hkdWxlX1N0YXR1cycgOiAnUGFydF9TY2hkdWxlX1N0YXR1cyc7CiAgICB9LAogICAgaW5zdGFsbE5hbWU6IGZ1bmN0aW9uIGluc3RhbGxOYW1lKCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICB2YXIgaXRlbSA9IHRoaXMuaW5zdGFsbFVuaXRJZExpc3QuZmluZChmdW5jdGlvbiAodikgewogICAgICAgIHJldHVybiB2LklkID09PSBfdGhpcy5mb3JtSW5saW5lLkluc3RhbGxVbml0X0lkOwogICAgICB9KTsKICAgICAgaWYgKGl0ZW0pIHsKICAgICAgICByZXR1cm4gaXRlbS5OYW1lOwogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAnJzsKICAgICAgfQogICAgfSwKICAgIGlzUGFydFByZXBhcmU6IGZ1bmN0aW9uIGlzUGFydFByZXBhcmUoKSB7CiAgICAgIHJldHVybiB0aGlzLmdldElzUGFydFByZXBhcmUgJiYgIXRoaXMuaXNDb207CiAgICB9LAogICAgaXNOZXN0OiBmdW5jdGlvbiBpc05lc3QoKSB7CiAgICAgIHJldHVybiBmYWxzZTsKICAgIH0sCiAgICBpc1N0b3BGbGFnOiBmdW5jdGlvbiBpc1N0b3BGbGFnKCkgewogICAgICByZXR1cm4gdGhpcy50YkRhdGEuc29tZShmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLnN0b3BGbGFnOwogICAgICB9KTsKICAgIH0KICB9LCBtYXBHZXR0ZXJzKCdmYWN0b3J5SW5mbycsIFsnd29ya3Nob3BFbmFibGVkJywgJ2dldElzUGFydFByZXBhcmUnXSkpLCBtYXBHZXR0ZXJzKCdzY2hlZHVsZScsIFsncHJvY2Vzc0xpc3QnLCAnbmVzdElkcyddKSksCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlKCkgewogICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZSQoX2NvbnRleHQpIHsKICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dC5wcmV2ID0gX2NvbnRleHQubmV4dCkgewogICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHsKICAgICAgICAgICAgICAvLyB0aGlzLmJhc2VDYWRVcmwgPSAnaHR0cDovL2xvY2FsaG9zdDo5NTI5JwogICAgICAgICAgICAgIC8vIHRoaXMuYmFzZUNhZFVybCA9ICdodHRwOi8vZ2xlbmRhbGUtbW9kZWwuYmltdGsuY29tJwogICAgICAgICAgICAgIF90aGlzMi5iYXNlQ2FkVXJsID0gJ2h0dHA6Ly9nbGVuZGFsZS1tb2RlbC1kZXYuYmltdGsudGVjaCc7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgZ2V0Q29uZmlndXJlKHsKICAgICAgICAgICAgICAgIGNvZGU6ICdnbGVuZGFsZV91cmwnCiAgICAgICAgICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgICAgICBfdGhpczIuYmFzZUNhZFVybCA9IHJlcy5EYXRhOwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICBjYXNlIDE6CiAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuc3RvcCgpOwogICAgICAgIH0KICAgICAgfSwgX2NhbGxlZSk7CiAgICB9KSkoKTsKICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7CiAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvcigvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yUnVudGltZSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTIoKSB7CiAgICAgIHZhciBfdGhpczMkJHJvdXRlJHF1ZXJ5LCBhcmVhSWQsIGluc3RhbGw7CiAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlMiQoX2NvbnRleHQyKSB7CiAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQyLnByZXYgPSBfY29udGV4dDIubmV4dCkgewogICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICBfdGhpczMuaW5pdFByb2Nlc3NMaXN0KCk7CiAgICAgICAgICAgIF90aGlzMy50YkRhdGFNYXAgPSB7fTsKICAgICAgICAgICAgX3RoaXMzLmNyYWZ0Q29kZU1hcCA9IHt9OwogICAgICAgICAgICBfdGhpczMucGFnZVR5cGUgPSBfdGhpczMuJHJvdXRlLnF1ZXJ5LnBnX3R5cGU7CiAgICAgICAgICAgIF90aGlzMy5sZXZlbCA9IF90aGlzMy4kcm91dGUucXVlcnkubGV2ZWw7CiAgICAgICAgICAgIF90aGlzMy5wYWdlU3RhdHVzID0gX3RoaXMzLiRyb3V0ZS5xdWVyeS5zdGF0dXM7CiAgICAgICAgICAgIF90aGlzMy5tb2RlbCA9IF90aGlzMy4kcm91dGUucXVlcnkubW9kZWw7CiAgICAgICAgICAgIF90aGlzMy5zY2hlZHVsZUlkID0gX3RoaXMzLiRyb3V0ZS5xdWVyeS5waWQgfHwgJyc7CiAgICAgICAgICAgIC8vIC8vIHRoaXMuZm9ybUlubGluZS5DcmVhdGVfVXNlck5hbWUgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLm5hbWUKICAgICAgICAgICAgLy8gLy8g5qGG5p626Zeu6aKY5byV6LW3c3RvcmXmlbDmja7kuKLlpLHvvIzlt7Llj43ppojvvIznu5PmnpzvvJrmraTlpITlhYjkvb/nlKhsb2NhbFN0b3JhZ2UKICAgICAgICAgICAgX3RoaXMzLmZvcm1JbmxpbmUuQ3JlYXRlX1VzZXJOYW1lID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ1VzZXJBY2NvdW50Jyk7CiAgICAgICAgICAgIC8vIGlmICghdGhpcy5pc0NvbSkgewogICAgICAgICAgICAvLyAgIHRoaXMuZ2V0UGFydFR5cGUoKQogICAgICAgICAgICAvLyB9IGVsc2UgewogICAgICAgICAgICAvLyB9CgogICAgICAgICAgICBfdGhpczMudW5pcXVlID0gdW5pcXVlQ29kZSgpOwogICAgICAgICAgICBfdGhpczMuY2hlY2tXb3Jrc2hvcElzT3BlbigpOwogICAgICAgICAgICBfdGhpczMuc2VhcmNoID0gZGVib3VuY2UoX3RoaXMzLmZldGNoRGF0YSwgODAwLCB0cnVlKTsKICAgICAgICAgICAgX2NvbnRleHQyLm5leHQgPSAxNDsKICAgICAgICAgICAgcmV0dXJuIF90aGlzMy5tZXJnZUNvbmZpZygpOwogICAgICAgICAgY2FzZSAxNDoKICAgICAgICAgICAgaWYgKF90aGlzMy5pc1ZpZXcgfHwgX3RoaXMzLmlzRWRpdCkgewogICAgICAgICAgICAgIF90aGlzMyQkcm91dGUkcXVlcnkgPSBfdGhpczMuJHJvdXRlLnF1ZXJ5LCBhcmVhSWQgPSBfdGhpczMkJHJvdXRlJHF1ZXJ5LmFyZWFJZCwgaW5zdGFsbCA9IF90aGlzMyQkcm91dGUkcXVlcnkuaW5zdGFsbDsgLy8gdGhpcy5hcmVhSWQgPSBhcmVhSWQKICAgICAgICAgICAgICAvLyB0aGlzLmZvcm1JbmxpbmUuSW5zdGFsbFVuaXRfSWQgPSBpbnN0YWxsCiAgICAgICAgICAgICAgLy8gdGhpcy5nZXRJbnN0YWxsVW5pdElkTmFtZUxpc3QoKQogICAgICAgICAgICAgIF90aGlzMy5mZXRjaERhdGEoKTsKICAgICAgICAgICAgfQogICAgICAgICAgICBpZiAoX3RoaXMzLmlzQWRkKSB7CiAgICAgICAgICAgICAgLy8gdGhpcy5mZXRjaFRyZWVEYXRhKCkKICAgICAgICAgICAgICBfdGhpczMuZ2V0VHlwZSgpOwogICAgICAgICAgICB9CiAgICAgICAgICAgIGlmIChfdGhpczMuaXNFZGl0KSB7CiAgICAgICAgICAgICAgX3RoaXMzLmdldFR5cGUoKTsKICAgICAgICAgICAgfQogICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDE5OwogICAgICAgICAgICByZXR1cm4gZ2V0Qm9tTmFtZShfdGhpczMubGV2ZWwpOwogICAgICAgICAgY2FzZSAxOToKICAgICAgICAgICAgX3RoaXMzLmxldmVsTmFtZSA9IF9jb250ZXh0Mi5zZW50OwogICAgICAgICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignbWVzc2FnZScsIF90aGlzMy5mcmFtZUxpc3RlbmVyKTsKICAgICAgICAgICAgX3RoaXMzLiRvbmNlKCdob29rOmJlZm9yZURlc3Ryb3knLCBmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ2RlYWN0aXZhdGVkJyk7CiAgICAgICAgICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21lc3NhZ2UnLCBfdGhpczMuZnJhbWVMaXN0ZW5lcik7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgY2FzZSAyMjoKICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgIHJldHVybiBfY29udGV4dDIuc3RvcCgpOwogICAgICAgIH0KICAgICAgfSwgX2NhbGxlZTIpOwogICAgfSkpKCk7CiAgfSwKICBhY3RpdmF0ZWQ6IGZ1bmN0aW9uIGFjdGl2YXRlZCgpIHsKICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ21lc3NhZ2UnLCB0aGlzLmZyYW1lTGlzdGVuZXIpOwogICAgdGhpcy4kb25jZSgnaG9vazpkZWFjdGl2YXRlZCcsIGZ1bmN0aW9uICgpIHsKICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21lc3NhZ2UnLCBfdGhpczQuZnJhbWVMaXN0ZW5lcik7CiAgICB9KTsKICB9LAogIG1ldGhvZHM6IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgbWFwQWN0aW9ucygnc2NoZWR1bGUnLCBbJ2NoYW5nZVByb2Nlc3NMaXN0JywgJ2luaXRQcm9jZXNzTGlzdCcsICdjaGFuZ2VBZGRUYktleXMnXSkpLCB7fSwgewogICAgY2hlY2tPd25lcjogZnVuY3Rpb24gY2hlY2tPd25lcigpIHsKICAgICAgaWYgKHRoaXMuaXNDb20pIHJldHVybjsKICAgICAgdGhpcy5pc093bmVyTnVsbCA9IHRoaXMudGJEYXRhLmV2ZXJ5KGZ1bmN0aW9uICh2KSB7CiAgICAgICAgcmV0dXJuICF2LkNvbXBfSW1wb3J0X0RldGFpbF9JZDsKICAgICAgfSkgJiYgIXRoaXMuaXNOZXN0OwogICAgICB2YXIgaWR4ID0gdGhpcy5jb2x1bW5zLmZpbmRJbmRleChmdW5jdGlvbiAodikgewogICAgICAgIHJldHVybiB2LkNvZGUgPT09ICdQYXJ0X1VzZWRfUHJvY2Vzcyc7CiAgICAgIH0pOwogICAgICBpZiAodGhpcy5pc093bmVyTnVsbCkgewogICAgICAgIGlkeCAhPT0gLTEgJiYgdGhpcy5jb2x1bW5zLnNwbGljZShpZHgsIDEpOwogICAgICB9IGVsc2UgewogICAgICAgIGlmIChpZHggPT09IC0xKSB7CiAgICAgICAgICBpZiAoIXRoaXMub3duZXJDb2x1bW4pIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIl+ihqOmFjee9ruWtl+autee8uuWwkemDqOS7tumihueUqOW3peW6j+Wtl+autScsCiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICByZXR1cm47CiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLmNvbHVtbnMucHVzaCh0aGlzLm93bmVyQ29sdW1uKTsKICAgICAgICB9CiAgICAgICAgdGhpcy5jb21QYXJ0ID0gdHJ1ZTsKICAgICAgfQogICAgfSwKICAgIG1lcmdlQ29uZmlnOiBmdW5jdGlvbiBtZXJnZUNvbmZpZygpIHsKICAgICAgdmFyIF90aGlzNSA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvcigvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yUnVudGltZSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTMoKSB7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUzJChfY29udGV4dDMpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0My5wcmV2ID0gX2NvbnRleHQzLm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF9jb250ZXh0My5uZXh0ID0gMjsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXM1LmdldENvbmZpZygpOwogICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgICAgX2NvbnRleHQzLm5leHQgPSA0OwogICAgICAgICAgICAgIHJldHVybiBfdGhpczUuZ2V0V29ya1RlYW0oKTsKICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDMuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUzKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgZG9GaWx0ZXI6IGZ1bmN0aW9uIGRvRmlsdGVyKCkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgdGhpcy5wcm9qZWN0TGlzdCA9IFtdOwogICAgICB0aGlzLmluc3RhbGxMaXN0ID0gW107CiAgICAgIHRoaXMuYXJlYUxpc3QgPSBbXTsKICAgICAgdGhpcy50YkRhdGEuZm9yRWFjaChmdW5jdGlvbiAoY3VyKSB7CiAgICAgICAgaWYgKGN1ci5Qcm9qZWN0X05hbWUgJiYgIV90aGlzNi5wcm9qZWN0TGlzdC5pbmNsdWRlcyhjdXIuUHJvamVjdF9OYW1lKSkgewogICAgICAgICAgX3RoaXM2LnByb2plY3RMaXN0LnB1c2goY3VyLlByb2plY3RfTmFtZSk7CiAgICAgICAgfQogICAgICAgIGlmIChjdXIuSW5zdGFsbFVuaXRfTmFtZSAmJiAhX3RoaXM2Lmluc3RhbGxMaXN0LmluY2x1ZGVzKGN1ci5JbnN0YWxsVW5pdF9OYW1lKSkgewogICAgICAgICAgX3RoaXM2Lmluc3RhbGxMaXN0LnB1c2goY3VyLkluc3RhbGxVbml0X05hbWUpOwogICAgICAgIH0KICAgICAgICBpZiAoY3VyLkFyZWFfTmFtZSAmJiAhX3RoaXM2LmFyZWFMaXN0LmluY2x1ZGVzKGN1ci5BcmVhX05hbWUpKSB7CiAgICAgICAgICBfdGhpczYuYXJlYUxpc3QucHVzaChjdXIuQXJlYV9OYW1lKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGdldENvbmZpZzogZnVuY3Rpb24gZ2V0Q29uZmlnKCkgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlNCgpIHsKICAgICAgICB2YXIgY29uZmlnQ29kZTsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTQkKF9jb250ZXh0NCkgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ0LnByZXYgPSBfY29udGV4dDQubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgY29uZmlnQ29kZSA9ICcnOwogICAgICAgICAgICAgIGlmIChfdGhpczcuaXNOZXN0KSB7CiAgICAgICAgICAgICAgICBpZiAoX3RoaXM3LmlzVmlldykgewogICAgICAgICAgICAgICAgICBjb25maWdDb2RlID0gJ1BST05lc3RpbmdTY2hlZHVsZURldGFpbCc7CiAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICBjb25maWdDb2RlID0gJ1BST05lc3RpbmdTY2hlZHVsZUNvbmZpZyc7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIGNvbmZpZ0NvZGUgPSBfdGhpczcuaXNWaWV3ID8gJ1BST1VuaXRQYXJ0Vmlld1BhZ2VUYkNvbmZpZ19uZXcnIDogJ1BST1VuaXRQYXJ0RHJhZnRQYWdlVGJDb25maWdfbmV3JzsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX3RoaXM3LmdyaWRDb2RlID0gY29uZmlnQ29kZTsKICAgICAgICAgICAgICBfY29udGV4dDQubmV4dCA9IDU7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzNy5nZXRUYWJsZUNvbmZpZyhjb25maWdDb2RlKTsKICAgICAgICAgICAgY2FzZSA1OgogICAgICAgICAgICAgIGlmICghX3RoaXM3LndvcmtzaG9wRW5hYmxlZCkgewogICAgICAgICAgICAgICAgX3RoaXM3LmNvbHVtbnMgPSBfdGhpczcuY29sdW1ucy5maWx0ZXIoZnVuY3Rpb24gKHYpIHsKICAgICAgICAgICAgICAgICAgcmV0dXJuIHYuQ29kZSAhPT0gJ1dvcmtzaG9wX05hbWUnOwogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzNy5jaGVja093bmVyKCk7CiAgICAgICAgICAgIGNhc2UgNzoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ0LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNCk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIGNoYW5nZUNvbHVtbjogZnVuY3Rpb24gY2hhbmdlQ29sdW1uKCkgewogICAgICB2YXIgX3RoaXM4ID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlNSgpIHsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTUkKF9jb250ZXh0NSkgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ1LnByZXYgPSBfY29udGV4dDUubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX2NvbnRleHQ1Lm5leHQgPSAyOwogICAgICAgICAgICAgIHJldHVybiBfdGhpczguZ2V0VGFibGVDb25maWcoX3RoaXM4LmdyaWRDb2RlKTsKICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgIF90aGlzOC50YktleSsrOwogICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0NS5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTUpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvKiAgICBoYW5kbGVOb2RlQ2xpY2soZGF0YSkgew0KICAgICAgY29uc29sZS5sb2coJ2RhdGEnLCBkYXRhKQ0KICAgICAgaWYgKHRoaXMuYXJlYUlkID09PSBkYXRhLklkKSB7DQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgdGhpcy4NCiAgICAgICA9IHRydWUNCiAgICAgIGlmICghZGF0YS5QYXJlbnROb2RlcyB8fCBkYXRhLkNoaWxkcmVuPy5sZW5ndGggPiAwKSB7DQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgaWYgKGRhdGE/LkRhdGFbdGhpcy5zdGF0dXNDb2RlXSA9PT0gJ+acquWvvOWFpScpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogJ+a4heWNleacquWvvOWFpe+8jOivt+iBlOezu+a3seWMluS6uuWRmOWvvOWFpea4heWNlScsDQogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgIH0pDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgICBjb25zdCBpbml0RGF0YSA9ICh7IERhdGEgfSkgPT4gew0KICAgICAgICB0aGlzLmFyZWFJZCA9IERhdGEuSWQNCiAgICAgICAgdGhpcy5wcm9qZWN0SWQgPSBEYXRhLlByb2plY3RfSWQNCiAgICAgICAgdGhpcy5leHBhbmRlZEtleSA9IHRoaXMuYXJlYUlkDQogICAgICAgIHRoaXMuZm9ybUlubGluZS5GaW5pc2hfRGF0ZSA9ICcnDQogICAgICAgIHRoaXMuZm9ybUlubGluZS5JbnN0YWxsVW5pdF9JZCA9ICcnDQogICAgICAgIHRoaXMuZm9ybUlubGluZS5SZW1hcmsgPSAnJw0KICAgICAgICB0aGlzLnRiRGF0YSA9IFtdDQogICAgICAgIHRoaXMuZ2V0QXJlYUluZm8oKQ0KICAgICAgICB0aGlzLmdldEluc3RhbGxVbml0SWROYW1lTGlzdCgpDQogICAgICB9DQogICAgICAgIGlmICh0aGlzLnRiRGF0YS5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy4kY29uZmlybSgn5YiH5o2i5Yy65Z+f5Y+z5L6n5pWw5o2u5riF56m677yM5piv5ZCm56Gu6K6kPycsICfmj5DnpLonLCB7DQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgICBpbml0RGF0YShkYXRhKQ0KICAgICAgICAgIHRoaXMuZGlzYWJsZWRBZGQgPSBmYWxzZQ0KICAgICAgICAgIHRoaXMudGJEYXRhTWFwID0ge30NCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgdHlwZTogJ2luZm8nLA0KICAgICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iCcNCiAgICAgICAgICB9KQ0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5kaXNhYmxlZEFkZCA9IGZhbHNlDQogICAgICAgIGluaXREYXRhKGRhdGEpDQogICAgICB9DQogICAgfSwqLwogICAgLyogY3VzdG9tRmlsdGVyRnVuKHZhbHVlLCBkYXRhLCBub2RlKSB7DQogICAgICBjb25zdCBhcnIgPSB2YWx1ZS5zcGxpdChTUExJVF9TWU1CT0wpDQogICAgICBjb25zdCBsYWJlbFZhbCA9IGFyclswXQ0KICAgICAgY29uc3Qgc3RhdHVzVmFsID0gYXJyWzFdDQogICAgICBpZiAoIXZhbHVlKSByZXR1cm4gdHJ1ZQ0KICAgICAgbGV0IHBhcmVudE5vZGUgPSBub2RlLnBhcmVudA0KICAgICAgbGV0IGxhYmVscyA9IFtub2RlLmxhYmVsXQ0KICAgICAgbGV0IHN0YXR1cyA9IFtkYXRhLkRhdGFbdGhpcy5zdGF0dXNDb2RlXV0NCiAgICAgIGxldCBsZXZlbCA9IDENCiAgICAgIHdoaWxlIChsZXZlbCA8IG5vZGUubGV2ZWwpIHsNCiAgICAgICAgbGFiZWxzID0gWy4uLmxhYmVscywgcGFyZW50Tm9kZS5sYWJlbF0NCiAgICAgICAgc3RhdHVzID0gWy4uLnN0YXR1cywgZGF0YS5EYXRhW3RoaXMuc3RhdHVzQ29kZV1dDQogICAgICAgIHBhcmVudE5vZGUgPSBwYXJlbnROb2RlLnBhcmVudA0KICAgICAgICBsZXZlbCsrDQogICAgICB9DQogICAgICBsYWJlbHMgPSBsYWJlbHMuZmlsdGVyKHYgPT4gISF2KQ0KICAgICAgc3RhdHVzID0gc3RhdHVzLmZpbHRlcih2ID0+ICEhdikNCiAgICAgIGxldCByZXN1bHRMYWJlbCA9IHRydWUNCiAgICAgIGxldCByZXN1bHRTdGF0dXMgPSB0cnVlDQogICAgICBpZiAodGhpcy5zdGF0dXNUeXBlKSB7DQogICAgICAgIHJlc3VsdFN0YXR1cyA9IHN0YXR1cy5zb21lKHMgPT4gcy5pbmRleE9mKHN0YXR1c1ZhbCkgIT09IC0xKQ0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMucHJvamVjdE5hbWUpIHsNCiAgICAgICAgcmVzdWx0TGFiZWwgPSBsYWJlbHMuc29tZShzID0+IHMuaW5kZXhPZihsYWJlbFZhbCkgIT09IC0xKQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIHJlc3VsdExhYmVsICYmIHJlc3VsdFN0YXR1cw0KICAgIH0sKi8KICAgIGZldGNoRGF0YTogZnVuY3Rpb24gZmV0Y2hEYXRhKCkgewogICAgICB2YXIgX3RoaXM5ID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlNigpIHsKICAgICAgICB2YXIgcmVzRGF0YTsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTYkKF9jb250ZXh0NikgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ2LnByZXYgPSBfY29udGV4dDYubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX3RoaXM5LnRiTG9hZGluZyA9IHRydWU7CiAgICAgICAgICAgICAgcmVzRGF0YSA9IG51bGw7CiAgICAgICAgICAgICAgaWYgKCFfdGhpczkuaXNOZXN0KSB7CiAgICAgICAgICAgICAgICBfY29udGV4dDYubmV4dCA9IDE0OwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGlmICghX3RoaXM5LmlzVmlldykgewogICAgICAgICAgICAgICAgX2NvbnRleHQ2Lm5leHQgPSA5OwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0Ni5uZXh0ID0gNjsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXM5LmdldFBhcnRQYWdlTGlzdCgpOwogICAgICAgICAgICBjYXNlIDY6CiAgICAgICAgICAgICAgcmVzRGF0YSA9IF9jb250ZXh0Ni5zZW50OwogICAgICAgICAgICAgIF9jb250ZXh0Ni5uZXh0ID0gMTI7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgOToKICAgICAgICAgICAgICBfY29udGV4dDYubmV4dCA9IDExOwogICAgICAgICAgICAgIHJldHVybiBfdGhpczkuZ2V0TmVzdFBhZ2VMaXN0KCk7CiAgICAgICAgICAgIGNhc2UgMTE6CiAgICAgICAgICAgICAgcmVzRGF0YSA9IF9jb250ZXh0Ni5zZW50OwogICAgICAgICAgICBjYXNlIDEyOgogICAgICAgICAgICAgIF9jb250ZXh0Ni5uZXh0ID0gMTc7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMTQ6CiAgICAgICAgICAgICAgX2NvbnRleHQ2Lm5leHQgPSAxNjsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXM5LmdldFBhcnRQYWdlTGlzdCgpOwogICAgICAgICAgICBjYXNlIDE2OgogICAgICAgICAgICAgIHJlc0RhdGEgPSBfY29udGV4dDYuc2VudDsKICAgICAgICAgICAgY2FzZSAxNzoKICAgICAgICAgICAgICBfdGhpczkuaW5pdFRiRGF0YShyZXNEYXRhKTsKICAgICAgICAgICAgICBfdGhpczkudGJMb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgIGNhc2UgMTk6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Ni5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTYpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBmZXRjaFRyZWVEYXRhTG9jYWw6IGZ1bmN0aW9uIGZldGNoVHJlZURhdGFMb2NhbCgpIHsKICAgICAgLy8gdGhpcy5maWx0ZXJUZXh0ID0gdGhpcy5wcm9qZWN0TmFtZQogICAgfSwKICAgIGZldGNoVHJlZVN0YXR1czogZnVuY3Rpb24gZmV0Y2hUcmVlU3RhdHVzKCkgewogICAgICAvLyB0aGlzLmZpbHRlclRleHQgPSB0aGlzLnN0YXR1c1R5cGUKICAgIH0sCiAgICAvKiAgICBmZXRjaFRyZWVEYXRhKCkgew0KICAgICAgdGhpcy50cmVlTG9hZGluZyA9IHRydWUNCiAgICAgIEdldFByb2plY3RBcmVhVHJlZUxpc3QoeyBwcm9qZWN0TmFtZTogdGhpcy5wcm9qZWN0TmFtZSwgdHlwZTogdGhpcy5pc0NvbSA/IDEgOiAyIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLkRhdGEubGVuZ3RoID09PSAwKSB7DQogICAgICAgICAgdGhpcy50cmVlTG9hZGluZyA9IGZhbHNlDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCiAgICAgICAgY29uc3QgcmVzRGF0YSA9IHJlcy5EYXRhLm1hcChpdGVtID0+IHsNCiAgICAgICAgICBpdGVtLklzX0RpcmVjdG9yeSA9IHRydWUNCiAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICB9KQ0KICAgICAgICB0aGlzLnRyZWVEYXRhID0gcmVzRGF0YQ0KICAgICAgICB0aGlzLnNldEtleSgpDQogICAgICAgIHRoaXMudHJlZUxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LCovCiAgICAvLyBzZXRLZXkoKSB7CiAgICAvLyAgIGNvbnN0IGRlZXBGaWx0ZXIgPSAodHJlZSkgPT4gewogICAgLy8gICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdHJlZS5sZW5ndGg7IGkrKykgewogICAgLy8gICAgICAgY29uc3QgaXRlbSA9IHRyZWVbaV0KICAgIC8vICAgICAgIGNvbnN0IHsgRGF0YSwgQ2hpbGRyZW4gfSA9IGl0ZW0KICAgIC8vICAgICAgIGNvbnNvbGUubG9nKERhdGEpCiAgICAvLyAgICAgICBpZiAoRGF0YS5QYXJlbnRJZCAmJiAhQ2hpbGRyZW4/Lmxlbmd0aCkgewogICAgLy8gICAgICAgICB0aGlzLmhhbmRsZU5vZGVDbGljayhpdGVtKQogICAgLy8gICAgICAgICByZXR1cm4KICAgIC8vICAgICAgIH0gZWxzZSB7CiAgICAvLyAgICAgICAgIGlmIChDaGlsZHJlbiAmJiBDaGlsZHJlbi5sZW5ndGggPiAwKSB7CiAgICAvLyAgICAgICAgICAgcmV0dXJuIGRlZXBGaWx0ZXIoQ2hpbGRyZW4pCiAgICAvLyAgICAgICAgIH0KICAgIC8vICAgICAgIH0KICAgIC8vICAgICB9CiAgICAvLyAgIH0KICAgIC8vICAgcmV0dXJuIGRlZXBGaWx0ZXIodGhpcy50cmVlRGF0YSkKICAgIC8vIH0sCiAgICBjbG9zZVZpZXc6IGZ1bmN0aW9uIGNsb3NlVmlldygpIHsKICAgICAgY2xvc2VUYWdWaWV3KHRoaXMuJHN0b3JlLCB0aGlzLiRyb3V0ZSk7CiAgICB9LAogICAgY2hlY2tXb3Jrc2hvcElzT3BlbjogZnVuY3Rpb24gY2hlY2tXb3Jrc2hvcElzT3BlbigpIHsKICAgICAgdGhpcy53b3JrU2hvcElzT3BlbiA9IHRydWU7CiAgICB9LAogICAgdGJTZWxlY3RDaGFuZ2U6IGZ1bmN0aW9uIHRiU2VsZWN0Q2hhbmdlKGFycmF5KSB7CiAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24gPSBhcnJheS5yZWNvcmRzOwogICAgfSwKICAgIGdldEFyZWFJbmZvOiBmdW5jdGlvbiBnZXRBcmVhSW5mbygpIHsKICAgICAgdmFyIF90aGlzMCA9IHRoaXM7CiAgICAgIHRoaXMuZm9ybUlubGluZS5GaW5pc2hfRGF0ZSA9ICcnOwogICAgICBBcmVhR2V0RW50aXR5KHsKICAgICAgICBpZDogdGhpcy5hcmVhSWQKICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIHZhciBfcmVzJERhdGEsIF9yZXMkRGF0YTI7CiAgICAgICAgICBpZiAoIXJlcy5EYXRhKSB7CiAgICAgICAgICAgIHJldHVybiBbXTsKICAgICAgICAgIH0KICAgICAgICAgIHZhciBzdGFydCA9IG1vbWVudCgoX3JlcyREYXRhID0gcmVzLkRhdGEpID09PSBudWxsIHx8IF9yZXMkRGF0YSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX3JlcyREYXRhLkRlbWFuZF9CZWdpbl9EYXRlKTsKICAgICAgICAgIHZhciBlbmQgPSBtb21lbnQoKF9yZXMkRGF0YTIgPSByZXMuRGF0YSkgPT09IG51bGwgfHwgX3JlcyREYXRhMiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX3JlcyREYXRhMi5EZW1hbmRfRW5kX0RhdGUpOwogICAgICAgICAgX3RoaXMwLnBpY2tlck9wdGlvbnMuZGlzYWJsZWREYXRlID0gZnVuY3Rpb24gKHRpbWUpIHsKICAgICAgICAgICAgcmV0dXJuIHRpbWUuZ2V0VGltZSgpIDwgc3RhcnQgfHwgdGltZS5nZXRUaW1lKCkgPiBlbmQ7CiAgICAgICAgICB9OwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpczAuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVDbG9zZTogZnVuY3Rpb24gaGFuZGxlQ2xvc2UoKSB7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgICB0aGlzLm9wZW5BZGREcmFmdCA9IGZhbHNlOwogICAgfSwKICAgIGdldE5lc3RQYWdlTGlzdDogZnVuY3Rpb24gZ2V0TmVzdFBhZ2VMaXN0KCkgewogICAgICB2YXIgX3RoaXMxID0gdGhpczsKICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uIChyZXNvbHZlLCByZWplY3QpIHsKICAgICAgICBHZXRDYW5TY2hkdWxpbmdQYXJ0TGlzdCh7CiAgICAgICAgICBJZHM6IF90aGlzMS5uZXN0SWRzCiAgICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICB2YXIgX2xpc3QgPSAocmVzID09PSBudWxsIHx8IHJlcyA9PT0gdm9pZCAwID8gdm9pZCAwIDogcmVzLkRhdGEpIHx8IFtdOwogICAgICAgICAgICB2YXIgbGlzdCA9IF9saXN0Lm1hcChmdW5jdGlvbiAodikgewogICAgICAgICAgICAgIHYuUGFydF9Vc2VkX1Byb2Nlc3MgPSB2LlNjaGVkdWxlZF9Vc2VkX1Byb2Nlc3MgfHwgdi5QYXJ0X1R5cGVfVXNlZF9Qcm9jZXNzOwogICAgICAgICAgICAgIC8vIHYub3JpZ2luYWxQYXRoID0gdi5TY2hlZHVsZWRfVGVjaG5vbG9neV9QYXRoID8gdi5TY2hlZHVsZWRfVGVjaG5vbG9neV9QYXRoIDogJycKICAgICAgICAgICAgICB2LldvcmtzaG9wX0lkID0gdi5TY2hlZHVsZWRfV29ya3Nob3BfSWQ7CiAgICAgICAgICAgICAgdi5Xb3Jrc2hvcF9OYW1lID0gdi5TY2hlZHVsZWRfV29ya3Nob3BfTmFtZTsKICAgICAgICAgICAgICB2LlRlY2hub2xvZ3lfUGF0aCA9IHYuU2NoZWR1bGVkX1RlY2hub2xvZ3lfUGF0aCB8fCB2LlRlY2hub2xvZ3lfUGF0aDsKICAgICAgICAgICAgICB2LmNob29zZUNvdW50ID0gdi5DYW5fU2NoZHVsaW5nX0NvdW50OwogICAgICAgICAgICAgIHJldHVybiB2OwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgcmVzb2x2ZShsaXN0KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIF90aGlzMS4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgcmVqZWN0KCk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIGdldFBhcnRQYWdlTGlzdDogZnVuY3Rpb24gZ2V0UGFydFBhZ2VMaXN0KCkgewogICAgICB2YXIgX3RoaXMxMCA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvcigvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yUnVudGltZSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTcoKSB7CiAgICAgICAgdmFyIHBpZCwgcmVzdWx0OwogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlNyQoX2NvbnRleHQ3KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDcucHJldiA9IF9jb250ZXh0Ny5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBwaWQgPSBfdGhpczEwLiRyb3V0ZS5xdWVyeS5waWQ7CiAgICAgICAgICAgICAgX2NvbnRleHQ3Lm5leHQgPSAzOwogICAgICAgICAgICAgIHJldHVybiBHZXRVbml0U2NoZHVsaW5nSW5mb0RldGFpbCh7CiAgICAgICAgICAgICAgICBTY2hkdWxpbmdfUGxhbl9JZDogcGlkCiAgICAgICAgICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICAgICAgICB2YXIgX3JlcyREYXRhMywgX3JlcyREYXRhNCwgX3JlcyREYXRhNTsKICAgICAgICAgICAgICAgICAgdmFyIFNhcmVQYXJ0c01vZGVsID0gKF9yZXMkRGF0YTMgPSByZXMuRGF0YSkgPT09IG51bGwgfHwgX3JlcyREYXRhMyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX3JlcyREYXRhMy5TYXJlUGFydHNNb2RlbC5tYXAoZnVuY3Rpb24gKHYpIHsKICAgICAgICAgICAgICAgICAgICBpZiAodi5TY2hlZHVsZWRfVXNlZF9Qcm9jZXNzKSB7CiAgICAgICAgICAgICAgICAgICAgICAvLyDlt7LlrZjlnKjmk43kvZzov4fmlbDmja4KICAgICAgICAgICAgICAgICAgICAgIHYuUGFydF9Vc2VkX1Byb2Nlc3MgPSB2LlNjaGVkdWxlZF9Vc2VkX1Byb2Nlc3M7CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIGlmICh2LlVuaXRfUGFydF9Vc2VkX1Byb2Nlc3MpIHsKICAgICAgICAgICAgICAgICAgICAgIC8vIOW3suWtmOWcqOaTjeS9nOi/h+aVsOaNrgogICAgICAgICAgICAgICAgICAgICAgdi5QYXJ0X1VzZWRfUHJvY2VzcyA9IHYuVW5pdF9QYXJ0X1VzZWRfUHJvY2VzczsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgdi5jaG9vc2VDb3VudCA9IHYuQ2FuX1NjaGR1bGluZ19Db3VudDsKICAgICAgICAgICAgICAgICAgICByZXR1cm4gdjsKICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgIF90aGlzMTAuZm9ybUlubGluZSA9IE9iamVjdC5hc3NpZ24oX3RoaXMxMC5mb3JtSW5saW5lLCAoX3JlcyREYXRhNCA9IHJlcy5EYXRhKSA9PT0gbnVsbCB8fCBfcmVzJERhdGE0ID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfcmVzJERhdGE0LlNjaGR1bGluZ19QbGFuKTsKICAgICAgICAgICAgICAgICAgKF9yZXMkRGF0YTUgPSByZXMuRGF0YSkgPT09IG51bGwgfHwgX3JlcyREYXRhNSA9PT0gdm9pZCAwIHx8IF9yZXMkRGF0YTUuUHJvY2Vzc19MaXN0LmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgICAgICAgICB2YXIgcGxpc3QgPSB7CiAgICAgICAgICAgICAgICAgICAgICBrZXk6IGl0ZW0uUHJvY2Vzc19Db2RlLAogICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0KICAgICAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgICAgICAgIF90aGlzMTAuY2hhbmdlUHJvY2Vzc0xpc3QocGxpc3QpOwogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgX3RoaXMxMC5nZXRTdG9wTGlzdChTYXJlUGFydHNNb2RlbCk7CiAgICAgICAgICAgICAgICAgIHJldHVybiBTYXJlUGFydHNNb2RlbDsKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgIF90aGlzMTAuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgIHJldHVybiBbXTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIHJlc3VsdCA9IF9jb250ZXh0Ny5zZW50OwogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdyZXN1bHQnLCByZXN1bHQpOwogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDcuYWJydXB0KCJyZXR1cm4iLCByZXN1bHQgfHwgW10pOwogICAgICAgICAgICBjYXNlIDY6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Ny5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTcpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBnZXRTdG9wTGlzdDogZnVuY3Rpb24gZ2V0U3RvcExpc3QobGlzdCkgewogICAgICB2YXIgX3RoaXMxMSA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvcigvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yUnVudGltZSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTgoKSB7CiAgICAgICAgdmFyIHN1Ym1pdE9iajsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTgkKF9jb250ZXh0OCkgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ4LnByZXYgPSBfY29udGV4dDgubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ2dldFN0b3BMaXN0JywgbGlzdCk7CiAgICAgICAgICAgICAgc3VibWl0T2JqID0gbGlzdC5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgICAgICAgIElkOiBpdGVtLlBhcnRfQWdncmVnYXRlX0lkLAogICAgICAgICAgICAgICAgICBUeXBlOiAzCiAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIF9jb250ZXh0OC5uZXh0ID0gNDsKICAgICAgICAgICAgICByZXR1cm4gR2V0U3RvcExpc3Qoc3VibWl0T2JqKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICAgICAgICAgIHZhciBzdG9wTWFwID0ge307CiAgICAgICAgICAgICAgICAgIHJlcy5EYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgICAgICAgICBzdG9wTWFwW2l0ZW0uSWRdID0gISFpdGVtLklzX1N0b3A7CiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICBsaXN0LmZvckVhY2goZnVuY3Rpb24gKHJvdykgewogICAgICAgICAgICAgICAgICAgIGlmIChzdG9wTWFwW3Jvdy5QYXJ0X0FnZ3JlZ2F0ZV9JZF0pIHsKICAgICAgICAgICAgICAgICAgICAgIF90aGlzMTEuJHNldChyb3csICdzdG9wRmxhZycsIHN0b3BNYXBbcm93LlBhcnRfQWdncmVnYXRlX0lkXSk7CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDguc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU4KTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgaW5pdFRiRGF0YTogZnVuY3Rpb24gaW5pdFRiRGF0YShsaXN0KSB7CiAgICAgIHZhciBfdGhpczEyID0gdGhpczsKICAgICAgdmFyIHRlYW1LZXkgPSBhcmd1bWVudHMubGVuZ3RoID4gMSAmJiBhcmd1bWVudHNbMV0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1sxXSA6ICdBbGxvY2F0aW9uX1RlYW1zJzsKICAgICAgY29uc29sZS5sb2coNSwgSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShsaXN0KSkpOwogICAgICB0aGlzLnRiRGF0YSA9IGxpc3QubWFwKGZ1bmN0aW9uIChyb3cpIHsKICAgICAgICB2YXIgX3JvdyRUZWNobm9sb2d5X1BhdGg7CiAgICAgICAgdmFyIHByb2Nlc3NMaXN0ID0gKChfcm93JFRlY2hub2xvZ3lfUGF0aCA9IHJvdy5UZWNobm9sb2d5X1BhdGgpID09PSBudWxsIHx8IF9yb3ckVGVjaG5vbG9neV9QYXRoID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfcm93JFRlY2hub2xvZ3lfUGF0aC5zcGxpdCgnLycpKSB8fCBbXTsKICAgICAgICByb3cudXVpZCA9IHV1aWR2NCgpOwogICAgICAgIF90aGlzMTIuYWRkRWxlbWVudFRvVGJEYXRhKHJvdyk7CiAgICAgICAgaWYgKHJvd1t0ZWFtS2V5XSkgewogICAgICAgICAgdmFyIG5ld0RhdGEgPSByb3dbdGVhbUtleV0uZmlsdGVyKGZ1bmN0aW9uIChyKSB7CiAgICAgICAgICAgIHJldHVybiBwcm9jZXNzTGlzdC5maW5kSW5kZXgoZnVuY3Rpb24gKHApIHsKICAgICAgICAgICAgICByZXR1cm4gci5Qcm9jZXNzX0NvZGUgPT09IHA7CiAgICAgICAgICAgIH0pICE9PSAtMTsKICAgICAgICAgIH0pOwogICAgICAgICAgbmV3RGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChlbGUsIGluZGV4KSB7CiAgICAgICAgICAgIHZhciBjb2RlID0gX3RoaXMxMi5nZXRSb3dVbmlxdWUocm93LnV1aWQsIGVsZS5Qcm9jZXNzX0NvZGUsIGVsZS5Xb3JraW5nX1RlYW1fSWQpOwogICAgICAgICAgICB2YXIgbWF4ID0gX3RoaXMxMi5nZXRSb3dVbmlxdWVNYXgocm93LnV1aWQsIGVsZS5Qcm9jZXNzX0NvZGUsIGVsZS5Xb3JraW5nX1RlYW1fSWQpOwogICAgICAgICAgICByb3dbY29kZV0gPSBlbGUuQ291bnQ7CiAgICAgICAgICAgIHJvd1ttYXhdID0gMDsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgICBfdGhpczEyLnNldElucHV0TWF4KHJvdyk7CiAgICAgICAgcmV0dXJuIHJvdzsKICAgICAgfSk7CiAgICAgIHZhciBpZHMgPSAnJzsKICAgICAgaWYgKHRoaXMuaXNDb20pIHsKICAgICAgICBpZHMgPSB0aGlzLnRiRGF0YS5tYXAoZnVuY3Rpb24gKHYpIHsKICAgICAgICAgIHJldHVybiB2LkNvbXBfSW1wb3J0X0RldGFpbF9JZDsKICAgICAgICB9KS50b1N0cmluZygpOwogICAgICB9IGVsc2UgewogICAgICAgIGlkcyA9IHRoaXMudGJEYXRhLm1hcChmdW5jdGlvbiAodikgewogICAgICAgICAgcmV0dXJuIHYuUGFydF9BZ2dyZWdhdGVfSWQ7CiAgICAgICAgfSkudG9TdHJpbmcoKTsKICAgICAgfQogICAgICB0aGlzLmN1cnJlbnRJZHMgPSBpZHM7CiAgICB9LAogICAgYWRkVG9UYkxpc3Q6IGZ1bmN0aW9uIGFkZFRvVGJMaXN0KG5ld0xpc3QpIHsKICAgICAgdmFyIF90aGlzMTMgPSB0aGlzOwogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWU5KCkgewogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlOSQoX2NvbnRleHQ5KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDkucHJldiA9IF9jb250ZXh0OS5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfY29udGV4dDkubmV4dCA9IDI7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzMTMubWVyZ2VTZWxlY3RMaXN0KG5ld0xpc3QpOwogICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgICAgX3RoaXMxMy5zZXRBZGRUYktleSgpOwogICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0OS5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTkpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBtZXJnZVNlbGVjdExpc3Q6IGZ1bmN0aW9uIG1lcmdlU2VsZWN0TGlzdChuZXdMaXN0KSB7CiAgICAgIHZhciBfdGhpczE0ID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlMCgpIHsKICAgICAgICB2YXIgaGFzVXNlZFBhcnRGbGFnOwogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlMCQoX2NvbnRleHQwKSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDAucHJldiA9IF9jb250ZXh0MC5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBjb25zb2xlLnRpbWUoJ2ZmZicpOwogICAgICAgICAgICAgIF9jb250ZXh0MC5uZXh0ID0gMzsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXMxNC5tZXJnZUNyYWZ0UHJvY2VzcyhuZXdMaXN0KTsKICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIGhhc1VzZWRQYXJ0RmxhZyA9IHRydWU7CiAgICAgICAgICAgICAgbmV3TGlzdC5mb3JFYWNoKGZ1bmN0aW9uIChlbGVtZW50LCBpbmRleCkgewogICAgICAgICAgICAgICAgdmFyIGN1ciA9IF90aGlzMTQuZ2V0TWVyZ2VVbmlxdWVSb3coZWxlbWVudCk7CiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygxLCBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KGVsZW1lbnQpKSk7CiAgICAgICAgICAgICAgICBpZiAoIWVsZW1lbnQuVGVjaG5vbG9neV9QYXRoKSB7CiAgICAgICAgICAgICAgICAgIHZhciBjdXJQYXRoQXJyID0gX3RoaXMxNC5jcmFmdENvZGVNYXBbZWxlbWVudC5UZWNobm9sb2d5X0NvZGVdOwogICAgICAgICAgICAgICAgICBpZiAoX3RoaXMxNC5jcmFmdENvZGVNYXBbZWxlbWVudC5UZWNobm9sb2d5X0NvZGVdIGluc3RhbmNlb2YgQXJyYXkpIHsKICAgICAgICAgICAgICAgICAgICBpZiAoZWxlbWVudC5Vbml0X1BhcnRfVXNlZF9Qcm9jZXNzKSB7CiAgICAgICAgICAgICAgICAgICAgICB2YXIgcGFydFVzZWRQcm9jZXNzQXJyID0gZWxlbWVudC5Vbml0X1BhcnRfVXNlZF9Qcm9jZXNzLnNwbGl0KCcsJyk7CiAgICAgICAgICAgICAgICAgICAgICB2YXIgYWxsUGFydHNJbmNsdWRlZCA9IHBhcnRVc2VkUHJvY2Vzc0Fyci5ldmVyeShmdW5jdGlvbiAocGFydCkgewogICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gY3VyUGF0aEFyci5pbmNsdWRlcyhwYXJ0KTsKICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgICAgaWYgKCFhbGxQYXJ0c0luY2x1ZGVkKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIGhhc1VzZWRQYXJ0RmxhZyA9IGZhbHNlOwogICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICAgICAgZWxlbWVudC5UZWNobm9sb2d5X1BhdGggPSBjdXJQYXRoQXJyLmpvaW4oJy8nKTsKICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgICAgZWxlbWVudC5UZWNobm9sb2d5X1BhdGggPSBjdXJQYXRoQXJyLmpvaW4oJy8nKTsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIGlmICghY3VyKSB7CiAgICAgICAgICAgICAgICAgIGVsZW1lbnQucHV1aWQgPSBlbGVtZW50LnV1aWQ7CiAgICAgICAgICAgICAgICAgIGVsZW1lbnQuU2NoZHVsZWRfQ291bnQgPSBlbGVtZW50LmNob29zZUNvdW50OwogICAgICAgICAgICAgICAgICBlbGVtZW50LlNjaGR1bGVkX1dlaWdodCA9IG51bWVyYWwoZWxlbWVudC5jaG9vc2VDb3VudCAqIGVsZW1lbnQuV2VpZ2h0KS5mb3JtYXQoJzAuWzAwXScpOwogICAgICAgICAgICAgICAgICBfdGhpczE0LnRiRGF0YS5wdXNoKGVsZW1lbnQpOwogICAgICAgICAgICAgICAgICBfdGhpczE0LmFkZEVsZW1lbnRUb1RiRGF0YShlbGVtZW50KTsKICAgICAgICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgY3VyLnB1dWlkID0gZWxlbWVudC51dWlkOwogICAgICAgICAgICAgICAgY3VyLlNjaGR1bGVkX0NvdW50ICs9IGVsZW1lbnQuY2hvb3NlQ291bnQ7CiAgICAgICAgICAgICAgICBjdXIuU2NoZHVsZWRfV2VpZ2h0ID0gbnVtZXJhbChjdXIuU2NoZHVsZWRfV2VpZ2h0KS5hZGQoZWxlbWVudC5jaG9vc2VDb3VudCAqIGVsZW1lbnQuV2VpZ2h0KS5mb3JtYXQoJzAuWzAwXScpOwogICAgICAgICAgICAgICAgaWYgKCFjdXIuVGVjaG5vbG9neV9QYXRoKSB7CiAgICAgICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIF90aGlzMTQuc2V0SW5wdXRNYXgoY3VyKTsKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICBjb25zb2xlLmxvZygnaGFzVXNlZFBhcnRGbGFnJywgaGFzVXNlZFBhcnRGbGFnKTsKICAgICAgICAgICAgICBfdGhpczE0LnNob3dDcmFmdFVzZWRQYXJ0UmVzdWx0KGhhc1VzZWRQYXJ0RmxhZyk7CiAgICAgICAgICAgICAgLy8gaWYgKHRoaXMuaXNDb20pIHsKICAgICAgICAgICAgICAvLyAgIHRoaXMudGJEYXRhLnNvcnQoKGEsIGIpID0+IGEuaW5pdFJvd0luZGV4IC0gYi5pbml0Um93SW5kZXgpCiAgICAgICAgICAgICAgLy8gfSBlbHNlIHsKICAgICAgICAgICAgICAvLyAgIHRoaXMudGJEYXRhLnNvcnQoKGEsIGIpID0+IGEuUGFydF9Db2RlLmxvY2FsZUNvbXBhcmUoYi5QYXJ0X0NvZGUpKQogICAgICAgICAgICAgIC8vIH0KICAgICAgICAgICAgICBfdGhpczE0LnRiRGF0YS5zb3J0KGZ1bmN0aW9uIChhLCBiKSB7CiAgICAgICAgICAgICAgICByZXR1cm4gYS5pbml0Um93SW5kZXggLSBiLmluaXRSb3dJbmRleDsKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICBjb25zb2xlLnRpbWVFbmQoJ2ZmZicpOwogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCd0aGlzLnRiRGF0YU1hcCcsIF90aGlzMTQudGJEYXRhTWFwLCBfdGhpczE0LnRiRGF0YSk7CiAgICAgICAgICAgIGNhc2UgMTA6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0MC5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTApOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBzaG93Q3JhZnRVc2VkUGFydFJlc3VsdDogZnVuY3Rpb24gc2hvd0NyYWZ0VXNlZFBhcnRSZXN1bHQoaGFzVXNlZFBhcnQpIHsKICAgICAgdmFyIF90aGlzMTUgPSB0aGlzOwogICAgICBpZiAoaGFzVXNlZFBhcnQpIHJldHVybiB0cnVlOwogICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczE1LiRhbGVydCgn6YOo5YiG6YOo5Lu25bel5bqP6Lev5b6E5YaF5LiN5YyF5ZCr6Zu25Lu26aKG55So5bel5bqP6K+35omL5Yqo5o6S5LqnJywgJ+aPkOekuicsIHsKICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJwogICAgICAgIH0pOwogICAgICB9LCAyMDApOwogICAgICByZXR1cm4gZmFsc2U7CiAgICB9LAogICAgYWRkRWxlbWVudFRvVGJEYXRhOiBmdW5jdGlvbiBhZGRFbGVtZW50VG9UYkRhdGEoZWxlbWVudCkgewogICAgICB2YXIga2V5ID0gdGhpcy5nZXRVbmlLZXkoZWxlbWVudCk7CiAgICAgIHRoaXMudGJEYXRhTWFwW2tleV0gPSBlbGVtZW50OwogICAgfSwKICAgIGdldE1lcmdlVW5pcXVlUm93OiBmdW5jdGlvbiBnZXRNZXJnZVVuaXF1ZVJvdyhlbGVtZW50KSB7CiAgICAgIHZhciBrZXkgPSB0aGlzLmdldFVuaUtleShlbGVtZW50KTsKICAgICAgcmV0dXJuIHRoaXMudGJEYXRhTWFwW2tleV07CiAgICB9LAogICAgZ2V0VW5pS2V5OiBmdW5jdGlvbiBnZXRVbmlLZXkoZWxlbWVudCkgewogICAgICByZXR1cm4gZ2V0VW5pcXVlKHRoaXMuaXNDb20sIGVsZW1lbnQpOwogICAgfSwKICAgIGNoZWNrRm9ybTogZnVuY3Rpb24gY2hlY2tGb3JtKCkgewogICAgICB2YXIgaXNWYWxpZGF0ZSA9IHRydWU7CiAgICAgIHRoaXMuJHJlZnNbJ2Zvcm1JbmxpbmUnXS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAoIXZhbGlkKSBpc1ZhbGlkYXRlID0gZmFsc2U7CiAgICAgIH0pOwogICAgICByZXR1cm4gaXNWYWxpZGF0ZTsKICAgIH0sCiAgICBzYXZlRHJhZnQ6IGZ1bmN0aW9uIHNhdmVEcmFmdCgpIHsKICAgICAgdmFyIF9hcmd1bWVudHMgPSBhcmd1bWVudHMsCiAgICAgICAgX3RoaXMxNiA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvcigvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yUnVudGltZSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTEoKSB7CiAgICAgICAgdmFyIF90aGlzMTYkJHJlZnMkZHJhZnQ7CiAgICAgICAgdmFyIGlzT3JkZXIsIGNoZWNrU3VjY2VzcywgX3RoaXMxNiRnZXRTdWJtaXRUYkluLCB0YWJsZURhdGEsIHN0YXR1cywgaXNTdWNjZXNzOwogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlMSQoX2NvbnRleHQxKSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDEucHJldiA9IF9jb250ZXh0MS5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBpc09yZGVyID0gX2FyZ3VtZW50cy5sZW5ndGggPiAwICYmIF9hcmd1bWVudHNbMF0gIT09IHVuZGVmaW5lZCA/IF9hcmd1bWVudHNbMF0gOiBmYWxzZTsKICAgICAgICAgICAgICBjaGVja1N1Y2Nlc3MgPSBfdGhpczE2LmNoZWNrRm9ybSgpOwogICAgICAgICAgICAgIGlmIChjaGVja1N1Y2Nlc3MpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0MS5uZXh0ID0gNDsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQxLmFicnVwdCgicmV0dXJuIiwgZmFsc2UpOwogICAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgICAgX3RoaXMxNiRnZXRTdWJtaXRUYkluID0gX3RoaXMxNi5nZXRTdWJtaXRUYkluZm8oKSwgdGFibGVEYXRhID0gX3RoaXMxNiRnZXRTdWJtaXRUYkluLnRhYmxlRGF0YSwgc3RhdHVzID0gX3RoaXMxNiRnZXRTdWJtaXRUYkluLnN0YXR1czsKICAgICAgICAgICAgICBpZiAoc3RhdHVzKSB7CiAgICAgICAgICAgICAgICBfY29udGV4dDEubmV4dCA9IDc7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0MS5hYnJ1cHQoInJldHVybiIsIGZhbHNlKTsKICAgICAgICAgICAgY2FzZSA3OgogICAgICAgICAgICAgIGlmICghaXNPcmRlcikgewogICAgICAgICAgICAgICAgX3RoaXMxNi5zYXZlTG9hZGluZyA9IHRydWU7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0MS5uZXh0ID0gMTA7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzMTYuaGFuZGxlU2F2ZURyYWZ0KHRhYmxlRGF0YSwgaXNPcmRlcik7CiAgICAgICAgICAgIGNhc2UgMTA6CiAgICAgICAgICAgICAgaXNTdWNjZXNzID0gX2NvbnRleHQxLnNlbnQ7CiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ2lzU3VjY2VzcycsIGlzU3VjY2Vzcyk7CiAgICAgICAgICAgICAgaWYgKGlzU3VjY2VzcykgewogICAgICAgICAgICAgICAgX2NvbnRleHQxLm5leHQgPSAxNDsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQxLmFicnVwdCgicmV0dXJuIiwgZmFsc2UpOwogICAgICAgICAgICBjYXNlIDE0OgogICAgICAgICAgICAgIGlmICghaXNPcmRlcikgewogICAgICAgICAgICAgICAgX2NvbnRleHQxLm5leHQgPSAxNjsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQxLmFicnVwdCgicmV0dXJuIiwgaXNTdWNjZXNzKTsKICAgICAgICAgICAgY2FzZSAxNjoKICAgICAgICAgICAgICAoX3RoaXMxNiQkcmVmcyRkcmFmdCA9IF90aGlzMTYuJHJlZnNbJ2RyYWZ0J10pID09PSBudWxsIHx8IF90aGlzMTYkJHJlZnMkZHJhZnQgPT09IHZvaWQgMCB8fCBfdGhpczE2JCRyZWZzJGRyYWZ0LmZldGNoRGF0YSgpOwogICAgICAgICAgICAgIF90aGlzMTYuc2F2ZUxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgY2FzZSAxODoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQxLnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIHNhdmVXb3JrU2hvcDogZnVuY3Rpb24gc2F2ZVdvcmtTaG9wKCkgewogICAgICB2YXIgX3RoaXMxNyA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvcigvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yUnVudGltZSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTEwKCkgewogICAgICAgIHZhciBjaGVja1N1Y2Nlc3MsIG9iajsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTEwJChfY29udGV4dDEwKSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDEwLnByZXYgPSBfY29udGV4dDEwLm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIGNoZWNrU3VjY2VzcyA9IF90aGlzMTcuY2hlY2tGb3JtKCk7CiAgICAgICAgICAgICAgaWYgKGNoZWNrU3VjY2VzcykgewogICAgICAgICAgICAgICAgX2NvbnRleHQxMC5uZXh0ID0gMzsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQxMC5hYnJ1cHQoInJldHVybiIsIGZhbHNlKTsKICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIG9iaiA9IHt9OwogICAgICAgICAgICAgIGlmIChfdGhpczE3LnRiRGF0YS5sZW5ndGgpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0MTAubmV4dCA9IDc7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX3RoaXMxNy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICBtZXNzYWdlOiAn5pWw5o2u5LiN6IO95Li656m6JywKICAgICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDEwLmFicnVwdCgicmV0dXJuIik7CiAgICAgICAgICAgIGNhc2UgNzoKICAgICAgICAgICAgICBpZiAoX3RoaXMxNy5pc0NvbSkgewogICAgICAgICAgICAgICAgb2JqLlNjaGR1bGluZ19Db21wcyA9IF90aGlzMTcudGJEYXRhOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBvYmouU2FyZVBhcnRzTW9kZWwgPSBfdGhpczE3LnRiRGF0YTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgaWYgKF90aGlzMTcuaXNFZGl0KSB7CiAgICAgICAgICAgICAgICBvYmouU2NoZHVsaW5nX1BsYW4gPSBfdGhpczE3LmZvcm1JbmxpbmU7CiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIG9iai5TY2hkdWxpbmdfUGxhbiA9IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgX3RoaXMxNy5mb3JtSW5saW5lKSwge30sIHsKICAgICAgICAgICAgICAgICAgUHJvamVjdF9JZDogX3RoaXMxNy5wcm9qZWN0SWQsCiAgICAgICAgICAgICAgICAgIEFyZWFfSWQ6IF90aGlzMTcuYXJlYUlkLAogICAgICAgICAgICAgICAgICBTY2hkdWxpbmdfTW9kZWw6IF90aGlzMTcubW9kZWwgLy8gMeaehOS7tuWNleeLrOaOkuS6p++8jDLpm7bku7bljZXni6zmjpLkuqfvvIwz5p6EL+mbtuS7tuS4gOi1t+aOkuS6pwogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzMTcucGdMb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgICAgICBTYXZlVW5pdFNjaGVkdWxpbmdXb3Jrc2hvcE5ldyhvYmopLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgICAgICAgICAgX3RoaXMxNy5wZ0xvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgX3RoaXMxNy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogJ+S/neWtmOaIkOWKnycsCiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICBfdGhpczE3LmNsb3NlVmlldygpOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgX3RoaXMxNy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgX3RoaXMxNy5wZ0xvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgY2FzZSAxMToKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQxMC5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTEwKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgZ2V0U3VibWl0VGJJbmZvOiBmdW5jdGlvbiBnZXRTdWJtaXRUYkluZm8oKSB7CiAgICAgIHZhciBfdGhpczE4ID0gdGhpczsKICAgICAgLy8g5aSE55CG5LiK5Lyg55qE5pWw5o2uCiAgICAgIHZhciB0YWJsZURhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMudGJEYXRhKSk7CiAgICAgIHRhYmxlRGF0YSA9IHRhYmxlRGF0YS5maWx0ZXIoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICByZXR1cm4gaXRlbS5TY2hkdWxlZF9Db3VudCA+IDA7CiAgICAgIH0pOwogICAgICB2YXIgX2xvb3AgPSBmdW5jdGlvbiBfbG9vcCgpIHsKICAgICAgICAgIHZhciBlbGVtZW50ID0gdGFibGVEYXRhW2ldOwogICAgICAgICAgdmFyIGxpc3QgPSBbXTsKICAgICAgICAgIGlmICghZWxlbWVudC5UZWNobm9sb2d5X1BhdGgpIHsKICAgICAgICAgICAgX3RoaXMxOC4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgbWVzc2FnZTogJ+W3peW6j+S4jeiDveS4uuepuicsCiAgICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICAgIHY6IHsKICAgICAgICAgICAgICAgIHN0YXR1czogZmFsc2UKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH07CiAgICAgICAgICB9CiAgICAgICAgICBpZiAoX3RoaXMxOC5pc1BhcnRQcmVwYXJlICYmICFlbGVtZW50LlBhcnRfVXNlZF9Qcm9jZXNzICYmIGVsZW1lbnQuVHlwZSAhPT0gJ0RpcmVjdCcgJiYgX3RoaXMxOC5jb21QYXJ0KSB7CiAgICAgICAgICAgIHZhciBtc2cgPSAn6aKG55So5bel5bqP5LiN6IO95Li656m6JzsKICAgICAgICAgICAgaWYgKF90aGlzMTguaXNOZXN0KSB7CiAgICAgICAgICAgICAgaWYgKGVsZW1lbnQuQ29tcF9JbXBvcnRfRGV0YWlsX0lkKSB7CiAgICAgICAgICAgICAgICBfdGhpczE4LiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgICAgbWVzc2FnZTogbXNnLAogICAgICAgICAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgICAgICAgdjogewogICAgICAgICAgICAgICAgICAgIHN0YXR1czogZmFsc2UKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgX3RoaXMxOC4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICBtZXNzYWdlOiBtc2csCiAgICAgICAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICAgICAgdjogewogICAgICAgICAgICAgICAgICBzdGF0dXM6IGZhbHNlCiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgICAgLy8gaWYgKCF0aGlzLmlzQ29tICYmIGVsZW1lbnQuQ29tcF9JbXBvcnRfRGV0YWlsX0lkICYmICFlbGVtZW50LlBhcnRfVXNlZF9Qcm9jZXNzKSB7CiAgICAgICAgICAvLyAgIC8vIOmbtuaehOS7tiDpm7bku7bljZXni6zmjpLkuqcKICAgICAgICAgIC8vICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAvLyAgICAgbWVzc2FnZTogJ+mbtuS7tumihueUqOW3peW6j+S4jeiDveS4uuepuicsCiAgICAgICAgICAvLyAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgICAvLyAgIH0pCiAgICAgICAgICAvLyAgIHJldHVybiB7IHN0YXR1czogZmFsc2UgfQogICAgICAgICAgLy8gfQogICAgICAgICAgaWYgKGVsZW1lbnQuU2NoZWR1bGVkX1RlY2hub2xvZ3lfUGF0aCAmJiBlbGVtZW50LlNjaGVkdWxlZF9UZWNobm9sb2d5X1BhdGggIT09IGVsZW1lbnQuVGVjaG5vbG9neV9QYXRoKSB7CiAgICAgICAgICAgIF90aGlzMTguJG1lc3NhZ2UoewogICAgICAgICAgICAgIG1lc3NhZ2U6ICJcdThCRjdcdTU0OENcdThCRTVcdTUzM0FcdTU3REZcdTYyNzlcdTZCMjFcdTRFMEJcdTVERjJcdTYzOTJcdTRFQTdcdTU0MEMiLmNvbmNhdChfdGhpczE4LmlzQ29tID8gJ+aehOS7ticgOiAn6YOo5Lu2JywgIlx1NEZERFx1NjMwMVx1NURFNVx1NUU4Rlx1NEUwMFx1ODFGNCIpLAogICAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgICB2OiB7CiAgICAgICAgICAgICAgICBzdGF0dXM6IGZhbHNlCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9OwogICAgICAgICAgfQogICAgICAgICAgaWYgKGVsZW1lbnQuU2NoZWR1bGVkX1VzZWRfUHJvY2VzcyAmJiBlbGVtZW50LlNjaGVkdWxlZF9Vc2VkX1Byb2Nlc3MgIT09IGVsZW1lbnQuUGFydF9Vc2VkX1Byb2Nlc3MpIHsKICAgICAgICAgICAgX3RoaXMxOC4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgbWVzc2FnZTogIlx1OEJGN1x1NTQ4Q1x1OEJFNVx1NTMzQVx1NTdERlx1NjI3OVx1NkIyMVx1NEUwQlx1NURGMlx1NjM5Mlx1NEVBN1x1NTQwQ1x1OTBFOFx1NEVGNlx1OTg4Nlx1NzUyOFx1NURFNVx1NUU4Rlx1NEZERFx1NjMwMVx1NEUwMFx1ODFGNCIsCiAgICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICAgIHY6IHsKICAgICAgICAgICAgICAgIHN0YXR1czogZmFsc2UKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH07CiAgICAgICAgICB9CiAgICAgICAgICB2YXIgcHJvY2Vzc0xpc3QgPSBBcnJheS5mcm9tKG5ldyBTZXQoZWxlbWVudC5UZWNobm9sb2d5X1BhdGguc3BsaXQoJy8nKSkpOwogICAgICAgICAgLy8gcHJvY2Vzc0xpc3QuZm9yRWFjaChjb2RlID0+IHsKICAgICAgICAgIC8vICAgY29uc3QgZ3JvdXBzID0gZWxlbWVudC5BbGxvY2F0aW9uX1RlYW1zLmZpbHRlcih2ID0+IHYuUHJvY2Vzc19Db2RlID09PSBjb2RlKQogICAgICAgICAgLy8gICAgY29uc3QgZ3JvdXBzTGlzdCA9IGdyb3Vwcy5tYXAoZ3JvdXAgPT4gewogICAgICAgICAgLy8gICBjb25zdCB1Q29kZSA9IHRoaXMuZ2V0Um93VW5pcXVlKGVsZW1lbnQudXVpZCwgY29kZSwgZ3JvdXAuV29ya2luZ19UZWFtX0lkKQogICAgICAgICAgLy8gICBjb25zdCB1TWF4ID0gdGhpcy5nZXRSb3dVbmlxdWVNYXgoZWxlbWVudC51dWlkLCBjb2RlLCBncm91cC5Xb3JraW5nX1RlYW1fSWQpCiAgICAgICAgICAvLyAgIGNvbnN0IG9iaiA9IHsKICAgICAgICAgIC8vICAgICBUZWFtX1Rhc2tfSWQ6IGVsZW1lbnQuVGVhbV9UYXNrX0lkLAogICAgICAgICAgLy8gICAgIENvbXBfQ29kZTogZWxlbWVudC5Db21wX0NvZGUsCiAgICAgICAgICAvLyAgICAgQWdhaW5fQ291bnQ6ICtlbGVtZW50W3VDb2RlXSB8fCAwLCAvLyDkuI3loavvvIzlkI7lj7DorqnkvKAwCiAgICAgICAgICAvLyAgICAgUGFydF9Db2RlOiB0aGlzLmlzQ29tID8gbnVsbCA6ICcnLAogICAgICAgICAgLy8gICAgIFByb2Nlc3NfQ29kZTogY29kZSwKICAgICAgICAgIC8vICAgICBUZWNobm9sb2d5X1BhdGg6IGVsZW1lbnQuVGVjaG5vbG9neV9QYXRoLAogICAgICAgICAgLy8gICAgIFdvcmtpbmdfVGVhbV9JZDogZ3JvdXAuV29ya2luZ19UZWFtX0lkLAogICAgICAgICAgLy8gICAgIFdvcmtpbmdfVGVhbV9OYW1lOiBncm91cC5Xb3JraW5nX1RlYW1fTmFtZQogICAgICAgICAgLy8gICB9CiAgICAgICAgICAvLyAgIGRlbGV0ZSBlbGVtZW50W3VDb2RlXQogICAgICAgICAgLy8gICBkZWxldGUgZWxlbWVudFt1TWF4XQogICAgICAgICAgLy8gICByZXR1cm4gb2JqCiAgICAgICAgICAvLyB9KQogICAgICAgICAgLy8gY29uc3QgYWdhaW5Db3VudCA9IGxpc3QucmVkdWNlKChhY2MsIGN1cikgPT4gewogICAgICAgICAgLy8gICByZXR1cm4gYWNjICsgY3VyLkFnYWluX0NvdW50CiAgICAgICAgICAvLyB9LCAwKQogICAgICAgICAgLy8gaWYgKGFnYWluQ291bnQgPiBlbGVtZW50LlNjaGR1bGVkX0NvdW50KSB7CiAgICAgICAgICAvLyAgIGVsZW1lbnQuQWxsb2NhdGlvbl9UZWFtcyA9IFtdCiAgICAgICAgICAvLyB9CiAgICAgICAgICB2YXIgX2xvb3AyID0gZnVuY3Rpb24gX2xvb3AyKCkgewogICAgICAgICAgICB2YXIgY29kZSA9IHByb2Nlc3NMaXN0W2pdOwogICAgICAgICAgICB2YXIgc2NoZHVsZWRDb3VudCA9IGVsZW1lbnQuU2NoZHVsZWRfQ291bnQgfHwgMDsKICAgICAgICAgICAgdmFyIGdyb3VwcyA9IFtdOwogICAgICAgICAgICBpZiAoZWxlbWVudC5BbGxvY2F0aW9uX1RlYW1zKSB7CiAgICAgICAgICAgICAgZ3JvdXBzID0gZWxlbWVudC5BbGxvY2F0aW9uX1RlYW1zLmZpbHRlcihmdW5jdGlvbiAodikgewogICAgICAgICAgICAgICAgcmV0dXJuIHYuUHJvY2Vzc19Db2RlID09PSBjb2RlOwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICAgIHZhciBhZ2FpbkNvdW50ID0gZ3JvdXBzLnJlZHVjZShmdW5jdGlvbiAoYWNjLCBjdXIpIHsKICAgICAgICAgICAgICByZXR1cm4gYWNjICsgKGN1ci5BZ2Fpbl9Db3VudCB8fCAwKTsKICAgICAgICAgICAgfSwgMCk7CiAgICAgICAgICAgIGlmIChhZ2FpbkNvdW50ID4gc2NoZHVsZWRDb3VudCkgewogICAgICAgICAgICAgIGxpc3QgPSBbXTsKICAgICAgICAgICAgICByZXR1cm4gMTsgLy8gYnJlYWsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB2YXIgX2xpc3QyOwogICAgICAgICAgICAgIChfbGlzdDIgPSBsaXN0KS5wdXNoLmFwcGx5KF9saXN0MiwgX3RvQ29uc3VtYWJsZUFycmF5KGdyb3VwcykpOwogICAgICAgICAgICB9CiAgICAgICAgICB9OwogICAgICAgICAgZm9yICh2YXIgaiA9IDA7IGogPCBwcm9jZXNzTGlzdC5sZW5ndGg7IGorKykgewogICAgICAgICAgICBpZiAoX2xvb3AyKCkpIGJyZWFrOwogICAgICAgICAgfQogICAgICAgICAgdmFyIGhhc0lucHV0ID0gT2JqZWN0LmtleXMoZWxlbWVudCkuZmlsdGVyKGZ1bmN0aW9uIChfKSB7CiAgICAgICAgICAgIHJldHVybiBfLnN0YXJ0c1dpdGgoZWxlbWVudFsndXVpZCddKTsKICAgICAgICAgIH0pOwogICAgICAgICAgaGFzSW5wdXQuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICBkZWxldGUgZWxlbWVudFtpdGVtXTsKICAgICAgICAgIH0pOwogICAgICAgICAgZGVsZXRlIGVsZW1lbnRbJ3V1aWQnXTsKICAgICAgICAgIGRlbGV0ZSBlbGVtZW50WydfWF9ST1dfS0VZJ107CiAgICAgICAgICBkZWxldGUgZWxlbWVudFsncHV1aWQnXTsKICAgICAgICAgIGVsZW1lbnQuQWxsb2NhdGlvbl9UZWFtcyA9IGxpc3Q7CiAgICAgICAgfSwKICAgICAgICBfcmV0OwogICAgICBmb3IgKHZhciBpID0gMDsgaSA8IHRhYmxlRGF0YS5sZW5ndGg7IGkrKykgewogICAgICAgIF9yZXQgPSBfbG9vcCgpOwogICAgICAgIGlmIChfcmV0KSByZXR1cm4gX3JldC52OwogICAgICB9CiAgICAgIHJldHVybiB7CiAgICAgICAgdGFibGVEYXRhOiB0YWJsZURhdGEsCiAgICAgICAgc3RhdHVzOiB0cnVlCiAgICAgIH07CiAgICB9LAogICAgaGFuZGxlU2F2ZURyYWZ0OiBmdW5jdGlvbiBoYW5kbGVTYXZlRHJhZnQodGFibGVEYXRhLCBpc09yZGVyKSB7CiAgICAgIHZhciBfdGhpczE5ID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlMTEoKSB7CiAgICAgICAgdmFyIF9mdW4sIG9iaiwgcCwgb2JqS2V5LCBvcmRlclN1Y2Nlc3M7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUxMSQoX2NvbnRleHQxMSkgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQxMS5wcmV2ID0gX2NvbnRleHQxMS5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5L+d5a2Y6I2J56i/Jyk7CiAgICAgICAgICAgICAgX2Z1biA9IFNhdmVVbml0U2NoZHVsaW5nRHJhZnROZXc7CiAgICAgICAgICAgICAgb2JqID0ge307CiAgICAgICAgICAgICAgb2JqLlNhcmVQYXJ0c01vZGVsID0gdGFibGVEYXRhOwogICAgICAgICAgICAgIHAgPSBbXTsKICAgICAgICAgICAgICBmb3IgKG9iaktleSBpbiBfdGhpczE5LnByb2Nlc3NMaXN0KSB7CiAgICAgICAgICAgICAgICBpZiAoX3RoaXMxOS5wcm9jZXNzTGlzdC5oYXNPd25Qcm9wZXJ0eShvYmpLZXkpKSB7CiAgICAgICAgICAgICAgICAgIHAucHVzaChfdGhpczE5LnByb2Nlc3NMaXN0W29iaktleV0pOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBvYmouUHJvY2Vzc19MaXN0ID0gcDsKICAgICAgICAgICAgICBpZiAoX3RoaXMxOS5pc0VkaXQpIHsKICAgICAgICAgICAgICAgIG9iai5TY2hkdWxpbmdfUGxhbiA9IF90aGlzMTkuZm9ybUlubGluZTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgb2JqLlNjaGR1bGluZ19QbGFuID0gX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBfdGhpczE5LmZvcm1JbmxpbmUpLCB7fSwgewogICAgICAgICAgICAgICAgICBQcm9qZWN0X0lkOiBfdGhpczE5LnByb2plY3RJZCwKICAgICAgICAgICAgICAgICAgQXJlYV9JZDogX3RoaXMxOS5hcmVhSWQsCiAgICAgICAgICAgICAgICAgIFNjaGR1bGluZ19Nb2RlbDogX3RoaXMxOS5tb2RlbCAvLyAx5p6E5Lu25Y2V54us5o6S5Lqn77yMMumbtuS7tuWNleeLrOaOkuS6p++8jDPmnoQv6Zu25Lu25LiA6LW35o6S5LqnCiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgb3JkZXJTdWNjZXNzID0gZmFsc2U7CiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ29iaicsIG9iaik7CiAgICAgICAgICAgICAgX2NvbnRleHQxMS5uZXh0ID0gMTI7CiAgICAgICAgICAgICAgcmV0dXJuIF9mdW4ob2JqKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICAgICAgICAgIGlmICghaXNPcmRlcikgewogICAgICAgICAgICAgICAgICAgIF90aGlzMTkucGdMb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgICAgX3RoaXMxOS4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAn5L+d5a2Y5oiQ5YqfJywKICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJwogICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgIF90aGlzMTkuY2xvc2VWaWV3KCk7CiAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgX3RoaXMxOS50ZW1wbGF0ZVNjaGVkdWxlQ29kZSA9IHJlcy5EYXRhOwogICAgICAgICAgICAgICAgICAgIG9yZGVyU3VjY2VzcyA9IHRydWU7CiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+S/neWtmOiNieeov+aIkOWKnyAnKTsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgX3RoaXMxOS5zYXZlTG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgICAgICAgICBfdGhpczE5LnBnTG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgICAgICAgICBfdGhpczE5LiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICBjYXNlIDEyOgogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfnu5PmnZ8gJyk7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0MTEuYWJydXB0KCJyZXR1cm4iLCBvcmRlclN1Y2Nlc3MpOwogICAgICAgICAgICBjYXNlIDE0OgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDExLnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMTEpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBoYW5kbGVEZWxldGU6IGZ1bmN0aW9uIGhhbmRsZURlbGV0ZSgpIHsKICAgICAgdmFyIF90aGlzMjAgPSB0aGlzOwogICAgICB0aGlzLmRlbGV0ZUxvYWRpbmcgPSB0cnVlOwogICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICB2YXIgc2VsZWN0ZWRVdWlkcyA9IG5ldyBTZXQoX3RoaXMyMC5tdWx0aXBsZVNlbGVjdGlvbi5tYXAoZnVuY3Rpb24gKHYpIHsKICAgICAgICAgIHJldHVybiB2LnV1aWQ7CiAgICAgICAgfSkpOwogICAgICAgIF90aGlzMjAudGJEYXRhID0gX3RoaXMyMC50YkRhdGEuZmlsdGVyKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICB2YXIgaXNTZWxlY3RlZCA9IHNlbGVjdGVkVXVpZHMuaGFzKGl0ZW0udXVpZCk7CiAgICAgICAgICBpZiAoaXNTZWxlY3RlZCkgewogICAgICAgICAgICB2YXIga2V5ID0gX3RoaXMyMC5nZXRVbmlLZXkoaXRlbSk7CiAgICAgICAgICAgIGRlbGV0ZSBfdGhpczIwLnRiRGF0YU1hcFtrZXldOwogICAgICAgICAgfQogICAgICAgICAgcmV0dXJuICFpc1NlbGVjdGVkOwogICAgICAgIH0pOwogICAgICAgIC8vIHRoaXMuJG5leHRUaWNrKF8gPT4gewogICAgICAgIC8vICAgY29uc3QgX2xpc3QgPSB0aGlzLm11bHRpcGxlU2VsZWN0aW9uLmZpbHRlcih2ID0+IHYucHV1aWQpCiAgICAgICAgLy8gICB0aGlzLiRyZWZzWydkcmFmdCddPy5tZXJnZURhdGEoX2xpc3QpCiAgICAgICAgLy8gICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gW10KICAgICAgICAvLyB9KQogICAgICAgIF90aGlzMjAuZGVsZXRlTG9hZGluZyA9IGZhbHNlOwogICAgICB9LCAwKTsKICAgIH0sCiAgICBnZXRXb3JrVGVhbTogZnVuY3Rpb24gZ2V0V29ya1RlYW0oKSB7CiAgICAgIHZhciBfdGhpczIxID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlMTIoKSB7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUxMiQoX2NvbnRleHQxMikgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQxMi5wcmV2ID0gX2NvbnRleHQxMi5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfY29udGV4dDEyLm5leHQgPSAyOwogICAgICAgICAgICAgIHJldHVybiBHZXRTY2hkdWxpbmdXb3JraW5nVGVhbXMoewogICAgICAgICAgICAgICAgdHlwZTogMwogICAgICAgICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgICAgICAgICAgX3RoaXMyMS53b3JraW5nVGVhbSA9IHJlcy5EYXRhOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgX3RoaXMyMS4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDEyLnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMTIpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBoYW5kbGVTdWJtaXQ6IGZ1bmN0aW9uIGhhbmRsZVN1Ym1pdCgpIHsKICAgICAgdmFyIF90aGlzMjIgPSB0aGlzOwogICAgICB0aGlzLiRyZWZzWydmb3JtSW5saW5lJ10udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKCF2YWxpZCkgcmV0dXJuOwogICAgICAgIHZhciBfdGhpczIyJGdldFN1Ym1pdFRiSW4gPSBfdGhpczIyLmdldFN1Ym1pdFRiSW5mbygpLAogICAgICAgICAgdGFibGVEYXRhID0gX3RoaXMyMiRnZXRTdWJtaXRUYkluLnRhYmxlRGF0YSwKICAgICAgICAgIHN0YXR1cyA9IF90aGlzMjIkZ2V0U3VibWl0VGJJbi5zdGF0dXM7CiAgICAgICAgaWYgKCFzdGF0dXMpIHJldHVybjsKICAgICAgICBfdGhpczIyLiRjb25maXJtKCfmmK/lkKbmj5DkuqTlvZPliY3mlbDmja4/JywgJ+aPkOekuicsIHsKICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgICBfdGhpczIyLnNhdmVEcmFmdERvU3VibWl0KHRhYmxlRGF0YSk7CiAgICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgICAgX3RoaXMyMi4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdpbmZvJywKICAgICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iCcKICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICBzYXZlRHJhZnREb1N1Ym1pdDogZnVuY3Rpb24gc2F2ZURyYWZ0RG9TdWJtaXQoKSB7CiAgICAgIHZhciBfdGhpczIzID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlMTMoKSB7CiAgICAgICAgdmFyIF90aGlzMjMkZm9ybUlubGluZTsKICAgICAgICB2YXIgaXNTdWNjZXNzLCBfaXNTdWNjZXNzOwogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlMTMkKF9jb250ZXh0MTMpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0MTMucHJldiA9IF9jb250ZXh0MTMubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX3RoaXMyMy5wZ0xvYWRpbmcgPSB0cnVlOwogICAgICAgICAgICAgIGlmICghKChfdGhpczIzJGZvcm1JbmxpbmUgPSBfdGhpczIzLmZvcm1JbmxpbmUpICE9PSBudWxsICYmIF90aGlzMjMkZm9ybUlubGluZSAhPT0gdm9pZCAwICYmIF90aGlzMjMkZm9ybUlubGluZS5TY2hkdWxpbmdfQ29kZSkpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0MTMubmV4dCA9IDk7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX2NvbnRleHQxMy5uZXh0ID0gNDsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXMyMy5zYXZlRHJhZnQodHJ1ZSk7CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICBpc1N1Y2Nlc3MgPSBfY29udGV4dDEzLnNlbnQ7CiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ3NhdmVEcmFmdERvU3VibWl0JywgaXNTdWNjZXNzKTsKICAgICAgICAgICAgICBpc1N1Y2Nlc3MgJiYgX3RoaXMyMy5kb1N1Ym1pdChfdGhpczIzLmZvcm1JbmxpbmUuSWQpOwogICAgICAgICAgICAgIF9jb250ZXh0MTMubmV4dCA9IDEzOwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDk6CiAgICAgICAgICAgICAgX2NvbnRleHQxMy5uZXh0ID0gMTE7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzMjMuc2F2ZURyYWZ0KHRydWUpOwogICAgICAgICAgICBjYXNlIDExOgogICAgICAgICAgICAgIF9pc1N1Y2Nlc3MgPSBfY29udGV4dDEzLnNlbnQ7CiAgICAgICAgICAgICAgX2lzU3VjY2VzcyAmJiBfdGhpczIzLmRvU3VibWl0KF90aGlzMjMudGVtcGxhdGVTY2hlZHVsZUNvZGUpOwogICAgICAgICAgICBjYXNlIDEzOgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDEzLnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMTMpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBkb1N1Ym1pdDogZnVuY3Rpb24gZG9TdWJtaXQoc2NoZWR1bGVDb2RlKSB7CiAgICAgIHZhciBfdGhpczI0ID0gdGhpczsKICAgICAgU2F2ZVNjaGR1bGluZ1Rhc2tCeUlkKHsKICAgICAgICBzY2hkdWxpbmdQbGFuSWQ6IHNjaGVkdWxlQ29kZQogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgX3RoaXMyNC4kbWVzc2FnZSh7CiAgICAgICAgICAgIG1lc3NhZ2U6ICfkuIvovr7miJDlip8nLAogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycKICAgICAgICAgIH0pOwogICAgICAgICAgX3RoaXMyNC5jbG9zZVZpZXcoKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXMyNC4kbWVzc2FnZSh7CiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pLmZpbmFsbHkoZnVuY3Rpb24gKF8pIHsKICAgICAgICBfdGhpczI0LnBnTG9hZGluZyA9IGZhbHNlOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoXykgewogICAgICAgIF90aGlzMjQucGdMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIGdldFdvcmtTaG9wOiBmdW5jdGlvbiBnZXRXb3JrU2hvcCh2YWx1ZSkgewogICAgICB2YXIgX3RoaXMyNSA9IHRoaXM7CiAgICAgIGNvbnNvbGUubG9nKCd2YWx1ZScsIHZhbHVlKTsKICAgICAgdmFyIG9yaWdpbiA9IHZhbHVlLm9yaWdpbiwKICAgICAgICByb3cgPSB2YWx1ZS5yb3csCiAgICAgICAgX3ZhbHVlJHdvcmtTaG9wID0gdmFsdWUud29ya1Nob3AsCiAgICAgICAgSWQgPSBfdmFsdWUkd29ya1Nob3AuSWQsCiAgICAgICAgRGlzcGxheV9OYW1lID0gX3ZhbHVlJHdvcmtTaG9wLkRpc3BsYXlfTmFtZTsKICAgICAgaWYgKG9yaWdpbiA9PT0gMikgewogICAgICAgIHZhciBfdmFsdWUkd29ya1Nob3AyOwogICAgICAgIGlmICgoX3ZhbHVlJHdvcmtTaG9wMiA9IHZhbHVlLndvcmtTaG9wKSAhPT0gbnVsbCAmJiBfdmFsdWUkd29ya1Nob3AyICE9PSB2b2lkIDAgJiYgX3ZhbHVlJHdvcmtTaG9wMi5JZCkgewogICAgICAgICAgcm93LldvcmtzaG9wX05hbWUgPSBEaXNwbGF5X05hbWU7CiAgICAgICAgICByb3cuV29ya3Nob3BfSWQgPSBJZDsKICAgICAgICAgIHRoaXMuc2V0UGF0aChyb3csIElkKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgcm93LldvcmtzaG9wX05hbWUgPSAnJzsKICAgICAgICAgIHJvdy5Xb3Jrc2hvcF9JZCA9ICcnOwogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgIHZhciBfdmFsdWUkd29ya1Nob3AzOwogICAgICAgICAgaWYgKChfdmFsdWUkd29ya1Nob3AzID0gdmFsdWUud29ya1Nob3ApICE9PSBudWxsICYmIF92YWx1ZSR3b3JrU2hvcDMgIT09IHZvaWQgMCAmJiBfdmFsdWUkd29ya1Nob3AzLklkKSB7CiAgICAgICAgICAgIGl0ZW0uV29ya3Nob3BfTmFtZSA9IERpc3BsYXlfTmFtZTsKICAgICAgICAgICAgaXRlbS5Xb3Jrc2hvcF9JZCA9IElkOwogICAgICAgICAgICBfdGhpczI1LnNldFBhdGgoaXRlbSwgSWQpOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgaXRlbS5Xb3Jrc2hvcF9OYW1lID0gJyc7CiAgICAgICAgICAgIGl0ZW0uV29ya3Nob3BfSWQgPSAnJzsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfQogICAgfSwKICAgIHNldFBhdGg6IGZ1bmN0aW9uIHNldFBhdGgocm93LCBJZCkgewogICAgICBpZiAocm93ICE9PSBudWxsICYmIHJvdyAhPT0gdm9pZCAwICYmIHJvdy5TY2hlZHVsZWRfV29ya3Nob3BfSWQpIHsKICAgICAgICBpZiAocm93LlNjaGVkdWxlZF9Xb3Jrc2hvcF9JZCAhPT0gSWQpIHsKICAgICAgICAgIHJvdy5UZWNobm9sb2d5X1BhdGggPSAnJzsKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcm93LlRlY2hub2xvZ3lfUGF0aCA9ICcnOwogICAgICB9CiAgICB9LAogICAgaGFuZGxlQmF0Y2hXb3Jrc2hvcDogZnVuY3Rpb24gaGFuZGxlQmF0Y2hXb3Jrc2hvcChvcmlnaW4sIHJvdykgewogICAgICB2YXIgX3RoaXMyNiA9IHRoaXM7CiAgICAgIHRoaXMudGl0bGUgPSBvcmlnaW4gPT09IDEgPyAn5om56YeP5YiG6YWN6L2m6Ze0JyA6ICfliIbphY3ovabpl7QnOwogICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSAnV29ya3Nob3AnOwogICAgICB0aGlzLmRXaWR0aCA9ICczMCUnOwogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoXykgewogICAgICAgIF90aGlzMjYuJHJlZnNbJ2NvbnRlbnQnXS5mZXRjaERhdGEob3JpZ2luLCByb3cpOwogICAgICB9KTsKICAgIH0sCiAgICBnZXRQcm9jZXNzT3B0aW9uOiBmdW5jdGlvbiBnZXRQcm9jZXNzT3B0aW9uKHdvcmtzaG9wSWQpIHsKICAgICAgdmFyIF90aGlzMjcgPSB0aGlzOwogICAgICByZXR1cm4gbmV3IFByb21pc2UoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkgewogICAgICAgIEdldFByb2Nlc3NMaXN0QmFzZSh7CiAgICAgICAgICB3b3Jrc2hvcElkOiB3b3Jrc2hvcElkLAogICAgICAgICAgdHlwZTogMwogICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgICAgdmFyIF9wcm9jZXNzID0gcmVzLkRhdGEubWFwKGZ1bmN0aW9uICh2KSB7CiAgICAgICAgICAgICAgcmV0dXJuIHYuQ29kZTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIHJlc29sdmUoX3Byb2Nlc3MpOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgX3RoaXMyNy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgc2V0TGliVHlwZTogZnVuY3Rpb24gc2V0TGliVHlwZShjb2RlLCB3b3Jrc2hvcElkKSB7CiAgICAgIHZhciBfdGhpczI4ID0gdGhpczsKICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uIChyZXNvbHZlKSB7CiAgICAgICAgdmFyIG9iaiA9IHsKICAgICAgICAgIENvbXBvbmVudF90eXBlOiBjb2RlLAogICAgICAgICAgdHlwZTogMQogICAgICAgIH07CiAgICAgICAgaWYgKF90aGlzMjgud29ya3Nob3BFbmFibGVkKSB7CiAgICAgICAgICBvYmoud29ya3Nob3BJZCA9IHdvcmtzaG9wSWQ7CiAgICAgICAgfQogICAgICAgIEdldExpYkxpc3RUeXBlKG9iaikudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICBpZiAocmVzLkRhdGEuRGF0YSAmJiByZXMuRGF0YS5EYXRhLmxlbmd0aCkgewogICAgICAgICAgICAgIHZhciBpbmZvID0gcmVzLkRhdGEuRGF0YVswXTsKICAgICAgICAgICAgICB2YXIgd29ya0NvZGUgPSBpbmZvLldvcmtDb2RlICYmIGluZm8uV29ya0NvZGUucmVwbGFjZSgvXFwvZywgJy8nKTsKICAgICAgICAgICAgICByZXNvbHZlKHdvcmtDb2RlKTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICByZXNvbHZlKHVuZGVmaW5lZCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIF90aGlzMjguJG1lc3NhZ2UoewogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIGlucHV0Q2hhbmdlOiBmdW5jdGlvbiBpbnB1dENoYW5nZShyb3cpIHsKICAgICAgdGhpcy5zZXRJbnB1dE1heChyb3cpOwogICAgfSwKICAgIHNldElucHV0TWF4OiBmdW5jdGlvbiBzZXRJbnB1dE1heChyb3cpIHsKICAgICAgdmFyIGlucHV0VmFsdWVzS2V5cyA9IE9iamVjdC5rZXlzKHJvdykuZmlsdGVyKGZ1bmN0aW9uICh2KSB7CiAgICAgICAgcmV0dXJuICF2LmVuZHNXaXRoKCdtYXgnKSAmJiB2LnN0YXJ0c1dpdGgocm93LnV1aWQpICYmIHYubGVuZ3RoID4gcm93LnV1aWQubGVuZ3RoOwogICAgICB9KTsKICAgICAgaW5wdXRWYWx1ZXNLZXlzLmZvckVhY2goZnVuY3Rpb24gKHZhbCkgewogICAgICAgIHZhciBjdXJDb2RlID0gdmFsLnNwbGl0KFNQTElUX1NZTUJPTClbMV07CiAgICAgICAgdmFyIG90aGVyVG90YWwgPSBpbnB1dFZhbHVlc0tleXMuZmlsdGVyKGZ1bmN0aW9uICh4KSB7CiAgICAgICAgICB2YXIgY29kZSA9IHguc3BsaXQoU1BMSVRfU1lNQk9MKVsxXTsKICAgICAgICAgIHJldHVybiB4ICE9PSB2YWwgJiYgY29kZSA9PT0gY3VyQ29kZTsKICAgICAgICB9KS5yZWR1Y2UoZnVuY3Rpb24gKGFjYywgaXRlbSkgewogICAgICAgICAgcmV0dXJuIGFjYyArIG51bWVyYWwocm93W2l0ZW1dKS52YWx1ZSgpOwogICAgICAgIH0sIDApOwogICAgICAgIHJvd1t2YWwgKyBTUExJVF9TWU1CT0wgKyAnbWF4J10gPSByb3cuU2NoZHVsZWRfQ291bnQgLSBvdGhlclRvdGFsOwogICAgICB9KTsKICAgIH0sCiAgICBzZW5kUHJvY2VzczogZnVuY3Rpb24gc2VuZFByb2Nlc3MoX3JlZikgewogICAgICB2YXIgYXJyID0gX3JlZi5hcnIsCiAgICAgICAgc3RyID0gX3JlZi5zdHI7CiAgICAgIHZhciBpc1N1Y2Nlc3MgPSB0cnVlOwogICAgICBmb3IgKHZhciBpID0gMDsgaSA8IGFyci5sZW5ndGg7IGkrKykgewogICAgICAgIHZhciBpdGVtID0gYXJyW2ldOwogICAgICAgIGlmIChpdGVtLm9yaWdpbmFsUGF0aCAmJiBpdGVtLm9yaWdpbmFsUGF0aCAhPT0gc3RyKSB7CiAgICAgICAgICBpc1N1Y2Nlc3MgPSBmYWxzZTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIH0KICAgICAgICBpdGVtLlRlY2hub2xvZ3lfUGF0aCA9IHN0cjsKICAgICAgfQogICAgICBpZiAoIWlzU3VjY2VzcykgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgbWVzc2FnZTogJ+ivt+WSjOivpeWMuuWfn+aJueasoeS4i+W3suaOkuS6p+WQjOmDqOS7tuS/neaMgeW3peW6j+S4gOiHtCcsCiAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICB9KTsKICAgICAgfQogICAgfSwKICAgIHJlc2V0V29ya1RlYW1NYXg6IGZ1bmN0aW9uIHJlc2V0V29ya1RlYW1NYXgocm93LCBzdHIpIHsKICAgICAgdmFyIF9zdHIsCiAgICAgICAgX3RoaXMyOSA9IHRoaXM7CiAgICAgIGlmIChzdHIpIHsKICAgICAgICByb3cuVGVjaG5vbG9neV9QYXRoID0gc3RyOwogICAgICB9IGVsc2UgewogICAgICAgIHN0ciA9IHJvdy5UZWNobm9sb2d5X1BhdGg7CiAgICAgIH0KICAgICAgdmFyIGxpc3QgPSAoKF9zdHIgPSBzdHIpID09PSBudWxsIHx8IF9zdHIgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9zdHIuc3BsaXQoJy8nKSkgfHwgW107CiAgICAgIHRoaXMud29ya2luZ1RlYW0uZm9yRWFjaChmdW5jdGlvbiAoZWxlbWVudCwgaWR4KSB7CiAgICAgICAgdmFyIGN1ciA9IGxpc3Quc29tZShmdW5jdGlvbiAoaykgewogICAgICAgICAgcmV0dXJuIGsgPT09IGVsZW1lbnQuUHJvY2Vzc19Db2RlOwogICAgICAgIH0pOwogICAgICAgIHZhciBjb2RlID0gX3RoaXMyOS5nZXRSb3dVbmlxdWUocm93LnV1aWQsIGVsZW1lbnQuUHJvY2Vzc19Db2RlLCBlbGVtZW50LldvcmtpbmdfVGVhbV9JZCk7CiAgICAgICAgdmFyIG1heCA9IF90aGlzMjkuZ2V0Um93VW5pcXVlTWF4KHJvdy51dWlkLCBlbGVtZW50LlByb2Nlc3NfQ29kZSwgZWxlbWVudC5Xb3JraW5nX1RlYW1fSWQpOwogICAgICAgIGlmIChjdXIpIHsKICAgICAgICAgIGlmICghcm93W2NvZGVdKSB7CiAgICAgICAgICAgIF90aGlzMjkuJHNldChyb3csIGNvZGUsIDApOwogICAgICAgICAgICBfdGhpczI5LiRzZXQocm93LCBtYXgsIHJvdy5TY2hkdWxlZF9Db3VudCk7CiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzMjkuJGRlbGV0ZShyb3csIGNvZGUpOwogICAgICAgICAgX3RoaXMyOS4kZGVsZXRlKHJvdywgbWF4KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGNoZWNrUGVybWlzc2lvblRlYW06IGZ1bmN0aW9uIGNoZWNrUGVybWlzc2lvblRlYW0ocHJvY2Vzc1N0ciwgcHJvY2Vzc0NvZGUpIHsKICAgICAgaWYgKCFwcm9jZXNzU3RyKSByZXR1cm4gZmFsc2U7CiAgICAgIHZhciBsaXN0ID0gKHByb2Nlc3NTdHIgPT09IG51bGwgfHwgcHJvY2Vzc1N0ciA9PT0gdm9pZCAwID8gdm9pZCAwIDogcHJvY2Vzc1N0ci5zcGxpdCgnLycpKSB8fCBbXTsKICAgICAgcmV0dXJuICEhbGlzdC5zb21lKGZ1bmN0aW9uICh2KSB7CiAgICAgICAgcmV0dXJuIHYgPT09IHByb2Nlc3NDb2RlOwogICAgICB9KTsKICAgIH0sCiAgICBnZXRUYWJsZUNvbmZpZzogZnVuY3Rpb24gZ2V0VGFibGVDb25maWcoY29kZSkgewogICAgICB2YXIgX3RoaXMzMCA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvcigvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yUnVudGltZSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTE0KCkgewogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlMTQkKF9jb250ZXh0MTQpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0MTQucHJldiA9IF9jb250ZXh0MTQubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX2NvbnRleHQxNC5uZXh0ID0gMjsKICAgICAgICAgICAgICByZXR1cm4gR2V0R3JpZEJ5Q29kZSh7CiAgICAgICAgICAgICAgICBjb2RlOiBjb2RlCiAgICAgICAgICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgICAgICB2YXIgSXNTdWNjZWVkID0gcmVzLklzU3VjY2VlZCwKICAgICAgICAgICAgICAgICAgRGF0YSA9IHJlcy5EYXRhLAogICAgICAgICAgICAgICAgICBNZXNzYWdlID0gcmVzLk1lc3NhZ2U7CiAgICAgICAgICAgICAgICBpZiAoSXNTdWNjZWVkKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzMzAudGJDb25maWcgPSBPYmplY3QuYXNzaWduKHt9LCBfdGhpczMwLnRiQ29uZmlnLCBEYXRhLkdyaWQpOwogICAgICAgICAgICAgICAgICB2YXIgbGlzdCA9IERhdGEuQ29sdW1uTGlzdCB8fCBbXTsKICAgICAgICAgICAgICAgICAgX3RoaXMzMC5vd25lckNvbHVtbiA9IGxpc3QuZmluZChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgICAgICAgIHJldHVybiBpdGVtLkNvZGUgPT09ICdQYXJ0X1VzZWRfUHJvY2Vzcyc7CiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICBfdGhpczMwLm93bmVyQ29sdW1uMiA9IGxpc3QuZmluZChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgICAgICAgIHJldHVybiBpdGVtLkNvZGUgPT09ICdJc19NYWluX1BhcnQnOwogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgX3RoaXMzMC5jb2x1bW5zID0gX3RoaXMzMC5zZXRDb2x1bW5EaXNwbGF5KGxpc3QpOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgX3RoaXMzMC4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogTWVzc2FnZSwKICAgICAgICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0MTQuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUxNCk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIHNldENvbHVtbkRpc3BsYXk6IGZ1bmN0aW9uIHNldENvbHVtbkRpc3BsYXkobGlzdCkgewogICAgICByZXR1cm4gbGlzdC5maWx0ZXIoZnVuY3Rpb24gKHYpIHsKICAgICAgICByZXR1cm4gdi5Jc19EaXNwbGF5OwogICAgICB9KTsKICAgICAgLy8gLm1hcChpdGVtID0+IHsKICAgICAgLy8gICBpZiAoRklYX0NPTFVNTi5pbmNsdWRlcyhpdGVtLkNvZGUpKSB7CiAgICAgIC8vICAgICBpdGVtLmZpeGVkID0gJ2xlZnQnCiAgICAgIC8vICAgfQogICAgICAvLyAgIHJldHVybiBpdGVtCiAgICAgIC8vIH0pCiAgICB9LAogICAgYWN0aXZlQ2VsbE1ldGhvZDogZnVuY3Rpb24gYWN0aXZlQ2VsbE1ldGhvZChfcmVmMikgewogICAgICB2YXIgX2NvbHVtbiRmaWVsZDsKICAgICAgdmFyIHJvdyA9IF9yZWYyLnJvdywKICAgICAgICBjb2x1bW4gPSBfcmVmMi5jb2x1bW4sCiAgICAgICAgY29sdW1uSW5kZXggPSBfcmVmMi5jb2x1bW5JbmRleDsKICAgICAgaWYgKHRoaXMuaXNWaWV3KSByZXR1cm4gZmFsc2U7CiAgICAgIHZhciBwcm9jZXNzQ29kZSA9IChfY29sdW1uJGZpZWxkID0gY29sdW1uLmZpZWxkKSA9PT0gbnVsbCB8fCBfY29sdW1uJGZpZWxkID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfY29sdW1uJGZpZWxkLnNwbGl0KCckXyQnKVsxXTsKICAgICAgcmV0dXJuIHRoaXMuY2hlY2tQZXJtaXNzaW9uVGVhbShyb3cuVGVjaG5vbG9neV9QYXRoLCBwcm9jZXNzQ29kZSk7CiAgICB9LAogICAgb3BlbkJQQURpYWxvZzogZnVuY3Rpb24gb3BlbkJQQURpYWxvZyh0eXBlLCByb3cpIHsKICAgICAgdmFyIF90aGlzMzEgPSB0aGlzOwogICAgICBpZiAodGhpcy53b3Jrc2hvcEVuYWJsZWQpIHsKICAgICAgICBpZiAodHlwZSA9PT0gMSkgewogICAgICAgICAgdmFyIElzVW5pcXVlID0gdGhpcy5jaGVja0lzVW5pcXVlV29ya3Nob3AoKTsKICAgICAgICAgIGlmICghSXNVbmlxdWUpIHJldHVybjsKICAgICAgICB9CiAgICAgIH0KICAgICAgdGhpcy50aXRsZSA9IHR5cGUgPT09IDIgPyAn5bel5bqP6LCD5pW0JyA6ICfmibnph4/lt6Xluo/osIPmlbQnOwogICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSAnQmF0Y2hQcm9jZXNzQWRqdXN0JzsKICAgICAgdGhpcy5kV2lkdGggPSAnNTAlJzsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKF8pIHsKICAgICAgICBfdGhpczMxLiRyZWZzWydjb250ZW50J10uc2V0RGF0YSh0eXBlID09PSAyID8gW3Jvd10gOiBfdGhpczMxLm11bHRpcGxlU2VsZWN0aW9uLCB0eXBlID09PSAyID8gcm93LlRlY2hub2xvZ3lfUGF0aCA6ICcnKTsKICAgICAgfSk7CiAgICB9LAogICAgY2hlY2tJc1VuaXF1ZVdvcmtzaG9wOiBmdW5jdGlvbiBjaGVja0lzVW5pcXVlV29ya3Nob3AoKSB7CiAgICAgIHZhciBpc1VuaXF1ZSA9IHRydWU7CiAgICAgIHZhciBmaXJzdFYgPSB0aGlzLm11bHRpcGxlU2VsZWN0aW9uWzBdLldvcmtzaG9wX05hbWU7CiAgICAgIGZvciAodmFyIGkgPSAxOyBpIDwgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5sZW5ndGg7IGkrKykgewogICAgICAgIHZhciBpdGVtID0gdGhpcy5tdWx0aXBsZVNlbGVjdGlvbltpXTsKICAgICAgICBpZiAoaXRlbS5Xb3Jrc2hvcF9OYW1lICE9PSBmaXJzdFYpIHsKICAgICAgICAgIGlzVW5pcXVlID0gZmFsc2U7CiAgICAgICAgICBicmVhazsKICAgICAgICB9CiAgICAgIH0KICAgICAgaWYgKCFpc1VuaXF1ZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgbWVzc2FnZTogJ+aJuemHj+WIhumFjeW3peW6j+aXtuWPquacieebuOWQjOi9pumXtOS4i+eahOaJjeWPr+S4gOi1t+aJuemHj+WIhumFjScsCiAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICB9KTsKICAgICAgfQogICAgICByZXR1cm4gaXNVbmlxdWU7CiAgICB9LAogICAgY2hlY2tIYXNXb3JrU2hvcDogZnVuY3Rpb24gY2hlY2tIYXNXb3JrU2hvcCh0eXBlLCBhcnIpIHsKICAgICAgdmFyIGhhc1dvcmtTaG9wID0gdHJ1ZTsKICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCBhcnIubGVuZ3RoOyBpKyspIHsKICAgICAgICB2YXIgaXRlbSA9IGFycltpXTsKICAgICAgICBpZiAoIWl0ZW0uV29ya3Nob3BfTmFtZSkgewogICAgICAgICAgaGFzV29ya1Nob3AgPSBmYWxzZTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIH0KICAgICAgfQogICAgICBpZiAoIWhhc1dvcmtTaG9wKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICBtZXNzYWdlOiAn6K+35YWI6YCJ5oup6L2m6Ze05ZCO5YaN6L+b6KGM5bel5bqP5YiG6YWNJywKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgIH0pOwogICAgICB9CiAgICAgIHJldHVybiBoYXNXb3JrU2hvcDsKICAgIH0sCiAgICBoYW5kbGVBZGREaWFsb2c6IGZ1bmN0aW9uIGhhbmRsZUFkZERpYWxvZygpIHsKICAgICAgdmFyIF90aGlzMzIgPSB0aGlzOwogICAgICB2YXIgdHlwZSA9IGFyZ3VtZW50cy5sZW5ndGggPiAwICYmIGFyZ3VtZW50c1swXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzBdIDogJ2FkZCc7CiAgICAgIHRoaXMudGl0bGUgPSAn5re75Yqg6YOo5Lu2JzsKICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ0FkZERyYWZ0JzsKICAgICAgdGhpcy5kV2lkdGggPSAnOTYlJzsKICAgICAgdGhpcy5vcGVuQWRkRHJhZnQgPSB0cnVlOwogICAgICB0aGlzLnNldEFkZFRiS2V5KCk7CiAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uIChfKSB7CiAgICAgICAgX3RoaXMzMi4kcmVmc1snZHJhZnQnXS5pbml0RGF0YSgpOwogICAgICB9KTsKICAgIH0sCiAgICBzZXRBZGRUYktleTogZnVuY3Rpb24gc2V0QWRkVGJLZXkoKSB7CiAgICAgIHZhciBzZWxlY3RLZXlzID0gdGhpcy50YkRhdGEuZmlsdGVyKGZ1bmN0aW9uIChjdXIpIHsKICAgICAgICByZXR1cm4gY3VyLnB1dWlkOwogICAgICB9KS5tYXAoZnVuY3Rpb24gKHYpIHsKICAgICAgICByZXR1cm4gdi5wdXVpZDsKICAgICAgfSk7CiAgICAgIHRoaXMuY2hhbmdlQWRkVGJLZXlzKHNlbGVjdEtleXMpOwogICAgfSwKICAgIGdldFJvd1VuaXF1ZTogZnVuY3Rpb24gZ2V0Um93VW5pcXVlKHV1aWQsIHByb2Nlc3NDb2RlLCB3b3JraW5nSWQpIHsKICAgICAgcmV0dXJuICIiLmNvbmNhdCh1dWlkKS5jb25jYXQoU1BMSVRfU1lNQk9MKS5jb25jYXQocHJvY2Vzc0NvZGUpLmNvbmNhdChTUExJVF9TWU1CT0wpLmNvbmNhdCh3b3JraW5nSWQpOwogICAgfSwKICAgIGdldFJvd1VuaXF1ZU1heDogZnVuY3Rpb24gZ2V0Um93VW5pcXVlTWF4KHV1aWQsIHByb2Nlc3NDb2RlLCB3b3JraW5nSWQpIHsKICAgICAgcmV0dXJuIHRoaXMuZ2V0Um93VW5pcXVlKHV1aWQsIHByb2Nlc3NDb2RlLCB3b3JraW5nSWQpICsgIiIuY29uY2F0KFNQTElUX1NZTUJPTCwgIm1heCIpOwogICAgfSwKICAgIGhhbmRsZVNlbGVjdE1lbnU6IGZ1bmN0aW9uIGhhbmRsZVNlbGVjdE1lbnUodikgewogICAgICBpZiAodiA9PT0gJ3Byb2Nlc3MnKSB7CiAgICAgICAgdGhpcy5vcGVuQlBBRGlhbG9nKDEpOwogICAgICB9IGVsc2UgaWYgKHYgPT09ICdjcmFmdCcpIHsKICAgICAgICB0aGlzLmhhbmRsZVNldENyYWZ0UHJvY2VzcygpOwogICAgICB9CiAgICB9LAogICAgbWVyZ2VDcmFmdFByb2Nlc3M6IGZ1bmN0aW9uIG1lcmdlQ3JhZnRQcm9jZXNzKGxpc3QpIHsKICAgICAgdmFyIF90aGlzMzMgPSB0aGlzOwogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUxNSgpIHsKICAgICAgICB2YXIgY29kZXMsIF9sb29wMywga2V5LCBfY3JhZnRDb2RlTWFwOwogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlMTUkKF9jb250ZXh0MTYpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0MTYucHJldiA9IF9jb250ZXh0MTYubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgY29kZXMgPSBfdG9Db25zdW1hYmxlQXJyYXkobmV3IFNldChsaXN0Lm1hcChmdW5jdGlvbiAodikgewogICAgICAgICAgICAgICAgcmV0dXJuIHYuVGVjaG5vbG9neV9Db2RlOwogICAgICAgICAgICAgIH0pKSk7CiAgICAgICAgICAgICAgX2xvb3AzID0gLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9sb29wMyhrZXkpIHsKICAgICAgICAgICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfbG9vcDMkKF9jb250ZXh0MTUpIHsKICAgICAgICAgICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQxNS5wcmV2ID0gX2NvbnRleHQxNS5uZXh0KSB7CiAgICAgICAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgICAgICAgaWYgKF90aGlzMzMuY3JhZnRDb2RlTWFwLmhhc093blByb3BlcnR5KGtleSkpIHsKICAgICAgICAgICAgICAgICAgICAgICAgY29kZXMgPSBjb2Rlcy5maWx0ZXIoZnVuY3Rpb24gKGNvZGUpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gY29kZSAhPT0ga2V5OwogICAgICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICBjYXNlIDE6CiAgICAgICAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDE1LnN0b3AoKTsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfSwgX2xvb3AzKTsKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICBfY29udGV4dDE2LnQwID0gX3JlZ2VuZXJhdG9yUnVudGltZSgpLmtleXMoX3RoaXMzMy5jcmFmdENvZGVNYXApOwogICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgaWYgKChfY29udGV4dDE2LnQxID0gX2NvbnRleHQxNi50MCgpKS5kb25lKSB7CiAgICAgICAgICAgICAgICBfY29udGV4dDE2Lm5leHQgPSA4OwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGtleSA9IF9jb250ZXh0MTYudDEudmFsdWU7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0MTYuZGVsZWdhdGVZaWVsZChfbG9vcDMoa2V5KSwgInQyIiwgNik7CiAgICAgICAgICAgIGNhc2UgNjoKICAgICAgICAgICAgICBfY29udGV4dDE2Lm5leHQgPSAzOwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDg6CiAgICAgICAgICAgICAgX2NvbnRleHQxNi5uZXh0ID0gMTA7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzMzMuZ2V0Q3JhZnRQcm9jZXNzKGNvZGVzKTsKICAgICAgICAgICAgY2FzZSAxMDoKICAgICAgICAgICAgICBfY3JhZnRDb2RlTWFwID0gX2NvbnRleHQxNi5zZW50OwogICAgICAgICAgICAgIE9iamVjdC5hc3NpZ24oX3RoaXMzMy5jcmFmdENvZGVNYXAsIF9jcmFmdENvZGVNYXApOwogICAgICAgICAgICBjYXNlIDEyOgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDE2LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMTUpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBoYW5kbGVTZXRDcmFmdFByb2Nlc3M6IGZ1bmN0aW9uIGhhbmRsZVNldENyYWZ0UHJvY2VzcygpIHsKICAgICAgdmFyIF90aGlzMzQgPSB0aGlzOwogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUxNigpIHsKICAgICAgICB2YXIgc2hvd1N1Y2Nlc3MsIHJvd0xpc3QsIHdvcmtzaG9wSWRzLCB3X3Byb2Nlc3MsIHdvcmtzaG9wUHJvbWlzZTsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTE2JChfY29udGV4dDE3KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDE3LnByZXYgPSBfY29udGV4dDE3Lm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIHNob3dTdWNjZXNzID0gZnVuY3Rpb24gc2hvd1N1Y2Nlc3MoKSB7CiAgICAgICAgICAgICAgICBfdGhpczM0LiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgICAgbWVzc2FnZTogJ+W3suWIhumFjeaIkOWKnycsCiAgICAgICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJwogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICByb3dMaXN0ID0gX3RoaXMzNC5tdWx0aXBsZVNlbGVjdGlvbi5tYXAoZnVuY3Rpb24gKHYpIHsKICAgICAgICAgICAgICAgIHJldHVybiB2LlRlY2hub2xvZ3lfQ29kZTsKICAgICAgICAgICAgICB9KS5maWx0ZXIoZnVuY3Rpb24gKHYpIHsKICAgICAgICAgICAgICAgIHJldHVybiAhIXY7CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgaWYgKHJvd0xpc3QubGVuZ3RoKSB7CiAgICAgICAgICAgICAgICBfY29udGV4dDE3Lm5leHQgPSA1OwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzMzQuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgbWVzc2FnZTogJ+W3peiJuuS7o+eggeS4jeWtmOWcqCcsCiAgICAgICAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQxNy5hYnJ1cHQoInJldHVybiIpOwogICAgICAgICAgICBjYXNlIDU6CiAgICAgICAgICAgICAgX2NvbnRleHQxNy5uZXh0ID0gNzsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXMzNC5tZXJnZUNyYWZ0UHJvY2VzcyhfdGhpczM0Lm11bHRpcGxlU2VsZWN0aW9uKTsKICAgICAgICAgICAgY2FzZSA3OgogICAgICAgICAgICAgIHdvcmtzaG9wSWRzID0gQXJyYXkuZnJvbShuZXcgU2V0KF90aGlzMzQubXVsdGlwbGVTZWxlY3Rpb24ubWFwKGZ1bmN0aW9uICh2KSB7CiAgICAgICAgICAgICAgICByZXR1cm4gdi5Xb3Jrc2hvcF9JZDsKICAgICAgICAgICAgICB9KS5maWx0ZXIoZnVuY3Rpb24gKHYpIHsKICAgICAgICAgICAgICAgIHJldHVybiAhIXY7CiAgICAgICAgICAgICAgfSkpKTsKICAgICAgICAgICAgICB3X3Byb2Nlc3MgPSBbXTsKICAgICAgICAgICAgICBpZiAod29ya3Nob3BJZHMubGVuZ3RoKSB7CiAgICAgICAgICAgICAgICB3b3Jrc2hvcElkcy5mb3JFYWNoKGZ1bmN0aW9uICh3b3Jrc2hvcElkKSB7CiAgICAgICAgICAgICAgICAgIHdfcHJvY2Vzcy5wdXNoKF90aGlzMzQuZ2V0UHJvY2Vzc09wdGlvbih3b3Jrc2hvcElkKS50aGVuKGZ1bmN0aW9uIChyZXN1bHQpIHsKICAgICAgICAgICAgICAgICAgICByZXR1cm4gX2RlZmluZVByb3BlcnR5KHt9LCB3b3Jrc2hvcElkLCByZXN1bHQpOwogICAgICAgICAgICAgICAgICB9KSk7CiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIHdvcmtzaG9wUHJvbWlzZSA9IFByb21pc2UuYWxsKHdfcHJvY2VzcykudGhlbihmdW5jdGlvbiAodmFsdWVzKSB7CiAgICAgICAgICAgICAgICAgIHJldHVybiBPYmplY3QuYXNzaWduLmFwcGx5KE9iamVjdCwgW3t9XS5jb25jYXQoX3RvQ29uc3VtYWJsZUFycmF5KHZhbHVlcykpKTsKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgd29ya3Nob3BQcm9taXNlLnRoZW4oZnVuY3Rpb24gKHdvcmtzaG9wKSB7CiAgICAgICAgICAgICAgICAgIHZhciBmbGFnID0gdHJ1ZTsKICAgICAgICAgICAgICAgICAgdmFyIF9sb29wNCA9IGZ1bmN0aW9uIF9sb29wNCgpIHsKICAgICAgICAgICAgICAgICAgICB2YXIgY3VyUm93ID0gX3RoaXMzNC5tdWx0aXBsZVNlbGVjdGlvbltpXTsKICAgICAgICAgICAgICAgICAgICB2YXIgd29ya3Nob3BQcm9jZXNzID0gd29ya3Nob3BbY3VyUm93LldvcmtzaG9wX0lkXTsKICAgICAgICAgICAgICAgICAgICB2YXIgY3JhZnRBcnJheSA9IF90aGlzMzQuY3JhZnRDb2RlTWFwW2N1clJvdy5UZWNobm9sb2d5X0NvZGVdOwogICAgICAgICAgICAgICAgICAgIGlmIChjcmFmdEFycmF5KSB7CiAgICAgICAgICAgICAgICAgICAgICB2YXIgaXNJbmNsdWRlZCA9IGNyYWZ0QXJyYXkuZXZlcnkoZnVuY3Rpb24gKHByb2Nlc3MpIHsKICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHdvcmtzaG9wUHJvY2Vzcy5pbmNsdWRlcyhwcm9jZXNzKTsKICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgICAgaWYgKCFpc0luY2x1ZGVkKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIGZsYWcgPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIDE7IC8vIGNvbnRpbnVlCiAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICBjdXJSb3cuVGVjaG5vbG9neV9QYXRoID0gY3JhZnRBcnJheS5qb2luKCcvJyk7CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgICAgICBmb3IgKHZhciBpID0gMDsgaSA8IF90aGlzMzQubXVsdGlwbGVTZWxlY3Rpb24ubGVuZ3RoOyBpKyspIHsKICAgICAgICAgICAgICAgICAgICBpZiAoX2xvb3A0KCkpIGNvbnRpbnVlOwogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIGlmICghZmxhZykgewogICAgICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgICAgICAgICAgX3RoaXMzNC4kYWxlcnQoJ+aJgOmAiei9pumXtOS4i+ePree7hOWKoOW3peW3peW6j+S4jeWMheWQq+W3peiJuuS7o+eggeW3peW6j+ivt+aJi+WKqOaOkuS6pycsICfmj5DnpLonLCB7CiAgICAgICAgICAgICAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJwogICAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgICAgfSwgMjAwKTsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICBmbGFnICYmIHNob3dTdWNjZXNzKCk7CiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgX3RoaXMzNC5tdWx0aXBsZVNlbGVjdGlvbi5mb3JFYWNoKGZ1bmN0aW9uIChjdXJSb3cpIHsKICAgICAgICAgICAgICAgICAgdmFyIGNyYWZ0QXJyYXkgPSBfdGhpczM0LmNyYWZ0Q29kZU1hcFtjdXJSb3cuVGVjaG5vbG9neV9Db2RlXTsKICAgICAgICAgICAgICAgICAgaWYgKGNyYWZ0QXJyYXkpIHsKICAgICAgICAgICAgICAgICAgICBjdXJSb3cuVGVjaG5vbG9neV9QYXRoID0gY3JhZnRBcnJheS5qb2luKCcvJyk7CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgc2hvd1N1Y2Nlc3MoKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIGNhc2UgMTA6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0MTcuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUxNik7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIGhhbmRsZUJhdGNoT3duZXI6IGZ1bmN0aW9uIGhhbmRsZUJhdGNoT3duZXIodHlwZSwgcm93KSB7CiAgICAgIHZhciBfdGhpczM1ID0gdGhpczsKICAgICAgdGhpcy50aXRsZSA9ICfmibnph4/liIbphY3poobnlKjlt6Xluo8nOwogICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSAnT3duZXJQcm9jZXNzJzsKICAgICAgdGhpcy5kV2lkdGggPSAnMzAlJzsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKF8pIHsKICAgICAgICBfdGhpczM1LiRyZWZzWydjb250ZW50J10uc2V0T3B0aW9uKHR5cGUgPT09IDIsIHR5cGUgPT09IDIgPyBbcm93XSA6IF90aGlzMzUubXVsdGlwbGVTZWxlY3Rpb24pOwogICAgICB9KTsKICAgIH0sCiAgICBnZXRDcmFmdFByb2Nlc3M6IGZ1bmN0aW9uIGdldENyYWZ0UHJvY2VzcygpIHsKICAgICAgdmFyIF90aGlzMzYgPSB0aGlzOwogICAgICB2YXIgZ3lHcm91cCA9IGFyZ3VtZW50cy5sZW5ndGggPiAwICYmIGFyZ3VtZW50c1swXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzBdIDogW107CiAgICAgIGd5R3JvdXAgPSBneUdyb3VwLmZpbHRlcihmdW5jdGlvbiAodikgewogICAgICAgIHJldHVybiAhIXY7CiAgICAgIH0pOwogICAgICBpZiAoIWd5R3JvdXAubGVuZ3RoKSByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKHt9KTsKICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uIChyZXNvbHZlLCByZWplY3QpIHsKICAgICAgICBHZXRQcm9jZXNzRmxvd0xpc3RXaXRoVGVjaG5vbG9neSh7CiAgICAgICAgICBUZWNobm9sb2d5Q29kZXM6IGd5R3JvdXAsCiAgICAgICAgICBUeXBlOiAzCiAgICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICB2YXIgZ3lMaXN0ID0gcmVzLkRhdGEgfHwgW107CiAgICAgICAgICAgIHZhciBneU1hcCA9IGd5TGlzdC5yZWR1Y2UoZnVuY3Rpb24gKGFjYywgaXRlbSkgewogICAgICAgICAgICAgIGFjY1tpdGVtLkNvZGVdID0gaXRlbS5UZWNobm9sb2d5X1BhdGg7CiAgICAgICAgICAgICAgcmV0dXJuIGFjYzsKICAgICAgICAgICAgfSwge30pOwogICAgICAgICAgICBjb25zb2xlLmxvZygnZ3lNYXAnLCBneU1hcCk7CiAgICAgICAgICAgIHJlc29sdmUoZ3lNYXApOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgX3RoaXMzNi4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgcmVqZWN0KCk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZVJldmVyc2U6IGZ1bmN0aW9uIGhhbmRsZVJldmVyc2UoKSB7CiAgICAgIHZhciBjdXIgPSBbXTsKICAgICAgdGhpcy50YkRhdGEuZm9yRWFjaChmdW5jdGlvbiAoZWxlbWVudCwgaWR4KSB7CiAgICAgICAgZWxlbWVudC5jaGVja2VkID0gIWVsZW1lbnQuY2hlY2tlZDsKICAgICAgICBpZiAoZWxlbWVudC5jaGVja2VkKSB7CiAgICAgICAgICBjdXIucHVzaChlbGVtZW50KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gY3VyOwogICAgICBpZiAodGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5sZW5ndGggPT09IHRoaXMudGJEYXRhLmxlbmd0aCkgewogICAgICAgIHRoaXMuJHJlZnNbJ3hUYWJsZSddLnNldEFsbENoZWNrYm94Um93KHRydWUpOwogICAgICB9CiAgICAgIGlmICh0aGlzLm11bHRpcGxlU2VsZWN0aW9uLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJHJlZnNbJ3hUYWJsZSddLnNldEFsbENoZWNrYm94Um93KGZhbHNlKTsKICAgICAgfQogICAgfSwKICAgIC8vIHRiRmlsdGVyQ2hhbmdlKCkgewogICAgLy8gICBjb25zdCB4VGFibGUgPSB0aGlzLiRyZWZzLnhUYWJsZQogICAgLy8gICBjb25zdCBjb2x1bW4gPSB4VGFibGUuZ2V0Q29sdW1uQnlGaWVsZCgnVHlwZV9OYW1lJykKICAgIC8vICAgaWYgKCFjb2x1bW4/LmZpbHRlcnM/Lmxlbmd0aCkgcmV0dXJuCiAgICAvLyAgIGNvbHVtbi5maWx0ZXJzLmZvckVhY2goZCA9PiB7CiAgICAvLyAgICAgZC5jaGVja2VkID0gZC52YWx1ZSA9PT0gdGhpcy5zZWFyY2hUeXBlCiAgICAvLyAgIH0pCiAgICAvLyAgIHhUYWJsZS51cGRhdGVEYXRhKCkKICAgIC8vIH0sCiAgICBnZXRUeXBlOiBmdW5jdGlvbiBnZXRUeXBlKCkgewogICAgICB2YXIgX3RoaXMzNyA9IHRoaXM7CiAgICAgIHZhciBnZXRDb21wVHJlZSA9IGZ1bmN0aW9uIGdldENvbXBUcmVlKCkgewogICAgICAgIHZhciBmdW4gPSBfdGhpczM3LmlzQ29tID8gR2V0Q29tcFR5cGVUcmVlIDogR2V0UGFydFR5cGVMaXN0OwogICAgICAgIGZ1bih7fSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICB2YXIgcmVzdWx0ID0gcmVzLkRhdGE7CiAgICAgICAgICAgIGlmICghX3RoaXMzNy5pc0NvbSkgewogICAgICAgICAgICAgIHJlc3VsdCA9IHJlc3VsdC5tYXAoZnVuY3Rpb24gKHYsIGlkeCkgewogICAgICAgICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgICAgICAgRGF0YTogdi5OYW1lLAogICAgICAgICAgICAgICAgICBMYWJlbDogdi5OYW1lCiAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICAgIF90aGlzMzcudHJlZVBhcmFtc0NvbXBvbmVudFR5cGUuZGF0YSA9IHJlc3VsdDsKICAgICAgICAgICAgX3RoaXMzNy4kbmV4dFRpY2soZnVuY3Rpb24gKF8pIHsKICAgICAgICAgICAgICB2YXIgX3RoaXMzNyQkcmVmcyR0cmVlU2VsOwogICAgICAgICAgICAgIChfdGhpczM3JCRyZWZzJHRyZWVTZWwgPSBfdGhpczM3LiRyZWZzLnRyZWVTZWxlY3RDb21wb25lbnRUeXBlKSA9PT0gbnVsbCB8fCBfdGhpczM3JCRyZWZzJHRyZWVTZWwgPT09IHZvaWQgMCB8fCBfdGhpczM3JCRyZWZzJHRyZWVTZWwudHJlZURhdGFVcGRhdGVGdW4ocmVzdWx0KTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBfdGhpczM3LiRtZXNzYWdlKHsKICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9OwogICAgICBnZXRDb21wVHJlZSgpOwogICAgfSwKICAgIC8vIOafpeeci+Wbvue6uAogICAgaGFuZGxlRHdnOiBmdW5jdGlvbiBoYW5kbGVEd2cocm93KSB7CiAgICAgIHZhciBfdGhpczM4ID0gdGhpczsKICAgICAgY29uc29sZS5sb2coJ3JvdycsIHJvdyk7CiAgICAgIHZhciBvYmogPSB7fTsKICAgICAgaWYgKHRoaXMuaXNDb20pIHsKICAgICAgICBvYmouQ29tcF9JZCA9IHJvdy5Db21wX0ltcG9ydF9EZXRhaWxfSWQ7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgb2JqLlBhcnRfSWQgPSByb3cuUGFydF9BZ2dyZWdhdGVfSWQ7CiAgICAgIH0KICAgICAgR2V0U3RlZWxDYWRBbmRCaW1JZCh7CiAgICAgICAgaW1wb3J0RGV0YWlsSWQ6IG9iai5QYXJ0X0lkCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICBfdGhpczM4LmV4dGVuc2lvbk5hbWUgPSByZXMuRGF0YVswXS5FeHRlbnNpb25OYW1lOwogICAgICAgICAgX3RoaXMzOC5maWxlQmltID0gcmVzLkRhdGFbMF0uZmlsZUJpbTsKICAgICAgICAgIF90aGlzMzguSXNVcGxvYWRDYWQgPSByZXMuRGF0YVswXS5Jc1VwbG9hZDsKICAgICAgICAgIF90aGlzMzguY2FkUm93Q29kZSA9IHJvdy5QYXJ0X0NvZGU7CiAgICAgICAgICBfdGhpczM4LmNhZFJvd1Byb2plY3RJZCA9IHJvdy5TeXNfUHJvamVjdF9JZDsKICAgICAgICAgIF90aGlzMzguZmlsZVZpZXcoKTsKICAgICAgICB9CiAgICAgIH0pOwoKICAgICAgLy8gR2V0RHdnKG9iaikudGhlbihyZXMgPT4gewogICAgICAvLyAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgIC8vICAgICBjb25zdCBmaWxldXJsID0gcmVzPy5EYXRhPy5sZW5ndGggJiYgcmVzLkRhdGFbMF0uRmlsZV9VcmwKICAgICAgLy8gICAgIHdpbmRvdy5vcGVuKCdodHRwOi8vZHdndjEuYmltdGsuY29tOjU0MzIvP0NhZFVybD0nICsgcGFyc2VPc3NVcmwoZmlsZXVybCksICdfYmxhbmsnKQogICAgICAvLyAgIH0gZWxzZSB7CiAgICAgIC8vICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgLy8gICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgIC8vICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgLy8gICAgIH0pCiAgICAgIC8vICAgfQogICAgICAvLyB9KQogICAgfSwKICAgIHNldFByb2Nlc3NMaXN0OiBmdW5jdGlvbiBzZXRQcm9jZXNzTGlzdChpbmZvKSB7CiAgICAgIHRoaXMuY2hhbmdlUHJvY2Vzc0xpc3QoaW5mbyk7CiAgICB9LAogICAgcmVzZXRJbm5lckZvcm06IGZ1bmN0aW9uIHJlc2V0SW5uZXJGb3JtKCkgewogICAgICB0aGlzLiRyZWZzWydzZWFyY2hGb3JtJ10ucmVzZXRGaWVsZHMoKTsKICAgICAgdGhpcy4kcmVmcy54VGFibGUuY2xlYXJGaWx0ZXIoKTsKICAgIH0sCiAgICBpbm5lckZpbHRlcjogZnVuY3Rpb24gaW5uZXJGaWx0ZXIoKSB7CiAgICAgIHZhciBfdGhpczM5ID0gdGhpczsKICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbiA9IFtdOwogICAgICB2YXIgYXJyID0gW107CiAgICAgIGlmICh0aGlzLmlzQ29tKSB7CiAgICAgICAgYXJyLnB1c2goJ1R5cGUnLCAnQ29tcF9Db2RlJywgJ1NwZWMnLCAnSXNfQ29tcG9uZW50Jyk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgYXJyLnB1c2goJ1BhcnRfQ29kZScsICdTcGVjJywgJ1R5cGVfTmFtZScsICdQcm9qZWN0X05hbWUnLCAnQXJlYV9OYW1lJywgJ0luc3RhbGxVbml0X05hbWUnKTsKICAgICAgfQogICAgICB2YXIgeFRhYmxlID0gdGhpcy4kcmVmcy54VGFibGU7CiAgICAgIHhUYWJsZS5jbGVhckNoZWNrYm94Um93KCk7CiAgICAgIGFyci5mb3JFYWNoKGZ1bmN0aW9uIChlbGVtZW50LCBpZHgpIHsKICAgICAgICB2YXIgY29sdW1uID0geFRhYmxlLmdldENvbHVtbkJ5RmllbGQoZWxlbWVudCk7CiAgICAgICAgaWYgKGVsZW1lbnQgPT09ICdJc19Db21wb25lbnQnKSB7CiAgICAgICAgICBjb2x1bW4uZmlsdGVycy5mb3JFYWNoKGZ1bmN0aW9uIChvcHRpb24sIGlkeCkgewogICAgICAgICAgICBvcHRpb24uY2hlY2tlZCA9IGlkeCA9PT0gKF90aGlzMzkuaW5uZXJGb3JtLnNlYXJjaERpcmVjdCA/IDAgOiAxKTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgICBpZiAoZWxlbWVudCA9PT0gJ1NwZWMnKSB7CiAgICAgICAgICB2YXIgb3B0aW9uID0gY29sdW1uLmZpbHRlcnNbMF07CiAgICAgICAgICBvcHRpb24uZGF0YSA9IF90aGlzMzkuaW5uZXJGb3JtLnNlYXJjaFNwZWNTZWFyY2g7CiAgICAgICAgICBvcHRpb24uY2hlY2tlZCA9IHRydWU7CiAgICAgICAgfQogICAgICAgIGlmIChlbGVtZW50ID09PSAnQ29tcF9Db2RlJyB8fCBlbGVtZW50ID09PSAnUGFydF9Db2RlJykgewogICAgICAgICAgdmFyIF9vcHRpb24gPSBjb2x1bW4uZmlsdGVyc1swXTsKICAgICAgICAgIF9vcHRpb24uZGF0YSA9IF90aGlzMzkuaW5uZXJGb3JtLnNlYXJjaENvbnRlbnQ7CiAgICAgICAgICBfb3B0aW9uLmNoZWNrZWQgPSB0cnVlOwogICAgICAgIH0KICAgICAgICBpZiAoZWxlbWVudCA9PT0gJ1Byb2plY3RfTmFtZScpIHsKICAgICAgICAgIHZhciBfb3B0aW9uMiA9IGNvbHVtbi5maWx0ZXJzWzBdOwogICAgICAgICAgX29wdGlvbjIuZGF0YSA9IF90aGlzMzkuaW5uZXJGb3JtLnByb2plY3ROYW1lOwogICAgICAgICAgX29wdGlvbjIuY2hlY2tlZCA9IHRydWU7CiAgICAgICAgfQogICAgICAgIGlmIChlbGVtZW50ID09PSAnQXJlYV9OYW1lJykgewogICAgICAgICAgdmFyIF9vcHRpb24zID0gY29sdW1uLmZpbHRlcnNbMF07CiAgICAgICAgICBfb3B0aW9uMy5kYXRhID0gX3RoaXMzOS5pbm5lckZvcm0uYXJlYU5hbWU7CiAgICAgICAgICBfb3B0aW9uMy5jaGVja2VkID0gdHJ1ZTsKICAgICAgICB9CiAgICAgICAgaWYgKGVsZW1lbnQgPT09ICdJbnN0YWxsVW5pdF9OYW1lJykgewogICAgICAgICAgdmFyIF9vcHRpb240ID0gY29sdW1uLmZpbHRlcnNbMF07CiAgICAgICAgICBfb3B0aW9uNC5kYXRhID0gX3RoaXMzOS5pbm5lckZvcm0uaW5zdGFsbE5hbWU7CiAgICAgICAgICBfb3B0aW9uNC5jaGVja2VkID0gdHJ1ZTsKICAgICAgICB9CiAgICAgIH0pOwogICAgICB4VGFibGUudXBkYXRlRGF0YSgpOwogICAgfSwKICAgIGZpbHRlckNvbXBvbmVudE1ldGhvZDogZnVuY3Rpb24gZmlsdGVyQ29tcG9uZW50TWV0aG9kKF9yZWY0KSB7CiAgICAgIHZhciBvcHRpb24gPSBfcmVmNC5vcHRpb24sCiAgICAgICAgcm93ID0gX3JlZjQucm93OwogICAgICBpZiAodGhpcy5pbm5lckZvcm0uc2VhcmNoRGlyZWN0ID09PSAnJykgewogICAgICAgIHJldHVybiB0cnVlOwogICAgICB9CiAgICAgIHJldHVybiByb3cuSXNfQ29tcG9uZW50ID09PSAhdGhpcy5pbm5lckZvcm0uc2VhcmNoRGlyZWN0OwogICAgfSwKICAgIGZpbHRlclNwZWNNZXRob2Q6IGZ1bmN0aW9uIGZpbHRlclNwZWNNZXRob2QoX3JlZjUpIHsKICAgICAgdmFyIG9wdGlvbiA9IF9yZWY1Lm9wdGlvbiwKICAgICAgICByb3cgPSBfcmVmNS5yb3c7CiAgICAgIGlmICh0aGlzLmlubmVyRm9ybS5zZWFyY2hTcGVjU2VhcmNoLnRyaW0oKSA9PT0gJycpIHsKICAgICAgICByZXR1cm4gdHJ1ZTsKICAgICAgfQogICAgICB2YXIgc3BsaXRBbmRDbGVhbiA9IGZ1bmN0aW9uIHNwbGl0QW5kQ2xlYW4oaW5wdXQpIHsKICAgICAgICByZXR1cm4gaW5wdXQudHJpbSgpLnJlcGxhY2UoL1xzKy9nLCAnICcpLnNwbGl0KCcgJyk7CiAgICAgIH07CiAgICAgIHZhciBzcGVjQXJyYXkgPSBzcGxpdEFuZENsZWFuKHRoaXMuaW5uZXJGb3JtLnNlYXJjaFNwZWNTZWFyY2gpOwogICAgICByZXR1cm4gc3BlY0FycmF5LnNvbWUoZnVuY3Rpb24gKGNvZGUpIHsKICAgICAgICByZXR1cm4gKHJvdy5TcGVjIHx8ICcnKS5pbmNsdWRlcyhjb2RlKTsKICAgICAgfSk7CiAgICB9LAogICAgZmlsdGVyQ29kZU1ldGhvZDogZnVuY3Rpb24gZmlsdGVyQ29kZU1ldGhvZChfcmVmNikgewogICAgICB2YXIgb3B0aW9uID0gX3JlZjYub3B0aW9uLAogICAgICAgIHJvdyA9IF9yZWY2LnJvdzsKICAgICAgaWYgKHRoaXMuaW5uZXJGb3JtLnNlYXJjaENvbnRlbnQudHJpbSgpID09PSAnJykgewogICAgICAgIHJldHVybiB0cnVlOwogICAgICB9CiAgICAgIHZhciBzcGxpdEFuZENsZWFuID0gZnVuY3Rpb24gc3BsaXRBbmRDbGVhbihpbnB1dCkgewogICAgICAgIHJldHVybiBpbnB1dC50cmltKCkucmVwbGFjZSgvXHMrL2csICcgJykuc3BsaXQoJyAnKTsKICAgICAgfTsKICAgICAgdmFyIGN1ciA9IHRoaXMuaXNDb20gPyAnQ29tcF9Db2RlJyA6ICdQYXJ0X0NvZGUnOwogICAgICB2YXIgYXJyID0gc3BsaXRBbmRDbGVhbih0aGlzLmlubmVyRm9ybS5zZWFyY2hDb250ZW50KTsKICAgICAgaWYgKHRoaXMuY3VyU2VhcmNoID09PSAxKSB7CiAgICAgICAgcmV0dXJuIGFyci5zb21lKGZ1bmN0aW9uIChjb2RlKSB7CiAgICAgICAgICByZXR1cm4gcm93W2N1cl0gPT09IGNvZGU7CiAgICAgICAgfSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCBhcnIubGVuZ3RoOyBpKyspIHsKICAgICAgICAgIHZhciBpdGVtID0gYXJyW2ldOwogICAgICAgICAgaWYgKHJvd1tjdXJdLmluY2x1ZGVzKGl0ZW0pKSB7CiAgICAgICAgICAgIHJldHVybiB0cnVlOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgIH0sCiAgICBmaWx0ZXJQcm9qZWN0TWV0aG9kOiBmdW5jdGlvbiBmaWx0ZXJQcm9qZWN0TWV0aG9kKF9yZWY3KSB7CiAgICAgIHZhciBvcHRpb24gPSBfcmVmNy5vcHRpb24sCiAgICAgICAgcm93ID0gX3JlZjcucm93OwogICAgICBpZiAob3B0aW9uLmRhdGEgPT09ICcnKSB7CiAgICAgICAgcmV0dXJuIHRydWU7CiAgICAgIH0KICAgICAgcmV0dXJuIHJvdy5Qcm9qZWN0X05hbWUgPT09IG9wdGlvbi5kYXRhOwogICAgfSwKICAgIGZpbHRlckFyZWFNZXRob2Q6IGZ1bmN0aW9uIGZpbHRlckFyZWFNZXRob2QoX3JlZjgpIHsKICAgICAgdmFyIG9wdGlvbiA9IF9yZWY4Lm9wdGlvbiwKICAgICAgICByb3cgPSBfcmVmOC5yb3c7CiAgICAgIGlmIChvcHRpb24uZGF0YSA9PT0gJycpIHsKICAgICAgICByZXR1cm4gdHJ1ZTsKICAgICAgfQogICAgICByZXR1cm4gcm93LkFyZWFfTmFtZSA9PT0gb3B0aW9uLmRhdGE7CiAgICB9LAogICAgZmlsdGVySW5zdGFsbE1ldGhvZDogZnVuY3Rpb24gZmlsdGVySW5zdGFsbE1ldGhvZChfcmVmOSkgewogICAgICB2YXIgb3B0aW9uID0gX3JlZjkub3B0aW9uLAogICAgICAgIHJvdyA9IF9yZWY5LnJvdzsKICAgICAgaWYgKG9wdGlvbi5kYXRhID09PSAnJykgewogICAgICAgIHJldHVybiB0cnVlOwogICAgICB9CiAgICAgIHJldHVybiByb3cuSW5zdGFsbFVuaXRfTmFtZSA9PT0gb3B0aW9uLmRhdGE7CiAgICB9LAogICAgY29tcG9uZW50VHlwZUZpbHRlcjogZnVuY3Rpb24gY29tcG9uZW50VHlwZUZpbHRlcihlKSB7CiAgICAgIHZhciBfdGhpcyQkcmVmczsKICAgICAgKF90aGlzJCRyZWZzID0gdGhpcy4kcmVmcykgPT09IG51bGwgfHwgX3RoaXMkJHJlZnMgPT09IHZvaWQgMCB8fCBfdGhpcyQkcmVmcy50cmVlU2VsZWN0Q29tcG9uZW50VHlwZS5maWx0ZXJGdW4oZSk7CiAgICB9LAogICAgZ2V0SW5zdGFsbFVuaXRJZE5hbWVMaXN0OiBmdW5jdGlvbiBnZXRJbnN0YWxsVW5pdElkTmFtZUxpc3QoaWQpIHsKICAgICAgdmFyIF90aGlzNDAgPSB0aGlzOwogICAgICBpZiAoIXRoaXMuYXJlYUlkKSB7CiAgICAgICAgdGhpcy5pbnN0YWxsVW5pdElkTGlzdCA9IFtdOwogICAgICAgIHRoaXMuZGlzYWJsZWRBZGQgPSBmYWxzZTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmRpc2FibGVkQWRkID0gdHJ1ZTsKICAgICAgICBHZXRJbnN0YWxsVW5pdElkTmFtZUxpc3QoewogICAgICAgICAgQXJlYV9JZDogdGhpcy5hcmVhSWQKICAgICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgIF90aGlzNDAuaW5zdGFsbFVuaXRJZExpc3QgPSByZXMuRGF0YTsKICAgICAgICAgIGlmIChfdGhpczQwLmluc3RhbGxVbml0SWRMaXN0Lmxlbmd0aCkgewogICAgICAgICAgICBfdGhpczQwLmZvcm1JbmxpbmUuSW5zdGFsbFVuaXRfSWQgPSBfdGhpczQwLmluc3RhbGxVbml0SWRMaXN0WzBdLklkOwogICAgICAgICAgfQogICAgICAgICAgX3RoaXM0MC5kaXNhYmxlZEFkZCA9IGZhbHNlOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgaW5zdGFsbENoYW5nZTogZnVuY3Rpb24gaW5zdGFsbENoYW5nZSgpIHsKICAgICAgdmFyIF90aGlzNDEgPSB0aGlzOwogICAgICBpZiAoIXRoaXMudGJEYXRhLmxlbmd0aCkgewogICAgICAgIHRoaXMuJHJlZnNbJ3NlYXJjaEZvcm0nXS5yZXNldEZpZWxkcygpOwogICAgICAgIHRoaXMuJHJlZnMueFRhYmxlLmNsZWFyRmlsdGVyKCk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+WIh+aNouWMuuWfn+WPs+S+p+aVsOaNrua4heepuiwg5piv5ZCm56Gu6K6kPycsICfmj5DnpLonLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczQxLnRiRGF0YSA9IFtdOwogICAgICAgIF90aGlzNDEucmVzZXRJbm5lckZvcm0oKTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzNDEuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ2luZm8nLAogICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iCcKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgc2hvd1BhcnRVc2VkUHJvY2VzczogZnVuY3Rpb24gc2hvd1BhcnRVc2VkUHJvY2Vzcyhyb3cpIHsKICAgICAgaWYgKHRoaXMuaXNOZXN0KSB7CiAgICAgICAgcmV0dXJuICEhcm93LkNvbXBfSW1wb3J0X0RldGFpbF9JZDsKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gIXRoaXMuaXNWaWV3ICYmIHJvdy5UeXBlICE9PSAnRGlyZWN0JzsKICAgICAgfQogICAgfSwKICAgIGhhbmRsZUV4cG9ydDogZnVuY3Rpb24gaGFuZGxlRXhwb3J0KCkgewogICAgICBpZiAoIXRoaXMudGJEYXRhLmxlbmd0aCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgbWVzc2FnZTogJ+aaguaXoOaVsOaNricsCiAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICB9KTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy4kcmVmcy54VGFibGUuZXhwb3J0RGF0YSh7CiAgICAgICAgZmlsZW5hbWU6ICJcdTkwRThcdTRFRjZcdTYzOTJcdTRFQTctIi5jb25jYXQodGhpcy5mb3JtSW5saW5lLlNjaGR1bGluZ19Db2RlLCAiKFx1OTBFOFx1NEVGNikiKSwKICAgICAgICB0eXBlOiAneGxzeCcsCiAgICAgICAgZGF0YTogdGhpcy50YkRhdGEKICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlQ2xvc2VEcmF3ZXI6IGZ1bmN0aW9uIGhhbmRsZUNsb3NlRHJhd2VyKCkgewogICAgICB0aGlzLmRyYXdlciA9IGZhbHNlOwogICAgfSwKICAgIGZpbGVWaWV3OiBmdW5jdGlvbiBmaWxlVmlldygpIHsKICAgICAgdGhpcy5pZnJhbWVLZXkgPSB1dWlkdjQoKTsKICAgICAgdGhpcy5pZnJhbWVVcmwgPSAiIi5jb25jYXQodGhpcy5iYXNlQ2FkVXJsLCAiP3JvdXRlcj0xJmlmcmFtZUlkPTExJmJhc2VVcmw9IikuY29uY2F0KGJhc2VVcmwoKSwgIiZ0b2tlbj0iKS5jb25jYXQobG9jYWxTdG9yYWdlLmdldEl0ZW0oJ1Rva2VuJyksICImYXV0aF9pZD0iKS5jb25jYXQobG9jYWxTdG9yYWdlLmdldEl0ZW0oJ0xhc3RfV29ya2luZ19PYmplY3RfSWQnKSk7CiAgICAgIHRoaXMuZHJhd2VyID0gdHJ1ZTsKICAgIH0sCiAgICByZW5kZXJJZnJhbWU6IGZ1bmN0aW9uIHJlbmRlcklmcmFtZSgpIHsKICAgICAgdmFyIEV4dGVuc2lvbk5hbWUgPSB0aGlzLmV4dGVuc2lvbk5hbWU7CiAgICAgIHZhciBmaWxlQmltID0gdGhpcy5maWxlQmltOwogICAgICB0aGlzLmlmcmFtZVVybCA9ICIiLmNvbmNhdCh0aGlzLmJhc2VDYWRVcmwsICI/cm91dGVyPTEmaWZyYW1lSWQ9MTEmYmFzZVVybD0iKS5jb25jYXQoYmFzZVVybCgpLCAiJnRva2VuPSIpLmNvbmNhdChsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnVG9rZW4nKSwgIiZhdXRoX2lkPSIpLmNvbmNhdChsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnTGFzdF9Xb3JraW5nX09iamVjdF9JZCcpKTsKICAgICAgdGhpcy5mdWxsc2NyZWVuaWQgPSBFeHRlbnNpb25OYW1lOwogICAgICB0aGlzLmZ1bGxiaW1pZCA9IGZpbGVCaW07CiAgICB9LAogICAgZnVsbHNjcmVlbjogZnVuY3Rpb24gZnVsbHNjcmVlbih2KSB7CiAgICAgIHRoaXMudGVtcGxhdGVVcmwgPSAiIi5jb25jYXQodGhpcy5iYXNlQ2FkVXJsLCAiP3JvdXRlcj0xJmlmcmFtZUlkPTEzJmJhc2VVcmw9IikuY29uY2F0KGJhc2VVcmwoKSwgIiZ0b2tlbj0iKS5jb25jYXQobG9jYWxTdG9yYWdlLmdldEl0ZW0oJ1Rva2VuJyksICImYXV0aF9pZD0iKS5jb25jYXQobG9jYWxTdG9yYWdlLmdldEl0ZW0oJ0xhc3RfV29ya2luZ19PYmplY3RfSWQnKSk7CiAgICAgIHRoaXMuZHJhd2Vyc3VsbCA9IHRydWU7CiAgICB9LAogICAgZnJhbWVMaXN0ZW5lcjogZnVuY3Rpb24gZnJhbWVMaXN0ZW5lcihfcmVmMCkgewogICAgICB2YXIgZGF0YSA9IF9yZWYwLmRhdGE7CiAgICAgIGlmIChkYXRhLnR5cGUgPT09ICdsb2FkZWQnKSB7CiAgICAgICAgY29uc29sZS5sb2coJ2RhdGEnLCBkYXRhKTsKICAgICAgICBjb25zb2xlLmVycm9yKCdkYXRhLmRhdGEuaWZyYW1lSWQnLCBkYXRhLmRhdGEuaWZyYW1lSWQsIF90eXBlb2YoZGF0YS5kYXRhLmlmcmFtZUlkKSk7CiAgICAgICAgaWYgKGRhdGEuZGF0YS5pZnJhbWVJZCA9PT0gJzExJykgewogICAgICAgICAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ2ZyYW1lJykuY29udGVudFdpbmRvdy5wb3N0TWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdyb3V0ZXInLAogICAgICAgICAgICBwYXRoOiAnL21vZGVsQ2FkJywKICAgICAgICAgICAgcXVlcnk6IHsKICAgICAgICAgICAgICAvLyBiYXNlVXJsOiBiYXNlVXJsKCksCiAgICAgICAgICAgICAgY2FkSWQ6IHRoaXMuZmlsZUJpbSwKICAgICAgICAgICAgICBwcm9qZWN0SWQ6IHRoaXMuY2FkUm93UHJvamVjdElkLAogICAgICAgICAgICAgIHN0ZWVsTmFtZTogdGhpcy5jYWRSb3dDb2RlLAogICAgICAgICAgICAgIHNob3dDYWQ6IHRoaXMuSXNVcGxvYWRDYWQsCiAgICAgICAgICAgICAgaXNTdWJBc3NlbWJseTogdHJ1ZSwKICAgICAgICAgICAgICBpc1BhcnQ6IHRydWUKICAgICAgICAgICAgICAvLyBjYWRJZDogdGhpcy5maWxlQmltCiAgICAgICAgICAgICAgLy8gdG9rZW46IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdUb2tlbicpLAogICAgICAgICAgICAgIC8vIGF1dGhfaWQ6IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdMYXN0X1dvcmtpbmdfT2JqZWN0X0lkJykKICAgICAgICAgICAgfQogICAgICAgICAgfSwgJyonKTsKICAgICAgICB9IGVsc2UgaWYgKGRhdGEuZGF0YS5pZnJhbWVJZCA9PT0gJzEzJykgewogICAgICAgICAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ2Z1bGxGcmFtZScpLmNvbnRlbnRXaW5kb3cucG9zdE1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAncm91dGVyJywKICAgICAgICAgICAgcGF0aDogJy9tb2RlbENhZCcsCiAgICAgICAgICAgIHF1ZXJ5OiB7CiAgICAgICAgICAgICAgLy8gYmFzZVVybDogYmFzZVVybCgpLAogICAgICAgICAgICAgIGNhZElkOiB0aGlzLmZpbGVCaW0sCiAgICAgICAgICAgICAgcHJvamVjdElkOiB0aGlzLmNhZFJvd1Byb2plY3RJZCwKICAgICAgICAgICAgICBzdGVlbE5hbWU6IHRoaXMuY2FkUm93Q29kZSwKICAgICAgICAgICAgICBzaG93Q2FkOiB0aGlzLklzVXBsb2FkQ2FkLAogICAgICAgICAgICAgIGlzU3ViQXNzZW1ibHk6IHRydWUsCiAgICAgICAgICAgICAgaXNQYXJ0OiB0cnVlCiAgICAgICAgICAgICAgLy8gdG9rZW46IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdUb2tlbicpLAogICAgICAgICAgICAgIC8vIGF1dGhfaWQ6IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdMYXN0X1dvcmtpbmdfT2JqZWN0X0lkJykKICAgICAgICAgICAgfQogICAgICAgICAgfSwgJyonKTsKICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9KQp9Ow=="}, {"version": 3, "names": ["closeTagView", "debounce", "BatchProcessAdjust", "GetCanSchdulingPartList", "GetDwg", "GetSchdulingWorkingTeams", "SaveUnitSchedulingWorkshopNew", "SaveSchdulingTaskById", "SaveUnitSchdulingDraftNew", "GetUnitSchdulingInfoDetail", "GetStopList", "AddDraft", "OwnerProcess", "Workshop", "GetGridByCode", "getUnique", "uniqueCode", "v4", "uuidv4", "numeral", "GetLibListType", "GetProcessFlowListWithTechnology", "GetProcessListBase", "AreaGetEntity", "mapActions", "mapGetters", "GetPartTypeList", "moment", "ExpandableSection", "TreeDetail", "GetInstallUnitIdNameList", "GetProjectAreaTreeList", "GetCompTypeTree", "parseOssUrl", "DynamicTableFields", "getConfigure", "baseUrl", "GetSteelCadAndBimId", "getBomName", "SPLIT_SYMBOL", "components", "data", "drawer", "drawersull", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fullscreenid", "iframeUrl", "fullbimid", "fileBim", "IsUploadCad", "cadRowCode", "cadRowProjectId", "tb<PERSON><PERSON>", "isComponentOptions", "label", "value", "specOptions", "filterTypeOption", "filterCodeOption", "projectOptions", "areaOptions", "installOptions", "projectList", "installList", "areaList", "pickerOptions", "disabledDate", "time", "innerForm", "projectName", "areaName", "installName", "searchContent", "searchSpecSearch", "searchDirect", "cur<PERSON><PERSON>ch", "searchType", "formInline", "Schduling_Code", "Create_UserName", "Finish_Date", "InstallUnit_Id", "Remark", "total", "currentIds", "columns", "tbData", "tbConfig", "TotalCount", "multipleSelection", "showExpand", "pgLoading", "deleteLoading", "workShopIsOpen", "isOwnerNull", "dialogVisible", "openAddDraft", "saveLoading", "tbLoading", "isCheckAll", "currentComponent", "gridCode", "dWidth", "title", "search", "pageType", "undefined", "tipLabel", "technologyOption", "typeOption", "workingTeam", "pageStatus", "scheduleId", "partComOwnerColumn", "installUnitIdList", "projectId", "areaId", "statusType", "expandedKey", "treeData", "treeParamsComponentType", "filterable", "clickParent", "props", "children", "treeSelectParams", "placeholder", "collapseTags", "clearable", "disabledAdd", "levelName", "projectOption", "watch", "handler", "n", "o", "checkOwner", "<PERSON><PERSON><PERSON><PERSON>", "immediate", "computed", "_objectSpread", "isCom", "<PERSON><PERSON><PERSON><PERSON>", "isEdit", "isAdd", "addDraftKey", "statusCode", "_this", "item", "find", "v", "Id", "Name", "isPartPrepare", "getIsPartPrepare", "isNest", "isStopFlag", "some", "stopFlag", "created", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "process", "env", "NODE_ENV", "baseCadUrl", "code", "then", "res", "Data", "stop", "mounted", "_this3", "_callee2", "_this3$$route$query", "install", "_callee2$", "_context2", "initProcessList", "tbDataMap", "craftCodeMap", "$route", "query", "pg_type", "level", "status", "model", "pid", "localStorage", "getItem", "unique", "checkWorkshopIsOpen", "fetchData", "mergeConfig", "getType", "sent", "window", "addEventListener", "frameListener", "$once", "console", "log", "removeEventListener", "activated", "_this4", "methods", "every", "Comp_Import_Detail_Id", "idx", "findIndex", "Code", "splice", "ownerColumn", "$message", "message", "type", "push", "comPart", "_this5", "_callee3", "_callee3$", "_context3", "getConfig", "getWorkTeam", "_this6", "for<PERSON>ach", "cur", "Project_Name", "includes", "InstallUnit_Name", "Area_Name", "_this7", "_callee4", "configCode", "_callee4$", "_context4", "getTableConfig", "workshopEnabled", "filter", "changeColumn", "_this8", "_callee5", "_callee5$", "_context5", "_this9", "_callee6", "resData", "_callee6$", "_context6", "getPartPageList", "getNestPageList", "initTbData", "fetchTreeDataLocal", "fetchTreeStatus", "<PERSON><PERSON><PERSON><PERSON>", "$store", "tbSelectChange", "array", "records", "getAreaInfo", "_this0", "id", "IsSucceed", "_res$Data", "_res$Data2", "start", "Demand_Begin_Date", "end", "Demand_End_Date", "getTime", "Message", "handleClose", "_this1", "Promise", "resolve", "reject", "Ids", "nestIds", "_list", "list", "map", "Part_Used_Process", "Scheduled_Used_Process", "Part_Type_Used_Process", "Workshop_Id", "Scheduled_Workshop_Id", "Workshop_Name", "Scheduled_Workshop_Name", "Technology_Path", "Scheduled_Technology_Path", "chooseCount", "Can_Schduling_Count", "_this10", "_callee7", "result", "_callee7$", "_context7", "Schduling_Plan_Id", "_res$Data3", "_res$Data4", "_res$Data5", "SarePartsModel", "Unit_Part_Used_Process", "Object", "assign", "Schduling_Plan", "Process_List", "plist", "key", "Process_Code", "changeProcessList", "getStopList", "abrupt", "_this11", "_callee8", "submitObj", "_callee8$", "_context8", "Part_Aggregate_Id", "Type", "stopMap", "Is_Stop", "row", "$set", "_this12", "teamKey", "arguments", "length", "JSON", "parse", "stringify", "_row$Technology_Path", "processList", "split", "uuid", "addElementToTbData", "newData", "r", "p", "ele", "index", "getRowUnique", "Working_Team_Id", "max", "getRowUniqueMax", "Count", "setInputMax", "ids", "toString", "addToTbList", "newList", "_this13", "_callee9", "_callee9$", "_context9", "mergeSelectList", "setAddTbKey", "_this14", "_callee0", "hasUsedPartFlag", "_callee0$", "_context0", "mergeCraftProcess", "element", "getMergeUniqueRow", "cur<PERSON><PERSON><PERSON><PERSON>", "Technology_Code", "Array", "partUsedProcessArr", "allPartsIncluded", "part", "join", "pu<PERSON>", "<PERSON><PERSON><PERSON><PERSON>_Count", "Schduled_Weight", "Weight", "format", "add", "showCraftUsedPartResult", "sort", "a", "b", "initRowIndex", "timeEnd", "hasUsedPart", "_this15", "setTimeout", "$alert", "confirmButtonText", "getUniKey", "checkForm", "isValidate", "$refs", "validate", "valid", "saveDraft", "_arguments", "_this16", "_callee1", "_this16$$refs$draft", "isOrder", "checkSuccess", "_this16$getSubmitTbIn", "tableData", "isSuccess", "_callee1$", "_context1", "getSubmitTbInfo", "handleSaveDraft", "saveWorkShop", "_this17", "_callee10", "obj", "_callee10$", "_context10", "Sch<PERSON>ling_Comps", "Project_Id", "Area_Id", "Schduling_Model", "_this18", "_loop", "i", "msg", "concat", "from", "Set", "_loop2", "j", "schduledCount", "groups", "Allocation_Teams", "againCount", "reduce", "acc", "Again_Count", "_list2", "apply", "_toConsumableArray", "hasInput", "keys", "_", "startsWith", "_ret", "_this19", "_callee11", "_fun", "obj<PERSON><PERSON>", "orderSuccess", "_callee11$", "_context11", "hasOwnProperty", "templateScheduleCode", "handleDelete", "_this20", "selectedUuids", "isSelected", "has", "_this21", "_callee12", "_callee12$", "_context12", "handleSubmit", "_this22", "_this22$getSubmitTbIn", "$confirm", "cancelButtonText", "saveDraftDoSubmit", "catch", "_this23", "_callee13", "_this23$formInline", "_isSuccess", "_callee13$", "_context13", "doSubmit", "scheduleCode", "_this24", "schdulingPlanId", "finally", "getWorkShop", "_this25", "origin", "_value$workShop", "workShop", "Display_Name", "_value$workShop2", "set<PERSON>ath", "_value$workShop3", "handleBatchWorkshop", "_this26", "$nextTick", "getProcessOption", "workshopId", "_this27", "setLibType", "_this28", "Component_type", "info", "workCode", "WorkCode", "replace", "inputChange", "inputValuesKeys", "endsWith", "val", "curCode", "otherTotal", "x", "sendProcess", "_ref", "arr", "str", "originalPath", "resetWorkTeamMax", "_str", "_this29", "k", "$delete", "checkPermissionTeam", "processStr", "processCode", "_this30", "_callee14", "_callee14$", "_context14", "Grid", "ColumnList", "ownerColumn2", "setColumnDisplay", "Is_Display", "activeCellMethod", "_ref2", "_column$field", "column", "columnIndex", "field", "openBPADialog", "_this31", "IsUnique", "checkIsUniqueWorkshop", "setData", "isUnique", "firstV", "checkHasWorkShop", "hasWorkShop", "handleAddDialog", "_this32", "initData", "selectKeys", "changeAddTbKeys", "workingId", "handleSelectMenu", "handleSetCraftProcess", "_this33", "_callee15", "codes", "_loop3", "_craftCodeMap", "_callee15$", "_context16", "_loop3$", "_context15", "t0", "t1", "done", "<PERSON><PERSON><PERSON>", "getCraftProcess", "_this34", "_callee16", "showSuccess", "rowList", "workshopIds", "w_process", "workshopPromise", "_callee16$", "_context17", "_defineProperty", "all", "values", "workshop", "flag", "_loop4", "curRow", "workshopProcess", "craftArray", "isIncluded", "handleBatchOwner", "_this35", "setOption", "_this36", "gyGroup", "TechnologyCodes", "gyList", "gyMap", "handleReverse", "checked", "setAllCheckboxRow", "_this37", "getCompTree", "fun", "Label", "_this37$$refs$treeSel", "treeSelectComponentType", "treeDataUpdateFun", "handleDwg", "_this38", "Comp_Id", "Part_Id", "importDetailId", "extensionName", "ExtensionName", "IsUpload", "Part_Code", "Sys_Project_Id", "fileView", "setProcessList", "resetInnerForm", "resetFields", "xTable", "clearFilter", "innerFilter", "_this39", "clearCheckboxRow", "getColumnByField", "filters", "option", "updateData", "filterComponentMethod", "_ref4", "Is_Component", "filterSpecMethod", "_ref5", "trim", "splitAndClean", "input", "specArray", "Spec", "filterCodeMethod", "_ref6", "filterProjectMethod", "_ref7", "filterAreaMethod", "_ref8", "filterInstallMethod", "_ref9", "componentTypeFilter", "e", "_this$$refs", "filterFun", "getInstallUnitIdNameList", "_this40", "installChange", "_this41", "showPartUsedProcess", "handleExport", "exportData", "filename", "handleCloseDrawer", "renderIframe", "fullscreen", "templateUrl", "_ref0", "error", "iframeId", "_typeof", "document", "getElementById", "contentWindow", "postMessage", "path", "cadId", "steelName", "showCad", "isSubAssembly", "isPart"], "sources": ["src/views/PRO/plan-production/schedule-production-new-unit-part/draft.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 flex-row\">\r\n    <div class=\"cs-right\">\r\n      <el-card v-loading=\"pgLoading\" class=\"box-card h100\" element-loading-text=\"正在处理...\">\r\n        <h4 class=\"topTitle\"><span />基本信息</h4>\r\n        <el-form\r\n          ref=\"formInline\"\r\n          :inline=\"true\"\r\n          :model=\"formInline\"\r\n          class=\"demo-form-inline\"\r\n        >\r\n          <el-form-item v-if=\"!isAdd&&!isNest\" label=\"排产单号\" prop=\"Schduling_Code\">\r\n            <span v-if=\"isView\">{{ formInline.Status === 0 ? '' : formInline.Schduling_Code }}</span>\r\n            <el-input v-else v-model=\"formInline.Schduling_Code\" disabled />\r\n          </el-form-item>\r\n          <el-form-item label=\"计划员\" prop=\"Create_UserName\">\r\n            <span v-if=\"isView\">{{ formInline.Create_UserName }}</span>\r\n            <el-input\r\n              v-else\r\n              v-model=\"formInline.Create_UserName\"\r\n              disabled\r\n            />\r\n          </el-form-item>\r\n          <el-form-item\r\n            label=\"要求完成时间\"\r\n            prop=\"Finish_Date\"\r\n            :rules=\"{ required: true, message: '请选择', trigger: 'change' }\"\r\n          >\r\n            <span v-if=\"isView\">{{ formInline.Finish_Date | timeFormat }}</span>\r\n            <el-date-picker\r\n              v-else\r\n              v-model=\"formInline.Finish_Date\"\r\n              :picker-options=\"pickerOptions\"\r\n              :disabled=\"isView\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              type=\"date\"\r\n              placeholder=\"选择日期\"\r\n            />\r\n          </el-form-item>\r\n          <!--          <el-form-item v-if=\"!isNest\" label=\"批次\" prop=\"Create_UserName\">\r\n            <span v-if=\"isView\">{{ installName }}</span>\r\n            <el-select\r\n              v-else\r\n              v-model=\"formInline.InstallUnit_Id\"\r\n              :disabled=\"!isAdd\"\r\n              filterable\r\n              placeholder=\"请选择\"\r\n              @change=\"installChange\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in installUnitIdList\"\r\n                :key=\"item.Id\"\r\n                :label=\"item.Name\"\r\n                :value=\"item.Id\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>-->\r\n          <el-form-item label=\"备注\" prop=\"Remark\">\r\n            <span v-if=\"isView\">{{ formInline.Remark }}</span>\r\n            <el-input\r\n              v-else\r\n              v-model=\"formInline.Remark\"\r\n              :disabled=\"isView\"\r\n              style=\"width: 320px\"\r\n              placeholder=\"请输入\"\r\n            />\r\n          </el-form-item>\r\n\r\n        </el-form>\r\n        <el-divider class=\"elDivder\" />\r\n        <div v-if=\"!isView\">\r\n          <div ref=\"searchDom\" class=\"search-container\">\r\n\r\n            <el-form ref=\"searchForm\" :model=\"innerForm\">\r\n              <el-row>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"70px\" label=\"项目名称\" prop=\"projectName\">\r\n                    <el-select\r\n                      v-model=\"innerForm.projectName\"\r\n                      filterable\r\n                      clearable\r\n                      placeholder=\"请选择\"\r\n                      class=\"w100\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"(item,idx) in projectList\"\r\n                        :key=\"idx\"\r\n                        :label=\"item\"\r\n                        :value=\"item\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"70px\" label=\"区域\" prop=\"areaName\">\r\n                    <el-select\r\n                      v-model=\"innerForm.areaName\"\r\n                      filterable\r\n                      clearable\r\n                      placeholder=\"请选择\"\r\n                      class=\"w100\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"(item,idx) in areaList\"\r\n                        :key=\"idx\"\r\n                        :label=\"item\"\r\n                        :value=\"item\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"70px\" label=\"批次\" prop=\"installName\">\r\n                    <el-select\r\n                      v-model=\"innerForm.installName\"\r\n                      filterable\r\n                      clearable\r\n                      placeholder=\"请选择\"\r\n                      class=\"w100\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"(item,idx) in installList\"\r\n                        :key=\"idx\"\r\n                        :label=\"item\"\r\n                        :value=\"item\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"90px\" prop=\"searchContent\" :label=\"`${levelName}名称`\">\r\n                    <el-input\r\n                      v-model=\"innerForm.searchContent\"\r\n                      clearable\r\n                      class=\"input-with-select w100\"\r\n                      placeholder=\"请输入内容\"\r\n                      size=\"small\"\r\n                    >\r\n                      <el-select\r\n                        slot=\"prepend\"\r\n                        v-model=\"curSearch\"\r\n                        placeholder=\"请选择\"\r\n                        style=\"width: 100px\"\r\n                      >\r\n                        <el-option label=\"精准查询\" :value=\"1\" />\r\n                        <el-option label=\"模糊查询\" :value=\"0\" />\r\n                      </el-select>\r\n                    </el-input>\r\n                  </el-form-item>\r\n                </el-col>\r\n\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"70px\" label=\"规格\" prop=\"searchSpecSearch\">\r\n                    <el-input v-model=\"innerForm.searchSpecSearch\" class=\"w100\" placeholder=\"请输入\" clearable=\"\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item v-if=\"isCom\" label-width=\"80px\" label=\"是否直发件\" prop=\"searchDirect\">\r\n                    <el-select v-model=\"innerForm.searchDirect\" class=\"w100\" placeholder=\"请选择\" clearable>\r\n                      <el-option label=\"是\" :value=\"true\" />\r\n                      <el-option label=\"否\" :value=\"false\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label-width=\"20px\">\r\n                    <el-button type=\"primary\" @click=\"innerFilter\">搜索</el-button>\r\n                    <el-button @click=\"resetInnerForm\">重置</el-button>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form>\r\n\r\n          </div>\r\n        </div>\r\n\r\n        <vxe-toolbar\r\n          ref=\"xToolbar1\"\r\n        >\r\n          <template #buttons>\r\n            <div v-if=\"!isView\" class=\"btn-x\">\r\n              <el-button v-if=\"!isNest\" type=\"primary\" @click=\"handleAddDialog()\">添加</el-button>\r\n\r\n              <el-button\r\n                v-if=\"workshopEnabled\"\r\n                :disabled=\"!multipleSelection.length\"\r\n                @click=\"handleBatchWorkshop(1)\"\r\n              >分配车间\r\n              </el-button>\r\n\r\n              <el-dropdown style=\"margin:0 10px\" @command=\"handleSelectMenu\">\r\n                <el-button :disabled=\"!multipleSelection.length\" type=\"primary\" plain>\r\n                  分配工序<i class=\"el-icon-arrow-down el-icon--right\" />\r\n                </el-button>\r\n                <el-dropdown-menu slot=\"dropdown\">\r\n                  <el-dropdown-item\r\n                    command=\"process\"\r\n                  >批量分配工序\r\n                  </el-dropdown-item>\r\n                  <el-dropdown-item\r\n                    command=\"craft\"\r\n                  >工艺代码分配\r\n                  </el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </el-dropdown>\r\n              <el-button\r\n                v-if=\"!isCom && !isOwnerNull\"\r\n                :disabled=\"!multipleSelection.length\"\r\n                @click=\"handleBatchOwner(1)\"\r\n              >批量分配领用工序\r\n              </el-button>\r\n              <el-button\r\n                plain\r\n                :disabled=\"!tbData.length\"\r\n                :loading=\"false\"\r\n                @click=\"handleReverse\"\r\n              >反选\r\n              </el-button>\r\n              <el-button\r\n                type=\"danger\"\r\n                plain\r\n                :loading=\"deleteLoading\"\r\n                :disabled=\"!multipleSelection.length\"\r\n                @click=\"handleDelete\"\r\n              >删除\r\n              </el-button>\r\n            </div>\r\n            <div v-else>\r\n              <el-button style=\"margin-bottom: 8px;\" :disabled=\"!tbData.length\" @click=\"handleExport\">导出</el-button>\r\n            </div>\r\n          </template>\r\n          <template #tools>\r\n            <DynamicTableFields\r\n              title=\"表格配置\"\r\n              :table-config-code=\"gridCode\"\r\n              @updateColumn=\"changeColumn\"\r\n            />\r\n          </template>\r\n        </vxe-toolbar>\r\n        <div class=\"tb-x\">\r\n          <!--          activeMethod: activeCellMethod,-->\r\n          <vxe-table\r\n            ref=\"xTable\"\r\n            :key=\"tbKey\"\r\n            :empty-render=\"{name: 'NotData'}\"\r\n            show-header-overflow\r\n            :checkbox-config=\"{checkField: 'checked'}\"\r\n            class=\"cs-vxe-table\"\r\n            :row-config=\"{isCurrent: true, isHover: true}\"\r\n            align=\"left\"\r\n            height=\"100%\"\r\n            :filter-config=\"{showIcon:false}\"\r\n            show-overflow\r\n            :loading=\"tbLoading\"\r\n            stripe\r\n            :scroll-y=\"{enabled: true, gt: 20}\"\r\n            size=\"medium\"\r\n            :edit-config=\"{\r\n              trigger: 'click',\r\n              mode: 'cell',\r\n              showIcon: !isView,\r\n\r\n            }\"\r\n            :data=\"tbData\"\r\n            resizable\r\n            :tooltip-config=\"{ enterable: true }\"\r\n            @checkbox-all=\"tbSelectChange\"\r\n            @checkbox-change=\"tbSelectChange\"\r\n          >\r\n            <vxe-column v-if=\"!isView\" fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n            <template v-for=\"item in columns\">\r\n              <vxe-column\r\n                v-if=\"item.Code === 'Is_Component'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"isComponentOptions\"\r\n                :filter-method=\"filterComponentMethod\"\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <el-tag\r\n                    :type=\"row.Is_Component ? 'danger' : 'success'\"\r\n                  >{{ row.Is_Component ? '否' : '是' }}\r\n                  </el-tag>\r\n                </template>\r\n              </vxe-column>\r\n\r\n              <vxe-column\r\n                v-else-if=\"['Comp_Code','Part_Code'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :filter-method=\"filterCodeMethod\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"filterCodeOption\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <input\r\n                    v-for=\"(option, index) in column.filters\"\r\n                    :key=\"index\"\r\n                    v-model=\"option.data\"\r\n                    type=\"type\"\r\n                    @input=\"$panel.changeOption($event, !!option.data, option)\"\r\n                  >\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                  <el-tag v-if=\"row.Is_Change\" style=\"margin: 8px;\" type=\"danger\">变</el-tag>\r\n                  <el-link v-if=\"row.DwgCount>0\" type=\"primary\" @click.stop=\"handleDwg(row)\"> {{ row[item.Code] | displayValue }}\r\n                  </el-link>\r\n                  <span v-else>{{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Spec'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"specOptions\"\r\n                :filter-method=\"filterSpecMethod\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <input\r\n                    v-for=\"(option, index) in column.filters\"\r\n                    :key=\"index\"\r\n                    v-model=\"option.data\"\r\n                    type=\"type\"\r\n                    @input=\"$panel.changeOption($event, !!option.data, option)\"\r\n                  >\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Spec | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Project_Name'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"projectOptions\"\r\n                :filter-method=\"filterProjectMethod\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <select v-for=\"(option, index) in column.filters\" :key=\"index\" v-model=\"option.data\" class=\"my-select\" @change=\"$panel.changeOption($event, !!option.data, option)\">\r\n                    <option v-for=\"(label, cIndex) in projectList\" :key=\"cIndex\" :value=\"label\">{{ label }}</option>\r\n                  </select>\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Project_Name | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Area_Name'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"areaOptions\"\r\n                :filter-method=\"filterAreaMethod\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <select v-for=\"(option, index) in column.filters\" :key=\"index\" v-model=\"option.data\" class=\"my-select\" @change=\"$panel.changeOption($event, !!option.data, option)\">\r\n                    <option v-for=\"(label, cIndex) in areaList\" :key=\"cIndex\" :value=\"label\">{{ label }}</option>\r\n                  </select>\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Area_Name | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'InstallUnit_Name'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"installOptions\"\r\n                :filter-method=\"filterInstallMethod\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <select v-for=\"(option, index) in column.filters\" :key=\"index\" v-model=\"option.data\" class=\"my-select\" @change=\"$panel.changeOption($event, !!option.data, option)\">\r\n                    <option v-for=\"(label, cIndex) in installList\" :key=\"cIndex\" :value=\"label\">{{ label }}</option>\r\n                  </select>\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.InstallUnit_Name | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Schduled_Weight'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  {{ ((row.Schduled_Count * row.Weight).toFixed(2) / 1 ) | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Technology_Path'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n                :show-overflow=\"false\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <div class=\"cs-column-row\">\r\n                    <div class=\"cs-ell\">\r\n                      <el-tooltip class=\"item\" effect=\"dark\" :content=\"row.Technology_Path\" placement=\"top\">\r\n                        <span>{{ row.Technology_Path | displayValue }}</span>\r\n                      </el-tooltip>\r\n                    </div>\r\n                    <i\r\n                      v-if=\"!isView\"\r\n                      class=\"el-icon-edit\"\r\n                      @click=\"openBPADialog(2, row)\"\r\n                    />\r\n                  </div>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Part_Used_Process'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :title=\"item.Display_Name\"\r\n                :show-overflow=\"false\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <div class=\"cs-column-row\">\r\n                    <div class=\"cs-ell\">\r\n                      <el-tooltip class=\"item\" effect=\"dark\" :content=\"row.Part_Used_Process\" placement=\"top\">\r\n                        <span>{{ row.Part_Used_Process | displayValue }}</span>\r\n                      </el-tooltip>\r\n                    </div>\r\n                    <i\r\n                      v-if=\"showPartUsedProcess(row)\"\r\n                      class=\"el-icon-edit\"\r\n                      @click=\"handleBatchOwner(2, row)\"\r\n                    />\r\n                  </div>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Workshop_Name'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :show-overflow=\"false\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <div class=\"cs-column-row\">\r\n                    <div class=\"cs-ell\">\r\n                      <el-tooltip class=\"item\" effect=\"dark\" :content=\"row.Workshop_Name\" placement=\"top\">\r\n                        <span>{{ row.Workshop_Name | displayValue }}</span>\r\n                      </el-tooltip>\r\n                    </div>\r\n                    <i\r\n                      v-if=\"!isView\"\r\n                      class=\"el-icon-edit\"\r\n                      @click=\"handleBatchWorkshop(2, row)\"\r\n                    />\r\n                  </div>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Schduled_Count'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :edit-render=\"{enabled:!isView}\"\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #edit=\"{ row }\">\r\n                  <vxe-input\r\n                    v-model.number=\"row.Schduled_Count\"\r\n                    type=\"integer\"\r\n                    min=\"0\"\r\n                    :max=\"row.chooseCount\"\r\n                  />\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Schduled_Count | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else\r\n                :key=\"item.Id\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                show-overflow=\"tooltip\"\r\n                sortable\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                :min-width=\"item.Width\"\r\n              />\r\n            </template>\r\n\r\n          </vxe-table>\r\n        </div>\r\n        <el-divider v-if=\"!isView\" class=\"elDivder\" />\r\n        <footer v-if=\"!isView\">\r\n          <div class=\"data-info\">\r\n            <el-tag\r\n              size=\"medium\"\r\n              class=\"info-x\"\r\n            >已选 {{ multipleSelection.length }} 条数据\r\n            </el-tag>\r\n            <el-tag v-if=\"tipLabel\" size=\"medium\" class=\"info-x\">{{\r\n              tipLabel\r\n            }}\r\n            </el-tag>\r\n          </div>\r\n          <div>\r\n            <el-button v-if=\"workshopEnabled&&!isNest\" type=\"primary\" @click=\"saveWorkShop\">保存车间分配</el-button>\r\n            <el-button\r\n              v-if=\"!isNest\"\r\n              type=\"primary\"\r\n              :loading=\"saveLoading\"\r\n              @click=\"saveDraft(false)\"\r\n            >保存草稿\r\n            </el-button>\r\n            <el-button :disabled=\"deleteLoading || isStopFlag\" @click=\"handleSubmit\">下发任务</el-button>\r\n          </div>\r\n        </footer>\r\n      </el-card>\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"dWidth\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :is-nest=\"isNest\"\r\n        :is-part-prepare=\"isPartPrepare\"\r\n        :process-list=\"processList\"\r\n        :page-type=\"pageType\"\r\n        :part-type-option=\"typeOption\"\r\n        @close=\"handleClose\"\r\n        @sendProcess=\"sendProcess\"\r\n        @workShop=\"getWorkShop\"\r\n        @refresh=\"fetchData\"\r\n        @setProcessList=\"setProcessList\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      :key=\"addDraftKey\"\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"openAddDraft\"\r\n      :width=\"dWidth\"\r\n      top=\"7vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <add-draft\r\n        v-if=\"openAddDraft\"\r\n        ref=\"draft\"\r\n        :level-name=\"levelName\"\r\n        :current-ids=\"currentIds\"\r\n        :is-part-prepare=\"isPartPrepare\"\r\n        :install-id=\"formInline.InstallUnit_Id\"\r\n        :schedule-id=\"scheduleId\"\r\n        :show-dialog=\"openAddDraft\"\r\n        :page-type=\"pageType\"\r\n        @addToTbList=\"addToTbList\"\r\n        @sendSelectList=\"mergeSelectList\"\r\n        @setAddTbKey=\"setAddTbKey\"\r\n        @close=\"handleClose\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-drawer\r\n      :visible.sync=\"drawer\"\r\n      direction=\"btt\"\r\n      size=\"60%\"\r\n      destroy-on-close\r\n      :before-close=\"handleCloseDrawer\"\r\n      @opened=\"renderIframe\"\r\n    >\r\n      <div style=\"width: 100%; display: flex\">\r\n        <div style=\"margin-left: 20px\">\r\n          <span style=\"display: inline-block; width: 100px\">部件图纸</span>\r\n        </div>\r\n        <el-button\r\n          v-if=\"fileBim\"\r\n          style=\"margin-left: 42%\"\r\n          @click=\"fullscreen(1)\"\r\n        >全屏</el-button>\r\n      </div>\r\n      <iframe\r\n        id=\"frame\"\r\n        :key=\"iframeKey\"\r\n        :src=\"iframeUrl\"\r\n        style=\"width: 100%; border: 0px; margin: 0; height: 60vh\"\r\n      />\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { closeTagView, debounce } from '@/utils'\r\nimport BatchProcessAdjust from './components/BatchProcessAdjust'\r\nimport {\r\n  GetCanSchdulingPartList,\r\n  GetDwg,\r\n  GetSchdulingWorkingTeams,\r\n  SaveUnitSchedulingWorkshopNew,\r\n  SaveSchdulingTaskById, SaveUnitSchdulingDraftNew, GetUnitSchdulingInfoDetail\r\n} from '@/api/PRO/production-task'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nimport AddDraft from './components/addDraft'\r\nimport OwnerProcess from './components/OwnerProcess'\r\nimport Workshop from './components/Workshop.vue'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { getUnique, uniqueCode } from './constant'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport numeral from 'numeral'\r\nimport { GetLibListType, GetProcessFlowListWithTechnology, GetProcessListBase } from '@/api/PRO/technology-lib'\r\nimport { AreaGetEntity } from '@/api/plm/projects'\r\nimport { mapActions, mapGetters } from 'vuex'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\nimport moment from 'moment'\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport TreeDetail from '@/components/TreeDetail/index.vue'\r\nimport { GetInstallUnitIdNameList, GetProjectAreaTreeList } from '@/api/PRO/project'\r\n\r\nimport { GetCompTypeTree } from '@/api/PRO/factorycheck'\r\nimport { parseOssUrl } from '@/utils/file'\r\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\r\n\r\nimport { getConfigure } from '@/api/user'\r\nimport { baseUrl } from '@/utils/baseurl'\r\nimport { GetSteelCadAndBimId } from '@/api/PRO/component'\r\nimport { getBomName } from '@/views/PRO/bom-setting/utils'\r\n\r\nconst SPLIT_SYMBOL = '$_$'\r\nexport default {\r\n  components: { DynamicTableFields, TreeDetail, ExpandableSection, BatchProcessAdjust, AddDraft, Workshop, OwnerProcess },\r\n  data() {\r\n    return {\r\n      drawer: false,\r\n      drawersull: false,\r\n      iframeKey: '',\r\n      fullscreenid: '',\r\n      iframeUrl: '',\r\n      fullbimid: '',\r\n      fileBim: '',\r\n      IsUploadCad: false,\r\n      cadRowCode: '',\r\n      cadRowProjectId: '',\r\n      tbKey: 100,\r\n      isComponentOptions: [\r\n        { label: '是', value: false },\r\n        { label: '否', value: true }\r\n      ],\r\n      specOptions: [{ data: '' }],\r\n      filterTypeOption: [{ data: '' }],\r\n      filterCodeOption: [{ data: '' }],\r\n      projectOptions: [{ data: '' }],\r\n      areaOptions: [{ data: '' }],\r\n      installOptions: [{ data: '' }],\r\n      projectList: [],\r\n      installList: [],\r\n      areaList: [],\r\n      pickerOptions: {\r\n        disabledDate(time) {\r\n        }\r\n      },\r\n      innerForm: {\r\n        projectName: '',\r\n        areaName: '',\r\n        installName: '',\r\n        searchContent: '',\r\n        searchSpecSearch: '',\r\n        searchDirect: ''\r\n      },\r\n      curSearch: 1,\r\n      searchType: '',\r\n      formInline: {\r\n        Schduling_Code: '',\r\n        Create_UserName: '',\r\n        Finish_Date: '',\r\n        InstallUnit_Id: '',\r\n        Remark: ''\r\n      },\r\n      total: 0,\r\n      currentIds: '',\r\n      columns: [],\r\n      tbData: [],\r\n      tbConfig: {},\r\n      TotalCount: 0,\r\n      multipleSelection: [],\r\n      showExpand: true,\r\n      pgLoading: false,\r\n      deleteLoading: false,\r\n      workShopIsOpen: false,\r\n      isOwnerNull: false,\r\n      dialogVisible: false,\r\n      openAddDraft: false,\r\n      saveLoading: false,\r\n      tbLoading: false,\r\n      isCheckAll: false,\r\n      currentComponent: '',\r\n      gridCode: '',\r\n      dWidth: '25%',\r\n      title: '',\r\n      search: () => ({}),\r\n      pageType: undefined,\r\n      tipLabel: '',\r\n      technologyOption: [],\r\n      typeOption: [],\r\n      workingTeam: [],\r\n      pageStatus: undefined,\r\n      scheduleId: '',\r\n      partComOwnerColumn: null,\r\n\r\n      installUnitIdList: [],\r\n      projectId: '',\r\n      areaId: '',\r\n      projectName: '',\r\n      statusType: '',\r\n      expandedKey: '',\r\n      // treeLoading: false,\r\n      treeData: [],\r\n      treeParamsComponentType: {\r\n        'default-expand-all': true,\r\n        'check-strictly': true,\r\n        filterable: true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      },\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        collapseTags: true,\r\n        clearable: true\r\n      },\r\n      disabledAdd: true,\r\n      levelName: '',\r\n      projectOption: []\r\n    }\r\n  },\r\n  watch: {\r\n    'tbData.length': {\r\n      handler(n, o) {\r\n        this.checkOwner()\r\n        this.doFilter()\r\n      },\r\n      immediate: false\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    },\r\n    isView() {\r\n      return this.pageStatus === 'view'\r\n    },\r\n    isEdit() {\r\n      return this.pageStatus === 'edit'\r\n    },\r\n    isAdd() {\r\n      return this.pageStatus === 'add'\r\n    },\r\n    addDraftKey() {\r\n      return this.expandedKey + this.formInline.InstallUnit_Id\r\n    },\r\n    // filterText() {\r\n    //   return this.projectName + SPLIT_SYMBOL + this.statusType\r\n    // },\r\n    statusCode() {\r\n      return this.isCom ? 'Comp_Schdule_Status' : 'Part_Schdule_Status'\r\n    },\r\n    installName() {\r\n      const item = this.installUnitIdList.find(v => v.Id === this.formInline.InstallUnit_Id)\r\n      if (item) {\r\n        return item.Name\r\n      } else {\r\n        return ''\r\n      }\r\n    },\r\n    isPartPrepare() {\r\n      return this.getIsPartPrepare && !this.isCom\r\n    },\r\n    isNest() {\r\n      return false\r\n    },\r\n    isStopFlag() {\r\n      return this.tbData.some(item => item.stopFlag)\r\n    },\r\n    ...mapGetters('factoryInfo', ['workshopEnabled', 'getIsPartPrepare']),\r\n    ...mapGetters('schedule', ['processList', 'nestIds'])\r\n  },\r\n  async created() {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      // this.baseCadUrl = 'http://localhost:9529'\r\n      // this.baseCadUrl = 'http://glendale-model.bimtk.com'\r\n      this.baseCadUrl = 'http://glendale-model-dev.bimtk.tech'\r\n    } else {\r\n      getConfigure({ code: 'glendale_url' }).then((res) => {\r\n        this.baseCadUrl = res.Data\r\n      })\r\n    }\r\n  },\r\n  async mounted() {\r\n    this.initProcessList()\r\n    this.tbDataMap = {}\r\n    this.craftCodeMap = {}\r\n    this.pageType = this.$route.query.pg_type\r\n    this.level = this.$route.query.level\r\n    this.pageStatus = this.$route.query.status\r\n    this.model = this.$route.query.model\r\n    this.scheduleId = this.$route.query.pid || ''\r\n    // // this.formInline.Create_UserName = this.$store.getters.name\r\n    // // 框架问题引起store数据丢失，已反馈，结果：此处先使用localStorage\r\n    this.formInline.Create_UserName = localStorage.getItem('UserAccount')\r\n    // if (!this.isCom) {\r\n    //   this.getPartType()\r\n    // } else {\r\n    // }\r\n\r\n    this.unique = uniqueCode()\r\n    this.checkWorkshopIsOpen()\r\n\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    await this.mergeConfig()\r\n    if (this.isView || this.isEdit) {\r\n      const { areaId, install } = this.$route.query\r\n      // this.areaId = areaId\r\n      // this.formInline.InstallUnit_Id = install\r\n      // this.getInstallUnitIdNameList()\r\n      this.fetchData()\r\n    }\r\n\r\n    if (this.isAdd) {\r\n      // this.fetchTreeData()\r\n      this.getType()\r\n    }\r\n    if (this.isEdit) {\r\n      this.getType()\r\n    }\r\n\r\n    this.levelName = await getBomName(this.level)\r\n\r\n    window.addEventListener('message', this.frameListener)\r\n    this.$once('hook:beforeDestroy', () => {\r\n      console.log('deactivated')\r\n      window.removeEventListener('message', this.frameListener)\r\n    })\r\n  },\r\n  activated() {\r\n    window.addEventListener('message', this.frameListener)\r\n    this.$once('hook:deactivated', () => {\r\n      window.removeEventListener('message', this.frameListener)\r\n    })\r\n  },\r\n  methods: {\r\n    ...mapActions('schedule', ['changeProcessList', 'initProcessList', 'changeAddTbKeys']),\r\n    checkOwner() {\r\n      if (this.isCom) return\r\n      this.isOwnerNull = this.tbData.every(v => !v.Comp_Import_Detail_Id) && !this.isNest\r\n      const idx = this.columns.findIndex(v => v.Code === 'Part_Used_Process')\r\n      if (this.isOwnerNull) {\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n      } else {\r\n        if (idx === -1) {\r\n          if (!this.ownerColumn) {\r\n            this.$message({\r\n              message: '列表配置字段缺少部件领用工序字段',\r\n              type: 'success'\r\n            })\r\n            return\r\n          }\r\n          this.columns.push(this.ownerColumn)\r\n        }\r\n        this.comPart = true\r\n      }\r\n    },\r\n    async mergeConfig() {\r\n      await this.getConfig()\r\n      await this.getWorkTeam()\r\n    },\r\n    doFilter() {\r\n      this.projectList = []\r\n      this.installList = []\r\n      this.areaList = []\r\n      this.tbData.forEach(cur => {\r\n        if (cur.Project_Name && !this.projectList.includes(cur.Project_Name)) {\r\n          this.projectList.push(cur.Project_Name)\r\n        }\r\n        if (cur.InstallUnit_Name && !this.installList.includes(cur.InstallUnit_Name)) {\r\n          this.installList.push(cur.InstallUnit_Name)\r\n        }\r\n        if (cur.Area_Name && !this.areaList.includes(cur.Area_Name)) {\r\n          this.areaList.push(cur.Area_Name)\r\n        }\r\n      })\r\n    },\r\n    async getConfig() {\r\n      let configCode = ''\r\n      if (this.isNest) {\r\n        if (this.isView) {\r\n          configCode = 'PRONestingScheduleDetail'\r\n        } else {\r\n          configCode = 'PRONestingScheduleConfig'\r\n        }\r\n      } else {\r\n        configCode = (this.isView ? 'PROUnitPartViewPageTbConfig_new' : 'PROUnitPartDraftPageTbConfig_new')\r\n      }\r\n      this.gridCode = configCode\r\n      await this.getTableConfig(configCode)\r\n      if (!this.workshopEnabled) {\r\n        this.columns = this.columns.filter(v => v.Code !== 'Workshop_Name')\r\n      }\r\n      this.checkOwner()\r\n    },\r\n    async changeColumn() {\r\n      await this.getTableConfig(this.gridCode)\r\n      this.tbKey++\r\n    },\r\n    /*    handleNodeClick(data) {\r\n      console.log('data', data)\r\n      if (this.areaId === data.Id) {\r\n        return\r\n      }\r\n      this.\r\n       = true\r\n      if (!data.ParentNodes || data.Children?.length > 0) {\r\n        return\r\n      }\r\n      if (data?.Data[this.statusCode] === '未导入') {\r\n        this.$message({\r\n          message: '清单未导入，请联系深化人员导入清单',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n\r\n      const initData = ({ Data }) => {\r\n        this.areaId = Data.Id\r\n        this.projectId = Data.Project_Id\r\n        this.expandedKey = this.areaId\r\n        this.formInline.Finish_Date = ''\r\n        this.formInline.InstallUnit_Id = ''\r\n        this.formInline.Remark = ''\r\n        this.tbData = []\r\n        this.getAreaInfo()\r\n        this.getInstallUnitIdNameList()\r\n      }\r\n\r\n      if (this.tbData.length) {\r\n        this.$confirm('切换区域右侧数据清空，是否确认?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          initData(data)\r\n          this.disabledAdd = false\r\n          this.tbDataMap = {}\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n      } else {\r\n        this.disabledAdd = false\r\n        initData(data)\r\n      }\r\n    },*/\r\n\r\n    /* customFilterFun(value, data, node) {\r\n      const arr = value.split(SPLIT_SYMBOL)\r\n      const labelVal = arr[0]\r\n      const statusVal = arr[1]\r\n      if (!value) return true\r\n      let parentNode = node.parent\r\n      let labels = [node.label]\r\n      let status = [data.Data[this.statusCode]]\r\n      let level = 1\r\n      while (level < node.level) {\r\n        labels = [...labels, parentNode.label]\r\n        status = [...status, data.Data[this.statusCode]]\r\n        parentNode = parentNode.parent\r\n        level++\r\n      }\r\n      labels = labels.filter(v => !!v)\r\n      status = status.filter(v => !!v)\r\n      let resultLabel = true\r\n      let resultStatus = true\r\n      if (this.statusType) {\r\n        resultStatus = status.some(s => s.indexOf(statusVal) !== -1)\r\n      }\r\n      if (this.projectName) {\r\n        resultLabel = labels.some(s => s.indexOf(labelVal) !== -1)\r\n      }\r\n      return resultLabel && resultStatus\r\n    },*/\r\n    async fetchData() {\r\n      this.tbLoading = true\r\n      let resData = null\r\n      if (this.isNest) {\r\n        if (this.isView) {\r\n          resData = await this.getPartPageList()\r\n        } else {\r\n          resData = await this.getNestPageList()\r\n        }\r\n      } else {\r\n        resData = await this.getPartPageList()\r\n      }\r\n\r\n      this.initTbData(resData)\r\n      this.tbLoading = false\r\n    },\r\n    fetchTreeDataLocal() {\r\n      // this.filterText = this.projectName\r\n    },\r\n    fetchTreeStatus() {\r\n      // this.filterText = this.statusType\r\n    },\r\n    /*    fetchTreeData() {\r\n      this.treeLoading = true\r\n      GetProjectAreaTreeList({ projectName: this.projectName, type: this.isCom ? 1 : 2 }).then((res) => {\r\n        if (res.Data.length === 0) {\r\n          this.treeLoading = false\r\n          return\r\n        }\r\n        const resData = res.Data.map(item => {\r\n          item.Is_Directory = true\r\n          return item\r\n        })\r\n        this.treeData = resData\r\n        this.setKey()\r\n        this.treeLoading = false\r\n      })\r\n    },*/\r\n    // setKey() {\r\n    //   const deepFilter = (tree) => {\r\n    //     for (let i = 0; i < tree.length; i++) {\r\n    //       const item = tree[i]\r\n    //       const { Data, Children } = item\r\n    //       console.log(Data)\r\n    //       if (Data.ParentId && !Children?.length) {\r\n    //         this.handleNodeClick(item)\r\n    //         return\r\n    //       } else {\r\n    //         if (Children && Children.length > 0) {\r\n    //           return deepFilter(Children)\r\n    //         }\r\n    //       }\r\n    //     }\r\n    //   }\r\n    //   return deepFilter(this.treeData)\r\n    // },\r\n    closeView() {\r\n      closeTagView(this.$store, this.$route)\r\n    },\r\n    checkWorkshopIsOpen() {\r\n      this.workShopIsOpen = true\r\n    },\r\n    tbSelectChange(array) {\r\n      this.multipleSelection = array.records\r\n    },\r\n    getAreaInfo() {\r\n      this.formInline.Finish_Date = ''\r\n      AreaGetEntity({\r\n        id: this.areaId\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (!res.Data) {\r\n            return []\r\n          }\r\n\r\n          const start = moment(res.Data?.Demand_Begin_Date)\r\n          const end = moment(res.Data?.Demand_End_Date)\r\n          this.pickerOptions.disabledDate = (time) => {\r\n            return time.getTime() < start || time.getTime() > end\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n      this.openAddDraft = false\r\n    },\r\n    getNestPageList() {\r\n      return new Promise((resolve, reject) => {\r\n        GetCanSchdulingPartList({\r\n          Ids: this.nestIds\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            const _list = res?.Data || []\r\n            const list = _list.map(v => {\r\n              v.Part_Used_Process = v.Scheduled_Used_Process || v.Part_Type_Used_Process\r\n              // v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n              v.Workshop_Id = v.Scheduled_Workshop_Id\r\n              v.Workshop_Name = v.Scheduled_Workshop_Name\r\n              v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n              v.chooseCount = v.Can_Schduling_Count\r\n\r\n              return v\r\n            })\r\n\r\n            resolve(list)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    async getPartPageList() {\r\n      const {\r\n        pid\r\n      } = this.$route.query\r\n      const result = await GetUnitSchdulingInfoDetail({\r\n        Schduling_Plan_Id: pid\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const SarePartsModel = res.Data?.SarePartsModel.map(v => {\r\n            if (v.Scheduled_Used_Process) {\r\n              // 已存在操作过数据\r\n              v.Part_Used_Process = v.Scheduled_Used_Process\r\n            } if (v.Unit_Part_Used_Process) {\r\n              // 已存在操作过数据\r\n              v.Part_Used_Process = v.Unit_Part_Used_Process\r\n            }\r\n            v.chooseCount = v.Can_Schduling_Count\r\n            return v\r\n          })\r\n          this.formInline = Object.assign(this.formInline, res.Data?.Schduling_Plan)\r\n          res.Data?.Process_List.forEach(item => {\r\n            const plist = {\r\n              key: item.Process_Code,\r\n              value: item\r\n            }\r\n            this.changeProcessList(plist)\r\n          })\r\n\r\n          this.getStopList(SarePartsModel)\r\n          return SarePartsModel\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          return []\r\n        }\r\n      })\r\n      console.log('result', result)\r\n      return result || []\r\n    },\r\n    async getStopList(list) {\r\n      console.log('getStopList', list)\r\n      const submitObj = list.map(item => {\r\n        return {\r\n          Id: item.Part_Aggregate_Id,\r\n          Type: 3\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = !!item.Is_Stop\r\n          })\r\n          list.forEach(row => {\r\n            if (stopMap[row.Part_Aggregate_Id]) {\r\n              this.$set(row, 'stopFlag', stopMap[row.Part_Aggregate_Id])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    initTbData(list, teamKey = 'Allocation_Teams') {\r\n      console.log(5, JSON.parse(JSON.stringify(list)))\r\n      this.tbData = list.map(row => {\r\n        const processList = row.Technology_Path?.split('/') || []\r\n        row.uuid = uuidv4()\r\n        this.addElementToTbData(row)\r\n        if (row[teamKey]) {\r\n          const newData = row[teamKey].filter((r) => processList.findIndex((p) => r.Process_Code === p) !== -1)\r\n          newData.forEach((ele, index) => {\r\n            const code = this.getRowUnique(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n            const max = this.getRowUniqueMax(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n            row[code] = ele.Count\r\n            row[max] = 0\r\n          })\r\n        }\r\n        this.setInputMax(row)\r\n        return row\r\n      })\r\n      let ids = ''\r\n      if (this.isCom) {\r\n        ids = this.tbData.map(v => v.Comp_Import_Detail_Id).toString()\r\n      } else {\r\n        ids = this.tbData.map(v => v.Part_Aggregate_Id).toString()\r\n      }\r\n      this.currentIds = ids\r\n    },\r\n    async addToTbList(newList) {\r\n      await this.mergeSelectList(newList)\r\n      this.setAddTbKey()\r\n    },\r\n    async mergeSelectList(newList) {\r\n      console.time('fff')\r\n      await this.mergeCraftProcess(newList)\r\n      let hasUsedPartFlag = true\r\n      newList.forEach((element, index) => {\r\n        const cur = this.getMergeUniqueRow(element)\r\n        console.log(1, JSON.parse(JSON.stringify(element)))\r\n        if (!element.Technology_Path) {\r\n          const curPathArr = this.craftCodeMap[element.Technology_Code]\r\n          if (this.craftCodeMap[element.Technology_Code] instanceof Array) {\r\n            if (element.Unit_Part_Used_Process) {\r\n              const partUsedProcessArr = element.Unit_Part_Used_Process.split(',')\r\n              const allPartsIncluded = partUsedProcessArr.every(part => curPathArr.includes(part))\r\n\r\n              if (!allPartsIncluded) {\r\n                hasUsedPartFlag = false\r\n              } else {\r\n                element.Technology_Path = curPathArr.join('/')\r\n              }\r\n            } else {\r\n              element.Technology_Path = curPathArr.join('/')\r\n            }\r\n          }\r\n        }\r\n        if (!cur) {\r\n          element.puuid = element.uuid\r\n          element.Schduled_Count = element.chooseCount\r\n          element.Schduled_Weight = numeral(element.chooseCount * element.Weight).format('0.[00]')\r\n          this.tbData.push(element)\r\n          this.addElementToTbData(element)\r\n          return\r\n        }\r\n\r\n        cur.puuid = element.uuid\r\n\r\n        cur.Schduled_Count += element.chooseCount\r\n        cur.Schduled_Weight = numeral(cur.Schduled_Weight).add(element.chooseCount * element.Weight).format('0.[00]')\r\n        if (!cur.Technology_Path) {\r\n          return\r\n        }\r\n        this.setInputMax(cur)\r\n      })\r\n      console.log('hasUsedPartFlag', hasUsedPartFlag)\r\n      this.showCraftUsedPartResult(hasUsedPartFlag)\r\n      // if (this.isCom) {\r\n      //   this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n      // } else {\r\n      //   this.tbData.sort((a, b) => a.Part_Code.localeCompare(b.Part_Code))\r\n      // }\r\n      this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n      console.timeEnd('fff')\r\n      console.log('this.tbDataMap', this.tbDataMap, this.tbData)\r\n    },\r\n    showCraftUsedPartResult(hasUsedPart) {\r\n      if (hasUsedPart) return true\r\n      setTimeout(() => {\r\n        this.$alert('部分部件工序路径内不包含零件领用工序请手动排产', '提示', {\r\n          confirmButtonText: '确定'\r\n        })\r\n      }, 200)\r\n      return false\r\n    },\r\n    addElementToTbData(element) {\r\n      const key = this.getUniKey(element)\r\n      this.tbDataMap[key] = element\r\n    },\r\n    getMergeUniqueRow(element) {\r\n      const key = this.getUniKey(element)\r\n      return this.tbDataMap[key]\r\n    },\r\n    getUniKey(element) {\r\n      return getUnique(this.isCom, element)\r\n    },\r\n    checkForm() {\r\n      let isValidate = true\r\n      this.$refs['formInline'].validate((valid) => {\r\n        if (!valid) isValidate = false\r\n      })\r\n      return isValidate\r\n    },\r\n    async saveDraft(isOrder = false) {\r\n      const checkSuccess = this.checkForm()\r\n      if (!checkSuccess) return false\r\n      const { tableData, status } = this.getSubmitTbInfo()\r\n      if (!status) return false\r\n      if (!isOrder) {\r\n        this.saveLoading = true\r\n      }\r\n\r\n      const isSuccess = await this.handleSaveDraft(tableData, isOrder)\r\n      console.log('isSuccess', isSuccess)\r\n      if (!isSuccess) return false\r\n      if (isOrder) return isSuccess\r\n      this.$refs['draft']?.fetchData()\r\n      this.saveLoading = false\r\n    },\r\n    async saveWorkShop() {\r\n      const checkSuccess = this.checkForm()\r\n      if (!checkSuccess) return false\r\n      const obj = {}\r\n      if (!this.tbData.length) {\r\n        this.$message({\r\n          message: '数据不能为空',\r\n          type: 'success'\r\n        })\r\n        return\r\n      }\r\n      if (this.isCom) {\r\n        obj.Schduling_Comps = this.tbData\r\n      } else {\r\n        obj.SarePartsModel = this.tbData\r\n      }\r\n      if (this.isEdit) {\r\n        obj.Schduling_Plan = this.formInline\r\n      } else {\r\n        obj.Schduling_Plan = {\r\n          ...this.formInline,\r\n          Project_Id: this.projectId,\r\n          Area_Id: this.areaId,\r\n          Schduling_Model: this.model // 1构件单独排产，2零件单独排产，3构/零件一起排产\r\n        }\r\n      }\r\n      this.pgLoading = true\r\n\r\n      SaveUnitSchedulingWorkshopNew(obj).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.pgLoading = false\r\n          this.$message({\r\n            message: '保存成功',\r\n            type: 'success'\r\n          })\r\n          this.closeView()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.pgLoading = false\r\n        }\r\n      })\r\n    },\r\n    getSubmitTbInfo() {\r\n      // 处理上传的数据\r\n      let tableData = JSON.parse(JSON.stringify(this.tbData))\r\n      tableData = tableData.filter(item => item.Schduled_Count > 0)\r\n      for (let i = 0; i < tableData.length; i++) {\r\n        const element = tableData[i]\r\n        let list = []\r\n        if (!element.Technology_Path) {\r\n          this.$message({\r\n            message: '工序不能为空',\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        if (this.isPartPrepare && !element.Part_Used_Process && element.Type !== 'Direct' && this.comPart) {\r\n          const msg = '领用工序不能为空'\r\n          if (this.isNest) {\r\n            if (element.Comp_Import_Detail_Id) {\r\n              this.$message({\r\n                message: msg,\r\n                type: 'warning'\r\n              })\r\n              return { status: false }\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: msg,\r\n              type: 'warning'\r\n            })\r\n            return { status: false }\r\n          }\r\n        }\r\n        // if (!this.isCom && element.Comp_Import_Detail_Id && !element.Part_Used_Process) {\r\n        //   // 零构件 零件单独排产\r\n        //   this.$message({\r\n        //     message: '零件领用工序不能为空',\r\n        //     type: 'warning'\r\n        //   })\r\n        //   return { status: false }\r\n        // }\r\n        if (element.Scheduled_Technology_Path && element.Scheduled_Technology_Path !== element.Technology_Path) {\r\n          this.$message({\r\n            message: `请和该区域批次下已排产同${this.isCom ? '构件' : '部件'}保持工序一致`,\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        if (element.Scheduled_Used_Process && element.Scheduled_Used_Process !== element.Part_Used_Process) {\r\n          this.$message({\r\n            message: `请和该区域批次下已排产同部件领用工序保持一致`,\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        const processList = Array.from(new Set(element.Technology_Path.split('/')))\r\n        // processList.forEach(code => {\r\n        //   const groups = element.Allocation_Teams.filter(v => v.Process_Code === code)\r\n        //    const groupsList = groups.map(group => {\r\n        //   const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)\r\n        //   const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)\r\n        //   const obj = {\r\n        //     Team_Task_Id: element.Team_Task_Id,\r\n        //     Comp_Code: element.Comp_Code,\r\n        //     Again_Count: +element[uCode] || 0, // 不填，后台让传0\r\n        //     Part_Code: this.isCom ? null : '',\r\n        //     Process_Code: code,\r\n        //     Technology_Path: element.Technology_Path,\r\n        //     Working_Team_Id: group.Working_Team_Id,\r\n        //     Working_Team_Name: group.Working_Team_Name\r\n        //   }\r\n        //   delete element[uCode]\r\n        //   delete element[uMax]\r\n        //   return obj\r\n        // })\r\n        // const againCount = list.reduce((acc, cur) => {\r\n        //   return acc + cur.Again_Count\r\n        // }, 0)\r\n        // if (againCount > element.Schduled_Count) {\r\n        //   element.Allocation_Teams = []\r\n        // }\r\n\r\n        for (let j = 0; j < processList.length; j++) {\r\n          const code = processList[j]\r\n          const schduledCount = element.Schduled_Count || 0\r\n          let groups = []\r\n          if (element.Allocation_Teams) {\r\n            groups = element.Allocation_Teams.filter(v => v.Process_Code === code)\r\n          }\r\n          const againCount = groups.reduce((acc, cur) => {\r\n            return acc + (cur.Again_Count || 0)\r\n          }, 0)\r\n          if (againCount > schduledCount) {\r\n            list = []\r\n            break\r\n          } else {\r\n            list.push(...groups)\r\n          }\r\n        }\r\n\r\n        const hasInput = Object.keys(element).filter(_ => _.startsWith(element['uuid']))\r\n        hasInput.forEach((item) => {\r\n          delete element[item]\r\n        })\r\n        delete element['uuid']\r\n        delete element['_X_ROW_KEY']\r\n        delete element['puuid']\r\n        element.Allocation_Teams = list\r\n      }\r\n      return { tableData, status: true }\r\n    },\r\n    async handleSaveDraft(tableData, isOrder) {\r\n      console.log('保存草稿')\r\n      const _fun = SaveUnitSchdulingDraftNew\r\n      const obj = {}\r\n      obj.SarePartsModel = tableData\r\n      const p = []\r\n      for (const objKey in this.processList) {\r\n        if (this.processList.hasOwnProperty(objKey)) {\r\n          p.push(this.processList[objKey])\r\n        }\r\n      }\r\n      obj.Process_List = p\r\n\r\n      if (this.isEdit) {\r\n        obj.Schduling_Plan = this.formInline\r\n      } else {\r\n        obj.Schduling_Plan = {\r\n          ...this.formInline,\r\n          Project_Id: this.projectId,\r\n          Area_Id: this.areaId,\r\n          Schduling_Model: this.model // 1构件单独排产，2零件单独排产，3构/零件一起排产\r\n        }\r\n      }\r\n      let orderSuccess = false\r\n      console.log('obj', obj)\r\n\r\n      await _fun(obj).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (!isOrder) {\r\n            this.pgLoading = false\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            this.closeView()\r\n          } else {\r\n            this.templateScheduleCode = res.Data\r\n            orderSuccess = true\r\n            console.log('保存草稿成功 ')\r\n          }\r\n        } else {\r\n          this.saveLoading = false\r\n          this.pgLoading = false\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      console.log('结束 ')\r\n      return orderSuccess\r\n    },\r\n    handleDelete() {\r\n      this.deleteLoading = true\r\n      setTimeout(() => {\r\n        const selectedUuids = new Set(this.multipleSelection.map(v => v.uuid))\r\n        this.tbData = this.tbData.filter(item => {\r\n          const isSelected = selectedUuids.has(item.uuid)\r\n          if (isSelected) {\r\n            const key = this.getUniKey(item)\r\n            delete this.tbDataMap[key]\r\n          }\r\n          return !isSelected\r\n        })\r\n        // this.$nextTick(_ => {\r\n        //   const _list = this.multipleSelection.filter(v => v.puuid)\r\n        //   this.$refs['draft']?.mergeData(_list)\r\n        //   this.multipleSelection = []\r\n        // })\r\n        this.deleteLoading = false\r\n      }, 0)\r\n    },\r\n    async getWorkTeam() {\r\n      await GetSchdulingWorkingTeams({\r\n        type: 3\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.workingTeam = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      this.$refs['formInline'].validate((valid) => {\r\n        if (!valid) return\r\n        const { tableData, status } = this.getSubmitTbInfo()\r\n        if (!status) return\r\n        this.$confirm('是否提交当前数据?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.saveDraftDoSubmit(tableData)\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n      })\r\n    },\r\n    async saveDraftDoSubmit() {\r\n      this.pgLoading = true\r\n      if (this.formInline?.Schduling_Code) {\r\n        const isSuccess = await this.saveDraft(true)\r\n        console.log('saveDraftDoSubmit', isSuccess)\r\n        isSuccess && this.doSubmit(this.formInline.Id)\r\n      } else {\r\n        const isSuccess = await this.saveDraft(true)\r\n        isSuccess && this.doSubmit(this.templateScheduleCode)\r\n      }\r\n    },\r\n    doSubmit(scheduleCode) {\r\n      SaveSchdulingTaskById({\r\n        schdulingPlanId: scheduleCode\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '下达成功',\r\n            type: 'success'\r\n          })\r\n          this.closeView()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.pgLoading = false\r\n      }).catch(_ => {\r\n        this.pgLoading = false\r\n      })\r\n    },\r\n    getWorkShop(value) {\r\n      console.log('value', value)\r\n      const {\r\n        origin,\r\n        row,\r\n        workShop: {\r\n          Id,\r\n          Display_Name\r\n        }\r\n      } = value\r\n      if (origin === 2) {\r\n        if (value.workShop?.Id) {\r\n          row.Workshop_Name = Display_Name\r\n          row.Workshop_Id = Id\r\n          this.setPath(row, Id)\r\n        } else {\r\n          row.Workshop_Name = ''\r\n          row.Workshop_Id = ''\r\n        }\r\n      } else {\r\n        this.multipleSelection.forEach(item => {\r\n          if (value.workShop?.Id) {\r\n            item.Workshop_Name = Display_Name\r\n            item.Workshop_Id = Id\r\n            this.setPath(item, Id)\r\n          } else {\r\n            item.Workshop_Name = ''\r\n            item.Workshop_Id = ''\r\n          }\r\n        })\r\n      }\r\n    },\r\n    setPath(row, Id) {\r\n      if (row?.Scheduled_Workshop_Id) {\r\n        if (row.Scheduled_Workshop_Id !== Id) {\r\n          row.Technology_Path = ''\r\n        }\r\n      } else {\r\n        row.Technology_Path = ''\r\n      }\r\n    },\r\n    handleBatchWorkshop(origin, row) {\r\n      this.title = origin === 1 ? '批量分配车间' : '分配车间'\r\n      this.currentComponent = 'Workshop'\r\n      this.dWidth = '30%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].fetchData(origin, row)\r\n      })\r\n    },\r\n    getProcessOption(workshopId) {\r\n      return new Promise((resolve, reject) => {\r\n        GetProcessListBase({\r\n          workshopId: workshopId,\r\n          type: 3\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            const process = res.Data.map(v => v.Code)\r\n            resolve(process)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    setLibType(code, workshopId) {\r\n      return new Promise((resolve) => {\r\n        const obj = {\r\n          Component_type: code,\r\n          type: 1\r\n        }\r\n        if (this.workshopEnabled) {\r\n          obj.workshopId = workshopId\r\n        }\r\n        GetLibListType(obj).then(res => {\r\n          if (res.IsSucceed) {\r\n            if (res.Data.Data && res.Data.Data.length) {\r\n              const info = res.Data.Data[0]\r\n              const workCode = info.WorkCode && info.WorkCode.replace(/\\\\/g, '/')\r\n              resolve(workCode)\r\n            } else {\r\n              resolve(undefined)\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    inputChange(row) {\r\n      this.setInputMax(row)\r\n    },\r\n    setInputMax(row) {\r\n      const inputValuesKeys = Object.keys(row)\r\n        .filter(v => !v.endsWith('max') && v.startsWith(row.uuid) && v.length > row.uuid.length)\r\n      inputValuesKeys.forEach((val) => {\r\n        const curCode = val.split(SPLIT_SYMBOL)[1]\r\n        const otherTotal = inputValuesKeys.filter(x => {\r\n          const code = x.split(SPLIT_SYMBOL)[1]\r\n          return x !== val && code === curCode\r\n        }).reduce((acc, item) => {\r\n          return acc + numeral(row[item]).value()\r\n        }, 0)\r\n        row[val + SPLIT_SYMBOL + 'max'] = row.Schduled_Count - otherTotal\r\n      })\r\n    },\r\n    sendProcess({ arr, str }) {\r\n      let isSuccess = true\r\n      for (let i = 0; i < arr.length; i++) {\r\n        const item = arr[i]\r\n        if (item.originalPath && item.originalPath !== str) {\r\n          isSuccess = false\r\n          break\r\n        }\r\n        item.Technology_Path = str\r\n      }\r\n      if (!isSuccess) {\r\n        this.$message({\r\n          message: '请和该区域批次下已排产同部件保持工序一致',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    resetWorkTeamMax(row, str) {\r\n      if (str) {\r\n        row.Technology_Path = str\r\n      } else {\r\n        str = row.Technology_Path\r\n      }\r\n      const list = str?.split('/') || []\r\n      this.workingTeam.forEach((element, idx) => {\r\n        const cur = list.some(k => k === element.Process_Code)\r\n        const code = this.getRowUnique(row.uuid, element.Process_Code, element.Working_Team_Id)\r\n        const max = this.getRowUniqueMax(row.uuid, element.Process_Code, element.Working_Team_Id)\r\n        if (cur) {\r\n          if (!row[code]) {\r\n            this.$set(row, code, 0)\r\n            this.$set(row, max, row.Schduled_Count)\r\n          }\r\n        } else {\r\n          this.$delete(row, code)\r\n          this.$delete(row, max)\r\n        }\r\n      })\r\n    },\r\n    checkPermissionTeam(processStr, processCode) {\r\n      if (!processStr) return false\r\n      const list = processStr?.split('/') || []\r\n      return !!list.some(v => v === processCode)\r\n    },\r\n\r\n    async getTableConfig(code) {\r\n      await GetGridByCode({\r\n        code\r\n      }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n          const list = Data.ColumnList || []\r\n          this.ownerColumn = list.find(item => item.Code === 'Part_Used_Process')\r\n          this.ownerColumn2 = list.find(item => item.Code === 'Is_Main_Part')\r\n          this.columns = this.setColumnDisplay(list)\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setColumnDisplay(list) {\r\n      return list.filter(v => v.Is_Display)\r\n      // .map(item => {\r\n      //   if (FIX_COLUMN.includes(item.Code)) {\r\n      //     item.fixed = 'left'\r\n      //   }\r\n      //   return item\r\n      // })\r\n    },\r\n    activeCellMethod({ row, column, columnIndex }) {\r\n      if (this.isView) return false\r\n      const processCode = column.field?.split('$_$')[1]\r\n      return this.checkPermissionTeam(row.Technology_Path, processCode)\r\n    },\r\n    openBPADialog(type, row) {\r\n      if (this.workshopEnabled) {\r\n        if (type === 1) {\r\n          const IsUnique = this.checkIsUniqueWorkshop()\r\n          if (!IsUnique) return\r\n        }\r\n      }\r\n      this.title = type === 2 ? '工序调整' : '批量工序调整'\r\n      this.currentComponent = 'BatchProcessAdjust'\r\n      this.dWidth = '50%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].setData(type === 2 ? [row] : this.multipleSelection, type === 2 ? row.Technology_Path : '')\r\n      })\r\n    },\r\n    checkIsUniqueWorkshop() {\r\n      let isUnique = true\r\n      const firstV = this.multipleSelection[0].Workshop_Name\r\n      for (let i = 1; i < this.multipleSelection.length; i++) {\r\n        const item = this.multipleSelection[i]\r\n        if (item.Workshop_Name !== firstV) {\r\n          isUnique = false\r\n          break\r\n        }\r\n      }\r\n      if (!isUnique) {\r\n        this.$message({\r\n          message: '批量分配工序时只有相同车间下的才可一起批量分配',\r\n          type: 'warning'\r\n        })\r\n      }\r\n      return isUnique\r\n    },\r\n    checkHasWorkShop(type, arr) {\r\n      let hasWorkShop = true\r\n      for (let i = 0; i < arr.length; i++) {\r\n        const item = arr[i]\r\n        if (!item.Workshop_Name) {\r\n          hasWorkShop = false\r\n          break\r\n        }\r\n      }\r\n      if (!hasWorkShop) {\r\n        this.$message({\r\n          message: '请先选择车间后再进行工序分配',\r\n          type: 'warning'\r\n        })\r\n      }\r\n      return hasWorkShop\r\n    },\r\n    handleAddDialog(type = 'add') {\r\n      this.title = '添加部件'\r\n\r\n      this.currentComponent = 'AddDraft'\r\n      this.dWidth = '96%'\r\n      this.openAddDraft = true\r\n\r\n      this.setAddTbKey()\r\n\r\n      this.$nextTick(_ => {\r\n        this.$refs['draft'].initData()\r\n      })\r\n    },\r\n    setAddTbKey() {\r\n      const selectKeys = this.tbData.filter(cur => cur.puuid).map(v => v.puuid)\r\n      this.changeAddTbKeys(selectKeys)\r\n    },\r\n    getRowUnique(uuid, processCode, workingId) {\r\n      return `${uuid}${SPLIT_SYMBOL}${processCode}${SPLIT_SYMBOL}${workingId}`\r\n    },\r\n    getRowUniqueMax(uuid, processCode, workingId) {\r\n      return this.getRowUnique(uuid, processCode, workingId) + `${SPLIT_SYMBOL}max`\r\n    },\r\n    handleSelectMenu(v) {\r\n      if (v === 'process') {\r\n        this.openBPADialog(1)\r\n      } else if (v === 'craft') {\r\n        this.handleSetCraftProcess()\r\n      }\r\n    },\r\n    async mergeCraftProcess(list) {\r\n      let codes = [...new Set(list.map(v => v.Technology_Code))]\r\n      for (const key in this.craftCodeMap) {\r\n        if (this.craftCodeMap.hasOwnProperty(key)) {\r\n          codes = codes.filter(code => code !== key)\r\n        }\r\n      }\r\n      const _craftCodeMap = await this.getCraftProcess(codes)\r\n      Object.assign(this.craftCodeMap, _craftCodeMap)\r\n    },\r\n    async handleSetCraftProcess() {\r\n      const showSuccess = () => {\r\n        this.$message({\r\n          message: '已分配成功',\r\n          type: 'success'\r\n        })\r\n      }\r\n      const rowList = this.multipleSelection.map(v => v.Technology_Code).filter(v => !!v)\r\n      if (!rowList.length) {\r\n        this.$message({\r\n          message: '工艺代码不存在',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      await this.mergeCraftProcess(this.multipleSelection)\r\n      const workshopIds = Array.from(new Set(this.multipleSelection.map(v => v.Workshop_Id).filter(v => !!v)))\r\n      const w_process = []\r\n      if (workshopIds.length) {\r\n        workshopIds.forEach(workshopId => {\r\n          w_process.push(this.getProcessOption(workshopId).then(result => ({\r\n            [workshopId]: result\r\n          })))\r\n        })\r\n        const workshopPromise = Promise.all(w_process).then((values) => {\r\n          return Object.assign({}, ...values)\r\n        })\r\n        workshopPromise.then(workshop => {\r\n          let flag = true\r\n          for (let i = 0; i < this.multipleSelection.length; i++) {\r\n            const curRow = this.multipleSelection[i]\r\n            const workshopProcess = workshop[curRow.Workshop_Id]\r\n            const craftArray = this.craftCodeMap[curRow.Technology_Code]\r\n            if (craftArray) {\r\n              const isIncluded = craftArray.every(process => workshopProcess.includes(process))\r\n              if (!isIncluded) {\r\n                flag = false\r\n                continue\r\n              }\r\n              curRow.Technology_Path = craftArray.join('/')\r\n            }\r\n          }\r\n          if (!flag) {\r\n            setTimeout(() => {\r\n              this.$alert('所选车间下班组加工工序不包含工艺代码工序请手动排产', '提示', {\r\n                confirmButtonText: '确定'\r\n              })\r\n            }, 200)\r\n          }\r\n\r\n          flag && showSuccess()\r\n        })\r\n      } else {\r\n        this.multipleSelection.forEach((curRow) => {\r\n          const craftArray = this.craftCodeMap[curRow.Technology_Code]\r\n          if (craftArray) {\r\n            curRow.Technology_Path = craftArray.join('/')\r\n          }\r\n        })\r\n        showSuccess()\r\n      }\r\n    },\r\n    handleBatchOwner(type, row) {\r\n      this.title = '批量分配领用工序'\r\n      this.currentComponent = 'OwnerProcess'\r\n      this.dWidth = '30%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].setOption(type === 2, type === 2 ? [row] : this.multipleSelection)\r\n      })\r\n    },\r\n    getCraftProcess(gyGroup = []) {\r\n      gyGroup = gyGroup.filter(v => !!v)\r\n      if (!gyGroup.length) return Promise.resolve({})\r\n      return new Promise((resolve, reject) => {\r\n        GetProcessFlowListWithTechnology({\r\n          TechnologyCodes: gyGroup,\r\n          Type: 3\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            const gyList = res.Data || []\r\n            const gyMap = gyList.reduce((acc, item) => {\r\n              acc[item.Code] = item.Technology_Path\r\n              return acc\r\n            }, {})\r\n            console.log('gyMap', gyMap)\r\n            resolve(gyMap)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    handleReverse() {\r\n      const cur = []\r\n      this.tbData.forEach((element, idx) => {\r\n        element.checked = !element.checked\r\n        if (element.checked) {\r\n          cur.push(element)\r\n        }\r\n      })\r\n      this.multipleSelection = cur\r\n      if (this.multipleSelection.length === this.tbData.length) {\r\n        this.$refs['xTable'].setAllCheckboxRow(true)\r\n      }\r\n      if (this.multipleSelection.length === 0) {\r\n        this.$refs['xTable'].setAllCheckboxRow(false)\r\n      }\r\n    },\r\n    // tbFilterChange() {\r\n    //   const xTable = this.$refs.xTable\r\n    //   const column = xTable.getColumnByField('Type_Name')\r\n    //   if (!column?.filters?.length) return\r\n    //   column.filters.forEach(d => {\r\n    //     d.checked = d.value === this.searchType\r\n    //   })\r\n    //   xTable.updateData()\r\n    // },\r\n    getType() {\r\n      const getCompTree = () => {\r\n        const fun = this.isCom ? GetCompTypeTree : GetPartTypeList\r\n        fun({}).then(res => {\r\n          if (res.IsSucceed) {\r\n            let result = res.Data\r\n            if (!this.isCom) {\r\n              result = result\r\n                .map((v, idx) => {\r\n                  return {\r\n                    Data: v.Name,\r\n                    Label: v.Name\r\n                  }\r\n                })\r\n            }\r\n            this.treeParamsComponentType.data = result\r\n            this.$nextTick((_) => {\r\n              this.$refs.treeSelectComponentType?.treeDataUpdateFun(result)\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n\r\n      getCompTree()\r\n    },\r\n    // 查看图纸\r\n    handleDwg(row) {\r\n      console.log('row', row)\r\n      const obj = {}\r\n      if (this.isCom) {\r\n        obj.Comp_Id = row.Comp_Import_Detail_Id\r\n      } else {\r\n        obj.Part_Id = row.Part_Aggregate_Id\r\n      }\r\n      GetSteelCadAndBimId({ importDetailId: obj.Part_Id }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.extensionName = res.Data[0].ExtensionName\r\n          this.fileBim = res.Data[0].fileBim\r\n          this.IsUploadCad = res.Data[0].IsUpload\r\n          this.cadRowCode = row.Part_Code\r\n          this.cadRowProjectId = row.Sys_Project_Id\r\n          this.fileView()\r\n        }\r\n      })\r\n\r\n      // GetDwg(obj).then(res => {\r\n      //   if (res.IsSucceed) {\r\n      //     const fileurl = res?.Data?.length && res.Data[0].File_Url\r\n      //     window.open('http://dwgv1.bimtk.com:5432/?CadUrl=' + parseOssUrl(fileurl), '_blank')\r\n      //   } else {\r\n      //     this.$message({\r\n      //       message: res.Message,\r\n      //       type: 'error'\r\n      //     })\r\n      //   }\r\n      // })\r\n    },\r\n    setProcessList(info) {\r\n      this.changeProcessList(info)\r\n    },\r\n    resetInnerForm() {\r\n      this.$refs['searchForm'].resetFields()\r\n      this.$refs.xTable.clearFilter()\r\n    },\r\n    innerFilter() {\r\n      this.multipleSelection = []\r\n      const arr = []\r\n      if (this.isCom) {\r\n        arr.push('Type', 'Comp_Code', 'Spec', 'Is_Component')\r\n      } else {\r\n        arr.push('Part_Code', 'Spec', 'Type_Name', 'Project_Name', 'Area_Name', 'InstallUnit_Name')\r\n      }\r\n\r\n      const xTable = this.$refs.xTable\r\n      xTable.clearCheckboxRow()\r\n      arr.forEach((element, idx) => {\r\n        const column = xTable.getColumnByField(element)\r\n        if (element === 'Is_Component') {\r\n          column.filters.forEach((option, idx) => {\r\n            option.checked = idx === (this.innerForm.searchDirect ? 0 : 1)\r\n          })\r\n        }\r\n        if (element === 'Spec') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.searchSpecSearch\r\n          option.checked = true\r\n        }\r\n\r\n        if (element === 'Comp_Code' || element === 'Part_Code') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.searchContent\r\n          option.checked = true\r\n        }\r\n        if (element === 'Project_Name') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.projectName\r\n          option.checked = true\r\n        }\r\n        if (element === 'Area_Name') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.areaName\r\n          option.checked = true\r\n        }\r\n        if (element === 'InstallUnit_Name') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.installName\r\n          option.checked = true\r\n        }\r\n      })\r\n      xTable.updateData()\r\n    },\r\n    filterComponentMethod({ option, row }) {\r\n      if (this.innerForm.searchDirect === '') {\r\n        return true\r\n      }\r\n      return row.Is_Component === !this.innerForm.searchDirect\r\n    },\r\n    filterSpecMethod({ option, row }) {\r\n      if (this.innerForm.searchSpecSearch.trim() === '') {\r\n        return true\r\n      }\r\n      const splitAndClean = (input) => input.trim().replace(/\\s+/g, ' ').split(' ')\r\n      const specArray = splitAndClean(this.innerForm.searchSpecSearch)\r\n      return specArray.some(code => (row.Spec || '').includes(code))\r\n    },\r\n\r\n    filterCodeMethod({ option, row }) {\r\n      if (this.innerForm.searchContent.trim() === '') {\r\n        return true\r\n      }\r\n\r\n      const splitAndClean = (input) => input.trim().replace(/\\s+/g, ' ').split(' ')\r\n\r\n      const cur = this.isCom ? 'Comp_Code' : 'Part_Code'\r\n\r\n      const arr = splitAndClean(this.innerForm.searchContent)\r\n\r\n      if (this.curSearch === 1) {\r\n        return arr.some(code => row[cur] === code)\r\n      } else {\r\n        for (let i = 0; i < arr.length; i++) {\r\n          const item = arr[i]\r\n          if (row[cur].includes(item)) {\r\n            return true\r\n          }\r\n        }\r\n        return false\r\n      }\r\n    },\r\n    filterProjectMethod({ option, row }) {\r\n      if (option.data === '') {\r\n        return true\r\n      }\r\n      return row.Project_Name === option.data\r\n    },\r\n    filterAreaMethod({ option, row }) {\r\n      if (option.data === '') {\r\n        return true\r\n      }\r\n      return row.Area_Name === option.data\r\n    },\r\n    filterInstallMethod({ option, row }) {\r\n      if (option.data === '') {\r\n        return true\r\n      }\r\n      return row.InstallUnit_Name === option.data\r\n    },\r\n    componentTypeFilter(e) {\r\n      this.$refs?.treeSelectComponentType.filterFun(e)\r\n    },\r\n    getInstallUnitIdNameList(id) {\r\n      if (!this.areaId) {\r\n        this.installUnitIdList = []\r\n        this.disabledAdd = false\r\n      } else {\r\n        this.disabledAdd = true\r\n        GetInstallUnitIdNameList({ Area_Id: this.areaId }).then(res => {\r\n          this.installUnitIdList = res.Data\r\n          if (this.installUnitIdList.length) {\r\n            this.formInline.InstallUnit_Id = this.installUnitIdList[0].Id\r\n          }\r\n          this.disabledAdd = false\r\n        })\r\n      }\r\n    },\r\n    installChange() {\r\n      if (!this.tbData.length) {\r\n        this.$refs['searchForm'].resetFields()\r\n        this.$refs.xTable.clearFilter()\r\n        return\r\n      }\r\n      this.$confirm('切换区域右侧数据清空, 是否确认?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbData = []\r\n        this.resetInnerForm()\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    showPartUsedProcess(row) {\r\n      if (this.isNest) {\r\n        return !!row.Comp_Import_Detail_Id\r\n      } else {\r\n        return !this.isView && row.Type !== 'Direct'\r\n      }\r\n    },\r\n    handleExport() {\r\n      if (!this.tbData.length) {\r\n        this.$message({\r\n          message: '暂无数据',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      this.$refs.xTable.exportData({\r\n        filename: `部件排产-${this.formInline.Schduling_Code}(部件)`,\r\n        type: 'xlsx',\r\n        data: this.tbData\r\n      })\r\n    },\r\n    handleCloseDrawer() {\r\n      this.drawer = false\r\n    },\r\n    fileView() {\r\n      this.iframeKey = uuidv4()\r\n      this.iframeUrl = `${\r\n        this.baseCadUrl\r\n      }?router=1&iframeId=11&baseUrl=${baseUrl()}&token=${localStorage.getItem(\r\n        'Token'\r\n      )}&auth_id=${localStorage.getItem('Last_Working_Object_Id')}`\r\n      this.drawer = true\r\n    },\r\n    renderIframe() {\r\n      const ExtensionName = this.extensionName\r\n      const fileBim = this.fileBim\r\n      this.iframeUrl = `${\r\n        this.baseCadUrl\r\n      }?router=1&iframeId=11&baseUrl=${baseUrl()}&token=${localStorage.getItem(\r\n        'Token'\r\n      )}&auth_id=${localStorage.getItem('Last_Working_Object_Id')}`\r\n      this.fullscreenid = ExtensionName\r\n      this.fullbimid = fileBim\r\n    },\r\n    fullscreen(v) {\r\n      this.templateUrl = `${\r\n        this.baseCadUrl\r\n      }?router=1&iframeId=13&baseUrl=${baseUrl()}&token=${localStorage.getItem(\r\n        'Token'\r\n      )}&auth_id=${localStorage.getItem('Last_Working_Object_Id')}`\r\n      this.drawersull = true\r\n    },\r\n    frameListener({ data }) {\r\n      if (data.type === 'loaded') {\r\n        console.log('data', data)\r\n        console.error(\r\n          'data.data.iframeId',\r\n          data.data.iframeId,\r\n          typeof data.data.iframeId\r\n        )\r\n        if (data.data.iframeId === '11') {\r\n          document.getElementById('frame').contentWindow.postMessage(\r\n            {\r\n              type: 'router',\r\n              path: '/modelCad',\r\n              query: {\r\n                // baseUrl: baseUrl(),\r\n                cadId: this.fileBim,\r\n                projectId: this.cadRowProjectId,\r\n                steelName: this.cadRowCode,\r\n                showCad: this.IsUploadCad,\r\n                isSubAssembly: true,\r\n                isPart: true\r\n                // cadId: this.fileBim\r\n                // token: localStorage.getItem('Token'),\r\n                // auth_id: localStorage.getItem('Last_Working_Object_Id')\r\n              }\r\n            },\r\n            '*'\r\n          )\r\n        } else if (data.data.iframeId === '13') {\r\n          document.getElementById('fullFrame').contentWindow.postMessage(\r\n            {\r\n              type: 'router',\r\n              path: '/modelCad',\r\n              query: {\r\n                // baseUrl: baseUrl(),\r\n                cadId: this.fileBim,\r\n                projectId: this.cadRowProjectId,\r\n                steelName: this.cadRowCode,\r\n                showCad: this.IsUploadCad,\r\n                isSubAssembly: true,\r\n                isPart: true\r\n                // token: localStorage.getItem('Token'),\r\n                // auth_id: localStorage.getItem('Last_Working_Object_Id')\r\n              }\r\n            },\r\n            '*'\r\n          )\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.flex-row {\r\n  display: flex;\r\n\r\n  .cs-left {\r\n    background-color: #ffffff;\r\n    margin-right: 20px;\r\n    border-radius: 4px;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n\r\n    .cs-tree-wrapper {\r\n      height: 100%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      overflow: hidden;\r\n      padding: 16px;\r\n\r\n      .tree-search {\r\n        display: flex;\r\n\r\n        .search-select {\r\n          margin-right: 8px;\r\n        }\r\n      }\r\n\r\n      .el-tree {\r\n        flex: 1;\r\n        overflow: auto;\r\n      }\r\n    }\r\n  }\r\n\r\n  .cs-right {\r\n    flex: 1;\r\n    overflow: hidden;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  padding: 0;\r\n  text-align: right;\r\n}\r\n\r\n::v-deep .el-card__body {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.tb-x {\r\n  flex: 1;\r\n  height: 0;\r\n  margin-bottom: 10px;\r\n  overflow: auto;\r\n}\r\n\r\n.topTitle {\r\n  font-size: 14px;\r\n  margin: 0 0 16px;\r\n\r\n  span {\r\n    display: inline-block;\r\n    width: 2px;\r\n    height: 14px;\r\n    background: #009dff;\r\n    vertical-align: middle;\r\n    margin-right: 6px;\r\n  }\r\n}\r\n\r\n::v-deep .elDivder {\r\n  margin: 10px;\r\n}\r\n\r\n.btn-x {\r\n  //margin-bottom: 16px;\r\n\r\n}\r\n\r\n.el-icon-edit {\r\n  cursor: pointer;\r\n}\r\n\r\nfooter {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.cs-bottom {\r\n  position: relative;\r\n  height: 40px;\r\n  line-height: 40px;\r\n\r\n  .data-info {\r\n    position: absolute;\r\n    bottom: 0;\r\n\r\n    .info-x {\r\n      margin-right: 20px;\r\n    }\r\n  }\r\n}\r\n\r\n.demo-form-inline {\r\n  ::v-deep {\r\n    .el-form-item {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.cs-tree-x {\r\n  ::v-deep {\r\n    .el-select {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n.cs-column-row{\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .cs-ell{\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2oBA,SAAAA,YAAA,EAAAC,QAAA;AACA,OAAAC,kBAAA;AACA,SACAC,uBAAA,EACAC,MAAA,EACAC,wBAAA,EACAC,6BAAA,EACAC,qBAAA,EAAAC,yBAAA,EAAAC,0BAAA,QACA;AACA,SAAAC,WAAA;AACA,OAAAC,QAAA;AACA,OAAAC,YAAA;AACA,OAAAC,QAAA;AACA,SAAAC,aAAA;AACA,SAAAC,SAAA,EAAAC,UAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AACA,OAAAC,OAAA;AACA,SAAAC,cAAA,EAAAC,gCAAA,EAAAC,kBAAA;AACA,SAAAC,aAAA;AACA,SAAAC,UAAA,EAAAC,UAAA;AACA,SAAAC,eAAA;AACA,OAAAC,MAAA;AACA,OAAAC,iBAAA;AACA,OAAAC,UAAA;AACA,SAAAC,wBAAA,EAAAC,sBAAA;AAEA,SAAAC,eAAA;AACA,SAAAC,WAAA;AACA,OAAAC,kBAAA;AAEA,SAAAC,YAAA;AACA,SAAAC,OAAA;AACA,SAAAC,mBAAA;AACA,SAAAC,UAAA;AAEA,IAAAC,YAAA;AACA;EACAC,UAAA;IAAAN,kBAAA,EAAAA,kBAAA;IAAAL,UAAA,EAAAA,UAAA;IAAAD,iBAAA,EAAAA,iBAAA;IAAA1B,kBAAA,EAAAA,kBAAA;IAAAS,QAAA,EAAAA,QAAA;IAAAE,QAAA,EAAAA,QAAA;IAAAD,YAAA,EAAAA;EAAA;EACA6B,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA;MACAC,UAAA;MACAC,SAAA;MACAC,YAAA;MACAC,SAAA;MACAC,SAAA;MACAC,OAAA;MACAC,WAAA;MACAC,UAAA;MACAC,eAAA;MACAC,KAAA;MACAC,kBAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,WAAA;QAAAf,IAAA;MAAA;MACAgB,gBAAA;QAAAhB,IAAA;MAAA;MACAiB,gBAAA;QAAAjB,IAAA;MAAA;MACAkB,cAAA;QAAAlB,IAAA;MAAA;MACAmB,WAAA;QAAAnB,IAAA;MAAA;MACAoB,cAAA;QAAApB,IAAA;MAAA;MACAqB,WAAA;MACAC,WAAA;MACAC,QAAA;MACAC,aAAA;QACAC,YAAA,WAAAA,aAAAC,IAAA,GACA;MACA;MACAC,SAAA;QACAC,WAAA;QACAC,QAAA;QACAC,WAAA;QACAC,aAAA;QACAC,gBAAA;QACAC,YAAA;MACA;MACAC,SAAA;MACAC,UAAA;MACAC,UAAA;QACAC,cAAA;QACAC,eAAA;QACAC,WAAA;QACAC,cAAA;QACAC,MAAA;MACA;MACAC,KAAA;MACAC,UAAA;MACAC,OAAA;MACAC,MAAA;MACAC,QAAA;MACAC,UAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,SAAA;MACAC,aAAA;MACAC,cAAA;MACAC,WAAA;MACAC,aAAA;MACAC,YAAA;MACAC,WAAA;MACAC,SAAA;MACAC,UAAA;MACAC,gBAAA;MACAC,QAAA;MACAC,MAAA;MACAC,KAAA;MACAC,MAAA,WAAAA,OAAA;QAAA;MAAA;MACAC,QAAA,EAAAC,SAAA;MACAC,QAAA;MACAC,gBAAA;MACAC,UAAA;MACAC,WAAA;MACAC,UAAA,EAAAL,SAAA;MACAM,UAAA;MACAC,kBAAA;MAEAC,iBAAA;MACAC,SAAA;MACAC,MAAA;MACA/C,WAAA;MACAgD,UAAA;MACAC,WAAA;MACA;MACAC,QAAA;MACAC,uBAAA;QACA;QACA;QACAC,UAAA;QACAC,WAAA;QACAjF,IAAA;QACAkF,KAAA;UACAC,QAAA;UACAtE,KAAA;UACAC,KAAA;QACA;MACA;MACAsE,gBAAA;QACAC,WAAA;QACAC,YAAA;QACAC,SAAA;MACA;MACAC,WAAA;MACAC,SAAA;MACAC,aAAA;IACA;EACA;EACAC,KAAA;IACA;MACAC,OAAA,WAAAA,QAAAC,CAAA,EAAAC,CAAA;QACA,KAAAC,UAAA;QACA,KAAAC,QAAA;MACA;MACAC,SAAA;IACA;EACA;EAEAC,QAAA,EAAAC,aAAA,CAAAA,aAAA;IACAC,KAAA,WAAAA,MAAA;MACA,YAAApC,QAAA;IACA;IACAqC,MAAA,WAAAA,OAAA;MACA,YAAA/B,UAAA;IACA;IACAgC,MAAA,WAAAA,OAAA;MACA,YAAAhC,UAAA;IACA;IACAiC,KAAA,WAAAA,MAAA;MACA,YAAAjC,UAAA;IACA;IACAkC,WAAA,WAAAA,YAAA;MACA,YAAA3B,WAAA,QAAAzC,UAAA,CAAAI,cAAA;IACA;IACA;IACA;IACA;IACAiE,UAAA,WAAAA,WAAA;MACA,YAAAL,KAAA;IACA;IACAtE,WAAA,WAAAA,YAAA;MAAA,IAAA4E,KAAA;MACA,IAAAC,IAAA,QAAAlC,iBAAA,CAAAmC,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,EAAA,KAAAJ,KAAA,CAAAtE,UAAA,CAAAI,cAAA;MAAA;MACA,IAAAmE,IAAA;QACA,OAAAA,IAAA,CAAAI,IAAA;MACA;QACA;MACA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA,YAAAC,gBAAA,UAAAb,KAAA;IACA;IACAc,MAAA,WAAAA,OAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,YAAAtE,MAAA,CAAAuE,IAAA,WAAAT,IAAA;QAAA,OAAAA,IAAA,CAAAU,QAAA;MAAA;IACA;EAAA,GACArI,UAAA,2DACAA,UAAA,yCACA;EACAsI,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACA,IAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;cACA;cACA;cACAZ,MAAA,CAAAa,UAAA;YACA;cACA1I,YAAA;gBAAA2I,IAAA;cAAA,GAAAC,IAAA,WAAAC,GAAA;gBACAhB,MAAA,CAAAa,UAAA,GAAAG,GAAA,CAAAC,IAAA;cACA;YACA;UAAA;UAAA;YAAA,OAAAV,QAAA,CAAAW,IAAA;QAAA;MAAA,GAAAd,OAAA;IAAA;EACA;EACAe,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAnB,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkB,SAAA;MAAA,IAAAC,mBAAA,EAAAlE,MAAA,EAAAmE,OAAA;MAAA,OAAArB,mBAAA,GAAAG,IAAA,UAAAmB,UAAAC,SAAA;QAAA,kBAAAA,SAAA,CAAAjB,IAAA,GAAAiB,SAAA,CAAAhB,IAAA;UAAA;YACAW,MAAA,CAAAM,eAAA;YACAN,MAAA,CAAAO,SAAA;YACAP,MAAA,CAAAQ,YAAA;YACAR,MAAA,CAAA3E,QAAA,GAAA2E,MAAA,CAAAS,MAAA,CAAAC,KAAA,CAAAC,OAAA;YACAX,MAAA,CAAAY,KAAA,GAAAZ,MAAA,CAAAS,MAAA,CAAAC,KAAA,CAAAE,KAAA;YACAZ,MAAA,CAAArE,UAAA,GAAAqE,MAAA,CAAAS,MAAA,CAAAC,KAAA,CAAAG,MAAA;YACAb,MAAA,CAAAc,KAAA,GAAAd,MAAA,CAAAS,MAAA,CAAAC,KAAA,CAAAI,KAAA;YACAd,MAAA,CAAApE,UAAA,GAAAoE,MAAA,CAAAS,MAAA,CAAAC,KAAA,CAAAK,GAAA;YACA;YACA;YACAf,MAAA,CAAAvG,UAAA,CAAAE,eAAA,GAAAqH,YAAA,CAAAC,OAAA;YACA;YACA;YACA;YACA;;YAEAjB,MAAA,CAAAkB,MAAA,GAAAtL,UAAA;YACAoK,MAAA,CAAAmB,mBAAA;YAEAnB,MAAA,CAAA5E,MAAA,GAAAvG,QAAA,CAAAmL,MAAA,CAAAoB,SAAA;YAAAf,SAAA,CAAAhB,IAAA;YAAA,OACAW,MAAA,CAAAqB,WAAA;UAAA;YACA,IAAArB,MAAA,CAAAtC,MAAA,IAAAsC,MAAA,CAAArC,MAAA;cAAAuC,mBAAA,GACAF,MAAA,CAAAS,MAAA,CAAAC,KAAA,EAAA1E,MAAA,GAAAkE,mBAAA,CAAAlE,MAAA,EAAAmE,OAAA,GAAAD,mBAAA,CAAAC,OAAA,EACA;cACA;cACA;cACAH,MAAA,CAAAoB,SAAA;YACA;YAEA,IAAApB,MAAA,CAAApC,KAAA;cACA;cACAoC,MAAA,CAAAsB,OAAA;YACA;YACA,IAAAtB,MAAA,CAAArC,MAAA;cACAqC,MAAA,CAAAsB,OAAA;YACA;YAAAjB,SAAA,CAAAhB,IAAA;YAAA,OAEAnI,UAAA,CAAA8I,MAAA,CAAAY,KAAA;UAAA;YAAAZ,MAAA,CAAAlD,SAAA,GAAAuD,SAAA,CAAAkB,IAAA;YAEAC,MAAA,CAAAC,gBAAA,YAAAzB,MAAA,CAAA0B,aAAA;YACA1B,MAAA,CAAA2B,KAAA;cACAC,OAAA,CAAAC,GAAA;cACAL,MAAA,CAAAM,mBAAA,YAAA9B,MAAA,CAAA0B,aAAA;YACA;UAAA;UAAA;YAAA,OAAArB,SAAA,CAAAP,IAAA;QAAA;MAAA,GAAAG,QAAA;IAAA;EACA;EACA8B,SAAA,WAAAA,UAAA;IAAA,IAAAC,MAAA;IACAR,MAAA,CAAAC,gBAAA,iBAAAC,aAAA;IACA,KAAAC,KAAA;MACAH,MAAA,CAAAM,mBAAA,YAAAE,MAAA,CAAAN,aAAA;IACA;EACA;EACAO,OAAA,EAAAzE,aAAA,CAAAA,aAAA,KACApH,UAAA;IACAgH,UAAA,WAAAA,WAAA;MACA,SAAAK,KAAA;MACA,KAAA/C,WAAA,QAAAR,MAAA,CAAAgI,KAAA,WAAAhE,CAAA;QAAA,QAAAA,CAAA,CAAAiE,qBAAA;MAAA,YAAA5D,MAAA;MACA,IAAA6D,GAAA,QAAAnI,OAAA,CAAAoI,SAAA,WAAAnE,CAAA;QAAA,OAAAA,CAAA,CAAAoE,IAAA;MAAA;MACA,SAAA5H,WAAA;QACA0H,GAAA,gBAAAnI,OAAA,CAAAsI,MAAA,CAAAH,GAAA;MACA;QACA,IAAAA,GAAA;UACA,UAAAI,WAAA;YACA,KAAAC,QAAA;cACAC,OAAA;cACAC,IAAA;YACA;YACA;UACA;UACA,KAAA1I,OAAA,CAAA2I,IAAA,MAAAJ,WAAA;QACA;QACA,KAAAK,OAAA;MACA;IACA;IACAxB,WAAA,WAAAA,YAAA;MAAA,IAAAyB,MAAA;MAAA,OAAAjE,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAgE,SAAA;QAAA,OAAAjE,mBAAA,GAAAG,IAAA,UAAA+D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7D,IAAA,GAAA6D,SAAA,CAAA5D,IAAA;YAAA;cAAA4D,SAAA,CAAA5D,IAAA;cAAA,OACAyD,MAAA,CAAAI,SAAA;YAAA;cAAAD,SAAA,CAAA5D,IAAA;cAAA,OACAyD,MAAA,CAAAK,WAAA;YAAA;YAAA;cAAA,OAAAF,SAAA,CAAAnD,IAAA;UAAA;QAAA,GAAAiD,QAAA;MAAA;IACA;IACA1F,QAAA,WAAAA,SAAA;MAAA,IAAA+F,MAAA;MACA,KAAA1K,WAAA;MACA,KAAAC,WAAA;MACA,KAAAC,QAAA;MACA,KAAAsB,MAAA,CAAAmJ,OAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,YAAA,KAAAH,MAAA,CAAA1K,WAAA,CAAA8K,QAAA,CAAAF,GAAA,CAAAC,YAAA;UACAH,MAAA,CAAA1K,WAAA,CAAAkK,IAAA,CAAAU,GAAA,CAAAC,YAAA;QACA;QACA,IAAAD,GAAA,CAAAG,gBAAA,KAAAL,MAAA,CAAAzK,WAAA,CAAA6K,QAAA,CAAAF,GAAA,CAAAG,gBAAA;UACAL,MAAA,CAAAzK,WAAA,CAAAiK,IAAA,CAAAU,GAAA,CAAAG,gBAAA;QACA;QACA,IAAAH,GAAA,CAAAI,SAAA,KAAAN,MAAA,CAAAxK,QAAA,CAAA4K,QAAA,CAAAF,GAAA,CAAAI,SAAA;UACAN,MAAA,CAAAxK,QAAA,CAAAgK,IAAA,CAAAU,GAAA,CAAAI,SAAA;QACA;MACA;IACA;IACAR,SAAA,WAAAA,UAAA;MAAA,IAAAS,MAAA;MAAA,OAAA9E,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6E,SAAA;QAAA,IAAAC,UAAA;QAAA,OAAA/E,mBAAA,GAAAG,IAAA,UAAA6E,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3E,IAAA,GAAA2E,SAAA,CAAA1E,IAAA;YAAA;cACAwE,UAAA;cACA,IAAAF,MAAA,CAAApF,MAAA;gBACA,IAAAoF,MAAA,CAAAjG,MAAA;kBACAmG,UAAA;gBACA;kBACAA,UAAA;gBACA;cACA;gBACAA,UAAA,GAAAF,MAAA,CAAAjG,MAAA;cACA;cACAiG,MAAA,CAAA1I,QAAA,GAAA4I,UAAA;cAAAE,SAAA,CAAA1E,IAAA;cAAA,OACAsE,MAAA,CAAAK,cAAA,CAAAH,UAAA;YAAA;cACA,KAAAF,MAAA,CAAAM,eAAA;gBACAN,MAAA,CAAA1J,OAAA,GAAA0J,MAAA,CAAA1J,OAAA,CAAAiK,MAAA,WAAAhG,CAAA;kBAAA,OAAAA,CAAA,CAAAoE,IAAA;gBAAA;cACA;cACAqB,MAAA,CAAAvG,UAAA;YAAA;YAAA;cAAA,OAAA2G,SAAA,CAAAjE,IAAA;UAAA;QAAA,GAAA8D,QAAA;MAAA;IACA;IACAO,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MAAA,OAAAvF,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAsF,SAAA;QAAA,OAAAvF,mBAAA,GAAAG,IAAA,UAAAqF,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnF,IAAA,GAAAmF,SAAA,CAAAlF,IAAA;YAAA;cAAAkF,SAAA,CAAAlF,IAAA;cAAA,OACA+E,MAAA,CAAAJ,cAAA,CAAAI,MAAA,CAAAnJ,QAAA;YAAA;cACAmJ,MAAA,CAAApM,KAAA;YAAA;YAAA;cAAA,OAAAuM,SAAA,CAAAzE,IAAA;UAAA;QAAA,GAAAuE,QAAA;MAAA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAjD,SAAA,WAAAA,UAAA;MAAA,IAAAoD,MAAA;MAAA,OAAA3F,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA0F,SAAA;QAAA,IAAAC,OAAA;QAAA,OAAA5F,mBAAA,GAAAG,IAAA,UAAA0F,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxF,IAAA,GAAAwF,SAAA,CAAAvF,IAAA;YAAA;cACAmF,MAAA,CAAA1J,SAAA;cACA4J,OAAA;cAAA,KACAF,MAAA,CAAAjG,MAAA;gBAAAqG,SAAA,CAAAvF,IAAA;gBAAA;cAAA;cAAA,KACAmF,MAAA,CAAA9G,MAAA;gBAAAkH,SAAA,CAAAvF,IAAA;gBAAA;cAAA;cAAAuF,SAAA,CAAAvF,IAAA;cAAA,OACAmF,MAAA,CAAAK,eAAA;YAAA;cAAAH,OAAA,GAAAE,SAAA,CAAArD,IAAA;cAAAqD,SAAA,CAAAvF,IAAA;cAAA;YAAA;cAAAuF,SAAA,CAAAvF,IAAA;cAAA,OAEAmF,MAAA,CAAAM,eAAA;YAAA;cAAAJ,OAAA,GAAAE,SAAA,CAAArD,IAAA;YAAA;cAAAqD,SAAA,CAAAvF,IAAA;cAAA;YAAA;cAAAuF,SAAA,CAAAvF,IAAA;cAAA,OAGAmF,MAAA,CAAAK,eAAA;YAAA;cAAAH,OAAA,GAAAE,SAAA,CAAArD,IAAA;YAAA;cAGAiD,MAAA,CAAAO,UAAA,CAAAL,OAAA;cACAF,MAAA,CAAA1J,SAAA;YAAA;YAAA;cAAA,OAAA8J,SAAA,CAAA9E,IAAA;UAAA;QAAA,GAAA2E,QAAA;MAAA;IACA;IACAO,kBAAA,WAAAA,mBAAA;MACA;IAAA,CACA;IACAC,eAAA,WAAAA,gBAAA;MACA;IAAA,CACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC,SAAA,WAAAA,UAAA;MACAtQ,YAAA,MAAAuQ,MAAA,OAAA1E,MAAA;IACA;IACAU,mBAAA,WAAAA,oBAAA;MACA,KAAA1G,cAAA;IACA;IACA2K,cAAA,WAAAA,eAAAC,KAAA;MACA,KAAAhL,iBAAA,GAAAgL,KAAA,CAAAC,OAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAA/L,UAAA,CAAAG,WAAA;MACAzD,aAAA;QACAsP,EAAA,OAAAzJ;MACA,GAAA2D,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAA8F,SAAA;UAAA,IAAAC,SAAA,EAAAC,UAAA;UACA,KAAAhG,GAAA,CAAAC,IAAA;YACA;UACA;UAEA,IAAAgG,KAAA,GAAAtP,MAAA,EAAAoP,SAAA,GAAA/F,GAAA,CAAAC,IAAA,cAAA8F,SAAA,uBAAAA,SAAA,CAAAG,iBAAA;UACA,IAAAC,GAAA,GAAAxP,MAAA,EAAAqP,UAAA,GAAAhG,GAAA,CAAAC,IAAA,cAAA+F,UAAA,uBAAAA,UAAA,CAAAI,eAAA;UACAR,MAAA,CAAA3M,aAAA,CAAAC,YAAA,aAAAC,IAAA;YACA,OAAAA,IAAA,CAAAkN,OAAA,KAAAJ,KAAA,IAAA9M,IAAA,CAAAkN,OAAA,KAAAF,GAAA;UACA;QACA;UACAP,MAAA,CAAA/C,QAAA;YACAC,OAAA,EAAA9C,GAAA,CAAAsG,OAAA;YACAvD,IAAA;UACA;QACA;MACA;IACA;IACAwD,WAAA,WAAAA,YAAA;MACA,KAAAxL,aAAA;MACA,KAAAC,YAAA;IACA;IACAkK,eAAA,WAAAA,gBAAA;MAAA,IAAAsB,MAAA;MACA,WAAAC,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACAxR,uBAAA;UACAyR,GAAA,EAAAJ,MAAA,CAAAK;QACA,GAAA9G,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAA8F,SAAA;YACA,IAAAgB,KAAA,IAAA9G,GAAA,aAAAA,GAAA,uBAAAA,GAAA,CAAAC,IAAA;YACA,IAAA8G,IAAA,GAAAD,KAAA,CAAAE,GAAA,WAAA1I,CAAA;cACAA,CAAA,CAAA2I,iBAAA,GAAA3I,CAAA,CAAA4I,sBAAA,IAAA5I,CAAA,CAAA6I,sBAAA;cACA;cACA7I,CAAA,CAAA8I,WAAA,GAAA9I,CAAA,CAAA+I,qBAAA;cACA/I,CAAA,CAAAgJ,aAAA,GAAAhJ,CAAA,CAAAiJ,uBAAA;cACAjJ,CAAA,CAAAkJ,eAAA,GAAAlJ,CAAA,CAAAmJ,yBAAA,IAAAnJ,CAAA,CAAAkJ,eAAA;cACAlJ,CAAA,CAAAoJ,WAAA,GAAApJ,CAAA,CAAAqJ,mBAAA;cAEA,OAAArJ,CAAA;YACA;YAEAoI,OAAA,CAAAK,IAAA;UACA;YACAP,MAAA,CAAA3D,QAAA;cACAC,OAAA,EAAA9C,GAAA,CAAAsG,OAAA;cACAvD,IAAA;YACA;YACA4D,MAAA;UACA;QACA;MACA;IACA;IACA1B,eAAA,WAAAA,gBAAA;MAAA,IAAA2C,OAAA;MAAA,OAAA3I,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA0I,SAAA;QAAA,IAAA1G,GAAA,EAAA2G,MAAA;QAAA,OAAA5I,mBAAA,GAAAG,IAAA,UAAA0I,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxI,IAAA,GAAAwI,SAAA,CAAAvI,IAAA;YAAA;cAEA0B,GAAA,GACAyG,OAAA,CAAA/G,MAAA,CAAAC,KAAA,CADAK,GAAA;cAAA6G,SAAA,CAAAvI,IAAA;cAAA,OAEAhK,0BAAA;gBACAwS,iBAAA,EAAA9G;cACA,GAAApB,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAA8F,SAAA;kBAAA,IAAAoC,UAAA,EAAAC,UAAA,EAAAC,UAAA;kBACA,IAAAC,cAAA,IAAAH,UAAA,GAAAlI,GAAA,CAAAC,IAAA,cAAAiI,UAAA,uBAAAA,UAAA,CAAAG,cAAA,CAAArB,GAAA,WAAA1I,CAAA;oBACA,IAAAA,CAAA,CAAA4I,sBAAA;sBACA;sBACA5I,CAAA,CAAA2I,iBAAA,GAAA3I,CAAA,CAAA4I,sBAAA;oBACA;oBAAA,IAAA5I,CAAA,CAAAgK,sBAAA;sBACA;sBACAhK,CAAA,CAAA2I,iBAAA,GAAA3I,CAAA,CAAAgK,sBAAA;oBACA;oBACAhK,CAAA,CAAAoJ,WAAA,GAAApJ,CAAA,CAAAqJ,mBAAA;oBACA,OAAArJ,CAAA;kBACA;kBACAsJ,OAAA,CAAA/N,UAAA,GAAA0O,MAAA,CAAAC,MAAA,CAAAZ,OAAA,CAAA/N,UAAA,GAAAsO,UAAA,GAAAnI,GAAA,CAAAC,IAAA,cAAAkI,UAAA,uBAAAA,UAAA,CAAAM,cAAA;kBACA,CAAAL,UAAA,GAAApI,GAAA,CAAAC,IAAA,cAAAmI,UAAA,eAAAA,UAAA,CAAAM,YAAA,CAAAjF,OAAA,WAAArF,IAAA;oBACA,IAAAuK,KAAA;sBACAC,GAAA,EAAAxK,IAAA,CAAAyK,YAAA;sBACAtQ,KAAA,EAAA6F;oBACA;oBACAwJ,OAAA,CAAAkB,iBAAA,CAAAH,KAAA;kBACA;kBAEAf,OAAA,CAAAmB,WAAA,CAAAV,cAAA;kBACA,OAAAA,cAAA;gBACA;kBACAT,OAAA,CAAA/E,QAAA;oBACAC,OAAA,EAAA9C,GAAA,CAAAsG,OAAA;oBACAvD,IAAA;kBACA;kBACA;gBACA;cACA;YAAA;cAjCA+E,MAAA,GAAAE,SAAA,CAAArG,IAAA;cAkCAK,OAAA,CAAAC,GAAA,WAAA6F,MAAA;cAAA,OAAAE,SAAA,CAAAgB,MAAA,WACAlB,MAAA;YAAA;YAAA;cAAA,OAAAE,SAAA,CAAA9H,IAAA;UAAA;QAAA,GAAA2H,QAAA;MAAA;IACA;IACAkB,WAAA,WAAAA,YAAAhC,IAAA;MAAA,IAAAkC,OAAA;MAAA,OAAAhK,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA+J,SAAA;QAAA,IAAAC,SAAA;QAAA,OAAAjK,mBAAA,GAAAG,IAAA,UAAA+J,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7J,IAAA,GAAA6J,SAAA,CAAA5J,IAAA;YAAA;cACAuC,OAAA,CAAAC,GAAA,gBAAA8E,IAAA;cACAoC,SAAA,GAAApC,IAAA,CAAAC,GAAA,WAAA5I,IAAA;gBACA;kBACAG,EAAA,EAAAH,IAAA,CAAAkL,iBAAA;kBACAC,IAAA;gBACA;cACA;cAAAF,SAAA,CAAA5J,IAAA;cAAA,OACA/J,WAAA,CAAAyT,SAAA,EAAApJ,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAA8F,SAAA;kBACA,IAAA0D,OAAA;kBACAxJ,GAAA,CAAAC,IAAA,CAAAwD,OAAA,WAAArF,IAAA;oBACAoL,OAAA,CAAApL,IAAA,CAAAG,EAAA,MAAAH,IAAA,CAAAqL,OAAA;kBACA;kBACA1C,IAAA,CAAAtD,OAAA,WAAAiG,GAAA;oBACA,IAAAF,OAAA,CAAAE,GAAA,CAAAJ,iBAAA;sBACAL,OAAA,CAAAU,IAAA,CAAAD,GAAA,cAAAF,OAAA,CAAAE,GAAA,CAAAJ,iBAAA;oBACA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAD,SAAA,CAAAnJ,IAAA;UAAA;QAAA,GAAAgJ,QAAA;MAAA;IACA;IACA/D,UAAA,WAAAA,WAAA4B,IAAA;MAAA,IAAA6C,OAAA;MAAA,IAAAC,OAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAApO,SAAA,GAAAoO,SAAA;MACA9H,OAAA,CAAAC,GAAA,IAAA+H,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAnD,IAAA;MACA,KAAAzM,MAAA,GAAAyM,IAAA,CAAAC,GAAA,WAAA0C,GAAA;QAAA,IAAAS,oBAAA;QACA,IAAAC,WAAA,KAAAD,oBAAA,GAAAT,GAAA,CAAAlC,eAAA,cAAA2C,oBAAA,uBAAAA,oBAAA,CAAAE,KAAA;QACAX,GAAA,CAAAY,IAAA,GAAApU,MAAA;QACA0T,OAAA,CAAAW,kBAAA,CAAAb,GAAA;QACA,IAAAA,GAAA,CAAAG,OAAA;UACA,IAAAW,OAAA,GAAAd,GAAA,CAAAG,OAAA,EAAAvF,MAAA,WAAAmG,CAAA;YAAA,OAAAL,WAAA,CAAA3H,SAAA,WAAAiI,CAAA;cAAA,OAAAD,CAAA,CAAA5B,YAAA,KAAA6B,CAAA;YAAA;UAAA;UACAF,OAAA,CAAA/G,OAAA,WAAAkH,GAAA,EAAAC,KAAA;YACA,IAAA9K,IAAA,GAAA8J,OAAA,CAAAiB,YAAA,CAAAnB,GAAA,CAAAY,IAAA,EAAAK,GAAA,CAAA9B,YAAA,EAAA8B,GAAA,CAAAG,eAAA;YACA,IAAAC,GAAA,GAAAnB,OAAA,CAAAoB,eAAA,CAAAtB,GAAA,CAAAY,IAAA,EAAAK,GAAA,CAAA9B,YAAA,EAAA8B,GAAA,CAAAG,eAAA;YACApB,GAAA,CAAA5J,IAAA,IAAA6K,GAAA,CAAAM,KAAA;YACAvB,GAAA,CAAAqB,GAAA;UACA;QACA;QACAnB,OAAA,CAAAsB,WAAA,CAAAxB,GAAA;QACA,OAAAA,GAAA;MACA;MACA,IAAAyB,GAAA;MACA,SAAAtN,KAAA;QACAsN,GAAA,QAAA7Q,MAAA,CAAA0M,GAAA,WAAA1I,CAAA;UAAA,OAAAA,CAAA,CAAAiE,qBAAA;QAAA,GAAA6I,QAAA;MACA;QACAD,GAAA,QAAA7Q,MAAA,CAAA0M,GAAA,WAAA1I,CAAA;UAAA,OAAAA,CAAA,CAAAgL,iBAAA;QAAA,GAAA8B,QAAA;MACA;MACA,KAAAhR,UAAA,GAAA+Q,GAAA;IACA;IACAE,WAAA,WAAAA,YAAAC,OAAA;MAAA,IAAAC,OAAA;MAAA,OAAAtM,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAqM,SAAA;QAAA,OAAAtM,mBAAA,GAAAG,IAAA,UAAAoM,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlM,IAAA,GAAAkM,SAAA,CAAAjM,IAAA;YAAA;cAAAiM,SAAA,CAAAjM,IAAA;cAAA,OACA8L,OAAA,CAAAI,eAAA,CAAAL,OAAA;YAAA;cACAC,OAAA,CAAAK,WAAA;YAAA;YAAA;cAAA,OAAAF,SAAA,CAAAxL,IAAA;UAAA;QAAA,GAAAsL,QAAA;MAAA;IACA;IACAG,eAAA,WAAAA,gBAAAL,OAAA;MAAA,IAAAO,OAAA;MAAA,OAAA5M,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA2M,SAAA;QAAA,IAAAC,eAAA;QAAA,OAAA7M,mBAAA,GAAAG,IAAA,UAAA2M,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzM,IAAA,GAAAyM,SAAA,CAAAxM,IAAA;YAAA;cACAuC,OAAA,CAAA7I,IAAA;cAAA8S,SAAA,CAAAxM,IAAA;cAAA,OACAoM,OAAA,CAAAK,iBAAA,CAAAZ,OAAA;YAAA;cACAS,eAAA;cACAT,OAAA,CAAA7H,OAAA,WAAA0I,OAAA,EAAAvB,KAAA;gBACA,IAAAlH,GAAA,GAAAmI,OAAA,CAAAO,iBAAA,CAAAD,OAAA;gBACAnK,OAAA,CAAAC,GAAA,IAAA+H,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAiC,OAAA;gBACA,KAAAA,OAAA,CAAA3E,eAAA;kBACA,IAAA6E,UAAA,GAAAR,OAAA,CAAAjL,YAAA,CAAAuL,OAAA,CAAAG,eAAA;kBACA,IAAAT,OAAA,CAAAjL,YAAA,CAAAuL,OAAA,CAAAG,eAAA,aAAAC,KAAA;oBACA,IAAAJ,OAAA,CAAA7D,sBAAA;sBACA,IAAAkE,kBAAA,GAAAL,OAAA,CAAA7D,sBAAA,CAAA+B,KAAA;sBACA,IAAAoC,gBAAA,GAAAD,kBAAA,CAAAlK,KAAA,WAAAoK,IAAA;wBAAA,OAAAL,UAAA,CAAAzI,QAAA,CAAA8I,IAAA;sBAAA;sBAEA,KAAAD,gBAAA;wBACAV,eAAA;sBACA;wBACAI,OAAA,CAAA3E,eAAA,GAAA6E,UAAA,CAAAM,IAAA;sBACA;oBACA;sBACAR,OAAA,CAAA3E,eAAA,GAAA6E,UAAA,CAAAM,IAAA;oBACA;kBACA;gBACA;gBACA,KAAAjJ,GAAA;kBACAyI,OAAA,CAAAS,KAAA,GAAAT,OAAA,CAAA7B,IAAA;kBACA6B,OAAA,CAAAU,cAAA,GAAAV,OAAA,CAAAzE,WAAA;kBACAyE,OAAA,CAAAW,eAAA,GAAA3W,OAAA,CAAAgW,OAAA,CAAAzE,WAAA,GAAAyE,OAAA,CAAAY,MAAA,EAAAC,MAAA;kBACAnB,OAAA,CAAAvR,MAAA,CAAA0I,IAAA,CAAAmJ,OAAA;kBACAN,OAAA,CAAAtB,kBAAA,CAAA4B,OAAA;kBACA;gBACA;gBAEAzI,GAAA,CAAAkJ,KAAA,GAAAT,OAAA,CAAA7B,IAAA;gBAEA5G,GAAA,CAAAmJ,cAAA,IAAAV,OAAA,CAAAzE,WAAA;gBACAhE,GAAA,CAAAoJ,eAAA,GAAA3W,OAAA,CAAAuN,GAAA,CAAAoJ,eAAA,EAAAG,GAAA,CAAAd,OAAA,CAAAzE,WAAA,GAAAyE,OAAA,CAAAY,MAAA,EAAAC,MAAA;gBACA,KAAAtJ,GAAA,CAAA8D,eAAA;kBACA;gBACA;gBACAqE,OAAA,CAAAX,WAAA,CAAAxH,GAAA;cACA;cACA1B,OAAA,CAAAC,GAAA,oBAAA8J,eAAA;cACAF,OAAA,CAAAqB,uBAAA,CAAAnB,eAAA;cACA;cACA;cACA;cACA;cACA;cACAF,OAAA,CAAAvR,MAAA,CAAA6S,IAAA,WAAAC,CAAA,EAAAC,CAAA;gBAAA,OAAAD,CAAA,CAAAE,YAAA,GAAAD,CAAA,CAAAC,YAAA;cAAA;cACAtL,OAAA,CAAAuL,OAAA;cACAvL,OAAA,CAAAC,GAAA,mBAAA4J,OAAA,CAAAlL,SAAA,EAAAkL,OAAA,CAAAvR,MAAA;YAAA;YAAA;cAAA,OAAA2R,SAAA,CAAA/L,IAAA;UAAA;QAAA,GAAA4L,QAAA;MAAA;IACA;IACAoB,uBAAA,WAAAA,wBAAAM,WAAA;MAAA,IAAAC,OAAA;MACA,IAAAD,WAAA;MACAE,UAAA;QACAD,OAAA,CAAAE,MAAA;UACAC,iBAAA;QACA;MACA;MACA;IACA;IACArD,kBAAA,WAAAA,mBAAA4B,OAAA;MACA,IAAAvD,GAAA,QAAAiF,SAAA,CAAA1B,OAAA;MACA,KAAAxL,SAAA,CAAAiI,GAAA,IAAAuD,OAAA;IACA;IACAC,iBAAA,WAAAA,kBAAAD,OAAA;MACA,IAAAvD,GAAA,QAAAiF,SAAA,CAAA1B,OAAA;MACA,YAAAxL,SAAA,CAAAiI,GAAA;IACA;IACAiF,SAAA,WAAAA,UAAA1B,OAAA;MACA,OAAApW,SAAA,MAAA8H,KAAA,EAAAsO,OAAA;IACA;IACA2B,SAAA,WAAAA,UAAA;MACA,IAAAC,UAAA;MACA,KAAAC,KAAA,eAAAC,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA,EAAAH,UAAA;MACA;MACA,OAAAA,UAAA;IACA;IACAI,SAAA,WAAAA,UAAA;MAAA,IAAAC,UAAA,GAAAtE,SAAA;QAAAuE,OAAA;MAAA,OAAApP,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAmP,SAAA;QAAA,IAAAC,mBAAA;QAAA,IAAAC,OAAA,EAAAC,YAAA,EAAAC,qBAAA,EAAAC,SAAA,EAAA1N,MAAA,EAAA2N,SAAA;QAAA,OAAA1P,mBAAA,GAAAG,IAAA,UAAAwP,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtP,IAAA,GAAAsP,SAAA,CAAArP,IAAA;YAAA;cAAA+O,OAAA,GAAAJ,UAAA,CAAArE,MAAA,QAAAqE,UAAA,QAAA1S,SAAA,GAAA0S,UAAA;cACAK,YAAA,GAAAJ,OAAA,CAAAP,SAAA;cAAA,IACAW,YAAA;gBAAAK,SAAA,CAAArP,IAAA;gBAAA;cAAA;cAAA,OAAAqP,SAAA,CAAA9F,MAAA;YAAA;cAAA0F,qBAAA,GACAL,OAAA,CAAAU,eAAA,IAAAJ,SAAA,GAAAD,qBAAA,CAAAC,SAAA,EAAA1N,MAAA,GAAAyN,qBAAA,CAAAzN,MAAA;cAAA,IACAA,MAAA;gBAAA6N,SAAA,CAAArP,IAAA;gBAAA;cAAA;cAAA,OAAAqP,SAAA,CAAA9F,MAAA;YAAA;cACA,KAAAwF,OAAA;gBACAH,OAAA,CAAApT,WAAA;cACA;cAAA6T,SAAA,CAAArP,IAAA;cAAA,OAEA4O,OAAA,CAAAW,eAAA,CAAAL,SAAA,EAAAH,OAAA;YAAA;cAAAI,SAAA,GAAAE,SAAA,CAAAnN,IAAA;cACAK,OAAA,CAAAC,GAAA,cAAA2M,SAAA;cAAA,IACAA,SAAA;gBAAAE,SAAA,CAAArP,IAAA;gBAAA;cAAA;cAAA,OAAAqP,SAAA,CAAA9F,MAAA;YAAA;cAAA,KACAwF,OAAA;gBAAAM,SAAA,CAAArP,IAAA;gBAAA;cAAA;cAAA,OAAAqP,SAAA,CAAA9F,MAAA,WAAA4F,SAAA;YAAA;cACA,CAAAL,mBAAA,GAAAF,OAAA,CAAAL,KAAA,uBAAAO,mBAAA,eAAAA,mBAAA,CAAA/M,SAAA;cACA6M,OAAA,CAAApT,WAAA;YAAA;YAAA;cAAA,OAAA6T,SAAA,CAAA5O,IAAA;UAAA;QAAA,GAAAoO,QAAA;MAAA;IACA;IACAW,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,OAAAjQ,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAgQ,UAAA;QAAA,IAAAV,YAAA,EAAAW,GAAA;QAAA,OAAAlQ,mBAAA,GAAAG,IAAA,UAAAgQ,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA9P,IAAA,GAAA8P,UAAA,CAAA7P,IAAA;YAAA;cACAgP,YAAA,GAAAS,OAAA,CAAApB,SAAA;cAAA,IACAW,YAAA;gBAAAa,UAAA,CAAA7P,IAAA;gBAAA;cAAA;cAAA,OAAA6P,UAAA,CAAAtG,MAAA;YAAA;cACAoG,GAAA;cAAA,IACAF,OAAA,CAAA5U,MAAA,CAAAyP,MAAA;gBAAAuF,UAAA,CAAA7P,IAAA;gBAAA;cAAA;cACAyP,OAAA,CAAArM,QAAA;gBACAC,OAAA;gBACAC,IAAA;cACA;cAAA,OAAAuM,UAAA,CAAAtG,MAAA;YAAA;cAGA,IAAAkG,OAAA,CAAArR,KAAA;gBACAuR,GAAA,CAAAG,eAAA,GAAAL,OAAA,CAAA5U,MAAA;cACA;gBACA8U,GAAA,CAAA/G,cAAA,GAAA6G,OAAA,CAAA5U,MAAA;cACA;cACA,IAAA4U,OAAA,CAAAnR,MAAA;gBACAqR,GAAA,CAAA3G,cAAA,GAAAyG,OAAA,CAAArV,UAAA;cACA;gBACAuV,GAAA,CAAA3G,cAAA,GAAA7K,aAAA,CAAAA,aAAA,KACAsR,OAAA,CAAArV,UAAA;kBACA2V,UAAA,EAAAN,OAAA,CAAA/S,SAAA;kBACAsT,OAAA,EAAAP,OAAA,CAAA9S,MAAA;kBACAsT,eAAA,EAAAR,OAAA,CAAAhO,KAAA;gBAAA,EACA;cACA;cACAgO,OAAA,CAAAvU,SAAA;cAEArF,6BAAA,CAAA8Z,GAAA,EAAArP,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAA8F,SAAA;kBACAoJ,OAAA,CAAAvU,SAAA;kBACAuU,OAAA,CAAArM,QAAA;oBACAC,OAAA;oBACAC,IAAA;kBACA;kBACAmM,OAAA,CAAA5J,SAAA;gBACA;kBACA4J,OAAA,CAAArM,QAAA;oBACAC,OAAA,EAAA9C,GAAA,CAAAsG,OAAA;oBACAvD,IAAA;kBACA;kBACAmM,OAAA,CAAAvU,SAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA2U,UAAA,CAAApP,IAAA;UAAA;QAAA,GAAAiP,SAAA;MAAA;IACA;IACAJ,eAAA,WAAAA,gBAAA;MAAA,IAAAY,OAAA;MACA;MACA,IAAAhB,SAAA,GAAA3E,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAA5P,MAAA;MACAqU,SAAA,GAAAA,SAAA,CAAArK,MAAA,WAAAlG,IAAA;QAAA,OAAAA,IAAA,CAAAyO,cAAA;MAAA;MAAA,IAAA+C,KAAA,YAAAA,MAAA,EACA;UACA,IAAAzD,OAAA,GAAAwC,SAAA,CAAAkB,CAAA;UACA,IAAA9I,IAAA;UACA,KAAAoF,OAAA,CAAA3E,eAAA;YACAmI,OAAA,CAAA9M,QAAA;cACAC,OAAA;cACAC,IAAA;YACA;YAAA;cAAAzE,CAAA,EACA;gBAAA2C,MAAA;cAAA;YAAA;UACA;UACA,IAAA0O,OAAA,CAAAlR,aAAA,KAAA0N,OAAA,CAAAlF,iBAAA,IAAAkF,OAAA,CAAA5C,IAAA,iBAAAoG,OAAA,CAAA1M,OAAA;YACA,IAAA6M,GAAA;YACA,IAAAH,OAAA,CAAAhR,MAAA;cACA,IAAAwN,OAAA,CAAA5J,qBAAA;gBACAoN,OAAA,CAAA9M,QAAA;kBACAC,OAAA,EAAAgN,GAAA;kBACA/M,IAAA;gBACA;gBAAA;kBAAAzE,CAAA,EACA;oBAAA2C,MAAA;kBAAA;gBAAA;cACA;YACA;cACA0O,OAAA,CAAA9M,QAAA;gBACAC,OAAA,EAAAgN,GAAA;gBACA/M,IAAA;cACA;cAAA;gBAAAzE,CAAA,EACA;kBAAA2C,MAAA;gBAAA;cAAA;YACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAAkL,OAAA,CAAA1E,yBAAA,IAAA0E,OAAA,CAAA1E,yBAAA,KAAA0E,OAAA,CAAA3E,eAAA;YACAmI,OAAA,CAAA9M,QAAA;cACAC,OAAA,6EAAAiN,MAAA,CAAAJ,OAAA,CAAA9R,KAAA;cACAkF,IAAA;YACA;YAAA;cAAAzE,CAAA,EACA;gBAAA2C,MAAA;cAAA;YAAA;UACA;UACA,IAAAkL,OAAA,CAAAjF,sBAAA,IAAAiF,OAAA,CAAAjF,sBAAA,KAAAiF,OAAA,CAAAlF,iBAAA;YACA0I,OAAA,CAAA9M,QAAA;cACAC,OAAA;cACAC,IAAA;YACA;YAAA;cAAAzE,CAAA,EACA;gBAAA2C,MAAA;cAAA;YAAA;UACA;UACA,IAAAmJ,WAAA,GAAAmC,KAAA,CAAAyD,IAAA,KAAAC,GAAA,CAAA9D,OAAA,CAAA3E,eAAA,CAAA6C,KAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UAAA,IAAA6F,MAAA,YAAAA,OAAA,EAEA;YACA,IAAApQ,IAAA,GAAAsK,WAAA,CAAA+F,CAAA;YACA,IAAAC,aAAA,GAAAjE,OAAA,CAAAU,cAAA;YACA,IAAAwD,MAAA;YACA,IAAAlE,OAAA,CAAAmE,gBAAA;cACAD,MAAA,GAAAlE,OAAA,CAAAmE,gBAAA,CAAAhM,MAAA,WAAAhG,CAAA;gBAAA,OAAAA,CAAA,CAAAuK,YAAA,KAAA/I,IAAA;cAAA;YACA;YACA,IAAAyQ,UAAA,GAAAF,MAAA,CAAAG,MAAA,WAAAC,GAAA,EAAA/M,GAAA;cACA,OAAA+M,GAAA,IAAA/M,GAAA,CAAAgN,WAAA;YACA;YACA,IAAAH,UAAA,GAAAH,aAAA;cACArJ,IAAA;cAAA;YAEA;cAAA,IAAA4J,MAAA;cACA,CAAAA,MAAA,GAAA5J,IAAA,EAAA/D,IAAA,CAAA4N,KAAA,CAAAD,MAAA,EAAAE,kBAAA,CAAAR,MAAA;YACA;UACA;UAhBA,SAAAF,CAAA,MAAAA,CAAA,GAAA/F,WAAA,CAAAL,MAAA,EAAAoG,CAAA;YAAA,IAAAD,MAAA,IAYA;UAAA;UAMA,IAAAY,QAAA,GAAAvI,MAAA,CAAAwI,IAAA,CAAA5E,OAAA,EAAA7H,MAAA,WAAA0M,CAAA;YAAA,OAAAA,CAAA,CAAAC,UAAA,CAAA9E,OAAA;UAAA;UACA2E,QAAA,CAAArN,OAAA,WAAArF,IAAA;YACA,OAAA+N,OAAA,CAAA/N,IAAA;UACA;UACA,OAAA+N,OAAA;UACA,OAAAA,OAAA;UACA,OAAAA,OAAA;UACAA,OAAA,CAAAmE,gBAAA,GAAAvJ,IAAA;QACA;QAAAmK,IAAA;MAvGA,SAAArB,CAAA,MAAAA,CAAA,GAAAlB,SAAA,CAAA5E,MAAA,EAAA8F,CAAA;QAAAqB,IAAA,GAAAtB,KAAA;QAAA,IAAAsB,IAAA,SAAAA,IAAA,CAAA5S,CAAA;MAAA;MAwGA;QAAAqQ,SAAA,EAAAA,SAAA;QAAA1N,MAAA;MAAA;IACA;IACA+N,eAAA,WAAAA,gBAAAL,SAAA,EAAAH,OAAA;MAAA,IAAA2C,OAAA;MAAA,OAAAlS,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAiS,UAAA;QAAA,IAAAC,IAAA,EAAAjC,GAAA,EAAA1E,CAAA,EAAA4G,MAAA,EAAAC,YAAA;QAAA,OAAArS,mBAAA,GAAAG,IAAA,UAAAmS,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAjS,IAAA,GAAAiS,UAAA,CAAAhS,IAAA;YAAA;cACAuC,OAAA,CAAAC,GAAA;cACAoP,IAAA,GAAA7b,yBAAA;cACA4Z,GAAA;cACAA,GAAA,CAAA/G,cAAA,GAAAsG,SAAA;cACAjE,CAAA;cACA,KAAA4G,MAAA,IAAAH,OAAA,CAAA/G,WAAA;gBACA,IAAA+G,OAAA,CAAA/G,WAAA,CAAAsH,cAAA,CAAAJ,MAAA;kBACA5G,CAAA,CAAA1H,IAAA,CAAAmO,OAAA,CAAA/G,WAAA,CAAAkH,MAAA;gBACA;cACA;cACAlC,GAAA,CAAA1G,YAAA,GAAAgC,CAAA;cAEA,IAAAyG,OAAA,CAAApT,MAAA;gBACAqR,GAAA,CAAA3G,cAAA,GAAA0I,OAAA,CAAAtX,UAAA;cACA;gBACAuV,GAAA,CAAA3G,cAAA,GAAA7K,aAAA,CAAAA,aAAA,KACAuT,OAAA,CAAAtX,UAAA;kBACA2V,UAAA,EAAA2B,OAAA,CAAAhV,SAAA;kBACAsT,OAAA,EAAA0B,OAAA,CAAA/U,MAAA;kBACAsT,eAAA,EAAAyB,OAAA,CAAAjQ,KAAA;gBAAA,EACA;cACA;cACAqQ,YAAA;cACAvP,OAAA,CAAAC,GAAA,QAAAmN,GAAA;cAAAqC,UAAA,CAAAhS,IAAA;cAAA,OAEA4R,IAAA,CAAAjC,GAAA,EAAArP,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAA8F,SAAA;kBACA,KAAA0I,OAAA;oBACA2C,OAAA,CAAAxW,SAAA;oBACAwW,OAAA,CAAAtO,QAAA;sBACAC,OAAA;sBACAC,IAAA;oBACA;oBACAoO,OAAA,CAAA7L,SAAA;kBACA;oBACA6L,OAAA,CAAAQ,oBAAA,GAAA3R,GAAA,CAAAC,IAAA;oBACAsR,YAAA;oBACAvP,OAAA,CAAAC,GAAA;kBACA;gBACA;kBACAkP,OAAA,CAAAlW,WAAA;kBACAkW,OAAA,CAAAxW,SAAA;kBACAwW,OAAA,CAAAtO,QAAA;oBACAC,OAAA,EAAA9C,GAAA,CAAAsG,OAAA;oBACAvD,IAAA;kBACA;gBACA;cACA;YAAA;cACAf,OAAA,CAAAC,GAAA;cAAA,OAAAwP,UAAA,CAAAzI,MAAA,WACAuI,YAAA;YAAA;YAAA;cAAA,OAAAE,UAAA,CAAAvR,IAAA;UAAA;QAAA,GAAAkR,SAAA;MAAA;IACA;IACAQ,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,KAAAjX,aAAA;MACA8S,UAAA;QACA,IAAAoE,aAAA,OAAA7B,GAAA,CAAA4B,OAAA,CAAApX,iBAAA,CAAAuM,GAAA,WAAA1I,CAAA;UAAA,OAAAA,CAAA,CAAAgM,IAAA;QAAA;QACAuH,OAAA,CAAAvX,MAAA,GAAAuX,OAAA,CAAAvX,MAAA,CAAAgK,MAAA,WAAAlG,IAAA;UACA,IAAA2T,UAAA,GAAAD,aAAA,CAAAE,GAAA,CAAA5T,IAAA,CAAAkM,IAAA;UACA,IAAAyH,UAAA;YACA,IAAAnJ,GAAA,GAAAiJ,OAAA,CAAAhE,SAAA,CAAAzP,IAAA;YACA,OAAAyT,OAAA,CAAAlR,SAAA,CAAAiI,GAAA;UACA;UACA,QAAAmJ,UAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACAF,OAAA,CAAAjX,aAAA;MACA;IACA;IACA2I,WAAA,WAAAA,YAAA;MAAA,IAAA0O,OAAA;MAAA,OAAAhT,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA+S,UAAA;QAAA,OAAAhT,mBAAA,GAAAG,IAAA,UAAA8S,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA5S,IAAA,GAAA4S,UAAA,CAAA3S,IAAA;YAAA;cAAA2S,UAAA,CAAA3S,IAAA;cAAA,OACApK,wBAAA;gBACA0N,IAAA;cACA,GAAAhD,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAA8F,SAAA;kBACAmM,OAAA,CAAAnW,WAAA,GAAAkE,GAAA,CAAAC,IAAA;gBACA;kBACAgS,OAAA,CAAApP,QAAA;oBACAC,OAAA,EAAA9C,GAAA,CAAAsG,OAAA;oBACAvD,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAqP,UAAA,CAAAlS,IAAA;UAAA;QAAA,GAAAgS,SAAA;MAAA;IACA;IACAG,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,KAAAtE,KAAA,eAAAC,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;QACA,IAAAqE,qBAAA,GAAAD,OAAA,CAAAvD,eAAA;UAAAJ,SAAA,GAAA4D,qBAAA,CAAA5D,SAAA;UAAA1N,MAAA,GAAAsR,qBAAA,CAAAtR,MAAA;QACA,KAAAA,MAAA;QACAqR,OAAA,CAAAE,QAAA;UACA5E,iBAAA;UACA6E,gBAAA;UACA1P,IAAA;QACA,GAAAhD,IAAA;UACAuS,OAAA,CAAAI,iBAAA,CAAA/D,SAAA;QACA,GAAAgE,KAAA;UACAL,OAAA,CAAAzP,QAAA;YACAE,IAAA;YACAD,OAAA;UACA;QACA;MACA;IACA;IACA4P,iBAAA,WAAAA,kBAAA;MAAA,IAAAE,OAAA;MAAA,OAAA3T,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA0T,UAAA;QAAA,IAAAC,kBAAA;QAAA,IAAAlE,SAAA,EAAAmE,UAAA;QAAA,OAAA7T,mBAAA,GAAAG,IAAA,UAAA2T,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAzT,IAAA,GAAAyT,UAAA,CAAAxT,IAAA;YAAA;cACAmT,OAAA,CAAAjY,SAAA;cAAA,OAAAmY,kBAAA,GACAF,OAAA,CAAA/Y,UAAA,cAAAiZ,kBAAA,eAAAA,kBAAA,CAAAhZ,cAAA;gBAAAmZ,UAAA,CAAAxT,IAAA;gBAAA;cAAA;cAAAwT,UAAA,CAAAxT,IAAA;cAAA,OACAmT,OAAA,CAAAzE,SAAA;YAAA;cAAAS,SAAA,GAAAqE,UAAA,CAAAtR,IAAA;cACAK,OAAA,CAAAC,GAAA,sBAAA2M,SAAA;cACAA,SAAA,IAAAgE,OAAA,CAAAM,QAAA,CAAAN,OAAA,CAAA/Y,UAAA,CAAA0E,EAAA;cAAA0U,UAAA,CAAAxT,IAAA;cAAA;YAAA;cAAAwT,UAAA,CAAAxT,IAAA;cAAA,OAEAmT,OAAA,CAAAzE,SAAA;YAAA;cAAAS,UAAA,GAAAqE,UAAA,CAAAtR,IAAA;cACAiN,UAAA,IAAAgE,OAAA,CAAAM,QAAA,CAAAN,OAAA,CAAAjB,oBAAA;YAAA;YAAA;cAAA,OAAAsB,UAAA,CAAA/S,IAAA;UAAA;QAAA,GAAA2S,SAAA;MAAA;IAEA;IACAK,QAAA,WAAAA,SAAAC,YAAA;MAAA,IAAAC,OAAA;MACA7d,qBAAA;QACA8d,eAAA,EAAAF;MACA,GAAApT,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAA8F,SAAA;UACAsN,OAAA,CAAAvQ,QAAA;YACAC,OAAA;YACAC,IAAA;UACA;UACAqQ,OAAA,CAAA9N,SAAA;QACA;UACA8N,OAAA,CAAAvQ,QAAA;YACAC,OAAA,EAAA9C,GAAA,CAAAsG,OAAA;YACAvD,IAAA;UACA;QACA;MACA,GAAAuQ,OAAA,WAAAtC,CAAA;QACAoC,OAAA,CAAAzY,SAAA;MACA,GAAAgY,KAAA,WAAA3B,CAAA;QACAoC,OAAA,CAAAzY,SAAA;MACA;IACA;IACA4Y,WAAA,WAAAA,YAAAhb,KAAA;MAAA,IAAAib,OAAA;MACAxR,OAAA,CAAAC,GAAA,UAAA1J,KAAA;MACA,IACAkb,MAAA,GAMAlb,KAAA,CANAkb,MAAA;QACA/J,GAAA,GAKAnR,KAAA,CALAmR,GAAA;QAAAgK,eAAA,GAKAnb,KAAA,CAJAob,QAAA;QACApV,EAAA,GAAAmV,eAAA,CAAAnV,EAAA;QACAqV,YAAA,GAAAF,eAAA,CAAAE,YAAA;MAGA,IAAAH,MAAA;QAAA,IAAAI,gBAAA;QACA,KAAAA,gBAAA,GAAAtb,KAAA,CAAAob,QAAA,cAAAE,gBAAA,eAAAA,gBAAA,CAAAtV,EAAA;UACAmL,GAAA,CAAApC,aAAA,GAAAsM,YAAA;UACAlK,GAAA,CAAAtC,WAAA,GAAA7I,EAAA;UACA,KAAAuV,OAAA,CAAApK,GAAA,EAAAnL,EAAA;QACA;UACAmL,GAAA,CAAApC,aAAA;UACAoC,GAAA,CAAAtC,WAAA;QACA;MACA;QACA,KAAA3M,iBAAA,CAAAgJ,OAAA,WAAArF,IAAA;UAAA,IAAA2V,gBAAA;UACA,KAAAA,gBAAA,GAAAxb,KAAA,CAAAob,QAAA,cAAAI,gBAAA,eAAAA,gBAAA,CAAAxV,EAAA;YACAH,IAAA,CAAAkJ,aAAA,GAAAsM,YAAA;YACAxV,IAAA,CAAAgJ,WAAA,GAAA7I,EAAA;YACAiV,OAAA,CAAAM,OAAA,CAAA1V,IAAA,EAAAG,EAAA;UACA;YACAH,IAAA,CAAAkJ,aAAA;YACAlJ,IAAA,CAAAgJ,WAAA;UACA;QACA;MACA;IACA;IACA0M,OAAA,WAAAA,QAAApK,GAAA,EAAAnL,EAAA;MACA,IAAAmL,GAAA,aAAAA,GAAA,eAAAA,GAAA,CAAArC,qBAAA;QACA,IAAAqC,GAAA,CAAArC,qBAAA,KAAA9I,EAAA;UACAmL,GAAA,CAAAlC,eAAA;QACA;MACA;QACAkC,GAAA,CAAAlC,eAAA;MACA;IACA;IACAwM,mBAAA,WAAAA,oBAAAP,MAAA,EAAA/J,GAAA;MAAA,IAAAuK,OAAA;MACA,KAAA1Y,KAAA,GAAAkY,MAAA;MACA,KAAArY,gBAAA;MACA,KAAAE,MAAA;MACA,KAAAP,aAAA;MACA,KAAAmZ,SAAA,WAAAlD,CAAA;QACAiD,OAAA,CAAAjG,KAAA,YAAAxM,SAAA,CAAAiS,MAAA,EAAA/J,GAAA;MACA;IACA;IACAyK,gBAAA,WAAAA,iBAAAC,UAAA;MAAA,IAAAC,OAAA;MACA,WAAA5N,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACArQ,kBAAA;UACA8d,UAAA,EAAAA,UAAA;UACArR,IAAA;QACA,GAAAhD,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAA8F,SAAA;YACA,IAAApG,QAAA,GAAAM,GAAA,CAAAC,IAAA,CAAA+G,GAAA,WAAA1I,CAAA;cAAA,OAAAA,CAAA,CAAAoE,IAAA;YAAA;YACAgE,OAAA,CAAAhH,QAAA;UACA;YACA2U,OAAA,CAAAxR,QAAA;cACAC,OAAA,EAAA9C,GAAA,CAAAsG,OAAA;cACAvD,IAAA;YACA;UACA;QACA;MACA;IACA;IACAuR,UAAA,WAAAA,WAAAxU,IAAA,EAAAsU,UAAA;MAAA,IAAAG,OAAA;MACA,WAAA9N,OAAA,WAAAC,OAAA;QACA,IAAA0I,GAAA;UACAoF,cAAA,EAAA1U,IAAA;UACAiD,IAAA;QACA;QACA,IAAAwR,OAAA,CAAAlQ,eAAA;UACA+K,GAAA,CAAAgF,UAAA,GAAAA,UAAA;QACA;QACAhe,cAAA,CAAAgZ,GAAA,EAAArP,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAA8F,SAAA;YACA,IAAA9F,GAAA,CAAAC,IAAA,CAAAA,IAAA,IAAAD,GAAA,CAAAC,IAAA,CAAAA,IAAA,CAAA8J,MAAA;cACA,IAAA0K,IAAA,GAAAzU,GAAA,CAAAC,IAAA,CAAAA,IAAA;cACA,IAAAyU,QAAA,GAAAD,IAAA,CAAAE,QAAA,IAAAF,IAAA,CAAAE,QAAA,CAAAC,OAAA;cACAlO,OAAA,CAAAgO,QAAA;YACA;cACAhO,OAAA,CAAAhL,SAAA;YACA;UACA;YACA6Y,OAAA,CAAA1R,QAAA;cACAC,OAAA,EAAA9C,GAAA,CAAAsG,OAAA;cACAvD,IAAA;YACA;UACA;QACA;MACA;IACA;IACA8R,WAAA,WAAAA,YAAAnL,GAAA;MACA,KAAAwB,WAAA,CAAAxB,GAAA;IACA;IACAwB,WAAA,WAAAA,YAAAxB,GAAA;MACA,IAAAoL,eAAA,GAAAvM,MAAA,CAAAwI,IAAA,CAAArH,GAAA,EACApF,MAAA,WAAAhG,CAAA;QAAA,QAAAA,CAAA,CAAAyW,QAAA,WAAAzW,CAAA,CAAA2S,UAAA,CAAAvH,GAAA,CAAAY,IAAA,KAAAhM,CAAA,CAAAyL,MAAA,GAAAL,GAAA,CAAAY,IAAA,CAAAP,MAAA;MAAA;MACA+K,eAAA,CAAArR,OAAA,WAAAuR,GAAA;QACA,IAAAC,OAAA,GAAAD,GAAA,CAAA3K,KAAA,CAAA9S,YAAA;QACA,IAAA2d,UAAA,GAAAJ,eAAA,CAAAxQ,MAAA,WAAA6Q,CAAA;UACA,IAAArV,IAAA,GAAAqV,CAAA,CAAA9K,KAAA,CAAA9S,YAAA;UACA,OAAA4d,CAAA,KAAAH,GAAA,IAAAlV,IAAA,KAAAmV,OAAA;QACA,GAAAzE,MAAA,WAAAC,GAAA,EAAArS,IAAA;UACA,OAAAqS,GAAA,GAAAta,OAAA,CAAAuT,GAAA,CAAAtL,IAAA,GAAA7F,KAAA;QACA;QACAmR,GAAA,CAAAsL,GAAA,GAAAzd,YAAA,YAAAmS,GAAA,CAAAmD,cAAA,GAAAqI,UAAA;MACA;IACA;IACAE,WAAA,WAAAA,YAAAC,IAAA;MAAA,IAAAC,GAAA,GAAAD,IAAA,CAAAC,GAAA;QAAAC,GAAA,GAAAF,IAAA,CAAAE,GAAA;MACA,IAAA3G,SAAA;MACA,SAAAiB,CAAA,MAAAA,CAAA,GAAAyF,GAAA,CAAAvL,MAAA,EAAA8F,CAAA;QACA,IAAAzR,IAAA,GAAAkX,GAAA,CAAAzF,CAAA;QACA,IAAAzR,IAAA,CAAAoX,YAAA,IAAApX,IAAA,CAAAoX,YAAA,KAAAD,GAAA;UACA3G,SAAA;UACA;QACA;QACAxQ,IAAA,CAAAoJ,eAAA,GAAA+N,GAAA;MACA;MACA,KAAA3G,SAAA;QACA,KAAA/L,QAAA;UACAC,OAAA;UACAC,IAAA;QACA;MACA;IACA;IACA0S,gBAAA,WAAAA,iBAAA/L,GAAA,EAAA6L,GAAA;MAAA,IAAAG,IAAA;QAAAC,OAAA;MACA,IAAAJ,GAAA;QACA7L,GAAA,CAAAlC,eAAA,GAAA+N,GAAA;MACA;QACAA,GAAA,GAAA7L,GAAA,CAAAlC,eAAA;MACA;MACA,IAAAT,IAAA,KAAA2O,IAAA,GAAAH,GAAA,cAAAG,IAAA,uBAAAA,IAAA,CAAArL,KAAA;MACA,KAAAvO,WAAA,CAAA2H,OAAA,WAAA0I,OAAA,EAAA3J,GAAA;QACA,IAAAkB,GAAA,GAAAqD,IAAA,CAAAlI,IAAA,WAAA+W,CAAA;UAAA,OAAAA,CAAA,KAAAzJ,OAAA,CAAAtD,YAAA;QAAA;QACA,IAAA/I,IAAA,GAAA6V,OAAA,CAAA9K,YAAA,CAAAnB,GAAA,CAAAY,IAAA,EAAA6B,OAAA,CAAAtD,YAAA,EAAAsD,OAAA,CAAArB,eAAA;QACA,IAAAC,GAAA,GAAA4K,OAAA,CAAA3K,eAAA,CAAAtB,GAAA,CAAAY,IAAA,EAAA6B,OAAA,CAAAtD,YAAA,EAAAsD,OAAA,CAAArB,eAAA;QACA,IAAApH,GAAA;UACA,KAAAgG,GAAA,CAAA5J,IAAA;YACA6V,OAAA,CAAAhM,IAAA,CAAAD,GAAA,EAAA5J,IAAA;YACA6V,OAAA,CAAAhM,IAAA,CAAAD,GAAA,EAAAqB,GAAA,EAAArB,GAAA,CAAAmD,cAAA;UACA;QACA;UACA8I,OAAA,CAAAE,OAAA,CAAAnM,GAAA,EAAA5J,IAAA;UACA6V,OAAA,CAAAE,OAAA,CAAAnM,GAAA,EAAAqB,GAAA;QACA;MACA;IACA;IACA+K,mBAAA,WAAAA,oBAAAC,UAAA,EAAAC,WAAA;MACA,KAAAD,UAAA;MACA,IAAAhP,IAAA,IAAAgP,UAAA,aAAAA,UAAA,uBAAAA,UAAA,CAAA1L,KAAA;MACA,SAAAtD,IAAA,CAAAlI,IAAA,WAAAP,CAAA;QAAA,OAAAA,CAAA,KAAA0X,WAAA;MAAA;IACA;IAEA5R,cAAA,WAAAA,eAAAtE,IAAA;MAAA,IAAAmW,OAAA;MAAA,OAAAhX,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA+W,UAAA;QAAA,OAAAhX,mBAAA,GAAAG,IAAA,UAAA8W,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA5W,IAAA,GAAA4W,UAAA,CAAA3W,IAAA;YAAA;cAAA2W,UAAA,CAAA3W,IAAA;cAAA,OACA3J,aAAA;gBACAgK,IAAA,EAAAA;cACA,GAAAC,IAAA,WAAAC,GAAA;gBACA,IAAA8F,SAAA,GAAA9F,GAAA,CAAA8F,SAAA;kBAAA7F,IAAA,GAAAD,GAAA,CAAAC,IAAA;kBAAAqG,OAAA,GAAAtG,GAAA,CAAAsG,OAAA;gBACA,IAAAR,SAAA;kBACAmQ,OAAA,CAAA1b,QAAA,GAAAgO,MAAA,CAAAC,MAAA,KAAAyN,OAAA,CAAA1b,QAAA,EAAA0F,IAAA,CAAAoW,IAAA;kBACA,IAAAtP,IAAA,GAAA9G,IAAA,CAAAqW,UAAA;kBACAL,OAAA,CAAArT,WAAA,GAAAmE,IAAA,CAAA1I,IAAA,WAAAD,IAAA;oBAAA,OAAAA,IAAA,CAAAsE,IAAA;kBAAA;kBACAuT,OAAA,CAAAM,YAAA,GAAAxP,IAAA,CAAA1I,IAAA,WAAAD,IAAA;oBAAA,OAAAA,IAAA,CAAAsE,IAAA;kBAAA;kBACAuT,OAAA,CAAA5b,OAAA,GAAA4b,OAAA,CAAAO,gBAAA,CAAAzP,IAAA;gBACA;kBACAkP,OAAA,CAAApT,QAAA;oBACAC,OAAA,EAAAwD,OAAA;oBACAvD,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAqT,UAAA,CAAAlW,IAAA;UAAA;QAAA,GAAAgW,SAAA;MAAA;IACA;IACAM,gBAAA,WAAAA,iBAAAzP,IAAA;MACA,OAAAA,IAAA,CAAAzC,MAAA,WAAAhG,CAAA;QAAA,OAAAA,CAAA,CAAAmY,UAAA;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAC,aAAA;MAAA,IAAAlN,GAAA,GAAAiN,KAAA,CAAAjN,GAAA;QAAAmN,MAAA,GAAAF,KAAA,CAAAE,MAAA;QAAAC,WAAA,GAAAH,KAAA,CAAAG,WAAA;MACA,SAAAhZ,MAAA;MACA,IAAAkY,WAAA,IAAAY,aAAA,GAAAC,MAAA,CAAAE,KAAA,cAAAH,aAAA,uBAAAA,aAAA,CAAAvM,KAAA;MACA,YAAAyL,mBAAA,CAAApM,GAAA,CAAAlC,eAAA,EAAAwO,WAAA;IACA;IACAgB,aAAA,WAAAA,cAAAjU,IAAA,EAAA2G,GAAA;MAAA,IAAAuN,OAAA;MACA,SAAA5S,eAAA;QACA,IAAAtB,IAAA;UACA,IAAAmU,QAAA,QAAAC,qBAAA;UACA,KAAAD,QAAA;QACA;MACA;MACA,KAAA3b,KAAA,GAAAwH,IAAA;MACA,KAAA3H,gBAAA;MACA,KAAAE,MAAA;MACA,KAAAP,aAAA;MACA,KAAAmZ,SAAA,WAAAlD,CAAA;QACAiG,OAAA,CAAAjJ,KAAA,YAAAoJ,OAAA,CAAArU,IAAA,UAAA2G,GAAA,IAAAuN,OAAA,CAAAxc,iBAAA,EAAAsI,IAAA,SAAA2G,GAAA,CAAAlC,eAAA;MACA;IACA;IACA2P,qBAAA,WAAAA,sBAAA;MACA,IAAAE,QAAA;MACA,IAAAC,MAAA,QAAA7c,iBAAA,IAAA6M,aAAA;MACA,SAAAuI,CAAA,MAAAA,CAAA,QAAApV,iBAAA,CAAAsP,MAAA,EAAA8F,CAAA;QACA,IAAAzR,IAAA,QAAA3D,iBAAA,CAAAoV,CAAA;QACA,IAAAzR,IAAA,CAAAkJ,aAAA,KAAAgQ,MAAA;UACAD,QAAA;UACA;QACA;MACA;MACA,KAAAA,QAAA;QACA,KAAAxU,QAAA;UACAC,OAAA;UACAC,IAAA;QACA;MACA;MACA,OAAAsU,QAAA;IACA;IACAE,gBAAA,WAAAA,iBAAAxU,IAAA,EAAAuS,GAAA;MACA,IAAAkC,WAAA;MACA,SAAA3H,CAAA,MAAAA,CAAA,GAAAyF,GAAA,CAAAvL,MAAA,EAAA8F,CAAA;QACA,IAAAzR,IAAA,GAAAkX,GAAA,CAAAzF,CAAA;QACA,KAAAzR,IAAA,CAAAkJ,aAAA;UACAkQ,WAAA;UACA;QACA;MACA;MACA,KAAAA,WAAA;QACA,KAAA3U,QAAA;UACAC,OAAA;UACAC,IAAA;QACA;MACA;MACA,OAAAyU,WAAA;IACA;IACAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MAAA,IAAA3U,IAAA,GAAA+G,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAApO,SAAA,GAAAoO,SAAA;MACA,KAAAvO,KAAA;MAEA,KAAAH,gBAAA;MACA,KAAAE,MAAA;MACA,KAAAN,YAAA;MAEA,KAAA4Q,WAAA;MAEA,KAAAsI,SAAA,WAAAlD,CAAA;QACA0G,OAAA,CAAA1J,KAAA,UAAA2J,QAAA;MACA;IACA;IACA/L,WAAA,WAAAA,YAAA;MACA,IAAAgM,UAAA,QAAAtd,MAAA,CAAAgK,MAAA,WAAAZ,GAAA;QAAA,OAAAA,GAAA,CAAAkJ,KAAA;MAAA,GAAA5F,GAAA,WAAA1I,CAAA;QAAA,OAAAA,CAAA,CAAAsO,KAAA;MAAA;MACA,KAAAiL,eAAA,CAAAD,UAAA;IACA;IACA/M,YAAA,WAAAA,aAAAP,IAAA,EAAA0L,WAAA,EAAA8B,SAAA;MACA,UAAA/H,MAAA,CAAAzF,IAAA,EAAAyF,MAAA,CAAAxY,YAAA,EAAAwY,MAAA,CAAAiG,WAAA,EAAAjG,MAAA,CAAAxY,YAAA,EAAAwY,MAAA,CAAA+H,SAAA;IACA;IACA9M,eAAA,WAAAA,gBAAAV,IAAA,EAAA0L,WAAA,EAAA8B,SAAA;MACA,YAAAjN,YAAA,CAAAP,IAAA,EAAA0L,WAAA,EAAA8B,SAAA,OAAA/H,MAAA,CAAAxY,YAAA;IACA;IACAwgB,gBAAA,WAAAA,iBAAAzZ,CAAA;MACA,IAAAA,CAAA;QACA,KAAA0Y,aAAA;MACA,WAAA1Y,CAAA;QACA,KAAA0Z,qBAAA;MACA;IACA;IACA9L,iBAAA,WAAAA,kBAAAnF,IAAA;MAAA,IAAAkR,OAAA;MAAA,OAAAhZ,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA+Y,UAAA;QAAA,IAAAC,KAAA,EAAAC,MAAA,EAAAxP,GAAA,EAAAyP,aAAA;QAAA,OAAAnZ,mBAAA,GAAAG,IAAA,UAAAiZ,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA/Y,IAAA,GAAA+Y,UAAA,CAAA9Y,IAAA;YAAA;cACA0Y,KAAA,GAAAtH,kBAAA,KAAAZ,GAAA,CAAAlJ,IAAA,CAAAC,GAAA,WAAA1I,CAAA;gBAAA,OAAAA,CAAA,CAAAgO,eAAA;cAAA;cAAA8L,MAAA,gBAAAlZ,mBAAA,GAAAC,IAAA,UAAAiZ,OAAAxP,GAAA;gBAAA,OAAA1J,mBAAA,GAAAG,IAAA,UAAAmZ,QAAAC,UAAA;kBAAA,kBAAAA,UAAA,CAAAjZ,IAAA,GAAAiZ,UAAA,CAAAhZ,IAAA;oBAAA;sBAEA,IAAAwY,OAAA,CAAArX,YAAA,CAAA8Q,cAAA,CAAA9I,GAAA;wBACAuP,KAAA,GAAAA,KAAA,CAAA7T,MAAA,WAAAxE,IAAA;0BAAA,OAAAA,IAAA,KAAA8I,GAAA;wBAAA;sBACA;oBAAA;oBAAA;sBAAA,OAAA6P,UAAA,CAAAvY,IAAA;kBAAA;gBAAA,GAAAkY,MAAA;cAAA;cAAAG,UAAA,CAAAG,EAAA,GAAAxZ,mBAAA,GAAA6R,IAAA,CAHAkH,OAAA,CAAArX,YAAA;YAAA;cAAA,KAAA2X,UAAA,CAAAI,EAAA,GAAAJ,UAAA,CAAAG,EAAA,IAAAE,IAAA;gBAAAL,UAAA,CAAA9Y,IAAA;gBAAA;cAAA;cAAAmJ,GAAA,GAAA2P,UAAA,CAAAI,EAAA,CAAApgB,KAAA;cAAA,OAAAggB,UAAA,CAAAM,aAAA,CAAAT,MAAA,CAAAxP,GAAA;YAAA;cAAA2P,UAAA,CAAA9Y,IAAA;cAAA;YAAA;cAAA8Y,UAAA,CAAA9Y,IAAA;cAAA,OAKAwY,OAAA,CAAAa,eAAA,CAAAX,KAAA;YAAA;cAAAE,aAAA,GAAAE,UAAA,CAAA5W,IAAA;cACA4G,MAAA,CAAAC,MAAA,CAAAyP,OAAA,CAAArX,YAAA,EAAAyX,aAAA;YAAA;YAAA;cAAA,OAAAE,UAAA,CAAArY,IAAA;UAAA;QAAA,GAAAgY,SAAA;MAAA;IACA;IACAF,qBAAA,WAAAA,sBAAA;MAAA,IAAAe,OAAA;MAAA,OAAA9Z,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6Z,UAAA;QAAA,IAAAC,WAAA,EAAAC,OAAA,EAAAC,WAAA,EAAAC,SAAA,EAAAC,eAAA;QAAA,OAAAna,mBAAA,GAAAG,IAAA,UAAAia,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA/Z,IAAA,GAAA+Z,UAAA,CAAA9Z,IAAA;YAAA;cACAwZ,WAAA,YAAAA,YAAA;gBACAF,OAAA,CAAAlW,QAAA;kBACAC,OAAA;kBACAC,IAAA;gBACA;cACA;cACAmW,OAAA,GAAAH,OAAA,CAAAte,iBAAA,CAAAuM,GAAA,WAAA1I,CAAA;gBAAA,OAAAA,CAAA,CAAAgO,eAAA;cAAA,GAAAhI,MAAA,WAAAhG,CAAA;gBAAA,SAAAA,CAAA;cAAA;cAAA,IACA4a,OAAA,CAAAnP,MAAA;gBAAAwP,UAAA,CAAA9Z,IAAA;gBAAA;cAAA;cACAsZ,OAAA,CAAAlW,QAAA;gBACAC,OAAA;gBACAC,IAAA;cACA;cAAA,OAAAwW,UAAA,CAAAvQ,MAAA;YAAA;cAAAuQ,UAAA,CAAA9Z,IAAA;cAAA,OAGAsZ,OAAA,CAAA7M,iBAAA,CAAA6M,OAAA,CAAAte,iBAAA;YAAA;cACA0e,WAAA,GAAA5M,KAAA,CAAAyD,IAAA,KAAAC,GAAA,CAAA8I,OAAA,CAAAte,iBAAA,CAAAuM,GAAA,WAAA1I,CAAA;gBAAA,OAAAA,CAAA,CAAA8I,WAAA;cAAA,GAAA9C,MAAA,WAAAhG,CAAA;gBAAA,SAAAA,CAAA;cAAA;cACA8a,SAAA;cACA,IAAAD,WAAA,CAAApP,MAAA;gBACAoP,WAAA,CAAA1V,OAAA,WAAA2Q,UAAA;kBACAgF,SAAA,CAAApW,IAAA,CAAA+V,OAAA,CAAA5E,gBAAA,CAAAC,UAAA,EAAArU,IAAA,WAAA+H,MAAA;oBAAA,OAAA0R,eAAA,KACApF,UAAA,EAAAtM,MAAA;kBAAA,CACA;gBACA;gBACAuR,eAAA,GAAA5S,OAAA,CAAAgT,GAAA,CAAAL,SAAA,EAAArZ,IAAA,WAAA2Z,MAAA;kBACA,OAAAnR,MAAA,CAAAC,MAAA,CAAAoI,KAAA,CAAArI,MAAA,OAAAwH,MAAA,CAAAc,kBAAA,CAAA6I,MAAA;gBACA;gBACAL,eAAA,CAAAtZ,IAAA,WAAA4Z,QAAA;kBACA,IAAAC,IAAA;kBAAA,IAAAC,MAAA,YAAAA,OAAA,EACA;oBACA,IAAAC,MAAA,GAAAf,OAAA,CAAAte,iBAAA,CAAAoV,CAAA;oBACA,IAAAkK,eAAA,GAAAJ,QAAA,CAAAG,MAAA,CAAA1S,WAAA;oBACA,IAAA4S,UAAA,GAAAjB,OAAA,CAAAnY,YAAA,CAAAkZ,MAAA,CAAAxN,eAAA;oBACA,IAAA0N,UAAA;sBACA,IAAAC,UAAA,GAAAD,UAAA,CAAA1X,KAAA,WAAA5C,OAAA;wBAAA,OAAAqa,eAAA,CAAAnW,QAAA,CAAAlE,OAAA;sBAAA;sBACA,KAAAua,UAAA;wBACAL,IAAA;wBAAA;sBAEA;sBACAE,MAAA,CAAAtS,eAAA,GAAAwS,UAAA,CAAArN,IAAA;oBACA;kBACA;kBAZA,SAAAkD,CAAA,MAAAA,CAAA,GAAAkJ,OAAA,CAAAte,iBAAA,CAAAsP,MAAA,EAAA8F,CAAA;oBAAA,IAAAgK,MAAA,IAQA;kBAAA;kBAKA,KAAAD,IAAA;oBACAlM,UAAA;sBACAqL,OAAA,CAAApL,MAAA;wBACAC,iBAAA;sBACA;oBACA;kBACA;kBAEAgM,IAAA,IAAAX,WAAA;gBACA;cACA;gBACAF,OAAA,CAAAte,iBAAA,CAAAgJ,OAAA,WAAAqW,MAAA;kBACA,IAAAE,UAAA,GAAAjB,OAAA,CAAAnY,YAAA,CAAAkZ,MAAA,CAAAxN,eAAA;kBACA,IAAA0N,UAAA;oBACAF,MAAA,CAAAtS,eAAA,GAAAwS,UAAA,CAAArN,IAAA;kBACA;gBACA;gBACAsM,WAAA;cACA;YAAA;YAAA;cAAA,OAAAM,UAAA,CAAArZ,IAAA;UAAA;QAAA,GAAA8Y,SAAA;MAAA;IACA;IACAkB,gBAAA,WAAAA,iBAAAnX,IAAA,EAAA2G,GAAA;MAAA,IAAAyQ,OAAA;MACA,KAAA5e,KAAA;MACA,KAAAH,gBAAA;MACA,KAAAE,MAAA;MACA,KAAAP,aAAA;MACA,KAAAmZ,SAAA,WAAAlD,CAAA;QACAmJ,OAAA,CAAAnM,KAAA,YAAAoM,SAAA,CAAArX,IAAA,QAAAA,IAAA,UAAA2G,GAAA,IAAAyQ,OAAA,CAAA1f,iBAAA;MACA;IACA;IACAqe,eAAA,WAAAA,gBAAA;MAAA,IAAAuB,OAAA;MAAA,IAAAC,OAAA,GAAAxQ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAApO,SAAA,GAAAoO,SAAA;MACAwQ,OAAA,GAAAA,OAAA,CAAAhW,MAAA,WAAAhG,CAAA;QAAA,SAAAA,CAAA;MAAA;MACA,KAAAgc,OAAA,CAAAvQ,MAAA,SAAAtD,OAAA,CAAAC,OAAA;MACA,WAAAD,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACAtQ,gCAAA;UACAkkB,eAAA,EAAAD,OAAA;UACA/Q,IAAA;QACA,GAAAxJ,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAA8F,SAAA;YACA,IAAA0U,MAAA,GAAAxa,GAAA,CAAAC,IAAA;YACA,IAAAwa,KAAA,GAAAD,MAAA,CAAAhK,MAAA,WAAAC,GAAA,EAAArS,IAAA;cACAqS,GAAA,CAAArS,IAAA,CAAAsE,IAAA,IAAAtE,IAAA,CAAAoJ,eAAA;cACA,OAAAiJ,GAAA;YACA;YACAzO,OAAA,CAAAC,GAAA,UAAAwY,KAAA;YACA/T,OAAA,CAAA+T,KAAA;UACA;YACAJ,OAAA,CAAAxX,QAAA;cACAC,OAAA,EAAA9C,GAAA,CAAAsG,OAAA;cACAvD,IAAA;YACA;YACA4D,MAAA;UACA;QACA;MACA;IACA;IACA+T,aAAA,WAAAA,cAAA;MACA,IAAAhX,GAAA;MACA,KAAApJ,MAAA,CAAAmJ,OAAA,WAAA0I,OAAA,EAAA3J,GAAA;QACA2J,OAAA,CAAAwO,OAAA,IAAAxO,OAAA,CAAAwO,OAAA;QACA,IAAAxO,OAAA,CAAAwO,OAAA;UACAjX,GAAA,CAAAV,IAAA,CAAAmJ,OAAA;QACA;MACA;MACA,KAAA1R,iBAAA,GAAAiJ,GAAA;MACA,SAAAjJ,iBAAA,CAAAsP,MAAA,UAAAzP,MAAA,CAAAyP,MAAA;QACA,KAAAiE,KAAA,WAAA4M,iBAAA;MACA;MACA,SAAAngB,iBAAA,CAAAsP,MAAA;QACA,KAAAiE,KAAA,WAAA4M,iBAAA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAlZ,OAAA,WAAAA,QAAA;MAAA,IAAAmZ,OAAA;MACA,IAAAC,WAAA,YAAAA,YAAA;QACA,IAAAC,GAAA,GAAAF,OAAA,CAAAhd,KAAA,GAAA7G,eAAA,GAAAN,eAAA;QACAqkB,GAAA,KAAAhb,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAA8F,SAAA;YACA,IAAAgC,MAAA,GAAA9H,GAAA,CAAAC,IAAA;YACA,KAAA4a,OAAA,CAAAhd,KAAA;cACAiK,MAAA,GAAAA,MAAA,CACAd,GAAA,WAAA1I,CAAA,EAAAkE,GAAA;gBACA;kBACAvC,IAAA,EAAA3B,CAAA,CAAAE,IAAA;kBACAwc,KAAA,EAAA1c,CAAA,CAAAE;gBACA;cACA;YACA;YACAqc,OAAA,CAAAre,uBAAA,CAAA/E,IAAA,GAAAqQ,MAAA;YACA+S,OAAA,CAAA3G,SAAA,WAAAlD,CAAA;cAAA,IAAAiK,qBAAA;cACA,CAAAA,qBAAA,GAAAJ,OAAA,CAAA7M,KAAA,CAAAkN,uBAAA,cAAAD,qBAAA,eAAAA,qBAAA,CAAAE,iBAAA,CAAArT,MAAA;YACA;UACA;YACA+S,OAAA,CAAAhY,QAAA;cACAC,OAAA,EAAA9C,GAAA,CAAAsG,OAAA;cACAvD,IAAA;YACA;UACA;QACA;MACA;MAEA+X,WAAA;IACA;IACA;IACAM,SAAA,WAAAA,UAAA1R,GAAA;MAAA,IAAA2R,OAAA;MACArZ,OAAA,CAAAC,GAAA,QAAAyH,GAAA;MACA,IAAA0F,GAAA;MACA,SAAAvR,KAAA;QACAuR,GAAA,CAAAkM,OAAA,GAAA5R,GAAA,CAAAnH,qBAAA;MACA;QACA6M,GAAA,CAAAmM,OAAA,GAAA7R,GAAA,CAAAJ,iBAAA;MACA;MACAjS,mBAAA;QAAAmkB,cAAA,EAAApM,GAAA,CAAAmM;MAAA,GAAAxb,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAA8F,SAAA;UACAuV,OAAA,CAAAI,aAAA,GAAAzb,GAAA,CAAAC,IAAA,IAAAyb,aAAA;UACAL,OAAA,CAAArjB,OAAA,GAAAgI,GAAA,CAAAC,IAAA,IAAAjI,OAAA;UACAqjB,OAAA,CAAApjB,WAAA,GAAA+H,GAAA,CAAAC,IAAA,IAAA0b,QAAA;UACAN,OAAA,CAAAnjB,UAAA,GAAAwR,GAAA,CAAAkS,SAAA;UACAP,OAAA,CAAAljB,eAAA,GAAAuR,GAAA,CAAAmS,cAAA;UACAR,OAAA,CAAAS,QAAA;QACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC,cAAA,WAAAA,eAAAtH,IAAA;MACA,KAAA3L,iBAAA,CAAA2L,IAAA;IACA;IACAuH,cAAA,WAAAA,eAAA;MACA,KAAAhO,KAAA,eAAAiO,WAAA;MACA,KAAAjO,KAAA,CAAAkO,MAAA,CAAAC,WAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,OAAA;MACA,KAAA5hB,iBAAA;MACA,IAAA6a,GAAA;MACA,SAAAzX,KAAA;QACAyX,GAAA,CAAAtS,IAAA;MACA;QACAsS,GAAA,CAAAtS,IAAA;MACA;MAEA,IAAAkZ,MAAA,QAAAlO,KAAA,CAAAkO,MAAA;MACAA,MAAA,CAAAI,gBAAA;MACAhH,GAAA,CAAA7R,OAAA,WAAA0I,OAAA,EAAA3J,GAAA;QACA,IAAAqU,MAAA,GAAAqF,MAAA,CAAAK,gBAAA,CAAApQ,OAAA;QACA,IAAAA,OAAA;UACA0K,MAAA,CAAA2F,OAAA,CAAA/Y,OAAA,WAAAgZ,MAAA,EAAAja,GAAA;YACAia,MAAA,CAAA9B,OAAA,GAAAnY,GAAA,MAAA6Z,OAAA,CAAAjjB,SAAA,CAAAM,YAAA;UACA;QACA;QACA,IAAAyS,OAAA;UACA,IAAAsQ,MAAA,GAAA5F,MAAA,CAAA2F,OAAA;UACAC,MAAA,CAAAhlB,IAAA,GAAA4kB,OAAA,CAAAjjB,SAAA,CAAAK,gBAAA;UACAgjB,MAAA,CAAA9B,OAAA;QACA;QAEA,IAAAxO,OAAA,oBAAAA,OAAA;UACA,IAAAsQ,OAAA,GAAA5F,MAAA,CAAA2F,OAAA;UACAC,OAAA,CAAAhlB,IAAA,GAAA4kB,OAAA,CAAAjjB,SAAA,CAAAI,aAAA;UACAijB,OAAA,CAAA9B,OAAA;QACA;QACA,IAAAxO,OAAA;UACA,IAAAsQ,QAAA,GAAA5F,MAAA,CAAA2F,OAAA;UACAC,QAAA,CAAAhlB,IAAA,GAAA4kB,OAAA,CAAAjjB,SAAA,CAAAC,WAAA;UACAojB,QAAA,CAAA9B,OAAA;QACA;QACA,IAAAxO,OAAA;UACA,IAAAsQ,QAAA,GAAA5F,MAAA,CAAA2F,OAAA;UACAC,QAAA,CAAAhlB,IAAA,GAAA4kB,OAAA,CAAAjjB,SAAA,CAAAE,QAAA;UACAmjB,QAAA,CAAA9B,OAAA;QACA;QACA,IAAAxO,OAAA;UACA,IAAAsQ,QAAA,GAAA5F,MAAA,CAAA2F,OAAA;UACAC,QAAA,CAAAhlB,IAAA,GAAA4kB,OAAA,CAAAjjB,SAAA,CAAAG,WAAA;UACAkjB,QAAA,CAAA9B,OAAA;QACA;MACA;MACAuB,MAAA,CAAAQ,UAAA;IACA;IACAC,qBAAA,WAAAA,sBAAAC,KAAA;MAAA,IAAAH,MAAA,GAAAG,KAAA,CAAAH,MAAA;QAAA/S,GAAA,GAAAkT,KAAA,CAAAlT,GAAA;MACA,SAAAtQ,SAAA,CAAAM,YAAA;QACA;MACA;MACA,OAAAgQ,GAAA,CAAAmT,YAAA,WAAAzjB,SAAA,CAAAM,YAAA;IACA;IACAojB,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAN,MAAA,GAAAM,KAAA,CAAAN,MAAA;QAAA/S,GAAA,GAAAqT,KAAA,CAAArT,GAAA;MACA,SAAAtQ,SAAA,CAAAK,gBAAA,CAAAujB,IAAA;QACA;MACA;MACA,IAAAC,aAAA,YAAAA,cAAAC,KAAA;QAAA,OAAAA,KAAA,CAAAF,IAAA,GAAApI,OAAA,cAAAvK,KAAA;MAAA;MACA,IAAA8S,SAAA,GAAAF,aAAA,MAAA7jB,SAAA,CAAAK,gBAAA;MACA,OAAA0jB,SAAA,CAAAte,IAAA,WAAAiB,IAAA;QAAA,QAAA4J,GAAA,CAAA0T,IAAA,QAAAxZ,QAAA,CAAA9D,IAAA;MAAA;IACA;IAEAud,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAb,MAAA,GAAAa,KAAA,CAAAb,MAAA;QAAA/S,GAAA,GAAA4T,KAAA,CAAA5T,GAAA;MACA,SAAAtQ,SAAA,CAAAI,aAAA,CAAAwjB,IAAA;QACA;MACA;MAEA,IAAAC,aAAA,YAAAA,cAAAC,KAAA;QAAA,OAAAA,KAAA,CAAAF,IAAA,GAAApI,OAAA,cAAAvK,KAAA;MAAA;MAEA,IAAA3G,GAAA,QAAA7F,KAAA;MAEA,IAAAyX,GAAA,GAAA2H,aAAA,MAAA7jB,SAAA,CAAAI,aAAA;MAEA,SAAAG,SAAA;QACA,OAAA2b,GAAA,CAAAzW,IAAA,WAAAiB,IAAA;UAAA,OAAA4J,GAAA,CAAAhG,GAAA,MAAA5D,IAAA;QAAA;MACA;QACA,SAAA+P,CAAA,MAAAA,CAAA,GAAAyF,GAAA,CAAAvL,MAAA,EAAA8F,CAAA;UACA,IAAAzR,IAAA,GAAAkX,GAAA,CAAAzF,CAAA;UACA,IAAAnG,GAAA,CAAAhG,GAAA,EAAAE,QAAA,CAAAxF,IAAA;YACA;UACA;QACA;QACA;MACA;IACA;IACAmf,mBAAA,WAAAA,oBAAAC,KAAA;MAAA,IAAAf,MAAA,GAAAe,KAAA,CAAAf,MAAA;QAAA/S,GAAA,GAAA8T,KAAA,CAAA9T,GAAA;MACA,IAAA+S,MAAA,CAAAhlB,IAAA;QACA;MACA;MACA,OAAAiS,GAAA,CAAA/F,YAAA,KAAA8Y,MAAA,CAAAhlB,IAAA;IACA;IACAgmB,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAjB,MAAA,GAAAiB,KAAA,CAAAjB,MAAA;QAAA/S,GAAA,GAAAgU,KAAA,CAAAhU,GAAA;MACA,IAAA+S,MAAA,CAAAhlB,IAAA;QACA;MACA;MACA,OAAAiS,GAAA,CAAA5F,SAAA,KAAA2Y,MAAA,CAAAhlB,IAAA;IACA;IACAkmB,mBAAA,WAAAA,oBAAAC,KAAA;MAAA,IAAAnB,MAAA,GAAAmB,KAAA,CAAAnB,MAAA;QAAA/S,GAAA,GAAAkU,KAAA,CAAAlU,GAAA;MACA,IAAA+S,MAAA,CAAAhlB,IAAA;QACA;MACA;MACA,OAAAiS,GAAA,CAAA7F,gBAAA,KAAA4Y,MAAA,CAAAhlB,IAAA;IACA;IACAomB,mBAAA,WAAAA,oBAAAC,CAAA;MAAA,IAAAC,WAAA;MACA,CAAAA,WAAA,QAAA/P,KAAA,cAAA+P,WAAA,eAAAA,WAAA,CAAA7C,uBAAA,CAAA8C,SAAA,CAAAF,CAAA;IACA;IACAG,wBAAA,WAAAA,yBAAApY,EAAA;MAAA,IAAAqY,OAAA;MACA,UAAA9hB,MAAA;QACA,KAAAF,iBAAA;QACA,KAAAe,WAAA;MACA;QACA,KAAAA,WAAA;QACAnG,wBAAA;UAAA2Y,OAAA,OAAArT;QAAA,GAAA2D,IAAA,WAAAC,GAAA;UACAke,OAAA,CAAAhiB,iBAAA,GAAA8D,GAAA,CAAAC,IAAA;UACA,IAAAie,OAAA,CAAAhiB,iBAAA,CAAA6N,MAAA;YACAmU,OAAA,CAAArkB,UAAA,CAAAI,cAAA,GAAAikB,OAAA,CAAAhiB,iBAAA,IAAAqC,EAAA;UACA;UACA2f,OAAA,CAAAjhB,WAAA;QACA;MACA;IACA;IACAkhB,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MACA,UAAA9jB,MAAA,CAAAyP,MAAA;QACA,KAAAiE,KAAA,eAAAiO,WAAA;QACA,KAAAjO,KAAA,CAAAkO,MAAA,CAAAC,WAAA;QACA;MACA;MACA,KAAA3J,QAAA;QACA5E,iBAAA;QACA6E,gBAAA;QACA1P,IAAA;MACA,GAAAhD,IAAA;QACAqe,OAAA,CAAA9jB,MAAA;QACA8jB,OAAA,CAAApC,cAAA;MACA,GAAArJ,KAAA;QACAyL,OAAA,CAAAvb,QAAA;UACAE,IAAA;UACAD,OAAA;QACA;MACA;IACA;IACAub,mBAAA,WAAAA,oBAAA3U,GAAA;MACA,SAAA/K,MAAA;QACA,SAAA+K,GAAA,CAAAnH,qBAAA;MACA;QACA,aAAAzE,MAAA,IAAA4L,GAAA,CAAAH,IAAA;MACA;IACA;IACA+U,YAAA,WAAAA,aAAA;MACA,UAAAhkB,MAAA,CAAAyP,MAAA;QACA,KAAAlH,QAAA;UACAC,OAAA;UACAC,IAAA;QACA;QACA;MACA;MACA,KAAAiL,KAAA,CAAAkO,MAAA,CAAAqC,UAAA;QACAC,QAAA,8BAAAzO,MAAA,MAAAlW,UAAA,CAAAC,cAAA;QACAiJ,IAAA;QACAtL,IAAA,OAAA6C;MACA;IACA;IACAmkB,iBAAA,WAAAA,kBAAA;MACA,KAAA/mB,MAAA;IACA;IACAokB,QAAA,WAAAA,SAAA;MACA,KAAAlkB,SAAA,GAAA1B,MAAA;MACA,KAAA4B,SAAA,MAAAiY,MAAA,CACA,KAAAlQ,UAAA,oCAAAkQ,MAAA,CACA3Y,OAAA,eAAA2Y,MAAA,CAAA3O,YAAA,CAAAC,OAAA,CACA,OACA,gBAAA0O,MAAA,CAAA3O,YAAA,CAAAC,OAAA;MACA,KAAA3J,MAAA;IACA;IACAgnB,YAAA,WAAAA,aAAA;MACA,IAAAhD,aAAA,QAAAD,aAAA;MACA,IAAAzjB,OAAA,QAAAA,OAAA;MACA,KAAAF,SAAA,MAAAiY,MAAA,CACA,KAAAlQ,UAAA,oCAAAkQ,MAAA,CACA3Y,OAAA,eAAA2Y,MAAA,CAAA3O,YAAA,CAAAC,OAAA,CACA,OACA,gBAAA0O,MAAA,CAAA3O,YAAA,CAAAC,OAAA;MACA,KAAAxJ,YAAA,GAAA6jB,aAAA;MACA,KAAA3jB,SAAA,GAAAC,OAAA;IACA;IACA2mB,UAAA,WAAAA,WAAArgB,CAAA;MACA,KAAAsgB,WAAA,MAAA7O,MAAA,CACA,KAAAlQ,UAAA,oCAAAkQ,MAAA,CACA3Y,OAAA,eAAA2Y,MAAA,CAAA3O,YAAA,CAAAC,OAAA,CACA,OACA,gBAAA0O,MAAA,CAAA3O,YAAA,CAAAC,OAAA;MACA,KAAA1J,UAAA;IACA;IACAmK,aAAA,WAAAA,cAAA+c,KAAA;MAAA,IAAApnB,IAAA,GAAAonB,KAAA,CAAApnB,IAAA;MACA,IAAAA,IAAA,CAAAsL,IAAA;QACAf,OAAA,CAAAC,GAAA,SAAAxK,IAAA;QACAuK,OAAA,CAAA8c,KAAA,CACA,sBACArnB,IAAA,CAAAA,IAAA,CAAAsnB,QAAA,EAAAC,OAAA,CACAvnB,IAAA,CAAAA,IAAA,CAAAsnB,QAAA,CACA;QACA,IAAAtnB,IAAA,CAAAA,IAAA,CAAAsnB,QAAA;UACAE,QAAA,CAAAC,cAAA,UAAAC,aAAA,CAAAC,WAAA,CACA;YACArc,IAAA;YACAsc,IAAA;YACAve,KAAA;cACA;cACAwe,KAAA,OAAAtnB,OAAA;cACAmE,SAAA,OAAAhE,eAAA;cACAonB,SAAA,OAAArnB,UAAA;cACAsnB,OAAA,OAAAvnB,WAAA;cACAwnB,aAAA;cACAC,MAAA;cACA;cACA;cACA;YACA;UACA,GACA,GACA;QACA,WAAAjoB,IAAA,CAAAA,IAAA,CAAAsnB,QAAA;UACAE,QAAA,CAAAC,cAAA,cAAAC,aAAA,CAAAC,WAAA,CACA;YACArc,IAAA;YACAsc,IAAA;YACAve,KAAA;cACA;cACAwe,KAAA,OAAAtnB,OAAA;cACAmE,SAAA,OAAAhE,eAAA;cACAonB,SAAA,OAAArnB,UAAA;cACAsnB,OAAA,OAAAvnB,WAAA;cACAwnB,aAAA;cACAC,MAAA;cACA;cACA;YACA;UACA,GACA,GACA;QACA;MACA;IACA;EAAA;AAEA", "ignoreList": []}]}
import { GetBomLevelList } from '@/api/PRO/bom-level'

// code根据层级配置来，URL中level同Code,只有部件存在level
// 0:零件BOM ，-1：一级BOM,1:二级BOM,2:三级BOM,3:四级BOM
export async function GetBOMInfo(code = null) {
  const res = await GetBomLevelList()
  if (res.IsSucceed) {
    const list = (res.Data || []).filter(v => v.Is_Enabled).map(item => {
      item.Sort = parseInt(item.Sort)
      return item
    }).sort((a, b) => a.Sort - b.Sort)

    const comInfo = list.find(v => v.Code === '-1')
    const partInfo = list.find(v => v.Code === '0')

    const comName = comInfo?.Display_Name || ''
    const partName = partInfo?.Display_Name || ''

    let currentBOMInfo = null
    let currentParentBOMInfo = null

    if (typeof code === 'number') {
      const index = list.findIndex(v => v.Code === code.toString())
      currentBOMInfo = list[index]
      currentParentBOMInfo = list[index - 1]
    }

    return {
      comName,
      partName,
      list,
      currentBOMInfo,
      currentParentBOMInfo
    }
  }
}

{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\addDraft.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\addDraft.vue", "mtime": 1757468127991}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRHcmlkQnlDb2RlIH0gZnJvbSAnQC9hcGkvc3lzJw0KaW1wb3J0IHsgR2V0Q2FuU2NoZHVsaW5nQ29tcHMgfSBmcm9tICdAL2FwaS9QUk8vcHJvZHVjdGlvbi10YXNrJw0KaW1wb3J0IHsgR2V0Q2FuU2NoZHVsaW5nUGFydHMsIEdldFBhcnRMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL3Byb2R1Y3Rpb24tcGFydCcNCmltcG9ydCB7IHY0IGFzIHV1aWR2NCB9IGZyb20gJ3V1aWQnDQppbXBvcnQgeyBkZWJvdW5jZSwgZGVlcENsb25lIH0gZnJvbSAnQC91dGlscycNCmltcG9ydCB7IHRhYmxlUGFnZVNpemUgfSBmcm9tICdAL3ZpZXdzL1BSTy9zZXR0aW5nJw0KaW1wb3J0IHsgR2V0Q29tcFR5cGVUcmVlIH0gZnJvbSAnQC9hcGkvUFJPL3Byb2Zlc3Npb25hbFR5cGUnDQppbXBvcnQgeyBHZXRQYXJ0VHlwZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vcGFydFR5cGUnDQppbXBvcnQgVHJlZURldGFpbCBmcm9tICdAL2NvbXBvbmVudHMvVHJlZURldGFpbC9pbmRleC52dWUnDQppbXBvcnQgRXhwYW5kYWJsZVNlY3Rpb24gZnJvbSAnQC9jb21wb25lbnRzL0V4cGFuZGFibGVTZWN0aW9uL2luZGV4LnZ1ZScNCmltcG9ydCB7IEdldEluc3RhbGxVbml0SWROYW1lTGlzdCwgR2V0UHJvamVjdEFyZWFUcmVlTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9wcm9qZWN0Jw0KaW1wb3J0IHsgZ2V0VW5pcXVlIH0gZnJvbSAnLi4vY29uc3RhbnQnDQppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCcNCmltcG9ydCB7IGZpbmRBbGxQYXJlbnROb2RlIH0gZnJvbSAnQC91dGlscy90cmVlJw0KaW1wb3J0IHsgR2V0U3RvcExpc3QgfSBmcm9tICdAL2FwaS9QUk8vcHJvZHVjdGlvbi10YXNrJw0KY29uc3QgU1BMSVRfU1lNQk9MID0gJyRfJCcNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7IEV4cGFuZGFibGVTZWN0aW9uLCBUcmVlRGV0YWlsIH0sDQogIHByb3BzOiB7DQogICAgc2NoZWR1bGVJZDogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJycNCiAgICB9LA0KICAgIHBhZ2VUeXBlOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnY29tJw0KICAgIH0sDQogICAgc2hvd0RpYWxvZzogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfSwNCg0KICAgIGluc3RhbGxJZDogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJycNCiAgICB9LA0KICAgIGN1cnJlbnRJZHM6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICcnDQogICAgfSwNCg0KICAgIGlzUGFydFByZXBhcmU6IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiBmYWxzZQ0KICAgIH0sDQogICAgY29tTmFtZTogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJycNCiAgICB9LA0KICAgIHBhcnROYW1lOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnJw0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgcGFnZUluZm86IHsNCiAgICAgICAgcGFnZTogMSwNCiAgICAgICAgcGFnZVNpemU6IDUwMCwNCiAgICAgICAgcGFnZVNpemVzOiB0YWJsZVBhZ2VTaXplLA0KICAgICAgICB0b3RhbDogMA0KICAgICAgfSwNCiAgICAgIGZvcm06IHsNCiAgICAgICAgQ29tcF9Db2RlOiAnJywNCiAgICAgICAgQ29tcF9Db2RlQmx1cjogJycsDQogICAgICAgIFBhcnRfQ29kZUJsdXI6ICcnLA0KICAgICAgICBQYXJ0X0NvZGU6ICcnLA0KICAgICAgICBUeXBlX05hbWU6ICcnLA0KICAgICAgICBJbnN0YWxsVW5pdF9JZDogW10sDQogICAgICAgIFNwZWM6ICcnLA0KICAgICAgICBUeXBlOiAnJw0KICAgICAgfSwNCiAgICAgIGN1clNlYXJjaDogMSwNCiAgICAgIGN1clBhcnRTZWFyY2g6IDEsDQogICAgICBzaG93RXhwYW5kOiB0cnVlLA0KICAgICAgc2VhcmNoQ29udGVudDogJycsDQogICAgICBzZWFyY2hQYXJ0Q29udGVudDogJycsDQogICAgICBzdGF0dXNUeXBlOiAnJywNCiAgICAgIHByb2plY3ROYW1lOiAnJywNCiAgICAgIGV4cGFuZGVkS2V5OiAnJywNCiAgICAgIHN0YXR1c0NvZGU6ICdQYXJ0X1NjaGR1bGVfU3RhdHVzJywNCiAgICAgIGlzT3duZXJOdWxsOiB0cnVlLA0KICAgICAgdGJMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHRyZWVMb2FkaW5nOiBmYWxzZSwNCiAgICAgIGFkZExvYWRpbmc6IGZhbHNlLA0KICAgICAgc2F2ZUxvYWRpbmc6IGZhbHNlLA0KICAgICAgc2hvd1NjOiBmYWxzZSwNCiAgICAgIGluc3RhbGxVbml0SWRMaXN0OiBbXSwNCiAgICAgIGNvbHVtbnM6IFtdLA0KICAgICAgZlRhYmxlOiBbXSwNCiAgICAgIHRiQ29uZmlnOiB7fSwNCiAgICAgIFRvdGFsQ291bnQ6IDAsDQogICAgICBQYWdlOiAwLA0KICAgICAgdG90YWxTZWxlY3Rpb246IFtdLA0KICAgICAgdHJlZURhdGE6IFtdLA0KICAgICAgc2VhcmNoOiAoKSA9PiAoe30pLA0KICAgICAgdHJlZVNlbGVjdFBhcmFtczogew0KICAgICAgICBwbGFjZWhvbGRlcjogJ+ivt+mAieaLqScsDQogICAgICAgIGNsZWFyYWJsZTogdHJ1ZQ0KICAgICAgfSwNCiAgICAgIE9iamVjdFR5cGVMaXN0OiB7DQogICAgICAgIC8vIOaehOS7tuexu+Weiw0KICAgICAgICAnY2hlY2stc3RyaWN0bHknOiB0cnVlLA0KICAgICAgICAnZGVmYXVsdC1leHBhbmQtYWxsJzogdHJ1ZSwNCiAgICAgICAgY2xpY2tQYXJlbnQ6IHRydWUsDQogICAgICAgIGRhdGE6IFtdLA0KICAgICAgICBwcm9wczogew0KICAgICAgICAgIGNoaWxkcmVuOiAnQ2hpbGRyZW4nLA0KICAgICAgICAgIGxhYmVsOiAnTGFiZWwnLA0KICAgICAgICAgIHZhbHVlOiAnRGF0YScNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIGFyZWFJZDogJycsDQogICAgICB0eXBlT3B0aW9uOiBbXQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICBpc0NvbSgpIHsNCiAgICAgIHJldHVybiB0aGlzLnBhZ2VUeXBlID09PSAnY29tJw0KICAgIH0sDQogICAgZmlsdGVyVGV4dCgpIHsNCiAgICAgIHJldHVybiB0aGlzLnByb2plY3ROYW1lICsgU1BMSVRfU1lNQk9MICsgdGhpcy5zdGF0dXNUeXBlDQogICAgfSwNCiAgICAuLi5tYXBHZXR0ZXJzKCdzY2hlZHVsZScsIFsnYWRkVGJLZXlzJ10pDQogIH0sDQogIHdhdGNoOiB7DQogICAgc2hvd0RpYWxvZyhuZXdWYWx1ZSkgew0KICAgICAgbmV3VmFsdWUgJiYgKHRoaXMuc2F2ZUxvYWRpbmcgPSBmYWxzZSkNCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQoNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGluaXREYXRhKCkgew0KICAgICAgY29uc29sZS5sb2coJ2luaXREYXRhJykNCiAgICAgIHRoaXMudGJEYXRhID0gW10NCiAgICAgIHRoaXMuZ2V0Q29uZmlnKCkNCiAgICAgIHRoaXMuZmV0Y2hUcmVlRGF0YSgpDQogICAgICBpZiAodGhpcy5pc0NvbSkgew0KICAgICAgICB0aGlzLmdldE9iamVjdFR5cGVMaXN0KCkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuZ2V0VHlwZSgpDQogICAgICB9DQogICAgICB0aGlzLnNlYXJjaCA9IGRlYm91bmNlKHRoaXMuZmV0Y2hEYXRhLCA4MDAsIHRydWUpDQogICAgICB0aGlzLnNldFBhZ2VEYXRhKCkNCiAgICB9LA0KICAgIGhhbmRsZU5vZGVDbGljayhkYXRhKSB7DQogICAgICBpZiAodGhpcy5hcmVhSWQgPT09IGRhdGEuSWQpIHsNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICBpZiAoIWRhdGEuUGFyZW50Tm9kZXMgfHwgZGF0YS5DaGlsZHJlbj8ubGVuZ3RoID4gMCkgew0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIGlmIChkYXRhPy5EYXRhW3RoaXMuc3RhdHVzQ29kZV0gPT09ICfmnKrlr7zlhaUnKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICfmuIXljZXmnKrlr7zlhaXvvIzor7fogZTns7vmt7HljJbkurrlkZjlr7zlhaXmuIXljZUnLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KQ0KICAgICAgICB0aGlzLmV4cGFuZGVkS2V5ID0gZGF0YS5JZA0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgY29uc3Qgc2V0RGF0YSA9ICh7IERhdGEgfSkgPT4gew0KICAgICAgICB0aGlzLmFyZWFJZCA9IERhdGEuSWQNCiAgICAgICAgdGhpcy5wcm9qZWN0SWQgPSBEYXRhLlByb2plY3RfSWQNCiAgICAgICAgdGhpcy5leHBhbmRlZEtleSA9IHRoaXMuYXJlYUlkDQoNCiAgICAgICAgY29uc3QgX2FyciA9IGZpbmRBbGxQYXJlbnROb2RlKHRoaXMudHJlZURhdGEsIGRhdGEuSWQsIHRydWUpDQogICAgICAgIHRoaXMubm9kZUxhYmVscyA9IF9hcnIuZmlsdGVyKHYgPT4gISF2LlBhcmVudE5vZGVzKS5tYXAocCA9PiBwLkxhYmVsKQ0KDQogICAgICAgIC8vIHRoaXMuZm9ybUlubGluZS5GaW5pc2hfRGF0ZSA9ICcnDQogICAgICAgIC8vIHRoaXMuZm9ybUlubGluZS5JbnN0YWxsVW5pdF9JZCA9ICcnDQogICAgICAgIC8vIHRoaXMuZm9ybUlubGluZS5SZW1hcmsgPSAnJw0KICAgICAgICB0aGlzLmZldGNoRGF0YSgpDQogICAgICAgIC8vIHRoaXMuZ2V0QXJlYUluZm8oKQ0KICAgICAgICB0aGlzLmdldEluc3RhbGxVbml0SWROYW1lTGlzdCgpDQogICAgICB9DQoNCiAgICAgIHNldERhdGEoZGF0YSkNCiAgICB9LA0KICAgIGZldGNoVHJlZURhdGEoKSB7DQogICAgICB0aGlzLnRyZWVMb2FkaW5nID0gdHJ1ZQ0KICAgICAgR2V0UHJvamVjdEFyZWFUcmVlTGlzdCh7IE1lbnVJZDogdGhpcy4kcm91dGUubWV0YS5JZCwgcHJvamVjdE5hbWU6IHRoaXMucHJvamVjdE5hbWUsIHR5cGU6IHRoaXMuaXNDb20gPyAxIDogMiB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICBpZiAocmVzLkRhdGEubGVuZ3RoID09PSAwKSB7DQogICAgICAgICAgICB0aGlzLnRyZWVEYXRhID0gW10NCiAgICAgICAgICAgIHRoaXMudHJlZUxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAgICAgcmV0dXJuDQogICAgICAgICAgfQ0KICAgICAgICAgIGNvbnN0IHJlc0RhdGEgPSByZXMuRGF0YS5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgICBpdGVtLklzX0RpcmVjdG9yeSA9IHRydWUNCiAgICAgICAgICAgIHJldHVybiBpdGVtDQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLnRyZWVEYXRhID0gcmVzRGF0YQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCdzZXRLZXknKQ0KICAgICAgICAgIHRoaXMuc2V0S2V5KCkNCiAgICAgICAgICB0aGlzLnRyZWVMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy50cmVlRGF0YSA9IFtdDQogICAgICAgICAgdGhpcy50cmVlTG9hZGluZyA9IGZhbHNlDQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy50cmVlTG9hZGluZyA9IGZhbHNlDQogICAgICAgIHRoaXMudHJlZURhdGEgPSBbXQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHNldEtleSgpIHsNCiAgICAgIGNvbnN0IGRlZXBGaWx0ZXIgPSAodHJlZSkgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygndHJlZScsIHRyZWUpDQogICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdHJlZS5sZW5ndGg7IGkrKykgew0KICAgICAgICAgIGNvbnN0IGl0ZW0gPSB0cmVlW2ldDQogICAgICAgICAgY29uc3QgeyBEYXRhLCBDaGlsZHJlbiB9ID0gaXRlbQ0KICAgICAgICAgIGNvbnNvbGUubG9nKERhdGEpDQogICAgICAgICAgaWYgKERhdGEuUGFyZW50SWQgJiYgIUNoaWxkcmVuPy5sZW5ndGgpIHsNCiAgICAgICAgICAgIHRoaXMuaGFuZGxlTm9kZUNsaWNrKGl0ZW0pDQogICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgaWYgKENoaWxkcmVuICYmIENoaWxkcmVuPy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgIHJldHVybiBkZWVwRmlsdGVyKENoaWxkcmVuKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGRlZXBGaWx0ZXIodGhpcy50cmVlRGF0YSkNCiAgICB9LA0KICAgIGN1c3RvbUZpbHRlckZ1bih2YWx1ZSwgZGF0YSwgbm9kZSkgew0KICAgICAgY29uc3QgYXJyID0gdmFsdWUuc3BsaXQoU1BMSVRfU1lNQk9MKQ0KICAgICAgY29uc3QgbGFiZWxWYWwgPSBhcnJbMF0NCiAgICAgIGNvbnN0IHN0YXR1c1ZhbCA9IGFyclsxXQ0KICAgICAgaWYgKCF2YWx1ZSkgcmV0dXJuIHRydWUNCiAgICAgIGxldCBwYXJlbnROb2RlID0gbm9kZS5wYXJlbnQNCiAgICAgIGxldCBsYWJlbHMgPSBbbm9kZS5sYWJlbF0NCiAgICAgIGxldCBzdGF0dXMgPSBbZGF0YS5EYXRhW3RoaXMuc3RhdHVzQ29kZV1dDQogICAgICBsZXQgbGV2ZWwgPSAxDQogICAgICB3aGlsZSAobGV2ZWwgPCBub2RlLmxldmVsKSB7DQogICAgICAgIGxhYmVscyA9IFsuLi5sYWJlbHMsIHBhcmVudE5vZGUubGFiZWxdDQogICAgICAgIHN0YXR1cyA9IFsuLi5zdGF0dXMsIGRhdGEuRGF0YVt0aGlzLnN0YXR1c0NvZGVdXQ0KICAgICAgICBwYXJlbnROb2RlID0gcGFyZW50Tm9kZS5wYXJlbnQNCiAgICAgICAgbGV2ZWwrKw0KICAgICAgfQ0KICAgICAgbGFiZWxzID0gbGFiZWxzLmZpbHRlcih2ID0+ICEhdikNCiAgICAgIHN0YXR1cyA9IHN0YXR1cy5maWx0ZXIodiA9PiAhIXYpDQogICAgICBsZXQgcmVzdWx0TGFiZWwgPSB0cnVlDQogICAgICBsZXQgcmVzdWx0U3RhdHVzID0gdHJ1ZQ0KICAgICAgaWYgKHRoaXMuc3RhdHVzVHlwZSkgew0KICAgICAgICByZXN1bHRTdGF0dXMgPSBzdGF0dXMuc29tZShzID0+IHMuaW5kZXhPZihzdGF0dXNWYWwpICE9PSAtMSkNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLnByb2plY3ROYW1lKSB7DQogICAgICAgIHJlc3VsdExhYmVsID0gbGFiZWxzLnNvbWUocyA9PiBzLmluZGV4T2YobGFiZWxWYWwpICE9PSAtMSkNCiAgICAgIH0NCiAgICAgIHJldHVybiByZXN1bHRMYWJlbCAmJiByZXN1bHRTdGF0dXMNCiAgICB9LA0KICAgIGFzeW5jIGdldENvbmZpZygpIHsNCiAgICAgIGxldCBjb2RlID0gJycNCiAgICAgIGNvZGUgPSB0aGlzLmlzQ29tDQogICAgICAgID8gJ1BST0NvbURyYWZ0RWRpdFRiQ29uZmlnJw0KICAgICAgICA6ICdQUk9QYXJ0RHJhZnRFZGl0VGJDb25maWdfbmV3Jw0KICAgICAgYXdhaXQgdGhpcy5nZXRUYWJsZUNvbmZpZyhjb2RlKQ0KICAgICAgLy8gdGhpcy5mZXRjaERhdGEoKQ0KICAgIH0sDQogICAgZmlsdGVyRGF0YShwYWdlKSB7DQogICAgICBjb25zb2xlLmxvZygyMikNCiAgICAgIGNvbnN0IHNwbGl0QW5kQ2xlYW4gPSAoaW5wdXQpID0+IGlucHV0LnRyaW0oKS5yZXBsYWNlKC9ccysvZywgJyAnKS5zcGxpdCgnICcpDQoNCiAgICAgIGlmICh0aGlzLmN1clNlYXJjaCA9PT0gMSkgew0KICAgICAgICB0aGlzLmZvcm0uQ29tcF9Db2RlID0gdGhpcy5zZWFyY2hDb250ZW50DQogICAgICAgIHRoaXMuZm9ybS5Db21wX0NvZGVCbHVyID0gJycNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmN1clNlYXJjaCA9PT0gMCkgew0KICAgICAgICB0aGlzLmZvcm0uQ29tcF9Db2RlQmx1ciA9IHRoaXMuc2VhcmNoQ29udGVudA0KICAgICAgICB0aGlzLmZvcm0uQ29tcF9Db2RlID0gJycNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmN1clBhcnRTZWFyY2ggPT09IDEpIHsNCiAgICAgICAgdGhpcy5mb3JtLlBhcnRfQ29kZUJsdXIgPSAnJw0KICAgICAgICB0aGlzLmZvcm0uUGFydF9Db2RlID0gdGhpcy5zZWFyY2hQYXJ0Q29udGVudA0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuY3VyUGFydFNlYXJjaCA9PT0gMCkgew0KICAgICAgICB0aGlzLmZvcm0uUGFydF9Db2RlID0gJycNCiAgICAgICAgdGhpcy5mb3JtLlBhcnRfQ29kZUJsdXIgPSB0aGlzLnNlYXJjaFBhcnRDb250ZW50DQogICAgICB9DQoNCiAgICAgIGNvbnN0IGYgPSBbXQ0KICAgICAgZm9yIChjb25zdCBmb3JtS2V5IGluIHRoaXMuZm9ybSkgew0KICAgICAgICBpZiAodGhpcy5mb3JtW2Zvcm1LZXldIHx8IHRoaXMuZm9ybVtmb3JtS2V5XSA9PT0gZmFsc2UpIHsNCiAgICAgICAgICBmLnB1c2goZm9ybUtleSkNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgaWYgKCFmLmxlbmd0aCkgew0KICAgICAgICB0aGlzLnNldFBhZ2UoKQ0KICAgICAgICAhcGFnZSAmJiAodGhpcy5wYWdlSW5mby5wYWdlID0gMSkNCiAgICAgICAgdGhpcy5wYWdlSW5mby50b3RhbCA9IHRoaXMudGJEYXRhLmxlbmd0aA0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgY29uc3QgY2hlY2tNYXRjaCA9IChvcmlnaW4sIGNvbXApID0+IHsNCiAgICAgICAgY29uc3QgX2NvbXAgPSBjb21wLm1hcChjb2RlID0+IHsNCiAgICAgICAgICBjb25zdCBba2V5LCB2YWx1ZV0gPSBjb2RlLnNwbGl0KCcmJiYnKQ0KICAgICAgICAgIHJldHVybiBrZXkNCiAgICAgICAgfSkNCiAgICAgICAgY29uc3QgX29yaWdpbiA9IG9yaWdpbi5tYXAoY29kZSA9PiB7DQogICAgICAgICAgY29uc3QgW2tleSwgdmFsdWVdID0gY29kZS5zcGxpdCgnJiYmJykNCiAgICAgICAgICByZXR1cm4ga2V5DQogICAgICAgIH0pDQogICAgICAgIHJldHVybiBfb3JpZ2luLnNvbWUoaXRlbSA9PiB7DQogICAgICAgICAgcmV0dXJuIF9jb21wLnNvbWUodmFsdWUgPT4gaXRlbS5pbmNsdWRlcyh2YWx1ZSkpDQogICAgICAgIH0pDQogICAgICB9DQogICAgICBjb25zdCBjaGVja0V4YWN0TWF0Y2ggPSAob3JpZ2luLCBjb21wKSA9PiB7DQogICAgICAgIGNvbnN0IF9jb21wID0gY29tcC5tYXAoY29kZSA9PiB7DQogICAgICAgICAgY29uc3QgW2tleSwgdmFsdWVdID0gY29kZS5zcGxpdCgnJiYmJykNCiAgICAgICAgICByZXR1cm4ga2V5DQogICAgICAgIH0pDQogICAgICAgIGNvbnN0IF9vcmlnaW4gPSBvcmlnaW4ubWFwKGNvZGUgPT4gew0KICAgICAgICAgIGNvbnN0IFtrZXksIHZhbHVlXSA9IGNvZGUuc3BsaXQoJyYmJicpDQogICAgICAgICAgcmV0dXJuIGtleQ0KICAgICAgICB9KQ0KDQogICAgICAgIHJldHVybiBfb3JpZ2luLnNvbWUoaXRlbSA9PiBfY29tcC5pbmNsdWRlcyhpdGVtKSkNCiAgICAgIH0NCg0KICAgICAgY29uc3QgdGVtVGJEYXRhID0gdGhpcy50YkRhdGEuZmlsdGVyKHYgPT4gew0KICAgICAgICB2LmNoZWNrZWQgPSBmYWxzZQ0KICAgICAgICBjb25zdCBjb21wQ29kZSA9IHYuQ29tcG9uZW50X0NvZGVzIHx8IFtdDQoNCiAgICAgICAgaWYgKHRoaXMuZm9ybS5Db21wX0NvZGUudHJpbSgpKSB7DQogICAgICAgICAgY29uc3QgY29tcENvZGVBcnJheSA9IHNwbGl0QW5kQ2xlYW4odGhpcy5mb3JtLkNvbXBfQ29kZSkNCiAgICAgICAgICBpZiAoY29tcENvZGVBcnJheS5sZW5ndGgpIHsNCiAgICAgICAgICAgIGNvbnN0IGZsYWcgPSBjaGVja0V4YWN0TWF0Y2goY29tcENvZGUsIGNvbXBDb2RlQXJyYXkpDQogICAgICAgICAgICBjb25zb2xlLmxvZyg4ODcsIGNvbXBDb2RlLCBjb21wQ29kZUFycmF5LCBmbGFnKQ0KICAgICAgICAgICAgaWYgKCFmbGFnKSByZXR1cm4gZmFsc2UNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICBpZiAodGhpcy5mb3JtLkNvbXBfQ29kZUJsdXIudHJpbSgpKSB7DQogICAgICAgICAgY29uc3QgY29tcENvZGVBcnJheSA9IHNwbGl0QW5kQ2xlYW4odGhpcy5mb3JtLkNvbXBfQ29kZUJsdXIpDQogICAgICAgICAgaWYgKGNvbXBDb2RlQXJyYXkubGVuZ3RoKSB7DQogICAgICAgICAgICBjb25zdCBmbGFnID0gY2hlY2tNYXRjaChjb21wQ29kZSwgY29tcENvZGVBcnJheSkNCiAgICAgICAgICAgIGlmICghZmxhZykgcmV0dXJuIGZhbHNlDQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgaWYgKHRoaXMuZm9ybS5UeXBlICYmIHYuVHlwZSAhPT0gdGhpcy5mb3JtLlR5cGUpIHsNCiAgICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgICAgfQ0KDQogICAgICAgIGlmICh0aGlzLmZvcm0uUGFydF9Db2RlQmx1ci50cmltKCkpIHsNCiAgICAgICAgICBjb25zdCBwYXJ0Q29kZUJsdXJBcnJheSA9IHNwbGl0QW5kQ2xlYW4odGhpcy5mb3JtLlBhcnRfQ29kZUJsdXIpDQogICAgICAgICAgaWYgKCFwYXJ0Q29kZUJsdXJBcnJheS5zb21lKGNvZGUgPT4gdlsnUGFydF9Db2RlJ10uaW5jbHVkZXMoY29kZSkpKSB7DQogICAgICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICBpZiAodGhpcy5mb3JtLlBhcnRfQ29kZS50cmltKCkpIHsNCiAgICAgICAgICBjb25zdCBwYXJ0Q29kZUFycmF5ID0gc3BsaXRBbmRDbGVhbih0aGlzLmZvcm0uUGFydF9Db2RlKQ0KICAgICAgICAgIGlmICghcGFydENvZGVBcnJheS5pbmNsdWRlcyh2WydQYXJ0X0NvZGUnXSkpIHsNCiAgICAgICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIGlmICh0aGlzLmZvcm0uSW5zdGFsbFVuaXRfSWQubGVuZ3RoICYmICF0aGlzLmZvcm0uSW5zdGFsbFVuaXRfSWQuaW5jbHVkZXModi5JbnN0YWxsVW5pdF9JZCkpIHsNCiAgICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgICAgfQ0KDQogICAgICAgIGlmICh0aGlzLmZvcm0uVHlwZV9OYW1lICE9PSAnJyAmJiB2LlR5cGVfTmFtZSAhPT0gdGhpcy5mb3JtLlR5cGVfTmFtZSkgew0KICAgICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgICB9DQoNCiAgICAgICAgaWYgKHRoaXMuZm9ybS5TcGVjLnRyaW0oKSAhPT0gJycpIHsNCiAgICAgICAgICBjb25zdCBzcGVjQXJyYXkgPSBzcGxpdEFuZENsZWFuKHRoaXMuZm9ybS5TcGVjKQ0KICAgICAgICAgIGlmICghc3BlY0FycmF5LnNvbWUoc3BlYyA9PiB2LlNwZWMuaW5jbHVkZXMoc3BlYykpKSB7DQogICAgICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgaWYgKHRoaXMuc2VhcmNoQ29udGVudC50cmltKCkubGVuZ3RoKSB7DQogICAgICAgICAgbGV0IGNzQ291bnQgPSAwDQoNCiAgICAgICAgICB2LmNvbXBvbmVudE1hcCA9ICh2LkNvbXBvbmVudF9Db2RlcyB8fCBbXSkucmVkdWNlKChhY2MsIGNvZGUpID0+IHsNCiAgICAgICAgICAgIGNvbnN0IFtrZXksIHZhbHVlXSA9IGNvZGUuc3BsaXQoJyYmJicpDQogICAgICAgICAgICBhY2Nba2V5XSA9IHBhcnNlSW50KHZhbHVlKQ0KICAgICAgICAgICAgaWYgKHRoaXMuY3VyU2VhcmNoID09PSAxKSB7DQogICAgICAgICAgICAgIGNvbnN0IGNvbXBDb2RlQXJyYXkgPSBzcGxpdEFuZENsZWFuKHRoaXMuZm9ybS5Db21wX0NvZGUpDQogICAgICAgICAgICAgIGlmIChjb21wQ29kZUFycmF5Lmxlbmd0aCkgew0KICAgICAgICAgICAgICAgIGNvbnN0IGZsYWcgPSBjaGVja0V4YWN0TWF0Y2goW2tleV0sIGNvbXBDb2RlQXJyYXkpDQogICAgICAgICAgICAgICAgaWYgKGZsYWcpIHsNCiAgICAgICAgICAgICAgICAgIGNzQ291bnQgKz0gcGFyc2VJbnQodmFsdWUpDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBjb25zdCBjb21wQ29kZUFycmF5ID0gc3BsaXRBbmRDbGVhbih0aGlzLmZvcm0uQ29tcF9Db2RlQmx1cikNCiAgICAgICAgICAgICAgaWYgKGNvbXBDb2RlQXJyYXkubGVuZ3RoKSB7DQogICAgICAgICAgICAgICAgY29uc3QgZmxhZyA9IGNoZWNrTWF0Y2goW2tleV0sIGNvbXBDb2RlQXJyYXkpDQogICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ3BmbGFnJywga2V5LCBjb21wQ29kZUFycmF5LCBmbGFnLCB2YWx1ZSkNCiAgICAgICAgICAgICAgICBpZiAoZmxhZykgew0KICAgICAgICAgICAgICAgICAgY3NDb3VudCArPSBwYXJzZUludCh2YWx1ZSkNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHJldHVybiBhY2MNCiAgICAgICAgICB9LCB7fSkNCiAgICAgICAgICB0aGlzLiRzZXQodiwgJ2NzQ291bnQnLCBNYXRoLm1pbihjc0NvdW50LCB2LkNhbl9TY2hkdWxpbmdfQ291bnQpKQ0KICAgICAgICAgIHRoaXMuJHNldCh2LCAnY3NDb3VudFdlaWdodCcsIE1hdGgubWluKHYuQ2FuX1NjaGR1bGluZ19XZWlnaHQsIHYuY3NDb3VudCAqIHYuV2VpZ2h0KSkNCg0KICAgICAgICAgIHYuc2VhcmNoY291bnQgPSB2LmNvdW50DQogICAgICAgICAgdi5zZWFyY2hjb3VudE1heCA9IHYubWF4Q291bnQNCiAgICAgICAgICAvLyBjb25zdCBjcyA9IHYuQ29tcG9uZW50X0NvZGVzIHx8IFtdDQogICAgICAgICAgLy8gbGV0IG1pbiA9IDANCiAgICAgICAgICAvLyBjcy5mb3JFYWNoKChlbGVtZW50LCBpZHgpID0+IHsNCiAgICAgICAgICAvLyAgIGNvbnN0IFtrZXksIHZhbHVlXSA9IGVsZW1lbnQuc3BsaXQoJyYmJicpDQogICAgICAgICAgLy8gICBtaW4gPSB2LmNvbXBvbmVudE1hcFtrZXldDQogICAgICAgICAgLy8gfSkNCg0KICAgICAgICAgIHYuY291bnQgPSB2LmNzQ291bnQNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB2LmNvdW50ID0gdi5DYW5fU2NoZHVsaW5nX0NvdW50DQogICAgICAgIH0NCg0KICAgICAgICAvLyB2LkNhbl9TY2hkdWxpbmdfQ291bnQgPSB2LmNzQ291bnQNCiAgICAgICAgLy8gdi5DYW5fU2NoZHVsaW5nX1dlaWdodCA9IHYuY3NDb3VudFdlaWdodA0KDQogICAgICAgIHJldHVybiB0cnVlDQogICAgICB9KQ0KDQogICAgICAhcGFnZSAmJiAodGhpcy5wYWdlSW5mby5wYWdlID0gMSkNCiAgICAgIHRoaXMucGFnZUluZm8udG90YWwgPSB0ZW1UYkRhdGEubGVuZ3RoDQogICAgICB0aGlzLnNldFBhZ2UodGVtVGJEYXRhKQ0KICAgICAgaWYgKHRoaXMuc2VhcmNoQ29udGVudC50cmltKCkubGVuZ3RoKSB7DQogICAgICAgIHRoaXMuc2hvd1NjID0gdHJ1ZQ0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlU2VhcmNoKCkgew0KICAgICAgdGhpcy50b3RhbFNlbGVjdGlvbiA9IFtdDQogICAgICB0aGlzLmNsZWFyU2VsZWN0KCkNCiAgICAgIGlmICh0aGlzLnRiRGF0YT8ubGVuZ3RoKSB7DQogICAgICAgIHRoaXMudGJEYXRhLmZvckVhY2goaXRlbSA9PiBpdGVtLmNoZWNrZWQgPSBmYWxzZSkNCiAgICAgICAgdGhpcy5maWx0ZXJEYXRhKCkNCiAgICAgIH0NCiAgICAgIHRoaXMuc2hvd1NjID0gISF0aGlzLnNlYXJjaENvbnRlbnQudHJpbSgpLmxlbmd0aA0KICAgIH0sDQogICAgdGJTZWxlY3RDaGFuZ2UoYXJyYXkpIHsNCiAgICAgIHRoaXMudG90YWxTZWxlY3Rpb24gPSB0aGlzLnRiRGF0YS5maWx0ZXIodiA9PiB2LmNoZWNrZWQpDQogICAgfSwNCiAgICBjbGVhclNlbGVjdCgpIHsNCiAgICAgIHRoaXMuJHJlZnMueFRhYmxlMS5jbGVhckNoZWNrYm94Um93KCkNCiAgICAgIHRoaXMudG90YWxTZWxlY3Rpb24gPSBbXQ0KICAgIH0sDQogICAgYXN5bmMgZmV0Y2hEYXRhKCkgew0KICAgICAgdGhpcy5oYW5kbGVSZXNldCgpDQogICAgICB0aGlzLnRiTG9hZGluZyA9IHRydWUNCiAgICAgIGlmICh0aGlzLmlzQ29tKSB7DQogICAgICAgIGF3YWl0IHRoaXMuZ2V0Q29tVGJEYXRhKCkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGF3YWl0IHRoaXMuZ2V0UGFydFRiRGF0YSgpDQogICAgICB9DQogICAgICB0aGlzLmluaXRUYkRhdGEoKQ0KICAgICAgdGhpcy5maWx0ZXJEYXRhKCkNCiAgICAgIHRoaXMudGJMb2FkaW5nID0gZmFsc2UNCiAgICB9LA0KICAgIHNldFBhZ2VEYXRhKCkgew0KICAgICAgaWYgKHRoaXMudGJEYXRhPy5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy5wYWdlSW5mby5wYWdlID0gMQ0KICAgICAgICB0aGlzLnRiRGF0YSA9IHRoaXMudGJEYXRhLmZpbHRlcih2ID0+IHYuQ2FuX1NjaGR1bGluZ19Db3VudCA+IDApDQogICAgICAgIHRoaXMuZmlsdGVyRGF0YSgpDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVTYXZlKHR5cGUgPSAyKSB7DQogICAgICBpZiAodHlwZSA9PT0gMSkgew0KICAgICAgICB0aGlzLmFkZExvYWRpbmcgPSB0cnVlDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnNhdmVMb2FkaW5nID0gdHJ1ZQ0KICAgICAgfQ0KICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgIHRoaXMudG90YWxTZWxlY3Rpb24uZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgIGNvbnN0IGludENvdW50ID0gcGFyc2VJbnQoaXRlbS5jb3VudCkNCiAgICAgICAgICBpZiAodGhpcy5zZWFyY2hDb250ZW50LnRyaW0oKS5sZW5ndGgpIHsNCiAgICAgICAgICAgIGl0ZW0uU2NoZHVsZWRfQ291bnQgPSBpdGVtLkNhbl9TY2hkdWxpbmdfQ291bnQNCg0KICAgICAgICAgICAgaXRlbS5tYXhDb3VudCA9IGl0ZW0uQ2FuX1NjaGR1bGluZ19Db3VudA0KICAgICAgICAgICAgaXRlbS5jaG9vc2VDb3VudCA9IGludENvdW50DQogICAgICAgICAgICBpdGVtLmNvdW50ID0gaXRlbS5DYW5fU2NoZHVsaW5nX0NvdW50DQoNCiAgICAgICAgICAgIGl0ZW0uQ2FuX1NjaGR1bGluZ19Db3VudCA9IDANCiAgICAgICAgICAgIGl0ZW0uQ2FuX1NjaGR1bGluZ19XZWlnaHQgPSBpdGVtLkNhbl9TY2hkdWxpbmdfQ291bnQgKiBpdGVtLldlaWdodA0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBpdGVtLlNjaGR1bGVkX0NvdW50ICs9IGludENvdW50DQogICAgICAgICAgICBpdGVtLkNhbl9TY2hkdWxpbmdfQ291bnQgLT0gaW50Q291bnQNCiAgICAgICAgICAgIGl0ZW0uQ2FuX1NjaGR1bGluZ19XZWlnaHQgPSBpdGVtLkNhbl9TY2hkdWxpbmdfQ291bnQgKiBpdGVtLldlaWdodA0KICAgICAgICAgICAgaXRlbS5tYXhDb3VudCA9IGludENvdW50DQogICAgICAgICAgICBpdGVtLmNob29zZUNvdW50ID0gaW50Q291bnQNCiAgICAgICAgICAgIGl0ZW0uY291bnQgPSBpdGVtLkNhbl9TY2hkdWxpbmdfQ291bnQNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBpdGVtLmNoZWNrZWQgPSBmYWxzZQ0KICAgICAgICB9KQ0KICAgICAgICBjb25zdCBjcCA9IGRlZXBDbG9uZSh0aGlzLnRvdGFsU2VsZWN0aW9uKQ0KDQogICAgICAgIC8vIHRoaXMuJGVtaXQoJ3NlbmRTZWxlY3RMaXN0JywgY3ApDQogICAgICAgIHRoaXMuYWRkTG9hZGluZyA9IGZhbHNlDQogICAgICAgIHRoaXMuY2xlYXJTZWxlY3QoKQ0KICAgICAgICAvLyB0aGlzLnNldFBhZ2UoKQ0KICAgICAgICB0aGlzLnNldFBhZ2VEYXRhKCkNCiAgICAgICAgaWYgKHR5cGUgPT09IDIpIHsNCiAgICAgICAgICB0aGlzLiRlbWl0KCdzZW5kU2VsZWN0TGlzdCcsIGNwKQ0KICAgICAgICAgIHRoaXMuJGVtaXQoJ2Nsb3NlJykNCiAgICAgICAgICB0aGlzLmZUYWJsZSA9IFtdDQogICAgICAgICAgdGhpcy50YkRhdGEgPSBbXQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJGVtaXQoJ2FkZFRvVGJMaXN0JywgY3ApDQogICAgICAgIH0NCiAgICAgIH0sIDApDQogICAgfSwNCiAgICBpbml0VGJEYXRhKCkgew0KICAgICAgLy8g6K6+572u5paH5pys5qGG6YCJ5oup55qE5o6S5Lqn5pWw6YePLOiuvue9ruiHquWumuS5ieWUr+S4gOeggQ0KICAgICAgY29uc3Qgb2JqS2V5ID0ge30NCiAgICAgIGlmICghdGhpcy50YkRhdGE/Lmxlbmd0aCkgew0KICAgICAgICB0aGlzLnRiRGF0YSA9IFtdDQogICAgICAgIC8vIHRoaXMuYmFja2VuZFRiID0gW10NCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICBjb25zb2xlLmxvZyg5OTgsIEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy50YkRhdGEpKSkNCiAgICAgIC8vIHRoaXMuYmFja2VuZFRiID0gZGVlcENsb25lKHRoaXMudGJEYXRhKQ0KICAgICAgdGhpcy50YkRhdGEgPSB0aGlzLnRiRGF0YS5maWx0ZXIoaXRlbSA9PiB7DQogICAgICAgIHRoaXMuJHNldChpdGVtLCAnY291bnQnLCBpdGVtLkNhbl9TY2hkdWxpbmdfQ291bnQpDQogICAgICAgIHRoaXMuJHNldChpdGVtLCAnbWF4Q291bnQnLCBpdGVtLkNhbl9TY2hkdWxpbmdfQ291bnQpDQogICAgICAgIGl0ZW0udXVpZCA9IGdldFVuaXF1ZSh0aGlzLmlzQ29tLCBpdGVtKQ0KICAgICAgICBvYmpLZXlbaXRlbS5UeXBlXSA9IHRydWUNCiAgICAgICAgLy8gbGV0IGNzQ291bnQgPSAwDQogICAgICAgIC8vIGl0ZW0uY29tcG9uZW50TWFwID0gKGl0ZW0uQ29tcG9uZW50X0NvZGVzIHx8IFtdKS5yZWR1Y2UoKGFjYywgY29kZSkgPT4gew0KICAgICAgICAvLyAgIGNvbnN0IFtrZXksIHZhbHVlXSA9IGNvZGUuc3BsaXQoJyYmJicpDQogICAgICAgIC8vICAgYWNjW2tleV0gPSBwYXJzZUludCh2YWx1ZSkNCiAgICAgICAgLy8gICBjc0NvdW50ICs9IHBhcnNlSW50KHZhbHVlKQ0KICAgICAgICAvLyAgIHJldHVybiBhY2MNCiAgICAgICAgLy8gfSwge30pDQogICAgICAgIC8vIHRoaXMuJHNldChpdGVtLCAnY3NDb3VudCcsIGNzQ291bnQpDQogICAgICAgIC8vIE9iamVjdC5rZXlzKGl0ZW0uY29tcG9uZW50TWFwKS5mb3JFYWNoKGtleSA9PiB7DQogICAgICAgIC8vICAgdGhpcy4kc2V0KGl0ZW0sIGtleSwgaXRlbS5jb21wb25lbnRNYXBba2V5XSkNCiAgICAgICAgLy8gfSkNCg0KICAgICAgICByZXR1cm4gIXRoaXMuYWRkVGJLZXlzLmluY2x1ZGVzKGl0ZW0udXVpZCkNCiAgICAgIH0pDQogICAgICAvLyAgIC5tYXAoKGl0ZW0pID0+IHsNCiAgICAgIC8vICAgdGhpcy4kc2V0KGl0ZW0sICdjb3VudCcsIGl0ZW0uQ2FuX1NjaGR1bGluZ19Db3VudCkNCiAgICAgIC8vICAgdGhpcy4kc2V0KGl0ZW0sICdtYXhDb3VudCcsIGl0ZW0uQ2FuX1NjaGR1bGluZ19Db3VudCkNCiAgICAgIC8vICAgLy8gaXRlbS51dWlkID0gdXVpZHY0KCkNCiAgICAgIC8vICAgaXRlbS51dWlkID0gaXRlbS5JbnN0YWxsVW5pdF9JZCArIGl0ZW0uUGFydF9BZ2dyZWdhdGVfSWQNCiAgICAgIC8vICAgb2JqS2V5W2l0ZW0uVHlwZV0gPSB0cnVlDQogICAgICAvLw0KICAgICAgLy8gICBjb25zdCBfc2VsZWN0TGlzdCA9IHRoaXMuc2VsZWN0VGJEYXRhLmZpbHRlcih2ID0+IHYucHV1aWQpDQogICAgICAvLyAgIGNvbnNvbGUubG9nKCdfc2VsZWN0TGlzdCcsIF9zZWxlY3RMaXN0KQ0KICAgICAgLy8gICAvLyBfc2VsZWN0TGlzdC5mb3JFYWNoKChlbGVtZW50LCBpZHgpID0+IHsNCiAgICAgIC8vICAgLy8gICBpZihlbGVtZW50LnB1dWlkID09PSBpdGVtLnV1aWQpew0KICAgICAgLy8gICAvLw0KICAgICAgLy8gICAvLyAgIH0NCiAgICAgIC8vICAgLy8gfSkNCiAgICAgIC8vICAgcmV0dXJuIGl0ZW0NCiAgICAgIC8vIH0pDQoNCiAgICAgIC8vIHRoaXMuYmFja2VuZFRiID0gZGVlcENsb25lKHRoaXMudGJEYXRhKQ0KICAgIH0sDQogICAgYXN5bmMgZ2V0Q29tVGJEYXRhKCkgew0KICAgICAgLy8gY29uc3QgeyBpbnN0YWxsLCBhcmVhSWQgfSA9IHRoaXMuJHJvdXRlLnF1ZXJ5DQogICAgICBjb25zdCB7IENvbXBfQ29kZXMsIC4uLm9iaiB9ID0gdGhpcy5mb3JtDQogICAgICBsZXQgY29kZXMgPSBbXQ0KICAgICAgaWYgKE9iamVjdC5wcm90b3R5cGUudG9TdHJpbmcuY2FsbChDb21wX0NvZGVzKSA9PT0gJ1tvYmplY3QgU3RyaW5nXScpIHsNCiAgICAgICAgY29kZXMgPSBDb21wX0NvZGVzICYmIENvbXBfQ29kZXMuc3BsaXQoJyAnKS5maWx0ZXIodiA9PiAhIXYpDQogICAgICB9DQogICAgICBhd2FpdCBHZXRDYW5TY2hkdWxpbmdDb21wcyh7DQogICAgICAgIElkczogdGhpcy5jdXJyZW50SWRzLA0KICAgICAgICAuLi5vYmosDQogICAgICAgIFNjaGR1bGluZ19QbGFuX0lkOiB0aGlzLnNjaGVkdWxlSWQsDQogICAgICAgIENvbXBfQ29kZXM6IGNvZGVzLA0KICAgICAgICBJbnN0YWxsVW5pdF9JZDogdGhpcy5pbnN0YWxsSWQsDQogICAgICAgIEFyZWFfSWQ6IHRoaXMuYXJlYUlkDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLnBhZ2VJbmZvLnRvdGFsID0gcmVzLkRhdGEubGVuZ3RoDQogICAgICAgICAgdGhpcy50YkRhdGEgPSByZXMuRGF0YS5tYXAoKHYsIGlkeCkgPT4gew0KICAgICAgICAgICAgLy8g5bey5o6S5Lqn6LWL5YC8DQogICAgICAgICAgICB2Lm9yaWdpbmFsUGF0aCA9IHYuU2NoZWR1bGVkX1RlY2hub2xvZ3lfUGF0aCA/IHYuU2NoZWR1bGVkX1RlY2hub2xvZ3lfUGF0aCA6ICcnDQogICAgICAgICAgICB2LldvcmtzaG9wX0lkID0gdi5TY2hlZHVsZWRfV29ya3Nob3BfSWQNCiAgICAgICAgICAgIHYuV29ya3Nob3BfTmFtZSA9IHYuU2NoZWR1bGVkX1dvcmtzaG9wX05hbWUNCiAgICAgICAgICAgIHYuVGVjaG5vbG9neV9QYXRoID0gdi5TY2hlZHVsZWRfVGVjaG5vbG9neV9QYXRoIHx8IHYuVGVjaG5vbG9neV9QYXRoDQogICAgICAgICAgICAvLyBpZiAodi5vcmlnaW5hbFBhdGgpIHsNCiAgICAgICAgICAgIC8vIHYuaXNEaXNhYmxlZCA9IHRydWUNCiAgICAgICAgICAgIC8vIH0NCiAgICAgICAgICAgIHYuY2hlY2tlZCA9IGZhbHNlDQogICAgICAgICAgICB2LmluaXRSb3dJbmRleCA9IGlkeA0KICAgICAgICAgICAgdi5BcmVhX05hbWUgPSB0aGlzLm5vZGVMYWJlbHMuam9pbignLycpDQoNCiAgICAgICAgICAgIC8vIHYudGVjaG5vbG9neVBhdGhEaXNhYmxlZCA9ICEhdi5UZWNobm9sb2d5X1BhdGgNCiAgICAgICAgICAgIHJldHVybiB2DQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLnNldFBhZ2UoKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKg0KICAgICAqIOWIhumhtQ0KICAgICAqLw0KICAgIGhhbmRsZVBhZ2VDaGFuZ2UoeyBjdXJyZW50UGFnZSwgcGFnZVNpemUgfSkgew0KICAgICAgaWYgKHRoaXMudGJMb2FkaW5nKSByZXR1cm4NCiAgICAgIHRoaXMucGFnZUluZm8ucGFnZSA9IGN1cnJlbnRQYWdlDQogICAgICB0aGlzLnBhZ2VJbmZvLnBhZ2VTaXplID0gcGFnZVNpemUNCiAgICAgIHRoaXMuc2V0UGFnZSgpDQogICAgICB0aGlzLmZpbHRlckRhdGEoY3VycmVudFBhZ2UpDQogICAgfSwNCg0KICAgIHNldFBhZ2UodGIgPSB0aGlzLnRiRGF0YSkgew0KICAgICAgdGhpcy5mVGFibGUgPSB0Yi5zbGljZSgodGhpcy5wYWdlSW5mby5wYWdlIC0gMSkgKiB0aGlzLnBhZ2VJbmZvLnBhZ2VTaXplLCB0aGlzLnBhZ2VJbmZvLnBhZ2UgKiB0aGlzLnBhZ2VJbmZvLnBhZ2VTaXplKQ0KICAgIH0sDQoNCiAgICBhc3luYyBnZXRQYXJ0VGJEYXRhKCkgew0KICAgICAgLy8gY29uc3QgeyBpbnN0YWxsLCBhcmVhSWQgfSA9IHRoaXMuJHJvdXRlLnF1ZXJ5DQogICAgICBhd2FpdCBHZXRDYW5TY2hkdWxpbmdQYXJ0cyh7DQogICAgICAgIElkczogdGhpcy5jdXJyZW50SWRzLA0KICAgICAgICAuLi50aGlzLmZvcm0sDQogICAgICAgIFNjaGR1bGluZ19QbGFuX0lkOiB0aGlzLnNjaGVkdWxlSWQsDQogICAgICAgIEluc3RhbGxVbml0X0lkOiB0aGlzLmluc3RhbGxJZCwNCiAgICAgICAgQXJlYV9JZDogdGhpcy5hcmVhSWQNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMucGFnZUluZm8udG90YWwgPSByZXMuRGF0YS5sZW5ndGgNCiAgICAgICAgICB0aGlzLnRiRGF0YSA9IHJlcy5EYXRhLm1hcCgodiwgaWR4KSA9PiB7DQogICAgICAgICAgICB2Lm9yaWdpbmFsUGF0aCA9IHYuU2NoZWR1bGVkX1RlY2hub2xvZ3lfUGF0aCA/IHYuU2NoZWR1bGVkX1RlY2hub2xvZ3lfUGF0aCA6ICcnDQogICAgICAgICAgICB2LldvcmtzaG9wX0lkID0gdi5TY2hlZHVsZWRfV29ya3Nob3BfSWQNCiAgICAgICAgICAgIHYuV29ya3Nob3BfTmFtZSA9IHYuU2NoZWR1bGVkX1dvcmtzaG9wX05hbWUNCiAgICAgICAgICAgIGlmICh2LkNvbXBfSW1wb3J0X0RldGFpbF9JZCkgew0KICAgICAgICAgICAgICB2LlBhcnRfVXNlZF9Qcm9jZXNzID0gdGhpcy5nZXRQYXJ0VXNlZFByb2Nlc3ModikNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHYuVGVjaG5vbG9neV9QYXRoID0gdi5TY2hlZHVsZWRfVGVjaG5vbG9neV9QYXRoIHx8IHYuVGVjaG5vbG9neV9QYXRoDQogICAgICAgICAgICAvLyB2LmlzRGlzYWJsZWQgPSAhIXYub3JpZ2luYWxQYXRoDQogICAgICAgICAgICB2LmNoZWNrZWQgPSBmYWxzZQ0KICAgICAgICAgICAgdi5pbml0Um93SW5kZXggPSBpZHgNCiAgICAgICAgICAgIC8vIHYucGFydFVzZWRQcm9jZXNzRGlzYWJsZWQgPSB0aGlzLmlzUGFydFByZXBhcmUgPyAhIXYuUGFydF9Vc2VkX1Byb2Nlc3MgOiBmYWxzZQ0KICAgICAgICAgICAgLy8gdi50ZWNobm9sb2d5UGF0aERpc2FibGVkID0gISF2LlRlY2hub2xvZ3lfUGF0aA0KICAgICAgICAgICAgaWYgKCF0aGlzLmlzUGFydFByZXBhcmUpIHsNCiAgICAgICAgICAgICAgdi5UZW1wX1BhcnRfVXNlZF9Qcm9jZXNzID0gdi5QYXJ0X1VzZWRfUHJvY2Vzcw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgcmV0dXJuIHYNCiAgICAgICAgICB9KQ0KICAgICAgICAgIHRoaXMuc2V0UGFydENvbHVtbigpDQogICAgICAgICAgdGhpcy5zZXRQYWdlKCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQoNCiAgICAgIGNvbnN0IHN1Ym1pdE9iaiA9IHRoaXMudGJEYXRhLm1hcChpdGVtID0+IHsNCiAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICBJZDogaXRlbS5QYXJ0X0FnZ3JlZ2F0ZV9JZCwNCiAgICAgICAgICBUeXBlOiAxDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICBhd2FpdCBHZXRTdG9wTGlzdChzdWJtaXRPYmopLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICBjb25zdCBzdG9wTWFwID0ge30NCiAgICAgICAgICByZXMuRGF0YS5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgICAgc3RvcE1hcFtpdGVtLklkXSA9ICEhaXRlbS5Jc19TdG9wDQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLnRiRGF0YS5mb3JFYWNoKHJvdyA9PiB7DQogICAgICAgICAgICBpZiAoc3RvcE1hcC5oYXNPd25Qcm9wZXJ0eShyb3cuUGFydF9BZ2dyZWdhdGVfSWQpKSB7DQogICAgICAgICAgICAgIHRoaXMuJHNldChyb3csICdzdG9wRmxhZycsIHN0b3BNYXBbcm93LlBhcnRfQWdncmVnYXRlX0lkXSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgY2hlY2tDaGVja2JveE1ldGhvZCh7IHJvdyB9KSB7DQogICAgICByZXR1cm4gIXJvdy5zdG9wRmxhZw0KICAgIH0sDQogICAgZ2V0UGFydFVzZWRQcm9jZXNzKGl0ZW0pIHsNCiAgICAgIGlmIChpdGVtLlNjaGVkdWxlZF9Vc2VkX1Byb2Nlc3MpIHsNCiAgICAgICAgcmV0dXJuIGl0ZW0uU2NoZWR1bGVkX1VzZWRfUHJvY2Vzcw0KICAgICAgfQ0KICAgICAgaWYgKGl0ZW0uQ29tcG9uZW50X1RlY2hub2xvZ3lfUGF0aCkgew0KICAgICAgICBjb25zdCBsaXN0ID0gaXRlbS5Db21wb25lbnRfVGVjaG5vbG9neV9QYXRoLnNwbGl0KCcvJykNCiAgICAgICAgaWYgKGxpc3QuaW5jbHVkZXMoaXRlbS5QYXJ0X1VzZWRfUHJvY2VzcykpIHsNCiAgICAgICAgICByZXR1cm4gaXRlbS5QYXJ0X1VzZWRfUHJvY2Vzcw0KICAgICAgICB9IGVsc2UgaWYgKGxpc3QuaW5jbHVkZXMoaXRlbS5QYXJ0X1R5cGVfVXNlZF9Qcm9jZXNzKSkgew0KICAgICAgICAgIHJldHVybiBpdGVtLlBhcnRfVHlwZV9Vc2VkX1Byb2Nlc3MNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgaWYgKGl0ZW0uUGFydF9Vc2VkX1Byb2Nlc3MpIHsNCiAgICAgICAgICByZXR1cm4gaXRlbS5QYXJ0X1VzZWRfUHJvY2Vzcw0KICAgICAgICB9IGVsc2UgaWYgKGl0ZW0uUGFydF9UeXBlX1VzZWRfUHJvY2Vzcykgew0KICAgICAgICAgIHJldHVybiBpdGVtLlBhcnRfVHlwZV9Vc2VkX1Byb2Nlc3MNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICByZXR1cm4gJycNCiAgICB9LA0KICAgIHNldFBhcnRDb2x1bW4oKSB7DQogICAgICAvLyDnuq/pm7bku7YNCiAgICAgIHRoaXMuaXNPd25lck51bGwgPSB0aGlzLnRiRGF0YS5ldmVyeSh2ID0+ICF2LkNvbXBfSW1wb3J0X0RldGFpbF9JZCkNCiAgICAgIGNvbnNvbGUubG9nKCd0aGlzLmlzT3duZXJOdWxsJywgdGhpcy5pc093bmVyTnVsbCkNCiAgICAgIGlmICh0aGlzLmlzT3duZXJOdWxsKSB7DQogICAgICAgIGNvbnN0IGlkeCA9IHRoaXMuY29sdW1ucy5maW5kSW5kZXgodiA9PiB2LkNvZGUgPT09ICdDb21wb25lbnRfQ29kZScpDQogICAgICAgIGlkeCAhPT0gLTEgJiYgdGhpcy5jb2x1bW5zLnNwbGljZShpZHgsIDEpDQogICAgICB9DQogICAgfSwNCiAgICBtZXJnZURhdGEobGlzdCkgew0KICAgICAgLyogICAgICBjb25zb2xlLmxvZygnbGlzdCcsIGxpc3QpDQogICAgICBjb25zb2xlLmxvZygndGhpcy5iYWNrZW5kVGInLCB0aGlzLmJhY2tlbmRUYikNCiAgICAgIGxpc3QNCiAgICAgICAgLmZvckVhY2goKGVsZW1lbnQsIGluZGV4KSA9PiB7DQogICAgICAgICAgY29uc3QgaWR4ID0gdGhpcy5iYWNrZW5kVGIuZmluZEluZGV4KA0KICAgICAgICAgICAgKGl0ZW0pID0+IGVsZW1lbnQucHV1aWQgJiYgaXRlbS51dWlkID09PSBlbGVtZW50LnB1dWlkDQogICAgICAgICAgKQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCdpZHgnLCBpZHgsIHRoaXMuYmFja2VuZFRiW2lkeF0pDQogICAgICAgICAgY29uc29sZS5sb2coJ2luZGV4JywgaW5kZXgpDQogICAgICAgICAgaWYgKGlkeCAhPT0gLTEpIHsNCiAgICAgICAgICAgIHRoaXMudGJEYXRhLnNwbGljZShpZHgsIDAsIGRlZXBDbG9uZSh0aGlzLmJhY2tlbmRUYltpZHhdKSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQoNCiAgICAgIHRoaXMudGJEYXRhLnNvcnQoKGEsIGIpID0+IGEuaW5pdFJvd0luZGV4IC0gYi5pbml0Um93SW5kZXgpDQogICAgICBjb25zb2xlLmxvZygndGhpcy50YkRhdGEnLCBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMudGJEYXRhKSkpDQoNCiAgICAgIHRoaXMuZmlsdGVyRGF0YSgpKi8NCiAgICB9LA0KICAgIGhhbmRsZUNsb3NlKCkgew0KICAgICAgdGhpcy4kZW1pdCgnY2xvc2UnKQ0KICAgIH0sDQogICAgLy8gYWN0aXZlQ2VsbE1ldGhvZCh7IHJvdywgY29sdW1uLCBjb2x1bW5JbmRleCB9KSB7DQogICAgLy8gICByZXR1cm4gY29sdW1uLmZpZWxkID09PSAnU2NoZHVsaW5nX0NvdW50Jw0KICAgIC8vIH0sDQogICAgYXN5bmMgZ2V0VGFibGVDb25maWcoY29kZSkgew0KICAgICAgYXdhaXQgR2V0R3JpZEJ5Q29kZSh7DQogICAgICAgIGNvZGUNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBjb25zdCB7IElzU3VjY2VlZCwgRGF0YSwgTWVzc2FnZSB9ID0gcmVzDQogICAgICAgIGlmIChJc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLnRiQ29uZmlnID0gT2JqZWN0LmFzc2lnbih7fSwgdGhpcy50YkNvbmZpZywgRGF0YS5HcmlkKQ0KICAgICAgICAgIHRoaXMucGFnZUluZm8ucGFnZVNpemUgPSBOdW1iZXIodGhpcy50YkNvbmZpZy5Sb3dfTnVtYmVyKQ0KICAgICAgICAgIGNvbnN0IGxpc3QgPSBEYXRhLkNvbHVtbkxpc3QgfHwgW10NCiAgICAgICAgICB0aGlzLmNvbHVtbnMgPSBsaXN0LmZpbHRlcih2ID0+IHYuSXNfRGlzcGxheSkNCiAgICAgICAgICAgIC5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgICAgIGlmIChpdGVtLklzX0Zyb3plbikgew0KICAgICAgICAgICAgICAgIGl0ZW0uZml4ZWQgPSAnbGVmdCcNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAvLyB0aGlzLmNvbHVtbnMucHVzaCh7DQogICAgICAgICAgLy8gICBEaXNwbGF5X05hbWU6ICfmjpLkuqfmlbDph48nLA0KICAgICAgICAgIC8vICAgQ29kZTogJ1NjaGR1bGluZ19Db3VudCcNCiAgICAgICAgICAvLyB9KQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgZ2V0T2JqZWN0VHlwZUxpc3QoKSB7DQogICAgICBHZXRDb21wVHlwZVRyZWUoeyBwcm9mZXNzaW9uYWw6ICdTdGVlbCcgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgLy8gdGhpcy5PYmplY3RUeXBlTGlzdC5kYXRhID0gcmVzLkRhdGENCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoXykgPT4gew0KICAgICAgICAgICAgdGhpcy4kcmVmcy50cmVlU2VsZWN0T2JqZWN0VHlwZS50cmVlRGF0YVVwZGF0ZUZ1bihyZXMuRGF0YSkNCiAgICAgICAgICB9KQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZVJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtLlR5cGVfTmFtZSA9ICcnDQogICAgICB0aGlzLmZvcm0uQ29tcF9Db2RlID0gJycNCiAgICAgIHRoaXMuZm9ybS5Db21wX0NvZGVCbHVyID0gJycNCiAgICAgIHRoaXMuZm9ybS5UeXBlID0gJycNCiAgICAgIHRoaXMuZm9ybS5TcGVjID0gJycNCiAgICAgIHRoaXMuZm9ybS5JbnN0YWxsVW5pdF9JZCA9IFtdDQogICAgICB0aGlzLmZvcm0uUGFydF9Db2RlQmx1ciA9ICcnDQogICAgICB0aGlzLmZvcm0uUGFydF9Db2RlID0gJycNCiAgICAgIHRoaXMuc2VhcmNoQ29udGVudCA9ICcnDQogICAgICB0aGlzLnNlYXJjaFBhcnRDb250ZW50ID0gJycNCiAgICAgIHRoaXMuaGFuZGxlU2VhcmNoKCkNCiAgICB9LA0KICAgIGdldFR5cGUoKSB7DQogICAgICBHZXRQYXJ0VHlwZUxpc3QoeyBQYXJ0X0dyYWRlOiAwIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLnR5cGVPcHRpb24gPSByZXMuRGF0YQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFkZFRvTGlzdCgpIHsNCiAgICAgIGlmICghdGhpcy50b3RhbFNlbGVjdGlvbi5sZW5ndGgpIHJldHVybg0KICAgICAgdGhpcy5oYW5kbGVTYXZlKDEpDQogICAgfSwNCiAgICBnZXRJbnN0YWxsVW5pdElkTmFtZUxpc3QoaWQpIHsNCiAgICAgIGlmICghdGhpcy5hcmVhSWQpIHsNCiAgICAgICAgdGhpcy5pbnN0YWxsVW5pdElkTGlzdCA9IFtdDQogICAgICB9IGVsc2Ugew0KICAgICAgICBHZXRJbnN0YWxsVW5pdElkTmFtZUxpc3QoeyBBcmVhX0lkOiB0aGlzLmFyZWFJZCB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgdGhpcy5pbnN0YWxsVW5pdElkTGlzdCA9IHJlcy5EYXRhIHx8IFtdDQogICAgICAgICAgLy8gaWYgKHRoaXMuaW5zdGFsbFVuaXRJZExpc3QubGVuZ3RoKSB7DQogICAgICAgICAgLy8gICB0aGlzLmZvcm0uSW5zdGFsbFVuaXRfSWQgPSBbdGhpcy5pbnN0YWxsVW5pdElkTGlzdFswXS5JZF0NCiAgICAgICAgICAvLyB9DQogICAgICAgIH0pDQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["addDraft.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAu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file": "addDraft.vue", "sourceRoot": "src/views/PRO/plan-production/schedule-production-new-part/components", "sourcesContent": ["<template>\r\n  <div class=\"contentBox\">\r\n    <div class=\"main-info\">\r\n      <div class=\"left\">\r\n        <ExpandableSection v-model=\"showExpand\" v-loading=\"tbLoading\" class=\"fff\" :width=\"300\">\r\n          <div class=\"inner-wrapper\">\r\n            <div class=\"tree-search\">\r\n              <el-select\r\n                v-model=\"statusType\"\r\n                clearable\r\n                class=\"search-select\"\r\n                placeholder=\"请选择\"\r\n              >\r\n                <el-option label=\"可排产\" value=\"可排产\" />\r\n                <el-option label=\"排产完成\" value=\"排产完成\" />\r\n                <el-option label=\"未导入\" value=\"未导入\" />\r\n              </el-select>\r\n              <el-input\r\n                v-model.trim=\"projectName\"\r\n                placeholder=\"搜索...\"\r\n                size=\"small\"\r\n                clearable\r\n                suffix-icon=\"el-icon-search\"\r\n              />\r\n            </div>\r\n            <el-divider class=\"cs-divider\" />\r\n            <div class=\"tree-x cs-scroll\">\r\n              <tree-detail\r\n                ref=\"tree\"\r\n                icon=\"icon-folder\"\r\n                is-custom-filter\r\n                :custom-filter-fun=\"customFilterFun\"\r\n                :loading=\"treeLoading\"\r\n                :tree-data=\"treeData\"\r\n                show-status\r\n                show-detail\r\n                :filter-text=\"filterText\"\r\n                :expanded-key=\"expandedKey\"\r\n                @handleNodeClick=\"handleNodeClick\"\r\n              >\r\n                <template #csLabel=\"{showStatus,data}\">\r\n                  <span v-if=\"!data.ParentNodes\" class=\"cs-blue\">({{ data.Code }})</span>{{ data.Label }}\r\n                  <template v-if=\"showStatus\">\r\n                    <span :class=\"['cs-tag',data.Data[statusCode]=='可排产' ? 'greenBg' : data.Data[statusCode]=='排产完成' ?'orangeBg':data.Data[statusCode]=='未导入'?'redBg':'']\">\r\n                      <i\r\n                        v-if=\"data.Data[statusCode]\"\r\n                        :class=\"[data.Data[statusCode]=='可排产' ? 'fourGreen' : data.Data[statusCode]=='排产完成' ?'fourOrange':data.Data[statusCode]=='未导入'?'fourRed':'']\"\r\n                      >\r\n                        {{ data.Data[statusCode] }}\r\n                      </i>\r\n\r\n                    </span>\r\n                  </template>\r\n                </template>\r\n\r\n              </tree-detail>\r\n            </div>\r\n          </div>\r\n        </ExpandableSection>\r\n      </div>\r\n      <div class=\"right\">\r\n\r\n        <el-form ref=\"form\" :model=\"form\" label-width=\"90px\">\r\n          <el-row>\r\n            <template >\r\n              <el-col :span=\"12\">\r\n                <!--                <el-form-item label-width=\"70px\" label=\"零件名称\" prop=\"Part_Code\">\r\n                  <div class=\"cs-input-x\">\r\n                    <el-input\r\n                      v-model=\"form.Part_Code\"\r\n                      placeholder=\"请输入(空格区分/多个搜索)\"\r\n                      clearable\r\n                      class=\"w100\"\r\n                    />\r\n                    <el-input\r\n                      v-model=\"form.Part_CodeBlur\"\r\n                      clearable\r\n                      class=\"w100\"\r\n                      style=\"margin-left: 10px;\"\r\n                      placeholder=\"模糊查找(请输入关键字)\"\r\n                      type=\"text\"\r\n                    />\r\n                  </div>\r\n                </el-form-item>\r\n             -->\r\n                <el-form-item prop=\"searchContent\" :label=\"`${comName}名称`\">\r\n                  <el-input\r\n                    v-model=\"searchContent\"\r\n                    clearable\r\n                    class=\"input-with-select w100\"\r\n                    placeholder=\"请输入(空格区分/多个搜索)\"\r\n                    size=\"small\"\r\n                  >\r\n                    <el-select\r\n                      slot=\"prepend\"\r\n                      v-model=\"curSearch\"\r\n                      placeholder=\"请选择\"\r\n                      style=\"width: 100px\"\r\n                    >\r\n                      <el-option label=\"精准查询\" :value=\"1\" />\r\n                      <el-option label=\"模糊查询\" :value=\"0\" />\r\n                    </el-select>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item prop=\"searchPartContent\" :label=\"`${partName}名称`\">\r\n                  <el-input\r\n                    v-model=\"searchPartContent\"\r\n                    clearable\r\n                    class=\"input-with-select w100\"\r\n                    placeholder=\"请输入(空格区分/多个搜索)\"\r\n                    size=\"small\"\r\n                  >\r\n                    <el-select\r\n                      slot=\"prepend\"\r\n                      v-model=\"curPartSearch\"\r\n                      placeholder=\"请选择\"\r\n                      style=\"width: 100px\"\r\n                    >\r\n                      <el-option label=\"精准查询\" :value=\"1\" />\r\n                      <el-option label=\"模糊查询\" :value=\"0\" />\r\n                    </el-select>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"5\">\r\n                <el-form-item label=\"规格\" prop=\"Spec\">\r\n                  <el-input\r\n                    v-model=\"form.Spec\"\r\n                    placeholder=\"请输入\"\r\n                    clearable\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"5\">\r\n                <el-form-item :label=\"`${partName}种类`\" prop=\"Type_Name\">\r\n                  <el-select\r\n                    v-model=\"form.Type_Name\"\r\n                    placeholder=\"请选择\"\r\n                    clearable\r\n                  >\r\n                    <el-option\r\n                      v-for=\"item in typeOption\"\r\n                      :key=\"item.Code\"\r\n                      :label=\"item.Name\"\r\n                      :value=\"item.Name\"\r\n                    />\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n            </template>\r\n            <el-col :span=\"5\">\r\n              <el-form-item label=\"批次\" label-width=\"50px\" prop=\"Create_UserName\">\r\n                <el-select\r\n                  v-model=\"form.InstallUnit_Id\"\r\n                  filterable\r\n                  clearable\r\n                  multiple\r\n                  style=\"width: 100%\"\r\n                  placeholder=\"请选择\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in installUnitIdList\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Name\"\r\n                    :value=\"item.Id\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"9\">\r\n              <el-form-item label-width=\"0\">\r\n                <el-button style=\"margin-left: 10px\" @click=\"handleReset\">重置</el-button>\r\n                <el-button style=\"margin-left: 10px\" type=\"primary\" @click=\"handleSearch()\">查询</el-button>\r\n                <el-button :loading=\"addLoading\" style=\"margin-left: 10px\" type=\"primary\" @click=\"addToList()\">加入列表</el-button>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n          </el-row>\r\n        </el-form>\r\n\r\n        <div class=\"tb-wrapper\">\r\n          <vxe-table\r\n            ref=\"xTable1\"\r\n            :empty-render=\"{name: 'NotData'}\"\r\n            show-header-overflow\r\n            empty-text=\"暂无数据\"\r\n            height=\"auto\"\r\n            show-overflow\r\n            :checkbox-config=\"{checkField: 'checked', checkMethod: checkCheckboxMethod}\"\r\n            :loading=\"tbLoading\"\r\n            :row-config=\"{isCurrent: true, isHover: true }\"\r\n            class=\"cs-vxe-table\"\r\n            align=\"left\"\r\n            stripe\r\n            :data=\"fTable\"\r\n            resizable\r\n            :edit-config=\"{trigger: 'click', mode: 'cell'}\"\r\n            :tooltip-config=\"{ enterable: true }\"\r\n            @checkbox-all=\"tbSelectChange\"\r\n            @checkbox-change=\"tbSelectChange\"\r\n          >\r\n            <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n            <template v-for=\"item in columns\">\r\n              <vxe-column\r\n                v-if=\"item.Code === 'Is_Component'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <el-tag :type=\"row.Is_Component ? 'danger' : 'success'\">{{\r\n                    row.Is_Component ? \"否\" : \"是\"\r\n                  }}</el-tag>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"['Part_Code','Comp_Code'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                  <el-tag v-if=\"row.Is_Change\" style=\"margin-right: 8px;\" type=\"danger\">变</el-tag>\r\n                  {{ row[item.Code] | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"['Can_Schduling_Count'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <span v-if=\"showSc\">{{ row.csCount||'' }}</span>\r\n                  <span v-else>{{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"['Can_Schduling_Weight'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <span v-if=\"showSc\">{{ row.csCountWeight||'' }}</span>\r\n                  <span v-else>{{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                show-overflow=\"tooltip\"\r\n                sortable\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                :min-width=\"item.Width\"\r\n              />\r\n            </template>\r\n          </vxe-table>\r\n        </div>\r\n        <div class=\"data-info\">\r\n          <el-tag\r\n            size=\"medium\"\r\n            class=\"info-x\"\r\n          >已选 {{ totalSelection.length }} 条数据\r\n          </el-tag>\r\n          <vxe-pager\r\n            border\r\n            background\r\n            :loading=\"tbLoading\"\r\n            :current-page.sync=\"pageInfo.page\"\r\n            :page-size.sync=\"pageInfo.pageSize\"\r\n            :page-sizes=\"pageInfo.pageSizes\"\r\n            :total=\"pageInfo.total\"\r\n            :layouts=\"['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']\"\r\n            size=\"small\"\r\n            @page-change=\"handlePageChange\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"button\">\r\n      <el-button @click=\"handleClose\">取消</el-button>\r\n      <el-button\r\n        type=\"primary\"\r\n        :disabled=\"!totalSelection.length\"\r\n        :loading=\"saveLoading\"\r\n        @click=\"handleSave(2)\"\r\n      >保存</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { GetCanSchdulingComps } from '@/api/PRO/production-task'\r\nimport { GetCanSchdulingParts, GetPartList } from '@/api/PRO/production-part'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { debounce, deepClone } from '@/utils'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport { GetCompTypeTree } from '@/api/PRO/professionalType'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\nimport TreeDetail from '@/components/TreeDetail/index.vue'\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport { GetInstallUnitIdNameList, GetProjectAreaTreeList } from '@/api/PRO/project'\r\nimport { getUnique } from '../constant'\r\nimport { mapGetters } from 'vuex'\r\nimport { findAllParentNode } from '@/utils/tree'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nconst SPLIT_SYMBOL = '$_$'\r\n\r\nexport default {\r\n  components: { ExpandableSection, TreeDetail },\r\n  props: {\r\n    scheduleId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    pageType: {\r\n      type: String,\r\n      default: 'com'\r\n    },\r\n    showDialog: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n\r\n    installId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    currentIds: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n\r\n    isPartPrepare: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    comName: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    partName: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      pageInfo: {\r\n        page: 1,\r\n        pageSize: 500,\r\n        pageSizes: tablePageSize,\r\n        total: 0\r\n      },\r\n      form: {\r\n        Comp_Code: '',\r\n        Comp_CodeBlur: '',\r\n        Part_CodeBlur: '',\r\n        Part_Code: '',\r\n        Type_Name: '',\r\n        InstallUnit_Id: [],\r\n        Spec: '',\r\n        Type: ''\r\n      },\r\n      curSearch: 1,\r\n      curPartSearch: 1,\r\n      showExpand: true,\r\n      searchContent: '',\r\n      searchPartContent: '',\r\n      statusType: '',\r\n      projectName: '',\r\n      expandedKey: '',\r\n      statusCode: 'Part_Schdule_Status',\r\n      isOwnerNull: true,\r\n      tbLoading: false,\r\n      treeLoading: false,\r\n      addLoading: false,\r\n      saveLoading: false,\r\n      showSc: false,\r\n      installUnitIdList: [],\r\n      columns: [],\r\n      fTable: [],\r\n      tbConfig: {},\r\n      TotalCount: 0,\r\n      Page: 0,\r\n      totalSelection: [],\r\n      treeData: [],\r\n      search: () => ({}),\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        clearable: true\r\n      },\r\n      ObjectTypeList: {\r\n        // 构件类型\r\n        'check-strictly': true,\r\n        'default-expand-all': true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      },\r\n      areaId: '',\r\n      typeOption: []\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    },\r\n    filterText() {\r\n      return this.projectName + SPLIT_SYMBOL + this.statusType\r\n    },\r\n    ...mapGetters('schedule', ['addTbKeys'])\r\n  },\r\n  watch: {\r\n    showDialog(newValue) {\r\n      newValue && (this.saveLoading = false)\r\n    }\r\n  },\r\n  mounted() {\r\n\r\n  },\r\n  methods: {\r\n    initData() {\r\n      console.log('initData')\r\n      this.tbData = []\r\n      this.getConfig()\r\n      this.fetchTreeData()\r\n      if (this.isCom) {\r\n        this.getObjectTypeList()\r\n      } else {\r\n        this.getType()\r\n      }\r\n      this.search = debounce(this.fetchData, 800, true)\r\n      this.setPageData()\r\n    },\r\n    handleNodeClick(data) {\r\n      if (this.areaId === data.Id) {\r\n        return\r\n      }\r\n      if (!data.ParentNodes || data.Children?.length > 0) {\r\n        return\r\n      }\r\n      if (data?.Data[this.statusCode] === '未导入') {\r\n        this.$message({\r\n          message: '清单未导入，请联系深化人员导入清单',\r\n          type: 'warning'\r\n        })\r\n        this.expandedKey = data.Id\r\n        return\r\n      }\r\n\r\n      const setData = ({ Data }) => {\r\n        this.areaId = Data.Id\r\n        this.projectId = Data.Project_Id\r\n        this.expandedKey = this.areaId\r\n\r\n        const _arr = findAllParentNode(this.treeData, data.Id, true)\r\n        this.nodeLabels = _arr.filter(v => !!v.ParentNodes).map(p => p.Label)\r\n\r\n        // this.formInline.Finish_Date = ''\r\n        // this.formInline.InstallUnit_Id = ''\r\n        // this.formInline.Remark = ''\r\n        this.fetchData()\r\n        // this.getAreaInfo()\r\n        this.getInstallUnitIdNameList()\r\n      }\r\n\r\n      setData(data)\r\n    },\r\n    fetchTreeData() {\r\n      this.treeLoading = true\r\n      GetProjectAreaTreeList({ MenuId: this.$route.meta.Id, projectName: this.projectName, type: this.isCom ? 1 : 2 }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          if (res.Data.length === 0) {\r\n            this.treeData = []\r\n            this.treeLoading = false\r\n            return\r\n          }\r\n          const resData = res.Data.map(item => {\r\n            item.Is_Directory = true\r\n            return item\r\n          })\r\n          this.treeData = resData\r\n          console.log('setKey')\r\n          this.setKey()\r\n          this.treeLoading = false\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.treeData = []\r\n          this.treeLoading = false\r\n        }\r\n      }).catch(() => {\r\n        this.treeLoading = false\r\n        this.treeData = []\r\n      })\r\n    },\r\n    setKey() {\r\n      const deepFilter = (tree) => {\r\n        console.log('tree', tree)\r\n        for (let i = 0; i < tree.length; i++) {\r\n          const item = tree[i]\r\n          const { Data, Children } = item\r\n          console.log(Data)\r\n          if (Data.ParentId && !Children?.length) {\r\n            this.handleNodeClick(item)\r\n            return\r\n          } else {\r\n            if (Children && Children?.length > 0) {\r\n              return deepFilter(Children)\r\n            }\r\n          }\r\n        }\r\n      }\r\n      return deepFilter(this.treeData)\r\n    },\r\n    customFilterFun(value, data, node) {\r\n      const arr = value.split(SPLIT_SYMBOL)\r\n      const labelVal = arr[0]\r\n      const statusVal = arr[1]\r\n      if (!value) return true\r\n      let parentNode = node.parent\r\n      let labels = [node.label]\r\n      let status = [data.Data[this.statusCode]]\r\n      let level = 1\r\n      while (level < node.level) {\r\n        labels = [...labels, parentNode.label]\r\n        status = [...status, data.Data[this.statusCode]]\r\n        parentNode = parentNode.parent\r\n        level++\r\n      }\r\n      labels = labels.filter(v => !!v)\r\n      status = status.filter(v => !!v)\r\n      let resultLabel = true\r\n      let resultStatus = true\r\n      if (this.statusType) {\r\n        resultStatus = status.some(s => s.indexOf(statusVal) !== -1)\r\n      }\r\n      if (this.projectName) {\r\n        resultLabel = labels.some(s => s.indexOf(labelVal) !== -1)\r\n      }\r\n      return resultLabel && resultStatus\r\n    },\r\n    async getConfig() {\r\n      let code = ''\r\n      code = this.isCom\r\n        ? 'PROComDraftEditTbConfig'\r\n        : 'PROPartDraftEditTbConfig_new'\r\n      await this.getTableConfig(code)\r\n      // this.fetchData()\r\n    },\r\n    filterData(page) {\r\n      console.log(22)\r\n      const splitAndClean = (input) => input.trim().replace(/\\s+/g, ' ').split(' ')\r\n\r\n      if (this.curSearch === 1) {\r\n        this.form.Comp_Code = this.searchContent\r\n        this.form.Comp_CodeBlur = ''\r\n      }\r\n      if (this.curSearch === 0) {\r\n        this.form.Comp_CodeBlur = this.searchContent\r\n        this.form.Comp_Code = ''\r\n      }\r\n      if (this.curPartSearch === 1) {\r\n        this.form.Part_CodeBlur = ''\r\n        this.form.Part_Code = this.searchPartContent\r\n      }\r\n      if (this.curPartSearch === 0) {\r\n        this.form.Part_Code = ''\r\n        this.form.Part_CodeBlur = this.searchPartContent\r\n      }\r\n\r\n      const f = []\r\n      for (const formKey in this.form) {\r\n        if (this.form[formKey] || this.form[formKey] === false) {\r\n          f.push(formKey)\r\n        }\r\n      }\r\n      if (!f.length) {\r\n        this.setPage()\r\n        !page && (this.pageInfo.page = 1)\r\n        this.pageInfo.total = this.tbData.length\r\n        return\r\n      }\r\n\r\n      const checkMatch = (origin, comp) => {\r\n        const _comp = comp.map(code => {\r\n          const [key, value] = code.split('&&&')\r\n          return key\r\n        })\r\n        const _origin = origin.map(code => {\r\n          const [key, value] = code.split('&&&')\r\n          return key\r\n        })\r\n        return _origin.some(item => {\r\n          return _comp.some(value => item.includes(value))\r\n        })\r\n      }\r\n      const checkExactMatch = (origin, comp) => {\r\n        const _comp = comp.map(code => {\r\n          const [key, value] = code.split('&&&')\r\n          return key\r\n        })\r\n        const _origin = origin.map(code => {\r\n          const [key, value] = code.split('&&&')\r\n          return key\r\n        })\r\n\r\n        return _origin.some(item => _comp.includes(item))\r\n      }\r\n\r\n      const temTbData = this.tbData.filter(v => {\r\n        v.checked = false\r\n        const compCode = v.Component_Codes || []\r\n\r\n        if (this.form.Comp_Code.trim()) {\r\n          const compCodeArray = splitAndClean(this.form.Comp_Code)\r\n          if (compCodeArray.length) {\r\n            const flag = checkExactMatch(compCode, compCodeArray)\r\n            console.log(887, compCode, compCodeArray, flag)\r\n            if (!flag) return false\r\n          }\r\n        }\r\n\r\n        if (this.form.Comp_CodeBlur.trim()) {\r\n          const compCodeArray = splitAndClean(this.form.Comp_CodeBlur)\r\n          if (compCodeArray.length) {\r\n            const flag = checkMatch(compCode, compCodeArray)\r\n            if (!flag) return false\r\n          }\r\n        }\r\n\r\n        if (this.form.Type && v.Type !== this.form.Type) {\r\n          return false\r\n        }\r\n\r\n        if (this.form.Part_CodeBlur.trim()) {\r\n          const partCodeBlurArray = splitAndClean(this.form.Part_CodeBlur)\r\n          if (!partCodeBlurArray.some(code => v['Part_Code'].includes(code))) {\r\n            return false\r\n          }\r\n        }\r\n\r\n        if (this.form.Part_Code.trim()) {\r\n          const partCodeArray = splitAndClean(this.form.Part_Code)\r\n          if (!partCodeArray.includes(v['Part_Code'])) {\r\n            return false\r\n          }\r\n        }\r\n\r\n        if (this.form.InstallUnit_Id.length && !this.form.InstallUnit_Id.includes(v.InstallUnit_Id)) {\r\n          return false\r\n        }\r\n\r\n        if (this.form.Type_Name !== '' && v.Type_Name !== this.form.Type_Name) {\r\n          return false\r\n        }\r\n\r\n        if (this.form.Spec.trim() !== '') {\r\n          const specArray = splitAndClean(this.form.Spec)\r\n          if (!specArray.some(spec => v.Spec.includes(spec))) {\r\n            return false\r\n          }\r\n        }\r\n        if (this.searchContent.trim().length) {\r\n          let csCount = 0\r\n\r\n          v.componentMap = (v.Component_Codes || []).reduce((acc, code) => {\r\n            const [key, value] = code.split('&&&')\r\n            acc[key] = parseInt(value)\r\n            if (this.curSearch === 1) {\r\n              const compCodeArray = splitAndClean(this.form.Comp_Code)\r\n              if (compCodeArray.length) {\r\n                const flag = checkExactMatch([key], compCodeArray)\r\n                if (flag) {\r\n                  csCount += parseInt(value)\r\n                }\r\n              }\r\n            } else {\r\n              const compCodeArray = splitAndClean(this.form.Comp_CodeBlur)\r\n              if (compCodeArray.length) {\r\n                const flag = checkMatch([key], compCodeArray)\r\n                console.log('pflag', key, compCodeArray, flag, value)\r\n                if (flag) {\r\n                  csCount += parseInt(value)\r\n                }\r\n              }\r\n            }\r\n            return acc\r\n          }, {})\r\n          this.$set(v, 'csCount', Math.min(csCount, v.Can_Schduling_Count))\r\n          this.$set(v, 'csCountWeight', Math.min(v.Can_Schduling_Weight, v.csCount * v.Weight))\r\n\r\n          v.searchcount = v.count\r\n          v.searchcountMax = v.maxCount\r\n          // const cs = v.Component_Codes || []\r\n          // let min = 0\r\n          // cs.forEach((element, idx) => {\r\n          //   const [key, value] = element.split('&&&')\r\n          //   min = v.componentMap[key]\r\n          // })\r\n\r\n          v.count = v.csCount\r\n        } else {\r\n          v.count = v.Can_Schduling_Count\r\n        }\r\n\r\n        // v.Can_Schduling_Count = v.csCount\r\n        // v.Can_Schduling_Weight = v.csCountWeight\r\n\r\n        return true\r\n      })\r\n\r\n      !page && (this.pageInfo.page = 1)\r\n      this.pageInfo.total = temTbData.length\r\n      this.setPage(temTbData)\r\n      if (this.searchContent.trim().length) {\r\n        this.showSc = true\r\n      }\r\n    },\r\n    handleSearch() {\r\n      this.totalSelection = []\r\n      this.clearSelect()\r\n      if (this.tbData?.length) {\r\n        this.tbData.forEach(item => item.checked = false)\r\n        this.filterData()\r\n      }\r\n      this.showSc = !!this.searchContent.trim().length\r\n    },\r\n    tbSelectChange(array) {\r\n      this.totalSelection = this.tbData.filter(v => v.checked)\r\n    },\r\n    clearSelect() {\r\n      this.$refs.xTable1.clearCheckboxRow()\r\n      this.totalSelection = []\r\n    },\r\n    async fetchData() {\r\n      this.handleReset()\r\n      this.tbLoading = true\r\n      if (this.isCom) {\r\n        await this.getComTbData()\r\n      } else {\r\n        await this.getPartTbData()\r\n      }\r\n      this.initTbData()\r\n      this.filterData()\r\n      this.tbLoading = false\r\n    },\r\n    setPageData() {\r\n      if (this.tbData?.length) {\r\n        this.pageInfo.page = 1\r\n        this.tbData = this.tbData.filter(v => v.Can_Schduling_Count > 0)\r\n        this.filterData()\r\n      }\r\n    },\r\n    handleSave(type = 2) {\r\n      if (type === 1) {\r\n        this.addLoading = true\r\n      } else {\r\n        this.saveLoading = true\r\n      }\r\n      setTimeout(() => {\r\n        this.totalSelection.forEach((item) => {\r\n          const intCount = parseInt(item.count)\r\n          if (this.searchContent.trim().length) {\r\n            item.Schduled_Count = item.Can_Schduling_Count\r\n\r\n            item.maxCount = item.Can_Schduling_Count\r\n            item.chooseCount = intCount\r\n            item.count = item.Can_Schduling_Count\r\n\r\n            item.Can_Schduling_Count = 0\r\n            item.Can_Schduling_Weight = item.Can_Schduling_Count * item.Weight\r\n          } else {\r\n            item.Schduled_Count += intCount\r\n            item.Can_Schduling_Count -= intCount\r\n            item.Can_Schduling_Weight = item.Can_Schduling_Count * item.Weight\r\n            item.maxCount = intCount\r\n            item.chooseCount = intCount\r\n            item.count = item.Can_Schduling_Count\r\n          }\r\n\r\n          item.checked = false\r\n        })\r\n        const cp = deepClone(this.totalSelection)\r\n\r\n        // this.$emit('sendSelectList', cp)\r\n        this.addLoading = false\r\n        this.clearSelect()\r\n        // this.setPage()\r\n        this.setPageData()\r\n        if (type === 2) {\r\n          this.$emit('sendSelectList', cp)\r\n          this.$emit('close')\r\n          this.fTable = []\r\n          this.tbData = []\r\n        } else {\r\n          this.$emit('addToTbList', cp)\r\n        }\r\n      }, 0)\r\n    },\r\n    initTbData() {\r\n      // 设置文本框选择的排产数量,设置自定义唯一码\r\n      const objKey = {}\r\n      if (!this.tbData?.length) {\r\n        this.tbData = []\r\n        // this.backendTb = []\r\n        return\r\n      }\r\n      console.log(998, JSON.parse(JSON.stringify(this.tbData)))\r\n      // this.backendTb = deepClone(this.tbData)\r\n      this.tbData = this.tbData.filter(item => {\r\n        this.$set(item, 'count', item.Can_Schduling_Count)\r\n        this.$set(item, 'maxCount', item.Can_Schduling_Count)\r\n        item.uuid = getUnique(this.isCom, item)\r\n        objKey[item.Type] = true\r\n        // let csCount = 0\r\n        // item.componentMap = (item.Component_Codes || []).reduce((acc, code) => {\r\n        //   const [key, value] = code.split('&&&')\r\n        //   acc[key] = parseInt(value)\r\n        //   csCount += parseInt(value)\r\n        //   return acc\r\n        // }, {})\r\n        // this.$set(item, 'csCount', csCount)\r\n        // Object.keys(item.componentMap).forEach(key => {\r\n        //   this.$set(item, key, item.componentMap[key])\r\n        // })\r\n\r\n        return !this.addTbKeys.includes(item.uuid)\r\n      })\r\n      //   .map((item) => {\r\n      //   this.$set(item, 'count', item.Can_Schduling_Count)\r\n      //   this.$set(item, 'maxCount', item.Can_Schduling_Count)\r\n      //   // item.uuid = uuidv4()\r\n      //   item.uuid = item.InstallUnit_Id + item.Part_Aggregate_Id\r\n      //   objKey[item.Type] = true\r\n      //\r\n      //   const _selectList = this.selectTbData.filter(v => v.puuid)\r\n      //   console.log('_selectList', _selectList)\r\n      //   // _selectList.forEach((element, idx) => {\r\n      //   //   if(element.puuid === item.uuid){\r\n      //   //\r\n      //   //   }\r\n      //   // })\r\n      //   return item\r\n      // })\r\n\r\n      // this.backendTb = deepClone(this.tbData)\r\n    },\r\n    async getComTbData() {\r\n      // const { install, areaId } = this.$route.query\r\n      const { Comp_Codes, ...obj } = this.form\r\n      let codes = []\r\n      if (Object.prototype.toString.call(Comp_Codes) === '[object String]') {\r\n        codes = Comp_Codes && Comp_Codes.split(' ').filter(v => !!v)\r\n      }\r\n      await GetCanSchdulingComps({\r\n        Ids: this.currentIds,\r\n        ...obj,\r\n        Schduling_Plan_Id: this.scheduleId,\r\n        Comp_Codes: codes,\r\n        InstallUnit_Id: this.installId,\r\n        Area_Id: this.areaId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.pageInfo.total = res.Data.length\r\n          this.tbData = res.Data.map((v, idx) => {\r\n            // 已排产赋值\r\n            v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n            v.Workshop_Id = v.Scheduled_Workshop_Id\r\n            v.Workshop_Name = v.Scheduled_Workshop_Name\r\n            v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n            // if (v.originalPath) {\r\n            // v.isDisabled = true\r\n            // }\r\n            v.checked = false\r\n            v.initRowIndex = idx\r\n            v.Area_Name = this.nodeLabels.join('/')\r\n\r\n            // v.technologyPathDisabled = !!v.Technology_Path\r\n            return v\r\n          })\r\n          this.setPage()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 分页\r\n     */\r\n    handlePageChange({ currentPage, pageSize }) {\r\n      if (this.tbLoading) return\r\n      this.pageInfo.page = currentPage\r\n      this.pageInfo.pageSize = pageSize\r\n      this.setPage()\r\n      this.filterData(currentPage)\r\n    },\r\n\r\n    setPage(tb = this.tbData) {\r\n      this.fTable = tb.slice((this.pageInfo.page - 1) * this.pageInfo.pageSize, this.pageInfo.page * this.pageInfo.pageSize)\r\n    },\r\n\r\n    async getPartTbData() {\r\n      // const { install, areaId } = this.$route.query\r\n      await GetCanSchdulingParts({\r\n        Ids: this.currentIds,\r\n        ...this.form,\r\n        Schduling_Plan_Id: this.scheduleId,\r\n        InstallUnit_Id: this.installId,\r\n        Area_Id: this.areaId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.pageInfo.total = res.Data.length\r\n          this.tbData = res.Data.map((v, idx) => {\r\n            v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n            v.Workshop_Id = v.Scheduled_Workshop_Id\r\n            v.Workshop_Name = v.Scheduled_Workshop_Name\r\n            if (v.Comp_Import_Detail_Id) {\r\n              v.Part_Used_Process = this.getPartUsedProcess(v)\r\n            }\r\n            v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n            // v.isDisabled = !!v.originalPath\r\n            v.checked = false\r\n            v.initRowIndex = idx\r\n            // v.partUsedProcessDisabled = this.isPartPrepare ? !!v.Part_Used_Process : false\r\n            // v.technologyPathDisabled = !!v.Technology_Path\r\n            if (!this.isPartPrepare) {\r\n              v.Temp_Part_Used_Process = v.Part_Used_Process\r\n            }\r\n            return v\r\n          })\r\n          this.setPartColumn()\r\n          this.setPage()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n\r\n      const submitObj = this.tbData.map(item => {\r\n        return {\r\n          Id: item.Part_Aggregate_Id,\r\n          Type: 1\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = !!item.Is_Stop\r\n          })\r\n          this.tbData.forEach(row => {\r\n            if (stopMap.hasOwnProperty(row.Part_Aggregate_Id)) {\r\n              this.$set(row, 'stopFlag', stopMap[row.Part_Aggregate_Id])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    checkCheckboxMethod({ row }) {\r\n      return !row.stopFlag\r\n    },\r\n    getPartUsedProcess(item) {\r\n      if (item.Scheduled_Used_Process) {\r\n        return item.Scheduled_Used_Process\r\n      }\r\n      if (item.Component_Technology_Path) {\r\n        const list = item.Component_Technology_Path.split('/')\r\n        if (list.includes(item.Part_Used_Process)) {\r\n          return item.Part_Used_Process\r\n        } else if (list.includes(item.Part_Type_Used_Process)) {\r\n          return item.Part_Type_Used_Process\r\n        }\r\n      } else {\r\n        if (item.Part_Used_Process) {\r\n          return item.Part_Used_Process\r\n        } else if (item.Part_Type_Used_Process) {\r\n          return item.Part_Type_Used_Process\r\n        }\r\n      }\r\n\r\n      return ''\r\n    },\r\n    setPartColumn() {\r\n      // 纯零件\r\n      this.isOwnerNull = this.tbData.every(v => !v.Comp_Import_Detail_Id)\r\n      console.log('this.isOwnerNull', this.isOwnerNull)\r\n      if (this.isOwnerNull) {\r\n        const idx = this.columns.findIndex(v => v.Code === 'Component_Code')\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n      }\r\n    },\r\n    mergeData(list) {\r\n      /*      console.log('list', list)\r\n      console.log('this.backendTb', this.backendTb)\r\n      list\r\n        .forEach((element, index) => {\r\n          const idx = this.backendTb.findIndex(\r\n            (item) => element.puuid && item.uuid === element.puuid\r\n          )\r\n          console.log('idx', idx, this.backendTb[idx])\r\n          console.log('index', index)\r\n          if (idx !== -1) {\r\n            this.tbData.splice(idx, 0, deepClone(this.backendTb[idx]))\r\n          }\r\n        })\r\n\r\n      this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n      console.log('this.tbData', JSON.parse(JSON.stringify(this.tbData)))\r\n\r\n      this.filterData()*/\r\n    },\r\n    handleClose() {\r\n      this.$emit('close')\r\n    },\r\n    // activeCellMethod({ row, column, columnIndex }) {\r\n    //   return column.field === 'Schduling_Count'\r\n    // },\r\n    async getTableConfig(code) {\r\n      await GetGridByCode({\r\n        code\r\n      }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n          this.pageInfo.pageSize = Number(this.tbConfig.Row_Number)\r\n          const list = Data.ColumnList || []\r\n          this.columns = list.filter(v => v.Is_Display)\r\n            .map(item => {\r\n              if (item.Is_Frozen) {\r\n                item.fixed = 'left'\r\n              }\r\n              return item\r\n            })\r\n          // this.columns.push({\r\n          //   Display_Name: '排产数量',\r\n          //   Code: 'Schduling_Count'\r\n          // })\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getObjectTypeList() {\r\n      GetCompTypeTree({ professional: 'Steel' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          // this.ObjectTypeList.data = res.Data\r\n          this.$nextTick((_) => {\r\n            this.$refs.treeSelectObjectType.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.form.Type_Name = ''\r\n      this.form.Comp_Code = ''\r\n      this.form.Comp_CodeBlur = ''\r\n      this.form.Type = ''\r\n      this.form.Spec = ''\r\n      this.form.InstallUnit_Id = []\r\n      this.form.Part_CodeBlur = ''\r\n      this.form.Part_Code = ''\r\n      this.searchContent = ''\r\n      this.searchPartContent = ''\r\n      this.handleSearch()\r\n    },\r\n    getType() {\r\n      GetPartTypeList({ Part_Grade: 0 }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.typeOption = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    addToList() {\r\n      if (!this.totalSelection.length) return\r\n      this.handleSave(1)\r\n    },\r\n    getInstallUnitIdNameList(id) {\r\n      if (!this.areaId) {\r\n        this.installUnitIdList = []\r\n      } else {\r\n        GetInstallUnitIdNameList({ Area_Id: this.areaId }).then(res => {\r\n          this.installUnitIdList = res.Data || []\r\n          // if (this.installUnitIdList.length) {\r\n          //   this.form.InstallUnit_Id = [this.installUnitIdList[0].Id]\r\n          // }\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n@import \"~@/styles/mixin.scss\";\r\n.cs-divider{\r\n  margin:16px 0 0 0;\r\n}\r\n.contentBox {\r\n  height: 75vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .main-info{\r\n    display: flex;\r\n    overflow: hidden;\r\n    flex: 1;\r\n    .left{\r\n      height: 100%;\r\n      margin-right: 16px;\r\n      border: 1px solid #eee;\r\n      .cs-tag{\r\n        margin-left: 8px;\r\n        font-size: 12px;\r\n        padding:2px 4px;\r\n        border-radius: 1px;\r\n      }\r\n\r\n      .inner-wrapper {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n        padding: 16px;\r\n        border-radius: 4px;\r\n        overflow: hidden;\r\n\r\n        .tree-search {\r\n          display: flex;\r\n\r\n          .search-select {\r\n            margin-right: 8px;\r\n          }\r\n        }\r\n\r\n        .tree-x {\r\n          overflow: hidden;\r\n          margin-top: 16px;\r\n          flex: 1;\r\n\r\n          .el-tree {\r\n            height: 100%;\r\n          }\r\n        }\r\n\r\n        .cs-scroll {\r\n          overflow-y: auto;\r\n          @include scrollBar;\r\n        }\r\n\r\n      }\r\n    }\r\n    .right{\r\n      overflow: hidden;\r\n      flex: 1;\r\n      display: flex;\r\n      flex-direction: column;\r\n      border: 1px solid #eee;\r\n      padding:16px;\r\n    }\r\n\r\n  }\r\n\r\n  .button {\r\n    margin-top: 16px;\r\n    display: flex;\r\n    justify-content: end;\r\n  }\r\n\r\n  .tb-wrapper {\r\n    flex: 1 1 auto;\r\n  }\r\n\r\n  .data-info{\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-top: 16px;\r\n  }\r\n}\r\n.fourGreen {\r\n  color: #00C361;\r\n  font-style: normal;\r\n}\r\n\r\n.fourOrange {\r\n  color: #FF9400;\r\n  font-style: normal;\r\n}\r\n\r\n.fourRed {\r\n  color: #FF0000;\r\n  font-style: normal;\r\n}\r\n\r\n.cs-blue {\r\n  color: #5AC8FA;\r\n}\r\n\r\n.orangeBg{\r\n  background: rgba(255,148,0,0.1);\r\n}\r\n\r\n.redBg{\r\n  background: rgba(252,107,127,0.1);\r\n}\r\n.greenBg{\r\n  background: rgba(0, 195, 97, 0.10);\r\n}\r\n.cs-input-x{\r\n  display: flex;\r\n}\r\n</style>\r\n"]}]}
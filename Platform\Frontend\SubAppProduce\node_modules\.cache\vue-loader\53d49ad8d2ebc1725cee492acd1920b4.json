{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\addDraft.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\addDraft.vue", "mtime": 1757926768436}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRHcmlkQnlDb2RlIH0gZnJvbSAnQC9hcGkvc3lzJw0KaW1wb3J0IHsgR2V0Q2FuU2NoZHVsaW5nQ29tcHMgfSBmcm9tICdAL2FwaS9QUk8vcHJvZHVjdGlvbi10YXNrJw0KaW1wb3J0IHsgR2V0Q2FuU2NoZHVsaW5nUGFydHMsIEdldFBhcnRMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL3Byb2R1Y3Rpb24tcGFydCcNCmltcG9ydCB7IHY0IGFzIHV1aWR2NCB9IGZyb20gJ3V1aWQnDQppbXBvcnQgeyBkZWJvdW5jZSwgZGVlcENsb25lIH0gZnJvbSAnQC91dGlscycNCmltcG9ydCB7IHRhYmxlUGFnZVNpemUgfSBmcm9tICdAL3ZpZXdzL1BSTy9zZXR0aW5nJw0KaW1wb3J0IHsgR2V0Q29tcFR5cGVUcmVlIH0gZnJvbSAnQC9hcGkvUFJPL3Byb2Zlc3Npb25hbFR5cGUnDQppbXBvcnQgeyBHZXRQYXJ0VHlwZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vcGFydFR5cGUnDQppbXBvcnQgVHJlZURldGFpbCBmcm9tICdAL2NvbXBvbmVudHMvVHJlZURldGFpbC9pbmRleC52dWUnDQppbXBvcnQgRXhwYW5kYWJsZVNlY3Rpb24gZnJvbSAnQC9jb21wb25lbnRzL0V4cGFuZGFibGVTZWN0aW9uL2luZGV4LnZ1ZScNCmltcG9ydCB7IEdldEluc3RhbGxVbml0SWROYW1lTGlzdCwgR2V0UHJvamVjdEFyZWFUcmVlTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9wcm9qZWN0Jw0KaW1wb3J0IHsgZ2V0VW5pcXVlIH0gZnJvbSAnLi4vY29uc3RhbnQnDQppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCcNCmltcG9ydCB7IGZpbmRBbGxQYXJlbnROb2RlIH0gZnJvbSAnQC91dGlscy90cmVlJw0KaW1wb3J0IHsgR2V0U3RvcExpc3QgfSBmcm9tICdAL2FwaS9QUk8vcHJvZHVjdGlvbi10YXNrJw0KY29uc3QgU1BMSVRfU1lNQk9MID0gJyRfJCcNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7IEV4cGFuZGFibGVTZWN0aW9uLCBUcmVlRGV0YWlsIH0sDQogIHByb3BzOiB7DQogICAgc2NoZWR1bGVJZDogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJycNCiAgICB9LA0KICAgIHBhZ2VUeXBlOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnY29tJw0KICAgIH0sDQogICAgc2hvd0RpYWxvZzogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfSwNCg0KICAgIGluc3RhbGxJZDogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJycNCiAgICB9LA0KICAgIGN1cnJlbnRJZHM6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICcnDQogICAgfSwNCg0KICAgIGlzUGFydFByZXBhcmU6IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiBmYWxzZQ0KICAgIH0sDQogICAgY29tTmFtZTogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJycNCiAgICB9LA0KICAgIHBhcnROYW1lOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnJw0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgcGFnZUluZm86IHsNCiAgICAgICAgcGFnZTogMSwNCiAgICAgICAgcGFnZVNpemU6IDUwMCwNCiAgICAgICAgcGFnZVNpemVzOiB0YWJsZVBhZ2VTaXplLA0KICAgICAgICB0b3RhbDogMA0KICAgICAgfSwNCiAgICAgIGZvcm06IHsNCiAgICAgICAgQ29tcF9Db2RlOiAnJywNCiAgICAgICAgQ29tcF9Db2RlQmx1cjogJycsDQogICAgICAgIFBhcnRfQ29kZUJsdXI6ICcnLA0KICAgICAgICBQYXJ0X0NvZGU6ICcnLA0KICAgICAgICBVbml0X1BhcnRfQ29kZUJsdXI6ICcnLA0KICAgICAgICBVbml0X1BhcnRfQ29kZTogJycsDQogICAgICAgIFR5cGVfTmFtZTogJycsDQogICAgICAgIEluc3RhbGxVbml0X0lkOiBbXSwNCiAgICAgICAgU3BlYzogJycsDQogICAgICAgIFR5cGU6ICcnDQogICAgICB9LA0KICAgICAgY3VyU2VhcmNoOiAxLA0KICAgICAgY3VyUGFydFNlYXJjaDogMSwNCiAgICAgIGN1clVuaXRQYXJ0U2VhcmNoOiAxLA0KICAgICAgc2hvd0V4cGFuZDogdHJ1ZSwNCiAgICAgIHNlYXJjaENvbnRlbnQ6ICcnLA0KICAgICAgc2VhcmNoVW5pdFBhcnRDb250ZW50OiAnJywNCiAgICAgIHNlYXJjaFBhcnRDb250ZW50OiAnJywNCiAgICAgIHN0YXR1c1R5cGU6ICcnLA0KICAgICAgcHJvamVjdE5hbWU6ICcnLA0KICAgICAgZXhwYW5kZWRLZXk6ICcnLA0KICAgICAgc3RhdHVzQ29kZTogJ1BhcnRfU2NoZHVsZV9TdGF0dXMnLA0KICAgICAgaXNPd25lck51bGw6IHRydWUsDQogICAgICB0YkxvYWRpbmc6IGZhbHNlLA0KICAgICAgdHJlZUxvYWRpbmc6IGZhbHNlLA0KICAgICAgYWRkTG9hZGluZzogZmFsc2UsDQogICAgICBzYXZlTG9hZGluZzogZmFsc2UsDQogICAgICBzaG93U2M6IGZhbHNlLA0KICAgICAgaW5zdGFsbFVuaXRJZExpc3Q6IFtdLA0KICAgICAgY29sdW1uczogW10sDQogICAgICBmVGFibGU6IFtdLA0KICAgICAgdGJDb25maWc6IHt9LA0KICAgICAgVG90YWxDb3VudDogMCwNCiAgICAgIFBhZ2U6IDAsDQogICAgICB0b3RhbFNlbGVjdGlvbjogW10sDQogICAgICB0cmVlRGF0YTogW10sDQogICAgICBzZWFyY2g6ICgpID0+ICh7fSksDQogICAgICB0cmVlU2VsZWN0UGFyYW1zOiB7DQogICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36YCJ5oupJywNCiAgICAgICAgY2xlYXJhYmxlOiB0cnVlDQogICAgICB9LA0KICAgICAgT2JqZWN0VHlwZUxpc3Q6IHsNCiAgICAgICAgLy8g5p6E5Lu257G75Z6LDQogICAgICAgICdjaGVjay1zdHJpY3RseSc6IHRydWUsDQogICAgICAgICdkZWZhdWx0LWV4cGFuZC1hbGwnOiB0cnVlLA0KICAgICAgICBjbGlja1BhcmVudDogdHJ1ZSwNCiAgICAgICAgZGF0YTogW10sDQogICAgICAgIHByb3BzOiB7DQogICAgICAgICAgY2hpbGRyZW46ICdDaGlsZHJlbicsDQogICAgICAgICAgbGFiZWw6ICdMYWJlbCcsDQogICAgICAgICAgdmFsdWU6ICdEYXRhJw0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgYXJlYUlkOiAnJywNCiAgICAgIHR5cGVPcHRpb246IFtdDQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIGlzQ29tKCkgew0KICAgICAgcmV0dXJuIHRoaXMucGFnZVR5cGUgPT09ICdjb20nDQogICAgfSwNCiAgICBmaWx0ZXJUZXh0KCkgew0KICAgICAgcmV0dXJuIHRoaXMucHJvamVjdE5hbWUgKyBTUExJVF9TWU1CT0wgKyB0aGlzLnN0YXR1c1R5cGUNCiAgICB9LA0KICAgIC4uLm1hcEdldHRlcnMoJ3NjaGVkdWxlJywgWydhZGRUYktleXMnXSkNCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICBzaG93RGlhbG9nKG5ld1ZhbHVlKSB7DQogICAgICBuZXdWYWx1ZSAmJiAodGhpcy5zYXZlTG9hZGluZyA9IGZhbHNlKQ0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCg0KICB9LA0KICBtZXRob2RzOiB7DQogICAgaW5pdERhdGEoKSB7DQogICAgICBjb25zb2xlLmxvZygnaW5pdERhdGEnKQ0KICAgICAgdGhpcy50YkRhdGEgPSBbXQ0KICAgICAgdGhpcy5nZXRDb25maWcoKQ0KICAgICAgdGhpcy5mZXRjaFRyZWVEYXRhKCkNCiAgICAgIGlmICh0aGlzLmlzQ29tKSB7DQogICAgICAgIHRoaXMuZ2V0T2JqZWN0VHlwZUxpc3QoKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5nZXRUeXBlKCkNCiAgICAgIH0NCiAgICAgIHRoaXMuc2VhcmNoID0gZGVib3VuY2UodGhpcy5mZXRjaERhdGEsIDgwMCwgdHJ1ZSkNCiAgICAgIHRoaXMuc2V0UGFnZURhdGEoKQ0KICAgIH0sDQogICAgaGFuZGxlTm9kZUNsaWNrKGRhdGEpIHsNCiAgICAgIGlmICh0aGlzLmFyZWFJZCA9PT0gZGF0YS5JZCkgew0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIGlmICghZGF0YS5QYXJlbnROb2RlcyB8fCBkYXRhLkNoaWxkcmVuPy5sZW5ndGggPiAwKSB7DQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgaWYgKGRhdGE/LkRhdGFbdGhpcy5zdGF0dXNDb2RlXSA9PT0gJ+acquWvvOWFpScpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogJ+a4heWNleacquWvvOWFpe+8jOivt+iBlOezu+a3seWMluS6uuWRmOWvvOWFpea4heWNlScsDQogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgIH0pDQogICAgICAgIHRoaXMuZXhwYW5kZWRLZXkgPSBkYXRhLklkDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICBjb25zdCBzZXREYXRhID0gKHsgRGF0YSB9KSA9PiB7DQogICAgICAgIHRoaXMuYXJlYUlkID0gRGF0YS5JZA0KICAgICAgICB0aGlzLnByb2plY3RJZCA9IERhdGEuUHJvamVjdF9JZA0KICAgICAgICB0aGlzLmV4cGFuZGVkS2V5ID0gdGhpcy5hcmVhSWQNCg0KICAgICAgICBjb25zdCBfYXJyID0gZmluZEFsbFBhcmVudE5vZGUodGhpcy50cmVlRGF0YSwgZGF0YS5JZCwgdHJ1ZSkNCiAgICAgICAgdGhpcy5ub2RlTGFiZWxzID0gX2Fyci5maWx0ZXIodiA9PiAhIXYuUGFyZW50Tm9kZXMpLm1hcChwID0+IHAuTGFiZWwpDQoNCiAgICAgICAgLy8gdGhpcy5mb3JtSW5saW5lLkZpbmlzaF9EYXRlID0gJycNCiAgICAgICAgLy8gdGhpcy5mb3JtSW5saW5lLkluc3RhbGxVbml0X0lkID0gJycNCiAgICAgICAgLy8gdGhpcy5mb3JtSW5saW5lLlJlbWFyayA9ICcnDQogICAgICAgIHRoaXMuZmV0Y2hEYXRhKCkNCiAgICAgICAgLy8gdGhpcy5nZXRBcmVhSW5mbygpDQogICAgICAgIHRoaXMuZ2V0SW5zdGFsbFVuaXRJZE5hbWVMaXN0KCkNCiAgICAgIH0NCg0KICAgICAgc2V0RGF0YShkYXRhKQ0KICAgIH0sDQogICAgZmV0Y2hUcmVlRGF0YSgpIHsNCiAgICAgIHRoaXMudHJlZUxvYWRpbmcgPSB0cnVlDQogICAgICBHZXRQcm9qZWN0QXJlYVRyZWVMaXN0KHsgTWVudUlkOiB0aGlzLiRyb3V0ZS5tZXRhLklkLCBwcm9qZWN0TmFtZTogdGhpcy5wcm9qZWN0TmFtZSwgdHlwZTogdGhpcy5pc0NvbSA/IDEgOiAyIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIGlmIChyZXMuRGF0YS5sZW5ndGggPT09IDApIHsNCiAgICAgICAgICAgIHRoaXMudHJlZURhdGEgPSBbXQ0KICAgICAgICAgICAgdGhpcy50cmVlTG9hZGluZyA9IGZhbHNlDQogICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICB9DQogICAgICAgICAgY29uc3QgcmVzRGF0YSA9IHJlcy5EYXRhLm1hcChpdGVtID0+IHsNCiAgICAgICAgICAgIGl0ZW0uSXNfRGlyZWN0b3J5ID0gdHJ1ZQ0KICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICB9KQ0KICAgICAgICAgIHRoaXMudHJlZURhdGEgPSByZXNEYXRhDQogICAgICAgICAgY29uc29sZS5sb2coJ3NldEtleScpDQogICAgICAgICAgdGhpcy5zZXRLZXkoKQ0KICAgICAgICAgIHRoaXMudHJlZUxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLnRyZWVEYXRhID0gW10NCiAgICAgICAgICB0aGlzLnRyZWVMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLnRyZWVMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgdGhpcy50cmVlRGF0YSA9IFtdDQogICAgICB9KQ0KICAgIH0sDQogICAgc2V0S2V5KCkgew0KICAgICAgY29uc3QgZGVlcEZpbHRlciA9ICh0cmVlKSA9PiB7DQogICAgICAgIGNvbnNvbGUubG9nKCd0cmVlJywgdHJlZSkNCiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0cmVlLmxlbmd0aDsgaSsrKSB7DQogICAgICAgICAgY29uc3QgaXRlbSA9IHRyZWVbaV0NCiAgICAgICAgICBjb25zdCB7IERhdGEsIENoaWxkcmVuIH0gPSBpdGVtDQogICAgICAgICAgY29uc29sZS5sb2coRGF0YSkNCiAgICAgICAgICBpZiAoRGF0YS5QYXJlbnRJZCAmJiAhQ2hpbGRyZW4/Lmxlbmd0aCkgew0KICAgICAgICAgICAgdGhpcy5oYW5kbGVOb2RlQ2xpY2soaXRlbSkNCiAgICAgICAgICAgIHJldHVybg0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBpZiAoQ2hpbGRyZW4gJiYgQ2hpbGRyZW4/Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgcmV0dXJuIGRlZXBGaWx0ZXIoQ2hpbGRyZW4pDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgICByZXR1cm4gZGVlcEZpbHRlcih0aGlzLnRyZWVEYXRhKQ0KICAgIH0sDQogICAgY3VzdG9tRmlsdGVyRnVuKHZhbHVlLCBkYXRhLCBub2RlKSB7DQogICAgICBjb25zdCBhcnIgPSB2YWx1ZS5zcGxpdChTUExJVF9TWU1CT0wpDQogICAgICBjb25zdCBsYWJlbFZhbCA9IGFyclswXQ0KICAgICAgY29uc3Qgc3RhdHVzVmFsID0gYXJyWzFdDQogICAgICBpZiAoIXZhbHVlKSByZXR1cm4gdHJ1ZQ0KICAgICAgbGV0IHBhcmVudE5vZGUgPSBub2RlLnBhcmVudA0KICAgICAgbGV0IGxhYmVscyA9IFtub2RlLmxhYmVsXQ0KICAgICAgbGV0IHN0YXR1cyA9IFtkYXRhLkRhdGFbdGhpcy5zdGF0dXNDb2RlXV0NCiAgICAgIGxldCBsZXZlbCA9IDENCiAgICAgIHdoaWxlIChsZXZlbCA8IG5vZGUubGV2ZWwpIHsNCiAgICAgICAgbGFiZWxzID0gWy4uLmxhYmVscywgcGFyZW50Tm9kZS5sYWJlbF0NCiAgICAgICAgc3RhdHVzID0gWy4uLnN0YXR1cywgZGF0YS5EYXRhW3RoaXMuc3RhdHVzQ29kZV1dDQogICAgICAgIHBhcmVudE5vZGUgPSBwYXJlbnROb2RlLnBhcmVudA0KICAgICAgICBsZXZlbCsrDQogICAgICB9DQogICAgICBsYWJlbHMgPSBsYWJlbHMuZmlsdGVyKHYgPT4gISF2KQ0KICAgICAgc3RhdHVzID0gc3RhdHVzLmZpbHRlcih2ID0+ICEhdikNCiAgICAgIGxldCByZXN1bHRMYWJlbCA9IHRydWUNCiAgICAgIGxldCByZXN1bHRTdGF0dXMgPSB0cnVlDQogICAgICBpZiAodGhpcy5zdGF0dXNUeXBlKSB7DQogICAgICAgIHJlc3VsdFN0YXR1cyA9IHN0YXR1cy5zb21lKHMgPT4gcy5pbmRleE9mKHN0YXR1c1ZhbCkgIT09IC0xKQ0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMucHJvamVjdE5hbWUpIHsNCiAgICAgICAgcmVzdWx0TGFiZWwgPSBsYWJlbHMuc29tZShzID0+IHMuaW5kZXhPZihsYWJlbFZhbCkgIT09IC0xKQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIHJlc3VsdExhYmVsICYmIHJlc3VsdFN0YXR1cw0KICAgIH0sDQogICAgYXN5bmMgZ2V0Q29uZmlnKCkgew0KICAgICAgbGV0IGNvZGUgPSAnJw0KICAgICAgY29kZSA9IHRoaXMuaXNDb20NCiAgICAgICAgPyAnUFJPQ29tRHJhZnRFZGl0VGJDb25maWcnDQogICAgICAgIDogJ1BST1BhcnREcmFmdEVkaXRUYkNvbmZpZ19uZXcnDQogICAgICBhd2FpdCB0aGlzLmdldFRhYmxlQ29uZmlnKGNvZGUpDQogICAgICAvLyB0aGlzLmZldGNoRGF0YSgpDQogICAgfSwNCiAgICBmaWx0ZXJEYXRhKHBhZ2UpIHsNCiAgICAgIGNvbnNvbGUubG9nKDIyKQ0KICAgICAgY29uc3Qgc3BsaXRBbmRDbGVhbiA9IChpbnB1dCkgPT4gaW5wdXQudHJpbSgpLnJlcGxhY2UoL1xzKy9nLCAnICcpLnNwbGl0KCcgJykNCg0KICAgICAgaWYgKHRoaXMuY3VyU2VhcmNoID09PSAxKSB7DQogICAgICAgIHRoaXMuZm9ybS5Db21wX0NvZGUgPSB0aGlzLnNlYXJjaENvbnRlbnQNCiAgICAgICAgdGhpcy5mb3JtLkNvbXBfQ29kZUJsdXIgPSAnJw0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuY3VyU2VhcmNoID09PSAwKSB7DQogICAgICAgIHRoaXMuZm9ybS5Db21wX0NvZGVCbHVyID0gdGhpcy5zZWFyY2hDb250ZW50DQogICAgICAgIHRoaXMuZm9ybS5Db21wX0NvZGUgPSAnJw0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuY3VyUGFydFNlYXJjaCA9PT0gMSkgew0KICAgICAgICB0aGlzLmZvcm0uUGFydF9Db2RlQmx1ciA9ICcnDQogICAgICAgIHRoaXMuZm9ybS5QYXJ0X0NvZGUgPSB0aGlzLnNlYXJjaFBhcnRDb250ZW50DQogICAgICB9DQogICAgICBpZiAodGhpcy5jdXJQYXJ0U2VhcmNoID09PSAwKSB7DQogICAgICAgIHRoaXMuZm9ybS5QYXJ0X0NvZGUgPSAnJw0KICAgICAgICB0aGlzLmZvcm0uUGFydF9Db2RlQmx1ciA9IHRoaXMuc2VhcmNoUGFydENvbnRlbnQNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmN1clVuaXRQYXJ0U2VhcmNoID09PSAxKSB7DQogICAgICAgIHRoaXMuZm9ybS5Vbml0X1BhcnRfQ29kZUJsdXIgPSAnJw0KICAgICAgICB0aGlzLmZvcm0uVW5pdF9QYXJ0X0NvZGUgPSB0aGlzLnNlYXJjaFVuaXRQYXJ0Q29udGVudA0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuY3VyVW5pdFBhcnRTZWFyY2ggPT09IDApIHsNCiAgICAgICAgdGhpcy5mb3JtLlVuaXRfUGFydF9Db2RlID0gJycNCiAgICAgICAgdGhpcy5mb3JtLlVuaXRfUGFydF9Db2RlQmx1ciA9IHRoaXMuc2VhcmNoVW5pdFBhcnRDb250ZW50DQogICAgICB9DQoNCiAgICAgIGNvbnN0IGYgPSBbXQ0KICAgICAgZm9yIChjb25zdCBmb3JtS2V5IGluIHRoaXMuZm9ybSkgew0KICAgICAgICBpZiAodGhpcy5mb3JtW2Zvcm1LZXldIHx8IHRoaXMuZm9ybVtmb3JtS2V5XSA9PT0gZmFsc2UpIHsNCiAgICAgICAgICBmLnB1c2goZm9ybUtleSkNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgaWYgKCFmLmxlbmd0aCkgew0KICAgICAgICB0aGlzLnNldFBhZ2UoKQ0KICAgICAgICAhcGFnZSAmJiAodGhpcy5wYWdlSW5mby5wYWdlID0gMSkNCiAgICAgICAgdGhpcy5wYWdlSW5mby50b3RhbCA9IHRoaXMudGJEYXRhLmxlbmd0aA0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgY29uc3QgY2hlY2tNYXRjaCA9IChvcmlnaW4sIGNvbXApID0+IHsNCiAgICAgICAgY29uc3QgX2NvbXAgPSBjb21wLm1hcChjb2RlID0+IHsNCiAgICAgICAgICBjb25zdCBba2V5LCB2YWx1ZV0gPSBjb2RlLnNwbGl0KCcmJiYnKQ0KICAgICAgICAgIHJldHVybiBrZXkNCiAgICAgICAgfSkNCiAgICAgICAgY29uc3QgX29yaWdpbiA9IG9yaWdpbi5tYXAoY29kZSA9PiB7DQogICAgICAgICAgY29uc3QgW2tleSwgdmFsdWVdID0gY29kZS5zcGxpdCgnJiYmJykNCiAgICAgICAgICByZXR1cm4ga2V5DQogICAgICAgIH0pDQogICAgICAgIHJldHVybiBfb3JpZ2luLnNvbWUoaXRlbSA9PiB7DQogICAgICAgICAgcmV0dXJuIF9jb21wLnNvbWUodmFsdWUgPT4gaXRlbS5pbmNsdWRlcyh2YWx1ZSkpDQogICAgICAgIH0pDQogICAgICB9DQogICAgICBjb25zdCBjaGVja0V4YWN0TWF0Y2ggPSAob3JpZ2luLCBjb21wKSA9PiB7DQogICAgICAgIGNvbnN0IF9jb21wID0gY29tcC5tYXAoY29kZSA9PiB7DQogICAgICAgICAgY29uc3QgW2tleSwgdmFsdWVdID0gY29kZS5zcGxpdCgnJiYmJykNCiAgICAgICAgICByZXR1cm4ga2V5DQogICAgICAgIH0pDQogICAgICAgIGNvbnN0IF9vcmlnaW4gPSBvcmlnaW4ubWFwKGNvZGUgPT4gew0KICAgICAgICAgIGNvbnN0IFtrZXksIHZhbHVlXSA9IGNvZGUuc3BsaXQoJyYmJicpDQogICAgICAgICAgcmV0dXJuIGtleQ0KICAgICAgICB9KQ0KDQogICAgICAgIHJldHVybiBfb3JpZ2luLnNvbWUoaXRlbSA9PiBfY29tcC5pbmNsdWRlcyhpdGVtKSkNCiAgICAgIH0NCg0KICAgICAgY29uc3QgdGVtVGJEYXRhID0gdGhpcy50YkRhdGEuZmlsdGVyKHYgPT4gew0KICAgICAgICB2LmNoZWNrZWQgPSBmYWxzZQ0KICAgICAgICBjb25zdCBjb21wQ29kZSA9IHYuQ29tcG9uZW50X0NvZGVzIHx8IFtdDQoNCiAgICAgICAgaWYgKHRoaXMuZm9ybS5Db21wX0NvZGUudHJpbSgpKSB7DQogICAgICAgICAgY29uc3QgY29tcENvZGVBcnJheSA9IHNwbGl0QW5kQ2xlYW4odGhpcy5mb3JtLkNvbXBfQ29kZSkNCiAgICAgICAgICBpZiAoY29tcENvZGVBcnJheS5sZW5ndGgpIHsNCiAgICAgICAgICAgIGNvbnN0IGZsYWcgPSBjaGVja0V4YWN0TWF0Y2goY29tcENvZGUsIGNvbXBDb2RlQXJyYXkpDQogICAgICAgICAgICBjb25zb2xlLmxvZyg4ODcsIGNvbXBDb2RlLCBjb21wQ29kZUFycmF5LCBmbGFnKQ0KICAgICAgICAgICAgaWYgKCFmbGFnKSByZXR1cm4gZmFsc2UNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICBpZiAodGhpcy5mb3JtLkNvbXBfQ29kZUJsdXIudHJpbSgpKSB7DQogICAgICAgICAgY29uc3QgY29tcENvZGVBcnJheSA9IHNwbGl0QW5kQ2xlYW4odGhpcy5mb3JtLkNvbXBfQ29kZUJsdXIpDQogICAgICAgICAgaWYgKGNvbXBDb2RlQXJyYXkubGVuZ3RoKSB7DQogICAgICAgICAgICBjb25zdCBmbGFnID0gY2hlY2tNYXRjaChjb21wQ29kZSwgY29tcENvZGVBcnJheSkNCiAgICAgICAgICAgIGlmICghZmxhZykgcmV0dXJuIGZhbHNlDQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgY29uc3QgY05hbWUgPSAodlsnQ29tcG9uZW50X0NvZGUnXSB8fCAnJykuc3BsaXQoJywnKQ0KICAgICAgICBjb25zb2xlLmxvZygnY05hbWUnLCBjTmFtZSkNCiAgICAgICAgaWYgKHRoaXMuZm9ybS5Vbml0X1BhcnRfQ29kZUJsdXIudHJpbSgpKSB7DQogICAgICAgICAgY29uc3QgdW5pdFBhcnRDb2RlQmx1ckFycmF5ID0gc3BsaXRBbmRDbGVhbih0aGlzLmZvcm0uVW5pdF9QYXJ0X0NvZGVCbHVyKQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCd1bml0UGFydENvZGVCbHVyQXJyYXknLCB1bml0UGFydENvZGVCbHVyQXJyYXkpDQogICAgICAgICAgaWYgKHYuQmVsb25nX1RvX0NvbXBvbmVudCB8fCAhdW5pdFBhcnRDb2RlQmx1ckFycmF5LnNvbWUoY29kZSA9Pg0KICAgICAgICAgICAgY05hbWUuc29tZShuYW1lID0+IG5hbWUuaW5jbHVkZXMoY29kZSkpDQogICAgICAgICAgKSkgew0KICAgICAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgaWYgKHRoaXMuZm9ybS5Vbml0X1BhcnRfQ29kZS50cmltKCkpIHsNCiAgICAgICAgICBjb25zdCB1bml0UGFydENvZGVBcnJheSA9IHNwbGl0QW5kQ2xlYW4odGhpcy5mb3JtLlVuaXRfUGFydF9Db2RlKQ0KICAgICAgICAgIGlmICh2LkJlbG9uZ19Ub19Db21wb25lbnQgfHwgIXVuaXRQYXJ0Q29kZUFycmF5LnNvbWUoY29kZSA9PiBjTmFtZS5pbmNsdWRlcyhjb2RlKSkpIHsNCiAgICAgICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIGlmICh0aGlzLmZvcm0uVHlwZSAmJiB2LlR5cGUgIT09IHRoaXMuZm9ybS5UeXBlKSB7DQogICAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICAgIH0NCg0KICAgICAgICBpZiAodGhpcy5mb3JtLlBhcnRfQ29kZUJsdXIudHJpbSgpKSB7DQogICAgICAgICAgY29uc3QgcGFydENvZGVCbHVyQXJyYXkgPSBzcGxpdEFuZENsZWFuKHRoaXMuZm9ybS5QYXJ0X0NvZGVCbHVyKQ0KICAgICAgICAgIGlmICghcGFydENvZGVCbHVyQXJyYXkuc29tZShjb2RlID0+IHZbJ1BhcnRfQ29kZSddLmluY2x1ZGVzKGNvZGUpKSkgew0KICAgICAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgaWYgKHRoaXMuZm9ybS5QYXJ0X0NvZGUudHJpbSgpKSB7DQogICAgICAgICAgY29uc3QgcGFydENvZGVBcnJheSA9IHNwbGl0QW5kQ2xlYW4odGhpcy5mb3JtLlBhcnRfQ29kZSkNCiAgICAgICAgICBpZiAoIXBhcnRDb2RlQXJyYXkuaW5jbHVkZXModlsnUGFydF9Db2RlJ10pKSB7DQogICAgICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICBpZiAodGhpcy5mb3JtLkluc3RhbGxVbml0X0lkLmxlbmd0aCAmJiAhdGhpcy5mb3JtLkluc3RhbGxVbml0X0lkLmluY2x1ZGVzKHYuSW5zdGFsbFVuaXRfSWQpKSB7DQogICAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICAgIH0NCg0KICAgICAgICBpZiAodGhpcy5mb3JtLlR5cGVfTmFtZSAhPT0gJycgJiYgdi5UeXBlX05hbWUgIT09IHRoaXMuZm9ybS5UeXBlX05hbWUpIHsNCiAgICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgICAgfQ0KDQogICAgICAgIGlmICh0aGlzLmZvcm0uU3BlYy50cmltKCkgIT09ICcnKSB7DQogICAgICAgICAgY29uc3Qgc3BlY0FycmF5ID0gc3BsaXRBbmRDbGVhbih0aGlzLmZvcm0uU3BlYykNCiAgICAgICAgICBpZiAoIXNwZWNBcnJheS5zb21lKHNwZWMgPT4gdi5TcGVjLmluY2x1ZGVzKHNwZWMpKSkgew0KICAgICAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIGlmICh0aGlzLnNlYXJjaENvbnRlbnQudHJpbSgpLmxlbmd0aCkgew0KICAgICAgICAgIGxldCBjc0NvdW50ID0gMA0KDQogICAgICAgICAgdi5jb21wb25lbnRNYXAgPSAodi5Db21wb25lbnRfQ29kZXMgfHwgW10pLnJlZHVjZSgoYWNjLCBjb2RlKSA9PiB7DQogICAgICAgICAgICBjb25zdCBba2V5LCB2YWx1ZV0gPSBjb2RlLnNwbGl0KCcmJiYnKQ0KICAgICAgICAgICAgYWNjW2tleV0gPSBwYXJzZUludCh2YWx1ZSkNCiAgICAgICAgICAgIGlmICh0aGlzLmN1clNlYXJjaCA9PT0gMSkgew0KICAgICAgICAgICAgICBjb25zdCBjb21wQ29kZUFycmF5ID0gc3BsaXRBbmRDbGVhbih0aGlzLmZvcm0uQ29tcF9Db2RlKQ0KICAgICAgICAgICAgICBpZiAoY29tcENvZGVBcnJheS5sZW5ndGgpIHsNCiAgICAgICAgICAgICAgICBjb25zdCBmbGFnID0gY2hlY2tFeGFjdE1hdGNoKFtrZXldLCBjb21wQ29kZUFycmF5KQ0KICAgICAgICAgICAgICAgIGlmIChmbGFnKSB7DQogICAgICAgICAgICAgICAgICBjc0NvdW50ICs9IHBhcnNlSW50KHZhbHVlKQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgY29uc3QgY29tcENvZGVBcnJheSA9IHNwbGl0QW5kQ2xlYW4odGhpcy5mb3JtLkNvbXBfQ29kZUJsdXIpDQogICAgICAgICAgICAgIGlmIChjb21wQ29kZUFycmF5Lmxlbmd0aCkgew0KICAgICAgICAgICAgICAgIGNvbnN0IGZsYWcgPSBjaGVja01hdGNoKFtrZXldLCBjb21wQ29kZUFycmF5KQ0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdwZmxhZycsIGtleSwgY29tcENvZGVBcnJheSwgZmxhZywgdmFsdWUpDQogICAgICAgICAgICAgICAgaWYgKGZsYWcpIHsNCiAgICAgICAgICAgICAgICAgIGNzQ291bnQgKz0gcGFyc2VJbnQodmFsdWUpDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgICByZXR1cm4gYWNjDQogICAgICAgICAgfSwge30pDQogICAgICAgICAgdGhpcy4kc2V0KHYsICdjc0NvdW50JywgTWF0aC5taW4oY3NDb3VudCwgdi5DYW5fU2NoZHVsaW5nX0NvdW50KSkNCiAgICAgICAgICB0aGlzLiRzZXQodiwgJ2NzQ291bnRXZWlnaHQnLCBNYXRoLm1pbih2LkNhbl9TY2hkdWxpbmdfV2VpZ2h0LCB2LmNzQ291bnQgKiB2LldlaWdodCkpDQoNCiAgICAgICAgICB2LnNlYXJjaGNvdW50ID0gdi5jb3VudA0KICAgICAgICAgIHYuc2VhcmNoY291bnRNYXggPSB2Lm1heENvdW50DQogICAgICAgICAgLy8gY29uc3QgY3MgPSB2LkNvbXBvbmVudF9Db2RlcyB8fCBbXQ0KICAgICAgICAgIC8vIGxldCBtaW4gPSAwDQogICAgICAgICAgLy8gY3MuZm9yRWFjaCgoZWxlbWVudCwgaWR4KSA9PiB7DQogICAgICAgICAgLy8gICBjb25zdCBba2V5LCB2YWx1ZV0gPSBlbGVtZW50LnNwbGl0KCcmJiYnKQ0KICAgICAgICAgIC8vICAgbWluID0gdi5jb21wb25lbnRNYXBba2V5XQ0KICAgICAgICAgIC8vIH0pDQoNCiAgICAgICAgICB2LmNvdW50ID0gdi5jc0NvdW50DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdi5jb3VudCA9IHYuQ2FuX1NjaGR1bGluZ19Db3VudA0KICAgICAgICB9DQoNCiAgICAgICAgLy8gdi5DYW5fU2NoZHVsaW5nX0NvdW50ID0gdi5jc0NvdW50DQogICAgICAgIC8vIHYuQ2FuX1NjaGR1bGluZ19XZWlnaHQgPSB2LmNzQ291bnRXZWlnaHQNCg0KICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgfSkNCg0KICAgICAgIXBhZ2UgJiYgKHRoaXMucGFnZUluZm8ucGFnZSA9IDEpDQogICAgICB0aGlzLnBhZ2VJbmZvLnRvdGFsID0gdGVtVGJEYXRhLmxlbmd0aA0KICAgICAgdGhpcy5zZXRQYWdlKHRlbVRiRGF0YSkNCiAgICAgIGlmICh0aGlzLnNlYXJjaENvbnRlbnQudHJpbSgpLmxlbmd0aCkgew0KICAgICAgICB0aGlzLnNob3dTYyA9IHRydWUNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZVNlYXJjaCgpIHsNCiAgICAgIHRoaXMudG90YWxTZWxlY3Rpb24gPSBbXQ0KICAgICAgdGhpcy5jbGVhclNlbGVjdCgpDQogICAgICBpZiAodGhpcy50YkRhdGE/Lmxlbmd0aCkgew0KICAgICAgICB0aGlzLnRiRGF0YS5mb3JFYWNoKGl0ZW0gPT4gaXRlbS5jaGVja2VkID0gZmFsc2UpDQogICAgICAgIHRoaXMuZmlsdGVyRGF0YSgpDQogICAgICB9DQogICAgICB0aGlzLnNob3dTYyA9ICEhdGhpcy5zZWFyY2hDb250ZW50LnRyaW0oKS5sZW5ndGgNCiAgICB9LA0KICAgIHRiU2VsZWN0Q2hhbmdlKGFycmF5KSB7DQogICAgICB0aGlzLnRvdGFsU2VsZWN0aW9uID0gdGhpcy50YkRhdGEuZmlsdGVyKHYgPT4gdi5jaGVja2VkKQ0KICAgIH0sDQogICAgY2xlYXJTZWxlY3QoKSB7DQogICAgICB0aGlzLiRyZWZzLnhUYWJsZTEuY2xlYXJDaGVja2JveFJvdygpDQogICAgICB0aGlzLnRvdGFsU2VsZWN0aW9uID0gW10NCiAgICB9LA0KICAgIGFzeW5jIGZldGNoRGF0YSgpIHsNCiAgICAgIHRoaXMuaGFuZGxlUmVzZXQoKQ0KICAgICAgdGhpcy50YkxvYWRpbmcgPSB0cnVlDQogICAgICBpZiAodGhpcy5pc0NvbSkgew0KICAgICAgICBhd2FpdCB0aGlzLmdldENvbVRiRGF0YSgpDQogICAgICB9IGVsc2Ugew0KICAgICAgICBhd2FpdCB0aGlzLmdldFBhcnRUYkRhdGEoKQ0KICAgICAgfQ0KICAgICAgdGhpcy5pbml0VGJEYXRhKCkNCiAgICAgIHRoaXMuZmlsdGVyRGF0YSgpDQogICAgICB0aGlzLnRiTG9hZGluZyA9IGZhbHNlDQogICAgfSwNCiAgICBzZXRQYWdlRGF0YSgpIHsNCiAgICAgIGlmICh0aGlzLnRiRGF0YT8ubGVuZ3RoKSB7DQogICAgICAgIHRoaXMucGFnZUluZm8ucGFnZSA9IDENCiAgICAgICAgdGhpcy50YkRhdGEgPSB0aGlzLnRiRGF0YS5maWx0ZXIodiA9PiB2LkNhbl9TY2hkdWxpbmdfQ291bnQgPiAwKQ0KICAgICAgICB0aGlzLmZpbHRlckRhdGEoKQ0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlU2F2ZSh0eXBlID0gMikgew0KICAgICAgaWYgKHR5cGUgPT09IDEpIHsNCiAgICAgICAgdGhpcy5hZGRMb2FkaW5nID0gdHJ1ZQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5zYXZlTG9hZGluZyA9IHRydWUNCiAgICAgIH0NCiAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICB0aGlzLnRvdGFsU2VsZWN0aW9uLmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICBjb25zdCBpbnRDb3VudCA9IHBhcnNlSW50KGl0ZW0uY291bnQpDQogICAgICAgICAgaWYgKHRoaXMuc2VhcmNoQ29udGVudC50cmltKCkubGVuZ3RoKSB7DQogICAgICAgICAgICBpdGVtLlNjaGR1bGVkX0NvdW50ID0gaXRlbS5DYW5fU2NoZHVsaW5nX0NvdW50DQoNCiAgICAgICAgICAgIGl0ZW0ubWF4Q291bnQgPSBpdGVtLkNhbl9TY2hkdWxpbmdfQ291bnQNCiAgICAgICAgICAgIGl0ZW0uY2hvb3NlQ291bnQgPSBpbnRDb3VudA0KICAgICAgICAgICAgaXRlbS5jb3VudCA9IGl0ZW0uQ2FuX1NjaGR1bGluZ19Db3VudA0KDQogICAgICAgICAgICBpdGVtLkNhbl9TY2hkdWxpbmdfQ291bnQgPSAwDQogICAgICAgICAgICBpdGVtLkNhbl9TY2hkdWxpbmdfV2VpZ2h0ID0gaXRlbS5DYW5fU2NoZHVsaW5nX0NvdW50ICogaXRlbS5XZWlnaHQNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgaXRlbS5TY2hkdWxlZF9Db3VudCArPSBpbnRDb3VudA0KICAgICAgICAgICAgaXRlbS5DYW5fU2NoZHVsaW5nX0NvdW50IC09IGludENvdW50DQogICAgICAgICAgICBpdGVtLkNhbl9TY2hkdWxpbmdfV2VpZ2h0ID0gaXRlbS5DYW5fU2NoZHVsaW5nX0NvdW50ICogaXRlbS5XZWlnaHQNCiAgICAgICAgICAgIGl0ZW0ubWF4Q291bnQgPSBpbnRDb3VudA0KICAgICAgICAgICAgaXRlbS5jaG9vc2VDb3VudCA9IGludENvdW50DQogICAgICAgICAgICBpdGVtLmNvdW50ID0gaXRlbS5DYW5fU2NoZHVsaW5nX0NvdW50DQogICAgICAgICAgfQ0KDQogICAgICAgICAgaXRlbS5jaGVja2VkID0gZmFsc2UNCiAgICAgICAgfSkNCiAgICAgICAgY29uc3QgY3AgPSBkZWVwQ2xvbmUodGhpcy50b3RhbFNlbGVjdGlvbikNCg0KICAgICAgICAvLyB0aGlzLiRlbWl0KCdzZW5kU2VsZWN0TGlzdCcsIGNwKQ0KICAgICAgICB0aGlzLmFkZExvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB0aGlzLmNsZWFyU2VsZWN0KCkNCiAgICAgICAgLy8gdGhpcy5zZXRQYWdlKCkNCiAgICAgICAgdGhpcy5zZXRQYWdlRGF0YSgpDQogICAgICAgIGlmICh0eXBlID09PSAyKSB7DQogICAgICAgICAgdGhpcy4kZW1pdCgnc2VuZFNlbGVjdExpc3QnLCBjcCkNCiAgICAgICAgICB0aGlzLiRlbWl0KCdjbG9zZScpDQogICAgICAgICAgdGhpcy5mVGFibGUgPSBbXQ0KICAgICAgICAgIHRoaXMudGJEYXRhID0gW10NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRlbWl0KCdhZGRUb1RiTGlzdCcsIGNwKQ0KICAgICAgICB9DQogICAgICB9LCAwKQ0KICAgIH0sDQogICAgaW5pdFRiRGF0YSgpIHsNCiAgICAgIC8vIOiuvue9ruaWh+acrOahhumAieaLqeeahOaOkuS6p+aVsOmHjyzorr7nva7oh6rlrprkuYnllK/kuIDnoIENCiAgICAgIGNvbnN0IG9iaktleSA9IHt9DQogICAgICBpZiAoIXRoaXMudGJEYXRhPy5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy50YkRhdGEgPSBbXQ0KICAgICAgICAvLyB0aGlzLmJhY2tlbmRUYiA9IFtdDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgY29uc29sZS5sb2coOTk4LCBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMudGJEYXRhKSkpDQogICAgICAvLyB0aGlzLmJhY2tlbmRUYiA9IGRlZXBDbG9uZSh0aGlzLnRiRGF0YSkNCiAgICAgIHRoaXMudGJEYXRhID0gdGhpcy50YkRhdGEuZmlsdGVyKGl0ZW0gPT4gew0KICAgICAgICB0aGlzLiRzZXQoaXRlbSwgJ2NvdW50JywgaXRlbS5DYW5fU2NoZHVsaW5nX0NvdW50KQ0KICAgICAgICB0aGlzLiRzZXQoaXRlbSwgJ21heENvdW50JywgaXRlbS5DYW5fU2NoZHVsaW5nX0NvdW50KQ0KICAgICAgICBpdGVtLnV1aWQgPSBnZXRVbmlxdWUodGhpcy5pc0NvbSwgaXRlbSkNCiAgICAgICAgb2JqS2V5W2l0ZW0uVHlwZV0gPSB0cnVlDQogICAgICAgIC8vIGxldCBjc0NvdW50ID0gMA0KICAgICAgICAvLyBpdGVtLmNvbXBvbmVudE1hcCA9IChpdGVtLkNvbXBvbmVudF9Db2RlcyB8fCBbXSkucmVkdWNlKChhY2MsIGNvZGUpID0+IHsNCiAgICAgICAgLy8gICBjb25zdCBba2V5LCB2YWx1ZV0gPSBjb2RlLnNwbGl0KCcmJiYnKQ0KICAgICAgICAvLyAgIGFjY1trZXldID0gcGFyc2VJbnQodmFsdWUpDQogICAgICAgIC8vICAgY3NDb3VudCArPSBwYXJzZUludCh2YWx1ZSkNCiAgICAgICAgLy8gICByZXR1cm4gYWNjDQogICAgICAgIC8vIH0sIHt9KQ0KICAgICAgICAvLyB0aGlzLiRzZXQoaXRlbSwgJ2NzQ291bnQnLCBjc0NvdW50KQ0KICAgICAgICAvLyBPYmplY3Qua2V5cyhpdGVtLmNvbXBvbmVudE1hcCkuZm9yRWFjaChrZXkgPT4gew0KICAgICAgICAvLyAgIHRoaXMuJHNldChpdGVtLCBrZXksIGl0ZW0uY29tcG9uZW50TWFwW2tleV0pDQogICAgICAgIC8vIH0pDQoNCiAgICAgICAgcmV0dXJuICF0aGlzLmFkZFRiS2V5cy5pbmNsdWRlcyhpdGVtLnV1aWQpDQogICAgICB9KQ0KICAgICAgLy8gICAubWFwKChpdGVtKSA9PiB7DQogICAgICAvLyAgIHRoaXMuJHNldChpdGVtLCAnY291bnQnLCBpdGVtLkNhbl9TY2hkdWxpbmdfQ291bnQpDQogICAgICAvLyAgIHRoaXMuJHNldChpdGVtLCAnbWF4Q291bnQnLCBpdGVtLkNhbl9TY2hkdWxpbmdfQ291bnQpDQogICAgICAvLyAgIC8vIGl0ZW0udXVpZCA9IHV1aWR2NCgpDQogICAgICAvLyAgIGl0ZW0udXVpZCA9IGl0ZW0uSW5zdGFsbFVuaXRfSWQgKyBpdGVtLlBhcnRfQWdncmVnYXRlX0lkDQogICAgICAvLyAgIG9iaktleVtpdGVtLlR5cGVdID0gdHJ1ZQ0KICAgICAgLy8NCiAgICAgIC8vICAgY29uc3QgX3NlbGVjdExpc3QgPSB0aGlzLnNlbGVjdFRiRGF0YS5maWx0ZXIodiA9PiB2LnB1dWlkKQ0KICAgICAgLy8gICBjb25zb2xlLmxvZygnX3NlbGVjdExpc3QnLCBfc2VsZWN0TGlzdCkNCiAgICAgIC8vICAgLy8gX3NlbGVjdExpc3QuZm9yRWFjaCgoZWxlbWVudCwgaWR4KSA9PiB7DQogICAgICAvLyAgIC8vICAgaWYoZWxlbWVudC5wdXVpZCA9PT0gaXRlbS51dWlkKXsNCiAgICAgIC8vICAgLy8NCiAgICAgIC8vICAgLy8gICB9DQogICAgICAvLyAgIC8vIH0pDQogICAgICAvLyAgIHJldHVybiBpdGVtDQogICAgICAvLyB9KQ0KDQogICAgICAvLyB0aGlzLmJhY2tlbmRUYiA9IGRlZXBDbG9uZSh0aGlzLnRiRGF0YSkNCiAgICB9LA0KICAgIGFzeW5jIGdldENvbVRiRGF0YSgpIHsNCiAgICAgIC8vIGNvbnN0IHsgaW5zdGFsbCwgYXJlYUlkIH0gPSB0aGlzLiRyb3V0ZS5xdWVyeQ0KICAgICAgY29uc3QgeyBDb21wX0NvZGVzLCAuLi5vYmogfSA9IHRoaXMuZm9ybQ0KICAgICAgbGV0IGNvZGVzID0gW10NCiAgICAgIGlmIChPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nLmNhbGwoQ29tcF9Db2RlcykgPT09ICdbb2JqZWN0IFN0cmluZ10nKSB7DQogICAgICAgIGNvZGVzID0gQ29tcF9Db2RlcyAmJiBDb21wX0NvZGVzLnNwbGl0KCcgJykuZmlsdGVyKHYgPT4gISF2KQ0KICAgICAgfQ0KICAgICAgYXdhaXQgR2V0Q2FuU2NoZHVsaW5nQ29tcHMoew0KICAgICAgICBJZHM6IHRoaXMuY3VycmVudElkcywNCiAgICAgICAgLi4ub2JqLA0KICAgICAgICBTY2hkdWxpbmdfUGxhbl9JZDogdGhpcy5zY2hlZHVsZUlkLA0KICAgICAgICBDb21wX0NvZGVzOiBjb2RlcywNCiAgICAgICAgSW5zdGFsbFVuaXRfSWQ6IHRoaXMuaW5zdGFsbElkLA0KICAgICAgICBBcmVhX0lkOiB0aGlzLmFyZWFJZA0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy5wYWdlSW5mby50b3RhbCA9IHJlcy5EYXRhLmxlbmd0aA0KICAgICAgICAgIHRoaXMudGJEYXRhID0gcmVzLkRhdGEubWFwKCh2LCBpZHgpID0+IHsNCiAgICAgICAgICAgIC8vIOW3suaOkuS6p+i1i+WAvA0KICAgICAgICAgICAgdi5vcmlnaW5hbFBhdGggPSB2LlNjaGVkdWxlZF9UZWNobm9sb2d5X1BhdGggPyB2LlNjaGVkdWxlZF9UZWNobm9sb2d5X1BhdGggOiAnJw0KICAgICAgICAgICAgdi5Xb3Jrc2hvcF9JZCA9IHYuU2NoZWR1bGVkX1dvcmtzaG9wX0lkDQogICAgICAgICAgICB2LldvcmtzaG9wX05hbWUgPSB2LlNjaGVkdWxlZF9Xb3Jrc2hvcF9OYW1lDQogICAgICAgICAgICB2LlRlY2hub2xvZ3lfUGF0aCA9IHYuU2NoZWR1bGVkX1RlY2hub2xvZ3lfUGF0aCB8fCB2LlRlY2hub2xvZ3lfUGF0aA0KICAgICAgICAgICAgLy8gaWYgKHYub3JpZ2luYWxQYXRoKSB7DQogICAgICAgICAgICAvLyB2LmlzRGlzYWJsZWQgPSB0cnVlDQogICAgICAgICAgICAvLyB9DQogICAgICAgICAgICB2LmNoZWNrZWQgPSBmYWxzZQ0KICAgICAgICAgICAgdi5pbml0Um93SW5kZXggPSBpZHgNCiAgICAgICAgICAgIHYuQXJlYV9OYW1lID0gdGhpcy5ub2RlTGFiZWxzLmpvaW4oJy8nKQ0KDQogICAgICAgICAgICAvLyB2LnRlY2hub2xvZ3lQYXRoRGlzYWJsZWQgPSAhIXYuVGVjaG5vbG9neV9QYXRoDQogICAgICAgICAgICByZXR1cm4gdg0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy5zZXRQYWdlKCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICAvKioNCiAgICAgKiDliIbpobUNCiAgICAgKi8NCiAgICBoYW5kbGVQYWdlQ2hhbmdlKHsgY3VycmVudFBhZ2UsIHBhZ2VTaXplIH0pIHsNCiAgICAgIGlmICh0aGlzLnRiTG9hZGluZykgcmV0dXJuDQogICAgICB0aGlzLnBhZ2VJbmZvLnBhZ2UgPSBjdXJyZW50UGFnZQ0KICAgICAgdGhpcy5wYWdlSW5mby5wYWdlU2l6ZSA9IHBhZ2VTaXplDQogICAgICB0aGlzLnNldFBhZ2UoKQ0KICAgICAgdGhpcy5maWx0ZXJEYXRhKGN1cnJlbnRQYWdlKQ0KICAgIH0sDQoNCiAgICBzZXRQYWdlKHRiID0gdGhpcy50YkRhdGEpIHsNCiAgICAgIHRoaXMuZlRhYmxlID0gdGIuc2xpY2UoKHRoaXMucGFnZUluZm8ucGFnZSAtIDEpICogdGhpcy5wYWdlSW5mby5wYWdlU2l6ZSwgdGhpcy5wYWdlSW5mby5wYWdlICogdGhpcy5wYWdlSW5mby5wYWdlU2l6ZSkNCiAgICB9LA0KDQogICAgYXN5bmMgZ2V0UGFydFRiRGF0YSgpIHsNCiAgICAgIC8vIGNvbnN0IHsgaW5zdGFsbCwgYXJlYUlkIH0gPSB0aGlzLiRyb3V0ZS5xdWVyeQ0KICAgICAgYXdhaXQgR2V0Q2FuU2NoZHVsaW5nUGFydHMoew0KICAgICAgICBJZHM6IHRoaXMuY3VycmVudElkcywNCiAgICAgICAgLi4udGhpcy5mb3JtLA0KICAgICAgICBTY2hkdWxpbmdfUGxhbl9JZDogdGhpcy5zY2hlZHVsZUlkLA0KICAgICAgICBJbnN0YWxsVW5pdF9JZDogdGhpcy5pbnN0YWxsSWQsDQogICAgICAgIEFyZWFfSWQ6IHRoaXMuYXJlYUlkDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLnBhZ2VJbmZvLnRvdGFsID0gcmVzLkRhdGEubGVuZ3RoDQogICAgICAgICAgdGhpcy50YkRhdGEgPSByZXMuRGF0YS5tYXAoKHYsIGlkeCkgPT4gew0KICAgICAgICAgICAgdi5vcmlnaW5hbFBhdGggPSB2LlNjaGVkdWxlZF9UZWNobm9sb2d5X1BhdGggPyB2LlNjaGVkdWxlZF9UZWNobm9sb2d5X1BhdGggOiAnJw0KICAgICAgICAgICAgdi5Xb3Jrc2hvcF9JZCA9IHYuU2NoZWR1bGVkX1dvcmtzaG9wX0lkDQogICAgICAgICAgICB2LldvcmtzaG9wX05hbWUgPSB2LlNjaGVkdWxlZF9Xb3Jrc2hvcF9OYW1lDQogICAgICAgICAgICBpZiAodi5Db21wX0ltcG9ydF9EZXRhaWxfSWQpIHsNCiAgICAgICAgICAgICAgdi5QYXJ0X1VzZWRfUHJvY2VzcyA9IHRoaXMuZ2V0UGFydFVzZWRQcm9jZXNzKHYpDQogICAgICAgICAgICB9DQogICAgICAgICAgICB2LlRlY2hub2xvZ3lfUGF0aCA9IHYuU2NoZWR1bGVkX1RlY2hub2xvZ3lfUGF0aCB8fCB2LlRlY2hub2xvZ3lfUGF0aA0KICAgICAgICAgICAgLy8gdi5pc0Rpc2FibGVkID0gISF2Lm9yaWdpbmFsUGF0aA0KICAgICAgICAgICAgdi5jaGVja2VkID0gZmFsc2UNCiAgICAgICAgICAgIHYuaW5pdFJvd0luZGV4ID0gaWR4DQogICAgICAgICAgICAvLyB2LnBhcnRVc2VkUHJvY2Vzc0Rpc2FibGVkID0gdGhpcy5pc1BhcnRQcmVwYXJlID8gISF2LlBhcnRfVXNlZF9Qcm9jZXNzIDogZmFsc2UNCiAgICAgICAgICAgIC8vIHYudGVjaG5vbG9neVBhdGhEaXNhYmxlZCA9ICEhdi5UZWNobm9sb2d5X1BhdGgNCiAgICAgICAgICAgIGlmICghdGhpcy5pc1BhcnRQcmVwYXJlKSB7DQogICAgICAgICAgICAgIHYuVGVtcF9QYXJ0X1VzZWRfUHJvY2VzcyA9IHYuUGFydF9Vc2VkX1Byb2Nlc3MNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHJldHVybiB2DQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLnNldFBhcnRDb2x1bW4oKQ0KICAgICAgICAgIHRoaXMuc2V0UGFnZSgpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KDQogICAgICBjb25zdCBzdWJtaXRPYmogPSB0aGlzLnRiRGF0YS5tYXAoaXRlbSA9PiB7DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgSWQ6IGl0ZW0uUGFydF9BZ2dyZWdhdGVfSWQsDQogICAgICAgICAgVHlwZTogMQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgYXdhaXQgR2V0U3RvcExpc3Qoc3VibWl0T2JqKS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgY29uc3Qgc3RvcE1hcCA9IHt9DQogICAgICAgICAgcmVzLkRhdGEuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICAgIHN0b3BNYXBbaXRlbS5JZF0gPSAhIWl0ZW0uSXNfU3RvcA0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy50YkRhdGEuZm9yRWFjaChyb3cgPT4gew0KICAgICAgICAgICAgaWYgKHN0b3BNYXAuaGFzT3duUHJvcGVydHkocm93LlBhcnRfQWdncmVnYXRlX0lkKSkgew0KICAgICAgICAgICAgICB0aGlzLiRzZXQocm93LCAnc3RvcEZsYWcnLCBzdG9wTWFwW3Jvdy5QYXJ0X0FnZ3JlZ2F0ZV9JZF0pDQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGNoZWNrQ2hlY2tib3hNZXRob2QoeyByb3cgfSkgew0KICAgICAgcmV0dXJuICFyb3cuc3RvcEZsYWcNCiAgICB9LA0KICAgIGdldFBhcnRVc2VkUHJvY2VzcyhpdGVtKSB7DQogICAgICBpZiAoaXRlbS5TY2hlZHVsZWRfVXNlZF9Qcm9jZXNzKSB7DQogICAgICAgIHJldHVybiBpdGVtLlNjaGVkdWxlZF9Vc2VkX1Byb2Nlc3MNCiAgICAgIH0NCiAgICAgIGlmIChpdGVtLkNvbXBvbmVudF9UZWNobm9sb2d5X1BhdGgpIHsNCiAgICAgICAgY29uc3QgbGlzdCA9IGl0ZW0uQ29tcG9uZW50X1RlY2hub2xvZ3lfUGF0aC5zcGxpdCgnLycpDQogICAgICAgIGlmIChsaXN0LmluY2x1ZGVzKGl0ZW0uUGFydF9Vc2VkX1Byb2Nlc3MpKSB7DQogICAgICAgICAgcmV0dXJuIGl0ZW0uUGFydF9Vc2VkX1Byb2Nlc3MNCiAgICAgICAgfSBlbHNlIGlmIChsaXN0LmluY2x1ZGVzKGl0ZW0uUGFydF9UeXBlX1VzZWRfUHJvY2VzcykpIHsNCiAgICAgICAgICByZXR1cm4gaXRlbS5QYXJ0X1R5cGVfVXNlZF9Qcm9jZXNzDQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGlmIChpdGVtLlBhcnRfVXNlZF9Qcm9jZXNzKSB7DQogICAgICAgICAgcmV0dXJuIGl0ZW0uUGFydF9Vc2VkX1Byb2Nlc3MNCiAgICAgICAgfSBlbHNlIGlmIChpdGVtLlBhcnRfVHlwZV9Vc2VkX1Byb2Nlc3MpIHsNCiAgICAgICAgICByZXR1cm4gaXRlbS5QYXJ0X1R5cGVfVXNlZF9Qcm9jZXNzDQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgcmV0dXJuICcnDQogICAgfSwNCiAgICBzZXRQYXJ0Q29sdW1uKCkgew0KICAgICAgLy8g57qv6Zu25Lu2DQogICAgICB0aGlzLmlzT3duZXJOdWxsID0gdGhpcy50YkRhdGEuZXZlcnkodiA9PiAhdi5Db21wX0ltcG9ydF9EZXRhaWxfSWQpDQogICAgICBjb25zb2xlLmxvZygndGhpcy5pc093bmVyTnVsbCcsIHRoaXMuaXNPd25lck51bGwpDQogICAgICBpZiAodGhpcy5pc093bmVyTnVsbCkgew0KICAgICAgICBjb25zdCBpZHggPSB0aGlzLmNvbHVtbnMuZmluZEluZGV4KHYgPT4gdi5Db2RlID09PSAnQ29tcG9uZW50X0NvZGUnKQ0KICAgICAgICBpZHggIT09IC0xICYmIHRoaXMuY29sdW1ucy5zcGxpY2UoaWR4LCAxKQ0KICAgICAgfQ0KICAgIH0sDQogICAgbWVyZ2VEYXRhKGxpc3QpIHsNCiAgICAgIC8qICAgICAgY29uc29sZS5sb2coJ2xpc3QnLCBsaXN0KQ0KICAgICAgY29uc29sZS5sb2coJ3RoaXMuYmFja2VuZFRiJywgdGhpcy5iYWNrZW5kVGIpDQogICAgICBsaXN0DQogICAgICAgIC5mb3JFYWNoKChlbGVtZW50LCBpbmRleCkgPT4gew0KICAgICAgICAgIGNvbnN0IGlkeCA9IHRoaXMuYmFja2VuZFRiLmZpbmRJbmRleCgNCiAgICAgICAgICAgIChpdGVtKSA9PiBlbGVtZW50LnB1dWlkICYmIGl0ZW0udXVpZCA9PT0gZWxlbWVudC5wdXVpZA0KICAgICAgICAgICkNCiAgICAgICAgICBjb25zb2xlLmxvZygnaWR4JywgaWR4LCB0aGlzLmJhY2tlbmRUYltpZHhdKQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCdpbmRleCcsIGluZGV4KQ0KICAgICAgICAgIGlmIChpZHggIT09IC0xKSB7DQogICAgICAgICAgICB0aGlzLnRiRGF0YS5zcGxpY2UoaWR4LCAwLCBkZWVwQ2xvbmUodGhpcy5iYWNrZW5kVGJbaWR4XSkpDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KDQogICAgICB0aGlzLnRiRGF0YS5zb3J0KChhLCBiKSA9PiBhLmluaXRSb3dJbmRleCAtIGIuaW5pdFJvd0luZGV4KQ0KICAgICAgY29uc29sZS5sb2coJ3RoaXMudGJEYXRhJywgSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLnRiRGF0YSkpKQ0KDQogICAgICB0aGlzLmZpbHRlckRhdGEoKSovDQogICAgfSwNCiAgICBoYW5kbGVDbG9zZSgpIHsNCiAgICAgIHRoaXMuJGVtaXQoJ2Nsb3NlJykNCiAgICB9LA0KICAgIC8vIGFjdGl2ZUNlbGxNZXRob2QoeyByb3csIGNvbHVtbiwgY29sdW1uSW5kZXggfSkgew0KICAgIC8vICAgcmV0dXJuIGNvbHVtbi5maWVsZCA9PT0gJ1NjaGR1bGluZ19Db3VudCcNCiAgICAvLyB9LA0KICAgIGFzeW5jIGdldFRhYmxlQ29uZmlnKGNvZGUpIHsNCiAgICAgIGF3YWl0IEdldEdyaWRCeUNvZGUoew0KICAgICAgICBjb2RlDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgY29uc3QgeyBJc1N1Y2NlZWQsIERhdGEsIE1lc3NhZ2UgfSA9IHJlcw0KICAgICAgICBpZiAoSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy50YkNvbmZpZyA9IE9iamVjdC5hc3NpZ24oe30sIHRoaXMudGJDb25maWcsIERhdGEuR3JpZCkNCiAgICAgICAgICB0aGlzLnBhZ2VJbmZvLnBhZ2VTaXplID0gTnVtYmVyKHRoaXMudGJDb25maWcuUm93X051bWJlcikNCiAgICAgICAgICBjb25zdCBsaXN0ID0gRGF0YS5Db2x1bW5MaXN0IHx8IFtdDQogICAgICAgICAgdGhpcy5jb2x1bW5zID0gbGlzdC5maWx0ZXIodiA9PiB2LklzX0Rpc3BsYXkpDQogICAgICAgICAgICAubWFwKGl0ZW0gPT4gew0KICAgICAgICAgICAgICBpZiAoaXRlbS5Jc19Gcm96ZW4pIHsNCiAgICAgICAgICAgICAgICBpdGVtLmZpeGVkID0gJ2xlZnQnDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICAgIH0pDQogICAgICAgICAgLy8gdGhpcy5jb2x1bW5zLnB1c2goew0KICAgICAgICAgIC8vICAgRGlzcGxheV9OYW1lOiAn5o6S5Lqn5pWw6YePJywNCiAgICAgICAgICAvLyAgIENvZGU6ICdTY2hkdWxpbmdfQ291bnQnDQogICAgICAgICAgLy8gfSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IE1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGdldE9iamVjdFR5cGVMaXN0KCkgew0KICAgICAgR2V0Q29tcFR5cGVUcmVlKHsgcHJvZmVzc2lvbmFsOiAnU3RlZWwnIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIC8vIHRoaXMuT2JqZWN0VHlwZUxpc3QuZGF0YSA9IHJlcy5EYXRhDQogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKF8pID0+IHsNCiAgICAgICAgICAgIHRoaXMuJHJlZnMudHJlZVNlbGVjdE9iamVjdFR5cGUudHJlZURhdGFVcGRhdGVGdW4ocmVzLkRhdGEpDQogICAgICAgICAgfSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsDQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVSZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybS5UeXBlX05hbWUgPSAnJw0KICAgICAgdGhpcy5mb3JtLkNvbXBfQ29kZSA9ICcnDQogICAgICB0aGlzLmZvcm0uQ29tcF9Db2RlQmx1ciA9ICcnDQogICAgICB0aGlzLmZvcm0uVHlwZSA9ICcnDQogICAgICB0aGlzLmZvcm0uU3BlYyA9ICcnDQogICAgICB0aGlzLmZvcm0uSW5zdGFsbFVuaXRfSWQgPSBbXQ0KICAgICAgdGhpcy5mb3JtLlBhcnRfQ29kZUJsdXIgPSAnJw0KICAgICAgdGhpcy5mb3JtLlBhcnRfQ29kZSA9ICcnDQogICAgICB0aGlzLmZvcm0uVW5pdF9QYXJ0X0NvZGVCbHVyID0gJycNCiAgICAgIHRoaXMuZm9ybS5Vbml0X1BhcnRfQ29kZSA9ICcnDQogICAgICB0aGlzLnNlYXJjaENvbnRlbnQgPSAnJw0KICAgICAgdGhpcy5zZWFyY2hQYXJ0Q29udGVudCA9ICcnDQogICAgICB0aGlzLnNlYXJjaFVuaXRQYXJ0Q29udGVudCA9ICcnDQogICAgICB0aGlzLmhhbmRsZVNlYXJjaCgpDQogICAgfSwNCiAgICBnZXRUeXBlKCkgew0KICAgICAgR2V0UGFydFR5cGVMaXN0KHsgUGFydF9HcmFkZTogMCB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy50eXBlT3B0aW9uID0gcmVzLkRhdGENCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBhZGRUb0xpc3QoKSB7DQogICAgICBpZiAoIXRoaXMudG90YWxTZWxlY3Rpb24ubGVuZ3RoKSByZXR1cm4NCiAgICAgIHRoaXMuaGFuZGxlU2F2ZSgxKQ0KICAgIH0sDQogICAgZ2V0SW5zdGFsbFVuaXRJZE5hbWVMaXN0KGlkKSB7DQogICAgICBpZiAoIXRoaXMuYXJlYUlkKSB7DQogICAgICAgIHRoaXMuaW5zdGFsbFVuaXRJZExpc3QgPSBbXQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgR2V0SW5zdGFsbFVuaXRJZE5hbWVMaXN0KHsgQXJlYV9JZDogdGhpcy5hcmVhSWQgfSkudGhlbihyZXMgPT4gew0KICAgICAgICAgIHRoaXMuaW5zdGFsbFVuaXRJZExpc3QgPSByZXMuRGF0YSB8fCBbXQ0KICAgICAgICAgIC8vIGlmICh0aGlzLmluc3RhbGxVbml0SWRMaXN0Lmxlbmd0aCkgew0KICAgICAgICAgIC8vICAgdGhpcy5mb3JtLkluc3RhbGxVbml0X0lkID0gW3RoaXMuaW5zdGFsbFVuaXRJZExpc3RbMF0uSWRdDQogICAgICAgICAgLy8gfQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["addDraft.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4UA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "addDraft.vue", "sourceRoot": "src/views/PRO/plan-production/schedule-production-new-part/components", "sourcesContent": ["<template>\r\n  <div class=\"contentBox\">\r\n    <div class=\"main-info\">\r\n      <div class=\"left\">\r\n        <ExpandableSection v-model=\"showExpand\" v-loading=\"tbLoading\" class=\"fff\" :width=\"300\">\r\n          <div class=\"inner-wrapper\">\r\n            <div class=\"tree-search\">\r\n              <el-select\r\n                v-model=\"statusType\"\r\n                clearable\r\n                class=\"search-select\"\r\n                placeholder=\"请选择\"\r\n              >\r\n                <el-option label=\"可排产\" value=\"可排产\" />\r\n                <el-option label=\"排产完成\" value=\"排产完成\" />\r\n                <el-option label=\"未导入\" value=\"未导入\" />\r\n              </el-select>\r\n              <el-input\r\n                v-model.trim=\"projectName\"\r\n                placeholder=\"搜索...\"\r\n                size=\"small\"\r\n                clearable\r\n                suffix-icon=\"el-icon-search\"\r\n              />\r\n            </div>\r\n            <el-divider class=\"cs-divider\" />\r\n            <div class=\"tree-x cs-scroll\">\r\n              <tree-detail\r\n                ref=\"tree\"\r\n                icon=\"icon-folder\"\r\n                is-custom-filter\r\n                :custom-filter-fun=\"customFilterFun\"\r\n                :loading=\"treeLoading\"\r\n                :tree-data=\"treeData\"\r\n                show-status\r\n                show-detail\r\n                :filter-text=\"filterText\"\r\n                :expanded-key=\"expandedKey\"\r\n                @handleNodeClick=\"handleNodeClick\"\r\n              >\r\n                <template #csLabel=\"{showStatus,data}\">\r\n                  <span v-if=\"!data.ParentNodes\" class=\"cs-blue\">({{ data.Code }})</span>{{ data.Label }}\r\n                  <template v-if=\"showStatus\">\r\n                    <span :class=\"['cs-tag',data.Data[statusCode]=='可排产' ? 'greenBg' : data.Data[statusCode]=='排产完成' ?'orangeBg':data.Data[statusCode]=='未导入'?'redBg':'']\">\r\n                      <i\r\n                        v-if=\"data.Data[statusCode]\"\r\n                        :class=\"[data.Data[statusCode]=='可排产' ? 'fourGreen' : data.Data[statusCode]=='排产完成' ?'fourOrange':data.Data[statusCode]=='未导入'?'fourRed':'']\"\r\n                      >\r\n                        {{ data.Data[statusCode] }}\r\n                      </i>\r\n\r\n                    </span>\r\n                  </template>\r\n                </template>\r\n\r\n              </tree-detail>\r\n            </div>\r\n          </div>\r\n        </ExpandableSection>\r\n      </div>\r\n      <div class=\"right\">\r\n\r\n        <el-form ref=\"form\" :model=\"form\" label-width=\"90px\">\r\n          <el-row>\r\n            <template >\r\n              <el-col :span=\"12\">\r\n                <!--                <el-form-item label-width=\"70px\" label=\"零件名称\" prop=\"Part_Code\">\r\n                  <div class=\"cs-input-x\">\r\n                    <el-input\r\n                      v-model=\"form.Part_Code\"\r\n                      placeholder=\"请输入(空格区分/多个搜索)\"\r\n                      clearable\r\n                      class=\"w100\"\r\n                    />\r\n                    <el-input\r\n                      v-model=\"form.Part_CodeBlur\"\r\n                      clearable\r\n                      class=\"w100\"\r\n                      style=\"margin-left: 10px;\"\r\n                      placeholder=\"模糊查找(请输入关键字)\"\r\n                      type=\"text\"\r\n                    />\r\n                  </div>\r\n                </el-form-item>\r\n             -->\r\n                <el-form-item prop=\"searchContent\" :label=\"`${comName}名称`\">\r\n                  <el-input\r\n                    v-model=\"searchContent\"\r\n                    clearable\r\n                    class=\"input-with-select w100\"\r\n                    placeholder=\"请输入(空格区分/多个搜索)\"\r\n                    size=\"small\"\r\n                  >\r\n                    <el-select\r\n                      slot=\"prepend\"\r\n                      v-model=\"curSearch\"\r\n                      placeholder=\"请选择\"\r\n                      style=\"width: 100px\"\r\n                    >\r\n                      <el-option label=\"精准查询\" :value=\"1\" />\r\n                      <el-option label=\"模糊查询\" :value=\"0\" />\r\n                    </el-select>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item prop=\"searchUnitPartContent\" label=\"部件名称\">\r\n                  <el-input\r\n                    v-model=\"searchUnitPartContent\"\r\n                    clearable\r\n                    class=\"input-with-select w100\"\r\n                    placeholder=\"请输入(空格区分/多个搜索)\"\r\n                    size=\"small\"\r\n                  >\r\n                    <el-select\r\n                      slot=\"prepend\"\r\n                      v-model=\"curUnitPartSearch\"\r\n                      placeholder=\"请选择\"\r\n                      style=\"width: 100px\"\r\n                    >\r\n                      <el-option label=\"精准查询\" :value=\"1\" />\r\n                      <el-option label=\"模糊查询\" :value=\"0\" />\r\n                    </el-select>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item prop=\"searchPartContent\" :label=\"`${partName}名称`\">\r\n                  <el-input\r\n                    v-model=\"searchPartContent\"\r\n                    clearable\r\n                    class=\"input-with-select w100\"\r\n                    placeholder=\"请输入(空格区分/多个搜索)\"\r\n                    size=\"small\"\r\n                  >\r\n                    <el-select\r\n                      slot=\"prepend\"\r\n                      v-model=\"curPartSearch\"\r\n                      placeholder=\"请选择\"\r\n                      style=\"width: 100px\"\r\n                    >\r\n                      <el-option label=\"精准查询\" :value=\"1\" />\r\n                      <el-option label=\"模糊查询\" :value=\"0\" />\r\n                    </el-select>\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"5\">\r\n                <el-form-item label=\"规格\" prop=\"Spec\">\r\n                  <el-input\r\n                    v-model=\"form.Spec\"\r\n                    placeholder=\"请输入\"\r\n                    clearable\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"5\">\r\n                <el-form-item :label=\"`${partName}种类`\" prop=\"Type_Name\">\r\n                  <el-select\r\n                    v-model=\"form.Type_Name\"\r\n                    placeholder=\"请选择\"\r\n                    clearable\r\n                  >\r\n                    <el-option\r\n                      v-for=\"item in typeOption\"\r\n                      :key=\"item.Code\"\r\n                      :label=\"item.Name\"\r\n                      :value=\"item.Name\"\r\n                    />\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n            </template>\r\n            <el-col :span=\"5\">\r\n              <el-form-item label=\"批次\" label-width=\"50px\" prop=\"Create_UserName\">\r\n                <el-select\r\n                  v-model=\"form.InstallUnit_Id\"\r\n                  filterable\r\n                  clearable\r\n                  multiple\r\n                  style=\"width: 100%\"\r\n                  placeholder=\"请选择\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in installUnitIdList\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Name\"\r\n                    :value=\"item.Id\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"9\">\r\n              <el-form-item label-width=\"0\">\r\n                <el-button style=\"margin-left: 10px\" @click=\"handleReset\">重置</el-button>\r\n                <el-button style=\"margin-left: 10px\" type=\"primary\" @click=\"handleSearch()\">查询</el-button>\r\n                <el-button :loading=\"addLoading\" style=\"margin-left: 10px\" type=\"primary\" @click=\"addToList()\">加入列表</el-button>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n          </el-row>\r\n        </el-form>\r\n\r\n        <div class=\"tb-wrapper\">\r\n          <vxe-table\r\n            ref=\"xTable1\"\r\n            :empty-render=\"{name: 'NotData'}\"\r\n            show-header-overflow\r\n            empty-text=\"暂无数据\"\r\n            height=\"auto\"\r\n            show-overflow\r\n            :checkbox-config=\"{checkField: 'checked', checkMethod: checkCheckboxMethod}\"\r\n            :loading=\"tbLoading\"\r\n            :row-config=\"{isCurrent: true, isHover: true }\"\r\n            class=\"cs-vxe-table\"\r\n            align=\"left\"\r\n            stripe\r\n            :data=\"fTable\"\r\n            resizable\r\n            :edit-config=\"{trigger: 'click', mode: 'cell'}\"\r\n            :tooltip-config=\"{ enterable: true }\"\r\n            @checkbox-all=\"tbSelectChange\"\r\n            @checkbox-change=\"tbSelectChange\"\r\n          >\r\n            <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n            <template v-for=\"item in columns\">\r\n              <vxe-column\r\n                v-if=\"item.Code === 'Is_Component'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <el-tag :type=\"row.Is_Component ? 'danger' : 'success'\">{{\r\n                    row.Is_Component ? \"否\" : \"是\"\r\n                  }}</el-tag>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"['Part_Code','Comp_Code'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                  <el-tag v-if=\"row.Is_Change\" style=\"margin-right: 8px;\" type=\"danger\">变</el-tag>\r\n                  {{ row[item.Code] | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"['Can_Schduling_Count'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <span v-if=\"showSc\">{{ row.csCount||'' }}</span>\r\n                  <span v-else>{{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"['Can_Schduling_Weight'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <span v-if=\"showSc\">{{ row.csCountWeight||'' }}</span>\r\n                  <span v-else>{{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                show-overflow=\"tooltip\"\r\n                sortable\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                :min-width=\"item.Width\"\r\n              />\r\n            </template>\r\n          </vxe-table>\r\n        </div>\r\n        <div class=\"data-info\">\r\n          <el-tag\r\n            size=\"medium\"\r\n            class=\"info-x\"\r\n          >已选 {{ totalSelection.length }} 条数据\r\n          </el-tag>\r\n          <vxe-pager\r\n            border\r\n            background\r\n            :loading=\"tbLoading\"\r\n            :current-page.sync=\"pageInfo.page\"\r\n            :page-size.sync=\"pageInfo.pageSize\"\r\n            :page-sizes=\"pageInfo.pageSizes\"\r\n            :total=\"pageInfo.total\"\r\n            :layouts=\"['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']\"\r\n            size=\"small\"\r\n            @page-change=\"handlePageChange\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"button\">\r\n      <el-button @click=\"handleClose\">取消</el-button>\r\n      <el-button\r\n        type=\"primary\"\r\n        :disabled=\"!totalSelection.length\"\r\n        :loading=\"saveLoading\"\r\n        @click=\"handleSave(2)\"\r\n      >保存</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { GetCanSchdulingComps } from '@/api/PRO/production-task'\r\nimport { GetCanSchdulingParts, GetPartList } from '@/api/PRO/production-part'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { debounce, deepClone } from '@/utils'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport { GetCompTypeTree } from '@/api/PRO/professionalType'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\nimport TreeDetail from '@/components/TreeDetail/index.vue'\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport { GetInstallUnitIdNameList, GetProjectAreaTreeList } from '@/api/PRO/project'\r\nimport { getUnique } from '../constant'\r\nimport { mapGetters } from 'vuex'\r\nimport { findAllParentNode } from '@/utils/tree'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nconst SPLIT_SYMBOL = '$_$'\r\n\r\nexport default {\r\n  components: { ExpandableSection, TreeDetail },\r\n  props: {\r\n    scheduleId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    pageType: {\r\n      type: String,\r\n      default: 'com'\r\n    },\r\n    showDialog: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n\r\n    installId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    currentIds: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n\r\n    isPartPrepare: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    comName: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    partName: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      pageInfo: {\r\n        page: 1,\r\n        pageSize: 500,\r\n        pageSizes: tablePageSize,\r\n        total: 0\r\n      },\r\n      form: {\r\n        Comp_Code: '',\r\n        Comp_CodeBlur: '',\r\n        Part_CodeBlur: '',\r\n        Part_Code: '',\r\n        Unit_Part_CodeBlur: '',\r\n        Unit_Part_Code: '',\r\n        Type_Name: '',\r\n        InstallUnit_Id: [],\r\n        Spec: '',\r\n        Type: ''\r\n      },\r\n      curSearch: 1,\r\n      curPartSearch: 1,\r\n      curUnitPartSearch: 1,\r\n      showExpand: true,\r\n      searchContent: '',\r\n      searchUnitPartContent: '',\r\n      searchPartContent: '',\r\n      statusType: '',\r\n      projectName: '',\r\n      expandedKey: '',\r\n      statusCode: 'Part_Schdule_Status',\r\n      isOwnerNull: true,\r\n      tbLoading: false,\r\n      treeLoading: false,\r\n      addLoading: false,\r\n      saveLoading: false,\r\n      showSc: false,\r\n      installUnitIdList: [],\r\n      columns: [],\r\n      fTable: [],\r\n      tbConfig: {},\r\n      TotalCount: 0,\r\n      Page: 0,\r\n      totalSelection: [],\r\n      treeData: [],\r\n      search: () => ({}),\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        clearable: true\r\n      },\r\n      ObjectTypeList: {\r\n        // 构件类型\r\n        'check-strictly': true,\r\n        'default-expand-all': true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      },\r\n      areaId: '',\r\n      typeOption: []\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    },\r\n    filterText() {\r\n      return this.projectName + SPLIT_SYMBOL + this.statusType\r\n    },\r\n    ...mapGetters('schedule', ['addTbKeys'])\r\n  },\r\n  watch: {\r\n    showDialog(newValue) {\r\n      newValue && (this.saveLoading = false)\r\n    }\r\n  },\r\n  mounted() {\r\n\r\n  },\r\n  methods: {\r\n    initData() {\r\n      console.log('initData')\r\n      this.tbData = []\r\n      this.getConfig()\r\n      this.fetchTreeData()\r\n      if (this.isCom) {\r\n        this.getObjectTypeList()\r\n      } else {\r\n        this.getType()\r\n      }\r\n      this.search = debounce(this.fetchData, 800, true)\r\n      this.setPageData()\r\n    },\r\n    handleNodeClick(data) {\r\n      if (this.areaId === data.Id) {\r\n        return\r\n      }\r\n      if (!data.ParentNodes || data.Children?.length > 0) {\r\n        return\r\n      }\r\n      if (data?.Data[this.statusCode] === '未导入') {\r\n        this.$message({\r\n          message: '清单未导入，请联系深化人员导入清单',\r\n          type: 'warning'\r\n        })\r\n        this.expandedKey = data.Id\r\n        return\r\n      }\r\n\r\n      const setData = ({ Data }) => {\r\n        this.areaId = Data.Id\r\n        this.projectId = Data.Project_Id\r\n        this.expandedKey = this.areaId\r\n\r\n        const _arr = findAllParentNode(this.treeData, data.Id, true)\r\n        this.nodeLabels = _arr.filter(v => !!v.ParentNodes).map(p => p.Label)\r\n\r\n        // this.formInline.Finish_Date = ''\r\n        // this.formInline.InstallUnit_Id = ''\r\n        // this.formInline.Remark = ''\r\n        this.fetchData()\r\n        // this.getAreaInfo()\r\n        this.getInstallUnitIdNameList()\r\n      }\r\n\r\n      setData(data)\r\n    },\r\n    fetchTreeData() {\r\n      this.treeLoading = true\r\n      GetProjectAreaTreeList({ MenuId: this.$route.meta.Id, projectName: this.projectName, type: this.isCom ? 1 : 2 }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          if (res.Data.length === 0) {\r\n            this.treeData = []\r\n            this.treeLoading = false\r\n            return\r\n          }\r\n          const resData = res.Data.map(item => {\r\n            item.Is_Directory = true\r\n            return item\r\n          })\r\n          this.treeData = resData\r\n          console.log('setKey')\r\n          this.setKey()\r\n          this.treeLoading = false\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.treeData = []\r\n          this.treeLoading = false\r\n        }\r\n      }).catch(() => {\r\n        this.treeLoading = false\r\n        this.treeData = []\r\n      })\r\n    },\r\n    setKey() {\r\n      const deepFilter = (tree) => {\r\n        console.log('tree', tree)\r\n        for (let i = 0; i < tree.length; i++) {\r\n          const item = tree[i]\r\n          const { Data, Children } = item\r\n          console.log(Data)\r\n          if (Data.ParentId && !Children?.length) {\r\n            this.handleNodeClick(item)\r\n            return\r\n          } else {\r\n            if (Children && Children?.length > 0) {\r\n              return deepFilter(Children)\r\n            }\r\n          }\r\n        }\r\n      }\r\n      return deepFilter(this.treeData)\r\n    },\r\n    customFilterFun(value, data, node) {\r\n      const arr = value.split(SPLIT_SYMBOL)\r\n      const labelVal = arr[0]\r\n      const statusVal = arr[1]\r\n      if (!value) return true\r\n      let parentNode = node.parent\r\n      let labels = [node.label]\r\n      let status = [data.Data[this.statusCode]]\r\n      let level = 1\r\n      while (level < node.level) {\r\n        labels = [...labels, parentNode.label]\r\n        status = [...status, data.Data[this.statusCode]]\r\n        parentNode = parentNode.parent\r\n        level++\r\n      }\r\n      labels = labels.filter(v => !!v)\r\n      status = status.filter(v => !!v)\r\n      let resultLabel = true\r\n      let resultStatus = true\r\n      if (this.statusType) {\r\n        resultStatus = status.some(s => s.indexOf(statusVal) !== -1)\r\n      }\r\n      if (this.projectName) {\r\n        resultLabel = labels.some(s => s.indexOf(labelVal) !== -1)\r\n      }\r\n      return resultLabel && resultStatus\r\n    },\r\n    async getConfig() {\r\n      let code = ''\r\n      code = this.isCom\r\n        ? 'PROComDraftEditTbConfig'\r\n        : 'PROPartDraftEditTbConfig_new'\r\n      await this.getTableConfig(code)\r\n      // this.fetchData()\r\n    },\r\n    filterData(page) {\r\n      console.log(22)\r\n      const splitAndClean = (input) => input.trim().replace(/\\s+/g, ' ').split(' ')\r\n\r\n      if (this.curSearch === 1) {\r\n        this.form.Comp_Code = this.searchContent\r\n        this.form.Comp_CodeBlur = ''\r\n      }\r\n      if (this.curSearch === 0) {\r\n        this.form.Comp_CodeBlur = this.searchContent\r\n        this.form.Comp_Code = ''\r\n      }\r\n      if (this.curPartSearch === 1) {\r\n        this.form.Part_CodeBlur = ''\r\n        this.form.Part_Code = this.searchPartContent\r\n      }\r\n      if (this.curPartSearch === 0) {\r\n        this.form.Part_Code = ''\r\n        this.form.Part_CodeBlur = this.searchPartContent\r\n      }\r\n      if (this.curUnitPartSearch === 1) {\r\n        this.form.Unit_Part_CodeBlur = ''\r\n        this.form.Unit_Part_Code = this.searchUnitPartContent\r\n      }\r\n      if (this.curUnitPartSearch === 0) {\r\n        this.form.Unit_Part_Code = ''\r\n        this.form.Unit_Part_CodeBlur = this.searchUnitPartContent\r\n      }\r\n\r\n      const f = []\r\n      for (const formKey in this.form) {\r\n        if (this.form[formKey] || this.form[formKey] === false) {\r\n          f.push(formKey)\r\n        }\r\n      }\r\n      if (!f.length) {\r\n        this.setPage()\r\n        !page && (this.pageInfo.page = 1)\r\n        this.pageInfo.total = this.tbData.length\r\n        return\r\n      }\r\n\r\n      const checkMatch = (origin, comp) => {\r\n        const _comp = comp.map(code => {\r\n          const [key, value] = code.split('&&&')\r\n          return key\r\n        })\r\n        const _origin = origin.map(code => {\r\n          const [key, value] = code.split('&&&')\r\n          return key\r\n        })\r\n        return _origin.some(item => {\r\n          return _comp.some(value => item.includes(value))\r\n        })\r\n      }\r\n      const checkExactMatch = (origin, comp) => {\r\n        const _comp = comp.map(code => {\r\n          const [key, value] = code.split('&&&')\r\n          return key\r\n        })\r\n        const _origin = origin.map(code => {\r\n          const [key, value] = code.split('&&&')\r\n          return key\r\n        })\r\n\r\n        return _origin.some(item => _comp.includes(item))\r\n      }\r\n\r\n      const temTbData = this.tbData.filter(v => {\r\n        v.checked = false\r\n        const compCode = v.Component_Codes || []\r\n\r\n        if (this.form.Comp_Code.trim()) {\r\n          const compCodeArray = splitAndClean(this.form.Comp_Code)\r\n          if (compCodeArray.length) {\r\n            const flag = checkExactMatch(compCode, compCodeArray)\r\n            console.log(887, compCode, compCodeArray, flag)\r\n            if (!flag) return false\r\n          }\r\n        }\r\n\r\n        if (this.form.Comp_CodeBlur.trim()) {\r\n          const compCodeArray = splitAndClean(this.form.Comp_CodeBlur)\r\n          if (compCodeArray.length) {\r\n            const flag = checkMatch(compCode, compCodeArray)\r\n            if (!flag) return false\r\n          }\r\n        }\r\n\r\n        const cName = (v['Component_Code'] || '').split(',')\r\n        console.log('cName', cName)\r\n        if (this.form.Unit_Part_CodeBlur.trim()) {\r\n          const unitPartCodeBlurArray = splitAndClean(this.form.Unit_Part_CodeBlur)\r\n          console.log('unitPartCodeBlurArray', unitPartCodeBlurArray)\r\n          if (v.Belong_To_Component || !unitPartCodeBlurArray.some(code =>\r\n            cName.some(name => name.includes(code))\r\n          )) {\r\n            return false\r\n          }\r\n        }\r\n\r\n        if (this.form.Unit_Part_Code.trim()) {\r\n          const unitPartCodeArray = splitAndClean(this.form.Unit_Part_Code)\r\n          if (v.Belong_To_Component || !unitPartCodeArray.some(code => cName.includes(code))) {\r\n            return false\r\n          }\r\n        }\r\n\r\n        if (this.form.Type && v.Type !== this.form.Type) {\r\n          return false\r\n        }\r\n\r\n        if (this.form.Part_CodeBlur.trim()) {\r\n          const partCodeBlurArray = splitAndClean(this.form.Part_CodeBlur)\r\n          if (!partCodeBlurArray.some(code => v['Part_Code'].includes(code))) {\r\n            return false\r\n          }\r\n        }\r\n\r\n        if (this.form.Part_Code.trim()) {\r\n          const partCodeArray = splitAndClean(this.form.Part_Code)\r\n          if (!partCodeArray.includes(v['Part_Code'])) {\r\n            return false\r\n          }\r\n        }\r\n\r\n        if (this.form.InstallUnit_Id.length && !this.form.InstallUnit_Id.includes(v.InstallUnit_Id)) {\r\n          return false\r\n        }\r\n\r\n        if (this.form.Type_Name !== '' && v.Type_Name !== this.form.Type_Name) {\r\n          return false\r\n        }\r\n\r\n        if (this.form.Spec.trim() !== '') {\r\n          const specArray = splitAndClean(this.form.Spec)\r\n          if (!specArray.some(spec => v.Spec.includes(spec))) {\r\n            return false\r\n          }\r\n        }\r\n        if (this.searchContent.trim().length) {\r\n          let csCount = 0\r\n\r\n          v.componentMap = (v.Component_Codes || []).reduce((acc, code) => {\r\n            const [key, value] = code.split('&&&')\r\n            acc[key] = parseInt(value)\r\n            if (this.curSearch === 1) {\r\n              const compCodeArray = splitAndClean(this.form.Comp_Code)\r\n              if (compCodeArray.length) {\r\n                const flag = checkExactMatch([key], compCodeArray)\r\n                if (flag) {\r\n                  csCount += parseInt(value)\r\n                }\r\n              }\r\n            } else {\r\n              const compCodeArray = splitAndClean(this.form.Comp_CodeBlur)\r\n              if (compCodeArray.length) {\r\n                const flag = checkMatch([key], compCodeArray)\r\n                console.log('pflag', key, compCodeArray, flag, value)\r\n                if (flag) {\r\n                  csCount += parseInt(value)\r\n                }\r\n              }\r\n            }\r\n            return acc\r\n          }, {})\r\n          this.$set(v, 'csCount', Math.min(csCount, v.Can_Schduling_Count))\r\n          this.$set(v, 'csCountWeight', Math.min(v.Can_Schduling_Weight, v.csCount * v.Weight))\r\n\r\n          v.searchcount = v.count\r\n          v.searchcountMax = v.maxCount\r\n          // const cs = v.Component_Codes || []\r\n          // let min = 0\r\n          // cs.forEach((element, idx) => {\r\n          //   const [key, value] = element.split('&&&')\r\n          //   min = v.componentMap[key]\r\n          // })\r\n\r\n          v.count = v.csCount\r\n        } else {\r\n          v.count = v.Can_Schduling_Count\r\n        }\r\n\r\n        // v.Can_Schduling_Count = v.csCount\r\n        // v.Can_Schduling_Weight = v.csCountWeight\r\n\r\n        return true\r\n      })\r\n\r\n      !page && (this.pageInfo.page = 1)\r\n      this.pageInfo.total = temTbData.length\r\n      this.setPage(temTbData)\r\n      if (this.searchContent.trim().length) {\r\n        this.showSc = true\r\n      }\r\n    },\r\n    handleSearch() {\r\n      this.totalSelection = []\r\n      this.clearSelect()\r\n      if (this.tbData?.length) {\r\n        this.tbData.forEach(item => item.checked = false)\r\n        this.filterData()\r\n      }\r\n      this.showSc = !!this.searchContent.trim().length\r\n    },\r\n    tbSelectChange(array) {\r\n      this.totalSelection = this.tbData.filter(v => v.checked)\r\n    },\r\n    clearSelect() {\r\n      this.$refs.xTable1.clearCheckboxRow()\r\n      this.totalSelection = []\r\n    },\r\n    async fetchData() {\r\n      this.handleReset()\r\n      this.tbLoading = true\r\n      if (this.isCom) {\r\n        await this.getComTbData()\r\n      } else {\r\n        await this.getPartTbData()\r\n      }\r\n      this.initTbData()\r\n      this.filterData()\r\n      this.tbLoading = false\r\n    },\r\n    setPageData() {\r\n      if (this.tbData?.length) {\r\n        this.pageInfo.page = 1\r\n        this.tbData = this.tbData.filter(v => v.Can_Schduling_Count > 0)\r\n        this.filterData()\r\n      }\r\n    },\r\n    handleSave(type = 2) {\r\n      if (type === 1) {\r\n        this.addLoading = true\r\n      } else {\r\n        this.saveLoading = true\r\n      }\r\n      setTimeout(() => {\r\n        this.totalSelection.forEach((item) => {\r\n          const intCount = parseInt(item.count)\r\n          if (this.searchContent.trim().length) {\r\n            item.Schduled_Count = item.Can_Schduling_Count\r\n\r\n            item.maxCount = item.Can_Schduling_Count\r\n            item.chooseCount = intCount\r\n            item.count = item.Can_Schduling_Count\r\n\r\n            item.Can_Schduling_Count = 0\r\n            item.Can_Schduling_Weight = item.Can_Schduling_Count * item.Weight\r\n          } else {\r\n            item.Schduled_Count += intCount\r\n            item.Can_Schduling_Count -= intCount\r\n            item.Can_Schduling_Weight = item.Can_Schduling_Count * item.Weight\r\n            item.maxCount = intCount\r\n            item.chooseCount = intCount\r\n            item.count = item.Can_Schduling_Count\r\n          }\r\n\r\n          item.checked = false\r\n        })\r\n        const cp = deepClone(this.totalSelection)\r\n\r\n        // this.$emit('sendSelectList', cp)\r\n        this.addLoading = false\r\n        this.clearSelect()\r\n        // this.setPage()\r\n        this.setPageData()\r\n        if (type === 2) {\r\n          this.$emit('sendSelectList', cp)\r\n          this.$emit('close')\r\n          this.fTable = []\r\n          this.tbData = []\r\n        } else {\r\n          this.$emit('addToTbList', cp)\r\n        }\r\n      }, 0)\r\n    },\r\n    initTbData() {\r\n      // 设置文本框选择的排产数量,设置自定义唯一码\r\n      const objKey = {}\r\n      if (!this.tbData?.length) {\r\n        this.tbData = []\r\n        // this.backendTb = []\r\n        return\r\n      }\r\n      console.log(998, JSON.parse(JSON.stringify(this.tbData)))\r\n      // this.backendTb = deepClone(this.tbData)\r\n      this.tbData = this.tbData.filter(item => {\r\n        this.$set(item, 'count', item.Can_Schduling_Count)\r\n        this.$set(item, 'maxCount', item.Can_Schduling_Count)\r\n        item.uuid = getUnique(this.isCom, item)\r\n        objKey[item.Type] = true\r\n        // let csCount = 0\r\n        // item.componentMap = (item.Component_Codes || []).reduce((acc, code) => {\r\n        //   const [key, value] = code.split('&&&')\r\n        //   acc[key] = parseInt(value)\r\n        //   csCount += parseInt(value)\r\n        //   return acc\r\n        // }, {})\r\n        // this.$set(item, 'csCount', csCount)\r\n        // Object.keys(item.componentMap).forEach(key => {\r\n        //   this.$set(item, key, item.componentMap[key])\r\n        // })\r\n\r\n        return !this.addTbKeys.includes(item.uuid)\r\n      })\r\n      //   .map((item) => {\r\n      //   this.$set(item, 'count', item.Can_Schduling_Count)\r\n      //   this.$set(item, 'maxCount', item.Can_Schduling_Count)\r\n      //   // item.uuid = uuidv4()\r\n      //   item.uuid = item.InstallUnit_Id + item.Part_Aggregate_Id\r\n      //   objKey[item.Type] = true\r\n      //\r\n      //   const _selectList = this.selectTbData.filter(v => v.puuid)\r\n      //   console.log('_selectList', _selectList)\r\n      //   // _selectList.forEach((element, idx) => {\r\n      //   //   if(element.puuid === item.uuid){\r\n      //   //\r\n      //   //   }\r\n      //   // })\r\n      //   return item\r\n      // })\r\n\r\n      // this.backendTb = deepClone(this.tbData)\r\n    },\r\n    async getComTbData() {\r\n      // const { install, areaId } = this.$route.query\r\n      const { Comp_Codes, ...obj } = this.form\r\n      let codes = []\r\n      if (Object.prototype.toString.call(Comp_Codes) === '[object String]') {\r\n        codes = Comp_Codes && Comp_Codes.split(' ').filter(v => !!v)\r\n      }\r\n      await GetCanSchdulingComps({\r\n        Ids: this.currentIds,\r\n        ...obj,\r\n        Schduling_Plan_Id: this.scheduleId,\r\n        Comp_Codes: codes,\r\n        InstallUnit_Id: this.installId,\r\n        Area_Id: this.areaId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.pageInfo.total = res.Data.length\r\n          this.tbData = res.Data.map((v, idx) => {\r\n            // 已排产赋值\r\n            v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n            v.Workshop_Id = v.Scheduled_Workshop_Id\r\n            v.Workshop_Name = v.Scheduled_Workshop_Name\r\n            v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n            // if (v.originalPath) {\r\n            // v.isDisabled = true\r\n            // }\r\n            v.checked = false\r\n            v.initRowIndex = idx\r\n            v.Area_Name = this.nodeLabels.join('/')\r\n\r\n            // v.technologyPathDisabled = !!v.Technology_Path\r\n            return v\r\n          })\r\n          this.setPage()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 分页\r\n     */\r\n    handlePageChange({ currentPage, pageSize }) {\r\n      if (this.tbLoading) return\r\n      this.pageInfo.page = currentPage\r\n      this.pageInfo.pageSize = pageSize\r\n      this.setPage()\r\n      this.filterData(currentPage)\r\n    },\r\n\r\n    setPage(tb = this.tbData) {\r\n      this.fTable = tb.slice((this.pageInfo.page - 1) * this.pageInfo.pageSize, this.pageInfo.page * this.pageInfo.pageSize)\r\n    },\r\n\r\n    async getPartTbData() {\r\n      // const { install, areaId } = this.$route.query\r\n      await GetCanSchdulingParts({\r\n        Ids: this.currentIds,\r\n        ...this.form,\r\n        Schduling_Plan_Id: this.scheduleId,\r\n        InstallUnit_Id: this.installId,\r\n        Area_Id: this.areaId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.pageInfo.total = res.Data.length\r\n          this.tbData = res.Data.map((v, idx) => {\r\n            v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n            v.Workshop_Id = v.Scheduled_Workshop_Id\r\n            v.Workshop_Name = v.Scheduled_Workshop_Name\r\n            if (v.Comp_Import_Detail_Id) {\r\n              v.Part_Used_Process = this.getPartUsedProcess(v)\r\n            }\r\n            v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n            // v.isDisabled = !!v.originalPath\r\n            v.checked = false\r\n            v.initRowIndex = idx\r\n            // v.partUsedProcessDisabled = this.isPartPrepare ? !!v.Part_Used_Process : false\r\n            // v.technologyPathDisabled = !!v.Technology_Path\r\n            if (!this.isPartPrepare) {\r\n              v.Temp_Part_Used_Process = v.Part_Used_Process\r\n            }\r\n            return v\r\n          })\r\n          this.setPartColumn()\r\n          this.setPage()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n\r\n      const submitObj = this.tbData.map(item => {\r\n        return {\r\n          Id: item.Part_Aggregate_Id,\r\n          Type: 1\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = !!item.Is_Stop\r\n          })\r\n          this.tbData.forEach(row => {\r\n            if (stopMap.hasOwnProperty(row.Part_Aggregate_Id)) {\r\n              this.$set(row, 'stopFlag', stopMap[row.Part_Aggregate_Id])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    checkCheckboxMethod({ row }) {\r\n      return !row.stopFlag\r\n    },\r\n    getPartUsedProcess(item) {\r\n      if (item.Scheduled_Used_Process) {\r\n        return item.Scheduled_Used_Process\r\n      }\r\n      if (item.Component_Technology_Path) {\r\n        const list = item.Component_Technology_Path.split('/')\r\n        if (list.includes(item.Part_Used_Process)) {\r\n          return item.Part_Used_Process\r\n        } else if (list.includes(item.Part_Type_Used_Process)) {\r\n          return item.Part_Type_Used_Process\r\n        }\r\n      } else {\r\n        if (item.Part_Used_Process) {\r\n          return item.Part_Used_Process\r\n        } else if (item.Part_Type_Used_Process) {\r\n          return item.Part_Type_Used_Process\r\n        }\r\n      }\r\n\r\n      return ''\r\n    },\r\n    setPartColumn() {\r\n      // 纯零件\r\n      this.isOwnerNull = this.tbData.every(v => !v.Comp_Import_Detail_Id)\r\n      console.log('this.isOwnerNull', this.isOwnerNull)\r\n      if (this.isOwnerNull) {\r\n        const idx = this.columns.findIndex(v => v.Code === 'Component_Code')\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n      }\r\n    },\r\n    mergeData(list) {\r\n      /*      console.log('list', list)\r\n      console.log('this.backendTb', this.backendTb)\r\n      list\r\n        .forEach((element, index) => {\r\n          const idx = this.backendTb.findIndex(\r\n            (item) => element.puuid && item.uuid === element.puuid\r\n          )\r\n          console.log('idx', idx, this.backendTb[idx])\r\n          console.log('index', index)\r\n          if (idx !== -1) {\r\n            this.tbData.splice(idx, 0, deepClone(this.backendTb[idx]))\r\n          }\r\n        })\r\n\r\n      this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n      console.log('this.tbData', JSON.parse(JSON.stringify(this.tbData)))\r\n\r\n      this.filterData()*/\r\n    },\r\n    handleClose() {\r\n      this.$emit('close')\r\n    },\r\n    // activeCellMethod({ row, column, columnIndex }) {\r\n    //   return column.field === 'Schduling_Count'\r\n    // },\r\n    async getTableConfig(code) {\r\n      await GetGridByCode({\r\n        code\r\n      }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n          this.pageInfo.pageSize = Number(this.tbConfig.Row_Number)\r\n          const list = Data.ColumnList || []\r\n          this.columns = list.filter(v => v.Is_Display)\r\n            .map(item => {\r\n              if (item.Is_Frozen) {\r\n                item.fixed = 'left'\r\n              }\r\n              return item\r\n            })\r\n          // this.columns.push({\r\n          //   Display_Name: '排产数量',\r\n          //   Code: 'Schduling_Count'\r\n          // })\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getObjectTypeList() {\r\n      GetCompTypeTree({ professional: 'Steel' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          // this.ObjectTypeList.data = res.Data\r\n          this.$nextTick((_) => {\r\n            this.$refs.treeSelectObjectType.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.form.Type_Name = ''\r\n      this.form.Comp_Code = ''\r\n      this.form.Comp_CodeBlur = ''\r\n      this.form.Type = ''\r\n      this.form.Spec = ''\r\n      this.form.InstallUnit_Id = []\r\n      this.form.Part_CodeBlur = ''\r\n      this.form.Part_Code = ''\r\n      this.form.Unit_Part_CodeBlur = ''\r\n      this.form.Unit_Part_Code = ''\r\n      this.searchContent = ''\r\n      this.searchPartContent = ''\r\n      this.searchUnitPartContent = ''\r\n      this.handleSearch()\r\n    },\r\n    getType() {\r\n      GetPartTypeList({ Part_Grade: 0 }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.typeOption = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    addToList() {\r\n      if (!this.totalSelection.length) return\r\n      this.handleSave(1)\r\n    },\r\n    getInstallUnitIdNameList(id) {\r\n      if (!this.areaId) {\r\n        this.installUnitIdList = []\r\n      } else {\r\n        GetInstallUnitIdNameList({ Area_Id: this.areaId }).then(res => {\r\n          this.installUnitIdList = res.Data || []\r\n          // if (this.installUnitIdList.length) {\r\n          //   this.form.InstallUnit_Id = [this.installUnitIdList[0].Id]\r\n          // }\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n@import \"~@/styles/mixin.scss\";\r\n.cs-divider{\r\n  margin:16px 0 0 0;\r\n}\r\n.contentBox {\r\n  height: 75vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .main-info{\r\n    display: flex;\r\n    overflow: hidden;\r\n    flex: 1;\r\n    .left{\r\n      height: 100%;\r\n      margin-right: 16px;\r\n      border: 1px solid #eee;\r\n      .cs-tag{\r\n        margin-left: 8px;\r\n        font-size: 12px;\r\n        padding:2px 4px;\r\n        border-radius: 1px;\r\n      }\r\n\r\n      .inner-wrapper {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n        padding: 16px;\r\n        border-radius: 4px;\r\n        overflow: hidden;\r\n\r\n        .tree-search {\r\n          display: flex;\r\n\r\n          .search-select {\r\n            margin-right: 8px;\r\n          }\r\n        }\r\n\r\n        .tree-x {\r\n          overflow: hidden;\r\n          margin-top: 16px;\r\n          flex: 1;\r\n\r\n          .el-tree {\r\n            height: 100%;\r\n          }\r\n        }\r\n\r\n        .cs-scroll {\r\n          overflow-y: auto;\r\n          @include scrollBar;\r\n        }\r\n\r\n      }\r\n    }\r\n    .right{\r\n      overflow: hidden;\r\n      flex: 1;\r\n      display: flex;\r\n      flex-direction: column;\r\n      border: 1px solid #eee;\r\n      padding:16px;\r\n    }\r\n\r\n  }\r\n\r\n  .button {\r\n    margin-top: 16px;\r\n    display: flex;\r\n    justify-content: end;\r\n  }\r\n\r\n  .tb-wrapper {\r\n    flex: 1 1 auto;\r\n  }\r\n\r\n  .data-info{\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-top: 16px;\r\n  }\r\n}\r\n.fourGreen {\r\n  color: #00C361;\r\n  font-style: normal;\r\n}\r\n\r\n.fourOrange {\r\n  color: #FF9400;\r\n  font-style: normal;\r\n}\r\n\r\n.fourRed {\r\n  color: #FF0000;\r\n  font-style: normal;\r\n}\r\n\r\n.cs-blue {\r\n  color: #5AC8FA;\r\n}\r\n\r\n.orangeBg{\r\n  background: rgba(255,148,0,0.1);\r\n}\r\n\r\n.redBg{\r\n  background: rgba(252,107,127,0.1);\r\n}\r\n.greenBg{\r\n  background: rgba(0, 195, 97, 0.10);\r\n}\r\n.cs-input-x{\r\n  display: flex;\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\components\\ExportCustomReport\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\components\\ExportCustomReport\\index.vue", "mtime": 1757468127968}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBFeHBvcnRSZXBvcnRCeVRlbXBsYXRlLCBHZXRSZXBvcnRUZW1wbGF0ZUxpc3QgfSBmcm9tICdAL2FwaS9zeXMvY3VzdG9tLXRlbXBsYXRlJw0KaW1wb3J0IHsgY29tYmluZVVSTCB9IGZyb20gJ0AvdXRpbHMnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ0V4cG9ydEN1c3RvbVJlcG9ydCcsDQogIHByb3BzOiB7DQogICAgLy8g5oyJ6ZKu5ZCN56ewDQogICAgbmFtZTogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJ+WvvOWHuuiHquWumuS5ieaKpeihqCcNCiAgICB9LA0KICAgIGNvZGU6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIHJlcXVpcmVkOiB0cnVlDQogICAgfSwNCiAgICAvLyDkuJrliqFpZA0KICAgIGlkczogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiBmdW5jdGlvbigpIHsNCiAgICAgICAgcmV0dXJuIFtdDQogICAgICB9DQogICAgfSwNCiAgICBqc29uRGF0YTogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJycNCiAgICB9DQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGxpc3Q6IFtdLA0KICAgICAgbG9hZGluZzogZmFsc2UNCiAgICB9DQogIH0sDQogIHdhdGNoOiB7DQogICAgY29kZTogew0KICAgICAgaGFuZGxlcigpIHsNCiAgICAgICAgdGhpcy5mZXRjaFRlbXBsYXRlcygpDQogICAgICB9LA0KICAgICAgaW1tZWRpYXRlOiB0cnVlDQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgZmV0Y2hUZW1wbGF0ZXMoKSB7DQogICAgICBHZXRSZXBvcnRUZW1wbGF0ZUxpc3Qoew0KICAgICAgICBUeXBlQ29kZTogdGhpcy5jb2RlDQogICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIHRoaXMubGlzdCA9IHJlcy5EYXRhDQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g5aSE55CG56m65qih5p2/5YiX6KGo55qE5oOF5Ya1DQogICAgaGFuZGxlRW1wdHlMaXN0KCkgew0KICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfmnKrphY3nva7oh6rlrprkuYnmqKHmnb/vvIzor7flhYjphY3nva7mqKHmnb8nKQ0KICAgIH0sDQogICAgLy8g5aSE55CG5Y2V5Liq5qih5p2/55qE5oOF5Ya1DQogICAgaGFuZGxlU2luZ2xlVGVtcGxhdGUoKSB7DQogICAgICBpZiAodGhpcy5saXN0Lmxlbmd0aCA9PT0gMSkgew0KICAgICAgICB0aGlzLmNvbW1hbmRNZW51KHRoaXMubGlzdFswXSkNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOWkhOeQhuWkmuS4quaooeadv+eahOaDheWGtQ0KICAgIGNvbW1hbmRNZW51KGl0ZW0pIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIEV4cG9ydFJlcG9ydEJ5VGVtcGxhdGUoew0KICAgICAgICBUeXBlQ29kZTogdGhpcy5jb2RlLA0KICAgICAgICBUZW1wbGF0ZUlkOiBpdGVtLklkLA0KICAgICAgICBKc29uRGF0YTogdGhpcy5qc29uRGF0YSwNCiAgICAgICAgU2VsZWN0SWRzOiB0aGlzLmlkcw0KICAgICAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHdpbmRvdy5vcGVuKGNvbWJpbmVVUkwodGhpcy4kYmFzZVVybCwgcmVzLkRhdGEpLCAnX2JsYW5rJykNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5NZXNzYWdlKQ0KICAgICAgICB9DQogICAgICB9KS5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/ExportCustomReport", "sourcesContent": ["<template>\r\n  <!-- 当模板列表为空时 -->\r\n  <el-button\r\n    v-if=\"list.length === 0\"\r\n    type=\"primary\"\r\n    :loading=\"loading\"\r\n    :disabled=\"!ids.length\"\r\n    @click=\"handleEmptyList\"\r\n  >\r\n    {{ name }}\r\n  </el-button>\r\n\r\n  <!-- 当模板列表只有一个时 -->\r\n  <el-button\r\n    v-else-if=\"list.length === 1\"\r\n    type=\"primary\"\r\n    :loading=\"loading\"\r\n    :disabled=\"!ids.length\"\r\n    @click=\"handleSingleTemplate\"\r\n  >\r\n    {{ name }}\r\n  </el-button>\r\n\r\n  <!-- 当模板列表有多个时 -->\r\n  <el-dropdown v-else trigger=\"click\" @command=\"commandMenu\">\r\n    <el-button type=\"primary\" :loading=\"loading\" :disabled=\"!ids.length\">\r\n      {{ name }}<i class=\"el-icon-arrow-down el-icon--right\" />\r\n    </el-button>\r\n    <el-dropdown-menu slot=\"dropdown\">\r\n      <el-dropdown-item v-for=\"item in list\" :key=\"item.Id\" :command=\"item\">{{ item.Template_Name }}</el-dropdown-item>\r\n    </el-dropdown-menu>\r\n  </el-dropdown>\r\n</template>\r\n<script>\r\nimport { ExportReportByTemplate, GetReportTemplateList } from '@/api/sys/custom-template'\r\nimport { combineURL } from '@/utils'\r\n\r\nexport default {\r\n  name: 'ExportCustomReport',\r\n  props: {\r\n    // 按钮名称\r\n    name: {\r\n      type: String,\r\n      default: '导出自定义报表'\r\n    },\r\n    code: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    // 业务id\r\n    ids: {\r\n      type: Array,\r\n      default: function() {\r\n        return []\r\n      }\r\n    },\r\n    jsonData: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      list: [],\r\n      loading: false\r\n    }\r\n  },\r\n  watch: {\r\n    code: {\r\n      handler() {\r\n        this.fetchTemplates()\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  methods: {\r\n    fetchTemplates() {\r\n      GetReportTemplateList({\r\n        TypeCode: this.code\r\n      }).then(res => {\r\n        this.list = res.Data\r\n      })\r\n    },\r\n    // 处理空模板列表的情况\r\n    handleEmptyList() {\r\n      this.$message.warning('未配置自定义模板，请先配置模板')\r\n    },\r\n    // 处理单个模板的情况\r\n    handleSingleTemplate() {\r\n      if (this.list.length === 1) {\r\n        this.commandMenu(this.list[0])\r\n      }\r\n    },\r\n    // 处理多个模板的情况\r\n    commandMenu(item) {\r\n      this.loading = true\r\n      ExportReportByTemplate({\r\n        TypeCode: this.code,\r\n        TemplateId: item.Id,\r\n        JsonData: this.jsonData,\r\n        SelectIds: this.ids\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}
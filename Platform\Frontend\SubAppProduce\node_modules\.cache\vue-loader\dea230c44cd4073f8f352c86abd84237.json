{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\component-list\\v4\\component\\BatchEditor.vue?vue&type=template&id=74c80ace&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\component-list\\v4\\component\\BatchEditor.vue", "mtime": 1758266753081}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}
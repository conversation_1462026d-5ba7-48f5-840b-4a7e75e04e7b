<template>
  <div v-loading="pageLoading" element-loading-text="加载中...">
    <el-form ref="form" :rules="rules" :model="form" label-width="130px">
      <el-row>
        <el-form-item label="检查项组合名称" prop="Group_Name">
          <el-input v-model="form.Group_Name" />
        </el-form-item>

        <el-form-item label="质检节点" prop="Check_Node_Id">
          <el-select
            v-model="form.Check_Node_Id"
            clearable
            style="width: 100%"
            placeholder="请选择质检节点"
            @change="changeNode"
          >
            <el-option
              v-for="(item, index) in CheckNodeList"
              :key="index"
              :label="item.Display_Name"
              :value="item.Id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="质检类型" prop="Check_Type">
          <el-select
            v-model="Change_Check_Type"
            clearable
            multiple
            :multiple-limit="1"
            style="width: 100%"
            :disabled="Isdisable"
            placeholder="请选择质检类型"
            @change="SelectType"
          >
            <el-option
              v-for="(item, index) in QualityTypeList"
              :key="index"
              :label="item.Name"
              :value="item.Id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="专业类别" prop="Pro_Category_Id">
          <el-select
            v-model="form.Pro_Category_Id"
            clearable
            style="width: 100%"
            placeholder="请选择专业类别"
            @change="changeCategory"
          >
            <el-option
              v-for="(item, index) in ProCategoryList"
              :key="index"
              :label="item.Name"
              :value="item.Id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="检查类型" prop="Questionlab_Ids">
          <el-select
            v-model="form.Questionlab_Ids"
            style="width: 100%"
            multiple
            placeholder="请选择检查类型"
            @change="ChangeCheckType"
            @remove-tag="removeCheckType"
          >
            <el-option
              v-for="(item, index) in CheckTypeList"
              :key="index"
              :label="item.Name"
              :value="item.Id"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          label="产品类型"
          prop="Object_Type_Ids"
        >
          <el-tree-select
            ref="treeSelectObjectType"
            v-model="form.Object_Type_Ids"
            :disabled="!Boolean(form.Pro_Category_Id)"
            class="cs-tree-x"
            :tree-params="ObjectTypeList"
            value-key="Id"
            @removeTag="removeTagFn"
          />
        </el-form-item>

        <el-col :span="24">
          <h3>检查项设置</h3>
          <el-form-item label="" prop="" class="checkItem">
            <el-table :data="ProcessFlow" border style="width: 100%">
              <el-table-column prop="" label="*检查类型" align="center">
                <template slot-scope="{ row }">
                  <el-select
                    v-model="row.Questionlab_Id"
                    style="width: 100%"
                    clearable
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="(item, index) in CheckTypeList"
                      :key="index"
                      :label="item.Name"
                      :value="item.Id"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="" label="*检查项内容" align="center">
                <template slot-scope="{ row, $index }">
                  <el-select
                    v-model="row.Check_Item_Id"
                    style="width: 100%"
                    clearable
                    placeholder="请选择"
                    @change="ChangeItem($event, $index, row)"
                  >
                    <el-option
                      v-for="(item, index) in CheckItemList"
                      :key="index"
                      :label="item.Check_Content"
                      :value="item.Id"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="" label="*合格标准" align="center">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.Eligibility_Criteria"
                    disabled
                  />
                </template>
              </el-table-column>
              <el-table-column
                prop="address"
                label="操作"
                width="140"
                align="center"
              >
                <template slot-scope="{ row, $index }">
                  <el-button
                    type="text"
                    icon="el-icon-top"
                    :disabled="$index == 0"
                    @click="moveUpward(row, $index)"
                  />
                  <el-button
                    type="text"
                    icon="el-icon-bottom"
                    :disabled="$index == ProcessFlow.length - 1"
                    @click="moveDown(row, $index)"
                  />
                  <el-button
                    type="text"
                    icon="el-icon-delete"
                    style="color: #f56c6c"
                    @click.native.prevent="deleteRow($index, ProcessFlow)"
                  />
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-button
            type="text"
            class="addcheckItem"
            @click="addTableData"
          >+ 新增检查项</el-button>
        </el-col>
        <el-col :span="24" style="text-align: right">
          <el-form-item style="text-align: right">
            <el-button @click="$emit('close')">关 闭</el-button>
            <el-button
              type="primary"
              @click="handleSubmit('form')"
            >确 定</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { AddCheckItemCombination } from '@/api/PRO/factorycheck'
import { EntityQualityList } from '@/api/PRO/factorycheck'

import { GetCheckTypeList } from '@/api/PRO/factorycheck'
import { GetCheckItemList } from '@/api/PRO/factorycheck'
import { GetNodeList } from '@/api/PRO/factorycheck'
import { GetCompTypeTree } from '@/api/PRO/factorycheck'
import {
  GetFactoryProfessionalByCode,
  GetMaterialType
} from '@/api/PRO/factorycheck'
import { GetPartTypeTree } from '@/api/PRO/partType'

export default {
  data() {
    return {
      mode: '', // 区分项目和工厂
      ProjectId: '', // 项目Id
      Check_Object_Id: '',
      checkType: {}, // 区分构件、零件、物料
      form: {
        Object_Type_Ids: []
      },
      rules: {
        Check_Content: [
          { required: true, message: '请填写完整表单', trigger: 'blur' }
        ],
        Eligibility_Criteria: [
          { required: true, message: '请填写完整表单', trigger: 'blur' }
        ],
        Group_Name: [
          { required: true, message: '请填写完整表单', trigger: 'blur' }
        ],
        Check_Type: [
          { required: true, message: '请填写完整表单', trigger: 'change' }
        ],
        Questionlab_Ids: [
          { required: true, message: '请填写完整表单', trigger: 'blur' }
        ]
      },
      title: '',
      options: [],
      ProcessFlow: [],
      CheckTypeList: [], // 检查类型下拉
      CheckItemList: [], // 检查项下拉
      Change_Check_Type: [],
      QualityTypeList: [
        {
          Name: '质量',
          Id: 1
        },
        {
          Name: '探伤',
          Id: 2
        }
      ], // 质检类型
      ProCategoryList: [], // 专业类别下拉
      CheckNodeList: [], // 质检节点下拉
      verification: false,
      ProCategoryCode: '', // 专业类别Code
      Eligibility_Criteria: '',
      ObjectTypeList: {
        // 对象类型
        'check-strictly': true,
        'default-expand-all': true,
        filterable: false,
        clickParent: true,
        data: [],
        props: {
          children: 'Children',
          label: 'Label',
          value: 'Id'
        }
      },
      Isdisable: false,
      typeCode: '',
      typeId: '',
      partGrade: '',
      pageLoading: false
    }
  },
  watch: {
    ProcessFlow: {
      handler(newName, oldName) {
        console.log(newName)
        this.form.Questionlab_Ids = []
        this.ProcessFlow.forEach((item) => {
          if (
            item.Questionlab_Id &&
            !this.form.Questionlab_Ids.includes(item.Questionlab_Id)
          ) {
            this.form.Questionlab_Ids.push(item.Questionlab_Id)
          }
        })
      },
      deep: true
    }
  },
  mounted() {},
  methods: {
    async init(title, checkType, data) {
      if (title === '编辑') {
        this.pageLoading = true
      }
      this.partGrade = checkType.Code
      this.Check_Object_Id = checkType.Id
      this.checkType = checkType
      this.title = title
      this.form.Check_Object_Id = checkType.Id
      this.form.Bom_Level = checkType.Code
      await this.getProfessionalType() // 专业类别
      await this.getCheckTypeList() // 检查类型
      await this.getCheckItemList()
      await this.getNodeList(data) // 质检节点
    },
    async addCheckItemCombination() {
      await AddCheckItemCombination({
        Group: this.form,
        Items: this.ProcessFlow
      }).then((res) => {
        if (res.IsSucceed) {
          this.$message({
            type: 'success',
            message: '保存成功'
          })
          this.$emit('refresh')
          this.$emit('close')
          this.dialogData = {}
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    removeTagFn(ids, tag) {
      console.log('ids', ids)
      console.log('tag', tag)
    },
    SelectType(item) {
      console.log('item', item)

      if (item.length === 1) {
        this.form.Check_Type = item[0]
      } else {
        this.form.Check_Type = -1
      }
      console.log('this.form.Check_Type', this.form.Check_Type)
    },
    changeNode(val) {
      console.log(val)
      console.log(this.CheckNodeList)
      if (val) {
        this.form.Check_Type = this.CheckNodeList.find((v) => {
          return v.Id === val
        }).Check_Type
        // 处理质检类型数据

        this.Change_Check_Type = []
        if (this.form.Check_Type === 1 || this.form.Check_Type === 2) {
          this.Isdisable = true
          this.Change_Check_Type.push(this.form.Check_Type)
        } else if (this.form.Check_Type === -1) {
          this.Isdisable = false // 质检类型可编辑
          this.Change_Check_Type = []
        } else {
          this.Change_Check_Type = []
          this.Isdisable = false
        }
        console.log(' this.Isdisable', this.Isdisable)
      } else {
        this.Change_Check_Type = []
      }
    },
    getEntityCheckType(data) {
      console.log(data)
      EntityQualityList({
        id: data.Id,
        check_object_id: this.Check_Object_Id
      }).then((res) => {
        if (res.IsSucceed) {
          this.form = res.Data[0].Group
          console.log(this.form.Object_Type_Ids, 'Object_Type_Ids')

          this.ProcessFlow = res.Data[0].Items
          this.Change_Check_Type = []
          // 处理质检类型数据
          if (this.form.Check_Type === 1 || this.form.Check_Type === 2) {
            this.Change_Check_Type.push(this.form.Check_Type)
            if (res.Data[0].CheckNode_Type === -1) {
              this.Isdisable = false
            } else {
              this.Isdisable = true
            }
          } else if (this.form.Check_Type === -1) {
            this.Change_Check_Type = [1, 2]
            this.Isdisable = true // 质检类型不可编辑
          } else {
            this.Change_Check_Type = []
            this.Isdisable = false
          }
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
        this.pageLoading = false
      })
    },
    handleSubmit(form) {
      if (this.Change_Check_Type.length === 0) {
        this.$message({
          type: 'error',
          message: '请选择检查类型'
        })
        return
      }
      let verification = true
      if (this.ProcessFlow.length === 0) {
        verification = false
      } else {
        this.ProcessFlow.forEach((val) => {
          for (const key in val) {
            if (val[key] === '') {
              verification = false
            }
          }
        })
      }
      if (!verification) {
        this.$message({
          type: 'error',
          message: '请填写完整检查项设置内容'
        })
        return
      }

      const processFlowCopy = JSON.parse(JSON.stringify(this.ProcessFlow))
      const processFlowNew = []
      processFlowCopy.forEach((item) => {
        const processFlowJson = {}
        processFlowJson.Check_Item_Id = item.Check_Item_Id
        processFlowJson.Eligibility_Criteria = item.Eligibility_Criteria
        processFlowJson.Questionlab_Id = item.Questionlab_Id
        processFlowNew.push(processFlowJson)
      })
      const processFlowTemp = processFlowNew.map((item) => {
        return JSON.stringify(item)
      })
      if (new Set(processFlowTemp).size !== processFlowTemp.length) {
        this.$message({
          type: 'error',
          message: '检查项设置内容不能完全相同'
        })
        return
      }

      const processFlowArr = this.ProcessFlow.map((v) => {
        return v.Questionlab_Id
      })

      const isIncludes = this.form.Questionlab_Ids.every((item) =>
        processFlowArr.includes(item)
      )
      if (!isIncludes) {
        this.$message({
          type: 'error',
          message: '检查项设置必须包含已选检查类型'
        })
        return
      }

      this.$refs[form].validate((valid) => {
        if (valid) {
          this.addCheckItemCombination()
        } else {
          return false
        }
      })
    },
    // 获取专业类别
    async getProfessionalType() {
      const Platform =
        localStorage.getItem('Platform') || localStorage.getItem('CurPlatform')
      if (Platform === '2') {
        this.mode = 'factory'
        await GetFactoryProfessionalByCode().then((res) => {
          if (res.IsSucceed) {
            this.ProCategoryList = res.Data
            const {
              Code,
              Id
            } = res.Data?.find(item => item.Code === 'Steel') || {}
            this.typeCode = Code
            this.typeId = Id
          } else {
            this.$message({
              type: 'error',
              message: res.Message
            })
          }
        })
      }

      // 获取项目/工厂id
      this.ProjectId =
        this.mode === 'factory'
          ? localStorage.getItem('CurReferenceId')
          : this.ProjectId
    },

    // 获取检查类型下拉框
    async getCheckTypeList() {
      await GetCheckTypeList({ check_object_id: this.Check_Object_Id, Bom_Level: this.form.Bom_Level }).then(
        (res) => {
          if (res.IsSucceed) {
            this.CheckTypeList = res.Data
            console.log(res.Data)
          } else {
            this.$message({
              type: 'error',
              message: res.Message
            })
          }
        }
      )
    },
    // 检查项内容
    async getCheckItemList() {
      await GetCheckItemList({ check_object_id: this.Check_Object_Id }).then(
        (res) => {
          if (res.IsSucceed) {
            this.CheckItemList = res.Data
            console.log(res.Data)
          } else {
            this.$message({
              type: 'error',
              message: res.Message
            })
          }
        }
      )
    },
    // 通过专业类别选择对象类型
    changeCategory(val) {
      this.form.Object_Type_Ids = []
      this.chooseType(val)
    },
    // 通过专业类别选择对象类型
    chooseType(val) {
      console.log(this.ProCategoryList)
      this.ProCategoryCode = this.ProCategoryList.find((v) => {
        return v.Id === val
      }).Code
      this.getObjectTypeList(this.ProCategoryCode) // 对象类型
    },
    // 选中表格外检查类型
    ChangeCheckType(val) {
      const arrJson = Object.assign([], val)
      // let index = arrJson.indexOf(Isexist);
      // this.ProcessFlow.splice(index, 1);
      console.log(arrJson)
      if (this.ProcessFlow.length > arrJson.length) {
        const arrJsonTemp = arrJson.map((item) => {
          const itemField = {
            Check_Item_Id: '',
            Eligibility_Criteria: '',
            Questionlab_Id: item
          }
          this.ProcessFlow.forEach((items) => {
            if (items.Questionlab_Id === item) {
              itemField.Check_Item_Id = items.Check_Item_Id
              itemField.Eligibility_Criteria = items.Eligibility_Criteria
            }
          })

          return itemField
        })
        this.ProcessFlow = [].concat(arrJsonTemp)
      } else {
        for (var i = 0; i < arrJson.length; i++) {
          const Isexist = this.ProcessFlow.find((v) => {
            return v.Questionlab_Id === arrJson[i]
          })
          if (!Isexist) {
            this.ProcessFlow.push({
              Questionlab_Id: arrJson[i],
              Check_Item_Id: '',
              Eligibility_Criteria: ''
            })
          }
        }
      }

      console.log('ChangeCheckType()', this.ProcessFlow)
    },

    removeCheckType(val) {
      const Isexist = this.ProcessFlow.find((v) => {
        return v.Questionlab_Id === val
      })
      const index = this.ProcessFlow.indexOf(Isexist)
      if (Isexist) {
        this.ProcessFlow.splice(index, 1)
      }
    },
    // 选中检查项内容
    ChangeItem(data, index, row) {
      // console.log(data);
      // console.log(index);
      // console.log(row)
      // console.log(this.CheckItemList);
      row.Eligibility_Criteria = ''
      this.Eligibility_Criteria = ''
      this.Eligibility_Criteria = this.CheckItemList.find((v) => {
        return v.Id === data
      })?.Eligibility_Criteria
      this.$set(
        this.ProcessFlow[index],
        'Eligibility_Criteria',
        this.Eligibility_Criteria
      )
      this.$set(this.ProcessFlow[index], 'sort', index)
      console.log(this.ProcessFlow)
    },

    async editHandleData(data) {
      if (this.title === '编辑') {
        console.log('data', data)
        this.form.Id = data.Id
        this.getEntityCheckType(data)
        await this.chooseType(data.Pro_Category_Id)
      }
    },

    // 质检节点下拉菜单
    async getNodeList(data) {
      await GetNodeList({ check_object_id: this.Check_Object_Id, Bom_Level: this.form.Bom_Level }).then(
        (res) => {
          if (res.IsSucceed) {
            this.CheckNodeList = res.Data
            this.editHandleData(data)
            console.log(res.Data)
          } else {
            this.$message({
              type: 'error',
              message: res.Message
            })
          }
        }
      )
    },
    // 对象类型下拉
    async getObjectTypeList(code) {
      if (this.checkType.Display_Name === '物料') {
        GetMaterialType({}).then((res) => {
          this.ObjectTypeList = res.Data
        })
      } else {
        let res
        if (this.partGrade === '-1') {
          res = await GetCompTypeTree({ professional: code })
        } else {
          res = await GetPartTypeTree({ professionalId: this.typeId, partGrade: this.partGrade })
        }
        if (res.IsSucceed) {
          this.ObjectTypeList.data = res.Data
          this.$nextTick((_) => {
            this.$refs.treeSelectObjectType.treeDataUpdateFun(res.Data)
          })
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
          this.ObjectTypeList.data = []
          this.$nextTick((_) => {
            this.$refs.treeSelectObjectType.treeDataUpdateFun([])
          })
        }
      }
    },

    // 检查项设置部分
    addTableData() {
      this.ProcessFlow.push({
        Check_Item_Id: '',
        Eligibility_Criteria: '',
        Questionlab_Id: ''
      })
      console.log('addTableData()', this.ProcessFlow)
    },
    deleteRow(index, rows) {
      console.log(index)
      rows.splice(index, 1)
      console.log(this.ProcessFlow)
      if (this.ProcessFlow.length > 0 && index !== this.ProcessFlow.length) {
        this.$set(this.ProcessFlow[index], 'sort', index)
      }
    },
    moveUpward(row, index) {
      console.log(index)
      const upData = this.ProcessFlow[index - 1]
      this.ProcessFlow.splice(index - 1, 1)
      this.ProcessFlow.splice(index, 0, upData)
      this.$set(this.ProcessFlow[index - 1], 'sort', index - 1)
      this.$set(this.ProcessFlow[index], 'sort', index)
      console.log(this.ProcessFlow)
    },
    moveDown(row, index) {
      console.log(index)
      const downData = this.ProcessFlow[index + 1]
      this.ProcessFlow.splice(index + 1, 1)
      this.ProcessFlow.splice(index, 0, downData)
      console.log(this.ProcessFlow)
      this.$set(this.ProcessFlow[index], 'sort', index)
      this.$set(this.ProcessFlow[index + 1], 'sort', index + 1)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .checkItem {
    width: 100%;
    .el-form-item__content {
      margin-left: 0 !important;
    }
  }
  .addcheckItem {
    font-size: 16px;
    margin-bottom: 10px;
  }
}
::v-deep .el-form-item {
  display: inline-block;
  .el-form-item__content {
    & > .el-input {
      width: 220px !important;
    }
    & > .el-select {
      width: 220px !important;
    }
    .el-tree-select-input {
      width: 220px !important;
    }
  }
}
</style>

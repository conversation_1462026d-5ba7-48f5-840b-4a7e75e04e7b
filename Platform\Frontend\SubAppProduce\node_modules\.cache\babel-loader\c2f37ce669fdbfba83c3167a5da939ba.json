{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\index.vue", "mtime": 1756109946518}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["TreeData", "Add", "DeleteComponentType", "GetComponentTypeEntity", "SaveProBimComponentType", "name", "components", "data", "typeCode", "typeId", "level", "addLevel", "undefined", "dialogVisible", "submitLoading", "deleteLoading", "showForm", "hasChildrenNode", "currentComponent", "parentId", "title", "form", "Name", "Code", "Is_Component", "Lead_Time", "rules", "required", "message", "trigger", "computed", "levelName", "methods", "addNext", "Id", "showRight", "v", "submit", "_this", "$refs", "validate", "valid", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "submitConfirm", "catch", "$message", "_this2", "res", "IsSucceed", "getTreeData", "Message", "finally", "_", "fetchData", "add<PERSON><PERSON><PERSON>", "handleClose", "nodeClick", "node", "childNodes", "length", "getInfo", "id", "_this3", "Object", "assign", "Data", "handleDelete", "_this4", "ids", "reset<PERSON>ey"], "sources": ["src/views/PRO/bom-setting/structure-type-config/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"container abs100\">\r\n\r\n    <div class=\"card-x\">\r\n      <tree-data ref=\"tree\" :type-code=\"typeCode\" :type-id=\"typeId\" @nodeClick=\"nodeClick\" @AddFirst=\"addFirst\" @showRight=\"showRight\" />\r\n      <div class=\"right-card\">\r\n        <el-form v-if=\"showForm\" ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n          <el-form-item :label=\"`${levelName}大类名称`\" prop=\"Name\">\r\n            <el-input v-model.trim=\"form.Name\" clearable maxlength=\"50\" />\r\n          </el-form-item>\r\n          <el-form-item :label=\"`${levelName}大类编号`\" prop=\"Code\">\r\n            <el-input v-model=\"form.Code\" disabled />\r\n          </el-form-item>\r\n          <el-form-item label=\"生产周期\" prop=\"Lead_Time\">\r\n            <el-input-number v-model.number=\"form.Lead_Time\" class=\"cs-number-btn-hidden w100\" clearable />\r\n          </el-form-item>\r\n          <el-form-item label=\"直发件\" prop=\"Is_Component\">\r\n            <el-radio-group v-model=\"form.Is_Component\">\r\n              <el-radio :label=\"true\">否</el-radio>\r\n              <el-radio :label=\"false\">是</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button v-if=\"level<3\" type=\"text\" icon=\"el-icon-plus\" @click=\"addNext\">新增下一级</el-button>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" :loading=\"submitLoading\" @click=\"submit\">保存</el-button>\r\n            <el-button type=\"danger\" :loading=\"deleteLoading\" :disabled=\"hasChildrenNode\" @click=\"handleDelete\">删除</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"title\"\r\n      class=\"plm-custom-dialog\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"30%\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        ref=\"content\"\r\n        :add-level=\"addLevel\"\r\n        :parent-id=\"parentId\"\r\n        @close=\"handleClose\"\r\n        @getTreeList=\"getTreeData\"\r\n      />\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport TreeData from './component/TreeData'\r\nimport Add from './component/Add'\r\nimport { DeleteComponentType, GetComponentTypeEntity, SaveProBimComponentType } from '@/api/PRO/component-type'\r\n\r\nexport default {\r\n  name: 'ProfessionalCategoryListInfo',\r\n  components: {\r\n    TreeData,\r\n    Add\r\n  },\r\n  data() {\r\n    return {\r\n      typeCode: 'Steel',\r\n      typeId: 'd28a81a0-ce31-4b56-8e22-b86922668894',\r\n      level: 1,\r\n      addLevel: undefined,\r\n      dialogVisible: false,\r\n      submitLoading: false,\r\n      deleteLoading: false,\r\n      showForm: false,\r\n      hasChildrenNode: true,\r\n      currentComponent: '',\r\n      parentId: '',\r\n      title: '',\r\n      form: {\r\n        Name: '',\r\n        Code: '',\r\n        Is_Component: '',\r\n        Lead_Time: 0\r\n      },\r\n      rules: {\r\n        Name: [\r\n          { required: true, message: '请输入名称', trigger: 'blur' }\r\n        ],\r\n        Code: [\r\n          { required: true, message: '请输入编码', trigger: 'blur' }\r\n        ],\r\n        Is_Component: [\r\n          { required: true, message: '请选择是否直发件', trigger: 'change' }\r\n        ],\r\n        Lead_Time: [\r\n          { required: true, message: '请输入周期', trigger: 'blur' }\r\n        ]\r\n      },\r\n      Is_Component: ''\r\n    }\r\n  },\r\n  computed: {\r\n    levelName() {\r\n      return this.level === 1 ? '一级' : (this.level === 2 ? '二级' : (this.level === 3 ? '三级' : ''))\r\n    }\r\n  },\r\n  methods: {\r\n    addNext() {\r\n      this.currentComponent = 'Add'\r\n      this.addLevel = this.level + 1\r\n      this.title = `新增下一级`\r\n      this.parentId = this.form.Id\r\n      this.dialogVisible = true\r\n    },\r\n    showRight(v) {\r\n      this.showForm = v\r\n    },\r\n    submit() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (!valid) {\r\n          return false\r\n        }\r\n        if (this.Is_Component != this.form.Is_Component) {\r\n          this.$confirm('直发件属性不会同步到已导入构件清单中，确认修改？', '提示', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n            this.submitConfirm()\r\n          }).catch(() => {\r\n            this.$message({\r\n              type: 'info',\r\n              message: '已取消修改'\r\n            })\r\n          })\r\n        } else {\r\n          this.submitConfirm()\r\n        }\r\n      })\r\n    },\r\n    submitConfirm() {\r\n      this.submitLoading = true\r\n      SaveProBimComponentType(this.form).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '修改成功',\r\n            type: 'success'\r\n          })\r\n          this.getTreeData()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.submitLoading = false\r\n      })\r\n    },\r\n    getTreeData() {\r\n      this.$refs['tree'].fetchData()\r\n    },\r\n    addFirst() {\r\n      this.currentComponent = 'Add'\r\n      this.title = '新增类别'\r\n      this.addLevel = 1\r\n      this.dialogVisible = true\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    nodeClick(node) {\r\n      this.level = node.level\r\n      this.hasChildrenNode = node.childNodes.length > 0\r\n      this.getInfo(node.data.Id)\r\n    },\r\n    getInfo(id) {\r\n      GetComponentTypeEntity({\r\n        id\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          Object.assign(this.form, res.Data)\r\n          this.Is_Component = res.Data.Is_Component\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    handleDelete() {\r\n      this.$confirm('是否删除当前类别?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.deleteLoading = true\r\n        DeleteComponentType({\r\n          ids: this.form.Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.getTreeData()\r\n            this.$refs['tree'].resetKey(this.form.Id)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        }).finally(_ => {\r\n          this.deleteLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.container {\r\n  display: flex;\r\n  padding: 0 16px 16px;\r\n  flex-direction: column;\r\n  min-width: 998px;\r\n  overflow: hidden;\r\n\r\n  .top-x {\r\n    line-height: 48px;\r\n    height: 48px;\r\n  }\r\n\r\n  .card-x {\r\n    height: calc(100% - 48px);\r\n    display: flex;\r\n\r\n    .right-card {\r\n      display: flex;\r\n      flex-direction: column;\r\n      flex: 1;\r\n      border-radius: 4px;\r\n      background-color: #FFFFFF;\r\n      .el-form{\r\n        width: 50%;\r\n        margin:  auto;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDA,OAAAA,QAAA;AACA,OAAAC,GAAA;AACA,SAAAC,mBAAA,EAAAC,sBAAA,EAAAC,uBAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAN,QAAA,EAAAA,QAAA;IACAC,GAAA,EAAAA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,MAAA;MACAC,KAAA;MACAC,QAAA,EAAAC,SAAA;MACAC,aAAA;MACAC,aAAA;MACAC,aAAA;MACAC,QAAA;MACAC,eAAA;MACAC,gBAAA;MACAC,QAAA;MACAC,KAAA;MACAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,YAAA;QACAC,SAAA;MACA;MACAC,KAAA;QACAJ,IAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,IAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,YAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAJ,SAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAL,YAAA;IACA;EACA;EACAM,QAAA;IACAC,SAAA,WAAAA,UAAA;MACA,YAAArB,KAAA,qBAAAA,KAAA,qBAAAA,KAAA;IACA;EACA;EACAsB,OAAA;IACAC,OAAA,WAAAA,QAAA;MACA,KAAAf,gBAAA;MACA,KAAAP,QAAA,QAAAD,KAAA;MACA,KAAAU,KAAA;MACA,KAAAD,QAAA,QAAAE,IAAA,CAAAa,EAAA;MACA,KAAArB,aAAA;IACA;IACAsB,SAAA,WAAAA,UAAAC,CAAA;MACA,KAAApB,QAAA,GAAAoB,CAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;UACA;QACA;QACA,IAAAH,KAAA,CAAAd,YAAA,IAAAc,KAAA,CAAAjB,IAAA,CAAAG,YAAA;UACAc,KAAA,CAAAI,QAAA;YACAC,iBAAA;YACAC,gBAAA;YACAC,IAAA;UACA,GAAAC,IAAA;YACAR,KAAA,CAAAS,aAAA;UACA,GAAAC,KAAA;YACAV,KAAA,CAAAW,QAAA;cACAJ,IAAA;cACAjB,OAAA;YACA;UACA;QACA;UACAU,KAAA,CAAAS,aAAA;QACA;MACA;IACA;IACAA,aAAA,WAAAA,cAAA;MAAA,IAAAG,MAAA;MACA,KAAApC,aAAA;MACAV,uBAAA,MAAAiB,IAAA,EAAAyB,IAAA,WAAAK,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAF,MAAA,CAAAD,QAAA;YACArB,OAAA;YACAiB,IAAA;UACA;UACAK,MAAA,CAAAG,WAAA;QACA;UACAH,MAAA,CAAAD,QAAA;YACArB,OAAA,EAAAuB,GAAA,CAAAG,OAAA;YACAT,IAAA;UACA;QACA;MACA,GAAAU,OAAA,WAAAC,CAAA;QACAN,MAAA,CAAApC,aAAA;MACA;IACA;IACAuC,WAAA,WAAAA,YAAA;MACA,KAAAd,KAAA,SAAAkB,SAAA;IACA;IACAC,QAAA,WAAAA,SAAA;MACA,KAAAxC,gBAAA;MACA,KAAAE,KAAA;MACA,KAAAT,QAAA;MACA,KAAAE,aAAA;IACA;IACA8C,WAAA,WAAAA,YAAA;MACA,KAAA9C,aAAA;IACA;IACA+C,SAAA,WAAAA,UAAAC,IAAA;MACA,KAAAnD,KAAA,GAAAmD,IAAA,CAAAnD,KAAA;MACA,KAAAO,eAAA,GAAA4C,IAAA,CAAAC,UAAA,CAAAC,MAAA;MACA,KAAAC,OAAA,CAAAH,IAAA,CAAAtD,IAAA,CAAA2B,EAAA;IACA;IACA8B,OAAA,WAAAA,QAAAC,EAAA;MAAA,IAAAC,MAAA;MACA/D,sBAAA;QACA8D,EAAA,EAAAA;MACA,GAAAnB,IAAA,WAAAK,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAe,MAAA,CAAAC,MAAA,CAAAF,MAAA,CAAA7C,IAAA,EAAA8B,GAAA,CAAAkB,IAAA;UACAH,MAAA,CAAA1C,YAAA,GAAA2B,GAAA,CAAAkB,IAAA,CAAA7C,YAAA;QACA;UACA0C,MAAA,CAAAjB,QAAA;YACArB,OAAA,EAAAuB,GAAA,CAAAG,OAAA;YACAT,IAAA;UACA;QACA;MACA;IACA;IAEAyB,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAA7B,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACAyB,MAAA,CAAAxD,aAAA;QACAb,mBAAA;UACAsE,GAAA,EAAAD,MAAA,CAAAlD,IAAA,CAAAa;QACA,GAAAY,IAAA,WAAAK,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAmB,MAAA,CAAAtB,QAAA;cACAJ,IAAA;cACAjB,OAAA;YACA;YACA2C,MAAA,CAAAlB,WAAA;YACAkB,MAAA,CAAAhC,KAAA,SAAAkC,QAAA,CAAAF,MAAA,CAAAlD,IAAA,CAAAa,EAAA;UACA;YACAqC,MAAA,CAAAtB,QAAA;cACArB,OAAA,EAAAuB,GAAA,CAAAG,OAAA;cACAT,IAAA;YACA;UACA;QACA,GAAAU,OAAA,WAAAC,CAAA;UACAe,MAAA,CAAAxD,aAAA;QACA;MACA,GAAAiC,KAAA;QACAuB,MAAA,CAAAtB,QAAA;UACAJ,IAAA;UACAjB,OAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
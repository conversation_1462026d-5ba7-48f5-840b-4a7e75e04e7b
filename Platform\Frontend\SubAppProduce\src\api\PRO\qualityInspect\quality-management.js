import request from '@/utils/request'

// 获取质检管理列表 (Auth)
export function GetPageQualityManagement(data) {
  return request({
    url: '/PRO/Inspection/GetPageQualityManagement',
    method: 'post',
    data
  })
}
// 管理新增单个质检单 (Auth)
export function ManageAdd(data) {
  return request({
    url: '/PRO/Inspection/ManageAdd',
    method: 'post',
    data
  })
}
// 批量新增质检单 (Auth)
export function AddLanch(data) {
  return request({
    url: '/PRO/Inspection/AddLanch',
    method: 'post',
    data
  })
}
// 获取质检对象 (Auth)
export function GetDictionaryDetailListByCode(data) {
  return request({
    url: '/SYS/Dictionary/GetDictionaryDetailListByCode',
    method: 'post',
    data
  })
}
// 获取质检节点 (Auth)
export function GetNodeList(data) {
  return request({
    url: '/PRO/Inspection/GetNodeList',
    method: 'post',
    data
  })
}
// 获取零件或者构件信息 (Auth)
export function GetPartAndSteelBacrode(data) {
  return request({
    url: '/PRO/Inspection/GetPartAndSteelBacrode',
    method: 'post',
    data
  })
}
// 查看质检详情 (Auth)
export function EntityQualityManagement(data) {
  return request({
    url: '/PRO/Inspection/EntityQualityManagement',
    method: 'post',
    data
  })
}
// 质检管理 质检 (Auth)
export function SaveTesting(data) {
  return request({
    url: '/PRO/Inspection/SaveTesting',
    method: 'post',
    data
  })
}
// 获取人员列表
export function GetFactoryPeoplelist(data) {
  return request({
    url: '/PRO/Factory/GetFactoryPeoplelist',
    method: 'post',
    data
  })
}
// 获取深化图纸
export function GetSheetDwg(data) {
  return request({
    url: '/PRO/Inspection/GetSheetDwg',
    method: 'post',
    data
  })
}
// 获取深化图纸(批量)
export function GetCanvasSheetDwg(data) {
  return request({
    url: '/PRO/Inspection/GetCanvasSheetDwg',
    method: 'post',
    data
  })
}
// 批量合格 (Auth)
export function SavePass(data) {
  return request({
    url: '/PRO/Inspection/SavePass',
    method: 'post',
    data,
    timeout: 20 * 60 * 1000
  })
}
// 发起质检列表提交 (Auth)
export function SubmitLanch(data) {
  return request({
    url: '/PRO/Inspection/SubmitLanch',
    method: 'post',
    data
  })
}
// 根据id进行删除质检 (Auth)
export function DelLanch(data) {
  return request({
    url: '/PRO/Inspection/DelLanch',
    method: 'post',
    data
  })
}
// 质检管理批量添加
export function BatchManageSaveCheck(data) {
  return request({
    url: '/PRO/Inspection/BatchManageSaveCheck',
    method: 'post',
    data
  })
}
// 质检查看
export function GetEditById(data) {
  return request({
    url: '/PRO/Inspection/GetEditById',
    method: 'post',
    data
  })
}
// 抽检查看
export function GetCheckingEntity(data) {
  return request({
    url: '/PRO/Inspection/GetCheckingEntity',
    method: 'post',
    data
  })
}
// 质检反馈列表
export function GetPageFeedBack(data) {
  return request({
    url: '/PRO/Inspection/GetPageFeedBack',
    method: 'post',
    data
  })
}
// 整改复核
export function SaveFeedBack(data) {
  return request({
    url: '/PRO/Inspection/SaveFeedBack',
    method: 'post',
    data
  })
}
// 整改复核
export function RectificationRecord(data) {
  return request({
    url: '/PRO/Inspection/RectificationRecord',
    method: 'post',
    data
  })
}
export function GetCompPartForSpotCheckPageList(data) {
  return request({
    url: '/PRO/Inspection/GetCompPartForSpotCheckPageList',
    method: 'post',
    data
  })
}
export function GetSpotCheckingEntity(data) {
  return request({
    url: '/PRO/Inspection/GetSpotCheckingEntity',
    method: 'post',
    data
  })
}

export function GetCompQISummary(data) {
  return request({
    // url: '/PRO/Inspection/GetCompQISummary',
    url: '/PRO/Inspection/GetCompQISummary',
    method: 'post',
    data
  })
}

export function ExportQISummary(data) {
  return request({
    url: '/PRO/Inspection/ExportQISummary',
    method: 'post',
    data
  })
}

export function ImportQISummary(data) {
  return request({
    url: '/PRO/Inspection/ImportQISummary',
    method: 'post',
    data
  })
}

export function SaveQIReportData(data) {
  return request({
    url: '/PRO/Inspection/SaveQIReportData',
    method: 'post',
    data
  })
}

export function SaveToleranceSetting(data) {
  return request({
    url: '/pro/Inspection/SaveToleranceSetting',
    method: 'post',
    data
  })
}

export function DeleteToleranceSetting(data) {
  return request({
    url: '/pro/Inspection/DeleteToleranceSetting',
    method: 'post',
    data
  })
}

export function GetToleranceSettingList(data) {
  return request({
    url: '/pro/Inspection/GetToleranceSettingList',
    method: 'post',
    data
  })
}

export function InsertBatchTrack(data) {
  return request({
    url: '/pro/Track/InsertBatchTrack',
    method: 'post',
    timeout: 5 * 60 * 1000,
    data
  })
}

export function BatchUpdateTrackEditableFields(data) {
  return request({
    url: '/pro/Track/BatchUpdateTrackEditableFields',
    method: 'post',
    data
  })
}

export function ExportstBatchTrackFromSourceAsync(data) {
  return request({
    url: '/pro/Track/ExportstBatchTrackFromSourceAsync',
    method: 'post',
    data
  })
}

// 质检分配 - 更改质检人员
export function ChangeCheckUser(data) {
  return request({
    url: '/pro/Inspection/ChangeCheckUser',
    method: 'post',
    data
  })
}

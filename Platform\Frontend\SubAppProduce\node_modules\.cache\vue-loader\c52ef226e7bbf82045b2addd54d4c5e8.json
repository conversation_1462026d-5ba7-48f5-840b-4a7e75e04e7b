{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-path\\compoments\\Add.vue?vue&type=style&index=0&id=298595a7&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-path\\compoments\\Add.vue", "mtime": 1757492056372}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5jcy1yb3d7DQogIGRpc3BsYXk6IGZsZXg7DQp9DQouY3MtY29sew0KICB0ZXh0LWFsaWduOiByaWdodDsNCiAgbWFyZ2luLXRvcDogN3B4Ow0KfQ0KLmNzLWNvbDJ7DQp9DQouY3MtZHJhZ3sNCiAgY3Vyc29yOiBwb2ludGVyOw0KICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQp9DQouY3Mtd3JhcHsNCiAgbWF4LWhlaWdodDogNjB2aDsNCiAgb3ZlcmZsb3cteTogYXV0bzsNCn0NCi5jcy10cmVlLXggew0KICA6OnYtZGVlcCB7DQogICAgLmVsLXNlbGVjdCB7DQogICAgICB3aWR0aDogOTAlOw0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["Add.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6aA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Add.vue", "sourceRoot": "src/views/PRO/process-path/compoments", "sourcesContent": ["<template>\r\n  <el-dialog\r\n    v-dialogDrag\r\n    :title=\"isEdit?'编辑':'新增'\"\r\n    :visible.sync=\"dialogVisible\"\r\n    width=\"30%\"\r\n    class=\"plm-custom-dialog\"\r\n    @close=\"handleClose\"\r\n  >\r\n    <div class=\"cs-wrap\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"工艺代码\" prop=\"Code\">\r\n          <el-input v-model=\"form.Code\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"类型\" prop=\"Bom_Level\">\r\n          <template>\r\n            <el-radio-group v-model=\"form.Bom_Level\" @change=\"radioChange\">\r\n              <!-- <el-radio :label=\"1\">构件工艺</el-radio>\r\n            <el-radio :label=\"3\">部件工艺</el-radio>\r\n            <el-radio :label=\"2\">零件工艺</el-radio> -->\r\n              <el-radio v-for=\"item in bomList\" :key=\"item.Code\" :label=\"item.Code\">{{ item.Display_Name }}工艺</el-radio>\r\n            </el-radio-group>\r\n          </template>\r\n        </el-form-item>\r\n        <draggable v-model=\"list\" handle=\".icon-drag\" @change=\"changeDraggable\">\r\n          <el-row v-for=\"(element,index) in list\" :key=\"element.key\" class=\"cs-row\">\r\n            <el-col class=\"cs-col\" :span=\"2\"> <i class=\"iconfont icon-drag cs-drag\" /> </el-col>\r\n            <el-col :span=\"19\">\r\n              <el-form-item :label=\"`工序${index+1}`\" label-width=\"50px\">\r\n                <el-select :key=\"element.key\" v-model=\"element.value\" style=\"width:90%\" :disabled=\"!form.Bom_Level\" placeholder=\"请选择\" clearable @change=\"selectChange($event,element)\">\r\n                  <el-option\r\n                    v-for=\"item in options\"\r\n                    :key=\"item.Code\"\r\n                    :label=\"item.Name\"\r\n                    :disabled=\"item.disabled\"\r\n                    :value=\"item.Code\"\r\n                  >\r\n                    <div class=\"cs-option\">\r\n                      <span class=\"cs-label\">{{ item.Name }}</span>\r\n                    </div>\r\n                  </el-option>\r\n                </el-select>\r\n\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col class=\"cs-col2\" :span=\"3\">\r\n              <span class=\"btn-x\">\r\n                <el-button v-if=\"index===0 && list.length<options.length\" type=\"primary\" icon=\"el-icon-plus\" circle @click=\"handleAdd\" />\r\n                <el-button v-if=\"index!==0\" type=\"danger\" icon=\"el-icon-delete\" circle @click=\"handleDelete(element)\" />\r\n              </span>\r\n            </el-col>\r\n          </el-row>\r\n\r\n        </draggable>\r\n        <el-form-item label=\"备注\" prop=\"Remark\">\r\n          <el-input\r\n            v-model=\"form.Remark\"\r\n            style=\"width: 90%\"\r\n            :autosize=\"{ minRows: 3, maxRows: 5}\"\r\n            show-word-limit\r\n            :maxlength=\"50\"\r\n            type=\"textarea\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"产品类型\" prop=\"Component_Type\">\r\n          <el-tree-select\r\n            ref=\"treeSelectComponentType\"\r\n            v-model=\"searchComTypeSearch\"\r\n            style=\"width: 90%\"\r\n            placeholder=\"请选择\"\r\n            :select-params=\"treeSelectParams\"\r\n            class=\"cs-tree-x\"\r\n            :disabled=\"!form.Bom_Level\"\r\n            :tree-params=\"treeParamsComponentType\"\r\n            @searchFun=\"componentTypeFilter\"\r\n            @check=\"componentTypeChange\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n    <span slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"submit\">确 定</el-button>\r\n    </span>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport Draggable from 'vuedraggable'\r\nimport { AddProessLib, GetProcessFlow, GetProcessListBase } from '@/api/PRO/technology-lib'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { GetCompTypeTree } from '@/api/PRO/component-type'\r\n\r\nimport { GetAllEntities } from '@/api/PRO/settings'\r\nimport { GetPartTypeTree } from '@/api/PRO/partType'\r\n\r\nexport default {\r\n  components: { Draggable },\r\n  props: {\r\n    bomList: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      btnLoading: false,\r\n      isEdit: false,\r\n      searchComTypeSearch: [],\r\n      productTypeList: [],\r\n      form: {\r\n        Component_Type_Codes: [],\r\n        Component_Type_Ids: [],\r\n        Code: '',\r\n        Remark: '',\r\n        Bom_Level: undefined\r\n      },\r\n      rules: {\r\n        Code: [\r\n          { required: true, message: '请输入 ', trigger: 'blur' }\r\n        ],\r\n        Bom_Level: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ]\r\n      },\r\n      list: [],\r\n      options: [],\r\n      treeParamsComponentType: {\r\n        'default-expand-all': true,\r\n        'check-strictly': true,\r\n        filterable: true,\r\n        clickParent: true,\r\n        collapseTags: true,\r\n        multiple: false,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data',\r\n          disabled: 'Is_Disabled'\r\n        }\r\n      },\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        clearable: true\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    searchComTypeSearch: {\r\n      handler(val) {\r\n        if (!val.length) {\r\n          this.form.Component_Type_Codes = []\r\n          this.form.Component_Type_Ids = []\r\n        }\r\n      }\r\n    }\r\n  },\r\n  async mounted() {\r\n    this.getProfession()\r\n  },\r\n  methods: {\r\n    async getProfession() {\r\n      const res = await GetAllEntities({\r\n        companyId: localStorage.getItem('Last_Working_Object_Id'),\r\n        is_System: false\r\n      })\r\n      if (res.IsSucceed) {\r\n        const {\r\n          Code,\r\n          Id\r\n        } = res.Data?.Data?.find(item => item.Code === 'Steel') || {}\r\n        this.typeCode = Code\r\n        this.typeId = Id\r\n        console.log(this.typeCode, this.typeId)\r\n      }\r\n    },\r\n    // componentTypeNodeClick(node) {\r\n    //   console.log(node)\r\n    //   const { Data, Id } = node\r\n    //   this.form.Component_Type_Codes = [Data]\r\n    //   this.form.Component_Type_Ids = [Id]\r\n    // },\r\n    componentTypeChange(vv, c) {\r\n      const _ids = []\r\n      const _codes = []\r\n      const nodes = c.checkedNodes\r\n      console.log(11, nodes)\r\n      nodes.forEach((element, idx) => {\r\n        if (this.form.Bom_Level === 1) {\r\n          const { Data, Id } = element\r\n          _ids.push(Id)\r\n          _codes.push(Data)\r\n        } else {\r\n          const { Data, Id } = element\r\n          _ids.push(Id)\r\n          _codes.push(Data)\r\n        }\r\n      })\r\n      console.log(_ids, _codes)\r\n      this.form.Component_Type_Codes = _codes\r\n      this.form.Component_Type_Ids = _ids\r\n      this.searchComTypeSearch = _ids\r\n    },\r\n    changeDraggable() {\r\n    },\r\n    init() {\r\n      this.list = [{\r\n        key: uuidv4(),\r\n        value: '',\r\n        id: ''\r\n      }]\r\n    },\r\n    async getProductType() {\r\n      let res\r\n      if (this.form.Bom_Level === '-1') {\r\n        res = await GetCompTypeTree({\r\n          professional: this.typeCode,\r\n          markAsOccupied: true,\r\n          technologyId: this.technologyId\r\n        })\r\n      } else {\r\n        res = await GetPartTypeTree({\r\n          professionalId: this.typeId,\r\n          markAsOccupied: true,\r\n          technologyId: this.technologyId,\r\n          partGrade: this.form.Bom_Level.toString()\r\n        })\r\n      }\r\n      if (res.IsSucceed) {\r\n        // this.setDisabledTree(tree)\r\n        this.treeParamsComponentType.data = res.Data\r\n        this.$nextTick(_ => {\r\n            this.$refs.treeSelectComponentType?.treeDataUpdateFun(res.Data)\r\n        })\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    _searchFun(value) {\r\n      this.$refs.treeSelectType.filterFun(value)\r\n    },\r\n    selectChange(val, item) {\r\n      const arr = this.list.map(i => i.value)\r\n      const idx = this.options.findIndex(v => v.Code === val)\r\n      if (idx !== -1) {\r\n        item.id = this.options[idx].Id\r\n      }\r\n      this.options.forEach((item, index) => {\r\n        item.disabled = arr.includes(item.Code)\r\n      })\r\n    },\r\n    handleOpen(row) {\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        if (row && row.Id) {\r\n          this.isEdit = true\r\n          this.technologyId = row.Id\r\n          this.form.Bom_Level = row.Bom_Level.toString()\r\n          console.log('row', row)\r\n          this.getInfo(row)\r\n          if (row.Component_Type_Codes?.length) {\r\n            this.searchComTypeSearch = row?.Component_Type_Codes || []\r\n          } else {\r\n            this.searchComTypeSearch = []\r\n          }\r\n          this.getProductType()\r\n        } else {\r\n          this.technologyId = undefined\r\n          this.isEdit = false\r\n          this.init()\r\n          this.form.Bom_Level = undefined\r\n          this.form.Code = ''\r\n          this.form.Remark = ''\r\n          this.searchComTypeSearch = []\r\n        }\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.$refs['form'].resetFields()\r\n      this.dialogVisible = false\r\n    },\r\n    getInfo(row) {\r\n      GetProcessFlow({\r\n        technologyId: row.Id\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const lr = res.Data.sort((a, b) => a.Step - b.Step)\r\n          if (lr.length) {\r\n            Object.assign(this.form, {\r\n              Code: lr[0].Technology_Code,\r\n              Remark: row.Remark,\r\n              Id: row.Id\r\n            })\r\n            this.getProcessOption()\r\n            this.list = lr.map(v => {\r\n              return {\r\n                key: uuidv4(),\r\n                value: v.Process_code,\r\n                id: v.Process_Id,\r\n                tId: row.Id\r\n              }\r\n            })\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    radioChange() {\r\n      this.init()\r\n      this.getProcessOption()\r\n      this.getProductType()\r\n      this.searchComTypeSearch = []\r\n    },\r\n    componentTypeFilter(e) {\r\n      this.$refs?.treeSelectComponentType.filterFun(e)\r\n    },\r\n    handleAdd() {\r\n      const arr = this.list.map(v => v.value)\r\n      this.options.forEach(v => {\r\n        if (arr.includes(v.Code)) {\r\n          v.disabled = true\r\n        }\r\n      })\r\n      this.list.push({\r\n        key: uuidv4(),\r\n        value: '',\r\n        id: ''\r\n      })\r\n    },\r\n    handleDelete(element) {\r\n      const idx = this.list.findIndex(v => v.value === element.value)\r\n      if (idx !== -1) {\r\n        this.list.splice(idx, 1)\r\n      }\r\n    },\r\n    submit() {\r\n      console.log(this.form, this.list)\r\n      this.$refs['form'].validate((valid) => {\r\n        if (!valid) return false\r\n        const p = this.list.filter(v => v.value)\r\n        if (!p.length) {\r\n          this.$message({\r\n            message: '请至少选择一个工序',\r\n            type: 'error'\r\n          })\r\n          return\r\n        }\r\n        this.btnLoading = true\r\n        if (!this.isEdit && this.form.Id) {\r\n          delete this.form.Id\r\n        }\r\n        // const { Type, ...other } = this.form\r\n        // if (this.form.Type === '-1') {\r\n        //   other.Type = 1\r\n        // } else if (this.form.Type === '0') {\r\n        //   other.Type = 2\r\n        // } else {\r\n        //   other.Type = 3\r\n\r\n        // }\r\n        const form = { ...this.form }\r\n\r\n        const submitObj = {\r\n          TechnologyLib: form,\r\n          ProcessFlow: p.map((v, idx) => {\r\n            return {\r\n              Technology_Id: v.tId || '',\r\n              Process_Id: v.id,\r\n              Step: idx + 1\r\n            }\r\n          })\r\n        }\r\n        AddProessLib(submitObj).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            this.$emit('refresh')\r\n            this.dialogVisible = false\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        }).finally(() => {\r\n          this.btnLoading = false\r\n        })\r\n      })\r\n    },\r\n    getProcessOption() {\r\n      if (!this.form.Bom_Level) return\r\n      return new Promise((resolve, reject) => {\r\n        this.pgLoading = true\r\n        GetProcessListBase({\r\n          bomLevel: this.form.Bom_Level\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.options = res.Data.filter(v => v.Is_Enable).map(v => {\r\n              this.$set(v, 'disabled', false)\r\n              return v\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          resolve()\r\n        }).finally(_ => {\r\n          this.pgLoading = false\r\n        })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.cs-row{\r\n  display: flex;\r\n}\r\n.cs-col{\r\n  text-align: right;\r\n  margin-top: 7px;\r\n}\r\n.cs-col2{\r\n}\r\n.cs-drag{\r\n  cursor: pointer;\r\n  display: inline-block;\r\n}\r\n.cs-wrap{\r\n  max-height: 60vh;\r\n  overflow-y: auto;\r\n}\r\n.cs-tree-x {\r\n  ::v-deep {\r\n    .el-select {\r\n      width: 90%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
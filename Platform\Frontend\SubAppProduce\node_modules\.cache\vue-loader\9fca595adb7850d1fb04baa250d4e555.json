{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\mainPage.vue?vue&type=style&index=0&id=be426f82&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\mainPage.vue", "mtime": 1757909680924}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouZWwtZGl2aWRlcnsNCiAgbWFyZ2luOjAgMCAxMHB4Ow0KfQ0KLmJ0bi13cmFwcGVyew0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtc3RhcnQ7DQogICYgPiBidXR0b24gew0KICAgIG1hcmdpbi1yaWdodDogMTBweDsNCiAgfQ0KICAmID4gZGl2IHsNCiAgICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQogIH0NCn0NCi5jcy16LXBhZ2UtbWFpbi1jb250ZW50ew0KICBib3gtc2hhZG93OiB1bnNldDsNCn0NCg=="}, {"version": 3, "sources": ["mainPage.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAscA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "mainPage.vue", "sourceRoot": "src/views/PRO/plan-production/task-list", "sourcesContent": ["<template>\r\n  <div v-loading=\"Loading\" class=\"h100\" element-loading-text=\"数据生成中\">\r\n    <div class=\"cs-z-page-main-content\">\r\n      <el-form ref=\"form\" :model=\"queryForm\" inline label-width=\"80px\">\r\n        <el-form-item label=\"任务单号\" prop=\"Task_Code\">\r\n          <el-input v-model=\"queryForm.Task_Code\" placeholder=\"请输入\" clearable=\"\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"排产单号\" prop=\"Schduling_Code\">\r\n          <el-input v-model=\"queryForm.Schduling_Code\" placeholder=\"请输入\" clearable=\"\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"项目名称\" prop=\"projectId\">\r\n          <el-select\r\n            v-model=\"queryForm.projectId\"\r\n            filterable\r\n            clearable\r\n            placeholder=\"请选择\"\r\n            style=\"width: 100%\"\r\n            @change=\"projectChange\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in projectOption\"\r\n              :key=\"item.Id\"\r\n              :label=\"item.Short_Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item v-if=\"!isPart || isPart && hasUnitPart\" label=\"区域名称\" prop=\"areaId\">\r\n          <el-tree-select\r\n            ref=\"treeSelect\"\r\n            v-model=\"queryForm.areaId\"\r\n            :disabled=\"!queryForm.projectId\"\r\n            :select-params=\"{\r\n              clearable: true,\r\n            }\"\r\n            class=\"cs-tree-x\"\r\n            :tree-params=\"treeParams\"\r\n            @select-clear=\"areaClear\"\r\n            @node-click=\"areaChange\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item v-if=\"!isPart||isPart && hasUnitPart\" label=\"批次\" prop=\"install\">\r\n          <el-select\r\n            v-model=\"queryForm.install\"\r\n            :disabled=\"!queryForm.areaId\"\r\n            clearable\r\n            placeholder=\"请选择\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in installOption\"\r\n              :key=\"item.Id\"\r\n              :label=\"item.Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\" prop=\"Task_Status\">\r\n          <el-select\r\n            v-model=\"queryForm.Task_Status\"\r\n            clearable\r\n            placeholder=\"请选择\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option label=\"已完成\" :value=\"1\" />\r\n            <el-option label=\"未完成\" :value=\"0\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"search(1)\">搜索</el-button>\r\n          <el-button @click=\"handleReset\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-divider />\r\n      <div class=\"btn-wrapper\">\r\n        <el-button type=\"primary\" :disabled=\"!selectArray.length\" @click=\"handleExport\">导出任务单列表</el-button>\r\n        <template v-if=\"isCom\">\r\n          <el-dropdown\r\n            trigger=\"click\"\r\n            placement=\"bottom-start\"\r\n            @command=\"handleCommand($event, 1)\"\r\n          >\r\n            <el-button\r\n              type=\"primary\"\r\n              :disabled=\"!selectArray.length\"\r\n            >导出任务单\r\n              <i class=\"el-icon-arrow-down el-icon--right\" />\r\n            </el-button>\r\n            <el-dropdown-menu slot=\"dropdown\">\r\n              <el-dropdown-item\r\n                command=\"name\"\r\n              >构件名称导出</el-dropdown-item>\r\n              <el-dropdown-item\r\n                command=\"code\"\r\n              >构件号合并导出</el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </el-dropdown>\r\n        </template>\r\n        <template v-if=\"isUnitPart\">\r\n          <el-button :disabled=\"!selectArray.length\" type=\"success\" @click=\"handleCommand('name')\">导出任务单</el-button>\r\n        </template>\r\n        <template v-if=\"isPart\">\r\n          <el-button :disabled=\"!selectArray.length\" type=\"success\" @click=\"handleCommand('name')\">导出任务单</el-button>\r\n        </template>\r\n      </div>\r\n      <div\r\n        v-loading=\"tbLoading\"\r\n        element-loading-text=\"加载中\"\r\n        element-loading-spinner=\"el-icon-loading\"\r\n        class=\"fff  cs-z-tb-wrapper\"\r\n      >\r\n        <dynamic-data-table\r\n          ref=\"dyTable\"\r\n          :columns=\"columns\"\r\n          :data=\"tbData\"\r\n          :config=\"tbConfig\"\r\n          :page=\"queryInfo.Page\"\r\n          :total=\"total\"\r\n          border\r\n          class=\"cs-plm-dy-table\"\r\n          stripe\r\n          @multiSelectedChange=\"getSelectVal\"\r\n          @gridPageChange=\"handlePageChange\"\r\n          @gridSizeChange=\"handlePageChange\"\r\n        >\r\n          <template slot=\"Task_Code\" slot-scope=\"{ row }\">\r\n            <el-link type=\"primary\" @click=\"handleView(row)\">{{ row.Task_Code }}</el-link>\r\n          </template>\r\n          <template slot=\"Task_Status\" slot-scope=\"{ row }\">\r\n            <el-tag v-if=\"row.Task_Status === 0\" type=\"danger\">未完成</el-tag>\r\n            <el-tag v-else type=\"success\">已完成</el-tag>\r\n          </template>\r\n          <template slot=\"op\" slot-scope=\"{ row }\">\r\n            <el-button\r\n              type=\"text\"\r\n              @click=\"handleView(row)\"\r\n            >查看\r\n            </el-button>\r\n            <template v-if=\"isCom\">\r\n              <el-dropdown\r\n                trigger=\"click\"\r\n                placement=\"bottom-start\"\r\n                style=\"margin-left: 12px;\"\r\n                @command=\"printSelected($event, row)\"\r\n              >\r\n                <el-button>打印任务单\r\n                  <i class=\"el-icon-arrow-down el-icon--right\" />\r\n                </el-button>\r\n                <el-dropdown-menu slot=\"dropdown\">\r\n                  <el-dropdown-item\r\n                    command=\"name\"\r\n                  >构件名称打印</el-dropdown-item>\r\n                  <el-dropdown-item\r\n                    command=\"code\"\r\n                  >构件号合并打印</el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </el-dropdown>\r\n            </template>\r\n            <template v-if=\"isUnitPart\">\r\n              <el-button type=\"text\" @click=\"printSelected('name', row)\">打印任务单</el-button>\r\n            </template>\r\n            <template v-if=\"isPart\">\r\n              <el-button type=\"text\" @click=\"printSelected('name', row)\">打印任务单</el-button>\r\n            </template>\r\n          </template>\r\n        </dynamic-data-table>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport getQueryInfo from '@/views/PRO/plan-production/schedule-production/mixin'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'\r\nimport { GetTeamTaskPageList, ExportTaskCodeDetails } from '@/api/PRO/production-task'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nimport { timeFormat } from '@/filters'\r\nimport { parseTime, combineURL, debounce } from '@/utils'\r\nimport { mapGetters } from 'vuex'\r\n\r\nexport default {\r\n  components: {\r\n    DynamicDataTable\r\n  },\r\n  mixins: [getQueryInfo, getTbInfo, addRouterPage],\r\n  props: {\r\n    pageType: {\r\n      type: String,\r\n      default: '-1'\r\n    },\r\n    hasUnitPart: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/detail',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/plan-production/task-list/detail'),\r\n          name: 'PROTaskListDetail',\r\n          meta: { title: '任务单详情' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/detailPrint',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/plan-production/task-list/detailPrint'),\r\n          name: 'PROTaskListDetailPrint',\r\n          meta: { title: '打印任务单详情' }\r\n        }\r\n      ],\r\n      queryForm: {\r\n        Task_Status: undefined,\r\n        Task_Code: undefined,\r\n        Schduling_Code: undefined\r\n      },\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: 20\r\n      },\r\n      tbConfig: {\r\n        Op_Width: 200,\r\n        IS_ToolTip: true\r\n      },\r\n      Loading: false,\r\n      tbLoading: false,\r\n      columns: [],\r\n      tbData: [],\r\n      selectArray: [],\r\n      total: 0,\r\n      search: () => ({})\r\n    }\r\n  },\r\n  computed: {\r\n    isPart() {\r\n      return this.pageType === '0'\r\n    },\r\n    isCom() {\r\n      return this.pageType === '-1'\r\n    },\r\n    isUnitPart() {\r\n      return +this.pageType > 0\r\n    }\r\n  },\r\n\r\n  created() {\r\n    this.tbConfig.Op_Width = this.isCom ? 200 : 140\r\n  },\r\n\r\n  async mounted() {\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    await this.getTableConfig((this.isCom || this.isUnitPart) ? 'PROComTaskList' : 'PROPartTaskList')\r\n\r\n    if (!this.hasUnitPart) {\r\n      const arr = ['Project_Name', 'Area_Name', 'InstallUnit_Name']\r\n      arr.forEach((item) => {\r\n        const idx = this.columns.findIndex((v) => v.Code === item)\r\n        if (idx !== -1) {\r\n          this.columns.splice(idx, 1)\r\n        }\r\n      })\r\n    }\r\n\r\n    if (this.isPart) {\r\n      const idx = this.columns.findIndex((item) => item.Code === 'Process_Finish_Date')\r\n      idx !== -1 && this.columns.splice(idx, 1)\r\n    }\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    getSelectVal(v) {\r\n      this.selectArray = v\r\n    },\r\n    // 导出任务单列表\r\n    handleExport() {\r\n      const filterVal = this.columns.map(v => v.Code)\r\n      const data = formatJson(filterVal, this.selectArray)\r\n      const header = this.columns.map(v => v.Display_Name)\r\n      import('@/vendor/Export2Excel').then(excel => {\r\n        excel.export_json_to_excel({\r\n          header: header,\r\n          data,\r\n          filename: `${this.isCom ? '构件任务单' : this.isUnitPart ? '部件任务单' : '零件任务单'}`,\r\n          autoWidth: true,\r\n          bookType: 'xlsx'\r\n        })\r\n      })\r\n      function formatJson(filterVal, jsonData) {\r\n        return jsonData.map(v => filterVal.map(j => {\r\n          if (j === 'Order_Date') {\r\n            return timeFormat(v[j])\r\n          } else if (j === 'Task_Status') {\r\n            return v[j] === 0 ? '未完成' : '已完成'\r\n          } else {\r\n            return v[j]\r\n          }\r\n        }))\r\n      }\r\n    },\r\n    fetchData(page) {\r\n      page && (this.queryInfo.Page = page)\r\n      this.tbLoading = true\r\n      GetTeamTaskPageList({\r\n        ...this.queryInfo,\r\n        Task_Code: this.queryForm.Task_Code,\r\n        Schduling_Code: this.queryForm.Schduling_Code,\r\n        Project_Id: this.queryForm.projectId,\r\n        Task_Status: this.queryForm.Task_Status,\r\n        Area_Id: this.queryForm.areaId,\r\n        InstallUnit_Id: this.queryForm.install,\r\n        Bom_Level: this.pageType,\r\n        Process_Type: this.isCom ? 2 : this.isPart ? 1 : 3 // 工序类型 1零件 2构件\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.Data.map((v) => {\r\n            v.Finish_Date = v.Finish_Date\r\n              ? parseTime(new Date(v.Finish_Date), '{y}-{m}-{d}')\r\n              : v.Finish_Date\r\n            v.Order_Date = v.Order_Date\r\n              ? parseTime(new Date(v.Order_Date), '{y}-{m}-{d}')\r\n              : v.Order_Date\r\n            v.Task_Finish_Date = v.Task_Finish_Date\r\n              ? parseTime(new Date(v.Task_Finish_Date), '{y}-{m}-{d}')\r\n              : v.Task_Finish_Date\r\n            v.Process_Finish_Date = v.Process_Finish_Date\r\n              ? parseTime(new Date(v.Process_Finish_Date), '{y}-{m}-{d}')\r\n              : v.Process_Finish_Date\r\n            return v\r\n          })\r\n          this.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.$refs['form'].resetFields()\r\n      this.search(1)\r\n    },\r\n    // 导出任务单详情\r\n    handleCommand(command, type) {\r\n      const TeamTaskModel = this.selectArray.map(v => {\r\n        return {\r\n          Task_Code: v.Task_Code,\r\n          Working_Team_Id: v.Working_Team_Id\r\n        }\r\n      })\r\n      const Working_Team_Id = this.selectArray.length === 1 ? this.selectArray[0].Working_Team_Id : ''\r\n      const Task_Code = this.selectArray.length === 1 ? this.selectArray[0].Task_Code : ''\r\n      this.Loading = true\r\n      ExportTaskCodeDetails({\r\n        Process_Type: this.isCom ? 2 : this.isPart ? 1 : 3, // 1零件，2构件\r\n        Working_Team_Id,\r\n        Task_Code,\r\n        Is_Merge: command === 'code',\r\n        ExportTeamTaskModel: TeamTaskModel\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '导出成功',\r\n            type: 'success'\r\n          })\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.Loading = false\r\n      })\r\n    },\r\n    // 打印\r\n    printSelected(command, row) {\r\n      const params = {\r\n        Task_Code: row.Task_Code,\r\n        Project_Name: row.Project_Name,\r\n        Area_Name: row.Area_Name,\r\n        InstallUnit_Name: row.InstallUnit_Name,\r\n        Schduling_Code: row.Schduling_Code,\r\n        Finish_Date: row.Finish_Date,\r\n        Order_Date: row.Order_Date,\r\n        Task_Finish_Date: row.Task_Finish_Date,\r\n        Process_Finish_Date: row.Process_Finish_Date,\r\n        Working_Team_Name: row.Working_Team_Name,\r\n        Working_Process_Name: row.Working_Process_Name,\r\n        Working_Team_Id: row.Working_Team_Id\r\n      }\r\n\r\n      this.$router.push({\r\n        name: 'PROTaskListDetailPrint',\r\n        query: {\r\n          type: this.pageType,\r\n          command: command,\r\n          pg_redirect: this.$route.name,\r\n          other: encodeURIComponent(JSON.stringify(params))\r\n        }\r\n      })\r\n    },\r\n\r\n    handleView(row) {\r\n      const {\r\n        Task_Code,\r\n        Project_Name,\r\n        Area_Name,\r\n        InstallUnit_Name,\r\n        Schduling_Code,\r\n        Finish_Date,\r\n        Order_Date,\r\n        Task_Finish_Date,\r\n        Process_Finish_Date,\r\n        Working_Team_Name,\r\n        Working_Process_Name\r\n      } = row\r\n      this.$router.push({\r\n        name: 'PROTaskListDetail',\r\n        query: {\r\n          id: row.Task_Code,\r\n          type: this.pageType,\r\n          tid: row.Working_Team_Id,\r\n          pg_redirect: this.$route.name,\r\n          // pg_redirect: this.isPart ? 'PROPartTaskList' : 'PROComTaskList',\r\n          other: encodeURIComponent(JSON.stringify({\r\n            Task_Code,\r\n            Project_Name,\r\n            Area_Name,\r\n            InstallUnit_Name,\r\n            Schduling_Code: Schduling_Code,\r\n            Finish_Date: Finish_Date,\r\n            Process_Finish_Date: Process_Finish_Date,\r\n            Order_Date: Order_Date,\r\n            Finish_Date2: Task_Finish_Date,\r\n            Working_Team_Name: Working_Team_Name,\r\n            Working_Process_Name: Working_Process_Name\r\n          }))\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.el-divider{\r\n  margin:0 0 10px;\r\n}\r\n.btn-wrapper{\r\n  margin-bottom: 10px;\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  & > button {\r\n    margin-right: 10px;\r\n  }\r\n  & > div {\r\n    margin-right: 10px;\r\n  }\r\n}\r\n.cs-z-page-main-content{\r\n  box-shadow: unset;\r\n}\r\n</style>\r\n"]}]}
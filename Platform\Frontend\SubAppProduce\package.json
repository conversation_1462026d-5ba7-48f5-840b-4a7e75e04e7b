{"name": "produce", "version": "4.4.0", "description": "A magical vue admin. An out-of-box UI solution for enterprise applications. Newest development stack of vue. Lots of awesome features", "author": "Pan <<EMAIL>>", "scripts": {"dev": "cross-env NODE_OPTIONS=--max_old_space_size=8192 vue-cli-service serve", "lint": "eslint --ext .js,.vue src", "build:prod": "vue-cli-service build ", "build:test": "vue-cli-service build --mode test", "build:stage": "vue-cli-service build --mode staging", "build:release": "vue-cli-service build --mode release", "build:analyzer": "vue-cli-service build --report", "preview": "node build/index.js --preview", "new": "plop", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"@components/BimtkUI": "^1.1.15", "@components/gantt": "^7.1.0", "@jiaminghi/data-view": "^2.10.0", "@liveqing/liveplayer": "^2.3.1", "ali-oss": "^6.16.0", "and": "^0.0.3", "axios": "^1.6.7", "clipboard": "^2.0.6", "copy-webpack-plugin": "^6.0.3", "core-js": "^3.6.5", "crypto-js": "^4.0.0", "decimal.js": "^10.5.0", "downloadjs": "^1.4.7", "echarts": "^5.2.2", "el-tree-select": "^3.1.12", "el-tree-transfer": "^2.4.7", "element-china-area-data": "^5.0.2", "element-ui": "^2.15.9", "exceljs": "^4.4.0", "exports-loader": "^2.0.0", "file-saver": "^2.0.5", "fuse.js": "3.4.4", "imports-loader": "^2.0.0", "js-cookie": "2.2.0", "jsencrypt": "^3.2.1", "jsplumb": "^2.15.5", "jszip": "^3.6.0", "less-loader": "^8.0.0", "md5": "^2.3.0", "moment": "^2.29.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "numeral": "^2.0.6", "pako": "^2.0.3", "path-to-regexp": "2.4.0", "portal-vue": "^2.1.6", "qrcode": "^1.5.1", "qrcode.vue": "^1.7.0", "qrcodejs2": "0.0.2", "qs": "^6.10.1", "screenfull": "^5.1.0", "script-loader": "0.7.2", "tinymce": "^5.7.0", "uuid": "^8.3.2", "video.js": "^7.20.3", "vue": "2.6.10", "vue-amap": "^0.5.10", "vue-baidu-map": "^0.21.22", "vue-calendar-component": "^2.8.2", "vue-clipboard2": "^0.3.3", "vue-contextmenu": "^1.5.10", "vue-count-to": "1.0.13", "vue-draggable-resizable-gorkys": "^2.4.4", "vue-echarts": "^6.0.0-rc.4", "vue-fullscreen": "^2.5.1", "vue-json-viewer": "^2.2.18", "vue-jsonp": "^2.0.0", "vue-pdf": "^4.3.0", "vue-plugin-hiprint": "0.0.56", "vue-print-nb": "^1.7.4", "vue-router": "^3.5.1", "vue-seamless-scroll": "^1.1.23", "vue-ueditor-wrap": "^2.5.6", "vue-video-player": "^5.0.2", "vue-virtual-scroll-list": "^2.3.4", "vuedraggable": "^2.24.3", "vuex": "3.1.0", "vxe-table": "3.6.17", "vxe-table-plugin-export-xlsx": "^2.3.1", "webpack": "^4.44.2", "xe-utils": "^3.5.6", "xlsx": "^0.17.0"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/composition-api": "^1.0.0-rc.6", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "babel-plugin-import": "^1.13.3", "chalk": "2.4.2", "chokidar": "2.1.5", "compression-webpack-plugin": "^6.0.5", "connect": "3.6.6", "cross-env": "^7.0.3", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "husky": "1.3.1", "import-three-examples": "^2.2.3", "lint-staged": "8.1.5", "mockjs": "1.0.1-beta3", "plop": "2.3.0", "runjs": "4.3.2", "sass": "1.26.2", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "speed-measure-webpack-plugin": "^1.5.0", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "thread-loader": "^3.0.4", "three": "^0.128.0", "vue-template-compiler": "2.6.10", "worker-loader": "^3.0.8"}, "browserslist": ["> 1%", "last 2 versions"], "bugs": {"url": "https://github.com/PanJiaChen/vue-element-admin/issues"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "license": "MIT", "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "repository": {"type": "git", "url": "git+http://www.zcloudbim.com:25030/yingzy/common-admin.git"}, "volta": {"node": "18.18.0"}}
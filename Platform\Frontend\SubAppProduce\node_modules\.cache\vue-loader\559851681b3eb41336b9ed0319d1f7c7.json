{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\material-inventory-reconfig\\raw-outbound-new\\components\\ReceiveTb.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\material-inventory-reconfig\\raw-outbound-new\\components\\ReceiveTb.vue", "mtime": 1757926768435}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRUYWJsZUNvbmZpZyB9IGZyb20gJ0Avdmlld3MvUFJPL21hdGVyaWFsLXJlY2VpcHQtbWFuYWdlbWVudC91dGlscycNCmltcG9ydCB7IGZvcm1hdE51bSB9IGZyb20gJ0Avdmlld3MvUFJPL21hdGVyaWFsLWludmVudG9yeS1yZWNvbmZpZy9yYXctb3V0Ym91bmQtbmV3L3V0aWxzJw0KaW1wb3J0IFdhcmVob3VzZSBmcm9tICdAL3ZpZXdzL1BSTy9tYXRlcmlhbC1pbnZlbnRvcnktcmVjb25maWcvcmF3LW91dGJvdW5kLW5ldy9jb21wb25lbnRzL1dhcmVob3VzZS52dWUnDQppbXBvcnQgQmF0Y2hFZGl0IGZyb20gJy4vQmF0Y2hFZGl0LnZ1ZScNCmltcG9ydCBTZWxlY3RQcm9qZWN0IGZyb20gJ0AvY29tcG9uZW50cy9TZWxlY3QvU2VsZWN0UHJvamVjdC9pbmRleC52dWUnDQppbXBvcnQgeyBHZXRQcm9qZWN0UGFnZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vcHJvamVjdCcNCmltcG9ydCB7IENPVU5UX0RFQ0lNQUwsIE91dEJPVU5EX0RFVEFJTF9TVU1NQVJZX0ZJRUxEUywgV0VJR0hUX0RFQ0lNQUwgfSBmcm9tICdAL3ZpZXdzL1BSTy9tYXRlcmlhbF92NC9jb25maWcnDQppbXBvcnQgRHluYW1pY1RhYmxlRmllbGRzIGZyb20gJ0AvY29tcG9uZW50cy9EeW5hbWljVGFibGVGaWVsZHMvaW5kZXgudnVlJw0KaW1wb3J0IFBpY2tTZWxlY3QgZnJvbSAnQC92aWV3cy9QUk8vbWF0ZXJpYWxfdjQvcGlja0FwcGx5L3NlbGVjdC52dWUnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgY29tcG9uZW50czogeyBQaWNrU2VsZWN0LCBEeW5hbWljVGFibGVGaWVsZHMsIFNlbGVjdFByb2plY3QsIFdhcmVob3VzZSwgQmF0Y2hFZGl0IH0sDQogIGZpbHRlcnM6IHsNCiAgICBnZXRGb3JtYXROdW0odmFsdWUsIG51bSkgew0KICAgICAgcmV0dXJuIGZvcm1hdE51bSh2YWx1ZSwgbnVtKQ0KICAgIH0NCiAgfSwNCiAgcHJvcHM6IHsNCiAgICBpc1ZpZXc6IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiBmYWxzZQ0KICAgIH0sDQogICAgaXNSZXR1cm46IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiBmYWxzZQ0KICAgIH0sDQogICAgaXNPdXRzb3VyY2luZzogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBDT1VOVF9ERUNJTUFMLA0KICAgICAgV0VJR0hUX0RFQ0lNQUwsDQogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGVuYWJsZWRFZGl0OiBmYWxzZSwNCiAgICAgIGRlbGV0ZUxvYWRpbmc6IGZhbHNlLA0KICAgICAgdGJMb2FkaW5nOiBmYWxzZSwNCiAgICAgIGN1cnJlbnRUYkRhdGE6IFtdLA0KICAgICAgY29sdW1uczogW10sDQogICAgICBtdWx0aXBsZVNlbGVjdGlvbjogW10sDQogICAgICBiaWdUeXBlRGF0YTogMSwNCiAgICAgIHZhbGlkUnVsZXM6IHsNCiAgICAgICAgT3V0U3RvcmVDb3VudDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHR5cGU6ICdudW1iZXInLCBtaW46IDAsIG1lc3NhZ2U6ICfor7fovpPlhaUnIH0NCiAgICAgICAgXSwNCiAgICAgICAgUGlja19TeXNfUHJvamVjdF9JZDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHR5cGU6ICdzdHJpbmcnLCBtaW46IDAsIG1lc3NhZ2U6ICfor7fpgInmi6knIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIHByb2plY3RPcHRpb25zOiBbXSwNCiAgICAgIGJhdGNoRGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBiYXRjaFByb2plY3RJZDogJycsDQogICAgICBleGNsdWRlZFJvdXRlczogWydQUk9SYXdNYXRlcmlhbE91dGJvdW5kVmlldyddLA0KICAgICAgZ3JpZENvZGU6ICdQUk9SYXdSZWNlaXZlT3V0TGlzdCcsDQogICAgICByZW5kZXJDb21wb25lbnQ6IHRydWUNCg0KICAgIH0NCiAgfSwNCiAgYXN5bmMgbW91bnRlZCgpIHsNCiAgICB0aGlzLmdldFByb2plY3QoKQ0KICAgIHRoaXMudGJEYXRhID0gW10NCiAgICBhd2FpdCB0aGlzLmluaXQoKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLy8g6YeN5paw5riy5p+TdnV4LXRhYmxlDQogICAgZm9yY2VSZXJlbmRlcigpIHsNCiAgICAgIC8vIOS7jiBET00g5Lit5Yig6ZmkIG15LWNvbXBvbmVudCDnu4Tku7YNCiAgICAgIHRoaXMucmVuZGVyQ29tcG9uZW50ID0gZmFsc2UNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgLy8g5ZyoIERPTSDkuK3mt7vliqAgbXktY29tcG9uZW50IOe7hOS7tg0KICAgICAgICB0aGlzLnJlbmRlckNvbXBvbmVudCA9IHRydWUNCiAgICAgIH0pDQogICAgfSwNCiAgICBjbG9zZUJhdGNoRGlhbG9nKCkgew0KICAgICAgdGhpcy5iYXRjaFByb2plY3RJZCA9ICcnDQogICAgICB0aGlzLmJhdGNoRGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgfSwNCiAgICBiYXRjaENoYW5nZVByb2plY3QoKSB7DQogICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uLmZvckVhY2goKGVsZW1lbnQsIGlkeCkgPT4gew0KICAgICAgICBjb25zdCBpdGVtID0gdGhpcy50YkRhdGEuZmluZCgodikgPT4gdi51dWlkID09PSBlbGVtZW50LnV1aWQpDQogICAgICAgIGNvbnN0IGkgPSB0aGlzLnRiRGF0YS5maW5kSW5kZXgoKHYpID0+IHYudXVpZCA9PT0gZWxlbWVudC51dWlkKQ0KICAgICAgICBjb25zb2xlLmxvZyh7IGkgfSkNCiAgICAgICAgLy8g5pu05paw6aG555uuSUTlkozpobnnm67lkI3np7ANCiAgICAgICAgaXRlbS5QaWNrX1N5c19Qcm9qZWN0X0lkID0gdGhpcy5iYXRjaFByb2plY3RJZA0KICAgICAgICBpdGVtLlBpY2tfUHJvamVjdF9OYW1lID0gdGhpcy5wcm9qZWN0T3B0aW9ucy5maW5kKHByb2ogPT4gcHJvai5TeXNfUHJvamVjdF9JZCA9PT0gdGhpcy5iYXRjaFByb2plY3RJZCk/LlNob3J0X05hbWUNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMudGJEYXRhLCBpLCBpdGVtKQ0KICAgICAgfSkNCiAgICAgIC8vIOabtOaWsGN1cnJlbnRUYkRhdGHku6XliLfmlrDop4blm74NCiAgICAgIHRoaXMubWVyZ2VEYXRhKCkNCiAgICAgIHRoaXMuY2xvc2VCYXRjaERpYWxvZygpDQogICAgfSwNCiAgICAvKioNCiAgICAgKiDojrflj5bmiYDlsZ7pobnnm64NCiAgICAgKi8NCiAgICBnZXRQcm9qZWN0KCkgew0KICAgICAgR2V0UHJvamVjdFBhZ2VMaXN0KHsNCiAgICAgICAgUGFnZTogMSwNCiAgICAgICAgUGFnZVNpemU6IC0xDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLnByb2plY3RPcHRpb25zID0gcmVzLkRhdGEuRGF0YQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZUNsb3NlKCkgew0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICB9LA0KICAgIGdldFRheFVuaXRQcmljZSh2YWwpIHsNCiAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24uZm9yRWFjaChyb3cgPT4gew0KICAgICAgICByb3cuVGF4VW5pdFByaWNlID0gdmFsDQogICAgICB9KQ0KICAgIH0sDQogICAgYXN5bmMgaW5pdCgpIHsNCiAgICAgIHRoaXMuZW5hYmxlZEVkaXQgPSAhdGhpcy5pc1ZpZXcNCiAgICAgIHRoaXMudGJMb2FkaW5nID0gdHJ1ZQ0KICAgICAgdGhpcy5jb2x1bW5zID0gYXdhaXQgZ2V0VGFibGVDb25maWcodGhpcy5ncmlkQ29kZSkNCiAgICAgIHRoaXMuY29sdW1ucyA9IHRoaXMuY29sdW1ucy5tYXAoaXRlbSA9PiB7DQogICAgICAgIGl0ZW0uU3R5bGUgPSBpdGVtLlN0eWxlID8gSlNPTi5wYXJzZShpdGVtLlN0eWxlKSA6ICcnDQogICAgICAgIHJldHVybiBpdGVtDQogICAgICB9KQ0KICAgICAgdGhpcy5mb3JjZVJlcmVuZGVyKCkNCiAgICAgIHRoaXMudGJMb2FkaW5nID0gZmFsc2UNCiAgICB9LA0KICAgIHNldFRiRGF0YShsaXN0LCBjaGVja092ZXIsIGluZm8pIHsNCiAgICAgIGxpc3QuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgdGhpcy5jaGVja0NvdW50KGl0ZW0sIGNoZWNrT3ZlcikNCiAgICAgICAgaXRlbS5QaWNrX1Byb2plY3RfTmFtZSA9IGl0ZW0uUHJvamVjdF9OYW1lIC8vIOm7mOiupOmihueUqOmhueebruS4uuaJgOWxnumhueebrg0KICAgICAgfSkNCiAgICAgIHRoaXMudGJEYXRhLnB1c2goLi4ubGlzdCkNCiAgICAgIHRoaXMuZmlsdGVyTWV0aG9kKCkNCiAgICAgIHRoaXMuJGVtaXQoJ3NldEluZm8nLCBpbmZvKQ0KICAgIH0sDQogICAgY2hhbmdlUHJvamVjdChlLCBpdGVtKSB7DQogICAgICBpdGVtLlBpY2tfUHJvamVjdF9OYW1lID0gdGhpcy5wcm9qZWN0T3B0aW9ucy5maW5kKGl0ZW0gPT4gaXRlbS5TeXNfUHJvamVjdF9JZCA9PT0gZS52YWx1ZSk/LlNob3J0X05hbWUNCiAgICB9LA0KICAgIHRiU2VsZWN0Q2hhbmdlKGFycmF5KSB7DQogICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gYXJyYXkucmVjb3Jkcw0KICAgIH0sDQogICAgY2hlY2tXZWlnaHQocm93LCBjaGVja092ZXIgPSB0cnVlKSB7DQogICAgICBpZiAodGhpcy5leGNsdWRlZFJvdXRlcy5pbmNsdWRlcyh0aGlzLiRyb3V0ZS5uYW1lKSkgew0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIHJvdy5PdXRTdG9yZVdlaWdodCA9IChyb3cuVW5pdF9XZWlnaHQgKiByb3cuT3V0U3RvcmVDb3VudCkudG9GaXhlZChXRUlHSFRfREVDSU1BTCkgLyAxDQogICAgICBpZiAocm93Lk91dFN0b3JlV2VpZ2h0ID49IHJvdy5BdmFpbGFibGVXZWlnaHQgJiYgY2hlY2tPdmVyKSB7DQogICAgICAgIHJvdy5PdXRTdG9yZVdlaWdodCA9IHJvdy5BdmFpbGFibGVXZWlnaHQNCiAgICAgICAgcm93Lk91dFN0b3JlQ291bnQgPSByb3cuQXZhaWxhYmxlQ291bnQNCiAgICAgIH0NCiAgICB9LA0KICAgIGNoZWNrQ291bnQocm93LCBjaGVja092ZXIgPSB0cnVlKSB7DQogICAgICBpZiAocm93Lk91dFN0b3JlQ291bnQgPj0gcm93LkF2YWlsYWJsZUNvdW50ICYmIGNoZWNrT3Zlcikgew0KICAgICAgICByb3cuT3V0U3RvcmVDb3VudCA9IHJvdy5BdmFpbGFibGVDb3VudA0KICAgICAgICByb3cuT3V0U3RvcmVXZWlnaHQgPSByb3cuQXZhaWxhYmxlV2VpZ2h0DQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgdGhpcy5jaGVja1dlaWdodChyb3csIGZhbHNlKQ0KICAgIH0sDQoNCiAgICBnZXRUYkRhdGEoKSB7DQogICAgICByZXR1cm4gdGhpcy50YkRhdGENCiAgICB9LA0KDQogICAgb3BlbkFkZERpYWxvZygpIHsNCiAgICAgIHRoaXMuJGVtaXQoJ29wZW5BZGREaWFsb2cnKQ0KICAgIH0sDQogICAgaGFuZGxlRGVsZXRlKCkgew0KICAgICAgdGhpcy5kZWxldGVMb2FkaW5nID0gdHJ1ZQ0KICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgIGNvbnN0IGlkcyA9IHRoaXMubXVsdGlwbGVTZWxlY3Rpb24ubWFwKHYgPT4gdi51dWlkKQ0KICAgICAgICB0aGlzLnRiRGF0YSA9IHRoaXMudGJEYXRhLmZpbHRlcihyb3cgPT4gIWlkcy5pbmNsdWRlcyhyb3cudXVpZCkpDQogICAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24gPSBbXQ0KICAgICAgICBjb25zb2xlLmxvZyh0aGlzLnRiRGF0YSkNCiAgICAgICAgdGhpcy5kZWxldGVMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgdGhpcy5maWx0ZXJNZXRob2QoKQ0KICAgICAgfSwgMCkNCiAgICB9LA0KICAgIEJ1bGtFZGl0KCkgew0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgIH0sDQogICAgY2xlYXJUYigpIHsNCiAgICAgIHRoaXMudGJEYXRhID0gW10NCiAgICAgIHRoaXMuZmlsdGVyTWV0aG9kKCkNCiAgICB9LA0KICAgIG1lcmdlRGF0YSgpIHsNCiAgICAgIC8vIOS9v+eUqOa3seaLt+i0neehruS/neWTjeW6lOW8j+abtOaWsA0KICAgICAgdGhpcy5jdXJyZW50VGJEYXRhID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLnRiRGF0YSkpDQogICAgfSwNCiAgICBmaWx0ZXJNZXRob2QoZmlsdGVySW5mbykgew0KICAgICAgY29uc3QgZmlsdGVyS2V5cyA9IChhcnJheSwgZmlsdGVycykgPT4gew0KICAgICAgICByZXR1cm4gYXJyYXkuZmlsdGVyKGl0ZW0gPT4gew0KICAgICAgICAgIGxldCBmbGFnID0gdHJ1ZQ0KICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZmlsdGVycy5sZW5ndGg7IGkrKykgew0KICAgICAgICAgICAgY29uc3QgZWxlbWVudCA9IGZpbHRlcnNbaV0NCiAgICAgICAgICAgIGxldCByb3dMYWJlbCA9IGl0ZW1bZWxlbWVudC5rZXldIHx8ICcnDQogICAgICAgICAgICBpZiAoZWxlbWVudC52YWx1ZSA9PT0gJycpIHsNCiAgICAgICAgICAgICAgZmxhZyA9IHRydWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGlmICh0eXBlb2Ygcm93TGFiZWwgIT09ICdzdHJpbmcnKSB7DQogICAgICAgICAgICAgIHJvd0xhYmVsID0gcm93TGFiZWwudG9TdHJpbmcoKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgaWYgKHJvd0xhYmVsLmluY2x1ZGVzKGVsZW1lbnQudmFsdWUpKSB7DQogICAgICAgICAgICAgIGZsYWcgPSB0cnVlDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBmbGFnID0gZmFsc2UNCiAgICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgICAgcmV0dXJuIGZsYWcNCiAgICAgICAgfSkNCiAgICAgIH0NCg0KICAgICAgaWYgKCFmaWx0ZXJJbmZvKSB7DQogICAgICAgIHRoaXMuY3VycmVudFRiRGF0YSA9IHRoaXMudGJEYXRhDQogICAgICB9IGVsc2Ugew0KICAgICAgICBjb25zdCBmaWx0ZXJzID0gW10NCiAgICAgICAgZm9yIChjb25zdCBmaWx0ZXJJbmZvS2V5IGluIGZpbHRlckluZm8pIHsNCiAgICAgICAgICBmaWx0ZXJzLnB1c2goew0KICAgICAgICAgICAga2V5OiBmaWx0ZXJJbmZvS2V5LA0KICAgICAgICAgICAgdmFsdWU6IGZpbHRlckluZm9bZmlsdGVySW5mb0tleV0NCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICAgIGNvbnNvbGUubG9nKCdmaWx0ZXJJbmZvS2V5JywgZmlsdGVycykNCg0KICAgICAgICB0aGlzLmN1cnJlbnRUYkRhdGEgPSBmaWx0ZXJLZXlzKHRoaXMudGJEYXRhLCBmaWx0ZXJzKQ0KICAgICAgfQ0KICAgICAgY29uc29sZS5sb2coJ3RoaXMuY3VycmVudFRiRGF0YScsIHRoaXMuY3VycmVudFRiRGF0YSkNCiAgICB9LA0KICAgIGNoZWNrVmFsaWRhdGUodGJEYXRhKSB7DQogICAgICBpZiAoIXRiRGF0YS5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogJ+aVsOaNruS4jeiDveS4uuepuicsDQogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgIH0pDQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgc3RhdHVzOiBmYWxzZQ0KICAgICAgICB9DQogICAgICB9DQogICAgICBjb25zdCB0YiA9IHRiRGF0YS5maWx0ZXIoaXRlbSA9PiBpdGVtLk91dFN0b3JlQ291bnQgPiAwKQ0KICAgICAgaWYgKCF0Yi5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogJ+WHuuW6k+aVsOmHj+S4jeiDveS4ujAnLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KQ0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIHN0YXR1czogZmFsc2UNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5ZCI6K6hDQogICAgZm9vdGVyTWV0aG9kKHsgY29sdW1ucywgZGF0YSB9KSB7DQogICAgICBjb25zdCBmb290ZXJEYXRhID0gWw0KICAgICAgICBjb2x1bW5zLm1hcCgoY29sdW1uLCBpbmRleCkgPT4gew0KICAgICAgICAgIGlmIChPdXRCT1VORF9ERVRBSUxfU1VNTUFSWV9GSUVMRFMuaW5jbHVkZXMoY29sdW1uLmZpZWxkKSkgew0KICAgICAgICAgICAgcmV0dXJuIHRoaXMuc3VtTnVtKGRhdGEsIGNvbHVtbi5maWVsZCwgNSkNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKGluZGV4ID09PSAwKSB7DQogICAgICAgICAgICByZXR1cm4gJ+WQiOiuoScNCiAgICAgICAgICB9DQogICAgICAgICAgcmV0dXJuIG51bGwNCiAgICAgICAgfSkNCiAgICAgIF0NCiAgICAgIHJldHVybiBmb290ZXJEYXRhDQogICAgfSwNCiAgICAvLyDov5vooYzlkIjorqENCiAgICBzdW1OdW0oY29zdEZvcm0sIGZpZWxkLCBkaWdpdCkgew0KICAgICAgbGV0IHRvdGFsID0gMA0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjb3N0Rm9ybS5sZW5ndGg7IGkrKykgew0KICAgICAgICB0b3RhbCArPSBOdW1iZXIoY29zdEZvcm1baV1bZmllbGRdKSB8fCAwDQogICAgICB9DQogICAgICByZXR1cm4gdG90YWwudG9GaXhlZChkaWdpdCkgLyAxDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["ReceiveTb.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ReceiveTb.vue", "sourceRoot": "src/views/PRO/material-inventory-reconfig/raw-outbound-new/components", "sourcesContent": ["<template>\r\n  <div class=\"receive-tb\">\r\n    <div v-if=\"!isView&&!isReturn\" class=\"toolbar-container\" style=\"margin-bottom: 8px\">\r\n      <vxe-toolbar>\r\n        <template #buttons>\r\n          <el-button type=\"primary\" @click=\"openAddDialog(null)\">新增</el-button>\r\n          <el-button\r\n            :disabled=\"!multipleSelection.length\"\r\n            type=\"danger\"\r\n            :loading=\"deleteLoading\"\r\n            @click=\"handleDelete\"\r\n          >删除\r\n          </el-button>\r\n          <el-button type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"batchDialogVisible = true\">批量编辑领用项目</el-button>\r\n          <PickSelect style=\"margin-left: 10px\" :selected-list=\"currentTbData\" :material-type=\"0\" @addList=\"setTbData\" />\r\n          <el-button v-if=\"isOutsourcing\" style=\"margin-left: 10px\" :disabled=\"!multipleSelection.length\" type=\"primary\" @click=\"BulkEdit\">整车含税单价</el-button>\r\n          <DynamicTableFields\r\n            style=\"margin-left: auto\"\r\n            title=\"表格配置\"\r\n            :table-config-code=\"gridCode\"\r\n            @updateColumn=\"init\"\r\n          />\r\n        </template>\r\n      </vxe-toolbar>\r\n    </div>\r\n    <div class=\"tb-x\">\r\n      <vxe-table\r\n        v-if=\"renderComponent\"\r\n        ref=\"xTable\"\r\n        :empty-render=\"{name: 'NotData'}\"\r\n        show-header-overflow\r\n        class=\"cs-vxe-table\"\r\n        :row-config=\"{ isCurrent: true, isHover: true}\"\r\n        align=\"left\"\r\n        height=\"auto\"\r\n        show-overflow\r\n        auto-resize\r\n        :loading=\"tbLoading\"\r\n        stripe\r\n        size=\"medium\"\r\n        :data=\"currentTbData\"\r\n        resizable\r\n        :edit-config=\"{\r\n          enabled:enabledEdit,\r\n          trigger: 'click',\r\n          mode: 'cell',\r\n          showIcon: !isView, showStatus: true\r\n        }\"\r\n        :edit-rules=\"validRules\"\r\n        :tooltip-config=\"{ enterable: true }\"\r\n        show-footer\r\n        :footer-method=\"footerMethod\"\r\n        @checkbox-all=\"tbSelectChange\"\r\n        @checkbox-change=\"tbSelectChange\"\r\n      >\r\n        <vxe-column v-if=\"!isView\" fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n        <template v-for=\"item in columns\">\r\n          <vxe-column\r\n            :key=\"item.Code\"\r\n            :fixed=\"item.Is_Frozen ? (item.Frozen_Dirction || 'left') : ''\"\r\n            show-overflow=\"tooltip\"\r\n            :align=\"item.Align\"\r\n            :field=\"item.Code\"\r\n            :visible=\"item.Is_Display\"\r\n            :title=\"item.Display_Name\"\r\n            :min-width=\"item.Width\"\r\n            :edit-render=\"item.Is_Edit ? {} : null\"\r\n            :sortable=\"item.Is_Sort\"\r\n          >\r\n            <template v-if=\"item.Style.tips\" #header>\r\n              <span>{{ item.Display_Name }}</span>\r\n              <el-tooltip class=\"item\" effect=\"dark\">\r\n                <div slot=\"content\" v-html=\"item.Style.tips\" />\r\n                <i class=\"el-icon-question\" style=\"cursor:pointer;font-size: 16px\" />\r\n              </el-tooltip>\r\n            </template>\r\n            <template #default=\"{ row }\">\r\n              <span v-if=\"item.Code === 'Warehouse_Location'\">\r\n                {{ row.WarehouseName }}/{{ row.LocationName }}\r\n              </span>\r\n              <span v-else-if=\"item.Code === 'InStoreDate'\">\r\n                {{ row.InStoreDate | timeFormat }}\r\n              </span>\r\n              <template v-else-if=\"item.Code === 'RawName'\">\r\n                <div>\r\n                  <el-tag v-if=\"row.Is_PartA\" type=\"danger\" effect=\"dark\" size=\"mini\">甲供</el-tag>\r\n                  <el-tag v-if=\"row.Is_Replace_Purchase\" type=\"success\" effect=\"dark\" size=\"mini\">代购</el-tag>\r\n                  <el-tag v-if=\"row.Is_Surplus\" type=\"warning\" effect=\"dark\" size=\"mini\">余料</el-tag>\r\n                  {{ row.RawName }}</div>\r\n              </template>\r\n              <template v-else-if=\"item.Code === 'OutStoreWeight'\">\r\n                {{ row.OutStoreWeight | getFormatNum(WEIGHT_DECIMAL) }}\r\n              </template>\r\n              <template v-else-if=\"item.Code === 'Voucher_Weight'\">\r\n                {{ row.Voucher_Weight | getFormatNum(3) }}\r\n              </template>\r\n              <span v-else> {{ row[item.Code] | displayValue }}</span>\r\n            </template>\r\n            <template v-if=\"item.Is_Edit\" #edit=\"{ row }\">\r\n              <div v-if=\"item.Code === 'Actual_Thick'\">\r\n                <el-input v-model=\"row[item.Code]\" type=\"text\" @change=\"$emit('updateRow')\" />\r\n              </div>\r\n              <div v-else-if=\"item.Code === 'Width'\">\r\n                <el-input\r\n                  v-model=\"row[item.Code]\"\r\n                  :min=\"0\"\r\n                  type=\"number\"\r\n                  @change=\"checkWeight(row)\"\r\n                />\r\n              </div>\r\n              <div v-else-if=\"item.Code === 'Length'\">\r\n                <el-input\r\n                  v-model=\"row[item.Code]\"\r\n                  :min=\"0\"\r\n                  type=\"number\"\r\n                  @change=\"checkWeight(row)\"\r\n                />\r\n              </div>\r\n              <div v-else-if=\"item.Code === 'OutStoreCount'\">\r\n                <el-input\r\n                  v-model=\"row[item.Code]\"\r\n                  v-inp-num=\"{ toFixed: COUNT_DECIMAL, min: 0 }\"\r\n                  :min=\"0\"\r\n                  :disabled=\"!!row.PickSubId\"\r\n                  :max=\"row.AvailableCount\"\r\n                  @change=\"checkCount(row)\"\r\n                />\r\n              </div>\r\n              <div v-else-if=\"item.Code === 'OutStoreWeight'\">\r\n                <span> {{ row[item.Code] | displayValue }}</span>\r\n              </div>\r\n              <template v-else-if=\"item.Code === 'Pick_Project_Name'\">\r\n                <vxe-select\r\n                  v-model=\"row.Pick_Sys_Project_Id\"\r\n                  style=\"width: 100%\"\r\n                  placeholder=\"请选择\"\r\n                  transfer\r\n                  clearable\r\n                  filterable\r\n                  :disabled=\"!!row.PickSubId\"\r\n                  @change=\"(e)=>changeProject(e,row)\"\r\n                >\r\n                  <vxe-option\r\n                    v-for=\"item in projectOptions\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Short_Name\"\r\n                    :value=\"item.Sys_Project_Id\"\r\n                  />\r\n                </vxe-select>\r\n              </template>\r\n              <div v-else>\r\n                <el-input v-model=\"row[item.Code]\" type=\"text\" @blur=\"$emit('updateRow')\" />\r\n              </div>\r\n            </template>\r\n          </vxe-column>\r\n        </template>\r\n      </vxe-table>\r\n    </div>\r\n    <footer v-if=\"!isView\">\r\n      <div class=\"data-info\">\r\n        <el-tag v-if=\"!isReturn\" size=\"medium\" class=\"info-x\">已选{{ multipleSelection.length }}条数据 </el-tag>\r\n      </div>\r\n      <div>\r\n        <slot />\r\n      </div>\r\n    </footer>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      title=\"选择含税单价\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"30%\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <BatchEdit v-if=\"dialogVisible\" @close=\"handleClose\" @taxUnitPrice=\"getTaxUnitPrice\" />\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      title=\"批量编辑领用项目\"\r\n      :visible.sync=\"batchDialogVisible\"\r\n      top=\"10vh\"\r\n      width=\"350px\"\r\n      @close=\"closeBatchDialog\"\r\n    >\r\n      <el-select\r\n        v-model=\"batchProjectId\"\r\n        style=\"width: 300px\"\r\n        placeholder=\"请选择\"\r\n        clearable\r\n        filterable\r\n      >\r\n        <el-option\r\n          v-for=\"item in projectOptions\"\r\n          :key=\"item.Id\"\r\n          :label=\"item.Short_Name\"\r\n          :value=\"item.Sys_Project_Id\"\r\n        />\r\n      </el-select>\r\n      <p style=\"margin: 20px\">\r\n        <i>注：仅能批量编辑公共库存的领用项目</i>\r\n      </p>\r\n      <div style=\"text-align: right\">\r\n        <el-button @click=\"closeBatchDialog\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"batchChangeProject\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n\r\n</template>\r\n<script>\r\nimport { getTableConfig } from '@/views/PRO/material-receipt-management/utils'\r\nimport { formatNum } from '@/views/PRO/material-inventory-reconfig/raw-outbound-new/utils'\r\nimport Warehouse from '@/views/PRO/material-inventory-reconfig/raw-outbound-new/components/Warehouse.vue'\r\nimport BatchEdit from './BatchEdit.vue'\r\nimport SelectProject from '@/components/Select/SelectProject/index.vue'\r\nimport { GetProjectPageList } from '@/api/PRO/project'\r\nimport { COUNT_DECIMAL, OutBOUND_DETAIL_SUMMARY_FIELDS, WEIGHT_DECIMAL } from '@/views/PRO/material_v4/config'\r\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\r\nimport PickSelect from '@/views/PRO/material_v4/pickApply/select.vue'\r\n\r\nexport default {\r\n  components: { PickSelect, DynamicTableFields, SelectProject, Warehouse, BatchEdit },\r\n  filters: {\r\n    getFormatNum(value, num) {\r\n      return formatNum(value, num)\r\n    }\r\n  },\r\n  props: {\r\n    isView: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    isReturn: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    isOutsourcing: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      COUNT_DECIMAL,\r\n      WEIGHT_DECIMAL,\r\n      dialogVisible: false,\r\n      enabledEdit: false,\r\n      deleteLoading: false,\r\n      tbLoading: false,\r\n      currentTbData: [],\r\n      columns: [],\r\n      multipleSelection: [],\r\n      bigTypeData: 1,\r\n      validRules: {\r\n        OutStoreCount: [\r\n          { required: true, type: 'number', min: 0, message: '请输入' }\r\n        ],\r\n        Pick_Sys_Project_Id: [\r\n          { required: true, type: 'string', min: 0, message: '请选择' }\r\n        ]\r\n      },\r\n      projectOptions: [],\r\n      batchDialogVisible: false,\r\n      batchProjectId: '',\r\n      excludedRoutes: ['PRORawMaterialOutboundView'],\r\n      gridCode: 'PRORawReceiveOutList',\r\n      renderComponent: true\r\n\r\n    }\r\n  },\r\n  async mounted() {\r\n    this.getProject()\r\n    this.tbData = []\r\n    await this.init()\r\n  },\r\n  methods: {\r\n    // 重新渲染vux-table\r\n    forceRerender() {\r\n      // 从 DOM 中删除 my-component 组件\r\n      this.renderComponent = false\r\n      this.$nextTick(() => {\r\n        // 在 DOM 中添加 my-component 组件\r\n        this.renderComponent = true\r\n      })\r\n    },\r\n    closeBatchDialog() {\r\n      this.batchProjectId = ''\r\n      this.batchDialogVisible = false\r\n    },\r\n    batchChangeProject() {\r\n      this.multipleSelection.forEach((element, idx) => {\r\n        const item = this.tbData.find((v) => v.uuid === element.uuid)\r\n        const i = this.tbData.findIndex((v) => v.uuid === element.uuid)\r\n        console.log({ i })\r\n        // 更新项目ID和项目名称\r\n        item.Pick_Sys_Project_Id = this.batchProjectId\r\n        item.Pick_Project_Name = this.projectOptions.find(proj => proj.Sys_Project_Id === this.batchProjectId)?.Short_Name\r\n        this.$set(this.tbData, i, item)\r\n      })\r\n      // 更新currentTbData以刷新视图\r\n      this.mergeData()\r\n      this.closeBatchDialog()\r\n    },\r\n    /**\r\n     * 获取所属项目\r\n     */\r\n    getProject() {\r\n      GetProjectPageList({\r\n        Page: 1,\r\n        PageSize: -1\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.projectOptions = res.Data.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    getTaxUnitPrice(val) {\r\n      this.multipleSelection.forEach(row => {\r\n        row.TaxUnitPrice = val\r\n      })\r\n    },\r\n    async init() {\r\n      this.enabledEdit = !this.isView\r\n      this.tbLoading = true\r\n      this.columns = await getTableConfig(this.gridCode)\r\n      this.columns = this.columns.map(item => {\r\n        item.Style = item.Style ? JSON.parse(item.Style) : ''\r\n        return item\r\n      })\r\n      this.forceRerender()\r\n      this.tbLoading = false\r\n    },\r\n    setTbData(list, checkOver, info) {\r\n      list.forEach(item => {\r\n        this.checkCount(item, checkOver)\r\n        item.Pick_Project_Name = item.Project_Name // 默认领用项目为所属项目\r\n      })\r\n      this.tbData.push(...list)\r\n      this.filterMethod()\r\n      this.$emit('setInfo', info)\r\n    },\r\n    changeProject(e, item) {\r\n      item.Pick_Project_Name = this.projectOptions.find(item => item.Sys_Project_Id === e.value)?.Short_Name\r\n    },\r\n    tbSelectChange(array) {\r\n      this.multipleSelection = array.records\r\n    },\r\n    checkWeight(row, checkOver = true) {\r\n      if (this.excludedRoutes.includes(this.$route.name)) {\r\n        return\r\n      }\r\n      row.OutStoreWeight = (row.Unit_Weight * row.OutStoreCount).toFixed(WEIGHT_DECIMAL) / 1\r\n      if (row.OutStoreWeight >= row.AvailableWeight && checkOver) {\r\n        row.OutStoreWeight = row.AvailableWeight\r\n        row.OutStoreCount = row.AvailableCount\r\n      }\r\n    },\r\n    checkCount(row, checkOver = true) {\r\n      if (row.OutStoreCount >= row.AvailableCount && checkOver) {\r\n        row.OutStoreCount = row.AvailableCount\r\n        row.OutStoreWeight = row.AvailableWeight\r\n        return\r\n      }\r\n      this.checkWeight(row, false)\r\n    },\r\n\r\n    getTbData() {\r\n      return this.tbData\r\n    },\r\n\r\n    openAddDialog() {\r\n      this.$emit('openAddDialog')\r\n    },\r\n    handleDelete() {\r\n      this.deleteLoading = true\r\n      setTimeout(() => {\r\n        const ids = this.multipleSelection.map(v => v.uuid)\r\n        this.tbData = this.tbData.filter(row => !ids.includes(row.uuid))\r\n        this.multipleSelection = []\r\n        console.log(this.tbData)\r\n        this.deleteLoading = false\r\n        this.filterMethod()\r\n      }, 0)\r\n    },\r\n    BulkEdit() {\r\n      this.dialogVisible = true\r\n    },\r\n    clearTb() {\r\n      this.tbData = []\r\n      this.filterMethod()\r\n    },\r\n    mergeData() {\r\n      // 使用深拷贝确保响应式更新\r\n      this.currentTbData = JSON.parse(JSON.stringify(this.tbData))\r\n    },\r\n    filterMethod(filterInfo) {\r\n      const filterKeys = (array, filters) => {\r\n        return array.filter(item => {\r\n          let flag = true\r\n          for (let i = 0; i < filters.length; i++) {\r\n            const element = filters[i]\r\n            let rowLabel = item[element.key] || ''\r\n            if (element.value === '') {\r\n              flag = true\r\n            }\r\n            if (typeof rowLabel !== 'string') {\r\n              rowLabel = rowLabel.toString()\r\n            }\r\n            if (rowLabel.includes(element.value)) {\r\n              flag = true\r\n            } else {\r\n              flag = false\r\n              break\r\n            }\r\n          }\r\n          return flag\r\n        })\r\n      }\r\n\r\n      if (!filterInfo) {\r\n        this.currentTbData = this.tbData\r\n      } else {\r\n        const filters = []\r\n        for (const filterInfoKey in filterInfo) {\r\n          filters.push({\r\n            key: filterInfoKey,\r\n            value: filterInfo[filterInfoKey]\r\n          })\r\n        }\r\n        console.log('filterInfoKey', filters)\r\n\r\n        this.currentTbData = filterKeys(this.tbData, filters)\r\n      }\r\n      console.log('this.currentTbData', this.currentTbData)\r\n    },\r\n    checkValidate(tbData) {\r\n      if (!tbData.length) {\r\n        this.$message({\r\n          message: '数据不能为空',\r\n          type: 'warning'\r\n        })\r\n        return {\r\n          status: false\r\n        }\r\n      }\r\n      const tb = tbData.filter(item => item.OutStoreCount > 0)\r\n      if (!tb.length) {\r\n        this.$message({\r\n          message: '出库数量不能为0',\r\n          type: 'warning'\r\n        })\r\n        return {\r\n          status: false\r\n        }\r\n      }\r\n    },\r\n    // 合计\r\n    footerMethod({ columns, data }) {\r\n      const footerData = [\r\n        columns.map((column, index) => {\r\n          if (OutBOUND_DETAIL_SUMMARY_FIELDS.includes(column.field)) {\r\n            return this.sumNum(data, column.field, 5)\r\n          }\r\n          if (index === 0) {\r\n            return '合计'\r\n          }\r\n          return null\r\n        })\r\n      ]\r\n      return footerData\r\n    },\r\n    // 进行合计\r\n    sumNum(costForm, field, digit) {\r\n      let total = 0\r\n      for (let i = 0; i < costForm.length; i++) {\r\n        total += Number(costForm[i][field]) || 0\r\n      }\r\n      return total.toFixed(digit) / 1\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.receive-tb{\r\n height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .tb-x{\r\n    flex:1;\r\n    overflow: hidden;\r\n  }\r\n  footer {\r\n    margin-top: 16px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n  }\r\n}\r\n</style>\r\n"]}]}
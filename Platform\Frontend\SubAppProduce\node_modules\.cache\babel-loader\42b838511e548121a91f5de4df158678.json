{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\NodeDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\NodeDialog.vue", "mtime": 1757666764694}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["SaveNode", "GetEntityNode", "GetFactoryPeoplelist", "GetProcessCodeList", "data", "mode", "ProjectId", "Check_Object_Id", "Bom_Level", "form", "Node_Code", "Change_Check_Type", "Display_Name", "TC_UserId", "ZL_UserId", "Demand_Spot_Check_Rate", "undefined", "Requirement_Spot_Check_Rate", "TC_UserIds", "ZL_UserIds", "Check_Style", "Ts_Require_Time", "rules", "required", "message", "trigger", "Check_Type", "validator", "Check_Type_rules", "rules_Zl", "rules_Tc", "ZL_UserIds_Rules", "Check_ZL_UserIds", "TC_UserIds_Rules", "Check_TC_UserIds", "title", "editInfo", "QualityNodeList", "Name", "CheckTypeList", "Id", "UserList", "CheckStyleList", "computed", "Node_Code_Com", "mounted", "getFactoryPeoplelist", "methods", "rule", "value", "callback", "includes", "length", "Error", "SelectType", "item", "$forceUpdate", "console", "log", "removeType", "clearType", "val", "init", "checkType", "Code", "getEntityNode", "getCheckNode", "addCheckNode", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "_this$form", "others", "submit", "wrap", "_callee$", "_context", "prev", "next", "_objectWithoutProperties", "_excluded", "_objectSpread", "then", "res", "IsSucceed", "$message", "type", "$emit", "dialogData", "Message", "stop", "_this2", "Data", "Platform", "localStorage", "getItem", "changeNodeCode", "changeZLUser", "i", "changeTCUser", "_this3", "id", "push", "split", "handleSubmit", "_this4", "$refs", "validate", "valid"], "sources": ["src/views/PRO/factoryQuality/checkoutGroup/components/Dialog/NodeDialog.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"120px\">\n      <el-row>\n        <el-col :span=\"12\">\n          <el-form-item label=\"质检节点\" prop=\"Display_Name\">\n            <el-select\n              v-model=\"form.Display_Name\"\n              :disabled=\"Node_Code_Com\"\n              clearable\n              style=\"width: 100%\"\n              filterable\n              allow-create\n              placeholder=\"请输入质检节点\"\n              @change=\"changeNodeCode\"\n            >\n              <el-option\n                v-for=\"(item, index) in QualityNodeList\"\n                :key=\"index\"\n                :label=\"item.Name\"\n                :value=\"item.Name\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"专检类型\" prop=\"Change_Check_Type\">\n            <el-select\n              v-model=\"form.Change_Check_Type\"\n              style=\"width: 100%\"\n              placeholder=\"请选择专检类型\"\n              multiple\n              :disabled=\"Node_Code_Com\"\n              @change=\"SelectType\"\n              @remove-tag=\"removeType\"\n            >\n              <el-option\n                v-for=\"(item, index) in CheckTypeList\"\n                :key=\"index\"\n                :label=\"item.Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item\n            label=\"质量员\"\n            prop=\"ZL_UserIds\"\n            :rules=\"ZL_UserIds_Rules\"\n          >\n            <el-select\n              v-model=\"form.ZL_UserIds\"\n              filterable\n              clearable\n              multiple\n              style=\"width: 100%\"\n              placeholder=\"请选择质量员\"\n              :disabled=\"\n                Node_Code_Com ||\n                  (form.Change_Check_Type[0] != 1 &&\n                    form.Change_Check_Type.length != 2)\n              \"\n              @change=\"changeZLUser\"\n            >\n              <el-option\n                v-for=\"(item, index) in UserList\"\n                :key=\"index\"\n                :label=\"item.Display_Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item\n            label=\"探伤员\"\n            prop=\"TC_UserIds\"\n            :rules=\"TC_UserIds_Rules\"\n          >\n            <el-select\n              v-model=\"form.TC_UserIds\"\n              filterable\n              clearable\n              multiple\n              style=\"width: 100%\"\n              :disabled=\"\n                Node_Code_Com ||\n                  (form.Change_Check_Type[0] != 2 &&\n                    form.Change_Check_Type.length != 2)\n              \"\n              placeholder=\"请选择探伤员\"\n              @change=\"changeTCUser\"\n            >\n              <el-option\n                v-for=\"(item, index) in UserList\"\n                :key=\"index\"\n                :label=\"item.Display_Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"专检方式\" prop=\"Check_Style\">\n            <el-select\n              v-model=\"form.Check_Style\"\n              clearable\n              :disabled=\"Node_Code_Com\"\n              style=\"width: 100%\"\n              placeholder=\"请选择专检方式\"\n            >\n              <el-option\n                v-for=\"(item, index) in CheckStyleList\"\n                :key=\"index\"\n                :label=\"item.Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Change_Check_Type.includes(1)\" :span=\"12\">\n          <el-form-item label=\"探伤要求时间(h)\" prop=\"Ts_Require_Time\">\n            <el-input v-model=\"form.Ts_Require_Time\" />\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Check_Style===0\" :span=\"12\">\n          <el-form-item label=\"要求合格率(%)\" prop=\"Demand_Spot_Check_Rate\">\n            <el-input-number v-model=\"form.Demand_Spot_Check_Rate\" :min=\"0\" :max=\"100\" class=\"cs-number-btn-hidden w100\" placeholder=\"请输入\" clearable />\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Check_Style===0\" :span=\"12\">\n          <el-form-item label=\"要求抽检率(%)\" prop=\"Requirement_Spot_Check_Rate\">\n            <el-input-number v-model=\"form.Requirement_Spot_Check_Rate\" :min=\"0\" :max=\"100\" class=\"cs-number-btn-hidden w100\" placeholder=\"请输入\" clearable />\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item style=\"text-align: right\">\n            <el-button @click=\"$emit('close')\">关 闭</el-button>\n            <el-button\n              type=\"primary\"\n              @click=\"handleSubmit('form')\"\n            >确 定</el-button>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n  </div>\n</template>\n\n<script>\nimport { SaveNode } from '@/api/PRO/factorycheck'\nimport { GetEntityNode } from '@/api/PRO/factorycheck'\n// import { SaveCheckType } from \"@/api/PRO/factorycheck\";\nimport { GetFactoryPeoplelist } from '@/api/PRO/factorycheck'\nimport { GetProcessCodeList } from '@/api/PRO/factorycheck'\nexport default {\n  data() {\n    return {\n      mode: '', // 区分项目和工厂\n      ProjectId: '', // 项目Id\n      Check_Object_Id: '',\n      Bom_Level: '',\n      form: {\n        Node_Code: '',\n        Change_Check_Type: [],\n        Display_Name: '',\n        TC_UserId: '',\n        ZL_UserId: '',\n        Demand_Spot_Check_Rate: undefined,\n        Requirement_Spot_Check_Rate: undefined,\n        TC_UserIds: [],\n        ZL_UserIds: [],\n        Check_Style: '',\n        Ts_Require_Time: ''\n      },\n\n      rules: {\n        Display_Name: [\n          { required: true, message: '请填写完整表单', trigger: 'change' }\n        ],\n        Check_Type: [\n          { required: true, message: '请填写完整表单', trigger: 'change' }\n        ],\n        Change_Check_Type: [\n          { required: true, validator: this.Check_Type_rules, message: '请填写完整表单', trigger: 'change' }\n        ],\n        Check_Style: [\n          { required: true, message: '请填写完整表单', trigger: 'change' }\n        ]\n      },\n      rules_Zl: { required: true, message: '请填写完整表单', trigger: 'bur' },\n      rules_Tc: { required: true, message: '请填写完整表单', trigger: 'bur' },\n      ZL_UserIds_Rules: [\n        { required: true, validator: this.Check_ZL_UserIds, message: '请填写完整表单', trigger: 'change' }\n      ],\n      TC_UserIds_Rules: [\n        { required: true, validator: this.Check_TC_UserIds, message: '请填写完整表单', trigger: 'change' }\n      ],\n      title: '',\n      editInfo: {},\n      QualityNodeList: [{ Name: '入库' }, { Name: '出库' }], // 质检节点列表\n      CheckTypeList: [\n        {\n          Name: '质量',\n          Id: 1\n        },\n        {\n          Name: '探伤',\n          Id: 2\n        }\n      ], // 质检类型\n      UserList: [], // 质量员，探伤人员\n      CheckStyleList: [\n        {\n          Name: '抽检',\n          Id: 0\n        },\n        {\n          Name: '全检',\n          Id: 1\n        }\n      ] // 质检方式\n    }\n  },\n  computed: {\n    Node_Code_Com: function() {\n      if (this.form.Node_Code) {\n        return true\n      } else {\n        return false\n      }\n    }\n  },\n  mounted() {\n    this.getFactoryPeoplelist()\n  },\n  methods: {\n    Check_ZL_UserIds(rule, value, callback) {\n      if (this.form.Change_Check_Type.includes(1) && this.form.ZL_UserIds.length === 0) {\n        callback(new Error('请填写完整表单'))\n      } else {\n        callback()\n      }\n    },\n    Check_TC_UserIds(rule, value, callback) {\n      if (!this.Node_Code_Com && !(this.form.Change_Check_Type[0] != 2 && this.form.Change_Check_Type.length != 2) && this.form.TC_UserIds.length === 0) {\n        callback(new Error('请填写完整表单'))\n      } else {\n        callback()\n      }\n    },\n    Check_Type_rules(rule, value, callback) {\n      if (this.form.Change_Check_Type.length === 0) {\n        callback(new Error('请填写完整表单'))\n      } else {\n        callback()\n      }\n    },\n    SelectType(item) {\n      this.$forceUpdate()\n      this.form.Change_Check_Type = item\n      if (item.length == 1) {\n        this.form.Check_Type = item[0]\n      } else if (item.length == 2) {\n        this.form.Check_Type = -1\n      }\n\n      if (!item.includes(1)) {\n        this.form.ZL_UserId = ''\n        this.form.ZL_UserIds = []\n      }\n      if (!item.includes(2)) {\n        this.form.TC_UserId = ''\n        this.form.TC_UserIds = []\n      }\n      console.log(this.form.Change_Check_Type)\n    },\n    removeType(item) {\n      console.log(item, 'b')\n      // if (item == 1) {\n      //   this.form.ZL_UserId = \"\";\n      // } else if (item == 2) {\n      //   this.form.TC_UserId = \"\";\n      // }\n    },\n    clearType(val) {\n      console.log(val)\n      this.form.ZL_UserId = ''\n      this.form.TC_UserId = ''\n      this.form.ZL_UserIds = []\n      this.form.TC_UserIds = []\n    },\n    init(title, checkType, data) {\n      this.Check_Object_Id = checkType.Id\n      this.Bom_Level = checkType.Code\n      this.title = title\n      if (title === '编辑') {\n        console.log(data)\n        this.form.Id = data.Id\n        this.getEntityNode(data)\n      }\n      this.getCheckNode()\n    },\n    async addCheckNode() {\n      const { Demand_Spot_Check_Rate, ...others } = this.form\n      const submit = {\n        ...others,\n        Check_Object_Id: this.Check_Object_Id,\n        Bom_Level: this.Bom_Level\n      }\n      if (this.form.Check_Style === 0) {\n        submit.Demand_Spot_Check_Rate = Demand_Spot_Check_Rate\n      }\n      await SaveNode(submit).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            type: 'success',\n            message: '保存成功'\n          })\n          this.$emit('close')\n          this.dialogData = {}\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    getFactoryPeoplelist() {\n      GetFactoryPeoplelist().then((res) => {\n        if (res.IsSucceed) {\n          console.log(res.Data)\n          this.UserList = res.Data\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    // 判断是工厂还是项目获取质检节点\n    getCheckNode() {\n      const Platform =\n        localStorage.getItem('Platform') || localStorage.getItem('CurPlatform')\n      if (Platform === '2') {\n        this.mode = 'factory'\n        // this.getFactoryNode();\n      }\n      // 获取项目/工厂id\n      this.ProjectId =\n        this.mode === 'factory'\n          ? localStorage.getItem('CurReferenceId')\n          : this.ProjectId\n    },\n    // 如果是工厂获取质检节点\n    // getFactoryNode() {\n    //   GetProcessCodeList({sys_workobject_id:this.Check_Object_Id}).then((res) => {\n    //     if (res.IsSucceed) {\n    //       let CheckJson = res.Data;\n    //       CheckJson.push({ Name: \"入库\" }, { Name: \"出库\" });\n    //       console.log(CheckJson);\n    //       this.QualityNodeList = CheckJson;\n    //       console.log(this.QualityNodeList);\n    //     } else {\n    //       this.$message({\n    //         type: \"error\",\n    //         message: res.Message,\n    //       });\n    //     }\n    //   });\n    // },\n\n    // 质检节点获取质检节点名\n    changeNodeCode(val) {\n      this.form = {\n        Node_Code: '',\n        Change_Check_Type: [],\n        Display_Name: '',\n        TC_UserId: '',\n        ZL_UserId: '',\n        TC_UserIds: [],\n        ZL_UserIds: [],\n        Check_Style: ''\n      }\n      this.form.Display_Name = val\n      this.form.Node_Code = null\n      // this.form.Change_Check_Type = [];\n      // try {\n      //   this.form.Node_Code = this.QualityNodeList.find((v) => {\n      //     return v.Name == val;\n      //   }).Id;\n      // } catch (err) {\n      //   this.form.Node_Code = null;\n      // }\n      // console.log\n      // let arr = {};\n      // arr = this.QualityNodeList.find((v) => {\n      //   return v.Name == val;\n      // });\n      // console.log(arr);\n      // if (arr) {\n      //   this.form.Check_Style = arr.Check_Style ? Number(arr.Check_Style) : \"\";\n      //   arr.Is_Need_TC &&(this.form.Change_Check_Type.push(2))\n      //   arr.Is_Need_ZL &&( this.form.Change_Check_Type.push(1));\n      //   this.SelectType(this.form.Change_Check_Type);\n\n      //   this.form.ZL_UserId = arr.ZL_Check_UserId ? arr.ZL_Check_UserId: \"\";\n      //   this.form.TC_UserId = arr.TC_Check_UserId ? arr.TC_Check_UserId : \"\"\n      //   console.log(this.form.ZL_UserId)\n      // }\n    },\n    changeZLUser(val) {\n      console.log(val)\n      // 解决下拉框回显问题\n      this.$forceUpdate()\n      this.form.ZL_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i == val.length - 1) {\n          this.form.ZL_UserId += val[i]\n        } else {\n          this.form.ZL_UserId += val[i] + ','\n        }\n      }\n      console.log(this.form.ZL_UserId, 'this.form.ZL_UserId ')\n    },\n    changeTCUser(val) {\n      this.$forceUpdate()\n      this.form.TC_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i == val.length - 1) {\n          this.form.TC_UserId += val[i]\n        } else {\n          this.form.TC_UserId += val[i] + ','\n        }\n      }\n      // 解决下拉框回显问题\n\n      console.log(this.form.TC_UserId, 'this.form.TC_UserId ')\n    },\n    getEntityNode(data) {\n      GetEntityNode({ id: data.Id }).then((res) => {\n        if (res.IsSucceed) {\n          console.log(res.Data)\n          this.form = res.Data[0]\n          this.form.Change_Check_Type = []\n          if (this.form.Check_Type == 1 || this.form.Check_Type == 2) {\n            this.form.Change_Check_Type.push(this.form.Check_Type)\n          } else if (this.form.Check_Type == -1) {\n            this.form.Change_Check_Type = [1, 2]\n          } else {\n            this.form.Change_Check_Type = []\n          }\n          this.form.ZL_UserIds = this.form.ZL_UserId ? this.form.ZL_UserId.split(',') : []\n          this.form.TC_UserIds = this.form.TC_UserId ? this.form.TC_UserId.split(',') : []\n          console.log(this.form.ZL_UserIds, this.form.TC_UserId)\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    handleSubmit(form) {\n      this.$refs[form].validate((valid) => {\n        if (valid) {\n          this.addCheckNode()\n        } else {\n          return false\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped></style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuJA,SAAAA,QAAA;AACA,SAAAC,aAAA;AACA;AACA,SAAAC,oBAAA;AACA,SAAAC,kBAAA;AACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;MAAA;MACAC,SAAA;MAAA;MACAC,eAAA;MACAC,SAAA;MACAC,IAAA;QACAC,SAAA;QACAC,iBAAA;QACAC,YAAA;QACAC,SAAA;QACAC,SAAA;QACAC,sBAAA,EAAAC,SAAA;QACAC,2BAAA,EAAAD,SAAA;QACAE,UAAA;QACAC,UAAA;QACAC,WAAA;QACAC,eAAA;MACA;MAEAC,KAAA;QACAV,YAAA,GACA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,UAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAd,iBAAA,GACA;UAAAY,QAAA;UAAAI,SAAA,OAAAC,gBAAA;UAAAJ,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,WAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAI,QAAA;QAAAN,QAAA;QAAAC,OAAA;QAAAC,OAAA;MAAA;MACAK,QAAA;QAAAP,QAAA;QAAAC,OAAA;QAAAC,OAAA;MAAA;MACAM,gBAAA,GACA;QAAAR,QAAA;QAAAI,SAAA,OAAAK,gBAAA;QAAAR,OAAA;QAAAC,OAAA;MAAA,EACA;MACAQ,gBAAA,GACA;QAAAV,QAAA;QAAAI,SAAA,OAAAO,gBAAA;QAAAV,OAAA;QAAAC,OAAA;MAAA,EACA;MACAU,KAAA;MACAC,QAAA;MACAC,eAAA;QAAAC,IAAA;MAAA;QAAAA,IAAA;MAAA;MAAA;MACAC,aAAA,GACA;QACAD,IAAA;QACAE,EAAA;MACA,GACA;QACAF,IAAA;QACAE,EAAA;MACA,EACA;MAAA;MACAC,QAAA;MAAA;MACAC,cAAA,GACA;QACAJ,IAAA;QACAE,EAAA;MACA,GACA;QACAF,IAAA;QACAE,EAAA;MACA,EACA;IACA;EACA;EACAG,QAAA;IACAC,aAAA,WAAAA,cAAA;MACA,SAAAnC,IAAA,CAAAC,SAAA;QACA;MACA;QACA;MACA;IACA;EACA;EACAmC,OAAA,WAAAA,QAAA;IACA,KAAAC,oBAAA;EACA;EACAC,OAAA;IACAf,gBAAA,WAAAA,iBAAAgB,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,SAAAzC,IAAA,CAAAE,iBAAA,CAAAwC,QAAA,YAAA1C,IAAA,CAAAU,UAAA,CAAAiC,MAAA;QACAF,QAAA,KAAAG,KAAA;MACA;QACAH,QAAA;MACA;IACA;IACAhB,gBAAA,WAAAA,iBAAAc,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,UAAAN,aAAA,WAAAnC,IAAA,CAAAE,iBAAA,iBAAAF,IAAA,CAAAE,iBAAA,CAAAyC,MAAA,eAAA3C,IAAA,CAAAS,UAAA,CAAAkC,MAAA;QACAF,QAAA,KAAAG,KAAA;MACA;QACAH,QAAA;MACA;IACA;IACAtB,gBAAA,WAAAA,iBAAAoB,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,SAAAzC,IAAA,CAAAE,iBAAA,CAAAyC,MAAA;QACAF,QAAA,KAAAG,KAAA;MACA;QACAH,QAAA;MACA;IACA;IACAI,UAAA,WAAAA,WAAAC,IAAA;MACA,KAAAC,YAAA;MACA,KAAA/C,IAAA,CAAAE,iBAAA,GAAA4C,IAAA;MACA,IAAAA,IAAA,CAAAH,MAAA;QACA,KAAA3C,IAAA,CAAAiB,UAAA,GAAA6B,IAAA;MACA,WAAAA,IAAA,CAAAH,MAAA;QACA,KAAA3C,IAAA,CAAAiB,UAAA;MACA;MAEA,KAAA6B,IAAA,CAAAJ,QAAA;QACA,KAAA1C,IAAA,CAAAK,SAAA;QACA,KAAAL,IAAA,CAAAU,UAAA;MACA;MACA,KAAAoC,IAAA,CAAAJ,QAAA;QACA,KAAA1C,IAAA,CAAAI,SAAA;QACA,KAAAJ,IAAA,CAAAS,UAAA;MACA;MACAuC,OAAA,CAAAC,GAAA,MAAAjD,IAAA,CAAAE,iBAAA;IACA;IACAgD,UAAA,WAAAA,WAAAJ,IAAA;MACAE,OAAA,CAAAC,GAAA,CAAAH,IAAA;MACA;MACA;MACA;MACA;MACA;IACA;IACAK,SAAA,WAAAA,UAAAC,GAAA;MACAJ,OAAA,CAAAC,GAAA,CAAAG,GAAA;MACA,KAAApD,IAAA,CAAAK,SAAA;MACA,KAAAL,IAAA,CAAAI,SAAA;MACA,KAAAJ,IAAA,CAAAU,UAAA;MACA,KAAAV,IAAA,CAAAS,UAAA;IACA;IACA4C,IAAA,WAAAA,KAAA3B,KAAA,EAAA4B,SAAA,EAAA3D,IAAA;MACA,KAAAG,eAAA,GAAAwD,SAAA,CAAAvB,EAAA;MACA,KAAAhC,SAAA,GAAAuD,SAAA,CAAAC,IAAA;MACA,KAAA7B,KAAA,GAAAA,KAAA;MACA,IAAAA,KAAA;QACAsB,OAAA,CAAAC,GAAA,CAAAtD,IAAA;QACA,KAAAK,IAAA,CAAA+B,EAAA,GAAApC,IAAA,CAAAoC,EAAA;QACA,KAAAyB,aAAA,CAAA7D,IAAA;MACA;MACA,KAAA8D,YAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,UAAA,EAAA1D,sBAAA,EAAA2D,MAAA,EAAAC,MAAA;QAAA,OAAAL,mBAAA,GAAAM,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAP,UAAA,GACAL,KAAA,CAAA3D,IAAA,EAAAM,sBAAA,GAAA0D,UAAA,CAAA1D,sBAAA,EAAA2D,MAAA,GAAAO,wBAAA,CAAAR,UAAA,EAAAS,SAAA;cACAP,MAAA,GAAAQ,aAAA,CAAAA,aAAA,KACAT,MAAA;gBACAnE,eAAA,EAAA6D,KAAA,CAAA7D,eAAA;gBACAC,SAAA,EAAA4D,KAAA,CAAA5D;cAAA;cAEA,IAAA4D,KAAA,CAAA3D,IAAA,CAAAW,WAAA;gBACAuD,MAAA,CAAA5D,sBAAA,GAAAA,sBAAA;cACA;cAAA+D,QAAA,CAAAE,IAAA;cAAA,OACAhF,QAAA,CAAA2E,MAAA,EAAAS,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAlB,KAAA,CAAAmB,QAAA;oBACAC,IAAA;oBACAhE,OAAA;kBACA;kBACA4C,KAAA,CAAAqB,KAAA;kBACArB,KAAA,CAAAsB,UAAA;gBACA;kBACAtB,KAAA,CAAAmB,QAAA;oBACAC,IAAA;oBACAhE,OAAA,EAAA6D,GAAA,CAAAM;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAb,QAAA,CAAAc,IAAA;UAAA;QAAA,GAAApB,OAAA;MAAA;IACA;IACA1B,oBAAA,WAAAA,qBAAA;MAAA,IAAA+C,MAAA;MACA3F,oBAAA,GAAAkF,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA7B,OAAA,CAAAC,GAAA,CAAA2B,GAAA,CAAAS,IAAA;UACAD,MAAA,CAAApD,QAAA,GAAA4C,GAAA,CAAAS,IAAA;QACA;UACAD,MAAA,CAAAN,QAAA;YACAC,IAAA;YACAhE,OAAA,EAAA6D,GAAA,CAAAM;UACA;QACA;MACA;IACA;IACA;IACAzB,YAAA,WAAAA,aAAA;MACA,IAAA6B,QAAA,GACAC,YAAA,CAAAC,OAAA,gBAAAD,YAAA,CAAAC,OAAA;MACA,IAAAF,QAAA;QACA,KAAA1F,IAAA;QACA;MACA;MACA;MACA,KAAAC,SAAA,GACA,KAAAD,IAAA,iBACA2F,YAAA,CAAAC,OAAA,qBACA,KAAA3F,SAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA4F,cAAA,WAAAA,eAAArC,GAAA;MACA,KAAApD,IAAA;QACAC,SAAA;QACAC,iBAAA;QACAC,YAAA;QACAC,SAAA;QACAC,SAAA;QACAI,UAAA;QACAC,UAAA;QACAC,WAAA;MACA;MACA,KAAAX,IAAA,CAAAG,YAAA,GAAAiD,GAAA;MACA,KAAApD,IAAA,CAAAC,SAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;IACA;IACAyF,YAAA,WAAAA,aAAAtC,GAAA;MACAJ,OAAA,CAAAC,GAAA,CAAAG,GAAA;MACA;MACA,KAAAL,YAAA;MACA,KAAA/C,IAAA,CAAAK,SAAA;MACA,SAAAsF,CAAA,MAAAA,CAAA,GAAAvC,GAAA,CAAAT,MAAA,EAAAgD,CAAA;QACA,IAAAA,CAAA,IAAAvC,GAAA,CAAAT,MAAA;UACA,KAAA3C,IAAA,CAAAK,SAAA,IAAA+C,GAAA,CAAAuC,CAAA;QACA;UACA,KAAA3F,IAAA,CAAAK,SAAA,IAAA+C,GAAA,CAAAuC,CAAA;QACA;MACA;MACA3C,OAAA,CAAAC,GAAA,MAAAjD,IAAA,CAAAK,SAAA;IACA;IACAuF,YAAA,WAAAA,aAAAxC,GAAA;MACA,KAAAL,YAAA;MACA,KAAA/C,IAAA,CAAAI,SAAA;MACA,SAAAuF,CAAA,MAAAA,CAAA,GAAAvC,GAAA,CAAAT,MAAA,EAAAgD,CAAA;QACA,IAAAA,CAAA,IAAAvC,GAAA,CAAAT,MAAA;UACA,KAAA3C,IAAA,CAAAI,SAAA,IAAAgD,GAAA,CAAAuC,CAAA;QACA;UACA,KAAA3F,IAAA,CAAAI,SAAA,IAAAgD,GAAA,CAAAuC,CAAA;QACA;MACA;MACA;;MAEA3C,OAAA,CAAAC,GAAA,MAAAjD,IAAA,CAAAI,SAAA;IACA;IACAoD,aAAA,WAAAA,cAAA7D,IAAA;MAAA,IAAAkG,MAAA;MACArG,aAAA;QAAAsG,EAAA,EAAAnG,IAAA,CAAAoC;MAAA,GAAA4C,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA7B,OAAA,CAAAC,GAAA,CAAA2B,GAAA,CAAAS,IAAA;UACAQ,MAAA,CAAA7F,IAAA,GAAA4E,GAAA,CAAAS,IAAA;UACAQ,MAAA,CAAA7F,IAAA,CAAAE,iBAAA;UACA,IAAA2F,MAAA,CAAA7F,IAAA,CAAAiB,UAAA,SAAA4E,MAAA,CAAA7F,IAAA,CAAAiB,UAAA;YACA4E,MAAA,CAAA7F,IAAA,CAAAE,iBAAA,CAAA6F,IAAA,CAAAF,MAAA,CAAA7F,IAAA,CAAAiB,UAAA;UACA,WAAA4E,MAAA,CAAA7F,IAAA,CAAAiB,UAAA;YACA4E,MAAA,CAAA7F,IAAA,CAAAE,iBAAA;UACA;YACA2F,MAAA,CAAA7F,IAAA,CAAAE,iBAAA;UACA;UACA2F,MAAA,CAAA7F,IAAA,CAAAU,UAAA,GAAAmF,MAAA,CAAA7F,IAAA,CAAAK,SAAA,GAAAwF,MAAA,CAAA7F,IAAA,CAAAK,SAAA,CAAA2F,KAAA;UACAH,MAAA,CAAA7F,IAAA,CAAAS,UAAA,GAAAoF,MAAA,CAAA7F,IAAA,CAAAI,SAAA,GAAAyF,MAAA,CAAA7F,IAAA,CAAAI,SAAA,CAAA4F,KAAA;UACAhD,OAAA,CAAAC,GAAA,CAAA4C,MAAA,CAAA7F,IAAA,CAAAU,UAAA,EAAAmF,MAAA,CAAA7F,IAAA,CAAAI,SAAA;QACA;UACAyF,MAAA,CAAAf,QAAA;YACAC,IAAA;YACAhE,OAAA,EAAA6D,GAAA,CAAAM;UACA;QACA;MACA;IACA;IACAe,YAAA,WAAAA,aAAAjG,IAAA;MAAA,IAAAkG,MAAA;MACA,KAAAC,KAAA,CAAAnG,IAAA,EAAAoG,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAAxC,YAAA;QACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
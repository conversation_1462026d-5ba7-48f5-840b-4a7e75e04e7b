export default {
  PROBOMLevelConfig: () => import('@/views/PRO/bom-setting/bom-level-config/index.vue'),
  PROPartsConfig: () => import('@/views/PRO/bom-setting/part-config/index.vue'),
  PROComponentConfig: () => import('@/views/PRO/bom-setting/com-config/index.vue'),
  PROHalfPartConfig: () => import('@/views/PRO/bom-setting/half-part-config/index.vue'),
  PROStructureTypeConfig: () => import('@/views/PRO/bom-setting/structure-type-config/index.vue'),
  PROBomImportTemplateConfig: () => import('@/views/PRO/bom-setting/bom-import-temp-config/index.vue')
}

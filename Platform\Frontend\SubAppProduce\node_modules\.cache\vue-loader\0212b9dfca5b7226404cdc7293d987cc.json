{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\ship\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\ship\\index.vue", "mtime": 1758677034219}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgQ2FyZCBmcm9tICcuL2NvbXBvbmVudC9DYXJkJw0KaW1wb3J0IFRvcEhlYWRlciBmcm9tICdAL2NvbXBvbmVudHMvVG9wSGVhZGVyJw0KaW1wb3J0IGFkZEVkaXQgZnJvbSAnQC92aWV3cy9QUk8vYmFzaWMtaW5mb3JtYXRpb24vc2hpcC9jb21wb25lbnQvQWRkRWRpdCcNCmltcG9ydCBDYXJJbXBvcnQgZnJvbSAnLi9jb21wb25lbnQvSW1wb3J0Jw0KaW1wb3J0IHsgR2V0UGFnZUluZm8sRGVsZXRlQm9hdCB9IGZyb20gJ0AvYXBpL1BSTy9jYXInDQppbXBvcnQgUGFnaW5hdGlvbiBmcm9tICdAL2NvbXBvbmVudHMvUGFnaW5hdGlvbicNCmltcG9ydCB7IEdldEZhY3RvcnlMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL2ZhY3RvcnknDQppbXBvcnQgeyB0YWJsZVBhZ2VTaXplIH0gZnJvbSAnQC92aWV3cy9QUk8vc2V0dGluZycNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnUFJPQmFzaWNWZWhpY2xlJywNCiAgY29tcG9uZW50czogew0KICAgIENhcmQsDQogICAgVG9wSGVhZGVyLA0KICAgIENhckltcG9ydCwNCiAgICBhZGRFZGl0LA0KICAgIFBhZ2luYXRpb24NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgdGFibGVQYWdlU2l6ZTogdGFibGVQYWdlU2l6ZSwNCiAgICAgIHRvdGFsOiAwLA0KICAgICAgbGlzdFF1ZXJ5OiB7DQogICAgICAgIFBhZ2VTaXplOiAxMiwNCiAgICAgICAgUGFnZTogMSwNCiAgICAgICAgLy8gUHJvamVjdF9JZDogJycsDQogICAgICAgIFNoaXBudW1iZXI6ICcnDQogICAgICB9LA0KICAgICAgdmFsdWU6ICcnLA0KICAgICAgdGl0bGU6ICcnLA0KICAgICAgbGlzdDogW10sDQogICAgICBjdXJyZW50Q29tcG9uZW50OiAnJywNCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgY2FyT3B0aW9uczogW10sDQogICAgICBmYWN0b3J5T3B0aW9uOiBbXSwNCiAgICAgIGZhY3RvcnlJZDogJycNCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5mZXRjaERhdGEoKQ0KICAgIC8vIHRoaXMuZ2V0RmFjdG9yeSgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBmZXRjaERhdGEoKSB7DQogICAgICBHZXRQYWdlSW5mbyh0aGlzLmxpc3RRdWVyeSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMubGlzdCA9IHJlcy5EYXRhLkRhdGENCiAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzLkRhdGEuVG90YWxDb3VudA0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGZhY3RvcnlDaGFuZ2Uodikgew0KICAgICAgdGhpcy5mZXRjaERhdGEoKQ0KICAgIH0sDQogICAgLy8gZ2V0RmFjdG9yeSgpIHsNCiAgICAvLyAgIEdldEZhY3RvcnlMaXN0KHt9KS50aGVuKHJlcyA9PiB7DQogICAgLy8gICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgLy8gICAgICAgdGhpcy5mYWN0b3J5T3B0aW9uID0gcmVzLkRhdGENCiAgICAvLyAgICAgfSBlbHNlIHsNCiAgICAvLyAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAvLyAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgIC8vICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgIC8vICAgICAgIH0pDQogICAgLy8gICAgIH0NCiAgICAvLyAgIH0pDQogICAgLy8gfSwNCiAgICBsaWNlbnNlQ2hhbmdlKHYpIHsNCiAgICAgIHRoaXMuZmV0Y2hEYXRhKCkNCiAgICB9LA0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9ICdhZGRFZGl0Jw0KICAgICAgdGhpcy50aXRsZSA9ICfmlrDlop7oiLnoiLYnDQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgfSwNCiAgICBoYW5kbGVFZGl0KHJvdykgew0KICAgICAgY29uc29sZS5sb2cocm93LCdyb3cnKTsNCiAgICAgIA0KICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ2FkZEVkaXQnDQogICAgICB0aGlzLnRpdGxlID0gJ+e8lui+keiIueiIticNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuJG5leHRUaWNrKF8gPT4gew0KICAgICAgICB0aGlzLiRyZWZzWydjb250ZW50J10uZWRpdEluaXQocm93KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuWIoOmZpOivpeiIueiItj8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICBEZWxldGVCb2F0KHsNCiAgICAgICAgICBpZDogcm93LklkDQogICAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywNCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKnyEnDQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgdGhpcy4kZW1pdCgnY2xvc2UnKQ0KICAgICAgICAgICAgdGhpcy5mZXRjaERhdGEoKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICB0eXBlOiAnaW5mbycsDQogICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iOWIoOmZpCcNCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfSwNCiAgICBjYXJJbXBvcnQoKSB7DQogICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSAnQ2FySW1wb3J0Jw0KICAgICAgdGhpcy50aXRsZSA9ICflr7zlhaUnDQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgfSwNCiAgICBoYW5kbGVDbG9zZSgpIHsNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/basic-information/ship", "sourcesContent": ["<template>\r\n  <div class=\"x-container\">\r\n    <top-header style=\"padding: 0 8px; margin-bottom: 10px\">\r\n      <template #left>\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增</el-button>\r\n        <el-button type=\"success\" @click=\"carImport\">导 入</el-button>\r\n      </template>\r\n      <template #right>\r\n        <!--        <el-select v-model=\"listQuery.Factory_Id\" style=\"margin-right: 8px\" placeholder=\"服务工厂\" clearable=\"\" @change=\"factoryChange\">\r\n          <el-option\r\n            v-for=\"item in factoryOption\"\r\n            :key=\"item.Id\"\r\n            :label=\"item.Name\"\r\n            :value=\"item.Id\"\r\n          />\r\n        </el-select>-->\r\n        <el-input v-model=\"listQuery.Shipnumber\" style=\"width: 180px\" placeholder=\"请输入船号\" clearable=\"\" @change=\"licenseChange\" />\r\n        <el-button type=\"primary\" @click=\"licenseChange\">查 询</el-button>\r\n        \r\n      </template>\r\n    </top-header>\r\n    <main class=\"cs-main\">\r\n      <card\r\n        v-for=\"(item, index) in list\"\r\n        :key=\"index\"\r\n        :item=\"item\"\r\n        @edit=\"handleEdit\"\r\n        @delete=\"handleDelete\"\r\n      />\r\n    </main>\r\n    <!-- :class=\"[{'mr-8':!index%4}]\" -->\r\n    <div class=\"cs-pagination-container\">\r\n      <Pagination\r\n        :total=\"total\"\r\n        :page.sync=\"listQuery.Page\"\r\n        :limit.sync=\"listQuery.PageSize\"\r\n        :page-sizes=\"tablePageSize\"\r\n        @pagination=\"fetchData\"\r\n      />\r\n    </div>\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      v-dialogDrag\r\n       class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"40%\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        ref=\"content\"\r\n        @close=\"handleClose\"\r\n        @refresh=\"fetchData\"\r\n      />\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Card from './component/Card'\r\nimport TopHeader from '@/components/TopHeader'\r\nimport addEdit from '@/views/PRO/basic-information/ship/component/AddEdit'\r\nimport CarImport from './component/Import'\r\nimport { GetPageInfo,DeleteBoat } from '@/api/PRO/car'\r\nimport Pagination from '@/components/Pagination'\r\nimport { GetFactoryList } from '@/api/PRO/factory'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\n\r\nexport default {\r\n  name: 'PROBasicVehicle',\r\n  components: {\r\n    Card,\r\n    TopHeader,\r\n    CarImport,\r\n    addEdit,\r\n    Pagination\r\n  },\r\n  data() {\r\n    return {\r\n      tablePageSize: tablePageSize,\r\n      total: 0,\r\n      listQuery: {\r\n        PageSize: 12,\r\n        Page: 1,\r\n        // Project_Id: '',\r\n        Shipnumber: ''\r\n      },\r\n      value: '',\r\n      title: '',\r\n      list: [],\r\n      currentComponent: '',\r\n      dialogVisible: false,\r\n      carOptions: [],\r\n      factoryOption: [],\r\n      factoryId: ''\r\n    }\r\n  },\r\n  mounted() {\r\n    this.fetchData()\r\n    // this.getFactory()\r\n  },\r\n  methods: {\r\n    fetchData() {\r\n      GetPageInfo(this.listQuery).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.list = res.Data.Data\r\n          this.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    factoryChange(v) {\r\n      this.fetchData()\r\n    },\r\n    // getFactory() {\r\n    //   GetFactoryList({}).then(res => {\r\n    //     if (res.IsSucceed) {\r\n    //       this.factoryOption = res.Data\r\n    //     } else {\r\n    //       this.$message({\r\n    //         message: res.Message,\r\n    //         type: 'error'\r\n    //       })\r\n    //     }\r\n    //   })\r\n    // },\r\n    licenseChange(v) {\r\n      this.fetchData()\r\n    },\r\n    handleAdd() {\r\n      this.currentComponent = 'addEdit'\r\n      this.title = '新增船舶'\r\n      this.dialogVisible = true\r\n    },\r\n    handleEdit(row) {\r\n      console.log(row,'row');\r\n      \r\n      this.currentComponent = 'addEdit'\r\n      this.title = '编辑船舶'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].editInit(row)\r\n      })\r\n    },\r\n    handleDelete(row) {\r\n      this.$confirm('是否删除该船舶?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        DeleteBoat({\r\n          id: row.Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.$emit('close')\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    },\r\n    carImport() {\r\n      this.currentComponent = 'CarImport'\r\n      this.title = '导入'\r\n      this.dialogVisible = true\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.x-container {\r\n  padding: 16px 8px;\r\n  .cs-top-header-box {\r\n    background: #F4F5F7;\r\n    padding: 0;\r\n  }\r\n  .mb-8{\r\n    margin-bottom: 8px;\r\n  }\r\n  .mt-8{\r\n    margin-top: 8px;\r\n  }\r\n  .ml-8{\r\n    margin-left: 8px;\r\n  }\r\n  .mr-8{\r\n    margin-right: 8px;\r\n  }\r\n  .cs-main{\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n  }\r\n  .cs-pagination-container {\r\n    padding: 0 8px;\r\n  }\r\n}\r\n\r\n</style>\r\n\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\component-list\\v4\\component\\BatchEditor.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\component-list\\v4\\component\\BatchEditor.vue", "mtime": 1758266753081}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["BatchEditor.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "BatchEditor.vue", "sourceRoot": "src/views/PRO/component-list/v4/component", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-row v-for=\"(info,index) in list\" :key=\"info.id\" class=\"item-x\">\r\n      <div class=\"item\">\r\n        <label>\r\n          属性名称\r\n          <el-select\r\n            v-model=\"info.key\"\r\n            style=\"width: calc(100% - 65px)\"\r\n            clearable\r\n            placeholder=\"请选择\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in filterOption(info.key)\"\r\n              :key=\"item.key\"\r\n              :label=\"item.label\"\r\n              :value=\"item.key \"\r\n            />\r\n          </el-select>\r\n        </label>\r\n      </div>\r\n      <div class=\"item\" style=\"line-height: 32px;\">\r\n        <label>请输入值\r\n          <el-input-number v-if=\"checkType(info.key,'number')\" v-model=\"info.val\" :min=\"0\" class=\"cs-number-btn-hidden\" />\r\n          <el-input v-if=\"checkType(info.key,'string')\" v-model=\"info.val\" />\r\n          <el-select\r\n            v-if=\"checkType(info.key,'array') && info.key==='Is_Component'\"\r\n            v-model=\"info.val\"\r\n            clearable\r\n            placeholder=\"请选择\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in Is_Component_Data\"\r\n              :key=\"item.Id\"\r\n              :label=\"item.Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n          <el-tree-select\r\n            v-show=\"checkType(info.key,'array') && info.key==='SteelType'\"\r\n            ref=\"treeSelect\"\r\n            v-model=\"info.val\"\r\n            :tree-params=\"treeParams\"\r\n            style=\"width: 100%;display: inline-block\"\r\n            @node-click=\"steelTypeChange\"\r\n          />\r\n        </label>\r\n      </div>\r\n      <span v-if=\"index === 0\" class=\"item-span\">\r\n        <i class=\"el-icon-circle-plus-outline\" @click=\"handleAdd\" />\r\n      </span>\r\n      <span v-else class=\"item-span\">\r\n        <i class=\"el-icon-circle-plus-outline\" @click=\"handleAdd\" />\r\n        <i class=\"el-icon-remove-outline txt-red\" @click=\"handleDelete(index)\" />\r\n      </span>\r\n    </el-row>\r\n    <div style=\"text-align: right;width: 100%;padding: 20px 2% 0 0\">\r\n      <el-button @click=\"$emit('close')\">取消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"onSubmit\">确定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { BatchUpdateComponentImportInfo } from '@/api/PRO/component'\r\nimport { GetCompTypeTree } from '@/api/PRO/component-type'\r\nimport { GetUserableAttr } from '@/api/PRO/professionalType'\r\n\r\nexport default {\r\n  props: {\r\n    typeEntity: {\r\n      type: Object,\r\n      default: () => {}\r\n    },\r\n    projectId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    sysProjectId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      treeParams: {\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      },\r\n      Is_Component_Data: [{ Name: '是', Id: '否' }, { Name: '否', Id: '是' }], // 是否是直发件数据\r\n      Is_Component: '',\r\n      value: '',\r\n      options: [\r\n        {\r\n          key: 'SetupPosition',\r\n          label: '批次',\r\n          type: 'string'\r\n        },\r\n        {\r\n          key: 'SteelSpec',\r\n          label: '规格',\r\n          type: 'string'\r\n        }, {\r\n          key: 'SteelWeight',\r\n          label: '单重',\r\n          type: 'number'\r\n        }, {\r\n          key: 'SteelLength',\r\n          label: '长度',\r\n          type: 'number'\r\n        }, {\r\n          key: 'SteelAmount',\r\n          label: '深化数量',\r\n          type: 'number'\r\n        }, {\r\n          key: 'SteelMaterial',\r\n          label: '材质 ',\r\n          type: 'string'\r\n        }, {\r\n          key: 'Is_Component',\r\n          label: '是否直发件',\r\n          type: 'array'\r\n        }, {\r\n          key: 'Remark',\r\n          label: '备注',\r\n          type: 'string'\r\n        },\r\n        {\r\n          key: 'GrossWeight',\r\n          label: '单毛重',\r\n          type: 'number'\r\n        }\r\n      ],\r\n      list: [{\r\n        id: uuidv4(),\r\n        val: undefined,\r\n        key: ''\r\n      }]\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.getCompTypeTree()\r\n    await this.getUserableAttr()\r\n    const codeArr = this.options.filter((item, index) => index).map(i => i.key)\r\n    const columns = await this.convertCode(this.typeEntity.Code, codeArr)\r\n    this.options = this.options.map((item, index) => {\r\n      if (index) {\r\n        item.label = columns.filter((v) => v.Is_Display).find(i => i.Code === item.key)?.Display_Name\r\n      }\r\n      return item\r\n    })\r\n  },\r\n  methods: {\r\n    // 获取拓展字段\r\n    async getUserableAttr() {\r\n      await GetUserableAttr({\r\n        IsComponent: true,\r\n        Bom_Level: -1\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const resData = res.Data\r\n          const expandData = []\r\n          resData.forEach(item => {\r\n            const expandJson = {}\r\n            expandJson.key = item.Code\r\n            expandJson.lable = item.Display_Name\r\n            expandJson.type = 'string'\r\n            expandData.push(expandJson)\r\n          })\r\n          this.options = this.options.concat(expandData)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 获取构件类型\r\n    async getCompTypeTree() {\r\n      await GetCompTypeTree({\r\n        professional: this.typeEntity.Code\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$refs.treeSelect.forEach(item => {\r\n            item.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.treeData = []\r\n        }\r\n      }).finally(_ => {\r\n      })\r\n    },\r\n\r\n    handleAdd() {\r\n      this.list.push({\r\n        id: uuidv4(),\r\n        val: undefined,\r\n        key: ''\r\n      })\r\n      this.getCompTypeTree()\r\n    },\r\n\r\n    handleDelete(index) {\r\n      this.list.splice(index, 1)\r\n    },\r\n\r\n    async onSubmit() {\r\n      this.btnLoading = true\r\n      const obj = {}\r\n      for (let i = 0; i < this.list.length; i++) {\r\n        const element = this.list[i]\r\n        if (!element.val) {\r\n          if (element.key === 'SteelWeight' || element.key === 'SteelLength' || element.key === 'SteelAmount' || element.key === 'GrossWeight') {\r\n            element.val === 0 ? this.$message({ message: '值不能为0', type: 'warning' }) : this.$message({ message: '值不能为空', type: 'warning' })\r\n          } else {\r\n            this.$message({\r\n              message: '值不能为空',\r\n              type: 'warning'\r\n            })\r\n          }\r\n          this.btnLoading = false\r\n          return\r\n        }\r\n        // obj[element.key] = element.key=='Is_Component' ? eval(element.val.toLowerCase()) : element.val; // \"true\"转true\r\n        obj[element.key] = element.val\r\n      }\r\n      // if (!obj.hasOwnProperty('Is_Component') && obj.hasOwnProperty('SteelType')) {\r\n      //   obj['Is_Component'] = this.Is_Component\r\n      // }\r\n      await BatchUpdateComponentImportInfo({\r\n        Ids: this.selectList.map(v => v.Id).toString(),\r\n        EditInfo: obj\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '修改成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('close')\r\n          this.$emit('refresh')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.btnLoading = false\r\n      })\r\n    },\r\n\r\n    filterOption(currentValue) {\r\n      console.log(currentValue)\r\n      return this.options.filter(k => {\r\n        return (!this.list.map(v => v.key).includes(k.key) || k.key === currentValue) && k.label\r\n      })\r\n    },\r\n\r\n    checkType(key, type) {\r\n      if (!key) return false\r\n      return this.options.find(v => v.key === key).type === type\r\n    },\r\n\r\n    init(list, columnsOption) {\r\n      console.log(list)\r\n      this.selectList = list\r\n\r\n    //   let filterarr = columnsOption.filter(v=> {\r\n    //   return v.Display_Name != \"项目名称\" && v.Display_Name != \"区域\" && v.Display_Name != \"批次\" && v.Code != \"SteelAllWeight\" &&  v.Code != \"SteelName\" && v.Code != \"TotalGrossWeight\" && v.Code != \"SteelType\" && v.Code != \"Part\" && v.Code != \"SchedulingNum\"  && v.Code != \"Create_UserName\" && v.Code === \"Is_Component_Status\"\r\n    //  })\r\n    //  this.options = filterarr?.map(item => ({ key: item.Code, label: item.Display_Name, type: item.Code === \"Is_Component\" ?\"array\": item.Code === \"SteelWeight\" || item.Code === \"GrossWeight\" || item.Code === \"SteelLength\" || item.Code === \"SchedulingNum\"  ? \"number\" : \"string\"}))\r\n      // this.options = columnsOption?.map(v => ({ key: v.Code, label: v.Display_Name, type: v.Code === \"Is_Component\" ? \"array\": v.Code === \"SteelWeight\" ||  v.Code === \"SteelAllWeight\" ? 'number' : 'string' }))\r\n    },\r\n\r\n    // 获取配置数据\r\n    async getColumnConfiguration(code, mainType = 'plm_component_page_list') {\r\n      const res = await GetGridByCode({ code: mainType + ',' + code })\r\n      return res.Data.ColumnList\r\n    },\r\n\r\n    // 根据Code（数据）获取名称\r\n    async convertCode(typeCode, propsArr = [], mainType) {\r\n      console.log(propsArr)\r\n      const SchedulArr = this.selectList.filter((item) => {\r\n        return item.SteelType !== null && item.SteelType !== ''\r\n      })\r\n      const propsArrfilter = SchedulArr.length > 0 ? propsArr.filter((v) => v !== 'Is_Component') : propsArr\r\n      const props = await this.getColumnConfiguration(typeCode, mainType)\r\n      console.log(props)\r\n      const columns = props.filter(i => {\r\n        const arr = propsArrfilter.map(i => i.toLowerCase())\r\n        return arr.includes(i.Code.toLowerCase())\r\n      })\r\n      console.log(columns)\r\n      return columns\r\n    },\r\n\r\n    // 选择构件类型\r\n    steelTypeChange(e) {\r\n      this.Is_Component = e.Code == 'true' ? '是' : '否'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n[class^=\"el-icon\"] {\r\n  font-size: 24px;\r\n  vertical-align: middle;\r\n  cursor: pointer;\r\n  margin-left: 15px;\r\n}\r\n\r\n.item-x {\r\n  display: flex;\r\n  margin-bottom: 20px;\r\n  flex: 0 1 50%;\r\n  justify-content: space-between;\r\n\r\n  .item {\r\n    width: 45%;\r\n    white-space: nowrap;\r\n    &:not(:first-of-type) {\r\n      margin-left: 20px;\r\n      .cs-number-btn-hidden,.el-input,.el-select{\r\n        width: 80%;\r\n      }\r\n    }\r\n  }\r\n\r\n  .item-span {\r\n    width: 90px;\r\n    padding-top: 5px;\r\n  }\r\n\r\n}\r\n  ::v-deep{\r\n    .el-tree-select-input{\r\n      width: 80%!important;\r\n    }\r\n  }\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\part-type\\component\\Lack.vue?vue&type=template&id=08e2c548&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\part-type\\component\\Lack.vue", "mtime": 1757468112175}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxlbC1mb3JtIHJlZj0iZm9ybSIgOm1vZGVsPSJmb3JtIiA6cnVsZXM9InJ1bGVzIiBsYWJlbC13aWR0aD0iMTkwcHgiIHN0eWxlPSJ3aWR0aDogMTAwJSI+CiAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5YiG57G757y655yB5pe277yM6buY6K6k6YCJ5oup5Li677yaIiBwcm9wPSJJZCI+CiAgICA8ZWwtc2VsZWN0IHYtbW9kZWw9ImZvcm0uSWQiIHBsYWNlaG9sZGVyPSLor7fpgInmi6kiIGNsZWFyYWJsZT0iIj4KICAgICAgPGVsLW9wdGlvbgogICAgICAgIHYtZm9yPSJpdGVtIGluIG9wdGlvbnMiCiAgICAgICAgOmtleT0iaXRlbS5JZCIKICAgICAgICA6bGFiZWw9Iml0ZW0uTmFtZSIKICAgICAgICA6dmFsdWU9Iml0ZW0uSWQiCiAgICAgIC8+CiAgICA8L2VsLXNlbGVjdD4KICA8L2VsLWZvcm0taXRlbT4KICA8c3BhbiBjbGFzcz0idGlwIj7lvZPlr7zlhaXnmoTpm7bku7bmuIXljZXkuK3vvIzpm7bku7bliIbnsbvnvLrnnIHml7bvvIzns7vnu5/oh6rliqjooaXlhajkuIrov7DpgInmi6nnmoTliIbnsbvjgII8L3NwYW4+CiAgPGVsLWZvcm0taXRlbSBjbGFzcz0iY3MtZm9vdGVyIj4KICAgIDxlbC1idXR0b24gQGNsaWNrPSJoYW5kbGVDbG9zZSI+5Y+WIOa2iDwvZWwtYnV0dG9uPgogICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiA6bG9hZGluZz0iYnRuTG9hZGluZyIgQGNsaWNrPSJoYW5kbGVTdWJtaXQoJ2Zvcm0nKSI+56GuIOWumjwvZWwtYnV0dG9uPgogIDwvZWwtZm9ybS1pdGVtPgo8L2VsLWZvcm0+Cg=="}, null]}
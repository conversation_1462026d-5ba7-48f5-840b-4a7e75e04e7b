{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\RecognitionConfig.vue?vue&type=style&index=0&id=f0e6d9b4&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\RecognitionConfig.vue", "mtime": 1757474528544}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5mb3JtLXJlY29nbml0aW9uLXdyYXBwZXIgewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICBvdmVyZmxvdzogaGlkZGVuOwogIG1heC1oZWlnaHQ6IDcwdmg7CiAgLmZvcm0tcmVjb2duaXRpb24tdGFicyB7CgogIH0KfQo="}, {"version": 3, "sources": ["RecognitionConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "RecognitionConfig.vue", "sourceRoot": "src/views/PRO/project-config/process-settings/component", "sourcesContent": ["<template>\n  <div class=\"form-recognition-wrapper\">\n    <div class=\"form-recognition-tabs\">\n      <el-tabs v-model=\"bomActiveName\">\n        <el-tab-pane v-for=\"(item, index) in bomList\" :key=\"index\" :label=\"item.Display_Name\" :name=\"item.Code\" />\n      </el-tabs>\n    </div>\n    <div>\n      <comp-recognition-config v-if=\"bomActiveName === '-1'\" @close=\"handleClose\" />\n      <part-recognition-config v-if=\"bomActiveName === '0'\" :bom-list=\"bomList\" @close=\"handleClose\" />\n      <unit-part-recognition-config v-if=\"bomActiveName !== '-1' && bomActiveName !== '0'\" :bom-list=\"bomList\" :level=\"Number(bomActiveName)\" @close=\"handleClose\" />\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport compRecognitionConfig from './compRecognitionConfig'\nimport partRecognitionConfig from './partRecognitionConfig'\nimport unitPartRecognitionConfig from './unitPartRecognitionConfig'\n\nexport default {\n  components: {\n    compRecognitionConfig,\n    partRecognitionConfig,\n    unitPartRecognitionConfig\n  },\n  data() {\n    return {\n      bomList: [],\n      comName: '',\n      partName: '',\n      bomActiveName: '',\n      btnLoading: false\n    }\n  },\n  async mounted() {\n    const { comName, partName, list } = await GetBOMInfo()\n    this.comName = comName\n    this.partName = partName\n    this.bomList = list\n    this.bomActiveName = list[0].Code\n  },\n  methods: {\n    handleClose() {\n      this.$emit('close')\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n  .form-recognition-wrapper {\n    display: flex;\n    flex-direction: column;\n    overflow: hidden;\n    max-height: 70vh;\n    .form-recognition-tabs {\n\n    }\n  }\n</style>\n"]}]}
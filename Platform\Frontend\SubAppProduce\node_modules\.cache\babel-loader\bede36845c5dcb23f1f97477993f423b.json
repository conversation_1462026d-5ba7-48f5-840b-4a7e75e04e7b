{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\bom-level-config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\bom-level-config\\index.vue", "mtime": 1758266753080}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetBomLevelList", "SaveBomLevel", "addRouterPage", "store", "name", "mixins", "data", "selectedType", "btnLoading", "loading", "apiData", "typeOptions", "label", "value", "tableData", "tbLoading", "computed", "comName", "length", "Display_Name", "partName", "addPageArray", "_this", "unitPart", "filter", "item", "Is_Enabled", "Code", "route", "path", "$route", "hidden", "component", "Promise", "resolve", "then", "_interopRequireWildcard", "require", "meta", "title", "curList", "for<PERSON>ach", "push", "concat", "splice", "apply", "console", "log", "mounted", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getBomLevelList", "handleInitPageRoute", "$router", "getRoutes", "stop", "methods", "initPage", "handleBottomLabelClick", "layer", "_name", "query", "pg_redirect", "level", "JSON", "parse", "stringify", "_this3", "res", "IsSucceed", "Message", "Data", "map", "v", "isEditing", "originalName", "enabledCount", "generateTableData", "$store", "dispatch", "$message", "message", "type", "catch", "error", "finally", "levelCount", "arguments", "undefined", "levelCodeMap", "validCodes", "filteredData", "includes", "sort", "a", "b", "parseInt", "Sort", "handleTypeChange", "handleSubmit", "_this4", "<PERSON><PERSON><PERSON><PERSON>", "every", "Is_Default_Model", "warning", "saveData", "_toConsumableArray", "$confirm", "confirmButtonText", "cancelButtonText", "success", "info", "handleEdit", "row", "rowIndex", "customTableConfig", "handleSaveName", "handleCancel", "handleDefaultModelChange", "editRowEvent", "$table", "$refs", "xTable1", "setEditRow", "saveRowEvent", "_this5", "clearEdit", "setTimeout", "cancelRowEvent", "revertData", "handleShowConfig"], "sources": ["src/views/PRO/bom-setting/bom-level-config/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"bom-level-config abs100 cs-z-flex-pd16-wrap\">\r\n    <div class=\"cs-z-page-main-content\">\r\n      <div class=\"query-section\">\r\n        <div class=\"query-form\">\r\n          <div>\r\n            <span class=\"query-label\">请选择BOM层级数：</span>\r\n            <el-radio-group\r\n              v-model=\"selectedType\"\r\n              class=\"query-radio-group\"\r\n              @change=\"handleTypeChange\"\r\n            >\r\n              <el-radio\r\n                v-for=\"item in typeOptions\"\r\n                :key=\"item.value\"\r\n                :label=\"item.value\"\r\n              >{{ item.label }}</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n          <el-button type=\"primary\" class=\"submit-btn\" :loading=\"btnLoading\" @click=\"handleSubmit\">\r\n            提交\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"table-section\">\r\n        <vxe-table\r\n          ref=\"xTable1\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          empty-text=\"暂无数据\"\r\n          height=\"auto\"\r\n          show-overflow\r\n          :checkbox-config=\"{checkField: 'checked'}\"\r\n          :loading=\"tbLoading\"\r\n          :row-config=\"{isCurrent: true, isHover: true }\"\r\n          class=\"cs-vxe-table\"\r\n          align=\"left\"\r\n          keep-source\r\n          stripe\r\n          :data=\"tableData\"\r\n          resizable\r\n          :edit-config=\"{trigger: 'manual', mode: 'row', showStatus: true}\"\r\n        >\r\n          <vxe-column field=\"Sys_Name\" title=\"BOM层级\" align=\"center\">\r\n            <template #default=\"{ row }\">\r\n              {{ row.Sys_Name }}\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column field=\"Display_Name\" title=\"名称\" :edit-render=\"{}\" align=\"center\">\r\n            <template #edit=\"{ row }\">\r\n              <vxe-input v-model=\"row.Display_Name\" type=\"text\" />\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column title=\"列表字段显示配置\" align=\"center\" width=\"140\">\r\n            <template #default=\"{ row }\">\r\n              <el-button type=\"text\" @click=\"handleBottomLabelClick(row)\">显示配置</el-button>\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column field=\"Is_Default_Model\" title=\"模型默认层级\" align=\"center\" :edit-render=\"{}\">\r\n            <template #edit=\"{ row }\">\r\n              <el-switch\r\n                v-model=\"row.Is_Default_Model\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n                @change=\"handleDefaultModelChange(row, $event)\"\r\n              />\r\n            </template>\r\n            <template #default=\"{ row }\">\r\n              <el-tag v-if=\"row.Is_Default_Model\" type=\"success\">是</el-tag>\r\n              <el-tag v-else type=\"danger\">否</el-tag>\r\n            </template>\r\n          </vxe-column>\r\n\r\n          <vxe-column title=\"操作\" width=\"160\" align=\"center\">\r\n            <template #default=\"{ row }\">\r\n              <template v-if=\"$refs.xTable1.isActiveByRow(row)\">\r\n                <el-button type=\"text\" @click=\"saveRowEvent(row)\">保存</el-button>\r\n                <el-button type=\"text\" @click=\"cancelRowEvent(row)\">取消</el-button>\r\n              </template>\r\n              <template v-else>\r\n                <el-button type=\"text\" @click=\"editRowEvent(row)\">编辑</el-button>\r\n              </template>\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n      </div>\r\n    </div>\r\n  </div></template>\r\n\r\n<script>\r\nimport { GetBomLevelList, SaveBomLevel } from '@/api/PRO/bom-level'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nimport store from '@/store'\r\n\r\nexport default {\r\n  name: 'PROBOMLevelConfig',\r\n  mixins: [addRouterPage],\r\n  data() {\r\n    return {\r\n      selectedType: 2,\r\n      btnLoading: false,\r\n      loading: false,\r\n      apiData: [],\r\n      typeOptions: [\r\n        { label: '二层', value: 2 },\r\n        { label: '三层', value: 3 },\r\n        { label: '四层', value: 4 },\r\n        { label: '五层', value: 5 }\r\n      ],\r\n      tableData: [],\r\n      tbLoading: false\r\n    }\r\n  },\r\n  computed: {\r\n    comName() {\r\n      if (!this.apiData || this.apiData.length === 0) return ''\r\n      return this.apiData[0].Display_Name + '清单'\r\n    },\r\n    partName() {\r\n      if (!this.apiData || this.apiData.length === 0) return ''\r\n      return this.apiData[4].Display_Name + '清单'\r\n    },\r\n    addPageArray() {\r\n      const unitPart = this.tableData.filter(item => item.Is_Enabled && +item.Code > 0)\r\n      const route = [{\r\n        path: this.$route.path + '/ComponentConfig',\r\n        hidden: true,\r\n        component: () => import('@/views/PRO/bom-setting/com-config/index'),\r\n        name: 'PROComponentConfig',\r\n        meta: { title: this.comName }\r\n      },\r\n      {\r\n        path: this.$route.path + '/part-config',\r\n        hidden: true,\r\n        component: () => import('@/views/PRO/bom-setting/part-config/index'),\r\n        name: 'PROPartsConfig',\r\n        meta: { title: this.partName }\r\n      }]\r\n      const curList = []\r\n      if (unitPart.length > 0) {\r\n        unitPart.forEach(item => {\r\n          curList.push({\r\n            path: this.$route.path + `/half-part-config${item.Code}`,\r\n            hidden: true,\r\n            component: () => import('@/views/PRO/bom-setting/half-part-config/index'),\r\n            name: 'PROHalfPartConfig' + item.Code,\r\n            meta: { title: item.Display_Name + '清单' }\r\n          })\r\n        })\r\n      }\r\n      route.splice(1, 0, ...curList)\r\n      console.log('route', route)\r\n      return route\r\n    }\r\n  },\r\n  async mounted() {\r\n    this.selectedType = 2\r\n    await this.getBomLevelList()\r\n    this.handleInitPageRoute()\r\n    console.log('this.addPageArray', this.$router.getRoutes())\r\n  },\r\n  methods: {\r\n    initPage() {\r\n      console.log('hello word 存在即合理')\r\n    },\r\n    handleBottomLabelClick(layer) {\r\n      const _name = layer.Code === '-1' ? 'PROComponentConfig' : layer.Code === '0' ? 'PROPartsConfig' : 'PROHalfPartConfig' + layer.Code\r\n      console.log('_name', _name)\r\n      const query = { pg_redirect: this.$route.name }\r\n      if (+layer.Code > 0) {\r\n        query.level = layer.Code\r\n      }\r\n      console.log(777, JSON.parse(JSON.stringify({ name: _name, query })))\r\n      this.$router.push({ name: _name, query })\r\n    },\r\n    getBomLevelList() {\r\n      this.tbLoading = true\r\n      return new Promise((resolve) => {\r\n        GetBomLevelList().then(res => {\r\n          const { IsSucceed, Message, Data } = res\r\n          if (IsSucceed) {\r\n            this.apiData = (Data || []).map(v => {\r\n              v.isEditing = false\r\n              v.originalName = v.Display_Name\r\n              return v\r\n            })\r\n\r\n            if (this.apiData && this.apiData.length > 0) {\r\n              const enabledCount = this.apiData.filter(item => !!item.Is_Enabled).length\r\n              if (enabledCount >= 2) {\r\n                this.selectedType = enabledCount\r\n              } else {\r\n                this.selectedType = 2\r\n              }\r\n            }\r\n            this.generateTableData(this.selectedType || 2, this.apiData)\r\n            this.$store.dispatch('bomInfo/clearBomLevelCache')\r\n            this.handleInitPageRoute()\r\n          } else {\r\n            this.$message({\r\n              message: Message || '获取BOM层级列表失败',\r\n              type: 'error'\r\n            })\r\n          }\r\n          resolve()\r\n        }).catch(error => {\r\n          console.error('获取BOM层级列表失败:', error)\r\n          this.$message.error('获取BOM层级列表失败')\r\n          resolve()\r\n        }).finally(() => {\r\n          this.tbLoading = false\r\n        })\r\n      })\r\n    },\r\n\r\n    generateTableData(levelCount, apiData = []) {\r\n      const levelCodeMap = {\r\n        2: ['-1', '0'],\r\n        3: ['-1', '1', '0'],\r\n        4: ['-1', '1', '2', '0'],\r\n        5: ['-1', '1', '2', '3', '0']\r\n      }\r\n\r\n      const validCodes = levelCodeMap[levelCount] || levelCodeMap[2]\r\n\r\n      apiData.forEach(item => {\r\n        item.Is_Enabled = false\r\n      })\r\n\r\n      const filteredData = apiData.filter(v => validCodes.includes(v.Code))\r\n\r\n      filteredData.forEach(item => {\r\n        item.Is_Enabled = true\r\n      })\r\n\r\n      filteredData.sort((a, b) => parseInt(a.Sort) - parseInt(b.Sort))\r\n\r\n      this.tableData = filteredData\r\n    },\r\n\r\n    handleTypeChange(value) {\r\n      if (this.apiData) {\r\n        this.generateTableData(value, this.apiData)\r\n      }\r\n    },\r\n\r\n    handleSubmit() {\r\n      const hasDefault = this.tableData.every(item => item.Is_Default_Model === false)\r\n      if (hasDefault) {\r\n        this.$message.warning('至少需要一个默认层级')\r\n        return\r\n      }\r\n\r\n      const saveData = [...this.apiData]\r\n\r\n      this.$confirm('确定要保存所有修改吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.btnLoading = true\r\n        SaveBomLevel(saveData).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message.success('保存成功')\r\n            this.getBomLevelList()\r\n          } else {\r\n            this.$message.error(res.Message || '保存失败')\r\n          }\r\n        }).catch(error => {\r\n          console.error('保存失败:', error)\r\n          this.$message.error('保存失败')\r\n        }).finally(() => {\r\n          this.btnLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('已取消保存')\r\n      })\r\n    },\r\n\r\n    handleEdit(row, rowIndex) {\r\n      this.customTableConfig.tableData.forEach(item => {\r\n        if (item !== row) {\r\n          row.isEditing = false\r\n        }\r\n      })\r\n      row.isEditing = true\r\n    },\r\n\r\n    handleSaveName(row, rowIndex) {\r\n      row.isEditing = false\r\n    },\r\n\r\n    handleCancel(row, rowIndex) {\r\n      row.Display_Name = row.originalName\r\n      row.isEditing = false\r\n    },\r\n\r\n    handleDefaultModelChange(row, value) {\r\n      this.tableData.forEach(item => {\r\n        if (item !== row) {\r\n          item.Is_Default_Model = false\r\n        }\r\n      })\r\n    },\r\n    editRowEvent(row) {\r\n      const $table = this.$refs.xTable1\r\n      $table.setEditRow(row)\r\n    },\r\n    saveRowEvent() {\r\n      const $table = this.$refs.xTable1\r\n      $table.clearEdit().then(() => {\r\n        this.loading = true\r\n        setTimeout(() => {\r\n          this.loading = false\r\n        }, 300)\r\n      })\r\n    },\r\n    cancelRowEvent(row) {\r\n      const $table = this.$refs.xTable1\r\n      $table.clearEdit().then(() => {\r\n        $table.revertData(row)\r\n      })\r\n    },\r\n    handleShowConfig(row) {\r\n      // TODO: 打开配置弹窗或其他逻辑\r\n      this.$message.info('点击了显示配置')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.bom-level-config {\r\n  .cs-z-page-main-content {\r\n    display: flex;\r\n    flex-direction: column;\r\n    flex: 1;\r\n    overflow: hidden;\r\n    height: 100%;\r\n\r\n    .query-section {\r\n      margin-bottom: 20px;\r\n      border-radius: 4px;\r\n\r\n      .query-form {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n\r\n        .query-label {\r\n         font-weight: bold;\r\n          font-size: 14px;\r\n          color: #333;\r\n          white-space: nowrap;\r\n        }\r\n\r\n        .query-select {\r\n          width: 200px;\r\n        }\r\n\r\n        .submit-btn {\r\n          margin-left: 8px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .table-section {\r\n      flex: 1;\r\n      overflow: hidden;\r\n\r\n    }\r\n  }\r\n  .query-radio-group {\r\n    width: 300px;\r\n    margin-right: 8px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2FA,SAAAA,eAAA,EAAAC,YAAA;AACA,OAAAC,aAAA;AACA,OAAAC,KAAA;AAEA;EACAC,IAAA;EACAC,MAAA,GAAAH,aAAA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;MACAC,UAAA;MACAC,OAAA;MACAC,OAAA;MACAC,WAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,SAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACAC,OAAA,WAAAA,QAAA;MACA,UAAAP,OAAA,SAAAA,OAAA,CAAAQ,MAAA;MACA,YAAAR,OAAA,IAAAS,YAAA;IACA;IACAC,QAAA,WAAAA,SAAA;MACA,UAAAV,OAAA,SAAAA,OAAA,CAAAQ,MAAA;MACA,YAAAR,OAAA,IAAAS,YAAA;IACA;IACAE,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,QAAA,QAAAT,SAAA,CAAAU,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,UAAA,KAAAD,IAAA,CAAAE,IAAA;MAAA;MACA,IAAAC,KAAA;QACAC,IAAA,OAAAC,MAAA,CAAAD,IAAA;QACAE,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAjC,IAAA;QACAkC,IAAA;UAAAC,KAAA,OAAAtB;QAAA;MACA,GACA;QACAY,IAAA,OAAAC,MAAA,CAAAD,IAAA;QACAE,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAjC,IAAA;QACAkC,IAAA;UAAAC,KAAA,OAAAnB;QAAA;MACA;MACA,IAAAoB,OAAA;MACA,IAAAjB,QAAA,CAAAL,MAAA;QACAK,QAAA,CAAAkB,OAAA,WAAAhB,IAAA;UACAe,OAAA,CAAAE,IAAA;YACAb,IAAA,EAAAP,KAAA,CAAAQ,MAAA,CAAAD,IAAA,uBAAAc,MAAA,CAAAlB,IAAA,CAAAE,IAAA;YACAI,MAAA;YACAC,SAAA,WAAAA,UAAA;cAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;gBAAA,OAAAC,uBAAA,CAAAC,OAAA;cAAA;YAAA;YACAjC,IAAA,wBAAAqB,IAAA,CAAAE,IAAA;YACAW,IAAA;cAAAC,KAAA,EAAAd,IAAA,CAAAN,YAAA;YAAA;UACA;QACA;MACA;MACAS,KAAA,CAAAgB,MAAA,CAAAC,KAAA,CAAAjB,KAAA,SAAAe,MAAA,CAAAH,OAAA;MACAM,OAAA,CAAAC,GAAA,UAAAnB,KAAA;MACA,OAAAA,KAAA;IACA;EACA;EACAoB,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACAT,MAAA,CAAA1C,YAAA;YAAAiD,QAAA,CAAAE,IAAA;YAAA,OACAT,MAAA,CAAAU,eAAA;UAAA;YACAV,MAAA,CAAAW,mBAAA;YACAd,OAAA,CAAAC,GAAA,sBAAAE,MAAA,CAAAY,OAAA,CAAAC,SAAA;UAAA;UAAA;YAAA,OAAAN,QAAA,CAAAO,IAAA;QAAA;MAAA,GAAAV,OAAA;IAAA;EACA;EACAW,OAAA;IACAC,QAAA,WAAAA,SAAA;MACAnB,OAAA,CAAAC,GAAA;IACA;IACAmB,sBAAA,WAAAA,uBAAAC,KAAA;MACA,IAAAC,KAAA,GAAAD,KAAA,CAAAxC,IAAA,mCAAAwC,KAAA,CAAAxC,IAAA,oDAAAwC,KAAA,CAAAxC,IAAA;MACAmB,OAAA,CAAAC,GAAA,UAAAqB,KAAA;MACA,IAAAC,KAAA;QAAAC,WAAA,OAAAxC,MAAA,CAAA1B;MAAA;MACA,KAAA+D,KAAA,CAAAxC,IAAA;QACA0C,KAAA,CAAAE,KAAA,GAAAJ,KAAA,CAAAxC,IAAA;MACA;MACAmB,OAAA,CAAAC,GAAA,MAAAyB,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA;QAAAtE,IAAA,EAAAgE,KAAA;QAAAC,KAAA,EAAAA;MAAA;MACA,KAAAR,OAAA,CAAAnB,IAAA;QAAAtC,IAAA,EAAAgE,KAAA;QAAAC,KAAA,EAAAA;MAAA;IACA;IACAV,eAAA,WAAAA,gBAAA;MAAA,IAAAgB,MAAA;MACA,KAAA5D,SAAA;MACA,WAAAkB,OAAA,WAAAC,OAAA;QACAlC,eAAA,GAAAmC,IAAA,WAAAyC,GAAA;UACA,IAAAC,SAAA,GAAAD,GAAA,CAAAC,SAAA;YAAAC,OAAA,GAAAF,GAAA,CAAAE,OAAA;YAAAC,IAAA,GAAAH,GAAA,CAAAG,IAAA;UACA,IAAAF,SAAA;YACAF,MAAA,CAAAjE,OAAA,IAAAqE,IAAA,QAAAC,GAAA,WAAAC,CAAA;cACAA,CAAA,CAAAC,SAAA;cACAD,CAAA,CAAAE,YAAA,GAAAF,CAAA,CAAA9D,YAAA;cACA,OAAA8D,CAAA;YACA;YAEA,IAAAN,MAAA,CAAAjE,OAAA,IAAAiE,MAAA,CAAAjE,OAAA,CAAAQ,MAAA;cACA,IAAAkE,YAAA,GAAAT,MAAA,CAAAjE,OAAA,CAAAc,MAAA,WAAAC,IAAA;gBAAA,SAAAA,IAAA,CAAAC,UAAA;cAAA,GAAAR,MAAA;cACA,IAAAkE,YAAA;gBACAT,MAAA,CAAApE,YAAA,GAAA6E,YAAA;cACA;gBACAT,MAAA,CAAApE,YAAA;cACA;YACA;YACAoE,MAAA,CAAAU,iBAAA,CAAAV,MAAA,CAAApE,YAAA,OAAAoE,MAAA,CAAAjE,OAAA;YACAiE,MAAA,CAAAW,MAAA,CAAAC,QAAA;YACAZ,MAAA,CAAAf,mBAAA;UACA;YACAe,MAAA,CAAAa,QAAA;cACAC,OAAA,EAAAX,OAAA;cACAY,IAAA;YACA;UACA;UACAxD,OAAA;QACA,GAAAyD,KAAA,WAAAC,KAAA;UACA9C,OAAA,CAAA8C,KAAA,iBAAAA,KAAA;UACAjB,MAAA,CAAAa,QAAA,CAAAI,KAAA;UACA1D,OAAA;QACA,GAAA2D,OAAA;UACAlB,MAAA,CAAA5D,SAAA;QACA;MACA;IACA;IAEAsE,iBAAA,WAAAA,kBAAAS,UAAA;MAAA,IAAApF,OAAA,GAAAqF,SAAA,CAAA7E,MAAA,QAAA6E,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,IAAAE,YAAA;QACA;QACA;QACA;QACA;MACA;MAEA,IAAAC,UAAA,GAAAD,YAAA,CAAAH,UAAA,KAAAG,YAAA;MAEAvF,OAAA,CAAA+B,OAAA,WAAAhB,IAAA;QACAA,IAAA,CAAAC,UAAA;MACA;MAEA,IAAAyE,YAAA,GAAAzF,OAAA,CAAAc,MAAA,WAAAyD,CAAA;QAAA,OAAAiB,UAAA,CAAAE,QAAA,CAAAnB,CAAA,CAAAtD,IAAA;MAAA;MAEAwE,YAAA,CAAA1D,OAAA,WAAAhB,IAAA;QACAA,IAAA,CAAAC,UAAA;MACA;MAEAyE,YAAA,CAAAE,IAAA,WAAAC,CAAA,EAAAC,CAAA;QAAA,OAAAC,QAAA,CAAAF,CAAA,CAAAG,IAAA,IAAAD,QAAA,CAAAD,CAAA,CAAAE,IAAA;MAAA;MAEA,KAAA3F,SAAA,GAAAqF,YAAA;IACA;IAEAO,gBAAA,WAAAA,iBAAA7F,KAAA;MACA,SAAAH,OAAA;QACA,KAAA2E,iBAAA,CAAAxE,KAAA,OAAAH,OAAA;MACA;IACA;IAEAiG,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,UAAA,QAAA/F,SAAA,CAAAgG,KAAA,WAAArF,IAAA;QAAA,OAAAA,IAAA,CAAAsF,gBAAA;MAAA;MACA,IAAAF,UAAA;QACA,KAAArB,QAAA,CAAAwB,OAAA;QACA;MACA;MAEA,IAAAC,QAAA,GAAAC,kBAAA,MAAAxG,OAAA;MAEA,KAAAyG,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACA3B,IAAA;MACA,GAAAvD,IAAA;QACAyE,MAAA,CAAApG,UAAA;QACAP,YAAA,CAAAgH,QAAA,EAAA9E,IAAA,WAAAyC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACA+B,MAAA,CAAApB,QAAA,CAAA8B,OAAA;YACAV,MAAA,CAAAjD,eAAA;UACA;YACAiD,MAAA,CAAApB,QAAA,CAAAI,KAAA,CAAAhB,GAAA,CAAAE,OAAA;UACA;QACA,GAAAa,KAAA,WAAAC,KAAA;UACA9C,OAAA,CAAA8C,KAAA,UAAAA,KAAA;UACAgB,MAAA,CAAApB,QAAA,CAAAI,KAAA;QACA,GAAAC,OAAA;UACAe,MAAA,CAAApG,UAAA;QACA;MACA,GAAAmF,KAAA;QACAiB,MAAA,CAAApB,QAAA,CAAA+B,IAAA;MACA;IACA;IAEAC,UAAA,WAAAA,WAAAC,GAAA,EAAAC,QAAA;MACA,KAAAC,iBAAA,CAAA7G,SAAA,CAAA2B,OAAA,WAAAhB,IAAA;QACA,IAAAA,IAAA,KAAAgG,GAAA;UACAA,GAAA,CAAAvC,SAAA;QACA;MACA;MACAuC,GAAA,CAAAvC,SAAA;IACA;IAEA0C,cAAA,WAAAA,eAAAH,GAAA,EAAAC,QAAA;MACAD,GAAA,CAAAvC,SAAA;IACA;IAEA2C,YAAA,WAAAA,aAAAJ,GAAA,EAAAC,QAAA;MACAD,GAAA,CAAAtG,YAAA,GAAAsG,GAAA,CAAAtC,YAAA;MACAsC,GAAA,CAAAvC,SAAA;IACA;IAEA4C,wBAAA,WAAAA,yBAAAL,GAAA,EAAA5G,KAAA;MACA,KAAAC,SAAA,CAAA2B,OAAA,WAAAhB,IAAA;QACA,IAAAA,IAAA,KAAAgG,GAAA;UACAhG,IAAA,CAAAsF,gBAAA;QACA;MACA;IACA;IACAgB,YAAA,WAAAA,aAAAN,GAAA;MACA,IAAAO,MAAA,QAAAC,KAAA,CAAAC,OAAA;MACAF,MAAA,CAAAG,UAAA,CAAAV,GAAA;IACA;IACAW,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAL,MAAA,QAAAC,KAAA,CAAAC,OAAA;MACAF,MAAA,CAAAM,SAAA,GAAAnG,IAAA;QACAkG,MAAA,CAAA5H,OAAA;QACA8H,UAAA;UACAF,MAAA,CAAA5H,OAAA;QACA;MACA;IACA;IACA+H,cAAA,WAAAA,eAAAf,GAAA;MACA,IAAAO,MAAA,QAAAC,KAAA,CAAAC,OAAA;MACAF,MAAA,CAAAM,SAAA,GAAAnG,IAAA;QACA6F,MAAA,CAAAS,UAAA,CAAAhB,GAAA;MACA;IACA;IACAiB,gBAAA,WAAAA,iBAAAjB,GAAA;MACA;MACA,KAAAjC,QAAA,CAAA+B,IAAA;IACA;EACA;AACA", "ignoreList": []}]}
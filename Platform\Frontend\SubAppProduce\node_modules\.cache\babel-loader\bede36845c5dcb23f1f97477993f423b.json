{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\bom-level-config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\bom-level-config\\index.vue", "mtime": 1757468112202}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetBomLevelList", "SaveBomLevel", "name", "data", "selectedType", "btnLoading", "loading", "apiData", "typeOptions", "label", "value", "tableData", "tbLoading", "created", "getBomLevelList", "methods", "_this", "Promise", "resolve", "then", "res", "IsSucceed", "Message", "Data", "map", "v", "isEditing", "originalName", "Display_Name", "length", "enabledCount", "filter", "item", "Is_Enabled", "generateTableData", "$message", "message", "type", "catch", "error", "console", "finally", "levelCount", "arguments", "undefined", "levelCodeMap", "validCodes", "for<PERSON>ach", "filteredData", "includes", "Code", "sort", "a", "b", "parseInt", "Sort", "handleTypeChange", "handleSubmit", "_this2", "<PERSON><PERSON><PERSON><PERSON>", "every", "Is_Default_Model", "warning", "saveData", "_toConsumableArray", "$confirm", "confirmButtonText", "cancelButtonText", "success", "info", "handleEdit", "row", "rowIndex", "customTableConfig", "handleSaveName", "handleCancel", "handleDefaultModelChange", "editRowEvent", "$table", "$refs", "xTable1", "setEditRow", "saveRowEvent", "_this3", "clearEdit", "setTimeout", "cancelRowEvent", "revertData"], "sources": ["src/views/PRO/bom-setting/bom-level-config/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"bom-level-config abs100 cs-z-flex-pd16-wrap\">\r\n    <div class=\"cs-z-page-main-content\">\r\n      <div class=\"query-section\">\r\n        <div class=\"query-form\">\r\n          <div>\r\n            <span class=\"query-label\">请选择BOM层级数：</span>\r\n            <el-select\r\n              v-model=\"selectedType\"\r\n              placeholder=\"请选择\"\r\n              class=\"query-select\"\r\n              @change=\"handleTypeChange\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in typeOptions\"\r\n                :key=\"item.value\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"\r\n              />\r\n            </el-select>\r\n          </div>\r\n          <el-button type=\"primary\" class=\"submit-btn\" :loading=\"btnLoading\" @click=\"handleSubmit\">\r\n            提交\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"table-section\">\r\n        <vxe-table\r\n          ref=\"xTable1\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          empty-text=\"暂无数据\"\r\n          height=\"auto\"\r\n          show-overflow\r\n          :checkbox-config=\"{checkField: 'checked'}\"\r\n          :loading=\"tbLoading\"\r\n          :row-config=\"{isCurrent: true, isHover: true }\"\r\n          class=\"cs-vxe-table\"\r\n          align=\"left\"\r\n          keep-source\r\n          stripe\r\n          :data=\"tableData\"\r\n          resizable\r\n          :edit-config=\"{trigger: 'manual', mode: 'row', showStatus: true}\"\r\n        >\r\n          <vxe-column field=\"Sys_Name\" title=\"BOM层级\" align=\"center\">\r\n            <template #default=\"{ row }\">\r\n              {{ row.Sys_Name }}\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column field=\"Display_Name\" title=\"名称\" :edit-render=\"{}\" align=\"center\">\r\n            <template #edit=\"{ row }\">\r\n              <vxe-input v-model=\"row.Display_Name\" type=\"text\" />\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column field=\"Is_Default_Model\" title=\"模型默认层级\" align=\"center\" :edit-render=\"{}\">\r\n            <template #edit=\"{ row }\">\r\n              <el-switch\r\n                v-model=\"row.Is_Default_Model\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n                @change=\"handleDefaultModelChange(row, $event)\"\r\n              />\r\n            </template>\r\n            <template #default=\"{ row }\">\r\n              <el-tag v-if=\"row.Is_Default_Model\" type=\"success\">是</el-tag>\r\n              <el-tag v-else type=\"danger\">否</el-tag>\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column title=\"操作\" width=\"160\" align=\"center\">\r\n            <template #default=\"{ row }\">\r\n              <template v-if=\"$refs.xTable1.isActiveByRow(row)\">\r\n                <el-button type=\"text\" @click=\"saveRowEvent(row)\">保存</el-button>\r\n                <el-button type=\"text\" @click=\"cancelRowEvent(row)\">取消</el-button>\r\n              </template>\r\n              <template v-else>\r\n                <el-button type=\"text\" @click=\"editRowEvent(row)\">编辑</el-button>\r\n              </template>\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n      </div>\r\n    </div>\r\n  </div></template>\r\n\r\n<script>\r\nimport { GetBomLevelList, SaveBomLevel } from '@/api/PRO/bom-level'\r\n\r\nexport default {\r\n  name: 'PROBOMLevelConfig',\r\n  data() {\r\n    return {\r\n      selectedType: 2,\r\n      btnLoading: false,\r\n      loading: false,\r\n      apiData: [],\r\n      typeOptions: [\r\n        { label: '二层', value: 2 },\r\n        { label: '三层', value: 3 },\r\n        { label: '四层', value: 4 },\r\n        { label: '五层', value: 5 }\r\n      ],\r\n      tableData: [],\r\n      tbLoading: false\r\n    }\r\n  },\r\n  created() {\r\n    this.selectedType = 2\r\n    this.getBomLevelList()\r\n  },\r\n  methods: {\r\n    getBomLevelList() {\r\n      this.tbLoading = true\r\n      return new Promise((resolve) => {\r\n        GetBomLevelList().then(res => {\r\n          const { IsSucceed, Message, Data } = res\r\n          if (IsSucceed) {\r\n            this.apiData = (Data || []).map(v => {\r\n              v.isEditing = false\r\n              v.originalName = v.Display_Name\r\n              return v\r\n            })\r\n\r\n            if (this.apiData && this.apiData.length > 0) {\r\n              const enabledCount = this.apiData.filter(item => !!item.Is_Enabled).length\r\n              if (enabledCount >= 2) {\r\n                this.selectedType = enabledCount\r\n              } else {\r\n                this.selectedType = 2\r\n              }\r\n            }\r\n\r\n            this.generateTableData(this.selectedType || 2, this.apiData)\r\n          } else {\r\n            this.$message({\r\n              message: Message || '获取BOM层级列表失败',\r\n              type: 'error'\r\n            })\r\n          }\r\n          resolve()\r\n        }).catch(error => {\r\n          console.error('获取BOM层级列表失败:', error)\r\n          this.$message.error('获取BOM层级列表失败')\r\n          resolve()\r\n        }).finally(() => {\r\n          this.tbLoading = false\r\n        })\r\n      })\r\n    },\r\n\r\n    generateTableData(levelCount, apiData = []) {\r\n      const levelCodeMap = {\r\n        2: ['-1', '0'],\r\n        3: ['-1', '1', '0'],\r\n        4: ['-1', '1', '2', '0'],\r\n        5: ['-1', '1', '2', '3', '0']\r\n      }\r\n\r\n      const validCodes = levelCodeMap[levelCount] || levelCodeMap[2]\r\n\r\n      apiData.forEach(item => {\r\n        item.Is_Enabled = false\r\n      })\r\n\r\n      const filteredData = apiData.filter(v => validCodes.includes(v.Code))\r\n\r\n      filteredData.forEach(item => {\r\n        item.Is_Enabled = true\r\n      })\r\n\r\n      filteredData.sort((a, b) => parseInt(a.Sort) - parseInt(b.Sort))\r\n\r\n      this.tableData = filteredData\r\n    },\r\n\r\n    handleTypeChange(value) {\r\n      if (this.apiData) {\r\n        this.generateTableData(value, this.apiData)\r\n      }\r\n    },\r\n\r\n    handleSubmit() {\r\n      const hasDefault = this.tableData.every(item => item.Is_Default_Model === false)\r\n      if (hasDefault) {\r\n        this.$message.warning('至少需要一个默认层级')\r\n        return\r\n      }\r\n\r\n      const saveData = [...this.apiData]\r\n\r\n      this.$confirm('确定要保存所有修改吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.btnLoading = true\r\n        SaveBomLevel(saveData).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message.success('保存成功')\r\n            this.getBomLevelList()\r\n          } else {\r\n            this.$message.error(res.Message || '保存失败')\r\n          }\r\n        }).catch(error => {\r\n          console.error('保存失败:', error)\r\n          this.$message.error('保存失败')\r\n        }).finally(() => {\r\n          this.btnLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('已取消保存')\r\n      })\r\n    },\r\n\r\n    handleEdit(row, rowIndex) {\r\n      this.customTableConfig.tableData.forEach(item => {\r\n        if (item !== row) {\r\n          row.isEditing = false\r\n        }\r\n      })\r\n      row.isEditing = true\r\n    },\r\n\r\n    handleSaveName(row, rowIndex) {\r\n      row.isEditing = false\r\n    },\r\n\r\n    handleCancel(row, rowIndex) {\r\n      row.Display_Name = row.originalName\r\n      row.isEditing = false\r\n    },\r\n\r\n    handleDefaultModelChange(row, value) {\r\n      this.tableData.forEach(item => {\r\n        if (item !== row) {\r\n          item.Is_Default_Model = false\r\n        }\r\n      })\r\n    },\r\n    editRowEvent(row) {\r\n      const $table = this.$refs.xTable1\r\n      $table.setEditRow(row)\r\n    },\r\n    saveRowEvent() {\r\n      const $table = this.$refs.xTable1\r\n      $table.clearEdit().then(() => {\r\n        this.loading = true\r\n        setTimeout(() => {\r\n          this.loading = false\r\n        }, 300)\r\n      })\r\n    },\r\n    cancelRowEvent(row) {\r\n      const $table = this.$refs.xTable1\r\n      $table.clearEdit().then(() => {\r\n        $table.revertData(row)\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.bom-level-config {\r\n  .cs-z-page-main-content {\r\n    display: flex;\r\n    flex-direction: column;\r\n    flex: 1;\r\n    overflow: hidden;\r\n    height: 100%;\r\n\r\n    .query-section {\r\n      margin-bottom: 20px;\r\n      border-radius: 4px;\r\n\r\n      .query-form {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n\r\n        .query-label {\r\n          font-size: 14px;\r\n          color: #333;\r\n          white-space: nowrap;\r\n        }\r\n\r\n                 .query-select {\r\n           width: 200px;\r\n         }\r\n\r\n         .submit-btn {\r\n           margin-left: 8px;\r\n         }\r\n      }\r\n    }\r\n\r\n       .table-section {\r\n      flex: 1;\r\n      overflow: hidden;\r\n\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuFA,SAAAA,eAAA,EAAAC,YAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;MACAC,UAAA;MACAC,OAAA;MACAC,OAAA;MACAC,WAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,SAAA;MACAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAT,YAAA;IACA,KAAAU,eAAA;EACA;EACAC,OAAA;IACAD,eAAA,WAAAA,gBAAA;MAAA,IAAAE,KAAA;MACA,KAAAJ,SAAA;MACA,WAAAK,OAAA,WAAAC,OAAA;QACAlB,eAAA,GAAAmB,IAAA,WAAAC,GAAA;UACA,IAAAC,SAAA,GAAAD,GAAA,CAAAC,SAAA;YAAAC,OAAA,GAAAF,GAAA,CAAAE,OAAA;YAAAC,IAAA,GAAAH,GAAA,CAAAG,IAAA;UACA,IAAAF,SAAA;YACAL,KAAA,CAAAT,OAAA,IAAAgB,IAAA,QAAAC,GAAA,WAAAC,CAAA;cACAA,CAAA,CAAAC,SAAA;cACAD,CAAA,CAAAE,YAAA,GAAAF,CAAA,CAAAG,YAAA;cACA,OAAAH,CAAA;YACA;YAEA,IAAAT,KAAA,CAAAT,OAAA,IAAAS,KAAA,CAAAT,OAAA,CAAAsB,MAAA;cACA,IAAAC,YAAA,GAAAd,KAAA,CAAAT,OAAA,CAAAwB,MAAA,WAAAC,IAAA;gBAAA,SAAAA,IAAA,CAAAC,UAAA;cAAA,GAAAJ,MAAA;cACA,IAAAC,YAAA;gBACAd,KAAA,CAAAZ,YAAA,GAAA0B,YAAA;cACA;gBACAd,KAAA,CAAAZ,YAAA;cACA;YACA;YAEAY,KAAA,CAAAkB,iBAAA,CAAAlB,KAAA,CAAAZ,YAAA,OAAAY,KAAA,CAAAT,OAAA;UACA;YACAS,KAAA,CAAAmB,QAAA;cACAC,OAAA,EAAAd,OAAA;cACAe,IAAA;YACA;UACA;UACAnB,OAAA;QACA,GAAAoB,KAAA,WAAAC,KAAA;UACAC,OAAA,CAAAD,KAAA,iBAAAA,KAAA;UACAvB,KAAA,CAAAmB,QAAA,CAAAI,KAAA;UACArB,OAAA;QACA,GAAAuB,OAAA;UACAzB,KAAA,CAAAJ,SAAA;QACA;MACA;IACA;IAEAsB,iBAAA,WAAAA,kBAAAQ,UAAA;MAAA,IAAAnC,OAAA,GAAAoC,SAAA,CAAAd,MAAA,QAAAc,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,IAAAE,YAAA;QACA;QACA;QACA;QACA;MACA;MAEA,IAAAC,UAAA,GAAAD,YAAA,CAAAH,UAAA,KAAAG,YAAA;MAEAtC,OAAA,CAAAwC,OAAA,WAAAf,IAAA;QACAA,IAAA,CAAAC,UAAA;MACA;MAEA,IAAAe,YAAA,GAAAzC,OAAA,CAAAwB,MAAA,WAAAN,CAAA;QAAA,OAAAqB,UAAA,CAAAG,QAAA,CAAAxB,CAAA,CAAAyB,IAAA;MAAA;MAEAF,YAAA,CAAAD,OAAA,WAAAf,IAAA;QACAA,IAAA,CAAAC,UAAA;MACA;MAEAe,YAAA,CAAAG,IAAA,WAAAC,CAAA,EAAAC,CAAA;QAAA,OAAAC,QAAA,CAAAF,CAAA,CAAAG,IAAA,IAAAD,QAAA,CAAAD,CAAA,CAAAE,IAAA;MAAA;MAEA,KAAA5C,SAAA,GAAAqC,YAAA;IACA;IAEAQ,gBAAA,WAAAA,iBAAA9C,KAAA;MACA,SAAAH,OAAA;QACA,KAAA2B,iBAAA,CAAAxB,KAAA,OAAAH,OAAA;MACA;IACA;IAEAkD,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,UAAA,QAAAhD,SAAA,CAAAiD,KAAA,WAAA5B,IAAA;QAAA,OAAAA,IAAA,CAAA6B,gBAAA;MAAA;MACA,IAAAF,UAAA;QACA,KAAAxB,QAAA,CAAA2B,OAAA;QACA;MACA;MAEA,IAAAC,QAAA,GAAAC,kBAAA,MAAAzD,OAAA;MAEA,KAAA0D,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACA9B,IAAA;MACA,GAAAlB,IAAA;QACAuC,MAAA,CAAArD,UAAA;QACAJ,YAAA,CAAA8D,QAAA,EAAA5C,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAqC,MAAA,CAAAvB,QAAA,CAAAiC,OAAA;YACAV,MAAA,CAAA5C,eAAA;UACA;YACA4C,MAAA,CAAAvB,QAAA,CAAAI,KAAA,CAAAnB,GAAA,CAAAE,OAAA;UACA;QACA,GAAAgB,KAAA,WAAAC,KAAA;UACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;UACAmB,MAAA,CAAAvB,QAAA,CAAAI,KAAA;QACA,GAAAE,OAAA;UACAiB,MAAA,CAAArD,UAAA;QACA;MACA,GAAAiC,KAAA;QACAoB,MAAA,CAAAvB,QAAA,CAAAkC,IAAA;MACA;IACA;IAEAC,UAAA,WAAAA,WAAAC,GAAA,EAAAC,QAAA;MACA,KAAAC,iBAAA,CAAA9D,SAAA,CAAAoC,OAAA,WAAAf,IAAA;QACA,IAAAA,IAAA,KAAAuC,GAAA;UACAA,GAAA,CAAA7C,SAAA;QACA;MACA;MACA6C,GAAA,CAAA7C,SAAA;IACA;IAEAgD,cAAA,WAAAA,eAAAH,GAAA,EAAAC,QAAA;MACAD,GAAA,CAAA7C,SAAA;IACA;IAEAiD,YAAA,WAAAA,aAAAJ,GAAA,EAAAC,QAAA;MACAD,GAAA,CAAA3C,YAAA,GAAA2C,GAAA,CAAA5C,YAAA;MACA4C,GAAA,CAAA7C,SAAA;IACA;IAEAkD,wBAAA,WAAAA,yBAAAL,GAAA,EAAA7D,KAAA;MACA,KAAAC,SAAA,CAAAoC,OAAA,WAAAf,IAAA;QACA,IAAAA,IAAA,KAAAuC,GAAA;UACAvC,IAAA,CAAA6B,gBAAA;QACA;MACA;IACA;IACAgB,YAAA,WAAAA,aAAAN,GAAA;MACA,IAAAO,MAAA,QAAAC,KAAA,CAAAC,OAAA;MACAF,MAAA,CAAAG,UAAA,CAAAV,GAAA;IACA;IACAW,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAL,MAAA,QAAAC,KAAA,CAAAC,OAAA;MACAF,MAAA,CAAAM,SAAA,GAAAjE,IAAA;QACAgE,MAAA,CAAA7E,OAAA;QACA+E,UAAA;UACAF,MAAA,CAAA7E,OAAA;QACA;MACA;IACA;IACAgF,cAAA,WAAAA,eAAAf,GAAA;MACA,IAAAO,MAAA,QAAAC,KAAA,CAAAC,OAAA;MACAF,MAAA,CAAAM,SAAA,GAAAjE,IAAA;QACA2D,MAAA,CAAAS,UAAA,CAAAhB,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
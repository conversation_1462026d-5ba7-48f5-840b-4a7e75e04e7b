{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\bom-level-config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\bom-level-config\\index.vue", "mtime": 1757909680919}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetBomLevelList", "SaveBomLevel", "deepClone", "addRouterPage", "mixins", "data", "apiData", "tbLoading", "selectedType", "computed", "visibleLayers", "length", "arr", "filter", "item", "idx", "Is_Enabled", "comName", "Display_Name", "partName", "addPageArray", "_this", "unitPart", "defaultData", "Code", "route", "path", "$route", "hidden", "component", "Promise", "resolve", "then", "_interopRequireWildcard", "require", "name", "meta", "title", "curList", "for<PERSON>ach", "push", "concat", "splice", "apply", "console", "log", "mounted", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getBomLevelList", "handleInitPageRoute", "$router", "getRoutes", "stop", "methods", "initPage", "handleClose", "layer", "_this3", "$confirm", "confirmButtonText", "cancelButtonText", "type", "target", "find", "l", "key", "saveBomLevelList", "catch", "$message", "message", "handleAddLayer", "canAdd", "handleInputBlur", "_this4", "flag", "payload", "map", "bottomValue", "subtitle", "color", "others", "_objectWithoutProperties", "_excluded", "error", "data1", "JSON", "stringify", "data2", "parse", "btnLoading", "res", "IsSucceed", "success", "$store", "dispatch", "Message", "finally", "info", "defaultItem", "d", "Is_Default_Model", "handleBottomLabelClick", "_name", "query", "pg_redirect", "level", "handleModelDefaultClick", "handleInputEditClick", "inputEdit", "_this5", "Data", "v", "index", "toString", "padStart", "enabledCount", "numToHan", "num", "han<PERSON><PERSON>", "getMainColor"], "sources": ["src/views/PRO/bom-setting/bom-level-config/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"bom-level-config abs100 cs-z-flex-pd16-wrap\">\r\n    <div class=\"cs-z-page-main-content\">\r\n      <div class=\"title\">\r\n        <i class=\"el-icon-warning\" />\r\n        自行配置BOM层级数，<span class=\"title-span\">最多可新增5层</span>\r\n      </div>\r\n      <div class=\"box-wrapper\">\r\n        <div\r\n          v-for=\"(layer, idx) in visibleLayers\"\r\n          :key=\"layer.key\"\r\n          class=\"box\"\r\n          :class=\"layer.color\"\r\n        >\r\n          <span v-if=\"idx !== 0 && idx !== visibleLayers.length - 1\" class=\"close-icon\" @click=\"handleClose(layer, idx)\" />\r\n          <div class=\"box-title\">BOM{{ numToHan(idx) }}层</div>\r\n          <div class=\"box-subtitle\">{{ layer.subtitle }}</div>\r\n          <div class=\"box-input\">\r\n            <el-input\r\n              v-model.trim=\"layer.title\"\r\n              maxlength=\"20\"\r\n              size=\"medium\"\r\n              class=\"cs-input\"\r\n              placeholder=\"请输入\"\r\n              @blur=\"handleInputBlur\"\r\n            />\r\n            <i class=\"el-icon-edit-outline\" />\r\n          </div>\r\n          <el-divider />\r\n          <div class=\"box-bottom\">\r\n            <div class=\"box-bottom-label\">\r\n              <span\r\n                class=\"cs-bom-btn cs-bom-btn-main\"\r\n                :style=\"{background: layer.color === 'color1' ? '#298DFF' : layer.color === 'color2' ? '#3ECC93' : layer.color === 'color3' ? '#F1B430' : layer.color === 'color4' ? '#426BD8' : '#FF7D23'}\"\r\n                @click=\"handleBottomLabelClick(layer, idx)\"\r\n              >清单配置</span>\r\n              <span\r\n                class=\"cs-bom-btn cs-bom-btn-model\"\r\n                :class=\"{selected: layer.Is_Default_Model}\"\r\n                :style=\"layer.Is_Default_Model ? {borderColor: getMainColor(layer), color: getMainColor(layer)} : {borderColor: 'transparent', color: '#999'}\"\r\n                @click=\"handleModelDefaultClick(layer)\"\r\n              >\r\n                模型默认层级\r\n                <span v-if=\"layer.Is_Default_Model\" class=\"cs-bom-btn-check\" :style=\"{color: getMainColor(layer)}\">\r\n                  <svg width=\"11\" height=\"11\" viewBox=\"0 0 11 11\">\r\n                    <rect width=\"11\" height=\"11\" rx=\"2\" fill=\"currentColor\" />\r\n                    <text x=\"1.5\" y=\"9\" font-size=\"10\" fill=\"#fff\">✓</text>\r\n                  </svg>\r\n                </span>\r\n              </span>\r\n            </div>\r\n            <span class=\"box-bottom-value\">{{ (idx+1).toString().padStart(2, '0') }}</span>\r\n          </div>\r\n        </div>\r\n        <div v-if=\"visibleLayers.length < 5\" class=\"box add-box\" @click=\"handleAddLayer\">\r\n          <div class=\"add-plus\">+</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { GetBomLevelList, SaveBomLevel } from '@/api/PRO/bom-level'\r\nimport { deepClone } from '@/utils'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nexport default {\r\n  mixins: [addRouterPage],\r\n  data() {\r\n    return {\r\n\r\n      apiData: [], // GetBomLevelList接口数据\r\n      tbLoading: false,\r\n      selectedType: 2\r\n    }\r\n  },\r\n  computed: {\r\n    visibleLayers() {\r\n      // 一层和五层始终显示，中间层根据Is_Enabled\r\n      if (!this.apiData || this.apiData.length === 0) return []\r\n      const arr = this.apiData.filter((item, idx) => {\r\n        if (idx === 0 || idx === 4) return true\r\n        return !!item.Is_Enabled\r\n      })\r\n      return arr\r\n    },\r\n    comName() {\r\n      if (!this.apiData || this.apiData.length === 0) return ''\r\n      return this.apiData[0].Display_Name + '清单'\r\n    },\r\n    partName() {\r\n      if (!this.apiData || this.apiData.length === 0) return ''\r\n      return this.apiData[4].Display_Name + '清单'\r\n    },\r\n    addPageArray() {\r\n      const unitPart = this.defaultData.filter(item => item.Is_Enabled && +item.Code > 0)\r\n      const route = [{\r\n        path: this.$route.path + '/ComponentConfig',\r\n        hidden: true,\r\n        component: () => import('@/views/PRO/bom-setting/com-config/index'),\r\n        name: 'PROComponentConfig',\r\n        meta: { title: this.comName }\r\n      },\r\n      {\r\n        path: this.$route.path + '/part-config',\r\n        hidden: true,\r\n        component: () => import('@/views/PRO/bom-setting/part-config/index'),\r\n        name: 'PROPartsConfig',\r\n        meta: { title: this.partName }\r\n      }]\r\n      const curList = []\r\n      if (unitPart.length > 0) {\r\n        unitPart.forEach(item => {\r\n          curList.push({\r\n            path: this.$route.path + `/half-part-config${item.Code}`,\r\n            hidden: true,\r\n            component: () => import('@/views/PRO/bom-setting/half-part-config/index'),\r\n            name: 'PROHalfPartConfig' + item.Code,\r\n            meta: { title: item.Display_Name + '清单' }\r\n          })\r\n        })\r\n      }\r\n      route.splice(1, 0, ...curList)\r\n      console.log('route', route)\r\n      return route\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.getBomLevelList()\r\n    this.handleInitPageRoute()\r\n    console.log('this.addPageArray', this.$router.getRoutes())\r\n  },\r\n  methods: {\r\n    initPage() {\r\n      console.log('hello word 存在即合理')\r\n    },\r\n    handleClose(layer, idx) {\r\n      this.$confirm('确定要关闭该层级吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        if (idx === 0 || idx === this.visibleLayers.length - 1) return\r\n        const target = this.apiData.find(l => l.key === layer.key)\r\n        if (target) target.Is_Enabled = false\r\n        this.saveBomLevelList()\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消关闭'\r\n        })\r\n      })\r\n    },\r\n    handleAddLayer() {\r\n      const canAdd = this.apiData.find((l, idx) => idx !== 0 && idx !== 4 && !l.Is_Enabled)\r\n      if (canAdd) {\r\n        canAdd.Is_Enabled = true\r\n        this.saveBomLevelList()\r\n      }\r\n    },\r\n    handleInputBlur() {\r\n      this.saveBomLevelList()\r\n    },\r\n    saveBomLevelList() {\r\n      // console.log('this.apiData', this.apiData)\r\n      let flag = true\r\n      const payload = this.apiData.map(item => {\r\n        const { key, title, bottomValue, subtitle, color, ...others } = item\r\n\r\n        others.Display_Name = title\r\n        if (title === '') {\r\n          flag = false\r\n        }\r\n        return others\r\n      })\r\n      if (!flag) {\r\n        this.$message.error('请输入BOM层级名称')\r\n        return\r\n      }\r\n      const data1 = JSON.stringify(this.defaultData)\r\n      const data2 = JSON.stringify(payload)\r\n      if (data1 === data2) {\r\n        console.log('没有变化')\r\n        return\r\n      }\r\n      console.log('payload', JSON.parse(JSON.stringify(payload)))\r\n\r\n      this.$confirm('确定要保存所有修改吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.btnLoading = true\r\n        SaveBomLevel(payload).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message.success('保存成功')\r\n            this.$store.dispatch('bomInfo/clearBomLevelCache')\r\n            this.getBomLevelList()\r\n          } else {\r\n            this.$message.error(res.Message || '保存失败')\r\n          }\r\n        }).catch(error => {\r\n          console.error('保存失败:', error)\r\n          this.$message.error('保存失败')\r\n        }).finally(() => {\r\n          this.btnLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('已取消保存')\r\n        this.apiData.forEach(item => {\r\n          const defaultItem = this.defaultData.find(d => d.Code === item.Code)\r\n          item.Is_Default_Model = defaultItem.Is_Default_Model\r\n        })\r\n      })\r\n\r\n      // this.handleInitPageRoute()\r\n      // 调用保存接口\r\n      // SaveBomLevelList(payload).then(...)\r\n    },\r\n    handleBottomLabelClick(layer, idx) {\r\n      const _name = layer.Code === '-1' ? 'PROComponentConfig' : layer.Code === '0' ? 'PROPartsConfig' : 'PROHalfPartConfig' + layer.Code\r\n      console.log('_name', _name)\r\n      const query = { pg_redirect: this.$route.name }\r\n      if (+layer.Code > 0) {\r\n        query.level = layer.Code\r\n      }\r\n      this.$router.push({ name: _name, query })\r\n      // 这里可加保存逻辑\r\n    },\r\n    handleModelDefaultClick(layer) {\r\n      this.apiData.forEach(l => { l.Is_Default_Model = false })\r\n      layer.Is_Default_Model = true\r\n      this.saveBomLevelList()\r\n    },\r\n    handleInputEditClick() {\r\n      this.inputEdit = true\r\n    },\r\n    getBomLevelList() {\r\n      this.tbLoading = true\r\n      return new Promise((resolve) => {\r\n        GetBomLevelList().then(res => {\r\n          const { IsSucceed, Message, Data } = res\r\n          if (IsSucceed) {\r\n            this.defaultData = deepClone(Data)\r\n            this.apiData = (Data || []).map((v, index) => {\r\n              v.key = v.Code\r\n              v.subtitle = index === 0 ? 'layer One' : index === 1 ? 'layer Two' : index === 2 ? 'layer Three' : index === 3 ? 'layer Four' : 'layer Five'\r\n              v.color = index === 0 ? 'color1' : index === 1 ? 'color2' : index === 2 ? 'color3' : index === 3 ? 'color4' : 'color5'\r\n              v.bottomValue = (index + 1).toString().padStart(2, '0')\r\n              v.title = v.Display_Name\r\n              return v\r\n            })\r\n            if (this.apiData && this.apiData.length > 0) {\r\n              const enabledCount = this.apiData.filter(item => !!item.Is_Enabled).length\r\n              if (enabledCount >= 2) {\r\n                this.selectedType = enabledCount\r\n              } else {\r\n                this.selectedType = 2\r\n              }\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: Message || '获取BOM层级列表失败',\r\n              type: 'error'\r\n            })\r\n          }\r\n          console.log('this.apiData', this.apiData)\r\n          resolve()\r\n        }).catch(error => {\r\n          console.error('获取BOM层级列表失败:', error)\r\n          this.$message.error('获取BOM层级列表失败')\r\n          resolve()\r\n        }).finally(() => {\r\n          this.tbLoading = false\r\n        })\r\n      })\r\n    },\r\n    numToHan(num) {\r\n      const hanArr = ['一', '二', '三', '四', '五']\r\n      return hanArr[num] || num\r\n    },\r\n    getMainColor(layer) {\r\n      return layer.color === 'color1' ? '#298DFF' : layer.color === 'color2' ? '#3ECC93' : layer.color === 'color3' ? '#F1B430' : layer.color === 'color4' ? '#426BD8' : '#FF7D23'\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.cs-z-page-main-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 50px 20px;\r\n  overflow: hidden;\r\n  width: 100%;\r\n\r\n}\r\n\r\n.title {\r\n  margin:100px 0 70px 0;\r\n  font-family: Microsoft YaHei, Microsoft YaHei;\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  color: #222834;\r\n\r\n  .title-span,\r\n  .el-icon-warning {\r\n    color: #FF7C19;\r\n  }\r\n}\r\n\r\n.box-wrapper {\r\n  display: flex;\r\n  overflow-x: auto;\r\n  width: 95%;\r\n}\r\n\r\n.box {\r\n  display: flex;\r\n  flex-direction: column;\r\n  box-sizing: border-box;\r\n  padding: 20px;\r\n  min-width: 280px;\r\n  height: 340px;\r\n  margin: 12px;\r\n  border-radius: 4px 4px 4px 4px;\r\n  position: relative;\r\n\r\n  .box-title {\r\n    display: flex;\r\n    justify-content: center;\r\n    height: 35px;\r\n    font-family: Microsoft YaHei, Microsoft YaHei;\r\n    font-weight: bold;\r\n    font-size: 26px;\r\n    color: #333333;\r\n    margin-top: 30px;\r\n  }\r\n\r\n  .box-subtitle {\r\n    display: flex;\r\n    justify-content: center;\r\n    height: 19px;\r\n    margin-top: 8px;\r\n    font-family: Microsoft YaHei, Microsoft YaHei;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #999999;\r\n  }\r\n\r\n  .box-input {\r\n    display: flex;\r\n    flex-wrap: nowrap;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-top: 56px;\r\n    gap: 8px;\r\n  }\r\n\r\n  .close-icon {\r\n    width: 27px;\r\n    height: 27px;\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    cursor: pointer;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    &::before {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 0;\r\n      right: 0;\r\n      width: 0;\r\n      height: 0;\r\n      border-top: 27px solid #ACD4FF;\r\n      border-right: 27px solid #ACD4FF;\r\n      border-bottom: 27px solid transparent;\r\n      border-left: 27px solid transparent;\r\n      z-index: 0;\r\n    }\r\n\r\n    &::after {\r\n      content: \"×\";\r\n      position: relative;\r\n      z-index: 1;\r\n      color: #ffffff;\r\n      font-size: 24px;\r\n      font-weight: bold;\r\n      left: 1px;\r\n      top: 1px;\r\n      pointer-events: none;\r\n    }\r\n  }\r\n  .el-icon-edit-outline{\r\n    font-size: 18px;\r\n  }\r\n\r\n  .box-bottom {\r\n    flex: 1;\r\n    margin-top: 8px;\r\n    display: flex;\r\n    align-items: center;\r\n    position: relative;\r\n\r\n    .box-bottom-label {\r\n      z-index: 1;\r\n      align-self: flex-end;\r\n      padding-bottom: 12px;\r\n\r\n    }\r\n\r\n    .box-bottom-value {\r\n      position: absolute;\r\n      top: 0;\r\n      right: 0;\r\n      z-index: 0;\r\n      font-size: 72px;\r\n      font-family: STHupo, STHupo;\r\n      font-weight: 400;\r\n      font-size: 72px;\r\n    }\r\n  }\r\n}\r\n\r\n.add-box {\r\n  background: #F4F5F6;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-width: 280px;\r\n  height: 323px;\r\n  margin: 12px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  .add-plus {\r\n    width: 100px;\r\n    height: 100px;\r\n    color: #C8C9CC;\r\n    font-size: 100px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    user-select: none;\r\n  }\r\n}\r\n\r\n.el-divider--horizontal {\r\n  color: #D9DBE2;\r\n  margin: 14px 17px;\r\n  width: unset;\r\n}\r\n\r\n.el-icon-edit-outline {\r\n  color: #8E95AA;\r\n  margin-left: 6px;\r\n}\r\n\r\n.cs-input {\r\n  border-color: unset;\r\n  outline: unset;\r\n  border: unset;\r\n  background: transparent;\r\n  box-shadow: unset;\r\n  width: 120px;\r\n  text-align: center;\r\n\r\n  &:focus {\r\n    border-color: unset;\r\n    outline: unset;\r\n    border: unset;\r\n    background: transparent;\r\n    box-shadow: unset;\r\n  }\r\n\r\n  ::v-deep .el-input__inner {\r\n    padding: 0;\r\n    font-size: 18px;\r\n    border: unset;\r\n    outline: unset;\r\n    background: transparent;\r\n    box-shadow: unset;\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n.color1 {\r\n  background: rgba(41, 141, 255, 0.12);\r\n  .close-icon::before {\r\n    border-top: 27px solid rgba(41, 141, 255, 0.11);\r\n    border-right: 27px solid rgba(41, 141, 255, 0.11);\r\n  }\r\n  .box-bottom-label {\r\n    color: #298DFF;\r\n  }\r\n  .box-bottom-value {\r\n    color: #D3E8FF;\r\n  }\r\n}\r\n\r\n.color2 {\r\n  background: rgba(62, 204, 147, 0.12);\r\n  .close-icon::before {\r\n    border-top: 27px solid #ABE9D0;\r\n    border-right: 27px solid #ABE9D0;\r\n  }\r\n  .box-bottom-label {\r\n    color: #3ECC93;\r\n  }\r\n  .box-bottom-value {\r\n    color: #D3F3E6;\r\n  }\r\n}\r\n\r\n.color3 {\r\n  background: rgba(255, 170, 0, 0.12);\r\n  .close-icon::before {\r\n    border-top: 27px solid #FEDA92;\r\n    border-right: 27px solid #FEDA92;\r\n  }\r\n  .box-bottom-label {\r\n    color: #F1B430;\r\n  }\r\n  .box-bottom-value {\r\n    color: #FFECC4;\r\n  }\r\n}\r\n\r\n.color4 {\r\n  background: rgba(66, 107, 216, 0.12);\r\n  .close-icon::before {\r\n    border-top: 27px solid rgba(66, 107, 216, 0.11);\r\n    border-right: 27px solid rgba(66, 107, 216, 0.11);\r\n  }\r\n  .box-bottom-label {\r\n    color: #426BD8;\r\n  }\r\n  .box-bottom-value {\r\n    color: rgba(66, 107, 216,0.12);\r\n  }\r\n}\r\n\r\n.color5 {\r\n  background: rgba(255, 125, 35, 0.12);\r\n  .close-icon::before {\r\n    border-top: 27px solid rgba(255, 125, 35, 0.11);\r\n    border-right: 27px solid rgba(255, 125, 35, 0.11);\r\n  }\r\n  .box-bottom-label {\r\n    color: #FF7D23;\r\n  }\r\n  .box-bottom-value {\r\n    color: rgba(255, 125, 35, 0.12);\r\n  }\r\n}\r\n\r\n.cs-bom-btn {\r\n  display: inline-block;\r\n  border-radius: 4px;\r\n  padding: 6px 12px;\r\n  font-size: 14px;\r\n  margin-right: 8px;\r\n  cursor: pointer;\r\n  user-select: none;\r\n  transition: border 0.2s;\r\n  &.cs-bom-btn-main {\r\n    color: #fff;\r\n    border: none;\r\n  }\r\n  &.cs-bom-btn-model {\r\n    background: #fff;\r\n    color: #999;\r\n    border: 1.5px solid transparent;\r\n    position: relative;\r\n    padding-right: 22px;\r\n    &.selected {\r\n      font-weight: bold;\r\n    }\r\n    .cs-bom-btn-check {\r\n      position: absolute;\r\n      top: 0px;\r\n      right: 0px;\r\n      width: 11px;\r\n      height: 11px;\r\n      background: transparent;\r\n      border-radius: 2px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      color: inherit;\r\n      z-index: 2;\r\n      svg {\r\n        display: block;\r\n      }\r\n    }\r\n  }\r\n}\r\n.cs-bom-btn:last-child {\r\n  margin-right: 0;\r\n}\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA,SAAAA,eAAA,EAAAC,YAAA;AACA,SAAAC,SAAA;AACA,OAAAC,aAAA;AACA;EACAC,MAAA,GAAAD,aAAA;EACAE,IAAA,WAAAA,KAAA;IACA;MAEAC,OAAA;MAAA;MACAC,SAAA;MACAC,YAAA;IACA;EACA;EACAC,QAAA;IACAC,aAAA,WAAAA,cAAA;MACA;MACA,UAAAJ,OAAA,SAAAA,OAAA,CAAAK,MAAA;MACA,IAAAC,GAAA,QAAAN,OAAA,CAAAO,MAAA,WAAAC,IAAA,EAAAC,GAAA;QACA,IAAAA,GAAA,UAAAA,GAAA;QACA,SAAAD,IAAA,CAAAE,UAAA;MACA;MACA,OAAAJ,GAAA;IACA;IACAK,OAAA,WAAAA,QAAA;MACA,UAAAX,OAAA,SAAAA,OAAA,CAAAK,MAAA;MACA,YAAAL,OAAA,IAAAY,YAAA;IACA;IACAC,QAAA,WAAAA,SAAA;MACA,UAAAb,OAAA,SAAAA,OAAA,CAAAK,MAAA;MACA,YAAAL,OAAA,IAAAY,YAAA;IACA;IACAE,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,QAAA,QAAAC,WAAA,CAAAV,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAE,UAAA,KAAAF,IAAA,CAAAU,IAAA;MAAA;MACA,IAAAC,KAAA;QACAC,IAAA,OAAAC,MAAA,CAAAD,IAAA;QACAE,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAC,IAAA;QACAC,IAAA;UAAAC,KAAA,OAAApB;QAAA;MACA,GACA;QACAS,IAAA,OAAAC,MAAA,CAAAD,IAAA;QACAE,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAC,IAAA;QACAC,IAAA;UAAAC,KAAA,OAAAlB;QAAA;MACA;MACA,IAAAmB,OAAA;MACA,IAAAhB,QAAA,CAAAX,MAAA;QACAW,QAAA,CAAAiB,OAAA,WAAAzB,IAAA;UACAwB,OAAA,CAAAE,IAAA;YACAd,IAAA,EAAAL,KAAA,CAAAM,MAAA,CAAAD,IAAA,uBAAAe,MAAA,CAAA3B,IAAA,CAAAU,IAAA;YACAI,MAAA;YACAC,SAAA,WAAAA,UAAA;cAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;gBAAA,OAAAC,uBAAA,CAAAC,OAAA;cAAA;YAAA;YACAC,IAAA,wBAAArB,IAAA,CAAAU,IAAA;YACAY,IAAA;cAAAC,KAAA,EAAAvB,IAAA,CAAAI,YAAA;YAAA;UACA;QACA;MACA;MACAO,KAAA,CAAAiB,MAAA,CAAAC,KAAA,CAAAlB,KAAA,SAAAgB,MAAA,CAAAH,OAAA;MACAM,OAAA,CAAAC,GAAA,UAAApB,KAAA;MACA,OAAAA,KAAA;IACA;EACA;EACAqB,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACAT,MAAA,CAAAU,eAAA;UAAA;YACAV,MAAA,CAAAW,mBAAA;YACAd,OAAA,CAAAC,GAAA,sBAAAE,MAAA,CAAAY,OAAA,CAAAC,SAAA;UAAA;UAAA;YAAA,OAAAN,QAAA,CAAAO,IAAA;QAAA;MAAA,GAAAV,OAAA;IAAA;EACA;EACAW,OAAA;IACAC,QAAA,WAAAA,SAAA;MACAnB,OAAA,CAAAC,GAAA;IACA;IACAmB,WAAA,WAAAA,YAAAC,KAAA,EAAAlD,GAAA;MAAA,IAAAmD,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAtC,IAAA;QACA,IAAAjB,GAAA,UAAAA,GAAA,KAAAmD,MAAA,CAAAxD,aAAA,CAAAC,MAAA;QACA,IAAA4D,MAAA,GAAAL,MAAA,CAAA5D,OAAA,CAAAkE,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,GAAA,KAAAT,KAAA,CAAAS,GAAA;QAAA;QACA,IAAAH,MAAA,EAAAA,MAAA,CAAAvD,UAAA;QACAkD,MAAA,CAAAS,gBAAA;MACA,GAAAC,KAAA;QACAV,MAAA,CAAAW,QAAA;UACAP,IAAA;UACAQ,OAAA;QACA;MACA;IACA;IACAC,cAAA,WAAAA,eAAA;MACA,IAAAC,MAAA,QAAA1E,OAAA,CAAAkE,IAAA,WAAAC,CAAA,EAAA1D,GAAA;QAAA,OAAAA,GAAA,UAAAA,GAAA,WAAA0D,CAAA,CAAAzD,UAAA;MAAA;MACA,IAAAgE,MAAA;QACAA,MAAA,CAAAhE,UAAA;QACA,KAAA2D,gBAAA;MACA;IACA;IACAM,eAAA,WAAAA,gBAAA;MACA,KAAAN,gBAAA;IACA;IACAA,gBAAA,WAAAA,iBAAA;MAAA,IAAAO,MAAA;MACA;MACA,IAAAC,IAAA;MACA,IAAAC,OAAA,QAAA9E,OAAA,CAAA+E,GAAA,WAAAvE,IAAA;QACA,IAAA4D,GAAA,GAAA5D,IAAA,CAAA4D,GAAA;UAAArC,KAAA,GAAAvB,IAAA,CAAAuB,KAAA;UAAAiD,WAAA,GAAAxE,IAAA,CAAAwE,WAAA;UAAAC,QAAA,GAAAzE,IAAA,CAAAyE,QAAA;UAAAC,KAAA,GAAA1E,IAAA,CAAA0E,KAAA;UAAAC,MAAA,GAAAC,wBAAA,CAAA5E,IAAA,EAAA6E,SAAA;QAEAF,MAAA,CAAAvE,YAAA,GAAAmB,KAAA;QACA,IAAAA,KAAA;UACA8C,IAAA;QACA;QACA,OAAAM,MAAA;MACA;MACA,KAAAN,IAAA;QACA,KAAAN,QAAA,CAAAe,KAAA;QACA;MACA;MACA,IAAAC,KAAA,GAAAC,IAAA,CAAAC,SAAA,MAAAxE,WAAA;MACA,IAAAyE,KAAA,GAAAF,IAAA,CAAAC,SAAA,CAAAX,OAAA;MACA,IAAAS,KAAA,KAAAG,KAAA;QACApD,OAAA,CAAAC,GAAA;QACA;MACA;MACAD,OAAA,CAAAC,GAAA,YAAAiD,IAAA,CAAAG,KAAA,CAAAH,IAAA,CAAAC,SAAA,CAAAX,OAAA;MAEA,KAAAjB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAtC,IAAA;QACAkD,MAAA,CAAAgB,UAAA;QACAjG,YAAA,CAAAmF,OAAA,EAAApD,IAAA,WAAAmE,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAlB,MAAA,CAAAL,QAAA,CAAAwB,OAAA;YACAnB,MAAA,CAAAoB,MAAA,CAAAC,QAAA;YACArB,MAAA,CAAAzB,eAAA;UACA;YACAyB,MAAA,CAAAL,QAAA,CAAAe,KAAA,CAAAO,GAAA,CAAAK,OAAA;UACA;QACA,GAAA5B,KAAA,WAAAgB,KAAA;UACAhD,OAAA,CAAAgD,KAAA,UAAAA,KAAA;UACAV,MAAA,CAAAL,QAAA,CAAAe,KAAA;QACA,GAAAa,OAAA;UACAvB,MAAA,CAAAgB,UAAA;QACA;MACA,GAAAtB,KAAA;QACAM,MAAA,CAAAL,QAAA,CAAA6B,IAAA;QACAxB,MAAA,CAAA5E,OAAA,CAAAiC,OAAA,WAAAzB,IAAA;UACA,IAAA6F,WAAA,GAAAzB,MAAA,CAAA3D,WAAA,CAAAiD,IAAA,WAAAoC,CAAA;YAAA,OAAAA,CAAA,CAAApF,IAAA,KAAAV,IAAA,CAAAU,IAAA;UAAA;UACAV,IAAA,CAAA+F,gBAAA,GAAAF,WAAA,CAAAE,gBAAA;QACA;MACA;;MAEA;MACA;MACA;IACA;IACAC,sBAAA,WAAAA,uBAAA7C,KAAA,EAAAlD,GAAA;MACA,IAAAgG,KAAA,GAAA9C,KAAA,CAAAzC,IAAA,mCAAAyC,KAAA,CAAAzC,IAAA,oDAAAyC,KAAA,CAAAzC,IAAA;MACAoB,OAAA,CAAAC,GAAA,UAAAkE,KAAA;MACA,IAAAC,KAAA;QAAAC,WAAA,OAAAtF,MAAA,CAAAQ;MAAA;MACA,KAAA8B,KAAA,CAAAzC,IAAA;QACAwF,KAAA,CAAAE,KAAA,GAAAjD,KAAA,CAAAzC,IAAA;MACA;MACA,KAAAmC,OAAA,CAAAnB,IAAA;QAAAL,IAAA,EAAA4E,KAAA;QAAAC,KAAA,EAAAA;MAAA;MACA;IACA;IACAG,uBAAA,WAAAA,wBAAAlD,KAAA;MACA,KAAA3D,OAAA,CAAAiC,OAAA,WAAAkC,CAAA;QAAAA,CAAA,CAAAoC,gBAAA;MAAA;MACA5C,KAAA,CAAA4C,gBAAA;MACA,KAAAlC,gBAAA;IACA;IACAyC,oBAAA,WAAAA,qBAAA;MACA,KAAAC,SAAA;IACA;IACA5D,eAAA,WAAAA,gBAAA;MAAA,IAAA6D,MAAA;MACA,KAAA/G,SAAA;MACA,WAAAuB,OAAA,WAAAC,OAAA;QACA/B,eAAA,GAAAgC,IAAA,WAAAmE,GAAA;UACA,IAAAC,SAAA,GAAAD,GAAA,CAAAC,SAAA;YAAAI,OAAA,GAAAL,GAAA,CAAAK,OAAA;YAAAe,IAAA,GAAApB,GAAA,CAAAoB,IAAA;UACA,IAAAnB,SAAA;YACAkB,MAAA,CAAA/F,WAAA,GAAArB,SAAA,CAAAqH,IAAA;YACAD,MAAA,CAAAhH,OAAA,IAAAiH,IAAA,QAAAlC,GAAA,WAAAmC,CAAA,EAAAC,KAAA;cACAD,CAAA,CAAA9C,GAAA,GAAA8C,CAAA,CAAAhG,IAAA;cACAgG,CAAA,CAAAjC,QAAA,GAAAkC,KAAA,uBAAAA,KAAA,uBAAAA,KAAA,yBAAAA,KAAA;cACAD,CAAA,CAAAhC,KAAA,GAAAiC,KAAA,oBAAAA,KAAA,oBAAAA,KAAA,oBAAAA,KAAA;cACAD,CAAA,CAAAlC,WAAA,IAAAmC,KAAA,MAAAC,QAAA,GAAAC,QAAA;cACAH,CAAA,CAAAnF,KAAA,GAAAmF,CAAA,CAAAtG,YAAA;cACA,OAAAsG,CAAA;YACA;YACA,IAAAF,MAAA,CAAAhH,OAAA,IAAAgH,MAAA,CAAAhH,OAAA,CAAAK,MAAA;cACA,IAAAiH,YAAA,GAAAN,MAAA,CAAAhH,OAAA,CAAAO,MAAA,WAAAC,IAAA;gBAAA,SAAAA,IAAA,CAAAE,UAAA;cAAA,GAAAL,MAAA;cACA,IAAAiH,YAAA;gBACAN,MAAA,CAAA9G,YAAA,GAAAoH,YAAA;cACA;gBACAN,MAAA,CAAA9G,YAAA;cACA;YACA;UACA;YACA8G,MAAA,CAAAzC,QAAA;cACAC,OAAA,EAAA0B,OAAA;cACAlC,IAAA;YACA;UACA;UACA1B,OAAA,CAAAC,GAAA,iBAAAyE,MAAA,CAAAhH,OAAA;UACAyB,OAAA;QACA,GAAA6C,KAAA,WAAAgB,KAAA;UACAhD,OAAA,CAAAgD,KAAA,iBAAAA,KAAA;UACA0B,MAAA,CAAAzC,QAAA,CAAAe,KAAA;UACA7D,OAAA;QACA,GAAA0E,OAAA;UACAa,MAAA,CAAA/G,SAAA;QACA;MACA;IACA;IACAsH,QAAA,WAAAA,SAAAC,GAAA;MACA,IAAAC,MAAA;MACA,OAAAA,MAAA,CAAAD,GAAA,KAAAA,GAAA;IACA;IACAE,YAAA,WAAAA,aAAA/D,KAAA;MACA,OAAAA,KAAA,CAAAuB,KAAA,4BAAAvB,KAAA,CAAAuB,KAAA,4BAAAvB,KAAA,CAAAuB,KAAA,4BAAAvB,KAAA,CAAAuB,KAAA;IACA;EACA;AACA", "ignoreList": []}]}
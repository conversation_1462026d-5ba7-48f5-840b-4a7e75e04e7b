{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\components\\processHead.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\components\\processHead.vue", "mtime": 1757572678807}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["processHead.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "processHead.vue", "sourceRoot": "src/views/PRO/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <div v-if=\"noStyle\">\r\n      <el-button type=\"primary\" @click=\"opendialog('approve')\">{{ approve }}</el-button>\r\n      <el-button v-if=\"showReject\" type=\"danger\" @click=\"opendialog('reject')\">{{ reject }}</el-button>\r\n      <el-button v-if=\"showrefuse\" type=\"danger\" @click=\"opendialog('refuse')\">{{ refuse }}</el-button>\r\n    </div>\r\n    <div v-else class=\"processhead\">\r\n      <div class=\"title\"><span class=\"span\" />{{ title }}</div>\r\n      <div>\r\n        <el-button type=\"primary\" @click=\"opendialog('approve')\">{{ approve }}</el-button>\r\n        <el-button v-if=\"showReject\" type=\"danger\" @click=\"opendialog('reject')\">{{ reject }}</el-button>\r\n        <el-button v-if=\"showrefuse\" type=\"danger\" @click=\"opendialog('refuse')\">{{ refuse }}</el-button>\r\n      </div>\r\n    </div>\r\n    <bimdialog\r\n      dialog-title=\"审批意见\"\r\n      dialog-width=\"660px\"\r\n      :visible.sync=\"showaudit\"\r\n      :hidebtn=\"false\"\r\n      append-to-body\r\n      @submitbtn=\"audit\"\r\n      @cancelbtn=\"closeaudit\"\r\n      @handleClose=\"closeaudit\"\r\n    >\r\n      <el-form ref=\"form\">\r\n        <el-form-item label=\"审批意见:\" :required=\"required\" label-width=\"80\">\r\n          <el-input v-model=\"VerificationOpinion\" type=\"textarea\" />\r\n        </el-form-item>\r\n      </el-form>\r\n    </bimdialog>\r\n  </div>\r\n</template>\r\n<script>\r\nimport bimdialog from '@/views/plm/components/dialog'\r\n// import { Verification } from '@/api/plm/processmanagement'\r\nimport { Verification } from '@/api/sys/flow'\r\nimport { closeTagView } from '@/utils'\r\nimport { baseUrl } from '@/utils/baseurl'\r\nexport default {\r\n  components: {\r\n    bimdialog\r\n  },\r\n  props: {\r\n    title: {\r\n      type: String,\r\n      default: '流程计划审批'\r\n    },\r\n    // 流程Id\r\n    processId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    approve: {\r\n      type: String,\r\n      default: '通 过'\r\n    },\r\n    reject: {\r\n      type: String,\r\n      default: '驳 回'\r\n    },\r\n    nodeRejectType: {\r\n      type: String,\r\n      default: '1'\r\n    },\r\n    nodeRejectStep: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    refuse: {\r\n      type: String,\r\n      default: '不通过'\r\n    },\r\n    showrefuse: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    beforeapprove: {\r\n      type: Function,\r\n      default: () => { return new Promise((resolve, reject) => { resolve() }) }\r\n    },\r\n    // 审批意见必填\r\n    required: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    noStyle: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    showReject: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    webId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    businessId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      showaudit: false,\r\n      type: '',\r\n      VerificationOpinion: ''\r\n    }\r\n  },\r\n  methods: {\r\n    opendialog(type) {\r\n      this.showaudit = true\r\n      this.type = type\r\n    },\r\n    closeaudit() {\r\n      this.VerificationOpinion = ''\r\n      this.showaudit = false\r\n    },\r\n    audit() {\r\n      if (this.type === 'approve') {\r\n        this.approveIt()\r\n      } else if (this.type === 'reject') {\r\n        this.rejectIt()\r\n      } else if (this.type === 'refuse') {\r\n        this.refuseIt()\r\n      }\r\n    },\r\n    // flowInstanceId:流程Id\r\n    // VerificationFinally:1:同意；2：不同意；3：驳回\r\n    // VerificationOpinion:审核意见\r\n    // nodeRejectType:驳回至0:前一步/1:第一步/2：指定节点\r\n    // nodeRejectStep:当驳回类型为2时，驳回结点code\r\n    // 同意\r\n    async approveIt() {\r\n      if (this.required && !this.VerificationOpinion && this.VerificationOpinion !== 0) {\r\n        this.$message.warning('请填写审核意见')\r\n        return\r\n      }\r\n      this.beforeapprove().then(() => {\r\n        var param = {\r\n          flowInstanceId: this.processId,\r\n          VerificationFinally: 1,\r\n          VerificationOpinion: this.VerificationOpinion,\r\n          NodeRejectType: this.nodeRejectType,\r\n          NodeRejectStep: this.nodeRejectStep,\r\n          WebId: this.webId,\r\n          BusinessDateld: this.businessId,\r\n          FormDataJson: '',\r\n          PlateForm_Url: baseUrl()\r\n        }\r\n        Verification(param).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '提交成功',\r\n              type: 'success'\r\n            })\r\n            this.$emit('afterapproval', 'approve')\r\n            this.showaudit = false\r\n            // this.cancel()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    // 驳回\r\n    async rejectIt() {\r\n      if (this.required && !this.VerificationOpinion && this.VerificationOpinion !== 0) {\r\n        this.$message.warning('请填写审核意见')\r\n        return\r\n      }\r\n      var param = {\r\n        flowInstanceId: this.processId,\r\n        VerificationFinally: 2,\r\n        VerificationOpinion: this.VerificationOpinion,\r\n        NodeRejectType: this.nodeRejectType,\r\n        NodeRejectStep: this.nodeRejectStep,\r\n        WebId: this.webId,\r\n        BusinessDateld: this.businessId,\r\n        FormDataJson: '',\r\n        PlateForm_Url: baseUrl()\r\n      }\r\n      Verification(param).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '提交成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('afterapproval', 'reject')\r\n          this.showaudit = false\r\n          // this.cancel()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 不同意\r\n    async refuseIt() {\r\n      if (this.required && !this.VerificationOpinion && this.VerificationOpinion !== 0) {\r\n        this.$message.warning('请填写审核意见')\r\n        return\r\n      }\r\n      var param = {\r\n        flowInstanceId: this.processId,\r\n        VerificationFinally: 2,\r\n        VerificationOpinion: this.VerificationOpinion,\r\n        NodeRejectType: this.nodeRejectType,\r\n        NodeRejectStep: this.nodeRejectStep\r\n      }\r\n      Verification(param).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '提交成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('afterapproval', 'refuse')\r\n          this.showaudit = false\r\n          // this.cancel()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 关闭当前页\r\n    cancel() {\r\n      closeTagView(this.$store, this.$route)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scope lang=\"scss\">\r\n.processhead{\r\n    width:100%;\r\n    height:64px;\r\n    background:#fff;\r\n    line-height:24px;\r\n    display:flex;\r\n    justify-content:space-between;\r\n    box-shadow: 0px 1px 3px 1px rgba(20,35,78,0.08);\r\n    margin-bottom:15px;\r\n    // border:1px solid #000;\r\n    padding:20px 16px;\r\n    .title{\r\n        font-size: 18px;\r\n        font-weight: bold;\r\n        color: rgba(34,40,52,0.85);\r\n    }\r\n    .span{\r\n        background-image: linear-gradient(180deg, #71B3FF 0%, #298DFF 100%);\r\n        width:4px;\r\n        height:14px;\r\n        display:inline-block;\r\n        border-radius:3px;\r\n        margin-right:8px;\r\n    }\r\n}\r\n</style>\r\n"]}]}
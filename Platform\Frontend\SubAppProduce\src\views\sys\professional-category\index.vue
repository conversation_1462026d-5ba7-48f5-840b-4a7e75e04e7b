<template>
  <div>
    <div class="page-container">
      <el-row type="flex" justify="space-between" style="margin-bottom: 16px">
        <el-col :span="4">
          <el-button v-if="false" type="primary" @click="addProfession">新增专业</el-button>
        </el-col>
        <el-col :span="20">
          <el-row type="flex" justify="end">
            <el-input v-model="searchValue" placeholder="请输入关键字" style="width:250px " clearable />
            <div>
              <el-button type="primary" style="margin-left: 16px" @click="getData">搜索</el-button>
            </div>
          </el-row>
        </el-col>
      </el-row>

      <div class="list-wrapper">
        <el-table
          ref="table"
          tablecode="plm_professionaltype_list_pro"
          :custom-param="{ is_System: false, typeid: '', name: searchValue, companyId: companyId }"
          @get-selection-data="getSelectionData"
          @getbutton="getClick"
        />
      </div>
    </div>
    <bimdialog ref="dialog" @getData="getData" />
    <nodeList ref="nodeList" @getData="getData" />
  </div>
</template>
<script>
import table from '@/views/plm/components/table'
import bimdialog from './dialog'
import nodeList from './nodeList'
import { GetProfessionalDelete } from '@/api/plm/settings'
import addRouterPage from '@/mixins/add-router-page/index'
export default {
  name: 'ProfessionalCategoryList',
  components: {
    'el-table': table,
    bimdialog,
    nodeList
  },
  mixins: [addRouterPage],
  data() {
    return {
      companyId: localStorage.getItem('Last_Working_Object_Id'),
      searchValue: '',
      addPageArray: [
        {
          path: 'unit-template-setting',
          hidden: true,
          component: () => import('@/views/sys/professional-category/unitPartTemp.vue'),
          name: 'SYSUnitPartTemp',
          meta: { title: '专用模板配置' }
        },
        {
          path: 'template-setting',
          hidden: true,
          component: () => import('@/views/sys/professional-category/templateSetting'),
          name: 'TemplateSetting',
          meta: { title: '专用模板配置' }
        },
        {
          path: 'template-setting-lj',
          hidden: true,
          component: () => import('@/views/sys/professional-category/templateSettingLj'),
          name: 'TemplateSettingLj',
          meta: { title: '零件模板配置' }
        },
        {
          path: this.$route.path + '/category',
          hidden: true,
          component: () => import('@/views/sys/professional-category/category/index.vue'),
          name: 'ProfessionalCategoryListInfo',
          meta: { title: '零构件类型' }
        }
      ]
    }
  },
  methods: {
    getSelectionData() {},
    getData() {
      this.$refs.table.refresh()
    },
    addProfession() {
      this.$refs.dialog.handleOpen('add')
    },
    edit(item, row) {
      if (row.row.is_system === true) {
        this.$message({
          type: 'warning',
          message: '该类别属于系统级别，不可操作'
        })
        return false
      }
      this.$refs.dialog.handleOpen('edit', row.row)
    },
    delete(item, row) {
      if (row.row.is_system === true) {
        this.$message({
          type: 'warning',
          message: '该类别属于系统级别，不可操作'
        })
        return false
      }
      this.$confirm(' 确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          GetProfessionalDelete({ id: row.row.id }).then((res) => {
            if (res.IsSucceed === true) {
              this.$message({
                type: 'success',
                message: '删除成功'
              })
              this.getData()
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    getClick(item, row) {
      switch (item) {
        case 'btnedit':
          this.edit(item, row)
          break
        case 'btndelete':
          this.delete(item, row)
          break
        case 'jdedit':
          this.$refs.nodeList.handleOpen(true, row.row)
          break
        case 'unitPartCode':
          this.$router.push({
            name: 'SYSUnitPartTemp',
            query: {
              pg_redirect: this.$route.name,
              name: row.row.name,
              unit: row.row.unit,
              steel_unit: row.row.steel_unit
            }
          })
          break
        case 'mbedit':
          this.$router.push(
            { name: 'TemplateSetting',
              query: {
                pg_redirect: this.$route.name,
                typeCode: row.row.code,
                materialCode: row.row.materialcode,
                name: row.row.name,
                unit: row.row.unit,
                steel_unit: row.row.steel_unit
              }
            })
          break
        case 'ljedit':
          this.$router.push(
            { name: 'TemplateSettingLj',
              query: {
                pg_redirect: this.$route.name,
                typeCode: row.row.code,
                materialCode: row.row.materialcode,
                name: row.row.name,
                unit: row.row.unit,
                steel_unit: row.row.steel_unit
              }
            })
          break
        case 'gjedit':
          this.$router.push(
            { name: 'ProfessionalCategoryListInfo',
              query: {
                pg_redirect: this.$route.name,
                typeCode: row.row.code,
                materialCode: row.row.materialcode,
                name: row.row.name,
                unit: row.row.unit,
                steel_unit: row.row.steel_unit,
                typeId: row.row.id
              }
            })
          break
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.page-container{
  margin:16px;
  background: #fff;
  padding:16px;
  box-sizing: border-box;
}
</style>

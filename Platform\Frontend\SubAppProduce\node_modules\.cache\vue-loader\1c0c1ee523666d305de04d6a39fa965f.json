{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\ItemDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\ItemDialog.vue", "mtime": 1756109159675}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ItemDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ItemDialog.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup/components/Dialog", "sourcesContent": ["<template>\n  <div>\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"120px\">\n      <el-row>\n        <el-col :span=\"24\">\n          <el-form-item label=\"检查项内容\" prop=\"Check_Content\">\n            <el-input v-model=\"form.Check_Content\" />\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item label=\"合格标准\" prop=\"Eligibility_Criteria\">\n            <el-input v-model=\"form.Eligibility_Criteria\" />\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item style=\"text-align: right\">\n            <el-button @click=\"$emit('close')\">关 闭</el-button>\n            <el-button\n              type=\"primary\"\n              @click=\"handleSubmit('form')\"\n            >确 定</el-button>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n  </div>\n</template>\n\n<script>\nimport { AddCheckItem } from '@/api/PRO/factorycheck'\nimport { EntityCheckItem } from '@/api/PRO/factorycheck'\nimport { SaveCheckItem } from '@/api/PRO/factorycheck'\nexport default {\n  data() {\n    return {\n      check_object_id: '',\n      form: {},\n      rules: {\n        Check_Content: [{ required: true, message: '请填写完整表单', trigger: 'blur' }],\n        Eligibility_Criteria: [{ required: true, message: '请填写完整表单', trigger: 'blur' }]\n      },\n      title: '',\n      editInfo: {}\n    }\n  },\n  mounted() {},\n  methods: {\n    init(title, Id, data) {\n      this.title = title\n      if (title == '新增') {\n        this.Check_Object_Id = Id\n      } else {\n        this.Check_Object_Id = Id\n        this.editInfo = data\n        console.log(this.editInfo)\n        this.getEntityCheckType(data)\n      }\n    },\n    async addCheckType() {\n      await AddCheckItem({\n        ...this.form,\n        Check_Object_Id: this.Check_Object_Id\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            type: 'success',\n            message: '保存成功'\n          })\n          this.$emit('close')\n          this.dialogData = {}\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    getEntityCheckType(data) {\n      console.log(data)\n      EntityCheckItem({ id: data.Id }).then((res) => {\n        if (res.IsSucceed) {\n          console.log(res.Data)\n          this.form = res.Data[0]\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    editCheckType() {\n      SaveCheckItem({\n        Id: this.editInfo.Id,\n        ...this.form,\n        Check_Object_Id: this.Check_Object_Id\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            type: 'success',\n            message: '编辑成功'\n          })\n          this.$emit('close')\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    handleSubmit(form) {\n      this.$refs[form].validate((valid) => {\n        if (valid) {\n          this.title == '新增' ? this.addCheckType() : this.editCheckType()\n        } else {\n          return false\n        }\n      })\n    }\n  }\n}\n</script>\n\n  <style scoped></style>\n"]}]}
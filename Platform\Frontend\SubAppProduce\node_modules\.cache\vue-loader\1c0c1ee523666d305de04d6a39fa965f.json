{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\ItemDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\ItemDialog.vue", "mtime": 1757468112766}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ItemDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ItemDialog.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup/components/Dialog", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"120px\">\r\n      <el-row>\r\n        <el-col :span=\"24\">\r\n          <el-form-item label=\"检查项内容\" prop=\"Check_Content\">\r\n            <el-input v-model=\"form.Check_Content\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"24\">\r\n          <el-form-item label=\"合格标准\" prop=\"Eligibility_Criteria\">\r\n            <el-input v-model=\"form.Eligibility_Criteria\" maxlength=\"100\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"24\">\r\n          <el-form-item style=\"text-align: right\">\r\n            <el-button @click=\"$emit('close')\">关 闭</el-button>\r\n            <el-button\r\n              type=\"primary\"\r\n              @click=\"handleSubmit('form')\"\r\n            >确 定</el-button>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { AddCheckItem } from '@/api/PRO/factorycheck'\r\nimport { EntityCheckItem } from '@/api/PRO/factorycheck'\r\nimport { SaveCheckItem } from '@/api/PRO/factorycheck'\r\nexport default {\r\n  data() {\r\n    return {\r\n      Bom_Level: '',\r\n      check_object_id: '',\r\n      form: {},\r\n      rules: {\r\n        Check_Content: [{ required: true, message: '请填写完整表单', trigger: 'blur' }],\r\n        Eligibility_Criteria: [{ required: true, message: '请填写完整表单', trigger: 'blur' }]\r\n      },\r\n      title: '',\r\n      editInfo: {}\r\n    }\r\n  },\r\n  mounted() {},\r\n  methods: {\r\n    init(title, checkType, data) {\r\n      this.title = title\r\n      this.Check_Object_Id = checkType.Id\r\n      this.Bom_Level = checkType.Code\r\n      if (title === '编辑') {\r\n        this.editInfo = data\r\n        console.log(this.editInfo)\r\n        this.getEntityCheckType(data)\r\n      }\r\n    },\r\n    async addCheckType() {\r\n      await AddCheckItem({\r\n        ...this.form,\r\n        Check_Object_Id: this.Check_Object_Id,\r\n        Bom_Level: this.Bom_Level\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            type: 'success',\r\n            message: '保存成功'\r\n          })\r\n          this.$emit('close')\r\n          this.dialogData = {}\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getEntityCheckType(data) {\r\n      EntityCheckItem({ id: data.Id }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.form = res.Data[0]\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    editCheckType() {\r\n      SaveCheckItem({\r\n        Id: this.editInfo.Id,\r\n        ...this.form,\r\n        Check_Object_Id: this.Check_Object_Id,\r\n        Bom_Level: this.Bom_Level\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            type: 'success',\r\n            message: '编辑成功'\r\n          })\r\n          this.$emit('close')\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleSubmit(form) {\r\n      this.$refs[form].validate((valid) => {\r\n        if (valid) {\r\n          this.title === '新增' ? this.addCheckType() : this.editCheckType()\r\n        } else {\r\n          return false\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n  <style scoped></style>\r\n"]}]}
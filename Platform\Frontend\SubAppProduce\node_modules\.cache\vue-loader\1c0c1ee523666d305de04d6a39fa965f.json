{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\ItemDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\Dialog\\ItemDialog.vue", "mtime": 1757921869177}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ItemDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ItemDialog.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup/components/Dialog", "sourcesContent": ["<template>\n  <div>\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"120px\">\n      <el-row>\n        <el-col :span=\"24\">\n          <el-form-item label=\"检查项内容\" prop=\"Check_Content\">\n            <el-input v-model=\"form.Check_Content\" />\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item label=\"合格标准\" prop=\"Eligibility_Criteria\">\n            <el-input v-model=\"form.Eligibility_Criteria\" maxlength=\"100\" />\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item style=\"text-align: right\">\n            <el-button @click=\"$emit('close')\">关 闭</el-button>\n            <el-button\n              type=\"primary\"\n              @click=\"handleSubmit('form')\"\n            >确 定</el-button>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n  </div>\n</template>\n\n<script>\nimport { AddCheckItem } from '@/api/PRO/factorycheck'\nimport { EntityCheckItem } from '@/api/PRO/factorycheck'\nimport { SaveCheckItem } from '@/api/PRO/factorycheck'\nexport default {\n  data() {\n    return {\n      Bom_Level: '',\n      check_object_id: '',\n      form: {},\n      rules: {\n        Check_Content: [{ required: true, message: '请填写完整表单', trigger: 'blur' }],\n        Eligibility_Criteria: [{ required: true, message: '请填写完整表单', trigger: 'blur' }]\n      },\n      title: '',\n      editInfo: {}\n    }\n  },\n  mounted() {},\n  methods: {\n    init(title, checkType, data) {\n      this.title = title\n      this.Check_Object_Id = checkType.Id\n      this.Bom_Level = checkType.Code\n      if (title === '编辑') {\n        this.editInfo = data\n        console.log(this.editInfo)\n        this.getEntityCheckType(data)\n      }\n    },\n    async addCheckType() {\n      await AddCheckItem({\n        ...this.form,\n        Check_Object_Id: this.Check_Object_Id,\n        Bom_Level: this.Bom_Level\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            type: 'success',\n            message: '保存成功'\n          })\n          this.$emit('refresh')\n          this.$emit('close')\n          this.dialogData = {}\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    getEntityCheckType(data) {\n      EntityCheckItem({ id: data.Id }).then((res) => {\n        if (res.IsSucceed) {\n          this.form = res.Data[0]\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    editCheckType() {\n      SaveCheckItem({\n        Id: this.editInfo.Id,\n        ...this.form,\n        Check_Object_Id: this.Check_Object_Id,\n        Bom_Level: this.Bom_Level\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            type: 'success',\n            message: '编辑成功'\n          })\n          this.$emit('refresh')\n          this.$emit('close')\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    handleSubmit(form) {\n      this.$refs[form].validate((valid) => {\n        if (valid) {\n          this.title === '新增' ? this.addCheckType() : this.editCheckType()\n        } else {\n          return false\n        }\n      })\n    }\n  }\n}\n</script>\n\n  <style scoped></style>\n"]}]}
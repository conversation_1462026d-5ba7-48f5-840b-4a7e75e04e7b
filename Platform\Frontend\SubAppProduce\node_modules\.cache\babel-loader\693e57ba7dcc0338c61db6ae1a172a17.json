{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\index.vue", "mtime": 1758266753125}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["MainPage", "mapGetters", "GetBOMInfo", "getBomCode", "name", "components", "data", "bomList", "pgLoading", "activeName", "computed", "_objectSpread", "isCom", "hasUnitPart", "isVersionFour", "created", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "_yield$GetBOMInfo", "list", "wrap", "_callee$", "_context", "prev", "next", "sent", "stop", "methods", "handleClick", "tab", "event", "console", "log", "changeTab", "_this2", "_callee2", "_callee2$", "_context2", "getTbConfigInfo", "fetchData"], "sources": ["src/views/PRO/plan-production/task-list/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"container abs100\">\r\n    <el-tabs v-model=\"activeName\" @tab-click=\"changeTab\">\r\n      <!-- <el-tab-pane label=\"构件任务单\" name=\"com\" />\r\n      <el-tab-pane label=\"部件任务单\" name=\"unitPart\" />\r\n      <el-tab-pane label=\"零件任务单\" name=\"part\" /> -->\r\n      <el-tab-pane v-for=\"item in bomList\" :key=\"item.Code\" :label=\"item.Display_Name\" :name=\"item.Code\" />\r\n    </el-tabs>\r\n    <div class=\"main-wrapper\">\r\n      <component :is=\"'MainPage'\" :key=\"activeName\" :has-unit-part=\"hasUnitPart\" :page-type=\"activeName\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport MainPage from './mainPage'\r\nimport { mapGetters } from 'vuex'\r\nimport { GetBOMInfo, getBomCode } from '@/views/PRO/bom-setting/utils'\r\nexport default {\r\n  name: 'PROTaskList',\r\n  components: {\r\n    MainPage\r\n  },\r\n  data() {\r\n    return {\r\n      bomList: [],\r\n      pgLoading: false,\r\n      activeName: getBomCode('-1')\r\n    }\r\n  },\r\n  computed: {\r\n\r\n    isCom() {\r\n      return this.activeName === getBomCode('-1')\r\n    },\r\n    ...mapGetters('tenant', ['isVersionFour']),\r\n    hasUnitPart() {\r\n      return this.isVersionFour\r\n    }\r\n  },\r\n  async created() {\r\n    const { list } = await GetBOMInfo()\r\n    this.bomList = list || []\r\n  },\r\n  methods: {\r\n    handleClick(tab, event) {\r\n      console.log(tab, event)\r\n    },\r\n    async changeTab(tab) {\r\n      this.pgLoading = true\r\n      this.activeName = tab.name\r\n      await this.getTbConfigInfo()\r\n      this.fetchData(1)\r\n    },\r\n    getTbConfigInfo() {\r\n\r\n    },\r\n    fetchData() {\r\n\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.container {\r\n  padding: 16px;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .top-x {\r\n    padding: 0 16px;\r\n    margin-bottom: 16px;\r\n    background-color: #ffffff;\r\n\r\n    .top-inner {\r\n      display: flex;\r\n      position: relative;\r\n      overflow: hidden;\r\n\r\n      &:after {\r\n        content: \"\";\r\n        position: absolute;\r\n        left: 0;\r\n        bottom: 0;\r\n        width: 100%;\r\n        height: 2px;\r\n        background-color: #e4e7ed;\r\n        z-index: 1;\r\n      }\r\n\r\n      .item {\r\n        color: #999999;\r\n        display: inline-block;\r\n        padding: 20px 38px;\r\n\r\n        &:hover {\r\n          cursor: pointer;\r\n        }\r\n      }\r\n\r\n      .cs-item-bar {\r\n        display: inline-block;\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 10px;\r\n        height: 2px;\r\n        width: 140px;\r\n        background-color: #409eff;\r\n        z-index: 2;\r\n        transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);\r\n      }\r\n\r\n      .active {\r\n        font-weight: 500;\r\n        color: #298dff;\r\n      }\r\n\r\n      ::v-deep {\r\n        .el-badge__content.is-fixed {\r\n          right: 78px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .main-wrapper{\r\n    flex: 1;\r\n  }\r\n  .el-tabs{\r\n    margin-bottom: 16px;\r\n    background-color: #fff;\r\n    padding-left: 16px;\r\n  }\r\n}\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAeA,OAAAA,QAAA;AACA,SAAAC,UAAA;AACA,SAAAC,UAAA,EAAAC,UAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAL,QAAA,EAAAA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;MACAC,UAAA,EAAAN,UAAA;IACA;EACA;EACAO,QAAA,EAAAC,aAAA,CAAAA,aAAA;IAEAC,KAAA,WAAAA,MAAA;MACA,YAAAH,UAAA,KAAAN,UAAA;IACA;EAAA,GACAF,UAAA;IACAY,WAAA,WAAAA,YAAA;MACA,YAAAC,aAAA;IACA;EAAA,EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAAC,iBAAA,EAAAC,IAAA;MAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACAzB,UAAA;UAAA;YAAAmB,iBAAA,GAAAI,QAAA,CAAAG,IAAA;YAAAN,IAAA,GAAAD,iBAAA,CAAAC,IAAA;YACAN,KAAA,CAAAT,OAAA,GAAAe,IAAA;UAAA;UAAA;YAAA,OAAAG,QAAA,CAAAI,IAAA;QAAA;MAAA,GAAAT,OAAA;IAAA;EACA;EACAU,OAAA;IACAC,WAAA,WAAAA,YAAAC,GAAA,EAAAC,KAAA;MACAC,OAAA,CAAAC,GAAA,CAAAH,GAAA,EAAAC,KAAA;IACA;IACAG,SAAA,WAAAA,UAAAJ,GAAA;MAAA,IAAAK,MAAA;MAAA,OAAApB,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAmB,SAAA;QAAA,OAAApB,mBAAA,GAAAK,IAAA,UAAAgB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAd,IAAA,GAAAc,SAAA,CAAAb,IAAA;YAAA;cACAU,MAAA,CAAA7B,SAAA;cACA6B,MAAA,CAAA5B,UAAA,GAAAuB,GAAA,CAAA5B,IAAA;cAAAoC,SAAA,CAAAb,IAAA;cAAA,OACAU,MAAA,CAAAI,eAAA;YAAA;cACAJ,MAAA,CAAAK,SAAA;YAAA;YAAA;cAAA,OAAAF,SAAA,CAAAX,IAAA;UAAA;QAAA,GAAAS,QAAA;MAAA;IACA;IACAG,eAAA,WAAAA,gBAAA,GAEA;IACAC,SAAA,WAAAA,UAAA,GAEA;EACA;AACA", "ignoreList": []}]}
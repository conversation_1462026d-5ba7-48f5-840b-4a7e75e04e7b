{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\index.vue", "mtime": 1757468127974}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQoNCmltcG9ydCBUcmVlRGF0YSBmcm9tICcuL2NvbXBvbmVudC9UcmVlRGF0YScNCmltcG9ydCBBZGQgZnJvbSAnLi9jb21wb25lbnQvQWRkJw0KaW1wb3J0IHsgRGVsZXRlQ29tcG9uZW50VHlwZSwgR2V0Q29tcG9uZW50VHlwZUVudGl0eSwgU2F2ZVByb0JpbUNvbXBvbmVudFR5cGUgfSBmcm9tICdAL2FwaS9QUk8vY29tcG9uZW50LXR5cGUnDQppbXBvcnQgeyBHZXRCT01JbmZvIH0gZnJvbSAnQC92aWV3cy9QUk8vYm9tLXNldHRpbmcvdXRpbHMnDQppbXBvcnQgeyBEZWxldGVQYXJ0VHlwZSwgR2V0UGFydFR5cGVFbnRpdHksIFNhdmVQYXJ0VHlwZSB9IGZyb20gJ0AvYXBpL1BSTy9wYXJ0VHlwZScNCmltcG9ydCB7IEdldEFsbEVudGl0aWVzIH0gZnJvbSAnQC9hcGkvUFJPL3NldHRpbmdzJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdQcm9mZXNzaW9uYWxDYXRlZ29yeUxpc3RJbmZvJywNCiAgY29tcG9uZW50czogew0KICAgIFRyZWVEYXRhLA0KICAgIEFkZA0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBib21MaXN0OiBbXSwNCiAgICAgIHR5cGVDb2RlOiAnJywNCiAgICAgIHR5cGVJZDogJycsDQogICAgICBsZXZlbDogMSwNCiAgICAgIGFkZExldmVsOiB1bmRlZmluZWQsDQogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHN1Ym1pdExvYWRpbmc6IGZhbHNlLA0KICAgICAgZGVsZXRlTG9hZGluZzogZmFsc2UsDQogICAgICBzaG93Rm9ybTogZmFsc2UsDQogICAgICBpc0RlZmF1bHQ6IGZhbHNlLA0KICAgICAgaGFzQ2hpbGRyZW5Ob2RlOiB0cnVlLA0KICAgICAgY3VycmVudENvbXBvbmVudDogJycsDQogICAgICBhY3RpdmVUeXBlOiAnLTEnLA0KICAgICAgcGFyZW50SWQ6ICcnLA0KICAgICAgdGl0bGU6ICcnLA0KICAgICAgZm9ybTogew0KICAgICAgICBOYW1lOiAnJywNCiAgICAgICAgQ29kZTogJycsDQogICAgICAgIElzX0NvbXBvbmVudDogJycsDQogICAgICAgIExlYWRfVGltZTogMA0KICAgICAgfSwNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIE5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5ZCN56ewJywgdHJpZ2dlcjogJ2JsdXInIH0NCiAgICAgICAgXSwNCiAgICAgICAgQ29kZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXnvJbnoIEnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICBJc19Db21wb25lbnQ6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup5piv5ZCm55u05Y+R5Lu2JywgdHJpZ2dlcjogJ2NoYW5nZScgfQ0KICAgICAgICBdLA0KICAgICAgICBMZWFkX1RpbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5ZGo5pyfJywgdHJpZ2dlcjogJ2JsdXInIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIElzX0NvbXBvbmVudDogJycNCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgbGV2ZWxOYW1lKCkgew0KICAgICAgcmV0dXJuIHRoaXMubGV2ZWwgPT09IDEgPyAn5LiA57qnJyA6ICh0aGlzLmxldmVsID09PSAyID8gJ+S6jOe6pycgOiAodGhpcy5sZXZlbCA9PT0gMyA/ICfkuInnuqcnIDogJycpKQ0KICAgIH0sDQogICAgaXNDb21wKCkgew0KICAgICAgcmV0dXJuIHRoaXMuYWN0aXZlVHlwZSA9PT0gJy0xJw0KICAgIH0sDQogICAgc2hvd0RpcmVjdCgpIHsNCiAgICAgIHJldHVybiB0cnVlLy8gdGhpcy5hY3RpdmVUeXBlICE9PSAnMCcNCiAgICB9DQogIH0sDQogIGFzeW5jIGNyZWF0ZWQoKSB7DQogICAgYXdhaXQgdGhpcy5nZXRQcm9mZXNzaW9uKCkNCiAgICBjb25zdCB7IGxpc3QgfSA9IGF3YWl0IEdldEJPTUluZm8oKQ0KICAgIHRoaXMuYm9tTGlzdCA9IGxpc3QNCiAgICAvLyBUcmVlRGF0YSDnu4Tku7bkvJrlnKjoh6rlt7HnmoQgbW91bnRlZCDkuK3oh6rliqjosIPnlKggZmV0Y2hEYXRhKCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGFkZE5leHQoKSB7DQogICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSAnQWRkJw0KICAgICAgdGhpcy5hZGRMZXZlbCA9IHRoaXMubGV2ZWwgKyAxDQogICAgICB0aGlzLnRpdGxlID0gYOaWsOWinuS4i+S4gOe6p2ANCiAgICAgIHRoaXMucGFyZW50SWQgPSB0aGlzLmZvcm0uSWQNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIGFzeW5jIGdldFByb2Zlc3Npb24oKSB7DQogICAgICBjb25zdCByZXMgPSBhd2FpdCBHZXRBbGxFbnRpdGllcyh7DQogICAgICAgIGNvbXBhbnlJZDogbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ0xhc3RfV29ya2luZ19PYmplY3RfSWQnKSwNCiAgICAgICAgaXNfU3lzdGVtOiBmYWxzZQ0KICAgICAgfSkNCiAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgIGNvbnN0IHsNCiAgICAgICAgICBDb2RlLA0KICAgICAgICAgIElkDQogICAgICAgIH0gPSByZXMuRGF0YT8uRGF0YT8uZmluZChpdGVtID0+IGl0ZW0uQ29kZSA9PT0gJ1N0ZWVsJykgfHwge30NCiAgICAgICAgdGhpcy50eXBlQ29kZSA9IENvZGUNCiAgICAgICAgdGhpcy50eXBlSWQgPSBJZA0KICAgICAgICBjb25zb2xlLmxvZyh0aGlzLnR5cGVDb2RlLCB0aGlzLnR5cGVJZCkNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIHNob3dSaWdodCh2KSB7DQogICAgLy8gICB0aGlzLnNob3dGb3JtID0gdg0KICAgIC8vIH0sDQogICAgaGFuZGxlQ2xpY2sodGFiLCBldmVudCkgew0KICAgICAgdGhpcy5zaG93Rm9ybSA9IGZhbHNlDQogICAgICBjb25zb2xlLmxvZyh0YWIsIGV2ZW50KQ0KICAgICAgLy8g55Sx5LqO5L2/55So5LqGIGtlee+8jOe7hOS7tuS8mumHjeaWsOWIm+W7uuW5tuWcqCBtb3VudGVkIOS4reiHquWKqOiwg+eUqCBmZXRjaERhdGEoKQ0KICAgIH0sDQogICAgc3VibWl0KCkgew0KICAgICAgdGhpcy4kcmVmc1snZm9ybSddLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAoIXZhbGlkKSB7DQogICAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICAgIH0NCiAgICAgICAgaWYgKHRoaXMuSXNfQ29tcG9uZW50ICE9PSB0aGlzLmZvcm0uSXNfQ29tcG9uZW50KSB7DQogICAgICAgICAgdGhpcy4kY29uZmlybSgn55u05Y+R5Lu25bGe5oCn5LiN5Lya5ZCM5q2l5Yiw5bey5a+85YWl5p6E5Lu25riF5Y2V5Lit77yM56Gu6K6k5L+u5pS577yfJywgJ+aPkOekuicsIHsNCiAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgICAgICB0aGlzLnN1Ym1pdENvbmZpcm0oKQ0KICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICB0eXBlOiAnaW5mbycsDQogICAgICAgICAgICAgIG1lc3NhZ2U6ICflt7Llj5bmtojkv67mlLknDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0pDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5zdWJtaXRDb25maXJtKCkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHN1Ym1pdENvbmZpcm0oKSB7DQogICAgICB0aGlzLnN1Ym1pdExvYWRpbmcgPSB0cnVlDQogICAgICBjb25zdCBzdWJtaXRPYmogPSB7IC4uLnRoaXMuZm9ybSB9DQogICAgICBzdWJtaXRPYmouSXNfRGlyZWN0ID0gIXN1Ym1pdE9iai5Jc19Db21wb25lbnQNCiAgICAgIHN1Ym1pdE9iai5Qcm9mZXNzaW9uYWxfSWQgPSB0aGlzLnR5cGVJZA0KICAgICAgY29uc3QgcG9zdEZuID0gdGhpcy5pc0NvbXAgPyBTYXZlUHJvQmltQ29tcG9uZW50VHlwZSA6IFNhdmVQYXJ0VHlwZQ0KDQogICAgICBwb3N0Rm4oc3VibWl0T2JqKS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiAn5L+u5pS55oiQ5YqfJywNCiAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy5nZXRUcmVlRGF0YSgpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KS5maW5hbGx5KF8gPT4gew0KICAgICAgICB0aGlzLnN1Ym1pdExvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGdldFRyZWVEYXRhKCkgew0KICAgICAgdGhpcy4kcmVmc1sndHJlZSddLmZldGNoRGF0YSgpDQogICAgfSwNCiAgICBhZGRGaXJzdCgpIHsNCiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9ICdBZGQnDQogICAgICB0aGlzLnRpdGxlID0gJ+aWsOWinuexu+WIqycNCiAgICAgIHRoaXMuYWRkTGV2ZWwgPSAxDQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgfSwNCiAgICBoYW5kbGVDbG9zZSgpIHsNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgfSwNCiAgICBub2RlQ2xpY2sobm9kZSkgew0KICAgICAgdGhpcy5zaG93Rm9ybSA9IHRydWUNCiAgICAgIHRoaXMubGV2ZWwgPSBub2RlLmxldmVsDQogICAgICB0aGlzLmhhc0NoaWxkcmVuTm9kZSA9IG5vZGUuY2hpbGROb2Rlcy5sZW5ndGggPiAwDQogICAgICB0aGlzLmdldEluZm8obm9kZS5kYXRhLklkKQ0KICAgIH0sDQogICAgYXN5bmMgZ2V0SW5mbyhpZCkgew0KICAgICAgY29uc3QgcG9zdEZuID0gdGhpcy5pc0NvbXAgPyBHZXRDb21wb25lbnRUeXBlRW50aXR5IDogR2V0UGFydFR5cGVFbnRpdHkNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IHBvc3RGbih7IGlkIH0pDQogICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICBPYmplY3QuYXNzaWduKHRoaXMuZm9ybSwgcmVzLkRhdGEpDQogICAgICAgIGlmICh0aGlzLmlzQ29tcCkgew0KICAgICAgICAgIHRoaXMuaXNEZWZhdWx0ID0gZmFsc2UNCiAgICAgICAgICB0aGlzLklzX0NvbXBvbmVudCA9IHJlcy5EYXRhLklzX0NvbXBvbmVudA0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuaXNEZWZhdWx0ID0gISFyZXMuRGF0YS5Jc19EZWZhdWx0DQogICAgICAgICAgdGhpcy5mb3JtLklzX0NvbXBvbmVudCA9ICFyZXMuRGF0YS5Jc19EaXJlY3QNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlRGVsZXRlKCkgew0KICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm5Yig6Zmk5b2T5YmN57G75YirPycsICfmj5DnpLonLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgfSkudGhlbihhc3luYygpID0+IHsNCiAgICAgICAgdGhpcy5kZWxldGVMb2FkaW5nID0gdHJ1ZQ0KICAgICAgICBsZXQgcG9zdEZuDQogICAgICAgIGxldCBvYmogPSB7fQ0KICAgICAgICBpZiAodGhpcy5pc0NvbXApIHsNCiAgICAgICAgICBwb3N0Rm4gPSBEZWxldGVDb21wb25lbnRUeXBlDQogICAgICAgICAgb2JqID0gew0KICAgICAgICAgICAgaWRzOiB0aGlzLmZvcm0uSWQNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgcG9zdEZuID0gRGVsZXRlUGFydFR5cGUNCiAgICAgICAgICBvYmogPSB7DQogICAgICAgICAgICBpZDogdGhpcy5mb3JtLklkDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHBvc3RGbihvYmopLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywNCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKnyEnDQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgdGhpcy5nZXRUcmVlRGF0YSgpDQogICAgICAgICAgICB0aGlzLiRyZWZzWyd0cmVlJ10ucmVzZXRLZXkodGhpcy5mb3JtLklkKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9KS5maW5hbGx5KF8gPT4gew0KICAgICAgICAgIHRoaXMuZGVsZXRlTG9hZGluZyA9IGZhbHNlDQogICAgICAgICAgdGhpcy5zaG93Rm9ybSA9IGZhbHNlDQogICAgICAgIH0pDQogICAgICB9KS5jYXRjaCgoZSkgPT4gew0KICAgICAgICBjb25zb2xlLmxvZyhlLCAzMzEzKQ0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICB0eXBlOiAnaW5mbycsDQogICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iOWIoOmZpCcNCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/bom-setting/structure-type-config", "sourcesContent": ["<template>\r\n  <div class=\"container abs100\">\r\n\r\n    <div class=\"card-x\">\r\n      <el-tabs v-model=\"activeType\" type=\"card\" @tab-click=\"handleClick\">\r\n        <el-tab-pane v-for=\"item in bomList\" :key=\"item.Code\" :label=\"item.Display_Name\" :name=\"item.Code\" />\r\n      </el-tabs>\r\n      <div class=\"card-x-content\">\r\n        <tree-data ref=\"tree\" :key=\"activeType\" :active-type=\"activeType\" :type-code=\"typeCode\" :type-id=\"typeId\" @nodeClick=\"nodeClick\" @AddFirst=\"addFirst\" />\r\n        <div class=\"right-card\">\r\n          <el-form v-if=\"showForm\" ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n            <el-form-item :label=\"`${levelName}大类名称`\" prop=\"Name\">\r\n              <el-input v-model.trim=\"form.Name\" clearable maxlength=\"50\" />\r\n            </el-form-item>\r\n            <el-form-item :label=\"`${levelName}大类编号`\" prop=\"Code\">\r\n              <el-input v-model=\"form.Code\" disabled />\r\n            </el-form-item>\r\n            <el-form-item label=\"生产周期\" prop=\"Lead_Time\">\r\n              <el-input-number v-model.number=\"form.Lead_Time\" class=\"cs-number-btn-hidden w100\" clearable />\r\n            </el-form-item>\r\n            <el-form-item v-if=\"showDirect\" label=\"直发件\" prop=\"Is_Component\">\r\n              <el-radio-group v-model=\"form.Is_Component\">\r\n                <el-radio :label=\"true\">否</el-radio>\r\n                <el-radio :label=\"false\">是</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <el-form-item>\r\n              <el-button v-if=\"level<3\" type=\"text\" icon=\"el-icon-plus\" @click=\"addNext\">新增下一级</el-button>\r\n            </el-form-item>\r\n            <el-form-item>\r\n              <el-button type=\"primary\" :loading=\"submitLoading\" :disabled=\"isDefault\" @click=\"submit\">保存</el-button>\r\n              <el-button type=\"danger\" :loading=\"deleteLoading\" :disabled=\"hasChildrenNode || isDefault\" @click=\"handleDelete\">删除</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <!-- <el-radio-group v-model=\"activeType\" @change=\"changeType\">\r\n        <el-radio-button v-for=\"item in bomList\" :key=\"item.Code\" :label=\"item.Code\">{{ item.Display_Name }}大类</el-radio-button>\r\n      </el-radio-group> -->\r\n\r\n      <!-- <tree-data ref=\"tree\" :key=\"88\" :type-code=\"typeCode\" :type-id=\"typeId\" @nodeClick=\"nodeClick\" @AddFirst=\"addFirst\" @showRight=\"showRight\" />\r\n      <div class=\"right-card\">\r\n        <el-form v-if=\"showForm\" ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n          <el-form-item :label=\"`${levelName}大类名称`\" prop=\"Name\">\r\n            <el-input v-model.trim=\"form.Name\" clearable maxlength=\"50\" />\r\n          </el-form-item>\r\n          <el-form-item :label=\"`${levelName}大类编号`\" prop=\"Code\">\r\n            <el-input v-model=\"form.Code\" disabled />\r\n          </el-form-item>\r\n          <el-form-item label=\"生产周期\" prop=\"Lead_Time\">\r\n            <el-input-number v-model.number=\"form.Lead_Time\" class=\"cs-number-btn-hidden w100\" clearable />\r\n          </el-form-item>\r\n          <el-form-item label=\"直发件\" prop=\"Is_Component\">\r\n            <el-radio-group v-model=\"form.Is_Component\">\r\n              <el-radio :label=\"true\">否</el-radio>\r\n              <el-radio :label=\"false\">是</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button v-if=\"level<3\" type=\"text\" icon=\"el-icon-plus\" @click=\"addNext\">新增下一级</el-button>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" :loading=\"submitLoading\" @click=\"submit\">保存</el-button>\r\n            <el-button type=\"danger\" :loading=\"deleteLoading\" :disabled=\"hasChildrenNode\" @click=\"handleDelete\">删除</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div> -->\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"title\"\r\n      class=\"plm-custom-dialog\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"30%\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        ref=\"content\"\r\n        :type-id=\"typeId\"\r\n        :add-level=\"addLevel\"\r\n        :parent-id=\"parentId\"\r\n        :active-type=\"activeType\"\r\n        :type-code=\"typeCode\"\r\n        :is-comp=\"isComp\"\r\n        :show-direct=\"showDirect\"\r\n        @close=\"handleClose\"\r\n        @getTreeList=\"getTreeData\"\r\n      />\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport TreeData from './component/TreeData'\r\nimport Add from './component/Add'\r\nimport { DeleteComponentType, GetComponentTypeEntity, SaveProBimComponentType } from '@/api/PRO/component-type'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\nimport { DeletePartType, GetPartTypeEntity, SavePartType } from '@/api/PRO/partType'\r\nimport { GetAllEntities } from '@/api/PRO/settings'\r\n\r\nexport default {\r\n  name: 'ProfessionalCategoryListInfo',\r\n  components: {\r\n    TreeData,\r\n    Add\r\n  },\r\n  data() {\r\n    return {\r\n      bomList: [],\r\n      typeCode: '',\r\n      typeId: '',\r\n      level: 1,\r\n      addLevel: undefined,\r\n      dialogVisible: false,\r\n      submitLoading: false,\r\n      deleteLoading: false,\r\n      showForm: false,\r\n      isDefault: false,\r\n      hasChildrenNode: true,\r\n      currentComponent: '',\r\n      activeType: '-1',\r\n      parentId: '',\r\n      title: '',\r\n      form: {\r\n        Name: '',\r\n        Code: '',\r\n        Is_Component: '',\r\n        Lead_Time: 0\r\n      },\r\n      rules: {\r\n        Name: [\r\n          { required: true, message: '请输入名称', trigger: 'blur' }\r\n        ],\r\n        Code: [\r\n          { required: true, message: '请输入编码', trigger: 'blur' }\r\n        ],\r\n        Is_Component: [\r\n          { required: true, message: '请选择是否直发件', trigger: 'change' }\r\n        ],\r\n        Lead_Time: [\r\n          { required: true, message: '请输入周期', trigger: 'blur' }\r\n        ]\r\n      },\r\n      Is_Component: ''\r\n    }\r\n  },\r\n  computed: {\r\n    levelName() {\r\n      return this.level === 1 ? '一级' : (this.level === 2 ? '二级' : (this.level === 3 ? '三级' : ''))\r\n    },\r\n    isComp() {\r\n      return this.activeType === '-1'\r\n    },\r\n    showDirect() {\r\n      return true// this.activeType !== '0'\r\n    }\r\n  },\r\n  async created() {\r\n    await this.getProfession()\r\n    const { list } = await GetBOMInfo()\r\n    this.bomList = list\r\n    // TreeData 组件会在自己的 mounted 中自动调用 fetchData()\r\n  },\r\n  methods: {\r\n    addNext() {\r\n      this.currentComponent = 'Add'\r\n      this.addLevel = this.level + 1\r\n      this.title = `新增下一级`\r\n      this.parentId = this.form.Id\r\n      this.dialogVisible = true\r\n    },\r\n    async getProfession() {\r\n      const res = await GetAllEntities({\r\n        companyId: localStorage.getItem('Last_Working_Object_Id'),\r\n        is_System: false\r\n      })\r\n      if (res.IsSucceed) {\r\n        const {\r\n          Code,\r\n          Id\r\n        } = res.Data?.Data?.find(item => item.Code === 'Steel') || {}\r\n        this.typeCode = Code\r\n        this.typeId = Id\r\n        console.log(this.typeCode, this.typeId)\r\n      }\r\n    },\r\n    // showRight(v) {\r\n    //   this.showForm = v\r\n    // },\r\n    handleClick(tab, event) {\r\n      this.showForm = false\r\n      console.log(tab, event)\r\n      // 由于使用了 key，组件会重新创建并在 mounted 中自动调用 fetchData()\r\n    },\r\n    submit() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (!valid) {\r\n          return false\r\n        }\r\n        if (this.Is_Component !== this.form.Is_Component) {\r\n          this.$confirm('直发件属性不会同步到已导入构件清单中，确认修改？', '提示', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n            this.submitConfirm()\r\n          }).catch(() => {\r\n            this.$message({\r\n              type: 'info',\r\n              message: '已取消修改'\r\n            })\r\n          })\r\n        } else {\r\n          this.submitConfirm()\r\n        }\r\n      })\r\n    },\r\n    submitConfirm() {\r\n      this.submitLoading = true\r\n      const submitObj = { ...this.form }\r\n      submitObj.Is_Direct = !submitObj.Is_Component\r\n      submitObj.Professional_Id = this.typeId\r\n      const postFn = this.isComp ? SaveProBimComponentType : SavePartType\r\n\r\n      postFn(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '修改成功',\r\n            type: 'success'\r\n          })\r\n          this.getTreeData()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.submitLoading = false\r\n      })\r\n    },\r\n    getTreeData() {\r\n      this.$refs['tree'].fetchData()\r\n    },\r\n    addFirst() {\r\n      this.currentComponent = 'Add'\r\n      this.title = '新增类别'\r\n      this.addLevel = 1\r\n      this.dialogVisible = true\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    nodeClick(node) {\r\n      this.showForm = true\r\n      this.level = node.level\r\n      this.hasChildrenNode = node.childNodes.length > 0\r\n      this.getInfo(node.data.Id)\r\n    },\r\n    async getInfo(id) {\r\n      const postFn = this.isComp ? GetComponentTypeEntity : GetPartTypeEntity\r\n      const res = await postFn({ id })\r\n      if (res.IsSucceed) {\r\n        Object.assign(this.form, res.Data)\r\n        if (this.isComp) {\r\n          this.isDefault = false\r\n          this.Is_Component = res.Data.Is_Component\r\n        } else {\r\n          this.isDefault = !!res.Data.Is_Default\r\n          this.form.Is_Component = !res.Data.Is_Direct\r\n        }\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    handleDelete() {\r\n      this.$confirm('是否删除当前类别?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async() => {\r\n        this.deleteLoading = true\r\n        let postFn\r\n        let obj = {}\r\n        if (this.isComp) {\r\n          postFn = DeleteComponentType\r\n          obj = {\r\n            ids: this.form.Id\r\n          }\r\n        } else {\r\n          postFn = DeletePartType\r\n          obj = {\r\n            id: this.form.Id\r\n          }\r\n        }\r\n        postFn(obj).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.getTreeData()\r\n            this.$refs['tree'].resetKey(this.form.Id)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        }).finally(_ => {\r\n          this.deleteLoading = false\r\n          this.showForm = false\r\n        })\r\n      }).catch((e) => {\r\n        console.log(e, 3313)\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-width: 998px;\r\n  overflow: hidden;\r\n\r\n  .top-x {\r\n    line-height: 48px;\r\n    height: 48px;\r\n  }\r\n\r\n  .card-x {\r\n    padding: 16px;\r\n    overflow: hidden;\r\n    // background-color: #FFFFFF;\r\n    height: 100%;\r\n    width: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    .el-tabs{\r\n      width: 100%;\r\n      padding: 16px 16px 0 16px;\r\n      background-color: #FFFFFF;\r\n    }\r\n    .card-x-content{\r\n      display: flex;\r\n      flex: 1;\r\n      overflow: hidden;\r\n    }\r\n\r\n    .right-card {\r\n      display: flex;\r\n      flex-direction: column;\r\n      flex: 1;\r\n      border-radius: 4px;\r\n      background-color: #FFFFFF;\r\n      .el-form{\r\n        width: 50%;\r\n        margin:  auto;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
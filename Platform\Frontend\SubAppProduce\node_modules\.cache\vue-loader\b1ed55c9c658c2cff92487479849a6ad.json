{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\index.vue", "mtime": 1756109946518}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQoNCmltcG9ydCBUcmVlRGF0YSBmcm9tICcuL2NvbXBvbmVudC9UcmVlRGF0YScNCmltcG9ydCBBZGQgZnJvbSAnLi9jb21wb25lbnQvQWRkJw0KaW1wb3J0IHsgRGVsZXRlQ29tcG9uZW50VHlwZSwgR2V0Q29tcG9uZW50VHlwZUVudGl0eSwgU2F2ZVByb0JpbUNvbXBvbmVudFR5cGUgfSBmcm9tICdAL2FwaS9QUk8vY29tcG9uZW50LXR5cGUnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ1Byb2Zlc3Npb25hbENhdGVnb3J5TGlzdEluZm8nLA0KICBjb21wb25lbnRzOiB7DQogICAgVHJlZURhdGEsDQogICAgQWRkDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHR5cGVDb2RlOiAnU3RlZWwnLA0KICAgICAgdHlwZUlkOiAnZDI4YTgxYTAtY2UzMS00YjU2LThlMjItYjg2OTIyNjY4ODk0JywNCiAgICAgIGxldmVsOiAxLA0KICAgICAgYWRkTGV2ZWw6IHVuZGVmaW5lZCwNCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgc3VibWl0TG9hZGluZzogZmFsc2UsDQogICAgICBkZWxldGVMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHNob3dGb3JtOiBmYWxzZSwNCiAgICAgIGhhc0NoaWxkcmVuTm9kZTogdHJ1ZSwNCiAgICAgIGN1cnJlbnRDb21wb25lbnQ6ICcnLA0KICAgICAgcGFyZW50SWQ6ICcnLA0KICAgICAgdGl0bGU6ICcnLA0KICAgICAgZm9ybTogew0KICAgICAgICBOYW1lOiAnJywNCiAgICAgICAgQ29kZTogJycsDQogICAgICAgIElzX0NvbXBvbmVudDogJycsDQogICAgICAgIExlYWRfVGltZTogMA0KICAgICAgfSwNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIE5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5ZCN56ewJywgdHJpZ2dlcjogJ2JsdXInIH0NCiAgICAgICAgXSwNCiAgICAgICAgQ29kZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXnvJbnoIEnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICBJc19Db21wb25lbnQ6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ<PERSON>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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/bom-setting/structure-type-config", "sourcesContent": ["<template>\r\n  <div class=\"container abs100\">\r\n\r\n    <div class=\"card-x\">\r\n      <tree-data ref=\"tree\" :type-code=\"typeCode\" :type-id=\"typeId\" @nodeClick=\"nodeClick\" @AddFirst=\"addFirst\" @showRight=\"showRight\" />\r\n      <div class=\"right-card\">\r\n        <el-form v-if=\"showForm\" ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n          <el-form-item :label=\"`${levelName}大类名称`\" prop=\"Name\">\r\n            <el-input v-model.trim=\"form.Name\" clearable maxlength=\"50\" />\r\n          </el-form-item>\r\n          <el-form-item :label=\"`${levelName}大类编号`\" prop=\"Code\">\r\n            <el-input v-model=\"form.Code\" disabled />\r\n          </el-form-item>\r\n          <el-form-item label=\"生产周期\" prop=\"Lead_Time\">\r\n            <el-input-number v-model.number=\"form.Lead_Time\" class=\"cs-number-btn-hidden w100\" clearable />\r\n          </el-form-item>\r\n          <el-form-item label=\"直发件\" prop=\"Is_Component\">\r\n            <el-radio-group v-model=\"form.Is_Component\">\r\n              <el-radio :label=\"true\">否</el-radio>\r\n              <el-radio :label=\"false\">是</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button v-if=\"level<3\" type=\"text\" icon=\"el-icon-plus\" @click=\"addNext\">新增下一级</el-button>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" :loading=\"submitLoading\" @click=\"submit\">保存</el-button>\r\n            <el-button type=\"danger\" :loading=\"deleteLoading\" :disabled=\"hasChildrenNode\" @click=\"handleDelete\">删除</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"title\"\r\n      class=\"plm-custom-dialog\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"30%\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        ref=\"content\"\r\n        :add-level=\"addLevel\"\r\n        :parent-id=\"parentId\"\r\n        @close=\"handleClose\"\r\n        @getTreeList=\"getTreeData\"\r\n      />\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport TreeData from './component/TreeData'\r\nimport Add from './component/Add'\r\nimport { DeleteComponentType, GetComponentTypeEntity, SaveProBimComponentType } from '@/api/PRO/component-type'\r\n\r\nexport default {\r\n  name: 'ProfessionalCategoryListInfo',\r\n  components: {\r\n    TreeData,\r\n    Add\r\n  },\r\n  data() {\r\n    return {\r\n      typeCode: 'Steel',\r\n      typeId: 'd28a81a0-ce31-4b56-8e22-b86922668894',\r\n      level: 1,\r\n      addLevel: undefined,\r\n      dialogVisible: false,\r\n      submitLoading: false,\r\n      deleteLoading: false,\r\n      showForm: false,\r\n      hasChildrenNode: true,\r\n      currentComponent: '',\r\n      parentId: '',\r\n      title: '',\r\n      form: {\r\n        Name: '',\r\n        Code: '',\r\n        Is_Component: '',\r\n        Lead_Time: 0\r\n      },\r\n      rules: {\r\n        Name: [\r\n          { required: true, message: '请输入名称', trigger: 'blur' }\r\n        ],\r\n        Code: [\r\n          { required: true, message: '请输入编码', trigger: 'blur' }\r\n        ],\r\n        Is_Component: [\r\n          { required: true, message: '请选择是否直发件', trigger: 'change' }\r\n        ],\r\n        Lead_Time: [\r\n          { required: true, message: '请输入周期', trigger: 'blur' }\r\n        ]\r\n      },\r\n      Is_Component: ''\r\n    }\r\n  },\r\n  computed: {\r\n    levelName() {\r\n      return this.level === 1 ? '一级' : (this.level === 2 ? '二级' : (this.level === 3 ? '三级' : ''))\r\n    }\r\n  },\r\n  methods: {\r\n    addNext() {\r\n      this.currentComponent = 'Add'\r\n      this.addLevel = this.level + 1\r\n      this.title = `新增下一级`\r\n      this.parentId = this.form.Id\r\n      this.dialogVisible = true\r\n    },\r\n    showRight(v) {\r\n      this.showForm = v\r\n    },\r\n    submit() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (!valid) {\r\n          return false\r\n        }\r\n        if (this.Is_Component != this.form.Is_Component) {\r\n          this.$confirm('直发件属性不会同步到已导入构件清单中，确认修改？', '提示', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n            this.submitConfirm()\r\n          }).catch(() => {\r\n            this.$message({\r\n              type: 'info',\r\n              message: '已取消修改'\r\n            })\r\n          })\r\n        } else {\r\n          this.submitConfirm()\r\n        }\r\n      })\r\n    },\r\n    submitConfirm() {\r\n      this.submitLoading = true\r\n      SaveProBimComponentType(this.form).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '修改成功',\r\n            type: 'success'\r\n          })\r\n          this.getTreeData()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.submitLoading = false\r\n      })\r\n    },\r\n    getTreeData() {\r\n      this.$refs['tree'].fetchData()\r\n    },\r\n    addFirst() {\r\n      this.currentComponent = 'Add'\r\n      this.title = '新增类别'\r\n      this.addLevel = 1\r\n      this.dialogVisible = true\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    nodeClick(node) {\r\n      this.level = node.level\r\n      this.hasChildrenNode = node.childNodes.length > 0\r\n      this.getInfo(node.data.Id)\r\n    },\r\n    getInfo(id) {\r\n      GetComponentTypeEntity({\r\n        id\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          Object.assign(this.form, res.Data)\r\n          this.Is_Component = res.Data.Is_Component\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    handleDelete() {\r\n      this.$confirm('是否删除当前类别?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.deleteLoading = true\r\n        DeleteComponentType({\r\n          ids: this.form.Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.getTreeData()\r\n            this.$refs['tree'].resetKey(this.form.Id)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        }).finally(_ => {\r\n          this.deleteLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.container {\r\n  display: flex;\r\n  padding: 0 16px 16px;\r\n  flex-direction: column;\r\n  min-width: 998px;\r\n  overflow: hidden;\r\n\r\n  .top-x {\r\n    line-height: 48px;\r\n    height: 48px;\r\n  }\r\n\r\n  .card-x {\r\n    height: calc(100% - 48px);\r\n    display: flex;\r\n\r\n    .right-card {\r\n      display: flex;\r\n      flex-direction: column;\r\n      flex: 1;\r\n      border-radius: 4px;\r\n      background-color: #FFFFFF;\r\n      .el-form{\r\n        width: 50%;\r\n        margin:  auto;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
<template>
  <div class="form-wrapper">
    <div class="form-x">
      <el-form ref="form" :model="form" label-width="120px">
        <el-form-item label="是否启用" prop="enable">
          <el-radio-group v-model="form.enable">
            <el-radio :label="false">否</el-radio>
            <el-radio :label="true">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <template v-if="form.enable">
          <el-form-item label="识别类型" prop="identifyAttr">
            <el-radio-group v-model="form.identifyAttr">
              <el-radio :label="1">{{ currentBomName }}名称前缀</el-radio>
              <el-radio :label="2">{{ currentBomName }}规格前缀</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-for="(element,index) in list" :key="index" :show-message="false" :label="element.Part_Type_Name" prop="mainPart">
            <el-input v-model.trim="form['item'+index]" :placeholder="`请输入（多个使用'${splitSymbol}'隔开），单个配置不超过10个字符`" clearable @blur="mainBlur" />
          </el-form-item>
        </template>
      </el-form>
    </div>
    <div class="btn-x">
      <el-button @click="$emit('close')">取 消</el-button>
      <el-button type="primary" :loading="btnLoading" @click="handleSubmit">确 定</el-button>
    </div>
  </div>
</template>

<script>

import { uniqueArr } from '@/utils'
import {
  GetFactoryPartTypeIndentifySetting,
  SavePartTypeIdentifySetting
} from '@/api/PRO/partType'

const SPLITVALUE = '|'

export default {
  props: {
    bomList: {
      type: Array,
      default: () => []
    },
    level: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      form: {
        enable: false,
        identifyAttr: 1 // 默认为零件名称前缀
      },
      list: [],
      splitSymbol: SPLITVALUE,
      btnLoading: false
    }
  },
  computed: {
    currentBomName() {
      return this.bomList.find(item => +item.Code === this.level)?.Display_Name
    }
  },
  mounted() {
    this.getTypeList()
  },
  methods: {
    async getTypeList() {
      GetFactoryPartTypeIndentifySetting({
        Part_Grade: 0
      }).then(res => {
        if (res.IsSucceed) {
          const { Is_Enabled, Setting_List } = res.Data
          this.form.enable = Is_Enabled
          this.list = Setting_List.map((v, index) => {
            this.$set(this.form, 'item' + index, v.Prefixs || '')
            return v
          })

          // 获取Setting_List中的Identify_Attr，如果有效（值为1或2）则使用，否则默认为1
          if (Setting_List.length > 0) {
            const identifyAttr = Setting_List[0].Identify_Attr
            this.form.identifyAttr = (identifyAttr === 1 || identifyAttr === 2) ? identifyAttr : 1
          }
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    handleSubmit() {
      if (this.form.enable) {
        const arr = []
        console.log('this.form', this.form)
        for (let i = 0; i < this.list.length; i++) {
          const regex = /^(?!.*\|\|)(?!.*\|$)(?!^\|)[^|]{1,10}(?:\|[^|]{1,10})*$/
          if (!regex.test(this.form[`item${i}`])) {
            this.$message({
              message: `${this.list[i].Part_Type_Name}配置不符合要求`,
              type: 'warning'
            })
            return
          }

          const item = this.form[`item${i}`].split(this.splitSymbol).filter(v => !!v)

          if (item.length === 0) {
            this.$message({
              message: `${this.list[i].Part_Type_Name}不能为空`,
              type: 'warning'
            })
            return
          }

          for (let j = 0; j < item.length; j++) {
            const d = item[j]
            if (d.length > 10) {
              this.$message({
                message: `${this.list[i].Part_Type_Name}单个配置，不能超过10个字符`,
                type: 'warning'
              })
              return
            }
          }

          arr.push(...item)
        }
        const uniArr = uniqueArr(arr)
        if (uniArr.length !== arr.length) {
          this.$message({
            message: '配置不能相同',
            type: 'warning'
          })
          return
        }
      }
      this.btnLoading = true
      SavePartTypeIdentifySetting({
        Is_Enabled: this.form.enable,
        Setting_List: this.list.map((v, i) => {
          return {
            ...v,
            Prefixs: this.form[`item${i}`],
            Identify_Attr: this.form.identifyAttr
          }
        })
      }).then(res => {
        if (res.IsSucceed) {
          this.$message({
            message: '操作成功',
            type: 'success'
          })
          this.$emit('close')
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      }).finally(() => {
        this.btnLoading = false
      })
    },
    mainBlur(e) {

    }
  }
}
</script>

<style scoped lang="scss">
@import "~@/styles/mixin.scss";
.form-wrapper {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-height: 70vh;
  .form-x{
    overflow: auto;
    padding-right: 16px;
    @include scrollBar;
  }
  .btn-x {
    padding-top: 16px;
    text-align: right;
  }

}
</style>

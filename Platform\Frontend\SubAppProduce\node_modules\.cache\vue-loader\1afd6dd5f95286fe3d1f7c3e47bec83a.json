{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\plan\\overallControlPlan.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\plan\\overallControlPlan.vue", "mtime": 1757926768489}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRDdXJyQ29tcGFueVByb2plY3RMaXN0IH0gZnJvbSAnQC9hcGkvcGxtL3Byb2plY3RzJw0KaW1wb3J0IE92ZXJhbGxDb250cm9sUGxhbkNvbnRlbnQgZnJvbSAnLi9jb21wb25lbnRzL092ZXJhbGxDb250cm9sUGxhbkNvbnRlbnQudnVlJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdPdmVyYWxsQ29udHJvbFBsYW4nLA0KICBjb21wb25lbnRzOiB7IE92ZXJhbGxDb250cm9sUGxhbkNvbnRlbnQgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgdHJlZURhdGE6IFtdLA0KICAgICAgdHJlZVByb3BzOiB7DQogICAgICAgIGxhYmVsOiAnU2hvcnRfTmFtZScsDQogICAgICAgIGlkOiAnU3lzX1Byb2plY3RfSWQnDQogICAgICB9LA0KICAgICAgdHJlZURlZmF1bHRTZWxlY3RlZEtleTogJycsDQogICAgICBjdXJQcm9qZWN0OiAnJw0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldFRyZWVEYXRhKCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGdldFRyZWVEYXRhKCkgew0KICAgICAgR2V0Q3VyckNvbXBhbnlQcm9qZWN0TGlzdCh7DQogICAgICAgIGNvbXBhbklkOiBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnQ3VyUmVmZXJlbmNlSWQnKSwNCiAgICAgICAgSXNDYXNjYWRlOiBmYWxzZQ0KICAgICAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICB0aGlzLnRyZWVEYXRhID0gcmVzLkRhdGEubWFwKGl0ZW0gPT4gew0KICAgICAgICAgIGl0ZW0ubG9hZGluZyA9IGZhbHNlDQogICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgfSkNCiAgICAgICAgaWYgKHRoaXMudHJlZURhdGEubGVuZ3RoKSB7DQogICAgICAgICAgdGhpcy50cmVlRGVmYXVsdFNlbGVjdGVkS2V5ID0gdGhpcy50cmVlRGF0YVswXS5TeXNfUHJvamVjdF9JZA0KICAgICAgICAgIHRoaXMuY3VyUHJvamVjdCA9IHRoaXMudHJlZURhdGFbMF0NCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIG5vZGVDbGljayhub2RlKSB7DQogICAgICB0aGlzLmN1clByb2plY3QgPSBub2RlDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["overallControlPlan.vue"], "names": [], "mappings": ";;;;;;;;;;;;;AAaA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "overallControlPlan.vue", "sourceRoot": "src/views/plan", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <bt-tree :data=\"treeData\" :props=\"treeProps\" :default-selected-key=\"treeDefaultSelectedKey\" node-key=\"Sys_Project_Id\" @node-click=\"nodeClick\">\r\n      <template #default=\"{ data }\">\r\n        <span style=\"color: #5ac8fa!important;\">({{ data.Code }})</span>\r\n        <span>{{ data.Short_Name }}</span>\r\n      </template>\r\n    </bt-tree>\r\n    <OverallControlPlanContent :cur-project=\"curProject\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetCurrCompanyProjectList } from '@/api/plm/projects'\r\nimport OverallControlPlanContent from './components/OverallControlPlanContent.vue'\r\n\r\nexport default {\r\n  name: 'OverallControlPlan',\r\n  components: { OverallControlPlanContent },\r\n  data() {\r\n    return {\r\n      treeData: [],\r\n      treeProps: {\r\n        label: 'Short_Name',\r\n        id: 'Sys_Project_Id'\r\n      },\r\n      treeDefaultSelectedKey: '',\r\n      curProject: ''\r\n    }\r\n  },\r\n  created() {\r\n    this.getTreeData()\r\n  },\r\n  methods: {\r\n    getTreeData() {\r\n      GetCurrCompanyProjectList({\r\n        companId: localStorage.getItem('CurReferenceId'),\r\n        IsCascade: false\r\n      }).then(res => {\r\n        this.treeData = res.Data.map(item => {\r\n          item.loading = false\r\n          return item\r\n        })\r\n        if (this.treeData.length) {\r\n          this.treeDefaultSelectedKey = this.treeData[0].Sys_Project_Id\r\n          this.curProject = this.treeData[0]\r\n        }\r\n      })\r\n    },\r\n    nodeClick(node) {\r\n      this.curProject = node\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  font-family: PingFang SC, PingFang SC;\r\n  display: flex;\r\n}\r\n</style>\r\n"]}]}
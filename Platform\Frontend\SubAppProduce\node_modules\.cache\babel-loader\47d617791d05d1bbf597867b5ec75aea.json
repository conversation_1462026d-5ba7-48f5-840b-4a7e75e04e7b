{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\section-list\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\section-list\\index.vue", "mtime": 1757468113549}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Deletepart", "ExportPlanpartInfo", "ExportPlanpartcountInfo", "DeletepartByfindkeywodes", "GetStopList", "GetUnitPageList", "GetUnitWeightList", "ExportPlanUnitInfo", "GetGridByCode", "GetFactoryProfessionalByCode", "GetProjectAreaTreeList", "GetInstallUnitIdNameList", "TreeDetail", "TopHeader", "comImport", "ComponentsHistory", "comImportByFactory", "HistoryExport", "BatchEdit", "ComponentPack", "Edit", "OneClickGeneratePack", "GeneratePack", "DeepMaterial", "<PERSON><PERSON><PERSON><PERSON>", "PartList", "ProcessData", "elDragDialog", "Pagination", "timeFormat", "AuthButtons", "bimdialog", "sysUseType", "promptBox", "combineURL", "tablePageSize", "parseOssUrl", "baseUrl", "v4", "uuidv4", "GetSteelCadAndBimId", "getConfigure", "GetFileType", "ExpandableSection", "comDrawdialog", "TracePlot", "modelDrawing", "GetBOMInfo", "SPLIT_SYMBOL", "name", "directives", "components", "mixins", "data", "allStopFlag", "showExpand", "drawer", "drawersull", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fullscreenid", "iframeUrl", "fullbimid", "expandedKey", "partTypeOption", "directOption", "value", "label", "treeData", "treeLoading", "projectName", "statusType", "searchHeight", "tbData", "total", "tbLoading", "pgLoading", "countLoading", "queryInfo", "Page", "PageSize", "Parameter<PERSON>son", "customPageSize", "installUnitIdNameList", "nameMode", "customParams", "TypeId", "Type_Name", "Code", "Code_Like", "Spec", "DateName", "Texture", "InstallUnit_Id", "IsDirect", "Part_Type_Id", "InstallUnit_Name", "Sys_Project_Id", "Project_Id", "Area_Id", "Project_Name", "Area_Name", "names", "customDialogParams", "dialogVisible", "currentComponent", "selectList", "factoryOption", "projectList", "typeOption", "columns", "columnsOption", "title", "width", "tipLabel", "monomerList", "mode", "isMonomer", "historyVisible", "undefined", "deleteContent", "SteelAmountTotal", "SchedulingNumTotal", "SteelAllWeightTotal", "SchedulingAllWeightTotal", "FinishCountTotal", "FinishWeightTotal", "DirectCountTotal", "DirectWeightTotal", "Unit", "fileBim", "Proportion", "command", "currentLastLevel", "templateUrl", "currentNode", "comDrawData", "trackDrawer", "trackDrawerTitle", "trackDrawerData", "drawingActive", "drawingDataList", "levelName", "levelCode", "computed", "showP9Btn", "buttons", "some", "item", "typeEntity", "_this", "find", "i", "Id", "PID", "_this$projectList$fin", "_this2", "filterText", "watch", "customParamsTypeId", "newValue", "oldValue", "console", "log", "fetchData", "n", "o", "changeMode", "mounted", "$refs", "searchDom", "offsetHeight", "created", "_this3", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "level", "_yield$GetBOMInfo", "currentBOMInfo", "wrap", "_callee$", "_context", "prev", "next", "$route", "query", "sent", "Display_Name", "getTypeList", "fetchTreeData", "getFileType", "Keywords01Value", "stop", "methods", "replace", "_this4", "Type", "MenuId", "meta", "Level", "then", "res", "IsSucceed", "$message", "message", "Message", "type", "Data", "length", "resData", "map", "Children", "Is_Imported", "ich", "Is_Directory", "it", "Object", "keys", "<PERSON><PERSON><PERSON>", "handleNodeClick", "_this5", "deepFilter", "tree", "ParentId", "handelsearch", "dataId", "ParentNodes", "Name", "_data$Data", "Label", "getInstallUnitIdNameList", "id", "_this6", "getProcessData", "_this7", "customParamsData", "JSON", "parse", "stringify", "InstallUnit_Ids", "join", "generateComponent", "$nextTick", "_", "init", "v", "Part_Aggregate_Id", "toString", "getTableConfig", "code", "_this8", "Promise", "resolve", "error", "tbConfig", "assign", "Grid", "list", "ColumnList", "sortList", "sort", "a", "b", "Sort", "filter", "Is_Display", "fixed", "Row_Number", "selectOption", "indexOf", "fetchList", "_this9", "_callee2", "_callee2$", "_context2", "_objectSpread", "trim", "replaceAll", "TotalCount", "Is_Main", "Exdate", "getUnitWeightList", "getStopList", "finally", "_this0", "_callee3", "submitObj", "_callee3$", "_context3", "stopMap", "for<PERSON>ach", "Is_Stop", "row", "$set", "_this1", "_callee4", "activeName", "_callee4$", "_context4", "concat", "changePage", "_this10", "_callee5", "_callee5$", "_context5", "getTbData", "YearAllWeight", "YearSteel", "CountInfo", "_this11", "_callee6", "_this11$typeOption$", "_this11$typeOption$2", "_callee6$", "_context6", "factoryId", "localStorage", "getItem", "freeze", "handleDelete", "_this12", "$confirm", "confirmButtonText", "cancelButtonText", "ids", "catch", "handleEdit", "_this13", "isReadOnly", "handleBatchEdit", "_this14", "SchedulArr", "<PERSON><PERSON><PERSON><PERSON>_Count", "handleView", "_this15", "handleExport", "_this16", "_callee7", "obj", "_callee7$", "_context7", "Part_Aggregate_Ids", "ProfessionalCode", "window", "open", "$baseUrl", "handleCommand", "deepListImport", "fileType", "Catalog_Code", "dialog", "handleOpen", "handleAllDelete", "_this17", "_callee8", "_callee8$", "_context8", "success", "warning", "handleClose", "component", "reset", "hasSearch", "arguments", "resetFields", "handleDeepMaterial", "_this18", "handelSchduling", "_this19", "handlePartList", "_this20", "_this21", "Math", "round", "DeepenNum", "SchedulingNum", "DeepenWeight", "SchedulingWeight", "Finish_Count", "Finish_Weight", "Direct_Count", "Direct_Weight", "tbSelectChange", "array", "_this22", "records", "SteelAllWeightTotalTemp", "SchedulingAllWeightTotalTemp", "FinishWeightTotalTemp", "DirectWeightTotalTemp", "schedulingNum", "<PERSON><PERSON>", "Number", "Total_Weight", "Weight", "fetchTreeDataLocal", "getPartInfo", "drawingData", "Drawing", "split", "fileUrlData", "File_Url", "index", "url", "getPartInfoDrawing", "_this23", "importDetailId", "ExtensionName", "IsUpload", "modelDrawingRef", "dwgInit", "changeDrawing", "tab", "event", "tabName", "drawingTab", "file", "customFilterFun", "node", "arr", "labelVal", "statusVal", "parentNode", "parent", "labels", "status", "Is_Deepen_Change", "_toConsumableArray", "resultLabel", "resultStatus", "s", "_this24", "_callee9", "_data$Data2", "params", "lable", "_callee9$", "_context9", "catalogCode", "isSHQD", "English_Name", "handelImport", "comDrawdialogRef", "handleTrack"], "sources": ["src/views/PRO/section-list/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <div\r\n      v-loading=\"pgLoading\"\r\n      style=\"display: flex\"\r\n      class=\"h100\"\r\n      element-loading-text=\"加载中\"\r\n    >\r\n      <ExpandableSection v-model=\"showExpand\" :width=\"300\" class=\"cs-left fff\">\r\n        <div class=\"inner-wrapper\">\r\n          <div class=\"tree-search\">\r\n            <el-select\r\n              v-model=\"statusType\"\r\n              clearable\r\n              class=\"search-select\"\r\n              placeholder=\"导入状态选择\"\r\n            >\r\n              <el-option label=\"已导入\" value=\"已导入\" />\r\n              <el-option label=\"未导入\" value=\"未导入\" />\r\n              <el-option label=\"已变更\" value=\"已变更\" />\r\n            </el-select>\r\n            <el-input\r\n              v-model.trim=\"projectName\"\r\n              placeholder=\"关键词搜索\"\r\n              size=\"small\"\r\n              clearable\r\n              suffix-icon=\"el-icon-search\"\r\n              @blur=\"fetchTreeDataLocal\"\r\n              @clear=\"fetchTreeDataLocal\"\r\n              @keydown.enter.native=\"fetchTreeDataLocal\"\r\n            />\r\n          </div>\r\n          <el-divider class=\"cs-divider\" />\r\n          <div class=\"tree-x cs-scroll\">\r\n            <tree-detail\r\n              ref=\"tree\"\r\n              icon=\"icon-folder\"\r\n              is-custom-filter\r\n              :custom-filter-fun=\"customFilterFun\"\r\n              :loading=\"treeLoading\"\r\n              :tree-data=\"treeData\"\r\n              show-status\r\n              show-detail\r\n              :filter-text=\"filterText\"\r\n              :expanded-key=\"expandedKey\"\r\n              @handleNodeClick=\"handleNodeClick\"\r\n            >\r\n              <template #csLabel=\"{ showStatus, data }\">\r\n                <span\r\n                  v-if=\"!data.ParentNodes\"\r\n                  class=\"cs-blue\"\r\n                >({{ data.Code }})</span>{{ data.Label }}\r\n                <template v-if=\"showStatus && data.Label != '全部'\">\r\n                  <span v-if=\"data.Data.Is_Deepen_Change\" class=\"cs-tag redBg\">\r\n                    <i class=\"fourRed\">已变更</i></span>\r\n                  <span\r\n                    v-else\r\n                    :class=\"[\r\n                      'cs-tag',\r\n                      data.Data.Is_Imported == true ? 'greenBg' : 'orangeBg',\r\n                    ]\"\r\n                  >\r\n                    <i\r\n                      :class=\"[\r\n                        data.Data.Is_Imported == true\r\n                          ? 'fourGreen'\r\n                          : 'fourOrange',\r\n                      ]\"\r\n                    >{{\r\n                      data.Data.Is_Imported == true ? \"已导入\" : \"未导入\"\r\n                    }}</i>\r\n                  </span>\r\n                </template>\r\n              </template></tree-detail>\r\n          </div>\r\n        </div>\r\n      </ExpandableSection>\r\n      <div class=\"cs-right\" style=\"padding-right: 0\">\r\n        <div class=\"container\">\r\n          <div ref=\"searchDom\" class=\"cs-from\">\r\n            <div class=\"cs-search\">\r\n              <el-form\r\n                ref=\"customParams\"\r\n                :model=\"customParams\"\r\n                label-width=\"80px\"\r\n                class=\"demo-form-inline\"\r\n              >\r\n                <el-row>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item\r\n                      label-width=\"110px\"\r\n                      :label=\"levelName + '名称'\"\r\n                      prop=\"Names\"\r\n                    >\r\n                      <el-input\r\n                        v-model=\"names\"\r\n                        clearable\r\n                        style=\"width: 100%\"\r\n                        class=\"input-with-select\"\r\n                        placeholder=\"请输入内容\"\r\n                        size=\"small\"\r\n                      >\r\n                        <el-select\r\n                          slot=\"prepend\"\r\n                          v-model=\"nameMode\"\r\n                          placeholder=\"请选择\"\r\n                          style=\"width: 100px\"\r\n                        >\r\n                          <el-option label=\"模糊搜索\" :value=\"1\" />\r\n                          <el-option label=\"精确搜索\" :value=\"2\" />\r\n                        </el-select>\r\n                      </el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item\r\n                      class=\"mb0\"\r\n                      label=\"批次\"\r\n                      prop=\"InstallUnit_Id\"\r\n                    >\r\n                      <el-select\r\n                        v-model=\"customParams.InstallUnit_Id\"\r\n                        multiple\r\n                        filterable\r\n                        clearable\r\n                        placeholder=\"请选择\"\r\n                        style=\"width: 100%\"\r\n                        :disabled=\"!Boolean(customParams.Area_Id)\"\r\n                      >\r\n                        <el-option\r\n                          v-for=\"item in installUnitIdNameList\"\r\n                          :key=\"item.Id\"\r\n                          :label=\"item.Name\"\r\n                          :value=\"item.Id\"\r\n                        />\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item\r\n                      label-width=\"92px\"\r\n                      label=\"是否直发件\"\r\n                      prop=\"IsDirect\"\r\n                    >\r\n                      <el-select\r\n                        v-model=\"customParams.IsDirect\"\r\n                        style=\"width: 100%\"\r\n                        placeholder=\"请选择\"\r\n                        clearable\r\n                      >\r\n                        <el-option\r\n                          v-for=\"item in directOption\"\r\n                          :key=\"item.value\"\r\n                          :label=\"item.label\"\r\n                          :value=\"item.value\"\r\n                        />\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item label=\"规格\" prop=\"Spec\">\r\n                      <el-input\r\n                        v-model=\"customParams.Spec\"\r\n                        placeholder=\"请输入\"\r\n                        clearable\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item\r\n                      label=\"材质\"\r\n                      prop=\"Texture\"\r\n                    >\r\n                      <el-input\r\n                        v-model=\"customParams.Texture\"\r\n                        placeholder=\"请输入\"\r\n                        clearable\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item\r\n                      label=\"操作人\"\r\n                      prop=\"DateName\"\r\n                    >\r\n                      <el-input\r\n                        v-model=\"customParams.DateName\"\r\n                        style=\"width: 100%\"\r\n                        placeholder=\"请输入\"\r\n                        clearable\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item class=\"mb0\" label-width=\"16px\">\r\n                      <el-button\r\n                        type=\"primary\"\r\n                        @click=\"handelsearch()\"\r\n                      >搜索\r\n                      </el-button>\r\n                      <el-button @click=\"handelsearch('reset')\">重置</el-button>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form>\r\n            </div>\r\n          </div>\r\n          <div class=\"fff cs-z-tb-wrapper\">\r\n            <div class=\"cs-button-box\">\r\n              <template>\r\n                <el-button\r\n                  :disabled=\"!selectList.length\"\r\n                  @click=\"handleExport\"\r\n                >导出部件</el-button>\r\n                <el-button\r\n                  :disabled=\"!selectList.length || selectList.some(item=>item.stopFlag)\"\r\n                  type=\"primary\"\r\n                  plain\r\n                  @click=\"handleBatchEdit\"\r\n                >批量编辑</el-button>\r\n                <!-- <el-button\r\n                  type=\"danger\"\r\n                  plain\r\n                  :disabled=\"!selectList.length\"\r\n                  @click=\"handleDelete\"\r\n                  >删除选中</el-button\r\n                > -->\r\n                <el-button\r\n                  type=\"success\"\r\n                  plain\r\n                  :disabled=\"!Boolean(customParams.Sys_Project_Id)\"\r\n                  @click=\"handelImport\"\r\n                >图纸导入\r\n                </el-button>\r\n              </template>\r\n            </div>\r\n            <div v-loading=\"countLoading\" class=\"info-box\">\r\n              <div class=\"cs-col\">\r\n                <span><span class=\"info-label\">深化总数</span><i>{{ SteelAmountTotal }} 件</i></span>\r\n                <span><span class=\"info-label\">深化总量</span><i>{{ SteelAllWeightTotal }}t</i></span>\r\n              </div>\r\n              <div class=\"cs-col\">\r\n                <span><span class=\"info-label\">排产总数</span><i>{{ SchedulingNumTotal }} 件</i></span>\r\n                <span><span class=\"info-label\">排产总量</span><i>{{ SchedulingAllWeightTotal }} t</i></span>\r\n              </div>\r\n              <div class=\"cs-col\" style=\"cursor: pointer;\" @click=\"getProcessData()\">\r\n                <span><span class=\"info-label\">完成总数</span><i>{{ FinishCountTotal }} 件</i></span>\r\n                <span><span class=\"info-label\">完成总量</span><i>{{ FinishWeightTotal }} t</i></span>\r\n              </div>\r\n              <div class=\"cs-col\">\r\n                <span><span class=\"info-label\">直发件总数</span><i>{{ DirectCountTotal }} 件</i></span>\r\n                <span><span class=\"info-label\">直发件总量</span><i>{{ DirectWeightTotal }} t</i></span>\r\n              </div>\r\n            </div>\r\n            <div class=\"tb-container\">\r\n              <vxe-table\r\n                v-loading=\"tbLoading\"\r\n                :empty-render=\"{name: 'NotData'}\"\r\n                show-header-overflow\r\n                element-loading-spinner=\"el-icon-loading\"\r\n                element-loading-text=\"拼命加载中\"\r\n                empty-text=\"暂无数据\"\r\n                class=\"cs-vxe-table\"\r\n                height=\"100%\"\r\n                align=\"left\"\r\n                stripe\r\n                :data=\"tbData\"\r\n                resizable\r\n                :tooltip-config=\"{ enterable: true }\"\r\n                :row-config=\"{ isHover: true }\"\r\n                @checkbox-all=\"tbSelectChange\"\r\n                @checkbox-change=\"tbSelectChange\"\r\n              >\r\n                <vxe-column fixed=\"left\" type=\"checkbox\" width=\"44\" />\r\n                <vxe-column\r\n                  v-for=\"(item, index) in columns\"\r\n                  :key=\"index\"\r\n                  :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                  show-overflow=\"tooltip\"\r\n                  sortable\r\n                  :align=\"item.Align\"\r\n                  :field=\"item.Code\"\r\n                  :title=\"item.Display_Name\"\r\n                  :width=\"item.Width ? item.Width : 120\"\r\n                  :min-width=\"item.Width\"\r\n                >\r\n                  <template #default=\"{ row }\">\r\n                    <div v-if=\"item.Code == 'Code'\">\r\n                      <el-tag v-if=\"row.Is_Change\" style=\"margin-right: 8px;\" type=\"danger\">变</el-tag>\r\n                      <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                      <el-link type=\"primary\" @click=\"getPartInfo(row)\"> {{ row[item.Code] | displayValue }}</el-link>\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'IsDirect'\">\r\n                      <el-tag v-if=\"row.IsDirect === true\">是</el-tag>\r\n                      <el-tag v-else type=\"danger\">否</el-tag>\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'Deep_Material'\">\r\n                      <el-link\r\n                        type=\"primary\"\r\n                        @click=\"handleDeepMaterial(row)\"\r\n                      >查看</el-link>\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'Num' && row[item.Code] > 0\">\r\n                      <span v-if=\"row[item.Code]\"> {{ row[item.Code] | displayValue }}件</span>\r\n                      <span v-else>-</span>\r\n                    </div>\r\n                    <div\r\n                      v-else-if=\"\r\n                        item.Code == 'Schduling_Count' && row[item.Code] > 0\r\n                      \"\r\n                    >\r\n                      <el-link\r\n                        v-if=\"row[item.Code]\"\r\n                        type=\"primary\"\r\n                        @click=\"handelSchduling(row)\"\r\n                      > {{ row[item.Code] | displayValue }}件</el-link>\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'Drawing'\">\r\n                      <span\r\n                        v-if=\"row.Drawing !== '暂无'\"\r\n                        style=\"color: #298dff; cursor: pointer\"\r\n                        @click=\"getPartInfo(row)\"\r\n                      > {{ row[item.Code] | displayValue }}\r\n                      </span>\r\n                      <span v-else> {{ row[item.Code] | displayValue }}</span>\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'Part'\">\r\n                      <el-link\r\n                        type=\"primary\"\r\n                        @click=\"handlePartList(row)\"\r\n                      >查看</el-link>\r\n                    </div>\r\n                    <div v-else>\r\n                      <span>{{ row[item.Code] | displayValue }}</span>\r\n                    </div>\r\n                  </template>\r\n                </vxe-column>\r\n                <vxe-column\r\n                  fixed=\"right\"\r\n                  title=\"操作\"\r\n                  width=\"150\"\r\n                  show-overflow\r\n                >\r\n                  <template #default=\"{ row }\">\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"handleView(row)\"\r\n                    >详情</el-button>\r\n                    <el-button\r\n                      :disabled=\"row.stopFlag\"\r\n                      type=\"text\"\r\n                      @click=\"handleEdit(row)\"\r\n                    >编辑</el-button>\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"handleTrack(row)\"\r\n                    >轨迹图\r\n                    </el-button>\r\n                  </template>\r\n                </vxe-column>\r\n              </vxe-table>\r\n            </div>\r\n            <div class=\"cs-bottom\">\r\n              <Pagination\r\n                class=\"cs-table-pagination\"\r\n                :total=\"total\"\r\n                max-height=\"100%\"\r\n                :page-sizes=\"tablePageSize\"\r\n                :page.sync=\"queryInfo.Page\"\r\n                :limit.sync=\"queryInfo.PageSize\"\r\n                layout=\"total, sizes, prev, pager, next, jumper\"\r\n                @pagination=\"changePage\"\r\n              >\r\n                <!--                <span class=\"pg-input\">\r\n                  <el-select\r\n                    v-model.number=\"queryInfo.PageSize\"\r\n                    allow-create\r\n                    filterable\r\n                    default-first-option\r\n                    @change=\"changePage\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"(item, index) in customPageSize\"\r\n                      :key=\"index\"\r\n                      :label=\"`${item}条/页`\"\r\n                      :value=\"item\"\r\n                    />\r\n                  </el-select>\r\n                </span>-->\r\n              </Pagination>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"card\" />\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      ref=\"content\"\r\n      v-el-drag-dialog\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"width\"\r\n      class=\"z-dialog\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :select-list=\"selectList\"\r\n        :custom-params=\"customDialogParams\"\r\n        :type-id=\"customParams.TypeId\"\r\n        :type-entity=\"typeEntity\"\r\n        :project-id=\"customParams.Project_Id\"\r\n        :sys-project-id=\"customParams.Project_Id\"\r\n        @close=\"handleClose\"\r\n        @refresh=\"fetchData\"\r\n      />\r\n    </el-dialog>\r\n    <bimdialog\r\n      ref=\"dialog\"\r\n      :type-entity=\"typeEntity\"\r\n      :area-id=\"customParams.Area_Id\"\r\n      :project-id=\"customParams.Project_Id\"\r\n      @getData=\"fetchData\"\r\n      @getTreeData=\"fetchTreeData\"\r\n    />\r\n\r\n    <el-drawer\r\n      :visible.sync=\"drawersull\"\r\n      direction=\"btt\"\r\n      size=\"100%\"\r\n      destroy-on-close\r\n    >\r\n      <iframe\r\n        v-if=\"templateUrl\"\r\n        id=\"fullFrame\"\r\n        :src=\"templateUrl\"\r\n        frameborder=\"0\"\r\n        style=\"width: 96%; margin-left: 2%; height: 70vh; margin-top: 2%\"\r\n      />\r\n    </el-drawer>\r\n\r\n    <el-drawer\r\n      :visible.sync=\"trackDrawer\"\r\n      direction=\"rtl\"\r\n      size=\"30%\"\r\n      destroy-on-close\r\n      custom-class=\"trackDrawerClass\"\r\n    >\r\n      <template #title>\r\n        <div>\r\n          <span>{{ trackDrawerTitle }}</span>\r\n          <span style=\"margin-left: 24px\">{{ trackDrawerData.Num }}</span>\r\n        </div>\r\n      </template>\r\n      <TracePlot :track-drawer-data=\"trackDrawerData\" />\r\n    </el-drawer>\r\n\r\n    <comDrawdialog ref=\"comDrawdialogRef\" @getData=\"fetchData\" />\r\n    <modelDrawing ref=\"modelDrawingRef\" type=\"部件\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  Deletepart,\r\n  ExportPlanpartInfo,\r\n  ExportPlanpartcountInfo,\r\n  DeletepartByfindkeywodes\r\n} from '@/api/plm/production'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nimport {\r\n  GetUnitPageList,\r\n  GetUnitWeightList,\r\n  ExportPlanUnitInfo\r\n} from '@/api/plm/section'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\r\nimport {\r\n  GetProjectAreaTreeList,\r\n  GetInstallUnitIdNameList\r\n} from '@/api/PRO/project'\r\n\r\nimport TreeDetail from '@/components/TreeDetail'\r\nimport TopHeader from '@/components/TopHeader'\r\nimport comImport from './component/Import'\r\nimport ComponentsHistory from './component/ComponentsHistory'\r\nimport comImportByFactory from './component/ImportByFactory'\r\nimport HistoryExport from './component/HistoryExport'\r\nimport BatchEdit from './component/BatchEditor'\r\nimport ComponentPack from './component/ComponentPack/index'\r\nimport Edit from './component/Edit'\r\nimport OneClickGeneratePack from './component/OneClickGeneratePack'\r\nimport GeneratePack from './component/GeneratePack'\r\nimport DeepMaterial from './component/DeepMaterial'\r\nimport Schduling from './component/Schduling'\r\nimport PartList from './component/PartList.vue'\r\nimport ProcessData from './component/ProcessData.vue'\r\n\r\nimport elDragDialog from '@/directive/el-drag-dialog'\r\nimport Pagination from '@/components/Pagination'\r\nimport { timeFormat } from '@/filters'\r\n// import { Column, Header, Table, Tooltip } from 'vxe-table'\r\n// import Vue from 'vue'\r\nimport AuthButtons from '@/mixins/auth-buttons'\r\nimport bimdialog from './component/bimdialog'\r\nimport sysUseType from '@/directive/sys-use-type/index.js'\r\nimport { promptBox } from './component/messageBox'\r\n\r\nimport { combineURL } from '@/utils'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport { parseOssUrl } from '@/utils/file'\r\n\r\nimport { baseUrl } from '@/utils/baseurl'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { GetSteelCadAndBimId } from '@/api/PRO/component'\r\nimport { getConfigure } from '@/api/user'\r\nimport { GetFileType } from '@/api/sys'\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport comDrawdialog from '@/views/PRO/production-order/deepen-files/dialog' // 深化文件-零件详图导入\r\nimport TracePlot from './component/TracePlot'\r\n\r\nimport modelDrawing from '@/views/PRO/components/modelDrawing.vue'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\n\r\n// Vue.use(Header).use(Column).use(Tooltip).use(Table)\r\nconst SPLIT_SYMBOL = '$_$'\r\nexport default {\r\n  name: 'PROSectionList',\r\n  directives: { elDragDialog, sysUseType },\r\n  components: {\r\n    ExpandableSection,\r\n    TreeDetail,\r\n    TopHeader,\r\n    comImport,\r\n    comImportByFactory,\r\n    BatchEdit,\r\n    HistoryExport,\r\n    GeneratePack,\r\n    Edit,\r\n    ComponentPack,\r\n    OneClickGeneratePack,\r\n    Pagination,\r\n    bimdialog,\r\n    ComponentsHistory,\r\n    DeepMaterial,\r\n    Schduling,\r\n    comDrawdialog,\r\n    TracePlot,\r\n    PartList,\r\n    modelDrawing,\r\n    ProcessData\r\n  },\r\n  mixins: [AuthButtons],\r\n  data() {\r\n    return {\r\n      allStopFlag: false,\r\n      showExpand: true,\r\n      drawer: false,\r\n      drawersull: false,\r\n      iframeKey: '',\r\n      fullscreenid: '',\r\n      iframeUrl: '',\r\n      fullbimid: '',\r\n      expandedKey: '', // -1是全部\r\n      tablePageSize: tablePageSize,\r\n      partTypeOption: [],\r\n      directOption: [\r\n        { value: true, label: '是' },\r\n        { value: false, label: '否' }\r\n      ],\r\n      treeData: [],\r\n      treeLoading: true,\r\n      projectName: '',\r\n      statusType: '',\r\n      searchHeight: 0,\r\n      tbData: [],\r\n      total: 0,\r\n      tbLoading: false,\r\n      pgLoading: false,\r\n      countLoading: false,\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: 10,\r\n        ParameterJson: []\r\n      },\r\n      customPageSize: [10, 20, 50, 100],\r\n      installUnitIdNameList: [], // 批次数组\r\n      nameMode: 1,\r\n\r\n      customParams: {\r\n        TypeId: '',\r\n        Type_Name: '',\r\n        Code: '',\r\n        Code_Like: '',\r\n        Spec: '',\r\n        DateName: '',\r\n        Texture: '',\r\n        // Keywords01: 'Code',\r\n        // Keywords01Value: '',\r\n        // Keywords02: 'Spec',\r\n        // Keywords02Value: '',\r\n        // Keywords03: 'Length',\r\n        // Keywords03Value: '',\r\n        // Keywords04: 'Texture',\r\n        // Keywords04Value: '',\r\n        InstallUnit_Id: [],\r\n        IsDirect: null, // 是否直发\r\n        Part_Type_Id: '',\r\n        InstallUnit_Name: '',\r\n        Sys_Project_Id: '',\r\n        Project_Id: '',\r\n        Area_Id: '',\r\n        Project_Name: '',\r\n        Area_Name: ''\r\n      },\r\n      names: '',\r\n      customDialogParams: {},\r\n      dialogVisible: false,\r\n      currentComponent: '',\r\n      selectList: [],\r\n      factoryOption: [],\r\n      projectList: [],\r\n      typeOption: [],\r\n      columns: [],\r\n      columnsOption: [\r\n        // { Display_Name: '零件名称', Code: 'Code' },\r\n        // { Display_Name: '规格', Code: 'Spec' },\r\n        // { Display_Name: '长度', Code: 'Length' },\r\n        // { Display_Name: '材质', Code: 'Texture' },\r\n        // { Display_Name: '深化数量', Code: 'Num' },\r\n        // { Display_Name: '排产数量', Code: 'Schduling_Count' },\r\n        // { Display_Name: '单重', Code: 'Weight' },\r\n        // { Display_Name: '总重', Code: 'Total_Weight' },\r\n        // { Display_Name: '形状', Code: 'Shape' },\r\n        // { Display_Name: '构件名称', Code: 'Component_Code' },\r\n        // { Display_Name: '操作人', Code: 'datename' },\r\n        // { Display_Name: '操作时间', Code: 'Exdate' }\r\n      ],\r\n      title: '',\r\n      width: '60%',\r\n      tipLabel: '',\r\n      monomerList: [],\r\n      mode: '',\r\n      isMonomer: true,\r\n      historyVisible: false,\r\n      sysUseType: undefined,\r\n      deleteContent: true,\r\n      SteelAmountTotal: 0, // 深化总量\r\n      SchedulingNumTotal: 0, // 排产总量\r\n      SteelAllWeightTotal: 0, // 深化总重\r\n      SchedulingAllWeightTotal: 0, // 排产总重\r\n      FinishCountTotal: 0, // 完成数量\r\n      FinishWeightTotal: 0, // 完成重量\r\n      DirectCountTotal: 0, // 直发件总数\r\n      DirectWeightTotal: 0, // 直发件总量\r\n      Unit: '',\r\n      fileBim: '',\r\n      Proportion: 0, // 专业的单位换算\r\n      command: 'cover',\r\n      currentLastLevel: false,\r\n      templateUrl: '',\r\n      currentNode: {},\r\n      comDrawData: {},\r\n      trackDrawer: false,\r\n      trackDrawerTitle: '',\r\n      trackDrawerData: {},\r\n      drawingActive: '',\r\n      drawingDataList: [],\r\n      levelName: '',\r\n      levelCode: ''\r\n    }\r\n  },\r\n  computed: {\r\n    showP9Btn() {\r\n      return this.AuthButtons.buttons.some((item) => item.Code === 'p9BtnAdd')\r\n    },\r\n    typeEntity() {\r\n      return this.typeOption.find((i) => i.Id === this.customParams.TypeId)\r\n    },\r\n    PID() {\r\n      return this.projectList.find(\r\n        (i) => i.Sys_Project_Id === this.customParams.Project_Id\r\n      )?.Id\r\n    },\r\n    filterText() {\r\n      return this.projectName + SPLIT_SYMBOL + this.statusType\r\n    }\r\n  },\r\n  watch: {\r\n    'customParams.TypeId': function(newValue, oldValue) {\r\n      console.log({ oldValue })\r\n      if (oldValue && oldValue !== '0') {\r\n        this.fetchData()\r\n      }\r\n    },\r\n    names(n, o) {\r\n      this.changeMode()\r\n    },\r\n    nameMode(n, o) {\r\n      this.changeMode()\r\n    }\r\n  },\r\n  mounted() {\r\n    this.pgLoading = true\r\n    console.log(this.columns)\r\n    // this.getUnitWeightList()\r\n    this.searchHeight = this.$refs.searchDom.offsetHeight + 327\r\n  },\r\n  async created() {\r\n    const level = +this.$route.query.level\r\n    const { currentBOMInfo } = await GetBOMInfo(level)\r\n    console.log('list', currentBOMInfo)\r\n    this.levelName = currentBOMInfo?.Display_Name\r\n    this.levelCode = currentBOMInfo?.Code\r\n    await this.getTypeList()\r\n    // await this.fetchData()\r\n    this.fetchTreeData()\r\n    this.getFileType()\r\n    if (this.Keywords01Value === '是') {\r\n      console.log('this.Keywords01Value', this.Keywords01Value)\r\n    }\r\n  },\r\n  methods: {\r\n    changeMode() {\r\n      if (this.nameMode === 1) {\r\n        this.customParams.Code_Like = this.names\r\n        this.customParams.Code = ''\r\n      } else {\r\n        this.customParams.Code_Like = ''\r\n        this.customParams.Code = this.names.replace(/\\s+/g, '\\n')\r\n      }\r\n    },\r\n\r\n    // 项目区域数据集\r\n    fetchTreeData() {\r\n      GetProjectAreaTreeList({ Type: 0, MenuId: this.$route.meta.Id, projectName: this.projectName, Level: this.levelCode }).then((res) => {\r\n        // const resAll = [\r\n        //   {\r\n        //     ParentNodes: null,\r\n        //     Id: '-1',\r\n        //     Code: '全部',\r\n        //     Label: '全部',\r\n        //     Level: null,\r\n        //     Data: {},\r\n        //     Children: []\r\n        //   }\r\n        // ]\r\n        // const resData = resAll.concat(res.Data)\r\n        if (!res.IsSucceed) {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.treeLoading = false\r\n          this.treeData = []\r\n          return\r\n        }\r\n        if (res.Data.length === 0) {\r\n          this.treeLoading = false\r\n          return\r\n        }\r\n        const resData = res.Data\r\n        resData.map((item) => {\r\n          if (item.Children.length === 0) {\r\n            item.Is_Imported = false\r\n          } else {\r\n            item.Data.Is_Imported = item.Children.some((ich) => {\r\n              return ich.Data.Is_Imported === true\r\n            })\r\n            item.Is_Directory = true\r\n            item.Children.map((it) => {\r\n              if (it.Children.length > 0) {\r\n                it.Is_Directory = true\r\n              }\r\n            })\r\n          }\r\n        })\r\n        this.treeData = resData\r\n        if (Object.keys(this.currentNode).length === 0) {\r\n          // this.fetchData()\r\n          this.setKey()\r\n        } else {\r\n          this.handleNodeClick(this.currentNode)\r\n        }\r\n        this.treeLoading = false\r\n        // this.expandedKey = this.customParams.Area_Id ? this.customParams.Area_Id : this.customParams.Project_Id ? this.customParams.Project_Id : resData[0].Id // '-1'\r\n        // this.customParams.Sys_Project_Id = this.customParams.Sys_Project_Id || resData[0].Data.Sys_Project_Id\r\n        // this.customParams.Project_Id = this.customParams.Project_Id || resData[0].Data.Id\r\n        // this.customParams.Area_Name = ''\r\n        // this.treeLoading = false\r\n        // this.fetchData()\r\n      })\r\n    },\r\n    // 设置默认选中第一个区域末级节点\r\n    setKey() {\r\n      const deepFilter = (tree) => {\r\n        for (let i = 0; i < tree.length; i++) {\r\n          const item = tree[i]\r\n          const { Data, Children } = item\r\n          console.log(Data)\r\n          if (Data.ParentId && !Children?.length) {\r\n            console.log(Data, '????')\r\n            this.currentNode = Data\r\n            this.handleNodeClick(item)\r\n            return\r\n          } else {\r\n            if (Children && Children.length > 0) {\r\n              return deepFilter(Children)\r\n            } else {\r\n              this.handleNodeClick(item)\r\n              return\r\n            }\r\n          }\r\n        }\r\n      }\r\n      return deepFilter(this.treeData)\r\n    },\r\n    // 选中左侧项目节点\r\n    handleNodeClick(data) {\r\n      this.handelsearch('reset', false)\r\n      this.currentNode = data\r\n      this.expandedKey = data.Id\r\n      this.customParams.InstallUnit_Id = []\r\n      const dataId = data.Id === '-1' ? '' : data.Id\r\n      console.log('nodeData', data)\r\n      if (data.ParentNodes) {\r\n        this.customParams.Project_Id = data.Data.Project_Id\r\n        this.customParams.Area_Id = data.Id\r\n        this.customParams.Area_Name = data.Data.Name\r\n        this.customParams.Sys_Project_Id = data.Data.Sys_Project_Id\r\n      } else {\r\n        this.customParams.Project_Id = dataId\r\n        this.customParams.Area_Id = ''\r\n        this.customParams.Area_Name = data.Data.Name\r\n        this.customParams.Sys_Project_Id = data.Data.Sys_Project_Id\r\n      }\r\n      console.log(\r\n        this.customParams.Sys_Project_Id,\r\n        'this.customParams.Sys_Project_Id============11111'\r\n      )\r\n      console.log(\r\n        this.customParams.Area_Id,\r\n        'this.customParams.Area_Id============11111'\r\n      )\r\n      this.currentLastLevel = !!(data.Data.Level && data.Children.length === 0)\r\n      if (this.currentLastLevel) {\r\n        this.customParams.Project_Name = data.Data?.Project_Name\r\n        this.customParams.Area_Name = data.Label\r\n      }\r\n      this.queryInfo.Page = 1\r\n      this.pgLoading = true\r\n      this.fetchData()\r\n      console.log(this.customParams.Area_Id)\r\n      this.getInstallUnitIdNameList(dataId, data)\r\n    },\r\n\r\n    // 获取批次\r\n    getInstallUnitIdNameList(id, data) {\r\n      if (id === '' || data.Children.length > 0) {\r\n        this.installUnitIdNameList = []\r\n      } else {\r\n        GetInstallUnitIdNameList({ Area_Id: id, Level: this.levelCode }).then((res) => {\r\n          this.installUnitIdNameList = res.Data\r\n        })\r\n      }\r\n    },\r\n    // 工序完成量\r\n    getProcessData() {\r\n      const customParamsData = JSON.parse(JSON.stringify(this.customParams))\r\n      const InstallUnit_Ids = customParamsData.InstallUnit_Id.join(',')\r\n      delete customParamsData.InstallUnit_Id\r\n\r\n      this.width = '40%'\r\n      this.generateComponent('部件工序完成量', 'ProcessData')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(customParamsData, InstallUnit_Ids, this.selectList.map((v) => v.Part_Aggregate_Id).toString())\r\n      })\r\n    },\r\n    getTableConfig(code) {\r\n      return new Promise((resolve) => {\r\n        GetGridByCode({\r\n          code:\r\n            code +\r\n            ',' +\r\n            this.typeOption.find((i) => i.Id === this.customParams.TypeId).Code\r\n        }).then((res) => {\r\n          const { IsSucceed, Data, Message } = res\r\n          if (IsSucceed) {\r\n            if (!Data) {\r\n              this.$message.error('当前专业没有配置相对应表格')\r\n              this.tbLoading = true\r\n              return\r\n            }\r\n            this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n            const list = Data.ColumnList || []\r\n            const sortList = list.sort((a, b) => a.Sort - b.Sort)\r\n            this.columns = sortList\r\n              .filter((v) => v.Is_Display)\r\n              .map((item) => {\r\n                if (item.Code === 'Code') {\r\n                  item.fixed = 'left'\r\n                }\r\n\r\n                return item\r\n              })\r\n            this.queryInfo.PageSize = +Data.Grid.Row_Number || 20\r\n            resolve(this.columns)\r\n            console.log(this.columns)\r\n            const selectOption = JSON.parse(JSON.stringify(this.columns))\r\n            console.log(selectOption)\r\n            this.columnsOption = selectOption.filter((v) => {\r\n              return (\r\n                v.Display_Name !== '操作时间' &&\r\n                v.Display_Name !== '模型ID' &&\r\n                v.Display_Name !== '深化资料' &&\r\n                v.Display_Name !== '备注' &&\r\n                v.Display_Name !== '排产数量' &&\r\n                v.Code.indexOf('Attr') === -1 &&\r\n                v.Display_Name !== '批次'\r\n              )\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    async fetchList() {\r\n      const customParamsData = JSON.parse(JSON.stringify(this.customParams))\r\n      const InstallUnit_Ids = customParamsData.InstallUnit_Id.join(',')\r\n      delete customParamsData.InstallUnit_Id\r\n\r\n      await GetUnitPageList({\r\n        Level: this.levelCode,\r\n        ...this.queryInfo,\r\n        ...customParamsData,\r\n        Code: customParamsData.Code.trim().replaceAll(' ', '\\n'),\r\n        InstallUnit_Ids: InstallUnit_Ids\r\n      })\r\n        .then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.queryInfo.PageSize = res.Data.PageSize\r\n            this.total = res.Data.TotalCount\r\n            this.tbData = res.Data.Data.map((v) => {\r\n              v.Is_Main = v.Is_Main ? '是' : '否'\r\n              v.Exdate = timeFormat(v.Exdate, '{y}-{m}-{d} {h}:{i}:{s}')\r\n              // console.log(v)\r\n              return v\r\n            })\r\n            this.selectList = []\r\n            this.getUnitWeightList()\r\n            this.getStopList()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n        .finally(() => {\r\n          this.tbLoading = false\r\n          this.pgLoading = false\r\n        })\r\n    },\r\n    async getStopList() {\r\n      const submitObj = this.tbData.map(item => {\r\n        return {\r\n          Id: item.Part_Aggregate_Id,\r\n          Type: 3\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = item.Is_Stop !== null\r\n          })\r\n          this.tbData.forEach(row => {\r\n            if (stopMap[row.Part_Aggregate_Id]) {\r\n              this.$set(row, 'stopFlag', stopMap[row.Part_Aggregate_Id])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async fetchData() {\r\n      console.log('更新列表')\r\n      // 分开获取，提高接口速度\r\n      const activeName = `plm_level${this.levelCode}_page_list`\r\n      await this.getTableConfig(activeName)\r\n      this.tbLoading = true\r\n      this.fetchList().then((res) => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    async changePage() {\r\n      this.tbLoading = true\r\n      if (\r\n        typeof this.queryInfo.PageSize !== 'number' ||\r\n        this.queryInfo.PageSize < 1\r\n      ) {\r\n        this.queryInfo.PageSize = 10\r\n      }\r\n      this.fetchList().then((res) => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    // tbSelectChange(array) {\r\n    //   console.log('array', array)\r\n    //   this.selectList = array.records\r\n    //   console.log('this.selectList', this.selectList)\r\n    // },\r\n    getTbData(data) {\r\n      const { YearAllWeight, YearSteel, CountInfo } = data\r\n      // this.tipLabel = `累计上传构件${YearSteel}件，总重${YearAllWeight}t。`\r\n      this.tipLabel = CountInfo\r\n    },\r\n    async getTypeList() {\r\n      let res = null\r\n      let data = null\r\n      res = await GetFactoryProfessionalByCode({\r\n        factoryId: localStorage.getItem('CurReferenceId')\r\n      })\r\n      data = res.Data\r\n      if (res.IsSucceed) {\r\n        this.typeOption = Object.freeze(data)\r\n        if (this.typeOption.length > 0) {\r\n          this.Proportion = data[0].Proportion\r\n          this.Unit = data[0].Unit\r\n          this.customParams.TypeId = this.typeOption[0]?.Id\r\n          this.customParams.Type_Name = this.typeOption[0]?.Name\r\n        }\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    handleDelete() {\r\n      this.$confirm('此操作将删除选择数据, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          Deletepart({\r\n            Level: this.levelCode,\r\n            ids: this.selectList.map((v) => v.Part_Aggregate_Id).toString()\r\n          }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.fetchData()\r\n              this.$message({\r\n                message: '删除成功',\r\n                type: 'success'\r\n              })\r\n              this.getUnitWeightList()\r\n              this.fetchTreeData()\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n    handleEdit(row) {\r\n      this.width = '45%'\r\n      this.generateComponent('编辑部件', 'Edit')\r\n      this.$nextTick((_) => {\r\n        row.isReadOnly = false\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n    handleBatchEdit() {\r\n      const SchedulArr = this.selectList.filter((item) => {\r\n        return item.Schduling_Count != null && item.Schduling_Count > 0\r\n      })\r\n      if (SchedulArr.length > 0) {\r\n        this.$message({\r\n          type: 'error',\r\n          message: '选中行包含已排产的部件,编辑信息需要进行变更操作'\r\n        })\r\n      } else {\r\n        this.width = '40%'\r\n        this.generateComponent('批量编辑', 'BatchEdit')\r\n        this.$nextTick((_) => {\r\n          this.$refs['content'].init(this.selectList, this.columnsOption)\r\n        })\r\n      }\r\n    },\r\n    handleView(row) {\r\n      this.width = '45%'\r\n      this.generateComponent('详情', 'Edit')\r\n      this.$nextTick((_) => {\r\n        row.isReadOnly = true\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n    async handleExport() {\r\n      const obj = {\r\n        Level: this.levelCode,\r\n        Part_Aggregate_Ids: this.selectList\r\n          .map((v) => v.Part_Aggregate_Id)\r\n          .toString(),\r\n        ProfessionalCode: this.typeEntity.Code\r\n      }\r\n      ExportPlanUnitInfo(obj).then((res) => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    // 覆盖导入 or 新增导入\r\n    handleCommand(command) {\r\n      console.log(command, 'command')\r\n      this.command = command\r\n      this.deepListImport()\r\n    },\r\n    deepListImport() {\r\n      const fileType = {\r\n        Catalog_Code: 'PLMDeepenFiles',\r\n        Code: this.typeEntity.Code,\r\n        name: this.typeEntity.Name\r\n      }\r\n      this.$refs.dialog.handleOpen(\r\n        'add',\r\n        fileType,\r\n        null,\r\n        true,\r\n        this.PID,\r\n        this.command,\r\n        this.customParams\r\n      )\r\n    },\r\n    async handleAllDelete() {\r\n      console.log(this.customParams.Project_Id)\r\n      if (this.customParams.Project_Id) {\r\n        await promptBox({ title: '删除' })\r\n        await DeletepartByfindkeywodes({\r\n          Level: this.levelCode,\r\n          ...this.customParams,\r\n          ...this.queryInfo\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message.success('删除成功')\r\n            this.fetchData()\r\n            this.fetchTreeData()\r\n          } else {\r\n            this.$message.error(res.Message)\r\n          }\r\n        })\r\n      } else {\r\n        this.$message.warning('请先选择项目')\r\n      }\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    generateComponent(title, component) {\r\n      this.title = title\r\n      this.currentComponent = component\r\n      this.dialogVisible = true\r\n    },\r\n    // 点击搜索\r\n    handelsearch(reset, hasSearch = true) {\r\n      this.deleteContent = false\r\n      if (reset) {\r\n        this.$refs.customParams.resetFields()\r\n        this.deleteContent = true\r\n        this.names = ''\r\n      }\r\n      hasSearch && this.fetchData()\r\n    },\r\n    // 深化资料查看\r\n    handleDeepMaterial(row) {\r\n      console.log('handleDeepMaterial')\r\n      this.width = '45%'\r\n      this.generateComponent('查看深化资料', 'DeepMaterial')\r\n      this.$nextTick((_) => {\r\n        row.isReadOnly = false\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n    // 排产数量\r\n    handelSchduling(row) {\r\n      this.width = '45%'\r\n      this.generateComponent('生产详情', 'Schduling')\r\n      this.$nextTick((_) => {\r\n        row.isReadOnly = false\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n    // 查看零件\r\n    handlePartList(row) {\r\n      this.width = '45%'\r\n      this.generateComponent('零件清单', 'PartList')\r\n      this.$nextTick((_) => {\r\n        row.isReadOnly = false\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n    // 部件分页统计\r\n    getUnitWeightList() {\r\n      this.countLoading = true\r\n      const customParamsData = JSON.parse(JSON.stringify(this.customParams))\r\n      const InstallUnit_Ids = customParamsData.InstallUnit_Id.join(',')\r\n      delete customParamsData.InstallUnit_Id\r\n      GetUnitWeightList({\r\n        Level: this.levelCode,\r\n        ...this.queryInfo,\r\n        ...customParamsData,\r\n        InstallUnit_Ids\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.SteelAmountTotal = Math.round(res.Data.DeepenNum * 1000) / 1000 // 深化总量\r\n          this.SchedulingNumTotal =\r\n            Math.round(res.Data.SchedulingNum * 1000) / 1000 // 排产总量\r\n          this.SteelAllWeightTotal =\r\n            Math.round(res.Data.DeepenWeight * 1000) / 1000 // 深化总重\r\n          this.SchedulingAllWeightTotal =\r\n            Math.round(res.Data.SchedulingWeight * 1000) / 1000 // 排产总重\r\n          this.FinishCountTotal =\r\n            Math.round(res.Data.Finish_Count * 1000) / 1000 // 完成总数\r\n          this.FinishWeightTotal =\r\n            Math.round(res.Data.Finish_Weight * 1000) / 1000 // 完成总重\r\n          this.DirectCountTotal =\r\n            Math.round(res.Data.Direct_Count * 1000) / 1000 // 直发件总数\r\n          this.DirectWeightTotal =\r\n            Math.round(res.Data.Direct_Weight * 1000) / 1000 // 直发件总量\r\n          console.log(' this.SteelAllWeightTotal', this.SteelAllWeightTotal)\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n        this.countLoading = false\r\n      })\r\n    },\r\n    tbSelectChange(array) {\r\n      this.selectList = array.records\r\n      this.SteelAmountTotal = 0\r\n      this.SchedulingNumTotal = 0\r\n      this.SteelAllWeightTotal = 0\r\n      this.SchedulingAllWeightTotal = 0\r\n      this.FinishCountTotal = 0\r\n      this.FinishWeightTotal = 0\r\n      this.DirectCountTotal = 0\r\n      let SteelAllWeightTotalTemp = 0\r\n      let SchedulingAllWeightTotalTemp = 0\r\n      let FinishWeightTotalTemp = 0\r\n      let DirectWeightTotalTemp = 0\r\n      if (this.selectList.length > 0) {\r\n        this.selectList.forEach((item) => {\r\n          const schedulingNum =\r\n            item.Schduling_Count == null ? 0 : item.Schduling_Count\r\n          this.SteelAmountTotal += item.Num\r\n          this.SchedulingNumTotal += Number(item.Schduling_Count)\r\n          this.FinishCountTotal += item.Finish_Count\r\n          if (item.IsDirect) {\r\n            this.DirectCountTotal += item.Num // 直发件总数\r\n            DirectWeightTotalTemp += item.Total_Weight // 直发件总量\r\n          }\r\n          SteelAllWeightTotalTemp += item.Total_Weight\r\n          SchedulingAllWeightTotalTemp += item.Weight * schedulingNum\r\n          FinishWeightTotalTemp += item.Finish_Weight\r\n        })\r\n        this.SteelAllWeightTotal =\r\n          Math.round((SteelAllWeightTotalTemp / this.Proportion) * 1000) / 1000\r\n        this.SchedulingAllWeightTotal =\r\n          Math.round((SchedulingAllWeightTotalTemp / this.Proportion) * 1000) /\r\n          1000\r\n        this.FinishWeightTotal =\r\n          Math.round((FinishWeightTotalTemp / this.Proportion) * 1000) / 1000\r\n        this.DirectWeightTotal =\r\n          Math.round((DirectWeightTotalTemp / this.Proportion) * 1000) / 1000\r\n      } else {\r\n        this.getUnitWeightList()\r\n      }\r\n    },\r\n    fetchTreeDataLocal() {\r\n      // this.filterText = this.projectName\r\n    },\r\n    getPartInfo(row) {\r\n      const drawingData = row.Drawing ? row.Drawing.split(',') : [] // 图纸数据\r\n      const fileUrlData = row.File_Url ? row.File_Url.split(',') : [] // 图纸数据文件地址数据\r\n      if (fileUrlData.length === 0) {\r\n        this.$message({\r\n          message: '当前部件无图纸',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      if (drawingData.length > 0 && fileUrlData.length > 0) {\r\n        this.drawingActive = drawingData[0]\r\n      }\r\n      if (drawingData.length > 0 && fileUrlData.length > 0) {\r\n        this.drawingDataList = drawingData.map((item, index) => ({\r\n          name: item,\r\n          label: item,\r\n          url: fileUrlData[index]\r\n        }))\r\n      }\r\n\r\n      this.getPartInfoDrawing(row)\r\n    },\r\n\r\n    getPartInfoDrawing(row) {\r\n      const importDetailId = row.Part_Aggregate_Id\r\n      GetSteelCadAndBimId({ importDetailId: importDetailId, Level: this.levelCode }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const drawingData = {\r\n            'extensionName': res.Data[0].ExtensionName,\r\n            'fileBim': res.Data[0].fileBim,\r\n            'IsUpload': res.Data[0].IsUpload,\r\n            'Code': row.Code,\r\n            'Sys_Project_Id': row.Sys_Project_Id\r\n          }\r\n          this.$refs.modelDrawingRef.dwgInit(drawingData)\r\n        }\r\n      })\r\n    },\r\n\r\n    changeDrawing(tab, event) {\r\n      console.log(tab, event)\r\n      const tabName = tab.name\r\n      // 查找对应的 URL\r\n      const drawingTab = this.drawingDataList.find(\r\n        (file) => file.name === tabName\r\n      )\r\n      if (drawingTab) {\r\n        console.log('URL:', drawingTab.url) // 输出 URL\r\n        // 在这里你可以处理其他逻辑，如打开链接等\r\n      }\r\n    },\r\n    /*    handleViewDwg(row) {\r\n      if (!row.File_Url) {\r\n        this.$message({\r\n          message: '当前零件无图纸',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      window.open('http://dwgv1.bimtk.com:5432/?CadUrl=' + parseOssUrl(row.File_Url), '_blank')\r\n    },*/\r\n    customFilterFun(value, data, node) {\r\n      const arr = value.split(SPLIT_SYMBOL)\r\n      const labelVal = arr[0]\r\n      const statusVal = arr[1]\r\n      if (!value) return true\r\n      let parentNode = node.parent\r\n      let labels = [node.label]\r\n      let status = [\r\n        data.Data.Is_Deepen_Change\r\n          ? '已变更'\r\n          : data.Data.Is_Imported\r\n            ? '已导入'\r\n            : '未导入'\r\n      ]\r\n      let level = 1\r\n      while (level < node.level) {\r\n        labels = [...labels, parentNode.label]\r\n        status = [\r\n          ...status,\r\n          data.Data.Is_Deepen_Change\r\n            ? '已变更'\r\n            : data.Data.Is_Imported\r\n              ? '已导入'\r\n              : '未导入'\r\n        ]\r\n        parentNode = parentNode.parent\r\n        level++\r\n      }\r\n      labels = labels.filter((v) => !!v)\r\n      status = status.filter((v) => !!v)\r\n      let resultLabel = true\r\n      let resultStatus = true\r\n      if (this.statusType) {\r\n        resultStatus = status.some((s) => s.indexOf(statusVal) !== -1)\r\n      }\r\n      if (this.projectName) {\r\n        resultLabel = labels.some((s) => s.indexOf(labelVal) !== -1)\r\n      }\r\n      return resultLabel && resultStatus\r\n    },\r\n    async getFileType() {\r\n      const params = {\r\n        Level: this.levelCode,\r\n        catalogCode: 'PLMDeepenFiles'\r\n      }\r\n      const res = await GetFileType(params)\r\n      // 获取构件详图\r\n      const lable = `${this.levelName}详图`\r\n      const data = res.Data.find((v) => v.Label === lable)\r\n\r\n      this.comDrawData = {\r\n        isSHQD: false,\r\n        Id: data.Id,\r\n        name: data.Label,\r\n        Catalog_Code: data.Code,\r\n        Code: data.Data?.English_Name\r\n      }\r\n\r\n      console.log(this.comDrawData, 'comDrawData')\r\n    },\r\n    // 图纸导入\r\n    handelImport() {\r\n      this.$refs.comDrawdialogRef.handleOpen(\r\n        'add',\r\n        this.comDrawData,\r\n        '',\r\n        false,\r\n        this.customParams.Sys_Project_Id,\r\n        false\r\n      )\r\n    },\r\n    // 轨迹图\r\n    handleTrack(row) {\r\n      console.log(row, 'row')\r\n      this.trackDrawer = true\r\n      this.trackDrawerTitle = row.Code\r\n      this.trackDrawerData = row\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/styles/mixin.scss\";\r\n@import \"~@/styles/tabs.scss\";\r\n.min900 {\r\n  min-width: 900px;\r\n  overflow: auto;\r\n}\r\n.z-dialog {\r\n  ::v-deep {\r\n    .el-dialog__header {\r\n      background-color: #298dff;\r\n\r\n      .el-dialog__title,\r\n      .el-dialog__close {\r\n        color: #ffffff;\r\n      }\r\n    }\r\n\r\n    .el-dialog__body {\r\n      // max-height: 750px;\r\n      overflow: auto;\r\n      @include scrollBar;\r\n\r\n      &::-webkit-scrollbar {\r\n        width: 8px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.container {\r\n  padding: 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100%;\r\n}\r\n\r\n.tb-container {\r\n  padding: 0 16px 0 16px;\r\n  flex: 1;\r\n  height: 0; //解决溢出问题\r\n  // .vxe-table {\r\n  //   height: calc(100%);\r\n  // }\r\n}\r\n\r\n.cs-z-tb-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 0; //解决溢出问题\r\n}\r\n\r\n.cs-bottom {\r\n  padding: 8px 16px 8px 16px;\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: row-reverse;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  box-sizing: border-box;\r\n\r\n  .data-info {\r\n    .info-x {\r\n      margin-right: 20px;\r\n    }\r\n  }\r\n  .pg-input {\r\n    width: 100px;\r\n    margin-right: 20px;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  text-align: right;\r\n  margin: 0;\r\n  padding: 0;\r\n  ::v-deep .el-input--small .el-input__inner {\r\n    height: 28px;\r\n    line-height: 28px;\r\n  }\r\n}\r\n\r\n.cs-from {\r\n  background-color: #ffffff;\r\n  border-radius: 4px;\r\n  margin-bottom: 16px;\r\n  padding: 16px 16px 0 16px;\r\n  display: flex;\r\n  font-size: 14px;\r\n  color: rgba(34, 40, 52, 0.65);\r\n  label {\r\n    display: inline-block;\r\n    margin-right: 20px;\r\n    white-space: nowrap;\r\n    vertical-align: top;\r\n  }\r\n  .cs-from-title {\r\n    flex: 1;\r\n  }\r\n\r\n  .mb0 {\r\n    margin-bottom: 0;\r\n\r\n    ::v-deep {\r\n      .el-form-item {\r\n        margin-bottom: 0\r\n      }\r\n    }\r\n  }\r\n\r\n  .cs-search {\r\n    width: 100%;\r\n    label {\r\n      margin-bottom: 10px;\r\n    }\r\n    button {\r\n      margin-right: 10px;\r\n      margin-left: 0;\r\n      margin-bottom: 10px;\r\n    }\r\n  }\r\n}\r\n\r\n.input-with-select {\r\n  width: 250px;\r\n}\r\n\r\n.cs-button-box {\r\n  padding: 16px 16px 6px 16px;\r\n  position: relative;\r\n  background-color: #ffffff;\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  flex-wrap: wrap;\r\n\r\n  ::v-deep .el-button {\r\n    margin-left: 0 !important;\r\n    margin-right: 10px !important;\r\n    margin-bottom: 10px !important;\r\n  }\r\n}\r\n.info-box {\r\n  margin: 0 16px 16px 16px;\r\n  display: flex;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  height: 64px;\r\n  background: rgba(41, 141, 255, 0.05);\r\n\r\n  .cs-col {\r\n    display: flex;\r\n    justify-content: space-evenly;\r\n    flex-direction: column;\r\n    margin-right: 64px;\r\n  }\r\n\r\n  .info-label {\r\n    color: #999999;\r\n  }\r\n\r\n  i {\r\n    color: #00c361;\r\n    font-style: normal;\r\n    font-weight: 600;\r\n    margin-left: 10px;\r\n  }\r\n}\r\n\r\n::v-deep .el-tree-node {\r\n  min-width: 240px;\r\n  width: min-content;\r\n}\r\n::v-deep .el-tree-node > .el-tree-node__children {\r\n  overflow: inherit;\r\n}\r\n\r\n.stretch-btn {\r\n  position: absolute;\r\n  width: 20px;\r\n  height: 130px;\r\n  top: calc((100% - 130px) / 2);\r\n\r\n  display: flex;\r\n  align-items: center;\r\n  background: #eff1f3;\r\n  cursor: pointer;\r\n  .center-btn {\r\n    width: 14px;\r\n    height: 100px;\r\n    border-radius: 0 9px 9px 0;\r\n    background-color: #8c95a8;\r\n    > i {\r\n      line-height: 100px;\r\n      text-align: center;\r\n      color: #fff;\r\n    }\r\n  }\r\n}\r\n.cs-left {\r\n  position: relative;\r\n  margin-right: 20px;\r\n  .inner-wrapper {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    padding: 16px 10px 16px 16px;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n\r\n    .tree-search {\r\n      display: flex;\r\n\r\n      .search-select {\r\n        margin-right: 8px;\r\n      }\r\n    }\r\n\r\n    .tree-x {\r\n      overflow: hidden;\r\n      margin-top: 16px;\r\n      flex: 1;\r\n\r\n      .cs-scroll {\r\n        overflow-y: auto;\r\n        @include scrollBar;\r\n      }\r\n\r\n      .el-tree {\r\n        height: 100%;\r\n\r\n        //::v-deep {\r\n        //  .el-tree-node {\r\n        //    min-width: 240px;\r\n        //    width: min-content;\r\n        //\r\n        //    .el-tree-node__children {\r\n        //      overflow: inherit;\r\n        //    }\r\n        //  }\r\n        //}\r\n      }\r\n    }\r\n  }\r\n}\r\n.cs-left-contract {\r\n  padding-left: 0;\r\n  position: relative;\r\n  width: 20px;\r\n  margin-right: 26px;\r\n}\r\n.cs-right {\r\n  padding-right: 0;\r\n  flex: 1;\r\n  width: 0;\r\n}\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n.fourGreen {\r\n  color: #00c361;\r\n  font-style: normal;\r\n}\r\n\r\n.fourOrange {\r\n  color: #ff9400;\r\n  font-style: normal;\r\n}\r\n\r\n.fourRed {\r\n  color: #ff0000;\r\n  font-style: normal;\r\n}\r\n\r\n.cs-blue {\r\n  color: #5ac8fa;\r\n}\r\n\r\n.orangeBg {\r\n  background: rgba(255, 148, 0, 0.1);\r\n}\r\n\r\n.redBg {\r\n  background: rgba(252, 107, 127, 0.1);\r\n}\r\n.greenBg {\r\n  background: rgba(0, 195, 97, 0.1);\r\n}\r\n\r\n.cs-tag {\r\n  margin-left: 8px;\r\n  font-size: 12px;\r\n  padding: 2px 4px;\r\n  border-radius: 1px;\r\n}\r\n.cs-tree-x {\r\n  ::v-deep {\r\n    .el-select {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n.cs-divider {\r\n  margin: 16px 0 0 0;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmdA,SACAA,UAAA,EACAC,kBAAA,EACAC,uBAAA,EACAC,wBAAA,QACA;AACA,SAAAC,WAAA;AACA,SACAC,eAAA,EACAC,iBAAA,EACAC,kBAAA,QACA;AACA,SAAAC,aAAA;AACA,SAAAC,4BAAA;AACA,SACAC,sBAAA,EACAC,wBAAA,QACA;AAEA,OAAAC,UAAA;AACA,OAAAC,SAAA;AACA,OAAAC,SAAA;AACA,OAAAC,iBAAA;AACA,OAAAC,kBAAA;AACA,OAAAC,aAAA;AACA,OAAAC,SAAA;AACA,OAAAC,aAAA;AACA,OAAAC,IAAA;AACA,OAAAC,oBAAA;AACA,OAAAC,YAAA;AACA,OAAAC,YAAA;AACA,OAAAC,SAAA;AACA,OAAAC,QAAA;AACA,OAAAC,WAAA;AAEA,OAAAC,YAAA;AACA,OAAAC,UAAA;AACA,SAAAC,UAAA;AACA;AACA;AACA,OAAAC,WAAA;AACA,OAAAC,SAAA;AACA,OAAAC,UAAA;AACA,SAAAC,SAAA;AAEA,SAAAC,UAAA;AACA,SAAAC,aAAA;AACA,SAAAC,WAAA;AAEA,SAAAC,OAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AACA,SAAAC,mBAAA;AACA,SAAAC,YAAA;AACA,SAAAC,WAAA;AACA,OAAAC,iBAAA;AACA,OAAAC,aAAA;AACA,OAAAC,SAAA;AAEA,OAAAC,YAAA;AACA,SAAAC,UAAA;;AAEA;AACA,IAAAC,YAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAvB,YAAA,EAAAA,YAAA;IAAAK,UAAA,EAAAA;EAAA;EACAmB,UAAA;IACAR,iBAAA,EAAAA,iBAAA;IACA/B,UAAA,EAAAA,UAAA;IACAC,SAAA,EAAAA,SAAA;IACAC,SAAA,EAAAA,SAAA;IACAE,kBAAA,EAAAA,kBAAA;IACAE,SAAA,EAAAA,SAAA;IACAD,aAAA,EAAAA,aAAA;IACAK,YAAA,EAAAA,YAAA;IACAF,IAAA,EAAAA,IAAA;IACAD,aAAA,EAAAA,aAAA;IACAE,oBAAA,EAAAA,oBAAA;IACAO,UAAA,EAAAA,UAAA;IACAG,SAAA,EAAAA,SAAA;IACAhB,iBAAA,EAAAA,iBAAA;IACAQ,YAAA,EAAAA,YAAA;IACAC,SAAA,EAAAA,SAAA;IACAoB,aAAA,EAAAA,aAAA;IACAC,SAAA,EAAAA,SAAA;IACApB,QAAA,EAAAA,QAAA;IACAqB,YAAA,EAAAA,YAAA;IACApB,WAAA,EAAAA;EACA;EACA0B,MAAA,GAAAtB,WAAA;EACAuB,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,UAAA;MACAC,MAAA;MACAC,UAAA;MACAC,SAAA;MACAC,YAAA;MACAC,SAAA;MACAC,SAAA;MACAC,WAAA;MAAA;MACA3B,aAAA,EAAAA,aAAA;MACA4B,cAAA;MACAC,YAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,QAAA;MACAC,WAAA;MACAC,WAAA;MACAC,UAAA;MACAC,YAAA;MACAC,MAAA;MACAC,KAAA;MACAC,SAAA;MACAC,SAAA;MACAC,YAAA;MACAC,SAAA;QACAC,IAAA;QACAC,QAAA;QACAC,aAAA;MACA;MACAC,cAAA;MACAC,qBAAA;MAAA;MACAC,QAAA;MAEAC,YAAA;QACAC,MAAA;QACAC,SAAA;QACAC,IAAA;QACAC,SAAA;QACAC,IAAA;QACAC,QAAA;QACAC,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAC,cAAA;QACAC,QAAA;QAAA;QACAC,YAAA;QACAC,gBAAA;QACAC,cAAA;QACAC,UAAA;QACAC,OAAA;QACAC,YAAA;QACAC,SAAA;MACA;MACAC,KAAA;MACAC,kBAAA;MACAC,aAAA;MACAC,gBAAA;MACAC,UAAA;MACAC,aAAA;MACAC,WAAA;MACAC,UAAA;MACAC,OAAA;MACAC,aAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAAA,CACA;MACAC,KAAA;MACAC,KAAA;MACAC,QAAA;MACAC,WAAA;MACAC,IAAA;MACAC,SAAA;MACAC,cAAA;MACArF,UAAA,EAAAsF,SAAA;MACAC,aAAA;MACAC,gBAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,mBAAA;MAAA;MACAC,wBAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,IAAA;MACAC,OAAA;MACAC,UAAA;MAAA;MACAC,OAAA;MACAC,gBAAA;MACAC,WAAA;MACAC,WAAA;MACAC,WAAA;MACAC,WAAA;MACAC,gBAAA;MACAC,eAAA;MACAC,aAAA;MACAC,eAAA;MACAC,SAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACAC,SAAA,WAAAA,UAAA;MACA,YAAAlH,WAAA,CAAAmH,OAAA,CAAAC,IAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA5D,IAAA;MAAA;IACA;IACA6D,UAAA,WAAAA,WAAA;MAAA,IAAAC,KAAA;MACA,YAAAzC,UAAA,CAAA0C,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,EAAA,KAAAH,KAAA,CAAAjE,YAAA,CAAAC,MAAA;MAAA;IACA;IACAoE,GAAA,WAAAA,IAAA;MAAA,IAAAC,qBAAA;QAAAC,MAAA;MACA,QAAAD,qBAAA,QAAA/C,WAAA,CAAA2C,IAAA,CACA,UAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAvD,cAAA,KAAA2D,MAAA,CAAAvE,YAAA,CAAAa,UAAA;MAAA,CACA,eAAAyD,qBAAA,uBAFAA,qBAAA,CAEAF,EAAA;IACA;IACAI,UAAA,WAAAA,WAAA;MACA,YAAAvF,WAAA,GAAArB,YAAA,QAAAsB,UAAA;IACA;EACA;EACAuF,KAAA;IACA,gCAAAC,mBAAAC,QAAA,EAAAC,QAAA;MACAC,OAAA,CAAAC,GAAA;QAAAF,QAAA,EAAAA;MAAA;MACA,IAAAA,QAAA,IAAAA,QAAA;QACA,KAAAG,SAAA;MACA;IACA;IACA9D,KAAA,WAAAA,MAAA+D,CAAA,EAAAC,CAAA;MACA,KAAAC,UAAA;IACA;IACAnF,QAAA,WAAAA,SAAAiF,CAAA,EAAAC,CAAA;MACA,KAAAC,UAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAA5F,SAAA;IACAsF,OAAA,CAAAC,GAAA,MAAArD,OAAA;IACA;IACA,KAAAtC,YAAA,QAAAiG,KAAA,CAAAC,SAAA,CAAAC,YAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAAC,KAAA,EAAAC,iBAAA,EAAAC,cAAA;MAAA,OAAAL,mBAAA,GAAAM,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACAP,KAAA,IAAAL,MAAA,CAAAa,MAAA,CAAAC,KAAA,CAAAT,KAAA;YAAAK,QAAA,CAAAE,IAAA;YAAA,OACAzI,UAAA,CAAAkI,KAAA;UAAA;YAAAC,iBAAA,GAAAI,QAAA,CAAAK,IAAA;YAAAR,cAAA,GAAAD,iBAAA,CAAAC,cAAA;YACAlB,OAAA,CAAAC,GAAA,SAAAiB,cAAA;YACAP,MAAA,CAAA/B,SAAA,GAAAsC,cAAA,aAAAA,cAAA,uBAAAA,cAAA,CAAAS,YAAA;YACAhB,MAAA,CAAA9B,SAAA,GAAAqC,cAAA,aAAAA,cAAA,uBAAAA,cAAA,CAAA5F,IAAA;YAAA+F,QAAA,CAAAE,IAAA;YAAA,OACAZ,MAAA,CAAAiB,WAAA;UAAA;YACA;YACAjB,MAAA,CAAAkB,aAAA;YACAlB,MAAA,CAAAmB,WAAA;YACA,IAAAnB,MAAA,CAAAoB,eAAA;cACA/B,OAAA,CAAAC,GAAA,yBAAAU,MAAA,CAAAoB,eAAA;YACA;UAAA;UAAA;YAAA,OAAAV,QAAA,CAAAW,IAAA;QAAA;MAAA,GAAAjB,OAAA;IAAA;EACA;EACAkB,OAAA;IACA5B,UAAA,WAAAA,WAAA;MACA,SAAAnF,QAAA;QACA,KAAAC,YAAA,CAAAI,SAAA,QAAAa,KAAA;QACA,KAAAjB,YAAA,CAAAG,IAAA;MACA;QACA,KAAAH,YAAA,CAAAI,SAAA;QACA,KAAAJ,YAAA,CAAAG,IAAA,QAAAc,KAAA,CAAA8F,OAAA;MACA;IACA;IAEA;IACAL,aAAA,WAAAA,cAAA;MAAA,IAAAM,MAAA;MACA1L,sBAAA;QAAA2L,IAAA;QAAAC,MAAA,OAAAb,MAAA,CAAAc,IAAA,CAAA/C,EAAA;QAAAnF,WAAA,OAAAA,WAAA;QAAAmI,KAAA,OAAA1D;MAAA,GAAA2D,IAAA,WAAAC,GAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,KAAAA,GAAA,CAAAC,SAAA;UACAP,MAAA,CAAAQ,QAAA;YACAC,OAAA,EAAAH,GAAA,CAAAI,OAAA;YACAC,IAAA;UACA;UACAX,MAAA,CAAAhI,WAAA;UACAgI,MAAA,CAAAjI,QAAA;UACA;QACA;QACA,IAAAuI,GAAA,CAAAM,IAAA,CAAAC,MAAA;UACAb,MAAA,CAAAhI,WAAA;UACA;QACA;QACA,IAAA8I,OAAA,GAAAR,GAAA,CAAAM,IAAA;QACAE,OAAA,CAAAC,GAAA,WAAAhE,IAAA;UACA,IAAAA,IAAA,CAAAiE,QAAA,CAAAH,MAAA;YACA9D,IAAA,CAAAkE,WAAA;UACA;YACAlE,IAAA,CAAA6D,IAAA,CAAAK,WAAA,GAAAlE,IAAA,CAAAiE,QAAA,CAAAlE,IAAA,WAAAoE,GAAA;cACA,OAAAA,GAAA,CAAAN,IAAA,CAAAK,WAAA;YACA;YACAlE,IAAA,CAAAoE,YAAA;YACApE,IAAA,CAAAiE,QAAA,CAAAD,GAAA,WAAAK,EAAA;cACA,IAAAA,EAAA,CAAAJ,QAAA,CAAAH,MAAA;gBACAO,EAAA,CAAAD,YAAA;cACA;YACA;UACA;QACA;QACAnB,MAAA,CAAAjI,QAAA,GAAA+I,OAAA;QACA,IAAAO,MAAA,CAAAC,IAAA,CAAAtB,MAAA,CAAA9D,WAAA,EAAA2E,MAAA;UACA;UACAb,MAAA,CAAAuB,MAAA;QACA;UACAvB,MAAA,CAAAwB,eAAA,CAAAxB,MAAA,CAAA9D,WAAA;QACA;QACA8D,MAAA,CAAAhI,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAuJ,MAAA,WAAAA,OAAA;MAAA,IAAAE,MAAA;MACA,IAAAC,WAAA,YAAAA,WAAAC,IAAA;QACA,SAAAxE,CAAA,MAAAA,CAAA,GAAAwE,IAAA,CAAAd,MAAA,EAAA1D,CAAA;UACA,IAAAJ,IAAA,GAAA4E,IAAA,CAAAxE,CAAA;UACA,IAAAyD,IAAA,GAAA7D,IAAA,CAAA6D,IAAA;YAAAI,QAAA,GAAAjE,IAAA,CAAAiE,QAAA;UACAnD,OAAA,CAAAC,GAAA,CAAA8C,IAAA;UACA,IAAAA,IAAA,CAAAgB,QAAA,MAAAZ,QAAA,aAAAA,QAAA,eAAAA,QAAA,CAAAH,MAAA;YACAhD,OAAA,CAAAC,GAAA,CAAA8C,IAAA;YACAa,MAAA,CAAAvF,WAAA,GAAA0E,IAAA;YACAa,MAAA,CAAAD,eAAA,CAAAzE,IAAA;YACA;UACA;YACA,IAAAiE,QAAA,IAAAA,QAAA,CAAAH,MAAA;cACA,OAAAa,WAAA,CAAAV,QAAA;YACA;cACAS,MAAA,CAAAD,eAAA,CAAAzE,IAAA;cACA;YACA;UACA;QACA;MACA;MACA,OAAA2E,WAAA,MAAA3J,QAAA;IACA;IACA;IACAyJ,eAAA,WAAAA,gBAAAvK,IAAA;MACA,KAAA4K,YAAA;MACA,KAAA3F,WAAA,GAAAjF,IAAA;MACA,KAAAS,WAAA,GAAAT,IAAA,CAAAmG,EAAA;MACA,KAAApE,YAAA,CAAAQ,cAAA;MACA,IAAAsI,MAAA,GAAA7K,IAAA,CAAAmG,EAAA,iBAAAnG,IAAA,CAAAmG,EAAA;MACAS,OAAA,CAAAC,GAAA,aAAA7G,IAAA;MACA,IAAAA,IAAA,CAAA8K,WAAA;QACA,KAAA/I,YAAA,CAAAa,UAAA,GAAA5C,IAAA,CAAA2J,IAAA,CAAA/G,UAAA;QACA,KAAAb,YAAA,CAAAc,OAAA,GAAA7C,IAAA,CAAAmG,EAAA;QACA,KAAApE,YAAA,CAAAgB,SAAA,GAAA/C,IAAA,CAAA2J,IAAA,CAAAoB,IAAA;QACA,KAAAhJ,YAAA,CAAAY,cAAA,GAAA3C,IAAA,CAAA2J,IAAA,CAAAhH,cAAA;MACA;QACA,KAAAZ,YAAA,CAAAa,UAAA,GAAAiI,MAAA;QACA,KAAA9I,YAAA,CAAAc,OAAA;QACA,KAAAd,YAAA,CAAAgB,SAAA,GAAA/C,IAAA,CAAA2J,IAAA,CAAAoB,IAAA;QACA,KAAAhJ,YAAA,CAAAY,cAAA,GAAA3C,IAAA,CAAA2J,IAAA,CAAAhH,cAAA;MACA;MACAiE,OAAA,CAAAC,GAAA,CACA,KAAA9E,YAAA,CAAAY,cAAA,EACA,mDACA;MACAiE,OAAA,CAAAC,GAAA,CACA,KAAA9E,YAAA,CAAAc,OAAA,EACA,4CACA;MACA,KAAAkC,gBAAA,MAAA/E,IAAA,CAAA2J,IAAA,CAAAR,KAAA,IAAAnJ,IAAA,CAAA+J,QAAA,CAAAH,MAAA;MACA,SAAA7E,gBAAA;QAAA,IAAAiG,UAAA;QACA,KAAAjJ,YAAA,CAAAe,YAAA,IAAAkI,UAAA,GAAAhL,IAAA,CAAA2J,IAAA,cAAAqB,UAAA,uBAAAA,UAAA,CAAAlI,YAAA;QACA,KAAAf,YAAA,CAAAgB,SAAA,GAAA/C,IAAA,CAAAiL,KAAA;MACA;MACA,KAAAzJ,SAAA,CAAAC,IAAA;MACA,KAAAH,SAAA;MACA,KAAAwF,SAAA;MACAF,OAAA,CAAAC,GAAA,MAAA9E,YAAA,CAAAc,OAAA;MACA,KAAAqI,wBAAA,CAAAL,MAAA,EAAA7K,IAAA;IACA;IAEA;IACAkL,wBAAA,WAAAA,yBAAAC,EAAA,EAAAnL,IAAA;MAAA,IAAAoL,MAAA;MACA,IAAAD,EAAA,WAAAnL,IAAA,CAAA+J,QAAA,CAAAH,MAAA;QACA,KAAA/H,qBAAA;MACA;QACAvE,wBAAA;UAAAuF,OAAA,EAAAsI,EAAA;UAAAhC,KAAA,OAAA1D;QAAA,GAAA2D,IAAA,WAAAC,GAAA;UACA+B,MAAA,CAAAvJ,qBAAA,GAAAwH,GAAA,CAAAM,IAAA;QACA;MACA;IACA;IACA;IACA0B,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,gBAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAA3J,YAAA;MACA,IAAA4J,eAAA,GAAAJ,gBAAA,CAAAhJ,cAAA,CAAAqJ,IAAA;MACA,OAAAL,gBAAA,CAAAhJ,cAAA;MAEA,KAAAoB,KAAA;MACA,KAAAkI,iBAAA;MACA,KAAAC,SAAA,WAAAC,CAAA;QACAT,MAAA,CAAAnE,KAAA,YAAA6E,IAAA,CAAAT,gBAAA,EAAAI,eAAA,EAAAL,MAAA,CAAAlI,UAAA,CAAA0G,GAAA,WAAAmC,CAAA;UAAA,OAAAA,CAAA,CAAAC,iBAAA;QAAA,GAAAC,QAAA;MACA;IACA;IACAC,cAAA,WAAAA,eAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,WAAAC,OAAA,WAAAC,OAAA;QACArP,aAAA;UACAkP,IAAA,EACAA,IAAA,GACA,MACAC,MAAA,CAAA/I,UAAA,CAAA0C,IAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAC,EAAA,KAAAmG,MAAA,CAAAvK,YAAA,CAAAC,MAAA;UAAA,GAAAE;QACA,GAAAkH,IAAA,WAAAC,GAAA;UACA,IAAAC,SAAA,GAAAD,GAAA,CAAAC,SAAA;YAAAK,IAAA,GAAAN,GAAA,CAAAM,IAAA;YAAAF,OAAA,GAAAJ,GAAA,CAAAI,OAAA;UACA,IAAAH,SAAA;YACA,KAAAK,IAAA;cACA2C,MAAA,CAAA/C,QAAA,CAAAkD,KAAA;cACAH,MAAA,CAAAjL,SAAA;cACA;YACA;YACAiL,MAAA,CAAAI,QAAA,GAAAtC,MAAA,CAAAuC,MAAA,KAAAL,MAAA,CAAAI,QAAA,EAAA/C,IAAA,CAAAiD,IAAA;YACA,IAAAC,IAAA,GAAAlD,IAAA,CAAAmD,UAAA;YACA,IAAAC,QAAA,GAAAF,IAAA,CAAAG,IAAA,WAAAC,CAAA,EAAAC,CAAA;cAAA,OAAAD,CAAA,CAAAE,IAAA,GAAAD,CAAA,CAAAC,IAAA;YAAA;YACAb,MAAA,CAAA9I,OAAA,GAAAuJ,QAAA,CACAK,MAAA,WAAAnB,CAAA;cAAA,OAAAA,CAAA,CAAAoB,UAAA;YAAA,GACAvD,GAAA,WAAAhE,IAAA;cACA,IAAAA,IAAA,CAAA5D,IAAA;gBACA4D,IAAA,CAAAwH,KAAA;cACA;cAEA,OAAAxH,IAAA;YACA;YACAwG,MAAA,CAAA9K,SAAA,CAAAE,QAAA,IAAAiI,IAAA,CAAAiD,IAAA,CAAAW,UAAA;YACAf,OAAA,CAAAF,MAAA,CAAA9I,OAAA;YACAoD,OAAA,CAAAC,GAAA,CAAAyF,MAAA,CAAA9I,OAAA;YACA,IAAAgK,YAAA,GAAAhC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAY,MAAA,CAAA9I,OAAA;YACAoD,OAAA,CAAAC,GAAA,CAAA2G,YAAA;YACAlB,MAAA,CAAA7I,aAAA,GAAA+J,YAAA,CAAAJ,MAAA,WAAAnB,CAAA;cACA,OACAA,CAAA,CAAA1D,YAAA,eACA0D,CAAA,CAAA1D,YAAA,eACA0D,CAAA,CAAA1D,YAAA,eACA0D,CAAA,CAAA1D,YAAA,aACA0D,CAAA,CAAA1D,YAAA,eACA0D,CAAA,CAAA/J,IAAA,CAAAuL,OAAA,mBACAxB,CAAA,CAAA1D,YAAA;YAEA;UACA;YACA+D,MAAA,CAAA/C,QAAA;cACAC,OAAA,EAAAC,OAAA;cACAC,IAAA;YACA;UACA;QACA;MACA;IACA;IACAgE,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MAAA,OAAAnG,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkG,SAAA;QAAA,IAAArC,gBAAA,EAAAI,eAAA;QAAA,OAAAlE,mBAAA,GAAAM,IAAA,UAAA8F,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5F,IAAA,GAAA4F,SAAA,CAAA3F,IAAA;YAAA;cACAoD,gBAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAiC,MAAA,CAAA5L,YAAA;cACA4J,eAAA,GAAAJ,gBAAA,CAAAhJ,cAAA,CAAAqJ,IAAA;cACA,OAAAL,gBAAA,CAAAhJ,cAAA;cAAAuL,SAAA,CAAA3F,IAAA;cAAA,OAEAnL,eAAA,CAAA+Q,aAAA,CAAAA,aAAA,CAAAA,aAAA;gBACA5E,KAAA,EAAAwE,MAAA,CAAAlI;cAAA,GACAkI,MAAA,CAAAnM,SAAA,GACA+J,gBAAA;gBACArJ,IAAA,EAAAqJ,gBAAA,CAAArJ,IAAA,CAAA8L,IAAA,GAAAC,UAAA;gBACAtC,eAAA,EAAAA;cAAA,EACA,EACAvC,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAqE,MAAA,CAAAnM,SAAA,CAAAE,QAAA,GAAA2H,GAAA,CAAAM,IAAA,CAAAjI,QAAA;kBACAiM,MAAA,CAAAvM,KAAA,GAAAiI,GAAA,CAAAM,IAAA,CAAAuE,UAAA;kBACAP,MAAA,CAAAxM,MAAA,GAAAkI,GAAA,CAAAM,IAAA,CAAAA,IAAA,CAAAG,GAAA,WAAAmC,CAAA;oBACAA,CAAA,CAAAkC,OAAA,GAAAlC,CAAA,CAAAkC,OAAA;oBACAlC,CAAA,CAAAmC,MAAA,GAAA5P,UAAA,CAAAyN,CAAA,CAAAmC,MAAA;oBACA;oBACA,OAAAnC,CAAA;kBACA;kBACA0B,MAAA,CAAAvK,UAAA;kBACAuK,MAAA,CAAAU,iBAAA;kBACAV,MAAA,CAAAW,WAAA;gBACA;kBACAX,MAAA,CAAApE,QAAA;oBACAC,OAAA,EAAAH,GAAA,CAAAI,OAAA;oBACAC,IAAA;kBACA;gBACA;cACA,GACA6E,OAAA;gBACAZ,MAAA,CAAAtM,SAAA;gBACAsM,MAAA,CAAArM,SAAA;cACA;YAAA;YAAA;cAAA,OAAAwM,SAAA,CAAAlF,IAAA;UAAA;QAAA,GAAAgF,QAAA;MAAA;IACA;IACAU,WAAA,WAAAA,YAAA;MAAA,IAAAE,MAAA;MAAA,OAAAhH,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA+G,SAAA;QAAA,IAAAC,SAAA;QAAA,OAAAjH,mBAAA,GAAAM,IAAA,UAAA4G,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1G,IAAA,GAAA0G,SAAA,CAAAzG,IAAA;YAAA;cACAuG,SAAA,GAAAF,MAAA,CAAArN,MAAA,CAAA2I,GAAA,WAAAhE,IAAA;gBACA;kBACAK,EAAA,EAAAL,IAAA,CAAAoG,iBAAA;kBACAlD,IAAA;gBACA;cACA;cAAA4F,SAAA,CAAAzG,IAAA;cAAA,OACApL,WAAA,CAAA2R,SAAA,EAAAtF,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACA,IAAAuF,OAAA;kBACAxF,GAAA,CAAAM,IAAA,CAAAmF,OAAA,WAAAhJ,IAAA;oBACA+I,OAAA,CAAA/I,IAAA,CAAAK,EAAA,IAAAL,IAAA,CAAAiJ,OAAA;kBACA;kBACAP,MAAA,CAAArN,MAAA,CAAA2N,OAAA,WAAAE,GAAA;oBACA,IAAAH,OAAA,CAAAG,GAAA,CAAA9C,iBAAA;sBACAsC,MAAA,CAAAS,IAAA,CAAAD,GAAA,cAAAH,OAAA,CAAAG,GAAA,CAAA9C,iBAAA;oBACA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA0C,SAAA,CAAAhG,IAAA;UAAA;QAAA,GAAA6F,QAAA;MAAA;IACA;IACA3H,SAAA,WAAAA,UAAA;MAAA,IAAAoI,MAAA;MAAA,OAAA1H,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAyH,SAAA;QAAA,IAAAC,UAAA;QAAA,OAAA3H,mBAAA,GAAAM,IAAA,UAAAsH,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApH,IAAA,GAAAoH,SAAA,CAAAnH,IAAA;YAAA;cACAvB,OAAA,CAAAC,GAAA;cACA;cACAuI,UAAA,eAAAG,MAAA,CAAAL,MAAA,CAAAzJ,SAAA;cAAA6J,SAAA,CAAAnH,IAAA;cAAA,OACA+G,MAAA,CAAA9C,cAAA,CAAAgD,UAAA;YAAA;cACAF,MAAA,CAAA7N,SAAA;cACA6N,MAAA,CAAAxB,SAAA,GAAAtE,IAAA,WAAAC,GAAA;gBACA6F,MAAA,CAAA7N,SAAA;cACA;YAAA;YAAA;cAAA,OAAAiO,SAAA,CAAA1G,IAAA;UAAA;QAAA,GAAAuG,QAAA;MAAA;IACA;IACAK,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MAAA,OAAAjI,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAgI,SAAA;QAAA,OAAAjI,mBAAA,GAAAM,IAAA,UAAA4H,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1H,IAAA,GAAA0H,SAAA,CAAAzH,IAAA;YAAA;cACAsH,OAAA,CAAApO,SAAA;cACA,IACA,OAAAoO,OAAA,CAAAjO,SAAA,CAAAE,QAAA,iBACA+N,OAAA,CAAAjO,SAAA,CAAAE,QAAA,MACA;gBACA+N,OAAA,CAAAjO,SAAA,CAAAE,QAAA;cACA;cACA+N,OAAA,CAAA/B,SAAA,GAAAtE,IAAA,WAAAC,GAAA;gBACAoG,OAAA,CAAApO,SAAA;cACA;YAAA;YAAA;cAAA,OAAAuO,SAAA,CAAAhH,IAAA;UAAA;QAAA,GAAA8G,QAAA;MAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACAG,SAAA,WAAAA,UAAA7P,IAAA;MACA,IAAA8P,aAAA,GAAA9P,IAAA,CAAA8P,aAAA;QAAAC,SAAA,GAAA/P,IAAA,CAAA+P,SAAA;QAAAC,SAAA,GAAAhQ,IAAA,CAAAgQ,SAAA;MACA;MACA,KAAApM,QAAA,GAAAoM,SAAA;IACA;IACAxH,WAAA,WAAAA,YAAA;MAAA,IAAAyH,OAAA;MAAA,OAAAzI,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAwI,SAAA;QAAA,IAAA7G,GAAA,EAAArJ,IAAA,EAAAmQ,mBAAA,EAAAC,oBAAA;QAAA,OAAA3I,mBAAA,GAAAM,IAAA,UAAAsI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApI,IAAA,GAAAoI,SAAA,CAAAnI,IAAA;YAAA;cACAkB,GAAA;cACArJ,IAAA;cAAAsQ,SAAA,CAAAnI,IAAA;cAAA,OACA/K,4BAAA;gBACAmT,SAAA,EAAAC,YAAA,CAAAC,OAAA;cACA;YAAA;cAFApH,GAAA,GAAAiH,SAAA,CAAAhI,IAAA;cAGAtI,IAAA,GAAAqJ,GAAA,CAAAM,IAAA;cACA,IAAAN,GAAA,CAAAC,SAAA;gBACA2G,OAAA,CAAA1M,UAAA,GAAA6G,MAAA,CAAAsG,MAAA,CAAA1Q,IAAA;gBACA,IAAAiQ,OAAA,CAAA1M,UAAA,CAAAqG,MAAA;kBACAqG,OAAA,CAAApL,UAAA,GAAA7E,IAAA,IAAA6E,UAAA;kBACAoL,OAAA,CAAAtL,IAAA,GAAA3E,IAAA,IAAA2E,IAAA;kBACAsL,OAAA,CAAAlO,YAAA,CAAAC,MAAA,IAAAmO,mBAAA,GAAAF,OAAA,CAAA1M,UAAA,iBAAA4M,mBAAA,uBAAAA,mBAAA,CAAAhK,EAAA;kBACA8J,OAAA,CAAAlO,YAAA,CAAAE,SAAA,IAAAmO,oBAAA,GAAAH,OAAA,CAAA1M,UAAA,iBAAA6M,oBAAA,uBAAAA,oBAAA,CAAArF,IAAA;gBACA;cACA;gBACAkF,OAAA,CAAA1G,QAAA;kBACAC,OAAA,EAAAH,GAAA,CAAAI,OAAA;kBACAC,IAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA4G,SAAA,CAAA1H,IAAA;UAAA;QAAA,GAAAsH,QAAA;MAAA;IACA;IACAS,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACArH,IAAA;MACA,GACAN,IAAA;QACAzM,UAAA;UACAwM,KAAA,EAAAyH,OAAA,CAAAnL,SAAA;UACAuL,GAAA,EAAAJ,OAAA,CAAAxN,UAAA,CAAA0G,GAAA,WAAAmC,CAAA;YAAA,OAAAA,CAAA,CAAAC,iBAAA;UAAA,GAAAC,QAAA;QACA,GAAA/C,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAsH,OAAA,CAAA9J,SAAA;YACA8J,OAAA,CAAArH,QAAA;cACAC,OAAA;cACAE,IAAA;YACA;YACAkH,OAAA,CAAAvC,iBAAA;YACAuC,OAAA,CAAAnI,aAAA;UACA;YACAmI,OAAA,CAAArH,QAAA;cACAC,OAAA,EAAAH,GAAA,CAAAI,OAAA;cACAC,IAAA;YACA;UACA;QACA;MACA,GACAuH,KAAA;QACAL,OAAA,CAAArH,QAAA;UACAG,IAAA;UACAF,OAAA;QACA;MACA;IACA;IACA0H,UAAA,WAAAA,WAAAlC,GAAA;MAAA,IAAAmC,OAAA;MACA,KAAAxN,KAAA;MACA,KAAAkI,iBAAA;MACA,KAAAC,SAAA,WAAAC,CAAA;QACAiD,GAAA,CAAAoC,UAAA;QACAD,OAAA,CAAAhK,KAAA,YAAA6E,IAAA,CAAAgD,GAAA;MACA;IACA;IACAqC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,UAAA,QAAAnO,UAAA,CAAAgK,MAAA,WAAAtH,IAAA;QACA,OAAAA,IAAA,CAAA0L,eAAA,YAAA1L,IAAA,CAAA0L,eAAA;MACA;MACA,IAAAD,UAAA,CAAA3H,MAAA;QACA,KAAAL,QAAA;UACAG,IAAA;UACAF,OAAA;QACA;MACA;QACA,KAAA7F,KAAA;QACA,KAAAkI,iBAAA;QACA,KAAAC,SAAA,WAAAC,CAAA;UACAuF,OAAA,CAAAnK,KAAA,YAAA6E,IAAA,CAAAsF,OAAA,CAAAlO,UAAA,EAAAkO,OAAA,CAAA7N,aAAA;QACA;MACA;IACA;IACAgO,UAAA,WAAAA,WAAAzC,GAAA;MAAA,IAAA0C,OAAA;MACA,KAAA/N,KAAA;MACA,KAAAkI,iBAAA;MACA,KAAAC,SAAA,WAAAC,CAAA;QACAiD,GAAA,CAAAoC,UAAA;QACAM,OAAA,CAAAvK,KAAA,YAAA6E,IAAA,CAAAgD,GAAA;MACA;IACA;IACA2C,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,OAAApK,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAmK,SAAA;QAAA,IAAAC,GAAA;QAAA,OAAArK,mBAAA,GAAAM,IAAA,UAAAgK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9J,IAAA,GAAA8J,SAAA,CAAA7J,IAAA;YAAA;cACA2J,GAAA;gBACA3I,KAAA,EAAAyI,OAAA,CAAAnM,SAAA;gBACAwM,kBAAA,EAAAL,OAAA,CAAAxO,UAAA,CACA0G,GAAA,WAAAmC,CAAA;kBAAA,OAAAA,CAAA,CAAAC,iBAAA;gBAAA,GACAC,QAAA;gBACA+F,gBAAA,EAAAN,OAAA,CAAA7L,UAAA,CAAA7D;cACA;cACAhF,kBAAA,CAAA4U,GAAA,EAAA1I,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACA6I,MAAA,CAAAC,IAAA,CAAAvT,UAAA,CAAA+S,OAAA,CAAAS,QAAA,EAAAhJ,GAAA,CAAAM,IAAA;gBACA;kBACAiI,OAAA,CAAArI,QAAA,CAAAkD,KAAA,CAAApD,GAAA,CAAAI,OAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAuI,SAAA,CAAApJ,IAAA;UAAA;QAAA,GAAAiJ,QAAA;MAAA;IACA;IACA;IACAS,aAAA,WAAAA,cAAAxN,OAAA;MACA8B,OAAA,CAAAC,GAAA,CAAA/B,OAAA;MACA,KAAAA,OAAA,GAAAA,OAAA;MACA,KAAAyN,cAAA;IACA;IACAA,cAAA,WAAAA,eAAA;MACA,IAAAC,QAAA;QACAC,YAAA;QACAvQ,IAAA,OAAA6D,UAAA,CAAA7D,IAAA;QACAtC,IAAA,OAAAmG,UAAA,CAAAgF;MACA;MACA,KAAA5D,KAAA,CAAAuL,MAAA,CAAAC,UAAA,CACA,OACAH,QAAA,EACA,MACA,MACA,KAAApM,GAAA,EACA,KAAAtB,OAAA,EACA,KAAA/C,YACA;IACA;IACA6Q,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MAAA,OAAArL,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAoL,SAAA;QAAA,OAAArL,mBAAA,GAAAM,IAAA,UAAAgL,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9K,IAAA,GAAA8K,SAAA,CAAA7K,IAAA;YAAA;cACAvB,OAAA,CAAAC,GAAA,CAAAgM,OAAA,CAAA9Q,YAAA,CAAAa,UAAA;cAAA,KACAiQ,OAAA,CAAA9Q,YAAA,CAAAa,UAAA;gBAAAoQ,SAAA,CAAA7K,IAAA;gBAAA;cAAA;cAAA6K,SAAA,CAAA7K,IAAA;cAAA,OACAvJ,SAAA;gBAAA8E,KAAA;cAAA;YAAA;cAAAsP,SAAA,CAAA7K,IAAA;cAAA,OACArL,wBAAA,CAAAiR,aAAA,CAAAA,aAAA;gBACA5E,KAAA,EAAA0J,OAAA,CAAApN;cAAA,GACAoN,OAAA,CAAA9Q,YAAA,GACA8Q,OAAA,CAAArR,SAAA,CACA,EAAA4H,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAuJ,OAAA,CAAAtJ,QAAA,CAAA0J,OAAA;kBACAJ,OAAA,CAAA/L,SAAA;kBACA+L,OAAA,CAAApK,aAAA;gBACA;kBACAoK,OAAA,CAAAtJ,QAAA,CAAAkD,KAAA,CAAApD,GAAA,CAAAI,OAAA;gBACA;cACA;YAAA;cAAAuJ,SAAA,CAAA7K,IAAA;cAAA;YAAA;cAEA0K,OAAA,CAAAtJ,QAAA,CAAA2J,OAAA;YAAA;YAAA;cAAA,OAAAF,SAAA,CAAApK,IAAA;UAAA;QAAA,GAAAkK,QAAA;MAAA;IAEA;IACAK,WAAA,WAAAA,YAAA;MACA,KAAAjQ,aAAA;IACA;IACA2I,iBAAA,WAAAA,kBAAAnI,KAAA,EAAA0P,SAAA;MACA,KAAA1P,KAAA,GAAAA,KAAA;MACA,KAAAP,gBAAA,GAAAiQ,SAAA;MACA,KAAAlQ,aAAA;IACA;IACA;IACA0H,YAAA,WAAAA,aAAAyI,KAAA;MAAA,IAAAC,SAAA,GAAAC,SAAA,CAAA3J,MAAA,QAAA2J,SAAA,QAAAtP,SAAA,GAAAsP,SAAA;MACA,KAAArP,aAAA;MACA,IAAAmP,KAAA;QACA,KAAAlM,KAAA,CAAApF,YAAA,CAAAyR,WAAA;QACA,KAAAtP,aAAA;QACA,KAAAlB,KAAA;MACA;MACAsQ,SAAA,SAAAxM,SAAA;IACA;IACA;IACA2M,kBAAA,WAAAA,mBAAAzE,GAAA;MAAA,IAAA0E,OAAA;MACA9M,OAAA,CAAAC,GAAA;MACA,KAAAlD,KAAA;MACA,KAAAkI,iBAAA;MACA,KAAAC,SAAA,WAAAC,CAAA;QACAiD,GAAA,CAAAoC,UAAA;QACAsC,OAAA,CAAAvM,KAAA,YAAA6E,IAAA,CAAAgD,GAAA;MACA;IACA;IACA;IACA2E,eAAA,WAAAA,gBAAA3E,GAAA;MAAA,IAAA4E,OAAA;MACA,KAAAjQ,KAAA;MACA,KAAAkI,iBAAA;MACA,KAAAC,SAAA,WAAAC,CAAA;QACAiD,GAAA,CAAAoC,UAAA;QACAwC,OAAA,CAAAzM,KAAA,YAAA6E,IAAA,CAAAgD,GAAA;MACA;IACA;IACA;IACA6E,cAAA,WAAAA,eAAA7E,GAAA;MAAA,IAAA8E,OAAA;MACA,KAAAnQ,KAAA;MACA,KAAAkI,iBAAA;MACA,KAAAC,SAAA,WAAAC,CAAA;QACAiD,GAAA,CAAAoC,UAAA;QACA0C,OAAA,CAAA3M,KAAA,YAAA6E,IAAA,CAAAgD,GAAA;MACA;IACA;IACA;IACAX,iBAAA,WAAAA,kBAAA;MAAA,IAAA0F,OAAA;MACA,KAAAxS,YAAA;MACA,IAAAgK,gBAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAA3J,YAAA;MACA,IAAA4J,eAAA,GAAAJ,gBAAA,CAAAhJ,cAAA,CAAAqJ,IAAA;MACA,OAAAL,gBAAA,CAAAhJ,cAAA;MACAtF,iBAAA,CAAA8Q,aAAA,CAAAA,aAAA,CAAAA,aAAA;QACA5E,KAAA,OAAA1D;MAAA,GACA,KAAAjE,SAAA,GACA+J,gBAAA;QACAI,eAAA,EAAAA;MAAA,EACA,EAAAvC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAyK,OAAA,CAAA5P,gBAAA,GAAA6P,IAAA,CAAAC,KAAA,CAAA5K,GAAA,CAAAM,IAAA,CAAAuK,SAAA;UACAH,OAAA,CAAA3P,kBAAA,GACA4P,IAAA,CAAAC,KAAA,CAAA5K,GAAA,CAAAM,IAAA,CAAAwK,aAAA;UACAJ,OAAA,CAAA1P,mBAAA,GACA2P,IAAA,CAAAC,KAAA,CAAA5K,GAAA,CAAAM,IAAA,CAAAyK,YAAA;UACAL,OAAA,CAAAzP,wBAAA,GACA0P,IAAA,CAAAC,KAAA,CAAA5K,GAAA,CAAAM,IAAA,CAAA0K,gBAAA;UACAN,OAAA,CAAAxP,gBAAA,GACAyP,IAAA,CAAAC,KAAA,CAAA5K,GAAA,CAAAM,IAAA,CAAA2K,YAAA;UACAP,OAAA,CAAAvP,iBAAA,GACAwP,IAAA,CAAAC,KAAA,CAAA5K,GAAA,CAAAM,IAAA,CAAA4K,aAAA;UACAR,OAAA,CAAAtP,gBAAA,GACAuP,IAAA,CAAAC,KAAA,CAAA5K,GAAA,CAAAM,IAAA,CAAA6K,YAAA;UACAT,OAAA,CAAArP,iBAAA,GACAsP,IAAA,CAAAC,KAAA,CAAA5K,GAAA,CAAAM,IAAA,CAAA8K,aAAA;UACA7N,OAAA,CAAAC,GAAA,8BAAAkN,OAAA,CAAA1P,mBAAA;QACA;UACA0P,OAAA,CAAAxK,QAAA,CAAAkD,KAAA,CAAApD,GAAA,CAAAI,OAAA;QACA;QACAsK,OAAA,CAAAxS,YAAA;MACA;IACA;IACAmT,cAAA,WAAAA,eAAAC,KAAA;MAAA,IAAAC,OAAA;MACA,KAAAxR,UAAA,GAAAuR,KAAA,CAAAE,OAAA;MACA,KAAA1Q,gBAAA;MACA,KAAAC,kBAAA;MACA,KAAAC,mBAAA;MACA,KAAAC,wBAAA;MACA,KAAAC,gBAAA;MACA,KAAAC,iBAAA;MACA,KAAAC,gBAAA;MACA,IAAAqQ,uBAAA;MACA,IAAAC,4BAAA;MACA,IAAAC,qBAAA;MACA,IAAAC,qBAAA;MACA,SAAA7R,UAAA,CAAAwG,MAAA;QACA,KAAAxG,UAAA,CAAA0L,OAAA,WAAAhJ,IAAA;UACA,IAAAoP,aAAA,GACApP,IAAA,CAAA0L,eAAA,eAAA1L,IAAA,CAAA0L,eAAA;UACAoD,OAAA,CAAAzQ,gBAAA,IAAA2B,IAAA,CAAAqP,GAAA;UACAP,OAAA,CAAAxQ,kBAAA,IAAAgR,MAAA,CAAAtP,IAAA,CAAA0L,eAAA;UACAoD,OAAA,CAAArQ,gBAAA,IAAAuB,IAAA,CAAAwO,YAAA;UACA,IAAAxO,IAAA,CAAAtD,QAAA;YACAoS,OAAA,CAAAnQ,gBAAA,IAAAqB,IAAA,CAAAqP,GAAA;YACAF,qBAAA,IAAAnP,IAAA,CAAAuP,YAAA;UACA;UACAP,uBAAA,IAAAhP,IAAA,CAAAuP,YAAA;UACAN,4BAAA,IAAAjP,IAAA,CAAAwP,MAAA,GAAAJ,aAAA;UACAF,qBAAA,IAAAlP,IAAA,CAAAyO,aAAA;QACA;QACA,KAAAlQ,mBAAA,GACA2P,IAAA,CAAAC,KAAA,CAAAa,uBAAA,QAAAjQ,UAAA;QACA,KAAAP,wBAAA,GACA0P,IAAA,CAAAC,KAAA,CAAAc,4BAAA,QAAAlQ,UAAA,WACA;QACA,KAAAL,iBAAA,GACAwP,IAAA,CAAAC,KAAA,CAAAe,qBAAA,QAAAnQ,UAAA;QACA,KAAAH,iBAAA,GACAsP,IAAA,CAAAC,KAAA,CAAAgB,qBAAA,QAAApQ,UAAA;MACA;QACA,KAAAwJ,iBAAA;MACA;IACA;IACAkH,kBAAA,WAAAA,mBAAA;MACA;IAAA,CACA;IACAC,WAAA,WAAAA,YAAAxG,GAAA;MACA,IAAAyG,WAAA,GAAAzG,GAAA,CAAA0G,OAAA,GAAA1G,GAAA,CAAA0G,OAAA,CAAAC,KAAA;MACA,IAAAC,WAAA,GAAA5G,GAAA,CAAA6G,QAAA,GAAA7G,GAAA,CAAA6G,QAAA,CAAAF,KAAA;MACA,IAAAC,WAAA,CAAAhM,MAAA;QACA,KAAAL,QAAA;UACAC,OAAA;UACAE,IAAA;QACA;QACA;MACA;MACA,IAAA+L,WAAA,CAAA7L,MAAA,QAAAgM,WAAA,CAAAhM,MAAA;QACA,KAAAtE,aAAA,GAAAmQ,WAAA;MACA;MACA,IAAAA,WAAA,CAAA7L,MAAA,QAAAgM,WAAA,CAAAhM,MAAA;QACA,KAAArE,eAAA,GAAAkQ,WAAA,CAAA3L,GAAA,WAAAhE,IAAA,EAAAgQ,KAAA;UAAA;YACAlW,IAAA,EAAAkG,IAAA;YACAjF,KAAA,EAAAiF,IAAA;YACAiQ,GAAA,EAAAH,WAAA,CAAAE,KAAA;UACA;QAAA;MACA;MAEA,KAAAE,kBAAA,CAAAhH,GAAA;IACA;IAEAgH,kBAAA,WAAAA,mBAAAhH,GAAA;MAAA,IAAAiH,OAAA;MACA,IAAAC,cAAA,GAAAlH,GAAA,CAAA9C,iBAAA;MACA/M,mBAAA;QAAA+W,cAAA,EAAAA,cAAA;QAAA/M,KAAA,OAAA1D;MAAA,GAAA2D,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA,IAAAmM,WAAA;YACA,iBAAApM,GAAA,CAAAM,IAAA,IAAAwM,aAAA;YACA,WAAA9M,GAAA,CAAAM,IAAA,IAAA/E,OAAA;YACA,YAAAyE,GAAA,CAAAM,IAAA,IAAAyM,QAAA;YACA,QAAApH,GAAA,CAAA9M,IAAA;YACA,kBAAA8M,GAAA,CAAArM;UACA;UACAsT,OAAA,CAAA9O,KAAA,CAAAkP,eAAA,CAAAC,OAAA,CAAAb,WAAA;QACA;MACA;IACA;IAEAc,aAAA,WAAAA,cAAAC,GAAA,EAAAC,KAAA;MACA7P,OAAA,CAAAC,GAAA,CAAA2P,GAAA,EAAAC,KAAA;MACA,IAAAC,OAAA,GAAAF,GAAA,CAAA5W,IAAA;MACA;MACA,IAAA+W,UAAA,QAAApR,eAAA,CAAAU,IAAA,CACA,UAAA2Q,IAAA;QAAA,OAAAA,IAAA,CAAAhX,IAAA,KAAA8W,OAAA;MAAA,CACA;MACA,IAAAC,UAAA;QACA/P,OAAA,CAAAC,GAAA,SAAA8P,UAAA,CAAAZ,GAAA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAc,eAAA,WAAAA,gBAAAjW,KAAA,EAAAZ,IAAA,EAAA8W,IAAA;MACA,IAAAC,GAAA,GAAAnW,KAAA,CAAA+U,KAAA,CAAAhW,YAAA;MACA,IAAAqX,QAAA,GAAAD,GAAA;MACA,IAAAE,SAAA,GAAAF,GAAA;MACA,KAAAnW,KAAA;MACA,IAAAsW,UAAA,GAAAJ,IAAA,CAAAK,MAAA;MACA,IAAAC,MAAA,IAAAN,IAAA,CAAAjW,KAAA;MACA,IAAAwW,MAAA,IACArX,IAAA,CAAA2J,IAAA,CAAA2N,gBAAA,GACA,QACAtX,IAAA,CAAA2J,IAAA,CAAAK,WAAA,GACA,QACA,MACA;MACA,IAAApC,KAAA;MACA,OAAAA,KAAA,GAAAkP,IAAA,CAAAlP,KAAA;QACAwP,MAAA,MAAA7H,MAAA,CAAAgI,kBAAA,CAAAH,MAAA,IAAAF,UAAA,CAAArW,KAAA;QACAwW,MAAA,MAAA9H,MAAA,CAAAgI,kBAAA,CACAF,MAAA,IACArX,IAAA,CAAA2J,IAAA,CAAA2N,gBAAA,GACA,QACAtX,IAAA,CAAA2J,IAAA,CAAAK,WAAA,GACA,QACA,OACA;QACAkN,UAAA,GAAAA,UAAA,CAAAC,MAAA;QACAvP,KAAA;MACA;MACAwP,MAAA,GAAAA,MAAA,CAAAhK,MAAA,WAAAnB,CAAA;QAAA,SAAAA,CAAA;MAAA;MACAoL,MAAA,GAAAA,MAAA,CAAAjK,MAAA,WAAAnB,CAAA;QAAA,SAAAA,CAAA;MAAA;MACA,IAAAuL,WAAA;MACA,IAAAC,YAAA;MACA,SAAAxW,UAAA;QACAwW,YAAA,GAAAJ,MAAA,CAAAxR,IAAA,WAAA6R,CAAA;UAAA,OAAAA,CAAA,CAAAjK,OAAA,CAAAwJ,SAAA;QAAA;MACA;MACA,SAAAjW,WAAA;QACAwW,WAAA,GAAAJ,MAAA,CAAAvR,IAAA,WAAA6R,CAAA;UAAA,OAAAA,CAAA,CAAAjK,OAAA,CAAAuJ,QAAA;QAAA;MACA;MACA,OAAAQ,WAAA,IAAAC,YAAA;IACA;IACA/O,WAAA,WAAAA,YAAA;MAAA,IAAAiP,OAAA;MAAA,OAAAnQ,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkQ,SAAA;QAAA,IAAAC,WAAA;QAAA,IAAAC,MAAA,EAAAzO,GAAA,EAAA0O,KAAA,EAAA/X,IAAA;QAAA,OAAAyH,mBAAA,GAAAM,IAAA,UAAAiQ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/P,IAAA,GAAA+P,SAAA,CAAA9P,IAAA;YAAA;cACA2P,MAAA;gBACA3O,KAAA,EAAAwO,OAAA,CAAAlS,SAAA;gBACAyS,WAAA;cACA;cAAAD,SAAA,CAAA9P,IAAA;cAAA,OACA9I,WAAA,CAAAyY,MAAA;YAAA;cAAAzO,GAAA,GAAA4O,SAAA,CAAA3P,IAAA;cACA;cACAyP,KAAA,MAAAxI,MAAA,CAAAoI,OAAA,CAAAnS,SAAA;cACAxF,IAAA,GAAAqJ,GAAA,CAAAM,IAAA,CAAA1D,IAAA,WAAAgG,CAAA;gBAAA,OAAAA,CAAA,CAAAhB,KAAA,KAAA8M,KAAA;cAAA;cAEAJ,OAAA,CAAAzS,WAAA;gBACAiT,MAAA;gBACAhS,EAAA,EAAAnG,IAAA,CAAAmG,EAAA;gBACAvG,IAAA,EAAAI,IAAA,CAAAiL,KAAA;gBACAwH,YAAA,EAAAzS,IAAA,CAAAkC,IAAA;gBACAA,IAAA,GAAA2V,WAAA,GAAA7X,IAAA,CAAA2J,IAAA,cAAAkO,WAAA,uBAAAA,WAAA,CAAAO;cACA;cAEAxR,OAAA,CAAAC,GAAA,CAAA8Q,OAAA,CAAAzS,WAAA;YAAA;YAAA;cAAA,OAAA+S,SAAA,CAAArP,IAAA;UAAA;QAAA,GAAAgP,QAAA;MAAA;IACA;IACA;IACAS,YAAA,WAAAA,aAAA;MACA,KAAAlR,KAAA,CAAAmR,gBAAA,CAAA3F,UAAA,CACA,OACA,KAAAzN,WAAA,EACA,IACA,OACA,KAAAnD,YAAA,CAAAY,cAAA,EACA,KACA;IACA;IACA;IACA4V,WAAA,WAAAA,YAAAvJ,GAAA;MACApI,OAAA,CAAAC,GAAA,CAAAmI,GAAA;MACA,KAAA7J,WAAA;MACA,KAAAC,gBAAA,GAAA4J,GAAA,CAAA9M,IAAA;MACA,KAAAmD,eAAA,GAAA2J,GAAA;IACA;EACA;AACA", "ignoreList": []}]}
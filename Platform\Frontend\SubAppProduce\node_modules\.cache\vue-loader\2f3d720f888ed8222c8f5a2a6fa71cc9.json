{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\sys\\professional-category\\index.vue?vue&type=template&id=66081efb&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\sys\\professional-category\\index.vue", "mtime": 1757468113629}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXY+CiAgPGRpdiBjbGFzcz0icGFnZS1jb250YWluZXIiPgogICAgPGVsLXJvdyB0eXBlPSJmbGV4IiBqdXN0aWZ5PSJzcGFjZS1iZXR3ZWVuIiBzdHlsZT0ibWFyZ2luLWJvdHRvbTogMTZweCI+CiAgICAgIDxlbC1jb2wgOnNwYW49IjQiPgogICAgICAgIDxlbC1idXR0b24gdi1pZj0iZmFsc2UiIHR5cGU9InByaW1hcnkiIEBjbGljaz0iYWRkUHJvZmVzc2lvbiI+5paw5aKe5LiT5LiaPC9lbC1idXR0b24+CiAgICAgIDwvZWwtY29sPgogICAgICA8ZWwtY29sIDpzcGFuPSIyMCI+CiAgICAgICAgPGVsLXJvdyB0eXBlPSJmbGV4IiBqdXN0aWZ5PSJlbmQiPgogICAgICAgICAgPGVsLWlucHV0IHYtbW9kZWw9InNlYXJjaFZhbHVlIiBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5YWz6ZSu5a2XIiBzdHlsZT0id2lkdGg6MjUwcHggIiBjbGVhcmFibGUgLz4KICAgICAgICAgIDxkaXY+CiAgICAgICAgICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgc3R5bGU9Im1hcmdpbi1sZWZ0OiAxNnB4IiBAY2xpY2s9ImdldERhdGEiPuaQnOe0ojwvZWwtYnV0dG9uPgogICAgICAgICAgPC9kaXY+CiAgICAgICAgPC9lbC1yb3c+CiAgICAgIDwvZWwtY29sPgogICAgPC9lbC1yb3c+CgogICAgPGRpdiBjbGFzcz0ibGlzdC13cmFwcGVyIj4KICAgICAgPGVsLXRhYmxlCiAgICAgICAgcmVmPSJ0YWJsZSIKICAgICAgICB0YWJsZWNvZGU9InBsbV9wcm9mZXNzaW9uYWx0eXBlX2xpc3RfcHJvIgogICAgICAgIDpjdXN0b20tcGFyYW09InsgaXNfU3lzdGVtOiBmYWxzZSwgdHlwZWlkOiAnJywgbmFtZTogc2VhcmNoVmFsdWUsIGNvbXBhbnlJZDogY29tcGFueUlkIH0iCiAgICAgICAgQGdldC1zZWxlY3Rpb24tZGF0YT0iZ2V0U2VsZWN0aW9uRGF0YSIKICAgICAgICBAZ2V0YnV0dG9uPSJnZXRDbGljayIKICAgICAgLz4KICAgIDwvZGl2PgogIDwvZGl2PgogIDxiaW1kaWFsb2cgcmVmPSJkaWFsb2ciIEBnZXREYXRhPSJnZXREYXRhIiAvPgogIDxub2RlTGlzdCByZWY9Im5vZGVMaXN0IiBAZ2V0RGF0YT0iZ2V0RGF0YSIgLz4KPC9kaXY+Cg=="}, null]}
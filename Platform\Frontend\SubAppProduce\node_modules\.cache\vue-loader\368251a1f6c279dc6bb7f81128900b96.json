{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\quality_Inspection\\quality_summary\\components\\spotCheck.vue?vue&type=style&index=0&id=2cb2c2b6&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\quality_Inspection\\quality_summary\\components\\spotCheck.vue", "mtime": 1757572678833}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KQGltcG9ydCAifkAvc3R5bGVzL21peGluLnNjc3MiOw0KQGltcG9ydCAifkAvc3R5bGVzL3ZhcmlhYmxlcy5zY3NzIjsNCi5zZWFyY2hfd3JhcHBlciB7DQogIHBhZGRpbmc6IDE2cHggMTZweCAwOw0KICBib3gtc2l6aW5nOiBib3JkZXItYm94Ow0KICA6OnYtZGVlcCAuZWwtZm9ybS1pdGVtIHsNCiAgICAuZWwtZm9ybS1pdGVtX19jb250ZW50IHsNCiAgICAgICYgPiAuZWwtaW5wdXQgew0KICAgICAgICB3aWR0aDogMjIwcHg7DQogICAgICB9DQogICAgICAmID4gLmVsLXNlbGVjdCB7DQogICAgICAgIHdpZHRoOiAyMjBweDsNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg0KLmNzLWJvdHRvbS13YXBwZXIgew0KICBwYWRkaW5nOiAwIDE2cHg7DQogIGhlaWdodDogMTAwJTsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgLnRiLXggew0KICAgIGZsZXg6IDE7DQogICAgaGVpZ2h0OiAwOw0KICB9DQoNCiAgLnBhZ2luYXRpb24tY29udGFpbmVyIHsNCiAgICB0ZXh0LWFsaWduOiByaWdodDsNCiAgICBwYWRkaW5nOiAxNnB4Ow0KICAgIG1hcmdpbjogMDsNCiAgfQ0KDQogIC5kYXRhLWluZm8gew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIH0NCn0NCg0KLmJ5LWRvdCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAmOmJlZm9yZSB7DQogICAgY29udGVudDogIiI7DQogICAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICAgIHdpZHRoOiA1cHg7DQogICAgaGVpZ2h0OiA1cHg7DQogICAgYmFja2dyb3VuZDogI2Y1NmM2YzsNCiAgICBib3JkZXItcmFkaXVzOiA1MCU7DQogICAgbWFyZ2luLXJpZ2h0OiA1cHg7DQogIH0NCn0NCi5ieS1kb3Qtc3VjY2VzcyB7DQogIGNvbG9yOiAjNjdjMjNhOw0KICAmOmJlZm9yZSB7DQogICAgYmFja2dyb3VuZDogIzY3YzIzYTsNCiAgfQ0KfQ0KLmJ5LWRvdC1wcmltYXJ5IHsNCiAgY29sb3I6ICM0MDllZmY7DQogICY6YmVmb3JlIHsNCiAgICBiYWNrZ3JvdW5kOiAjNDA5ZWZmOw0KICB9DQp9DQouYnktZG90LWZhaWwgew0KICBjb2xvcjogI2ZmMDAwMDsNCiAgJjpiZWZvcmUgew0KICAgIGJhY2tncm91bmQ6ICNmZjAwMDA7DQogIH0NCn0NCi5ieS1kb3QtaW5mbyB7DQogICY6YmVmb3JlIHsNCiAgICBiYWNrZ3JvdW5kOiAjOTA5Mzk5Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["spotCheck.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6WA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "spotCheck.vue", "sourceRoot": "src/views/PRO/quality_Inspection/quality_summary/components", "sourcesContent": ["<template>\r\n  <div style=\"height: 100%\">\r\n    <!--    <div class=\"table_warrap\">\r\n      <div class=\"table_content\">\r\n        <el-main\r\n          v-loading=\"loading\"\r\n          class=\"no-v-padding\"\r\n          style=\"padding: 0; height: 100%\"\r\n        >\r\n          <DynamicDataTable\r\n            ref=\"table\"\r\n            :config=\"tbConfig\"\r\n            :columns=\"columns\"\r\n            :data=\"tbData\"\r\n            :total=\"pageInfo.TotalCount\"\r\n            :page=\"pageInfo.Page\"\r\n            stripe\r\n            height=\"100%\"\r\n            class=\"cs-plm-dy-table\"\r\n            border\r\n            @gridPageChange=\"gridPageChange\"\r\n            @gridSizeChange=\"gridSizeChange\"\r\n            @multiSelectedChange=\"multiSelectedChange\"\r\n          >\r\n            <template slot=\"Number\" slot-scope=\"{ row }\">\r\n              <span>{{ row.Number || \"-\" }}</span>\r\n            </template>\r\n            <template slot=\"Rectify_Date\" slot-scope=\"{ row }\">\r\n              <span>{{ row.Rectify_Date || \"-\" }}</span>\r\n            </template>\r\n            <template slot=\"Rectifier_name\" slot-scope=\"{ row }\">\r\n              <span>{{ row.Rectifier_name || \"-\" }}</span>\r\n            </template>\r\n            <template slot=\"Partcipant_name\" slot-scope=\"{ row }\">\r\n              <span>{{ row.Partcipant_name || \"-\" }}</span>\r\n            </template>\r\n            <template slot=\"Check_Result\" slot-scope=\"{ row }\">\r\n              <span>{{ row.Check_Result || \"-\" }}</span>\r\n            </template>\r\n            <template slot=\"Pick_Date\" slot-scope=\"{ row }\">\r\n              <span>{{ row.Pick_Date || \"-\" }}</span>\r\n            </template>\r\n            <template slot=\"Rectifier_Code\" slot-scope=\"{ row }\">\r\n              <el-button\r\n                v-if=\"Boolean(row.Rectifier_Code)\"\r\n                type=\"text\"\r\n                @click=\"handelSheet(row)\"\r\n              >{{ row.Rectifier_Code }}</el-button>\r\n              <span v-else>-</span>\r\n            </template>\r\n            <template slot=\"op\" slot-scope=\"{ row }\">\r\n              <el-button\r\n                v-if=\"row.Status != '草稿'\"\r\n                type=\"text\"\r\n                @click=\"handelView(row)\"\r\n              >查看</el-button>\r\n            </template>\r\n          </DynamicDataTable>\r\n        </el-main>\r\n      </div>\r\n    </div>-->\r\n    <div class=\"cs-bottom-wapper\">\r\n      <div class=\"fff tb-x\">\r\n        <vxe-table\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          :loading=\"loading\"\r\n          element-loading-spinner=\"el-icon-loading\"\r\n          element-loading-text=\"拼命加载中\"\r\n          empty-text=\"暂无数据\"\r\n          class=\"cs-vxe-table\"\r\n          height=\"100%\"\r\n          align=\"left\"\r\n          stripe\r\n          :data=\"tbData\"\r\n          resizable\r\n          :tooltip-config=\"{ enterable: true}\"\r\n          :checkbox-config=\"{checkField: 'checked', trigger: 'row'}\"\r\n          :row-config=\"{ isHover: true }\"\r\n          @checkbox-all=\"multiSelectedChange\"\r\n          @checkbox-change=\"multiSelectedChange\"\r\n        >\r\n          <vxe-column fixed=\"left\" type=\"checkbox\" width=\"44\" />\r\n          <template v-for=\"item in columns\">\r\n            <vxe-column\r\n              :key=\"item.Code\"\r\n              :min-width=\"item.Width\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :align=\"item.Align\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n            >\r\n              <template v-if=\"item.Code === 'Rectifier_Code' \" #default=\"{ row }\">\r\n                <el-button\r\n                  v-if=\"Boolean(row.Rectifier_Code)\"\r\n                  type=\"text\"\r\n                  @click=\"handelSheet(row)\"\r\n                >{{ row.Rectifier_Code }}</el-button>\r\n                <span v-else>-</span>\r\n              </template>\r\n              <template v-if=\"item.Code === 'Check_Result' \" #default=\"{ row }\">\r\n                <span v-if=\"!row.Check_Result\">-</span>\r\n                <template v-else>\r\n                  <el-tag v-if=\"row.Check_Result==='合格'\" type=\"success\">{{ row.Check_Result }}</el-tag>\r\n                  <el-tag v-else type=\"danger\">{{ row.Check_Result }}</el-tag>\r\n                </template>\r\n              </template>\r\n              <template v-else-if=\"item.Code === 'Status'\" #default=\"{ row }\">\r\n                <span v-if=\"row.Status === '已完成'\" class=\"by-dot by-dot-success\">\r\n                  {{ row.Status || \"-\" }}\r\n                </span>\r\n                <span v-else-if=\"row.Status === '待复核' || row.Status === '待整改'\" class=\"by-dot by-dot-primary\">\r\n                  {{ row.Status || \"-\" }}\r\n                </span>\r\n                <span v-else-if=\"row.Status === '待质检' || row.Status === '草稿'\" class=\"by-dot by-dot-info\">\r\n                  {{ row.Status || \"-\" }}\r\n                </span>\r\n                <span v-else>\r\n                  {{ row.Status || \"-\" }}\r\n                </span>\r\n              </template>\r\n              <template v-else #default=\"{ row }\">\r\n                <span>{{ row[item.Code] | displayValue }}</span>\r\n              </template>\r\n            </vxe-column>\r\n          </template>\r\n          <vxe-column fixed=\"right\" title=\"操作\" align=\"center\" width=\"60\">\r\n            <template #default=\"{ row }\">\r\n              <el-button\r\n                v-if=\"row.Status != '草稿'\"\r\n                type=\"text\"\r\n                @click=\"handelView(row)\"\r\n              >查看</el-button>\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n      </div>\r\n      <div class=\"data-info\">\r\n        <el-tag\r\n          size=\"medium\"\r\n          class=\"info-x\"\r\n        >已选 {{ selectList.length }} 条数据\r\n        </el-tag>\r\n        <Pagination\r\n          :total=\"total\"\r\n          :page-sizes=\"tablePageSize\"\r\n          :page.sync=\"queryInfo.Page\"\r\n          :limit.sync=\"queryInfo.PageSize\"\r\n          @pagination=\"pageChange\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      ref=\"content\"\r\n      v-el-drag-dialog\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"width\"\r\n      class=\"plm-custom-dialog\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component :is=\"currentComponent\" ref=\"content\" @close=\"handleClose\" />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport InspectDoc from './add/InspectDoc.vue'\r\nimport rectificationSheet from './rectification/rectificationSheet'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { GetPageQualitySummary } from '@/api/PRO/qualityInspect/start-Inspect'\r\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'\r\nimport elDragDialog from '@/directive/el-drag-dialog'\r\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\r\nimport { timeFormat } from '@/filters'\r\nimport { ExportInspsectionSummaryInfo } from '@/api/PRO/factorycheck'\r\nimport { combineURL } from '@/utils'\r\nimport Pagination from '@/components/Pagination/index.vue'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nexport default {\r\n  directives: { elDragDialog },\r\n  components: {\r\n    Pagination,\r\n    DynamicDataTable,\r\n    rectificationSheet,\r\n    InspectDoc\r\n  },\r\n  mixins: [getTbInfo],\r\n  props: {\r\n    searchDetail: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      width: '60%',\r\n      code: '',\r\n      TypeId: '',\r\n      typeOption: '',\r\n      dialogVisible: false,\r\n      loading: false,\r\n      dialogTitle: '',\r\n      Ismodal: true,\r\n      dialogData: {},\r\n      currentComponent: '',\r\n      tbConfig: {\r\n      },\r\n      Data: [],\r\n      columns: [],\r\n      tbData: [],\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: tablePageSize[0]\r\n      },\r\n      tablePageSize: tablePageSize,\r\n      total: 0,\r\n      gridCode: 'Pro_Inpection_summary_list_spot',\r\n      searchHeight: 0,\r\n      CheckResultData: [],\r\n      CheckNodeList: [], // 质检节点\r\n      CheckObjectData: [], // 质检对象\r\n      check_object_id: '',\r\n      ProjectNameData: [],\r\n      check_object_Name: '',\r\n      selectList: []\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getTypeList()\r\n  },\r\n  methods: {\r\n    handleSearch() {},\r\n    async getTypeList() {\r\n      let res, data\r\n      res = await GetFactoryProfessionalByCode({\r\n        factoryId: localStorage.getItem('CurReferenceId')\r\n      })\r\n      data = res.Data\r\n      if (res.IsSucceed) {\r\n        this.typeOption = Object.freeze(data)\r\n        console.log(this.typeOption)\r\n        if (this.typeOption.length > 0) {\r\n          this.TypeId = this.typeOption[0]?.Id\r\n          this.code = this.typeOption.find((i) => i.Id === this.TypeId).Code\r\n          this.getTableConfig(this.gridCode + ',' + this.code)\r\n        }\r\n        this.fetchData(1)\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    fetchData(page) {\r\n      page && (this.queryInfo.Page = page)\r\n      this.loading = true\r\n      const SeachParams = JSON.parse(JSON.stringify(this.searchDetail))\r\n      const SteelName = SeachParams.SteelName.trim().replaceAll(' ', '\\n')\r\n      if (SeachParams.Pick_Date && SeachParams.Pick_Date.length === 2) {\r\n        SeachParams.BeginDate = SeachParams.Pick_Date[0]\r\n        SeachParams.EndDate = SeachParams.Pick_Date[1]\r\n      } else {\r\n        SeachParams.BeginDate = null\r\n        SeachParams.EndDate = null\r\n      }\r\n      GetPageQualitySummary({\r\n        pageInfo: this.queryInfo,\r\n        ...SeachParams,\r\n        SteelName,\r\n        Check_Style: 0\r\n      })\r\n        .then((res) => {\r\n          if (res.IsSucceed) {\r\n            return this.setGridData(res.Data)\r\n          }\r\n        })\r\n        .catch(console.error)\r\n        .finally(() => {\r\n          // 结束loading\r\n          this.loading = false\r\n        })\r\n    },\r\n    handelSheet(row) {\r\n      console.log(row)\r\n      this.generateComponent('整改单', 'rectificationSheet')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(this.code, row)\r\n      })\r\n    },\r\n    handelView(row) {\r\n      this.generateComponent('查看质检单', 'inspectDoc')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(this.code, row)\r\n      })\r\n    },\r\n\r\n    setGridData(data) {\r\n      this.tbData = this.tbData = data.Data.map((v) => {\r\n        v.Id = v.SheetId // 解决全选框打勾问题\r\n        v.Rectify_Date = v.Rectify_Date\r\n          ? timeFormat(v.Rectify_Date, '{y}-{m}-{d}')\r\n          : '-'\r\n        v.Pick_Date = v.Pick_Date\r\n          ? timeFormat(v.Pick_Date, '{y}-{m}-{d}')\r\n          : '-'\r\n        return v\r\n      })\r\n\r\n      this.total = data.TotalCount\r\n    },\r\n\r\n    multiSelectedChange(array) {\r\n      this.selectList = array.records\r\n      this.$emit('selectChange', this.selectList)\r\n    },\r\n    generateComponent(title, component) {\r\n      this.dialogTitle = title\r\n      this.currentComponent = component\r\n      this.dialogVisible = true\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n      this.fetchData(1)\r\n    },\r\n    exportTb() {\r\n      const SheetIds = this.selectList.map((v) => v.SheetId)\r\n      this.$emit('setExportLoading', true)\r\n      const SeachParams = JSON.parse(JSON.stringify(this.searchDetail))\r\n      const SteelName = SeachParams.SteelName.trim().replaceAll(' ', '\\n')\r\n      if (SeachParams.Pick_Date && SeachParams.Pick_Date.length === 2) {\r\n        SeachParams.BeginDate = SeachParams.Pick_Date[0]\r\n        SeachParams.EndDate = SeachParams.Pick_Date[1]\r\n      } else {\r\n        SeachParams.BeginDate = null\r\n        SeachParams.EndDate = null\r\n      }\r\n      ExportInspsectionSummaryInfo({\r\n        pageInfo: this.queryInfo,\r\n        ...SeachParams,\r\n        SteelName,\r\n        Check_Style: 0,\r\n        SheetIds\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.$emit('setExportLoading', false)\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/styles/mixin.scss\";\r\n@import \"~@/styles/variables.scss\";\r\n.search_wrapper {\r\n  padding: 16px 16px 0;\r\n  box-sizing: border-box;\r\n  ::v-deep .el-form-item {\r\n    .el-form-item__content {\r\n      & > .el-input {\r\n        width: 220px;\r\n      }\r\n      & > .el-select {\r\n        width: 220px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.cs-bottom-wapper {\r\n  padding: 0 16px;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .tb-x {\r\n    flex: 1;\r\n    height: 0;\r\n  }\r\n\r\n  .pagination-container {\r\n    text-align: right;\r\n    padding: 16px;\r\n    margin: 0;\r\n  }\r\n\r\n  .data-info {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n}\r\n\r\n.by-dot {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  &:before {\r\n    content: \"\";\r\n    display: inline-block;\r\n    width: 5px;\r\n    height: 5px;\r\n    background: #f56c6c;\r\n    border-radius: 50%;\r\n    margin-right: 5px;\r\n  }\r\n}\r\n.by-dot-success {\r\n  color: #67c23a;\r\n  &:before {\r\n    background: #67c23a;\r\n  }\r\n}\r\n.by-dot-primary {\r\n  color: #409eff;\r\n  &:before {\r\n    background: #409eff;\r\n  }\r\n}\r\n.by-dot-fail {\r\n  color: #ff0000;\r\n  &:before {\r\n    background: #ff0000;\r\n  }\r\n}\r\n.by-dot-info {\r\n  &:before {\r\n    background: #909399;\r\n  }\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\material-inventory-reconfig\\aux-outbound\\info.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\material-inventory-reconfig\\aux-outbound\\info.vue", "mtime": 1757926768419}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0IF9yZWdlbmVyYXRvclJ1bnRpbWUgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9yZWdlbmVyYXRvclJ1bnRpbWUuanMiOwppbXBvcnQgX2FzeW5jVG9HZW5lcmF0b3IgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9hc3luY1RvR2VuZXJhdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuY29uY2F0LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsdGVyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmluZC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZpbmQtaW5kZXguanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5pbmNsdWRlcy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmpvaW4uanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5wdXNoLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuc2xpY2UuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5zcGxpY2UuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5mdW5jdGlvbi5uYW1lLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5maWx0ZXIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5maW5kLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuZm9yLWVhY2guanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5tYXAuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5qc29uLnN0cmluZ2lmeS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm51bWJlci5jb25zdHJ1Y3Rvci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC5rZXlzLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC5jb25zdHJ1Y3Rvci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC5kb3QtYWxsLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmV4ZWMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuc3RpY2t5LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLnRlc3QuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnNlYXJjaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuZm9yLWVhY2guanMiOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwoKaW1wb3J0IHsgY2xvc2VUYWdWaWV3LCBkZWJvdW5jZSwgZGVlcENsb25lIH0gZnJvbSAnQC91dGlscyc7CmltcG9ydCBQdXJjaGFzZVRiIGZyb20gJy4vY29tcG9uZW50cy9QdXJjaGFzZVRiLnZ1ZSc7CmltcG9ydCBBZGRMaXN0IGZyb20gJy4vY29tcG9uZW50cy9BZGRMaXN0LnZ1ZSc7CmltcG9ydCBBZGRQdXJjaGFzZUxpc3QgZnJvbSAnLi9jb21wb25lbnRzL0FkZFB1cmNoYXNlTGlzdC52dWUnOwppbXBvcnQgSW1wb3J0RmlsZSBmcm9tICcuLi9jb21wb25lbnRzL0ltcG9ydEZpbGUudnVlJzsKaW1wb3J0IFN0YW5kYXJkIGZyb20gJy4vY29tcG9uZW50cy9TdGFuZGFyZC52dWUnOwppbXBvcnQgeyBHZXRQcm9qZWN0UGFnZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vcHJvamVjdCc7CmltcG9ydCBPU1NVcGxvYWQgZnJvbSAnQC92aWV3cy9QUk8vY29tcG9uZW50cy9vc3N1cGxvYWQnOwppbXBvcnQgZ2V0Q29tbW9uRGF0YSBmcm9tICdAL21peGlucy9QUk8vZ2V0LWNvbW1vbi1kYXRhL2luZGV4LmpzJzsKaW1wb3J0IHsgR2V0Rmlyc3RMZXZlbERlcGFydHNVbmRlckZhY3RvcnkgfSBmcm9tICdAL2FwaS9PTUEvbm9uT3BlcmF0aW5nLmpzJzsKaW1wb3J0IHsgQXV4UmV0dXJuQnlSZWNlaXB0LCBHZXRBdXhEZXRhaWxCeVJlY2VpcHQsIEdldFRlYW1MaXN0QnlVc2VyRm9yTWF0ZXJpZWwsIEdldFVzZXJQYWdlIH0gZnJvbSAnQC9hcGkvUFJPL21hdGVyaWFsLXdhcmVob3VzZS9tYXRlcmlhbC1pbnZlbnRvcnktcmVjb25maWcuanMnOwovLyBpbXBvcnQgeyBHZXRGYWN0b3J5UGVvcGxlbGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9iYXNpYy1pbmZvcm1hdGlvbi93b3Jrc2hvcCcKaW1wb3J0IHsgR2V0UHJvY2Vzc0xpc3RCYXNlIH0gZnJvbSAnQC9hcGkvUFJPL3RlY2hub2xvZ3ktbGliJzsKaW1wb3J0IHsgZ2V0RGljdGlvbmFyeSB9IGZyb20gJ0AvdXRpbHMvY29tbW9uJzsKaW1wb3J0IHsgR2V0UHJlZmVyZW5jZVNldHRpbmdWYWx1ZSB9IGZyb20gJ0AvYXBpL3N5cy9zeXN0ZW0tc2V0dGluZyc7CmltcG9ydCB7IFBpY2tPdXRTdG9yZSwgR2V0UGlja091dERldGFpbCB9IGZyb20gJ0AvYXBpL1BSTy9tYXRlcmlhbC13YXJlaG91c2UvbWF0ZXJpYWwtaW52ZW50b3J5LXJlY29uZmlnLmpzJzsKaW1wb3J0IHsgR2V0T01BTGF0ZXN0U3RhdGlzdGljVGltZSB9IGZyb20gJ0AvYXBpL1BSTy9tYXRlcmlhbE1hbmFnZW1lbnQnOwppbXBvcnQgeyBHZXRPc3NVcmwsIEdldENvbXBhbnlEZXBhcnRUcmVlIH0gZnJvbSAnQC9hcGkvc3lzJzsKaW1wb3J0IFJldHVyblRiIGZyb20gJy4vY29tcG9uZW50cy9SZXR1cm5UYi52dWUnOwppbXBvcnQgZ2V0VGFibGVDb25maWcgZnJvbSAnQC9taXhpbnMvUFJPL2dldC10YWJsZS1pbmZvLXByby9pbmRleCc7CmltcG9ydCBXYXJlaG91c2UgZnJvbSAnQC92aWV3cy9QUk8vbWF0ZXJpYWwtcmVjZWlwdC1tYW5hZ2VtZW50L2NvbXBvbmVudHMvV2FyZWhvdXNlLnZ1ZSc7CmltcG9ydCB7IHY0IGFzIHV1aWR2NCB9IGZyb20gJ3V1aWQnOwppbXBvcnQgU2VsZWN0TWF0ZXJpYWxTdG9yZVR5cGUgZnJvbSAnQC9jb21wb25lbnRzL1NlbGVjdC9TZWxlY3RNYXRlcmlhbFN0b3JlVHlwZS9pbmRleC52dWUnOwppbXBvcnQgRHluYW1pY1RhYmxlRmllbGRzIGZyb20gJ0AvY29tcG9uZW50cy9EeW5hbWljVGFibGVGaWVsZHMvaW5kZXgudnVlJzsKaW1wb3J0IHsgR2V0V29ya2luZ1RlYW1zIH0gZnJvbSAnQC9hcGkvUFJPL3RlY2hub2xvZ3ktbGliJzsKaW1wb3J0IFNlbGVjdERlcGFydG1lbnQgZnJvbSAnQC9jb21wb25lbnRzL1NlbGVjdC9TZWxlY3REZXBhcnRtZW50L2luZGV4LnZ1ZSc7CmltcG9ydCBTZWxlY3REZXBhcnRtZW50VXNlciBmcm9tICdAL2NvbXBvbmVudHMvU2VsZWN0L1NlbGVjdERlcGFydG1lbnRVc2VyL2luZGV4LnZ1ZSc7CmltcG9ydCBQaWNrU2VsZWN0IGZyb20gJ0Avdmlld3MvUFJPL21hdGVyaWFsX3Y0L3BpY2tBcHBseS9zZWxlY3QudnVlJzsKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsKICAgIFBpY2tTZWxlY3Q6IFBpY2tTZWxlY3QsCiAgICBTZWxlY3REZXBhcnRtZW50VXNlcjogU2VsZWN0RGVwYXJ0bWVudFVzZXIsCiAgICBTZWxlY3REZXBhcnRtZW50OiBTZWxlY3REZXBhcnRtZW50LAogICAgRHluYW1pY1RhYmxlRmllbGRzOiBEeW5hbWljVGFibGVGaWVsZHMsCiAgICBTZWxlY3RNYXRlcmlhbFN0b3JlVHlwZTogU2VsZWN0TWF0ZXJpYWxTdG9yZVR5cGUsCiAgICBBZGRQdXJjaGFzZUxpc3Q6IEFkZFB1cmNoYXNlTGlzdCwKICAgIFB1cmNoYXNlVGI6IFB1cmNoYXNlVGIsCiAgICBSZXR1cm5UYjogUmV0dXJuVGIsCiAgICBJbXBvcnRGaWxlOiBJbXBvcnRGaWxlLAogICAgV2FyZWhvdXNlOiBXYXJlaG91c2UsCiAgICBTdGFuZGFyZDogU3RhbmRhcmQsCiAgICBBZGRMaXN0OiBBZGRMaXN0LAogICAgT1NTVXBsb2FkOiBPU1NVcGxvYWQKICB9LAogIG1peGluczogW2dldENvbW1vbkRhdGEsIGdldFRhYmxlQ29uZmlnXSwKICBwcm9wczogewogICAgcGFnZVR5cGU6IHsKICAgICAgdHlwZTogTnVtYmVyLAogICAgICBkZWZhdWx0OiB1bmRlZmluZWQKICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgcmV0dXJuIHsKICAgICAgaXNSZXRyYWN0OiBmYWxzZSwKICAgICAgLy8g5piv5ZCm5bGV5byACiAgICAgIHJldHVybmluZzogZmFsc2UsCiAgICAgIHRiTG9hZGluZzogZmFsc2UsCiAgICAgIHByb2plY3RPcHRpb25zOiBbXSwKICAgICAgbXVsdGlwbGVTZWxlY3Rpb246IFtdLAogICAgICAvLyBQYXJ0eVVuaXREYXRhOiBbXSwKICAgICAgLy8gU3VwcGxpZXJEYXRhOiBbXSwKICAgICAgZmFjdG9yeVBlb3BsZWxpc3Q6IFtdLAogICAgICAvLyDpoobmlpnkurrliJfooagKICAgICAgZGVwYXJ0bWVudGxpc3Q6IFtdLAogICAgICBQcm9jZXNzTGlzdDogW10sCiAgICAgIEF1eE91dGJvdW5kVHlwZUxpc3Q6IFtdLAogICAgICBXb3JraW5nVGVhbUxpc3Q6IFtdLAogICAgICAvLyDpoobmlpnnj63nu4TliJfooagKICAgICAgc2VhcmNoRm9ybTogewogICAgICAgIFJhd05hbWU6ICcnLAogICAgICAgIFNwZWM6ICcnCiAgICAgIH0sCiAgICAgIGZvcm06IHsKICAgICAgICBPdXRTdG9yZU5vOiAnJywKICAgICAgICAvLyDlh7rlupPljZXlj7cKICAgICAgICBPdXRTdG9yZVR5cGU6IDEsCiAgICAgICAgLy8g5Ye65bqT57G75Z6LCiAgICAgICAgT3V0U3RvcmVEYXRlOiB0aGlzLmdldERhdGUoKSwKICAgICAgICBVc2VfUHJvY2Vzc2luZ19JZDogJycsCiAgICAgICAgLy8g5bel5bqPaWQKICAgICAgICBQaWNrX0RlcGFydG1lbnRfSWQ6ICcnLAogICAgICAgIC8vIFByb2plY3RJZDogJycsCiAgICAgICAgU3lzUHJvamVjdElkOiAnJywKICAgICAgICBQaWNrX1Byb2plY3RfTmFtZTogJycsCiAgICAgICAgLy8g6aKG55So6aG555uu5ZCN56ewCiAgICAgICAgUmVjZWl2ZVVzZXJJZDogJycsCiAgICAgICAgLy8g6aKG5paZ5Lq6aWQKICAgICAgICBXb3JraW5nVGVhbUlkOiAnJywKICAgICAgICAvLyDpoobmlpnnj63nu4RpZAogICAgICAgIFJlbWFyazogJycsCiAgICAgICAgQXR0YWNobWVudDogJycKICAgICAgfSwKICAgICAgcnVsZXM6IHsKICAgICAgICBPdXRTdG9yZVR5cGU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfor7fpgInmi6knLAogICAgICAgICAgdHJpZ2dlcjogJ2NoYW5nZScKICAgICAgICB9XSwKICAgICAgICBPdXRTdG9yZURhdGU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfor7fpgInmi6knLAogICAgICAgICAgdHJpZ2dlcjogJ2NoYW5nZScKICAgICAgICB9XSwKICAgICAgICBQaWNrX0RlcGFydG1lbnRfSWQ6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfor7fpgInmi6knLAogICAgICAgICAgdHJpZ2dlcjogJ2NoYW5nZScKICAgICAgICB9XSwKICAgICAgICBSZWNlaXZlVXNlcklkOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36YCJ5oupJywKICAgICAgICAgIHRyaWdnZXI6ICdjaGFuZ2UnCiAgICAgICAgfV0KICAgICAgICAvLyBXb3JraW5nVGVhbUlkOiBbCiAgICAgICAgLy8gICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oupJywgdHJpZ2dlcjogJ2NoYW5nZScgfQogICAgICAgIC8vIF0KICAgICAgfSwKICAgICAgdHJlZVBhcmFtc0RlcGFydDogewogICAgICAgICdkZWZhdWx0LWV4cGFuZC1hbGwnOiB0cnVlLAogICAgICAgIGZpbHRlcmFibGU6IGZhbHNlLAogICAgICAgIGNsaWNrUGFyZW50OiB0cnVlLAogICAgICAgIGRhdGE6IFtdLAogICAgICAgIHByb3BzOiB7CiAgICAgICAgICBkaXNhYmxlZDogJ2Rpc2FibGVkJywKICAgICAgICAgIGNoaWxkcmVuOiAnQ2hpbGRyZW4nLAogICAgICAgICAgbGFiZWw6ICdMYWJlbCcsCiAgICAgICAgICB2YWx1ZTogJ0lkJwogICAgICAgIH0KICAgICAgfSwKICAgICAgYmFja2VuZERhdGU6IG51bGwsCiAgICAgIHBpY2tlck9wdGlvbnM6IHsKICAgICAgICBkaXNhYmxlZERhdGU6IGZ1bmN0aW9uIGRpc2FibGVkRGF0ZSh0aW1lKSB7CiAgICAgICAgICByZXR1cm4gdGltZS5nZXRUaW1lKCkgPCBuZXcgRGF0ZShfdGhpcy5iYWNrZW5kRGF0ZSkuZ2V0VGltZSgpOyAvLyDpmZDliLbpgInmi6nml6XmnJ/kuI3og73otoXov4flvZPliY3ml6XmnJ8KICAgICAgICB9CiAgICAgIH0sCiAgICAgIGN1cnJlbnRDb21wb25lbnQ6ICcnLAogICAgICB0aXRsZTogJycsCiAgICAgIGRXaWR0aDogJzYwJScsCiAgICAgIC8vIGlzU2luZ2xlOiBmYWxzZSwKICAgICAgc2F2ZUxvYWRpbmc6IGZhbHNlLAogICAgICBzZWFyY2g6IGZ1bmN0aW9uIHNlYXJjaCgpIHsKICAgICAgICByZXR1cm4ge307CiAgICAgIH0sCiAgICAgIG9wZW5BZGRMaXN0OiBmYWxzZSwKICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIEJpZ1R5cGU6IDEsCiAgICAgIGlzUHJvZHVjdHdlaWdodDogbnVsbCwKICAgICAgZmlsZUxpc3REYXRhOiBbXSwKICAgICAgZmlsZUxpc3RBcnI6IFtdLAogICAgICBzZWFyY2hOdW06IDEsCiAgICAgIHJvb3RUYWJsZURhdGE6IFtdLAogICAgICB0YWJsZURhdGE6IFtdLAogICAgICB0eXBlTnVtYmVyMTogMCwKICAgICAgdHlwZU51bWJlcjI6IDAsCiAgICAgIHR5cGVOdW1iZXIzOiAwLAogICAgICB0eXBlTnVtYmVyNDogMCwKICAgICAgY3VycmVudFRiQ29tcG9uZW50OiAnJywKICAgICAgYmF0Y2hEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgYmF0Y2hQcm9qZWN0SWQ6ICcnCiAgICB9OwogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGlzQWRkOiBmdW5jdGlvbiBpc0FkZCgpIHsKICAgICAgcmV0dXJuIHRoaXMucGFnZVR5cGUgPT09IDE7CiAgICB9LAogICAgaXNFZGl0OiBmdW5jdGlvbiBpc0VkaXQoKSB7CiAgICAgIHJldHVybiB0aGlzLnBhZ2VUeXBlID09PSAyOwogICAgfSwKICAgIGlzVmlldzogZnVuY3Rpb24gaXNWaWV3KCkgewogICAgICByZXR1cm4gdGhpcy5wYWdlVHlwZSA9PT0gMzsKICAgIH0sCiAgICBpc1B1cmNoYXNlOiBmdW5jdGlvbiBpc1B1cmNoYXNlKCkgewogICAgICByZXR1cm4gdGhpcy5mb3JtLk91dFN0b3JlVHlwZSA9PSAxOyAvLyDpoobnlKjlh7rlupMKICAgIH0sCiAgICBpc1JldHVybjogZnVuY3Rpb24gaXNSZXR1cm4oKSB7CiAgICAgIHJldHVybiB0aGlzLnBhZ2VUeXBlID09PSA4OwogICAgfSwKICAgIGdyaWRDb2RlOiBmdW5jdGlvbiBncmlkQ29kZSgpIHsKICAgICAgcmV0dXJuIHRoaXMuaXNSZXR1cm4gPyAncHJvX2F1eF9tYXRlcmlhbF9vdXRib3VuZF9kZXRhaWxfbGlzdF9yZXR1cm4nIDogJ3Byb19hdXhfbWF0ZXJpYWxfb3V0Ym91bmRfZGV0YWlsX2xpc3QsU3RlZWwnOwogICAgfQogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlKCkgewogICAgICB2YXIgY29sdW1uOwogICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZSQoX2NvbnRleHQpIHsKICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dC5wcmV2ID0gX2NvbnRleHQubmV4dCkgewogICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICBpZiAoIV90aGlzMi5pc1JldHVybikgewogICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSA4OwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICB9CiAgICAgICAgICAgIF90aGlzMi5jdXJyZW50VGJDb21wb25lbnQgPSBSZXR1cm5UYjsKICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDQ7CiAgICAgICAgICAgIHJldHVybiBfdGhpczIuZ2V0VGFibGVDb25maWcoX3RoaXMyLmdyaWRDb2RlKTsKICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgY29sdW1uID0gX2NvbnRleHQuc2VudDsKICAgICAgICAgICAgX3RoaXMyLiRuZXh0VGljayhmdW5jdGlvbiAoXykgewogICAgICAgICAgICAgIF90aGlzMi4kcmVmc1sndGFibGUnXS5pbml0KGNvbHVtbik7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gOTsKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICBjYXNlIDg6CiAgICAgICAgICAgIF90aGlzMi5jdXJyZW50VGJDb21wb25lbnQgPSBQdXJjaGFzZVRiOwogICAgICAgICAgY2FzZSA5OgogICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gMTE7CiAgICAgICAgICAgIHJldHVybiBfdGhpczIuZ2V0Q3VyRmFjdG9yeSgpOwogICAgICAgICAgY2FzZSAxMToKICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDEzOwogICAgICAgICAgICByZXR1cm4gX3RoaXMyLmdldE9NQUxhdGVzdFN0YXRpc3RpY1RpbWUoKTsKICAgICAgICAgIGNhc2UgMTM6CiAgICAgICAgICAgIF90aGlzMi5nZXRQcmVmZXJlbmNlU2V0dGluZ1ZhbHVlKCk7CiAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAxNjsKICAgICAgICAgICAgcmV0dXJuIGdldERpY3Rpb25hcnkoJ0F1eE91dGJvdW5kVHlwZScpOwogICAgICAgICAgY2FzZSAxNjoKICAgICAgICAgICAgX3RoaXMyLkF1eE91dGJvdW5kVHlwZUxpc3QgPSBfY29udGV4dC5zZW50OwogICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gMTk7CiAgICAgICAgICAgIHJldHVybiBfdGhpczIuZ2V0Rmlyc3RMZXZlbERlcGFydHNVbmRlckZhY3RvcnkoKTsKICAgICAgICAgIGNhc2UgMTk6CiAgICAgICAgICAgIF90aGlzMi5zZWFyY2ggPSBkZWJvdW5jZShfdGhpczIuZmV0Y2hEYXRhLCA4MDAsIHRydWUpOwogICAgICAgICAgICBfdGhpczIuZ2V0UHJvamVjdCgpOwogICAgICAgICAgICBfdGhpczIuZ2V0UHJvY2Vzc0xpc3RCYXNlKCk7CiAgICAgICAgICAgIC8vIHRoaXMuZ2V0RmFjdG9yeVBlb3BsZWxpc3QoKQogICAgICAgICAgICBfdGhpczIuZ2V0V29ya2luZ1RlYW1zKCk7CiAgICAgICAgICAgIGlmICghX3RoaXMyLmlzQWRkKSB7CiAgICAgICAgICAgICAgX3RoaXMyLmdldEluZm8oKTsKICAgICAgICAgICAgfQogICAgICAgICAgY2FzZSAyNDoKICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5zdG9wKCk7CiAgICAgICAgfQogICAgICB9LCBfY2FsbGVlKTsKICAgIH0pKSgpOwogIH0sCiAgbWV0aG9kczogewogICAgdXNlckNoYW5nZTogZnVuY3Rpb24gdXNlckNoYW5nZSh2YWwpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIEdldFRlYW1MaXN0QnlVc2VyRm9yTWF0ZXJpZWwoewogICAgICAgIGlkOiB2YWwKICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgdmFyIF9yZXMkRGF0YTsKICAgICAgICBpZiAoKChfcmVzJERhdGEgPSByZXMuRGF0YSkgPT09IG51bGwgfHwgX3JlcyREYXRhID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfcmVzJERhdGEubGVuZ3RoKSA9PT0gMSkgewogICAgICAgICAgX3RoaXMzLiRzZXQoX3RoaXMzLmZvcm0sICdXb3JraW5nVGVhbUlkJywgcmVzLkRhdGFbMF0uSWQpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpczMuJHNldChfdGhpczMuZm9ybSwgJ1dvcmtpbmdUZWFtSWQnLCAnJyk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBjbG9zZUJhdGNoRGlhbG9nOiBmdW5jdGlvbiBjbG9zZUJhdGNoRGlhbG9nKCkgewogICAgICB0aGlzLmJhdGNoUHJvamVjdElkID0gJyc7CiAgICAgIHRoaXMuYmF0Y2hEaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICB9LAogICAgYmF0Y2hDaGFuZ2VQcm9qZWN0OiBmdW5jdGlvbiBiYXRjaENoYW5nZVByb2plY3QoKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB2YXIgdGJEYXRhID0gdGhpcy4kcmVmcy50YWJsZS50YkRhdGE7CiAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24uZm9yRWFjaChmdW5jdGlvbiAoZWxlbWVudCwgaWR4KSB7CiAgICAgICAgdmFyIF90aGlzNCRwcm9qZWN0T3B0aW9uczsKICAgICAgICB2YXIgaXRlbSA9IHRiRGF0YS5maW5kKGZ1bmN0aW9uICh2KSB7CiAgICAgICAgICByZXR1cm4gdi5pbmRleCA9PT0gZWxlbWVudC5pbmRleDsKICAgICAgICB9KTsKICAgICAgICB2YXIgaSA9IHRiRGF0YS5maW5kSW5kZXgoZnVuY3Rpb24gKHYpIHsKICAgICAgICAgIHJldHVybiB2LmluZGV4ID09PSBlbGVtZW50LmluZGV4OwogICAgICAgIH0pOwogICAgICAgIGl0ZW0uUGlja19TeXNfUHJvamVjdF9JZCA9IF90aGlzNC5iYXRjaFByb2plY3RJZDsKICAgICAgICAvLyDlkIzml7bmm7TmlrDpobnnm67lkI3np7AKICAgICAgICBpdGVtLlBpY2tfUHJvamVjdF9OYW1lID0gKF90aGlzNCRwcm9qZWN0T3B0aW9ucyA9IF90aGlzNC5wcm9qZWN0T3B0aW9ucy5maW5kKGZ1bmN0aW9uIChwcm9qZWN0KSB7CiAgICAgICAgICByZXR1cm4gcHJvamVjdC5TeXNfUHJvamVjdF9JZCA9PT0gX3RoaXM0LmJhdGNoUHJvamVjdElkOwogICAgICAgIH0pKSA9PT0gbnVsbCB8fCBfdGhpczQkcHJvamVjdE9wdGlvbnMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF90aGlzNCRwcm9qZWN0T3B0aW9ucy5TaG9ydF9OYW1lOwogICAgICAgIF90aGlzNC4kc2V0KHRiRGF0YSwgaSwgaXRlbSk7CiAgICAgIH0pOwogICAgICB0aGlzLmNsb3NlQmF0Y2hEaWFsb2coKTsKICAgIH0sCiAgICBjaGFuZ2VDb2x1bW46IGZ1bmN0aW9uIGNoYW5nZUNvbHVtbigpIHsKICAgICAgdmFyIF90aGlzNSA9IHRoaXM7CiAgICAgIHZhciB0ZW1wID0gdGhpcy5jdXJyZW50VGJDb21wb25lbnQ7CiAgICAgIHRoaXMuY3VycmVudFRiQ29tcG9uZW50ID0gJyc7CiAgICAgIHRoaXMuJG5leHRUaWNrKC8qI19fUFVSRV9fKi9fYXN5bmNUb0dlbmVyYXRvcigvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yUnVudGltZSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTIoKSB7CiAgICAgICAgdmFyIGNvbHVtbjsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTIkKF9jb250ZXh0MikgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQyLnByZXYgPSBfY29udGV4dDIubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX3RoaXM1LmN1cnJlbnRUYkNvbXBvbmVudCA9IHRlbXA7CiAgICAgICAgICAgICAgaWYgKCFfdGhpczUuaXNSZXR1cm4pIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0Mi5uZXh0ID0gNjsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDQ7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzNS5nZXRUYWJsZUNvbmZpZyhfdGhpczUuZ3JpZENvZGUpOwogICAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgICAgY29sdW1uID0gX2NvbnRleHQyLnNlbnQ7CiAgICAgICAgICAgICAgX3RoaXM1LiRuZXh0VGljayhmdW5jdGlvbiAoXykgewogICAgICAgICAgICAgICAgX3RoaXM1LiRyZWZzWyd0YWJsZSddLmluaXQoY29sdW1uKTsKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgY2FzZSA2OgogICAgICAgICAgICAgIF90aGlzNS4kcmVmc1sndGFibGUnXS5zZXREYXRhKF90aGlzNS50YWJsZURhdGEpOwogICAgICAgICAgICBjYXNlIDc6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Mi5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTIpOwogICAgICB9KSkpOwogICAgfSwKICAgIC8vIOmZhOS7tuS4iuS8oOaIkOWKnwogICAgdXBsb2FkU3VjY2VzczogZnVuY3Rpb24gdXBsb2FkU3VjY2VzcyhyZXNwb25zZSwgZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgdGhpcy5maWxlTGlzdEFyciA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkoZmlsZUxpc3QpKTsKICAgIH0sCiAgICAvLyDnp7vpmaTlt7LkuIrkvKDmlofku7YKICAgIHVwbG9hZFJlbW92ZTogZnVuY3Rpb24gdXBsb2FkUmVtb3ZlKGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIHRoaXMuZmlsZUxpc3RBcnIgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KGZpbGVMaXN0KSk7CiAgICB9LAogICAgLy8g54K55Ye75bey5LiK5Lyg5paH5Lu2CiAgICBoYW5kbGVQcmV2aWV3OiBmdW5jdGlvbiBoYW5kbGVQcmV2aWV3KGZpbGUpIHsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlMygpIHsKICAgICAgICB2YXIgZW5jcnlwdGlvblVybDsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTMkKF9jb250ZXh0MykgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQzLnByZXYgPSBfY29udGV4dDMubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgZW5jcnlwdGlvblVybCA9ICcnOwogICAgICAgICAgICAgIGlmICghKGZpbGUucmVzcG9uc2UgJiYgZmlsZS5yZXNwb25zZS5lbmNyeXB0aW9uVXJsKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQzLm5leHQgPSA1OwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGVuY3J5cHRpb25VcmwgPSBmaWxlLnJlc3BvbnNlLmVuY3J5cHRpb25Vcmw7CiAgICAgICAgICAgICAgX2NvbnRleHQzLm5leHQgPSA5OwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDU6CiAgICAgICAgICAgICAgX2NvbnRleHQzLm5leHQgPSA3OwogICAgICAgICAgICAgIHJldHVybiBHZXRPc3NVcmwoewogICAgICAgICAgICAgICAgdXJsOiBmaWxlLmVuY3J5cHRpb25VcmwKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgY2FzZSA3OgogICAgICAgICAgICAgIGVuY3J5cHRpb25VcmwgPSBfY29udGV4dDMuc2VudDsKICAgICAgICAgICAgICBlbmNyeXB0aW9uVXJsID0gZW5jcnlwdGlvblVybC5EYXRhOwogICAgICAgICAgICBjYXNlIDk6CiAgICAgICAgICAgICAgd2luZG93Lm9wZW4oZW5jcnlwdGlvblVybCk7CiAgICAgICAgICAgIGNhc2UgMTA6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0My5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTMpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvLyDojrflj5bov5DokKXmoLjnrpflt7Lnu5/orqHnmoTmnIDmlrDml6XmnJ8KICAgIGdldE9NQUxhdGVzdFN0YXRpc3RpY1RpbWU6IGZ1bmN0aW9uIGdldE9NQUxhdGVzdFN0YXRpc3RpY1RpbWUoKSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWU0KCkgewogICAgICAgIHZhciByZXM7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWU0JChfY29udGV4dDQpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0NC5wcmV2ID0gX2NvbnRleHQ0Lm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF9jb250ZXh0NC5uZXh0ID0gMjsKICAgICAgICAgICAgICByZXR1cm4gR2V0T01BTGF0ZXN0U3RhdGlzdGljVGltZSh7fSk7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICByZXMgPSBfY29udGV4dDQuc2VudDsKICAgICAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICAgICAgX3RoaXM2LmJhY2tlbmREYXRlID0gcmVzLkRhdGEgfHwgJyc7CiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIF90aGlzNi5tZXNzYWdlLmVycm9yKHJlcy5NZXNhYWdlKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ0LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNCk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8vIOaQnOe0ogogICAgaGFuZGxlU2VhcmNoOiBmdW5jdGlvbiBoYW5kbGVTZWFyY2goKSB7CiAgICAgIHRoaXMudGFibGVEYXRhID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLnJvb3RUYWJsZURhdGEpKTsKICAgICAgaWYgKHRoaXMuc2VhcmNoRm9ybS5SYXdOYW1lKSB7CiAgICAgICAgdmFyIHJhd05hbWVSZWdleCA9IG5ldyBSZWdFeHAodGhpcy5zZWFyY2hGb3JtLlJhd05hbWUsICdpJyk7CiAgICAgICAgdGhpcy50YWJsZURhdGEgPSB0aGlzLnRhYmxlRGF0YS5maWx0ZXIoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgIHJldHVybiByYXdOYW1lUmVnZXgudGVzdChpdGVtLlJhd05hbWUpOwogICAgICAgIH0pOwogICAgICB9CiAgICAgIGlmICh0aGlzLnNlYXJjaEZvcm0uU3BlYykgewogICAgICAgIHZhciBzcGVjUmVnZXggPSBuZXcgUmVnRXhwKHRoaXMuc2VhcmNoRm9ybS5TcGVjLCAnaScpOwogICAgICAgIHRoaXMudGFibGVEYXRhID0gdGhpcy50YWJsZURhdGEuZmlsdGVyKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICByZXR1cm4gc3BlY1JlZ2V4LnRlc3QoaXRlbS5TcGVjKTsKICAgICAgICB9KTsKICAgICAgfQogICAgICBpZiAodGhpcy5zZWFyY2hGb3JtLlN5c1Byb2plY3RJZCkgewogICAgICAgIHZhciBzeXNQcm9qZWN0SWRSZWdleCA9IG5ldyBSZWdFeHAodGhpcy5zZWFyY2hGb3JtLlN5c1Byb2plY3RJZCwgJ2knKTsKICAgICAgICB0aGlzLnRhYmxlRGF0YSA9IHRoaXMudGFibGVEYXRhLmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgcmV0dXJuIHN5c1Byb2plY3RJZFJlZ2V4LnRlc3QoaXRlbS5TeXNfUHJvamVjdF9JZCk7CiAgICAgICAgfSk7CiAgICAgIH0KICAgICAgaWYgKHRoaXMuaXNSZXR1cm4pIHsKICAgICAgICB0aGlzLiRyZWZzWyd0YWJsZSddLnNldERhdGEodGhpcy50YWJsZURhdGEpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJHJlZnNbJ3RhYmxlJ10udGJEYXRhID0gdGhpcy50YWJsZURhdGE7CiAgICAgIH0KICAgIH0sCiAgICAvLyDmm7TmlrDooajmoLzmlbDmja4KICAgIGhhbmRsZVVwZGF0ZVRiOiBmdW5jdGlvbiBoYW5kbGVVcGRhdGVUYigpIHsKICAgICAgdGhpcy5yb290VGFibGVEYXRhID0gdGhpcy4kcmVmc1sndGFibGUnXS50YkRhdGE7CiAgICAgIHRoaXMudGFibGVEYXRhID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLnJvb3RUYWJsZURhdGEpKTsKICAgICAgY29uc29sZS5sb2codGhpcy5yb290VGFibGVEYXRhLCAnMTEnKTsKICAgICAgY29uc29sZS5sb2codGhpcy50YWJsZURhdGEsICcyMicpOwogICAgICAvLyB0aGlzLnNldFRhYkRhdGEoKQogICAgfSwKICAgIC8vIOiOt+WPluWTgemHjeWBj+Wlveiuvue9rgogICAgZ2V0UHJlZmVyZW5jZVNldHRpbmdWYWx1ZTogZnVuY3Rpb24gZ2V0UHJlZmVyZW5jZVNldHRpbmdWYWx1ZSgpIHsKICAgICAgdmFyIF90aGlzNyA9IHRoaXM7CiAgICAgIEdldFByZWZlcmVuY2VTZXR0aW5nVmFsdWUoewogICAgICAgIGNvZGU6ICdQcm9kdWN0d2VpZ2h0JwogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgX3RoaXM3LmlzUHJvZHVjdHdlaWdodCA9IHJlcy5EYXRhOwogICAgICAgICAgLy8gdGhpcy5pc1Byb2R1Y3R3ZWlnaHQgPSAiZmFsc2UiOwogICAgICAgICAgaWYgKF90aGlzNy5pc1Byb2R1Y3R3ZWlnaHQgIT09ICd0cnVlJykgewogICAgICAgICAgICBfdGhpczcuZ2V0RGVwYXJ0bWVudFRyZWUoKTsKICAgICAgICAgIH0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXM3LiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgZ2V0RGVwYXJ0bWVudFRyZWU6IGZ1bmN0aW9uIGdldERlcGFydG1lbnRUcmVlKCkgewogICAgICB2YXIgX3RoaXM4ID0gdGhpczsKICAgICAgR2V0Q29tcGFueURlcGFydFRyZWUoewogICAgICAgIGlzQWxsOiB0cnVlCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICB2YXIgdHJlZSA9IHJlcy5EYXRhOwogICAgICAgICAgX3RoaXM4LnNldERpc2FibGVkVHJlZSh0cmVlKTsKICAgICAgICAgIF90aGlzOC50cmVlUGFyYW1zRGVwYXJ0LmRhdGEgPSB0cmVlOwogICAgICAgICAgX3RoaXM4LiRuZXh0VGljayhmdW5jdGlvbiAoXykgewogICAgICAgICAgICBfdGhpczguJHJlZnMudHJlZVNlbGVjdERlcGFydC50cmVlRGF0YVVwZGF0ZUZ1bih0cmVlKTsKICAgICAgICAgIH0pOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpczguJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBzZXREaXNhYmxlZFRyZWU6IGZ1bmN0aW9uIHNldERpc2FibGVkVHJlZShyb290KSB7CiAgICAgIHZhciBfdGhpczkgPSB0aGlzOwogICAgICBpZiAoIXJvb3QpIHJldHVybjsKICAgICAgcm9vdC5mb3JFYWNoKGZ1bmN0aW9uIChlbGVtZW50KSB7CiAgICAgICAgdmFyIENoaWxkcmVuID0gZWxlbWVudC5DaGlsZHJlbjsKICAgICAgICBpZiAoZWxlbWVudC5EYXRhLklzX0NvbXBhbnkgPT09IHRydWUgfHwgZWxlbWVudC5EYXRhLlR5cGUgPT09ICcxJykgewogICAgICAgICAgZWxlbWVudC5kaXNhYmxlZCA9IHRydWU7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGVsZW1lbnQuZGlzYWJsZWQgPSBmYWxzZTsKICAgICAgICB9CiAgICAgICAgaWYgKENoaWxkcmVuLmxlbmd0aCA+IDApIHsKICAgICAgICAgIF90aGlzOS5zZXREaXNhYmxlZFRyZWUoQ2hpbGRyZW4pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy8g6YeN572u5pCc57SiCiAgICBoYW5kbGVSZXNldDogZnVuY3Rpb24gaGFuZGxlUmVzZXQoKSB7CiAgICAgIHRoaXMuc2VhcmNoTnVtID0gMTsKICAgICAgLy8gdGhpcy4kcmVmc1snc2VhcmNoRm9ybSddLnJlc2V0RmllbGRzKCkKICAgICAgdGhpcy5zZWFyY2hGb3JtLlJhd05hbWUgPSAnJzsKICAgICAgdGhpcy5zZWFyY2hGb3JtLlNwZWMgPSAnJzsKICAgICAgdGhpcy4kcmVmc1sndGFibGUnXS50YkRhdGEgPSB0aGlzLnJvb3RUYWJsZURhdGE7CiAgICAgIHRoaXMudGFibGVEYXRhID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLnJvb3RUYWJsZURhdGEpKTsKICAgIH0sCiAgICAvLyDmlofku7botoXlh7rmlbDph4/pmZDliLYKICAgIGhhbmRsZUV4Y2VlZDogZnVuY3Rpb24gaGFuZGxlRXhjZWVkKCkgewogICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICB0eXBlOiAnd2FybmluZycsCiAgICAgICAgbWVzc2FnZTogJ+mZhOS7tuaVsOmHj+S4jeiDvei2hei/hzXkuKonCiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZVJldHJhY3Q6IGZ1bmN0aW9uIGhhbmRsZVJldHJhY3QoKSB7CiAgICAgIHRoaXMuaXNSZXRyYWN0ID0gIXRoaXMuaXNSZXRyYWN0OwogICAgfSwKICAgIGZldGNoRGF0YTogZnVuY3Rpb24gZmV0Y2hEYXRhKCkge30sCiAgICBnZXRJbmZvOiBmdW5jdGlvbiBnZXRJbmZvKCkgewogICAgICB2YXIgX3RoaXMwID0gdGhpczsKICAgICAgdGhpcy5mb3JtLk91dFN0b3JlTm8gPSB0aGlzLiRyb3V0ZS5xdWVyeS5PdXRTdG9yZU5vOwogICAgICB0aGlzLmZvcm0uT3V0U3RvcmVUeXBlID0gK3RoaXMuJHJvdXRlLnF1ZXJ5Lk91dFN0b3JlVHlwZTsKICAgICAgLy8gY29uc3QgX2Z1biA9IHRoaXMuJHJvdXRlLnBhcmFtcy5PdXRTdG9yZVR5cGUgPT09IDEgPyBQaWNrVXBPdXRTdG9yZURldGFpbCA6IFBhcnR5QU91dFN0b3JlRGV0YWlsCiAgICAgIHZhciBfZnVuOwogICAgICB2YXIgcGFyYW1zOwogICAgICBpZiAodGhpcy5pc1JldHVybikgewogICAgICAgIF9mdW4gPSBHZXRBdXhEZXRhaWxCeVJlY2VpcHQ7CiAgICAgICAgcGFyYW1zID0gewogICAgICAgICAgSWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LmlkCiAgICAgICAgfTsKICAgICAgfSBlbHNlIHsKICAgICAgICBfZnVuID0gR2V0UGlja091dERldGFpbDsKICAgICAgICBwYXJhbXMgPSB7CiAgICAgICAgICBvdXRTdG9yZU5vOiB0aGlzLiRyb3V0ZS5xdWVyeS5PdXRTdG9yZU5vCiAgICAgICAgfTsKICAgICAgfQogICAgICBfZnVuKHBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIHZhciBSZWNlaXB0ID0gcmVzLkRhdGEuUmVjZWlwdDsKICAgICAgICAgIHZhciBTdWIgPSByZXMuRGF0YS5TdWI7CiAgICAgICAgICB2YXIgT3V0U3RvcmVEYXRlID0gUmVjZWlwdC5PdXRTdG9yZURhdGUsCiAgICAgICAgICAgIFJlbWFyayA9IFJlY2VpcHQuUmVtYXJrLAogICAgICAgICAgICBTeXNQcm9qZWN0SWQgPSBSZWNlaXB0LlN5c1Byb2plY3RJZCwKICAgICAgICAgICAgUGlja19EZXBhcnRtZW50X0lkID0gUmVjZWlwdC5QaWNrX0RlcGFydG1lbnRfSWQsCiAgICAgICAgICAgIFVzZV9Qcm9jZXNzaW5nX0lkID0gUmVjZWlwdC5Vc2VfUHJvY2Vzc2luZ19JZCwKICAgICAgICAgICAgV29ya2luZ1RlYW1JZCA9IFJlY2VpcHQuV29ya2luZ1RlYW1JZCwKICAgICAgICAgICAgUmVjZWl2ZVVzZXJJZCA9IFJlY2VpcHQuUmVjZWl2ZVVzZXJJZCwKICAgICAgICAgICAgQXR0YWNobWVudCA9IFJlY2VpcHQuQXR0YWNobWVudCwKICAgICAgICAgICAgU3RhdHVzID0gUmVjZWlwdC5TdGF0dXM7CiAgICAgICAgICBfdGhpczAuZm9ybS5PdXRTdG9yZURhdGUgPSBfdGhpczAuZ2V0RGF0ZShuZXcgRGF0ZShPdXRTdG9yZURhdGUpKTsKICAgICAgICAgIF90aGlzMC5mb3JtLlJlbWFyayA9IFJlbWFyazsKICAgICAgICAgIF90aGlzMC5mb3JtLlN5c1Byb2plY3RJZCA9IFN5c1Byb2plY3RJZDsKICAgICAgICAgIF90aGlzMC5mb3JtLlVzZV9Qcm9jZXNzaW5nX0lkID0gVXNlX1Byb2Nlc3NpbmdfSWQ7CiAgICAgICAgICBfdGhpczAuZm9ybS5QaWNrX0RlcGFydG1lbnRfSWQgPSBQaWNrX0RlcGFydG1lbnRfSWQ7CiAgICAgICAgICBpZiAoX3RoaXMwLmZvcm0uUGlja19EZXBhcnRtZW50X0lkKSB7CiAgICAgICAgICAgIF90aGlzMC5kZXBhcnRtZW50Q2hhbmdlKCk7CiAgICAgICAgICB9CiAgICAgICAgICBfdGhpczAuZm9ybS5Vc2VfUHJvY2Vzc2luZ19JZCA9IFVzZV9Qcm9jZXNzaW5nX0lkOwogICAgICAgICAgX3RoaXMwLmZvcm0uV29ya2luZ1RlYW1JZCA9IFdvcmtpbmdUZWFtSWQ7CiAgICAgICAgICBfdGhpczAuZm9ybS5SZWNlaXZlVXNlcklkID0gUmVjZWl2ZVVzZXJJZDsKICAgICAgICAgIF90aGlzMC5mb3JtLlN0YXR1cyA9IFN0YXR1czsKCiAgICAgICAgICAvLyDlpITnkIbooajmoLzmlbDmja4KICAgICAgICAgIHZhciBTdWJEYXRhID0gU3ViLm1hcChmdW5jdGlvbiAocm93LCBpbmRleCkgewogICAgICAgICAgICByb3cuaW5kZXggPSB1dWlkdjQoKTsKICAgICAgICAgICAgcm93LldhcmVob3VzZV9Mb2NhdGlvbiA9IHJvdy5XYXJlaG91c2VOYW1lID8gcm93LldhcmVob3VzZU5hbWUgKyAnLycgKyByb3cuTG9jYXRpb25OYW1lIDogJyc7CiAgICAgICAgICAgIC8vIOS4tOaXtuWtmOWCqGtnCiAgICAgICAgICAgIC8vIHJvdy5PdXRTdG9yZVdlaWdodEtHID0gcm93Lk91dFN0b3JlV2VpZ2h0CiAgICAgICAgICAgIC8vIHJvdy5Qb3VuZF9XZWlnaHRfS0cgPSByb3cuUG91bmRfV2VpZ2h0CiAgICAgICAgICAgIC8vIHJvdy5PdXRTdG9yZVdlaWdodCA9IE51bWJlcigocm93Lk91dFN0b3JlV2VpZ2h0S0cgLyAxMDAwKS50b0ZpeGVkKDMpKQogICAgICAgICAgICAvLyByb3cuUG91bmRfV2VpZ2h0ID0gTnVtYmVyKChyb3cuUG91bmRfV2VpZ2h0X0tHIC8gMTAwMCkudG9GaXhlZCgzKSkKICAgICAgICAgICAgcmV0dXJuIHJvdzsKICAgICAgICAgIH0pOwogICAgICAgICAgX3RoaXMwLiRuZXh0VGljayhmdW5jdGlvbiAoXykgewogICAgICAgICAgICBfdGhpczAuJHJlZnNbJ3RhYmxlJ10udGJEYXRhID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShTdWJEYXRhKSk7CiAgICAgICAgICAgIF90aGlzMC50YWJsZURhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KFN1YkRhdGEpKTsKICAgICAgICAgICAgX3RoaXMwLnJvb3RUYWJsZURhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KFN1YkRhdGEpKTsKICAgICAgICAgIH0pOwogICAgICAgICAgaWYgKEF0dGFjaG1lbnQpIHsKICAgICAgICAgICAgX3RoaXMwLmZvcm0uQXR0YWNobWVudCA9IEF0dGFjaG1lbnQ7CiAgICAgICAgICAgIHZhciBBdHRhY2htZW50QXJyID0gQXR0YWNobWVudC5zcGxpdCgnLCcpOwogICAgICAgICAgICBBdHRhY2htZW50QXJyLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgICB2YXIgZmlsZVVybCA9IGl0ZW0uaW5kZXhPZignP0V4cGlyZXM9JykgPiAtMSA/IGl0ZW0uc3Vic3RyaW5nKDAsIGl0ZW0ubGFzdEluZGV4T2YoJz9FeHBpcmVzPScpKSA6IGl0ZW07CiAgICAgICAgICAgICAgdmFyIGZpbGVOYW1lID0gZGVjb2RlVVJJKGZpbGVVcmwuc3Vic3RyaW5nKGZpbGVVcmwubGFzdEluZGV4T2YoJy8nKSArIDEpKTsKICAgICAgICAgICAgICB2YXIgQXR0YWNobWVudEpzb24gPSB7fTsKICAgICAgICAgICAgICBBdHRhY2htZW50SnNvbi5uYW1lID0gZmlsZU5hbWU7CiAgICAgICAgICAgICAgQXR0YWNobWVudEpzb24udXJsID0gZmlsZVVybDsKICAgICAgICAgICAgICBBdHRhY2htZW50SnNvbi5lbmNyeXB0aW9uVXJsID0gZmlsZVVybDsKICAgICAgICAgICAgICBfdGhpczAuZmlsZUxpc3REYXRhLnB1c2goQXR0YWNobWVudEpzb24pOwogICAgICAgICAgICAgIF90aGlzMC5maWxlTGlzdEFyci5wdXNoKEF0dGFjaG1lbnRKc29uKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzMC4kbWVzc2FnZSh7CiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGdldFByb2Nlc3NMaXN0QmFzZTogZnVuY3Rpb24gZ2V0UHJvY2Vzc0xpc3RCYXNlKCkgewogICAgICB2YXIgX3RoaXMxID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlNSgpIHsKICAgICAgICB2YXIgcmVzOwogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlNSQoX2NvbnRleHQ1KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDUucHJldiA9IF9jb250ZXh0NS5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfY29udGV4dDUubmV4dCA9IDI7CiAgICAgICAgICAgICAgcmV0dXJuIEdldFByb2Nlc3NMaXN0QmFzZSh7fSk7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICByZXMgPSBfY29udGV4dDUuc2VudDsKICAgICAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICAgICAgX3RoaXMxLlByb2Nlc3NMaXN0ID0gcmVzLkRhdGEgfHwgW107CiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIF90aGlzMS5tZXNzYWdlLmVycm9yKHJlcy5NZXNhYWdlKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ1LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIGRlcGFydG1lbnRDaGFuZ2U6IGZ1bmN0aW9uIGRlcGFydG1lbnRDaGFuZ2UoKSB7CiAgICAgIHRoaXMuZm9ybS5SZWNlaXZlVXNlcklkID0gJyc7CiAgICAgIGlmICh0aGlzLmZvcm0uUGlja19EZXBhcnRtZW50X0lkKSB7CiAgICAgICAgdGhpcy5nZXRVc2VyUGFnZUxpc3QodGhpcy5mb3JtLlBpY2tfRGVwYXJ0bWVudF9JZCk7CiAgICAgIH0KICAgIH0sCiAgICBnZXRVc2VyUGFnZUxpc3Q6IGZ1bmN0aW9uIGdldFVzZXJQYWdlTGlzdChJZCkgewogICAgICB2YXIgX3RoaXMxMCA9IHRoaXM7CiAgICAgIEdldFVzZXJQYWdlKHsKICAgICAgICBQYWdlU2l6ZTogLTEsCiAgICAgICAgRGVwYXJ0bWVudElkOiBJZAogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgX3RoaXMxMC5mYWN0b3J5UGVvcGxlbGlzdCA9IHJlcy5EYXRhLkRhdGE7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzMTAuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBnZXRGaXJzdExldmVsRGVwYXJ0c1VuZGVyRmFjdG9yeTogZnVuY3Rpb24gZ2V0Rmlyc3RMZXZlbERlcGFydHNVbmRlckZhY3RvcnkoKSB7CiAgICAgIHZhciBfdGhpczExID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlNigpIHsKICAgICAgICB2YXIgcmVzOwogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlNiQoX2NvbnRleHQ2KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDYucHJldiA9IF9jb250ZXh0Ni5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfY29udGV4dDYubmV4dCA9IDI7CiAgICAgICAgICAgICAgcmV0dXJuIEdldEZpcnN0TGV2ZWxEZXBhcnRzVW5kZXJGYWN0b3J5KHsKICAgICAgICAgICAgICAgIEZhY3RvcnlJZDogX3RoaXMxMS5GYWN0b3J5RGV0YWlsRGF0YS5JZAogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgICAgcmVzID0gX2NvbnRleHQ2LnNlbnQ7CiAgICAgICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgICAgICAgIF90aGlzMTEuZGVwYXJ0bWVudGxpc3QgPSByZXMuRGF0YSB8fCBbXTsKICAgICAgICAgICAgICAgIF90aGlzMTEuZm9ybS5QaWNrX0RlcGFydG1lbnRfSWQgPSByZXMuRGF0YS5maW5kKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgICAgICAgIHJldHVybiBpdGVtLklzX0N1cl9Vc2VyX0RlcGFydDsKICAgICAgICAgICAgICAgIH0pID8gcmVzLkRhdGEuZmluZChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgICAgICByZXR1cm4gaXRlbS5Jc19DdXJfVXNlcl9EZXBhcnQ7CiAgICAgICAgICAgICAgICB9KS5JZCA6ICcnOwogICAgICAgICAgICAgICAgX3RoaXMxMS5kZXBhcnRtZW50Q2hhbmdlKCk7CiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIF90aGlzMTEubWVzc2FnZS5lcnJvcihyZXMuTWVzYWFnZSk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Ni5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTYpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvKioNCiAgICAgKiDojrflj5bmiYDlsZ7pobnnm64NCiAgICAgKi8KICAgIGdldFByb2plY3Q6IGZ1bmN0aW9uIGdldFByb2plY3QoKSB7CiAgICAgIHZhciBfdGhpczEyID0gdGhpczsKICAgICAgR2V0UHJvamVjdFBhZ2VMaXN0KHsKICAgICAgICBQYWdlOiAxLAogICAgICAgIFBhZ2VTaXplOiAtMQogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgX3RoaXMxMi5wcm9qZWN0T3B0aW9ucyA9IHJlcy5EYXRhLkRhdGE7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzMTIuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKioNCiAgICAgKiDojrflj5bpoobmlpnkurrliJfooagNCiAgICAgKi8KICAgIC8vIGdldEZhY3RvcnlQZW9wbGVsaXN0KCkgewogICAgLy8gICBHZXRGYWN0b3J5UGVvcGxlbGlzdCgpLnRoZW4oKHJlcykgPT4gewogICAgLy8gICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAvLyAgICAgICB0aGlzLmZhY3RvcnlQZW9wbGVsaXN0ID0gcmVzLkRhdGEKICAgIC8vICAgICB9IGVsc2UgewogICAgLy8gICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAvLyAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgLy8gICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAvLyAgICAgICB9KQogICAgLy8gICAgIH0KICAgIC8vICAgfSkKICAgIC8vIH0sCiAgICAvKioNCiAgICAgKiDojrflj5bpoobmlpnnj63nu4TliJfooagNCiAgICAgKi8KICAgIGdldFdvcmtpbmdUZWFtczogZnVuY3Rpb24gZ2V0V29ya2luZ1RlYW1zKCkgewogICAgICB2YXIgX3RoaXMxMyA9IHRoaXM7CiAgICAgIEdldFdvcmtpbmdUZWFtcygpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICBfdGhpczEzLldvcmtpbmdUZWFtTGlzdCA9IHJlcy5EYXRhOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpczEzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgY2hhbmdlV2FyZWhvdXNlOiBmdW5jdGlvbiBjaGFuZ2VXYXJlaG91c2Uocm93KSB7CiAgICAgIHRoaXMuY3VycmVudFJvdyA9IHJvdzsKICAgICAgdGhpcy5oYW5kbGVXYXJlaG91c2UodHJ1ZSk7CiAgICB9LAogICAgaGFuZGxlV2FyZWhvdXNlOiBmdW5jdGlvbiBoYW5kbGVXYXJlaG91c2UoaXNJbmxpbmUpIHsKICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ1dhcmVob3VzZSc7CiAgICAgIHRoaXMuZFdpZHRoID0gJzQwJSc7CiAgICAgIHRoaXMudGl0bGUgPSAn6YCJ5oup5LuT5bqTL+W6k+S9jSc7CiAgICAgICFpc0lubGluZSAmJiAodGhpcy5jdXJyZW50Um93ID0gbnVsbCk7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgZ2V0V2FyZWhvdXNlOiBmdW5jdGlvbiBnZXRXYXJlaG91c2UoX3JlZjIpIHsKICAgICAgdmFyIHdhcmVob3VzZSA9IF9yZWYyLndhcmVob3VzZSwKICAgICAgICBsb2NhdGlvbiA9IF9yZWYyLmxvY2F0aW9uOwogICAgICBpZiAodGhpcy5jdXJyZW50Um93KSB7CiAgICAgICAgdGhpcy5jdXJyZW50Um93LlJldHVybldhcmVob3Vlc2VJZCA9IHdhcmVob3VzZS5JZDsKICAgICAgICB0aGlzLmN1cnJlbnRSb3cuUmV0dXJuTG9jYXRpb25JZCA9IGxvY2F0aW9uLklkOwogICAgICAgIHRoaXMuJHNldCh0aGlzLmN1cnJlbnRSb3csICdSZXR1cm5XYXJlaG91ZXNlTmFtZScsIHdhcmVob3VzZS5EaXNwbGF5X05hbWUpOwogICAgICAgIHRoaXMuJHNldCh0aGlzLmN1cnJlbnRSb3csICdSZXR1cm5Mb2NhdGlvbk5hbWUnLCBsb2NhdGlvbi5EaXNwbGF5X05hbWUpOwogICAgICAgIHRoaXMuJHNldCh0aGlzLmN1cnJlbnRSb3csICdXYXJlaG91c2VfTG9jYXRpb25fUmV0dXJuJywgd2FyZWhvdXNlLkRpc3BsYXlfTmFtZSArICcvJyArIGxvY2F0aW9uLkRpc3BsYXlfTmFtZSk7CiAgICAgIH0KICAgIH0sCiAgICBiYXRjaEVkaXRvckZuOiBmdW5jdGlvbiBiYXRjaEVkaXRvckZuKGRhdGEpIHsKICAgICAgdmFyIF90aGlzMTQgPSB0aGlzOwogICAgICBpZiAodGhpcy5jdXJyZW50Um93KSB7CiAgICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICBfdGhpczE0LiRzZXQoX3RoaXMxNC5jdXJyZW50Um93LCBpdGVtLmtleSwgaXRlbS52YWwpOwogICAgICAgIH0pOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24uZm9yRWFjaChmdW5jdGlvbiAoZWxlbWVudCwgaWR4KSB7CiAgICAgICAgICBkYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgX3RoaXMxNC4kc2V0KGVsZW1lbnQsIGl0ZW0ua2V5LCBpdGVtLnZhbCk7CiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgfQogICAgICB0aGlzLmhhbmRsZUNsb3NlKCk7CiAgICB9LAogICAgY2hhbmdlU3RhbmRhcmQ6IGZ1bmN0aW9uIGNoYW5nZVN0YW5kYXJkKHJvdykgewogICAgICB2YXIgX3RoaXMxNSA9IHRoaXM7CiAgICAgIHRoaXMuY3VycmVudFJvdyA9IHJvdzsKICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ1N0YW5kYXJkJzsKICAgICAgdGhpcy5kV2lkdGggPSAnNDAlJzsKICAgICAgdGhpcy50aXRsZSA9ICfpgInmi6nop4TmoLwnOwogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoXykgewogICAgICAgIF90aGlzMTUuJHJlZnNbJ2NvbnRlbnQnXS5nZXRPcHRpb24ocm93KTsKICAgICAgfSk7CiAgICB9LAogICAgZ2V0U3RhbmRhcmQ6IGZ1bmN0aW9uIGdldFN0YW5kYXJkKF9yZWYzKSB7CiAgICAgIHZhciB0eXBlID0gX3JlZjMudHlwZSwKICAgICAgICB2YWwgPSBfcmVmMy52YWw7CiAgICAgIGlmICh0eXBlID09PSAxKSB7CiAgICAgICAgdGhpcy4kc2V0KHRoaXMuY3VycmVudFJvdywgJ1N0YW5kYXJkRGVzYycsIHZhbCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kc2V0KHRoaXMuY3VycmVudFJvdywgJ1N0YW5kYXJkRGVzYycsIHZhbC5TdGFuZGFyZERlc2MpOwogICAgICAgIHRoaXMuY3VycmVudFJvdy5TdGFuZGFyZElkID0gdmFsLlN0YW5kYXJkSWQ7CiAgICAgIH0KICAgIH0sCiAgICAvLyDlh7rlupPnsbvlnovliIfmjaIs6KGo5qC85pWw5o2u5riF56m6CiAgICB0eXBlQ2hhbmdlOiBmdW5jdGlvbiB0eXBlQ2hhbmdlKG4pIHsKICAgICAgaWYgKG4gIT09IDApIHsKICAgICAgICB0aGlzLkJpZ1R5cGUgPSAxOwogICAgICAgIHRoaXMudHlwZU51bWJlcjEgPSAwOwogICAgICAgIHRoaXMudHlwZU51bWJlcjIgPSAwOwogICAgICAgIHRoaXMudHlwZU51bWJlcjMgPSAwOwogICAgICAgIHRoaXMudHlwZU51bWJlcjQgPSAwOwogICAgICAgIHRoaXMudGFibGVEYXRhID0gW107CiAgICAgICAgdGhpcy4kcmVmc1sndGFibGUnXS50YkRhdGEgPSBbXTsKICAgICAgfQogICAgfSwKICAgIC8vIGhhbmRsZVJlc2V0KCkgewogICAgLy8gICB0aGlzLiRyZWZzWyJmb3JtIl0ucmVzZXRGaWVsZHMoKTsKICAgIC8vICAgdGhpcy5zZWFyY2goMSk7CiAgICAvLyB9LAogICAgLy8g5paw5aKe54mp5paZCiAgICBnZXRBZGRMaXN0OiBmdW5jdGlvbiBnZXRBZGRMaXN0KGxpc3QsIGluZm8pIHsKICAgICAgdGhpcy5CaWdUeXBlID0gbGlzdFswXS5CaWdUeXBlOwogICAgICBsaXN0Lm1hcChmdW5jdGlvbiAoaXRlbSwgaW5kZXgpIHsKICAgICAgICBpdGVtLmluZGV4ID0gdXVpZHY0KCk7CiAgICAgICAgaXRlbS5QaWNrX1Byb2plY3RfTmFtZSA9IGl0ZW0uUHJvamVjdF9OYW1lOyAvLyDpu5jorqTpoobnlKjpobnnm67kuLrmiYDlsZ7pobnnm64KICAgICAgfSk7CiAgICAgIHRoaXMuJHJlZnNbJ3RhYmxlJ10uYWRkRGF0YShsaXN0KTsKICAgICAgdmFyIHRiRGF0YSA9IHRoaXMuJHJlZnMudGFibGUudGJEYXRhOwogICAgICB0aGlzLnRhYmxlRGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGJEYXRhKSk7CiAgICAgIHRoaXMucm9vdFRhYmxlRGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGJEYXRhKSk7CiAgICAgIGlmIChpbmZvKSB7CiAgICAgICAgdGhpcy5mb3JtLldvcmtpbmdUZWFtSWQgPSBpbmZvLlBpY2tfVGVhbV9JZDsKICAgICAgICB0aGlzLmZvcm0uVXNlX1Byb2Nlc3NpbmdfSWQgPSBpbmZvLlBpY2tfUHJvY2Vzc19JZDsKICAgICAgfQogICAgfSwKICAgIGltcG9ydERhdGE6IGZ1bmN0aW9uIGltcG9ydERhdGEobGlzdCkgewogICAgICB0aGlzLiRyZWZzWyd0YWJsZSddLmltcG9ydERhdGEobGlzdCk7CiAgICB9LAogICAgZ2V0Um93TmFtZTogZnVuY3Rpb24gZ2V0Um93TmFtZShfcmVmNCkgewogICAgICB2YXIgTmFtZSA9IF9yZWY0Lk5hbWUsCiAgICAgICAgSWQgPSBfcmVmNC5JZDsKICAgICAgdGhpcy5jdXJyZW50Um93Lk5hbWUgPSBOYW1lOwogICAgICB0aGlzLmN1cnJlbnRSb3cuUmF3SWQgPSBJZDsKICAgICAgdGhpcy5jdXJyZW50Um93LlN0YW5kYXJkRGVzYyA9ICcnOwogICAgfSwKICAgIGdlbmVyYXRlQ29tcG9uZW50OiBmdW5jdGlvbiBnZW5lcmF0ZUNvbXBvbmVudCh0aXRsZSwgY29tcG9uZW50KSB7CiAgICAgIHRoaXMudGl0bGUgPSB0aXRsZTsKICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gY29tcG9uZW50OwogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgfSwKICAgIGhhbmRsZURlbGV0ZTogZnVuY3Rpb24gaGFuZGxlRGVsZXRlKCkgewogICAgICB2YXIgX3RoaXMkJHJlZnMkdGFibGU7CiAgICAgIHZhciB0YkRhdGEgPSB0aGlzLiRyZWZzLnRhYmxlLnRiRGF0YTsKICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5mb3JFYWNoKGZ1bmN0aW9uIChlbGVtZW50LCBpZHgpIHsKICAgICAgICB2YXIgaSA9IHRiRGF0YS5maW5kSW5kZXgoZnVuY3Rpb24gKHYpIHsKICAgICAgICAgIHJldHVybiB2LmluZGV4ID09PSBlbGVtZW50LmluZGV4OwogICAgICAgIH0pOwogICAgICAgIHRiRGF0YS5zcGxpY2UoaSwgMSk7CiAgICAgIH0pOwogICAgICB0aGlzLnRhYmxlRGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGJEYXRhKSk7CiAgICAgIHRoaXMucm9vdFRhYmxlRGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGJEYXRhKSk7CiAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24gPSBbXTsKICAgICAgKF90aGlzJCRyZWZzJHRhYmxlID0gdGhpcy4kcmVmcy50YWJsZSkgPT09IG51bGwgfHwgX3RoaXMkJHJlZnMkdGFibGUgPT09IHZvaWQgMCB8fCAoX3RoaXMkJHJlZnMkdGFibGUgPSBfdGhpcyQkcmVmcyR0YWJsZS4kcmVmcykgPT09IG51bGwgfHwgX3RoaXMkJHJlZnMkdGFibGUgPT09IHZvaWQgMCB8fCBfdGhpcyQkcmVmcyR0YWJsZS54VGFibGUuY2xlYXJDaGVja2JveFJvdygpOwogICAgfSwKICAgIGhhbmRsZUNsb3NlOiBmdW5jdGlvbiBoYW5kbGVDbG9zZShyb3cpIHsKICAgICAgdGhpcy5vcGVuQWRkTGlzdCA9IGZhbHNlOwogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgIH0sCiAgICB0YlNlbGVjdENoYW5nZTogZnVuY3Rpb24gdGJTZWxlY3RDaGFuZ2UoYXJyYXkpIHsKICAgICAgLy8gY29uc29sZS5sb2coYXJyYXksICdhcnI9PT09JykKICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbiA9IGFycmF5LnJlY29yZHM7CiAgICB9LAogICAgaGFuZGxlRGV0YWlsOiBmdW5jdGlvbiBoYW5kbGVEZXRhaWwocm93KSB7fSwKICAgIC8vIOihqOagvOaVsOaNruagoemqjAogICAgY2hlY2tWYWxpZGF0ZTogZnVuY3Rpb24gY2hlY2tWYWxpZGF0ZSgpIHsKICAgICAgdmFyIHN1Ym1pdCA9IGRlZXBDbG9uZSh0aGlzLiRyZWZzWyd0YWJsZSddLnRiRGF0YSk7CiAgICAgIGlmICghc3VibWl0Lmxlbmd0aCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgbWVzc2FnZTogJ+aVsOaNruS4jeiDveS4uuepuicsCiAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICB9KTsKICAgICAgICByZXR1cm4gewogICAgICAgICAgc3RhdHVzOiBmYWxzZQogICAgICAgIH07CiAgICAgIH0KICAgICAgdmFyIF90aGlzJGNoZWNrVGIgPSB0aGlzLmNoZWNrVGIoc3VibWl0KSwKICAgICAgICBzdGF0dXMgPSBfdGhpcyRjaGVja1RiLnN0YXR1cywKICAgICAgICBtc2cgPSBfdGhpcyRjaGVja1RiLm1zZzsKICAgICAgaWYgKCFzdGF0dXMpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIG1lc3NhZ2U6ICIiLmNvbmNhdChtc2cgfHwgJ+W/heWhq+Wtl+autScsICJcdTRFMERcdTgwRkRcdTRFM0FcdTdBN0EiKSwKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgIH0pOwogICAgICAgIHJldHVybiB7CiAgICAgICAgICBzdGF0dXM6IGZhbHNlCiAgICAgICAgfTsKICAgICAgfQogICAgICByZXR1cm4gewogICAgICAgIGRhdGE6IHN1Ym1pdCwKICAgICAgICBzdGF0dXM6IHRydWUKICAgICAgfTsKICAgIH0sCiAgICBoYW5kbGVSZXR1cm46IGZ1bmN0aW9uIGhhbmRsZVJldHVybigpIHsKICAgICAgdmFyIF90aGlzMTYgPSB0aGlzOwogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbmj5DkuqTpgIDlupPkv6Hmga8/JywgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMTYuc2F2ZURyYWZ0KDMpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZSkgewogICAgICAgIGNvbnNvbGUubG9nKCdlcnJvcicsIGUpOwogICAgICAgIF90aGlzMTYuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ2luZm8nLAogICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iCcKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5o+Q5Lqk5YWl5bqTCiAgICBoYW5kbGVTdWJtaXQ6IGZ1bmN0aW9uIGhhbmRsZVN1Ym1pdChyb3cpIHsKICAgICAgdmFyIF90aGlzMTcgPSB0aGlzOwogICAgICB0aGlzLiRjb25maXJtKCfnoa7orqTmj5DkuqTlh7rlupPljZU/JywgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMTcuc2F2ZURyYWZ0KDMpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZSkgewogICAgICAgIF90aGlzMTcuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ2luZm8nLAogICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iCcKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5L+d5a2Y5Ye65bqT5Y2VCiAgICBzYXZlRHJhZnQ6IGZ1bmN0aW9uIHNhdmVEcmFmdCgpIHsKICAgICAgdmFyIF90aGlzMTggPSB0aGlzOwogICAgICB2YXIgdHlwZSA9IGFyZ3VtZW50cy5sZW5ndGggPiAwICYmIGFyZ3VtZW50c1swXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzBdIDogMTsKICAgICAgdmFyIF90aGlzJGNoZWNrVmFsaWRhdGUgPSB0aGlzLmNoZWNrVmFsaWRhdGUoKSwKICAgICAgICBkYXRhID0gX3RoaXMkY2hlY2tWYWxpZGF0ZS5kYXRhLAogICAgICAgIHN0YXR1cyA9IF90aGlzJGNoZWNrVmFsaWRhdGUuc3RhdHVzOwogICAgICBpZiAoIXN0YXR1cykgcmV0dXJuOwogICAgICB0aGlzLiRyZWZzWydmb3JtJ10udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKCF2YWxpZCkgcmV0dXJuIGZhbHNlOwogICAgICAgIF90aGlzMTguc2F2ZUxvYWRpbmcgPSB0cnVlOwogICAgICAgIC8vIHRoaXMuZm9ybS5BdHRhY2htZW50ID0gdGhpcy5maWxlTGlzdEFyci5qb2luKCcsJykKICAgICAgICB2YXIgZm9ybUF0dGFjaG1lbnQgPSBbXTsKICAgICAgICBpZiAoX3RoaXMxOC5maWxlTGlzdEFyci5sZW5ndGggPiAwKSB7CiAgICAgICAgICBfdGhpczE4LmZpbGVMaXN0QXJyLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgZm9ybUF0dGFjaG1lbnQucHVzaChpdGVtLnJlc3BvbnNlICYmIGl0ZW0ucmVzcG9uc2UuZW5jcnlwdGlvblVybCA/IGl0ZW0ucmVzcG9uc2UuZW5jcnlwdGlvblVybCA6IGl0ZW0uZW5jcnlwdGlvblVybCk7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgICAgX3RoaXMxOC5mb3JtLkF0dGFjaG1lbnQgPSBmb3JtQXR0YWNobWVudC5qb2luKCcsJyk7CiAgICAgICAgLy8gMeiNieeovyAy5a6h5qC45LitIDPpgJrov4cgNOmAgOWbngogICAgICAgIF90aGlzMTguZm9ybS5TdGF0dXMgPSB0eXBlID09IDEgPyAxIDogMzsKICAgICAgICB2YXIgZm9ybSA9IF9vYmplY3RTcHJlYWQoe30sIF90aGlzMTguZm9ybSk7CiAgICAgICAgLy8gY29uc29sZS5sb2coZm9ybSwgJ3RoaXMuZm9ybT09PTExMTEnKQogICAgICAgIC8vIGNvbnNvbGUubG9nKGRhdGEsICdTdWI9PT0xMTExJykKICAgICAgICB2YXIgX2Z1bjsKICAgICAgICBpZiAoX3RoaXMxOC5pc1JldHVybikgewogICAgICAgICAgX2Z1biA9IEF1eFJldHVybkJ5UmVjZWlwdDsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgX2Z1biA9IFBpY2tPdXRTdG9yZTsKICAgICAgICB9CiAgICAgICAgX2Z1bih7CiAgICAgICAgICBSZWNlaXB0OiBmb3JtLAogICAgICAgICAgU3ViOiBkYXRhCiAgICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICBfdGhpczE4LiRtZXNzYWdlKHsKICAgICAgICAgICAgICBtZXNzYWdlOiAn5L+d5a2Y5oiQ5YqfJywKICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIF90aGlzMTguY2xvc2VWaWV3KCk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBfdGhpczE4LiRtZXNzYWdlKHsKICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgICAgX3RoaXMxOC5zYXZlTG9hZGluZyA9IGZhbHNlOwogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICBjaGVja1RiOiBmdW5jdGlvbiBjaGVja1RiKGxpc3QpIHsKICAgICAgdmFyIF90aGlzMTkgPSB0aGlzOwogICAgICB2YXIgY2hlY2sgPSBudWxsOwogICAgICB2YXIgaXNFbXB0eSA9IFsnJywgbnVsbCwgdW5kZWZpbmVkXTsKICAgICAgaWYgKHRoaXMuaXNSZXR1cm4pIHsKICAgICAgICBpc0VtcHR5LnB1c2goMCk7CiAgICAgICAgY2hlY2sgPSBbJ1JldHVybkNvdW50JywgJ1dhcmVob3VzZV9Mb2NhdGlvbl9SZXR1cm4nXTsKICAgICAgfSBlbHNlIHsKICAgICAgICBjaGVjayA9IFsnT3V0U3RvcmVDb3VudCddOwogICAgICB9CiAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgbGlzdC5sZW5ndGg7IGkrKykgewogICAgICAgIHZhciBpdGVtID0gbGlzdFtpXTsKICAgICAgICB2YXIgX2xvb3AgPSBmdW5jdGlvbiBfbG9vcCgpIHsKICAgICAgICAgICAgdmFyIGMgPSBjaGVja1tqXTsKICAgICAgICAgICAgaWYgKGlzRW1wdHkuaW5jbHVkZXMoaXRlbVtjXSkpIHsKICAgICAgICAgICAgICB2YXIgY2xvdW1ucyA9IF90aGlzMTkuJHJlZnMudGFibGUucm9vdENvbHVtbnM7CiAgICAgICAgICAgICAgdmFyIGVsZW1lbnQgPSBjbG91bW5zLmZpbmQoZnVuY3Rpb24gKHYpIHsKICAgICAgICAgICAgICAgIHJldHVybiB2LkNvZGUgPT09IGM7CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgICAgIHY6IHsKICAgICAgICAgICAgICAgICAgc3RhdHVzOiBmYWxzZSwKICAgICAgICAgICAgICAgICAgbXNnOiBlbGVtZW50ID09PSBudWxsIHx8IGVsZW1lbnQgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGVsZW1lbnQuRGlzcGxheV9OYW1lCiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgfQogICAgICAgICAgfSwKICAgICAgICAgIF9yZXQ7CiAgICAgICAgZm9yICh2YXIgaiA9IDA7IGogPCBjaGVjay5sZW5ndGg7IGorKykgewogICAgICAgICAgX3JldCA9IF9sb29wKCk7CiAgICAgICAgICBpZiAoX3JldCkgcmV0dXJuIF9yZXQudjsKICAgICAgICB9CiAgICAgICAgZGVsZXRlIGl0ZW0uX1hfUk9XX0tFWTsKICAgICAgICBkZWxldGUgaXRlbS5XYXJlaG91c2VOYW1lOwogICAgICAgIGRlbGV0ZSBpdGVtLkxvY2F0aW9uTmFtZTsKICAgICAgfQogICAgICByZXR1cm4gewogICAgICAgIHN0YXR1czogdHJ1ZSwKICAgICAgICBtc2c6ICcnCiAgICAgIH07CiAgICB9LAogICAgb3BlbkFkZERpYWxvZzogZnVuY3Rpb24gb3BlbkFkZERpYWxvZyhyb3cpIHsKICAgICAgdGhpcy5vcGVuQWRkTGlzdCA9IHRydWU7CiAgICB9LAogICAgLy8gb3BlbkFkZERpYWxvZyhyb3cpIHsKICAgIC8vICAgIGlmICh0aGlzLmZvcm0uU3lzUHJvamVjdElkKSB7CiAgICAvLyAgICAgdGhpcy5vcGVuQWRkTGlzdCA9IHRydWUKICAgIC8vICAgfSBlbHNlIHsKICAgIC8vICAgICB0aGlzLiRtZXNzYWdlKHsKICAgIC8vICAgICAgIG1lc3NhZ2U6ICfor7flhYjpgInmi6npobnnm64nLAogICAgLy8gICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAvLyAgICAgfSkKICAgIC8vICAgICByZXR1cm4KICAgIC8vICAgfQogICAgLy8gfSwKICAgIGNsb3NlVmlldzogZnVuY3Rpb24gY2xvc2VWaWV3KCkgewogICAgICBjbG9zZVRhZ1ZpZXcodGhpcy4kc3RvcmUsIHRoaXMuJHJvdXRlKTsKICAgIH0sCiAgICBzZXRTZWxlY3RSb3c6IGZ1bmN0aW9uIHNldFNlbGVjdFJvdyhtdWx0aXBsZVNlbGVjdGlvbikgewogICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gbXVsdGlwbGVTZWxlY3Rpb247CiAgICB9LAogICAgLy8g5pel5pyf5qC85byP5YyWCiAgICBnZXREYXRlOiBmdW5jdGlvbiBnZXREYXRlKGRhdGEpIHsKICAgICAgdmFyIGRhdGUgPSBkYXRhIHx8IG5ldyBEYXRlKCk7CiAgICAgIHZhciB5ZWFyID0gZGF0ZS5nZXRGdWxsWWVhcigpOwogICAgICB2YXIgbW9udGggPSAoJzAnICsgKGRhdGUuZ2V0TW9udGgoKSArIDEpKS5zbGljZSgtMik7CiAgICAgIHZhciBkYXkgPSAoJzAnICsgZGF0ZS5nZXREYXRlKCkpLnNsaWNlKC0yKTsKICAgICAgcmV0dXJuICIiLmNvbmNhdCh5ZWFyLCAiLSIpLmNvbmNhdChtb250aCwgIi0iKS5jb25jYXQoZGF5KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["closeTagView", "debounce", "deepClone", "PurchaseTb", "AddList", "AddPurchaseList", "ImportFile", "Standard", "GetProjectPageList", "OSSUpload", "getCommonData", "GetFirstLevelDepartsUnderFactory", "AuxReturnByReceipt", "GetAuxDetailByReceipt", "GetTeamListByUserForMateriel", "GetUserPage", "GetProcessListBase", "getDictionary", "GetPreferenceSettingValue", "PickOutStore", "GetPickOutDetail", "GetOMALatestStatisticTime", "GetOssUrl", "GetCompanyDepartTree", "ReturnTb", "getTableConfig", "Warehouse", "v4", "uuidv4", "SelectMaterialStoreType", "DynamicTableFields", "GetWorkingTeams", "SelectDepartment", "SelectDepartmentUser", "PickSelect", "components", "mixins", "props", "pageType", "type", "Number", "default", "undefined", "data", "_this", "isRetract", "returning", "tbLoading", "projectOptions", "multipleSelection", "factoryPeoplelist", "departmentlist", "ProcessList", "AuxOutboundTypeList", "WorkingTeamList", "searchForm", "RawName", "Spec", "form", "OutStoreNo", "OutStoreType", "OutStoreDate", "getDate", "Use_Processing_Id", "Pick_Department_Id", "SysProjectId", "Pick_Project_Name", "ReceiveUserId", "WorkingTeamId", "Remark", "Attachment", "rules", "required", "message", "trigger", "treeParamsDepart", "filterable", "clickParent", "disabled", "children", "label", "value", "backendDate", "pickerOptions", "disabledDate", "time", "getTime", "Date", "currentComponent", "title", "dWidth", "saveLoading", "search", "openAddList", "dialogVisible", "BigType", "isProductweight", "fileListData", "fileListArr", "searchNum", "rootTableData", "tableData", "typeNumber1", "typeNumber2", "typeNumber3", "typeNumber4", "currentTbComponent", "batchDialogVisible", "batchProjectId", "computed", "isAdd", "isEdit", "<PERSON><PERSON><PERSON><PERSON>", "isPurchase", "isReturn", "gridCode", "mounted", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "column", "wrap", "_callee$", "_context", "prev", "next", "sent", "$nextTick", "_", "$refs", "init", "getCurFactory", "getOMALatestStatisticTime", "getPreferenceSettingValue", "getFirstLevelDepartsUnderFactory", "fetchData", "getProject", "getProcessListBase", "getWorkingTeams", "getInfo", "stop", "methods", "userChange", "val", "_this3", "id", "then", "res", "_res$Data", "Data", "length", "$set", "Id", "closeBatchDialog", "batchChangeProject", "_this4", "tbData", "table", "for<PERSON>ach", "element", "idx", "_this4$projectOptions", "item", "find", "v", "index", "i", "findIndex", "Pick_Sys_Project_Id", "project", "Sys_Project_Id", "Short_Name", "changeColumn", "_this5", "temp", "_callee2", "_callee2$", "_context2", "setData", "uploadSuccess", "response", "file", "fileList", "JSON", "parse", "stringify", "uploadRemove", "handlePreview", "_callee3", "encryptionUrl", "_callee3$", "_context3", "url", "window", "open", "_this6", "_callee4", "_callee4$", "_context4", "IsSucceed", "error", "Mesaage", "handleSearch", "rawNameRegex", "RegExp", "filter", "test", "specRegex", "sysProjectIdRegex", "handleUpdateTb", "console", "log", "_this7", "code", "getDepartmentTree", "$message", "Message", "_this8", "isAll", "tree", "setDisabledTree", "treeSelectDepart", "treeDataUpdateFun", "root", "_this9", "Children", "Is_Company", "Type", "handleReset", "handleExceed", "handleRetract", "_this0", "$route", "query", "_fun", "params", "outStoreNo", "Receipt", "Sub", "Status", "departmentChange", "SubData", "map", "row", "Warehouse_Location", "WarehouseName", "LocationName", "AttachmentArr", "split", "fileUrl", "indexOf", "substring", "lastIndexOf", "fileName", "decodeURI", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "push", "_this1", "_callee5", "_callee5$", "_context5", "getUserPageList", "_this10", "PageSize", "DepartmentId", "_this11", "_callee6", "_callee6$", "_context6", "FactoryId", "FactoryDetailData", "Is_<PERSON>ur_User_Depart", "_this12", "Page", "_this13", "changeWarehouse", "currentRow", "handleWarehouse", "isInline", "getWarehouse", "_ref2", "warehouse", "location", "ReturnWarehoueseId", "ReturnLocationId", "Display_Name", "batchEditorFn", "_this14", "key", "handleClose", "changeStandard", "_this15", "getOption", "getStandard", "_ref3", "StandardDesc", "StandardId", "typeChange", "n", "getAddList", "list", "info", "Project_Name", "addData", "Pick_Team_Id", "Pick_Process_Id", "importData", "getRowName", "_ref4", "Name", "RawId", "generateComponent", "component", "handleDelete", "_this$$refs$table", "splice", "xTable", "clearCheckboxRow", "tbSelectChange", "array", "records", "handleDetail", "checkValidate", "submit", "status", "_this$checkTb", "checkTb", "msg", "concat", "handleReturn", "_this16", "$confirm", "confirmButtonText", "cancelButtonText", "saveDraft", "catch", "e", "handleSubmit", "_this17", "_this18", "arguments", "_this$checkValidate", "validate", "valid", "formAttachment", "join", "_objectSpread", "<PERSON><PERSON><PERSON><PERSON>", "_this19", "check", "isEmpty", "_loop", "c", "j", "includes", "cloumns", "rootColumns", "Code", "_ret", "_X_ROW_KEY", "openAddDialog", "$store", "setSelectRow", "date", "year", "getFullYear", "month", "getMonth", "slice", "day"], "sources": ["src/views/PRO/material-inventory-reconfig/aux-outbound/info.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <el-card\r\n      class=\"box-card\"\r\n      :style=\"isRetract ? 'height: 110px; overflow: hidden;' : ''\"\r\n    >\r\n      <!-- <h3 style=\"margin-bottom: 20px\">出库单信息</h3> -->\r\n      <div\r\n        class=\"toolbar-container\"\r\n        style=\"\r\n          margin-bottom: 10px;\r\n          padding-bottom: 10;\r\n          border-bottom: 1px solid #d0d3db;\r\n        \"\r\n      >\r\n        <div class=\"toolbar-title\"><span />出库单信息</div>\r\n        <div class=\"retract-container\" @click=\"handleRetract\">\r\n          <el-button type=\"text\">{{ isRetract ? \"展开\" : \"收起\" }}</el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            :icon=\"isRetract ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"出库类型\" prop=\"OutStoreType\">\r\n              <SelectMaterialStoreType v-model=\"form.OutStoreType\" type=\"RawOutStoreType\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"出库日期\" prop=\"OutStoreDate\">\r\n              <el-date-picker\r\n                v-model=\"form.OutStoreDate\"\r\n                :disabled=\"isView|| isReturn\"\r\n                :picker-options=\"pickerOptions\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                type=\"date\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"领料部门\" prop=\"Pick_Department_Id\">\r\n              <el-select\r\n                v-if=\"isProductweight === 'true'\"\r\n                v-model=\"form.Pick_Department_Id\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请选择\"\r\n                clearable\r\n                filterable\r\n                :disabled=\"isView|| isReturn\"\r\n                @change=\"departmentChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in departmentlist\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n              <el-tree-select\r\n                v-else\r\n                ref=\"treeSelectDepart\"\r\n                v-model=\"form.Pick_Department_Id\"\r\n                :select-params=\"{\r\n                  clearable: true,\r\n                  disabled: isReturn\r\n                }\"\r\n                class=\"cs-tree-x\"\r\n                :tree-params=\"treeParamsDepart\"\r\n                @select-clear=\"departmentChange\"\r\n                @node-click=\"departmentChange\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"领料人\" prop=\"ReceiveUserId\">\r\n              <el-select\r\n                v-model=\"form.ReceiveUserId\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请选择\"\r\n                clearable\r\n                filterable\r\n                :disabled=\"isView || !form.Pick_Department_Id|| isReturn\"\r\n                @change=\"userChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in factoryPeoplelist\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col v-if=\"isPurchase\" :span=\"6\">\r\n            <el-form-item label=\"领料班组\" prop=\"WorkingTeamId\">\r\n              <el-select\r\n                v-model=\"form.WorkingTeamId\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请选择\"\r\n                clearable\r\n                filterable\r\n                :disabled=\"isView\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in WorkingTeamList\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col v-if=\"isPurchase\" :span=\"6\">\r\n            <el-form-item label=\"使用工序\" prop=\"Use_Processing_Id\">\r\n              <el-select\r\n                v-model=\"form.Use_Processing_Id\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请选择\"\r\n                clearable\r\n                filterable\r\n                :disabled=\"isView|| isReturn\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in ProcessList\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <!-- <el-col :span=\"6\">\r\n            <el-form-item label=\"领用部门\" prop=\"Pick_Department_Id\">\r\n              <el-select\r\n                v-model=\"form.Pick_Department_Id\"\r\n                filterable\r\n                style=\"width: 100%\"\r\n                clearable\r\n                placeholder=\"请选择\"\r\n                :disabled=\"isView\"\r\n                @change=\"departmentChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in departmentlist\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col> -->\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"备注\" prop=\"Remark\">\r\n              <el-input\r\n                v-model=\"form.Remark\"\r\n                :disabled=\"isView|| isReturn\"\r\n                style=\"width: 100%\"\r\n                show-word-limit\r\n                :rows=\"1\"\r\n                :maxlength=\"100\"\r\n                type=\"textarea\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col v-if=\"!isReturn\" :span=\"6\">\r\n            <el-form-item label=\"附件\" class=\"factory-img\">\r\n              <OSSUpload\r\n                class=\"upload-demo\"\r\n                action=\"alioss\"\r\n                :limit=\"5\"\r\n                :multiple=\"true\"\r\n                :on-success=\"\r\n                  (response, file, fileList) => {\r\n                    uploadSuccess(response, file, fileList);\r\n                  }\r\n                \"\r\n                :on-remove=\"uploadRemove\"\r\n                :on-preview=\"handlePreview\"\r\n                :on-exceed=\"handleExceed\"\r\n                :show-file-list=\"true\"\r\n                :file-list=\"fileListData\"\r\n                :disabled=\"isView|| isReturn\"\r\n              >\r\n                <el-button\r\n                  v-if=\"!(isView|| isReturn)\"\r\n                  type=\"primary\"\r\n                  :disabled=\"isView|| isReturn\"\r\n                >上传文件</el-button>\r\n              </OSSUpload>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col v-if=\"isReturn\" :span=\"6\">\r\n            <el-form-item label=\"退库部门\" prop=\"Return_Dept_Id\">\r\n              <SelectDepartment v-model=\"form.Return_Dept_Id\" :disabled=\"isView\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col v-if=\"isReturn\" :span=\"6\">\r\n            <el-form-item label=\"退库人\" prop=\"Return_Person_Id \">\r\n              <SelectDepartmentUser v-model=\"form.Return_Person_Id\" :department-id=\"form.Return_Dept_Id\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n      </el-form>\r\n    </el-card>\r\n    <el-card class=\"box-card box-card-tb\">\r\n      <!-- <el-divider class=\"elDivder\" /> -->\r\n      <!-- <h4>出库单明细</h4> -->\r\n      <div class=\"toolbar-container\">\r\n        <div class=\"toolbar-title\"><span />出库单明细</div>\r\n      </div>\r\n      <el-divider class=\"elDivder\" />\r\n\r\n      <div style=\"display: flex; justify-content: space-between;align-items: center;\">\r\n        <div v-if=\"!isView&&!isReturn\" style=\"display: flex;align-items: center;\">\r\n          <el-button\r\n            type=\"primary\"\r\n            @click=\"openAddDialog(null)\"\r\n          >新增</el-button>\r\n          <el-button\r\n            type=\"danger\"\r\n            :disabled=\"!multipleSelection.length\"\r\n            @click=\"handleDelete\"\r\n          >删除</el-button>\r\n          <el-button type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"batchDialogVisible = true\">批量编辑领用项目</el-button>\r\n          <PickSelect style=\"margin-left: 10px\" :selected-list=\"rootTableData\" :material-type=\"1\" @addList=\"getAddList\" />\r\n        </div>\r\n        <div style=\"margin-left: auto\">\r\n          <el-form\r\n            ref=\"searchForm\"\r\n            inline\r\n            :model=\"searchForm\"\r\n            label-width=\"80px\"\r\n          >\r\n            <el-form-item label=\"辅料名称\" prop=\"RawName\">\r\n              <el-input\r\n                v-model=\"searchForm.RawName\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请输入\"\r\n                clearable\r\n              />\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"规格\" prop=\"Spec\">\r\n              <el-input\r\n                v-model=\"searchForm.Spec\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请输入\"\r\n                clearable\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"所属项目\" prop=\"SysProjectId\">\r\n              <el-select\r\n                v-model=\"searchForm.SysProjectId\"\r\n                class=\"input\"\r\n                placeholder=\"所属项目\"\r\n                clearable\r\n                filterable\r\n              >\r\n                <el-option\r\n                  v-for=\"item in projectOptions\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Short_Name\"\r\n                  :value=\"item.Sys_Project_Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item>\r\n              <el-button type=\"primary\" @click=\"handleSearch\">搜索</el-button>\r\n              <el-button @click=\"handleReset\">重置</el-button>\r\n            </el-form-item>\r\n            <DynamicTableFields\r\n              title=\"表格配置\"\r\n              :table-config-code=\"gridCode\"\r\n              :manual-hide-columns=\"[{'Code':'PartyUnitName'}]\"\r\n              @updateColumn=\"changeColumn\"\r\n            />\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"tb-x\">\r\n        <component\r\n          :is=\"currentTbComponent\"\r\n          v-if=\"currentTbComponent\"\r\n          ref=\"table\"\r\n          :is-return=\"isReturn\"\r\n          :is-view=\"isView\"\r\n          :big-type-data=\"BigType\"\r\n          @changeStandard=\"changeStandard\"\r\n          @updateTb=\"handleUpdateTb\"\r\n          @changeWarehouse=\"changeWarehouse\"\r\n          @select=\"setSelectRow\"\r\n        />\r\n      </div>\r\n      <el-divider v-if=\"!isView\" class=\"elDivder\" />\r\n      <footer v-if=\"!isView\">\r\n        <div class=\"data-info\">\r\n          <el-tag\r\n            v-if=\"!isReturn\"\r\n            size=\"medium\"\r\n            class=\"info-x\"\r\n          >已选{{ multipleSelection.length }}条数据\r\n          </el-tag>\r\n        </div>\r\n        <div>\r\n          <el-button @click=\"closeView\">取消</el-button>\r\n          <el-button v-if=\"isReturn\" :loading=\"returning\" type=\"primary\" @click=\"handleReturn\">确认退库</el-button>\r\n\r\n          <template v-else>\r\n            <el-button\r\n              :loading=\"saveLoading\"\r\n              @click=\"saveDraft(1)\"\r\n            >保存草稿\r\n            </el-button>\r\n            <el-button type=\"primary\" :loading=\"saveLoading\" @click=\"handleSubmit\">提交出库</el-button>\r\n          </template>\r\n        </div>\r\n      </footer>\r\n    </el-card>\r\n\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"dWidth\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :page-type=\"1\"\r\n        @close=\"handleClose\"\r\n        @warehouse=\"getWarehouse\"\r\n        @batchEditor=\"batchEditorFn\"\r\n        @importData=\"importData\"\r\n        @standard=\"getStandard\"\r\n        @refresh=\"fetchData\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      title=\"新增\"\r\n      :visible.sync=\"openAddList\"\r\n      width=\"70%\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <template v-if=\"openAddList\">\r\n        <add-purchase-list\r\n          ref=\"draft\"\r\n          :big-type-data=\"BigType\"\r\n          :p-form=\"form\"\r\n          :joined-items=\"rootTableData\"\r\n          @getAddList=\"getAddList\"\r\n          @getRowName=\"getRowName\"\r\n          @close=\"handleClose\"\r\n        />\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      title=\"批量编辑领用项目\"\r\n      :visible.sync=\"batchDialogVisible\"\r\n      top=\"10vh\"\r\n      width=\"350px\"\r\n      @close=\"closeBatchDialog\"\r\n    >\r\n      <el-select\r\n        v-model=\"batchProjectId\"\r\n        style=\"width: 300px\"\r\n        placeholder=\"请选择\"\r\n        clearable\r\n        filterable\r\n      >\r\n        <el-option\r\n          v-for=\"item in projectOptions\"\r\n          :key=\"item.Id\"\r\n          :label=\"item.Short_Name\"\r\n          :value=\"item.Sys_Project_Id\"\r\n        />\r\n      </el-select>\r\n      <p style=\"margin: 20px\">\r\n        <i>注：仅能批量编辑公共库存的领用项目</i>\r\n      </p>\r\n      <div style=\"text-align: right\">\r\n        <el-button @click=\"closeBatchDialog\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"batchChangeProject\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { closeTagView, debounce, deepClone } from '@/utils'\r\nimport PurchaseTb from './components/PurchaseTb.vue'\r\nimport AddList from './components/AddList.vue'\r\nimport AddPurchaseList from './components/AddPurchaseList.vue'\r\nimport ImportFile from '../components/ImportFile.vue'\r\nimport Standard from './components/Standard.vue'\r\nimport { GetProjectPageList } from '@/api/PRO/project'\r\nimport OSSUpload from '@/views/PRO/components/ossupload'\r\nimport getCommonData from '@/mixins/PRO/get-common-data/index.js'\r\nimport { GetFirstLevelDepartsUnderFactory } from '@/api/OMA/nonOperating.js'\r\nimport {\r\n  AuxReturnByReceipt,\r\n  GetAuxDetailByReceipt, GetTeamListByUserForMateriel,\r\n  GetUserPage\r\n} from '@/api/PRO/material-warehouse/material-inventory-reconfig.js'\r\n// import { GetFactoryPeoplelist } from '@/api/PRO/basic-information/workshop'\r\nimport { GetProcessListBase } from '@/api/PRO/technology-lib'\r\nimport { getDictionary } from '@/utils/common'\r\nimport { GetPreferenceSettingValue } from '@/api/sys/system-setting'\r\nimport {\r\n  PickOutStore,\r\n  GetPickOutDetail\r\n} from '@/api/PRO/material-warehouse/material-inventory-reconfig.js'\r\nimport { GetOMALatestStatisticTime } from '@/api/PRO/materialManagement'\r\nimport { GetOssUrl, GetCompanyDepartTree } from '@/api/sys'\r\nimport ReturnTb from './components/ReturnTb.vue'\r\nimport getTableConfig from '@/mixins/PRO/get-table-info-pro/index'\r\nimport Warehouse from '@/views/PRO/material-receipt-management/components/Warehouse.vue'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport SelectMaterialStoreType from '@/components/Select/SelectMaterialStoreType/index.vue'\r\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\r\nimport { GetWorkingTeams } from '@/api/PRO/technology-lib'\r\nimport SelectDepartment from '@/components/Select/SelectDepartment/index.vue'\r\nimport SelectDepartmentUser from '@/components/Select/SelectDepartmentUser/index.vue'\r\nimport PickSelect from '@/views/PRO/material_v4/pickApply/select.vue'\r\nexport default {\r\n  components: {\r\n    PickSelect,\r\n    SelectDepartmentUser, SelectDepartment,\r\n    DynamicTableFields,\r\n    SelectMaterialStoreType,\r\n    AddPurchaseList,\r\n    PurchaseTb,\r\n    ReturnTb,\r\n    ImportFile,\r\n    Warehouse,\r\n    Standard,\r\n    AddList,\r\n    OSSUpload\r\n  },\r\n  mixins: [getCommonData, getTableConfig],\r\n  props: {\r\n    pageType: {\r\n      type: Number,\r\n      default: undefined\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      isRetract: false, // 是否展开\r\n      returning: false,\r\n      tbLoading: false,\r\n      projectOptions: [],\r\n      multipleSelection: [],\r\n      // PartyUnitData: [],\r\n      // SupplierData: [],\r\n      factoryPeoplelist: [], // 领料人列表\r\n      departmentlist: [],\r\n      ProcessList: [],\r\n      AuxOutboundTypeList: [],\r\n      WorkingTeamList: [], // 领料班组列表\r\n      searchForm: {\r\n        RawName: '',\r\n        Spec: ''\r\n      },\r\n      form: {\r\n        OutStoreNo: '', // 出库单号\r\n        OutStoreType: 1, // 出库类型\r\n        OutStoreDate: this.getDate(),\r\n        Use_Processing_Id: '', // 工序id\r\n        Pick_Department_Id: '',\r\n        // ProjectId: '',\r\n        SysProjectId: '',\r\n        Pick_Project_Name: '', // 领用项目名称\r\n        ReceiveUserId: '', // 领料人id\r\n        WorkingTeamId: '', // 领料班组id\r\n        Remark: '',\r\n        Attachment: ''\r\n      },\r\n      rules: {\r\n        OutStoreType: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        OutStoreDate: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        Pick_Department_Id: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        ReceiveUserId: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ]\r\n        // WorkingTeamId: [\r\n        //   { required: true, message: '请选择', trigger: 'change' }\r\n        // ]\r\n      },\r\n      treeParamsDepart: {\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          disabled: 'disabled',\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      backendDate: null,\r\n      pickerOptions: {\r\n        disabledDate: (time) => {\r\n          return time.getTime() < new Date(this.backendDate).getTime() // 限制选择日期不能超过当前日期\r\n        }\r\n      },\r\n      currentComponent: '',\r\n      title: '',\r\n      dWidth: '60%',\r\n      // isSingle: false,\r\n      saveLoading: false,\r\n      search: () => ({}),\r\n      openAddList: false,\r\n      dialogVisible: false,\r\n      BigType: 1,\r\n      isProductweight: null,\r\n      fileListData: [],\r\n      fileListArr: [],\r\n      searchNum: 1,\r\n      rootTableData: [],\r\n      tableData: [],\r\n      typeNumber1: 0,\r\n      typeNumber2: 0,\r\n      typeNumber3: 0,\r\n      typeNumber4: 0,\r\n      currentTbComponent: '',\r\n      batchDialogVisible: false,\r\n      batchProjectId: ''\r\n    }\r\n  },\r\n  computed: {\r\n    isAdd() {\r\n      return this.pageType === 1\r\n    },\r\n    isEdit() {\r\n      return this.pageType === 2\r\n    },\r\n    isView() {\r\n      return this.pageType === 3\r\n    },\r\n    isPurchase() {\r\n      return this.form.OutStoreType == 1 // 领用出库\r\n    },\r\n    isReturn() {\r\n      return this.pageType === 8\r\n    },\r\n    gridCode() {\r\n      return this.isReturn ? 'pro_aux_material_outbound_detail_list_return' : 'pro_aux_material_outbound_detail_list,Steel'\r\n    }\r\n  },\r\n  async mounted() {\r\n    if (this.isReturn) {\r\n      this.currentTbComponent = ReturnTb\r\n      const column = await this.getTableConfig(this.gridCode)\r\n      this.$nextTick(_ => {\r\n        this.$refs['table'].init(column)\r\n      })\r\n    } else {\r\n      this.currentTbComponent = PurchaseTb\r\n    }\r\n    await this.getCurFactory()\r\n    await this.getOMALatestStatisticTime()\r\n    this.getPreferenceSettingValue()\r\n    this.AuxOutboundTypeList = await getDictionary('AuxOutboundType')\r\n    await this.getFirstLevelDepartsUnderFactory()\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    this.getProject()\r\n    this.getProcessListBase()\r\n    // this.getFactoryPeoplelist()\r\n    this.getWorkingTeams()\r\n    if (!this.isAdd) {\r\n      this.getInfo()\r\n    }\r\n  },\r\n  methods: {\r\n    userChange(val) {\r\n      GetTeamListByUserForMateriel({\r\n        id: val\r\n      }).then(res => {\r\n        if (res.Data?.length === 1) {\r\n          this.$set(this.form, 'WorkingTeamId', res.Data[0].Id)\r\n        } else {\r\n          this.$set(this.form, 'WorkingTeamId', '')\r\n        }\r\n      })\r\n    },\r\n    closeBatchDialog() {\r\n      this.batchProjectId = ''\r\n      this.batchDialogVisible = false\r\n    },\r\n    batchChangeProject() {\r\n      const tbData = this.$refs.table.tbData\r\n      this.multipleSelection.forEach((element, idx) => {\r\n        const item = tbData.find((v) => v.index === element.index)\r\n        const i = tbData.findIndex((v) => v.index === element.index)\r\n        item.Pick_Sys_Project_Id = this.batchProjectId\r\n        // 同时更新项目名称\r\n        item.Pick_Project_Name = this.projectOptions.find(project => project.Sys_Project_Id === this.batchProjectId)?.Short_Name\r\n        this.$set(tbData, i, item)\r\n      })\r\n      this.closeBatchDialog()\r\n    },\r\n    changeColumn() {\r\n      const temp = this.currentTbComponent\r\n      this.currentTbComponent = ''\r\n      this.$nextTick(async() => {\r\n        this.currentTbComponent = temp\r\n        if (this.isReturn) {\r\n          const column = await this.getTableConfig(this.gridCode)\r\n          this.$nextTick(_ => {\r\n            this.$refs['table'].init(column)\r\n          })\r\n        }\r\n        this.$refs['table'].setData(this.tableData)\r\n      })\r\n    },\r\n    // 附件上传成功\r\n    uploadSuccess(response, file, fileList) {\r\n      this.fileListArr = JSON.parse(JSON.stringify(fileList))\r\n    },\r\n    // 移除已上传文件\r\n    uploadRemove(file, fileList) {\r\n      this.fileListArr = JSON.parse(JSON.stringify(fileList))\r\n    },\r\n    // 点击已上传文件\r\n    async handlePreview(file) {\r\n      let encryptionUrl = ''\r\n      if (file.response && file.response.encryptionUrl) {\r\n        encryptionUrl = file.response.encryptionUrl\r\n      } else {\r\n        encryptionUrl = await GetOssUrl({ url: file.encryptionUrl })\r\n        encryptionUrl = encryptionUrl.Data\r\n      }\r\n      window.open(encryptionUrl)\r\n    },\r\n    // 获取运营核算已统计的最新日期\r\n    async getOMALatestStatisticTime() {\r\n      const res = await GetOMALatestStatisticTime({})\r\n      if (res.IsSucceed) {\r\n        this.backendDate = res.Data || ''\r\n      } else {\r\n        this.message.error(res.Mesaage)\r\n      }\r\n    },\r\n    // 搜索\r\n    handleSearch() {\r\n      this.tableData = JSON.parse(JSON.stringify(this.rootTableData))\r\n      if (this.searchForm.RawName) {\r\n        const rawNameRegex = new RegExp(this.searchForm.RawName, 'i')\r\n        this.tableData = this.tableData.filter((item) => {\r\n          return rawNameRegex.test(item.RawName)\r\n        })\r\n      }\r\n\r\n      if (this.searchForm.Spec) {\r\n        const specRegex = new RegExp(this.searchForm.Spec, 'i')\r\n        this.tableData = this.tableData.filter((item) => {\r\n          return specRegex.test(item.Spec)\r\n        })\r\n      }\r\n      if (this.searchForm.SysProjectId) {\r\n        const sysProjectIdRegex = new RegExp(this.searchForm.SysProjectId, 'i')\r\n        this.tableData = this.tableData.filter((item) => {\r\n          return sysProjectIdRegex.test(item.Sys_Project_Id)\r\n        })\r\n      }\r\n      if (this.isReturn) {\r\n        this.$refs['table'].setData(this.tableData)\r\n      } else {\r\n        this.$refs['table'].tbData = this.tableData\r\n      }\r\n    },\r\n    // 更新表格数据\r\n    handleUpdateTb() {\r\n      this.rootTableData = this.$refs['table'].tbData\r\n      this.tableData = JSON.parse(JSON.stringify(this.rootTableData))\r\n      console.log(this.rootTableData, '11')\r\n      console.log(this.tableData, '22')\r\n      // this.setTabData()\r\n    },\r\n    // 获取品重偏好设置\r\n    getPreferenceSettingValue() {\r\n      GetPreferenceSettingValue({ code: 'Productweight' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.isProductweight = res.Data\r\n          // this.isProductweight = \"false\";\r\n          if (this.isProductweight !== 'true') {\r\n            this.getDepartmentTree()\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getDepartmentTree() {\r\n      GetCompanyDepartTree({ isAll: true }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const tree = res.Data\r\n          this.setDisabledTree(tree)\r\n          this.treeParamsDepart.data = tree\r\n          this.$nextTick((_) => {\r\n            this.$refs.treeSelectDepart.treeDataUpdateFun(tree)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setDisabledTree(root) {\r\n      if (!root) return\r\n      root.forEach((element) => {\r\n        const { Children } = element\r\n        if (element.Data.Is_Company === true || element.Data.Type === '1') {\r\n          element.disabled = true\r\n        } else {\r\n          element.disabled = false\r\n        }\r\n        if (Children.length > 0) {\r\n          this.setDisabledTree(Children)\r\n        }\r\n      })\r\n    },\r\n    // 重置搜索\r\n    handleReset() {\r\n      this.searchNum = 1\r\n      // this.$refs['searchForm'].resetFields()\r\n      this.searchForm.RawName = ''\r\n      this.searchForm.Spec = ''\r\n      this.$refs['table'].tbData = this.rootTableData\r\n      this.tableData = JSON.parse(JSON.stringify(this.rootTableData))\r\n    },\r\n    // 文件超出数量限制\r\n    handleExceed() {\r\n      this.$message({\r\n        type: 'warning',\r\n        message: '附件数量不能超过5个'\r\n      })\r\n    },\r\n    handleRetract() {\r\n      this.isRetract = !this.isRetract\r\n    },\r\n    fetchData() {},\r\n    getInfo() {\r\n      this.form.OutStoreNo = this.$route.query.OutStoreNo\r\n      this.form.OutStoreType = +this.$route.query.OutStoreType\r\n      // const _fun = this.$route.params.OutStoreType === 1 ? PickUpOutStoreDetail : PartyAOutStoreDetail\r\n      let _fun\r\n      let params\r\n      if (this.isReturn) {\r\n        _fun = GetAuxDetailByReceipt\r\n        params = {\r\n          Id: this.$route.query.id\r\n        }\r\n      } else {\r\n        _fun = GetPickOutDetail\r\n        params = {\r\n          outStoreNo: this.$route.query.OutStoreNo\r\n        }\r\n      }\r\n      _fun(params).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const Receipt = res.Data.Receipt\r\n          const Sub = res.Data.Sub\r\n          const {\r\n            OutStoreDate,\r\n            Remark,\r\n            SysProjectId,\r\n            Pick_Department_Id,\r\n            Use_Processing_Id,\r\n            WorkingTeamId,\r\n            ReceiveUserId,\r\n            Attachment,\r\n            Status\r\n          } = Receipt\r\n          this.form.OutStoreDate = this.getDate(new Date(OutStoreDate))\r\n          this.form.Remark = Remark\r\n          this.form.SysProjectId = SysProjectId\r\n          this.form.Use_Processing_Id = Use_Processing_Id\r\n          this.form.Pick_Department_Id = Pick_Department_Id\r\n          if (this.form.Pick_Department_Id) {\r\n            this.departmentChange()\r\n          }\r\n          this.form.Use_Processing_Id = Use_Processing_Id\r\n          this.form.WorkingTeamId = WorkingTeamId\r\n          this.form.ReceiveUserId = ReceiveUserId\r\n          this.form.Status = Status\r\n\r\n          // 处理表格数据\r\n          const SubData = Sub.map((row, index) => {\r\n            row.index = uuidv4()\r\n            row.Warehouse_Location = row.WarehouseName\r\n              ? row.WarehouseName + '/' + row.LocationName\r\n              : ''\r\n            // 临时存储kg\r\n            // row.OutStoreWeightKG = row.OutStoreWeight\r\n            // row.Pound_Weight_KG = row.Pound_Weight\r\n            // row.OutStoreWeight = Number((row.OutStoreWeightKG / 1000).toFixed(3))\r\n            // row.Pound_Weight = Number((row.Pound_Weight_KG / 1000).toFixed(3))\r\n            return row\r\n          })\r\n\r\n          this.$nextTick((_) => {\r\n            this.$refs['table'].tbData = JSON.parse(JSON.stringify(SubData))\r\n            this.tableData = JSON.parse(JSON.stringify(SubData))\r\n            this.rootTableData = JSON.parse(JSON.stringify(SubData))\r\n          })\r\n          if (Attachment) {\r\n            this.form.Attachment = Attachment\r\n            const AttachmentArr = Attachment.split(',')\r\n            AttachmentArr.forEach((item) => {\r\n              const fileUrl =\r\n                item.indexOf('?Expires=') > -1\r\n                  ? item.substring(0, item.lastIndexOf('?Expires='))\r\n                  : item\r\n              const fileName = decodeURI(fileUrl.substring(fileUrl.lastIndexOf('/') + 1))\r\n              const AttachmentJson = {}\r\n              AttachmentJson.name = fileName\r\n              AttachmentJson.url = fileUrl\r\n              AttachmentJson.encryptionUrl = fileUrl\r\n              this.fileListData.push(AttachmentJson)\r\n              this.fileListArr.push(AttachmentJson)\r\n            })\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async getProcessListBase() {\r\n      const res = await GetProcessListBase({})\r\n      if (res.IsSucceed) {\r\n        this.ProcessList = res.Data || []\r\n      } else {\r\n        this.message.error(res.Mesaage)\r\n      }\r\n    },\r\n    departmentChange() {\r\n      this.form.ReceiveUserId = ''\r\n      if (this.form.Pick_Department_Id) {\r\n        this.getUserPageList(this.form.Pick_Department_Id)\r\n      }\r\n    },\r\n    getUserPageList(Id) {\r\n      GetUserPage({\r\n        PageSize: -1,\r\n        DepartmentId: Id\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.factoryPeoplelist = res.Data.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async getFirstLevelDepartsUnderFactory() {\r\n      const res = await GetFirstLevelDepartsUnderFactory({\r\n        FactoryId: this.FactoryDetailData.Id\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.departmentlist = res.Data || []\r\n        this.form.Pick_Department_Id = res.Data.find(\r\n          (item) => item.Is_Cur_User_Depart\r\n        )\r\n          ? res.Data.find((item) => item.Is_Cur_User_Depart).Id\r\n          : ''\r\n        this.departmentChange()\r\n      } else {\r\n        this.message.error(res.Mesaage)\r\n      }\r\n    },\r\n    /**\r\n     * 获取所属项目\r\n     */\r\n    getProject() {\r\n      GetProjectPageList({\r\n        Page: 1,\r\n        PageSize: -1\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.projectOptions = res.Data.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 获取领料人列表\r\n     */\r\n    // getFactoryPeoplelist() {\r\n    //   GetFactoryPeoplelist().then((res) => {\r\n    //     if (res.IsSucceed) {\r\n    //       this.factoryPeoplelist = res.Data\r\n    //     } else {\r\n    //       this.$message({\r\n    //         message: res.Message,\r\n    //         type: 'error'\r\n    //       })\r\n    //     }\r\n    //   })\r\n    // },\r\n\r\n    /**\r\n     * 获取领料班组列表\r\n     */\r\n    getWorkingTeams() {\r\n      GetWorkingTeams().then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.WorkingTeamList = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    changeWarehouse(row) {\r\n      this.currentRow = row\r\n      this.handleWarehouse(true)\r\n    },\r\n    handleWarehouse(isInline) {\r\n      this.currentComponent = 'Warehouse'\r\n      this.dWidth = '40%'\r\n      this.title = '选择仓库/库位'\r\n      !isInline && (this.currentRow = null)\r\n      this.dialogVisible = true\r\n    },\r\n    getWarehouse({ warehouse, location }) {\r\n      if (this.currentRow) {\r\n        this.currentRow.ReturnWarehoueseId = warehouse.Id\r\n        this.currentRow.ReturnLocationId = location.Id\r\n        this.$set(this.currentRow, 'ReturnWarehoueseName', warehouse.Display_Name)\r\n        this.$set(this.currentRow, 'ReturnLocationName', location.Display_Name)\r\n        this.$set(\r\n          this.currentRow,\r\n          'Warehouse_Location_Return',\r\n          warehouse.Display_Name + '/' + location.Display_Name\r\n        )\r\n      }\r\n    },\r\n    batchEditorFn(data) {\r\n      if (this.currentRow) {\r\n        data.forEach((item) => {\r\n          this.$set(this.currentRow, item.key, item.val)\r\n        })\r\n      } else {\r\n        this.multipleSelection.forEach((element, idx) => {\r\n          data.forEach((item) => {\r\n            this.$set(element, item.key, item.val)\r\n          })\r\n        })\r\n      }\r\n      this.handleClose()\r\n    },\r\n    changeStandard(row) {\r\n      this.currentRow = row\r\n      this.currentComponent = 'Standard'\r\n      this.dWidth = '40%'\r\n      this.title = '选择规格'\r\n      this.dialogVisible = true\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].getOption(row)\r\n      })\r\n    },\r\n    getStandard({ type, val }) {\r\n      if (type === 1) {\r\n        this.$set(this.currentRow, 'StandardDesc', val)\r\n      } else {\r\n        this.$set(this.currentRow, 'StandardDesc', val.StandardDesc)\r\n        this.currentRow.StandardId = val.StandardId\r\n      }\r\n    },\r\n    // 出库类型切换,表格数据清空\r\n    typeChange(n) {\r\n      if (n !== 0) {\r\n        this.BigType = 1\r\n        this.typeNumber1 = 0\r\n        this.typeNumber2 = 0\r\n        this.typeNumber3 = 0\r\n        this.typeNumber4 = 0\r\n        this.tableData = []\r\n        this.$refs['table'].tbData = []\r\n      }\r\n    },\r\n    // handleReset() {\r\n    //   this.$refs[\"form\"].resetFields();\r\n    //   this.search(1);\r\n    // },\r\n    // 新增物料\r\n    getAddList(list, info) {\r\n      this.BigType = list[0].BigType\r\n      list.map((item, index) => {\r\n        item.index = uuidv4()\r\n        item.Pick_Project_Name = item.Project_Name // 默认领用项目为所属项目\r\n      })\r\n      this.$refs['table'].addData(list)\r\n      const tbData = this.$refs.table.tbData\r\n      this.tableData = JSON.parse(JSON.stringify(tbData))\r\n      this.rootTableData = JSON.parse(JSON.stringify(tbData))\r\n      if (info) {\r\n        this.form.WorkingTeamId = info.Pick_Team_Id\r\n        this.form.Use_Processing_Id = info.Pick_Process_Id\r\n      }\r\n    },\r\n    importData(list) {\r\n      this.$refs['table'].importData(list)\r\n    },\r\n    getRowName({ Name, Id }) {\r\n      this.currentRow.Name = Name\r\n      this.currentRow.RawId = Id\r\n      this.currentRow.StandardDesc = ''\r\n    },\r\n    generateComponent(title, component) {\r\n      this.title = title\r\n      this.currentComponent = component\r\n      this.dialogVisible = true\r\n    },\r\n    handleDelete() {\r\n      const tbData = this.$refs.table.tbData\r\n      this.multipleSelection.forEach((element, idx) => {\r\n        const i = tbData.findIndex((v) => v.index === element.index)\r\n        tbData.splice(i, 1)\r\n      })\r\n      this.tableData = JSON.parse(JSON.stringify(tbData))\r\n      this.rootTableData = JSON.parse(JSON.stringify(tbData))\r\n      this.multipleSelection = []\r\n      this.$refs.table?.$refs?.xTable.clearCheckboxRow()\r\n    },\r\n    handleClose(row) {\r\n      this.openAddList = false\r\n      this.dialogVisible = false\r\n    },\r\n    tbSelectChange(array) {\r\n      // console.log(array, 'arr====')\r\n      this.multipleSelection = array.records\r\n    },\r\n    handleDetail(row) {},\r\n    // 表格数据校验\r\n    checkValidate() {\r\n      const submit = deepClone(this.$refs['table'].tbData)\r\n      if (!submit.length) {\r\n        this.$message({\r\n          message: '数据不能为空',\r\n          type: 'warning'\r\n        })\r\n        return {\r\n          status: false\r\n        }\r\n      }\r\n      const { status, msg } = this.checkTb(submit)\r\n      if (!status) {\r\n        this.$message({\r\n          message: `${msg || '必填字段'}不能为空`,\r\n          type: 'warning'\r\n        })\r\n        return {\r\n          status: false\r\n        }\r\n      }\r\n      return {\r\n        data: submit,\r\n        status: true\r\n      }\r\n    },\r\n    handleReturn() {\r\n      this.$confirm('是否提交退库信息?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          this.saveDraft(3)\r\n        })\r\n        .catch((e) => {\r\n          console.log('error', e)\r\n\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n    },\r\n    // 提交入库\r\n    handleSubmit(row) {\r\n      this.$confirm('确认提交出库单?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          this.saveDraft(3)\r\n        })\r\n        .catch((e) => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n    },\r\n    // 保存出库单\r\n    saveDraft(type = 1) {\r\n      const { data, status } = this.checkValidate()\r\n      if (!status) return\r\n      this.$refs['form'].validate((valid) => {\r\n        if (!valid) return false\r\n        this.saveLoading = true\r\n        // this.form.Attachment = this.fileListArr.join(',')\r\n        const formAttachment = []\r\n        if (this.fileListArr.length > 0) {\r\n          this.fileListArr.forEach((item) => {\r\n            formAttachment.push(\r\n              item.response && item.response.encryptionUrl\r\n                ? item.response.encryptionUrl\r\n                : item.encryptionUrl\r\n            )\r\n          })\r\n        }\r\n        this.form.Attachment = formAttachment.join(',')\r\n        // 1草稿 2审核中 3通过 4退回\r\n        this.form.Status = type == 1 ? 1 : 3\r\n        const form = { ...this.form }\r\n        // console.log(form, 'this.form===1111')\r\n        // console.log(data, 'Sub===1111')\r\n        let _fun\r\n\r\n        if (this.isReturn) {\r\n          _fun = AuxReturnByReceipt\r\n        } else {\r\n          _fun = PickOutStore\r\n        }\r\n        _fun({\r\n          Receipt: form,\r\n          Sub: data\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            this.closeView()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.saveLoading = false\r\n        })\r\n      })\r\n    },\r\n    checkTb(list) {\r\n      let check = null\r\n      const isEmpty = ['', null, undefined]\r\n      if (this.isReturn) {\r\n        isEmpty.push(0)\r\n        check = ['ReturnCount', 'Warehouse_Location_Return']\r\n      } else {\r\n        check = ['OutStoreCount']\r\n      }\r\n      for (let i = 0; i < list.length; i++) {\r\n        const item = list[i]\r\n        for (let j = 0; j < check.length; j++) {\r\n          const c = check[j]\r\n          if (isEmpty.includes(item[c])) {\r\n            const cloumns = this.$refs.table.rootColumns\r\n            const element = cloumns.find((v) => v.Code === c)\r\n            return {\r\n              status: false,\r\n              msg: element?.Display_Name\r\n            }\r\n          }\r\n        }\r\n        delete item._X_ROW_KEY\r\n        delete item.WarehouseName\r\n        delete item.LocationName\r\n      }\r\n      return {\r\n        status: true,\r\n        msg: ''\r\n      }\r\n    },\r\n    openAddDialog(row) {\r\n      this.openAddList = true\r\n    },\r\n    // openAddDialog(row) {\r\n    //    if (this.form.SysProjectId) {\r\n    //     this.openAddList = true\r\n    //   } else {\r\n    //     this.$message({\r\n    //       message: '请先选择项目',\r\n    //       type: 'warning'\r\n    //     })\r\n    //     return\r\n    //   }\r\n    // },\r\n    closeView() {\r\n      closeTagView(this.$store, this.$route)\r\n    },\r\n    setSelectRow(multipleSelection) {\r\n      this.multipleSelection = multipleSelection\r\n    },\r\n    // 日期格式化\r\n    getDate(data) {\r\n      const date = data || new Date()\r\n      const year = date.getFullYear()\r\n      const month = ('0' + (date.getMonth() + 1)).slice(-2)\r\n      const day = ('0' + date.getDate()).slice(-2)\r\n      return `${year}-${month}-${day}`\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.app-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  .box-card-tb {\r\n    flex: 1;\r\n    margin-top: 8px;\r\n  }\r\n}\r\n// 表格工具栏css\r\n.toolbar-container {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  // margin: 10px 0 0 0;\r\n  // flex-wrap: nowrap;\r\n  ::v-deep .el-radio-group {\r\n    width: 400px;\r\n  }\r\n  .toolbar-title {\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    color: #333333;\r\n    span {\r\n      display: inline-block;\r\n      width: 2px;\r\n      height: 14px;\r\n      background: #009dff;\r\n      margin-right: 6px;\r\n      vertical-align: text-top;\r\n    }\r\n  }\r\n  .search-form {\r\n    width: 60%;\r\n    ::v-deep {\r\n      .el-form-item--small {\r\n        margin-bottom: 0;\r\n      }\r\n      .el-form-item__content {\r\n        width: 110px;\r\n      }\r\n      .last-btn {\r\n        display: flex;\r\n        justify-content: flex-end;\r\n        .el-form-item {\r\n          margin-right: 0;\r\n        }\r\n        .el-form-item__content {\r\n          width: 100%;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .statistics-container {\r\n    display: flex;\r\n    .statistics-item {\r\n      margin-right: 32px;\r\n      span:first-child {\r\n        display: inline-block;\r\n        font-size: 14px;\r\n        line-height: 18px;\r\n        font-weight: 500;\r\n        color: #999999;\r\n        margin-right: 16px !important;\r\n      }\r\n      span:last-child {\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n        color: #00c361;\r\n      }\r\n    }\r\n  }\r\n}\r\n.el-card {\r\n  ::v-deep {\r\n    .el-card__body {\r\n      display: flex;\r\n      flex-direction: column;\r\n    }\r\n  }\r\n\r\n  .tb-x {\r\n    flex: 1;\r\n    margin-bottom: 10px;\r\n    overflow: auto;\r\n  }\r\n}\r\n\r\n::v-deep .elDivder {\r\n  margin: 10px;\r\n}\r\n\r\n.pagination-container {\r\n  text-align: right;\r\n  margin-top: 10px;\r\n  padding: 0;\r\n}\r\n\r\n.upload-file-list {\r\n  & > div {\r\n    width: 100%;\r\n    height: 30px;\r\n    line-height: 30px;\r\n    padding-left: 15px;\r\n    padding-right: 15px;\r\n    cursor: pointer;\r\n    position: relative;\r\n    i {\r\n      margin-right: 10px;\r\n    }\r\n    i:last-child {\r\n      position: absolute;\r\n      top: 50%;\r\n      transform: translateY(-50%);\r\n      right: 15px;\r\n      color: #999999;\r\n      font-size: 18px;\r\n    }\r\n  }\r\n  & > div:hover {\r\n    background-color: #f8f8f8;\r\n    i:last-child {\r\n      color: #298dff;\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .el-form-item {\r\n  .el-form-item__content {\r\n    .el-tree-select-input {\r\n      width: 100% !important;\r\n    }\r\n  }\r\n}\r\n\r\nfooter {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuZA,SAAAA,YAAA,EAAAC,QAAA,EAAAC,SAAA;AACA,OAAAC,UAAA;AACA,OAAAC,OAAA;AACA,OAAAC,eAAA;AACA,OAAAC,UAAA;AACA,OAAAC,QAAA;AACA,SAAAC,kBAAA;AACA,OAAAC,SAAA;AACA,OAAAC,aAAA;AACA,SAAAC,gCAAA;AACA,SACAC,kBAAA,EACAC,qBAAA,EAAAC,4BAAA,EACAC,WAAA,QACA;AACA;AACA,SAAAC,kBAAA;AACA,SAAAC,aAAA;AACA,SAAAC,yBAAA;AACA,SACAC,YAAA,EACAC,gBAAA,QACA;AACA,SAAAC,yBAAA;AACA,SAAAC,SAAA,EAAAC,oBAAA;AACA,OAAAC,QAAA;AACA,OAAAC,cAAA;AACA,OAAAC,SAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AACA,OAAAC,uBAAA;AACA,OAAAC,kBAAA;AACA,SAAAC,eAAA;AACA,OAAAC,gBAAA;AACA,OAAAC,oBAAA;AACA,OAAAC,UAAA;AACA;EACAC,UAAA;IACAD,UAAA,EAAAA,UAAA;IACAD,oBAAA,EAAAA,oBAAA;IAAAD,gBAAA,EAAAA,gBAAA;IACAF,kBAAA,EAAAA,kBAAA;IACAD,uBAAA,EAAAA,uBAAA;IACAxB,eAAA,EAAAA,eAAA;IACAF,UAAA,EAAAA,UAAA;IACAqB,QAAA,EAAAA,QAAA;IACAlB,UAAA,EAAAA,UAAA;IACAoB,SAAA,EAAAA,SAAA;IACAnB,QAAA,EAAAA,QAAA;IACAH,OAAA,EAAAA,OAAA;IACAK,SAAA,EAAAA;EACA;EACA2B,MAAA,GAAA1B,aAAA,EAAAe,cAAA;EACAY,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,EAAAC;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,SAAA;MAAA;MACAC,SAAA;MACAC,SAAA;MACAC,cAAA;MACAC,iBAAA;MACA;MACA;MACAC,iBAAA;MAAA;MACAC,cAAA;MACAC,WAAA;MACAC,mBAAA;MACAC,eAAA;MAAA;MACAC,UAAA;QACAC,OAAA;QACAC,IAAA;MACA;MACAC,IAAA;QACAC,UAAA;QAAA;QACAC,YAAA;QAAA;QACAC,YAAA,OAAAC,OAAA;QACAC,iBAAA;QAAA;QACAC,kBAAA;QACA;QACAC,YAAA;QACAC,iBAAA;QAAA;QACAC,aAAA;QAAA;QACAC,aAAA;QAAA;QACAC,MAAA;QACAC,UAAA;MACA;MACAC,KAAA;QACAX,YAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAb,YAAA,GACA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,kBAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,aAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QAEA;QACA;QACA;MACA;MACAC,gBAAA;QACA;QACAC,UAAA;QACAC,WAAA;QACAlC,IAAA;QACAN,KAAA;UACAyC,QAAA;UACAC,QAAA;UACAC,KAAA;UACAC,KAAA;QACA;MACA;MACAC,WAAA;MACAC,aAAA;QACAC,YAAA,WAAAA,aAAAC,IAAA;UACA,OAAAA,IAAA,CAAAC,OAAA,SAAAC,IAAA,CAAA3C,KAAA,CAAAsC,WAAA,EAAAI,OAAA;QACA;MACA;MACAE,gBAAA;MACAC,KAAA;MACAC,MAAA;MACA;MACAC,WAAA;MACAC,MAAA,WAAAA,OAAA;QAAA;MAAA;MACAC,WAAA;MACAC,aAAA;MACAC,OAAA;MACAC,eAAA;MACAC,YAAA;MACAC,WAAA;MACAC,SAAA;MACAC,aAAA;MACAC,SAAA;MACAC,WAAA;MACAC,WAAA;MACAC,WAAA;MACAC,WAAA;MACAC,kBAAA;MACAC,kBAAA;MACAC,cAAA;IACA;EACA;EACAC,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA,YAAAxE,QAAA;IACA;IACAyE,MAAA,WAAAA,OAAA;MACA,YAAAzE,QAAA;IACA;IACA0E,MAAA,WAAAA,OAAA;MACA,YAAA1E,QAAA;IACA;IACA2E,UAAA,WAAAA,WAAA;MACA,YAAAvD,IAAA,CAAAE,YAAA;IACA;IACAsD,QAAA,WAAAA,SAAA;MACA,YAAA5E,QAAA;IACA;IACA6E,QAAA,WAAAA,SAAA;MACA,YAAAD,QAAA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAAC,MAAA;MAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAA,KACAV,MAAA,CAAAH,QAAA;cAAAW,QAAA,CAAAE,IAAA;cAAA;YAAA;YACAV,MAAA,CAAAX,kBAAA,GAAAlF,QAAA;YAAAqG,QAAA,CAAAE,IAAA;YAAA,OACAV,MAAA,CAAA5F,cAAA,CAAA4F,MAAA,CAAAF,QAAA;UAAA;YAAAO,MAAA,GAAAG,QAAA,CAAAG,IAAA;YACAX,MAAA,CAAAY,SAAA,WAAAC,CAAA;cACAb,MAAA,CAAAc,KAAA,UAAAC,IAAA,CAAAV,MAAA;YACA;YAAAG,QAAA,CAAAE,IAAA;YAAA;UAAA;YAEAV,MAAA,CAAAX,kBAAA,GAAAvG,UAAA;UAAA;YAAA0H,QAAA,CAAAE,IAAA;YAAA,OAEAV,MAAA,CAAAgB,aAAA;UAAA;YAAAR,QAAA,CAAAE,IAAA;YAAA,OACAV,MAAA,CAAAiB,yBAAA;UAAA;YACAjB,MAAA,CAAAkB,yBAAA;YAAAV,QAAA,CAAAE,IAAA;YAAA,OACA9G,aAAA;UAAA;YAAAoG,MAAA,CAAAhE,mBAAA,GAAAwE,QAAA,CAAAG,IAAA;YAAAH,QAAA,CAAAE,IAAA;YAAA,OACAV,MAAA,CAAAmB,gCAAA;UAAA;YACAnB,MAAA,CAAAzB,MAAA,GAAA3F,QAAA,CAAAoH,MAAA,CAAAoB,SAAA;YACApB,MAAA,CAAAqB,UAAA;YACArB,MAAA,CAAAsB,kBAAA;YACA;YACAtB,MAAA,CAAAuB,eAAA;YACA,KAAAvB,MAAA,CAAAP,KAAA;cACAO,MAAA,CAAAwB,OAAA;YACA;UAAA;UAAA;YAAA,OAAAhB,QAAA,CAAAiB,IAAA;QAAA;MAAA,GAAArB,OAAA;IAAA;EACA;EACAsB,OAAA;IACAC,UAAA,WAAAA,WAAAC,GAAA;MAAA,IAAAC,MAAA;MACApI,4BAAA;QACAqI,EAAA,EAAAF;MACA,GAAAG,IAAA,WAAAC,GAAA;QAAA,IAAAC,SAAA;QACA,MAAAA,SAAA,GAAAD,GAAA,CAAAE,IAAA,cAAAD,SAAA,uBAAAA,SAAA,CAAAE,MAAA;UACAN,MAAA,CAAAO,IAAA,CAAAP,MAAA,CAAAxF,IAAA,mBAAA2F,GAAA,CAAAE,IAAA,IAAAG,EAAA;QACA;UACAR,MAAA,CAAAO,IAAA,CAAAP,MAAA,CAAAxF,IAAA;QACA;MACA;IACA;IACAiG,gBAAA,WAAAA,iBAAA;MACA,KAAA/C,cAAA;MACA,KAAAD,kBAAA;IACA;IACAiD,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,MAAA,QAAA3B,KAAA,CAAA4B,KAAA,CAAAD,MAAA;MACA,KAAA7G,iBAAA,CAAA+G,OAAA,WAAAC,OAAA,EAAAC,GAAA;QAAA,IAAAC,qBAAA;QACA,IAAAC,IAAA,GAAAN,MAAA,CAAAO,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,KAAA,KAAAN,OAAA,CAAAM,KAAA;QAAA;QACA,IAAAC,CAAA,GAAAV,MAAA,CAAAW,SAAA,WAAAH,CAAA;UAAA,OAAAA,CAAA,CAAAC,KAAA,KAAAN,OAAA,CAAAM,KAAA;QAAA;QACAH,IAAA,CAAAM,mBAAA,GAAAb,MAAA,CAAAjD,cAAA;QACA;QACAwD,IAAA,CAAAlG,iBAAA,IAAAiG,qBAAA,GAAAN,MAAA,CAAA7G,cAAA,CAAAqH,IAAA,WAAAM,OAAA;UAAA,OAAAA,OAAA,CAAAC,cAAA,KAAAf,MAAA,CAAAjD,cAAA;QAAA,gBAAAuD,qBAAA,uBAAAA,qBAAA,CAAAU,UAAA;QACAhB,MAAA,CAAAJ,IAAA,CAAAK,MAAA,EAAAU,CAAA,EAAAJ,IAAA;MACA;MACA,KAAAT,gBAAA;IACA;IACAmB,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,IAAA,QAAAtE,kBAAA;MACA,KAAAA,kBAAA;MACA,KAAAuB,SAAA,cAAAX,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAyD,SAAA;QAAA,IAAAvD,MAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAuD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArD,IAAA,GAAAqD,SAAA,CAAApD,IAAA;YAAA;cACAgD,MAAA,CAAArE,kBAAA,GAAAsE,IAAA;cAAA,KACAD,MAAA,CAAA7D,QAAA;gBAAAiE,SAAA,CAAApD,IAAA;gBAAA;cAAA;cAAAoD,SAAA,CAAApD,IAAA;cAAA,OACAgD,MAAA,CAAAtJ,cAAA,CAAAsJ,MAAA,CAAA5D,QAAA;YAAA;cAAAO,MAAA,GAAAyD,SAAA,CAAAnD,IAAA;cACA+C,MAAA,CAAA9C,SAAA,WAAAC,CAAA;gBACA6C,MAAA,CAAA5C,KAAA,UAAAC,IAAA,CAAAV,MAAA;cACA;YAAA;cAEAqD,MAAA,CAAA5C,KAAA,UAAAiD,OAAA,CAAAL,MAAA,CAAA1E,SAAA;YAAA;YAAA;cAAA,OAAA8E,SAAA,CAAArC,IAAA;UAAA;QAAA,GAAAmC,QAAA;MAAA,CACA;IACA;IACA;IACAI,aAAA,WAAAA,cAAAC,QAAA,EAAAC,IAAA,EAAAC,QAAA;MACA,KAAAtF,WAAA,GAAAuF,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAH,QAAA;IACA;IACA;IACAI,YAAA,WAAAA,aAAAL,IAAA,EAAAC,QAAA;MACA,KAAAtF,WAAA,GAAAuF,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAH,QAAA;IACA;IACA;IACAK,aAAA,WAAAA,cAAAN,IAAA;MAAA,OAAAjE,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAsE,SAAA;QAAA,IAAAC,aAAA;QAAA,OAAAxE,mBAAA,GAAAI,IAAA,UAAAqE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnE,IAAA,GAAAmE,SAAA,CAAAlE,IAAA;YAAA;cACAgE,aAAA;cAAA,MACAR,IAAA,CAAAD,QAAA,IAAAC,IAAA,CAAAD,QAAA,CAAAS,aAAA;gBAAAE,SAAA,CAAAlE,IAAA;gBAAA;cAAA;cACAgE,aAAA,GAAAR,IAAA,CAAAD,QAAA,CAAAS,aAAA;cAAAE,SAAA,CAAAlE,IAAA;cAAA;YAAA;cAAAkE,SAAA,CAAAlE,IAAA;cAAA,OAEAzG,SAAA;gBAAA4K,GAAA,EAAAX,IAAA,CAAAQ;cAAA;YAAA;cAAAA,aAAA,GAAAE,SAAA,CAAAjE,IAAA;cACA+D,aAAA,GAAAA,aAAA,CAAAxC,IAAA;YAAA;cAEA4C,MAAA,CAAAC,IAAA,CAAAL,aAAA;YAAA;YAAA;cAAA,OAAAE,SAAA,CAAAnD,IAAA;UAAA;QAAA,GAAAgD,QAAA;MAAA;IACA;IACA;IACAxD,yBAAA,WAAAA,0BAAA;MAAA,IAAA+D,MAAA;MAAA,OAAA/E,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA8E,SAAA;QAAA,IAAAjD,GAAA;QAAA,OAAA9B,mBAAA,GAAAI,IAAA,UAAA4E,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1E,IAAA,GAAA0E,SAAA,CAAAzE,IAAA;YAAA;cAAAyE,SAAA,CAAAzE,IAAA;cAAA,OACA1G,yBAAA;YAAA;cAAAgI,GAAA,GAAAmD,SAAA,CAAAxE,IAAA;cACA,IAAAqB,GAAA,CAAAoD,SAAA;gBACAJ,MAAA,CAAAnH,WAAA,GAAAmE,GAAA,CAAAE,IAAA;cACA;gBACA8C,MAAA,CAAA5H,OAAA,CAAAiI,KAAA,CAAArD,GAAA,CAAAsD,OAAA;cACA;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAA1D,IAAA;UAAA;QAAA,GAAAwD,QAAA;MAAA;IACA;IACA;IACAM,YAAA,WAAAA,aAAA;MACA,KAAAvG,SAAA,GAAAoF,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAvF,aAAA;MACA,SAAA7C,UAAA,CAAAC,OAAA;QACA,IAAAqJ,YAAA,OAAAC,MAAA,MAAAvJ,UAAA,CAAAC,OAAA;QACA,KAAA6C,SAAA,QAAAA,SAAA,CAAA0G,MAAA,WAAA3C,IAAA;UACA,OAAAyC,YAAA,CAAAG,IAAA,CAAA5C,IAAA,CAAA5G,OAAA;QACA;MACA;MAEA,SAAAD,UAAA,CAAAE,IAAA;QACA,IAAAwJ,SAAA,OAAAH,MAAA,MAAAvJ,UAAA,CAAAE,IAAA;QACA,KAAA4C,SAAA,QAAAA,SAAA,CAAA0G,MAAA,WAAA3C,IAAA;UACA,OAAA6C,SAAA,CAAAD,IAAA,CAAA5C,IAAA,CAAA3G,IAAA;QACA;MACA;MACA,SAAAF,UAAA,CAAAU,YAAA;QACA,IAAAiJ,iBAAA,OAAAJ,MAAA,MAAAvJ,UAAA,CAAAU,YAAA;QACA,KAAAoC,SAAA,QAAAA,SAAA,CAAA0G,MAAA,WAAA3C,IAAA;UACA,OAAA8C,iBAAA,CAAAF,IAAA,CAAA5C,IAAA,CAAAQ,cAAA;QACA;MACA;MACA,SAAA1D,QAAA;QACA,KAAAiB,KAAA,UAAAiD,OAAA,MAAA/E,SAAA;MACA;QACA,KAAA8B,KAAA,UAAA2B,MAAA,QAAAzD,SAAA;MACA;IACA;IACA;IACA8G,cAAA,WAAAA,eAAA;MACA,KAAA/G,aAAA,QAAA+B,KAAA,UAAA2B,MAAA;MACA,KAAAzD,SAAA,GAAAoF,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAvF,aAAA;MACAgH,OAAA,CAAAC,GAAA,MAAAjH,aAAA;MACAgH,OAAA,CAAAC,GAAA,MAAAhH,SAAA;MACA;IACA;IACA;IACAkC,yBAAA,WAAAA,0BAAA;MAAA,IAAA+E,MAAA;MACApM,yBAAA;QAAAqM,IAAA;MAAA,GAAAnE,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAoD,SAAA;UACAa,MAAA,CAAAtH,eAAA,GAAAqD,GAAA,CAAAE,IAAA;UACA;UACA,IAAA+D,MAAA,CAAAtH,eAAA;YACAsH,MAAA,CAAAE,iBAAA;UACA;QACA;UACAF,MAAA,CAAAG,QAAA;YACAhJ,OAAA,EAAA4E,GAAA,CAAAqE,OAAA;YACAnL,IAAA;UACA;QACA;MACA;IACA;IACAiL,iBAAA,WAAAA,kBAAA;MAAA,IAAAG,MAAA;MACApM,oBAAA;QAAAqM,KAAA;MAAA,GAAAxE,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAoD,SAAA;UACA,IAAAoB,IAAA,GAAAxE,GAAA,CAAAE,IAAA;UACAoE,MAAA,CAAAG,eAAA,CAAAD,IAAA;UACAF,MAAA,CAAAhJ,gBAAA,CAAAhC,IAAA,GAAAkL,IAAA;UACAF,MAAA,CAAA1F,SAAA,WAAAC,CAAA;YACAyF,MAAA,CAAAxF,KAAA,CAAA4F,gBAAA,CAAAC,iBAAA,CAAAH,IAAA;UACA;QACA;UACAF,MAAA,CAAAF,QAAA;YACAhJ,OAAA,EAAA4E,GAAA,CAAAqE,OAAA;YACAnL,IAAA;UACA;QACA;MACA;IACA;IACAuL,eAAA,WAAAA,gBAAAG,IAAA;MAAA,IAAAC,MAAA;MACA,KAAAD,IAAA;MACAA,IAAA,CAAAjE,OAAA,WAAAC,OAAA;QACA,IAAAkE,QAAA,GAAAlE,OAAA,CAAAkE,QAAA;QACA,IAAAlE,OAAA,CAAAV,IAAA,CAAA6E,UAAA,aAAAnE,OAAA,CAAAV,IAAA,CAAA8E,IAAA;UACApE,OAAA,CAAAnF,QAAA;QACA;UACAmF,OAAA,CAAAnF,QAAA;QACA;QACA,IAAAqJ,QAAA,CAAA3E,MAAA;UACA0E,MAAA,CAAAJ,eAAA,CAAAK,QAAA;QACA;MACA;IACA;IACA;IACAG,WAAA,WAAAA,YAAA;MACA,KAAAnI,SAAA;MACA;MACA,KAAA5C,UAAA,CAAAC,OAAA;MACA,KAAAD,UAAA,CAAAE,IAAA;MACA,KAAA0E,KAAA,UAAA2B,MAAA,QAAA1D,aAAA;MACA,KAAAC,SAAA,GAAAoF,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAvF,aAAA;IACA;IACA;IACAmI,YAAA,WAAAA,aAAA;MACA,KAAAd,QAAA;QACAlL,IAAA;QACAkC,OAAA;MACA;IACA;IACA+J,aAAA,WAAAA,cAAA;MACA,KAAA3L,SAAA,SAAAA,SAAA;IACA;IACA4F,SAAA,WAAAA,UAAA;IACAI,OAAA,WAAAA,QAAA;MAAA,IAAA4F,MAAA;MACA,KAAA/K,IAAA,CAAAC,UAAA,QAAA+K,MAAA,CAAAC,KAAA,CAAAhL,UAAA;MACA,KAAAD,IAAA,CAAAE,YAAA,SAAA8K,MAAA,CAAAC,KAAA,CAAA/K,YAAA;MACA;MACA,IAAAgL,IAAA;MACA,IAAAC,MAAA;MACA,SAAA3H,QAAA;QACA0H,IAAA,GAAA/N,qBAAA;QACAgO,MAAA;UACAnF,EAAA,OAAAgF,MAAA,CAAAC,KAAA,CAAAxF;QACA;MACA;QACAyF,IAAA,GAAAxN,gBAAA;QACAyN,MAAA;UACAC,UAAA,OAAAJ,MAAA,CAAAC,KAAA,CAAAhL;QACA;MACA;MACAiL,IAAA,CAAAC,MAAA,EAAAzF,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAoD,SAAA;UACA,IAAAsC,OAAA,GAAA1F,GAAA,CAAAE,IAAA,CAAAwF,OAAA;UACA,IAAAC,GAAA,GAAA3F,GAAA,CAAAE,IAAA,CAAAyF,GAAA;UACA,IACAnL,YAAA,GASAkL,OAAA,CATAlL,YAAA;YACAQ,MAAA,GAQA0K,OAAA,CARA1K,MAAA;YACAJ,YAAA,GAOA8K,OAAA,CAPA9K,YAAA;YACAD,kBAAA,GAMA+K,OAAA,CANA/K,kBAAA;YACAD,iBAAA,GAKAgL,OAAA,CALAhL,iBAAA;YACAK,aAAA,GAIA2K,OAAA,CAJA3K,aAAA;YACAD,aAAA,GAGA4K,OAAA,CAHA5K,aAAA;YACAG,UAAA,GAEAyK,OAAA,CAFAzK,UAAA;YACA2K,MAAA,GACAF,OAAA,CADAE,MAAA;UAEAR,MAAA,CAAA/K,IAAA,CAAAG,YAAA,GAAA4K,MAAA,CAAA3K,OAAA,KAAAyB,IAAA,CAAA1B,YAAA;UACA4K,MAAA,CAAA/K,IAAA,CAAAW,MAAA,GAAAA,MAAA;UACAoK,MAAA,CAAA/K,IAAA,CAAAO,YAAA,GAAAA,YAAA;UACAwK,MAAA,CAAA/K,IAAA,CAAAK,iBAAA,GAAAA,iBAAA;UACA0K,MAAA,CAAA/K,IAAA,CAAAM,kBAAA,GAAAA,kBAAA;UACA,IAAAyK,MAAA,CAAA/K,IAAA,CAAAM,kBAAA;YACAyK,MAAA,CAAAS,gBAAA;UACA;UACAT,MAAA,CAAA/K,IAAA,CAAAK,iBAAA,GAAAA,iBAAA;UACA0K,MAAA,CAAA/K,IAAA,CAAAU,aAAA,GAAAA,aAAA;UACAqK,MAAA,CAAA/K,IAAA,CAAAS,aAAA,GAAAA,aAAA;UACAsK,MAAA,CAAA/K,IAAA,CAAAuL,MAAA,GAAAA,MAAA;;UAEA;UACA,IAAAE,OAAA,GAAAH,GAAA,CAAAI,GAAA,WAAAC,GAAA,EAAA9E,KAAA;YACA8E,GAAA,CAAA9E,KAAA,GAAA3I,MAAA;YACAyN,GAAA,CAAAC,kBAAA,GAAAD,GAAA,CAAAE,aAAA,GACAF,GAAA,CAAAE,aAAA,SAAAF,GAAA,CAAAG,YAAA,GACA;YACA;YACA;YACA;YACA;YACA;YACA,OAAAH,GAAA;UACA;UAEAZ,MAAA,CAAAxG,SAAA,WAAAC,CAAA;YACAuG,MAAA,CAAAtG,KAAA,UAAA2B,MAAA,GAAA2B,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAwD,OAAA;YACAV,MAAA,CAAApI,SAAA,GAAAoF,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAwD,OAAA;YACAV,MAAA,CAAArI,aAAA,GAAAqF,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAwD,OAAA;UACA;UACA,IAAA7K,UAAA;YACAmK,MAAA,CAAA/K,IAAA,CAAAY,UAAA,GAAAA,UAAA;YACA,IAAAmL,aAAA,GAAAnL,UAAA,CAAAoL,KAAA;YACAD,aAAA,CAAAzF,OAAA,WAAAI,IAAA;cACA,IAAAuF,OAAA,GACAvF,IAAA,CAAAwF,OAAA,qBACAxF,IAAA,CAAAyF,SAAA,IAAAzF,IAAA,CAAA0F,WAAA,iBACA1F,IAAA;cACA,IAAA2F,QAAA,GAAAC,SAAA,CAAAL,OAAA,CAAAE,SAAA,CAAAF,OAAA,CAAAG,WAAA;cACA,IAAAG,cAAA;cACAA,cAAA,CAAAC,IAAA,GAAAH,QAAA;cACAE,cAAA,CAAA/D,GAAA,GAAAyD,OAAA;cACAM,cAAA,CAAAlE,aAAA,GAAA4D,OAAA;cACAlB,MAAA,CAAAxI,YAAA,CAAAkK,IAAA,CAAAF,cAAA;cACAxB,MAAA,CAAAvI,WAAA,CAAAiK,IAAA,CAAAF,cAAA;YACA;UACA;QACA;UACAxB,MAAA,CAAAhB,QAAA;YACAhJ,OAAA,EAAA4E,GAAA,CAAAqE,OAAA;YACAnL,IAAA;UACA;QACA;MACA;IACA;IACAoG,kBAAA,WAAAA,mBAAA;MAAA,IAAAyH,MAAA;MAAA,OAAA9I,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6I,SAAA;QAAA,IAAAhH,GAAA;QAAA,OAAA9B,mBAAA,GAAAI,IAAA,UAAA2I,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzI,IAAA,GAAAyI,SAAA,CAAAxI,IAAA;YAAA;cAAAwI,SAAA,CAAAxI,IAAA;cAAA,OACA/G,kBAAA;YAAA;cAAAqI,GAAA,GAAAkH,SAAA,CAAAvI,IAAA;cACA,IAAAqB,GAAA,CAAAoD,SAAA;gBACA2D,MAAA,CAAAhN,WAAA,GAAAiG,GAAA,CAAAE,IAAA;cACA;gBACA6G,MAAA,CAAA3L,OAAA,CAAAiI,KAAA,CAAArD,GAAA,CAAAsD,OAAA;cACA;YAAA;YAAA;cAAA,OAAA4D,SAAA,CAAAzH,IAAA;UAAA;QAAA,GAAAuH,QAAA;MAAA;IACA;IACAnB,gBAAA,WAAAA,iBAAA;MACA,KAAAxL,IAAA,CAAAS,aAAA;MACA,SAAAT,IAAA,CAAAM,kBAAA;QACA,KAAAwM,eAAA,MAAA9M,IAAA,CAAAM,kBAAA;MACA;IACA;IACAwM,eAAA,WAAAA,gBAAA9G,EAAA;MAAA,IAAA+G,OAAA;MACA1P,WAAA;QACA2P,QAAA;QACAC,YAAA,EAAAjH;MACA,GAAAN,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAoD,SAAA;UACAgE,OAAA,CAAAvN,iBAAA,GAAAmG,GAAA,CAAAE,IAAA,CAAAA,IAAA;QACA;UACAkH,OAAA,CAAAhD,QAAA;YACAhJ,OAAA,EAAA4E,GAAA,CAAAqE,OAAA;YACAnL,IAAA;UACA;QACA;MACA;IACA;IACAiG,gCAAA,WAAAA,iCAAA;MAAA,IAAAoI,OAAA;MAAA,OAAAtJ,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAqJ,SAAA;QAAA,IAAAxH,GAAA;QAAA,OAAA9B,mBAAA,GAAAI,IAAA,UAAAmJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjJ,IAAA,GAAAiJ,SAAA,CAAAhJ,IAAA;YAAA;cAAAgJ,SAAA,CAAAhJ,IAAA;cAAA,OACApH,gCAAA;gBACAqQ,SAAA,EAAAJ,OAAA,CAAAK,iBAAA,CAAAvH;cACA;YAAA;cAFAL,GAAA,GAAA0H,SAAA,CAAA/I,IAAA;cAGA,IAAAqB,GAAA,CAAAoD,SAAA;gBACAmE,OAAA,CAAAzN,cAAA,GAAAkG,GAAA,CAAAE,IAAA;gBACAqH,OAAA,CAAAlN,IAAA,CAAAM,kBAAA,GAAAqF,GAAA,CAAAE,IAAA,CAAAc,IAAA,CACA,UAAAD,IAAA;kBAAA,OAAAA,IAAA,CAAA8G,kBAAA;gBAAA,CACA,IACA7H,GAAA,CAAAE,IAAA,CAAAc,IAAA,WAAAD,IAAA;kBAAA,OAAAA,IAAA,CAAA8G,kBAAA;gBAAA,GAAAxH,EAAA,GACA;gBACAkH,OAAA,CAAA1B,gBAAA;cACA;gBACA0B,OAAA,CAAAnM,OAAA,CAAAiI,KAAA,CAAArD,GAAA,CAAAsD,OAAA;cACA;YAAA;YAAA;cAAA,OAAAoE,SAAA,CAAAjI,IAAA;UAAA;QAAA,GAAA+H,QAAA;MAAA;IACA;IACA;AACA;AACA;IACAnI,UAAA,WAAAA,WAAA;MAAA,IAAAyI,OAAA;MACA3Q,kBAAA;QACA4Q,IAAA;QACAV,QAAA;MACA,GAAAtH,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAoD,SAAA;UACA0E,OAAA,CAAAnO,cAAA,GAAAqG,GAAA,CAAAE,IAAA,CAAAA,IAAA;QACA;UACA4H,OAAA,CAAA1D,QAAA;YACAhJ,OAAA,EAAA4E,GAAA,CAAAqE,OAAA;YACAnL,IAAA;UACA;QACA;MACA;IACA;IAEA;AACA;AACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;AACA;AACA;IACAqG,eAAA,WAAAA,gBAAA;MAAA,IAAAyI,OAAA;MACAtP,eAAA,GAAAqH,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAoD,SAAA;UACA4E,OAAA,CAAA/N,eAAA,GAAA+F,GAAA,CAAAE,IAAA;QACA;UACA8H,OAAA,CAAA5D,QAAA;YACAhJ,OAAA,EAAA4E,GAAA,CAAAqE,OAAA;YACAnL,IAAA;UACA;QACA;MACA;IACA;IAEA+O,eAAA,WAAAA,gBAAAjC,GAAA;MACA,KAAAkC,UAAA,GAAAlC,GAAA;MACA,KAAAmC,eAAA;IACA;IACAA,eAAA,WAAAA,gBAAAC,QAAA;MACA,KAAAjM,gBAAA;MACA,KAAAE,MAAA;MACA,KAAAD,KAAA;MACA,CAAAgM,QAAA,UAAAF,UAAA;MACA,KAAAzL,aAAA;IACA;IACA4L,YAAA,WAAAA,aAAAC,KAAA;MAAA,IAAAC,SAAA,GAAAD,KAAA,CAAAC,SAAA;QAAAC,QAAA,GAAAF,KAAA,CAAAE,QAAA;MACA,SAAAN,UAAA;QACA,KAAAA,UAAA,CAAAO,kBAAA,GAAAF,SAAA,CAAAlI,EAAA;QACA,KAAA6H,UAAA,CAAAQ,gBAAA,GAAAF,QAAA,CAAAnI,EAAA;QACA,KAAAD,IAAA,MAAA8H,UAAA,0BAAAK,SAAA,CAAAI,YAAA;QACA,KAAAvI,IAAA,MAAA8H,UAAA,wBAAAM,QAAA,CAAAG,YAAA;QACA,KAAAvI,IAAA,CACA,KAAA8H,UAAA,EACA,6BACAK,SAAA,CAAAI,YAAA,SAAAH,QAAA,CAAAG,YACA;MACA;IACA;IACAC,aAAA,WAAAA,cAAAtP,IAAA;MAAA,IAAAuP,OAAA;MACA,SAAAX,UAAA;QACA5O,IAAA,CAAAqH,OAAA,WAAAI,IAAA;UACA8H,OAAA,CAAAzI,IAAA,CAAAyI,OAAA,CAAAX,UAAA,EAAAnH,IAAA,CAAA+H,GAAA,EAAA/H,IAAA,CAAAnB,GAAA;QACA;MACA;QACA,KAAAhG,iBAAA,CAAA+G,OAAA,WAAAC,OAAA,EAAAC,GAAA;UACAvH,IAAA,CAAAqH,OAAA,WAAAI,IAAA;YACA8H,OAAA,CAAAzI,IAAA,CAAAQ,OAAA,EAAAG,IAAA,CAAA+H,GAAA,EAAA/H,IAAA,CAAAnB,GAAA;UACA;QACA;MACA;MACA,KAAAmJ,WAAA;IACA;IACAC,cAAA,WAAAA,eAAAhD,GAAA;MAAA,IAAAiD,OAAA;MACA,KAAAf,UAAA,GAAAlC,GAAA;MACA,KAAA7J,gBAAA;MACA,KAAAE,MAAA;MACA,KAAAD,KAAA;MACA,KAAAK,aAAA;MACA,KAAAmC,SAAA,WAAAC,CAAA;QACAoK,OAAA,CAAAnK,KAAA,YAAAoK,SAAA,CAAAlD,GAAA;MACA;IACA;IACAmD,WAAA,WAAAA,YAAAC,KAAA;MAAA,IAAAlQ,IAAA,GAAAkQ,KAAA,CAAAlQ,IAAA;QAAA0G,GAAA,GAAAwJ,KAAA,CAAAxJ,GAAA;MACA,IAAA1G,IAAA;QACA,KAAAkH,IAAA,MAAA8H,UAAA,kBAAAtI,GAAA;MACA;QACA,KAAAQ,IAAA,MAAA8H,UAAA,kBAAAtI,GAAA,CAAAyJ,YAAA;QACA,KAAAnB,UAAA,CAAAoB,UAAA,GAAA1J,GAAA,CAAA0J,UAAA;MACA;IACA;IACA;IACAC,UAAA,WAAAA,WAAAC,CAAA;MACA,IAAAA,CAAA;QACA,KAAA9M,OAAA;QACA,KAAAO,WAAA;QACA,KAAAC,WAAA;QACA,KAAAC,WAAA;QACA,KAAAC,WAAA;QACA,KAAAJ,SAAA;QACA,KAAA8B,KAAA,UAAA2B,MAAA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAgJ,UAAA,WAAAA,WAAAC,IAAA,EAAAC,IAAA;MACA,KAAAjN,OAAA,GAAAgN,IAAA,IAAAhN,OAAA;MACAgN,IAAA,CAAA3D,GAAA,WAAAhF,IAAA,EAAAG,KAAA;QACAH,IAAA,CAAAG,KAAA,GAAA3I,MAAA;QACAwI,IAAA,CAAAlG,iBAAA,GAAAkG,IAAA,CAAA6I,YAAA;MACA;MACA,KAAA9K,KAAA,UAAA+K,OAAA,CAAAH,IAAA;MACA,IAAAjJ,MAAA,QAAA3B,KAAA,CAAA4B,KAAA,CAAAD,MAAA;MACA,KAAAzD,SAAA,GAAAoF,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAA7B,MAAA;MACA,KAAA1D,aAAA,GAAAqF,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAA7B,MAAA;MACA,IAAAkJ,IAAA;QACA,KAAAtP,IAAA,CAAAU,aAAA,GAAA4O,IAAA,CAAAG,YAAA;QACA,KAAAzP,IAAA,CAAAK,iBAAA,GAAAiP,IAAA,CAAAI,eAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAN,IAAA;MACA,KAAA5K,KAAA,UAAAkL,UAAA,CAAAN,IAAA;IACA;IACAO,UAAA,WAAAA,WAAAC,KAAA;MAAA,IAAAC,IAAA,GAAAD,KAAA,CAAAC,IAAA;QAAA9J,EAAA,GAAA6J,KAAA,CAAA7J,EAAA;MACA,KAAA6H,UAAA,CAAAiC,IAAA,GAAAA,IAAA;MACA,KAAAjC,UAAA,CAAAkC,KAAA,GAAA/J,EAAA;MACA,KAAA6H,UAAA,CAAAmB,YAAA;IACA;IACAgB,iBAAA,WAAAA,kBAAAjO,KAAA,EAAAkO,SAAA;MACA,KAAAlO,KAAA,GAAAA,KAAA;MACA,KAAAD,gBAAA,GAAAmO,SAAA;MACA,KAAA7N,aAAA;IACA;IACA8N,YAAA,WAAAA,aAAA;MAAA,IAAAC,iBAAA;MACA,IAAA/J,MAAA,QAAA3B,KAAA,CAAA4B,KAAA,CAAAD,MAAA;MACA,KAAA7G,iBAAA,CAAA+G,OAAA,WAAAC,OAAA,EAAAC,GAAA;QACA,IAAAM,CAAA,GAAAV,MAAA,CAAAW,SAAA,WAAAH,CAAA;UAAA,OAAAA,CAAA,CAAAC,KAAA,KAAAN,OAAA,CAAAM,KAAA;QAAA;QACAT,MAAA,CAAAgK,MAAA,CAAAtJ,CAAA;MACA;MACA,KAAAnE,SAAA,GAAAoF,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAA7B,MAAA;MACA,KAAA1D,aAAA,GAAAqF,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAA7B,MAAA;MACA,KAAA7G,iBAAA;MACA,CAAA4Q,iBAAA,QAAA1L,KAAA,CAAA4B,KAAA,cAAA8J,iBAAA,gBAAAA,iBAAA,GAAAA,iBAAA,CAAA1L,KAAA,cAAA0L,iBAAA,eAAAA,iBAAA,CAAAE,MAAA,CAAAC,gBAAA;IACA;IACA5B,WAAA,WAAAA,YAAA/C,GAAA;MACA,KAAAxJ,WAAA;MACA,KAAAC,aAAA;IACA;IACAmO,cAAA,WAAAA,eAAAC,KAAA;MACA;MACA,KAAAjR,iBAAA,GAAAiR,KAAA,CAAAC,OAAA;IACA;IACAC,YAAA,WAAAA,aAAA/E,GAAA;IACA;IACAgF,aAAA,WAAAA,cAAA;MACA,IAAAC,MAAA,GAAApU,SAAA,MAAAiI,KAAA,UAAA2B,MAAA;MACA,KAAAwK,MAAA,CAAA9K,MAAA;QACA,KAAAiE,QAAA;UACAhJ,OAAA;UACAlC,IAAA;QACA;QACA;UACAgS,MAAA;QACA;MACA;MACA,IAAAC,aAAA,QAAAC,OAAA,CAAAH,MAAA;QAAAC,MAAA,GAAAC,aAAA,CAAAD,MAAA;QAAAG,GAAA,GAAAF,aAAA,CAAAE,GAAA;MACA,KAAAH,MAAA;QACA,KAAA9G,QAAA;UACAhJ,OAAA,KAAAkQ,MAAA,CAAAD,GAAA;UACAnS,IAAA;QACA;QACA;UACAgS,MAAA;QACA;MACA;MACA;QACA5R,IAAA,EAAA2R,MAAA;QACAC,MAAA;MACA;IACA;IACAK,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAzS,IAAA;MACA,GACA6G,IAAA;QACAyL,OAAA,CAAAI,SAAA;MACA,GACAC,KAAA,WAAAC,CAAA;QACA/H,OAAA,CAAAC,GAAA,UAAA8H,CAAA;QAEAN,OAAA,CAAApH,QAAA;UACAlL,IAAA;UACAkC,OAAA;QACA;MACA;IACA;IACA;IACA2Q,YAAA,WAAAA,aAAA/F,GAAA;MAAA,IAAAgG,OAAA;MACA,KAAAP,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAzS,IAAA;MACA,GACA6G,IAAA;QACAiM,OAAA,CAAAJ,SAAA;MACA,GACAC,KAAA,WAAAC,CAAA;QACAE,OAAA,CAAA5H,QAAA;UACAlL,IAAA;UACAkC,OAAA;QACA;MACA;IACA;IACA;IACAwQ,SAAA,WAAAA,UAAA;MAAA,IAAAK,OAAA;MAAA,IAAA/S,IAAA,GAAAgT,SAAA,CAAA/L,MAAA,QAAA+L,SAAA,QAAA7S,SAAA,GAAA6S,SAAA;MACA,IAAAC,mBAAA,QAAAnB,aAAA;QAAA1R,IAAA,GAAA6S,mBAAA,CAAA7S,IAAA;QAAA4R,MAAA,GAAAiB,mBAAA,CAAAjB,MAAA;MACA,KAAAA,MAAA;MACA,KAAApM,KAAA,SAAAsN,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;QACAJ,OAAA,CAAA3P,WAAA;QACA;QACA,IAAAgQ,cAAA;QACA,IAAAL,OAAA,CAAApP,WAAA,CAAAsD,MAAA;UACA8L,OAAA,CAAApP,WAAA,CAAA8D,OAAA,WAAAI,IAAA;YACAuL,cAAA,CAAAxF,IAAA,CACA/F,IAAA,CAAAkB,QAAA,IAAAlB,IAAA,CAAAkB,QAAA,CAAAS,aAAA,GACA3B,IAAA,CAAAkB,QAAA,CAAAS,aAAA,GACA3B,IAAA,CAAA2B,aACA;UACA;QACA;QACAuJ,OAAA,CAAA5R,IAAA,CAAAY,UAAA,GAAAqR,cAAA,CAAAC,IAAA;QACA;QACAN,OAAA,CAAA5R,IAAA,CAAAuL,MAAA,GAAA1M,IAAA;QACA,IAAAmB,IAAA,GAAAmS,aAAA,KAAAP,OAAA,CAAA5R,IAAA;QACA;QACA;QACA,IAAAkL,IAAA;QAEA,IAAA0G,OAAA,CAAApO,QAAA;UACA0H,IAAA,GAAAhO,kBAAA;QACA;UACAgO,IAAA,GAAAzN,YAAA;QACA;QACAyN,IAAA;UACAG,OAAA,EAAArL,IAAA;UACAsL,GAAA,EAAArM;QACA,GAAAyG,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAoD,SAAA;YACA6I,OAAA,CAAA7H,QAAA;cACAhJ,OAAA;cACAlC,IAAA;YACA;YACA+S,OAAA,CAAAQ,SAAA;UACA;YACAR,OAAA,CAAA7H,QAAA;cACAhJ,OAAA,EAAA4E,GAAA,CAAAqE,OAAA;cACAnL,IAAA;YACA;UACA;UACA+S,OAAA,CAAA3P,WAAA;QACA;MACA;IACA;IACA8O,OAAA,WAAAA,QAAA1B,IAAA;MAAA,IAAAgD,OAAA;MACA,IAAAC,KAAA;MACA,IAAAC,OAAA,cAAAvT,SAAA;MACA,SAAAwE,QAAA;QACA+O,OAAA,CAAA9F,IAAA;QACA6F,KAAA;MACA;QACAA,KAAA;MACA;MACA,SAAAxL,CAAA,MAAAA,CAAA,GAAAuI,IAAA,CAAAvJ,MAAA,EAAAgB,CAAA;QACA,IAAAJ,IAAA,GAAA2I,IAAA,CAAAvI,CAAA;QAAA,IAAA0L,KAAA,YAAAA,MAAA,EACA;YACA,IAAAC,CAAA,GAAAH,KAAA,CAAAI,CAAA;YACA,IAAAH,OAAA,CAAAI,QAAA,CAAAjM,IAAA,CAAA+L,CAAA;cACA,IAAAG,OAAA,GAAAP,OAAA,CAAA5N,KAAA,CAAA4B,KAAA,CAAAwM,WAAA;cACA,IAAAtM,OAAA,GAAAqM,OAAA,CAAAjM,IAAA,WAAAC,CAAA;gBAAA,OAAAA,CAAA,CAAAkM,IAAA,KAAAL,CAAA;cAAA;cAAA;gBAAA7L,CAAA,EACA;kBACAiK,MAAA;kBACAG,GAAA,EAAAzK,OAAA,aAAAA,OAAA,uBAAAA,OAAA,CAAA+H;gBACA;cAAA;YACA;UACA;UAAAyE,IAAA;QAVA,SAAAL,CAAA,MAAAA,CAAA,GAAAJ,KAAA,CAAAxM,MAAA,EAAA4M,CAAA;UAAAK,IAAA,GAAAP,KAAA;UAAA,IAAAO,IAAA,SAAAA,IAAA,CAAAnM,CAAA;QAAA;QAWA,OAAAF,IAAA,CAAAsM,UAAA;QACA,OAAAtM,IAAA,CAAAmF,aAAA;QACA,OAAAnF,IAAA,CAAAoF,YAAA;MACA;MACA;QACA+E,MAAA;QACAG,GAAA;MACA;IACA;IACAiC,aAAA,WAAAA,cAAAtH,GAAA;MACA,KAAAxJ,WAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAiQ,SAAA,WAAAA,UAAA;MACA9V,YAAA,MAAA4W,MAAA,OAAAlI,MAAA;IACA;IACAmI,YAAA,WAAAA,aAAA5T,iBAAA;MACA,KAAAA,iBAAA,GAAAA,iBAAA;IACA;IACA;IACAa,OAAA,WAAAA,QAAAnB,IAAA;MACA,IAAAmU,IAAA,GAAAnU,IAAA,QAAA4C,IAAA;MACA,IAAAwR,IAAA,GAAAD,IAAA,CAAAE,WAAA;MACA,IAAAC,KAAA,WAAAH,IAAA,CAAAI,QAAA,SAAAC,KAAA;MACA,IAAAC,GAAA,UAAAN,IAAA,CAAAhT,OAAA,IAAAqT,KAAA;MACA,UAAAxC,MAAA,CAAAoC,IAAA,OAAApC,MAAA,CAAAsC,KAAA,OAAAtC,MAAA,CAAAyC,GAAA;IACA;EACA;AACA", "ignoreList": []}]}
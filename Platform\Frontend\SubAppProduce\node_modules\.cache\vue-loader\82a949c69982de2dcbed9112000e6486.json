{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\component\\Add.vue?vue&type=template&id=03a8e043&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\component\\Add.vue", "mtime": 1756109946517}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxlbC1mb3JtIHJlZj0iZm9ybSIgOm1vZGVsPSJmb3JtIiA6cnVsZXM9InJ1bGVzIiBsYWJlbC13aWR0aD0iMTIwcHgiPgogIDxlbC1mb3JtLWl0ZW0gOmxhYmVsPSJgJHtsZXZlbE5hbWV95aSn57G75ZCN56ewYCIgcHJvcD0iTmFtZSI+CiAgICA8ZWwtaW5wdXQgdi1tb2RlbC50cmltPSJmb3JtLk5hbWUiIGNsZWFyYWJsZSBtYXhsZW5ndGg9IjUwIiAvPgogIDwvZWwtZm9ybS1pdGVtPgogIDxlbC1mb3JtLWl0ZW0gOmxhYmVsPSJgJHtsZXZlbE5hbWV95aSn57G757yW5Y+3YCIgcHJvcD0iQ29kZSI+CiAgICA8ZWwtaW5wdXQgdi1tb2RlbC50cmltPSJmb3JtLkNvZGUiIGNsZWFyYWJsZSBtYXhsZW5ndGg9IjUwIiAvPgogIDwvZWwtZm9ybS1pdGVtPgogIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IueUn+S6p+WRqOacnyIgcHJvcD0iTGVhZF9UaW1lIj4KICAgIDxlbC1pbnB1dC1udW1iZXIgdi1tb2RlbC5udW1iZXI9ImZvcm0uTGVhZF9UaW1lIiBjbGFzcz0iY3MtbnVtYmVyLWJ0bi1oaWRkZW4gdzEwMCIgY2xlYXJhYmxlIC8+CiAgPC9lbC1mb3JtLWl0ZW0+CiAgPGVsLWZvcm0taXRlbSB2LWlmPSJpc0NvbXAiIGxhYmVsPSLnm7Tlj5Hku7YiIHByb3A9IklzX0NvbXBvbmVudCI+CiAgICA8ZWwtcmFkaW8tZ3JvdXAgdi1tb2RlbD0iZm9ybS5Jc19Db21wb25lbnQiPgogICAgICA8ZWwtcmFkaW8gOmxhYmVsPSJ0cnVlIj7lkKY8L2VsLXJhZGlvPgogICAgICA8ZWwtcmFkaW8gOmxhYmVsPSJmYWxzZSI+5pivPC9lbC1yYWRpbz4KICAgIDwvZWwtcmFkaW8tZ3JvdXA+CiAgPC9lbC1mb3JtLWl0ZW0+CiAgPGVsLWZvcm0taXRlbSBzdHlsZT0idGV4dC1hbGlnbjogcmlnaHQiPgogICAgPGVsLWJ1dHRvbiBAY2xpY2s9IiRlbWl0KCdjbG9zZScpIj7lj5bmtog8L2VsLWJ1dHRvbj4KICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgOmxvYWRpbmc9ImJ0bkxvYWRpbmciIEBjbGljaz0ic3VibWl0Ij7kv53lrZg8L2VsLWJ1dHRvbj4KICA8L2VsLWZvcm0taXRlbT4KPC9lbC1mb3JtPgo="}, null]}
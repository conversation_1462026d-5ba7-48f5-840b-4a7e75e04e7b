{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\component\\Add.vue?vue&type=template&id=03a8e043&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\component\\Add.vue", "mtime": 1757468112469}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxlbC1mb3JtIHJlZj0iZm9ybSIgOm1vZGVsPSJmb3JtIiA6cnVsZXM9InJ1bGVzIiBsYWJlbC13aWR0aD0iMTIwcHgiPgogIDxlbC1mb3JtLWl0ZW0gOmxhYmVsPSJgJHtsZXZlbE5hbWV95aSn57G75ZCN56ewYCIgcHJvcD0iTmFtZSI+CiAgICA8ZWwtaW5wdXQgdi1tb2RlbC50cmltPSJmb3JtLk5hbWUiIGNsZWFyYWJsZSBtYXhsZW5ndGg9IjUwIiAvPgogIDwvZWwtZm9ybS1pdGVtPgogIDxlbC1mb3JtLWl0ZW0gOmxhYmVsPSJgJHtsZXZlbE5hbWV95aSn57G757yW5Y+3YCIgcHJvcD0iQ29kZSI+CiAgICA8ZWwtaW5wdXQgdi1tb2RlbC50cmltPSJmb3JtLkNvZGUiIGNsZWFyYWJsZSBtYXhsZW5ndGg9IjUwIiAvPgogIDwvZWwtZm9ybS1pdGVtPgogIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IueUn+S6p+WRqOacnyIgcHJvcD0iTGVhZF9UaW1lIj4KICAgIDxlbC1pbnB1dC1udW1iZXIgdi1tb2RlbC5udW1iZXI9ImZvcm0uTGVhZF9UaW1lIiBjbGFzcz0iY3MtbnVtYmVyLWJ0bi1oaWRkZW4gdzEwMCIgY2xlYXJhYmxlIC8+CiAgPC9lbC1mb3JtLWl0ZW0+CiAgPGVsLWZvcm0taXRlbSB2LWlmPSJzaG93RGlyZWN0IiBsYWJlbD0i55u05Y+R5Lu2IiBwcm9wPSJJc19Db21wb25lbnQiPgogICAgPGVsLXJhZGlvLWdyb3VwIHYtbW9kZWw9ImZvcm0uSXNfQ29tcG9uZW50Ij4KICAgICAgPGVsLXJhZGlvIDpsYWJlbD0idHJ1ZSI+5ZCmPC9lbC1yYWRpbz4KICAgICAgPGVsLXJhZGlvIDpsYWJlbD0iZmFsc2UiPuaYrzwvZWwtcmFkaW8+CiAgICA8L2VsLXJhZGlvLWdyb3VwPgogIDwvZWwtZm9ybS1pdGVtPgogIDxlbC1mb3JtLWl0ZW0gc3R5bGU9InRleHQtYWxpZ246IHJpZ2h0Ij4KICAgIDxlbC1idXR0b24gQGNsaWNrPSIkZW1pdCgnY2xvc2UnKSI+5Y+W5raIPC9lbC1idXR0b24+CiAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIDpsb2FkaW5nPSJidG5Mb2FkaW5nIiBAY2xpY2s9InN1Ym1pdCI+5L+d5a2YPC9lbC1idXR0b24+CiAgPC9lbC1mb3JtLWl0ZW0+CjwvZWwtZm9ybT4K"}, null]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\CheckItem.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\CheckItem.vue", "mtime": 1757468112635}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetCheckItemList", "DeleteCheckItem", "props", "checkType", "type", "Object", "default", "data", "tbLoading", "tbData", "watch", "handler", "newName", "old<PERSON>ame", "getCheckItemList", "deep", "mounted", "methods", "_this", "check_object_id", "Id", "Bom_Level", "Code", "then", "res", "IsSucceed", "Data", "$message", "message", "Message", "removeEvent", "row", "_this2", "$confirm", "confirmButtonText", "cancelButtonText", "id", "catch", "editEvent", "console", "log", "$emit"], "sources": ["src/views/PRO/factoryQuality/checkoutGroup/components/CheckItem.vue"], "sourcesContent": ["<template>\r\n  <div style=\"height: calc(100vh - 300px)\">\r\n    <vxe-table\r\n      v-loading=\"tbLoading\"\r\n      :empty-render=\"{name: 'NotData'}\"\r\n      show-header-overflow\r\n      element-loading-spinner=\"el-icon-loading\"\r\n      element-loading-text=\"拼命加载中\"\r\n      empty-text=\"暂无数据\"\r\n      height=\"100%\"\r\n      align=\"left\"\r\n      stripe\r\n      :data=\"tbData\"\r\n      resizable\r\n      :auto-resize=\"true\"\r\n      class=\"cs-vxe-table\"\r\n      :tooltip-config=\"{ enterable: true }\"\r\n    >\r\n      <vxe-column\r\n        show-overflow=\"tooltip\"\r\n        sortable\r\n        field=\"Check_Content\"\r\n        title=\"检查项内容\"\r\n        width=\"calc(100vh-200px)/2\"\r\n      />\r\n      <vxe-column\r\n        show-overflow=\"tooltip\"\r\n        sortable\r\n        field=\"Eligibility_Criteria\"\r\n        title=\"合格标准\"\r\n        width=\"calc(100vh-200px)/2\"\r\n      />\r\n      <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" align=\"center\" show-overflow>\r\n        <template #default=\"{ row }\">\r\n          <el-button type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\r\n          <el-divider direction=\"vertical\" />\r\n          <el-button type=\"text\" @click=\"removeEvent(row)\">删除</el-button>\r\n        </template>\r\n      </vxe-column>\r\n    </vxe-table>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetCheckItemList } from '@/api/PRO/factorycheck'\r\nimport { DeleteCheckItem } from '@/api/PRO/factorycheck'\r\n\r\nexport default {\r\n  props: {\r\n    checkType: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      tbLoading: false,\r\n      tbData: []\r\n    }\r\n  },\r\n  watch: {\r\n    checkType: {\r\n      handler(newName, oldName) {\r\n        this.checkType = newName\r\n        this.getCheckItemList()\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getCheckItemList()\r\n  },\r\n  methods: {\r\n    getCheckItemList() {\r\n      this.tbLoading = true\r\n      GetCheckItemList({ check_object_id: this.checkType.Id, Bom_Level: this.checkType.Code }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data\r\n          this.tbLoading = false\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n          this.tbLoading = false\r\n        }\r\n      })\r\n    },\r\n    removeEvent(row) {\r\n      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          DeleteCheckItem({ id: row.Id }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                type: 'success',\r\n                message: '删除成功!'\r\n              })\r\n              this.getCheckItemList()\r\n            } else {\r\n              this.$message({\r\n                type: 'error',\r\n                message: res.Message\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n    editEvent(row) {\r\n      // 获取每行内容\r\n      console.log('row', row)\r\n      this.$emit('ItemEdit', row)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,SAAAA,gBAAA;AACA,SAAAC,eAAA;AAEA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,MAAA;IACA;EACA;EACAC,KAAA;IACAP,SAAA;MACAQ,OAAA,WAAAA,QAAAC,OAAA,EAAAC,OAAA;QACA,KAAAV,SAAA,GAAAS,OAAA;QACA,KAAAE,gBAAA;MACA;MACAC,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAF,gBAAA;EACA;EACAG,OAAA;IACAH,gBAAA,WAAAA,iBAAA;MAAA,IAAAI,KAAA;MACA,KAAAV,SAAA;MACAR,gBAAA;QAAAmB,eAAA,OAAAhB,SAAA,CAAAiB,EAAA;QAAAC,SAAA,OAAAlB,SAAA,CAAAmB;MAAA,GAAAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAP,KAAA,CAAAT,MAAA,GAAAe,GAAA,CAAAE,IAAA;UACAR,KAAA,CAAAV,SAAA;QACA;UACAU,KAAA,CAAAS,QAAA;YACAvB,IAAA;YACAwB,OAAA,EAAAJ,GAAA,CAAAK;UACA;UACAX,KAAA,CAAAV,SAAA;QACA;MACA;IACA;IACAsB,WAAA,WAAAA,YAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACA/B,IAAA;MACA,GACAmB,IAAA;QACAtB,eAAA;UAAAmC,EAAA,EAAAL,GAAA,CAAAX;QAAA,GAAAG,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAO,MAAA,CAAAL,QAAA;cACAvB,IAAA;cACAwB,OAAA;YACA;YACAI,MAAA,CAAAlB,gBAAA;UACA;YACAkB,MAAA,CAAAL,QAAA;cACAvB,IAAA;cACAwB,OAAA,EAAAJ,GAAA,CAAAK;YACA;UACA;QACA;MACA,GACAQ,KAAA;QACAL,MAAA,CAAAL,QAAA;UACAvB,IAAA;UACAwB,OAAA;QACA;MACA;IACA;IACAU,SAAA,WAAAA,UAAAP,GAAA;MACA;MACAQ,OAAA,CAAAC,GAAA,QAAAT,GAAA;MACA,KAAAU,KAAA,aAAAV,GAAA;IACA;EACA;AACA", "ignoreList": []}]}
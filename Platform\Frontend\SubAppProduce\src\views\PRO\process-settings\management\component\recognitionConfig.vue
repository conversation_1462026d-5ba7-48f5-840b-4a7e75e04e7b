<template>
  <div class="form-recognition-wrapper">
    <div class="form-recognition-tabs">
      <el-tabs v-model="bomActiveName">
        <el-tab-pane v-for="(item, index) in bomLevel.list" :key="index" :label="item.Display_Name" :name="item.Code" />
      </el-tabs>
    </div>
    <div>
      <comp-recognition-config v-if="bomActiveName === '-1'" @close="handleClose" />
      <part-recognition-config v-else-if="bomActiveName === '0'" @close="handleClose" />
      <unit-part-recognition-config v-else :level="bomActiveName" @close="handleClose" />
    </div>
  </div>
</template>

<script>
import { GetBOMInfo } from '@/views/PRO/bom-setting/utils'
import compRecognitionConfig from './compRecognitionConfig'
import partRecognitionConfig from './partRecognitionConfig'
import unitPartRecognitionConfig from './unitPartRecognitionConfig'

export default {
  components: {
    compRecognitionConfig,
    partRecognitionConfig,
    unitPartRecognitionConfig
  },
  data() {
    return {
      bomLevel: [],
      bomActiveName: '',
      btnLoading: false
    }
  },
  async mounted() {
    this.bomLevel = await GetBOMInfo()
    this.bomActiveName = this.bomLevel.list[0].Code
    console.log('this.bomLevel', this.bomLevel)
  },
  methods: {
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>

<style scoped lang="scss">
@import "~@/styles/mixin.scss";
.form-recognition-wrapper {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-height: 70vh;
  .form-recognition-tabs {

  }
}
</style>

{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\ship\\component\\AddEdit.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\ship\\component\\AddEdit.vue", "mtime": 1758677034218}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["DeleteCar", "SaveBoat", "GetFactoryList", "data", "btnLoading", "factory", "form", "Shipnumber", "Captain", "Mobile", "Unit", "rules", "required", "message", "trigger", "computed", "mounted", "init", "console", "log", "Id", "methods", "_this", "then", "res", "IsSucceed", "Data", "$message", "Message", "type", "getLicense", "v", "License", "replace", "toUpperCase", "editInit", "row", "$set", "handleSubmit", "_this2", "$refs", "validate", "valid", "$emit"], "sources": ["src/views/PRO/basic-information/ship/component/AddEdit.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\" style=\"width: 100%\">\r\n      <!--      <el-form-item label=\"服务工厂\" prop=\"Factory_Id\">\r\n        <el-select v-model=\"form.Factory_Id\" class=\"w100\" multiple placeholder=\"请选择\" clearable=\"\">\r\n          <el-option\r\n            v-for=\"item in factory\"\r\n            :key=\"item.Id\"\r\n            :label=\"item.Name\"\r\n            :value=\"item.Id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>-->\r\n      <el-form-item label=\"船号\" prop=\"Shipnumber\">\r\n        <el-input v-model=\"form.Shipnumber\" clearable @change=\"getLicense\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"船长\" prop=\"Captain\">\r\n        <el-input v-model=\"form.Captain\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"电话\" prop=\"Mobile\">\r\n        <el-input v-model=\"form.Mobile\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"运输单位\" prop=\"Unit\">\r\n        <el-input v-model=\"form.Unit\" clearable />\r\n      </el-form-item>\r\n      <el-form-item style=\"text-align: right\">\r\n        <el-button @click=\"$emit('close')\">取 消</el-button>\r\n        <!-- <el-button v-if=\"showDelete\" type=\"danger\" @click=\"handleDelete\">删 除</el-button> -->\r\n        <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { DeleteCar, SaveBoat } from '@/api/PRO/car'\r\nimport { GetFactoryList } from '@/api/PRO/factory'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      factory: [],\r\n      form: {\r\n       Shipnumber: '',\r\n       Captain: '',\r\n       Mobile: '',\r\n       Unit: '',\r\n\r\n      },\r\n      rules: {\r\n  \r\n        Shipnumber: [\r\n          { required: true, message: '请输入', trigger: 'blur' }\r\n        ],\r\n        Captain:[\r\n          { required: true, message: '请输入', trigger: 'blur' }\r\n        ],\r\n        Mobile: [\r\n          { required: true, message: '请输入', trigger: 'blur' }\r\n        ],\r\n       \r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    // showDelete() {\r\n    //   return this.form.Id !== undefined\r\n    // }\r\n  },\r\n  mounted() {\r\n    this.init()\r\n    console.log(this.form.Id)\r\n  },\r\n  methods: {\r\n    init() {\r\n      GetFactoryList({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.factory = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getLicense(v) {\r\n      this.form.License = v.replace(/([a-zA-Z])/g, function(v) {\r\n        return v.toUpperCase()\r\n      })\r\n    },\r\n    editInit(row) {\r\n      console.log(row)\r\n      const { Shipnumber,Captain, Mobile, Id ,Unit} = row\r\n      // this.form.Factory_Id = Factory_Id\r\n      this.form.Shipnumber = Shipnumber\r\n      this.form.Captain = Captain\r\n      this.form.Unit = Unit\r\n      this.form.Mobile = Mobile\r\n      this.$set(this.form, 'Id', Id)\r\n    },\r\n    handleSubmit() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (!valid) return\r\n        this.btnLoading = true\r\n        SaveBoat(this.form).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '操作成功',\r\n              type: 'success'\r\n            })\r\n            this.$emit('close')\r\n            this.$emit('refresh')\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.btnLoading = false\r\n        })\r\n      })\r\n    }\r\n    // handleDelete() {\r\n    //   this.$confirm('是否删除该车辆?', '提示', {\r\n    //     confirmButtonText: '确定',\r\n    //     cancelButtonText: '取消',\r\n    //     type: 'warning'\r\n    //   }).then(() => {\r\n    //     DeleteCar({\r\n    //       id: this.form.Id\r\n    //     }).then(res => {\r\n    //       if (res.IsSucceed) {\r\n    //         this.$message({\r\n    //           type: 'success',\r\n    //           message: '删除成功!'\r\n    //         })\r\n    //         this.$emit('close')\r\n    //         this.$emit('refresh')\r\n    //       } else {\r\n    //         this.$message({\r\n    //           message: res.Message,\r\n    //           type: 'error'\r\n    //         })\r\n    //       }\r\n    //     })\r\n    //   }).catch(() => {\r\n    //     this.$message({\r\n    //       type: 'info',\r\n    //       message: '已取消删除'\r\n    //     })\r\n    //   })\r\n    // }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA,SAAAA,SAAA,EAAAC,QAAA;AACA,SAAAC,cAAA;AAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,OAAA;MACAC,IAAA;QACAC,UAAA;QACAC,OAAA;QACAC,MAAA;QACAC,IAAA;MAEA;MACAC,KAAA;QAEAJ,UAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,OAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,MAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAGA;IACA;EACA;EACAC,QAAA;IACA;IACA;IACA;EAAA,CACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;IACAC,OAAA,CAAAC,GAAA,MAAAb,IAAA,CAAAc,EAAA;EACA;EACAC,OAAA;IACAJ,IAAA,WAAAA,KAAA;MAAA,IAAAK,KAAA;MACApB,cAAA,KAAAqB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAH,KAAA,CAAAjB,OAAA,GAAAmB,GAAA,CAAAE,IAAA;QACA;UACAJ,KAAA,CAAAK,QAAA;YACAd,OAAA,EAAAW,GAAA,CAAAI,OAAA;YACAC,IAAA;UACA;QACA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAC,CAAA;MACA,KAAAzB,IAAA,CAAA0B,OAAA,GAAAD,CAAA,CAAAE,OAAA,0BAAAF,CAAA;QACA,OAAAA,CAAA,CAAAG,WAAA;MACA;IACA;IACAC,QAAA,WAAAA,SAAAC,GAAA;MACAlB,OAAA,CAAAC,GAAA,CAAAiB,GAAA;MACA,IAAA7B,UAAA,GAAA6B,GAAA,CAAA7B,UAAA;QAAAC,OAAA,GAAA4B,GAAA,CAAA5B,OAAA;QAAAC,MAAA,GAAA2B,GAAA,CAAA3B,MAAA;QAAAW,EAAA,GAAAgB,GAAA,CAAAhB,EAAA;QAAAV,IAAA,GAAA0B,GAAA,CAAA1B,IAAA;MACA;MACA,KAAAJ,IAAA,CAAAC,UAAA,GAAAA,UAAA;MACA,KAAAD,IAAA,CAAAE,OAAA,GAAAA,OAAA;MACA,KAAAF,IAAA,CAAAI,IAAA,GAAAA,IAAA;MACA,KAAAJ,IAAA,CAAAG,MAAA,GAAAA,MAAA;MACA,KAAA4B,IAAA,MAAA/B,IAAA,QAAAc,EAAA;IACA;IACAkB,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;QACAH,MAAA,CAAAnC,UAAA;QACAH,QAAA,CAAAsC,MAAA,CAAAjC,IAAA,EAAAiB,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAc,MAAA,CAAAZ,QAAA;cACAd,OAAA;cACAgB,IAAA;YACA;YACAU,MAAA,CAAAI,KAAA;YACAJ,MAAA,CAAAI,KAAA;UACA;YACAJ,MAAA,CAAAZ,QAAA;cACAd,OAAA,EAAAW,GAAA,CAAAI,OAAA;cACAC,IAAA;YACA;UACA;UACAU,MAAA,CAAAnC,UAAA;QACA;MACA;IACA,EACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;AACA", "ignoreList": []}]}
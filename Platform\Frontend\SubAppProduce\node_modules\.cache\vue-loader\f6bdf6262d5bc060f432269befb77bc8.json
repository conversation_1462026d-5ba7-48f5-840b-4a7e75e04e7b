{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\ProjectAddDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\ProjectAddDialog.vue", "mtime": 1758266768046}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRQcm9jZXNzT2ZQcm9qZWN0TGlzdCwgU3luY1Byb2plY3RQcm9jZXNzRnJvbVByb2plY3QgfSBmcm9tICdAL2FwaS9QUk8vdGVjaG5vbG9neS1saWInDQpleHBvcnQgZGVmYXVsdCB7DQogIGNvbXBvbmVudHM6IHsNCiAgfSwNCiAgcHJvcHM6IHsNCiAgICBzeXNQcm9qZWN0SWQ6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICcnDQogICAgfQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBidG5Mb2FkaW5nOiBmYWxzZSwNCiAgICAgIHBMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHByb2plY3RMaXN0OiBbXSwNCiAgICAgIGZvcm06IHsNCiAgICAgICAgRnJvbV9TeXNfUHJvamVjdF9JZDogJycNCiAgICAgIH0NCiAgICB9DQogIH0sDQogIGFzeW5jIG1vdW50ZWQoKSB7DQogICAgYXdhaXQgdGhpcy5nZXRQcm9jZXNzT2ZQcm9qZWN0TGlzdCgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBhc3luYyBnZXRQcm9jZXNzT2ZQcm9qZWN0TGlzdCgpIHsNCiAgICAgIHRoaXMucExvYWRpbmcgPSB0cnVlDQogICAgICBjb25zdCByZXMgPSBhd2FpdCBHZXRQcm9jZXNzT2ZQcm9qZWN0TGlzdCh7IH0pDQogICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICB0aGlzLnByb2plY3RMaXN0ID0gcmVzLkRhdGEgfHwgW10NCiAgICAgIH0NCiAgICAgIHRoaXMucExvYWRpbmcgPSBmYWxzZQ0KICAgIH0sDQogICAgaGFuZGxlU3VibWl0KCkgew0KICAgICAgaWYgKHRoaXMuZm9ybS5Gcm9tX1N5c19Qcm9qZWN0X0lkID09PSAnJykgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup6aG555uuJykNCiAgICAgIHRoaXMuYnRuTG9hZGluZyA9IHRydWUNCiAgICAgIFN5bmNQcm9qZWN0UHJvY2Vzc0Zyb21Qcm9qZWN0KHsNCiAgICAgICAgRnJvbV9TeXNfUHJvamVjdF9JZDogdGhpcy5mb3JtLkZyb21fU3lzX1Byb2plY3RfSWQsDQogICAgICAgIFRvX1N5c19Qcm9qZWN0X0lkOiB0aGlzLnN5c1Byb2plY3RJZA0KICAgICAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMuJGVtaXQoJ3JlZnJlc2gnKQ0KICAgICAgICAgIHRoaXMuJGVtaXQoJ2Nsb3NlJykNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WQjOatpeaIkOWKn++8gScpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubXNnKQ0KICAgICAgICB9DQogICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["ProjectAddDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAuBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ProjectAddDialog.vue", "sourceRoot": "src/views/PRO/project-config/process-settings/component", "sourcesContent": ["<template>\r\n  <div class=\"form-wrapper\">\r\n    <div class=\"instruction\">请选择项目，添加所选项目的<span>所有工序</span></div>\r\n    <div>\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"82px\">\r\n        <el-form-item v-if=\"!projectList.length\" label=\"项目名称：\">\r\n          <div v-loading=\"pLoading\">暂无可同步的项目</div>\r\n        </el-form-item>\r\n        <el-form-item v-else label=\"项目名称：\">\r\n          <el-select v-model=\"form.From_Sys_Project_Id\" clearable filterable placeholder=\"请选择项目\" style=\"width: 300px\">\r\n            <el-option v-for=\"(item, index) in projectList\" :key=\"index\" :label=\"item.Short_Name\" :value=\"item.Sys_Project_Id\" :disabled=\"item.Sys_Project_Id===sysProjectId\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n    <div class=\"btn-x\">\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button v-if=\"projectList.length\" type=\"primary\" :loading=\"btnLoading\" :disabled=\"!form.From_Sys_Project_Id\" @click=\"handleSubmit\">确 定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetProcessOfProjectList, SyncProjectProcessFromProject } from '@/api/PRO/technology-lib'\r\nexport default {\r\n  components: {\r\n  },\r\n  props: {\r\n    sysProjectId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      pLoading: false,\r\n      projectList: [],\r\n      form: {\r\n        From_Sys_Project_Id: ''\r\n      }\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.getProcessOfProjectList()\r\n  },\r\n  methods: {\r\n    async getProcessOfProjectList() {\r\n      this.pLoading = true\r\n      const res = await GetProcessOfProjectList({ })\r\n      if (res.IsSucceed) {\r\n        this.projectList = res.Data || []\r\n      }\r\n      this.pLoading = false\r\n    },\r\n    handleSubmit() {\r\n      if (this.form.From_Sys_Project_Id === '') return this.$message.warning('请选择项目')\r\n      this.btnLoading = true\r\n      SyncProjectProcessFromProject({\r\n        From_Sys_Project_Id: this.form.From_Sys_Project_Id,\r\n        To_Sys_Project_Id: this.sysProjectId\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$emit('refresh')\r\n          this.$emit('close')\r\n          this.$message.success('同步成功！')\r\n        } else {\r\n          this.$message.error(res.msg)\r\n        }\r\n        this.btnLoading = false\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n  .form-wrapper {\r\n    display: flex;\r\n    flex-direction: column;\r\n    overflow: hidden;\r\n    max-height: 70vh;\r\n\r\n    .btn-x {\r\n      padding-top: 16px;\r\n      text-align: right;\r\n    }\r\n    .instruction {\r\n    background: #f0f9ff;\r\n    border: 1px solid #b3d8ff;\r\n    color: #1890ff;\r\n    padding: 12px 16px;\r\n    border-radius: 4px;\r\n    margin-bottom: 16px;\r\n    font-weight: 500;\r\n  }\r\n  }\r\n</style>\r\n"]}]}
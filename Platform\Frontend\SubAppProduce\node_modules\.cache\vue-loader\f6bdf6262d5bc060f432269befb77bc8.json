{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\ProjectAddDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\ProjectAddDialog.vue", "mtime": 1757642584768}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IEdldEJPTUluZm8gfSBmcm9tICdAL3ZpZXdzL1BSTy9ib20tc2V0dGluZy91dGlscycKaW1wb3J0IHsgR2V0UHJvY2Vzc09mUHJvamVjdExpc3QsIFN5bmNQcm9qZWN0UHJvY2Vzc0Zyb21Qcm9qZWN0IH0gZnJvbSAnQC9hcGkvUFJPL3RlY2hub2xvZ3ktbGliJwpleHBvcnQgZGVmYXVsdCB7CiAgY29tcG9uZW50czogewogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGJvbUxpc3Q6IFtdLAogICAgICBjb21OYW1lOiAnJywKICAgICAgcGFydE5hbWU6ICcnLAogICAgICBib21BY3RpdmVOYW1lOiAnJywKICAgICAgYnRuTG9hZGluZzogZmFsc2UsCiAgICAgIGZvcm06IHsKICAgICAgICBGcm9tX1N5c19Qcm9qZWN0X0lkOiAnJwogICAgICB9CiAgICB9CiAgfSwKICBhc3luYyBtb3VudGVkKCkgewogICAgY29uc3QgeyBjb21OYW1lLCBwYXJ0TmFtZSwgbGlzdCB9ID0gYXdhaXQgR2V0Qk9NSW5mbygpCiAgICB0aGlzLmNvbU5hbWUgPSBjb21OYW1lCiAgICB0aGlzLnBhcnROYW1lID0gcGFydE5hbWUKICAgIHRoaXMuYm9tTGlzdCA9IGxpc3QKICAgIHRoaXMuYm9tQWN0aXZlTmFtZSA9IGxpc3RbMF0uQ29kZQogIH0sCiAgbWV0aG9kczogewogICAgaGFuZGxlU3VibWl0KCkgewoKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["ProjectAddDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAqBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "ProjectAddDialog.vue", "sourceRoot": "src/views/PRO/project-config/process-settings/component", "sourcesContent": ["<template>\n  <div class=\"form-wrapper\">\n    <div class=\"instruction\">请选择项目，添加所选项目的<span>所有工序</span></div>\n    <div>\n      <el-form ref=\"form\" :model=\"form\" label-width=\"82px\">\n        <el-form-item label=\"项目名称：\">\n          <el-select v-model=\"form.From_Sys_Project_Id\" placeholder=\"请选择项目\" style=\"width: 300px\">\n            <el-option label=\"区域一\" value=\"shanghai\" />\n            <el-option label=\"区域二\" value=\"beijing\" />\n          </el-select>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"btn-x\">\n      <el-button @click=\"$emit('close')\">取 消</el-button>\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport { GetProcessOfProjectList, SyncProjectProcessFromProject } from '@/api/PRO/technology-lib'\nexport default {\n  components: {\n  },\n  data() {\n    return {\n      bomList: [],\n      comName: '',\n      partName: '',\n      bomActiveName: '',\n      btnLoading: false,\n      form: {\n        From_Sys_Project_Id: ''\n      }\n    }\n  },\n  async mounted() {\n    const { comName, partName, list } = await GetBOMInfo()\n    this.comName = comName\n    this.partName = partName\n    this.bomList = list\n    this.bomActiveName = list[0].Code\n  },\n  methods: {\n    handleSubmit() {\n\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n  .form-wrapper {\n    display: flex;\n    flex-direction: column;\n    overflow: hidden;\n    max-height: 70vh;\n    .instruction {\n      font-size: 14px;\n      color: #333333;\n      margin-bottom: 30px;\n      span {\n        color: #000000;\n      }\n    }\n    .btn-x {\n      padding-top: 16px;\n      text-align: right;\n    }\n  }\n</style>\n"]}]}
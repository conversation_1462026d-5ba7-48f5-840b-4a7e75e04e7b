<template>

  <div>

    <el-form label-width="80px" :inline="true">
      <el-form-item>
        <el-button type="primary" @click="handleDevice">关联设备</el-button>
      </el-form-item>
      <el-form-item label="设备名称" prop="deviceName">
        <el-input v-model.trim="form.deviceName" :clearbale="true" />
      </el-form-item>
      <el-form-item label="设备类型" prop="deviceType">
        <el-select v-model="form.deviceType" clearable placeholder="请选择" style="width: 100%" @change="deviceTypeChange">
          <el-option v-for="item in deviceTypeOptions" :key="item.Id" :label="item.Display_Name" :value="item.Id" />
        </el-select>
      </el-form-item>
      <el-form-item label="设备子类" prop="Type_Detail_Id">
        <el-select
          v-model="form.Type_Detail_Id"
          clearable
          placeholder="请选择"
          :disabled="!form.deviceType"
          style="width: 100%"
        >
          <el-option v-for="item in deviceItemTypeOptions" :key="item.Id" :label="item.Display_Name" :value="item.Id" />
        </el-select>
      </el-form-item>
      <el-form-item label="所属部门" prop="department">
        <el-input v-model.trim="form.department" clearbale />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="reset">重置</el-button>
      </el-form-item>

    </el-form>

    <div v-loading="tbLoading" class="fff cs-z-tb-wrapper" style="height: calc(100vh - 320px);">
      <dynamic-data-table
        ref="dyTable"
        :key="tableKey"
        :columns="columns"
        :config="tbConfig"
        :data="tbData"
        :page="queryInfo.Page"
        :total="total"
        border
        stripe
        class="cs-plm-dy-table"
        @multiSelectedChange="handleSelectionChange"
        @gridPageChange="gridPageChange"
        @gridSizeChange="gridSizeChange"
        @tableSearch="tableSearch"
      >
        <template slot="Director_UserName" slot-scope="{ row }">
          <div>{{ row.Director_UserName || "-" }}</div>
        </template>
        <template slot="Working_Team_Names" slot-scope="{ row }">
          <div>{{ row.Working_Team_Names || "-" }}</div>
        </template>

      </dynamic-data-table>
    </div>
  </div>

</template>

<script>
import getTbInfo from '@/mixins/PRO/get-table-info'
import DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'
import TopHeader from '@/components/TopHeader'
// import detail from "./component/detail";
import { GetDictionaryDetailListByCode, GetDictionaryDetailListByParentId } from '@/api/sys'
import { GetEquipmentAssetPageList, AddWorkingProcess } from '@/api/PRO/technology-lib'

export default {
  name: 'PROGroup',

  components: {
    DynamicDataTable,
    TopHeader
    // detail,
  },
  mixins: [getTbInfo],
  props: {
    rowData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      tbConfig: {
        Pager_Align: 'center'
      },
      queryInfo: {
        Page: 1,
        PageSize: 10
      },
      deviceItemTypeOptions: [],
      form: {
        deviceName: '',
        deviceType: '',
        Type_Detail_Id: '',
        department: ''
      },
      deviceTypeOptions: [],
      currentComponent: '',
      title: '',
      columns: [],
      tbData: [],
      total: 0,
      tableKey: Math.random(),
      tbLoading: false,
      dialogVisible: false,
      selectList: [],
      keywords: ''
    }
  },
  async created() {
    this.tbLoading = true
    this.selectList = []
    await this.getTableConfig('plm_device_list')
    this.getEquipmentAssetPageList()
    this.getDictionaryDetailListByCode()
  },
  methods: {
    clearSelec() {
      this.$refs.dyTable.clearSelection()
    },
    deviceTypeChange(e) {
      this.form.Type_Detail_Id = ''
      this.deviceItemTypeOptions = []
      GetDictionaryDetailListByParentId(e).then((res) => {
        this.deviceItemTypeOptions = res.Data
      })
    },
    // 获取设备类型
    async getDictionaryDetailListByCode() {
      await GetDictionaryDetailListByCode({ dictionaryCode: 'deviceType' }).then((res) => {
        if (res.IsSucceed) {
          this.deviceTypeOptions = res.Data || []
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
      console.log(' this.optionsGroupList', this.optionsGroupList)
    },
    async getEquipmentAssetPageList() {
      await GetEquipmentAssetPageList({
        Display_Name: this.form.deviceName,
        Device_Type_Id: this.form.deviceType,
        Device_Type_Detail_Id: this.form.Type_Detail_Id,
        Department: this.form.department,
        Page: this.queryInfo.Page,
        PageSize: this.queryInfo.PageSize
      }).then((res) => {
        if (res.IsSucceed) {
          this.tbData = res.Data.Data
          this.total = res.Data.TotalCount
          this.tbLoading = false
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
      console.log(' this.optionsGroupList', this.optionsGroupList)
    },
    gridPageChange({ page }) {
      this.queryInfo.Page = Number(page)
      this.getEquipmentAssetPageList()
    },
    gridSizeChange({ size }) {
      this.queryInfo.PageSize = Number(size)
      this.queryInfo.Page = 1
      this.getEquipmentAssetPageList()
    },
    handleDevice() {
      if (this.selectList.length === 0) {
        this.$message({
          message: '请选择设备',
          type: 'error'
        })
        return
      } else {
        this.rowData.Device_Ids = this.selectList
        console.log(this.rowData, 'this.rowData')

        AddWorkingProcess(this.rowData).then((res) => {
          if (res.IsSucceed) {
            this.$message({
              message: '关联成功',
              type: 'success'
            })
            this.$emit('fetchData')
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      }
    },

    handleSelectionChange(list) {
      this.selectList = list.map((i) => i.Id)
      console.log(this.selectList, 'this.selectList')
    },
    handleAdd() {
      this.currentComponent = 'detail'
      this.title = '新增车间'
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.currentComponent = 'detail'
      this.title = '编辑车间'
      this.dialogVisible = true
      this.$nextTick((_) => {
        this.$refs['content'].initData(row)
      })
    },
    // handleDetail(row) {
    //   this.currentComponent = "info";
    //   this.title = "查看";
    //   this.dialogVisible = true;
    //   this.$nextTick((_) => {
    //     this.$refs["content"].initData(row.Id);
    //   });
    // },

    handleSearch() {
      this.getEquipmentAssetPageList()
      this.queryInfo.Page = 1
    },
    reset() {
      this.form = {}
      this.getEquipmentAssetPageList()
      this.queryInfo.Page = 1
    }
  }
}
</script>

<style scoped lang="scss">
// .cs-dialog {
//   ::v-deep {
//     .el-dialog__body {
//       padding-top: 0;
//     }
//   }
// }
::v-deep {
  .cs-top-header-box {
    line-height: 0px;
  }
}

::v-deep .pagination {
  justify-content: flex-end !important;
  margin-top: 12px !important;

  .el-input--small .el-input__inner {
    height: 28px;
    line-height: 28px;
  }
}
</style>

{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\component-list\\v4\\component\\PartList.vue?vue&type=style&index=0&id=204d914f&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\component-list\\v4\\component\\PartList.vue", "mtime": 1757583738729}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouY29udGVudHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgLnRvcC1pbmZvew0KICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICBzcGFuIHsNCiAgICAgIGNvbG9yOiAjMDBDMzYxDQogICAgfQ0KICAgIC50b3AtaW5mby10aXRsZXsNCiAgICAgIG1hcmdpbi1ib3R0b206IDhweDsNCiAgICAgIGZvbnQtc2l6ZTogMjRweDsNCiAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgIH0NCiAgfQ0KICAudGItY29udGFpbmVyew0KICAgIG1hcmdpbi10b3A6IDIwcHg7DQogICAgZmxleDogMTsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["PartList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PartList.vue", "sourceRoot": "src/views/PRO/component-list/v4/component", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"top-info\">\r\n      <div class=\"top-info-title\">{{ SteelName }}</div>\r\n      <div>需求零件总数：<span>{{ steelTotalNum }}</span> 件 需求零件总重：<span>{{ steelTotalWeight }}</span> kg</div>\r\n    </div>\r\n    <div class=\"tb-container\">\r\n      <vxe-table\r\n        v-loading=\"tbLoading\"\r\n        :empty-render=\"{name: 'NotData'}\"\r\n        show-header-overflow\r\n        element-loading-spinner=\"el-icon-loading\"\r\n        element-loading-text=\"拼命加载中\"\r\n        empty-text=\"暂无数据\"\r\n        class=\"cs-vxe-table\"\r\n        height=\"500\"\r\n        align=\"left\"\r\n        stripe\r\n        :data=\"tbData\"\r\n        resizable\r\n        :tree-config=\"{transform: true, rowField: 'Id', parentField: 'ParentId'}\"\r\n        :tooltip-config=\"{ enterable: true}\"\r\n      >\r\n        <template v-for=\"(item,idx) in columns\">\r\n          <vxe-column\r\n            :key=\"item.Code\"\r\n            :tree-node=\"idx===0\"\r\n            :min-width=\"item.Min_Width\"\r\n            :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n            show-overflow=\"tooltip\"\r\n            sortable\r\n            :align=\"item.Align\"\r\n            :field=\"item.Code\"\r\n            :title=\"item.Display_Name\"\r\n          >\r\n            <template #default=\"{ row }\">\r\n              <template v-if=\"item.Code === 'Code'\">\r\n                <el-tag v-if=\"row.Is_Change\" style=\"margin-right: 8px;\" type=\"danger\">变</el-tag>\r\n                <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                <span>{{ row.Code }}</span>\r\n                <span style=\"margin-left: 5px;\">\r\n                  <template v-for=\"firstName in getUnitPartGradeList(row)\">\r\n                    <el-tag v-if=\"row.Part_Grade>0\" :key=\"firstName\">{{ firstName }}</el-tag>\r\n                  </template>\r\n                  <el-tag v-if=\"row.Part_Grade===0\" type=\"success\">{{ getPartGradeName() }}</el-tag>\r\n                </span>\r\n              </template>\r\n              <span v-else-if=\"item.Code === 'SchedulingNum'\">\r\n                {{ row.SchedulingNum ? row.SchedulingNum : '-' }}\r\n              </span>\r\n              <span v-else-if=\"item.Code === 'Producting_Count'\">\r\n                {{ row.Producting_Count ? row.Producting_Count : '-' }}\r\n              </span>\r\n              <span v-else-if=\"item.Code === 'Is_Main'\">\r\n                <span v-if=\"row.Part_Grade===1\">-</span>\r\n                <span v-else>\r\n                  <el-tag\r\n                    :type=\"row.Is_Main ? 'success' : 'danger'\"\r\n                  >{{ row.Is_Main ? \"是\" : \"否\" }}\r\n                  </el-tag>\r\n                </span>\r\n              </span>\r\n              <span v-else-if=\"item.Code === 'SH'\">\r\n                <div v-if=\"row.SH==0\">/</div>\r\n                <div v-else>\r\n                  <el-button type=\"text\" @click=\"handleView(row)\">查看</el-button>\r\n                </div>\r\n              </span>\r\n              <span v-else>\r\n                {{ row[item.Code] | displayValue }}\r\n              </span>\r\n            </template>\r\n          </vxe-column>\r\n        </template>\r\n      </vxe-table>\r\n    </div>\r\n    <!--    <div v-loading=\"tbLoading\" class=\"tb-container\">\r\n      <dynamic-data-table\r\n        ref=\"dyTable\"\r\n        :columns=\"columns\"\r\n        :config=\"tbConfig\"\r\n        :data=\"tbData\"\r\n        :page=\"queryInfo.Page\"\r\n        :total=\"total\"\r\n        border\r\n        stripe\r\n        class=\"cs-plm-dy-table\"\r\n      >\r\n        <template slot=\"SchedulingNum\" slot-scope=\"{ row }\">\r\n          <div>\r\n            {{ row.SchedulingNum ? row.SchedulingNum : '-' }}\r\n          </div>\r\n        </template>\r\n        <template slot=\"Is_Main\" slot-scope=\"{ row }\">\r\n          <div>\r\n            {{ row.Is_Main==true ? '是' : '否' }}\r\n          </div>\r\n        </template>\r\n        <template slot=\"SH\" slot-scope=\"{ row }\">\r\n          <div v-if=\"row.SH==0\">/</div>\r\n          <div v-else>\r\n            <el-button type=\"text\" @click=\"handleView(row)\">查看</el-button>\r\n          </div>\r\n        </template>\r\n      </dynamic-data-table>\r\n    </div>-->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetPartListWithComponent } from '@/api/PRO/component'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable.vue'\r\nimport numeral from 'numeral'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\nexport default {\r\n  components: {\r\n    DynamicDataTable\r\n  },\r\n  mixins: [getTbInfo],\r\n  props: {\r\n    projectId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    sysProjectId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      tbLoading: false,\r\n      tbConfig: {\r\n        Is_Page: false,\r\n        Height: 600\r\n      },\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: 10\r\n      },\r\n      columns: [],\r\n      steelTotalNum: 0,\r\n      steelTotalWeight: 0,\r\n      total: 0,\r\n      tbData: [],\r\n      rowId: 0,\r\n      SteelName: '',\r\n      firstNameList: []\r\n    }\r\n  },\r\n  mounted() {\r\n\r\n  },\r\n  methods: {\r\n    async init(rowData) {\r\n      const { comName, list } = await GetBOMInfo()\r\n      const allName = list.map(item => item.Display_Name).join('/')\r\n      this.firstNameList = list\r\n      console.log('this.firstNameList', this.firstNameList)\r\n      this.columns = [\r\n        { Code: 'Code', Display_Name: `${allName}名称`, Is_Frozen: true, Frozen_To: 'left', Min_Width: 180, Is_Display: true, Sort: 1 },\r\n        { Code: 'Spec', Display_Name: '规格', Min_Width: 160, Is_Display: true, Sort: 2 },\r\n        { Code: 'Length', Display_Name: '长度', Min_Width: 160, Is_Display: true, Sort: 3 },\r\n        { Code: 'Component_Code', Display_Name: `所属${comName}`, Min_Width: 140, Is_Display: true, Sort: 4 },\r\n        { Code: 'Num', Display_Name: '需求数量', Min_Width: 120, Is_Display: true, Sort: 5 },\r\n        { Code: 'SchedulingNum', Display_Name: '排产数量', Min_Width: 120, Is_Display: true, Sort: 6 },\r\n        { Code: 'Producting_Count', Display_Name: '生产中数量', Min_Width: 140, Is_Display: true, Sort: 6 },\r\n        { Code: 'Weight', Display_Name: '单重（kg）', Min_Width: 120, Is_Display: true, Sort: 7 },\r\n        { Code: 'Total_Weight', Display_Name: '总重（kg）', Min_Width: 120, Is_Display: true, Sort: 8 },\r\n        { Code: 'Is_Main', Display_Name: '是否主零件', Min_Width: 140, Is_Display: true, Sort: 10 },\r\n        { Code: 'Remark', Display_Name: '备注', Min_Width: 160, Is_Display: true, Sort: 11 },\r\n        { Code: 'SH', Display_Name: '深化资料', Is_Frozen: true, Frozen_To: 'right', Min_Width: 120, Is_Display: true, Sort: 12 }\r\n      ]\r\n      console.log('rowData', JSON.parse(JSON.stringify(rowData)))\r\n      this.rowId = rowData.Id\r\n      this.SteelName = rowData.SteelName\r\n      this.tbLoading = true\r\n\r\n      GetPartListWithComponent({ id: rowData.Id }).then(async res => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data\r\n          let _num = 0\r\n          let _weight = 0\r\n          res.Data.forEach(item => {\r\n            if (item.Part_Grade !== 1) {\r\n              _num += item.Num\r\n              _weight += item.Total_Weight\r\n            }\r\n          })\r\n          this.steelTotalNum = numeral(_num).format('0.[00]')\r\n          this.steelTotalWeight = numeral(_weight).format('0.[00]')\r\n          await this.getStopList()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    async getStopList() {\r\n      const submitObj = this.tbData.map(item => {\r\n        return {\r\n          Id: item.Part_Aggregate_Id,\r\n          Type: item.Part_Grade === 0 ? 1 : 3\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = item.Is_Stop !== null\r\n          })\r\n          this.tbData.forEach(row => {\r\n            if (stopMap[row.Part_Aggregate_Id]) {\r\n              this.$set(row, 'stopFlag', stopMap[row.Part_Aggregate_Id])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleView(row) {\r\n      console.log('row', row)\r\n      this.$emit('checkSteelMeans', row)\r\n    },\r\n    getUnitPartGradeList(row) {\r\n      const item = this.firstNameList.find(item => +item.Code === row.Part_Grade)\r\n      return item.Display_Name[0]\r\n    },\r\n    getPartGradeName() {\r\n      const item = this.firstNameList.find(item => +item.Code === 0)\r\n      return item.Display_Name[0]\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.content{\r\n  display: flex;\r\n  flex-direction: column;\r\n  .top-info{\r\n    font-size: 14px;\r\n    span {\r\n      color: #00C361\r\n    }\r\n    .top-info-title{\r\n      margin-bottom: 8px;\r\n      font-size: 24px;\r\n      font-weight: bold;\r\n    }\r\n  }\r\n  .tb-container{\r\n    margin-top: 20px;\r\n    flex: 1;\r\n  }\r\n}\r\n</style>\r\n"]}]}
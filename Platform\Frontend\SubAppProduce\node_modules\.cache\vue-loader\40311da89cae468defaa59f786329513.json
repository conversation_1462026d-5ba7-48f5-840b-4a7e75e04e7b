{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\ProjectAddDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\ProjectAddDialog.vue", "mtime": 1757991914477}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IEdldFByb2Nlc3NPZlByb2plY3RMaXN0LCBTeW5jUHJvamVjdFByb2Nlc3NGcm9tUHJvamVjdCB9IGZyb20gJ0AvYXBpL1BSTy90ZWNobm9sb2d5LWxpYicKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsKICB9LAogIHByb3BzOiB7CiAgICBzeXNQcm9qZWN0SWQ6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnJwogICAgfQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGJ0bkxvYWRpbmc6IGZhbHNlLAogICAgICBwcm9qZWN0TGlzdDogW10sCiAgICAgIGZvcm06IHsKICAgICAgICBGcm9tX1N5c19Qcm9qZWN0X0lkOiAnJwogICAgICB9CiAgICB9CiAgfSwKICBhc3luYyBtb3VudGVkKCkgewogICAgYXdhaXQgdGhpcy5nZXRQcm9jZXNzT2ZQcm9qZWN0TGlzdCgpCiAgfSwKICBtZXRob2RzOiB7CiAgICBhc3luYyBnZXRQcm9jZXNzT2ZQcm9qZWN0TGlzdCgpIHsKICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0UHJvY2Vzc09mUHJvamVjdExpc3QoeyB9KQogICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgIHRoaXMucHJvamVjdExpc3QgPSByZXMuRGF0YQogICAgICB9CiAgICB9LAogICAgaGFuZGxlU3VibWl0KCkgewogICAgICBpZiAodGhpcy5mb3JtLkZyb21fU3lzX1Byb2plY3RfSWQgPT09ICcnKSByZXR1cm4gdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6npobnnm64nKQogICAgICB0aGlzLmJ0bkxvYWRpbmcgPSB0cnVlCiAgICAgIFN5bmNQcm9qZWN0UHJvY2Vzc0Zyb21Qcm9qZWN0KHsKICAgICAgICBGcm9tX1N5c19Qcm9qZWN0X0lkOiB0aGlzLmZvcm0uRnJvbV9TeXNfUHJvamVjdF9JZCwKICAgICAgICBUb19TeXNfUHJvamVjdF9JZDogdGhpcy5zeXNQcm9qZWN0SWQKICAgICAgfSkudGhlbihyZXMgPT4gewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICB0aGlzLiRlbWl0KCdyZWZyZXNoJykKICAgICAgICAgIHRoaXMuJGVtaXQoJ2Nsb3NlJykKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5ZCM5q2l5oiQ5Yqf77yBJykKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubXNnKQogICAgICAgIH0KICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQogICAgICB9KQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["ProjectAddDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAuBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ProjectAddDialog.vue", "sourceRoot": "src/views/PRO/project-config/project-quality/components/Dialog", "sourcesContent": ["<template>\n  <div class=\"form-wrapper\">\n    <div class=\"instruction\">请选择项目，添加所选项目的<span>所有质检配置</span></div>\n    <div>\n      <el-form ref=\"form\" :model=\"form\" label-width=\"82px\">\n        <el-form-item v-if=\"!projectList.length\" label=\"项目名称：\">\n          <div>暂无可同步的项目</div>\n        </el-form-item>\n        <el-form-item v-else label=\"项目名称：\">\n          <el-select v-model=\"form.From_Sys_Project_Id\" placeholder=\"请选择项目\" style=\"width: 300px\">\n            <el-option v-for=\"(item, index) in projectList\" :key=\"index\" :label=\"item.Short_Name\" :value=\"item.Sys_Project_Id\" :disabled=\"item.Sys_Project_Id===sysProjectId\" />\n          </el-select>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"btn-x\">\n      <el-button @click=\"$emit('close')\">取 消</el-button>\n      <el-button v-if=\"projectList.length\" type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetProcessOfProjectList, SyncProjectProcessFromProject } from '@/api/PRO/technology-lib'\nexport default {\n  components: {\n  },\n  props: {\n    sysProjectId: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      btnLoading: false,\n      projectList: [],\n      form: {\n        From_Sys_Project_Id: ''\n      }\n    }\n  },\n  async mounted() {\n    await this.getProcessOfProjectList()\n  },\n  methods: {\n    async getProcessOfProjectList() {\n      const res = await GetProcessOfProjectList({ })\n      if (res.IsSucceed) {\n        this.projectList = res.Data\n      }\n    },\n    handleSubmit() {\n      if (this.form.From_Sys_Project_Id === '') return this.$message.warning('请选择项目')\n      this.btnLoading = true\n      SyncProjectProcessFromProject({\n        From_Sys_Project_Id: this.form.From_Sys_Project_Id,\n        To_Sys_Project_Id: this.sysProjectId\n      }).then(res => {\n        if (res.IsSucceed) {\n          this.$emit('refresh')\n          this.$emit('close')\n          this.$message.success('同步成功！')\n        } else {\n          this.$message.error(res.msg)\n        }\n        this.btnLoading = false\n      })\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n  .form-wrapper {\n    display: flex;\n    flex-direction: column;\n    overflow: hidden;\n    max-height: 70vh;\n\n    .btn-x {\n      padding-top: 16px;\n      text-align: right;\n    }\n    .instruction {\n      background: #f0f9ff;\n      border: 1px solid #b3d8ff;\n      color: #1890ff;\n      padding: 12px 16px;\n      border-radius: 4px;\n      margin-bottom: 16px;\n      font-weight: 500;\n    }\n  }\n</style>\n"]}]}
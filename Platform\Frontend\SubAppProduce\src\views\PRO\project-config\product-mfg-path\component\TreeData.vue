<template>
  <div :key="activeType" class="tree-container">
    <div class="title">
      <!-- <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button> -->
      <el-button type="danger" @click="handleDelete">删除</el-button>
    </div>
    <div class="tree-wrapper">
      <el-tree
        ref="tree"
        v-loading="loading"
        :current-node-key="currentNodeKey"
        element-loading-text="加载中"
        element-loading-spinner="el-icon-loading"
        empty-text="暂无数据"
        highlight-current
        show-checkbox
        node-key="Id"
        default-expand-all
        :expand-on-click-node="false"
        :data="treeData"
        :props="{
          label:'Label',
          children:'Children'
        }"
        @node-click="handleNodeClick"
      >
        <span slot-scope="{ node, data }" class="custom-tree-node">
          <svg-icon
            :icon-class="
              node.expanded ? 'icon-folder-open' : 'icon-folder'
            "
            class-name="class-icon"
          />
          <span class="cs-label" :title="node.label">{{ node.label }}</span>
        </span>
      </el-tree>
    </div>
  </div>
</template>

<script>
import { GetCompTypeTree } from '@/api/PRO/component-type'
import { GetPartTypeTree } from '@/api/PRO/partType'

export default {
  props: {
    typeCode: {
      type: String,
      default: ''
    },
    typeId: {
      type: String,
      default: ''
    },
    activeType: {
      type: String,
      default: '-1'
    }
  },
  data() {
    return {
      treeData: [],
      bomList: [],
      loading: true,
      currentNodeKey: ''

    }
  },
  watch: {
    // currentNodeKey(newValue) {
    //   this.$emit('showRight', newValue !== '')
    // }
    typeId: {
      handler() {
        this.currentNodeKey = ''
        this.fetchData()
      },
      immediate: false
    },
    activeType(newValue) {
      this.currentNodeKey = ''
      this.fetchData()
    }
  },
  async mounted() {
    this.fetchData()
  },
  methods: {

    async fetchData() {
      if (!this.typeCode || !this.typeId) {
        return
      }
      this.loading = true
      console.log(this.activeType, 3313)
      let res
      try {
        if (this.activeType === '-1') {
          res = await GetCompTypeTree({ professional: this.typeCode })
        } else {
          res = await GetPartTypeTree({ professionalId: this.typeId, partGrade: this.activeType.toString() })
        }
        if (res.IsSucceed) {
          this.treeData = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
          this.treeData = []
        }
      } catch (error) {
        this.treeData = []
      }
      this.loading = false
      this.currentNodeKey && this.setTreeNode()
    },
    setTreeNode() {
      this.$emit('nodeClick', this.$refs['tree'].getNode(this.currentNodeKey))
      this.$nextTick(_ => {
        this.$refs['tree'].setCurrentKey(this.currentNodeKey)
      })
    },
    handleAdd() {
      this.$emit('AddFirst')
    },
    handleDelete() {
      this.$emit('Delete')
    },
    handleNodeClick(data, node) {
      this.currentNodeKey = data.Id
      this.$emit('nodeClick', node)
    },
    handleCheck(data, node) {
      this.$emit('check', data)
    },
    resetKey(id) {
      if (id === this.currentNodeKey) {
        this.currentNodeKey = ''
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/styles/mixin.scss';

.tree-container{
  height: 100%;
  margin-right: 16px;
  flex-basis: 25%;
  background-color: #FFFFFF;
  display: flex;
  flex-direction: column;
  .title{
    border-bottom: 1px solid #EEEEEE;
    font-weight: 500;
    padding: 0 16px 16px 16px;
    color: #333333;
    // display: flex;
    // justify-content: space-between;
    .title-name {
      height: 30px;
      line-height: 30px;
    }
    .btn-x{
      padding: 0 0;
      text-align: center;
    }
  }

  .tree-wrapper{
    padding: 16px;
    flex: 1;
    overflow: hidden;
    .el-tree{
      @include scrollBar;
      height: 100%;
      overflow: auto;
    }
    .cs-label{
      font-size: 14px;
      margin-left: 4px;
    }
  }
}
</style>

{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\nesting-management\\components\\NestResult.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\nesting-management\\components\\NestResult.vue", "mtime": 1757468127975}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBjb21iaW5lVVJMIH0gZnJvbSAnQC91dGlscycNCmltcG9ydCB7IEdldFBsYXRlTmVzdGluZ1Jlc3VsdEltcG9ydEZpbGUsIEltcG9ydFBsYXRlTmVzdGluZ1Jlc3VsdCB9IGZyb20gJ0AvYXBpL1BSTy9wcm9kdWN0aW9uLXRhc2snDQppbXBvcnQgVXBsb2FkRXhjZWwgZnJvbSAnQC9jb21wb25lbnRzL1VwbG9hZEV4Y2VsL2luZGV4LnZ1ZScNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7IFVwbG9hZEV4Y2VsIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGJ0bkxvYWRpbmc6IGZhbHNlDQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgYmVmb3JlVXBsb2FkKGZpbGUpIHsNCiAgICAgIHRoaXMuYnRuTG9hZGluZyA9IHRydWUNCiAgICAgIGNvbnN0IGZpbGVGb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpDQogICAgICBmaWxlRm9ybURhdGEuYXBwZW5kKCdGaWxlcycsIGZpbGUpDQogICAgICBJbXBvcnRQbGF0ZU5lc3RpbmdSZXN1bHQoZmlsZUZvcm1EYXRhKS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiAn5a+85YWl5oiQ5YqfJywNCiAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy4kZW1pdCgnY2xvc2UnKQ0KICAgICAgICAgIHRoaXMuJGVtaXQoJ3JlZnJlc2gnKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgICByZXMuRGF0YSAmJiB3aW5kb3cub3Blbihjb21iaW5lVVJMKHRoaXMuJGJhc2VVcmwsIHJlcy5EYXRhKSkNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZVN1Ym1pdCgpIHsNCiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmhhbmRsZVN1Ym1pdCgpDQogICAgfSwNCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICBHZXRQbGF0ZU5lc3RpbmdSZXN1bHRJbXBvcnRGaWxlKHt9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgd2luZG93Lm9wZW4oY29tYmluZVVSTCh0aGlzLiRiYXNlVXJsLCByZXMuRGF0YSkpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["NestResult.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "NestResult.vue", "sourceRoot": "src/views/PRO/nesting-management/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-alert\r\n      type=\"warning\"\r\n      :closable=\"false\"\r\n    >\r\n      <template>\r\n        <span class=\"cs-label\">注意：请先 <el-link\r\n          type=\"primary\"\r\n          :underline=\"false\"\r\n          @click=\"handleExport\"\r\n        >点击下载模板</el-link></span>\r\n      </template>\r\n    </el-alert>\r\n\r\n    <div class=\"cs-upload-x\">\r\n      <upload-excel ref=\"upload\" :before-upload=\"beforeUpload\" />\r\n    </div>\r\n    <div slot=\"footer\" class=\"dialog-footer\" style=\"text-align: right;\">\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button\r\n        type=\"primary\"\r\n        :loading=\"btnLoading\"\r\n        @click=\"handleSubmit\"\r\n      >确 定\r\n      </el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { combineURL } from '@/utils'\r\nimport { GetPlateNestingResultImportFile, ImportPlateNestingResult } from '@/api/PRO/production-task'\r\nimport UploadExcel from '@/components/UploadExcel/index.vue'\r\n\r\nexport default {\r\n  components: { UploadExcel },\r\n  data() {\r\n    return {\r\n      btnLoading: false\r\n    }\r\n  },\r\n  methods: {\r\n    beforeUpload(file) {\r\n      this.btnLoading = true\r\n      const fileFormData = new FormData()\r\n      fileFormData.append('Files', file)\r\n      ImportPlateNestingResult(fileFormData).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '导入成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('close')\r\n          this.$emit('refresh')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          res.Data && window.open(combineURL(this.$baseUrl, res.Data))\r\n        }\r\n        this.btnLoading = false\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      this.$refs.upload.handleSubmit()\r\n    },\r\n    handleExport() {\r\n      GetPlateNestingResultImportFile({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data))\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.cs-label {\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n  white-space: pre-wrap;\r\n}\r\n\r\n.cs-upload-x {\r\n  margin: 32px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  .c-upload-container{\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\detail.vue?vue&type=template&id=a5aa7006&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\detail.vue", "mtime": 1758330074629}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\detail.vue?vue&type=template&id=a5aa7006&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\detail.vue", "mtime": 1757909680922}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}
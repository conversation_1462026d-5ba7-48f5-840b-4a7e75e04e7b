{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new\\draft.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new\\draft.vue", "mtime": 1757468128014}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["closeTagView", "debounce", "BatchProcessAdjust", "GetCanSchdulingPartList", "GetCompSchdulingInfoDetail", "GetDwg", "GetPartSchdulingInfoDetail", "GetSchdulingWorkingTeams", "SaveComponentSchedulingWorkshop", "SaveCompSchdulingDraft", "SavePartSchdulingDraftNew", "SavePartSchedulingWorkshopNew", "SaveSchdulingTaskById", "GetStopList", "AddDraft", "OwnerProcess", "Workshop", "GetGridByCode", "uniqueCode", "v4", "uuidv4", "numeral", "GetLibListType", "GetProcessFlowListWithTechnology", "GetProcessListBase", "AreaGetEntity", "mapActions", "mapGetters", "GetPartTypeList", "moment", "ExpandableSection", "TreeDetail", "GetInstallUnitIdNameList", "GetProjectAreaTreeList", "GetCompTypeTree", "parseOssUrl", "DynamicTableFields", "GetBOMInfo", "SPLIT_SYMBOL", "components", "data", "bomList", "comName", "levelCode", "isComponentOptions", "label", "value", "specOptions", "filterTypeOption", "filterCodeOption", "pickerOptions", "disabledDate", "time", "innerForm", "searchContent", "searchComTypeSearch", "searchSpecSearch", "searchDirect", "cur<PERSON><PERSON>ch", "searchType", "formInline", "Schduling_Code", "Create_UserName", "Finish_Date", "InstallUnit_Id", "Remark", "total", "currentIds", "gridCode", "columns", "tbData", "tbConfig", "TotalCount", "multipleSelection", "showExpand", "pgLoading", "deleteLoading", "workShopIsOpen", "isOwnerNull", "dialogVisible", "openAddDraft", "saveLoading", "tbLoading", "isCheckAll", "currentComponent", "dWidth", "title", "tb<PERSON><PERSON>", "search", "pageType", "undefined", "tipLabel", "technologyOption", "typeOption", "workingTeam", "pageStatus", "scheduleId", "partComOwnerColumn", "installUnitIdList", "projectId", "areaId", "projectName", "statusType", "expandedKey", "treeLoading", "treeData", "treeParamsComponentType", "filterable", "clickParent", "props", "children", "treeSelectParams", "placeholder", "collapseTags", "clearable", "disabledAdd", "watch", "handler", "n", "o", "checkOwner", "immediate", "computed", "_objectSpread", "isCom", "<PERSON><PERSON><PERSON><PERSON>", "isEdit", "isAdd", "addDraftKey", "filterText", "statusCode", "installName", "_this", "item", "find", "v", "Id", "Name", "isPartPrepare", "getIsPartPrepare", "isNest", "$route", "query", "type", "mounted", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "_yield$GetBOMInfo", "list", "currentBOMInfo", "_this2$$route$query", "install", "wrap", "_callee$", "_context", "prev", "next", "sent", "Code", "console", "log", "initProcessList", "tbDataMap", "craftCodeMap", "pg_type", "status", "model", "pid", "localStorage", "getItem", "unique", "checkWorkshopIsOpen", "fetchData", "mergeConfig", "getInstallUnitIdNameList", "fetchTreeData", "getType", "stop", "methods", "every", "Comp_Import_Detail_Id", "idx", "findIndex", "splice", "ownerColumn", "$message", "message", "push", "comPart", "_this3", "_callee2", "_callee2$", "_context2", "getConfig", "getWorkTeam", "_this4", "_callee3", "configCode", "_callee3$", "_context3", "getTableConfig", "workshopEnabled", "filter", "isVersionFour", "changeColumn", "_this5", "_callee4", "_callee4$", "_context4", "handleNodeClick", "_data$Children", "_this6", "ParentNodes", "Children", "length", "Data", "initData", "_ref", "Project_Id", "getAreaInfo", "$confirm", "confirmButtonText", "cancelButtonText", "then", "catch", "customFilterFun", "node", "arr", "split", "labelVal", "statusVal", "parentNode", "parent", "labels", "level", "concat", "_toConsumableArray", "resultLabel", "resultStatus", "some", "s", "indexOf", "_this7", "_callee5", "resData", "_callee5$", "_context5", "getPartPageList", "getNestPageList", "getComPageList", "initTbData", "fetchTreeDataLocal", "fetchTreeStatus", "_this8", "$router", "MenuId", "meta", "Type", "res", "IsSucceed", "Message", "map", "Is_Directory", "$nextTick", "_", "$refs", "tree", "filterRef", "result", "<PERSON><PERSON><PERSON>", "e", "_this9", "deepFilter", "i", "getNode", "ParentId", "visible", "shouldStop", "key", "getNodeByKey", "<PERSON><PERSON><PERSON><PERSON>", "$store", "tbSelectChange", "array", "records", "_this0", "id", "_res$Data", "_res$Data2", "start", "Demand_Begin_Date", "end", "Demand_End_Date", "getTime", "handleClose", "_this1", "Promise", "resolve", "reject", "Ids", "nestIds", "_list", "Scheduled_Used_Process", "Part_Used_Process", "Workshop_Id", "Scheduled_Workshop_Id", "Workshop_Name", "Scheduled_Workshop_Name", "Technology_Path", "Scheduled_Technology_Path", "chooseCount", "Can_Schduling_Count", "_this10", "_callee6", "_callee6$", "_context6", "Schduling_Plan_Id", "_res$Data3", "Schduling_Plan", "Sch<PERSON>ling_Comps", "Process_List", "Object", "assign", "for<PERSON>ach", "plist", "Process_Code", "changeProcessList", "getStopList", "abrupt", "_this11", "_callee7", "submitObj", "_callee7$", "_context7", "stopMap", "Is_Stop", "row", "$set", "_this12", "_res$Data4", "_res$Data5", "SarePartsModel", "_this13", "teamKey", "arguments", "_row$Technology_Path", "processList", "uuid", "addElementToTbData", "newData", "r", "p", "ele", "index", "code", "getRowUnique", "Working_Team_Id", "max", "getRowUniqueMax", "Count", "setInputMax", "ids", "toString", "Part_Aggregate_Id", "mergeSelectList", "newList", "_this14", "_callee8", "hasUsedPartFlag", "_callee8$", "_context8", "mergeCraftProcess", "element", "cur", "getMergeUniqueRow", "pu<PERSON>", "<PERSON><PERSON><PERSON><PERSON>_Count", "Schduled_Weight", "Weight", "format", "Technology_Code", "Array", "cur<PERSON><PERSON><PERSON><PERSON>", "partUsedProcessArr", "allPartsIncluded", "part", "includes", "join", "add", "showCraftUsedPartResult", "sort", "a", "b", "initRowIndex", "timeEnd", "getUniKey", "_element$Component_Co", "Comp_Code", "InstallUnit_Name", "trim", "Component_Code", "Part_Code", "_element$Component_Co2", "checkForm", "isValidate", "validate", "valid", "saveDraft", "_arguments", "_this15", "_callee9", "_this15$$refs$draft", "isOrder", "checkSuccess", "_this15$getSubmitTbIn", "tableData", "isSuccess", "_callee9$", "_context9", "getSubmitTbInfo", "handleSaveDraft", "saveWorkShop", "_this16", "_callee0", "obj", "_fun", "_callee0$", "_context0", "Area_Id", "Schduling_Model", "_this17", "JSON", "parse", "stringify", "_loop", "msg", "from", "Set", "_loop2", "j", "schduledCount", "groups", "Allocation_Teams", "againCount", "reduce", "acc", "Again_Count", "_list2", "apply", "hasInput", "keys", "startsWith", "_ret", "_this18", "_callee1", "obj<PERSON><PERSON>", "orderSuccess", "_callee1$", "_context1", "hasOwnProperty", "templateScheduleCode", "handleDelete", "_this19", "setTimeout", "selectedUuids", "isSelected", "has", "_this19$$refs$draft", "mergeData", "_this20", "_callee10", "_callee10$", "_context10", "handleSubmit", "_this21", "_this21$getSubmitTbIn", "saveDraftDoSubmit", "_this22", "_callee11", "_this22$formInline", "_isSuccess", "_callee11$", "_context11", "doSubmit", "scheduleCode", "_this23", "schdulingPlanId", "finally", "getWorkShop", "_this24", "_callee12", "origin", "_value$workShop", "Display_Name", "_value$workShop2", "_callee12$", "_context12", "workShop", "set<PERSON>ath", "_value$workShop3", "handleBatchWorkshop", "_this25", "_this26", "_callee13", "codes", "_loop3", "_craftCodeMap", "_callee13$", "_context14", "_loop3$", "_context13", "t0", "t1", "done", "<PERSON><PERSON><PERSON>", "getCraftProcess", "_this27", "gyGroup", "TechnologyCodes", "gyList", "gyMap", "handleAutoDeal", "_this28", "_callee14", "_callee14$", "_context15", "uniqueType", "all", "info", "setLibType", "hasUndefined", "resetWorkTeamMax", "getProcessOption", "workshopId", "_this29", "process", "_this30", "Component_type", "workCode", "WorkCode", "replace", "inputChange", "inputValuesKeys", "endsWith", "val", "curCode", "otherTotal", "x", "sendProcess", "_ref2", "str", "originalPath", "_str", "_this31", "k", "$delete", "checkPermissionTeam", "processStr", "processCode", "_this32", "_callee15", "_callee15$", "_context16", "Grid", "ColumnList", "ownerColumn2", "setColumnDisplay", "Is_Display", "activeCellMethod", "_ref3", "_column$field", "column", "columnIndex", "field", "openBPADialog", "_this33", "IsUnique", "checkIsUniqueWorkshop", "setData", "isUnique", "firstV", "checkHasWorkShop", "hasWorkShop", "handleAddDialog", "_this34", "setPageData", "workingId", "handleSelectMenu", "_this35", "_callee16", "_callee16$", "_context17", "handleSetCraftProcess", "_this36", "_callee17", "showSuccess", "rowList", "workshopIds", "w_process", "workshopPromise", "usedPartFlag", "_callee17$", "_context18", "_defineProperty", "values", "workshop", "flag", "_loop4", "curRow", "workshopProcess", "craftArray", "isIncluded", "hasUsedPart", "checkHasCraftUsedPart", "$alert", "partUsedProcess", "_this37", "handleBatchOwner", "_this38", "setOption", "handleReverse", "checked", "setAllCheckboxRow", "_this39", "getCompTree", "fun", "Label", "_this39$$refs$treeSel", "treeSelectComponentType", "treeDataUpdateFun", "handleDwg", "_this40", "Comp_Id", "Part_Id", "_res$Data6", "fileurl", "File_Url", "window", "open", "setProcessList", "resetInnerForm", "resetFields", "xTable", "clearFilter", "innerFilter", "_this41", "clearCheckboxRow", "getColumnByField", "filters", "option", "updateData", "filterComponentMethod", "_ref5", "Is_Component", "filterSpecMethod", "_ref6", "splitAndClean", "input", "specArray", "Spec", "filterTypeMethod", "_ref7", "filterCodeMethod", "_ref8", "componentTypeFilter", "_this$$refs", "filterFun", "_this42", "installChange", "_this43", "showPartUsedProcess", "handleExport", "exportData", "filename", "Project_Name", "Area_Name"], "sources": ["src/views/PRO/plan-production/schedule-production-new/draft.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 flex-row\">\r\n    <div v-if=\"isAdd\" class=\"cs-left\">\r\n      <ExpandableSection v-model=\"showExpand\">\r\n        <div class=\"cs-tree-wrapper\">\r\n          <div class=\"tree-search\">\r\n            <el-select\r\n              v-model=\"statusType\"\r\n              clearable\r\n              class=\"search-select\"\r\n              placeholder=\"请选择\"\r\n              @change=\"fetchTreeStatus\"\r\n            >\r\n              <el-option label=\"可排产\" value=\"可排产\" />\r\n              <el-option label=\"排产完成\" value=\"排产完成\" />\r\n              <el-option label=\"未导入\" value=\"未导入\" />\r\n            </el-select>\r\n            <el-input\r\n              v-model.trim=\"projectName\"\r\n              placeholder=\"搜索...\"\r\n              size=\"small\"\r\n              clearable\r\n              suffix-icon=\"el-icon-search\"\r\n              @blur=\"fetchTreeDataLocal\"\r\n              @clear=\"fetchTreeDataLocal\"\r\n              @keydown.enter.native=\"fetchTreeDataLocal\"\r\n            />\r\n          </div>\r\n          <el-divider />\r\n          <tree-detail\r\n            ref=\"tree\"\r\n            icon=\"icon-folder\"\r\n            is-custom-filter\r\n            :custom-filter-fun=\"customFilterFun\"\r\n            :loading=\"treeLoading\"\r\n            :tree-data=\"treeData\"\r\n            show-status\r\n            show-detail\r\n            :filter-text=\"filterText\"\r\n            :expanded-key=\"expandedKey\"\r\n            @handleNodeClick=\"handleNodeClick\"\r\n          >\r\n            <template #csLabel=\"{showStatus,data}\">\r\n              <span v-if=\"!data.ParentNodes\" class=\"cs-blue\">({{ data.Code }})</span>{{ data.Label }}\r\n              <template v-if=\"showStatus\">\r\n                <i\r\n                  v-if=\"data.Data[statusCode]\"\r\n                  :class=\"[data.Data[statusCode]=='可排产' ? 'fourGreen' : data.Data[statusCode]=='排产完成' ?'fourOrange':data.Data[statusCode]=='未导入'?'fourRed':'']\"\r\n                >\r\n                  <span>({{ data.Data[statusCode] }})</span>\r\n                </i>\r\n              </template>\r\n            </template>\r\n          </tree-detail>\r\n        </div>\r\n      </ExpandableSection>\r\n    </div>\r\n    <div class=\"cs-right\">\r\n      <el-card v-loading=\"pgLoading\" class=\"box-card h100\" element-loading-text=\"正在处理...\">\r\n        <h4 class=\"topTitle\"><span />基本信息</h4>\r\n        <el-form\r\n          ref=\"formInline\"\r\n          :inline=\"true\"\r\n          :model=\"formInline\"\r\n          class=\"demo-form-inline\"\r\n        >\r\n          <el-form-item v-if=\"!isAdd&&!isNest\" label=\"排产单号\" prop=\"Schduling_Code\">\r\n            <span v-if=\"isView\">{{ formInline.Status === 0 ? '' : formInline.Schduling_Code }}</span>\r\n            <el-input v-else v-model=\"formInline.Schduling_Code\" disabled />\r\n          </el-form-item>\r\n          <el-form-item label=\"计划员\" prop=\"Create_UserName\">\r\n            <span v-if=\"isView\">{{ formInline.Create_UserName }}</span>\r\n            <el-input\r\n              v-else\r\n              v-model=\"formInline.Create_UserName\"\r\n              disabled\r\n            />\r\n          </el-form-item>\r\n          <el-form-item\r\n            label=\"要求完成时间\"\r\n            prop=\"Finish_Date\"\r\n            :rules=\"{ required: true, message: '请选择', trigger: 'change' }\"\r\n          >\r\n            <span v-if=\"isView\">{{ formInline.Finish_Date | timeFormat }}</span>\r\n            <el-date-picker\r\n              v-else\r\n              v-model=\"formInline.Finish_Date\"\r\n              :picker-options=\"pickerOptions\"\r\n              :disabled=\"isView\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              type=\"date\"\r\n              placeholder=\"选择日期\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"!isNest && !isVersionFour\" label=\"批次\" prop=\"Create_UserName\">\r\n            <span v-if=\"isView\">{{ installName }}</span>\r\n            <el-select\r\n              v-else\r\n              v-model=\"formInline.InstallUnit_Id\"\r\n              :disabled=\"!isAdd\"\r\n              filterable\r\n              placeholder=\"请选择\"\r\n              @change=\"installChange\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in installUnitIdList\"\r\n                :key=\"item.Id\"\r\n                :label=\"item.Name\"\r\n                :value=\"item.Id\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"备注\" prop=\"Remark\">\r\n            <span v-if=\"isView\">{{ formInline.Remark }}</span>\r\n            <el-input\r\n              v-else\r\n              v-model=\"formInline.Remark\"\r\n              :disabled=\"isView\"\r\n              style=\"width: 320px\"\r\n              placeholder=\"请输入\"\r\n            />\r\n          </el-form-item>\r\n\r\n        </el-form>\r\n        <el-divider class=\"elDivder\" />\r\n        <div class=\"btn-x\">\r\n          <div v-if=\"!isView\">\r\n            <div ref=\"searchDom\" class=\"search-container\">\r\n              <el-form ref=\"searchForm\" :model=\"innerForm\" inline>\r\n                <el-form-item label-width=\"80px\" prop=\"searchContent\" :label=\"`${comName}名称` \">\r\n                  <el-input\r\n                    v-model=\"innerForm.searchContent\"\r\n                    clearable\r\n                    class=\"input-with-select\"\r\n                    placeholder=\"请输入(空格区分/多个搜索)\"\r\n                    size=\"small\"\r\n                  >\r\n                    <el-select\r\n                      slot=\"prepend\"\r\n                      v-model=\"curSearch\"\r\n                      placeholder=\"请选择\"\r\n                      style=\"width: 100px\"\r\n                    >\r\n                      <el-option label=\"精准查询\" :value=\"1\" />\r\n                      <el-option label=\"模糊查询\" :value=\"0\" />\r\n                    </el-select>\r\n                  </el-input>\r\n                </el-form-item>\r\n\r\n                <el-form-item label-width=\"80px\" :label=\"isCom?`${comName}类型`:'零件类型'\" prop=\"searchComTypeSearch\">\r\n                  <el-tree-select\r\n                    v-if=\"$route.query.status!=='view'\"\r\n                    ref=\"treeSelectComponentType\"\r\n                    v-model=\"innerForm.searchComTypeSearch\"\r\n                    placeholder=\"请选择\"\r\n                    :select-params=\"treeSelectParams\"\r\n                    class=\"cs-tree-x\"\r\n                    :tree-params=\"treeParamsComponentType\"\r\n                    @searchFun=\"componentTypeFilter\"\r\n                  />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"规格\" label-width=\"50px\" prop=\"searchSpecSearch\">\r\n                  <el-input v-model=\"innerForm.searchSpecSearch\" placeholder=\"请输入\" clearable=\"\" />\r\n                </el-form-item>\r\n                <el-form-item v-if=\"isCom\" label=\"是否直发件\" prop=\"searchDirect\">\r\n                  <el-select v-model=\"innerForm.searchDirect\" placeholder=\"请选择\" clearable style=\"width: 120px\">\r\n                    <el-option label=\"是\" :value=\"true\" />\r\n                    <el-option label=\"否\" :value=\"false\" />\r\n                  </el-select>\r\n                </el-form-item>\r\n\r\n                <el-form-item>\r\n                  <el-button type=\"primary\" @click=\"innerFilter\">搜索</el-button>\r\n                  <el-button @click=\"resetInnerForm\">重置</el-button>\r\n                </el-form-item>\r\n              </el-form>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <vxe-toolbar\r\n          ref=\"xToolbar1\"\r\n        >\r\n          <template #buttons>\r\n            <template v-if=\"!isView\">\r\n              <el-button v-if=\"!isNest\" type=\"primary\" :disabled=\"disabledAdd\" @click=\"handleAddDialog()\">添加</el-button>\r\n              <el-button\r\n                v-if=\"workshopEnabled\"\r\n                :disabled=\"!multipleSelection.length\"\r\n                @click=\"handleBatchWorkshop(1)\"\r\n              >分配车间\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"!isCom\"\r\n                :disabled=\"!multipleSelection.length\"\r\n                @click=\"handleSelectMenu('process')\"\r\n              >分配工序\r\n              </el-button>\r\n              <el-dropdown v-if=\"isCom\" style=\"margin:0 10px\" @command=\"handleSelectMenu\">\r\n                <el-button :disabled=\"!multipleSelection.length\" type=\"primary\" plain>\r\n                  分配工序<i class=\"el-icon-arrow-down el-icon--right\" />\r\n                </el-button>\r\n                <el-dropdown-menu slot=\"dropdown\">\r\n                  <el-dropdown-item\r\n                    command=\"process\"\r\n                  >批量分配工序\r\n                  </el-dropdown-item>\r\n                  <el-dropdown-item\r\n                    v-if=\"isCom\"\r\n                    command=\"deal\"\r\n                  >{{ comName }}类型自动分配\r\n                  </el-dropdown-item>\r\n                  <el-dropdown-item\r\n                    v-if=\"isVersionFour\"\r\n                    command=\"craft\"\r\n                  >工艺代码分配\r\n                  </el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </el-dropdown>\r\n              <el-button\r\n                v-if=\"!isCom && !isOwnerNull\"\r\n                :disabled=\"!multipleSelection.length\"\r\n                @click=\"handleBatchOwner(1)\"\r\n              >批量分配领用工序\r\n              </el-button>\r\n              <el-button\r\n                plain\r\n                :disabled=\"!tbData.length\"\r\n                :loading=\"false\"\r\n                @click=\"handleReverse\"\r\n              >反选\r\n              </el-button>\r\n              <el-button\r\n                type=\"danger\"\r\n                plain\r\n                :loading=\"deleteLoading\"\r\n                :disabled=\"!multipleSelection.length\"\r\n                @click=\"handleDelete\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n            <template v-else>\r\n              <el-button :disabled=\"!tbData.length\" @click=\"handleExport\">导出</el-button>\r\n            </template>\r\n          </template>\r\n          <template #tools>\r\n            <DynamicTableFields\r\n              title=\"表格配置\"\r\n              :table-config-code=\"gridCode\"\r\n              @updateColumn=\"changeColumn\"\r\n            />\r\n          </template>\r\n        </vxe-toolbar>\r\n\r\n        <div class=\"tb-x\">\r\n          <!--          activeMethod: activeCellMethod,-->\r\n          <vxe-table\r\n            ref=\"xTable\"\r\n            :key=\"tbKey\"\r\n            :empty-render=\"{name: 'NotData'}\"\r\n            show-header-overflow\r\n            :checkbox-config=\"{checkField: 'checked'}\"\r\n            class=\"cs-vxe-table\"\r\n            :row-config=\"{isCurrent: true, isHover: true}\"\r\n            align=\"left\"\r\n            height=\"100%\"\r\n            :filter-config=\"{showIcon:false}\"\r\n            show-overflow\r\n            :loading=\"tbLoading\"\r\n            stripe\r\n            :scroll-y=\"{enabled: true, gt: 20}\"\r\n            size=\"medium\"\r\n            :edit-config=\"{\r\n              trigger: 'click',\r\n              mode: 'cell',\r\n              showIcon: !isView,\r\n\r\n            }\"\r\n            :data=\"tbData\"\r\n            resizable\r\n            :tooltip-config=\"{ enterable: true }\"\r\n            @checkbox-all=\"tbSelectChange\"\r\n            @checkbox-change=\"tbSelectChange\"\r\n          >\r\n            <vxe-column v-if=\"!isView\" fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n            <template v-for=\"item in columns\">\r\n              <vxe-column\r\n                v-if=\"item.Code === 'Is_Component'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :filters=\"isComponentOptions\"\r\n                :filter-method=\"filterComponentMethod\"\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <el-tag\r\n                    :type=\"row.Is_Component ? 'danger' : 'success'\"\r\n                  >{{ row.Is_Component ? '否' : '是' }}\r\n                  </el-tag>\r\n                </template>\r\n              </vxe-column>\r\n\r\n              <vxe-column\r\n                v-else-if=\"['Type','Type_Name'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :filter-method=\"filterTypeMethod\"\r\n                :field=\"item.Code\"\r\n                :filters=\"filterTypeOption\"\r\n                :title=\"item.Display_Name\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <input v-for=\"(option, index) in column.filters\" :key=\"index\" v-model=\"option.data\" type=\"type\" @input=\"$panel.changeOption($event, !!option.data, option)\">\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row[item.Code] | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"['Comp_Code','Part_Code'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :filter-method=\"filterCodeMethod\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"filterCodeOption\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <input v-for=\"(option, index) in column.filters\" :key=\"index\" v-model=\"option.data\" type=\"type\" @input=\"$panel.changeOption($event, !!option.data, option)\">\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  <el-tag v-if=\"row.Is_Change\" style=\"margin: 8px;\" type=\"danger\">变</el-tag>\r\n                  <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                  <!--                  <el-link v-if=\"row.DwgCount>0\" type=\"primary\" @click.stop=\"handleDwg(row)\"> {{  row[item.Code]  | displayValue }}</el-link>-->\r\n                  <span>{{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Spec'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"specOptions\"\r\n                :filter-method=\"filterSpecMethod\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <input v-for=\"(option, index) in column.filters\" :key=\"index\" v-model=\"option.data\" type=\"type\" @input=\"$panel.changeOption($event, !!option.data, option)\">\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Spec | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Schduled_Weight'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  {{ (row.Schduled_Count * row.Weight).toFixed(2)/1 }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Technology_Path'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :field=\"item.Code\"\r\n                :show-overflow=\"false\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <div class=\"cs-column-row\">\r\n                    <div class=\"cs-ell\">\r\n                      <el-tooltip class=\"item\" effect=\"dark\" :content=\"row.Technology_Path\" placement=\"top\">\r\n                        <span>{{ row.Technology_Path | displayValue }}</span>\r\n                      </el-tooltip>\r\n                    </div>\r\n                    <i\r\n                      v-if=\"!isView\"\r\n                      class=\"el-icon-edit\"\r\n                      @click=\"openBPADialog(2, row)\"\r\n                    />\r\n                  </div>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Part_Used_Process'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :show-overflow=\"false\"\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <div class=\"cs-column-row\">\r\n                    <div class=\"cs-ell\">\r\n                      <el-tooltip class=\"item\" effect=\"dark\" :content=\"row.Part_Used_Process\" placement=\"top\">\r\n                        <span>{{ row.Part_Used_Process | displayValue }}</span>\r\n                      </el-tooltip>\r\n                    </div>\r\n                    <i\r\n                      v-if=\"showPartUsedProcess(row)\"\r\n                      class=\"el-icon-edit\"\r\n                      @click=\"handleBatchOwner(2, row)\"\r\n                    />\r\n                  </div>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Workshop_Name'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :show-overflow=\"false\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <div class=\"cs-column-row\">\r\n                    <div class=\"cs-ell\">\r\n                      <el-tooltip class=\"item\" effect=\"dark\" :content=\"row.Workshop_Name\" placement=\"top\">\r\n                        <span>{{ row.Workshop_Name | displayValue }}</span>\r\n                      </el-tooltip>\r\n                    </div>\r\n                    <i\r\n                      v-if=\"!isView\"\r\n                      class=\"el-icon-edit\"\r\n                      @click=\"handleBatchWorkshop(2, row)\"\r\n                    />\r\n                  </div>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Schduled_Count'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :edit-render=\"{enabled:!isView}\"\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #edit=\"{ row }\">\r\n                  <vxe-input\r\n                    v-model.number=\"row.Schduled_Count\"\r\n                    type=\"integer\"\r\n                    min=\"0\"\r\n                    :max=\"row.chooseCount\"\r\n                  />\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Schduled_Count | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else\r\n                :key=\"item.Id\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                show-overflow=\"tooltip\"\r\n                sortable\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                :min-width=\"item.Width\"\r\n              />\r\n            </template>\r\n\r\n          </vxe-table>\r\n        </div>\r\n        <el-divider v-if=\"!isView\" class=\"elDivder\" />\r\n        <footer v-if=\"!isView\">\r\n          <div class=\"data-info\">\r\n            <el-tag\r\n              size=\"medium\"\r\n              class=\"info-x\"\r\n            >已选 {{ multipleSelection.length }} 条数据\r\n            </el-tag>\r\n            <el-tag v-if=\"tipLabel\" size=\"medium\" class=\"info-x\">{{\r\n              tipLabel\r\n            }}\r\n            </el-tag>\r\n          </div>\r\n          <div>\r\n            <el-button v-if=\"workshopEnabled&&!isNest \" type=\"primary\" @click=\"saveWorkShop\">保存车间分配</el-button>\r\n            <el-button\r\n              v-if=\"!isNest\"\r\n              type=\"primary\"\r\n              :loading=\"saveLoading\"\r\n              @click=\"saveDraft(false)\"\r\n            >保存草稿\r\n            </el-button>\r\n            <el-button :disabled=\"deleteLoading || tbData.some(item=>item.stopFlag)\" @click=\"handleSubmit\">下发任务</el-button>\r\n          </div>\r\n        </footer>\r\n      </el-card>\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"dWidth\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :is-nest=\"isNest\"\r\n        :is-version-four=\"isVersionFour\"\r\n        :is-part-prepare=\"isPartPrepare\"\r\n        :process-list=\"processList\"\r\n        :page-type=\"pageType\"\r\n        :part-type-option=\"typeOption\"\r\n        :level-code=\"levelCode\"\r\n        @close=\"handleClose\"\r\n        @sendProcess=\"sendProcess\"\r\n        @workShop=\"getWorkShop\"\r\n        @refresh=\"fetchData\"\r\n        @setProcessList=\"setProcessList\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      :key=\"addDraftKey\"\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"openAddDraft\"\r\n      :width=\"dWidth\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <add-draft\r\n        ref=\"draft\"\r\n        :level-code=\"levelCode\"\r\n        :com-name=\"comName\"\r\n        :current-ids=\"currentIds\"\r\n        :is-part-prepare=\"isPartPrepare\"\r\n        :area-id=\"areaId\"\r\n        :install-id=\"formInline.InstallUnit_Id\"\r\n        :schedule-id=\"scheduleId\"\r\n        :show-dialog=\"openAddDraft\"\r\n        :page-type=\"pageType\"\r\n        @sendSelectList=\"mergeSelectList\"\r\n        @close=\"handleClose\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { closeTagView, debounce } from '@/utils'\r\nimport BatchProcessAdjust from './components/BatchProcessAdjust'\r\nimport {\r\n  GetCanSchdulingPartList,\r\n  GetCompSchdulingInfoDetail,\r\n  GetDwg,\r\n  GetPartSchdulingInfoDetail,\r\n  GetSchdulingWorkingTeams,\r\n  SaveComponentSchedulingWorkshop,\r\n  SaveCompSchdulingDraft,\r\n  SavePartSchdulingDraftNew,\r\n  SavePartSchedulingWorkshopNew,\r\n  SaveSchdulingTaskById\r\n} from '@/api/PRO/production-task'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nimport AddDraft from './components/addDraft'\r\nimport OwnerProcess from './components/OwnerProcess'\r\nimport Workshop from './components/Workshop.vue'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { uniqueCode } from './constant'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport numeral from 'numeral'\r\nimport { GetLibListType, GetProcessFlowListWithTechnology, GetProcessListBase } from '@/api/PRO/technology-lib'\r\nimport { AreaGetEntity } from '@/api/plm/projects'\r\nimport { mapActions, mapGetters } from 'vuex'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\nimport moment from 'moment'\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport TreeDetail from '@/components/TreeDetail/index.vue'\r\nimport { GetInstallUnitIdNameList, GetProjectAreaTreeList } from '@/api/PRO/project'\r\n\r\nimport { GetCompTypeTree } from '@/api/PRO/factorycheck'\r\nimport { parseOssUrl } from '@/utils/file'\r\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\n\r\nconst SPLIT_SYMBOL = '$_$'\r\nexport default {\r\n  components: { DynamicTableFields, TreeDetail, ExpandableSection, BatchProcessAdjust, AddDraft, Workshop, OwnerProcess },\r\n  data() {\r\n    return {\r\n      bomList: [],\r\n      comName: '',\r\n      levelCode: '',\r\n      isComponentOptions: [\r\n        { label: '是', value: false },\r\n        { label: '否', value: true }\r\n      ],\r\n      specOptions: [{ data: '' }],\r\n      filterTypeOption: [{ data: '' }],\r\n      filterCodeOption: [{ data: '' }],\r\n      pickerOptions: {\r\n        disabledDate(time) {\r\n        }\r\n      },\r\n      innerForm: {\r\n        searchContent: '',\r\n        searchComTypeSearch: '',\r\n        searchSpecSearch: '',\r\n        searchDirect: ''\r\n      },\r\n      curSearch: 1,\r\n      searchType: '',\r\n      formInline: {\r\n        Schduling_Code: '',\r\n        Create_UserName: '',\r\n        Finish_Date: '',\r\n        InstallUnit_Id: '',\r\n        Remark: ''\r\n      },\r\n      total: 0,\r\n      currentIds: '',\r\n      gridCode: '',\r\n      columns: [],\r\n      tbData: [],\r\n      tbConfig: {},\r\n      TotalCount: 0,\r\n      multipleSelection: [],\r\n      showExpand: true,\r\n      pgLoading: false,\r\n      deleteLoading: false,\r\n      workShopIsOpen: false,\r\n      isOwnerNull: false,\r\n      dialogVisible: false,\r\n      openAddDraft: false,\r\n      saveLoading: false,\r\n      tbLoading: false,\r\n      isCheckAll: false,\r\n      currentComponent: '',\r\n      dWidth: '25%',\r\n      title: '',\r\n      tbKey: 100,\r\n      search: () => ({}),\r\n      pageType: undefined,\r\n      tipLabel: '',\r\n      technologyOption: [],\r\n      typeOption: [],\r\n      workingTeam: [],\r\n      pageStatus: undefined,\r\n      scheduleId: '',\r\n      partComOwnerColumn: null,\r\n\r\n      installUnitIdList: [],\r\n      projectId: '',\r\n      areaId: '',\r\n      projectName: '',\r\n      statusType: '可排产',\r\n      expandedKey: '',\r\n      treeLoading: false,\r\n      treeData: [],\r\n      treeParamsComponentType: {\r\n        'default-expand-all': true,\r\n        'check-strictly': true,\r\n        filterable: true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      },\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        collapseTags: true,\r\n        clearable: true\r\n      },\r\n      disabledAdd: true\r\n    }\r\n  },\r\n  watch: {\r\n    'tbData.length': {\r\n      handler(n, o) {\r\n        this.checkOwner()\r\n      },\r\n      immediate: false\r\n    }\r\n\r\n  },\r\n\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    },\r\n    isView() {\r\n      return this.pageStatus === 'view'\r\n    },\r\n    isEdit() {\r\n      return this.pageStatus === 'edit'\r\n    },\r\n    isAdd() {\r\n      return this.pageStatus === 'add'\r\n    },\r\n    addDraftKey() {\r\n      return this.expandedKey + this.formInline.InstallUnit_Id\r\n    },\r\n    filterText() {\r\n      return this.projectName + SPLIT_SYMBOL + this.statusType\r\n    },\r\n    statusCode() {\r\n      return this.isCom ? 'Comp_Schdule_Status' : 'Part_Schdule_Status'\r\n    },\r\n    installName() {\r\n      const item = this.installUnitIdList.find(v => v.Id === this.formInline.InstallUnit_Id)\r\n      if (item) {\r\n        return item.Name\r\n      } else {\r\n        return ''\r\n      }\r\n    },\r\n    isPartPrepare() {\r\n      return this.getIsPartPrepare && !this.isCom\r\n    },\r\n    isNest() {\r\n      return this.$route.query.type === '1'\r\n    },\r\n    ...mapGetters('factoryInfo', ['workshopEnabled', 'getIsPartPrepare']),\r\n    ...mapGetters('schedule', ['processList', 'nestIds']),\r\n    ...mapGetters('tenant', ['isVersionFour'])\r\n  },\r\n  async mounted() {\r\n    const { list, comName, currentBOMInfo } = await GetBOMInfo(-1)\r\n    this.bomList = list || []\r\n    this.comName = comName\r\n    this.levelCode = currentBOMInfo?.Code\r\n    console.log('currentBOMInfo', currentBOMInfo)\r\n    console.log('levelCode', this.levelCode)\r\n    this.initProcessList()\r\n    this.tbDataMap = {}\r\n    this.craftCodeMap = {}\r\n    this.pageType = this.$route.query.pg_type\r\n    this.pageStatus = this.$route.query.status\r\n    this.model = this.$route.query.model\r\n    this.scheduleId = this.$route.query.pid || ''\r\n    // // this.formInline.Create_UserName = this.$store.getters.name\r\n    // // 框架问题引起store数据丢失，已反馈，结果：此处先使用localStorage\r\n    this.formInline.Create_UserName = localStorage.getItem('UserAccount')\r\n    // if (!this.isCom) {\r\n    //   this.getPartType()\r\n    // } else {\r\n    // }\r\n\r\n    this.unique = uniqueCode()\r\n    this.checkWorkshopIsOpen()\r\n\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    await this.mergeConfig()\r\n    if (this.isView || this.isEdit) {\r\n      const { areaId, install } = this.$route.query\r\n      this.areaId = areaId\r\n      this.formInline.InstallUnit_Id = install\r\n      this.getInstallUnitIdNameList()\r\n      this.fetchData()\r\n    }\r\n\r\n    if (this.isAdd) {\r\n      this.fetchTreeData()\r\n      this.getType()\r\n    }\r\n    if (this.isEdit) {\r\n      this.getType()\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions('schedule', ['changeProcessList', 'initProcessList']),\r\n    checkOwner() {\r\n      if (this.isCom) return\r\n      this.isOwnerNull = this.tbData.every(v => !v.Comp_Import_Detail_Id) && !this.isNest\r\n      const idx = this.columns.findIndex(v => v.Code === 'Part_Used_Process')\r\n      if (this.isOwnerNull) {\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n      } else {\r\n        if (idx === -1) {\r\n          if (!this.ownerColumn) {\r\n            this.$message({\r\n              message: '列表配置字段缺少零件领用工序字段',\r\n              type: 'success'\r\n            })\r\n            return\r\n          }\r\n          this.columns.push(this.ownerColumn)\r\n        }\r\n        this.comPart = true\r\n      }\r\n    },\r\n    async mergeConfig() {\r\n      await this.getConfig()\r\n      await this.getWorkTeam()\r\n    },\r\n    async getConfig() {\r\n      let configCode = ''\r\n      if (this.isNest) {\r\n        if (this.isView) {\r\n          configCode = 'PRONestingScheduleDetail'\r\n        } else {\r\n          configCode = 'PRONestingScheduleConfig'\r\n        }\r\n      } else {\r\n        configCode = (this.isView ? 'PROComViewPageTbConfig' : 'PROComDraftPageTbConfig')\r\n      }\r\n      this.gridCode = configCode\r\n      await this.getTableConfig(configCode)\r\n      if (!this.workshopEnabled) {\r\n        this.columns = this.columns.filter(v => v.Code !== 'Workshop_Name')\r\n      }\r\n      if (!this.isVersionFour) {\r\n        this.columns = this.columns.filter(v => v.Code !== 'Technology_Code')\r\n      }\r\n      this.checkOwner()\r\n    },\r\n    async changeColumn() {\r\n      await this.getTableConfig(this.gridCode)\r\n      this.tbKey++\r\n    },\r\n    handleNodeClick(data) {\r\n      this.expandedKey = data.Id\r\n      if (this.areaId === data.Id) {\r\n        this.disabledAdd = false\r\n        return\r\n      }\r\n      this.disabledAdd = true\r\n      if (!data.ParentNodes || data.Children?.length > 0) {\r\n        return\r\n      }\r\n      if (data?.Data[this.statusCode] === '未导入') {\r\n        this.$message({\r\n          message: '清单未导入，请联系深化人员导入清单',\r\n          type: 'warning'\r\n        })\r\n\r\n        return\r\n      }\r\n\r\n      const initData = ({ Data }) => {\r\n        this.areaId = Data.Id\r\n        this.projectId = Data.Project_Id\r\n        this.expandedKey = this.areaId\r\n        this.formInline.Finish_Date = ''\r\n        this.formInline.InstallUnit_Id = ''\r\n        this.formInline.Remark = ''\r\n        this.tbData = []\r\n        this.getAreaInfo()\r\n        this.getInstallUnitIdNameList()\r\n      }\r\n\r\n      if (this.tbData.length) {\r\n        this.$confirm('切换区域右侧数据清空，是否确认?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          initData(data)\r\n          this.disabledAdd = false\r\n          this.tbDataMap = {}\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n      } else {\r\n        this.disabledAdd = false\r\n        initData(data)\r\n      }\r\n    },\r\n\r\n    customFilterFun(value, data, node) {\r\n      // console.log('customFilterFun', value, data, node)\r\n\r\n      const arr = value.split(SPLIT_SYMBOL)\r\n      const labelVal = arr[0]\r\n      const statusVal = arr[1]\r\n      if (!value) return true\r\n      let parentNode = node.parent\r\n      let labels = [node.label]\r\n      let status = [data.Data[this.statusCode]]\r\n      let level = 1\r\n      while (level < node.level) {\r\n        labels = [...labels, parentNode.label]\r\n        status = [...status, data.Data[this.statusCode]]\r\n        parentNode = parentNode.parent\r\n        level++\r\n      }\r\n      labels = labels.filter(v => !!v)\r\n      status = status.filter(v => !!v)\r\n      let resultLabel = true\r\n      let resultStatus = true\r\n      if (this.statusType) {\r\n        resultStatus = status.some(s => s.indexOf(statusVal) !== -1)\r\n      }\r\n      if (this.projectName) {\r\n        resultLabel = labels.some(s => s.indexOf(labelVal) !== -1)\r\n      }\r\n      return resultLabel && resultStatus\r\n    },\r\n    async fetchData() {\r\n      this.tbLoading = true\r\n      let resData = null\r\n      if (this.isNest) {\r\n        if (this.isView) {\r\n          resData = await this.getPartPageList()\r\n        } else {\r\n          resData = await this.getNestPageList()\r\n        }\r\n      } else {\r\n        resData = await this.getComPageList()\r\n        console.log('resData', resData)\r\n      }\r\n\r\n      this.initTbData(resData)\r\n      this.tbLoading = false\r\n    },\r\n    fetchTreeDataLocal() {\r\n      // this.filterText = this.projectName\r\n    },\r\n    fetchTreeStatus() {\r\n      // this.filterText = this.statusType\r\n    },\r\n    fetchTreeData() {\r\n      this.treeLoading = true\r\n      console.log('78,this.$route.meta', this.$route, this.$router)\r\n      GetProjectAreaTreeList({ MenuId: this.$route.meta.Id, projectName: this.projectName, Type: this.isCom ? 1 : 2 }).then((res) => {\r\n        if (!res.IsSucceed) {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.treeData = []\r\n          this.treeLoading = false\r\n          return\r\n        }\r\n        if (res.Data.length === 0) {\r\n          this.treeLoading = false\r\n          this.treeData = []\r\n          return\r\n        }\r\n        const resData = res.Data.map(item => {\r\n          item.Is_Directory = true\r\n          return item\r\n        })\r\n        this.treeData = resData\r\n        this.$nextTick(_ => {\r\n          this.$refs.tree.filterRef(this.filterText)\r\n          const result = this.setKey()\r\n          if (!result) {\r\n            this.pgLoading = false\r\n          }\r\n        })\r\n        this.treeLoading = false\r\n      }).catch((e) => {\r\n        console.log('catche', e)\r\n        this.treeLoading = false\r\n        this.treeData = []\r\n      })\r\n    },\r\n    setKey() {\r\n      const deepFilter = (tree) => {\r\n        for (let i = 0; i < tree.length; i++) {\r\n          const item = tree[i]\r\n          const { Data, Children } = item\r\n          const node = getNode(Data.Id)\r\n          if (Data.ParentId && !Children?.length && node.visible) {\r\n            this.handleNodeClick(item)\r\n            return true\r\n          } else {\r\n            if (Children?.length) {\r\n              const shouldStop = deepFilter(Children)\r\n              if (shouldStop) return true\r\n            }\r\n          }\r\n        }\r\n        return false\r\n      }\r\n      const getNode = (key) => {\r\n        return this.$refs['tree'].getNodeByKey(key)\r\n      }\r\n      return deepFilter(this.treeData)\r\n    },\r\n    closeView() {\r\n      closeTagView(this.$store, this.$route)\r\n    },\r\n    checkWorkshopIsOpen() {\r\n      this.workShopIsOpen = true\r\n    },\r\n    tbSelectChange(array) {\r\n      this.multipleSelection = array.records\r\n    },\r\n    getAreaInfo() {\r\n      this.formInline.Finish_Date = ''\r\n      AreaGetEntity({\r\n        id: this.areaId\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (!res.Data) {\r\n            return []\r\n          }\r\n\r\n          const start = moment(res.Data?.Demand_Begin_Date)\r\n          const end = moment(res.Data?.Demand_End_Date)\r\n          this.pickerOptions.disabledDate = (time) => {\r\n            return time.getTime() < start || time.getTime() > end\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n      this.openAddDraft = false\r\n    },\r\n    getNestPageList() {\r\n      return new Promise((resolve, reject) => {\r\n        GetCanSchdulingPartList({\r\n          Ids: this.nestIds\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            const _list = res?.Data || []\r\n            const list = _list.map(v => {\r\n              if (v.Scheduled_Used_Process) {\r\n                // 已存在操作过数据\r\n                v.Part_Used_Process = v.Scheduled_Used_Process\r\n              }\r\n              // v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n              v.Workshop_Id = v.Scheduled_Workshop_Id\r\n              v.Workshop_Name = v.Scheduled_Workshop_Name\r\n              v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n              v.chooseCount = v.Can_Schduling_Count\r\n              return v\r\n            })\r\n\r\n            resolve(list)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    async getComPageList() {\r\n      const {\r\n        pid\r\n      } = this.$route.query\r\n      const result = await GetCompSchdulingInfoDetail({\r\n        Schduling_Plan_Id: pid\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const { Schduling_Plan, Schduling_Comps, Process_List } = res.Data\r\n          this.formInline = Object.assign(this.formInline, Schduling_Plan)\r\n          Process_List.forEach(item => {\r\n            const plist = {\r\n              key: item.Process_Code,\r\n              value: item\r\n            }\r\n            this.changeProcessList(plist)\r\n          })\r\n          const list = Schduling_Comps.map(v => {\r\n            v.chooseCount = v.Can_Schduling_Count\r\n            return v\r\n          })\r\n          this.getStopList(list)\r\n          return list || []\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      return result || []\r\n    },\r\n    async getStopList(list) {\r\n      console.log('getStopList', list)\r\n      const submitObj = list.map(item => {\r\n        return {\r\n          Id: item.Comp_Import_Detail_Id,\r\n          Type: 2\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = !!item.Is_Stop\r\n          })\r\n          list.forEach(row => {\r\n            if (stopMap[row.Comp_Import_Detail_Id]) {\r\n              this.$set(row, 'stopFlag', stopMap[row.Comp_Import_Detail_Id])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getPartPageList() {\r\n      return new Promise((resolve, reject) => {\r\n        const {\r\n          pid\r\n        } = this.$route.query\r\n        GetPartSchdulingInfoDetail({\r\n          Schduling_Plan_Id: pid\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            const SarePartsModel = res.Data?.SarePartsModel.map(v => {\r\n              if (v.Scheduled_Used_Process) {\r\n                // 已存在操作过数据\r\n                v.Part_Used_Process = v.Scheduled_Used_Process\r\n              }\r\n              v.chooseCount = v.Can_Schduling_Count\r\n              return v\r\n            })\r\n            this.formInline = Object.assign(this.formInline, res.Data?.Schduling_Plan)\r\n            resolve(SarePartsModel || [])\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    initTbData(list, teamKey = 'Allocation_Teams') {\r\n      this.tbData = list.map(row => {\r\n        const processList = row.Technology_Path?.split('/') || []\r\n        row.uuid = uuidv4()\r\n        this.addElementToTbData(row)\r\n        if (row[teamKey]) {\r\n          const newData = row[teamKey].filter((r) => processList.findIndex((p) => r.Process_Code === p) !== -1)\r\n          newData.forEach((ele, index) => {\r\n            const code = this.getRowUnique(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n            const max = this.getRowUniqueMax(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n            row[code] = ele.Count\r\n            row[max] = 0\r\n          })\r\n        }\r\n        this.setInputMax(row)\r\n        return row\r\n      })\r\n      let ids = ''\r\n      if (this.isCom) {\r\n        ids = this.tbData.map(v => v.Comp_Import_Detail_Id).toString()\r\n      } else {\r\n        ids = this.tbData.map(v => v.Part_Aggregate_Id).toString()\r\n      }\r\n      this.currentIds = ids\r\n    },\r\n    async mergeSelectList(newList) {\r\n      if (this.isVersionFour) {\r\n        await this.mergeCraftProcess(newList)\r\n      }\r\n      console.time('mergeSelectListTime')\r\n      let hasUsedPartFlag = true\r\n      newList.forEach((element, index) => {\r\n        const cur = this.getMergeUniqueRow(element)\r\n        if (!cur) {\r\n          element.puuid = element.uuid\r\n          element.Schduled_Count = element.chooseCount\r\n          element.Schduled_Weight = numeral(element.chooseCount * element.Weight).format('0.[00]')\r\n          if (this.isVersionFour && !element.Technology_Path) {\r\n            /*          if (this.craftCodeMap[element.Technology_Code] && this.craftCodeMap[element.Technology_Code] instanceof Array) {\r\n              const curPathArr = this.craftCodeMap[element.Technology_Code]\r\n              if (element.Part_Used_Process && !curPathArr.includes(element.Part_Used_Process)) {\r\n                hasUsedPartFlag = false\r\n              } else {\r\n                element.Technology_Path = curPathArr.join('/')\r\n              }\r\n            }*/\r\n            if (this.craftCodeMap[element.Technology_Code] instanceof Array) {\r\n              const curPathArr = this.craftCodeMap[element.Technology_Code]\r\n              if (element.Part_Used_Process) {\r\n                const partUsedProcessArr = element.Part_Used_Process.split(',')\r\n                const allPartsIncluded = partUsedProcessArr.every(part => curPathArr.includes(part))\r\n\r\n                if (!allPartsIncluded) {\r\n                  hasUsedPartFlag = false\r\n                } else {\r\n                  element.Technology_Path = curPathArr.join('/')\r\n                }\r\n              } else {\r\n                element.Technology_Path = curPathArr.join('/')\r\n              }\r\n            }\r\n          }\r\n          this.tbData.push(element)\r\n          this.addElementToTbData(element)\r\n          return\r\n        }\r\n\r\n        cur.puuid = element.uuid\r\n\r\n        cur.Schduled_Count += element.chooseCount\r\n        cur.Schduled_Weight = numeral(cur.Schduled_Weight).add(element.chooseCount * element.Weight).format('0.[00]')\r\n        if (!cur.Technology_Path) {\r\n          return\r\n        }\r\n        this.setInputMax(cur)\r\n      })\r\n      this.showCraftUsedPartResult(hasUsedPartFlag)\r\n\r\n      // if (this.isCom) {\r\n      //   this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n      // } else {\r\n      //   this.tbData.sort((a, b) => a.Part_Code.localeCompare(b.Part_Code))\r\n      // }\r\n      this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n      console.timeEnd('mergeSelectListTime')\r\n    },\r\n    addElementToTbData(element) {\r\n      const key = this.getUniKey(element)\r\n      this.tbDataMap[key] = element\r\n    },\r\n    getMergeUniqueRow(element) {\r\n      const key = this.getUniKey(element)\r\n      return this.tbDataMap[key]\r\n    },\r\n    getUniKey(element) {\r\n      if (this.isVersionFour) {\r\n        return this.isCom ? (element.Comp_Code + element.InstallUnit_Name).toString().trim() : ((element.Component_Code ?? '') + element.Part_Code + element.Part_Aggregate_Id)\r\n      } else {\r\n        return this.isCom ? element.Comp_Code : ((element.Component_Code ?? '') + element.Part_Code + element.Part_Aggregate_Id)\r\n      }\r\n    },\r\n    checkForm() {\r\n      let isValidate = true\r\n      this.$refs['formInline'].validate((valid) => {\r\n        if (!valid) isValidate = false\r\n      })\r\n      return isValidate\r\n    },\r\n    async saveDraft(isOrder = false) {\r\n      const checkSuccess = this.checkForm()\r\n      if (!checkSuccess) return false\r\n      const { tableData, status } = this.getSubmitTbInfo()\r\n      if (!status) return false\r\n      if (!isOrder) {\r\n        this.saveLoading = true\r\n      }\r\n\r\n      const isSuccess = await this.handleSaveDraft(tableData, isOrder)\r\n      console.log('isSuccess', isSuccess)\r\n      if (!isSuccess) return false\r\n      if (isOrder) return isSuccess\r\n      this.$refs['draft']?.fetchData()\r\n      this.saveLoading = false\r\n    },\r\n    async saveWorkShop() {\r\n      const checkSuccess = this.checkForm()\r\n      if (!checkSuccess) return false\r\n      const obj = {}\r\n      if (!this.tbData.length) {\r\n        this.$message({\r\n          message: '数据不能为空',\r\n          type: 'success'\r\n        })\r\n        return\r\n      }\r\n      if (this.isCom) {\r\n        obj.Schduling_Comps = this.tbData\r\n      } else {\r\n        obj.SarePartsModel = this.tbData\r\n      }\r\n      if (this.isEdit) {\r\n        obj.Schduling_Plan = this.formInline\r\n      } else {\r\n        obj.Schduling_Plan = {\r\n          ...this.formInline,\r\n          Project_Id: this.projectId,\r\n          Area_Id: this.areaId,\r\n          Schduling_Model: this.model // 1构件单独排产，2零件单独排产，3构/零件一起排产\r\n        }\r\n      }\r\n      this.pgLoading = true\r\n      const _fun = this.isCom ? SaveComponentSchedulingWorkshop : SavePartSchedulingWorkshopNew\r\n      _fun(obj).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.pgLoading = false\r\n          this.$message({\r\n            message: '保存成功',\r\n            type: 'success'\r\n          })\r\n          this.closeView()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.pgLoading = false\r\n        }\r\n      })\r\n    },\r\n    getSubmitTbInfo() {\r\n      // 处理上传的数据\r\n      let tableData = JSON.parse(JSON.stringify(this.tbData))\r\n      tableData = tableData.filter(item => item.Schduled_Count > 0)\r\n      for (let i = 0; i < tableData.length; i++) {\r\n        const element = tableData[i]\r\n        let list = []\r\n        if (!element.Technology_Path) {\r\n          this.$message({\r\n            message: '工序不能为空',\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        if (this.isPartPrepare && !element.Part_Used_Process && element.Type !== 'Direct' && this.comPart) {\r\n          const msg = '领用工序不能为空'\r\n          if (this.isNest) {\r\n            if (element.Comp_Import_Detail_Id) {\r\n              this.$message({\r\n                message: msg,\r\n                type: 'warning'\r\n              })\r\n              return { status: false }\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: msg,\r\n              type: 'warning'\r\n            })\r\n            return { status: false }\r\n          }\r\n        }\r\n        // if (!this.isCom && element.Comp_Import_Detail_Id && !element.Part_Used_Process) {\r\n        //   // 零构件 零件单独排产\r\n        //   this.$message({\r\n        //     message: '零件领用工序不能为空',\r\n        //     type: 'warning'\r\n        //   })\r\n        //   return { status: false }\r\n        // }\r\n        if (element.Scheduled_Technology_Path && element.Scheduled_Technology_Path !== element.Technology_Path) {\r\n          this.$message({\r\n            message: `请和该区域批次下已排产同${this.isCom ? `${this.comName}` : '零件'}保持工序一致`,\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        if (element.Scheduled_Used_Process && element.Scheduled_Used_Process !== element.Part_Used_Process) {\r\n          this.$message({\r\n            message: `请和该区域批次下已排产同零件领用工序保持一致`,\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        const processList = Array.from(new Set(element.Technology_Path.split('/')))\r\n        /* processList.forEach(code => {\r\n          const groups = this.workingTeam.filter(v => v.Process_Code === code)\r\n          const groupsList = groups.map(group => {\r\n            const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)\r\n            const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)\r\n            const obj = {\r\n              Team_Task_Id: element.Team_Task_Id,\r\n              Comp_Code: element.Comp_Code,\r\n              Again_Count: +element[uCode] || 0, // 不填，后台让传0\r\n              Part_Code: this.isCom ? null : '',\r\n              Process_Code: code,\r\n              Technology_Path: element.Technology_Path,\r\n              Working_Team_Id: group.Working_Team_Id,\r\n              Working_Team_Name: group.Working_Team_Name\r\n            }\r\n            delete element[uCode]\r\n            delete element[uMax]\r\n            return obj\r\n          })\r\n          list.push(...groupsList)\r\n        })*/\r\n        for (let j = 0; j < processList.length; j++) {\r\n          const code = processList[j]\r\n          const schduledCount = element.Schduled_Count || 0\r\n          let groups = []\r\n          if (element.Allocation_Teams) {\r\n            groups = element.Allocation_Teams.filter(v => v.Process_Code === code)\r\n          }\r\n          const againCount = groups.reduce((acc, cur) => {\r\n            return acc + (cur.Again_Count || 0)\r\n          }, 0)\r\n          if (againCount > schduledCount) {\r\n            list = []\r\n            break\r\n          } else {\r\n            list.push(...groups)\r\n          }\r\n        }\r\n        const hasInput = Object.keys(element).filter(_ => _.startsWith(element['uuid']))\r\n        hasInput.forEach((item) => {\r\n          delete element[item]\r\n        })\r\n        delete element['uuid']\r\n        delete element['_X_ROW_KEY']\r\n        delete element['puuid']\r\n        element.Allocation_Teams = list\r\n      }\r\n      return { tableData, status: true }\r\n    },\r\n    async handleSaveDraft(tableData, isOrder) {\r\n      console.log('保存草稿')\r\n      const _fun = this.isCom ? SaveCompSchdulingDraft : SavePartSchdulingDraftNew\r\n      const obj = {}\r\n      if (this.isCom) {\r\n        obj.Schduling_Comps = tableData\r\n        const p = []\r\n        for (const objKey in this.processList) {\r\n          if (this.processList.hasOwnProperty(objKey)) {\r\n            p.push(this.processList[objKey])\r\n          }\r\n        }\r\n        obj.Process_List = p\r\n      } else {\r\n        obj.SarePartsModel = tableData\r\n      }\r\n      if (this.isEdit) {\r\n        obj.Schduling_Plan = this.formInline\r\n      } else {\r\n        obj.Schduling_Plan = {\r\n          ...this.formInline,\r\n          Project_Id: this.projectId,\r\n          Area_Id: this.areaId,\r\n          Schduling_Model: this.model // 1构件单独排产，2零件单独排产，3构/零件一起排产\r\n        }\r\n      }\r\n      let orderSuccess = false\r\n      console.log('obj', obj)\r\n\r\n      await _fun(obj).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (!isOrder) {\r\n            this.pgLoading = false\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            this.closeView()\r\n          } else {\r\n            this.templateScheduleCode = res.Data\r\n            orderSuccess = true\r\n            console.log('保存草稿成功 ')\r\n          }\r\n        } else {\r\n          this.saveLoading = false\r\n          this.pgLoading = false\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      console.log('结束 ')\r\n      return orderSuccess\r\n    },\r\n    handleDelete() {\r\n      this.deleteLoading = true\r\n      setTimeout(() => {\r\n        const selectedUuids = new Set(this.multipleSelection.map(v => v.uuid))\r\n        this.tbData = this.tbData.filter(item => {\r\n          const isSelected = selectedUuids.has(item.uuid)\r\n          if (isSelected) {\r\n            const key = this.getUniKey(item)\r\n            delete this.tbDataMap[key]\r\n          }\r\n          return !isSelected\r\n        })\r\n        this.$nextTick(_ => {\r\n          this.$refs['draft']?.mergeData(this.multipleSelection)\r\n          this.multipleSelection = []\r\n        })\r\n        this.deleteLoading = false\r\n      }, 0)\r\n    },\r\n    async getWorkTeam() {\r\n      await GetSchdulingWorkingTeams({\r\n        type: this.isCom ? 1 : 2\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.workingTeam = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      this.$refs['formInline'].validate((valid) => {\r\n        if (!valid) return\r\n        const { tableData, status } = this.getSubmitTbInfo()\r\n        if (!status) return\r\n        this.$confirm('是否提交当前数据?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.saveDraftDoSubmit(tableData)\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n      })\r\n    },\r\n    async saveDraftDoSubmit() {\r\n      this.pgLoading = true\r\n      if (this.formInline?.Schduling_Code) {\r\n        const isSuccess = await this.saveDraft(true)\r\n        console.log('saveDraftDoSubmit', isSuccess)\r\n        isSuccess && this.doSubmit(this.formInline.Id)\r\n      } else {\r\n        const isSuccess = await this.saveDraft(true)\r\n        isSuccess && this.doSubmit(this.templateScheduleCode)\r\n      }\r\n    },\r\n    doSubmit(scheduleCode) {\r\n      SaveSchdulingTaskById({\r\n        schdulingPlanId: scheduleCode\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '下达成功',\r\n            type: 'success'\r\n          })\r\n          this.closeView()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.pgLoading = false\r\n      }).catch(_ => {\r\n        this.pgLoading = false\r\n      })\r\n    },\r\n    async getWorkShop(value) {\r\n      const {\r\n        origin,\r\n        row,\r\n        workShop: {\r\n          Id,\r\n          Display_Name\r\n        }\r\n      } = value\r\n      if (origin === 2) {\r\n        if (value.workShop?.Id) {\r\n          row.Workshop_Name = Display_Name\r\n          row.Workshop_Id = Id\r\n          this.setPath(row, Id)\r\n        } else {\r\n          row.Workshop_Name = ''\r\n          row.Workshop_Id = ''\r\n        }\r\n      } else {\r\n        // const gyMap = await this.getCraftProcess()\r\n        // const _process = await this.getProcessOption(value.workShop?.Id)\r\n        this.multipleSelection.forEach(item => {\r\n          if (value.workShop?.Id) {\r\n            item.Workshop_Name = Display_Name\r\n            item.Workshop_Id = Id\r\n            this.setPath(item, Id)\r\n          } else {\r\n            item.Workshop_Name = ''\r\n            item.Workshop_Id = ''\r\n          }\r\n        })\r\n      }\r\n    },\r\n    setPath(row, Id) {\r\n      if (row?.Scheduled_Workshop_Id) {\r\n        if (row.Scheduled_Workshop_Id !== Id) {\r\n          row.Technology_Path = ''\r\n        }\r\n      } else {\r\n        row.Technology_Path = ''\r\n      }\r\n    },\r\n    handleBatchWorkshop(origin, row) {\r\n      this.title = origin === 1 ? '批量分配车间' : '分配车间'\r\n      this.currentComponent = 'Workshop'\r\n      this.dWidth = '30%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].fetchData(origin, row, this.multipleSelection)\r\n      })\r\n    },\r\n    async mergeCraftProcess(list) {\r\n      let codes = [...new Set(list.map(v => v.Technology_Code))]\r\n      for (const key in this.craftCodeMap) {\r\n        if (this.craftCodeMap.hasOwnProperty(key)) {\r\n          codes = codes.filter(code => code !== key)\r\n        }\r\n      }\r\n      const _craftCodeMap = await this.getCraftProcess(codes)\r\n      Object.assign(this.craftCodeMap, _craftCodeMap)\r\n    },\r\n    getCraftProcess(gyGroup = []) {\r\n      gyGroup = gyGroup.filter(v => !!v)\r\n      if (!gyGroup.length) return Promise.resolve({})\r\n      return new Promise((resolve, reject) => {\r\n        GetProcessFlowListWithTechnology({\r\n          TechnologyCodes: gyGroup,\r\n          Type: 1\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            const gyList = res.Data || []\r\n            const gyMap = gyList.reduce((acc, item) => {\r\n              acc[item.Code] = item.Technology_Path\r\n              return acc\r\n            }, {})\r\n            console.log('gyMap', gyMap)\r\n            resolve(gyMap)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    /*   checkProcess(pList, flowList) {\r\n      return flowList.every(item => pList.includes(item))\r\n    },*/\r\n    async handleAutoDeal() {\r\n      /*      if (this.workshopEnabled) {\r\n        const hasWorkShop = this.checkHasWorkShop(1, this.multipleSelection)\r\n        if (!hasWorkShop) return\r\n      }*/\r\n\r\n      this.$confirm(`是否将选中数据按${this.comName}类型自动分配`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        if (this.workshopEnabled) {\r\n          const p = this.multipleSelection.map(item => {\r\n            return {\r\n              uniqueType: `${item.Type}$_$${item.Workshop_Id}`\r\n            }\r\n          })\r\n          const codes = Array.from(new Set(p.map(v => v.uniqueType)))\r\n          const objKey = {}\r\n          Promise.all(codes.map(v => {\r\n            const info = v.split('$_$')\r\n            return this.setLibType(info[0], info[1])\r\n          })\r\n          ).then(res => {\r\n            const hasUndefined = res.some(item => item == undefined)\r\n            if (hasUndefined) {\r\n              this.$message({\r\n                message: `所选车间内工序班组与${this.comName}类型工序不匹配，请手动分配工序`,\r\n                type: 'warning'\r\n              })\r\n            }\r\n\r\n            res.forEach((element, idx) => {\r\n              objKey[codes[idx]] = element\r\n            })\r\n            this.multipleSelection.forEach((element) => {\r\n              element.Technology_Path = objKey[`${element.Type}$_$${element.Workshop_Id}`]\r\n              this.resetWorkTeamMax(element, element.Technology_Path)\r\n            })\r\n          })\r\n        } else {\r\n          const p = this.multipleSelection.map(item => item.Type)\r\n          const codes = Array.from(new Set(p))\r\n          const objKey = {}\r\n\r\n          Promise.all(codes.map(v => {\r\n            return this.setLibType(v)\r\n          })).then(res => {\r\n            res.forEach((element, idx) => {\r\n              objKey[codes[idx]] = element\r\n            })\r\n            this.multipleSelection.forEach((element) => {\r\n              element.Technology_Path = objKey[element.Type]\r\n              this.resetWorkTeamMax(element, element.Technology_Path)\r\n            })\r\n          })\r\n        }\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    getProcessOption(workshopId) {\r\n      return new Promise((resolve, reject) => {\r\n        GetProcessListBase({\r\n          workshopId: workshopId,\r\n          type: 1 // 0:全部，工艺类型1：构件工艺，2：零件工艺\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            const process = res.Data.map(v => v.Code)\r\n            resolve(process)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    setLibType(code, workshopId) {\r\n      return new Promise((resolve) => {\r\n        const obj = {\r\n          Component_type: code,\r\n          type: 1\r\n        }\r\n        if (this.workshopEnabled) {\r\n          obj.workshopId = workshopId\r\n        }\r\n        GetLibListType(obj).then(res => {\r\n          if (res.IsSucceed) {\r\n            if (res.Data.Data && res.Data.Data.length) {\r\n              const info = res.Data.Data[0]\r\n              const workCode = info.WorkCode && info.WorkCode.replace(/\\\\/g, '/')\r\n              resolve(workCode)\r\n            } else {\r\n              resolve(undefined)\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    inputChange(row) {\r\n      this.setInputMax(row)\r\n    },\r\n    setInputMax(row) {\r\n      const inputValuesKeys = Object.keys(row)\r\n        .filter(v => !v.endsWith('max') && v.startsWith(row.uuid) && v.length > row.uuid.length)\r\n      inputValuesKeys.forEach((val) => {\r\n        const curCode = val.split(SPLIT_SYMBOL)[1]\r\n        const otherTotal = inputValuesKeys.filter(x => {\r\n          const code = x.split(SPLIT_SYMBOL)[1]\r\n          return x !== val && code === curCode\r\n        }).reduce((acc, item) => {\r\n          return acc + numeral(row[item]).value()\r\n        }, 0)\r\n        row[val + SPLIT_SYMBOL + 'max'] = row.Schduled_Count - otherTotal\r\n      })\r\n    },\r\n    sendProcess({ arr, str }) {\r\n      let isSuccess = true\r\n      for (let i = 0; i < arr.length; i++) {\r\n        const item = arr[i]\r\n        if (item.originalPath && item.originalPath !== str) {\r\n          isSuccess = false\r\n          break\r\n        }\r\n        item.Technology_Path = str\r\n      }\r\n      if (!isSuccess) {\r\n        this.$message({\r\n          message: `请和该区域批次下已排产同${this.comName}保持工序一致`,\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    resetWorkTeamMax(row, str) {\r\n      if (str) {\r\n        row.Technology_Path = str\r\n      } else {\r\n        str = row.Technology_Path\r\n      }\r\n      const list = str?.split('/') || []\r\n      this.workingTeam.forEach((element, idx) => {\r\n        const cur = list.some(k => k === element.Process_Code)\r\n        const code = this.getRowUnique(row.uuid, element.Process_Code, element.Working_Team_Id)\r\n        const max = this.getRowUniqueMax(row.uuid, element.Process_Code, element.Working_Team_Id)\r\n        if (cur) {\r\n          if (!row[code]) {\r\n            this.$set(row, code, 0)\r\n            this.$set(row, max, row.Schduled_Count)\r\n          }\r\n        } else {\r\n          this.$delete(row, code)\r\n          this.$delete(row, max)\r\n        }\r\n      })\r\n    },\r\n    checkPermissionTeam(processStr, processCode) {\r\n      if (!processStr) return false\r\n      const list = processStr?.split('/') || []\r\n      return !!list.some(v => v === processCode)\r\n    },\r\n\r\n    async getTableConfig(code) {\r\n      await GetGridByCode({\r\n        code\r\n      }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n          const list = Data.ColumnList || []\r\n          this.ownerColumn = list.find(item => item.Code === 'Part_Used_Process')\r\n          this.ownerColumn2 = list.find(item => item.Code === 'Is_Main_Part')\r\n          this.columns = this.setColumnDisplay(list)\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setColumnDisplay(list) {\r\n      return list.filter(v => v.Is_Display)\r\n      // .map(item => {\r\n      //   if (FIX_COLUMN.includes(item.Code)) {\r\n      //     item.fixed = 'left'\r\n      //   }\r\n      //   return item\r\n      // })\r\n    },\r\n    activeCellMethod({ row, column, columnIndex }) {\r\n      if (this.isView) return false\r\n      const processCode = column.field?.split('$_$')[1]\r\n      return this.checkPermissionTeam(row.Technology_Path, processCode)\r\n    },\r\n    openBPADialog(type, row) {\r\n      if (this.workshopEnabled) {\r\n        if (type === 1) {\r\n          const IsUnique = this.checkIsUniqueWorkshop()\r\n          if (!IsUnique) return\r\n        }\r\n      }\r\n      this.title = type === 2 ? '工序调整' : '批量工序调整'\r\n      this.currentComponent = 'BatchProcessAdjust'\r\n      this.dWidth = this.isCom ? '60%' : '35%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].setData(type === 2 ? [row] : this.multipleSelection, type === 2 ? row.Technology_Path : '')\r\n      })\r\n    },\r\n    checkIsUniqueWorkshop() {\r\n      let isUnique = true\r\n      const firstV = this.multipleSelection[0].Workshop_Name\r\n      for (let i = 1; i < this.multipleSelection.length; i++) {\r\n        const item = this.multipleSelection[i]\r\n        if (item.Workshop_Name !== firstV) {\r\n          isUnique = false\r\n          break\r\n        }\r\n      }\r\n      if (!isUnique) {\r\n        this.$message({\r\n          message: '批量分配工序时只有相同车间下的才可一起批量分配',\r\n          type: 'warning'\r\n        })\r\n      }\r\n      return isUnique\r\n    },\r\n    checkHasWorkShop(type, arr) {\r\n      let hasWorkShop = true\r\n      for (let i = 0; i < arr.length; i++) {\r\n        const item = arr[i]\r\n        if (!item.Workshop_Name) {\r\n          hasWorkShop = false\r\n          break\r\n        }\r\n      }\r\n      if (!hasWorkShop) {\r\n        this.$message({\r\n          message: '请先选择车间后再进行工序分配',\r\n          type: 'warning'\r\n        })\r\n      }\r\n      return hasWorkShop\r\n    },\r\n    handleAddDialog(type = 'add') {\r\n      if (this.isCom) {\r\n        this.title = `${this.comName}排产`\r\n      } else {\r\n        this.title = '添加零件'\r\n      }\r\n      this.currentComponent = 'AddDraft'\r\n      this.dWidth = '80%'\r\n      this.openAddDraft = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['draft'].setPageData()\r\n      })\r\n    },\r\n    getRowUnique(uuid, processCode, workingId) {\r\n      return `${uuid}${SPLIT_SYMBOL}${processCode}${SPLIT_SYMBOL}${workingId}`\r\n    },\r\n    getRowUniqueMax(uuid, processCode, workingId) {\r\n      return this.getRowUnique(uuid, processCode, workingId) + `${SPLIT_SYMBOL}max`\r\n    },\r\n    async handleSelectMenu(v) {\r\n      if (v === 'process') {\r\n        this.openBPADialog(1)\r\n      } else if (v === 'deal') {\r\n        await this.handleAutoDeal(1)\r\n      } else if (v === 'craft') {\r\n        await this.handleSetCraftProcess()\r\n      }\r\n    },\r\n    async handleSetCraftProcess() {\r\n      const showSuccess = () => {\r\n        this.$message({\r\n          message: '已分配成功',\r\n          type: 'success'\r\n        })\r\n      }\r\n      const rowList = this.multipleSelection.map(v => v.Technology_Code).filter(v => !!v)\r\n      if (!rowList.length) {\r\n        this.$message({\r\n          message: '工艺代码不存在',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      await this.mergeCraftProcess(this.multipleSelection)\r\n      const workshopIds = Array.from(new Set(this.multipleSelection.map(v => v.Workshop_Id).filter(v => !!v)))\r\n      const w_process = []\r\n      if (workshopIds.length) {\r\n        workshopIds.forEach(workshopId => {\r\n          w_process.push(this.getProcessOption(workshopId).then(result => ({\r\n            [workshopId]: result\r\n          })))\r\n        })\r\n        const workshopPromise = Promise.all(w_process).then((values) => {\r\n          return Object.assign({}, ...values)\r\n        })\r\n        workshopPromise.then(workshop => {\r\n          let flag = true\r\n          let usedPartFlag = true\r\n          for (let i = 0; i < this.multipleSelection.length; i++) {\r\n            const curRow = this.multipleSelection[i]\r\n            console.log('curRow', JSON.parse(JSON.stringify(curRow)))\r\n            const workshopProcess = workshop[curRow.Workshop_Id]\r\n            console.log('workshopProcess', workshopProcess)\r\n            const craftArray = this.craftCodeMap[curRow.Technology_Code]\r\n            console.log('craftArray', craftArray)\r\n\r\n            if (craftArray) {\r\n              const isIncluded = craftArray.every(process => workshopProcess.includes(process))\r\n              if (!isIncluded) {\r\n                flag = false\r\n                continue\r\n              }\r\n              const hasUsedPart = this.checkHasCraftUsedPart(curRow, craftArray)\r\n              if (hasUsedPart) {\r\n                curRow.Technology_Path = craftArray.join('/')\r\n              } else {\r\n                usedPartFlag = false\r\n              }\r\n            }\r\n          }\r\n          if (!flag) {\r\n            setTimeout(() => {\r\n              this.$alert('所选车间下班组加工工序不包含工艺代码工序请手动排产', '提示', {\r\n                confirmButtonText: '确定'\r\n              })\r\n            }, 200)\r\n          }\r\n\r\n          const isSuccess = this.showCraftUsedPartResult(usedPartFlag)\r\n          flag && isSuccess && showSuccess()\r\n        })\r\n      } else {\r\n        let usedPartFlag = true\r\n        this.multipleSelection.forEach((curRow) => {\r\n          const craftArray = this.craftCodeMap[curRow.Technology_Code]\r\n          if (craftArray) {\r\n            const hasUsedPart = this.checkHasCraftUsedPart(curRow, craftArray)\r\n            if (hasUsedPart) {\r\n              curRow.Technology_Path = craftArray.join('/')\r\n            } else {\r\n              usedPartFlag = false\r\n            }\r\n          }\r\n        })\r\n        const isSuccess = this.showCraftUsedPartResult(usedPartFlag)\r\n        isSuccess && showSuccess()\r\n      }\r\n    },\r\n    checkHasCraftUsedPart(curRow, craftArray) {\r\n      if (!curRow.Part_Used_Process) return true\r\n      const partUsedProcess = curRow.Part_Used_Process.split(',')\r\n      const result = partUsedProcess.every(item => craftArray.includes(item))\r\n      return result\r\n      // return !(curRow.Part_Used_Process && !craftArray.includes(curRow.Part_Used_Process))\r\n    },\r\n    showCraftUsedPartResult(hasUsedPart) {\r\n      if (hasUsedPart) return true\r\n      setTimeout(() => {\r\n        this.$alert(`部分${this.comName}工序路径内不包含零件领用工序请手动排产`, '提示', {\r\n          confirmButtonText: '确定'\r\n        })\r\n      }, 200)\r\n      return false\r\n    },\r\n    handleBatchOwner(type, row) {\r\n      this.title = '批量分配领用工序'\r\n      this.currentComponent = 'OwnerProcess'\r\n      this.dWidth = '30%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].setOption(type === 2, type === 2 ? [row] : this.multipleSelection)\r\n      })\r\n    },\r\n    handleReverse() {\r\n      const cur = []\r\n      this.tbData.forEach((element, idx) => {\r\n        element.checked = !element.checked\r\n        if (element.checked) {\r\n          cur.push(element)\r\n        }\r\n      })\r\n      this.multipleSelection = cur\r\n      if (this.multipleSelection.length === this.tbData.length) {\r\n        this.$refs['xTable'].setAllCheckboxRow(true)\r\n      }\r\n      if (this.multipleSelection.length === 0) {\r\n        this.$refs['xTable'].setAllCheckboxRow(false)\r\n      }\r\n    },\r\n    // tbFilterChange() {\r\n    //   const xTable = this.$refs.xTable\r\n    //   const column = xTable.getColumnByField('Type_Name')\r\n    //   if (!column?.filters?.length) return\r\n    //   column.filters.forEach(d => {\r\n    //     d.checked = d.value === this.searchType\r\n    //   })\r\n    //   xTable.updateData()\r\n    // },\r\n    getType() {\r\n      const getCompTree = () => {\r\n        const fun = this.isCom ? GetCompTypeTree : GetPartTypeList\r\n        fun({}).then(res => {\r\n          if (res.IsSucceed) {\r\n            let result = res.Data\r\n            if (!this.isCom) {\r\n              result = result\r\n                .map((v, idx) => {\r\n                  return {\r\n                    Data: v.Name,\r\n                    Label: v.Name\r\n                  }\r\n                })\r\n            }\r\n            this.treeParamsComponentType.data = result\r\n            this.$nextTick((_) => {\r\n              this.$refs.treeSelectComponentType?.treeDataUpdateFun(result)\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n\r\n      getCompTree()\r\n    },\r\n    // 查看图纸\r\n    handleDwg(row) {\r\n      const obj = {}\r\n      if (this.isCom) {\r\n        obj.Comp_Id = row.Comp_Import_Detail_Id\r\n      } else {\r\n        obj.Part_Id = row.Part_Aggregate_Id\r\n      }\r\n      GetDwg(obj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const fileurl = res?.Data?.length && res.Data[0].File_Url\r\n          window.open('http://dwgv1.bimtk.com:5432/?CadUrl=' + parseOssUrl(fileurl), '_blank')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setProcessList(info) {\r\n      this.changeProcessList(info)\r\n    },\r\n    resetInnerForm() {\r\n      this.$refs['searchForm'].resetFields()\r\n      this.$refs.xTable.clearFilter()\r\n    },\r\n    innerFilter() {\r\n      this.multipleSelection = []\r\n      const arr = []\r\n      if (this.isCom) {\r\n        arr.push('Type', 'Comp_Code', 'Spec', 'Is_Component')\r\n      } else {\r\n        arr.push('Part_Code', 'Spec', 'Type_Name')\r\n      }\r\n\r\n      const xTable = this.$refs.xTable\r\n      xTable.clearCheckboxRow()\r\n      arr.forEach((element, idx) => {\r\n        const column = xTable.getColumnByField(element)\r\n        if (element === 'Is_Component') {\r\n          column.filters.forEach((option, idx) => {\r\n            option.checked = idx === (this.innerForm.searchDirect ? 0 : 1)\r\n          })\r\n        }\r\n        if (element === 'Spec') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.searchSpecSearch\r\n          option.checked = true\r\n        }\r\n        if (element === 'Type' || element === 'Type_Name') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.searchComTypeSearch\r\n          option.checked = true\r\n        }\r\n        if (element === 'Comp_Code' || element === 'Part_Code') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.searchContent\r\n          option.checked = true\r\n        }\r\n      })\r\n      xTable.updateData()\r\n    },\r\n    filterComponentMethod({ option, row }) {\r\n      if (this.innerForm.searchDirect === '') {\r\n        return true\r\n      }\r\n      return row.Is_Component === !this.innerForm.searchDirect\r\n    },\r\n    filterSpecMethod({ option, row }) {\r\n      if (this.innerForm.searchSpecSearch.trim() === '') {\r\n        return true\r\n      }\r\n      const splitAndClean = (input) => input.trim().replace(/\\s+/g, ' ').split(' ')\r\n      const specArray = splitAndClean(this.innerForm.searchSpecSearch)\r\n      return specArray.some(code => (row.Spec || '').includes(code))\r\n    },\r\n\r\n    filterTypeMethod({ option, row }) {\r\n      if (this.innerForm.searchComTypeSearch === '') {\r\n        return true\r\n      }\r\n      const cur = this.isCom ? 'Type' : 'Type_Name'\r\n      return row[cur] === this.innerForm.searchComTypeSearch\r\n    },\r\n    filterCodeMethod({ option, row }) {\r\n      if (this.innerForm.searchContent.trim() === '') {\r\n        return true\r\n      }\r\n\r\n      const splitAndClean = (input) => input.trim().replace(/\\s+/g, ' ').split(' ')\r\n\r\n      const cur = this.isCom ? 'Comp_Code' : 'Part_Code'\r\n\r\n      const arr = splitAndClean(this.innerForm.searchContent)\r\n\r\n      if (this.curSearch === 1) {\r\n        return arr.some(code => row[cur] === code)\r\n      } else {\r\n        for (let i = 0; i < arr.length; i++) {\r\n          const item = arr[i]\r\n          if (row[cur].includes(item)) {\r\n            return true\r\n          }\r\n        }\r\n        return false\r\n      }\r\n    },\r\n    componentTypeFilter(e) {\r\n      this.$refs?.treeSelectComponentType.filterFun(e)\r\n    },\r\n    getInstallUnitIdNameList(id) {\r\n      if (!this.areaId || this.isVersionFour) {\r\n        this.installUnitIdList = []\r\n        this.disabledAdd = false\r\n      } else {\r\n        this.disabledAdd = true\r\n        GetInstallUnitIdNameList({ Area_Id: this.areaId }).then(res => {\r\n          this.installUnitIdList = res.Data\r\n          if (this.installUnitIdList.length) {\r\n            this.formInline.InstallUnit_Id = this.installUnitIdList[0].Id\r\n          }\r\n          this.disabledAdd = false\r\n        })\r\n      }\r\n    },\r\n    installChange() {\r\n      if (!this.tbData.length) {\r\n        this.$refs['searchForm'].resetFields()\r\n        this.$refs.xTable.clearFilter()\r\n        return\r\n      }\r\n      this.$confirm('切换区域右侧数据清空, 是否确认?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbData = []\r\n        this.resetInnerForm()\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    showPartUsedProcess(row) {\r\n      if (this.isNest) {\r\n        return !!row.Comp_Import_Detail_Id\r\n      } else {\r\n        return !this.isView && row.Type !== 'Direct'\r\n      }\r\n    },\r\n    handleExport() {\r\n      if (!this.tbData.length) {\r\n        this.$message({\r\n          message: '暂无数据',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      console.log(7, this.$refs.xTable)\r\n      const item = this.tbData[0]\r\n      this.$refs.xTable.exportData({\r\n        filename: `${this.comName}排产-${item.Project_Name}-${item.Area_Name}-${this.formInline.Schduling_Code}(${this.comName})`,\r\n        type: 'xlsx',\r\n        data: this.tbData\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.flex-row {\r\n  display: flex;\r\n\r\n  .cs-left {\r\n    background-color: #ffffff;\r\n    margin-right: 20px;\r\n    border-radius: 4px;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n\r\n    .cs-tree-wrapper {\r\n      height: 100%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      overflow: hidden;\r\n      padding: 16px;\r\n\r\n      .tree-search {\r\n        display: flex;\r\n\r\n        .search-select {\r\n          margin-right: 8px;\r\n        }\r\n      }\r\n\r\n      .el-tree {\r\n        flex: 1;\r\n        overflow: auto;\r\n      }\r\n    }\r\n  }\r\n\r\n  .cs-right {\r\n    flex: 1;\r\n    overflow: hidden;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  padding: 0;\r\n  text-align: right;\r\n}\r\n\r\n::v-deep .el-card__body {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.tb-x {\r\n  flex: 1;\r\n  height: 0;\r\n  margin-bottom: 10px;\r\n  overflow: auto;\r\n}\r\n\r\n.topTitle {\r\n  font-size: 14px;\r\n  margin: 0 0 16px;\r\n\r\n  span {\r\n    display: inline-block;\r\n    width: 2px;\r\n    height: 14px;\r\n    background: #009dff;\r\n    vertical-align: middle;\r\n    margin-right: 6px;\r\n  }\r\n}\r\n\r\n::v-deep .elDivder {\r\n  margin: 10px;\r\n}\r\n\r\n.btn-x {\r\n  //margin-bottom: 10px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.el-icon-edit {\r\n  cursor: pointer;\r\n}\r\n\r\nfooter {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.cs-bottom {\r\n  position: relative;\r\n  height: 40px;\r\n  line-height: 40px;\r\n\r\n  .data-info {\r\n    position: absolute;\r\n    bottom: 0;\r\n\r\n    .info-x {\r\n      margin-right: 20px;\r\n    }\r\n  }\r\n}\r\n\r\n.fourGreen {\r\n  color: #00C361;\r\n  font-style: normal;\r\n}\r\n\r\n.fourOrange {\r\n  color: #FF9400;\r\n  font-style: normal;\r\n}\r\n\r\n.fourRed {\r\n  color: #FF0000;\r\n  font-style: normal;\r\n}\r\n\r\n.cs-blue {\r\n  color: #5AC8FA;\r\n}\r\n.cs-column-row{\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .cs-ell{\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAskBA,SAAAA,YAAA,EAAAC,QAAA;AACA,OAAAC,kBAAA;AACA,SACAC,uBAAA,EACAC,0BAAA,EACAC,MAAA,EACAC,0BAAA,EACAC,wBAAA,EACAC,+BAAA,EACAC,sBAAA,EACAC,yBAAA,EACAC,6BAAA,EACAC,qBAAA,QACA;AACA,SAAAC,WAAA;AACA,OAAAC,QAAA;AACA,OAAAC,YAAA;AACA,OAAAC,QAAA;AACA,SAAAC,aAAA;AACA,SAAAC,UAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AACA,OAAAC,OAAA;AACA,SAAAC,cAAA,EAAAC,gCAAA,EAAAC,kBAAA;AACA,SAAAC,aAAA;AACA,SAAAC,UAAA,EAAAC,UAAA;AACA,SAAAC,eAAA;AACA,OAAAC,MAAA;AACA,OAAAC,iBAAA;AACA,OAAAC,UAAA;AACA,SAAAC,wBAAA,EAAAC,sBAAA;AAEA,SAAAC,eAAA;AACA,SAAAC,WAAA;AACA,OAAAC,kBAAA;AACA,SAAAC,UAAA;AAEA,IAAAC,YAAA;AACA;EACAC,UAAA;IAAAH,kBAAA,EAAAA,kBAAA;IAAAL,UAAA,EAAAA,UAAA;IAAAD,iBAAA,EAAAA,iBAAA;IAAA5B,kBAAA,EAAAA,kBAAA;IAAAY,QAAA,EAAAA,QAAA;IAAAE,QAAA,EAAAA,QAAA;IAAAD,YAAA,EAAAA;EAAA;EACAyB,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,OAAA;MACAC,SAAA;MACAC,kBAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,WAAA;QAAAP,IAAA;MAAA;MACAQ,gBAAA;QAAAR,IAAA;MAAA;MACAS,gBAAA;QAAAT,IAAA;MAAA;MACAU,aAAA;QACAC,YAAA,WAAAA,aAAAC,IAAA,GACA;MACA;MACAC,SAAA;QACAC,aAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,YAAA;MACA;MACAC,SAAA;MACAC,UAAA;MACAC,UAAA;QACAC,cAAA;QACAC,eAAA;QACAC,WAAA;QACAC,cAAA;QACAC,MAAA;MACA;MACAC,KAAA;MACAC,UAAA;MACAC,QAAA;MACAC,OAAA;MACAC,MAAA;MACAC,QAAA;MACAC,UAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,SAAA;MACAC,aAAA;MACAC,cAAA;MACAC,WAAA;MACAC,aAAA;MACAC,YAAA;MACAC,WAAA;MACAC,SAAA;MACAC,UAAA;MACAC,gBAAA;MACAC,MAAA;MACAC,KAAA;MACAC,KAAA;MACAC,MAAA,WAAAA,OAAA;QAAA;MAAA;MACAC,QAAA,EAAAC,SAAA;MACAC,QAAA;MACAC,gBAAA;MACAC,UAAA;MACAC,WAAA;MACAC,UAAA,EAAAL,SAAA;MACAM,UAAA;MACAC,kBAAA;MAEAC,iBAAA;MACAC,SAAA;MACAC,MAAA;MACAC,WAAA;MACAC,UAAA;MACAC,WAAA;MACAC,WAAA;MACAC,QAAA;MACAC,uBAAA;QACA;QACA;QACAC,UAAA;QACAC,WAAA;QACApE,IAAA;QACAqE,KAAA;UACAC,QAAA;UACAjE,KAAA;UACAC,KAAA;QACA;MACA;MACAiE,gBAAA;QACAC,WAAA;QACAC,YAAA;QACAC,SAAA;MACA;MACAC,WAAA;IACA;EACA;EACAC,KAAA;IACA;MACAC,OAAA,WAAAA,QAAAC,CAAA,EAAAC,CAAA;QACA,KAAAC,UAAA;MACA;MACAC,SAAA;IACA;EAEA;EAEAC,QAAA,EAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA;IACAC,KAAA,WAAAA,MAAA;MACA,YAAAnC,QAAA;IACA;IACAoC,MAAA,WAAAA,OAAA;MACA,YAAA9B,UAAA;IACA;IACA+B,MAAA,WAAAA,OAAA;MACA,YAAA/B,UAAA;IACA;IACAgC,KAAA,WAAAA,MAAA;MACA,YAAAhC,UAAA;IACA;IACAiC,WAAA,WAAAA,YAAA;MACA,YAAAzB,WAAA,QAAA3C,UAAA,CAAAI,cAAA;IACA;IACAiE,UAAA,WAAAA,WAAA;MACA,YAAA5B,WAAA,GAAA/D,YAAA,QAAAgE,UAAA;IACA;IACA4B,UAAA,WAAAA,WAAA;MACA,YAAAN,KAAA;IACA;IACAO,WAAA,WAAAA,YAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,IAAA,QAAAnC,iBAAA,CAAAoC,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,EAAA,KAAAJ,KAAA,CAAAxE,UAAA,CAAAI,cAAA;MAAA;MACA,IAAAqE,IAAA;QACA,OAAAA,IAAA,CAAAI,IAAA;MACA;QACA;MACA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA,YAAAC,gBAAA,UAAAf,KAAA;IACA;IACAgB,MAAA,WAAAA,OAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA;IACA;EAAA,GACApH,UAAA,2DACAA,UAAA,2CACAA,UAAA,8BACA;EACAqH,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAAC,iBAAA,EAAAC,IAAA,EAAA7G,OAAA,EAAA8G,cAAA,EAAAC,mBAAA,EAAArD,MAAA,EAAAsD,OAAA;MAAA,OAAAP,mBAAA,GAAAQ,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACA1H,UAAA;UAAA;YAAAiH,iBAAA,GAAAO,QAAA,CAAAG,IAAA;YAAAT,IAAA,GAAAD,iBAAA,CAAAC,IAAA;YAAA7G,OAAA,GAAA4G,iBAAA,CAAA5G,OAAA;YAAA8G,cAAA,GAAAF,iBAAA,CAAAE,cAAA;YACAP,MAAA,CAAAxG,OAAA,GAAA8G,IAAA;YACAN,MAAA,CAAAvG,OAAA,GAAAA,OAAA;YACAuG,MAAA,CAAAtG,SAAA,GAAA6G,cAAA,aAAAA,cAAA,uBAAAA,cAAA,CAAAS,IAAA;YACAC,OAAA,CAAAC,GAAA,mBAAAX,cAAA;YACAU,OAAA,CAAAC,GAAA,cAAAlB,MAAA,CAAAtG,SAAA;YACAsG,MAAA,CAAAmB,eAAA;YACAnB,MAAA,CAAAoB,SAAA;YACApB,MAAA,CAAAqB,YAAA;YACArB,MAAA,CAAAxD,QAAA,GAAAwD,MAAA,CAAAJ,MAAA,CAAAC,KAAA,CAAAyB,OAAA;YACAtB,MAAA,CAAAlD,UAAA,GAAAkD,MAAA,CAAAJ,MAAA,CAAAC,KAAA,CAAA0B,MAAA;YACAvB,MAAA,CAAAwB,KAAA,GAAAxB,MAAA,CAAAJ,MAAA,CAAAC,KAAA,CAAA2B,KAAA;YACAxB,MAAA,CAAAjD,UAAA,GAAAiD,MAAA,CAAAJ,MAAA,CAAAC,KAAA,CAAA4B,GAAA;YACA;YACA;YACAzB,MAAA,CAAArF,UAAA,CAAAE,eAAA,GAAA6G,YAAA,CAAAC,OAAA;YACA;YACA;YACA;YACA;;YAEA3B,MAAA,CAAA4B,MAAA,GAAA3J,UAAA;YACA+H,MAAA,CAAA6B,mBAAA;YAEA7B,MAAA,CAAAzD,MAAA,GAAAvF,QAAA,CAAAgJ,MAAA,CAAA8B,SAAA;YAAAlB,QAAA,CAAAE,IAAA;YAAA,OACAd,MAAA,CAAA+B,WAAA;UAAA;YACA,IAAA/B,MAAA,CAAApB,MAAA,IAAAoB,MAAA,CAAAnB,MAAA;cAAA2B,mBAAA,GACAR,MAAA,CAAAJ,MAAA,CAAAC,KAAA,EAAA1C,MAAA,GAAAqD,mBAAA,CAAArD,MAAA,EAAAsD,OAAA,GAAAD,mBAAA,CAAAC,OAAA;cACAT,MAAA,CAAA7C,MAAA,GAAAA,MAAA;cACA6C,MAAA,CAAArF,UAAA,CAAAI,cAAA,GAAA0F,OAAA;cACAT,MAAA,CAAAgC,wBAAA;cACAhC,MAAA,CAAA8B,SAAA;YACA;YAEA,IAAA9B,MAAA,CAAAlB,KAAA;cACAkB,MAAA,CAAAiC,aAAA;cACAjC,MAAA,CAAAkC,OAAA;YACA;YACA,IAAAlC,MAAA,CAAAnB,MAAA;cACAmB,MAAA,CAAAkC,OAAA;YACA;UAAA;UAAA;YAAA,OAAAtB,QAAA,CAAAuB,IAAA;QAAA;MAAA,GAAA/B,OAAA;IAAA;EACA;EACAgC,OAAA,EAAA1D,aAAA,CAAAA,aAAA,KACAjG,UAAA;IACA8F,UAAA,WAAAA,WAAA;MACA,SAAAI,KAAA;MACA,KAAA9C,WAAA,QAAAR,MAAA,CAAAgH,KAAA,WAAA/C,CAAA;QAAA,QAAAA,CAAA,CAAAgD,qBAAA;MAAA,YAAA3C,MAAA;MACA,IAAA4C,GAAA,QAAAnH,OAAA,CAAAoH,SAAA,WAAAlD,CAAA;QAAA,OAAAA,CAAA,CAAA0B,IAAA;MAAA;MACA,SAAAnF,WAAA;QACA0G,GAAA,gBAAAnH,OAAA,CAAAqH,MAAA,CAAAF,GAAA;MACA;QACA,IAAAA,GAAA;UACA,UAAAG,WAAA;YACA,KAAAC,QAAA;cACAC,OAAA;cACA9C,IAAA;YACA;YACA;UACA;UACA,KAAA1E,OAAA,CAAAyH,IAAA,MAAAH,WAAA;QACA;QACA,KAAAI,OAAA;MACA;IACA;IACAf,WAAA,WAAAA,YAAA;MAAA,IAAAgB,MAAA;MAAA,OAAA9C,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6C,SAAA;QAAA,OAAA9C,mBAAA,GAAAQ,IAAA,UAAAuC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArC,IAAA,GAAAqC,SAAA,CAAApC,IAAA;YAAA;cAAAoC,SAAA,CAAApC,IAAA;cAAA,OACAiC,MAAA,CAAAI,SAAA;YAAA;cAAAD,SAAA,CAAApC,IAAA;cAAA,OACAiC,MAAA,CAAAK,WAAA;YAAA;YAAA;cAAA,OAAAF,SAAA,CAAAf,IAAA;UAAA;QAAA,GAAAa,QAAA;MAAA;IACA;IACAG,SAAA,WAAAA,UAAA;MAAA,IAAAE,MAAA;MAAA,OAAApD,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAmD,SAAA;QAAA,IAAAC,UAAA;QAAA,OAAArD,mBAAA,GAAAQ,IAAA,UAAA8C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5C,IAAA,GAAA4C,SAAA,CAAA3C,IAAA;YAAA;cACAyC,UAAA;cACA,IAAAF,MAAA,CAAA1D,MAAA;gBACA,IAAA0D,MAAA,CAAAzE,MAAA;kBACA2E,UAAA;gBACA;kBACAA,UAAA;gBACA;cACA;gBACAA,UAAA,GAAAF,MAAA,CAAAzE,MAAA;cACA;cACAyE,MAAA,CAAAlI,QAAA,GAAAoI,UAAA;cAAAE,SAAA,CAAA3C,IAAA;cAAA,OACAuC,MAAA,CAAAK,cAAA,CAAAH,UAAA;YAAA;cACA,KAAAF,MAAA,CAAAM,eAAA;gBACAN,MAAA,CAAAjI,OAAA,GAAAiI,MAAA,CAAAjI,OAAA,CAAAwI,MAAA,WAAAtE,CAAA;kBAAA,OAAAA,CAAA,CAAA0B,IAAA;gBAAA;cACA;cACA,KAAAqC,MAAA,CAAAQ,aAAA;gBACAR,MAAA,CAAAjI,OAAA,GAAAiI,MAAA,CAAAjI,OAAA,CAAAwI,MAAA,WAAAtE,CAAA;kBAAA,OAAAA,CAAA,CAAA0B,IAAA;gBAAA;cACA;cACAqC,MAAA,CAAA9E,UAAA;YAAA;YAAA;cAAA,OAAAkF,SAAA,CAAAtB,IAAA;UAAA;QAAA,GAAAmB,QAAA;MAAA;IACA;IACAQ,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MAAA,OAAA9D,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6D,SAAA;QAAA,OAAA9D,mBAAA,GAAAQ,IAAA,UAAAuD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArD,IAAA,GAAAqD,SAAA,CAAApD,IAAA;YAAA;cAAAoD,SAAA,CAAApD,IAAA;cAAA,OACAiD,MAAA,CAAAL,cAAA,CAAAK,MAAA,CAAA5I,QAAA;YAAA;cACA4I,MAAA,CAAAzH,KAAA;YAAA;YAAA;cAAA,OAAA4H,SAAA,CAAA/B,IAAA;UAAA;QAAA,GAAA6B,QAAA;MAAA;IACA;IACAG,eAAA,WAAAA,gBAAA5K,IAAA;MAAA,IAAA6K,cAAA;QAAAC,MAAA;MACA,KAAA/G,WAAA,GAAA/D,IAAA,CAAAgG,EAAA;MACA,SAAApC,MAAA,KAAA5D,IAAA,CAAAgG,EAAA;QACA,KAAArB,WAAA;QACA;MACA;MACA,KAAAA,WAAA;MACA,KAAA3E,IAAA,CAAA+K,WAAA,MAAAF,cAAA,GAAA7K,IAAA,CAAAgL,QAAA,cAAAH,cAAA,uBAAAA,cAAA,CAAAI,MAAA;QACA;MACA;MACA,KAAAjL,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAAkL,IAAA,MAAAxF,UAAA;QACA,KAAA0D,QAAA;UACAC,OAAA;UACA9C,IAAA;QACA;QAEA;MACA;MAEA,IAAA4E,QAAA,YAAAA,SAAAC,IAAA;QAAA,IAAAF,IAAA,GAAAE,IAAA,CAAAF,IAAA;QACAJ,MAAA,CAAAlH,MAAA,GAAAsH,IAAA,CAAAlF,EAAA;QACA8E,MAAA,CAAAnH,SAAA,GAAAuH,IAAA,CAAAG,UAAA;QACAP,MAAA,CAAA/G,WAAA,GAAA+G,MAAA,CAAAlH,MAAA;QACAkH,MAAA,CAAA1J,UAAA,CAAAG,WAAA;QACAuJ,MAAA,CAAA1J,UAAA,CAAAI,cAAA;QACAsJ,MAAA,CAAA1J,UAAA,CAAAK,MAAA;QACAqJ,MAAA,CAAAhJ,MAAA;QACAgJ,MAAA,CAAAQ,WAAA;QACAR,MAAA,CAAArC,wBAAA;MACA;MAEA,SAAA3G,MAAA,CAAAmJ,MAAA;QACA,KAAAM,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAlF,IAAA;QACA,GAAAmF,IAAA;UACAP,QAAA,CAAAnL,IAAA;UACA8K,MAAA,CAAAnG,WAAA;UACAmG,MAAA,CAAAjD,SAAA;QACA,GAAA8D,KAAA;UACAb,MAAA,CAAA1B,QAAA;YACA7C,IAAA;YACA8C,OAAA;UACA;QACA;MACA;QACA,KAAA1E,WAAA;QACAwG,QAAA,CAAAnL,IAAA;MACA;IACA;IAEA4L,eAAA,WAAAA,gBAAAtL,KAAA,EAAAN,IAAA,EAAA6L,IAAA;MACA;;MAEA,IAAAC,GAAA,GAAAxL,KAAA,CAAAyL,KAAA,CAAAjM,YAAA;MACA,IAAAkM,QAAA,GAAAF,GAAA;MACA,IAAAG,SAAA,GAAAH,GAAA;MACA,KAAAxL,KAAA;MACA,IAAA4L,UAAA,GAAAL,IAAA,CAAAM,MAAA;MACA,IAAAC,MAAA,IAAAP,IAAA,CAAAxL,KAAA;MACA,IAAA2H,MAAA,IAAAhI,IAAA,CAAAkL,IAAA,MAAAxF,UAAA;MACA,IAAA2G,KAAA;MACA,OAAAA,KAAA,GAAAR,IAAA,CAAAQ,KAAA;QACAD,MAAA,MAAAE,MAAA,CAAAC,kBAAA,CAAAH,MAAA,IAAAF,UAAA,CAAA7L,KAAA;QACA2H,MAAA,MAAAsE,MAAA,CAAAC,kBAAA,CAAAvE,MAAA,IAAAhI,IAAA,CAAAkL,IAAA,MAAAxF,UAAA;QACAwG,UAAA,GAAAA,UAAA,CAAAC,MAAA;QACAE,KAAA;MACA;MACAD,MAAA,GAAAA,MAAA,CAAA/B,MAAA,WAAAtE,CAAA;QAAA,SAAAA,CAAA;MAAA;MACAiC,MAAA,GAAAA,MAAA,CAAAqC,MAAA,WAAAtE,CAAA;QAAA,SAAAA,CAAA;MAAA;MACA,IAAAyG,WAAA;MACA,IAAAC,YAAA;MACA,SAAA3I,UAAA;QACA2I,YAAA,GAAAzE,MAAA,CAAA0E,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,OAAA,CAAAX,SAAA;QAAA;MACA;MACA,SAAApI,WAAA;QACA2I,WAAA,GAAAJ,MAAA,CAAAM,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,OAAA,CAAAZ,QAAA;QAAA;MACA;MACA,OAAAQ,WAAA,IAAAC,YAAA;IACA;IACAlE,SAAA,WAAAA,UAAA;MAAA,IAAAsE,MAAA;MAAA,OAAAnG,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkG,SAAA;QAAA,IAAAC,OAAA;QAAA,OAAApG,mBAAA,GAAAQ,IAAA,UAAA6F,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3F,IAAA,GAAA2F,SAAA,CAAA1F,IAAA;YAAA;cACAsF,MAAA,CAAAnK,SAAA;cACAqK,OAAA;cAAA,KACAF,MAAA,CAAAzG,MAAA;gBAAA6G,SAAA,CAAA1F,IAAA;gBAAA;cAAA;cAAA,KACAsF,MAAA,CAAAxH,MAAA;gBAAA4H,SAAA,CAAA1F,IAAA;gBAAA;cAAA;cAAA0F,SAAA,CAAA1F,IAAA;cAAA,OACAsF,MAAA,CAAAK,eAAA;YAAA;cAAAH,OAAA,GAAAE,SAAA,CAAAzF,IAAA;cAAAyF,SAAA,CAAA1F,IAAA;cAAA;YAAA;cAAA0F,SAAA,CAAA1F,IAAA;cAAA,OAEAsF,MAAA,CAAAM,eAAA;YAAA;cAAAJ,OAAA,GAAAE,SAAA,CAAAzF,IAAA;YAAA;cAAAyF,SAAA,CAAA1F,IAAA;cAAA;YAAA;cAAA0F,SAAA,CAAA1F,IAAA;cAAA,OAGAsF,MAAA,CAAAO,cAAA;YAAA;cAAAL,OAAA,GAAAE,SAAA,CAAAzF,IAAA;cACAE,OAAA,CAAAC,GAAA,YAAAoF,OAAA;YAAA;cAGAF,MAAA,CAAAQ,UAAA,CAAAN,OAAA;cACAF,MAAA,CAAAnK,SAAA;YAAA;YAAA;cAAA,OAAAuK,SAAA,CAAArE,IAAA;UAAA;QAAA,GAAAkE,QAAA;MAAA;IACA;IACAQ,kBAAA,WAAAA,mBAAA;MACA;IAAA,CACA;IACAC,eAAA,WAAAA,gBAAA;MACA;IAAA,CACA;IACA7E,aAAA,WAAAA,cAAA;MAAA,IAAA8E,MAAA;MACA,KAAAxJ,WAAA;MACA0D,OAAA,CAAAC,GAAA,6BAAAtB,MAAA,OAAAoH,OAAA;MACAhO,sBAAA;QAAAiO,MAAA,OAAArH,MAAA,CAAAsH,IAAA,CAAA3H,EAAA;QAAAnC,WAAA,OAAAA,WAAA;QAAA+J,IAAA,OAAAxI,KAAA;MAAA,GAAAsG,IAAA,WAAAmC,GAAA;QACA,KAAAA,GAAA,CAAAC,SAAA;UACAN,MAAA,CAAApE,QAAA;YACAC,OAAA,EAAAwE,GAAA,CAAAE,OAAA;YACAxH,IAAA;UACA;UACAiH,MAAA,CAAAvJ,QAAA;UACAuJ,MAAA,CAAAxJ,WAAA;UACA;QACA;QACA,IAAA6J,GAAA,CAAA3C,IAAA,CAAAD,MAAA;UACAuC,MAAA,CAAAxJ,WAAA;UACAwJ,MAAA,CAAAvJ,QAAA;UACA;QACA;QACA,IAAA8I,OAAA,GAAAc,GAAA,CAAA3C,IAAA,CAAA8C,GAAA,WAAAnI,IAAA;UACAA,IAAA,CAAAoI,YAAA;UACA,OAAApI,IAAA;QACA;QACA2H,MAAA,CAAAvJ,QAAA,GAAA8I,OAAA;QACAS,MAAA,CAAAU,SAAA,WAAAC,CAAA;UACAX,MAAA,CAAAY,KAAA,CAAAC,IAAA,CAAAC,SAAA,CAAAd,MAAA,CAAA/H,UAAA;UACA,IAAA8I,MAAA,GAAAf,MAAA,CAAAgB,MAAA;UACA,KAAAD,MAAA;YACAf,MAAA,CAAArL,SAAA;UACA;QACA;QACAqL,MAAA,CAAAxJ,WAAA;MACA,GAAA2H,KAAA,WAAA8C,CAAA;QACA/G,OAAA,CAAAC,GAAA,WAAA8G,CAAA;QACAjB,MAAA,CAAAxJ,WAAA;QACAwJ,MAAA,CAAAvJ,QAAA;MACA;IACA;IACAuK,MAAA,WAAAA,OAAA;MAAA,IAAAE,MAAA;MACA,IAAAC,WAAA,YAAAA,WAAAN,IAAA;QACA,SAAAO,CAAA,MAAAA,CAAA,GAAAP,IAAA,CAAApD,MAAA,EAAA2D,CAAA;UACA,IAAA/I,IAAA,GAAAwI,IAAA,CAAAO,CAAA;UACA,IAAA1D,IAAA,GAAArF,IAAA,CAAAqF,IAAA;YAAAF,QAAA,GAAAnF,IAAA,CAAAmF,QAAA;UACA,IAAAa,IAAA,GAAAgD,OAAA,CAAA3D,IAAA,CAAAlF,EAAA;UACA,IAAAkF,IAAA,CAAA4D,QAAA,MAAA9D,QAAA,aAAAA,QAAA,eAAAA,QAAA,CAAAC,MAAA,KAAAY,IAAA,CAAAkD,OAAA;YACAL,MAAA,CAAA9D,eAAA,CAAA/E,IAAA;YACA;UACA;YACA,IAAAmF,QAAA,aAAAA,QAAA,eAAAA,QAAA,CAAAC,MAAA;cACA,IAAA+D,UAAA,GAAAL,WAAA,CAAA3D,QAAA;cACA,IAAAgE,UAAA;YACA;UACA;QACA;QACA;MACA;MACA,IAAAH,OAAA,YAAAA,QAAAI,GAAA;QACA,OAAAP,MAAA,CAAAN,KAAA,SAAAc,YAAA,CAAAD,GAAA;MACA;MACA,OAAAN,WAAA,MAAA1K,QAAA;IACA;IACAkL,SAAA,WAAAA,UAAA;MACA3R,YAAA,MAAA4R,MAAA,OAAA/I,MAAA;IACA;IACAiC,mBAAA,WAAAA,oBAAA;MACA,KAAAjG,cAAA;IACA;IACAgN,cAAA,WAAAA,eAAAC,KAAA;MACA,KAAArN,iBAAA,GAAAqN,KAAA,CAAAC,OAAA;IACA;IACAjE,WAAA,WAAAA,YAAA;MAAA,IAAAkE,MAAA;MACA,KAAApO,UAAA,CAAAG,WAAA;MACAtC,aAAA;QACAwQ,EAAA,OAAA7L;MACA,GAAA8H,IAAA,WAAAmC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UAAA,IAAA4B,SAAA,EAAAC,UAAA;UACA,KAAA9B,GAAA,CAAA3C,IAAA;YACA;UACA;UAEA,IAAA0E,KAAA,GAAAvQ,MAAA,EAAAqQ,SAAA,GAAA7B,GAAA,CAAA3C,IAAA,cAAAwE,SAAA,uBAAAA,SAAA,CAAAG,iBAAA;UACA,IAAAC,GAAA,GAAAzQ,MAAA,EAAAsQ,UAAA,GAAA9B,GAAA,CAAA3C,IAAA,cAAAyE,UAAA,uBAAAA,UAAA,CAAAI,eAAA;UACAP,MAAA,CAAA9O,aAAA,CAAAC,YAAA,aAAAC,IAAA;YACA,OAAAA,IAAA,CAAAoP,OAAA,KAAAJ,KAAA,IAAAhP,IAAA,CAAAoP,OAAA,KAAAF,GAAA;UACA;QACA;UACAN,MAAA,CAAApG,QAAA;YACAC,OAAA,EAAAwE,GAAA,CAAAE,OAAA;YACAxH,IAAA;UACA;QACA;MACA;IACA;IACA0J,WAAA,WAAAA,YAAA;MACA,KAAA1N,aAAA;MACA,KAAAC,YAAA;IACA;IACA2K,eAAA,WAAAA,gBAAA;MAAA,IAAA+C,MAAA;MACA,WAAAC,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACA1S,uBAAA;UACA2S,GAAA,EAAAJ,MAAA,CAAAK;QACA,GAAA7E,IAAA,WAAAmC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACA,IAAA0C,KAAA,IAAA3C,GAAA,aAAAA,GAAA,uBAAAA,GAAA,CAAA3C,IAAA;YACA,IAAAnE,IAAA,GAAAyJ,KAAA,CAAAxC,GAAA,WAAAjI,CAAA;cACA,IAAAA,CAAA,CAAA0K,sBAAA;gBACA;gBACA1K,CAAA,CAAA2K,iBAAA,GAAA3K,CAAA,CAAA0K,sBAAA;cACA;cACA;cACA1K,CAAA,CAAA4K,WAAA,GAAA5K,CAAA,CAAA6K,qBAAA;cACA7K,CAAA,CAAA8K,aAAA,GAAA9K,CAAA,CAAA+K,uBAAA;cACA/K,CAAA,CAAAgL,eAAA,GAAAhL,CAAA,CAAAiL,yBAAA,IAAAjL,CAAA,CAAAgL,eAAA;cACAhL,CAAA,CAAAkL,WAAA,GAAAlL,CAAA,CAAAmL,mBAAA;cACA,OAAAnL,CAAA;YACA;YAEAqK,OAAA,CAAArJ,IAAA;UACA;YACAmJ,MAAA,CAAA9G,QAAA;cACAC,OAAA,EAAAwE,GAAA,CAAAE,OAAA;cACAxH,IAAA;YACA;YACA8J,MAAA;UACA;QACA;MACA;IACA;IACAjD,cAAA,WAAAA,eAAA;MAAA,IAAA+D,OAAA;MAAA,OAAAzK,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAwK,SAAA;QAAA,IAAAlJ,GAAA,EAAAqG,MAAA;QAAA,OAAA5H,mBAAA,GAAAQ,IAAA,UAAAkK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhK,IAAA,GAAAgK,SAAA,CAAA/J,IAAA;YAAA;cAEAW,GAAA,GACAiJ,OAAA,CAAA9K,MAAA,CAAAC,KAAA,CADA4B,GAAA;cAAAoJ,SAAA,CAAA/J,IAAA;cAAA,OAEA3J,0BAAA;gBACA2T,iBAAA,EAAArJ;cACA,GAAAwD,IAAA,WAAAmC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACA,IAAA0D,UAAA,GAAA3D,GAAA,CAAA3C,IAAA;oBAAAuG,cAAA,GAAAD,UAAA,CAAAC,cAAA;oBAAAC,eAAA,GAAAF,UAAA,CAAAE,eAAA;oBAAAC,YAAA,GAAAH,UAAA,CAAAG,YAAA;kBACAR,OAAA,CAAA/P,UAAA,GAAAwQ,MAAA,CAAAC,MAAA,CAAAV,OAAA,CAAA/P,UAAA,EAAAqQ,cAAA;kBACAE,YAAA,CAAAG,OAAA,WAAAjM,IAAA;oBACA,IAAAkM,KAAA;sBACA9C,GAAA,EAAApJ,IAAA,CAAAmM,YAAA;sBACA1R,KAAA,EAAAuF;oBACA;oBACAsL,OAAA,CAAAc,iBAAA,CAAAF,KAAA;kBACA;kBACA,IAAAhL,IAAA,GAAA2K,eAAA,CAAA1D,GAAA,WAAAjI,CAAA;oBACAA,CAAA,CAAAkL,WAAA,GAAAlL,CAAA,CAAAmL,mBAAA;oBACA,OAAAnL,CAAA;kBACA;kBACAoL,OAAA,CAAAe,WAAA,CAAAnL,IAAA;kBACA,OAAAA,IAAA;gBACA;kBACAoK,OAAA,CAAA/H,QAAA;oBACAC,OAAA,EAAAwE,GAAA,CAAAE,OAAA;oBACAxH,IAAA;kBACA;gBACA;cACA;YAAA;cAzBAgI,MAAA,GAAA+C,SAAA,CAAA9J,IAAA;cAAA,OAAA8J,SAAA,CAAAa,MAAA,WA0BA5D,MAAA;YAAA;YAAA;cAAA,OAAA+C,SAAA,CAAA1I,IAAA;UAAA;QAAA,GAAAwI,QAAA;MAAA;IACA;IACAc,WAAA,WAAAA,YAAAnL,IAAA;MAAA,IAAAqL,OAAA;MAAA,OAAA1L,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAyL,SAAA;QAAA,IAAAC,SAAA;QAAA,OAAA3L,mBAAA,GAAAQ,IAAA,UAAAoL,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlL,IAAA,GAAAkL,SAAA,CAAAjL,IAAA;YAAA;cACAG,OAAA,CAAAC,GAAA,gBAAAZ,IAAA;cACAuL,SAAA,GAAAvL,IAAA,CAAAiH,GAAA,WAAAnI,IAAA;gBACA;kBACAG,EAAA,EAAAH,IAAA,CAAAkD,qBAAA;kBACA6E,IAAA;gBACA;cACA;cAAA4E,SAAA,CAAAjL,IAAA;cAAA,OACAlJ,WAAA,CAAAiU,SAAA,EAAA5G,IAAA,WAAAmC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACA,IAAA2E,OAAA;kBACA5E,GAAA,CAAA3C,IAAA,CAAA4G,OAAA,WAAAjM,IAAA;oBACA4M,OAAA,CAAA5M,IAAA,CAAAG,EAAA,MAAAH,IAAA,CAAA6M,OAAA;kBACA;kBACA3L,IAAA,CAAA+K,OAAA,WAAAa,GAAA;oBACA,IAAAF,OAAA,CAAAE,GAAA,CAAA5J,qBAAA;sBACAqJ,OAAA,CAAAQ,IAAA,CAAAD,GAAA,cAAAF,OAAA,CAAAE,GAAA,CAAA5J,qBAAA;oBACA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAyJ,SAAA,CAAA5J,IAAA;UAAA;QAAA,GAAAyJ,QAAA;MAAA;IACA;IACAnF,eAAA,WAAAA,gBAAA;MAAA,IAAA2F,OAAA;MACA,WAAA1C,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACA,IACAnI,GAAA,GACA2K,OAAA,CAAAxM,MAAA,CAAAC,KAAA,CADA4B,GAAA;QAEApK,0BAAA;UACAyT,iBAAA,EAAArJ;QACA,GAAAwD,IAAA,WAAAmC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YAAA,IAAAgF,UAAA,EAAAC,UAAA;YACA,IAAAC,cAAA,IAAAF,UAAA,GAAAjF,GAAA,CAAA3C,IAAA,cAAA4H,UAAA,uBAAAA,UAAA,CAAAE,cAAA,CAAAhF,GAAA,WAAAjI,CAAA;cACA,IAAAA,CAAA,CAAA0K,sBAAA;gBACA;gBACA1K,CAAA,CAAA2K,iBAAA,GAAA3K,CAAA,CAAA0K,sBAAA;cACA;cACA1K,CAAA,CAAAkL,WAAA,GAAAlL,CAAA,CAAAmL,mBAAA;cACA,OAAAnL,CAAA;YACA;YACA8M,OAAA,CAAAzR,UAAA,GAAAwQ,MAAA,CAAAC,MAAA,CAAAgB,OAAA,CAAAzR,UAAA,GAAA2R,UAAA,GAAAlF,GAAA,CAAA3C,IAAA,cAAA6H,UAAA,uBAAAA,UAAA,CAAAtB,cAAA;YACArB,OAAA,CAAA4C,cAAA;UACA;YACAH,OAAA,CAAAzJ,QAAA;cACAC,OAAA,EAAAwE,GAAA,CAAAE,OAAA;cACAxH,IAAA;YACA;YACA8J,MAAA;UACA;QACA;MACA;IACA;IACAhD,UAAA,WAAAA,WAAAtG,IAAA;MAAA,IAAAkM,OAAA;MAAA,IAAAC,OAAA,GAAAC,SAAA,CAAAlI,MAAA,QAAAkI,SAAA,QAAAjQ,SAAA,GAAAiQ,SAAA;MACA,KAAArR,MAAA,GAAAiF,IAAA,CAAAiH,GAAA,WAAA2E,GAAA;QAAA,IAAAS,oBAAA;QACA,IAAAC,WAAA,KAAAD,oBAAA,GAAAT,GAAA,CAAA5B,eAAA,cAAAqC,oBAAA,uBAAAA,oBAAA,CAAArH,KAAA;QACA4G,GAAA,CAAAW,IAAA,GAAA1U,MAAA;QACAqU,OAAA,CAAAM,kBAAA,CAAAZ,GAAA;QACA,IAAAA,GAAA,CAAAO,OAAA;UACA,IAAAM,OAAA,GAAAb,GAAA,CAAAO,OAAA,EAAA7I,MAAA,WAAAoJ,CAAA;YAAA,OAAAJ,WAAA,CAAApK,SAAA,WAAAyK,CAAA;cAAA,OAAAD,CAAA,CAAAzB,YAAA,KAAA0B,CAAA;YAAA;UAAA;UACAF,OAAA,CAAA1B,OAAA,WAAA6B,GAAA,EAAAC,KAAA;YACA,IAAAC,IAAA,GAAAZ,OAAA,CAAAa,YAAA,CAAAnB,GAAA,CAAAW,IAAA,EAAAK,GAAA,CAAA3B,YAAA,EAAA2B,GAAA,CAAAI,eAAA;YACA,IAAAC,GAAA,GAAAf,OAAA,CAAAgB,eAAA,CAAAtB,GAAA,CAAAW,IAAA,EAAAK,GAAA,CAAA3B,YAAA,EAAA2B,GAAA,CAAAI,eAAA;YACApB,GAAA,CAAAkB,IAAA,IAAAF,GAAA,CAAAO,KAAA;YACAvB,GAAA,CAAAqB,GAAA;UACA;QACA;QACAf,OAAA,CAAAkB,WAAA,CAAAxB,GAAA;QACA,OAAAA,GAAA;MACA;MACA,IAAAyB,GAAA;MACA,SAAAhP,KAAA;QACAgP,GAAA,QAAAtS,MAAA,CAAAkM,GAAA,WAAAjI,CAAA;UAAA,OAAAA,CAAA,CAAAgD,qBAAA;QAAA,GAAAsL,QAAA;MACA;QACAD,GAAA,QAAAtS,MAAA,CAAAkM,GAAA,WAAAjI,CAAA;UAAA,OAAAA,CAAA,CAAAuO,iBAAA;QAAA,GAAAD,QAAA;MACA;MACA,KAAA1S,UAAA,GAAAyS,GAAA;IACA;IACAG,eAAA,WAAAA,gBAAAC,OAAA;MAAA,IAAAC,OAAA;MAAA,OAAA/N,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA8N,SAAA;QAAA,IAAAC,eAAA;QAAA,OAAAhO,mBAAA,GAAAQ,IAAA,UAAAyN,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvN,IAAA,GAAAuN,SAAA,CAAAtN,IAAA;YAAA;cAAA,KACAkN,OAAA,CAAAnK,aAAA;gBAAAuK,SAAA,CAAAtN,IAAA;gBAAA;cAAA;cAAAsN,SAAA,CAAAtN,IAAA;cAAA,OACAkN,OAAA,CAAAK,iBAAA,CAAAN,OAAA;YAAA;cAEA9M,OAAA,CAAA9G,IAAA;cACA+T,eAAA;cACAH,OAAA,CAAA1C,OAAA,WAAAiD,OAAA,EAAAnB,KAAA;gBACA,IAAAoB,GAAA,GAAAP,OAAA,CAAAQ,iBAAA,CAAAF,OAAA;gBACA,KAAAC,GAAA;kBACAD,OAAA,CAAAG,KAAA,GAAAH,OAAA,CAAAzB,IAAA;kBACAyB,OAAA,CAAAI,cAAA,GAAAJ,OAAA,CAAA9D,WAAA;kBACA8D,OAAA,CAAAK,eAAA,GAAAvW,OAAA,CAAAkW,OAAA,CAAA9D,WAAA,GAAA8D,OAAA,CAAAM,MAAA,EAAAC,MAAA;kBACA,IAAAb,OAAA,CAAAnK,aAAA,KAAAyK,OAAA,CAAAhE,eAAA;oBACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;oBACA,IAAA0D,OAAA,CAAA3M,YAAA,CAAAiN,OAAA,CAAAQ,eAAA,aAAAC,KAAA;sBACA,IAAAC,UAAA,GAAAhB,OAAA,CAAA3M,YAAA,CAAAiN,OAAA,CAAAQ,eAAA;sBACA,IAAAR,OAAA,CAAArE,iBAAA;wBACA,IAAAgF,kBAAA,GAAAX,OAAA,CAAArE,iBAAA,CAAA3E,KAAA;wBACA,IAAA4J,gBAAA,GAAAD,kBAAA,CAAA5M,KAAA,WAAA8M,IAAA;0BAAA,OAAAH,UAAA,CAAAI,QAAA,CAAAD,IAAA;wBAAA;wBAEA,KAAAD,gBAAA;0BACAhB,eAAA;wBACA;0BACAI,OAAA,CAAAhE,eAAA,GAAA0E,UAAA,CAAAK,IAAA;wBACA;sBACA;wBACAf,OAAA,CAAAhE,eAAA,GAAA0E,UAAA,CAAAK,IAAA;sBACA;oBACA;kBACA;kBACArB,OAAA,CAAA3S,MAAA,CAAAwH,IAAA,CAAAyL,OAAA;kBACAN,OAAA,CAAAlB,kBAAA,CAAAwB,OAAA;kBACA;gBACA;gBAEAC,GAAA,CAAAE,KAAA,GAAAH,OAAA,CAAAzB,IAAA;gBAEA0B,GAAA,CAAAG,cAAA,IAAAJ,OAAA,CAAA9D,WAAA;gBACA+D,GAAA,CAAAI,eAAA,GAAAvW,OAAA,CAAAmW,GAAA,CAAAI,eAAA,EAAAW,GAAA,CAAAhB,OAAA,CAAA9D,WAAA,GAAA8D,OAAA,CAAAM,MAAA,EAAAC,MAAA;gBACA,KAAAN,GAAA,CAAAjE,eAAA;kBACA;gBACA;gBACA0D,OAAA,CAAAN,WAAA,CAAAa,GAAA;cACA;cACAP,OAAA,CAAAuB,uBAAA,CAAArB,eAAA;;cAEA;cACA;cACA;cACA;cACA;cACAF,OAAA,CAAA3S,MAAA,CAAAmU,IAAA,WAAAC,CAAA,EAAAC,CAAA;gBAAA,OAAAD,CAAA,CAAAE,YAAA,GAAAD,CAAA,CAAAC,YAAA;cAAA;cACA1O,OAAA,CAAA2O,OAAA;YAAA;YAAA;cAAA,OAAAxB,SAAA,CAAAjM,IAAA;UAAA;QAAA,GAAA8L,QAAA;MAAA;IACA;IACAnB,kBAAA,WAAAA,mBAAAwB,OAAA;MACA,IAAA9F,GAAA,QAAAqH,SAAA,CAAAvB,OAAA;MACA,KAAAlN,SAAA,CAAAoH,GAAA,IAAA8F,OAAA;IACA;IACAE,iBAAA,WAAAA,kBAAAF,OAAA;MACA,IAAA9F,GAAA,QAAAqH,SAAA,CAAAvB,OAAA;MACA,YAAAlN,SAAA,CAAAoH,GAAA;IACA;IACAqH,SAAA,WAAAA,UAAAvB,OAAA;MACA,SAAAzK,aAAA;QAAA,IAAAiM,qBAAA;QACA,YAAAnR,KAAA,IAAA2P,OAAA,CAAAyB,SAAA,GAAAzB,OAAA,CAAA0B,gBAAA,EAAApC,QAAA,GAAAqC,IAAA,OAAAH,qBAAA,GAAAxB,OAAA,CAAA4B,cAAA,cAAAJ,qBAAA,cAAAA,qBAAA,SAAAxB,OAAA,CAAA6B,SAAA,GAAA7B,OAAA,CAAAT,iBAAA;MACA;QAAA,IAAAuC,sBAAA;QACA,YAAAzR,KAAA,GAAA2P,OAAA,CAAAyB,SAAA,KAAAK,sBAAA,GAAA9B,OAAA,CAAA4B,cAAA,cAAAE,sBAAA,cAAAA,sBAAA,SAAA9B,OAAA,CAAA6B,SAAA,GAAA7B,OAAA,CAAAT,iBAAA;MACA;IACA;IACAwC,SAAA,WAAAA,UAAA;MACA,IAAAC,UAAA;MACA,KAAA3I,KAAA,eAAA4I,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA,EAAAF,UAAA;MACA;MACA,OAAAA,UAAA;IACA;IACAG,SAAA,WAAAA,UAAA;MAAA,IAAAC,UAAA,GAAAhE,SAAA;QAAAiE,OAAA;MAAA,OAAA1Q,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAyQ,SAAA;QAAA,IAAAC,mBAAA;QAAA,IAAAC,OAAA,EAAAC,YAAA,EAAAC,qBAAA,EAAAC,SAAA,EAAA1P,MAAA,EAAA2P,SAAA;QAAA,OAAAhR,mBAAA,GAAAQ,IAAA,UAAAyQ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvQ,IAAA,GAAAuQ,SAAA,CAAAtQ,IAAA;YAAA;cAAAgQ,OAAA,GAAAJ,UAAA,CAAAlM,MAAA,QAAAkM,UAAA,QAAAjU,SAAA,GAAAiU,UAAA;cACAK,YAAA,GAAAJ,OAAA,CAAAN,SAAA;cAAA,IACAU,YAAA;gBAAAK,SAAA,CAAAtQ,IAAA;gBAAA;cAAA;cAAA,OAAAsQ,SAAA,CAAA1F,MAAA;YAAA;cAAAsF,qBAAA,GACAL,OAAA,CAAAU,eAAA,IAAAJ,SAAA,GAAAD,qBAAA,CAAAC,SAAA,EAAA1P,MAAA,GAAAyP,qBAAA,CAAAzP,MAAA;cAAA,IACAA,MAAA;gBAAA6P,SAAA,CAAAtQ,IAAA;gBAAA;cAAA;cAAA,OAAAsQ,SAAA,CAAA1F,MAAA;YAAA;cACA,KAAAoF,OAAA;gBACAH,OAAA,CAAA3U,WAAA;cACA;cAAAoV,SAAA,CAAAtQ,IAAA;cAAA,OAEA6P,OAAA,CAAAW,eAAA,CAAAL,SAAA,EAAAH,OAAA;YAAA;cAAAI,SAAA,GAAAE,SAAA,CAAArQ,IAAA;cACAE,OAAA,CAAAC,GAAA,cAAAgQ,SAAA;cAAA,IACAA,SAAA;gBAAAE,SAAA,CAAAtQ,IAAA;gBAAA;cAAA;cAAA,OAAAsQ,SAAA,CAAA1F,MAAA;YAAA;cAAA,KACAoF,OAAA;gBAAAM,SAAA,CAAAtQ,IAAA;gBAAA;cAAA;cAAA,OAAAsQ,SAAA,CAAA1F,MAAA,WAAAwF,SAAA;YAAA;cACA,CAAAL,mBAAA,GAAAF,OAAA,CAAAhJ,KAAA,uBAAAkJ,mBAAA,eAAAA,mBAAA,CAAA/O,SAAA;cACA6O,OAAA,CAAA3U,WAAA;YAAA;YAAA;cAAA,OAAAoV,SAAA,CAAAjP,IAAA;UAAA;QAAA,GAAAyO,QAAA;MAAA;IACA;IACAW,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,OAAAvR,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAsR,SAAA;QAAA,IAAAV,YAAA,EAAAW,GAAA,EAAAC,IAAA;QAAA,OAAAzR,mBAAA,GAAAQ,IAAA,UAAAkR,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhR,IAAA,GAAAgR,SAAA,CAAA/Q,IAAA;YAAA;cACAiQ,YAAA,GAAAS,OAAA,CAAAnB,SAAA;cAAA,IACAU,YAAA;gBAAAc,SAAA,CAAA/Q,IAAA;gBAAA;cAAA;cAAA,OAAA+Q,SAAA,CAAAnG,MAAA;YAAA;cACAgG,GAAA;cAAA,IACAF,OAAA,CAAAnW,MAAA,CAAAmJ,MAAA;gBAAAqN,SAAA,CAAA/Q,IAAA;gBAAA;cAAA;cACA0Q,OAAA,CAAA7O,QAAA;gBACAC,OAAA;gBACA9C,IAAA;cACA;cAAA,OAAA+R,SAAA,CAAAnG,MAAA;YAAA;cAGA,IAAA8F,OAAA,CAAA7S,KAAA;gBACA+S,GAAA,CAAAzG,eAAA,GAAAuG,OAAA,CAAAnW,MAAA;cACA;gBACAqW,GAAA,CAAAnF,cAAA,GAAAiF,OAAA,CAAAnW,MAAA;cACA;cACA,IAAAmW,OAAA,CAAA3S,MAAA;gBACA6S,GAAA,CAAA1G,cAAA,GAAAwG,OAAA,CAAA7W,UAAA;cACA;gBACA+W,GAAA,CAAA1G,cAAA,GAAAtM,aAAA,CAAAA,aAAA,KACA8S,OAAA,CAAA7W,UAAA;kBACAiK,UAAA,EAAA4M,OAAA,CAAAtU,SAAA;kBACA4U,OAAA,EAAAN,OAAA,CAAArU,MAAA;kBACA4U,eAAA,EAAAP,OAAA,CAAAhQ,KAAA;gBAAA,EACA;cACA;cACAgQ,OAAA,CAAA9V,SAAA;cACAiW,IAAA,GAAAH,OAAA,CAAA7S,KAAA,GAAApH,+BAAA,GAAAG,6BAAA;cACAia,IAAA,CAAAD,GAAA,EAAAzM,IAAA,WAAAmC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAmK,OAAA,CAAA9V,SAAA;kBACA8V,OAAA,CAAA7O,QAAA;oBACAC,OAAA;oBACA9C,IAAA;kBACA;kBACA0R,OAAA,CAAA9I,SAAA;gBACA;kBACA8I,OAAA,CAAA7O,QAAA;oBACAC,OAAA,EAAAwE,GAAA,CAAAE,OAAA;oBACAxH,IAAA;kBACA;kBACA0R,OAAA,CAAA9V,SAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAmW,SAAA,CAAA1P,IAAA;UAAA;QAAA,GAAAsP,QAAA;MAAA;IACA;IACAJ,eAAA,WAAAA,gBAAA;MAAA,IAAAW,OAAA;MACA;MACA,IAAAf,SAAA,GAAAgB,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAA9W,MAAA;MACA4V,SAAA,GAAAA,SAAA,CAAArN,MAAA,WAAAxE,IAAA;QAAA,OAAAA,IAAA,CAAAsP,cAAA;MAAA;MAAA,IAAA0D,KAAA,YAAAA,MAAA,EACA;UACA,IAAA9D,OAAA,GAAA2C,SAAA,CAAA9I,CAAA;UACA,IAAA7H,IAAA;UACA,KAAAgO,OAAA,CAAAhE,eAAA;YACA0H,OAAA,CAAArP,QAAA;cACAC,OAAA;cACA9C,IAAA;YACA;YAAA;cAAAR,CAAA,EACA;gBAAAiC,MAAA;cAAA;YAAA;UACA;UACA,IAAAyQ,OAAA,CAAAvS,aAAA,KAAA6O,OAAA,CAAArE,iBAAA,IAAAqE,OAAA,CAAAnH,IAAA,iBAAA6K,OAAA,CAAAlP,OAAA;YACA,IAAAuP,GAAA;YACA,IAAAL,OAAA,CAAArS,MAAA;cACA,IAAA2O,OAAA,CAAAhM,qBAAA;gBACA0P,OAAA,CAAArP,QAAA;kBACAC,OAAA,EAAAyP,GAAA;kBACAvS,IAAA;gBACA;gBAAA;kBAAAR,CAAA,EACA;oBAAAiC,MAAA;kBAAA;gBAAA;cACA;YACA;cACAyQ,OAAA,CAAArP,QAAA;gBACAC,OAAA,EAAAyP,GAAA;gBACAvS,IAAA;cACA;cAAA;gBAAAR,CAAA,EACA;kBAAAiC,MAAA;gBAAA;cAAA;YACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAA+M,OAAA,CAAA/D,yBAAA,IAAA+D,OAAA,CAAA/D,yBAAA,KAAA+D,OAAA,CAAAhE,eAAA;YACA0H,OAAA,CAAArP,QAAA;cACAC,OAAA,6EAAAiD,MAAA,CAAAmM,OAAA,CAAArT,KAAA,MAAAkH,MAAA,CAAAmM,OAAA,CAAAvY,OAAA;cACAqG,IAAA;YACA;YAAA;cAAAR,CAAA,EACA;gBAAAiC,MAAA;cAAA;YAAA;UACA;UACA,IAAA+M,OAAA,CAAAtE,sBAAA,IAAAsE,OAAA,CAAAtE,sBAAA,KAAAsE,OAAA,CAAArE,iBAAA;YACA+H,OAAA,CAAArP,QAAA;cACAC,OAAA;cACA9C,IAAA;YACA;YAAA;cAAAR,CAAA,EACA;gBAAAiC,MAAA;cAAA;YAAA;UACA;UACA,IAAAqL,WAAA,GAAAmC,KAAA,CAAAuD,IAAA,KAAAC,GAAA,CAAAjE,OAAA,CAAAhE,eAAA,CAAAhF,KAAA;UACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UApBA,IAAAkN,MAAA,YAAAA,OAAA,EAqBA;YACA,IAAApF,IAAA,GAAAR,WAAA,CAAA6F,CAAA;YACA,IAAAC,aAAA,GAAApE,OAAA,CAAAI,cAAA;YACA,IAAAiE,MAAA;YACA,IAAArE,OAAA,CAAAsE,gBAAA;cACAD,MAAA,GAAArE,OAAA,CAAAsE,gBAAA,CAAAhP,MAAA,WAAAtE,CAAA;gBAAA,OAAAA,CAAA,CAAAiM,YAAA,KAAA6B,IAAA;cAAA;YACA;YACA,IAAAyF,UAAA,GAAAF,MAAA,CAAAG,MAAA,WAAAC,GAAA,EAAAxE,GAAA;cACA,OAAAwE,GAAA,IAAAxE,GAAA,CAAAyE,WAAA;YACA;YACA,IAAAH,UAAA,GAAAH,aAAA;cACApS,IAAA;cAAA;YAEA;cAAA,IAAA2S,MAAA;cACA,CAAAA,MAAA,GAAA3S,IAAA,EAAAuC,IAAA,CAAAqQ,KAAA,CAAAD,MAAA,EAAAnN,kBAAA,CAAA6M,MAAA;YACA;UACA;UAhBA,SAAAF,CAAA,MAAAA,CAAA,GAAA7F,WAAA,CAAApI,MAAA,EAAAiO,CAAA;YAAA,IAAAD,MAAA,IAYA;UAAA;UAKA,IAAAW,QAAA,GAAAhI,MAAA,CAAAiI,IAAA,CAAA9E,OAAA,EAAA1K,MAAA,WAAA8D,CAAA;YAAA,OAAAA,CAAA,CAAA2L,UAAA,CAAA/E,OAAA;UAAA;UACA6E,QAAA,CAAA9H,OAAA,WAAAjM,IAAA;YACA,OAAAkP,OAAA,CAAAlP,IAAA;UACA;UACA,OAAAkP,OAAA;UACA,OAAAA,OAAA;UACA,OAAAA,OAAA;UACAA,OAAA,CAAAsE,gBAAA,GAAAtS,IAAA;QACA;QAAAgT,IAAA;MAjGA,SAAAnL,CAAA,MAAAA,CAAA,GAAA8I,SAAA,CAAAzM,MAAA,EAAA2D,CAAA;QAAAmL,IAAA,GAAAlB,KAAA;QAAA,IAAAkB,IAAA,SAAAA,IAAA,CAAAhU,CAAA;MAAA;MAkGA;QAAA2R,SAAA,EAAAA,SAAA;QAAA1P,MAAA;MAAA;IACA;IACA+P,eAAA,WAAAA,gBAAAL,SAAA,EAAAH,OAAA;MAAA,IAAAyC,OAAA;MAAA,OAAAtT,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAqT,SAAA;QAAA,IAAA7B,IAAA,EAAAD,GAAA,EAAAzE,CAAA,EAAAwG,MAAA,EAAAC,YAAA;QAAA,OAAAxT,mBAAA,GAAAQ,IAAA,UAAAiT,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/S,IAAA,GAAA+S,SAAA,CAAA9S,IAAA;YAAA;cACAG,OAAA,CAAAC,GAAA;cACAyQ,IAAA,GAAA4B,OAAA,CAAA5U,KAAA,GAAAnH,sBAAA,GAAAC,yBAAA;cACAia,GAAA;cACA,IAAA6B,OAAA,CAAA5U,KAAA;gBACA+S,GAAA,CAAAzG,eAAA,GAAAgG,SAAA;gBACAhE,CAAA;gBACA,KAAAwG,MAAA,IAAAF,OAAA,CAAA3G,WAAA;kBACA,IAAA2G,OAAA,CAAA3G,WAAA,CAAAiH,cAAA,CAAAJ,MAAA;oBACAxG,CAAA,CAAApK,IAAA,CAAA0Q,OAAA,CAAA3G,WAAA,CAAA6G,MAAA;kBACA;gBACA;gBACA/B,GAAA,CAAAxG,YAAA,GAAA+B,CAAA;cACA;gBACAyE,GAAA,CAAAnF,cAAA,GAAA0E,SAAA;cACA;cACA,IAAAsC,OAAA,CAAA1U,MAAA;gBACA6S,GAAA,CAAA1G,cAAA,GAAAuI,OAAA,CAAA5Y,UAAA;cACA;gBACA+W,GAAA,CAAA1G,cAAA,GAAAtM,aAAA,CAAAA,aAAA,KACA6U,OAAA,CAAA5Y,UAAA;kBACAiK,UAAA,EAAA2O,OAAA,CAAArW,SAAA;kBACA4U,OAAA,EAAAyB,OAAA,CAAApW,MAAA;kBACA4U,eAAA,EAAAwB,OAAA,CAAA/R,KAAA;gBAAA,EACA;cACA;cACAkS,YAAA;cACAzS,OAAA,CAAAC,GAAA,QAAAwQ,GAAA;cAAAkC,SAAA,CAAA9S,IAAA;cAAA,OAEA6Q,IAAA,CAAAD,GAAA,EAAAzM,IAAA,WAAAmC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACA,KAAAyJ,OAAA;oBACAyC,OAAA,CAAA7X,SAAA;oBACA6X,OAAA,CAAA5Q,QAAA;sBACAC,OAAA;sBACA9C,IAAA;oBACA;oBACAyT,OAAA,CAAA7K,SAAA;kBACA;oBACA6K,OAAA,CAAAO,oBAAA,GAAA1M,GAAA,CAAA3C,IAAA;oBACAiP,YAAA;oBACAzS,OAAA,CAAAC,GAAA;kBACA;gBACA;kBACAqS,OAAA,CAAAvX,WAAA;kBACAuX,OAAA,CAAA7X,SAAA;kBACA6X,OAAA,CAAA5Q,QAAA;oBACAC,OAAA,EAAAwE,GAAA,CAAAE,OAAA;oBACAxH,IAAA;kBACA;gBACA;cACA;YAAA;cACAmB,OAAA,CAAAC,GAAA;cAAA,OAAA0S,SAAA,CAAAlI,MAAA,WACAgI,YAAA;YAAA;YAAA;cAAA,OAAAE,SAAA,CAAAzR,IAAA;UAAA;QAAA,GAAAqR,QAAA;MAAA;IACA;IACAO,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,KAAArY,aAAA;MACAsY,UAAA;QACA,IAAAC,aAAA,OAAA3B,GAAA,CAAAyB,OAAA,CAAAxY,iBAAA,CAAA+L,GAAA,WAAAjI,CAAA;UAAA,OAAAA,CAAA,CAAAuN,IAAA;QAAA;QACAmH,OAAA,CAAA3Y,MAAA,GAAA2Y,OAAA,CAAA3Y,MAAA,CAAAuI,MAAA,WAAAxE,IAAA;UACA,IAAA+U,UAAA,GAAAD,aAAA,CAAAE,GAAA,CAAAhV,IAAA,CAAAyN,IAAA;UACA,IAAAsH,UAAA;YACA,IAAA3L,GAAA,GAAAwL,OAAA,CAAAnE,SAAA,CAAAzQ,IAAA;YACA,OAAA4U,OAAA,CAAA5S,SAAA,CAAAoH,GAAA;UACA;UACA,QAAA2L,UAAA;QACA;QACAH,OAAA,CAAAvM,SAAA,WAAAC,CAAA;UAAA,IAAA2M,mBAAA;UACA,CAAAA,mBAAA,GAAAL,OAAA,CAAArM,KAAA,uBAAA0M,mBAAA,eAAAA,mBAAA,CAAAC,SAAA,CAAAN,OAAA,CAAAxY,iBAAA;UACAwY,OAAA,CAAAxY,iBAAA;QACA;QACAwY,OAAA,CAAArY,aAAA;MACA;IACA;IACAyH,WAAA,WAAAA,YAAA;MAAA,IAAAmR,OAAA;MAAA,OAAAtU,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAqU,UAAA;QAAA,OAAAtU,mBAAA,GAAAQ,IAAA,UAAA+T,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA7T,IAAA,GAAA6T,UAAA,CAAA5T,IAAA;YAAA;cAAA4T,UAAA,CAAA5T,IAAA;cAAA,OACAxJ,wBAAA;gBACAwI,IAAA,EAAAyU,OAAA,CAAA5V,KAAA;cACA,GAAAsG,IAAA,WAAAmC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAkN,OAAA,CAAA1X,WAAA,GAAAuK,GAAA,CAAA3C,IAAA;gBACA;kBACA8P,OAAA,CAAA5R,QAAA;oBACAC,OAAA,EAAAwE,GAAA,CAAAE,OAAA;oBACAxH,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA4U,UAAA,CAAAvS,IAAA;UAAA;QAAA,GAAAqS,SAAA;MAAA;IACA;IACAG,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,KAAAjN,KAAA,eAAA4I,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;QACA,IAAAqE,qBAAA,GAAAD,OAAA,CAAAvD,eAAA;UAAAJ,SAAA,GAAA4D,qBAAA,CAAA5D,SAAA;UAAA1P,MAAA,GAAAsT,qBAAA,CAAAtT,MAAA;QACA,KAAAA,MAAA;QACAqT,OAAA,CAAA9P,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAlF,IAAA;QACA,GAAAmF,IAAA;UACA2P,OAAA,CAAAE,iBAAA,CAAA7D,SAAA;QACA,GAAA/L,KAAA;UACA0P,OAAA,CAAAjS,QAAA;YACA7C,IAAA;YACA8C,OAAA;UACA;QACA;MACA;IACA;IACAkS,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MAAA,OAAA9U,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6U,UAAA;QAAA,IAAAC,kBAAA;QAAA,IAAA/D,SAAA,EAAAgE,UAAA;QAAA,OAAAhV,mBAAA,GAAAQ,IAAA,UAAAyU,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAvU,IAAA,GAAAuU,UAAA,CAAAtU,IAAA;YAAA;cACAiU,OAAA,CAAArZ,SAAA;cAAA,OAAAuZ,kBAAA,GACAF,OAAA,CAAApa,UAAA,cAAAsa,kBAAA,eAAAA,kBAAA,CAAAra,cAAA;gBAAAwa,UAAA,CAAAtU,IAAA;gBAAA;cAAA;cAAAsU,UAAA,CAAAtU,IAAA;cAAA,OACAiU,OAAA,CAAAtE,SAAA;YAAA;cAAAS,SAAA,GAAAkE,UAAA,CAAArU,IAAA;cACAE,OAAA,CAAAC,GAAA,sBAAAgQ,SAAA;cACAA,SAAA,IAAA6D,OAAA,CAAAM,QAAA,CAAAN,OAAA,CAAApa,UAAA,CAAA4E,EAAA;cAAA6V,UAAA,CAAAtU,IAAA;cAAA;YAAA;cAAAsU,UAAA,CAAAtU,IAAA;cAAA,OAEAiU,OAAA,CAAAtE,SAAA;YAAA;cAAAS,UAAA,GAAAkE,UAAA,CAAArU,IAAA;cACAmQ,UAAA,IAAA6D,OAAA,CAAAM,QAAA,CAAAN,OAAA,CAAAjB,oBAAA;YAAA;YAAA;cAAA,OAAAsB,UAAA,CAAAjT,IAAA;UAAA;QAAA,GAAA6S,SAAA;MAAA;IAEA;IACAK,QAAA,WAAAA,SAAAC,YAAA;MAAA,IAAAC,OAAA;MACA5d,qBAAA;QACA6d,eAAA,EAAAF;MACA,GAAArQ,IAAA,WAAAmC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAkO,OAAA,CAAA5S,QAAA;YACAC,OAAA;YACA9C,IAAA;UACA;UACAyV,OAAA,CAAA7M,SAAA;QACA;UACA6M,OAAA,CAAA5S,QAAA;YACAC,OAAA,EAAAwE,GAAA,CAAAE,OAAA;YACAxH,IAAA;UACA;QACA;MACA,GAAA2V,OAAA,WAAA/N,CAAA;QACA6N,OAAA,CAAA7Z,SAAA;MACA,GAAAwJ,KAAA,WAAAwC,CAAA;QACA6N,OAAA,CAAA7Z,SAAA;MACA;IACA;IACAga,WAAA,WAAAA,YAAA7b,KAAA;MAAA,IAAA8b,OAAA;MAAA,OAAA1V,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAyV,UAAA;QAAA,IAAAC,MAAA,EAAA3J,GAAA,EAAA4J,eAAA,EAAAvW,EAAA,EAAAwW,YAAA,EAAAC,gBAAA;QAAA,OAAA9V,mBAAA,GAAAQ,IAAA,UAAAuV,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAArV,IAAA,GAAAqV,UAAA,CAAApV,IAAA;YAAA;cAEA+U,MAAA,GAMAhc,KAAA,CANAgc,MAAA,EACA3J,GAAA,GAKArS,KAAA,CALAqS,GAAA,EAAA4J,eAAA,GAKAjc,KAAA,CAJAsc,QAAA,EACA5W,EAAA,GAAAuW,eAAA,CAAAvW,EAAA,EACAwW,YAAA,GAAAD,eAAA,CAAAC,YAAA;cAGA,IAAAF,MAAA;gBACA,KAAAG,gBAAA,GAAAnc,KAAA,CAAAsc,QAAA,cAAAH,gBAAA,eAAAA,gBAAA,CAAAzW,EAAA;kBACA2M,GAAA,CAAA9B,aAAA,GAAA2L,YAAA;kBACA7J,GAAA,CAAAhC,WAAA,GAAA3K,EAAA;kBACAoW,OAAA,CAAAS,OAAA,CAAAlK,GAAA,EAAA3M,EAAA;gBACA;kBACA2M,GAAA,CAAA9B,aAAA;kBACA8B,GAAA,CAAAhC,WAAA;gBACA;cACA;gBACA;gBACA;gBACAyL,OAAA,CAAAna,iBAAA,CAAA6P,OAAA,WAAAjM,IAAA;kBAAA,IAAAiX,gBAAA;kBACA,KAAAA,gBAAA,GAAAxc,KAAA,CAAAsc,QAAA,cAAAE,gBAAA,eAAAA,gBAAA,CAAA9W,EAAA;oBACAH,IAAA,CAAAgL,aAAA,GAAA2L,YAAA;oBACA3W,IAAA,CAAA8K,WAAA,GAAA3K,EAAA;oBACAoW,OAAA,CAAAS,OAAA,CAAAhX,IAAA,EAAAG,EAAA;kBACA;oBACAH,IAAA,CAAAgL,aAAA;oBACAhL,IAAA,CAAA8K,WAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAgM,UAAA,CAAA/T,IAAA;UAAA;QAAA,GAAAyT,SAAA;MAAA;IACA;IACAQ,OAAA,WAAAA,QAAAlK,GAAA,EAAA3M,EAAA;MACA,IAAA2M,GAAA,aAAAA,GAAA,eAAAA,GAAA,CAAA/B,qBAAA;QACA,IAAA+B,GAAA,CAAA/B,qBAAA,KAAA5K,EAAA;UACA2M,GAAA,CAAA5B,eAAA;QACA;MACA;QACA4B,GAAA,CAAA5B,eAAA;MACA;IACA;IACAgM,mBAAA,WAAAA,oBAAAT,MAAA,EAAA3J,GAAA;MAAA,IAAAqK,OAAA;MACA,KAAAla,KAAA,GAAAwZ,MAAA;MACA,KAAA1Z,gBAAA;MACA,KAAAC,MAAA;MACA,KAAAN,aAAA;MACA,KAAA2L,SAAA,WAAAC,CAAA;QACA6O,OAAA,CAAA5O,KAAA,YAAA7F,SAAA,CAAA+T,MAAA,EAAA3J,GAAA,EAAAqK,OAAA,CAAA/a,iBAAA;MACA;IACA;IACA6S,iBAAA,WAAAA,kBAAA/N,IAAA;MAAA,IAAAkW,OAAA;MAAA,OAAAvW,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAsW,UAAA;QAAA,IAAAC,KAAA,EAAAC,MAAA,EAAAnO,GAAA,EAAAoO,aAAA;QAAA,OAAA1W,mBAAA,GAAAQ,IAAA,UAAAmW,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAjW,IAAA,GAAAiW,UAAA,CAAAhW,IAAA;YAAA;cACA4V,KAAA,GAAA5Q,kBAAA,KAAAyM,GAAA,CAAAjS,IAAA,CAAAiH,GAAA,WAAAjI,CAAA;gBAAA,OAAAA,CAAA,CAAAwP,eAAA;cAAA;cAAA6H,MAAA,gBAAAzW,mBAAA,GAAAC,IAAA,UAAAwW,OAAAnO,GAAA;gBAAA,OAAAtI,mBAAA,GAAAQ,IAAA,UAAAqW,QAAAC,UAAA;kBAAA,kBAAAA,UAAA,CAAAnW,IAAA,GAAAmW,UAAA,CAAAlW,IAAA;oBAAA;sBAEA,IAAA0V,OAAA,CAAAnV,YAAA,CAAAwS,cAAA,CAAArL,GAAA;wBACAkO,KAAA,GAAAA,KAAA,CAAA9S,MAAA,WAAAwJ,IAAA;0BAAA,OAAAA,IAAA,KAAA5E,GAAA;wBAAA;sBACA;oBAAA;oBAAA;sBAAA,OAAAwO,UAAA,CAAA7U,IAAA;kBAAA;gBAAA,GAAAwU,MAAA;cAAA;cAAAG,UAAA,CAAAG,EAAA,GAAA/W,mBAAA,GAAAkT,IAAA,CAHAoD,OAAA,CAAAnV,YAAA;YAAA;cAAA,KAAAyV,UAAA,CAAAI,EAAA,GAAAJ,UAAA,CAAAG,EAAA,IAAAE,IAAA;gBAAAL,UAAA,CAAAhW,IAAA;gBAAA;cAAA;cAAA0H,GAAA,GAAAsO,UAAA,CAAAI,EAAA,CAAArd,KAAA;cAAA,OAAAid,UAAA,CAAAM,aAAA,CAAAT,MAAA,CAAAnO,GAAA;YAAA;cAAAsO,UAAA,CAAAhW,IAAA;cAAA;YAAA;cAAAgW,UAAA,CAAAhW,IAAA;cAAA,OAKA0V,OAAA,CAAAa,eAAA,CAAAX,KAAA;YAAA;cAAAE,aAAA,GAAAE,UAAA,CAAA/V,IAAA;cACAoK,MAAA,CAAAC,MAAA,CAAAoL,OAAA,CAAAnV,YAAA,EAAAuV,aAAA;YAAA;YAAA;cAAA,OAAAE,UAAA,CAAA3U,IAAA;UAAA;QAAA,GAAAsU,SAAA;MAAA;IACA;IACAY,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MAAA,IAAAC,OAAA,GAAA7K,SAAA,CAAAlI,MAAA,QAAAkI,SAAA,QAAAjQ,SAAA,GAAAiQ,SAAA;MACA6K,OAAA,GAAAA,OAAA,CAAA3T,MAAA,WAAAtE,CAAA;QAAA,SAAAA,CAAA;MAAA;MACA,KAAAiY,OAAA,CAAA/S,MAAA,SAAAkF,OAAA,CAAAC,OAAA;MACA,WAAAD,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACAtR,gCAAA;UACAkf,eAAA,EAAAD,OAAA;UACApQ,IAAA;QACA,GAAAlC,IAAA,WAAAmC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACA,IAAAoQ,MAAA,GAAArQ,GAAA,CAAA3C,IAAA;YACA,IAAAiT,KAAA,GAAAD,MAAA,CAAA3E,MAAA,WAAAC,GAAA,EAAA3T,IAAA;cACA2T,GAAA,CAAA3T,IAAA,CAAA4B,IAAA,IAAA5B,IAAA,CAAAkL,eAAA;cACA,OAAAyI,GAAA;YACA;YACA9R,OAAA,CAAAC,GAAA,UAAAwW,KAAA;YACA/N,OAAA,CAAA+N,KAAA;UACA;YACAJ,OAAA,CAAA3U,QAAA;cACAC,OAAA,EAAAwE,GAAA,CAAAE,OAAA;cACAxH,IAAA;YACA;YACA8J,MAAA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACA+N,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MAAA,OAAA3X,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA0X,UAAA;QAAA,OAAA3X,mBAAA,GAAAQ,IAAA,UAAAoX,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAlX,IAAA,GAAAkX,UAAA,CAAAjX,IAAA;YAAA;cACA;AACA;AACA;AACA;;cAEA8W,OAAA,CAAA9S,QAAA,oDAAAe,MAAA,CAAA+R,OAAA,CAAAne,OAAA;gBACAsL,iBAAA;gBACAC,gBAAA;gBACAlF,IAAA;cACA,GAAAmF,IAAA;gBACA,IAAA2S,OAAA,CAAAjU,eAAA;kBACA,IAAAsJ,CAAA,GAAA2K,OAAA,CAAApc,iBAAA,CAAA+L,GAAA,WAAAnI,IAAA;oBACA;sBACA4Y,UAAA,KAAAnS,MAAA,CAAAzG,IAAA,CAAA+H,IAAA,SAAAtB,MAAA,CAAAzG,IAAA,CAAA8K,WAAA;oBACA;kBACA;kBACA,IAAAwM,KAAA,GAAA3H,KAAA,CAAAuD,IAAA,KAAAC,GAAA,CAAAtF,CAAA,CAAA1F,GAAA,WAAAjI,CAAA;oBAAA,OAAAA,CAAA,CAAA0Y,UAAA;kBAAA;kBACA,IAAAvE,MAAA;kBACA/J,OAAA,CAAAuO,GAAA,CAAAvB,KAAA,CAAAnP,GAAA,WAAAjI,CAAA;oBACA,IAAA4Y,IAAA,GAAA5Y,CAAA,CAAAgG,KAAA;oBACA,OAAAsS,OAAA,CAAAO,UAAA,CAAAD,IAAA,KAAAA,IAAA;kBACA,EACA,EAAAjT,IAAA,WAAAmC,GAAA;oBACA,IAAAgR,YAAA,GAAAhR,GAAA,CAAAnB,IAAA,WAAA7G,IAAA;sBAAA,OAAAA,IAAA,IAAA3C,SAAA;oBAAA;oBACA,IAAA2b,YAAA;sBACAR,OAAA,CAAAjV,QAAA;wBACAC,OAAA,iEAAAiD,MAAA,CAAA+R,OAAA,CAAAne,OAAA;wBACAqG,IAAA;sBACA;oBACA;oBAEAsH,GAAA,CAAAiE,OAAA,WAAAiD,OAAA,EAAA/L,GAAA;sBACAkR,MAAA,CAAAiD,KAAA,CAAAnU,GAAA,KAAA+L,OAAA;oBACA;oBACAsJ,OAAA,CAAApc,iBAAA,CAAA6P,OAAA,WAAAiD,OAAA;sBACAA,OAAA,CAAAhE,eAAA,GAAAmJ,MAAA,IAAA5N,MAAA,CAAAyI,OAAA,CAAAnH,IAAA,SAAAtB,MAAA,CAAAyI,OAAA,CAAApE,WAAA;sBACA0N,OAAA,CAAAS,gBAAA,CAAA/J,OAAA,EAAAA,OAAA,CAAAhE,eAAA;oBACA;kBACA;gBACA;kBACA,IAAA2C,EAAA,GAAA2K,OAAA,CAAApc,iBAAA,CAAA+L,GAAA,WAAAnI,IAAA;oBAAA,OAAAA,IAAA,CAAA+H,IAAA;kBAAA;kBACA,IAAAuP,MAAA,GAAA3H,KAAA,CAAAuD,IAAA,KAAAC,GAAA,CAAAtF,EAAA;kBACA,IAAAwG,OAAA;kBAEA/J,OAAA,CAAAuO,GAAA,CAAAvB,MAAA,CAAAnP,GAAA,WAAAjI,CAAA;oBACA,OAAAsY,OAAA,CAAAO,UAAA,CAAA7Y,CAAA;kBACA,IAAA2F,IAAA,WAAAmC,GAAA;oBACAA,GAAA,CAAAiE,OAAA,WAAAiD,OAAA,EAAA/L,GAAA;sBACAkR,OAAA,CAAAiD,MAAA,CAAAnU,GAAA,KAAA+L,OAAA;oBACA;oBACAsJ,OAAA,CAAApc,iBAAA,CAAA6P,OAAA,WAAAiD,OAAA;sBACAA,OAAA,CAAAhE,eAAA,GAAAmJ,OAAA,CAAAnF,OAAA,CAAAnH,IAAA;sBACAyQ,OAAA,CAAAS,gBAAA,CAAA/J,OAAA,EAAAA,OAAA,CAAAhE,eAAA;oBACA;kBACA;gBACA;cACA,GAAApF,KAAA;gBACA0S,OAAA,CAAAjV,QAAA;kBACA7C,IAAA;kBACA8C,OAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAmV,UAAA,CAAA5V,IAAA;UAAA;QAAA,GAAA0V,SAAA;MAAA;IACA;IACAS,gBAAA,WAAAA,iBAAAC,UAAA;MAAA,IAAAC,OAAA;MACA,WAAA9O,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACArR,kBAAA;UACAggB,UAAA,EAAAA,UAAA;UACAzY,IAAA;QACA,GAAAmF,IAAA,WAAAmC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACA,IAAAoR,OAAA,GAAArR,GAAA,CAAA3C,IAAA,CAAA8C,GAAA,WAAAjI,CAAA;cAAA,OAAAA,CAAA,CAAA0B,IAAA;YAAA;YACA2I,OAAA,CAAA8O,OAAA;UACA;YACAD,OAAA,CAAA7V,QAAA;cACAC,OAAA,EAAAwE,GAAA,CAAAE,OAAA;cACAxH,IAAA;YACA;UACA;QACA;MACA;IACA;IACAqY,UAAA,WAAAA,WAAA/K,IAAA,EAAAmL,UAAA;MAAA,IAAAG,OAAA;MACA,WAAAhP,OAAA,WAAAC,OAAA;QACA,IAAA+H,GAAA;UACAiH,cAAA,EAAAvL,IAAA;UACAtN,IAAA;QACA;QACA,IAAA4Y,OAAA,CAAA/U,eAAA;UACA+N,GAAA,CAAA6G,UAAA,GAAAA,UAAA;QACA;QACAlgB,cAAA,CAAAqZ,GAAA,EAAAzM,IAAA,WAAAmC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACA,IAAAD,GAAA,CAAA3C,IAAA,CAAAA,IAAA,IAAA2C,GAAA,CAAA3C,IAAA,CAAAA,IAAA,CAAAD,MAAA;cACA,IAAA0T,IAAA,GAAA9Q,GAAA,CAAA3C,IAAA,CAAAA,IAAA;cACA,IAAAmU,QAAA,GAAAV,IAAA,CAAAW,QAAA,IAAAX,IAAA,CAAAW,QAAA,CAAAC,OAAA;cACAnP,OAAA,CAAAiP,QAAA;YACA;cACAjP,OAAA,CAAAlN,SAAA;YACA;UACA;YACAic,OAAA,CAAA/V,QAAA;cACAC,OAAA,EAAAwE,GAAA,CAAAE,OAAA;cACAxH,IAAA;YACA;UACA;QACA;MACA;IACA;IACAiZ,WAAA,WAAAA,YAAA7M,GAAA;MACA,KAAAwB,WAAA,CAAAxB,GAAA;IACA;IACAwB,WAAA,WAAAA,YAAAxB,GAAA;MACA,IAAA8M,eAAA,GAAA7N,MAAA,CAAAiI,IAAA,CAAAlH,GAAA,EACAtI,MAAA,WAAAtE,CAAA;QAAA,QAAAA,CAAA,CAAA2Z,QAAA,WAAA3Z,CAAA,CAAA+T,UAAA,CAAAnH,GAAA,CAAAW,IAAA,KAAAvN,CAAA,CAAAkF,MAAA,GAAA0H,GAAA,CAAAW,IAAA,CAAArI,MAAA;MAAA;MACAwU,eAAA,CAAA3N,OAAA,WAAA6N,GAAA;QACA,IAAAC,OAAA,GAAAD,GAAA,CAAA5T,KAAA,CAAAjM,YAAA;QACA,IAAA+f,UAAA,GAAAJ,eAAA,CAAApV,MAAA,WAAAyV,CAAA;UACA,IAAAjM,IAAA,GAAAiM,CAAA,CAAA/T,KAAA,CAAAjM,YAAA;UACA,OAAAggB,CAAA,KAAAH,GAAA,IAAA9L,IAAA,KAAA+L,OAAA;QACA,GAAArG,MAAA,WAAAC,GAAA,EAAA3T,IAAA;UACA,OAAA2T,GAAA,GAAA3a,OAAA,CAAA8T,GAAA,CAAA9M,IAAA,GAAAvF,KAAA;QACA;QACAqS,GAAA,CAAAgN,GAAA,GAAA7f,YAAA,YAAA6S,GAAA,CAAAwC,cAAA,GAAA0K,UAAA;MACA;IACA;IACAE,WAAA,WAAAA,YAAAC,KAAA;MAAA,IAAAlU,GAAA,GAAAkU,KAAA,CAAAlU,GAAA;QAAAmU,GAAA,GAAAD,KAAA,CAAAC,GAAA;MACA,IAAAtI,SAAA;MACA,SAAA/I,CAAA,MAAAA,CAAA,GAAA9C,GAAA,CAAAb,MAAA,EAAA2D,CAAA;QACA,IAAA/I,IAAA,GAAAiG,GAAA,CAAA8C,CAAA;QACA,IAAA/I,IAAA,CAAAqa,YAAA,IAAAra,IAAA,CAAAqa,YAAA,KAAAD,GAAA;UACAtI,SAAA;UACA;QACA;QACA9R,IAAA,CAAAkL,eAAA,GAAAkP,GAAA;MACA;MACA,KAAAtI,SAAA;QACA,KAAAvO,QAAA;UACAC,OAAA,6EAAAiD,MAAA,MAAApM,OAAA;UACAqG,IAAA;QACA;MACA;IACA;IACAuY,gBAAA,WAAAA,iBAAAnM,GAAA,EAAAsN,GAAA;MAAA,IAAAE,IAAA;QAAAC,OAAA;MACA,IAAAH,GAAA;QACAtN,GAAA,CAAA5B,eAAA,GAAAkP,GAAA;MACA;QACAA,GAAA,GAAAtN,GAAA,CAAA5B,eAAA;MACA;MACA,IAAAhK,IAAA,KAAAoZ,IAAA,GAAAF,GAAA,cAAAE,IAAA,uBAAAA,IAAA,CAAApU,KAAA;MACA,KAAAzI,WAAA,CAAAwO,OAAA,WAAAiD,OAAA,EAAA/L,GAAA;QACA,IAAAgM,GAAA,GAAAjO,IAAA,CAAA2F,IAAA,WAAA2T,CAAA;UAAA,OAAAA,CAAA,KAAAtL,OAAA,CAAA/C,YAAA;QAAA;QACA,IAAA6B,IAAA,GAAAuM,OAAA,CAAAtM,YAAA,CAAAnB,GAAA,CAAAW,IAAA,EAAAyB,OAAA,CAAA/C,YAAA,EAAA+C,OAAA,CAAAhB,eAAA;QACA,IAAAC,GAAA,GAAAoM,OAAA,CAAAnM,eAAA,CAAAtB,GAAA,CAAAW,IAAA,EAAAyB,OAAA,CAAA/C,YAAA,EAAA+C,OAAA,CAAAhB,eAAA;QACA,IAAAiB,GAAA;UACA,KAAArC,GAAA,CAAAkB,IAAA;YACAuM,OAAA,CAAAxN,IAAA,CAAAD,GAAA,EAAAkB,IAAA;YACAuM,OAAA,CAAAxN,IAAA,CAAAD,GAAA,EAAAqB,GAAA,EAAArB,GAAA,CAAAwC,cAAA;UACA;QACA;UACAiL,OAAA,CAAAE,OAAA,CAAA3N,GAAA,EAAAkB,IAAA;UACAuM,OAAA,CAAAE,OAAA,CAAA3N,GAAA,EAAAqB,GAAA;QACA;MACA;IACA;IACAuM,mBAAA,WAAAA,oBAAAC,UAAA,EAAAC,WAAA;MACA,KAAAD,UAAA;MACA,IAAAzZ,IAAA,IAAAyZ,UAAA,aAAAA,UAAA,uBAAAA,UAAA,CAAAzU,KAAA;MACA,SAAAhF,IAAA,CAAA2F,IAAA,WAAA3G,CAAA;QAAA,OAAAA,CAAA,KAAA0a,WAAA;MAAA;IACA;IAEAtW,cAAA,WAAAA,eAAA0J,IAAA;MAAA,IAAA6M,OAAA;MAAA,OAAAha,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA+Z,UAAA;QAAA,OAAAha,mBAAA,GAAAQ,IAAA,UAAAyZ,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAvZ,IAAA,GAAAuZ,UAAA,CAAAtZ,IAAA;YAAA;cAAAsZ,UAAA,CAAAtZ,IAAA;cAAA,OACA9I,aAAA;gBACAoV,IAAA,EAAAA;cACA,GAAAnI,IAAA,WAAAmC,GAAA;gBACA,IAAAC,SAAA,GAAAD,GAAA,CAAAC,SAAA;kBAAA5C,IAAA,GAAA2C,GAAA,CAAA3C,IAAA;kBAAA6C,OAAA,GAAAF,GAAA,CAAAE,OAAA;gBACA,IAAAD,SAAA;kBACA4S,OAAA,CAAA3e,QAAA,GAAA6P,MAAA,CAAAC,MAAA,KAAA6O,OAAA,CAAA3e,QAAA,EAAAmJ,IAAA,CAAA4V,IAAA;kBACA,IAAA/Z,IAAA,GAAAmE,IAAA,CAAA6V,UAAA;kBACAL,OAAA,CAAAvX,WAAA,GAAApC,IAAA,CAAAjB,IAAA,WAAAD,IAAA;oBAAA,OAAAA,IAAA,CAAA4B,IAAA;kBAAA;kBACAiZ,OAAA,CAAAM,YAAA,GAAAja,IAAA,CAAAjB,IAAA,WAAAD,IAAA;oBAAA,OAAAA,IAAA,CAAA4B,IAAA;kBAAA;kBACAiZ,OAAA,CAAA7e,OAAA,GAAA6e,OAAA,CAAAO,gBAAA,CAAAla,IAAA;gBACA;kBACA2Z,OAAA,CAAAtX,QAAA;oBACAC,OAAA,EAAA0E,OAAA;oBACAxH,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAsa,UAAA,CAAAjY,IAAA;UAAA;QAAA,GAAA+X,SAAA;MAAA;IACA;IACAM,gBAAA,WAAAA,iBAAAla,IAAA;MACA,OAAAA,IAAA,CAAAsD,MAAA,WAAAtE,CAAA;QAAA,OAAAA,CAAA,CAAAmb,UAAA;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAC,aAAA;MAAA,IAAA1O,GAAA,GAAAyO,KAAA,CAAAzO,GAAA;QAAA2O,MAAA,GAAAF,KAAA,CAAAE,MAAA;QAAAC,WAAA,GAAAH,KAAA,CAAAG,WAAA;MACA,SAAAlc,MAAA;MACA,IAAAob,WAAA,IAAAY,aAAA,GAAAC,MAAA,CAAAE,KAAA,cAAAH,aAAA,uBAAAA,aAAA,CAAAtV,KAAA;MACA,YAAAwU,mBAAA,CAAA5N,GAAA,CAAA5B,eAAA,EAAA0P,WAAA;IACA;IACAgB,aAAA,WAAAA,cAAAlb,IAAA,EAAAoM,GAAA;MAAA,IAAA+O,OAAA;MACA,SAAAtX,eAAA;QACA,IAAA7D,IAAA;UACA,IAAAob,QAAA,QAAAC,qBAAA;UACA,KAAAD,QAAA;QACA;MACA;MACA,KAAA7e,KAAA,GAAAyD,IAAA;MACA,KAAA3D,gBAAA;MACA,KAAAC,MAAA,QAAAuC,KAAA;MACA,KAAA7C,aAAA;MACA,KAAA2L,SAAA,WAAAC,CAAA;QACAuT,OAAA,CAAAtT,KAAA,YAAAyT,OAAA,CAAAtb,IAAA,UAAAoM,GAAA,IAAA+O,OAAA,CAAAzf,iBAAA,EAAAsE,IAAA,SAAAoM,GAAA,CAAA5B,eAAA;MACA;IACA;IACA6Q,qBAAA,WAAAA,sBAAA;MACA,IAAAE,QAAA;MACA,IAAAC,MAAA,QAAA9f,iBAAA,IAAA4O,aAAA;MACA,SAAAjC,CAAA,MAAAA,CAAA,QAAA3M,iBAAA,CAAAgJ,MAAA,EAAA2D,CAAA;QACA,IAAA/I,IAAA,QAAA5D,iBAAA,CAAA2M,CAAA;QACA,IAAA/I,IAAA,CAAAgL,aAAA,KAAAkR,MAAA;UACAD,QAAA;UACA;QACA;MACA;MACA,KAAAA,QAAA;QACA,KAAA1Y,QAAA;UACAC,OAAA;UACA9C,IAAA;QACA;MACA;MACA,OAAAub,QAAA;IACA;IACAE,gBAAA,WAAAA,iBAAAzb,IAAA,EAAAuF,GAAA;MACA,IAAAmW,WAAA;MACA,SAAArT,CAAA,MAAAA,CAAA,GAAA9C,GAAA,CAAAb,MAAA,EAAA2D,CAAA;QACA,IAAA/I,IAAA,GAAAiG,GAAA,CAAA8C,CAAA;QACA,KAAA/I,IAAA,CAAAgL,aAAA;UACAoR,WAAA;UACA;QACA;MACA;MACA,KAAAA,WAAA;QACA,KAAA7Y,QAAA;UACAC,OAAA;UACA9C,IAAA;QACA;MACA;MACA,OAAA0b,WAAA;IACA;IACAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MAAA,IAAA5b,IAAA,GAAA4M,SAAA,CAAAlI,MAAA,QAAAkI,SAAA,QAAAjQ,SAAA,GAAAiQ,SAAA;MACA,SAAA/N,KAAA;QACA,KAAAtC,KAAA,MAAAwJ,MAAA,MAAApM,OAAA;MACA;QACA,KAAA4C,KAAA;MACA;MACA,KAAAF,gBAAA;MACA,KAAAC,MAAA;MACA,KAAAL,YAAA;MACA,KAAA0L,SAAA,WAAAC,CAAA;QACAgU,OAAA,CAAA/T,KAAA,UAAAgU,WAAA;MACA;IACA;IACAtO,YAAA,WAAAA,aAAAR,IAAA,EAAAmN,WAAA,EAAA4B,SAAA;MACA,UAAA/V,MAAA,CAAAgH,IAAA,EAAAhH,MAAA,CAAAxM,YAAA,EAAAwM,MAAA,CAAAmU,WAAA,EAAAnU,MAAA,CAAAxM,YAAA,EAAAwM,MAAA,CAAA+V,SAAA;IACA;IACApO,eAAA,WAAAA,gBAAAX,IAAA,EAAAmN,WAAA,EAAA4B,SAAA;MACA,YAAAvO,YAAA,CAAAR,IAAA,EAAAmN,WAAA,EAAA4B,SAAA,OAAA/V,MAAA,CAAAxM,YAAA;IACA;IACAwiB,gBAAA,WAAAA,iBAAAvc,CAAA;MAAA,IAAAwc,OAAA;MAAA,OAAA7b,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA4b,UAAA;QAAA,OAAA7b,mBAAA,GAAAQ,IAAA,UAAAsb,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAApb,IAAA,GAAAob,UAAA,CAAAnb,IAAA;YAAA;cAAA,MACAxB,CAAA;gBAAA2c,UAAA,CAAAnb,IAAA;gBAAA;cAAA;cACAgb,OAAA,CAAAd,aAAA;cAAAiB,UAAA,CAAAnb,IAAA;cAAA;YAAA;cAAA,MACAxB,CAAA;gBAAA2c,UAAA,CAAAnb,IAAA;gBAAA;cAAA;cAAAmb,UAAA,CAAAnb,IAAA;cAAA,OACAgb,OAAA,CAAAnE,cAAA;YAAA;cAAAsE,UAAA,CAAAnb,IAAA;cAAA;YAAA;cAAA,MACAxB,CAAA;gBAAA2c,UAAA,CAAAnb,IAAA;gBAAA;cAAA;cAAAmb,UAAA,CAAAnb,IAAA;cAAA,OACAgb,OAAA,CAAAI,qBAAA;YAAA;YAAA;cAAA,OAAAD,UAAA,CAAA9Z,IAAA;UAAA;QAAA,GAAA4Z,SAAA;MAAA;IAEA;IACAG,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,OAAA;MAAA,OAAAlc,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAic,UAAA;QAAA,IAAAC,WAAA,EAAAC,OAAA,EAAAC,WAAA,EAAAC,SAAA,EAAAC,eAAA,EAAAC,YAAA,EAAAxL,SAAA;QAAA,OAAAhR,mBAAA,GAAAQ,IAAA,UAAAic,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA/b,IAAA,GAAA+b,UAAA,CAAA9b,IAAA;YAAA;cACAub,WAAA,YAAAA,YAAA;gBACAF,OAAA,CAAAxZ,QAAA;kBACAC,OAAA;kBACA9C,IAAA;gBACA;cACA;cACAwc,OAAA,GAAAH,OAAA,CAAA3gB,iBAAA,CAAA+L,GAAA,WAAAjI,CAAA;gBAAA,OAAAA,CAAA,CAAAwP,eAAA;cAAA,GAAAlL,MAAA,WAAAtE,CAAA;gBAAA,SAAAA,CAAA;cAAA;cAAA,IACAgd,OAAA,CAAA9X,MAAA;gBAAAoY,UAAA,CAAA9b,IAAA;gBAAA;cAAA;cACAqb,OAAA,CAAAxZ,QAAA;gBACAC,OAAA;gBACA9C,IAAA;cACA;cAAA,OAAA8c,UAAA,CAAAlR,MAAA;YAAA;cAAAkR,UAAA,CAAA9b,IAAA;cAAA,OAGAqb,OAAA,CAAA9N,iBAAA,CAAA8N,OAAA,CAAA3gB,iBAAA;YAAA;cACA+gB,WAAA,GAAAxN,KAAA,CAAAuD,IAAA,KAAAC,GAAA,CAAA4J,OAAA,CAAA3gB,iBAAA,CAAA+L,GAAA,WAAAjI,CAAA;gBAAA,OAAAA,CAAA,CAAA4K,WAAA;cAAA,GAAAtG,MAAA,WAAAtE,CAAA;gBAAA,SAAAA,CAAA;cAAA;cACAkd,SAAA;cACA,IAAAD,WAAA,CAAA/X,MAAA;gBACA+X,WAAA,CAAAlR,OAAA,WAAAkN,UAAA;kBACAiE,SAAA,CAAA3Z,IAAA,CAAAsZ,OAAA,CAAA7D,gBAAA,CAAAC,UAAA,EAAAtT,IAAA,WAAA6C,MAAA;oBAAA,OAAA+U,eAAA,KACAtE,UAAA,EAAAzQ,MAAA;kBAAA,CACA;gBACA;gBACA2U,eAAA,GAAA/S,OAAA,CAAAuO,GAAA,CAAAuE,SAAA,EAAAvX,IAAA,WAAA6X,MAAA;kBACA,OAAA3R,MAAA,CAAAC,MAAA,CAAA8H,KAAA,CAAA/H,MAAA,OAAAtF,MAAA,CAAAC,kBAAA,CAAAgX,MAAA;gBACA;gBACAL,eAAA,CAAAxX,IAAA,WAAA8X,QAAA;kBACA,IAAAC,IAAA;kBACA,IAAAN,YAAA;kBAAA,IAAAO,MAAA,YAAAA,OAAA,EACA;oBACA,IAAAC,MAAA,GAAAf,OAAA,CAAA3gB,iBAAA,CAAA2M,CAAA;oBACAlH,OAAA,CAAAC,GAAA,WAAA+Q,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAA+K,MAAA;oBACA,IAAAC,eAAA,GAAAJ,QAAA,CAAAG,MAAA,CAAAhT,WAAA;oBACAjJ,OAAA,CAAAC,GAAA,oBAAAic,eAAA;oBACA,IAAAC,UAAA,GAAAjB,OAAA,CAAA9a,YAAA,CAAA6b,MAAA,CAAApO,eAAA;oBACA7N,OAAA,CAAAC,GAAA,eAAAkc,UAAA;oBAEA,IAAAA,UAAA;sBACA,IAAAC,UAAA,GAAAD,UAAA,CAAA/a,KAAA,WAAAoW,OAAA;wBAAA,OAAA0E,eAAA,CAAA/N,QAAA,CAAAqJ,OAAA;sBAAA;sBACA,KAAA4E,UAAA;wBACAL,IAAA;wBAAA;sBAEA;sBACA,IAAAM,WAAA,GAAAnB,OAAA,CAAAoB,qBAAA,CAAAL,MAAA,EAAAE,UAAA;sBACA,IAAAE,WAAA;wBACAJ,MAAA,CAAA5S,eAAA,GAAA8S,UAAA,CAAA/N,IAAA;sBACA;wBACAqN,YAAA;sBACA;oBACA;kBACA;kBArBA,SAAAvU,CAAA,MAAAA,CAAA,GAAAgU,OAAA,CAAA3gB,iBAAA,CAAAgJ,MAAA,EAAA2D,CAAA;oBAAA,IAAA8U,MAAA,IAYA;kBAAA;kBAUA,KAAAD,IAAA;oBACA/I,UAAA;sBACAkI,OAAA,CAAAqB,MAAA;wBACAzY,iBAAA;sBACA;oBACA;kBACA;kBAEA,IAAAmM,SAAA,GAAAiL,OAAA,CAAA5M,uBAAA,CAAAmN,YAAA;kBACAM,IAAA,IAAA9L,SAAA,IAAAmL,WAAA;gBACA;cACA;gBACAK,YAAA;gBACAP,OAAA,CAAA3gB,iBAAA,CAAA6P,OAAA,WAAA6R,MAAA;kBACA,IAAAE,UAAA,GAAAjB,OAAA,CAAA9a,YAAA,CAAA6b,MAAA,CAAApO,eAAA;kBACA,IAAAsO,UAAA;oBACA,IAAAE,WAAA,GAAAnB,OAAA,CAAAoB,qBAAA,CAAAL,MAAA,EAAAE,UAAA;oBACA,IAAAE,WAAA;sBACAJ,MAAA,CAAA5S,eAAA,GAAA8S,UAAA,CAAA/N,IAAA;oBACA;sBACAqN,YAAA;oBACA;kBACA;gBACA;gBACAxL,SAAA,GAAAiL,OAAA,CAAA5M,uBAAA,CAAAmN,YAAA;gBACAxL,SAAA,IAAAmL,WAAA;cACA;YAAA;YAAA;cAAA,OAAAO,UAAA,CAAAza,IAAA;UAAA;QAAA,GAAAia,SAAA;MAAA;IACA;IACAmB,qBAAA,WAAAA,sBAAAL,MAAA,EAAAE,UAAA;MACA,KAAAF,MAAA,CAAAjT,iBAAA;MACA,IAAAwT,eAAA,GAAAP,MAAA,CAAAjT,iBAAA,CAAA3E,KAAA;MACA,IAAAwC,MAAA,GAAA2V,eAAA,CAAApb,KAAA,WAAAjD,IAAA;QAAA,OAAAge,UAAA,CAAAhO,QAAA,CAAAhQ,IAAA;MAAA;MACA,OAAA0I,MAAA;MACA;IACA;IACAyH,uBAAA,WAAAA,wBAAA+N,WAAA;MAAA,IAAAI,OAAA;MACA,IAAAJ,WAAA;MACArJ,UAAA;QACAyJ,OAAA,CAAAF,MAAA,gBAAA3X,MAAA,CAAA6X,OAAA,CAAAjkB,OAAA;UACAsL,iBAAA;QACA;MACA;MACA;IACA;IACA4Y,gBAAA,WAAAA,iBAAA7d,IAAA,EAAAoM,GAAA;MAAA,IAAA0R,OAAA;MACA,KAAAvhB,KAAA;MACA,KAAAF,gBAAA;MACA,KAAAC,MAAA;MACA,KAAAN,aAAA;MACA,KAAA2L,SAAA,WAAAC,CAAA;QACAkW,OAAA,CAAAjW,KAAA,YAAAkW,SAAA,CAAA/d,IAAA,QAAAA,IAAA,UAAAoM,GAAA,IAAA0R,OAAA,CAAApiB,iBAAA;MACA;IACA;IACAsiB,aAAA,WAAAA,cAAA;MACA,IAAAvP,GAAA;MACA,KAAAlT,MAAA,CAAAgQ,OAAA,WAAAiD,OAAA,EAAA/L,GAAA;QACA+L,OAAA,CAAAyP,OAAA,IAAAzP,OAAA,CAAAyP,OAAA;QACA,IAAAzP,OAAA,CAAAyP,OAAA;UACAxP,GAAA,CAAA1L,IAAA,CAAAyL,OAAA;QACA;MACA;MACA,KAAA9S,iBAAA,GAAA+S,GAAA;MACA,SAAA/S,iBAAA,CAAAgJ,MAAA,UAAAnJ,MAAA,CAAAmJ,MAAA;QACA,KAAAmD,KAAA,WAAAqW,iBAAA;MACA;MACA,SAAAxiB,iBAAA,CAAAgJ,MAAA;QACA,KAAAmD,KAAA,WAAAqW,iBAAA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA9b,OAAA,WAAAA,QAAA;MAAA,IAAA+b,OAAA;MACA,IAAAC,WAAA,YAAAA,YAAA;QACA,IAAAC,GAAA,GAAAF,OAAA,CAAAtf,KAAA,GAAA1F,eAAA,GAAAN,eAAA;QACAwlB,GAAA,KAAAlZ,IAAA,WAAAmC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACA,IAAAS,MAAA,GAAAV,GAAA,CAAA3C,IAAA;YACA,KAAAwZ,OAAA,CAAAtf,KAAA;cACAmJ,MAAA,GAAAA,MAAA,CACAP,GAAA,WAAAjI,CAAA,EAAAiD,GAAA;gBACA;kBACAkC,IAAA,EAAAnF,CAAA,CAAAE,IAAA;kBACA4e,KAAA,EAAA9e,CAAA,CAAAE;gBACA;cACA;YACA;YACAye,OAAA,CAAAxgB,uBAAA,CAAAlE,IAAA,GAAAuO,MAAA;YACAmW,OAAA,CAAAxW,SAAA,WAAAC,CAAA;cAAA,IAAA2W,qBAAA;cACA,CAAAA,qBAAA,GAAAJ,OAAA,CAAAtW,KAAA,CAAA2W,uBAAA,cAAAD,qBAAA,eAAAA,qBAAA,CAAAE,iBAAA,CAAAzW,MAAA;YACA;UACA;YACAmW,OAAA,CAAAtb,QAAA;cACAC,OAAA,EAAAwE,GAAA,CAAAE,OAAA;cACAxH,IAAA;YACA;UACA;QACA;MACA;MAEAoe,WAAA;IACA;IACA;IACAM,SAAA,WAAAA,UAAAtS,GAAA;MAAA,IAAAuS,OAAA;MACA,IAAA/M,GAAA;MACA,SAAA/S,KAAA;QACA+S,GAAA,CAAAgN,OAAA,GAAAxS,GAAA,CAAA5J,qBAAA;MACA;QACAoP,GAAA,CAAAiN,OAAA,GAAAzS,GAAA,CAAA2B,iBAAA;MACA;MACAzW,MAAA,CAAAsa,GAAA,EAAAzM,IAAA,WAAAmC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UAAA,IAAAuX,UAAA;UACA,IAAAC,OAAA,IAAAzX,GAAA,aAAAA,GAAA,gBAAAwX,UAAA,GAAAxX,GAAA,CAAA3C,IAAA,cAAAma,UAAA,uBAAAA,UAAA,CAAApa,MAAA,KAAA4C,GAAA,CAAA3C,IAAA,IAAAqa,QAAA;UACAC,MAAA,CAAAC,IAAA,0CAAA9lB,WAAA,CAAA2lB,OAAA;QACA;UACAJ,OAAA,CAAA9b,QAAA;YACAC,OAAA,EAAAwE,GAAA,CAAAE,OAAA;YACAxH,IAAA;UACA;QACA;MACA;IACA;IACAmf,cAAA,WAAAA,eAAA/G,IAAA;MACA,KAAA1M,iBAAA,CAAA0M,IAAA;IACA;IACAgH,cAAA,WAAAA,eAAA;MACA,KAAAvX,KAAA,eAAAwX,WAAA;MACA,KAAAxX,KAAA,CAAAyX,MAAA,CAAAC,WAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,OAAA;MACA,KAAA/jB,iBAAA;MACA,IAAA6J,GAAA;MACA,SAAA1G,KAAA;QACA0G,GAAA,CAAAxC,IAAA;MACA;QACAwC,GAAA,CAAAxC,IAAA;MACA;MAEA,IAAAuc,MAAA,QAAAzX,KAAA,CAAAyX,MAAA;MACAA,MAAA,CAAAI,gBAAA;MACAna,GAAA,CAAAgG,OAAA,WAAAiD,OAAA,EAAA/L,GAAA;QACA,IAAAsY,MAAA,GAAAuE,MAAA,CAAAK,gBAAA,CAAAnR,OAAA;QACA,IAAAA,OAAA;UACAuM,MAAA,CAAA6E,OAAA,CAAArU,OAAA,WAAAsU,MAAA,EAAApd,GAAA;YACAod,MAAA,CAAA5B,OAAA,GAAAxb,GAAA,MAAAgd,OAAA,CAAAnlB,SAAA,CAAAI,YAAA;UACA;QACA;QACA,IAAA8T,OAAA;UACA,IAAAqR,MAAA,GAAA9E,MAAA,CAAA6E,OAAA;UACAC,MAAA,CAAApmB,IAAA,GAAAgmB,OAAA,CAAAnlB,SAAA,CAAAG,gBAAA;UACAolB,MAAA,CAAA5B,OAAA;QACA;QACA,IAAAzP,OAAA,eAAAA,OAAA;UACA,IAAAqR,OAAA,GAAA9E,MAAA,CAAA6E,OAAA;UACAC,OAAA,CAAApmB,IAAA,GAAAgmB,OAAA,CAAAnlB,SAAA,CAAAE,mBAAA;UACAqlB,OAAA,CAAA5B,OAAA;QACA;QACA,IAAAzP,OAAA,oBAAAA,OAAA;UACA,IAAAqR,QAAA,GAAA9E,MAAA,CAAA6E,OAAA;UACAC,QAAA,CAAApmB,IAAA,GAAAgmB,OAAA,CAAAnlB,SAAA,CAAAC,aAAA;UACAslB,QAAA,CAAA5B,OAAA;QACA;MACA;MACAqB,MAAA,CAAAQ,UAAA;IACA;IACAC,qBAAA,WAAAA,sBAAAC,KAAA;MAAA,IAAAH,MAAA,GAAAG,KAAA,CAAAH,MAAA;QAAAzT,GAAA,GAAA4T,KAAA,CAAA5T,GAAA;MACA,SAAA9R,SAAA,CAAAI,YAAA;QACA;MACA;MACA,OAAA0R,GAAA,CAAA6T,YAAA,WAAA3lB,SAAA,CAAAI,YAAA;IACA;IACAwlB,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAN,MAAA,GAAAM,KAAA,CAAAN,MAAA;QAAAzT,GAAA,GAAA+T,KAAA,CAAA/T,GAAA;MACA,SAAA9R,SAAA,CAAAG,gBAAA,CAAA0V,IAAA;QACA;MACA;MACA,IAAAiQ,aAAA,YAAAA,cAAAC,KAAA;QAAA,OAAAA,KAAA,CAAAlQ,IAAA,GAAA6I,OAAA,cAAAxT,KAAA;MAAA;MACA,IAAA8a,SAAA,GAAAF,aAAA,MAAA9lB,SAAA,CAAAG,gBAAA;MACA,OAAA6lB,SAAA,CAAAna,IAAA,WAAAmH,IAAA;QAAA,QAAAlB,GAAA,CAAAmU,IAAA,QAAAjR,QAAA,CAAAhC,IAAA;MAAA;IACA;IAEAkT,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAZ,MAAA,GAAAY,KAAA,CAAAZ,MAAA;QAAAzT,GAAA,GAAAqU,KAAA,CAAArU,GAAA;MACA,SAAA9R,SAAA,CAAAE,mBAAA;QACA;MACA;MACA,IAAAiU,GAAA,QAAA5P,KAAA;MACA,OAAAuN,GAAA,CAAAqC,GAAA,WAAAnU,SAAA,CAAAE,mBAAA;IACA;IACAkmB,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAd,MAAA,GAAAc,KAAA,CAAAd,MAAA;QAAAzT,GAAA,GAAAuU,KAAA,CAAAvU,GAAA;MACA,SAAA9R,SAAA,CAAAC,aAAA,CAAA4V,IAAA;QACA;MACA;MAEA,IAAAiQ,aAAA,YAAAA,cAAAC,KAAA;QAAA,OAAAA,KAAA,CAAAlQ,IAAA,GAAA6I,OAAA,cAAAxT,KAAA;MAAA;MAEA,IAAAiJ,GAAA,QAAA5P,KAAA;MAEA,IAAA0G,GAAA,GAAA6a,aAAA,MAAA9lB,SAAA,CAAAC,aAAA;MAEA,SAAAI,SAAA;QACA,OAAA4K,GAAA,CAAAY,IAAA,WAAAmH,IAAA;UAAA,OAAAlB,GAAA,CAAAqC,GAAA,MAAAnB,IAAA;QAAA;MACA;QACA,SAAAjF,CAAA,MAAAA,CAAA,GAAA9C,GAAA,CAAAb,MAAA,EAAA2D,CAAA;UACA,IAAA/I,IAAA,GAAAiG,GAAA,CAAA8C,CAAA;UACA,IAAA+D,GAAA,CAAAqC,GAAA,EAAAa,QAAA,CAAAhQ,IAAA;YACA;UACA;QACA;QACA;MACA;IACA;IACAshB,mBAAA,WAAAA,oBAAA1Y,CAAA;MAAA,IAAA2Y,WAAA;MACA,CAAAA,WAAA,QAAAhZ,KAAA,cAAAgZ,WAAA,eAAAA,WAAA,CAAArC,uBAAA,CAAAsC,SAAA,CAAA5Y,CAAA;IACA;IACAhG,wBAAA,WAAAA,yBAAAgH,EAAA;MAAA,IAAA6X,OAAA;MACA,UAAA1jB,MAAA,SAAA0G,aAAA;QACA,KAAA5G,iBAAA;QACA,KAAAiB,WAAA;MACA;QACA,KAAAA,WAAA;QACAnF,wBAAA;UAAA+Y,OAAA,OAAA3U;QAAA,GAAA8H,IAAA,WAAAmC,GAAA;UACAyZ,OAAA,CAAA5jB,iBAAA,GAAAmK,GAAA,CAAA3C,IAAA;UACA,IAAAoc,OAAA,CAAA5jB,iBAAA,CAAAuH,MAAA;YACAqc,OAAA,CAAAlmB,UAAA,CAAAI,cAAA,GAAA8lB,OAAA,CAAA5jB,iBAAA,IAAAsC,EAAA;UACA;UACAshB,OAAA,CAAA3iB,WAAA;QACA;MACA;IACA;IACA4iB,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MACA,UAAA1lB,MAAA,CAAAmJ,MAAA;QACA,KAAAmD,KAAA,eAAAwX,WAAA;QACA,KAAAxX,KAAA,CAAAyX,MAAA,CAAAC,WAAA;QACA;MACA;MACA,KAAAva,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAlF,IAAA;MACA,GAAAmF,IAAA;QACA8b,OAAA,CAAA1lB,MAAA;QACA0lB,OAAA,CAAA7B,cAAA;MACA,GAAAha,KAAA;QACA6b,OAAA,CAAApe,QAAA;UACA7C,IAAA;UACA8C,OAAA;QACA;MACA;IACA;IACAoe,mBAAA,WAAAA,oBAAA9U,GAAA;MACA,SAAAvM,MAAA;QACA,SAAAuM,GAAA,CAAA5J,qBAAA;MACA;QACA,aAAA1D,MAAA,IAAAsN,GAAA,CAAA/E,IAAA;MACA;IACA;IACA8Z,YAAA,WAAAA,aAAA;MACA,UAAA5lB,MAAA,CAAAmJ,MAAA;QACA,KAAA7B,QAAA;UACAC,OAAA;UACA9C,IAAA;QACA;QACA;MACA;MACAmB,OAAA,CAAAC,GAAA,SAAAyG,KAAA,CAAAyX,MAAA;MACA,IAAAhgB,IAAA,QAAA/D,MAAA;MACA,KAAAsM,KAAA,CAAAyX,MAAA,CAAA8B,UAAA;QACAC,QAAA,KAAAtb,MAAA,MAAApM,OAAA,mBAAAoM,MAAA,CAAAzG,IAAA,CAAAgiB,YAAA,OAAAvb,MAAA,CAAAzG,IAAA,CAAAiiB,SAAA,OAAAxb,MAAA,MAAAlL,UAAA,CAAAC,cAAA,OAAAiL,MAAA,MAAApM,OAAA;QACAqG,IAAA;QACAvG,IAAA,OAAA8B;MACA;IACA;EAAA;AAEA", "ignoreList": []}]}
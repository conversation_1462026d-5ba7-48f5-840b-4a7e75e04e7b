{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-unit-part\\components\\addDraft.vue?vue&type=template&id=cbc5f106&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-unit-part\\components\\addDraft.vue", "mtime": 1758266753117}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\actually-sent\\v4\\component\\Add.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\actually-sent\\v4\\component\\Add.vue", "mtime": 1757468128055}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgVGl0bGVJbmZvIGZyb20gJy4vVGl0bGVJbmZvLnZ1ZScNCmltcG9ydCBBZGREaWFsb2cgZnJvbSAnLi9BZGREaWFsb2cudnVlJw0KaW1wb3J0IHBhY2tEZXRhaWwgZnJvbSAnLi9wYWNrRGV0YWlsLnZ1ZScNCmltcG9ydCB7DQogIEFkZFByb2plY3RTZW5kaW5nSW5mbywNCiAgR2V0UHJvamVjdHNlbmRpbmdpbkVudGl0eSwNCiAgRWRpdFByb2plY3RTZW5kaW5nSW5mbw0KfSBmcm9tICdAL2FwaS9QUk8vY29tcG9uZW50LXN0b2NrLW91dCcNCmltcG9ydCB7IEdldEZpcnN0TGV2ZWxEZXBhcnRzVW5kZXJGYWN0b3J5IH0gZnJvbSAnQC9hcGkvUFJPL21hdGVyaWFsLXdhcmVob3VzZS9tYXRlcmlhbC1pbnZlbnRvcnktcmVjb25maWcuanMnDQppbXBvcnQgeyBHZXRQcmVmZXJlbmNlU2V0dGluZ1ZhbHVlIH0gZnJvbSAnQC9hcGkvc3lzL3N5c3RlbS1zZXR0aW5nJw0KaW1wb3J0IER5bmFtaWNEYXRhVGFibGUgZnJvbSAnQC9jb21wb25lbnRzL0R5bmFtaWNEYXRhVGFibGUvRHluYW1pY0RhdGFUYWJsZS52dWUnDQppbXBvcnQgQ2FyRGlhbG9nIGZyb20gJy4vQ2FyRGlhbG9nLnZ1ZScNCmltcG9ydCB7IEdldEN1ckNhclBhZ2VMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL2NhcicNCmltcG9ydCBUb3BIZWFkZXIgZnJvbSAnQC9jb21wb25lbnRzL1RvcEhlYWRlci9pbmRleC52dWUnDQppbXBvcnQgQ2hlY2tJbmZvIGZyb20gJ0Avdmlld3MvUFJPL0NvbXBvbmVudC9HZXRQYWNraW5nRGV0YWlsL2luZGV4LnZ1ZScNCmltcG9ydCB7IGNsb3NlVGFnVmlldywgcGFyc2VUaW1lIH0gZnJvbSAnQC91dGlscycNCmltcG9ydCB7IEdldFByb2plY3RQYWdlTGlzdCwgR2V0UHJvamVjdEVudGl0eSB9IGZyb20gJ0AvYXBpL1BSTy9wcm8tc2NoZWR1bGVzJw0KaW1wb3J0IHsgR2V0R3JpZEJ5Q29kZSwgR2V0T3NzVXJsIH0gZnJvbSAnQC9hcGkvc3lzJw0KaW1wb3J0IHsgR2V0RmFjdG9yeVByb2Zlc3Npb25hbEJ5Q29kZSB9IGZyb20gJ0AvYXBpL1BSTy9wcm9mZXNzaW9uYWxUeXBlJw0KaW1wb3J0IHsgR2V0Q29tcGFueURlcGFydFRyZWUgfSBmcm9tICdAL2FwaS9zeXMnDQppbXBvcnQgbnVtZXJhbCBmcm9tICdudW1lcmFsJw0KaW1wb3J0IE9TU1VwbG9hZCBmcm9tICdAL3ZpZXdzL1BSTy9jb21wb25lbnRzL29zc3VwbG9hZC52dWUnDQppbXBvcnQgeyBnZXRGaWxlTmFtZUZyb21VcmwgfSBmcm9tICdAL3V0aWxzL2ZpbGUnDQppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCcNCmltcG9ydCB7IEdldENvbnRyYWN0TGlzdCB9IGZyb20gJ0AvYXBpL3BsbS9wcm9kdWN0aW9uJw0KaW1wb3J0IHsgR2V0U3RvcExpc3QgfSBmcm9tICdAL2FwaS9QUk8vcHJvZHVjdGlvbi10YXNrJw0KaW1wb3J0IFNlbGVjdERlcGFydG1lbnRVc2VyIGZyb20gJ0AvY29tcG9uZW50cy9TZWxlY3QvU2VsZWN0RGVwYXJ0bWVudFVzZXIvaW5kZXgudnVlJw0KaW1wb3J0IFNlbGVjdERlcGFydG1lbnQgZnJvbSAnQC9jb21wb25lbnRzL1NlbGVjdC9TZWxlY3REZXBhcnRtZW50L2luZGV4LnZ1ZScNCg0KY29uc3QgVEFCX1RZUEUgPSB7DQogIGNvbTogMSwNCiAgcGFja2FnZTogMiwNCiAgdW5pdFBhcnQ6IDMsDQogIHBhcnQ6IDQNCn0NCmV4cG9ydCBkZWZhdWx0IHsNCiAgY29tcG9uZW50czogew0KICAgIFNlbGVjdERlcGFydG1lbnQsIFNlbGVjdERlcGFydG1lbnRVc2VyLA0KICAgIE9TU1VwbG9hZCwNCiAgICBUaXRsZUluZm8sDQogICAgQWRkRGlhbG9nLA0KICAgIFRvcEhlYWRlciwNCiAgICBEeW5hbWljRGF0YVRhYmxlLA0KICAgIENhckRpYWxvZywNCiAgICBDaGVja0luZm8sDQogICAgcGFja0RldGFpbA0KICB9LA0KICBwcm9wczogew0KICAgIGlzRWRpdDogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBwYWdlU3RhdHVzOiB1bmRlZmluZWQsDQogICAgICByYWRpbzogJ3Byb19jb21wb25lbnRfb3V0X2RldGFpbF9saXN0JywNCiAgICAgIGFkZHJhZGlvOiAncHJvX3dhaXRpbmdfb3V0X2xpc3QnLA0KICAgICAgSXNfUGFjazogZmFsc2UsDQogICAgICBpc0NsaWNrZWQ6IGZhbHNlLA0KICAgICAgaXNTdWI6IGZhbHNlLCAvLyDmmK/lkKbmj5DkuqTov4cNCiAgICAgIHdpZHRoOiAnNDAlJywNCiAgICAgIHRvcERpYWxvZzogJzF2aCcsDQogICAgICBidG5Mb2FkaW5nOiBmYWxzZSwNCiAgICAgIGN1cnJlbnRDb21wb25lbnQ6ICcnLA0KICAgICAgdGl0bGU6ICcnLA0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHNlbmROdW1iZXI6ICcnLA0KICAgICAgZm9ybTogew0KICAgICAgICBQcm9qZWN0SWQ6ICcnLA0KICAgICAgICBPdXRfRGF0ZTogbmV3IERhdGUoKSwNCiAgICAgICAgUmVtYXJrczogJycsDQogICAgICAgIENvbnRhY3RfVXNlck5hbWU6ICcnLA0KICAgICAgICBNb2JpbGU6ICcnLA0KICAgICAgICBMaWNlbnNlOiAnJywNCiAgICAgICAgQWRkcmVzczogJycsDQogICAgICAgIHJlY2VpdmVOYW1lOiAnJywNCiAgICAgICAgaXNzdWVOYW1lOiAnJywNCiAgICAgICAgUmVjZWl2ZXJfVGVsOiAnJywNCiAgICAgICAgQXJlYV9JZDogJycsDQogICAgICAgIHByb2plY3ROYW1lOiAnJywNCiAgICAgICAgcmVjZWl2ZU51bTogJycsDQogICAgICAgIFByb2Zlc3Npb25hbFR5cGVOYW1lOiAnJywNCiAgICAgICAgRGVwYXJ0X0lkOiAnJywNCiAgICAgICAgTG9naXN0aWNzX0ZlZTogdW5kZWZpbmVkLA0KICAgICAgICBDb250cmFjdF9JZDogJycsDQogICAgICAgIFBhZ2VJbmZvOiB7DQogICAgICAgICAgUGFyYW1ldGVySnNvbjogW10sDQogICAgICAgICAgUGFnZTogMSwNCiAgICAgICAgICBQYWdlU2l6ZTogMjANCiAgICAgICAgfSwNCiAgICAgICAgTG9hZGluZ3M6ICcnLA0KICAgICAgICBMb2FkaW5nc1BlcnNvbm5lbDogJycsDQogICAgICAgIFRyaXBzOiAnJywNCiAgICAgICAgUmVjZWl2aW5nVW5pdDogJycNCiAgICAgIH0sDQogICAgICAvLyDlj5HotKfpg6jpl6gNCiAgICAgIHRyZWVQYXJhbXNEZXBhcnQ6IHsNCiAgICAgICAgJ2RlZmF1bHQtZXhwYW5kLWFsbCc6IHRydWUsDQogICAgICAgIGZpbHRlcmFibGU6IGZhbHNlLA0KICAgICAgICBjbGlja1BhcmVudDogdHJ1ZSwNCiAgICAgICAgZGF0YTogW10sDQogICAgICAgIHByb3BzOiB7DQogICAgICAgICAgZGlzYWJsZWQ6ICdkaXNhYmxlZCcsDQogICAgICAgICAgY2hpbGRyZW46ICdDaGlsZHJlbicsDQogICAgICAgICAgbGFiZWw6ICdMYWJlbCcsDQogICAgICAgICAgdmFsdWU6ICdJZCcNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIHBpY2tEZXBhcnRtZW50TGlzdDogW10sIC8vIOWTgemHjeWPkei0p+mDqOmXqA0KICAgICAgcGxtX1Byb2plY3RTZW5kaW5nSW5mbzoge30sDQogICAgICBwcm9kdWNlZF9Db21wb25lbnRzOiBbXSwNCiAgICAgIHdlaWdodEZpbGVJbmZvOiBbXSwNCiAgICAgIHByb2plY3RTZW5kaW5nSW5mb19JdGVtOiBbXSwNCiAgICAgIEl0ZW1kZXRhaWw6IFtdLA0KICAgICAgUGFja2FnZXNMaXN0OiBbXSwNCiAgICAgIFByb2Zlc3Npb25hbFR5cGU6IFtdLA0KICAgICAgZmlsZUxpc3RBcnI6IFtdLA0KICAgICAgY2FyT3B0aW9uczogW10sDQogICAgICBwcm9qZWN0czogJycsDQogICAgICBJZDogJycsDQogICAgICBwcm9qZWN0SWQ6ICcnLA0KICAgICAgcGxhblRpbWU6ICcnLA0KICAgICAgc2hvd0RpYWxvZzogZmFsc2UsDQogICAgICB0YkNvbmZpZzogew0KICAgICAgICBQYWdlcl9BbGlnbjogJ2NlbnRlcicNCiAgICAgIH0sDQogICAgICBjb2x1bW5zOiBbXSwNCiAgICAgIHRiRGF0YTogW10sDQogICAgICB0YkRhdGEyOiBbXSwNCiAgICAgIHRiRGF0YTM6IFtdLA0KICAgICAgdGJEYXRhNDogW10sDQogICAgICB0b3RhbDogMCwNCiAgICAgIHRiTG9hZGluZzogZmFsc2UsDQogICAgICBhdXRvR2VuZXJhdGU6IHRydWUsDQogICAgICBzZWxlY3RMaXN0OiBbXSwNCiAgICAgIGZpbGVMaXN0RGF0YTogW10sDQogICAgICBzdW1zOiBbXSwNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIE91dF9EYXRlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeWPkei0p+aXtumXtCcsIHRyaWdnZXI6ICdjaGFuZ2UnIH0NCiAgICAgICAgXSwNCiAgICAgICAgcmVjZWl2ZU51bTogW3sgcmVxdWlyZWQ6IGZhbHNlLCBtZXNzYWdlOiAn6K+36L6T5YWl5Y+R6LSn5Y2V5Y+3JywgdHJpZ2dlcjogJ2JsdXInIH1dLA0KICAgICAgICAvLyByZWNlaXZlTmFtZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaUnLCB0cmlnZ2VyOiAnYmx1cicgfV0sDQogICAgICAgIC8vIFJlY2VpdmVyX1RlbDogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaUnLCB0cmlnZ2VyOiAnYmx1cicgfV0sDQogICAgICAgIERlcGFydF9JZDogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6knLCB0cmlnZ2VyOiAnY2hhbmdlJyB9XSwNCiAgICAgICAgaXNzdWVOYW1lOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpScsIHRyaWdnZXI6ICdibHVyJyB9XQ0KICAgICAgfSwNCiAgICAgIG9sZF9Db21wb25lbnRfSWRzOiBbXSwNCiAgICAgIHdlaWdodGZvcm06IHsNCiAgICAgICAgVGFyZV9XZWlnaHQ6IDAsIC8vIOearumHjQ0KICAgICAgICBSZWFzb25fV2VpZ2h0OiAwLCAvLyDnkIbph40NCiAgICAgICAgUG91bmRfV2VpZ2h0OiAwLCAvLyDno4Xph40NCiAgICAgICAgTmV0X1dlaWdodDogMCwgLy8g5YeA6YeNDQogICAgICAgIFdlaWdoX1dhcm5pbmdfVGhyZXNob2xkOiAwDQogICAgICB9LA0KICAgICAgaXNQcm9kdWN0d2VpZ2h0OiBudWxsLA0KICAgICAgdGFiVHlwZUNvZGU6IDEsDQogICAgICBpc0RyYWZ0OiBmYWxzZSwNCiAgICAgIGNvbnRyYWN0T3B0aW9uczogW10NCiAgICB9DQogIH0sDQogIHByb3ZpZGUoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGlzVmVyc2lvbkZvdXI6IHRoaXMuaXNWZXJzaW9uRm91cg0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICBuZXRXZWlnaHQoKSB7DQogICAgICBpZiAoIXRoaXMud2VpZ2h0Zm9ybS5Qb3VuZF9XZWlnaHQgfHwgIXRoaXMud2VpZ2h0Zm9ybS5UYXJlX1dlaWdodCkgcmV0dXJuIDANCiAgICAgIHJldHVybiB0aGlzLndlaWdodGZvcm0uUG91bmRfV2VpZ2h0IC0gdGhpcy53ZWlnaHRmb3JtLlRhcmVfV2VpZ2h0DQogICAgfSwNCiAgICBzaG93UmVkKHsgbmV0V2VpZ2h0IH0pIHsNCiAgICAgIHJldHVybiBNYXRoLmFicyhuZXRXZWlnaHQgLSB0aGlzLndlaWdodGZvcm0uUmVhc29uX1dlaWdodCkgPj0gdGhpcy53ZWlnaHRmb3JtLldlaWdoX1dhcm5pbmdfVGhyZXNob2xkDQogICAgfSwNCiAgICAuLi5tYXBHZXR0ZXJzKCd0ZW5hbnQnLCBbJ2lzVmVyc2lvbkZvdXInXSkNCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICAndGJEYXRhLmxlbmd0aCc6IHsNCiAgICAgIGhhbmRsZXIoKSB7DQogICAgICAgIHRoaXMuZ2V0VG90YWwoKQ0KICAgICAgfQ0KICAgIH0NCg0KICB9LA0KICBhc3luYyBjcmVhdGVkKCkgew0KICAgIHRoaXMudGFiVHlwZUNvZGUgPSBUQUJfVFlQRS5jb20NCiAgICB0aGlzLmlzU3ViID0gdGhpcy4kcm91dGUucXVlcnkuaXNTdWIgPT09ICcxJyB8fCBmYWxzZQ0KICAgIGF3YWl0IHRoaXMuZ2V0U2V0dGluZ1Byb2R1Y3R3ZWlnaHQoKQ0KICAgIHRoaXMuZ2V0RmFjdG9yeURlcGFydG1lbnREYXRhKCkNCiAgICB0aGlzLmdldERlcGFydG1lbnRUcmVlKCkNCiAgICB0aGlzLmdldEZhY3RvcnlUeXBlT3B0aW9uKCkNCiAgICB0aGlzLmdldEFsbENhckxpc3QoKQ0KICB9LA0KICBhc3luYyBtb3VudGVkKCkgew0KICAgIGlmICh0aGlzLmlzRWRpdCkgew0KICAgICAgY29uc3Qgew0KICAgICAgICBhdXRvR2VuZXJhdGUNCiAgICAgIH0gPSBKU09OLnBhcnNlKGRlY29kZVVSSUNvbXBvbmVudCh0aGlzLiRyb3V0ZS5xdWVyeS5wKSkNCiAgICAgIHRoaXMuYXV0b0dlbmVyYXRlID0gYXV0b0dlbmVyYXRlDQogICAgICBhd2FpdCB0aGlzLmdldEluZm8oKQ0KICAgIH0gZWxzZSB7DQogICAgICBjb25zdCB7DQogICAgICAgIE5hbWUsDQogICAgICAgIElkLA0KICAgICAgICBDb2RlLA0KICAgICAgICBBZGRyZXNzLA0KICAgICAgICBhdXRvR2VuZXJhdGUsDQogICAgICAgIFN5c19Qcm9qZWN0X0lkDQogICAgICB9ID0gSlNPTi5wYXJzZShkZWNvZGVVUklDb21wb25lbnQodGhpcy4kcm91dGUucXVlcnkucCkpDQogICAgICAvLyB0aGlzLnByb2plY3ROYW1lID0gTmFtZTsNCiAgICAgIHRoaXMuYXV0b0dlbmVyYXRlID0gYXV0b0dlbmVyYXRlDQogICAgICB0aGlzLnByb2plY3RJZCA9IElkDQogICAgICB0aGlzLlByb2plY3RfQ29kZSA9IENvZGUNCiAgICAgIHRoaXMuZm9ybS5wcm9qZWN0TmFtZSA9IE5hbWUNCiAgICAgIHRoaXMuZm9ybS5BZGRyZXNzID0gQWRkcmVzcw0KICAgICAgdGhpcy5mb3JtLlByb2plY3RJZCA9IFN5c19Qcm9qZWN0X0lkDQogICAgICB0aGlzLmZvcm0uaXNzdWVOYW1lID0gdGhpcy4kc3RvcmUuc3RhdGUudXNlci5uYW1lDQogICAgICB0aGlzLmdldFByb2plY3RFbnRpdHkodGhpcy5wcm9qZWN0SWQpDQogICAgICB0aGlzLnJ1bGVzLnJlY2VpdmVOdW1bMF0ucmVxdWlyZWQgPSAhYXV0b0dlbmVyYXRlDQogICAgfQ0KICAgIHRoaXMuZ2V0Q29udHJhY3RMaXN0KCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGFzeW5jIGdldFNldHRpbmdQcm9kdWN0d2VpZ2h0KCkgew0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0UHJlZmVyZW5jZVNldHRpbmdWYWx1ZSh7IGNvZGU6ICdQcm9kdWN0d2VpZ2h0JyB9KQ0KICAgICAgaWYgKHJlcy5EYXRhID09PSAndHJ1ZScpIHsNCiAgICAgICAgdGhpcy5pc1Byb2R1Y3R3ZWlnaHQgPSB0cnVlDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOiOt+WPluW3peWOguaVsOaNrg0KICAgIGdldEZhY3RvcnlEZXBhcnRtZW50RGF0YSgpIHsNCiAgICAgIEdldEZpcnN0TGV2ZWxEZXBhcnRzVW5kZXJGYWN0b3J5KHsgRmFjdG9yeUlkOiBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnQ3VyUmVmZXJlbmNlSWQnKSB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy5waWNrRGVwYXJ0bWVudExpc3QgPSByZXMuRGF0YQ0KICAgICAgICAgIGNvbnN0IGRlcElkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ0RlcGFydG1lbnRJZCcpDQogICAgICAgICAgY29uc3QgY3VyID0gdGhpcy5waWNrRGVwYXJ0bWVudExpc3QuZmluZCgoaXRlbSkgPT4gaXRlbS5JZCA9PT0gZGVwSWQpDQogICAgICAgICAgaWYgKGN1cikgew0KICAgICAgICAgICAgdGhpcy5mb3JtLkRlcGFydF9JZCA9IGN1ci5JZA0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIGdldERlcGFydG1lbnRUcmVlKCkgew0KICAgICAgR2V0Q29tcGFueURlcGFydFRyZWUoeyBpc0FsbDogdHJ1ZSB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICBjb25zdCB0cmVlID0gcmVzLkRhdGENCiAgICAgICAgICB0aGlzLnNldERpc2FibGVkVHJlZSh0cmVlKQ0KICAgICAgICAgIHRoaXMudHJlZVBhcmFtc0RlcGFydC5kYXRhID0gdHJlZQ0KICAgICAgICAgIHRoaXMuJG5leHRUaWNrKF8gPT4gew0KICAgICAgICAgICAgdGhpcy4kcmVmcy50cmVlU2VsZWN0RGVwYXJ0Py50cmVlRGF0YVVwZGF0ZUZ1bih0cmVlKQ0KICAgICAgICAgICAgY29uc3QgYXJyID0gdGhpcy5nZXRGbGF0dGVuZWRTZWxlY3RhYmxlSXRlbXModHJlZSkNCiAgICAgICAgICB9KQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGdldEZsYXR0ZW5lZFNlbGVjdGFibGVJdGVtcyhyb290KSB7DQogICAgICBpZiAoIXJvb3QgfHwgIXJvb3QubGVuZ3RoKSByZXR1cm4gW10NCiAgICAgIGNvbnN0IHJlc3VsdCA9IFtdDQogICAgICBjb25zdCBmbGF0dGVuID0gKGl0ZW1zKSA9PiB7DQogICAgICAgIGlmICghaXRlbXMgfHwgIWl0ZW1zLmxlbmd0aCkgcmV0dXJuDQogICAgICAgIGl0ZW1zLmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICBjb25zdCB7IENoaWxkcmVuIH0gPSBpdGVtDQogICAgICAgICAgaWYgKGl0ZW0uRGF0YT8uSXNfQ29tcGFueSAhPT0gdHJ1ZSAmJiBpdGVtLkRhdGE/LlR5cGUgIT09ICcxJykgew0KICAgICAgICAgICAgcmVzdWx0LnB1c2goaXRlbSkNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKENoaWxkcmVuICYmIENoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIGZsYXR0ZW4oQ2hpbGRyZW4pDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgICAgZmxhdHRlbihyb290KQ0KICAgICAgY29uc3QgY3VyID0gcmVzdWx0LmZpbmQoKGl0ZW0pID0+IGl0ZW0uSWQgPT09IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdEZXBhcnRtZW50SWQnKSkNCiAgICAgIGlmIChjdXIgJiYgIXRoaXMuaXNFZGl0KSB7DQogICAgICAgIHRoaXMuZm9ybS5EZXBhcnRfSWQgPSBjdXIuSWQNCiAgICAgIH0NCiAgICB9LA0KICAgIHNldERpc2FibGVkVHJlZShyb290KSB7DQogICAgICBpZiAoIXJvb3QpIHJldHVybg0KICAgICAgcm9vdC5mb3JFYWNoKChlbGVtZW50KSA9PiB7DQogICAgICAgIGNvbnN0IHsgQ2hpbGRyZW4gfSA9IGVsZW1lbnQNCiAgICAgICAgaWYgKGVsZW1lbnQuRGF0YS5Jc19Db21wYW55ID09PSB0cnVlIHx8IGVsZW1lbnQuRGF0YS5UeXBlID09PSAnMScpIHsNCiAgICAgICAgICBlbGVtZW50LmRpc2FibGVkID0gdHJ1ZQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGVsZW1lbnQuZGlzYWJsZWQgPSBmYWxzZQ0KICAgICAgICB9DQogICAgICAgIGlmIChDaGlsZHJlbi5sZW5ndGggPiAwKSB7DQogICAgICAgICAgdGhpcy5zZXREaXNhYmxlZFRyZWUoQ2hpbGRyZW4pDQogICAgICAgIH0NCiAgICAgICAgLy8gaWYgKENoaWxkcmVuICYmIENoaWxkcmVuLmxlbmd0aCkgew0KICAgICAgICAvLyAgIGVsZW1lbnQuZGlzYWJsZWQgPSB0cnVlDQogICAgICAgIC8vIH0gZWxzZSB7DQogICAgICAgIC8vICAgZWxlbWVudC5kaXNhYmxlZCA9IGZhbHNlDQogICAgICAgIC8vICAgdGhpcy5zZXREaXNhYmxlZFRyZWUoQ2hpbGRyZW4pDQogICAgICAgIC8vIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBkZXBhcnRDbGVhcigpIHsNCg0KICAgIH0sDQogICAgZGVwYXJ0Q2hhbmdlKCkgew0KDQogICAgfSwNCiAgICBnZXROdW0oYSwgYikgew0KICAgICAgcmV0dXJuIG51bWVyYWwoYSkuc3VidHJhY3QoYikuZm9ybWF0KCcwLlswMDBdJykNCiAgICB9LA0KICAgIGFzeW5jIGdldEZhY3RvcnlUeXBlT3B0aW9uKCkgew0KICAgICAgYXdhaXQgR2V0RmFjdG9yeVByb2Zlc3Npb25hbEJ5Q29kZSh7DQogICAgICAgIGZhY3RvcnlJZDogbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ0N1clJlZmVyZW5jZUlkJykNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMuUHJvZmVzc2lvbmFsVHlwZSA9IHJlcy5EYXRhDQogICAgICAgICAgdGhpcy5mb3JtLlByb2Zlc3Npb25hbFR5cGVOYW1lID0gdGhpcy5Qcm9mZXNzaW9uYWxUeXBlWzBdLk5hbWUNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICBhd2FpdCB0aGlzLmdldFRhYmxlQ29uZmlnKA0KICAgICAgICBgcHJvX2NvbXBvbmVudF9vdXRfZGV0YWlsX2xpc3QsJHt0aGlzLlByb2Zlc3Npb25hbFR5cGVbMF0uQ29kZX1gDQogICAgICApDQogICAgfSwNCiAgICBnZXRQcm9qZWN0RW50aXR5KElkKSB7DQogICAgICBHZXRQcm9qZWN0RW50aXR5KHsgSWQgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgY29uc3QgQ29uc2lnbmVlID0gcmVzLkRhdGEuQ29udGFjdHMuZmluZCgoaXRlbSkgPT4gew0KICAgICAgICAgICAgcmV0dXJuIGl0ZW0uVHlwZSA9PSAnQ29uc2lnbmVlJw0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy5mb3JtLnJlY2VpdmVOYW1lID0gQ29uc2lnbmVlPy5OYW1lIHx8ICcnDQogICAgICAgICAgdGhpcy5mb3JtLlJlY2VpdmVyX1RlbCA9IENvbnNpZ25lZT8uVGVsIHx8ICcnDQogICAgICAgICAgdGhpcy5mb3JtLlRyaXBzID0gcmVzLkRhdGEuVHJpcHMgfHwgJycNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGdldFByb2plY3RQYWdlTGlzdCgpIHsNCiAgICAgIEdldFByb2plY3RQYWdlTGlzdCh7IFBhZ2VTaXplOiAtMSB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLnByb2plY3RzID0gcmVzLkRhdGEuRGF0YQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgdG9CYWNrKCkgew0KICAgICAgdGhpcy4kY29uZmlybSgn5q2k5pON5L2c5LiN5Lya5L+d5a2Y57yW6L6R5pWw5o2u77yM5piv5ZCm6YCA5Ye677yfJywgJ+aPkOekuicsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgY2xvc2VUYWdWaWV3KHRoaXMuJHN0b3JlLCB0aGlzLiRyb3V0ZSkNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIHR5cGU6ICdpbmZvJywNCiAgICAgICAgICAgIG1lc3NhZ2U6ICflt7Llj5bmtognDQogICAgICAgICAgfSkNCiAgICAgICAgfSkNCiAgICB9LA0KICAgIHJhZGlvQ2hhbmdlKGUpIHsNCiAgICAgIGlmIChlID09PSAncHJvX2NvbXBvbmVudF9vdXRfZGV0YWlsX2xpc3QnKSB7DQogICAgICAgIHRoaXMuYWRkcmFkaW8gPSAncHJvX3dhaXRpbmdfb3V0X2xpc3QnDQogICAgICAgIHRoaXMuSXNfUGFjayA9IGZhbHNlDQogICAgICAgIHRoaXMudGFiVHlwZUNvZGUgPSBUQUJfVFlQRS5jb20NCiAgICAgICAgdGhpcy5nZXRUYWJsZUNvbmZpZyhgJHtlfSwke3RoaXMuUHJvZmVzc2lvbmFsVHlwZVswXS5Db2RlfWApDQogICAgICB9IGVsc2UgaWYgKGUgPT09ICdwcm9fcGFja2FnZV9vdXRfZGV0YWlsX2xpc3QnKSB7DQogICAgICAgIHRoaXMuYWRkcmFkaW8gPSAncHJvX3dhaXRpbmdfb3V0X2xpc3RfcGFja2FnZScNCiAgICAgICAgdGhpcy5Jc19QYWNrID0gdHJ1ZQ0KICAgICAgICB0aGlzLnRhYlR5cGVDb2RlID0gVEFCX1RZUEUucGFja2FnZQ0KICAgICAgICB0aGlzLmdldFRhYmxlQ29uZmlnKGAke2V9LCR7dGhpcy5Qcm9mZXNzaW9uYWxUeXBlWzBdLkNvZGV9YCkNCiAgICAgIH0gZWxzZSBpZiAoZSA9PT0gJ1BST1NoaXBVbml0UGFydCcpIHsNCiAgICAgICAgdGhpcy5hZGRyYWRpbyA9ICdQUk9TaGlwQWRkVW5pdFBhcnQnDQogICAgICAgIHRoaXMudGFiVHlwZUNvZGUgPSBUQUJfVFlQRS51bml0UGFydA0KICAgICAgICB0aGlzLmdldFRhYmxlQ29uZmlnKGAke2V9YCkNCiAgICAgICAgdGhpcy5Jc19QYWNrID0gZmFsc2UNCiAgICAgIH0gZWxzZSBpZiAoZSA9PT0gJ1BST1NoaXBQYXJ0Jykgew0KICAgICAgICB0aGlzLmFkZHJhZGlvID0gJ1BST1NoaXBBZGRQYXJ0Jw0KICAgICAgICB0aGlzLklzX1BhY2sgPSBmYWxzZQ0KICAgICAgICB0aGlzLnRhYlR5cGVDb2RlID0gVEFCX1RZUEUucGFydA0KICAgICAgICB0aGlzLmdldFRhYmxlQ29uZmlnKGAke2V9YCkNCiAgICAgIH0NCiAgICB9LA0KICAgIGlucHV0Qmx1cihlLCBlMSwgcm93KSB7DQogICAgICBjb25zb2xlLmxvZygnYmx1cicsIGUxLCByb3csIHJvdy5XYWl0X1N0b2NrX0NvdW50KQ0KICAgICAgaWYgKGUxIDwgMSB8fCBlMSA+IHJvdy5XYWl0X1N0b2NrX0NvdW50KSB7DQogICAgICAgIHJvdy5TX0NvdW50ID0gcm93LldhaXRfU3RvY2tfQ291bnQNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJvdy5TX0NvdW50ID0gTnVtYmVyKGUxKQ0KICAgICAgfQ0KICAgICAgcm93LkFsbFdlaWdodCA9IHRoaXMuZ2V0QWxsV2VpZ2h0KHJvdykNCiAgICAgIHRoaXMuSXRlbWRldGFpbC5maW5kKChpdGVtKSA9PiB7DQogICAgICAgIGlmIChpdGVtLkNvbXBvbmVudF9JZCA9PSByb3cuSWQpIHsNCiAgICAgICAgICBpdGVtLlN0ZWVsQW1vdW50ID0gcm93LlNfQ291bnQNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZVN1Ym1pdCgpIHsNCiAgICAgIHRoaXMuJHJlZnNbJ2Zvcm0nXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKCF2YWxpZCkgew0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICAgIHRoaXMuaXNDbGlja2VkID0gdHJ1ZQ0KDQogICAgICAgIGNvbnN0IGZvcm1BdHRhY2htZW50ID0gW10NCiAgICAgICAgaWYgKHRoaXMuZmlsZUxpc3RBcnIubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoaXMuZmlsZUxpc3RBcnIuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICAgIGZvcm1BdHRhY2htZW50LnB1c2goDQogICAgICAgICAgICAgIGl0ZW0ucmVzcG9uc2UgJiYgaXRlbS5yZXNwb25zZS5lbmNyeXB0aW9uVXJsDQogICAgICAgICAgICAgICAgPyBpdGVtLnJlc3BvbnNlLmVuY3J5cHRpb25VcmwNCiAgICAgICAgICAgICAgICA6IGl0ZW0uZW5jcnlwdGlvblVybA0KICAgICAgICAgICAgKQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCg0KICAgICAgICBjb25zdCBzdWJtaXRPYmogPSB7DQogICAgICAgICAgcGxtX1Byb2plY3RTZW5kaW5nSW5mbzogew0KICAgICAgICAgICAgLy8gSWQ6dGhpcy5JZCwNCiAgICAgICAgICAgIENvZGU6IHRoaXMuZm9ybS5yZWNlaXZlTnVtLA0KICAgICAgICAgICAgQXR0YWNobWVudDogZm9ybUF0dGFjaG1lbnQudG9TdHJpbmcoKSwNCiAgICAgICAgICAgIFByb2plY3RJZDogdGhpcy5wcm9qZWN0SWQsDQogICAgICAgICAgICBDb25zaWduZWU6IHRoaXMuZm9ybS5yZWNlaXZlTmFtZSwNCiAgICAgICAgICAgIENvbnNpZ25lZVRlbDogdGhpcy5mb3JtLlJlY2VpdmVyX1RlbCwNCiAgICAgICAgICAgIERlcGFydF9JZDogdGhpcy5mb3JtLkRlcGFydF9JZCwNCiAgICAgICAgICAgIE1ha2VyTmFtZTogdGhpcy5mb3JtLmlzc3VlTmFtZSwNCiAgICAgICAgICAgIFZlaGljbGVObzogdGhpcy5mb3JtLkxpY2Vuc2UsDQogICAgICAgICAgICBEcml2ZXJOYW1lOiB0aGlzLmZvcm0uQ29udGFjdF9Vc2VyTmFtZSwNCiAgICAgICAgICAgIFRlbGVwaG9uZTogdGhpcy5mb3JtLk1vYmlsZSwNCiAgICAgICAgICAgIFNlbmREYXRlOiBwYXJzZVRpbWUodGhpcy5mb3JtLk91dF9EYXRlLCAne3l9LXttfS17ZH0ge2h9OntpfTp7c30nKSwNCiAgICAgICAgICAgIFJlbWFya3M6IHRoaXMuZm9ybS5SZW1hcmtzLA0KICAgICAgICAgICAgUHJvamVjdE5hbWU6IHRoaXMuZm9ybS5wcm9qZWN0TmFtZSwNCiAgICAgICAgICAgIFR5cGVJZDogdGhpcy5Qcm9mZXNzaW9uYWxUeXBlWzBdLkNvZGUsDQogICAgICAgICAgICBBZGRyZXNzOiB0aGlzLmZvcm0uQWRkcmVzcywNCiAgICAgICAgICAgIExvZ2lzdGljc19GZWU6IHRoaXMuZm9ybS5Mb2dpc3RpY3NfRmVlLA0KICAgICAgICAgICAgQ29udHJhY3RfSWQ6IHRoaXMuZm9ybS5Db250cmFjdF9JZCwNCiAgICAgICAgICAgIExvYWRpbmdzOiB0aGlzLmZvcm0uTG9hZGluZ3MsDQogICAgICAgICAgICBMb2FkaW5nc1BlcnNvbm5lbDogdGhpcy5mb3JtLkxvYWRpbmdzUGVyc29ubmVsLA0KICAgICAgICAgICAgVHJpcHM6IHRoaXMuZm9ybS5UcmlwcywNCiAgICAgICAgICAgIFJlY2VpdmluZ1VuaXQ6IHRoaXMuZm9ybS5SZWNlaXZpbmdVbml0LA0KICAgICAgICAgIH0sDQogICAgICAgICAgcHJvamVjdFNlbmRpbmdJbmZvX0l0ZW06IFtdLA0KICAgICAgICAgIFBhcnRMaXN0OiBbXQ0KICAgICAgICB9DQogICAgICAgIC8vIOWPquiOt+WPluaWsOeahOihqOagvOaVsOaNrg0KICAgICAgICBjb25zdCB0ZW1wRGF0YSA9IFsuLi50aGlzLnRiRGF0YSwgLi4udGhpcy50YkRhdGEyLCAuLi50aGlzLnRiRGF0YTMsIC4uLnRoaXMudGJEYXRhNF0NCiAgICAgICAgdGVtcERhdGEuZmlsdGVyKChpdGVtKSA9PiB7DQogICAgICAgICAgaWYgKGl0ZW0uUGFja2FnZVNuKSB7DQogICAgICAgICAgICBjb25zdCB7DQogICAgICAgICAgICAgIElkLA0KICAgICAgICAgICAgICBTdG9ja19Db3VudCwNCiAgICAgICAgICAgICAgUGFja2FnZVNuLA0KICAgICAgICAgICAgICBXYXJlaG91c2VfSWQsDQogICAgICAgICAgICAgIExvY2F0aW9uX0lkLA0KICAgICAgICAgICAgICBOZXR3ZWlnaHQsDQogICAgICAgICAgICAgIEFsbFdlaWdodCwNCiAgICAgICAgICAgICAgRElNLA0KICAgICAgICAgICAgICBWb2x1bWUsDQogICAgICAgICAgICAgIEFsbEFtb3VudCwNCiAgICAgICAgICAgICAgSW1wb3J0X0RldGFpbF9JZCwNCiAgICAgICAgICAgICAgTW9kZWxfSWRzLA0KICAgICAgICAgICAgICBpc09sZA0KICAgICAgICAgICAgfSA9IGl0ZW0NCiAgICAgICAgICAgIGNvbnN0IHRlbXBJdGVtID0gew0KICAgICAgICAgICAgICBQYWNrYWdlU246IFBhY2thZ2VTbiB8fCAnJywNCiAgICAgICAgICAgICAgU3RlZWxBbW91bnQ6IFN0b2NrX0NvdW50IHx8IDEsDQogICAgICAgICAgICAgIFdhcmVob3VzZV9JZDogV2FyZWhvdXNlX0lkIHx8ICcnLA0KICAgICAgICAgICAgICBMb2NhdGlvbl9JZDogTG9jYXRpb25fSWQgfHwgJycsDQogICAgICAgICAgICAgIFN0ZWVsV2VpZ2h0OiBOZXR3ZWlnaHQgfHwgJycsDQogICAgICAgICAgICAgIEltcG9ydF9EZXRhaWxfSWQsDQogICAgICAgICAgICAgIE1vZGVsX0lkcywNCiAgICAgICAgICAgICAgRElNLA0KICAgICAgICAgICAgICBWb2x1bWUsDQogICAgICAgICAgICAgIEFsbFdlaWdodCwNCiAgICAgICAgICAgICAgQWxsQW1vdW50LA0KICAgICAgICAgICAgICBpc09sZA0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgaWYgKCFpc09sZCkgew0KICAgICAgICAgICAgICBzdWJtaXRPYmoucHJvamVjdFNlbmRpbmdJbmZvX0l0ZW0ucHVzaCh0ZW1wSXRlbSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGVsc2UgaWYgKGl0ZW0uQ29kZSkgew0KICAgICAgICAgICAgY29uc3Qgew0KICAgICAgICAgICAgICBJZCwNCiAgICAgICAgICAgICAgU19Db3VudCwNCiAgICAgICAgICAgICAgU3RvY2tfQ291bnQsDQogICAgICAgICAgICAgIFdhcmVob3VzZV9JZCwNCiAgICAgICAgICAgICAgSW1wb3J0X0RldGFpbF9JZCwNCiAgICAgICAgICAgICAgTW9kZWxfSWRzLA0KICAgICAgICAgICAgICBMb2NhdGlvbl9JZCwNCiAgICAgICAgICAgICAgTmV0d2VpZ2h0LA0KICAgICAgICAgICAgICBBbGxXZWlnaHQsDQogICAgICAgICAgICAgIGlzT2xkDQogICAgICAgICAgICB9ID0gaXRlbQ0KICAgICAgICAgICAgY29uc3QgdGVtcEl0ZW0gPSB7DQogICAgICAgICAgICAgIENvbXBvbmVudF9JZDogSWQgfHwgJycsDQogICAgICAgICAgICAgIFN0ZWVsQW1vdW50OiBTX0NvdW50IHx8ICcnLA0KICAgICAgICAgICAgICBXYXJlaG91c2VfSWQ6IFdhcmVob3VzZV9JZCB8fCAnJywNCiAgICAgICAgICAgICAgTG9jYXRpb25fSWQ6IExvY2F0aW9uX0lkIHx8ICcnLA0KICAgICAgICAgICAgICBTdGVlbFdlaWdodDogTmV0d2VpZ2h0IHx8ICcnLA0KICAgICAgICAgICAgICBJbXBvcnRfRGV0YWlsX0lkLA0KICAgICAgICAgICAgICBNb2RlbF9JZHMsDQogICAgICAgICAgICAgIGlzT2xkLA0KICAgICAgICAgICAgICBBbGxXZWlnaHQNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGlmICghaXNPbGQpIHsNCiAgICAgICAgICAgICAgZGVsZXRlIHRlbXBJdGVtLmlzT2xkDQogICAgICAgICAgICAgIHN1Ym1pdE9iai5wcm9qZWN0U2VuZGluZ0luZm9fSXRlbS5wdXNoKHRlbXBJdGVtKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gZWxzZSBpZiAoaXRlbS5QYXJ0X0NvZGUpIHsNCiAgICAgICAgICAgIGNvbnN0IHsNCiAgICAgICAgICAgICAgUGFydF9Qcm9kdWNlZF9JZCwNCiAgICAgICAgICAgICAgUGFydF9Db2RlLA0KICAgICAgICAgICAgICBBcmVhX05hbWUsDQogICAgICAgICAgICAgIEluc3RhbGxVbml0X05hbWUsDQogICAgICAgICAgICAgIFNfQ291bnQsDQogICAgICAgICAgICAgIFNwZWMsDQogICAgICAgICAgICAgIExlbmd0aCwNCiAgICAgICAgICAgICAgV2VpZ2h0LA0KICAgICAgICAgICAgICBQYXJ0X0dyYWRlDQogICAgICAgICAgICB9ID0gaXRlbQ0KICAgICAgICAgICAgY29uc3QgdGVtcEl0ZW0gPSB7DQogICAgICAgICAgICAgIFBhcnRfUHJvZHVjZWRfSWQ6IFBhcnRfUHJvZHVjZWRfSWQsDQogICAgICAgICAgICAgIFBhcnRfQ29kZSwNCiAgICAgICAgICAgICAgQXJlYV9OYW1lLA0KICAgICAgICAgICAgICBJbnN0YWxsVW5pdF9OYW1lLA0KICAgICAgICAgICAgICBBbW91bnQ6IFNfQ291bnQsDQogICAgICAgICAgICAgIFNwZWMsDQogICAgICAgICAgICAgIExlbmd0aCwNCiAgICAgICAgICAgICAgV2VpZ2h0LA0KICAgICAgICAgICAgICBQYXJ0X0dyYWRlDQogICAgICAgICAgICB9DQogICAgICAgICAgICBzdWJtaXRPYmouUGFydExpc3QucHVzaCh0ZW1wSXRlbSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQoNCiAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gdHJ1ZQ0KICAgICAgICBpZiAodGhpcy5pc0VkaXQpIHsNCiAgICAgICAgICAvLyDojrflj5bmm7TmlrDlkI7nmoTooajljZXmlbDmja4NCiAgICAgICAgICAvLyBzdWJtaXRPYmouZW50aXR5LklkID0gdGhpcy4kcm91dGUucXVlcnkuaWQ7DQogICAgICAgICAgdGhpcy5wbG1fUHJvamVjdFNlbmRpbmdJbmZvLkNvZGUgPSB0aGlzLmZvcm0ucmVjZWl2ZU51bQ0KICAgICAgICAgIHRoaXMucGxtX1Byb2plY3RTZW5kaW5nSW5mby5Db25zaWduZWUgPSB0aGlzLmZvcm0ucmVjZWl2ZU5hbWUNCiAgICAgICAgICB0aGlzLnBsbV9Qcm9qZWN0U2VuZGluZ0luZm8uQ29uc2lnbmVlVGVsID0gdGhpcy5mb3JtLlJlY2VpdmVyX1RlbA0KICAgICAgICAgIHRoaXMucGxtX1Byb2plY3RTZW5kaW5nSW5mby5WZWhpY2xlTm8gPSB0aGlzLmZvcm0uTGljZW5zZQ0KICAgICAgICAgIHRoaXMucGxtX1Byb2plY3RTZW5kaW5nSW5mby5Ecml2ZXJOYW1lID0gdGhpcy5mb3JtLkNvbnRhY3RfVXNlck5hbWUNCiAgICAgICAgICB0aGlzLnBsbV9Qcm9qZWN0U2VuZGluZ0luZm8uVGVsZXBob25lID0gdGhpcy5mb3JtLk1vYmlsZQ0KICAgICAgICAgIHRoaXMucGxtX1Byb2plY3RTZW5kaW5nSW5mby5EZXBhcnRfSWQgPSB0aGlzLmZvcm0uRGVwYXJ0X0lkDQogICAgICAgICAgdGhpcy5wbG1fUHJvamVjdFNlbmRpbmdJbmZvLk1ha2VyTmFtZSA9IHRoaXMuZm9ybS5pc3N1ZU5hbWUNCiAgICAgICAgICB0aGlzLnBsbV9Qcm9qZWN0U2VuZGluZ0luZm8uQWRkcmVzcyA9IHRoaXMuZm9ybS5BZGRyZXNzDQogICAgICAgICAgdGhpcy5wbG1fUHJvamVjdFNlbmRpbmdJbmZvLkNvbnRyYWN0X0lkID0gdGhpcy5mb3JtLkNvbnRyYWN0X0lkDQogICAgICAgICAgdGhpcy5wbG1fUHJvamVjdFNlbmRpbmdJbmZvLkxvZ2lzdGljc19GZWUgPSB0aGlzLmZvcm0uTG9naXN0aWNzX0ZlZQ0KICAgICAgICAgIHRoaXMucGxtX1Byb2plY3RTZW5kaW5nSW5mby5Mb2FkaW5ncyA9IHRoaXMuZm9ybS5Mb2FkaW5ncw0KICAgICAgICAgIHRoaXMucGxtX1Byb2plY3RTZW5kaW5nSW5mby5Mb2FkaW5nc1BlcnNvbm5lbCA9IHRoaXMuZm9ybS5Mb2FkaW5nc1BlcnNvbm5lbA0KICAgICAgICAgIHRoaXMucGxtX1Byb2plY3RTZW5kaW5nSW5mby5UcmlwcyA9IHRoaXMuZm9ybS5Ucmlwcw0KICAgICAgICAgIHRoaXMucGxtX1Byb2plY3RTZW5kaW5nSW5mby5SZWNlaXZpbmdVbml0ID0gdGhpcy5mb3JtLlJlY2VpdmluZ1VuaXQNCiAgICAgICAgICB0aGlzLnBsbV9Qcm9qZWN0U2VuZGluZ0luZm8uU2VuZERhdGUgPSBwYXJzZVRpbWUoDQogICAgICAgICAgICB0aGlzLmZvcm0uT3V0X0RhdGUsDQogICAgICAgICAgICAne3l9LXttfS17ZH0ge2h9OntpfTp7c30nDQogICAgICAgICAgKQ0KICAgICAgICAgIHRoaXMucGxtX1Byb2plY3RTZW5kaW5nSW5mby5SZW1hcmtzID0gdGhpcy5mb3JtLlJlbWFya3MNCg0KICAgICAgICAgIGNvbnN0IGZvcm1BdHRhY2htZW50ID0gW10NCiAgICAgICAgICBpZiAodGhpcy5maWxlTGlzdEFyci5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICB0aGlzLmZpbGVMaXN0QXJyLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgICAgIGZvcm1BdHRhY2htZW50LnB1c2goDQogICAgICAgICAgICAgICAgaXRlbS5yZXNwb25zZSAmJiBpdGVtLnJlc3BvbnNlLmVuY3J5cHRpb25VcmwNCiAgICAgICAgICAgICAgICAgID8gaXRlbS5yZXNwb25zZS5lbmNyeXB0aW9uVXJsDQogICAgICAgICAgICAgICAgICA6IGl0ZW0uZW5jcnlwdGlvblVybA0KICAgICAgICAgICAgICApDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLnBsbV9Qcm9qZWN0U2VuZGluZ0luZm8uQXR0YWNobWVudCA9IGZvcm1BdHRhY2htZW50LnRvU3RyaW5nKCkNCiAgICAgICAgICBzdWJtaXRPYmoucGxtX1Byb2plY3RTZW5kaW5nSW5mbyA9IHRoaXMucGxtX1Byb2plY3RTZW5kaW5nSW5mbw0KICAgICAgICAgIC8vIOiOt+WPluaWsOeahOihqOagvOaVsOaNrg0KICAgICAgICAgIC8vIHN1Ym1pdE9iai5wcm9qZWN0U2VuZGluZ0luZm9fSXRlbSA9DQogICAgICAgICAgLy8gICBzdWJtaXRPYmoucHJvamVjdFNlbmRpbmdJbmZvX0l0ZW0uZmlsdGVyKChpdGVtKSA9PiB7DQogICAgICAgICAgLy8gICAgIHJldHVybiAhaXRlbS5pc09sZDsNCiAgICAgICAgICAvLyAgIH0pOw0KICAgICAgICAgIC8vIOa3u+WKoOaWsOiAgeihqOagvOaVsOaNrg0KICAgICAgICAgIHN1Ym1pdE9iai5wcm9qZWN0U2VuZGluZ0luZm9fSXRlbSA9IFsNCiAgICAgICAgICAgIC4uLnRoaXMuSXRlbWRldGFpbCwNCiAgICAgICAgICAgIC4uLnRoaXMuUGFja2FnZXNMaXN0LA0KICAgICAgICAgICAgLi4uc3VibWl0T2JqLnByb2plY3RTZW5kaW5nSW5mb19JdGVtDQogICAgICAgICAgXQ0KICAgICAgICAgIGlmIChzdWJtaXRPYmoucHJvamVjdFNlbmRpbmdJbmZvX0l0ZW0ubGVuZ3RoID09PSAwICYmIHN1Ym1pdE9iai5QYXJ0TGlzdC5sZW5ndGggPT09IDApIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiAn5LiN6IO95L+d5a2Y56m65Y+R6LSn5Y2VJywNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQ0KICAgICAgICAgICAgRWRpdFByb2plY3RTZW5kaW5nSW5mbyhzdWJtaXRPYmopLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgICAgbWVzc2FnZTogJ+e8lui+keaIkOWKnycsDQogICAgICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycNCiAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgIGNsb3NlVGFnVmlldyh0aGlzLiRzdG9yZSwgdGhpcy4kcm91dGUpDQogICAgICAgICAgICAgICAgdGhpcy4kcm91dGVyLnJlcGxhY2Uoew0KICAgICAgICAgICAgICAgICAgbmFtZTogJ1BST1NoaXBTZW50JywNCiAgICAgICAgICAgICAgICAgIHF1ZXJ5OiB7DQogICAgICAgICAgICAgICAgICAgIHJlZnJlc2g6IDENCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIHRoaXMuaXNDbGlja2VkID0gZmFsc2UNCiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGlmIChzdWJtaXRPYmoucHJvamVjdFNlbmRpbmdJbmZvX0l0ZW0ubGVuZ3RoID09PSAwICYmIHN1Ym1pdE9iai5QYXJ0TGlzdC5sZW5ndGggPT09IDApIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiAn5LiN6IO95L+d5a2Y56m65Y+R6LSn5Y2VJywNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgY29uc29sZS5sb2coJ3N1Ym1pdE9iaicsIHN1Ym1pdE9iaikNCiAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgICAgICAgIEFkZFByb2plY3RTZW5kaW5nSW5mbyhzdWJtaXRPYmopLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgICAgbWVzc2FnZTogJ+a3u+WKoOaIkOWKnycsDQogICAgICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycNCiAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgIGNsb3NlVGFnVmlldyh0aGlzLiRzdG9yZSwgdGhpcy4kcm91dGUpDQogICAgICAgICAgICAgICAgdGhpcy4kcm91dGVyLnJlcGxhY2Uoew0KICAgICAgICAgICAgICAgICAgbmFtZTogJ1BST1NoaXBTZW50JywNCiAgICAgICAgICAgICAgICAgIHF1ZXJ5OiB7DQogICAgICAgICAgICAgICAgICAgIHJlZnJlc2g6IDENCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIHRoaXMuaXNDbGlja2VkID0gZmFsc2UNCiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgYXN5bmMgZ2V0U3RvcExpc3QobGlzdCwga2V5KSB7DQogICAgICBjb25zdCBzdWJtaXRPYmogPSBsaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICBJZDogaXRlbVtrZXldLA0KICAgICAgICAgIFR5cGU6IHRoaXMudGFiVHlwZUNvZGUgPT09IFRBQl9UWVBFLmNvbSA/IDIgOiB0aGlzLnRhYlR5cGVDb2RlID09PSBUQUJfVFlQRS51bml0UGFydCA/IDMgOiAxIC8vIDHvvJrpm7bku7bvvIwz77ya6YOo5Lu277yMMu+8muaehOS7tg0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgYXdhaXQgR2V0U3RvcExpc3Qoc3VibWl0T2JqKS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgY29uc3Qgc3RvcE1hcCA9IHt9DQogICAgICAgICAgcmVzLkRhdGEuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICAgIHN0b3BNYXBbaXRlbS5JZF0gPSAhIWl0ZW0uSXNfU3RvcA0KICAgICAgICAgIH0pDQogICAgICAgICAgbGlzdC5mb3JFYWNoKHJvdyA9PiB7DQogICAgICAgICAgICBpZiAoc3RvcE1hcFtyb3dba2V5XV0pIHsNCiAgICAgICAgICAgICAgdGhpcy4kc2V0KHJvdywgJ3N0b3BGbGFnJywgc3RvcE1hcFtyb3dba2V5XV0pDQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFzeW5jIGdldEluZm8oKSB7DQogICAgICBhd2FpdCBHZXRQcm9qZWN0c2VuZGluZ2luRW50aXR5KHsNCiAgICAgICAgSWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LmlkDQogICAgICB9KS50aGVuKGFzeW5jKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMucGxtX1Byb2plY3RTZW5kaW5nSW5mbyA9IHJlcy5EYXRhLlBsbV9Qcm9qZWN0U2VuZGluZ0luZm8NCiAgICAgICAgICB0aGlzLkl0ZW1kZXRhaWwgPSByZXMuRGF0YS5JdGVtZGV0YWlsDQogICAgICAgICAgdGhpcy5QYWNrYWdlc0xpc3QgPSByZXMuRGF0YS5QYWNrYWdlc0xpc3QNCiAgICAgICAgICB0aGlzLlBhcnRMaXN0ID0gcmVzLkRhdGEuUGFydExpc3QNCiAgICAgICAgICB0aGlzLndlaWdodGZvcm0gPSByZXMuRGF0YS5XZWlnaHRJbmZvDQogICAgICAgICAgdGhpcy5wYWdlU3RhdHVzID0gdGhpcy5wbG1fUHJvamVjdFNlbmRpbmdJbmZvLlN0YXR1cw0KICAgICAgICAgIHRoaXMuaXNEcmFmdCA9IHRoaXMucGxtX1Byb2plY3RTZW5kaW5nSW5mbz8uU3RhdHVzID09PSAwDQoNCiAgICAgICAgICBjb25zdCB7DQogICAgICAgICAgICBJZCwNCiAgICAgICAgICAgIENvZGUsDQogICAgICAgICAgICBQcm9qZWN0SWQsDQogICAgICAgICAgICBQcm9qZWN0TmFtZSwNCiAgICAgICAgICAgIENvbnNpZ25lZSwNCiAgICAgICAgICAgIENvbnNpZ25lZVRlbCwNCiAgICAgICAgICAgIERlcGFydF9JZCwNCiAgICAgICAgICAgIE1ha2VyTmFtZSwNCiAgICAgICAgICAgIFZlaGljbGVObywNCiAgICAgICAgICAgIERyaXZlck5hbWUsDQogICAgICAgICAgICBUZWxlcGhvbmUsDQogICAgICAgICAgICBBZGRyZXNzLA0KICAgICAgICAgICAgQXR0YWNobWVudCwNCiAgICAgICAgICAgIFNlbmREYXRlLA0KICAgICAgICAgICAgUmVtYXJrcywNCiAgICAgICAgICAgIE51bWJlciwNCiAgICAgICAgICAgIExvZ2lzdGljc19GZWUsDQogICAgICAgICAgICBDb250cmFjdF9JZCwNCiAgICAgICAgICAgIExvYWRpbmdzLA0KICAgICAgICAgICAgTG9hZGluZ3NQZXJzb25uZWwsDQogICAgICAgICAgICBUcmlwcywNCiAgICAgICAgICAgIFJlY2VpdmluZ1VuaXQsDQoNCiAgICAgICAgICB9ID0gdGhpcy5wbG1fUHJvamVjdFNlbmRpbmdJbmZvDQoNCiAgICAgICAgICB0aGlzLmZvcm0uUHJvamVjdElkID0gUHJvamVjdElkDQogICAgICAgICAgdGhpcy5mb3JtLnJlY2VpdmVOdW0gPSBDb2RlDQogICAgICAgICAgdGhpcy5mb3JtLnByb2plY3ROYW1lID0gUHJvamVjdE5hbWUNCiAgICAgICAgICB0aGlzLmZvcm0ucmVjZWl2ZU5hbWUgPSBDb25zaWduZWUNCiAgICAgICAgICB0aGlzLmZvcm0uUmVjZWl2ZXJfVGVsID0gQ29uc2lnbmVlVGVsDQogICAgICAgICAgdGhpcy5mb3JtLkRlcGFydF9JZCA9IERlcGFydF9JZA0KICAgICAgICAgIHRoaXMuZm9ybS5pc3N1ZU5hbWUgPSBNYWtlck5hbWUNCiAgICAgICAgICB0aGlzLmZvcm0uTGljZW5zZSA9IFZlaGljbGVObw0KICAgICAgICAgIHRoaXMuZm9ybS5Db250YWN0X1VzZXJOYW1lID0gRHJpdmVyTmFtZQ0KICAgICAgICAgIHRoaXMuZm9ybS5Nb2JpbGUgPSBUZWxlcGhvbmUNCiAgICAgICAgICB0aGlzLmZvcm0uQWRkcmVzcyA9IEFkZHJlc3MNCiAgICAgICAgICB0aGlzLmZvcm0uT3V0X0RhdGUgPSBuZXcgRGF0ZShTZW5kRGF0ZSkNCiAgICAgICAgICB0aGlzLmZvcm0uUmVtYXJrcyA9IFJlbWFya3MNCiAgICAgICAgICB0aGlzLmZvcm0uTG9naXN0aWNzX0ZlZSA9IExvZ2lzdGljc19GZWUgfHwgdW5kZWZpbmVkDQogICAgICAgICAgdGhpcy5mb3JtLkNvbnRyYWN0X0lkID0gQ29udHJhY3RfSWQNCiAgICAgICAgICB0aGlzLmZvcm0uTG9hZGluZ3MgPSBMb2FkaW5ncw0KICAgICAgICAgIHRoaXMuZm9ybS5Mb2FkaW5nc1BlcnNvbm5lbCA9IExvYWRpbmdzUGVyc29ubmVsDQogICAgICAgICAgdGhpcy5mb3JtLlRyaXBzID0gVHJpcHMNCiAgICAgICAgICB0aGlzLmZvcm0uUmVjZWl2aW5nVW5pdCA9IFJlY2VpdmluZ1VuaXQNCiAgICAgICAgICB0aGlzLnNlbmROdW1iZXIgPSBOdW1iZXINCg0KICAgICAgICAgIGlmIChWZWhpY2xlTm8gJiYgdGhpcy5jYXJPcHRpb25zLmV2ZXJ5KCh2KSA9PiB2LkxpY2Vuc2UgIT09IFZlaGljbGVObykpIHsNCiAgICAgICAgICAgIHRoaXMuY2FyT3B0aW9ucy5wdXNoKHsNCiAgICAgICAgICAgICAgTGljZW5zZTogVmVoaWNsZU5vLA0KICAgICAgICAgICAgICBDb250YWN0X1VzZXJOYW1lOiBEcml2ZXJOYW1lLA0KICAgICAgICAgICAgICBNb2JpbGU6IFRlbGVwaG9uZSwNCiAgICAgICAgICAgICAgZGV0YWlsOiBWZWhpY2xlTm8gPyBgJHtWZWhpY2xlTm99KCR7RHJpdmVyTmFtZX0gJHtUZWxlcGhvbmV9KWAgOiAnJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBpZiAoQXR0YWNobWVudCkgew0KICAgICAgICAgICAgY29uc3QgQXR0YWNobWVudEFyciA9IEF0dGFjaG1lbnQuc3BsaXQoJywnKQ0KICAgICAgICAgICAgQXR0YWNobWVudEFyci5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgICAgICBjb25zdCBmaWxlVXJsID0NCiAgICAgICAgICAgICAgICBpdGVtLmluZGV4T2YoJz9FeHBpcmVzPScpID4gLTENCiAgICAgICAgICAgICAgICAgID8gaXRlbS5zdWJzdHJpbmcoMCwgaXRlbS5sYXN0SW5kZXhPZignP0V4cGlyZXM9JykpDQogICAgICAgICAgICAgICAgICA6IGl0ZW0NCiAgICAgICAgICAgICAgY29uc3QgZmlsZU5hbWUgPSBkZWNvZGVVUkkoZmlsZVVybC5zdWJzdHJpbmcoZmlsZVVybC5sYXN0SW5kZXhPZignLycpICsgMSkpDQogICAgICAgICAgICAgIGNvbnN0IEF0dGFjaG1lbnRKc29uID0ge30NCiAgICAgICAgICAgICAgQXR0YWNobWVudEpzb24ubmFtZSA9IGRlY29kZVVSSUNvbXBvbmVudChmaWxlTmFtZSkNCiAgICAgICAgICAgICAgQXR0YWNobWVudEpzb24udXJsID0gZmlsZVVybA0KICAgICAgICAgICAgICBBdHRhY2htZW50SnNvbi5lbmNyeXB0aW9uVXJsID0gZmlsZVVybA0KICAgICAgICAgICAgICB0aGlzLmZpbGVMaXN0RGF0YS5wdXNoKEF0dGFjaG1lbnRKc29uKQ0KICAgICAgICAgICAgICB0aGlzLmZpbGVMaXN0QXJyLnB1c2goQXR0YWNobWVudEpzb24pDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGlmICh0aGlzLndlaWdodGZvcm0uQXR0YWNobWVudF9XZWlnaHQpIHsNCiAgICAgICAgICAgIGNvbnN0IGltZ1Byb21pc2VBbGwyID0gdGhpcy53ZWlnaHRmb3JtLkF0dGFjaG1lbnRfV2VpZ2h0LnNwbGl0KCcsJykubWFwKGFzeW5jIHVybCA9PiB7DQogICAgICAgICAgICAgIGNvbnN0IGZpbGVVcmwgPSB1cmwuc3BsaXQoJz8nKVswXQ0KICAgICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICAgIHVybDogYXdhaXQgdGhpcy5oYW5kbGVVcmwoZmlsZVVybCksDQogICAgICAgICAgICAgICAgbmFtZTogZ2V0RmlsZU5hbWVGcm9tVXJsKGZpbGVVcmwpDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICB0aGlzLndlaWdodEZpbGVJbmZvID0gYXdhaXQgUHJvbWlzZS5hbGwoaW1nUHJvbWlzZUFsbDIpDQogICAgICAgICAgfQ0KDQogICAgICAgICAgdGhpcy5JdGVtZGV0YWlsLmZvckVhY2goKGl0ZW0sIGluZGV4KSA9PiB7DQogICAgICAgICAgICBjb25zdCB7DQogICAgICAgICAgICAgIENvbXBvbmVudF9JZCwNCiAgICAgICAgICAgICAgU19Db3VudCwNCiAgICAgICAgICAgICAgU3RlZWxXZWlnaHQsDQogICAgICAgICAgICAgIEFsbFdlaWdodCwNCiAgICAgICAgICAgICAgTmFtZSwNCiAgICAgICAgICAgICAgU3BlYywNCiAgICAgICAgICAgICAgTGVuZ3RoLA0KICAgICAgICAgICAgICBXYXJlaG91c2VOYW1lLA0KICAgICAgICAgICAgICBDb2RlLA0KICAgICAgICAgICAgICBMb2NhdGlvbk5hbWUsDQogICAgICAgICAgICAgIEFyZWFfTmFtZSwNCiAgICAgICAgICAgICAgV2FpdF9TdG9ja19Db3VudCwNCiAgICAgICAgICAgICAgSW1wb3J0X0RldGFpbF9JZCwNCiAgICAgICAgICAgICAgTG9jYXRpb25fSWQsDQogICAgICAgICAgICAgIFNlcmlhbE51bWJlcg0KICAgICAgICAgICAgfSA9IGl0ZW0NCiAgICAgICAgICAgIGNvbnN0IHRlbXBJdGVtID0gew0KICAgICAgICAgICAgICBJZDogQ29tcG9uZW50X0lkLA0KICAgICAgICAgICAgICBBcmVhX05hbWU6IEFyZWFfTmFtZSwNCiAgICAgICAgICAgICAgTmFtZSwNCiAgICAgICAgICAgICAgU3BlYywNCiAgICAgICAgICAgICAgTGVuZ3RoLA0KICAgICAgICAgICAgICBXYXJlaG91c2VOYW1lLA0KICAgICAgICAgICAgICBDb2RlLA0KICAgICAgICAgICAgICBMb2NhdGlvbk5hbWUsDQogICAgICAgICAgICAgIEltcG9ydF9EZXRhaWxfSWQsDQogICAgICAgICAgICAgIExvY2F0aW9uX0lkLA0KICAgICAgICAgICAgICBTX0NvdW50OiBTX0NvdW50LA0KICAgICAgICAgICAgICBXYWl0X1N0b2NrX0NvdW50OiBXYWl0X1N0b2NrX0NvdW50LA0KICAgICAgICAgICAgICBOZXR3ZWlnaHQ6IFN0ZWVsV2VpZ2h0LA0KICAgICAgICAgICAgICBBbGxXZWlnaHQ6IEFsbFdlaWdodCwNCiAgICAgICAgICAgICAgaXNPbGQ6IHRydWUsDQogICAgICAgICAgICAgIFNlcmlhbE51bWJlcjogU2VyaWFsTnVtYmVyDQogICAgICAgICAgICB9DQogICAgICAgICAgICB0aGlzLnRiRGF0YS5wdXNoKHRlbXBJdGVtKQ0KICAgICAgICAgICAgdGhpcy5vbGRfQ29tcG9uZW50X0lkcy5wdXNoKENvbXBvbmVudF9JZCkNCiAgICAgICAgICAgIHRoaXMuZ2V0U3RvcExpc3QodGhpcy50YkRhdGEsICdDb21wb25lbnRfSWQnKQ0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy5QYXJ0TGlzdC5mb3JFYWNoKChlbGVtZW50LCBpZHgpID0+IHsNCiAgICAgICAgICAgIGNvbnN0IHRlbXBJdGVtID0geyAuLi5lbGVtZW50IH0NCiAgICAgICAgICAgIHRlbXBJdGVtLlNfQ291bnQgPSB0ZW1wSXRlbS5BbW91bnQNCiAgICAgICAgICAgIHRlbXBJdGVtLk5ldHdlaWdodCA9IHRlbXBJdGVtLldlaWdodA0KICAgICAgICAgICAgdGVtcEl0ZW0uQWxsV2VpZ2h0ID0gdGhpcy5nZXRBbGxXZWlnaHQodGVtcEl0ZW0pDQogICAgICAgICAgICB0ZW1wSXRlbS5Ub3RhbF9XZWlnaHQgPSBudW1lcmFsKHRlbXBJdGVtLlN0b2NrX0NvdW50KS5tdWx0aXBseSh0ZW1wSXRlbS5XZWlnaHQpLnZhbHVlKCkNCiAgICAgICAgICAgIGlmICh0ZW1wSXRlbS5QYXJ0X0dyYWRlID4gMCkgew0KICAgICAgICAgICAgICB0aGlzLnRiRGF0YTMucHVzaCh0ZW1wSXRlbSkNCiAgICAgICAgICAgICAgdGhpcy5nZXRTdG9wTGlzdCh0aGlzLnRiRGF0YTMsICdQYXJ0X0FnZ3JlZ2F0ZV9JZCcpDQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCd0aGlzLnRiRGF0YTMnLCB0aGlzLnRiRGF0YTMpDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLnRiRGF0YTQucHVzaCh0ZW1wSXRlbSkNCiAgICAgICAgICAgICAgdGhpcy5nZXRTdG9wTGlzdCh0aGlzLnRiRGF0YTQsICdQYXJ0X0FnZ3JlZ2F0ZV9JZCcpDQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCd0aGlzLnRiRGF0YTQnLCB0aGlzLnRiRGF0YTQpDQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCg0KICAgICAgICAgIHRoaXMuUGFja2FnZXNMaXN0LmZvckVhY2goKGl0ZW0sIGluZGV4KSA9PiB7DQogICAgICAgICAgICBjb25zdCB7DQogICAgICAgICAgICAgIFBrZ05PLA0KICAgICAgICAgICAgICBQYWNrYWdlU24sDQogICAgICAgICAgICAgIEFsbFdlaWdodCwNCiAgICAgICAgICAgICAgVm9sdW1lLA0KICAgICAgICAgICAgICBBbGxBbW91bnQsDQogICAgICAgICAgICAgIFdhcmVob3VzZU5hbWUsDQogICAgICAgICAgICAgIExvY2F0aW9uTmFtZSwNCiAgICAgICAgICAgICAgRElNLA0KICAgICAgICAgICAgICBQYWNrYWdlSWQNCiAgICAgICAgICAgIH0gPSBpdGVtDQogICAgICAgICAgICBjb25zdCB0ZW1wSXRlbSA9IHsNCiAgICAgICAgICAgICAgUGtnTk86IFBrZ05PLA0KICAgICAgICAgICAgICBQYWNrYWdlU246IFBhY2thZ2VTbiwNCiAgICAgICAgICAgICAgQWxsV2VpZ2h0LA0KICAgICAgICAgICAgICBBbGxBbW91bnQsDQogICAgICAgICAgICAgIFZvbHVtZSwNCiAgICAgICAgICAgICAgV2FyZWhvdXNlTmFtZSwNCiAgICAgICAgICAgICAgTG9jYXRpb25OYW1lLA0KICAgICAgICAgICAgICBpc09sZDogdHJ1ZSwNCiAgICAgICAgICAgICAgRElNOiBESU0sDQogICAgICAgICAgICAgIFBhY2thZ2VJZA0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgdGhpcy50YkRhdGEyLnB1c2godGVtcEl0ZW0pDQogICAgICAgICAgfSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBjaGVja1NlbGVjdGFibGUocm93KSB7DQogICAgICByZXR1cm4gIXJvdy5zdG9wRmxhZw0KICAgIH0sDQogICAgYXN5bmMgaGFuZGxlVXJsKHVybCkgew0KICAgICAgY29uc3QgeyBEYXRhIH0gPSBhd2FpdCBHZXRPc3NVcmwoeyB1cmwgfSkNCiAgICAgIHJldHVybiBEYXRhDQogICAgfSwNCiAgICBjYXJDaGFuZ2UodmFsKSB7DQogICAgICBpZiAoIXZhbCkgew0KICAgICAgICB0aGlzLmZvcm0uQ29udGFjdF9Vc2VyTmFtZSA9ICcnDQogICAgICAgIHRoaXMuZm9ybS5Nb2JpbGUgPSAnJw0KICAgICAgICB0aGlzLmZvcm0uTGljZW5zZSA9ICcnDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgY29uc3QgaXRlbSA9IHRoaXMuY2FyT3B0aW9ucy5maW5kKCh2KSA9PiB2LkxpY2Vuc2UgPT09IHRoaXMuZm9ybS5MaWNlbnNlKQ0KICAgICAgdGhpcy5mb3JtLkNvbnRhY3RfVXNlck5hbWUgPSBpdGVtLkNvbnRhY3RfVXNlck5hbWUNCiAgICAgIHRoaXMuZm9ybS5Nb2JpbGUgPSBpdGVtLk1vYmlsZQ0KICAgICAgdGhpcy5mb3JtLkxpY2Vuc2UgPSBpdGVtLkxpY2Vuc2UNCiAgICB9LA0KICAgIHByb2plY3RJZENoYW5nZShlKSB7DQogICAgICAvLyBpZiAoZSkgew0KICAgICAgLy8gICB0aGlzLmdldEFyZWFMaXN0KCk7DQogICAgICAvLyB9DQogICAgfSwNCiAgICBwcm9qZWN0SWRDbGVhcihlKSB7DQogICAgICAvLyB0aGlzLiRyZWZzLmZvcm0yLnJlc2V0RmllbGRzKCk7DQogICAgfSwNCiAgICBnZXRBbGxXZWlnaHQoaXRlbSkgew0KICAgICAgcmV0dXJuIE51bWJlcihpdGVtLlNfQ291bnQgKiBpdGVtLk5ldHdlaWdodCkudG9GaXhlZCgyKSAvIDENCiAgICB9LA0KICAgIGFkZFNlbGVjdExpc3QobGlzdCkgew0KICAgICAgY29uc29sZS5sb2cobGlzdCwgJ2xpc3QnKQ0KICAgICAgY29uc29sZS5sb2codGhpcy50YWJUeXBlQ29kZSkNCiAgICAgIGlmICh0aGlzLnRhYlR5cGVDb2RlID09PSBUQUJfVFlQRS5jb20pIHsNCiAgICAgICAgY29uc29sZS5sb2coMTEpDQogICAgICAgIGxpc3QuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgIGl0ZW0uQWxsV2VpZ2h0ID0gdGhpcy5nZXRBbGxXZWlnaHQoaXRlbSkNCg0KICAgICAgICAgIHRoaXMudGJEYXRhLnB1c2goaXRlbSkNCiAgICAgICAgfSkNCiAgICAgICAgdGhpcy50YkRhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMudGJEYXRhKSkNCiAgICAgICAgdGhpcy50b3RhbCA9IHRoaXMudGJEYXRhLmxlbmd0aA0KICAgICAgfSBlbHNlIGlmICh0aGlzLnRhYlR5cGVDb2RlID09PSBUQUJfVFlQRS51bml0UGFydCkgew0KICAgICAgICBsaXN0LmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICBpdGVtLkFsbFdlaWdodCA9IHRoaXMuZ2V0QWxsV2VpZ2h0KGl0ZW0pDQogICAgICAgICAgdGhpcy50YkRhdGEzLnB1c2goaXRlbSkNCiAgICAgICAgfSkNCiAgICAgICAgLy8gZG9udCBhc2sgd2h5IGp1c3QgY3YNCiAgICAgICAgdGhpcy50YkRhdGEzID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLnRiRGF0YTMpKQ0KICAgICAgICB0aGlzLnRvdGFsID0gdGhpcy50YkRhdGEzLmxlbmd0aA0KICAgICAgfSBlbHNlIGlmICh0aGlzLnRhYlR5cGVDb2RlID09PSBUQUJfVFlQRS5wYXJ0KSB7DQogICAgICAgIGxpc3QuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgIGl0ZW0uQWxsV2VpZ2h0ID0gdGhpcy5nZXRBbGxXZWlnaHQoaXRlbSkNCiAgICAgICAgICB0aGlzLnRiRGF0YTQucHVzaChpdGVtKQ0KICAgICAgICB9KQ0KICAgICAgICB0aGlzLnRiRGF0YTQgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMudGJEYXRhNCkpDQogICAgICAgIHRoaXMudG90YWwgPSB0aGlzLnRiRGF0YTQubGVuZ3RoDQogICAgICB9IGVsc2UgaWYgKHRoaXMudGFiVHlwZUNvZGUgPT09IFRBQl9UWVBFLnBhY2thZ2UpIHsNCiAgICAgICAgbGlzdC5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgdGhpcy50YkRhdGEyLnB1c2goaXRlbSkNCiAgICAgICAgfSkNCiAgICAgICAgdGhpcy50b3RhbCA9IHRoaXMudGJEYXRhMi5sZW5ndGgNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZURlbGV0ZSgpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+WIoOmZpOivpeaVsOaNriwg5piv5ZCm57un57utPycsICfmj5DnpLonLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIGlmICh0aGlzLklzX1BhY2spIHsNCiAgICAgICAgICAgIHRoaXMuc2VsZWN0TGlzdC5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy50YkRhdGEyLmZpbmRJbmRleCgNCiAgICAgICAgICAgICAgICAodikgPT4gdi5QYWNrYWdlU24gPT09IGl0ZW0uUGFja2FnZVNuDQogICAgICAgICAgICAgICkNCiAgICAgICAgICAgICAgaW5kZXggIT09IC0xICYmIHRoaXMudGJEYXRhMi5zcGxpY2UoaW5kZXgsIDEpDQogICAgICAgICAgICAgIGlmICh0aGlzLmlzRWRpdCkgew0KICAgICAgICAgICAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5QYWNrYWdlc0xpc3QuZmluZEluZGV4KA0KICAgICAgICAgICAgICAgICAgKHYpID0+IHYuUGFja2FnZVNuID09PSBpdGVtLlBhY2thZ2VTbg0KICAgICAgICAgICAgICAgICkNCiAgICAgICAgICAgICAgICBpbmRleCAhPT0gLTEgJiYgdGhpcy5QYWNrYWdlc0xpc3Quc3BsaWNlKGluZGV4LCAxKQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBsZXQgX3RhYmxlID0gbnVsbA0KICAgICAgICAgICAgaWYgKHRoaXMudGFiVHlwZUNvZGUgPT09IFRBQl9UWVBFLmNvbSkgew0KICAgICAgICAgICAgICBfdGFibGUgPSB0aGlzLnRiRGF0YQ0KICAgICAgICAgICAgICB0aGlzLnNlbGVjdExpc3QuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgICAgICAgIGNvbnN0IGluZGV4ID0gX3RhYmxlLmZpbmRJbmRleCgodikgPT4gdi5JbXBvcnRfRGV0YWlsX0lkICsgdi5Mb2NhdGlvbl9JZCA9PT0gaXRlbS5JbXBvcnRfRGV0YWlsX0lkICsgaXRlbS5Mb2NhdGlvbl9JZCkNCiAgICAgICAgICAgICAgICBpbmRleCAhPT0gLTEgJiYgX3RhYmxlLnNwbGljZShpbmRleCwgMSkNCiAgICAgICAgICAgICAgICBpZiAodGhpcy5pc0VkaXQpIHsNCiAgICAgICAgICAgICAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5JdGVtZGV0YWlsLmZpbmRJbmRleCgNCiAgICAgICAgICAgICAgICAgICAgKHYpID0+IHYuQ29tcG9uZW50X0lkID09PSBpdGVtLklkDQogICAgICAgICAgICAgICAgICApDQogICAgICAgICAgICAgICAgICBpbmRleCAhPT0gLTEgJiYgdGhpcy5JdGVtZGV0YWlsLnNwbGljZShpbmRleCwgMSkNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICB9IGVsc2UgaWYgKHRoaXMudGFiVHlwZUNvZGUgPT09IFRBQl9UWVBFLnVuaXRQYXJ0KSB7DQogICAgICAgICAgICAgIF90YWJsZSA9IHRoaXMudGJEYXRhMw0KICAgICAgICAgICAgICB0aGlzLnNlbGVjdExpc3QuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgICAgICAgIGNvbnN0IGluZGV4ID0gX3RhYmxlLmZpbmRJbmRleCgodikgPT4gdi5QYXJ0X1Byb2R1Y2VkX0lkID09PSBpdGVtLlBhcnRfUHJvZHVjZWRfSWQpDQogICAgICAgICAgICAgICAgaW5kZXggIT09IC0xICYmIF90YWJsZS5zcGxpY2UoaW5kZXgsIDEpDQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICB9IGVsc2UgaWYgKHRoaXMudGFiVHlwZUNvZGUgPT09IFRBQl9UWVBFLnBhcnQpIHsNCiAgICAgICAgICAgICAgX3RhYmxlID0gdGhpcy50YkRhdGE0DQogICAgICAgICAgICAgIHRoaXMuc2VsZWN0TGlzdC5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgICAgICAgY29uc3QgaW5kZXggPSBfdGFibGUuZmluZEluZGV4KCh2KSA9PiB2LlBhcnRfUHJvZHVjZWRfSWQgPT09IGl0ZW0uUGFydF9Qcm9kdWNlZF9JZCkNCiAgICAgICAgICAgICAgICBpbmRleCAhPT0gLTEgJiYgX3RhYmxlLnNwbGljZShpbmRleCwgMSkNCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsDQogICAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5oiQ5YqfIScNCiAgICAgICAgICB9KQ0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKCkgPT4ge30pDQogICAgfSwNCiAgICBtdWx0aVNlbGVjdGVkQ2hhbmdlKHYpIHsNCiAgICAgIHRoaXMuc2VsZWN0TGlzdCA9IHYNCiAgICB9LA0KICAgIGZldGNoRGF0YSgpIHsNCiAgICAgIC8vIEdldFN0b2NrT3V0RGV0YWlsTGlzdCh7IHN0b2NrT3V0SWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LmlkIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgIC8vICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgIC8vICAgICB0aGlzLnRiRGF0YSA9IHJlcy5EYXRhDQogICAgICAvLyAgICAgdGhpcy50YkRhdGEuZm9yRWFjaCgoZWxlbWVudCwgaWR4KSA9PiB7DQogICAgICAvLyAgICAgICB0aGlzLiRzZXQoZWxlbWVudCwgJ1VuaXF1ZUNvZGVzQXJyYXknLCBlbGVtZW50LlVuaXF1ZUNvZGVzLnNwbGl0KCcsJykpDQogICAgICAvLyAgICAgfSkNCiAgICAgIC8vICAgfSBlbHNlIHsNCiAgICAgIC8vICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgIC8vICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgLy8gICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgLy8gICAgIH0pDQogICAgICAvLyAgIH0NCiAgICAgIC8vICAgdGhpcy50YkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgLy8gfSkNCiAgICB9LA0KICAgIGdldFRvdGFsKCkgew0KICAgICAgLy8gdGhpcy4kbmV4dFRpY2soXyA9PiB7DQogICAgICAvLyAgIGNvbnN0IGNvbHVtbnMgPSB0aGlzLiRyZWZzLmR5VGFibGUuJHJlZnMuZHRhYmxlLmNvbHVtbnMNCiAgICAgIC8vICAgY29sdW1ucy5mb3JFYWNoKChlbGVtZW50LCBpZHgpID0+IHsNCiAgICAgIC8vICAgICBpZiAoaWR4ID09PSAwKSB7DQogICAgICAvLyAgICAgICB0aGlzLnN1bXNbMF0gPSAn5ZCI6K6hJw0KICAgICAgLy8gICAgIH0gZWxzZSBpZiAoZWxlbWVudC5wcm9wZXJ0eSA9PT0gJ091dF9Db3VudCcpIHsNCiAgICAgIC8vICAgICAgIGNvbnN0IHYgPSB0aGlzLnRiRGF0YS5yZWR1Y2UoKGFjYywgY3VyKSA9PiB7DQogICAgICAvLyAgICAgICAgIHJldHVybiB0aGlzLmhpZ2hQcmVjaXNpb25BZGQoYWNjLCBjdXJbZWxlbWVudC5wcm9wZXJ0eV0pDQogICAgICAvLyAgICAgICB9LCAwKQ0KICAgICAgLy8gICAgICAgdGhpcy4kc2V0KHRoaXMuc3VtcywgaWR4LCB2KQ0KICAgICAgLy8gICAgIH0gZWxzZSBpZiAoZWxlbWVudC5wcm9wZXJ0eSA9PT0gJ05ldFdlaWdodCcpIHsNCiAgICAgIC8vICAgICAgIGNvbnN0IHYgPSB0aGlzLnRiRGF0YS5yZWR1Y2UoKGFjYywgY3VyKSA9PiB7DQogICAgICAvLyAgICAgICAgIHJldHVybiB0aGlzLmhpZ2hQcmVjaXNpb25BZGQoYWNjLCBjdXJbZWxlbWVudC5wcm9wZXJ0eV0gKiBjdXJbJ091dF9Db3VudCddKQ0KICAgICAgLy8gICAgICAgfSwgMCkNCiAgICAgIC8vICAgICAgIHRoaXMuJHNldCh0aGlzLnN1bXMsIGlkeCwgbnVtZXJhbCh2KS5kaXZpZGUoMTAwMCkuZm9ybWF0KCcwLlswMF0nKSArICfvvIh077yJJykNCiAgICAgIC8vICAgICB9IGVsc2Ugew0KICAgICAgLy8gICAgICAgdGhpcy4kc2V0KHRoaXMuc3VtcywgaWR4LCAnJykNCiAgICAgIC8vICAgICB9DQogICAgICAvLyAgIH0pDQogICAgICAvLyB9KQ0KICAgIH0sDQogICAgZ2V0QWxsQ2FyTGlzdCgpIHsNCiAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4gew0KICAgICAgICBHZXRDdXJDYXJQYWdlTGlzdCh7fSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIHRoaXMuY2FyT3B0aW9ucyA9IHJlcy5EYXRhDQogICAgICAgICAgICB0aGlzLmNhck9wdGlvbnMuZm9yRWFjaCgoZWxlbWVudCwgaWR4KSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJHNldCgNCiAgICAgICAgICAgICAgICBlbGVtZW50LA0KICAgICAgICAgICAgICAgICdkZXRhaWwnLA0KICAgICAgICAgICAgICAgIGAke2VsZW1lbnQuTGljZW5zZX0oJHtlbGVtZW50LkNvbnRhY3RfVXNlck5hbWV9ICR7ZWxlbWVudC5Nb2JpbGV9KWANCiAgICAgICAgICAgICAgKQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHJlc29sdmUoKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFkZENhckRhdGEoZGF0YSkgew0KICAgICAgdGhpcy5nZXRBbGxDYXJMaXN0KCkNCiAgICAgIC8vIHRoaXMuY2FyT3B0aW9ucy5wdXNoKGRhdGEpOw0KICAgIH0sDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ0FkZERpYWxvZycNCiAgICAgIHRoaXMud2lkdGggPSAnODAlJw0KICAgICAgdGhpcy50b3BEaWFsb2cgPSAnMXZoJw0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgICAgaWYgKHRoaXMuSXNfUGFjayA9PT0gZmFsc2UpIHsNCiAgICAgICAgbGV0IF90YWJsZSA9IG51bGwNCiAgICAgICAgaWYgKHRoaXMudGFiVHlwZUNvZGUgPT09IFRBQl9UWVBFLmNvbSkgew0KICAgICAgICAgIF90YWJsZSA9IHRoaXMudGJEYXRhDQogICAgICAgICAgdGhpcy50aXRsZSA9ICfmt7vliqDmnoTku7YnDQogICAgICAgIH0gZWxzZSBpZiAodGhpcy50YWJUeXBlQ29kZSA9PT0gVEFCX1RZUEUudW5pdFBhcnQpIHsNCiAgICAgICAgICBfdGFibGUgPSB0aGlzLnRiRGF0YTMNCiAgICAgICAgICB0aGlzLnRpdGxlID0gJ+a3u+WKoOmDqOS7ticNCiAgICAgICAgfSBlbHNlIGlmICh0aGlzLnRhYlR5cGVDb2RlID09PSBUQUJfVFlQRS5wYXJ0KSB7DQogICAgICAgICAgX3RhYmxlID0gdGhpcy50YkRhdGE0DQogICAgICAgICAgdGhpcy50aXRsZSA9ICfmt7vliqDpm7bku7YnDQogICAgICAgIH0NCg0KICAgICAgICBjb25zdCB0ZW1wRGF0YTEgPSBfdGFibGUuZmlsdGVyKChpdGVtKSA9PiB7DQogICAgICAgICAgcmV0dXJuICFpdGVtLmlzT2xkDQogICAgICAgIH0pDQogICAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgICAgdGhpcy4kcmVmcy5jb250ZW50LmluaXQodGVtcERhdGExLCBfdGFibGUpDQogICAgICAgIH0pDQogICAgICB9IGVsc2UgaWYgKHRoaXMuSXNfUGFjayA9PT0gdHJ1ZSkgew0KICAgICAgICB0aGlzLnRpdGxlID0gJ+a3u+WKoOaJk+WMheS7ticNCiAgICAgICAgY29uc3QgdGVtcERhdGEyID0gdGhpcy50YkRhdGEyLmZpbHRlcigoaXRlbSkgPT4gew0KICAgICAgICAgIHJldHVybiAhaXRlbS5pc09sZA0KICAgICAgICB9KQ0KICAgICAgICB0aGlzLiRuZXh0VGljaygoXykgPT4gew0KICAgICAgICAgIHRoaXMuJHJlZnMuY29udGVudC5pbml0KHRlbXBEYXRhMiwgdGhpcy50YkRhdGEyKQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5omT5YyF5Lu26K+m5oOFDQogICAgaGFuZGxlRGV0YWlsKHJvdykgew0KICAgICAgY29uc29sZS5sb2cocm93LCAnaWQ9PT0nKQ0KICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ3BhY2tEZXRhaWwnDQogICAgICB0aGlzLndpZHRoID0gJzYwJScNCiAgICAgIHRoaXMudGl0bGUgPSAn5omT5YyF5Lu26K+m5oOFJw0KICAgICAgdGhpcy50b3BEaWFsb2cgPSAnMTB2aCcNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgIHRoaXMuJHJlZnMuY29udGVudC5pbml0KHJvdy5QYWNrYWdlSWQgfHwgcm93LklkKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZUVkaXRDYXIoKSB7DQogICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSAnQ2FyRGlhbG9nJw0KICAgICAgdGhpcy50aXRsZSA9ICfmlrDlop7ovabovoYnDQogICAgICB0aGlzLnRvcERpYWxvZyA9ICcxMHZoJw0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgIH0sDQogICAgY2xvc2UoKSB7DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgIH0sDQogICAgaGFuZGxlSW5mbyhyb3cpIHsNCiAgICAgIHRoaXMuJHJlZnMuaW5mby5oYW5kbGVPcGVuKHJvdykNCiAgICB9LA0KICAgIGdldFRhYmxlQ29uZmlnKGNvZGUpIHsNCiAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4gew0KICAgICAgICBHZXRHcmlkQnlDb2RlKHsNCiAgICAgICAgICBjb2RlDQogICAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIGNvbnN0IHsgSXNTdWNjZWVkLCBEYXRhLCBNZXNzYWdlIH0gPSByZXMNCiAgICAgICAgICBpZiAoSXNTdWNjZWVkKSB7DQogICAgICAgICAgICBpZiAoIURhdGEpIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgbWVzc2FnZTogJ+ihqOagvOmFjee9ruS4jeWtmOWcqCcsDQogICAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHRoaXMudGJDb25maWcgPSBPYmplY3QuYXNzaWduKHt9LCB0aGlzLnRiQ29uZmlnLCBEYXRhLkdyaWQpDQogICAgICAgICAgICB0aGlzLmNvbHVtbnMgPSAoDQogICAgICAgICAgICAgIERhdGEuQ29sdW1uTGlzdC5maWx0ZXIoKHYpID0+IHYuSXNfRGlzcGxheSkgfHwgW10NCiAgICAgICAgICAgICkubWFwKChpdGVtKSA9PiB7DQogICAgICAgICAgICAgIGl0ZW0uSXNfUmVzaXphYmxlID0gdHJ1ZQ0KICAgICAgICAgICAgICBpdGVtLklzX1NvcnRhYmxlID0gdHJ1ZQ0KICAgICAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHRoaXMuZm9ybS5QYWdlSW5mby5QYWdlU2l6ZSA9ICtEYXRhLkdyaWQuUm93X051bWJlcg0KICAgICAgICAgICAgaWYgKHRoaXMuaXNTdWIpIHsNCiAgICAgICAgICAgICAgdGhpcy50YkNvbmZpZy5Jc19TZWxlY3QgPSBmYWxzZQ0KICAgICAgICAgICAgICB0aGlzLnRiQ29uZmlnLklzX1Jvd19OdW1iZXIgPSB0cnVlDQogICAgICAgICAgICB9DQogICAgICAgICAgICByZXNvbHZlKHRoaXMuY29sdW1ucykNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6IE1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHVwbG9hZFN1Y2Nlc3MocmVzcG9uc2UsIGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICBjb25zb2xlLmxvZygnZmlsZUxpc3QnLCBmaWxlTGlzdCkNCiAgICAgIHRoaXMuZmlsZUxpc3RBcnIgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KGZpbGVMaXN0KSkNCiAgICB9LA0KICAgIHVwbG9hZFJlbW92ZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy5maWxlTGlzdEFyciA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkoZmlsZUxpc3QpKQ0KICAgIH0sDQogICAgYXN5bmMgaGFuZGxlUHJldmlldyhmaWxlKSB7DQogICAgICBjb25zb2xlLmxvZygnZmlsZScsIGZpbGUpDQogICAgICBsZXQgZW5jcnlwdGlvblVybCA9ICcnDQogICAgICBpZiAoZmlsZS5yZXNwb25zZSAmJiBmaWxlLnJlc3BvbnNlLmVuY3J5cHRpb25VcmwpIHsNCiAgICAgICAgZW5jcnlwdGlvblVybCA9IGZpbGUucmVzcG9uc2UuZW5jcnlwdGlvblVybA0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgZW5jcnlwdGlvblVybCA9IGF3YWl0IEdldE9zc1VybCh7IHVybDogZmlsZS5lbmNyeXB0aW9uVXJsIH0pDQogICAgICAgIGVuY3J5cHRpb25VcmwgPSBlbmNyeXB0aW9uVXJsLkRhdGENCiAgICAgIH0NCiAgICAgIHdpbmRvdy5vcGVuKGVuY3J5cHRpb25VcmwpDQogICAgfSwNCiAgICBoYW5kbGVFeGNlZWQoKSB7DQogICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnLA0KICAgICAgICBtZXNzYWdlOiAn6ZmE5Lu25pWw6YeP5LiN6IO96LaF6L+HMTDkuKonDQogICAgICB9KQ0KICAgIH0sDQogICAgYXN5bmMgZ2V0Q29udHJhY3RMaXN0KCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3Qgc3VibWl0RGF0YSA9IHsNCiAgICAgICAgICBDb250cmFjdFR5cGVDb2RlOiAzLA0KICAgICAgICAgIFByb2plY3RJZHM6IFtdDQogICAgICAgIH0NCiAgICAgICAgaWYgKHRoaXMuZm9ybS5Qcm9qZWN0SWQpIHsNCiAgICAgICAgICBzdWJtaXREYXRhLlByb2plY3RJZHMucHVzaCh0aGlzLmZvcm0uUHJvamVjdElkKQ0KICAgICAgICB9DQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IEdldENvbnRyYWN0TGlzdChzdWJtaXREYXRhKQ0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMuY29udHJhY3RPcHRpb25zID0gcmVzLkRhdGENCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluWQiOWQjOWIl+ihqOWksei0pTonLCBlcnJvcikNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["Add.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2YA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Add.vue", "sourceRoot": "src/views/PRO/shipment/actually-sent/v4/component", "sourcesContent": ["<template>\r\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\r\n    <div class=\"cs-z-page-main-content\">\r\n      <top-header>\r\n        <template #left>\r\n          <div class=\"cs-header\">\r\n            <!-- <el-button\r\n              circle\r\n              icon=\"el-icon-arrow-left\"\r\n              size=\"mini\"\r\n              @click=\"toBack\"\r\n            /> -->\r\n            <el-button @click=\"toBack\">返回</el-button>\r\n            <!-- <strong class=\"title\">{{\r\n              isEdit === true ? \"编辑发货单\" : \"新增发货单\"\r\n            }}</strong> -->\r\n          </div>\r\n        </template>\r\n        <template #right>\r\n          <!-- <el-button type=\"primary\" :loading=\"btnLoading\">打印发货单</el-button> -->\r\n          <el-button type=\"primary\" :loading=\"loading\" @click=\"handleSubmit\">保存</el-button>\r\n        </template>\r\n      </top-header>\r\n      <!-- <title-info :title=\"projectName\" /> -->\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"发货单号\" prop=\"receiveNum\">\r\n              <el-input\r\n                v-model=\"form.receiveNum\"\r\n                :disabled=\"autoGenerate || (!isDraft && isEdit)\"\r\n                :placeholder=\"autoGenerate ? '自动生成':'请输入'\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"项目名称\" prop=\"projectName\">\r\n              <el-input v-model=\"form.projectName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"收货人\" prop=\"receiveName\">\r\n              <el-input\r\n                v-model=\"form.receiveName\"\r\n                clearable=\"\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"收货人电话\" prop=\"Receiver_Tel\">\r\n              <el-input\r\n                v-model=\"form.Receiver_Tel\"\r\n                clearable=\"\"\r\n                placeholder=\"请输入\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"车辆信息\" prop=\"License\">\r\n              <el-select\r\n                v-model=\"form.License\"\r\n                clearable\r\n                :disabled=\"((pageStatus>=2)||pageStatus===1)&&isEdit\"\r\n                placeholder=\"请选择\"\r\n                style=\"width: 70%\"\r\n                filterable\r\n                @change=\"carChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in carOptions\"\r\n                  :key=\"item.License\"\r\n                  :label=\"item.detail\"\r\n                  :value=\"item.License\"\r\n                />\r\n              </el-select>\r\n              <el-button\r\n                style=\"margin-left: 10px\"\r\n                type=\"text\"\r\n                @click=\"handleEditCar\"\r\n              >新增车辆</el-button>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"发货部门\" prop=\"Depart_Id\">\r\n              <el-select\r\n                v-if=\"isProductweight===true\"\r\n                v-model=\"form.Depart_Id\"\r\n                clearable\r\n                placeholder=\"请选择\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in pickDepartmentList\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n              <el-tree-select\r\n                v-else\r\n                ref=\"treeSelectDepart\"\r\n                v-model=\"form.Depart_Id\"\r\n                :select-params=\"{\r\n                  clearable: true,\r\n                }\"\r\n                class=\"cs-tree-x\"\r\n                :tree-params=\"treeParamsDepart\"\r\n                @select-clear=\"departClear\"\r\n                @node-click=\"departChange\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"发货人\" prop=\"issueName\">\r\n              <el-input v-model=\"form.issueName\" clearable=\"\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"发货时间\" prop=\"Out_Date\">\r\n              <el-date-picker\r\n                v-model=\"form.Out_Date\"\r\n                style=\"width: 100%\"\r\n                type=\"datetime\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"装车班\" prop=\"Loadings\">\r\n              <SelectDepartment v-model=\"form.Loadings\" @change=\"form.LoadingsPersonnel = ''\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"装车班人员\" prop=\"LoadingsPersonnel \">\r\n              <SelectDepartmentUser v-model=\"form.LoadingsPersonnel\" :department-id=\"form.Loadings\" :disabled=\"!form.Loadings\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"车次\" prop=\"Trips \">\r\n              <el-input v-model=\"form.Trips\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"专业\" prop=\"ProfessionalTypeName\">\r\n              <el-input v-model=\"form.ProfessionalTypeName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"收货地址\" prop=\"Address\">\r\n              <el-input v-model=\"form.Address\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"收货单位\" prop=\"ReceivingUnit\">\r\n              <el-input v-model=\"form.ReceivingUnit\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"物流费\" prop=\"Logistics_Fee\">\r\n              <el-input-number\r\n                v-model=\"form.Logistics_Fee\"\r\n                :min=\"0\"\r\n                :precision=\"2\"\r\n                style=\"width: 100%\"\r\n                class=\"cs-number-btn-hidden\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"关联合同\" prop=\"Contract_Id\">\r\n              <el-select\r\n                v-model=\"form.Contract_Id\"\r\n                clearable\r\n                filterable\r\n                placeholder=\"请选择\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in contractOptions\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.ContractName\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"备注\">\r\n              <el-input\r\n                v-model=\"form.Remarks\"\r\n                :autosize=\"{ minRows: 2, maxRows: 2 }\"\r\n                :maxlength=\"1000\"\r\n                show-word-limit\r\n                type=\"textarea\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"附件\" class=\"factory-img\">\r\n              <OSSUpload\r\n                class=\"upload-demo\"\r\n                action=\"alioss\"\r\n                :limit=\"10\"\r\n                :multiple=\"true\"\r\n                :on-success=\"\r\n                  (response, file, fileList) => {\r\n                    uploadSuccess(response, file, fileList)\r\n                  }\r\n                \"\r\n                :on-remove=\"uploadRemove\"\r\n                :on-preview=\"handlePreview\"\r\n                :on-exceed=\"handleExceed\"\r\n                :file-list=\"fileListData\"\r\n                :show-file-list=\"true\"\r\n                :disabled=\"false\"\r\n              >\r\n                <el-button\r\n                  type=\"primary\"\r\n                  :disabled=\"false\"\r\n                >上传文件</el-button>\r\n              </OSSUpload>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n\r\n      <div v-if=\"isEdit\">\r\n        <h4> 过磅信息</h4>\r\n        <el-form ref=\"form\" inline :model=\"weightform\" label-width=\"80px\">\r\n          <el-form-item label=\"皮重\">\r\n            {{ weightform.Tare_Weight }}kg\r\n          </el-form-item>\r\n          <el-form-item label=\"理重\">\r\n            {{ weightform.Reason_Weight }}kg\r\n          </el-form-item>\r\n          <el-form-item label=\"磅重\">\r\n            {{ weightform.Pound_Weight }}kg\r\n          </el-form-item>\r\n          <el-form-item label=\"净重\" prop=\"region\">\r\n            <span :class=\"{'cs-red':showRed}\">{{ netWeight }}\r\n              <span v-if=\"showRed\">（{{ getNum(netWeight,weightform.Reason_Weight)>0 ?'高于':'低于' }}理重{{ Math.abs(+getNum(netWeight, weightform.Reason_Weight)) }}kg）</span>\r\n            </span>\r\n          </el-form-item>\r\n          <el-form-item label=\"过磅备注\" prop=\"region\">\r\n            {{ plm_ProjectSendingInfo.Pound_Remark }}\r\n          </el-form-item>\r\n          <el-form-item label=\"附件\">\r\n            <template v-for=\"(item,idx) in weightFileInfo\">\r\n              <el-link\r\n                :key=\"idx\"\r\n                :href=\"item.url\"\r\n                target=\"_blank\"\r\n              >{{ item.name }}</el-link>\r\n              <el-divider v-if=\"idx!==weightFileInfo.length -1\" :key=\"idx\" direction=\"vertical\" />\r\n            </template>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <top-header style=\"margin-bottom: 10px\">\r\n        <template #left>\r\n          <div class=\"cs-header\" style=\"margin-bottom: 20px\">\r\n            <el-radio-group v-model=\"radio\" size=\"small\" @change=\"radioChange\">\r\n              <el-radio-button\r\n                label=\"pro_component_out_detail_list\"\r\n              >构件</el-radio-button>\r\n              <el-radio-button\r\n                label=\"pro_package_out_detail_list\"\r\n              >打包件</el-radio-button>\r\n              <el-radio-button\r\n                label=\"PROShipUnitPart\"\r\n              >部件</el-radio-button>\r\n              <el-radio-button\r\n                label=\"PROShipPart\"\r\n              >零件</el-radio-button>\r\n            </el-radio-group>\r\n          </div>\r\n        </template>\r\n        <template #right>\r\n          <div v-if=\"sendNumber\" class=\"statistics-container\">\r\n            <div class=\"statistics-item\" style=\"margin-right: 0\">\r\n              <span>发货序号：</span>\r\n              <span>{{ sendNumber }}</span>\r\n            </div>\r\n          </div>\r\n          <el-button\r\n            v-if=\"!isSub\"\r\n            :disabled=\"!selectList.length\"\r\n            size=\"mini\"\r\n            type=\"danger\"\r\n            @click=\"handleDelete\"\r\n          >删除</el-button>\r\n          <el-button\r\n            v-if=\"!isSub\"\r\n            size=\"mini\"\r\n            type=\"primary\"\r\n            @click=\"handleAdd\"\r\n          >添加</el-button>\r\n        </template>\r\n      </top-header>\r\n\r\n      <div v-loading=\"tbLoading\" class=\"fff cs-z-tb-wrapper\">\r\n        <dynamic-data-table\r\n          ref=\"dyTable\"\r\n          class=\"cs-plm-dy-table\"\r\n          :columns=\"columns\"\r\n          :config=\"tbConfig\"\r\n          :data=\"tabTypeCode === 2 ? tbData2 : tabTypeCode === 1 ? tbData :tabTypeCode === 3 ? tbData3 :tbData4\"\r\n          :page=\"form.PageInfo.Page\"\r\n          :sum-values=\"sums\"\r\n          :select-width=\"70\"\r\n          :total=\"total\"\r\n          border\r\n          stripe\r\n          @checkSelectable=\"checkSelectable\"\r\n          @multiSelectedChange=\"multiSelectedChange\"\r\n        >\r\n          <template slot=\"Code\" slot-scope=\"{ row }\">\r\n            <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n            <span>{{ row.Code }}</span>\r\n          </template>\r\n          <template slot=\"Part_Code\" slot-scope=\"{ row }\">\r\n            <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n            <span>{{ row.Part_Code }}</span>\r\n          </template>\r\n          <template slot=\"S_Count\" slot-scope=\"{ row }\">\r\n            <div v-if=\"!Is_Pack && !isSub\">\r\n              <el-input\r\n                v-model=\"row.S_Count\"\r\n                type=\"text\"\r\n                :readonly=\"row.Wait_Stock_Count == 1\"\r\n                style=\"width: 50px; border: 1px solid #eee; border-radius: 4px\"\r\n                @blur=\"\r\n                  (e) => {\r\n                    inputBlur(e, row.S_Count, row);\r\n                  }\r\n                \"\r\n              />\r\n            </div>\r\n            <div v-else>{{ row.S_Count }}</div>\r\n          </template>\r\n          <template slot=\"PackageSn\" slot-scope=\"{ row }\">\r\n            <div style=\"color: #298dff; cursor: pointer;\" @click=\"handleDetail(row)\">{{ row.PackageSn }}</div>\r\n          </template>\r\n          <!--          <template slot=\"AllWeight\" slot-scope=\"{ row }\">-->\r\n          <!--            {{ row.S_Count * row.Netweight }}-->\r\n          <!--          </template>-->\r\n          <!--  <template slot=\"Unique_Code\" slot-scope=\"{ row }\">\r\n            {{ row.C_Type === \"打包件\" ? row.Unique_Code : \"-\" }}\r\n          </template>\r\n          <template slot=\"op\" slot-scope=\"{ row, index }\">\r\n            <el-button\r\n              v-if=\"row.C_Type === '打包件'\"\r\n              :index=\"index\"\r\n              type=\"text\"\r\n              @click=\"handleInfo(row)\"\r\n              >查看</el-button\r\n            >\r\n          </template> -->\r\n        </dynamic-data-table>\r\n      </div>\r\n\r\n      <el-dialog\r\n        v-if=\"dialogVisible\"\r\n        v-dialogDrag\r\n        class=\"plm-custom-dialog\"\r\n        :title=\"title\"\r\n        :visible.sync=\"dialogVisible\"\r\n        :width=\"width\"\r\n        :top=\"topDialog\"\r\n        @close=\"close\"\r\n      >\r\n        <component\r\n          :is=\"currentComponent\"\r\n          ref=\"content\"\r\n          :dialog-visible=\"dialogVisible\"\r\n          :project-id=\"projectId\"\r\n          :sys-project-id=\"form.ProjectId\"\r\n          :add-radio=\"addradio\"\r\n          :tab-type-code=\"tabTypeCode\"\r\n          :is-pack=\"Is_Pack\"\r\n          @addCarData=\"addCarData\"\r\n          @close=\"close\"\r\n          @reCount=\"getTotal\"\r\n          @refresh=\"fetchData\"\r\n          @selectList=\"addSelectList\"\r\n        />\r\n      </el-dialog>\r\n      <check-info ref=\"info\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport TitleInfo from './TitleInfo.vue'\r\nimport AddDialog from './AddDialog.vue'\r\nimport packDetail from './packDetail.vue'\r\nimport {\r\n  AddProjectSendingInfo,\r\n  GetProjectsendinginEntity,\r\n  EditProjectSendingInfo\r\n} from '@/api/PRO/component-stock-out'\r\nimport { GetFirstLevelDepartsUnderFactory } from '@/api/PRO/material-warehouse/material-inventory-reconfig.js'\r\nimport { GetPreferenceSettingValue } from '@/api/sys/system-setting'\r\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable.vue'\r\nimport CarDialog from './CarDialog.vue'\r\nimport { GetCurCarPageList } from '@/api/PRO/car'\r\nimport TopHeader from '@/components/TopHeader/index.vue'\r\nimport CheckInfo from '@/views/PRO/Component/GetPackingDetail/index.vue'\r\nimport { closeTagView, parseTime } from '@/utils'\r\nimport { GetProjectPageList, GetProjectEntity } from '@/api/PRO/pro-schedules'\r\nimport { GetGridByCode, GetOssUrl } from '@/api/sys'\r\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\r\nimport { GetCompanyDepartTree } from '@/api/sys'\r\nimport numeral from 'numeral'\r\nimport OSSUpload from '@/views/PRO/components/ossupload.vue'\r\nimport { getFileNameFromUrl } from '@/utils/file'\r\nimport { mapGetters } from 'vuex'\r\nimport { GetContractList } from '@/api/plm/production'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nimport SelectDepartmentUser from '@/components/Select/SelectDepartmentUser/index.vue'\r\nimport SelectDepartment from '@/components/Select/SelectDepartment/index.vue'\r\n\r\nconst TAB_TYPE = {\r\n  com: 1,\r\n  package: 2,\r\n  unitPart: 3,\r\n  part: 4\r\n}\r\nexport default {\r\n  components: {\r\n    SelectDepartment, SelectDepartmentUser,\r\n    OSSUpload,\r\n    TitleInfo,\r\n    AddDialog,\r\n    TopHeader,\r\n    DynamicDataTable,\r\n    CarDialog,\r\n    CheckInfo,\r\n    packDetail\r\n  },\r\n  props: {\r\n    isEdit: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      pageStatus: undefined,\r\n      radio: 'pro_component_out_detail_list',\r\n      addradio: 'pro_waiting_out_list',\r\n      Is_Pack: false,\r\n      isClicked: false,\r\n      isSub: false, // 是否提交过\r\n      width: '40%',\r\n      topDialog: '1vh',\r\n      btnLoading: false,\r\n      currentComponent: '',\r\n      title: '',\r\n      loading: false,\r\n      dialogVisible: false,\r\n      sendNumber: '',\r\n      form: {\r\n        ProjectId: '',\r\n        Out_Date: new Date(),\r\n        Remarks: '',\r\n        Contact_UserName: '',\r\n        Mobile: '',\r\n        License: '',\r\n        Address: '',\r\n        receiveName: '',\r\n        issueName: '',\r\n        Receiver_Tel: '',\r\n        Area_Id: '',\r\n        projectName: '',\r\n        receiveNum: '',\r\n        ProfessionalTypeName: '',\r\n        Depart_Id: '',\r\n        Logistics_Fee: undefined,\r\n        Contract_Id: '',\r\n        PageInfo: {\r\n          ParameterJson: [],\r\n          Page: 1,\r\n          PageSize: 20\r\n        },\r\n        Loadings: '',\r\n        LoadingsPersonnel: '',\r\n        Trips: '',\r\n        ReceivingUnit: ''\r\n      },\r\n      // 发货部门\r\n      treeParamsDepart: {\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          disabled: 'disabled',\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      pickDepartmentList: [], // 品重发货部门\r\n      plm_ProjectSendingInfo: {},\r\n      produced_Components: [],\r\n      weightFileInfo: [],\r\n      projectSendingInfo_Item: [],\r\n      Itemdetail: [],\r\n      PackagesList: [],\r\n      ProfessionalType: [],\r\n      fileListArr: [],\r\n      carOptions: [],\r\n      projects: '',\r\n      Id: '',\r\n      projectId: '',\r\n      planTime: '',\r\n      showDialog: false,\r\n      tbConfig: {\r\n        Pager_Align: 'center'\r\n      },\r\n      columns: [],\r\n      tbData: [],\r\n      tbData2: [],\r\n      tbData3: [],\r\n      tbData4: [],\r\n      total: 0,\r\n      tbLoading: false,\r\n      autoGenerate: true,\r\n      selectList: [],\r\n      fileListData: [],\r\n      sums: [],\r\n      rules: {\r\n        Out_Date: [\r\n          { required: true, message: '请选择发货时间', trigger: 'change' }\r\n        ],\r\n        receiveNum: [{ required: false, message: '请输入发货单号', trigger: 'blur' }],\r\n        // receiveName: [{ required: true, message: '请输入', trigger: 'blur' }],\r\n        // Receiver_Tel: [{ required: true, message: '请输入', trigger: 'blur' }],\r\n        Depart_Id: [{ required: true, message: '请选择', trigger: 'change' }],\r\n        issueName: [{ required: true, message: '请输入', trigger: 'blur' }]\r\n      },\r\n      old_Component_Ids: [],\r\n      weightform: {\r\n        Tare_Weight: 0, // 皮重\r\n        Reason_Weight: 0, // 理重\r\n        Pound_Weight: 0, // 磅重\r\n        Net_Weight: 0, // 净重\r\n        Weigh_Warning_Threshold: 0\r\n      },\r\n      isProductweight: null,\r\n      tabTypeCode: 1,\r\n      isDraft: false,\r\n      contractOptions: []\r\n    }\r\n  },\r\n  provide() {\r\n    return {\r\n      isVersionFour: this.isVersionFour\r\n    }\r\n  },\r\n  computed: {\r\n    netWeight() {\r\n      if (!this.weightform.Pound_Weight || !this.weightform.Tare_Weight) return 0\r\n      return this.weightform.Pound_Weight - this.weightform.Tare_Weight\r\n    },\r\n    showRed({ netWeight }) {\r\n      return Math.abs(netWeight - this.weightform.Reason_Weight) >= this.weightform.Weigh_Warning_Threshold\r\n    },\r\n    ...mapGetters('tenant', ['isVersionFour'])\r\n  },\r\n  watch: {\r\n    'tbData.length': {\r\n      handler() {\r\n        this.getTotal()\r\n      }\r\n    }\r\n\r\n  },\r\n  async created() {\r\n    this.tabTypeCode = TAB_TYPE.com\r\n    this.isSub = this.$route.query.isSub === '1' || false\r\n    await this.getSettingProductweight()\r\n    this.getFactoryDepartmentData()\r\n    this.getDepartmentTree()\r\n    this.getFactoryTypeOption()\r\n    this.getAllCarList()\r\n  },\r\n  async mounted() {\r\n    if (this.isEdit) {\r\n      const {\r\n        autoGenerate\r\n      } = JSON.parse(decodeURIComponent(this.$route.query.p))\r\n      this.autoGenerate = autoGenerate\r\n      await this.getInfo()\r\n    } else {\r\n      const {\r\n        Name,\r\n        Id,\r\n        Code,\r\n        Address,\r\n        autoGenerate,\r\n        Sys_Project_Id\r\n      } = JSON.parse(decodeURIComponent(this.$route.query.p))\r\n      // this.projectName = Name;\r\n      this.autoGenerate = autoGenerate\r\n      this.projectId = Id\r\n      this.Project_Code = Code\r\n      this.form.projectName = Name\r\n      this.form.Address = Address\r\n      this.form.ProjectId = Sys_Project_Id\r\n      this.form.issueName = this.$store.state.user.name\r\n      this.getProjectEntity(this.projectId)\r\n      this.rules.receiveNum[0].required = !autoGenerate\r\n    }\r\n    this.getContractList()\r\n  },\r\n  methods: {\r\n    async getSettingProductweight() {\r\n      const res = await GetPreferenceSettingValue({ code: 'Productweight' })\r\n      if (res.Data === 'true') {\r\n        this.isProductweight = true\r\n      }\r\n    },\r\n\r\n    // 获取工厂数据\r\n    getFactoryDepartmentData() {\r\n      GetFirstLevelDepartsUnderFactory({ FactoryId: localStorage.getItem('CurReferenceId') }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.pickDepartmentList = res.Data\r\n          const depId = localStorage.getItem('DepartmentId')\r\n          const cur = this.pickDepartmentList.find((item) => item.Id === depId)\r\n          if (cur) {\r\n            this.form.Depart_Id = cur.Id\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    getDepartmentTree() {\r\n      GetCompanyDepartTree({ isAll: true }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const tree = res.Data\r\n          this.setDisabledTree(tree)\r\n          this.treeParamsDepart.data = tree\r\n          this.$nextTick(_ => {\r\n            this.$refs.treeSelectDepart?.treeDataUpdateFun(tree)\r\n            const arr = this.getFlattenedSelectableItems(tree)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getFlattenedSelectableItems(root) {\r\n      if (!root || !root.length) return []\r\n      const result = []\r\n      const flatten = (items) => {\r\n        if (!items || !items.length) return\r\n        items.forEach((item) => {\r\n          const { Children } = item\r\n          if (item.Data?.Is_Company !== true && item.Data?.Type !== '1') {\r\n            result.push(item)\r\n          }\r\n          if (Children && Children.length > 0) {\r\n            flatten(Children)\r\n          }\r\n        })\r\n      }\r\n      flatten(root)\r\n      const cur = result.find((item) => item.Id === localStorage.getItem('DepartmentId'))\r\n      if (cur && !this.isEdit) {\r\n        this.form.Depart_Id = cur.Id\r\n      }\r\n    },\r\n    setDisabledTree(root) {\r\n      if (!root) return\r\n      root.forEach((element) => {\r\n        const { Children } = element\r\n        if (element.Data.Is_Company === true || element.Data.Type === '1') {\r\n          element.disabled = true\r\n        } else {\r\n          element.disabled = false\r\n        }\r\n        if (Children.length > 0) {\r\n          this.setDisabledTree(Children)\r\n        }\r\n        // if (Children && Children.length) {\r\n        //   element.disabled = true\r\n        // } else {\r\n        //   element.disabled = false\r\n        //   this.setDisabledTree(Children)\r\n        // }\r\n      })\r\n    },\r\n    departClear() {\r\n\r\n    },\r\n    departChange() {\r\n\r\n    },\r\n    getNum(a, b) {\r\n      return numeral(a).subtract(b).format('0.[000]')\r\n    },\r\n    async getFactoryTypeOption() {\r\n      await GetFactoryProfessionalByCode({\r\n        factoryId: localStorage.getItem('CurReferenceId')\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.ProfessionalType = res.Data\r\n          this.form.ProfessionalTypeName = this.ProfessionalType[0].Name\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      await this.getTableConfig(\r\n        `pro_component_out_detail_list,${this.ProfessionalType[0].Code}`\r\n      )\r\n    },\r\n    getProjectEntity(Id) {\r\n      GetProjectEntity({ Id }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const Consignee = res.Data.Contacts.find((item) => {\r\n            return item.Type == 'Consignee'\r\n          })\r\n          this.form.receiveName = Consignee?.Name || ''\r\n          this.form.Receiver_Tel = Consignee?.Tel || ''\r\n          this.form.Trips = res.Data.Trips || ''\r\n        }\r\n      })\r\n    },\r\n    getProjectPageList() {\r\n      GetProjectPageList({ PageSize: -1 }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.projects = res.Data.Data\r\n        }\r\n      })\r\n    },\r\n    toBack() {\r\n      this.$confirm('此操作不会保存编辑数据，是否退出？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          closeTagView(this.$store, this.$route)\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n    },\r\n    radioChange(e) {\r\n      if (e === 'pro_component_out_detail_list') {\r\n        this.addradio = 'pro_waiting_out_list'\r\n        this.Is_Pack = false\r\n        this.tabTypeCode = TAB_TYPE.com\r\n        this.getTableConfig(`${e},${this.ProfessionalType[0].Code}`)\r\n      } else if (e === 'pro_package_out_detail_list') {\r\n        this.addradio = 'pro_waiting_out_list_package'\r\n        this.Is_Pack = true\r\n        this.tabTypeCode = TAB_TYPE.package\r\n        this.getTableConfig(`${e},${this.ProfessionalType[0].Code}`)\r\n      } else if (e === 'PROShipUnitPart') {\r\n        this.addradio = 'PROShipAddUnitPart'\r\n        this.tabTypeCode = TAB_TYPE.unitPart\r\n        this.getTableConfig(`${e}`)\r\n        this.Is_Pack = false\r\n      } else if (e === 'PROShipPart') {\r\n        this.addradio = 'PROShipAddPart'\r\n        this.Is_Pack = false\r\n        this.tabTypeCode = TAB_TYPE.part\r\n        this.getTableConfig(`${e}`)\r\n      }\r\n    },\r\n    inputBlur(e, e1, row) {\r\n      console.log('blur', e1, row, row.Wait_Stock_Count)\r\n      if (e1 < 1 || e1 > row.Wait_Stock_Count) {\r\n        row.S_Count = row.Wait_Stock_Count\r\n      } else {\r\n        row.S_Count = Number(e1)\r\n      }\r\n      row.AllWeight = this.getAllWeight(row)\r\n      this.Itemdetail.find((item) => {\r\n        if (item.Component_Id == row.Id) {\r\n          item.SteelAmount = row.S_Count\r\n        }\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (!valid) {\r\n          return\r\n        }\r\n        this.isClicked = true\r\n\r\n        const formAttachment = []\r\n        if (this.fileListArr.length > 0) {\r\n          this.fileListArr.forEach(item => {\r\n            formAttachment.push(\r\n              item.response && item.response.encryptionUrl\r\n                ? item.response.encryptionUrl\r\n                : item.encryptionUrl\r\n            )\r\n          })\r\n        }\r\n\r\n        const submitObj = {\r\n          plm_ProjectSendingInfo: {\r\n            // Id:this.Id,\r\n            Code: this.form.receiveNum,\r\n            Attachment: formAttachment.toString(),\r\n            ProjectId: this.projectId,\r\n            Consignee: this.form.receiveName,\r\n            ConsigneeTel: this.form.Receiver_Tel,\r\n            Depart_Id: this.form.Depart_Id,\r\n            MakerName: this.form.issueName,\r\n            VehicleNo: this.form.License,\r\n            DriverName: this.form.Contact_UserName,\r\n            Telephone: this.form.Mobile,\r\n            SendDate: parseTime(this.form.Out_Date, '{y}-{m}-{d} {h}:{i}:{s}'),\r\n            Remarks: this.form.Remarks,\r\n            ProjectName: this.form.projectName,\r\n            TypeId: this.ProfessionalType[0].Code,\r\n            Address: this.form.Address,\r\n            Logistics_Fee: this.form.Logistics_Fee,\r\n            Contract_Id: this.form.Contract_Id,\r\n            Loadings: this.form.Loadings,\r\n            LoadingsPersonnel: this.form.LoadingsPersonnel,\r\n            Trips: this.form.Trips,\r\n            ReceivingUnit: this.form.ReceivingUnit,\r\n          },\r\n          projectSendingInfo_Item: [],\r\n          PartList: []\r\n        }\r\n        // 只获取新的表格数据\r\n        const tempData = [...this.tbData, ...this.tbData2, ...this.tbData3, ...this.tbData4]\r\n        tempData.filter((item) => {\r\n          if (item.PackageSn) {\r\n            const {\r\n              Id,\r\n              Stock_Count,\r\n              PackageSn,\r\n              Warehouse_Id,\r\n              Location_Id,\r\n              Netweight,\r\n              AllWeight,\r\n              DIM,\r\n              Volume,\r\n              AllAmount,\r\n              Import_Detail_Id,\r\n              Model_Ids,\r\n              isOld\r\n            } = item\r\n            const tempItem = {\r\n              PackageSn: PackageSn || '',\r\n              SteelAmount: Stock_Count || 1,\r\n              Warehouse_Id: Warehouse_Id || '',\r\n              Location_Id: Location_Id || '',\r\n              SteelWeight: Netweight || '',\r\n              Import_Detail_Id,\r\n              Model_Ids,\r\n              DIM,\r\n              Volume,\r\n              AllWeight,\r\n              AllAmount,\r\n              isOld\r\n            }\r\n            if (!isOld) {\r\n              submitObj.projectSendingInfo_Item.push(tempItem)\r\n            }\r\n          } else if (item.Code) {\r\n            const {\r\n              Id,\r\n              S_Count,\r\n              Stock_Count,\r\n              Warehouse_Id,\r\n              Import_Detail_Id,\r\n              Model_Ids,\r\n              Location_Id,\r\n              Netweight,\r\n              AllWeight,\r\n              isOld\r\n            } = item\r\n            const tempItem = {\r\n              Component_Id: Id || '',\r\n              SteelAmount: S_Count || '',\r\n              Warehouse_Id: Warehouse_Id || '',\r\n              Location_Id: Location_Id || '',\r\n              SteelWeight: Netweight || '',\r\n              Import_Detail_Id,\r\n              Model_Ids,\r\n              isOld,\r\n              AllWeight\r\n            }\r\n            if (!isOld) {\r\n              delete tempItem.isOld\r\n              submitObj.projectSendingInfo_Item.push(tempItem)\r\n            }\r\n          } else if (item.Part_Code) {\r\n            const {\r\n              Part_Produced_Id,\r\n              Part_Code,\r\n              Area_Name,\r\n              InstallUnit_Name,\r\n              S_Count,\r\n              Spec,\r\n              Length,\r\n              Weight,\r\n              Part_Grade\r\n            } = item\r\n            const tempItem = {\r\n              Part_Produced_Id: Part_Produced_Id,\r\n              Part_Code,\r\n              Area_Name,\r\n              InstallUnit_Name,\r\n              Amount: S_Count,\r\n              Spec,\r\n              Length,\r\n              Weight,\r\n              Part_Grade\r\n            }\r\n            submitObj.PartList.push(tempItem)\r\n          }\r\n        })\r\n\r\n        this.btnLoading = true\r\n        if (this.isEdit) {\r\n          // 获取更新后的表单数据\r\n          // submitObj.entity.Id = this.$route.query.id;\r\n          this.plm_ProjectSendingInfo.Code = this.form.receiveNum\r\n          this.plm_ProjectSendingInfo.Consignee = this.form.receiveName\r\n          this.plm_ProjectSendingInfo.ConsigneeTel = this.form.Receiver_Tel\r\n          this.plm_ProjectSendingInfo.VehicleNo = this.form.License\r\n          this.plm_ProjectSendingInfo.DriverName = this.form.Contact_UserName\r\n          this.plm_ProjectSendingInfo.Telephone = this.form.Mobile\r\n          this.plm_ProjectSendingInfo.Depart_Id = this.form.Depart_Id\r\n          this.plm_ProjectSendingInfo.MakerName = this.form.issueName\r\n          this.plm_ProjectSendingInfo.Address = this.form.Address\r\n          this.plm_ProjectSendingInfo.Contract_Id = this.form.Contract_Id\r\n          this.plm_ProjectSendingInfo.Logistics_Fee = this.form.Logistics_Fee\r\n          this.plm_ProjectSendingInfo.Loadings = this.form.Loadings\r\n          this.plm_ProjectSendingInfo.LoadingsPersonnel = this.form.LoadingsPersonnel\r\n          this.plm_ProjectSendingInfo.Trips = this.form.Trips\r\n          this.plm_ProjectSendingInfo.ReceivingUnit = this.form.ReceivingUnit\r\n          this.plm_ProjectSendingInfo.SendDate = parseTime(\r\n            this.form.Out_Date,\r\n            '{y}-{m}-{d} {h}:{i}:{s}'\r\n          )\r\n          this.plm_ProjectSendingInfo.Remarks = this.form.Remarks\r\n\r\n          const formAttachment = []\r\n          if (this.fileListArr.length > 0) {\r\n            this.fileListArr.forEach(item => {\r\n              formAttachment.push(\r\n                item.response && item.response.encryptionUrl\r\n                  ? item.response.encryptionUrl\r\n                  : item.encryptionUrl\r\n              )\r\n            })\r\n          }\r\n          this.plm_ProjectSendingInfo.Attachment = formAttachment.toString()\r\n          submitObj.plm_ProjectSendingInfo = this.plm_ProjectSendingInfo\r\n          // 获取新的表格数据\r\n          // submitObj.projectSendingInfo_Item =\r\n          //   submitObj.projectSendingInfo_Item.filter((item) => {\r\n          //     return !item.isOld;\r\n          //   });\r\n          // 添加新老表格数据\r\n          submitObj.projectSendingInfo_Item = [\r\n            ...this.Itemdetail,\r\n            ...this.PackagesList,\r\n            ...submitObj.projectSendingInfo_Item\r\n          ]\r\n          if (submitObj.projectSendingInfo_Item.length === 0 && submitObj.PartList.length === 0) {\r\n            this.$message({\r\n              message: '不能保存空发货单',\r\n              type: 'error'\r\n            })\r\n          } else {\r\n            this.loading = true\r\n            EditProjectSendingInfo(submitObj).then((res) => {\r\n              if (res.IsSucceed) {\r\n                this.$message({\r\n                  message: '编辑成功',\r\n                  type: 'success'\r\n                })\r\n                closeTagView(this.$store, this.$route)\r\n                this.$router.replace({\r\n                  name: 'PROShipSent',\r\n                  query: {\r\n                    refresh: 1\r\n                  }\r\n                })\r\n              } else {\r\n                this.isClicked = false\r\n                this.$message({\r\n                  message: res.Message,\r\n                  type: 'error'\r\n                })\r\n              }\r\n              this.loading = false\r\n              this.btnLoading = false\r\n            })\r\n          }\r\n        } else {\r\n          if (submitObj.projectSendingInfo_Item.length === 0 && submitObj.PartList.length === 0) {\r\n            this.$message({\r\n              message: '不能保存空发货单',\r\n              type: 'error'\r\n            })\r\n          } else {\r\n            console.log('submitObj', submitObj)\r\n            this.loading = true\r\n            AddProjectSendingInfo(submitObj).then((res) => {\r\n              if (res.IsSucceed) {\r\n                this.$message({\r\n                  message: '添加成功',\r\n                  type: 'success'\r\n                })\r\n                closeTagView(this.$store, this.$route)\r\n                this.$router.replace({\r\n                  name: 'PROShipSent',\r\n                  query: {\r\n                    refresh: 1\r\n                  }\r\n                })\r\n              } else {\r\n                this.isClicked = false\r\n                this.$message({\r\n                  message: res.Message,\r\n                  type: 'error'\r\n                })\r\n              }\r\n              this.btnLoading = false\r\n              this.loading = false\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    async getStopList(list, key) {\r\n      const submitObj = list.map(item => {\r\n        return {\r\n          Id: item[key],\r\n          Type: this.tabTypeCode === TAB_TYPE.com ? 2 : this.tabTypeCode === TAB_TYPE.unitPart ? 3 : 1 // 1：零件，3：部件，2：构件\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = !!item.Is_Stop\r\n          })\r\n          list.forEach(row => {\r\n            if (stopMap[row[key]]) {\r\n              this.$set(row, 'stopFlag', stopMap[row[key]])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async getInfo() {\r\n      await GetProjectsendinginEntity({\r\n        Id: this.$route.query.id\r\n      }).then(async(res) => {\r\n        if (res.IsSucceed) {\r\n          this.plm_ProjectSendingInfo = res.Data.Plm_ProjectSendingInfo\r\n          this.Itemdetail = res.Data.Itemdetail\r\n          this.PackagesList = res.Data.PackagesList\r\n          this.PartList = res.Data.PartList\r\n          this.weightform = res.Data.WeightInfo\r\n          this.pageStatus = this.plm_ProjectSendingInfo.Status\r\n          this.isDraft = this.plm_ProjectSendingInfo?.Status === 0\r\n\r\n          const {\r\n            Id,\r\n            Code,\r\n            ProjectId,\r\n            ProjectName,\r\n            Consignee,\r\n            ConsigneeTel,\r\n            Depart_Id,\r\n            MakerName,\r\n            VehicleNo,\r\n            DriverName,\r\n            Telephone,\r\n            Address,\r\n            Attachment,\r\n            SendDate,\r\n            Remarks,\r\n            Number,\r\n            Logistics_Fee,\r\n            Contract_Id,\r\n            Loadings,\r\n            LoadingsPersonnel,\r\n            Trips,\r\n            ReceivingUnit,\r\n\r\n          } = this.plm_ProjectSendingInfo\r\n\r\n          this.form.ProjectId = ProjectId\r\n          this.form.receiveNum = Code\r\n          this.form.projectName = ProjectName\r\n          this.form.receiveName = Consignee\r\n          this.form.Receiver_Tel = ConsigneeTel\r\n          this.form.Depart_Id = Depart_Id\r\n          this.form.issueName = MakerName\r\n          this.form.License = VehicleNo\r\n          this.form.Contact_UserName = DriverName\r\n          this.form.Mobile = Telephone\r\n          this.form.Address = Address\r\n          this.form.Out_Date = new Date(SendDate)\r\n          this.form.Remarks = Remarks\r\n          this.form.Logistics_Fee = Logistics_Fee || undefined\r\n          this.form.Contract_Id = Contract_Id\r\n          this.form.Loadings = Loadings\r\n          this.form.LoadingsPersonnel = LoadingsPersonnel\r\n          this.form.Trips = Trips\r\n          this.form.ReceivingUnit = ReceivingUnit\r\n          this.sendNumber = Number\r\n\r\n          if (VehicleNo && this.carOptions.every((v) => v.License !== VehicleNo)) {\r\n            this.carOptions.push({\r\n              License: VehicleNo,\r\n              Contact_UserName: DriverName,\r\n              Mobile: Telephone,\r\n              detail: VehicleNo ? `${VehicleNo}(${DriverName} ${Telephone})` : ''\r\n            })\r\n          }\r\n\r\n          if (Attachment) {\r\n            const AttachmentArr = Attachment.split(',')\r\n            AttachmentArr.forEach(item => {\r\n              const fileUrl =\r\n                item.indexOf('?Expires=') > -1\r\n                  ? item.substring(0, item.lastIndexOf('?Expires='))\r\n                  : item\r\n              const fileName = decodeURI(fileUrl.substring(fileUrl.lastIndexOf('/') + 1))\r\n              const AttachmentJson = {}\r\n              AttachmentJson.name = decodeURIComponent(fileName)\r\n              AttachmentJson.url = fileUrl\r\n              AttachmentJson.encryptionUrl = fileUrl\r\n              this.fileListData.push(AttachmentJson)\r\n              this.fileListArr.push(AttachmentJson)\r\n            })\r\n          }\r\n\r\n          if (this.weightform.Attachment_Weight) {\r\n            const imgPromiseAll2 = this.weightform.Attachment_Weight.split(',').map(async url => {\r\n              const fileUrl = url.split('?')[0]\r\n              return {\r\n                url: await this.handleUrl(fileUrl),\r\n                name: getFileNameFromUrl(fileUrl)\r\n              }\r\n            })\r\n            this.weightFileInfo = await Promise.all(imgPromiseAll2)\r\n          }\r\n\r\n          this.Itemdetail.forEach((item, index) => {\r\n            const {\r\n              Component_Id,\r\n              S_Count,\r\n              SteelWeight,\r\n              AllWeight,\r\n              Name,\r\n              Spec,\r\n              Length,\r\n              WarehouseName,\r\n              Code,\r\n              LocationName,\r\n              Area_Name,\r\n              Wait_Stock_Count,\r\n              Import_Detail_Id,\r\n              Location_Id,\r\n              SerialNumber\r\n            } = item\r\n            const tempItem = {\r\n              Id: Component_Id,\r\n              Area_Name: Area_Name,\r\n              Name,\r\n              Spec,\r\n              Length,\r\n              WarehouseName,\r\n              Code,\r\n              LocationName,\r\n              Import_Detail_Id,\r\n              Location_Id,\r\n              S_Count: S_Count,\r\n              Wait_Stock_Count: Wait_Stock_Count,\r\n              Netweight: SteelWeight,\r\n              AllWeight: AllWeight,\r\n              isOld: true,\r\n              SerialNumber: SerialNumber\r\n            }\r\n            this.tbData.push(tempItem)\r\n            this.old_Component_Ids.push(Component_Id)\r\n            this.getStopList(this.tbData, 'Component_Id')\r\n          })\r\n          this.PartList.forEach((element, idx) => {\r\n            const tempItem = { ...element }\r\n            tempItem.S_Count = tempItem.Amount\r\n            tempItem.Netweight = tempItem.Weight\r\n            tempItem.AllWeight = this.getAllWeight(tempItem)\r\n            tempItem.Total_Weight = numeral(tempItem.Stock_Count).multiply(tempItem.Weight).value()\r\n            if (tempItem.Part_Grade > 0) {\r\n              this.tbData3.push(tempItem)\r\n              this.getStopList(this.tbData3, 'Part_Aggregate_Id')\r\n              console.log('this.tbData3', this.tbData3)\r\n            } else {\r\n              this.tbData4.push(tempItem)\r\n              this.getStopList(this.tbData4, 'Part_Aggregate_Id')\r\n              console.log('this.tbData4', this.tbData4)\r\n            }\r\n          })\r\n\r\n          this.PackagesList.forEach((item, index) => {\r\n            const {\r\n              PkgNO,\r\n              PackageSn,\r\n              AllWeight,\r\n              Volume,\r\n              AllAmount,\r\n              WarehouseName,\r\n              LocationName,\r\n              DIM,\r\n              PackageId\r\n            } = item\r\n            const tempItem = {\r\n              PkgNO: PkgNO,\r\n              PackageSn: PackageSn,\r\n              AllWeight,\r\n              AllAmount,\r\n              Volume,\r\n              WarehouseName,\r\n              LocationName,\r\n              isOld: true,\r\n              DIM: DIM,\r\n              PackageId\r\n            }\r\n            this.tbData2.push(tempItem)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    checkSelectable(row) {\r\n      return !row.stopFlag\r\n    },\r\n    async handleUrl(url) {\r\n      const { Data } = await GetOssUrl({ url })\r\n      return Data\r\n    },\r\n    carChange(val) {\r\n      if (!val) {\r\n        this.form.Contact_UserName = ''\r\n        this.form.Mobile = ''\r\n        this.form.License = ''\r\n        return\r\n      }\r\n      const item = this.carOptions.find((v) => v.License === this.form.License)\r\n      this.form.Contact_UserName = item.Contact_UserName\r\n      this.form.Mobile = item.Mobile\r\n      this.form.License = item.License\r\n    },\r\n    projectIdChange(e) {\r\n      // if (e) {\r\n      //   this.getAreaList();\r\n      // }\r\n    },\r\n    projectIdClear(e) {\r\n      // this.$refs.form2.resetFields();\r\n    },\r\n    getAllWeight(item) {\r\n      return Number(item.S_Count * item.Netweight).toFixed(2) / 1\r\n    },\r\n    addSelectList(list) {\r\n      console.log(list, 'list')\r\n      console.log(this.tabTypeCode)\r\n      if (this.tabTypeCode === TAB_TYPE.com) {\r\n        console.log(11)\r\n        list.forEach((item) => {\r\n          item.AllWeight = this.getAllWeight(item)\r\n\r\n          this.tbData.push(item)\r\n        })\r\n        this.tbData = JSON.parse(JSON.stringify(this.tbData))\r\n        this.total = this.tbData.length\r\n      } else if (this.tabTypeCode === TAB_TYPE.unitPart) {\r\n        list.forEach((item) => {\r\n          item.AllWeight = this.getAllWeight(item)\r\n          this.tbData3.push(item)\r\n        })\r\n        // dont ask why just cv\r\n        this.tbData3 = JSON.parse(JSON.stringify(this.tbData3))\r\n        this.total = this.tbData3.length\r\n      } else if (this.tabTypeCode === TAB_TYPE.part) {\r\n        list.forEach((item) => {\r\n          item.AllWeight = this.getAllWeight(item)\r\n          this.tbData4.push(item)\r\n        })\r\n        this.tbData4 = JSON.parse(JSON.stringify(this.tbData4))\r\n        this.total = this.tbData4.length\r\n      } else if (this.tabTypeCode === TAB_TYPE.package) {\r\n        list.forEach((item) => {\r\n          this.tbData2.push(item)\r\n        })\r\n        this.total = this.tbData2.length\r\n      }\r\n    },\r\n    handleDelete() {\r\n      this.$confirm('删除该数据, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          if (this.Is_Pack) {\r\n            this.selectList.forEach((item) => {\r\n              const index = this.tbData2.findIndex(\r\n                (v) => v.PackageSn === item.PackageSn\r\n              )\r\n              index !== -1 && this.tbData2.splice(index, 1)\r\n              if (this.isEdit) {\r\n                const index = this.PackagesList.findIndex(\r\n                  (v) => v.PackageSn === item.PackageSn\r\n                )\r\n                index !== -1 && this.PackagesList.splice(index, 1)\r\n              }\r\n            })\r\n          } else {\r\n            let _table = null\r\n            if (this.tabTypeCode === TAB_TYPE.com) {\r\n              _table = this.tbData\r\n              this.selectList.forEach((item) => {\r\n                const index = _table.findIndex((v) => v.Import_Detail_Id + v.Location_Id === item.Import_Detail_Id + item.Location_Id)\r\n                index !== -1 && _table.splice(index, 1)\r\n                if (this.isEdit) {\r\n                  const index = this.Itemdetail.findIndex(\r\n                    (v) => v.Component_Id === item.Id\r\n                  )\r\n                  index !== -1 && this.Itemdetail.splice(index, 1)\r\n                }\r\n              })\r\n            } else if (this.tabTypeCode === TAB_TYPE.unitPart) {\r\n              _table = this.tbData3\r\n              this.selectList.forEach((item) => {\r\n                const index = _table.findIndex((v) => v.Part_Produced_Id === item.Part_Produced_Id)\r\n                index !== -1 && _table.splice(index, 1)\r\n              })\r\n            } else if (this.tabTypeCode === TAB_TYPE.part) {\r\n              _table = this.tbData4\r\n              this.selectList.forEach((item) => {\r\n                const index = _table.findIndex((v) => v.Part_Produced_Id === item.Part_Produced_Id)\r\n                index !== -1 && _table.splice(index, 1)\r\n              })\r\n            }\r\n          }\r\n          this.$message({\r\n            type: 'success',\r\n            message: '删除成功!'\r\n          })\r\n        })\r\n        .catch(() => {})\r\n    },\r\n    multiSelectedChange(v) {\r\n      this.selectList = v\r\n    },\r\n    fetchData() {\r\n      // GetStockOutDetailList({ stockOutId: this.$route.query.id }).then(res => {\r\n      //   if (res.IsSucceed) {\r\n      //     this.tbData = res.Data\r\n      //     this.tbData.forEach((element, idx) => {\r\n      //       this.$set(element, 'UniqueCodesArray', element.UniqueCodes.split(','))\r\n      //     })\r\n      //   } else {\r\n      //     this.$message({\r\n      //       message: res.Message,\r\n      //       type: 'error'\r\n      //     })\r\n      //   }\r\n      //   this.tbLoading = false\r\n      // })\r\n    },\r\n    getTotal() {\r\n      // this.$nextTick(_ => {\r\n      //   const columns = this.$refs.dyTable.$refs.dtable.columns\r\n      //   columns.forEach((element, idx) => {\r\n      //     if (idx === 0) {\r\n      //       this.sums[0] = '合计'\r\n      //     } else if (element.property === 'Out_Count') {\r\n      //       const v = this.tbData.reduce((acc, cur) => {\r\n      //         return this.highPrecisionAdd(acc, cur[element.property])\r\n      //       }, 0)\r\n      //       this.$set(this.sums, idx, v)\r\n      //     } else if (element.property === 'NetWeight') {\r\n      //       const v = this.tbData.reduce((acc, cur) => {\r\n      //         return this.highPrecisionAdd(acc, cur[element.property] * cur['Out_Count'])\r\n      //       }, 0)\r\n      //       this.$set(this.sums, idx, numeral(v).divide(1000).format('0.[00]') + '（t）')\r\n      //     } else {\r\n      //       this.$set(this.sums, idx, '')\r\n      //     }\r\n      //   })\r\n      // })\r\n    },\r\n    getAllCarList() {\r\n      return new Promise((resolve) => {\r\n        GetCurCarPageList({}).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.carOptions = res.Data\r\n            this.carOptions.forEach((element, idx) => {\r\n              this.$set(\r\n                element,\r\n                'detail',\r\n                `${element.License}(${element.Contact_UserName} ${element.Mobile})`\r\n              )\r\n            })\r\n            resolve()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    addCarData(data) {\r\n      this.getAllCarList()\r\n      // this.carOptions.push(data);\r\n    },\r\n    handleAdd() {\r\n      this.currentComponent = 'AddDialog'\r\n      this.width = '80%'\r\n      this.topDialog = '1vh'\r\n      this.dialogVisible = true\r\n      if (this.Is_Pack === false) {\r\n        let _table = null\r\n        if (this.tabTypeCode === TAB_TYPE.com) {\r\n          _table = this.tbData\r\n          this.title = '添加构件'\r\n        } else if (this.tabTypeCode === TAB_TYPE.unitPart) {\r\n          _table = this.tbData3\r\n          this.title = '添加部件'\r\n        } else if (this.tabTypeCode === TAB_TYPE.part) {\r\n          _table = this.tbData4\r\n          this.title = '添加零件'\r\n        }\r\n\r\n        const tempData1 = _table.filter((item) => {\r\n          return !item.isOld\r\n        })\r\n        this.$nextTick((_) => {\r\n          this.$refs.content.init(tempData1, _table)\r\n        })\r\n      } else if (this.Is_Pack === true) {\r\n        this.title = '添加打包件'\r\n        const tempData2 = this.tbData2.filter((item) => {\r\n          return !item.isOld\r\n        })\r\n        this.$nextTick((_) => {\r\n          this.$refs.content.init(tempData2, this.tbData2)\r\n        })\r\n      }\r\n    },\r\n    // 打包件详情\r\n    handleDetail(row) {\r\n      console.log(row, 'id===')\r\n      this.currentComponent = 'packDetail'\r\n      this.width = '60%'\r\n      this.title = '打包件详情'\r\n      this.topDialog = '10vh'\r\n      this.dialogVisible = true\r\n      this.$nextTick((_) => {\r\n        this.$refs.content.init(row.PackageId || row.Id)\r\n      })\r\n    },\r\n    handleEditCar() {\r\n      this.currentComponent = 'CarDialog'\r\n      this.title = '新增车辆'\r\n      this.topDialog = '10vh'\r\n      this.dialogVisible = true\r\n    },\r\n    close() {\r\n      this.dialogVisible = false\r\n    },\r\n    handleInfo(row) {\r\n      this.$refs.info.handleOpen(row)\r\n    },\r\n    getTableConfig(code) {\r\n      return new Promise((resolve) => {\r\n        GetGridByCode({\r\n          code\r\n        }).then((res) => {\r\n          const { IsSucceed, Data, Message } = res\r\n          if (IsSucceed) {\r\n            if (!Data) {\r\n              this.$message({\r\n                message: '表格配置不存在',\r\n                type: 'error'\r\n              })\r\n              return\r\n            }\r\n            this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n            this.columns = (\r\n              Data.ColumnList.filter((v) => v.Is_Display) || []\r\n            ).map((item) => {\r\n              item.Is_Resizable = true\r\n              item.Is_Sortable = true\r\n              return item\r\n            })\r\n            this.form.PageInfo.PageSize = +Data.Grid.Row_Number\r\n            if (this.isSub) {\r\n              this.tbConfig.Is_Select = false\r\n              this.tbConfig.Is_Row_Number = true\r\n            }\r\n            resolve(this.columns)\r\n          } else {\r\n            this.$message({\r\n              message: Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    uploadSuccess(response, file, fileList) {\r\n      console.log('fileList', fileList)\r\n      this.fileListArr = JSON.parse(JSON.stringify(fileList))\r\n    },\r\n    uploadRemove(file, fileList) {\r\n      this.fileListArr = JSON.parse(JSON.stringify(fileList))\r\n    },\r\n    async handlePreview(file) {\r\n      console.log('file', file)\r\n      let encryptionUrl = ''\r\n      if (file.response && file.response.encryptionUrl) {\r\n        encryptionUrl = file.response.encryptionUrl\r\n      } else {\r\n        encryptionUrl = await GetOssUrl({ url: file.encryptionUrl })\r\n        encryptionUrl = encryptionUrl.Data\r\n      }\r\n      window.open(encryptionUrl)\r\n    },\r\n    handleExceed() {\r\n      this.$message({\r\n        type: 'warning',\r\n        message: '附件数量不能超过10个'\r\n      })\r\n    },\r\n    async getContractList() {\r\n      try {\r\n        const submitData = {\r\n          ContractTypeCode: 3,\r\n          ProjectIds: []\r\n        }\r\n        if (this.form.ProjectId) {\r\n          submitData.ProjectIds.push(this.form.ProjectId)\r\n        }\r\n        const res = await GetContractList(submitData)\r\n        if (res.IsSucceed) {\r\n          this.contractOptions = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      } catch (error) {\r\n        console.error('获取合同列表失败:', error)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.tb-status {\r\n  background: #fae6bb;\r\n  padding: 16px 20px;\r\n  font-size: 1.2em;\r\n  font-weight: bold;\r\n  display: flex;\r\n\r\n  * {\r\n    margin-right: 12px;\r\n  }\r\n}\r\n\r\n.el-form {\r\n  margin: 16px 10px;\r\n}\r\n\r\n.title {\r\n  margin-left: 10px;\r\n}\r\n\r\n.cs-red{\r\n  color:red\r\n}\r\n\r\n.cs-tree-x {\r\n  ::v-deep {\r\n    .el-select {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n\r\n.statistics-container {\r\n  display: flex;\r\n  .statistics-item {\r\n    margin-right: 32px;\r\n    span:first-child {\r\n      display: inline-block;\r\n      font-size: 14px;\r\n      line-height: 18px;\r\n      font-weight: 500;\r\n      color: #999999;\r\n      // margin-right: 16px;\r\n    }\r\n    span:last-child {\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n      color: #00c361;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\part-list\\v4\\component\\BatchEditor.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\part-list\\v4\\component\\BatchEditor.vue", "mtime": 1758266753107}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["BatchEditor.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BatchEditor.vue", "sourceRoot": "src/views/PRO/part-list/v4/component", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-row v-for=\"(info, index) in list\" :key=\"info.id\" class=\"item-x\">\r\n      <div class=\"item\">\r\n        <label>\r\n          属性名称\r\n          <el-select\r\n            v-model=\"info.key\"\r\n            style=\"width: calc(100% - 65px)\"\r\n            clearable\r\n            placeholder=\"请选择\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in filterOption(info.key)\"\r\n              :key=\"item.key\"\r\n              :label=\"item.label\"\r\n              :value=\"item.key\"\r\n            />\r\n          </el-select>\r\n        </label>\r\n      </div>\r\n      <div class=\"item\" style=\"line-height: 32px\">\r\n        <label>请输入值\r\n          <el-input-number\r\n            v-if=\"checkType(info.key, 'number')\"\r\n            v-model=\"info.val\"\r\n            :min=\"0\"\r\n            class=\"cs-number-btn-hidden\"\r\n          />\r\n          <el-input v-if=\"checkType(info.key, 'string')\" v-model=\"info.val\" />\r\n          <el-select\r\n            v-if=\"checkType(info.key, 'array') && info.key === 'Is_Main'\"\r\n            v-model=\"info.val\"\r\n            clearable\r\n            placeholder=\"请选择\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in Is_Main_Data\"\r\n              :key=\"item.Id\"\r\n              :label=\"item.Name\"\r\n              :value=\"item.Name\"\r\n            />\r\n          </el-select>\r\n          <!-- <el-tree-select\r\n            v-show=\"checkType(info.key, 'array') && info.key === 'AreaPosition'\"\r\n            ref=\"treeSelect\"\r\n            v-model=\"info.val\"\r\n            :tree-params=\"treeParams\"\r\n            style=\"width: 100%; display: inline-block\"\r\n          /> -->\r\n        </label>\r\n      </div>\r\n      <span v-if=\"index === 0\" class=\"item-span\">\r\n        <i class=\"el-icon-circle-plus-outline\" @click=\"handleAdd\" />\r\n      </span>\r\n      <span v-else class=\"item-span\">\r\n        <i class=\"el-icon-circle-plus-outline\" @click=\"handleAdd\" />\r\n        <i\r\n          class=\"el-icon-remove-outline txt-red\"\r\n          @click=\"handleDelete(index)\"\r\n        />\r\n      </span>\r\n    </el-row>\r\n    <div style=\"text-align: right; width: 100%; padding: 20px 2% 0 0\">\r\n      <el-button @click=\"$emit('close')\">取消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"onSubmit\">确定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { EditPartpagelist } from '@/api/plm/production'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { convertCode } from '@/utils/multi-specialty'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { GetUserableAttr } from '@/api/PRO/professionalType'\r\nexport default {\r\n  props: {\r\n    typeEntity: {\r\n      type: Object,\r\n      default: () => {}\r\n    },\r\n    AreaId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    ProjectId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      treeParams: {\r\n        'default-expand-all': true,\r\n        filterable: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      value: '',\r\n      options: [\r\n        {\r\n          key: 'Spec',\r\n          label: '规格',\r\n          type: 'string'\r\n        },\r\n        {\r\n          key: 'Length',\r\n          label: '长度',\r\n          type: 'number'\r\n        },\r\n        {\r\n          key: 'Texture',\r\n          label: '材质',\r\n          type: 'string'\r\n        },\r\n        // {\r\n        //   key: 'Num',\r\n        //   label: '深化数量',\r\n        //   type: 'number'\r\n        // },\r\n        {\r\n          key: 'Weight',\r\n          label: '单重',\r\n          type: 'number'\r\n        },\r\n        {\r\n          key: 'Shape',\r\n          label: '形状',\r\n          type: 'string'\r\n        },\r\n        // {\r\n        //   key: \"Component_Code\",\r\n        //   label: \"所属构件 \",\r\n        //   type: \"string\",\r\n        // },\r\n        {\r\n          key: 'Is_Main',\r\n          label: '是否主零件',\r\n          type: 'array'\r\n        },\r\n        {\r\n          key: 'Times',\r\n          label: '单数',\r\n          type: 'number'\r\n        },\r\n        {\r\n          key: 'Remark',\r\n          label: '备注',\r\n          type: 'string'\r\n        }\r\n      ],\r\n      list: [\r\n        {\r\n          id: uuidv4(),\r\n          val: undefined,\r\n          key: ''\r\n        }\r\n      ],\r\n      Is_Main_Data: [{ Name: '是', Id: true }, { Name: '否', Id: false }]\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.getUserableAttr()\r\n    const codeArr = this.options.filter((item, index) => index).map(i => i.key)\r\n    const columns = await this.convertCode(\r\n      this.typeEntity.Code,\r\n      codeArr,\r\n      'plm_parts_page_list'\r\n    )\r\n    console.log(columns)\r\n    this.options = this.options.map((item, index) => {\r\n      if (index) {\r\n        item.label = columns.filter((v) => v.Is_Display).find((i) => i.Code === item.key)?.Display_Name\r\n      }\r\n      return item\r\n    })\r\n\r\n    this.options = [...this.options]\r\n    console.log({ columns })\r\n    console.log(this.AreaId)\r\n  },\r\n  methods: {\r\n    // 获取拓展字段\r\n    async getUserableAttr() {\r\n      await GetUserableAttr({\r\n        IsComponent: false,\r\n        Bom_Level: 0\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const resData = res.Data\r\n          const expandData = []\r\n          resData.forEach(item => {\r\n            const expandJson = {}\r\n            expandJson.key = item.Code\r\n            expandJson.lable = item.Display_Name\r\n            expandJson.type = 'string'\r\n            expandData.push(expandJson)\r\n          })\r\n          this.options = this.options.concat(expandData)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    init(list, columnsOption) {\r\n      this.selectList = list\r\n      console.log(list)\r\n      const arr = list.filter(item => item.Component_Code !== null && item.Component_Code !== '')\r\n      console.log(arr)\r\n      this.options = arr.length > 0 ? this.options.filter(v => v.key != 'Num') : this.options\r\n      //  let filterarr = columnsOption.filter(v=> {\r\n      //   return v.Display_Name != \"项目名称\" && v.Display_Name != \"区域\" && v.Display_Name != \"批次\" && v.Code != \"Total_Weight\" &&  v.Code != \"Code\" && v.Code != \"Times\" && v.Code != \"Schduling_Count\"\r\n      //  })\r\n      //  this.options = filterarr?.map(item => ({ key: item.Code, label: item.Display_Name, type: item.Code === \"Is_Main\"?\"array\": item.Code === \"Num\" || item.Code === \"Schduling_Count\" || item.Code === \"Weight\" || item.Code === \"Times\" || item.Code === \"Length\" ? \"number\" : \"string\"}))\r\n    },\r\n    handleAdd() {\r\n      this.list.push({\r\n        id: uuidv4(),\r\n        val: undefined,\r\n        key: ''\r\n      })\r\n    },\r\n    handleDelete(index) {\r\n      this.list.splice(index, 1)\r\n    },\r\n    async onSubmit() {\r\n      this.btnLoading = true\r\n      const Keysmodel = []\r\n      for (let i = 0; i < this.list.length; i++) {\r\n        console.log(this.list)\r\n        const obj = {}\r\n        const element = this.list[i]\r\n        console.log(element)\r\n        if (!element.val) {\r\n          if (element.key === 'Length' || element.key === 'Num' || element.key === 'Weight' || element.key === 'Times') {\r\n            element.val === 0 ? this.$message({ message: '值不能为0', type: 'warning' }) : this.$message({ message: '值不能为空', type: 'warning' })\r\n          } else {\r\n            this.$message({\r\n              message: '值不能为空',\r\n              type: 'warning'\r\n            })\r\n          }\r\n          this.btnLoading = false\r\n          return\r\n        }\r\n        obj.code = element.key\r\n        obj.value = element.val\r\n        Keysmodel.push(obj)\r\n        console.log(Keysmodel)\r\n      }\r\n      await EditPartpagelist({\r\n        Ids: this.selectList.map((v) => v.Part_Aggregate_Id).toString(),\r\n        Keysmodel,\r\n        Area_Id: this.AreaId,\r\n        Project_Id: this.ProjectId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '修改成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('close')\r\n          this.$emit('refresh')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.btnLoading = false\r\n      })\r\n    },\r\n    filterOption(currentValue) {\r\n      console.log(currentValue)\r\n      return this.options.filter((k) => {\r\n        return (\r\n          (!this.list.map((v) => v.key).includes(k.key) ||\r\n            k.key === currentValue) &&\r\n          k.label\r\n        )\r\n      })\r\n    },\r\n\r\n    checkType(key, type) {\r\n      if (!key) return false\r\n      return this.options.find((v) => v.key === key).type === type\r\n    },\r\n\r\n    // 获取配置数据\r\n    async getColumnConfiguration(code, mainType = 'plm_parts_page_list') {\r\n      const res = await GetGridByCode({ code: mainType + ',' + code })\r\n      return res.Data.ColumnList\r\n    },\r\n\r\n    // 根据Code（数据）获取名称\r\n    async convertCode(typeCode, propsArr = [], mainType) {\r\n      const props = await this.getColumnConfiguration(typeCode, mainType)\r\n      console.log(props)\r\n      const columns = props.filter(i => {\r\n        const arr = propsArr.map(i => i.toLowerCase())\r\n        return arr.includes(i.Code.toLowerCase())\r\n      })\r\n      console.log(columns)\r\n      return columns\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n[class^=\"el-icon\"] {\r\n  font-size: 24px;\r\n  vertical-align: middle;\r\n  cursor: pointer;\r\n  margin-left: 15px;\r\n}\r\n\r\n.item-x {\r\n  display: flex;\r\n  margin-bottom: 20px;\r\n  flex: 0 1 50%;\r\n  justify-content: space-between;\r\n\r\n  .item {\r\n    width: 45%;\r\n    white-space: nowrap;\r\n    &:not(:first-of-type) {\r\n      margin-left: 20px;\r\n      .cs-number-btn-hidden,\r\n      .el-input,\r\n      .el-select {\r\n        width: 80%;\r\n      }\r\n    }\r\n  }\r\n\r\n  .item-span {\r\n    width: 90px;\r\n    padding-top: 5px;\r\n  }\r\n}\r\n::v-deep {\r\n  .el-tree-select-input {\r\n    width: 80% !important;\r\n  }\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\compRecognitionConfig.vue?vue&type=style&index=0&id=34af80f7&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\compRecognitionConfig.vue", "mtime": 1745557754679}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQpAaW1wb3J0ICJ+QC9zdHlsZXMvbWl4aW4uc2NzcyI7DQouZm9ybS13cmFwcGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgbWF4LWhlaWdodDogNzB2aDsNCiAgLmZvcm0teHsNCiAgICBvdmVyZmxvdzogYXV0bzsNCiAgICBwYWRkaW5nLXJpZ2h0OiAxNnB4Ow0KICAgIEBpbmNsdWRlIHNjcm9sbEJhcjsNCiAgfQ0KICAuYnRuLXggew0KICAgIHBhZGRpbmctdG9wOiAxNnB4Ow0KICAgIHRleHQtYWxpZ246IHJpZ2h0Ow0KICB9DQoNCn0NCg=="}, {"version": 3, "sources": ["compRecognitionConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6JA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "compRecognitionConfig.vue", "sourceRoot": "src/views/PRO/project-config/process-settings/component", "sourcesContent": ["<template>\r\n  <div class=\"form-wrapper\">\r\n    <div class=\"form-x\">\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"120px\">\r\n        <el-form-item label=\"是否启用\" prop=\"enable\">\r\n          <el-radio-group v-model=\"form.enable\">\r\n            <el-radio :label=\"false\">否</el-radio>\r\n            <el-radio :label=\"true\">是</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <template v-if=\"form.enable\">\r\n          <el-form-item\r\n            v-for=\"(element,index) in list\"\r\n            :key=\"index\"\r\n            :show-message=\"false\"\r\n            :label=\"element.Comp_Type_Name\"\r\n            prop=\"mainPart\"\r\n          >\r\n            <el-input\r\n              v-model.trim=\"form['item'+index]\"\r\n              :placeholder=\"`请输入（多个使用'${splitSymbol}'隔开），单个配置不超过10个字符`\"\r\n              clearable\r\n              @blur=\"mainBlur\"\r\n            />\r\n          </el-form-item>\r\n        </template>\r\n\r\n      </el-form>\r\n    </div>\r\n    <div class=\"btn-x\">\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { uniqueArr } from '@/utils'\r\nimport {\r\n  GetFactoryCompTypeIndentifySetting,\r\n  SaveCompTypeIdentifySetting\r\n} from '@/api/PRO/component-type'\r\n\r\nconst SPLITVALUE = '|'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      form: {\r\n        enable: false\r\n      },\r\n      list: [],\r\n      splitSymbol: SPLITVALUE,\r\n      btnLoading: false\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getTypeList()\r\n  },\r\n  methods: {\r\n    getTypeList() {\r\n      GetFactoryCompTypeIndentifySetting({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          const { Is_Enabled, Setting_List } = res.Data\r\n          this.form.enable = Is_Enabled\r\n          this.list = Setting_List.map((v, index) => {\r\n            this.$set(this.form, 'item' + index, v.Prefixs || '')\r\n            return v\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      if (this.form.enable) {\r\n        const arr = []\r\n        for (let i = 0; i < this.list.length; i++) {\r\n          const regex = /^(?!.*\\|\\|)(?!.*\\|$)(?!^\\|)[^|]{0,10}(?:\\|[^|]{0,10})*$/\r\n          if (!regex.test(this.form[`item${i}`])) {\r\n            this.$message({\r\n              message: `${this.list[i].Comp_Type_Name}配置不符合要求`,\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n\r\n          const item = this.form[`item${i}`].split(this.splitSymbol).filter(v => !!v)\r\n\r\n          // if (item.length === 0) {\r\n          //   this.$message({\r\n          //     message: `${this.list[i].Comp_Type_Name}不能为空`,\r\n          //     type: 'warning'\r\n          //   })\r\n          //   return\r\n          // }\r\n\r\n          for (let j = 0; j < item.length; j++) {\r\n            const d = item[j]\r\n            if (d.length > 10) {\r\n              this.$message({\r\n                message: `${this.list[i].Comp_Type_Name}单个配置，不能超过10个字符`,\r\n                type: 'warning'\r\n              })\r\n              return\r\n            }\r\n          }\r\n\r\n          arr.push(...item)\r\n        }\r\n        const uniArr = uniqueArr(arr)\r\n        if (uniArr.length !== arr.length) {\r\n          this.$message({\r\n            message: '配置不能相同',\r\n            type: 'warning'\r\n          })\r\n          return\r\n        }\r\n      }\r\n      this.btnLoading = true\r\n      SaveCompTypeIdentifySetting({\r\n        Is_Enabled: this.form.enable,\r\n        Setting_List: this.list.map((v, i) => {\r\n          return {\r\n            ...v,\r\n            Prefixs: this.form[`item${i}`]\r\n          }\r\n        })\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '操作成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('close')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.btnLoading = false\r\n      })\r\n    },\r\n    mainBlur(e) {\r\n\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"~@/styles/mixin.scss\";\r\n.form-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  max-height: 70vh;\r\n  .form-x{\r\n    overflow: auto;\r\n    padding-right: 16px;\r\n    @include scrollBar;\r\n  }\r\n  .btn-x {\r\n    padding-top: 16px;\r\n    text-align: right;\r\n  }\r\n\r\n}\r\n</style>\r\n"]}]}
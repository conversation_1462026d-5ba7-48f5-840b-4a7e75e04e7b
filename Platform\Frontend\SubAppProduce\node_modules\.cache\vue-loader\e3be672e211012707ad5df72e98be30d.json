{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\ship\\index.vue?vue&type=style&index=0&id=620b4146&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\ship\\index.vue", "mtime": 1758677034219}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLngtY29udGFpbmVyIHsNCiAgcGFkZGluZzogMTZweCA4cHg7DQogIC5jcy10b3AtaGVhZGVyLWJveCB7DQogICAgYmFja2dyb3VuZDogI0Y0RjVGNzsNCiAgICBwYWRkaW5nOiAwOw0KICB9DQogIC5tYi04ew0KICAgIG1hcmdpbi1ib3R0b206IDhweDsNCiAgfQ0KICAubXQtOHsNCiAgICBtYXJnaW4tdG9wOiA4cHg7DQogIH0NCiAgLm1sLTh7DQogICAgbWFyZ2luLWxlZnQ6IDhweDsNCiAgfQ0KICAubXItOHsNCiAgICBtYXJnaW4tcmlnaHQ6IDhweDsNCiAgfQ0KICAuY3MtbWFpbnsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGZsZXgtd3JhcDogd3JhcDsNCiAgfQ0KICAuY3MtcGFnaW5hdGlvbi1jb250YWluZXIgew0KICAgIHBhZGRpbmc6IDAgOHB4Ow0KICB9DQp9DQoNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/basic-information/ship", "sourcesContent": ["<template>\r\n  <div class=\"x-container\">\r\n    <top-header style=\"padding: 0 8px; margin-bottom: 10px\">\r\n      <template #left>\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增</el-button>\r\n        <el-button type=\"success\" @click=\"carImport\">导 入</el-button>\r\n      </template>\r\n      <template #right>\r\n        <!--        <el-select v-model=\"listQuery.Factory_Id\" style=\"margin-right: 8px\" placeholder=\"服务工厂\" clearable=\"\" @change=\"factoryChange\">\r\n          <el-option\r\n            v-for=\"item in factoryOption\"\r\n            :key=\"item.Id\"\r\n            :label=\"item.Name\"\r\n            :value=\"item.Id\"\r\n          />\r\n        </el-select>-->\r\n        <el-input v-model=\"listQuery.Shipnumber\" style=\"width: 180px\" placeholder=\"请输入船号\" clearable=\"\" @change=\"licenseChange\" />\r\n        <el-button type=\"primary\" @click=\"licenseChange\">查 询</el-button>\r\n        \r\n      </template>\r\n    </top-header>\r\n    <main class=\"cs-main\">\r\n      <card\r\n        v-for=\"(item, index) in list\"\r\n        :key=\"index\"\r\n        :item=\"item\"\r\n        @edit=\"handleEdit\"\r\n        @delete=\"handleDelete\"\r\n      />\r\n    </main>\r\n    <!-- :class=\"[{'mr-8':!index%4}]\" -->\r\n    <div class=\"cs-pagination-container\">\r\n      <Pagination\r\n        :total=\"total\"\r\n        :page.sync=\"listQuery.Page\"\r\n        :limit.sync=\"listQuery.PageSize\"\r\n        :page-sizes=\"tablePageSize\"\r\n        @pagination=\"fetchData\"\r\n      />\r\n    </div>\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      v-dialogDrag\r\n       class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"40%\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        ref=\"content\"\r\n        @close=\"handleClose\"\r\n        @refresh=\"fetchData\"\r\n      />\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Card from './component/Card'\r\nimport TopHeader from '@/components/TopHeader'\r\nimport addEdit from '@/views/PRO/basic-information/ship/component/AddEdit'\r\nimport CarImport from './component/Import'\r\nimport { GetPageInfo,DeleteBoat } from '@/api/PRO/car'\r\nimport Pagination from '@/components/Pagination'\r\nimport { GetFactoryList } from '@/api/PRO/factory'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\n\r\nexport default {\r\n  name: 'PROBasicVehicle',\r\n  components: {\r\n    Card,\r\n    TopHeader,\r\n    CarImport,\r\n    addEdit,\r\n    Pagination\r\n  },\r\n  data() {\r\n    return {\r\n      tablePageSize: tablePageSize,\r\n      total: 0,\r\n      listQuery: {\r\n        PageSize: 12,\r\n        Page: 1,\r\n        // Project_Id: '',\r\n        Shipnumber: ''\r\n      },\r\n      value: '',\r\n      title: '',\r\n      list: [],\r\n      currentComponent: '',\r\n      dialogVisible: false,\r\n      carOptions: [],\r\n      factoryOption: [],\r\n      factoryId: ''\r\n    }\r\n  },\r\n  mounted() {\r\n    this.fetchData()\r\n    // this.getFactory()\r\n  },\r\n  methods: {\r\n    fetchData() {\r\n      GetPageInfo(this.listQuery).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.list = res.Data.Data\r\n          this.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    factoryChange(v) {\r\n      this.fetchData()\r\n    },\r\n    // getFactory() {\r\n    //   GetFactoryList({}).then(res => {\r\n    //     if (res.IsSucceed) {\r\n    //       this.factoryOption = res.Data\r\n    //     } else {\r\n    //       this.$message({\r\n    //         message: res.Message,\r\n    //         type: 'error'\r\n    //       })\r\n    //     }\r\n    //   })\r\n    // },\r\n    licenseChange(v) {\r\n      this.fetchData()\r\n    },\r\n    handleAdd() {\r\n      this.currentComponent = 'addEdit'\r\n      this.title = '新增船舶'\r\n      this.dialogVisible = true\r\n    },\r\n    handleEdit(row) {\r\n      console.log(row,'row');\r\n      \r\n      this.currentComponent = 'addEdit'\r\n      this.title = '编辑船舶'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].editInit(row)\r\n      })\r\n    },\r\n    handleDelete(row) {\r\n      this.$confirm('是否删除该船舶?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        DeleteBoat({\r\n          id: row.Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.$emit('close')\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    },\r\n    carImport() {\r\n      this.currentComponent = 'CarImport'\r\n      this.title = '导入'\r\n      this.dialogVisible = true\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.x-container {\r\n  padding: 16px 8px;\r\n  .cs-top-header-box {\r\n    background: #F4F5F7;\r\n    padding: 0;\r\n  }\r\n  .mb-8{\r\n    margin-bottom: 8px;\r\n  }\r\n  .mt-8{\r\n    margin-top: 8px;\r\n  }\r\n  .ml-8{\r\n    margin-left: 8px;\r\n  }\r\n  .mr-8{\r\n    margin-right: 8px;\r\n  }\r\n  .cs-main{\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n  }\r\n  .cs-pagination-container {\r\n    padding: 0 8px;\r\n  }\r\n}\r\n\r\n</style>\r\n\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\part-list\\v4\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\part-list\\v4\\index.vue", "mtime": 1757915717327}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Deletepart", "GetPartWeightList", "ExportPlanpartInfo", "ExportPlanpartcountInfo", "DeletepartByfindkeywodes", "GetPartPageList", "GetGridByCode", "GetFactoryProfessionalByCode", "GetProjectAreaTreeList", "GetInstallUnitIdNameList", "TreeDetail", "TopHeader", "comImport", "ComponentsHistory", "comImportByFactory", "HistoryExport", "BatchEdit", "ComponentPack", "Edit", "OneClickGeneratePack", "GeneratePack", "DeepMaterial", "<PERSON><PERSON><PERSON><PERSON>", "PartSplit", "ProcessData", "elDragDialog", "Pagination", "timeFormat", "AuthButtons", "bimdialog", "sysUseType", "promptBox", "combineURL", "tablePageSize", "parseOssUrl", "GetPartTypeList", "baseUrl", "v4", "uuidv4", "GetSteelCadAndBimId", "getConfigure", "GetFileType", "ExpandableSection", "comDrawdialog", "TracePlot", "GetStopList", "modelDrawing", "GetBOMInfo", "SPLIT_SYMBOL", "name", "directives", "components", "mixins", "data", "allStopFlag", "showExpand", "drawer", "drawersull", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fullscreenid", "iframeUrl", "fullbimid", "expandedKey", "partTypeOption", "treeData", "treeLoading", "projectName", "statusType", "searchHeight", "tbData", "total", "tbLoading", "pgLoading", "countLoading", "queryInfo", "Page", "PageSize", "Parameter<PERSON>son", "customPageSize", "installUnitIdNameList", "nameMode", "montageOption", "value", "label", "customParams", "TypeId", "Type_Name", "Code", "Code_Like", "Spec", "DateName", "Texture", "isMontage", "InstallUnit_Id", "Part_Type_Id", "InstallUnit_Name", "Sys_Project_Id", "Project_Id", "Area_Id", "Project_Name", "Area_Name", "names", "customDialogParams", "dialogVisible", "currentComponent", "selectList", "factoryOption", "projectList", "typeOption", "columns", "columnsOption", "title", "width", "tipLabel", "monomerList", "mode", "isMonomer", "historyVisible", "undefined", "deleteContent", "SteelAmountTotal", "SchedulingNumTotal", "SteelAllWeightTotal", "SchedulingAllWeightTotal", "FinishCountTotal", "FinishWeightTotal", "Unit", "Proportion", "command", "currentLastLevel", "templateUrl", "currentNode", "comDrawData", "trackDrawer", "trackDrawerTitle", "trackDrawerData", "levelName", "levelCode", "computed", "showP9Btn", "buttons", "some", "item", "typeEntity", "_this", "find", "i", "Id", "PID", "_this$projectList$fin", "_this2", "filterText", "watch", "customParamsTypeId", "newValue", "oldValue", "console", "log", "fetchData", "n", "o", "changeMode", "mounted", "created", "_this3", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "_yield$GetBOMInfo", "currentBOMInfo", "wrap", "_callee$", "_context", "prev", "next", "sent", "Display_Name", "getTypeList", "getTableConfig", "fetchTreeData", "getFileType", "Keywords01Value", "$nextTick", "getPartWeightList", "getPartType", "$refs", "searchDom", "offsetHeight", "stop", "methods", "replace", "_this4", "MenuId", "$route", "meta", "Type", "Level", "then", "res", "Data", "length", "resData", "map", "Children", "Is_Imported", "ich", "Is_Directory", "it", "Object", "keys", "<PERSON><PERSON><PERSON>", "handleNodeClick", "_this5", "deepFilter", "tree", "ParentId", "handelsearch", "dataId", "ParentNodes", "Name", "_data$Data", "Label", "fetchList", "getInstallUnitIdNameList", "id", "_this6", "getProcessData", "_this7", "customParamsData", "JSON", "parse", "stringify", "InstallUnit_Ids", "join", "generateComponent", "concat", "_", "init", "v", "Part_Aggregate_Id", "toString", "code", "_this8", "Promise", "resolve", "IsSucceed", "Message", "$message", "error", "tbConfig", "assign", "Grid", "list", "ColumnList", "sortList", "sort", "a", "b", "Sort", "filter", "Is_Display", "fixed", "Row_Number", "selectOption", "indexOf", "message", "type", "_this9", "_callee2", "_callee2$", "_context2", "_objectSpread", "trim", "replaceAll", "TotalCount", "Is_Main", "Exdate", "getStopList", "finally", "_this0", "_callee3", "submitObj", "_callee3$", "_context3", "Bom_Level", "stopMap", "for<PERSON>ach", "Is_Stop", "row", "$set", "_this1", "_callee4", "_callee4$", "_context4", "changePage", "_this10", "_callee5", "_callee5$", "_context5", "getTbData", "YearAllWeight", "YearSteel", "CountInfo", "_this11", "_callee6", "_this11$typeOption$", "_this11$typeOption$2", "_callee6$", "_context6", "factoryId", "localStorage", "getItem", "freeze", "handleDelete", "_this12", "$confirm", "confirmButtonText", "cancelButtonText", "ids", "catch", "handleEdit", "_this13", "isReadOnly", "handleBatchEdit", "_this14", "SchedulArr", "<PERSON><PERSON><PERSON><PERSON>_Count", "handleView", "_this15", "handleExport", "_this16", "_callee7", "obj", "_callee7$", "_context7", "Part_Aggregate_Ids", "ProfessionalCode", "window", "open", "$baseUrl", "partSplit", "_this17", "modelListImport", "_this18", "$alert", "handleCommand", "deepListImport", "fileType", "Catalog_Code", "dialog", "handleOpen", "handleAllDelete", "_this19", "_callee8", "_callee8$", "_context8", "success", "warning", "handleClose", "component", "reset", "hasSearch", "arguments", "resetFields", "handleDeepMaterial", "_this20", "handelSchduling", "_this21", "_this22", "Math", "round", "DeepenNum", "SchedulingNum", "DeepenWeight", "SchedulingWeight", "Finish_Count", "Finish_Weight", "tbSelectChange", "array", "_this23", "records", "SteelAllWeightTotalTemp", "SchedulingAllWeightTotalTemp", "FinishWeightTotalTemp", "schedulingNum", "<PERSON><PERSON>", "Number", "Total_Weight", "Weight", "fetchTreeDataLocal", "getPartInfo", "drawingData", "Drawing", "split", "fileUrlData", "File_Url", "drawingActive", "drawingDataList", "index", "url", "getPartInfoDrawing", "_this24", "importDetailId", "ExtensionName", "fileBim", "IsUpload", "modelDrawingRef", "dwgInit", "customFilterFun", "node", "arr", "labelVal", "statusVal", "parentNode", "parent", "labels", "status", "Is_Deepen_Change", "level", "_toConsumableArray", "resultLabel", "resultStatus", "s", "_this25", "Part_Grade", "_this26", "_callee9", "_data$Data2", "params", "_callee9$", "_context9", "catalogCode", "isSHQD", "English_Name", "handelImport", "comDrawdialogRef", "handleTrack"], "sources": ["src/views/PRO/part-list/v4/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <div\r\n      v-loading=\"pgLoading\"\r\n      style=\"display: flex\"\r\n      class=\"h100\"\r\n      element-loading-text=\"加载中\"\r\n    >\r\n      <ExpandableSection v-model=\"showExpand\" :width=\"300\" class=\"cs-left fff\">\r\n        <div class=\"inner-wrapper\">\r\n          <div class=\"tree-search\">\r\n            <el-select\r\n              v-model=\"statusType\"\r\n              clearable\r\n              class=\"search-select\"\r\n              placeholder=\"导入状态选择\"\r\n            >\r\n              <el-option label=\"已导入\" value=\"已导入\" />\r\n              <el-option label=\"未导入\" value=\"未导入\" />\r\n              <el-option label=\"已变更\" value=\"已变更\" />\r\n            </el-select>\r\n            <el-input\r\n              v-model.trim=\"projectName\"\r\n              placeholder=\"关键词搜索\"\r\n              size=\"small\"\r\n              clearable\r\n              suffix-icon=\"el-icon-search\"\r\n              @blur=\"fetchTreeDataLocal\"\r\n              @clear=\"fetchTreeDataLocal\"\r\n              @keydown.enter.native=\"fetchTreeDataLocal\"\r\n            />\r\n          </div>\r\n          <el-divider class=\"cs-divider\" />\r\n          <div class=\"tree-x cs-scroll\">\r\n            <tree-detail\r\n              ref=\"tree\"\r\n              icon=\"icon-folder\"\r\n              is-custom-filter\r\n              :custom-filter-fun=\"customFilterFun\"\r\n              :loading=\"treeLoading\"\r\n              :tree-data=\"treeData\"\r\n              show-status\r\n              show-detail\r\n              :filter-text=\"filterText\"\r\n              :expanded-key=\"expandedKey\"\r\n              @handleNodeClick=\"handleNodeClick\"\r\n            >\r\n              <template #csLabel=\"{ showStatus, data }\">\r\n                <span\r\n                  v-if=\"!data.ParentNodes\"\r\n                  class=\"cs-blue\"\r\n                >({{ data.Code }})</span>{{ data.Label }}\r\n                <template v-if=\"showStatus && data.Label != '全部'\">\r\n                  <span v-if=\"data.Data.Is_Deepen_Change\" class=\"cs-tag redBg\">\r\n                    <i class=\"fourRed\">已变更</i></span>\r\n                  <span\r\n                    v-else\r\n                    :class=\"[\r\n                      'cs-tag',\r\n                      data.Data.Is_Imported == true ? 'greenBg' : 'orangeBg',\r\n                    ]\"\r\n                  >\r\n                    <i\r\n                      :class=\"[\r\n                        data.Data.Is_Imported == true\r\n                          ? 'fourGreen'\r\n                          : 'fourOrange',\r\n                      ]\"\r\n                    >{{\r\n                      data.Data.Is_Imported == true ? \"已导入\" : \"未导入\"\r\n                    }}</i>\r\n                  </span>\r\n                </template>\r\n              </template></tree-detail>\r\n          </div>\r\n        </div>\r\n      </ExpandableSection>\r\n      <div class=\"cs-right\" style=\"padding-right: 0\">\r\n        <div class=\"container\">\r\n          <div ref=\"searchDom\" class=\"cs-from\">\r\n            <div class=\"cs-search\">\r\n              <el-form\r\n                ref=\"customParams\"\r\n                :model=\"customParams\"\r\n                label-width=\"80px\"\r\n                class=\"demo-form-inline\"\r\n              >\r\n                <el-row>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item\r\n                      :label=\"levelName + '名称'\"\r\n                      prop=\"Names\"\r\n                    >\r\n                      <el-input\r\n                        v-model=\"names\"\r\n                        clearable\r\n                        style=\"width: 100%\"\r\n                        class=\"input-with-select\"\r\n                        placeholder=\"请输入内容\"\r\n                        size=\"small\"\r\n                      >\r\n                        <el-select\r\n                          slot=\"prepend\"\r\n                          v-model=\"nameMode\"\r\n                          placeholder=\"请选择\"\r\n                          style=\"width: 100px\"\r\n                        >\r\n                          <el-option label=\"模糊搜索\" :value=\"1\" />\r\n                          <el-option label=\"精确搜索\" :value=\"2\" />\r\n                        </el-select>\r\n                      </el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item\r\n                      :label=\"levelName + '种类'\"\r\n                      prop=\"Part_Type_Id\"\r\n                    >\r\n                      <el-select\r\n                        v-model=\"customParams.Part_Type_Id\"\r\n                        style=\"width: 100%\"\r\n                        placeholder=\"请选择\"\r\n                        clearable\r\n                      >\r\n                        <el-option\r\n                          v-for=\"item in partTypeOption\"\r\n                          :key=\"item.value\"\r\n                          :label=\"item.label\"\r\n                          :value=\"item.value\"\r\n                        />\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item label=\"规格\" prop=\"Spec\">\r\n                      <el-input\r\n                        v-model=\"customParams.Spec\"\r\n                        placeholder=\"请输入\"\r\n                        clearable\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item\r\n                      label=\"材质\"\r\n                      prop=\"Texture\"\r\n                    >\r\n                      <el-input\r\n                        v-model=\"customParams.Texture\"\r\n                        placeholder=\"请输入\"\r\n                        clearable\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item\r\n                      label=\"操作人\"\r\n                      prop=\"DateName\"\r\n                    >\r\n                      <el-input\r\n                        v-model=\"customParams.DateName\"\r\n                        style=\"width: 100%\"\r\n                        placeholder=\"请输入\"\r\n                        clearable\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item\r\n                      class=\"mb0\"\r\n                      label=\"批次\"\r\n                      prop=\"InstallUnit_Id\"\r\n                    >\r\n                      <el-select\r\n                        v-model=\"customParams.InstallUnit_Id\"\r\n                        multiple\r\n                        filterable\r\n                        clearable\r\n                        placeholder=\"请选择\"\r\n                        style=\"width: 100%\"\r\n                        :disabled=\"!Boolean(customParams.Area_Id)\"\r\n                      >\r\n                        <el-option\r\n                          v-for=\"item in installUnitIdNameList\"\r\n                          :key=\"item.Id\"\r\n                          :label=\"item.Name\"\r\n                          :value=\"item.Id\"\r\n                        />\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item\r\n                      label=\"是否拼接\"\r\n                      prop=\"isMontage\"\r\n                    >\r\n                      <el-select\r\n                        v-model=\"customParams.isMontage\"\r\n                        style=\"width: 100%\"\r\n                        placeholder=\"请选择\"\r\n                        clearable\r\n                      >\r\n                        <el-option\r\n                          v-for=\"item in montageOption\"\r\n                          :key=\"item.value\"\r\n                          :label=\"item.label\"\r\n                          :value=\"item.value\"\r\n                        />\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item class=\"mb0\" label-width=\"16px\">\r\n                      <el-button\r\n                        type=\"primary\"\r\n                        @click=\"handelsearch()\"\r\n                      >搜索\r\n                      </el-button>\r\n                      <el-button @click=\"handelsearch('reset')\">重置</el-button>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form>\r\n            </div>\r\n          </div>\r\n          <div class=\"fff cs-z-tb-wrapper\">\r\n            <div class=\"cs-button-box\">\r\n              <template>\r\n                <!-- <el-dropdown trigger=\"click\" placement=\"bottom-start\" @command=\"handleCommand\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    :disabled=\"!currentLastLevel\"\r\n                  >零件导入\r\n                    <i class=\"el-icon-arrow-down el-icon--right\" />\r\n                  </el-button>\r\n                  <el-dropdown-menu slot=\"dropdown\">\r\n                    <el-dropdown-item command=\"cover\">覆盖导入</el-dropdown-item>\r\n                    <el-dropdown-item command=\"add\">新增导入</el-dropdown-item>\r\n                  </el-dropdown-menu>\r\n                </el-dropdown> -->\r\n                <!-- <el-button\r\n                  type=\"primary\"\r\n                  @click=\"deepListImport\"\r\n                >零件导入</el-button>\r\n                <el-button\r\n                  :disabled=\"!selectList.length\"\r\n                  @click=\"modelListImport\"\r\n                  >导出零件排产单模板</el-button\r\n                > -->\r\n                <el-button\r\n                  type=\"primary\"\r\n                  :disabled=\"!selectList.length || selectList.length !== 1 || selectList.some(item=>item.stopFlag)\"\r\n                  @click=\"partSplit\"\r\n                >{{ levelName }}拆分</el-button>\r\n                <el-button\r\n                  :disabled=\"!selectList.length\"\r\n                  @click=\"handleExport\"\r\n                >导出{{ levelName }}</el-button>\r\n                <el-button\r\n                  :disabled=\"!selectList.length || selectList.some(item=>item.stopFlag)\"\r\n                  type=\"primary\"\r\n                  plain\r\n                  @click=\"handleBatchEdit\"\r\n                >批量编辑</el-button>\r\n                <!-- <el-button\r\n                  type=\"danger\"\r\n                  plain\r\n                  :disabled=\"!selectList.length\"\r\n                  @click=\"handleDelete\"\r\n                >删除选中</el-button> -->\r\n                <el-button\r\n                  type=\"success\"\r\n                  plain\r\n                  :disabled=\"!Boolean(customParams.Sys_Project_Id)\"\r\n                  @click=\"handelImport\"\r\n                >图纸导入\r\n                </el-button>\r\n              </template>\r\n            </div>\r\n            <div v-loading=\"countLoading\" class=\"info-box\">\r\n              <div class=\"cs-col\">\r\n                <span><span class=\"info-label\">深化总数</span><i>{{ SteelAmountTotal }} 件</i></span>\r\n                <span><span class=\"info-label\">深化总量</span><i>{{ SteelAllWeightTotal }}t</i></span>\r\n              </div>\r\n              <div class=\"cs-col\">\r\n                <span><span class=\"info-label\">排产总数</span><i>{{ SchedulingNumTotal }} 件</i></span>\r\n                <span><span class=\"info-label\">排产总量</span><i>{{ SchedulingAllWeightTotal }} t</i></span>\r\n              </div>\r\n              <div class=\"cs-col\" style=\"cursor: pointer;\" @click=\"getProcessData()\">\r\n                <span><span class=\"info-label\">完成总数</span><i>{{ FinishCountTotal }} 件</i></span>\r\n                <span><span class=\"info-label\">完成总量</span><i>{{ FinishWeightTotal }} t</i></span>\r\n              </div>\r\n            </div>\r\n            <div class=\"tb-container\">\r\n              <vxe-table\r\n                v-loading=\"tbLoading\"\r\n                :empty-render=\"{name: 'NotData'}\"\r\n                show-header-overflow\r\n                element-loading-spinner=\"el-icon-loading\"\r\n                element-loading-text=\"拼命加载中\"\r\n                empty-text=\"暂无数据\"\r\n                class=\"cs-vxe-table\"\r\n                height=\"100%\"\r\n                align=\"left\"\r\n                stripe\r\n                :data=\"tbData\"\r\n                resizable\r\n                :tooltip-config=\"{ enterable: true }\"\r\n                @checkbox-all=\"tbSelectChange\"\r\n                @checkbox-change=\"tbSelectChange\"\r\n              >\r\n                <vxe-column fixed=\"left\" type=\"checkbox\" width=\"44\" />\r\n                <vxe-column\r\n                  v-for=\"(item, index) in columns\"\r\n                  :key=\"index\"\r\n                  :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                  show-overflow=\"tooltip\"\r\n                  sortable\r\n                  :align=\"item.Align\"\r\n                  :field=\"item.Code\"\r\n                  :title=\"item.Display_Name\"\r\n                  :width=\"item.Width ? item.Width : 120\"\r\n                >\r\n                  <template #default=\"{ row }\">\r\n                    <div v-if=\"item.Code == 'Code'\">\r\n                      <el-tag v-if=\"row.Is_Change\" style=\"margin-right: 8px;\" type=\"danger\">变</el-tag>\r\n                      <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                      <el-link type=\"primary\" @click=\"getPartInfo(row)\"> {{ row[item.Code] | displayValue }}</el-link>\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'Is_Component'\">\r\n                      <span>\r\n                        <!--                      这列表叫是否非直发件 -->\r\n                        <el-tag\r\n                          v-if=\"row.Is_Component === 'True'\"\r\n                          type=\"danger\"\r\n                        >是</el-tag>\r\n                        <el-tag v-else type=\"success\">否</el-tag>\r\n                      </span>\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'Is_Split'\">\r\n                      <el-tag v-if=\"row.Is_Split === true\">是</el-tag>\r\n                      <el-tag v-else type=\"danger\">否</el-tag>\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'Deep_Material'\">\r\n                      <el-link\r\n                        type=\"primary\"\r\n                        @click=\"handleDeepMaterial(row)\"\r\n                      >查看</el-link>\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'Num' && row[item.Code] > 0\">\r\n                      <span v-if=\"row[item.Code]\"> {{ row[item.Code] | displayValue }}件</span>\r\n                      <span v-else>-</span>\r\n                    </div>\r\n                    <div\r\n                      v-else-if=\"\r\n                        item.Code == 'Schduling_Count' && row[item.Code] > 0\r\n                      \"\r\n                    >\r\n                      <el-link\r\n                        v-if=\"row[item.Code]\"\r\n                        type=\"primary\"\r\n                        @click=\"handelSchduling(row)\"\r\n                      > {{ row[item.Code] | displayValue }}件</el-link>\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'Is_Trace'\">\r\n                      <span>\r\n                        <el-tag\r\n                          v-if=\"row.Is_Trace\"\r\n                          type=\"success\"\r\n                        >是</el-tag>\r\n                        <el-tag v-else type=\"danger\">否</el-tag>\r\n                      </span>\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'Launch_Time'\">\r\n                      {{ row[item.Code] | timeFormat }}\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'Drawing'\">\r\n                      <span\r\n                        v-if=\"row.Drawing !== '暂无'\"\r\n                        style=\"color: #298dff; cursor: pointer\"\r\n                        @click=\"getPartInfo(row)\"\r\n                      > {{ row[item.Code] | displayValue }}\r\n                      </span>\r\n                      <span v-else> {{ row[item.Code] | displayValue }}</span>\r\n                    </div>\r\n                    <div v-else>\r\n                      <span>{{ row[item.Code] !== undefined && row[item.Code] !== null ? row[item.Code] : \"-\" }}</span>\r\n                    </div>\r\n                  </template>\r\n                </vxe-column>\r\n                <vxe-column\r\n                  fixed=\"right\"\r\n                  title=\"操作\"\r\n                  width=\"150\"\r\n                  show-overflow\r\n                >\r\n                  <template #default=\"{ row }\">\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"handleView(row)\"\r\n                    >详情</el-button>\r\n                    <el-button\r\n                      :disabled=\"row.stopFlag\"\r\n                      type=\"text\"\r\n                      @click=\"handleEdit(row)\"\r\n                    >编辑</el-button>\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"handleTrack(row)\"\r\n                    >轨迹图\r\n                    </el-button>\r\n                  </template>\r\n                </vxe-column>\r\n              </vxe-table>\r\n            </div>\r\n            <div class=\"cs-bottom\">\r\n              <Pagination\r\n                class=\"cs-table-pagination\"\r\n                :total=\"total\"\r\n                max-height=\"100%\"\r\n                :page-sizes=\"tablePageSize\"\r\n                :page.sync=\"queryInfo.Page\"\r\n                :limit.sync=\"queryInfo.PageSize\"\r\n                layout=\"total, sizes, prev, pager, next, jumper\"\r\n                @pagination=\"changePage\"\r\n              >\r\n                <!--                <span class=\"pg-input\">\r\n                  <el-select\r\n                    v-model.number=\"queryInfo.PageSize\"\r\n                    allow-create\r\n                    filterable\r\n                    default-first-option\r\n                    @change=\"changePage\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"(item, index) in customPageSize\"\r\n                      :key=\"index\"\r\n                      :label=\"`${item}条/页`\"\r\n                      :value=\"item\"\r\n                    />\r\n                  </el-select>\r\n                </span>-->\r\n              </Pagination>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"card\" />\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      ref=\"content\"\r\n      v-el-drag-dialog\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"width\"\r\n      class=\"z-dialog\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :level-code=\"levelCode\"\r\n        :level-name=\"levelName\"\r\n        :select-list=\"selectList\"\r\n        :custom-params=\"customDialogParams\"\r\n        :type-id=\"customParams.TypeId\"\r\n        :type-entity=\"typeEntity\"\r\n        :project-id=\"customParams.Project_Id\"\r\n        :sys-project-id=\"customParams.Project_Id\"\r\n        @close=\"handleClose\"\r\n        @refresh=\"fetchData\"\r\n      />\r\n    </el-dialog>\r\n    <bimdialog\r\n      ref=\"dialog\"\r\n      :type-entity=\"typeEntity\"\r\n      :area-id=\"customParams.Area_Id\"\r\n      :project-id=\"customParams.Project_Id\"\r\n      @getData=\"fetchData\"\r\n      @getTreeData=\"fetchTreeData\"\r\n    />\r\n\r\n    <el-drawer\r\n      :visible.sync=\"drawersull\"\r\n      direction=\"btt\"\r\n      size=\"100%\"\r\n      destroy-on-close\r\n    >\r\n      <iframe\r\n        v-if=\"templateUrl\"\r\n        id=\"fullFrame\"\r\n        :src=\"templateUrl\"\r\n        frameborder=\"0\"\r\n        style=\"width: 96%; margin-left: 2%; height: 70vh; margin-top: 2%\"\r\n      />\r\n    </el-drawer>\r\n\r\n    <el-drawer\r\n      :visible.sync=\"trackDrawer\"\r\n      direction=\"rtl\"\r\n      size=\"30%\"\r\n      destroy-on-close\r\n      custom-class=\"trackDrawerClass\"\r\n    >\r\n      <template #title>\r\n        <div>\r\n          <span>{{ trackDrawerTitle }}</span>\r\n          <span style=\"margin-left: 24px\">{{ trackDrawerData.Num }}</span>\r\n        </div>\r\n      </template>\r\n      <TracePlot :track-drawer-data=\"trackDrawerData\" />\r\n    </el-drawer>\r\n\r\n    <comDrawdialog ref=\"comDrawdialogRef\" @getData=\"fetchData\" />\r\n    <modelDrawing ref=\"modelDrawingRef\" type=\"零件\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  Deletepart,\r\n  GetPartWeightList,\r\n  ExportPlanpartInfo,\r\n  ExportPlanpartcountInfo,\r\n  DeletepartByfindkeywodes\r\n} from '@/api/plm/production'\r\nimport { GetPartPageList } from '@/api/plm/component'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\r\nimport {\r\n  GetProjectAreaTreeList,\r\n  GetInstallUnitIdNameList\r\n} from '@/api/PRO/project'\r\n\r\nimport TreeDetail from '@/components/TreeDetail'\r\nimport TopHeader from '@/components/TopHeader'\r\nimport comImport from './component/Import'\r\nimport ComponentsHistory from './component/ComponentsHistory'\r\nimport comImportByFactory from './component/ImportByFactory'\r\nimport HistoryExport from './component/HistoryExport'\r\nimport BatchEdit from './component/BatchEditor'\r\nimport ComponentPack from './component/ComponentPack/index'\r\nimport Edit from './component/Edit'\r\nimport OneClickGeneratePack from './component/OneClickGeneratePack'\r\nimport GeneratePack from './component/GeneratePack'\r\nimport DeepMaterial from './component/DeepMaterial'\r\nimport Schduling from './component/Schduling'\r\nimport PartSplit from './component/PartSplit'\r\nimport ProcessData from './component/ProcessData.vue'\r\n\r\nimport elDragDialog from '@/directive/el-drag-dialog'\r\nimport Pagination from '@/components/Pagination'\r\nimport { timeFormat } from '@/filters'\r\n// import { Column, Header, Table, Tooltip } from 'vxe-table'\r\n// import Vue from 'vue'\r\nimport AuthButtons from '@/mixins/auth-buttons'\r\nimport bimdialog from './component/bimdialog'\r\nimport sysUseType from '@/directive/sys-use-type/index.js'\r\nimport { promptBox } from './component/messageBox'\r\n\r\nimport { combineURL } from '@/utils'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport { parseOssUrl } from '@/utils/file'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\nimport { baseUrl } from '@/utils/baseurl'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { GetSteelCadAndBimId } from '@/api/PRO/component'\r\nimport { getConfigure } from '@/api/user'\r\nimport { GetFileType } from '@/api/sys'\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport comDrawdialog from '@/views/PRO/production-order/deepen-files/dialog' // 深化文件-零件详图导入\r\nimport TracePlot from './component/TracePlot'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nimport modelDrawing from '@/views/PRO/components/modelDrawing.vue'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\n// Vue.use(Header).use(Column).use(Tooltip).use(Table)\r\nconst SPLIT_SYMBOL = '$_$'\r\nexport default {\r\n  name: 'PROPartList',\r\n  directives: { elDragDialog, sysUseType },\r\n  components: {\r\n    ExpandableSection,\r\n    TreeDetail,\r\n    TopHeader,\r\n    comImport,\r\n    comImportByFactory,\r\n    BatchEdit,\r\n    HistoryExport,\r\n    GeneratePack,\r\n    Edit,\r\n    ComponentPack,\r\n    OneClickGeneratePack,\r\n    Pagination,\r\n    bimdialog,\r\n    ComponentsHistory,\r\n    DeepMaterial,\r\n    Schduling,\r\n    comDrawdialog,\r\n    TracePlot,\r\n    PartSplit,\r\n    modelDrawing,\r\n    ProcessData\r\n  },\r\n  mixins: [AuthButtons],\r\n  data() {\r\n    return {\r\n      allStopFlag: false,\r\n      showExpand: true,\r\n      drawer: false,\r\n      drawersull: false,\r\n      iframeKey: '',\r\n      fullscreenid: '',\r\n      iframeUrl: '',\r\n      fullbimid: '',\r\n      expandedKey: '', // -1是全部\r\n      tablePageSize: tablePageSize,\r\n      partTypeOption: [],\r\n      treeData: [],\r\n      treeLoading: true,\r\n      projectName: '',\r\n      statusType: '',\r\n      searchHeight: 0,\r\n      tbData: [],\r\n      total: 0,\r\n      tbLoading: false,\r\n      pgLoading: false,\r\n      countLoading: false,\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: 10,\r\n        ParameterJson: []\r\n      },\r\n      customPageSize: [10, 20, 50, 100],\r\n      installUnitIdNameList: [], // 批次数组\r\n      nameMode: 1,\r\n      montageOption: [\r\n        { value: true, label: '是' },\r\n        { value: false, label: '否' }\r\n      ],\r\n      customParams: {\r\n        TypeId: '',\r\n        Type_Name: '',\r\n        Code: '',\r\n        Code_Like: '',\r\n        Spec: '',\r\n        DateName: '',\r\n        Texture: '',\r\n        // Keywords01: 'Code',\r\n        // Keywords01Value: '',\r\n        // Keywords02: 'Spec',\r\n        // Keywords02Value: '',\r\n        // Keywords03: 'Length',\r\n        // Keywords03Value: '',\r\n        // Keywords04: 'Texture',\r\n        // Keywords04Value: '',\r\n        isMontage: null,\r\n        InstallUnit_Id: [],\r\n        Part_Type_Id: '',\r\n        InstallUnit_Name: '',\r\n        Sys_Project_Id: '',\r\n        Project_Id: '',\r\n        Area_Id: '',\r\n        Project_Name: '',\r\n        Area_Name: ''\r\n      },\r\n      names: '',\r\n      customDialogParams: {},\r\n      dialogVisible: false,\r\n      currentComponent: '',\r\n      selectList: [],\r\n      factoryOption: [],\r\n      projectList: [],\r\n      typeOption: [],\r\n      columns: [],\r\n      columnsOption: [\r\n        // { Display_Name: '零件名称', Code: 'Code' },\r\n        // { Display_Name: '规格', Code: 'Spec' },\r\n        // { Display_Name: '长度', Code: 'Length' },\r\n        // { Display_Name: '材质', Code: 'Texture' },\r\n        // { Display_Name: '深化数量', Code: 'Num' },\r\n        // { Display_Name: '排产数量', Code: 'Schduling_Count' },\r\n        // { Display_Name: '单重', Code: 'Weight' },\r\n        // { Display_Name: '总重', Code: 'Total_Weight' },\r\n        // { Display_Name: '形状', Code: 'Shape' },\r\n        // { Display_Name: '构件名称', Code: 'Component_Code' },\r\n        // { Display_Name: '操作人', Code: 'datename' },\r\n        // { Display_Name: '操作时间', Code: 'Exdate' }\r\n      ],\r\n      title: '',\r\n      width: '60%',\r\n      tipLabel: '',\r\n      monomerList: [],\r\n      mode: '',\r\n      isMonomer: true,\r\n      historyVisible: false,\r\n      sysUseType: undefined,\r\n      deleteContent: true,\r\n      SteelAmountTotal: 0, // 深化总量\r\n      SchedulingNumTotal: 0, // 排产总量\r\n      SteelAllWeightTotal: 0, // 深化总重\r\n      SchedulingAllWeightTotal: 0, // 排产总重\r\n      FinishCountTotal: 0, // 完成数量\r\n      FinishWeightTotal: 0, // 完成重量\r\n      Unit: '',\r\n      Proportion: 0, // 专业的单位换算\r\n      command: 'cover',\r\n      currentLastLevel: false,\r\n      templateUrl: '',\r\n      currentNode: {},\r\n      comDrawData: {},\r\n      trackDrawer: false,\r\n      trackDrawerTitle: '',\r\n      trackDrawerData: {},\r\n      levelName: '',\r\n      levelCode: ''\r\n    }\r\n  },\r\n  computed: {\r\n    showP9Btn() {\r\n      return this.AuthButtons.buttons.some((item) => item.Code === 'p9BtnAdd')\r\n    },\r\n    typeEntity() {\r\n      return this.typeOption.find((i) => i.Id === this.customParams.TypeId)\r\n    },\r\n    PID() {\r\n      return this.projectList.find(\r\n        (i) => i.Sys_Project_Id === this.customParams.Project_Id\r\n      )?.Id\r\n    },\r\n    filterText() {\r\n      return this.projectName + SPLIT_SYMBOL + this.statusType\r\n    }\r\n  },\r\n  watch: {\r\n    'customParams.TypeId': function(newValue, oldValue) {\r\n      console.log({ oldValue })\r\n      if (oldValue && oldValue !== '0') {\r\n        this.fetchData()\r\n      }\r\n    },\r\n    names(n, o) {\r\n      this.changeMode()\r\n    },\r\n    nameMode(n, o) {\r\n      this.changeMode()\r\n    }\r\n  },\r\n  mounted() {\r\n\r\n  },\r\n  async created() {\r\n    const { currentBOMInfo } = await GetBOMInfo(0)\r\n    console.log('list', currentBOMInfo)\r\n    this.levelName = currentBOMInfo?.Display_Name\r\n    this.levelCode = currentBOMInfo?.Code\r\n    await this.getTypeList()\r\n    // await this.fetchData()\r\n    await this.getTableConfig('plm_parts_page_list')\r\n\r\n    this.fetchTreeData()\r\n    this.getFileType()\r\n    if (this.Keywords01Value === '是') {\r\n      console.log('this.Keywords01Value', this.Keywords01Value)\r\n    }\r\n    this.$nextTick(() => {\r\n      this.pgLoading = true\r\n      console.log(this.columns)\r\n      this.getPartWeightList()\r\n      this.getPartType()\r\n      this.searchHeight = this.$refs.searchDom.offsetHeight + 327\r\n    })\r\n  },\r\n  methods: {\r\n    changeMode() {\r\n      if (this.nameMode === 1) {\r\n        this.customParams.Code_Like = this.names\r\n        this.customParams.Code = ''\r\n      } else {\r\n        this.customParams.Code_Like = ''\r\n        this.customParams.Code = this.names.replace(/\\s+/g, '\\n')\r\n      }\r\n    },\r\n    // 项目区域数据集\r\n    fetchTreeData() {\r\n      GetProjectAreaTreeList({ MenuId: this.$route.meta.Id, Type: 0, projectName: this.projectName, Level: this.levelCode }).then((res) => {\r\n        // const resAll = [\r\n        //   {\r\n        //     ParentNodes: null,\r\n        //     Id: '-1',\r\n        //     Code: '全部',\r\n        //     Label: '全部',\r\n        //     Level: null,\r\n        //     Data: {},\r\n        //     Children: []\r\n        //   }\r\n        // ]\r\n        // const resData = resAll.concat(res.Data)\r\n        if (res.Data.length === 0) {\r\n          this.treeLoading = false\r\n          this.pgLoading = false\r\n          return\r\n        }\r\n        const resData = res.Data\r\n        resData.map((item) => {\r\n          if (item.Children.length === 0) {\r\n            item.Is_Imported = false\r\n          } else {\r\n            item.Data.Is_Imported = item.Children.some((ich) => {\r\n              return ich.Data.Is_Imported === true\r\n            })\r\n            item.Is_Directory = true\r\n            item.Children.map((it) => {\r\n              if (it.Children.length > 0) {\r\n                it.Is_Directory = true\r\n              }\r\n            })\r\n          }\r\n        })\r\n        this.treeData = resData\r\n        if (Object.keys(this.currentNode).length === 0) {\r\n          // this.fetchData()\r\n          this.setKey()\r\n        } else {\r\n          this.handleNodeClick(this.currentNode)\r\n        }\r\n        this.treeLoading = false\r\n        // this.expandedKey = this.customParams.Area_Id ? this.customParams.Area_Id : this.customParams.Project_Id ? this.customParams.Project_Id : resData[0].Id // '-1'\r\n        // this.customParams.Sys_Project_Id = this.customParams.Sys_Project_Id || resData[0].Data.Sys_Project_Id\r\n        // this.customParams.Project_Id = this.customParams.Project_Id || resData[0].Data.Id\r\n        // this.customParams.Area_Name = ''\r\n        // this.treeLoading = false\r\n        // this.fetchData()\r\n      })\r\n    },\r\n    // 设置默认选中第一个区域末级节点\r\n    setKey() {\r\n      const deepFilter = (tree) => {\r\n        for (let i = 0; i < tree.length; i++) {\r\n          const item = tree[i]\r\n          const { Data, Children } = item\r\n          console.log(Data)\r\n          if (Data.ParentId && !Children?.length) {\r\n            console.log(Data, '????')\r\n            this.currentNode = Data\r\n            this.handleNodeClick(item)\r\n            return\r\n          } else {\r\n            if (Children && Children.length > 0) {\r\n              return deepFilter(Children)\r\n            } else {\r\n              this.handleNodeClick(item)\r\n              return\r\n            }\r\n          }\r\n        }\r\n      }\r\n      return deepFilter(this.treeData)\r\n    },\r\n    // 选中左侧项目节点\r\n    handleNodeClick(data) {\r\n      this.handelsearch('reset', false)\r\n      this.currentNode = data\r\n      this.expandedKey = data.Id\r\n      const dataId = data.Id === '-1' ? '' : data.Id\r\n      console.log('nodeData', data)\r\n      if (data.ParentNodes) {\r\n        this.customParams.Project_Id = data.Data.Project_Id\r\n        this.customParams.Area_Id = data.Id\r\n        this.customParams.Area_Name = data.Data.Name\r\n        this.customParams.Sys_Project_Id = data.Data.Sys_Project_Id\r\n      } else {\r\n        this.customParams.Project_Id = dataId\r\n        this.customParams.Area_Id = ''\r\n        this.customParams.Area_Name = data.Data.Name\r\n        this.customParams.Sys_Project_Id = data.Data.Sys_Project_Id\r\n      }\r\n      console.log(\r\n        this.customParams.Sys_Project_Id,\r\n        'this.customParams.Sys_Project_Id============11111'\r\n      )\r\n      console.log(\r\n        this.customParams.Area_Id,\r\n        'this.customParams.Area_Id============11111'\r\n      )\r\n      this.currentLastLevel = !!(data.Data.Level && data.Children.length === 0)\r\n      if (this.currentLastLevel) {\r\n        this.customParams.Project_Name = data.Data?.Project_Name\r\n        this.customParams.Area_Name = data.Label\r\n      }\r\n      this.queryInfo.Page = 1\r\n      this.pgLoading = true\r\n      this.fetchList()\r\n      console.log(this.customParams.Area_Id)\r\n      this.getInstallUnitIdNameList(dataId, data)\r\n      this.getPartWeightList()\r\n    },\r\n\r\n    // 获取批次\r\n    getInstallUnitIdNameList(id, data) {\r\n      if (id === '' || data.Children.length > 0) {\r\n        this.installUnitIdNameList = []\r\n      } else {\r\n        GetInstallUnitIdNameList({ Area_Id: id }).then((res) => {\r\n          this.installUnitIdNameList = res.Data\r\n        })\r\n      }\r\n    },\r\n    // 工序完成量\r\n    getProcessData() {\r\n      const customParamsData = JSON.parse(JSON.stringify(this.customParams))\r\n      const InstallUnit_Ids = customParamsData.InstallUnit_Id.join(',')\r\n      delete customParamsData.InstallUnit_Id\r\n\r\n      this.width = '40%'\r\n      this.generateComponent(`${this.levelName}工序完成量`, 'ProcessData')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(customParamsData, InstallUnit_Ids, this.selectList.map((v) => v.Part_Aggregate_Id).toString())\r\n      })\r\n    },\r\n    getTableConfig(code) {\r\n      return new Promise((resolve) => {\r\n        GetGridByCode({\r\n          code:\r\n            code +\r\n            ',' +\r\n            this.typeOption.find((i) => i.Id === this.customParams.TypeId).Code\r\n        }).then((res) => {\r\n          const { IsSucceed, Data, Message } = res\r\n          if (IsSucceed) {\r\n            if (!Data) {\r\n              this.$message.error('当前专业没有配置相对应表格')\r\n              this.tbLoading = true\r\n              return\r\n            }\r\n            this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n            const list = Data.ColumnList || []\r\n            const sortList = list.sort((a, b) => a.Sort - b.Sort)\r\n            this.columns = sortList\r\n              .filter((v) => v.Is_Display)\r\n              .map((item) => {\r\n                if (item.Code === 'Code') {\r\n                  item.fixed = 'left'\r\n                }\r\n\r\n                return item\r\n              })\r\n            this.queryInfo.PageSize = +Data.Grid.Row_Number || 20\r\n            resolve(this.columns)\r\n            console.log(this.columns)\r\n            const selectOption = JSON.parse(JSON.stringify(this.columns))\r\n            console.log(selectOption)\r\n            this.columnsOption = selectOption.filter((v) => {\r\n              return (\r\n                v.Display_Name !== '操作时间' &&\r\n                v.Display_Name !== '模型ID' &&\r\n                v.Display_Name !== '深化资料' &&\r\n                v.Display_Name !== '备注' &&\r\n                v.Display_Name !== '排产数量' &&\r\n                v.Code.indexOf('Attr') === -1 &&\r\n                v.Display_Name !== '批次'\r\n              )\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    async fetchList() {\r\n      const customParamsData = JSON.parse(JSON.stringify(this.customParams))\r\n      const InstallUnit_Ids = customParamsData.InstallUnit_Id.join(',')\r\n      delete customParamsData.InstallUnit_Id\r\n      await GetPartPageList({\r\n        ...this.queryInfo,\r\n        ...customParamsData,\r\n        Code: customParamsData.Code.trim().replaceAll(' ', '\\n'),\r\n        InstallUnit_Ids: InstallUnit_Ids\r\n      })\r\n        .then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.queryInfo.PageSize = res.Data.PageSize\r\n            this.total = res.Data.TotalCount\r\n            this.tbData = res.Data.Data.map((v) => {\r\n              v.Is_Main = v.Is_Main ? '是' : '否'\r\n              v.Exdate = timeFormat(v.Exdate, '{y}-{m}-{d} {h}:{i}:{s}')\r\n              // console.log(v)\r\n              return v\r\n            })\r\n            this.selectList = []\r\n            this.getStopList()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n        .finally(() => {\r\n          this.tbLoading = false\r\n          this.pgLoading = false\r\n        })\r\n    },\r\n    async getStopList() {\r\n      const submitObj = this.tbData.map(item => {\r\n        return {\r\n          Id: item.Part_Aggregate_Id,\r\n          Type: 1,\r\n          Bom_Level: this.levelCode\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = item.Is_Stop !== null\r\n          })\r\n          this.tbData.forEach(row => {\r\n            if (stopMap[row.Part_Aggregate_Id]) {\r\n              this.$set(row, 'stopFlag', stopMap[row.Part_Aggregate_Id])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async fetchData() {\r\n      console.log('更新列表')\r\n      // 分开获取，提高接口速度\r\n      await this.getTableConfig('plm_parts_page_list')\r\n      this.tbLoading = true\r\n      this.fetchList().then((res) => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    async changePage() {\r\n      this.tbLoading = true\r\n      if (\r\n        typeof this.queryInfo.PageSize !== 'number' ||\r\n        this.queryInfo.PageSize < 1\r\n      ) {\r\n        this.queryInfo.PageSize = 10\r\n      }\r\n      this.fetchList().then((res) => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    // tbSelectChange(array) {\r\n    //   console.log('array', array)\r\n    //   this.selectList = array.records\r\n    //   console.log('this.selectList', this.selectList)\r\n    // },\r\n    getTbData(data) {\r\n      const { YearAllWeight, YearSteel, CountInfo } = data\r\n      // this.tipLabel = `累计上传构件${YearSteel}件，总重${YearAllWeight}t。`\r\n      this.tipLabel = CountInfo\r\n    },\r\n    async getTypeList() {\r\n      let res = null\r\n      let data = null\r\n      res = await GetFactoryProfessionalByCode({\r\n        factoryId: localStorage.getItem('CurReferenceId')\r\n      })\r\n      data = res.Data\r\n      if (res.IsSucceed) {\r\n        this.typeOption = Object.freeze(data)\r\n        if (this.typeOption.length > 0) {\r\n          this.Proportion = data[0].Proportion\r\n          this.Unit = data[0].Unit\r\n          this.customParams.TypeId = this.typeOption[0]?.Id\r\n          this.customParams.Type_Name = this.typeOption[0]?.Name\r\n        }\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    handleDelete() {\r\n      this.$confirm('此操作将删除选择数据, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          Deletepart({\r\n            ids: this.selectList.map((v) => v.Part_Aggregate_Id).toString()\r\n          }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.fetchData()\r\n              this.$message({\r\n                message: '删除成功',\r\n                type: 'success'\r\n              })\r\n              this.getPartWeightList()\r\n              this.fetchTreeData()\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n    handleEdit(row) {\r\n      this.width = '45%'\r\n      this.generateComponent(`编辑${this.levelName}`, 'Edit')\r\n      this.$nextTick((_) => {\r\n        row.isReadOnly = false\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n    handleBatchEdit() {\r\n      const SchedulArr = this.selectList.filter((item) => {\r\n        return item.Schduling_Count != null && item.Schduling_Count > 0\r\n      })\r\n      if (SchedulArr.length > 0) {\r\n        this.$message({\r\n          type: 'error',\r\n          message: `选中行包含已排产的${this.levelName},编辑信息需要进行变更操作`\r\n        })\r\n      } else {\r\n        this.width = '40%'\r\n        this.generateComponent('批量编辑', 'BatchEdit')\r\n        this.$nextTick((_) => {\r\n          this.$refs['content'].init(this.selectList, this.columnsOption)\r\n        })\r\n      }\r\n    },\r\n    handleView(row) {\r\n      this.width = '45%'\r\n      this.generateComponent('详情', 'Edit')\r\n      this.$nextTick((_) => {\r\n        row.isReadOnly = true\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n    async handleExport() {\r\n      const obj = {\r\n        Part_Aggregate_Ids: this.selectList\r\n          .map((v) => v.Part_Aggregate_Id)\r\n          .toString(),\r\n        ProfessionalCode: this.typeEntity.Code\r\n      }\r\n      ExportPlanpartInfo(obj).then((res) => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    // 零件拆分\r\n    partSplit() {\r\n      const Type_Name = this.selectList[0].Type_Name\r\n      const Schduling_Count = this.selectList[0].Schduling_Count\r\n      if (Type_Name === '直发件' || Schduling_Count > 0) {\r\n        this.$message({\r\n          type: 'error',\r\n          message: `${this.levelName}已排产或是直发件,无法拆分`\r\n        })\r\n        return\r\n      }\r\n      this.width = '45%'\r\n      this.generateComponent(`${this.levelName}拆分`, 'PartSplit')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(this.selectList)\r\n      })\r\n    },\r\n    modelListImport() {\r\n      const obj = {\r\n        Part_Aggregate_Ids: this.selectList\r\n          .map((v) => v.Part_Aggregate_Id)\r\n          .toString(),\r\n        ProfessionalCode: this.typeEntity.Code\r\n      }\r\n      ExportPlanpartcountInfo(obj).then((res) => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n          if (res.Message) {\r\n            this.$alert(res.Message, '导出通知', {\r\n              confirmButtonText: '我知道了'\r\n            })\r\n          }\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    // 覆盖导入 or 新增导入\r\n    handleCommand(command) {\r\n      console.log(command, 'command')\r\n      this.command = command\r\n      this.deepListImport()\r\n    },\r\n    deepListImport() {\r\n      const fileType = {\r\n        Catalog_Code: 'PLMDeepenFiles',\r\n        Code: this.typeEntity.Code,\r\n        name: this.typeEntity.Name\r\n      }\r\n      this.$refs.dialog.handleOpen(\r\n        'add',\r\n        fileType,\r\n        null,\r\n        true,\r\n        this.PID,\r\n        this.command,\r\n        this.customParams\r\n      )\r\n    },\r\n    async handleAllDelete() {\r\n      console.log(this.customParams.Project_Id)\r\n      if (this.customParams.Project_Id) {\r\n        await promptBox({ title: '删除' })\r\n        await DeletepartByfindkeywodes({\r\n          ...this.customParams,\r\n          ...this.queryInfo\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message.success('删除成功')\r\n            this.fetchData()\r\n            this.fetchTreeData()\r\n          } else {\r\n            this.$message.error(res.Message)\r\n          }\r\n        })\r\n      } else {\r\n        this.$message.warning('请先选择项目')\r\n      }\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    generateComponent(title, component) {\r\n      this.title = title\r\n      this.currentComponent = component\r\n      this.dialogVisible = true\r\n    },\r\n    // 点击搜索\r\n    handelsearch(reset, hasSearch = true) {\r\n      this.deleteContent = false\r\n      if (reset) {\r\n        this.$refs.customParams.resetFields()\r\n        this.deleteContent = true\r\n        this.names = ''\r\n      }\r\n      hasSearch && this.fetchData()\r\n      this.getPartWeightList()\r\n    },\r\n    // 深化资料查看\r\n    handleDeepMaterial(row) {\r\n      console.log('handleDeepMaterial')\r\n      this.width = '45%'\r\n      this.generateComponent('查看深化资料', 'DeepMaterial')\r\n      this.$nextTick((_) => {\r\n        row.isReadOnly = false\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n    // 排产数量\r\n    handelSchduling(row) {\r\n      this.width = '45%'\r\n      this.generateComponent('生产详情', 'Schduling')\r\n      this.$nextTick((_) => {\r\n        row.isReadOnly = false\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n    // 零件排产信息\r\n    getPartWeightList() {\r\n      this.countLoading = true\r\n      const customParamsData = JSON.parse(JSON.stringify(this.customParams))\r\n      const InstallUnit_Ids = customParamsData.InstallUnit_Id.join(',')\r\n      delete customParamsData.InstallUnit_Id\r\n      GetPartWeightList({\r\n        ...this.queryInfo,\r\n        ...customParamsData,\r\n        InstallUnit_Ids\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.SteelAmountTotal = Math.round(res.Data.DeepenNum * 1000) / 1000 // 深化总量\r\n          this.SchedulingNumTotal =\r\n            Math.round(res.Data.SchedulingNum * 1000) / 1000 // 排产总量\r\n          this.SteelAllWeightTotal =\r\n            Math.round(res.Data.DeepenWeight * 1000) / 1000 // 深化总重\r\n          this.SchedulingAllWeightTotal =\r\n            Math.round(res.Data.SchedulingWeight * 1000) / 1000 // 排产总重\r\n          this.FinishCountTotal =\r\n            Math.round(res.Data.Finish_Count * 1000) / 1000 // 完成总数\r\n          this.FinishWeightTotal =\r\n            Math.round(res.Data.Finish_Weight * 1000) / 1000 // 完成总重\r\n          console.log(' this.SteelAllWeightTotal', this.SteelAllWeightTotal)\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n        this.countLoading = false\r\n      })\r\n    },\r\n    tbSelectChange(array) {\r\n      this.selectList = array.records\r\n      this.SteelAmountTotal = 0\r\n      this.SchedulingNumTotal = 0\r\n      this.SteelAllWeightTotal = 0\r\n      this.SchedulingAllWeightTotal = 0\r\n      this.FinishCountTotal = 0\r\n      this.FinishWeightTotal = 0\r\n      let SteelAllWeightTotalTemp = 0\r\n      let SchedulingAllWeightTotalTemp = 0\r\n      let FinishWeightTotalTemp = 0\r\n      if (this.selectList.length > 0) {\r\n        this.selectList.forEach((item) => {\r\n          const schedulingNum =\r\n            item.Schduling_Count == null ? 0 : item.Schduling_Count\r\n          this.SteelAmountTotal += item.Num\r\n          this.SchedulingNumTotal += Number(item.Schduling_Count)\r\n          this.FinishCountTotal += item.Finish_Count\r\n          SteelAllWeightTotalTemp += item.Total_Weight\r\n          SchedulingAllWeightTotalTemp += item.Weight * schedulingNum\r\n          FinishWeightTotalTemp += item.Finish_Weight\r\n        })\r\n        this.SteelAllWeightTotal =\r\n          Math.round((SteelAllWeightTotalTemp / this.Proportion) * 1000) / 1000\r\n        this.SchedulingAllWeightTotal =\r\n          Math.round((SchedulingAllWeightTotalTemp / this.Proportion) * 1000) /\r\n          1000\r\n        this.FinishWeightTotal =\r\n          Math.round((FinishWeightTotalTemp / this.Proportion) * 1000) / 1000\r\n      } else {\r\n        this.getPartWeightList()\r\n      }\r\n    },\r\n    fetchTreeDataLocal() {\r\n      // this.filterText = this.projectName\r\n    },\r\n    getPartInfo(row) {\r\n      const drawingData = row.Drawing ? row.Drawing.split(',') : [] // 图纸数据\r\n      const fileUrlData = row.File_Url ? row.File_Url.split(',') : [] // 图纸数据文件地址数据\r\n      if (fileUrlData.length === 0) {\r\n        this.$message({\r\n          message: `当前${this.levelName}无图纸`,\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      if (drawingData.length > 0 && fileUrlData.length > 0) {\r\n        this.drawingActive = drawingData[0]\r\n      }\r\n      if (drawingData.length > 0 && fileUrlData.length > 0) {\r\n        this.drawingDataList = drawingData.map((item, index) => ({\r\n          name: item,\r\n          label: item,\r\n          url: fileUrlData[index]\r\n        }))\r\n      }\r\n      this.getPartInfoDrawing(row)\r\n    },\r\n\r\n    getPartInfoDrawing(row) {\r\n      const importDetailId = row.Part_Aggregate_Id\r\n      GetSteelCadAndBimId({ importDetailId: importDetailId }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const drawingData = {\r\n            'extensionName': res.Data[0].ExtensionName,\r\n            'fileBim': res.Data[0].fileBim,\r\n            'IsUpload': res.Data[0].IsUpload,\r\n            'Code': row.Code,\r\n            'Sys_Project_Id': row.Sys_Project_Id\r\n          }\r\n          this.$refs.modelDrawingRef.dwgInit(drawingData)\r\n        }\r\n      })\r\n    },\r\n\r\n    /*    handleViewDwg(row) {\r\n      if (!row.File_Url) {\r\n        this.$message({\r\n          message: '当前零件无图纸',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      window.open('http://dwgv1.bimtk.com:5432/?CadUrl=' + parseOssUrl(row.File_Url), '_blank')\r\n    },*/\r\n    customFilterFun(value, data, node) {\r\n      const arr = value.split(SPLIT_SYMBOL)\r\n      const labelVal = arr[0]\r\n      const statusVal = arr[1]\r\n      if (!value) return true\r\n      let parentNode = node.parent\r\n      let labels = [node.label]\r\n      let status = [\r\n        data.Data.Is_Deepen_Change\r\n          ? '已变更'\r\n          : data.Data.Is_Imported\r\n            ? '已导入'\r\n            : '未导入'\r\n      ]\r\n      let level = 1\r\n      while (level < node.level) {\r\n        labels = [...labels, parentNode.label]\r\n        status = [\r\n          ...status,\r\n          data.Data.Is_Deepen_Change\r\n            ? '已变更'\r\n            : data.Data.Is_Imported\r\n              ? '已导入'\r\n              : '未导入'\r\n        ]\r\n        parentNode = parentNode.parent\r\n        level++\r\n      }\r\n      labels = labels.filter((v) => !!v)\r\n      status = status.filter((v) => !!v)\r\n      let resultLabel = true\r\n      let resultStatus = true\r\n      if (this.statusType) {\r\n        resultStatus = status.some((s) => s.indexOf(statusVal) !== -1)\r\n      }\r\n      if (this.projectName) {\r\n        resultLabel = labels.some((s) => s.indexOf(labelVal) !== -1)\r\n      }\r\n      return resultLabel && resultStatus\r\n    },\r\n    getPartType() {\r\n      GetPartTypeList({ Part_Grade: 0 }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.partTypeOption = res.Data.map((v) => {\r\n            return {\r\n              label: v.Name,\r\n              value: v.Id\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async getFileType() {\r\n      const params = {\r\n        catalogCode: 'PLMDeepenFiles'\r\n      }\r\n      const res = await GetFileType(params)\r\n      // 获取构件详图\r\n      const data = res.Data.find((v) => v.Label === '零件详图')\r\n\r\n      this.comDrawData = {\r\n        isSHQD: false,\r\n        Id: data.Id,\r\n        name: data.Label,\r\n        Catalog_Code: data.Code,\r\n        Code: data.Data?.English_Name\r\n      }\r\n\r\n      console.log(this.comDrawData, 'comDrawData')\r\n    },\r\n    // 图纸导入\r\n    handelImport() {\r\n      this.$refs.comDrawdialogRef.handleOpen(\r\n        'add',\r\n        this.comDrawData,\r\n        '',\r\n        false,\r\n        this.customParams.Sys_Project_Id,\r\n        false\r\n      )\r\n    },\r\n    // 轨迹图\r\n    handleTrack(row) {\r\n      console.log(row, 'row')\r\n      this.trackDrawer = true\r\n      this.trackDrawerTitle = row.Code\r\n      this.trackDrawerData = row\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/styles/mixin.scss\";\r\n@import \"~@/styles/tabs.scss\";\r\n.min900 {\r\n  min-width: 900px;\r\n  overflow: auto;\r\n}\r\n.z-dialog {\r\n  ::v-deep {\r\n    .el-dialog__header {\r\n      background-color: #298dff;\r\n\r\n      .el-dialog__title,\r\n      .el-dialog__close {\r\n        color: #ffffff;\r\n      }\r\n    }\r\n\r\n    .el-dialog__body {\r\n      // max-height: 750px;\r\n      overflow: auto;\r\n      @include scrollBar;\r\n\r\n      &::-webkit-scrollbar {\r\n        width: 8px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.container {\r\n  padding: 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100%;\r\n}\r\n\r\n.tb-container {\r\n  padding: 0 16px 0 16px;\r\n  flex: 1;\r\n  height: 0; //解决溢出问题\r\n  // .vxe-table {\r\n  //   height: calc(100%);\r\n  // }\r\n}\r\n\r\n.cs-z-tb-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 0; //解决溢出问题\r\n}\r\n\r\n.cs-bottom {\r\n  padding: 8px 16px 8px 16px;\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: row-reverse;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  box-sizing: border-box;\r\n\r\n  .data-info {\r\n    .info-x {\r\n      margin-right: 20px;\r\n    }\r\n  }\r\n  .pg-input {\r\n    width: 100px;\r\n    margin-right: 20px;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  text-align: right;\r\n  margin: 0;\r\n  padding: 0;\r\n  ::v-deep .el-input--small .el-input__inner {\r\n    height: 28px;\r\n    line-height: 28px;\r\n  }\r\n}\r\n\r\n.cs-from {\r\n  background-color: #ffffff;\r\n  border-radius: 4px;\r\n  margin-bottom: 16px;\r\n  padding: 16px 16px 0 16px;\r\n  display: flex;\r\n  font-size: 14px;\r\n  color: rgba(34, 40, 52, 0.65);\r\n  label {\r\n    display: inline-block;\r\n    margin-right: 20px;\r\n    white-space: nowrap;\r\n    vertical-align: top;\r\n  }\r\n  .cs-from-title {\r\n    flex: 1;\r\n  }\r\n\r\n  .mb0 {\r\n    margin-bottom: 0;\r\n\r\n    ::v-deep {\r\n      .el-form-item {\r\n        margin-bottom: 0\r\n      }\r\n    }\r\n  }\r\n\r\n  .cs-search {\r\n    width: 100%;\r\n    label {\r\n      margin-bottom: 10px;\r\n    }\r\n    button {\r\n      margin-right: 10px;\r\n      margin-left: 0;\r\n      margin-bottom: 10px;\r\n    }\r\n  }\r\n}\r\n\r\n.input-with-select {\r\n  width: 250px;\r\n}\r\n\r\n.cs-button-box {\r\n  padding: 16px 16px 6px 16px;\r\n  position: relative;\r\n  background-color: #ffffff;\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  flex-wrap: wrap;\r\n\r\n  ::v-deep .el-button {\r\n    margin-left: 0 !important;\r\n    margin-right: 10px !important;\r\n    margin-bottom: 10px !important;\r\n  }\r\n}\r\n.info-box {\r\n  margin: 0 16px 16px 16px;\r\n  display: flex;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  height: 64px;\r\n  background: rgba(41, 141, 255, 0.05);\r\n\r\n  .cs-col {\r\n    display: flex;\r\n    justify-content: space-evenly;\r\n    flex-direction: column;\r\n    margin-right: 64px;\r\n  }\r\n\r\n  .info-label {\r\n    color: #999999;\r\n  }\r\n\r\n  i {\r\n    color: #00c361;\r\n    font-style: normal;\r\n    font-weight: 600;\r\n    margin-left: 10px;\r\n  }\r\n}\r\n\r\n::v-deep .el-tree-node {\r\n  min-width: 240px;\r\n  width: min-content;\r\n}\r\n::v-deep .el-tree-node > .el-tree-node__children {\r\n  overflow: inherit;\r\n}\r\n\r\n.stretch-btn {\r\n  position: absolute;\r\n  width: 20px;\r\n  height: 130px;\r\n  top: calc((100% - 130px) / 2);\r\n\r\n  display: flex;\r\n  align-items: center;\r\n  background: #eff1f3;\r\n  cursor: pointer;\r\n  .center-btn {\r\n    width: 14px;\r\n    height: 100px;\r\n    border-radius: 0 9px 9px 0;\r\n    background-color: #8c95a8;\r\n    > i {\r\n      line-height: 100px;\r\n      text-align: center;\r\n      color: #fff;\r\n    }\r\n  }\r\n}\r\n.cs-left {\r\n  position: relative;\r\n  margin-right: 20px;\r\n  .inner-wrapper {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    padding: 16px 10px 16px 16px;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n\r\n    .tree-search {\r\n      display: flex;\r\n\r\n      .search-select {\r\n        margin-right: 8px;\r\n      }\r\n    }\r\n\r\n    .tree-x {\r\n      overflow: hidden;\r\n      margin-top: 16px;\r\n      flex: 1;\r\n\r\n      .cs-scroll {\r\n        overflow-y: auto;\r\n        @include scrollBar;\r\n      }\r\n\r\n      .el-tree {\r\n        height: 100%;\r\n\r\n        //::v-deep {\r\n        //  .el-tree-node {\r\n        //    min-width: 240px;\r\n        //    width: min-content;\r\n        //\r\n        //    .el-tree-node__children {\r\n        //      overflow: inherit;\r\n        //    }\r\n        //  }\r\n        //}\r\n      }\r\n    }\r\n  }\r\n}\r\n.cs-left-contract {\r\n  padding-left: 0;\r\n  position: relative;\r\n  width: 20px;\r\n  margin-right: 26px;\r\n}\r\n.cs-right {\r\n  padding-right: 0;\r\n  flex: 1;\r\n  width: 0;\r\n}\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n.fourGreen {\r\n  color: #00c361;\r\n  font-style: normal;\r\n}\r\n\r\n.fourOrange {\r\n  color: #ff9400;\r\n  font-style: normal;\r\n}\r\n\r\n.fourRed {\r\n  color: #ff0000;\r\n  font-style: normal;\r\n}\r\n\r\n.cs-blue {\r\n  color: #5ac8fa;\r\n}\r\n\r\n.orangeBg {\r\n  background: rgba(255, 148, 0, 0.1);\r\n}\r\n\r\n.redBg {\r\n  background: rgba(252, 107, 127, 0.1);\r\n}\r\n.greenBg {\r\n  background: rgba(0, 195, 97, 0.1);\r\n}\r\n\r\n.cs-tag {\r\n  margin-left: 8px;\r\n  font-size: 12px;\r\n  padding: 2px 4px;\r\n  border-radius: 1px;\r\n}\r\n.cs-tree-x {\r\n  ::v-deep {\r\n    .el-select {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n.cs-divider {\r\n  margin: 16px 0 0 0;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0gBA,SACAA,UAAA,EACAC,iBAAA,EACAC,kBAAA,EACAC,uBAAA,EACAC,wBAAA,QACA;AACA,SAAAC,eAAA;AACA,SAAAC,aAAA;AACA,SAAAC,4BAAA;AACA,SACAC,sBAAA,EACAC,wBAAA,QACA;AAEA,OAAAC,UAAA;AACA,OAAAC,SAAA;AACA,OAAAC,SAAA;AACA,OAAAC,iBAAA;AACA,OAAAC,kBAAA;AACA,OAAAC,aAAA;AACA,OAAAC,SAAA;AACA,OAAAC,aAAA;AACA,OAAAC,IAAA;AACA,OAAAC,oBAAA;AACA,OAAAC,YAAA;AACA,OAAAC,YAAA;AACA,OAAAC,SAAA;AACA,OAAAC,SAAA;AACA,OAAAC,WAAA;AAEA,OAAAC,YAAA;AACA,OAAAC,UAAA;AACA,SAAAC,UAAA;AACA;AACA;AACA,OAAAC,WAAA;AACA,OAAAC,SAAA;AACA,OAAAC,UAAA;AACA,SAAAC,SAAA;AAEA,SAAAC,UAAA;AACA,SAAAC,aAAA;AACA,SAAAC,WAAA;AACA,SAAAC,eAAA;AACA,SAAAC,OAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AACA,SAAAC,mBAAA;AACA,SAAAC,YAAA;AACA,SAAAC,WAAA;AACA,OAAAC,iBAAA;AACA,OAAAC,aAAA;AACA,OAAAC,SAAA;AACA,SAAAC,WAAA;AACA,OAAAC,YAAA;AACA,SAAAC,UAAA;AACA;AACA,IAAAC,YAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAzB,YAAA,EAAAA,YAAA;IAAAK,UAAA,EAAAA;EAAA;EACAqB,UAAA;IACAT,iBAAA,EAAAA,iBAAA;IACAhC,UAAA,EAAAA,UAAA;IACAC,SAAA,EAAAA,SAAA;IACAC,SAAA,EAAAA,SAAA;IACAE,kBAAA,EAAAA,kBAAA;IACAE,SAAA,EAAAA,SAAA;IACAD,aAAA,EAAAA,aAAA;IACAK,YAAA,EAAAA,YAAA;IACAF,IAAA,EAAAA,IAAA;IACAD,aAAA,EAAAA,aAAA;IACAE,oBAAA,EAAAA,oBAAA;IACAO,UAAA,EAAAA,UAAA;IACAG,SAAA,EAAAA,SAAA;IACAhB,iBAAA,EAAAA,iBAAA;IACAQ,YAAA,EAAAA,YAAA;IACAC,SAAA,EAAAA,SAAA;IACAqB,aAAA,EAAAA,aAAA;IACAC,SAAA,EAAAA,SAAA;IACArB,SAAA,EAAAA,SAAA;IACAuB,YAAA,EAAAA,YAAA;IACAtB,WAAA,EAAAA;EACA;EACA4B,MAAA,GAAAxB,WAAA;EACAyB,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,UAAA;MACAC,MAAA;MACAC,UAAA;MACAC,SAAA;MACAC,YAAA;MACAC,SAAA;MACAC,SAAA;MACAC,WAAA;MAAA;MACA7B,aAAA,EAAAA,aAAA;MACA8B,cAAA;MACAC,QAAA;MACAC,WAAA;MACAC,WAAA;MACAC,UAAA;MACAC,YAAA;MACAC,MAAA;MACAC,KAAA;MACAC,SAAA;MACAC,SAAA;MACAC,YAAA;MACAC,SAAA;QACAC,IAAA;QACAC,QAAA;QACAC,aAAA;MACA;MACAC,cAAA;MACAC,qBAAA;MAAA;MACAC,QAAA;MACAC,aAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,YAAA;QACAC,MAAA;QACAC,SAAA;QACAC,IAAA;QACAC,SAAA;QACAC,IAAA;QACAC,QAAA;QACAC,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAC,SAAA;QACAC,cAAA;QACAC,YAAA;QACAC,gBAAA;QACAC,cAAA;QACAC,UAAA;QACAC,OAAA;QACAC,YAAA;QACAC,SAAA;MACA;MACAC,KAAA;MACAC,kBAAA;MACAC,aAAA;MACAC,gBAAA;MACAC,UAAA;MACAC,aAAA;MACAC,WAAA;MACAC,UAAA;MACAC,OAAA;MACAC,aAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAAA,CACA;MACAC,KAAA;MACAC,KAAA;MACAC,QAAA;MACAC,WAAA;MACAC,IAAA;MACAC,SAAA;MACAC,cAAA;MACAvF,UAAA,EAAAwF,SAAA;MACAC,aAAA;MACAC,gBAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,mBAAA;MAAA;MACAC,wBAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,IAAA;MACAC,UAAA;MAAA;MACAC,OAAA;MACAC,gBAAA;MACAC,WAAA;MACAC,WAAA;MACAC,WAAA;MACAC,WAAA;MACAC,gBAAA;MACAC,eAAA;MACAC,SAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACAC,SAAA,WAAAA,UAAA;MACA,YAAA/G,WAAA,CAAAgH,OAAA,CAAAC,IAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAvD,IAAA;MAAA;IACA;IACAwD,UAAA,WAAAA,WAAA;MAAA,IAAAC,KAAA;MACA,YAAApC,UAAA,CAAAqC,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,EAAA,KAAAH,KAAA,CAAA5D,YAAA,CAAAC,MAAA;MAAA;IACA;IACA+D,GAAA,WAAAA,IAAA;MAAA,IAAAC,qBAAA;QAAAC,MAAA;MACA,QAAAD,qBAAA,QAAA1C,WAAA,CAAAsC,IAAA,CACA,UAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAlD,cAAA,KAAAsD,MAAA,CAAAlE,YAAA,CAAAa,UAAA;MAAA,CACA,eAAAoD,qBAAA,uBAFAA,qBAAA,CAEAF,EAAA;IACA;IACAI,UAAA,WAAAA,WAAA;MACA,YAAArF,WAAA,GAAAlB,YAAA,QAAAmB,UAAA;IACA;EACA;EACAqF,KAAA;IACA,gCAAAC,mBAAAC,QAAA,EAAAC,QAAA;MACAC,OAAA,CAAAC,GAAA;QAAAF,QAAA,EAAAA;MAAA;MACA,IAAAA,QAAA,IAAAA,QAAA;QACA,KAAAG,SAAA;MACA;IACA;IACAzD,KAAA,WAAAA,MAAA0D,CAAA,EAAAC,CAAA;MACA,KAAAC,UAAA;IACA;IACAjF,QAAA,WAAAA,SAAA+E,CAAA,EAAAC,CAAA;MACA,KAAAC,UAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA,GAEA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAAC,iBAAA,EAAAC,cAAA;MAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACAhI,UAAA;UAAA;YAAA0H,iBAAA,GAAAI,QAAA,CAAAG,IAAA;YAAAN,cAAA,GAAAD,iBAAA,CAAAC,cAAA;YACAd,OAAA,CAAAC,GAAA,SAAAa,cAAA;YACAN,MAAA,CAAA5B,SAAA,GAAAkC,cAAA,aAAAA,cAAA,uBAAAA,cAAA,CAAAO,YAAA;YACAb,MAAA,CAAA3B,SAAA,GAAAiC,cAAA,aAAAA,cAAA,uBAAAA,cAAA,CAAAnF,IAAA;YAAAsF,QAAA,CAAAE,IAAA;YAAA,OACAX,MAAA,CAAAc,WAAA;UAAA;YAAAL,QAAA,CAAAE,IAAA;YAAA,OAEAX,MAAA,CAAAe,cAAA;UAAA;YAEAf,MAAA,CAAAgB,aAAA;YACAhB,MAAA,CAAAiB,WAAA;YACA,IAAAjB,MAAA,CAAAkB,eAAA;cACA1B,OAAA,CAAAC,GAAA,yBAAAO,MAAA,CAAAkB,eAAA;YACA;YACAlB,MAAA,CAAAmB,SAAA;cACAnB,MAAA,CAAA5F,SAAA;cACAoF,OAAA,CAAAC,GAAA,CAAAO,MAAA,CAAAvD,OAAA;cACAuD,MAAA,CAAAoB,iBAAA;cACApB,MAAA,CAAAqB,WAAA;cACArB,MAAA,CAAAhG,YAAA,GAAAgG,MAAA,CAAAsB,KAAA,CAAAC,SAAA,CAAAC,YAAA;YACA;UAAA;UAAA;YAAA,OAAAf,QAAA,CAAAgB,IAAA;QAAA;MAAA,GAAArB,OAAA;IAAA;EACA;EACAsB,OAAA;IACA7B,UAAA,WAAAA,WAAA;MACA,SAAAjF,QAAA;QACA,KAAAI,YAAA,CAAAI,SAAA,QAAAa,KAAA;QACA,KAAAjB,YAAA,CAAAG,IAAA;MACA;QACA,KAAAH,YAAA,CAAAI,SAAA;QACA,KAAAJ,YAAA,CAAAG,IAAA,QAAAc,KAAA,CAAA0F,OAAA;MACA;IACA;IACA;IACAX,aAAA,WAAAA,cAAA;MAAA,IAAAY,MAAA;MACAxL,sBAAA;QAAAyL,MAAA,OAAAC,MAAA,CAAAC,IAAA,CAAAhD,EAAA;QAAAiD,IAAA;QAAAlI,WAAA,OAAAA,WAAA;QAAAmI,KAAA,OAAA5D;MAAA,GAAA6D,IAAA,WAAAC,GAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAAA,GAAA,CAAAC,IAAA,CAAAC,MAAA;UACAT,MAAA,CAAA/H,WAAA;UACA+H,MAAA,CAAAxH,SAAA;UACA;QACA;QACA,IAAAkI,OAAA,GAAAH,GAAA,CAAAC,IAAA;QACAE,OAAA,CAAAC,GAAA,WAAA7D,IAAA;UACA,IAAAA,IAAA,CAAA8D,QAAA,CAAAH,MAAA;YACA3D,IAAA,CAAA+D,WAAA;UACA;YACA/D,IAAA,CAAA0D,IAAA,CAAAK,WAAA,GAAA/D,IAAA,CAAA8D,QAAA,CAAA/D,IAAA,WAAAiE,GAAA;cACA,OAAAA,GAAA,CAAAN,IAAA,CAAAK,WAAA;YACA;YACA/D,IAAA,CAAAiE,YAAA;YACAjE,IAAA,CAAA8D,QAAA,CAAAD,GAAA,WAAAK,EAAA;cACA,IAAAA,EAAA,CAAAJ,QAAA,CAAAH,MAAA;gBACAO,EAAA,CAAAD,YAAA;cACA;YACA;UACA;QACA;QACAf,MAAA,CAAAhI,QAAA,GAAA0I,OAAA;QACA,IAAAO,MAAA,CAAAC,IAAA,CAAAlB,MAAA,CAAA7D,WAAA,EAAAsE,MAAA;UACA;UACAT,MAAA,CAAAmB,MAAA;QACA;UACAnB,MAAA,CAAAoB,eAAA,CAAApB,MAAA,CAAA7D,WAAA;QACA;QACA6D,MAAA,CAAA/H,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAkJ,MAAA,WAAAA,OAAA;MAAA,IAAAE,MAAA;MACA,IAAAC,WAAA,YAAAA,WAAAC,IAAA;QACA,SAAArE,CAAA,MAAAA,CAAA,GAAAqE,IAAA,CAAAd,MAAA,EAAAvD,CAAA;UACA,IAAAJ,IAAA,GAAAyE,IAAA,CAAArE,CAAA;UACA,IAAAsD,IAAA,GAAA1D,IAAA,CAAA0D,IAAA;YAAAI,QAAA,GAAA9D,IAAA,CAAA8D,QAAA;UACAhD,OAAA,CAAAC,GAAA,CAAA2C,IAAA;UACA,IAAAA,IAAA,CAAAgB,QAAA,MAAAZ,QAAA,aAAAA,QAAA,eAAAA,QAAA,CAAAH,MAAA;YACA7C,OAAA,CAAAC,GAAA,CAAA2C,IAAA;YACAa,MAAA,CAAAlF,WAAA,GAAAqE,IAAA;YACAa,MAAA,CAAAD,eAAA,CAAAtE,IAAA;YACA;UACA;YACA,IAAA8D,QAAA,IAAAA,QAAA,CAAAH,MAAA;cACA,OAAAa,WAAA,CAAAV,QAAA;YACA;cACAS,MAAA,CAAAD,eAAA,CAAAtE,IAAA;cACA;YACA;UACA;QACA;MACA;MACA,OAAAwE,WAAA,MAAAtJ,QAAA;IACA;IACA;IACAoJ,eAAA,WAAAA,gBAAA/J,IAAA;MACA,KAAAoK,YAAA;MACA,KAAAtF,WAAA,GAAA9E,IAAA;MACA,KAAAS,WAAA,GAAAT,IAAA,CAAA8F,EAAA;MACA,IAAAuE,MAAA,GAAArK,IAAA,CAAA8F,EAAA,iBAAA9F,IAAA,CAAA8F,EAAA;MACAS,OAAA,CAAAC,GAAA,aAAAxG,IAAA;MACA,IAAAA,IAAA,CAAAsK,WAAA;QACA,KAAAvI,YAAA,CAAAa,UAAA,GAAA5C,IAAA,CAAAmJ,IAAA,CAAAvG,UAAA;QACA,KAAAb,YAAA,CAAAc,OAAA,GAAA7C,IAAA,CAAA8F,EAAA;QACA,KAAA/D,YAAA,CAAAgB,SAAA,GAAA/C,IAAA,CAAAmJ,IAAA,CAAAoB,IAAA;QACA,KAAAxI,YAAA,CAAAY,cAAA,GAAA3C,IAAA,CAAAmJ,IAAA,CAAAxG,cAAA;MACA;QACA,KAAAZ,YAAA,CAAAa,UAAA,GAAAyH,MAAA;QACA,KAAAtI,YAAA,CAAAc,OAAA;QACA,KAAAd,YAAA,CAAAgB,SAAA,GAAA/C,IAAA,CAAAmJ,IAAA,CAAAoB,IAAA;QACA,KAAAxI,YAAA,CAAAY,cAAA,GAAA3C,IAAA,CAAAmJ,IAAA,CAAAxG,cAAA;MACA;MACA4D,OAAA,CAAAC,GAAA,CACA,KAAAzE,YAAA,CAAAY,cAAA,EACA,mDACA;MACA4D,OAAA,CAAAC,GAAA,CACA,KAAAzE,YAAA,CAAAc,OAAA,EACA,4CACA;MACA,KAAA+B,gBAAA,MAAA5E,IAAA,CAAAmJ,IAAA,CAAAH,KAAA,IAAAhJ,IAAA,CAAAuJ,QAAA,CAAAH,MAAA;MACA,SAAAxE,gBAAA;QAAA,IAAA4F,UAAA;QACA,KAAAzI,YAAA,CAAAe,YAAA,IAAA0H,UAAA,GAAAxK,IAAA,CAAAmJ,IAAA,cAAAqB,UAAA,uBAAAA,UAAA,CAAA1H,YAAA;QACA,KAAAf,YAAA,CAAAgB,SAAA,GAAA/C,IAAA,CAAAyK,KAAA;MACA;MACA,KAAApJ,SAAA,CAAAC,IAAA;MACA,KAAAH,SAAA;MACA,KAAAuJ,SAAA;MACAnE,OAAA,CAAAC,GAAA,MAAAzE,YAAA,CAAAc,OAAA;MACA,KAAA8H,wBAAA,CAAAN,MAAA,EAAArK,IAAA;MACA,KAAAmI,iBAAA;IACA;IAEA;IACAwC,wBAAA,WAAAA,yBAAAC,EAAA,EAAA5K,IAAA;MAAA,IAAA6K,MAAA;MACA,IAAAD,EAAA,WAAA5K,IAAA,CAAAuJ,QAAA,CAAAH,MAAA;QACA,KAAA1H,qBAAA;MACA;QACAtE,wBAAA;UAAAyF,OAAA,EAAA+H;QAAA,GAAA3B,IAAA,WAAAC,GAAA;UACA2B,MAAA,CAAAnJ,qBAAA,GAAAwH,GAAA,CAAAC,IAAA;QACA;MACA;IACA;IACA;IACA2B,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,gBAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAApJ,YAAA;MACA,IAAAqJ,eAAA,GAAAJ,gBAAA,CAAAxI,cAAA,CAAA6I,IAAA;MACA,OAAAL,gBAAA,CAAAxI,cAAA;MAEA,KAAAmB,KAAA;MACA,KAAA2H,iBAAA,IAAAC,MAAA,MAAApG,SAAA;MACA,KAAA+C,SAAA,WAAAsD,CAAA;QACAT,MAAA,CAAA1C,KAAA,YAAAoD,IAAA,CAAAT,gBAAA,EAAAI,eAAA,EAAAL,MAAA,CAAA3H,UAAA,CAAAkG,GAAA,WAAAoC,CAAA;UAAA,OAAAA,CAAA,CAAAC,iBAAA;QAAA,GAAAC,QAAA;MACA;IACA;IACA9D,cAAA,WAAAA,eAAA+D,IAAA;MAAA,IAAAC,MAAA;MACA,WAAAC,OAAA,WAAAC,OAAA;QACA/O,aAAA;UACA4O,IAAA,EACAA,IAAA,GACA,MACAC,MAAA,CAAAvI,UAAA,CAAAqC,IAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAC,EAAA,KAAAgG,MAAA,CAAA/J,YAAA,CAAAC,MAAA;UAAA,GAAAE;QACA,GAAA+G,IAAA,WAAAC,GAAA;UACA,IAAA+C,SAAA,GAAA/C,GAAA,CAAA+C,SAAA;YAAA9C,IAAA,GAAAD,GAAA,CAAAC,IAAA;YAAA+C,OAAA,GAAAhD,GAAA,CAAAgD,OAAA;UACA,IAAAD,SAAA;YACA,KAAA9C,IAAA;cACA2C,MAAA,CAAAK,QAAA,CAAAC,KAAA;cACAN,MAAA,CAAA5K,SAAA;cACA;YACA;YACA4K,MAAA,CAAAO,QAAA,GAAAzC,MAAA,CAAA0C,MAAA,KAAAR,MAAA,CAAAO,QAAA,EAAAlD,IAAA,CAAAoD,IAAA;YACA,IAAAC,IAAA,GAAArD,IAAA,CAAAsD,UAAA;YACA,IAAAC,QAAA,GAAAF,IAAA,CAAAG,IAAA,WAAAC,CAAA,EAAAC,CAAA;cAAA,OAAAD,CAAA,CAAAE,IAAA,GAAAD,CAAA,CAAAC,IAAA;YAAA;YACAhB,MAAA,CAAAtI,OAAA,GAAAkJ,QAAA,CACAK,MAAA,WAAArB,CAAA;cAAA,OAAAA,CAAA,CAAAsB,UAAA;YAAA,GACA1D,GAAA,WAAA7D,IAAA;cACA,IAAAA,IAAA,CAAAvD,IAAA;gBACAuD,IAAA,CAAAwH,KAAA;cACA;cAEA,OAAAxH,IAAA;YACA;YACAqG,MAAA,CAAAzK,SAAA,CAAAE,QAAA,IAAA4H,IAAA,CAAAoD,IAAA,CAAAW,UAAA;YACAlB,OAAA,CAAAF,MAAA,CAAAtI,OAAA;YACA+C,OAAA,CAAAC,GAAA,CAAAsF,MAAA,CAAAtI,OAAA;YACA,IAAA2J,YAAA,GAAAlC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAW,MAAA,CAAAtI,OAAA;YACA+C,OAAA,CAAAC,GAAA,CAAA2G,YAAA;YACArB,MAAA,CAAArI,aAAA,GAAA0J,YAAA,CAAAJ,MAAA,WAAArB,CAAA;cACA,OACAA,CAAA,CAAA9D,YAAA,eACA8D,CAAA,CAAA9D,YAAA,eACA8D,CAAA,CAAA9D,YAAA,eACA8D,CAAA,CAAA9D,YAAA,aACA8D,CAAA,CAAA9D,YAAA,eACA8D,CAAA,CAAAxJ,IAAA,CAAAkL,OAAA,mBACA1B,CAAA,CAAA9D,YAAA;YAEA;UACA;YACAkE,MAAA,CAAAK,QAAA;cACAkB,OAAA,EAAAnB,OAAA;cACAoB,IAAA;YACA;UACA;QACA;MACA;IACA;IACA5C,SAAA,WAAAA,UAAA;MAAA,IAAA6C,MAAA;MAAA,OAAAvG,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAsG,SAAA;QAAA,IAAAxC,gBAAA,EAAAI,eAAA;QAAA,OAAAnE,mBAAA,GAAAK,IAAA,UAAAmG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjG,IAAA,GAAAiG,SAAA,CAAAhG,IAAA;YAAA;cACAsD,gBAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAoC,MAAA,CAAAxL,YAAA;cACAqJ,eAAA,GAAAJ,gBAAA,CAAAxI,cAAA,CAAA6I,IAAA;cACA,OAAAL,gBAAA,CAAAxI,cAAA;cAAAkL,SAAA,CAAAhG,IAAA;cAAA,OACA1K,eAAA,CAAA2Q,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACAJ,MAAA,CAAAlM,SAAA,GACA2J,gBAAA;gBACA9I,IAAA,EAAA8I,gBAAA,CAAA9I,IAAA,CAAA0L,IAAA,GAAAC,UAAA;gBACAzC,eAAA,EAAAA;cAAA,EACA,EACAnC,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAA+C,SAAA;kBACAsB,MAAA,CAAAlM,SAAA,CAAAE,QAAA,GAAA2H,GAAA,CAAAC,IAAA,CAAA5H,QAAA;kBACAgM,MAAA,CAAAtM,KAAA,GAAAiI,GAAA,CAAAC,IAAA,CAAA2E,UAAA;kBACAP,MAAA,CAAAvM,MAAA,GAAAkI,GAAA,CAAAC,IAAA,CAAAA,IAAA,CAAAG,GAAA,WAAAoC,CAAA;oBACAA,CAAA,CAAAqC,OAAA,GAAArC,CAAA,CAAAqC,OAAA;oBACArC,CAAA,CAAAsC,MAAA,GAAA1P,UAAA,CAAAoN,CAAA,CAAAsC,MAAA;oBACA;oBACA,OAAAtC,CAAA;kBACA;kBACA6B,MAAA,CAAAnK,UAAA;kBACAmK,MAAA,CAAAU,WAAA;gBACA;kBACAV,MAAA,CAAApB,QAAA;oBACAkB,OAAA,EAAAnE,GAAA,CAAAgD,OAAA;oBACAoB,IAAA;kBACA;gBACA;cACA,GACAY,OAAA;gBACAX,MAAA,CAAArM,SAAA;gBACAqM,MAAA,CAAApM,SAAA;cACA;YAAA;YAAA;cAAA,OAAAuM,SAAA,CAAAlF,IAAA;UAAA;QAAA,GAAAgF,QAAA;MAAA;IACA;IACAS,WAAA,WAAAA,YAAA;MAAA,IAAAE,MAAA;MAAA,OAAAnH,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkH,SAAA;QAAA,IAAAC,SAAA;QAAA,OAAApH,mBAAA,GAAAK,IAAA,UAAAgH,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9G,IAAA,GAAA8G,SAAA,CAAA7G,IAAA;YAAA;cACA2G,SAAA,GAAAF,MAAA,CAAAnN,MAAA,CAAAsI,GAAA,WAAA7D,IAAA;gBACA;kBACAK,EAAA,EAAAL,IAAA,CAAAkG,iBAAA;kBACA5C,IAAA;kBACAyF,SAAA,EAAAL,MAAA,CAAA/I;gBACA;cACA;cAAAmJ,SAAA,CAAA7G,IAAA;cAAA,OACAlI,WAAA,CAAA6O,SAAA,EAAApF,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAA+C,SAAA;kBACA,IAAAwC,OAAA;kBACAvF,GAAA,CAAAC,IAAA,CAAAuF,OAAA,WAAAjJ,IAAA;oBACAgJ,OAAA,CAAAhJ,IAAA,CAAAK,EAAA,IAAAL,IAAA,CAAAkJ,OAAA;kBACA;kBACAR,MAAA,CAAAnN,MAAA,CAAA0N,OAAA,WAAAE,GAAA;oBACA,IAAAH,OAAA,CAAAG,GAAA,CAAAjD,iBAAA;sBACAwC,MAAA,CAAAU,IAAA,CAAAD,GAAA,cAAAH,OAAA,CAAAG,GAAA,CAAAjD,iBAAA;oBACA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA4C,SAAA,CAAA/F,IAAA;UAAA;QAAA,GAAA4F,QAAA;MAAA;IACA;IACA3H,SAAA,WAAAA,UAAA;MAAA,IAAAqI,MAAA;MAAA,OAAA9H,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6H,SAAA;QAAA,OAAA9H,mBAAA,GAAAK,IAAA,UAAA0H,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxH,IAAA,GAAAwH,SAAA,CAAAvH,IAAA;YAAA;cACAnB,OAAA,CAAAC,GAAA;cACA;cAAAyI,SAAA,CAAAvH,IAAA;cAAA,OACAoH,MAAA,CAAAhH,cAAA;YAAA;cACAgH,MAAA,CAAA5N,SAAA;cACA4N,MAAA,CAAApE,SAAA,GAAAzB,IAAA,WAAAC,GAAA;gBACA4F,MAAA,CAAA5N,SAAA;cACA;YAAA;YAAA;cAAA,OAAA+N,SAAA,CAAAzG,IAAA;UAAA;QAAA,GAAAuG,QAAA;MAAA;IACA;IACAG,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MAAA,OAAAnI,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkI,SAAA;QAAA,OAAAnI,mBAAA,GAAAK,IAAA,UAAA+H,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7H,IAAA,GAAA6H,SAAA,CAAA5H,IAAA;YAAA;cACAyH,OAAA,CAAAjO,SAAA;cACA,IACA,OAAAiO,OAAA,CAAA9N,SAAA,CAAAE,QAAA,iBACA4N,OAAA,CAAA9N,SAAA,CAAAE,QAAA,MACA;gBACA4N,OAAA,CAAA9N,SAAA,CAAAE,QAAA;cACA;cACA4N,OAAA,CAAAzE,SAAA,GAAAzB,IAAA,WAAAC,GAAA;gBACAiG,OAAA,CAAAjO,SAAA;cACA;YAAA;YAAA;cAAA,OAAAoO,SAAA,CAAA9G,IAAA;UAAA;QAAA,GAAA4G,QAAA;MAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACAG,SAAA,WAAAA,UAAAvP,IAAA;MACA,IAAAwP,aAAA,GAAAxP,IAAA,CAAAwP,aAAA;QAAAC,SAAA,GAAAzP,IAAA,CAAAyP,SAAA;QAAAC,SAAA,GAAA1P,IAAA,CAAA0P,SAAA;MACA;MACA,KAAA9L,QAAA,GAAA8L,SAAA;IACA;IACA7H,WAAA,WAAAA,YAAA;MAAA,IAAA8H,OAAA;MAAA,OAAA3I,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA0I,SAAA;QAAA,IAAA1G,GAAA,EAAAlJ,IAAA,EAAA6P,mBAAA,EAAAC,oBAAA;QAAA,OAAA7I,mBAAA,GAAAK,IAAA,UAAAyI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvI,IAAA,GAAAuI,SAAA,CAAAtI,IAAA;YAAA;cACAwB,GAAA;cACAlJ,IAAA;cAAAgQ,SAAA,CAAAtI,IAAA;cAAA,OACAxK,4BAAA;gBACA+S,SAAA,EAAAC,YAAA,CAAAC,OAAA;cACA;YAAA;cAFAjH,GAAA,GAAA8G,SAAA,CAAArI,IAAA;cAGA3H,IAAA,GAAAkJ,GAAA,CAAAC,IAAA;cACA,IAAAD,GAAA,CAAA+C,SAAA;gBACA0D,OAAA,CAAApM,UAAA,GAAAqG,MAAA,CAAAwG,MAAA,CAAApQ,IAAA;gBACA,IAAA2P,OAAA,CAAApM,UAAA,CAAA6F,MAAA;kBACAuG,OAAA,CAAAjL,UAAA,GAAA1E,IAAA,IAAA0E,UAAA;kBACAiL,OAAA,CAAAlL,IAAA,GAAAzE,IAAA,IAAAyE,IAAA;kBACAkL,OAAA,CAAA5N,YAAA,CAAAC,MAAA,IAAA6N,mBAAA,GAAAF,OAAA,CAAApM,UAAA,iBAAAsM,mBAAA,uBAAAA,mBAAA,CAAA/J,EAAA;kBACA6J,OAAA,CAAA5N,YAAA,CAAAE,SAAA,IAAA6N,oBAAA,GAAAH,OAAA,CAAApM,UAAA,iBAAAuM,oBAAA,uBAAAA,oBAAA,CAAAvF,IAAA;gBACA;cACA;gBACAoF,OAAA,CAAAxD,QAAA;kBACAkB,OAAA,EAAAnE,GAAA,CAAAgD,OAAA;kBACAoB,IAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA0C,SAAA,CAAAxH,IAAA;UAAA;QAAA,GAAAoH,QAAA;MAAA;IACA;IACAS,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAnD,IAAA;MACA,GACArE,IAAA;QACAtM,UAAA;UACA+T,GAAA,EAAAJ,OAAA,CAAAlN,UAAA,CAAAkG,GAAA,WAAAoC,CAAA;YAAA,OAAAA,CAAA,CAAAC,iBAAA;UAAA,GAAAC,QAAA;QACA,GAAA3C,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAA+C,SAAA;YACAqE,OAAA,CAAA7J,SAAA;YACA6J,OAAA,CAAAnE,QAAA;cACAkB,OAAA;cACAC,IAAA;YACA;YACAgD,OAAA,CAAAnI,iBAAA;YACAmI,OAAA,CAAAvI,aAAA;UACA;YACAuI,OAAA,CAAAnE,QAAA;cACAkB,OAAA,EAAAnE,GAAA,CAAAgD,OAAA;cACAoB,IAAA;YACA;UACA;QACA;MACA,GACAqD,KAAA;QACAL,OAAA,CAAAnE,QAAA;UACAmB,IAAA;UACAD,OAAA;QACA;MACA;IACA;IACAuD,UAAA,WAAAA,WAAAhC,GAAA;MAAA,IAAAiC,OAAA;MACA,KAAAlN,KAAA;MACA,KAAA2H,iBAAA,gBAAAC,MAAA,MAAApG,SAAA;MACA,KAAA+C,SAAA,WAAAsD,CAAA;QACAoD,GAAA,CAAAkC,UAAA;QACAD,OAAA,CAAAxI,KAAA,YAAAoD,IAAA,CAAAmD,GAAA;MACA;IACA;IACAmC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,UAAA,QAAA7N,UAAA,CAAA2J,MAAA,WAAAtH,IAAA;QACA,OAAAA,IAAA,CAAAyL,eAAA,YAAAzL,IAAA,CAAAyL,eAAA;MACA;MACA,IAAAD,UAAA,CAAA7H,MAAA;QACA,KAAA+C,QAAA;UACAmB,IAAA;UACAD,OAAA,2DAAA9B,MAAA,MAAApG,SAAA;QACA;MACA;QACA,KAAAxB,KAAA;QACA,KAAA2H,iBAAA;QACA,KAAApD,SAAA,WAAAsD,CAAA;UACAwF,OAAA,CAAA3I,KAAA,YAAAoD,IAAA,CAAAuF,OAAA,CAAA5N,UAAA,EAAA4N,OAAA,CAAAvN,aAAA;QACA;MACA;IACA;IACA0N,UAAA,WAAAA,WAAAvC,GAAA;MAAA,IAAAwC,OAAA;MACA,KAAAzN,KAAA;MACA,KAAA2H,iBAAA;MACA,KAAApD,SAAA,WAAAsD,CAAA;QACAoD,GAAA,CAAAkC,UAAA;QACAM,OAAA,CAAA/I,KAAA,YAAAoD,IAAA,CAAAmD,GAAA;MACA;IACA;IACAyC,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,OAAAtK,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAqK,SAAA;QAAA,IAAAC,GAAA;QAAA,OAAAvK,mBAAA,GAAAK,IAAA,UAAAmK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjK,IAAA,GAAAiK,SAAA,CAAAhK,IAAA;YAAA;cACA8J,GAAA;gBACAG,kBAAA,EAAAL,OAAA,CAAAlO,UAAA,CACAkG,GAAA,WAAAoC,CAAA;kBAAA,OAAAA,CAAA,CAAAC,iBAAA;gBAAA,GACAC,QAAA;gBACAgG,gBAAA,EAAAN,OAAA,CAAA5L,UAAA,CAAAxD;cACA;cACArF,kBAAA,CAAA2U,GAAA,EAAAvI,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAA+C,SAAA;kBACA4F,MAAA,CAAAC,IAAA,CAAAnT,UAAA,CAAA2S,OAAA,CAAAS,QAAA,EAAA7I,GAAA,CAAAC,IAAA;gBACA;kBACAmI,OAAA,CAAAnF,QAAA,CAAAC,KAAA,CAAAlD,GAAA,CAAAgD,OAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAwF,SAAA,CAAAlJ,IAAA;UAAA;QAAA,GAAA+I,QAAA;MAAA;IACA;IACA;IACAS,SAAA,WAAAA,UAAA;MAAA,IAAAC,OAAA;MACA,IAAAhQ,SAAA,QAAAmB,UAAA,IAAAnB,SAAA;MACA,IAAAiP,eAAA,QAAA9N,UAAA,IAAA8N,eAAA;MACA,IAAAjP,SAAA,cAAAiP,eAAA;QACA,KAAA/E,QAAA;UACAmB,IAAA;UACAD,OAAA,KAAA9B,MAAA,MAAApG,SAAA;QACA;QACA;MACA;MACA,KAAAxB,KAAA;MACA,KAAA2H,iBAAA,IAAAC,MAAA,MAAApG,SAAA;MACA,KAAA+C,SAAA,WAAAsD,CAAA;QACAyG,OAAA,CAAA5J,KAAA,YAAAoD,IAAA,CAAAwG,OAAA,CAAA7O,UAAA;MACA;IACA;IACA8O,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MACA,IAAAX,GAAA;QACAG,kBAAA,OAAAvO,UAAA,CACAkG,GAAA,WAAAoC,CAAA;UAAA,OAAAA,CAAA,CAAAC,iBAAA;QAAA,GACAC,QAAA;QACAgG,gBAAA,OAAAlM,UAAA,CAAAxD;MACA;MACApF,uBAAA,CAAA0U,GAAA,EAAAvI,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAA+C,SAAA;UACA4F,MAAA,CAAAC,IAAA,CAAAnT,UAAA,CAAAwT,OAAA,CAAAJ,QAAA,EAAA7I,GAAA,CAAAC,IAAA;UACA,IAAAD,GAAA,CAAAgD,OAAA;YACAiG,OAAA,CAAAC,MAAA,CAAAlJ,GAAA,CAAAgD,OAAA;cACAsE,iBAAA;YACA;UACA;QACA;UACA2B,OAAA,CAAAhG,QAAA,CAAAC,KAAA,CAAAlD,GAAA,CAAAgD,OAAA;QACA;MACA;IACA;IACA;IACAmG,aAAA,WAAAA,cAAA1N,OAAA;MACA4B,OAAA,CAAAC,GAAA,CAAA7B,OAAA;MACA,KAAAA,OAAA,GAAAA,OAAA;MACA,KAAA2N,cAAA;IACA;IACAA,cAAA,WAAAA,eAAA;MACA,IAAAC,QAAA;QACAC,YAAA;QACAtQ,IAAA,OAAAwD,UAAA,CAAAxD,IAAA;QACAtC,IAAA,OAAA8F,UAAA,CAAA6E;MACA;MACA,KAAAlC,KAAA,CAAAoK,MAAA,CAAAC,UAAA,CACA,OACAH,QAAA,EACA,MACA,MACA,KAAAxM,GAAA,EACA,KAAApB,OAAA,EACA,KAAA5C,YACA;IACA;IACA4Q,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MAAA,OAAA5L,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA2L,SAAA;QAAA,OAAA5L,mBAAA,GAAAK,IAAA,UAAAwL,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtL,IAAA,GAAAsL,SAAA,CAAArL,IAAA;YAAA;cACAnB,OAAA,CAAAC,GAAA,CAAAoM,OAAA,CAAA7Q,YAAA,CAAAa,UAAA;cAAA,KACAgQ,OAAA,CAAA7Q,YAAA,CAAAa,UAAA;gBAAAmQ,SAAA,CAAArL,IAAA;gBAAA;cAAA;cAAAqL,SAAA,CAAArL,IAAA;cAAA,OACAhJ,SAAA;gBAAAgF,KAAA;cAAA;YAAA;cAAAqP,SAAA,CAAArL,IAAA;cAAA,OACA3K,wBAAA,CAAA4Q,aAAA,CAAAA,aAAA,KACAiF,OAAA,CAAA7Q,YAAA,GACA6Q,OAAA,CAAAvR,SAAA,CACA,EAAA4H,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAA+C,SAAA;kBACA2G,OAAA,CAAAzG,QAAA,CAAA6G,OAAA;kBACAJ,OAAA,CAAAnM,SAAA;kBACAmM,OAAA,CAAA7K,aAAA;gBACA;kBACA6K,OAAA,CAAAzG,QAAA,CAAAC,KAAA,CAAAlD,GAAA,CAAAgD,OAAA;gBACA;cACA;YAAA;cAAA6G,SAAA,CAAArL,IAAA;cAAA;YAAA;cAEAkL,OAAA,CAAAzG,QAAA,CAAA8G,OAAA;YAAA;YAAA;cAAA,OAAAF,SAAA,CAAAvK,IAAA;UAAA;QAAA,GAAAqK,QAAA;MAAA;IAEA;IACAK,WAAA,WAAAA,YAAA;MACA,KAAAhQ,aAAA;IACA;IACAoI,iBAAA,WAAAA,kBAAA5H,KAAA,EAAAyP,SAAA;MACA,KAAAzP,KAAA,GAAAA,KAAA;MACA,KAAAP,gBAAA,GAAAgQ,SAAA;MACA,KAAAjQ,aAAA;IACA;IACA;IACAkH,YAAA,WAAAA,aAAAgJ,KAAA;MAAA,IAAAC,SAAA,GAAAC,SAAA,CAAAlK,MAAA,QAAAkK,SAAA,QAAArP,SAAA,GAAAqP,SAAA;MACA,KAAApP,aAAA;MACA,IAAAkP,KAAA;QACA,KAAA/K,KAAA,CAAAtG,YAAA,CAAAwR,WAAA;QACA,KAAArP,aAAA;QACA,KAAAlB,KAAA;MACA;MACAqQ,SAAA,SAAA5M,SAAA;MACA,KAAA0B,iBAAA;IACA;IACA;IACAqL,kBAAA,WAAAA,mBAAA5E,GAAA;MAAA,IAAA6E,OAAA;MACAlN,OAAA,CAAAC,GAAA;MACA,KAAA7C,KAAA;MACA,KAAA2H,iBAAA;MACA,KAAApD,SAAA,WAAAsD,CAAA;QACAoD,GAAA,CAAAkC,UAAA;QACA2C,OAAA,CAAApL,KAAA,YAAAoD,IAAA,CAAAmD,GAAA;MACA;IACA;IACA;IACA8E,eAAA,WAAAA,gBAAA9E,GAAA;MAAA,IAAA+E,OAAA;MACA,KAAAhQ,KAAA;MACA,KAAA2H,iBAAA;MACA,KAAApD,SAAA,WAAAsD,CAAA;QACAoD,GAAA,CAAAkC,UAAA;QACA6C,OAAA,CAAAtL,KAAA,YAAAoD,IAAA,CAAAmD,GAAA;MACA;IACA;IACA;IACAzG,iBAAA,WAAAA,kBAAA;MAAA,IAAAyL,OAAA;MACA,KAAAxS,YAAA;MACA,IAAA4J,gBAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAApJ,YAAA;MACA,IAAAqJ,eAAA,GAAAJ,gBAAA,CAAAxI,cAAA,CAAA6I,IAAA;MACA,OAAAL,gBAAA,CAAAxI,cAAA;MACA5F,iBAAA,CAAA+Q,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACA,KAAAtM,SAAA,GACA2J,gBAAA;QACAI,eAAA,EAAAA;MAAA,EACA,EAAAnC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAA+C,SAAA;UACA2H,OAAA,CAAAzP,gBAAA,GAAA0P,IAAA,CAAAC,KAAA,CAAA5K,GAAA,CAAAC,IAAA,CAAA4K,SAAA;UACAH,OAAA,CAAAxP,kBAAA,GACAyP,IAAA,CAAAC,KAAA,CAAA5K,GAAA,CAAAC,IAAA,CAAA6K,aAAA;UACAJ,OAAA,CAAAvP,mBAAA,GACAwP,IAAA,CAAAC,KAAA,CAAA5K,GAAA,CAAAC,IAAA,CAAA8K,YAAA;UACAL,OAAA,CAAAtP,wBAAA,GACAuP,IAAA,CAAAC,KAAA,CAAA5K,GAAA,CAAAC,IAAA,CAAA+K,gBAAA;UACAN,OAAA,CAAArP,gBAAA,GACAsP,IAAA,CAAAC,KAAA,CAAA5K,GAAA,CAAAC,IAAA,CAAAgL,YAAA;UACAP,OAAA,CAAApP,iBAAA,GACAqP,IAAA,CAAAC,KAAA,CAAA5K,GAAA,CAAAC,IAAA,CAAAiL,aAAA;UACA7N,OAAA,CAAAC,GAAA,8BAAAoN,OAAA,CAAAvP,mBAAA;QACA;UACAuP,OAAA,CAAAzH,QAAA,CAAAC,KAAA,CAAAlD,GAAA,CAAAgD,OAAA;QACA;QACA0H,OAAA,CAAAxS,YAAA;MACA;IACA;IACAiT,cAAA,WAAAA,eAAAC,KAAA;MAAA,IAAAC,OAAA;MACA,KAAAnR,UAAA,GAAAkR,KAAA,CAAAE,OAAA;MACA,KAAArQ,gBAAA;MACA,KAAAC,kBAAA;MACA,KAAAC,mBAAA;MACA,KAAAC,wBAAA;MACA,KAAAC,gBAAA;MACA,KAAAC,iBAAA;MACA,IAAAiQ,uBAAA;MACA,IAAAC,4BAAA;MACA,IAAAC,qBAAA;MACA,SAAAvR,UAAA,CAAAgG,MAAA;QACA,KAAAhG,UAAA,CAAAsL,OAAA,WAAAjJ,IAAA;UACA,IAAAmP,aAAA,GACAnP,IAAA,CAAAyL,eAAA,eAAAzL,IAAA,CAAAyL,eAAA;UACAqD,OAAA,CAAApQ,gBAAA,IAAAsB,IAAA,CAAAoP,GAAA;UACAN,OAAA,CAAAnQ,kBAAA,IAAA0Q,MAAA,CAAArP,IAAA,CAAAyL,eAAA;UACAqD,OAAA,CAAAhQ,gBAAA,IAAAkB,IAAA,CAAA0O,YAAA;UACAM,uBAAA,IAAAhP,IAAA,CAAAsP,YAAA;UACAL,4BAAA,IAAAjP,IAAA,CAAAuP,MAAA,GAAAJ,aAAA;UACAD,qBAAA,IAAAlP,IAAA,CAAA2O,aAAA;QACA;QACA,KAAA/P,mBAAA,GACAwP,IAAA,CAAAC,KAAA,CAAAW,uBAAA,QAAA/P,UAAA;QACA,KAAAJ,wBAAA,GACAuP,IAAA,CAAAC,KAAA,CAAAY,4BAAA,QAAAhQ,UAAA,WACA;QACA,KAAAF,iBAAA,GACAqP,IAAA,CAAAC,KAAA,CAAAa,qBAAA,QAAAjQ,UAAA;MACA;QACA,KAAAyD,iBAAA;MACA;IACA;IACA8M,kBAAA,WAAAA,mBAAA;MACA;IAAA,CACA;IACAC,WAAA,WAAAA,YAAAtG,GAAA;MACA,IAAAuG,WAAA,GAAAvG,GAAA,CAAAwG,OAAA,GAAAxG,GAAA,CAAAwG,OAAA,CAAAC,KAAA;MACA,IAAAC,WAAA,GAAA1G,GAAA,CAAA2G,QAAA,GAAA3G,GAAA,CAAA2G,QAAA,CAAAF,KAAA;MACA,IAAAC,WAAA,CAAAlM,MAAA;QACA,KAAA+C,QAAA;UACAkB,OAAA,iBAAA9B,MAAA,MAAApG,SAAA;UACAmI,IAAA;QACA;QACA;MACA;MACA,IAAA6H,WAAA,CAAA/L,MAAA,QAAAkM,WAAA,CAAAlM,MAAA;QACA,KAAAoM,aAAA,GAAAL,WAAA;MACA;MACA,IAAAA,WAAA,CAAA/L,MAAA,QAAAkM,WAAA,CAAAlM,MAAA;QACA,KAAAqM,eAAA,GAAAN,WAAA,CAAA7L,GAAA,WAAA7D,IAAA,EAAAiQ,KAAA;UAAA;YACA9V,IAAA,EAAA6F,IAAA;YACA3D,KAAA,EAAA2D,IAAA;YACAkQ,GAAA,EAAAL,WAAA,CAAAI,KAAA;UACA;QAAA;MACA;MACA,KAAAE,kBAAA,CAAAhH,GAAA;IACA;IAEAgH,kBAAA,WAAAA,mBAAAhH,GAAA;MAAA,IAAAiH,OAAA;MACA,IAAAC,cAAA,GAAAlH,GAAA,CAAAjD,iBAAA;MACAzM,mBAAA;QAAA4W,cAAA,EAAAA;MAAA,GAAA7M,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAA+C,SAAA;UACA,IAAAkJ,WAAA;YACA,iBAAAjM,GAAA,CAAAC,IAAA,IAAA4M,aAAA;YACA,WAAA7M,GAAA,CAAAC,IAAA,IAAA6M,OAAA;YACA,YAAA9M,GAAA,CAAAC,IAAA,IAAA8M,QAAA;YACA,QAAArH,GAAA,CAAA1M,IAAA;YACA,kBAAA0M,GAAA,CAAAjM;UACA;UACAkT,OAAA,CAAAxN,KAAA,CAAA6N,eAAA,CAAAC,OAAA,CAAAhB,WAAA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAiB,eAAA,WAAAA,gBAAAvU,KAAA,EAAA7B,IAAA,EAAAqW,IAAA;MACA,IAAAC,GAAA,GAAAzU,KAAA,CAAAwT,KAAA,CAAA1V,YAAA;MACA,IAAA4W,QAAA,GAAAD,GAAA;MACA,IAAAE,SAAA,GAAAF,GAAA;MACA,KAAAzU,KAAA;MACA,IAAA4U,UAAA,GAAAJ,IAAA,CAAAK,MAAA;MACA,IAAAC,MAAA,IAAAN,IAAA,CAAAvU,KAAA;MACA,IAAA8U,MAAA,IACA5W,IAAA,CAAAmJ,IAAA,CAAA0N,gBAAA,GACA,QACA7W,IAAA,CAAAmJ,IAAA,CAAAK,WAAA,GACA,QACA,MACA;MACA,IAAAsN,KAAA;MACA,OAAAA,KAAA,GAAAT,IAAA,CAAAS,KAAA;QACAH,MAAA,MAAApL,MAAA,CAAAwL,kBAAA,CAAAJ,MAAA,IAAAF,UAAA,CAAA3U,KAAA;QACA8U,MAAA,MAAArL,MAAA,CAAAwL,kBAAA,CACAH,MAAA,IACA5W,IAAA,CAAAmJ,IAAA,CAAA0N,gBAAA,GACA,QACA7W,IAAA,CAAAmJ,IAAA,CAAAK,WAAA,GACA,QACA,OACA;QACAiN,UAAA,GAAAA,UAAA,CAAAC,MAAA;QACAI,KAAA;MACA;MACAH,MAAA,GAAAA,MAAA,CAAA5J,MAAA,WAAArB,CAAA;QAAA,SAAAA,CAAA;MAAA;MACAkL,MAAA,GAAAA,MAAA,CAAA7J,MAAA,WAAArB,CAAA;QAAA,SAAAA,CAAA;MAAA;MACA,IAAAsL,WAAA;MACA,IAAAC,YAAA;MACA,SAAAnW,UAAA;QACAmW,YAAA,GAAAL,MAAA,CAAApR,IAAA,WAAA0R,CAAA;UAAA,OAAAA,CAAA,CAAA9J,OAAA,CAAAoJ,SAAA;QAAA;MACA;MACA,SAAA3V,WAAA;QACAmW,WAAA,GAAAL,MAAA,CAAAnR,IAAA,WAAA0R,CAAA;UAAA,OAAAA,CAAA,CAAA9J,OAAA,CAAAmJ,QAAA;QAAA;MACA;MACA,OAAAS,WAAA,IAAAC,YAAA;IACA;IACA7O,WAAA,WAAAA,YAAA;MAAA,IAAA+O,OAAA;MACArY,eAAA;QAAAsY,UAAA;MAAA,GAAAnO,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAA+C,SAAA;UACAkL,OAAA,CAAAzW,cAAA,GAAAwI,GAAA,CAAAC,IAAA,CAAAG,GAAA,WAAAoC,CAAA;YACA;cACA5J,KAAA,EAAA4J,CAAA,CAAAnB,IAAA;cACA1I,KAAA,EAAA6J,CAAA,CAAA5F;YACA;UACA;QACA;UACAqR,OAAA,CAAAhL,QAAA;YACAkB,OAAA,EAAAnE,GAAA,CAAAgD,OAAA;YACAoB,IAAA;UACA;QACA;MACA;IACA;IACAtF,WAAA,WAAAA,YAAA;MAAA,IAAAqP,OAAA;MAAA,OAAArQ,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAoQ,SAAA;QAAA,IAAAC,WAAA;QAAA,IAAAC,MAAA,EAAAtO,GAAA,EAAAlJ,IAAA;QAAA,OAAAiH,mBAAA,GAAAK,IAAA,UAAAmQ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjQ,IAAA,GAAAiQ,SAAA,CAAAhQ,IAAA;YAAA;cACA8P,MAAA;gBACAG,WAAA;cACA;cAAAD,SAAA,CAAAhQ,IAAA;cAAA,OACAtI,WAAA,CAAAoY,MAAA;YAAA;cAAAtO,GAAA,GAAAwO,SAAA,CAAA/P,IAAA;cACA;cACA3H,IAAA,GAAAkJ,GAAA,CAAAC,IAAA,CAAAvD,IAAA,WAAA8F,CAAA;gBAAA,OAAAA,CAAA,CAAAjB,KAAA;cAAA;cAEA4M,OAAA,CAAAtS,WAAA;gBACA6S,MAAA;gBACA9R,EAAA,EAAA9F,IAAA,CAAA8F,EAAA;gBACAlG,IAAA,EAAAI,IAAA,CAAAyK,KAAA;gBACA+H,YAAA,EAAAxS,IAAA,CAAAkC,IAAA;gBACAA,IAAA,GAAAqV,WAAA,GAAAvX,IAAA,CAAAmJ,IAAA,cAAAoO,WAAA,uBAAAA,WAAA,CAAAM;cACA;cAEAtR,OAAA,CAAAC,GAAA,CAAA6Q,OAAA,CAAAtS,WAAA;YAAA;YAAA;cAAA,OAAA2S,SAAA,CAAAlP,IAAA;UAAA;QAAA,GAAA8O,QAAA;MAAA;IACA;IACA;IACAQ,YAAA,WAAAA,aAAA;MACA,KAAAzP,KAAA,CAAA0P,gBAAA,CAAArF,UAAA,CACA,OACA,KAAA3N,WAAA,EACA,IACA,OACA,KAAAhD,YAAA,CAAAY,cAAA,EACA,KACA;IACA;IACA;IACAqV,WAAA,WAAAA,YAAApJ,GAAA;MACArI,OAAA,CAAAC,GAAA,CAAAoI,GAAA;MACA,KAAA5J,WAAA;MACA,KAAAC,gBAAA,GAAA2J,GAAA,CAAA1M,IAAA;MACA,KAAAgD,eAAA,GAAA0J,GAAA;IACA;EACA;AACA", "ignoreList": []}]}
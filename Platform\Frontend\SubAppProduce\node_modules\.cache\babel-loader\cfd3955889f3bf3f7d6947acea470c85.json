{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-unit-part\\constant.js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-unit-part\\constant.js", "mtime": 1758266753119}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuZnVuY3Rpb24ubmFtZS5qcyI7Ci8qKg0KICogQERlc2NyaXB0aW9uOiDojrflj5bpobXpnaLot6/nlLHot7PovawNCiAqIEBhdXRob3I6IHlpbmd6eQ0KICogQGRhdGUgMjAyMi85LzEzDQogKiBAcGFyYW0geyFzdHJpbmd9IG5hbWUg6Lev55Sx5ZCN56ewDQogKiBAcGFyYW0geygnYWRkJ3wnZWRpdCd8J3ZpZXcnKX0gc3RhdHVzIOmhtemdoueKtuaAgeaWsOWini/nvJbovpENCiAqIEBwYXJhbSB7KCdjb20nfCdwYXJ0Jyl9IHBhZ2VUeXBlIOmbtuaehOS7tuexu+Weiw0KICogQHBhcmFtIHtPYmplY3R9IFtxdWVyeV0g6aKd5aSWcXVlcnnlj4LmlbANCiAqIEByZXR1cm5zIHt7bmFtZTpzdHJpbmcscXVlcnk6T2JqZWN0fX0NCiAqKi8KCmV4cG9ydCB2YXIgZ2V0RHJhZnRRdWVyeSA9IGZ1bmN0aW9uIGdldERyYWZ0UXVlcnkobmFtZSwgc3RhdHVzLCBwYWdlVHlwZSwgcXVlcnksICRyb3V0ZSkgewogIHJldHVybiB7CiAgICBuYW1lOiBuYW1lLAogICAgcXVlcnk6IF9vYmplY3RTcHJlYWQoewogICAgICBzdGF0dXM6IHN0YXR1cywKICAgICAgcGdfdHlwZTogcGFnZVR5cGUsCiAgICAgIHBnX3JlZGlyZWN0OiAkcm91dGUubmFtZQogICAgfSwgcXVlcnkpCiAgfTsKfTsKCi8vIHF1ZXJ5OiB7IHN0YXR1cywgcGdfdHlwZTogcGFnZVR5cGUsIHBnX3JlZGlyZWN0OiBwYWdlVHlwZSA9PT0gJ2NvbScgPyAnUFJPMkNvbVNjaGVkdWxlTmV3JyA6IHBhZ2VUeXBlID09PSAncGFydCcgPyAnUFJPMlBhcnRTY2hlZHVsZU5ldycgOiAnUFJPMlVuaXRQYXJ0U2NoZWR1bGVOZXcnLCAuLi5xdWVyeSB9CgovLyDpm7bmnoTlu7rmnoTku7blkIjlubbllK/kuIDnoIEs6Ziy5q2i5pu05o2iCmV4cG9ydCB2YXIgdW5pcXVlQ29kZSA9IGZ1bmN0aW9uIHVuaXF1ZUNvZGUocGFnZVR5cGUpIHsKICByZXR1cm4gJ3V1aWQnOwp9OwpleHBvcnQgdmFyIEZJWF9DT0xVTU4gPSBbJ0NvbXBfQ29kZScsICdQYXJ0X0NvZGUnXTsKZXhwb3J0IHZhciBnZXRVbmlxdWUgPSBmdW5jdGlvbiBnZXRVbmlxdWUoaXNDb20sIGVsZW1lbnQpIHsKICByZXR1cm4gZWxlbWVudC5JbnN0YWxsVW5pdF9JZCArIGVsZW1lbnQuUGFydF9Db2RlICsgZWxlbWVudC5QYXJ0X0FnZ3JlZ2F0ZV9JZDsKfTs="}, {"version": 3, "names": ["getDraftQuery", "name", "status", "pageType", "query", "$route", "_objectSpread", "pg_type", "pg_redirect", "uniqueCode", "FIX_COLUMN", "getUnique", "isCom", "element", "InstallUnit_Id", "Part_Code", "Part_Aggregate_Id"], "sources": ["D:/project/platform_framework_master/platform_framework/Platform/Frontend/SubAppProduce/src/views/PRO/plan-production/schedule-production-new-unit-part/constant.js"], "sourcesContent": ["/**\r\n * @Description: 获取页面路由跳转\r\n * @author: yingzy\r\n * @date 2022/9/13\r\n * @param {!string} name 路由名称\r\n * @param {('add'|'edit'|'view')} status 页面状态新增/编辑\r\n * @param {('com'|'part')} pageType 零构件类型\r\n * @param {Object} [query] 额外query参数\r\n * @returns {{name:string,query:Object}}\r\n **/\r\n\r\nexport const getDraftQuery = (name, status, pageType, query, $route) => {\r\n  return {\r\n    name,\r\n    query: { status, pg_type: pageType, pg_redirect: $route.name, ...query }\r\n  }\r\n}\r\n\r\n// query: { status, pg_type: pageType, pg_redirect: pageType === 'com' ? 'PRO2ComScheduleNew' : pageType === 'part' ? 'PRO2PartScheduleNew' : 'PRO2UnitPartScheduleNew', ...query }\r\n\r\n// 零构建构件合并唯一码,防止更换\r\nexport const uniqueCode = (pageType) => {\r\n  return 'uuid'\r\n}\r\n\r\nexport const FIX_COLUMN = ['Comp_Code', 'Part_Code']\r\n\r\nexport const getUnique = (isCom, element) => {\r\n  return element.InstallUnit_Id + element.Part_Code + element.Part_Aggregate_Id\r\n}\r\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,IAAMA,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAK;EACtE,OAAO;IACLJ,IAAI,EAAJA,IAAI;IACJG,KAAK,EAAAE,aAAA;MAAIJ,MAAM,EAANA,MAAM;MAAEK,OAAO,EAAEJ,QAAQ;MAAEK,WAAW,EAAEH,MAAM,CAACJ;IAAI,GAAKG,KAAK;EACxE,CAAC;AACH,CAAC;;AAED;;AAEA;AACA,OAAO,IAAMK,UAAU,GAAG,SAAbA,UAAUA,CAAIN,QAAQ,EAAK;EACtC,OAAO,MAAM;AACf,CAAC;AAED,OAAO,IAAMO,UAAU,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC;AAEpD,OAAO,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAIC,KAAK,EAAEC,OAAO,EAAK;EAC3C,OAAOA,OAAO,CAACC,cAAc,GAAGD,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,iBAAiB;AAC/E,CAAC", "ignoreList": []}]}
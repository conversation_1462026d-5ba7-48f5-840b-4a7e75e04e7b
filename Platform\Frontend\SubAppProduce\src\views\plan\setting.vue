<template>
  <div class="app-container abs100">
    <bt-tree :data="treeData" :props="treeProps" :default-selected-key="treeDefaultSelectedKey" node-key="Sys_Project_Id" @node-click="nodeClick">
      <template #default="{ data }">
        <span v-if="data.Code" style="color: #5ac8fa!important;">({{ data.Code }})</span>
        <span>{{ data.Short_Name }}</span>
      </template>
    </bt-tree>
    <!-- 计划配置和预警规则 -->
    <el-card class="card">
      <div slot="header" class="card-header">
        <span>计划配置</span>
        <div class="header-buttons">
          <div v-if="showProjectButtons" class="project-buttons">
            <el-button
              :loading="resetLoading"
              size="small"
              type="primary"
              @click="resetConfig"
            >
              重置配置
            </el-button>
            <el-button
              :loading="syncLoading"
              size="small"
              type="primary"
              @click="syncToArea"
            >
              同步至项目分区
            </el-button>
          </div>
          <el-button
            type="primary"
            size="small"
            @click="saveAllConfig"
          >
            保存
          </el-button>
        </div>
      </div>
      <div class="content">
        <!-- 计划配置 - 只在默认配置时显示 -->
        <div v-if="showPlanConfig" class="plan-config-section">
          <el-form style="width: 782px" inline>
            <el-row v-for="item in nodeData" :key="item.Plan_Type" :gutter="50">
              <el-form-item :label="item.Plan_Name||' '" label-width="100px">
                <el-input v-model="item.Plan_Name" style="width:150px" clearable />
              </el-form-item>
              <el-form-item label="计划下发通知人" label-width="150px">
                <el-select
                  v-model="item.Plan_Notice_Userids"
                  placeholder="请选择"
                  style="width: 400px"
                  clearable
                  filterable
                  multiple
                >
                  <el-option
                    v-for="item in userList"
                    :key="item.Id"
                    :label="item.Display_Name"
                    :value="item.Id"
                  />
                </el-select>
              </el-form-item>
            </el-row>

          </el-form>
        </div>

        <!-- 预警规则 - 在非默认配置时显示 -->
        <div v-if="showWarnConfig" class="warn-config-section">
          <h3>预警规则</h3>
          <!-- Tab切换 -->
          <el-tabs v-model="activeTab" @tab-click="handleTabClick">
            <el-tab-pane
              v-for="item in nodeData"
              :key="item.Plan_Type"
              :label="item.Plan_Name"
              :name="item.Plan_Type.toString()"
            >
              <!-- 预警规则表单 -->
              <div class="warn-rules">
                <div class="rule-header">
                  <el-button size="small" type="primary" @click="addWarnRule">新增预警规则</el-button>
                </div>
                <div v-if="currentWarnRules.length === 0" class="empty-rules">
                  暂无预警规则
                </div>
                <el-form v-else>
                  <div class="rules-list">
                    <div v-for="(rule, index) in currentWarnRules" :key="index" class="rule-item">
                      <el-row :gutter="20">
                        <el-col :span="4">
                          <el-form-item label="工期进度达到" required>
                            <el-input
                              v-model="rule.Progress_Percent"
                              placeholder="70%"
                              style="width: 100%"
                            >
                              <template slot="append">%</template>
                            </el-input>
                          </el-form-item>
                        </el-col>
                        <el-col :span="4">
                          <el-form-item label="完成进度达到" required>
                            <el-input
                              v-model="rule.Finish_Percent"
                              placeholder="70%"
                              style="width: 100%"
                            >
                              <template slot="append">%</template>
                            </el-input>
                          </el-form-item>
                        </el-col>
                        <el-col :span="14">
                          <el-form-item label="计划预警通知人" required>
                            <el-select
                              v-model="rule.Plan_Notice_Userids"
                              placeholder="下拉选择当前公司下标准人，支持搜索"
                              style="width: 100%"
                              clearable
                              filterable
                              multiple
                            >
                              <el-option
                                v-for="user in userList"
                                :key="user.Id"
                                :label="user.Display_Name"
                                :value="user.Id"
                              />
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="2">
                          <el-button
                            type="danger"
                            icon="el-icon-delete"
                            size="small"
                            @click="deleteWarnRule(index)"
                          />
                        </el-col>
                      </el-row>
                    </div>
                  </div>

                </el-form>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-card>
  </div>
</template>
<script>
import { GetConfigs, GetCurrCompanyProjectList, SaveConfigs } from '@/api/plm/projects'
import { GetUserPage } from '@/api/sys'
import { GetWarnConfigs, SaveWarnConfigs, ResetWarnConfig, SyncAreaWarnConfig } from '@/api/PRO/control-plan'

export default {
  data() {
    return {
      userList: [],
      nodeData: [],
      companyId: localStorage.getItem('CurReferenceId'),
      treeData: [],
      treeProps: {
        label: 'Short_Name',
        id: 'Sys_Project_Id'
      },
      treeDefaultSelectedKey: '',
      params: {
        CompanyId: localStorage.getItem('CurReferenceId'),
        ProjectId: '',
        AreaId: ''
      },
      resetLoading: false,
      syncLoading: false,
      activeTab: '',
      warnRules: {} // 存储各个计划类型的预警规则
    }
  },
  computed: {
    showProjectButtons() {
      // 只有在选中项目级节点（非默认配置且非区域）时才显示按钮
      return this.params.ProjectId !== '' &&
              this.params.ProjectId !== 'default_config' &&
              this.params.AreaId === ''
    },
    showPlanConfig() {
      // 只有在默认配置时才显示计划配置
      return this.params.ProjectId === '' && this.params.AreaId === ''
    },
    showWarnConfig() {
      // 预警规则无论何种情况都显示
      return true
    },
    currentWarnRules() {
      // 获取当前选中tab的预警规则
      if (!this.activeTab || !this.warnRules[this.activeTab]) {
        return []
      }
      return this.warnRules[this.activeTab]
    }
  },
  async created() {
    await this.getTreeData()
    await this.getList()
  },
  methods: {
    nodeClick(node) {
      this.curProject = node

      // 判断是否为默认配置节点
      if (node.isDefault) {
        this.params.ProjectId = ''
        this.params.AreaId = ''
        // 默认配置时也需要请求预警规则数据
        if (this.nodeData.length > 0) {
          this.activeTab = this.nodeData[0].Plan_Type.toString()
          this.$nextTick(() => {
            this.getWarnRules()
          })
        }
        return
      }

      // 判断节点层级
      // 如果节点没有父级ID或父级ID为空，说明是第一级（项目）
      if (!node.Parent_Id || node.Parent_Id === '' || node.Parent_Id === null) {
        // 第一级节点：设置ProjectId为当前节点ID，AreaId为空
        this.params.ProjectId = node.Sys_Project_Id
        this.params.AreaId = ''
      } else {
        // 其他级别节点（区域）：设置AreaId为当前节点ID，ProjectId为所属第一级节点ID
        this.params.AreaId = node.Sys_Project_Id
        // 查找所属的第一级节点ID
        this.params.ProjectId = this.findTopLevelParent(node)
      }

      // 初始化activeTab并获取预警规则数据
      if (this.nodeData.length > 0) {
        this.activeTab = this.nodeData[0].Plan_Type.toString()
        this.$nextTick(() => {
          // 当节点变化时，重新请求预警规则数据
          this.getWarnRules()
        })
      }

      console.log(this.params)
    },
    async getTreeData() {
      try {
        const res = await GetCurrCompanyProjectList({
          companId: localStorage.getItem('CurReferenceId'),
          IsCascade: false
        })

        // 在最前面添加【默认配置】节点
        const defaultNode = {
          Sys_Project_Id: '',
          Short_Name: '【默认配置】',
          Code: '',
          loading: false,
          isDefault: true // 标记为默认配置节点
        }

        this.treeData = [defaultNode, ...res.Data.map(item => {
          item.loading = false
          return item
        })]

        // 默认选中【默认配置】节点
        if (this.treeData.length) {
          this.treeDefaultSelectedKey = ''
          this.curProject = defaultNode
          // 设置默认配置的参数
          this.params.ProjectId = ''
          this.params.AreaId = ''

          // 先获取配置列表数据，然后再触发nodeClick
          await this.getConfigList()
          this.$nextTick(() => {
            this.nodeClick(defaultNode)
          })
        }
      } catch (error) {
        console.error('获取树形数据失败:', error)
      }
    },
    async getList() {
      const res = await GetUserPage({
        DepartmentId: this.departmentId,
        PageSize: 10000
      })
      this.userList = res.Data.Data.filter(item => item.UserStatusName === '正常')
    },
    async getConfigList() {
      try {
        const res = await GetConfigs({
          CompanyId: this.companyId
        })
        this.$set(this, 'nodeData', res.Data)
        return res.Data
      } catch (error) {
        console.error('获取配置列表失败:', error)
        return []
      }
    },
    async saveConfig() {
      try {
        const res = await SaveConfigs(this.nodeData)
        if (res.IsSucceed) {
          this.$message.success('计划配置保存成功')
          this.getConfigList()
        } else {
          this.$message.error(res.Message)
        }
      } catch (error) {
        this.$message.error('计划配置保存失败')
        console.error('保存计划配置错误:', error)
      }
    },
    // 查找顶级父节点ID
    findTopLevelParent(node) {
      // 递归查找直到找到没有父级的节点
      const findParent = (currentNode) => {
        if (!currentNode.Parent_Id || currentNode.Parent_Id === '' || currentNode.Parent_Id === null) {
          return currentNode.Sys_Project_Id
        }
        // 在树数据中查找父节点
        const parentNode = this.findNodeById(currentNode.Parent_Id)
        if (parentNode) {
          return findParent(parentNode)
        }
        return currentNode.Sys_Project_Id
      }
      return findParent(node)
    },
    // 根据ID查找节点
    findNodeById(id) {
      const findInTree = (nodes) => {
        for (const node of nodes) {
          if (node.Sys_Project_Id === id) {
            return node
          }
          if (node.children && node.children.length > 0) {
            const found = findInTree(node.children)
            if (found) return found
          }
        }
        return null
      }
      return findInTree(this.treeData)
    },
    // 重置配置
    async resetConfig() {
      this.resetLoading = true
      try {
        const res = await ResetWarnConfig({
          CompanyId: this.companyId,
          ProjectId: this.params.ProjectId
        })
        if (res.IsSucceed) {
          this.$message.success('重置配置成功')
          this.getConfigList() // 重新获取配置列表
        } else {
          this.$message.error(res.Message || '重置配置失败')
        }
      } catch (error) {
        this.$message.error('重置配置失败')
        console.error('重置配置错误:', error)
      } finally {
        this.resetLoading = false
      }
    },
    // 同步至项目分区
    async syncToArea() {
      this.syncLoading = true
      try {
        const res = await SyncAreaWarnConfig({
          CompanyId: this.companyId,
          ProjectId: this.params.ProjectId
        })
        if (res.IsSucceed) {
          this.$message.success('同步至项目分区成功')
        } else {
          this.$message.error(res.Message || '同步至项目分区失败')
        }
      } catch (error) {
        this.$message.error('同步至项目分区失败')
        console.error('同步至项目分区错误:', error)
      } finally {
        this.syncLoading = false
      }
    },
    // Tab切换处理
    handleTabClick(tab) {
      this.activeTab = tab.name
      // 不重新请求数据，避免数据丢失
    },
    // 新增预警规则
    addWarnRule() {
      if (!this.warnRules[this.activeTab]) {
        this.$set(this.warnRules, this.activeTab, [])
      }
      const newRule = {
        Plan_Type: parseInt(this.activeTab),
        Project_Id: this.params.ProjectId,
        Area_Id: this.params.AreaId,
        Progress_Percent: '',
        Finish_Percent: '',
        Plan_Notice_Userids: []
      }
      this.warnRules[this.activeTab].push(newRule)
    },
    // 删除预警规则
    deleteWarnRule(index) {
      this.$confirm('确定要删除这条预警规则吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.warnRules[this.activeTab].splice(index, 1)
        this.$message.success('删除成功')
      }).catch(() => {})
    },
    // 获取预警规则
    async getWarnRules() {
      try {
        const res = await GetWarnConfigs({
          CompanyId: this.companyId,
          ProjectId: this.params.ProjectId,
          AreaId: this.params.AreaId,
          Plan_Type: parseInt(this.activeTab)
        })
        if (res.IsSucceed) {
          this.$set(this.warnRules, this.activeTab, res.Data || [])
        }
      } catch (error) {
        console.error('获取预警规则失败:', error)
      }
    },
    // 保存所有配置
    async saveAllConfig() {
      try {
        // 如果显示计划配置，保存计划配置
        if (this.showPlanConfig && this.nodeData && this.nodeData.length) {
          await this.saveConfig()
        }

        // 如果显示预警配置，保存预警配置
        if (this.showWarnConfig) {
          await this.saveWarnConfig()
        }

        // 如果两个都不显示或都没有数据，提示用户
        if (!this.showPlanConfig && !this.showWarnConfig) {
          this.$message.warning('暂无可保存的配置')
        }
      } catch (error) {
        console.error('保存配置失败:', error)
      }
    },
    // 保存预警配置
    async saveWarnConfig() {
      // 校验必填项
      const rules = this.currentWarnRules
      if (rules.length > 0) {
        for (let i = 0; i < rules.length; i++) {
          const rule = rules[i]
          if (!rule.Progress_Percent || !rule.Finish_Percent || !rule.Plan_Notice_Userids || rule.Plan_Notice_Userids.length === 0) {
            this.$message.error(`第${i + 1}条预警规则存在必填项未填写`)
            return
          }
          // 验证百分比格式
          if (isNaN(rule.Progress_Percent) || isNaN(rule.Finish_Percent)) {
            this.$message.error(`第${i + 1}条预警规则的百分比必须为数字`)
            return
          }
        }
      }

      try {
        const res = await SaveWarnConfigs({
          CompanyId: this.companyId,
          ProjectId: this.params.ProjectId,
          AreaId: this.params.AreaId,
          Items: rules
        })
        if (res.IsSucceed) {
          this.$message.success('预警规则保存成功')
          this.getWarnRules() // 重新获取数据
        } else {
          this.$message.error(res.Message || '预警规则保存失败')
        }
      } catch (error) {
        this.$message.error('保存失败')
        console.error('保存预警配置错误:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container{
  font-family: PingFang SC, PingFang SC;
  display: flex;
  .card{
    flex:1;
    height: 100%;
    margin-left: 16px;
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .header-buttons {
        display: flex;
        align-items: center;
        gap: 8px;

        .project-buttons {
          display: flex;
          gap: 8px;
        }

        .el-button {
          margin: 0;
        }
      }
    }
    .content{
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    .bt-table{
      flex:1;
    }

    // 计划配置和预警规则section样式
    .plan-config-section {
      margin-bottom: 30px;
    }

    .warn-config-section {
      h3 {
        margin: 0 0 20px 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        padding-bottom: 8px;
      }
    }

    // 预警规则样式
    .warn-rules {
      .rule-header {
        margin-bottom: 20px;
      }

      .empty-rules {
        text-align: center;
        color: #999;
        padding: 40px 0;
        font-size: 14px;
      }

      .rules-list {
        .rule-item {
          margin-bottom: 20px;
          padding: 20px;
          border: 1px solid #e4e7ed;
          border-radius: 4px;
          background-color: #fafafa;

          .el-form-item {
            margin-bottom: 0;
          }

          .el-col:last-child {
            display: flex;
            align-items: center;
            justify-content: center;
            padding-top: 30px;
          }
        }
      }
    }
  }
  .header{
    display: flex;
    align-items: flex-end;
    .project-name{
      font-size: 16px;
      color: #333333;
      font-weight: bold;
      margin-right: 8px;
    }
    .el-icon-time{
      margin-left: 8px;
      margin-right: 4px;
      font-size: 14px;
    }
    .label{
      color: #333333;
      font-size: 12px;
    }
    .value{
      font-size: 14px;
      color: #333333;
      font-weight: bold;
      margin-left: 7px;
    }
  }
}
</style>

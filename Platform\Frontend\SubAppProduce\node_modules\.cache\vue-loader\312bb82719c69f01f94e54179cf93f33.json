{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\plan\\overallControlPlan.vue?vue&type=style&index=0&id=25584f95&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\plan\\overallControlPlan.vue", "mtime": 1757926768489}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hcHAtY29udGFpbmVyIHsNCiAgZm9udC1mYW1pbHk6IFBpbmdGYW5nIFNDLCBQaW5nRmFuZyBTQzsNCiAgZGlzcGxheTogZmxleDsNCn0NCg=="}, {"version": 3, "sources": ["overallControlPlan.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDA;AACA;AACA;AACA", "file": "overallControlPlan.vue", "sourceRoot": "src/views/plan", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <bt-tree :data=\"treeData\" :props=\"treeProps\" :default-selected-key=\"treeDefaultSelectedKey\" node-key=\"Sys_Project_Id\" @node-click=\"nodeClick\">\r\n      <template #default=\"{ data }\">\r\n        <span style=\"color: #5ac8fa!important;\">({{ data.Code }})</span>\r\n        <span>{{ data.Short_Name }}</span>\r\n      </template>\r\n    </bt-tree>\r\n    <OverallControlPlanContent :cur-project=\"curProject\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetCurrCompanyProjectList } from '@/api/plm/projects'\r\nimport OverallControlPlanContent from './components/OverallControlPlanContent.vue'\r\n\r\nexport default {\r\n  name: 'OverallControlPlan',\r\n  components: { OverallControlPlanContent },\r\n  data() {\r\n    return {\r\n      treeData: [],\r\n      treeProps: {\r\n        label: 'Short_Name',\r\n        id: 'Sys_Project_Id'\r\n      },\r\n      treeDefaultSelectedKey: '',\r\n      curProject: ''\r\n    }\r\n  },\r\n  created() {\r\n    this.getTreeData()\r\n  },\r\n  methods: {\r\n    getTreeData() {\r\n      GetCurrCompanyProjectList({\r\n        companId: localStorage.getItem('CurReferenceId'),\r\n        IsCascade: false\r\n      }).then(res => {\r\n        this.treeData = res.Data.map(item => {\r\n          item.loading = false\r\n          return item\r\n        })\r\n        if (this.treeData.length) {\r\n          this.treeDefaultSelectedKey = this.treeData[0].Sys_Project_Id\r\n          this.curProject = this.treeData[0]\r\n        }\r\n      })\r\n    },\r\n    nodeClick(node) {\r\n      this.curProject = node\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  font-family: PingFang SC, PingFang SC;\r\n  display: flex;\r\n}\r\n</style>\r\n"]}]}
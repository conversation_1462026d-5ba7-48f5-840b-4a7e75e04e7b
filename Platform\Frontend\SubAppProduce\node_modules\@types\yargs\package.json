{"name": "@types/yargs", "version": "13.0.12", "description": "TypeScript definitions for yargs", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/poelstra", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/mizunashi-mana", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/pushplay", "githubUsername": "pushplay"}, {"name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "url": "https://github.com/JimiC", "githubUsername": "JimiC"}, {"name": "Steffen Viken Valvåg", "url": "https://github.com/steffenvv", "githubUsername": "steffenvv"}, {"name": "<PERSON>", "url": "https://github.com/forivall", "githubUsername": "forivall"}, {"name": "ExE Boss", "url": "https://github.com/ExE-Boss", "githubUsername": "ExE-Boss"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/yargs"}, "scripts": {}, "dependencies": {"@types/yargs-parser": "*"}, "typesPublisherContentHash": "0c832761c9bdd2e9f6188b74466b3908a9fedb41ebf63717f183b4d74a4c0367", "typeScriptVersion": "3.6"}
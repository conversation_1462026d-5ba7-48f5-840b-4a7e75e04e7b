{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\mainPage.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\mainPage.vue", "mtime": 1757909680924}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQoNCmltcG9ydCBnZXRRdWVyeUluZm8gZnJvbSAnQC92aWV3cy9QUk8vcGxhbi1wcm9kdWN0aW9uL3NjaGVkdWxlLXByb2R1Y3Rpb24vbWl4aW4nDQppbXBvcnQgZ2V0VGJJbmZvIGZyb20gJ0AvbWl4aW5zL1BSTy9nZXQtdGFibGUtaW5mbycNCmltcG9ydCBEeW5hbWljRGF0YVRhYmxlIGZyb20gJ0AvY29tcG9uZW50cy9EeW5hbWljRGF0YVRhYmxlL0R5bmFtaWNEYXRhVGFibGUnDQppbXBvcnQgeyBHZXRUZWFtVGFza1BhZ2VMaXN0LCBFeHBvcnRUYXNrQ29kZURldGFpbHMgfSBmcm9tICdAL2FwaS9QUk8vcHJvZHVjdGlvbi10YXNrJw0KaW1wb3J0IGFkZFJvdXRlclBhZ2UgZnJvbSAnQC9taXhpbnMvYWRkLXJvdXRlci1wYWdlJw0KaW1wb3J0IHsgdGltZUZvcm1hdCB9IGZyb20gJ0AvZmlsdGVycycNCmltcG9ydCB7IHBhcnNlVGltZSwgY29tYmluZVVSTCwgZGVib3VuY2UgfSBmcm9tICdAL3V0aWxzJw0KaW1wb3J0IHsgbWFwR2V0dGVycyB9IGZyb20gJ3Z1ZXgnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgY29tcG9uZW50czogew0KICAgIER5bmFtaWNEYXRhVGFibGUNCiAgfSwNCiAgbWl4aW5zOiBbZ2V0UXVlcnlJbmZvLCBnZXRUYkluZm8sIGFkZFJvdXRlclBhZ2VdLA0KICBwcm9wczogew0KICAgIHBhZ2VUeXBlOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnLTEnDQogICAgfSwNCiAgICBoYXNVbml0UGFydDogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIGFkZFBhZ2VBcnJheTogWw0KICAgICAgICB7DQogICAgICAgICAgcGF0aDogdGhpcy4kcm91dGUucGF0aCArICcvZGV0YWlsJywNCiAgICAgICAgICBoaWRkZW46IHRydWUsDQogICAgICAgICAgY29tcG9uZW50OiAoKSA9PiBpbXBvcnQoJ0Avdmlld3MvUFJPL3BsYW4tcHJvZHVjdGlvbi90YXNrLWxpc3QvZGV0YWlsJyksDQogICAgICAgICAgbmFtZTogJ1BST1Rhc2tMaXN0RGV0YWlsJywNCiAgICAgICAgICBtZXRhOiB7IHRpdGxlOiAn5Lu75Yqh5Y2V6K+m5oOFJyB9DQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBwYXRoOiB0aGlzLiRyb3V0ZS5wYXRoICsgJy9kZXRhaWxQcmludCcsDQogICAgICAgICAgaGlkZGVuOiB0cnVlLA0KICAgICAgICAgIGNvbXBvbmVudDogKCkgPT4gaW1wb3J0KCdAL3ZpZXdzL1BSTy9wbGFuLXByb2R1Y3Rpb24vdGFzay1saXN0L2RldGFpbFByaW50JyksDQogICAgICAgICAgbmFtZTogJ1BST1Rhc2tMaXN0RGV0YWlsUHJpbnQnLA0KICAgICAgICAgIG1ldGE6IHsgdGl0bGU6ICfmiZPljbDku7vliqHljZXor6bmg4UnIH0NCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIHF1ZXJ5Rm9ybTogew0KICAgICAgICBUYXNrX1N0YXR1czogdW5kZWZpbmVkLA0KICAgICAgICBUYXNrX0NvZGU6IHVuZGVmaW5lZCwNCiAgICAgICAgU2NoZHVsaW5nX0NvZGU6IHVuZGVmaW5lZA0KICAgICAgfSwNCiAgICAgIHF1ZXJ5SW5mbzogew0KICAgICAgICBQYWdlOiAxLA0KICAgICAgICBQYWdlU2l6ZTogMjANCiAgICAgIH0sDQogICAgICB0YkNvbmZpZzogew0KICAgICAgICBPcF9XaWR0aDogMjAwLA0KICAgICAgICBJU19Ub29sVGlwOiB0cnVlDQogICAgICB9LA0KICAgICAgTG9hZGluZzogZmFsc2UsDQogICAgICB0YkxvYWRpbmc6IGZhbHNlLA0KICAgICAgY29sdW1uczogW10sDQogICAgICB0YkRhdGE6IFtdLA0KICAgICAgc2VsZWN0QXJyYXk6IFtdLA0KICAgICAgdG90YWw6IDAsDQogICAgICBzZWFyY2g6ICgpID0+ICh7fSkNCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgaXNQYXJ0KCkgew0KICAgICAgcmV0dXJuIHRoaXMucGFnZVR5cGUgPT09ICcwJw0KICAgIH0sDQogICAgaXNDb20oKSB7DQogICAgICByZXR1cm4gdGhpcy5wYWdlVHlwZSA9PT0gJy0xJw0KICAgIH0sDQogICAgaXNVbml0UGFydCgpIHsNCiAgICAgIHJldHVybiArdGhpcy5wYWdlVHlwZSA+IDANCiAgICB9DQogIH0sDQoNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLnRiQ29uZmlnLk9wX1dpZHRoID0gdGhpcy5pc0NvbSA/IDIwMCA6IDE0MA0KICB9LA0KDQogIGFzeW5jIG1vdW50ZWQoKSB7DQogICAgdGhpcy5zZWFyY2ggPSBkZWJvdW5jZSh0aGlzLmZldGNoRGF0YSwgODAwLCB0cnVlKQ0KICAgIGF3YWl0IHRoaXMuZ2V0VGFibGVDb25maWcoKHRoaXMuaXNDb20gfHwgdGhpcy5pc1VuaXRQYXJ0KSA/ICdQUk9Db21UYXNrTGlzdCcgOiAnUFJPUGFydFRhc2tMaXN0JykNCg0KICAgIGlmICghdGhpcy5oYXNVbml0UGFydCkgew0KICAgICAgY29uc3QgYXJyID0gWydQcm9qZWN0X05hbWUnLCAnQXJlYV9OYW1lJywgJ0luc3RhbGxVbml0X05hbWUnXQ0KICAgICAgYXJyLmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgY29uc3QgaWR4ID0gdGhpcy5jb2x1bW5zLmZpbmRJbmRleCgodikgPT4gdi5Db2RlID09PSBpdGVtKQ0KICAgICAgICBpZiAoaWR4ICE9PSAtMSkgew0KICAgICAgICAgIHRoaXMuY29sdW1ucy5zcGxpY2UoaWR4LCAxKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0NCg0KICAgIGlmICh0aGlzLmlzUGFydCkgew0KICAgICAgY29uc3QgaWR4ID0gdGhpcy5jb2x1bW5zLmZpbmRJbmRleCgoaXRlbSkgPT4gaXRlbS5Db2RlID09PSAnUHJvY2Vzc19GaW5pc2hfRGF0ZScpDQogICAgICBpZHggIT09IC0xICYmIHRoaXMuY29sdW1ucy5zcGxpY2UoaWR4LCAxKQ0KICAgIH0NCiAgICB0aGlzLmZldGNoRGF0YSgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBnZXRTZWxlY3RWYWwodikgew0KICAgICAgdGhpcy5zZWxlY3RBcnJheSA9IHYNCiAgICB9LA0KICAgIC8vIOWvvOWHuuS7u+WKoeWNleWIl+ihqA0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIGNvbnN0IGZpbHRlclZhbCA9IHRoaXMuY29sdW1ucy5tYXAodiA9PiB2LkNvZGUpDQogICAgICBjb25zdCBkYXRhID0gZm9ybWF0SnNvbihmaWx0ZXJWYWwsIHRoaXMuc2VsZWN0QXJyYXkpDQogICAgICBjb25zdCBoZWFkZXIgPSB0aGlzLmNvbHVtbnMubWFwKHYgPT4gdi5EaXNwbGF5X05hbWUpDQogICAgICBpbXBvcnQoJ0AvdmVuZG9yL0V4cG9ydDJFeGNlbCcpLnRoZW4oZXhjZWwgPT4gew0KICAgICAgICBleGNlbC5leHBvcnRfanNvbl90b19leGNlbCh7DQogICAgICAgICAgaGVhZGVyOiBoZWFkZXIsDQogICAgICAgICAgZGF0YSwNCiAgICAgICAgICBmaWxlbmFtZTogYCR7dGhpcy5pc0NvbSA/ICfmnoTku7bku7vliqHljZUnIDogdGhpcy5pc1VuaXRQYXJ0ID8gJ+mDqOS7tuS7u+WKoeWNlScgOiAn6Zu25Lu25Lu75Yqh5Y2VJ31gLA0KICAgICAgICAgIGF1dG9XaWR0aDogdHJ1ZSwNCiAgICAgICAgICBib29rVHlwZTogJ3hsc3gnDQogICAgICAgIH0pDQogICAgICB9KQ0KICAgICAgZnVuY3Rpb24gZm9ybWF0SnNvbihmaWx0ZXJWYWwsIGpzb25EYXRhKSB7DQogICAgICAgIHJldHVybiBqc29uRGF0YS5tYXAodiA9PiBmaWx0ZXJWYWwubWFwKGogPT4gew0KICAgICAgICAgIGlmIChqID09PSAnT3JkZXJfRGF0ZScpIHsNCiAgICAgICAgICAgIHJldHVybiB0aW1lRm9ybWF0KHZbal0pDQogICAgICAgICAgfSBlbHNlIGlmIChqID09PSAnVGFza19TdGF0dXMnKSB7DQogICAgICAgICAgICByZXR1cm4gdltqXSA9PT0gMCA/ICfmnKrlrozmiJAnIDogJ+W3suWujOaIkCcNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgcmV0dXJuIHZbal0NCiAgICAgICAgICB9DQogICAgICAgIH0pKQ0KICAgICAgfQ0KICAgIH0sDQogICAgZmV0Y2hEYXRhKHBhZ2UpIHsNCiAgICAgIHBhZ2UgJiYgKHRoaXMucXVlcnlJbmZvLlBhZ2UgPSBwYWdlKQ0KICAgICAgdGhpcy50YkxvYWRpbmcgPSB0cnVlDQogICAgICBHZXRUZWFtVGFza1BhZ2VMaXN0KHsNCiAgICAgICAgLi4udGhpcy5xdWVyeUluZm8sDQogICAgICAgIFRhc2tfQ29kZTogdGhpcy5xdWVyeUZvcm0uVGFza19Db2RlLA0KICAgICAgICBTY2hkdWxpbmdfQ29kZTogdGhpcy5xdWVyeUZvcm0uU2NoZHVsaW5nX0NvZGUsDQogICAgICAgIFByb2plY3RfSWQ6IHRoaXMucXVlcnlGb3JtLnByb2plY3RJZCwNCiAgICAgICAgVGFza19TdGF0dXM6IHRoaXMucXVlcnlGb3JtLlRhc2tfU3RhdHVzLA0KICAgICAgICBBcmVhX0lkOiB0aGlzLnF1ZXJ5Rm9ybS5hcmVhSWQsDQogICAgICAgIEluc3RhbGxVbml0X0lkOiB0aGlzLnF1ZXJ5Rm9ybS5pbnN0YWxsLA0KICAgICAgICBCb21fTGV2ZWw6IHRoaXMucGFnZVR5cGUsDQogICAgICAgIFByb2Nlc3NfVHlwZTogdGhpcy5pc0NvbSA/IDIgOiB0aGlzLmlzUGFydCA/IDEgOiAzIC8vIOW3peW6j+exu+WeiyAx6Zu25Lu2IDLmnoTku7YNCiAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLnRiRGF0YSA9IHJlcy5EYXRhLkRhdGEubWFwKCh2KSA9PiB7DQogICAgICAgICAgICB2LkZpbmlzaF9EYXRlID0gdi5GaW5pc2hfRGF0ZQ0KICAgICAgICAgICAgICA/IHBhcnNlVGltZShuZXcgRGF0ZSh2LkZpbmlzaF9EYXRlKSwgJ3t5fS17bX0te2R9JykNCiAgICAgICAgICAgICAgOiB2LkZpbmlzaF9EYXRlDQogICAgICAgICAgICB2Lk9yZGVyX0RhdGUgPSB2Lk9yZGVyX0RhdGUNCiAgICAgICAgICAgICAgPyBwYXJzZVRpbWUobmV3IERhdGUodi5PcmRlcl9EYXRlKSwgJ3t5fS17bX0te2R9JykNCiAgICAgICAgICAgICAgOiB2Lk9yZGVyX0RhdGUNCiAgICAgICAgICAgIHYuVGFza19GaW5pc2hfRGF0ZSA9IHYuVGFza19GaW5pc2hfRGF0ZQ0KICAgICAgICAgICAgICA/IHBhcnNlVGltZShuZXcgRGF0ZSh2LlRhc2tfRmluaXNoX0RhdGUpLCAne3l9LXttfS17ZH0nKQ0KICAgICAgICAgICAgICA6IHYuVGFza19GaW5pc2hfRGF0ZQ0KICAgICAgICAgICAgdi5Qcm9jZXNzX0ZpbmlzaF9EYXRlID0gdi5Qcm9jZXNzX0ZpbmlzaF9EYXRlDQogICAgICAgICAgICAgID8gcGFyc2VUaW1lKG5ldyBEYXRlKHYuUHJvY2Vzc19GaW5pc2hfRGF0ZSksICd7eX0te219LXtkfScpDQogICAgICAgICAgICAgIDogdi5Qcm9jZXNzX0ZpbmlzaF9EYXRlDQogICAgICAgICAgICByZXR1cm4gdg0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy50b3RhbCA9IHJlcy5EYXRhLlRvdGFsQ291bnQNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pLmZpbmFsbHkoXyA9PiB7DQogICAgICAgIHRoaXMudGJMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVSZXNldCgpIHsNCiAgICAgIHRoaXMuJHJlZnNbJ2Zvcm0nXS5yZXNldEZpZWxkcygpDQogICAgICB0aGlzLnNlYXJjaCgxKQ0KICAgIH0sDQogICAgLy8g5a+85Ye65Lu75Yqh5Y2V6K+m5oOFDQogICAgaGFuZGxlQ29tbWFuZChjb21tYW5kLCB0eXBlKSB7DQogICAgICBjb25zdCBUZWFtVGFza01vZGVsID0gdGhpcy5zZWxlY3RBcnJheS5tYXAodiA9PiB7DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgVGFza19Db2RlOiB2LlRhc2tfQ29kZSwNCiAgICAgICAgICBXb3JraW5nX1RlYW1fSWQ6IHYuV29ya2luZ19UZWFtX0lkDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICBjb25zdCBXb3JraW5nX1RlYW1fSWQgPSB0aGlzLnNlbGVjdEFycmF5Lmxlbmd0aCA9PT0gMSA/IHRoaXMuc2VsZWN0QXJyYXlbMF0uV29ya2luZ19UZWFtX0lkIDogJycNCiAgICAgIGNvbnN0IFRhc2tfQ29kZSA9IHRoaXMuc2VsZWN0QXJyYXkubGVuZ3RoID09PSAxID8gdGhpcy5zZWxlY3RBcnJheVswXS5UYXNrX0NvZGUgOiAnJw0KICAgICAgdGhpcy5Mb2FkaW5nID0gdHJ1ZQ0KICAgICAgRXhwb3J0VGFza0NvZGVEZXRhaWxzKHsNCiAgICAgICAgUHJvY2Vzc19UeXBlOiB0aGlzLmlzQ29tID8gMiA6IHRoaXMuaXNQYXJ0ID8gMSA6IDMsIC8vIDHpm7bku7bvvIwy5p6E5Lu2DQogICAgICAgIFdvcmtpbmdfVGVhbV9JZCwNCiAgICAgICAgVGFza19Db2RlLA0KICAgICAgICBJc19NZXJnZTogY29tbWFuZCA9PT0gJ2NvZGUnLA0KICAgICAgICBFeHBvcnRUZWFtVGFza01vZGVsOiBUZWFtVGFza01vZGVsDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6ICflr7zlh7rmiJDlip8nLA0KICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgICAgfSkNCiAgICAgICAgICB3aW5kb3cub3Blbihjb21iaW5lVVJMKHRoaXMuJGJhc2VVcmwsIHJlcy5EYXRhKSwgJ19ibGFuaycpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KS5maW5hbGx5KF8gPT4gew0KICAgICAgICB0aGlzLkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOaJk+WNsA0KICAgIHByaW50U2VsZWN0ZWQoY29tbWFuZCwgcm93KSB7DQogICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgIFRhc2tfQ29kZTogcm93LlRhc2tfQ29kZSwNCiAgICAgICAgUHJvamVjdF9OYW1lOiByb3cuUHJvamVjdF9OYW1lLA0KICAgICAgICBBcmVhX05hbWU6IHJvdy5BcmVhX05hbWUsDQogICAgICAgIEluc3RhbGxVbml0X05hbWU6IHJvdy5JbnN0YWxsVW5pdF9OYW1lLA0KICAgICAgICBTY2hkdWxpbmdfQ29kZTogcm93LlNjaGR1bGluZ19Db2RlLA0KICAgICAgICBGaW5pc2hfRGF0ZTogcm93LkZpbmlzaF9EYXRlLA0KICAgICAgICBPcmRlcl9EYXRlOiByb3cuT3JkZXJfRGF0ZSwNCiAgICAgICAgVGFza19GaW5pc2hfRGF0ZTogcm93LlRhc2tfRmluaXNoX0RhdGUsDQogICAgICAgIFByb2Nlc3NfRmluaXNoX0RhdGU6IHJvdy5Qcm9jZXNzX0ZpbmlzaF9EYXRlLA0KICAgICAgICBXb3JraW5nX1RlYW1fTmFtZTogcm93LldvcmtpbmdfVGVhbV9OYW1lLA0KICAgICAgICBXb3JraW5nX1Byb2Nlc3NfTmFtZTogcm93LldvcmtpbmdfUHJvY2Vzc19OYW1lLA0KICAgICAgICBXb3JraW5nX1RlYW1fSWQ6IHJvdy5Xb3JraW5nX1RlYW1fSWQNCiAgICAgIH0NCg0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICBuYW1lOiAnUFJPVGFza0xpc3REZXRhaWxQcmludCcsDQogICAgICAgIHF1ZXJ5OiB7DQogICAgICAgICAgdHlwZTogdGhpcy5wYWdlVHlwZSwNCiAgICAgICAgICBjb21tYW5kOiBjb21tYW5kLA0KICAgICAgICAgIHBnX3JlZGlyZWN0OiB0aGlzLiRyb3V0ZS5uYW1lLA0KICAgICAgICAgIG90aGVyOiBlbmNvZGVVUklDb21wb25lbnQoSlNPTi5zdHJpbmdpZnkocGFyYW1zKSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgaGFuZGxlVmlldyhyb3cpIHsNCiAgICAgIGNvbnN0IHsNCiAgICAgICAgVGFza19Db2RlLA0KICAgICAgICBQcm9qZWN0X05hbWUsDQogICAgICAgIEFyZWFfTmFtZSwNCiAgICAgICAgSW5zdGFsbFVuaXRfTmFtZSwNCiAgICAgICAgU2NoZHVsaW5nX0NvZGUsDQogICAgICAgIEZpbmlzaF9EYXRlLA0KICAgICAgICBPcmRlcl9EYXRlLA0KICAgICAgICBUYXNrX0ZpbmlzaF9EYXRlLA0KICAgICAgICBQcm9jZXNzX0ZpbmlzaF9EYXRlLA0KICAgICAgICBXb3JraW5nX1RlYW1fTmFtZSwNCiAgICAgICAgV29ya2luZ19Qcm9jZXNzX05hbWUNCiAgICAgIH0gPSByb3cNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsNCiAgICAgICAgbmFtZTogJ1BST1Rhc2tMaXN0RGV0YWlsJywNCiAgICAgICAgcXVlcnk6IHsNCiAgICAgICAgICBpZDogcm93LlRhc2tfQ29kZSwNCiAgICAgICAgICB0eXBlOiB0aGlzLnBhZ2VUeXBlLA0KICAgICAgICAgIHRpZDogcm93LldvcmtpbmdfVGVhbV9JZCwNCiAgICAgICAgICBwZ19yZWRpcmVjdDogdGhpcy4kcm91dGUubmFtZSwNCiAgICAgICAgICAvLyBwZ19yZWRpcmVjdDogdGhpcy5pc1BhcnQgPyAnUFJPUGFydFRhc2tMaXN0JyA6ICdQUk9Db21UYXNrTGlzdCcsDQogICAgICAgICAgb3RoZXI6IGVuY29kZVVSSUNvbXBvbmVudChKU09OLnN0cmluZ2lmeSh7DQogICAgICAgICAgICBUYXNrX0NvZGUsDQogICAgICAgICAgICBQcm9qZWN0X05hbWUsDQogICAgICAgICAgICBBcmVhX05hbWUsDQogICAgICAgICAgICBJbnN0YWxsVW5pdF9OYW1lLA0KICAgICAgICAgICAgU2NoZHVsaW5nX0NvZGU6IFNjaGR1bGluZ19Db2RlLA0KICAgICAgICAgICAgRmluaXNoX0RhdGU6IEZpbmlzaF9EYXRlLA0KICAgICAgICAgICAgUHJvY2Vzc19GaW5pc2hfRGF0ZTogUHJvY2Vzc19GaW5pc2hfRGF0ZSwNCiAgICAgICAgICAgIE9yZGVyX0RhdGU6IE9yZGVyX0RhdGUsDQogICAgICAgICAgICBGaW5pc2hfRGF0ZTI6IFRhc2tfRmluaXNoX0RhdGUsDQogICAgICAgICAgICBXb3JraW5nX1RlYW1fTmFtZTogV29ya2luZ19UZWFtX05hbWUsDQogICAgICAgICAgICBXb3JraW5nX1Byb2Nlc3NfTmFtZTogV29ya2luZ19Qcm9jZXNzX05hbWUNCiAgICAgICAgICB9KSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["mainPage.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6KA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "mainPage.vue", "sourceRoot": "src/views/PRO/plan-production/task-list", "sourcesContent": ["<template>\r\n  <div v-loading=\"Loading\" class=\"h100\" element-loading-text=\"数据生成中\">\r\n    <div class=\"cs-z-page-main-content\">\r\n      <el-form ref=\"form\" :model=\"queryForm\" inline label-width=\"80px\">\r\n        <el-form-item label=\"任务单号\" prop=\"Task_Code\">\r\n          <el-input v-model=\"queryForm.Task_Code\" placeholder=\"请输入\" clearable=\"\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"排产单号\" prop=\"Schduling_Code\">\r\n          <el-input v-model=\"queryForm.Schduling_Code\" placeholder=\"请输入\" clearable=\"\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"项目名称\" prop=\"projectId\">\r\n          <el-select\r\n            v-model=\"queryForm.projectId\"\r\n            filterable\r\n            clearable\r\n            placeholder=\"请选择\"\r\n            style=\"width: 100%\"\r\n            @change=\"projectChange\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in projectOption\"\r\n              :key=\"item.Id\"\r\n              :label=\"item.Short_Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item v-if=\"!isPart || isPart && hasUnitPart\" label=\"区域名称\" prop=\"areaId\">\r\n          <el-tree-select\r\n            ref=\"treeSelect\"\r\n            v-model=\"queryForm.areaId\"\r\n            :disabled=\"!queryForm.projectId\"\r\n            :select-params=\"{\r\n              clearable: true,\r\n            }\"\r\n            class=\"cs-tree-x\"\r\n            :tree-params=\"treeParams\"\r\n            @select-clear=\"areaClear\"\r\n            @node-click=\"areaChange\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item v-if=\"!isPart||isPart && hasUnitPart\" label=\"批次\" prop=\"install\">\r\n          <el-select\r\n            v-model=\"queryForm.install\"\r\n            :disabled=\"!queryForm.areaId\"\r\n            clearable\r\n            placeholder=\"请选择\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in installOption\"\r\n              :key=\"item.Id\"\r\n              :label=\"item.Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\" prop=\"Task_Status\">\r\n          <el-select\r\n            v-model=\"queryForm.Task_Status\"\r\n            clearable\r\n            placeholder=\"请选择\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option label=\"已完成\" :value=\"1\" />\r\n            <el-option label=\"未完成\" :value=\"0\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"search(1)\">搜索</el-button>\r\n          <el-button @click=\"handleReset\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-divider />\r\n      <div class=\"btn-wrapper\">\r\n        <el-button type=\"primary\" :disabled=\"!selectArray.length\" @click=\"handleExport\">导出任务单列表</el-button>\r\n        <template v-if=\"isCom\">\r\n          <el-dropdown\r\n            trigger=\"click\"\r\n            placement=\"bottom-start\"\r\n            @command=\"handleCommand($event, 1)\"\r\n          >\r\n            <el-button\r\n              type=\"primary\"\r\n              :disabled=\"!selectArray.length\"\r\n            >导出任务单\r\n              <i class=\"el-icon-arrow-down el-icon--right\" />\r\n            </el-button>\r\n            <el-dropdown-menu slot=\"dropdown\">\r\n              <el-dropdown-item\r\n                command=\"name\"\r\n              >构件名称导出</el-dropdown-item>\r\n              <el-dropdown-item\r\n                command=\"code\"\r\n              >构件号合并导出</el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </el-dropdown>\r\n        </template>\r\n        <template v-if=\"isUnitPart\">\r\n          <el-button :disabled=\"!selectArray.length\" type=\"success\" @click=\"handleCommand('name')\">导出任务单</el-button>\r\n        </template>\r\n        <template v-if=\"isPart\">\r\n          <el-button :disabled=\"!selectArray.length\" type=\"success\" @click=\"handleCommand('name')\">导出任务单</el-button>\r\n        </template>\r\n      </div>\r\n      <div\r\n        v-loading=\"tbLoading\"\r\n        element-loading-text=\"加载中\"\r\n        element-loading-spinner=\"el-icon-loading\"\r\n        class=\"fff  cs-z-tb-wrapper\"\r\n      >\r\n        <dynamic-data-table\r\n          ref=\"dyTable\"\r\n          :columns=\"columns\"\r\n          :data=\"tbData\"\r\n          :config=\"tbConfig\"\r\n          :page=\"queryInfo.Page\"\r\n          :total=\"total\"\r\n          border\r\n          class=\"cs-plm-dy-table\"\r\n          stripe\r\n          @multiSelectedChange=\"getSelectVal\"\r\n          @gridPageChange=\"handlePageChange\"\r\n          @gridSizeChange=\"handlePageChange\"\r\n        >\r\n          <template slot=\"Task_Code\" slot-scope=\"{ row }\">\r\n            <el-link type=\"primary\" @click=\"handleView(row)\">{{ row.Task_Code }}</el-link>\r\n          </template>\r\n          <template slot=\"Task_Status\" slot-scope=\"{ row }\">\r\n            <el-tag v-if=\"row.Task_Status === 0\" type=\"danger\">未完成</el-tag>\r\n            <el-tag v-else type=\"success\">已完成</el-tag>\r\n          </template>\r\n          <template slot=\"op\" slot-scope=\"{ row }\">\r\n            <el-button\r\n              type=\"text\"\r\n              @click=\"handleView(row)\"\r\n            >查看\r\n            </el-button>\r\n            <template v-if=\"isCom\">\r\n              <el-dropdown\r\n                trigger=\"click\"\r\n                placement=\"bottom-start\"\r\n                style=\"margin-left: 12px;\"\r\n                @command=\"printSelected($event, row)\"\r\n              >\r\n                <el-button>打印任务单\r\n                  <i class=\"el-icon-arrow-down el-icon--right\" />\r\n                </el-button>\r\n                <el-dropdown-menu slot=\"dropdown\">\r\n                  <el-dropdown-item\r\n                    command=\"name\"\r\n                  >构件名称打印</el-dropdown-item>\r\n                  <el-dropdown-item\r\n                    command=\"code\"\r\n                  >构件号合并打印</el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </el-dropdown>\r\n            </template>\r\n            <template v-if=\"isUnitPart\">\r\n              <el-button type=\"text\" @click=\"printSelected('name', row)\">打印任务单</el-button>\r\n            </template>\r\n            <template v-if=\"isPart\">\r\n              <el-button type=\"text\" @click=\"printSelected('name', row)\">打印任务单</el-button>\r\n            </template>\r\n          </template>\r\n        </dynamic-data-table>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport getQueryInfo from '@/views/PRO/plan-production/schedule-production/mixin'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'\r\nimport { GetTeamTaskPageList, ExportTaskCodeDetails } from '@/api/PRO/production-task'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nimport { timeFormat } from '@/filters'\r\nimport { parseTime, combineURL, debounce } from '@/utils'\r\nimport { mapGetters } from 'vuex'\r\n\r\nexport default {\r\n  components: {\r\n    DynamicDataTable\r\n  },\r\n  mixins: [getQueryInfo, getTbInfo, addRouterPage],\r\n  props: {\r\n    pageType: {\r\n      type: String,\r\n      default: '-1'\r\n    },\r\n    hasUnitPart: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/detail',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/plan-production/task-list/detail'),\r\n          name: 'PROTaskListDetail',\r\n          meta: { title: '任务单详情' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/detailPrint',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/plan-production/task-list/detailPrint'),\r\n          name: 'PROTaskListDetailPrint',\r\n          meta: { title: '打印任务单详情' }\r\n        }\r\n      ],\r\n      queryForm: {\r\n        Task_Status: undefined,\r\n        Task_Code: undefined,\r\n        Schduling_Code: undefined\r\n      },\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: 20\r\n      },\r\n      tbConfig: {\r\n        Op_Width: 200,\r\n        IS_ToolTip: true\r\n      },\r\n      Loading: false,\r\n      tbLoading: false,\r\n      columns: [],\r\n      tbData: [],\r\n      selectArray: [],\r\n      total: 0,\r\n      search: () => ({})\r\n    }\r\n  },\r\n  computed: {\r\n    isPart() {\r\n      return this.pageType === '0'\r\n    },\r\n    isCom() {\r\n      return this.pageType === '-1'\r\n    },\r\n    isUnitPart() {\r\n      return +this.pageType > 0\r\n    }\r\n  },\r\n\r\n  created() {\r\n    this.tbConfig.Op_Width = this.isCom ? 200 : 140\r\n  },\r\n\r\n  async mounted() {\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    await this.getTableConfig((this.isCom || this.isUnitPart) ? 'PROComTaskList' : 'PROPartTaskList')\r\n\r\n    if (!this.hasUnitPart) {\r\n      const arr = ['Project_Name', 'Area_Name', 'InstallUnit_Name']\r\n      arr.forEach((item) => {\r\n        const idx = this.columns.findIndex((v) => v.Code === item)\r\n        if (idx !== -1) {\r\n          this.columns.splice(idx, 1)\r\n        }\r\n      })\r\n    }\r\n\r\n    if (this.isPart) {\r\n      const idx = this.columns.findIndex((item) => item.Code === 'Process_Finish_Date')\r\n      idx !== -1 && this.columns.splice(idx, 1)\r\n    }\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    getSelectVal(v) {\r\n      this.selectArray = v\r\n    },\r\n    // 导出任务单列表\r\n    handleExport() {\r\n      const filterVal = this.columns.map(v => v.Code)\r\n      const data = formatJson(filterVal, this.selectArray)\r\n      const header = this.columns.map(v => v.Display_Name)\r\n      import('@/vendor/Export2Excel').then(excel => {\r\n        excel.export_json_to_excel({\r\n          header: header,\r\n          data,\r\n          filename: `${this.isCom ? '构件任务单' : this.isUnitPart ? '部件任务单' : '零件任务单'}`,\r\n          autoWidth: true,\r\n          bookType: 'xlsx'\r\n        })\r\n      })\r\n      function formatJson(filterVal, jsonData) {\r\n        return jsonData.map(v => filterVal.map(j => {\r\n          if (j === 'Order_Date') {\r\n            return timeFormat(v[j])\r\n          } else if (j === 'Task_Status') {\r\n            return v[j] === 0 ? '未完成' : '已完成'\r\n          } else {\r\n            return v[j]\r\n          }\r\n        }))\r\n      }\r\n    },\r\n    fetchData(page) {\r\n      page && (this.queryInfo.Page = page)\r\n      this.tbLoading = true\r\n      GetTeamTaskPageList({\r\n        ...this.queryInfo,\r\n        Task_Code: this.queryForm.Task_Code,\r\n        Schduling_Code: this.queryForm.Schduling_Code,\r\n        Project_Id: this.queryForm.projectId,\r\n        Task_Status: this.queryForm.Task_Status,\r\n        Area_Id: this.queryForm.areaId,\r\n        InstallUnit_Id: this.queryForm.install,\r\n        Bom_Level: this.pageType,\r\n        Process_Type: this.isCom ? 2 : this.isPart ? 1 : 3 // 工序类型 1零件 2构件\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.Data.map((v) => {\r\n            v.Finish_Date = v.Finish_Date\r\n              ? parseTime(new Date(v.Finish_Date), '{y}-{m}-{d}')\r\n              : v.Finish_Date\r\n            v.Order_Date = v.Order_Date\r\n              ? parseTime(new Date(v.Order_Date), '{y}-{m}-{d}')\r\n              : v.Order_Date\r\n            v.Task_Finish_Date = v.Task_Finish_Date\r\n              ? parseTime(new Date(v.Task_Finish_Date), '{y}-{m}-{d}')\r\n              : v.Task_Finish_Date\r\n            v.Process_Finish_Date = v.Process_Finish_Date\r\n              ? parseTime(new Date(v.Process_Finish_Date), '{y}-{m}-{d}')\r\n              : v.Process_Finish_Date\r\n            return v\r\n          })\r\n          this.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.$refs['form'].resetFields()\r\n      this.search(1)\r\n    },\r\n    // 导出任务单详情\r\n    handleCommand(command, type) {\r\n      const TeamTaskModel = this.selectArray.map(v => {\r\n        return {\r\n          Task_Code: v.Task_Code,\r\n          Working_Team_Id: v.Working_Team_Id\r\n        }\r\n      })\r\n      const Working_Team_Id = this.selectArray.length === 1 ? this.selectArray[0].Working_Team_Id : ''\r\n      const Task_Code = this.selectArray.length === 1 ? this.selectArray[0].Task_Code : ''\r\n      this.Loading = true\r\n      ExportTaskCodeDetails({\r\n        Process_Type: this.isCom ? 2 : this.isPart ? 1 : 3, // 1零件，2构件\r\n        Working_Team_Id,\r\n        Task_Code,\r\n        Is_Merge: command === 'code',\r\n        ExportTeamTaskModel: TeamTaskModel\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '导出成功',\r\n            type: 'success'\r\n          })\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.Loading = false\r\n      })\r\n    },\r\n    // 打印\r\n    printSelected(command, row) {\r\n      const params = {\r\n        Task_Code: row.Task_Code,\r\n        Project_Name: row.Project_Name,\r\n        Area_Name: row.Area_Name,\r\n        InstallUnit_Name: row.InstallUnit_Name,\r\n        Schduling_Code: row.Schduling_Code,\r\n        Finish_Date: row.Finish_Date,\r\n        Order_Date: row.Order_Date,\r\n        Task_Finish_Date: row.Task_Finish_Date,\r\n        Process_Finish_Date: row.Process_Finish_Date,\r\n        Working_Team_Name: row.Working_Team_Name,\r\n        Working_Process_Name: row.Working_Process_Name,\r\n        Working_Team_Id: row.Working_Team_Id\r\n      }\r\n\r\n      this.$router.push({\r\n        name: 'PROTaskListDetailPrint',\r\n        query: {\r\n          type: this.pageType,\r\n          command: command,\r\n          pg_redirect: this.$route.name,\r\n          other: encodeURIComponent(JSON.stringify(params))\r\n        }\r\n      })\r\n    },\r\n\r\n    handleView(row) {\r\n      const {\r\n        Task_Code,\r\n        Project_Name,\r\n        Area_Name,\r\n        InstallUnit_Name,\r\n        Schduling_Code,\r\n        Finish_Date,\r\n        Order_Date,\r\n        Task_Finish_Date,\r\n        Process_Finish_Date,\r\n        Working_Team_Name,\r\n        Working_Process_Name\r\n      } = row\r\n      this.$router.push({\r\n        name: 'PROTaskListDetail',\r\n        query: {\r\n          id: row.Task_Code,\r\n          type: this.pageType,\r\n          tid: row.Working_Team_Id,\r\n          pg_redirect: this.$route.name,\r\n          // pg_redirect: this.isPart ? 'PROPartTaskList' : 'PROComTaskList',\r\n          other: encodeURIComponent(JSON.stringify({\r\n            Task_Code,\r\n            Project_Name,\r\n            Area_Name,\r\n            InstallUnit_Name,\r\n            Schduling_Code: Schduling_Code,\r\n            Finish_Date: Finish_Date,\r\n            Process_Finish_Date: Process_Finish_Date,\r\n            Order_Date: Order_Date,\r\n            Finish_Date2: Task_Finish_Date,\r\n            Working_Team_Name: Working_Team_Name,\r\n            Working_Process_Name: Working_Process_Name\r\n          }))\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.el-divider{\r\n  margin:0 0 10px;\r\n}\r\n.btn-wrapper{\r\n  margin-bottom: 10px;\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  & > button {\r\n    margin-right: 10px;\r\n  }\r\n  & > div {\r\n    margin-right: 10px;\r\n  }\r\n}\r\n.cs-z-page-main-content{\r\n  box-shadow: unset;\r\n}\r\n</style>\r\n"]}]}
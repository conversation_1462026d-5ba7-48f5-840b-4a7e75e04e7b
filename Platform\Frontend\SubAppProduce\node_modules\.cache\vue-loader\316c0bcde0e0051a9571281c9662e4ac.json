{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\plan\\setting.vue?vue&type=style&index=0&id=081e8584&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\plan\\setting.vue", "mtime": 1757572678863}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hcHAtY29udGFpbmVyew0KICBmb250LWZhbWlseTogUGluZ0ZhbmcgU0MsIFBpbmdGYW5nIFNDOw0KICBkaXNwbGF5OiBmbGV4Ow0KICAuY2FyZHsNCiAgICBmbGV4OjE7DQogICAgaGVpZ2h0OiAxMDAlOw0KICAgIG1hcmdpbi1sZWZ0OiAxNnB4Ow0KICAgIC5jYXJkLWhlYWRlciB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgIC5oZWFkZXItYnV0dG9ucyB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgIGdhcDogOHB4Ow0KDQogICAgICAgIC5wcm9qZWN0LWJ1dHRvbnMgew0KICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAgZ2FwOiA4cHg7DQogICAgICAgIH0NCg0KICAgICAgICAuZWwtYnV0dG9uIHsNCiAgICAgICAgICBtYXJnaW46IDA7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogICAgLmNvbnRlbnR7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICAgIGhlaWdodDogMTAwJTsNCiAgICB9DQogICAgLmJ0LXRhYmxlew0KICAgICAgZmxleDoxOw0KICAgIH0NCg0KICAgIC8vIOiuoeWIkumFjee9ruWSjOmihOitpuinhOWImXNlY3Rpb27moLflvI8NCiAgICAucGxhbi1jb25maWctc2VjdGlvbiB7DQogICAgICBtYXJnaW4tYm90dG9tOiAzMHB4Ow0KICAgIH0NCg0KICAgIC53YXJuLWNvbmZpZy1zZWN0aW9uIHsNCiAgICAgIGgzIHsNCiAgICAgICAgbWFyZ2luOiAwIDAgMjBweCAwOw0KICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7DQogICAgICAgIGNvbG9yOiAjMzAzMTMzOw0KICAgICAgICBwYWRkaW5nLWJvdHRvbTogOHB4Ow0KICAgICAgfQ0KICAgIH0NCg0KICAgIC8vIOmihOitpuinhOWImeagt+W8jw0KICAgIC53YXJuLXJ1bGVzIHsNCiAgICAgIC5ydWxlLWhlYWRlciB7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDIwcHg7DQogICAgICB9DQoNCiAgICAgIC5lbXB0eS1ydWxlcyB7DQogICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICAgICAgY29sb3I6ICM5OTk7DQogICAgICAgIHBhZGRpbmc6IDQwcHggMDsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgfQ0KDQogICAgICAucnVsZXMtbGlzdCB7DQogICAgICAgIC5ydWxlLWl0ZW0gew0KICAgICAgICAgIG1hcmdpbi1ib3R0b206IDIwcHg7DQogICAgICAgICAgcGFkZGluZzogMjBweDsNCiAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTRlN2VkOw0KICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmFmYWZhOw0KDQogICAgICAgICAgLmVsLWZvcm0taXRlbSB7DQogICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAwOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC5lbC1jb2w6bGFzdC1jaGlsZCB7DQogICAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgICAgICAgICAgcGFkZGluZy10b3A6IDMwcHg7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQogIC5oZWFkZXJ7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogZmxleC1lbmQ7DQogICAgLnByb2plY3QtbmFtZXsNCiAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgIGNvbG9yOiAjMzMzMzMzOw0KICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICBtYXJnaW4tcmlnaHQ6IDhweDsNCiAgICB9DQogICAgLmVsLWljb24tdGltZXsNCiAgICAgIG1hcmdpbi1sZWZ0OiA4cHg7DQogICAgICBtYXJnaW4tcmlnaHQ6IDRweDsNCiAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICB9DQogICAgLmxhYmVsew0KICAgICAgY29sb3I6ICMzMzMzMzM7DQogICAgICBmb250LXNpemU6IDEycHg7DQogICAgfQ0KICAgIC52YWx1ZXsNCiAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgIGNvbG9yOiAjMzMzMzMzOw0KICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICBtYXJnaW4tbGVmdDogN3B4Ow0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["setting.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2fA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "setting.vue", "sourceRoot": "src/views/plan", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <bt-tree :data=\"treeData\" :props=\"treeProps\" :default-selected-key=\"treeDefaultSelectedKey\" node-key=\"Sys_Project_Id\" @node-click=\"nodeClick\">\r\n      <template #default=\"{ data }\">\r\n        <span v-if=\"data.Code\" style=\"color: #5ac8fa!important;\">({{ data.Code }})</span>\r\n        <span>{{ data.Short_Name }}</span>\r\n      </template>\r\n    </bt-tree>\r\n    <!-- 计划配置和预警规则 -->\r\n    <el-card class=\"card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <span>计划配置</span>\r\n        <div class=\"header-buttons\">\r\n          <div v-if=\"showProjectButtons\" class=\"project-buttons\">\r\n            <el-button\r\n              :loading=\"resetLoading\"\r\n              size=\"small\"\r\n              type=\"primary\"\r\n              @click=\"resetConfig\"\r\n            >\r\n              重置配置\r\n            </el-button>\r\n            <el-button\r\n              :loading=\"syncLoading\"\r\n              size=\"small\"\r\n              type=\"primary\"\r\n              @click=\"syncToArea\"\r\n            >\r\n              同步至项目分区\r\n            </el-button>\r\n          </div>\r\n          <el-button\r\n            type=\"primary\"\r\n            size=\"small\"\r\n            @click=\"saveAllConfig\"\r\n          >\r\n            保存\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      <div class=\"content\">\r\n        <!-- 计划配置 - 只在默认配置时显示 -->\r\n        <div v-if=\"showPlanConfig\" class=\"plan-config-section\">\r\n          <el-form style=\"width: 782px\" inline>\r\n            <el-row v-for=\"item in nodeData\" :key=\"item.Plan_Type\" :gutter=\"50\">\r\n              <el-form-item :label=\"item.Plan_Name||' '\" label-width=\"100px\">\r\n                <el-input v-model=\"item.Plan_Name\" style=\"width:150px\" clearable />\r\n              </el-form-item>\r\n              <el-form-item label=\"计划下发通知人\" label-width=\"150px\">\r\n                <el-select\r\n                  v-model=\"item.Plan_Notice_Userids\"\r\n                  placeholder=\"请选择\"\r\n                  style=\"width: 400px\"\r\n                  clearable\r\n                  filterable\r\n                  multiple\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in userList\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Display_Name\"\r\n                    :value=\"item.Id\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-row>\r\n\r\n          </el-form>\r\n        </div>\r\n\r\n        <!-- 预警规则 - 在非默认配置时显示 -->\r\n        <div v-if=\"showWarnConfig\" class=\"warn-config-section\">\r\n          <h3>预警规则</h3>\r\n          <!-- Tab切换 -->\r\n          <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\r\n            <el-tab-pane\r\n              v-for=\"item in nodeData\"\r\n              :key=\"item.Plan_Type\"\r\n              :label=\"item.Plan_Name\"\r\n              :name=\"item.Plan_Type.toString()\"\r\n            >\r\n              <!-- 预警规则表单 -->\r\n              <div class=\"warn-rules\">\r\n                <div class=\"rule-header\">\r\n                  <el-button size=\"small\" type=\"primary\" @click=\"addWarnRule\">新增预警规则</el-button>\r\n                </div>\r\n                <div v-if=\"currentWarnRules.length === 0\" class=\"empty-rules\">\r\n                  暂无预警规则\r\n                </div>\r\n                <el-form v-else>\r\n                  <div class=\"rules-list\">\r\n                    <div v-for=\"(rule, index) in currentWarnRules\" :key=\"index\" class=\"rule-item\">\r\n                      <el-row :gutter=\"20\">\r\n                        <el-col :span=\"4\">\r\n                          <el-form-item label=\"工期进度达到\" required>\r\n                            <el-input\r\n                              v-model=\"rule.Progress_Percent\"\r\n                              placeholder=\"70%\"\r\n                              style=\"width: 100%\"\r\n                            >\r\n                              <template slot=\"append\">%</template>\r\n                            </el-input>\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"4\">\r\n                          <el-form-item label=\"完成进度达到\" required>\r\n                            <el-input\r\n                              v-model=\"rule.Finish_Percent\"\r\n                              placeholder=\"70%\"\r\n                              style=\"width: 100%\"\r\n                            >\r\n                              <template slot=\"append\">%</template>\r\n                            </el-input>\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"14\">\r\n                          <el-form-item label=\"计划预警通知人\" required>\r\n                            <el-select\r\n                              v-model=\"rule.Plan_Notice_Userids\"\r\n                              placeholder=\"下拉选择当前公司下标准人，支持搜索\"\r\n                              style=\"width: 100%\"\r\n                              clearable\r\n                              filterable\r\n                              multiple\r\n                            >\r\n                              <el-option\r\n                                v-for=\"user in userList\"\r\n                                :key=\"user.Id\"\r\n                                :label=\"user.Display_Name\"\r\n                                :value=\"user.Id\"\r\n                              />\r\n                            </el-select>\r\n                          </el-form-item>\r\n                        </el-col>\r\n                        <el-col :span=\"2\">\r\n                          <el-button\r\n                            type=\"danger\"\r\n                            icon=\"el-icon-delete\"\r\n                            size=\"small\"\r\n                            @click=\"deleteWarnRule(index)\"\r\n                          />\r\n                        </el-col>\r\n                      </el-row>\r\n                    </div>\r\n                  </div>\r\n\r\n                </el-form>\r\n              </div>\r\n            </el-tab-pane>\r\n          </el-tabs>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { GetConfigs, GetCurrCompanyProjectList, SaveConfigs } from '@/api/plm/projects'\r\nimport { GetUserPage } from '@/api/sys'\r\nimport { GetWarnConfigs, SaveWarnConfigs, ResetWarnConfig, SyncAreaWarnConfig } from '@/api/PRO/control-plan'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      userList: [],\r\n      nodeData: [],\r\n      companyId: localStorage.getItem('CurReferenceId'),\r\n      treeData: [],\r\n      treeProps: {\r\n        label: 'Short_Name',\r\n        id: 'Sys_Project_Id'\r\n      },\r\n      treeDefaultSelectedKey: '',\r\n      params: {\r\n        CompanyId: localStorage.getItem('CurReferenceId'),\r\n        ProjectId: '',\r\n        AreaId: ''\r\n      },\r\n      resetLoading: false,\r\n      syncLoading: false,\r\n      activeTab: '',\r\n      warnRules: {} // 存储各个计划类型的预警规则\r\n    }\r\n  },\r\n  computed: {\r\n    showProjectButtons() {\r\n      // 只有在选中项目级节点（非默认配置且非区域）时才显示按钮\r\n      return this.params.ProjectId !== '' &&\r\n              this.params.ProjectId !== 'default_config' &&\r\n              this.params.AreaId === ''\r\n    },\r\n    showPlanConfig() {\r\n      // 只有在默认配置时才显示计划配置\r\n      return this.params.ProjectId === '' && this.params.AreaId === ''\r\n    },\r\n    showWarnConfig() {\r\n      // 预警规则无论何种情况都显示\r\n      return true\r\n    },\r\n    currentWarnRules() {\r\n      // 获取当前选中tab的预警规则\r\n      if (!this.activeTab || !this.warnRules[this.activeTab]) {\r\n        return []\r\n      }\r\n      return this.warnRules[this.activeTab]\r\n    }\r\n  },\r\n  async created() {\r\n    await this.getTreeData()\r\n    await this.getList()\r\n  },\r\n  methods: {\r\n    nodeClick(node) {\r\n      this.curProject = node\r\n\r\n      // 判断是否为默认配置节点\r\n      if (node.isDefault) {\r\n        this.params.ProjectId = ''\r\n        this.params.AreaId = ''\r\n        // 默认配置时也需要请求预警规则数据\r\n        if (this.nodeData.length > 0) {\r\n          this.activeTab = this.nodeData[0].Plan_Type.toString()\r\n          this.$nextTick(() => {\r\n            this.getWarnRules()\r\n          })\r\n        }\r\n        return\r\n      }\r\n\r\n      // 判断节点层级\r\n      // 如果节点没有父级ID或父级ID为空，说明是第一级（项目）\r\n      if (!node.Parent_Id || node.Parent_Id === '' || node.Parent_Id === null) {\r\n        // 第一级节点：设置ProjectId为当前节点ID，AreaId为空\r\n        this.params.ProjectId = node.Sys_Project_Id\r\n        this.params.AreaId = ''\r\n      } else {\r\n        // 其他级别节点（区域）：设置AreaId为当前节点ID，ProjectId为所属第一级节点ID\r\n        this.params.AreaId = node.Sys_Project_Id\r\n        // 查找所属的第一级节点ID\r\n        this.params.ProjectId = this.findTopLevelParent(node)\r\n      }\r\n\r\n      // 初始化activeTab并获取预警规则数据\r\n      if (this.nodeData.length > 0) {\r\n        this.activeTab = this.nodeData[0].Plan_Type.toString()\r\n        this.$nextTick(() => {\r\n          // 当节点变化时，重新请求预警规则数据\r\n          this.getWarnRules()\r\n        })\r\n      }\r\n\r\n      console.log(this.params)\r\n    },\r\n    async getTreeData() {\r\n      try {\r\n        const res = await GetCurrCompanyProjectList({\r\n          companId: localStorage.getItem('CurReferenceId'),\r\n          IsCascade: false\r\n        })\r\n\r\n        // 在最前面添加【默认配置】节点\r\n        const defaultNode = {\r\n          Sys_Project_Id: '',\r\n          Short_Name: '【默认配置】',\r\n          Code: '',\r\n          loading: false,\r\n          isDefault: true // 标记为默认配置节点\r\n        }\r\n\r\n        this.treeData = [defaultNode, ...res.Data.map(item => {\r\n          item.loading = false\r\n          return item\r\n        })]\r\n\r\n        // 默认选中【默认配置】节点\r\n        if (this.treeData.length) {\r\n          this.treeDefaultSelectedKey = ''\r\n          this.curProject = defaultNode\r\n          // 设置默认配置的参数\r\n          this.params.ProjectId = ''\r\n          this.params.AreaId = ''\r\n\r\n          // 先获取配置列表数据，然后再触发nodeClick\r\n          await this.getConfigList()\r\n          this.$nextTick(() => {\r\n            this.nodeClick(defaultNode)\r\n          })\r\n        }\r\n      } catch (error) {\r\n        console.error('获取树形数据失败:', error)\r\n      }\r\n    },\r\n    async getList() {\r\n      const res = await GetUserPage({\r\n        DepartmentId: this.departmentId,\r\n        PageSize: 10000\r\n      })\r\n      this.userList = res.Data.Data.filter(item => item.UserStatusName === '正常')\r\n    },\r\n    async getConfigList() {\r\n      try {\r\n        const res = await GetConfigs({\r\n          CompanyId: this.companyId\r\n        })\r\n        this.$set(this, 'nodeData', res.Data)\r\n        return res.Data\r\n      } catch (error) {\r\n        console.error('获取配置列表失败:', error)\r\n        return []\r\n      }\r\n    },\r\n    async saveConfig() {\r\n      try {\r\n        const res = await SaveConfigs(this.nodeData)\r\n        if (res.IsSucceed) {\r\n          this.$message.success('计划配置保存成功')\r\n          this.getConfigList()\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('计划配置保存失败')\r\n        console.error('保存计划配置错误:', error)\r\n      }\r\n    },\r\n    // 查找顶级父节点ID\r\n    findTopLevelParent(node) {\r\n      // 递归查找直到找到没有父级的节点\r\n      const findParent = (currentNode) => {\r\n        if (!currentNode.Parent_Id || currentNode.Parent_Id === '' || currentNode.Parent_Id === null) {\r\n          return currentNode.Sys_Project_Id\r\n        }\r\n        // 在树数据中查找父节点\r\n        const parentNode = this.findNodeById(currentNode.Parent_Id)\r\n        if (parentNode) {\r\n          return findParent(parentNode)\r\n        }\r\n        return currentNode.Sys_Project_Id\r\n      }\r\n      return findParent(node)\r\n    },\r\n    // 根据ID查找节点\r\n    findNodeById(id) {\r\n      const findInTree = (nodes) => {\r\n        for (const node of nodes) {\r\n          if (node.Sys_Project_Id === id) {\r\n            return node\r\n          }\r\n          if (node.children && node.children.length > 0) {\r\n            const found = findInTree(node.children)\r\n            if (found) return found\r\n          }\r\n        }\r\n        return null\r\n      }\r\n      return findInTree(this.treeData)\r\n    },\r\n    // 重置配置\r\n    async resetConfig() {\r\n      this.resetLoading = true\r\n      try {\r\n        const res = await ResetWarnConfig({\r\n          CompanyId: this.companyId,\r\n          ProjectId: this.params.ProjectId\r\n        })\r\n        if (res.IsSucceed) {\r\n          this.$message.success('重置配置成功')\r\n          this.getConfigList() // 重新获取配置列表\r\n        } else {\r\n          this.$message.error(res.Message || '重置配置失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('重置配置失败')\r\n        console.error('重置配置错误:', error)\r\n      } finally {\r\n        this.resetLoading = false\r\n      }\r\n    },\r\n    // 同步至项目分区\r\n    async syncToArea() {\r\n      this.syncLoading = true\r\n      try {\r\n        const res = await SyncAreaWarnConfig({\r\n          CompanyId: this.companyId,\r\n          ProjectId: this.params.ProjectId\r\n        })\r\n        if (res.IsSucceed) {\r\n          this.$message.success('同步至项目分区成功')\r\n        } else {\r\n          this.$message.error(res.Message || '同步至项目分区失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('同步至项目分区失败')\r\n        console.error('同步至项目分区错误:', error)\r\n      } finally {\r\n        this.syncLoading = false\r\n      }\r\n    },\r\n    // Tab切换处理\r\n    handleTabClick(tab) {\r\n      this.activeTab = tab.name\r\n      // 不重新请求数据，避免数据丢失\r\n    },\r\n    // 新增预警规则\r\n    addWarnRule() {\r\n      if (!this.warnRules[this.activeTab]) {\r\n        this.$set(this.warnRules, this.activeTab, [])\r\n      }\r\n      const newRule = {\r\n        Plan_Type: parseInt(this.activeTab),\r\n        Project_Id: this.params.ProjectId,\r\n        Area_Id: this.params.AreaId,\r\n        Progress_Percent: '',\r\n        Finish_Percent: '',\r\n        Plan_Notice_Userids: []\r\n      }\r\n      this.warnRules[this.activeTab].push(newRule)\r\n    },\r\n    // 删除预警规则\r\n    deleteWarnRule(index) {\r\n      this.$confirm('确定要删除这条预警规则吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.warnRules[this.activeTab].splice(index, 1)\r\n        this.$message.success('删除成功')\r\n      }).catch(() => {})\r\n    },\r\n    // 获取预警规则\r\n    async getWarnRules() {\r\n      try {\r\n        const res = await GetWarnConfigs({\r\n          CompanyId: this.companyId,\r\n          ProjectId: this.params.ProjectId,\r\n          AreaId: this.params.AreaId,\r\n          Plan_Type: parseInt(this.activeTab)\r\n        })\r\n        if (res.IsSucceed) {\r\n          this.$set(this.warnRules, this.activeTab, res.Data || [])\r\n        }\r\n      } catch (error) {\r\n        console.error('获取预警规则失败:', error)\r\n      }\r\n    },\r\n    // 保存所有配置\r\n    async saveAllConfig() {\r\n      try {\r\n        // 如果显示计划配置，保存计划配置\r\n        if (this.showPlanConfig && this.nodeData && this.nodeData.length) {\r\n          await this.saveConfig()\r\n        }\r\n\r\n        // 如果显示预警配置，保存预警配置\r\n        if (this.showWarnConfig) {\r\n          await this.saveWarnConfig()\r\n        }\r\n\r\n        // 如果两个都不显示或都没有数据，提示用户\r\n        if (!this.showPlanConfig && !this.showWarnConfig) {\r\n          this.$message.warning('暂无可保存的配置')\r\n        }\r\n      } catch (error) {\r\n        console.error('保存配置失败:', error)\r\n      }\r\n    },\r\n    // 保存预警配置\r\n    async saveWarnConfig() {\r\n      // 校验必填项\r\n      const rules = this.currentWarnRules\r\n      if (rules.length > 0) {\r\n        for (let i = 0; i < rules.length; i++) {\r\n          const rule = rules[i]\r\n          if (!rule.Progress_Percent || !rule.Finish_Percent || !rule.Plan_Notice_Userids || rule.Plan_Notice_Userids.length === 0) {\r\n            this.$message.error(`第${i + 1}条预警规则存在必填项未填写`)\r\n            return\r\n          }\r\n          // 验证百分比格式\r\n          if (isNaN(rule.Progress_Percent) || isNaN(rule.Finish_Percent)) {\r\n            this.$message.error(`第${i + 1}条预警规则的百分比必须为数字`)\r\n            return\r\n          }\r\n        }\r\n      }\r\n\r\n      try {\r\n        const res = await SaveWarnConfigs({\r\n          CompanyId: this.companyId,\r\n          ProjectId: this.params.ProjectId,\r\n          AreaId: this.params.AreaId,\r\n          Items: rules\r\n        })\r\n        if (res.IsSucceed) {\r\n          this.$message.success('预警规则保存成功')\r\n          this.getWarnRules() // 重新获取数据\r\n        } else {\r\n          this.$message.error(res.Message || '预警规则保存失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('保存失败')\r\n        console.error('保存预警配置错误:', error)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container{\r\n  font-family: PingFang SC, PingFang SC;\r\n  display: flex;\r\n  .card{\r\n    flex:1;\r\n    height: 100%;\r\n    margin-left: 16px;\r\n    .card-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      .header-buttons {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 8px;\r\n\r\n        .project-buttons {\r\n          display: flex;\r\n          gap: 8px;\r\n        }\r\n\r\n        .el-button {\r\n          margin: 0;\r\n        }\r\n      }\r\n    }\r\n    .content{\r\n      display: flex;\r\n      flex-direction: column;\r\n      height: 100%;\r\n    }\r\n    .bt-table{\r\n      flex:1;\r\n    }\r\n\r\n    // 计划配置和预警规则section样式\r\n    .plan-config-section {\r\n      margin-bottom: 30px;\r\n    }\r\n\r\n    .warn-config-section {\r\n      h3 {\r\n        margin: 0 0 20px 0;\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        color: #303133;\r\n        padding-bottom: 8px;\r\n      }\r\n    }\r\n\r\n    // 预警规则样式\r\n    .warn-rules {\r\n      .rule-header {\r\n        margin-bottom: 20px;\r\n      }\r\n\r\n      .empty-rules {\r\n        text-align: center;\r\n        color: #999;\r\n        padding: 40px 0;\r\n        font-size: 14px;\r\n      }\r\n\r\n      .rules-list {\r\n        .rule-item {\r\n          margin-bottom: 20px;\r\n          padding: 20px;\r\n          border: 1px solid #e4e7ed;\r\n          border-radius: 4px;\r\n          background-color: #fafafa;\r\n\r\n          .el-form-item {\r\n            margin-bottom: 0;\r\n          }\r\n\r\n          .el-col:last-child {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            padding-top: 30px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .header{\r\n    display: flex;\r\n    align-items: flex-end;\r\n    .project-name{\r\n      font-size: 16px;\r\n      color: #333333;\r\n      font-weight: bold;\r\n      margin-right: 8px;\r\n    }\r\n    .el-icon-time{\r\n      margin-left: 8px;\r\n      margin-right: 4px;\r\n      font-size: 14px;\r\n    }\r\n    .label{\r\n      color: #333333;\r\n      font-size: 12px;\r\n    }\r\n    .value{\r\n      font-size: 14px;\r\n      color: #333333;\r\n      font-weight: bold;\r\n      margin-left: 7px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\ProjectAddDialog.vue?vue&type=style&index=0&id=55c7b122&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\ProjectAddDialog.vue", "mtime": 1757991914477}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouZm9ybS13cmFwcGVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICBtYXgtaGVpZ2h0OiA3MHZoOwoKICAuYnRuLXggewogICAgcGFkZGluZy10b3A6IDE2cHg7CiAgICB0ZXh0LWFsaWduOiByaWdodDsKICB9CiAgLmluc3RydWN0aW9uIHsKICAgIGJhY2tncm91bmQ6ICNmMGY5ZmY7CiAgICBib3JkZXI6IDFweCBzb2xpZCAjYjNkOGZmOwogICAgY29sb3I6ICMxODkwZmY7CiAgICBwYWRkaW5nOiAxMnB4IDE2cHg7CiAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICBtYXJnaW4tYm90dG9tOiAxNnB4OwogICAgZm9udC13ZWlnaHQ6IDUwMDsKICB9Cn0K"}, {"version": 3, "sources": ["ProjectAddDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0EA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ProjectAddDialog.vue", "sourceRoot": "src/views/PRO/project-config/project-quality/components/Dialog", "sourcesContent": ["<template>\n  <div class=\"form-wrapper\">\n    <div class=\"instruction\">请选择项目，添加所选项目的<span>所有质检配置</span></div>\n    <div>\n      <el-form ref=\"form\" :model=\"form\" label-width=\"82px\">\n        <el-form-item v-if=\"!projectList.length\" label=\"项目名称：\">\n          <div>暂无可同步的项目</div>\n        </el-form-item>\n        <el-form-item v-else label=\"项目名称：\">\n          <el-select v-model=\"form.From_Sys_Project_Id\" placeholder=\"请选择项目\" style=\"width: 300px\">\n            <el-option v-for=\"(item, index) in projectList\" :key=\"index\" :label=\"item.Short_Name\" :value=\"item.Sys_Project_Id\" :disabled=\"item.Sys_Project_Id===sysProjectId\" />\n          </el-select>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"btn-x\">\n      <el-button @click=\"$emit('close')\">取 消</el-button>\n      <el-button v-if=\"projectList.length\" type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetProcessOfProjectList, SyncProjectProcessFromProject } from '@/api/PRO/technology-lib'\nexport default {\n  components: {\n  },\n  props: {\n    sysProjectId: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      btnLoading: false,\n      projectList: [],\n      form: {\n        From_Sys_Project_Id: ''\n      }\n    }\n  },\n  async mounted() {\n    await this.getProcessOfProjectList()\n  },\n  methods: {\n    async getProcessOfProjectList() {\n      const res = await GetProcessOfProjectList({ })\n      if (res.IsSucceed) {\n        this.projectList = res.Data\n      }\n    },\n    handleSubmit() {\n      if (this.form.From_Sys_Project_Id === '') return this.$message.warning('请选择项目')\n      this.btnLoading = true\n      SyncProjectProcessFromProject({\n        From_Sys_Project_Id: this.form.From_Sys_Project_Id,\n        To_Sys_Project_Id: this.sysProjectId\n      }).then(res => {\n        if (res.IsSucceed) {\n          this.$emit('refresh')\n          this.$emit('close')\n          this.$message.success('同步成功！')\n        } else {\n          this.$message.error(res.msg)\n        }\n        this.btnLoading = false\n      })\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n  .form-wrapper {\n    display: flex;\n    flex-direction: column;\n    overflow: hidden;\n    max-height: 70vh;\n\n    .btn-x {\n      padding-top: 16px;\n      text-align: right;\n    }\n    .instruction {\n      background: #f0f9ff;\n      border: 1px solid #b3d8ff;\n      color: #1890ff;\n      padding: 12px 16px;\n      border-radius: 4px;\n      margin-bottom: 16px;\n      font-weight: 500;\n    }\n  }\n</style>\n"]}]}
<template>
  <div class="app-container abs100">
    <div class="cs-box mb10">
      <el-row>
        <el-form ref="form" :model="form" label-width="100px">
          <el-col :span="6">
            <el-form-item label="项目名称" prop="Sys_Project_Id">
              <el-select
                v-model="form.Sys_Project_Id"
                clearable
                style="width: 100%"
                placeholder="请选择"
                filterable
                @change="projectChange(form.Sys_Project_Id)"
              >
                <el-option
                  v-for="item in projectList"
                  :key="item.Sys_Project_Id"
                  :label="item.Short_Name"
                  :value="item.Sys_Project_Id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="区域" prop="Area_Id">
              <el-tree-select
                ref="treeSelectArea"
                v-model="form.Area_Id"
                class="cs-tree-x"
                :disabled="!form.Sys_Project_Id"
                :select-params="{
                  clearable: true,
                }"
                :tree-params="treeParamsArea"
                @select-clear="areaClear"
                @node-click="areaChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="单据状态" prop="Status">
              <el-select v-model="form.Status" clearable placeholder="请选择" style="width: 100%">
                <el-option label="草稿" :value="1" />
                <el-option label="审核中" :value="2" />
                <el-option label="审核未通过" :value="-2" />
                <el-option label="审核通过" :value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="变更类型" prop="Moc_Type_Id">
              <el-select v-model="form.Moc_Type_Id" placeholder="请选择" clearable="">
                <el-option
                  v-for="item in mocType"
                  :key="item.Id"
                  :label="item.Display_Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="执行情况" prop="Exec_Status">
              <el-select v-model="form.Exec_Status" clearable placeholder="请选择" style="width: 100%">
                <el-option label="未开始" :value="1" />
                <el-option label="执行中" :value="2" />
                <el-option label="已完成" :value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="变更日期" prop="Change_Date">
              <el-date-picker
                v-model="form.Change_Date"
                value-format="yyyy-MM-dd"
                style="width: 100%"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="紧急程度" prop="Urgency">
              <el-select v-model="form.Urgency" clearable placeholder="请选择" style="width: 100%">
                <el-option label="普通" :value="1" />
                <el-option label="紧急" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label-width="20px">
              <el-button type="primary" @click="search(1)">搜索</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
    </div>
    <div class="cs-box cs-main">
      <vxe-toolbar
        ref="xToolbar1"
        class="cs-toolBar"
      >
        <template #buttons>
          <el-button type="primary" @click="handleAdd">新建联系单</el-button>
        </template>
        <template #tools>
          <el-button type="primary" @click="handleSetting">变更类型配置</el-button>
          <DynamicTableFields
            title="表格配置"
            :table-config-code="gridCode"
            @updateColumn="changeColumn"
          />
        </template>
      </vxe-toolbar>

      <div class="cs-bottom-wapper">
        <div class="fff tb-x">
          <vxe-table
            :empty-render="{name: 'NotData'}"
            show-header-overflow
            :loading="tbLoading"
            element-loading-spinner="el-icon-loading"
            element-loading-text="拼命加载中"
            empty-text="暂无数据"
            class="cs-vxe-table"
            height="100%"
            align="left"
            stripe
            :data="tbData"
            resizable
            :tooltip-config="{ enterable: true}"
            :checkbox-config="{checkField: 'checked'}"
            @checkbox-all="multiSelectedChange"
            @checkbox-change="multiSelectedChange"
          >
            <!--            <vxe-column fixed="left" type="checkbox" width="60" />-->
            <template v-for="item in columns">
              <vxe-column
                :key="item.Code"
                :min-width="item.Width"
                show-overflow="tooltip"
                sortable
                align="center"
                :field="item.Code"
                :title="item.Display_Name"
                :fixed="item.Is_Frozen ? (item.Frozen_Dirction || 'left') : ''"
              >
                <template v-if="['Change_Date','Demand_Date','Create_Date','Change_End'].includes(item.Code) " #default="{ row }">
                  {{ row[item.Code] | timeFormat }}
                </template>
                <template v-else-if="item.Code === 'Exec_Status'" #default="{ row }">
                  <span :class="['cs-tags',row[item.Code]===1?'cs-red':row[item.Code]===2?'cs-blue':'cs-green']">{{ row[item.Code]===1?'未开始':row[item.Code]===2?'执行中':row[item.Code]===3?'已完成':'' }}</span>
                </template>
                <template v-else-if="item.Code === 'Urgency'" #default="{ row }">
                  <el-tag v-if="row.Urgency == 1" type="primary">普通</el-tag>
                  <el-tag v-else-if="row.Urgency == 2" type="danger">紧急</el-tag>
                  <span v-else>-</span>
                </template>
                <!--                <template v-else-if="item.Code === 'Change_Type'" #default="{ row }">-->
                <!--                  <span> {{ row[item.Code] ==='0'?'完整变更':row[item.Code] ==='1'?'部分变更':row[item.Code] ==='2'?'手动变更':'' }}</span>-->
                <!--                </template>-->
                <template v-else #default="{ row }">
                  <span> {{ row[item.Code] | displayValue }}</span>
                </template>
              </vxe-column>
            </template>
            <vxe-column fixed="right" title="操作" width="180">
              <template #default="{ row }">
                <template v-for="btn in getButtonsByStatus(row.Status,row)">
                  <el-button
                    v-if="btn.checkKey(btn.key,row)"
                    :key="btn.text"
                    :class="{'txt-red':btn.isRed}"
                    type="text"
                    @click="btn.handler(row)"
                  >{{ btn.text }}</el-button>
                </template>

              </template>
            </vxe-column>
          </vxe-table>
        </div>
        <div class="data-info">
          <!--          <el-tag-->
          <!--            size="medium"-->
          <!--            class="info-x"-->
          <!--          >已选 {{ multipleSelection.length }} 条数据-->
          <!--          </el-tag>-->
          <Pagination
            :total="total"
            :page-sizes="tablePageSize"
            :page.sync="queryInfo.Page"
            :limit.sync="queryInfo.PageSize"
            @pagination="pageChange"
          />
        </div>
      </div>
    </div>

    <el-dialog
      v-dialogDrag
      title="变更类型"
      :visible.sync="dialogVisible"
      width="50%"
      class="plm-custom-dialog cs-dialog"
      @close="dialogVisible=false"
    >
      <div>
        <vxe-toolbar>
          <template #buttons>
            <vxe-button status="primary" content="添加" @click="handleAddSetting()" />
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="xTable"
          :empty-render="{name: 'NotData'}"
          show-header-overflow
          border
          class="cs-vxe-table"
          stripe
          resizable
          show-overflow
          :loading="settingLoading"
          :data="dialogTable"
          :edit-config="{beforeEditMethod: activeRowMethod,trigger: 'click',showStatus: true, mode: 'row'}"
          @edit-closed="editClosedEvent"
          @edit-disabled="editDisabledEvent"
        >

          <vxe-column
            align="left"
            field="Display_Name"
            title="类型名称"
            min-width="180"
            :edit-render="{autofocus: '.vxe-input--inner'}"
          >
            <template #edit="{ row }">
              <vxe-input v-model="row.Display_Name" type="text" />
            </template>
          </vxe-column>
          <vxe-column
            align="left"
            field="Is_Deepen_Change"
            title="是否变更清单"
            min-width="180"
            :edit-render="{}"
          >
            <template #edit="{ row }">
              <el-radio v-model="row.Is_Deepen_Change" :label="true">是</el-radio>
              <el-radio v-model="row.Is_Deepen_Change" :label="false">否</el-radio>
            </template>
            <template #default="{row}">
              <el-tag v-if=" row.Is_Deepen_Change" type="success">是</el-tag>
              <el-tag v-else type="danger">否</el-tag>
            </template>
          </vxe-column>
          <vxe-column
            align="left"
            title="操作"
          >
            <template #default="{ row }">
              <el-button type="text" class="txt-red" @click="removeRowEvent(row)">删除</el-button>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>

    <Monitor ref="monitor" />
  </div>
</template>

<script>
import { tablePageSize } from '@/views/PRO/setting'
import getTbInfo from '@/mixins/PRO/get-table-info'
import Pagination from '@/components/Pagination/index.vue'
import {
  DeleteMocType,
  DeleteMocOrder,
  GetMocOrderPageList, GetMocOrderTypeList,
  SaveMocOrderType, ChangeMocOrderStatus, SubmitMocOrder
} from '@/api/PRO/changeManagement'
import addRouterPage from '@/mixins/add-router-page'
import { GeAreaTrees, GetProjectPageList } from '@/api/PRO/project'
import Monitor from '@/components/Monitor/index.vue'
import { CancelFlow } from '@/api/PRO/component-stock-out'
import DynamicTableFields from '@/components/DynamicTableFields/index.vue'
import { debounce } from '@/utils'

export default {
  name: 'PROEngineeringChangeOrder',
  components: {
    DynamicTableFields,
    Monitor,
    Pagination
  },
  mixins: [getTbInfo, addRouterPage],
  data() {
    return {
      addPageArray: [
        {
          path: this.$route.path + '/add',
          hidden: true,
          component: () => import('./add.vue'),
          name: 'PROEngineeringChangeOrderAdd',
          meta: { title: '新增' }
        },
        {
          path: this.$route.path + '/edit',
          hidden: true,
          component: () => import('./add.vue'),
          name: 'PROEngineeringChangeOrderEdit',
          meta: { title: '编辑' }
        },
        {
          path: this.$route.path + '/view',
          hidden: true,
          component: () => import('./add.vue'),
          name: 'PROEngineeringChangeOrderView',
          meta: { title: '查看' }
        }
      ],
      form: {
        Sys_Project_Id: '',
        Status: '',
        Exec_Status: '',
        Change_Date: '',
        Moc_Type_Id: '',
        Area_Id: '',
        Urgency: ''
      },
      activeName: 'second',
      dialogVisible: false,
      tbLoading: false,
      settingLoading: false,
      tbData: [],
      projectList: [],
      dialogTable: [],
      mocType: [],
      installUnitList: [],
      treeParamsArea: {
        'default-expand-all': true,
        filterable: false,
        clickParent: true,
        data: [],
        props: {
          disabled: 'disabled',
          children: 'Children',
          label: 'Label',
          value: 'Id'
        }
      },
      tbConfig: {},
      multipleSelection: [],
      search: () => ({}),
      columns: [],
      gridCode: 'PROEngChangeOrder',
      tablePageSize: tablePageSize,
      queryInfo: {
        Page: 1,
        PageSize: tablePageSize[0]
      },
      total: 0,
      buttonConfigs: {
        draft: [
          { text: '提交审核', handler: this.handleSubmitAudit, checkKey: this.checkKey },
          { text: '编辑', handler: this.handleEdit, checkKey: this.checkKey },
          // { text: '监控', handler: this.handleMonitor, checkKey: this.checkKey },
          { text: '删除', isRed: true, handler: this.handleDelete, checkKey: this.checkKey }
        ],
        reviewing: [
          { text: '查看', handler: this.handleView, checkKey: this.checkKey },
          { text: '监控', key: 'monitor', handler: this.handleMonitor, checkKey: this.checkKey },
          { text: '回收', handler: this.handleRecycle, checkKey: this.checkKey }
        ],
        approved: [
          { text: '查看', handler: this.handleView, checkKey: this.checkKey },
          { text: '监控', key: 'monitor', handler: this.handleMonitor, checkKey: this.checkKey }
        ],
        finish: [
          { text: '完成', handler: this.handleComplete, checkKey: this.checkKey }
        ]
      }
    }
  },
  mounted() {
    this.search = debounce(this.fetchData, 800, true)
    this.getTableConfig(this.gridCode)
    this.fetchData(1)
    this.getBasicData()
    this.getSettingInfo()
  },
  methods: {
    fetchData(page) {
      page && (this.queryInfo.Page = page)
      const { Change_Date, ...others } = this.form
      const Change_Begin = Change_Date[0]
      const Change_End = Change_Date[1]
      this.tbLoading = true
      GetMocOrderPageList({ ...others, Change_Begin, Change_End, ...this.queryInfo }).then(res => {
        if (res.IsSucceed) {
          this.tbData = res?.Data?.Data || []
          this.total = res.Data.TotalCount
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
        this.tbLoading = false
      }).catch(() => {
        this.tbLoading = false
      })
    },
    handleSetting() {
      this.dialogVisible = true
      this.getSettingInfo()
    },
    async handleAddSetting(row) {
      const $table = this.$refs.xTable
      const record = {
        Display_Name: '',
        Is_Deepen_Change: false
      }
      const { row: newRow } = await $table.insertAt(record, row)
      await $table.setEditCell(newRow, 'name')
    },
    removeRowEvent(row) {
      if (!row.Id) {
        this.$refs.xTable.remove(row)
        return
      }
      this.$confirm(' 是否删除该类型?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        DeleteMocType({
          ids: row.Id
        }).then(res => {
          if (res.IsSucceed) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getSettingInfo()
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    getSettingInfo() {
      GetMocOrderTypeList({}).then(res => {
        if (res.IsSucceed) {
          this.dialogTable = res.Data
          this.mocType = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    async changeColumn() {
      await this.getTableConfig(this.gridCode)
    },
    editClosedEvent({ row, column }) {
      if (!row.Display_Name) {
        this.$message({
          message: '名称不能为空',
          type: 'warning'
        })
        return
      }
      const $table = this.$refs.xTable
      const field = column.field
      let flag = false
      if ($table.isUpdateByRow(row, field) && row.Id || !row.Id) {
        flag = true
      }
      if (flag) {
        const obj = {
          Display_Name: row.Display_Name,
          Is_Deepen_Change: row.Is_Deepen_Change
        }
        row.Id && (obj.Id = row.Id)
        SaveMocOrderType(obj).then(res => {
          if (res.IsSucceed) {
            this.$message({
              message: '保存成功',
              type: 'success'
            })
            $table.reloadRow(row, null, field)
            this.getSettingInfo()
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      }
    },

    getBasicData() {
      GetProjectPageList({ PageSize: -1 }).then((res) => {
        if (res.IsSucceed) {
          this.projectList = res.Data.Data
        }
      })
    },
    getAreaList(Sys_Project_Id) {
      GeAreaTrees({
        sysProjectId: Sys_Project_Id
      }).then(res => {
        if (res.IsSucceed) {
          const tree = res.Data
          this.setDisabledTree(tree)
          this.treeParamsArea.data = res.Data
          this.$nextTick(_ => {
            this.$refs.treeSelectArea.treeDataUpdateFun(res.Data)
          })
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    setDisabledTree(root) {
      if (!root) return
      root.forEach((element) => {
        const { Children } = element
        if (Children && Children.length) {
          element.disabled = true
        } else {
          element.disabled = false
          this.setDisabledTree(Children)
        }
      })
    },
    /*    getInstallUnitPageList() {
      GetInstallUnitPageList({
        Area_Id: this.form.Area_Id,
        Page: 1,
        PageSize: -1
      }).then((res) => {
        if (res.IsSucceed) {
          if (res.IsSucceed) {
            this.installUnitList = res.Data.Data
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },*/
    projectChange(e) {
      const Sys_Project_Id = e
      this.form.Area_Id = ''
      this.treeParamsArea.data = []
      this.$nextTick(_ => {
        this.$refs.treeSelectArea.treeDataUpdateFun([])
      })
      if (e) {
        this.getAreaList(Sys_Project_Id)
      }
    },
    areaChange() {
      // this.getInstallUnitPageList()
    },
    areaClear() {
      this.form.Area_Id = ''
    },

    handleAdd(tab, event) {
      this.$router.push({ name: 'PROEngineeringChangeOrderAdd', query: { pg_redirect: this.$route.name }})
    },
    handleSearch(tab, event) {
      console.log(tab, event)
    },
    multiSelectedChange(array) {
      this.multipleSelection = array.records
    },
    handleReset() {
      this.$refs['form'].resetFields()
      this.search(1)
    },
    getButtonsByStatus(status, row) {
      // +-1：草稿，2：审批中，3：已通过完成，-2：审核未通过
      switch (status) {
        case -1:
        case 1:
        case -2:
          return this.buttonConfigs.draft
        case 2:
          return this.buttonConfigs.reviewing
        case 3:
          if (row.Exec_Status === 2) {
            return [...this.buttonConfigs.approved, ...this.buttonConfigs.finish]
          }
          return this.buttonConfigs.approved
        default:
          return []
      }
    },
    handleSubmitAudit(row) {
      console.log('提交审核', row)
      this.$confirm('是否提交审核?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tbLoading = true
        SubmitMocOrder({
          Id: row.Id
        }).then(res => {
          if (res.IsSucceed) {
            this.$message({
              type: 'success',
              message: '提交成功!'
            })
            this.fetchData(1)
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
          this.tbLoading = false
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    handleEdit(row) {
      console.log('编辑', row)
      this.$router.push({ name: 'PROEngineeringChangeOrderEdit', query: { pg_redirect: this.$route.name, type: 1, id: row.Id }})
    },
    handleDelete(row) {
      this.$confirm('是否删除该数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tbLoading = true
        DeleteMocOrder({
          Id: row.Id
        }).then(res => {
          if (res.IsSucceed) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.fetchData(1)
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
          this.tbLoading = false
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    handleView(row) {
      console.log('查看', row)
      this.$router.push({ name: 'PROEngineeringChangeOrderView', query: { pg_redirect: this.$route.name, id: row.Id, type: 2 }})
    },
    handleMonitor(row) {
      console.log('监控', row)
      this.$refs['monitor'].opendialog(row.Instance_Id, false)
    },
    handleRecycle(row) {
      console.log('回收', row)
      this.$confirm('是否回收?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tbLoading = true
        CancelFlow({
          instanceId: row.Instance_Id
        }).then(res => {
          if (res.IsSucceed) {
            this.$message({
              message: '回收成功',
              type: 'success'
            })
            this.fetchData()
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
          this.tbLoading = false
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    editDisabledEvent({ row, column }) {
      // const $table = this.$refs.xTable
      // $table.modal.message({ content: '禁止编辑', status: 'error' })
    },
    activeRowMethod({ row, rowIndex }) {
      return !row.Id
    },
    handleComplete(row) {
      console.log('完成', row)
      this.$confirm('是否完成?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tbLoading = true
        ChangeMocOrderStatus({
          Id: row.Id
        }).then(res => {
          if (res.IsSucceed) {
            this.$message({
              type: 'success',
              message: '操作成功!'
            })
            this.fetchData(1)
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
          this.tbLoading = false
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    checkKey(key, row) {
      if (!key) return true
      if (key === 'monitor') {
        return !!row['Instance_Id']
      }
      return true
    }
  }
}
</script>

<style scoped lang="scss">
.app-container{
  display: flex;
  flex-direction: column;
  .cs-main{
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
    .cs-bottom-wapper{
      display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;
      .tb-x{
        flex: 1;
        overflow: hidden;
      }
      .data-info{
        //display: flex;
        //justify-content: space-between;
        //align-items: center;
        text-align: right;
        margin-top: 10px;
      }
      .pagination-container {
        padding: 0;
        padding-bottom: 8px;
        text-align: right;
        margin-top: 0;
      }
    }
  }
}
.cs-box{
  background-color: #FFFFFF;
  padding: 16px;
  border-radius: 4px;
}
.mb10{
  margin-bottom: 10px;
}

.cs-tree-x {
  ::v-deep {
    .el-select {
      width: 100%;
    }
  }
}
.cs-tags{
  padding: 2px 4px;
  border-radius: 4px;
}

.cs-red{
  color: #FB6B7F;
  background-color: rgba(251, 107, 127, .1);
}
.cs-blue{
  color: #3ECC93;
  background-color:rgba(62, 204, 147, .1);
}
.cs-green{
  color: #52C41A;
  background-color: rgba(82,196,26, .1);
}
.cs-zbtn{
  pointer-events: none;
  z-index: 1;
}
.cs-toolBar {
  ::v-deep {
    .vxe-button--icon.vxe-icon-custom-column{
      display: none;
    }

    .vxe-button.type--button.is--circle {
      width: 97px;
      z-index: 0;
      border-radius: 4px;
    }

    .el-form-item {
      margin-bottom: 0;
    }
  }
}
.cs-dialog{
  ::v-deep{
    .el-dialog__body{
      max-height: 70vh;
      overflow: auto;
    }
  }
}

</style>

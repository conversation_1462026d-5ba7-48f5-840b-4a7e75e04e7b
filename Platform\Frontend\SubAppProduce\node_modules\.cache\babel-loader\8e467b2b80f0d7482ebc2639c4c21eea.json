{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\Add.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\Add.vue", "mtime": 1758001438856}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetBOMInfo", "GetUserList", "GetFactoryPeoplelist", "GetCheckGroupList", "GetWorkingTeams", "SaveProjectProcess", "mapGetters", "GetDictionaryDetailListByCode", "props", "type", "String", "default", "rowInfo", "Object", "totalWorkloadProportion", "Number", "sysProjectId", "data", "checkList", "btnLoading", "hiddenPart", "form", "Code", "Name", "Bom_Level", "Month_Avg_Load", "Coordinate_UserId", "Sort", "undefined", "Is_Enable", "Is_External", "Is_Nest", "Is_Need_Check", "Is_Self_Check", "Is_Inter_Check", "Is_Pick_Material", "Is_Need_TC", "Is_Welding_Assembling", "Is_Cutting", "TC_Check_UserId", "Is_Need_ZL", "ZL_Check_UserId", "Show_Model", "Check_Style", "Working_Team_Ids", "Remark", "Workload_Proportion", "ZL_Check_UserIds", "TC_Check_UserIds", "CheckChange", "userOptions", "optionsUserList", "optionsGroupList", "optionsWorkingTeamsList", "rules", "required", "message", "trigger", "max", "bomList", "comName", "partName", "computed", "_objectSpread", "allocableWorkloadProportion", "created", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "_yield$GetBOMInfo", "list", "wrap", "_callee$", "_context", "prev", "next", "sent", "stop", "mounted", "getUserList", "getFactoryPeoplelist", "getWorkingTeamsList", "initForm", "methods", "_this$rowInfo", "others", "_objectWithoutProperties", "_excluded", "assign", "console", "log", "split", "radioSelfCheck", "val", "radioInterCheck", "getDictionaryDetailListByCode", "_this2", "_callee2", "_callee2$", "_context2", "dictionaryCode", "then", "res", "IsSucceed", "deviceTypeList", "Data", "$message", "Message", "_this3", "_this4", "getCheckGroupList", "_this5", "_callee3", "_callee3$", "_context3", "_this6", "radioCheckStyleChange", "radioChange", "checkChange", "changeType", "typeChange", "Task_Model", "changeTc", "i", "length", "changeZL", "checkboxChange", "handleSubmit", "_this7", "$refs", "validate", "valid", "uItems", "find", "v", "Id", "Coordinate_UserName", "Display_Name", "error", "ZL", "TC", "Sys_Project_Id", "Process_Id", "$emit", "codeChange", "e", "replace", "handlePercentageInput", "value", "inputValue", "dotCount", "match", "firstDotIndex", "indexOf", "substring", "includes", "parts", "numValue", "parseFloat", "isNaN", "maxValue", "currentTotal", "total", "toFixed", "current", "toString", "difference", "verification"], "sources": ["src/views/PRO/project-config/process-settings/component/Add.vue"], "sourcesContent": ["<template>\n  <div class=\"form-wrapper\">\n    <div class=\"form-x\">\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\" style=\"width: 100%\">\n        <el-divider content-position=\"left\">基础信息</el-divider>\n        <el-form-item label=\"名称\" prop=\"Name\">\n          <el-input v-model=\"form.Name\" :maxlength=\"30\" placeholder=\"最多30个字\" show-word-limit :disabled=\"true\" />\n        </el-form-item>\n        <el-form-item label=\"代号\" prop=\"Code\">\n          <el-input\n            v-model=\"form.Code\"\n            :maxlength=\"30\"\n            placeholder=\"字母+数字，30字符\"\n            show-word-limit\n            :disabled=\"true\"\n            @input=\"(e) => (form.Code = codeChange(e))\"\n          />\n        </el-form-item>\n        <el-form-item label=\"类型\" prop=\"Bom_Level\">\n          <el-radio-group\n            v-for=\"(item, index) in bomList\"\n            :key=\"index\"\n            v-model=\"form.Bom_Level\"\n            class=\"radio\"\n            :disabled=\"true\"\n            @change=\"changeType\"\n          >\n            <el-radio style=\"margin-right: 8px;\" :label=\"item.Code\">{{ item.Display_Name }}</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"Sort\">\n          <el-input-number\n            v-model=\"form.Sort\"\n            :min=\"0\"\n            step-strictly\n            :step=\"1\"\n            class=\"cs-number-btn-hidden w100\"\n            placeholder=\"请输入\"\n            clearable=\"\"\n            :disabled=\"true\"\n          />\n        </el-form-item>\n        <el-form-item label=\"协调人\" prop=\"Coordinate_UserId\">\n          <el-select v-model=\"form.Coordinate_UserId\" class=\"w100\" clearable filterable placeholder=\"请选择\" :disabled=\"true\">\n            <el-option v-for=\"item in optionsUserList\" :key=\"item.Id\" :label=\"item.Display_Name\" :value=\"item.Id\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"工序月均负荷\" prop=\"Month_Avg_Load\">\n          <el-input v-model=\"form.Month_Avg_Load\" placeholder=\"请输入\" :disabled=\"true\">\n            <template slot=\"append\">吨</template>\n          </el-input>\n        </el-form-item>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否启用\" prop=\"Is_Enable\">\n              <el-radio-group v-model=\"form.Is_Enable\" :disabled=\"true\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否外协\" prop=\"Is_External\">\n              <el-radio-group v-model=\"form.Is_External\" :disabled=\"true\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否装焊工序\" prop=\"Is_Welding_Assembling\">\n              <el-radio-group v-model=\"form.Is_Welding_Assembling\" :disabled=\"true\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否下料工序\" prop=\"Is_Cutting\" :disabled=\"true\">\n              <el-radio-group v-model=\"form.Is_Cutting\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否套料工序\" prop=\"Is_Nest\" :disabled=\"true\">\n              <el-radio-group v-model=\"form.Is_Nest\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否领料工序\" prop=\"Is_Pick_Material\" :disabled=\"true\">\n              <el-radio-group v-model=\"form.Is_Pick_Material\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"加工班组\" prop=\"Working_Team_Ids\">\n          <el-select v-model=\"form.Working_Team_Ids\" multiple style=\"width: 100%\" placeholder=\"请选择加工班组\" :disabled=\"true\">\n            <el-option v-for=\"item in optionsWorkingTeamsList\" :key=\"item.Id\" :label=\"item.Name\" :value=\"item.Id\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"备注\">\n          <el-input v-model=\"form.Remark\" type=\"textarea\" :disabled=\"true\" />\n        </el-form-item>\n        <el-divider content-position=\"left\">质检信息</el-divider>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否自检\" prop=\"Is_Self_Check\">\n              <el-radio-group v-model=\"form.Is_Self_Check\" @change=\"radioSelfCheck\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否互检\" prop=\"Is_Inter_Check\">\n              <el-radio-group v-model=\"form.Is_Inter_Check\" @change=\"radioInterCheck\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否专检\" prop=\"Is_Need_Check\">\n              <el-radio-group v-model=\"form.Is_Need_Check\" @change=\"radioChange\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <template v-if=\"form.Is_Need_Check\">\n              <el-form-item label=\"专检方式\" prop=\"Check_Style\">\n                <el-radio-group v-model=\"form.Check_Style\" @change=\"radioCheckStyleChange\">\n                  <el-radio label=\"0\">抽检</el-radio>\n                  <el-radio label=\"1\">全检</el-radio>\n                </el-radio-group>\n              </el-form-item>\n            </template>\n          </el-col>\n        </el-row>\n\n        <template v-if=\"form.Is_Need_Check\">\n          <el-form-item label=\"专检类型\" prop=\"\">\n            <div>\n              <div style=\"margin-bottom: 10px;\">\n                <el-checkbox v-model=\"form.Is_Need_TC\" @change=\"checkboxChange($event, 1)\">\n                  <span> 探伤</span>\n                </el-checkbox>\n                <span style=\"margin-left: 30px; \">\n                  <span style=\"color: rgba(34, 40, 52, 0.65)\">探伤员：</span>\n                  <el-select\n                    v-model=\"TC_Check_UserIds\"\n                    filterable\n                    clearable\n                    :disabled=\"!form.Is_Need_TC\"\n                    multiple\n                    placeholder=\"请选择探伤员\"\n                    @change=\"changeTc\"\n                  >\n                    <el-option\n                      v-for=\"item in optionsUserList\"\n                      :key=\"item.Id\"\n                      :label=\"item.Display_Name\"\n                      :value=\"item.Id\"\n                    />\n                  </el-select>\n                </span>\n              </div>\n              <div>\n                <el-checkbox v-model=\"form.Is_Need_ZL\" @change=\"checkboxChange($event, 2)\">\n                  <span> 质量</span>\n                </el-checkbox>\n                <span style=\"margin-left: 30px\">\n                  <span style=\"color: rgba(34, 40, 52, 0.65)\">质检员：</span>\n                  <el-select\n                    v-model=\"ZL_Check_UserIds\"\n                    :disabled=\"!form.Is_Need_ZL\"\n                    filterable\n                    clearable\n                    multiple\n                    placeholder=\"请选择质检员\"\n                    @change=\"changeZL\"\n                  >\n                    <el-option\n                      v-for=\"item in optionsUserList\"\n                      :key=\"item.Id\"\n                      :label=\"item.Display_Name\"\n                      :value=\"item.Id\"\n                    />\n                  </el-select>\n                </span>\n              </div>\n            </div>\n          </el-form-item>\n        </template>\n        <el-divider content-position=\"left\">其他信息</el-divider>\n        <el-form-item label=\"工作量占比\" prop=\"Workload_Proportion\">\n          <el-input v-model=\"form.Workload_Proportion\" placeholder=\"请输入\" @input=\"handlePercentageInput\">\n            <template slot=\"append\">%</template>\n          </el-input>\n        </el-form-item>\n        <el-form-item label=\"是否展示模型\" prop=\"Show_Model\">\n          <el-radio-group v-model=\"form.Show_Model\" :disabled=\"true\">\n            <el-radio :label=\"true\">是</el-radio>\n            <el-radio :label=\"false\">否</el-radio>\n          </el-radio-group>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"btn-x\">\n      <el-button @click=\"$emit('close')\">取 消</el-button>\n      <el-button :loading=\"btnLoading\" type=\"primary\" @click=\"handleSubmit\">确 定\n      </el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport { GetUserList } from '@/api/sys'\nimport {\n  GetFactoryPeoplelist,\n  GetCheckGroupList,\n  GetWorkingTeams,\n  SaveProjectProcess\n} from '@/api/PRO/technology-lib'\nimport { mapGetters } from 'vuex'\nimport { GetDictionaryDetailListByCode } from '@/api/sys'\nexport default {\n  props: {\n    type: {\n      type: String,\n      default: ''\n    },\n    rowInfo: {\n      type: Object,\n      default() {\n        return {}\n      }\n    },\n    totalWorkloadProportion: {\n      type: Number,\n      default: 0\n    },\n    sysProjectId: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      checkList: [],\n      btnLoading: false,\n      hiddenPart: false,\n\n      form: {\n        Code: '',\n        Name: '',\n        Bom_Level: '',\n        Month_Avg_Load: '',\n        Coordinate_UserId: '',\n        Sort: undefined,\n        Is_Enable: true,\n        Is_External: false,\n        Is_Nest: false,\n        Is_Need_Check: true,\n        Is_Self_Check: true,\n        Is_Inter_Check: true,\n        Is_Pick_Material: false,\n        Is_Need_TC: true,\n        Is_Welding_Assembling: false,\n        Is_Cutting: false,\n        TC_Check_UserId: '',\n        Is_Need_ZL: false,\n        ZL_Check_UserId: '',\n        Show_Model: false,\n\n        Check_Style: '0',\n\n        Working_Team_Ids: [],\n        Remark: '',\n        Workload_Proportion: ''\n      },\n      ZL_Check_UserIds: [],\n      TC_Check_UserIds: [],\n      CheckChange: true,\n      userOptions: [],\n      optionsUserList: [],\n      optionsGroupList: [],\n      optionsWorkingTeamsList: [],\n      rules: {\n        Code: [\n          { required: true, message: '请输入代号', trigger: 'blur' },\n          { max: 30, message: '长度在 30 个字符内', trigger: 'blur' }\n        ],\n        Name: [\n          { required: true, message: '请输入名称', trigger: 'blur' },\n          { max: 30, message: '长度在 30 个字符内', trigger: 'blur' }\n        ],\n        Bom_Level: [{ required: true, message: '请选择类型', trigger: 'change' }],\n        Sort: [{ required: true, message: '请输入', trigger: 'blur' }],\n        Is_Need_Check: [\n          { required: true, message: '请选择是否质检', trigger: 'change' }\n        ]\n      },\n      Workload_Proportion: 0,\n      bomList: [],\n      comName: '',\n      partName: ''\n    }\n  },\n  computed: {\n    ...mapGetters('tenant', ['isVersionFour']),\n    allocableWorkloadProportion() {\n      return this.totalWorkloadProportion - this.Workload_Proportion\n    }\n  },\n  async created() {\n    const { comName, partName, list } = await GetBOMInfo()\n    this.comName = comName\n    this.partName = partName\n    this.bomList = list\n  },\n  mounted() {\n    this.getUserList()\n    this.getFactoryPeoplelist()\n    // this.getCheckGroupList();\n    this.getWorkingTeamsList()\n    this.type === 'edit' && this.initForm()\n  },\n  methods: {\n    initForm() {\n      const { Is_Nest, ...others } = this.rowInfo\n      this.form = Object.assign({}, others, { Is_Nest: !!Is_Nest })\n      this.form.Bom_Level = String(this.form.Bom_Level)\n      this.Workload_Proportion = this.rowInfo.Workload_Proportion\n      //  if(this.form.Type==2){\n      //   this.form.Types = '0'\n      //  }else if(this.form.Type==3){\n      //   let Types = this.radioList.find(v => ['1', '2','3'].includes(v.Code))?.Code\n      //   console.log('Types', Types)\n      //   console.log('this.radioList', this.radioList)\n      //   this.form.Types = Types\n      //  }else if(this.form.Type==1){\n      //   this.form.Types = '-1'\n      //  }\n      console.log('this.form', this.form)\n\n      // 处理历史数据多选问题\n      // if (this.form.Is_Need_Check) {\n      //   if (this.form.Check_Style === '1') {\n\n      //   } else {\n      //     this.CheckChange = !!this.form.Is_Need_TC\n      //     if (this.form.Is_Need_ZL && this.form.Is_Need_TC) {\n      //       this.form.Is_Need_TC = true\n      //       this.form.Is_Need_ZL = false\n      //     }\n      //   }\n      // }\n      this.ZL_Check_UserIds = this.form.ZL_Check_UserId\n        ? this.form.ZL_Check_UserId.split(',')\n        : []\n      this.TC_Check_UserIds = this.form.TC_Check_UserId\n        ? this.form.TC_Check_UserId.split(',')\n        : []\n    },\n    // 是否自检\n    radioSelfCheck(val) { },\n    // 是否互检\n    radioInterCheck(val) { },\n    // 获取设备类型\n    async getDictionaryDetailListByCode() {\n      await GetDictionaryDetailListByCode({ dictionaryCode: 'deviceType' }).then((res) => {\n        if (res.IsSucceed) {\n          this.deviceTypeList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n      console.log(' this.optionsGroupList', this.optionsGroupList)\n    },\n    getUserList() {\n      GetUserList({}).then((res) => {\n        if (res.IsSucceed) {\n          this.userOptions = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    getFactoryPeoplelist() {\n      GetFactoryPeoplelist({}).then((res) => {\n        if (res.IsSucceed) {\n          this.optionsUserList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    async getCheckGroupList() {\n      await GetCheckGroupList({}).then((res) => {\n        if (res.IsSucceed) {\n          this.optionsGroupList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n      console.log(' this.optionsGroupList', this.optionsGroupList)\n    },\n    getWorkingTeamsList() {\n      GetWorkingTeams({}).then((res) => {\n        if (res.IsSucceed) {\n          this.optionsWorkingTeamsList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    // 选择专检方式 抽检、全检\n    radioCheckStyleChange(val) {\n      // if (val === '0') {\n      //   this.form.Is_Need_TC = true\n      //   this.form.Is_Need_ZL = false\n      // }\n      this.ZL_Check_UserIds = []\n      this.TC_Check_UserIds = []\n      this.form.ZL_Check_UserId = ''\n      this.form.TC_Check_UserId = ''\n    },\n    // 是否专检\n    radioChange(val) {\n      if (val === false && this.type === 'add') {\n        this.form.checkChange = false\n        this.form.Is_Need_TC = false\n        this.form.Is_Need_ZL = false\n        this.TC_Check_UserIds = []\n        this.ZL_Check_UserIds = []\n        this.form.ZL_Check_UserId = ''\n        this.form.TC_Check_UserId = ''\n        this.form.Check_Style = ''\n      } else {\n        // this.form.checkChange = true\n        // this.form.Is_Need_TC = true\n        // this.form.Is_Need_ZL = false\n        // this.CheckChange = !!this.form.Is_Need_TC\n        this.form.Check_Style = '0'\n      }\n    },\n    // 选择BOM层级\n    changeType(val) {\n      // const Code = val\n      // console.log(Code, 'Code');\n      // if (Code === '-1') {\n      //   this.form.Type = 1\n      // } else if (Code === '0') {\n      //   this.form.Type = 2\n      // } else if (Code === '1' || Code === '3'|| Code === '2') {\n      //   this.form.Type = 3\n      // }\n      // if (this.form.Type === 1 || this.form.Type === 3) {\n      //   this.form.Is_Cutting = undefined\n      // } else if (this.form.Type === 2) {\n      //   this.form.Is_Welding_Assembling = undefined\n      // }\n    },\n    typeChange() {\n      this.form.Task_Model = ''\n    },\n    changeTc(val) {\n      console.log(val)\n      this.form.TC_Check_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i === val.length - 1) {\n          this.form.TC_Check_UserId += val[i]\n        } else {\n          this.form.TC_Check_UserId += val[i] + ','\n        }\n      }\n      console.log(this.form.TC_Check_UserId, 'this.form.TC_Check_UserId ')\n    },\n    changeZL(val) {\n      console.log(val)\n      this.form.ZL_Check_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i === val.length - 1) {\n          this.form.ZL_Check_UserId += val[i]\n        } else {\n          this.form.ZL_Check_UserId += val[i] + ','\n        }\n      }\n    },\n    checkboxChange(val, type) {\n      if (type === 1) {\n        if (!val) {\n          this.TC_Check_UserIds = []\n        }\n      }\n      if (type === 2) {\n        if (!val) {\n          this.ZL_Check_UserIds = []\n        }\n      }\n    },\n    handleSubmit() {\n      // delete this.form.Types\n      console.log(this.form, 'this.form')\n      this.$refs.form.validate((valid) => {\n        if (!valid) return\n        this.btnLoading = true\n        const uItems = this.optionsUserList.find(\n          (v) => v.Id === this.form.Coordinate_UserId\n        )\n        if (uItems) {\n          this.form.Coordinate_UserName = uItems.Display_Name\n        }\n        if (this.form.Is_Need_Check) { // 如果需要专检\n          if (this.form.Is_Need_ZL === false && this.form.Is_Need_TC === false) {\n            this.$message.error('请选择质检类型')\n            this.btnLoading = false\n            return\n          }\n        } else { // 如果不需要专检，则不需要专检方式、专检类型、专检人员  后续需要互检逻辑迭代\n          this.form.checkChange = false\n          this.form.Is_Need_TC = false\n          this.form.Is_Need_ZL = false\n          this.TC_Check_UserIds = []\n          this.ZL_Check_UserIds = []\n          this.form.ZL_Check_UserId = ''\n          this.form.TC_Check_UserId = ''\n          this.form.Check_Style = null\n        }\n        const ZL = this.form.Is_Need_ZL ? this.form.ZL_Check_UserId : ''\n        const TC = this.form.Is_Need_TC ? this.form.TC_Check_UserId : ''\n        if (this.form.Is_Need_ZL && (ZL ?? '') === '') {\n          this.$message.error('请选择质检员')\n          this.btnLoading = false\n          return\n        }\n        if (this.form.Is_Need_TC && (TC ?? '') === '') {\n          this.$message.error('请选择探伤员')\n          this.btnLoading = false\n          return\n        }\n\n        SaveProjectProcess({\n          Bom_Level: this.form.Bom_Level,\n          Workload_Proportion: this.form.Workload_Proportion,\n          Is_Self_Check: this.form.Is_Self_Check,\n          Is_Inter_Check: this.form.Is_Inter_Check,\n          Is_Need_Check: this.form.Is_Need_Check,\n          Check_Style: this.form.Check_Style,\n          Is_Need_TC: this.form.Is_Need_TC,\n          Is_Need_ZL: this.form.Is_Need_ZL,\n          Sys_Project_Id: this.sysProjectId,\n          Process_Id: this.form.Id,\n          ZL_Check_UserId: ZL,\n          TC_Check_UserId: TC\n        }).then((res) => {\n          if (res.IsSucceed) {\n            this.$message({\n              message: '保存成功',\n              type: 'success'\n            })\n            this.$emit('refresh')\n            this.$emit('close')\n          } else {\n            this.$message({\n              message: res.Message,\n              type: 'error'\n            })\n          }\n          this.btnLoading = false\n        })\n      })\n    },\n\n    codeChange(e) {\n      return e.replace(/[^a-zA-Z0-9]/g, '')\n    },\n\n    handlePercentageInput(value) {\n      // 如果输入为空，直接返回\n      if (value === '' || value === null || value === undefined) {\n        this.form.Workload_Proportion = ''\n        return\n      }\n\n      // 转换为字符串进行处理\n      let inputValue = String(value)\n\n      // 只允许数字和一个小数点，移除其他字符（包括负号）\n      inputValue = inputValue.replace(/[^0-9.]/g, '')\n\n      // 确保只有一个小数点\n      const dotCount = (inputValue.match(/\\./g) || []).length\n      if (dotCount > 1) {\n        // 如果有多个小数点，只保留第一个\n        const firstDotIndex = inputValue.indexOf('.')\n        inputValue = inputValue.substring(0, firstDotIndex + 1) + inputValue.substring(firstDotIndex + 1).replace(/\\./g, '')\n      }\n\n      // 如果只是小数点，设置为空\n      if (inputValue === '.') {\n        this.form.Workload_Proportion = ''\n        return\n      }\n\n      // 如果处理后为空字符串，设置为空\n      if (inputValue === '') {\n        this.form.Workload_Proportion = ''\n        return\n      }\n\n      // 限制小数位数最多2位\n      if (inputValue.includes('.')) {\n        const parts = inputValue.split('.')\n        if (parts[1] && parts[1].length > 2) {\n          inputValue = parts[0] + '.' + parts[1].substring(0, 2)\n        }\n      }\n\n      // 转换为数字进行范围检查\n      const numValue = parseFloat(inputValue)\n\n      // 如果不是有效数字，设置为空\n      if (isNaN(numValue)) {\n        this.form.Workload_Proportion = ''\n        return\n      }\n\n      // 根据 totalWorkloadProportion 计算最大值\n      let maxValue = 100\n      let currentTotal = 0\n\n      if (this.totalWorkloadProportion) {\n        // 确保所有数值都是浮点数并保留2位小数进行计算\n        const total = parseFloat(this.totalWorkloadProportion.toFixed(2))\n        const current = parseFloat((this.Workload_Proportion || 0).toString())\n\n        // 计算差值并保留2位小数\n        const difference = total - current\n        currentTotal = parseFloat(difference.toFixed(2))\n\n        // 验证数学一致性：确保 currentTotal + current = total\n        const verification = parseFloat((currentTotal + current).toFixed(2))\n        if (verification !== total) {\n          // 如果不一致，调整 currentTotal 以保证一致性\n          currentTotal = parseFloat((total - current).toFixed(2))\n        }\n      }\n\n      if (currentTotal === 100) {\n        // 如果总和已经是100，最大只能输入0\n        maxValue = 0\n      } else if (currentTotal > 0 && currentTotal < 100) {\n        // 如果总和在0-100之间，最大值是100减去当前总和\n        maxValue = 100 - currentTotal\n      }\n      // 如果总和是0或空，最大值保持100\n\n      // 限制范围在 0-maxValue 之间\n      if (numValue < 0) {\n        this.form.Workload_Proportion = '0'\n      } else if (numValue > maxValue) {\n        // 保留2位小数\n        this.form.Workload_Proportion = maxValue.toFixed(2).replace(/\\.?0+$/, '')\n      } else {\n        // 保持原始输入格式（包括小数点），但确保不超过2位小数\n        this.form.Workload_Proportion = inputValue\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/mixin.scss\";\n\n.btn-del {\n  margin-left: -100px;\n}\n\n.customRadioClass {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.checkboxFlex {\n  display: flex;\n  align-items: center;\n}\n\n.form-wrapper {\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  min-height: 40vh;\n\n  .form-x {\n    max-height: 70vh;\n    overflow: auto;\n    padding-right: 16px;\n    @include scrollBar;\n  }\n\n  .btn-x {\n    padding-top: 16px;\n    text-align: right;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuOA,SAAAA,UAAA;AACA,SAAAC,WAAA;AACA,SACAC,oBAAA,EACAC,iBAAA,EACAC,eAAA,EACAC,kBAAA,QACA;AACA,SAAAC,UAAA;AACA,SAAAC,6BAAA;AACA;EACAC,KAAA;IACAC,IAAA;MACAA,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,OAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA,WAAAA,SAAA;QACA;MACA;IACA;IACAG,uBAAA;MACAL,IAAA,EAAAM,MAAA;MACAJ,OAAA;IACA;IACAK,YAAA;MACAP,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,UAAA;MACAC,UAAA;MAEAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,SAAA;QACAC,cAAA;QACAC,iBAAA;QACAC,IAAA,EAAAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,OAAA;QACAC,aAAA;QACAC,aAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,UAAA;QACAC,qBAAA;QACAC,UAAA;QACAC,eAAA;QACAC,UAAA;QACAC,eAAA;QACAC,UAAA;QAEAC,WAAA;QAEAC,gBAAA;QACAC,MAAA;QACAC,mBAAA;MACA;MACAC,gBAAA;MACAC,gBAAA;MACAC,WAAA;MACAC,WAAA;MACAC,eAAA;MACAC,gBAAA;MACAC,uBAAA;MACAC,KAAA;QACAhC,IAAA,GACA;UAAAiC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAlC,IAAA,GACA;UAAAgC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAjC,SAAA;UAAA+B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACA9B,IAAA;UAAA4B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAzB,aAAA,GACA;UAAAuB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAX,mBAAA;MACAa,OAAA;MACAC,OAAA;MACAC,QAAA;IACA;EACA;EACAC,QAAA,EAAAC,aAAA,CAAAA,aAAA,KACAzD,UAAA;IACA0D,2BAAA,WAAAA,4BAAA;MACA,YAAAlD,uBAAA,QAAAgC,mBAAA;IACA;EAAA,EACA;EACAmB,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAAC,iBAAA,EAAAX,OAAA,EAAAC,QAAA,EAAAW,IAAA;MAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACA7E,UAAA;UAAA;YAAAuE,iBAAA,GAAAI,QAAA,CAAAG,IAAA;YAAAlB,OAAA,GAAAW,iBAAA,CAAAX,OAAA;YAAAC,QAAA,GAAAU,iBAAA,CAAAV,QAAA;YAAAW,IAAA,GAAAD,iBAAA,CAAAC,IAAA;YACAN,KAAA,CAAAN,OAAA,GAAAA,OAAA;YACAM,KAAA,CAAAL,QAAA,GAAAA,QAAA;YACAK,KAAA,CAAAP,OAAA,GAAAa,IAAA;UAAA;UAAA;YAAA,OAAAG,QAAA,CAAAI,IAAA;QAAA;MAAA,GAAAT,OAAA;IAAA;EACA;EACAU,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA;IACA,KAAAC,oBAAA;IACA;IACA,KAAAC,mBAAA;IACA,KAAA1E,IAAA,oBAAA2E,QAAA;EACA;EACAC,OAAA;IACAD,QAAA,WAAAA,SAAA;MACA,IAAAE,aAAA,QAAA1E,OAAA;QAAAmB,OAAA,GAAAuD,aAAA,CAAAvD,OAAA;QAAAwD,MAAA,GAAAC,wBAAA,CAAAF,aAAA,EAAAG,SAAA;MACA,KAAApE,IAAA,GAAAR,MAAA,CAAA6E,MAAA,KAAAH,MAAA;QAAAxD,OAAA,IAAAA;MAAA;MACA,KAAAV,IAAA,CAAAG,SAAA,GAAAd,MAAA,MAAAW,IAAA,CAAAG,SAAA;MACA,KAAAsB,mBAAA,QAAAlC,OAAA,CAAAkC,mBAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA6C,OAAA,CAAAC,GAAA,mBAAAvE,IAAA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,KAAA0B,gBAAA,QAAA1B,IAAA,CAAAoB,eAAA,GACA,KAAApB,IAAA,CAAAoB,eAAA,CAAAoD,KAAA,QACA;MACA,KAAA7C,gBAAA,QAAA3B,IAAA,CAAAkB,eAAA,GACA,KAAAlB,IAAA,CAAAkB,eAAA,CAAAsD,KAAA,QACA;IACA;IACA;IACAC,cAAA,WAAAA,eAAAC,GAAA;IACA;IACAC,eAAA,WAAAA,gBAAAD,GAAA;IACA;IACAE,6BAAA,WAAAA,8BAAA;MAAA,IAAAC,MAAA;MAAA,OAAA/B,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA8B,SAAA;QAAA,OAAA/B,mBAAA,GAAAK,IAAA,UAAA2B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzB,IAAA,GAAAyB,SAAA,CAAAxB,IAAA;YAAA;cAAAwB,SAAA,CAAAxB,IAAA;cAAA,OACAtE,6BAAA;gBAAA+F,cAAA;cAAA,GAAAC,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAP,MAAA,CAAAQ,cAAA,GAAAF,GAAA,CAAAG,IAAA;gBACA;kBACAT,MAAA,CAAAU,QAAA;oBACApD,OAAA,EAAAgD,GAAA,CAAAK,OAAA;oBACApG,IAAA;kBACA;gBACA;cACA;YAAA;cACAkF,OAAA,CAAAC,GAAA,2BAAAM,MAAA,CAAA9C,gBAAA;YAAA;YAAA;cAAA,OAAAiD,SAAA,CAAAtB,IAAA;UAAA;QAAA,GAAAoB,QAAA;MAAA;IACA;IACAlB,WAAA,WAAAA,YAAA;MAAA,IAAA6B,MAAA;MACA7G,WAAA,KAAAsG,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAK,MAAA,CAAA5D,WAAA,GAAAsD,GAAA,CAAAG,IAAA;QACA;UACAG,MAAA,CAAAF,QAAA;YACApD,OAAA,EAAAgD,GAAA,CAAAK,OAAA;YACApG,IAAA;UACA;QACA;MACA;IACA;IACAyE,oBAAA,WAAAA,qBAAA;MAAA,IAAA6B,MAAA;MACA7G,oBAAA,KAAAqG,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAM,MAAA,CAAA5D,eAAA,GAAAqD,GAAA,CAAAG,IAAA;QACA;UACAI,MAAA,CAAAH,QAAA;YACApD,OAAA,EAAAgD,GAAA,CAAAK,OAAA;YACApG,IAAA;UACA;QACA;MACA;IACA;IACAuG,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MAAA,OAAA9C,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6C,SAAA;QAAA,OAAA9C,mBAAA,GAAAK,IAAA,UAAA0C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxC,IAAA,GAAAwC,SAAA,CAAAvC,IAAA;YAAA;cAAAuC,SAAA,CAAAvC,IAAA;cAAA,OACA1E,iBAAA,KAAAoG,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAQ,MAAA,CAAA7D,gBAAA,GAAAoD,GAAA,CAAAG,IAAA;gBACA;kBACAM,MAAA,CAAAL,QAAA;oBACApD,OAAA,EAAAgD,GAAA,CAAAK,OAAA;oBACApG,IAAA;kBACA;gBACA;cACA;YAAA;cACAkF,OAAA,CAAAC,GAAA,2BAAAqB,MAAA,CAAA7D,gBAAA;YAAA;YAAA;cAAA,OAAAgE,SAAA,CAAArC,IAAA;UAAA;QAAA,GAAAmC,QAAA;MAAA;IACA;IACA/B,mBAAA,WAAAA,oBAAA;MAAA,IAAAkC,MAAA;MACAjH,eAAA,KAAAmG,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAY,MAAA,CAAAhE,uBAAA,GAAAmD,GAAA,CAAAG,IAAA;QACA;UACAU,MAAA,CAAAT,QAAA;YACApD,OAAA,EAAAgD,GAAA,CAAAK,OAAA;YACApG,IAAA;UACA;QACA;MACA;IACA;IACA;IACA6G,qBAAA,WAAAA,sBAAAvB,GAAA;MACA;MACA;MACA;MACA;MACA,KAAAhD,gBAAA;MACA,KAAAC,gBAAA;MACA,KAAA3B,IAAA,CAAAoB,eAAA;MACA,KAAApB,IAAA,CAAAkB,eAAA;IACA;IACA;IACAgF,WAAA,WAAAA,YAAAxB,GAAA;MACA,IAAAA,GAAA,mBAAAtF,IAAA;QACA,KAAAY,IAAA,CAAAmG,WAAA;QACA,KAAAnG,IAAA,CAAAe,UAAA;QACA,KAAAf,IAAA,CAAAmB,UAAA;QACA,KAAAQ,gBAAA;QACA,KAAAD,gBAAA;QACA,KAAA1B,IAAA,CAAAoB,eAAA;QACA,KAAApB,IAAA,CAAAkB,eAAA;QACA,KAAAlB,IAAA,CAAAsB,WAAA;MACA;QACA;QACA;QACA;QACA;QACA,KAAAtB,IAAA,CAAAsB,WAAA;MACA;IACA;IACA;IACA8E,UAAA,WAAAA,WAAA1B,GAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA,CACA;IACA2B,UAAA,WAAAA,WAAA;MACA,KAAArG,IAAA,CAAAsG,UAAA;IACA;IACAC,QAAA,WAAAA,SAAA7B,GAAA;MACAJ,OAAA,CAAAC,GAAA,CAAAG,GAAA;MACA,KAAA1E,IAAA,CAAAkB,eAAA;MACA,SAAAsF,CAAA,MAAAA,CAAA,GAAA9B,GAAA,CAAA+B,MAAA,EAAAD,CAAA;QACA,IAAAA,CAAA,KAAA9B,GAAA,CAAA+B,MAAA;UACA,KAAAzG,IAAA,CAAAkB,eAAA,IAAAwD,GAAA,CAAA8B,CAAA;QACA;UACA,KAAAxG,IAAA,CAAAkB,eAAA,IAAAwD,GAAA,CAAA8B,CAAA;QACA;MACA;MACAlC,OAAA,CAAAC,GAAA,MAAAvE,IAAA,CAAAkB,eAAA;IACA;IACAwF,QAAA,WAAAA,SAAAhC,GAAA;MACAJ,OAAA,CAAAC,GAAA,CAAAG,GAAA;MACA,KAAA1E,IAAA,CAAAoB,eAAA;MACA,SAAAoF,CAAA,MAAAA,CAAA,GAAA9B,GAAA,CAAA+B,MAAA,EAAAD,CAAA;QACA,IAAAA,CAAA,KAAA9B,GAAA,CAAA+B,MAAA;UACA,KAAAzG,IAAA,CAAAoB,eAAA,IAAAsD,GAAA,CAAA8B,CAAA;QACA;UACA,KAAAxG,IAAA,CAAAoB,eAAA,IAAAsD,GAAA,CAAA8B,CAAA;QACA;MACA;IACA;IACAG,cAAA,WAAAA,eAAAjC,GAAA,EAAAtF,IAAA;MACA,IAAAA,IAAA;QACA,KAAAsF,GAAA;UACA,KAAA/C,gBAAA;QACA;MACA;MACA,IAAAvC,IAAA;QACA,KAAAsF,GAAA;UACA,KAAAhD,gBAAA;QACA;MACA;IACA;IACAkF,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA;MACAvC,OAAA,CAAAC,GAAA,MAAAvE,IAAA;MACA,KAAA8G,KAAA,CAAA9G,IAAA,CAAA+G,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;QACAH,MAAA,CAAA/G,UAAA;QACA,IAAAmH,MAAA,GAAAJ,MAAA,CAAA/E,eAAA,CAAAoF,IAAA,CACA,UAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,EAAA,KAAAP,MAAA,CAAA7G,IAAA,CAAAK,iBAAA;QAAA,CACA;QACA,IAAA4G,MAAA;UACAJ,MAAA,CAAA7G,IAAA,CAAAqH,mBAAA,GAAAJ,MAAA,CAAAK,YAAA;QACA;QACA,IAAAT,MAAA,CAAA7G,IAAA,CAAAW,aAAA;UAAA;UACA,IAAAkG,MAAA,CAAA7G,IAAA,CAAAmB,UAAA,cAAA0F,MAAA,CAAA7G,IAAA,CAAAe,UAAA;YACA8F,MAAA,CAAAtB,QAAA,CAAAgC,KAAA;YACAV,MAAA,CAAA/G,UAAA;YACA;UACA;QACA;UAAA;UACA+G,MAAA,CAAA7G,IAAA,CAAAmG,WAAA;UACAU,MAAA,CAAA7G,IAAA,CAAAe,UAAA;UACA8F,MAAA,CAAA7G,IAAA,CAAAmB,UAAA;UACA0F,MAAA,CAAAlF,gBAAA;UACAkF,MAAA,CAAAnF,gBAAA;UACAmF,MAAA,CAAA7G,IAAA,CAAAoB,eAAA;UACAyF,MAAA,CAAA7G,IAAA,CAAAkB,eAAA;UACA2F,MAAA,CAAA7G,IAAA,CAAAsB,WAAA;QACA;QACA,IAAAkG,EAAA,GAAAX,MAAA,CAAA7G,IAAA,CAAAmB,UAAA,GAAA0F,MAAA,CAAA7G,IAAA,CAAAoB,eAAA;QACA,IAAAqG,EAAA,GAAAZ,MAAA,CAAA7G,IAAA,CAAAe,UAAA,GAAA8F,MAAA,CAAA7G,IAAA,CAAAkB,eAAA;QACA,IAAA2F,MAAA,CAAA7G,IAAA,CAAAmB,UAAA,KAAAqG,EAAA,aAAAA,EAAA,cAAAA,EAAA;UACAX,MAAA,CAAAtB,QAAA,CAAAgC,KAAA;UACAV,MAAA,CAAA/G,UAAA;UACA;QACA;QACA,IAAA+G,MAAA,CAAA7G,IAAA,CAAAe,UAAA,KAAA0G,EAAA,aAAAA,EAAA,cAAAA,EAAA;UACAZ,MAAA,CAAAtB,QAAA,CAAAgC,KAAA;UACAV,MAAA,CAAA/G,UAAA;UACA;QACA;QAEAd,kBAAA;UACAmB,SAAA,EAAA0G,MAAA,CAAA7G,IAAA,CAAAG,SAAA;UACAsB,mBAAA,EAAAoF,MAAA,CAAA7G,IAAA,CAAAyB,mBAAA;UACAb,aAAA,EAAAiG,MAAA,CAAA7G,IAAA,CAAAY,aAAA;UACAC,cAAA,EAAAgG,MAAA,CAAA7G,IAAA,CAAAa,cAAA;UACAF,aAAA,EAAAkG,MAAA,CAAA7G,IAAA,CAAAW,aAAA;UACAW,WAAA,EAAAuF,MAAA,CAAA7G,IAAA,CAAAsB,WAAA;UACAP,UAAA,EAAA8F,MAAA,CAAA7G,IAAA,CAAAe,UAAA;UACAI,UAAA,EAAA0F,MAAA,CAAA7G,IAAA,CAAAmB,UAAA;UACAuG,cAAA,EAAAb,MAAA,CAAAlH,YAAA;UACAgI,UAAA,EAAAd,MAAA,CAAA7G,IAAA,CAAAoH,EAAA;UACAhG,eAAA,EAAAoG,EAAA;UACAtG,eAAA,EAAAuG;QACA,GAAAvC,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAyB,MAAA,CAAAtB,QAAA;cACApD,OAAA;cACA/C,IAAA;YACA;YACAyH,MAAA,CAAAe,KAAA;YACAf,MAAA,CAAAe,KAAA;UACA;YACAf,MAAA,CAAAtB,QAAA;cACApD,OAAA,EAAAgD,GAAA,CAAAK,OAAA;cACApG,IAAA;YACA;UACA;UACAyH,MAAA,CAAA/G,UAAA;QACA;MACA;IACA;IAEA+H,UAAA,WAAAA,WAAAC,CAAA;MACA,OAAAA,CAAA,CAAAC,OAAA;IACA;IAEAC,qBAAA,WAAAA,sBAAAC,KAAA;MACA;MACA,IAAAA,KAAA,WAAAA,KAAA,aAAAA,KAAA,KAAA1H,SAAA;QACA,KAAAP,IAAA,CAAAyB,mBAAA;QACA;MACA;;MAEA;MACA,IAAAyG,UAAA,GAAA7I,MAAA,CAAA4I,KAAA;;MAEA;MACAC,UAAA,GAAAA,UAAA,CAAAH,OAAA;;MAEA;MACA,IAAAI,QAAA,IAAAD,UAAA,CAAAE,KAAA,eAAA3B,MAAA;MACA,IAAA0B,QAAA;QACA;QACA,IAAAE,aAAA,GAAAH,UAAA,CAAAI,OAAA;QACAJ,UAAA,GAAAA,UAAA,CAAAK,SAAA,IAAAF,aAAA,QAAAH,UAAA,CAAAK,SAAA,CAAAF,aAAA,MAAAN,OAAA;MACA;;MAEA;MACA,IAAAG,UAAA;QACA,KAAAlI,IAAA,CAAAyB,mBAAA;QACA;MACA;;MAEA;MACA,IAAAyG,UAAA;QACA,KAAAlI,IAAA,CAAAyB,mBAAA;QACA;MACA;;MAEA;MACA,IAAAyG,UAAA,CAAAM,QAAA;QACA,IAAAC,KAAA,GAAAP,UAAA,CAAA1D,KAAA;QACA,IAAAiE,KAAA,OAAAA,KAAA,IAAAhC,MAAA;UACAyB,UAAA,GAAAO,KAAA,YAAAA,KAAA,IAAAF,SAAA;QACA;MACA;;MAEA;MACA,IAAAG,QAAA,GAAAC,UAAA,CAAAT,UAAA;;MAEA;MACA,IAAAU,KAAA,CAAAF,QAAA;QACA,KAAA1I,IAAA,CAAAyB,mBAAA;QACA;MACA;;MAEA;MACA,IAAAoH,QAAA;MACA,IAAAC,YAAA;MAEA,SAAArJ,uBAAA;QACA;QACA,IAAAsJ,KAAA,GAAAJ,UAAA,MAAAlJ,uBAAA,CAAAuJ,OAAA;QACA,IAAAC,OAAA,GAAAN,UAAA,OAAAlH,mBAAA,OAAAyH,QAAA;;QAEA;QACA,IAAAC,UAAA,GAAAJ,KAAA,GAAAE,OAAA;QACAH,YAAA,GAAAH,UAAA,CAAAQ,UAAA,CAAAH,OAAA;;QAEA;QACA,IAAAI,YAAA,GAAAT,UAAA,EAAAG,YAAA,GAAAG,OAAA,EAAAD,OAAA;QACA,IAAAI,YAAA,KAAAL,KAAA;UACA;UACAD,YAAA,GAAAH,UAAA,EAAAI,KAAA,GAAAE,OAAA,EAAAD,OAAA;QACA;MACA;MAEA,IAAAF,YAAA;QACA;QACAD,QAAA;MACA,WAAAC,YAAA,QAAAA,YAAA;QACA;QACAD,QAAA,SAAAC,YAAA;MACA;MACA;;MAEA;MACA,IAAAJ,QAAA;QACA,KAAA1I,IAAA,CAAAyB,mBAAA;MACA,WAAAiH,QAAA,GAAAG,QAAA;QACA;QACA,KAAA7I,IAAA,CAAAyB,mBAAA,GAAAoH,QAAA,CAAAG,OAAA,IAAAjB,OAAA;MACA;QACA;QACA,KAAA/H,IAAA,CAAAyB,mBAAA,GAAAyG,UAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\sys\\professional-category\\index.vue?vue&type=style&index=0&id=66081efb&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\sys\\professional-category\\index.vue", "mtime": 1757468113629}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5wYWdlLWNvbnRhaW5lcnsNCiAgbWFyZ2luOjE2cHg7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIHBhZGRpbmc6MTZweDsNCiAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsMA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/sys/professional-category", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"page-container\">\r\n      <el-row type=\"flex\" justify=\"space-between\" style=\"margin-bottom: 16px\">\r\n        <el-col :span=\"4\">\r\n          <el-button v-if=\"false\" type=\"primary\" @click=\"addProfession\">新增专业</el-button>\r\n        </el-col>\r\n        <el-col :span=\"20\">\r\n          <el-row type=\"flex\" justify=\"end\">\r\n            <el-input v-model=\"searchValue\" placeholder=\"请输入关键字\" style=\"width:250px \" clearable />\r\n            <div>\r\n              <el-button type=\"primary\" style=\"margin-left: 16px\" @click=\"getData\">搜索</el-button>\r\n            </div>\r\n          </el-row>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <div class=\"list-wrapper\">\r\n        <el-table\r\n          ref=\"table\"\r\n          tablecode=\"plm_professionaltype_list_pro\"\r\n          :custom-param=\"{ is_System: false, typeid: '', name: searchValue, companyId: companyId }\"\r\n          @get-selection-data=\"getSelectionData\"\r\n          @getbutton=\"getClick\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <bimdialog ref=\"dialog\" @getData=\"getData\" />\r\n    <nodeList ref=\"nodeList\" @getData=\"getData\" />\r\n  </div>\r\n</template>\r\n<script>\r\nimport table from '@/views/plm/components/table'\r\nimport bimdialog from './dialog'\r\nimport nodeList from './nodeList'\r\nimport { GetProfessionalDelete } from '@/api/plm/settings'\r\nimport addRouterPage from '@/mixins/add-router-page/index'\r\nexport default {\r\n  name: 'ProfessionalCategoryList',\r\n  components: {\r\n    'el-table': table,\r\n    bimdialog,\r\n    nodeList\r\n  },\r\n  mixins: [addRouterPage],\r\n  data() {\r\n    return {\r\n      companyId: localStorage.getItem('Last_Working_Object_Id'),\r\n      searchValue: '',\r\n      addPageArray: [\r\n        {\r\n          path: 'unit-template-setting',\r\n          hidden: true,\r\n          component: () => import('@/views/sys/professional-category/unitPartTemp.vue'),\r\n          name: 'SYSUnitPartTemp',\r\n          meta: { title: '专用模板配置' }\r\n        },\r\n        {\r\n          path: 'template-setting',\r\n          hidden: true,\r\n          component: () => import('@/views/sys/professional-category/templateSetting'),\r\n          name: 'TemplateSetting',\r\n          meta: { title: '专用模板配置' }\r\n        },\r\n        {\r\n          path: 'template-setting-lj',\r\n          hidden: true,\r\n          component: () => import('@/views/sys/professional-category/templateSettingLj'),\r\n          name: 'TemplateSettingLj',\r\n          meta: { title: '零件模板配置' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/category',\r\n          hidden: true,\r\n          component: () => import('@/views/sys/professional-category/category/index.vue'),\r\n          name: 'ProfessionalCategoryListInfo',\r\n          meta: { title: '零构件类型' }\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    getSelectionData() {},\r\n    getData() {\r\n      this.$refs.table.refresh()\r\n    },\r\n    addProfession() {\r\n      this.$refs.dialog.handleOpen('add')\r\n    },\r\n    edit(item, row) {\r\n      if (row.row.is_system === true) {\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '该类别属于系统级别，不可操作'\r\n        })\r\n        return false\r\n      }\r\n      this.$refs.dialog.handleOpen('edit', row.row)\r\n    },\r\n    delete(item, row) {\r\n      if (row.row.is_system === true) {\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '该类别属于系统级别，不可操作'\r\n        })\r\n        return false\r\n      }\r\n      this.$confirm(' 确认删除?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          GetProfessionalDelete({ id: row.row.id }).then((res) => {\r\n            if (res.IsSucceed === true) {\r\n              this.$message({\r\n                type: 'success',\r\n                message: '删除成功'\r\n              })\r\n              this.getData()\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n    getClick(item, row) {\r\n      switch (item) {\r\n        case 'btnedit':\r\n          this.edit(item, row)\r\n          break\r\n        case 'btndelete':\r\n          this.delete(item, row)\r\n          break\r\n        case 'jdedit':\r\n          this.$refs.nodeList.handleOpen(true, row.row)\r\n          break\r\n        case 'unitPartCode':\r\n          this.$router.push({\r\n            name: 'SYSUnitPartTemp',\r\n            query: {\r\n              pg_redirect: this.$route.name,\r\n              name: row.row.name,\r\n              unit: row.row.unit,\r\n              steel_unit: row.row.steel_unit\r\n            }\r\n          })\r\n          break\r\n        case 'mbedit':\r\n          this.$router.push(\r\n            { name: 'TemplateSetting',\r\n              query: {\r\n                pg_redirect: this.$route.name,\r\n                typeCode: row.row.code,\r\n                materialCode: row.row.materialcode,\r\n                name: row.row.name,\r\n                unit: row.row.unit,\r\n                steel_unit: row.row.steel_unit\r\n              }\r\n            })\r\n          break\r\n        case 'ljedit':\r\n          this.$router.push(\r\n            { name: 'TemplateSettingLj',\r\n              query: {\r\n                pg_redirect: this.$route.name,\r\n                typeCode: row.row.code,\r\n                materialCode: row.row.materialcode,\r\n                name: row.row.name,\r\n                unit: row.row.unit,\r\n                steel_unit: row.row.steel_unit\r\n              }\r\n            })\r\n          break\r\n        case 'gjedit':\r\n          this.$router.push(\r\n            { name: 'ProfessionalCategoryListInfo',\r\n              query: {\r\n                pg_redirect: this.$route.name,\r\n                typeCode: row.row.code,\r\n                materialCode: row.row.materialcode,\r\n                name: row.row.name,\r\n                unit: row.row.unit,\r\n                steel_unit: row.row.steel_unit,\r\n                typeId: row.row.id\r\n              }\r\n            })\r\n          break\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.page-container{\r\n  margin:16px;\r\n  background: #fff;\r\n  padding:16px;\r\n  box-sizing: border-box;\r\n}\r\n</style>\r\n"]}]}
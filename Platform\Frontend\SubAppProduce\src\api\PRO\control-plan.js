import request from '@/utils/request'

// 获取采购计划跟踪
export function GetPurchaseControlPlan(data) {
  return request({
    url: '/PRO/ControlPlan/GetPurchaseControlPlan',
    method: 'post',
    data
  })
}

// 生产计划跟踪
export function GetProduceControlPlan(data) {
  return request({
    url: '/PRO/ControlPlan/GetProduceControlPlan',
    method: 'post',
    data
  })
}

// 深化计划跟踪
export function GetDeepenControlPlan(data) {
  return request({
    url: '/PRO/ControlPlan/GetDeepenControlPlan',
    method: 'post',
    data
  })
}

// 施工计划跟踪
export function GetConstructionControlPlan(data) {
  return request({
    url: '/PRO/ControlPlan/GetConstructionControlPlan',
    method: 'post',
    data
  })
}

// 保存采购跟踪数据
export function SavePurchaseControlPlan(data) {
  return request({
    url: '/PRO/ControlPlan/SavePurchaseControlPlan',
    method: 'post',
    data
  })
}

// 保存生产计划跟踪数据
export function SaveProduceControlPlan(data) {
  return request({
    url: '/PRO/ControlPlan/SaveProduceControlPlan',
    method: 'post',
    data
  })
}

// 保存施工计划跟踪数据
export function SaveConstructionControlPlan(data) {
  return request({
    url: '/PRO/ControlPlan/SaveConstructionControlPlan',
    method: 'post',
    data
  })
}

export function GetAreaPlanBusinessData(data) {
  return request({
    url: '/PRO/ControlPlan/GetAreaPlanBusinessData',
    method: 'post',
    data
  })
}

// 保存深化计划跟踪
export function SaveDeepenControlPlan(data) {
  return request({
    url: '/PRO/ControlPlan/SaveDeepenControlPlan',
    method: 'post',
    data
  })
}

// 获取预警配置
export function GetWarnConfigs(data) {
  return request({
    url: '/PRO/ControlPlanConfig/GetWarnConfigs',
    method: 'post',
    data
  })
}

// 保存预警配置
export function SaveWarnConfigs(data) {
  return request({
    url: '/PRO/ControlPlanConfig/SaveWarnConfigs',
    method: 'post',
    data
  })
}

// 重置预警配置
export function ResetWarnConfig(data) {
  return request({
    url: '/PRO/ControlPlanConfig/ResetWarnConfig',
    method: 'post',
    data
  })
}

// 同步至项目分区
export function SyncAreaWarnConfig(data) {
  return request({
    url: '/PRO/ControlPlanConfig/SyncAreaWarnConfig',
    method: 'post',
    data
  })
}

{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\part-list\\v4\\component\\Schduling.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\part-list\\v4\\component\\Schduling.vue", "mtime": 1757468112945}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRTY2hlZHVsaW5nUGFydExpc3QgfSBmcm9tICdAL2FwaS9QUk8vcHJvamVjdCcNCmV4cG9ydCBkZWZhdWx0IHsNCiAgcHJvcHM6IHsNCiAgICB0eXBlRW50aXR5OiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgICBkZWZhdWx0OiAoKSA9PiB7DQogICAgICAgIHJldHVybiB7fQ0KICAgICAgfQ0KDQogICAgfSwNCiAgICBsZXZlbE5hbWU6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICcnDQogICAgfQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICB0YWJsZURhdGE6IFsNCg0KICAgICAgXQ0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHt9LA0KICBtZXRob2RzOiB7DQogICAgaW5pdChyb3dEYXRhKSB7DQogICAgICBHZXRTY2hlZHVsaW5nUGFydExpc3QoeyBpZDogcm93RGF0YS5QYXJ0X0FnZ3JlZ2F0ZV9JZCB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLnRhYmxlRGF0YSA9IHJlcy5EYXRhLlNjaGVkdWxpbmdfTGlzdA0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["Schduling.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAkBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Schduling.vue", "sourceRoot": "src/views/PRO/part-list/v4/component", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-row>\r\n      <el-table stripe class=\"cs-custom-table\" :data=\"tableData\" empty-text=\"暂无数据\" border style=\"width: 100%\">\r\n        <el-table-column prop=\"Scheduling_Status\" :label=\"levelName + '状态'\" align=\"center\" />\r\n        <el-table-column prop=\"Component_Count\" label=\"数量\" align=\"center\" />\r\n      </el-table>\r\n\r\n      <el-col :span=\"24\">\r\n        <div style=\"text-align: right; margin-top: 10px\">\r\n          <el-button @click=\"$emit('close')\">关 闭</el-button>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetSchedulingPartList } from '@/api/PRO/project'\r\nexport default {\r\n  props: {\r\n    typeEntity: {\r\n      type: Object,\r\n      default: () => {\r\n        return {}\r\n      }\r\n\r\n    },\r\n    levelName: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      tableData: [\r\n\r\n      ]\r\n    }\r\n  },\r\n  mounted() {},\r\n  methods: {\r\n    init(rowData) {\r\n      GetSchedulingPartList({ id: rowData.Part_Aggregate_Id }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.tableData = res.Data.Scheduling_List\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped></style>\r\n"]}]}
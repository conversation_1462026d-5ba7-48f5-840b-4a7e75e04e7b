{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new\\components\\BatchProcessAdjust.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new\\components\\BatchProcessAdjust.vue", "mtime": 1757572678816}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["BatchProcessAdjust.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BatchProcessAdjust.vue", "sourceRoot": "src/views/PRO/plan-production/schedule-production-new/components", "sourcesContent": ["<template>\r\n  <div v-loading=\"pgLoading\" class=\"cs-container\">\r\n    <el-form ref=\"form\" :model=\"form\" label-width=\"100px\">\r\n\r\n      <el-form-item label=\"工艺代码\">\r\n        <el-select v-model=\"craftCode\" filterable placeholder=\"下拉选择支持搜索\" clearable=\"\" @change=\"craftChange\">\r\n          <el-option\r\n            v-for=\"item in gyList\"\r\n            :key=\"item.Code\"\r\n            :label=\"item.Code\"\r\n            :value=\"item.Code\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-divider />\r\n      <draggable v-model=\"list\" handle=\".icon-drag\" @change=\"changeDraggable\">\r\n        <transition-group>\r\n          <el-row v-for=\"(element,index) in list\" :key=\"element.key\">\r\n            <el-col :span=\"1\"> <i class=\"iconfont icon-drag cs-drag\" /> </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item :label=\"`排产工序${index+1}`\">\r\n                <el-select :key=\"element.key\" v-model=\"element.value\" style=\"width:90%\" :disabled=\"element.isPart\" placeholder=\"请选择\" clearable @change=\"selectChange($event,element)\">\r\n                  <el-option\r\n                    v-for=\"item in options\"\r\n                    :key=\"item.Code\"\r\n                    :label=\"item.Name\"\r\n                    :disabled=\"item.disabled\"\r\n                    :value=\"item.Code\"\r\n                  >\r\n                    <div class=\"cs-option\">\r\n                      <span class=\"cs-label\">{{ item.Name }}</span>\r\n                      <span v-if=\"item.Is_Nest && isNest\" class=\"cs-tip\">(套)</span>\r\n                    </div>\r\n                  </el-option>\r\n                </el-select>\r\n\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col v-if=\"isCom\" :span=\"7\">\r\n              <el-form-item label=\"要求完成时间\">\r\n                <el-date-picker\r\n                  :key=\"element.key\"\r\n                  v-model=\"element.date\"\r\n                  type=\"date\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  style=\"width: 100%\"\r\n                  placeholder=\"选择日期\"\r\n                  @change=\"dateChange($event,element)\"\r\n                />\r\n              </el-form-item>\r\n\r\n            </el-col>\r\n            <el-col :span=\"7\">\r\n              <el-form-item label=\"班组\" label-width=\"60px\">\r\n                <el-select v-model=\"element.Working_Team_Id\" clearable placeholder=\"请选择\">\r\n                  <el-option v-for=\"item in getWorkingTeam(element.Teams,element)\" :key=\"item.Id\" :label=\"item.Name\" :value=\"item.Id\" />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"3\">\r\n              <span class=\"btn-x\">\r\n                <el-button v-if=\"index===0 && list.length<options.length\" type=\"primary\" icon=\"el-icon-plus\" circle @click=\"handleAdd\" />\r\n                <el-button v-if=\"index!==0&&!element.isPart\" type=\"danger\" icon=\"el-icon-delete\" circle @click=\"handleDelete(element)\" />\r\n              </span>\r\n            </el-col>\r\n\r\n          </el-row>\r\n        </transition-group>\r\n      </draggable>\r\n    </el-form>\r\n    <div class=\"dialog-footer\">\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n      <el-button v-if=\"list.length\" type=\"primary\" :loading=\"btnLoading\" @click=\"submit\">确 定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetProcessListBase } from '@/api/PRO/technology-lib'\r\nimport Draggable from 'vuedraggable'\r\nimport { deepClone, uniqueArr } from '@/utils'\r\nimport { mapActions } from 'vuex'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { GetLibList } from '@/api/PRO/technology-lib'\r\nexport default {\r\n  components: {\r\n    Draggable\r\n  },\r\n  props: {\r\n    pageType: {\r\n      type: String,\r\n      default: undefined\r\n    },\r\n    isNest: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    processList: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      Working_Team_List: [],\r\n      list: [],\r\n      options: [],\r\n      // defaultOptions: [],\r\n      gyList: [],\r\n      btnLoading: false,\r\n      pgLoading: false,\r\n      craftCode: '',\r\n      form: {}\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    }\r\n  },\r\n  watch: {\r\n    list: {\r\n      handler(newVal) {\r\n        if (!this.craftCode) return\r\n        const workCode = this.gyList.find(v => v.Code === this.craftCode)?.WorkCode\r\n        const newCode = newVal.map(v => v.value).filter(v => !!v).join('/')\r\n        if (workCode !== newCode) {\r\n          this.craftCode = ''\r\n        }\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions('schedule', ['initProcessList']),\r\n    getProcessOption(workshopId) {\r\n      return new Promise((resolve, reject) => {\r\n        this.pgLoading = true\r\n        GetProcessListBase({\r\n          workshopId: workshopId,\r\n          type: 1 // 0:全部，工艺类型1：构件工艺，2：零件工艺\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.options = res.Data.map(v => {\r\n              this.$set(v, 'disabled', false)\r\n              return v\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          resolve()\r\n        }).finally(_ => {\r\n          this.pgLoading = false\r\n        })\r\n      })\r\n    },\r\n    craftChange(val) {\r\n      this.craftCode = val\r\n      if (!val) {\r\n        // this.options = this.defaultOptions\r\n        return\r\n      }\r\n      const info = this.gyList.find(v => v.Code === val)\r\n      if (info) {\r\n        const plist = info.WorkCode.split('/')\r\n        // this.options = this.defaultOptions.filter(v => plist.includes(v.Code))\r\n        this.options.forEach((item) => {\r\n          if (plist.includes(item.Code)) {\r\n            item.disabled = true\r\n          } else {\r\n            item.disabled = false\r\n          }\r\n        })\r\n        const newList = []\r\n        console.log('plist', plist)\r\n        plist.forEach((listVal, idx) => {\r\n          const item = this.list.find(v => v.value === listVal)\r\n          console.log('item', item)\r\n          if (item) {\r\n            if (item.Teams.length === 1 && !item.Working_Team_Id) {\r\n              item.Working_Team_Id = item.Teams[0].Id\r\n            }\r\n            newList.push(item)\r\n          } else {\r\n            const item2 = this.options.find(v => v.Code === listVal)\r\n            console.log('item2', item2)\r\n            if (item2) {\r\n              const obj = {\r\n                key: uuidv4(),\r\n                value: item2.Code,\r\n                Working_Team_Id: '',\r\n                Teams: item2.Teams,\r\n                date: ''\r\n              }\r\n              if (item2.Teams.length === 1 && !obj.Working_Team_Id) {\r\n                obj.Working_Team_Id = item2.Teams[0].Id\r\n              }\r\n              newList.push(obj)\r\n            }\r\n          }\r\n        })\r\n        this.list = newList\r\n      }\r\n    },\r\n    getCraftProcess() {\r\n      return new Promise((resolve, reject) => {\r\n        GetLibList({\r\n          Id: '',\r\n          Type: 1\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.gyList = (res.Data || [])\r\n            resolve()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    selectChange(val, element) {\r\n      const arr = this.list.map(i => i.value)\r\n      this.options.forEach((item, index) => {\r\n        item.disabled = arr.includes(item.Code)\r\n        if (item.Code === val) {\r\n          element.Teams = item.Teams\r\n        }\r\n      })\r\n\r\n      if (element) {\r\n        if (val) {\r\n          element.date = this.processList[val]?.Finish_Date\r\n          element.Working_Team_Id = this.processList[val]?.Working_Team_Id\r\n        } else {\r\n          element.Working_Team_Id = ''\r\n          element.Teams = []\r\n        }\r\n      }\r\n\r\n      if (!element?.Working_Team_Id && element?.Teams?.length === 1) {\r\n        element.Working_Team_Id = element.Teams[0].Id\r\n      }\r\n    },\r\n    dateChange(val, element) {\r\n      const item = this.options.find(v => v.Code === element.value)\r\n      console.log('item', item, this.list)\r\n      let obj = {}\r\n      if (item) {\r\n        obj = {\r\n          Schduling_Id: this.formInline?.Schduling_Code,\r\n          Process_Id: item.Id,\r\n          Process_Code: item.Code,\r\n          Finish_Date: val\r\n        }\r\n      }\r\n      // this.$emit('setProcessList', { key: element.value, value: obj })\r\n    },\r\n    handleAdd(item) {\r\n      const arr = this.list.map(v => v.value)\r\n      this.options.forEach(v => {\r\n        if (arr.includes(v.Code)) {\r\n          v.disabled = true\r\n        }\r\n      })\r\n      this.list.push({\r\n        key: uuidv4(),\r\n        value: '',\r\n        Working_Team_Id: '',\r\n        Teams: [],\r\n        date: ''\r\n      })\r\n    },\r\n    handleDelete(element) {\r\n      const idx = this.list.findIndex(v => v.value === element.value)\r\n      if (idx !== -1) {\r\n        this.list.splice(idx, 1)\r\n        this.selectChange()\r\n      }\r\n    },\r\n    getWorkingTeam(teams, curItem) {\r\n      const newTeams = teams.filter(v => {\r\n        if (this.workshopId) {\r\n          return v.Workshop_Id === this.workshopId\r\n        }\r\n        return true\r\n      })\r\n      if (!newTeams.length) {\r\n        curItem.Working_Team_Id = ''\r\n        return []\r\n      }\r\n      if (newTeams.every(v => v.Id !== curItem.Working_Team_Id)) {\r\n        curItem.Working_Team_Id = ''\r\n      }\r\n      return newTeams\r\n    },\r\n    async setData(arr, technologyStr) {\r\n      console.log('arr', arr, technologyStr)\r\n      await this.getCraftProcess()\r\n      let technologyArr = []\r\n      if (technologyStr) {\r\n        technologyArr = technologyStr.split('/')\r\n      }\r\n      const workshopId = arr[0].Workshop_Id\r\n      this.workshopId = workshopId\r\n\r\n      const partUsedProcess = []\r\n\r\n      arr.forEach(v => {\r\n        if (v.Part_Used_Process) {\r\n          const arr = v.Part_Used_Process.split(',')\r\n          partUsedProcess.push(...arr)\r\n        }\r\n      })\r\n      await this.getProcessOption(workshopId)\r\n\r\n      this.options = this.options.filter(item => {\r\n        let flag = false\r\n        if (technologyArr.length && technologyArr.includes(item.Code)) {\r\n          flag = true\r\n        }\r\n        if (partUsedProcess.length && partUsedProcess.includes(item.Code)) {\r\n          flag = true\r\n        }\r\n        if (item.Part_Type_Used_Process && item.Part_Type_Used_Process === item.Code) {\r\n          flag = true\r\n        }\r\n        if (!flag) {\r\n          flag = !!item.Is_Enable\r\n        }\r\n        return flag\r\n      })\r\n      // this.defaultOptions = deepClone(this.options)\r\n\r\n      this.arr = arr || []\r\n      this.list = []\r\n      let codes = []\r\n      if (this.isCom) {\r\n        const origin = arr.map(v => (v?.Part_Used_Process || '').split(','))\r\n        codes = this.getUnique(origin.flat()).filter(v => !!v)\r\n\r\n        if (codes.length) {\r\n          // 零构件\r\n          const checkOption = codes.filter(c => {\r\n            return !!this.options.find(k => k.Code === c)\r\n          })\r\n          console.log(codes, checkOption, this.options.map(v => v.Code))\r\n          // if (checkOption.length < codes.length) {\r\n          //   this.$message({\r\n          //     message: '当前构件生产所属车间内没有该构件所属零件领用工序，请至车间管理内关联相关工序班组',\r\n          //     type: 'warning'\r\n          //   })\r\n          //   return\r\n          // }\r\n\r\n          codes.forEach((value, idx) => {\r\n            const obj = {\r\n              value,\r\n              isPart: true,\r\n              key: uuidv4(),\r\n              Working_Team_Id: this.processList[value]?.Working_Team_Id,\r\n              Teams: this.options.find(item => item.Code === value)?.Teams || [],\r\n              date: this.processList[value]?.Finish_Date\r\n            }\r\n            if (obj.Teams.length === 1 && !obj.Working_Team_Id) {\r\n              obj.Working_Team_Id = obj.Teams[0].Id\r\n            }\r\n            this.list.push(obj)\r\n          })\r\n        }\r\n      }\r\n      if (technologyArr.length) {\r\n        console.log('this.options6666', this.options)\r\n        console.log('this.processList', this.processList)\r\n        const techArr = technologyArr.map(v => {\r\n          const obj = {\r\n            key: uuidv4(),\r\n            value: v,\r\n            Working_Team_Id: this.processList[v]?.Working_Team_Id,\r\n            Teams: this.options.find(item => item.Code === v)?.Teams || [],\r\n            date: this.processList[v]?.Finish_Date\r\n          }\r\n          if (obj.Teams.length === 1 && !obj.Working_Team_Id) {\r\n            obj.Working_Team_Id = obj.Teams[0].Id\r\n          }\r\n          return obj\r\n        })\r\n        console.log('techArr', techArr)\r\n        techArr.forEach((element, idx) => {\r\n          if (!codes.includes(element.value)) {\r\n            this.list.push(element)\r\n          }\r\n        })\r\n      }\r\n      if (!this.list.length) {\r\n        this.list.push({\r\n          value: '',\r\n          key: uuidv4(),\r\n          Working_Team_Id: '',\r\n          Teams: [],\r\n          date: ''\r\n        })\r\n        if (this.isNest) {\r\n          const xur = this.options.filter(item => item.Is_Nest)\r\n          if (xur.length === 1) {\r\n            this.list[0].value = xur[0].Code\r\n          }\r\n        }\r\n      }\r\n      const indexMap = technologyArr.reduce((map, item, index) => {\r\n        map[item] = index\r\n        return map\r\n      }, {})\r\n\r\n      this.list.sort((item1, item2) => {\r\n        return indexMap[item1.value] - indexMap[item2.value]\r\n      })\r\n\r\n      this.selectChange()\r\n    },\r\n    getUnique(arr) {\r\n      return uniqueArr(arr)\r\n    },\r\n    submit() {\r\n      const list = this.list.map(item => item.value).filter(k => !!k)\r\n      const isTrue = this.checkCode(list)\r\n      if (!isTrue) {\r\n        this.$message({\r\n          message: '相邻工序不能相同',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      if (!list.length) {\r\n        this.$message({\r\n          message: '工序不能全为空',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n\r\n      if (this.isNest) {\r\n        const xur = this.options.filter(item => item.Is_Nest)\r\n        if (xur.length) {\r\n          const hasNest = xur.some(obj => list.includes(obj.Code))\r\n          if (!hasNest) {\r\n            this.$message({\r\n              message: '请至少选择一个套料工序！',\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n        }\r\n      }\r\n\r\n      this.btnLoading = true\r\n      const str = list.join('/')\r\n      this.list.forEach((element, idx) => {\r\n        const item = this.options.find(v => v.Code === element.value)\r\n\r\n        let obj = {}\r\n        if (item) {\r\n          obj = {\r\n            Schduling_Id: this.formInline?.Schduling_Code,\r\n            Process_Id: item.Id,\r\n            Process_Code: item.Code,\r\n            Finish_Date: element.date,\r\n            Working_Team_Id: element.Working_Team_Id\r\n          }\r\n        }\r\n        this.$emit('setProcessList', { key: element.value, value: obj })\r\n      })\r\n\r\n      this.$emit('sendProcess', { arr: this.arr, str })\r\n      this.btnLoading = false\r\n      this.handleClose()\r\n    },\r\n    handleClose() {\r\n      this.$emit('close')\r\n    },\r\n    checkCode(list) {\r\n      let flag = true\r\n      for (let i = 0; i < list.length; i++) {\r\n        if (i !== list.length - 1 && list[i] === list[i + 1]) {\r\n          flag = false\r\n          break\r\n        }\r\n      }\r\n      return flag\r\n    },\r\n    changeDraggable() {\r\n      this.list.forEach(v => {\r\n        this.$set(v, 'date', '')\r\n      })\r\n      this.initProcessList()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.btn-x{\r\n  margin-left: 20px;\r\n}\r\n.dialog-footer{\r\n  text-align: right;\r\n  margin-top: 30px;\r\n}\r\n\r\n.cs-drag{\r\n  line-height: 32px;\r\n  cursor: move;\r\n}\r\n.cs-option{\r\n  display:flex;\r\n  justify-content: space-between;\r\n  .cs-label{\r\n\r\n  }\r\n  .cs-tip{\r\n    color: #409EFF;\r\n  }\r\n}\r\n\r\n</style>\r\n"]}]}
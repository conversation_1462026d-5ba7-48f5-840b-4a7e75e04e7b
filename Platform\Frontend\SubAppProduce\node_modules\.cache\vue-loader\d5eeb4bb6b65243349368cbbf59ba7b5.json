{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\ToleranceConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\ToleranceConfig.vue", "mtime": 1757468112847}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ToleranceConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "ToleranceConfig.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup/components", "sourcesContent": ["<template>\r\n  <div style=\"height: calc(100vh - 300px)\">\r\n    <vxe-table\r\n      v-loading=\"tbLoading\"\r\n      :empty-render=\"{name: 'NotData'}\"\r\n      show-header-overflow\r\n      element-loading-spinner=\"el-icon-loading\"\r\n      element-loading-text=\"拼命加载中\"\r\n      empty-text=\"暂无数据\"\r\n      height=\"100%\"\r\n      align=\"left\"\r\n      stripe\r\n      :data=\"tbData\"\r\n      resizable\r\n      :auto-resize=\"true\"\r\n      class=\"cs-vxe-table\"\r\n      :tooltip-config=\"{ enterable: true }\"\r\n    >\r\n      <vxe-column\r\n        show-overflow=\"tooltip\"\r\n        sortable\r\n        field=\"Length\"\r\n        title=\"长度\"\r\n        min-width=\"150\"\r\n        align=\"center\"\r\n      >\r\n        <template #default=\"{ row }\">\r\n          <span>\r\n            {{ row.TypeTag }}\r\n            {{ row.Length }}\r\n          </span>\r\n        </template>\r\n      </vxe-column>\r\n      <vxe-column\r\n        show-overflow=\"tooltip\"\r\n        sortable\r\n        min-width=\"150\"\r\n        align=\"left\"\r\n        field=\"Demand\"\r\n        title=\"公差要求\"\r\n      />\r\n      <vxe-column\r\n        show-overflow=\"tooltip\"\r\n        sortable\r\n        field=\"Modify_Date\"\r\n        title=\"编辑时间\"\r\n        min-width=\"150\"\r\n        align=\"center\"\r\n      />\r\n      <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" align=\"center\" show-overflow>\r\n        <template #default=\"{ row }\">\r\n          <el-button type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\r\n          <el-divider direction=\"vertical\" />\r\n          <el-button type=\"text\" @click=\"removeEvent(row)\">删除</el-button>\r\n        </template>\r\n      </vxe-column>\r\n    </vxe-table>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport {\r\n  DeleteToleranceSetting,\r\n  GetToleranceSettingList\r\n} from '@/api/PRO/qualityInspect/quality-management'\r\n\r\nexport default {\r\n\r\n  data() {\r\n    return {\r\n      tbLoading: false,\r\n      tbData: [],\r\n      dialogVisible: false,\r\n      form: {\r\n        Id: '',\r\n        Length: 0,\r\n        Demand: '',\r\n        Type: 1\r\n      }\r\n\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getToleranceList()\r\n  },\r\n  methods: {\r\n    getToleranceList() {\r\n      this.tbLoading = true\r\n      GetToleranceSettingList({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.map(item => {\r\n            item.Modify_Date = item.Modify_Date || item.Create_Date\r\n            item.TypeTag = item.Type === 1 ? '<' : item.Type === 2 ? '<=' : item.Type === 3 ? '>' : item.Type === 4 ? '>=' : '='\r\n            return item\r\n          })\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    editEvent(row) {\r\n      const form = JSON.parse(JSON.stringify(row))\r\n      this.$emit('edit', form)\r\n    },\r\n    removeEvent(row) {\r\n      this.$confirm('此操作将永久删除该公差配置, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          DeleteToleranceSetting({ id: row.Id }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                type: 'success',\r\n                message: '删除成功!'\r\n              })\r\n              this.getToleranceList()\r\n            } else {\r\n              this.$message({\r\n                type: 'error',\r\n                message: res.Message\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    }\r\n\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n</style>\r\n"]}]}
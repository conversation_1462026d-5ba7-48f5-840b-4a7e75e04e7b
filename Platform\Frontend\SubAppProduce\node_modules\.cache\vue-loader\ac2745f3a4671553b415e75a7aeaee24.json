{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\suggestDevice.vue?vue&type=template&id=20c0ba33&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\suggestDevice.vue", "mtime": 1758689267822}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImNvbnRlbnRCb3giPgogIDxkaXYgY2xhc3M9ImRldmljZS10b3RhbCI+CiAgICA8ZGl2IGNsYXNzPSJmaXJzdCI+5Yqg5bel6K6+5aSH5pWw6YePPHNwYW4+Mjwvc3Bhbj48L2Rpdj4KICAgIDxkaXYgY2xhc3M9InNlY29uZCI+57Sv6K6h5Yqg5bel5pe26Ze0PHNwYW4+MTBoPC9zcGFuPjwvZGl2PgogICAgPGRpdiBjbGFzcz0idGhpcmQiPue0r+iuoeWIh+WJsuexs+aVsDxzcGFuPjIwMC4yNTwvc3Bhbj48L2Rpdj4KICA8L2Rpdj4KICA8ZGl2IGNsYXNzPSJkZXZpY2UtbGlzdCI+CiAgICA8ZGl2IGNsYXNzPSJkZXZpY2UtaW5mbyI+CiAgICAgIDxkaXYgY2xhc3M9ImltYWdlIj48RWxUYWJsZUVtcHR5IDplbXB0eS1jb250ZW50PSLmmoLml6Dlm77niYciIC8+PC9kaXY+CiAgICAgIDxkaXYgY2xhc3M9ImluZm8iPuS/oeaBrzwvZGl2PgogICAgPC9kaXY+CiAgPC9kaXY+CjwvZGl2Pgo="}, null]}
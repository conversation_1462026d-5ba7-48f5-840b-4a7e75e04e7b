{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\half-part-config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\half-part-config\\index.vue", "mtime": 1756109946516}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetTableSettingList", "UpdateComponentPartTableSetting", "UpdateColumnSetting", "GetBOMInfo", "name", "components", "data", "activeNameApi", "activeName", "currentCode", "partName", "typeCode", "materialCode", "currentFinalTypeCode", "tabPosition", "searchVal", "majorName", "unit", "steelUnit", "templateList", "templateListNew", "loading", "systemField", "expandField", "businessField", "computed", "tabList", "label", "value", "created", "level", "$route", "query", "mounted", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "steel_unit", "GetTableSettingListFn", "stop", "methods", "changeStatus", "$event", "id", "displayName", "find", "item", "Id", "Display_Name", "$message", "type", "message", "map", "Is_Enabled", "handleClick", "tab", "event", "searchValue", "_this2", "filterList", "search", "RegExp", "push", "saveModifyChangesFn", "_this3", "_callee2", "res", "_callee2$", "_context2", "sent", "IsSucceed", "Message", "_this4", "_callee3", "_callee3$", "_context3", "_this5", "_callee4", "_yield$GetBOMInfo", "list", "currentParentBOMInfo", "currentBOMInfo", "partParent", "_callee4$", "_context4", "IsComponent", "ProfessionalCode", "TypeCode", "abrupt", "Data", "length", "for<PERSON>ach", "v", "Code", "some", "Column_Type"], "sources": ["src/views/PRO/bom-setting/half-part-config/index.vue"], "sourcesContent": ["<!-- 半构件 -->\r\n<template>\r\n  <div>\r\n    <div class=\"page-container\">\r\n      <div class=\"top-wrapper\">\r\n        <!-- <div class=\"title\">零件模板配置：</div> -->\r\n        <div class=\"info\">\r\n          <template v-if=\"!!majorName\">\r\n            <div class=\"title\">当前专业：</div>\r\n            <div class=\"value\">{{ majorName }}</div>\r\n          </template>\r\n          <template v-if=\"!!unit\">\r\n            <div class=\"title\">统计单位：</div>\r\n            <div class=\"value\">{{ unit }}</div>\r\n          </template>\r\n          <template v-if=\"!!steelUnit\">\r\n            <div class=\"title\">构件单位：</div>\r\n            <div class=\"value\">{{ steelUnit }}</div>\r\n          </template>\r\n          <template>\r\n            <div class=\"title\">单位统计字段：</div>\r\n            <div v-for=\"(item,index) in templateListNew\" v-show=\"item.Code==='Num'\" :key=\"index\" style=\"display: flex;flex-direction: row\">\r\n              {{ item.Display_Name }}\r\n            </div>\r\n            <div v-for=\"(item,index) in templateListNew\" v-show=\"item.Code==='Weight'\" :key=\"index+999\" style=\"display: flex;flex-direction: row\">\r\n              *{{ item.Display_Name }}\r\n            </div>\r\n          </template>\r\n        </div>\r\n        <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\r\n          <el-tab-pane v-for=\"(item,index) in tabList\" :key=\"partName+index\" :label=\"item.label\" :name=\"item.value\" />\r\n\r\n        </el-tabs>\r\n      </div>\r\n      <div class=\"content-wrapper\" style=\"min-height: calc(100vh - 340px)\">\r\n        <div class=\"right-c\">\r\n          <el-row type=\"flex\" justify=\"space-between\">\r\n            <div class=\"right-c-title\">\r\n              <div class=\"setting-title\">{{ systemField==true ? '系统字段' : businessField==true ? '业务字段' : expandField==true ? '拓展字段' : '' }}</div>\r\n            </div>\r\n            <div style=\"display: flex;flex-direction: row\">\r\n              <span style=\"width:140px;font-size:12px;height:32px; line-height: 32px;display: inline-block;\">字段名称：</span>\r\n              <el-input v-model=\"searchVal\" placeholder=\"请输入字段名称\" clearable />\r\n              <el-button type=\"primary\" style=\"margin-left: 10px\" @click=\"searchValue\">查询</el-button>\r\n              <el-button type=\"primary\" :loading=\"loading\" @click=\"saveModifyChangesFn\">保存设置</el-button>\r\n            </div>\r\n          </el-row>\r\n          <el-form label-width=\"120px\" style=\"margin-top: 24px\">\r\n            <template v-for=\"(item,index) in templateListNew\">\r\n              <el-row v-if=\"item.Column_Type==0\" :key=\"index\">\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"item.Code\" label-width=\"150px\">\r\n                    <el-input v-model=\"item.Display_Name\" :disabled=\"false\" style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"'备注说明'\">\r\n                    <el-input v-model=\"item.Remark\" disabled style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"排序\">\r\n                    <el-input v-model=\"item.Sort\" style=\"width: 100px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"是否显示\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </template>\r\n            <div v-show=\"businessField==true && systemField==true\" class=\"setting-title\">业务字段</div>\r\n            <template v-for=\"(item,index) in templateListNew\">\r\n              <el-row v-if=\"item.Column_Type==2\" :key=\"index\">\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"item.Code\" label-width=\"150px\">\r\n                    <el-input v-model=\"item.Display_Name\" style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"'备注说明'\">\r\n                    <el-input v-model=\"item.Remark\" disabled style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"排序\">\r\n                    <el-input v-model=\"item.Sort\" style=\"width: 100px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName==activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"是否启用\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-else :span=\"4\">\r\n                  <el-form-item label=\"是否显示\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </template>\r\n            <div v-show=\"expandField==true\" class=\"setting-title\">拓展字段</div>\r\n            <template v-for=\"(item,index) in templateListNew\">\r\n              <el-row v-if=\"item.Column_Type==1\" :key=\"index\">\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"item.Code\" label-width=\"150px\">\r\n                    <el-input v-model=\"item.Display_Name\" style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"'备注说明'\">\r\n                    <el-input v-model=\"item.Remark\" disabled style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"排序\">\r\n                    <el-input v-model=\"item.Sort\" style=\"width: 100px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName==activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"是否启用\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-else :span=\"4\">\r\n                  <el-form-item label=\"是否显示\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </template>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { GetTableSettingList, UpdateComponentPartTableSetting, UpdateColumnSetting } from '@/api/PRO/component-type'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\n\r\nexport default {\r\n  name: 'PROHalfPartConfig',\r\n  components: {\r\n  },\r\n  data() {\r\n    return {\r\n      activeNameApi: 'plm_parts_field_page_list',\r\n      activeName: 'plm_parts_field_page_list',\r\n      currentCode: 'plm_parts_page_list',\r\n      partName: '',\r\n      typeCode: '',\r\n      materialCode: '',\r\n      currentFinalTypeCode: '',\r\n      tabPosition: 'left',\r\n      //   tabList: [\r\n      //     {\r\n      //       label: '零件字段维护',\r\n      //       value: 'plm_parts_field_page_list'\r\n      //     },\r\n      //     {\r\n      //       label: '零件管理列表',\r\n      //       value: 'plm_parts_page_list'\r\n      //     },\r\n      //     {\r\n      //       label: '零件深化清单',\r\n      //       value: 'plm_parts_detailImport'\r\n      //     },\r\n      //     {\r\n      //       label: '生产详情列表',\r\n      //       value: 'plm_parts_modelImport'\r\n      //     }\r\n      //   ],\r\n      searchVal: '',\r\n      majorName: '',\r\n      unit: '',\r\n      steelUnit: '',\r\n      templateList: [],\r\n      templateListNew: [],\r\n      loading: false,\r\n      systemField: false,\r\n      expandField: false,\r\n      businessField: false\r\n    }\r\n  },\r\n  computed: {\r\n    tabList() {\r\n      return [\r\n        {\r\n          label: this.partName + '字段维护',\r\n          value: 'plm_parts_field_page_list'\r\n        },\r\n        {\r\n          label: this.partName + '管理列表',\r\n          value: 'plm_parts_page_list'\r\n        },\r\n        {\r\n          label: this.partName + '深化清单',\r\n          value: 'plm_parts_detailImport'\r\n        },\r\n        {\r\n          label: '生产详情列表',\r\n          value: 'plm_parts_modelImport'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  created() {\r\n    this.level = +this.$route.query.level || 2\r\n    this.typeCode = this.$route.query.typeCode || 'Steel'\r\n    this.materialCode = this.$route.query.materialCode || 'StructuralAs'\r\n    this.currentFinalTypeCode = this.typeCode\r\n  },\r\n  async mounted() {\r\n    this.majorName = this.$route.query.name || '钢结构'\r\n    this.unit = this.$route.query.unit || 't'\r\n    this.steelUnit = this.$route.query.steel_unit || 'kg'\r\n\r\n    this.GetTableSettingListFn()\r\n  },\r\n  methods: {\r\n\r\n    changeStatus($event, id) {\r\n      const displayName = this.templateList.find((item) => { return item.Id == id }).Display_Name\r\n      if (displayName == '' && $event == true) {\r\n        this.$message({ type: 'error', message: '请先填写字段名' })\r\n        this.templateList.map((item) => {\r\n          if (item.Id == id) {\r\n            item.Is_Enabled = false\r\n          }\r\n          return item\r\n        })\r\n      }\r\n    },\r\n    handleClick(tab, event) {\r\n      this.currentCode = tab.name\r\n      this.GetTableSettingListFn()\r\n    },\r\n    searchValue() {\r\n      if (!this.searchVal) {\r\n        this.templateListNew = this.templateList\r\n      } else {\r\n        const filterList = []\r\n        this.templateList.map(item => {\r\n          if (item.Display_Name.search(new RegExp(this.searchVal, 'ig')) !== -1) {\r\n            filterList.push(item)\r\n          }\r\n        })\r\n        this.templateListNew = filterList\r\n      }\r\n    },\r\n\r\n    saveModifyChangesFn() {\r\n      if (this.activeName == this.activeNameApi) {\r\n        this.UpdateComponentPartTableSetting()\r\n      } else {\r\n        this.UpdateColumnSetting()\r\n      }\r\n    },\r\n\r\n    async UpdateColumnSetting() {\r\n      this.loading = true\r\n      const res = await UpdateColumnSetting(this.templateList)\r\n      this.loading = false\r\n      if (res.IsSucceed) {\r\n        this.$message({ type: 'success', message: '保存成功' })\r\n      } else {\r\n        this.$message({ type: 'error', message: res.Message })\r\n      }\r\n    },\r\n\r\n    async UpdateComponentPartTableSetting() {\r\n      this.loading = true\r\n      const res = await UpdateComponentPartTableSetting(this.templateList)\r\n      this.loading = false\r\n      if (res.IsSucceed) {\r\n        this.$message({ type: 'success', message: '保存成功' })\r\n      } else {\r\n        this.$message({ type: 'error', message: res.Message })\r\n      }\r\n    },\r\n\r\n    async GetTableSettingListFn() {\r\n      let data = {}\r\n      if (this.activeName == this.activeNameApi) {\r\n        data = { IsComponent: false, ProfessionalCode: this.currentFinalTypeCode }\r\n      } else {\r\n        data = { IsComponent: false, ProfessionalCode: this.currentFinalTypeCode, TypeCode: this.currentCode + ',' + this.currentFinalTypeCode }\r\n      }\r\n      const { list, currentParentBOMInfo, currentBOMInfo } = await GetBOMInfo(this.level)\r\n      if (this.level < 1) {\r\n        this.$message({ type: 'error', message: '系统层级配置错误' })\r\n        return\r\n      }\r\n      const partName = currentBOMInfo?.Display_Name || ''\r\n      const partParent = currentParentBOMInfo?.Display_Name || ''\r\n      this.partName = partName\r\n      const res = await GetTableSettingList(data)\r\n      if (res.IsSucceed) {\r\n        this.templateList = res.Data || []\r\n        if (this.templateList.length > 0) {\r\n          this.templateList.forEach(v => {\r\n            if (v.Code === 'Code') {\r\n              v.Display_Name = partName\r\n            }\r\n            if (v.Code === 'Component_Code') {\r\n              v.Display_Name = partParent\r\n            }\r\n          })\r\n        }\r\n        this.templateListNew = this.templateList\r\n        this.systemField = this.templateList.some(item => { return item.Column_Type == 0 })\r\n        this.expandField = this.templateList.some(item => { return item.Column_Type == 1 })\r\n        this.businessField = this.templateList.some(item => { return item.Column_Type == 2 })\r\n      } else {\r\n        this.$message({ type: 'error', message: res.Message })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n  <style lang=\"scss\" scoped>\r\n    .page-container{\r\n      margin:16px;\r\n      box-sizing: border-box;\r\n      .top-wrapper{\r\n        background: #fff;\r\n        padding:16px;\r\n        box-sizing: border-box;\r\n        .title{\r\n          font-size: 16px;\r\n          font-weight: 500;\r\n          color:#333333;\r\n        }\r\n        .info{\r\n          font-size: 14px;\r\n          margin:8px 0 24px 0;\r\n          display: flex;\r\n          flex-direction: row;\r\n          .title{\r\n            font-size: 14px;\r\n            color: #999999;\r\n          }\r\n          .value{\r\n            color: #333333;\r\n            margin-right: 24px;\r\n          }\r\n        }\r\n      }\r\n      .content-wrapper{\r\n        margin-top:16px;\r\n        display: flex;\r\n        flex-direction: row;\r\n        .left-c{\r\n          width: 160px;\r\n          background: #fff;\r\n          margin-right: 16px;\r\n        }\r\n        .right-c{\r\n          background: #fff;\r\n          width: 100%;\r\n          padding: 16px 24px;\r\n          box-sizing: border-box;\r\n        }\r\n      }\r\n    }\r\n    ::v-deep.el-tabs--left .el-tabs__nav-wrap.is-left::after{\r\n      left:0\r\n    }\r\n    ::v-deep.el-tabs--left .el-tabs__active-bar.is-left{\r\n      left: 0;\r\n    }\r\n    .setting-title {\r\n      font-weight: 400;\r\n      color: #1f2f3d;\r\n      margin: 30px 0 20px;\r\n      font-size: 22px;\r\n    }\r\n    .setting-title:first-child {\r\n      margin: 0;\r\n    }\r\n  </style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoKA,SAAAA,mBAAA,EAAAC,+BAAA,IAAAA,gCAAA,EAAAC,mBAAA,IAAAA,oBAAA;AACA,SAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,UAAA,GACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,UAAA;MACAC,WAAA;MACAC,QAAA;MACAC,QAAA;MACAC,YAAA;MACAC,oBAAA;MACAC,WAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC,SAAA;MACAC,SAAA;MACAC,IAAA;MACAC,SAAA;MACAC,YAAA;MACAC,eAAA;MACAC,OAAA;MACAC,WAAA;MACAC,WAAA;MACAC,aAAA;IACA;EACA;EACAC,QAAA;IACAC,OAAA,WAAAA,QAAA;MACA,QACA;QACAC,KAAA,OAAAjB,QAAA;QACAkB,KAAA;MACA,GACA;QACAD,KAAA,OAAAjB,QAAA;QACAkB,KAAA;MACA,GACA;QACAD,KAAA,OAAAjB,QAAA;QACAkB,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,KAAA,SAAAC,MAAA,CAAAC,KAAA,CAAAF,KAAA;IACA,KAAAnB,QAAA,QAAAoB,MAAA,CAAAC,KAAA,CAAArB,QAAA;IACA,KAAAC,YAAA,QAAAmB,MAAA,CAAAC,KAAA,CAAApB,YAAA;IACA,KAAAC,oBAAA,QAAAF,QAAA;EACA;EACAsB,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACAT,KAAA,CAAAlB,SAAA,GAAAkB,KAAA,CAAAH,MAAA,CAAAC,KAAA,CAAA5B,IAAA;YACA8B,KAAA,CAAAjB,IAAA,GAAAiB,KAAA,CAAAH,MAAA,CAAAC,KAAA,CAAAf,IAAA;YACAiB,KAAA,CAAAhB,SAAA,GAAAgB,KAAA,CAAAH,MAAA,CAAAC,KAAA,CAAAY,UAAA;YAEAV,KAAA,CAAAW,qBAAA;UAAA;UAAA;YAAA,OAAAJ,QAAA,CAAAK,IAAA;QAAA;MAAA,GAAAR,OAAA;IAAA;EACA;EACAS,OAAA;IAEAC,YAAA,WAAAA,aAAAC,MAAA,EAAAC,EAAA;MACA,IAAAC,WAAA,QAAAhC,YAAA,CAAAiC,IAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,EAAA,IAAAJ,EAAA;MAAA,GAAAK,YAAA;MACA,IAAAJ,WAAA,UAAAF,MAAA;QACA,KAAAO,QAAA;UAAAC,IAAA;UAAAC,OAAA;QAAA;QACA,KAAAvC,YAAA,CAAAwC,GAAA,WAAAN,IAAA;UACA,IAAAA,IAAA,CAAAC,EAAA,IAAAJ,EAAA;YACAG,IAAA,CAAAO,UAAA;UACA;UACA,OAAAP,IAAA;QACA;MACA;IACA;IACAQ,WAAA,WAAAA,YAAAC,GAAA,EAAAC,KAAA;MACA,KAAAtD,WAAA,GAAAqD,GAAA,CAAA1D,IAAA;MACA,KAAAyC,qBAAA;IACA;IACAmB,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,UAAAlD,SAAA;QACA,KAAAK,eAAA,QAAAD,YAAA;MACA;QACA,IAAA+C,UAAA;QACA,KAAA/C,YAAA,CAAAwC,GAAA,WAAAN,IAAA;UACA,IAAAA,IAAA,CAAAE,YAAA,CAAAY,MAAA,KAAAC,MAAA,CAAAH,MAAA,CAAAlD,SAAA;YACAmD,UAAA,CAAAG,IAAA,CAAAhB,IAAA;UACA;QACA;QACA,KAAAjC,eAAA,GAAA8C,UAAA;MACA;IACA;IAEAI,mBAAA,WAAAA,oBAAA;MACA,SAAA9D,UAAA,SAAAD,aAAA;QACA,KAAAN,+BAAA;MACA;QACA,KAAAC,mBAAA;MACA;IACA;IAEAA,mBAAA,WAAAA,oBAAA;MAAA,IAAAqE,MAAA;MAAA,OAAApC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAmC,SAAA;QAAA,IAAAC,GAAA;QAAA,OAAArC,mBAAA,GAAAG,IAAA,UAAAmC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjC,IAAA,GAAAiC,SAAA,CAAAhC,IAAA;YAAA;cACA4B,MAAA,CAAAlD,OAAA;cAAAsD,SAAA,CAAAhC,IAAA;cAAA,OACAzC,oBAAA,CAAAqE,MAAA,CAAApD,YAAA;YAAA;cAAAsD,GAAA,GAAAE,SAAA,CAAAC,IAAA;cACAL,MAAA,CAAAlD,OAAA;cACA,IAAAoD,GAAA,CAAAI,SAAA;gBACAN,MAAA,CAAAf,QAAA;kBAAAC,IAAA;kBAAAC,OAAA;gBAAA;cACA;gBACAa,MAAA,CAAAf,QAAA;kBAAAC,IAAA;kBAAAC,OAAA,EAAAe,GAAA,CAAAK;gBAAA;cACA;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAA7B,IAAA;UAAA;QAAA,GAAA0B,QAAA;MAAA;IACA;IAEAvE,+BAAA,WAAAA,gCAAA;MAAA,IAAA8E,MAAA;MAAA,OAAA5C,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA2C,SAAA;QAAA,IAAAP,GAAA;QAAA,OAAArC,mBAAA,GAAAG,IAAA,UAAA0C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxC,IAAA,GAAAwC,SAAA,CAAAvC,IAAA;YAAA;cACAoC,MAAA,CAAA1D,OAAA;cAAA6D,SAAA,CAAAvC,IAAA;cAAA,OACA1C,gCAAA,CAAA8E,MAAA,CAAA5D,YAAA;YAAA;cAAAsD,GAAA,GAAAS,SAAA,CAAAN,IAAA;cACAG,MAAA,CAAA1D,OAAA;cACA,IAAAoD,GAAA,CAAAI,SAAA;gBACAE,MAAA,CAAAvB,QAAA;kBAAAC,IAAA;kBAAAC,OAAA;gBAAA;cACA;gBACAqB,MAAA,CAAAvB,QAAA;kBAAAC,IAAA;kBAAAC,OAAA,EAAAe,GAAA,CAAAK;gBAAA;cACA;YAAA;YAAA;cAAA,OAAAI,SAAA,CAAApC,IAAA;UAAA;QAAA,GAAAkC,QAAA;MAAA;IACA;IAEAnC,qBAAA,WAAAA,sBAAA;MAAA,IAAAsC,MAAA;MAAA,OAAAhD,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA+C,SAAA;QAAA,IAAA9E,IAAA,EAAA+E,iBAAA,EAAAC,IAAA,EAAAC,oBAAA,EAAAC,cAAA,EAAA9E,QAAA,EAAA+E,UAAA,EAAAhB,GAAA;QAAA,OAAArC,mBAAA,GAAAG,IAAA,UAAAmD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjD,IAAA,GAAAiD,SAAA,CAAAhD,IAAA;YAAA;cACArC,IAAA;cACA,IAAA6E,MAAA,CAAA3E,UAAA,IAAA2E,MAAA,CAAA5E,aAAA;gBACAD,IAAA;kBAAAsF,WAAA;kBAAAC,gBAAA,EAAAV,MAAA,CAAAtE;gBAAA;cACA;gBACAP,IAAA;kBAAAsF,WAAA;kBAAAC,gBAAA,EAAAV,MAAA,CAAAtE,oBAAA;kBAAAiF,QAAA,EAAAX,MAAA,CAAA1E,WAAA,SAAA0E,MAAA,CAAAtE;gBAAA;cACA;cAAA8E,SAAA,CAAAhD,IAAA;cAAA,OACAxC,UAAA,CAAAgF,MAAA,CAAArD,KAAA;YAAA;cAAAuD,iBAAA,GAAAM,SAAA,CAAAf,IAAA;cAAAU,IAAA,GAAAD,iBAAA,CAAAC,IAAA;cAAAC,oBAAA,GAAAF,iBAAA,CAAAE,oBAAA;cAAAC,cAAA,GAAAH,iBAAA,CAAAG,cAAA;cAAA,MACAL,MAAA,CAAArD,KAAA;gBAAA6D,SAAA,CAAAhD,IAAA;gBAAA;cAAA;cACAwC,MAAA,CAAA3B,QAAA;gBAAAC,IAAA;gBAAAC,OAAA;cAAA;cAAA,OAAAiC,SAAA,CAAAI,MAAA;YAAA;cAGArF,QAAA,IAAA8E,cAAA,aAAAA,cAAA,uBAAAA,cAAA,CAAAjC,YAAA;cACAkC,UAAA,IAAAF,oBAAA,aAAAA,oBAAA,uBAAAA,oBAAA,CAAAhC,YAAA;cACA4B,MAAA,CAAAzE,QAAA,GAAAA,QAAA;cAAAiF,SAAA,CAAAhD,IAAA;cAAA,OACA3C,mBAAA,CAAAM,IAAA;YAAA;cAAAmE,GAAA,GAAAkB,SAAA,CAAAf,IAAA;cACA,IAAAH,GAAA,CAAAI,SAAA;gBACAM,MAAA,CAAAhE,YAAA,GAAAsD,GAAA,CAAAuB,IAAA;gBACA,IAAAb,MAAA,CAAAhE,YAAA,CAAA8E,MAAA;kBACAd,MAAA,CAAAhE,YAAA,CAAA+E,OAAA,WAAAC,CAAA;oBACA,IAAAA,CAAA,CAAAC,IAAA;sBACAD,CAAA,CAAA5C,YAAA,GAAA7C,QAAA;oBACA;oBACA,IAAAyF,CAAA,CAAAC,IAAA;sBACAD,CAAA,CAAA5C,YAAA,GAAAkC,UAAA;oBACA;kBACA;gBACA;gBACAN,MAAA,CAAA/D,eAAA,GAAA+D,MAAA,CAAAhE,YAAA;gBACAgE,MAAA,CAAA7D,WAAA,GAAA6D,MAAA,CAAAhE,YAAA,CAAAkF,IAAA,WAAAhD,IAAA;kBAAA,OAAAA,IAAA,CAAAiD,WAAA;gBAAA;gBACAnB,MAAA,CAAA5D,WAAA,GAAA4D,MAAA,CAAAhE,YAAA,CAAAkF,IAAA,WAAAhD,IAAA;kBAAA,OAAAA,IAAA,CAAAiD,WAAA;gBAAA;gBACAnB,MAAA,CAAA3D,aAAA,GAAA2D,MAAA,CAAAhE,YAAA,CAAAkF,IAAA,WAAAhD,IAAA;kBAAA,OAAAA,IAAA,CAAAiD,WAAA;gBAAA;cACA;gBACAnB,MAAA,CAAA3B,QAAA;kBAAAC,IAAA;kBAAAC,OAAA,EAAAe,GAAA,CAAAK;gBAAA;cACA;YAAA;YAAA;cAAA,OAAAa,SAAA,CAAA7C,IAAA;UAAA;QAAA,GAAAsC,QAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}
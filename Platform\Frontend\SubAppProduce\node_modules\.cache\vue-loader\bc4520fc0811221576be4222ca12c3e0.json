{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-product-type\\index.vue?vue&type=template&id=6ae1aa1c&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-product-type\\index.vue", "mtime": 1757468128035}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}
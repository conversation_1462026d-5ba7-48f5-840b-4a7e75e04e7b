{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\group\\component\\detail.vue?vue&type=style&index=0&id=09a19f5e&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\group\\component\\detail.vue", "mtime": 1757468112129}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCkBpbXBvcnQgIn5AL3N0eWxlcy9taXhpbi5zY3NzIjsNCg0KaDMgew0KICBjb2xvcjogIzI5OGRmZjsNCn0NCg0KLnRhZy14IHsNCiAgdGV4dC1hbGlnbjogbGVmdDsNCg0KICAudGFnLXdyYXBwZXIgew0KICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgICBmbGV4LXdyYXA6IHdyYXA7DQogICAgbWF4LWhlaWdodDogMTYwcHg7DQogICAgb3ZlcmZsb3c6IGF1dG87DQogICAgQGluY2x1ZGUgc2Nyb2xsQmFyOw0KDQogICAgLmVsLXRhZyB7DQogICAgICBtYXJnaW46IDhweCAwIDAgOHB4Ow0KICAgIH0NCiAgfQ0KDQogIC5hZGQtYnRuIHsNCiAgICBtYXJnaW4tdG9wOiAxMnB4Ow0KICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgaGVpZ2h0OiAzMnB4Ow0KICAgIGxpbmUtaGVpZ2h0OiAzMnB4Ow0KICAgIGJhY2tncm91bmQ6IHJnYmEoNDEsIDE0MSwgMjU1LCAwLjAzKTsNCiAgICBjb2xvcjogIzI5OGRmZjsNCiAgICBib3JkZXI6IDFweCBkYXNoZWQgcmdiYSg0MSwgMTQxLCAyNTUsIDAuMzIxNTY4NjI3NDUwOTgwMzYpOw0KICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgfQ0KfQ0KDQpmb290ZXIgew0KICBtYXJnaW46IDIwcHg7DQogIHRleHQtYWxpZ246IHJpZ2h0Ow0KDQogICY6Zmlyc3QtY2hpbGQgew0KICAgIG1hcmdpbi1yaWdodDogMjBweDsNCiAgfQ0KfQ0KDQo6OnYtZGVlcCB7DQogIC5pbnB1dC1udW1iZXIgew0KICAgIGlucHV0IHsNCiAgICAgIHBhZGRpbmctcmlnaHQ6IDJweDsNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8ZA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/PRO/basic-information/group/component", "sourcesContent": ["<template>\r\n  <div style=\"margin-top: 16px;\">\r\n    <el-form\r\n      ref=\"form\"\r\n      :model=\"form\"\r\n      :rules=\"rules\"\r\n      label-width=\"110px\"\r\n      style=\"width: 100%\"\r\n    >\r\n      <h3>基本信息</h3>\r\n      <el-form-item label=\"班组名称\" prop=\"Name\">\r\n        <el-input v-model=\"form.Name\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"班组长\" prop=\"Manager_UserId\">\r\n        <el-select\r\n          v-model=\"form.Manager_UserId\"\r\n          class=\"w100\"\r\n          clearable\r\n          filterable\r\n          placeholder=\"请选择\"\r\n          @change=\"managerChange\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in classList\"\r\n            :key=\"item.Id\"\r\n            :label=\"item.Display_Name\"\r\n            :value=\"item.Id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"负荷提醒线\" prop=\"Load\">\r\n        <el-input\r\n          v-model.number=\"form.Load\"\r\n          placeholder=\"请输入\"\r\n          class=\"input-number\"\r\n          type=\"number\"\r\n          min=\"0\"\r\n          @blur=\"inputBlur(form.Load)\"\r\n        >\r\n          <template slot=\"append\">吨</template>\r\n        </el-input>\r\n      </el-form-item> -->\r\n      <el-form-item label=\"班组月均负荷\" prop=\"Month_Avg_Load\">\r\n        <el-input\r\n          v-model.number=\"form.Month_Avg_Load\"\r\n          placeholder=\"请输入\"\r\n          class=\"input-number\"\r\n          type=\"number\"\r\n          min=\"0\"\r\n          @blur=\"inputBlur(form.Month_Avg_Load)\"\r\n        >\r\n          <template slot=\"append\">吨</template>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"排序号\" prop=\"Sort\">\r\n        <el-input\r\n          v-model=\"form.Sort\"\r\n          type=\"text\"\r\n          @input=\"handleNumberInput\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"是否外协\" prop=\"Is_Outsource\">\r\n        <el-radio-group v-model=\"form.Is_Outsource\">\r\n          <el-radio :label=\"true\">是</el-radio>\r\n          <el-radio :label=\"false\">否</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item label=\"是否启用\" prop=\"Is_Enabled\">\r\n        <el-radio-group v-model=\"form.Is_Enabled\">\r\n          <el-radio :label=\"true\">是</el-radio>\r\n          <el-radio :label=\"false\">否</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item label=\"关联仓库/库位\">\r\n        <el-select\r\n          ref=\"WarehouseRef\"\r\n          v-model=\"form.Warehouse_Id\"\r\n          clearable\r\n          placeholder=\"请选择仓库\"\r\n          style=\"width: 250px; margin-right: 10px;\"\r\n          @change=\"wareChange\"\r\n        >\r\n          <el-option\r\n            v-for=\"p in warehouses\"\r\n            :key=\"p.Id\"\r\n            :label=\"p.Display_Name\"\r\n            :value=\"p.Id\"\r\n          />\r\n        </el-select>\r\n        <el-select\r\n          ref=\"LocationRef\"\r\n          v-model=\"form.Location_Id\"\r\n          clearable\r\n          placeholder=\"请选择库位\"\r\n          style=\"width: 250px;\"\r\n          :disabled=\"!form.Warehouse_Id\"\r\n        >\r\n          <el-option\r\n            v-for=\"p in locations\"\r\n            :key=\"p.Id\"\r\n            :label=\"p.Display_Name\"\r\n            :value=\"p.Id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item\r\n        v-if=\"Is_Workshop_Enabled\"\r\n        label=\"所属车间\"\r\n        prop=\"Workshop_Id\"\r\n      >\r\n        <el-select\r\n          v-model=\"form.Workshop_Id\"\r\n          class=\"w100\"\r\n          clearable\r\n          filterable\r\n          placeholder=\"请选择\"\r\n          @change=\"workshopChange\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in workshopList\"\r\n            :key=\"item.Id\"\r\n            :label=\"item.Display_Name\"\r\n            :value=\"item.Id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <h3>班组成员</h3>\r\n      <div class=\"tag-x\">\r\n        <div class=\"tag-wrapper\">\r\n          <el-tag\r\n            v-for=\"tag in tags\"\r\n            :key=\"tag.Id\"\r\n            size=\"large\"\r\n            closable\r\n            type=\"info\"\r\n            @close=\"deleteTag(tag)\"\r\n          >\r\n            {{ tag.Display_Name }}\r\n          </el-tag>\r\n        </div>\r\n        <div class=\"add-btn\" @click.stop=\"handleAdd\">\r\n          <el-icon class=\"el-icon-plus\" />\r\n          <span>添加</span>\r\n        </div>\r\n      </div>\r\n    </el-form>\r\n    <footer>\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button type=\"primary\" @click=\"handleSubmit\">确 定</el-button>\r\n    </footer>\r\n    <add-user\r\n      v-if=\"showDialog\"\r\n      :tags=\"tags\"\r\n      :show.sync=\"showDialog\"\r\n      @selectList=\"getSelectList\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport AddUser from './AddUser'\r\nimport {\r\n  GetWorkingTeamInfo,\r\n  SaveWorkingTeams,\r\n  GetFactoryPeoplelist\r\n} from '@/api/PRO/technology-lib'\r\nimport { GetWorkshopPageList } from '@/api/PRO/basic-information/workshop'\r\nimport { GetLocationList, GetTotalWarehouseListOfCurFactory } from '@/api/PRO/pro-stock'\r\n\r\nexport default {\r\n  components: {\r\n    AddUser\r\n  },\r\n  data() {\r\n    return {\r\n      showDialog: false,\r\n      tags: [],\r\n      form: {\r\n        Name: '',\r\n        Manager_UserName: '',\r\n        Manager_UserId: '',\r\n        Load: '',\r\n        Workshop_Name: '', // 所属车间\r\n        Workshop_Id: '', // 所属车间Id\r\n        Month_Avg_Load: null,\r\n        Sort: '0',\r\n        Is_Outsource: false,\r\n        Is_Enabled: true,\r\n        Warehouse_Id: '',\r\n        Location_Id: ''\r\n      },\r\n      rules: {\r\n        Name: [{ required: true, message: '请输入', trigger: 'blur' }],\r\n        Sort: [{ required: true, message: '请输入', trigger: 'blur' }]\r\n      },\r\n      classList: [],\r\n      workshopList: [],\r\n      warehouses: [],\r\n      locations: [],\r\n\r\n      Is_Workshop_Enabled: ''\r\n    }\r\n  },\r\n  created() {},\r\n  mounted() {\r\n    this.getTeam()\r\n    this.getWarehouseList()\r\n  },\r\n  methods: {\r\n    // 输入框校验\r\n    inputBlur(e) {\r\n      if (e < 0) {\r\n        this.form.Load = 0\r\n      }\r\n    },\r\n    // 数据初始化\r\n    initData(id, Is_Workshop_Enabled) {\r\n      if (id) {\r\n        this.isEdit = true\r\n        GetWorkingTeamInfo({\r\n          id\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            const {\r\n              Manager_UserName,\r\n              Manager_UserId,\r\n              Load,\r\n              Name,\r\n              Users,\r\n              Id,\r\n              Workshop_Name,\r\n              Workshop_Id,\r\n              Month_Avg_Load,\r\n              Sort,\r\n              Is_Outsource,\r\n              Is_Enabled,\r\n              Warehouse_Id,\r\n              Location_Id\r\n            } = res.Data\r\n            this.form.Manager_UserName = Manager_UserName\r\n            this.form.Manager_UserId = Manager_UserId\r\n            this.form.Load = Load\r\n            this.form.Name = Name\r\n            this.isEditId = Id\r\n            this.form.Workshop_Name = Workshop_Name\r\n            this.form.Workshop_Id = Workshop_Id\r\n            this.Is_Workshop_Enabled = Is_Workshop_Enabled\r\n            this.form.Month_Avg_Load = Month_Avg_Load\r\n            this.form.Sort = Sort\r\n            this.form.Is_Outsource = Is_Outsource\r\n            this.form.Is_Enabled = Is_Enabled\r\n\r\n            if (Warehouse_Id) {\r\n              this.form.Warehouse_Id = Warehouse_Id\r\n              this.wareChange(Warehouse_Id)\r\n              this.form.Location_Id = Location_Id\r\n            }\r\n            this.tags = Users.map((v) => {\r\n              this.$set(v, 'Display_Name', v.User_Name)\r\n              this.$set(v, 'Id', v.User_Id)\r\n              return v\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      } else {\r\n        this.Is_Workshop_Enabled = Is_Workshop_Enabled\r\n      }\r\n    },\r\n\r\n    managerChange(val) {\r\n      const items = this.classList.find((i) => i.Id === val)\r\n      if (items) {\r\n        this.form.Manager_UserName = items.Display_Name\r\n        console.log(this.tags.find(v => v.Id !== items.Id))\r\n        const idx = this.tags.findIndex(v => v.Id === items.Id)\r\n        if (idx === -1) {\r\n          this.tags.push({\r\n            Display_Name: items.Display_Name,\r\n            Id: items.Id,\r\n            User_Id: items.Id,\r\n            User_Name: items.Display_Name\r\n          })\r\n        }\r\n      }\r\n    },\r\n    workshopChange(val) {\r\n      this.form.Workshop_Name = this.workshopList.find(\r\n        (i) => i.Id === val\r\n      ).Display_Name\r\n    },\r\n    getTeam() {\r\n      GetFactoryPeoplelist().then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.classList = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      GetWorkshopPageList({ Page: -1 }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.workshopList = res.Data.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleAdd() {\r\n      this.showDialog = true\r\n    },\r\n    getSelectList(list) {\r\n      const objKey = {}\r\n      this.tags = [...this.tags, ...list].reduce((acc, cur) => {\r\n        objKey[cur.Id] ? '' : (objKey[cur.Id] = true && acc.push(cur))\r\n        return acc\r\n      }, [])\r\n    },\r\n    deleteTag(item) {\r\n      const index = this.tags.findIndex((v) => v.Id === item.Id)\r\n      index !== -1 && this.tags.splice(index, 1)\r\n    },\r\n    handleSubmit() {\r\n      // return\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.Warehouse_Id && !this.form.Location_Id) {\r\n            this.$message({\r\n              message: '请选择关联仓库的库位',\r\n              type: 'error'\r\n            })\r\n            return false\r\n          }\r\n          const subObj = {\r\n            Name: this.form.Name,\r\n            Manager_UserName: this.form.Manager_UserName,\r\n            Manager_UserId: this.form.Manager_UserId,\r\n            Load: this.form.Load,\r\n            Members: this.tags.map((v) => v.Id),\r\n            Workshop_Id: this.form.Workshop_Id,\r\n            Month_Avg_Load: this.form.Month_Avg_Load,\r\n            Sort: this.form.Sort,\r\n            Is_Outsource: this.form.Is_Outsource,\r\n            Is_Enabled: this.form.Is_Enabled,\r\n            Warehouse_Id: this.form.Warehouse_Id,\r\n            Location_Id: this.form.Location_Id\r\n          }\r\n          this.isEdit && (subObj.Id = this.isEditId)\r\n          SaveWorkingTeams(subObj).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                message: '操作成功',\r\n                type: 'success'\r\n              })\r\n              this.$emit('close')\r\n              this.$emit('refresh')\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    getWarehouseList() {\r\n      GetTotalWarehouseListOfCurFactory({ }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.warehouses = res.Data\r\n        }\r\n      })\r\n    },\r\n    wareChange(v) {\r\n      this.form.Location_Id = ''\r\n      GetLocationList({\r\n        Warehouse_Id: v\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.locations = res.Data\r\n        }\r\n      })\r\n    },\r\n    handleNumberInput(value) {\r\n      // 移除所有非数字字符\r\n      let cleaned = value.replace(/[^\\d]/g, '')\r\n\r\n      // 处理前导零：如果是 0 开头且有后续数字，移除前导零\r\n      if (cleaned.length > 1 && cleaned.startsWith('0')) {\r\n        cleaned = cleaned.replace(/^0+/, '')\r\n        // 如果全部是0，保留一个0\r\n        if (cleaned === '') cleaned = '0'\r\n      }\r\n\r\n      // 更新值\r\n      this.form.Sort = cleaned\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"~@/styles/mixin.scss\";\r\n\r\nh3 {\r\n  color: #298dff;\r\n}\r\n\r\n.tag-x {\r\n  text-align: left;\r\n\r\n  .tag-wrapper {\r\n    display: inline-block;\r\n    flex-wrap: wrap;\r\n    max-height: 160px;\r\n    overflow: auto;\r\n    @include scrollBar;\r\n\r\n    .el-tag {\r\n      margin: 8px 0 0 8px;\r\n    }\r\n  }\r\n\r\n  .add-btn {\r\n    margin-top: 12px;\r\n    text-align: center;\r\n    cursor: pointer;\r\n    height: 32px;\r\n    line-height: 32px;\r\n    background: rgba(41, 141, 255, 0.03);\r\n    color: #298dff;\r\n    border: 1px dashed rgba(41, 141, 255, 0.32156862745098036);\r\n    border-radius: 4px;\r\n  }\r\n}\r\n\r\nfooter {\r\n  margin: 20px;\r\n  text-align: right;\r\n\r\n  &:first-child {\r\n    margin-right: 20px;\r\n  }\r\n}\r\n\r\n::v-deep {\r\n  .input-number {\r\n    input {\r\n      padding-right: 2px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
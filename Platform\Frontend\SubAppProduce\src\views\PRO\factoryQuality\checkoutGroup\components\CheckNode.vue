<template>
  <div style="height: calc(100vh - 300px)">
    <vxe-table
      v-loading="tbLoading"
      :empty-render="{name: 'NotData'}"
      show-header-overflow
      element-loading-spinner="el-icon-loading"
      element-loading-text="拼命加载中"
      empty-text="暂无数据"
      height="100%"
      :data="tbData"
      stripe
      resizable
      :auto-resize="true"
      class="cs-vxe-table"
      :tooltip-config="{ enterable: true }"
    >
      <!-- <vxe-column fixed="left" type="checkbox" width="60" /> -->
      <vxe-column
        v-for="(item, index) in columns"
        :key="index"
        :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
        show-overflow="tooltip"
        sortable
        :align="item.Align"
        :field="item.Code"
        :title="item.Display_Name"
      >
        <template #default="{ row }">
          <span v-if="item.Code === 'Is_Special_Check'">
            <el-tag v-if="row.Is_Special_Check" type="success">是</el-tag><el-tag v-else type="danger">否</el-tag>
          </span>
          <span v-else-if="item.Code === 'Is_Inter_Check'">
            <el-tag v-if="row.Is_Inter_Check" type="success">是</el-tag><el-tag v-else type="danger">否</el-tag>
          </span>
          <span v-else-if="item.Code === 'Is_Self_Check'">
            <el-tag v-if="row.Is_Self_Check" type="success">是</el-tag><el-tag v-else type="danger">否</el-tag>
          </span>
          <span v-else>{{ row[item.Code] | displayValue }}</span>
        </template>
      </vxe-column>
      <vxe-column fixed="right" title="操作" width="200" show-overflow align="center">
        <template #default="{ row }">
          <el-button v-if="!row.Node_Code||(row.Node_Code&&row.Check_Style === '抽检')" type="text" @click="editEvent(row)">编辑</el-button>
          <el-divider v-if="!row.Node_Code" direction="vertical" />
          <el-button v-if="!row.Node_Code" type="text" @click="removeEvent(row)">删除</el-button>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<script>
import { GetGridByCode } from '@/api/sys'
import { DelNode } from '@/api/PRO/factorycheck'
import { GetNodeList } from '@/api/PRO/factorycheck'
import { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'
import { timeFormat } from '@/filters'
export default {
  props: {
    checkType: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tbData: [],
      columns: [],
      tbLoading: false
    }
  },
  watch: {
    checkType: {
      handler(newName, oldName) {
        this.checkType = newName
        this.getNodeList()
      },
      deep: true
    }
  },
  mounted() {
    // this.getNodeList()
    this.getTypeList()
  },
  methods: {
    async getTypeList() {
      const res = await GetFactoryProfessionalByCode({
        factoryId: localStorage.getItem('CurReferenceId')
      })
      const data = res.Data
      if (res.IsSucceed) {
        this.typeOption = Object.freeze(data)
        console.log(this.typeOption)
        if (this.typeOption.length > 0) {
          this.TypeId = this.typeOption[0]?.Id
          this.fetchData()
        }
      } else {
        this.$message({
          message: res.Message,
          type: 'error'
        })
      }
    },
    fetchData() {
      this.getTableConfig('Quality_Inspection_Node')
      //   this.tbLoading = true;
    },
    getNodeList() {
      this.tbLoading = true
      GetNodeList({ check_object_id: this.checkType.Id, Bom_Level: this.checkType.Code }).then((res) => {
        if (res.IsSucceed) {
          this.tbData = res.Data.map(v => {
            switch (v.Check_Style) {
              case 0 : v.Check_Style = '抽检'; break // 谁写的，坑死了
              case 1 : v.Check_Style = '全检'; break
              default: v.Check_Style = ''
            }
            switch (v.Check_Type) {
              case 1 : v.Check_Type = '质量'; break
              case 2 : v.Check_Type = '探伤'; break
              case -1 : v.Check_Type = '质量/探伤'; break
              default: v.Check_Type = ''
            }
            v.Create_Date = timeFormat(v.Create_Date, '{y}-{m}-{d} {h}:{i}:{s}')
            return v
          })
          console.log(res.Data)
          this.tbLoading = false
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
          this.tbLoading = false
        }
      })
    },
    getTableConfig(code) {
      GetGridByCode({ code: code + ',' + this.typeOption.find((i) => i.Id === this.TypeId).Code }).then((res) => {
        const { IsSucceed, Data, Message } = res
        if (IsSucceed) {
          if (!Data) {
            this.$message.error('当前专业没有配置相对应表格')
            this.tbLoading = true
            return
          }
          const list = Data.ColumnList || []
          this.columns = list
          console.log(this.columns)
          this.tbLoading = false
        } else {
          this.$message({
            message: Message,
            type: 'error'
          })
        }
      })
    },
    // 删除单个检查项组合
    removeEvent(row) {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          DelNode({ id: row.Id }).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getNodeList()
            } else {
              this.$message({
                type: 'error',
                message: res.Message
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },

    // 编辑每行信息
    editEvent(row) {
      // 获取每行内容
      console.log('row', row)
      this.$emit('NodeEdit', row)
    }
  }
}
</script>

<style scoped></style>

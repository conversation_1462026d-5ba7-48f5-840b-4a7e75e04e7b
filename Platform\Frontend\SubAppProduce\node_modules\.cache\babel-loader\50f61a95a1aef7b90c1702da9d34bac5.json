{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckCombination.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckCombination.vue", "mtime": 1758086053963}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetGridByCode", "GetFactoryProfessionalByCode", "QualityList", "DelQualityList", "timeFormat", "props", "checkType", "type", "Object", "default", "sysProjectId", "String", "data", "columns", "tbLoading", "TypeId", "typeOption", "tbData", "watch", "handler", "newName", "old<PERSON>ame", "getQualityList", "deep", "newVal", "Id", "immediate", "mounted", "getTypeList", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "_this$typeOption$", "wrap", "_callee$", "_context", "prev", "next", "factoryId", "localStorage", "getItem", "sent", "Data", "IsSucceed", "freeze", "console", "log", "length", "fetchData", "$message", "message", "Message", "stop", "_this2", "_callee2", "_callee2$", "_context2", "getTableConfig", "code", "_this3", "find", "i", "Code", "then", "error", "list", "ColumnList", "filter", "v", "Is_Display", "map", "item", "fixed", "_this4", "check_object_id", "Bom_Level", "Check_Type", "Create_Date", "removeEvent", "row", "_this5", "$confirm", "confirmButtonText", "cancelButtonText", "id", "catch", "editEvent", "$emit"], "sources": ["src/views/PRO/project-config/project-quality/components/CheckCombination.vue"], "sourcesContent": ["<template>\n  <div style=\"height: calc(100vh - 300px)\">\n    <vxe-table\n      v-loading=\"tbLoading\"\n      :empty-render=\"{name: 'NotData'}\"\n      show-header-overflow\n      element-loading-spinner=\"el-icon-loading\"\n      element-loading-text=\"拼命加载中\"\n      empty-text=\"暂无数据\"\n      height=\"100%\"\n      :data=\"tbData\"\n      stripe\n      resizable\n      :auto-resize=\"true\"\n      class=\"cs-vxe-table\"\n      :tooltip-config=\"{ enterable: true }\"\n    >\n      <!-- <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" /> -->\n      <vxe-column\n        v-for=\"(item, index) in columns\"\n        :key=\"index\"\n        :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n        show-overflow=\"tooltip\"\n        sortable\n        :align=\"item.Align\"\n        :field=\"item.Code\"\n        :title=\"item.Display_Name\"\n      >\n        <template #default=\"{ row }\">\n          <span>{{ row[item.Code] || '-' }}</span>\n        </template>\n      </vxe-column>\n      <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" align=\"center\" show-overflow>\n        <template #default=\"{ row }\">\n          <el-button type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\n          <el-divider direction=\"vertical\" />\n          <el-button type=\"text\" @click=\"removeEvent(row)\">删除</el-button>\n        </template>\n      </vxe-column>\n    </vxe-table>\n  </div>\n</template>\n\n<script>\nimport { GetGridByCode } from '@/api/sys'\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\nimport { QualityList } from '@/api/PRO/factorycheck'\nimport { DelQualityList } from '@/api/PRO/factorycheck'\nimport { timeFormat } from '@/filters'\nexport default {\n  props: {\n    checkType: {\n      type: Object,\n      default: () => ({})\n    },\n    sysProjectId: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      columns: null,\n      tbLoading: false,\n      TypeId: '',\n      typeOption: '',\n      tbData: []\n\n    }\n  },\n  watch: {\n    checkType: {\n      handler(newName, oldName) {\n        this.checkType = newName\n        if (this.sysProjectId) {\n          this.getQualityList()\n        }\n      },\n      deep: true\n    },\n    sysProjectId: {\n      handler(newVal) {\n        if (newVal && this.checkType && this.checkType.Id) {\n          this.getQualityList()\n        }\n      },\n      immediate: true\n    }\n  },\n  mounted() {\n    // this.getQualityList()\n    this.getTypeList()\n  },\n  methods: {\n    async getTypeList() {\n      let res = null\n      let data = null\n      res = await GetFactoryProfessionalByCode({\n        factoryId: localStorage.getItem('CurReferenceId')\n      })\n      data = res.Data\n      if (res.IsSucceed) {\n        this.typeOption = Object.freeze(data)\n        console.log(this.typeOption)\n        if (this.typeOption.length > 0) {\n          this.TypeId = this.typeOption[0]?.Id\n          this.fetchData()\n        }\n      } else {\n        this.$message({\n          message: res.Message,\n          type: 'error'\n        })\n      }\n    },\n    async fetchData() {\n      await this.getTableConfig('Check_item_combination')\n    //   this.tbLoading = true;\n    },\n    // 获取列表\n    getTableConfig(code) {\n      GetGridByCode({ code: code + ',' + this.typeOption.find((i) => i.Id === this.TypeId).Code }).then((res) => {\n        const { IsSucceed, Data, Message } = res\n        if (IsSucceed) {\n          if (!Data) {\n            this.$message.error('当前专业没有配置相对应表格')\n            this.tbLoading = true\n            return\n          }\n          this.tbLoading = false\n          const list = Data.ColumnList || []\n          this.columns = list.filter((v) => v.Is_Display).map((item) => {\n            if (item.Code === 'CheckName') {\n              item.fixed = 'left'\n            }\n            return item\n          })\n          console.log(this.columns)\n        } else {\n          this.$message({\n            message: Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    // 获取检查项组合列表\n    getQualityList() {\n      this.tbLoading = true\n      QualityList({ check_object_id: this.checkType.Id, Bom_Level: this.checkType.Code, sysProjectId: this.sysProjectId }).then((res) => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data.map((v) => {\n            switch (v.Check_Type) {\n              case 1 : v.Check_Type = '质量'; break\n              case 2 : v.Check_Type = '探伤'; break\n              case -1 : v.Check_Type = '质量、探伤'; break\n              default: v.Check_Type = ''\n            }\n            v.Create_Date = timeFormat(v.Create_Date, '{y}-{m}-{d} {h}:{i}:{s}')\n            return v\n          })\n          this.tbLoading = false\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n          this.tbLoading = false\n        }\n      })\n    },\n    // 删除单个检查项组合\n    removeEvent(row) {\n      console.log(row)\n      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DelQualityList({ id: row.Id }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getQualityList()\n            } else {\n              this.$message({\n                type: 'error',\n                message: res.Message\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    },\n    // 编辑每行信息\n    editEvent(row) {\n      // 获取每行内容\n      console.log('row', row)\n      this.$emit('CombinationEdit', row)\n    }\n  }\n}\n</script>\n\n<style scoped></style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,SAAAA,aAAA;AACA,SAAAC,4BAAA;AACA,SAAAC,WAAA;AACA,SAAAC,cAAA;AACA,SAAAC,UAAA;AACA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAC,YAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;MACAC,MAAA;MACAC,UAAA;MACAC,MAAA;IAEA;EACA;EACAC,KAAA;IACAZ,SAAA;MACAa,OAAA,WAAAA,QAAAC,OAAA,EAAAC,OAAA;QACA,KAAAf,SAAA,GAAAc,OAAA;QACA,SAAAV,YAAA;UACA,KAAAY,cAAA;QACA;MACA;MACAC,IAAA;IACA;IACAb,YAAA;MACAS,OAAA,WAAAA,QAAAK,MAAA;QACA,IAAAA,MAAA,SAAAlB,SAAA,SAAAA,SAAA,CAAAmB,EAAA;UACA,KAAAH,cAAA;QACA;MACA;MACAI,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACAD,WAAA,WAAAA,YAAA;MAAA,IAAAE,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA,EAAAvB,IAAA,EAAAwB,iBAAA;QAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAN,GAAA;cACAvB,IAAA;cAAA2B,QAAA,CAAAE,IAAA;cAAA,OACAxC,4BAAA;gBACAyC,SAAA,EAAAC,YAAA,CAAAC,OAAA;cACA;YAAA;cAFAT,GAAA,GAAAI,QAAA,CAAAM,IAAA;cAGAjC,IAAA,GAAAuB,GAAA,CAAAW,IAAA;cACA,IAAAX,GAAA,CAAAY,SAAA;gBACAjB,KAAA,CAAAd,UAAA,GAAAR,MAAA,CAAAwC,MAAA,CAAApC,IAAA;gBACAqC,OAAA,CAAAC,GAAA,CAAApB,KAAA,CAAAd,UAAA;gBACA,IAAAc,KAAA,CAAAd,UAAA,CAAAmC,MAAA;kBACArB,KAAA,CAAAf,MAAA,IAAAqB,iBAAA,GAAAN,KAAA,CAAAd,UAAA,iBAAAoB,iBAAA,uBAAAA,iBAAA,CAAAX,EAAA;kBACAK,KAAA,CAAAsB,SAAA;gBACA;cACA;gBACAtB,KAAA,CAAAuB,QAAA;kBACAC,OAAA,EAAAnB,GAAA,CAAAoB,OAAA;kBACAhD,IAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAgC,QAAA,CAAAiB,IAAA;UAAA;QAAA,GAAAtB,OAAA;MAAA;IACA;IACAkB,SAAA,WAAAA,UAAA;MAAA,IAAAK,MAAA;MAAA,OAAA1B,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAyB,SAAA;QAAA,OAAA1B,mBAAA,GAAAK,IAAA,UAAAsB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApB,IAAA,GAAAoB,SAAA,CAAAnB,IAAA;YAAA;cAAAmB,SAAA,CAAAnB,IAAA;cAAA,OACAgB,MAAA,CAAAI,cAAA;YAAA;YAAA;cAAA,OAAAD,SAAA,CAAAJ,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IAEA;IACA;IACAG,cAAA,WAAAA,eAAAC,IAAA;MAAA,IAAAC,MAAA;MACA/D,aAAA;QAAA8D,IAAA,EAAAA,IAAA,cAAA9C,UAAA,CAAAgD,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAxC,EAAA,KAAAsC,MAAA,CAAAhD,MAAA;QAAA,GAAAmD;MAAA,GAAAC,IAAA,WAAAhC,GAAA;QACA,IAAAY,SAAA,GAAAZ,GAAA,CAAAY,SAAA;UAAAD,IAAA,GAAAX,GAAA,CAAAW,IAAA;UAAAS,OAAA,GAAApB,GAAA,CAAAoB,OAAA;QACA,IAAAR,SAAA;UACA,KAAAD,IAAA;YACAiB,MAAA,CAAAV,QAAA,CAAAe,KAAA;YACAL,MAAA,CAAAjD,SAAA;YACA;UACA;UACAiD,MAAA,CAAAjD,SAAA;UACA,IAAAuD,IAAA,GAAAvB,IAAA,CAAAwB,UAAA;UACAP,MAAA,CAAAlD,OAAA,GAAAwD,IAAA,CAAAE,MAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAC,UAAA;UAAA,GAAAC,GAAA,WAAAC,IAAA;YACA,IAAAA,IAAA,CAAAT,IAAA;cACAS,IAAA,CAAAC,KAAA;YACA;YACA,OAAAD,IAAA;UACA;UACA1B,OAAA,CAAAC,GAAA,CAAAa,MAAA,CAAAlD,OAAA;QACA;UACAkD,MAAA,CAAAV,QAAA;YACAC,OAAA,EAAAC,OAAA;YACAhD,IAAA;UACA;QACA;MACA;IACA;IACA;IACAe,cAAA,WAAAA,eAAA;MAAA,IAAAuD,MAAA;MACA,KAAA/D,SAAA;MACAZ,WAAA;QAAA4E,eAAA,OAAAxE,SAAA,CAAAmB,EAAA;QAAAsD,SAAA,OAAAzE,SAAA,CAAA4D,IAAA;QAAAxD,YAAA,OAAAA;MAAA,GAAAyD,IAAA,WAAAhC,GAAA;QACA,IAAAA,GAAA,CAAAY,SAAA;UACA8B,MAAA,CAAA5D,MAAA,GAAAkB,GAAA,CAAAW,IAAA,CAAA4B,GAAA,WAAAF,CAAA;YACA,QAAAA,CAAA,CAAAQ,UAAA;cACA;gBAAAR,CAAA,CAAAQ,UAAA;gBAAA;cACA;gBAAAR,CAAA,CAAAQ,UAAA;gBAAA;cACA;gBAAAR,CAAA,CAAAQ,UAAA;gBAAA;cACA;gBAAAR,CAAA,CAAAQ,UAAA;YACA;YACAR,CAAA,CAAAS,WAAA,GAAA7E,UAAA,CAAAoE,CAAA,CAAAS,WAAA;YACA,OAAAT,CAAA;UACA;UACAK,MAAA,CAAA/D,SAAA;QACA;UACA+D,MAAA,CAAAxB,QAAA;YACA9C,IAAA;YACA+C,OAAA,EAAAnB,GAAA,CAAAoB;UACA;UACAsB,MAAA,CAAA/D,SAAA;QACA;MACA;IACA;IACA;IACAoE,WAAA,WAAAA,YAAAC,GAAA;MAAA,IAAAC,MAAA;MACAnC,OAAA,CAAAC,GAAA,CAAAiC,GAAA;MACA,KAAAE,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAhF,IAAA;MACA,GACA4D,IAAA;QACAhE,cAAA;UAAAqF,EAAA,EAAAL,GAAA,CAAA1D;QAAA,GAAA0C,IAAA,WAAAhC,GAAA;UACA,IAAAA,GAAA,CAAAY,SAAA;YACAqC,MAAA,CAAA/B,QAAA;cACA9C,IAAA;cACA+C,OAAA;YACA;YACA8B,MAAA,CAAA9D,cAAA;UACA;YACA8D,MAAA,CAAA/B,QAAA;cACA9C,IAAA;cACA+C,OAAA,EAAAnB,GAAA,CAAAoB;YACA;UACA;QACA;MACA,GACAkC,KAAA;QACAL,MAAA,CAAA/B,QAAA;UACA9C,IAAA;UACA+C,OAAA;QACA;MACA;IACA;IACA;IACAoC,SAAA,WAAAA,UAAAP,GAAA;MACA;MACAlC,OAAA,CAAAC,GAAA,QAAAiC,GAAA;MACA,KAAAQ,KAAA,oBAAAR,GAAA;IACA;EACA;AACA", "ignoreList": []}]}
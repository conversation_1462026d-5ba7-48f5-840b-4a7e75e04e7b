{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\CheckItem.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\CheckItem.vue", "mtime": 1757468112635}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["CheckItem.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CheckItem.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup/components", "sourcesContent": ["<template>\r\n  <div style=\"height: calc(100vh - 300px)\">\r\n    <vxe-table\r\n      v-loading=\"tbLoading\"\r\n      :empty-render=\"{name: 'NotData'}\"\r\n      show-header-overflow\r\n      element-loading-spinner=\"el-icon-loading\"\r\n      element-loading-text=\"拼命加载中\"\r\n      empty-text=\"暂无数据\"\r\n      height=\"100%\"\r\n      align=\"left\"\r\n      stripe\r\n      :data=\"tbData\"\r\n      resizable\r\n      :auto-resize=\"true\"\r\n      class=\"cs-vxe-table\"\r\n      :tooltip-config=\"{ enterable: true }\"\r\n    >\r\n      <vxe-column\r\n        show-overflow=\"tooltip\"\r\n        sortable\r\n        field=\"Check_Content\"\r\n        title=\"检查项内容\"\r\n        width=\"calc(100vh-200px)/2\"\r\n      />\r\n      <vxe-column\r\n        show-overflow=\"tooltip\"\r\n        sortable\r\n        field=\"Eligibility_Criteria\"\r\n        title=\"合格标准\"\r\n        width=\"calc(100vh-200px)/2\"\r\n      />\r\n      <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" align=\"center\" show-overflow>\r\n        <template #default=\"{ row }\">\r\n          <el-button type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\r\n          <el-divider direction=\"vertical\" />\r\n          <el-button type=\"text\" @click=\"removeEvent(row)\">删除</el-button>\r\n        </template>\r\n      </vxe-column>\r\n    </vxe-table>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetCheckItemList } from '@/api/PRO/factorycheck'\r\nimport { DeleteCheckItem } from '@/api/PRO/factorycheck'\r\n\r\nexport default {\r\n  props: {\r\n    checkType: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      tbLoading: false,\r\n      tbData: []\r\n    }\r\n  },\r\n  watch: {\r\n    checkType: {\r\n      handler(newName, oldName) {\r\n        this.checkType = newName\r\n        this.getCheckItemList()\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getCheckItemList()\r\n  },\r\n  methods: {\r\n    getCheckItemList() {\r\n      this.tbLoading = true\r\n      GetCheckItemList({ check_object_id: this.checkType.Id, Bom_Level: this.checkType.Code }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data\r\n          this.tbLoading = false\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n          this.tbLoading = false\r\n        }\r\n      })\r\n    },\r\n    removeEvent(row) {\r\n      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          DeleteCheckItem({ id: row.Id }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                type: 'success',\r\n                message: '删除成功!'\r\n              })\r\n              this.getCheckItemList()\r\n            } else {\r\n              this.$message({\r\n                type: 'error',\r\n                message: res.Message\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n    editEvent(row) {\r\n      // 获取每行内容\r\n      console.log('row', row)\r\n      this.$emit('ItemEdit', row)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\actually-sent\\v4\\component\\Add.vue?vue&type=template&id=27ec9428&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\actually-sent\\v4\\component\\Add.vue", "mtime": 1757468128055}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}
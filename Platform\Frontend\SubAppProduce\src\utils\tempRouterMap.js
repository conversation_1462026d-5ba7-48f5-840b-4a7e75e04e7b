// src/utils/tempRouterMap.js
import { name } from '../../package.json'
import { handleAddRouterPage } from '@/utils'

const otherAppTempRouter = [
  // {
  //   path: 'unit-template-setting',
  //   hidden: true,
  //   name: 'SYSUnitPartTemp',
  //   platform: 'produce',
  //   meta: { title: '专用模板配置' }
  // }
]
const thisAppTempRouter = [
  {
    path: 'unit-template-setting',
    hidden: true,
    component: () => import('@/views/sys/professional-category/unitPartTemp.vue'),
    name: 'SYSUnitPartTemp',
    meta: { title: '专用模板配置' }
  },
  {
    path: 'template-setting',
    hidden: true,
    component: () => import('@/views/sys/professional-category/templateSetting'),
    name: 'TemplateSetting',
    meta: { title: '专用模板配置' }
  }
]

export function transferRoutesWithParent() {
  return otherAppTempRouter.map(item => ({
    ...item,
    parentName: null,
    parentPath: null
  }))
}

export function getRoutesWithParent() {
  handleAddRouterPage(thisAppTempRouter, '', '')
  return thisAppTempRouter.map(item => ({
    ...item,
    parentName: null,
    parentPath: null,
    platform: name
  }))
}


{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\index.vue", "mtime": 1757920277150}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBQcm9qZWN0RGF0YSBmcm9tICcuLi9jb21wb25lbnRzL1Byb2plY3REYXRhLnZ1ZScKaW1wb3J0IFRvcEhlYWRlciBmcm9tICdAL2NvbXBvbmVudHMvVG9wSGVhZGVyJwppbXBvcnQgQWRkIGZyb20gJy4vY29tcG9uZW50L0FkZCcKaW1wb3J0IFpDbGFzcyBmcm9tICcuL2NvbXBvbmVudC9Hcm91cCcKaW1wb3J0IFByb2plY3RBZGQgZnJvbSAnLi9jb21wb25lbnQvUHJvamVjdEFkZERpYWxvZy52dWUnCmltcG9ydCBBc3NvY2lhdGVkRGV2aWNlIGZyb20gJy4vY29tcG9uZW50L0Fzc29jaWF0ZWREZXZpY2UnCmltcG9ydCB7IEdldFByb2Nlc3NMaXN0QmFzZSwgRGVsZXRlUHJvY2VzcywgUmVzdG9yZUZhY3RvcnlQcm9jZXNzRnJvbVByb2plY3QgfSBmcm9tICdAL2FwaS9QUk8vdGVjaG5vbG9neS1saWInCmltcG9ydCBFbFRhYmxlRW1wdHkgZnJvbSAnQC9jb21wb25lbnRzL0VsVGFibGVFbXB0eS9pbmRleC52dWUnCmltcG9ydCBhZGRSb3V0ZXJQYWdlIGZyb20gJ0AvbWl4aW5zL2FkZC1yb3V0ZXItcGFnZScKaW1wb3J0IER5bmFtaWNUYWJsZUZpZWxkcyBmcm9tICdAL2NvbXBvbmVudHMvRHluYW1pY1RhYmxlRmllbGRzL2luZGV4LnZ1ZScKaW1wb3J0IHsgR2V0Qk9NSW5mbyB9IGZyb20gJ0Avdmlld3MvUFJPL2JvbS1zZXR0aW5nL3V0aWxzJwppbXBvcnQgZ2V0VGJJbmZvIGZyb20gJ0AvbWl4aW5zL1BSTy9nZXQtdGFibGUtaW5mbycKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnUFJPUHJvY2Vzc01hbmFnZW1lbnQnLAogIGNvbXBvbmVudHM6IHsKICAgIFByb2plY3REYXRhLAogICAgRWxUYWJsZUVtcHR5LAogICAgVG9wSGVhZGVyLAogICAgQWRkLAogICAgWkNsYXNzLAogICAgQXNzb2NpYXRlZERldmljZSwKICAgIER5bmFtaWNUYWJsZUZpZWxkcywKICAgIFByb2plY3RBZGQKICB9LAogIG1peGluczogW2FkZFJvdXRlclBhZ2UsIGdldFRiSW5mb10sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHRiTG9hZGluZzogZmFsc2UsCiAgICAgIGxldmVsOiAwLAogICAgICBib21MaXN0OiBbXSwKICAgICAgYWRkUGFnZUFycmF5OiBbCiAgICAgICAgewogICAgICAgICAgcGF0aDogJy9Bc3NvY2lhdGVkRGV2aWNlJywKICAgICAgICAgIGhpZGRlbjogdHJ1ZSwKICAgICAgICAgIGNvbXBvbmVudDogKCkgPT4gaW1wb3J0KCdAL3ZpZXdzL1BSTy9wcm9jZXNzLXNldHRpbmdzL21hbmFnZW1lbnQvY29tcG9uZW50L0Fzc29jaWF0ZWREZXZpY2UudnVlJyksCiAgICAgICAgICBuYW1lOiAnQXNzb2NpYXRlZERldmljZScsCiAgICAgICAgICBtZXRhOiB7IHRpdGxlOiAn5YWz6IGU6K6+5aSHJyB9CiAgICAgICAgfQoKICAgICAgXSwKICAgICAgY29sdW1uczogW10sCiAgICAgIHRiRGF0YTogW10sCiAgICAgIGN1cnJlbnRDb21wb25lbnQ6ICcnLAogICAgICB0aXRsZTogJycsCiAgICAgIGNvbU5hbWU6ICcnLAogICAgICBwYXJ0TmFtZTogJycsCiAgICAgIHJvd0luZm86IG51bGwsCiAgICAgIHJvd0RhdGE6IHt9LAogICAgICB0eXBlOiAnJywKICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGRpYWxvZ1Zpc2libGUxOiBmYWxzZSwKICAgICAgZm9ybUlubGluZTogeyBuYW1lOiAnJywgY29kZTogJycgfSwKICAgICAgc3lzUHJvamVjdElkOiAnJywKICAgICAgdG90YWxXb3JrbG9hZFByb3BvcnRpb246IDAsCiAgICAgIHRiS2V5OiAxMDAsCiAgICAgIGdyaWRDb2RlOiAncHJvY2Vzc1NldHRpbmdzTGlzdCcKICAgIH0KICB9LAogIGFzeW5jIGNyZWF0ZWQoKSB7CiAgICBhd2FpdCB0aGlzLmdldFRhYmxlSW5mbygpCiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5nZXRCT01JbmZvKCkKICB9LAogIG1ldGhvZHM6IHsKICAgIGFzeW5jIGdldFRhYmxlSW5mbygpIHsKICAgICAgYXdhaXQgdGhpcy5nZXRUYWJsZUNvbmZpZyh0aGlzLmdyaWRDb2RlKQogICAgfSwKICAgIGFzeW5jIGdldEJPTUluZm8oKSB7CiAgICAgIGNvbnN0IHsgY29tTmFtZSwgcGFydE5hbWUsIGxpc3QgfSA9IGF3YWl0IEdldEJPTUluZm8oKQogICAgICB0aGlzLmNvbU5hbWUgPSBjb21OYW1lCiAgICAgIHRoaXMucGFydE5hbWUgPSBwYXJ0TmFtZQogICAgICB0aGlzLmJvbUxpc3QgPSBsaXN0CiAgICB9LAogICAgaGFuZGxlT3BlbkRldmljZShyb3cpIHsKICAgICAgdGhpcy5yb3dEYXRhID0gcm93CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZTEgPSB0cnVlCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICB0aGlzLiRyZWZzLkRldmljZS5jbGVhclNlbGVjKCkKICAgICAgfSkKICAgICAgLy8gIHRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogJ2h0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9wcm9kdWNlL3Byby9uZXN0aW5nL2luZGV4JyB9KQogICAgfSwKCiAgICBmZXRjaERhdGEoKSB7CiAgICAgIHRoaXMudGJMb2FkaW5nID0gdHJ1ZQogICAgICBHZXRQcm9jZXNzTGlzdEJhc2UoeyAuLi50aGlzLmZvcm1JbmxpbmUsIHN5c1Byb2plY3RJZDogdGhpcy5zeXNQcm9qZWN0SWQgfSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIHRoaXMudGJEYXRhID0gcmVzLkRhdGEKCiAgICAgICAgICAvLyDorqHnrpfmiYDmnIkgV29ya2xvYWRfUHJvcG9ydGlvbiDnmoTmgLvlkowKICAgICAgICAgIHRoaXMudG90YWxXb3JrbG9hZFByb3BvcnRpb24gPSByZXMuRGF0YS5yZWR1Y2UoKHRvdGFsLCBpdGVtKSA9PiB7CiAgICAgICAgICAgIGNvbnN0IHByb3BvcnRpb24gPSBwYXJzZUZsb2F0KGl0ZW0uV29ya2xvYWRfUHJvcG9ydGlvbikgfHwgMAogICAgICAgICAgICByZXR1cm4gdG90YWwgKyBwcm9wb3J0aW9uCiAgICAgICAgICB9LCAwKQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICAgIHRoaXMudGJMb2FkaW5nID0gZmFsc2UKICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUxID0gZmFsc2UKICAgICAgfSkKICAgIH0sCiAgICBoYW5kbGVDbG9zZSgpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UKICAgIH0sCiAgICBoYW5kbGVDbG9zZTEoKSB7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZTEgPSBmYWxzZQogICAgfSwKICAgIGhhbmRsZUFkZFByb2plY3QoKSB7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUKICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ1Byb2plY3RBZGQnCiAgICAgIHRoaXMudGl0bGUgPSAn5ZCM5q2l6aG555uu6YWN572uJwogICAgfSwKICAgIGhhbmRsZURpYWxvZyh0eXBlLCByb3cpIHsKICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ0FkZCcKICAgICAgdGhpcy50eXBlID0gdHlwZQogICAgICBpZiAodHlwZSA9PT0gJ2FkZCcpIHsKICAgICAgICB0aGlzLnRpdGxlID0gJ+aWsOW7uicKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnRpdGxlID0gJ+e8lui+kScKICAgICAgICB0aGlzLnJvd0luZm8gPSByb3cKICAgICAgfQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlCiAgICB9LAogICAgaGFuZGxlTWFuYWdlKHJvdykgewogICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSAnWkNsYXNzJwogICAgICB0aGlzLnRpdGxlID0gJ+ePree7hOeuoeeQhicKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQogICAgICB0aGlzLiRuZXh0VGljaygoXykgPT4gewogICAgICAgIHRoaXMuJHJlZnMuY29udGVudC5pbml0KHJvdykKICAgICAgfSkKICAgIH0sCiAgICAvLyDku47lt6XljoLnuqflkIzmraUKICAgIGhhbmRsZUFkZEZhY3RvcnkoKSB7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+atpOaTjeS9nOWwhuS8muaBouWkjeWIsOW3peWOguW3peW6jywg5piv5ZCm57un57utPycsICfmj5DnpLonLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLnJlc3RvcmVGYWN0b3J5UHJvY2Vzc0Zyb21Qcm9qZWN0KCkKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ2luZm8nLAogICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iOaBouWkjScKICAgICAgICB9KQogICAgICB9KQogICAgfSwKICAgIHJlc3RvcmVGYWN0b3J5UHJvY2Vzc0Zyb21Qcm9qZWN0KCkgewogICAgICBSZXN0b3JlRmFjdG9yeVByb2Nlc3NGcm9tUHJvamVjdCh7CiAgICAgICAgU3lzX1Byb2plY3RfSWQ6IHRoaXMuc3lzUHJvamVjdElkCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogJ+aBouWkjeaIkOWKnycsCiAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJwogICAgICAgICAgfSkKICAgICAgICAgIHRoaXMuZmV0Y2hEYXRhKCkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICBoYW5kbGVEZWxldGUocHJvY2Vzc0lkKSB7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuWIoOmZpOW9k+WJjeW3peW6jz8nLCAn5o+Q56S6JywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkKICAgICAgICAudGhlbigoKSA9PiB7CiAgICAgICAgICBEZWxldGVQcm9jZXNzKHsKICAgICAgICAgICAgcHJvY2Vzc0lkCiAgICAgICAgICB9KS50aGVuKChyZXMpID0+IHsKICAgICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTmiJDlip8nLAogICAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICB0aGlzLmZldGNoRGF0YSgpCiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgICAgICB9KQogICAgICAgICAgICB9CiAgICAgICAgICB9KQogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKCgpID0+IHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnaW5mbycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICflt7Llj5bmtojliKDpmaQnCiAgICAgICAgICB9KQogICAgICAgIH0pCiAgICB9LAogICAgYXN5bmMgY2hhbmdlQ29sdW1uKCkgewogICAgICBhd2FpdCB0aGlzLmdldFRhYmxlQ29uZmlnKHRoaXMuZ3JpZENvZGUpCiAgICAgIHRoaXMudGJLZXkrKwogICAgfSwKICAgIGhhbmRsZVNlYXJjaCgpIHsKICAgICAgdGhpcy5mZXRjaERhdGEoKQogICAgfSwKICAgIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm1JbmxpbmUubmFtZSA9ICcnCiAgICAgIHRoaXMuZm9ybUlubGluZS5jb2RlID0gJycKICAgICAgdGhpcy5mZXRjaERhdGEoKQogICAgfSwKICAgIHNldFByb2plY3REYXRhKGRhdGEpIHsKICAgICAgdGhpcy5zeXNQcm9qZWN0SWQgPSBkYXRhLlN5c19Qcm9qZWN0X0lkCiAgICAgIHRoaXMuZmV0Y2hEYXRhKCkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/project-config/process-settings", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <ProjectData @setProjectData=\"setProjectData\" />\n    <div class=\"cs-z-page-main-content\">\n      <top-header padding=\"0\">\n        <template #right>\n          <div style=\"display: flex;\">\n            <el-form label-width=\"80px\" :inline=\"true\">\n              <el-form-item label=\"工序名称\">\n                <el-input v-model=\"formInline.name\" clearable placeholder=\"请输入工序名称\" @keyup.enter.native=\"handleSearch\" />\n              </el-form-item>\n              <el-form-item label=\"代号\">\n                <el-input v-model=\"formInline.code\" clearable placeholder=\"请输入代号\" @keyup.enter.native=\"handleSearch\" />\n              </el-form-item>\n              <el-form-item>\n                <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\n                <el-button @click=\"reset\">重置</el-button>\n              </el-form-item>\n            </el-form>\n            <DynamicTableFields\n              title=\"表格配置\"\n              :table-config-code=\"gridCode\"\n              @updateColumn=\"changeColumn\"\n            />\n          </div>\n        </template>\n        <template #left>\n          <el-button type=\"primary\" @click=\"handleAddProject\">同步项目配置</el-button>\n          <el-button type=\"primary\" @click=\"handleAddFactory\">恢复工厂默认配置</el-button>\n        </template>\n      </top-header>\n      <div v-loading=\"tbLoading\" class=\"fff cs-z-tb-wrapper\">\n        <vxe-table\n          ref=\"xTable\"\n          :key=\"tbKey\"\n          v-loading=\"tbLoading\"\n          :empty-render=\"{name: 'NotData'}\"\n          show-header-overflow\n          element-loading-spinner=\"el-icon-loading\"\n          element-loading-text=\"拼命加载中\"\n          empty-text=\"暂无数据\"\n          height=\"100%\"\n          :data=\"tbData\"\n          stripe\n          resizable\n          :auto-resize=\"true\"\n          class=\"cs-vxe-table\"\n          :tooltip-config=\"{ enterable: true }\"\n        >\n          <vxe-column\n            v-for=\"item in columns\"\n            :key=\"item.Code\"\n            :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n            show-overflow=\"tooltip\"\n            sortable\n            :align=\"item.Align\"\n            :field=\"item.Code\"\n            :title=\"item.Display_Name\"\n            :visible=\"item.Is_Display\"\n            :width=\"item.Width\"\n          >\n            <template #default=\"{ row }\">\n              <span v-if=\"item.Code === 'Workload_Proportion'\">\n                {{ row.Workload_Proportion ? row.Workload_Proportion + '%' : \"-\" }}\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Need_Check'\">\n                <el-tag v-if=\"row.Is_Need_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Self_Check'\">\n                <el-tag v-if=\"row.Is_Self_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Inter_Check'\">\n                <el-tag v-if=\"row.Is_Inter_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Enable'\">\n                <el-tag v-if=\"row.Is_Enable\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Nest'\">\n                <el-tag v-if=\"row.Is_Nest\" type=\"success\">是</el-tag><el-tag v-else-if=\"row.Is_Nest===false\" type=\"danger\">否</el-tag><span v-else>-</span>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Cutting'\">\n                <el-tag v-if=\"row.Is_Cutting\" type=\"success\">是</el-tag><el-tag v-else-if=\"row.Is_Cutting===false\" type=\"danger\">否</el-tag><span v-else>-</span>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Pick_Material'\">\n                <el-tag v-if=\"row.Is_Pick_Material\" type=\"success\">是</el-tag><el-tag v-else-if=\"row.Is_Pick_Material===false\" type=\"danger\">否</el-tag><span v-else>-</span>\n              </span>\n              <span v-else>{{ row[item.Code] || \"-\" }}</span>\n            </template>\n          </vxe-column>\n          <vxe-column fixed=\"right\" title=\"操作\" width=\"100\" show-overflow align=\"center\">\n            <template #default=\"{ row }\">\n              <el-button type=\"text\" size=\"small\" @click=\"handleDialog('edit', row)\">编辑</el-button>\n            </template>\n          </vxe-column>\n        </vxe-table>\n      </div>\n      <div style=\"height:50px; line-height: 50px; font-size: 14px; color: #298DFF;\">\n        工作量占比合计：{{ totalWorkloadProportion }}%\n      </div>\n      <el-dialog\n        v-if=\"dialogVisible\"\n        v-dialog-drag\n        class=\"cs-dialog\"\n        :close-on-click-modal=\"false\"\n        :title=\"title\"\n        :visible.sync=\"dialogVisible\"\n        custom-class=\"dialogCustomClass\"\n        width=\"580px\"\n        top=\"10vh\"\n        @close=\"handleClose\"\n      >\n        <component\n          :is=\"currentComponent\"\n          ref=\"content\"\n          :total-workload-proportion=\"totalWorkloadProportion\"\n          :row-info=\"rowInfo\"\n          :type=\"type\"\n          :level=\"level\"\n          :bom-list=\"bomList\"\n          :sys-project-id=\"sysProjectId\"\n          :dialog-visible=\"dialogVisible\"\n          @close=\"handleClose\"\n          @refresh=\"fetchData\"\n        />\n      </el-dialog>\n      <el-dialog\n        v-dialog-drag\n        class=\"cs-dialog\"\n        title=\"关联设备\"\n        :close-on-click-modal=\"false\"\n        :visible.sync=\"dialogVisible1\"\n        custom-class=\"dialogCustomClass\"\n        width=\"86%\"\n        top=\"5vh\"\n        @close=\"handleClose1\"\n      >\n        <AssociatedDevice ref=\"Device\" :row-data=\"rowData\" @fetchData=\"fetchData\" />\n      </el-dialog>\n    </div>\n  </div>\n</template>\n\n<script>\nimport ProjectData from '../components/ProjectData.vue'\nimport TopHeader from '@/components/TopHeader'\nimport Add from './component/Add'\nimport ZClass from './component/Group'\nimport ProjectAdd from './component/ProjectAddDialog.vue'\nimport AssociatedDevice from './component/AssociatedDevice'\nimport { GetProcessListBase, DeleteProcess, RestoreFactoryProcessFromProject } from '@/api/PRO/technology-lib'\nimport ElTableEmpty from '@/components/ElTableEmpty/index.vue'\nimport addRouterPage from '@/mixins/add-router-page'\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport getTbInfo from '@/mixins/PRO/get-table-info'\n\nexport default {\n  name: 'PROProcessManagement',\n  components: {\n    ProjectData,\n    ElTableEmpty,\n    TopHeader,\n    Add,\n    ZClass,\n    AssociatedDevice,\n    DynamicTableFields,\n    ProjectAdd\n  },\n  mixins: [addRouterPage, getTbInfo],\n  data() {\n    return {\n      tbLoading: false,\n      level: 0,\n      bomList: [],\n      addPageArray: [\n        {\n          path: '/AssociatedDevice',\n          hidden: true,\n          component: () => import('@/views/PRO/process-settings/management/component/AssociatedDevice.vue'),\n          name: 'AssociatedDevice',\n          meta: { title: '关联设备' }\n        }\n\n      ],\n      columns: [],\n      tbData: [],\n      currentComponent: '',\n      title: '',\n      comName: '',\n      partName: '',\n      rowInfo: null,\n      rowData: {},\n      type: '',\n      dialogVisible: false,\n      dialogVisible1: false,\n      formInline: { name: '', code: '' },\n      sysProjectId: '',\n      totalWorkloadProportion: 0,\n      tbKey: 100,\n      gridCode: 'processSettingsList'\n    }\n  },\n  async created() {\n    await this.getTableInfo()\n  },\n  mounted() {\n    this.getBOMInfo()\n  },\n  methods: {\n    async getTableInfo() {\n      await this.getTableConfig(this.gridCode)\n    },\n    async getBOMInfo() {\n      const { comName, partName, list } = await GetBOMInfo()\n      this.comName = comName\n      this.partName = partName\n      this.bomList = list\n    },\n    handleOpenDevice(row) {\n      this.rowData = row\n      this.dialogVisible1 = true\n      this.$nextTick(() => {\n        this.$refs.Device.clearSelec()\n      })\n      //  this.$router.push({ path: 'http://localhost:3000/produce/pro/nesting/index' })\n    },\n\n    fetchData() {\n      this.tbLoading = true\n      GetProcessListBase({ ...this.formInline, sysProjectId: this.sysProjectId }).then((res) => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data\n\n          // 计算所有 Workload_Proportion 的总和\n          this.totalWorkloadProportion = res.Data.reduce((total, item) => {\n            const proportion = parseFloat(item.Workload_Proportion) || 0\n            return total + proportion\n          }, 0)\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n        this.tbLoading = false\n        this.dialogVisible1 = false\n      })\n    },\n    handleClose() {\n      this.dialogVisible = false\n    },\n    handleClose1() {\n      this.dialogVisible1 = false\n    },\n    handleAddProject() {\n      this.dialogVisible = true\n      this.currentComponent = 'ProjectAdd'\n      this.title = '同步项目配置'\n    },\n    handleDialog(type, row) {\n      this.currentComponent = 'Add'\n      this.type = type\n      if (type === 'add') {\n        this.title = '新建'\n      } else {\n        this.title = '编辑'\n        this.rowInfo = row\n      }\n      this.dialogVisible = true\n    },\n    handleManage(row) {\n      this.currentComponent = 'ZClass'\n      this.title = '班组管理'\n      this.dialogVisible = true\n      this.$nextTick((_) => {\n        this.$refs.content.init(row)\n      })\n    },\n    // 从工厂级同步\n    handleAddFactory() {\n      this.$confirm('此操作将会恢复到工厂工序, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.restoreFactoryProcessFromProject()\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消恢复'\n        })\n      })\n    },\n    restoreFactoryProcessFromProject() {\n      RestoreFactoryProcessFromProject({\n        Sys_Project_Id: this.sysProjectId\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            message: '恢复成功',\n            type: 'success'\n          })\n          this.fetchData()\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    handleDelete(processId) {\n      this.$confirm('是否删除当前工序?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DeleteProcess({\n            processId\n          }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                message: '删除成功',\n                type: 'success'\n              })\n              this.fetchData()\n            } else {\n              this.$message({\n                message: res.Message,\n                type: 'error'\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    },\n    async changeColumn() {\n      await this.getTableConfig(this.gridCode)\n      this.tbKey++\n    },\n    handleSearch() {\n      this.fetchData()\n    },\n    reset() {\n      this.formInline.name = ''\n      this.formInline.code = ''\n      this.fetchData()\n    },\n    setProjectData(data) {\n      this.sysProjectId = data.Sys_Project_Id\n      this.fetchData()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/mixin.scss\";\n.app-container{\n  display: flex;\n  flex-direction: row;\n  height: 100%;\n  .cs-z-page-main-content {\n    flex: 1;\n  }\n}\n\n.cs-z-tb-wrapper {\n  height: 0;\n  flex: 1;\n}\n\n::v-deep {\n  .cs-top-header-box {\n    line-height: 0px;\n  }\n}\n\n.tb {\n  ::v-deep {\n    @include scrollBar;\n\n    &::-webkit-scrollbar {\n      width: 8px;\n    }\n  }\n}\n\n.cs-dialog {\n  ::v-deep {\n    .el-dialog__body {\n      padding: 20px 20px !important;\n      overflow: hidden;\n    }\n  }\n}\n\n.cs-tb-icon {\n  vertical-align: middle;\n}\n\n.cs-tag {\n  &:nth-child(2n) {\n    margin-left: 4px;\n  }\n\n  &:nth-child(n + 3) {\n    margin-top: 4px;\n  }\n}\n</style>\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\index.vue", "mtime": 1757554254466}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBUb3BIZWFkZXIgZnJvbSAnQC9jb21wb25lbnRzL1RvcEhlYWRlcicKaW1wb3J0IEFkZCBmcm9tICcuL2NvbXBvbmVudC9BZGQnCmltcG9ydCBaQ2xhc3MgZnJvbSAnLi9jb21wb25lbnQvR3JvdXAnCmltcG9ydCBBc3NvY2lhdGVkRGV2aWNlIGZyb20gJy4vY29tcG9uZW50L0Fzc29jaWF0ZWREZXZpY2UnCmltcG9ydCBSZWNvZ25pdGlvbkNvbmZpZyBmcm9tICcuL2NvbXBvbmVudC9SZWNvZ25pdGlvbkNvbmZpZycKaW1wb3J0IFBhcnRUYWtlQ29uZmlnIGZyb20gJy4vY29tcG9uZW50L1BhcnRUYWtlQ29uZmlnJwppbXBvcnQgeyBHZXRQcm9jZXNzTGlzdEJhc2UsIERlbGV0ZVByb2Nlc3MgfSBmcm9tICdAL2FwaS9QUk8vdGVjaG5vbG9neS1saWInCmltcG9ydCBFbFRhYmxlRW1wdHkgZnJvbSAnQC9jb21wb25lbnRzL0VsVGFibGVFbXB0eS9pbmRleC52dWUnCmltcG9ydCBhZGRSb3V0ZXJQYWdlIGZyb20gJ0AvbWl4aW5zL2FkZC1yb3V0ZXItcGFnZScKaW1wb3J0IER5bmFtaWNUYWJsZUZpZWxkcyBmcm9tICdAL2NvbXBvbmVudHMvRHluYW1pY1RhYmxlRmllbGRzL2luZGV4LnZ1ZScKaW1wb3J0IHsgR2V0Qk9NSW5mbyB9IGZyb20gJ0Avdmlld3MvUFJPL2JvbS1zZXR0aW5nL3V0aWxzJwppbXBvcnQgZ2V0VGJJbmZvIGZyb20gJ0AvbWl4aW5zL1BSTy9nZXQtdGFibGUtaW5mbycKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnUFJPUHJvY2Vzc01hbmFnZW1lbnQnLAogIGNvbXBvbmVudHM6IHsKICAgIEVsVGFibGVFbXB0eSwKICAgIFRvcEhlYWRlciwKICAgIEFkZCwKICAgIFBhcnRUYWtlQ29uZmlnLAogICAgWkNsYXNzLAogICAgQXNzb2NpYXRlZERldmljZSwKICAgIFJlY29nbml0aW9uQ29uZmlnLAogICAgRHluYW1pY1RhYmxlRmllbGRzCiAgfSwKICBtaXhpbnM6IFthZGRSb3V0ZXJQYWdlLCBnZXRUYkluZm9dLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB0YkxvYWRpbmc6IGZhbHNlLAogICAgICBsZXZlbDogMCwKICAgICAgYm9tTGlzdDogW10sCiAgICAgIGFkZFBhZ2VBcnJheTogWwogICAgICAgIHsKICAgICAgICAgIHBhdGg6ICcvQXNzb2NpYXRlZERldmljZScsCiAgICAgICAgICBoaWRkZW46IHRydWUsCiAgICAgICAgICBjb21wb25lbnQ6ICgpID0+IGltcG9ydCgnQC92aWV3cy9QUk8vcHJvY2Vzcy1zZXR0aW5ncy9tYW5hZ2VtZW50L2NvbXBvbmVudC9Bc3NvY2lhdGVkRGV2aWNlLnZ1ZScpLAogICAgICAgICAgbmFtZTogJ0Fzc29jaWF0ZWREZXZpY2UnLAogICAgICAgICAgbWV0YTogeyB0aXRsZTogJ+WFs+iBlOiuvuWkhycgfQogICAgICAgIH0KCiAgICAgIF0sCiAgICAgIGNvbHVtbnM6IFtdLAogICAgICB0YkRhdGE6IFtdLAogICAgICBjdXJyZW50Q29tcG9uZW50OiAnJywKICAgICAgdGl0bGU6ICcnLAogICAgICBjb21OYW1lOiAnJywKICAgICAgcGFydE5hbWU6ICcnLAogICAgICByb3dJbmZvOiBudWxsLAogICAgICByb3dEYXRhOiB7fSwKICAgICAgdHlwZTogJycsCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBkaWFsb2dWaXNpYmxlMTogZmFsc2UsCiAgICAgIGZvcm1JbmxpbmU6IHsgbmFtZTogJycsIGNvZGU6ICcnIH0sCiAgICAgIHRiS2V5OiAxMDAsCiAgICAgIGdyaWRDb2RlOiAncHJvY2Vzc1NldHRpbmdzTGlzdCcKICAgIH0KICB9LAogIGFzeW5jIGNyZWF0ZWQoKSB7CiAgICBhd2FpdCB0aGlzLmdldFRhYmxlSW5mbygpCiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5nZXRCT01JbmZvKCkKICAgIHRoaXMuZmV0Y2hEYXRhKCkKICB9LAogIG1ldGhvZHM6IHsKICAgIGFzeW5jIGdldFRhYmxlSW5mbygpIHsKICAgICAgYXdhaXQgdGhpcy5nZXRUYWJsZUNvbmZpZyh0aGlzLmdyaWRDb2RlKQogICAgfSwKICAgIGFzeW5jIGdldEJPTUluZm8oKSB7CiAgICAgIGNvbnN0IHsgY29tTmFtZSwgcGFydE5hbWUsIGxpc3QgfSA9IGF3YWl0IEdldEJPTUluZm8oKQogICAgICB0aGlzLmNvbU5hbWUgPSBjb21OYW1lCiAgICAgIHRoaXMucGFydE5hbWUgPSBwYXJ0TmFtZQogICAgICB0aGlzLmJvbUxpc3QgPSBsaXN0CiAgICB9LAogICAgaGFuZGxlT3BlbkRldmljZShyb3cpIHsKICAgICAgdGhpcy5yb3dEYXRhID0gcm93CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZTEgPSB0cnVlCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICB0aGlzLiRyZWZzLkRldmljZS5jbGVhclNlbGVjKCkKICAgICAgfSkKICAgICAgLy8gIHRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogJ2h0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9wcm9kdWNlL3Byby9uZXN0aW5nL2luZGV4JyB9KQogICAgfSwKCiAgICBmZXRjaERhdGEoKSB7CiAgICAgIHRoaXMudGJMb2FkaW5nID0gdHJ1ZQogICAgICBHZXRQcm9jZXNzTGlzdEJhc2UodGhpcy5mb3JtSW5saW5lKS50aGVuKChyZXMpID0+IHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgdGhpcy50YkRhdGEgPSByZXMuRGF0YQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICAgIHRoaXMudGJMb2FkaW5nID0gZmFsc2UKICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUxID0gZmFsc2UKICAgICAgfSkKICAgIH0sCiAgICBoYW5kbGVDbG9zZSgpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UKICAgIH0sCiAgICBoYW5kbGVDbG9zZTEoKSB7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZTEgPSBmYWxzZQogICAgfSwKICAgIGhhbmRsZVJlY29nbml0aW9uQ29uZmlnKCkgewogICAgICB0aGlzLnRpdGxlID0gYOWvvOWFpeivhuWIq+mFjee9rmAKICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ1JlY29nbml0aW9uQ29uZmlnJwogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlCiAgICB9LAogICAgaGFuZGxlVGFrZUNvbmZpZygpIHsKICAgICAgdGhpcy50aXRsZSA9IGAke3RoaXMucGFydE5hbWV96aKG55So6YWN572uYAogICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSAnUGFydFRha2VDb25maWcnCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUKICAgIH0sCiAgICBoYW5kbGVEaWFsb2codHlwZSwgcm93KSB7CiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9ICdBZGQnCiAgICAgIHRoaXMudHlwZSA9IHR5cGUKICAgICAgaWYgKHR5cGUgPT09ICdhZGQnKSB7CiAgICAgICAgdGhpcy50aXRsZSA9ICfmlrDlu7onCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy50aXRsZSA9ICfnvJbovpEnCiAgICAgICAgdGhpcy5yb3dJbmZvID0gcm93CiAgICAgIH0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQogICAgfSwKICAgIGhhbmRsZU1hbmFnZShyb3cpIHsKICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ1pDbGFzcycKICAgICAgdGhpcy50aXRsZSA9ICfnj63nu4TnrqHnkIYnCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUKICAgICAgdGhpcy4kbmV4dFRpY2soKF8pID0+IHsKICAgICAgICB0aGlzLiRyZWZzLmNvbnRlbnQuaW5pdChyb3cpCiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlRGVsZXRlKHByb2Nlc3NJZCkgewogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbliKDpmaTlvZPliY3lt6Xluo8/JywgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pCiAgICAgICAgLnRoZW4oKCkgPT4gewogICAgICAgICAgRGVsZXRlUHJvY2Vzcyh7CiAgICAgICAgICAgIHByb2Nlc3NJZAogICAgICAgICAgfSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5oiQ5YqfJywKICAgICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJwogICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgdGhpcy5mZXRjaERhdGEoKQogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgfQogICAgICAgICAgfSkKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ2luZm8nLAogICAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raI5Yig6ZmkJwogICAgICAgICAgfSkKICAgICAgICB9KQogICAgfSwKICAgIGFzeW5jIGNoYW5nZUNvbHVtbigpIHsKICAgICAgYXdhaXQgdGhpcy5nZXRUYWJsZUNvbmZpZyh0aGlzLmdyaWRDb2RlKQogICAgICB0aGlzLnRiS2V5KysKICAgIH0sCiAgICBoYW5kbGVTZWFyY2goKSB7CiAgICAgIHRoaXMuZmV0Y2hEYXRhKCkKICAgIH0sCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtSW5saW5lLm5hbWUgPSAnJwogICAgICB0aGlzLmZvcm1JbmxpbmUuY29kZSA9ICcnCiAgICAgIHRoaXMuZmV0Y2hEYXRhKCkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/project-config/process-settings", "sourcesContent": ["<template>\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\n    <div class=\"cs-z-page-main-content\">\n      <top-header padding=\"0\">\n        <template #right>\n          <div style=\"display: flex;\">\n            <el-form label-width=\"80px\" :inline=\"true\">\n              <el-form-item label=\"工序名称\">\n                <el-input v-model=\"formInline.name\" clearable placeholder=\"请输入工序名称\" @keyup.enter.native=\"handleSearch\" />\n              </el-form-item>\n              <el-form-item label=\"代号\">\n                <el-input v-model=\"formInline.code\" clearable placeholder=\"请输入代号\" @keyup.enter.native=\"handleSearch\" />\n              </el-form-item>\n              <el-form-item>\n                <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\n                <el-button @click=\"reset\">重置</el-button>\n              </el-form-item>\n            </el-form>\n            <DynamicTableFields\n              title=\"表格配置\"\n              :table-config-code=\"gridCode\"\n              @updateColumn=\"changeColumn\"\n            />\n          </div>\n        </template>\n        <template #left>\n          <el-button icon=\"el-icon-plus\" type=\"primary\" size=\"small\" @click=\"handleDialog('add')\">新增</el-button>\n          <el-button type=\"success\" size=\"small\" @click=\"handleRecognitionConfig\">导入识别配置</el-button>\n          <el-button type=\"primary\" size=\"small\" @click=\"handleTakeConfig\">{{ partName }}领用配置</el-button>\n        </template>\n      </top-header>\n      <div v-loading=\"tbLoading\" class=\"fff cs-z-tb-wrapper\">\n        <vxe-table\n          ref=\"xTable\"\n          :key=\"tbKey\"\n          v-loading=\"tbLoading\"\n          :empty-render=\"{name: 'NotData'}\"\n          show-header-overflow\n          element-loading-spinner=\"el-icon-loading\"\n          element-loading-text=\"拼命加载中\"\n          empty-text=\"暂无数据\"\n          height=\"100%\"\n          :data=\"tbData\"\n          stripe\n          resizable\n          :auto-resize=\"true\"\n          class=\"cs-vxe-table\"\n          :tooltip-config=\"{ enterable: true }\"\n        >\n          <vxe-column\n            v-for=\"item in columns\"\n            :key=\"item.Code\"\n            :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n            show-overflow=\"tooltip\"\n            sortable\n            :align=\"item.Align\"\n            :field=\"item.Code\"\n            :title=\"item.Display_Name\"\n            :visible=\"item.Is_Display\"\n            :width=\"item.Width\"\n          >\n            <template #default=\"{ row }\">\n              <span v-if=\"item.Code === 'Workload_Proportion'\">\n                {{ row.Workload_Proportion ? row.Workload_Proportion + '%' : \"-\" }}\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Need_Check'\">\n                <el-tag v-if=\"row.Is_Need_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Self_Check'\">\n                <el-tag v-if=\"row.Is_Self_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Inter_Check'\">\n                <el-tag v-if=\"row.Is_Inter_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Enable'\">\n                <el-tag v-if=\"row.Is_Enable\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Nest'\">\n                <el-tag v-if=\"row.Is_Nest\" type=\"success\">是</el-tag><el-tag v-else-if=\"row.Is_Nest===false\" type=\"danger\">否</el-tag><span v-else>-</span>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Cutting'\">\n                <el-tag v-if=\"row.Is_Cutting\" type=\"success\">是</el-tag><el-tag v-else-if=\"row.Is_Cutting===false\" type=\"danger\">否</el-tag><span v-else>-</span>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Pick_Material'\">\n                <el-tag v-if=\"row.Is_Pick_Material\" type=\"success\">是</el-tag><el-tag v-else-if=\"row.Is_Pick_Material===false\" type=\"danger\">否</el-tag><span v-else>-</span>\n              </span>\n              <span v-else>{{ row[item.Code] || \"-\" }}</span>\n            </template>\n          </vxe-column>\n          <vxe-column fixed=\"right\" title=\"操作\" width=\"180\" show-overflow align=\"center\">\n            <template #default=\"{ row }\">\n              <el-button type=\"text\" size=\"small\" @click=\"handleDialog('edit', row)\">编辑</el-button>\n              <el-button type=\"text\" size=\"small\" @click=\"handleOpenDevice(row)\">关联设备</el-button>\n              <el-button class=\"txt-red\" type=\"text\" size=\"small\" @click=\"handleDelete(row.Id)\">删除</el-button>\n            </template>\n          </vxe-column>\n        </vxe-table>\n      </div>\n      <el-dialog\n        v-if=\"dialogVisible\"\n        v-dialog-drag\n        class=\"cs-dialog\"\n        :close-on-click-modal=\"false\"\n        :title=\"title\"\n        :visible.sync=\"dialogVisible\"\n        custom-class=\"dialogCustomClass\"\n        :width=\"currentComponent==='PartTakeConfig' ? '800px' : '580px'\"\n        top=\"5vh\"\n        @close=\"handleClose\"\n      >\n        <component\n          :is=\"currentComponent\"\n          ref=\"content\"\n          :row-info=\"rowInfo\"\n          :type=\"type\"\n          :level=\"level\"\n          :bom-list=\"bomList\"\n          :dialog-visible=\"dialogVisible\"\n          @close=\"handleClose\"\n          @refresh=\"fetchData\"\n        />\n      </el-dialog>\n      <el-dialog\n        v-dialog-drag\n        class=\"cs-dialog\"\n        title=\"关联设备\"\n        :close-on-click-modal=\"false\"\n        :visible.sync=\"dialogVisible1\"\n        custom-class=\"dialogCustomClass\"\n        width=\"86%\"\n        top=\"5vh\"\n        @close=\"handleClose1\"\n      >\n        <AssociatedDevice ref=\"Device\" :row-data=\"rowData\" @fetchData=\"fetchData\" />\n        <!-- <span slot=\"footer\" class=\"dialog-footer\">\n          <el-button @click=\"dialogVisible1 = false\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"dialogVisible1 = false\">确 定</el-button>\n        </span> -->\n      </el-dialog>\n    </div>\n  </div>\n</template>\n\n<script>\nimport TopHeader from '@/components/TopHeader'\nimport Add from './component/Add'\nimport ZClass from './component/Group'\nimport AssociatedDevice from './component/AssociatedDevice'\nimport RecognitionConfig from './component/RecognitionConfig'\nimport PartTakeConfig from './component/PartTakeConfig'\nimport { GetProcessListBase, DeleteProcess } from '@/api/PRO/technology-lib'\nimport ElTableEmpty from '@/components/ElTableEmpty/index.vue'\nimport addRouterPage from '@/mixins/add-router-page'\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport getTbInfo from '@/mixins/PRO/get-table-info'\n\nexport default {\n  name: 'PROProcessManagement',\n  components: {\n    ElTableEmpty,\n    TopHeader,\n    Add,\n    PartTakeConfig,\n    ZClass,\n    AssociatedDevice,\n    RecognitionConfig,\n    DynamicTableFields\n  },\n  mixins: [addRouterPage, getTbInfo],\n  data() {\n    return {\n      tbLoading: false,\n      level: 0,\n      bomList: [],\n      addPageArray: [\n        {\n          path: '/AssociatedDevice',\n          hidden: true,\n          component: () => import('@/views/PRO/process-settings/management/component/AssociatedDevice.vue'),\n          name: 'AssociatedDevice',\n          meta: { title: '关联设备' }\n        }\n\n      ],\n      columns: [],\n      tbData: [],\n      currentComponent: '',\n      title: '',\n      comName: '',\n      partName: '',\n      rowInfo: null,\n      rowData: {},\n      type: '',\n      dialogVisible: false,\n      dialogVisible1: false,\n      formInline: { name: '', code: '' },\n      tbKey: 100,\n      gridCode: 'processSettingsList'\n    }\n  },\n  async created() {\n    await this.getTableInfo()\n  },\n  mounted() {\n    this.getBOMInfo()\n    this.fetchData()\n  },\n  methods: {\n    async getTableInfo() {\n      await this.getTableConfig(this.gridCode)\n    },\n    async getBOMInfo() {\n      const { comName, partName, list } = await GetBOMInfo()\n      this.comName = comName\n      this.partName = partName\n      this.bomList = list\n    },\n    handleOpenDevice(row) {\n      this.rowData = row\n      this.dialogVisible1 = true\n      this.$nextTick(() => {\n        this.$refs.Device.clearSelec()\n      })\n      //  this.$router.push({ path: 'http://localhost:3000/produce/pro/nesting/index' })\n    },\n\n    fetchData() {\n      this.tbLoading = true\n      GetProcessListBase(this.formInline).then((res) => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n        this.tbLoading = false\n        this.dialogVisible1 = false\n      })\n    },\n    handleClose() {\n      this.dialogVisible = false\n    },\n    handleClose1() {\n      this.dialogVisible1 = false\n    },\n    handleRecognitionConfig() {\n      this.title = `导入识别配置`\n      this.currentComponent = 'RecognitionConfig'\n      this.dialogVisible = true\n    },\n    handleTakeConfig() {\n      this.title = `${this.partName}领用配置`\n      this.currentComponent = 'PartTakeConfig'\n      this.dialogVisible = true\n    },\n    handleDialog(type, row) {\n      this.currentComponent = 'Add'\n      this.type = type\n      if (type === 'add') {\n        this.title = '新建'\n      } else {\n        this.title = '编辑'\n        this.rowInfo = row\n      }\n      this.dialogVisible = true\n    },\n    handleManage(row) {\n      this.currentComponent = 'ZClass'\n      this.title = '班组管理'\n      this.dialogVisible = true\n      this.$nextTick((_) => {\n        this.$refs.content.init(row)\n      })\n    },\n    handleDelete(processId) {\n      this.$confirm('是否删除当前工序?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DeleteProcess({\n            processId\n          }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                message: '删除成功',\n                type: 'success'\n              })\n              this.fetchData()\n            } else {\n              this.$message({\n                message: res.Message,\n                type: 'error'\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    },\n    async changeColumn() {\n      await this.getTableConfig(this.gridCode)\n      this.tbKey++\n    },\n    handleSearch() {\n      this.fetchData()\n    },\n    reset() {\n      this.formInline.name = ''\n      this.formInline.code = ''\n      this.fetchData()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/mixin.scss\";\n.cs-z-page-main-content{\n  min-width: 1000px;\n}\n\n.cs-z-tb-wrapper {\n  height: 0;\n  flex: 1;\n}\n\n::v-deep {\n  .cs-top-header-box {\n    line-height: 0px;\n  }\n}\n\n.tb {\n  ::v-deep {\n    @include scrollBar;\n\n    &::-webkit-scrollbar {\n      width: 8px;\n    }\n  }\n}\n\n.cs-dialog {\n  ::v-deep {\n    .el-dialog__body {\n      padding: 20px 20px !important;\n      overflow: hidden;\n    }\n  }\n}\n\n.cs-tb-icon {\n  vertical-align: middle;\n}\n\n.cs-tag {\n  &:nth-child(2n) {\n    margin-left: 4px;\n  }\n\n  &:nth-child(n + 3) {\n    margin-top: 4px;\n  }\n}\n</style>\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\index.vue", "mtime": 1757646015794}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBQcm9qZWN0RGF0YSBmcm9tICcuLi9jb21wb25lbnRzL1Byb2plY3REYXRhLnZ1ZScKaW1wb3J0IFRvcEhlYWRlciBmcm9tICdAL2NvbXBvbmVudHMvVG9wSGVhZGVyJwppbXBvcnQgQWRkIGZyb20gJy4vY29tcG9uZW50L0FkZCcKaW1wb3J0IFpDbGFzcyBmcm9tICcuL2NvbXBvbmVudC9Hcm91cCcKaW1wb3J0IFByb2plY3RBZGQgZnJvbSAnLi9jb21wb25lbnQvUHJvamVjdEFkZERpYWxvZy52dWUnCmltcG9ydCBBc3NvY2lhdGVkRGV2aWNlIGZyb20gJy4vY29tcG9uZW50L0Fzc29jaWF0ZWREZXZpY2UnCmltcG9ydCB7IEdldFByb2Nlc3NMaXN0QmFzZSwgRGVsZXRlUHJvY2VzcywgR2V0UHJvY2Vzc09mUHJvamVjdExpc3QsIFJlc3RvcmVGYWN0b3J5UHJvY2Vzc0Zyb21Qcm9qZWN0IH0gZnJvbSAnQC9hcGkvUFJPL3RlY2hub2xvZ3ktbGliJwppbXBvcnQgRWxUYWJsZUVtcHR5IGZyb20gJ0AvY29tcG9uZW50cy9FbFRhYmxlRW1wdHkvaW5kZXgudnVlJwppbXBvcnQgYWRkUm91dGVyUGFnZSBmcm9tICdAL21peGlucy9hZGQtcm91dGVyLXBhZ2UnCmltcG9ydCBEeW5hbWljVGFibGVGaWVsZHMgZnJvbSAnQC9jb21wb25lbnRzL0R5bmFtaWNUYWJsZUZpZWxkcy9pbmRleC52dWUnCmltcG9ydCB7IEdldEJPTUluZm8gfSBmcm9tICdAL3ZpZXdzL1BSTy9ib20tc2V0dGluZy91dGlscycKaW1wb3J0IGdldFRiSW5mbyBmcm9tICdAL21peGlucy9QUk8vZ2V0LXRhYmxlLWluZm8nCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1BST1Byb2Nlc3NNYW5hZ2VtZW50JywKICBjb21wb25lbnRzOiB7CiAgICBQcm9qZWN0RGF0YSwKICAgIEVsVGFibGVFbXB0eSwKICAgIFRvcEhlYWRlciwKICAgIEFkZCwKICAgIFpDbGFzcywKICAgIEFzc29jaWF0ZWREZXZpY2UsCiAgICBEeW5hbWljVGFibGVGaWVsZHMsCiAgICBQcm9qZWN0QWRkCiAgfSwKICBtaXhpbnM6IFthZGRSb3V0ZXJQYWdlLCBnZXRUYkluZm9dLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB0YkxvYWRpbmc6IGZhbHNlLAogICAgICBsZXZlbDogMCwKICAgICAgYm9tTGlzdDogW10sCiAgICAgIGFkZFBhZ2VBcnJheTogWwogICAgICAgIHsKICAgICAgICAgIHBhdGg6ICcvQXNzb2NpYXRlZERldmljZScsCiAgICAgICAgICBoaWRkZW46IHRydWUsCiAgICAgICAgICBjb21wb25lbnQ6ICgpID0+IGltcG9ydCgnQC92aWV3cy9QUk8vcHJvY2Vzcy1zZXR0aW5ncy9tYW5hZ2VtZW50L2NvbXBvbmVudC9Bc3NvY2lhdGVkRGV2aWNlLnZ1ZScpLAogICAgICAgICAgbmFtZTogJ0Fzc29jaWF0ZWREZXZpY2UnLAogICAgICAgICAgbWV0YTogeyB0aXRsZTogJ+WFs+iBlOiuvuWkhycgfQogICAgICAgIH0KCiAgICAgIF0sCiAgICAgIGNvbHVtbnM6IFtdLAogICAgICB0YkRhdGE6IFtdLAogICAgICBjdXJyZW50Q29tcG9uZW50OiAnJywKICAgICAgdGl0bGU6ICcnLAogICAgICBjb21OYW1lOiAnJywKICAgICAgcGFydE5hbWU6ICcnLAogICAgICByb3dJbmZvOiBudWxsLAogICAgICByb3dEYXRhOiB7fSwKICAgICAgdHlwZTogJycsCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBkaWFsb2dWaXNpYmxlMTogZmFsc2UsCiAgICAgIGZvcm1JbmxpbmU6IHsgbmFtZTogJycsIGNvZGU6ICcnIH0sCiAgICAgIHN5c1Byb2plY3RJZDogJycsCiAgICAgIHRvdGFsV29ya2xvYWRQcm9wb3J0aW9uOiAwLAogICAgICB0YktleTogMTAwLAogICAgICBncmlkQ29kZTogJ3Byb2Nlc3NTZXR0aW5nc0xpc3QnCiAgICB9CiAgfSwKICBhc3luYyBjcmVhdGVkKCkgewogICAgYXdhaXQgdGhpcy5nZXRUYWJsZUluZm8oKQogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMuZ2V0Qk9NSW5mbygpCiAgICB0aGlzLmZldGNoRGF0YSgpCiAgfSwKICBtZXRob2RzOiB7CiAgICBhc3luYyBnZXRUYWJsZUluZm8oKSB7CiAgICAgIGF3YWl0IHRoaXMuZ2V0VGFibGVDb25maWcodGhpcy5ncmlkQ29kZSkKICAgIH0sCiAgICBhc3luYyBnZXRCT01JbmZvKCkgewogICAgICBjb25zdCB7IGNvbU5hbWUsIHBhcnROYW1lLCBsaXN0IH0gPSBhd2FpdCBHZXRCT01JbmZvKCkKICAgICAgdGhpcy5jb21OYW1lID0gY29tTmFtZQogICAgICB0aGlzLnBhcnROYW1lID0gcGFydE5hbWUKICAgICAgdGhpcy5ib21MaXN0ID0gbGlzdAogICAgfSwKICAgIGhhbmRsZU9wZW5EZXZpY2Uocm93KSB7CiAgICAgIHRoaXMucm93RGF0YSA9IHJvdwogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUxID0gdHJ1ZQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgdGhpcy4kcmVmcy5EZXZpY2UuY2xlYXJTZWxlYygpCiAgICAgIH0pCiAgICAgIC8vICB0aGlzLiRyb3V0ZXIucHVzaCh7IHBhdGg6ICdodHRwOi8vbG9jYWxob3N0OjMwMDAvcHJvZHVjZS9wcm8vbmVzdGluZy9pbmRleCcgfSkKICAgIH0sCgogICAgZmV0Y2hEYXRhKCkgewogICAgICB0aGlzLnRiTG9hZGluZyA9IHRydWUKICAgICAgR2V0UHJvY2Vzc0xpc3RCYXNlKHRoaXMuZm9ybUlubGluZSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIHRoaXMudGJEYXRhID0gcmVzLkRhdGEKCiAgICAgICAgICAvLyDorqHnrpfmiYDmnIkgV29ya2xvYWRfUHJvcG9ydGlvbiDnmoTmgLvlkowKICAgICAgICAgIHRoaXMudG90YWxXb3JrbG9hZFByb3BvcnRpb24gPSByZXMuRGF0YS5yZWR1Y2UoKHRvdGFsLCBpdGVtKSA9PiB7CiAgICAgICAgICAgIGNvbnN0IHByb3BvcnRpb24gPSBwYXJzZUZsb2F0KGl0ZW0uV29ya2xvYWRfUHJvcG9ydGlvbikgfHwgMAogICAgICAgICAgICByZXR1cm4gdG90YWwgKyBwcm9wb3J0aW9uCiAgICAgICAgICB9LCAwKQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICAgIHRoaXMudGJMb2FkaW5nID0gZmFsc2UKICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUxID0gZmFsc2UKICAgICAgfSkKICAgIH0sCiAgICBoYW5kbGVDbG9zZSgpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UKICAgIH0sCiAgICBoYW5kbGVDbG9zZTEoKSB7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZTEgPSBmYWxzZQogICAgfSwKICAgIGhhbmRsZUFkZFByb2plY3QoKSB7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUKICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ1Byb2plY3RBZGQnCiAgICAgIHRoaXMudGl0bGUgPSAn5ZCM5q2l6aG555uu6YWN572uJwogICAgfSwKICAgIGhhbmRsZURpYWxvZyh0eXBlLCByb3cpIHsKICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ0FkZCcKICAgICAgdGhpcy50eXBlID0gdHlwZQogICAgICBpZiAodHlwZSA9PT0gJ2FkZCcpIHsKICAgICAgICB0aGlzLnRpdGxlID0gJ+aWsOW7uicKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnRpdGxlID0gJ+e8lui+kScKICAgICAgICB0aGlzLnJvd0luZm8gPSByb3cKICAgICAgfQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlCiAgICB9LAogICAgaGFuZGxlTWFuYWdlKHJvdykgewogICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSAnWkNsYXNzJwogICAgICB0aGlzLnRpdGxlID0gJ+ePree7hOeuoeeQhicKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQogICAgICB0aGlzLiRuZXh0VGljaygoXykgPT4gewogICAgICAgIHRoaXMuJHJlZnMuY29udGVudC5pbml0KHJvdykKICAgICAgfSkKICAgIH0sCiAgICAvLyDku47lt6XljoLnuqflkIzmraUKICAgIGhhbmRsZUFkZEZhY3RvcnkoKSB7CiAgICAgIFJlc3RvcmVGYWN0b3J5UHJvY2Vzc0Zyb21Qcm9qZWN0KHsKICAgICAgICBTeXNfUHJvamVjdF9JZDogdGhpcy5zeXNQcm9qZWN0SWQKICAgICAgfSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiAn5ZCM5q2l5oiQ5YqfJywKICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgICAgICB9KQogICAgICAgICAgdGhpcy5mZXRjaERhdGEoKQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZURlbGV0ZShwcm9jZXNzSWQpIHsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm5Yig6Zmk5b2T5YmN5bel5bqPPycsICfmj5DnpLonLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KQogICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgIERlbGV0ZVByb2Nlc3MoewogICAgICAgICAgICBwcm9jZXNzSWQKICAgICAgICAgIH0pLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKnycsCiAgICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycKICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgIHRoaXMuZmV0Y2hEYXRhKCkKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgICAgIH0pCiAgICAgICAgICAgIH0KICAgICAgICAgIH0pCiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goKCkgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdpbmZvJywKICAgICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iOWIoOmZpCcKICAgICAgICAgIH0pCiAgICAgICAgfSkKICAgIH0sCiAgICBhc3luYyBjaGFuZ2VDb2x1bW4oKSB7CiAgICAgIGF3YWl0IHRoaXMuZ2V0VGFibGVDb25maWcodGhpcy5ncmlkQ29kZSkKICAgICAgdGhpcy50YktleSsrCiAgICB9LAogICAgaGFuZGxlU2VhcmNoKCkgewogICAgICB0aGlzLmZldGNoRGF0YSgpCiAgICB9LAogICAgcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybUlubGluZS5uYW1lID0gJycKICAgICAgdGhpcy5mb3JtSW5saW5lLmNvZGUgPSAnJwogICAgICB0aGlzLmZldGNoRGF0YSgpCiAgICB9LAogICAgc2V0UHJvamVjdERhdGEoZGF0YSkgewogICAgICB0aGlzLnN5c1Byb2plY3RJZCA9IGRhdGEuU3lzX1Byb2plY3RfSWQKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/project-config/process-settings", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <ProjectData @setProjectData=\"setProjectData\" />\n    <div class=\"cs-z-page-main-content\">\n      <top-header padding=\"0\">\n        <template #right>\n          <div style=\"display: flex;\">\n            <el-form label-width=\"80px\" :inline=\"true\">\n              <el-form-item label=\"工序名称\">\n                <el-input v-model=\"formInline.name\" clearable placeholder=\"请输入工序名称\" @keyup.enter.native=\"handleSearch\" />\n              </el-form-item>\n              <el-form-item label=\"代号\">\n                <el-input v-model=\"formInline.code\" clearable placeholder=\"请输入代号\" @keyup.enter.native=\"handleSearch\" />\n              </el-form-item>\n              <el-form-item>\n                <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\n                <el-button @click=\"reset\">重置</el-button>\n              </el-form-item>\n            </el-form>\n            <DynamicTableFields\n              title=\"表格配置\"\n              :table-config-code=\"gridCode\"\n              @updateColumn=\"changeColumn\"\n            />\n          </div>\n        </template>\n        <template #left>\n          <el-button type=\"primary\" @click=\"handleAddProject\">同步项目配置</el-button>\n          <el-button type=\"primary\" @click=\"handleAddFactory\">恢复工厂默认配置</el-button>\n        </template>\n      </top-header>\n      <div v-loading=\"tbLoading\" class=\"fff cs-z-tb-wrapper\">\n        <vxe-table\n          ref=\"xTable\"\n          :key=\"tbKey\"\n          v-loading=\"tbLoading\"\n          :empty-render=\"{name: 'NotData'}\"\n          show-header-overflow\n          element-loading-spinner=\"el-icon-loading\"\n          element-loading-text=\"拼命加载中\"\n          empty-text=\"暂无数据\"\n          height=\"100%\"\n          :data=\"tbData\"\n          stripe\n          resizable\n          :auto-resize=\"true\"\n          class=\"cs-vxe-table\"\n          :tooltip-config=\"{ enterable: true }\"\n        >\n          <vxe-column\n            v-for=\"item in columns\"\n            :key=\"item.Code\"\n            :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n            show-overflow=\"tooltip\"\n            sortable\n            :align=\"item.Align\"\n            :field=\"item.Code\"\n            :title=\"item.Display_Name\"\n            :visible=\"item.Is_Display\"\n            :width=\"item.Width\"\n          >\n            <template #default=\"{ row }\">\n              <span v-if=\"item.Code === 'Workload_Proportion'\">\n                {{ row.Workload_Proportion ? row.Workload_Proportion + '%' : \"-\" }}\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Need_Check'\">\n                <el-tag v-if=\"row.Is_Need_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Self_Check'\">\n                <el-tag v-if=\"row.Is_Self_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Inter_Check'\">\n                <el-tag v-if=\"row.Is_Inter_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Enable'\">\n                <el-tag v-if=\"row.Is_Enable\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Nest'\">\n                <el-tag v-if=\"row.Is_Nest\" type=\"success\">是</el-tag><el-tag v-else-if=\"row.Is_Nest===false\" type=\"danger\">否</el-tag><span v-else>-</span>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Cutting'\">\n                <el-tag v-if=\"row.Is_Cutting\" type=\"success\">是</el-tag><el-tag v-else-if=\"row.Is_Cutting===false\" type=\"danger\">否</el-tag><span v-else>-</span>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Pick_Material'\">\n                <el-tag v-if=\"row.Is_Pick_Material\" type=\"success\">是</el-tag><el-tag v-else-if=\"row.Is_Pick_Material===false\" type=\"danger\">否</el-tag><span v-else>-</span>\n              </span>\n              <span v-else>{{ row[item.Code] || \"-\" }}</span>\n            </template>\n          </vxe-column>\n          <vxe-column fixed=\"right\" title=\"操作\" width=\"100\" show-overflow align=\"center\">\n            <template #default=\"{ row }\">\n              <el-button type=\"text\" size=\"small\" @click=\"handleDialog('edit', row)\">编辑</el-button>\n            </template>\n          </vxe-column>\n        </vxe-table>\n      </div>\n      <div style=\"height:50px; line-height: 50px; font-size: 14px; color: #298DFF;\">\n        工作量占比合计：{{ totalWorkloadProportion }}%\n      </div>\n      <el-dialog\n        v-if=\"dialogVisible\"\n        v-dialog-drag\n        class=\"cs-dialog\"\n        :close-on-click-modal=\"false\"\n        :title=\"title\"\n        :visible.sync=\"dialogVisible\"\n        custom-class=\"dialogCustomClass\"\n        width=\"580px\"\n        top=\"10vh\"\n        @close=\"handleClose\"\n      >\n        <component\n          :is=\"currentComponent\"\n          ref=\"content\"\n          :total-workload-proportion=\"totalWorkloadProportion\"\n          :row-info=\"rowInfo\"\n          :type=\"type\"\n          :level=\"level\"\n          :bom-list=\"bomList\"\n          :sys-project-id=\"sysProjectId\"\n          :dialog-visible=\"dialogVisible\"\n          @close=\"handleClose\"\n          @refresh=\"fetchData\"\n        />\n      </el-dialog>\n      <el-dialog\n        v-dialog-drag\n        class=\"cs-dialog\"\n        title=\"关联设备\"\n        :close-on-click-modal=\"false\"\n        :visible.sync=\"dialogVisible1\"\n        custom-class=\"dialogCustomClass\"\n        width=\"86%\"\n        top=\"5vh\"\n        @close=\"handleClose1\"\n      >\n        <AssociatedDevice ref=\"Device\" :row-data=\"rowData\" @fetchData=\"fetchData\" />\n      </el-dialog>\n    </div>\n  </div>\n</template>\n\n<script>\nimport ProjectData from '../components/ProjectData.vue'\nimport TopHeader from '@/components/TopHeader'\nimport Add from './component/Add'\nimport ZClass from './component/Group'\nimport ProjectAdd from './component/ProjectAddDialog.vue'\nimport AssociatedDevice from './component/AssociatedDevice'\nimport { GetProcessListBase, DeleteProcess, GetProcessOfProjectList, RestoreFactoryProcessFromProject } from '@/api/PRO/technology-lib'\nimport ElTableEmpty from '@/components/ElTableEmpty/index.vue'\nimport addRouterPage from '@/mixins/add-router-page'\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport getTbInfo from '@/mixins/PRO/get-table-info'\n\nexport default {\n  name: 'PROProcessManagement',\n  components: {\n    ProjectData,\n    ElTableEmpty,\n    TopHeader,\n    Add,\n    ZClass,\n    AssociatedDevice,\n    DynamicTableFields,\n    ProjectAdd\n  },\n  mixins: [addRouterPage, getTbInfo],\n  data() {\n    return {\n      tbLoading: false,\n      level: 0,\n      bomList: [],\n      addPageArray: [\n        {\n          path: '/AssociatedDevice',\n          hidden: true,\n          component: () => import('@/views/PRO/process-settings/management/component/AssociatedDevice.vue'),\n          name: 'AssociatedDevice',\n          meta: { title: '关联设备' }\n        }\n\n      ],\n      columns: [],\n      tbData: [],\n      currentComponent: '',\n      title: '',\n      comName: '',\n      partName: '',\n      rowInfo: null,\n      rowData: {},\n      type: '',\n      dialogVisible: false,\n      dialogVisible1: false,\n      formInline: { name: '', code: '' },\n      sysProjectId: '',\n      totalWorkloadProportion: 0,\n      tbKey: 100,\n      gridCode: 'processSettingsList'\n    }\n  },\n  async created() {\n    await this.getTableInfo()\n  },\n  mounted() {\n    this.getBOMInfo()\n    this.fetchData()\n  },\n  methods: {\n    async getTableInfo() {\n      await this.getTableConfig(this.gridCode)\n    },\n    async getBOMInfo() {\n      const { comName, partName, list } = await GetBOMInfo()\n      this.comName = comName\n      this.partName = partName\n      this.bomList = list\n    },\n    handleOpenDevice(row) {\n      this.rowData = row\n      this.dialogVisible1 = true\n      this.$nextTick(() => {\n        this.$refs.Device.clearSelec()\n      })\n      //  this.$router.push({ path: 'http://localhost:3000/produce/pro/nesting/index' })\n    },\n\n    fetchData() {\n      this.tbLoading = true\n      GetProcessListBase(this.formInline).then((res) => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data\n\n          // 计算所有 Workload_Proportion 的总和\n          this.totalWorkloadProportion = res.Data.reduce((total, item) => {\n            const proportion = parseFloat(item.Workload_Proportion) || 0\n            return total + proportion\n          }, 0)\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n        this.tbLoading = false\n        this.dialogVisible1 = false\n      })\n    },\n    handleClose() {\n      this.dialogVisible = false\n    },\n    handleClose1() {\n      this.dialogVisible1 = false\n    },\n    handleAddProject() {\n      this.dialogVisible = true\n      this.currentComponent = 'ProjectAdd'\n      this.title = '同步项目配置'\n    },\n    handleDialog(type, row) {\n      this.currentComponent = 'Add'\n      this.type = type\n      if (type === 'add') {\n        this.title = '新建'\n      } else {\n        this.title = '编辑'\n        this.rowInfo = row\n      }\n      this.dialogVisible = true\n    },\n    handleManage(row) {\n      this.currentComponent = 'ZClass'\n      this.title = '班组管理'\n      this.dialogVisible = true\n      this.$nextTick((_) => {\n        this.$refs.content.init(row)\n      })\n    },\n    // 从工厂级同步\n    handleAddFactory() {\n      RestoreFactoryProcessFromProject({\n        Sys_Project_Id: this.sysProjectId\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            message: '同步成功',\n            type: 'success'\n          })\n          this.fetchData()\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    handleDelete(processId) {\n      this.$confirm('是否删除当前工序?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DeleteProcess({\n            processId\n          }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                message: '删除成功',\n                type: 'success'\n              })\n              this.fetchData()\n            } else {\n              this.$message({\n                message: res.Message,\n                type: 'error'\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    },\n    async changeColumn() {\n      await this.getTableConfig(this.gridCode)\n      this.tbKey++\n    },\n    handleSearch() {\n      this.fetchData()\n    },\n    reset() {\n      this.formInline.name = ''\n      this.formInline.code = ''\n      this.fetchData()\n    },\n    setProjectData(data) {\n      this.sysProjectId = data.Sys_Project_Id\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/mixin.scss\";\n.app-container{\n  display: flex;\n  flex-direction: row;\n  height: 100%;\n  .card-x{\n    padding: 16px;\n    flex: 1;\n    display: flex;\n    flex-direction: column;\n    background-color: #ffffff;\n    .card-x-top{\n      margin-bottom: 16px;\n    }\n    .table-section {\n      flex: 1;\n      background: #fff;\n      border-radius: 4px;\n      overflow: hidden;\n    }\n  }\n}\n\n.cs-z-tb-wrapper {\n  height: 0;\n  flex: 1;\n}\n\n::v-deep {\n  .cs-top-header-box {\n    line-height: 0px;\n  }\n}\n\n.tb {\n  ::v-deep {\n    @include scrollBar;\n\n    &::-webkit-scrollbar {\n      width: 8px;\n    }\n  }\n}\n\n.cs-dialog {\n  ::v-deep {\n    .el-dialog__body {\n      padding: 20px 20px !important;\n      overflow: hidden;\n    }\n  }\n}\n\n.cs-tb-icon {\n  vertical-align: middle;\n}\n\n.cs-tag {\n  &:nth-child(2n) {\n    margin-left: 4px;\n  }\n\n  &:nth-child(n + 3) {\n    margin-top: 4px;\n  }\n}\n</style>\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-product-type\\component\\CompanyAdd.vue?vue&type=template&id=9bf0e608", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-product-type\\component\\CompanyAdd.vue", "mtime": 1757468128032}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImNvbXBhbnktYWRkLWNvbnRhaW5lciI+CiAgPGRpdiBjbGFzcz0idGl0bGUiPgogICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBAY2xpY2s9ImhhbmRsZUFkZFRvTGlzdCI+5Yqg5YWl5YiX6KGoPC9lbC1idXR0b24+CiAgPC9kaXY+CiAgPGVsLXRhYnMgdi1tb2RlbD0iYWN0aXZlVHlwZSIgdHlwZT0iY2FyZCIgQHRhYi1jbGljaz0iaGFuZGxlQ2xpY2siPgogICAgPGVsLXRhYi1wYW5lIHYtZm9yPSJpdGVtIGluIGJvbUxpc3QiIDprZXk9Iml0ZW0uQ29kZSIgOmxhYmVsPSJpdGVtLkRpc3BsYXlfTmFtZSIgOm5hbWU9Iml0ZW0uQ29kZSIgLz4KICA8L2VsLXRhYnM+CiAgPGRpdiBjbGFzcz0idHJlZS1jb250YWluZXIiPgogICAgPGVsLXRyZWUKICAgICAgcmVmPSJ0cmVlIgogICAgICB2LWxvYWRpbmc9ImxvYWRpbmciCiAgICAgIDpjdXJyZW50LW5vZGUta2V5PSJjdXJyZW50Tm9kZUtleSIKICAgICAgZWxlbWVudC1sb2FkaW5nLXRleHQ9IuWKoOi9veS4rSIKICAgICAgZWxlbWVudC1sb2FkaW5nLXNwaW5uZXI9ImVsLWljb24tbG9hZGluZyIKICAgICAgZW1wdHktdGV4dD0i5pqC5peg5pWw5o2uIgogICAgICBoaWdobGlnaHQtY3VycmVudAogICAgICBzaG93LWNoZWNrYm94CiAgICAgIG5vZGUta2V5PSJJZCIKICAgICAgZGVmYXVsdC1leHBhbmQtYWxsCiAgICAgIDpleHBhbmQtb24tY2xpY2stbm9kZT0iZmFsc2UiCiAgICAgIDpkYXRhPSJ0cmVlRGF0YSIKICAgICAgOnByb3BzPSJ7CiAgICAgICAgbGFiZWw6J0xhYmVsJywKICAgICAgICBjaGlsZHJlbjonQ2hpbGRyZW4nCiAgICAgIH0iCiAgICAgIEBub2RlLWNsaWNrPSJoYW5kbGVOb2RlQ2xpY2siCiAgICA+CiAgICAgIDxzcGFuIHNsb3Qtc2NvcGU9Insgbm9kZSwgZGF0YSB9IiBjbGFzcz0iY3VzdG9tLXRyZWUtbm9kZSI+CiAgICAgICAgPHN2Zy1pY29uCiAgICAgICAgICA6aWNvbi1jbGFzcz0iCiAgICAgICAgICAgIG5vZGUuZXhwYW5kZWQgPyAnaWNvbi1mb2xkZXItb3BlbicgOiAnaWNvbi1mb2xkZXInCiAgICAgICAgICAiCiAgICAgICAgICBjbGFzcy1uYW1lPSJjbGFzcy1pY29uIgogICAgICAgIC8+CiAgICAgICAgPHNwYW4gY2xhc3M9ImNzLWxhYmVsIiA6dGl0bGU9Im5vZGUubGFiZWwiPnt7IG5vZGUubGFiZWwgfX08L3NwYW4+CiAgICAgIDwvc3Bhbj4KICAgIDwvZWwtdHJlZT4KICA8L2Rpdj4KICA8Zm9vdGVyPgogICAgPGVsLWJ1dHRvbiBAY2xpY2s9ImhhbmRsZUNhbmNlbCI+5Y+WIOa2iDwvZWwtYnV0dG9uPgogICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBAY2xpY2s9ImhhbmRsZUFkZCI+56GuIOWumjwvZWwtYnV0dG9uPgogIDwvZm9vdGVyPgo8L2Rpdj4K"}, null]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\actually-sent\\v4\\index.vue?vue&type=style&index=0&id=497c75cf&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\actually-sent\\v4\\index.vue", "mtime": 1757468128067}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoudG90YWwtd3JhcHBlciB7DQogIGZsb2F0OiByaWdodDsNCiAgY29sb3I6ICMyOThkZmY7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWZhZmY7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgcGFkZGluZzogNnB4IDEwcHggNnB4IDEwcHg7DQogIG1hcmdpbi1yaWdodDogMTBweDsNCn0NCi5kYXRlLXBpY2tlci13cmFwcGVyIHsNCiAgZmxvYXQ6IHJpZ2h0Ow0KICB3aWR0aDogMjAlOw0KfQ0KOjp2LWRlZXAgLmZvcm0tc2VhcmNoIHsNCiAgd2lkdGg6IDEwMCU7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtd3JhcDogd3JhcDsNCiAgLmVsLWZvcm0taXRlbSB7DQogICAgd2lkdGg6IDI0JTsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICB9DQogIC5lbC1mb3JtLWl0ZW1fX2xhYmVsIHsNCiAgICB3aWR0aDogMTAwcHg7DQogIH0NCiAgLmVsLWZvcm0taXRlbV9fY29udGVudCB7DQogICAgbWluLXdpZHRoOiAxMHB4Ow0KICAgIGZsZXg6IDE7DQogIH0NCiAgLmVsLXNlbGVjdCB7DQogICAgd2lkdGg6IDEwMCU7DQogIH0NCn0NCi5saWNlbnNlLWJveCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICB3aWR0aDogMTAwcHg7DQogIGhlaWdodDogMjhweDsNCiAgYmFja2dyb3VuZDogIzgxOGZiNzsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBvdmVyZmxvdzogaGlkZGVuOw0KDQogIC5pbm5lci1ib3ggew0KICAgIHdpZHRoOiA5OHB4Ow0KICAgIGhlaWdodDogMjRweDsNCiAgICBib3JkZXI6IDFweCBzb2xpZCAjZmZmZmZmOw0KICAgIGJvcmRlci1yYWRpdXM6IDNweDsNCiAgICBjb2xvcjogI2ZmZmZmZjsNCiAgICBmb250LXNpemU6IDE0cHg7DQogIH0NCn0NCjo6di1kZWVwIC5jdXN0b20tcGFnaW5hdGlvbiAuY2hlY2tlZC1jb3VudCB7DQogIHRvcDogMjBweDsNCn0NCjo6di1kZWVwIC5wYWdpbmF0aW9uIHsNCiAganVzdGlmeS1jb250ZW50OiByaWdodCAhaW1wb3J0YW50Ow0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAymCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/shipment/actually-sent/v4", "sourcesContent": ["<!--成品发货-->\r\n<template>\r\n  <div\r\n    v-loading=\"pageLoading\"\r\n    class=\"abs100 cs-z-flex-pd16-wrap\"\r\n    style=\"display: flex; flex-direction: column\"\r\n  >\r\n    <div\r\n      class=\"cs-z-page-main-content\"\r\n      style=\"height: auto; margin-bottom: 16px\"\r\n    >\r\n      <top-header style=\"height: 100px; line-height: normal\">\r\n        <template #left>\r\n          <el-form\r\n            ref=\"searchForm\"\r\n            :inline=\"true\"\r\n            :model=\"form\"\r\n            class=\"demo-form-inline form-search\"\r\n            style=\"height: 100px\"\r\n          >\r\n            <el-form-item label=\"发货单号：\" prop=\"Code\">\r\n              <el-input\r\n                v-model=\"form.Code\"\r\n                clearable\r\n                placeholder=\"请输入\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"发货单状态：\" prop=\"Status\">\r\n              <el-select v-model=\"form.Status\" clearable placeholder=\"请选择\">\r\n                <!--                <el-option label=\"未发货\" :value=\"0\" />\r\n                <el-option label=\"已发货\" :value=\"1\" />\r\n                <el-option label=\"部分验收\" :value=\"2\" />\r\n                <el-option label=\"已验收\" :value=\"3\" />-->\r\n                <!--                <el-option label=\"草稿\" :value=\"0\" />\r\n                <el-option label=\"待过磅\" :value=\"2\" />\r\n                <el-option label=\"已过磅\" :value=\"3\" />\r\n                <el-option label=\"未验收\" :value=\"4\" />\r\n                <el-option label=\"部分验收\" :value=\"5\" />\r\n                <el-option label=\"已验收\" :value=\"6\" />-->\r\n\r\n                <el-option v-for=\"(item,key) in statusInfo\" :key=\"key\" :label=\"item\" :value=\"key\" />\r\n\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"项目名称\" prop=\"ProjectId\">\r\n              <el-select\r\n                v-model=\"form.ProjectId\"\r\n                class=\"w100\"\r\n                placeholder=\"请选择\"\r\n                filterable\r\n                clearable\r\n              >\r\n                <el-option\r\n                  v-for=\"item in projects\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Short_Name\"\r\n                  :value=\"item.Sys_Project_Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"收货人：\" prop=\"Consignee\">\r\n              <el-input\r\n                v-model=\"form.Consignee\"\r\n                clearable\r\n                placeholder=\"请输入\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"车牌号：\" prop=\"VehicleNo\">\r\n              <el-input\r\n                v-model=\"form.VehicleNo\"\r\n                clearable\r\n                placeholder=\"请输入\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"是否退货\" prop=\"IsReturn\">\r\n              <el-select v-model=\"form.IsReturn\" clearable placeholder=\"请选择\">\r\n                <el-option label=\"无退货\" :value=\"false\" />\r\n                <el-option label=\"有退货\" :value=\"true\" />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"过磅预警\" prop=\"Is_Weight_Warning\">\r\n              <el-select v-model=\"form.Is_Weight_Warning\" clearable placeholder=\"请选择\">\r\n                <el-option label=\"是\" :value=\"true\" />\r\n                <el-option label=\"否\" :value=\"false\" />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item>\r\n              <el-button\r\n                type=\"primary\"\r\n                @click=\"\r\n                  () => {\r\n                    form.PageInfo.Page = 1;\r\n                    getPageList();\r\n                  }\r\n                \"\r\n              >查询</el-button>\r\n              <el-button @click=\"resetForm('searchForm')\">重置</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </template>\r\n      </top-header>\r\n    </div>\r\n    <div class=\"cs-z-page-main-content\" style=\"flex: 1; display: -webkit-box\">\r\n      <div style=\"color: rgba(34, 40, 52, 0.65); padding: 10px 0px\">\r\n        <el-button type=\"primary\" @click=\"handleAdd\">新增发货单</el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          :disabled=\"!selectList.length\"\r\n          @click=\"handleExport\"\r\n        >导出发货单(pdf)</el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          :disabled=\"selectEmpty\"\r\n          @click=\"handleExportExcel\"\r\n        >导出发货单(excel)</el-button>\r\n        <ExportCustomReport code=\"Shipping_single_template\" style=\"margin:0 10px\" name=\"自定义发货单(excel)\" :ids=\"selectList.map(i=>i.Id)\"></ExportCustomReport>\r\n        <el-button\r\n          type=\"success\"\r\n          :disabled=\"!selectList.length\"\r\n          @click=\"handlePrint\"\r\n        >打印</el-button>\r\n        <!--        <el-button-->\r\n        <!--          type=\"success\"-->\r\n        <!--          @click=\"handlePrintNoWeight\"-->\r\n        <!--          :disabled=\"!selectRow\"-->\r\n        <!--          >预览(无重量)-->\r\n        <!--        </el-button>-->\r\n        <el-button\r\n          type=\"success\"\r\n          :loading=\"btnLoading\"\r\n          :disabled=\"tbData.length === 0\"\r\n          @click=\"handleLeadingOut\"\r\n        >导出</el-button>\r\n        <div class=\"date-picker-wrapper\">\r\n          <el-date-picker\r\n            v-model=\"form.dateRange\"\r\n            style=\"width: 100%\"\r\n            type=\"daterange\"\r\n            align=\"right\"\r\n            unlink-panels\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            :picker-options=\"pickerOptions\"\r\n            @change=\"datePickerwrapper\"\r\n          />\r\n        </div>\r\n        <div v-if=\"ProfessionalType\" class=\"total-wrapper\">\r\n          <span\r\n            style=\"margin: 0 24px 0 12px\"\r\n          >总数：{{ totalData.Allsteelamount }}</span><span>总重：{{ totalData.Allsteelweight }}（{{\r\n            ProfessionalType[0].Unit\r\n          }}）</span>\r\n        </div>\r\n      </div>\r\n      <div\r\n        v-loading=\"tbLoading\"\r\n        class=\"fff cs-z-tb-wrapper\"\r\n        style=\"flex: 1 1 auto\"\r\n      >\r\n        <dynamic-data-table\r\n          ref=\"dyTable\"\r\n          class=\"cs-plm-dy-table\"\r\n          :columns=\"comList\"\r\n          :config=\"tbConfig\"\r\n          :data=\"tbData\"\r\n          :page=\"queryInfo.Page\"\r\n          :total=\"total\"\r\n          border\r\n          stripe\r\n          @gridPageChange=\"handlePageChange\"\r\n          @gridSizeChange=\"handleSizeChange\"\r\n          @select=\"selectChange\"\r\n          @multiSelectedChange=\"handleSelectionChange\"\r\n          @selectAll=\"handleSelectAll\"\r\n        >\r\n          <template slot=\"SumNetWeight\" slot-scope=\"{ row }\">\r\n            {{ row.SumNetWeight | displayValue }}\r\n          </template>\r\n          <template slot=\"Status\" slot-scope=\"{ row }\">\r\n            {{ statusInfo[row.Status] }}\r\n          </template>\r\n          <template slot=\"Number\" slot-scope=\"{ row }\">\r\n            <div>{{ row.Number || '-' }}</div>\r\n          </template>\r\n          <template slot=\"SumReturnCount\" slot-scope=\"{ row }\">\r\n            <span>{{ row.SumReturnCount != null ? row.SumReturnCount : '-' }}</span>\r\n          </template>\r\n          <template slot=\"SumAcceptCount\" slot-scope=\"{ row }\">\r\n            <span>{{ row.SumAcceptCount != null ? row.SumAcceptCount : '-' }}</span>\r\n          </template>\r\n          <template slot=\"SumPendingCount\" slot-scope=\"{ row }\">\r\n            <span>{{ row.SumPendingCount != null ? row.SumPendingCount : '-' }}</span>\r\n          </template>\r\n          <template slot=\"SendDate\" slot-scope=\"{ row }\">\r\n            <div>\r\n              {{ row.SendDate || \"—\" }}\r\n            </div>\r\n          </template>\r\n          <template slot=\"op\" slot-scope=\"{ row, index }\">\r\n            <template v-if=\"row.Status == '0'\">\r\n              <el-button\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handleEdit(row.Id, false)\"\r\n              >编辑</el-button>\r\n              <el-button\r\n                v-if=\"Shipping_Weigh_Enabled\"\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handSubmit(row)\"\r\n              >提交过磅</el-button>\r\n              <template v-else>\r\n                <el-button v-if=\"getAuditStatus(row)\" type=\"text\" @click=\"submitForReview(row)\">提交审核</el-button>\r\n                <el-button\r\n                  v-else\r\n                  :index=\"index\"\r\n                  type=\"text\"\r\n                  @click=\"handleSub(row)\"\r\n                >提交发货</el-button>\r\n              </template>\r\n              <el-button\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                style=\"color:red\"\r\n                @click=\"handleDel(row.Id)\"\r\n              >删除</el-button>\r\n            </template>\r\n            <template v-else>\r\n              <el-button\r\n                v-if=\"row.Status == '2'\"\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handleWithdraw(row.Id)\"\r\n              >撤回草稿</el-button>\r\n              <el-button\r\n                v-if=\"row.Status != '999'\"\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handleEdit(row.Id, row.Status!='-1')\"\r\n              >编辑</el-button>\r\n              <el-button\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handleInfo(row.Id)\"\r\n              >查看</el-button>\r\n              <el-button\r\n                v-if=\"[4,5,6].includes(+row.Status)\"\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handleChange(row.Id)\"\r\n              >变更记录</el-button>\r\n              <el-button\r\n                v-if=\"Is_Integration&&[4,5,6].includes(+row.Status)\"\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handelView(row.Id)\"\r\n              >验收情况</el-button>\r\n            </template>\r\n            <el-button v-if=\"row.Status==999\" type=\"text\" @click=\"handleCancelFlow(row.FlowId)\">撤回</el-button>\r\n            <template v-if=\"row.Status==3\">\r\n              <el-button\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handleWithdraw(row.Id)\"\r\n              >撤回草稿</el-button>\r\n              <el-button v-if=\"getAuditStatus(row)\" type=\"text\" @click=\"submitForReview(row)\">提交审核</el-button>\r\n              <el-button\r\n                v-else\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handleSub(row)\"\r\n              >提交发货</el-button>\r\n            </template>\r\n            <el-button v-if=\"row.FlowId\" type=\"text\" @click=\"handleMonitor(row.FlowId)\">监控</el-button>\r\n            <template v-if=\"row.Status==-1\">\r\n              <el-button v-if=\"Shipping_Weigh_Enabled\" type=\"text\" @click=\"handSubmit(row)\">提交过磅</el-button>\r\n              <template v-else>\r\n                <el-button v-if=\"getAuditStatus(row)\" type=\"text\" @click=\"submitForReview(row)\">提交审核</el-button>\r\n                <el-button\r\n                  v-else\r\n                  :index=\"index\"\r\n                  type=\"text\"\r\n                  @click=\"handleSub(row)\"\r\n                >提交发货</el-button>\r\n              </template>\r\n            </template>\r\n          </template>\r\n          <!-- <template slot=\"Out_Date\" slot-scope=\"{ row }\">\r\n            {{ row.Out_Date | timeFormat }}\r\n          </template> -->\r\n        </dynamic-data-table>\r\n      </div>\r\n      <el-dialog\r\n        v-dialogDrag\r\n        title=\"新增发货单\"\r\n        class=\"plm-custom-dialog\"\r\n        :visible.sync=\"dialogVisible\"\r\n        width=\"30%\"\r\n        @close=\"handleClose\"\r\n      >\r\n        <el-form\r\n          ref=\"form2\"\r\n          :model=\"form2\"\r\n          :rules=\"rules\"\r\n          label-width=\"70px\"\r\n          class=\"demo-ruleForm\"\r\n        >\r\n          <el-form-item label=\"项目\" prop=\"ProjectId\">\r\n            <el-select\r\n              v-model=\"form2.ProjectId\"\r\n              class=\"w100\"\r\n              placeholder=\"请选择\"\r\n              filterable\r\n              clearable\r\n              @change=\"projectIdChange\"\r\n              @clear=\"projectIdClear\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in projects\"\r\n                :key=\"item.Id\"\r\n                :label=\"item.Short_Name\"\r\n                :value=\"item.Id\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item style=\"text-align: right\">\r\n            <el-button @click=\"resetForm2('form2')\">取 消</el-button>\r\n            <el-button\r\n              type=\"primary\"\r\n              @click=\"submitForm2('form2')\"\r\n            >确 定</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-dialog>\r\n    </div>\r\n    <PrintDialog ref=\"PrintDialog\" />\r\n    <radioDialog\r\n      ref=\"radioDialog\"\r\n      :send-id=\"selectRow.Id\"\r\n      :title=\"title\"\r\n      :send-data=\"selectRow\"\r\n    />\r\n    <dialogExcel\r\n      ref=\"dialogExcel\"\r\n      :send-id=\"selectRow.Id\"\r\n      :title=\"title\"\r\n      :send-data=\"selectRow\"\r\n    />\r\n    <checkDialog ref=\"checkDialog\" />\r\n    <Monitor ref=\"monitor\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Monitor from '@/components/Monitor/index.vue'\r\nimport TopHeader from '@/components/TopHeader/index.vue'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable.vue'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info-pro2'\r\nimport {\r\n  GetProjectSendingInfoPagelist,\r\n  DeleteProjectSendingInfo,\r\n  SubmitProjectSending,\r\n  GetProjectSendingAllCount,\r\n  TransformsWithoutWeight,\r\n  SubmitWeighingForPC,\r\n  WithdrawDraft,\r\n  ExportInvoiceList, SubmitApproval, CancelFlow\r\n} from '@/api/PRO/component-stock-out'\r\nimport { GetProjectPageList } from '@/api/PRO/pro-schedules'\r\nimport { GeAreaTrees } from '@/api/PRO/project'\r\nimport { GetInstallUnitPageList } from '@/api/PRO/install-unit'\r\nimport { parseTime } from '@/utils'\r\nimport { baseUrl } from '@/utils/baseurl'\r\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\r\nimport PrintDialog from './component/printDialog.vue'\r\nimport radioDialog from './component/dialog.vue'\r\nimport dialogExcel from './component/dialogExcel.vue'\r\nimport checkDialog from './component/check.vue'\r\nimport { mapGetters } from 'vuex'\r\nimport ExportCustomReport from \"@/components/ExportCustomReport/index.vue\";\r\n\r\nconst StatusMap = {\r\n  0: '草稿',\r\n  2: '待过磅',\r\n  3: '已过磅',\r\n  4: '未验收',\r\n  5: '部分验收',\r\n  6: '已验收',\r\n  7: '已退货',\r\n  999: '审批中',\r\n  '-1': '已退回'\r\n}\r\n\r\nexport default {\r\n  components: {\r\n    ExportCustomReport,\r\n    TopHeader,\r\n    Monitor,\r\n    DynamicDataTable,\r\n    PrintDialog,\r\n    radioDialog,\r\n    checkDialog,\r\n    dialogExcel\r\n  },\r\n  filters: {\r\n    sendDateFilter(e) {\r\n      // console.log(e,\"eeee\");\r\n      return parseTime(new Date(e))\r\n    }\r\n  },\r\n  mixins: [addRouterPage, getTbInfo],\r\n  data() {\r\n    return {\r\n      selectList: [],\r\n      statusInfo: StatusMap,\r\n      IsVisabel: false,\r\n      btnLoading: false,\r\n      form: {\r\n        Code: '',\r\n        Status: null,\r\n        ProjectId: '',\r\n        VehicleNo: '',\r\n        Consignee: '',\r\n        IsReturn: null,\r\n        Is_Weight_Warning: null,\r\n        dateRange: ['', ''],\r\n        PageInfo: {\r\n          ParameterJson: [],\r\n          Page: 1,\r\n          PageSize: 20\r\n        }\r\n      },\r\n      form2: {\r\n        ProjectId: '',\r\n        Area_Id: '',\r\n        InstallUnit_Id: ''\r\n      },\r\n      rules: {\r\n        ProjectId: [{ required: true, message: '请选择', trigger: 'change' }]\r\n        // Area_Id: [{ required: true, message: \"请选择\", trigger: \"change\" }],\r\n      },\r\n      pickerOptions: {\r\n        shortcuts: [\r\n          {\r\n            text: '今天',\r\n            onClick(picker) {\r\n              const end = new Date()\r\n              const start = new Date()\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 1)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          },\r\n          {\r\n            text: '最近一周',\r\n            onClick(picker) {\r\n              const end = new Date()\r\n              const start = new Date()\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          },\r\n          {\r\n            text: '最近一个月',\r\n            onClick(picker) {\r\n              const end = new Date()\r\n              const start = new Date()\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      selectParams: {\r\n        clearable: true,\r\n        placeholder: '请选择'\r\n      },\r\n      ProjectId: '',\r\n      dialogVisible: false,\r\n      pageLoading: false,\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/add',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/shipment/actually-sent/v4/add.vue'),\r\n          name: 'PROShipSentAdd',\r\n          meta: { title: '新建发货单' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/edit',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/shipment/actually-sent/v4/edit.vue'),\r\n          name: 'PROShipSentEdit',\r\n          meta: { title: '编辑发货单' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/detail',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/shipment/actually-sent/v4/detail.vue'),\r\n          name: 'PROShipSentDetail',\r\n          meta: { title: '详情' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/changeRecord',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/shipment/actually-sent/v4/changeRecord.vue'),\r\n          name: 'PROShipSentChangeRecord',\r\n          meta: { title: '发货单变更记录' }\r\n        }\r\n      ],\r\n      projects: [],\r\n      // 区域数据\r\n      treeParamsArea: {\r\n        'check-strictly': true,\r\n        'expand-on-click-node': false,\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      styles: { width: '100%' },\r\n      SetupPositionData: [],\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: 10,\r\n        ParameterJson: []\r\n      },\r\n      queryInfo2: {\r\n        BeginDate: '',\r\n        EndDate: '',\r\n        PageInfo: {\r\n          ParameterJson: [],\r\n          Page: 1,\r\n          PageSize: 2\r\n        }\r\n      },\r\n      tbConfig: {\r\n        Pager_Align: 'center',\r\n        Op_Width: 280\r\n      },\r\n      columns: [],\r\n      tbData: [],\r\n      total: 0,\r\n      tbLoading: false,\r\n      selectRow: {},\r\n      totalData: {\r\n        Allsteelamount: 0,\r\n        Allsteelweight: 0\r\n      },\r\n      ProfessionalType: null,\r\n      title: '',\r\n      Is_Integration: false // 是否一体化\r\n    }\r\n  },\r\n  computed: {\r\n    selectEmpty() {\r\n      return Object.keys(this.selectRow).length === 0\r\n    },\r\n    comList() {\r\n      if (!this.Is_Integration) {\r\n        return this.columns.filter((item) => {\r\n          return (\r\n            item.Code !== 'SumAcceptCount' && item.Code !== 'SumPendingCount'\r\n          )\r\n        })\r\n      } else {\r\n        return this.columns\r\n      }\r\n    },\r\n    ...mapGetters('factoryInfo', ['Component_Shipping_Approval', 'autoGenerate', 'Shipping_Approval_LowerLimit', 'Shipping_Weigh_Enabled'])\r\n  },\r\n  async activated() {\r\n    console.log('activated')\r\n    if (this.$route.query.refresh) {\r\n      this.fetchData()\r\n    }\r\n    // this.fetchData()\r\n  },\r\n  async created() {\r\n    this.$store.dispatch('factoryInfo/getWorkshop')\r\n\r\n    this.Is_Integration = await this.$store.dispatch('user/getPreferenceSetting', 'Is_Integration')\r\n    this.getFactoryTypeOption()\r\n    this.getProjectPageList()\r\n  },\r\n  mounted() {\r\n    console.log('mounted')\r\n  },\r\n  methods: {\r\n    handleCancelFlow(instanceId) {\r\n      this.$confirm('是否撤回?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        CancelFlow({\r\n          instanceId\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '操作成功',\r\n              type: 'success'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    submitForReview(row) {\r\n      const Id = row.Id\r\n      if (!row.VehicleNo) {\r\n        this.$message({\r\n          message: '发货单车辆信息未完善，请完善后提交',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n\r\n      this.$confirm('是否提交审核?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        SubmitApproval({\r\n          Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.fetchData()\r\n            this.$message({\r\n              message: '操作成功',\r\n              type: 'success'\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    handleMonitor(rowId) {\r\n      this.$refs['monitor'].opendialog(rowId, false)\r\n    },\r\n    getAuditStatus(row) {\r\n      return this.Component_Shipping_Approval && (this.Shipping_Approval_LowerLimit || 0) * 1000 < row.Project_Sending_Weight\r\n    },\r\n    async getFactoryTypeOption() {\r\n      await GetFactoryProfessionalByCode({\r\n        factoryId: localStorage.getItem('CurReferenceId')\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.ProfessionalType = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      await this.getTableConfig(\r\n        `pro_component_out_bill_list,${this.ProfessionalType[0].Code}`\r\n      )\r\n      this.fetchData()\r\n    },\r\n    fetchData() {\r\n      this.tbLoading = true\r\n      const form = { ...this.form }\r\n      delete form['dateRange']\r\n      this.form.dateRange = this.form.dateRange || []\r\n      form.BeginDate = parseTime(this.form.dateRange[0])\r\n        ? parseTime(this.form.dateRange[0])\r\n        : ''\r\n      form.EndDate = parseTime(this.form.dateRange[1])\r\n        ? parseTime(this.form.dateRange[1])\r\n        : ''\r\n      GetProjectSendingInfoPagelist(form).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.Data.map((v) => {\r\n            v.SendDate = v.SendDate\r\n              ? parseTime(new Date(v.SendDate), '{y}-{m}-{d}')\r\n              : v.SendDate\r\n            return v\r\n          })\r\n          // this.tbData = res.Data.Data;\r\n          this.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.tbLoading = false\r\n      })\r\n      GetProjectSendingAllCount({ ...form }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          // console.log(res.Data,\"res.Data\");\r\n          this.totalData = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getPageList() {\r\n      this.fetchData()\r\n      console.log(this.form, 'this.form')\r\n    },\r\n    handSubmit(row) {\r\n      const Id = row.Id\r\n      if (!row.VehicleNo) {\r\n        this.$message({\r\n          message: '发货单车辆信息未完善，请完善后提交',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      this.$confirm('是否提交过磅?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        SubmitWeighingForPC({\r\n          Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.fetchData()\r\n            this.$message({\r\n              message: res.Data,\r\n              type: 'success'\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    datePickerwrapper() {\r\n      // console.log(form,\"form1111111111\");\r\n      // console.log(new Date(\"2022-09-28T10:12:35.583Z\"),\"new Date111\");\r\n      // console.log(parseTime(new Date(\"2022-10-11T14:03:54\")),\"new Date222\");\r\n      if (!this.form.dateRange) {\r\n        this.form.dateRange = ['', '']\r\n      }\r\n      this.fetchData()\r\n    },\r\n    resetForm(formName) {\r\n      this.$refs[formName].resetFields()\r\n      this.fetchData()\r\n    },\r\n    submitForm2(formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          const { ProjectId, Area_Id, InstallUnit_Id } = this.form2\r\n          const {\r\n            Name,\r\n            Id,\r\n            Code,\r\n            SPIC_UserName,\r\n            Address,\r\n            Receiver,\r\n            Receiver_Tel,\r\n            Sys_Project_Id,\r\n            Receive_UserName\r\n          } = this.projects.find((v) => v.Id === this.form2.ProjectId)\r\n          const data = {\r\n            ProjectId,\r\n            Area_Id,\r\n            InstallUnit_Id,\r\n            Id,\r\n            Name,\r\n            Code,\r\n            Address,\r\n            Receiver,\r\n            Receiver_Tel,\r\n            Sys_Project_Id,\r\n            Receive_UserName,\r\n            autoGenerate: this.autoGenerate,\r\n            ProfessionalType: this.ProfessionalType\r\n          }\r\n          this.$router.push({\r\n            name: 'PROShipSentAdd',\r\n            query: {\r\n              pg_redirect: 'PROShipSent',\r\n              p: encodeURIComponent(JSON.stringify(data))\r\n            }\r\n          })\r\n          this.dialogVisible = false\r\n          this.$refs.form2.resetFields()\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    resetForm2(formName) {\r\n      this.dialogVisible = false\r\n      this.$refs[formName].resetFields()\r\n    },\r\n    handleClose() {\r\n      this.$refs.form2.resetFields()\r\n      this.dialogVisible = false\r\n    },\r\n    handleAdd() {\r\n      this.dialogVisible = true\r\n      // this.$router.push({ name: 'PROShipSentAdd', query: { pg_redirect: 'PROShipSent' }})\r\n    },\r\n    getProjectPageList() {\r\n      GetProjectPageList({ PageSize: -1 }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.projects = res.Data.Data\r\n        }\r\n      })\r\n    },\r\n    projectIdChange(e) {\r\n      console.log(e, 'e')\r\n      // if (e) {\r\n      //   this.getAreaList();\r\n      // }\r\n    },\r\n    projectIdClear(e) {\r\n      this.$refs.form2.resetFields()\r\n    },\r\n    // 获取区域\r\n    getAreaList() {\r\n      GeAreaTrees({\r\n        projectId: this.form2.ProjectId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.treeParamsArea.data = res.Data\r\n          this.$nextTick((_) => {\r\n            this.$refs.treeSelectArea.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    filterFun(val, ref) {\r\n      this.$refs[ref].filterFun(val)\r\n    },\r\n    areaChange(e) {\r\n      console.log(e, 'e')\r\n      this.getInstall()\r\n    },\r\n    // 清空区域\r\n    areaClear() {\r\n      this.form2.Area_Id = ''\r\n      this.form.InstallUnit_Id = ''\r\n    },\r\n    // 获取批次\r\n    getInstall() {\r\n      GetInstallUnitPageList({\r\n        Area_Id: this.form2.Area_Id,\r\n        Page: 1,\r\n        PageSize: -1\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          if (res.IsSucceed) {\r\n            this.SetupPositionData = res.Data.Data\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleEdit(id, isSub) {\r\n      this.$router.push({\r\n        name: 'PROShipSentEdit',\r\n        query: { pg_redirect: 'PROShipSent', id, isSub: isSub ? '1' : '0',\r\n          p: encodeURIComponent(JSON.stringify({ autoGenerate: this.autoGenerate }))\r\n        }\r\n      })\r\n    },\r\n    // 撤回至草稿\r\n    handleWithdraw(id) {\r\n      this.$confirm('撤回至草稿, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          WithdrawDraft({\r\n            id: id\r\n          }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                message: '撤销成功',\r\n                type: 'success'\r\n              })\r\n              this.fetchData()\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => { })\r\n    },\r\n    handleSub(row) {\r\n      const id = row.Id\r\n      if (!row.VehicleNo) {\r\n        this.$message({\r\n          message: '发货单车辆信息未完善，请完善后提交',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      this.$confirm('提交该发货单, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          this.pageLoading = true\r\n          SubmitProjectSending({\r\n            Id: id\r\n          }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                message: '发货成功',\r\n                type: 'success'\r\n              })\r\n              this.fetchData()\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n            this.pageLoading = false\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.pageLoading = false\r\n        })\r\n    },\r\n    handleDel(id) {\r\n      this.$confirm('是否删除该发货单?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        DeleteProjectSendingInfo({\r\n          Id: id\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '删除成功',\r\n              type: 'success'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    },\r\n    handleInfo(id) {\r\n      this.$router.push({\r\n        name: 'PROShipSentDetail',\r\n        query: { pg_redirect: 'PROShipSent', id }\r\n      })\r\n    },\r\n    handleChange(id) {\r\n      this.$router.push({\r\n        name: 'PROShipSentChangeRecord',\r\n        query: { pg_redirect: 'PROShipSent', id }\r\n      })\r\n    },\r\n    // printMe() {},\r\n    handleExport() {\r\n      this.title = '导出'\r\n      this.$nextTick(_ => {\r\n        this.$refs.radioDialog.handleOpen(this.selectList)\r\n      })\r\n    },\r\n    handleExportExcel() {\r\n      this.title = '导出'\r\n      this.$refs.dialogExcel.handleOpen(this.selectList)\r\n    },\r\n    handlePrint() {\r\n      this.title = '打印'\r\n      this.$refs.radioDialog.handleOpen(this.selectList)\r\n    },\r\n    handlePrintNoWeight() {\r\n      // console.log(this.selectRow.Code, \"this.selectRow.Code\");\r\n      // console.log(this.selectRow.Id, \"this.selectRow.Id\");\r\n      TransformsWithoutWeight({\r\n        sendId: this.selectRow.Id\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          // const templateUrl = combineURL(this.$baseUrl, res.Data);\r\n          // window.open(templateUrl, \"_blank\");\r\n          const url = new URL(res.Data, baseUrl())\r\n          window.open(url.href, '_blank')\r\n          this.$message({\r\n            type: 'success',\r\n            message: '打印成功!'\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      // this.$refs.PrintDialog.open(this.selectRow.Code)\r\n    },\r\n\r\n    // 导出\r\n    handleLeadingOut() {\r\n      this.btnLoading = true\r\n      const form = { ...this.form }\r\n      delete form['dateRange']\r\n      delete form['PageInfo']\r\n      this.form.dateRange = this.form.dateRange || []\r\n      form.BeginDate = parseTime(this.form.dateRange[0])\r\n        ? parseTime(this.form.dateRange[0])\r\n        : ''\r\n      form.EndDate = parseTime(this.form.dateRange[1])\r\n        ? parseTime(this.form.dateRange[1])\r\n        : ''\r\n      ExportInvoiceList({ ...form })\r\n        .then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message.success('导出成功')\r\n            const url = new URL(res.Data, baseUrl())\r\n            window.open(url.href)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n        .finally(() => {\r\n          // 结束loading\r\n          this.btnLoading = false\r\n        })\r\n    },\r\n    handleSelectionChange(list) {\r\n      this.selectList = list\r\n    },\r\n    selectChange({ selection, row }) {\r\n      this.$refs.dyTable.$refs.dtable.clearSelection()\r\n      if (selection.length != 0) {\r\n        this.selectRow = row\r\n      } else {\r\n        this.selectRow = {}\r\n      }\r\n      if (selection.length > 1) {\r\n        selection.shift()\r\n      }\r\n      // console.log(selection, \"selection2\");\r\n      this.$refs.dyTable.$refs.dtable.toggleRowSelection(\r\n        row,\r\n        !!selection.length\r\n      )\r\n    },\r\n    handleSelectAll() {\r\n      // this.$refs.dyTable.$refs.dtable.clearSelection()\r\n    },\r\n    handelView(Id) {\r\n      this.IsVisabel = true\r\n      this.$nextTick((_) => {\r\n        this.$refs.checkDialog.handelOpen(Id)\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.total-wrapper {\r\n  float: right;\r\n  color: #298dff;\r\n  background-color: #f5faff;\r\n  font-size: 14px;\r\n  padding: 6px 10px 6px 10px;\r\n  margin-right: 10px;\r\n}\r\n.date-picker-wrapper {\r\n  float: right;\r\n  width: 20%;\r\n}\r\n::v-deep .form-search {\r\n  width: 100%;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  .el-form-item {\r\n    width: 24%;\r\n    display: flex;\r\n  }\r\n  .el-form-item__label {\r\n    width: 100px;\r\n  }\r\n  .el-form-item__content {\r\n    min-width: 10px;\r\n    flex: 1;\r\n  }\r\n  .el-select {\r\n    width: 100%;\r\n  }\r\n}\r\n.license-box {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100px;\r\n  height: 28px;\r\n  background: #818fb7;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n\r\n  .inner-box {\r\n    width: 98px;\r\n    height: 24px;\r\n    border: 1px solid #ffffff;\r\n    border-radius: 3px;\r\n    color: #ffffff;\r\n    font-size: 14px;\r\n  }\r\n}\r\n::v-deep .custom-pagination .checked-count {\r\n  top: 20px;\r\n}\r\n::v-deep .pagination {\r\n  justify-content: right !important;\r\n}\r\n</style>\r\n"]}]}
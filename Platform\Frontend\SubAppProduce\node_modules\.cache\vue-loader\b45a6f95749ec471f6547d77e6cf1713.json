{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new\\draft.vue?vue&type=template&id=112ae276&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new\\draft.vue", "mtime": 1757468128014}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}
<template>
  <div class="bom-level-config abs100 cs-z-flex-pd16-wrap">
    <div class="cs-z-page-main-content">
      <div class="title">
        <i class="el-icon-warning" />
        自行配置BOM层级数，<span class="title-span">最多可新增5层</span>
      </div>
      <div class="box-wrapper">
        <div
          v-for="(layer, idx) in visibleLayers"
          :key="layer.key"
          class="box"
          :class="layer.color"
        >
          <span v-if="idx !== 0 && idx !== visibleLayers.length - 1" class="close-icon" @click="handleClose(layer, idx)" />
          <div class="box-title">BOM{{ numToHan(idx) }}层</div>
          <div class="box-subtitle">{{ layer.subtitle }}</div>
          <div class="box-input">
            <el-input
              v-model.trim="layer.title"
              maxlength="20"
              size="medium"
              class="cs-input"
              placeholder="请输入"
              @blur="handleInputBlur"
            />
            <i class="el-icon-edit-outline" />
          </div>
          <el-divider />
          <div class="box-bottom">
            <div class="box-bottom-label">
              <span
                class="cs-bom-btn cs-bom-btn-main"
                :style="{background: layer.color === 'color1' ? '#298DFF' : layer.color === 'color2' ? '#3ECC93' : layer.color === 'color3' ? '#F1B430' : layer.color === 'color4' ? '#426BD8' : '#FF7D23'}"
                @click="handleBottomLabelClick(layer, idx)"
              >清单配置</span>
              <span
                class="cs-bom-btn cs-bom-btn-model"
                :class="{selected: layer.Is_Default_Model}"
                :style="layer.Is_Default_Model ? {borderColor: getMainColor(layer), color: getMainColor(layer)} : {borderColor: 'transparent', color: '#999'}"
                @click="handleModelDefaultClick(layer)"
              >
                模型默认层级
                <span v-if="layer.Is_Default_Model" class="cs-bom-btn-check" :style="{color: getMainColor(layer)}">
                  <svg width="11" height="11" viewBox="0 0 11 11">
                    <rect width="11" height="11" rx="2" fill="currentColor" />
                    <text x="1.5" y="9" font-size="10" fill="#fff">✓</text>
                  </svg>
                </span>
              </span>
            </div>
            <span class="box-bottom-value">{{ (idx+1).toString().padStart(2, '0') }}</span>
          </div>
        </div>
        <div v-if="visibleLayers.length < 5" class="box add-box" @click="handleAddLayer">
          <div class="add-plus">+</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { GetBomLevelList, SaveBomLevel } from '@/api/PRO/bom-level'
import { deepClone } from '@/utils'
import addRouterPage from '@/mixins/add-router-page'
export default {
  mixins: [addRouterPage],
  data() {
    return {

      apiData: [], // GetBomLevelList接口数据
      tbLoading: false,
      selectedType: 2
    }
  },
  computed: {
    visibleLayers() {
      // 一层和五层始终显示，中间层根据Is_Enabled
      if (!this.apiData || this.apiData.length === 0) return []
      const arr = this.apiData.filter((item, idx) => {
        if (idx === 0 || idx === 4) return true
        return !!item.Is_Enabled
      })
      return arr
    },
    comName() {
      if (!this.apiData || this.apiData.length === 0) return ''
      return this.apiData[0].Display_Name + '清单'
    },
    partName() {
      if (!this.apiData || this.apiData.length === 0) return ''
      return this.apiData[4].Display_Name + '清单'
    },
    addPageArray() {
      const unitPart = this.defaultData.filter(item => item.Is_Enabled && +item.Code > 0)
      const route = [{
        path: this.$route.path + '/ComponentConfig',
        hidden: true,
        component: () => import('@/views/PRO/bom-setting/com-config/index'),
        name: 'PROComponentConfig',
        meta: { title: this.comName }
      },
      {
        path: this.$route.path + '/part-config',
        hidden: true,
        component: () => import('@/views/PRO/bom-setting/part-config/index'),
        name: 'PROPartsConfig',
        meta: { title: this.partName }
      }]
      const curList = []
      if (unitPart.length > 0) {
        unitPart.forEach(item => {
          curList.push({
            path: this.$route.path + `/half-part-config${item.Code}`,
            hidden: true,
            component: () => import('@/views/PRO/bom-setting/half-part-config/index'),
            name: 'PROHalfPartConfig' + item.Code,
            meta: { title: item.Display_Name + '清单' }
          })
        })
      }
      route.splice(1, 0, ...curList)
      console.log('route', route)
      return route
    }
  },
  async mounted() {
    await this.getBomLevelList()
    this.handleInitPageRoute()
    console.log('this.addPageArray', this.$router.getRoutes())
  },
  methods: {
    initPage() {
      console.log('hello word 存在即合理')
    },
    handleClose(layer, idx) {
      this.$confirm('确定要关闭该层级吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (idx === 0 || idx === this.visibleLayers.length - 1) return
        const target = this.apiData.find(l => l.key === layer.key)
        if (target) target.Is_Enabled = false
        this.saveBomLevelList()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消关闭'
        })
      })
    },
    handleAddLayer() {
      const canAdd = this.apiData.find((l, idx) => idx !== 0 && idx !== 4 && !l.Is_Enabled)
      if (canAdd) {
        canAdd.Is_Enabled = true
        this.saveBomLevelList()
      }
    },
    handleInputBlur() {
      this.saveBomLevelList()
    },
    saveBomLevelList() {
      // console.log('this.apiData', this.apiData)
      let flag = true
      const payload = this.apiData.map(item => {
        const { key, title, bottomValue, subtitle, color, ...others } = item

        others.Display_Name = title
        if (title === '') {
          flag = false
        }
        return others
      })
      if (!flag) {
        this.$message.error('请输入BOM层级名称')
        return
      }
      const data1 = JSON.stringify(this.defaultData)
      const data2 = JSON.stringify(payload)
      if (data1 === data2) {
        console.log('没有变化')
        return
      }
      console.log('payload', JSON.parse(JSON.stringify(payload)))

      this.$confirm('确定要保存所有修改吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.btnLoading = true
        SaveBomLevel(payload).then(res => {
          if (res.IsSucceed) {
            this.$message.success('保存成功')
            this.$store.dispatch('bomInfo/clearBomLevelCache')
            this.getBomLevelList()
          } else {
            this.$message.error(res.Message || '保存失败')
          }
        }).catch(error => {
          console.error('保存失败:', error)
          this.$message.error('保存失败')
        }).finally(() => {
          this.btnLoading = false
        })
      }).catch(() => {
        this.$message.info('已取消保存')
        this.apiData.forEach(item => {
          const defaultItem = this.defaultData.find(d => d.Code === item.Code)
          item.Is_Default_Model = defaultItem.Is_Default_Model
        })
      })

      // this.handleInitPageRoute()
      // 调用保存接口
      // SaveBomLevelList(payload).then(...)
    },
    handleBottomLabelClick(layer, idx) {
      const _name = layer.Code === '-1' ? 'PROComponentConfig' : layer.Code === '0' ? 'PROPartsConfig' : 'PROHalfPartConfig' + layer.Code
      console.log('_name', _name)
      const query = { pg_redirect: this.$route.name }
      if (+layer.Code > 0) {
        query.level = layer.Code
      }
      this.$router.push({ name: _name, query })
      // 这里可加保存逻辑
    },
    handleModelDefaultClick(layer) {
      this.apiData.forEach(l => { l.Is_Default_Model = false })
      layer.Is_Default_Model = true
      this.saveBomLevelList()
    },
    handleInputEditClick() {
      this.inputEdit = true
    },
    getBomLevelList() {
      this.tbLoading = true
      return new Promise((resolve) => {
        GetBomLevelList().then(res => {
          const { IsSucceed, Message, Data } = res
          if (IsSucceed) {
            this.defaultData = deepClone(Data)
            this.apiData = (Data || []).map((v, index) => {
              v.key = v.Code
              v.subtitle = index === 0 ? 'layer One' : index === 1 ? 'layer Two' : index === 2 ? 'layer Three' : index === 3 ? 'layer Four' : 'layer Five'
              v.color = index === 0 ? 'color1' : index === 1 ? 'color2' : index === 2 ? 'color3' : index === 3 ? 'color4' : 'color5'
              v.bottomValue = (index + 1).toString().padStart(2, '0')
              v.title = v.Display_Name
              return v
            })
            if (this.apiData && this.apiData.length > 0) {
              const enabledCount = this.apiData.filter(item => !!item.Is_Enabled).length
              if (enabledCount >= 2) {
                this.selectedType = enabledCount
              } else {
                this.selectedType = 2
              }
            }
          } else {
            this.$message({
              message: Message || '获取BOM层级列表失败',
              type: 'error'
            })
          }
          console.log('this.apiData', this.apiData)
          resolve()
        }).catch(error => {
          console.error('获取BOM层级列表失败:', error)
          this.$message.error('获取BOM层级列表失败')
          resolve()
        }).finally(() => {
          this.tbLoading = false
        })
      })
    },
    numToHan(num) {
      const hanArr = ['一', '二', '三', '四', '五']
      return hanArr[num] || num
    },
    getMainColor(layer) {
      return layer.color === 'color1' ? '#298DFF' : layer.color === 'color2' ? '#3ECC93' : layer.color === 'color3' ? '#F1B430' : layer.color === 'color4' ? '#426BD8' : '#FF7D23'
    }
  }
}
</script>
<style lang="scss" scoped>
.cs-z-page-main-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 50px 20px;
  overflow: hidden;
  width: 100%;

}

.title {
  margin:100px 0 70px 0;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 16px;
  color: #222834;

  .title-span,
  .el-icon-warning {
    color: #FF7C19;
  }
}

.box-wrapper {
  display: flex;
  overflow-x: auto;
  width: 95%;
}

.box {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 20px;
  min-width: 280px;
  height: 340px;
  margin: 12px;
  border-radius: 4px 4px 4px 4px;
  position: relative;

  .box-title {
    display: flex;
    justify-content: center;
    height: 35px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 26px;
    color: #333333;
    margin-top: 30px;
  }

  .box-subtitle {
    display: flex;
    justify-content: center;
    height: 19px;
    margin-top: 8px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #999999;
  }

  .box-input {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: center;
    margin-top: 56px;
    gap: 8px;
  }

  .close-icon {
    width: 27px;
    height: 27px;
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      right: 0;
      width: 0;
      height: 0;
      border-top: 27px solid #ACD4FF;
      border-right: 27px solid #ACD4FF;
      border-bottom: 27px solid transparent;
      border-left: 27px solid transparent;
      z-index: 0;
    }

    &::after {
      content: "×";
      position: relative;
      z-index: 1;
      color: #ffffff;
      font-size: 24px;
      font-weight: bold;
      left: 1px;
      top: 1px;
      pointer-events: none;
    }
  }
  .el-icon-edit-outline{
    font-size: 18px;
  }

  .box-bottom {
    flex: 1;
    margin-top: 8px;
    display: flex;
    align-items: center;
    position: relative;

    .box-bottom-label {
      z-index: 1;
      align-self: flex-end;
      padding-bottom: 12px;

    }

    .box-bottom-value {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 0;
      font-size: 72px;
      font-family: STHupo, STHupo;
      font-weight: 400;
      font-size: 72px;
    }
  }
}

.add-box {
  background: #F4F5F6;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 280px;
  height: 323px;
  margin: 12px;
  border-radius: 4px;
  cursor: pointer;
  .add-plus {
    width: 100px;
    height: 100px;
    color: #C8C9CC;
    font-size: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;
  }
}

.el-divider--horizontal {
  color: #D9DBE2;
  margin: 14px 17px;
  width: unset;
}

.el-icon-edit-outline {
  color: #8E95AA;
  margin-left: 6px;
}

.cs-input {
  border-color: unset;
  outline: unset;
  border: unset;
  background: transparent;
  box-shadow: unset;
  width: 120px;
  text-align: center;

  &:focus {
    border-color: unset;
    outline: unset;
    border: unset;
    background: transparent;
    box-shadow: unset;
  }

  ::v-deep .el-input__inner {
    padding: 0;
    font-size: 18px;
    border: unset;
    outline: unset;
    background: transparent;
    box-shadow: unset;
    text-align: center;
  }
}

.color1 {
  background: rgba(41, 141, 255, 0.12);
  .close-icon::before {
    border-top: 27px solid rgba(41, 141, 255, 0.11);
    border-right: 27px solid rgba(41, 141, 255, 0.11);
  }
  .box-bottom-label {
    color: #298DFF;
  }
  .box-bottom-value {
    color: #D3E8FF;
  }
}

.color2 {
  background: rgba(62, 204, 147, 0.12);
  .close-icon::before {
    border-top: 27px solid #ABE9D0;
    border-right: 27px solid #ABE9D0;
  }
  .box-bottom-label {
    color: #3ECC93;
  }
  .box-bottom-value {
    color: #D3F3E6;
  }
}

.color3 {
  background: rgba(255, 170, 0, 0.12);
  .close-icon::before {
    border-top: 27px solid #FEDA92;
    border-right: 27px solid #FEDA92;
  }
  .box-bottom-label {
    color: #F1B430;
  }
  .box-bottom-value {
    color: #FFECC4;
  }
}

.color4 {
  background: rgba(66, 107, 216, 0.12);
  .close-icon::before {
    border-top: 27px solid rgba(66, 107, 216, 0.11);
    border-right: 27px solid rgba(66, 107, 216, 0.11);
  }
  .box-bottom-label {
    color: #426BD8;
  }
  .box-bottom-value {
    color: rgba(66, 107, 216,0.12);
  }
}

.color5 {
  background: rgba(255, 125, 35, 0.12);
  .close-icon::before {
    border-top: 27px solid rgba(255, 125, 35, 0.11);
    border-right: 27px solid rgba(255, 125, 35, 0.11);
  }
  .box-bottom-label {
    color: #FF7D23;
  }
  .box-bottom-value {
    color: rgba(255, 125, 35, 0.12);
  }
}

.cs-bom-btn {
  display: inline-block;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  margin-right: 8px;
  cursor: pointer;
  user-select: none;
  transition: border 0.2s;
  &.cs-bom-btn-main {
    color: #fff;
    border: none;
  }
  &.cs-bom-btn-model {
    background: #fff;
    color: #999;
    border: 1.5px solid transparent;
    position: relative;
    padding-right: 22px;
    &.selected {
      font-weight: bold;
    }
    .cs-bom-btn-check {
      position: absolute;
      top: 0px;
      right: 0px;
      width: 11px;
      height: 11px;
      background: transparent;
      border-radius: 2px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: inherit;
      z-index: 2;
      svg {
        display: block;
      }
    }
  }
}
.cs-bom-btn:last-child {
  margin-right: 0;
}

</style>

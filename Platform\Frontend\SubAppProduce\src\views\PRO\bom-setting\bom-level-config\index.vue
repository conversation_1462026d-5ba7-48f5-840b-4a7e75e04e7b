<template>
  <div class="bom-level-config abs100 cs-z-flex-pd16-wrap">
    <div class="cs-z-page-main-content">
      <div class="query-section">
        <div class="query-form">
          <div>
            <span class="query-label">请选择BOM层级数：</span>
            <el-radio-group
              v-model="selectedType"
              class="query-radio-group"
              @change="handleTypeChange"
            >
              <el-radio
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.value"
              >{{ item.label }}</el-radio>
            </el-radio-group>
          </div>
          <el-button type="primary" class="submit-btn" :loading="btnLoading" @click="handleSubmit">
            提交
          </el-button>
        </div>
      </div>

      <div class="table-section">
        <vxe-table
          ref="xTable1"
          :empty-render="{name: 'NotData'}"
          show-header-overflow
          empty-text="暂无数据"
          height="auto"
          show-overflow
          :checkbox-config="{checkField: 'checked'}"
          :loading="tbLoading"
          :row-config="{isCurrent: true, isHover: true }"
          class="cs-vxe-table"
          align="left"
          keep-source
          stripe
          :data="tableData"
          resizable
          :edit-config="{trigger: 'manual', mode: 'row', showStatus: true}"
        >
          <vxe-column field="Sys_Name" title="BOM层级" align="center">
            <template #default="{ row }">
              {{ row.Sys_Name }}
            </template>
          </vxe-column>
          <vxe-column field="Display_Name" title="名称" :edit-render="{}" align="center">
            <template #edit="{ row }">
              <vxe-input v-model="row.Display_Name" type="text" />
            </template>
          </vxe-column>
          <vxe-column title="列表字段显示配置" align="center" width="140">
            <template #default="{ row }">
              <el-button type="text" @click="handleBottomLabelClick(row)">显示配置</el-button>
            </template>
          </vxe-column>
          <vxe-column field="Is_Default_Model" title="模型默认层级" align="center" :edit-render="{}">
            <template #edit="{ row }">
              <el-switch
                v-model="row.Is_Default_Model"
                active-color="#13ce66"
                inactive-color="#ff4949"
                @change="handleDefaultModelChange(row, $event)"
              />
            </template>
            <template #default="{ row }">
              <el-tag v-if="row.Is_Default_Model" type="success">是</el-tag>
              <el-tag v-else type="danger">否</el-tag>
            </template>
          </vxe-column>

          <vxe-column title="操作" width="160" align="center">
            <template #default="{ row }">
              <template v-if="$refs.xTable1.isActiveByRow(row)">
                <el-button type="text" @click="saveRowEvent(row)">保存</el-button>
                <el-button type="text" @click="cancelRowEvent(row)">取消</el-button>
              </template>
              <template v-else>
                <el-button type="text" @click="editRowEvent(row)">编辑</el-button>
              </template>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
    </div>
  </div></template>

<script>
import { GetBomLevelList, SaveBomLevel } from '@/api/PRO/bom-level'
import addRouterPage from '@/mixins/add-router-page'
import store from '@/store'

export default {
  name: 'PROBOMLevelConfig',
  mixins: [addRouterPage],
  data() {
    return {
      selectedType: 2,
      btnLoading: false,
      loading: false,
      apiData: [],
      typeOptions: [
        { label: '二层', value: 2 },
        { label: '三层', value: 3 },
        { label: '四层', value: 4 },
        { label: '五层', value: 5 }
      ],
      tableData: [],
      tbLoading: false
    }
  },
  computed: {
    comName() {
      if (!this.apiData || this.apiData.length === 0) return ''
      return this.apiData[0].Display_Name + '清单'
    },
    partName() {
      if (!this.apiData || this.apiData.length === 0) return ''
      return this.apiData[4].Display_Name + '清单'
    },
    addPageArray() {
      const unitPart = this.tableData.filter(item => item.Is_Enabled && +item.Code > 0)
      const route = [{
        path: this.$route.path + '/ComponentConfig',
        hidden: true,
        component: () => import('@/views/PRO/bom-setting/com-config/index'),
        name: 'PROComponentConfig',
        meta: { title: this.comName }
      },
      {
        path: this.$route.path + '/part-config',
        hidden: true,
        component: () => import('@/views/PRO/bom-setting/part-config/index'),
        name: 'PROPartsConfig',
        meta: { title: this.partName }
      }]
      const curList = []
      if (unitPart.length > 0) {
        unitPart.forEach(item => {
          curList.push({
            path: this.$route.path + `/half-part-config${item.Code}`,
            hidden: true,
            component: () => import('@/views/PRO/bom-setting/half-part-config/index'),
            name: 'PROHalfPartConfig' + item.Code,
            meta: { title: item.Display_Name + '清单' }
          })
        })
      }
      route.splice(1, 0, ...curList)
      console.log('route', route)
      return route
    }
  },
  async mounted() {
    this.selectedType = 2
    await this.getBomLevelList()
    this.handleInitPageRoute()
    console.log('this.addPageArray', this.$router.getRoutes())
  },
  methods: {
    initPage() {
      console.log('hello word 存在即合理')
    },
    handleBottomLabelClick(layer) {
      const _name = layer.Code === '-1' ? 'PROComponentConfig' : layer.Code === '0' ? 'PROPartsConfig' : 'PROHalfPartConfig' + layer.Code
      console.log('_name', _name)
      const query = { pg_redirect: this.$route.name }
      if (+layer.Code > 0) {
        query.level = layer.Code
      }
      console.log(777, JSON.parse(JSON.stringify({ name: _name, query })))
      this.$router.push({ name: _name, query })
    },
    getBomLevelList() {
      this.tbLoading = true
      return new Promise((resolve) => {
        GetBomLevelList().then(res => {
          const { IsSucceed, Message, Data } = res
          if (IsSucceed) {
            this.apiData = (Data || []).map(v => {
              v.isEditing = false
              v.originalName = v.Display_Name
              return v
            })

            if (this.apiData && this.apiData.length > 0) {
              const enabledCount = this.apiData.filter(item => !!item.Is_Enabled).length
              if (enabledCount >= 2) {
                this.selectedType = enabledCount
              } else {
                this.selectedType = 2
              }
            }
            this.generateTableData(this.selectedType || 2, this.apiData)
            this.$store.dispatch('bomInfo/clearBomLevelCache')
            this.handleInitPageRoute()
          } else {
            this.$message({
              message: Message || '获取BOM层级列表失败',
              type: 'error'
            })
          }
          resolve()
        }).catch(error => {
          console.error('获取BOM层级列表失败:', error)
          this.$message.error('获取BOM层级列表失败')
          resolve()
        }).finally(() => {
          this.tbLoading = false
        })
      })
    },

    generateTableData(levelCount, apiData = []) {
      const levelCodeMap = {
        2: ['-1', '0'],
        3: ['-1', '1', '0'],
        4: ['-1', '1', '2', '0'],
        5: ['-1', '1', '2', '3', '0']
      }

      const validCodes = levelCodeMap[levelCount] || levelCodeMap[2]

      apiData.forEach(item => {
        item.Is_Enabled = false
      })

      const filteredData = apiData.filter(v => validCodes.includes(v.Code))

      filteredData.forEach(item => {
        item.Is_Enabled = true
      })

      filteredData.sort((a, b) => parseInt(a.Sort) - parseInt(b.Sort))

      this.tableData = filteredData
    },

    handleTypeChange(value) {
      if (this.apiData) {
        this.generateTableData(value, this.apiData)
      }
    },

    handleSubmit() {
      const hasDefault = this.tableData.every(item => item.Is_Default_Model === false)
      if (hasDefault) {
        this.$message.warning('至少需要一个默认层级')
        return
      }

      const saveData = [...this.apiData]

      this.$confirm('确定要保存所有修改吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.btnLoading = true
        SaveBomLevel(saveData).then(res => {
          if (res.IsSucceed) {
            this.$message.success('保存成功')
            this.getBomLevelList()
          } else {
            this.$message.error(res.Message || '保存失败')
          }
        }).catch(error => {
          console.error('保存失败:', error)
          this.$message.error('保存失败')
        }).finally(() => {
          this.btnLoading = false
        })
      }).catch(() => {
        this.$message.info('已取消保存')
      })
    },

    handleEdit(row, rowIndex) {
      this.customTableConfig.tableData.forEach(item => {
        if (item !== row) {
          row.isEditing = false
        }
      })
      row.isEditing = true
    },

    handleSaveName(row, rowIndex) {
      row.isEditing = false
    },

    handleCancel(row, rowIndex) {
      row.Display_Name = row.originalName
      row.isEditing = false
    },

    handleDefaultModelChange(row, value) {
      this.tableData.forEach(item => {
        if (item !== row) {
          item.Is_Default_Model = false
        }
      })
    },
    editRowEvent(row) {
      const $table = this.$refs.xTable1
      $table.setEditRow(row)
    },
    saveRowEvent() {
      const $table = this.$refs.xTable1
      $table.clearEdit().then(() => {
        this.loading = true
        setTimeout(() => {
          this.loading = false
        }, 300)
      })
    },
    cancelRowEvent(row) {
      const $table = this.$refs.xTable1
      $table.clearEdit().then(() => {
        $table.revertData(row)
      })
    },
    handleShowConfig(row) {
      // TODO: 打开配置弹窗或其他逻辑
      this.$message.info('点击了显示配置')
    }
  }
}
</script>

<style lang="scss" scoped>
.bom-level-config {
  .cs-z-page-main-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    height: 100%;

    .query-section {
      margin-bottom: 20px;
      border-radius: 4px;

      .query-form {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .query-label {
         font-weight: bold;
          font-size: 14px;
          color: #333;
          white-space: nowrap;
        }

        .query-select {
          width: 200px;
        }

        .submit-btn {
          margin-left: 8px;
        }
      }
    }

    .table-section {
      flex: 1;
      overflow: hidden;

    }
  }
  .query-radio-group {
    width: 300px;
    margin-right: 8px;
  }
}
</style>

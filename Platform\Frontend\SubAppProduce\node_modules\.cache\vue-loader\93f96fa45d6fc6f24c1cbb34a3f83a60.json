{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\factory\\component\\Card.vue?vue&type=template&id=7b677bc5&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\factory\\component\\Card.vue", "mtime": 1758011160329}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uICgpIHsKICB2YXIgX3ZtID0gdGhpcwogIHZhciBfaCA9IF92bS4kY3JlYXRlRWxlbWVudAogIHZhciBfYyA9IF92bS5fc2VsZi5fYyB8fCBfaAogIHJldHVybiBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogImNhcmQtd3JhcHBlciIgfSwgWwogICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJ0b3AtYm94IiB9LCBbCiAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAidC1ib3giIH0sIFsKICAgICAgICBfYygiaSIsIHsgc3RhdGljQ2xhc3M6ICJpY29uZm9udCBpY29uLXN0ZWVsIiB9KSwKICAgICAgICBfYygic3Ryb25nIiwgeyBzdGF0aWNDbGFzczogInRpdGxlIiB9LCBbCiAgICAgICAgICBfdm0uX3YoX3ZtLl9zKF92bS5pdGVtLlNob3J0X05hbWUpKSwKICAgICAgICBdKSwKICAgICAgXSksCiAgICAgIF9jKAogICAgICAgICJkaXYiLAogICAgICAgIHsgc3RhdGljQ2xhc3M6ICJ0LWJveC1idG4iIH0sCiAgICAgICAgWwogICAgICAgICAgdHJ1ZQogICAgICAgICAgICA/IF9jKAogICAgICAgICAgICAgICAgImVsLWJ1dHRvbiIsCiAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHNpemU6ICJtaW5pIiwgdHlwZTogImRhbmdlciIgfSwKICAgICAgICAgICAgICAgICAgb246IHsKICAgICAgICAgICAgICAgICAgICBjbGljazogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIF92bS5oYW5kbGVEZWxldGUoKQogICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgW192bS5fdigi5YigIOmZpCIpXQogICAgICAgICAgICAgICkKICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgIF9jKAogICAgICAgICAgICAiZWwtYnV0dG9uIiwKICAgICAgICAgICAgeyBhdHRyczogeyBzaXplOiAibWluaSIgfSwgb246IHsgY2xpY2s6IF92bS5oYW5kbGVFZGl0IH0gfSwKICAgICAgICAgICAgW192bS5fdigi57yWIOi+kSIpXQogICAgICAgICAgKSwKICAgICAgICBdLAogICAgICAgIDEKICAgICAgKSwKICAgIF0pLAogICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJzdWItdGl0bGUiIH0sIFsKICAgICAgX3ZtLl92KCIgIiArIF92bS5fcyhfdm0uaXRlbS5OYW1lKSArICIgIiksCiAgICBdKSwKICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAidGFnLWNvbnRhaW5lciIgfSwgWwogICAgICBfYygic3BhbiIsIHsgc3RhdGljQ2xhc3M6ICJ0YWctYm94IiB9LCBbCiAgICAgICAgX3ZtLl92KF92bS5fcyhfdm0uaXRlbS5DYXRlZ29yeSkpLAogICAgICBdKSwKICAgIF0pLAogIF0pCn0KdmFyIHN0YXRpY1JlbmRlckZucyA9IFtdCnJlbmRlci5fd2l0aFN0cmlwcGVkID0gdHJ1ZQoKZXhwb3J0IHsgcmVuZGVyLCBzdGF0aWNSZW5kZXJGbnMgfQ=="}]}
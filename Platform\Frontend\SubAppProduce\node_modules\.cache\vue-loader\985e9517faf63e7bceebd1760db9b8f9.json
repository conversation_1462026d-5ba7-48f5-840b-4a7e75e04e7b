{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\component\\ProjectAddDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\component\\ProjectAddDialog.vue", "mtime": 1757926768439}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRQcm9qZWN0UGFnZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vcHJvamVjdCcNCmltcG9ydCB7IFN5bmNQcm9qZWN0VGVjaG5vbG9neUZyb21Qcm9qZWN0IH0gZnJvbSAnQC9hcGkvUFJPL3RlY2hub2xvZ3ktbGliJw0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnQWRkUHJvamVjdCcsDQogIHByb3BzOiB7DQogICAgc3lzUHJvamVjdElkOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnJw0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBwcm9qZWN0TGlzdDogW10sDQogICAgICBzZWFyY2hGb3JtOiB7DQogICAgICAgIFByb2plY3RDb2RlOiAnJw0KICAgICAgfQ0KDQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZmV0Y2hQcm9qZWN0TGlzdCgpDQogIH0sDQogIG1ldGhvZHM6IHsNCg0KICAgIC8vIOiOt+W<PERSON><PERSON>hueebruWIl+ihqA0KICAgIGFzeW5jIGZldGNoUHJvamVjdExpc3QoKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgIH0NCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0UHJvamVjdFBhZ2VMaXN0KHBhcmFtcykNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICBjb25zdCB0YWJsZSA9IHJlcz8uRGF0YT8uRGF0YSB8fCBbXQ0KICAgICAgICAgIHRoaXMucHJvamVjdExpc3QgPSB0YWJsZS5maWx0ZXIoaXRlbSA9PiBpdGVtLlN5c19Qcm9qZWN0X0lkICE9PSB0aGlzLnN5c1Byb2plY3RJZCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5NZXNzYWdlIHx8ICfojrflj5bpobnnm67liJfooajlpLHotKUnKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bpobnnm67liJfooajlpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WP<PERSON>hu<PERSON>bruWIl+ihqOWksei0pScpDQogICAgICB9IGZpbmFsbHkgew0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlQ2FuY2VsKCkgew0KICAgICAgdGhpcy4kZW1pdCgnY2xvc2UnKQ0KICAgIH0sDQogICAgLy8g56Gu6K6k6YCJ5oupDQogICAgaGFuZGxlQ29uZmlybSgpIHsNCiAgICAgIGlmICh0aGlzLnNlYXJjaEZvcm0uUHJvamVjdENvZGUgPT09ICcnKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36Iez5bCR6YCJ5oup5LiA5Liq6aG555uuJykNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlDQoNCiAgICAgIFN5bmNQcm9qZWN0VGVjaG5vbG9neUZyb21Qcm9qZWN0KHsNCiAgICAgICAgRnJvbV9TeXNfUHJvamVjdF9JZDogdGhpcy5zZWFyY2hGb3JtLlByb2plY3RDb2RlLA0KICAgICAgICBUb19TeXNfUHJvamVjdF9JZDogdGhpcy5zeXNQcm9qZWN0SWQNCiAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLiRlbWl0KCdyZWZyZXNoJykNCiAgICAgICAgICB0aGlzLiRlbWl0KCdjbG9zZScpDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflkIzmraXmiJDlip/vvIEnKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLk1lc3NhZ2UgfHwgJ+WQjOatpeWksei0pScpDQogICAgICAgIH0NCiAgICAgIH0pLmZpbmFsbHkoKCkgPT4gew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["ProjectAddDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ProjectAddDialog.vue", "sourceRoot": "src/views/PRO/project-config/product-mfg-path/component", "sourcesContent": ["<template>\r\n  <div class=\"add-project-container\">\r\n    <div class=\"instruction\">\r\n      请选择项目,添加所选项目的所有生产路径\r\n    </div>\r\n    <div class=\"search-section\">\r\n      <el-form :model=\"searchForm\" inline>\r\n        <el-form-item v-if=\"!projectList.length\" label=\"项目名称：\">\r\n          <div>暂无可同步的项目</div>\r\n        </el-form-item>\r\n        <el-form-item v-else label=\"项目名称：\">\r\n          <el-select\r\n            v-model=\"searchForm.ProjectCode\"\r\n            clearable\r\n            filterable\r\n            placeholder=\"请选择\"\r\n            style=\"width: 200px\"\r\n          >\r\n            <el-option v-for=\"item in projectList\" :key=\"item.Sys_Project_Id\" :label=\"item.Short_Name\" :value=\"item.Sys_Project_Id\" />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n      </el-form>\r\n    </div>\r\n\r\n    <!-- 提示信息 -->\r\n\r\n    <!-- 底部按钮 -->\r\n    <div class=\"footer-actions\">\r\n      <el-button @click=\"handleCancel\">取消</el-button>\r\n      <el-button v-if=\"projectList.length\" type=\"primary\" :loading=\"loading\" :disabled=\"searchForm.ProjectCode === ''\" @click=\"handleConfirm\">\r\n        确定\r\n      </el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetProjectPageList } from '@/api/PRO/project'\r\nimport { SyncProjectTechnologyFromProject } from '@/api/PRO/technology-lib'\r\nexport default {\r\n  name: 'AddProject',\r\n  props: {\r\n    sysProjectId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      projectList: [],\r\n      searchForm: {\r\n        ProjectCode: ''\r\n      }\r\n\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchProjectList()\r\n  },\r\n  methods: {\r\n\r\n    // 获取项目列表\r\n    async fetchProjectList() {\r\n      try {\r\n        const params = {\r\n        }\r\n        const res = await GetProjectPageList(params)\r\n        if (res.IsSucceed) {\r\n          const table = res?.Data?.Data || []\r\n          this.projectList = table.filter(item => item.Sys_Project_Id !== this.sysProjectId)\r\n        } else {\r\n          this.$message.error(res.Message || '获取项目列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取项目列表失败:', error)\r\n        this.$message.error('获取项目列表失败')\r\n      } finally {\r\n      }\r\n    },\r\n    handleCancel() {\r\n      this.$emit('close')\r\n    },\r\n    // 确认选择\r\n    handleConfirm() {\r\n      if (this.searchForm.ProjectCode === '') {\r\n        this.$message.warning('请至少选择一个项目')\r\n        return\r\n      }\r\n      this.loading = true\r\n\r\n      SyncProjectTechnologyFromProject({\r\n        From_Sys_Project_Id: this.searchForm.ProjectCode,\r\n        To_Sys_Project_Id: this.sysProjectId\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$emit('refresh')\r\n          this.$emit('close')\r\n          this.$message.success('同步成功！')\r\n        } else {\r\n          this.$message.error(res.Message || '同步失败')\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.add-project-container {\r\n\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .search-section {\r\n    background: #fff;\r\n    border-radius: 4px;\r\n  }\r\n\r\n  .instruction {\r\n    background: #f0f9ff;\r\n    border: 1px solid #b3d8ff;\r\n    color: #1890ff;\r\n    padding: 12px 16px;\r\n    border-radius: 4px;\r\n    margin-bottom: 16px;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .table-section {\r\n    flex: 1;\r\n    background: #fff;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .footer-actions {\r\n    margin-top: 16px;\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    padding: 0px 8px 0 0;\r\n    background: #fff;\r\n  }\r\n}\r\n</style>\r\n"]}]}
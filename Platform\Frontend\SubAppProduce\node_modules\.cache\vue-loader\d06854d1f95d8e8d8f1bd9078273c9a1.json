{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\material-inventory-reconfig\\aux-outbound\\info.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\material-inventory-reconfig\\aux-outbound\\info.vue", "mtime": 1757926768419}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBjbG9zZVRhZ1ZpZXcsIGRlYm91bmNlLCBkZWVwQ2xvbmUgfSBmcm9tICdAL3V0aWxzJw0KaW1wb3J0IFB1cmNoYXNlVGIgZnJvbSAnLi9jb21wb25lbnRzL1B1cmNoYXNlVGIudnVlJw0KaW1wb3J0IEFkZExpc3QgZnJvbSAnLi9jb21wb25lbnRzL0FkZExpc3QudnVlJw0KaW1wb3J0IEFkZFB1cmNoYXNlTGlzdCBmcm9tICcuL2NvbXBvbmVudHMvQWRkUHVyY2hhc2VMaXN0LnZ1ZScNCmltcG9ydCBJbXBvcnRGaWxlIGZyb20gJy4uL2NvbXBvbmVudHMvSW1wb3J0RmlsZS52dWUnDQppbXBvcnQgU3RhbmRhcmQgZnJvbSAnLi9jb21wb25lbnRzL1N0YW5kYXJkLnZ1ZScNCmltcG9ydCB7IEdldFByb2plY3RQYWdlTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9wcm9qZWN0Jw0KaW1wb3J0IE9TU1VwbG9hZCBmcm9tICdAL3ZpZXdzL1BSTy9jb21wb25lbnRzL29zc3VwbG9hZCcNCmltcG9ydCBnZXRDb21tb25EYXRhIGZyb20gJ0AvbWl4aW5zL1BSTy9nZXQtY29tbW9uLWRhdGEvaW5kZXguanMnDQppbXBvcnQgeyBHZXRGaXJzdExldmVsRGVwYXJ0c1VuZGVyRmFjdG9yeSB9IGZyb20gJ0AvYXBpL09NQS9ub25PcGVyYXRpbmcuanMnDQppbXBvcnQgew0KICBBdXhSZXR1cm5CeVJlY2VpcHQsDQogIEdldEF1eERldGFpbEJ5UmVjZWlwdCwgR2V0VGVhbUxpc3RCeVVzZXJGb3JNYXRlcmllbCwNCiAgR2V0VXNlclBhZ2UNCn0gZnJvbSAnQC9hcGkvUFJPL21hdGVyaWFsLXdhcmVob3VzZS9tYXRlcmlhbC1pbnZlbnRvcnktcmVjb25maWcuanMnDQovLyBpbXBvcnQgeyBHZXRGYWN0b3J5UGVvcGxlbGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9iYXNpYy1pbmZvcm1hdGlvbi93b3Jrc2hvcCcNCmltcG9ydCB7IEdldFByb2Nlc3NMaXN0QmFzZSB9IGZyb20gJ0AvYXBpL1BSTy90ZWNobm9sb2d5LWxpYicNCmltcG9ydCB7IGdldERpY3Rpb25hcnkgfSBmcm9tICdAL3V0aWxzL2NvbW1vbicNCmltcG9ydCB7IEdldFByZWZlcmVuY2VTZXR0aW5nVmFsdWUgfSBmcm9tICdAL2FwaS9zeXMvc3lzdGVtLXNldHRpbmcnDQppbXBvcnQgew0KICBQaWNrT3V0U3RvcmUsDQogIEdldFBpY2tPdXREZXRhaWwNCn0gZnJvbSAnQC9hcGkvUFJPL21hdGVyaWFsLXdhcmVob3VzZS9tYXRlcmlhbC1pbnZlbnRvcnktcmVjb25maWcuanMnDQppbXBvcnQgeyBHZXRPTUFMYXRlc3RTdGF0aXN0aWNUaW1lIH0gZnJvbSAnQC9hcGkvUFJPL21hdGVyaWFsTWFuYWdlbWVudCcNCmltcG9ydCB7IEdldE9zc1VybCwgR2V0Q29tcGFueURlcGFydFRyZWUgfSBmcm9tICdAL2FwaS9zeXMnDQppbXBvcnQgUmV0dXJuVGIgZnJvbSAnLi9jb21wb25lbnRzL1JldHVyblRiLnZ1ZScNCmltcG9ydCBnZXRUYWJsZUNvbmZpZyBmcm9tICdAL21peGlucy9QUk8vZ2V0LXRhYmxlLWluZm8tcHJvL2luZGV4Jw0KaW1wb3J0IFdhcmVob3VzZSBmcm9tICdAL3ZpZXdzL1BSTy9tYXRlcmlhbC1yZWNlaXB0LW1hbmFnZW1lbnQvY29tcG9uZW50cy9XYXJlaG91c2UudnVlJw0KaW1wb3J0IHsgdjQgYXMgdXVpZHY0IH0gZnJvbSAndXVpZCcNCmltcG9ydCBTZWxlY3RNYXRlcmlhbFN0b3JlVHlwZSBmcm9tICdAL2NvbXBvbmVudHMvU2VsZWN0L1NlbGVjdE1hdGVyaWFsU3RvcmVUeXBlL2luZGV4LnZ1ZScNCmltcG9ydCBEeW5hbWljVGFibGVGaWVsZHMgZnJvbSAnQC9jb21wb25lbnRzL0R5bmFtaWNUYWJsZUZpZWxkcy9pbmRleC52dWUnDQppbXBvcnQgeyBHZXRXb3JraW5nVGVhbXMgfSBmcm9tICdAL2FwaS9QUk8vdGVjaG5vbG9neS1saWInDQppbXBvcnQgU2VsZWN0RGVwYXJ0bWVudCBmcm9tICdAL2NvbXBvbmVudHMvU2VsZWN0L1NlbGVjdERlcGFydG1lbnQvaW5kZXgudnVlJw0KaW1wb3J0IFNlbGVjdERlcGFydG1lbnRVc2VyIGZyb20gJ0AvY29tcG9uZW50cy9TZWxlY3QvU2VsZWN0RGVwYXJ0bWVudFVzZXIvaW5kZXgudnVlJw0KaW1wb3J0IFBpY2tTZWxlY3QgZnJvbSAnQC92aWV3cy9QUk8vbWF0ZXJpYWxfdjQvcGlja0FwcGx5L3NlbGVjdC52dWUnDQpleHBvcnQgZGVmYXVsdCB7DQogIGNvbXBvbmVudHM6IHsNCiAgICBQaWNrU2VsZWN0LA0KICAgIFNlbGVjdERlcGFydG1lbnRVc2VyLCBTZWxlY3REZXBhcnRtZW50LA0KICAgIER5bmFtaWNUYWJsZUZpZWxkcywNCiAgICBTZWxlY3RNYXRlcmlhbFN0b3JlVHlwZSwNCiAgICBBZGRQdXJjaGFzZUxpc3QsDQogICAgUHVyY2hhc2VUYiwNCiAgICBSZXR1cm5UYiwNCiAgICBJbXBvcnRGaWxlLA0KICAgIFdhcmVob3VzZSwNCiAgICBTdGFuZGFyZCwNCiAgICBBZGRMaXN0LA0KICAgIE9TU1VwbG9hZA0KICB9LA0KICBtaXhpbnM6IFtnZXRDb21tb25EYXRhLCBnZXRUYWJsZUNvbmZpZ10sDQogIHByb3BzOiB7DQogICAgcGFnZVR5cGU6IHsNCiAgICAgIHR5cGU6IE51bWJlciwNCiAgICAgIGRlZmF1bHQ6IHVuZGVmaW5lZA0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgaXNSZXRyYWN0OiBmYWxzZSwgLy8g5piv5ZCm5bGV5byADQogICAgICByZXR1cm5pbmc6IGZhbHNlLA0KICAgICAgdGJMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHByb2plY3RPcHRpb25zOiBbXSwNCiAgICAgIG11bHRpcGxlU2VsZWN0aW9uOiBbXSwNCiAgICAgIC8vIFBhcnR5VW5pdERhdGE6IFtdLA0KICAgICAgLy8gU3VwcGxpZXJEYXRhOiBbXSwNCiAgICAgIGZhY3RvcnlQZW9wbGVsaXN0OiBbXSwgLy8g6aKG5paZ5Lq65YiX6KGoDQogICAgICBkZXBhcnRtZW50bGlzdDogW10sDQogICAgICBQcm9jZXNzTGlzdDogW10sDQogICAgICBBdXhPdXRib3VuZFR5cGVMaXN0OiBbXSwNCiAgICAgIFdvcmtpbmdUZWFtTGlzdDogW10sIC8vIOmihuaWmeePree7hOWIl+ihqA0KICAgICAgc2VhcmNoRm9ybTogew0KICAgICAgICBSYXdOYW1lOiAnJywNCiAgICAgICAgU3BlYzogJycNCiAgICAgIH0sDQogICAgICBmb3JtOiB7DQogICAgICAgIE91dFN0b3JlTm86ICcnLCAvLyDlh7rlupPljZXlj7cNCiAgICAgICAgT3V0U3RvcmVUeXBlOiAxLCAvLyDlh7rlupPnsbvlnosNCiAgICAgICAgT3V0U3RvcmVEYXRlOiB0aGlzLmdldERhdGUoKSwNCiAgICAgICAgVXNlX1Byb2Nlc3NpbmdfSWQ6ICcnLCAvLyDlt6Xluo9pZA0KICAgICAgICBQaWNrX0RlcGFydG1lbnRfSWQ6ICcnLA0KICAgICAgICAvLyBQcm9qZWN0SWQ6ICcnLA0KICAgICAgICBTeXNQcm9qZWN0SWQ6ICcnLA0KICAgICAgICBQaWNrX1Byb2plY3RfTmFtZTogJycsIC8vIOmihueUqOmhueebruWQjeensA0KICAgICAgICBSZWNlaXZlVXNlcklkOiAnJywgLy8g6aKG5paZ5Lq6aWQNCiAgICAgICAgV29ya2luZ1RlYW1JZDogJycsIC8vIOmihuaWmeePree7hGlkDQogICAgICAgIFJlbWFyazogJycsDQogICAgICAgIEF0dGFjaG1lbnQ6ICcnDQogICAgICB9LA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgT3V0U3RvcmVUeXBlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqScsIHRyaWdnZXI6ICdjaGFuZ2UnIH0NCiAgICAgICAgXSwNCiAgICAgICAgT3V0U3RvcmVEYXRlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqScsIHRyaWdnZXI6ICdjaGFuZ2UnIH0NCiAgICAgICAgXSwNCiAgICAgICAgUGlja19EZXBhcnRtZW50X0lkOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqScsIHRyaWdnZXI6ICdjaGFuZ2UnIH0NCiAgICAgICAgXSwNCiAgICAgICAgUmVjZWl2ZVVzZXJJZDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6knLCB0cmlnZ2VyOiAnY2hhbmdlJyB9DQogICAgICAgIF0NCiAgICAgICAgLy8gV29ya2luZ1RlYW1JZDogWw0KICAgICAgICAvLyAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6knLCB0cmlnZ2VyOiAnY2hhbmdlJyB9DQogICAgICAgIC8vIF0NCiAgICAgIH0sDQogICAgICB0cmVlUGFyYW1zRGVwYXJ0OiB7DQogICAgICAgICdkZWZhdWx0LWV4cGFuZC1hbGwnOiB0cnVlLA0KICAgICAgICBmaWx0ZXJhYmxlOiBmYWxzZSwNCiAgICAgICAgY2xpY2tQYXJlbnQ6IHRydWUsDQogICAgICAgIGRhdGE6IFtdLA0KICAgICAgICBwcm9wczogew0KICAgICAgICAgIGRpc2FibGVkOiAnZGlzYWJsZWQnLA0KICAgICAgICAgIGNoaWxkcmVuOiAnQ2hpbGRyZW4nLA0KICAgICAgICAgIGxhYmVsOiAnTGFiZWwnLA0KICAgICAgICAgIHZhbHVlOiAnSWQnDQogICAgICAgIH0NCiAgICAgIH0sDQogICAgICBiYWNrZW5kRGF0ZTogbnVsbCwNCiAgICAgIHBpY2tlck9wdGlvbnM6IHsNCiAgICAgICAgZGlzYWJsZWREYXRlOiAodGltZSkgPT4gew0KICAgICAgICAgIHJldHVybiB0aW1lLmdldFRpbWUoKSA8IG5ldyBEYXRlKHRoaXMuYmFja2VuZERhdGUpLmdldFRpbWUoKSAvLyDpmZDliLbpgInmi6nml6XmnJ/kuI3og73otoXov4flvZPliY3ml6XmnJ8NCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIGN1cnJlbnRDb21wb25lbnQ6ICcnLA0KICAgICAgdGl0bGU6ICcnLA0KICAgICAgZFdpZHRoOiAnNjAlJywNCiAgICAgIC8vIGlzU2luZ2xlOiBmYWxzZSwNCiAgICAgIHNhdmVMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHNlYXJjaDogKCkgPT4gKHt9KSwNCiAgICAgIG9wZW5BZGRMaXN0OiBmYWxzZSwNCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgQmlnVHlwZTogMSwNCiAgICAgIGlzUHJvZHVjdHdlaWdodDogbnVsbCwNCiAgICAgIGZpbGVMaXN0RGF0YTogW10sDQogICAgICBmaWxlTGlzdEFycjogW10sDQogICAgICBzZWFyY2hOdW06IDEsDQogICAgICByb290VGFibGVEYXRhOiBbXSwNCiAgICAgIHRhYmxlRGF0YTogW10sDQogICAgICB0eXBlTnVtYmVyMTogMCwNCiAgICAgIHR5cGVOdW1iZXIyOiAwLA0KICAgICAgdHlwZU51bWJlcjM6IDAsDQogICAgICB0eXBlTnVtYmVyNDogMCwNCiAgICAgIGN1cnJlbnRUYkNvbXBvbmVudDogJycsDQogICAgICBiYXRjaERpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgYmF0Y2hQcm9qZWN0SWQ6ICcnDQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIGlzQWRkKCkgew0KICAgICAgcmV0dXJuIHRoaXMucGFnZVR5cGUgPT09IDENCiAgICB9LA0KICAgIGlzRWRpdCgpIHsNCiAgICAgIHJldHVybiB0aGlzLnBhZ2VUeXBlID09PSAyDQogICAgfSwNCiAgICBpc1ZpZXcoKSB7DQogICAgICByZXR1cm4gdGhpcy5wYWdlVHlwZSA9PT0gMw0KICAgIH0sDQogICAgaXNQdXJjaGFzZSgpIHsNCiAgICAgIHJldHVybiB0aGlzLmZvcm0uT3V0U3RvcmVUeXBlID09IDEgLy8g6aKG55So5Ye65bqTDQogICAgfSwNCiAgICBpc1JldHVybigpIHsNCiAgICAgIHJldHVybiB0aGlzLnBhZ2VUeXBlID09PSA4DQogICAgfSwNCiAgICBncmlkQ29kZSgpIHsNCiAgICAgIHJldHVybiB0aGlzLmlzUmV0dXJuID8gJ3Byb19hdXhfbWF0ZXJpYWxfb3V0Ym91bmRfZGV0YWlsX2xpc3RfcmV0dXJuJyA6ICdwcm9fYXV4X21hdGVyaWFsX291dGJvdW5kX2RldGFpbF9saXN0LFN0ZWVsJw0KICAgIH0NCiAgfSwNCiAgYXN5bmMgbW91bnRlZCgpIHsNCiAgICBpZiAodGhpcy5pc1JldHVybikgew0KICAgICAgdGhpcy5jdXJyZW50VGJDb21wb25lbnQgPSBSZXR1cm5UYg0KICAgICAgY29uc3QgY29sdW1uID0gYXdhaXQgdGhpcy5nZXRUYWJsZUNvbmZpZyh0aGlzLmdyaWRDb2RlKQ0KICAgICAgdGhpcy4kbmV4dFRpY2soXyA9PiB7DQogICAgICAgIHRoaXMuJHJlZnNbJ3RhYmxlJ10uaW5pdChjb2x1bW4pDQogICAgICB9KQ0KICAgIH0gZWxzZSB7DQogICAgICB0aGlzLmN1cnJlbnRUYkNvbXBvbmVudCA9IFB1cmNoYXNlVGINCiAgICB9DQogICAgYXdhaXQgdGhpcy5nZXRDdXJGYWN0b3J5KCkNCiAgICBhd2FpdCB0aGlzLmdldE9NQUxhdGVzdFN0YXRpc3RpY1RpbWUoKQ0KICAgIHRoaXMuZ2V0UHJlZmVyZW5jZVNldHRpbmdWYWx1ZSgpDQogICAgdGhpcy5BdXhPdXRib3VuZFR5cGVMaXN0ID0gYXdhaXQgZ2V0RGljdGlvbmFyeSgnQXV4T3V0Ym91bmRUeXBlJykNCiAgICBhd2FpdCB0aGlzLmdldEZpcnN0TGV2ZWxEZXBhcnRzVW5kZXJGYWN0b3J5KCkNCiAgICB0aGlzLnNlYXJjaCA9IGRlYm91bmNlKHRoaXMuZmV0Y2hEYXRhLCA4MDAsIHRydWUpDQogICAgdGhpcy5nZXRQcm9qZWN0KCkNCiAgICB0aGlzLmdldFByb2Nlc3NMaXN0QmFzZSgpDQogICAgLy8gdGhpcy5nZXRGYWN0b3J5UGVvcGxlbGlzdCgpDQogICAgdGhpcy5nZXRXb3JraW5nVGVhbXMoKQ0KICAgIGlmICghdGhpcy5pc0FkZCkgew0KICAgICAgdGhpcy5nZXRJbmZvKCkNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICB1c2VyQ2hhbmdlKHZhbCkgew0KICAgICAgR2V0VGVhbUxpc3RCeVVzZXJGb3JNYXRlcmllbCh7DQogICAgICAgIGlkOiB2YWwNCiAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5EYXRhPy5sZW5ndGggPT09IDEpIHsNCiAgICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAnV29ya2luZ1RlYW1JZCcsIHJlcy5EYXRhWzBdLklkKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICdXb3JraW5nVGVhbUlkJywgJycpDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBjbG9zZUJhdGNoRGlhbG9nKCkgew0KICAgICAgdGhpcy5iYXRjaFByb2plY3RJZCA9ICcnDQogICAgICB0aGlzLmJhdGNoRGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgfSwNCiAgICBiYXRjaENoYW5nZVByb2plY3QoKSB7DQogICAgICBjb25zdCB0YkRhdGEgPSB0aGlzLiRyZWZzLnRhYmxlLnRiRGF0YQ0KICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5mb3JFYWNoKChlbGVtZW50LCBpZHgpID0+IHsNCiAgICAgICAgY29uc3QgaXRlbSA9IHRiRGF0YS5maW5kKCh2KSA9PiB2LmluZGV4ID09PSBlbGVtZW50LmluZGV4KQ0KICAgICAgICBjb25zdCBpID0gdGJEYXRhLmZpbmRJbmRleCgodikgPT4gdi5pbmRleCA9PT0gZWxlbWVudC5pbmRleCkNCiAgICAgICAgaXRlbS5QaWNrX1N5c19Qcm9qZWN0X0lkID0gdGhpcy5iYXRjaFByb2plY3RJZA0KICAgICAgICAvLyDlkIzml7bmm7TmlrDpobnnm67lkI3np7ANCiAgICAgICAgaXRlbS5QaWNrX1Byb2plY3RfTmFtZSA9IHRoaXMucHJvamVjdE9wdGlvbnMuZmluZChwcm9qZWN0ID0+IHByb2plY3QuU3lzX1Byb2plY3RfSWQgPT09IHRoaXMuYmF0Y2hQcm9qZWN0SWQpPy5TaG9ydF9OYW1lDQogICAgICAgIHRoaXMuJHNldCh0YkRhdGEsIGksIGl0ZW0pDQogICAgICB9KQ0KICAgICAgdGhpcy5jbG9zZUJhdGNoRGlhbG9nKCkNCiAgICB9LA0KICAgIGNoYW5nZUNvbHVtbigpIHsNCiAgICAgIGNvbnN0IHRlbXAgPSB0aGlzLmN1cnJlbnRUYkNvbXBvbmVudA0KICAgICAgdGhpcy5jdXJyZW50VGJDb21wb25lbnQgPSAnJw0KICAgICAgdGhpcy4kbmV4dFRpY2soYXN5bmMoKSA9PiB7DQogICAgICAgIHRoaXMuY3VycmVudFRiQ29tcG9uZW50ID0gdGVtcA0KICAgICAgICBpZiAodGhpcy5pc1JldHVybikgew0KICAgICAgICAgIGNvbnN0IGNvbHVtbiA9IGF3YWl0IHRoaXMuZ2V0VGFibGVDb25maWcodGhpcy5ncmlkQ29kZSkNCiAgICAgICAgICB0aGlzLiRuZXh0VGljayhfID0+IHsNCiAgICAgICAgICAgIHRoaXMuJHJlZnNbJ3RhYmxlJ10uaW5pdChjb2x1bW4pDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLiRyZWZzWyd0YWJsZSddLnNldERhdGEodGhpcy50YWJsZURhdGEpDQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g6ZmE5Lu25LiK5Lyg5oiQ5YqfDQogICAgdXBsb2FkU3VjY2VzcyhyZXNwb25zZSwgZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIHRoaXMuZmlsZUxpc3RBcnIgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KGZpbGVMaXN0KSkNCiAgICB9LA0KICAgIC8vIOenu+mZpOW3suS4iuS8oOaWh+S7tg0KICAgIHVwbG9hZFJlbW92ZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy5maWxlTGlzdEFyciA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkoZmlsZUxpc3QpKQ0KICAgIH0sDQogICAgLy8g54K55Ye75bey5LiK5Lyg5paH5Lu2DQogICAgYXN5bmMgaGFuZGxlUHJldmlldyhmaWxlKSB7DQogICAgICBsZXQgZW5jcnlwdGlvblVybCA9ICcnDQogICAgICBpZiAoZmlsZS5yZXNwb25zZSAmJiBmaWxlLnJlc3BvbnNlLmVuY3J5cHRpb25VcmwpIHsNCiAgICAgICAgZW5jcnlwdGlvblVybCA9IGZpbGUucmVzcG9uc2UuZW5jcnlwdGlvblVybA0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgZW5jcnlwdGlvblVybCA9IGF3YWl0IEdldE9zc1VybCh7IHVybDogZmlsZS5lbmNyeXB0aW9uVXJsIH0pDQogICAgICAgIGVuY3J5cHRpb25VcmwgPSBlbmNyeXB0aW9uVXJsLkRhdGENCiAgICAgIH0NCiAgICAgIHdpbmRvdy5vcGVuKGVuY3J5cHRpb25VcmwpDQogICAgfSwNCiAgICAvLyDojrflj5bov5DokKXmoLjnrpflt7Lnu5/orqHnmoTmnIDmlrDml6XmnJ8NCiAgICBhc3luYyBnZXRPTUFMYXRlc3RTdGF0aXN0aWNUaW1lKCkgew0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0T01BTGF0ZXN0U3RhdGlzdGljVGltZSh7fSkNCiAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgIHRoaXMuYmFja2VuZERhdGUgPSByZXMuRGF0YSB8fCAnJw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5tZXNzYWdlLmVycm9yKHJlcy5NZXNhYWdlKQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5pCc57SiDQogICAgaGFuZGxlU2VhcmNoKCkgew0KICAgICAgdGhpcy50YWJsZURhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMucm9vdFRhYmxlRGF0YSkpDQogICAgICBpZiAodGhpcy5zZWFyY2hGb3JtLlJhd05hbWUpIHsNCiAgICAgICAgY29uc3QgcmF3TmFtZVJlZ2V4ID0gbmV3IFJlZ0V4cCh0aGlzLnNlYXJjaEZvcm0uUmF3TmFtZSwgJ2knKQ0KICAgICAgICB0aGlzLnRhYmxlRGF0YSA9IHRoaXMudGFibGVEYXRhLmZpbHRlcigoaXRlbSkgPT4gew0KICAgICAgICAgIHJldHVybiByYXdOYW1lUmVnZXgudGVzdChpdGVtLlJhd05hbWUpDQogICAgICAgIH0pDQogICAgICB9DQoNCiAgICAgIGlmICh0aGlzLnNlYXJjaEZvcm0uU3BlYykgew0KICAgICAgICBjb25zdCBzcGVjUmVnZXggPSBuZXcgUmVnRXhwKHRoaXMuc2VhcmNoRm9ybS5TcGVjLCAnaScpDQogICAgICAgIHRoaXMudGFibGVEYXRhID0gdGhpcy50YWJsZURhdGEuZmlsdGVyKChpdGVtKSA9PiB7DQogICAgICAgICAgcmV0dXJuIHNwZWNSZWdleC50ZXN0KGl0ZW0uU3BlYykNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLnNlYXJjaEZvcm0uU3lzUHJvamVjdElkKSB7DQogICAgICAgIGNvbnN0IHN5c1Byb2plY3RJZFJlZ2V4ID0gbmV3IFJlZ0V4cCh0aGlzLnNlYXJjaEZvcm0uU3lzUHJvamVjdElkLCAnaScpDQogICAgICAgIHRoaXMudGFibGVEYXRhID0gdGhpcy50YWJsZURhdGEuZmlsdGVyKChpdGVtKSA9PiB7DQogICAgICAgICAgcmV0dXJuIHN5c1Byb2plY3RJZFJlZ2V4LnRlc3QoaXRlbS5TeXNfUHJvamVjdF9JZCkNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmlzUmV0dXJuKSB7DQogICAgICAgIHRoaXMuJHJlZnNbJ3RhYmxlJ10uc2V0RGF0YSh0aGlzLnRhYmxlRGF0YSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJHJlZnNbJ3RhYmxlJ10udGJEYXRhID0gdGhpcy50YWJsZURhdGENCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOabtOaWsOihqOagvOaVsOaNrg0KICAgIGhhbmRsZVVwZGF0ZVRiKCkgew0KICAgICAgdGhpcy5yb290VGFibGVEYXRhID0gdGhpcy4kcmVmc1sndGFibGUnXS50YkRhdGENCiAgICAgIHRoaXMudGFibGVEYXRhID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLnJvb3RUYWJsZURhdGEpKQ0KICAgICAgY29uc29sZS5sb2codGhpcy5yb290VGFibGVEYXRhLCAnMTEnKQ0KICAgICAgY29uc29sZS5sb2codGhpcy50YWJsZURhdGEsICcyMicpDQogICAgICAvLyB0aGlzLnNldFRhYkRhdGEoKQ0KICAgIH0sDQogICAgLy8g6I635Y+W5ZOB6YeN5YGP5aW96K6+572uDQogICAgZ2V0UHJlZmVyZW5jZVNldHRpbmdWYWx1ZSgpIHsNCiAgICAgIEdldFByZWZlcmVuY2VTZXR0aW5nVmFsdWUoeyBjb2RlOiAnUHJvZHVjdHdlaWdodCcgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy5pc1Byb2R1Y3R3ZWlnaHQgPSByZXMuRGF0YQ0KICAgICAgICAgIC8vIHRoaXMuaXNQcm9kdWN0d2VpZ2h0ID0gImZhbHNlIjsNCiAgICAgICAgICBpZiAodGhpcy5pc1Byb2R1Y3R3ZWlnaHQgIT09ICd0cnVlJykgew0KICAgICAgICAgICAgdGhpcy5nZXREZXBhcnRtZW50VHJlZSgpDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGdldERlcGFydG1lbnRUcmVlKCkgew0KICAgICAgR2V0Q29tcGFueURlcGFydFRyZWUoeyBpc0FsbDogdHJ1ZSB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICBjb25zdCB0cmVlID0gcmVzLkRhdGENCiAgICAgICAgICB0aGlzLnNldERpc2FibGVkVHJlZSh0cmVlKQ0KICAgICAgICAgIHRoaXMudHJlZVBhcmFtc0RlcGFydC5kYXRhID0gdHJlZQ0KICAgICAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgICAgICB0aGlzLiRyZWZzLnRyZWVTZWxlY3REZXBhcnQudHJlZURhdGFVcGRhdGVGdW4odHJlZSkNCiAgICAgICAgICB9KQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHNldERpc2FibGVkVHJlZShyb290KSB7DQogICAgICBpZiAoIXJvb3QpIHJldHVybg0KICAgICAgcm9vdC5mb3JFYWNoKChlbGVtZW50KSA9PiB7DQogICAgICAgIGNvbnN0IHsgQ2hpbGRyZW4gfSA9IGVsZW1lbnQNCiAgICAgICAgaWYgKGVsZW1lbnQuRGF0YS5Jc19Db21wYW55ID09PSB0cnVlIHx8IGVsZW1lbnQuRGF0YS5UeXBlID09PSAnMScpIHsNCiAgICAgICAgICBlbGVtZW50LmRpc2FibGVkID0gdHJ1ZQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGVsZW1lbnQuZGlzYWJsZWQgPSBmYWxzZQ0KICAgICAgICB9DQogICAgICAgIGlmIChDaGlsZHJlbi5sZW5ndGggPiAwKSB7DQogICAgICAgICAgdGhpcy5zZXREaXNhYmxlZFRyZWUoQ2hpbGRyZW4pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyDph43nva7mkJzntKINCiAgICBoYW5kbGVSZXNldCgpIHsNCiAgICAgIHRoaXMuc2VhcmNoTnVtID0gMQ0KICAgICAgLy8gdGhpcy4kcmVmc1snc2VhcmNoRm9ybSddLnJlc2V0RmllbGRzKCkNCiAgICAgIHRoaXMuc2VhcmNoRm9ybS5SYXdOYW1lID0gJycNCiAgICAgIHRoaXMuc2VhcmNoRm9ybS5TcGVjID0gJycNCiAgICAgIHRoaXMuJHJlZnNbJ3RhYmxlJ10udGJEYXRhID0gdGhpcy5yb290VGFibGVEYXRhDQogICAgICB0aGlzLnRhYmxlRGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy5yb290VGFibGVEYXRhKSkNCiAgICB9LA0KICAgIC8vIOaWh+S7tui2heWHuuaVsOmHj+mZkOWItg0KICAgIGhhbmRsZUV4Y2VlZCgpIHsNCiAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICB0eXBlOiAnd2FybmluZycsDQogICAgICAgIG1lc3NhZ2U6ICfpmYTku7bmlbDph4/kuI3og73otoXov4c15LiqJw0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZVJldHJhY3QoKSB7DQogICAgICB0aGlzLmlzUmV0cmFjdCA9ICF0aGlzLmlzUmV0cmFjdA0KICAgIH0sDQogICAgZmV0Y2hEYXRhKCkge30sDQogICAgZ2V0SW5mbygpIHsNCiAgICAgIHRoaXMuZm9ybS5PdXRTdG9yZU5vID0gdGhpcy4kcm91dGUucXVlcnkuT3V0U3RvcmVObw0KICAgICAgdGhpcy5mb3JtLk91dFN0b3JlVHlwZSA9ICt0aGlzLiRyb3V0ZS5xdWVyeS5PdXRTdG9yZVR5cGUNCiAgICAgIC8vIGNvbnN0IF9mdW4gPSB0aGlzLiRyb3V0ZS5wYXJhbXMuT3V0U3RvcmVUeXBlID09PSAxID8gUGlja1VwT3V0U3RvcmVEZXRhaWwgOiBQYXJ0eUFPdXRTdG9yZURldGFpbA0KICAgICAgbGV0IF9mdW4NCiAgICAgIGxldCBwYXJhbXMNCiAgICAgIGlmICh0aGlzLmlzUmV0dXJuKSB7DQogICAgICAgIF9mdW4gPSBHZXRBdXhEZXRhaWxCeVJlY2VpcHQNCiAgICAgICAgcGFyYW1zID0gew0KICAgICAgICAgIElkOiB0aGlzLiRyb3V0ZS5xdWVyeS5pZA0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICBfZnVuID0gR2V0UGlja091dERldGFpbA0KICAgICAgICBwYXJhbXMgPSB7DQogICAgICAgICAgb3V0U3RvcmVObzogdGhpcy4kcm91dGUucXVlcnkuT3V0U3RvcmVObw0KICAgICAgICB9DQogICAgICB9DQogICAgICBfZnVuKHBhcmFtcykudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgY29uc3QgUmVjZWlwdCA9IHJlcy5EYXRhLlJlY2VpcHQNCiAgICAgICAgICBjb25zdCBTdWIgPSByZXMuRGF0YS5TdWINCiAgICAgICAgICBjb25zdCB7DQogICAgICAgICAgICBPdXRTdG9yZURhdGUsDQogICAgICAgICAgICBSZW1hcmssDQogICAgICAgICAgICBTeXNQcm9qZWN0SWQsDQogICAgICAgICAgICBQaWNrX0RlcGFydG1lbnRfSWQsDQogICAgICAgICAgICBVc2VfUHJvY2Vzc2luZ19JZCwNCiAgICAgICAgICAgIFdvcmtpbmdUZWFtSWQsDQogICAgICAgICAgICBSZWNlaXZlVXNlcklkLA0KICAgICAgICAgICAgQXR0YWNobWVudCwNCiAgICAgICAgICAgIFN0YXR1cw0KICAgICAgICAgIH0gPSBSZWNlaXB0DQogICAgICAgICAgdGhpcy5mb3JtLk91dFN0b3JlRGF0ZSA9IHRoaXMuZ2V0RGF0ZShuZXcgRGF0ZShPdXRTdG9yZURhdGUpKQ0KICAgICAgICAgIHRoaXMuZm9ybS5SZW1hcmsgPSBSZW1hcmsNCiAgICAgICAgICB0aGlzLmZvcm0uU3lzUHJvamVjdElkID0gU3lzUHJvamVjdElkDQogICAgICAgICAgdGhpcy5mb3JtLlVzZV9Qcm9jZXNzaW5nX0lkID0gVXNlX1Byb2Nlc3NpbmdfSWQNCiAgICAgICAgICB0aGlzLmZvcm0uUGlja19EZXBhcnRtZW50X0lkID0gUGlja19EZXBhcnRtZW50X0lkDQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5QaWNrX0RlcGFydG1lbnRfSWQpIHsNCiAgICAgICAgICAgIHRoaXMuZGVwYXJ0bWVudENoYW5nZSgpDQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMuZm9ybS5Vc2VfUHJvY2Vzc2luZ19JZCA9IFVzZV9Qcm9jZXNzaW5nX0lkDQogICAgICAgICAgdGhpcy5mb3JtLldvcmtpbmdUZWFtSWQgPSBXb3JraW5nVGVhbUlkDQogICAgICAgICAgdGhpcy5mb3JtLlJlY2VpdmVVc2VySWQgPSBSZWNlaXZlVXNlcklkDQogICAgICAgICAgdGhpcy5mb3JtLlN0YXR1cyA9IFN0YXR1cw0KDQogICAgICAgICAgLy8g5aSE55CG6KGo5qC85pWw5o2uDQogICAgICAgICAgY29uc3QgU3ViRGF0YSA9IFN1Yi5tYXAoKHJvdywgaW5kZXgpID0+IHsNCiAgICAgICAgICAgIHJvdy5pbmRleCA9IHV1aWR2NCgpDQogICAgICAgICAgICByb3cuV2FyZWhvdXNlX0xvY2F0aW9uID0gcm93LldhcmVob3VzZU5hbWUNCiAgICAgICAgICAgICAgPyByb3cuV2FyZWhvdXNlTmFtZSArICcvJyArIHJvdy5Mb2NhdGlvbk5hbWUNCiAgICAgICAgICAgICAgOiAnJw0KICAgICAgICAgICAgLy8g5Li05pe25a2Y5YKoa2cNCiAgICAgICAgICAgIC8vIHJvdy5PdXRTdG9yZVdlaWdodEtHID0gcm93Lk91dFN0b3JlV2VpZ2h0DQogICAgICAgICAgICAvLyByb3cuUG91bmRfV2VpZ2h0X0tHID0gcm93LlBvdW5kX1dlaWdodA0KICAgICAgICAgICAgLy8gcm93Lk91dFN0b3JlV2VpZ2h0ID0gTnVtYmVyKChyb3cuT3V0U3RvcmVXZWlnaHRLRyAvIDEwMDApLnRvRml4ZWQoMykpDQogICAgICAgICAgICAvLyByb3cuUG91bmRfV2VpZ2h0ID0gTnVtYmVyKChyb3cuUG91bmRfV2VpZ2h0X0tHIC8gMTAwMCkudG9GaXhlZCgzKSkNCiAgICAgICAgICAgIHJldHVybiByb3cNCiAgICAgICAgICB9KQ0KDQogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKF8pID0+IHsNCiAgICAgICAgICAgIHRoaXMuJHJlZnNbJ3RhYmxlJ10udGJEYXRhID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShTdWJEYXRhKSkNCiAgICAgICAgICAgIHRoaXMudGFibGVEYXRhID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShTdWJEYXRhKSkNCiAgICAgICAgICAgIHRoaXMucm9vdFRhYmxlRGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkoU3ViRGF0YSkpDQogICAgICAgICAgfSkNCiAgICAgICAgICBpZiAoQXR0YWNobWVudCkgew0KICAgICAgICAgICAgdGhpcy5mb3JtLkF0dGFjaG1lbnQgPSBBdHRhY2htZW50DQogICAgICAgICAgICBjb25zdCBBdHRhY2htZW50QXJyID0gQXR0YWNobWVudC5zcGxpdCgnLCcpDQogICAgICAgICAgICBBdHRhY2htZW50QXJyLmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgY29uc3QgZmlsZVVybCA9DQogICAgICAgICAgICAgICAgaXRlbS5pbmRleE9mKCc/RXhwaXJlcz0nKSA+IC0xDQogICAgICAgICAgICAgICAgICA/IGl0ZW0uc3Vic3RyaW5nKDAsIGl0ZW0ubGFzdEluZGV4T2YoJz9FeHBpcmVzPScpKQ0KICAgICAgICAgICAgICAgICAgOiBpdGVtDQogICAgICAgICAgICAgIGNvbnN0IGZpbGVOYW1lID0gZGVjb2RlVVJJKGZpbGVVcmwuc3Vic3RyaW5nKGZpbGVVcmwubGFzdEluZGV4T2YoJy8nKSArIDEpKQ0KICAgICAgICAgICAgICBjb25zdCBBdHRhY2htZW50SnNvbiA9IHt9DQogICAgICAgICAgICAgIEF0dGFjaG1lbnRKc29uLm5hbWUgPSBmaWxlTmFtZQ0KICAgICAgICAgICAgICBBdHRhY2htZW50SnNvbi51cmwgPSBmaWxlVXJsDQogICAgICAgICAgICAgIEF0dGFjaG1lbnRKc29uLmVuY3J5cHRpb25VcmwgPSBmaWxlVXJsDQogICAgICAgICAgICAgIHRoaXMuZmlsZUxpc3REYXRhLnB1c2goQXR0YWNobWVudEpzb24pDQogICAgICAgICAgICAgIHRoaXMuZmlsZUxpc3RBcnIucHVzaChBdHRhY2htZW50SnNvbikNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFzeW5jIGdldFByb2Nlc3NMaXN0QmFzZSgpIHsNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IEdldFByb2Nlc3NMaXN0QmFzZSh7fSkNCiAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgIHRoaXMuUHJvY2Vzc0xpc3QgPSByZXMuRGF0YSB8fCBbXQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5tZXNzYWdlLmVycm9yKHJlcy5NZXNhYWdlKQ0KICAgICAgfQ0KICAgIH0sDQogICAgZGVwYXJ0bWVudENoYW5nZSgpIHsNCiAgICAgIHRoaXMuZm9ybS5SZWNlaXZlVXNlcklkID0gJycNCiAgICAgIGlmICh0aGlzLmZvcm0uUGlja19EZXBhcnRtZW50X0lkKSB7DQogICAgICAgIHRoaXMuZ2V0VXNlclBhZ2VMaXN0KHRoaXMuZm9ybS5QaWNrX0RlcGFydG1lbnRfSWQpDQogICAgICB9DQogICAgfSwNCiAgICBnZXRVc2VyUGFnZUxpc3QoSWQpIHsNCiAgICAgIEdldFVzZXJQYWdlKHsNCiAgICAgICAgUGFnZVNpemU6IC0xLA0KICAgICAgICBEZXBhcnRtZW50SWQ6IElkDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLmZhY3RvcnlQZW9wbGVsaXN0ID0gcmVzLkRhdGEuRGF0YQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFzeW5jIGdldEZpcnN0TGV2ZWxEZXBhcnRzVW5kZXJGYWN0b3J5KCkgew0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0Rmlyc3RMZXZlbERlcGFydHNVbmRlckZhY3Rvcnkoew0KICAgICAgICBGYWN0b3J5SWQ6IHRoaXMuRmFjdG9yeURldGFpbERhdGEuSWQNCiAgICAgIH0pDQogICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICB0aGlzLmRlcGFydG1lbnRsaXN0ID0gcmVzLkRhdGEgfHwgW10NCiAgICAgICAgdGhpcy5mb3JtLlBpY2tfRGVwYXJ0bWVudF9JZCA9IHJlcy5EYXRhLmZpbmQoDQogICAgICAgICAgKGl0ZW0pID0+IGl0ZW0uSXNfQ3VyX1VzZXJfRGVwYXJ0DQogICAgICAgICkNCiAgICAgICAgICA/IHJlcy5EYXRhLmZpbmQoKGl0ZW0pID0+IGl0ZW0uSXNfQ3VyX1VzZXJfRGVwYXJ0KS5JZA0KICAgICAgICAgIDogJycNCiAgICAgICAgdGhpcy5kZXBhcnRtZW50Q2hhbmdlKCkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMubWVzc2FnZS5lcnJvcihyZXMuTWVzYWFnZSkNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKg0KICAgICAqIOiOt+WPluaJgOWxnumhueebrg0KICAgICAqLw0KICAgIGdldFByb2plY3QoKSB7DQogICAgICBHZXRQcm9qZWN0UGFnZUxpc3Qoew0KICAgICAgICBQYWdlOiAxLA0KICAgICAgICBQYWdlU2l6ZTogLTENCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMucHJvamVjdE9wdGlvbnMgPSByZXMuRGF0YS5EYXRhDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgKiDojrflj5bpoobmlpnkurrliJfooagNCiAgICAgKi8NCiAgICAvLyBnZXRGYWN0b3J5UGVvcGxlbGlzdCgpIHsNCiAgICAvLyAgIEdldEZhY3RvcnlQZW9wbGVsaXN0KCkudGhlbigocmVzKSA9PiB7DQogICAgLy8gICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgLy8gICAgICAgdGhpcy5mYWN0b3J5UGVvcGxlbGlzdCA9IHJlcy5EYXRhDQogICAgLy8gICAgIH0gZWxzZSB7DQogICAgLy8gICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgLy8gICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAvLyAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAvLyAgICAgICB9KQ0KICAgIC8vICAgICB9DQogICAgLy8gICB9KQ0KICAgIC8vIH0sDQoNCiAgICAvKioNCiAgICAgKiDojrflj5bpoobmlpnnj63nu4TliJfooagNCiAgICAgKi8NCiAgICBnZXRXb3JraW5nVGVhbXMoKSB7DQogICAgICBHZXRXb3JraW5nVGVhbXMoKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLldvcmtpbmdUZWFtTGlzdCA9IHJlcy5EYXRhDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQoNCiAgICBjaGFuZ2VXYXJlaG91c2Uocm93KSB7DQogICAgICB0aGlzLmN1cnJlbnRSb3cgPSByb3cNCiAgICAgIHRoaXMuaGFuZGxlV2FyZWhvdXNlKHRydWUpDQogICAgfSwNCiAgICBoYW5kbGVXYXJlaG91c2UoaXNJbmxpbmUpIHsNCiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9ICdXYXJlaG91c2UnDQogICAgICB0aGlzLmRXaWR0aCA9ICc0MCUnDQogICAgICB0aGlzLnRpdGxlID0gJ+mAieaLqeS7k+W6ky/lupPkvY0nDQogICAgICAhaXNJbmxpbmUgJiYgKHRoaXMuY3VycmVudFJvdyA9IG51bGwpDQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgfSwNCiAgICBnZXRXYXJlaG91c2UoeyB3YXJlaG91c2UsIGxvY2F0aW9uIH0pIHsNCiAgICAgIGlmICh0aGlzLmN1cnJlbnRSb3cpIHsNCiAgICAgICAgdGhpcy5jdXJyZW50Um93LlJldHVybldhcmVob3Vlc2VJZCA9IHdhcmVob3VzZS5JZA0KICAgICAgICB0aGlzLmN1cnJlbnRSb3cuUmV0dXJuTG9jYXRpb25JZCA9IGxvY2F0aW9uLklkDQogICAgICAgIHRoaXMuJHNldCh0aGlzLmN1cnJlbnRSb3csICdSZXR1cm5XYXJlaG91ZXNlTmFtZScsIHdhcmVob3VzZS5EaXNwbGF5X05hbWUpDQogICAgICAgIHRoaXMuJHNldCh0aGlzLmN1cnJlbnRSb3csICdSZXR1cm5Mb2NhdGlvbk5hbWUnLCBsb2NhdGlvbi5EaXNwbGF5X05hbWUpDQogICAgICAgIHRoaXMuJHNldCgNCiAgICAgICAgICB0aGlzLmN1cnJlbnRSb3csDQogICAgICAgICAgJ1dhcmVob3VzZV9Mb2NhdGlvbl9SZXR1cm4nLA0KICAgICAgICAgIHdhcmVob3VzZS5EaXNwbGF5X05hbWUgKyAnLycgKyBsb2NhdGlvbi5EaXNwbGF5X05hbWUNCiAgICAgICAgKQ0KICAgICAgfQ0KICAgIH0sDQogICAgYmF0Y2hFZGl0b3JGbihkYXRhKSB7DQogICAgICBpZiAodGhpcy5jdXJyZW50Um93KSB7DQogICAgICAgIGRhdGEuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmN1cnJlbnRSb3csIGl0ZW0ua2V5LCBpdGVtLnZhbCkNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24uZm9yRWFjaCgoZWxlbWVudCwgaWR4KSA9PiB7DQogICAgICAgICAgZGF0YS5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgICB0aGlzLiRzZXQoZWxlbWVudCwgaXRlbS5rZXksIGl0ZW0udmFsKQ0KICAgICAgICAgIH0pDQogICAgICAgIH0pDQogICAgICB9DQogICAgICB0aGlzLmhhbmRsZUNsb3NlKCkNCiAgICB9LA0KICAgIGNoYW5nZVN0YW5kYXJkKHJvdykgew0KICAgICAgdGhpcy5jdXJyZW50Um93ID0gcm93DQogICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSAnU3RhbmRhcmQnDQogICAgICB0aGlzLmRXaWR0aCA9ICc0MCUnDQogICAgICB0aGlzLnRpdGxlID0gJ+mAieaLqeinhOagvCcNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgIHRoaXMuJHJlZnNbJ2NvbnRlbnQnXS5nZXRPcHRpb24ocm93KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGdldFN0YW5kYXJkKHsgdHlwZSwgdmFsIH0pIHsNCiAgICAgIGlmICh0eXBlID09PSAxKSB7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLmN1cnJlbnRSb3csICdTdGFuZGFyZERlc2MnLCB2YWwpDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRzZXQodGhpcy5jdXJyZW50Um93LCAnU3RhbmRhcmREZXNjJywgdmFsLlN0YW5kYXJkRGVzYykNCiAgICAgICAgdGhpcy5jdXJyZW50Um93LlN0YW5kYXJkSWQgPSB2YWwuU3RhbmRhcmRJZA0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5Ye65bqT57G75Z6L5YiH5o2iLOihqOagvOaVsOaNrua4heepug0KICAgIHR5cGVDaGFuZ2Uobikgew0KICAgICAgaWYgKG4gIT09IDApIHsNCiAgICAgICAgdGhpcy5CaWdUeXBlID0gMQ0KICAgICAgICB0aGlzLnR5cGVOdW1iZXIxID0gMA0KICAgICAgICB0aGlzLnR5cGVOdW1iZXIyID0gMA0KICAgICAgICB0aGlzLnR5cGVOdW1iZXIzID0gMA0KICAgICAgICB0aGlzLnR5cGVOdW1iZXI0ID0gMA0KICAgICAgICB0aGlzLnRhYmxlRGF0YSA9IFtdDQogICAgICAgIHRoaXMuJHJlZnNbJ3RhYmxlJ10udGJEYXRhID0gW10NCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIGhhbmRsZVJlc2V0KCkgew0KICAgIC8vICAgdGhpcy4kcmVmc1siZm9ybSJdLnJlc2V0RmllbGRzKCk7DQogICAgLy8gICB0aGlzLnNlYXJjaCgxKTsNCiAgICAvLyB9LA0KICAgIC8vIOaWsOWinueJqeaWmQ0KICAgIGdldEFkZExpc3QobGlzdCwgaW5mbykgew0KICAgICAgdGhpcy5CaWdUeXBlID0gbGlzdFswXS5CaWdUeXBlDQogICAgICBsaXN0Lm1hcCgoaXRlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgaXRlbS5pbmRleCA9IHV1aWR2NCgpDQogICAgICAgIGl0ZW0uUGlja19Qcm9qZWN0X05hbWUgPSBpdGVtLlByb2plY3RfTmFtZSAvLyDpu5jorqTpoobnlKjpobnnm67kuLrmiYDlsZ7pobnnm64NCiAgICAgIH0pDQogICAgICB0aGlzLiRyZWZzWyd0YWJsZSddLmFkZERhdGEobGlzdCkNCiAgICAgIGNvbnN0IHRiRGF0YSA9IHRoaXMuJHJlZnMudGFibGUudGJEYXRhDQogICAgICB0aGlzLnRhYmxlRGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGJEYXRhKSkNCiAgICAgIHRoaXMucm9vdFRhYmxlRGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGJEYXRhKSkNCiAgICAgIGlmIChpbmZvKSB7DQogICAgICAgIHRoaXMuZm9ybS5Xb3JraW5nVGVhbUlkID0gaW5mby5QaWNrX1RlYW1fSWQNCiAgICAgICAgdGhpcy5mb3JtLlVzZV9Qcm9jZXNzaW5nX0lkID0gaW5mby5QaWNrX1Byb2Nlc3NfSWQNCiAgICAgIH0NCiAgICB9LA0KICAgIGltcG9ydERhdGEobGlzdCkgew0KICAgICAgdGhpcy4kcmVmc1sndGFibGUnXS5pbXBvcnREYXRhKGxpc3QpDQogICAgfSwNCiAgICBnZXRSb3dOYW1lKHsgTmFtZSwgSWQgfSkgew0KICAgICAgdGhpcy5jdXJyZW50Um93Lk5hbWUgPSBOYW1lDQogICAgICB0aGlzLmN1cnJlbnRSb3cuUmF3SWQgPSBJZA0KICAgICAgdGhpcy5jdXJyZW50Um93LlN0YW5kYXJkRGVzYyA9ICcnDQogICAgfSwNCiAgICBnZW5lcmF0ZUNvbXBvbmVudCh0aXRsZSwgY29tcG9uZW50KSB7DQogICAgICB0aGlzLnRpdGxlID0gdGl0bGUNCiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9IGNvbXBvbmVudA0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgIH0sDQogICAgaGFuZGxlRGVsZXRlKCkgew0KICAgICAgY29uc3QgdGJEYXRhID0gdGhpcy4kcmVmcy50YWJsZS50YkRhdGENCiAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24uZm9yRWFjaCgoZWxlbWVudCwgaWR4KSA9PiB7DQogICAgICAgIGNvbnN0IGkgPSB0YkRhdGEuZmluZEluZGV4KCh2KSA9PiB2LmluZGV4ID09PSBlbGVtZW50LmluZGV4KQ0KICAgICAgICB0YkRhdGEuc3BsaWNlKGksIDEpDQogICAgICB9KQ0KICAgICAgdGhpcy50YWJsZURhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRiRGF0YSkpDQogICAgICB0aGlzLnJvb3RUYWJsZURhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRiRGF0YSkpDQogICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gW10NCiAgICAgIHRoaXMuJHJlZnMudGFibGU/LiRyZWZzPy54VGFibGUuY2xlYXJDaGVja2JveFJvdygpDQogICAgfSwNCiAgICBoYW5kbGVDbG9zZShyb3cpIHsNCiAgICAgIHRoaXMub3BlbkFkZExpc3QgPSBmYWxzZQ0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICB9LA0KICAgIHRiU2VsZWN0Q2hhbmdlKGFycmF5KSB7DQogICAgICAvLyBjb25zb2xlLmxvZyhhcnJheSwgJ2Fycj09PT0nKQ0KICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbiA9IGFycmF5LnJlY29yZHMNCiAgICB9LA0KICAgIGhhbmRsZURldGFpbChyb3cpIHt9LA0KICAgIC8vIOihqOagvOaVsOaNruagoemqjA0KICAgIGNoZWNrVmFsaWRhdGUoKSB7DQogICAgICBjb25zdCBzdWJtaXQgPSBkZWVwQ2xvbmUodGhpcy4kcmVmc1sndGFibGUnXS50YkRhdGEpDQogICAgICBpZiAoIXN1Ym1pdC5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogJ+aVsOaNruS4jeiDveS4uuepuicsDQogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgIH0pDQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgc3RhdHVzOiBmYWxzZQ0KICAgICAgICB9DQogICAgICB9DQogICAgICBjb25zdCB7IHN0YXR1cywgbXNnIH0gPSB0aGlzLmNoZWNrVGIoc3VibWl0KQ0KICAgICAgaWYgKCFzdGF0dXMpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogYCR7bXNnIHx8ICflv4XloavlrZfmrrUnfeS4jeiDveS4uuepumAsDQogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgIH0pDQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgc3RhdHVzOiBmYWxzZQ0KICAgICAgICB9DQogICAgICB9DQogICAgICByZXR1cm4gew0KICAgICAgICBkYXRhOiBzdWJtaXQsDQogICAgICAgIHN0YXR1czogdHJ1ZQ0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlUmV0dXJuKCkgew0KICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm5o+Q5Lqk6YCA5bqT5L+h5oGvPycsICfmj5DnpLonLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuc2F2ZURyYWZ0KDMpDQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoZSkgPT4gew0KICAgICAgICAgIGNvbnNvbGUubG9nKCdlcnJvcicsIGUpDQoNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIHR5cGU6ICdpbmZvJywNCiAgICAgICAgICAgIG1lc3NhZ2U6ICflt7Llj5bmtognDQogICAgICAgICAgfSkNCiAgICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOaPkOS6pOWFpeW6kw0KICAgIGhhbmRsZVN1Ym1pdChyb3cpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruiupOaPkOS6pOWHuuW6k+WNlT8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pDQogICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLnNhdmVEcmFmdCgzKQ0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKGUpID0+IHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIHR5cGU6ICdpbmZvJywNCiAgICAgICAgICAgIG1lc3NhZ2U6ICflt7Llj5bmtognDQogICAgICAgICAgfSkNCiAgICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOS/neWtmOWHuuW6k+WNlQ0KICAgIHNhdmVEcmFmdCh0eXBlID0gMSkgew0KICAgICAgY29uc3QgeyBkYXRhLCBzdGF0dXMgfSA9IHRoaXMuY2hlY2tWYWxpZGF0ZSgpDQogICAgICBpZiAoIXN0YXR1cykgcmV0dXJuDQogICAgICB0aGlzLiRyZWZzWydmb3JtJ10udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICghdmFsaWQpIHJldHVybiBmYWxzZQ0KICAgICAgICB0aGlzLnNhdmVMb2FkaW5nID0gdHJ1ZQ0KICAgICAgICAvLyB0aGlzLmZvcm0uQXR0YWNobWVudCA9IHRoaXMuZmlsZUxpc3RBcnIuam9pbignLCcpDQogICAgICAgIGNvbnN0IGZvcm1BdHRhY2htZW50ID0gW10NCiAgICAgICAgaWYgKHRoaXMuZmlsZUxpc3RBcnIubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoaXMuZmlsZUxpc3RBcnIuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgICAgZm9ybUF0dGFjaG1lbnQucHVzaCgNCiAgICAgICAgICAgICAgaXRlbS5yZXNwb25zZSAmJiBpdGVtLnJlc3BvbnNlLmVuY3J5cHRpb25VcmwNCiAgICAgICAgICAgICAgICA/IGl0ZW0ucmVzcG9uc2UuZW5jcnlwdGlvblVybA0KICAgICAgICAgICAgICAgIDogaXRlbS5lbmNyeXB0aW9uVXJsDQogICAgICAgICAgICApDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLmZvcm0uQXR0YWNobWVudCA9IGZvcm1BdHRhY2htZW50LmpvaW4oJywnKQ0KICAgICAgICAvLyAx6I2J56i/IDLlrqHmoLjkuK0gM+mAmui/hyA06YCA5ZueDQogICAgICAgIHRoaXMuZm9ybS5TdGF0dXMgPSB0eXBlID09IDEgPyAxIDogMw0KICAgICAgICBjb25zdCBmb3JtID0geyAuLi50aGlzLmZvcm0gfQ0KICAgICAgICAvLyBjb25zb2xlLmxvZyhmb3JtLCAndGhpcy5mb3JtPT09MTExMScpDQogICAgICAgIC8vIGNvbnNvbGUubG9nKGRhdGEsICdTdWI9PT0xMTExJykNCiAgICAgICAgbGV0IF9mdW4NCg0KICAgICAgICBpZiAodGhpcy5pc1JldHVybikgew0KICAgICAgICAgIF9mdW4gPSBBdXhSZXR1cm5CeVJlY2VpcHQNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBfZnVuID0gUGlja091dFN0b3JlDQogICAgICAgIH0NCiAgICAgICAgX2Z1bih7DQogICAgICAgICAgUmVjZWlwdDogZm9ybSwNCiAgICAgICAgICBTdWI6IGRhdGENCiAgICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiAn5L+d5a2Y5oiQ5YqfJywNCiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgdGhpcy5jbG9zZVZpZXcoKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMuc2F2ZUxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGNoZWNrVGIobGlzdCkgew0KICAgICAgbGV0IGNoZWNrID0gbnVsbA0KICAgICAgY29uc3QgaXNFbXB0eSA9IFsnJywgbnVsbCwgdW5kZWZpbmVkXQ0KICAgICAgaWYgKHRoaXMuaXNSZXR1cm4pIHsNCiAgICAgICAgaXNFbXB0eS5wdXNoKDApDQogICAgICAgIGNoZWNrID0gWydSZXR1cm5Db3VudCcsICdXYXJlaG91c2VfTG9jYXRpb25fUmV0dXJuJ10NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNoZWNrID0gWydPdXRTdG9yZUNvdW50J10NCiAgICAgIH0NCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbGlzdC5sZW5ndGg7IGkrKykgew0KICAgICAgICBjb25zdCBpdGVtID0gbGlzdFtpXQ0KICAgICAgICBmb3IgKGxldCBqID0gMDsgaiA8IGNoZWNrLmxlbmd0aDsgaisrKSB7DQogICAgICAgICAgY29uc3QgYyA9IGNoZWNrW2pdDQogICAgICAgICAgaWYgKGlzRW1wdHkuaW5jbHVkZXMoaXRlbVtjXSkpIHsNCiAgICAgICAgICAgIGNvbnN0IGNsb3VtbnMgPSB0aGlzLiRyZWZzLnRhYmxlLnJvb3RDb2x1bW5zDQogICAgICAgICAgICBjb25zdCBlbGVtZW50ID0gY2xvdW1ucy5maW5kKCh2KSA9PiB2LkNvZGUgPT09IGMpDQogICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICBzdGF0dXM6IGZhbHNlLA0KICAgICAgICAgICAgICBtc2c6IGVsZW1lbnQ/LkRpc3BsYXlfTmFtZQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICBkZWxldGUgaXRlbS5fWF9ST1dfS0VZDQogICAgICAgIGRlbGV0ZSBpdGVtLldhcmVob3VzZU5hbWUNCiAgICAgICAgZGVsZXRlIGl0ZW0uTG9jYXRpb25OYW1lDQogICAgICB9DQogICAgICByZXR1cm4gew0KICAgICAgICBzdGF0dXM6IHRydWUsDQogICAgICAgIG1zZzogJycNCiAgICAgIH0NCiAgICB9LA0KICAgIG9wZW5BZGREaWFsb2cocm93KSB7DQogICAgICB0aGlzLm9wZW5BZGRMaXN0ID0gdHJ1ZQ0KICAgIH0sDQogICAgLy8gb3BlbkFkZERpYWxvZyhyb3cpIHsNCiAgICAvLyAgICBpZiAodGhpcy5mb3JtLlN5c1Byb2plY3RJZCkgew0KICAgIC8vICAgICB0aGlzLm9wZW5BZGRMaXN0ID0gdHJ1ZQ0KICAgIC8vICAgfSBlbHNlIHsNCiAgICAvLyAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgLy8gICAgICAgbWVzc2FnZTogJ+ivt+WFiOmAieaLqemhueebricsDQogICAgLy8gICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgLy8gICAgIH0pDQogICAgLy8gICAgIHJldHVybg0KICAgIC8vICAgfQ0KICAgIC8vIH0sDQogICAgY2xvc2VWaWV3KCkgew0KICAgICAgY2xvc2VUYWdWaWV3KHRoaXMuJHN0b3JlLCB0aGlzLiRyb3V0ZSkNCiAgICB9LA0KICAgIHNldFNlbGVjdFJvdyhtdWx0aXBsZVNlbGVjdGlvbikgew0KICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbiA9IG11bHRpcGxlU2VsZWN0aW9uDQogICAgfSwNCiAgICAvLyDml6XmnJ/moLzlvI/ljJYNCiAgICBnZXREYXRlKGRhdGEpIHsNCiAgICAgIGNvbnN0IGRhdGUgPSBkYXRhIHx8IG5ldyBEYXRlKCkNCiAgICAgIGNvbnN0IHllYXIgPSBkYXRlLmdldEZ1bGxZZWFyKCkNCiAgICAgIGNvbnN0IG1vbnRoID0gKCcwJyArIChkYXRlLmdldE1vbnRoKCkgKyAxKSkuc2xpY2UoLTIpDQogICAgICBjb25zdCBkYXkgPSAoJzAnICsgZGF0ZS5nZXREYXRlKCkpLnNsaWNlKC0yKQ0KICAgICAgcmV0dXJuIGAke3llYXJ9LSR7bW9udGh9LSR7ZGF5fWANCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["info.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "info.vue", "sourceRoot": "src/views/PRO/material-inventory-reconfig/aux-outbound", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <el-card\r\n      class=\"box-card\"\r\n      :style=\"isRetract ? 'height: 110px; overflow: hidden;' : ''\"\r\n    >\r\n      <!-- <h3 style=\"margin-bottom: 20px\">出库单信息</h3> -->\r\n      <div\r\n        class=\"toolbar-container\"\r\n        style=\"\r\n          margin-bottom: 10px;\r\n          padding-bottom: 10;\r\n          border-bottom: 1px solid #d0d3db;\r\n        \"\r\n      >\r\n        <div class=\"toolbar-title\"><span />出库单信息</div>\r\n        <div class=\"retract-container\" @click=\"handleRetract\">\r\n          <el-button type=\"text\">{{ isRetract ? \"展开\" : \"收起\" }}</el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            :icon=\"isRetract ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"出库类型\" prop=\"OutStoreType\">\r\n              <SelectMaterialStoreType v-model=\"form.OutStoreType\" type=\"RawOutStoreType\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"出库日期\" prop=\"OutStoreDate\">\r\n              <el-date-picker\r\n                v-model=\"form.OutStoreDate\"\r\n                :disabled=\"isView|| isReturn\"\r\n                :picker-options=\"pickerOptions\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                type=\"date\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"领料部门\" prop=\"Pick_Department_Id\">\r\n              <el-select\r\n                v-if=\"isProductweight === 'true'\"\r\n                v-model=\"form.Pick_Department_Id\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请选择\"\r\n                clearable\r\n                filterable\r\n                :disabled=\"isView|| isReturn\"\r\n                @change=\"departmentChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in departmentlist\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n              <el-tree-select\r\n                v-else\r\n                ref=\"treeSelectDepart\"\r\n                v-model=\"form.Pick_Department_Id\"\r\n                :select-params=\"{\r\n                  clearable: true,\r\n                  disabled: isReturn\r\n                }\"\r\n                class=\"cs-tree-x\"\r\n                :tree-params=\"treeParamsDepart\"\r\n                @select-clear=\"departmentChange\"\r\n                @node-click=\"departmentChange\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"领料人\" prop=\"ReceiveUserId\">\r\n              <el-select\r\n                v-model=\"form.ReceiveUserId\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请选择\"\r\n                clearable\r\n                filterable\r\n                :disabled=\"isView || !form.Pick_Department_Id|| isReturn\"\r\n                @change=\"userChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in factoryPeoplelist\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col v-if=\"isPurchase\" :span=\"6\">\r\n            <el-form-item label=\"领料班组\" prop=\"WorkingTeamId\">\r\n              <el-select\r\n                v-model=\"form.WorkingTeamId\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请选择\"\r\n                clearable\r\n                filterable\r\n                :disabled=\"isView\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in WorkingTeamList\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col v-if=\"isPurchase\" :span=\"6\">\r\n            <el-form-item label=\"使用工序\" prop=\"Use_Processing_Id\">\r\n              <el-select\r\n                v-model=\"form.Use_Processing_Id\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请选择\"\r\n                clearable\r\n                filterable\r\n                :disabled=\"isView|| isReturn\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in ProcessList\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <!-- <el-col :span=\"6\">\r\n            <el-form-item label=\"领用部门\" prop=\"Pick_Department_Id\">\r\n              <el-select\r\n                v-model=\"form.Pick_Department_Id\"\r\n                filterable\r\n                style=\"width: 100%\"\r\n                clearable\r\n                placeholder=\"请选择\"\r\n                :disabled=\"isView\"\r\n                @change=\"departmentChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in departmentlist\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col> -->\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"备注\" prop=\"Remark\">\r\n              <el-input\r\n                v-model=\"form.Remark\"\r\n                :disabled=\"isView|| isReturn\"\r\n                style=\"width: 100%\"\r\n                show-word-limit\r\n                :rows=\"1\"\r\n                :maxlength=\"100\"\r\n                type=\"textarea\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col v-if=\"!isReturn\" :span=\"6\">\r\n            <el-form-item label=\"附件\" class=\"factory-img\">\r\n              <OSSUpload\r\n                class=\"upload-demo\"\r\n                action=\"alioss\"\r\n                :limit=\"5\"\r\n                :multiple=\"true\"\r\n                :on-success=\"\r\n                  (response, file, fileList) => {\r\n                    uploadSuccess(response, file, fileList);\r\n                  }\r\n                \"\r\n                :on-remove=\"uploadRemove\"\r\n                :on-preview=\"handlePreview\"\r\n                :on-exceed=\"handleExceed\"\r\n                :show-file-list=\"true\"\r\n                :file-list=\"fileListData\"\r\n                :disabled=\"isView|| isReturn\"\r\n              >\r\n                <el-button\r\n                  v-if=\"!(isView|| isReturn)\"\r\n                  type=\"primary\"\r\n                  :disabled=\"isView|| isReturn\"\r\n                >上传文件</el-button>\r\n              </OSSUpload>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col v-if=\"isReturn\" :span=\"6\">\r\n            <el-form-item label=\"退库部门\" prop=\"Return_Dept_Id\">\r\n              <SelectDepartment v-model=\"form.Return_Dept_Id\" :disabled=\"isView\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col v-if=\"isReturn\" :span=\"6\">\r\n            <el-form-item label=\"退库人\" prop=\"Return_Person_Id \">\r\n              <SelectDepartmentUser v-model=\"form.Return_Person_Id\" :department-id=\"form.Return_Dept_Id\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n      </el-form>\r\n    </el-card>\r\n    <el-card class=\"box-card box-card-tb\">\r\n      <!-- <el-divider class=\"elDivder\" /> -->\r\n      <!-- <h4>出库单明细</h4> -->\r\n      <div class=\"toolbar-container\">\r\n        <div class=\"toolbar-title\"><span />出库单明细</div>\r\n      </div>\r\n      <el-divider class=\"elDivder\" />\r\n\r\n      <div style=\"display: flex; justify-content: space-between;align-items: center;\">\r\n        <div v-if=\"!isView&&!isReturn\" style=\"display: flex;align-items: center;\">\r\n          <el-button\r\n            type=\"primary\"\r\n            @click=\"openAddDialog(null)\"\r\n          >新增</el-button>\r\n          <el-button\r\n            type=\"danger\"\r\n            :disabled=\"!multipleSelection.length\"\r\n            @click=\"handleDelete\"\r\n          >删除</el-button>\r\n          <el-button type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"batchDialogVisible = true\">批量编辑领用项目</el-button>\r\n          <PickSelect style=\"margin-left: 10px\" :selected-list=\"rootTableData\" :material-type=\"1\" @addList=\"getAddList\" />\r\n        </div>\r\n        <div style=\"margin-left: auto\">\r\n          <el-form\r\n            ref=\"searchForm\"\r\n            inline\r\n            :model=\"searchForm\"\r\n            label-width=\"80px\"\r\n          >\r\n            <el-form-item label=\"辅料名称\" prop=\"RawName\">\r\n              <el-input\r\n                v-model=\"searchForm.RawName\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请输入\"\r\n                clearable\r\n              />\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"规格\" prop=\"Spec\">\r\n              <el-input\r\n                v-model=\"searchForm.Spec\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请输入\"\r\n                clearable\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"所属项目\" prop=\"SysProjectId\">\r\n              <el-select\r\n                v-model=\"searchForm.SysProjectId\"\r\n                class=\"input\"\r\n                placeholder=\"所属项目\"\r\n                clearable\r\n                filterable\r\n              >\r\n                <el-option\r\n                  v-for=\"item in projectOptions\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Short_Name\"\r\n                  :value=\"item.Sys_Project_Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item>\r\n              <el-button type=\"primary\" @click=\"handleSearch\">搜索</el-button>\r\n              <el-button @click=\"handleReset\">重置</el-button>\r\n            </el-form-item>\r\n            <DynamicTableFields\r\n              title=\"表格配置\"\r\n              :table-config-code=\"gridCode\"\r\n              :manual-hide-columns=\"[{'Code':'PartyUnitName'}]\"\r\n              @updateColumn=\"changeColumn\"\r\n            />\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"tb-x\">\r\n        <component\r\n          :is=\"currentTbComponent\"\r\n          v-if=\"currentTbComponent\"\r\n          ref=\"table\"\r\n          :is-return=\"isReturn\"\r\n          :is-view=\"isView\"\r\n          :big-type-data=\"BigType\"\r\n          @changeStandard=\"changeStandard\"\r\n          @updateTb=\"handleUpdateTb\"\r\n          @changeWarehouse=\"changeWarehouse\"\r\n          @select=\"setSelectRow\"\r\n        />\r\n      </div>\r\n      <el-divider v-if=\"!isView\" class=\"elDivder\" />\r\n      <footer v-if=\"!isView\">\r\n        <div class=\"data-info\">\r\n          <el-tag\r\n            v-if=\"!isReturn\"\r\n            size=\"medium\"\r\n            class=\"info-x\"\r\n          >已选{{ multipleSelection.length }}条数据\r\n          </el-tag>\r\n        </div>\r\n        <div>\r\n          <el-button @click=\"closeView\">取消</el-button>\r\n          <el-button v-if=\"isReturn\" :loading=\"returning\" type=\"primary\" @click=\"handleReturn\">确认退库</el-button>\r\n\r\n          <template v-else>\r\n            <el-button\r\n              :loading=\"saveLoading\"\r\n              @click=\"saveDraft(1)\"\r\n            >保存草稿\r\n            </el-button>\r\n            <el-button type=\"primary\" :loading=\"saveLoading\" @click=\"handleSubmit\">提交出库</el-button>\r\n          </template>\r\n        </div>\r\n      </footer>\r\n    </el-card>\r\n\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"dWidth\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :page-type=\"1\"\r\n        @close=\"handleClose\"\r\n        @warehouse=\"getWarehouse\"\r\n        @batchEditor=\"batchEditorFn\"\r\n        @importData=\"importData\"\r\n        @standard=\"getStandard\"\r\n        @refresh=\"fetchData\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      title=\"新增\"\r\n      :visible.sync=\"openAddList\"\r\n      width=\"70%\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <template v-if=\"openAddList\">\r\n        <add-purchase-list\r\n          ref=\"draft\"\r\n          :big-type-data=\"BigType\"\r\n          :p-form=\"form\"\r\n          :joined-items=\"rootTableData\"\r\n          @getAddList=\"getAddList\"\r\n          @getRowName=\"getRowName\"\r\n          @close=\"handleClose\"\r\n        />\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      title=\"批量编辑领用项目\"\r\n      :visible.sync=\"batchDialogVisible\"\r\n      top=\"10vh\"\r\n      width=\"350px\"\r\n      @close=\"closeBatchDialog\"\r\n    >\r\n      <el-select\r\n        v-model=\"batchProjectId\"\r\n        style=\"width: 300px\"\r\n        placeholder=\"请选择\"\r\n        clearable\r\n        filterable\r\n      >\r\n        <el-option\r\n          v-for=\"item in projectOptions\"\r\n          :key=\"item.Id\"\r\n          :label=\"item.Short_Name\"\r\n          :value=\"item.Sys_Project_Id\"\r\n        />\r\n      </el-select>\r\n      <p style=\"margin: 20px\">\r\n        <i>注：仅能批量编辑公共库存的领用项目</i>\r\n      </p>\r\n      <div style=\"text-align: right\">\r\n        <el-button @click=\"closeBatchDialog\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"batchChangeProject\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { closeTagView, debounce, deepClone } from '@/utils'\r\nimport PurchaseTb from './components/PurchaseTb.vue'\r\nimport AddList from './components/AddList.vue'\r\nimport AddPurchaseList from './components/AddPurchaseList.vue'\r\nimport ImportFile from '../components/ImportFile.vue'\r\nimport Standard from './components/Standard.vue'\r\nimport { GetProjectPageList } from '@/api/PRO/project'\r\nimport OSSUpload from '@/views/PRO/components/ossupload'\r\nimport getCommonData from '@/mixins/PRO/get-common-data/index.js'\r\nimport { GetFirstLevelDepartsUnderFactory } from '@/api/OMA/nonOperating.js'\r\nimport {\r\n  AuxReturnByReceipt,\r\n  GetAuxDetailByReceipt, GetTeamListByUserForMateriel,\r\n  GetUserPage\r\n} from '@/api/PRO/material-warehouse/material-inventory-reconfig.js'\r\n// import { GetFactoryPeoplelist } from '@/api/PRO/basic-information/workshop'\r\nimport { GetProcessListBase } from '@/api/PRO/technology-lib'\r\nimport { getDictionary } from '@/utils/common'\r\nimport { GetPreferenceSettingValue } from '@/api/sys/system-setting'\r\nimport {\r\n  PickOutStore,\r\n  GetPickOutDetail\r\n} from '@/api/PRO/material-warehouse/material-inventory-reconfig.js'\r\nimport { GetOMALatestStatisticTime } from '@/api/PRO/materialManagement'\r\nimport { GetOssUrl, GetCompanyDepartTree } from '@/api/sys'\r\nimport ReturnTb from './components/ReturnTb.vue'\r\nimport getTableConfig from '@/mixins/PRO/get-table-info-pro/index'\r\nimport Warehouse from '@/views/PRO/material-receipt-management/components/Warehouse.vue'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport SelectMaterialStoreType from '@/components/Select/SelectMaterialStoreType/index.vue'\r\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\r\nimport { GetWorkingTeams } from '@/api/PRO/technology-lib'\r\nimport SelectDepartment from '@/components/Select/SelectDepartment/index.vue'\r\nimport SelectDepartmentUser from '@/components/Select/SelectDepartmentUser/index.vue'\r\nimport PickSelect from '@/views/PRO/material_v4/pickApply/select.vue'\r\nexport default {\r\n  components: {\r\n    PickSelect,\r\n    SelectDepartmentUser, SelectDepartment,\r\n    DynamicTableFields,\r\n    SelectMaterialStoreType,\r\n    AddPurchaseList,\r\n    PurchaseTb,\r\n    ReturnTb,\r\n    ImportFile,\r\n    Warehouse,\r\n    Standard,\r\n    AddList,\r\n    OSSUpload\r\n  },\r\n  mixins: [getCommonData, getTableConfig],\r\n  props: {\r\n    pageType: {\r\n      type: Number,\r\n      default: undefined\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      isRetract: false, // 是否展开\r\n      returning: false,\r\n      tbLoading: false,\r\n      projectOptions: [],\r\n      multipleSelection: [],\r\n      // PartyUnitData: [],\r\n      // SupplierData: [],\r\n      factoryPeoplelist: [], // 领料人列表\r\n      departmentlist: [],\r\n      ProcessList: [],\r\n      AuxOutboundTypeList: [],\r\n      WorkingTeamList: [], // 领料班组列表\r\n      searchForm: {\r\n        RawName: '',\r\n        Spec: ''\r\n      },\r\n      form: {\r\n        OutStoreNo: '', // 出库单号\r\n        OutStoreType: 1, // 出库类型\r\n        OutStoreDate: this.getDate(),\r\n        Use_Processing_Id: '', // 工序id\r\n        Pick_Department_Id: '',\r\n        // ProjectId: '',\r\n        SysProjectId: '',\r\n        Pick_Project_Name: '', // 领用项目名称\r\n        ReceiveUserId: '', // 领料人id\r\n        WorkingTeamId: '', // 领料班组id\r\n        Remark: '',\r\n        Attachment: ''\r\n      },\r\n      rules: {\r\n        OutStoreType: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        OutStoreDate: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        Pick_Department_Id: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        ReceiveUserId: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ]\r\n        // WorkingTeamId: [\r\n        //   { required: true, message: '请选择', trigger: 'change' }\r\n        // ]\r\n      },\r\n      treeParamsDepart: {\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          disabled: 'disabled',\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      backendDate: null,\r\n      pickerOptions: {\r\n        disabledDate: (time) => {\r\n          return time.getTime() < new Date(this.backendDate).getTime() // 限制选择日期不能超过当前日期\r\n        }\r\n      },\r\n      currentComponent: '',\r\n      title: '',\r\n      dWidth: '60%',\r\n      // isSingle: false,\r\n      saveLoading: false,\r\n      search: () => ({}),\r\n      openAddList: false,\r\n      dialogVisible: false,\r\n      BigType: 1,\r\n      isProductweight: null,\r\n      fileListData: [],\r\n      fileListArr: [],\r\n      searchNum: 1,\r\n      rootTableData: [],\r\n      tableData: [],\r\n      typeNumber1: 0,\r\n      typeNumber2: 0,\r\n      typeNumber3: 0,\r\n      typeNumber4: 0,\r\n      currentTbComponent: '',\r\n      batchDialogVisible: false,\r\n      batchProjectId: ''\r\n    }\r\n  },\r\n  computed: {\r\n    isAdd() {\r\n      return this.pageType === 1\r\n    },\r\n    isEdit() {\r\n      return this.pageType === 2\r\n    },\r\n    isView() {\r\n      return this.pageType === 3\r\n    },\r\n    isPurchase() {\r\n      return this.form.OutStoreType == 1 // 领用出库\r\n    },\r\n    isReturn() {\r\n      return this.pageType === 8\r\n    },\r\n    gridCode() {\r\n      return this.isReturn ? 'pro_aux_material_outbound_detail_list_return' : 'pro_aux_material_outbound_detail_list,Steel'\r\n    }\r\n  },\r\n  async mounted() {\r\n    if (this.isReturn) {\r\n      this.currentTbComponent = ReturnTb\r\n      const column = await this.getTableConfig(this.gridCode)\r\n      this.$nextTick(_ => {\r\n        this.$refs['table'].init(column)\r\n      })\r\n    } else {\r\n      this.currentTbComponent = PurchaseTb\r\n    }\r\n    await this.getCurFactory()\r\n    await this.getOMALatestStatisticTime()\r\n    this.getPreferenceSettingValue()\r\n    this.AuxOutboundTypeList = await getDictionary('AuxOutboundType')\r\n    await this.getFirstLevelDepartsUnderFactory()\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    this.getProject()\r\n    this.getProcessListBase()\r\n    // this.getFactoryPeoplelist()\r\n    this.getWorkingTeams()\r\n    if (!this.isAdd) {\r\n      this.getInfo()\r\n    }\r\n  },\r\n  methods: {\r\n    userChange(val) {\r\n      GetTeamListByUserForMateriel({\r\n        id: val\r\n      }).then(res => {\r\n        if (res.Data?.length === 1) {\r\n          this.$set(this.form, 'WorkingTeamId', res.Data[0].Id)\r\n        } else {\r\n          this.$set(this.form, 'WorkingTeamId', '')\r\n        }\r\n      })\r\n    },\r\n    closeBatchDialog() {\r\n      this.batchProjectId = ''\r\n      this.batchDialogVisible = false\r\n    },\r\n    batchChangeProject() {\r\n      const tbData = this.$refs.table.tbData\r\n      this.multipleSelection.forEach((element, idx) => {\r\n        const item = tbData.find((v) => v.index === element.index)\r\n        const i = tbData.findIndex((v) => v.index === element.index)\r\n        item.Pick_Sys_Project_Id = this.batchProjectId\r\n        // 同时更新项目名称\r\n        item.Pick_Project_Name = this.projectOptions.find(project => project.Sys_Project_Id === this.batchProjectId)?.Short_Name\r\n        this.$set(tbData, i, item)\r\n      })\r\n      this.closeBatchDialog()\r\n    },\r\n    changeColumn() {\r\n      const temp = this.currentTbComponent\r\n      this.currentTbComponent = ''\r\n      this.$nextTick(async() => {\r\n        this.currentTbComponent = temp\r\n        if (this.isReturn) {\r\n          const column = await this.getTableConfig(this.gridCode)\r\n          this.$nextTick(_ => {\r\n            this.$refs['table'].init(column)\r\n          })\r\n        }\r\n        this.$refs['table'].setData(this.tableData)\r\n      })\r\n    },\r\n    // 附件上传成功\r\n    uploadSuccess(response, file, fileList) {\r\n      this.fileListArr = JSON.parse(JSON.stringify(fileList))\r\n    },\r\n    // 移除已上传文件\r\n    uploadRemove(file, fileList) {\r\n      this.fileListArr = JSON.parse(JSON.stringify(fileList))\r\n    },\r\n    // 点击已上传文件\r\n    async handlePreview(file) {\r\n      let encryptionUrl = ''\r\n      if (file.response && file.response.encryptionUrl) {\r\n        encryptionUrl = file.response.encryptionUrl\r\n      } else {\r\n        encryptionUrl = await GetOssUrl({ url: file.encryptionUrl })\r\n        encryptionUrl = encryptionUrl.Data\r\n      }\r\n      window.open(encryptionUrl)\r\n    },\r\n    // 获取运营核算已统计的最新日期\r\n    async getOMALatestStatisticTime() {\r\n      const res = await GetOMALatestStatisticTime({})\r\n      if (res.IsSucceed) {\r\n        this.backendDate = res.Data || ''\r\n      } else {\r\n        this.message.error(res.Mesaage)\r\n      }\r\n    },\r\n    // 搜索\r\n    handleSearch() {\r\n      this.tableData = JSON.parse(JSON.stringify(this.rootTableData))\r\n      if (this.searchForm.RawName) {\r\n        const rawNameRegex = new RegExp(this.searchForm.RawName, 'i')\r\n        this.tableData = this.tableData.filter((item) => {\r\n          return rawNameRegex.test(item.RawName)\r\n        })\r\n      }\r\n\r\n      if (this.searchForm.Spec) {\r\n        const specRegex = new RegExp(this.searchForm.Spec, 'i')\r\n        this.tableData = this.tableData.filter((item) => {\r\n          return specRegex.test(item.Spec)\r\n        })\r\n      }\r\n      if (this.searchForm.SysProjectId) {\r\n        const sysProjectIdRegex = new RegExp(this.searchForm.SysProjectId, 'i')\r\n        this.tableData = this.tableData.filter((item) => {\r\n          return sysProjectIdRegex.test(item.Sys_Project_Id)\r\n        })\r\n      }\r\n      if (this.isReturn) {\r\n        this.$refs['table'].setData(this.tableData)\r\n      } else {\r\n        this.$refs['table'].tbData = this.tableData\r\n      }\r\n    },\r\n    // 更新表格数据\r\n    handleUpdateTb() {\r\n      this.rootTableData = this.$refs['table'].tbData\r\n      this.tableData = JSON.parse(JSON.stringify(this.rootTableData))\r\n      console.log(this.rootTableData, '11')\r\n      console.log(this.tableData, '22')\r\n      // this.setTabData()\r\n    },\r\n    // 获取品重偏好设置\r\n    getPreferenceSettingValue() {\r\n      GetPreferenceSettingValue({ code: 'Productweight' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.isProductweight = res.Data\r\n          // this.isProductweight = \"false\";\r\n          if (this.isProductweight !== 'true') {\r\n            this.getDepartmentTree()\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getDepartmentTree() {\r\n      GetCompanyDepartTree({ isAll: true }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const tree = res.Data\r\n          this.setDisabledTree(tree)\r\n          this.treeParamsDepart.data = tree\r\n          this.$nextTick((_) => {\r\n            this.$refs.treeSelectDepart.treeDataUpdateFun(tree)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setDisabledTree(root) {\r\n      if (!root) return\r\n      root.forEach((element) => {\r\n        const { Children } = element\r\n        if (element.Data.Is_Company === true || element.Data.Type === '1') {\r\n          element.disabled = true\r\n        } else {\r\n          element.disabled = false\r\n        }\r\n        if (Children.length > 0) {\r\n          this.setDisabledTree(Children)\r\n        }\r\n      })\r\n    },\r\n    // 重置搜索\r\n    handleReset() {\r\n      this.searchNum = 1\r\n      // this.$refs['searchForm'].resetFields()\r\n      this.searchForm.RawName = ''\r\n      this.searchForm.Spec = ''\r\n      this.$refs['table'].tbData = this.rootTableData\r\n      this.tableData = JSON.parse(JSON.stringify(this.rootTableData))\r\n    },\r\n    // 文件超出数量限制\r\n    handleExceed() {\r\n      this.$message({\r\n        type: 'warning',\r\n        message: '附件数量不能超过5个'\r\n      })\r\n    },\r\n    handleRetract() {\r\n      this.isRetract = !this.isRetract\r\n    },\r\n    fetchData() {},\r\n    getInfo() {\r\n      this.form.OutStoreNo = this.$route.query.OutStoreNo\r\n      this.form.OutStoreType = +this.$route.query.OutStoreType\r\n      // const _fun = this.$route.params.OutStoreType === 1 ? PickUpOutStoreDetail : PartyAOutStoreDetail\r\n      let _fun\r\n      let params\r\n      if (this.isReturn) {\r\n        _fun = GetAuxDetailByReceipt\r\n        params = {\r\n          Id: this.$route.query.id\r\n        }\r\n      } else {\r\n        _fun = GetPickOutDetail\r\n        params = {\r\n          outStoreNo: this.$route.query.OutStoreNo\r\n        }\r\n      }\r\n      _fun(params).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const Receipt = res.Data.Receipt\r\n          const Sub = res.Data.Sub\r\n          const {\r\n            OutStoreDate,\r\n            Remark,\r\n            SysProjectId,\r\n            Pick_Department_Id,\r\n            Use_Processing_Id,\r\n            WorkingTeamId,\r\n            ReceiveUserId,\r\n            Attachment,\r\n            Status\r\n          } = Receipt\r\n          this.form.OutStoreDate = this.getDate(new Date(OutStoreDate))\r\n          this.form.Remark = Remark\r\n          this.form.SysProjectId = SysProjectId\r\n          this.form.Use_Processing_Id = Use_Processing_Id\r\n          this.form.Pick_Department_Id = Pick_Department_Id\r\n          if (this.form.Pick_Department_Id) {\r\n            this.departmentChange()\r\n          }\r\n          this.form.Use_Processing_Id = Use_Processing_Id\r\n          this.form.WorkingTeamId = WorkingTeamId\r\n          this.form.ReceiveUserId = ReceiveUserId\r\n          this.form.Status = Status\r\n\r\n          // 处理表格数据\r\n          const SubData = Sub.map((row, index) => {\r\n            row.index = uuidv4()\r\n            row.Warehouse_Location = row.WarehouseName\r\n              ? row.WarehouseName + '/' + row.LocationName\r\n              : ''\r\n            // 临时存储kg\r\n            // row.OutStoreWeightKG = row.OutStoreWeight\r\n            // row.Pound_Weight_KG = row.Pound_Weight\r\n            // row.OutStoreWeight = Number((row.OutStoreWeightKG / 1000).toFixed(3))\r\n            // row.Pound_Weight = Number((row.Pound_Weight_KG / 1000).toFixed(3))\r\n            return row\r\n          })\r\n\r\n          this.$nextTick((_) => {\r\n            this.$refs['table'].tbData = JSON.parse(JSON.stringify(SubData))\r\n            this.tableData = JSON.parse(JSON.stringify(SubData))\r\n            this.rootTableData = JSON.parse(JSON.stringify(SubData))\r\n          })\r\n          if (Attachment) {\r\n            this.form.Attachment = Attachment\r\n            const AttachmentArr = Attachment.split(',')\r\n            AttachmentArr.forEach((item) => {\r\n              const fileUrl =\r\n                item.indexOf('?Expires=') > -1\r\n                  ? item.substring(0, item.lastIndexOf('?Expires='))\r\n                  : item\r\n              const fileName = decodeURI(fileUrl.substring(fileUrl.lastIndexOf('/') + 1))\r\n              const AttachmentJson = {}\r\n              AttachmentJson.name = fileName\r\n              AttachmentJson.url = fileUrl\r\n              AttachmentJson.encryptionUrl = fileUrl\r\n              this.fileListData.push(AttachmentJson)\r\n              this.fileListArr.push(AttachmentJson)\r\n            })\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async getProcessListBase() {\r\n      const res = await GetProcessListBase({})\r\n      if (res.IsSucceed) {\r\n        this.ProcessList = res.Data || []\r\n      } else {\r\n        this.message.error(res.Mesaage)\r\n      }\r\n    },\r\n    departmentChange() {\r\n      this.form.ReceiveUserId = ''\r\n      if (this.form.Pick_Department_Id) {\r\n        this.getUserPageList(this.form.Pick_Department_Id)\r\n      }\r\n    },\r\n    getUserPageList(Id) {\r\n      GetUserPage({\r\n        PageSize: -1,\r\n        DepartmentId: Id\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.factoryPeoplelist = res.Data.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async getFirstLevelDepartsUnderFactory() {\r\n      const res = await GetFirstLevelDepartsUnderFactory({\r\n        FactoryId: this.FactoryDetailData.Id\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.departmentlist = res.Data || []\r\n        this.form.Pick_Department_Id = res.Data.find(\r\n          (item) => item.Is_Cur_User_Depart\r\n        )\r\n          ? res.Data.find((item) => item.Is_Cur_User_Depart).Id\r\n          : ''\r\n        this.departmentChange()\r\n      } else {\r\n        this.message.error(res.Mesaage)\r\n      }\r\n    },\r\n    /**\r\n     * 获取所属项目\r\n     */\r\n    getProject() {\r\n      GetProjectPageList({\r\n        Page: 1,\r\n        PageSize: -1\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.projectOptions = res.Data.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 获取领料人列表\r\n     */\r\n    // getFactoryPeoplelist() {\r\n    //   GetFactoryPeoplelist().then((res) => {\r\n    //     if (res.IsSucceed) {\r\n    //       this.factoryPeoplelist = res.Data\r\n    //     } else {\r\n    //       this.$message({\r\n    //         message: res.Message,\r\n    //         type: 'error'\r\n    //       })\r\n    //     }\r\n    //   })\r\n    // },\r\n\r\n    /**\r\n     * 获取领料班组列表\r\n     */\r\n    getWorkingTeams() {\r\n      GetWorkingTeams().then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.WorkingTeamList = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    changeWarehouse(row) {\r\n      this.currentRow = row\r\n      this.handleWarehouse(true)\r\n    },\r\n    handleWarehouse(isInline) {\r\n      this.currentComponent = 'Warehouse'\r\n      this.dWidth = '40%'\r\n      this.title = '选择仓库/库位'\r\n      !isInline && (this.currentRow = null)\r\n      this.dialogVisible = true\r\n    },\r\n    getWarehouse({ warehouse, location }) {\r\n      if (this.currentRow) {\r\n        this.currentRow.ReturnWarehoueseId = warehouse.Id\r\n        this.currentRow.ReturnLocationId = location.Id\r\n        this.$set(this.currentRow, 'ReturnWarehoueseName', warehouse.Display_Name)\r\n        this.$set(this.currentRow, 'ReturnLocationName', location.Display_Name)\r\n        this.$set(\r\n          this.currentRow,\r\n          'Warehouse_Location_Return',\r\n          warehouse.Display_Name + '/' + location.Display_Name\r\n        )\r\n      }\r\n    },\r\n    batchEditorFn(data) {\r\n      if (this.currentRow) {\r\n        data.forEach((item) => {\r\n          this.$set(this.currentRow, item.key, item.val)\r\n        })\r\n      } else {\r\n        this.multipleSelection.forEach((element, idx) => {\r\n          data.forEach((item) => {\r\n            this.$set(element, item.key, item.val)\r\n          })\r\n        })\r\n      }\r\n      this.handleClose()\r\n    },\r\n    changeStandard(row) {\r\n      this.currentRow = row\r\n      this.currentComponent = 'Standard'\r\n      this.dWidth = '40%'\r\n      this.title = '选择规格'\r\n      this.dialogVisible = true\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].getOption(row)\r\n      })\r\n    },\r\n    getStandard({ type, val }) {\r\n      if (type === 1) {\r\n        this.$set(this.currentRow, 'StandardDesc', val)\r\n      } else {\r\n        this.$set(this.currentRow, 'StandardDesc', val.StandardDesc)\r\n        this.currentRow.StandardId = val.StandardId\r\n      }\r\n    },\r\n    // 出库类型切换,表格数据清空\r\n    typeChange(n) {\r\n      if (n !== 0) {\r\n        this.BigType = 1\r\n        this.typeNumber1 = 0\r\n        this.typeNumber2 = 0\r\n        this.typeNumber3 = 0\r\n        this.typeNumber4 = 0\r\n        this.tableData = []\r\n        this.$refs['table'].tbData = []\r\n      }\r\n    },\r\n    // handleReset() {\r\n    //   this.$refs[\"form\"].resetFields();\r\n    //   this.search(1);\r\n    // },\r\n    // 新增物料\r\n    getAddList(list, info) {\r\n      this.BigType = list[0].BigType\r\n      list.map((item, index) => {\r\n        item.index = uuidv4()\r\n        item.Pick_Project_Name = item.Project_Name // 默认领用项目为所属项目\r\n      })\r\n      this.$refs['table'].addData(list)\r\n      const tbData = this.$refs.table.tbData\r\n      this.tableData = JSON.parse(JSON.stringify(tbData))\r\n      this.rootTableData = JSON.parse(JSON.stringify(tbData))\r\n      if (info) {\r\n        this.form.WorkingTeamId = info.Pick_Team_Id\r\n        this.form.Use_Processing_Id = info.Pick_Process_Id\r\n      }\r\n    },\r\n    importData(list) {\r\n      this.$refs['table'].importData(list)\r\n    },\r\n    getRowName({ Name, Id }) {\r\n      this.currentRow.Name = Name\r\n      this.currentRow.RawId = Id\r\n      this.currentRow.StandardDesc = ''\r\n    },\r\n    generateComponent(title, component) {\r\n      this.title = title\r\n      this.currentComponent = component\r\n      this.dialogVisible = true\r\n    },\r\n    handleDelete() {\r\n      const tbData = this.$refs.table.tbData\r\n      this.multipleSelection.forEach((element, idx) => {\r\n        const i = tbData.findIndex((v) => v.index === element.index)\r\n        tbData.splice(i, 1)\r\n      })\r\n      this.tableData = JSON.parse(JSON.stringify(tbData))\r\n      this.rootTableData = JSON.parse(JSON.stringify(tbData))\r\n      this.multipleSelection = []\r\n      this.$refs.table?.$refs?.xTable.clearCheckboxRow()\r\n    },\r\n    handleClose(row) {\r\n      this.openAddList = false\r\n      this.dialogVisible = false\r\n    },\r\n    tbSelectChange(array) {\r\n      // console.log(array, 'arr====')\r\n      this.multipleSelection = array.records\r\n    },\r\n    handleDetail(row) {},\r\n    // 表格数据校验\r\n    checkValidate() {\r\n      const submit = deepClone(this.$refs['table'].tbData)\r\n      if (!submit.length) {\r\n        this.$message({\r\n          message: '数据不能为空',\r\n          type: 'warning'\r\n        })\r\n        return {\r\n          status: false\r\n        }\r\n      }\r\n      const { status, msg } = this.checkTb(submit)\r\n      if (!status) {\r\n        this.$message({\r\n          message: `${msg || '必填字段'}不能为空`,\r\n          type: 'warning'\r\n        })\r\n        return {\r\n          status: false\r\n        }\r\n      }\r\n      return {\r\n        data: submit,\r\n        status: true\r\n      }\r\n    },\r\n    handleReturn() {\r\n      this.$confirm('是否提交退库信息?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          this.saveDraft(3)\r\n        })\r\n        .catch((e) => {\r\n          console.log('error', e)\r\n\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n    },\r\n    // 提交入库\r\n    handleSubmit(row) {\r\n      this.$confirm('确认提交出库单?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          this.saveDraft(3)\r\n        })\r\n        .catch((e) => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n    },\r\n    // 保存出库单\r\n    saveDraft(type = 1) {\r\n      const { data, status } = this.checkValidate()\r\n      if (!status) return\r\n      this.$refs['form'].validate((valid) => {\r\n        if (!valid) return false\r\n        this.saveLoading = true\r\n        // this.form.Attachment = this.fileListArr.join(',')\r\n        const formAttachment = []\r\n        if (this.fileListArr.length > 0) {\r\n          this.fileListArr.forEach((item) => {\r\n            formAttachment.push(\r\n              item.response && item.response.encryptionUrl\r\n                ? item.response.encryptionUrl\r\n                : item.encryptionUrl\r\n            )\r\n          })\r\n        }\r\n        this.form.Attachment = formAttachment.join(',')\r\n        // 1草稿 2审核中 3通过 4退回\r\n        this.form.Status = type == 1 ? 1 : 3\r\n        const form = { ...this.form }\r\n        // console.log(form, 'this.form===1111')\r\n        // console.log(data, 'Sub===1111')\r\n        let _fun\r\n\r\n        if (this.isReturn) {\r\n          _fun = AuxReturnByReceipt\r\n        } else {\r\n          _fun = PickOutStore\r\n        }\r\n        _fun({\r\n          Receipt: form,\r\n          Sub: data\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            this.closeView()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.saveLoading = false\r\n        })\r\n      })\r\n    },\r\n    checkTb(list) {\r\n      let check = null\r\n      const isEmpty = ['', null, undefined]\r\n      if (this.isReturn) {\r\n        isEmpty.push(0)\r\n        check = ['ReturnCount', 'Warehouse_Location_Return']\r\n      } else {\r\n        check = ['OutStoreCount']\r\n      }\r\n      for (let i = 0; i < list.length; i++) {\r\n        const item = list[i]\r\n        for (let j = 0; j < check.length; j++) {\r\n          const c = check[j]\r\n          if (isEmpty.includes(item[c])) {\r\n            const cloumns = this.$refs.table.rootColumns\r\n            const element = cloumns.find((v) => v.Code === c)\r\n            return {\r\n              status: false,\r\n              msg: element?.Display_Name\r\n            }\r\n          }\r\n        }\r\n        delete item._X_ROW_KEY\r\n        delete item.WarehouseName\r\n        delete item.LocationName\r\n      }\r\n      return {\r\n        status: true,\r\n        msg: ''\r\n      }\r\n    },\r\n    openAddDialog(row) {\r\n      this.openAddList = true\r\n    },\r\n    // openAddDialog(row) {\r\n    //    if (this.form.SysProjectId) {\r\n    //     this.openAddList = true\r\n    //   } else {\r\n    //     this.$message({\r\n    //       message: '请先选择项目',\r\n    //       type: 'warning'\r\n    //     })\r\n    //     return\r\n    //   }\r\n    // },\r\n    closeView() {\r\n      closeTagView(this.$store, this.$route)\r\n    },\r\n    setSelectRow(multipleSelection) {\r\n      this.multipleSelection = multipleSelection\r\n    },\r\n    // 日期格式化\r\n    getDate(data) {\r\n      const date = data || new Date()\r\n      const year = date.getFullYear()\r\n      const month = ('0' + (date.getMonth() + 1)).slice(-2)\r\n      const day = ('0' + date.getDate()).slice(-2)\r\n      return `${year}-${month}-${day}`\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.app-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  .box-card-tb {\r\n    flex: 1;\r\n    margin-top: 8px;\r\n  }\r\n}\r\n// 表格工具栏css\r\n.toolbar-container {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  // margin: 10px 0 0 0;\r\n  // flex-wrap: nowrap;\r\n  ::v-deep .el-radio-group {\r\n    width: 400px;\r\n  }\r\n  .toolbar-title {\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    color: #333333;\r\n    span {\r\n      display: inline-block;\r\n      width: 2px;\r\n      height: 14px;\r\n      background: #009dff;\r\n      margin-right: 6px;\r\n      vertical-align: text-top;\r\n    }\r\n  }\r\n  .search-form {\r\n    width: 60%;\r\n    ::v-deep {\r\n      .el-form-item--small {\r\n        margin-bottom: 0;\r\n      }\r\n      .el-form-item__content {\r\n        width: 110px;\r\n      }\r\n      .last-btn {\r\n        display: flex;\r\n        justify-content: flex-end;\r\n        .el-form-item {\r\n          margin-right: 0;\r\n        }\r\n        .el-form-item__content {\r\n          width: 100%;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .statistics-container {\r\n    display: flex;\r\n    .statistics-item {\r\n      margin-right: 32px;\r\n      span:first-child {\r\n        display: inline-block;\r\n        font-size: 14px;\r\n        line-height: 18px;\r\n        font-weight: 500;\r\n        color: #999999;\r\n        margin-right: 16px !important;\r\n      }\r\n      span:last-child {\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n        color: #00c361;\r\n      }\r\n    }\r\n  }\r\n}\r\n.el-card {\r\n  ::v-deep {\r\n    .el-card__body {\r\n      display: flex;\r\n      flex-direction: column;\r\n    }\r\n  }\r\n\r\n  .tb-x {\r\n    flex: 1;\r\n    margin-bottom: 10px;\r\n    overflow: auto;\r\n  }\r\n}\r\n\r\n::v-deep .elDivder {\r\n  margin: 10px;\r\n}\r\n\r\n.pagination-container {\r\n  text-align: right;\r\n  margin-top: 10px;\r\n  padding: 0;\r\n}\r\n\r\n.upload-file-list {\r\n  & > div {\r\n    width: 100%;\r\n    height: 30px;\r\n    line-height: 30px;\r\n    padding-left: 15px;\r\n    padding-right: 15px;\r\n    cursor: pointer;\r\n    position: relative;\r\n    i {\r\n      margin-right: 10px;\r\n    }\r\n    i:last-child {\r\n      position: absolute;\r\n      top: 50%;\r\n      transform: translateY(-50%);\r\n      right: 15px;\r\n      color: #999999;\r\n      font-size: 18px;\r\n    }\r\n  }\r\n  & > div:hover {\r\n    background-color: #f8f8f8;\r\n    i:last-child {\r\n      color: #298dff;\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .el-form-item {\r\n  .el-form-item__content {\r\n    .el-tree-select-input {\r\n      width: 100% !important;\r\n    }\r\n  }\r\n}\r\n\r\nfooter {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\component-list\\v4\\component\\bimdialog.vue?vue&type=style&index=0&id=5800fb80&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\component-list\\v4\\component\\bimdialog.vue", "mtime": 1757468112574}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmNzLWFsZXJ0IHsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgaGVpZ2h0OiAzOHB4OwogIGxpbmUtaGVpZ2h0OiAzOHB4OwogIGNvbG9yOiAjRjVDMTVBOwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBtYXJnaW4tYm90dG9tOiAzMHB4OwoKICAmLWluZm8gewogICAgY29sb3I6ICMyOThERkY7CiAgfQoKICAuZWwtaWNvbi13YXJuaW5nLW91dGxpbmUgewogICAgbWFyZ2luLWxlZnQ6IDE2cHg7CiAgfQoKICAmOmFmdGVyIHsKICAgIGNvbnRlbnQ6ICcnOwogICAgdG9wOiAwOwogICAgbGVmdDogMDsKICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgIHdpZHRoOiAxMDAlOwogICAgaGVpZ2h0OiAxMDAlOwogICAgYmFja2dyb3VuZDogI0Y1QzE1QTsKICAgIG9wYWNpdHk6IDAuMTI7CiAgICBwb2ludGVyLWV2ZW50czogbm9uZTsKICB9Cn0K"}, {"version": 3, "sources": ["bimdialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6jBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "bimdialog.vue", "sourceRoot": "src/views/PRO/component-list/v4/component", "sourcesContent": ["<template>\r\n  <el-dialog\r\n    class=\"plm-custom-dialog\"\r\n    :title=\"title\"\r\n    :visible.sync=\"dialogVisible\"\r\n    width=\"570px\"\r\n    top=\"5vh\"\r\n    :loading=\"loading\"\r\n    @submitbtn=\"handleSubmit('form')\"\r\n    @cancelbtn=\"handleClose\"\r\n    @handleClose=\"handleClose\"\r\n    @close=\"handleClose\"\r\n  >\r\n    <div class=\"cs-alert\">\r\n      <i class=\"el-icon-warning-outline\" />注意：请先<el-button type=\"text\" @click=\"getTemplate\">下载构件导入模板</el-button>\r\n    </div>\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n      <!--      <el-form-item v-if=\"!isVersionFour\" label=\"下载模板\" prop=\"Template_Type\">\r\n        <el-radio-group v-model=\"form.Template_Type\" @input=\"radioChange\">\r\n          <el-radio :label=\"2\">固定模板</el-radio>\r\n          <el-radio :label=\"1\">动态模板</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>-->\r\n      <el-form-item label=\"导入方式\" prop=\"areaType\">\r\n        <el-radio-group v-model=\"areaType\">\r\n          <el-radio :label=\"2\">多区域导入</el-radio>\r\n          <el-radio :label=\"1\">单区域导入</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item label=\"是否跳过生产\" prop=\"Is_Skip_Production\">\r\n        <el-radio-group v-model=\"form.Is_Skip_Production\">\r\n          <el-radio :label=\"true\">是</el-radio>\r\n          <el-radio :label=\"false\">否</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item label=\"项目名称\" prop=\"Project_Name\">\r\n        <el-input v-model=\"form.Project_Name\" style=\"width: 360px\" disabled />\r\n      </el-form-item>\r\n      <el-form-item v-if=\"areaType===1\" label=\"区域\" prop=\"Area_Name\">\r\n        <el-input v-model=\"form.Area_Name\" style=\"width: 360px\" disabled />\r\n      </el-form-item>\r\n      <el-form-item label=\"类别名称\" prop=\"Type_Name\">\r\n        <el-input v-model=\"form.Type_Name\" style=\"width: 360px\" disabled />\r\n      </el-form-item>\r\n      <el-form-item label=\"标题\" prop=\"Doc_Title\">\r\n        <el-input v-model=\"form.Doc_Title\" style=\"width: 360px\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"简要描述\" prop=\"Doc_Content\">\r\n        <el-input v-model=\"form.Doc_Content\" style=\"width: 360px\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"附件信息\" prop=\"Doc_File\">\r\n        <el-input v-model=\"form.Doc_File\" style=\"width: 360px\" disabled />\r\n      </el-form-item><!--      <el-form-item\r\n        v-if=\"!isVersionFour&&form.Type === 1&&!isDynamicTemplate\"\r\n        :rules=\" [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ]\"\r\n        label=\"自动拆分直发件\"\r\n        prop=\"Is_Auto_Split\"\r\n      >\r\n        <el-radio-group v-model=\"form.Is_Auto_Split\" :disabled=\"[true,false].includes(isAutoSplit)\">\r\n          <el-radio :label=\"false\">否</el-radio>\r\n          <el-radio :label=\"true\">是</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>-->\r\n\r\n      <el-form-item label=\"上传附件\">\r\n        <OSSUpload\r\n          ref=\"company\"\r\n          drag\r\n          class=\"upload-demo\"\r\n          :action=\"$store.state.uploadUrl\"\r\n          :on-change=\"handleChange\"\r\n          :before-upload=\"beforeUpload\"\r\n          :file-list=\"fileList\"\r\n          :limit=\"2\"\r\n          :on-success=\"uploadSuccess\"\r\n          :on-error=\"uploadError\"\r\n          :before-remove=\"beforeRemove\"\r\n          :on-remove=\"handleRemove\"\r\n          :multiple=\"false\"\r\n          :accept=\"allowFile\"\r\n        >\r\n          <!-- :on-exceed=\"onExceed\" -->\r\n          <i class=\"el-icon-upload\" />\r\n          <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n        </OSSUpload>\r\n      </el-form-item>\r\n    </el-form>\r\n    <span slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n      <el-button\r\n        type=\"primary\"\r\n        :loading=\"btnLoading\"\r\n        @click=\"handleSubmit()\"\r\n      >确 定</el-button>\r\n    </span>\r\n  </el-dialog>\r\n\r\n</template>\r\n\r\n<script>\r\nimport OSSUpload from '@/views/plm/components/ossupload.vue'\r\nimport {\r\n  GenerateDeepenFileFromDirect,\r\n  UpdatePartAggregateId, AppendImportDeepFiles, ThreeBomImportTemplate\r\n} from '@/api/PRO/component'\r\nimport { combineURL } from '@/utils'\r\nimport { mapGetters } from 'vuex'\r\nconst form = {\r\n  Id: '',\r\n  Is_Auto_Split: undefined,\r\n  Doc_Catelog: '',\r\n  Doc_Type: '',\r\n  Project_Name: '',\r\n  Project_Id: '',\r\n  Sys_Project_Id: '',\r\n  Area_Name: '',\r\n  Area_Id: '',\r\n  Type_Name: '',\r\n  Doc_Title: '',\r\n  Doc_Content: '',\r\n  IsChanged: false,\r\n  Is_Load: false,\r\n  Doc_File: '',\r\n  ishistory: true,\r\n  Is_Skip_Production: false,\r\n  ProfessionalCode: '',\r\n  Type: 0,\r\n  Bom_Level: '-1',\r\n  Template_Type: 2 // 1：动态模板，2：固定模板\r\n}\r\nexport default {\r\n  components: { OSSUpload },\r\n  props: {\r\n    typeEntity: {\r\n      type: Object,\r\n      default: () => {}\r\n    },\r\n    isAutoSplit: {\r\n      type: [Boolean, undefined],\r\n      default: undefined\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters('tenant', ['isVersionFour'])\r\n  },\r\n  data() {\r\n    return {\r\n      isDynamicTemplate: false,\r\n      btnLoading: false,\r\n      type: '',\r\n      areaType: 2,\r\n      allowFile: 'image/*,video/*,.txt,.pdf,.xlsx,.xls,.docx,.doc,.zip,.rar,.dwg,.nwd,.rvt,.ifc,.bzip,.bzip2',\r\n      fileList: [],\r\n      dialogVisible: false,\r\n      title: '上传文件',\r\n      loading: false,\r\n      form: { ...form },\r\n      attachments: [],\r\n      rules: {\r\n        Doc_Title: [\r\n          { required: true, message: '请输入标题', trigger: 'blur' }\r\n        ]\r\n        // Is_Auto_Split: [\r\n        //   { required: true, message: '请选择', trigger: 'change' }\r\n        // ]\r\n      },\r\n      fileType: '',\r\n      curFile: '',\r\n      bimvizId: '',\r\n      isDeep: false,\r\n      projectId: '',\r\n      isSHQD: '',\r\n      command: 'cover'\r\n    }\r\n  },\r\n  watch: {\r\n    isAutoSplit(newValue, oldValue) {\r\n      this.$set(this.form, 'Is_Auto_Split', newValue)\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$set(this.form, 'Is_Auto_Split', this.isAutoSplit)\r\n  },\r\n  created() {\r\n    this.fileType = this.$route.name\r\n  },\r\n\r\n  methods: {\r\n    onExceed() {\r\n      this.$message.error('只能上传一个文件')\r\n    },\r\n    getTemplate() {\r\n      console.log(this.form.Type, 'this.form.Type')\r\n      console.log(this.form.Template_Type, 'form.Template_Type')\r\n      const query = { ProfessionalCode: this.form.ProfessionalCode, Type: this.form.Type }\r\n      // if (this.form.Type === 1) {\r\n      query.Template_Type = this.form.Template_Type\r\n      // }\r\n      console.log(query, 'query=======')\r\n      ThreeBomImportTemplate({ }).then(res => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        // this.downFile(res.Data)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 通过文件下载url拿到对应的blob对象\r\n    getBlob(url) {\r\n      return new Promise(resolve => {\r\n        const xhr = new XMLHttpRequest()\r\n        xhr.open('GET', url, true)\r\n        xhr.responseType = 'blob'\r\n        xhr.onload = () => {\r\n          if (xhr.status === 200) {\r\n            resolve(xhr.response)\r\n          }\r\n        }\r\n\r\n        xhr.send()\r\n        console.log(xhr)\r\n      })\r\n    },\r\n    // 下载文件 　　js模拟点击a标签进行下载\r\n    saveAs(blob, filename) {\r\n      var link = document.createElement('a')\r\n      link.href = window.URL.createObjectURL(blob)\r\n      link.download = filename\r\n      link.click()\r\n    },\r\n    // 文件下载\r\n    downFile(fileUrl) {\r\n      this.getBlob(fileUrl).then(blob => {\r\n        this.saveAs(blob, '信用权证使用导入模板件名.xlsx')\r\n      })\r\n    },\r\n\r\n    handleChange(file, fileList) {\r\n      this.fileList = fileList.slice(-1)\r\n      if (fileList.length > 1) {\r\n        this.attachments.splice(-1)\r\n        this.form.Doc_File = ''\r\n        this.form.Doc_Content = ''\r\n        this.form.Doc_Title = ''\r\n      }\r\n    },\r\n    beforeUpload(file) {\r\n      this.curFile = file\r\n      console.log('beforeFile', file)\r\n      this.loading = true\r\n      this.btnLoading = true\r\n    },\r\n    beforeRemove(file) {\r\n      return this.$confirm(`确定移除 ${file.name}？`)\r\n    },\r\n    handleRemove(file, fileList) {\r\n      let i = 0\r\n      this.fileList.filter((item, index) => {\r\n        if (item.name === file.name) {\r\n          i = index\r\n        }\r\n      })\r\n      this.fileList.splice(i, 1)\r\n      this.attachments.splice(i, 1)\r\n      this.form.Doc_File = this.form.Doc_File.replace(file.name, '')\r\n      this.form.Doc_Content = this.form.Doc_File.replace(file.name, '')\r\n      this.form.Doc_Title = this.form.Doc_File.replace(file.name, '')\r\n      console.log('fileList', fileList)\r\n      this.loading = !fileList.every((item) => item.status === 'success')\r\n      setTimeout(() => {\r\n        this.btnLoading = !fileList.every((item) => item.status === 'success')\r\n      }, 1000)\r\n    },\r\n    uploadError(err, file, fileList) {\r\n      this.$message.error(`${file.name}上传失败`)\r\n    },\r\n    uploadSuccess(response, file, fileList) {\r\n      console.log('response', response)\r\n      console.log('uploadSuccess', file)\r\n      console.log('uploadSuccessList', fileList)\r\n      this.fileList = fileList\r\n      this.attachments.push(\r\n        {\r\n          File_Url: response.Data.split('*')[0],\r\n          File_Size: response.Data.split('*')[1],\r\n          File_Type: response.Data.split('*')[2],\r\n          File_Name: response.Data.split('*')[3]\r\n        }\r\n      )\r\n      const title = this.form.Doc_Title + (this.form.Doc_Title ? ',' : '') + response.Data.split('*')[3]\r\n      this.form.Doc_Title = title.substring(0, title.lastIndexOf('.'))\r\n      this.form.Doc_Content = this.form.Doc_Title\r\n      this.form.Doc_File = this.form.Doc_File + (this.form.Doc_File ? ',' : '') + response.Data.split('*')[3]\r\n      this.loading = !fileList.every((item) => item.status === 'success')\r\n      setTimeout(() => {\r\n        this.btnLoading = !fileList.every((item) => item.status === 'success')\r\n      }, 1000)\r\n    },\r\n    // isDeep是否是从构件管理打开的(深化)\r\n    handleOpen(type, row, bimvizId, isDeep = false, projectId, importType, productionConfirm, command = 'cover', customParams) {\r\n      this.projectId = projectId\r\n      this.isDeep = isDeep\r\n      this.form = Object.assign(this.form, form)\r\n\r\n      this.form.Type_Name = row.name\r\n      this.form.Doc_Type = row.Id\r\n      this.form.Doc_Catelog = row.Catalog_Code\r\n      this.isSHQD = row.isSHQD\r\n      this.form.ProfessionalCode = row.Code\r\n      this.dialogVisible = true\r\n      this.type = type\r\n      this.bimvizId = bimvizId\r\n      this.form.Type = importType\r\n      // this.form.Is_Skip_Production = (productionConfirm === '' ? false : productionConfirm)\r\n      this.command = command\r\n      console.log(command, 'command========')\r\n      this.form.Project_Name = customParams.Project_Name\r\n      this.form.Sys_Project_Id = customParams.Sys_Project_Id\r\n      this.form.Area_Name = customParams.Area_Name\r\n      this.form.Area_Id = customParams.Area_Id\r\n      this.isDynamicTemplate = false\r\n      this.Template_Type = 2\r\n\r\n      if (this.type === 'add') {\r\n        this.fileList = []\r\n        // this.title = '新增文件'\r\n        this.form.Id = ''\r\n        // this.$delete(this.form, \"Id\");\r\n      }\r\n      // importType  1:增量导入，2：覆盖导入 3：部分覆盖\r\n      if (this.command === 'cover') {\r\n        this.title = '覆盖文件'\r\n        this.form.ImportType = 2\r\n      } else if (this.command === 'add') {\r\n        this.title = '新增文件'\r\n        this.form.ImportType = 1\r\n      } else if (this.command === 'halfcover') {\r\n        this.title = '部分覆盖导入'\r\n        this.form.ImportType = 3\r\n      }\r\n      this.$set(this.form, 'Is_Auto_Split', this.isAutoSplit)\r\n    },\r\n    handleClose() {\r\n      try {\r\n        this.attachments = []\r\n        this.$refs['form'].resetFields()\r\n        this.btnLoading = false\r\n        this.loading = false\r\n        this.fileList = []\r\n        this.dialogVisible = false\r\n      } catch (e) {\r\n\r\n      }\r\n    },\r\n    handleSubmit(IsOk = false) {\r\n      this.$refs['form'].validate(async(valid) => {\r\n        if (valid) {\r\n          this.loading = true\r\n          this.btnLoading = true\r\n          // this.$delete(this.form, 'Type_Name')\r\n          this.updateInfo(IsOk)\r\n        } else {\r\n          this.$message({\r\n            message: '请将表单填写完整',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n\r\n    async updateInfo(IsOk) {\r\n      // Type 1零构件 0 构件\r\n      const form = { ...this.form, IsOk }\r\n      // if (form.Type === 0) {\r\n      //   delete form['Template_Type']\r\n      // }\r\n      console.log(form, 'form=========')\r\n      // if (form.Is_Auto_Split && form.Type === 1) {\r\n      //   this.getSplitInfo().then(() => {\r\n      //     this.updatePartAggregateId()\r\n      //   })\r\n      //   return\r\n      // }\r\n      this.submitAdd(form)\r\n      // if (this.command === 'cover') {\r\n      //   console.log(this.command, 'command========cover')\r\n      //   this.submitCoverAdd(form)\r\n      // } else if (this.command === 'add') {\r\n      //   console.log(this.command, 'command========add')\r\n      //   this.submitAdd(form)\r\n      // } else if (this.command === 'halfcover') {\r\n      //   console.log(this.command, 'command========add')\r\n      //   this.submitCoverAdd(form)\r\n      // }\r\n    },\r\n\r\n    async submitCoverAdd(form) {\r\n      try {\r\n        const res = await AppendImportDeepFiles({ ...form, AttachmentList: this.attachments })\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '保存成功',\r\n            type: 'success'\r\n          })\r\n          await this.updatePartAggregateId() // 确保在保存成功后执行\r\n          this.$emit('getData', this.form.Doc_Type)\r\n          this.$emit('getProjectAreaData')\r\n          this.handleClose()\r\n        } else {\r\n          res.Data && window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n          this.$message.error(res.Message)\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('保存失败')\r\n      } finally {\r\n        this.loading = false\r\n        this.btnLoading = false\r\n      }\r\n    },\r\n\r\n    async submitAdd(form) {\r\n      try {\r\n        const _form = { ...form }\r\n        if (this.areaType === 2) {\r\n          _form.Area_Id = undefined\r\n          _form.Area_Name = undefined\r\n        }\r\n        const res = await AppendImportDeepFiles({ ..._form, AttachmentList: this.attachments })\r\n\r\n        if (res.IsSucceed) {\r\n          if (!res.Data) {\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            await this.updatePartAggregateId() // 确保在保存成功后执行\r\n            this.$emit('getData', this.form.Doc_Type)\r\n            this.$emit('getProjectAreaData')\r\n            this.handleClose()\r\n          } else {\r\n            this.$confirm(res.Data, '提示', {\r\n              confirmButtonText: '确定',\r\n              cancelButtonText: '取消',\r\n              type: 'warning'\r\n            }).then(() => {\r\n              this.handleSubmit(true)\r\n            }).catch(() => {\r\n              this.$message({\r\n                type: 'info',\r\n                message: '已取消'\r\n              })\r\n            })\r\n          }\r\n        } else {\r\n          res.Data && window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n          this.$message.error(res.Message)\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('保存失败')\r\n      } finally {\r\n        this.loading = false\r\n        this.btnLoading = false\r\n      }\r\n    },\r\n    getSplitInfo() {\r\n      const { ProfessionalCode,\r\n        Type,\r\n        Is_Skip_Production,\r\n        Sys_Project_Id,\r\n        Area_Id\r\n      } = this.form\r\n      const obj = {\r\n        'ProfessionalCode': ProfessionalCode,\r\n        'Type': Type,\r\n        'Is_Skip_Production': Is_Skip_Production,\r\n        'Sys_Project_Id': Sys_Project_Id,\r\n        'Area_Id': Area_Id,\r\n        'AttachmentList': this.attachments,\r\n        'Is_Auto_Split': true\r\n      }\r\n      GenerateDeepenFileFromDirect(obj).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.open(res.Data)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    async  updatePartAggregateId() {\r\n      console.log('更新成功=========')\r\n      await UpdatePartAggregateId({ AreaId: this.form.Area_Id }).then((res) => {\r\n        if (!res.IsSucceed) {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    open(url) {\r\n      const h = this.$createElement\r\n      let fileName = ''\r\n      const match = url.match(/\\/([^/]+\\.xls)$/)\r\n      if (match) {\r\n        fileName = match[1]\r\n      }\r\n      const form = { ...this.form }\r\n      // if (form.Type === 0) {\r\n      //   delete form['Template_Type']\r\n      // }\r\n      this.$msgbox({\r\n        title: '提示',\r\n        message: h('div', null, [\r\n          h('div', null, '清单已拆分完成, 是否确定导入?'),\r\n          h('a', {\r\n            attrs: {\r\n              href: combineURL(this.$baseUrl, url),\r\n              target: '_blank',\r\n              style: 'color: #298DFF'\r\n            }\r\n          }, fileName)\r\n        ]),\r\n        showCancelButton: true,\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        beforeClose: async(action, instance, done) => {\r\n          if (action === 'confirm') {\r\n            instance.confirmButtonLoading = true\r\n            instance.confirmButtonText = '提交...'\r\n            // if (this.command === 'cover') {\r\n            //   await this.submitCoverAdd(form)\r\n            // } else if (this.command === 'add') {\r\n            //   await this.submitAdd(form)\r\n            // }\r\n            await this.submitAdd(form)\r\n            done()\r\n            setTimeout(() => {\r\n              instance.confirmButtonLoading = false\r\n            }, 300)\r\n          } else {\r\n            this.loading = false\r\n            this.btnLoading = false\r\n            done()\r\n          }\r\n        }\r\n      }).then(action => {\r\n\r\n      })\r\n    },\r\n    radioChange(val) {\r\n      if (val === 1) {\r\n        this.isDynamicTemplate = true\r\n        this.form.Is_Auto_Split = undefined\r\n      } else {\r\n        this.isDynamicTemplate = false\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .cs-alert {\r\n    position: relative;\r\n    height: 38px;\r\n    line-height: 38px;\r\n    color: #F5C15A;\r\n    border-radius: 4px;\r\n    margin-bottom: 30px;\r\n\r\n    &-info {\r\n      color: #298DFF;\r\n    }\r\n\r\n    .el-icon-warning-outline {\r\n      margin-left: 16px;\r\n    }\r\n\r\n    &:after {\r\n      content: '';\r\n      top: 0;\r\n      left: 0;\r\n      position: absolute;\r\n      width: 100%;\r\n      height: 100%;\r\n      background: #F5C15A;\r\n      opacity: 0.12;\r\n      pointer-events: none;\r\n    }\r\n  }\r\n</style>\r\n"]}]}
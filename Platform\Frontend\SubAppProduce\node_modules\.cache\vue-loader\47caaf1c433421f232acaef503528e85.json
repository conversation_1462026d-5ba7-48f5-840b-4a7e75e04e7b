{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\production-execution\\new-report\\home.vue?vue&type=style&index=0&id=71c3a17e&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\production-execution\\new-report\\home.vue", "mtime": 1758266753126}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KQGltcG9ydCAifkAvc3R5bGVzL21peGluLnNjc3MiOw0KLy8gQGltcG9ydCAifkAvc3R5bGVzL3RhYnMuc2NzcyI7DQoqIHsNCiAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCn0NCi5hcHAtd3JhcHBlciB7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDEwMCU7DQogIGRpc3BsYXk6IGZsZXg7DQogIG92ZXJmbG93OiBoaWRkZW47DQoNCiAgLmNzLWxlZnQgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICBtYXJnaW4tcmlnaHQ6IDIwcHg7DQoNCiAgICAuaW5uZXItd3JhcHBlciB7DQogICAgICBmbGV4OiAxOw0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICBwYWRkaW5nOiAxNnB4IDE2cHg7DQogICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgICAgLmNzLXNlYXJjaCB7DQogICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTJlNGU5Ow0KICAgICAgICBwYWRkaW5nLWJvdHRvbTogMTdweDsNCiAgICAgICAgLnRlYW0tc2VsZWN0IHsNCiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxMnB4Ow0KICAgICAgICAgIC5lbC1zZWxlY3Qgew0KICAgICAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgOjp2LWRlZXAgew0KICAgICAgICAgICAgLmVsLWlucHV0X19pbm5lciB7DQogICAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICMyOThkZmY7DQogICAgICAgICAgICAgIGNvbG9yOiAjMjk4ZGZmOw0KICAgICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIC5lbC1pbnB1dF9fcHJlZml4IHsNCiAgICAgICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQoNCiAgICAgICAgICAgICAgaW1nIHsNCiAgICAgICAgICAgICAgICB3aWR0aDogMTZweDsNCiAgICAgICAgICAgICAgICBoZWlnaHQ6IDE2cHg7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgLmVsLXNlbGVjdF9fY2FyZXQgew0KICAgICAgICAgICAgICBjb2xvcjogIzI5OGRmZjsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIC5jcy10cmVlIHsNCiAgICAgICAgbWFyZ2luLXRvcDogMjJweDsNCiAgICAgICAgZmxleDogMTsNCiAgICAgICAgaGVpZ2h0OiAwOw0KDQogICAgICAgIC5jcy1zY3JvbGwgew0KICAgICAgICAgIG92ZXJmbG93LXk6IGF1dG87DQogICAgICAgICAgQGluY2x1ZGUgc2Nyb2xsQmFyOw0KICAgICAgICB9DQoNCiAgICAgICAgLmVsLXRyZWUgew0KICAgICAgICAgIGhlaWdodDogMTAwJTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC5jcy1yaWdodCB7DQogICAgZmxleDogMTsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgb3ZlcmZsb3c6IGF1dG87DQogICAgcGFkZGluZzogMTZweCAxNnB4Ow0KDQogICAgLmNzLXRvcC13YXBwZXIgew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICAgIC8vIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAuYnRuIHsNCiAgICAgICAgd2lkdGg6IDIwcHg7DQogICAgICAgIGhlaWdodDogMjBweDsNCiAgICAgICAgYm9yZGVyLXJhZGl1czogMTBweDsNCiAgICAgICAgYmFja2dyb3VuZDogI2NmY2ZjZjsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgIGNvbG9yOiAjZmZmOw0KICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgICAgIG1hcmdpbi10b3A6IGNhbGMoKDk2cHggLyAyKSAtIDEwcHgpOw0KICAgICAgfQ0KICAgICAgLmJ0bi5kaXNhYmxlZCB7DQogICAgICAgIG9wYWNpdHk6IDAuNTsNCiAgICAgICAgLy8gcG9pbnRlci1ldmVudHM6IG5vbmU7DQogICAgICAgIGN1cnNvcjogbm90LWFsbG93ZWQgIWltcG9ydGFudDsNCiAgICAgIH0NCg0KICAgICAgLm1pZGRsZS13YXBwZXI6Oi13ZWJraXQtc2Nyb2xsYmFyIHsNCiAgICAgICAgaGVpZ2h0OiAwOyAvKiDlsIbmu5rliqjmnaHnmoTlrr3luqborr7kuLowICovDQogICAgICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50OyAvKiDkvb/mu5rliqjmnaHlrozlhajpgI/mmI4gKi8NCiAgICAgIH0NCg0KICAgICAgLm1pZGRsZS13YXBwZXIgew0KICAgICAgICBmbGV4OiAxOw0KICAgICAgICB3aWR0aDogMDsNCiAgICAgICAgb3ZlcmZsb3cteDogYXV0bzsNCiAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAgICAgICBoZWlnaHQ6IGNhbGMoOTZweCArIDEwcHgpOw0KDQogICAgICAgIC5ib3gtd2FwcGVyIHsNCiAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICBmbGV4LXdyYXA6IG5vLXdyYXA7DQogICAgICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgICAgIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjNzIGVhc2U7DQoNCiAgICAgICAgICAuaXRlbSB7DQogICAgICAgICAgICB3aWR0aDogMjc1cHg7DQogICAgICAgICAgICBoZWlnaHQ6IGNhbGMoMTAwJSAtIDEwcHgpOw0KICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4IDRweCA0cHggNHB4Ow0KICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2UyZTRlOTsNCiAgICAgICAgICAgIG1hcmdpbi1sZWZ0OiAxMnB4Ow0KICAgICAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgICAgICAgICAgLmNvbnRlbnQgew0KICAgICAgICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgICAgICBwYWRkaW5nOiAxMnB4IDEycHg7DQogICAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICAgICAgICAgIC8vIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgICAgIC8vIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICAgICAgICAgICAgLy8gYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KCA5MWRlZywgIzI5OERGRiAwJSwgIzU3QzJGRiAxMDAlKTsNCiAgICAgICAgICAgICAgLmRldGFpbCB7DQogICAgICAgICAgICAgICAgZmxleDogMTsNCiAgICAgICAgICAgICAgICBoZWlnaHQ6IDA7DQogICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAudGl0bGUgew0KICAgICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDhweDsNCiAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgLm5hbWUgew0KICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAucHJlY2VudCB7DQogICAgICAgICAgICAgICAgaGVpZ2h0OiAxN3B4Ow0KICAgICAgICAgICAgICAgIHBhZGRpbmc6IDAgNHB4Ow0KICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICMwMGNmYWE7DQogICAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogOXB4IDlweCA5cHggOXB4Ow0KICAgICAgICAgICAgICAgIGNvbG9yOiAjZmZmZmZmOw0KICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgICAgICAgICBtYXJnaW4tbGVmdDogOHB4Ow0KICAgICAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxN3B4Ow0KICAgICAgICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAuaW5mbyB7DQogICAgICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgICAgICAgIGNvbG9yOiAjOTk5OTk5Ow0KICAgICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDJweDsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC5yaWdodCB7DQogICAgICAgICAgICAgICAgLy8gd2lkdGg6IDgwcHg7DQogICAgICAgICAgICAgICAgcGFkZGluZzogNXB4IDhweDsNCiAgICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiA0cHggNHB4IDRweCA0cHg7DQogICAgICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOw0KDQogICAgICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZTlmM2ZmOw0KICAgICAgICAgICAgICAgIGNvbG9yOiAjMjk4ZGZmOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIC5mbGFnIHsNCiAgICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgICAgICAgICB0b3A6IDA7DQogICAgICAgICAgICAgIHJpZ2h0OiAwOw0KICAgICAgICAgICAgICB3aWR0aDogMzVweDsNCiAgICAgICAgICAgICAgaGVpZ2h0OiAzNXB4Ow0KICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiB1cmwoIn5AL2Fzc2V0cy9QUk8vZmxhZy5wbmciKSBuby1yZXBlYXQ7DQogICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgICAgICAgY29sb3I6ICNmZmZmZmY7DQogICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7DQogICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0Ow0KICAgICAgICAgICAgICBwYWRkaW5nLXJpZ2h0OiA0cHg7DQogICAgICAgICAgICAgIHBhZGRpbmctdG9wOiA2cHg7DQogICAgICAgICAgICB9DQogICAgICAgICAgICBpIHsNCiAgICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgICAgICAgICBib3R0b206IC0xMHB4Ow0KICAgICAgICAgICAgICBsZWZ0OiBjYWxjKCgxMDAlIC0gMTZweCkgLyAyKTsNCiAgICAgICAgICAgICAgY29sb3I6ICMyOThkZmY7DQogICAgICAgICAgICB9DQogICAgICAgICAgICAuZmxhZzIgew0KICAgICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgICAgICAgICAgIGJvdHRvbTogMDsNCiAgICAgICAgICAgICAgcmlnaHQ6IDA7DQogICAgICAgICAgICAgIHdpZHRoOiAwOw0KICAgICAgICAgICAgICBoZWlnaHQ6IDA7DQogICAgICAgICAgICAgIGJvcmRlci1sZWZ0OiAzNXB4IHNvbGlkIHRyYW5zcGFyZW50Ow0KICAgICAgICAgICAgICBib3JkZXItdG9wOiAzNXB4IHNvbGlkIHRyYW5zcGFyZW50Ow0KICAgICAgICAgICAgICBib3JkZXItYm90dG9tOiAzNXB4IHNvbGlkICNlNmEyM2M7DQogICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgICAgICAgY29sb3I6ICNmZmZmZmY7DQogICAgICAgICAgICAgIC5mbGFnMi10eHQgew0KICAgICAgICAgICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICAgICAgICAgICAgICByaWdodDogM3B4Ow0KICAgICAgICAgICAgICAgIGJvdHRvbTogLTMzcHg7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgICAgLmFjdGl2ZSB7DQogICAgICAgICAgICAuY29udGVudCB7DQogICAgICAgICAgICAgIGNvbG9yOiAjZmZmZmZmOw0KICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTFkZWcsICMyOThkZmYgMCUsICM1N2MyZmYgMTAwJSk7DQogICAgICAgICAgICAgIC5pbmZvIHsNCiAgICAgICAgICAgICAgICBjb2xvcjogI2ZmZmZmZjsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgLnJpZ2h0IHsNCiAgICAgICAgICAgICAgYmFja2dyb3VuZDogI2ZmZmZmZjsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgICAgLml0ZW06bGFzdC1jaGlsZCB7DQogICAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDEycHg7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KDQogICAgLmNzLW1pZGRsZS13YXBwZXIgew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICAgIG1hcmdpbjogMTJweCAwOw0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgIC5lbC1mb3JtLWl0ZW0gew0KICAgICAgICBtYXJnaW4tYm90dG9tOiAwOw0KICAgICAgfQ0KICAgIH0NCg0KICAgIC5jcy1ib3R0b20td2FwcGVyIHsNCiAgICAgIGZsZXg6IDE7DQogICAgICBoZWlnaHQ6IDA7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICAgIC50Yi14IHsNCiAgICAgICAgZmxleDogMTsNCiAgICAgICAgaGVpZ2h0OiAwOw0KICAgICAgfQ0KDQogICAgICAucGFnaW5hdGlvbi1jb250YWluZXIgew0KICAgICAgICB0ZXh0LWFsaWduOiByaWdodDsNCiAgICAgICAgcGFkZGluZzogMTZweDsNCiAgICAgICAgbWFyZ2luOiAwOw0KICAgICAgfQ0KDQogICAgICAuZGF0YS1pbmZvIHsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0KDQoucGxtLWN1c3RvbS1kaWFsb2cgew0KICA6OnYtZGVlcCB7DQogICAgLmVsLWRpYWxvZ19fYm9keSB7DQogICAgICBoZWlnaHQ6IDcwdmg7DQogICAgICBvdmVyZmxvdzogYXV0bzsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIH0NCiAgfQ0KfQ0KDQouZm91ckdyZWVuIHsNCiAgY29sb3I6ICMwMGMzNjE7DQogIGZvbnQtc3R5bGU6IG5vcm1hbDsNCn0NCg0KLmZvdXJPcmFuZ2Ugew0KICBjb2xvcjogI2ZmOTQwMDsNCiAgZm9udC1zdHlsZTogbm9ybWFsOw0KfQ0KDQouZm91clJlZCB7DQogIGNvbG9yOiAjZmYwMDAwOw0KICBmb250LXN0eWxlOiBub3JtYWw7DQp9DQoNCi5jcy1ibHVlIHsNCiAgY29sb3I6ICM1YWM4ZmE7DQp9DQouY3MtY2hlY2tib3ggew0KICBtYXJnaW4tbGVmdDogMTZweDsNCiAgbGluZS1oZWlnaHQ6IDMwcHg7DQp9DQouY3MtbWlkZGxlLXdhcHBlci1sZWZ0ew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LXdyYXA6IG5vd3JhcDsNCn0NCg=="}, {"version": 3, "sources": ["home.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAg7BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "home.vue", "sourceRoot": "src/views/PRO/production-execution/new-report", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <div\r\n      v-loading=\"pgLoading\"\r\n      element-loading-text=\"加载中\"\r\n      class=\"h100 app-wrapper\"\r\n    >\r\n      <ExpandableSection v-model=\"showExpand\" :width=\"300\" class=\"cs-left fff\">\r\n        <div class=\"inner-wrapper\">\r\n          <div class=\"cs-search\">\r\n            <el-row>\r\n              <el-col :span=\"24\" class=\"team-select\">\r\n                <el-select\r\n                  v-model=\"form.Working_Team_Id\"\r\n                  clearable\r\n                  placeholder=\"请选择\"\r\n                  @change=\"handleTeamChange\"\r\n                >\r\n                  <i slot=\"prefix\">\r\n                    <img src=\"@/assets/PRO/icon-search.png\" alt=\"\">\r\n                  </i>\r\n                  <el-option\r\n                    v-for=\"item in teamOptions\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"item.value\"\r\n                  />\r\n                </el-select>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row :span=\"24\" :gutter=\"8\">\r\n              <el-col :span=\"10\">\r\n                <el-select v-model=\"statusType\" clearable placeholder=\"请选择\">\r\n                  <el-option label=\"所有状态\" value=\"\" />\r\n                  <el-option label=\"待报工\" value=\"待报工\" />\r\n                  <el-option label=\"报工完成\" value=\"报工完成\" />\r\n                  <el-option label=\"排产未完成\" value=\"排产未完成\" />\r\n                </el-select>\r\n              </el-col>\r\n              <el-col :span=\"14\">\r\n                <el-input\r\n                  v-model.trim=\"projectName\"\r\n                  placeholder=\"搜索...\"\r\n                  size=\"small\"\r\n                  clearable\r\n                  suffix-icon=\"el-icon-search\"\r\n                />\r\n              </el-col>\r\n            </el-row>\r\n          </div>\r\n\r\n          <div class=\"cs-tree cs-scroll\">\r\n            <tree-detail\r\n              ref=\"tree\"\r\n              :default-expand-all=\"expandAll\"\r\n              icon=\"icon-folder\"\r\n              is-custom-filter\r\n              :custom-filter-fun=\"customFilterFun\"\r\n              :loading=\"treeLoading\"\r\n              :tree-data=\"treeData\"\r\n              show-status\r\n              show-detail\r\n              :filter-text=\"filterText\"\r\n              :expanded-key=\"expandedKey\"\r\n              :can-node-click=\"false\"\r\n              @handleNodeClick=\"handleNodeClick\"\r\n              @saveSortFinish=\"saveSortFinish\"\r\n            >\r\n              <template #csLabel=\"{ showStatus, data }\">\r\n                <span\r\n                  v-if=\"!data.ParentNodes\"\r\n                  class=\"cs-blue\"\r\n                >({{ data.Code }})</span>{{ data.Label }}\r\n                <template v-if=\"showStatus\">\r\n                  <i\r\n                    v-if=\"data.Data[statusKey]\"\r\n                    :class=\"[\r\n                      data.Data[statusKey] == '报工完成'\r\n                        ? 'fourGreen'\r\n                        : data.Data[statusKey] == '待报工'\r\n                          ? 'fourOrange'\r\n                          : data.Data[statusKey] == '排产未完成'\r\n                            ? 'fourRed'\r\n                            : '',\r\n                    ]\"\r\n                  >\r\n                    <span>({{ data.Data[statusKey] }})</span>\r\n                  </i>\r\n                </template>\r\n              </template>\r\n            </tree-detail>\r\n          </div>\r\n        </div>\r\n      </ExpandableSection>\r\n      <div class=\"cs-right fff\">\r\n        <div v-show=\"taskList.length > 0\" class=\"cs-top-wapper\">\r\n          <div\r\n            v-if=\"IsShowBtn\"\r\n            class=\"btn\"\r\n            :class=\"{ disabled: disableLeftBtn }\"\r\n            @click=\"clickMove('left')\"\r\n          >\r\n            <i class=\"el-icon-arrow-left\" />\r\n          </div>\r\n          <div\r\n            ref=\"middleWapper\"\r\n            class=\"middle-wapper\"\r\n            @mousedown.prevent=\"handleMouseDown\"\r\n            @mouseleave.prevent=\"handleMouseLeave\"\r\n            @mouseup.prevent=\"handleMouseUp\"\r\n            @mousemove.prevent=\"handleMouseMove\"\r\n          >\r\n            <div\r\n              ref=\"boxWapper\"\r\n              class=\"box-wapper\"\r\n              :style=\"{ transform: `translateX(${offset}px)` }\"\r\n            >\r\n              <div\r\n                v-for=\"(item, index) in taskList\"\r\n                :key=\"index\"\r\n                :ref=\"'task_' + item.Task_Code\"\r\n                :class=\"[\r\n                  'item',\r\n                  form.Task_Code === item.Task_Code ? 'active' : '',\r\n                ]\"\r\n                @click=\"handleTask(item)\"\r\n              >\r\n                <div v-if=\"item.Return_Count > 0\" class=\"flag\">退</div>\r\n                <div v-if=\"item.Is_Nest\" class=\"flag2\">\r\n                  <span class=\"flag2-txt\">套</span>\r\n                </div>\r\n                <div class=\"content\">\r\n                  <div class=\"title\">\r\n                    <span class=\"name\"> {{ item.Task_Code }}</span>\r\n                    <!--                    <span v-if=\"isCom && item.Part_Completion_Rate !== null\" class=\"precent\">\r\n                      {{ item.Part_Completion_Rate > 1 ? 100 : Math.round(item.Part_Completion_Rate * 100) }}%\r\n                    </span>-->\r\n                    <span\r\n                      v-if=\"checkShowP(item.Need_Part_Amount)\"\r\n                      class=\"precent\"\r\n                    >{{ item.percentage }}</span>\r\n                  </div>\r\n                  <div class=\"detail\">\r\n                    <div class=\"left\">\r\n                      <div class=\"info\">要求：{{ item.Demand_Date }}</div>\r\n                      <div class=\"info\">\r\n                        总量：{{ item.Total_Count }}/{{\r\n                          parseFloat((item.Total_Weight / 1000).toFixed(3))\r\n                        }}t\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div\r\n                      v-if=\"isCom && checkShowP(item.Need_Part_Amount)\"\r\n                      class=\"right\"\r\n                      @click.stop=\"viewPart(item)\"\r\n                    >\r\n                      查看零件\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <i\r\n                  v-if=\"form.Task_Code === item.Task_Code\"\r\n                  class=\"el-icon-caret-bottom\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div\r\n            v-if=\"IsShowBtn\"\r\n            class=\"btn\"\r\n            :class=\"{ disabled: disableRightBtn }\"\r\n            @click=\"clickMove('right')\"\r\n          >\r\n            <i class=\"el-icon-arrow-right\" />\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"cs-middle-wapper\">\r\n          <div class=\"cs-middle-wapper-left\">\r\n            <el-button\r\n              type=\"primary\"\r\n              :disabled=\"!multipleSelection.length || multipleSelection.some(item=>item.stopFlag)\"\r\n              @click=\"handleReport()\"\r\n            >报工</el-button>\r\n            <el-checkbox\r\n              v-if=\"!isCom\"\r\n              v-model=\"showNestPart\"\r\n              class=\"cs-checkbox\"\r\n              @change=\"showNestChange\"\r\n            >显示套料零件</el-checkbox>\r\n          </div>\r\n\r\n          <el-form ref=\"form\" :model=\"form\" label-width=\"80px\" inline>\r\n            <el-form-item v-if=\"!isCom\" label=\"排版编号\">\r\n              <el-input\r\n                v-model=\"form.Nesting_Result_Name\"\r\n                placeholder=\"请输入\"\r\n                clearable\r\n              />\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"生产状态\">\r\n              <el-select\r\n                v-model=\"form.Production_Status\"\r\n                placeholder=\"请选择生产状态\"\r\n                style=\"width: 140px\"\r\n                clearable\r\n              >\r\n                <el-option label=\"未完成\" value=\"未完成\" />\r\n                <el-option label=\"已完成\" value=\"已完成\" />\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item :label=\"isCom ? `${comName}名称` : `${partName}名称`\">\r\n              <el-input\r\n                v-model=\"form.Code_Like\"\r\n                :placeholder=\"isCom ? `请输入${comName}名称` : `请输入${partName}名称`\"\r\n                clearable\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"规格\" label-width=\"50px\">\r\n              <el-input\r\n                v-model=\"form.Spec_Like\"\r\n                style=\"width: 140px\"\r\n                placeholder=\"请输入规格\"\r\n                clearable\r\n              />\r\n            </el-form-item>\r\n\r\n            <el-form-item>\r\n              <el-button type=\"primary\" @click=\"refresh\">搜索</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n\r\n        <div class=\"cs-bottom-wapper\">\r\n          <div class=\"fff tb-x\">\r\n            <vxe-table\r\n              :empty-render=\"{ name: 'NotData' }\"\r\n              show-header-overflow\r\n              :loading=\"tbLoading\"\r\n              element-loading-spinner=\"el-icon-loading\"\r\n              element-loading-text=\"拼命加载中\"\r\n              empty-text=\"暂无数据\"\r\n              class=\"cs-vxe-table\"\r\n              height=\"100%\"\r\n              align=\"left\"\r\n              stripe\r\n              :data=\"tbData\"\r\n              resizable\r\n              :tooltip-config=\"{ enterable: true }\"\r\n              :checkbox-config=\"{\r\n                checkField: 'checked',\r\n                trigger: 'row',\r\n                checkMethod: checCheckboxkMethod3,\r\n              }\"\r\n              @checkbox-all=\"multiSelectedChange\"\r\n              @checkbox-change=\"multiSelectedChange\"\r\n            >\r\n              <vxe-column fixed=\"left\" type=\"checkbox\" />\r\n              <template v-for=\"item in columns\">\r\n                <vxe-column\r\n                  :key=\"item.Code\"\r\n                  :min-width=\"item.Width\"\r\n                  width=\"auto\"\r\n                  :fixed=\"\r\n                    ['Comp_Code', 'Part_Code'].includes(item.Code) ? 'left' : ''\r\n                  \"\r\n                  show-overflow=\"tooltip\"\r\n                  sortable\r\n                  :align=\"item.Align\"\r\n                  :field=\"item.Code\"\r\n                  :title=\"item.Display_Name\"\r\n                >\r\n                  <template\r\n                    v-if=\"\r\n                      item.Code === 'Comp_Code' || item.Code === 'Part_Code'\r\n                    \"\r\n                    #default=\"{ row }\"\r\n                  >\r\n                    <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                    <el-tag\r\n                      v-if=\"row.Is_Change\"\r\n                      style=\"margin-right: 6px\"\r\n                      type=\"danger\"\r\n                      class=\"cs-tag\"\r\n                    >变</el-tag>\r\n                    <el-link\r\n                      v-if=\"row.DwgCount > 0\"\r\n                      type=\"primary\"\r\n                      @click.stop=\"handleDwg(row)\"\r\n                    >\r\n                      {{ row[item.Code] | displayValue }}</el-link>\r\n                    <span v-else> {{ row[item.Code] | displayValue }}</span>\r\n                  </template>\r\n                  <template\r\n                    v-else-if=\"item.Code === 'Prepare_Count'\"\r\n                    #default=\"{ row }\"\r\n                  >\r\n                    <el-link\r\n                      v-if=\"typeof row.Prepare_Count === 'number'\"\r\n                      type=\"primary\"\r\n                      @click.stop=\"handleQitao(row)\"\r\n                    >\r\n                      {{ row[item.Code] || 0 }}</el-link>\r\n                    <span v-else> {{ row[item.Code] | displayValue }}</span>\r\n                  </template>\r\n                  <template\r\n                    v-else-if=\"item.Code === 'Production_Status'\"\r\n                    #default=\"{ row }\"\r\n                  >\r\n                    <span\r\n                      :class=\"\r\n                        row.Production_Status === '未完成'\r\n                          ? 'fourRed'\r\n                          : row.Production_Status === '已完成'\r\n                            ? 'fourGreen'\r\n                            : ''\r\n                      \"\r\n                    >{{ row.Production_Status }}</span>\r\n                  </template>\r\n                  <template\r\n                    v-else-if=\"item.Code === 'Working_Process_Name'\"\r\n                    #default=\"{ row }\"\r\n                  >\r\n                    <el-link type=\"primary\" @click=\"getData(row)\">\r\n                      {{ row[item.Code] }}</el-link>\r\n                  </template>\r\n                  <template v-else #default=\"{ row }\">\r\n                    <span> {{ row[item.Code] | displayValue }}</span>\r\n                  </template>\r\n                </vxe-column>\r\n              </template></vxe-table>\r\n          </div>\r\n          <div class=\"data-info\">\r\n            <el-tag\r\n              size=\"medium\"\r\n              class=\"info-x\"\r\n            >已选 {{ multipleSelection.length }} 条数据\r\n            </el-tag>\r\n            <Pagination\r\n              :total=\"total\"\r\n              :page-sizes=\"tablePageSize\"\r\n              :page.sync=\"queryInfo.Page\"\r\n              :limit.sync=\"queryInfo.PageSize\"\r\n              @pagination=\"pageChange\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      ref=\"content\"\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"width\"\r\n      class=\"plm-custom-dialog\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :is-nest=\"isNest\"\r\n        :tid=\"tid\"\r\n        :comp-code=\"compCode\"\r\n        @close=\"handleClose\"\r\n        @refresh=\"refresh\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <ProcessDialog ref=\"process\" :show-search=\"false\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport Pagination from '@/components/Pagination'\r\nimport PartDetail from './components/PartDetail'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport ComReport from './components/ComReport'\r\nimport PartReport from './components/PartReport.vue'\r\nimport TreeDetail from '@/components/TreeDetail/index.vue'\r\nimport Qitao from './components/Qitao'\r\nimport { GetProjectAreaTreeList } from '@/api/PRO/project'\r\nimport { GetTeamListByUser } from '@/api/PRO/technology-lib'\r\nimport {\r\n  GetCompTaskPageList,\r\n  GetCompTaskPartCompletionStock,\r\n  GetDwg\r\n} from '@/api/PRO/production-task'\r\nimport {\r\n  GetCompTaskList,\r\n  GetSimplifiedPartTaskList,\r\n  GetSimplifiedPartTaskPageList\r\n} from '@/api/PRO/production-report-new'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nimport { parseOssUrl } from '@/utils/file'\r\nimport { mapGetters } from 'vuex'\r\nimport scroll from './mixin/scroll'\r\nimport { timeFormat } from '@/utils/timeFormat'\r\nimport { GetProcessingProgressTask } from '@/api/PRO/processingprogress'\r\nimport ProcessDialog from '@/views/PRO/basic-information/production-scheduling/process/processdialog.vue'\r\nimport numeral from 'numeral'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\n\r\nconst SPLIT_SYMBOL = '$_$'\r\nexport default {\r\n  components: {\r\n    ExpandableSection,\r\n    Pagination,\r\n    ComReport,\r\n    TreeDetail,\r\n    Qitao,\r\n    PartDetail,\r\n    ProcessDialog,\r\n    PartReport\r\n  },\r\n  mixins: [getTbInfo, scroll],\r\n  inject: ['pageType'],\r\n  data() {\r\n    return {\r\n      tid: '',\r\n      comName: '',\r\n      compCode: '',\r\n      partName: '',\r\n      projectName: '',\r\n      statusType: '待报工',\r\n      expandedKey: '',\r\n      treeLoading: false,\r\n      treeData: [],\r\n      showExpand: true,\r\n      teamOptions: [],\r\n      value: '',\r\n      offset: 0,\r\n      itemWidth: 275 + 12, // item width + margin\r\n      taskList: [],\r\n      form: {\r\n        Nesting_Result_Name: '',\r\n        Working_Team_Id: '',\r\n        Code_Like: '',\r\n        Task_Code: '',\r\n        Sys_Project_Id: '',\r\n        Area_Id: '',\r\n        InstallUnit_Id: '',\r\n        Production_Status: '',\r\n        Spec_Like: ''\r\n      },\r\n      tbLoading: false,\r\n      showNestPart: false,\r\n      columns: [],\r\n      tbData: [],\r\n      tbConfig: {},\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: tablePageSize[0]\r\n      },\r\n      total: 0,\r\n      tablePageSize: tablePageSize,\r\n      dialogVisible: false,\r\n      currentComponent: '',\r\n      dialogTitle: '',\r\n      width: '80%',\r\n      IsShowBtn: false,\r\n      multipleSelection: [],\r\n      pgLoading: false,\r\n      isNest: false,\r\n      disableLeftBtn: true,\r\n      disableRightBtn: false\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    },\r\n    statusKey() {\r\n      return this.pageType === 'com'\r\n        ? 'Comp_Produce_Status'\r\n        : 'Part_Produce_Status'\r\n    },\r\n    filterText() {\r\n      return this.projectName + SPLIT_SYMBOL + this.statusType\r\n    },\r\n    expandAll() {\r\n      return process.env.NODE_ENV !== 'development'\r\n    },\r\n    ...mapGetters('factoryInfo', [\r\n      'Is_Skip_Warehousing_Operation',\r\n      'Nested_Must_Before_Processing',\r\n      'Is_Part_Prepare'\r\n    ])\r\n  },\r\n\r\n  async mounted() {\r\n    const { list, partName, comName } = await GetBOMInfo()\r\n    this.bomList = list || []\r\n    this.partName = partName\r\n    this.comName = comName\r\n    this.pgLoading = true\r\n    window.addEventListener('resize', this.checkResize)\r\n    this.checkResize() // initial check\r\n    this.getTableConfig(\r\n      this.isCom ? 'PROProductionComNewConfig' : 'PROProductionPartNewConfig'\r\n    )\r\n    this.getFactoryInfo()\r\n    await this.getProcessTeam()\r\n    await this.fetchTreeData()\r\n    // await this.getTaskList()\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.checkResize)\r\n  },\r\n  methods: {\r\n    getData(row) {\r\n      this.$nextTick((_) => {\r\n        this.$refs['process'].handleOpen({\r\n          Code: row.Task_Code,\r\n          Type: this.isCom ? '0' : '1',\r\n          Task_Id: row.Task_Id,\r\n          ProcessName: row.Working_Process_Name,\r\n          TeamName: row.Working_Team_Name,\r\n          Schduling_Code: row?.Task_Code.split('-')[0],\r\n          TeamId: this.form.Working_Team_Id\r\n        })\r\n      })\r\n    },\r\n    async getFactoryInfo() {\r\n      await this.$store.dispatch('factoryInfo/getWorkshop')\r\n      console.log(this.Is_Part_Prepare, '====')\r\n    },\r\n    async fetchTreeData() {\r\n      console.log('fetchTreeData')\r\n      this.treeLoading = true\r\n      await GetProjectAreaTreeList({\r\n        MenuId: this.$route.meta.Id,\r\n        projectName: this.projectName,\r\n        type: this.isCom ? 3 : 4\r\n      }).then((res) => {\r\n        if (res.Data.length === 0) {\r\n          this.treeLoading = false\r\n          return\r\n        }\r\n        const resData = res.Data.map((item) => {\r\n          item.Is_Directory = true\r\n\r\n          return item\r\n        })\r\n        this.treeData = resData\r\n        // this.expandedKey = resData[0]?.Children[0]?.Id\r\n        // this.form.Area_Id = this.expandedKey\r\n        // this.form.Sys_Project_Id = resData[0].Data.Sys_Project_Id\r\n        this.$nextTick((_) => {\r\n          this.$refs.tree.filterRef(this.filterText)\r\n          const result = this.setKey()\r\n          if (!result) {\r\n            this.pgLoading = false\r\n          }\r\n        })\r\n        this.treeLoading = false\r\n      })\r\n    },\r\n    setKey() {\r\n      const deepFilter = (tree) => {\r\n        for (let i = 0; i < tree.length; i++) {\r\n          const item = tree[i]\r\n          const { Data, Children } = item\r\n          const node = getNode(Data.Id)\r\n          if (Data.ParentId && !Children?.length && node.visible) {\r\n            this.handleNodeClick(item)\r\n            return true\r\n          } else {\r\n            if (Children?.length) {\r\n              const shouldStop = deepFilter(Children)\r\n              if (shouldStop) return true\r\n            }\r\n          }\r\n        }\r\n        return false\r\n      }\r\n      const getNode = (key) => {\r\n        return this.$refs['tree'].getNodeByKey(key)\r\n      }\r\n      return deepFilter(this.treeData)\r\n    },\r\n    handleNodeClick(data) {\r\n      this.expandedKey = data.Id\r\n      this.currentNodeData = data\r\n      this.areaId = data.Id\r\n      this.form.Area_Id = data.Id\r\n      this.form.Sys_Project_Id = data.Data.Sys_Project_Id\r\n      this.multipleSelection = []\r\n      this.getTaskList()\r\n    },\r\n\r\n    saveSortFinish() {\r\n      this.$refs.tree.filterRef(this.filterText)\r\n    },\r\n\r\n    customFilterFun(value, data, node) {\r\n      const arr = value.split(SPLIT_SYMBOL)\r\n      const labelVal = arr[0]\r\n      const statusVal = arr[1]\r\n      if (!value) return true\r\n      let parentNode = node.parent\r\n      let labels = [node.label]\r\n      let status = [data.Data[this.statusKey]]\r\n      let level = 1\r\n      while (level < node.level) {\r\n        labels = [...labels, parentNode.label]\r\n        status = [...status, data.Data[this.statusKey]]\r\n        parentNode = parentNode.parent\r\n        level++\r\n      }\r\n      labels = labels.filter((v) => !!v)\r\n      status = status.filter((v) => !!v)\r\n      let resultLabel = true\r\n      let resultStatus = true\r\n      if (this.statusType) {\r\n        resultStatus = status.some((s) => s.indexOf(statusVal) !== -1)\r\n      }\r\n      if (this.projectName) {\r\n        resultLabel = labels.some((s) => s.indexOf(labelVal) !== -1)\r\n      }\r\n      return resultLabel && resultStatus\r\n    },\r\n\r\n    // 获取班组\r\n    async getProcessTeam() {\r\n      await GetTeamListByUser({\r\n        type: this.isCom ? 1 : 2 // 0:全部，工艺类型1：构件工艺，2：零件工艺\r\n      }).then((res) => {\r\n        this.teamOptions = res.Data.map((item) => {\r\n          return {\r\n            value: item.Id,\r\n            label: item.Name\r\n          }\r\n        })\r\n      })\r\n      this.form.Working_Team_Id = this.teamOptions[0]?.value || ''\r\n    },\r\n    handleTeamChange(value) {\r\n      this.form.Working_Team_Id = value\r\n      this.getTaskList()\r\n    },\r\n    // 获取任务单列表\r\n    async getTaskList() {\r\n      this.form.Task_Code = ''\r\n      this.pgLoading = true\r\n      const requestFn = this.isCom\r\n        ? GetCompTaskList\r\n        : GetSimplifiedPartTaskList\r\n\r\n      try {\r\n        const res = await requestFn(this.form)\r\n        if (res.IsSucceed) {\r\n          this.taskList = (res.Data || []).map((v) => {\r\n            v.Demand_Date = timeFormat(v.Demand_Date)\r\n            v.percentage = 0\r\n            return v\r\n          })\r\n          this.isNest = this.taskList.some((v) => !!v.Is_Nest)\r\n          this.form.Task_Code = this.taskList[0]?.Task_Code || ''\r\n          if (this.taskList.length > 0) {\r\n            const cur = this.taskList[0]\r\n            if (this.checkShowP(cur.Need_Part_Amount)) {\r\n              this.getPercentDetail()\r\n            }\r\n          }\r\n        } else {\r\n          this.taskList = []\r\n          this.$message.error(res.Message)\r\n        }\r\n\r\n        if (this.taskList.length > 0) {\r\n          await this.fetchData(1)\r\n        } else {\r\n          this.tbData = []\r\n        }\r\n      } catch (error) {\r\n      } finally {\r\n        this.checkResize() // initial check\r\n        this.pgLoading = false\r\n      }\r\n    },\r\n    handleTask(item) {\r\n      this.form.Task_Code = item.Task_Code\r\n      this.scrollToActiveItem(item)\r\n      this.multipleSelection = []\r\n      this.form.Code_Like = ''\r\n      this.form.Production_Status = ''\r\n      this.form.Spec_Like = ''\r\n      this.isNest = item.Is_Nest\r\n      this.fetchData(1)\r\n    },\r\n    scrollToActiveItem(item) {\r\n      this.$nextTick(() => {\r\n        const itemElement = this.$refs[`task_${item.Task_Code}`][0]\r\n        console.log(itemElement)\r\n        if (itemElement) {\r\n          itemElement.scrollIntoView({ behavior: 'smooth', inline: 'center' })\r\n        }\r\n      })\r\n    },\r\n    refresh() {\r\n      this.multipleSelection = []\r\n      this.fetchData(1)\r\n    },\r\n    async fetchData(page) {\r\n      page && (this.queryInfo.Page = page)\r\n      const form = { ...this.form, ...this.queryInfo }\r\n      if (form.Working_Team_Id === 'all') {\r\n        const ids = this.teamOptions\r\n          .map((v) => v.value)\r\n          .filter((s) => s !== 'all')\r\n          .toString()\r\n        form.Working_Team_Id = ids\r\n      }\r\n      if (!this.isCom) {\r\n        form.Show_Nest_Part = this.showNestPart\r\n      }\r\n      this.tbLoading = true\r\n      const requestFn = this.isCom\r\n        ? GetCompTaskPageList\r\n        : GetSimplifiedPartTaskPageList\r\n      await requestFn(form)\r\n        .then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.tbData = (res?.Data?.Data || []).map((v) => {\r\n              v.checked = false\r\n              return v\r\n            })\r\n            this.total = res.Data.TotalCount\r\n            this.getStopList(this.tbData)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.tbLoading = false\r\n        })\r\n        .finally((e) => {\r\n          this.tbLoading = false\r\n        })\r\n    },\r\n    async getStopList(list) {\r\n      const key = 'Id'\r\n      const submitObj = list.map(item => {\r\n        return {\r\n          Id: item[key],\r\n          Type: this.isCom ? 2 : 1 // 1：零件，3：部件，2：构件\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = !!item.Is_Stop\r\n          })\r\n          list.forEach(row => {\r\n            if (stopMap[row[key]]) {\r\n              this.$set(row, 'stopFlag', stopMap[row[key]])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 查看图纸\r\n    handleDwg(row) {\r\n      GetDwg({\r\n        Task_Id: row.Task_Id\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const fileurl = res?.Data?.length && res.Data[0].File_Url\r\n          window.open(\r\n            'http://dwgv1.bimtk.com:5432/?CadUrl=' + parseOssUrl(fileurl),\r\n            '_blank'\r\n          )\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 查看齐套弹框\r\n    handleQitao(row) {\r\n      this.dialogTitle = '零件齐套'\r\n      this.currentComponent = 'Qitao'\r\n      this.tid = row.Task_Id\r\n      this.compCode = row.Comp_Code\r\n      this.$nextTick((_) => {\r\n        this.dialogVisible = true\r\n      })\r\n    },\r\n    // 查看零件\r\n    viewPart(item) {\r\n      this.dialogTitle = '零件明细表'\r\n      this.currentComponent = 'PartDetail'\r\n      this.dialogVisible = true\r\n      const obj = {\r\n        Task_Code: item.Task_Code,\r\n        Working_Team_Id: this.form.Working_Team_Id,\r\n        Sys_Project_Id: item.Sys_Project_Id,\r\n        Need_Use_Part: item.Need_Use_Part\r\n      }\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(obj)\r\n      })\r\n    },\r\n    checCheckboxkMethod3({ row }) {\r\n      if (this.isCom) {\r\n        if (typeof row.Prepare_Count === 'number' && this.Is_Part_Prepare) {\r\n          return row.Ready_Process_Count > 0 && row.Prepare_Count > 0\r\n        } else {\r\n          return row.Ready_Process_Count > 0\r\n        }\r\n      } else {\r\n        return row.Ready_Process_Count > 0\r\n      }\r\n    },\r\n    multiSelectedChange(array) {\r\n      this.multipleSelection = array.records\r\n    },\r\n\r\n    // 任务单左右移动方法\r\n    clickMove(direction) {\r\n      const middleWapperWidth = this.$refs.middleWapper?.offsetWidth\r\n      const boxWapperWidth = this.$refs.boxWapper?.scrollWidth\r\n      console.log(middleWapperWidth, boxWapperWidth)\r\n\r\n      if (middleWapperWidth < boxWapperWidth) {\r\n        if (direction === 'left') {\r\n          this.offset = Math.min(this.offset + this.itemWidth, 0)\r\n        } else if (direction === 'right') {\r\n          const maxOffset = middleWapperWidth - boxWapperWidth\r\n          console.log(maxOffset, this.offset - this.itemWidth)\r\n          this.offset = Math.max(this.offset - this.itemWidth, maxOffset)\r\n        }\r\n      }\r\n      // 更新按钮的禁用状态\r\n      this.disableLeftBtn = this.offset === 0\r\n      this.disableRightBtn = this.offset === middleWapperWidth - boxWapperWidth\r\n      console.log(this.offset, this.disableLeftBtn, this.disableRightBtn)\r\n    },\r\n    checkResize() {\r\n      const middleWapperWidth = this.$refs.middleWapper.offsetWidth\r\n      const boxWapperWidth = this.$refs.boxWapper.scrollWidth\r\n      // console.log(middleWapperWidth, boxWapperWidth)\r\n\r\n      if (middleWapperWidth >= boxWapperWidth) {\r\n        this.offset = 0\r\n        this.IsShowBtn = false\r\n      } else {\r\n        const maxOffset = middleWapperWidth - boxWapperWidth\r\n        this.offset = Math.max(this.offset, maxOffset)\r\n        this.IsShowBtn = true\r\n      }\r\n      // 更新按钮的禁用状态\r\n      this.disableLeftBtn = this.offset === 0\r\n      this.disableRightBtn = this.offset === middleWapperWidth - boxWapperWidth\r\n      console.log(this.offset, this.disableLeftBtn, this.disableRightBtn)\r\n    },\r\n    showNestChange(val) {\r\n      this.refresh()\r\n    },\r\n    handleReport() {\r\n      if (this.isCom) {\r\n        this.currentComponent = 'ComReport'\r\n        this.dialogTitle = '报工'\r\n        this.$nextTick((_) => {\r\n          this.dialogVisible = true\r\n          this.$nextTick((_) => {\r\n            this.$refs['content'].init(\r\n              JSON.parse(JSON.stringify(this.multipleSelection))\r\n            )\r\n          })\r\n        })\r\n      } else {\r\n        this.dialogTitle = '零件报工'\r\n        this.currentComponent = 'PartReport'\r\n        this.dialogVisible = true\r\n        this.$nextTick((_) => {\r\n          this.$refs['content'].init(\r\n            JSON.parse(JSON.stringify(this.multipleSelection))\r\n          )\r\n        })\r\n      }\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    checkShowP(num) {\r\n      if (typeof num !== 'number') {\r\n        return false\r\n      }\r\n      return true\r\n    },\r\n    getPercentDetail() {\r\n      GetCompTaskPartCompletionStock({\r\n        Working_Team_Id: this.form.Working_Team_Id,\r\n        Task_Code: this.taskList.map((v) => v.Task_Code)\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const list = res.Data\r\n          const taskMap = {}\r\n          this.taskList.forEach((item) => {\r\n            taskMap[item.Task_Code] = item\r\n          })\r\n          list.forEach((item) => {\r\n            const cur = taskMap[item.Task_Code]\r\n            if (cur.Need_Part_Amount === -1) {\r\n              cur.percentage = '100%'\r\n            } else {\r\n              const p = numeral(item.Part_Stock_Count).divide(\r\n                cur.Need_Part_Amount\r\n              )\r\n              let result = '100%'\r\n              if (p.value() <= 1) {\r\n                result = p.format('0.[00]%')\r\n              }\r\n              cur.percentage = result\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"~@/styles/mixin.scss\";\r\n// @import \"~@/styles/tabs.scss\";\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n.app-wrapper {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  overflow: hidden;\r\n\r\n  .cs-left {\r\n    display: flex;\r\n    flex-direction: column;\r\n    margin-right: 20px;\r\n\r\n    .inner-wrapper {\r\n      flex: 1;\r\n      display: flex;\r\n      flex-direction: column;\r\n      padding: 16px 16px;\r\n      border-radius: 4px;\r\n      overflow: hidden;\r\n      .cs-search {\r\n        border-bottom: 1px solid #e2e4e9;\r\n        padding-bottom: 17px;\r\n        .team-select {\r\n          margin-bottom: 12px;\r\n          .el-select {\r\n            width: 100%;\r\n          }\r\n\r\n          ::v-deep {\r\n            .el-input__inner {\r\n              border: 1px solid #298dff;\r\n              color: #298dff;\r\n              font-weight: bold;\r\n            }\r\n            .el-input__prefix {\r\n              display: flex;\r\n              justify-content: center;\r\n              align-items: center;\r\n\r\n              img {\r\n                width: 16px;\r\n                height: 16px;\r\n              }\r\n            }\r\n\r\n            .el-select__caret {\r\n              color: #298dff;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      .cs-tree {\r\n        margin-top: 22px;\r\n        flex: 1;\r\n        height: 0;\r\n\r\n        .cs-scroll {\r\n          overflow-y: auto;\r\n          @include scrollBar;\r\n        }\r\n\r\n        .el-tree {\r\n          height: 100%;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .cs-right {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n    padding: 16px 16px;\r\n\r\n    .cs-top-wapper {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      // align-items: center;\r\n      .btn {\r\n        width: 20px;\r\n        height: 20px;\r\n        border-radius: 10px;\r\n        background: #cfcfcf;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        color: #fff;\r\n        font-weight: bold;\r\n        font-size: 12px;\r\n        cursor: pointer;\r\n        margin-top: calc((96px / 2) - 10px);\r\n      }\r\n      .btn.disabled {\r\n        opacity: 0.5;\r\n        // pointer-events: none;\r\n        cursor: not-allowed !important;\r\n      }\r\n\r\n      .middle-wapper::-webkit-scrollbar {\r\n        height: 0; /* 将滚动条的宽度设为0 */\r\n        background: transparent; /* 使滚动条完全透明 */\r\n      }\r\n\r\n      .middle-wapper {\r\n        flex: 1;\r\n        width: 0;\r\n        overflow-x: auto;\r\n        position: relative;\r\n        height: calc(96px + 10px);\r\n\r\n        .box-wapper {\r\n          position: absolute;\r\n          display: flex;\r\n          flex-wrap: no-wrap;\r\n          height: 100%;\r\n          transition: transform 0.3s ease;\r\n\r\n          .item {\r\n            width: 275px;\r\n            height: calc(100% - 10px);\r\n            border-radius: 4px 4px 4px 4px;\r\n            border: 1px solid #e2e4e9;\r\n            margin-left: 12px;\r\n            position: relative;\r\n            cursor: pointer;\r\n            .content {\r\n              width: 100%;\r\n              height: 100%;\r\n              display: flex;\r\n              padding: 12px 12px;\r\n              flex-direction: column;\r\n              // align-items: center;\r\n              // justify-content: space-between;\r\n              // background: linear-gradient( 91deg, #298DFF 0%, #57C2FF 100%);\r\n              .detail {\r\n                flex: 1;\r\n                height: 0;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: space-between;\r\n              }\r\n              .title {\r\n                margin-bottom: 8px;\r\n                display: flex;\r\n                align-items: center;\r\n              }\r\n              .name {\r\n                font-weight: bold;\r\n                font-size: 16px;\r\n              }\r\n              .precent {\r\n                height: 17px;\r\n                padding: 0 4px;\r\n                background: #00cfaa;\r\n                border-radius: 9px 9px 9px 9px;\r\n                color: #ffffff;\r\n                font-size: 12px;\r\n                margin-left: 8px;\r\n                line-height: 17px;\r\n                text-align: center;\r\n              }\r\n              .info {\r\n                font-size: 14px;\r\n                color: #999999;\r\n                margin-bottom: 2px;\r\n              }\r\n\r\n              .right {\r\n                // width: 80px;\r\n                padding: 5px 8px;\r\n                border-radius: 4px 4px 4px 4px;\r\n                text-align: center;\r\n\r\n                font-size: 14px;\r\n                cursor: pointer;\r\n                background: #e9f3ff;\r\n                color: #298dff;\r\n              }\r\n            }\r\n\r\n            .flag {\r\n              position: absolute;\r\n              top: 0;\r\n              right: 0;\r\n              width: 35px;\r\n              height: 35px;\r\n              background: url(\"~@/assets/PRO/flag.png\") no-repeat;\r\n              font-size: 12px;\r\n              color: #ffffff;\r\n              display: flex;\r\n              justify-content: flex-end;\r\n              align-items: flex-start;\r\n              padding-right: 4px;\r\n              padding-top: 6px;\r\n            }\r\n            i {\r\n              position: absolute;\r\n              bottom: -10px;\r\n              left: calc((100% - 16px) / 2);\r\n              color: #298dff;\r\n            }\r\n            .flag2 {\r\n              position: absolute;\r\n              bottom: 0;\r\n              right: 0;\r\n              width: 0;\r\n              height: 0;\r\n              border-left: 35px solid transparent;\r\n              border-top: 35px solid transparent;\r\n              border-bottom: 35px solid #e6a23c;\r\n              font-size: 12px;\r\n              color: #ffffff;\r\n              .flag2-txt {\r\n                position: absolute;\r\n                right: 3px;\r\n                bottom: -33px;\r\n              }\r\n            }\r\n          }\r\n          .active {\r\n            .content {\r\n              color: #ffffff;\r\n              background: linear-gradient(91deg, #298dff 0%, #57c2ff 100%);\r\n              .info {\r\n                color: #ffffff;\r\n              }\r\n            }\r\n            .right {\r\n              background: #ffffff;\r\n            }\r\n          }\r\n          .item:last-child {\r\n            margin-right: 12px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .cs-middle-wapper {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      margin: 12px 0;\r\n      align-items: center;\r\n      .el-form-item {\r\n        margin-bottom: 0;\r\n      }\r\n    }\r\n\r\n    .cs-bottom-wapper {\r\n      flex: 1;\r\n      height: 0;\r\n      display: flex;\r\n      flex-direction: column;\r\n      .tb-x {\r\n        flex: 1;\r\n        height: 0;\r\n      }\r\n\r\n      .pagination-container {\r\n        text-align: right;\r\n        padding: 16px;\r\n        margin: 0;\r\n      }\r\n\r\n      .data-info {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.plm-custom-dialog {\r\n  ::v-deep {\r\n    .el-dialog__body {\r\n      height: 70vh;\r\n      overflow: auto;\r\n      display: flex;\r\n      flex-direction: column;\r\n    }\r\n  }\r\n}\r\n\r\n.fourGreen {\r\n  color: #00c361;\r\n  font-style: normal;\r\n}\r\n\r\n.fourOrange {\r\n  color: #ff9400;\r\n  font-style: normal;\r\n}\r\n\r\n.fourRed {\r\n  color: #ff0000;\r\n  font-style: normal;\r\n}\r\n\r\n.cs-blue {\r\n  color: #5ac8fa;\r\n}\r\n.cs-checkbox {\r\n  margin-left: 16px;\r\n  line-height: 30px;\r\n}\r\n.cs-middle-wapper-left{\r\n  display: flex;\r\n  flex-wrap: nowrap;\r\n}\r\n</style>\r\n"]}]}
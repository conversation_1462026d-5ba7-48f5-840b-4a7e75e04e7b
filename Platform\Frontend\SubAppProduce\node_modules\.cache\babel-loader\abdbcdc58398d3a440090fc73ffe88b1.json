{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\component\\FactoryAddDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\component\\FactoryAddDialog.vue", "mtime": 1757468128015}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetProjectPageList", "name", "props", "typeId", "type", "String", "default", "addLevel", "Number", "parentId", "activeType", "typeCode", "isComp", "Boolean", "showDirect", "data", "loading", "searchForm", "ProjectCode", "ProjectAbbr", "selectedProjects", "tableConfig", "tableColumns", "tableActions", "tableData", "checkbox", "operateOptions", "width", "align", "isShow", "created", "fetchProjectList", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "params", "res", "table", "wrap", "_callee$", "_context", "prev", "next", "Page", "currentPage", "PageSize", "pageSize", "sent", "IsSucceed", "Data", "for<PERSON>ach", "item", "index", "$message", "error", "Message", "t0", "console", "finish", "stop", "handleGridData", "log", "handleSearch", "handleReset", "handleSelectionChange", "selection", "handleRowClick", "row", "handleCancel", "$emit", "handleConfirm", "length", "warning", "success", "concat", "handleAddToList"], "sources": ["src/views/PRO/project-config/product-mfg-path/component/FactoryAddDialog.vue"], "sourcesContent": ["<template>\r\n  <div class=\"add-project-container\">\r\n    <!-- 搜索区域 -->\r\n    <div class=\"search-section\">\r\n      <el-form :model=\"searchForm\" inline>\r\n        <el-form-item label=\"项目编号：\">\r\n          <el-input\r\n            v-model=\"searchForm.ProjectCode\"\r\n            placeholder=\"请输入\"\r\n            clearable\r\n            style=\"width: 200px\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"项目简称：\">\r\n          <el-input\r\n            v-model=\"searchForm.ProjectAbbr\"\r\n            placeholder=\"请输入\"\r\n            clearable\r\n            style=\"width: 200px\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">搜索</el-button>\r\n          <el-button @click=\"handleReset\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n\r\n    <!-- 提示信息 -->\r\n    <div class=\"instruction-section\">\r\n      <div class=\"instruction\">\r\n        请选择工厂级配置的工序类型，添加到项目\r\n      </div>\r\n      <el-button type=\"primary\" @click=\"handleAddToList\"> 加入列表 </el-button>\r\n    </div>\r\n\r\n    <!-- 项目表格 -->\r\n    <div class=\"table-section\">\r\n      <bt-table\r\n        ref=\"projectTable\"\r\n        code=\"AddProjectList\"\r\n        :custom-table-config=\"tableConfig\"\r\n        :grid-data-handler=\"handleGridData\"\r\n        :loading=\"loading\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        @row-click=\"handleRowClick\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 底部按钮 -->\r\n    <div class=\"footer-actions\">\r\n      <el-button @click=\"handleCancel\">取消</el-button>\r\n      <el-button type=\"primary\" :disabled=\"selectedProjects.length === 0\" @click=\"handleConfirm\">\r\n        确定\r\n      </el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetProjectPageList } from '@/api/PRO/project'\r\n\r\nexport default {\r\n  name: 'AddProject',\r\n  props: {\r\n    typeId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    addLevel: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    parentId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    activeType: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    typeCode: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    isComp: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    showDirect: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      searchForm: {\r\n        ProjectCode: '',\r\n        ProjectAbbr: ''\r\n      },\r\n      selectedProjects: [],\r\n      tableConfig: {\r\n        tableColumns: [],\r\n        tableActions: [],\r\n        tableData: [],\r\n        checkbox: false,\r\n        operateOptions: {\r\n          width: 120,\r\n          align: 'center',\r\n          isShow: false\r\n        }\r\n\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchProjectList()\r\n  },\r\n  methods: {\r\n\r\n    // 获取项目列表\r\n    async fetchProjectList() {\r\n      this.loading = true\r\n      try {\r\n        const params = {\r\n          Page: this.tableConfig.currentPage,\r\n          PageSize: this.tableConfig.pageSize\r\n          // SortName: 'Create_Date',\r\n          // SortOrder: 'DESC',\r\n          // Search: '',\r\n          // ParameterJson: JSON.stringify({\r\n          //   ProjectCode: this.searchForm.ProjectCode,\r\n          //   ProjectAbbr: this.searchForm.ProjectAbbr\r\n          // })\r\n        }\r\n\r\n        const res = await GetProjectPageList(params)\r\n        if (res.IsSucceed) {\r\n          const table = res.Data.Data || []\r\n\r\n          // 为数据添加行号\r\n          table.forEach((item, index) => {\r\n            // item.IscheckMethod = undefined\r\n          })\r\n          this.tableConfig.tableData = table\r\n        } else {\r\n          this.$message.error(res.Message || '获取项目列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取项目列表失败:', error)\r\n        this.$message.error('获取项目列表失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    handleGridData(data) {\r\n      console.log(data, 3313)\r\n      return data\r\n    },\r\n    // 搜索\r\n    handleSearch() {\r\n      this.tableConfig.currentPage = 1\r\n      this.fetchProjectList()\r\n    },\r\n\r\n    // 重置\r\n    handleReset() {\r\n      this.searchForm = {\r\n        ProjectCode: '',\r\n        ProjectAbbr: ''\r\n      }\r\n      this.tableConfig.currentPage = 1\r\n      this.fetchProjectList()\r\n    },\r\n\r\n    // 选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedProjects = selection\r\n    },\r\n\r\n    // 行点击\r\n    handleRowClick(row) {\r\n      // 可以在这里添加行点击逻辑\r\n      console.log('点击行:', row)\r\n    },\r\n\r\n    // 取消\r\n    handleCancel() {\r\n      this.$emit('close')\r\n    },\r\n\r\n    // 确认选择\r\n    handleConfirm() {\r\n      if (this.selectedProjects.length === 0) {\r\n        this.$message.warning('请至少选择一个项目')\r\n        return\r\n      }\r\n\r\n      // 这里可以处理选中的项目数据\r\n      console.log('选中的项目:', this.selectedProjects)\r\n\r\n      // 触发父组件事件\r\n      this.$emit('close')\r\n      this.$emit('getTreeList')\r\n\r\n      this.$message.success(`已选择 ${this.selectedProjects.length} 个项目`)\r\n    },\r\n    handleAddToList() {\r\n      console.log('加入列表')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.add-project-container {\r\n  height:70vh;\r\n\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .search-section {\r\n    background: #fff;\r\n    border-radius: 4px;\r\n  }\r\n\r\n  .instruction-section {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n\r\n  .instruction {\r\n    background: #f0f9ff;\r\n    border: 1px solid #b3d8ff;\r\n    color: #1890ff;\r\n    padding: 12px 16px;\r\n    border-radius: 4px;\r\n    margin-bottom: 16px;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .table-section {\r\n    flex: 1;\r\n    background: #fff;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .footer-actions {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    padding: 0px 8px 0 0;\r\n    background: #fff;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DA,SAAAA,kBAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,MAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,QAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACAG,QAAA;MACAL,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAI,UAAA;MACAN,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAK,QAAA;MACAP,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAM,MAAA;MACAR,IAAA,EAAAS,OAAA;MACAP,OAAA;IACA;IACAQ,UAAA;MACAV,IAAA,EAAAS,OAAA;MACAP,OAAA;IACA;EACA;EACAS,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;QACAC,WAAA;QACAC,WAAA;MACA;MACAC,gBAAA;MACAC,WAAA;QACAC,YAAA;QACAC,YAAA;QACAC,SAAA;QACAC,QAAA;QACAC,cAAA;UACAC,KAAA;UACAC,KAAA;UACAC,MAAA;QACA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,gBAAA;EACA;EACAC,OAAA;IAEA;IACAD,gBAAA,WAAAA,iBAAA;MAAA,IAAAE,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,MAAA,EAAAC,GAAA,EAAAC,KAAA;QAAA,OAAAL,mBAAA,GAAAM,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAZ,KAAA,CAAAjB,OAAA;cAAA2B,QAAA,CAAAC,IAAA;cAEAN,MAAA;gBACAQ,IAAA,EAAAb,KAAA,CAAAZ,WAAA,CAAA0B,WAAA;gBACAC,QAAA,EAAAf,KAAA,CAAAZ,WAAA,CAAA4B;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;cACA;cAAAN,QAAA,CAAAE,IAAA;cAAA,OAEA7C,kBAAA,CAAAsC,MAAA;YAAA;cAAAC,GAAA,GAAAI,QAAA,CAAAO,IAAA;cACA,IAAAX,GAAA,CAAAY,SAAA;gBACAX,KAAA,GAAAD,GAAA,CAAAa,IAAA,CAAAA,IAAA,QAEA;gBACAZ,KAAA,CAAAa,OAAA,WAAAC,IAAA,EAAAC,KAAA;kBACA;gBAAA,CACA;gBACAtB,KAAA,CAAAZ,WAAA,CAAAG,SAAA,GAAAgB,KAAA;cACA;gBACAP,KAAA,CAAAuB,QAAA,CAAAC,KAAA,CAAAlB,GAAA,CAAAmB,OAAA;cACA;cAAAf,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAgB,EAAA,GAAAhB,QAAA;cAEAiB,OAAA,CAAAH,KAAA,cAAAd,QAAA,CAAAgB,EAAA;cACA1B,KAAA,CAAAuB,QAAA,CAAAC,KAAA;YAAA;cAAAd,QAAA,CAAAC,IAAA;cAEAX,KAAA,CAAAjB,OAAA;cAAA,OAAA2B,QAAA,CAAAkB,MAAA;YAAA;YAAA;cAAA,OAAAlB,QAAA,CAAAmB,IAAA;UAAA;QAAA,GAAAzB,OAAA;MAAA;IAEA;IAEA0B,cAAA,WAAAA,eAAAhD,IAAA;MACA6C,OAAA,CAAAI,GAAA,CAAAjD,IAAA;MACA,OAAAA,IAAA;IACA;IACA;IACAkD,YAAA,WAAAA,aAAA;MACA,KAAA5C,WAAA,CAAA0B,WAAA;MACA,KAAAhB,gBAAA;IACA;IAEA;IACAmC,WAAA,WAAAA,YAAA;MACA,KAAAjD,UAAA;QACAC,WAAA;QACAC,WAAA;MACA;MACA,KAAAE,WAAA,CAAA0B,WAAA;MACA,KAAAhB,gBAAA;IACA;IAEA;IACAoC,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAhD,gBAAA,GAAAgD,SAAA;IACA;IAEA;IACAC,cAAA,WAAAA,eAAAC,GAAA;MACA;MACAV,OAAA,CAAAI,GAAA,SAAAM,GAAA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,KAAA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAA;MACA,SAAArD,gBAAA,CAAAsD,MAAA;QACA,KAAAlB,QAAA,CAAAmB,OAAA;QACA;MACA;;MAEA;MACAf,OAAA,CAAAI,GAAA,gBAAA5C,gBAAA;;MAEA;MACA,KAAAoD,KAAA;MACA,KAAAA,KAAA;MAEA,KAAAhB,QAAA,CAAAoB,OAAA,uBAAAC,MAAA,MAAAzD,gBAAA,CAAAsD,MAAA;IACA;IACAI,eAAA,WAAAA,gBAAA;MACAlB,OAAA,CAAAI,GAAA;IACA;EACA;AACA", "ignoreList": []}]}
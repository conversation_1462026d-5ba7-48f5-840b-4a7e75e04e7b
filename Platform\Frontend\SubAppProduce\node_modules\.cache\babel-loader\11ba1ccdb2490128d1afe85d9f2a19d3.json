{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\detail.vue", "mtime": 1758683411838}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9yZWdlbmVyYXRvclJ1bnRpbWUgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9yZWdlbmVyYXRvclJ1bnRpbWUuanMiOwppbXBvcnQgX2FzeW5jVG9HZW5lcmF0b3IgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9hc3luY1RvR2VuZXJhdG9yLmpzIjsKaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuY29uY2F0LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsdGVyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmluZC1pbmRleC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmpvaW4uanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5wdXNoLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuc3BsaWNlLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5maWx0ZXIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5tYXAuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3Qua2V5cy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5zZWFyY2guanMiOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwoKaW1wb3J0IGdldFRiSW5mbyBmcm9tICdAL21peGlucy9QUk8vZ2V0LXRhYmxlLWluZm8nOwppbXBvcnQgeyBHZXRUZWFtVGFza0RldGFpbHMsIEV4cG9ydFRhc2tDb2RlRGV0YWlscywgR2V0U3VnZ2VzdERldmljZUFuZFJlbWFyayB9IGZyb20gJ0AvYXBpL1BSTy9wcm9kdWN0aW9uLXRhc2snOwppbXBvcnQgeyBkZWJvdW5jZSwgY2xvc2VUYWdWaWV3IH0gZnJvbSAnQC91dGlscyc7CmltcG9ydCBRcmNvZGVWdWUgZnJvbSAncXJjb2RlLnZ1ZSc7CmltcG9ydCB7IEdldFByb2Nlc3NMaXN0LCBHZXRXb3JraW5nVGVhbXNQYWdlTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy90ZWNobm9sb2d5LWxpYic7Ci8vIGltcG9ydCB7IEdldFByb2Nlc3NMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL3RlY2hub2xvZ3ktbGliJwppbXBvcnQgVHJhbnNmZXJEZXRhaWwgZnJvbSAnLi90cmFuc2ZlckRldGFpbCc7Ci8vIGltcG9ydCB7IEV4cG9ydCB9IGZyb20gJ3Z4ZS10YWJsZScKaW1wb3J0IHsgY29tYmluZVVSTCB9IGZyb20gJ0AvdXRpbHMnOwppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCc7CmltcG9ydCBTdWdnZXN0RGV2aWNlIGZyb20gJy4vc3VnZ2VzdERldmljZSc7CmltcG9ydCB7IGdldEJvbUNvZGUsIGdldEJvbU5hbWUsIGNoZWNrSXNVbml0UGFydCB9IGZyb20gJ0Avdmlld3MvUFJPL2JvbS1zZXR0aW5nL3V0aWxzJzsKdmFyIHByaW50U3R5bGUgPSAiXG4gICAgICAgIC50aXRsZSB7XG4gICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgICB9XG4gICAgICAgIC5pcy0tcHJpbnR7XG4gICAgICAgICAgYm94LXNpemluZzogYm9yZGVyLWJveDtcbiAgICAgICAgICB3aWR0aDo5NSUgIWltcG9ydGFudDtcbiAgICAgICAgICBtYXJnaW46MCBhdXRvICFpbXBvcnRhbnQ7XG4gICAgICAgIH1cbiAgICAgICAgLm15LWxpc3Qtcm93IHtcbiAgICAgICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XG4gICAgICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICAgICAgbWFyZ2luLWxlZnQ6MyU7XG4gICAgICAgIH1cbiAgICAgICAgLm15LWxpc3Qtcm93LWZpcnN0IHtcbiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxMHB4O1xuICAgICAgICB9XG4gICAgICAgIC5teS1saXN0LWNvbCB7XG4gICAgICAgICAgd2lkdGg6MzAlO1xuICAgICAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jaztcbiAgICAgICAgICBmbG9hdDogbGVmdDtcbiAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDElO1xuICAgICAgICAgIHdvcmQtd3JhcDpicmVhay13b3JkO1xuICAgICAgICAgIHdvcmQtYnJlYWs6bm9ybWFsO1xuICAgICAgICB9XG4gICAgICAgIC5sZWZ0e1xuICAgICAgICAgIGZsZXg6MTtcbiAgICAgICAgfVxuICAgICAgICAubXktdG9wIHtcbiAgICAgICAgICBkaXNwbGF5OmZsZXg7XG4gICAgICAgICAgZm9udC1zaXplOiAxMnB4O1xuICAgICAgICAgIG1hcmdpbi1ib3R0b206IDVweDtcbiAgICAgICAgfVxuICAgICAgICAucXJjb2Rle1xuICAgICAgICAgIG1hcmdpbi1yaWdodDoxMHB4XG4gICAgICAgIH1cbiAgICAgICAgLmNzLWltZ3tcbiAgICAgICAgICBwb3NpdGlvbjpyZWxhdGl2ZTtcbiAgICAgICAgICByaWdodDozMHB4XG4gICAgICAgIH1cbiAgICAgICAgIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdQUk9UYXNrTGlzdERldGFpbCcsCiAgY29tcG9uZW50czogewogICAgVHJhbnNmZXJEZXRhaWw6IFRyYW5zZmVyRGV0YWlsLAogICAgUXJjb2RlVnVlOiBRcmNvZGVWdWUsCiAgICBTdWdnZXN0RGV2aWNlOiBTdWdnZXN0RGV2aWNlCiAgfSwKICBtaXhpbnM6IFtnZXRUYkluZm9dLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgcmV0dXJuIHsKICAgICAgYm9tTmFtZTogJycsCiAgICAgIGRldmljZURpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgcXVlcnlGb3JtOiB7CiAgICAgICAgLy8gUHJvamVjdF9JZDogJycsCiAgICAgICAgLy8gQXJlYV9JZDogJycsCiAgICAgICAgLy8gSW5zdGFsbFVuaXRfSWQ6ICcnLAogICAgICAgIE5leHRfUHJvY2Vzc19JZDogJycsCiAgICAgICAgTmV4dF9UZWFtX0lkOiAnJwogICAgICB9LAogICAgICBxdWVyeUluZm86IHsKICAgICAgICBQYWdlOiAxLAogICAgICAgIFBhZ2VTaXplOiAtMQogICAgICB9LAogICAgICB0YkNvbmZpZzogewogICAgICAgIE9wX1dpZHRoOiAxMjAKICAgICAgfSwKICAgICAgdGJMb2FkaW5nOiBmYWxzZSwKICAgICAgY29sdW1uczogW10sCiAgICAgIG11bHRpcGxlU2VsZWN0aW9uOiBbXSwKICAgICAgdGJEYXRhOiBbXSwKICAgICAgZmluaXNoTGlzdDogW10sCiAgICAgIHByb2Nlc3NPcHRpb246IFtdLAogICAgICBncm91cE9wdGlvbjogW10sCiAgICAgIHRvdGFsOiAwLAogICAgICBwcmludENvbHVtbnM6IFtdLAogICAgICBzZWFyY2g6IGZ1bmN0aW9uIHNlYXJjaCgpIHsKICAgICAgICByZXR1cm4ge307CiAgICAgIH0sCiAgICAgIHBhZ2VUeXBlOiAnJywKICAgICAgaW5mbzogewogICAgICAgIFRhc2tfQ29kZTogJycsCiAgICAgICAgUHJvamVjdF9OYW1lOiAnJywKICAgICAgICBBcmVhX05hbWU6ICcnLAogICAgICAgIEluc3RhbGxVbml0X05hbWU6ICcnLAogICAgICAgIFNjaGR1bGluZ19Db2RlOiAnJywKICAgICAgICBUYXNrX0ZpbmlzaF9EYXRlOiAnJywKICAgICAgICBGaW5pc2hfRGF0ZTI6ICcnLAogICAgICAgIE9yZGVyX0RhdGU6ICcnLAogICAgICAgIFdvcmtpbmdfVGVhbV9OYW1lOiAnJywKICAgICAgICBXb3JraW5nX1Byb2Nlc3NfTmFtZTogJycsCiAgICAgICAgUHJvY2Vzc19TdGFydF9EYXRlOiAnJywKICAgICAgICBQcm9jZXNzX0ZpbmlzaF9EYXRlOiAnJwogICAgICB9LAogICAgICBwcmludENvbmZpZzogewogICAgICAgIHNoZWV0TmFtZTogJ+S7u+WKoeWNleivpuaDhScsCiAgICAgICAgc3R5bGU6IHByaW50U3R5bGUsCiAgICAgICAgYmVmb3JlUHJpbnRNZXRob2Q6IGZ1bmN0aW9uIGJlZm9yZVByaW50TWV0aG9kKF9yZWYpIHsKICAgICAgICAgIHZhciBjb250ZW50ID0gX3JlZi5jb250ZW50OwogICAgICAgICAgcmV0dXJuIF90aGlzLnRvcEh0bWwgKyBjb250ZW50OwogICAgICAgIH0KICAgICAgfSwKICAgICAgVGVuYW50X0NvZGU6IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0ZW5hbnQnKSwKICAgICAgUmVtYXJrOiAnJywKICAgICAgZXFwdEluZm9MaXN0OiBbXSwKICAgICAgZXFwdEluZm9MaXN0U3RyOiAnJwogICAgfTsKICB9LAogIGNvbXB1dGVkOiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIG1hcEdldHRlcnMoJ3RlbmFudCcsIFsnaXNWZXJzaW9uRm91ciddKSksIHt9LCB7CiAgICBpc0NvbTogZnVuY3Rpb24gaXNDb20oKSB7CiAgICAgIHJldHVybiB0aGlzLnBhZ2VUeXBlID09PSBnZXRCb21Db2RlKCctMScpOwogICAgfSwKICAgIGlzVW5pdFBhcnQ6IGZ1bmN0aW9uIGlzVW5pdFBhcnQoKSB7CiAgICAgIHJldHVybiBjaGVja0lzVW5pdFBhcnQodGhpcy5wYWdlVHlwZSk7CiAgICB9LAogICAgaXNQYXJ0OiBmdW5jdGlvbiBpc1BhcnQoKSB7CiAgICAgIHJldHVybiB0aGlzLnBhZ2VUeXBlID09PSBnZXRCb21Db2RlKCcwJyk7CiAgICB9CiAgfSksCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlKCkgewogICAgICB2YXIgaWR4OwogICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZSQoX2NvbnRleHQpIHsKICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dC5wcmV2ID0gX2NvbnRleHQubmV4dCkgewogICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICBfdGhpczIucGFnZVR5cGUgPSBfdGhpczIuJHJvdXRlLnF1ZXJ5LnR5cGU7CiAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAzOwogICAgICAgICAgICByZXR1cm4gZ2V0Qm9tTmFtZShfdGhpczIucGFnZVR5cGUpOwogICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICBfdGhpczIuYm9tTmFtZSA9IF9jb250ZXh0LnNlbnQ7CiAgICAgICAgICAgIF90aGlzMi53b3JrVGVhbUlkID0gX3RoaXMyLiRyb3V0ZS5xdWVyeS50aWQ7CiAgICAgICAgICAgIF90aGlzMi50YXNrQ29kZSA9IF90aGlzMi4kcm91dGUucXVlcnkuaWQ7CiAgICAgICAgICAgIF90aGlzMi5pbmZvID0gSlNPTi5wYXJzZShkZWNvZGVVUklDb21wb25lbnQoX3RoaXMyLiRyb3V0ZS5xdWVyeS5vdGhlcikpOwogICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gOTsKICAgICAgICAgICAgcmV0dXJuIF90aGlzMi5nZXRTdWdnZXN0RGV2aWNlQW5kUmVtYXJrKCk7CiAgICAgICAgICBjYXNlIDk6CiAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAxMTsKICAgICAgICAgICAgcmV0dXJuIF90aGlzMi5nZXRUYWJsZUNvbmZpZyhfdGhpczIuaXNDb20gPyAnUFJPQ29tVGFza0xpc3REZXRhaWwnIDogX3RoaXMyLmlzVW5pdFBhcnQgPyAnUFJPVW5pdFBhcnRUYXNrTGlzdERldGFpbCcgOiAnUFJPUGFydFRhc2tMaXN0RGV0YWlsJyk7CiAgICAgICAgICBjYXNlIDExOgogICAgICAgICAgICBpZiAoX3RoaXMyLmlzQ29tKSB7CiAgICAgICAgICAgICAgaWR4ID0gX3RoaXMyLmNvbHVtbnMuZmluZEluZGV4KGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgICAgICByZXR1cm4gaXRlbS5Db2RlID09PSAnUGFydF9Db2RlJzsKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICBpZHggIT09IC0xICYmIF90aGlzMi5jb2x1bW5zLnNwbGljZShpZHgsIDEpOwogICAgICAgICAgICAgIF90aGlzMi5wcmludENvbHVtbnMgPSBfdGhpczIuY29sdW1ucy5maWx0ZXIoZnVuY3Rpb24gKGNvbHVtbikgewogICAgICAgICAgICAgICAgcmV0dXJuIGNvbHVtbi5Db2RlICE9PSAnQ29tcF9EZXNjcmlwdGlvbic7CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgaWYgKF90aGlzMi5pc1VuaXRQYXJ0KSB7CiAgICAgICAgICAgICAgX3RoaXMyLnByaW50Q29sdW1ucyA9IF90aGlzMi5jb2x1bW5zLmZpbHRlcihmdW5jdGlvbiAoY29sdW1uKSB7CiAgICAgICAgICAgICAgICByZXR1cm4gY29sdW1uLkNvZGUgIT09ICdQcm9qZWN0X05hbWUnICYmIGNvbHVtbi5Db2RlICE9PSAnQXJlYV9OYW1lJyAmJiBjb2x1bW4uQ29kZSAhPT0gJ0ZpbmlzaF9Db3VudCcgJiYgY29sdW1uLkNvZGUgIT09ICdGaW5pc2hfV2VpZ2h0JyAmJiBjb2x1bW4uQ29kZSAhPT0gJ0NvbXBfRGVzY3JpcHRpb24nOwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICAgIGlmIChfdGhpczIuaXNQYXJ0KSB7CiAgICAgICAgICAgICAgX3RoaXMyLnByaW50Q29sdW1ucyA9IF90aGlzMi5jb2x1bW5zLmZpbHRlcihmdW5jdGlvbiAoY29sdW1uKSB7CiAgICAgICAgICAgICAgICByZXR1cm4gY29sdW1uLkNvZGUgIT09ICdQcm9qZWN0X05hbWUnICYmIGNvbHVtbi5Db2RlICE9PSAnQXJlYV9OYW1lJyAmJiBjb2x1bW4uQ29kZSAhPT0gJ0ZpbmlzaF9Db3VudCcgJiYgY29sdW1uLkNvZGUgIT09ICdGaW5pc2hfV2VpZ2h0JyAmJiBjb2x1bW4uQ29kZSAhPT0gJ0NvbXBfRGVzY3JpcHRpb24nOwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CgogICAgICAgICAgICAvLyBlbHNlIHsKICAgICAgICAgICAgLy8gICBjb25zdCBpZHggPSB0aGlzLmNvbHVtbnMuZmluZEluZGV4KChpdGVtKSA9PiBpdGVtLkNvZGUgPT09ICdDb21wX0NvZGUnKQogICAgICAgICAgICAvLyAgIGlkeCAhPT0gLTEgJiYgdGhpcy5jb2x1bW5zLnNwbGljZShpZHgsIDEpCiAgICAgICAgICAgIC8vIH0KICAgICAgICAgICAgX3RoaXMyLnNlYXJjaCA9IGRlYm91bmNlKF90aGlzMi5mZXRjaERhdGEsIDgwMCwgdHJ1ZSk7CiAgICAgICAgICAgIF90aGlzMi5mZXRjaERhdGEoKTsKICAgICAgICAgICAgX3RoaXMyLmdldFByb2Nlc3NPcHRpb24oKTsKICAgICAgICAgICAgX3RoaXMyLmdldEFsbFRlYW1PcHRpb24oKTsKICAgICAgICAgICAgX3RoaXMyLmdldEh0bWwoKTsKICAgICAgICAgIGNhc2UgMTk6CiAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuc3RvcCgpOwogICAgICAgIH0KICAgICAgfSwgX2NhbGxlZSk7CiAgICB9KSkoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGdldFN1Z2dlc3REZXZpY2VBbmRSZW1hcms6IGZ1bmN0aW9uIGdldFN1Z2dlc3REZXZpY2VBbmRSZW1hcmsoKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICBHZXRTdWdnZXN0RGV2aWNlQW5kUmVtYXJrKHsKICAgICAgICBCb21fTGV2ZWw6IHRoaXMucGFnZVR5cGUsCiAgICAgICAgVGFza19Db2RlOiB0aGlzLmluZm8uVGFza19Db2RlCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICB2YXIgX3JlcyREYXRhLCBfcmVzJERhdGEyOwogICAgICAgICAgX3RoaXMzLlJlbWFyayA9IChfcmVzJERhdGEgPSByZXMuRGF0YSkgPT09IG51bGwgfHwgX3JlcyREYXRhID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfcmVzJERhdGEuUmVtYXJrOwogICAgICAgICAgX3RoaXMzLmVxcHRJbmZvTGlzdCA9ICgoX3JlcyREYXRhMiA9IHJlcy5EYXRhKSA9PT0gbnVsbCB8fCBfcmVzJERhdGEyID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfcmVzJERhdGEyLmVxcHRJbmZvTGlzdCkgfHwgW107CiAgICAgICAgICBfdGhpczMuZXFwdEluZm9MaXN0U3RyID0gX3RoaXMzLmVxcHRJbmZvTGlzdC5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgcmV0dXJuIGl0ZW0uRGlzcGxheU5hbWU7CiAgICAgICAgICB9KS5qb2luKCcsJyk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzMy4kbWVzc2FnZSh7CiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIHRiU2VsZWN0Q2hhbmdlOiBmdW5jdGlvbiB0YlNlbGVjdENoYW5nZShhcnJheSkgewogICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gYXJyYXkucmVjb3JkczsKICAgIH0sCiAgICAvLyDlr7zlh7oKICAgIGhhbmRsZUV4cG9ydDogZnVuY3Rpb24gaGFuZGxlRXhwb3J0KCkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgY29uc29sZS5sb2codGhpcy5pbmZvKTsKICAgICAgRXhwb3J0VGFza0NvZGVEZXRhaWxzKHsKICAgICAgICBQcm9jZXNzX1R5cGU6IHRoaXMuaXNDb20gPyAyIDogdGhpcy5pc1BhcnQgPyAxIDogMywKICAgICAgICAvLyAx6Zu25Lu277yMMuaehOS7tgogICAgICAgIFdvcmtpbmdfVGVhbV9JZDogdGhpcy53b3JrVGVhbUlkLAogICAgICAgIFRhc2tfQ29kZTogdGhpcy50YXNrQ29kZQogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgX3RoaXM0LiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogJ+WvvOWHuuaIkOWKnycsCiAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJwogICAgICAgICAgfSk7CiAgICAgICAgICB3aW5kb3cub3Blbihjb21iaW5lVVJMKF90aGlzNC4kYmFzZVVybCwgcmVzLkRhdGEpLCAnX2JsYW5rJyk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzNC4kbWVzc2FnZSh7CiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgICAvLyBjb25zdCBmaWx0ZXJWYWwgPSB0aGlzLmNvbHVtbnMubWFwKCh2KSA9PiB2LkNvZGUpCiAgICAgIC8vIGNvbnN0IGRhdGEgPSBmb3JtYXRKc29uKGZpbHRlclZhbCwgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbikKICAgICAgLy8gY29uc3QgaGVhZGVyID0gdGhpcy5jb2x1bW5zLm1hcCgodikgPT4gdi5EaXNwbGF5X05hbWUpCiAgICAgIC8vIGltcG9ydCgnQC92ZW5kb3IvRXhwb3J0MkV4Y2VsJykudGhlbigoZXhjZWwpID0+IHsKICAgICAgLy8gICBleGNlbC5leHBvcnRfanNvbl90b19leGNlbCh7CiAgICAgIC8vICAgICBoZWFkZXI6IGhlYWRlciwKICAgICAgLy8gICAgIGRhdGEsCiAgICAgIC8vICAgICBmaWxlbmFtZTogYCR7CiAgICAgIC8vICAgICAgIHRoaXMuaW5mbz8uVGFza19Db2RlIHx8CiAgICAgIC8vICAgICAgICh0aGlzLmlzQ29tID8gJ+aehOS7tuS7u+WKoeWNleivpuaDhScgOiAn6Zu25Lu25Lu75Yqh5Y2V6K+m5oOFJykKICAgICAgLy8gICAgIH1gLAogICAgICAvLyAgICAgYXV0b1dpZHRoOiB0cnVlLAogICAgICAvLyAgICAgYm9va1R5cGU6ICd4bHN4JwogICAgICAvLyAgIH0pCiAgICAgIC8vIH0pCiAgICAgIC8vIGZ1bmN0aW9uIGZvcm1hdEpzb24oZmlsdGVyVmFsLCBqc29uRGF0YSkgewogICAgICAvLyAgIHJldHVybiBqc29uRGF0YS5tYXAoKHYpID0+IGZpbHRlclZhbC5tYXAoKGopID0+IHZbal0pKQogICAgICAvLyB9CiAgICB9LAogICAgZmV0Y2hEYXRhOiBmdW5jdGlvbiBmZXRjaERhdGEocGFnZSkgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKICAgICAgcGFnZSAmJiAodGhpcy5xdWVyeUluZm8uUGFnZSA9IHBhZ2UpOwogICAgICBjb25zb2xlLmxvZyh0aGlzLnF1ZXJ5SW5mbywgJ3RoaXMucXVlcnlJbmZvJyk7CiAgICAgIHRoaXMudGJMb2FkaW5nID0gdHJ1ZTsKICAgICAgR2V0VGVhbVRhc2tEZXRhaWxzKHsKICAgICAgICAvLyAuLi50aGlzLnF1ZXJ5SW5mbywKICAgICAgICBCb21fTGV2ZWw6IHRoaXMucGFnZVR5cGUsCiAgICAgICAgUGFnZTogLTEsCiAgICAgICAgUGFnZVNpemU6IC0xLAogICAgICAgIFByb2Nlc3NfVHlwZTogdGhpcy5pc0NvbSA/IDIgOiB0aGlzLmlzUGFydCA/IDEgOiAzLAogICAgICAgIC8vIDHpm7bku7bvvIwy5p6E5Lu2CiAgICAgICAgV29ya2luZ19UZWFtX0lkOiB0aGlzLndvcmtUZWFtSWQsCiAgICAgICAgVGFza19Db2RlOiB0aGlzLnRhc2tDb2RlLAogICAgICAgIE5leHRfVGVhbV9JZDogdGhpcy5xdWVyeUZvcm0uTmV4dF9UZWFtX0lkLAogICAgICAgIE5leHRfUHJvY2Vzc19JZDogdGhpcy5xdWVyeUZvcm0uTmV4dF9Qcm9jZXNzX0lkCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICBfdGhpczUudGJEYXRhID0gcmVzLkRhdGEuRGF0YS5maWx0ZXIoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgcmV0dXJuIGl0ZW0uQWxsb2NhdGlvbl9Db3VudCAhPT0gMDsKICAgICAgICAgIH0pOwogICAgICAgICAgLy8gdGhpcy50b3RhbCA9IHJlcy5EYXRhLlRvdGFsQ291bnQKICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXM1LiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSkuZmluYWxseShmdW5jdGlvbiAoXykgewogICAgICAgIF90aGlzNS50YkxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlUmVzZXQ6IGZ1bmN0aW9uIGhhbmRsZVJlc2V0KCkgewogICAgICB0aGlzLiRyZWZzWydmb3JtJ10ucmVzZXRGaWVsZHMoKTsKICAgICAgdGhpcy5zZWFyY2goMSk7CiAgICB9LAogICAgZ2V0UHJvY2Vzc09wdGlvbjogZnVuY3Rpb24gZ2V0UHJvY2Vzc09wdGlvbigpIHsKICAgICAgdmFyIF90aGlzNiA9IHRoaXM7CiAgICAgIEdldFByb2Nlc3NMaXN0KHsKICAgICAgICB0eXBlOiB0aGlzLmlzQ29tID8gMSA6IHRoaXMuaXNQYXJ0ID8gMiA6IDMsCiAgICAgICAgLy8gMeaehOS7tu+8jDLpm7bku7YKICAgICAgICBCb21fTGV2ZWw6IHRoaXMucGFnZVR5cGUKICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIF90aGlzNi5wcm9jZXNzT3B0aW9uID0gcmVzLkRhdGE7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzNi4kbWVzc2FnZSh7CiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGdldEFsbFRlYW1PcHRpb246IGZ1bmN0aW9uIGdldEFsbFRlYW1PcHRpb24oKSB7CiAgICAgIHZhciBfdGhpczcgPSB0aGlzOwogICAgICBHZXRXb3JraW5nVGVhbXNQYWdlTGlzdCh7CiAgICAgICAgcGFnZUluZm86IHsKICAgICAgICAgIFBhZ2U6IC0xLAogICAgICAgICAgUGFnZVNpemU6IC0xLAogICAgICAgICAgUGFyYW1ldGVySnNvbjogW10KICAgICAgICB9CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICBfdGhpczcuZ3JvdXBPcHRpb24gPSByZXMuRGF0YS5EYXRhOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpczcuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBnZXRIdG1sOiBmdW5jdGlvbiBnZXRIdG1sKCkgewogICAgICB2YXIgX3RoaXM4ID0gdGhpczsKICAgICAgLy8gJHt0aGlzLnByaW50Q29uZmlnLnNoZWV0TmFtZX0KICAgICAgdmFyIHFyID0gdGhpcy4kcmVmc1sncXJjb2RlUmVmJ107CiAgICAgIHJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbiAocmVzb2x2ZSwgcmVqZWN0KSB7CiAgICAgICAgX3RoaXM4LiRuZXh0VGljayhmdW5jdGlvbiAoXykgewogICAgICAgICAgdmFyIF90aGlzOCRpbmZvOwogICAgICAgICAgdmFyIGNhbnZhcyA9IHFyLiRyZWZzWydxcmNvZGUtdnVlJ107CiAgICAgICAgICB2YXIgZGF0YVVSTCA9IGNhbnZhcy50b0RhdGFVUkwoJ2ltYWdlL3BuZycpOwogICAgICAgICAgX3RoaXM4LnRvcEh0bWwgPSAiXG4gICAgICAgIDxoMSBjbGFzcz1cInRpdGxlXCI+IyIuY29uY2F0KF90aGlzOC5pbmZvLldvcmtpbmdfUHJvY2Vzc19OYW1lIHx8ICcnLCAiIyBcdTUyQTBcdTVERTVcdTRFRkJcdTUyQTFcdTUzNTU8L2gxPlxuICAgICAgICA8ZGl2IGNsYXNzPVwibXktdG9wXCI+XG4gICAgICAgICAgPGRpdiBjbGFzcz1cImxlZnRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3M9XCJteS1saXN0LXJvdyBteS1saXN0LXJvdy1maXJzdFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzPVwibXktbGlzdC1jb2xcIj5cdTk4NzlcdTc2RUVcdTU0MERcdTc5RjAvXHU1MzNBXHU1N0RGXHVGRjFBIikuY29uY2F0KF90aGlzOC5pbmZvLlByb2plY3RfTmFtZSB8fCAnJywgIi8iKS5jb25jYXQoX3RoaXM4LmluZm8uQXJlYV9OYW1lIHx8ICcnLCAiPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3M9XCJteS1saXN0LWNvbFwiPlx1NjM5Mlx1NEVBN1x1NTM1NVx1NTNGN1x1RkYxQSIpLmNvbmNhdChfdGhpczguaW5mby5TY2hkdWxpbmdfQ29kZSB8fCAnJywgIjwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzPVwibXktbGlzdC1jb2xcIj5cdTUyQTBcdTVERTVcdTczRURcdTdFQzRcdUZGMUEiKS5jb25jYXQoX3RoaXM4LmluZm8uV29ya2luZ19UZWFtX05hbWUgfHwgJycsICI8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzcz1cIm15LWxpc3Qtcm93XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3M9XCJteS1saXN0LWNvbFwiPlx1NEVGQlx1NTJBMVx1NEUwQlx1NTM1NVx1NjVGNlx1OTVGNFx1RkYxQSIpLmNvbmNhdChfdGhpczguaW5mby5PcmRlcl9EYXRlIHx8ICcnLCAiPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3M9XCJteS1saXN0LWNvbFwiPlx1NEVGQlx1NTJBMVx1NTM1NVx1NTNGN1x1RkYxQSIpLmNvbmNhdCgoKF90aGlzOCRpbmZvID0gX3RoaXM4LmluZm8pID09PSBudWxsIHx8IF90aGlzOCRpbmZvID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfdGhpczgkaW5mby5UYXNrX0NvZGUpIHx8ICcnLCAiPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3M9XCJteS1saXN0LWNvbFwiPlx1NURFNVx1NUU4Rlx1OEJBMVx1NTIxMlx1NUI4Q1x1NjIxMFx1NjVGNlx1OTVGNFx1RkYxQSIpLmNvbmNhdChfdGhpczguaW5mby5Qcm9jZXNzX0ZpbmlzaF9EYXRlIHx8ICcnLCAiPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzPVwicmlnaHRcIj5cbiAgICAgICAgICAgPGltZyBjbGFzcz1cImNzLWltZ1wiIHNyYz1cIiIpLmNvbmNhdChkYXRhVVJMLCAiXCIgYWx0PVwiXCI+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICAiKTsKICAgICAgICAgIHJlc29sdmUoKTsKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgcHJpbnRFdmVudDogZnVuY3Rpb24gcHJpbnRFdmVudCgpIHsKICAgICAgdmFyIF90aGlzOSA9IHRoaXM7CiAgICAgIHRoaXMuZ2V0SHRtbCgpLnRoZW4oZnVuY3Rpb24gKF8pIHsKICAgICAgICBjb25zb2xlLmxvZyhfdGhpczkucHJpbnRDb25maWcuc2hlZXROYW1lLCAndGhpcy5wcmludENvbmZpZy5zaGVldE5hbWUnKTsKICAgICAgICBfdGhpczkuJHJlZnMueFRhYmxlLnByaW50KHsKICAgICAgICAgIHNoZWV0TmFtZTogX3RoaXM5LnByaW50Q29uZmlnLnNoZWV0TmFtZSwKICAgICAgICAgIHN0eWxlOiBwcmludFN0eWxlLAogICAgICAgICAgbW9kZTogJ3NlbGVjdGVkJywKICAgICAgICAgIGNvbHVtbnM6IF90aGlzOS5wcmludENvbHVtbnMubWFwKGZ1bmN0aW9uICh2KSB7CiAgICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgICAgZmllbGQ6IHYuQ29kZQogICAgICAgICAgICB9OwogICAgICAgICAgfSksCiAgICAgICAgICBiZWZvcmVQcmludE1ldGhvZDogZnVuY3Rpb24gYmVmb3JlUHJpbnRNZXRob2QoX3JlZjIpIHsKICAgICAgICAgICAgdmFyIGNvbnRlbnQgPSBfcmVmMi5jb250ZW50OwogICAgICAgICAgICAvLyDmi6bmiKrmiZPljbDkuYvliY3vvIzov5Tlm57oh6rlrprkuYnnmoQgaHRtbCDlhoXlrrkKICAgICAgICAgICAgdmFyIHJlc3VsdCA9IF90aGlzOS50b3BIdG1sICsgY29udGVudDsKICAgICAgICAgICAgY29uc29sZS5sb2coJ3Jlc3VsdCcsIHJlc3VsdCk7CiAgICAgICAgICAgIHJldHVybiByZXN1bHQ7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZVZpZXc6IGZ1bmN0aW9uIGhhbmRsZVZpZXcocm93KSB7fSwKICAgIGdldElubmVyVGFibGU6IGZ1bmN0aW9uIGdldElubmVyVGFibGUocm93LCBjb2x1bW4pIHsKICAgICAgdmFyIG9iID0gewogICAgICAgIG51bTogMSwKICAgICAgICBkYXRlOiAnMjAyMi0yMy01MicKICAgICAgfTsKICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCA1MDsgaSsrKSB7CiAgICAgICAgdGhpcy5maW5pc2hMaXN0LnB1c2gob2IpOwogICAgICB9CiAgICB9LAogICAgdG9CYWNrOiBmdW5jdGlvbiB0b0JhY2soKSB7CiAgICAgIGNsb3NlVGFnVmlldyh0aGlzLiRzdG9yZSwgdGhpcy4kcm91dGUpOwogICAgfSwKICAgIC8vIOWFs+mXreW8ueeqlwogICAgaGFuZGxlQ2xvc2U6IGZ1bmN0aW9uIGhhbmRsZUNsb3NlKCkgewogICAgICB0aGlzLiRyZWZzLlRyYW5zZmVyRGV0YWlsLnJlc2V0Rm9ybSgpOwogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgdGhpcy5kZXZpY2VEaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICB9LAogICAgLy8g5Y2V5YWD5qC854K55Ye75pe26Ze0CiAgICBjZWxsQ2xpY2tFdmVudDogZnVuY3Rpb24gY2VsbENsaWNrRXZlbnQoX3JlZjMpIHsKICAgICAgdmFyIHJvdyA9IF9yZWYzLnJvdywKICAgICAgICByb3dJbmRleCA9IF9yZWYzLnJvd0luZGV4LAogICAgICAgIGNvbHVtbiA9IF9yZWYzLmNvbHVtbiwKICAgICAgICBjb2x1bW5JbmRleCA9IF9yZWYzLmNvbHVtbkluZGV4OwogICAgfSAvLyBpZiAoY29sdW1uLnByb3BlcnR5ID09PSAiRmluaXNoX0NvdW50IiAmJiByb3cuRmluaXNoX0NvdW50ID4gMCkgewogICAgLy8gICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgLy8gICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAvLyAgICAgdGhpcy4kcmVmcy5UcmFuc2ZlckRldGFpbC5pbml0KHJvdywgdGhpcy5pc0NvbSk7CiAgICAvLyAgIH0pOwogICAgLy8gfQogICAgLAogICAgLy8g5pS55Y+Y5Y2V5YWD5qC85qC35byPCiAgICBjZWxsQ2xhc3NOYW1lOiBmdW5jdGlvbiBjZWxsQ2xhc3NOYW1lKF9yZWY0KSB7CiAgICAgIHZhciByb3cgPSBfcmVmNC5yb3csCiAgICAgICAgcm93SW5kZXggPSBfcmVmNC5yb3dJbmRleCwKICAgICAgICBjb2x1bW4gPSBfcmVmNC5jb2x1bW4sCiAgICAgICAgY29sdW1uSW5kZXggPSBfcmVmNC5jb2x1bW5JbmRleDsKICAgIH0gLy8gaWYgKGNvbHVtbi5wcm9wZXJ0eSA9PT0gIkZpbmlzaF9Db3VudCIpIHsKICAgIC8vICAgcmV0dXJuICJjb2wtYmx1ZSI7CiAgICAvLyB9CiAgICAsCiAgICBoYW5kbGVTdWdnZXN0RGV2aWNlRGlhbG9nOiBmdW5jdGlvbiBoYW5kbGVTdWdnZXN0RGV2aWNlRGlhbG9nKCkgewogICAgICB2YXIgX3RoaXMwID0gdGhpczsKICAgICAgdGhpcy50aXRsZSA9ICJcdTc1MUZcdTRFQTdcdThCQkVcdTU5MDdcdThCRTZcdTYwQzUiOwogICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSAnU3VnZ2VzdERldmljZSc7CiAgICAgIHRoaXMuZFdpZHRoID0gJzgwMHB4JzsKICAgICAgdGhpcy5kZXZpY2VEaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKF8pIHsKICAgICAgICBfdGhpczAuJHJlZnNbJ2RyYWZ0J10uaW5pdERhdGEoKTsKICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["getTbInfo", "GetTeamTaskDetails", "ExportTaskCodeDetails", "GetSuggestDeviceAndRemark", "debounce", "closeTagView", "QrcodeVue", "GetProcessList", "GetWorkingTeamsPageList", "TransferDetail", "combineURL", "mapGetters", "SuggestDevice", "getBomCode", "getBomName", "checkIsUnitPart", "printStyle", "name", "components", "mixins", "data", "_this", "bom<PERSON>ame", "deviceDialogVisible", "dialogVisible", "queryForm", "Next_Process_Id", "Next_Team_Id", "queryInfo", "Page", "PageSize", "tbConfig", "<PERSON>_<PERSON><PERSON>th", "tbLoading", "columns", "multipleSelection", "tbData", "finishList", "processOption", "groupOption", "total", "printColumns", "search", "pageType", "info", "Task_Code", "Project_Name", "Area_Name", "InstallUnit_Name", "Schduling_Code", "Task_Finish_Date", "Finish_Date2", "Order_Date", "Working_Team_Name", "Working_Process_Name", "Process_Start_Date", "Process_Finish_Date", "printConfig", "sheetName", "style", "beforePrintMethod", "_ref", "content", "topHtml", "Tenant_Code", "localStorage", "getItem", "Remark", "eqptInfoList", "eqptInfoListStr", "computed", "_objectSpread", "isCom", "isUnitPart", "isPart", "mounted", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "idx", "wrap", "_callee$", "_context", "prev", "next", "$route", "query", "type", "sent", "workTeamId", "tid", "taskCode", "id", "JSON", "parse", "decodeURIComponent", "other", "getSuggestDeviceAndRemark", "getTableConfig", "findIndex", "item", "Code", "splice", "filter", "column", "fetchData", "getProcessOption", "getAllTeamOption", "getHtml", "stop", "methods", "_this3", "Bom_Level", "then", "res", "IsSucceed", "_res$Data", "_res$Data2", "Data", "map", "DisplayName", "join", "$message", "message", "Message", "tbSelectChange", "array", "records", "handleExport", "_this4", "console", "log", "Process_Type", "Working_Team_Id", "window", "open", "$baseUrl", "page", "_this5", "Allocation_Count", "finally", "_", "handleReset", "$refs", "resetFields", "_this6", "_this7", "pageInfo", "Parameter<PERSON>son", "_this8", "qr", "Promise", "resolve", "reject", "$nextTick", "_this8$info", "canvas", "dataURL", "toDataURL", "concat", "printEvent", "_this9", "xTable", "print", "mode", "v", "field", "_ref2", "result", "handleView", "row", "getInnerTable", "ob", "num", "date", "i", "push", "toBack", "$store", "handleClose", "resetForm", "cellClickEvent", "_ref3", "rowIndex", "columnIndex", "cellClassName", "_ref4", "handleSuggestDeviceDialog", "_this0", "title", "currentComponent", "dWidth", "initData"], "sources": ["src/views/PRO/plan-production/task-list/detail.vue"], "sourcesContent": ["<template>\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\n    <div class=\"top-btn\" @click=\"toBack\">\n      <el-button>返回</el-button>\n    </div>\n    <div class=\"cs-z-page-main-content\">\n      <el-form ref=\"form\" :model=\"queryForm\" inline label-width=\"130px\">\n        <el-row>\n          <el-col :span=\"20\">\n            <el-form-item label=\"排产单号\" prop=\"Schduling_Code\">\n              {{ info.Schduling_Code }}\n            </el-form-item>\n            <el-form-item label=\"任务单号\" prop=\"Task_Code\">\n              {{ info.Task_Code }}\n            </el-form-item>\n            <el-form-item label=\"项目名称\" prop=\"projectId\">\n              {{ info.Project_Name }}\n            </el-form-item>\n            <el-form-item label=\"区域名称\" prop=\"areaId\">\n              {{ info.Area_Name }}\n            </el-form-item>\n            <el-form-item v-if=\"!isVersionFour\" label=\"批次\" prop=\"install\">\n              {{ info.InstallUnit_Name }}\n            </el-form-item>\n            <!-- <br /> -->\n            <el-form-item label=\"任务下达时间\" prop=\"Order_Date\">\n              {{ info.Order_Date || '-' }}\n            </el-form-item>\n            <el-form-item label=\"工序计划开始时间\" prop=\"Process_Start_Date\">\n              {{ info.Process_Start_Date || '-' }}\n            </el-form-item>\n            <el-form-item label=\"工序计划完成时间\" prop=\"Process_Finish_Date\">\n              {{ info.Process_Finish_Date || '-' }}\n            </el-form-item>\n            <el-form-item label=\"完成时间\" prop=\"Finish_Date2\">\n              {{ info.Finish_Date2 || '-' }}\n            </el-form-item>\n            <el-form-item label=\"备注\" prop=\"Remark\">\n              {{ Remark || '-' }}\n            </el-form-item>\n            <el-form-item label=\"建议设备\" prop=\"eqptInfoListStr\">\n              {{ eqptInfoListStr || '-' }}\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\" style=\"margin-top: 12px;\">\n            <qrcode-vue ref=\"qrcodeRef\" :size=\"79\" :value=\"`T=${info.Task_Code}&C=${Tenant_Code}`\" class-name=\"qrcode\" level=\"H\" />\n          </el-col>\n        </el-row>\n        <br>\n        <el-form-item label=\"下道工序\" prop=\"Next_Process_Id\">\n          <el-select\n            v-model=\"queryForm.Next_Process_Id\"\n            clearable\n            placeholder=\"请选择\"\n            class=\"w100\"\n          >\n            <el-option\n              v-for=\"item in processOption\"\n              :key=\"item.Id\"\n              :label=\"item.Name\"\n              :value=\"item.Id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"下道班组\" prop=\"Next_Team_Id\">\n          <el-select\n            v-model=\"queryForm.Next_Team_Id\"\n            clearable\n            placeholder=\"请选择\"\n            class=\"w100\"\n          >\n            <el-option\n              v-for=\"item in groupOption\"\n              :key=\"item.Id\"\n              :label=\"item.Name\"\n              :value=\"item.Id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"search(1)\">搜索</el-button>\n          <el-button @click=\"handleReset()\">重置</el-button>\n          <el-button\n            :disabled=\"!multipleSelection.length\"\n            @click=\"printEvent()\"\n          >打印\n          </el-button>\n          <el-button\n            type=\"success\"\n            @click=\"handleExport\"\n          >导出\n          </el-button>\n          <el-button type=\"primary\" @click=\"handleSuggestDeviceDialog()\">设备详情</el-button>\n        </el-form-item>\n      </el-form>\n      <!--      <el-divider />-->\n      <div class=\"tb-x\">\n        <vxe-table\n          ref=\"xTable\"\n          :empty-render=\"{name: 'NotData'}\"\n          show-header-overflow\n          :print-config=\"printConfig\"\n          :row-config=\"{ isCurrent: true, isHover: true }\"\n          class=\"cs-vxe-table\"\n          align=\"left\"\n          height=\"auto\"\n          show-overflow\n          :loading=\"tbLoading\"\n          stripe\n          size=\"medium\"\n          :data=\"tbData\"\n          resizable\n          :tooltip-config=\"{ enterable: true }\"\n          :cell-class-name=\"cellClassName\"\n          @checkbox-all=\"tbSelectChange\"\n          @checkbox-change=\"tbSelectChange\"\n          @cell-click=\"cellClickEvent\"\n        >\n          <vxe-column type=\"checkbox\" align=\"center\" />\n          <template v-for=\"item in columns\">\n            <vxe-column\n              :key=\"item.Id\"\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n              show-overflow=\"tooltip\"\n              sortable\n              :align=\"item.Align\"\n              :field=\"item.Code\"\n              :title=\"item.Display_Name\"\n              :min-width=\"item.Width\"\n            />\n          </template>\n        </vxe-table>\n      </div>\n    </div>\n    <el-dialog\n      v-dialogDrag\n      class=\"plm-custom-dialog\"\n      :title=\"title\"\n      :visible.sync=\"deviceDialogVisible\"\n      :width=\"dWidth\"\n      :close-on-click-modal=\"false\"\n      top=\"10vh\"\n      @close=\"handleClose\"\n    >\n      <component\n        :is=\"currentComponent\"\n        ref=\"content\"\n        @close=\"handleClose\"\n      />\n    </el-dialog>\n    <el-dialog\n      v-dialogDrag\n      class=\"plm-custom-dialog\"\n      title=\"转移详情\"\n      :visible.sync=\"dialogVisible\"\n      width=\"576px\"\n      @close=\"handleClose\"\n    >\n      <TransferDetail ref=\"TransferDetail\" />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport getTbInfo from '@/mixins/PRO/get-table-info'\nimport { GetTeamTaskDetails, ExportTaskCodeDetails, GetSuggestDeviceAndRemark } from '@/api/PRO/production-task'\nimport { debounce, closeTagView } from '@/utils'\nimport QrcodeVue from 'qrcode.vue'\nimport {\n  GetProcessList,\n  GetWorkingTeamsPageList\n} from '@/api/PRO/technology-lib'\n// import { GetProcessList } from '@/api/PRO/technology-lib'\nimport TransferDetail from './transferDetail'\n// import { Export } from 'vxe-table'\nimport { combineURL } from '@/utils'\nimport { mapGetters } from 'vuex'\nimport SuggestDevice from './suggestDevice'\nimport { getBomCode, getBomName, checkIsUnitPart } from '@/views/PRO/bom-setting/utils'\nconst printStyle = `\n        .title {\n          text-align: center;\n        }\n        .is--print{\n          box-sizing: border-box;\n          width:95% !important;\n          margin:0 auto !important;\n        }\n        .my-list-row {\n          display: inline-block;\n          width: 100%;\n          margin-left:3%;\n        }\n        .my-list-row-first {\n          margin-bottom: 10px;\n        }\n        .my-list-col {\n          width:30%;\n          display: inline-block;\n          float: left;\n          margin-right: 1%;\n          word-wrap:break-word;\n          word-break:normal;\n        }\n        .left{\n          flex:1;\n        }\n        .my-top {\n          display:flex;\n          font-size: 12px;\n          margin-bottom: 5px;\n        }\n        .qrcode{\n          margin-right:10px\n        }\n        .cs-img{\n          position:relative;\n          right:30px\n        }\n        `\n\nexport default {\n  name: 'PROTaskListDetail',\n  components: {\n    TransferDetail,\n    QrcodeVue,\n    SuggestDevice\n  },\n  mixins: [getTbInfo],\n  data() {\n    return {\n      bomName: '',\n      deviceDialogVisible: false,\n      dialogVisible: false,\n      queryForm: {\n        // Project_Id: '',\n        // Area_Id: '',\n        // InstallUnit_Id: '',\n        Next_Process_Id: '',\n        Next_Team_Id: ''\n      },\n      queryInfo: {\n        Page: 1,\n        PageSize: -1\n      },\n      tbConfig: {\n        Op_Width: 120\n      },\n      tbLoading: false,\n      columns: [],\n      multipleSelection: [],\n      tbData: [],\n      finishList: [],\n      processOption: [],\n      groupOption: [],\n      total: 0,\n      printColumns: [],\n      search: () => ({}),\n      pageType: '',\n      info: {\n        Task_Code: '',\n        Project_Name: '',\n        Area_Name: '',\n        InstallUnit_Name: '',\n        Schduling_Code: '',\n        Task_Finish_Date: '',\n        Finish_Date2: '',\n        Order_Date: '',\n        Working_Team_Name: '',\n        Working_Process_Name: '',\n        Process_Start_Date: '',\n        Process_Finish_Date: ''\n      },\n      printConfig: {\n        sheetName: '任务单详情',\n        style: printStyle,\n        beforePrintMethod: ({ content }) => {\n          return this.topHtml + content\n        }\n      },\n      Tenant_Code: localStorage.getItem('tenant'),\n      Remark: '',\n      eqptInfoList: [],\n      eqptInfoListStr: ''\n    }\n  },\n  computed: {\n    ...mapGetters('tenant', ['isVersionFour']),\n    isCom() {\n      return this.pageType === getBomCode('-1')\n    },\n    isUnitPart() {\n      return checkIsUnitPart(this.pageType)\n    },\n    isPart() {\n      return this.pageType === getBomCode('0')\n    }\n  },\n  async mounted() {\n    this.pageType = this.$route.query.type\n    this.bomName = await getBomName(this.pageType)\n    this.workTeamId = this.$route.query.tid\n    this.taskCode = this.$route.query.id\n    this.info = JSON.parse(decodeURIComponent(this.$route.query.other))\n    await this.getSuggestDeviceAndRemark()\n    await this.getTableConfig(this.isCom ? 'PROComTaskListDetail' : this.isUnitPart ? 'PROUnitPartTaskListDetail' : 'PROPartTaskListDetail')\n    if (this.isCom) {\n      const idx = this.columns.findIndex((item) => item.Code === 'Part_Code')\n      idx !== -1 && this.columns.splice(idx, 1)\n\n      this.printColumns = this.columns.filter(column => column.Code !== 'Comp_Description')\n    }\n    if (this.isUnitPart) {\n      this.printColumns = this.columns.filter(column => column.Code !== 'Project_Name' && column.Code !== 'Area_Name' && column.Code !== 'Finish_Count' && column.Code !== 'Finish_Weight' && column.Code !== 'Comp_Description')\n    }\n    if (this.isPart) {\n      this.printColumns = this.columns.filter(column => column.Code !== 'Project_Name' && column.Code !== 'Area_Name' && column.Code !== 'Finish_Count' && column.Code !== 'Finish_Weight' && column.Code !== 'Comp_Description')\n    }\n\n    // else {\n    //   const idx = this.columns.findIndex((item) => item.Code === 'Comp_Code')\n    //   idx !== -1 && this.columns.splice(idx, 1)\n    // }\n    this.search = debounce(this.fetchData, 800, true)\n    this.fetchData()\n    this.getProcessOption()\n    this.getAllTeamOption()\n    this.getHtml()\n  },\n  methods: {\n    getSuggestDeviceAndRemark() {\n      GetSuggestDeviceAndRemark({\n        Bom_Level: this.pageType,\n        Task_Code: this.info.Task_Code\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.Remark = res.Data?.Remark\n          this.eqptInfoList = res.Data?.eqptInfoList || []\n          this.eqptInfoListStr = this.eqptInfoList.map(item => item.DisplayName).join(',')\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    tbSelectChange(array) {\n      this.multipleSelection = array.records\n    },\n    // 导出\n    handleExport() {\n      console.log(this.info)\n      ExportTaskCodeDetails({\n        Process_Type: this.isCom ? 2 : this.isPart ? 1 : 3, // 1零件，2构件\n        Working_Team_Id: this.workTeamId,\n        Task_Code: this.taskCode\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            message: '导出成功',\n            type: 'success'\n          })\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n      // const filterVal = this.columns.map((v) => v.Code)\n      // const data = formatJson(filterVal, this.multipleSelection)\n      // const header = this.columns.map((v) => v.Display_Name)\n      // import('@/vendor/Export2Excel').then((excel) => {\n      //   excel.export_json_to_excel({\n      //     header: header,\n      //     data,\n      //     filename: `${\n      //       this.info?.Task_Code ||\n      //       (this.isCom ? '构件任务单详情' : '零件任务单详情')\n      //     }`,\n      //     autoWidth: true,\n      //     bookType: 'xlsx'\n      //   })\n      // })\n      // function formatJson(filterVal, jsonData) {\n      //   return jsonData.map((v) => filterVal.map((j) => v[j]))\n      // }\n    },\n    fetchData(page) {\n      page && (this.queryInfo.Page = page)\n      console.log(this.queryInfo, 'this.queryInfo')\n      this.tbLoading = true\n      GetTeamTaskDetails({\n        // ...this.queryInfo,\n        Bom_Level: this.pageType,\n        Page: -1,\n        PageSize: -1,\n        Process_Type: this.isCom ? 2 : this.isPart ? 1 : 3, // 1零件，2构件\n        Working_Team_Id: this.workTeamId,\n        Task_Code: this.taskCode,\n        Next_Team_Id: this.queryForm.Next_Team_Id,\n        Next_Process_Id: this.queryForm.Next_Process_Id\n      })\n        .then((res) => {\n          if (res.IsSucceed) {\n            this.tbData = res.Data.Data.filter(item => {\n              return item.Allocation_Count !== 0\n            })\n            // this.total = res.Data.TotalCount\n          } else {\n            this.$message({\n              message: res.Message,\n              type: 'error'\n            })\n          }\n        })\n        .finally((_) => {\n          this.tbLoading = false\n        })\n    },\n    handleReset() {\n      this.$refs['form'].resetFields()\n      this.search(1)\n    },\n    getProcessOption() {\n      GetProcessList({\n        type: this.isCom ? 1 : this.isPart ? 2 : 3, // 1构件，2零件\n        Bom_Level: this.pageType\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.processOption = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    getAllTeamOption() {\n      GetWorkingTeamsPageList({\n        pageInfo: { Page: -1, PageSize: -1, ParameterJson: [] }\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.groupOption = res.Data.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    getHtml() {\n      // ${this.printConfig.sheetName}\n      const qr = this.$refs['qrcodeRef']\n      return new Promise((resolve, reject) => {\n        this.$nextTick(_ => {\n          const canvas = qr.$refs['qrcode-vue']\n          const dataURL = canvas.toDataURL('image/png')\n          this.topHtml = `\n        <h1 class=\"title\">#${this.info.Working_Process_Name || ''}# 加工任务单</h1>\n        <div class=\"my-top\">\n          <div class=\"left\">\n            <div class=\"my-list-row my-list-row-first\">\n              <div class=\"my-list-col\">项目名称/区域：${this.info.Project_Name || ''}/${this.info.Area_Name || ''}</div>\n              <div class=\"my-list-col\">排产单号：${this.info.Schduling_Code || ''}</div>\n              <div class=\"my-list-col\">加工班组：${this.info.Working_Team_Name || ''}</div>\n            </div>\n            <div class=\"my-list-row\">\n              <div class=\"my-list-col\">任务下单时间：${this.info.Order_Date || ''}</div>\n              <div class=\"my-list-col\">任务单号：${this.info?.Task_Code || ''}</div>\n              <div class=\"my-list-col\">工序计划完成时间：${this.info.Process_Finish_Date || ''}</div>\n            </div>\n          </div>\n          <div class=\"right\">\n           <img class=\"cs-img\" src=\"${dataURL}\" alt=\"\">\n          </div>\n        </div>\n        `\n          resolve()\n        })\n      })\n    },\n    printEvent() {\n      this.getHtml().then((_) => {\n        console.log(this.printConfig.sheetName, 'this.printConfig.sheetName')\n        this.$refs.xTable.print({\n          sheetName: this.printConfig.sheetName,\n          style: printStyle,\n          mode: 'selected',\n          columns: this.printColumns.map((v) => {\n            return {\n              field: v.Code\n            }\n          }),\n          beforePrintMethod: ({ content }) => {\n            // 拦截打印之前，返回自定义的 html 内容\n            const result = this.topHtml + content\n            console.log('result', result)\n            return result\n          }\n        })\n      })\n    },\n    handleView(row) {\n    },\n    getInnerTable(row, column) {\n      const ob = {\n        num: 1,\n        date: '2022-23-52'\n      }\n      for (let i = 0; i < 50; i++) {\n        this.finishList.push(ob)\n      }\n    },\n    toBack() {\n      closeTagView(this.$store, this.$route)\n    },\n\n    // 关闭弹窗\n    handleClose() {\n      this.$refs.TransferDetail.resetForm()\n      this.dialogVisible = false\n      this.deviceDialogVisible = false\n    },\n\n    // 单元格点击时间\n    cellClickEvent({ row, rowIndex, column, columnIndex }) {\n      // if (column.property === \"Finish_Count\" && row.Finish_Count > 0) {\n      //   this.dialogVisible = true;\n      //   this.$nextTick(() => {\n      //     this.$refs.TransferDetail.init(row, this.isCom);\n      //   });\n      // }\n    },\n\n    // 改变单元格样式\n    cellClassName({ row, rowIndex, column, columnIndex }) {\n      // if (column.property === \"Finish_Count\") {\n      //   return \"col-blue\";\n      // }\n    },\n\n    handleSuggestDeviceDialog() {\n      this.title = `生产设备详情`\n\n      this.currentComponent = 'SuggestDevice'\n      this.dWidth = '800px'\n      this.deviceDialogVisible = true\n\n      this.$nextTick(_ => {\n        this.$refs['draft'].initData()\n      })\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.el-divider {\n  margin: 0 0 10px;\n}\n\n.tb-x {\n  flex: 1;\n  overflow:auto;\n\n  ::v-deep {\n    .cs-vxe-table .vxe-body--column.col-blue {\n      color: #298dff;\n      cursor: pointer;\n    }\n  }\n}\n\n.cs-z-flex-pd16-wrap {\n  padding-top: 50px;\n\n  .top-btn {\n    position: absolute;\n    top: 12px;\n    left: 20px;\n    z-index: 99;\n\n    .el-button {\n      background-color: #f7f8f9;\n    }\n  }\n\n  .cs-z-page-main-content {\n    overflow:hidden;\n    ::v-deep {\n      .el-form-item__content {\n        min-width: 200px;\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoKA,OAAAA,SAAA;AACA,SAAAC,kBAAA,EAAAC,qBAAA,EAAAC,yBAAA;AACA,SAAAC,QAAA,EAAAC,YAAA;AACA,OAAAC,SAAA;AACA,SACAC,cAAA,EACAC,uBAAA,QACA;AACA;AACA,OAAAC,cAAA;AACA;AACA,SAAAC,UAAA;AACA,SAAAC,UAAA;AACA,OAAAC,aAAA;AACA,SAAAC,UAAA,EAAAC,UAAA,EAAAC,eAAA;AACA,IAAAC,UAAA,+4BAwCA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAT,cAAA,EAAAA,cAAA;IACAH,SAAA,EAAAA,SAAA;IACAM,aAAA,EAAAA;EACA;EACAO,MAAA,GAAAnB,SAAA;EACAoB,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,OAAA;MACAC,mBAAA;MACAC,aAAA;MACAC,SAAA;QACA;QACA;QACA;QACAC,eAAA;QACAC,YAAA;MACA;MACAC,SAAA;QACAC,IAAA;QACAC,QAAA;MACA;MACAC,QAAA;QACAC,QAAA;MACA;MACAC,SAAA;MACAC,OAAA;MACAC,iBAAA;MACAC,MAAA;MACAC,UAAA;MACAC,aAAA;MACAC,WAAA;MACAC,KAAA;MACAC,YAAA;MACAC,MAAA,WAAAA,OAAA;QAAA;MAAA;MACAC,QAAA;MACAC,IAAA;QACAC,SAAA;QACAC,YAAA;QACAC,SAAA;QACAC,gBAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,YAAA;QACAC,UAAA;QACAC,iBAAA;QACAC,oBAAA;QACAC,kBAAA;QACAC,mBAAA;MACA;MACAC,WAAA;QACAC,SAAA;QACAC,KAAA,EAAA3C,UAAA;QACA4C,iBAAA,WAAAA,kBAAAC,IAAA;UAAA,IAAAC,OAAA,GAAAD,IAAA,CAAAC,OAAA;UACA,OAAAzC,KAAA,CAAA0C,OAAA,GAAAD,OAAA;QACA;MACA;MACAE,WAAA,EAAAC,YAAA,CAAAC,OAAA;MACAC,MAAA;MACAC,YAAA;MACAC,eAAA;IACA;EACA;EACAC,QAAA,EAAAC,aAAA,CAAAA,aAAA,KACA5D,UAAA;IACA6D,KAAA,WAAAA,MAAA;MACA,YAAA7B,QAAA,KAAA9B,UAAA;IACA;IACA4D,UAAA,WAAAA,WAAA;MACA,OAAA1D,eAAA,MAAA4B,QAAA;IACA;IACA+B,MAAA,WAAAA,OAAA;MACA,YAAA/B,QAAA,KAAA9B,UAAA;IACA;EAAA,EACA;EACA8D,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAAC,GAAA;MAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACAV,MAAA,CAAAjC,QAAA,GAAAiC,MAAA,CAAAW,MAAA,CAAAC,KAAA,CAAAC,IAAA;YAAAL,QAAA,CAAAE,IAAA;YAAA,OACAxE,UAAA,CAAA8D,MAAA,CAAAjC,QAAA;UAAA;YAAAiC,MAAA,CAAAtD,OAAA,GAAA8D,QAAA,CAAAM,IAAA;YACAd,MAAA,CAAAe,UAAA,GAAAf,MAAA,CAAAW,MAAA,CAAAC,KAAA,CAAAI,GAAA;YACAhB,MAAA,CAAAiB,QAAA,GAAAjB,MAAA,CAAAW,MAAA,CAAAC,KAAA,CAAAM,EAAA;YACAlB,MAAA,CAAAhC,IAAA,GAAAmD,IAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAArB,MAAA,CAAAW,MAAA,CAAAC,KAAA,CAAAU,KAAA;YAAAd,QAAA,CAAAE,IAAA;YAAA,OACAV,MAAA,CAAAuB,yBAAA;UAAA;YAAAf,QAAA,CAAAE,IAAA;YAAA,OACAV,MAAA,CAAAwB,cAAA,CAAAxB,MAAA,CAAAJ,KAAA,4BAAAI,MAAA,CAAAH,UAAA;UAAA;YACA,IAAAG,MAAA,CAAAJ,KAAA;cACAS,GAAA,GAAAL,MAAA,CAAA1C,OAAA,CAAAmE,SAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAAC,IAAA;cAAA;cACAtB,GAAA,WAAAL,MAAA,CAAA1C,OAAA,CAAAsE,MAAA,CAAAvB,GAAA;cAEAL,MAAA,CAAAnC,YAAA,GAAAmC,MAAA,CAAA1C,OAAA,CAAAuE,MAAA,WAAAC,MAAA;gBAAA,OAAAA,MAAA,CAAAH,IAAA;cAAA;YACA;YACA,IAAA3B,MAAA,CAAAH,UAAA;cACAG,MAAA,CAAAnC,YAAA,GAAAmC,MAAA,CAAA1C,OAAA,CAAAuE,MAAA,WAAAC,MAAA;gBAAA,OAAAA,MAAA,CAAAH,IAAA,uBAAAG,MAAA,CAAAH,IAAA,oBAAAG,MAAA,CAAAH,IAAA,uBAAAG,MAAA,CAAAH,IAAA,wBAAAG,MAAA,CAAAH,IAAA;cAAA;YACA;YACA,IAAA3B,MAAA,CAAAF,MAAA;cACAE,MAAA,CAAAnC,YAAA,GAAAmC,MAAA,CAAA1C,OAAA,CAAAuE,MAAA,WAAAC,MAAA;gBAAA,OAAAA,MAAA,CAAAH,IAAA,uBAAAG,MAAA,CAAAH,IAAA,oBAAAG,MAAA,CAAAH,IAAA,uBAAAG,MAAA,CAAAH,IAAA,wBAAAG,MAAA,CAAAH,IAAA;cAAA;YACA;;YAEA;YACA;YACA;YACA;YACA3B,MAAA,CAAAlC,MAAA,GAAAtC,QAAA,CAAAwE,MAAA,CAAA+B,SAAA;YACA/B,MAAA,CAAA+B,SAAA;YACA/B,MAAA,CAAAgC,gBAAA;YACAhC,MAAA,CAAAiC,gBAAA;YACAjC,MAAA,CAAAkC,OAAA;UAAA;UAAA;YAAA,OAAA1B,QAAA,CAAA2B,IAAA;QAAA;MAAA,GAAA/B,OAAA;IAAA;EACA;EACAgC,OAAA;IACAb,yBAAA,WAAAA,0BAAA;MAAA,IAAAc,MAAA;MACA9G,yBAAA;QACA+G,SAAA,OAAAvE,QAAA;QACAE,SAAA,OAAAD,IAAA,CAAAC;MACA,GAAAsE,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UAAA,IAAAC,SAAA,EAAAC,UAAA;UACAN,MAAA,CAAA9C,MAAA,IAAAmD,SAAA,GAAAF,GAAA,CAAAI,IAAA,cAAAF,SAAA,uBAAAA,SAAA,CAAAnD,MAAA;UACA8C,MAAA,CAAA7C,YAAA,KAAAmD,UAAA,GAAAH,GAAA,CAAAI,IAAA,cAAAD,UAAA,uBAAAA,UAAA,CAAAnD,YAAA;UACA6C,MAAA,CAAA5C,eAAA,GAAA4C,MAAA,CAAA7C,YAAA,CAAAqD,GAAA,WAAAnB,IAAA;YAAA,OAAAA,IAAA,CAAAoB,WAAA;UAAA,GAAAC,IAAA;QACA;UACAV,MAAA,CAAAW,QAAA;YACAC,OAAA,EAAAT,GAAA,CAAAU,OAAA;YACArC,IAAA;UACA;QACA;MACA;IACA;IACAsC,cAAA,WAAAA,eAAAC,KAAA;MACA,KAAA7F,iBAAA,GAAA6F,KAAA,CAAAC,OAAA;IACA;IACA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACAC,OAAA,CAAAC,GAAA,MAAAzF,IAAA;MACA1C,qBAAA;QACAoI,YAAA,OAAA9D,KAAA,YAAAE,MAAA;QAAA;QACA6D,eAAA,OAAA5C,UAAA;QACA9C,SAAA,OAAAgD;MACA,GAAAsB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAc,MAAA,CAAAP,QAAA;YACAC,OAAA;YACApC,IAAA;UACA;UACA+C,MAAA,CAAAC,IAAA,CAAA/H,UAAA,CAAAyH,MAAA,CAAAO,QAAA,EAAAtB,GAAA,CAAAI,IAAA;QACA;UACAW,MAAA,CAAAP,QAAA;YACAC,OAAA,EAAAT,GAAA,CAAAU,OAAA;YACArC,IAAA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAkB,SAAA,WAAAA,UAAAgC,IAAA;MAAA,IAAAC,MAAA;MACAD,IAAA,UAAA/G,SAAA,CAAAC,IAAA,GAAA8G,IAAA;MACAP,OAAA,CAAAC,GAAA,MAAAzG,SAAA;MACA,KAAAK,SAAA;MACAhC,kBAAA;QACA;QACAiH,SAAA,OAAAvE,QAAA;QACAd,IAAA;QACAC,QAAA;QACAwG,YAAA,OAAA9D,KAAA,YAAAE,MAAA;QAAA;QACA6D,eAAA,OAAA5C,UAAA;QACA9C,SAAA,OAAAgD,QAAA;QACAlE,YAAA,OAAAF,SAAA,CAAAE,YAAA;QACAD,eAAA,OAAAD,SAAA,CAAAC;MACA,GACAyF,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAuB,MAAA,CAAAxG,MAAA,GAAAgF,GAAA,CAAAI,IAAA,CAAAA,IAAA,CAAAf,MAAA,WAAAH,IAAA;YACA,OAAAA,IAAA,CAAAuC,gBAAA;UACA;UACA;QACA;UACAD,MAAA,CAAAhB,QAAA;YACAC,OAAA,EAAAT,GAAA,CAAAU,OAAA;YACArC,IAAA;UACA;QACA;MACA,GACAqD,OAAA,WAAAC,CAAA;QACAH,MAAA,CAAA3G,SAAA;MACA;IACA;IACA+G,WAAA,WAAAA,YAAA;MACA,KAAAC,KAAA,SAAAC,WAAA;MACA,KAAAxG,MAAA;IACA;IACAkE,gBAAA,WAAAA,iBAAA;MAAA,IAAAuC,MAAA;MACA5I,cAAA;QACAkF,IAAA,OAAAjB,KAAA,YAAAE,MAAA;QAAA;QACAwC,SAAA,OAAAvE;MACA,GAAAwE,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA8B,MAAA,CAAA7G,aAAA,GAAA8E,GAAA,CAAAI,IAAA;QACA;UACA2B,MAAA,CAAAvB,QAAA;YACAC,OAAA,EAAAT,GAAA,CAAAU,OAAA;YACArC,IAAA;UACA;QACA;MACA;IACA;IACAoB,gBAAA,WAAAA,iBAAA;MAAA,IAAAuC,MAAA;MACA5I,uBAAA;QACA6I,QAAA;UAAAxH,IAAA;UAAAC,QAAA;UAAAwH,aAAA;QAAA;MACA,GAAAnC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA+B,MAAA,CAAA7G,WAAA,GAAA6E,GAAA,CAAAI,IAAA,CAAAA,IAAA;QACA;UACA4B,MAAA,CAAAxB,QAAA;YACAC,OAAA,EAAAT,GAAA,CAAAU,OAAA;YACArC,IAAA;UACA;QACA;MACA;IACA;IACAqB,OAAA,WAAAA,QAAA;MAAA,IAAAyC,MAAA;MACA;MACA,IAAAC,EAAA,QAAAP,KAAA;MACA,WAAAQ,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACAJ,MAAA,CAAAK,SAAA,WAAAb,CAAA;UAAA,IAAAc,WAAA;UACA,IAAAC,MAAA,GAAAN,EAAA,CAAAP,KAAA;UACA,IAAAc,OAAA,GAAAD,MAAA,CAAAE,SAAA;UACAT,MAAA,CAAAxF,OAAA,qCAAAkG,MAAA,CACAV,MAAA,CAAA3G,IAAA,CAAAU,oBAAA,kQAAA2G,MAAA,CAIAV,MAAA,CAAA3G,IAAA,CAAAE,YAAA,aAAAmH,MAAA,CAAAV,MAAA,CAAA3G,IAAA,CAAAG,SAAA,2FAAAkH,MAAA,CACAV,MAAA,CAAA3G,IAAA,CAAAK,cAAA,2FAAAgH,MAAA,CACAV,MAAA,CAAA3G,IAAA,CAAAS,iBAAA,oKAAA4G,MAAA,CAGAV,MAAA,CAAA3G,IAAA,CAAAQ,UAAA,2FAAA6G,MAAA,CACA,EAAAJ,WAAA,GAAAN,MAAA,CAAA3G,IAAA,cAAAiH,WAAA,uBAAAA,WAAA,CAAAhH,SAAA,oHAAAoH,MAAA,CACAV,MAAA,CAAA3G,IAAA,CAAAY,mBAAA,kIAAAyG,MAAA,CAIAF,OAAA,6DAGA;UACAL,OAAA;QACA;MACA;IACA;IACAQ,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAArD,OAAA,GAAAK,IAAA,WAAA4B,CAAA;QACAX,OAAA,CAAAC,GAAA,CAAA8B,MAAA,CAAA1G,WAAA,CAAAC,SAAA;QACAyG,MAAA,CAAAlB,KAAA,CAAAmB,MAAA,CAAAC,KAAA;UACA3G,SAAA,EAAAyG,MAAA,CAAA1G,WAAA,CAAAC,SAAA;UACAC,KAAA,EAAA3C,UAAA;UACAsJ,IAAA;UACApI,OAAA,EAAAiI,MAAA,CAAA1H,YAAA,CAAAgF,GAAA,WAAA8C,CAAA;YACA;cACAC,KAAA,EAAAD,CAAA,CAAAhE;YACA;UACA;UACA3C,iBAAA,WAAAA,kBAAA6G,KAAA;YAAA,IAAA3G,OAAA,GAAA2G,KAAA,CAAA3G,OAAA;YACA;YACA,IAAA4G,MAAA,GAAAP,MAAA,CAAApG,OAAA,GAAAD,OAAA;YACAsE,OAAA,CAAAC,GAAA,WAAAqC,MAAA;YACA,OAAAA,MAAA;UACA;QACA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAC,GAAA,GACA;IACAC,aAAA,WAAAA,cAAAD,GAAA,EAAAlE,MAAA;MACA,IAAAoE,EAAA;QACAC,GAAA;QACAC,IAAA;MACA;MACA,SAAAC,CAAA,MAAAA,CAAA,OAAAA,CAAA;QACA,KAAA5I,UAAA,CAAA6I,IAAA,CAAAJ,EAAA;MACA;IACA;IACAK,MAAA,WAAAA,OAAA;MACA9K,YAAA,MAAA+K,MAAA,OAAA7F,MAAA;IACA;IAEA;IACA8F,WAAA,WAAAA,YAAA;MACA,KAAApC,KAAA,CAAAxI,cAAA,CAAA6K,SAAA;MACA,KAAA9J,aAAA;MACA,KAAAD,mBAAA;IACA;IAEA;IACAgK,cAAA,WAAAA,eAAAC,KAAA;MAAA,IAAAZ,GAAA,GAAAY,KAAA,CAAAZ,GAAA;QAAAa,QAAA,GAAAD,KAAA,CAAAC,QAAA;QAAA/E,MAAA,GAAA8E,KAAA,CAAA9E,MAAA;QAAAgF,WAAA,GAAAF,KAAA,CAAAE,WAAA;IAOA,EANA;IACA;IACA;IACA;IACA;IACA;IAAA;IAGA;IACAC,aAAA,WAAAA,cAAAC,KAAA;MAAA,IAAAhB,GAAA,GAAAgB,KAAA,CAAAhB,GAAA;QAAAa,QAAA,GAAAG,KAAA,CAAAH,QAAA;QAAA/E,MAAA,GAAAkF,KAAA,CAAAlF,MAAA;QAAAgF,WAAA,GAAAE,KAAA,CAAAF,WAAA;IAIA,EAHA;IACA;IACA;IAAA;IAGAG,yBAAA,WAAAA,0BAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA;MAEA,KAAAC,gBAAA;MACA,KAAAC,MAAA;MACA,KAAA1K,mBAAA;MAEA,KAAAqI,SAAA,WAAAb,CAAA;QACA+C,MAAA,CAAA7C,KAAA,UAAAiD,QAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
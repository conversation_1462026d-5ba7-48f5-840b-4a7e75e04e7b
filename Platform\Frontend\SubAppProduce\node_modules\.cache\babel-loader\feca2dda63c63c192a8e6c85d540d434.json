{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-product-type\\component\\CompanyAdd.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-product-type\\component\\CompanyAdd.vue", "mtime": 1757468128032}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetCompTypeTree", "GetPartTypeTree", "GetBOMInfo", "components", "props", "typeCode", "type", "String", "default", "typeId", "data", "treeData", "loading", "currentNodeKey", "activeType", "bomList", "mounted", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "_yield$GetBOMInfo", "list", "wrap", "_callee$", "_context", "prev", "next", "sent", "fetchData", "stop", "methods", "_this2", "_callee2", "res", "_callee2$", "_context2", "professional", "professionalId", "partGrade", "toString", "IsSucceed", "Data", "handleNodeClick", "node", "console", "log", "handleClick", "tab", "event", "name", "handleAddToList", "handleAdd", "handleCancel", "$emit"], "sources": ["src/views/PRO/project-config/project-product-type/component/CompanyAdd.vue"], "sourcesContent": ["<template>\r\n  <div class=\"company-add-container\">\r\n    <div class=\"title\">\r\n      <el-button type=\"primary\" @click=\"handleAddToList\">加入列表</el-button>\r\n    </div>\r\n    <el-tabs v-model=\"activeType\" type=\"card\" @tab-click=\"handleClick\">\r\n      <el-tab-pane v-for=\"item in bomList\" :key=\"item.Code\" :label=\"item.Display_Name\" :name=\"item.Code\" />\r\n    </el-tabs>\r\n    <div class=\"tree-container\">\r\n      <el-tree\r\n        ref=\"tree\"\r\n        v-loading=\"loading\"\r\n        :current-node-key=\"currentNodeKey\"\r\n        element-loading-text=\"加载中\"\r\n        element-loading-spinner=\"el-icon-loading\"\r\n        empty-text=\"暂无数据\"\r\n        highlight-current\r\n        show-checkbox\r\n        node-key=\"Id\"\r\n        default-expand-all\r\n        :expand-on-click-node=\"false\"\r\n        :data=\"treeData\"\r\n        :props=\"{\r\n          label:'Label',\r\n          children:'Children'\r\n        }\"\r\n        @node-click=\"handleNodeClick\"\r\n      >\r\n        <span slot-scope=\"{ node, data }\" class=\"custom-tree-node\">\r\n          <svg-icon\r\n            :icon-class=\"\r\n              node.expanded ? 'icon-folder-open' : 'icon-folder'\r\n            \"\r\n            class-name=\"class-icon\"\r\n          />\r\n          <span class=\"cs-label\" :title=\"node.label\">{{ node.label }}</span>\r\n        </span>\r\n      </el-tree>\r\n    </div>\r\n    <footer>\r\n      <el-button @click=\"handleCancel\">取 消</el-button>\r\n      <el-button type=\"primary\" @click=\"handleAdd\">确 定</el-button>\r\n    </footer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetCompTypeTree } from '@/api/PRO/component-type'\r\nimport { GetPartTypeTree } from '@/api/PRO/partType'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\nexport default {\r\n  components: {\r\n\r\n  },\r\n  props: {\r\n    typeCode: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    typeId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      treeData: [],\r\n      loading: true,\r\n      currentNodeKey: '',\r\n      activeType: '-1',\r\n      bomList: []\r\n    }\r\n  },\r\n  async mounted() {\r\n    const { list } = await GetBOMInfo()\r\n    this.bomList = list\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    async fetchData() {\r\n      this.loading = true\r\n      let res\r\n      if (this.activeType === '-1') {\r\n        res = await GetCompTypeTree({ professional: this.typeCode })\r\n      } else {\r\n        res = await GetPartTypeTree({ professionalId: this.typeId, partGrade: this.activeType.toString() })\r\n      }\r\n      if (res.IsSucceed) {\r\n        this.treeData = res.Data\r\n      }\r\n      this.loading = false\r\n    },\r\n    handleNodeClick(node) {\r\n      console.log(node)\r\n    },\r\n    handleClick(tab, event) {\r\n      this.activeType = tab.name\r\n      this.fetchData()\r\n    },\r\n    handleAddToList() {\r\n      console.log('加入列表')\r\n    },\r\n    handleAdd() {\r\n      console.log('添加')\r\n    },\r\n    handleCancel() {\r\n      console.log('取消')\r\n      this.$emit('close')\r\n    }\r\n  }\r\n\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import \"~@/styles/mixin.scss\";\r\n.company-add-container {\r\n  height: 70vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .title {\r\n    margin-bottom: 16px;\r\n  }\r\n\r\n  .tree-container {\r\n    flex: 1;\r\n    overflow: hidden;\r\n\r\n    .el-tree {\r\n      @include scrollBar;\r\n      height: 100%;\r\n      overflow: auto;\r\n    }\r\n  }\r\n\r\n  footer {\r\n    text-align: right;\r\n    margin-top: 16px;\r\n  }\r\n}\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA,SAAAA,eAAA;AACA,SAAAC,eAAA;AACA,SAAAC,UAAA;AACA;EACAC,UAAA,GAEA;EACAC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,MAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,OAAA;MACAC,cAAA;MACAC,UAAA;MACAC,OAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAAC,iBAAA,EAAAC,IAAA;MAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACA1B,UAAA;UAAA;YAAAoB,iBAAA,GAAAI,QAAA,CAAAG,IAAA;YAAAN,IAAA,GAAAD,iBAAA,CAAAC,IAAA;YACAN,KAAA,CAAAF,OAAA,GAAAQ,IAAA;YACAN,KAAA,CAAAa,SAAA;UAAA;UAAA;YAAA,OAAAJ,QAAA,CAAAK,IAAA;QAAA;MAAA,GAAAV,OAAA;IAAA;EACA;EACAW,OAAA;IACAF,SAAA,WAAAA,UAAA;MAAA,IAAAG,MAAA;MAAA,OAAAf,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAc,SAAA;QAAA,IAAAC,GAAA;QAAA,OAAAhB,mBAAA,GAAAK,IAAA,UAAAY,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAV,IAAA,GAAAU,SAAA,CAAAT,IAAA;YAAA;cACAK,MAAA,CAAArB,OAAA;cAAA,MAEAqB,MAAA,CAAAnB,UAAA;gBAAAuB,SAAA,CAAAT,IAAA;gBAAA;cAAA;cAAAS,SAAA,CAAAT,IAAA;cAAA,OACA5B,eAAA;gBAAAsC,YAAA,EAAAL,MAAA,CAAA5B;cAAA;YAAA;cAAA8B,GAAA,GAAAE,SAAA,CAAAR,IAAA;cAAAQ,SAAA,CAAAT,IAAA;cAAA;YAAA;cAAAS,SAAA,CAAAT,IAAA;cAAA,OAEA3B,eAAA;gBAAAsC,cAAA,EAAAN,MAAA,CAAAxB,MAAA;gBAAA+B,SAAA,EAAAP,MAAA,CAAAnB,UAAA,CAAA2B,QAAA;cAAA;YAAA;cAAAN,GAAA,GAAAE,SAAA,CAAAR,IAAA;YAAA;cAEA,IAAAM,GAAA,CAAAO,SAAA;gBACAT,MAAA,CAAAtB,QAAA,GAAAwB,GAAA,CAAAQ,IAAA;cACA;cACAV,MAAA,CAAArB,OAAA;YAAA;YAAA;cAAA,OAAAyB,SAAA,CAAAN,IAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IACA;IACAU,eAAA,WAAAA,gBAAAC,IAAA;MACAC,OAAA,CAAAC,GAAA,CAAAF,IAAA;IACA;IACAG,WAAA,WAAAA,YAAAC,GAAA,EAAAC,KAAA;MACA,KAAApC,UAAA,GAAAmC,GAAA,CAAAE,IAAA;MACA,KAAArB,SAAA;IACA;IACAsB,eAAA,WAAAA,gBAAA;MACAN,OAAA,CAAAC,GAAA;IACA;IACAM,SAAA,WAAAA,UAAA;MACAP,OAAA,CAAAC,GAAA;IACA;IACAO,YAAA,WAAAA,aAAA;MACAR,OAAA,CAAAC,GAAA;MACA,KAAAQ,KAAA;IACA;EACA;AAEA", "ignoreList": []}]}
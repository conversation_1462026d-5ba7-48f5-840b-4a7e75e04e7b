{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\RecognitionConfig.vue?vue&type=template&id=4d274f86&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\RecognitionConfig.vue", "mtime": 1757474528544}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\addDraft.vue?vue&type=template&id=32942576&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\addDraft.vue", "mtime": 1757926768436}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImNvbnRlbnRCb3giPgogIDxkaXYgY2xhc3M9Im1haW4taW5mbyI+CiAgICA8ZGl2IGNsYXNzPSJsZWZ0Ij4KICAgICAgPEV4cGFuZGFibGVTZWN0aW9uIHYtbW9kZWw9InNob3dFeHBhbmQiIHYtbG9hZGluZz0idGJMb2FkaW5nIiBjbGFzcz0iZmZmIiA6d2lkdGg9IjMwMCI+CiAgICAgICAgPGRpdiBjbGFzcz0iaW5uZXItd3JhcHBlciI+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJ0cmVlLXNlYXJjaCI+CiAgICAgICAgICAgIDxlbC1zZWxlY3QKICAgICAgICAgICAgICB2LW1vZGVsPSJzdGF0dXNUeXBlIgogICAgICAgICAgICAgIGNsZWFyYWJsZQogICAgICAgICAgICAgIGNsYXNzPSJzZWFyY2gtc2VsZWN0IgogICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fpgInmi6kiCiAgICAgICAgICAgID4KICAgICAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLlj6/mjpLkuqciIHZhbHVlPSLlj6/mjpLkuqciIC8+CiAgICAgICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5o6S5Lqn5a6M5oiQIiB2YWx1ZT0i5o6S5Lqn5a6M5oiQIiAvPgogICAgICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuacquWvvOWFpSIgdmFsdWU9IuacquWvvOWFpSIgLz4KICAgICAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgIHYtbW9kZWwudHJpbT0icHJvamVjdE5hbWUiCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9IuaQnOe0oi4uLiIKICAgICAgICAgICAgICBzaXplPSJzbWFsbCIKICAgICAgICAgICAgICBjbGVhcmFibGUKICAgICAgICAgICAgICBzdWZmaXgtaWNvbj0iZWwtaWNvbi1zZWFyY2giCiAgICAgICAgICAgIC8+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDxlbC1kaXZpZGVyIGNsYXNzPSJjcy1kaXZpZGVyIiAvPgogICAgICAgICAgPGRpdiBjbGFzcz0idHJlZS14IGNzLXNjcm9sbCI+CiAgICAgICAgICAgIDx0cmVlLWRldGFpbAogICAgICAgICAgICAgIHJlZj0idHJlZSIKICAgICAgICAgICAgICBpY29uPSJpY29uLWZvbGRlciIKICAgICAgICAgICAgICBpcy1jdXN0b20tZmlsdGVyCiAgICAgICAgICAgICAgOmN1c3RvbS1maWx0ZXItZnVuPSJjdXN0b21GaWx0ZXJGdW4iCiAgICAgICAgICAgICAgOmxvYWRpbmc9InRyZWVMb2FkaW5nIgogICAgICAgICAgICAgIDp0cmVlLWRhdGE9InRyZWVEYXRhIgogICAgICAgICAgICAgIHNob3ctc3RhdHVzCiAgICAgICAgICAgICAgc2hvdy1kZXRhaWwKICAgICAgICAgICAgICA6ZmlsdGVyLXRleHQ9ImZpbHRlclRleHQiCiAgICAgICAgICAgICAgOmV4cGFuZGVkLWtleT0iZXhwYW5kZWRLZXkiCiAgICAgICAgICAgICAgQGhhbmRsZU5vZGVDbGljaz0iaGFuZGxlTm9kZUNsaWNrIgogICAgICAgICAgICA+CiAgICAgICAgICAgICAgPHRlbXBsYXRlICNjc0xhYmVsPSJ7c2hvd1N0YXR1cyxkYXRhfSI+CiAgICAgICAgICAgICAgICA8c3BhbiB2LWlmPSIhZGF0YS5QYXJlbnROb2RlcyIgY2xhc3M9ImNzLWJsdWUiPih7eyBkYXRhLkNvZGUgfX0pPC9zcGFuPnt7IGRhdGEuTGFiZWwgfX0KICAgICAgICAgICAgICAgIDx0ZW1wbGF0ZSB2LWlmPSJzaG93U3RhdHVzIj4KICAgICAgICAgICAgICAgICAgPHNwYW4gOmNsYXNzPSJbJ2NzLXRhZycsZGF0YS5EYXRhW3N0YXR1c0NvZGVdPT0n5Y+v5o6S5LqnJyA/ICdncmVlbkJnJyA6IGRhdGEuRGF0YVtzdGF0dXNDb2RlXT09J+aOkuS6p+WujOaIkCcgPydvcmFuZ2VCZyc6ZGF0YS5EYXRhW3N0YXR1c0NvZGVdPT0n5pyq5a+85YWlJz8ncmVkQmcnOicnXSI+CiAgICAgICAgICAgICAgICAgICAgPGkKICAgICAgICAgICAgICAgICAgICAgIHYtaWY9ImRhdGEuRGF0YVtzdGF0dXNDb2RlXSIKICAgICAgICAgICAgICAgICAgICAgIDpjbGFzcz0iW2RhdGEuRGF0YVtzdGF0dXNDb2RlXT09J+WPr+aOkuS6pycgPyAnZm91ckdyZWVuJyA6IGRhdGEuRGF0YVtzdGF0dXNDb2RlXT09J+aOkuS6p+WujOaIkCcgPydmb3VyT3JhbmdlJzpkYXRhLkRhdGFbc3RhdHVzQ29kZV09PSfmnKrlr7zlhaUnPydmb3VyUmVkJzonJ10iCiAgICAgICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgICAgICAge3sgZGF0YS5EYXRhW3N0YXR1c0NvZGVdIH19CiAgICAgICAgICAgICAgICAgICAgPC9pPgoKICAgICAgICAgICAgICAgICAgPC9zcGFuPgogICAgICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgICAgICA8L3RlbXBsYXRlPgoKICAgICAgICAgICAgPC90cmVlLWRldGFpbD4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvZGl2PgogICAgICA8L0V4cGFuZGFibGVTZWN0aW9uPgogICAgPC9kaXY+CiAgICA8ZGl2IGNsYXNzPSJyaWdodCI+CgogICAgICA8ZWwtZm9ybSByZWY9ImZvcm0iIDptb2RlbD0iZm9ybSIgbGFiZWwtd2lkdGg9IjkwcHgiPgogICAgICAgIDxlbC1yb3c+CiAgICAgICAgICA8dGVtcGxhdGUgPgogICAgICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICAgICAgPCEtLSAgICAgICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsLXdpZHRoPSI3MHB4IiBsYWJlbD0i6Zu25Lu25ZCN56ewIiBwcm9wPSJQYXJ0X0NvZGUiPgogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iY3MtaW5wdXQteCI+CiAgICAgICAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0uUGFydF9Db2RlIgogICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaUo56m65qC85Yy65YiGL+WkmuS4quaQnOe0oikiCiAgICAgICAgICAgICAgICAgICAgY2xlYXJhYmxlCiAgICAgICAgICAgICAgICAgICAgY2xhc3M9IncxMDAiCiAgICAgICAgICAgICAgICAgIC8+CiAgICAgICAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0uUGFydF9Db2RlQmx1ciIKICAgICAgICAgICAgICAgICAgICBjbGVhcmFibGUKICAgICAgICAgICAgICAgICAgICBjbGFzcz0idzEwMCIKICAgICAgICAgICAgICAgICAgICBzdHlsZT0ibWFyZ2luLWxlZnQ6IDEwcHg7IgogICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLmqKHns4rmn6Xmib4o6K+36L6T5YWl5YWz6ZSu5a2XKSIKICAgICAgICAgICAgICAgICAgICB0eXBlPSJ0ZXh0IgogICAgICAgICAgICAgICAgICAvPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgLS0+CiAgICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBwcm9wPSJzZWFyY2hDb250ZW50IiA6bGFiZWw9ImAke2NvbU5hbWV95ZCN56ewYCI+CiAgICAgICAgICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgICAgICAgICAgdi1tb2RlbD0ic2VhcmNoQ29udGVudCIKICAgICAgICAgICAgICAgICAgY2xlYXJhYmxlCiAgICAgICAgICAgICAgICAgIGNsYXNzPSJpbnB1dC13aXRoLXNlbGVjdCB3MTAwIgogICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWlKOepuuagvOWMuuWIhi/lpJrkuKrmkJzntKIpIgogICAgICAgICAgICAgICAgICBzaXplPSJzbWFsbCIKICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgICAgPGVsLXNlbGVjdAogICAgICAgICAgICAgICAgICAgIHNsb3Q9InByZXBlbmQiCiAgICAgICAgICAgICAgICAgICAgdi1tb2RlbD0iY3VyU2VhcmNoIgogICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fpgInmi6kiCiAgICAgICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiAxMDBweCIKICAgICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IueyvuWHhuafpeivoiIgOnZhbHVlPSIxIiAvPgogICAgICAgICAgICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuaooeeziuafpeivoiIgOnZhbHVlPSIwIiAvPgogICAgICAgICAgICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgICAgICAgICAgIDwvZWwtaW5wdXQ+CiAgICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgIDwvZWwtY29sPgogICAgICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBwcm9wPSJzZWFyY2hVbml0UGFydENvbnRlbnQiIGxhYmVsPSLpg6jku7blkI3np7AiPgogICAgICAgICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgICAgICAgIHYtbW9kZWw9InNlYXJjaFVuaXRQYXJ0Q29udGVudCIKICAgICAgICAgICAgICAgICAgY2xlYXJhYmxlCiAgICAgICAgICAgICAgICAgIGNsYXNzPSJpbnB1dC13aXRoLXNlbGVjdCB3MTAwIgogICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWlKOepuuagvOWMuuWIhi/lpJrkuKrmkJzntKIpIgogICAgICAgICAgICAgICAgICBzaXplPSJzbWFsbCIKICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgICAgPGVsLXNlbGVjdAogICAgICAgICAgICAgICAgICAgIHNsb3Q9InByZXBlbmQiCiAgICAgICAgICAgICAgICAgICAgdi1tb2RlbD0iY3VyVW5pdFBhcnRTZWFyY2giCiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+mAieaLqSIKICAgICAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDEwMHB4IgogICAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i57K+5YeG5p+l6K+iIiA6dmFsdWU9IjEiIC8+CiAgICAgICAgICAgICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5qih57OK5p+l6K+iIiA6dmFsdWU9IjAiIC8+CiAgICAgICAgICAgICAgICAgIDwvZWwtc2VsZWN0PgogICAgICAgICAgICAgICAgPC9lbC1pbnB1dD4KICAgICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjEyIj4KICAgICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIHByb3A9InNlYXJjaFBhcnRDb250ZW50IiA6bGFiZWw9ImAke3BhcnROYW1lfeWQjeensGAiPgogICAgICAgICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgICAgICAgIHYtbW9kZWw9InNlYXJjaFBhcnRDb250ZW50IgogICAgICAgICAgICAgICAgICBjbGVhcmFibGUKICAgICAgICAgICAgICAgICAgY2xhc3M9ImlucHV0LXdpdGgtc2VsZWN0IHcxMDAiCiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaUo56m65qC85Yy65YiGL+WkmuS4quaQnOe0oikiCiAgICAgICAgICAgICAgICAgIHNpemU9InNtYWxsIgogICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgICA8ZWwtc2VsZWN0CiAgICAgICAgICAgICAgICAgICAgc2xvdD0icHJlcGVuZCIKICAgICAgICAgICAgICAgICAgICB2LW1vZGVsPSJjdXJQYXJ0U2VhcmNoIgogICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fpgInmi6kiCiAgICAgICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiAxMDBweCIKICAgICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IueyvuWHhuafpeivoiIgOnZhbHVlPSIxIiAvPgogICAgICAgICAgICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuaooeeziuafpeivoiIgOnZhbHVlPSIwIiAvPgogICAgICAgICAgICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgICAgICAgICAgIDwvZWwtaW5wdXQ+CiAgICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgIDwvZWwtY29sPgogICAgICAgICAgICA8ZWwtY29sIDpzcGFuPSI1Ij4KICAgICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLop4TmoLwiIHByb3A9IlNwZWMiPgogICAgICAgICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0uU3BlYyIKICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpSIKICAgICAgICAgICAgICAgICAgY2xlYXJhYmxlCiAgICAgICAgICAgICAgICAvPgogICAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgICA8L2VsLWNvbD4KICAgICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iNSI+CiAgICAgICAgICAgICAgPGVsLWZvcm0taXRlbSA6bGFiZWw9ImAke3BhcnROYW1lfeenjeexu2AiIHByb3A9IlR5cGVfTmFtZSI+CiAgICAgICAgICAgICAgICA8ZWwtc2VsZWN0CiAgICAgICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0uVHlwZV9OYW1lIgogICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36YCJ5oupIgogICAgICAgICAgICAgICAgICBjbGVhcmFibGUKICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgICAgPGVsLW9wdGlvbgogICAgICAgICAgICAgICAgICAgIHYtZm9yPSJpdGVtIGluIHR5cGVPcHRpb24iCiAgICAgICAgICAgICAgICAgICAgOmtleT0iaXRlbS5Db2RlIgogICAgICAgICAgICAgICAgICAgIDpsYWJlbD0iaXRlbS5OYW1lIgogICAgICAgICAgICAgICAgICAgIDp2YWx1ZT0iaXRlbS5OYW1lIgogICAgICAgICAgICAgICAgICAvPgogICAgICAgICAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgIDwvZWwtY29sPgogICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjUiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmibnmrKEiIGxhYmVsLXdpZHRoPSI1MHB4IiBwcm9wPSJDcmVhdGVfVXNlck5hbWUiPgogICAgICAgICAgICAgIDxlbC1zZWxlY3QKICAgICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0uSW5zdGFsbFVuaXRfSWQiCiAgICAgICAgICAgICAgICBmaWx0ZXJhYmxlCiAgICAgICAgICAgICAgICBjbGVhcmFibGUKICAgICAgICAgICAgICAgIG11bHRpcGxlCiAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDEwMCUiCiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36YCJ5oupIgogICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgIDxlbC1vcHRpb24KICAgICAgICAgICAgICAgICAgdi1mb3I9Iml0ZW0gaW4gaW5zdGFsbFVuaXRJZExpc3QiCiAgICAgICAgICAgICAgICAgIDprZXk9Iml0ZW0uSWQiCiAgICAgICAgICAgICAgICAgIDpsYWJlbD0iaXRlbS5OYW1lIgogICAgICAgICAgICAgICAgICA6dmFsdWU9Iml0ZW0uSWQiCiAgICAgICAgICAgICAgICAvPgogICAgICAgICAgICAgIDwvZWwtc2VsZWN0PgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDwvZWwtY29sPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iOSI+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWwtd2lkdGg9IjAiPgogICAgICAgICAgICAgIDxlbC1idXR0b24gc3R5bGU9Im1hcmdpbi1sZWZ0OiAxMHB4IiBAY2xpY2s9ImhhbmRsZVJlc2V0Ij7ph43nva48L2VsLWJ1dHRvbj4KICAgICAgICAgICAgICA8ZWwtYnV0dG9uIHN0eWxlPSJtYXJnaW4tbGVmdDogMTBweCIgdHlwZT0icHJpbWFyeSIgQGNsaWNrPSJoYW5kbGVTZWFyY2goKSI+5p+l6K+iPC9lbC1idXR0b24+CiAgICAgICAgICAgICAgPGVsLWJ1dHRvbiA6bG9hZGluZz0iYWRkTG9hZGluZyIgc3R5bGU9Im1hcmdpbi1sZWZ0OiAxMHB4IiB0eXBlPSJwcmltYXJ5IiBAY2xpY2s9ImFkZFRvTGlzdCgpIj7liqDlhaXliJfooag8L2VsLWJ1dHRvbj4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICA8L2VsLWNvbD4KCiAgICAgICAgPC9lbC1yb3c+CiAgICAgIDwvZWwtZm9ybT4KCiAgICAgIDxkaXYgY2xhc3M9InRiLXdyYXBwZXIiPgogICAgICAgIDx2eGUtdGFibGUKICAgICAgICAgIHJlZj0ieFRhYmxlMSIKICAgICAgICAgIDplbXB0eS1yZW5kZXI9IntuYW1lOiAnTm90RGF0YSd9IgogICAgICAgICAgc2hvdy1oZWFkZXItb3ZlcmZsb3cKICAgICAgICAgIGVtcHR5LXRleHQ9IuaaguaXoOaVsOaNriIKICAgICAgICAgIGhlaWdodD0iYXV0byIKICAgICAgICAgIHNob3ctb3ZlcmZsb3cKICAgICAgICAgIDpjaGVja2JveC1jb25maWc9IntjaGVja0ZpZWxkOiAnY2hlY2tlZCcsIGNoZWNrTWV0aG9kOiBjaGVja0NoZWNrYm94TWV0aG9kfSIKICAgICAgICAgIDpsb2FkaW5nPSJ0YkxvYWRpbmciCiAgICAgICAgICA6cm93LWNvbmZpZz0ie2lzQ3VycmVudDogdHJ1ZSwgaXNIb3ZlcjogdHJ1ZSB9IgogICAgICAgICAgY2xhc3M9ImNzLXZ4ZS10YWJsZSIKICAgICAgICAgIGFsaWduPSJsZWZ0IgogICAgICAgICAgc3RyaXBlCiAgICAgICAgICA6ZGF0YT0iZlRhYmxlIgogICAgICAgICAgcmVzaXphYmxlCiAgICAgICAgICA6ZWRpdC1jb25maWc9Int0cmlnZ2VyOiAnY2xpY2snLCBtb2RlOiAnY2VsbCd9IgogICAgICAgICAgOnRvb2x0aXAtY29uZmlnPSJ7IGVudGVyYWJsZTogdHJ1ZSB9IgogICAgICAgICAgQGNoZWNrYm94LWFsbD0idGJTZWxlY3RDaGFuZ2UiCiAgICAgICAgICBAY2hlY2tib3gtY2hhbmdlPSJ0YlNlbGVjdENoYW5nZSIKICAgICAgICA+CiAgICAgICAgICA8dnhlLWNvbHVtbiBmaXhlZD0ibGVmdCIgdHlwZT0iY2hlY2tib3giIHdpZHRoPSI2MCIgLz4KICAgICAgICAgIDx0ZW1wbGF0ZSB2LWZvcj0iaXRlbSBpbiBjb2x1bW5zIj4KICAgICAgICAgICAgPHZ4ZS1jb2x1bW4KICAgICAgICAgICAgICB2LWlmPSJpdGVtLkNvZGUgPT09ICdJc19Db21wb25lbnQnIgogICAgICAgICAgICAgIDprZXk9Iml0ZW0uQ29kZSIKICAgICAgICAgICAgICA6YWxpZ249Iml0ZW0uQWxpZ24iCiAgICAgICAgICAgICAgOmZpZWxkPSJpdGVtLkNvZGUiCiAgICAgICAgICAgICAgOnRpdGxlPSJpdGVtLkRpc3BsYXlfTmFtZSIKICAgICAgICAgICAgICBzb3J0YWJsZQogICAgICAgICAgICAgIDptaW4td2lkdGg9Iml0ZW0uV2lkdGgiCiAgICAgICAgICAgID4KICAgICAgICAgICAgICA8dGVtcGxhdGUgI2RlZmF1bHQ9Insgcm93IH0iPgogICAgICAgICAgICAgICAgPGVsLXRhZyA6dHlwZT0icm93LklzX0NvbXBvbmVudCA/ICdkYW5nZXInIDogJ3N1Y2Nlc3MnIj57ewogICAgICAgICAgICAgICAgICByb3cuSXNfQ29tcG9uZW50ID8gIuWQpiIgOiAi5pivIgogICAgICAgICAgICAgICAgfX08L2VsLXRhZz4KICAgICAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgICAgICA8L3Z4ZS1jb2x1bW4+CiAgICAgICAgICAgIDx2eGUtY29sdW1uCiAgICAgICAgICAgICAgdi1lbHNlLWlmPSJbJ1BhcnRfQ29kZScsJ0NvbXBfQ29kZSddLmluY2x1ZGVzKGl0ZW0uQ29kZSkiCiAgICAgICAgICAgICAgOmtleT0iaXRlbS5Db2RlIgogICAgICAgICAgICAgIDphbGlnbj0iaXRlbS5BbGlnbiIKICAgICAgICAgICAgICA6ZmllbGQ9Iml0ZW0uQ29kZSIKICAgICAgICAgICAgICA6dGl0bGU9Iml0ZW0uRGlzcGxheV9OYW1lIgogICAgICAgICAgICAgIHNvcnRhYmxlCiAgICAgICAgICAgICAgOm1pbi13aWR0aD0iaXRlbS5XaWR0aCIKICAgICAgICAgICAgPgogICAgICAgICAgICAgIDx0ZW1wbGF0ZSAjZGVmYXVsdD0ieyByb3cgfSI+CiAgICAgICAgICAgICAgICA8ZWwtdGFnIHYtaWY9InJvdy5zdG9wRmxhZyIgc3R5bGU9Im1hcmdpbi1yaWdodDogOHB4OyIgdHlwZT0iZGFuZ2VyIj7lgZw8L2VsLXRhZz4KICAgICAgICAgICAgICAgIDxlbC10YWcgdi1pZj0icm93LklzX0NoYW5nZSIgc3R5bGU9Im1hcmdpbi1yaWdodDogOHB4OyIgdHlwZT0iZGFuZ2VyIj7lj5g8L2VsLXRhZz4KICAgICAgICAgICAgICAgIHt7IHJvd1tpdGVtLkNvZGVdIHwgZGlzcGxheVZhbHVlIH19CiAgICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgICAgPC92eGUtY29sdW1uPgogICAgICAgICAgICA8dnhlLWNvbHVtbgogICAgICAgICAgICAgIHYtZWxzZS1pZj0iWydDYW5fU2NoZHVsaW5nX0NvdW50J10uaW5jbHVkZXMoaXRlbS5Db2RlKSIKICAgICAgICAgICAgICA6a2V5PSJpdGVtLkNvZGUiCiAgICAgICAgICAgICAgOmFsaWduPSJpdGVtLkFsaWduIgogICAgICAgICAgICAgIDpmaWVsZD0iaXRlbS5Db2RlIgogICAgICAgICAgICAgIDp0aXRsZT0iaXRlbS5EaXNwbGF5X05hbWUiCiAgICAgICAgICAgICAgc29ydGFibGUKICAgICAgICAgICAgICA6bWluLXdpZHRoPSJpdGVtLldpZHRoIgogICAgICAgICAgICA+CiAgICAgICAgICAgICAgPHRlbXBsYXRlICNkZWZhdWx0PSJ7IHJvdyB9Ij4KICAgICAgICAgICAgICAgIDxzcGFuIHYtaWY9InNob3dTYyI+e3sgcm93LmNzQ291bnR8fCcnIH19PC9zcGFuPgogICAgICAgICAgICAgICAgPHNwYW4gdi1lbHNlPnt7IHJvd1tpdGVtLkNvZGVdIHwgZGlzcGxheVZhbHVlIH19PC9zcGFuPgogICAgICAgICAgICAgIDwvdGVtcGxhdGU+CiAgICAgICAgICAgIDwvdnhlLWNvbHVtbj4KICAgICAgICAgICAgPHZ4ZS1jb2x1bW4KICAgICAgICAgICAgICB2LWVsc2UtaWY9IlsnQ2FuX1NjaGR1bGluZ19XZWlnaHQnXS5pbmNsdWRlcyhpdGVtLkNvZGUpIgogICAgICAgICAgICAgIDprZXk9Iml0ZW0uQ29kZSIKICAgICAgICAgICAgICA6YWxpZ249Iml0ZW0uQWxpZ24iCiAgICAgICAgICAgICAgOmZpZWxkPSJpdGVtLkNvZGUiCiAgICAgICAgICAgICAgOnRpdGxlPSJpdGVtLkRpc3BsYXlfTmFtZSIKICAgICAgICAgICAgICBzb3J0YWJsZQogICAgICAgICAgICAgIDptaW4td2lkdGg9Iml0ZW0uV2lkdGgiCiAgICAgICAgICAgID4KICAgICAgICAgICAgICA8dGVtcGxhdGUgI2RlZmF1bHQ9Insgcm93IH0iPgogICAgICAgICAgICAgICAgPHNwYW4gdi1pZj0ic2hvd1NjIj57eyByb3cuY3NDb3VudFdlaWdodHx8JycgfX08L3NwYW4+CiAgICAgICAgICAgICAgICA8c3BhbiB2LWVsc2U+e3sgcm93W2l0ZW0uQ29kZV0gfCBkaXNwbGF5VmFsdWUgfX08L3NwYW4+CiAgICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgICAgPC92eGUtY29sdW1uPgogICAgICAgICAgICA8dnhlLWNvbHVtbgogICAgICAgICAgICAgIHYtZWxzZQogICAgICAgICAgICAgIDprZXk9Iml0ZW0uQ29kZSIKICAgICAgICAgICAgICA6YWxpZ249Iml0ZW0uQWxpZ24iCiAgICAgICAgICAgICAgOmZpeGVkPSJpdGVtLklzX0Zyb3plbj9pdGVtLkZyb3plbl9EaXJjdGlvbjonJyIKICAgICAgICAgICAgICBzaG93LW92ZXJmbG93PSJ0b29sdGlwIgogICAgICAgICAgICAgIHNvcnRhYmxlCiAgICAgICAgICAgICAgOmZpZWxkPSJpdGVtLkNvZGUiCiAgICAgICAgICAgICAgOnRpdGxlPSJpdGVtLkRpc3BsYXlfTmFtZSIKICAgICAgICAgICAgICA6bWluLXdpZHRoPSJpdGVtLldpZHRoIgogICAgICAgICAgICAvPgogICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICA8L3Z4ZS10YWJsZT4KICAgICAgPC9kaXY+CiAgICAgIDxkaXYgY2xhc3M9ImRhdGEtaW5mbyI+CiAgICAgICAgPGVsLXRhZwogICAgICAgICAgc2l6ZT0ibWVkaXVtIgogICAgICAgICAgY2xhc3M9ImluZm8teCIKICAgICAgICA+5bey6YCJIHt7IHRvdGFsU2VsZWN0aW9uLmxlbmd0aCB9fSDmnaHmlbDmja4KICAgICAgICA8L2VsLXRhZz4KICAgICAgICA8dnhlLXBhZ2VyCiAgICAgICAgICBib3JkZXIKICAgICAgICAgIGJhY2tncm91bmQKICAgICAgICAgIDpsb2FkaW5nPSJ0YkxvYWRpbmciCiAgICAgICAgICA6Y3VycmVudC1wYWdlLnN5bmM9InBhZ2VJbmZvLnBhZ2UiCiAgICAgICAgICA6cGFnZS1zaXplLnN5bmM9InBhZ2VJbmZvLnBhZ2VTaXplIgogICAgICAgICAgOnBhZ2Utc2l6ZXM9InBhZ2VJbmZvLnBhZ2VTaXplcyIKICAgICAgICAgIDp0b3RhbD0icGFnZUluZm8udG90YWwiCiAgICAgICAgICA6bGF5b3V0cz0iWydQcmV2UGFnZScsICdKdW1wTnVtYmVyJywgJ05leHRQYWdlJywgJ0Z1bGxKdW1wJywgJ1NpemVzJywgJ1RvdGFsJ10iCiAgICAgICAgICBzaXplPSJzbWFsbCIKICAgICAgICAgIEBwYWdlLWNoYW5nZT0iaGFuZGxlUGFnZUNoYW5nZSIKICAgICAgICAvPgogICAgICA8L2Rpdj4KICAgIDwvZGl2PgogIDwvZGl2PgogIDxkaXYgY2xhc3M9ImJ1dHRvbiI+CiAgICA8ZWwtYnV0dG9uIEBjbGljaz0iaGFuZGxlQ2xvc2UiPuWPlua2iDwvZWwtYnV0dG9uPgogICAgPGVsLWJ1dHRvbgogICAgICB0eXBlPSJwcmltYXJ5IgogICAgICA6ZGlzYWJsZWQ9IiF0b3RhbFNlbGVjdGlvbi5sZW5ndGgiCiAgICAgIDpsb2FkaW5nPSJzYXZlTG9hZGluZyIKICAgICAgQGNsaWNrPSJoYW5kbGVTYXZlKDIpIgogICAgPuS/neWtmDwvZWwtYnV0dG9uPgogIDwvZGl2Pgo8L2Rpdj4K"}, null]}
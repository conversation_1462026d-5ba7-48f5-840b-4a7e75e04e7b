{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\addDraft.vue?vue&type=template&id=32942576&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\addDraft.vue", "mtime": 1757468127991}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\quality_Inspection\\quality_summary\\components\\spotCheck.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\quality_Inspection\\quality_summary\\components\\spotCheck.vue", "mtime": 1757572678833}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgSW5zcGVjdERvYyBmcm9tICcuL2FkZC9JbnNwZWN0RG9jLnZ1ZScNCmltcG9ydCByZWN0aWZpY2F0aW9uU2hlZXQgZnJvbSAnLi9yZWN0aWZpY2F0aW9uL3JlY3RpZmljYXRpb25TaGVldCcNCmltcG9ydCB7IEdldEdyaWRCeUNvZGUgfSBmcm9tICdAL2FwaS9zeXMnDQppbXBvcnQgeyBHZXRQYWdlUXVhbGl0eVN1bW1hcnkgfSBmcm9tICdAL2FwaS9QUk8vcXVhbGl0eUluc3BlY3Qvc3RhcnQtSW5zcGVjdCcNCmltcG9ydCBEeW5hbWljRGF0YVRhYmxlIGZyb20gJ0AvY29tcG9uZW50cy9EeW5hbWljRGF0YVRhYmxlL0R5bmFtaWNEYXRhVGFibGUnDQppbXBvcnQgZWxEcmFnRGlhbG9nIGZyb20gJ0AvZGlyZWN0aXZlL2VsLWRyYWctZGlhbG9nJw0KaW1wb3J0IHsgR2V0RmFjdG9yeVByb2Zlc3Npb25hbEJ5Q29kZSB9IGZyb20gJ0AvYXBpL1BSTy9wcm9mZXNzaW9uYWxUeXBlJw0KaW1wb3J0IHsgdGltZUZvcm1hdCB9IGZyb20gJ0AvZmlsdGVycycNCmltcG9ydCB7IEV4cG9ydEluc3BzZWN0aW9uU3VtbWFyeUluZm8gfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJw0KaW1wb3J0IHsgY29tYmluZVVSTCB9IGZyb20gJ0AvdXRpbHMnDQppbXBvcnQgUGFnaW5hdGlvbiBmcm9tICdAL2NvbXBvbmVudHMvUGFnaW5hdGlvbi9pbmRleC52dWUnDQppbXBvcnQgZ2V0VGJJbmZvIGZyb20gJ0AvbWl4aW5zL1BSTy9nZXQtdGFibGUtaW5mbycNCmltcG9ydCB7IHRhYmxlUGFnZVNpemUgfSBmcm9tICdAL3ZpZXdzL1BSTy9zZXR0aW5nJw0KZXhwb3J0IGRlZmF1bHQgew0KICBkaXJlY3RpdmVzOiB7IGVsRHJhZ0RpYWxvZyB9LA0KICBjb21wb25lbnRzOiB7DQogICAgUGFnaW5hdGlvbiwNCiAgICBEeW5hbWljRGF0YVRhYmxlLA0KICAgIHJlY3RpZmljYXRpb25TaGVldCwNCiAgICBJbnNwZWN0RG9jDQogIH0sDQogIG1peGluczogW2dldFRiSW5mb10sDQogIHByb3BzOiB7DQogICAgc2VhcmNoRGV0YWlsOiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgICBkZWZhdWx0OiAoKSA9PiAoe30pDQogICAgfQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICB3aWR0aDogJzYwJScsDQogICAgICBjb2RlOiAnJywNCiAgICAgIFR5cGVJZDogJycsDQogICAgICB0eXBlT3B0aW9uOiAnJywNCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBkaWFsb2dUaXRsZTogJycsDQogICAgICBJc21vZGFsOiB0cnVlLA0KICAgICAgZGlhbG9nRGF0YToge30sDQogICAgICBjdXJyZW50Q29tcG9uZW50OiAnJywNCiAgICAgIHRiQ29uZmlnOiB7DQogICAgICB9LA0KICAgICAgRGF0YTogW10sDQogICAgICBjb2x1bW5zOiBbXSwNCiAgICAgIHRiRGF0YTogW10sDQogICAgICBxdWVyeUluZm86IHsNCiAgICAgICAgUGFnZTogMSwNCiAgICAgICAgUGFnZVNpemU6IHRhYmxlUGFnZVNpemVbMF0NCiAgICAgIH0sDQogICAgICB0YWJsZVBhZ2VTaXplOiB0YWJsZVBhZ2VTaXplLA0KICAgICAgdG90YWw6IDAsDQogICAgICBncmlkQ29kZTogJ1Byb19JbnBlY3Rpb25fc3VtbWFyeV9saXN0X3Nwb3QnLA0KICAgICAgc2VhcmNoSGVpZ2h0OiAwLA0KICAgICAgQ2hlY2tSZXN1bHREYXRhOiBbXSwNCiAgICAgIENoZWNrTm9kZUxpc3Q6IFtdLCAvLyDotKjmo4DoioLngrkNCiAgICAgIENoZWNrT2JqZWN0RGF0YTogW10sIC8vIOi0qOajgOWvueixoQ0KICAgICAgY2hlY2tfb2JqZWN0X2lkOiAnJywNCiAgICAgIFByb2plY3ROYW1lRGF0YTogW10sDQogICAgICBjaGVja19vYmplY3RfTmFtZTogJycsDQogICAgICBzZWxlY3RMaXN0OiBbXQ0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmdldFR5cGVMaXN0KCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGhhbmRsZVNlYXJjaCgpIHt9LA0KICAgIGFzeW5jIGdldFR5cGVMaXN0KCkgew0KICAgICAgbGV0IHJlcywgZGF0YQ0KICAgICAgcmVzID0gYXdhaXQgR2V0RmFjdG9yeVByb2Zlc3Npb25hbEJ5Q29kZSh7DQogICAgICAgIGZhY3RvcnlJZDogbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ0N1clJlZmVyZW5jZUlkJykNCiAgICAgIH0pDQogICAgICBkYXRhID0gcmVzLkRhdGENCiAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgIHRoaXMudHlwZU9wdGlvbiA9IE9iamVjdC5mcmVlemUoZGF0YSkNCiAgICAgICAgY29uc29sZS5sb2codGhpcy50eXBlT3B0aW9uKQ0KICAgICAgICBpZiAodGhpcy50eXBlT3B0aW9uLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICB0aGlzLlR5cGVJZCA9IHRoaXMudHlwZU9wdGlvblswXT8uSWQNCiAgICAgICAgICB0aGlzLmNvZGUgPSB0aGlzLnR5cGVPcHRpb24uZmluZCgoaSkgPT4gaS5JZCA9PT0gdGhpcy5UeXBlSWQpLkNvZGUNCiAgICAgICAgICB0aGlzLmdldFRhYmxlQ29uZmlnKHRoaXMuZ3JpZENvZGUgKyAnLCcgKyB0aGlzLmNvZGUpDQogICAgICAgIH0NCiAgICAgICAgdGhpcy5mZXRjaERhdGEoMSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KICAgIGZldGNoRGF0YShwYWdlKSB7DQogICAgICBwYWdlICYmICh0aGlzLnF1ZXJ5SW5mby5QYWdlID0gcGFnZSkNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIGNvbnN0IFNlYWNoUGFyYW1zID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLnNlYXJjaERldGFpbCkpDQogICAgICBjb25zdCBTdGVlbE5hbWUgPSBTZWFjaFBhcmFtcy5TdGVlbE5hbWUudHJpbSgpLnJlcGxhY2VBbGwoJyAnLCAnXG4nKQ0KICAgICAgaWYgKFNlYWNoUGFyYW1zLlBpY2tfRGF0ZSAmJiBTZWFjaFBhcmFtcy5QaWNrX0RhdGUubGVuZ3RoID09PSAyKSB7DQogICAgICAgIFNlYWNoUGFyYW1zLkJlZ2luRGF0ZSA9IFNlYWNoUGFyYW1zLlBpY2tfRGF0ZVswXQ0KICAgICAgICBTZWFjaFBhcmFtcy5FbmREYXRlID0gU2VhY2hQYXJhbXMuUGlja19EYXRlWzFdDQogICAgICB9IGVsc2Ugew0KICAgICAgICBTZWFjaFBhcmFtcy5CZWdpbkRhdGUgPSBudWxsDQogICAgICAgIFNlYWNoUGFyYW1zLkVuZERhdGUgPSBudWxsDQogICAgICB9DQogICAgICBHZXRQYWdlUXVhbGl0eVN1bW1hcnkoew0KICAgICAgICBwYWdlSW5mbzogdGhpcy5xdWVyeUluZm8sDQogICAgICAgIC4uLlNlYWNoUGFyYW1zLA0KICAgICAgICBTdGVlbE5hbWUsDQogICAgICAgIENoZWNrX1N0eWxlOiAwDQogICAgICB9KQ0KICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIHJldHVybiB0aGlzLnNldEdyaWREYXRhKHJlcy5EYXRhKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKGNvbnNvbGUuZXJyb3IpDQogICAgICAgIC5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgICAvLyDnu5PmnZ9sb2FkaW5nDQogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UNCiAgICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRlbFNoZWV0KHJvdykgew0KICAgICAgY29uc29sZS5sb2cocm93KQ0KICAgICAgdGhpcy5nZW5lcmF0ZUNvbXBvbmVudCgn5pW05pS55Y2VJywgJ3JlY3RpZmljYXRpb25TaGVldCcpDQogICAgICB0aGlzLiRuZXh0VGljaygoXykgPT4gew0KICAgICAgICB0aGlzLiRyZWZzWydjb250ZW50J10uaW5pdCh0aGlzLmNvZGUsIHJvdykNCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kZWxWaWV3KHJvdykgew0KICAgICAgdGhpcy5nZW5lcmF0ZUNvbXBvbmVudCgn5p+l55yL6LSo5qOA5Y2VJywgJ2luc3BlY3REb2MnKQ0KICAgICAgdGhpcy4kbmV4dFRpY2soKF8pID0+IHsNCiAgICAgICAgdGhpcy4kcmVmc1snY29udGVudCddLmluaXQodGhpcy5jb2RlLCByb3cpDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICBzZXRHcmlkRGF0YShkYXRhKSB7DQogICAgICB0aGlzLnRiRGF0YSA9IHRoaXMudGJEYXRhID0gZGF0YS5EYXRhLm1hcCgodikgPT4gew0KICAgICAgICB2LklkID0gdi5TaGVldElkIC8vIOino+WGs+WFqOmAieahhuaJk+WLvumXrumimA0KICAgICAgICB2LlJlY3RpZnlfRGF0ZSA9IHYuUmVjdGlmeV9EYXRlDQogICAgICAgICAgPyB0aW1lRm9ybWF0KHYuUmVjdGlmeV9EYXRlLCAne3l9LXttfS17ZH0nKQ0KICAgICAgICAgIDogJy0nDQogICAgICAgIHYuUGlja19EYXRlID0gdi5QaWNrX0RhdGUNCiAgICAgICAgICA/IHRpbWVGb3JtYXQodi5QaWNrX0RhdGUsICd7eX0te219LXtkfScpDQogICAgICAgICAgOiAnLScNCiAgICAgICAgcmV0dXJuIHYNCiAgICAgIH0pDQoNCiAgICAgIHRoaXMudG90YWwgPSBkYXRhLlRvdGFsQ291bnQNCiAgICB9LA0KDQogICAgbXVsdGlTZWxlY3RlZENoYW5nZShhcnJheSkgew0KICAgICAgdGhpcy5zZWxlY3RMaXN0ID0gYXJyYXkucmVjb3Jkcw0KICAgICAgdGhpcy4kZW1pdCgnc2VsZWN0Q2hhbmdlJywgdGhpcy5zZWxlY3RMaXN0KQ0KICAgIH0sDQogICAgZ2VuZXJhdGVDb21wb25lbnQodGl0bGUsIGNvbXBvbmVudCkgew0KICAgICAgdGhpcy5kaWFsb2dUaXRsZSA9IHRpdGxlDQogICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSBjb21wb25lbnQNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIGhhbmRsZUNsb3NlKCkgew0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICAgIHRoaXMuZmV0Y2hEYXRhKDEpDQogICAgfSwNCiAgICBleHBvcnRUYigpIHsNCiAgICAgIGNvbnN0IFNoZWV0SWRzID0gdGhpcy5zZWxlY3RMaXN0Lm1hcCgodikgPT4gdi5TaGVldElkKQ0KICAgICAgdGhpcy4kZW1pdCgnc2V0RXhwb3J0TG9hZGluZycsIHRydWUpDQogICAgICBjb25zdCBTZWFjaFBhcmFtcyA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy5zZWFyY2hEZXRhaWwpKQ0KICAgICAgY29uc3QgU3RlZWxOYW1lID0gU2VhY2hQYXJhbXMuU3RlZWxOYW1lLnRyaW0oKS5yZXBsYWNlQWxsKCcgJywgJ1xuJykNCiAgICAgIGlmIChTZWFjaFBhcmFtcy5QaWNrX0RhdGUgJiYgU2VhY2hQYXJhbXMuUGlja19EYXRlLmxlbmd0aCA9PT0gMikgew0KICAgICAgICBTZWFjaFBhcmFtcy5CZWdpbkRhdGUgPSBTZWFjaFBhcmFtcy5QaWNrX0RhdGVbMF0NCiAgICAgICAgU2VhY2hQYXJhbXMuRW5kRGF0ZSA9IFNlYWNoUGFyYW1zLlBpY2tfRGF0ZVsxXQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgU2VhY2hQYXJhbXMuQmVnaW5EYXRlID0gbnVsbA0KICAgICAgICBTZWFjaFBhcmFtcy5FbmREYXRlID0gbnVsbA0KICAgICAgfQ0KICAgICAgRXhwb3J0SW5zcHNlY3Rpb25TdW1tYXJ5SW5mbyh7DQogICAgICAgIHBhZ2VJbmZvOiB0aGlzLnF1ZXJ5SW5mbywNCiAgICAgICAgLi4uU2VhY2hQYXJhbXMsDQogICAgICAgIFN0ZWVsTmFtZSwNCiAgICAgICAgQ2hlY2tfU3R5bGU6IDAsDQogICAgICAgIFNoZWV0SWRzDQogICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgd2luZG93Lm9wZW4oY29tYmluZVVSTCh0aGlzLiRiYXNlVXJsLCByZXMuRGF0YSksICdfYmxhbmsnKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLiRlbWl0KCdzZXRFeHBvcnRMb2FkaW5nJywgZmFsc2UpDQogICAgICB9KQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["spotCheck.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0KA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "spotCheck.vue", "sourceRoot": "src/views/PRO/quality_Inspection/quality_summary/components", "sourcesContent": ["<template>\r\n  <div style=\"height: 100%\">\r\n    <!--    <div class=\"table_warrap\">\r\n      <div class=\"table_content\">\r\n        <el-main\r\n          v-loading=\"loading\"\r\n          class=\"no-v-padding\"\r\n          style=\"padding: 0; height: 100%\"\r\n        >\r\n          <DynamicDataTable\r\n            ref=\"table\"\r\n            :config=\"tbConfig\"\r\n            :columns=\"columns\"\r\n            :data=\"tbData\"\r\n            :total=\"pageInfo.TotalCount\"\r\n            :page=\"pageInfo.Page\"\r\n            stripe\r\n            height=\"100%\"\r\n            class=\"cs-plm-dy-table\"\r\n            border\r\n            @gridPageChange=\"gridPageChange\"\r\n            @gridSizeChange=\"gridSizeChange\"\r\n            @multiSelectedChange=\"multiSelectedChange\"\r\n          >\r\n            <template slot=\"Number\" slot-scope=\"{ row }\">\r\n              <span>{{ row.Number || \"-\" }}</span>\r\n            </template>\r\n            <template slot=\"Rectify_Date\" slot-scope=\"{ row }\">\r\n              <span>{{ row.Rectify_Date || \"-\" }}</span>\r\n            </template>\r\n            <template slot=\"Rectifier_name\" slot-scope=\"{ row }\">\r\n              <span>{{ row.Rectifier_name || \"-\" }}</span>\r\n            </template>\r\n            <template slot=\"Partcipant_name\" slot-scope=\"{ row }\">\r\n              <span>{{ row.Partcipant_name || \"-\" }}</span>\r\n            </template>\r\n            <template slot=\"Check_Result\" slot-scope=\"{ row }\">\r\n              <span>{{ row.Check_Result || \"-\" }}</span>\r\n            </template>\r\n            <template slot=\"Pick_Date\" slot-scope=\"{ row }\">\r\n              <span>{{ row.Pick_Date || \"-\" }}</span>\r\n            </template>\r\n            <template slot=\"Rectifier_Code\" slot-scope=\"{ row }\">\r\n              <el-button\r\n                v-if=\"Boolean(row.Rectifier_Code)\"\r\n                type=\"text\"\r\n                @click=\"handelSheet(row)\"\r\n              >{{ row.Rectifier_Code }}</el-button>\r\n              <span v-else>-</span>\r\n            </template>\r\n            <template slot=\"op\" slot-scope=\"{ row }\">\r\n              <el-button\r\n                v-if=\"row.Status != '草稿'\"\r\n                type=\"text\"\r\n                @click=\"handelView(row)\"\r\n              >查看</el-button>\r\n            </template>\r\n          </DynamicDataTable>\r\n        </el-main>\r\n      </div>\r\n    </div>-->\r\n    <div class=\"cs-bottom-wapper\">\r\n      <div class=\"fff tb-x\">\r\n        <vxe-table\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          :loading=\"loading\"\r\n          element-loading-spinner=\"el-icon-loading\"\r\n          element-loading-text=\"拼命加载中\"\r\n          empty-text=\"暂无数据\"\r\n          class=\"cs-vxe-table\"\r\n          height=\"100%\"\r\n          align=\"left\"\r\n          stripe\r\n          :data=\"tbData\"\r\n          resizable\r\n          :tooltip-config=\"{ enterable: true}\"\r\n          :checkbox-config=\"{checkField: 'checked', trigger: 'row'}\"\r\n          :row-config=\"{ isHover: true }\"\r\n          @checkbox-all=\"multiSelectedChange\"\r\n          @checkbox-change=\"multiSelectedChange\"\r\n        >\r\n          <vxe-column fixed=\"left\" type=\"checkbox\" width=\"44\" />\r\n          <template v-for=\"item in columns\">\r\n            <vxe-column\r\n              :key=\"item.Code\"\r\n              :min-width=\"item.Width\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :align=\"item.Align\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n            >\r\n              <template v-if=\"item.Code === 'Rectifier_Code' \" #default=\"{ row }\">\r\n                <el-button\r\n                  v-if=\"Boolean(row.Rectifier_Code)\"\r\n                  type=\"text\"\r\n                  @click=\"handelSheet(row)\"\r\n                >{{ row.Rectifier_Code }}</el-button>\r\n                <span v-else>-</span>\r\n              </template>\r\n              <template v-if=\"item.Code === 'Check_Result' \" #default=\"{ row }\">\r\n                <span v-if=\"!row.Check_Result\">-</span>\r\n                <template v-else>\r\n                  <el-tag v-if=\"row.Check_Result==='合格'\" type=\"success\">{{ row.Check_Result }}</el-tag>\r\n                  <el-tag v-else type=\"danger\">{{ row.Check_Result }}</el-tag>\r\n                </template>\r\n              </template>\r\n              <template v-else-if=\"item.Code === 'Status'\" #default=\"{ row }\">\r\n                <span v-if=\"row.Status === '已完成'\" class=\"by-dot by-dot-success\">\r\n                  {{ row.Status || \"-\" }}\r\n                </span>\r\n                <span v-else-if=\"row.Status === '待复核' || row.Status === '待整改'\" class=\"by-dot by-dot-primary\">\r\n                  {{ row.Status || \"-\" }}\r\n                </span>\r\n                <span v-else-if=\"row.Status === '待质检' || row.Status === '草稿'\" class=\"by-dot by-dot-info\">\r\n                  {{ row.Status || \"-\" }}\r\n                </span>\r\n                <span v-else>\r\n                  {{ row.Status || \"-\" }}\r\n                </span>\r\n              </template>\r\n              <template v-else #default=\"{ row }\">\r\n                <span>{{ row[item.Code] | displayValue }}</span>\r\n              </template>\r\n            </vxe-column>\r\n          </template>\r\n          <vxe-column fixed=\"right\" title=\"操作\" align=\"center\" width=\"60\">\r\n            <template #default=\"{ row }\">\r\n              <el-button\r\n                v-if=\"row.Status != '草稿'\"\r\n                type=\"text\"\r\n                @click=\"handelView(row)\"\r\n              >查看</el-button>\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n      </div>\r\n      <div class=\"data-info\">\r\n        <el-tag\r\n          size=\"medium\"\r\n          class=\"info-x\"\r\n        >已选 {{ selectList.length }} 条数据\r\n        </el-tag>\r\n        <Pagination\r\n          :total=\"total\"\r\n          :page-sizes=\"tablePageSize\"\r\n          :page.sync=\"queryInfo.Page\"\r\n          :limit.sync=\"queryInfo.PageSize\"\r\n          @pagination=\"pageChange\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      ref=\"content\"\r\n      v-el-drag-dialog\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"width\"\r\n      class=\"plm-custom-dialog\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component :is=\"currentComponent\" ref=\"content\" @close=\"handleClose\" />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport InspectDoc from './add/InspectDoc.vue'\r\nimport rectificationSheet from './rectification/rectificationSheet'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { GetPageQualitySummary } from '@/api/PRO/qualityInspect/start-Inspect'\r\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'\r\nimport elDragDialog from '@/directive/el-drag-dialog'\r\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\r\nimport { timeFormat } from '@/filters'\r\nimport { ExportInspsectionSummaryInfo } from '@/api/PRO/factorycheck'\r\nimport { combineURL } from '@/utils'\r\nimport Pagination from '@/components/Pagination/index.vue'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nexport default {\r\n  directives: { elDragDialog },\r\n  components: {\r\n    Pagination,\r\n    DynamicDataTable,\r\n    rectificationSheet,\r\n    InspectDoc\r\n  },\r\n  mixins: [getTbInfo],\r\n  props: {\r\n    searchDetail: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      width: '60%',\r\n      code: '',\r\n      TypeId: '',\r\n      typeOption: '',\r\n      dialogVisible: false,\r\n      loading: false,\r\n      dialogTitle: '',\r\n      Ismodal: true,\r\n      dialogData: {},\r\n      currentComponent: '',\r\n      tbConfig: {\r\n      },\r\n      Data: [],\r\n      columns: [],\r\n      tbData: [],\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: tablePageSize[0]\r\n      },\r\n      tablePageSize: tablePageSize,\r\n      total: 0,\r\n      gridCode: 'Pro_Inpection_summary_list_spot',\r\n      searchHeight: 0,\r\n      CheckResultData: [],\r\n      CheckNodeList: [], // 质检节点\r\n      CheckObjectData: [], // 质检对象\r\n      check_object_id: '',\r\n      ProjectNameData: [],\r\n      check_object_Name: '',\r\n      selectList: []\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getTypeList()\r\n  },\r\n  methods: {\r\n    handleSearch() {},\r\n    async getTypeList() {\r\n      let res, data\r\n      res = await GetFactoryProfessionalByCode({\r\n        factoryId: localStorage.getItem('CurReferenceId')\r\n      })\r\n      data = res.Data\r\n      if (res.IsSucceed) {\r\n        this.typeOption = Object.freeze(data)\r\n        console.log(this.typeOption)\r\n        if (this.typeOption.length > 0) {\r\n          this.TypeId = this.typeOption[0]?.Id\r\n          this.code = this.typeOption.find((i) => i.Id === this.TypeId).Code\r\n          this.getTableConfig(this.gridCode + ',' + this.code)\r\n        }\r\n        this.fetchData(1)\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    fetchData(page) {\r\n      page && (this.queryInfo.Page = page)\r\n      this.loading = true\r\n      const SeachParams = JSON.parse(JSON.stringify(this.searchDetail))\r\n      const SteelName = SeachParams.SteelName.trim().replaceAll(' ', '\\n')\r\n      if (SeachParams.Pick_Date && SeachParams.Pick_Date.length === 2) {\r\n        SeachParams.BeginDate = SeachParams.Pick_Date[0]\r\n        SeachParams.EndDate = SeachParams.Pick_Date[1]\r\n      } else {\r\n        SeachParams.BeginDate = null\r\n        SeachParams.EndDate = null\r\n      }\r\n      GetPageQualitySummary({\r\n        pageInfo: this.queryInfo,\r\n        ...SeachParams,\r\n        SteelName,\r\n        Check_Style: 0\r\n      })\r\n        .then((res) => {\r\n          if (res.IsSucceed) {\r\n            return this.setGridData(res.Data)\r\n          }\r\n        })\r\n        .catch(console.error)\r\n        .finally(() => {\r\n          // 结束loading\r\n          this.loading = false\r\n        })\r\n    },\r\n    handelSheet(row) {\r\n      console.log(row)\r\n      this.generateComponent('整改单', 'rectificationSheet')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(this.code, row)\r\n      })\r\n    },\r\n    handelView(row) {\r\n      this.generateComponent('查看质检单', 'inspectDoc')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(this.code, row)\r\n      })\r\n    },\r\n\r\n    setGridData(data) {\r\n      this.tbData = this.tbData = data.Data.map((v) => {\r\n        v.Id = v.SheetId // 解决全选框打勾问题\r\n        v.Rectify_Date = v.Rectify_Date\r\n          ? timeFormat(v.Rectify_Date, '{y}-{m}-{d}')\r\n          : '-'\r\n        v.Pick_Date = v.Pick_Date\r\n          ? timeFormat(v.Pick_Date, '{y}-{m}-{d}')\r\n          : '-'\r\n        return v\r\n      })\r\n\r\n      this.total = data.TotalCount\r\n    },\r\n\r\n    multiSelectedChange(array) {\r\n      this.selectList = array.records\r\n      this.$emit('selectChange', this.selectList)\r\n    },\r\n    generateComponent(title, component) {\r\n      this.dialogTitle = title\r\n      this.currentComponent = component\r\n      this.dialogVisible = true\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n      this.fetchData(1)\r\n    },\r\n    exportTb() {\r\n      const SheetIds = this.selectList.map((v) => v.SheetId)\r\n      this.$emit('setExportLoading', true)\r\n      const SeachParams = JSON.parse(JSON.stringify(this.searchDetail))\r\n      const SteelName = SeachParams.SteelName.trim().replaceAll(' ', '\\n')\r\n      if (SeachParams.Pick_Date && SeachParams.Pick_Date.length === 2) {\r\n        SeachParams.BeginDate = SeachParams.Pick_Date[0]\r\n        SeachParams.EndDate = SeachParams.Pick_Date[1]\r\n      } else {\r\n        SeachParams.BeginDate = null\r\n        SeachParams.EndDate = null\r\n      }\r\n      ExportInspsectionSummaryInfo({\r\n        pageInfo: this.queryInfo,\r\n        ...SeachParams,\r\n        SteelName,\r\n        Check_Style: 0,\r\n        SheetIds\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.$emit('setExportLoading', false)\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/styles/mixin.scss\";\r\n@import \"~@/styles/variables.scss\";\r\n.search_wrapper {\r\n  padding: 16px 16px 0;\r\n  box-sizing: border-box;\r\n  ::v-deep .el-form-item {\r\n    .el-form-item__content {\r\n      & > .el-input {\r\n        width: 220px;\r\n      }\r\n      & > .el-select {\r\n        width: 220px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.cs-bottom-wapper {\r\n  padding: 0 16px;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .tb-x {\r\n    flex: 1;\r\n    height: 0;\r\n  }\r\n\r\n  .pagination-container {\r\n    text-align: right;\r\n    padding: 16px;\r\n    margin: 0;\r\n  }\r\n\r\n  .data-info {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n}\r\n\r\n.by-dot {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  &:before {\r\n    content: \"\";\r\n    display: inline-block;\r\n    width: 5px;\r\n    height: 5px;\r\n    background: #f56c6c;\r\n    border-radius: 50%;\r\n    margin-right: 5px;\r\n  }\r\n}\r\n.by-dot-success {\r\n  color: #67c23a;\r\n  &:before {\r\n    background: #67c23a;\r\n  }\r\n}\r\n.by-dot-primary {\r\n  color: #409eff;\r\n  &:before {\r\n    background: #409eff;\r\n  }\r\n}\r\n.by-dot-fail {\r\n  color: #ff0000;\r\n  &:before {\r\n    background: #ff0000;\r\n  }\r\n}\r\n.by-dot-info {\r\n  &:before {\r\n    background: #909399;\r\n  }\r\n}\r\n</style>\r\n"]}]}
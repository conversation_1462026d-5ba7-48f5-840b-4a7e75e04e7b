{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\template-print\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\template-print\\detail.vue", "mtime": 1757468128158}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBoaXByaW50LCBoaVByaW50UGx1Z2luIH0gZnJvbSAndnVlLXBsdWdpbi1oaXByaW50Jw0KaW1wb3J0IHByb3ZpZGVycyBmcm9tICcuL3Byb3ZpZGVycycNCmltcG9ydCB7DQogIERlbGV0ZVByaW50VGVtcGxhdGUsDQogIEdldFByaW50VGVtcGxhdGVFbnRpdHksDQogIEdldFByaW50VGVtcGxhdGVMaXN0LA0KICBTYXZlUHJpbnRUZW1wbGF0ZUVudGl0eQ0KfSBmcm9tICdAL2FwaS9QUk8vc2hpcG1lbnQvc2hpcC10ZW1wbGF0ZS1wcmludCcNCmltcG9ydCB7IHBhcGVyVHlwZXMgfSBmcm9tICcuL2NvbmZpZycNCmltcG9ydCB7IEdldFByZWZlcmVuY2VTZXR0aW5nVmFsdWUgfSBmcm9tICdAL2FwaS9zeXMvc3lzdGVtLXNldHRpbmcnDQppbXBvcnQgaHRtbDJjYW52YXMgZnJvbSAnaHRtbDJjYW52YXMnDQppbXBvcnQgeyBHZXRDb21wYW55IH0gZnJvbSAnQC9hcGkvcGxtL3NpdGUnDQppbXBvcnQgeyBkZWVwQ2xvbmUgfSBmcm9tICdAL3V0aWxzJw0KDQpoaVByaW50UGx1Z2luLmRpc0F1dG9Db25uZWN0KCkNCg0KbGV0IGhpcHJpbnRUZW1wbGF0ZQ0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnU2hpcFRlbXBsYXRlUHJpbnREZXRhaWwnLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDlvZPliY3nurjlvKANCiAgICAgIGN1clBhcGVyOiB7DQogICAgICAgIHR5cGU6ICdBNCcsDQogICAgICAgIHdpZHRoOiAyMTAsDQogICAgICAgIGhlaWdodDogMjk2LjYNCiAgICAgIH0sDQogICAgICBjdXJQYXBlclR5cGU6ICdBNCcsDQogICAgICAvLyDnurjlvKDnsbvlnosNCiAgICAgIHBhcGVyVHlwZXM6IGRlZXBDbG9uZShwYXBlclR5cGVzKSwNCiAgICAgIC8vIOiHquWumuS5iee6uOW8oA0KICAgICAgcGFwZXJXaWR0aDogJzIyMCcsDQogICAgICBwYXBlckhlaWdodDogJzgwJywNCiAgICAgIHRtcGxMaXN0OiBbXSwNCiAgICAgIG1vZGU6IDEsDQogICAgICBhY3RpdmVJbmRleDogJycsDQogICAgICBrZXl3b3JkOiAnJywNCiAgICAgIHRvRWRpdDogJycsDQogICAgICBmb3JtOiB7DQogICAgICAgIE5hbWU6ICcnLA0KICAgICAgICBUeXBlOiAxLCAvLyAxLeWPkei0p+WNlQ0KICAgICAgICBEYXRhOiAnJywNCiAgICAgICAgQmFzZTY0SW1hZ2U6ICcnDQogICAgICB9LA0KICAgICAgbG9nb1VybDogcmVxdWlyZSgnQC9hc3NldHMvbG9nby1pbm5lci5wbmcnKSwNCiAgICAgIC8vIOe8qeaUvg0KICAgICAgc2NhbGVWYWx1ZTogMSwNCiAgICAgIHNhdmVMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHNjYWxlTWF4OiA1LA0KICAgICAgc2NhbGVNaW46IDAuNQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICBmaWx0ZXJlZFRtcGxMaXN0KCkgew0KICAgICAgcmV0dXJuIHRoaXMudG1wbExpc3QuZmlsdGVyKHQgPT4gdC5OYW1lLmluZGV4T2YodGhpcy5rZXl3b3JkKSA+IC0xKQ0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmluaXQoKQ0KICAgIC8qKg0KICAgICAgICog6L+Z6YeM5b+F6aG76KaB5ZyoIG1vdW50ZWQg5Lit5Y675p6E5bu6IOW3puS+p+WPr+aLluaLveWFg+e0oCDmiJbogIUg6K6+6K6h5ZmoDQogICAgICAgKiDlm6DkuLrpg73mmK/miorlhYPntKDmjILovb3liLDlr7nlupTlrrnlmajkuK0sIOW/hemhu+imgeWFiOaJvuWIsOivpeWuueWZqA0KICAgICAgICovDQogICAgdGhpcy5idWlsZExlZnRFbGVtZW50KCkNCiAgICB0aGlzLmJ1aWxkRGVzaWduZXIoKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgY2hhbmdlU2NhbGUoYmlnKSB7DQogICAgICBsZXQgc2NhbGVWYWx1ZSA9IHRoaXMuc2NhbGVWYWx1ZQ0KICAgICAgaWYgKGJpZykgew0KICAgICAgICBzY2FsZVZhbHVlICs9IDAuMQ0KICAgICAgICBpZiAoc2NhbGVWYWx1ZSA+IHRoaXMuc2NhbGVNYXgpIHNjYWxlVmFsdWUgPSA1DQogICAgICB9IGVsc2Ugew0KICAgICAgICBzY2FsZVZhbHVlIC09IDAuMQ0KICAgICAgICBpZiAoc2NhbGVWYWx1ZSA8IHRoaXMuc2NhbGVNaW4pIHNjYWxlVmFsdWUgPSAwLjUNCiAgICAgIH0NCiAgICAgIGlmIChoaXByaW50VGVtcGxhdGUpIHsNCiAgICAgICAgLy8gc2NhbGVWYWx1ZTog5pS+5aSn57yp5bCP5YC8LCBmYWxzZTog5LiN5L+d5a2YKOS4jeS8oOS5n+S4gOagtyksIOWmguaenOS8oCB0cnVlLCDmiZPljbDml7bkuZ/kvJrmlL7lpKcNCiAgICAgICAgaGlwcmludFRlbXBsYXRlLnpvb20oc2NhbGVWYWx1ZSkNCiAgICAgICAgdGhpcy5zY2FsZVZhbHVlID0gc2NhbGVWYWx1ZQ0KICAgICAgfQ0KICAgIH0sDQogICAgYXN5bmMgZ2V0SW1hZ2UoKSB7DQogICAgICB0cnkgew0KICAgICAgICByZXR1cm4gYXdhaXQgbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4gew0KICAgICAgICAgIC8vIOWcqOa1j+iniOWZqOepuumXsuaXtuaJp+ihjOaIquWbvuaTjeS9nA0KICAgICAgICAgIGNvbnN0IGV4ZWN1dGVDYXB0dXJlID0gYXN5bmMoKSA9PiB7DQogICAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgICBjb25zdCBjYW52YXMgPSBhd2FpdCBodG1sMmNhbnZhcyhkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnaGlwcmludC1wcmludFRlbXBsYXRlJyksIHsNCiAgICAgICAgICAgICAgICB1c2VDT1JTOiB0cnVlLA0KICAgICAgICAgICAgICAgIGxvZ2dpbmc6IGZhbHNlLCAvLyDlhbPpl63ml6Xlv5fovpPlh7oNCiAgICAgICAgICAgICAgICByZW1vdmVDb250YWluZXI6IHRydWUsIC8vIOiHquWKqOa4heeQhuS4tOaXtuWuueWZqA0KICAgICAgICAgICAgICAgIG9uY2xvbmU6IChjbG9uZWREb2MpID0+IHsNCiAgICAgICAgICAgICAgICAgIC8vIOWcqOWFi+mahuaWh+aho+S4reenu+mZpOS4jeW/heimgeeahOWFg+e0oA0KICAgICAgICAgICAgICAgICAgY29uc3QgY2xvbmVkRWxlbWVudCA9IGNsb25lZERvYy5nZXRFbGVtZW50QnlJZCgnaGlwcmludC1wcmludFRlbXBsYXRlJykNCiAgICAgICAgICAgICAgICAgIGlmIChjbG9uZWRFbGVtZW50KSB7DQogICAgICAgICAgICAgICAgICAgIC8vIOenu+mZpOWKqOeUu+OAgeinhumikeetieiAl+aAp+iDveeahOWFg+e0oA0KICAgICAgICAgICAgICAgICAgICBjb25zdCBhbmltYXRpb25zID0gY2xvbmVkRWxlbWVudC5xdWVyeVNlbGVjdG9yQWxsKCdbY2xhc3MqPSJhbmltYXRlIl0nKQ0KICAgICAgICAgICAgICAgICAgICBhbmltYXRpb25zLmZvckVhY2goZWwgPT4gZWwucmVtb3ZlKCkpDQogICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICBjb25zdCBkYXRhVXJsID0gY2FudmFzLnRvRGF0YVVSTCgnaW1hZ2UvcG5nJykNCiAgICAgICAgICAgICAgcmVzb2x2ZShkYXRhVXJsKQ0KICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgICAgICAgcmVqZWN0KGVycm9yKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOS9v+eUqCByZXF1ZXN0SWRsZUNhbGxiYWNrIOWcqOa1j+iniOWZqOepuumXsuaXtuaJp+ihjA0KICAgICAgICAgIGlmICh3aW5kb3cucmVxdWVzdElkbGVDYWxsYmFjaykgew0KICAgICAgICAgICAgd2luZG93LnJlcXVlc3RJZGxlQ2FsbGJhY2soZXhlY3V0ZUNhcHR1cmUsIHsgdGltZW91dDogNTAwMCB9KQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBzZXRUaW1lb3V0KGV4ZWN1dGVDYXB0dXJlLCAwKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0gZmluYWxseSB7DQogICAgICB9DQogICAgfSwNCiAgICBnZXRMb2dvKCkgew0KICAgICAgR2V0Q29tcGFueSgpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgdGhpcy5sb2dvVXJsID0gcmVzLkRhdGEuSWNvbg0KICAgICAgfSkNCiAgICB9LA0KICAgIHRtcGxTZWxlY3QoaWQpIHsNCiAgICAgIHRoaXMudG9FZGl0ID0gJycNCiAgICAgIHRoaXMuYWN0aXZlSW5kZXggPSBpZA0KICAgICAgaWYgKHRoaXMuZm9ybSAmJiB0aGlzLmZvcm0uSWQgPT09IGlkKSByZXR1cm4NCiAgICAgIHRoaXMubG9hZFRlbXBsYXRlKGlkKQ0KICAgIH0sDQogICAgYXN5bmMgY2xvbmVUZW1wbGF0ZSgpIHsNCiAgICAgIGNvbnN0IGNvcHlUZW1wbGF0ZSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy5mb3JtKSkNCiAgICAgIGRlbGV0ZSBjb3B5VGVtcGxhdGUuSWQNCiAgICAgIGF3YWl0IFNhdmVQcmludFRlbXBsYXRlRW50aXR5KGNvcHlUZW1wbGF0ZSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5aSN5Yi25oiQ5YqfJykNCiAgICAgICAgICB0aGlzLmdldFRlbXBsYXRlTGlzdCgpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMuTWVzc2FnZSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGRlbGV0ZVRlbXBsYXRlKGlkKSB7DQogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbliKDpmaTmiYDpgInlhoXlrrknLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICBjZW50ZXI6IHRydWUNCiAgICAgIH0pDQogICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICBEZWxldGVQcmludFRlbXBsYXRlKHsgaWQgfSkudGhlbihyZXMgPT4gew0KICAgICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQ0KICAgICAgICAgICAgICB0aGlzLmdldFRlbXBsYXRlTGlzdCgpDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5NZXNzYWdlKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgICAgaWYgKHRoaXMudG9FZGl0KSB7DQogICAgICAgICAgICB0aGlzLnRvRWRpdCA9ICcnDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgdHlwZTogJ2luZm8nLA0KICAgICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iOWIoOmZpCcNCiAgICAgICAgICB9KQ0KICAgICAgICB9KQ0KICAgIH0sDQogICAgYXN5bmMgaW5pdCgpIHsNCiAgICAgIC8vIOWIneWni+WMliBwcm92aWRlcg0KICAgICAgdGhpcy5nZXRMb2dvKCkNCiAgICAgIGNvbnN0IHByb3ZpZGVyID0gcHJvdmlkZXJzLmZpbmQoaSA9PiBpLnZhbHVlID09IHRoaXMubW9kZSkNCiAgICAgIGhpcHJpbnQuaW5pdCh7DQogICAgICAgIHByb3ZpZGVyczogW3Byb3ZpZGVyLmZdDQogICAgICB9KQ0KICAgICAgdGhpcy5nZXRUZW1wbGF0ZUxpc3QoKQ0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgICAqIOaehOW7uuW3puS+p+WPr+aLluaLveWFg+e0oA0KICAgICAgICog5rOo5oSPOiDlj6/mi5bmi73lhYPntKDlv4XpobvlnKggaGlwcmludC5pbml0KCkg5LmL5ZCO6LCD55SoDQogICAgICAgKiDogIzkuJQg5b+F6aG75YyF5ZCrIGNsYXNzPSJlcC1kcmFnZ2FibGUtaXRlbSIg5ZCm5YiZ5peg5rOV5ouW5ou96L+b6K6+6K6h5ZmoDQogICAgICAgKi8NCiAgICBidWlsZExlZnRFbGVtZW50KCkgew0KICAgICAgaGlwcmludC5QcmludEVsZW1lbnRUeXBlTWFuYWdlci5idWlsZEJ5SHRtbCgkKCcuZXAtZHJhZ2dhYmxlLWl0ZW0nKSkNCiAgICB9LA0KICAgIGJ1aWxkRGVzaWduZXIodGVtcGxhdGUgPSB7fSkgew0KICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXVuZGVmDQogICAgICAkKCcjaGlwcmludC1wcmludFRlbXBsYXRlJykuZW1wdHkoKSAvLyDlhYjmuIXnqbosIOmBv+WFjemHjeWkjeaehOW7ug0KICAgICAgaGlwcmludFRlbXBsYXRlID0gbmV3IGhpcHJpbnQuUHJpbnRUZW1wbGF0ZSh7DQogICAgICAgIHRlbXBsYXRlLA0KICAgICAgICBzZXR0aW5nQ29udGFpbmVyOiAnI1ByaW50RWxlbWVudE9wdGlvblNldHRpbmcnIC8vIOWFg+e0oOWPguaVsOWuueWZqA0KICAgICAgfSkNCiAgICAgIC8vIOaehOW7uiDlubbloavlhYXliLAg5a655Zmo5LitDQogICAgICBoaXByaW50VGVtcGxhdGUuZGVzaWduKCcjaGlwcmludC1wcmludFRlbXBsYXRlJykNCiAgICB9LA0KICAgIGhhbmRsZVByaW50KCkgew0KICAgICAgLy8g5omT5Y2w5pWw5o2u77yMa2V5IOWvueW6lCDlhYPntKDnmoQg5a2X5q615ZCNDQogICAgICBjb25zdCBwcmludERhdGEgPSB7fQ0KICAgICAgY29uc3QgcHJpbnRBcnIgPSBoaXByaW50VGVtcGxhdGUuZ2V0SnNvbigpLnBhbmVsc1swXS5wcmludEVsZW1lbnRzDQogICAgICBwcmludEFyci5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICBpZiAoaXRlbS5vcHRpb25zLmZpZWxkID09ICdUYWJsZScpIHsNCiAgICAgICAgICBwcmludERhdGFbaXRlbS5vcHRpb25zLmZpZWxkXSA9IEpTT04ucGFyc2UoaXRlbS5vcHRpb25zLnRlc3REYXRhKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUubG9nKGl0ZW0pDQogICAgICAgICAgcHJpbnREYXRhW2l0ZW0ub3B0aW9ucy5maWVsZF0gPSBpdGVtLm9wdGlvbnMudGVzdERhdGEgfHwgaXRlbS5vcHRpb25zDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICBjb25zb2xlLmxvZyhwcmludERhdGEpDQoNCiAgICAgIC8vIGxldCBwcmludERhdGEgPSBoaXByaW50VGVtcGxhdGUuZ2V0SnNvbigpDQogICAgICAvLyDlj4LmlbA6IOaJk+WNsOaXtuiuvue9riDlt6blgY/np7vph4/vvIzkuIrlgY/np7vph48NCiAgICAgIGNvbnN0IG9wdGlvbnMgPSB7IGxlZnRPZmZzZXQ6IC0xLCB0b3BPZmZzZXQ6IC0xIH0NCiAgICAgIC8vIOaJqeWxlQ0KICAgICAgY29uc3QgZXh0ID0gew0KICAgICAgICBjYWxsYmFjazogKCkgPT4gew0KICAgICAgICAgIGNvbnNvbGUubG9nKCfmtY/op4jlmajmiZPljbDnqpflj6Plt7LmiZPlvIAnKQ0KICAgICAgICB9DQogICAgICAgIC8vIHN0eWxlSGFuZGxlcjogKCkgPT4gew0KICAgICAgICAvLyAgIC8vIOmHjeWGmSDmlofmnKwg5omT5Y2w5qC35byPDQogICAgICAgIC8vICAgcmV0dXJuICI8c3R5bGU+LmhpcHJpbnQtcHJpbnRFbGVtZW50LXRleHR7Y29sb3I6cmVkICFpbXBvcnRhbnQ7fTwvc3R5bGU+IjsNCiAgICAgICAgLy8gfQ0KICAgICAgfQ0KICAgICAgLy8g6LCD55So5rWP6KeI5Zmo5omT5Y2wDQogICAgICBoaXByaW50VGVtcGxhdGUucHJpbnQocHJpbnREYXRhLCBvcHRpb25zLCBleHQpDQogICAgfSwNCiAgICBjaGFuZ2VQYXBlcigpIHsNCiAgICAgIGNvbnN0IHRlbXAgPSB0aGlzLnBhcGVyVHlwZXMuZmluZChpID0+IGkudHlwZSA9PT0gdGhpcy5jdXJQYXBlclR5cGUpDQogICAgICBpZiAodGhpcy5jdXJQYXBlclR5cGUgPT09ICfoh6rlrprkuYnnurjlvKAnKSB7DQogICAgICAgIGhpcHJpbnRUZW1wbGF0ZS5zZXRQYXBlcih0aGlzLnBhcGVyV2lkdGgsIHRoaXMucGFwZXJIZWlnaHQpDQogICAgICB9IGVsc2Ugew0KICAgICAgICBoaXByaW50VGVtcGxhdGUuc2V0UGFwZXIodGVtcC53aWR0aCwgdGVtcC5oZWlnaHQpDQogICAgICB9DQogICAgfSwNCiAgICAvLyDmlrDlu7rmqKHmnb8NCiAgICBjcmVhdGVUZW1wbGF0ZSgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgTmFtZTogJycsDQogICAgICAgIFR5cGU6IDEsIC8vIDEt5Y+R6LSn5Y2VDQogICAgICAgIERhdGE6ICcnLA0KICAgICAgICBCYXNlNjRJbWFnZTogJycNCiAgICAgIH0NCiAgICAgIHRoaXMuY2xlYXJUZW1wbGF0ZSgpDQogICAgfSwNCiAgICAvLyDkv53lrZjmqKHmnb8NCiAgICBhc3luYyBzYXZlVGVtcGxhdGUoKSB7DQogICAgICB0aGlzLnNhdmVMb2FkaW5nID0gdHJ1ZQ0KICAgICAgdHJ5IHsNCiAgICAgICAgdGhpcy5mb3JtLkJhc2U2NEltYWdlID0gYXdhaXQgdGhpcy5nZXRJbWFnZSgpDQogICAgICAgIGlmICghdGhpcy5mb3JtLk5hbWUpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfor7fovpPlhaXmqKHmnb/lkI3np7AnKQ0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICAgIGNvbnN0IGpzb24gPSBoaXByaW50VGVtcGxhdGUuZ2V0SnNvbigpDQogICAgICAgIHRoaXMuZm9ybS5EYXRhID0gSlNPTi5zdHJpbmdpZnkoanNvbikNCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgU2F2ZVByaW50VGVtcGxhdGVFbnRpdHkodGhpcy5mb3JtKQ0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5L+d5a2Y5oiQ5YqfJykNCiAgICAgICAgICB0aGlzLmdldFRlbXBsYXRlTGlzdCgpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMuTWVzc2FnZSkNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5L+d5a2Y5aSx6LSlJykNCiAgICAgICAgY29uc29sZS5lcnJvcign5L+d5a2Y5qih5p2/5aSx6LSlOicsIGVycm9yKQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5zYXZlTG9hZGluZyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCiAgICAvLyDliqDovb3mqKHmnb8NCiAgICBhc3luYyBsb2FkVGVtcGxhdGUoaWQpIHsNCiAgICAgIHRoaXMuY2xlYXJUZW1wbGF0ZSgpDQogICAgICBjb25zdCByZXMgPSBhd2FpdCBHZXRQcmludFRlbXBsYXRlRW50aXR5KHsgaWQgfSkNCiAgICAgIHRoaXMuZm9ybSA9IHJlcy5EYXRhDQogICAgICBjb25zdCBwYXJzZURhdGEgPSBKU09OLnBhcnNlKHJlcy5EYXRhLkRhdGEpDQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBpbmRleCA9IHBhcnNlRGF0YS5wYW5lbHNbMF0ucHJpbnRFbGVtZW50cy5maW5kSW5kZXgoaSA9PiBpLm9wdGlvbnMuZmllbGQgPT09ICdMb2dvJykNCiAgICAgICAgcGFyc2VEYXRhLnBhbmVsc1swXS5wcmludEVsZW1lbnRzW2luZGV4XS5vcHRpb25zLnNyYyA9IHRoaXMubG9nb1VybA0KICAgICAgfSBjYXRjaCAoZSkge30NCiAgICAgIGNvbnNvbGUubG9nKCkNCiAgICAgIGNvbnN0IHRlbXBsYXRlID0gcGFyc2VEYXRhDQogICAgICB0aGlzLmJ1aWxkRGVzaWduZXIodGVtcGxhdGUpDQoNCiAgICAgIC8vIOWMuemFjee6uOW8oA0KICAgICAgY29uc3QgeyB3aWR0aCwgaGVpZ2h0IH0gPSB0ZW1wbGF0ZS5wYW5lbHNbMF0NCiAgICAgIGNvbnN0IG1hdGNoZWRQYXBlciA9IHRoaXMucGFwZXJUeXBlcy5maW5kKGkgPT4gaS53aWR0aCA9PSB3aWR0aCAmIGkuaGVpZ2h0ID09IGhlaWdodCkNCiAgICAgIGlmIChtYXRjaGVkUGFwZXIpIHsNCiAgICAgICAgdGhpcy5jdXJQYXBlciA9IG1hdGNoZWRQYXBlcg0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5jdXJQYXBlciA9IHsNCiAgICAgICAgICB0eXBlOiAn6Ieq5a6a5LmJ57q45bygJywNCiAgICAgICAgICB3aWR0aCwNCiAgICAgICAgICBoZWlnaHQNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgdGhpcy5jdXJQYXBlclR5cGUgPSB0aGlzLmN1clBhcGVyLnR5cGUNCiAgICAgIHRoaXMucGFwZXJXaWR0aCA9IHdpZHRoDQogICAgICB0aGlzLnBhcGVySGVpZ2h0ID0gaGVpZ2h0DQogICAgICB0aGlzLmNoYW5nZVBhcGVyKCkNCiAgICB9LA0KICAgIC8vIOa4heepuuaooeadvw0KICAgIGNsZWFyVGVtcGxhdGUoKSB7DQogICAgICAkKCcjaGlwcmludC1wcmludFRlbXBsYXRlJykuZW1wdHkoKSAvLyDlhYjmuIXnqbosIOmBv+WFjemHjeWkjeaehOW7ug0KICAgICAgdGhpcy5idWlsZERlc2lnbmVyKCkNCiAgICB9LA0KDQogICAgYXN5bmMgZ2V0VGVtcGxhdGVMaXN0KCkgew0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0UHJpbnRUZW1wbGF0ZUxpc3Qoew0KICAgICAgICB0eXBlOiB0aGlzLm1vZGUNCiAgICAgIH0pDQogICAgICB0aGlzLnRtcGxMaXN0ID0gcmVzLkRhdGENCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/PRO/shipment/template-print", "sourcesContent": ["<template>\r\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\r\n    <div style=\"display:flex;height:100%;\">\r\n      <el-aside\r\n        class=\"cs-z-page-main-content\"\r\n        style=\"background:#FFF;margin-right:16px;width: 20vw;min-width:320px;\"\r\n      >\r\n        <el-row :gutter=\"4\" style=\"flex-shrink:0;\">\r\n          <el-col :span=\"17\">\r\n            <el-input\r\n              v-model=\"keyword\"\r\n              placeholder=\"请输入内容\"\r\n              suffix-icon=\"el-icon-search\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"7\">\r\n            <el-button type=\"primary\" @click=\"createTemplate\">新建模板</el-button>\r\n          </el-col>\r\n        </el-row>\r\n        <div class=\"tmpl-list\">\r\n          <el-menu\r\n            class=\"tmpl-menu\"\r\n            :default-active=\"String(activeIndex)\"\r\n          >\r\n            <el-menu-item\r\n              v-for=\"tmpl in filteredTmplList\"\r\n              :key=\"tmpl.Id\"\r\n              :index=\"tmpl.Id\"\r\n              style=\"padding-left:12px;\"\r\n              :title=\"tmpl.Name\"\r\n            >\r\n              <div\r\n                style=\"overflow:hidden;max-width:220px;text-overflow: ellipsis;\"\r\n                @click.stop=\"tmplSelect(tmpl.Id)\"\r\n              >\r\n                <i class=\"el-icon-document\" />{{ tmpl.Name }}\r\n              </div>\r\n              <template v-if=\"String(activeIndex) === tmpl.Id\">\r\n                <!--                <el-link-->\r\n                <!--                  :underline=\"false\"-->\r\n                <!--                  type=\"primary\"-->\r\n                <!--                  @click.stop=\"toEdit = tmpl.Id\"-->\r\n                <!--                >-->\r\n                <!--                  <i class=\"right-align-icon el-icon-edit\" />-->\r\n                <!--                </el-link>-->\r\n                <el-link\r\n                  :underline=\"false\"\r\n                  type=\"danger\"\r\n                  @click=\"deleteTemplate(tmpl.Id)\"\r\n                >\r\n                  <i class=\"right-align-icon el-icon-delete\" />\r\n                </el-link>\r\n                <el-link\r\n                  :underline=\"false\"\r\n                  type=\"primary\"\r\n                  @click=\"cloneTemplate(tmpl.Id)\"\r\n                >\r\n                  <i class=\"right-align-icon el-icon-copy-document\" />\r\n                </el-link>\r\n              </template>\r\n            </el-menu-item>\r\n          </el-menu>\r\n        </div>\r\n        <div class=\"flex-row justify-center flex-wrap\" style=\"display: flex;flex-wrap: wrap\">\r\n          <!-- tid 与 defaultElementTypeProvider 中对应 -->\r\n          <!-- 包含 class=\"ep-draggable-item\" -->\r\n          <div class=\"title\">标题区</div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Logo\">\r\n            Logo图片\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.QrcodeText\">\r\n            二维码\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.ContractNumber\">\r\n            内部合同编号\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Code\">\r\n            单据号\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.SendDate\">\r\n            日期\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Number\">\r\n            发货序号\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Address\">\r\n            项目地址\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.ReceivingUnit\">\r\n            收货单位\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.ProjectName\">\r\n            项目名称\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.MakerName\">\r\n            出库人\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Consignee\">\r\n            收货人\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.ConsigneeTel\">\r\n            联系电话\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.VehicleNo\">\r\n            车牌\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Telephone\">\r\n            司机电话\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Trips\">\r\n            车次\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.LoadingsName\">\r\n            装车班\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.LoadingsPersonnelName\">\r\n            装车班人员\r\n          </div>\r\n          <div class=\"title\">数据区</div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Table\">\r\n            构件/包数据表\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Pound_Weight\">\r\n            磅重（kg）\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Tare_Weight\">\r\n            皮重（kg）\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Net_Weight\">\r\n            净重（kg）\r\n          </div>\r\n          <div class=\"title\">其他</div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.customText\">\r\n            自定义文本\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.hline\">\r\n            横线\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.vline\">\r\n            竖线\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.rect\">\r\n            矩形\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.oval\">\r\n            椭圆\r\n          </div>\r\n        </div>\r\n      </el-aside>\r\n      <el-container class=\"cs-z-page-main-content\">\r\n        <div class=\"header\">\r\n          <el-button type=\"primary\" sizi=\"mini\" @click=\"handlePrint\">打印预览</el-button>\r\n          <el-button type=\"success\" sizi=\"mini\" :loading=\"saveLoading\" @click=\"saveTemplate\">保存模板</el-button>\r\n          <el-button type=\"danger\" sizi=\"mini\" @click=\"clearTemplate\">清空</el-button>\r\n          <span class=\"label\">模板名称</span>\r\n          <el-input v-model=\"form.Name\" style=\"width: 150px\" :maxlength=\"50\" />\r\n          <span class=\"label\">模板布局</span>\r\n          <el-select v-model=\"curPaperType\" style=\"width: 120px\" @change=\"changePaper\">\r\n            <el-option v-for=\"item in paperTypes\" :key=\"item.type\" :value=\"item.type\" :label=\"item.type\" />\r\n          </el-select>\r\n          <div v-if=\"curPaperType==='自定义纸张'\">\r\n            <span class=\"label\">宽</span>\r\n            <el-input v-model=\"paperWidth\" type=\"input\" class=\"input\" @change=\"changePaper\" />\r\n            <span class=\"label\">高</span>\r\n            <el-input v-model=\"paperHeight\" type=\"input\" class=\"input\" @change=\"changePaper\" />\r\n          </div>\r\n          <div style=\"display: flex;align-items: center;margin-left: 10px\">\r\n            <i class=\"el-icon-zoom-out zoom-btn\" @click=\"changeScale(false)\" />\r\n            <div class=\"zoom\">{{ ~~(scaleValue * 100) }}%</div>\r\n            <i class=\"el-icon-zoom-in zoom-btn\" @click=\"changeScale(true)\" />\r\n          </div>\r\n        </div>\r\n        <!-- 设计器的 容器 -->\r\n        <div style=\"margin-top: 10px;display: flex\">\r\n          <div style=\"flex:1;padding-left: 16px;padding-top: 16px;overflow: auto\">\r\n            <div id=\"hiprint-printTemplate\" />\r\n          </div>\r\n          <div class=\"hinnn-layout-sider\" style=\"width: 20vw;min-width: 300px;margin-left: 16px\">\r\n            <div id=\"PrintElementOptionSetting\" />\r\n          </div>\r\n        </div>\r\n      </el-container>\r\n    </div>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { hiprint, hiPrintPlugin } from 'vue-plugin-hiprint'\r\nimport providers from './providers'\r\nimport {\r\n  DeletePrintTemplate,\r\n  GetPrintTemplateEntity,\r\n  GetPrintTemplateList,\r\n  SavePrintTemplateEntity\r\n} from '@/api/PRO/shipment/ship-template-print'\r\nimport { paperTypes } from './config'\r\nimport { GetPreferenceSettingValue } from '@/api/sys/system-setting'\r\nimport html2canvas from 'html2canvas'\r\nimport { GetCompany } from '@/api/plm/site'\r\nimport { deepClone } from '@/utils'\r\n\r\nhiPrintPlugin.disAutoConnect()\r\n\r\nlet hiprintTemplate\r\nexport default {\r\n  name: 'ShipTemplatePrintDetail',\r\n  data() {\r\n    return {\r\n      // 当前纸张\r\n      curPaper: {\r\n        type: 'A4',\r\n        width: 210,\r\n        height: 296.6\r\n      },\r\n      curPaperType: 'A4',\r\n      // 纸张类型\r\n      paperTypes: deepClone(paperTypes),\r\n      // 自定义纸张\r\n      paperWidth: '220',\r\n      paperHeight: '80',\r\n      tmplList: [],\r\n      mode: 1,\r\n      activeIndex: '',\r\n      keyword: '',\r\n      toEdit: '',\r\n      form: {\r\n        Name: '',\r\n        Type: 1, // 1-发货单\r\n        Data: '',\r\n        Base64Image: ''\r\n      },\r\n      logoUrl: require('@/assets/logo-inner.png'),\r\n      // 缩放\r\n      scaleValue: 1,\r\n      saveLoading: false,\r\n      scaleMax: 5,\r\n      scaleMin: 0.5\r\n    }\r\n  },\r\n  computed: {\r\n    filteredTmplList() {\r\n      return this.tmplList.filter(t => t.Name.indexOf(this.keyword) > -1)\r\n    }\r\n  },\r\n  mounted() {\r\n    this.init()\r\n    /**\r\n       * 这里必须要在 mounted 中去构建 左侧可拖拽元素 或者 设计器\r\n       * 因为都是把元素挂载到对应容器中, 必须要先找到该容器\r\n       */\r\n    this.buildLeftElement()\r\n    this.buildDesigner()\r\n  },\r\n  methods: {\r\n    changeScale(big) {\r\n      let scaleValue = this.scaleValue\r\n      if (big) {\r\n        scaleValue += 0.1\r\n        if (scaleValue > this.scaleMax) scaleValue = 5\r\n      } else {\r\n        scaleValue -= 0.1\r\n        if (scaleValue < this.scaleMin) scaleValue = 0.5\r\n      }\r\n      if (hiprintTemplate) {\r\n        // scaleValue: 放大缩小值, false: 不保存(不传也一样), 如果传 true, 打印时也会放大\r\n        hiprintTemplate.zoom(scaleValue)\r\n        this.scaleValue = scaleValue\r\n      }\r\n    },\r\n    async getImage() {\r\n      try {\r\n        return await new Promise((resolve, reject) => {\r\n          // 在浏览器空闲时执行截图操作\r\n          const executeCapture = async() => {\r\n            try {\r\n              const canvas = await html2canvas(document.getElementById('hiprint-printTemplate'), {\r\n                useCORS: true,\r\n                logging: false, // 关闭日志输出\r\n                removeContainer: true, // 自动清理临时容器\r\n                onclone: (clonedDoc) => {\r\n                  // 在克隆文档中移除不必要的元素\r\n                  const clonedElement = clonedDoc.getElementById('hiprint-printTemplate')\r\n                  if (clonedElement) {\r\n                    // 移除动画、视频等耗性能的元素\r\n                    const animations = clonedElement.querySelectorAll('[class*=\"animate\"]')\r\n                    animations.forEach(el => el.remove())\r\n                  }\r\n                }\r\n              })\r\n              const dataUrl = canvas.toDataURL('image/png')\r\n              resolve(dataUrl)\r\n            } catch (error) {\r\n              reject(error)\r\n            }\r\n          }\r\n\r\n          // 使用 requestIdleCallback 在浏览器空闲时执行\r\n          if (window.requestIdleCallback) {\r\n            window.requestIdleCallback(executeCapture, { timeout: 5000 })\r\n          } else {\r\n            setTimeout(executeCapture, 0)\r\n          }\r\n        })\r\n      } finally {\r\n      }\r\n    },\r\n    getLogo() {\r\n      GetCompany().then(res => {\r\n        this.logoUrl = res.Data.Icon\r\n      })\r\n    },\r\n    tmplSelect(id) {\r\n      this.toEdit = ''\r\n      this.activeIndex = id\r\n      if (this.form && this.form.Id === id) return\r\n      this.loadTemplate(id)\r\n    },\r\n    async cloneTemplate() {\r\n      const copyTemplate = JSON.parse(JSON.stringify(this.form))\r\n      delete copyTemplate.Id\r\n      await SavePrintTemplateEntity(copyTemplate).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message.success('复制成功')\r\n          this.getTemplateList()\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    deleteTemplate(id) {\r\n      this.$confirm('是否删除所选内容', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        center: true\r\n      })\r\n        .then(() => {\r\n          DeletePrintTemplate({ id }).then(res => {\r\n            if (res.IsSucceed) {\r\n              this.$message.success('删除成功')\r\n              this.getTemplateList()\r\n            } else {\r\n              this.$message.error(res.Message)\r\n            }\r\n          })\r\n          if (this.toEdit) {\r\n            this.toEdit = ''\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n    async init() {\r\n      // 初始化 provider\r\n      this.getLogo()\r\n      const provider = providers.find(i => i.value == this.mode)\r\n      hiprint.init({\r\n        providers: [provider.f]\r\n      })\r\n      this.getTemplateList()\r\n    },\r\n\r\n    /**\r\n       * 构建左侧可拖拽元素\r\n       * 注意: 可拖拽元素必须在 hiprint.init() 之后调用\r\n       * 而且 必须包含 class=\"ep-draggable-item\" 否则无法拖拽进设计器\r\n       */\r\n    buildLeftElement() {\r\n      hiprint.PrintElementTypeManager.buildByHtml($('.ep-draggable-item'))\r\n    },\r\n    buildDesigner(template = {}) {\r\n      // eslint-disable-next-line no-undef\r\n      $('#hiprint-printTemplate').empty() // 先清空, 避免重复构建\r\n      hiprintTemplate = new hiprint.PrintTemplate({\r\n        template,\r\n        settingContainer: '#PrintElementOptionSetting' // 元素参数容器\r\n      })\r\n      // 构建 并填充到 容器中\r\n      hiprintTemplate.design('#hiprint-printTemplate')\r\n    },\r\n    handlePrint() {\r\n      // 打印数据，key 对应 元素的 字段名\r\n      const printData = {}\r\n      const printArr = hiprintTemplate.getJson().panels[0].printElements\r\n      printArr.forEach(item => {\r\n        if (item.options.field == 'Table') {\r\n          printData[item.options.field] = JSON.parse(item.options.testData)\r\n        } else {\r\n          console.log(item)\r\n          printData[item.options.field] = item.options.testData || item.options\r\n        }\r\n      })\r\n      console.log(printData)\r\n\r\n      // let printData = hiprintTemplate.getJson()\r\n      // 参数: 打印时设置 左偏移量，上偏移量\r\n      const options = { leftOffset: -1, topOffset: -1 }\r\n      // 扩展\r\n      const ext = {\r\n        callback: () => {\r\n          console.log('浏览器打印窗口已打开')\r\n        }\r\n        // styleHandler: () => {\r\n        //   // 重写 文本 打印样式\r\n        //   return \"<style>.hiprint-printElement-text{color:red !important;}</style>\";\r\n        // }\r\n      }\r\n      // 调用浏览器打印\r\n      hiprintTemplate.print(printData, options, ext)\r\n    },\r\n    changePaper() {\r\n      const temp = this.paperTypes.find(i => i.type === this.curPaperType)\r\n      if (this.curPaperType === '自定义纸张') {\r\n        hiprintTemplate.setPaper(this.paperWidth, this.paperHeight)\r\n      } else {\r\n        hiprintTemplate.setPaper(temp.width, temp.height)\r\n      }\r\n    },\r\n    // 新建模板\r\n    createTemplate() {\r\n      this.form = {\r\n        Name: '',\r\n        Type: 1, // 1-发货单\r\n        Data: '',\r\n        Base64Image: ''\r\n      }\r\n      this.clearTemplate()\r\n    },\r\n    // 保存模板\r\n    async saveTemplate() {\r\n      this.saveLoading = true\r\n      try {\r\n        this.form.Base64Image = await this.getImage()\r\n        if (!this.form.Name) {\r\n          this.$message.error('请输入模板名称')\r\n          return\r\n        }\r\n        const json = hiprintTemplate.getJson()\r\n        this.form.Data = JSON.stringify(json)\r\n        const res = await SavePrintTemplateEntity(this.form)\r\n        if (res.IsSucceed) {\r\n          this.$message.success('保存成功')\r\n          this.getTemplateList()\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('保存失败')\r\n        console.error('保存模板失败:', error)\r\n      } finally {\r\n        this.saveLoading = false\r\n      }\r\n    },\r\n    // 加载模板\r\n    async loadTemplate(id) {\r\n      this.clearTemplate()\r\n      const res = await GetPrintTemplateEntity({ id })\r\n      this.form = res.Data\r\n      const parseData = JSON.parse(res.Data.Data)\r\n      try {\r\n        const index = parseData.panels[0].printElements.findIndex(i => i.options.field === 'Logo')\r\n        parseData.panels[0].printElements[index].options.src = this.logoUrl\r\n      } catch (e) {}\r\n      console.log()\r\n      const template = parseData\r\n      this.buildDesigner(template)\r\n\r\n      // 匹配纸张\r\n      const { width, height } = template.panels[0]\r\n      const matchedPaper = this.paperTypes.find(i => i.width == width & i.height == height)\r\n      if (matchedPaper) {\r\n        this.curPaper = matchedPaper\r\n      } else {\r\n        this.curPaper = {\r\n          type: '自定义纸张',\r\n          width,\r\n          height\r\n        }\r\n      }\r\n      this.curPaperType = this.curPaper.type\r\n      this.paperWidth = width\r\n      this.paperHeight = height\r\n      this.changePaper()\r\n    },\r\n    // 清空模板\r\n    clearTemplate() {\r\n      $('#hiprint-printTemplate').empty() // 先清空, 避免重复构建\r\n      this.buildDesigner()\r\n    },\r\n\r\n    async getTemplateList() {\r\n      const res = await GetPrintTemplateList({\r\n        type: this.mode\r\n      })\r\n      this.tmplList = res.Data\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .title{\r\n    min-width: 100%;\r\n    padding-left: 8px;\r\n    font-size: 18px;\r\n    margin-top: 20px;\r\n  }\r\n  ::-webkit-scrollbar {\r\n    width: 0px;\r\n    height: 8px;\r\n  }\r\n  ::-webkit-scrollbar-thumb {\r\n    border-radius: 4px;\r\n    // box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);\r\n    background: #ddd;\r\n  }\r\n  ::-webkit-scrollbar-track {\r\n    box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.1);\r\n    border-radius: 4px;\r\n    background: #ededed;\r\n  }\r\n  .item {\r\n    color: rgba(34,40,52,0.65);\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    margin: 10px 10px;\r\n    width: 112px;\r\n    height: 44px;\r\n    border-radius: 4px 4px 4px 4px;\r\n    border: 1px dashed #D0D3DB;\r\n    font-size: 14px;\r\n  }\r\n  .label{\r\n    margin: 0 5px 0 10px;\r\n  }\r\n  /*::v-deep{*/\r\n  /*    .hiprint-option-item-settingBtn{*/\r\n  /*        background: #0ba1f8;*/\r\n  /*    }*/\r\n  /*}*/\r\n  .header{\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    row-gap: 10px;\r\n    align-items: center;\r\n    margin-bottom: 10px;\r\n    color: rgba(34, 40, 52, 0.85);\r\n    font-size: 12px;\r\n    .input{\r\n      width: 100px;\r\n    }\r\n    .zoom{\r\n      margin: 0 10px;\r\n      border: 1px solid #D0D3DB;\r\n      border-radius: 4px;\r\n      width: 100px;\r\n      height: 32px;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      font-size: 14px;\r\n    }\r\n    .zoom-btn{\r\n      padding: 5px;\r\n      font-size: 19px;\r\n    }\r\n  }\r\n  ::v-deep{\r\n    .hinnn-layout-sider{\r\n      *{\r\n        color: rgba(34, 40, 52, 0.85);\r\n      }\r\n      input[placeholder=\"请输入图片地址\"]  {\r\n        width: 100%!important;\r\n        & + button{\r\n          display: none;\r\n        }\r\n      }\r\n      input,textarea,select{\r\n        border-radius: 4px!important;\r\n        border: 1px solid #D0D3DB!important;\r\n      }\r\n      input,select{\r\n        height: 32px!important;\r\n        line-height: 32px;\r\n      }\r\n      .hiprint-option-item-settingBtn{\r\n        background-color: #298DFF;\r\n        border-radius: 4px;\r\n        height: 30px;\r\n        color: #ffffff;\r\n        cursor: pointer;\r\n      }\r\n      .hiprint-option-item-deleteBtn{\r\n        background-color: #FB6B7F;\r\n      }\r\n    }\r\n\r\n  }\r\n  .tmpl-list {\r\n    margin-top: 12px;\r\n    overflow-y: auto;\r\n    min-height: 130px;\r\n    .tmpl-menu {\r\n      border-right: none;\r\n      .el-menu-item {\r\n        height: 32px;\r\n        line-height: 32px;\r\n        .el-link {\r\n          position: absolute;\r\n          top: 20%;\r\n          right: 12px;\r\n          margin-top: -7px;\r\n          transition: transform 0.3s;\r\n          &:last-child {\r\n            right: 36px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n</style>\r\n\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\index.vue", "mtime": 1757909680922}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["debounce", "GetTeamTaskAllocationPageList", "GetWorkingTeamLoadRealTime", "BatchAllocationWithPreStepTask", "GetProcessList", "GetWorkingTeamBase", "GetWorkshopPageList", "GetCurFactory", "getTbInfo", "addRouterPage", "getProjectAreaUnit", "echarts", "Pagination", "tablePageSize", "GetBOMInfo", "getBomCode", "checkIsUnitPart", "name", "components", "mixins", "data", "bomList", "bom<PERSON>ame", "selectList", "addPageArray", "path", "$route", "hidden", "component", "Promise", "resolve", "then", "_interopRequireWildcard", "require", "meta", "title", "activeName", "dialogVisible", "pgLoading", "tipLabel", "currentComponent", "dWidth", "form", "InstallUnit_Id", "Project_Id", "Area_Id", "Workshop_Id", "Schduling_Code", "Process_Code", "Working_Team_Id", "Allocate_Status", "queryInfo", "Page", "PageSize", "tbConfig", "<PERSON>_<PERSON><PERSON>th", "columns", "tbData", "processOption", "groupOption", "total", "search", "workShopOption", "Is_Workshop_Enabled", "drawer", "myChart", "computed", "isCom", "isUnitPart", "activated", "console", "log", "fetchData", "mounted", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "_yield$GetBOMInfo", "list", "wrap", "_callee$", "_context", "prev", "next", "sent", "getIsWorkShop", "getCurColumns", "getProcessOption", "getWorkshopOption", "stop", "methods", "_this2", "_callee2", "_callee2$", "_context2", "getTableConfig", "map", "item", "index", "length", "<PERSON>_<PERSON><PERSON>th", "Code", "Is_Display", "page", "_this3", "Process_Type", "Bom_Level", "_objectSpread", "res", "IsSucceed", "Data", "TotalCount", "$message", "message", "Message", "type", "_this4", "_type", "handleSelectionChange", "array", "records", "taskChange", "getTeamOption", "_this5", "processId", "cur", "find", "Id", "_this6", "pagesize", "_this7", "_callee3", "_callee3$", "_context3", "processChange", "v", "tbSelectChange", "handleClose", "handleReset", "$refs", "resetFields", "handleDetail", "row", "$router", "push", "query", "bomLevel", "pg_type", "pg_redirect", "other", "encodeURIComponent", "JSON", "stringify", "handleView", "batchAllocationWithPreStepTask", "_this8", "List", "Workshop_Name", "handleTabsClick", "tab", "event", "drawerOpen", "_this9", "xAxisData", "data1", "data2", "i", "_i$Load", "_i$Real_Time_Load", "Name", "Load", "Real_Time_Load", "$nextTick", "chartDom", "init", "emphasisStyle", "itemStyle", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "echartOption", "text", "textStyle", "fontSize", "color", "tooltip", "show", "trigger", "legend", "icon", "itemWidth", "itemHeight", "grid", "left", "right", "bottom", "containLabel", "xAxis", "axisLine", "onZero", "splitLine", "splitArea", "yAxis", "series", "barGap", "emphasis", "setOption"], "sources": ["src/views/PRO/plan-production/task-allocation/v4/index.vue"], "sourcesContent": ["<template>\r\n  <div v-loading=\"pgLoading\" class=\"container abs100\">\r\n    <el-tabs v-model=\"activeName\" @tab-click=\"handleTabsClick\">\r\n      <!-- <el-tab-pane label=\"构件\" name=\"2\" />\r\n      <el-tab-pane label=\"部件\" name=\"3\" />\r\n      <el-tab-pane label=\"零件\" name=\"1\" /> -->\r\n      <el-tab-pane v-for=\"item in bomList\" :key=\"item.Code\" :label=\"item.Display_Name\" :name=\"item.Code\" />\r\n    </el-tabs>\r\n    <div class=\"search-wrapper\">\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"80px\" class=\"demo-form-inline\">\r\n        <el-row>\r\n          <el-col :span=\"6\" :lg=\"6\" :xl=\"6\">\r\n            <el-form-item label=\"项目名称\" prop=\"Project_Id\">\r\n              <el-select\r\n                ref=\"ProjectName\"\r\n                v-model=\"form.Project_Id\"\r\n                filterable\r\n                clearable\r\n                placeholder=\"请选择\"\r\n                @change=\"projectChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in ProjectNameData\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Short_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\" :lg=\"6\" :xl=\"6\">\r\n            <el-form-item label=\"区域\" prop=\"Area_Id\">\r\n              <el-tree-select\r\n                ref=\"treeSelectArea\"\r\n                v-model=\"form.Area_Id\"\r\n                :disabled=\"!form.Project_Id\"\r\n                :select-params=\"{\r\n                  clearable: true,\r\n                }\"\r\n                class=\"cs-tree-x\"\r\n                :tree-params=\"treeParamsArea\"\r\n                @select-clear=\"areaClear\"\r\n                @node-click=\"areaChange\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\" :lg=\"6\" :xl=\"6\">\r\n            <template>\r\n              <el-form-item label=\"批次\" prop=\"InstallUnit_Id\">\r\n                <el-select\r\n                  ref=\"SetupPosition\"\r\n                  v-model=\"form.InstallUnit_Id\"\r\n                  :disabled=\"!form.Area_Id\"\r\n                  clearable\r\n                  placeholder=\"请选择\"\r\n                  @change=\"setupPositionChange\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in SetupPositionData\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Name\"\r\n                    :value=\"item.Id\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </template>\r\n          </el-col>\r\n          <el-col :span=\"6\" :lg=\"6\" :xl=\"6\">\r\n            <el-form-item label=\"排产单号\" prop=\"Schduling_Code\">\r\n              <el-input v-model=\"form.Schduling_Code\" type=\"text\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"4\" :lg=\"4\" :xl=\"4\">\r\n            <el-form-item label=\"任务工序\" prop=\"Process_Code\">\r\n              <el-select\r\n                v-model=\"form.Process_Code\"\r\n                clearable\r\n                placeholder=\"请选择\"\r\n                style=\"width: 100%\"\r\n                @change=\"processChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in processOption\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Name\"\r\n                  :value=\"item.Code\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\" :lg=\"4\" :xl=\"4\">\r\n            <el-form-item label=\"加工班组\" prop=\"Working_Team_Id\">\r\n              <el-select\r\n                v-model=\"form.Working_Team_Id\"\r\n                :disabled=\"!form.Process_Code\"\r\n                clearable\r\n                placeholder=\"请选择\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in groupOption\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\" :lg=\"4\" :xl=\"4\">\r\n            <el-form-item\r\n              v-if=\"Is_Workshop_Enabled\"\r\n              label=\"所属车间\"\r\n              prop=\"Workshop_Name\"\r\n            >\r\n              <el-select\r\n                v-model=\"form.Workshop_Id\"\r\n                clearable\r\n                placeholder=\"请选择\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in workShopOption\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\" :lg=\"6\" :xl=\"6\">\r\n            <el-form-item label=\"是否分配\" prop=\"Allocate_Status\">\r\n              <el-select v-model=\"form.Allocate_Status\" multiple placeholder=\"请选择\" clearable=\"\">\r\n                <!--            <el-option label=\"全部\" value=\"\" />-->\r\n                <el-option label=\"未分配\" :value=\"1\" />\r\n                <el-option label=\"已分配\" :value=\"2\" />\r\n                <el-option label=\"分配完成\" :value=\"3\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\" :lg=\"6\" :xl=\"6\">\r\n            <el-form-item label-width=\"16px\">\r\n              <el-button @click=\"handleReset\">重置</el-button>\r\n              <el-button type=\"primary\" @click=\"search(1)\">搜索</el-button>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div>\r\n        <el-button type=\"primary\" @click=\"drawerOpen\">班组负荷</el-button>\r\n        <el-button type=\"primary\" :disabled=\"!selectList.length\" @click=\"batchAllocationWithPreStepTask\">上道工序同步</el-button>\r\n      </div>\r\n    </div>\r\n    <div class=\"main-wrapper\">\r\n      <div class=\"tb-x\">\r\n        <vxe-table\r\n          :key=\"activeName\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          empty-text=\"暂无数据\"\r\n          height=\"auto\"\r\n          show-overflow\r\n          :row-config=\"{isCurrent: true, isHover: true}\"\r\n          :loading=\"pgLoading\"\r\n          class=\"cs-vxe-table\"\r\n          align=\"left\"\r\n          stripe\r\n          :data=\"tbData\"\r\n          resizable\r\n          :tooltip-config=\"{ enterable: true }\"\r\n          @checkbox-all=\"handleSelectionChange\"\r\n          @checkbox-change=\"handleSelectionChange\"\r\n        >\r\n          <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n          <vxe-column\r\n            v-for=\"(item, index) in columns\"\r\n            :key=\"item.Code\"\r\n            :align=\"item.Align\"\r\n            :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n            show-overflow=\"tooltip\"\r\n            sortable\r\n            :field=\"item.Code\"\r\n            :title=\"item.Display_Name\"\r\n            :min-width=\"item.Width||120\"\r\n            :visible=\"item.visible\"\r\n          >\r\n            <template v-if=\"item.Code==='Total_Allocation_Count'\" #default=\"{ row }\">\r\n              {{ row.Total_Allocation_Count - row.Total_Receive_Count === row.Can_Allocation_Count ?'分配完成' :row.Total_Allocation_Count > 0? '已分配':'未分配' }}\r\n            </template>\r\n            <template v-else-if=\"item.Code==='Finish_Date'||item.Code==='Order_Date'\" #default=\"{ row }\">\r\n              {{ row[item.Code] | timeFormat }}\r\n            </template>\r\n            <template v-else #default=\"{ row }\">\r\n              <span>{{ (row[item.Code] ===0 ?0 : row[item.Code]) | displayValue }}</span>\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column title=\"操作\" fixed=\"right\" width=\"125\">\r\n            <template #default=\"{ row , rowIndex }\">\r\n              <el-button type=\"text\" @click=\"handleView(row)\">查看</el-button>\r\n              <el-button\r\n                v-if=\"row.Can_Allocation_Count !== 0\"\r\n                type=\"text\"\r\n                @click=\"handleDetail(row)\"\r\n              >任务分配\r\n              </el-button>\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n      </div>\r\n      <Pagination\r\n        :total=\"total\"\r\n        :page-sizes=\"tablePageSize\"\r\n        :page.sync=\"queryInfo.Page\"\r\n        :limit.sync=\"queryInfo.PageSize\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\"\r\n        @pagination=\"pageChange\"\r\n      />\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"dWidth\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        @close=\"handleClose\"\r\n        @refresh=\"fetchData(1)\"\r\n      />\r\n    </el-dialog>\r\n    <el-drawer\r\n      size=\"60%\"\r\n      custom-class=\"drawerBox\"\r\n      :visible.sync=\"drawer\"\r\n      direction=\"btt\"\r\n      :with-header=\"false\"\r\n      append-to-body\r\n      wrapper-closable\r\n    >\r\n      <div class=\"chartWrapper\">\r\n        <div ref=\"chartDom\" style=\"width: 100%; height: 100%\" />\r\n      </div>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { debounce } from '@/utils'\r\nimport { GetTeamTaskAllocationPageList, GetWorkingTeamLoadRealTime, BatchAllocationWithPreStepTask } from '@/api/PRO/production-task'\r\nimport { GetProcessList, GetWorkingTeamBase } from '@/api/PRO/technology-lib'\r\nimport { GetWorkshopPageList } from '@/api/PRO/basic-information/workshop'\r\nimport { GetCurFactory } from '@/api/PRO/factory.js' // 获取是否开启车间接口\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nimport getProjectAreaUnit from '@/views/PRO/inventory/package/mixins/mixinsProject.js'\r\nimport * as echarts from 'echarts'\r\nimport Pagination from '@/components/Pagination/index.vue'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport { GetBOMInfo, getBomCode, checkIsUnitPart } from '@/views/PRO/bom-setting/utils'\r\n\r\nexport default {\r\n  name: 'PROTaskAllocationList',\r\n  components: {\r\n    Pagination\r\n  },\r\n  mixins: [getTbInfo, addRouterPage, getProjectAreaUnit],\r\n  data() {\r\n    return {\r\n      bomList: [],\r\n      bomName: '',\r\n      selectList: [],\r\n      tablePageSize: tablePageSize,\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/detail',\r\n          hidden: true,\r\n          component: () =>\r\n            import('@/views/PRO/plan-production/task-allocation/v4/detail.vue'),\r\n          name: 'PROTaskAllocationInfo',\r\n          meta: { title: '调整分配' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/view',\r\n          hidden: true,\r\n          component: () =>\r\n            import('@/views/PRO/plan-production/task-allocation/v4/detail.vue'),\r\n          name: 'PROTaskAllocationView',\r\n          meta: { title: '查看分配' }\r\n        }\r\n      ],\r\n      activeName: getBomCode('-1'), // 1零件  2构件\r\n      dialogVisible: false,\r\n      pgLoading: false,\r\n      tipLabel: '',\r\n      title: '',\r\n      currentComponent: '',\r\n      dWidth: '40%',\r\n      form: {\r\n        InstallUnit_Id: '', // 批次ID\r\n        Project_Id: '', // 项目名称\r\n        Area_Id: '', // 区域ID\r\n        Workshop_Id: '',\r\n        Schduling_Code: '',\r\n        Process_Code: '',\r\n        Working_Team_Id: '',\r\n        Allocate_Status: [1, 2]\r\n      },\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: tablePageSize[0]\r\n      },\r\n      tbConfig: {\r\n        Op_Width: 180\r\n      },\r\n      columns: [],\r\n      tbData: [],\r\n      processOption: [],\r\n      groupOption: [],\r\n      total: 0,\r\n      search: () => ({}),\r\n      workShopOption: [],\r\n      Is_Workshop_Enabled: false,\r\n      drawer: false,\r\n      myChart: null\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.activeName === getBomCode('-1')\r\n    },\r\n    isUnitPart() {\r\n      return checkIsUnitPart(this.activeName)\r\n    }\r\n  },\r\n  activated() {\r\n    console.log('activatedactivatedactivated')\r\n    this.fetchData()\r\n  },\r\n  async mounted() {\r\n    const { list } = await GetBOMInfo()\r\n    this.bomList = list || []\r\n\r\n    await this.getIsWorkShop()\r\n    await this.getCurColumns()\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    this.getProcessOption()\r\n    this.getWorkshopOption()\r\n  },\r\n  methods: {\r\n    async getCurColumns() {\r\n      this.columns = await this.getTableConfig(this.isUnitPart ? 'PROTaskUnitAllocationList' : 'PROTaskAllocationList')\r\n      this.columns = this.columns.map((item, index) => {\r\n        if (index === this.columns.length - 1) {\r\n          item.Min_Width = 140\r\n        }\r\n        if (item.Code === 'Workshop_Name') {\r\n          item.Is_Display = this.Is_Workshop_Enabled\r\n        }\r\n        return item\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 获取任务分配类别\r\n     * Page 分页\r\n     * PageSize\r\n     * form 筛选\r\n     * form.Process_Type   2构件  1零件\r\n     */\r\n    fetchData(page) {\r\n      this.form.Process_Type = this.isCom ? 2 : this.isUnitPart ? 3 : 1\r\n      this.form.Bom_Level = this.activeName\r\n      page && (this.queryInfo.Page = page)\r\n      this.pgLoading = true\r\n      GetTeamTaskAllocationPageList({\r\n        ...this.queryInfo,\r\n        ...this.form\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.Data\r\n          this.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.pgLoading = false\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 获取工序\r\n     * type  1构件  2零件 3部件\r\n     */\r\n    getProcessOption() {\r\n      // const _type = typeMap[+this.activeName]\r\n      const _type = this.isCom ? 1 : this.isUnitPart ? 3 : 2\r\n      GetProcessList({\r\n        type: _type,\r\n        Bom_Level: this.activeName\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.processOption = res.Data\r\n          /* if (this.processOption.length) {\r\n            this.form.Process_Id = this.processOption[0]?.Id\r\n            this.getTeamOption()\r\n          } */\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    handleSelectionChange(array) {\r\n      this.selectList = array.records\r\n    },\r\n\r\n    taskChange() {\r\n      this.form.Process_Code = ''\r\n      this.form.Working_Team_Id = ''\r\n      this.getProcessOption()\r\n    },\r\n\r\n    getTeamOption() {\r\n      let processId = ''\r\n      const cur = this.processOption.find(\r\n        (item) => item.Code === this.form.Process_Code\r\n      )\r\n      if (cur) {\r\n        processId = cur.Id\r\n      }\r\n      GetWorkingTeamBase({\r\n        processId: processId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.groupOption = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getWorkshopOption() {\r\n      GetWorkshopPageList({ page: 1, pagesize: -1 }).then(\r\n        (res) => {\r\n          if (res.IsSucceed) {\r\n            this.workShopOption = res.Data.Data\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n    },\r\n    async getIsWorkShop() {\r\n      await GetCurFactory({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.Is_Workshop_Enabled = res.Data[0].Is_Workshop_Enabled\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    processChange(v) {\r\n      this.form.Working_Team_Id = ''\r\n      if (!v) {\r\n        return\r\n      }\r\n      this.getTeamOption()\r\n    },\r\n\r\n    tbSelectChange(array) { },\r\n\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n\r\n    handleReset() {\r\n      this.$refs['form'].resetFields()\r\n      this.search(1)\r\n    },\r\n\r\n    handleDetail(row) {\r\n      this.$router.push({\r\n        name: 'PROTaskAllocationInfo',\r\n        query: {\r\n          bomLevel: this.activeName,\r\n          pg_type: this.isCom ? 'com' : this.isUnitPart ? 'unitPart' : 'part',\r\n          pg_redirect: 'PROTaskAllocationList',\r\n          Is_Workshop_Enabled: this.Is_Workshop_Enabled,\r\n          other: encodeURIComponent(JSON.stringify(row))\r\n        }\r\n      })\r\n    },\r\n\r\n    handleView(row) {\r\n      this.$router.push({\r\n        name: 'PROTaskAllocationView',\r\n        query: {\r\n          type: 'view',\r\n          bomLevel: this.activeName,\r\n          pg_type: this.isCom ? 'com' : this.isUnitPart ? 'unitPart' : 'part',\r\n          pg_redirect: 'PROTaskAllocationList',\r\n          Is_Workshop_Enabled: this.Is_Workshop_Enabled,\r\n          other: encodeURIComponent(JSON.stringify(row))\r\n        }\r\n      })\r\n    },\r\n\r\n    // 批量上道工序同步\r\n    batchAllocationWithPreStepTask() {\r\n      const List = this.selectList.map(item => {\r\n        return {\r\n          Area_Id: item.Area_Id,\r\n          InstallUnit_Id: item.InstallUnit_Id,\r\n          Process_Code: item.Process_Code,\r\n          Schduling_Code: item.Schduling_Code,\r\n          Workshop_Name: item.Workshop_Name\r\n        }\r\n      })\r\n      BatchAllocationWithPreStepTask({\r\n        Process_Type: this.isCom ? 2 : this.isUnitPart ? 3 : 1,\r\n        Bom_Level: this.activeName,\r\n        List\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '操作成功',\r\n            type: 'success'\r\n          })\r\n          this.fetchData()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 切换tab\r\n    handleTabsClick(tab, event) {\r\n      this.form = {\r\n        InstallUnit_Id: '',\r\n        Project_Id: '',\r\n        Area_Id: '',\r\n        Allocate_Status: [1, 2],\r\n        Schduling_Code: '',\r\n        Process_Code: '',\r\n        Working_Team_Id: ''\r\n      }\r\n      this.getCurColumns()\r\n      this.getProcessOption()\r\n      this.fetchData()\r\n    },\r\n    // drawer\r\n    drawerOpen() {\r\n      this.drawer = true\r\n      const xAxisData = []\r\n      const data1 = []\r\n      const data2 = []\r\n      GetWorkingTeamLoadRealTime({\r\n\r\n        type: this.isCom ? 1 : this.isUnitPart ? 3 : 2,\r\n        Bom_Level: this.activeName\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          console.log(res)\r\n          if (res.Data && res.Data.length > 0) {\r\n            res.Data.map(i => {\r\n              xAxisData.push(i.Name)\r\n              data1.push(i.Load ?? 0)\r\n              data2.push(i.Real_Time_Load ?? 0)\r\n              this.$nextTick(() => {\r\n                console.log('in')\r\n                const chartDom = this.$refs.chartDom\r\n                if (this.myChart == null) {\r\n                  this.myChart = echarts.init(chartDom)\r\n                }\r\n\r\n                const emphasisStyle = {\r\n                  itemStyle: {\r\n                    shadowBlur: 10,\r\n                    shadowColor: 'rgba(0,0,0,0.3)'\r\n                  }\r\n                }\r\n                const echartOption = {\r\n                  title: {\r\n                    text: '班组负荷实时情况',\r\n                    textStyle: {\r\n                      fontSize: 16,\r\n                      color: '#222834'\r\n                    }\r\n                  },\r\n                  tooltip: {\r\n                    show: true,\r\n                    trigger: 'axis'\r\n                  },\r\n                  legend: {\r\n                    icon: 'rect',\r\n                    itemWidth: 8,\r\n                    itemHeight: 4,\r\n                    data: [],\r\n                    textStyle: {\r\n                      fontSize: 12,\r\n                      color: '#999999 '\r\n                    }\r\n                  },\r\n                  grid: {\r\n                    left: '3%',\r\n                    right: '4%',\r\n                    bottom: '3%',\r\n                    containLabel: true\r\n                  },\r\n                  xAxis: {\r\n                    data: xAxisData,\r\n                    axisLine: { onZero: true },\r\n                    splitLine: { show: false },\r\n                    splitArea: { show: false }\r\n                  },\r\n                  yAxis: {\r\n\r\n                  },\r\n                  series: [\r\n                    {\r\n                      name: '负荷提醒线',\r\n                      type: 'bar',\r\n                      barGap: '-100%',\r\n                      emphasis: emphasisStyle,\r\n                      data: data1,\r\n                      itemStyle: { color: '#91cc75' }\r\n                    }, {\r\n                      name: '当前负荷',\r\n                      type: 'bar',\r\n                      barGap: '-100%',\r\n                      emphasis: emphasisStyle,\r\n                      data: data2,\r\n                      itemStyle: { color: '#5470C6' }\r\n                    }\r\n                  ]\r\n                }\r\n                this.myChart.setOption(echartOption)\r\n              })\r\n            })\r\n          }\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.container {\r\n  padding: 16px;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .search-wrapper {\r\n    padding: 16px 16px 16px 16px;\r\n    background: #ffffff;\r\n    border-radius: 4px 4px 4px 4px;\r\n\r\n    // ::v-deep .el-form-item__content {\r\n    //   width: 197px;\r\n    // }\r\n    ::v-deep .el-form-item {\r\n      .el-form-item__content {\r\n        & > .el-input {\r\n          width: 100%;\r\n        }\r\n\r\n        & > .el-select {\r\n          width: 100%;\r\n        }\r\n        & > .el-date-editor {\r\n          width: 100%;\r\n        }\r\n\r\n        .el-tree-select-input {\r\n          width: 100% !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .el-tabs{\r\n    margin-bottom: 16px;\r\n    background-color: #ffffff;\r\n    padding-left: 16px;\r\n    width: 100%;\r\n  }\r\n  ::v-deep .pagination {\r\n    justify-content: flex-end !important;\r\n    margin-top: 12px !important;\r\n\r\n    .el-input--small .el-input__inner {\r\n      height: 28px;\r\n      line-height: 28px;\r\n    }\r\n  }\r\n}\r\n\r\n.main-wrapper {\r\n  background: #ffffff;\r\n  margin-top: 16px;\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  border-radius: 4px 4px 4px 4px;\r\n  padding: 16px 16px 0;\r\n  overflow:hidden;\r\n\r\n  .tb-x{\r\n    flex: 1;\r\n    height: 0;\r\n  }\r\n  .btn-wrapper {\r\n    padding-bottom: 16px;\r\n  }\r\n}\r\n\r\n.plm-custom-dialog {\r\n  ::v-deep {\r\n    .el-dialog .el-dialog__body {\r\n      height: 70vh;\r\n    }\r\n  }\r\n}\r\n.drawerBox {\r\n  .chartWrapper {\r\n    padding: 20px;\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n}\r\n.pagination-container{\r\n  padding: 0;\r\n  margin: 10px 0;\r\n  text-align: right;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8PA,SAAAA,QAAA;AACA,SAAAC,6BAAA,EAAAC,0BAAA,EAAAC,8BAAA;AACA,SAAAC,cAAA,EAAAC,kBAAA;AACA,SAAAC,mBAAA;AACA,SAAAC,aAAA;AACA,OAAAC,SAAA;AACA,OAAAC,aAAA;AACA,OAAAC,kBAAA;AACA,YAAAC,OAAA;AACA,OAAAC,UAAA;AACA,SAAAC,aAAA;AACA,SAAAC,UAAA,EAAAC,UAAA,EAAAC,eAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAN,UAAA,EAAAA;EACA;EACAO,MAAA,GAAAX,SAAA,EAAAC,aAAA,EAAAC,kBAAA;EACAU,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,OAAA;MACAC,UAAA;MACAV,aAAA,EAAAA,aAAA;MACAW,YAAA,GACA;QACAC,IAAA,OAAAC,MAAA,CAAAD,IAAA;QACAE,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA,CACA;UAAA;QAAA;QACAhB,IAAA;QACAiB,IAAA;UAAAC,KAAA;QAAA;MACA,GACA;QACAV,IAAA,OAAAC,MAAA,CAAAD,IAAA;QACAE,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA,CACA;UAAA;QAAA;QACAhB,IAAA;QACAiB,IAAA;UAAAC,KAAA;QAAA;MACA,EACA;MACAC,UAAA,EAAArB,UAAA;MAAA;MACAsB,aAAA;MACAC,SAAA;MACAC,QAAA;MACAJ,KAAA;MACAK,gBAAA;MACAC,MAAA;MACAC,IAAA;QACAC,cAAA;QAAA;QACAC,UAAA;QAAA;QACAC,OAAA;QAAA;QACAC,WAAA;QACAC,cAAA;QACAC,YAAA;QACAC,eAAA;QACAC,eAAA;MACA;MACAC,SAAA;QACAC,IAAA;QACAC,QAAA,EAAAxC,aAAA;MACA;MACAyC,QAAA;QACAC,QAAA;MACA;MACAC,OAAA;MACAC,MAAA;MACAC,aAAA;MACAC,WAAA;MACAC,KAAA;MACAC,MAAA,WAAAA,OAAA;QAAA;MAAA;MACAC,cAAA;MACAC,mBAAA;MACAC,MAAA;MACAC,OAAA;IACA;EACA;EACAC,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA,YAAA/B,UAAA,KAAArB,UAAA;IACA;IACAqD,UAAA,WAAAA,WAAA;MACA,OAAApD,eAAA,MAAAoB,UAAA;IACA;EACA;EACAiC,SAAA,WAAAA,UAAA;IACAC,OAAA,CAAAC,GAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAAC,iBAAA,EAAAC,IAAA;MAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACAvE,UAAA;UAAA;YAAAiE,iBAAA,GAAAI,QAAA,CAAAG,IAAA;YAAAN,IAAA,GAAAD,iBAAA,CAAAC,IAAA;YACAN,KAAA,CAAArD,OAAA,GAAA2D,IAAA;YAAAG,QAAA,CAAAE,IAAA;YAAA,OAEAX,KAAA,CAAAa,aAAA;UAAA;YAAAJ,QAAA,CAAAE,IAAA;YAAA,OACAX,KAAA,CAAAc,aAAA;UAAA;YACAd,KAAA,CAAAb,MAAA,GAAA7D,QAAA,CAAA0E,KAAA,CAAAF,SAAA;YACAE,KAAA,CAAAe,gBAAA;YACAf,KAAA,CAAAgB,iBAAA;UAAA;UAAA;YAAA,OAAAP,QAAA,CAAAQ,IAAA;QAAA;MAAA,GAAAb,OAAA;IAAA;EACA;EACAc,OAAA;IACAJ,aAAA,WAAAA,cAAA;MAAA,IAAAK,MAAA;MAAA,OAAAlB,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAiB,SAAA;QAAA,OAAAlB,mBAAA,GAAAK,IAAA,UAAAc,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAZ,IAAA,GAAAY,SAAA,CAAAX,IAAA;YAAA;cAAAW,SAAA,CAAAX,IAAA;cAAA,OACAQ,MAAA,CAAAI,cAAA,CAAAJ,MAAA,CAAAzB,UAAA;YAAA;cAAAyB,MAAA,CAAArC,OAAA,GAAAwC,SAAA,CAAAV,IAAA;cACAO,MAAA,CAAArC,OAAA,GAAAqC,MAAA,CAAArC,OAAA,CAAA0C,GAAA,WAAAC,IAAA,EAAAC,KAAA;gBACA,IAAAA,KAAA,KAAAP,MAAA,CAAArC,OAAA,CAAA6C,MAAA;kBACAF,IAAA,CAAAG,SAAA;gBACA;gBACA,IAAAH,IAAA,CAAAI,IAAA;kBACAJ,IAAA,CAAAK,UAAA,GAAAX,MAAA,CAAA9B,mBAAA;gBACA;gBACA,OAAAoC,IAAA;cACA;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAAL,IAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IACA;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;IACAtB,SAAA,WAAAA,UAAAiC,IAAA;MAAA,IAAAC,MAAA;MACA,KAAAhE,IAAA,CAAAiE,YAAA,QAAAxC,KAAA,YAAAC,UAAA;MACA,KAAA1B,IAAA,CAAAkE,SAAA,QAAAxE,UAAA;MACAqE,IAAA,UAAAtD,SAAA,CAAAC,IAAA,GAAAqD,IAAA;MACA,KAAAnE,SAAA;MACArC,6BAAA,CAAA4G,aAAA,CAAAA,aAAA,KACA,KAAA1D,SAAA,GACA,KAAAT,IAAA,CACA,EAAAX,IAAA,WAAA+E,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAL,MAAA,CAAAjD,MAAA,GAAAqD,GAAA,CAAAE,IAAA,CAAAA,IAAA;UACAN,MAAA,CAAA9C,KAAA,GAAAkD,GAAA,CAAAE,IAAA,CAAAC,UAAA;QACA;UACAP,MAAA,CAAAQ,QAAA;YACAC,OAAA,EAAAL,GAAA,CAAAM,OAAA;YACAC,IAAA;UACA;QACA;QACAX,MAAA,CAAApE,SAAA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAmD,gBAAA,WAAAA,iBAAA;MAAA,IAAA6B,MAAA;MACA;MACA,IAAAC,KAAA,QAAApD,KAAA,YAAAC,UAAA;MACAhE,cAAA;QACAiH,IAAA,EAAAE,KAAA;QACAX,SAAA,OAAAxE;MACA,GAAAL,IAAA,WAAA+E,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAO,MAAA,CAAA5D,aAAA,GAAAoD,GAAA,CAAAE,IAAA;UACA;AACA;AACA;AACA;QACA;UACAM,MAAA,CAAAJ,QAAA;YACAC,OAAA,EAAAL,GAAA,CAAAM,OAAA;YACAC,IAAA;UACA;QACA;MACA;IACA;IAEAG,qBAAA,WAAAA,sBAAAC,KAAA;MACA,KAAAlG,UAAA,GAAAkG,KAAA,CAAAC,OAAA;IACA;IAEAC,UAAA,WAAAA,WAAA;MACA,KAAAjF,IAAA,CAAAM,YAAA;MACA,KAAAN,IAAA,CAAAO,eAAA;MACA,KAAAwC,gBAAA;IACA;IAEAmC,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,SAAA;MACA,IAAAC,GAAA,QAAArE,aAAA,CAAAsE,IAAA,CACA,UAAA7B,IAAA;QAAA,OAAAA,IAAA,CAAAI,IAAA,KAAAsB,MAAA,CAAAnF,IAAA,CAAAM,YAAA;MAAA,CACA;MACA,IAAA+E,GAAA;QACAD,SAAA,GAAAC,GAAA,CAAAE,EAAA;MACA;MACA5H,kBAAA;QACAyH,SAAA,EAAAA;MACA,GAAA/F,IAAA,WAAA+E,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAc,MAAA,CAAAlE,WAAA,GAAAmD,GAAA,CAAAE,IAAA;QACA;UACAa,MAAA,CAAAX,QAAA;YACAC,OAAA,EAAAL,GAAA,CAAAM,OAAA;YACAC,IAAA;UACA;QACA;MACA;IACA;IACA3B,iBAAA,WAAAA,kBAAA;MAAA,IAAAwC,MAAA;MACA5H,mBAAA;QAAAmG,IAAA;QAAA0B,QAAA;MAAA,GAAApG,IAAA,CACA,UAAA+E,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAmB,MAAA,CAAApE,cAAA,GAAAgD,GAAA,CAAAE,IAAA,CAAAA,IAAA;QACA;UACAkB,MAAA,CAAAhB,QAAA;YACAC,OAAA,EAAAL,GAAA,CAAAM,OAAA;YACAC,IAAA;UACA;QACA;MACA;IACA;IACA9B,aAAA,WAAAA,cAAA;MAAA,IAAA6C,MAAA;MAAA,OAAAzD,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAwD,SAAA;QAAA,OAAAzD,mBAAA,GAAAK,IAAA,UAAAqD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnD,IAAA,GAAAmD,SAAA,CAAAlD,IAAA;YAAA;cAAAkD,SAAA,CAAAlD,IAAA;cAAA,OACA9E,aAAA,KAAAwB,IAAA,WAAA+E,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAqB,MAAA,CAAArE,mBAAA,GAAA+C,GAAA,CAAAE,IAAA,IAAAjD,mBAAA;gBACA;kBACAqE,MAAA,CAAAlB,QAAA;oBACAC,OAAA,EAAAL,GAAA,CAAAM,OAAA;oBACAC,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAkB,SAAA,CAAA5C,IAAA;UAAA;QAAA,GAAA0C,QAAA;MAAA;IACA;IACAG,aAAA,WAAAA,cAAAC,CAAA;MACA,KAAA/F,IAAA,CAAAO,eAAA;MACA,KAAAwF,CAAA;QACA;MACA;MACA,KAAAb,aAAA;IACA;IAEAc,cAAA,WAAAA,eAAAjB,KAAA;IAEAkB,WAAA,WAAAA,YAAA;MACA,KAAAtG,aAAA;IACA;IAEAuG,WAAA,WAAAA,YAAA;MACA,KAAAC,KAAA,SAAAC,WAAA;MACA,KAAAjF,MAAA;IACA;IAEAkF,YAAA,WAAAA,aAAAC,GAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QACAjI,IAAA;QACAkI,KAAA;UACAC,QAAA,OAAAhH,UAAA;UACAiH,OAAA,OAAAlF,KAAA,gBAAAC,UAAA;UACAkF,WAAA;UACAvF,mBAAA,OAAAA,mBAAA;UACAwF,KAAA,EAAAC,kBAAA,CAAAC,IAAA,CAAAC,SAAA,CAAAV,GAAA;QACA;MACA;IACA;IAEAW,UAAA,WAAAA,WAAAX,GAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QACAjI,IAAA;QACAkI,KAAA;UACA9B,IAAA;UACA+B,QAAA,OAAAhH,UAAA;UACAiH,OAAA,OAAAlF,KAAA,gBAAAC,UAAA;UACAkF,WAAA;UACAvF,mBAAA,OAAAA,mBAAA;UACAwF,KAAA,EAAAC,kBAAA,CAAAC,IAAA,CAAAC,SAAA,CAAAV,GAAA;QACA;MACA;IACA;IAEA;IACAY,8BAAA,WAAAA,+BAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,IAAA,QAAAvI,UAAA,CAAA2E,GAAA,WAAAC,IAAA;QACA;UACAtD,OAAA,EAAAsD,IAAA,CAAAtD,OAAA;UACAF,cAAA,EAAAwD,IAAA,CAAAxD,cAAA;UACAK,YAAA,EAAAmD,IAAA,CAAAnD,YAAA;UACAD,cAAA,EAAAoD,IAAA,CAAApD,cAAA;UACAgH,aAAA,EAAA5D,IAAA,CAAA4D;QACA;MACA;MACA5J,8BAAA;QACAwG,YAAA,OAAAxC,KAAA,YAAAC,UAAA;QACAwC,SAAA,OAAAxE,UAAA;QACA0H,IAAA,EAAAA;MACA,GAAA/H,IAAA,WAAA+E,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA8C,MAAA,CAAA3C,QAAA;YACAC,OAAA;YACAE,IAAA;UACA;UACAwC,MAAA,CAAArF,SAAA;QACA;UACAqF,MAAA,CAAA3C,QAAA;YACAC,OAAA,EAAAL,GAAA,CAAAM,OAAA;YACAC,IAAA;UACA;QACA;MACA;IACA;IAEA;IACA2C,eAAA,WAAAA,gBAAAC,GAAA,EAAAC,KAAA;MACA,KAAAxH,IAAA;QACAC,cAAA;QACAC,UAAA;QACAC,OAAA;QACAK,eAAA;QACAH,cAAA;QACAC,YAAA;QACAC,eAAA;MACA;MACA,KAAAuC,aAAA;MACA,KAAAC,gBAAA;MACA,KAAAjB,SAAA;IACA;IACA;IACA2F,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAApG,MAAA;MACA,IAAAqG,SAAA;MACA,IAAAC,KAAA;MACA,IAAAC,KAAA;MACArK,0BAAA;QAEAmH,IAAA,OAAAlD,KAAA,YAAAC,UAAA;QACAwC,SAAA,OAAAxE;MACA,GAAAL,IAAA,WAAA+E,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAzC,OAAA,CAAAC,GAAA,CAAAuC,GAAA;UACA,IAAAA,GAAA,CAAAE,IAAA,IAAAF,GAAA,CAAAE,IAAA,CAAAX,MAAA;YACAS,GAAA,CAAAE,IAAA,CAAAd,GAAA,WAAAsE,CAAA;cAAA,IAAAC,OAAA,EAAAC,iBAAA;cACAL,SAAA,CAAAnB,IAAA,CAAAsB,CAAA,CAAAG,IAAA;cACAL,KAAA,CAAApB,IAAA,EAAAuB,OAAA,GAAAD,CAAA,CAAAI,IAAA,cAAAH,OAAA,cAAAA,OAAA;cACAF,KAAA,CAAArB,IAAA,EAAAwB,iBAAA,GAAAF,CAAA,CAAAK,cAAA,cAAAH,iBAAA,cAAAA,iBAAA;cACAN,MAAA,CAAAU,SAAA;gBACAxG,OAAA,CAAAC,GAAA;gBACA,IAAAwG,QAAA,GAAAX,MAAA,CAAAvB,KAAA,CAAAkC,QAAA;gBACA,IAAAX,MAAA,CAAAnG,OAAA;kBACAmG,MAAA,CAAAnG,OAAA,GAAAtD,OAAA,CAAAqK,IAAA,CAAAD,QAAA;gBACA;gBAEA,IAAAE,aAAA;kBACAC,SAAA;oBACAC,UAAA;oBACAC,WAAA;kBACA;gBACA;gBACA,IAAAC,YAAA;kBACAlJ,KAAA;oBACAmJ,IAAA;oBACAC,SAAA;sBACAC,QAAA;sBACAC,KAAA;oBACA;kBACA;kBACAC,OAAA;oBACAC,IAAA;oBACAC,OAAA;kBACA;kBACAC,MAAA;oBACAC,IAAA;oBACAC,SAAA;oBACAC,UAAA;oBACA5K,IAAA;oBACAmK,SAAA;sBACAC,QAAA;sBACAC,KAAA;oBACA;kBACA;kBACAQ,IAAA;oBACAC,IAAA;oBACAC,KAAA;oBACAC,MAAA;oBACAC,YAAA;kBACA;kBACAC,KAAA;oBACAlL,IAAA,EAAAiJ,SAAA;oBACAkC,QAAA;sBAAAC,MAAA;oBAAA;oBACAC,SAAA;sBAAAd,IAAA;oBAAA;oBACAe,SAAA;sBAAAf,IAAA;oBAAA;kBACA;kBACAgB,KAAA,GAEA;kBACAC,MAAA,GACA;oBACA3L,IAAA;oBACAoG,IAAA;oBACAwF,MAAA;oBACAC,QAAA,EAAA7B,aAAA;oBACA7J,IAAA,EAAAkJ,KAAA;oBACAY,SAAA;sBAAAO,KAAA;oBAAA;kBACA;oBACAxK,IAAA;oBACAoG,IAAA;oBACAwF,MAAA;oBACAC,QAAA,EAAA7B,aAAA;oBACA7J,IAAA,EAAAmJ,KAAA;oBACAW,SAAA;sBAAAO,KAAA;oBAAA;kBACA;gBAEA;gBACArB,MAAA,CAAAnG,OAAA,CAAA8I,SAAA,CAAA1B,YAAA;cACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
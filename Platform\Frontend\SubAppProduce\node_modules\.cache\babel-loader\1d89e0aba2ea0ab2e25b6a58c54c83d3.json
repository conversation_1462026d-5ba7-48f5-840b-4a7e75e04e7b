{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\router\\modules\\PRO\\bom\\index.js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\router\\modules\\PRO\\bom\\index.js", "mtime": 1757468111940}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["PROBOMLevelConfig", "Promise", "resolve", "then", "_interopRequireWildcard", "require", "PROPartsConfig", "PROComponentConfig", "PROHalfPartConfig", "PROStructureTypeConfig", "PROBomImportTemplateConfig"], "sources": ["D:/project/platform_framework_master/platform_framework/Platform/Frontend/SubAppProduce/src/router/modules/PRO/bom/index.js"], "sourcesContent": ["export default {\r\n  PROBOMLevelConfig: () => import('@/views/PRO/bom-setting/bom-level-config/index.vue'),\r\n  PROPartsConfig: () => import('@/views/PRO/bom-setting/part-config/index.vue'),\r\n  PROComponentConfig: () => import('@/views/PRO/bom-setting/com-config/index.vue'),\r\n  PROHalfPartConfig: () => import('@/views/PRO/bom-setting/half-part-config/index.vue'),\r\n  PROStructureTypeConfig: () => import('@/views/PRO/bom-setting/structure-type-config/index.vue'),\r\n  PROBomImportTemplateConfig: () => import('@/views/PRO/bom-setting/bom-import-temp-config/index.vue')\r\n}\r\n"], "mappings": ";;;;AAAA,eAAe;EACbA,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAA;IAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,oDAAoD;IAAA;EAAA,CAAC;EACrFC,cAAc,EAAE,SAAhBA,cAAcA,CAAA;IAAA,OAAAL,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,+CAA+C;IAAA;EAAA,CAAC;EAC7EE,kBAAkB,EAAE,SAApBA,kBAAkBA,CAAA;IAAA,OAAAN,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,8CAA8C;IAAA;EAAA,CAAC;EAChFG,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAA;IAAA,OAAAP,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,oDAAoD;IAAA;EAAA,CAAC;EACrFI,sBAAsB,EAAE,SAAxBA,sBAAsBA,CAAA;IAAA,OAAAR,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,yDAAyD;IAAA;EAAA,CAAC;EAC/FK,0BAA0B,EAAE,SAA5BA,0BAA0BA,CAAA;IAAA,OAAAT,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,0DAA0D;IAAA;EAAA;AACrG,CAAC", "ignoreList": []}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\quality_Inspection\\quality_summary\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\quality_Inspection\\quality_summary\\index.vue", "mtime": 1757572678842}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["fullCheck", "spotCheck", "GetDictionaryDetailListByCode", "GetNodeList", "GetProjectPageList", "SelectUser", "ChangeCheckUser", "name", "components", "data", "exportLoading", "activeName", "form", "Status", "Check_Result", "Project_Id", "Check_Object_Type", "SteelName", "Check_Node_Id", "Number_Like", "Pick_Date", "BeginDate", "EndDate", "Check_UserIds", "CheckNodeList", "CheckObjectData", "check_object_id", "ProjectNameData", "Check_Style", "selectList", "assignLoading", "assignDialogVisible", "assignForm", "userIds", "watch", "handler", "newName", "old<PERSON>ame", "deep", "mounted", "getCheckType", "getProjectOption", "methods", "_this", "Page", "PageSize", "then", "res", "IsSucceed", "Data", "$message", "message", "Message", "type", "_this2", "dictionaryCode", "catch", "console", "log", "changeObject", "val", "_this$CheckObjectData", "_this3", "checkObj", "find", "v", "Display_Name", "Id", "handleSearch", "$refs", "fullCheckRef", "fetchData", "spotCheckRef", "handleExport", "exportTb", "setExportLoading", "handleQualityAssign", "length", "warning", "handleAssignClose", "handleAssignSave", "_this4", "sheetIds", "map", "item", "SheetId", "ids", "success", "error", "finally"], "sources": ["src/views/PRO/quality_Inspection/quality_summary/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <div class=\"wrapper-c\">\r\n      <div class=\"header_tab\">\r\n        <el-tabs v-model=\"activeName\">\r\n          <el-tab-pane label=\"全检\" name=\"全检\" />\r\n          <el-tab-pane label=\"抽检\" name=\"抽检\" />\r\n        </el-tabs>\r\n      </div>\r\n      <div class=\"search_wrapper\">\r\n        <el-form ref=\"form\" :model=\"form\" label-width=\"80px\">\r\n          <el-row>\r\n            <el-col :span=\"5\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item label=\"质检对象\" prop=\"Check_Object_Type\">\r\n                <el-select\r\n                  v-model=\"form.Check_Object_Type\"\r\n                  filterable\r\n                  clearable\r\n                  placeholder=\"请选择\"\r\n                  @change=\"changeObject\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in CheckObjectData\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Display_Name\"\r\n                    :value=\"item.Display_Name\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item label=\"质检节点\" prop=\"Check_Node_Id\">\r\n                <el-select\r\n                  v-model=\"form.Check_Node_Id\"\r\n                  filterable\r\n                  clearable\r\n                  placeholder=\"请选择\"\r\n                  :disabled=\"!form.Check_Object_Type\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in CheckNodeList\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Display_Name\"\r\n                    :value=\"item.Id\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\" :lg=\"4\" :xl=\"4\">\r\n              <el-form-item label=\"质检结果\" prop=\"Check_Result\">\r\n                <el-select\r\n                  v-model=\"form.Check_Result\"\r\n                  filterable\r\n                  clearable\r\n                  placeholder=\"请选择\"\r\n                >\r\n                  <el-option label=\"合格\" :value=\"'合格'\" />\r\n                  <el-option label=\"不合格\" :value=\"'不合格'\" />\r\n                  <el-option v-if=\"activeName === '全检'\" label=\"未一次合格\" :value=\"'未一次合格'\" />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item label=\"质检时间\" prop=\"Pick_Date\">\r\n                <el-date-picker\r\n                  v-model=\"form.Pick_Date\"\r\n                  type=\"daterange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item label=\"质检人\" prop=\"Check_UserIds\">\r\n                <SelectUser v-model=\"form.Check_UserIds\" multiple collapse-tags />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row>\r\n            <el-col :span=\"5\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item label=\"质检单号\" prop=\"Number_Like\">\r\n                <el-input\r\n                  v-model=\"form.Number_Like \"\r\n                  type=\"text\"\r\n                  placeholder=\"请输入质检单号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item\r\n                v-if=\"activeName == '全检'\"\r\n                label=\"名称\"\r\n                prop=\"SteelName\"\r\n              >\r\n                <el-input\r\n                  v-model=\"form.SteelName\"\r\n                  type=\"text\"\r\n                  placeholder=\"请输入（空格间隔筛选多个）\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\" :lg=\"4\" :xl=\"4\">\r\n              <el-form-item label=\"单据状态\" prop=\"Status\">\r\n                <el-select\r\n                  v-model=\"form.Status\"\r\n                  filterable\r\n                  clearable\r\n                  placeholder=\"请选择\"\r\n                >\r\n                  <el-option label=\"草稿\" :value=\"'草稿'\" />\r\n                  <el-option label=\"待整改\" :value=\"'待整改'\" />\r\n                  <el-option label=\"待复核\" :value=\"'待复核'\" />\r\n                  <el-option label=\"待质检\" :value=\"'待质检'\" />\r\n                  <el-option label=\"已完成\" :value=\"'已完成'\" />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item\r\n                v-if=\"activeName == '全检'\"\r\n                label=\"项目名称\"\r\n                prop=\"Project_Id\"\r\n              >\r\n                <el-select\r\n                  v-model=\"form.Project_Id\"\r\n                  filterable\r\n                  clearable\r\n                  placeholder=\"请选择\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in ProjectNameData\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Short_Name\"\r\n                    :value=\"item.Sys_Project_Id\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item label-width=\"16px\">\r\n                <el-button type=\"primary\" @click=\"handleSearch\">搜索</el-button>\r\n                <el-button\r\n                  @click=\"\r\n                    $refs['form'].resetFields();\r\n                    handleSearch();\r\n                  \"\r\n                >重置</el-button>\r\n                <el-button type=\"success\" :loading=\"exportLoading\" @click=\"handleExport\">导出</el-button>\r\n                <el-button\r\n                  type=\"primary\"\r\n                  :disabled=\"selectList.length == 0\"\r\n                  @click=\"handleQualityAssign\"\r\n                >质检分配</el-button>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form>\r\n      </div>\r\n\r\n      <div class=\"main-wrapper\">\r\n        <!--        <el-button style=\"margin: 10px 0 0 10px\" @click=\"\">导出</el-button>-->\r\n\r\n        <full-check\r\n          v-if=\"activeName == '全检'\"\r\n          ref=\"fullCheckRef\"\r\n          :search-detail=\"form\"\r\n          @setExportLoading=\"setExportLoading\"\r\n          @selectChange=\"(val)=>selectList = val\"\r\n        />\r\n        <spot-check\r\n          v-if=\"activeName == '抽检'\"\r\n          ref=\"spotCheckRef\"\r\n          :search-detail=\"form\"\r\n          @setExportLoading=\"setExportLoading\"\r\n          @selectChange=\"(val)=>selectList = val\"\r\n        />\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 质检分配弹窗 -->\r\n    <el-dialog\r\n      title=\"质检分配\"\r\n      :visible.sync=\"assignDialogVisible\"\r\n      width=\"400px\"\r\n      class=\"plm-custom-dialog\"\r\n      @close=\"handleAssignClose\"\r\n    >\r\n      <el-form :model=\"assignForm\" label-width=\"80px\">\r\n        <el-form-item label=\"选择人员\" required>\r\n          <SelectUser\r\n            v-model=\"assignForm.userIds\"\r\n            placeholder=\"请选择质检人员\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"handleAssignClose\">取消</el-button>\r\n        <el-button type=\"primary\" :loading=\"assignLoading\" @click=\"handleAssignSave\">保存</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport fullCheck from './components/fullCheck.vue'\r\nimport spotCheck from './components/spotCheck.vue'\r\nimport { GetDictionaryDetailListByCode, GetNodeList } from '@/api/PRO/factorycheck'\r\nimport { GetProjectPageList } from '@/api/PRO/project'\r\nimport SelectUser from '@/components/Select/SelectUser/index.vue'\r\nimport { ChangeCheckUser } from '@/api/PRO/qualityInspect/quality-management'\r\n\r\nexport default {\r\n  name: 'PROStartInspect',\r\n  components: {\r\n    SelectUser,\r\n    fullCheck,\r\n    spotCheck\r\n  },\r\n  data() {\r\n    return {\r\n      exportLoading: false,\r\n      activeName: '全检',\r\n      form: {\r\n        Status: '', // 单据状态\r\n        Check_Result: '', // 质检结果\r\n        Project_Id: '', // 项目名称\r\n        Check_Object_Type: '', // 质检对象\r\n        SteelName: '', // 名称\r\n        Check_Node_Id: '', // 质检节点\r\n        Number_Like: '', // 质检单号\r\n        Pick_Date: [], // 质检时间\r\n        BeginDate: null,\r\n        EndDate: null,\r\n        Check_UserIds: []\r\n      },\r\n      CheckNodeList: [], // 质检节点\r\n      CheckObjectData: [], // 质检对象\r\n      check_object_id: null,\r\n      ProjectNameData: [],\r\n      Check_Style: '1',\r\n      selectList: [],\r\n      assignLoading: false,\r\n      assignDialogVisible: false,\r\n      assignForm: {\r\n        userIds: []\r\n      } // 当前要分配的行数据\r\n    }\r\n  },\r\n  watch: {\r\n    activeName: {\r\n      handler(newName, oldName) {\r\n        this.selectList = []\r\n        this.form = {\r\n          Status: '', // 单据状态\r\n          Check_Result: '', // 质检结果\r\n          Project_Id: '', // 项目名称\r\n          Check_Object_Type: '', // 质检对象\r\n          SteelName: '', // 名称\r\n          Check_Node_Id: '', // 质检节点\r\n          Number_Like: '' // 质检单号\r\n        }\r\n        if (newName === '全检') {\r\n          this.Check_Style = '1'\r\n        } else if (newName === '抽检') {\r\n          this.Check_Style = '0'\r\n        }\r\n        this.exportLoading = false\r\n      },\r\n\r\n      deep: true\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getCheckType()\r\n    this.getProjectOption()\r\n  },\r\n  methods: {\r\n    // 获取项目\r\n    getProjectOption() {\r\n      GetProjectPageList({\r\n        Page: 1,\r\n        PageSize: -1\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.ProjectNameData = res.Data.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getCheckType() {\r\n      GetDictionaryDetailListByCode({ dictionaryCode: 'Quality_Code' })\r\n        .then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.CheckObjectData = res.Data\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: 'res.Message'\r\n            })\r\n          }\r\n        })\r\n        .catch(() => {\r\n          console.log('sdfd')\r\n        })\r\n    },\r\n    changeObject(val) {\r\n      console.log('val', this.form.Check_Object_Type)\r\n      this.form.Check_Node_Id = ''\r\n      const checkObj = this.CheckObjectData.find((v) => {\r\n        return v.Display_Name === val\r\n      })?.Id\r\n      console.log(this.check_object_id)\r\n\r\n      GetNodeList({ check_object_id: checkObj, Check_Style: this.Check_Style }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.CheckNodeList = res.Data\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleSearch() {\r\n      console.log('this.form.Code', this.form.Check_Object_Type)\r\n      // this.form.Code = this.form.SearchCode.trim().replaceAll(\" \", \"\\n\");\r\n      if (this.activeName === '全检') {\r\n        this.$refs.fullCheckRef.fetchData(1)\r\n      } else if (this.activeName === '抽检') {\r\n        this.$refs.spotCheckRef.fetchData(1)\r\n      }\r\n    },\r\n    handleExport() {\r\n      if (this.activeName === '全检') {\r\n        this.$refs.fullCheckRef.exportTb()\r\n      } else if (this.activeName === '抽检') {\r\n        this.$refs.spotCheckRef.exportTb()\r\n      }\r\n    },\r\n    setExportLoading(val) {\r\n      console.log('v', val)\r\n      this.exportLoading = val\r\n    },\r\n    // 质检分配相关方法\r\n    handleQualityAssign() {\r\n      if (this.selectList.length === 0) {\r\n        this.$message.warning('请先选择要分配的数据')\r\n        return\r\n      }\r\n      this.assignForm.userIds = []\r\n      this.assignDialogVisible = true\r\n    },\r\n    handleAssignClose() {\r\n      this.assignDialogVisible = false\r\n      this.assignForm.userIds = []\r\n    },\r\n    handleAssignSave() {\r\n      if (!this.assignForm.userIds || this.assignForm.userIds.length === 0) {\r\n        this.$message.warning('请选择质检人员')\r\n        return\r\n      }\r\n\r\n      this.assignLoading = true\r\n\r\n      // 调用质检分配接口\r\n      const sheetIds = this.selectList.map(item => item.SheetId)\r\n      ChangeCheckUser({\r\n        ids: sheetIds,\r\n        userIds: this.assignForm.userIds\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message.success('质检分配成功')\r\n          this.handleAssignClose()\r\n          this.handleSearch() // 刷新列表数据\r\n        } else {\r\n          this.$message.error(res.Message || '质检分配失败')\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('质检分配失败')\r\n        console.error('质检分配错误:', error)\r\n      }).finally(() => {\r\n        this.assignLoading = false\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.wrapper-c {\r\n  height: 100%;\r\n  background: #fff;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .main-wrapper {\r\n    background: #ffffff;\r\n    // height: 0;\r\n    flex: 1;\r\n    // display: flex;\r\n    // flex-direction: column;\r\n  }\r\n  .header_tab {\r\n    padding: 0 16px 0 16px;\r\n    box-sizing: border-box;\r\n  }\r\n}\r\n.search_wrapper {\r\n  padding: 16px 16px 0;\r\n  box-sizing: border-box;\r\n  ::v-deep .el-form-item {\r\n    .el-form-item__content {\r\n      & > .el-input {\r\n        width: 100%;\r\n      }\r\n      & > .el-select {\r\n        width: 100%;\r\n      }\r\n    }\r\n    .el-date-editor--daterange.el-input__inner {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .el-tabs__header {\r\n  margin: 0 !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8MA,OAAAA,SAAA;AACA,OAAAC,SAAA;AACA,SAAAC,6BAAA,EAAAC,WAAA;AACA,SAAAC,kBAAA;AACA,OAAAC,UAAA;AACA,SAAAC,eAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAH,UAAA,EAAAA,UAAA;IACAL,SAAA,EAAAA,SAAA;IACAC,SAAA,EAAAA;EACA;EACAQ,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,UAAA;MACAC,IAAA;QACAC,MAAA;QAAA;QACAC,YAAA;QAAA;QACAC,UAAA;QAAA;QACAC,iBAAA;QAAA;QACAC,SAAA;QAAA;QACAC,aAAA;QAAA;QACAC,WAAA;QAAA;QACAC,SAAA;QAAA;QACAC,SAAA;QACAC,OAAA;QACAC,aAAA;MACA;MACAC,aAAA;MAAA;MACAC,eAAA;MAAA;MACAC,eAAA;MACAC,eAAA;MACAC,WAAA;MACAC,UAAA;MACAC,aAAA;MACAC,mBAAA;MACAC,UAAA;QACAC,OAAA;MACA;IACA;EACA;EACAC,KAAA;IACAvB,UAAA;MACAwB,OAAA,WAAAA,QAAAC,OAAA,EAAAC,OAAA;QACA,KAAAR,UAAA;QACA,KAAAjB,IAAA;UACAC,MAAA;UAAA;UACAC,YAAA;UAAA;UACAC,UAAA;UAAA;UACAC,iBAAA;UAAA;UACAC,SAAA;UAAA;UACAC,aAAA;UAAA;UACAC,WAAA;QACA;QACA,IAAAiB,OAAA;UACA,KAAAR,WAAA;QACA,WAAAQ,OAAA;UACA,KAAAR,WAAA;QACA;QACA,KAAAlB,aAAA;MACA;MAEA4B,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;IACA,KAAAC,gBAAA;EACA;EACAC,OAAA;IACA;IACAD,gBAAA,WAAAA,iBAAA;MAAA,IAAAE,KAAA;MACAvC,kBAAA;QACAwC,IAAA;QACAC,QAAA;MACA,GAAAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAL,KAAA,CAAAhB,eAAA,GAAAoB,GAAA,CAAAE,IAAA,CAAAA,IAAA;QACA;UACAN,KAAA,CAAAO,QAAA;YACAC,OAAA,EAAAJ,GAAA,CAAAK,OAAA;YACAC,IAAA;UACA;QACA;MACA;IACA;IACAb,YAAA,WAAAA,aAAA;MAAA,IAAAc,MAAA;MACApD,6BAAA;QAAAqD,cAAA;MAAA,GACAT,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAM,MAAA,CAAA7B,eAAA,GAAAsB,GAAA,CAAAE,IAAA;QACA;UACAK,MAAA,CAAAJ,QAAA;YACAG,IAAA;YACAF,OAAA;UACA;QACA;MACA,GACAK,KAAA;QACAC,OAAA,CAAAC,GAAA;MACA;IACA;IACAC,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,qBAAA;QAAAC,MAAA;MACAL,OAAA,CAAAC,GAAA,aAAA9C,IAAA,CAAAI,iBAAA;MACA,KAAAJ,IAAA,CAAAM,aAAA;MACA,IAAA6C,QAAA,IAAAF,qBAAA,QAAApC,eAAA,CAAAuC,IAAA,WAAAC,CAAA;QACA,OAAAA,CAAA,CAAAC,YAAA,KAAAN,GAAA;MACA,gBAAAC,qBAAA,uBAFAA,qBAAA,CAEAM,EAAA;MACAV,OAAA,CAAAC,GAAA,MAAAhC,eAAA;MAEAvB,WAAA;QAAAuB,eAAA,EAAAqC,QAAA;QAAAnC,WAAA,OAAAA;MAAA,GAAAkB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAc,MAAA,CAAAtC,aAAA,GAAAuB,GAAA,CAAAE,IAAA;QACA;UACAa,MAAA,CAAAZ,QAAA;YACAG,IAAA;YACAF,OAAA,EAAAJ,GAAA,CAAAK;UACA;QACA;MACA;IACA;IACAgB,YAAA,WAAAA,aAAA;MACAX,OAAA,CAAAC,GAAA,wBAAA9C,IAAA,CAAAI,iBAAA;MACA;MACA,SAAAL,UAAA;QACA,KAAA0D,KAAA,CAAAC,YAAA,CAAAC,SAAA;MACA,gBAAA5D,UAAA;QACA,KAAA0D,KAAA,CAAAG,YAAA,CAAAD,SAAA;MACA;IACA;IACAE,YAAA,WAAAA,aAAA;MACA,SAAA9D,UAAA;QACA,KAAA0D,KAAA,CAAAC,YAAA,CAAAI,QAAA;MACA,gBAAA/D,UAAA;QACA,KAAA0D,KAAA,CAAAG,YAAA,CAAAE,QAAA;MACA;IACA;IACAC,gBAAA,WAAAA,iBAAAf,GAAA;MACAH,OAAA,CAAAC,GAAA,MAAAE,GAAA;MACA,KAAAlD,aAAA,GAAAkD,GAAA;IACA;IACA;IACAgB,mBAAA,WAAAA,oBAAA;MACA,SAAA/C,UAAA,CAAAgD,MAAA;QACA,KAAA3B,QAAA,CAAA4B,OAAA;QACA;MACA;MACA,KAAA9C,UAAA,CAAAC,OAAA;MACA,KAAAF,mBAAA;IACA;IACAgD,iBAAA,WAAAA,kBAAA;MACA,KAAAhD,mBAAA;MACA,KAAAC,UAAA,CAAAC,OAAA;IACA;IACA+C,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,UAAAjD,UAAA,CAAAC,OAAA,SAAAD,UAAA,CAAAC,OAAA,CAAA4C,MAAA;QACA,KAAA3B,QAAA,CAAA4B,OAAA;QACA;MACA;MAEA,KAAAhD,aAAA;;MAEA;MACA,IAAAoD,QAAA,QAAArD,UAAA,CAAAsD,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,OAAA;MAAA;MACA/E,eAAA;QACAgF,GAAA,EAAAJ,QAAA;QACAjD,OAAA,OAAAD,UAAA,CAAAC;MACA,GAAAa,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAiC,MAAA,CAAA/B,QAAA,CAAAqC,OAAA;UACAN,MAAA,CAAAF,iBAAA;UACAE,MAAA,CAAAb,YAAA;QACA;UACAa,MAAA,CAAA/B,QAAA,CAAAsC,KAAA,CAAAzC,GAAA,CAAAK,OAAA;QACA;MACA,GAAAI,KAAA,WAAAgC,KAAA;QACAP,MAAA,CAAA/B,QAAA,CAAAsC,KAAA;QACA/B,OAAA,CAAA+B,KAAA,YAAAA,KAAA;MACA,GAAAC,OAAA;QACAR,MAAA,CAAAnD,aAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\index.vue", "mtime": 1758266753125}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgTWFpblBhZ2UgZnJvbSAnLi9tYWluUGFnZScNCmltcG9ydCB7IG1hcEdldHRlcnMgfSBmcm9tICd2dWV4Jw0KaW1wb3J0IHsgR2V0Qk9NSW5mbywgZ2V0Qm9tQ29kZSB9IGZyb20gJ0Avdmlld3MvUFJPL2JvbS1zZXR0aW5nL3V0aWxzJw0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnUFJPVGFza0xpc3QnLA0KICBjb21wb25lbnRzOiB7DQogICAgTWFpblBhZ2UNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgYm9tTGlzdDogW10sDQogICAgICBwZ0xvYWRpbmc6IGZhbHNlLA0KICAgICAgYWN0aXZlTmFtZTogZ2V0Qm9tQ29kZSgnLTEnKQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCg0KICAgIGlzQ29tKCkgew0KICAgICAgcmV0dXJuIHRoaXMuYWN0aXZlTmFtZSA9PT0gZ2V0Qm9tQ29kZSgnLTEnKQ0KICAgIH0sDQogICAgLi4ubWFwR2V0dGVycygndGVuYW50JywgWydpc1ZlcnNpb25Gb3VyJ10pLA0KICAgIGhhc1VuaXRQYXJ0KCkgew0KICAgICAgcmV0dXJuIHRoaXMuaXNWZXJzaW9uRm91cg0KICAgIH0NCiAgfSwNCiAgYXN5bmMgY3JlYXRlZCgpIHsNCiAgICBjb25zdCB7IGxpc3QgfSA9IGF3YWl0IEdldEJPTUluZm8oKQ0KICAgIHRoaXMuYm9tTGlzdCA9IGxpc3QgfHwgW10NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGhhbmRsZUNsaWNrKHRhYiwgZXZlbnQpIHsNCiAgICAgIGNvbnNvbGUubG9nKHRhYiwgZXZlbnQpDQogICAgfSwNCiAgICBhc3luYyBjaGFuZ2VUYWIodGFiKSB7DQogICAgICB0aGlzLnBnTG9hZGluZyA9IHRydWUNCiAgICAgIHRoaXMuYWN0aXZlTmFtZSA9IHRhYi5uYW1lDQogICAgICBhd2FpdCB0aGlzLmdldFRiQ29uZmlnSW5mbygpDQogICAgICB0aGlzLmZldGNoRGF0YSgxKQ0KICAgIH0sDQogICAgZ2V0VGJDb25maWdJbmZvKCkgew0KDQogICAgfSwNCiAgICBmZXRjaERhdGEoKSB7DQoNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/plan-production/task-list", "sourcesContent": ["<template>\r\n  <div class=\"container abs100\">\r\n    <el-tabs v-model=\"activeName\" @tab-click=\"changeTab\">\r\n      <!-- <el-tab-pane label=\"构件任务单\" name=\"com\" />\r\n      <el-tab-pane label=\"部件任务单\" name=\"unitPart\" />\r\n      <el-tab-pane label=\"零件任务单\" name=\"part\" /> -->\r\n      <el-tab-pane v-for=\"item in bomList\" :key=\"item.Code\" :label=\"item.Display_Name\" :name=\"item.Code\" />\r\n    </el-tabs>\r\n    <div class=\"main-wrapper\">\r\n      <component :is=\"'MainPage'\" :key=\"activeName\" :has-unit-part=\"hasUnitPart\" :page-type=\"activeName\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport MainPage from './mainPage'\r\nimport { mapGetters } from 'vuex'\r\nimport { GetBOMInfo, getBomCode } from '@/views/PRO/bom-setting/utils'\r\nexport default {\r\n  name: 'PROTaskList',\r\n  components: {\r\n    MainPage\r\n  },\r\n  data() {\r\n    return {\r\n      bomList: [],\r\n      pgLoading: false,\r\n      activeName: getBomCode('-1')\r\n    }\r\n  },\r\n  computed: {\r\n\r\n    isCom() {\r\n      return this.activeName === getBomCode('-1')\r\n    },\r\n    ...mapGetters('tenant', ['isVersionFour']),\r\n    hasUnitPart() {\r\n      return this.isVersionFour\r\n    }\r\n  },\r\n  async created() {\r\n    const { list } = await GetBOMInfo()\r\n    this.bomList = list || []\r\n  },\r\n  methods: {\r\n    handleClick(tab, event) {\r\n      console.log(tab, event)\r\n    },\r\n    async changeTab(tab) {\r\n      this.pgLoading = true\r\n      this.activeName = tab.name\r\n      await this.getTbConfigInfo()\r\n      this.fetchData(1)\r\n    },\r\n    getTbConfigInfo() {\r\n\r\n    },\r\n    fetchData() {\r\n\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.container {\r\n  padding: 16px;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .top-x {\r\n    padding: 0 16px;\r\n    margin-bottom: 16px;\r\n    background-color: #ffffff;\r\n\r\n    .top-inner {\r\n      display: flex;\r\n      position: relative;\r\n      overflow: hidden;\r\n\r\n      &:after {\r\n        content: \"\";\r\n        position: absolute;\r\n        left: 0;\r\n        bottom: 0;\r\n        width: 100%;\r\n        height: 2px;\r\n        background-color: #e4e7ed;\r\n        z-index: 1;\r\n      }\r\n\r\n      .item {\r\n        color: #999999;\r\n        display: inline-block;\r\n        padding: 20px 38px;\r\n\r\n        &:hover {\r\n          cursor: pointer;\r\n        }\r\n      }\r\n\r\n      .cs-item-bar {\r\n        display: inline-block;\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 10px;\r\n        height: 2px;\r\n        width: 140px;\r\n        background-color: #409eff;\r\n        z-index: 2;\r\n        transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);\r\n      }\r\n\r\n      .active {\r\n        font-weight: 500;\r\n        color: #298dff;\r\n      }\r\n\r\n      ::v-deep {\r\n        .el-badge__content.is-fixed {\r\n          right: 78px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .main-wrapper{\r\n    flex: 1;\r\n  }\r\n  .el-tabs{\r\n    margin-bottom: 16px;\r\n    background-color: #fff;\r\n    padding-left: 16px;\r\n  }\r\n}\r\n\r\n</style>\r\n"]}]}
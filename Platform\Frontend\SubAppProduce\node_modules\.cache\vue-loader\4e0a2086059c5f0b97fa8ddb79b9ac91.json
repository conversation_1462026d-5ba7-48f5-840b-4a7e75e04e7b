{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\index.vue", "mtime": 1758333327000}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBNYWluUGFnZSBmcm9tICcuL21haW5QYWdlJwppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCcKaW1wb3J0IHsgR2V0Qk9NSW5mbywgZ2V0Qm9tQ29kZSB9IGZyb20gJ0Avdmlld3MvUFJPL2JvbS1zZXR0aW5nL3V0aWxzJwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1BST1Rhc2tMaXN0JywKICBjb21wb25lbnRzOiB7CiAgICBNYWluUGFnZQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGJvbUxpc3Q6IFtdLAogICAgICBwZ0xvYWRpbmc6IGZhbHNlLAogICAgICBhY3RpdmVOYW1lOiBnZXRCb21Db2RlKCctMScpCiAgICB9CiAgfSwKICBjb21wdXRlZDogewoKICAgIGlzQ29tKCkgewogICAgICByZXR1cm4gdGhpcy5hY3RpdmVOYW1lID09PSBnZXRCb21Db2RlKCctMScpCiAgICB9LAogICAgLi4ubWFwR2V0dGVycygndGVuYW50JywgWydpc1ZlcnNpb25Gb3VyJ10pLAogICAgaGFzVW5pdFBhcnQoKSB7CiAgICAgIHJldHVybiB0aGlzLmlzVmVyc2lvbkZvdXIKICAgIH0KICB9LAogIGFzeW5jIGNyZWF0ZWQoKSB7CiAgICBjb25zdCB7IGxpc3QgfSA9IGF3YWl0IEdldEJPTUluZm8oKQogICAgdGhpcy5ib21MaXN0ID0gbGlzdCB8fCBbXQogIH0sCiAgbWV0aG9kczogewogICAgaGFuZGxlQ2xpY2sodGFiLCBldmVudCkgewogICAgICBjb25zb2xlLmxvZyh0YWIsIGV2ZW50KQogICAgfSwKICAgIGFzeW5jIGNoYW5nZVRhYih0YWIpIHsKICAgICAgdGhpcy5wZ0xvYWRpbmcgPSB0cnVlCiAgICAgIHRoaXMuYWN0aXZlTmFtZSA9IHRhYi5uYW1lCiAgICAgIGF3YWl0IHRoaXMuZ2V0VGJDb25maWdJbmZvKCkKICAgICAgdGhpcy5mZXRjaERhdGEoMSkKICAgIH0sCiAgICBnZXRUYkNvbmZpZ0luZm8oKSB7CgogICAgfSwKICAgIGZldGNoRGF0YSgpIHsKCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/plan-production/task-list", "sourcesContent": ["<template>\n  <div class=\"container abs100\">\n    <el-tabs v-model=\"activeName\" @tab-click=\"changeTab\">\n      <!-- <el-tab-pane label=\"构件任务单\" name=\"com\" />\n      <el-tab-pane label=\"部件任务单\" name=\"unitPart\" />\n      <el-tab-pane label=\"零件任务单\" name=\"part\" /> -->\n      <el-tab-pane v-for=\"item in bomList\" :key=\"item.Code\" :label=\"`${item.Display_Name}任务单`\" :name=\"item.Code\" />\n    </el-tabs>\n    <div class=\"main-wrapper\">\n      <component :is=\"'MainPage'\" :key=\"activeName\" :has-unit-part=\"hasUnitPart\" :page-type=\"activeName\" />\n    </div>\n  </div>\n</template>\n\n<script>\nimport MainPage from './mainPage'\nimport { mapGetters } from 'vuex'\nimport { GetBOMInfo, getBomCode } from '@/views/PRO/bom-setting/utils'\nexport default {\n  name: 'PROTaskList',\n  components: {\n    MainPage\n  },\n  data() {\n    return {\n      bomList: [],\n      pgLoading: false,\n      activeName: getBomCode('-1')\n    }\n  },\n  computed: {\n\n    isCom() {\n      return this.activeName === getBomCode('-1')\n    },\n    ...mapGetters('tenant', ['isVersionFour']),\n    hasUnitPart() {\n      return this.isVersionFour\n    }\n  },\n  async created() {\n    const { list } = await GetBOMInfo()\n    this.bomList = list || []\n  },\n  methods: {\n    handleClick(tab, event) {\n      console.log(tab, event)\n    },\n    async changeTab(tab) {\n      this.pgLoading = true\n      this.activeName = tab.name\n      await this.getTbConfigInfo()\n      this.fetchData(1)\n    },\n    getTbConfigInfo() {\n\n    },\n    fetchData() {\n\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.container {\n  padding: 16px;\n  display: flex;\n  flex-direction: column;\n\n  .top-x {\n    padding: 0 16px;\n    margin-bottom: 16px;\n    background-color: #ffffff;\n\n    .top-inner {\n      display: flex;\n      position: relative;\n      overflow: hidden;\n\n      &:after {\n        content: \"\";\n        position: absolute;\n        left: 0;\n        bottom: 0;\n        width: 100%;\n        height: 2px;\n        background-color: #e4e7ed;\n        z-index: 1;\n      }\n\n      .item {\n        color: #999999;\n        display: inline-block;\n        padding: 20px 38px;\n\n        &:hover {\n          cursor: pointer;\n        }\n      }\n\n      .cs-item-bar {\n        display: inline-block;\n        position: absolute;\n        bottom: 0;\n        left: 10px;\n        height: 2px;\n        width: 140px;\n        background-color: #409eff;\n        z-index: 2;\n        transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);\n      }\n\n      .active {\n        font-weight: 500;\n        color: #298dff;\n      }\n\n      ::v-deep {\n        .el-badge__content.is-fixed {\n          right: 78px;\n        }\n      }\n    }\n  }\n\n  .main-wrapper{\n    flex: 1;\n  }\n  .el-tabs{\n    margin-bottom: 16px;\n    background-color: #fff;\n    padding-left: 16px;\n  }\n}\n\n</style>\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\factory\\component\\Card.vue?vue&type=template&id=7b677bc5&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\factory\\component\\Card.vue", "mtime": 1758011160329}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImNhcmQtd3JhcHBlciI+CiAgPGRpdiBjbGFzcz0idG9wLWJveCI+CiAgICA8ZGl2IGNsYXNzPSJ0LWJveCI+CiAgICAgIDxpIGNsYXNzPSJpY29uZm9udCBpY29uLXN0ZWVsIiAvPgogICAgICA8c3Ryb25nIGNsYXNzPSJ0aXRsZSI+e3sgaXRlbS5TaG9ydF9OYW1lIH19PC9zdHJvbmc+CiAgICA8L2Rpdj4KICAgIDxkaXYgY2xhc3M9InQtYm94LWJ0biI+CiAgICAgIDxlbC1idXR0b24gdi1pZj0idHJ1ZSIgc2l6ZT0ibWluaSIgdHlwZT0iZGFuZ2VyIiBAY2xpY2s9ImhhbmRsZURlbGV0ZSgpIj7liKAg6ZmkPC9lbC1idXR0b24+CiAgICAgIDxlbC1idXR0b24gc2l6ZT0ibWluaSIgQGNsaWNrPSJoYW5kbGVFZGl0Ij7nvJYg6L6RPC9lbC1idXR0b24+CiAgICA8L2Rpdj4KICA8L2Rpdj4KICA8ZGl2IGNsYXNzPSJzdWItdGl0bGUiPgogICAge3sgaXRlbS5OYW1lIH19CiAgPC9kaXY+CgogIDxkaXYgY2xhc3M9InRhZy1jb250YWluZXIiPgogICAgPHNwYW4gY2xhc3M9InRhZy1ib3giPnt7IGl0ZW0uQ2F0ZWdvcnkgfX08L3NwYW4+CiAgPC9kaXY+CgogIDwhLS0gICAgPGRpdiBjbGFzcz0iY3MtYm90dG9tIj4KICAgIDxkaXYgY2xhc3M9ImNzLWxhYmVsIj7lr7nmjqXku5PlupPvvJo8L2Rpdj4KICAgIDxzcGFuIHYtZm9yPSIoaXRlbSxpbmRleCkgaW4gV2FyZWhvdXNlTGlzdCIgOmtleT0iaW5kZXgiIGNsYXNzPSJ0aXRsZSI+CiAgICAgIDxpIGNsYXNzPSJpY29uZm9udCBpY29uLWhvbWUzIiAvPgogICAgICA8c3Bhbj57eyBpdGVtIH19PC9zcGFuPgogICAgPC9zcGFuPgogIDwvZGl2Pi0tPgo8L2Rpdj4K"}, null]}
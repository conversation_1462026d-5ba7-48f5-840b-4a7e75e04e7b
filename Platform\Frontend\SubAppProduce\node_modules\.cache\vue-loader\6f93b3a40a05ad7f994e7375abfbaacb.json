{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-product-type\\component\\CompanyAdd.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-product-type\\component\\CompanyAdd.vue", "mtime": 1757468128032}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRDb21wVHlwZVRyZWUgfSBmcm9tICdAL2FwaS9QUk8vY29tcG9uZW50LXR5cGUnDQppbXBvcnQgeyBHZXRQYXJ0VHlwZVRyZWUgfSBmcm9tICdAL2FwaS9QUk8vcGFydFR5cGUnDQppbXBvcnQgeyBHZXRCT01JbmZvIH0gZnJvbSAnQC92aWV3cy9QUk8vYm9tLXNldHRpbmcvdXRpbHMnDQpleHBvcnQgZGVmYXVsdCB7DQogIGNvbXBvbmVudHM6IHsNCg0KICB9LA0KICBwcm9wczogew0KICAgIHR5cGVDb2RlOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnJw0KICAgIH0sDQogICAgdHlwZUlkOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnJw0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgdHJlZURhdGE6IFtdLA0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIGN1cnJlbnROb2RlS2V5OiAnJywNCiAgICAgIGFjdGl2ZVR5cGU6ICctMScsDQogICAgICBib21MaXN0OiBbXQ0KICAgIH0NCiAgfSwNCiAgYXN5bmMgbW91bnRlZCgpIHsNCiAgICBjb25zdCB7IGxpc3QgfSA9IGF3YWl0IEdldEJPTUluZm8oKQ0KICAgIHRoaXMuYm9tTGlzdCA9IGxpc3QNCiAgICB0aGlzLmZldGNoRGF0YSgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBhc3luYyBmZXRjaERhdGEoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlDQogICAgICBsZXQgcmVzDQogICAgICBpZiAodGhpcy5hY3RpdmVUeXBlID09PSAnLTEnKSB7DQogICAgICAgIHJlcyA9IGF3YWl0IEdldENvbXBUeXBlVHJlZSh7IHByb2Zlc3Npb25hbDogdGhpcy50eXBlQ29kZSB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmVzID0gYXdhaXQgR2V0UGFydFR5cGVUcmVlKHsgcHJvZmVzc2lvbmFsSWQ6IHRoaXMudHlwZUlkLCBwYXJ0R3JhZGU6IHRoaXMuYWN0aXZlVHlwZS50b1N0cmluZygpIH0pDQogICAgICB9DQogICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICB0aGlzLnRyZWVEYXRhID0gcmVzLkRhdGENCiAgICAgIH0NCiAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgfSwNCiAgICBoYW5kbGVOb2RlQ2xpY2sobm9kZSkgew0KICAgICAgY29uc29sZS5sb2cobm9kZSkNCiAgICB9LA0KICAgIGhhbmRsZUNsaWNrKHRhYiwgZXZlbnQpIHsNCiAgICAgIHRoaXMuYWN0aXZlVHlwZSA9IHRhYi5uYW1lDQogICAgICB0aGlzLmZldGNoRGF0YSgpDQogICAgfSwNCiAgICBoYW5kbGVBZGRUb0xpc3QoKSB7DQogICAgICBjb25zb2xlLmxvZygn5Yqg5YWl5YiX6KGoJykNCiAgICB9LA0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfmt7vliqAnKQ0KICAgIH0sDQogICAgaGFuZGxlQ2FuY2VsKCkgew0KICAgICAgY29uc29sZS5sb2coJ+WPlua2iCcpDQogICAgICB0aGlzLiRlbWl0KCdjbG9zZScpDQogICAgfQ0KICB9DQoNCn0NCg=="}, {"version": 3, "sources": ["CompanyAdd.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "CompanyAdd.vue", "sourceRoot": "src/views/PRO/project-config/project-product-type/component", "sourcesContent": ["<template>\r\n  <div class=\"company-add-container\">\r\n    <div class=\"title\">\r\n      <el-button type=\"primary\" @click=\"handleAddToList\">加入列表</el-button>\r\n    </div>\r\n    <el-tabs v-model=\"activeType\" type=\"card\" @tab-click=\"handleClick\">\r\n      <el-tab-pane v-for=\"item in bomList\" :key=\"item.Code\" :label=\"item.Display_Name\" :name=\"item.Code\" />\r\n    </el-tabs>\r\n    <div class=\"tree-container\">\r\n      <el-tree\r\n        ref=\"tree\"\r\n        v-loading=\"loading\"\r\n        :current-node-key=\"currentNodeKey\"\r\n        element-loading-text=\"加载中\"\r\n        element-loading-spinner=\"el-icon-loading\"\r\n        empty-text=\"暂无数据\"\r\n        highlight-current\r\n        show-checkbox\r\n        node-key=\"Id\"\r\n        default-expand-all\r\n        :expand-on-click-node=\"false\"\r\n        :data=\"treeData\"\r\n        :props=\"{\r\n          label:'Label',\r\n          children:'Children'\r\n        }\"\r\n        @node-click=\"handleNodeClick\"\r\n      >\r\n        <span slot-scope=\"{ node, data }\" class=\"custom-tree-node\">\r\n          <svg-icon\r\n            :icon-class=\"\r\n              node.expanded ? 'icon-folder-open' : 'icon-folder'\r\n            \"\r\n            class-name=\"class-icon\"\r\n          />\r\n          <span class=\"cs-label\" :title=\"node.label\">{{ node.label }}</span>\r\n        </span>\r\n      </el-tree>\r\n    </div>\r\n    <footer>\r\n      <el-button @click=\"handleCancel\">取 消</el-button>\r\n      <el-button type=\"primary\" @click=\"handleAdd\">确 定</el-button>\r\n    </footer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetCompTypeTree } from '@/api/PRO/component-type'\r\nimport { GetPartTypeTree } from '@/api/PRO/partType'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\nexport default {\r\n  components: {\r\n\r\n  },\r\n  props: {\r\n    typeCode: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    typeId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      treeData: [],\r\n      loading: true,\r\n      currentNodeKey: '',\r\n      activeType: '-1',\r\n      bomList: []\r\n    }\r\n  },\r\n  async mounted() {\r\n    const { list } = await GetBOMInfo()\r\n    this.bomList = list\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    async fetchData() {\r\n      this.loading = true\r\n      let res\r\n      if (this.activeType === '-1') {\r\n        res = await GetCompTypeTree({ professional: this.typeCode })\r\n      } else {\r\n        res = await GetPartTypeTree({ professionalId: this.typeId, partGrade: this.activeType.toString() })\r\n      }\r\n      if (res.IsSucceed) {\r\n        this.treeData = res.Data\r\n      }\r\n      this.loading = false\r\n    },\r\n    handleNodeClick(node) {\r\n      console.log(node)\r\n    },\r\n    handleClick(tab, event) {\r\n      this.activeType = tab.name\r\n      this.fetchData()\r\n    },\r\n    handleAddToList() {\r\n      console.log('加入列表')\r\n    },\r\n    handleAdd() {\r\n      console.log('添加')\r\n    },\r\n    handleCancel() {\r\n      console.log('取消')\r\n      this.$emit('close')\r\n    }\r\n  }\r\n\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import \"~@/styles/mixin.scss\";\r\n.company-add-container {\r\n  height: 70vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .title {\r\n    margin-bottom: 16px;\r\n  }\r\n\r\n  .tree-container {\r\n    flex: 1;\r\n    overflow: hidden;\r\n\r\n    .el-tree {\r\n      @include scrollBar;\r\n      height: 100%;\r\n      overflow: auto;\r\n    }\r\n  }\r\n\r\n  footer {\r\n    text-align: right;\r\n    margin-top: 16px;\r\n  }\r\n}\r\n\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\quality_Inspection\\quality_summary\\components\\fullCheck.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\quality_Inspection\\quality_summary\\components\\fullCheck.vue", "mtime": 1757572678831}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["fullCheck.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoHA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "fullCheck.vue", "sourceRoot": "src/views/PRO/quality_Inspection/quality_summary/components", "sourcesContent": ["<template>\r\n  <div style=\"height: 100%\">\r\n    <div class=\"cs-bottom-wapper\">\r\n      <div class=\"fff tb-x\">\r\n        <vxe-table\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          :loading=\"loading\"\r\n          element-loading-spinner=\"el-icon-loading\"\r\n          element-loading-text=\"拼命加载中\"\r\n          empty-text=\"暂无数据\"\r\n          class=\"cs-vxe-table\"\r\n          height=\"100%\"\r\n          align=\"left\"\r\n          stripe\r\n          :data=\"tbData\"\r\n          resizable\r\n          :tooltip-config=\"{ enterable: true}\"\r\n          :checkbox-config=\"{checkField: 'checked', trigger: 'row'}\"\r\n          :row-config=\"{ isHover: true }\"\r\n          @checkbox-all=\"multiSelectedChange\"\r\n          @checkbox-change=\"multiSelectedChange\"\r\n        >\r\n          <vxe-column fixed=\"left\" type=\"checkbox\" width=\"44\" />\r\n          <template v-for=\"item in columns\">\r\n            <vxe-column\r\n              :key=\"item.Code\"\r\n              :min-width=\"item.Width\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :align=\"item.Align\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n            >\r\n              <template v-if=\"item.Code === 'Rectifier_Code' \" #default=\"{ row }\">\r\n                <el-button\r\n                  v-if=\"Boolean(row.Rectifier_Code)\"\r\n                  type=\"text\"\r\n                  @click=\"handelrectifiction(row)\"\r\n                >查看</el-button>\r\n                <span v-else>-</span>\r\n              </template>\r\n              <template v-if=\"item.Code === 'Check_Result'\" #default=\"{ row }\">\r\n                <span v-if=\"!row.Check_Result\">-</span>\r\n                <template v-else>\r\n                  <el-tag v-if=\"row.Check_Result==='合格'\" type=\"success\">{{ row.Check_Result }}</el-tag>\r\n                  <el-tag v-else type=\"danger\">{{ row.Check_Result }}</el-tag>\r\n                </template>\r\n              </template>\r\n              <template v-else-if=\"item.Code === 'Status'\" #default=\"{ row }\">\r\n                <span v-if=\"row.Status === '已完成'\" class=\"by-dot by-dot-success\">\r\n                  {{ row.Status || \"-\" }}\r\n                </span>\r\n                <span v-else-if=\"row.Status === '待复核' || row.Status === '待整改'\" class=\"by-dot by-dot-primary\">\r\n                  {{ row.Status || \"-\" }}\r\n                </span>\r\n                <span v-else-if=\"row.Status === '待质检' || row.Status === '草稿'\" class=\"by-dot by-dot-info\">\r\n                  {{ row.Status || \"-\" }}\r\n                </span>\r\n                <span v-else>\r\n                  {{ row.Status || \"-\" }}\r\n                </span>\r\n              </template>\r\n              <template v-else #default=\"{ row }\">\r\n                <span>{{ (row[item.Code]==='' || row[item.Code] == null) ? '-' : row[item.Code] }}</span>\r\n              </template>\r\n            </vxe-column>\r\n          </template>\r\n          <vxe-column fixed=\"right\" title=\"操作\" align=\"center\" width=\"60\">\r\n            <template #default=\"{ row }\">\r\n              <el-button\r\n                v-if=\"row.Status !== '待质检' && row.Status !== '草稿'\"\r\n                type=\"text\"\r\n                @click=\"handleInfo(row.SheetId)\"\r\n              >查看</el-button>\r\n              <el-button\r\n                v-if=\"row.Status === '待质检'\"\r\n                type=\"text\"\r\n                @click=\"handelWaitInspect(row)\"\r\n              >查看</el-button>\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n      </div>\r\n      <div class=\"data-info\">\r\n        <el-tag\r\n          size=\"medium\"\r\n          class=\"info-x\"\r\n        >已选 {{ selectList.length }} 条数据\r\n        </el-tag>\r\n        <Pagination\r\n          :total=\"total\"\r\n          :page-sizes=\"tablePageSize\"\r\n          :page.sync=\"queryInfo.Page\"\r\n          :limit.sync=\"queryInfo.PageSize\"\r\n          @pagination=\"pageChange\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      ref=\"content\"\r\n      v-el-drag-dialog\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"width\"\r\n      class=\"plm-custom-dialog\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component :is=\"currentComponent\" ref=\"content\" @close=\"handleClose\" />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport addDialog from '../../start-inspect/components/add/addDialog.vue'\r\nimport rectificationDialog from './rectification/rectificationDialog'\r\n\r\nimport {\r\n  GetPageQualitySummary,\r\n  RectificationRecord\r\n} from '@/api/PRO/qualityInspect/start-Inspect'\r\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'\r\nimport elDragDialog from '@/directive/el-drag-dialog'\r\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\r\nimport { timeFormat } from '@/filters'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nimport { ExportInspsectionSummaryInfo } from '@/api/PRO/factorycheck'\r\nimport { combineURL } from '@/utils'\r\nimport Pagination from '@/components/Pagination/index.vue'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nexport default {\r\n  directives: { elDragDialog },\r\n  components: {\r\n    Pagination,\r\n    DynamicDataTable,\r\n    rectificationDialog,\r\n    addDialog\r\n  },\r\n  mixins: [addRouterPage, getTbInfo],\r\n  props: {\r\n    searchDetail: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      selectList: [],\r\n      width: '60%',\r\n      code: '',\r\n      TypeId: '',\r\n      typeOption: '',\r\n      dialogVisible: false,\r\n      dialogVisible2: false,\r\n      loading: false,\r\n      dialogTitle: '',\r\n      Ismodal: true,\r\n      dialogData: {},\r\n      currentComponent: '',\r\n      tbConfig: {},\r\n      Data: [],\r\n      Date_Time: '',\r\n      columns: [],\r\n      tbData: [],\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: tablePageSize[0]\r\n      },\r\n      total: 0,\r\n      tablePageSize: tablePageSize,\r\n      gridCode: 'Pro_Inpection_summary_list',\r\n      searchHeight: 0,\r\n      CheckResultData: [],\r\n      CheckNodeList: [], // 质检节点\r\n      CheckObjectData: [], // 质检对象\r\n      check_object_id: '',\r\n      ProjectNameData: [],\r\n      check_object_Name: '',\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/check',\r\n          hidden: true,\r\n          component: () =>\r\n            import(\r\n              '@/views/PRO/quality_Inspection/quality_summary/components/Detail'\r\n            ),\r\n          name: 'PROQualityInfoSummary',\r\n          meta: { title: '查看' }\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getTypeList()\r\n  },\r\n  methods: {\r\n    handleSearch() {},\r\n    async getTypeList() {\r\n      let res, data\r\n      res = await GetFactoryProfessionalByCode({\r\n        factoryId: localStorage.getItem('CurReferenceId')\r\n      })\r\n      data = res.Data\r\n      if (res.IsSucceed) {\r\n        this.typeOption = Object.freeze(data)\r\n        console.log(this.typeOption)\r\n        if (this.typeOption.length > 0) {\r\n          this.TypeId = this.typeOption[0]?.Id\r\n          this.code = this.typeOption.find((i) => i.Id === this.TypeId).Code\r\n          this.getTableConfig(this.gridCode + ',' + this.code)\r\n        }\r\n        this.fetchData()\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n\r\n    // 待质检查看\r\n    handelWaitInspect(row) {\r\n      this.generateComponent('查看', 'addDialog')\r\n      this.width = '500px'\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init('查看', row)\r\n      })\r\n    },\r\n    fetchData(page) {\r\n      page && (this.queryInfo.Page = page)\r\n      const SeachParams = JSON.parse(JSON.stringify(this.searchDetail))\r\n      const SteelName = SeachParams.SteelName.trim().replaceAll(' ', '\\n')\r\n      if (SeachParams.Pick_Date && SeachParams.Pick_Date.length === 2) {\r\n        SeachParams.BeginDate = SeachParams.Pick_Date[0]\r\n        SeachParams.EndDate = SeachParams.Pick_Date[1]\r\n      } else {\r\n        SeachParams.BeginDate = null\r\n        SeachParams.EndDate = null\r\n      }\r\n      this.loading = true\r\n      GetPageQualitySummary({\r\n        pageInfo: this.queryInfo,\r\n        ...SeachParams,\r\n        SteelName,\r\n        Check_Style: 1\r\n      })\r\n        .then((res) => {\r\n          if (res.IsSucceed) {\r\n            return this.setGridData(res.Data)\r\n          }\r\n        })\r\n        .catch(console.error)\r\n        .finally(() => {\r\n          // 结束loading\r\n          this.loading = false\r\n        })\r\n    },\r\n\r\n    setGridData(data) {\r\n      this.tbData = this.tbData = data.Data.map((v) => {\r\n        v.Id = v.SheetId\r\n        v.Rectify_Date = v.Rectify_Date\r\n          ? timeFormat(v.Rectify_Date, '{y}-{m}-{d}')\r\n          : '-'\r\n        v.Pick_Date = v.Pick_Date\r\n          ? timeFormat(v.Pick_Date, '{y}-{m}-{d}')\r\n          : '-'\r\n        return v\r\n      })\r\n      this.total = data.TotalCount\r\n    },\r\n\r\n    multiSelectedChange(array) {\r\n      this.selectList = array.records\r\n      this.$emit('selectChange', this.selectList)\r\n    },\r\n    generateComponent(title, component) {\r\n      this.dialogTitle = title\r\n      this.currentComponent = component\r\n      this.dialogVisible = true\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n      this.fetchData(1)\r\n    },\r\n    getrectificationRecord(row) {\r\n      RectificationRecord({ sheetid: row.SheetId }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          if (res.Data.length === 0) {\r\n            this.$message({\r\n              type: 'error',\r\n              message: '暂无操作记录'\r\n            })\r\n          } else {\r\n            this.generateComponent('整改记录', 'rectificationDialog')\r\n            this.$nextTick((_) => {\r\n              this.$refs['content'].init(row)\r\n            })\r\n          }\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handelrectifiction(row) {\r\n      this.getrectificationRecord(row)\r\n    },\r\n    handleInfo(sheetId) {\r\n      this.$router.push({\r\n        name: 'PROQualityInfoSummary',\r\n        query: { pg_redirect: this.$route.name, sheetId, isCheck: true }\r\n      })\r\n    },\r\n    exportTb() {\r\n      const SheetIds = this.selectList.map((v) => v.SheetId)\r\n      this.$emit('setExportLoading', true)\r\n      const SeachParams = JSON.parse(JSON.stringify(this.searchDetail))\r\n      const SteelName = SeachParams.SteelName.trim().replaceAll(' ', '\\n')\r\n      if (SeachParams.Pick_Date && SeachParams.Pick_Date.length === 2) {\r\n        SeachParams.BeginDate = SeachParams.Pick_Date[0]\r\n        SeachParams.EndDate = SeachParams.Pick_Date[1]\r\n      } else {\r\n        SeachParams.BeginDate = null\r\n        SeachParams.EndDate = null\r\n      }\r\n      ExportInspsectionSummaryInfo({\r\n        pageInfo: this.queryInfo,\r\n        ...SeachParams,\r\n        SteelName,\r\n        Check_Style: 1,\r\n        SheetIds\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.$emit('setExportLoading', false)\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/styles/mixin.scss\";\r\n@import \"~@/styles/variables.scss\";\r\n.search_wrapper {\r\n  padding: 16px 16px 0;\r\n  box-sizing: border-box;\r\n  ::v-deep .el-form-item {\r\n    .el-form-item__content {\r\n      & > .el-input {\r\n        width: 220px;\r\n      }\r\n      & > .el-select {\r\n        width: 220px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.cs-bottom-wapper {\r\n  padding: 0 16px;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .tb-x {\r\n    flex: 1;\r\n    height: 0;\r\n  }\r\n\r\n  .pagination-container {\r\n    text-align: right;\r\n    padding: 16px;\r\n    margin: 0;\r\n  }\r\n\r\n  .data-info {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n}\r\n\r\n.by-dot {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  &:before {\r\n    content: \"\";\r\n    display: inline-block;\r\n    width: 5px;\r\n    height: 5px;\r\n    background: #f56c6c;\r\n    border-radius: 50%;\r\n    margin-right: 5px;\r\n  }\r\n}\r\n.by-dot-success {\r\n  color: #67c23a;\r\n  &:before {\r\n    background: #67c23a;\r\n  }\r\n}\r\n.by-dot-primary {\r\n  color: #409eff;\r\n  &:before {\r\n    background: #409eff;\r\n  }\r\n}\r\n.by-dot-fail {\r\n  color: #ff0000;\r\n  &:before {\r\n    background: #ff0000;\r\n  }\r\n}\r\n.by-dot-info {\r\n  &:before {\r\n    background: #909399;\r\n  }\r\n}\r\n</style>\r\n"]}]}
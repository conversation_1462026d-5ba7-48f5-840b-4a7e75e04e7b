<template>
  <div v-loading="Loading" class="h100" element-loading-text="数据生成中">
    <div class="cs-z-page-main-content">
      <el-form ref="form" :model="queryForm" inline label-width="80px">
        <el-form-item label="任务单号" prop="Task_Code">
          <el-input v-model="queryForm.Task_Code" placeholder="请输入" clearable="" />
        </el-form-item>
        <el-form-item label="排产单号" prop="Schduling_Code">
          <el-input v-model="queryForm.Schduling_Code" placeholder="请输入" clearable="" />
        </el-form-item>
        <el-form-item label="项目名称" prop="projectId">
          <el-select
            v-model="queryForm.projectId"
            filterable
            clearable
            placeholder="请选择"
            style="width: 100%"
            @change="projectChange"
          >
            <el-option
              v-for="item in projectOption"
              :key="item.Id"
              :label="item.Short_Name"
              :value="item.Id"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="!isPart || isPart && hasUnitPart" label="区域名称" prop="areaId">
          <el-tree-select
            ref="treeSelect"
            v-model="queryForm.areaId"
            :disabled="!queryForm.projectId"
            :select-params="{
              clearable: true,
            }"
            class="cs-tree-x"
            :tree-params="treeParams"
            @select-clear="areaClear"
            @node-click="areaChange"
          />
        </el-form-item>
        <el-form-item v-if="!isPart||isPart && hasUnitPart" label="批次" prop="install">
          <el-select
            v-model="queryForm.install"
            :disabled="!queryForm.areaId"
            clearable
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in installOption"
              :key="item.Id"
              :label="item.Name"
              :value="item.Id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="Task_Status">
          <el-select
            v-model="queryForm.Task_Status"
            clearable
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option label="已完成" :value="1" />
            <el-option label="未完成" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search(1)">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
      <el-divider />
      <div class="btn-wrapper">
        <el-button type="primary" :disabled="!selectArray.length" @click="handleExport">导出任务单列表</el-button>
        <template v-if="isCom">
          <el-dropdown
            trigger="click"
            placement="bottom-start"
            @command="handleCommand($event, 1)"
          >
            <el-button
              type="primary"
              :disabled="!selectArray.length"
            >导出任务单
              <i class="el-icon-arrow-down el-icon--right" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                command="name"
              >构件名称导出</el-dropdown-item>
              <el-dropdown-item
                command="code"
              >构件号合并导出</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
        <template v-if="isUnitPart">
          <el-button :disabled="!selectArray.length" type="success" @click="handleCommand('name')">导出任务单</el-button>
        </template>
        <template v-if="isPart">
          <el-button :disabled="!selectArray.length" type="success" @click="handleCommand('name')">导出任务单</el-button>
        </template>
      </div>
      <div
        v-loading="tbLoading"
        element-loading-text="加载中"
        element-loading-spinner="el-icon-loading"
        class="fff  cs-z-tb-wrapper"
      >
        <dynamic-data-table
          ref="dyTable"
          :columns="columns"
          :data="tbData"
          :config="tbConfig"
          :page="queryInfo.Page"
          :total="total"
          border
          class="cs-plm-dy-table"
          stripe
          @multiSelectedChange="getSelectVal"
          @gridPageChange="handlePageChange"
          @gridSizeChange="handlePageChange"
        >
          <template slot="Task_Code" slot-scope="{ row }">
            <el-link type="primary" @click="handleView(row)">{{ row.Task_Code }}</el-link>
          </template>
          <template slot="Task_Status" slot-scope="{ row }">
            <el-tag v-if="row.Task_Status === 0" type="danger">未完成</el-tag>
            <el-tag v-else type="success">已完成</el-tag>
          </template>
          <template slot="op" slot-scope="{ row }">
            <el-button
              type="text"
              @click="handleView(row)"
            >查看
            </el-button>
            <template v-if="isCom">
              <el-dropdown
                trigger="click"
                placement="bottom-start"
                style="margin-left: 12px;"
                @command="printSelected($event, row)"
              >
                <el-button>打印任务单
                  <i class="el-icon-arrow-down el-icon--right" />
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    command="name"
                  >构件名称打印</el-dropdown-item>
                  <el-dropdown-item
                    command="code"
                  >构件号合并打印</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
            <template v-if="isUnitPart">
              <el-button type="text" @click="printSelected('name', row)">打印任务单</el-button>
            </template>
            <template v-if="isPart">
              <el-button type="text" @click="printSelected('name', row)">打印任务单</el-button>
            </template>
          </template>
        </dynamic-data-table>
      </div>
    </div>
  </div>
</template>

<script>

import getQueryInfo from '@/views/PRO/plan-production/schedule-production/mixin'
import getTbInfo from '@/mixins/PRO/get-table-info'
import DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'
import { GetTeamTaskPageList, ExportTaskCodeDetails } from '@/api/PRO/production-task'
import addRouterPage from '@/mixins/add-router-page'
import { timeFormat } from '@/filters'
import { parseTime, combineURL, debounce } from '@/utils'
import { mapGetters } from 'vuex'

export default {
  components: {
    DynamicDataTable
  },
  mixins: [getQueryInfo, getTbInfo, addRouterPage],
  props: {
    pageType: {
      type: String,
      default: '-1'
    },
    hasUnitPart: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      addPageArray: [
        {
          path: this.$route.path + '/detail',
          hidden: true,
          component: () => import('@/views/PRO/plan-production/task-list/detail'),
          name: 'PROTaskListDetail',
          meta: { title: '任务单详情' }
        },
        {
          path: this.$route.path + '/detailPrint',
          hidden: true,
          component: () => import('@/views/PRO/plan-production/task-list/detailPrint'),
          name: 'PROTaskListDetailPrint',
          meta: { title: '打印任务单详情' }
        }
      ],
      queryForm: {
        Task_Status: undefined,
        Task_Code: undefined,
        Schduling_Code: undefined
      },
      queryInfo: {
        Page: 1,
        PageSize: 20
      },
      tbConfig: {
        Op_Width: 200,
        IS_ToolTip: true
      },
      Loading: false,
      tbLoading: false,
      columns: [],
      tbData: [],
      selectArray: [],
      total: 0,
      search: () => ({})
    }
  },
  computed: {
    isPart() {
      return this.pageType === '0'
    },
    isCom() {
      return this.pageType === '-1'
    },
    isUnitPart() {
      return +this.pageType > 0
    }
  },

  created() {
    this.tbConfig.Op_Width = this.isCom ? 200 : 140
  },

  async mounted() {
    this.search = debounce(this.fetchData, 800, true)
    await this.getTableConfig((this.isCom || this.isUnitPart) ? 'PROComTaskList' : 'PROPartTaskList')

    if (!this.hasUnitPart) {
      const arr = ['Project_Name', 'Area_Name', 'InstallUnit_Name']
      arr.forEach((item) => {
        const idx = this.columns.findIndex((v) => v.Code === item)
        if (idx !== -1) {
          this.columns.splice(idx, 1)
        }
      })
    }

    if (this.isPart) {
      const idx = this.columns.findIndex((item) => item.Code === 'Process_Finish_Date')
      idx !== -1 && this.columns.splice(idx, 1)
    }
    this.fetchData()
  },
  methods: {
    getSelectVal(v) {
      this.selectArray = v
    },
    // 导出任务单列表
    handleExport() {
      const filterVal = this.columns.map(v => v.Code)
      const data = formatJson(filterVal, this.selectArray)
      const header = this.columns.map(v => v.Display_Name)
      import('@/vendor/Export2Excel').then(excel => {
        excel.export_json_to_excel({
          header: header,
          data,
          filename: `${this.isCom ? '构件任务单' : this.isUnitPart ? '部件任务单' : '零件任务单'}`,
          autoWidth: true,
          bookType: 'xlsx'
        })
      })
      function formatJson(filterVal, jsonData) {
        return jsonData.map(v => filterVal.map(j => {
          if (j === 'Order_Date') {
            return timeFormat(v[j])
          } else if (j === 'Task_Status') {
            return v[j] === 0 ? '未完成' : '已完成'
          } else {
            return v[j]
          }
        }))
      }
    },
    fetchData(page) {
      page && (this.queryInfo.Page = page)
      this.tbLoading = true
      GetTeamTaskPageList({
        ...this.queryInfo,
        Task_Code: this.queryForm.Task_Code,
        Schduling_Code: this.queryForm.Schduling_Code,
        Project_Id: this.queryForm.projectId,
        Task_Status: this.queryForm.Task_Status,
        Area_Id: this.queryForm.areaId,
        InstallUnit_Id: this.queryForm.install,
        Bom_Level: this.pageType,
        Process_Type: this.isCom ? 2 : this.isPart ? 1 : 3 // 工序类型 1零件 2构件
      }).then(res => {
        if (res.IsSucceed) {
          this.tbData = res.Data.Data.map((v) => {
            v.Finish_Date = v.Finish_Date
              ? parseTime(new Date(v.Finish_Date), '{y}-{m}-{d}')
              : v.Finish_Date
            v.Order_Date = v.Order_Date
              ? parseTime(new Date(v.Order_Date), '{y}-{m}-{d}')
              : v.Order_Date
            v.Task_Finish_Date = v.Task_Finish_Date
              ? parseTime(new Date(v.Task_Finish_Date), '{y}-{m}-{d}')
              : v.Task_Finish_Date
            v.Process_Finish_Date = v.Process_Finish_Date
              ? parseTime(new Date(v.Process_Finish_Date), '{y}-{m}-{d}')
              : v.Process_Finish_Date
            return v
          })
          this.total = res.Data.TotalCount
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      }).finally(_ => {
        this.tbLoading = false
      })
    },
    handleReset() {
      this.$refs['form'].resetFields()
      this.search(1)
    },
    // 导出任务单详情
    handleCommand(command, type) {
      const TeamTaskModel = this.selectArray.map(v => {
        return {
          Task_Code: v.Task_Code,
          Working_Team_Id: v.Working_Team_Id
        }
      })
      const Working_Team_Id = this.selectArray.length === 1 ? this.selectArray[0].Working_Team_Id : ''
      const Task_Code = this.selectArray.length === 1 ? this.selectArray[0].Task_Code : ''
      this.Loading = true
      ExportTaskCodeDetails({
        Process_Type: this.isCom ? 2 : this.isPart ? 1 : 3, // 1零件，2构件
        Working_Team_Id,
        Task_Code,
        Is_Merge: command === 'code',
        ExportTeamTaskModel: TeamTaskModel
      }).then((res) => {
        if (res.IsSucceed) {
          this.$message({
            message: '导出成功',
            type: 'success'
          })
          window.open(combineURL(this.$baseUrl, res.Data), '_blank')
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      }).finally(_ => {
        this.Loading = false
      })
    },
    // 打印
    printSelected(command, row) {
      const params = {
        Task_Code: row.Task_Code,
        Project_Name: row.Project_Name,
        Area_Name: row.Area_Name,
        InstallUnit_Name: row.InstallUnit_Name,
        Schduling_Code: row.Schduling_Code,
        Finish_Date: row.Finish_Date,
        Order_Date: row.Order_Date,
        Task_Finish_Date: row.Task_Finish_Date,
        Process_Finish_Date: row.Process_Finish_Date,
        Working_Team_Name: row.Working_Team_Name,
        Working_Process_Name: row.Working_Process_Name,
        Working_Team_Id: row.Working_Team_Id
      }

      this.$router.push({
        name: 'PROTaskListDetailPrint',
        query: {
          type: this.pageType,
          command: command,
          pg_redirect: this.$route.name,
          other: encodeURIComponent(JSON.stringify(params))
        }
      })
    },

    handleView(row) {
      const {
        Task_Code,
        Project_Name,
        Area_Name,
        InstallUnit_Name,
        Schduling_Code,
        Finish_Date,
        Order_Date,
        Task_Finish_Date,
        Process_Finish_Date,
        Working_Team_Name,
        Working_Process_Name
      } = row
      this.$router.push({
        name: 'PROTaskListDetail',
        query: {
          id: row.Task_Code,
          type: this.pageType,
          tid: row.Working_Team_Id,
          pg_redirect: this.$route.name,
          // pg_redirect: this.isPart ? 'PROPartTaskList' : 'PROComTaskList',
          other: encodeURIComponent(JSON.stringify({
            Task_Code,
            Project_Name,
            Area_Name,
            InstallUnit_Name,
            Schduling_Code: Schduling_Code,
            Finish_Date: Finish_Date,
            Process_Finish_Date: Process_Finish_Date,
            Order_Date: Order_Date,
            Finish_Date2: Task_Finish_Date,
            Working_Team_Name: Working_Team_Name,
            Working_Process_Name: Working_Process_Name
          }))
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.el-divider{
  margin:0 0 10px;
}
.btn-wrapper{
  margin-bottom: 10px;
  display: flex;
  justify-content: flex-start;
  & > button {
    margin-right: 10px;
  }
  & > div {
    margin-right: 10px;
  }
}
.cs-z-page-main-content{
  box-shadow: unset;
}
</style>

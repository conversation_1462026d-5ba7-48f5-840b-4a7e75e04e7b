{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\unitPartRecognitionConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\unitPartRecognitionConfig.vue", "mtime": 1757474258893}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["unitPartRecognitionConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "unitPartRecognitionConfig.vue", "sourceRoot": "src/views/PRO/project-config/process-settings/component", "sourcesContent": ["<template>\n  <div class=\"form-wrapper\">\n    <div class=\"form-x\">\n      <el-form ref=\"form\" :model=\"form\" label-width=\"120px\">\n        <el-form-item label=\"是否启用\" prop=\"enable\">\n          <el-radio-group v-model=\"form.enable\">\n            <el-radio :label=\"false\">否</el-radio>\n            <el-radio :label=\"true\">是</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <template v-if=\"form.enable\">\n          <el-form-item label=\"识别类型\" prop=\"identifyAttr\">\n            <el-radio-group v-model=\"form.identifyAttr\">\n              <el-radio :label=\"1\">{{ currentBomName }}名称前缀</el-radio>\n              <el-radio :label=\"2\">{{ currentBomName }}规格前缀</el-radio>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item v-for=\"(element,index) in list\" :key=\"index\" :show-message=\"false\" :label=\"element.Part_Type_Name\" prop=\"mainPart\">\n            <el-input v-model.trim=\"form['item'+index]\" :placeholder=\"`请输入（多个使用'${splitSymbol}'隔开），单个配置不超过10个字符`\" clearable @blur=\"mainBlur\" />\n          </el-form-item>\n        </template>\n      </el-form>\n    </div>\n    <div class=\"btn-x\">\n      <el-button @click=\"$emit('close')\">取 消</el-button>\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\n\nimport { uniqueArr } from '@/utils'\nimport {\n  GetFactoryPartTypeIndentifySetting,\n  SavePartTypeIdentifySetting\n} from '@/api/PRO/partType'\n\nconst SPLITVALUE = '|'\n\nexport default {\n  props: {\n    level: {\n      type: Number,\n      default: 0\n    },\n    bomList: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      form: {\n        enable: false,\n        identifyAttr: 1 // 默认为零件名称前缀\n      },\n      list: [],\n      splitSymbol: SPLITVALUE,\n      btnLoading: false\n    }\n  },\n  computed: {\n    currentBomName() {\n      return this.bomList.find(item => +item.Code === this.level)?.Display_Name\n    }\n  },\n  watch: {\n    level: {\n      handler(newVal) {\n        if (newVal) {\n          this.getTypeList()\n        }\n      },\n      immediate: true\n    }\n  },\n  mounted() {\n\n  },\n  methods: {\n    async getTypeList() {\n      GetFactoryPartTypeIndentifySetting({\n        Part_Grade: this.level\n      }).then(res => {\n        if (res.IsSucceed) {\n          const { Is_Enabled, Setting_List } = res.Data\n          this.form.enable = Is_Enabled\n          this.list = Setting_List.map((v, index) => {\n            this.$set(this.form, 'item' + index, v.Prefixs || '')\n            return v\n          })\n\n          // 获取Setting_List中的Identify_Attr，如果有效（值为1或2）则使用，否则默认为1\n          if (Setting_List.length > 0) {\n            const identifyAttr = Setting_List[0].Identify_Attr\n            this.form.identifyAttr = (identifyAttr === 1 || identifyAttr === 2) ? identifyAttr : 1\n          }\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    handleSubmit() {\n      if (this.form.enable) {\n        const arr = []\n        console.log('this.form', this.form)\n        for (let i = 0; i < this.list.length; i++) {\n          const regex = /^(?!.*\\|\\|)(?!.*\\|$)(?!^\\|)[^|]{1,10}(?:\\|[^|]{1,10})*$/\n          if (!regex.test(this.form[`item${i}`])) {\n            this.$message({\n              message: `${this.list[i].Part_Type_Name}配置不符合要求`,\n              type: 'warning'\n            })\n            return\n          }\n\n          const item = this.form[`item${i}`].split(this.splitSymbol).filter(v => !!v)\n\n          if (item.length === 0) {\n            this.$message({\n              message: `${this.list[i].Part_Type_Name}不能为空`,\n              type: 'warning'\n            })\n            return\n          }\n\n          for (let j = 0; j < item.length; j++) {\n            const d = item[j]\n            if (d.length > 10) {\n              this.$message({\n                message: `${this.list[i].Part_Type_Name}单个配置，不能超过10个字符`,\n                type: 'warning'\n              })\n              return\n            }\n          }\n\n          arr.push(...item)\n        }\n        const uniArr = uniqueArr(arr)\n        if (uniArr.length !== arr.length) {\n          this.$message({\n            message: '配置不能相同',\n            type: 'warning'\n          })\n          return\n        }\n      }\n      this.btnLoading = true\n      SavePartTypeIdentifySetting({\n        Is_Enabled: this.form.enable,\n        Part_Grade: this.level,\n        Setting_List: this.list.map((v, i) => {\n          return {\n            ...v,\n            Prefixs: this.form[`item${i}`],\n            Identify_Attr: this.form.identifyAttr\n          }\n        })\n      }).then(res => {\n        if (res.IsSucceed) {\n          this.$message({\n            message: '操作成功',\n            type: 'success'\n          })\n          this.$emit('close')\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      }).finally(() => {\n        this.btnLoading = false\n      })\n    },\n    mainBlur(e) {\n\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"~@/styles/mixin.scss\";\n.form-wrapper {\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  max-height: 70vh;\n  .form-x{\n    overflow: auto;\n    padding-right: 16px;\n    @include scrollBar;\n  }\n  .btn-x {\n    padding-top: 16px;\n    text-align: right;\n  }\n\n}\n</style>\n"]}]}
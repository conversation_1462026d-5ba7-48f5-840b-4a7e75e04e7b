{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\ship\\component\\Import.vue?vue&type=style&index=0&id=2b37895c&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\ship\\component\\Import.vue", "mtime": 1758677034219}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmJveHsNCiAgYm9yZGVyOiAxcHggZGFzaGVkICNEOURCRTI7DQogIHBhZGRpbmc6IDAgMTZweDsNCiAgZGlzcGxheTogZmxleDsNCiAgaGVpZ2h0OiA2NHB4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGJhY2tncm91bmQtY29sb3I6ICNGN0Y4Rjk7DQogICYgfiAuYm94ew0KICAgIG1hcmdpbi10b3A6IDIwcHg7DQogIH0NCg0KfQ0KLnVwbG9hZC1ib3h7DQogIGJhY2tncm91bmQtY29sb3I6ICAjRjdGOEY5Ow0KICBib3JkZXI6IDFweCBkYXNoZWQgI0Q5REJFMjsNCiAgbWFyZ2luLXRvcDogMTZweDsNCiAgcGFkZGluZzogMTZweDsNCn0NCi5jcy1mb290ZXJ7DQogIG1hcmdpbi10b3A6IDEwcHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg=="}, {"version": 3, "sources": ["Import.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6HA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Import.vue", "sourceRoot": "src/views/PRO/basic-information/ship/component", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"box\">\r\n      <span>1.下载模板</span>\r\n      <el-button size=\"large\" @click=\"handleDownload\">\r\n        <svg-icon icon-class=\"document_form_icon\" />\r\n        船舶模板\r\n      </el-button>\r\n    </div>\r\n\r\n    <div class=\"upload-box\">\r\n      <span style=\"margin-bottom: 20px;display: inline-block\">\r\n        2. 完善内容，重新上传。\r\n      </span>\r\n      <upload-excel ref=\"upload\" :before-upload=\"beforeUpload\" :limit=\"2\" :on-change=\"handleChange\" :file-list = \"fileList\"/>\r\n    </div>\r\n\r\n    <!--    <div class=\"box\">\r\n      <span style=\"display: inline-block;\">3. 选择导入工厂</span>\r\n      <el-select v-model=\"ProjectId\" filterable clearable style=\"width: 70%\" placeholder=\"请选择\">\r\n        <el-option\r\n          v-for=\"item in proOption\"\r\n          :key=\"item.Id\"\r\n          :label=\"item.Name\"\r\n          :value=\"item.Id\"\r\n        />\r\n      </el-select>\r\n    </div>-->\r\n\r\n    <footer class=\"cs-footer\">\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\r\n    </footer>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UploadExcel from '@/components/UploadExcel'\r\nimport { ImportBoat, BoatDataTemplate } from '@/api/PRO/car'\r\nimport { combineURL } from '@/utils'\r\nimport { GetFactoryList } from '@/api/PRO/pro-schedules'\r\n\r\nexport default {\r\n  name: 'Import',\r\n  components: {\r\n    UploadExcel\r\n  },\r\n  data() {\r\n    return {\r\n      options: [],\r\n      btnLoading: false,\r\n      // ProjectId: '',\r\n      proOption: [],\r\n      fileList:[]\r\n    }\r\n  },\r\n  mounted() {\r\n\r\n  },\r\n  methods: {\r\n    getFactory() {\r\n      GetFactoryList({}).then(res => {\r\n        this.proOption = res?.Data\r\n      })\r\n    },\r\n    handleDownload() {\r\n      BoatDataTemplate({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    beforeUpload(file) {\r\n      console.log(file)\r\n      const fileFormData = new FormData()\r\n      // fileFormData.append('factoryId', this.ProjectId)\r\n      fileFormData.append('files', file)\r\n      this.btnLoading = true\r\n      ImportBoat(fileFormData).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.btnLoading = false\r\n          this.$message({\r\n            message: '导入成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('refresh')\r\n          this.$emit('close')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.btnLoading = false\r\n        }\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      /*      if (!this.ProjectId) {\r\n        this.$message({\r\n          message: '请选择工厂',\r\n          type: 'info'\r\n        })\r\n        return\r\n      }*/\r\n      console.log(this.fileList)\r\n      this.$refs.upload.handleSubmit()\r\n    },\r\n    handleChange(file, fileList) {\r\n      console.log(file,fileList)\r\n      // this.fileList = fileList.slice(-1);\r\n      this.fileList = fileList;\r\n      console.log(this.fileList)\r\n      if(fileList.length>1) {\r\n        this.fileList.shift()\r\n      }\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.box{\r\n  border: 1px dashed #D9DBE2;\r\n  padding: 0 16px;\r\n  display: flex;\r\n  height: 64px;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background-color: #F7F8F9;\r\n  & ~ .box{\r\n    margin-top: 20px;\r\n  }\r\n\r\n}\r\n.upload-box{\r\n  background-color:  #F7F8F9;\r\n  border: 1px dashed #D9DBE2;\r\n  margin-top: 16px;\r\n  padding: 16px;\r\n}\r\n.cs-footer{\r\n  margin-top: 10px;\r\n  text-align: center;\r\n}\r\n</style>\r\n"]}]}
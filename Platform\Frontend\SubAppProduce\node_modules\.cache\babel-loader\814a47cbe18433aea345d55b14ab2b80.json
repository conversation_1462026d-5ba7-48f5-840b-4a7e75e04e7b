{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\detailPrint.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\detailPrint.vue", "mtime": 1758595482001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getTbInfo", "GetTeamTaskDetails", "GetSuggestDeviceAndRemark", "closeTagView", "QrcodeVue", "mapGetters", "getBomCode", "getBomName", "checkIsUnitPart", "printStyle", "name", "components", "mixins", "data", "_this", "bom<PERSON>ame", "printData", "tbConfig", "<PERSON>_<PERSON><PERSON>th", "tbLoading", "tbData", "columns", "pageType", "command", "printColumns", "info", "Task_Code", "Project_Name", "Area_Name", "InstallUnit_Name", "Schduling_Code", "Task_Finish_Date", "Finish_Date2", "Order_Date", "Working_Team_Name", "Working_Process_Name", "Process_Start_Date", "Process_Finish_Date", "printConfig", "sheetName", "style", "beforePrintMethod", "_ref", "content", "topHtml", "Tenant_Code", "localStorage", "getItem", "Remark", "eqptInfoList", "eqptInfoListStr", "computed", "_objectSpread", "isCom", "isUnitPart", "isPart", "mounted", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "$route", "query", "type", "sent", "JSON", "parse", "decodeURIComponent", "other", "getSuggestDeviceAndRemark", "getTableConfig", "filter", "item", "Code", "column", "fetchData", "Working_Team_Id", "mergeSimilarItems", "getHtml", "printEvent", "stop", "methods", "_this3", "Bom_Level", "then", "res", "IsSucceed", "_res$Data", "_res$Data2", "Data", "map", "DisplayName", "join", "$message", "message", "Message", "idx", "_this4", "_callee2", "_callee2$", "_context2", "Page", "PageSize", "Process_Type", "Next_Team_Id", "Next_Process_Id", "Allocation_Count", "abrupt", "mergedMap", "Map", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "value", "Steel_Code", "newItem", "Allocation_Weight", "Finish_Weight", "Finish_Count", "set", "concat", "size", "key", "Spec", "Weight", "Main_Part", "AttachedBoardsNumber", "Length", "Texture", "Next_Process_Name", "Next_Working_Team_Names", "has", "existingItem", "get", "err", "e", "f", "Array", "from", "values", "handleReset", "$refs", "resetFields", "_this5", "qr", "Promise", "resolve", "reject", "$nextTick", "_", "_this5$info", "canvas", "dataURL", "toDataURL", "_this6", "xTable", "print", "mode", "v", "field", "_ref2", "result", "handleView", "row", "toBack", "$store", "cellClickEvent", "_ref3", "rowIndex", "columnIndex", "cellClassName", "_ref4"], "sources": ["src/views/PRO/plan-production/task-list/detailPrint.vue"], "sourcesContent": ["<template>\n  <div v-loading=\"tbLoading\" element-loading-text=\"打印数据生成中\" class=\"abs100 cs-z-flex-pd16-wrap\">\n    <div class=\"top-btn\" @click=\"toBack\">\n      <el-button>返回</el-button>\n    </div>\n    <div class=\"top-btn-print\" @click=\"printEvent\">\n      <el-button type=\"primary\">打印</el-button>\n    </div>\n    <div class=\"cs-z-page-main-content\">\n      <!-- <div v-for=\"(item, index) in printData\" :key=\"index\" style=\"height: 100%; display: flex; flex-direction: column;\"> -->\n      <el-form ref=\"form\" inline label-width=\"140px\">\n        <el-row>\n          <el-col :span=\"20\">\n            <el-form-item label=\"项目名称/区域：\">\n              {{ info.Project_Name }}/{{ info.Area_Name }}\n            </el-form-item>\n            <el-form-item label=\"排产单号：\">\n              {{ info.Schduling_Code }}\n            </el-form-item>\n            <el-form-item label=\"加工班组：\">\n              {{ info.Working_Team_Name }}\n            </el-form-item>\n            <el-form-item label=\"任务下达时间：\">\n              {{ info.Order_Date }}\n            </el-form-item>\n            <el-form-item label=\"任务单号：\">\n              {{ info.Task_Code }}\n            </el-form-item>\n            <el-form-item label=\"工序计划开始时间\" prop=\"Process_Start_Date\">\n              {{ info.Process_Start_Date || '-' }}\n            </el-form-item>\n            <el-form-item label=\"工序计划完成时间\" prop=\"Process_Finish_Date\">\n              {{ info.Process_Finish_Date || '-' }}\n            </el-form-item>\n            <el-form-item label=\"备注\" prop=\"Remark\">\n              {{ Remark || '-' }}\n            </el-form-item>\n            <el-form-item label=\"建议设备\" prop=\"eqptInfoListStr\">\n              {{ eqptInfoListStr || '-' }}\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n            <qrcode-vue ref=\"qrcodeRef\" :size=\"79\" :value=\"`T=${info.Task_Code}&C=${Tenant_Code}`\" class-name=\"qrcode\" level=\"H\" />\n          </el-col>\n        </el-row>\n      </el-form>\n      <div class=\"tb-x\">\n        <vxe-table\n          ref=\"xTable\"\n          :empty-render=\"{name: 'NotData'}\"\n          show-header-overflow\n          :print-config=\"printConfig\"\n          :row-config=\"{ isCurrent: true, isHover: true }\"\n          class=\"cs-vxe-table\"\n          align=\"left\"\n          height=\"auto\"\n          show-overflow\n          :loading=\"tbLoading\"\n          stripe\n          size=\"medium\"\n          :data=\"tbData\"\n          resizable\n          :tooltip-config=\"{ enterable: true }\"\n          :cell-class-name=\"cellClassName\"\n          @cell-click=\"cellClickEvent\"\n        >\n          <template v-for=\"column in columns\">\n            <vxe-column\n              :key=\"column.Id\"\n              :fixed=\"column.Is_Frozen ? column.Frozen_Dirction : ''\"\n              show-overflow=\"tooltip\"\n              sortable\n              :align=\"column.Align\"\n              :field=\"column.Code\"\n              :title=\"column.Display_Name\"\n              :min-width=\"column.Width\"\n            />\n          </template>\n        </vxe-table>\n      </div>\n      <!-- </div> -->\n    </div>\n  </div>\n</template>\n\n<script>\nimport getTbInfo from '@/mixins/PRO/get-table-info'\nimport { GetTeamTaskDetails, GetSuggestDeviceAndRemark } from '@/api/PRO/production-task'\nimport { closeTagView } from '@/utils'\nimport QrcodeVue from 'qrcode.vue'\nimport { mapGetters } from 'vuex'\nimport { getBomCode, getBomName, checkIsUnitPart } from '@/views/PRO/bom-setting/utils'\n\nconst printStyle = `\n        .title {\n          text-align: center;\n        }\n        .is--print{\n          box-sizing: border-box;\n          width:95% !important;\n          margin:0 auto !important;\n        }\n        .my-list-row {\n          display: inline-block;\n          width: 100%;\n          margin-left:3%;\n        }\n        .my-list-row-first {\n          margin-bottom: 10px;\n        }\n        .my-list-row-second {\n          margin-bottom: 10px;\n        }\n        .my-list-row-third {\n          margin-bottom: 10px;\n        }\n        .my-list-col {\n          width:30%;\n          display: inline-block;\n          float: left;\n          margin-right: 1%;\n          word-wrap:break-word;\n          word-break:normal;\n        }\n        .left{\n          flex:1;\n        }\n        .my-top {\n          display:flex;\n          font-size: 12px;\n          margin-bottom: 5px;\n        }\n        .qrcode{\n          margin-right:10px\n        }\n        .cs-img{\n          position:relative;\n          right:30px\n        }\n        `\n\nexport default {\n  name: 'PROTaskListDetailPrint',\n  components: {\n    QrcodeVue\n  },\n  mixins: [getTbInfo],\n  data() {\n    return {\n      bomName: '',\n      printData: [],\n      tbConfig: {\n        Op_Width: 120\n      },\n      tbLoading: false,\n      tbData: [],\n      columns: [],\n      pageType: '',\n      command: '',\n      printColumns: [],\n      info: {\n        Task_Code: '',\n        Project_Name: '',\n        Area_Name: '',\n        InstallUnit_Name: '',\n        Schduling_Code: '',\n        Task_Finish_Date: '',\n        Finish_Date2: '',\n        Order_Date: '',\n        Working_Team_Name: '',\n        Working_Process_Name: '',\n        Process_Start_Date: '',\n        Process_Finish_Date: ''\n      },\n      printConfig: {\n        sheetName: '任务单详情',\n        style: printStyle,\n        beforePrintMethod: ({ content }) => {\n          return this.topHtml + content\n        }\n      },\n      Tenant_Code: localStorage.getItem('tenant'),\n      Remark: '',\n      eqptInfoList: [],\n      eqptInfoListStr: ''\n    }\n  },\n  computed: {\n    ...mapGetters('tenant', ['isVersionFour']),\n    isCom() {\n      return this.pageType === getBomCode('-1')\n    },\n    isUnitPart() {\n      return checkIsUnitPart(this.pageType)\n    },\n    isPart() {\n      return this.pageType === getBomCode('0')\n    }\n  },\n  async mounted() {\n    this.pageType = this.$route.query.type\n    this.command = this.$route.query.command\n    this.bomName = await getBomName(this.pageType)\n    this.info = JSON.parse(decodeURIComponent(this.$route.query.other))\n    await this.getSuggestDeviceAndRemark()\n    await this.getTableConfig(this.isCom ? 'PROComTaskListDetail' : this.isUnitPart ? 'PROUnitPartTaskListDetail' : 'PROPartTaskListDetail')\n    if (this.isCom) {\n      this.columns = this.columns.filter(item => item.Code !== 'Part_Code')\n      this.printColumns = this.columns.filter(item => item.Code !== 'Part_Code' && item.Code !== 'Comp_Description')\n      if (this.command === 'code') {\n        this.columns = this.columns.filter(item => item.Code !== 'Comp_Code' && item.Code !== 'Part_Code')\n        this.printColumns = this.columns.filter(item => item.Code !== 'Comp_Code' && item.Code !== 'Comp_Description' && item.Code !== 'Part_Code')\n      }\n    }\n\n    if (this.isUnitPart) {\n      this.printColumns = this.columns.filter(column => column.Code !== 'Project_Name' && column.Code !== 'Area_Name' && column.Code !== 'Finish_Count' && column.Code !== 'Finish_Weight' && column.Code !== 'Comp_Description')\n    }\n    if (this.isPart) {\n      this.printColumns = this.columns.filter(column => column.Code !== 'Project_Name' && column.Code !== 'Area_Name' && column.Code !== 'Finish_Count' && column.Code !== 'Finish_Weight' && column.Code !== 'Comp_Description')\n    }\n\n    const data = await this.fetchData(this.info.Task_Code, this.info.Working_Team_Id)\n    if (this.isCom && this.command === 'code') {\n      this.tbData = this.mergeSimilarItems(data)\n    } else {\n      this.tbData = data\n    }\n\n    this.getHtml()\n    this.printEvent()\n  },\n  methods: {\n    getSuggestDeviceAndRemark() {\n      GetSuggestDeviceAndRemark({\n        Bom_Level: this.pageType,\n        Task_Code: this.info.Task_Code\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.Remark = res.Data?.Remark\n          this.eqptInfoList = res.Data?.eqptInfoList || []\n          this.eqptInfoListStr = this.eqptInfoList.map(item => item.DisplayName).join(',')\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    async fetchData(Task_Code, Working_Team_Id, idx) {\n      this.tbLoading = true\n      const res = await GetTeamTaskDetails({\n        Page: -1,\n        PageSize: -1,\n        Bom_Level: this.pageType,\n        Process_Type: this.isCom ? 2 : this.isPart ? 1 : 3, // 1零件，2构件\n        Working_Team_Id: Working_Team_Id,\n        Task_Code: Task_Code,\n        Next_Team_Id: '',\n        Next_Process_Id: ''\n      })\n      if (res.IsSucceed) {\n        const data = res.Data.Data.filter(item => {\n          return item.Allocation_Count !== 0\n        })\n        this.tbLoading = false\n        return data\n      } else {\n        this.tbLoading = false\n      }\n    },\n\n    mergeSimilarItems(data) {\n      // 创建一个 Map 来存储合并后的数据，使用关键字段的组合作为键\n      const mergedMap = new Map()\n\n      // 遍历原始数据\n      for (const item of data) {\n        // 如果 Steel_Code 为空或 null，直接加入结果，不参与合并\n        if (item.Steel_Code == null || item.Steel_Code === '') {\n          // 确保数值字段有默认值\n          const newItem = {\n            ...item,\n            Allocation_Weight: item.Allocation_Weight || 0,\n            Finish_Weight: item.Finish_Weight || 0,\n            Allocation_Count: item.Allocation_Count || 0,\n            Finish_Count: item.Finish_Count || 0\n          }\n          // 使用一个唯一键（如索引）来避免覆盖\n          mergedMap.set(`null_${mergedMap.size}`, newItem)\n          continue\n        }\n\n        // 创建关键字段的组合作为键\n        const key = [\n          item.Steel_Code,\n          item.Spec,\n          item.Weight,\n          item.Main_Part,\n          item.AttachedBoardsNumber,\n          item.Length,\n          item.Texture,\n          item.Next_Process_Name,\n          item.Next_Working_Team_Names,\n          item.InstallUnit_Name\n        ].join('|')\n\n        // 检查是否已经有相同的记录\n        if (mergedMap.has(key)) {\n          // 获取已存在的记录\n          const existingItem = mergedMap.get(key)\n\n          // 累加数值字段\n          existingItem.Allocation_Weight += item.Allocation_Weight || 0\n          existingItem.Finish_Weight += item.Finish_Weight || 0\n          existingItem.Allocation_Count += item.Allocation_Count || 0\n          existingItem.Finish_Count += item.Finish_Count || 0\n        } else {\n          const newItem = {\n            ...item,\n            Allocation_Weight: item.Allocation_Weight || 0,\n            Finish_Weight: item.Finish_Weight || 0,\n            Allocation_Count: item.Allocation_Count || 0,\n            Finish_Count: item.Finish_Count || 0\n          }\n\n          // 如果是第一次出现，直接放入 Map\n          mergedMap.set(key, newItem)\n        }\n      }\n\n      // 将 Map 转换回数组\n      return Array.from(mergedMap.values())\n    },\n\n    handleReset() {\n      this.$refs['form'].resetFields()\n    },\n\n    getHtml() {\n      const qr = this.$refs['qrcodeRef']\n      return new Promise((resolve, reject) => {\n        this.$nextTick(_ => {\n          const canvas = qr.$refs['qrcode-vue']\n          const dataURL = canvas.toDataURL('image/png')\n          this.topHtml = `\n        <h1 class=\"title\">#${this.info.Working_Process_Name || ''}# 加工任务单</h1>\n        <div class=\"my-top\">\n          <div class=\"left\">\n            <div class=\"my-list-row my-list-row-first\">\n              <div class=\"my-list-col\">项目名称/区域：${this.info.Project_Name || ''}/${this.info.Area_Name || ''}</div>\n              <div class=\"my-list-col\">排产单号：${this.info.Schduling_Code || ''}</div>\n              <div class=\"my-list-col\">加工班组：${this.info.Working_Team_Name || ''}</div>\n            </div>\n            <div class=\"my-list-row my-list-row-second\">\n              <div class=\"my-list-col\">任务下单时间：${this.info.Order_Date || ''}</div>\n              <div class=\"my-list-col\">任务单号：${this.info?.Task_Code || ''}</div>\n              <div class=\"my-list-col\">工序计划开始时间：${this.info.Process_Start_Date || ''}</div>\n            </div>\n            <div class=\"my-list-row my-list-row-third\">\n              <div class=\"my-list-col\">工序计划完成时间：${this.info.Process_Finish_Date || ''}</div>\n              <div class=\"my-list-col\">备注：${this.Remark || ''}</div>\n            </div>\n            <div class=\"my-list-row\">\n              <div class=\"my-list-col\">建议设备：${this.eqptInfoListStr || ''}</div>\n            </div>\n          </div>\n          <div class=\"right\">\n           <img class=\"cs-img\" src=\"${dataURL}\" alt=\"\">\n          </div>\n        </div>\n        `\n          resolve()\n        })\n      })\n    },\n    printEvent() {\n      this.getHtml().then((_) => {\n        this.$refs.xTable.print({\n          sheetName: this.printConfig.sheetName,\n          style: printStyle,\n          mode: 'current',\n          columns: this.printColumns.map((v) => {\n            return {\n              field: v.Code\n            }\n          }),\n          beforePrintMethod: ({ content }) => {\n            const result = this.topHtml + content\n            return result\n          }\n        })\n      })\n    },\n    handleView(row) {\n    },\n    toBack() {\n      closeTagView(this.$store, this.$route)\n    },\n\n    // 单元格点击时间\n    cellClickEvent({ row, rowIndex, column, columnIndex }) {\n      // if (column.property === \"Finish_Count\" && row.Finish_Count > 0) {\n      //   this.$nextTick(() => {\n      //     this.$refs.TransferDetail.init(row, this.isCom);\n      //   });\n      // }\n    },\n\n    // 改变单元格样式\n    cellClassName({ row, rowIndex, column, columnIndex }) {\n      // if (column.property === \"Finish_Count\") {\n      //   return \"col-blue\";\n      // }\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.el-divider {\n  margin: 0 0 10px;\n}\n\n.tb-x {\n  flex: 1;\n  overflow:auto;\n\n  ::v-deep {\n    .cs-vxe-table .vxe-body--column.col-blue {\n      color: #298dff;\n      cursor: pointer;\n    }\n  }\n}\n\n.cs-z-flex-pd16-wrap {\n  padding-top: 50px;\n  .top-btn {\n    position: absolute;\n    top: 12px;\n    left: 20px;\n    z-index: 99;\n\n    .el-button {\n      background-color: #f7f8f9;\n    }\n  }\n  .top-btn-print {\n    position: absolute;\n    top: 12px;\n    right: 20px;\n    z-index: 99;\n  }\n\n  .cs-z-page-main-content {\n    overflow-y: auto;\n    ::v-deep {\n      .el-form-item__content {\n        min-width: 200px;\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsFA,OAAAA,SAAA;AACA,SAAAC,kBAAA,EAAAC,yBAAA;AACA,SAAAC,YAAA;AACA,OAAAC,SAAA;AACA,SAAAC,UAAA;AACA,SAAAC,UAAA,EAAAC,UAAA,EAAAC,eAAA;AAEA,IAAAC,UAAA,kiCA8CA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAP,SAAA,EAAAA;EACA;EACAQ,MAAA,GAAAZ,SAAA;EACAa,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;MACAC,QAAA;QACAC,QAAA;MACA;MACAC,SAAA;MACAC,MAAA;MACAC,OAAA;MACAC,QAAA;MACAC,OAAA;MACAC,YAAA;MACAC,IAAA;QACAC,SAAA;QACAC,YAAA;QACAC,SAAA;QACAC,gBAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,YAAA;QACAC,UAAA;QACAC,iBAAA;QACAC,oBAAA;QACAC,kBAAA;QACAC,mBAAA;MACA;MACAC,WAAA;QACAC,SAAA;QACAC,KAAA,EAAA/B,UAAA;QACAgC,iBAAA,WAAAA,kBAAAC,IAAA;UAAA,IAAAC,OAAA,GAAAD,IAAA,CAAAC,OAAA;UACA,OAAA7B,KAAA,CAAA8B,OAAA,GAAAD,OAAA;QACA;MACA;MACAE,WAAA,EAAAC,YAAA,CAAAC,OAAA;MACAC,MAAA;MACAC,YAAA;MACAC,eAAA;IACA;EACA;EACAC,QAAA,EAAAC,aAAA,CAAAA,aAAA,KACA/C,UAAA;IACAgD,KAAA,WAAAA,MAAA;MACA,YAAA/B,QAAA,KAAAhB,UAAA;IACA;IACAgD,UAAA,WAAAA,WAAA;MACA,OAAA9C,eAAA,MAAAc,QAAA;IACA;IACAiC,MAAA,WAAAA,OAAA;MACA,YAAAjC,QAAA,KAAAhB,UAAA;IACA;EAAA,EACA;EACAkD,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAAhD,IAAA;MAAA,OAAA8C,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACAT,MAAA,CAAAnC,QAAA,GAAAmC,MAAA,CAAAU,MAAA,CAAAC,KAAA,CAAAC,IAAA;YACAZ,MAAA,CAAAlC,OAAA,GAAAkC,MAAA,CAAAU,MAAA,CAAAC,KAAA,CAAA7C,OAAA;YAAAyC,QAAA,CAAAE,IAAA;YAAA,OACA3D,UAAA,CAAAkD,MAAA,CAAAnC,QAAA;UAAA;YAAAmC,MAAA,CAAA1C,OAAA,GAAAiD,QAAA,CAAAM,IAAA;YACAb,MAAA,CAAAhC,IAAA,GAAA8C,IAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAAhB,MAAA,CAAAU,MAAA,CAAAC,KAAA,CAAAM,KAAA;YAAAV,QAAA,CAAAE,IAAA;YAAA,OACAT,MAAA,CAAAkB,yBAAA;UAAA;YAAAX,QAAA,CAAAE,IAAA;YAAA,OACAT,MAAA,CAAAmB,cAAA,CAAAnB,MAAA,CAAAJ,KAAA,4BAAAI,MAAA,CAAAH,UAAA;UAAA;YACA,IAAAG,MAAA,CAAAJ,KAAA;cACAI,MAAA,CAAApC,OAAA,GAAAoC,MAAA,CAAApC,OAAA,CAAAwD,MAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAAC,IAAA;cAAA;cACAtB,MAAA,CAAAjC,YAAA,GAAAiC,MAAA,CAAApC,OAAA,CAAAwD,MAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAAC,IAAA,oBAAAD,IAAA,CAAAC,IAAA;cAAA;cACA,IAAAtB,MAAA,CAAAlC,OAAA;gBACAkC,MAAA,CAAApC,OAAA,GAAAoC,MAAA,CAAApC,OAAA,CAAAwD,MAAA,WAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAAC,IAAA,oBAAAD,IAAA,CAAAC,IAAA;gBAAA;gBACAtB,MAAA,CAAAjC,YAAA,GAAAiC,MAAA,CAAApC,OAAA,CAAAwD,MAAA,WAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAAC,IAAA,oBAAAD,IAAA,CAAAC,IAAA,2BAAAD,IAAA,CAAAC,IAAA;gBAAA;cACA;YACA;YAEA,IAAAtB,MAAA,CAAAH,UAAA;cACAG,MAAA,CAAAjC,YAAA,GAAAiC,MAAA,CAAApC,OAAA,CAAAwD,MAAA,WAAAG,MAAA;gBAAA,OAAAA,MAAA,CAAAD,IAAA,uBAAAC,MAAA,CAAAD,IAAA,oBAAAC,MAAA,CAAAD,IAAA,uBAAAC,MAAA,CAAAD,IAAA,wBAAAC,MAAA,CAAAD,IAAA;cAAA;YACA;YACA,IAAAtB,MAAA,CAAAF,MAAA;cACAE,MAAA,CAAAjC,YAAA,GAAAiC,MAAA,CAAApC,OAAA,CAAAwD,MAAA,WAAAG,MAAA;gBAAA,OAAAA,MAAA,CAAAD,IAAA,uBAAAC,MAAA,CAAAD,IAAA,oBAAAC,MAAA,CAAAD,IAAA,uBAAAC,MAAA,CAAAD,IAAA,wBAAAC,MAAA,CAAAD,IAAA;cAAA;YACA;YAAAf,QAAA,CAAAE,IAAA;YAAA,OAEAT,MAAA,CAAAwB,SAAA,CAAAxB,MAAA,CAAAhC,IAAA,CAAAC,SAAA,EAAA+B,MAAA,CAAAhC,IAAA,CAAAyD,eAAA;UAAA;YAAArE,IAAA,GAAAmD,QAAA,CAAAM,IAAA;YACA,IAAAb,MAAA,CAAAJ,KAAA,IAAAI,MAAA,CAAAlC,OAAA;cACAkC,MAAA,CAAArC,MAAA,GAAAqC,MAAA,CAAA0B,iBAAA,CAAAtE,IAAA;YACA;cACA4C,MAAA,CAAArC,MAAA,GAAAP,IAAA;YACA;YAEA4C,MAAA,CAAA2B,OAAA;YACA3B,MAAA,CAAA4B,UAAA;UAAA;UAAA;YAAA,OAAArB,QAAA,CAAAsB,IAAA;QAAA;MAAA,GAAAzB,OAAA;IAAA;EACA;EACA0B,OAAA;IACAZ,yBAAA,WAAAA,0BAAA;MAAA,IAAAa,MAAA;MACAtF,yBAAA;QACAuF,SAAA,OAAAnE,QAAA;QACAI,SAAA,OAAAD,IAAA,CAAAC;MACA,GAAAgE,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UAAA,IAAAC,SAAA,EAAAC,UAAA;UACAN,MAAA,CAAAxC,MAAA,IAAA6C,SAAA,GAAAF,GAAA,CAAAI,IAAA,cAAAF,SAAA,uBAAAA,SAAA,CAAA7C,MAAA;UACAwC,MAAA,CAAAvC,YAAA,KAAA6C,UAAA,GAAAH,GAAA,CAAAI,IAAA,cAAAD,UAAA,uBAAAA,UAAA,CAAA7C,YAAA;UACAuC,MAAA,CAAAtC,eAAA,GAAAsC,MAAA,CAAAvC,YAAA,CAAA+C,GAAA,WAAAlB,IAAA;YAAA,OAAAA,IAAA,CAAAmB,WAAA;UAAA,GAAAC,IAAA;QACA;UACAV,MAAA,CAAAW,QAAA;YACAC,OAAA,EAAAT,GAAA,CAAAU,OAAA;YACAhC,IAAA;UACA;QACA;MACA;IACA;IACAY,SAAA,WAAAA,UAAAvD,SAAA,EAAAwD,eAAA,EAAAoB,GAAA;MAAA,IAAAC,MAAA;MAAA,OAAA7C,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA4C,SAAA;QAAA,IAAAb,GAAA,EAAA9E,IAAA;QAAA,OAAA8C,mBAAA,GAAAG,IAAA,UAAA2C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzC,IAAA,GAAAyC,SAAA,CAAAxC,IAAA;YAAA;cACAqC,MAAA,CAAApF,SAAA;cAAAuF,SAAA,CAAAxC,IAAA;cAAA,OACAjE,kBAAA;gBACA0G,IAAA;gBACAC,QAAA;gBACAnB,SAAA,EAAAc,MAAA,CAAAjF,QAAA;gBACAuF,YAAA,EAAAN,MAAA,CAAAlD,KAAA,OAAAkD,MAAA,CAAAhD,MAAA;gBAAA;gBACA2B,eAAA,EAAAA,eAAA;gBACAxD,SAAA,EAAAA,SAAA;gBACAoF,YAAA;gBACAC,eAAA;cACA;YAAA;cATApB,GAAA,GAAAe,SAAA,CAAApC,IAAA;cAAA,KAUAqB,GAAA,CAAAC,SAAA;gBAAAc,SAAA,CAAAxC,IAAA;gBAAA;cAAA;cACArD,IAAA,GAAA8E,GAAA,CAAAI,IAAA,CAAAA,IAAA,CAAAlB,MAAA,WAAAC,IAAA;gBACA,OAAAA,IAAA,CAAAkC,gBAAA;cACA;cACAT,MAAA,CAAApF,SAAA;cAAA,OAAAuF,SAAA,CAAAO,MAAA,WACApG,IAAA;YAAA;cAEA0F,MAAA,CAAApF,SAAA;YAAA;YAAA;cAAA,OAAAuF,SAAA,CAAApB,IAAA;UAAA;QAAA,GAAAkB,QAAA;MAAA;IAEA;IAEArB,iBAAA,WAAAA,kBAAAtE,IAAA;MACA;MACA,IAAAqG,SAAA,OAAAC,GAAA;;MAEA;MAAA,IAAAC,SAAA,GAAAC,0BAAA,CACAxG,IAAA;QAAAyG,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;UAAA,IAAA3C,IAAA,GAAAwC,KAAA,CAAAI,KAAA;UACA;UACA,IAAA5C,IAAA,CAAA6C,UAAA,YAAA7C,IAAA,CAAA6C,UAAA;YACA;YACA,IAAAC,OAAA,GAAAxE,aAAA,CAAAA,aAAA,KACA0B,IAAA;cACA+C,iBAAA,EAAA/C,IAAA,CAAA+C,iBAAA;cACAC,aAAA,EAAAhD,IAAA,CAAAgD,aAAA;cACAd,gBAAA,EAAAlC,IAAA,CAAAkC,gBAAA;cACAe,YAAA,EAAAjD,IAAA,CAAAiD,YAAA;YAAA,EACA;YACA;YACAb,SAAA,CAAAc,GAAA,SAAAC,MAAA,CAAAf,SAAA,CAAAgB,IAAA,GAAAN,OAAA;YACA;UACA;;UAEA;UACA,IAAAO,GAAA,IACArD,IAAA,CAAA6C,UAAA,EACA7C,IAAA,CAAAsD,IAAA,EACAtD,IAAA,CAAAuD,MAAA,EACAvD,IAAA,CAAAwD,SAAA,EACAxD,IAAA,CAAAyD,oBAAA,EACAzD,IAAA,CAAA0D,MAAA,EACA1D,IAAA,CAAA2D,OAAA,EACA3D,IAAA,CAAA4D,iBAAA,EACA5D,IAAA,CAAA6D,uBAAA,EACA7D,IAAA,CAAAjD,gBAAA,CACA,CAAAqE,IAAA;;UAEA;UACA,IAAAgB,SAAA,CAAA0B,GAAA,CAAAT,GAAA;YACA;YACA,IAAAU,YAAA,GAAA3B,SAAA,CAAA4B,GAAA,CAAAX,GAAA;;YAEA;YACAU,YAAA,CAAAhB,iBAAA,IAAA/C,IAAA,CAAA+C,iBAAA;YACAgB,YAAA,CAAAf,aAAA,IAAAhD,IAAA,CAAAgD,aAAA;YACAe,YAAA,CAAA7B,gBAAA,IAAAlC,IAAA,CAAAkC,gBAAA;YACA6B,YAAA,CAAAd,YAAA,IAAAjD,IAAA,CAAAiD,YAAA;UACA;YACA,IAAAH,QAAA,GAAAxE,aAAA,CAAAA,aAAA,KACA0B,IAAA;cACA+C,iBAAA,EAAA/C,IAAA,CAAA+C,iBAAA;cACAC,aAAA,EAAAhD,IAAA,CAAAgD,aAAA;cACAd,gBAAA,EAAAlC,IAAA,CAAAkC,gBAAA;cACAe,YAAA,EAAAjD,IAAA,CAAAiD,YAAA;YAAA,EACA;;YAEA;YACAb,SAAA,CAAAc,GAAA,CAAAG,GAAA,EAAAP,QAAA;UACA;QACA;;QAEA;MAAA,SAAAmB,GAAA;QAAA3B,SAAA,CAAA4B,CAAA,CAAAD,GAAA;MAAA;QAAA3B,SAAA,CAAA6B,CAAA;MAAA;MACA,OAAAC,KAAA,CAAAC,IAAA,CAAAjC,SAAA,CAAAkC,MAAA;IACA;IAEAC,WAAA,WAAAA,YAAA;MACA,KAAAC,KAAA,SAAAC,WAAA;IACA;IAEAnE,OAAA,WAAAA,QAAA;MAAA,IAAAoE,MAAA;MACA,IAAAC,EAAA,QAAAH,KAAA;MACA,WAAAI,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACAJ,MAAA,CAAAK,SAAA,WAAAC,CAAA;UAAA,IAAAC,WAAA;UACA,IAAAC,MAAA,GAAAP,EAAA,CAAAH,KAAA;UACA,IAAAW,OAAA,GAAAD,MAAA,CAAAE,SAAA;UACAV,MAAA,CAAA5G,OAAA,qCAAAqF,MAAA,CACAuB,MAAA,CAAA/H,IAAA,CAAAU,oBAAA,kQAAA8F,MAAA,CAIAuB,MAAA,CAAA/H,IAAA,CAAAE,YAAA,aAAAsG,MAAA,CAAAuB,MAAA,CAAA/H,IAAA,CAAAG,SAAA,2FAAAqG,MAAA,CACAuB,MAAA,CAAA/H,IAAA,CAAAK,cAAA,2FAAAmG,MAAA,CACAuB,MAAA,CAAA/H,IAAA,CAAAS,iBAAA,uLAAA+F,MAAA,CAGAuB,MAAA,CAAA/H,IAAA,CAAAQ,UAAA,2FAAAgG,MAAA,CACA,EAAA8B,WAAA,GAAAP,MAAA,CAAA/H,IAAA,cAAAsI,WAAA,uBAAAA,WAAA,CAAArI,SAAA,oHAAAuG,MAAA,CACAuB,MAAA,CAAA/H,IAAA,CAAAW,kBAAA,kMAAA6F,MAAA,CAGAuB,MAAA,CAAA/H,IAAA,CAAAY,mBAAA,+EAAA4F,MAAA,CACAuB,MAAA,CAAAxG,MAAA,wJAAAiF,MAAA,CAGAuB,MAAA,CAAAtG,eAAA,kIAAA+E,MAAA,CAIAgC,OAAA,6DAGA;UACAN,OAAA;QACA;MACA;IACA;IACAtE,UAAA,WAAAA,WAAA;MAAA,IAAA8E,MAAA;MACA,KAAA/E,OAAA,GAAAM,IAAA,WAAAoE,CAAA;QACAK,MAAA,CAAAb,KAAA,CAAAc,MAAA,CAAAC,KAAA;UACA9H,SAAA,EAAA4H,MAAA,CAAA7H,WAAA,CAAAC,SAAA;UACAC,KAAA,EAAA/B,UAAA;UACA6J,IAAA;UACAjJ,OAAA,EAAA8I,MAAA,CAAA3I,YAAA,CAAAwE,GAAA,WAAAuE,CAAA;YACA;cACAC,KAAA,EAAAD,CAAA,CAAAxF;YACA;UACA;UACAtC,iBAAA,WAAAA,kBAAAgI,KAAA;YAAA,IAAA9H,OAAA,GAAA8H,KAAA,CAAA9H,OAAA;YACA,IAAA+H,MAAA,GAAAP,MAAA,CAAAvH,OAAA,GAAAD,OAAA;YACA,OAAA+H,MAAA;UACA;QACA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAC,GAAA,GACA;IACAC,MAAA,WAAAA,OAAA;MACA1K,YAAA,MAAA2K,MAAA,OAAA3G,MAAA;IACA;IAEA;IACA4G,cAAA,WAAAA,eAAAC,KAAA;MAAA,IAAAJ,GAAA,GAAAI,KAAA,CAAAJ,GAAA;QAAAK,QAAA,GAAAD,KAAA,CAAAC,QAAA;QAAAjG,MAAA,GAAAgG,KAAA,CAAAhG,MAAA;QAAAkG,WAAA,GAAAF,KAAA,CAAAE,WAAA;IAMA,EALA;IACA;IACA;IACA;IACA;IAAA;IAGA;IACAC,aAAA,WAAAA,cAAAC,KAAA;MAAA,IAAAR,GAAA,GAAAQ,KAAA,CAAAR,GAAA;QAAAK,QAAA,GAAAG,KAAA,CAAAH,QAAA;QAAAjG,MAAA,GAAAoG,KAAA,CAAApG,MAAA;QAAAkG,WAAA,GAAAE,KAAA,CAAAF,WAAA;IAIA,EAHA;IACA;IACA;EAEA;AACA", "ignoreList": []}]}
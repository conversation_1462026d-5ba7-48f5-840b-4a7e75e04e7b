{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\PartTakeConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\PartTakeConfig.vue", "mtime": 1757577002377}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON>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"}, {"version": 3, "names": ["GetBOMInfo", "GetConsumingAllList", "SaveConsumingProcessAllList2", "GetProcessList", "data", "list", "btnLoading", "selectList", "bomList", "tabBomList", "comName", "partName", "bomActiveName", "parentBomList", "computed", "filteredList", "_this", "filter", "item", "Use_Bom_Level", "toString", "mounted", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getBom", "getParentBom", "getProcessList", "stop", "methods", "_this3", "_callee2", "_yield$GetBOMInfo", "_callee2$", "_context2", "sent", "i", "Code", "length", "getTypeList", "_this4", "then", "res", "IsSucceed", "resData", "Data", "Process_List_All", "map", "Process_List_Json", "Bom_Level", "Working_Process_Id", "Working_Process_Code", "Working_Process_Name", "push", "for<PERSON>ach", "dataItem", "Process_List", "newItem", "exists", "some", "existingItem", "$nextTick", "ensureDataIndependence", "$message", "message", "Message", "type", "_this5", "finally", "handleSubmit", "_this6", "success", "$emit", "error", "bomClick", "_this7", "currentIndex", "findIndex", "slice", "getFilteredSelectList", "code", "getProcessId", "bomCode", "Array", "isArray", "processItem", "find", "p", "updateProcessId", "value", "$set", "processItemIndex", "selectedProcess", "s", "Id", "Name", "newProcessItem", "originalProcessList"], "sources": ["src/views/PRO/process-settings/management/component/PartTakeConfig.vue"], "sourcesContent": ["<template>\n  <div class=\"form-wrapper\">\n    <div class=\"form-recognition-tabs\">\n      <el-tabs v-model=\"bomActiveName\" @tab-click=\"bomClick\">\n        <el-tab-pane v-for=\"(item, index) in tabBomList\" :key=\"index\" :label=\"item.Display_Name\" :name=\"item.Code\" />\n      </el-tabs>\n    </div>\n    <div class=\"form-content\">\n      <div class=\"can-process-title\">\n        <div style=\"width: 120px;\" />\n        <div class=\"can-process-list\">\n          <div v-for=\"(item, index) in parentBomList\" :key=\"index\" :style=\"{ width: (100 / parentBomList.length) + '%', textAlign: 'center' }\">{{ item.Display_Name }}</div>\n        </div>\n      </div>\n      <div class=\"can-process-box\">\n        <div v-if=\"!filteredList.length\" class=\"can-process-empty\">\n          暂无数据\n        </div>\n        <div v-for=\"(item, index) in filteredList\" :key=\"index\" class=\"can-process-item\">\n          <div class=\"can-process-type\" style=\"width: 120px;\">{{ item.Part_Type_Name }}</div>\n          <div class=\"can-process-bom\">\n            <div v-for=\"bom in parentBomList\" :key=\"`${item.Part_Type_Id || index}-${bom.Code}`\" class=\"can-process-select\" :style=\"{ width: (100 / parentBomList.length) + '%' }\">\n              <el-select\n                :key=\"`select-${item.Part_Type_Id || item.Part_Type_Name || index}-${bom.Code}`\"\n                :value=\"getProcessId(item, bom.Code)\"\n                clearable\n                style=\"width: 100%;\"\n                placeholder=\"请选择\"\n                @change=\"updateProcessId(item, bom.Code, $event)\"\n              >\n                <el-option v-for=\"op in getFilteredSelectList(bom.Code)\" :key=\"op.Id\" :label=\"op.Name\" :value=\"op.Id\" />\n              </el-select>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div class=\"form-footer\">\n      <el-button @click=\"$emit('close')\">取 消</el-button>\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport { GetConsumingAllList, SaveConsumingProcessAllList2 } from '@/api/PRO/partType'\nimport { GetProcessList } from '@/api/PRO/technology-lib'\n\nexport default {\n  data() {\n    return {\n      list: [],\n      btnLoading: false,\n      selectList: [],\n      bomList: [],\n      tabBomList: [],\n      comName: '',\n      partName: '',\n      bomActiveName: '',\n      parentBomList: []\n    }\n  },\n  computed: {\n    // 根据当前选中的BOM层级过滤数据\n    filteredList() {\n      if (!this.list || !this.bomActiveName) {\n        return []\n      }\n      return this.list.filter(item => item.Use_Bom_Level.toString() === this.bomActiveName)\n    }\n  },\n  async mounted() {\n    await this.getBom()\n    await this.getParentBom()\n    await this.getProcessList()\n  },\n  methods: {\n    async getBom() {\n      const { comName, partName, list } = await GetBOMInfo()\n      this.comName = comName\n      this.partName = partName\n      this.bomList = list\n      this.tabBomList = list.filter(i => i.Code !== '-1')\n      this.bomActiveName = this.tabBomList[this.tabBomList.length - 1].Code\n    },\n    getTypeList() {\n      GetConsumingAllList({}).then(res => {\n        if (res.IsSucceed) {\n          const resData = res.Data\n          const Process_List_All = []\n          this.parentBomList.map(item => {\n            const Process_List_Json = {}\n            Process_List_Json.Bom_Level = item.Code\n            Process_List_Json.Working_Process_Id = ''\n            Process_List_Json.Working_Process_Code = ''\n            Process_List_Json.Working_Process_Name = ''\n            Process_List_All.push(Process_List_Json)\n          })\n\n          // 遍历 resData 中的每个项目，为每个项目的 Process_List 添加缺失的数据\n          resData.forEach(dataItem => {\n            // 确保每个项目的 Process_List 存在\n            if (!dataItem.Process_List) {\n              dataItem.Process_List = []\n            }\n\n            // 如果 Process_List 为空，直接追加所有 Process_List_All 的数据（深拷贝）\n            if (dataItem.Process_List.length === 0) {\n              Process_List_All.forEach(item => {\n                dataItem.Process_List.push({\n                  Bom_Level: item.Bom_Level,\n                  Working_Process_Id: item.Working_Process_Id,\n                  Working_Process_Code: item.Working_Process_Code,\n                  Working_Process_Name: item.Working_Process_Name\n                })\n              })\n            } else {\n              // 将 Process_List_All 中不存在于当前项目 Process_List 的数据追加进去\n              Process_List_All.forEach(newItem => {\n                // 检查当前项目的 Process_List 中是否已存在相同的 Bom_Level\n                const exists = dataItem.Process_List.some(existingItem =>\n                  existingItem.Bom_Level.toString() === newItem.Bom_Level.toString()\n                )\n\n                // 如果不存在，则追加到当前项目的 Process_List（深拷贝）\n                if (!exists) {\n                  dataItem.Process_List.push({\n                    Bom_Level: newItem.Bom_Level,\n                    Working_Process_Id: newItem.Working_Process_Id,\n                    Working_Process_Code: newItem.Working_Process_Code,\n                    Working_Process_Name: newItem.Working_Process_Name\n                  })\n                }\n              })\n            }\n          })\n          this.list = resData\n          // 确保数据独立性\n          this.$nextTick(() => {\n            this.ensureDataIndependence()\n          })\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    getProcessList() {\n      GetProcessList({ }).then(res => {\n        this.selectList = res.Data\n      }).finally(() => {\n        this.getTypeList()\n      })\n    },\n    handleSubmit() {\n      this.btnLoading = true\n      SaveConsumingProcessAllList2(this.list).then(res => {\n        if (res.IsSucceed) {\n          this.$message.success('保存成功')\n          this.$emit('close')\n        } else {\n          this.$message.error(res.Message)\n        }\n      }).finally(() => {\n        this.btnLoading = false\n      })\n    },\n    bomClick() {\n      this.getParentBom()\n    },\n    // 获取当前bom层级的所有父级bom层级信息\n    getParentBom() {\n      // 找到当前code在bomList中的索引位置\n      const currentIndex = this.bomList.findIndex(item => item.Code === this.bomActiveName)\n\n      // 如果找到了，则截取该索引之前的所有数据\n      if (currentIndex > 0) {\n        this.parentBomList = this.bomList.slice(0, currentIndex)\n      } else {\n        // 如果是第一个或者没找到，则返回空数组\n        this.parentBomList = []\n      }\n    },\n    // 根据BOM层级Code过滤selectList\n    getFilteredSelectList(code) {\n      if (!this.selectList || !code) {\n        return []\n      }\n      return this.selectList.filter(item => item.Bom_Level.toString() === code || item.Bom_Level.toString() === code)\n    },\n    // 获取指定item和bomCode对应的Working_Process_Id\n    getProcessId(item, bomCode) {\n      if (!item.Process_List || !bomCode) {\n        return ''\n      }\n\n      // 确保 Process_List 是数组\n      if (!Array.isArray(item.Process_List)) {\n        return ''\n      }\n\n      const processItem = item.Process_List.find(p =>\n        p && p.Bom_Level && p.Bom_Level.toString() === bomCode.toString()\n      )\n\n      return processItem ? (processItem.Working_Process_Id || '') : ''\n    },\n    // 更新指定item和bomCode对应的Working_Process_Id\n    updateProcessId(item, bomCode, value) {\n      // 确保 Process_List 存在且是数组\n      if (!item.Process_List || !Array.isArray(item.Process_List)) {\n        this.$set(item, 'Process_List', [])\n      }\n\n      // 查找对应的 Bom_Level 项目\n      const processItemIndex = item.Process_List.findIndex(p =>\n        p && p.Bom_Level && p.Bom_Level.toString() === bomCode.toString()\n      )\n\n      if (processItemIndex !== -1) {\n        // 如果找到了对应的项目，更新其值\n        const processItem = item.Process_List[processItemIndex]\n\n        // 使用 $set 确保响应式更新\n        this.$set(processItem, 'Working_Process_Id', value)\n\n        // 同时更新对应的工艺信息\n        if (value) {\n          const selectedProcess = this.selectList.find(s => s.Id === value)\n          if (selectedProcess) {\n            this.$set(processItem, 'Working_Process_Code', selectedProcess.Code || '')\n            this.$set(processItem, 'Working_Process_Name', selectedProcess.Name || '')\n          }\n        } else {\n          this.$set(processItem, 'Working_Process_Code', '')\n          this.$set(processItem, 'Working_Process_Name', '')\n        }\n      } else {\n        // 如果没找到，创建新的项目\n        const newProcessItem = {\n          Bom_Level: bomCode,\n          Working_Process_Id: value,\n          Working_Process_Code: '',\n          Working_Process_Name: ''\n        }\n\n        // 如果有选中值，填充工艺信息\n        if (value) {\n          const selectedProcess = this.selectList.find(s => s.Id === value)\n          if (selectedProcess) {\n            newProcessItem.Working_Process_Code = selectedProcess.Code || ''\n            newProcessItem.Working_Process_Name = selectedProcess.Name || ''\n          }\n        }\n\n        // 使用 $set 添加新项目\n        item.Process_List.push(newProcessItem)\n      }\n    },\n    // 确保每个item的Process_List都是独立的\n    ensureDataIndependence() {\n      if (this.list && Array.isArray(this.list)) {\n        this.list.forEach(item => {\n          if (item.Process_List && Array.isArray(item.Process_List)) {\n            // 深拷贝 Process_List 确保数据独立\n            const originalProcessList = item.Process_List\n            item.Process_List = originalProcessList.map(processItem => ({\n              Bom_Level: processItem.Bom_Level,\n              Working_Process_Id: processItem.Working_Process_Id || '',\n              Working_Process_Code: processItem.Working_Process_Code || '',\n              Working_Process_Name: processItem.Working_Process_Name || ''\n            }))\n          }\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"~@/styles/mixin.scss\";\n.form-wrapper {\n  height: 50vh;\n  display: flex;\n  flex-direction: column;\n\n  .form-content {\n    flex: 1;\n    overflow: auto;\n    padding-right: 16px;\n    @include scrollBar;\n\n    .form-x {\n      padding-bottom: 20px;\n    }\n  }\n\n  .form-footer {\n    text-align: right;\n    flex-shrink: 0;\n    padding-top: 16px;\n    background: #fff;\n  }\n}\n\n.can-process-title {\n  display: flex;\n  height: 38px;\n  .can-process-list {\n    flex: 1;\n    display: flex;\n    justify-content: flex-start;\n    font-size: 16px;\n    color: #333333;\n  }\n}\n\n.can-process-box {\n  height: calc(100% - 38px);\n  .can-process-empty {\n    height: 100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  .can-process-item {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    margin-bottom: 16px;\n    .can-process-type {\n      font-size: 14px;\n      color: #333333;\n      text-align: right;\n    }\n    .can-process-bom {\n      flex: 1;\n      display: flex;\n      justify-content: flex-start;\n      .can-process-select {\n        margin-left: 12px;\n        text-align: center;\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA,SAAAA,UAAA;AACA,SAAAC,mBAAA,EAAAC,4BAAA;AACA,SAAAC,cAAA;AAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;MACAC,UAAA;MACAC,UAAA;MACAC,OAAA;MACAC,UAAA;MACAC,OAAA;MACAC,QAAA;MACAC,aAAA;MACAC,aAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MACA,UAAAX,IAAA,UAAAO,aAAA;QACA;MACA;MACA,YAAAP,IAAA,CAAAY,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,aAAA,CAAAC,QAAA,OAAAJ,KAAA,CAAAJ,aAAA;MAAA;IACA;EACA;EACAS,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACAT,MAAA,CAAAU,MAAA;UAAA;YAAAH,QAAA,CAAAE,IAAA;YAAA,OACAT,MAAA,CAAAW,YAAA;UAAA;YAAAJ,QAAA,CAAAE,IAAA;YAAA,OACAT,MAAA,CAAAY,cAAA;UAAA;UAAA;YAAA,OAAAL,QAAA,CAAAM,IAAA;QAAA;MAAA,GAAAT,OAAA;IAAA;EACA;EACAU,OAAA;IACAJ,MAAA,WAAAA,OAAA;MAAA,IAAAK,MAAA;MAAA,OAAAd,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAa,SAAA;QAAA,IAAAC,iBAAA,EAAA7B,OAAA,EAAAC,QAAA,EAAAN,IAAA;QAAA,OAAAmB,mBAAA,GAAAG,IAAA,UAAAa,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAX,IAAA,GAAAW,SAAA,CAAAV,IAAA;YAAA;cAAAU,SAAA,CAAAV,IAAA;cAAA,OACA/B,UAAA;YAAA;cAAAuC,iBAAA,GAAAE,SAAA,CAAAC,IAAA;cAAAhC,OAAA,GAAA6B,iBAAA,CAAA7B,OAAA;cAAAC,QAAA,GAAA4B,iBAAA,CAAA5B,QAAA;cAAAN,IAAA,GAAAkC,iBAAA,CAAAlC,IAAA;cACAgC,MAAA,CAAA3B,OAAA,GAAAA,OAAA;cACA2B,MAAA,CAAA1B,QAAA,GAAAA,QAAA;cACA0B,MAAA,CAAA7B,OAAA,GAAAH,IAAA;cACAgC,MAAA,CAAA5B,UAAA,GAAAJ,IAAA,CAAAY,MAAA,WAAA0B,CAAA;gBAAA,OAAAA,CAAA,CAAAC,IAAA;cAAA;cACAP,MAAA,CAAAzB,aAAA,GAAAyB,MAAA,CAAA5B,UAAA,CAAA4B,MAAA,CAAA5B,UAAA,CAAAoC,MAAA,MAAAD,IAAA;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAAN,IAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IACA;IACAQ,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA9C,mBAAA,KAAA+C,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA,IAAAC,OAAA,GAAAF,GAAA,CAAAG,IAAA;UACA,IAAAC,gBAAA;UACAN,MAAA,CAAAlC,aAAA,CAAAyC,GAAA,WAAApC,IAAA;YACA,IAAAqC,iBAAA;YACAA,iBAAA,CAAAC,SAAA,GAAAtC,IAAA,CAAA0B,IAAA;YACAW,iBAAA,CAAAE,kBAAA;YACAF,iBAAA,CAAAG,oBAAA;YACAH,iBAAA,CAAAI,oBAAA;YACAN,gBAAA,CAAAO,IAAA,CAAAL,iBAAA;UACA;;UAEA;UACAJ,OAAA,CAAAU,OAAA,WAAAC,QAAA;YACA;YACA,KAAAA,QAAA,CAAAC,YAAA;cACAD,QAAA,CAAAC,YAAA;YACA;;YAEA;YACA,IAAAD,QAAA,CAAAC,YAAA,CAAAlB,MAAA;cACAQ,gBAAA,CAAAQ,OAAA,WAAA3C,IAAA;gBACA4C,QAAA,CAAAC,YAAA,CAAAH,IAAA;kBACAJ,SAAA,EAAAtC,IAAA,CAAAsC,SAAA;kBACAC,kBAAA,EAAAvC,IAAA,CAAAuC,kBAAA;kBACAC,oBAAA,EAAAxC,IAAA,CAAAwC,oBAAA;kBACAC,oBAAA,EAAAzC,IAAA,CAAAyC;gBACA;cACA;YACA;cACA;cACAN,gBAAA,CAAAQ,OAAA,WAAAG,OAAA;gBACA;gBACA,IAAAC,MAAA,GAAAH,QAAA,CAAAC,YAAA,CAAAG,IAAA,WAAAC,YAAA;kBAAA,OACAA,YAAA,CAAAX,SAAA,CAAApC,QAAA,OAAA4C,OAAA,CAAAR,SAAA,CAAApC,QAAA;gBAAA,CACA;;gBAEA;gBACA,KAAA6C,MAAA;kBACAH,QAAA,CAAAC,YAAA,CAAAH,IAAA;oBACAJ,SAAA,EAAAQ,OAAA,CAAAR,SAAA;oBACAC,kBAAA,EAAAO,OAAA,CAAAP,kBAAA;oBACAC,oBAAA,EAAAM,OAAA,CAAAN,oBAAA;oBACAC,oBAAA,EAAAK,OAAA,CAAAL;kBACA;gBACA;cACA;YACA;UACA;UACAZ,MAAA,CAAA1C,IAAA,GAAA8C,OAAA;UACA;UACAJ,MAAA,CAAAqB,SAAA;YACArB,MAAA,CAAAsB,sBAAA;UACA;QACA;UACAtB,MAAA,CAAAuB,QAAA;YACAC,OAAA,EAAAtB,GAAA,CAAAuB,OAAA;YACAC,IAAA;UACA;QACA;MACA;IACA;IACAvC,cAAA,WAAAA,eAAA;MAAA,IAAAwC,MAAA;MACAvE,cAAA,KAAA6C,IAAA,WAAAC,GAAA;QACAyB,MAAA,CAAAnE,UAAA,GAAA0C,GAAA,CAAAG,IAAA;MACA,GAAAuB,OAAA;QACAD,MAAA,CAAA5B,WAAA;MACA;IACA;IACA8B,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAvE,UAAA;MACAJ,4BAAA,MAAAG,IAAA,EAAA2C,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA2B,MAAA,CAAAP,QAAA,CAAAQ,OAAA;UACAD,MAAA,CAAAE,KAAA;QACA;UACAF,MAAA,CAAAP,QAAA,CAAAU,KAAA,CAAA/B,GAAA,CAAAuB,OAAA;QACA;MACA,GAAAG,OAAA;QACAE,MAAA,CAAAvE,UAAA;MACA;IACA;IACA2E,QAAA,WAAAA,SAAA;MACA,KAAAhD,YAAA;IACA;IACA;IACAA,YAAA,WAAAA,aAAA;MAAA,IAAAiD,MAAA;MACA;MACA,IAAAC,YAAA,QAAA3E,OAAA,CAAA4E,SAAA,WAAAlE,IAAA;QAAA,OAAAA,IAAA,CAAA0B,IAAA,KAAAsC,MAAA,CAAAtE,aAAA;MAAA;;MAEA;MACA,IAAAuE,YAAA;QACA,KAAAtE,aAAA,QAAAL,OAAA,CAAA6E,KAAA,IAAAF,YAAA;MACA;QACA;QACA,KAAAtE,aAAA;MACA;IACA;IACA;IACAyE,qBAAA,WAAAA,sBAAAC,IAAA;MACA,UAAAhF,UAAA,KAAAgF,IAAA;QACA;MACA;MACA,YAAAhF,UAAA,CAAAU,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAsC,SAAA,CAAApC,QAAA,OAAAmE,IAAA,IAAArE,IAAA,CAAAsC,SAAA,CAAApC,QAAA,OAAAmE,IAAA;MAAA;IACA;IACA;IACAC,YAAA,WAAAA,aAAAtE,IAAA,EAAAuE,OAAA;MACA,KAAAvE,IAAA,CAAA6C,YAAA,KAAA0B,OAAA;QACA;MACA;;MAEA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAzE,IAAA,CAAA6C,YAAA;QACA;MACA;MAEA,IAAA6B,WAAA,GAAA1E,IAAA,CAAA6C,YAAA,CAAA8B,IAAA,WAAAC,CAAA;QAAA,OACAA,CAAA,IAAAA,CAAA,CAAAtC,SAAA,IAAAsC,CAAA,CAAAtC,SAAA,CAAApC,QAAA,OAAAqE,OAAA,CAAArE,QAAA;MAAA,CACA;MAEA,OAAAwE,WAAA,GAAAA,WAAA,CAAAnC,kBAAA;IACA;IACA;IACAsC,eAAA,WAAAA,gBAAA7E,IAAA,EAAAuE,OAAA,EAAAO,KAAA;MACA;MACA,KAAA9E,IAAA,CAAA6C,YAAA,KAAA2B,KAAA,CAAAC,OAAA,CAAAzE,IAAA,CAAA6C,YAAA;QACA,KAAAkC,IAAA,CAAA/E,IAAA;MACA;;MAEA;MACA,IAAAgF,gBAAA,GAAAhF,IAAA,CAAA6C,YAAA,CAAAqB,SAAA,WAAAU,CAAA;QAAA,OACAA,CAAA,IAAAA,CAAA,CAAAtC,SAAA,IAAAsC,CAAA,CAAAtC,SAAA,CAAApC,QAAA,OAAAqE,OAAA,CAAArE,QAAA;MAAA,CACA;MAEA,IAAA8E,gBAAA;QACA;QACA,IAAAN,WAAA,GAAA1E,IAAA,CAAA6C,YAAA,CAAAmC,gBAAA;;QAEA;QACA,KAAAD,IAAA,CAAAL,WAAA,wBAAAI,KAAA;;QAEA;QACA,IAAAA,KAAA;UACA,IAAAG,eAAA,QAAA5F,UAAA,CAAAsF,IAAA,WAAAO,CAAA;YAAA,OAAAA,CAAA,CAAAC,EAAA,KAAAL,KAAA;UAAA;UACA,IAAAG,eAAA;YACA,KAAAF,IAAA,CAAAL,WAAA,0BAAAO,eAAA,CAAAvD,IAAA;YACA,KAAAqD,IAAA,CAAAL,WAAA,0BAAAO,eAAA,CAAAG,IAAA;UACA;QACA;UACA,KAAAL,IAAA,CAAAL,WAAA;UACA,KAAAK,IAAA,CAAAL,WAAA;QACA;MACA;QACA;QACA,IAAAW,cAAA;UACA/C,SAAA,EAAAiC,OAAA;UACAhC,kBAAA,EAAAuC,KAAA;UACAtC,oBAAA;UACAC,oBAAA;QACA;;QAEA;QACA,IAAAqC,KAAA;UACA,IAAAG,gBAAA,QAAA5F,UAAA,CAAAsF,IAAA,WAAAO,CAAA;YAAA,OAAAA,CAAA,CAAAC,EAAA,KAAAL,KAAA;UAAA;UACA,IAAAG,gBAAA;YACAI,cAAA,CAAA7C,oBAAA,GAAAyC,gBAAA,CAAAvD,IAAA;YACA2D,cAAA,CAAA5C,oBAAA,GAAAwC,gBAAA,CAAAG,IAAA;UACA;QACA;;QAEA;QACApF,IAAA,CAAA6C,YAAA,CAAAH,IAAA,CAAA2C,cAAA;MACA;IACA;IACA;IACAlC,sBAAA,WAAAA,uBAAA;MACA,SAAAhE,IAAA,IAAAqF,KAAA,CAAAC,OAAA,MAAAtF,IAAA;QACA,KAAAA,IAAA,CAAAwD,OAAA,WAAA3C,IAAA;UACA,IAAAA,IAAA,CAAA6C,YAAA,IAAA2B,KAAA,CAAAC,OAAA,CAAAzE,IAAA,CAAA6C,YAAA;YACA;YACA,IAAAyC,mBAAA,GAAAtF,IAAA,CAAA6C,YAAA;YACA7C,IAAA,CAAA6C,YAAA,GAAAyC,mBAAA,CAAAlD,GAAA,WAAAsC,WAAA;cAAA;gBACApC,SAAA,EAAAoC,WAAA,CAAApC,SAAA;gBACAC,kBAAA,EAAAmC,WAAA,CAAAnC,kBAAA;gBACAC,oBAAA,EAAAkC,WAAA,CAAAlC,oBAAA;gBACAC,oBAAA,EAAAiC,WAAA,CAAAjC,oBAAA;cACA;YAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
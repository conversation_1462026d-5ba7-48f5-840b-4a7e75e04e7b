<template>
  <div class="abs100 cs-z-flex-pd16-wrap">
    <div class="cs-z-page-main-content">
      <top-header padding="0">
        <template #left>
          <el-button type="primary" @click="handleAdd">新增</el-button>
        </template>
      </top-header>
      <div class="tb-x">
        <vxe-table
          :empty-render="{name: 'NotData'}"
          show-header-overflow
          element-loading-spinner="el-icon-loading"
          element-loading-text="拼命加载中"
          empty-text="暂无数据"
          class="cs-vxe-table"
          height="auto"
          align="left"
          stripe
          :loading="tbLoading"
          :row-config="{ isCurrent: true, isHover: true }"
          :data="tbData"
          resizable
          :tooltip-config="{ enterable: true }"
        >
          <template>
            <vxe-column
              v-for="(item, index) in columns"
              :key="index"
              :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
              show-overflow="tooltip"
              sortable
              align="left"
              :field="item.Code"
              :title="item.Display_Name"
              :min-width="item.Width ? item.Width : 120"
            >
              <template #default="{ row }">
                <div v-if="item.Code === 'Type'">
                  <span :style="{color:row[item.Code]===1 ?'#d29730': row[item.Code]===2?'#20bbc7':'#de85e4'}">
                    <!-- {{ row[item.Code]===1 ?'构件工艺':row[item.Code]===2?'零件工艺':'部件工艺' }} -->
                    <!-- {{ row.Bom_Level === '-1' ? '构件工艺' : row.Bom_Level === '0' ? '零件工艺' : '部件工艺' }} -->
                    {{ getBomName(row.Bom_Level) }}工艺
                  </span>
                </div>
                <div v-else>
                  {{ row[item.Code] | displayValue }}
                </div>
              </template>
            </vxe-column>
            <vxe-table-column title="操作" :min-width="120">
              <template #default="{ row }">
                <el-button type="text" @click="handleEdit(row)">编辑</el-button>
                <el-button type="text" class="txt-red" @click="handleDelete(row)">删除</el-button>
              </template>
            </vxe-table-column>
          </template>
        </vxe-table>
      </div>
      <Dialog ref="dialog" :bom-list="bomList" @refresh="fetchData" />
    </div>
  </div>
</template>

<script>
import TopHeader from '@/components/TopHeader/index.vue'
import getTbInfo from '@/mixins/PRO/get-table-info'
import { DeleteTechnology, GetLibList } from '@/api/PRO/technology-lib'
import { mapGetters } from 'vuex'
import Dialog from './compoments/Add.vue'
import { GetBOMInfo } from '@/views/PRO/bom-setting/utils'

export default {
  name: 'PROProcessPath',
  components: { TopHeader, Dialog },
  mixins: [getTbInfo],
  data() {
    return {
      btnLoading: false,
      tbLoading: false,
      total: 0,
      columns: [],
      tbData: [],
      bomList: [],
      tbConfig: {}
    }
  },
  computed: {
    ...mapGetters('tenant', ['isVersionFour'])
  },
  async mounted() {
    await this.getTableConfig('ProcessPathList')
    const { list } = await GetBOMInfo()
    this.bomList = list || []
    this.fetchData()
  },
  methods: {
    getBomName(code) {
      const currentBomInfo = this.bomList.find(item => {
        return item.Code.toString() === code.toString()
      })
      return currentBomInfo?.Display_Name || ''
    },
    fetchData() {
      this.tbLoading = true
      GetLibList({
        Id: '',
        Type: 0
      }).then(res => {
        if (res.IsSucceed) {
          const bomCode = this.bomList.map(item => +item.Code)
          this.tbData = (res.Data || []).filter(item => bomCode.includes(item.Bom_Level))
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      }).finally(() => {
        this.tbLoading = false
      })
    },
    handleAdd(row) {
      this.$refs['dialog'].handleOpen()
    },
    handleEdit(row) {
      this.$refs['dialog'].handleOpen(row)
    },
    handleDelete(row) {
      this.$confirm('是否删除该工艺', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tbLoading = true
        DeleteTechnology({
          technologyId: row.Id
        }).then(res => {
          if (res.IsSucceed) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.fetchData()
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.tb-x{
  flex: 1;
}
</style>

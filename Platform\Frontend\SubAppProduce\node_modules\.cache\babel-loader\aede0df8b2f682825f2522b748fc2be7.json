{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\template-print\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\template-print\\detail.vue", "mtime": 1757468128158}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "hiPrintPlugin", "providers", "DeletePrintTemplate", "GetPrintTemplateEntity", "GetPrintTemplateList", "SavePrintTemplateEntity", "paperTypes", "GetPreferenceSettingValue", "html2canvas", "GetCompany", "deepClone", "disAutoConnect", "hiprintTemplate", "name", "data", "curPaper", "type", "width", "height", "curPaperType", "paperWidth", "paperHeight", "tmplList", "mode", "activeIndex", "keyword", "toEdit", "form", "Name", "Type", "Data", "Base64Image", "logoUrl", "require", "scaleValue", "saveLoading", "scaleMax", "scaleMin", "computed", "filteredTmplList", "_this", "filter", "t", "indexOf", "mounted", "init", "buildLeftElement", "buildDesigner", "methods", "changeScale", "big", "zoom", "getImage", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee2", "wrap", "_callee2$", "_context2", "prev", "next", "Promise", "resolve", "reject", "executeCapture", "_ref", "_callee", "canvas", "dataUrl", "_callee$", "_context", "document", "getElementById", "useCORS", "logging", "remove<PERSON><PERSON><PERSON>", "onclone", "clonedDoc", "clonedElement", "animations", "querySelectorAll", "for<PERSON>ach", "el", "remove", "sent", "toDataURL", "t0", "stop", "apply", "arguments", "window", "requestIdleCallback", "timeout", "setTimeout", "abrupt", "finish", "get<PERSON>ogo", "_this2", "then", "res", "Icon", "tmplSelect", "id", "Id", "loadTemplate", "cloneTemplate", "_this3", "_callee3", "copyTemplate", "_callee3$", "_context3", "JSON", "parse", "stringify", "IsSucceed", "$message", "success", "getTemplateList", "error", "Message", "deleteTemplate", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "center", "catch", "message", "_this5", "_callee4", "provider", "_callee4$", "_context4", "find", "i", "value", "f", "PrintElementTypeManager", "buildByHtml", "$", "template", "length", "undefined", "empty", "PrintTemplate", "<PERSON><PERSON><PERSON><PERSON>", "design", "handlePrint", "printData", "printArr", "get<PERSON>son", "panels", "printElements", "item", "options", "field", "testData", "console", "log", "leftOffset", "topOffset", "ext", "callback", "print", "changePaper", "_this6", "temp", "setPaper", "createTemplate", "clearTemplate", "saveTemplate", "_this7", "_callee5", "json", "_callee5$", "_context5", "_this8", "_callee6", "parseData", "index", "_template$panels$", "matchedPaper", "_callee6$", "_context6", "findIndex", "src", "e", "_this9", "_callee7", "_callee7$", "_context7"], "sources": ["src/views/PRO/shipment/template-print/detail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\r\n    <div style=\"display:flex;height:100%;\">\r\n      <el-aside\r\n        class=\"cs-z-page-main-content\"\r\n        style=\"background:#FFF;margin-right:16px;width: 20vw;min-width:320px;\"\r\n      >\r\n        <el-row :gutter=\"4\" style=\"flex-shrink:0;\">\r\n          <el-col :span=\"17\">\r\n            <el-input\r\n              v-model=\"keyword\"\r\n              placeholder=\"请输入内容\"\r\n              suffix-icon=\"el-icon-search\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"7\">\r\n            <el-button type=\"primary\" @click=\"createTemplate\">新建模板</el-button>\r\n          </el-col>\r\n        </el-row>\r\n        <div class=\"tmpl-list\">\r\n          <el-menu\r\n            class=\"tmpl-menu\"\r\n            :default-active=\"String(activeIndex)\"\r\n          >\r\n            <el-menu-item\r\n              v-for=\"tmpl in filteredTmplList\"\r\n              :key=\"tmpl.Id\"\r\n              :index=\"tmpl.Id\"\r\n              style=\"padding-left:12px;\"\r\n              :title=\"tmpl.Name\"\r\n            >\r\n              <div\r\n                style=\"overflow:hidden;max-width:220px;text-overflow: ellipsis;\"\r\n                @click.stop=\"tmplSelect(tmpl.Id)\"\r\n              >\r\n                <i class=\"el-icon-document\" />{{ tmpl.Name }}\r\n              </div>\r\n              <template v-if=\"String(activeIndex) === tmpl.Id\">\r\n                <!--                <el-link-->\r\n                <!--                  :underline=\"false\"-->\r\n                <!--                  type=\"primary\"-->\r\n                <!--                  @click.stop=\"toEdit = tmpl.Id\"-->\r\n                <!--                >-->\r\n                <!--                  <i class=\"right-align-icon el-icon-edit\" />-->\r\n                <!--                </el-link>-->\r\n                <el-link\r\n                  :underline=\"false\"\r\n                  type=\"danger\"\r\n                  @click=\"deleteTemplate(tmpl.Id)\"\r\n                >\r\n                  <i class=\"right-align-icon el-icon-delete\" />\r\n                </el-link>\r\n                <el-link\r\n                  :underline=\"false\"\r\n                  type=\"primary\"\r\n                  @click=\"cloneTemplate(tmpl.Id)\"\r\n                >\r\n                  <i class=\"right-align-icon el-icon-copy-document\" />\r\n                </el-link>\r\n              </template>\r\n            </el-menu-item>\r\n          </el-menu>\r\n        </div>\r\n        <div class=\"flex-row justify-center flex-wrap\" style=\"display: flex;flex-wrap: wrap\">\r\n          <!-- tid 与 defaultElementTypeProvider 中对应 -->\r\n          <!-- 包含 class=\"ep-draggable-item\" -->\r\n          <div class=\"title\">标题区</div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Logo\">\r\n            Logo图片\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.QrcodeText\">\r\n            二维码\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.ContractNumber\">\r\n            内部合同编号\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Code\">\r\n            单据号\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.SendDate\">\r\n            日期\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Number\">\r\n            发货序号\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Address\">\r\n            项目地址\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.ReceivingUnit\">\r\n            收货单位\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.ProjectName\">\r\n            项目名称\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.MakerName\">\r\n            出库人\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Consignee\">\r\n            收货人\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.ConsigneeTel\">\r\n            联系电话\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.VehicleNo\">\r\n            车牌\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Telephone\">\r\n            司机电话\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Trips\">\r\n            车次\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.LoadingsName\">\r\n            装车班\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.LoadingsPersonnelName\">\r\n            装车班人员\r\n          </div>\r\n          <div class=\"title\">数据区</div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Table\">\r\n            构件/包数据表\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Pound_Weight\">\r\n            磅重（kg）\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Tare_Weight\">\r\n            皮重（kg）\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Net_Weight\">\r\n            净重（kg）\r\n          </div>\r\n          <div class=\"title\">其他</div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.customText\">\r\n            自定义文本\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.hline\">\r\n            横线\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.vline\">\r\n            竖线\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.rect\">\r\n            矩形\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.oval\">\r\n            椭圆\r\n          </div>\r\n        </div>\r\n      </el-aside>\r\n      <el-container class=\"cs-z-page-main-content\">\r\n        <div class=\"header\">\r\n          <el-button type=\"primary\" sizi=\"mini\" @click=\"handlePrint\">打印预览</el-button>\r\n          <el-button type=\"success\" sizi=\"mini\" :loading=\"saveLoading\" @click=\"saveTemplate\">保存模板</el-button>\r\n          <el-button type=\"danger\" sizi=\"mini\" @click=\"clearTemplate\">清空</el-button>\r\n          <span class=\"label\">模板名称</span>\r\n          <el-input v-model=\"form.Name\" style=\"width: 150px\" :maxlength=\"50\" />\r\n          <span class=\"label\">模板布局</span>\r\n          <el-select v-model=\"curPaperType\" style=\"width: 120px\" @change=\"changePaper\">\r\n            <el-option v-for=\"item in paperTypes\" :key=\"item.type\" :value=\"item.type\" :label=\"item.type\" />\r\n          </el-select>\r\n          <div v-if=\"curPaperType==='自定义纸张'\">\r\n            <span class=\"label\">宽</span>\r\n            <el-input v-model=\"paperWidth\" type=\"input\" class=\"input\" @change=\"changePaper\" />\r\n            <span class=\"label\">高</span>\r\n            <el-input v-model=\"paperHeight\" type=\"input\" class=\"input\" @change=\"changePaper\" />\r\n          </div>\r\n          <div style=\"display: flex;align-items: center;margin-left: 10px\">\r\n            <i class=\"el-icon-zoom-out zoom-btn\" @click=\"changeScale(false)\" />\r\n            <div class=\"zoom\">{{ ~~(scaleValue * 100) }}%</div>\r\n            <i class=\"el-icon-zoom-in zoom-btn\" @click=\"changeScale(true)\" />\r\n          </div>\r\n        </div>\r\n        <!-- 设计器的 容器 -->\r\n        <div style=\"margin-top: 10px;display: flex\">\r\n          <div style=\"flex:1;padding-left: 16px;padding-top: 16px;overflow: auto\">\r\n            <div id=\"hiprint-printTemplate\" />\r\n          </div>\r\n          <div class=\"hinnn-layout-sider\" style=\"width: 20vw;min-width: 300px;margin-left: 16px\">\r\n            <div id=\"PrintElementOptionSetting\" />\r\n          </div>\r\n        </div>\r\n      </el-container>\r\n    </div>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { hiprint, hiPrintPlugin } from 'vue-plugin-hiprint'\r\nimport providers from './providers'\r\nimport {\r\n  DeletePrintTemplate,\r\n  GetPrintTemplateEntity,\r\n  GetPrintTemplateList,\r\n  SavePrintTemplateEntity\r\n} from '@/api/PRO/shipment/ship-template-print'\r\nimport { paperTypes } from './config'\r\nimport { GetPreferenceSettingValue } from '@/api/sys/system-setting'\r\nimport html2canvas from 'html2canvas'\r\nimport { GetCompany } from '@/api/plm/site'\r\nimport { deepClone } from '@/utils'\r\n\r\nhiPrintPlugin.disAutoConnect()\r\n\r\nlet hiprintTemplate\r\nexport default {\r\n  name: 'ShipTemplatePrintDetail',\r\n  data() {\r\n    return {\r\n      // 当前纸张\r\n      curPaper: {\r\n        type: 'A4',\r\n        width: 210,\r\n        height: 296.6\r\n      },\r\n      curPaperType: 'A4',\r\n      // 纸张类型\r\n      paperTypes: deepClone(paperTypes),\r\n      // 自定义纸张\r\n      paperWidth: '220',\r\n      paperHeight: '80',\r\n      tmplList: [],\r\n      mode: 1,\r\n      activeIndex: '',\r\n      keyword: '',\r\n      toEdit: '',\r\n      form: {\r\n        Name: '',\r\n        Type: 1, // 1-发货单\r\n        Data: '',\r\n        Base64Image: ''\r\n      },\r\n      logoUrl: require('@/assets/logo-inner.png'),\r\n      // 缩放\r\n      scaleValue: 1,\r\n      saveLoading: false,\r\n      scaleMax: 5,\r\n      scaleMin: 0.5\r\n    }\r\n  },\r\n  computed: {\r\n    filteredTmplList() {\r\n      return this.tmplList.filter(t => t.Name.indexOf(this.keyword) > -1)\r\n    }\r\n  },\r\n  mounted() {\r\n    this.init()\r\n    /**\r\n       * 这里必须要在 mounted 中去构建 左侧可拖拽元素 或者 设计器\r\n       * 因为都是把元素挂载到对应容器中, 必须要先找到该容器\r\n       */\r\n    this.buildLeftElement()\r\n    this.buildDesigner()\r\n  },\r\n  methods: {\r\n    changeScale(big) {\r\n      let scaleValue = this.scaleValue\r\n      if (big) {\r\n        scaleValue += 0.1\r\n        if (scaleValue > this.scaleMax) scaleValue = 5\r\n      } else {\r\n        scaleValue -= 0.1\r\n        if (scaleValue < this.scaleMin) scaleValue = 0.5\r\n      }\r\n      if (hiprintTemplate) {\r\n        // scaleValue: 放大缩小值, false: 不保存(不传也一样), 如果传 true, 打印时也会放大\r\n        hiprintTemplate.zoom(scaleValue)\r\n        this.scaleValue = scaleValue\r\n      }\r\n    },\r\n    async getImage() {\r\n      try {\r\n        return await new Promise((resolve, reject) => {\r\n          // 在浏览器空闲时执行截图操作\r\n          const executeCapture = async() => {\r\n            try {\r\n              const canvas = await html2canvas(document.getElementById('hiprint-printTemplate'), {\r\n                useCORS: true,\r\n                logging: false, // 关闭日志输出\r\n                removeContainer: true, // 自动清理临时容器\r\n                onclone: (clonedDoc) => {\r\n                  // 在克隆文档中移除不必要的元素\r\n                  const clonedElement = clonedDoc.getElementById('hiprint-printTemplate')\r\n                  if (clonedElement) {\r\n                    // 移除动画、视频等耗性能的元素\r\n                    const animations = clonedElement.querySelectorAll('[class*=\"animate\"]')\r\n                    animations.forEach(el => el.remove())\r\n                  }\r\n                }\r\n              })\r\n              const dataUrl = canvas.toDataURL('image/png')\r\n              resolve(dataUrl)\r\n            } catch (error) {\r\n              reject(error)\r\n            }\r\n          }\r\n\r\n          // 使用 requestIdleCallback 在浏览器空闲时执行\r\n          if (window.requestIdleCallback) {\r\n            window.requestIdleCallback(executeCapture, { timeout: 5000 })\r\n          } else {\r\n            setTimeout(executeCapture, 0)\r\n          }\r\n        })\r\n      } finally {\r\n      }\r\n    },\r\n    getLogo() {\r\n      GetCompany().then(res => {\r\n        this.logoUrl = res.Data.Icon\r\n      })\r\n    },\r\n    tmplSelect(id) {\r\n      this.toEdit = ''\r\n      this.activeIndex = id\r\n      if (this.form && this.form.Id === id) return\r\n      this.loadTemplate(id)\r\n    },\r\n    async cloneTemplate() {\r\n      const copyTemplate = JSON.parse(JSON.stringify(this.form))\r\n      delete copyTemplate.Id\r\n      await SavePrintTemplateEntity(copyTemplate).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message.success('复制成功')\r\n          this.getTemplateList()\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    deleteTemplate(id) {\r\n      this.$confirm('是否删除所选内容', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        center: true\r\n      })\r\n        .then(() => {\r\n          DeletePrintTemplate({ id }).then(res => {\r\n            if (res.IsSucceed) {\r\n              this.$message.success('删除成功')\r\n              this.getTemplateList()\r\n            } else {\r\n              this.$message.error(res.Message)\r\n            }\r\n          })\r\n          if (this.toEdit) {\r\n            this.toEdit = ''\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n    async init() {\r\n      // 初始化 provider\r\n      this.getLogo()\r\n      const provider = providers.find(i => i.value == this.mode)\r\n      hiprint.init({\r\n        providers: [provider.f]\r\n      })\r\n      this.getTemplateList()\r\n    },\r\n\r\n    /**\r\n       * 构建左侧可拖拽元素\r\n       * 注意: 可拖拽元素必须在 hiprint.init() 之后调用\r\n       * 而且 必须包含 class=\"ep-draggable-item\" 否则无法拖拽进设计器\r\n       */\r\n    buildLeftElement() {\r\n      hiprint.PrintElementTypeManager.buildByHtml($('.ep-draggable-item'))\r\n    },\r\n    buildDesigner(template = {}) {\r\n      // eslint-disable-next-line no-undef\r\n      $('#hiprint-printTemplate').empty() // 先清空, 避免重复构建\r\n      hiprintTemplate = new hiprint.PrintTemplate({\r\n        template,\r\n        settingContainer: '#PrintElementOptionSetting' // 元素参数容器\r\n      })\r\n      // 构建 并填充到 容器中\r\n      hiprintTemplate.design('#hiprint-printTemplate')\r\n    },\r\n    handlePrint() {\r\n      // 打印数据，key 对应 元素的 字段名\r\n      const printData = {}\r\n      const printArr = hiprintTemplate.getJson().panels[0].printElements\r\n      printArr.forEach(item => {\r\n        if (item.options.field == 'Table') {\r\n          printData[item.options.field] = JSON.parse(item.options.testData)\r\n        } else {\r\n          console.log(item)\r\n          printData[item.options.field] = item.options.testData || item.options\r\n        }\r\n      })\r\n      console.log(printData)\r\n\r\n      // let printData = hiprintTemplate.getJson()\r\n      // 参数: 打印时设置 左偏移量，上偏移量\r\n      const options = { leftOffset: -1, topOffset: -1 }\r\n      // 扩展\r\n      const ext = {\r\n        callback: () => {\r\n          console.log('浏览器打印窗口已打开')\r\n        }\r\n        // styleHandler: () => {\r\n        //   // 重写 文本 打印样式\r\n        //   return \"<style>.hiprint-printElement-text{color:red !important;}</style>\";\r\n        // }\r\n      }\r\n      // 调用浏览器打印\r\n      hiprintTemplate.print(printData, options, ext)\r\n    },\r\n    changePaper() {\r\n      const temp = this.paperTypes.find(i => i.type === this.curPaperType)\r\n      if (this.curPaperType === '自定义纸张') {\r\n        hiprintTemplate.setPaper(this.paperWidth, this.paperHeight)\r\n      } else {\r\n        hiprintTemplate.setPaper(temp.width, temp.height)\r\n      }\r\n    },\r\n    // 新建模板\r\n    createTemplate() {\r\n      this.form = {\r\n        Name: '',\r\n        Type: 1, // 1-发货单\r\n        Data: '',\r\n        Base64Image: ''\r\n      }\r\n      this.clearTemplate()\r\n    },\r\n    // 保存模板\r\n    async saveTemplate() {\r\n      this.saveLoading = true\r\n      try {\r\n        this.form.Base64Image = await this.getImage()\r\n        if (!this.form.Name) {\r\n          this.$message.error('请输入模板名称')\r\n          return\r\n        }\r\n        const json = hiprintTemplate.getJson()\r\n        this.form.Data = JSON.stringify(json)\r\n        const res = await SavePrintTemplateEntity(this.form)\r\n        if (res.IsSucceed) {\r\n          this.$message.success('保存成功')\r\n          this.getTemplateList()\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('保存失败')\r\n        console.error('保存模板失败:', error)\r\n      } finally {\r\n        this.saveLoading = false\r\n      }\r\n    },\r\n    // 加载模板\r\n    async loadTemplate(id) {\r\n      this.clearTemplate()\r\n      const res = await GetPrintTemplateEntity({ id })\r\n      this.form = res.Data\r\n      const parseData = JSON.parse(res.Data.Data)\r\n      try {\r\n        const index = parseData.panels[0].printElements.findIndex(i => i.options.field === 'Logo')\r\n        parseData.panels[0].printElements[index].options.src = this.logoUrl\r\n      } catch (e) {}\r\n      console.log()\r\n      const template = parseData\r\n      this.buildDesigner(template)\r\n\r\n      // 匹配纸张\r\n      const { width, height } = template.panels[0]\r\n      const matchedPaper = this.paperTypes.find(i => i.width == width & i.height == height)\r\n      if (matchedPaper) {\r\n        this.curPaper = matchedPaper\r\n      } else {\r\n        this.curPaper = {\r\n          type: '自定义纸张',\r\n          width,\r\n          height\r\n        }\r\n      }\r\n      this.curPaperType = this.curPaper.type\r\n      this.paperWidth = width\r\n      this.paperHeight = height\r\n      this.changePaper()\r\n    },\r\n    // 清空模板\r\n    clearTemplate() {\r\n      $('#hiprint-printTemplate').empty() // 先清空, 避免重复构建\r\n      this.buildDesigner()\r\n    },\r\n\r\n    async getTemplateList() {\r\n      const res = await GetPrintTemplateList({\r\n        type: this.mode\r\n      })\r\n      this.tmplList = res.Data\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .title{\r\n    min-width: 100%;\r\n    padding-left: 8px;\r\n    font-size: 18px;\r\n    margin-top: 20px;\r\n  }\r\n  ::-webkit-scrollbar {\r\n    width: 0px;\r\n    height: 8px;\r\n  }\r\n  ::-webkit-scrollbar-thumb {\r\n    border-radius: 4px;\r\n    // box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);\r\n    background: #ddd;\r\n  }\r\n  ::-webkit-scrollbar-track {\r\n    box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.1);\r\n    border-radius: 4px;\r\n    background: #ededed;\r\n  }\r\n  .item {\r\n    color: rgba(34,40,52,0.65);\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    margin: 10px 10px;\r\n    width: 112px;\r\n    height: 44px;\r\n    border-radius: 4px 4px 4px 4px;\r\n    border: 1px dashed #D0D3DB;\r\n    font-size: 14px;\r\n  }\r\n  .label{\r\n    margin: 0 5px 0 10px;\r\n  }\r\n  /*::v-deep{*/\r\n  /*    .hiprint-option-item-settingBtn{*/\r\n  /*        background: #0ba1f8;*/\r\n  /*    }*/\r\n  /*}*/\r\n  .header{\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    row-gap: 10px;\r\n    align-items: center;\r\n    margin-bottom: 10px;\r\n    color: rgba(34, 40, 52, 0.85);\r\n    font-size: 12px;\r\n    .input{\r\n      width: 100px;\r\n    }\r\n    .zoom{\r\n      margin: 0 10px;\r\n      border: 1px solid #D0D3DB;\r\n      border-radius: 4px;\r\n      width: 100px;\r\n      height: 32px;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      font-size: 14px;\r\n    }\r\n    .zoom-btn{\r\n      padding: 5px;\r\n      font-size: 19px;\r\n    }\r\n  }\r\n  ::v-deep{\r\n    .hinnn-layout-sider{\r\n      *{\r\n        color: rgba(34, 40, 52, 0.85);\r\n      }\r\n      input[placeholder=\"请输入图片地址\"]  {\r\n        width: 100%!important;\r\n        & + button{\r\n          display: none;\r\n        }\r\n      }\r\n      input,textarea,select{\r\n        border-radius: 4px!important;\r\n        border: 1px solid #D0D3DB!important;\r\n      }\r\n      input,select{\r\n        height: 32px!important;\r\n        line-height: 32px;\r\n      }\r\n      .hiprint-option-item-settingBtn{\r\n        background-color: #298DFF;\r\n        border-radius: 4px;\r\n        height: 30px;\r\n        color: #ffffff;\r\n        cursor: pointer;\r\n      }\r\n      .hiprint-option-item-deleteBtn{\r\n        background-color: #FB6B7F;\r\n      }\r\n    }\r\n\r\n  }\r\n  .tmpl-list {\r\n    margin-top: 12px;\r\n    overflow-y: auto;\r\n    min-height: 130px;\r\n    .tmpl-menu {\r\n      border-right: none;\r\n      .el-menu-item {\r\n        height: 32px;\r\n        line-height: 32px;\r\n        .el-link {\r\n          position: absolute;\r\n          top: 20%;\r\n          right: 12px;\r\n          margin-top: -7px;\r\n          transition: transform 0.3s;\r\n          &:last-child {\r\n            right: 36px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n</style>\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4LA,SAAAA,OAAA,EAAAC,aAAA;AACA,OAAAC,SAAA;AACA,SACAC,mBAAA,EACAC,sBAAA,EACAC,oBAAA,EACAC,uBAAA,QACA;AACA,SAAAC,UAAA;AACA,SAAAC,yBAAA;AACA,OAAAC,WAAA;AACA,SAAAC,UAAA;AACA,SAAAC,SAAA;AAEAV,aAAA,CAAAW,cAAA;AAEA,IAAAC,eAAA;AACA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,QAAA;QACAC,IAAA;QACAC,KAAA;QACAC,MAAA;MACA;MACAC,YAAA;MACA;MACAb,UAAA,EAAAI,SAAA,CAAAJ,UAAA;MACA;MACAc,UAAA;MACAC,WAAA;MACAC,QAAA;MACAC,IAAA;MACAC,WAAA;MACAC,OAAA;MACAC,MAAA;MACAC,IAAA;QACAC,IAAA;QACAC,IAAA;QAAA;QACAC,IAAA;QACAC,WAAA;MACA;MACAC,OAAA,EAAAC,OAAA;MACA;MACAC,UAAA;MACAC,WAAA;MACAC,QAAA;MACAC,QAAA;IACA;EACA;EACAC,QAAA;IACAC,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,KAAA;MACA,YAAAlB,QAAA,CAAAmB,MAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAd,IAAA,CAAAe,OAAA,CAAAH,KAAA,CAAAf,OAAA;MAAA;IACA;EACA;EACAmB,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;IACA;AACA;AACA;AACA;IACA,KAAAC,gBAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAAC,GAAA;MACA,IAAAhB,UAAA,QAAAA,UAAA;MACA,IAAAgB,GAAA;QACAhB,UAAA;QACA,IAAAA,UAAA,QAAAE,QAAA,EAAAF,UAAA;MACA;QACAA,UAAA;QACA,IAAAA,UAAA,QAAAG,QAAA,EAAAH,UAAA;MACA;MACA,IAAAtB,eAAA;QACA;QACAA,eAAA,CAAAuC,IAAA,CAAAjB,UAAA;QACA,KAAAA,UAAA,GAAAA,UAAA;MACA;IACA;IACAkB,QAAA,WAAAA,SAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,SAAA;QAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAC,IAAA,GAAAD,SAAA,CAAAE,IAAA;YAAA;cAAAF,SAAA,CAAAC,IAAA;cAAAD,SAAA,CAAAE,IAAA;cAAA,OAEA,IAAAC,OAAA,WAAAC,OAAA,EAAAC,MAAA;gBACA;gBACA,IAAAC,cAAA;kBAAA,IAAAC,IAAA,GAAAb,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAY,QAAA;oBAAA,IAAAC,MAAA,EAAAC,OAAA;oBAAA,OAAAf,mBAAA,GAAAG,IAAA,UAAAa,SAAAC,QAAA;sBAAA,kBAAAA,QAAA,CAAAX,IAAA,GAAAW,QAAA,CAAAV,IAAA;wBAAA;0BAAAU,QAAA,CAAAX,IAAA;0BAAAW,QAAA,CAAAV,IAAA;0BAAA,OAEArD,WAAA,CAAAgE,QAAA,CAAAC,cAAA;4BACAC,OAAA;4BACAC,OAAA;4BAAA;4BACAC,eAAA;4BAAA;4BACAC,OAAA,WAAAA,QAAAC,SAAA;8BACA;8BACA,IAAAC,aAAA,GAAAD,SAAA,CAAAL,cAAA;8BACA,IAAAM,aAAA;gCACA;gCACA,IAAAC,UAAA,GAAAD,aAAA,CAAAE,gBAAA;gCACAD,UAAA,CAAAE,OAAA,WAAAC,EAAA;kCAAA,OAAAA,EAAA,CAAAC,MAAA;gCAAA;8BACA;4BACA;0BACA;wBAAA;0BAbAhB,MAAA,GAAAG,QAAA,CAAAc,IAAA;0BAcAhB,OAAA,GAAAD,MAAA,CAAAkB,SAAA;0BACAvB,OAAA,CAAAM,OAAA;0BAAAE,QAAA,CAAAV,IAAA;0BAAA;wBAAA;0BAAAU,QAAA,CAAAX,IAAA;0BAAAW,QAAA,CAAAgB,EAAA,GAAAhB,QAAA;0BAEAP,MAAA,CAAAO,QAAA,CAAAgB,EAAA;wBAAA;wBAAA;0BAAA,OAAAhB,QAAA,CAAAiB,IAAA;sBAAA;oBAAA,GAAArB,OAAA;kBAAA,CAEA;kBAAA,gBArBAF,eAAA;oBAAA,OAAAC,IAAA,CAAAuB,KAAA,OAAAC,SAAA;kBAAA;gBAAA,GAqBA;;gBAEA;gBACA,IAAAC,MAAA,CAAAC,mBAAA;kBACAD,MAAA,CAAAC,mBAAA,CAAA3B,cAAA;oBAAA4B,OAAA;kBAAA;gBACA;kBACAC,UAAA,CAAA7B,cAAA;gBACA;cACA;YAAA;cAAA,OAAAN,SAAA,CAAAoC,MAAA,WAAApC,SAAA,CAAA0B,IAAA;YAAA;cAAA1B,SAAA,CAAAC,IAAA;cAAA,OAAAD,SAAA,CAAAqC,MAAA;YAAA;YAAA;cAAA,OAAArC,SAAA,CAAA6B,IAAA;UAAA;QAAA,GAAAhC,QAAA;MAAA;IAGA;IACAyC,OAAA,WAAAA,QAAA;MAAA,IAAAC,MAAA;MACAzF,UAAA,GAAA0F,IAAA,WAAAC,GAAA;QACAF,MAAA,CAAAlE,OAAA,GAAAoE,GAAA,CAAAtE,IAAA,CAAAuE,IAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAC,EAAA;MACA,KAAA7E,MAAA;MACA,KAAAF,WAAA,GAAA+E,EAAA;MACA,SAAA5E,IAAA,SAAAA,IAAA,CAAA6E,EAAA,KAAAD,EAAA;MACA,KAAAE,YAAA,CAAAF,EAAA;IACA;IACAG,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MAAA,OAAAtD,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAqD,SAAA;QAAA,IAAAC,YAAA;QAAA,OAAAvD,mBAAA,GAAAG,IAAA,UAAAqD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnD,IAAA,GAAAmD,SAAA,CAAAlD,IAAA;YAAA;cACAgD,YAAA,GAAAG,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAP,MAAA,CAAAhF,IAAA;cACA,OAAAkF,YAAA,CAAAL,EAAA;cAAAO,SAAA,CAAAlD,IAAA;cAAA,OACAxD,uBAAA,CAAAwG,YAAA,EAAAV,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAe,SAAA;kBACAR,MAAA,CAAAS,QAAA,CAAAC,OAAA;kBACAV,MAAA,CAAAW,eAAA;gBACA;kBACAX,MAAA,CAAAS,QAAA,CAAAG,KAAA,CAAAnB,GAAA,CAAAoB,OAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAT,SAAA,CAAAvB,IAAA;UAAA;QAAA,GAAAoB,QAAA;MAAA;IACA;IACAa,cAAA,WAAAA,eAAAlB,EAAA;MAAA,IAAAmB,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,MAAA;MACA,GACA3B,IAAA;QACAjG,mBAAA;UAAAqG,EAAA,EAAAA;QAAA,GAAAJ,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAe,SAAA;YACAO,MAAA,CAAAN,QAAA,CAAAC,OAAA;YACAK,MAAA,CAAAJ,eAAA;UACA;YACAI,MAAA,CAAAN,QAAA,CAAAG,KAAA,CAAAnB,GAAA,CAAAoB,OAAA;UACA;QACA;QACA,IAAAE,MAAA,CAAAhG,MAAA;UACAgG,MAAA,CAAAhG,MAAA;QACA;MACA,GACAqG,KAAA;QACAL,MAAA,CAAAN,QAAA;UACApG,IAAA;UACAgH,OAAA;QACA;MACA;IACA;IACAnF,IAAA,WAAAA,KAAA;MAAA,IAAAoF,MAAA;MAAA,OAAA5E,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA2E,SAAA;QAAA,IAAAC,QAAA;QAAA,OAAA7E,mBAAA,GAAAG,IAAA,UAAA2E,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzE,IAAA,GAAAyE,SAAA,CAAAxE,IAAA;YAAA;cACA;cACAoE,MAAA,CAAAhC,OAAA;cACAkC,QAAA,GAAAlI,SAAA,CAAAqI,IAAA,WAAAC,CAAA;gBAAA,OAAAA,CAAA,CAAAC,KAAA,IAAAP,MAAA,CAAA1G,IAAA;cAAA;cACAxB,OAAA,CAAA8C,IAAA;gBACA5C,SAAA,GAAAkI,QAAA,CAAAM,CAAA;cACA;cACAR,MAAA,CAAAX,eAAA;YAAA;YAAA;cAAA,OAAAe,SAAA,CAAA7C,IAAA;UAAA;QAAA,GAAA0C,QAAA;MAAA;IACA;IAEA;AACA;AACA;AACA;AACA;IACApF,gBAAA,WAAAA,iBAAA;MACA/C,OAAA,CAAA2I,uBAAA,CAAAC,WAAA,CAAAC,CAAA;IACA;IACA7F,aAAA,WAAAA,cAAA;MAAA,IAAA8F,QAAA,GAAAnD,SAAA,CAAAoD,MAAA,QAAApD,SAAA,QAAAqD,SAAA,GAAArD,SAAA;MACA;MACAkD,CAAA,2BAAAI,KAAA;MACApI,eAAA,OAAAb,OAAA,CAAAkJ,aAAA;QACAJ,QAAA,EAAAA,QAAA;QACAK,gBAAA;MACA;MACA;MACAtI,eAAA,CAAAuI,MAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA;MACA,IAAAC,SAAA;MACA,IAAAC,QAAA,GAAA1I,eAAA,CAAA2I,OAAA,GAAAC,MAAA,IAAAC,aAAA;MACAH,QAAA,CAAApE,OAAA,WAAAwE,IAAA;QACA,IAAAA,IAAA,CAAAC,OAAA,CAAAC,KAAA;UACAP,SAAA,CAAAK,IAAA,CAAAC,OAAA,CAAAC,KAAA,IAAA5C,IAAA,CAAAC,KAAA,CAAAyC,IAAA,CAAAC,OAAA,CAAAE,QAAA;QACA;UACAC,OAAA,CAAAC,GAAA,CAAAL,IAAA;UACAL,SAAA,CAAAK,IAAA,CAAAC,OAAA,CAAAC,KAAA,IAAAF,IAAA,CAAAC,OAAA,CAAAE,QAAA,IAAAH,IAAA,CAAAC,OAAA;QACA;MACA;MACAG,OAAA,CAAAC,GAAA,CAAAV,SAAA;;MAEA;MACA;MACA,IAAAM,OAAA;QAAAK,UAAA;QAAAC,SAAA;MAAA;MACA;MACA,IAAAC,GAAA;QACAC,QAAA,WAAAA,SAAA;UACAL,OAAA,CAAAC,GAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;MACAnJ,eAAA,CAAAwJ,KAAA,CAAAf,SAAA,EAAAM,OAAA,EAAAO,GAAA;IACA;IACAG,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,IAAA,QAAAjK,UAAA,CAAAgI,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAvH,IAAA,KAAAsJ,MAAA,CAAAnJ,YAAA;MAAA;MACA,SAAAA,YAAA;QACAP,eAAA,CAAA4J,QAAA,MAAApJ,UAAA,OAAAC,WAAA;MACA;QACAT,eAAA,CAAA4J,QAAA,CAAAD,IAAA,CAAAtJ,KAAA,EAAAsJ,IAAA,CAAArJ,MAAA;MACA;IACA;IACA;IACAuJ,cAAA,WAAAA,eAAA;MACA,KAAA9I,IAAA;QACAC,IAAA;QACAC,IAAA;QAAA;QACAC,IAAA;QACAC,WAAA;MACA;MACA,KAAA2I,aAAA;IACA;IACA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MAAA,OAAAvH,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAsH,SAAA;QAAA,IAAAC,IAAA,EAAA1E,GAAA;QAAA,OAAA9C,mBAAA,GAAAG,IAAA,UAAAsH,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApH,IAAA,GAAAoH,SAAA,CAAAnH,IAAA;YAAA;cACA+G,MAAA,CAAAzI,WAAA;cAAA6I,SAAA,CAAApH,IAAA;cAAAoH,SAAA,CAAAnH,IAAA;cAAA,OAEA+G,MAAA,CAAAxH,QAAA;YAAA;cAAAwH,MAAA,CAAAjJ,IAAA,CAAAI,WAAA,GAAAiJ,SAAA,CAAA3F,IAAA;cAAA,IACAuF,MAAA,CAAAjJ,IAAA,CAAAC,IAAA;gBAAAoJ,SAAA,CAAAnH,IAAA;gBAAA;cAAA;cACA+G,MAAA,CAAAxD,QAAA,CAAAG,KAAA;cAAA,OAAAyD,SAAA,CAAAjF,MAAA;YAAA;cAGA+E,IAAA,GAAAlK,eAAA,CAAA2I,OAAA;cACAqB,MAAA,CAAAjJ,IAAA,CAAAG,IAAA,GAAAkF,IAAA,CAAAE,SAAA,CAAA4D,IAAA;cAAAE,SAAA,CAAAnH,IAAA;cAAA,OACAxD,uBAAA,CAAAuK,MAAA,CAAAjJ,IAAA;YAAA;cAAAyE,GAAA,GAAA4E,SAAA,CAAA3F,IAAA;cACA,IAAAe,GAAA,CAAAe,SAAA;gBACAyD,MAAA,CAAAxD,QAAA,CAAAC,OAAA;gBACAuD,MAAA,CAAAtD,eAAA;cACA;gBACAsD,MAAA,CAAAxD,QAAA,CAAAG,KAAA,CAAAnB,GAAA,CAAAoB,OAAA;cACA;cAAAwD,SAAA,CAAAnH,IAAA;cAAA;YAAA;cAAAmH,SAAA,CAAApH,IAAA;cAAAoH,SAAA,CAAAzF,EAAA,GAAAyF,SAAA;cAEAJ,MAAA,CAAAxD,QAAA,CAAAG,KAAA;cACAuC,OAAA,CAAAvC,KAAA,YAAAyD,SAAA,CAAAzF,EAAA;YAAA;cAAAyF,SAAA,CAAApH,IAAA;cAEAgH,MAAA,CAAAzI,WAAA;cAAA,OAAA6I,SAAA,CAAAhF,MAAA;YAAA;YAAA;cAAA,OAAAgF,SAAA,CAAAxF,IAAA;UAAA;QAAA,GAAAqF,QAAA;MAAA;IAEA;IACA;IACApE,YAAA,WAAAA,aAAAF,EAAA;MAAA,IAAA0E,MAAA;MAAA,OAAA5H,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA2H,SAAA;QAAA,IAAA9E,GAAA,EAAA+E,SAAA,EAAAC,KAAA,EAAAvC,QAAA,EAAAwC,iBAAA,EAAApK,KAAA,EAAAC,MAAA,EAAAoK,YAAA;QAAA,OAAAhI,mBAAA,GAAAG,IAAA,UAAA8H,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5H,IAAA,GAAA4H,SAAA,CAAA3H,IAAA;YAAA;cACAoH,MAAA,CAAAP,aAAA;cAAAc,SAAA,CAAA3H,IAAA;cAAA,OACA1D,sBAAA;gBAAAoG,EAAA,EAAAA;cAAA;YAAA;cAAAH,GAAA,GAAAoF,SAAA,CAAAnG,IAAA;cACA4F,MAAA,CAAAtJ,IAAA,GAAAyE,GAAA,CAAAtE,IAAA;cACAqJ,SAAA,GAAAnE,IAAA,CAAAC,KAAA,CAAAb,GAAA,CAAAtE,IAAA,CAAAA,IAAA;cACA;gBACAsJ,KAAA,GAAAD,SAAA,CAAA3B,MAAA,IAAAC,aAAA,CAAAgC,SAAA,WAAAlD,CAAA;kBAAA,OAAAA,CAAA,CAAAoB,OAAA,CAAAC,KAAA;gBAAA;gBACAuB,SAAA,CAAA3B,MAAA,IAAAC,aAAA,CAAA2B,KAAA,EAAAzB,OAAA,CAAA+B,GAAA,GAAAT,MAAA,CAAAjJ,OAAA;cACA,SAAA2J,CAAA;cACA7B,OAAA,CAAAC,GAAA;cACAlB,QAAA,GAAAsC,SAAA;cACAF,MAAA,CAAAlI,aAAA,CAAA8F,QAAA;;cAEA;cAAAwC,iBAAA,GACAxC,QAAA,CAAAW,MAAA,KAAAvI,KAAA,GAAAoK,iBAAA,CAAApK,KAAA,EAAAC,MAAA,GAAAmK,iBAAA,CAAAnK,MAAA;cACAoK,YAAA,GAAAL,MAAA,CAAA3K,UAAA,CAAAgI,IAAA,WAAAC,CAAA;gBAAA,OAAAA,CAAA,CAAAtH,KAAA,IAAAA,KAAA,GAAAsH,CAAA,CAAArH,MAAA,IAAAA,MAAA;cAAA;cACA,IAAAoK,YAAA;gBACAL,MAAA,CAAAlK,QAAA,GAAAuK,YAAA;cACA;gBACAL,MAAA,CAAAlK,QAAA;kBACAC,IAAA;kBACAC,KAAA,EAAAA,KAAA;kBACAC,MAAA,EAAAA;gBACA;cACA;cACA+J,MAAA,CAAA9J,YAAA,GAAA8J,MAAA,CAAAlK,QAAA,CAAAC,IAAA;cACAiK,MAAA,CAAA7J,UAAA,GAAAH,KAAA;cACAgK,MAAA,CAAA5J,WAAA,GAAAH,MAAA;cACA+J,MAAA,CAAAZ,WAAA;YAAA;YAAA;cAAA,OAAAmB,SAAA,CAAAhG,IAAA;UAAA;QAAA,GAAA0F,QAAA;MAAA;IACA;IACA;IACAR,aAAA,WAAAA,cAAA;MACA9B,CAAA,2BAAAI,KAAA;MACA,KAAAjG,aAAA;IACA;IAEAuE,eAAA,WAAAA,gBAAA;MAAA,IAAAsE,MAAA;MAAA,OAAAvI,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAsI,SAAA;QAAA,IAAAzF,GAAA;QAAA,OAAA9C,mBAAA,GAAAG,IAAA,UAAAqI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnI,IAAA,GAAAmI,SAAA,CAAAlI,IAAA;YAAA;cAAAkI,SAAA,CAAAlI,IAAA;cAAA,OACAzD,oBAAA;gBACAY,IAAA,EAAA4K,MAAA,CAAArK;cACA;YAAA;cAFA6E,GAAA,GAAA2F,SAAA,CAAA1G,IAAA;cAGAuG,MAAA,CAAAtK,QAAA,GAAA8E,GAAA,CAAAtE,IAAA;YAAA;YAAA;cAAA,OAAAiK,SAAA,CAAAvG,IAAA;UAAA;QAAA,GAAAqG,QAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue", "mtime": 1757559912598}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["PartTakeConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "PartTakeConfig.vue", "sourceRoot": "src/views/PRO/process-settings/management/component", "sourcesContent": ["<template>\n  <div class=\"form-wrapper\">\n    <div class=\"form-recognition-tabs\">\n      <el-tabs v-model=\"bomActiveName\">\n        <el-tab-pane v-for=\"(item, index) in bomList\" :key=\"index\" :label=\"item.Display_Name\" :name=\"item.Code\" />\n      </el-tabs>\n    </div>\n    <div class=\"form-content\">\n      <div style=\"display: flex; margin-bottom: 16px;\">\n        <div style=\"width: 120px;\" />\n        <div style=\"flex: 1; display: flex; font-size: 16px; color: #333333;\">\n          <div style=\"flex: 1; text-align: center;\">构件</div>\n          <div style=\"flex: 1; text-align: center;\">半成品</div>\n          <div style=\"flex: 1; text-align: center;\">成品</div>\n        </div>\n      </div>\n      <div>\n        <div v-for=\"(item, index) in filteredList\" :key=\"index\" style=\"display: flex;  justify-content: center; align-items: center; margin-bottom: 16px;\">\n          <div style=\"width: 120px; font-size: 14px; color: #333333; text-align: right; padding-right: 10px;\">{{ item.Part_Type_Name }}</div>\n          <div style=\"flex: 1; display: flex;\">\n            <div style=\"flex: 1; text-align: center;\">\n              <el-select v-model=\"item.Working_Process_Id\" clearable>\n                <el-option v-for=\"op in selectList\" :key=\"op.Id\" :label=\"op.Name\" :value=\"op.Id\" />\n              </el-select>\n            </div>\n            <div style=\"flex: 1; text-align: center;\">\n              <el-select v-model=\"item.Working_Process_Id\" clearable>\n                <el-option v-for=\"op in selectList\" :key=\"op.Id\" :label=\"op.Name\" :value=\"op.Id\" />\n              </el-select>\n            </div>\n            <div style=\"flex: 1; text-align: center;\">\n              <el-select v-model=\"item.Working_Process_Id\" clearable>\n                <el-option v-for=\"op in selectList\" :key=\"op.Id\" :label=\"op.Name\" :value=\"op.Id\" />\n              </el-select>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div class=\"form-footer\">\n      <el-button @click=\"$emit('close')\">取 消</el-button>\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport { GetConsumingProcessAllList, SaveConsumingProcessAllList } from '@/api/PRO/partType'\nimport { GetProcessList } from '@/api/PRO/technology-lib'\n\nexport default {\n  data() {\n    return {\n      list: [],\n      btnLoading: false,\n      selectList: [],\n      bomList: [],\n      comName: '',\n      partName: '',\n      bomActiveName: '',\n      parentBomList: []\n    }\n  },\n  computed: {\n    // 根据当前选中的BOM层级过滤数据\n    filteredList() {\n      if (!this.list || !this.bomActiveName) {\n        return []\n      }\n      return this.list.filter(item => item.Use_Bom_Level.toString() === this.bomActiveName)\n    }\n  },\n  async mounted() {\n    await this.getBom()\n    await this.getProcessList()\n  },\n  methods: {\n    async getBom() {\n      const { comName, partName, list } = await GetBOMInfo()\n      this.comName = comName\n      this.partName = partName\n      this.bomList = list.filter(i => i.Code !== '-1')\n      this.bomActiveName = this.bomList[0].Code\n    },\n    getTypeList() {\n      GetConsumingProcessAllList({}).then(res => {\n        if (res.IsSucceed) {\n          this.list = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    getProcessList() {\n      GetProcessList({ type: 1 }).then(res => {\n        this.selectList = res.Data\n      }).finally(() => {\n        this.getTypeList()\n      })\n    },\n    handleSubmit() {\n      this.btnLoading = true\n      SaveConsumingProcessAllList(this.list.filter(i => i.Working_Process_Id)).then(res => {\n        if (res.IsSucceed) {\n          this.$message.success('保存成功')\n          this.$emit('close')\n        } else {\n          this.$message.error(res.Message)\n        }\n      }).finally(() => {\n        this.btnLoading = false\n      })\n    },\n    // 获取当前bom层级的所有父级bom层级信息\n    getParentBom(code) {\n      this.parentBomList = this.bomList\n    },\n    mainBlur() {\n\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"~@/styles/mixin.scss\";\n.form-wrapper {\n  height: 70vh;\n  display: flex;\n  flex-direction: column;\n\n  .form-content {\n    flex: 1;\n    overflow: auto;\n    padding-right: 16px;\n    @include scrollBar;\n\n    .form-x {\n      padding-bottom: 20px;\n    }\n  }\n\n  .form-footer {\n    text-align: right;\n    flex-shrink: 0;\n    padding-top: 16px;\n    background: #fff;\n  }\n}\n</style>\n"]}]}
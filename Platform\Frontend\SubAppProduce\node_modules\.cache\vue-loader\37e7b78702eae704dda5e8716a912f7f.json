{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue", "mtime": 1757468113428}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQoNCmltcG9ydCB7IEdldENvbnN1bWluZ1Byb2Nlc3NBbGxMaXN0LCBTYXZlQ29uc3VtaW5nUHJvY2Vzc0FsbExpc3QgfSBmcm9tICdAL2FwaS9QUk8vcGFydFR5cGUnDQppbXBvcnQgeyBHZXRQcm9jZXNzTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy90ZWNobm9sb2d5LWxpYicNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBsaXN0OiBbXSwNCiAgICAgIGJ0bkxvYWRpbmc6IGZhbHNlLA0KICAgICAgc2VsZWN0TGlzdDogW10NCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5nZXRQcm9jZXNzTGlzdCgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBnZXRUeXBlTGlzdCgpIHsNCiAgICAgIEdldENvbnN1bWluZ1Byb2Nlc3NBbGxMaXN0KHt9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy5saXN0ID0gcmVzLkRhdGENCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBnZXRQcm9jZXNzTGlzdCgpIHsNCiAgICAgIEdldFByb2Nlc3NMaXN0KHsgdHlwZTogMSB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIHRoaXMuc2VsZWN0TGlzdCA9IHJlcy5EYXRhDQogICAgICB9KS5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRUeXBlTGlzdCgpDQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlU3VibWl0KCkgew0KICAgICAgdGhpcy5idG5Mb2FkaW5nID0gdHJ1ZQ0KICAgICAgU2F2ZUNvbnN1bWluZ1Byb2Nlc3NBbGxMaXN0KHRoaXMubGlzdC5maWx0ZXIoaSA9PiBpLldvcmtpbmdfUHJvY2Vzc19JZCkpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S/neWtmOaIkOWKnycpDQogICAgICAgICAgdGhpcy4kZW1pdCgnY2xvc2UnKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLk1lc3NhZ2UpDQogICAgICAgIH0NCiAgICAgIH0pLmZpbmFsbHkoKCkgPT4gew0KICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIG1haW5CbHVyKGUpIHsNCg0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["PartTakeConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAoBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "PartTakeConfig.vue", "sourceRoot": "src/views/PRO/process-settings/management/component", "sourcesContent": ["<template>\r\n  <div class=\"form-wrapper\">\r\n    <div class=\"form-content\">\r\n      <el-form ref=\"form\" label-width=\"120px\" class=\"form-x\">\r\n        <el-form-item v-for=\"(item,index) in list\" :key=\"index\" :show-message=\"false\" :label=\"item.Part_Type_Name\" prop=\"mainPart\">\r\n          <el-select v-model=\"item.Working_Process_Id\" clearable>\r\n            <el-option v-for=\"op in selectList\" :key=\"op.Id\" :label=\"op.Name\" :value=\"op.Id\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n    <div class=\"form-footer\">\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { GetConsumingProcessAllList, SaveConsumingProcessAllList } from '@/api/PRO/partType'\r\nimport { GetProcessList } from '@/api/PRO/technology-lib'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      list: [],\r\n      btnLoading: false,\r\n      selectList: []\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getProcessList()\r\n  },\r\n  methods: {\r\n    getTypeList() {\r\n      GetConsumingProcessAllList({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.list = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getProcessList() {\r\n      GetProcessList({ type: 1 }).then(res => {\r\n        this.selectList = res.Data\r\n      }).finally(() => {\r\n        this.getTypeList()\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      this.btnLoading = true\r\n      SaveConsumingProcessAllList(this.list.filter(i => i.Working_Process_Id)).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message.success('保存成功')\r\n          this.$emit('close')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      }).finally(() => {\r\n        this.btnLoading = false\r\n      })\r\n    },\r\n    mainBlur(e) {\r\n\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"~@/styles/mixin.scss\";\r\n.form-wrapper {\r\n  height: 70vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .form-content {\r\n    flex: 1;\r\n    overflow: auto;\r\n    padding-right: 16px;\r\n    @include scrollBar;\r\n\r\n    .form-x {\r\n      padding-bottom: 20px;\r\n    }\r\n  }\r\n\r\n  .form-footer {\r\n    text-align: right;\r\n    flex-shrink: 0;\r\n    padding-top: 16px;\r\n    background: #fff;\r\n  }\r\n}\r\n</style>\r\n"]}]}
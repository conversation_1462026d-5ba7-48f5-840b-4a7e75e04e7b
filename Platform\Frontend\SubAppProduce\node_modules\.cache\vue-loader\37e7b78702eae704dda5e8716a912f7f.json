{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue", "mtime": 1757561445799}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["PartTakeConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "PartTakeConfig.vue", "sourceRoot": "src/views/PRO/process-settings/management/component", "sourcesContent": ["<template>\n  <div class=\"form-wrapper\">\n    <div class=\"form-recognition-tabs\">\n      <el-tabs v-model=\"bomActiveName\" @tab-click=\"bomClick\">\n        <el-tab-pane v-for=\"(item, index) in tabBomList\" :key=\"index\" :label=\"item.Display_Name\" :name=\"item.Code\" />\n      </el-tabs>\n    </div>\n    <div class=\"form-content\">\n      <div style=\"display: flex; margin-bottom: 16px;\">\n        <div style=\"width: 120px;\" />\n        <div style=\"flex: 1; display: flex; justify-content: flex-start; font-size: 16px; color: #333333;\">\n          <div v-for=\"(item, index) in parentBomList\" :key=\"index\" style=\"width: 25%; text-align: center;\">{{ item.Display_Name }}</div>\n        </div>\n      </div>\n      <div>\n        <div v-for=\"(item, index) in filteredList\" :key=\"index\" style=\"display: flex;  justify-content: center; align-items: center; margin-bottom: 16px;\">\n          <div style=\"width: 120px; font-size: 14px; color: #333333; text-align: right; padding-right: 10px;\">{{ item.Part_Type_Name }}</div>\n          <div style=\"flex: 1; display: flex; justify-content: flex-start;\">\n            <div v-for=\"(item, index) in parentBomList\" :key=\"index\" style=\"flex: 1; text-align: center;\">\n              <el-select v-model=\"item.Working_Process_Id\" clearable>\n                <el-option v-for=\"op in selectList\" :key=\"op.Id\" :label=\"op.Name\" :value=\"op.Id\" />\n              </el-select>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div class=\"form-footer\">\n      <el-button @click=\"$emit('close')\">取 消</el-button>\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport { GetConsumingProcessAllList, SaveConsumingProcessAllList } from '@/api/PRO/partType'\nimport { GetProcessList } from '@/api/PRO/technology-lib'\n\nexport default {\n  data() {\n    return {\n      list: [],\n      btnLoading: false,\n      selectList: [],\n      bomList: [],\n      tabBomList: [],\n      comName: '',\n      partName: '',\n      bomActiveName: '',\n      parentBomList: []\n    }\n  },\n  computed: {\n    // 根据当前选中的BOM层级过滤数据\n    filteredList() {\n      if (!this.list || !this.bomActiveName) {\n        return []\n      }\n      return this.list.filter(item => item.Use_Bom_Level.toString() === this.bomActiveName)\n    }\n  },\n  async mounted() {\n    await this.getBom()\n    await this.getParentBom()\n    await this.getProcessList()\n  },\n  methods: {\n    async getBom() {\n      const { comName, partName, list } = await GetBOMInfo()\n      this.comName = comName\n      this.partName = partName\n      this.bomList = list\n      this.tabBomList = list.filter(i => i.Code !== '-1')\n      this.bomActiveName = this.tabBomList[0].Code\n    },\n    getTypeList() {\n      GetConsumingProcessAllList({}).then(res => {\n        if (res.IsSucceed) {\n          this.list = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    getProcessList() {\n      GetProcessList({ type: 1 }).then(res => {\n        this.selectList = res.Data\n      }).finally(() => {\n        this.getTypeList()\n      })\n    },\n    handleSubmit() {\n      this.btnLoading = true\n      SaveConsumingProcessAllList(this.list.filter(i => i.Working_Process_Id)).then(res => {\n        if (res.IsSucceed) {\n          this.$message.success('保存成功')\n          this.$emit('close')\n        } else {\n          this.$message.error(res.Message)\n        }\n      }).finally(() => {\n        this.btnLoading = false\n      })\n    },\n    bomClick() {\n      this.getParentBom()\n    },\n    // 获取当前bom层级的所有父级bom层级信息\n    getParentBom() {\n      // 找到当前code在bomList中的索引位置\n      console.log('this.bomList', this.bomList)\n      const currentIndex = this.bomList.findIndex(item => item.Code === this.bomActiveName)\n\n      console.log('bomActiveName', this.bomActiveName)\n      console.log('currentIndex', currentIndex)\n\n      // 如果找到了，则截取该索引之前的所有数据\n      if (currentIndex > 0) {\n        this.parentBomList = this.bomList.slice(0, currentIndex)\n      } else {\n        // 如果是第一个或者没找到，则返回空数组\n        this.parentBomList = []\n      }\n    },\n    mainBlur() {\n\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"~@/styles/mixin.scss\";\n.form-wrapper {\n  height: 70vh;\n  display: flex;\n  flex-direction: column;\n\n  .form-content {\n    flex: 1;\n    overflow: auto;\n    padding-right: 16px;\n    @include scrollBar;\n\n    .form-x {\n      padding-bottom: 20px;\n    }\n  }\n\n  .form-footer {\n    text-align: right;\n    flex-shrink: 0;\n    padding-top: 16px;\n    background: #fff;\n  }\n}\n</style>\n"]}]}
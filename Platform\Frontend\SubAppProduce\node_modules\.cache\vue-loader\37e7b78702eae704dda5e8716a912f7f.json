{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue", "mtime": 1757576966605}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["PartTakeConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PartTakeConfig.vue", "sourceRoot": "src/views/PRO/process-settings/management/component", "sourcesContent": ["<template>\n  <div class=\"form-wrapper\">\n    <div class=\"form-recognition-tabs\">\n      <el-tabs v-model=\"bomActiveName\" @tab-click=\"bomClick\">\n        <el-tab-pane v-for=\"(item, index) in tabBomList\" :key=\"index\" :label=\"item.Display_Name\" :name=\"item.Code\" />\n      </el-tabs>\n    </div>\n    <div class=\"form-content\">\n      <div class=\"can-process-title\">\n        <div style=\"width: 120px;\" />\n        <div class=\"can-process-list\">\n          <div v-for=\"(item, index) in parentBomList\" :key=\"index\" :style=\"{ width: (100 / parentBomList.length) + '%', textAlign: 'center' }\">{{ item.Display_Name }}</div>\n        </div>\n      </div>\n      <div class=\"can-process-box\">\n        <div v-if=\"!filteredList.length\" class=\"can-process-empty\">\n          暂无数据\n        </div>\n        <div v-for=\"(item, index) in filteredList\" :key=\"index\" class=\"can-process-item\">\n          <div class=\"can-process-type\" style=\"width: 120px;\">{{ item.Part_Type_Name }}</div>\n          <div class=\"can-process-bom\">\n            <div v-for=\"bom in parentBomList\" :key=\"`${item.Part_Type_Id || index}-${bom.Code}`\" class=\"can-process-select\" :style=\"{ width: (100 / parentBomList.length) + '%' }\">\n              <el-select\n                :key=\"`select-${item.Part_Type_Id || item.Part_Type_Name || index}-${bom.Code}`\"\n                :value=\"getProcessId(item, bom.Code)\"\n                clearable\n                style=\"width: 100%;\"\n                placeholder=\"请选择\"\n                @change=\"updateProcessId(item, bom.Code, $event)\"\n              >\n                <el-option v-for=\"op in getFilteredSelectList(bom.Code)\" :key=\"op.Id\" :label=\"op.Name\" :value=\"op.Id\" />\n              </el-select>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div class=\"form-footer\">\n      <el-button @click=\"$emit('close')\">取 消</el-button>\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport { GetConsumingAllList, SaveConsumingProcessAllList2 } from '@/api/PRO/partType'\nimport { GetProcessList } from '@/api/PRO/technology-lib'\n\nexport default {\n  data() {\n    return {\n      list: [],\n      btnLoading: false,\n      selectList: [],\n      bomList: [],\n      tabBomList: [],\n      comName: '',\n      partName: '',\n      bomActiveName: '',\n      parentBomList: []\n    }\n  },\n  computed: {\n    // 根据当前选中的BOM层级过滤数据\n    filteredList() {\n      if (!this.list || !this.bomActiveName) {\n        return []\n      }\n      return this.list.filter(item => item.Use_Bom_Level.toString() === this.bomActiveName)\n    }\n  },\n  async mounted() {\n    await this.getBom()\n    await this.getParentBom()\n    await this.getProcessList()\n  },\n  methods: {\n    async getBom() {\n      const { comName, partName, list } = await GetBOMInfo()\n      this.comName = comName\n      this.partName = partName\n      this.bomList = list\n      this.tabBomList = list.filter(i => i.Code !== '-1')\n      this.bomActiveName = this.tabBomList[this.tabBomList.length - 1].Code\n    },\n    getTypeList() {\n      GetConsumingAllList({}).then(res => {\n        if (res.IsSucceed) {\n          const resData = res.Data\n          const Process_List_All = []\n          this.parentBomList.map(item => {\n            const Process_List_Json = {}\n            Process_List_Json.Bom_Level = item.Code\n            Process_List_Json.Working_Process_Id = ''\n            Process_List_Json.Working_Process_Code = ''\n            Process_List_Json.Working_Process_Name = ''\n            Process_List_All.push(Process_List_Json)\n          })\n\n          // 遍历 resData 中的每个项目，为每个项目的 Process_List 添加缺失的数据\n          resData.forEach(dataItem => {\n            // 确保每个项目的 Process_List 存在\n            if (!dataItem.Process_List) {\n              dataItem.Process_List = []\n            }\n\n            // 如果 Process_List 为空，直接追加所有 Process_List_All 的数据（深拷贝）\n            if (dataItem.Process_List.length === 0) {\n              Process_List_All.forEach(item => {\n                dataItem.Process_List.push({\n                  Bom_Level: item.Bom_Level,\n                  Working_Process_Id: item.Working_Process_Id,\n                  Working_Process_Code: item.Working_Process_Code,\n                  Working_Process_Name: item.Working_Process_Name\n                })\n              })\n            } else {\n              // 将 Process_List_All 中不存在于当前项目 Process_List 的数据追加进去\n              Process_List_All.forEach(newItem => {\n                // 检查当前项目的 Process_List 中是否已存在相同的 Bom_Level\n                const exists = dataItem.Process_List.some(existingItem =>\n                  existingItem.Bom_Level.toString() === newItem.Bom_Level.toString()\n                )\n\n                // 如果不存在，则追加到当前项目的 Process_List（深拷贝）\n                if (!exists) {\n                  dataItem.Process_List.push({\n                    Bom_Level: newItem.Bom_Level,\n                    Working_Process_Id: newItem.Working_Process_Id,\n                    Working_Process_Code: newItem.Working_Process_Code,\n                    Working_Process_Name: newItem.Working_Process_Name\n                  })\n                }\n              })\n            }\n          })\n          this.list = resData\n          // 确保数据独立性\n          this.$nextTick(() => {\n            this.ensureDataIndependence()\n          })\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    getProcessList() {\n      GetProcessList({ }).then(res => {\n        this.selectList = res.Data\n      }).finally(() => {\n        this.getTypeList()\n      })\n    },\n    handleSubmit() {\n      this.btnLoading = true\n      SaveConsumingProcessAllList2(this.list).then(res => {\n        if (res.IsSucceed) {\n          this.$message.success('保存成功')\n          this.$emit('close')\n        } else {\n          this.$message.error(res.Message)\n        }\n      }).finally(() => {\n        this.btnLoading = false\n      })\n    },\n    bomClick() {\n      this.getParentBom()\n    },\n    // 获取当前bom层级的所有父级bom层级信息\n    getParentBom() {\n      // 找到当前code在bomList中的索引位置\n      const currentIndex = this.bomList.findIndex(item => item.Code === this.bomActiveName)\n\n      // 如果找到了，则截取该索引之前的所有数据\n      if (currentIndex > 0) {\n        this.parentBomList = this.bomList.slice(0, currentIndex)\n      } else {\n        // 如果是第一个或者没找到，则返回空数组\n        this.parentBomList = []\n      }\n    },\n    // 根据BOM层级Code过滤selectList\n    getFilteredSelectList(code) {\n      if (!this.selectList || !code) {\n        return []\n      }\n      return this.selectList.filter(item => item.Bom_Level.toString() === code || item.Bom_Level.toString() === code)\n    },\n    // 获取指定item和bomCode对应的Working_Process_Id\n    getProcessId(item, bomCode) {\n      if (!item.Process_List || !bomCode) {\n        return ''\n      }\n\n      // 确保 Process_List 是数组\n      if (!Array.isArray(item.Process_List)) {\n        return ''\n      }\n\n      const processItem = item.Process_List.find(p =>\n        p && p.Bom_Level && p.Bom_Level.toString() === bomCode.toString()\n      )\n\n      return processItem ? (processItem.Working_Process_Id || '') : ''\n    },\n    // 更新指定item和bomCode对应的Working_Process_Id\n    updateProcessId(item, bomCode, value) {\n      // 确保 Process_List 存在且是数组\n      if (!item.Process_List || !Array.isArray(item.Process_List)) {\n        this.$set(item, 'Process_List', [])\n      }\n\n      // 查找对应的 Bom_Level 项目\n      const processItemIndex = item.Process_List.findIndex(p =>\n        p && p.Bom_Level && p.Bom_Level.toString() === bomCode.toString()\n      )\n\n      if (processItemIndex !== -1) {\n        // 如果找到了对应的项目，更新其值\n        const processItem = item.Process_List[processItemIndex]\n\n        // 使用 $set 确保响应式更新\n        this.$set(processItem, 'Working_Process_Id', value)\n\n        // 同时更新对应的工艺信息\n        if (value) {\n          const selectedProcess = this.selectList.find(s => s.Id === value)\n          if (selectedProcess) {\n            this.$set(processItem, 'Working_Process_Code', selectedProcess.Code || '')\n            this.$set(processItem, 'Working_Process_Name', selectedProcess.Name || '')\n          }\n        } else {\n          this.$set(processItem, 'Working_Process_Code', '')\n          this.$set(processItem, 'Working_Process_Name', '')\n        }\n      } else {\n        // 如果没找到，创建新的项目\n        const newProcessItem = {\n          Bom_Level: bomCode,\n          Working_Process_Id: value,\n          Working_Process_Code: '',\n          Working_Process_Name: ''\n        }\n\n        // 如果有选中值，填充工艺信息\n        if (value) {\n          const selectedProcess = this.selectList.find(s => s.Id === value)\n          if (selectedProcess) {\n            newProcessItem.Working_Process_Code = selectedProcess.Code || ''\n            newProcessItem.Working_Process_Name = selectedProcess.Name || ''\n          }\n        }\n\n        // 使用 $set 添加新项目\n        item.Process_List.push(newProcessItem)\n      }\n    },\n    // 确保每个item的Process_List都是独立的\n    ensureDataIndependence() {\n      if (this.list && Array.isArray(this.list)) {\n        this.list.forEach(item => {\n          if (item.Process_List && Array.isArray(item.Process_List)) {\n            // 深拷贝 Process_List 确保数据独立\n            const originalProcessList = item.Process_List\n            item.Process_List = originalProcessList.map(processItem => ({\n              Bom_Level: processItem.Bom_Level,\n              Working_Process_Id: processItem.Working_Process_Id || '',\n              Working_Process_Code: processItem.Working_Process_Code || '',\n              Working_Process_Name: processItem.Working_Process_Name || ''\n            }))\n          }\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"~@/styles/mixin.scss\";\n.form-wrapper {\n  height: 50vh;\n  display: flex;\n  flex-direction: column;\n\n  .form-content {\n    flex: 1;\n    overflow: auto;\n    padding-right: 16px;\n    @include scrollBar;\n\n    .form-x {\n      padding-bottom: 20px;\n    }\n  }\n\n  .form-footer {\n    text-align: right;\n    flex-shrink: 0;\n    padding-top: 16px;\n    background: #fff;\n  }\n}\n\n.can-process-title {\n  display: flex;\n  height: 38px;\n  margin-bottom: 16px;\n  .can-process-list {\n    flex: 1;\n    display: flex;\n    justify-content: flex-start;\n    font-size: 16px;\n    color: #333333;\n  }\n}\n\n.can-process-box {\n  height: calc(100% - 50px);\n  .can-process-empty {\n    height: 100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  .can-process-item {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    margin-bottom: 16px;\n    .can-process-type {\n      font-size: 14px;\n      color: #333333;\n      text-align: right;\n    }\n    .can-process-bom {\n      flex: 1;\n      display: flex;\n      justify-content: flex-start;\n      .can-process-select {\n        margin-left: 12px;\n        text-align: center;\n      }\n    }\n  }\n}\n</style>\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue", "mtime": 1757573755748}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["PartTakeConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PartTakeConfig.vue", "sourceRoot": "src/views/PRO/process-settings/management/component", "sourcesContent": ["<template>\n  <div class=\"form-wrapper\">\n    <div class=\"form-recognition-tabs\">\n      <el-tabs v-model=\"bomActiveName\" @tab-click=\"bomClick\">\n        <el-tab-pane v-for=\"(item, index) in tabBomList\" :key=\"index\" :label=\"item.Display_Name\" :name=\"item.Code\" />\n      </el-tabs>\n    </div>\n    <div class=\"form-content\">\n      <div style=\"display: flex; margin-bottom: 16px;\">\n        <div style=\"width: 120px;\" />\n        <div style=\"flex: 1; display: flex; justify-content: flex-start; font-size: 16px; color: #333333;\">\n          <div v-for=\"(item, index) in parentBomList\" :key=\"index\" :style=\"{ width: (100 / parentBomList.length) + '%', textAlign: 'center' }\">{{ item.Display_Name }}</div>\n        </div>\n      </div>\n      <div>\n        <div v-for=\"(item, index) in filteredList\" :key=\"index\" style=\"display: flex;  justify-content: center; align-items: center; margin-bottom: 16px;\">\n          <div style=\"width: 120px; font-size: 14px; color: #333333; text-align: right;\">{{ item.Part_Type_Name }}</div>\n          <div style=\"flex: 1; display: flex; justify-content: flex-start;\">\n            <div v-for=\"bom in parentBomList\" :key=\"bom.Code\" style=\"margin-left: 12px; text-align: center;\" :style=\"{ width: (100 / parentBomList.length) + '%' }\">\n              <el-select v-model=\"item.Working_Process_Id\" clearable style=\"width: 100%;\">\n                <el-option v-for=\"op in getFilteredSelectList(bom.Code)\" :key=\"op.Id\" :label=\"op.Name\" :value=\"op.Id\" />\n              </el-select>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div class=\"form-footer\">\n      <el-button @click=\"$emit('close')\">取 消</el-button>\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport { GetConsumingProcessAllList, SaveConsumingProcessAllList, GetConsumingAllList, SaveConsumingProcessAllList2 } from '@/api/PRO/partType'\nimport { GetProcessList } from '@/api/PRO/technology-lib'\n\nexport default {\n  data() {\n    return {\n      list: [],\n      btnLoading: false,\n      selectList: [],\n      bomList: [],\n      tabBomList: [],\n      comName: '',\n      partName: '',\n      bomActiveName: '',\n      parentBomList: []\n    }\n  },\n  computed: {\n    // 根据当前选中的BOM层级过滤数据\n    filteredList() {\n      if (!this.list || !this.bomActiveName) {\n        return []\n      }\n      return this.list.filter(item => item.Use_Bom_Level.toString() === this.bomActiveName)\n    }\n  },\n  async mounted() {\n    await this.getBom()\n    await this.getParentBom()\n    await this.getProcessList()\n  },\n  methods: {\n    async getBom() {\n      const { comName, partName, list } = await GetBOMInfo()\n      this.comName = comName\n      this.partName = partName\n      this.bomList = list\n      this.tabBomList = list.filter(i => i.Code !== '-1')\n      this.bomActiveName = this.tabBomList[this.tabBomList.length - 1].Code\n    },\n    getTypeList() {\n      GetConsumingAllList({}).then(res => {\n        if (res.IsSucceed) {\n          const resData = res.Data\n          const Process_List_All = []\n          this.parentBomList.map(item => {\n            const Process_List_Json = {}\n            Process_List_Json.Bom_Level = item.Code\n            Process_List_Json.Working_Process_Id = ''\n            Process_List_Json.Working_Process_Code = ''\n            Process_List_Json.Working_Process_Name = ''\n            Process_List_All.push(Process_List_Json)\n          })\n\n          // 确保 Process_List 存在\n          if (!resData.Process_List) {\n            resData.Process_List = []\n          }\n\n          console.log('Process_List_All:', Process_List_All)\n          console.log('resData.Process_List before:', resData.Process_List)\n          console.log('parentBomList:', this.parentBomList)\n\n          // 如果 Process_List 为空，直接追加所有 Process_List_All 的数据\n          if (resData.Process_List.length === 0) {\n            resData.Process_List.push(...Process_List_All)\n            console.log('Process_List 为空，直接追加所有数据')\n          } else {\n            // 将 Process_List_All 中不存在于 Process_List 的数据追加到 Process_List\n            Process_List_All.forEach(newItem => {\n              // 检查 Process_List 中是否已存在相同的 Bom_Level\n              const exists = resData.Process_List.some(existingItem =>\n                existingItem.Bom_Level === newItem.Bom_Level ||\n                existingItem.Bom_Level === newItem.Bom_Level.toString()\n              )\n\n              console.log(`检查 Bom_Level: ${newItem.Bom_Level}, exists: ${exists}`)\n\n              // 如果不存在，则追加到 Process_List\n              if (!exists) {\n                console.log(`追加数据:`, newItem)\n                resData.Process_List.push(newItem)\n              }\n            })\n          }\n\n          console.log('resData.Process_List after:', resData.Process_List)\n\n          this.list = resData\n          console.log('this.list', this.list)\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    getProcessList() {\n      GetProcessList({ }).then(res => {\n        this.selectList = res.Data\n      }).finally(() => {\n        this.getTypeList()\n      })\n    },\n    handleSubmit() {\n      this.btnLoading = true\n      SaveConsumingProcessAllList(this.list.filter(i => i.Working_Process_Id)).then(res => {\n        if (res.IsSucceed) {\n          this.$message.success('保存成功')\n          this.$emit('close')\n        } else {\n          this.$message.error(res.Message)\n        }\n      }).finally(() => {\n        this.btnLoading = false\n      })\n    },\n    bomClick() {\n      this.getParentBom()\n    },\n    // 获取当前bom层级的所有父级bom层级信息\n    getParentBom() {\n      // 找到当前code在bomList中的索引位置\n      const currentIndex = this.bomList.findIndex(item => item.Code === this.bomActiveName)\n\n      // 如果找到了，则截取该索引之前的所有数据\n      if (currentIndex > 0) {\n        this.parentBomList = this.bomList.slice(0, currentIndex)\n      } else {\n        // 如果是第一个或者没找到，则返回空数组\n        this.parentBomList = []\n      }\n    },\n    // 根据BOM层级Code过滤selectList\n    getFilteredSelectList(code) {\n      if (!this.selectList || !code) {\n        return []\n      }\n      return this.selectList.filter(item => item.Bom_Level.toString() === code || item.Bom_Level.toString() === code)\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"~@/styles/mixin.scss\";\n.form-wrapper {\n  height: 70vh;\n  display: flex;\n  flex-direction: column;\n\n  .form-content {\n    flex: 1;\n    overflow: auto;\n    padding-right: 16px;\n    @include scrollBar;\n\n    .form-x {\n      padding-bottom: 20px;\n    }\n  }\n\n  .form-footer {\n    text-align: right;\n    flex-shrink: 0;\n    padding-top: 16px;\n    background: #fff;\n  }\n}\n</style>\n"]}]}
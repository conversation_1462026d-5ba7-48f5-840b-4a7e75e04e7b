{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\ProjectAddDialog.vue?vue&type=template&id=4cc3daf3&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\ProjectAddDialog.vue", "mtime": 1757658473726}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImZvcm0td3JhcHBlciI+CiAgPGRpdiBjbGFzcz0iaW5zdHJ1Y3Rpb24iPuivt+mAieaLqemhueebru+8jOa3u+WKoOaJgOmAiemhueebrueahDxzcGFuPuaJgOacieW3peW6jzwvc3Bhbj48L2Rpdj4KICA8ZGl2PgogICAgPGVsLWZvcm0gcmVmPSJmb3JtIiA6bW9kZWw9ImZvcm0iIGxhYmVsLXdpZHRoPSI4MnB4Ij4KICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i6aG555uu5ZCN56ew77yaIj4KICAgICAgICA8ZWwtc2VsZWN0IHYtbW9kZWw9ImZvcm0uRnJvbV9TeXNfUHJvamVjdF9JZCIgcGxhY2Vob2xkZXI9Iuivt+mAieaLqemhueebriIgc3R5bGU9IndpZHRoOiAzMDBweCI+CiAgICAgICAgICA8ZWwtb3B0aW9uIHYtZm9yPSIoaXRlbSwgaW5kZXgpIGluIHByb2plY3RMaXN0IiA6a2V5PSJpbmRleCIgOmxhYmVsPSJpdGVtLlNob3J0X05hbWUiIHZhbHVlPSJpdGVtLlN5c19Qcm9qZWN0X0lkIiAvPgogICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5Yy65Z+f5LqMIiB2YWx1ZT0iYmVpamluZyIgLz4KICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICA8L2VsLWZvcm0+CiAgPC9kaXY+CiAgPGRpdiBjbGFzcz0iYnRuLXgiPgogICAgPGVsLWJ1dHRvbiBAY2xpY2s9IiRlbWl0KCdjbG9zZScpIj7lj5Yg5raIPC9lbC1idXR0b24+CiAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIDpsb2FkaW5nPSJidG5Mb2FkaW5nIiBAY2xpY2s9ImhhbmRsZVN1Ym1pdCI+56GuIOWumjwvZWwtYnV0dG9uPgogIDwvZGl2Pgo8L2Rpdj4K"}, null]}
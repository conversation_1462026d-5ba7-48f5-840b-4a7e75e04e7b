{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\change-management\\contact-list\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\change-management\\contact-list\\index.vue", "mtime": 1757468112548}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["tablePageSize", "getTbInfo", "Pagination", "DeleteMocType", "DeleteMocOrder", "GetMocOrderPageList", "GetMocOrderTypeList", "SaveMocOrderType", "ChangeMocOrderStatus", "SubmitMocOrder", "addRouterPage", "GeAreaTrees", "GetProjectPageList", "Monitor", "CancelFlow", "DynamicTableFields", "debounce", "name", "components", "mixins", "data", "addPageArray", "path", "$route", "hidden", "component", "Promise", "resolve", "then", "_interopRequireWildcard", "require", "meta", "title", "form", "Sys_Project_Id", "Status", "Exec_Status", "Change_Date", "Moc_Type_Id", "Area_Id", "Urgency", "activeName", "dialogVisible", "tbLoading", "settingLoading", "tbData", "projectList", "dialogTable", "mocType", "installUnitList", "treeParamsArea", "filterable", "clickParent", "props", "disabled", "children", "label", "value", "tbConfig", "multipleSelection", "search", "columns", "gridCode", "queryInfo", "Page", "PageSize", "total", "buttonConfigs", "draft", "text", "handler", "handleSubmitAudit", "<PERSON><PERSON><PERSON>", "handleEdit", "isRed", "handleDelete", "reviewing", "handleView", "key", "handleMonitor", "handleRecycle", "approved", "finish", "handleComplete", "mounted", "fetchData", "getTableConfig", "getBasicData", "getSettingInfo", "methods", "page", "_this", "_this$form", "others", "_objectWithoutProperties", "_excluded", "Change_Begin", "Change_End", "_objectSpread", "res", "IsSucceed", "_res$Data", "Data", "TotalCount", "$message", "message", "Message", "type", "catch", "handleSetting", "handleAddSetting", "row", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "$table", "record", "_yield$$table$insertA", "newRow", "wrap", "_callee$", "_context", "prev", "next", "$refs", "xTable", "Display_Name", "Is_Deepen_Change", "insertAt", "sent", "setEditCell", "stop", "removeRowEvent", "_this3", "Id", "remove", "$confirm", "confirmButtonText", "cancelButtonText", "ids", "_this4", "changeColumn", "_this5", "_callee2", "_callee2$", "_context2", "editClosedEvent", "_ref", "_this6", "column", "field", "flag", "isUpdateByRow", "obj", "reloadRow", "_this7", "getAreaList", "_this8", "sysProjectId", "tree", "setDisabledTree", "$nextTick", "_", "treeSelectArea", "treeDataUpdateFun", "root", "_this9", "for<PERSON>ach", "element", "Children", "length", "projectChange", "e", "_this0", "areaChange", "areaClear", "handleAdd", "tab", "event", "$router", "push", "query", "pg_redirect", "handleSearch", "console", "log", "multiSelectedChange", "array", "records", "handleReset", "resetFields", "getButtonsByStatus", "status", "concat", "_toConsumableArray", "_this1", "id", "_this10", "opendialog", "Instance_Id", "_this11", "instanceId", "editDisabledEvent", "_ref2", "activeRowMethod", "_ref3", "rowIndex", "_this12"], "sources": ["src/views/PRO/change-management/contact-list/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <div class=\"cs-box mb10\">\r\n      <el-row>\r\n        <el-form ref=\"form\" :model=\"form\" label-width=\"100px\">\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"项目名称\" prop=\"Sys_Project_Id\">\r\n              <el-select\r\n                v-model=\"form.Sys_Project_Id\"\r\n                clearable\r\n                style=\"width: 100%\"\r\n                placeholder=\"请选择\"\r\n                filterable\r\n                @change=\"projectChange(form.Sys_Project_Id)\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in projectList\"\r\n                  :key=\"item.Sys_Project_Id\"\r\n                  :label=\"item.Short_Name\"\r\n                  :value=\"item.Sys_Project_Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"区域\" prop=\"Area_Id\">\r\n              <el-tree-select\r\n                ref=\"treeSelectArea\"\r\n                v-model=\"form.Area_Id\"\r\n                class=\"cs-tree-x\"\r\n                :disabled=\"!form.Sys_Project_Id\"\r\n                :select-params=\"{\r\n                  clearable: true,\r\n                }\"\r\n                :tree-params=\"treeParamsArea\"\r\n                @select-clear=\"areaClear\"\r\n                @node-click=\"areaChange\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"单据状态\" prop=\"Status\">\r\n              <el-select v-model=\"form.Status\" clearable placeholder=\"请选择\" style=\"width: 100%\">\r\n                <el-option label=\"草稿\" :value=\"1\" />\r\n                <el-option label=\"审核中\" :value=\"2\" />\r\n                <el-option label=\"审核未通过\" :value=\"-2\" />\r\n                <el-option label=\"审核通过\" :value=\"3\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"变更类型\" prop=\"Moc_Type_Id\">\r\n              <el-select v-model=\"form.Moc_Type_Id\" placeholder=\"请选择\" clearable=\"\">\r\n                <el-option\r\n                  v-for=\"item in mocType\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"执行情况\" prop=\"Exec_Status\">\r\n              <el-select v-model=\"form.Exec_Status\" clearable placeholder=\"请选择\" style=\"width: 100%\">\r\n                <el-option label=\"未开始\" :value=\"1\" />\r\n                <el-option label=\"执行中\" :value=\"2\" />\r\n                <el-option label=\"已完成\" :value=\"3\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"变更日期\" prop=\"Change_Date\">\r\n              <el-date-picker\r\n                v-model=\"form.Change_Date\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                type=\"daterange\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始日期\"\r\n                end-placeholder=\"结束日期\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"紧急程度\" prop=\"Urgency\">\r\n              <el-select v-model=\"form.Urgency\" clearable placeholder=\"请选择\" style=\"width: 100%\">\r\n                <el-option label=\"普通\" :value=\"1\" />\r\n                <el-option label=\"紧急\" :value=\"2\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label-width=\"20px\">\r\n              <el-button type=\"primary\" @click=\"search(1)\">搜索</el-button>\r\n              <el-button @click=\"handleReset\">重置</el-button>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-form>\r\n      </el-row>\r\n    </div>\r\n    <div class=\"cs-box cs-main\">\r\n      <vxe-toolbar\r\n        ref=\"xToolbar1\"\r\n        class=\"cs-toolBar\"\r\n      >\r\n        <template #buttons>\r\n          <el-button type=\"primary\" @click=\"handleAdd\">新建联系单</el-button>\r\n        </template>\r\n        <template #tools>\r\n          <el-button type=\"primary\" @click=\"handleSetting\">变更类型配置</el-button>\r\n          <DynamicTableFields\r\n            title=\"表格配置\"\r\n            :table-config-code=\"gridCode\"\r\n            @updateColumn=\"changeColumn\"\r\n          />\r\n        </template>\r\n      </vxe-toolbar>\r\n\r\n      <div class=\"cs-bottom-wapper\">\r\n        <div class=\"fff tb-x\">\r\n          <vxe-table\r\n            :empty-render=\"{name: 'NotData'}\"\r\n            show-header-overflow\r\n            :loading=\"tbLoading\"\r\n            element-loading-spinner=\"el-icon-loading\"\r\n            element-loading-text=\"拼命加载中\"\r\n            empty-text=\"暂无数据\"\r\n            class=\"cs-vxe-table\"\r\n            height=\"100%\"\r\n            align=\"left\"\r\n            stripe\r\n            :data=\"tbData\"\r\n            resizable\r\n            :tooltip-config=\"{ enterable: true}\"\r\n            :checkbox-config=\"{checkField: 'checked'}\"\r\n            @checkbox-all=\"multiSelectedChange\"\r\n            @checkbox-change=\"multiSelectedChange\"\r\n          >\r\n            <!--            <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" />-->\r\n            <template v-for=\"item in columns\">\r\n              <vxe-column\r\n                :key=\"item.Code\"\r\n                :min-width=\"item.Width\"\r\n                show-overflow=\"tooltip\"\r\n                sortable\r\n                align=\"center\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                :fixed=\"item.Is_Frozen ? (item.Frozen_Dirction || 'left') : ''\"\r\n              >\r\n                <template v-if=\"['Change_Date','Demand_Date','Create_Date','Change_End'].includes(item.Code) \" #default=\"{ row }\">\r\n                  {{ row[item.Code] | timeFormat }}\r\n                </template>\r\n                <template v-else-if=\"item.Code === 'Exec_Status'\" #default=\"{ row }\">\r\n                  <span :class=\"['cs-tags',row[item.Code]===1?'cs-red':row[item.Code]===2?'cs-blue':'cs-green']\">{{ row[item.Code]===1?'未开始':row[item.Code]===2?'执行中':row[item.Code]===3?'已完成':'' }}</span>\r\n                </template>\r\n                <template v-else-if=\"item.Code === 'Urgency'\" #default=\"{ row }\">\r\n                  <el-tag v-if=\"row.Urgency == 1\" type=\"primary\">普通</el-tag>\r\n                  <el-tag v-else-if=\"row.Urgency == 2\" type=\"danger\">紧急</el-tag>\r\n                  <span v-else>-</span>\r\n                </template>\r\n                <!--                <template v-else-if=\"item.Code === 'Change_Type'\" #default=\"{ row }\">-->\r\n                <!--                  <span> {{ row[item.Code] ==='0'?'完整变更':row[item.Code] ==='1'?'部分变更':row[item.Code] ==='2'?'手动变更':'' }}</span>-->\r\n                <!--                </template>-->\r\n                <template v-else #default=\"{ row }\">\r\n                  <span> {{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n            </template>\r\n            <vxe-column fixed=\"right\" title=\"操作\" width=\"180\">\r\n              <template #default=\"{ row }\">\r\n                <template v-for=\"btn in getButtonsByStatus(row.Status,row)\">\r\n                  <el-button\r\n                    v-if=\"btn.checkKey(btn.key,row)\"\r\n                    :key=\"btn.text\"\r\n                    :class=\"{'txt-red':btn.isRed}\"\r\n                    type=\"text\"\r\n                    @click=\"btn.handler(row)\"\r\n                  >{{ btn.text }}</el-button>\r\n                </template>\r\n\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-table>\r\n        </div>\r\n        <div class=\"data-info\">\r\n          <!--          <el-tag-->\r\n          <!--            size=\"medium\"-->\r\n          <!--            class=\"info-x\"-->\r\n          <!--          >已选 {{ multipleSelection.length }} 条数据-->\r\n          <!--          </el-tag>-->\r\n          <Pagination\r\n            :total=\"total\"\r\n            :page-sizes=\"tablePageSize\"\r\n            :page.sync=\"queryInfo.Page\"\r\n            :limit.sync=\"queryInfo.PageSize\"\r\n            @pagination=\"pageChange\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      title=\"变更类型\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"50%\"\r\n      class=\"plm-custom-dialog cs-dialog\"\r\n      @close=\"dialogVisible=false\"\r\n    >\r\n      <div>\r\n        <vxe-toolbar>\r\n          <template #buttons>\r\n            <vxe-button status=\"primary\" content=\"添加\" @click=\"handleAddSetting()\" />\r\n          </template>\r\n        </vxe-toolbar>\r\n\r\n        <vxe-table\r\n          ref=\"xTable\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          border\r\n          class=\"cs-vxe-table\"\r\n          stripe\r\n          resizable\r\n          show-overflow\r\n          :loading=\"settingLoading\"\r\n          :data=\"dialogTable\"\r\n          :edit-config=\"{beforeEditMethod: activeRowMethod,trigger: 'click',showStatus: true, mode: 'row'}\"\r\n          @edit-closed=\"editClosedEvent\"\r\n          @edit-disabled=\"editDisabledEvent\"\r\n        >\r\n\r\n          <vxe-column\r\n            align=\"left\"\r\n            field=\"Display_Name\"\r\n            title=\"类型名称\"\r\n            min-width=\"180\"\r\n            :edit-render=\"{autofocus: '.vxe-input--inner'}\"\r\n          >\r\n            <template #edit=\"{ row }\">\r\n              <vxe-input v-model=\"row.Display_Name\" type=\"text\" />\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column\r\n            align=\"left\"\r\n            field=\"Is_Deepen_Change\"\r\n            title=\"是否变更清单\"\r\n            min-width=\"180\"\r\n            :edit-render=\"{}\"\r\n          >\r\n            <template #edit=\"{ row }\">\r\n              <el-radio v-model=\"row.Is_Deepen_Change\" :label=\"true\">是</el-radio>\r\n              <el-radio v-model=\"row.Is_Deepen_Change\" :label=\"false\">否</el-radio>\r\n            </template>\r\n            <template #default=\"{row}\">\r\n              <el-tag v-if=\" row.Is_Deepen_Change\" type=\"success\">是</el-tag>\r\n              <el-tag v-else type=\"danger\">否</el-tag>\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column\r\n            align=\"left\"\r\n            title=\"操作\"\r\n          >\r\n            <template #default=\"{ row }\">\r\n              <el-button type=\"text\" class=\"txt-red\" @click=\"removeRowEvent(row)\">删除</el-button>\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">关 闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <Monitor ref=\"monitor\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport Pagination from '@/components/Pagination/index.vue'\r\nimport {\r\n  DeleteMocType,\r\n  DeleteMocOrder,\r\n  GetMocOrderPageList, GetMocOrderTypeList,\r\n  SaveMocOrderType, ChangeMocOrderStatus, SubmitMocOrder\r\n} from '@/api/PRO/changeManagement'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nimport { GeAreaTrees, GetProjectPageList } from '@/api/PRO/project'\r\nimport Monitor from '@/components/Monitor/index.vue'\r\nimport { CancelFlow } from '@/api/PRO/component-stock-out'\r\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\r\nimport { debounce } from '@/utils'\r\n\r\nexport default {\r\n  name: 'PROEngineeringChangeOrder',\r\n  components: {\r\n    DynamicTableFields,\r\n    Monitor,\r\n    Pagination\r\n  },\r\n  mixins: [getTbInfo, addRouterPage],\r\n  data() {\r\n    return {\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/add',\r\n          hidden: true,\r\n          component: () => import('./add.vue'),\r\n          name: 'PROEngineeringChangeOrderAdd',\r\n          meta: { title: '新增' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/edit',\r\n          hidden: true,\r\n          component: () => import('./add.vue'),\r\n          name: 'PROEngineeringChangeOrderEdit',\r\n          meta: { title: '编辑' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/view',\r\n          hidden: true,\r\n          component: () => import('./add.vue'),\r\n          name: 'PROEngineeringChangeOrderView',\r\n          meta: { title: '查看' }\r\n        }\r\n      ],\r\n      form: {\r\n        Sys_Project_Id: '',\r\n        Status: '',\r\n        Exec_Status: '',\r\n        Change_Date: '',\r\n        Moc_Type_Id: '',\r\n        Area_Id: '',\r\n        Urgency: ''\r\n      },\r\n      activeName: 'second',\r\n      dialogVisible: false,\r\n      tbLoading: false,\r\n      settingLoading: false,\r\n      tbData: [],\r\n      projectList: [],\r\n      dialogTable: [],\r\n      mocType: [],\r\n      installUnitList: [],\r\n      treeParamsArea: {\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          disabled: 'disabled',\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      tbConfig: {},\r\n      multipleSelection: [],\r\n      search: () => ({}),\r\n      columns: [],\r\n      gridCode: 'PROEngChangeOrder',\r\n      tablePageSize: tablePageSize,\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: tablePageSize[0]\r\n      },\r\n      total: 0,\r\n      buttonConfigs: {\r\n        draft: [\r\n          { text: '提交审核', handler: this.handleSubmitAudit, checkKey: this.checkKey },\r\n          { text: '编辑', handler: this.handleEdit, checkKey: this.checkKey },\r\n          // { text: '监控', handler: this.handleMonitor, checkKey: this.checkKey },\r\n          { text: '删除', isRed: true, handler: this.handleDelete, checkKey: this.checkKey }\r\n        ],\r\n        reviewing: [\r\n          { text: '查看', handler: this.handleView, checkKey: this.checkKey },\r\n          { text: '监控', key: 'monitor', handler: this.handleMonitor, checkKey: this.checkKey },\r\n          { text: '回收', handler: this.handleRecycle, checkKey: this.checkKey }\r\n        ],\r\n        approved: [\r\n          { text: '查看', handler: this.handleView, checkKey: this.checkKey },\r\n          { text: '监控', key: 'monitor', handler: this.handleMonitor, checkKey: this.checkKey }\r\n        ],\r\n        finish: [\r\n          { text: '完成', handler: this.handleComplete, checkKey: this.checkKey }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    this.getTableConfig(this.gridCode)\r\n    this.fetchData(1)\r\n    this.getBasicData()\r\n    this.getSettingInfo()\r\n  },\r\n  methods: {\r\n    fetchData(page) {\r\n      page && (this.queryInfo.Page = page)\r\n      const { Change_Date, ...others } = this.form\r\n      const Change_Begin = Change_Date[0]\r\n      const Change_End = Change_Date[1]\r\n      this.tbLoading = true\r\n      GetMocOrderPageList({ ...others, Change_Begin, Change_End, ...this.queryInfo }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res?.Data?.Data || []\r\n          this.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.tbLoading = false\r\n      }).catch(() => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    handleSetting() {\r\n      this.dialogVisible = true\r\n      this.getSettingInfo()\r\n    },\r\n    async handleAddSetting(row) {\r\n      const $table = this.$refs.xTable\r\n      const record = {\r\n        Display_Name: '',\r\n        Is_Deepen_Change: false\r\n      }\r\n      const { row: newRow } = await $table.insertAt(record, row)\r\n      await $table.setEditCell(newRow, 'name')\r\n    },\r\n    removeRowEvent(row) {\r\n      if (!row.Id) {\r\n        this.$refs.xTable.remove(row)\r\n        return\r\n      }\r\n      this.$confirm(' 是否删除该类型?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        DeleteMocType({\r\n          ids: row.Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.getSettingInfo()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    getSettingInfo() {\r\n      GetMocOrderTypeList({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.dialogTable = res.Data\r\n          this.mocType = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async changeColumn() {\r\n      await this.getTableConfig(this.gridCode)\r\n    },\r\n    editClosedEvent({ row, column }) {\r\n      if (!row.Display_Name) {\r\n        this.$message({\r\n          message: '名称不能为空',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      const $table = this.$refs.xTable\r\n      const field = column.field\r\n      let flag = false\r\n      if ($table.isUpdateByRow(row, field) && row.Id || !row.Id) {\r\n        flag = true\r\n      }\r\n      if (flag) {\r\n        const obj = {\r\n          Display_Name: row.Display_Name,\r\n          Is_Deepen_Change: row.Is_Deepen_Change\r\n        }\r\n        row.Id && (obj.Id = row.Id)\r\n        SaveMocOrderType(obj).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            $table.reloadRow(row, null, field)\r\n            this.getSettingInfo()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n    },\r\n\r\n    getBasicData() {\r\n      GetProjectPageList({ PageSize: -1 }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.projectList = res.Data.Data\r\n        }\r\n      })\r\n    },\r\n    getAreaList(Sys_Project_Id) {\r\n      GeAreaTrees({\r\n        sysProjectId: Sys_Project_Id\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const tree = res.Data\r\n          this.setDisabledTree(tree)\r\n          this.treeParamsArea.data = res.Data\r\n          this.$nextTick(_ => {\r\n            this.$refs.treeSelectArea.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setDisabledTree(root) {\r\n      if (!root) return\r\n      root.forEach((element) => {\r\n        const { Children } = element\r\n        if (Children && Children.length) {\r\n          element.disabled = true\r\n        } else {\r\n          element.disabled = false\r\n          this.setDisabledTree(Children)\r\n        }\r\n      })\r\n    },\r\n    /*    getInstallUnitPageList() {\r\n      GetInstallUnitPageList({\r\n        Area_Id: this.form.Area_Id,\r\n        Page: 1,\r\n        PageSize: -1\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          if (res.IsSucceed) {\r\n            this.installUnitList = res.Data.Data\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },*/\r\n    projectChange(e) {\r\n      const Sys_Project_Id = e\r\n      this.form.Area_Id = ''\r\n      this.treeParamsArea.data = []\r\n      this.$nextTick(_ => {\r\n        this.$refs.treeSelectArea.treeDataUpdateFun([])\r\n      })\r\n      if (e) {\r\n        this.getAreaList(Sys_Project_Id)\r\n      }\r\n    },\r\n    areaChange() {\r\n      // this.getInstallUnitPageList()\r\n    },\r\n    areaClear() {\r\n      this.form.Area_Id = ''\r\n    },\r\n\r\n    handleAdd(tab, event) {\r\n      this.$router.push({ name: 'PROEngineeringChangeOrderAdd', query: { pg_redirect: this.$route.name }})\r\n    },\r\n    handleSearch(tab, event) {\r\n      console.log(tab, event)\r\n    },\r\n    multiSelectedChange(array) {\r\n      this.multipleSelection = array.records\r\n    },\r\n    handleReset() {\r\n      this.$refs['form'].resetFields()\r\n      this.search(1)\r\n    },\r\n    getButtonsByStatus(status, row) {\r\n      // +-1：草稿，2：审批中，3：已通过完成，-2：审核未通过\r\n      switch (status) {\r\n        case -1:\r\n        case 1:\r\n        case -2:\r\n          return this.buttonConfigs.draft\r\n        case 2:\r\n          return this.buttonConfigs.reviewing\r\n        case 3:\r\n          if (row.Exec_Status === 2) {\r\n            return [...this.buttonConfigs.approved, ...this.buttonConfigs.finish]\r\n          }\r\n          return this.buttonConfigs.approved\r\n        default:\r\n          return []\r\n      }\r\n    },\r\n    handleSubmitAudit(row) {\r\n      console.log('提交审核', row)\r\n      this.$confirm('是否提交审核?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbLoading = true\r\n        SubmitMocOrder({\r\n          Id: row.Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '提交成功!'\r\n            })\r\n            this.fetchData(1)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.tbLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    handleEdit(row) {\r\n      console.log('编辑', row)\r\n      this.$router.push({ name: 'PROEngineeringChangeOrderEdit', query: { pg_redirect: this.$route.name, type: 1, id: row.Id }})\r\n    },\r\n    handleDelete(row) {\r\n      this.$confirm('是否删除该数据?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbLoading = true\r\n        DeleteMocOrder({\r\n          Id: row.Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.fetchData(1)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.tbLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    handleView(row) {\r\n      console.log('查看', row)\r\n      this.$router.push({ name: 'PROEngineeringChangeOrderView', query: { pg_redirect: this.$route.name, id: row.Id, type: 2 }})\r\n    },\r\n    handleMonitor(row) {\r\n      console.log('监控', row)\r\n      this.$refs['monitor'].opendialog(row.Instance_Id, false)\r\n    },\r\n    handleRecycle(row) {\r\n      console.log('回收', row)\r\n      this.$confirm('是否回收?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbLoading = true\r\n        CancelFlow({\r\n          instanceId: row.Instance_Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '回收成功',\r\n              type: 'success'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.tbLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    editDisabledEvent({ row, column }) {\r\n      // const $table = this.$refs.xTable\r\n      // $table.modal.message({ content: '禁止编辑', status: 'error' })\r\n    },\r\n    activeRowMethod({ row, rowIndex }) {\r\n      return !row.Id\r\n    },\r\n    handleComplete(row) {\r\n      console.log('完成', row)\r\n      this.$confirm('是否完成?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbLoading = true\r\n        ChangeMocOrderStatus({\r\n          Id: row.Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '操作成功!'\r\n            })\r\n            this.fetchData(1)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.tbLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    checkKey(key, row) {\r\n      if (!key) return true\r\n      if (key === 'monitor') {\r\n        return !!row['Instance_Id']\r\n      }\r\n      return true\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.app-container{\r\n  display: flex;\r\n  flex-direction: column;\r\n  .cs-main{\r\n    overflow: hidden;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    .cs-bottom-wapper{\r\n      display: flex;\r\n      flex-direction: column;\r\n      flex: 1;\r\n      overflow: hidden;\r\n      .tb-x{\r\n        flex: 1;\r\n        overflow: hidden;\r\n      }\r\n      .data-info{\r\n        //display: flex;\r\n        //justify-content: space-between;\r\n        //align-items: center;\r\n        text-align: right;\r\n        margin-top: 10px;\r\n      }\r\n      .pagination-container {\r\n        padding: 0;\r\n        padding-bottom: 8px;\r\n        text-align: right;\r\n        margin-top: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n.cs-box{\r\n  background-color: #FFFFFF;\r\n  padding: 16px;\r\n  border-radius: 4px;\r\n}\r\n.mb10{\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.cs-tree-x {\r\n  ::v-deep {\r\n    .el-select {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n.cs-tags{\r\n  padding: 2px 4px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.cs-red{\r\n  color: #FB6B7F;\r\n  background-color: rgba(251, 107, 127, .1);\r\n}\r\n.cs-blue{\r\n  color: #3ECC93;\r\n  background-color:rgba(62, 204, 147, .1);\r\n}\r\n.cs-green{\r\n  color: #52C41A;\r\n  background-color: rgba(82,196,26, .1);\r\n}\r\n.cs-zbtn{\r\n  pointer-events: none;\r\n  z-index: 1;\r\n}\r\n.cs-toolBar {\r\n  ::v-deep {\r\n    .vxe-button--icon.vxe-icon-custom-column{\r\n      display: none;\r\n    }\r\n\r\n    .vxe-button.type--button.is--circle {\r\n      width: 97px;\r\n      z-index: 0;\r\n      border-radius: 4px;\r\n    }\r\n\r\n    .el-form-item {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n.cs-dialog{\r\n  ::v-deep{\r\n    .el-dialog__body{\r\n      max-height: 70vh;\r\n      overflow: auto;\r\n    }\r\n  }\r\n}\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyRA,SAAAA,aAAA;AACA,OAAAC,SAAA;AACA,OAAAC,UAAA;AACA,SACAC,aAAA,EACAC,cAAA,EACAC,mBAAA,EAAAC,mBAAA,EACAC,gBAAA,EAAAC,oBAAA,EAAAC,cAAA,QACA;AACA,OAAAC,aAAA;AACA,SAAAC,WAAA,EAAAC,kBAAA;AACA,OAAAC,OAAA;AACA,SAAAC,UAAA;AACA,OAAAC,kBAAA;AACA,SAAAC,QAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAH,kBAAA,EAAAA,kBAAA;IACAF,OAAA,EAAAA,OAAA;IACAX,UAAA,EAAAA;EACA;EACAiB,MAAA,GAAAlB,SAAA,EAAAS,aAAA;EACAU,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA,GACA;QACAC,IAAA,OAAAC,MAAA,CAAAD,IAAA;QACAE,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAb,IAAA;QACAc,IAAA;UAAAC,KAAA;QAAA;MACA,GACA;QACAV,IAAA,OAAAC,MAAA,CAAAD,IAAA;QACAE,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAb,IAAA;QACAc,IAAA;UAAAC,KAAA;QAAA;MACA,GACA;QACAV,IAAA,OAAAC,MAAA,CAAAD,IAAA;QACAE,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAb,IAAA;QACAc,IAAA;UAAAC,KAAA;QAAA;MACA,EACA;MACAC,IAAA;QACAC,cAAA;QACAC,MAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;QACAC,OAAA;QACAC,OAAA;MACA;MACAC,UAAA;MACAC,aAAA;MACAC,SAAA;MACAC,cAAA;MACAC,MAAA;MACAC,WAAA;MACAC,WAAA;MACAC,OAAA;MACAC,eAAA;MACAC,cAAA;QACA;QACAC,UAAA;QACAC,WAAA;QACAhC,IAAA;QACAiC,KAAA;UACAC,QAAA;UACAC,QAAA;UACAC,KAAA;UACAC,KAAA;QACA;MACA;MACAC,QAAA;MACAC,iBAAA;MACAC,MAAA,WAAAA,OAAA;QAAA;MAAA;MACAC,OAAA;MACAC,QAAA;MACA9D,aAAA,EAAAA,aAAA;MACA+D,SAAA;QACAC,IAAA;QACAC,QAAA,EAAAjE,aAAA;MACA;MACAkE,KAAA;MACAC,aAAA;QACAC,KAAA,GACA;UAAAC,IAAA;UAAAC,OAAA,OAAAC,iBAAA;UAAAC,QAAA,OAAAA;QAAA,GACA;UAAAH,IAAA;UAAAC,OAAA,OAAAG,UAAA;UAAAD,QAAA,OAAAA;QAAA;QACA;QACA;UAAAH,IAAA;UAAAK,KAAA;UAAAJ,OAAA,OAAAK,YAAA;UAAAH,QAAA,OAAAA;QAAA,EACA;QACAI,SAAA,GACA;UAAAP,IAAA;UAAAC,OAAA,OAAAO,UAAA;UAAAL,QAAA,OAAAA;QAAA,GACA;UAAAH,IAAA;UAAAS,GAAA;UAAAR,OAAA,OAAAS,aAAA;UAAAP,QAAA,OAAAA;QAAA,GACA;UAAAH,IAAA;UAAAC,OAAA,OAAAU,aAAA;UAAAR,QAAA,OAAAA;QAAA,EACA;QACAS,QAAA,GACA;UAAAZ,IAAA;UAAAC,OAAA,OAAAO,UAAA;UAAAL,QAAA,OAAAA;QAAA,GACA;UAAAH,IAAA;UAAAS,GAAA;UAAAR,OAAA,OAAAS,aAAA;UAAAP,QAAA,OAAAA;QAAA,EACA;QACAU,MAAA,GACA;UAAAb,IAAA;UAAAC,OAAA,OAAAa,cAAA;UAAAX,QAAA,OAAAA;QAAA;MAEA;IACA;EACA;EACAY,OAAA,WAAAA,QAAA;IACA,KAAAxB,MAAA,GAAA5C,QAAA,MAAAqE,SAAA;IACA,KAAAC,cAAA,MAAAxB,QAAA;IACA,KAAAuB,SAAA;IACA,KAAAE,YAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACAJ,SAAA,WAAAA,UAAAK,IAAA;MAAA,IAAAC,KAAA;MACAD,IAAA,UAAA3B,SAAA,CAAAC,IAAA,GAAA0B,IAAA;MACA,IAAAE,UAAA,QAAA3D,IAAA;QAAAI,WAAA,GAAAuD,UAAA,CAAAvD,WAAA;QAAAwD,MAAA,GAAAC,wBAAA,CAAAF,UAAA,EAAAG,SAAA;MACA,IAAAC,YAAA,GAAA3D,WAAA;MACA,IAAA4D,UAAA,GAAA5D,WAAA;MACA,KAAAM,SAAA;MACAtC,mBAAA,CAAA6F,aAAA,CAAAA,aAAA,KAAAL,MAAA;QAAAG,YAAA,EAAAA,YAAA;QAAAC,UAAA,EAAAA;MAAA,QAAAlC,SAAA,GAAAnC,IAAA,WAAAuE,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UAAA,IAAAC,SAAA;UACAV,KAAA,CAAA9C,MAAA,IAAAsD,GAAA,aAAAA,GAAA,gBAAAE,SAAA,GAAAF,GAAA,CAAAG,IAAA,cAAAD,SAAA,uBAAAA,SAAA,CAAAC,IAAA;UACAX,KAAA,CAAAzB,KAAA,GAAAiC,GAAA,CAAAG,IAAA,CAAAC,UAAA;QACA;UACAZ,KAAA,CAAAa,QAAA;YACAC,OAAA,EAAAN,GAAA,CAAAO,OAAA;YACAC,IAAA;UACA;QACA;QACAhB,KAAA,CAAAhD,SAAA;MACA,GAAAiE,KAAA;QACAjB,KAAA,CAAAhD,SAAA;MACA;IACA;IACAkE,aAAA,WAAAA,cAAA;MACA,KAAAnE,aAAA;MACA,KAAA8C,cAAA;IACA;IACAsB,gBAAA,WAAAA,iBAAAC,GAAA;MAAA,IAAAC,MAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,MAAA,EAAAC,MAAA,EAAAC,qBAAA,EAAAC,MAAA;QAAA,OAAAN,mBAAA,GAAAO,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAR,MAAA,GAAAL,MAAA,CAAAc,KAAA,CAAAC,MAAA;cACAT,MAAA;gBACAU,YAAA;gBACAC,gBAAA;cACA;cAAAN,QAAA,CAAAE,IAAA;cAAA,OACAR,MAAA,CAAAa,QAAA,CAAAZ,MAAA,EAAAP,GAAA;YAAA;cAAAQ,qBAAA,GAAAI,QAAA,CAAAQ,IAAA;cAAAX,MAAA,GAAAD,qBAAA,CAAAR,GAAA;cAAAY,QAAA,CAAAE,IAAA;cAAA,OACAR,MAAA,CAAAe,WAAA,CAAAZ,MAAA;YAAA;YAAA;cAAA,OAAAG,QAAA,CAAAU,IAAA;UAAA;QAAA,GAAAjB,OAAA;MAAA;IACA;IACAkB,cAAA,WAAAA,eAAAvB,GAAA;MAAA,IAAAwB,MAAA;MACA,KAAAxB,GAAA,CAAAyB,EAAA;QACA,KAAAV,KAAA,CAAAC,MAAA,CAAAU,MAAA,CAAA1B,GAAA;QACA;MACA;MACA,KAAA2B,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAjC,IAAA;MACA,GAAA/E,IAAA;QACAzB,aAAA;UACA0I,GAAA,EAAA9B,GAAA,CAAAyB;QACA,GAAA5G,IAAA,WAAAuE,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAmC,MAAA,CAAA/B,QAAA;cACAG,IAAA;cACAF,OAAA;YACA;YACA8B,MAAA,CAAA/C,cAAA;UACA;YACA+C,MAAA,CAAA/B,QAAA;cACAC,OAAA,EAAAN,GAAA,CAAAO,OAAA;cACAC,IAAA;YACA;UACA;QACA;MACA,GAAAC,KAAA;QACA2B,MAAA,CAAA/B,QAAA;UACAG,IAAA;UACAF,OAAA;QACA;MACA;IACA;IACAjB,cAAA,WAAAA,eAAA;MAAA,IAAAsD,MAAA;MACAxI,mBAAA,KAAAsB,IAAA,WAAAuE,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA0C,MAAA,CAAA/F,WAAA,GAAAoD,GAAA,CAAAG,IAAA;UACAwC,MAAA,CAAA9F,OAAA,GAAAmD,GAAA,CAAAG,IAAA;QACA;UACAwC,MAAA,CAAAtC,QAAA;YACAC,OAAA,EAAAN,GAAA,CAAAO,OAAA;YACAC,IAAA;UACA;QACA;MACA;IACA;IACAoC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MAAA,OAAA/B,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA8B,SAAA;QAAA,OAAA/B,mBAAA,GAAAO,IAAA,UAAAyB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvB,IAAA,GAAAuB,SAAA,CAAAtB,IAAA;YAAA;cAAAsB,SAAA,CAAAtB,IAAA;cAAA,OACAmB,MAAA,CAAA1D,cAAA,CAAA0D,MAAA,CAAAlF,QAAA;YAAA;YAAA;cAAA,OAAAqF,SAAA,CAAAd,IAAA;UAAA;QAAA,GAAAY,QAAA;MAAA;IACA;IACAG,eAAA,WAAAA,gBAAAC,IAAA;MAAA,IAAAC,MAAA;MAAA,IAAAvC,GAAA,GAAAsC,IAAA,CAAAtC,GAAA;QAAAwC,MAAA,GAAAF,IAAA,CAAAE,MAAA;MACA,KAAAxC,GAAA,CAAAiB,YAAA;QACA,KAAAxB,QAAA;UACAC,OAAA;UACAE,IAAA;QACA;QACA;MACA;MACA,IAAAU,MAAA,QAAAS,KAAA,CAAAC,MAAA;MACA,IAAAyB,KAAA,GAAAD,MAAA,CAAAC,KAAA;MACA,IAAAC,IAAA;MACA,IAAApC,MAAA,CAAAqC,aAAA,CAAA3C,GAAA,EAAAyC,KAAA,KAAAzC,GAAA,CAAAyB,EAAA,KAAAzB,GAAA,CAAAyB,EAAA;QACAiB,IAAA;MACA;MACA,IAAAA,IAAA;QACA,IAAAE,GAAA;UACA3B,YAAA,EAAAjB,GAAA,CAAAiB,YAAA;UACAC,gBAAA,EAAAlB,GAAA,CAAAkB;QACA;QACAlB,GAAA,CAAAyB,EAAA,KAAAmB,GAAA,CAAAnB,EAAA,GAAAzB,GAAA,CAAAyB,EAAA;QACAjI,gBAAA,CAAAoJ,GAAA,EAAA/H,IAAA,WAAAuE,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAkD,MAAA,CAAA9C,QAAA;cACAC,OAAA;cACAE,IAAA;YACA;YACAU,MAAA,CAAAuC,SAAA,CAAA7C,GAAA,QAAAyC,KAAA;YACAF,MAAA,CAAA9D,cAAA;UACA;YACA8D,MAAA,CAAA9C,QAAA;cACAC,OAAA,EAAAN,GAAA,CAAAO,OAAA;cACAC,IAAA;YACA;UACA;QACA;MACA;IACA;IAEApB,YAAA,WAAAA,aAAA;MAAA,IAAAsE,MAAA;MACAjJ,kBAAA;QAAAqD,QAAA;MAAA,GAAArC,IAAA,WAAAuE,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAyD,MAAA,CAAA/G,WAAA,GAAAqD,GAAA,CAAAG,IAAA,CAAAA,IAAA;QACA;MACA;IACA;IACAwD,WAAA,WAAAA,YAAA5H,cAAA;MAAA,IAAA6H,MAAA;MACApJ,WAAA;QACAqJ,YAAA,EAAA9H;MACA,GAAAN,IAAA,WAAAuE,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA,IAAA6D,IAAA,GAAA9D,GAAA,CAAAG,IAAA;UACAyD,MAAA,CAAAG,eAAA,CAAAD,IAAA;UACAF,MAAA,CAAA7G,cAAA,CAAA9B,IAAA,GAAA+E,GAAA,CAAAG,IAAA;UACAyD,MAAA,CAAAI,SAAA,WAAAC,CAAA;YACAL,MAAA,CAAAjC,KAAA,CAAAuC,cAAA,CAAAC,iBAAA,CAAAnE,GAAA,CAAAG,IAAA;UACA;QACA;UACAyD,MAAA,CAAAvD,QAAA;YACAC,OAAA,EAAAN,GAAA,CAAAO,OAAA;YACAC,IAAA;UACA;QACA;MACA;IACA;IACAuD,eAAA,WAAAA,gBAAAK,IAAA;MAAA,IAAAC,MAAA;MACA,KAAAD,IAAA;MACAA,IAAA,CAAAE,OAAA,WAAAC,OAAA;QACA,IAAAC,QAAA,GAAAD,OAAA,CAAAC,QAAA;QACA,IAAAA,QAAA,IAAAA,QAAA,CAAAC,MAAA;UACAF,OAAA,CAAApH,QAAA;QACA;UACAoH,OAAA,CAAApH,QAAA;UACAkH,MAAA,CAAAN,eAAA,CAAAS,QAAA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAE,aAAA,WAAAA,cAAAC,CAAA;MAAA,IAAAC,MAAA;MACA,IAAA7I,cAAA,GAAA4I,CAAA;MACA,KAAA7I,IAAA,CAAAM,OAAA;MACA,KAAAW,cAAA,CAAA9B,IAAA;MACA,KAAA+I,SAAA,WAAAC,CAAA;QACAW,MAAA,CAAAjD,KAAA,CAAAuC,cAAA,CAAAC,iBAAA;MACA;MACA,IAAAQ,CAAA;QACA,KAAAhB,WAAA,CAAA5H,cAAA;MACA;IACA;IACA8I,UAAA,WAAAA,WAAA;MACA;IAAA,CACA;IACAC,SAAA,WAAAA,UAAA;MACA,KAAAhJ,IAAA,CAAAM,OAAA;IACA;IAEA2I,SAAA,WAAAA,UAAAC,GAAA,EAAAC,KAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QAAArK,IAAA;QAAAsK,KAAA;UAAAC,WAAA,OAAAjK,MAAA,CAAAN;QAAA;MAAA;IACA;IACAwK,YAAA,WAAAA,aAAAN,GAAA,EAAAC,KAAA;MACAM,OAAA,CAAAC,GAAA,CAAAR,GAAA,EAAAC,KAAA;IACA;IACAQ,mBAAA,WAAAA,oBAAAC,KAAA;MACA,KAAAlI,iBAAA,GAAAkI,KAAA,CAAAC,OAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAjE,KAAA,SAAAkE,WAAA;MACA,KAAApI,MAAA;IACA;IACAqI,kBAAA,WAAAA,mBAAAC,MAAA,EAAAnF,GAAA;MACA;MACA,QAAAmF,MAAA;QACA;QACA;QACA;UACA,YAAA/H,aAAA,CAAAC,KAAA;QACA;UACA,YAAAD,aAAA,CAAAS,SAAA;QACA;UACA,IAAAmC,GAAA,CAAA3E,WAAA;YACA,UAAA+J,MAAA,CAAAC,kBAAA,MAAAjI,aAAA,CAAAc,QAAA,GAAAmH,kBAAA,MAAAjI,aAAA,CAAAe,MAAA;UACA;UACA,YAAAf,aAAA,CAAAc,QAAA;QACA;UACA;MACA;IACA;IACAV,iBAAA,WAAAA,kBAAAwC,GAAA;MAAA,IAAAsF,MAAA;MACAX,OAAA,CAAAC,GAAA,SAAA5E,GAAA;MACA,KAAA2B,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAjC,IAAA;MACA,GAAA/E,IAAA;QACAyK,MAAA,CAAA1J,SAAA;QACAlC,cAAA;UACA+H,EAAA,EAAAzB,GAAA,CAAAyB;QACA,GAAA5G,IAAA,WAAAuE,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAiG,MAAA,CAAA7F,QAAA;cACAG,IAAA;cACAF,OAAA;YACA;YACA4F,MAAA,CAAAhH,SAAA;UACA;YACAgH,MAAA,CAAA7F,QAAA;cACAC,OAAA,EAAAN,GAAA,CAAAO,OAAA;cACAC,IAAA;YACA;UACA;UACA0F,MAAA,CAAA1J,SAAA;QACA;MACA,GAAAiE,KAAA;QACAyF,MAAA,CAAA7F,QAAA;UACAG,IAAA;UACAF,OAAA;QACA;MACA;IACA;IACAhC,UAAA,WAAAA,WAAAsC,GAAA;MACA2E,OAAA,CAAAC,GAAA,OAAA5E,GAAA;MACA,KAAAsE,OAAA,CAAAC,IAAA;QAAArK,IAAA;QAAAsK,KAAA;UAAAC,WAAA,OAAAjK,MAAA,CAAAN,IAAA;UAAA0F,IAAA;UAAA2F,EAAA,EAAAvF,GAAA,CAAAyB;QAAA;MAAA;IACA;IACA7D,YAAA,WAAAA,aAAAoC,GAAA;MAAA,IAAAwF,OAAA;MACA,KAAA7D,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAjC,IAAA;MACA,GAAA/E,IAAA;QACA2K,OAAA,CAAA5J,SAAA;QACAvC,cAAA;UACAoI,EAAA,EAAAzB,GAAA,CAAAyB;QACA,GAAA5G,IAAA,WAAAuE,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAmG,OAAA,CAAA/F,QAAA;cACAG,IAAA;cACAF,OAAA;YACA;YACA8F,OAAA,CAAAlH,SAAA;UACA;YACAkH,OAAA,CAAA/F,QAAA;cACAC,OAAA,EAAAN,GAAA,CAAAO,OAAA;cACAC,IAAA;YACA;UACA;UACA4F,OAAA,CAAA5J,SAAA;QACA;MACA,GAAAiE,KAAA;QACA2F,OAAA,CAAA/F,QAAA;UACAG,IAAA;UACAF,OAAA;QACA;MACA;IACA;IACA5B,UAAA,WAAAA,WAAAkC,GAAA;MACA2E,OAAA,CAAAC,GAAA,OAAA5E,GAAA;MACA,KAAAsE,OAAA,CAAAC,IAAA;QAAArK,IAAA;QAAAsK,KAAA;UAAAC,WAAA,OAAAjK,MAAA,CAAAN,IAAA;UAAAqL,EAAA,EAAAvF,GAAA,CAAAyB,EAAA;UAAA7B,IAAA;QAAA;MAAA;IACA;IACA5B,aAAA,WAAAA,cAAAgC,GAAA;MACA2E,OAAA,CAAAC,GAAA,OAAA5E,GAAA;MACA,KAAAe,KAAA,YAAA0E,UAAA,CAAAzF,GAAA,CAAA0F,WAAA;IACA;IACAzH,aAAA,WAAAA,cAAA+B,GAAA;MAAA,IAAA2F,OAAA;MACAhB,OAAA,CAAAC,GAAA,OAAA5E,GAAA;MACA,KAAA2B,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAjC,IAAA;MACA,GAAA/E,IAAA;QACA8K,OAAA,CAAA/J,SAAA;QACA7B,UAAA;UACA6L,UAAA,EAAA5F,GAAA,CAAA0F;QACA,GAAA7K,IAAA,WAAAuE,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAsG,OAAA,CAAAlG,QAAA;cACAC,OAAA;cACAE,IAAA;YACA;YACA+F,OAAA,CAAArH,SAAA;UACA;YACAqH,OAAA,CAAAlG,QAAA;cACAC,OAAA,EAAAN,GAAA,CAAAO,OAAA;cACAC,IAAA;YACA;UACA;UACA+F,OAAA,CAAA/J,SAAA;QACA;MACA,GAAAiE,KAAA;QACA8F,OAAA,CAAAlG,QAAA;UACAG,IAAA;UACAF,OAAA;QACA;MACA;IACA;IACAmG,iBAAA,WAAAA,kBAAAC,KAAA;MAAA,IAAA9F,GAAA,GAAA8F,KAAA,CAAA9F,GAAA;QAAAwC,MAAA,GAAAsD,KAAA,CAAAtD,MAAA;IAGA,EAFA;IACA;IAAA;IAEAuD,eAAA,WAAAA,gBAAAC,KAAA;MAAA,IAAAhG,GAAA,GAAAgG,KAAA,CAAAhG,GAAA;QAAAiG,QAAA,GAAAD,KAAA,CAAAC,QAAA;MACA,QAAAjG,GAAA,CAAAyB,EAAA;IACA;IACArD,cAAA,WAAAA,eAAA4B,GAAA;MAAA,IAAAkG,OAAA;MACAvB,OAAA,CAAAC,GAAA,OAAA5E,GAAA;MACA,KAAA2B,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAjC,IAAA;MACA,GAAA/E,IAAA;QACAqL,OAAA,CAAAtK,SAAA;QACAnC,oBAAA;UACAgI,EAAA,EAAAzB,GAAA,CAAAyB;QACA,GAAA5G,IAAA,WAAAuE,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACA6G,OAAA,CAAAzG,QAAA;cACAG,IAAA;cACAF,OAAA;YACA;YACAwG,OAAA,CAAA5H,SAAA;UACA;YACA4H,OAAA,CAAAzG,QAAA;cACAC,OAAA,EAAAN,GAAA,CAAAO,OAAA;cACAC,IAAA;YACA;UACA;UACAsG,OAAA,CAAAtK,SAAA;QACA;MACA,GAAAiE,KAAA;QACAqG,OAAA,CAAAzG,QAAA;UACAG,IAAA;UACAF,OAAA;QACA;MACA;IACA;IACAjC,QAAA,WAAAA,SAAAM,GAAA,EAAAiC,GAAA;MACA,KAAAjC,GAAA;MACA,IAAAA,GAAA;QACA,SAAAiC,GAAA;MACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
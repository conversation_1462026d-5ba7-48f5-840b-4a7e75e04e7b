{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production\\components\\addDraft.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production\\components\\addDraft.vue", "mtime": 1757468113335}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetGridByCode", "GetCanSchdulingComps", "GetPartList", "v4", "uuidv4", "FIX_COLUMN", "debounce", "deepClone", "tablePageSize", "GetCompTypeTree", "GetPartTypeList", "props", "scheduleId", "type", "String", "default", "pageType", "showDialog", "Boolean", "data", "pageInfo", "page", "pageSize", "pageSizes", "total", "form", "Comp_Code", "Comp_CodeBlur", "Part_CodeBlur", "Part_Code", "Type_Name", "Spec", "Type", "isOwnerNull", "tbLoading", "saveLoading", "columns", "fTable", "tbConfig", "TotalCount", "Page", "multipleSelection", "totalSelection", "search", "treeSelectParams", "placeholder", "clearable", "ObjectTypeList", "clickParent", "children", "label", "value", "typeOption", "computed", "isCom", "watch", "newValue", "mounted", "getConfig", "getObjectTypeList", "getType", "fetchData", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "code", "wrap", "_callee$", "_context", "prev", "next", "getTableConfig", "stop", "filterData", "_this2", "f", "formKey", "push", "length", "setPage", "tbData", "temTbData", "filter", "v", "checked", "trim", "split", "includes", "console", "log", "handleSearch", "_this$tbData", "clearSelect", "for<PERSON>ach", "item", "handleSelect", "tbSelectChange", "array", "$refs", "xTable1", "clearCheckboxRow", "_this3", "_callee2", "_callee2$", "_context2", "getComTbData", "getPartTbData", "initTbData", "setPageData", "_this$tbData2", "Can_Schduling_Count", "handleSave", "_this4", "setTimeout", "intCount", "parseInt", "count", "<PERSON><PERSON><PERSON><PERSON>_Count", "Can_Schduling_Weight", "Weight", "maxCount", "chooseCount", "cp", "$emit", "_this$tbData3", "_this5", "backendTb", "obj<PERSON><PERSON>", "$set", "uuid", "_this6", "_callee3", "_this6$$route$query", "install", "areaId", "_this6$form", "Comp_Codes", "obj", "codes", "_callee3$", "_context3", "$route", "query", "_objectWithoutProperties", "_excluded", "Object", "prototype", "toString", "call", "_objectSpread", "Schduling_Plan_Id", "InstallUnit_Id", "Area_Id", "then", "res", "IsSucceed", "Data", "map", "idx", "originalPath", "Scheduled_Technology_Path", "Workshop_Id", "Scheduled_Workshop_Id", "Workshop_Name", "Scheduled_Workshop_Name", "Technology_Path", "isDisabled", "initRowIndex", "$message", "message", "Message", "handlePageChange", "_ref", "currentPage", "tb", "arguments", "undefined", "slice", "_this7", "_callee4", "_this7$$route$query", "_callee4$", "_context4", "Component_Technology_Path", "list", "some", "x", "Part_Used_Process", "originalUsedProcess", "Scheduled_Used_Process", "setPartColumn", "every", "Comp_Import_Detail_Id", "findIndex", "Code", "splice", "mergeData", "_this8", "element", "pu<PERSON>", "sort", "a", "b", "handleClose", "activeCellMethod", "_ref2", "row", "column", "columnIndex", "field", "_this9", "_callee5", "_callee5$", "_context5", "assign", "Grid", "Number", "Row_Number", "ColumnList", "Is_Display", "fixed", "Display_Name", "_this0", "professional", "$nextTick", "_", "treeSelectObjectType", "treeDataUpdateFun", "_this1", "Part_Grade"], "sources": ["src/views/PRO/plan-production/schedule-production/components/addDraft.vue"], "sourcesContent": ["<template>\r\n  <div class=\"contentBox\">\r\n    <el-form ref=\"form\" :model=\"form\" label-width=\"90px\">\r\n      <el-row>\r\n        <template v-if=\"isCom\">\r\n          <el-col :span=\"10\">\r\n            <el-form-item label=\"构件编号\" prop=\"Comp_Codes\">\r\n              <el-input\r\n                v-model=\"form.Comp_Code\"\r\n                clearable\r\n                style=\"width: 45%\"\r\n                placeholder=\"请输入(空格区分/多个搜索)\"\r\n                type=\"text\"\r\n              />\r\n              <el-input\r\n                v-model=\"form.Comp_CodeBlur\"\r\n                clearable\r\n                style=\"width: 45%;margin-left: 16px\"\r\n                placeholder=\"模糊查找(请输入关键字)\"\r\n                type=\"text\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"构件类型\" prop=\"Type\">\r\n              <el-tree-select\r\n                ref=\"treeSelectObjectType\"\r\n                v-model=\"form.Type\"\r\n                style=\"width: 100%\"\r\n                class=\"cs-tree-x\"\r\n                :select-params=\"treeSelectParams\"\r\n                :tree-params=\"ObjectTypeList\"\r\n                value-key=\"Id\"\r\n              />\r\n              <!--              <el-select v-model=\"form.Type\" placeholder=\"请选择\" clearable @clear=\"filterData\">\r\n                <el-option label=\"全部\" value=\"\" />\r\n                <el-option\r\n                  v-for=\"item in comTypeOptions\"\r\n                  :key=\"item.value\"\r\n                  :label=\"item.label\"\r\n                  :value=\"item.value\"\r\n                />\r\n              </el-select>-->\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"5\">\r\n            <el-form-item label=\"规格\" prop=\"Spec\">\r\n              <el-input v-model.trim=\"form.Spec\" placeholder=\"请输入\" clearable />\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n        </template>\r\n        <template v-else>\r\n          <el-col :span=\"7\">\r\n            <el-form-item label=\"所属构件\" prop=\"Comp_Code\">\r\n              <el-input\r\n                v-model=\"form.Comp_Code\"\r\n                style=\"width: 45%;\"\r\n                placeholder=\"请输入(空格区分/多个搜索)\"\r\n                clearable\r\n              />\r\n              <el-input\r\n                v-model=\"form.Comp_CodeBlur\"\r\n                clearable\r\n                style=\"width: 45%;margin-left: 16px\"\r\n                placeholder=\"模糊查找(请输入关键字)\"\r\n                type=\"text\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"7\">\r\n            <el-form-item label=\"零件名称\" prop=\"Part_Code\">\r\n              <el-input\r\n                v-model=\"form.Part_Code\"\r\n                style=\"width: 45%;\"\r\n                placeholder=\"请输入(空格区分/多个搜索)\"\r\n                clearable\r\n              />\r\n              <el-input\r\n                v-model=\"form.Part_CodeBlur\"\r\n                clearable\r\n                style=\"width: 45%;margin-left: 16px\"\r\n                placeholder=\"模糊查找(请输入关键字)\"\r\n                type=\"text\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-form-item label=\"规格\" prop=\"Spec\">\r\n              <el-input\r\n                v-model.trim=\"form.Spec\"\r\n                placeholder=\"请输入\"\r\n                clearable\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-form-item label=\"零件种类\" prop=\"Type_Name\">\r\n              <el-select\r\n                v-model=\"form.Type_Name\"\r\n                placeholder=\"请选择\"\r\n                clearable\r\n              >\r\n                <el-option\r\n                  v-for=\"item in typeOption\"\r\n                  :key=\"item.Code\"\r\n                  :label=\"item.Name\"\r\n                  :value=\"item.Name\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </template>\r\n        <el-col :span=\"2\">\r\n          <el-button style=\"margin-left: 10px\" type=\"primary\" @click=\"handleSearch()\">查询</el-button>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n    <div class=\"tb-wrapper\">\r\n      <vxe-table\r\n        ref=\"xTable1\"\r\n        :empty-render=\"{name: 'NotData'}\"\r\n        show-header-overflow\r\n        empty-text=\"暂无数据\"\r\n        height=\"auto\"\r\n        show-overflow\r\n        :checkbox-config=\"{checkField: 'checked'}\"\r\n        :loading=\"tbLoading\"\r\n        :row-config=\"{isCurrent: true, isHover: true }\"\r\n        class=\"cs-vxe-table\"\r\n        align=\"left\"\r\n        stripe\r\n        :data=\"fTable\"\r\n        resizable\r\n        :edit-config=\"{trigger: 'click', mode: 'cell', activeMethod: activeCellMethod}\"\r\n        :tooltip-config=\"{ enterable: true }\"\r\n        @checkbox-all=\"tbSelectChange\"\r\n        @checkbox-change=\"tbSelectChange\"\r\n      >\r\n        <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n        <template v-for=\"item in columns\">\r\n          <vxe-column\r\n            v-if=\"item.Code === 'customCountColumn'\"\r\n            :key=\"item.Code\"\r\n            :align=\"item.Align\"\r\n            :field=\"item.Code\"\r\n            :title=\"item.Display_Name\"\r\n            sortable\r\n            :edit-render=\"{}\"\r\n            min-width=\"120\"\r\n          >\r\n            <template #edit=\"{ row }\">\r\n              <vxe-input\r\n                v-model.number=\"row.count\"\r\n                type=\"integer\"\r\n                :min=\"1\"\r\n                :max=\"row.maxCount\"\r\n              />\r\n            </template>\r\n            <template #default=\"{ row }\">\r\n              {{ row.count | displayValue }}\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column\r\n            v-else-if=\"item.Code === 'Is_Component'\"\r\n            :key=\"item.Code\"\r\n            :align=\"item.Align\"\r\n            :field=\"item.Code\"\r\n            :title=\"item.Display_Name\"\r\n            sortable\r\n            :min-width=\"item.Width\"\r\n          >\r\n            <template #default=\"{ row }\">\r\n              <el-tag :type=\"row.Is_Component ? 'danger' : 'success'\">{{\r\n                row.Is_Component ? \"否\" : \"是\"\r\n              }}</el-tag>\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column\r\n            v-else\r\n            :key=\"item.Code\"\r\n            :align=\"item.Align\"\r\n            :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n            show-overflow=\"tooltip\"\r\n            sortable\r\n            :field=\"item.Code\"\r\n            :title=\"item.Display_Name\"\r\n            :min-width=\"item.Width\"\r\n          />\r\n        </template>\r\n      </vxe-table>\r\n    </div>\r\n    <div class=\"data-info\">\r\n      <el-tag\r\n        size=\"medium\"\r\n        class=\"info-x\"\r\n      >已选 {{ totalSelection.length }} 条数据\r\n      </el-tag>\r\n      <vxe-pager\r\n        border\r\n        background\r\n        :loading=\"tbLoading\"\r\n        :current-page.sync=\"pageInfo.page\"\r\n        :page-size.sync=\"pageInfo.pageSize\"\r\n        :page-sizes=\"pageInfo.pageSizes\"\r\n        :total=\"pageInfo.total\"\r\n        :layouts=\"['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']\"\r\n        size=\"small\"\r\n        @page-change=\"handlePageChange\"\r\n      />\r\n    </div>\r\n    <div class=\"button\">\r\n      <el-button @click=\"handleClose\">取消</el-button>\r\n      <el-button\r\n        type=\"primary\"\r\n        :disabled=\"!totalSelection.length\"\r\n        :loading=\"saveLoading\"\r\n        @click=\"handleSave\"\r\n      >保存</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { GetCanSchdulingComps } from '@/api/PRO/production-task'\r\nimport { GetPartList } from '@/api/PRO/production-part'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { FIX_COLUMN } from '@/views/PRO/plan-production/schedule-production/constant'\r\nimport { debounce, deepClone } from '@/utils'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport { GetCompTypeTree } from '@/api/PRO/professionalType'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\n\r\nexport default {\r\n  props: {\r\n    scheduleId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    pageType: {\r\n      type: String,\r\n      default: 'com'\r\n    },\r\n    showDialog: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      pageInfo: {\r\n        page: 1,\r\n        pageSize: 500,\r\n        pageSizes: tablePageSize,\r\n        total: 0\r\n      },\r\n      form: {\r\n        Comp_Code: '',\r\n        Comp_CodeBlur: '',\r\n        Part_CodeBlur: '',\r\n        Part_Code: '',\r\n        Type_Name: '',\r\n        Spec: '',\r\n        Type: ''\r\n      },\r\n      isOwnerNull: true,\r\n      tbLoading: false,\r\n      saveLoading: false,\r\n      columns: [],\r\n      fTable: [],\r\n      tbConfig: {},\r\n      TotalCount: 0,\r\n      Page: 0,\r\n      multipleSelection: [],\r\n      totalSelection: [],\r\n      search: () => ({}),\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        clearable: true\r\n      },\r\n      ObjectTypeList: {\r\n        // 构件类型\r\n        'check-strictly': true,\r\n        'default-expand-all': true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      },\r\n      typeOption: []\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    }\r\n  },\r\n  watch: {\r\n    showDialog(newValue) {\r\n      newValue && (this.saveLoading = false)\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getConfig()\r\n    if (this.isCom) {\r\n      this.getObjectTypeList()\r\n    } else {\r\n      this.getType()\r\n    }\r\n    this.search = debounce(this.fetchData, 800, true)\r\n  },\r\n  methods: {\r\n    async getConfig() {\r\n      let code = ''\r\n      code = this.isCom\r\n        ? 'PROComDraftEditTbConfig'\r\n        : 'PROPartDraftEditTbConfig'\r\n      await this.getTableConfig(code)\r\n      this.fetchData()\r\n    },\r\n    filterData(page) {\r\n      const f = []\r\n      for (const formKey in this.form) {\r\n        if (this.form[formKey] || this.form[formKey] === false) {\r\n          f.push(formKey)\r\n        }\r\n      }\r\n      if (!f.length) {\r\n        this.setPage()\r\n        !page && (this.pageInfo.page = 1)\r\n        this.pageInfo.total = this.tbData.length\r\n        return\r\n      }\r\n      const temTbData = this.tbData.filter(v => {\r\n        v.checked = false\r\n        if (this.form.Comp_Code.trim() && !this.form['Comp_Code'].split(' ').includes(v['Comp_Code'])) {\r\n          return false\r\n        }\r\n        if (this.form.Comp_CodeBlur.trim() && !v.Comp_Code.includes(this.form.Comp_CodeBlur)) {\r\n          return false\r\n        }\r\n        if (this.form.Type && v.Type !== this.form.Type) {\r\n          return false\r\n        }\r\n        if (this.form.Part_CodeBlur.trim() && !v.Part_Code.includes(this.form.Part_CodeBlur)) {\r\n          return false\r\n        }\r\n        if (this.form.Part_Code.trim() && !this.form['Part_Code'].split(' ').includes(v['Part_Code'])) {\r\n          return false\r\n        }\r\n        if (this.form.Type_Name !== '' && v.Type_Name !== this.form.Type_Name) {\r\n          return false\r\n        }\r\n        if (this.form.Spec.trim() !== '' && !v.Spec.includes(this.form.Spec)) {\r\n          return false\r\n        }\r\n        return true\r\n      })\r\n\r\n      console.log('page', page)\r\n      !page && (this.pageInfo.page = 1)\r\n      this.pageInfo.total = temTbData.length\r\n      this.setPage(temTbData)\r\n    },\r\n    handleSearch() {\r\n      this.totalSelection = []\r\n      this.clearSelect()\r\n      if (this.tbData?.length) {\r\n        this.tbData.forEach(item => item.checked = false)\r\n        this.filterData()\r\n      }\r\n    },\r\n    handleSelect(data) {\r\n      this.multipleSelection = data\r\n    },\r\n    tbSelectChange(array) {\r\n      console.log('array', array)\r\n      this.totalSelection = this.tbData.filter(v => v.checked)\r\n    },\r\n    clearSelect() {\r\n      this.$refs.xTable1.clearCheckboxRow()\r\n      this.totalSelection = []\r\n    },\r\n    async fetchData() {\r\n      this.tbLoading = true\r\n      if (this.isCom) {\r\n        await this.getComTbData()\r\n      } else {\r\n        await this.getPartTbData()\r\n      }\r\n      this.initTbData()\r\n      this.filterData()\r\n      this.tbLoading = false\r\n    },\r\n    setPageData() {\r\n      if (this.tbData?.length) {\r\n        this.pageInfo.page = 1\r\n        this.tbData = this.tbData.filter(v => v.Can_Schduling_Count > 0)\r\n        this.filterData()\r\n      }\r\n    },\r\n    handleSave() {\r\n      this.saveLoading = true\r\n      setTimeout(() => {\r\n        this.totalSelection.forEach((item) => {\r\n          const intCount = parseInt(item.count)\r\n          item.Schduled_Count += intCount\r\n          item.Can_Schduling_Count -= intCount\r\n          item.Can_Schduling_Weight = item.Can_Schduling_Count * item.Weight\r\n          item.maxCount = item.Can_Schduling_Count\r\n          item.chooseCount = intCount\r\n          item.count = item.Can_Schduling_Count\r\n          item.checked = false\r\n        })\r\n        const cp = deepClone(this.totalSelection)\r\n\r\n        this.$emit('sendSelectList', cp)\r\n        this.$emit('close')\r\n        this.clearSelect()\r\n        this.setPage()\r\n      }, 0)\r\n    },\r\n    initTbData() {\r\n      if (!this.tbData?.length) {\r\n        this.tbData = []\r\n        this.backendTb = []\r\n        return\r\n      }\r\n      // 设置文本框选择的排产数量,设置自定义唯一码\r\n      const objKey = {}\r\n      this.tbData.forEach((item) => {\r\n        this.$set(item, 'count', item.Can_Schduling_Count)\r\n        this.$set(item, 'maxCount', item.Can_Schduling_Count)\r\n        item.uuid = uuidv4()\r\n        objKey[item.Type] = true\r\n      })\r\n      this.backendTb = deepClone(this.tbData)\r\n    },\r\n    async getComTbData() {\r\n      const { install, areaId } = this.$route.query\r\n      const { Comp_Codes, ...obj } = this.form\r\n      let codes = []\r\n      if (Object.prototype.toString.call(Comp_Codes) === '[object String]') {\r\n        codes = Comp_Codes && Comp_Codes.split(' ').filter(v => !!v)\r\n      }\r\n      await GetCanSchdulingComps({\r\n        ...obj,\r\n        Schduling_Plan_Id: this.scheduleId,\r\n        Comp_Codes: codes,\r\n        InstallUnit_Id: install,\r\n        Area_Id: areaId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.pageInfo.total = res.Data.length\r\n          this.tbData = res.Data.map((v, idx) => {\r\n            // 已排产赋值\r\n            v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n            v.Workshop_Id = v.Scheduled_Workshop_Id\r\n            v.Workshop_Name = v.Scheduled_Workshop_Name\r\n            v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n            if (v.originalPath) {\r\n              v.isDisabled = true\r\n            }\r\n            v.checked = false\r\n            v.initRowIndex = idx\r\n            return v\r\n          })\r\n          this.setPage()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 分页\r\n     */\r\n    handlePageChange({ currentPage, pageSize }) {\r\n      console.log(' currentPage, pageSize', currentPage, pageSize)\r\n      if (this.tbLoading) return\r\n      this.pageInfo.page = currentPage\r\n      this.pageInfo.pageSize = pageSize\r\n      this.setPage()\r\n      this.filterData(currentPage)\r\n    },\r\n\r\n    setPage(tb = this.tbData) {\r\n      this.fTable = tb.slice((this.pageInfo.page - 1) * this.pageInfo.pageSize, this.pageInfo.page * this.pageInfo.pageSize)\r\n    },\r\n\r\n    async getPartTbData() {\r\n      const { install, areaId } = this.$route.query\r\n      await GetPartList({\r\n        ...this.form,\r\n        Schduling_Plan_Id: this.scheduleId,\r\n        InstallUnit_Id: install,\r\n        Area_Id: areaId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.pageInfo.total = res.Data.length\r\n          this.tbData = res.Data.map((v, idx) => {\r\n            if (v.Component_Technology_Path) {\r\n              const list = v.Component_Technology_Path.split('/')\r\n              if (list.length && list.some(x => x === v.Part_Used_Process)) {\r\n                v.originalUsedProcess = v.Part_Used_Process\r\n              } else {\r\n                v.originalUsedProcess = ''\r\n                v.Part_Used_Process = ''\r\n              }\r\n            }\r\n            v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n            v.Workshop_Id = v.Scheduled_Workshop_Id\r\n            v.Workshop_Name = v.Scheduled_Workshop_Name\r\n            v.Part_Used_Process = v.Scheduled_Used_Process || v.Part_Used_Process// 是否存在已使用的领用工序\r\n            v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n            v.isDisabled = !!v.originalPath\r\n            v.checked = false\r\n            v.initRowIndex = idx\r\n            return v\r\n          })\r\n          this.setPartColumn()\r\n          this.setPage()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setPartColumn() {\r\n      // 纯零件\r\n      this.isOwnerNull = this.tbData.every(v => !v.Comp_Import_Detail_Id)\r\n      console.log('this.isOwnerNull', this.isOwnerNull)\r\n      if (this.isOwnerNull) {\r\n        const idx = this.columns.findIndex(v => v.Code === 'Component_Code')\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n      }\r\n    },\r\n    mergeData(list) {\r\n      list\r\n        .forEach((element) => {\r\n          const idx = this.backendTb.findIndex(\r\n            (item) => element.puuid && item.uuid === element.puuid\r\n          )\r\n          if (idx !== -1) {\r\n            this.tbData.splice(idx, 0, deepClone(this.backendTb[idx]))\r\n          }\r\n        })\r\n\r\n      this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n\r\n      this.filterData()\r\n    },\r\n    handleClose() {\r\n      this.$emit('close')\r\n    },\r\n    activeCellMethod({ row, column, columnIndex }) {\r\n      return column.field === 'customCountColumn'\r\n    },\r\n    async getTableConfig(code) {\r\n      await GetGridByCode({\r\n        code\r\n      }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n          this.pageInfo.pageSize = Number(this.tbConfig.Row_Number)\r\n          const list = Data.ColumnList || []\r\n          this.columns = list.filter(v => v.Is_Display).map(item => {\r\n            if (FIX_COLUMN.includes(item.Code)) {\r\n              item.fixed = 'left'\r\n            }\r\n            return item\r\n          })\r\n          this.columns.push({\r\n            Display_Name: '排产数量',\r\n            Code: 'customCountColumn'\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getObjectTypeList() {\r\n      GetCompTypeTree({ professional: 'Steel' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.ObjectTypeList.data = res.Data\r\n          this.$nextTick((_) => {\r\n            this.$refs.treeSelectObjectType.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getType() {\r\n      GetPartTypeList({ Part_Grade: 0 }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.typeOption = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.contentBox {\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .button {\r\n    margin-top: 16px;\r\n    display: flex;\r\n    justify-content: end;\r\n  }\r\n\r\n  .tb-wrapper {\r\n    flex: 1 1 auto;\r\n    height: 50vh;\r\n  }\r\n\r\n  .data-info{\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-top: 16px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgOA,SAAAA,aAAA;AACA,SAAAC,oBAAA;AACA,SAAAC,WAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AACA,SAAAC,UAAA;AACA,SAAAC,QAAA,EAAAC,SAAA;AACA,SAAAC,aAAA;AACA,SAAAC,eAAA;AACA,SAAAC,eAAA;AAEA;EACAC,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,QAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAE,UAAA;MACAJ,IAAA,EAAAK,OAAA;MACAH,OAAA;IACA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;QACAC,IAAA;QACAC,QAAA;QACAC,SAAA,EAAAf,aAAA;QACAgB,KAAA;MACA;MACAC,IAAA;QACAC,SAAA;QACAC,aAAA;QACAC,aAAA;QACAC,SAAA;QACAC,SAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACAC,WAAA;MACAC,SAAA;MACAC,WAAA;MACAC,OAAA;MACAC,MAAA;MACAC,QAAA;MACAC,UAAA;MACAC,IAAA;MACAC,iBAAA;MACAC,cAAA;MACAC,MAAA,WAAAA,OAAA;QAAA;MAAA;MACAC,gBAAA;QACAC,WAAA;QACAC,SAAA;MACA;MACAC,cAAA;QACA;QACA;QACA;QACAC,WAAA;QACA7B,IAAA;QACAR,KAAA;UACAsC,QAAA;UACAC,KAAA;UACAC,KAAA;QACA;MACA;MACAC,UAAA;IACA;EACA;EACAC,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA,YAAAtC,QAAA;IACA;EACA;EACAuC,KAAA;IACAtC,UAAA,WAAAA,WAAAuC,QAAA;MACAA,QAAA,UAAArB,WAAA;IACA;EACA;EACAsB,OAAA,WAAAA,QAAA;IACA,KAAAC,SAAA;IACA,SAAAJ,KAAA;MACA,KAAAK,iBAAA;IACA;MACA,KAAAC,OAAA;IACA;IACA,KAAAjB,MAAA,GAAArC,QAAA,MAAAuD,SAAA;EACA;EACAC,OAAA;IACAJ,SAAA,WAAAA,UAAA;MAAA,IAAAK,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,IAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAL,IAAA;cACAA,IAAA,GAAAL,KAAA,CAAAT,KAAA,GACA,4BACA;cAAAiB,QAAA,CAAAE,IAAA;cAAA,OACAV,KAAA,CAAAW,cAAA,CAAAN,IAAA;YAAA;cACAL,KAAA,CAAAF,SAAA;YAAA;YAAA;cAAA,OAAAU,QAAA,CAAAI,IAAA;UAAA;QAAA,GAAAR,OAAA;MAAA;IACA;IACAS,UAAA,WAAAA,WAAAvD,IAAA;MAAA,IAAAwD,MAAA;MACA,IAAAC,CAAA;MACA,SAAAC,OAAA,SAAAtD,IAAA;QACA,SAAAA,IAAA,CAAAsD,OAAA,UAAAtD,IAAA,CAAAsD,OAAA;UACAD,CAAA,CAAAE,IAAA,CAAAD,OAAA;QACA;MACA;MACA,KAAAD,CAAA,CAAAG,MAAA;QACA,KAAAC,OAAA;QACA,CAAA7D,IAAA,UAAAD,QAAA,CAAAC,IAAA;QACA,KAAAD,QAAA,CAAAI,KAAA,QAAA2D,MAAA,CAAAF,MAAA;QACA;MACA;MACA,IAAAG,SAAA,QAAAD,MAAA,CAAAE,MAAA,WAAAC,CAAA;QACAA,CAAA,CAAAC,OAAA;QACA,IAAAV,MAAA,CAAApD,IAAA,CAAAC,SAAA,CAAA8D,IAAA,OAAAX,MAAA,CAAApD,IAAA,cAAAgE,KAAA,MAAAC,QAAA,CAAAJ,CAAA;UACA;QACA;QACA,IAAAT,MAAA,CAAApD,IAAA,CAAAE,aAAA,CAAA6D,IAAA,OAAAF,CAAA,CAAA5D,SAAA,CAAAgE,QAAA,CAAAb,MAAA,CAAApD,IAAA,CAAAE,aAAA;UACA;QACA;QACA,IAAAkD,MAAA,CAAApD,IAAA,CAAAO,IAAA,IAAAsD,CAAA,CAAAtD,IAAA,KAAA6C,MAAA,CAAApD,IAAA,CAAAO,IAAA;UACA;QACA;QACA,IAAA6C,MAAA,CAAApD,IAAA,CAAAG,aAAA,CAAA4D,IAAA,OAAAF,CAAA,CAAAzD,SAAA,CAAA6D,QAAA,CAAAb,MAAA,CAAApD,IAAA,CAAAG,aAAA;UACA;QACA;QACA,IAAAiD,MAAA,CAAApD,IAAA,CAAAI,SAAA,CAAA2D,IAAA,OAAAX,MAAA,CAAApD,IAAA,cAAAgE,KAAA,MAAAC,QAAA,CAAAJ,CAAA;UACA;QACA;QACA,IAAAT,MAAA,CAAApD,IAAA,CAAAK,SAAA,WAAAwD,CAAA,CAAAxD,SAAA,KAAA+C,MAAA,CAAApD,IAAA,CAAAK,SAAA;UACA;QACA;QACA,IAAA+C,MAAA,CAAApD,IAAA,CAAAM,IAAA,CAAAyD,IAAA,cAAAF,CAAA,CAAAvD,IAAA,CAAA2D,QAAA,CAAAb,MAAA,CAAApD,IAAA,CAAAM,IAAA;UACA;QACA;QACA;MACA;MAEA4D,OAAA,CAAAC,GAAA,SAAAvE,IAAA;MACA,CAAAA,IAAA,UAAAD,QAAA,CAAAC,IAAA;MACA,KAAAD,QAAA,CAAAI,KAAA,GAAA4D,SAAA,CAAAH,MAAA;MACA,KAAAC,OAAA,CAAAE,SAAA;IACA;IACAS,YAAA,WAAAA,aAAA;MAAA,IAAAC,YAAA;MACA,KAAApD,cAAA;MACA,KAAAqD,WAAA;MACA,KAAAD,YAAA,QAAAX,MAAA,cAAAW,YAAA,eAAAA,YAAA,CAAAb,MAAA;QACA,KAAAE,MAAA,CAAAa,OAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAV,OAAA;QAAA;QACA,KAAAX,UAAA;MACA;IACA;IACAsB,YAAA,WAAAA,aAAA/E,IAAA;MACA,KAAAsB,iBAAA,GAAAtB,IAAA;IACA;IACAgF,cAAA,WAAAA,eAAAC,KAAA;MACAT,OAAA,CAAAC,GAAA,UAAAQ,KAAA;MACA,KAAA1D,cAAA,QAAAyC,MAAA,CAAAE,MAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,OAAA;MAAA;IACA;IACAQ,WAAA,WAAAA,YAAA;MACA,KAAAM,KAAA,CAAAC,OAAA,CAAAC,gBAAA;MACA,KAAA7D,cAAA;IACA;IACAmB,SAAA,WAAAA,UAAA;MAAA,IAAA2C,MAAA;MAAA,OAAAxC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAuC,SAAA;QAAA,OAAAxC,mBAAA,GAAAI,IAAA,UAAAqC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnC,IAAA,GAAAmC,SAAA,CAAAlC,IAAA;YAAA;cACA+B,MAAA,CAAAtE,SAAA;cAAA,KACAsE,MAAA,CAAAlD,KAAA;gBAAAqD,SAAA,CAAAlC,IAAA;gBAAA;cAAA;cAAAkC,SAAA,CAAAlC,IAAA;cAAA,OACA+B,MAAA,CAAAI,YAAA;YAAA;cAAAD,SAAA,CAAAlC,IAAA;cAAA;YAAA;cAAAkC,SAAA,CAAAlC,IAAA;cAAA,OAEA+B,MAAA,CAAAK,aAAA;YAAA;cAEAL,MAAA,CAAAM,UAAA;cACAN,MAAA,CAAA5B,UAAA;cACA4B,MAAA,CAAAtE,SAAA;YAAA;YAAA;cAAA,OAAAyE,SAAA,CAAAhC,IAAA;UAAA;QAAA,GAAA8B,QAAA;MAAA;IACA;IACAM,WAAA,WAAAA,YAAA;MAAA,IAAAC,aAAA;MACA,KAAAA,aAAA,QAAA7B,MAAA,cAAA6B,aAAA,eAAAA,aAAA,CAAA/B,MAAA;QACA,KAAA7D,QAAA,CAAAC,IAAA;QACA,KAAA8D,MAAA,QAAAA,MAAA,CAAAE,MAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAA2B,mBAAA;QAAA;QACA,KAAArC,UAAA;MACA;IACA;IACAsC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAhF,WAAA;MACAiF,UAAA;QACAD,MAAA,CAAAzE,cAAA,CAAAsD,OAAA,WAAAC,IAAA;UACA,IAAAoB,QAAA,GAAAC,QAAA,CAAArB,IAAA,CAAAsB,KAAA;UACAtB,IAAA,CAAAuB,cAAA,IAAAH,QAAA;UACApB,IAAA,CAAAgB,mBAAA,IAAAI,QAAA;UACApB,IAAA,CAAAwB,oBAAA,GAAAxB,IAAA,CAAAgB,mBAAA,GAAAhB,IAAA,CAAAyB,MAAA;UACAzB,IAAA,CAAA0B,QAAA,GAAA1B,IAAA,CAAAgB,mBAAA;UACAhB,IAAA,CAAA2B,WAAA,GAAAP,QAAA;UACApB,IAAA,CAAAsB,KAAA,GAAAtB,IAAA,CAAAgB,mBAAA;UACAhB,IAAA,CAAAV,OAAA;QACA;QACA,IAAAsC,EAAA,GAAAtH,SAAA,CAAA4G,MAAA,CAAAzE,cAAA;QAEAyE,MAAA,CAAAW,KAAA,mBAAAD,EAAA;QACAV,MAAA,CAAAW,KAAA;QACAX,MAAA,CAAApB,WAAA;QACAoB,MAAA,CAAAjC,OAAA;MACA;IACA;IACA4B,UAAA,WAAAA,WAAA;MAAA,IAAAiB,aAAA;QAAAC,MAAA;MACA,OAAAD,aAAA,QAAA5C,MAAA,cAAA4C,aAAA,eAAAA,aAAA,CAAA9C,MAAA;QACA,KAAAE,MAAA;QACA,KAAA8C,SAAA;QACA;MACA;MACA;MACA,IAAAC,MAAA;MACA,KAAA/C,MAAA,CAAAa,OAAA,WAAAC,IAAA;QACA+B,MAAA,CAAAG,IAAA,CAAAlC,IAAA,WAAAA,IAAA,CAAAgB,mBAAA;QACAe,MAAA,CAAAG,IAAA,CAAAlC,IAAA,cAAAA,IAAA,CAAAgB,mBAAA;QACAhB,IAAA,CAAAmC,IAAA,GAAAhI,MAAA;QACA8H,MAAA,CAAAjC,IAAA,CAAAjE,IAAA;MACA;MACA,KAAAiG,SAAA,GAAA1H,SAAA,MAAA4E,MAAA;IACA;IACAyB,YAAA,WAAAA,aAAA;MAAA,IAAAyB,MAAA;MAAA,OAAArE,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAoE,SAAA;QAAA,IAAAC,mBAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,WAAA,EAAAC,UAAA,EAAAC,GAAA,EAAAC,KAAA;QAAA,OAAA5E,mBAAA,GAAAI,IAAA,UAAAyE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvE,IAAA,GAAAuE,SAAA,CAAAtE,IAAA;YAAA;cAAA8D,mBAAA,GACAF,MAAA,CAAAW,MAAA,CAAAC,KAAA,EAAAT,OAAA,GAAAD,mBAAA,CAAAC,OAAA,EAAAC,MAAA,GAAAF,mBAAA,CAAAE,MAAA;cAAAC,WAAA,GACAL,MAAA,CAAA5G,IAAA,EAAAkH,UAAA,GAAAD,WAAA,CAAAC,UAAA,EAAAC,GAAA,GAAAM,wBAAA,CAAAR,WAAA,EAAAS,SAAA;cACAN,KAAA;cACA,IAAAO,MAAA,CAAAC,SAAA,CAAAC,QAAA,CAAAC,IAAA,CAAAZ,UAAA;gBACAE,KAAA,GAAAF,UAAA,IAAAA,UAAA,CAAAlD,KAAA,MAAAJ,MAAA,WAAAC,CAAA;kBAAA,SAAAA,CAAA;gBAAA;cACA;cAAAyD,SAAA,CAAAtE,IAAA;cAAA,OACAxE,oBAAA,CAAAuJ,aAAA,CAAAA,aAAA,KACAZ,GAAA;gBACAa,iBAAA,EAAApB,MAAA,CAAAzH,UAAA;gBACA+H,UAAA,EAAAE,KAAA;gBACAa,cAAA,EAAAlB,OAAA;gBACAmB,OAAA,EAAAlB;cAAA,EACA,EAAAmB,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAzB,MAAA,CAAAjH,QAAA,CAAAI,KAAA,GAAAqI,GAAA,CAAAE,IAAA,CAAA9E,MAAA;kBACAoD,MAAA,CAAAlD,MAAA,GAAA0E,GAAA,CAAAE,IAAA,CAAAC,GAAA,WAAA1E,CAAA,EAAA2E,GAAA;oBACA;oBACA3E,CAAA,CAAA4E,YAAA,GAAA5E,CAAA,CAAA6E,yBAAA,GAAA7E,CAAA,CAAA6E,yBAAA;oBACA7E,CAAA,CAAA8E,WAAA,GAAA9E,CAAA,CAAA+E,qBAAA;oBACA/E,CAAA,CAAAgF,aAAA,GAAAhF,CAAA,CAAAiF,uBAAA;oBACAjF,CAAA,CAAAkF,eAAA,GAAAlF,CAAA,CAAA6E,yBAAA,IAAA7E,CAAA,CAAAkF,eAAA;oBACA,IAAAlF,CAAA,CAAA4E,YAAA;sBACA5E,CAAA,CAAAmF,UAAA;oBACA;oBACAnF,CAAA,CAAAC,OAAA;oBACAD,CAAA,CAAAoF,YAAA,GAAAT,GAAA;oBACA,OAAA3E,CAAA;kBACA;kBACA+C,MAAA,CAAAnD,OAAA;gBACA;kBACAmD,MAAA,CAAAsC,QAAA;oBACAC,OAAA,EAAAf,GAAA,CAAAgB,OAAA;oBACAhK,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAkI,SAAA,CAAApE,IAAA;UAAA;QAAA,GAAA2D,QAAA;MAAA;IACA;IACA;AACA;AACA;IACAwC,gBAAA,WAAAA,iBAAAC,IAAA;MAAA,IAAAC,WAAA,GAAAD,IAAA,CAAAC,WAAA;QAAA1J,QAAA,GAAAyJ,IAAA,CAAAzJ,QAAA;MACAqE,OAAA,CAAAC,GAAA,2BAAAoF,WAAA,EAAA1J,QAAA;MACA,SAAAY,SAAA;MACA,KAAAd,QAAA,CAAAC,IAAA,GAAA2J,WAAA;MACA,KAAA5J,QAAA,CAAAE,QAAA,GAAAA,QAAA;MACA,KAAA4D,OAAA;MACA,KAAAN,UAAA,CAAAoG,WAAA;IACA;IAEA9F,OAAA,WAAAA,QAAA;MAAA,IAAA+F,EAAA,GAAAC,SAAA,CAAAjG,MAAA,QAAAiG,SAAA,QAAAC,SAAA,GAAAD,SAAA,WAAA/F,MAAA;MACA,KAAA9C,MAAA,GAAA4I,EAAA,CAAAG,KAAA,OAAAhK,QAAA,CAAAC,IAAA,aAAAD,QAAA,CAAAE,QAAA,OAAAF,QAAA,CAAAC,IAAA,QAAAD,QAAA,CAAAE,QAAA;IACA;IAEAuF,aAAA,WAAAA,cAAA;MAAA,IAAAwE,MAAA;MAAA,OAAArH,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAoH,SAAA;QAAA,IAAAC,mBAAA,EAAA/C,OAAA,EAAAC,MAAA;QAAA,OAAAxE,mBAAA,GAAAI,IAAA,UAAAmH,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjH,IAAA,GAAAiH,SAAA,CAAAhH,IAAA;YAAA;cAAA8G,mBAAA,GACAF,MAAA,CAAArC,MAAA,CAAAC,KAAA,EAAAT,OAAA,GAAA+C,mBAAA,CAAA/C,OAAA,EAAAC,MAAA,GAAA8C,mBAAA,CAAA9C,MAAA;cAAAgD,SAAA,CAAAhH,IAAA;cAAA,OACAvE,WAAA,CAAAsJ,aAAA,CAAAA,aAAA,KACA6B,MAAA,CAAA5J,IAAA;gBACAgI,iBAAA,EAAA4B,MAAA,CAAAzK,UAAA;gBACA8I,cAAA,EAAAlB,OAAA;gBACAmB,OAAA,EAAAlB;cAAA,EACA,EAAAmB,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAuB,MAAA,CAAAjK,QAAA,CAAAI,KAAA,GAAAqI,GAAA,CAAAE,IAAA,CAAA9E,MAAA;kBACAoG,MAAA,CAAAlG,MAAA,GAAA0E,GAAA,CAAAE,IAAA,CAAAC,GAAA,WAAA1E,CAAA,EAAA2E,GAAA;oBACA,IAAA3E,CAAA,CAAAoG,yBAAA;sBACA,IAAAC,IAAA,GAAArG,CAAA,CAAAoG,yBAAA,CAAAjG,KAAA;sBACA,IAAAkG,IAAA,CAAA1G,MAAA,IAAA0G,IAAA,CAAAC,IAAA,WAAAC,CAAA;wBAAA,OAAAA,CAAA,KAAAvG,CAAA,CAAAwG,iBAAA;sBAAA;wBACAxG,CAAA,CAAAyG,mBAAA,GAAAzG,CAAA,CAAAwG,iBAAA;sBACA;wBACAxG,CAAA,CAAAyG,mBAAA;wBACAzG,CAAA,CAAAwG,iBAAA;sBACA;oBACA;oBACAxG,CAAA,CAAA4E,YAAA,GAAA5E,CAAA,CAAA6E,yBAAA,GAAA7E,CAAA,CAAA6E,yBAAA;oBACA7E,CAAA,CAAA8E,WAAA,GAAA9E,CAAA,CAAA+E,qBAAA;oBACA/E,CAAA,CAAAgF,aAAA,GAAAhF,CAAA,CAAAiF,uBAAA;oBACAjF,CAAA,CAAAwG,iBAAA,GAAAxG,CAAA,CAAA0G,sBAAA,IAAA1G,CAAA,CAAAwG,iBAAA;oBACAxG,CAAA,CAAAkF,eAAA,GAAAlF,CAAA,CAAA6E,yBAAA,IAAA7E,CAAA,CAAAkF,eAAA;oBACAlF,CAAA,CAAAmF,UAAA,KAAAnF,CAAA,CAAA4E,YAAA;oBACA5E,CAAA,CAAAC,OAAA;oBACAD,CAAA,CAAAoF,YAAA,GAAAT,GAAA;oBACA,OAAA3E,CAAA;kBACA;kBACA+F,MAAA,CAAAY,aAAA;kBACAZ,MAAA,CAAAnG,OAAA;gBACA;kBACAmG,MAAA,CAAAV,QAAA;oBACAC,OAAA,EAAAf,GAAA,CAAAgB,OAAA;oBACAhK,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA4K,SAAA,CAAA9G,IAAA;UAAA;QAAA,GAAA2G,QAAA;MAAA;IACA;IACAW,aAAA,WAAAA,cAAA;MACA;MACA,KAAAhK,WAAA,QAAAkD,MAAA,CAAA+G,KAAA,WAAA5G,CAAA;QAAA,QAAAA,CAAA,CAAA6G,qBAAA;MAAA;MACAxG,OAAA,CAAAC,GAAA,0BAAA3D,WAAA;MACA,SAAAA,WAAA;QACA,IAAAgI,GAAA,QAAA7H,OAAA,CAAAgK,SAAA,WAAA9G,CAAA;UAAA,OAAAA,CAAA,CAAA+G,IAAA;QAAA;QACApC,GAAA,gBAAA7H,OAAA,CAAAkK,MAAA,CAAArC,GAAA;MACA;IACA;IACAsC,SAAA,WAAAA,UAAAZ,IAAA;MAAA,IAAAa,MAAA;MACAb,IAAA,CACA3F,OAAA,WAAAyG,OAAA;QACA,IAAAxC,GAAA,GAAAuC,MAAA,CAAAvE,SAAA,CAAAmE,SAAA,CACA,UAAAnG,IAAA;UAAA,OAAAwG,OAAA,CAAAC,KAAA,IAAAzG,IAAA,CAAAmC,IAAA,KAAAqE,OAAA,CAAAC,KAAA;QAAA,CACA;QACA,IAAAzC,GAAA;UACAuC,MAAA,CAAArH,MAAA,CAAAmH,MAAA,CAAArC,GAAA,KAAA1J,SAAA,CAAAiM,MAAA,CAAAvE,SAAA,CAAAgC,GAAA;QACA;MACA;MAEA,KAAA9E,MAAA,CAAAwH,IAAA,WAAAC,CAAA,EAAAC,CAAA;QAAA,OAAAD,CAAA,CAAAlC,YAAA,GAAAmC,CAAA,CAAAnC,YAAA;MAAA;MAEA,KAAA9F,UAAA;IACA;IACAkI,WAAA,WAAAA,YAAA;MACA,KAAAhF,KAAA;IACA;IACAiF,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAC,GAAA,GAAAD,KAAA,CAAAC,GAAA;QAAAC,MAAA,GAAAF,KAAA,CAAAE,MAAA;QAAAC,WAAA,GAAAH,KAAA,CAAAG,WAAA;MACA,OAAAD,MAAA,CAAAE,KAAA;IACA;IACA1I,cAAA,WAAAA,eAAAN,IAAA;MAAA,IAAAiJ,MAAA;MAAA,OAAArJ,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAoJ,SAAA;QAAA,OAAArJ,mBAAA,GAAAI,IAAA,UAAAkJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhJ,IAAA,GAAAgJ,SAAA,CAAA/I,IAAA;YAAA;cAAA+I,SAAA,CAAA/I,IAAA;cAAA,OACAzE,aAAA;gBACAoE,IAAA,EAAAA;cACA,GAAAwF,IAAA,WAAAC,GAAA;gBACA,IAAAC,SAAA,GAAAD,GAAA,CAAAC,SAAA;kBAAAC,IAAA,GAAAF,GAAA,CAAAE,IAAA;kBAAAc,OAAA,GAAAhB,GAAA,CAAAgB,OAAA;gBACA,IAAAf,SAAA;kBACAuD,MAAA,CAAA/K,QAAA,GAAA8G,MAAA,CAAAqE,MAAA,KAAAJ,MAAA,CAAA/K,QAAA,EAAAyH,IAAA,CAAA2D,IAAA;kBACAL,MAAA,CAAAjM,QAAA,CAAAE,QAAA,GAAAqM,MAAA,CAAAN,MAAA,CAAA/K,QAAA,CAAAsL,UAAA;kBACA,IAAAjC,IAAA,GAAA5B,IAAA,CAAA8D,UAAA;kBACAR,MAAA,CAAAjL,OAAA,GAAAuJ,IAAA,CAAAtG,MAAA,WAAAC,CAAA;oBAAA,OAAAA,CAAA,CAAAwI,UAAA;kBAAA,GAAA9D,GAAA,WAAA/D,IAAA;oBACA,IAAA5F,UAAA,CAAAqF,QAAA,CAAAO,IAAA,CAAAoG,IAAA;sBACApG,IAAA,CAAA8H,KAAA;oBACA;oBACA,OAAA9H,IAAA;kBACA;kBACAoH,MAAA,CAAAjL,OAAA,CAAA4C,IAAA;oBACAgJ,YAAA;oBACA3B,IAAA;kBACA;gBACA;kBACAgB,MAAA,CAAA1C,QAAA;oBACAC,OAAA,EAAAC,OAAA;oBACAhK,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA2M,SAAA,CAAA7I,IAAA;UAAA;QAAA,GAAA2I,QAAA;MAAA;IACA;IACA3J,iBAAA,WAAAA,kBAAA;MAAA,IAAAsK,MAAA;MACAxN,eAAA;QAAAyN,YAAA;MAAA,GAAAtE,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAmE,MAAA,CAAAlL,cAAA,CAAA5B,IAAA,GAAA0I,GAAA,CAAAE,IAAA;UACAkE,MAAA,CAAAE,SAAA,WAAAC,CAAA;YACAH,MAAA,CAAA5H,KAAA,CAAAgI,oBAAA,CAAAC,iBAAA,CAAAzE,GAAA,CAAAE,IAAA;UACA;QACA;UACAkE,MAAA,CAAAtD,QAAA;YACA9J,IAAA;YACA+J,OAAA,EAAAf,GAAA,CAAAgB;UACA;QACA;MACA;IACA;IACAjH,OAAA,WAAAA,QAAA;MAAA,IAAA2K,MAAA;MACA7N,eAAA;QAAA8N,UAAA;MAAA,GAAA5E,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAyE,MAAA,CAAAnL,UAAA,GAAAyG,GAAA,CAAAE,IAAA;QACA;UACAwE,MAAA,CAAA5D,QAAA;YACAC,OAAA,EAAAf,GAAA,CAAAgB,OAAA;YACAhK,IAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
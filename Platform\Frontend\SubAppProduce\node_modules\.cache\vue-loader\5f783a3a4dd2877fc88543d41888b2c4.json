{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\change-management\\contact-list\\components\\HandleEdit.vue?vue&type=style&index=0&id=05a92672&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\change-management\\contact-list\\components\\HandleEdit.vue", "mtime": 1757468112546}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmhhbmRsZS1lZGl0LWNvbnRhaW5lciB7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgLmhhbmRsZS1lZGl0IHsNCiAgICBtYXgtaGVpZ2h0OiA1MHZoOw0KICAgIG92ZXJmbG93LXk6IGF1dG87DQogICAgLmZsZXgtcm93IHsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICAuZmxleC1pdGVtIHsNCiAgICAgIGZsZXg6IDI7DQogICAgICBtYXJnaW46IDEwcHggMDsNCiAgICB9DQogICAgLmZsZXgtaXRlbS0xIHsNCiAgICAgIGZsZXg6IDM7DQogICAgfQ0KICAgIC5mbHhlLWl0ZW0yIHsNCiAgICAgIG1hcmdpbjowIDEwcHg7DQogICAgfQ0KICAgIC5mbGV4LWl0ZW0zIHsNCiAgICAgIGZsZXg6IDE7DQogICAgfQ0KICAgIC5idG4teCB7DQogICAgICBtYXJnaW4tbGVmdDogMTBweDsNCg0KICAgIH0NCiAgfQ0KDQp9DQouYnRuLWdyb3VwIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7DQogICAgcGFkZGluZy10b3A6IDEwcHg7DQogICAgYmFja2dyb3VuZDogI2ZmZjsNCiAgfQ0KfQ0KLmNzLXRyZWUteCB7DQogIDo6di1kZWVwIHsNCiAgICAuZWwtc2VsZWN0IHsNCiAgICAgIHdpZHRoOiAxMDAlOw0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["HandleEdit.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+XA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "HandleEdit.vue", "sourceRoot": "src/views/PRO/change-management/contact-list/components", "sourcesContent": ["<template>\r\n  <div class=\"handle-edit-container\">\r\n    <div class=\"handle-edit\">\r\n      <el-button v-if=\"!list.length\" type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd(-1)\">添加</el-button>\r\n      <div v-for=\"(item, index) in list\" :key=\"index\" class=\"flex-row\">\r\n        <div class=\"flex-item flex-item-1\">\r\n          <el-input\r\n            v-model=\"item.Value\"\r\n            style=\"width: 100%;\"\r\n            clearable\r\n            readonly\r\n            placeholder=\"请输入\"\r\n            class=\"input-with-select\"\r\n          >\r\n            <el-select\r\n              slot=\"prepend\"\r\n              v-model=\"item.Code\"\r\n              placeholder=\"请选择\"\r\n              style=\"width: 160px\"\r\n              @change=\"selectChange($event,item)\"\r\n            >\r\n              <el-option v-for=\"option in getAvailableOptions(index)\" :key=\"option.Code\" :label=\"option.Name\" :value=\"option.Code\" />\r\n            </el-select>\r\n\r\n          </el-input>\r\n        </div>\r\n        <div class=\"flxe-item2\">\r\n          <span>变更后：</span>\r\n        </div>\r\n        <div class=\"flex-item\">\r\n\r\n          <el-tree-select\r\n            v-if=\"item.Code === 'SteelType'\"\r\n            ref=\"treeSelectObjectType1\"\r\n            v-model=\"item.NewValue\"\r\n            class=\"cs-tree-x\"\r\n            style=\"width: 100%;\"\r\n            :select-params=\"treeSelectParams\"\r\n            :tree-params=\"ObjectTypeList\"\r\n            value-key=\"Id\"\r\n          />\r\n          <el-select v-else-if=\"item.Code==='PartType'\" v-model=\"item.NewValue\" placeholder=\"请选择\" clearable=\"\">\r\n            <el-option\r\n              v-for=\"cur in partTypeOption\"\r\n              :key=\"cur.Code\"\r\n              :label=\"cur.Name\"\r\n              :value=\"cur.Code\"\r\n            />\r\n          </el-select>\r\n          <el-select v-else-if=\"item.Code==='Technology_Code'\" v-model=\"item.NewValue\" placeholder=\"请选择\" clearable=\"\">\r\n            <el-option\r\n              v-for=\"cur in processOption\"\r\n              :key=\"cur.Code\"\r\n              :label=\"cur.Code\"\r\n              :value=\"cur.Code\"\r\n            />\r\n          </el-select>\r\n          <el-input-number\r\n            v-else-if=\"item.Field_Type==='number'\"\r\n            v-model=\"item.NewValue\"\r\n            :min=\"0\"\r\n            :precision=\"item.precision || 0\"\r\n            style=\"width: 100%\"\r\n            class=\"cs-number-btn-hidden\"\r\n          />\r\n          <el-input v-else v-model.trim=\"item.NewValue\" style=\"width: 100%;\" clearable placeholder=\"请输入\" />\r\n\r\n        </div>\r\n        <div class=\"flex-item3 btn-x\">\r\n          <el-button\r\n            v-if=\"(list.length < options.length)\"\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            circle\r\n            @click=\"handleAdd(index)\"\r\n          />\r\n          <el-button\r\n            type=\"danger\"\r\n            icon=\"el-icon-delete\"\r\n            circle\r\n            @click=\"handleDelete(item, index)\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"btn-group\">\r\n      <el-button @click=\"handleClose\">取消</el-button>\r\n      <el-button type=\"primary\" :disabled=\"isSaveDisabled\" @click=\"handleSave\">保存</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { allCodes, defaultPrefix, filterByCodeType, generateAllCodes, getAllCodesByType } from '../utils'\r\nimport { GetCompTypeTree } from '@/api/PRO/component-type'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\nimport { GetLibList } from '@/api/PRO/technology-lib'\r\nimport { deepClone } from '@/utils'\r\nimport { CheckCanMocName } from '@/api/PRO/changeManagement'\r\n\r\nexport default {\r\n\r\n  data() {\r\n    return {\r\n      list: [{\r\n        Value: '',\r\n        NewValue: '',\r\n        Field_Type: '',\r\n        IsCoreField: '',\r\n        precision: '',\r\n        Code: ''\r\n      }],\r\n      options: [],\r\n      partTypeOption: [],\r\n      isSaveDisabled: false,\r\n      processOption: [],\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        clearable: true\r\n      },\r\n      ObjectTypeList: {\r\n        // 构件类型\r\n        'check-strictly': true,\r\n        'default-expand-all': true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    init(row, defaultRow, isEdit, tbData, allCodes) {\r\n      const tbCode = tbData.map(item => item.CPCode)\r\n      this._tbData = JSON.parse(JSON.stringify(tbData))\r\n      generateAllCodes(allCodes)\r\n      this.row = row\r\n      this.tbCode = tbCode\r\n      this.defaultRow = defaultRow\r\n      console.log('isEdit', isEdit)\r\n      console.log('row1', row)\r\n      const _columns = filterByCodeType(row.CodeType)\r\n      this.options = _columns.map(v => ({\r\n        Code: v.Code,\r\n        Name: v.Display_Name,\r\n        disabled: false,\r\n        Field_Type: v.Field_Type,\r\n        precision: v.precision,\r\n        IsCoreField: v.IsCoreField\r\n      }))\r\n      console.log('this.options', JSON.parse(JSON.stringify(this.options)))\r\n\r\n      const changeCode = deepClone(this.$store.state.contactList.changeCode)\r\n      if (changeCode[this.row.uuid]) {\r\n        this.list = changeCode[this.row.uuid]\r\n        const selectItem = this.list.filter(v => v.Field_Type === 'select')\r\n        if (selectItem.length) {\r\n          if (selectItem.some(v => v.Code === 'SteelType')) {\r\n            this.getObjectTypeList()\r\n          }\r\n          if (selectItem.some(v => v.Code === 'PartType')) {\r\n            this.getPartTypeList()\r\n          }\r\n          if (selectItem.some(v => v.Code === 'Technology_Code')) {\r\n            this.getLibList()\r\n          }\r\n        }\r\n      }\r\n    },\r\n\r\n    handleClose() {\r\n      this.$emit('close')\r\n    },\r\n    handleAdd(index) {\r\n      this.list.splice(index + 1, 0, {\r\n        Value: '',\r\n        NewValue: '',\r\n        Field_Type: '',\r\n        IsCoreField: '',\r\n        precision: 0,\r\n        Code: ''\r\n      })\r\n    },\r\n    getObjectTypeList() {\r\n      if (this.row.Type === 0) {\r\n        GetCompTypeTree({ professional: 'Steel' }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.ObjectTypeList.data = res.Data\r\n            this.$nextTick((_) => {\r\n              this.$refs.treeSelectObjectType1[0].treeDataUpdateFun(res.Data)\r\n            })\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        })\r\n      } else {\r\n        GetPartTypeList({ Part_Grade: 0 }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.partTypeOption = res.Data\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n    },\r\n    selectChange(code, item) {\r\n      console.log('code', code, item)\r\n      const cur = this.options.find(v => v.Code === code)\r\n      console.log(cur, 'cur')\r\n      console.log(this.defaultRow, 'this.defaultRow')\r\n      item.Field_Type = cur.Field_Type\r\n      item.IsCoreField = cur.IsCoreField\r\n      item.precision = cur.precision\r\n      item.Name = cur.Name\r\n      item.Value = this.defaultRow[code]\r\n      item.NewValue = undefined\r\n      if (code === 'SteelType' || code === 'PartType') {\r\n        this.getObjectTypeList()\r\n      }\r\n      if (code === 'Technology_Code') {\r\n        this.getLibList()\r\n      }\r\n    },\r\n    handleDelete(element, index) {\r\n      const idx = this.list.findIndex(v => v.Code === element.Code)\r\n      console.log(idx, 'omd')\r\n      if (idx !== -1) {\r\n        this.list.splice(idx, 1)\r\n      }\r\n    },\r\n    getAvailableOptions(currentIndex) {\r\n      const selectedCodes = this.list\r\n        .filter((item, idx) => idx !== currentIndex)\r\n        .map(item => item.Code)\r\n      return this.options.filter(option => !selectedCodes.includes(option.Code))\r\n    },\r\n    async handleSave() {\r\n      let success = true\r\n      const list = this.list.filter(item => !!item.Code)\r\n      console.log(list, 'list')\r\n\r\n      const isMustInputs = allCodes.filter(item => item.isMustInput).map(item => item.Code)\r\n      console.log(isMustInputs, 'isMustInputs')\r\n      list.forEach(item => {\r\n        if (isMustInputs.includes(item.Code)) {\r\n          if (!item.NewValue) {\r\n            this.$message({\r\n              message: '请输入' + item.Name,\r\n              type: 'error'\r\n            })\r\n            success = false\r\n            return\r\n          }\r\n        }\r\n      })\r\n\r\n      const isValid = await this.checkName()\r\n      console.log('isValid', isValid)\r\n      if (!isValid) {\r\n        success = false\r\n        return\r\n      }\r\n\r\n      // 根据Type判断唯一性字段：0-构件(SteelName), 1-部件(ComponentName), 2/3-零件(PartName)\r\n      // let hasRepeat = false\r\n      // let nameItem = null\r\n      // let nameField = ''\r\n      // if (this.row.Type === 0) {\r\n      //   nameField = 'SteelName'\r\n      // } else if (this.row.Type === 1) {\r\n      //   nameField = 'ComponentName'\r\n      // } else if (this.row.Type === 2 || this.row.Type === 3) {\r\n      //   nameField = 'PartName'\r\n      // }\r\n      // nameItem = list.find(v => v.Code === nameField)\r\n      // if (nameItem) {\r\n      //   const newName = nameItem.NewValue?.trim()\r\n      //   for (let i = 0; i < this._tbData.length; i++) {\r\n      //     const item = this._tbData[i]\r\n      //     if (item.CodeType === 3 || item.CodeType === 2) {\r\n      //       if (item.Part_Aggregate_Id === this.row.Part_Aggregate_Id) {\r\n      //         continue\r\n      //       }\r\n      //     } else if (item.CodeType === 1 && item.uuid === this.row.uuid) {\r\n      //       continue\r\n      //     }\r\n      //     // 只比较同类型的唯一性字段\r\n      //     if (item[nameField]?.trim() === newName && this.row.Type === item.Type) {\r\n      //       hasRepeat = true\r\n      //       break\r\n      //     }\r\n      //   }\r\n      // }\r\n\r\n      if (!success) return\r\n\r\n      // this.$store.dispatch('contactList/addChangeCode', { uuid: this.row.uuid, list: list })\r\n      this.$emit('editInfo', { row: this.row, list: list })\r\n      this.$emit('close')\r\n    },\r\n    async checkName() {\r\n      const item = this.list.find(v => v.Code === 'SteelName' || v.Code === 'ComponentName' || v.Code === 'PartName')\r\n      if (!item) return true\r\n      let flag = true\r\n      await CheckCanMocName({\r\n        Type: this.row.Type,\r\n        Id: this.row.Type === 0 ? this.row.MocIdBefore : this.row.MocAggregateIdBefore,\r\n        NewName: item.NewValue\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          flag = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          flag = false\r\n        }\r\n      })\r\n      if (!flag) {\r\n        return\r\n      }\r\n      const key = this.row.Type === 0 ? 'SteelName' : this.row.Type === 1 ? 'ComponentName' : 'PartName'\r\n      const hasSimilar = this.findSimilarTypeItems(this.row, key, item.NewValue)\r\n      console.log('hasSimilar', hasSimilar, item.NewValue)\r\n      if (hasSimilar) {\r\n        this.$message({\r\n          message: `${this.row.Type === 0 ? '构件' : this.row.Type === 1 ? '部件' : '零件'}名称已存在，请修改`,\r\n          type: 'error'\r\n        })\r\n        flag = false\r\n      }\r\n      return flag\r\n    },\r\n    findSimilarTypeItems(targetItem, key, newName) {\r\n      let flag = false\r\n      for (let i = 0; i < this._tbData.length; i++) {\r\n        const item = this._tbData[i]\r\n        if (item.uuid === targetItem.uuid) continue\r\n        if (item.CodeType !== targetItem.CodeType) continue\r\n        if (item.Type === 1 || item.Type === 2 || item.Type === 3) {\r\n          if ((targetItem.MocAggregateIdBefore !== item.MocAggregateIdBefore) && (item[key] === newName)) {\r\n            flag = true\r\n          }\r\n        } else if (item.Type === 0) {\r\n          if (item[key] === newName) {\r\n            flag = true\r\n          }\r\n        }\r\n      }\r\n      return flag\r\n    },\r\n    handleCancel() {\r\n      this.$emit('close')\r\n    },\r\n    getLibList() {\r\n      GetLibList({\r\n        type: this.row.Type === 0 ? 1 : this.row.Type === 1 ? 3 : 2\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.processOption = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.handle-edit-container {\r\n  position: relative;\r\n  .handle-edit {\r\n    max-height: 50vh;\r\n    overflow-y: auto;\r\n    .flex-row {\r\n      display: flex;\r\n      align-items: center;\r\n    justify-content: space-between;\r\n    .flex-item {\r\n      flex: 2;\r\n      margin: 10px 0;\r\n    }\r\n    .flex-item-1 {\r\n      flex: 3;\r\n    }\r\n    .flxe-item2 {\r\n      margin:0 10px;\r\n    }\r\n    .flex-item3 {\r\n      flex: 1;\r\n    }\r\n    .btn-x {\r\n      margin-left: 10px;\r\n\r\n    }\r\n  }\r\n\r\n}\r\n.btn-group {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    padding-top: 10px;\r\n    background: #fff;\r\n  }\r\n}\r\n.cs-tree-x {\r\n  ::v-deep {\r\n    .el-select {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
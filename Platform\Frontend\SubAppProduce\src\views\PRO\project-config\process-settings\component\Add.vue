<template>
  <div class="form-wrapper">
    <div class="form-x">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" style="width: 100%">
        <el-divider content-position="left">基础信息</el-divider>
        <el-form-item label="名称" prop="Name">
          <el-input v-model="form.Name" :maxlength="30" placeholder="最多30个字" show-word-limit />
        </el-form-item>
        <el-form-item label="代号" prop="Code">
          <el-input
            v-model="form.Code"
            :maxlength="30"
            placeholder="字母+数字，30字符"
            show-word-limit
            @input="(e) => (form.Code = codeChange(e))"
          />
        </el-form-item>
        <el-form-item label="类型" prop="Bom_Level">
          <el-radio-group
            v-for="(item, index) in bomList"
            :key="index"
            v-model="form.Bom_Level"
            class="radio"
            @change="changeType"
          >
            <el-radio style="margin-right: 8px;" :label="item.Code">{{ item.Display_Name }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序" prop="Sort">
          <el-input-number
            v-model="form.Sort"
            :min="0"
            step-strictly
            :step="1"
            class="cs-number-btn-hidden w100"
            placeholder="请输入"
            clearable=""
          />
        </el-form-item>
        <el-form-item label="协调人" prop="Coordinate_UserId">
          <el-select v-model="form.Coordinate_UserId" class="w100" clearable filterable placeholder="请选择">
            <el-option v-for="item in optionsUserList" :key="item.Id" :label="item.Display_Name" :value="item.Id" />
          </el-select>
        </el-form-item>
        <el-form-item label="工序月均负荷" prop="Month_Avg_Load">
          <el-input v-model="form.Month_Avg_Load" placeholder="请输入">
            <template slot="append">吨</template>
          </el-input>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否启用" prop="Is_Enable">
              <el-radio-group v-model="form.Is_Enable">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否外协" prop="Is_External">
              <el-radio-group v-model="form.Is_External">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否装焊工序" prop="Is_Welding_Assembling">
              <el-radio-group v-model="form.Is_Welding_Assembling">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.Bom_Level === '0'" label="是否下料工序" prop="Is_Cutting">
              <el-radio-group v-model="form.Is_Cutting">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item v-if="form.Bom_Level === '0'" label="是否套料工序" prop="Is_Nest">
              <el-radio-group v-model="form.Is_Nest">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.Bom_Level === '0'" label="是否领料工序" prop="Is_Pick_Material">
              <el-radio-group v-model="form.Is_Pick_Material">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="加工班组" prop="Working_Team_Ids">
          <el-select v-model="form.Working_Team_Ids" multiple style="width: 100%" placeholder="请选择加工班组">
            <el-option v-for="item in optionsWorkingTeamsList" :key="item.Id" :label="item.Name" :value="item.Id" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="form.Remark" type="textarea" />
        </el-form-item>
        <el-divider content-position="left">质检信息</el-divider>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否自检" prop="Is_Self_Check">
              <el-radio-group v-model="form.Is_Self_Check" @change="radioSelfCheck">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否互检" prop="Is_Inter_Check">
              <el-radio-group v-model="form.Is_Inter_Check" @change="radioInterCheck">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否专检" prop="Is_Need_Check">
              <el-radio-group v-model="form.Is_Need_Check" @change="radioChange">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <template v-if="form.Is_Need_Check">
              <el-form-item label="专检方式" prop="Check_Style">
                <el-radio-group v-model="form.Check_Style" @change="radioCheckStyleChange">
                  <el-radio label="0">抽检</el-radio>
                  <el-radio label="1">全检</el-radio>
                </el-radio-group>
              </el-form-item>
            </template>
          </el-col>
        </el-row>

        <template v-if="form.Is_Need_Check">
          <el-form-item label="专检类型" prop="">
            <div>
              <div style="margin-bottom: 10px;">
                <el-checkbox v-model="form.Is_Need_TC" @change="checkboxChange($event, 1)">
                  <span> 探伤</span>
                </el-checkbox>
                <span style="margin-left: 30px; ">
                  <span style="color: rgba(34, 40, 52, 0.65)">探伤员：</span>
                  <el-select
                    v-model="TC_Check_UserIds"
                    filterable
                    clearable
                    :disabled="!form.Is_Need_TC"
                    multiple
                    placeholder="请选择探伤员"
                    @change="changeTc"
                  >
                    <el-option
                      v-for="item in optionsUserList"
                      :key="item.Id"
                      :label="item.Display_Name"
                      :value="item.Id"
                    />
                  </el-select>
                </span>
              </div>
              <div>
                <el-checkbox v-model="form.Is_Need_ZL" @change="checkboxChange($event, 2)">
                  <span> 质量</span>
                </el-checkbox>
                <span style="margin-left: 30px">
                  <span style="color: rgba(34, 40, 52, 0.65)">质检员：</span>
                  <el-select
                    v-model="ZL_Check_UserIds"
                    :disabled="!form.Is_Need_ZL"
                    filterable
                    clearable
                    multiple
                    placeholder="请选择质检员"
                    @change="changeZL"
                  >
                    <el-option
                      v-for="item in optionsUserList"
                      :key="item.Id"
                      :label="item.Display_Name"
                      :value="item.Id"
                    />
                  </el-select>
                </span>
              </div>
            </div>
          </el-form-item>
        </template>
        <el-divider content-position="left">其他信息</el-divider>
        <el-form-item label="工作量占比" prop="Workload_Proportion">
          <el-input v-model="form.Workload_Proportion" placeholder="请输入" type="number">
            <template slot="append">%</template>
          </el-input>
        </el-form-item>
        <el-form-item label="是否展示模型" prop="Show_Model">
          <el-radio-group v-model="form.Show_Model">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
    <div class="btn-x">
      <el-button @click="$emit('close')">取 消</el-button>
      <el-button :loading="btnLoading" type="primary" @click="handleSubmit">确 定
      </el-button>
    </div>
  </div>
</template>

<script>
import { GetBOMInfo } from '@/views/PRO/bom-setting/utils'
import { GetUserList } from '@/api/sys'
import {
  AddWorkingProcess,
  GetFactoryPeoplelist,
  GetCheckGroupList,
  GetWorkingTeams
} from '@/api/PRO/technology-lib'
import { mapGetters } from 'vuex'
import { GetDictionaryDetailListByCode } from '@/api/sys'
export default {
  props: {
    type: {
      type: String,
      default: ''
    },
    rowInfo: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      checkList: [],
      btnLoading: false,
      hiddenPart: false,

      form: {
        Code: '',
        Name: '',
        Bom_Level: '',
        Month_Avg_Load: '',
        Coordinate_UserId: '',
        Sort: undefined,
        Is_Enable: true,
        Is_External: false,
        Is_Nest: false,
        Is_Need_Check: true,
        Is_Self_Check: true,
        Is_Inter_Check: true,
        Is_Pick_Material: false,
        Is_Need_TC: true,
        Is_Welding_Assembling: false,
        Is_Cutting: false,
        TC_Check_UserId: '',
        Is_Need_ZL: false,
        ZL_Check_UserId: '',
        Show_Model: false,

        Check_Style: '0',

        Working_Team_Ids: [],
        Remark: '',
        Workload_Proportion: ''
      },
      ZL_Check_UserIds: [],
      TC_Check_UserIds: [],
      CheckChange: true,
      userOptions: [],
      optionsUserList: [],
      optionsGroupList: [],
      optionsWorkingTeamsList: [],
      rules: {
        Code: [
          { required: true, message: '请输入代号', trigger: 'blur' },
          { max: 30, message: '长度在 30 个字符内', trigger: 'blur' }
        ],
        Name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          { max: 30, message: '长度在 30 个字符内', trigger: 'blur' }
        ],
        Bom_Level: [{ required: true, message: '请选择类型', trigger: 'change' }],
        Sort: [{ required: true, message: '请输入', trigger: 'blur' }],
        Is_Need_Check: [
          { required: true, message: '请选择是否质检', trigger: 'change' }
        ]
      },
      bomList: [],
      comName: '',
      partName: ''
    }
  },
  computed: {
    ...mapGetters('tenant', ['isVersionFour'])
  },
  async created() {
    const { comName, partName, list } = await GetBOMInfo()
    this.comName = comName
    this.partName = partName
    this.bomList = list
  },
  mounted() {
    this.getUserList()
    this.getFactoryPeoplelist()
    // this.getCheckGroupList();
    this.getWorkingTeamsList()
    this.type === 'edit' && this.initForm()
  },
  methods: {
    initForm() {
      const { Is_Nest, ...others } = this.rowInfo
      this.form = Object.assign({}, others, { Is_Nest: !!Is_Nest })
      this.form.Bom_Level = String(this.form.Bom_Level)
      //  if(this.form.Type==2){
      //   this.form.Types = '0'
      //  }else if(this.form.Type==3){
      //   let Types = this.radioList.find(v => ['1', '2','3'].includes(v.Code))?.Code
      //   console.log('Types', Types)
      //   console.log('this.radioList', this.radioList)
      //   this.form.Types = Types
      //  }else if(this.form.Type==1){
      //   this.form.Types = '-1'
      //  }
      console.log('this.form', this.form)

      // 处理历史数据多选问题
      // if (this.form.Is_Need_Check) {
      //   if (this.form.Check_Style === '1') {

      //   } else {
      //     this.CheckChange = !!this.form.Is_Need_TC
      //     if (this.form.Is_Need_ZL && this.form.Is_Need_TC) {
      //       this.form.Is_Need_TC = true
      //       this.form.Is_Need_ZL = false
      //     }
      //   }
      // }
      this.ZL_Check_UserIds = this.form.ZL_Check_UserId
        ? this.form.ZL_Check_UserId.split(',')
        : []
      this.TC_Check_UserIds = this.form.TC_Check_UserId
        ? this.form.TC_Check_UserId.split(',')
        : []
    },
    // 是否自检
    radioSelfCheck(val) { },
    // 是否互检
    radioInterCheck(val) { },
    // 获取设备类型
    async getDictionaryDetailListByCode() {
      await GetDictionaryDetailListByCode({ dictionaryCode: 'deviceType' }).then((res) => {
        if (res.IsSucceed) {
          this.deviceTypeList = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
      console.log(' this.optionsGroupList', this.optionsGroupList)
    },
    getUserList() {
      GetUserList({}).then((res) => {
        if (res.IsSucceed) {
          this.userOptions = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    getFactoryPeoplelist() {
      GetFactoryPeoplelist({}).then((res) => {
        if (res.IsSucceed) {
          this.optionsUserList = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    async getCheckGroupList() {
      await GetCheckGroupList({}).then((res) => {
        if (res.IsSucceed) {
          this.optionsGroupList = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
      console.log(' this.optionsGroupList', this.optionsGroupList)
    },
    getWorkingTeamsList() {
      GetWorkingTeams({}).then((res) => {
        if (res.IsSucceed) {
          this.optionsWorkingTeamsList = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    // 选择专检方式 抽检、全检
    radioCheckStyleChange(val) {
      // if (val === '0') {
      //   this.form.Is_Need_TC = true
      //   this.form.Is_Need_ZL = false
      // }
      this.ZL_Check_UserIds = []
      this.TC_Check_UserIds = []
      this.form.ZL_Check_UserId = ''
      this.form.TC_Check_UserId = ''
    },
    // 是否专检
    radioChange(val) {
      if (val === false) {
        this.form.checkChange = false
        this.form.Is_Need_TC = false
        this.form.Is_Need_ZL = false
        this.TC_Check_UserIds = []
        this.ZL_Check_UserIds = []
        this.form.ZL_Check_UserId = ''
        this.form.TC_Check_UserId = ''
        this.form.Check_Style = ''
      } else {
        // this.form.checkChange = true
        // this.form.Is_Need_TC = true
        // this.form.Is_Need_ZL = false
        // this.CheckChange = !!this.form.Is_Need_TC
        this.form.Check_Style = '0'
      }
    },
    // 选择BOM层级
    changeType(val) {
      // const Code = val
      // console.log(Code, 'Code');
      // if (Code === '-1') {
      //   this.form.Type = 1
      // } else if (Code === '0') {
      //   this.form.Type = 2
      // } else if (Code === '1' || Code === '3'|| Code === '2') {
      //   this.form.Type = 3
      // }
      // if (this.form.Type === 1 || this.form.Type === 3) {
      //   this.form.Is_Cutting = undefined
      // } else if (this.form.Type === 2) {
      //   this.form.Is_Welding_Assembling = undefined
      // }
    },
    typeChange() {
      this.form.Task_Model = ''
    },
    changeTc(val) {
      console.log(val)
      this.form.TC_Check_UserId = ''
      for (let i = 0; i < val.length; i++) {
        if (i === val.length - 1) {
          this.form.TC_Check_UserId += val[i]
        } else {
          this.form.TC_Check_UserId += val[i] + ','
        }
      }
      console.log(this.form.TC_Check_UserId, 'this.form.TC_Check_UserId ')
    },
    changeZL(val) {
      console.log(val)
      this.form.ZL_Check_UserId = ''
      for (let i = 0; i < val.length; i++) {
        if (i === val.length - 1) {
          this.form.ZL_Check_UserId += val[i]
        } else {
          this.form.ZL_Check_UserId += val[i] + ','
        }
      }
    },
    checkboxChange(val, type) {
      if (type === 1) {
        if (!val) {
          this.TC_Check_UserIds = []
        }
      }
      if (type === 2) {
        if (!val) {
          this.ZL_Check_UserIds = []
        }
      }
    },
    handleSubmit() {
      // delete this.form.Types
      console.log(this.form, 'this.form')
      this.$refs.form.validate((valid) => {
        if (!valid) return
        this.btnLoading = true
        const uItems = this.optionsUserList.find(
          (v) => v.Id === this.form.Coordinate_UserId
        )
        if (uItems) {
          this.form.Coordinate_UserName = uItems.Display_Name
        }
        if (this.form.Is_Need_Check) {
          if (this.form.Is_Need_ZL === false && this.form.Is_Need_TC === false) {
            this.$message.error('请选择质检类型')
            this.btnLoading = false
            return
          }
        } else {
          this.form.Check_Style = null
          this.form.Check_Group_List = []
        }
        const ZL = this.form.Is_Need_ZL ? this.form.ZL_Check_UserId : ''
        const TC = this.form.Is_Need_TC ? this.form.TC_Check_UserId : ''
        if (this.form.Is_Need_ZL && (ZL ?? '') === '') {
          this.$message.error('请选择质检员')
          this.btnLoading = false
          return
        }
        if (this.form.Is_Need_TC && (TC ?? '') === '') {
          this.$message.error('请选择探伤员')
          this.btnLoading = false
          return
        }

        AddWorkingProcess({
          ...this.form,
          ZL_Check_UserId: ZL,
          TC_Check_UserId: TC
        }).then((res) => {
          if (res.IsSucceed) {
            this.$message({
              message: '保存成功',
              type: 'success'
            })
            this.$emit('refresh')
            this.$emit('close')
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
          this.btnLoading = false
        })
      })
    },

    codeChange(e) {
      return e.replace(/[^a-zA-Z0-9]/g, '')
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";

.btn-del {
  margin-left: -100px;
}

.customRadioClass {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.checkboxFlex {
  display: flex;
  align-items: center;
}

.form-wrapper {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 40vh;

  .form-x {
    max-height: 70vh;
    overflow: auto;
    padding-right: 16px;
    @include scrollBar;
  }

  .btn-x {
    padding-top: 16px;
    text-align: right;
  }
}
</style>

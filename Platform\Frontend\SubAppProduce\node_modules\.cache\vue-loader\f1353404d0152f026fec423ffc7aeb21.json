{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\index.vue", "mtime": 1758266768055}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "index.vue", "sourceRoot": "src/views/PRO/project-config/product-mfg-path", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <ProjectData @setProjectData=\"setProjectData\" />\r\n    <div class=\"card-x\">\r\n      <div class=\"card-x-top\">\r\n        <el-button type=\"success\" :disabled=\"!sysProjectId\" @click=\"handleAdd\">新增</el-button>\r\n\r\n        <el-button type=\"primary\" :disabled=\"!sysProjectId\" @click=\"handleAddProject\">同步项目配置</el-button>\r\n        <el-button type=\"primary\" :disabled=\"!sysProjectId\" @click=\"handleReset\">恢复工厂默认配置</el-button>\r\n      </div>\r\n      <div class=\"table-section\">\r\n        <bt-table\r\n          ref=\"projectTable\"\r\n          code=\"ProductMftPathConfig\"\r\n          :custom-table-config=\"tableConfig\"\r\n          :grid-data-handler=\"handleGridData\"\r\n          :loading=\"loading\"\r\n        >\r\n          <template #Type=\"{row}\">\r\n            <div>\r\n              <span :style=\"{color:row.Type===1 ?'#d29730': row.Type===2?'#20bbc7':'#de85e4'}\">\r\n                {{ getCurBomName(row.Bom_Level) }}工艺\r\n              </span>\r\n            </div>\r\n          </template>\r\n          <template #Type1=\"{row}\">\r\n            {{ row.Component_Type }}\r\n          </template>\r\n          <template #actions=\"{row}\">\r\n            <div>\r\n              <el-button v-if=\"!row.isSysDefault\" type=\"text\" @click=\"handleEdit(row)\">编辑</el-button>\r\n              <el-button type=\"text\" style=\"color: red\" @click=\"handleDelete(row)\">删除</el-button>\r\n            </div>\r\n          </template>\r\n        </bt-table>\r\n\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"title\"\r\n      class=\"plm-custom-dialog\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"30%\"\r\n      top=\"5vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        ref=\"content\"\r\n        :sys-project-id=\"sysProjectId\"\r\n        @refresh=\"fetchData\"\r\n        @close=\"handleClose\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <AddDialog ref=\"dialog\" :bom-list=\"bomList\" :sys-project-id=\"sysProjectId\" @refresh=\"fetchData\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ProjectData from '../components/ProjectData.vue'\r\nimport { GetLibList, DeleteTechnology, RestoreFactoryTechnologyFromProject } from '@/api/PRO/technology-lib'\r\nimport ProjectAdd from './component/ProjectAddDialog.vue'\r\nimport AddDialog from '@/views/PRO/process-path/compoments/Add.vue'\r\nimport { GetBOMInfo, getBomName } from '@/views/PRO/bom-setting/utils'\r\n\r\nexport default {\r\n  name: 'PROProductMfgPath',\r\n  components: {\r\n    ProjectData,\r\n    ProjectAdd,\r\n    AddDialog\r\n  },\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      currentComponent: '',\r\n      bomList: [],\r\n      title: '',\r\n      sysProjectId: '',\r\n      tableConfig: {\r\n        tableColumns: [],\r\n        tableActions: [],\r\n        tableData: [],\r\n        operateOptions: {\r\n          width: 120,\r\n          align: 'center',\r\n          isShow: false\r\n        }\r\n      },\r\n      loading: false,\r\n      selectedProjects: []\r\n    }\r\n  },\r\n  async mounted() {\r\n    // this.fetchData()\r\n    const { list } = await GetBOMInfo()\r\n    this.bomList = list\r\n    console.log('bomList', this.bomList)\r\n  },\r\n  methods: {\r\n    getCurBomName(code) {\r\n      const currentBomInfo = this.bomList.find(item => {\r\n        return item.Code.toString() === code.toString()\r\n      })\r\n      return currentBomInfo?.Display_Name || ''\r\n    },\r\n    async fetchData() {\r\n      if (!this.sysProjectId) {\r\n        this.tableConfig.tableData = []\r\n        return\r\n      }\r\n      this.loading = true\r\n      try {\r\n        const params = {\r\n          // Bom_Level: 1,\r\n          Type: 0,\r\n          Sys_Project_Id: this.sysProjectId\r\n        }\r\n        const res = await GetLibList(params)\r\n        if (res.IsSucceed) {\r\n          this.tableConfig.tableData = await Promise.all(\r\n            res.Data.map(async v => {\r\n              v.isSysDefault = !v.Sys_Project_Id\r\n              v.bomName = await getBomName(v.Type)\r\n              return v\r\n            })\r\n          )\r\n        }\r\n      } catch (error) {\r\n        console.log('error', error)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    handleGridData(data) {\r\n      console.log('data', data)\r\n      return data\r\n    },\r\n    handleAdd() {\r\n      console.log('新增')\r\n      this.$nextTick(() => {\r\n        this.$refs['dialog'].handleOpen()\r\n      })\r\n    },\r\n    handleEdit(row) {\r\n      console.log(row)\r\n      this.$nextTick(() => {\r\n        this.$refs['dialog'].handleOpen(row)\r\n      })\r\n    },\r\n    handleDelete(row) {\r\n      this.$confirm('是否删除该工艺', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbLoading = true\r\n        DeleteTechnology({\r\n          technologyId: row.Id,\r\n          sysProjectId: this.sysProjectId\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.$confirm('此操作将会恢复到工厂的产品生产路径, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        RestoreFactoryTechnologyFromProject({\r\n          Sys_Project_Id: this.sysProjectId\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '恢复成功!'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    handleAddProject() {\r\n      console.log('同步项目配置')\r\n      this.dialogVisible = true\r\n      this.currentComponent = 'ProjectAdd'\r\n      this.title = '同步项目配置'\r\n    },\r\n\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n      this.currentComponent = ''\r\n      this.title = ''\r\n    },\r\n    setProjectData(data) {\r\n      this.selectedProjects = data\r\n      this.sysProjectId = data?.Sys_Project_Id || ''\r\n      console.log('selectedProjects', this.selectedProjects)\r\n      this.fetchData()\r\n    }\r\n  }\r\n\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container{\r\n  display: flex;\r\n  flex-direction: row;\r\n  height: 100%;\r\n  .card-x{\r\n    padding: 16px;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    background-color: #ffffff;\r\n    .card-x-top{\r\n      margin-bottom: 16px;\r\n    }\r\n    .table-section {\r\n      flex: 1;\r\n      background: #fff;\r\n      border-radius: 4px;\r\n      overflow: hidden;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
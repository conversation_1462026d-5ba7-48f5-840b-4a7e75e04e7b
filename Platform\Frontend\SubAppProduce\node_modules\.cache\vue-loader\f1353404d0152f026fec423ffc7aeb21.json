{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\index.vue", "mtime": 1757468128031}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "index.vue", "sourceRoot": "src/views/PRO/project-config/product-mfg-path", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <ProjectData />\r\n    <div class=\"card-x\">\r\n      <div class=\"card-x-top\">\r\n        <el-button type=\"primary\" @click=\"handleAdd\">新增</el-button>\r\n        <el-button type=\"danger\" @click=\"handleDelete\">删除</el-button>\r\n\r\n        <el-button type=\"primary\" @click=\"handleAddProject\">从项目添加</el-button>\r\n        <el-button type=\"primary\" @click=\"handleAddFactory\">从工厂添加</el-button>\r\n      </div>\r\n      <div class=\"table-section\">\r\n        <bt-table\r\n          ref=\"projectTable\"\r\n          code=\"ProductMftPathConfig\"\r\n          :custom-table-config=\"tableConfig\"\r\n          :grid-data-handler=\"handleGridData\"\r\n          :loading=\"loading\"\r\n          @selection-change=\"handleSelectionChange\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"title\"\r\n      class=\"plm-custom-dialog\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"80%\"\r\n      top=\"5vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        ref=\"content\"\r\n        @close=\"handleClose\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ProjectData from '../components/ProjectData.vue'\r\nimport { GetLibList } from '@/api/PRO/technology-lib'\r\nimport ProjectAdd from './component/ProjectAddDialog.vue'\r\nimport FactoryAdd from './component/FactoryAddDialog.vue'\r\nexport default {\r\n  name: 'PROProductMfgPath',\r\n  components: {\r\n    ProjectData,\r\n    ProjectAdd,\r\n    FactoryAdd\r\n  },\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      currentComponent: '',\r\n      title: '',\r\n      tableConfig: {\r\n        tableColumns: [],\r\n        tableActions: [],\r\n        tableData: [],\r\n        checkbox: true,\r\n        operateOptions: {\r\n          width: 120,\r\n          align: 'center',\r\n          isShow: false\r\n        }\r\n      },\r\n      loading: false,\r\n      selectedProjects: []\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    async fetchData() {\r\n      this.loading = true\r\n      try {\r\n        const params = {\r\n          Id: '',\r\n          Type: 0\r\n        }\r\n        const res = await GetLibList(params)\r\n        if (res.IsSucceed) {\r\n          this.tableConfig.tableData = res.Data\r\n        }\r\n      } catch (error) {\r\n        console.log('error', error)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    handleGridData(data) {\r\n      console.log('data', data)\r\n      return data\r\n    },\r\n    handleSelectionChange(selection) {\r\n      console.log('selection', selection)\r\n    },\r\n    handleAdd() {\r\n      console.log('新增')\r\n    },\r\n    handleDelete() {\r\n      console.log('删除')\r\n    },\r\n    handleAddProject() {\r\n      console.log('从项目添加')\r\n      this.dialogVisible = true\r\n      this.currentComponent = 'ProjectAdd'\r\n      this.title = '从项目添加'\r\n    },\r\n    handleAddFactory() {\r\n      console.log('从工厂添加')\r\n      this.dialogVisible = true\r\n      this.currentComponent = 'FactoryAdd'\r\n      this.title = '从工厂添加'\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n      this.currentComponent = ''\r\n      this.title = ''\r\n    }\r\n  }\r\n\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container{\r\n  display: flex;\r\n  flex-direction: row;\r\n  height: 100%;\r\n  .card-x{\r\n    padding: 16px;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    background-color: #ffffff;\r\n    .card-x-top{\r\n      margin-bottom: 16px;\r\n    }\r\n    .table-section {\r\n      flex: 1;\r\n      background: #fff;\r\n      border-radius: 4px;\r\n      overflow: hidden;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\change-management\\contact-list\\components\\HandleEdit.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\change-management\\contact-list\\components\\HandleEdit.vue", "mtime": 1757468112546}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBhbGxDb2RlcywgZGVmYXVsdFByZWZpeCwgZmlsdGVyQnlDb2RlVHlwZSwgZ2VuZXJhdGVBbGxDb2RlcywgZ2V0QWxsQ29kZXNCeVR5cGUgfSBmcm9tICcuLi91dGlscycNCmltcG9ydCB7IEdldENvbXBUeXBlVHJlZSB9IGZyb20gJ0AvYXBpL1BSTy9jb21wb25lbnQtdHlwZScNCmltcG9ydCB7IEdldFBhcnRUeXBlTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9wYXJ0VHlwZScNCmltcG9ydCB7IEdldExpYkxpc3QgfSBmcm9tICdAL2FwaS9QUk8vdGVjaG5vbG9neS1saWInDQppbXBvcnQgeyBkZWVwQ2xvbmUgfSBmcm9tICdAL3V0aWxzJw0KaW1wb3J0IHsgQ2hlY2tDYW5Nb2NOYW1lIH0gZnJvbSAnQC9hcGkvUFJPL2NoYW5nZU1hbmFnZW1lbnQnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCg0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBsaXN0OiBbew0KICAgICAgICBWYWx1ZTogJycsDQogICAgICAgIE5ld1ZhbHVlOiAnJywNCiAgICAgICAgRmllbGRfVHlwZTogJycsDQogICAgICAgIElzQ29yZUZpZWxkOiAnJywNCiAgICAgICAgcHJlY2lzaW9uOiAnJywNCiAgICAgICAgQ29kZTogJycNCiAgICAgIH1dLA0KICAgICAgb3B0aW9uczogW10sDQogICAgICBwYXJ0VHlwZU9wdGlvbjogW10sDQogICAgICBpc1NhdmVEaXNhYmxlZDogZmFsc2UsDQogICAgICBwcm9jZXNzT3B0aW9uOiBbXSwNCiAgICAgIHRyZWVTZWxlY3RQYXJhbXM6IHsNCiAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fpgInmi6knLA0KICAgICAgICBjbGVhcmFibGU6IHRydWUNCiAgICAgIH0sDQogICAgICBPYmplY3RUeXBlTGlzdDogew0KICAgICAgICAvLyDmnoTku7bnsbvlnosNCiAgICAgICAgJ2NoZWNrLXN0cmljdGx5JzogdHJ1ZSwNCiAgICAgICAgJ2RlZmF1bHQtZXhwYW5kLWFsbCc6IHRydWUsDQogICAgICAgIGNsaWNrUGFyZW50OiB0cnVlLA0KICAgICAgICBkYXRhOiBbXSwNCiAgICAgICAgcHJvcHM6IHsNCiAgICAgICAgICBjaGlsZHJlbjogJ0NoaWxkcmVuJywNCiAgICAgICAgICBsYWJlbDogJ0xhYmVsJywNCiAgICAgICAgICB2YWx1ZTogJ0RhdGEnDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBpbml0KHJvdywgZGVmYXVsdFJvdywgaXNFZGl0LCB0YkRhdGEsIGFsbENvZGVzKSB7DQogICAgICBjb25zdCB0YkNvZGUgPSB0YkRhdGEubWFwKGl0ZW0gPT4gaXRlbS5DUENvZGUpDQogICAgICB0aGlzLl90YkRhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRiRGF0YSkpDQogICAgICBnZW5lcmF0ZUFsbENvZGVzKGFsbENvZGVzKQ0KICAgICAgdGhpcy5yb3cgPSByb3cNCiAgICAgIHRoaXMudGJDb2RlID0gdGJDb2RlDQogICAgICB0aGlzLmRlZmF1bHRSb3cgPSBkZWZhdWx0Um93DQogICAgICBjb25zb2xlLmxvZygnaXNFZGl0JywgaXNFZGl0KQ0KICAgICAgY29uc29sZS5sb2coJ3JvdzEnLCByb3cpDQogICAgICBjb25zdCBfY29sdW1ucyA9IGZpbHRlckJ5Q29kZVR5cGUocm93LkNvZGVUeXBlKQ0KICAgICAgdGhpcy5vcHRpb25zID0gX2NvbHVtbnMubWFwKHYgPT4gKHsNCiAgICAgICAgQ29kZTogdi5Db2RlLA0KICAgICAgICBOYW1lOiB2LkRpc3BsYXlfTmFtZSwNCiAgICAgICAgZGlzYWJsZWQ6IGZhbHNlLA0KICAgICAgICBGaWVsZF9UeXBlOiB2LkZpZWxkX1R5cGUsDQogICAgICAgIHByZWNpc2lvbjogdi5wcmVjaXNpb24sDQogICAgICAgIElzQ29yZUZpZWxkOiB2LklzQ29yZUZpZWxkDQogICAgICB9KSkNCiAgICAgIGNvbnNvbGUubG9nKCd0aGlzLm9wdGlvbnMnLCBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMub3B0aW9ucykpKQ0KDQogICAgICBjb25zdCBjaGFuZ2VDb2RlID0gZGVlcENsb25lKHRoaXMuJHN0b3JlLnN0YXRlLmNvbnRhY3RMaXN0LmNoYW5nZUNvZGUpDQogICAgICBpZiAoY2hhbmdlQ29kZVt0aGlzLnJvdy51dWlkXSkgew0KICAgICAgICB0aGlzLmxpc3QgPSBjaGFuZ2VDb2RlW3RoaXMucm93LnV1aWRdDQogICAgICAgIGNvbnN0IHNlbGVjdEl0ZW0gPSB0aGlzLmxpc3QuZmlsdGVyKHYgPT4gdi5GaWVsZF9UeXBlID09PSAnc2VsZWN0JykNCiAgICAgICAgaWYgKHNlbGVjdEl0ZW0ubGVuZ3RoKSB7DQogICAgICAgICAgaWYgKHNlbGVjdEl0ZW0uc29tZSh2ID0+IHYuQ29kZSA9PT0gJ1N0ZWVsVHlwZScpKSB7DQogICAgICAgICAgICB0aGlzLmdldE9iamVjdFR5cGVMaXN0KCkNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKHNlbGVjdEl0ZW0uc29tZSh2ID0+IHYuQ29kZSA9PT0gJ1BhcnRUeXBlJykpIHsNCiAgICAgICAgICAgIHRoaXMuZ2V0UGFydFR5cGVMaXN0KCkNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKHNlbGVjdEl0ZW0uc29tZSh2ID0+IHYuQ29kZSA9PT0gJ1RlY2hub2xvZ3lfQ29kZScpKSB7DQogICAgICAgICAgICB0aGlzLmdldExpYkxpc3QoKQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBoYW5kbGVDbG9zZSgpIHsNCiAgICAgIHRoaXMuJGVtaXQoJ2Nsb3NlJykNCiAgICB9LA0KICAgIGhhbmRsZUFkZChpbmRleCkgew0KICAgICAgdGhpcy5saXN0LnNwbGljZShpbmRleCArIDEsIDAsIHsNCiAgICAgICAgVmFsdWU6ICcnLA0KICAgICAgICBOZXdWYWx1ZTogJycsDQogICAgICAgIEZpZWxkX1R5cGU6ICcnLA0KICAgICAgICBJc0NvcmVGaWVsZDogJycsDQogICAgICAgIHByZWNpc2lvbjogMCwNCiAgICAgICAgQ29kZTogJycNCiAgICAgIH0pDQogICAgfSwNCiAgICBnZXRPYmplY3RUeXBlTGlzdCgpIHsNCiAgICAgIGlmICh0aGlzLnJvdy5UeXBlID09PSAwKSB7DQogICAgICAgIEdldENvbXBUeXBlVHJlZSh7IHByb2Zlc3Npb25hbDogJ1N0ZWVsJyB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgdGhpcy5PYmplY3RUeXBlTGlzdC5kYXRhID0gcmVzLkRhdGENCiAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJHJlZnMudHJlZVNlbGVjdE9iamVjdFR5cGUxWzBdLnRyZWVEYXRhVXBkYXRlRnVuKHJlcy5EYXRhKQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsDQogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIEdldFBhcnRUeXBlTGlzdCh7IFBhcnRfR3JhZGU6IDAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICB0aGlzLnBhcnRUeXBlT3B0aW9uID0gcmVzLkRhdGENCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KICAgIHNlbGVjdENoYW5nZShjb2RlLCBpdGVtKSB7DQogICAgICBjb25zb2xlLmxvZygnY29kZScsIGNvZGUsIGl0ZW0pDQogICAgICBjb25zdCBjdXIgPSB0aGlzLm9wdGlvbnMuZmluZCh2ID0+IHYuQ29kZSA9PT0gY29kZSkNCiAgICAgIGNvbnNvbGUubG9nKGN1ciwgJ2N1cicpDQogICAgICBjb25zb2xlLmxvZyh0aGlzLmRlZmF1bHRSb3csICd0aGlzLmRlZmF1bHRSb3cnKQ0KICAgICAgaXRlbS5GaWVsZF9UeXBlID0gY3VyLkZpZWxkX1R5cGUNCiAgICAgIGl0ZW0uSXNDb3JlRmllbGQgPSBjdXIuSXNDb3JlRmllbGQNCiAgICAgIGl0ZW0ucHJlY2lzaW9uID0gY3VyLnByZWNpc2lvbg0KICAgICAgaXRlbS5OYW1lID0gY3VyLk5hbWUNCiAgICAgIGl0ZW0uVmFsdWUgPSB0aGlzLmRlZmF1bHRSb3dbY29kZV0NCiAgICAgIGl0ZW0uTmV3VmFsdWUgPSB1bmRlZmluZWQNCiAgICAgIGlmIChjb2RlID09PSAnU3RlZWxUeXBlJyB8fCBjb2RlID09PSAnUGFydFR5cGUnKSB7DQogICAgICAgIHRoaXMuZ2V0T2JqZWN0VHlwZUxpc3QoKQ0KICAgICAgfQ0KICAgICAgaWYgKGNvZGUgPT09ICdUZWNobm9sb2d5X0NvZGUnKSB7DQogICAgICAgIHRoaXMuZ2V0TGliTGlzdCgpDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVEZWxldGUoZWxlbWVudCwgaW5kZXgpIHsNCiAgICAgIGNvbnN0IGlkeCA9IHRoaXMubGlzdC5maW5kSW5kZXgodiA9PiB2LkNvZGUgPT09IGVsZW1lbnQuQ29kZSkNCiAgICAgIGNvbnNvbGUubG9nKGlkeCwgJ29tZCcpDQogICAgICBpZiAoaWR4ICE9PSAtMSkgew0KICAgICAgICB0aGlzLmxpc3Quc3BsaWNlKGlkeCwgMSkNCiAgICAgIH0NCiAgICB9LA0KICAgIGdldEF2YWlsYWJsZU9wdGlvbnMoY3VycmVudEluZGV4KSB7DQogICAgICBjb25zdCBzZWxlY3RlZENvZGVzID0gdGhpcy5saXN0DQogICAgICAgIC5maWx0ZXIoKGl0ZW0sIGlkeCkgPT4gaWR4ICE9PSBjdXJyZW50SW5kZXgpDQogICAgICAgIC5tYXAoaXRlbSA9PiBpdGVtLkNvZGUpDQogICAgICByZXR1cm4gdGhpcy5vcHRpb25zLmZpbHRlcihvcHRpb24gPT4gIXNlbGVjdGVkQ29kZXMuaW5jbHVkZXMob3B0aW9uLkNvZGUpKQ0KICAgIH0sDQogICAgYXN5bmMgaGFuZGxlU2F2ZSgpIHsNCiAgICAgIGxldCBzdWNjZXNzID0gdHJ1ZQ0KICAgICAgY29uc3QgbGlzdCA9IHRoaXMubGlzdC5maWx0ZXIoaXRlbSA9PiAhIWl0ZW0uQ29kZSkNCiAgICAgIGNvbnNvbGUubG9nKGxpc3QsICdsaXN0JykNCg0KICAgICAgY29uc3QgaXNNdXN0SW5wdXRzID0gYWxsQ29kZXMuZmlsdGVyKGl0ZW0gPT4gaXRlbS5pc011c3RJbnB1dCkubWFwKGl0ZW0gPT4gaXRlbS5Db2RlKQ0KICAgICAgY29uc29sZS5sb2coaXNNdXN0SW5wdXRzLCAnaXNNdXN0SW5wdXRzJykNCiAgICAgIGxpc3QuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgaWYgKGlzTXVzdElucHV0cy5pbmNsdWRlcyhpdGVtLkNvZGUpKSB7DQogICAgICAgICAgaWYgKCFpdGVtLk5ld1ZhbHVlKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+ivt+i+k+WFpScgKyBpdGVtLk5hbWUsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICBzdWNjZXNzID0gZmFsc2UNCiAgICAgICAgICAgIHJldHVybg0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSkNCg0KICAgICAgY29uc3QgaXNWYWxpZCA9IGF3YWl0IHRoaXMuY2hlY2tOYW1lKCkNCiAgICAgIGNvbnNvbGUubG9nKCdpc1ZhbGlkJywgaXNWYWxpZCkNCiAgICAgIGlmICghaXNWYWxpZCkgew0KICAgICAgICBzdWNjZXNzID0gZmFsc2UNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIC8vIOagueaNrlR5cGXliKTmlq3llK/kuIDmgKflrZfmrrXvvJowLeaehOS7tihTdGVlbE5hbWUpLCAxLemDqOS7tihDb21wb25lbnROYW1lKSwgMi8zLembtuS7tihQYXJ0TmFtZSkNCiAgICAgIC8vIGxldCBoYXNSZXBlYXQgPSBmYWxzZQ0KICAgICAgLy8gbGV0IG5hbWVJdGVtID0gbnVsbA0KICAgICAgLy8gbGV0IG5hbWVGaWVsZCA9ICcnDQogICAgICAvLyBpZiAodGhpcy5yb3cuVHlwZSA9PT0gMCkgew0KICAgICAgLy8gICBuYW1lRmllbGQgPSAnU3RlZWxOYW1lJw0KICAgICAgLy8gfSBlbHNlIGlmICh0aGlzLnJvdy5UeXBlID09PSAxKSB7DQogICAgICAvLyAgIG5hbWVGaWVsZCA9ICdDb21wb25lbnROYW1lJw0KICAgICAgLy8gfSBlbHNlIGlmICh0aGlzLnJvdy5UeXBlID09PSAyIHx8IHRoaXMucm93LlR5cGUgPT09IDMpIHsNCiAgICAgIC8vICAgbmFtZUZpZWxkID0gJ1BhcnROYW1lJw0KICAgICAgLy8gfQ0KICAgICAgLy8gbmFtZUl0ZW0gPSBsaXN0LmZpbmQodiA9PiB2LkNvZGUgPT09IG5hbWVGaWVsZCkNCiAgICAgIC8vIGlmIChuYW1lSXRlbSkgew0KICAgICAgLy8gICBjb25zdCBuZXdOYW1lID0gbmFtZUl0ZW0uTmV3VmFsdWU/LnRyaW0oKQ0KICAgICAgLy8gICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMuX3RiRGF0YS5sZW5ndGg7IGkrKykgew0KICAgICAgLy8gICAgIGNvbnN0IGl0ZW0gPSB0aGlzLl90YkRhdGFbaV0NCiAgICAgIC8vICAgICBpZiAoaXRlbS5Db2RlVHlwZSA9PT0gMyB8fCBpdGVtLkNvZGVUeXBlID09PSAyKSB7DQogICAgICAvLyAgICAgICBpZiAoaXRlbS5QYXJ0X0FnZ3JlZ2F0ZV9JZCA9PT0gdGhpcy5yb3cuUGFydF9BZ2dyZWdhdGVfSWQpIHsNCiAgICAgIC8vICAgICAgICAgY29udGludWUNCiAgICAgIC8vICAgICAgIH0NCiAgICAgIC8vICAgICB9IGVsc2UgaWYgKGl0ZW0uQ29kZVR5cGUgPT09IDEgJiYgaXRlbS51dWlkID09PSB0aGlzLnJvdy51dWlkKSB7DQogICAgICAvLyAgICAgICBjb250aW51ZQ0KICAgICAgLy8gICAgIH0NCiAgICAgIC8vICAgICAvLyDlj6rmr5TovoPlkIznsbvlnovnmoTllK/kuIDmgKflrZfmrrUNCiAgICAgIC8vICAgICBpZiAoaXRlbVtuYW1lRmllbGRdPy50cmltKCkgPT09IG5ld05hbWUgJiYgdGhpcy5yb3cuVHlwZSA9PT0gaXRlbS5UeXBlKSB7DQogICAgICAvLyAgICAgICBoYXNSZXBlYXQgPSB0cnVlDQogICAgICAvLyAgICAgICBicmVhaw0KICAgICAgLy8gICAgIH0NCiAgICAgIC8vICAgfQ0KICAgICAgLy8gfQ0KDQogICAgICBpZiAoIXN1Y2Nlc3MpIHJldHVybg0KDQogICAgICAvLyB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnY29udGFjdExpc3QvYWRkQ2hhbmdlQ29kZScsIHsgdXVpZDogdGhpcy5yb3cudXVpZCwgbGlzdDogbGlzdCB9KQ0KICAgICAgdGhpcy4kZW1pdCgnZWRpdEluZm8nLCB7IHJvdzogdGhpcy5yb3csIGxpc3Q6IGxpc3QgfSkNCiAgICAgIHRoaXMuJGVtaXQoJ2Nsb3NlJykNCiAgICB9LA0KICAgIGFzeW5jIGNoZWNrTmFtZSgpIHsNCiAgICAgIGNvbnN0IGl0ZW0gPSB0aGlzLmxpc3QuZmluZCh2ID0+IHYuQ29kZSA9PT0gJ1N0ZWVsTmFtZScgfHwgdi5Db2RlID09PSAnQ29tcG9uZW50TmFtZScgfHwgdi5Db2RlID09PSAnUGFydE5hbWUnKQ0KICAgICAgaWYgKCFpdGVtKSByZXR1cm4gdHJ1ZQ0KICAgICAgbGV0IGZsYWcgPSB0cnVlDQogICAgICBhd2FpdCBDaGVja0Nhbk1vY05hbWUoew0KICAgICAgICBUeXBlOiB0aGlzLnJvdy5UeXBlLA0KICAgICAgICBJZDogdGhpcy5yb3cuVHlwZSA9PT0gMCA/IHRoaXMucm93Lk1vY0lkQmVmb3JlIDogdGhpcy5yb3cuTW9jQWdncmVnYXRlSWRCZWZvcmUsDQogICAgICAgIE5ld05hbWU6IGl0ZW0uTmV3VmFsdWUNCiAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICBmbGFnID0gcmVzLkRhdGENCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgICAgZmxhZyA9IGZhbHNlDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICBpZiAoIWZsYWcpIHsNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICBjb25zdCBrZXkgPSB0aGlzLnJvdy5UeXBlID09PSAwID8gJ1N0ZWVsTmFtZScgOiB0aGlzLnJvdy5UeXBlID09PSAxID8gJ0NvbXBvbmVudE5hbWUnIDogJ1BhcnROYW1lJw0KICAgICAgY29uc3QgaGFzU2ltaWxhciA9IHRoaXMuZmluZFNpbWlsYXJUeXBlSXRlbXModGhpcy5yb3csIGtleSwgaXRlbS5OZXdWYWx1ZSkNCiAgICAgIGNvbnNvbGUubG9nKCdoYXNTaW1pbGFyJywgaGFzU2ltaWxhciwgaXRlbS5OZXdWYWx1ZSkNCiAgICAgIGlmIChoYXNTaW1pbGFyKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6IGAke3RoaXMucm93LlR5cGUgPT09IDAgPyAn5p6E5Lu2JyA6IHRoaXMucm93LlR5cGUgPT09IDEgPyAn6YOo5Lu2JyA6ICfpm7bku7YnfeWQjeensOW3suWtmOWcqO+8jOivt+S/ruaUuWAsDQogICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICB9KQ0KICAgICAgICBmbGFnID0gZmFsc2UNCiAgICAgIH0NCiAgICAgIHJldHVybiBmbGFnDQogICAgfSwNCiAgICBmaW5kU2ltaWxhclR5cGVJdGVtcyh0YXJnZXRJdGVtLCBrZXksIG5ld05hbWUpIHsNCiAgICAgIGxldCBmbGFnID0gZmFsc2UNCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5fdGJEYXRhLmxlbmd0aDsgaSsrKSB7DQogICAgICAgIGNvbnN0IGl0ZW0gPSB0aGlzLl90YkRhdGFbaV0NCiAgICAgICAgaWYgKGl0ZW0udXVpZCA9PT0gdGFyZ2V0SXRlbS51dWlkKSBjb250aW51ZQ0KICAgICAgICBpZiAoaXRlbS5Db2RlVHlwZSAhPT0gdGFyZ2V0SXRlbS5Db2RlVHlwZSkgY29udGludWUNCiAgICAgICAgaWYgKGl0ZW0uVHlwZSA9PT0gMSB8fCBpdGVtLlR5cGUgPT09IDIgfHwgaXRlbS5UeXBlID09PSAzKSB7DQogICAgICAgICAgaWYgKCh0YXJnZXRJdGVtLk1vY0FnZ3JlZ2F0ZUlkQmVmb3JlICE9PSBpdGVtLk1vY0FnZ3JlZ2F0ZUlkQmVmb3JlKSAmJiAoaXRlbVtrZXldID09PSBuZXdOYW1lKSkgew0KICAgICAgICAgICAgZmxhZyA9IHRydWUNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSBpZiAoaXRlbS5UeXBlID09PSAwKSB7DQogICAgICAgICAgaWYgKGl0ZW1ba2V5XSA9PT0gbmV3TmFtZSkgew0KICAgICAgICAgICAgZmxhZyA9IHRydWUNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHJldHVybiBmbGFnDQogICAgfSwNCiAgICBoYW5kbGVDYW5jZWwoKSB7DQogICAgICB0aGlzLiRlbWl0KCdjbG9zZScpDQogICAgfSwNCiAgICBnZXRMaWJMaXN0KCkgew0KICAgICAgR2V0TGliTGlzdCh7DQogICAgICAgIHR5cGU6IHRoaXMucm93LlR5cGUgPT09IDAgPyAxIDogdGhpcy5yb3cuVHlwZSA9PT0gMSA/IDMgOiAyDQogICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy5wcm9jZXNzT3B0aW9uID0gcmVzLkRhdGENCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["HandleEdit.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6FA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "HandleEdit.vue", "sourceRoot": "src/views/PRO/change-management/contact-list/components", "sourcesContent": ["<template>\r\n  <div class=\"handle-edit-container\">\r\n    <div class=\"handle-edit\">\r\n      <el-button v-if=\"!list.length\" type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd(-1)\">添加</el-button>\r\n      <div v-for=\"(item, index) in list\" :key=\"index\" class=\"flex-row\">\r\n        <div class=\"flex-item flex-item-1\">\r\n          <el-input\r\n            v-model=\"item.Value\"\r\n            style=\"width: 100%;\"\r\n            clearable\r\n            readonly\r\n            placeholder=\"请输入\"\r\n            class=\"input-with-select\"\r\n          >\r\n            <el-select\r\n              slot=\"prepend\"\r\n              v-model=\"item.Code\"\r\n              placeholder=\"请选择\"\r\n              style=\"width: 160px\"\r\n              @change=\"selectChange($event,item)\"\r\n            >\r\n              <el-option v-for=\"option in getAvailableOptions(index)\" :key=\"option.Code\" :label=\"option.Name\" :value=\"option.Code\" />\r\n            </el-select>\r\n\r\n          </el-input>\r\n        </div>\r\n        <div class=\"flxe-item2\">\r\n          <span>变更后：</span>\r\n        </div>\r\n        <div class=\"flex-item\">\r\n\r\n          <el-tree-select\r\n            v-if=\"item.Code === 'SteelType'\"\r\n            ref=\"treeSelectObjectType1\"\r\n            v-model=\"item.NewValue\"\r\n            class=\"cs-tree-x\"\r\n            style=\"width: 100%;\"\r\n            :select-params=\"treeSelectParams\"\r\n            :tree-params=\"ObjectTypeList\"\r\n            value-key=\"Id\"\r\n          />\r\n          <el-select v-else-if=\"item.Code==='PartType'\" v-model=\"item.NewValue\" placeholder=\"请选择\" clearable=\"\">\r\n            <el-option\r\n              v-for=\"cur in partTypeOption\"\r\n              :key=\"cur.Code\"\r\n              :label=\"cur.Name\"\r\n              :value=\"cur.Code\"\r\n            />\r\n          </el-select>\r\n          <el-select v-else-if=\"item.Code==='Technology_Code'\" v-model=\"item.NewValue\" placeholder=\"请选择\" clearable=\"\">\r\n            <el-option\r\n              v-for=\"cur in processOption\"\r\n              :key=\"cur.Code\"\r\n              :label=\"cur.Code\"\r\n              :value=\"cur.Code\"\r\n            />\r\n          </el-select>\r\n          <el-input-number\r\n            v-else-if=\"item.Field_Type==='number'\"\r\n            v-model=\"item.NewValue\"\r\n            :min=\"0\"\r\n            :precision=\"item.precision || 0\"\r\n            style=\"width: 100%\"\r\n            class=\"cs-number-btn-hidden\"\r\n          />\r\n          <el-input v-else v-model.trim=\"item.NewValue\" style=\"width: 100%;\" clearable placeholder=\"请输入\" />\r\n\r\n        </div>\r\n        <div class=\"flex-item3 btn-x\">\r\n          <el-button\r\n            v-if=\"(list.length < options.length)\"\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            circle\r\n            @click=\"handleAdd(index)\"\r\n          />\r\n          <el-button\r\n            type=\"danger\"\r\n            icon=\"el-icon-delete\"\r\n            circle\r\n            @click=\"handleDelete(item, index)\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"btn-group\">\r\n      <el-button @click=\"handleClose\">取消</el-button>\r\n      <el-button type=\"primary\" :disabled=\"isSaveDisabled\" @click=\"handleSave\">保存</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { allCodes, defaultPrefix, filterByCodeType, generateAllCodes, getAllCodesByType } from '../utils'\r\nimport { GetCompTypeTree } from '@/api/PRO/component-type'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\nimport { GetLibList } from '@/api/PRO/technology-lib'\r\nimport { deepClone } from '@/utils'\r\nimport { CheckCanMocName } from '@/api/PRO/changeManagement'\r\n\r\nexport default {\r\n\r\n  data() {\r\n    return {\r\n      list: [{\r\n        Value: '',\r\n        NewValue: '',\r\n        Field_Type: '',\r\n        IsCoreField: '',\r\n        precision: '',\r\n        Code: ''\r\n      }],\r\n      options: [],\r\n      partTypeOption: [],\r\n      isSaveDisabled: false,\r\n      processOption: [],\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        clearable: true\r\n      },\r\n      ObjectTypeList: {\r\n        // 构件类型\r\n        'check-strictly': true,\r\n        'default-expand-all': true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    init(row, defaultRow, isEdit, tbData, allCodes) {\r\n      const tbCode = tbData.map(item => item.CPCode)\r\n      this._tbData = JSON.parse(JSON.stringify(tbData))\r\n      generateAllCodes(allCodes)\r\n      this.row = row\r\n      this.tbCode = tbCode\r\n      this.defaultRow = defaultRow\r\n      console.log('isEdit', isEdit)\r\n      console.log('row1', row)\r\n      const _columns = filterByCodeType(row.CodeType)\r\n      this.options = _columns.map(v => ({\r\n        Code: v.Code,\r\n        Name: v.Display_Name,\r\n        disabled: false,\r\n        Field_Type: v.Field_Type,\r\n        precision: v.precision,\r\n        IsCoreField: v.IsCoreField\r\n      }))\r\n      console.log('this.options', JSON.parse(JSON.stringify(this.options)))\r\n\r\n      const changeCode = deepClone(this.$store.state.contactList.changeCode)\r\n      if (changeCode[this.row.uuid]) {\r\n        this.list = changeCode[this.row.uuid]\r\n        const selectItem = this.list.filter(v => v.Field_Type === 'select')\r\n        if (selectItem.length) {\r\n          if (selectItem.some(v => v.Code === 'SteelType')) {\r\n            this.getObjectTypeList()\r\n          }\r\n          if (selectItem.some(v => v.Code === 'PartType')) {\r\n            this.getPartTypeList()\r\n          }\r\n          if (selectItem.some(v => v.Code === 'Technology_Code')) {\r\n            this.getLibList()\r\n          }\r\n        }\r\n      }\r\n    },\r\n\r\n    handleClose() {\r\n      this.$emit('close')\r\n    },\r\n    handleAdd(index) {\r\n      this.list.splice(index + 1, 0, {\r\n        Value: '',\r\n        NewValue: '',\r\n        Field_Type: '',\r\n        IsCoreField: '',\r\n        precision: 0,\r\n        Code: ''\r\n      })\r\n    },\r\n    getObjectTypeList() {\r\n      if (this.row.Type === 0) {\r\n        GetCompTypeTree({ professional: 'Steel' }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.ObjectTypeList.data = res.Data\r\n            this.$nextTick((_) => {\r\n              this.$refs.treeSelectObjectType1[0].treeDataUpdateFun(res.Data)\r\n            })\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        })\r\n      } else {\r\n        GetPartTypeList({ Part_Grade: 0 }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.partTypeOption = res.Data\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n    },\r\n    selectChange(code, item) {\r\n      console.log('code', code, item)\r\n      const cur = this.options.find(v => v.Code === code)\r\n      console.log(cur, 'cur')\r\n      console.log(this.defaultRow, 'this.defaultRow')\r\n      item.Field_Type = cur.Field_Type\r\n      item.IsCoreField = cur.IsCoreField\r\n      item.precision = cur.precision\r\n      item.Name = cur.Name\r\n      item.Value = this.defaultRow[code]\r\n      item.NewValue = undefined\r\n      if (code === 'SteelType' || code === 'PartType') {\r\n        this.getObjectTypeList()\r\n      }\r\n      if (code === 'Technology_Code') {\r\n        this.getLibList()\r\n      }\r\n    },\r\n    handleDelete(element, index) {\r\n      const idx = this.list.findIndex(v => v.Code === element.Code)\r\n      console.log(idx, 'omd')\r\n      if (idx !== -1) {\r\n        this.list.splice(idx, 1)\r\n      }\r\n    },\r\n    getAvailableOptions(currentIndex) {\r\n      const selectedCodes = this.list\r\n        .filter((item, idx) => idx !== currentIndex)\r\n        .map(item => item.Code)\r\n      return this.options.filter(option => !selectedCodes.includes(option.Code))\r\n    },\r\n    async handleSave() {\r\n      let success = true\r\n      const list = this.list.filter(item => !!item.Code)\r\n      console.log(list, 'list')\r\n\r\n      const isMustInputs = allCodes.filter(item => item.isMustInput).map(item => item.Code)\r\n      console.log(isMustInputs, 'isMustInputs')\r\n      list.forEach(item => {\r\n        if (isMustInputs.includes(item.Code)) {\r\n          if (!item.NewValue) {\r\n            this.$message({\r\n              message: '请输入' + item.Name,\r\n              type: 'error'\r\n            })\r\n            success = false\r\n            return\r\n          }\r\n        }\r\n      })\r\n\r\n      const isValid = await this.checkName()\r\n      console.log('isValid', isValid)\r\n      if (!isValid) {\r\n        success = false\r\n        return\r\n      }\r\n\r\n      // 根据Type判断唯一性字段：0-构件(SteelName), 1-部件(ComponentName), 2/3-零件(PartName)\r\n      // let hasRepeat = false\r\n      // let nameItem = null\r\n      // let nameField = ''\r\n      // if (this.row.Type === 0) {\r\n      //   nameField = 'SteelName'\r\n      // } else if (this.row.Type === 1) {\r\n      //   nameField = 'ComponentName'\r\n      // } else if (this.row.Type === 2 || this.row.Type === 3) {\r\n      //   nameField = 'PartName'\r\n      // }\r\n      // nameItem = list.find(v => v.Code === nameField)\r\n      // if (nameItem) {\r\n      //   const newName = nameItem.NewValue?.trim()\r\n      //   for (let i = 0; i < this._tbData.length; i++) {\r\n      //     const item = this._tbData[i]\r\n      //     if (item.CodeType === 3 || item.CodeType === 2) {\r\n      //       if (item.Part_Aggregate_Id === this.row.Part_Aggregate_Id) {\r\n      //         continue\r\n      //       }\r\n      //     } else if (item.CodeType === 1 && item.uuid === this.row.uuid) {\r\n      //       continue\r\n      //     }\r\n      //     // 只比较同类型的唯一性字段\r\n      //     if (item[nameField]?.trim() === newName && this.row.Type === item.Type) {\r\n      //       hasRepeat = true\r\n      //       break\r\n      //     }\r\n      //   }\r\n      // }\r\n\r\n      if (!success) return\r\n\r\n      // this.$store.dispatch('contactList/addChangeCode', { uuid: this.row.uuid, list: list })\r\n      this.$emit('editInfo', { row: this.row, list: list })\r\n      this.$emit('close')\r\n    },\r\n    async checkName() {\r\n      const item = this.list.find(v => v.Code === 'SteelName' || v.Code === 'ComponentName' || v.Code === 'PartName')\r\n      if (!item) return true\r\n      let flag = true\r\n      await CheckCanMocName({\r\n        Type: this.row.Type,\r\n        Id: this.row.Type === 0 ? this.row.MocIdBefore : this.row.MocAggregateIdBefore,\r\n        NewName: item.NewValue\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          flag = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          flag = false\r\n        }\r\n      })\r\n      if (!flag) {\r\n        return\r\n      }\r\n      const key = this.row.Type === 0 ? 'SteelName' : this.row.Type === 1 ? 'ComponentName' : 'PartName'\r\n      const hasSimilar = this.findSimilarTypeItems(this.row, key, item.NewValue)\r\n      console.log('hasSimilar', hasSimilar, item.NewValue)\r\n      if (hasSimilar) {\r\n        this.$message({\r\n          message: `${this.row.Type === 0 ? '构件' : this.row.Type === 1 ? '部件' : '零件'}名称已存在，请修改`,\r\n          type: 'error'\r\n        })\r\n        flag = false\r\n      }\r\n      return flag\r\n    },\r\n    findSimilarTypeItems(targetItem, key, newName) {\r\n      let flag = false\r\n      for (let i = 0; i < this._tbData.length; i++) {\r\n        const item = this._tbData[i]\r\n        if (item.uuid === targetItem.uuid) continue\r\n        if (item.CodeType !== targetItem.CodeType) continue\r\n        if (item.Type === 1 || item.Type === 2 || item.Type === 3) {\r\n          if ((targetItem.MocAggregateIdBefore !== item.MocAggregateIdBefore) && (item[key] === newName)) {\r\n            flag = true\r\n          }\r\n        } else if (item.Type === 0) {\r\n          if (item[key] === newName) {\r\n            flag = true\r\n          }\r\n        }\r\n      }\r\n      return flag\r\n    },\r\n    handleCancel() {\r\n      this.$emit('close')\r\n    },\r\n    getLibList() {\r\n      GetLibList({\r\n        type: this.row.Type === 0 ? 1 : this.row.Type === 1 ? 3 : 2\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.processOption = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.handle-edit-container {\r\n  position: relative;\r\n  .handle-edit {\r\n    max-height: 50vh;\r\n    overflow-y: auto;\r\n    .flex-row {\r\n      display: flex;\r\n      align-items: center;\r\n    justify-content: space-between;\r\n    .flex-item {\r\n      flex: 2;\r\n      margin: 10px 0;\r\n    }\r\n    .flex-item-1 {\r\n      flex: 3;\r\n    }\r\n    .flxe-item2 {\r\n      margin:0 10px;\r\n    }\r\n    .flex-item3 {\r\n      flex: 1;\r\n    }\r\n    .btn-x {\r\n      margin-left: 10px;\r\n\r\n    }\r\n  }\r\n\r\n}\r\n.btn-group {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    padding-top: 10px;\r\n    background: #fff;\r\n  }\r\n}\r\n.cs-tree-x {\r\n  ::v-deep {\r\n    .el-select {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
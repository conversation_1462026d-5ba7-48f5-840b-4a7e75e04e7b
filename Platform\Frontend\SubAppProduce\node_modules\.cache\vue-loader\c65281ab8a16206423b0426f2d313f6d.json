{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\sys\\professional-category\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\sys\\professional-category\\index.vue", "mtime": 1757468113629}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/sys/professional-category", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"page-container\">\r\n      <el-row type=\"flex\" justify=\"space-between\" style=\"margin-bottom: 16px\">\r\n        <el-col :span=\"4\">\r\n          <el-button v-if=\"false\" type=\"primary\" @click=\"addProfession\">新增专业</el-button>\r\n        </el-col>\r\n        <el-col :span=\"20\">\r\n          <el-row type=\"flex\" justify=\"end\">\r\n            <el-input v-model=\"searchValue\" placeholder=\"请输入关键字\" style=\"width:250px \" clearable />\r\n            <div>\r\n              <el-button type=\"primary\" style=\"margin-left: 16px\" @click=\"getData\">搜索</el-button>\r\n            </div>\r\n          </el-row>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <div class=\"list-wrapper\">\r\n        <el-table\r\n          ref=\"table\"\r\n          tablecode=\"plm_professionaltype_list_pro\"\r\n          :custom-param=\"{ is_System: false, typeid: '', name: searchValue, companyId: companyId }\"\r\n          @get-selection-data=\"getSelectionData\"\r\n          @getbutton=\"getClick\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <bimdialog ref=\"dialog\" @getData=\"getData\" />\r\n    <nodeList ref=\"nodeList\" @getData=\"getData\" />\r\n  </div>\r\n</template>\r\n<script>\r\nimport table from '@/views/plm/components/table'\r\nimport bimdialog from './dialog'\r\nimport nodeList from './nodeList'\r\nimport { GetProfessionalDelete } from '@/api/plm/settings'\r\nimport addRouterPage from '@/mixins/add-router-page/index'\r\nexport default {\r\n  name: 'ProfessionalCategoryList',\r\n  components: {\r\n    'el-table': table,\r\n    bimdialog,\r\n    nodeList\r\n  },\r\n  mixins: [addRouterPage],\r\n  data() {\r\n    return {\r\n      companyId: localStorage.getItem('Last_Working_Object_Id'),\r\n      searchValue: '',\r\n      addPageArray: [\r\n        {\r\n          path: 'unit-template-setting',\r\n          hidden: true,\r\n          component: () => import('@/views/sys/professional-category/unitPartTemp.vue'),\r\n          name: 'SYSUnitPartTemp',\r\n          meta: { title: '专用模板配置' }\r\n        },\r\n        {\r\n          path: 'template-setting',\r\n          hidden: true,\r\n          component: () => import('@/views/sys/professional-category/templateSetting'),\r\n          name: 'TemplateSetting',\r\n          meta: { title: '专用模板配置' }\r\n        },\r\n        {\r\n          path: 'template-setting-lj',\r\n          hidden: true,\r\n          component: () => import('@/views/sys/professional-category/templateSettingLj'),\r\n          name: 'TemplateSettingLj',\r\n          meta: { title: '零件模板配置' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/category',\r\n          hidden: true,\r\n          component: () => import('@/views/sys/professional-category/category/index.vue'),\r\n          name: 'ProfessionalCategoryListInfo',\r\n          meta: { title: '零构件类型' }\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    getSelectionData() {},\r\n    getData() {\r\n      this.$refs.table.refresh()\r\n    },\r\n    addProfession() {\r\n      this.$refs.dialog.handleOpen('add')\r\n    },\r\n    edit(item, row) {\r\n      if (row.row.is_system === true) {\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '该类别属于系统级别，不可操作'\r\n        })\r\n        return false\r\n      }\r\n      this.$refs.dialog.handleOpen('edit', row.row)\r\n    },\r\n    delete(item, row) {\r\n      if (row.row.is_system === true) {\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '该类别属于系统级别，不可操作'\r\n        })\r\n        return false\r\n      }\r\n      this.$confirm(' 确认删除?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          GetProfessionalDelete({ id: row.row.id }).then((res) => {\r\n            if (res.IsSucceed === true) {\r\n              this.$message({\r\n                type: 'success',\r\n                message: '删除成功'\r\n              })\r\n              this.getData()\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n    getClick(item, row) {\r\n      switch (item) {\r\n        case 'btnedit':\r\n          this.edit(item, row)\r\n          break\r\n        case 'btndelete':\r\n          this.delete(item, row)\r\n          break\r\n        case 'jdedit':\r\n          this.$refs.nodeList.handleOpen(true, row.row)\r\n          break\r\n        case 'unitPartCode':\r\n          this.$router.push({\r\n            name: 'SYSUnitPartTemp',\r\n            query: {\r\n              pg_redirect: this.$route.name,\r\n              name: row.row.name,\r\n              unit: row.row.unit,\r\n              steel_unit: row.row.steel_unit\r\n            }\r\n          })\r\n          break\r\n        case 'mbedit':\r\n          this.$router.push(\r\n            { name: 'TemplateSetting',\r\n              query: {\r\n                pg_redirect: this.$route.name,\r\n                typeCode: row.row.code,\r\n                materialCode: row.row.materialcode,\r\n                name: row.row.name,\r\n                unit: row.row.unit,\r\n                steel_unit: row.row.steel_unit\r\n              }\r\n            })\r\n          break\r\n        case 'ljedit':\r\n          this.$router.push(\r\n            { name: 'TemplateSettingLj',\r\n              query: {\r\n                pg_redirect: this.$route.name,\r\n                typeCode: row.row.code,\r\n                materialCode: row.row.materialcode,\r\n                name: row.row.name,\r\n                unit: row.row.unit,\r\n                steel_unit: row.row.steel_unit\r\n              }\r\n            })\r\n          break\r\n        case 'gjedit':\r\n          this.$router.push(\r\n            { name: 'ProfessionalCategoryListInfo',\r\n              query: {\r\n                pg_redirect: this.$route.name,\r\n                typeCode: row.row.code,\r\n                materialCode: row.row.materialcode,\r\n                name: row.row.name,\r\n                unit: row.row.unit,\r\n                steel_unit: row.row.steel_unit,\r\n                typeId: row.row.id\r\n              }\r\n            })\r\n          break\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.page-container{\r\n  margin:16px;\r\n  background: #fff;\r\n  padding:16px;\r\n  box-sizing: border-box;\r\n}\r\n</style>\r\n"]}]}
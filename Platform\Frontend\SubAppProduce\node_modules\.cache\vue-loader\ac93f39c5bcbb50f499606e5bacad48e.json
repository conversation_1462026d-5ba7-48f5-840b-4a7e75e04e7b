{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\CheckNode.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\CheckNode.vue", "mtime": 1757468112671}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["CheckNode.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CheckNode.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup/components", "sourcesContent": ["<template>\r\n  <div style=\"height: calc(100vh - 300px)\">\r\n    <vxe-table\r\n      v-loading=\"tbLoading\"\r\n      :empty-render=\"{name: 'NotData'}\"\r\n      show-header-overflow\r\n      element-loading-spinner=\"el-icon-loading\"\r\n      element-loading-text=\"拼命加载中\"\r\n      empty-text=\"暂无数据\"\r\n      height=\"100%\"\r\n      :data=\"tbData\"\r\n      stripe\r\n      resizable\r\n      :auto-resize=\"true\"\r\n      class=\"cs-vxe-table\"\r\n      :tooltip-config=\"{ enterable: true }\"\r\n    >\r\n      <!-- <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" /> -->\r\n      <vxe-column\r\n        v-for=\"(item, index) in columns\"\r\n        :key=\"index\"\r\n        :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n        show-overflow=\"tooltip\"\r\n        sortable\r\n        :align=\"item.Align\"\r\n        :field=\"item.Code\"\r\n        :title=\"item.Display_Name\"\r\n      >\r\n        <template #default=\"{ row }\">\r\n          <span v-if=\"item.Code === 'Is_Special_Check'\">\r\n            <el-tag v-if=\"row.Is_Special_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"info\">否</el-tag>\r\n          </span>\r\n          <span v-else-if=\"item.Code === 'Is_Inter_Check'\">\r\n            <el-tag v-if=\"row.Is_Inter_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"info\">否</el-tag>\r\n          </span>\r\n          <span v-else>{{ row[item.Code] || \"-\" }}</span>\r\n        </template>\r\n      </vxe-column>\r\n      <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" show-overflow align=\"center\">\r\n        <template #default=\"{ row }\">\r\n          <el-button v-if=\"!row.Node_Code||(row.Node_Code&&row.Check_Style === '抽检')\" type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\r\n          <el-divider v-if=\"!row.Node_Code\" direction=\"vertical\" />\r\n          <el-button v-if=\"!row.Node_Code\" type=\"text\" @click=\"removeEvent(row)\">删除</el-button>\r\n        </template>\r\n      </vxe-column>\r\n    </vxe-table>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { DelNode } from '@/api/PRO/factorycheck'\r\nimport { GetNodeList } from '@/api/PRO/factorycheck'\r\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\r\nimport { timeFormat } from '@/filters'\r\nexport default {\r\n  props: {\r\n    checkType: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      tbData: [],\r\n      columns: [],\r\n      tbLoading: false\r\n    }\r\n  },\r\n  watch: {\r\n    checkType: {\r\n      handler(newName, oldName) {\r\n        this.checkType = newName\r\n        this.getNodeList()\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getNodeList()\r\n    this.getTypeList()\r\n  },\r\n  methods: {\r\n    async getTypeList() {\r\n      const res = await GetFactoryProfessionalByCode({\r\n        factoryId: localStorage.getItem('CurReferenceId')\r\n      })\r\n      const data = res.Data\r\n      if (res.IsSucceed) {\r\n        this.typeOption = Object.freeze(data)\r\n        console.log(this.typeOption)\r\n        if (this.typeOption.length > 0) {\r\n          this.TypeId = this.typeOption[0]?.Id\r\n          this.fetchData()\r\n        }\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    fetchData() {\r\n      this.getTableConfig('Quality_Inspection_Node')\r\n      //   this.tbLoading = true;\r\n    },\r\n    getNodeList() {\r\n      this.tbLoading = true\r\n      GetNodeList({ check_object_id: this.checkType.Id, Bom_Level: this.checkType.Code }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.map(v => {\r\n            switch (v.Check_Style) {\r\n              case 0 : v.Check_Style = '抽检'; break // 谁写的，坑死了\r\n              case 1 : v.Check_Style = '全检'; break\r\n              default: v.Check_Style = ''\r\n            }\r\n            switch (v.Check_Type) {\r\n              case 1 : v.Check_Type = '质量'; break\r\n              case 2 : v.Check_Type = '探伤'; break\r\n              case -1 : v.Check_Type = '质量/探伤'; break\r\n              default: v.Check_Type = ''\r\n            }\r\n            v.Create_Date = timeFormat(v.Create_Date, '{y}-{m}-{d} {h}:{i}:{s}')\r\n            return v\r\n          })\r\n          console.log(res.Data)\r\n          this.tbLoading = false\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n          this.tbLoading = false\r\n        }\r\n      })\r\n    },\r\n    getTableConfig(code) {\r\n      GetGridByCode({ code: code + ',' + this.typeOption.find((i) => i.Id === this.TypeId).Code }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          if (!Data) {\r\n            this.$message.error('当前专业没有配置相对应表格')\r\n            this.tbLoading = true\r\n            return\r\n          }\r\n          const list = Data.ColumnList || []\r\n          this.columns = list\r\n          console.log(this.columns)\r\n          this.tbLoading = false\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 删除单个检查项组合\r\n    removeEvent(row) {\r\n      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          DelNode({ id: row.Id }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                type: 'success',\r\n                message: '删除成功!'\r\n              })\r\n              this.getNodeList()\r\n            } else {\r\n              this.$message({\r\n                type: 'error',\r\n                message: res.Message\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n\r\n    // 编辑每行信息\r\n    editEvent(row) {\r\n      // 获取每行内容\r\n      console.log('row', row)\r\n      this.$emit('NodeEdit', row)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped></style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\CheckNode.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\CheckNode.vue", "mtime": 1757468112671}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetGridByCode", "DelNode", "GetNodeList", "GetFactoryProfessionalByCode", "timeFormat", "props", "checkType", "type", "Object", "default", "data", "tbData", "columns", "tbLoading", "watch", "handler", "newName", "old<PERSON>ame", "getNodeList", "deep", "mounted", "getTypeList", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "_this$typeOption$", "wrap", "_callee$", "_context", "prev", "next", "factoryId", "localStorage", "getItem", "sent", "Data", "IsSucceed", "typeOption", "freeze", "console", "log", "length", "TypeId", "Id", "fetchData", "$message", "message", "Message", "stop", "getTableConfig", "_this2", "check_object_id", "Bom_Level", "Code", "then", "map", "v", "Check_Style", "Check_Type", "Create_Date", "code", "_this3", "find", "i", "error", "list", "ColumnList", "removeEvent", "row", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "id", "catch", "editEvent", "$emit"], "sources": ["src/views/PRO/factoryQuality/checkoutGroup/components/CheckNode.vue"], "sourcesContent": ["<template>\r\n  <div style=\"height: calc(100vh - 300px)\">\r\n    <vxe-table\r\n      v-loading=\"tbLoading\"\r\n      :empty-render=\"{name: 'NotData'}\"\r\n      show-header-overflow\r\n      element-loading-spinner=\"el-icon-loading\"\r\n      element-loading-text=\"拼命加载中\"\r\n      empty-text=\"暂无数据\"\r\n      height=\"100%\"\r\n      :data=\"tbData\"\r\n      stripe\r\n      resizable\r\n      :auto-resize=\"true\"\r\n      class=\"cs-vxe-table\"\r\n      :tooltip-config=\"{ enterable: true }\"\r\n    >\r\n      <!-- <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" /> -->\r\n      <vxe-column\r\n        v-for=\"(item, index) in columns\"\r\n        :key=\"index\"\r\n        :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n        show-overflow=\"tooltip\"\r\n        sortable\r\n        :align=\"item.Align\"\r\n        :field=\"item.Code\"\r\n        :title=\"item.Display_Name\"\r\n      >\r\n        <template #default=\"{ row }\">\r\n          <span v-if=\"item.Code === 'Is_Special_Check'\">\r\n            <el-tag v-if=\"row.Is_Special_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"info\">否</el-tag>\r\n          </span>\r\n          <span v-else-if=\"item.Code === 'Is_Inter_Check'\">\r\n            <el-tag v-if=\"row.Is_Inter_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"info\">否</el-tag>\r\n          </span>\r\n          <span v-else>{{ row[item.Code] || \"-\" }}</span>\r\n        </template>\r\n      </vxe-column>\r\n      <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" show-overflow align=\"center\">\r\n        <template #default=\"{ row }\">\r\n          <el-button v-if=\"!row.Node_Code||(row.Node_Code&&row.Check_Style === '抽检')\" type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\r\n          <el-divider v-if=\"!row.Node_Code\" direction=\"vertical\" />\r\n          <el-button v-if=\"!row.Node_Code\" type=\"text\" @click=\"removeEvent(row)\">删除</el-button>\r\n        </template>\r\n      </vxe-column>\r\n    </vxe-table>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { DelNode } from '@/api/PRO/factorycheck'\r\nimport { GetNodeList } from '@/api/PRO/factorycheck'\r\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\r\nimport { timeFormat } from '@/filters'\r\nexport default {\r\n  props: {\r\n    checkType: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      tbData: [],\r\n      columns: [],\r\n      tbLoading: false\r\n    }\r\n  },\r\n  watch: {\r\n    checkType: {\r\n      handler(newName, oldName) {\r\n        this.checkType = newName\r\n        this.getNodeList()\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getNodeList()\r\n    this.getTypeList()\r\n  },\r\n  methods: {\r\n    async getTypeList() {\r\n      const res = await GetFactoryProfessionalByCode({\r\n        factoryId: localStorage.getItem('CurReferenceId')\r\n      })\r\n      const data = res.Data\r\n      if (res.IsSucceed) {\r\n        this.typeOption = Object.freeze(data)\r\n        console.log(this.typeOption)\r\n        if (this.typeOption.length > 0) {\r\n          this.TypeId = this.typeOption[0]?.Id\r\n          this.fetchData()\r\n        }\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    fetchData() {\r\n      this.getTableConfig('Quality_Inspection_Node')\r\n      //   this.tbLoading = true;\r\n    },\r\n    getNodeList() {\r\n      this.tbLoading = true\r\n      GetNodeList({ check_object_id: this.checkType.Id, Bom_Level: this.checkType.Code }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.map(v => {\r\n            switch (v.Check_Style) {\r\n              case 0 : v.Check_Style = '抽检'; break // 谁写的，坑死了\r\n              case 1 : v.Check_Style = '全检'; break\r\n              default: v.Check_Style = ''\r\n            }\r\n            switch (v.Check_Type) {\r\n              case 1 : v.Check_Type = '质量'; break\r\n              case 2 : v.Check_Type = '探伤'; break\r\n              case -1 : v.Check_Type = '质量/探伤'; break\r\n              default: v.Check_Type = ''\r\n            }\r\n            v.Create_Date = timeFormat(v.Create_Date, '{y}-{m}-{d} {h}:{i}:{s}')\r\n            return v\r\n          })\r\n          console.log(res.Data)\r\n          this.tbLoading = false\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n          this.tbLoading = false\r\n        }\r\n      })\r\n    },\r\n    getTableConfig(code) {\r\n      GetGridByCode({ code: code + ',' + this.typeOption.find((i) => i.Id === this.TypeId).Code }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          if (!Data) {\r\n            this.$message.error('当前专业没有配置相对应表格')\r\n            this.tbLoading = true\r\n            return\r\n          }\r\n          const list = Data.ColumnList || []\r\n          this.columns = list\r\n          console.log(this.columns)\r\n          this.tbLoading = false\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 删除单个检查项组合\r\n    removeEvent(row) {\r\n      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          DelNode({ id: row.Id }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                type: 'success',\r\n                message: '删除成功!'\r\n              })\r\n              this.getNodeList()\r\n            } else {\r\n              this.$message({\r\n                type: 'error',\r\n                message: res.Message\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n\r\n    // 编辑每行信息\r\n    editEvent(row) {\r\n      // 获取每行内容\r\n      console.log('row', row)\r\n      this.$emit('NodeEdit', row)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped></style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkDA,SAAAA,aAAA;AACA,SAAAC,OAAA;AACA,SAAAC,WAAA;AACA,SAAAC,4BAAA;AACA,SAAAC,UAAA;AACA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA;MACAC,OAAA;MACAC,SAAA;IACA;EACA;EACAC,KAAA;IACAR,SAAA;MACAS,OAAA,WAAAA,QAAAC,OAAA,EAAAC,OAAA;QACA,KAAAX,SAAA,GAAAU,OAAA;QACA,KAAAE,WAAA;MACA;MACAC,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAF,WAAA;IACA,KAAAG,WAAA;EACA;EACAC,OAAA;IACAD,WAAA,WAAAA,YAAA;MAAA,IAAAE,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA,EAAAlB,IAAA,EAAAmB,iBAAA;QAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACA/B,4BAAA;gBACAgC,SAAA,EAAAC,YAAA,CAAAC,OAAA;cACA;YAAA;cAFAT,GAAA,GAAAI,QAAA,CAAAM,IAAA;cAGA5B,IAAA,GAAAkB,GAAA,CAAAW,IAAA;cACA,IAAAX,GAAA,CAAAY,SAAA;gBACAjB,KAAA,CAAAkB,UAAA,GAAAjC,MAAA,CAAAkC,MAAA,CAAAhC,IAAA;gBACAiC,OAAA,CAAAC,GAAA,CAAArB,KAAA,CAAAkB,UAAA;gBACA,IAAAlB,KAAA,CAAAkB,UAAA,CAAAI,MAAA;kBACAtB,KAAA,CAAAuB,MAAA,IAAAjB,iBAAA,GAAAN,KAAA,CAAAkB,UAAA,iBAAAZ,iBAAA,uBAAAA,iBAAA,CAAAkB,EAAA;kBACAxB,KAAA,CAAAyB,SAAA;gBACA;cACA;gBACAzB,KAAA,CAAA0B,QAAA;kBACAC,OAAA,EAAAtB,GAAA,CAAAuB,OAAA;kBACA5C,IAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAyB,QAAA,CAAAoB,IAAA;UAAA;QAAA,GAAAzB,OAAA;MAAA;IACA;IACAqB,SAAA,WAAAA,UAAA;MACA,KAAAK,cAAA;MACA;IACA;IACAnC,WAAA,WAAAA,YAAA;MAAA,IAAAoC,MAAA;MACA,KAAAzC,SAAA;MACAX,WAAA;QAAAqD,eAAA,OAAAjD,SAAA,CAAAyC,EAAA;QAAAS,SAAA,OAAAlD,SAAA,CAAAmD;MAAA,GAAAC,IAAA,WAAA9B,GAAA;QACA,IAAAA,GAAA,CAAAY,SAAA;UACAc,MAAA,CAAA3C,MAAA,GAAAiB,GAAA,CAAAW,IAAA,CAAAoB,GAAA,WAAAC,CAAA;YACA,QAAAA,CAAA,CAAAC,WAAA;cACA;gBAAAD,CAAA,CAAAC,WAAA;gBAAA;cAAA;cACA;gBAAAD,CAAA,CAAAC,WAAA;gBAAA;cACA;gBAAAD,CAAA,CAAAC,WAAA;YACA;YACA,QAAAD,CAAA,CAAAE,UAAA;cACA;gBAAAF,CAAA,CAAAE,UAAA;gBAAA;cACA;gBAAAF,CAAA,CAAAE,UAAA;gBAAA;cACA;gBAAAF,CAAA,CAAAE,UAAA;gBAAA;cACA;gBAAAF,CAAA,CAAAE,UAAA;YACA;YACAF,CAAA,CAAAG,WAAA,GAAA3D,UAAA,CAAAwD,CAAA,CAAAG,WAAA;YACA,OAAAH,CAAA;UACA;UACAjB,OAAA,CAAAC,GAAA,CAAAhB,GAAA,CAAAW,IAAA;UACAe,MAAA,CAAAzC,SAAA;QACA;UACAyC,MAAA,CAAAL,QAAA;YACA1C,IAAA;YACA2C,OAAA,EAAAtB,GAAA,CAAAuB;UACA;UACAG,MAAA,CAAAzC,SAAA;QACA;MACA;IACA;IACAwC,cAAA,WAAAA,eAAAW,IAAA;MAAA,IAAAC,MAAA;MACAjE,aAAA;QAAAgE,IAAA,EAAAA,IAAA,cAAAvB,UAAA,CAAAyB,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAApB,EAAA,KAAAkB,MAAA,CAAAnB,MAAA;QAAA,GAAAW;MAAA,GAAAC,IAAA,WAAA9B,GAAA;QACA,IAAAY,SAAA,GAAAZ,GAAA,CAAAY,SAAA;UAAAD,IAAA,GAAAX,GAAA,CAAAW,IAAA;UAAAY,OAAA,GAAAvB,GAAA,CAAAuB,OAAA;QACA,IAAAX,SAAA;UACA,KAAAD,IAAA;YACA0B,MAAA,CAAAhB,QAAA,CAAAmB,KAAA;YACAH,MAAA,CAAApD,SAAA;YACA;UACA;UACA,IAAAwD,IAAA,GAAA9B,IAAA,CAAA+B,UAAA;UACAL,MAAA,CAAArD,OAAA,GAAAyD,IAAA;UACA1B,OAAA,CAAAC,GAAA,CAAAqB,MAAA,CAAArD,OAAA;UACAqD,MAAA,CAAApD,SAAA;QACA;UACAoD,MAAA,CAAAhB,QAAA;YACAC,OAAA,EAAAC,OAAA;YACA5C,IAAA;UACA;QACA;MACA;IACA;IACA;IACAgE,WAAA,WAAAA,YAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACArE,IAAA;MACA,GACAmD,IAAA;QACAzD,OAAA;UAAA4E,EAAA,EAAAL,GAAA,CAAAzB;QAAA,GAAAW,IAAA,WAAA9B,GAAA;UACA,IAAAA,GAAA,CAAAY,SAAA;YACAiC,MAAA,CAAAxB,QAAA;cACA1C,IAAA;cACA2C,OAAA;YACA;YACAuB,MAAA,CAAAvD,WAAA;UACA;YACAuD,MAAA,CAAAxB,QAAA;cACA1C,IAAA;cACA2C,OAAA,EAAAtB,GAAA,CAAAuB;YACA;UACA;QACA;MACA,GACA2B,KAAA;QACAL,MAAA,CAAAxB,QAAA;UACA1C,IAAA;UACA2C,OAAA;QACA;MACA;IACA;IAEA;IACA6B,SAAA,WAAAA,UAAAP,GAAA;MACA;MACA7B,OAAA,CAAAC,GAAA,QAAA4B,GAAA;MACA,KAAAQ,KAAA,aAAAR,GAAA;IACA;EACA;AACA", "ignoreList": []}]}
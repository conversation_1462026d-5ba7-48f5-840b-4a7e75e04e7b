
<template>
  <div v-loading="pgLoading" class="page-container">
    <!-- <el-button style="margin-bottom: 16px" @click="backPage">返回</el-button>-->
    <div class="top-wrapper">
      <div class="info">
        <template v-if="!!majorName">
          <div class="title">当前专业：</div>
          <div class="value">{{ majorName }}</div>
        </template>
        <template v-if="!!unit">
          <div class="title">统计单位：</div>
          <div class="value">{{ unit }}</div>
        </template>
        <template v-if="!!steelUnit">
          <div class="title">构件单位：</div>
          <div class="value">{{ steelUnit }}</div>
        </template>
        <template>
          <div class="title">单位统计字段：</div>
          {{ unitInfo }}
        </template>
      </div>
      <el-tabs v-model="activeName">
        <el-tab-pane v-for="(item,index) in tabList" :key="index" :label="item.label" :name="item.value" />
      </el-tabs>
    </div>

    <div class="cs-content-wrapper">
      <div class="content-top">
        <span class="content-title">系统字段</span>
        <div class="content-top-right">
          <label class="cs-label">
            <span>字段名称：</span>
            <el-input v-model="searchValue" placeholder="请输入" clearable="" />
          </label>
          <div>
            <el-button type="primary" @click="filterList">查询</el-button>
            <el-button type="primary" :loading="saveLoading" @click="save">保存设置</el-button>
            <el-button type="success" :loading="restoreLoading1" @click="restore(1)">恢复默认二级清单</el-button>
            <el-button type="success" :loading="restoreLoading2" @click="restore(2)">恢复默认三级清单</el-button>
          </div>
        </div>
      </div>
      <el-form ref="form" :model="form" label-width="100px">
        <el-row v-for="item in list" :key="item.uuid">
          <el-col :span="6">
            <el-form-item label="字段名：">
              <el-input v-model="item.Display_Name" clearble :class="['w100',{'showRed':item.showRed}]" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="Code：">
              <el-input v-model="item.Code" clearble class="w100" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="备注说明：">
              <el-input v-model="item.Remark" clearble class="w100" />
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-form-item label="排序：">
              <el-input-number v-model.number="item.Sort" :min="0" class="w100 cs-number-btn-hidden" clearble />
            </el-form-item>
          </el-col>
          <el-col v-if="!item.showRed" :span="3">
            <el-form-item label="是否启用：">
              <el-switch v-model="item.Is_Enabled" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>
<script>
import { closeTagView, deepClone } from '@/utils'
import { GetTableSettingList, RestoreTemplateType, SavDeepenTemplateSetting } from '@/api/PRO/component-type'
import { v4 as uuidv4 } from 'uuid'

export default {
  name: 'PROBomImportTemplateConfig',
  data() {
    return {
      activeName: 'pz',
      tabList: [{
        label: '深化清单导入配置',
        value: 'pz'
      }],
      searchValue: '',
      majorName: '',
      unit: '',
      steelUnit: '',
      templateListNew: [],
      list: [],
      form: {},
      pgLoading: false,
      saveLoading: false,
      restoreLoading1: false,
      restoreLoading2: false,
      unitInfo: ''

    }
  },
  mounted() {
    this.majorName = this.$route.query.name || ''
    this.unit = this.$route.query.unit || ''
    this.steelUnit = this.$route.query.steel_unit || ''
    this.fetchData()
  },
  methods: {

    fetchData() {
      this.pgLoading = true
      GetTableSettingList({
        ProfessionalCode: 'Steel'
      }).then(res => {
        if (res.IsSucceed) {
          let result = ''
          this.defaultList = res.Data.map(item => {
            item.uuid = uuidv4()
            if (item.Code === 'SteelAmount') {
              result += `${item.Display_Name || ''}*`
            }
            if (item.Code === 'SteelWeight') {
              result += `${item.Display_Name || ''}`
            }
            item.showRed = item.Column_Type === 0
            return item
          })
          this.list = deepClone(this.defaultList)
          this.unitInfo = result
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
        this.pgLoading = false
      })
    },
    filterList() {
      if (!this.searchValue) {
        this.list = deepClone(this.defaultList)
        return
      }
      this.list = this.defaultList.filter(item => {
        return item.Display_Name.includes(this.searchValue)
      })
    },
    save() {
      const hasEmpty = this.list.some(item =>
        !item.Display_Name || item.Display_Name.trim() === ''
      )
      if (hasEmpty) {
        this.$message.error('字段名不能为空')
        return
      }

      const nameSet = new Set()
      const duplicates = this.list.filter(item => {
        if (nameSet.has(item.Display_Name)) {
          return true
        }
        nameSet.add(item.Display_Name)
        return false
      })

      if (duplicates.length > 0) {
        this.$message.error(`存在重复的字段名 : ${duplicates.map(d => d.Display_Name).join(', ')}`)
        return []
      }

      this.$confirm('是否保存当前配置?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.saveLoading = true
        const submitList = this.list.map(item => {
          return {
            Professional_Code: 'Steel',
            Is_Component: '',
            Code: item.Code,
            Display_Name: item.Display_Name,
            Column_Type: item.Column_Type,
            Sort: item.Sort,
            Remark: item.Remark,
            Is_Enabled: item.Is_Enabled
          }
        })
        SavDeepenTemplateSetting(submitList).then(res => {
          if (res.IsSucceed) {
            this.$message({
              message: '保存成功',
              type: 'success'
            })
            this.fetchData()
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
          this.saveLoading = false
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
        this.saveLoading = false
      })
    },
    restore(type) {
      const label = type === 1 ? '二级' : '三级'
      if (type === 1) {
        this.restoreLoading1 = true
      } else {
        this.restoreLoading2 = true
      }
      this.$confirm(`此是否恢复默认${label}清单?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        RestoreTemplateType({
          ProfessionalCode: 'Steel',
          Type: type
        }).then(res => {
          if (res.IsSucceed) {
            this.fetchData()
            this.$message({
              type: 'success',
              message: '恢复成功!'
            })
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
          this.restoreLoading1 = false
          this.restoreLoading2 = false
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
        this.restoreLoading1 = false
        this.restoreLoading2 = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  margin: 16px;
  box-sizing: border-box;

  .top-wrapper {
    background: #fff;
    padding: 16px 16px 0 16px;
    box-sizing: border-box;

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #333333;
    }
    .info{
      font-size: 14px;
      display: flex;
      flex-direction: row;
      margin-bottom: 16px;
      .title{
        font-size: 14px;
        color: #999999;
      }
      .value{
        color: #333333;
        margin-right: 24px;
      }
    }
  }

  .cs-content-wrapper{
    background-color: #ffffff;
    margin-top: 16px;
    padding: 16px;
    .content-top{
      margin-bottom: 32px;
      display: flex;
      justify-content: space-between;
      .content-title{
        font-weight: 400;
        color: #1f2f3d;
        font-size: 22px;
      }
      .cs-label{
        font-size: 14px;
        font-family: "Microsoft YaHei", "微软雅黑", "PingFang SC", "Hiragino Sans GB", "Helvetica Neue", Arial, sans-serif;
        font-weight: normal;
        display: flex;
        white-space: nowrap;
        align-items: center;
        margin-right: 8px;

        span{
          margin-right: 16px;
        }
      }
      .content-top-right{
        display: flex;
      }
    }
    .showRed{
      ::v-deep{
        .el-input__inner {
          color:red !important;
        }
      }
    }
  }
}
</style>

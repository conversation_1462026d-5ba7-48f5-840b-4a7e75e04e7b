{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\nesting-management\\index.vue?vue&type=style&index=0&id=41e295c5&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\nesting-management\\index.vue", "mtime": 1757468127975}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmVsLWRpdmlkZXJ7DQogIG1hcmdpbjowIDAgOHB4ICAwOw0KfQ0KLmNzLXotcGFnZS1tYWluLWNvbnRlbnR7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIC50Yi14ew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICBmbGV4OiAxOw0KICAgIG92ZXJmbG93OiBoaWRkZW47DQogIH0NCiAgLmRhdGEtaW5mb3sNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIC5pbmZvLXh7DQogICAgICBtYXJnaW4tcmlnaHQ6IDE2cHg7DQogICAgfQ0KICB9DQp9DQoucGFnaW5hdGlvbi1jb250YWluZXIgew0KICB0ZXh0LWFsaWduOiByaWdodDsNCiAgcGFkZGluZzogMTZweCAxNnB4IDAgMTZweDsNCiAgbWFyZ2luOiAwOw0KfQ0KLmVsLWZvcm0taXRlbXsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkbA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/nesting-management", "sourcesContent": ["<template>\r\n  <div class=\"cs-z-flex-pd16-wrap abs100\">\r\n    <div class=\"cs-z-page-main-content\">\r\n      <el-form ref=\"form\" :model=\"form\" inline label-width=\"80px\" style=\"width: 100%\">\r\n        <el-form-item label=\"排版名称\" prop=\"Nesting_Result_Name\">\r\n          <el-input v-model=\"form.Nesting_Result_Name\" placeholder=\"请输入\" clearable type=\"text\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"原料名称\" prop=\"Raw_Name\">\r\n          <el-input v-model=\"form.Raw_Name\" placeholder=\"请输入\" type=\"text\" clearable />\r\n        </el-form-item>\r\n        <el-form-item label=\"厚度\" prop=\"Thickness\" label-width=\"50px\">\r\n          <el-input-number v-model=\"form.Thickness\" :min=\"0\" :max=\"1000000\" class=\"w100 cs-number-btn-hidden\" placeholder=\"请输入\" clearable />\r\n        </el-form-item>\r\n        <el-form-item label=\"材质\" prop=\"Texture\" label-width=\"50px\">\r\n          <el-input v-model=\"form.Texture\" placeholder=\"请输入\" type=\"text\" clearable />\r\n        </el-form-item>\r\n        <el-form-item label=\"切割状态\" prop=\"Cut_Status\">\r\n          <el-select v-model=\"form.Cut_Status\" placeholder=\"请选择\" clearable style=\"width: 120px\">\r\n            <el-option label=\"待切割\" value=\"待切割\" />\r\n            <el-option label=\"已切割\" value=\"已切割\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"类型\" prop=\"Type\" label-width=\"50px\">\r\n          <el-select v-model=\"form.Type\" placeholder=\"请选择\" clearable style=\"width: 120px\">\r\n            <el-option label=\"手动导入\" :value=\"1\" />\r\n            <el-option label=\"系统推送\" :value=\"2\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"fetchData(1)\">查询</el-button>\r\n          <el-button @click=\"handleReset\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <vxe-toolbar ref=\"xToolbar\">\r\n        <template #buttons>\r\n          <el-button type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"toCreatePickList\">生成内调单</el-button>\r\n          <el-button type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"toCreateReturnList\">生成余料退库单</el-button>\r\n          <el-button type=\"success\" @click=\"handleImport\">导入套料报告</el-button>\r\n          <el-button type=\"success\" @click=\"handleImportResult\">导入套料结果</el-button>\r\n          <el-button type=\"success\" @click=\"handleImportThumbs\">导入缩略图</el-button>\r\n          <el-button type=\"danger\" :disabled=\"!multipleSelection.length\" @click=\"handleDelete\">删除</el-button>\r\n          <el-button type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"handleSchedule\">下发排产任务</el-button>\r\n          <el-button type=\"default\" :disabled=\"!multipleSelection.length\" :loading=\"downloadLoading\" @click=\"handleDownload\">导出加工信息</el-button>\r\n          <el-upload\r\n            :action=\"$baseUrl + 'Pro/Nesting/ImportProcess'\"\r\n            :show-file-list=\"false\"\r\n            :headers=\"headers\"\r\n            style=\"margin: 0 10px\"\r\n            accept=\".xlsx,.xls\"\r\n            :on-success=\"()=>fetchData(1)\"\r\n          >\r\n            <el-button type=\"default\">导入加工信息</el-button>\r\n          </el-upload>\r\n          <el-upload\r\n            :action=\"$baseUrl + 'Pro/Nesting/ImportProfiles'\"\r\n            :show-file-list=\"false\"\r\n            :on-success=\"uploadSuccess\"\r\n            :headers=\"headers\"\r\n            accept=\".xlsx,.xls\"\r\n          >\r\n            <el-button type=\"default\">导入型材</el-button>\r\n          </el-upload>\r\n          <DynamicTableFields\r\n            style=\"margin-left: auto\"\r\n            title=\"表格配置\"\r\n            table-config-code=\"PRONestingManagementIndex\"\r\n            @updateColumn=\"getTbConfig\"\r\n          />\r\n        </template>\r\n      </vxe-toolbar>\r\n\r\n      <div v-loading=\"tbLoading\" class=\"tb-x\">\r\n        <vxe-table\r\n          v-if=\"!tbLoading\"\r\n          ref=\"xTable\"\r\n          class=\"cs-vxe-table\"\r\n          :checkbox-config=\"{checkField: 'checked'}\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          :row-config=\"{ isCurrent: true, isHover: true}\"\r\n          align=\"center\"\r\n          height=\"auto\"\r\n          show-overflow\r\n          :auto-resize=\"true\"\r\n          stripe\r\n          size=\"medium\"\r\n          :data=\"tbData\"\r\n          resizable\r\n          :tooltip-config=\"{ enterable: true }\"\r\n          @checkbox-all=\"tbSelectChange\"\r\n          @checkbox-change=\"tbSelectChange\"\r\n        >\r\n          <vxe-column fixed=\"left\" type=\"checkbox\" />\r\n\r\n          <template v-for=\"item in columns\">\r\n            <vxe-column\r\n              :key=\"item.Code\"\r\n              :fixed=\"item.Is_Frozen ? (item.Frozen_Dirction || 'left') : ''\"\r\n              show-overflow=\"tooltip\"\r\n              :align=\"item.Align\"\r\n              :field=\"item.Code\"\r\n              :visible=\"item.Is_Display\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n              :edit-render=\"item.Is_Edit ? {} : null\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                <span v-if=\"item.Code === 'Type'\">\r\n                  <el-tag v-if=\"row[item.Code] === 1\" effect=\"plain\" type=\"success\">手动导入</el-tag>\r\n                  <el-tag v-else effect=\"plain\" type=\"warning\">自动推送</el-tag>\r\n                </span>\r\n                <span v-else-if=\"item.Code === 'Surplus_Raw'\">\r\n                  <el-button v-if=\"row.Surplus_Count>0\" type=\"text\" @click=\"handleRaw(row)\">查看</el-button>\r\n                </span>\r\n                <span v-else-if=\"item.Code === 'Part_Amount'\">\r\n                  <el-link type=\"primary\" :underline=\"false\" @click=\"handleAmount(row)\">{{ row[item.Code] }}</el-link>\r\n                </span>\r\n                <span v-else-if=\"item.Code === 'Utilization'\">\r\n                  {{ row[item.Code] }}<span v-if=\"row[item.Code]\">%</span>\r\n                </span>\r\n                <span v-else>{{ row[item.Code] }}</span>\r\n              </template>\r\n            </vxe-column>\r\n          </template>\r\n        </vxe-table>\r\n      </div>\r\n      <footer class=\"data-info\">\r\n        <el-tag\r\n          size=\"medium\"\r\n          class=\"info-x\"\r\n        >已选 {{ multipleSelection.length }} 条数据\r\n        </el-tag>\r\n        <Pagination\r\n          :total=\"total\"\r\n          max-height=\"100%\"\r\n          :page-sizes=\"tablePageSize\"\r\n          :page.sync=\"queryInfo.Page\"\r\n          :limit.sync=\"queryInfo.PageSize\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          @pagination=\"pageChange\"\r\n        />\r\n      </footer>\r\n\r\n      <el-dialog\r\n        v-if=\"dialogVisible\"\r\n        v-dialogDrag\r\n        class=\"plm-custom-dialog\"\r\n        :title=\"title\"\r\n        :visible.sync=\"dialogVisible\"\r\n        :width=\"width\"\r\n        @close=\"handleClose\"\r\n      >\r\n        <component\r\n          :is=\"currentComponent\"\r\n          ref=\"content\"\r\n          @close=\"handleClose\"\r\n          @refresh=\"fetchData(1)\"\r\n        />\r\n      </el-dialog>\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Pagination from '@/components/Pagination/index.vue'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nimport { DeleteNestingResult, GetNestingResultPageList } from '@/api/PRO/production-task'\r\nimport SurplusRaw from './components/SurplusRaw.vue'\r\nimport NestReport from './components/NestReport.vue'\r\nimport NestResult from './components/NestResult.vue'\r\nimport NestThumbs from './components/NestThumbs.vue'\r\nimport PartsLayout from './components/PartsLayout.vue'\r\nimport { mapActions } from 'vuex'\r\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\r\nimport { ExportProcess } from '@/api/PRO/materialManagement'\r\nimport { combineURL } from '@/utils'\r\nimport OSSUpload from '@/views/plm/components/ossupload.vue'\r\nimport { getToken } from '@/utils/auth'\r\n\r\nexport default {\r\n  name: 'PRONestingManagement',\r\n  components: { OSSUpload, DynamicTableFields, Pagination, SurplusRaw, NestReport, NestResult, PartsLayout, NestThumbs },\r\n  mixins: [getTbInfo, addRouterPage],\r\n  data() {\r\n    return {\r\n      addPageArray: [\r\n        {\r\n          path: '/material/pick/add',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/material_v4/pickApply/add.vue'),\r\n          name: 'AddMaterialPickList',\r\n          meta: { title: '新增领料单' },\r\n          query: { pg_redirect: this.$route.name }\r\n        },\r\n        {\r\n          path: this.$route.path + '/requisition',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/nesting-management/requisition.vue'),\r\n          name: 'ModelCompare',\r\n          meta: { title: '领料单' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/schedule',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/nesting-management/schedule.vue'),\r\n          name: 'PRONestingSchedule',\r\n          meta: { title: '下发排产任务' }\r\n        }, {\r\n          path: this.$route.path + '/draft',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/plan-production/schedule-production-new-part/draft'),\r\n          name: 'PRO2PartScheduleDraftNestNew',\r\n          meta: { title: '草稿' }\r\n        }, {\r\n          path: this.$route.path + '/add-return',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/material-receipt-management/raw-stock-return/add.vue'),\r\n          name: 'PRORawMaterialStockReturnAddReturn',\r\n          meta: { title: '新建退库单' }\r\n        }\r\n      ],\r\n      form: {\r\n        Nesting_Result_Name: '',\r\n        Raw_Name: '',\r\n        Thickness: undefined,\r\n        Texture: '',\r\n        Cut_Status: '',\r\n        Type: undefined\r\n      },\r\n      dialogVisible: false,\r\n      width: '70%',\r\n      title: '',\r\n      currentComponent: '',\r\n      tbLoading: false,\r\n      options: [],\r\n      columns: [],\r\n      tbData: [],\r\n      multipleSelection: [],\r\n      tablePageSize: tablePageSize,\r\n      total: 0,\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: 20\r\n      },\r\n      downloadLoading: false,\r\n      headers: {\r\n        Authorization: getToken(),\r\n        Last_Working_Object_Id: localStorage.getItem('Last_Working_Object_Id')\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getTbConfig()\r\n    this.fetchData(1)\r\n  },\r\n  methods: {\r\n    uploadSuccess(response) {\r\n      if (response.IsSucceed) {\r\n        this.$message({\r\n          message: '上传成功',\r\n          type: 'success'\r\n        })\r\n        this.fetchData(1)\r\n      } else {\r\n        this.$message({\r\n          message: response.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    async getTbConfig() {\r\n      this.tbLoading = true\r\n      await this.getTableConfig('PRONestingManagementIndex')\r\n      this.tbLoading = false\r\n      const aaa = ['Picking_Bills', 'Out_Bills', 'Machining_Files', 'Thumbnail']\r\n      this.columns = this.columns.filter(item => !aaa.includes(item.Code))\r\n    },\r\n    ...mapActions('schedule', ['changeNestIds']),\r\n    fetchData(page) {\r\n      page && (this.queryInfo.Page = page)\r\n      this.tbLoading = true\r\n      GetNestingResultPageList({\r\n        ...this.queryInfo,\r\n        ...this.form\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.Data\r\n          this.total = res.Data.TotalCount\r\n          this.multipleSelection = []\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    handleSearch() {\r\n\r\n    },\r\n    handleImport() {\r\n      this.currentComponent = 'NestReport'\r\n      this.width = '30%'\r\n      this.title = '导入套料报告'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n\r\n      })\r\n    },\r\n    handleImportResult() {\r\n      this.currentComponent = 'NestResult'\r\n      this.width = '30%'\r\n      this.title = '导入套料结果'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n\r\n      })\r\n    },\r\n    handleImportThumbs() {\r\n      this.currentComponent = 'NestThumbs'\r\n      this.width = '30%'\r\n      this.title = '导入缩略图'\r\n      this.dialogVisible = true\r\n    },\r\n    handleDownload() {\r\n      this.downloadLoading = true\r\n      ExportProcess({ ids: this.multipleSelection.map(v => v.Id) }).then(res => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data))\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.downloadLoading = false\r\n      })\r\n    },\r\n    handleDelete() {\r\n      this.$confirm(' 是否删除该数据?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        DeleteNestingResult({\r\n          ids: this.multipleSelection.map(v => v.Id).toString()\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.fetchData(1)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    handleSubmit() {\r\n\r\n    },\r\n    tbSelectChange(array) {\r\n      console.log('111111')\r\n      this.multipleSelection = array.records\r\n    },\r\n    handleRaw(row) {\r\n      this.width = '70%'\r\n      this.currentComponent = 'SurplusRaw'\r\n      this.title = '余料信息'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].getData(row.Id)\r\n      })\r\n    },\r\n    handleAmount(row) {\r\n      this.width = '70%'\r\n      this.currentComponent = 'PartsLayout'\r\n      this.title = '排版零件'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].getData(row.Id)\r\n      })\r\n    },\r\n    handleSchedule() {\r\n      const ids = this.multipleSelection.filter(s => s.Type === 1).map(v => v.Id)\r\n      if (!ids) {\r\n        return\r\n      }\r\n      this.changeNestIds(ids)\r\n      this.$router.push({ name: 'PRO2PartScheduleDraftNestNew', query: { status: 'edit', pg_type: 'part', pg_redirect: this.$route.name, type: '1' }})\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    handleReset() {\r\n      this.$refs['form'].resetFields()\r\n      this.fetchData(1)\r\n    },\r\n    toCreatePickList() {\r\n      console.log(this.multipleSelection)\r\n      if (this.multipleSelection.some(item => item.PickNo)) {\r\n        this.$message.error('已有内调单号的不能重复生成')\r\n        return\r\n      }\r\n      this.$router.push({\r\n        name: 'AddMaterialPickList',\r\n        query: { pg_redirect: this.$route.name, type: 0, pickType: 1, ids: this.multipleSelection.map(i => i.Id) } // type原料为0,pickType：0手动生成 1套料生成\r\n      })\r\n    },\r\n    toCreateReturnList() {\r\n      this.$router.push({\r\n        name: 'PRORawMaterialStockReturnAddReturn',\r\n        query: { pg_redirect: this.$route.name, isNesting: 1, ids: this.multipleSelection.map(i => i.Id) }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.el-divider{\r\n  margin:0 0 8px  0;\r\n}\r\n.cs-z-page-main-content{\r\n  display: flex;\r\n  flex-direction: column;\r\n  .tb-x{\r\n    display: flex;\r\n    flex-direction: column;\r\n    flex: 1;\r\n    overflow: hidden;\r\n  }\r\n  .data-info{\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    .info-x{\r\n      margin-right: 16px;\r\n    }\r\n  }\r\n}\r\n.pagination-container {\r\n  text-align: right;\r\n  padding: 16px 16px 0 16px;\r\n  margin: 0;\r\n}\r\n.el-form-item{\r\n  margin-bottom: 10px;\r\n}\r\n</style>\r\n"]}]}
<template>
  <div v-loading="pgLoading" class="cs-container">
    <el-form ref="form" :model="form" label-width="100px">

      <el-form-item label="工艺代码">
        <el-select v-model="craftCode" filterable placeholder="下拉选择支持搜索" clearable="" @change="craftChange">
          <el-option
            v-for="item in gyList"
            :key="item.Code"
            :label="item.Code"
            :value="item.Code"
          />
        </el-select>
      </el-form-item>
      <el-divider />
      <draggable v-model="list" handle=".icon-drag" @change="changeDraggable">
        <transition-group>
          <el-row v-for="(element,index) in list" :key="element.key">
            <el-col :span="1"> <i class="iconfont icon-drag cs-drag" /> </el-col>
            <el-col :span="6">
              <el-form-item :label="`排产工序${index+1}`">
                <el-select :key="element.key" v-model="element.value" style="width:90%" :disabled="element.isPart" placeholder="请选择" clearable @change="selectChange($event,element)">
                  <el-option
                    v-for="item in options"
                    :key="item.Code"
                    :label="item.Name"
                    :disabled="item.disabled"
                    :value="item.Code"
                  >
                    <div class="cs-option">
                      <span class="cs-label">{{ item.Name }}</span>
                      <span v-if="item.Is_Nest && isNest" class="cs-tip">(套)</span>
                    </div>
                  </el-option>
                </el-select>

              </el-form-item>
            </el-col>

            <el-col v-if="isCom" :span="7">
              <el-form-item label="要求完成时间">
                <el-date-picker
                  :key="element.key"
                  v-model="element.date"
                  type="date"
                  value-format="yyyy-MM-dd"
                  style="width: 100%"
                  placeholder="选择日期"
                  @change="dateChange($event,element)"
                />
              </el-form-item>

            </el-col>
            <el-col :span="7">
              <el-form-item label="班组" label-width="60px">
                <el-select v-model="element.Working_Team_Id" clearable placeholder="请选择">
                  <el-option v-for="item in getWorkingTeam(element.Teams,element)" :key="item.Id" :label="item.Name" :value="item.Id" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="3">
              <span class="btn-x">
                <el-button v-if="index===0 && list.length<options.length" type="primary" icon="el-icon-plus" circle @click="handleAdd" />
                <el-button v-if="index!==0&&!element.isPart" type="danger" icon="el-icon-delete" circle @click="handleDelete(element)" />
              </span>
            </el-col>

          </el-row>
        </transition-group>
      </draggable>
    </el-form>
    <div class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button v-if="list.length" type="primary" :loading="btnLoading" @click="submit">确 定</el-button>
    </div>
  </div>
</template>

<script>
import { GetProcessListBase } from '@/api/PRO/technology-lib'
import Draggable from 'vuedraggable'
import { deepClone, uniqueArr } from '@/utils'
import { mapActions } from 'vuex'
import { v4 as uuidv4 } from 'uuid'
import { GetLibList } from '@/api/PRO/technology-lib'
export default {
  components: {
    Draggable
  },
  props: {
    pageType: {
      type: String,
      default: undefined
    },
    isNest: {
      type: Boolean,
      default: false
    },
    processList: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      Working_Team_List: [],
      list: [],
      options: [],
      // defaultOptions: [],
      gyList: [],
      btnLoading: false,
      pgLoading: false,
      craftCode: '',
      form: {}
    }
  },
  computed: {
    isCom() {
      return this.pageType === 'com'
    }
  },
  watch: {
    list: {
      handler(newVal) {
        if (!this.craftCode) return
        const workCode = this.gyList.find(v => v.Code === this.craftCode)?.WorkCode
        const newCode = newVal.map(v => v.value).filter(v => !!v).join('/')
        if (workCode !== newCode) {
          this.craftCode = ''
        }
      },
      deep: true
    }
  },
  methods: {
    ...mapActions('schedule', ['initProcessList']),
    getProcessOption(workshopId) {
      return new Promise((resolve, reject) => {
        this.pgLoading = true
        GetProcessListBase({
          workshopId: workshopId,
          type: 1 // 0:全部，工艺类型1：构件工艺，2：零件工艺
        }).then(res => {
          if (res.IsSucceed) {
            this.options = res.Data.map(v => {
              this.$set(v, 'disabled', false)
              return v
            })
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
          resolve()
        }).finally(_ => {
          this.pgLoading = false
        })
      })
    },
    craftChange(val) {
      this.craftCode = val
      if (!val) {
        // this.options = this.defaultOptions
        return
      }
      const info = this.gyList.find(v => v.Code === val)
      if (info) {
        const plist = info.WorkCode.split('/')
        // this.options = this.defaultOptions.filter(v => plist.includes(v.Code))
        const newList = []
        console.log('plist', plist)
        plist.forEach((listVal, idx) => {
          const item = this.list.find(v => v.value === listVal)
          console.log('item', item)
          if (item) {
            if (item.Teams.length === 1 && !item.Working_Team_Id) {
              item.Working_Team_Id = item.Teams[0].Id
            }
            newList.push(item)
          } else {
            const item2 = this.options.find(v => v.Code === listVal)
            console.log('item2', item2)
            if (item2) {
              const obj = {
                key: uuidv4(),
                value: item2.Code,
                Working_Team_Id: '',
                Teams: item2.Teams,
                date: ''
              }
              if (item2.Teams.length === 1 && !obj.Working_Team_Id) {
                obj.Working_Team_Id = item2.Teams[0].Id
              }
              newList.push(obj)
            }
          }
        })
        this.list = newList
      }
    },
    getCraftProcess() {
      return new Promise((resolve, reject) => {
        GetLibList({
          Id: '',
          Type: 1
        }).then(res => {
          if (res.IsSucceed) {
            this.gyList = (res.Data || [])
            resolve()
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
            reject()
          }
        })
      })
    },
    selectChange(val, element) {
      const arr = this.list.map(i => i.value)
      this.options.forEach((item, index) => {
        item.disabled = arr.includes(item.Code)
        if (item.Code === val) {
          element.Teams = item.Teams
        }
      })

      if (element) {
        if (val) {
          element.date = this.processList[val]?.Finish_Date
          element.Working_Team_Id = this.processList[val]?.Working_Team_Id
        } else {
          element.Working_Team_Id = ''
          element.Teams = []
        }
      }

      if (!element?.Working_Team_Id && element?.Teams?.length === 1) {
        element.Working_Team_Id = element.Teams[0].Id
      }
    },
    dateChange(val, element) {
      const item = this.options.find(v => v.Code === element.value)
      console.log('item', item, this.list)
      let obj = {}
      if (item) {
        obj = {
          Schduling_Id: this.formInline?.Schduling_Code,
          Process_Id: item.Id,
          Process_Code: item.Code,
          Finish_Date: val
        }
      }
      // this.$emit('setProcessList', { key: element.value, value: obj })
    },
    handleAdd(item) {
      const arr = this.list.map(v => v.value)
      this.options.forEach(v => {
        if (arr.includes(v.Code)) {
          v.disabled = true
        }
      })
      this.list.push({
        key: uuidv4(),
        value: '',
        Working_Team_Id: '',
        Teams: [],
        date: ''
      })
    },
    handleDelete(element) {
      const idx = this.list.findIndex(v => v.value === element.value)
      if (idx !== -1) {
        this.list.splice(idx, 1)
        this.selectChange()
      }
    },
    getWorkingTeam(teams, curItem) {
      const newTeams = teams.filter(v => {
        if (this.workshopId) {
          return v.Workshop_Id === this.workshopId
        }
        return true
      })
      if (!newTeams.length) {
        curItem.Working_Team_Id = ''
        return []
      }
      if (newTeams.every(v => v.Id !== curItem.Working_Team_Id)) {
        curItem.Working_Team_Id = ''
      }
      return newTeams
    },
    async setData(arr, technologyStr) {
      console.log('arr', arr, technologyStr)
      await this.getCraftProcess()
      let technologyArr = []
      if (technologyStr) {
        technologyArr = technologyStr.split('/')
      }
      const workshopId = arr[0].Workshop_Id
      this.workshopId = workshopId

      const partUsedProcess = []

      arr.forEach(v => {
        if (v.Part_Used_Process) {
          const arr = v.Part_Used_Process.split(',')
          partUsedProcess.push(...arr)
        }
      })
      await this.getProcessOption(workshopId)

      this.options = this.options.filter(item => {
        let flag = false
        if (technologyArr.length && technologyArr.includes(item.Code)) {
          flag = true
        }
        if (partUsedProcess.length && partUsedProcess.includes(item.Code)) {
          flag = true
        }
        if (item.Part_Type_Used_Process && item.Part_Type_Used_Process === item.Code) {
          flag = true
        }
        if (!flag) {
          flag = !!item.Is_Enable
        }
        return flag
      })
      // this.defaultOptions = deepClone(this.options)

      this.arr = arr || []
      this.list = []
      let codes = []
      if (this.isCom) {
        const origin = arr.map(v => (v?.Part_Used_Process || '').split(','))
        codes = this.getUnique(origin.flat()).filter(v => !!v)

        if (codes.length) {
          // 零构件
          const checkOption = codes.filter(c => {
            return !!this.options.find(k => k.Code === c)
          })
          console.log(codes, checkOption, this.options.map(v => v.Code))
          // if (checkOption.length < codes.length) {
          //   this.$message({
          //     message: '当前构件生产所属车间内没有该构件所属零件领用工序，请至车间管理内关联相关工序班组',
          //     type: 'warning'
          //   })
          //   return
          // }

          codes.forEach((value, idx) => {
            const obj = {
              value,
              isPart: true,
              key: uuidv4(),
              Working_Team_Id: this.processList[value]?.Working_Team_Id,
              Teams: this.options.find(item => item.Code === value)?.Teams || [],
              date: this.processList[value]?.Finish_Date
            }
            if (obj.Teams.length === 1 && !obj.Working_Team_Id) {
              obj.Working_Team_Id = obj.Teams[0].Id
            }
            this.list.push(obj)
          })
        }
      }
      if (technologyArr.length) {
        console.log('this.options6666', this.options)
        console.log('this.processList', this.processList)
        const techArr = technologyArr.map(v => {
          const obj = {
            key: uuidv4(),
            value: v,
            Working_Team_Id: this.processList[v]?.Working_Team_Id,
            Teams: this.options.find(item => item.Code === v)?.Teams || [],
            date: this.processList[v]?.Finish_Date
          }
          if (obj.Teams.length === 1 && !obj.Working_Team_Id) {
            obj.Working_Team_Id = obj.Teams[0].Id
          }
          return obj
        })
        console.log('techArr', techArr)
        techArr.forEach((element, idx) => {
          if (!codes.includes(element.value)) {
            this.list.push(element)
          }
        })
      }
      if (!this.list.length) {
        this.list.push({
          value: '',
          key: uuidv4(),
          Working_Team_Id: '',
          Teams: [],
          date: ''
        })
        if (this.isNest) {
          const xur = this.options.filter(item => item.Is_Nest)
          if (xur.length === 1) {
            this.list[0].value = xur[0].Code
          }
        }
      }
      const indexMap = technologyArr.reduce((map, item, index) => {
        map[item] = index
        return map
      }, {})

      this.list.sort((item1, item2) => {
        return indexMap[item1.value] - indexMap[item2.value]
      })

      this.selectChange()
    },
    getUnique(arr) {
      return uniqueArr(arr)
    },
    submit() {
      const list = this.list.map(item => item.value).filter(k => !!k)
      const isTrue = this.checkCode(list)
      if (!isTrue) {
        this.$message({
          message: '相邻工序不能相同',
          type: 'warning'
        })
        return
      }
      if (!list.length) {
        this.$message({
          message: '工序不能全为空',
          type: 'warning'
        })
        return
      }

      if (this.isNest) {
        const xur = this.options.filter(item => item.Is_Nest)
        if (xur.length) {
          const hasNest = xur.some(obj => list.includes(obj.Code))
          if (!hasNest) {
            this.$message({
              message: '请至少选择一个套料工序！',
              type: 'warning'
            })
            return
          }
        }
      }

      this.btnLoading = true
      const str = list.join('/')
      this.list.forEach((element, idx) => {
        const item = this.options.find(v => v.Code === element.value)

        let obj = {}
        if (item) {
          obj = {
            Schduling_Id: this.formInline?.Schduling_Code,
            Process_Id: item.Id,
            Process_Code: item.Code,
            Finish_Date: element.date,
            Working_Team_Id: element.Working_Team_Id
          }
        }
        this.$emit('setProcessList', { key: element.value, value: obj })
      })

      this.$emit('sendProcess', { arr: this.arr, str })
      this.btnLoading = false
      this.handleClose()
    },
    handleClose() {
      this.$emit('close')
    },
    checkCode(list) {
      let flag = true
      for (let i = 0; i < list.length; i++) {
        if (i !== list.length - 1 && list[i] === list[i + 1]) {
          flag = false
          break
        }
      }
      return flag
    },
    changeDraggable() {
      this.list.forEach(v => {
        this.$set(v, 'date', '')
      })
      this.initProcessList()
    }
  }
}
</script>

<style scoped>
.btn-x{
  margin-left: 20px;
}
.dialog-footer{
  text-align: right;
  margin-top: 30px;
}

.cs-drag{
  line-height: 32px;
  cursor: move;
}
.cs-option{
  display:flex;
  justify-content: space-between;
  .cs-label{

  }
  .cs-tip{
    color: #409EFF;
  }
}

</style>

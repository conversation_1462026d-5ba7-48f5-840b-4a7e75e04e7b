{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\unitPartRecognitionConfig.vue?vue&type=style&index=0&id=b6515df4&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\unitPartRecognitionConfig.vue", "mtime": 1757474258893}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgpAaW1wb3J0ICJ+QC9zdHlsZXMvbWl4aW4uc2NzcyI7Ci5mb3JtLXdyYXBwZXIgewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICBvdmVyZmxvdzogaGlkZGVuOwogIG1heC1oZWlnaHQ6IDcwdmg7CiAgLmZvcm0teHsKICAgIG92ZXJmbG93OiBhdXRvOwogICAgcGFkZGluZy1yaWdodDogMTZweDsKICAgIEBpbmNsdWRlIHNjcm9sbEJhcjsKICB9CiAgLmJ0bi14IHsKICAgIHBhZGRpbmctdG9wOiAxNnB4OwogICAgdGV4dC1hbGlnbjogcmlnaHQ7CiAgfQoKfQo="}, {"version": 3, "sources": ["unitPartRecognitionConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "unitPartRecognitionConfig.vue", "sourceRoot": "src/views/PRO/process-settings/management/component", "sourcesContent": ["<template>\n  <div class=\"form-wrapper\">\n    <div class=\"form-x\">\n      <el-form ref=\"form\" :model=\"form\" label-width=\"120px\">\n        <el-form-item label=\"是否启用\" prop=\"enable\">\n          <el-radio-group v-model=\"form.enable\">\n            <el-radio :label=\"false\">否</el-radio>\n            <el-radio :label=\"true\">是</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <template v-if=\"form.enable\">\n          <el-form-item label=\"识别类型\" prop=\"identifyAttr\">\n            <el-radio-group v-model=\"form.identifyAttr\">\n              <el-radio :label=\"1\">{{ currentBomName }}名称前缀</el-radio>\n              <el-radio :label=\"2\">{{ currentBomName }}规格前缀</el-radio>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item v-for=\"(element,index) in list\" :key=\"index\" :show-message=\"false\" :label=\"element.Part_Type_Name\" prop=\"mainPart\">\n            <el-input v-model.trim=\"form['item'+index]\" :placeholder=\"`请输入（多个使用'${splitSymbol}'隔开），单个配置不超过10个字符`\" clearable @blur=\"mainBlur\" />\n          </el-form-item>\n        </template>\n      </el-form>\n    </div>\n    <div class=\"btn-x\">\n      <el-button @click=\"$emit('close')\">取 消</el-button>\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\n\nimport { uniqueArr } from '@/utils'\nimport {\n  GetFactoryPartTypeIndentifySetting,\n  SavePartTypeIdentifySetting\n} from '@/api/PRO/partType'\n\nconst SPLITVALUE = '|'\n\nexport default {\n  props: {\n    level: {\n      type: Number,\n      default: 0\n    },\n    bomList: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      form: {\n        enable: false,\n        identifyAttr: 1 // 默认为零件名称前缀\n      },\n      list: [],\n      splitSymbol: SPLITVALUE,\n      btnLoading: false\n    }\n  },\n  computed: {\n    currentBomName() {\n      return this.bomList.find(item => +item.Code === this.level)?.Display_Name\n    }\n  },\n  watch: {\n    level: {\n      handler(newVal) {\n        if (newVal) {\n          this.getTypeList()\n        }\n      },\n      immediate: true\n    }\n  },\n  mounted() {\n\n  },\n  methods: {\n    async getTypeList() {\n      GetFactoryPartTypeIndentifySetting({\n        Part_Grade: this.level\n      }).then(res => {\n        if (res.IsSucceed) {\n          const { Is_Enabled, Setting_List } = res.Data\n          this.form.enable = Is_Enabled\n          this.list = Setting_List.map((v, index) => {\n            this.$set(this.form, 'item' + index, v.Prefixs || '')\n            return v\n          })\n\n          // 获取Setting_List中的Identify_Attr，如果有效（值为1或2）则使用，否则默认为1\n          if (Setting_List.length > 0) {\n            const identifyAttr = Setting_List[0].Identify_Attr\n            this.form.identifyAttr = (identifyAttr === 1 || identifyAttr === 2) ? identifyAttr : 1\n          }\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    handleSubmit() {\n      if (this.form.enable) {\n        const arr = []\n        console.log('this.form', this.form)\n        for (let i = 0; i < this.list.length; i++) {\n          const regex = /^(?!.*\\|\\|)(?!.*\\|$)(?!^\\|)[^|]{1,10}(?:\\|[^|]{1,10})*$/\n          if (!regex.test(this.form[`item${i}`])) {\n            this.$message({\n              message: `${this.list[i].Part_Type_Name}配置不符合要求`,\n              type: 'warning'\n            })\n            return\n          }\n\n          const item = this.form[`item${i}`].split(this.splitSymbol).filter(v => !!v)\n\n          if (item.length === 0) {\n            this.$message({\n              message: `${this.list[i].Part_Type_Name}不能为空`,\n              type: 'warning'\n            })\n            return\n          }\n\n          for (let j = 0; j < item.length; j++) {\n            const d = item[j]\n            if (d.length > 10) {\n              this.$message({\n                message: `${this.list[i].Part_Type_Name}单个配置，不能超过10个字符`,\n                type: 'warning'\n              })\n              return\n            }\n          }\n\n          arr.push(...item)\n        }\n        const uniArr = uniqueArr(arr)\n        if (uniArr.length !== arr.length) {\n          this.$message({\n            message: '配置不能相同',\n            type: 'warning'\n          })\n          return\n        }\n      }\n      this.btnLoading = true\n      SavePartTypeIdentifySetting({\n        Is_Enabled: this.form.enable,\n        Part_Grade: this.level,\n        Setting_List: this.list.map((v, i) => {\n          return {\n            ...v,\n            Prefixs: this.form[`item${i}`],\n            Identify_Attr: this.form.identifyAttr\n          }\n        })\n      }).then(res => {\n        if (res.IsSucceed) {\n          this.$message({\n            message: '操作成功',\n            type: 'success'\n          })\n          this.$emit('close')\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      }).finally(() => {\n        this.btnLoading = false\n      })\n    },\n    mainBlur(e) {\n\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"~@/styles/mixin.scss\";\n.form-wrapper {\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  max-height: 70vh;\n  .form-x{\n    overflow: auto;\n    padding-right: 16px;\n    @include scrollBar;\n  }\n  .btn-x {\n    padding-top: 16px;\n    text-align: right;\n  }\n\n}\n</style>\n"]}]}
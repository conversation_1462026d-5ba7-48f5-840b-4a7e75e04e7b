.cs-z-page-main-content {
  display: flex;
  flex-direction: column;
  background: #fff;
  padding: 16px;
  height: 100%;
  position: relative;
  //box-shadow: 0px 1px 3px rgba(20, 35, 78, 0.08);
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  overflow: auto;
}

.cs-z-shadow {
  //box-shadow: 0px 1px 3px rgba(20, 35, 78, 0.08);
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}

.cs-vxe-table {
  thead {
    th {
      color: #333333 !important;
      background: #EBEEF2;
      border: 1px solid #ffffff;

      .vxe-cell {
        display: flex;
      }

      .vxe-cell--title {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }


    }
  }

  .vxe-footer--row {
    background: rgba(255, 124, 25, 0.12);

    .vxe-footer--column {
      height: 36px !important;
      line-height: 36px !important;
      padding: 0 !important;

    }
  }

  .vxe-header--column {
    text-align: center !important;
    height: 36px !important;


    .c--tooltip.vxe-cell {
      display: flex;
      justify-content: center;
    }

    .vxe-cell--sort {
      i {
        font-size: 12px !important;
      }

      .vxe-sort--desc-btn {
        top: 8px;
      }

      .vxe-sort--asc-btn {
        top: 1px;
      }
    }


  }

  .row--stripe {
    background-color: #F5F7FA !important;
  }

  .vxe-body--row.row--hover.row--stripe {
    background-color: #E2EDF6 !important;
  }

  .vxe-body--row.row--hover,
  .vxe-table--render-default .vxe-body--row.row--hover.row--stripe {
    background-color: #E2EDF6 !important;
  }


  .vxe-body--column:not(.col--checkbox) .vxe-cell {
    padding-left: 12px !important;
    padding-right: 12px !important;
  }

  .vxe-body--column {
    background-image: linear-gradient(#ffffff, #ffffff) !important;
    background-size: 2px 100%, 100% 0 !important;
    height: 36px !important;

    .vxe-cell--label {
      color: #333333;
      font-family: Microsoft YaHei, Microsoft YaHei;
    }

    .vxe-cell {
      color: #333333;
      font-family: Microsoft YaHei, Microsoft YaHei;

      div {
        color: #333333;
        font-family: Microsoft YaHei, Microsoft YaHei;
      }
    }
  }

  .col--tree-node .vxe-cell div{
    text-align: left;
  }


  .vxe-body--column.col--last .vxe-cell button,
  .vxe-body--column.col--last .vxe-cell div button {
    font-size: 14px !important;
  }

  .vxe-table--body-wrapper,
  .vxe-table--footer-wrapper.body--wrapper {
    &::-webkit-scrollbar {
      width: 10px;
      height: 10px;
      background-color: #E5E5E5;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #BBBBBB;
      border-radius: 5px;
    }
  }

  .vxe-table--empty-content {
    height: 100%;
  }

  .vxe-body--column.col--checkbox {
    .vxe-cell {
      // width: 43px !important;
      padding: 0;
    }
  }

  .vxe-header--column.col--checkbox {
    .vxe-cell {
      //width: 43px !important;
      padding: 0;
    }
  }

  .col--checkbox {
    .vxe-cell {
      text-align: center;
    }
  }

  .vxe-body--column:not(.col--ellipsis) {
    padding: 0 0 !important;
  }
}

.cs-plm-dy-table {

  /* 或使用多重选择器增加特异性 */
  .el-table__row.hover-row {
    background-color: #E2EDF6 !important;

    td {
      background-color: #E2EDF6 !important;
    }
  }

  .el-table__row.hover-row.el-table__row.el-table__row--striped {
    background-color: #E2EDF6 !important;

    td {
      background-color: #E2EDF6 !important;
    }
  }

  .el-table__row.el-table__row--striped {
    background-color: #f5f7fa !important;

    td {
      background-color: #f5f7fa !important;
    }
  }

  .el-table th.is-leaf,
  .el-table td {
    border-bottom: 1px solid #FFF !important;
  }

  .el-table th.is-leaf {
    color: #333333;
    font-weight: 400;
  }

  .el-table td,
  .el-table tr th {
    border-right: 2px solid #FFF !important;
  }

  .el-table th {
    background-color: #e7eaee;
  }

  td {
    color: #333333;

    .cell {
      padding-left: 12px !important;
      padding-right: 12px !important;
    }
  }

  td.is-left {
    div {
      text-align: left;
    }
  }


  .el-table__body-wrapper {
    &::-webkit-scrollbar {
      width: 10px;
      height: 10px;
      background-color: #E5E5E5;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #BBBBBB;
      border-radius: 5px;
    }
  }

  .el-table__empty-block {
    height: 100%;
    position: sticky;
    width: 100% !important;
    left: 0;
  }
}

.cs-custom-table {
  tr.el-table__row--striped {
    background-color: #f2f7fb;

    >td {
      background-color: #f2f7fb !important;
    }
  }

  th.is-leaf,
  td {
    border-bottom: 1px solid #FFF !important;
  }

  th.is-leaf {
    color: rgba(34, 40, 52, 0.85);
  }

  th.el-table-column--selection {
    text-align: center;

    .cell {
      .el-checkbox {
        width: 14px;
        height: 23px;
      }
    }
  }

  td.el-table-column--selection {
    text-align: center;
  }

  td,
  tr th {
    border-right: 2px solid #FFF !important;
    text-align: left;
  }

  th {
    background-color: #EBEEF2 !important;
    line-height: normal !important;

    .cell {
      text-align: center;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      white-space: nowrap;
      overflow: hidden;
      color: #333333;
      text-overflow: ellipsis;
      padding-left: 12px;
      padding-right: 12px;
      line-height: 25px !important;
    }
  }

  tr {
    td {
      color: #333333;

      .cell {
        padding-left: 12px;
        padding-right: 12px;

        button {
          font-size: 14px !important;
        }
      }
    }
  }

  .el-table__body-wrapper {
    &::-webkit-scrollbar {
      width: 10px;
      height: 10px;
      background-color: #E5E5E5;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #BBBBBB;
      border-radius: 5px;
    }

    .el-table__empty-block {
      height: 100%;
      position: sticky;
      width: 100% !important;
      left: 0;
    }
  }
}

.el-table--small {
  font-size: 14px !important;

  th {
    padding: 5px 0 5px 0 !important;
  }

  td {
    padding: 2px 0 1px 0 !important;
  }
}

.cs-toolBar-tbConfig {
  .vxe-button--icon.vxe-icon-custom-column {
    display: none;
  }

  .vxe-button.type--button.is--circle {
    width: 97px;
    z-index: 0;
    border-radius: 4px;
  }

  .el-form-item {
    margin-bottom: 0;
  }

  .vxe-custom--wrapper.is--active>.vxe-button {
    border-radius: unset !important;
  }
}

.cs-vxe-tree-down {
  width: 16px;
  height: 16px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cdefs%3E%3Cstyle%3E.b%7Bfill:%23333%7D%3C/style%3E%3C/defs%3E%3Cpath class='b' d='M11.698 6.52l-3.7 3.7-3.7-3.702.74-.74 2.96 2.963 2.963-2.963z'/%3E%3Cpath class='b' d='M8 1a7 7 0 1 0 7 7 7.008 7.008 0 0 0-7-7zm0 12.923A5.923 5.923 0 1 1 13.923 8 5.93 5.93 0 0 1 8 13.923z'/%3E%3C/svg%3E");
  background-size: cover;
}

.cs-vxe-tree-up {
  width: 16px;
  height: 16px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cdefs%3E%3Cstyle%3E.b%7Bfill:%23333%7D%3C/style%3E%3C/defs%3E%3Cpath class='b' d='M11.699 9.478l-3.7-3.7-3.7 3.7.74.74 2.96-2.958 2.963 2.96z'/%3E%3Cpath class='b' d='M8 15a7 7 0 1 1 7-7 7.008 7.008 0 0 1-7 7zM8 2.077A5.923 5.923 0 1 0 13.923 8 5.93 5.93 0 0 0 8 2.077z'/%3E%3C/svg%3E");
  background-size: cover;
}

.el-form.el-form--inline {
  .el-form-item {
    margin-bottom: 10px;
  }
}

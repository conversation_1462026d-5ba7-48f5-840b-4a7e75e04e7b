<template>
  <div class="add-project-container">
    <div class="instruction">
      请选择项目,添加所选项目的所有生产路径
    </div>
    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item v-if="!projectList.length" label="项目名称：">
          <div>暂无可同步的项目</div>
        </el-form-item>
        <el-form-item v-else label="项目名称：">
          <el-select
            v-model="searchForm.ProjectCode"
            clearable
            filterable
            placeholder="请选择"
            style="width: 200px"
          >
            <el-option v-for="item in projectList" :key="item.Sys_Project_Id" :label="item.Short_Name" :value="item.Sys_Project_Id" />
          </el-select>
        </el-form-item>

      </el-form>
    </div>

    <!-- 提示信息 -->

    <!-- 底部按钮 -->
    <div class="footer-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button v-if="projectList.length" type="primary" :loading="loading" :disabled="searchForm.ProjectCode === ''" @click="handleConfirm">
        确定
      </el-button>
    </div>
  </div>
</template>

<script>
import { GetProjectPageList } from '@/api/PRO/project'
import { SyncProjectTechnologyFromProject } from '@/api/PRO/technology-lib'
export default {
  name: 'AddProject',
  props: {
    sysProjectId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      projectList: [],
      searchForm: {
        ProjectCode: ''
      }

    }
  },
  created() {
    this.fetchProjectList()
  },
  methods: {

    // 获取项目列表
    async fetchProjectList() {
      try {
        const params = {
        }
        const res = await GetProjectPageList(params)
        if (res.IsSucceed) {
          const table = res?.Data?.Data || []
          this.projectList = table.filter(item => item.Sys_Project_Id !== this.sysProjectId)
        } else {
          this.$message.error(res.Message || '获取项目列表失败')
        }
      } catch (error) {
        console.error('获取项目列表失败:', error)
        this.$message.error('获取项目列表失败')
      } finally {
      }
    },
    handleCancel() {
      this.$emit('close')
    },
    // 确认选择
    handleConfirm() {
      if (this.searchForm.ProjectCode === '') {
        this.$message.warning('请至少选择一个项目')
        return
      }
      this.loading = true

      SyncProjectTechnologyFromProject({
        From_Sys_Project_Id: this.searchForm.ProjectCode,
        To_Sys_Project_Id: this.sysProjectId
      }).then(res => {
        if (res.IsSucceed) {
          this.$emit('refresh')
          this.$emit('close')
          this.$message.success('同步成功！')
        } else {
          this.$message.error(res.Message || '同步失败')
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
.add-project-container {

  display: flex;
  flex-direction: column;

  .search-section {
    background: #fff;
    border-radius: 4px;
  }

  .instruction {
    background: #f0f9ff;
    border: 1px solid #b3d8ff;
    color: #1890ff;
    padding: 12px 16px;
    border-radius: 4px;
    margin-bottom: 16px;
    font-weight: 500;
  }

  .table-section {
    flex: 1;
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
  }

  .footer-actions {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
    padding: 0px 8px 0 0;
    background: #fff;
  }
}
</style>

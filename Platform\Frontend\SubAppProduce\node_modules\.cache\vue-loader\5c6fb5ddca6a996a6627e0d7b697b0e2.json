{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new\\components\\addDraft.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new\\components\\addDraft.vue", "mtime": 1757468128013}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRHcmlkQnlDb2RlIH0gZnJvbSAnQC9hcGkvc3lzJw0KaW1wb3J0IHsgR2V0Q2FuU2NoZHVsaW5nQ29tcHMsIEdldFN0b3BMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL3Byb2R1Y3Rpb24tdGFzaycNCmltcG9ydCB7IEdldENhblNjaGR1bGluZ1BhcnRzLCBHZXRQYXJ0TGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9wcm9kdWN0aW9uLXBhcnQnDQppbXBvcnQgeyB2NCBhcyB1dWlkdjQgfSBmcm9tICd1dWlkJw0KaW1wb3J0IHsgZGVib3VuY2UsIGRlZXBDbG9uZSB9IGZyb20gJ0AvdXRpbHMnDQppbXBvcnQgeyB0YWJsZVBhZ2VTaXplIH0gZnJvbSAnQC92aWV3cy9QUk8vc2V0dGluZycNCmltcG9ydCB7IEdldENvbXBUeXBlVHJlZSB9IGZyb20gJ0AvYXBpL1BSTy9wcm9mZXNzaW9uYWxUeXBlJw0KaW1wb3J0IHsgR2V0UGFydFR5cGVMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL3BhcnRUeXBlJw0KaW1wb3J0IHsgbWFwR2V0dGVycyB9IGZyb20gJ3Z1ZXgnDQppbXBvcnQgeyBHZXRJbnN0YWxsVW5pdElkTmFtZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vcHJvamVjdCcNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBwcm9wczogew0KICAgIGNvbU5hbWU6IHsNCiAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJycNCiAgICB9LA0KICAgIGxldmVsQ29kZTogew0KICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnJw0KICAgIH0sDQogICAgc2NoZWR1bGVJZDogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJycNCiAgICB9LA0KICAgIHBhZ2VUeXBlOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnY29tJw0KICAgIH0sDQogICAgc2hvd0RpYWxvZzogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfSwNCiAgICBhcmVhSWQ6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICcnDQogICAgfSwNCiAgICBpbnN0YWxsSWQ6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICcnDQogICAgfSwNCiAgICBjdXJyZW50SWRzOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnJw0KICAgIH0sDQogICAgaXNQYXJ0UHJlcGFyZTogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBwYWdlSW5mbzogew0KICAgICAgICBwYWdlOiAxLA0KICAgICAgICBwYWdlU2l6ZTogNTAwLA0KICAgICAgICBwYWdlU2l6ZXM6IHRhYmxlUGFnZVNpemUsDQogICAgICAgIHRvdGFsOiAwDQogICAgICB9LA0KICAgICAgZm9ybTogew0KICAgICAgICBDb21wX0NvZGU6ICcnLA0KICAgICAgICBDb21wX0NvZGVCbHVyOiAnJywNCiAgICAgICAgUGFydF9Db2RlQmx1cjogJycsDQogICAgICAgIFBhcnRfQ29kZTogJycsDQogICAgICAgIFR5cGVfTmFtZTogJycsDQogICAgICAgIEluc3RhbGxVbml0X0lkOiAnJywNCiAgICAgICAgU3BlYzogJycsDQogICAgICAgIFR5cGU6ICcnDQogICAgICB9LA0KICAgICAgc2VhcmNoQ29udGVudDogJycsDQogICAgICBjdXJTZWFyY2g6IDEsDQogICAgICBpc093bmVyTnVsbDogdHJ1ZSwNCiAgICAgIHRiTG9hZGluZzogZmFsc2UsDQogICAgICBhZGRMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHNhdmVMb2FkaW5nOiBmYWxzZSwNCiAgICAgIGNvbHVtbnM6IFtdLA0KICAgICAgZlRhYmxlOiBbXSwNCiAgICAgIHRiQ29uZmlnOiB7fSwNCiAgICAgIFRvdGFsQ291bnQ6IDAsDQogICAgICBQYWdlOiAwLA0KICAgICAgbXVsdGlwbGVTZWxlY3Rpb246IFtdLA0KICAgICAgaW5zdGFsbFVuaXRJZExpc3Q6IFtdLA0KICAgICAgdG90YWxTZWxlY3Rpb246IFtdLA0KICAgICAgc2VhcmNoOiAoKSA9PiAoe30pLA0KICAgICAgdHJlZVNlbGVjdFBhcmFtczogew0KICAgICAgICBwbGFjZWhvbGRlcjogJ+ivt+mAieaLqScsDQogICAgICAgIGNsZWFyYWJsZTogdHJ1ZQ0KICAgICAgfSwNCiAgICAgIE9iamVjdFR5cGVMaXN0OiB7DQogICAgICAgIC8vIOaehOS7tuexu+Weiw0KICAgICAgICAnY2hlY2stc3RyaWN0bHknOiB0cnVlLA0KICAgICAgICAnZGVmYXVsdC1leHBhbmQtYWxsJzogdHJ1ZSwNCiAgICAgICAgY2xpY2tQYXJlbnQ6IHRydWUsDQogICAgICAgIGRhdGE6IFtdLA0KICAgICAgICBwcm9wczogew0KICAgICAgICAgIGNoaWxkcmVuOiAnQ2hpbGRyZW4nLA0KICAgICAgICAgIGxhYmVsOiAnTGFiZWwnLA0KICAgICAgICAgIHZhbHVlOiAnRGF0YScNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIHR5cGVPcHRpb246IFtdDQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIGlzQ29tKCkgew0KICAgICAgcmV0dXJuIHRoaXMucGFnZVR5cGUgPT09ICdjb20nDQogICAgfSwNCiAgICAuLi5tYXBHZXR0ZXJzKCd0ZW5hbnQnLCBbJ2lzVmVyc2lvbkZvdXInXSkNCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICBzaG93RGlhbG9nKG5ld1ZhbHVlKSB7DQogICAgICBuZXdWYWx1ZSAmJiAodGhpcy5zYXZlTG9hZGluZyA9IGZhbHNlKQ0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmdldENvbmZpZygpDQogICAgaWYgKHRoaXMuaXNDb20pIHsNCiAgICAgIHRoaXMuZ2V0T2JqZWN0VHlwZUxpc3QoKQ0KICAgIH0gZWxzZSB7DQogICAgICB0aGlzLmdldFR5cGUoKQ0KICAgIH0NCiAgICB0aGlzLnNlYXJjaCA9IGRlYm91bmNlKHRoaXMuZmV0Y2hEYXRhLCA4MDAsIHRydWUpDQogICAgdGhpcy5nZXRJbnN0YWxsVW5pdElkTmFtZUxpc3QoKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgYXN5bmMgZ2V0Q29uZmlnKCkgew0KICAgICAgbGV0IGNvZGUgPSAnJw0KICAgICAgY29kZSA9IHRoaXMuaXNDb20NCiAgICAgICAgPyAnUFJPQ29tRHJhZnRFZGl0VGJDb25maWcnDQogICAgICAgIDogJ1BST1BhcnREcmFmdEVkaXRUYkNvbmZpZ19uZXcnDQogICAgICBhd2FpdCB0aGlzLmdldFRhYmxlQ29uZmlnKGNvZGUpDQogICAgICB0aGlzLmZldGNoRGF0YSgpDQogICAgfSwNCiAgICBmaWx0ZXJEYXRhKHBhZ2UpIHsNCiAgICAgIGlmICh0aGlzLmN1clNlYXJjaCA9PT0gMSkgew0KICAgICAgICB0aGlzLmZvcm0uQ29tcF9Db2RlID0gdGhpcy5zZWFyY2hDb250ZW50DQogICAgICAgIHRoaXMuZm9ybS5Db21wX0NvZGVCbHVyID0gJycNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmN1clNlYXJjaCA9PT0gMCkgew0KICAgICAgICB0aGlzLmZvcm0uQ29tcF9Db2RlQmx1ciA9IHRoaXMuc2VhcmNoQ29udGVudA0KICAgICAgICB0aGlzLmZvcm0uQ29tcF9Db2RlID0gJycNCiAgICAgIH0NCg0KICAgICAgY29uc3QgZiA9IFtdDQogICAgICBmb3IgKGNvbnN0IGZvcm1LZXkgaW4gdGhpcy5mb3JtKSB7DQogICAgICAgIGlmICh0aGlzLmZvcm1bZm9ybUtleV0gfHwgdGhpcy5mb3JtW2Zvcm1LZXldID09PSBmYWxzZSkgew0KICAgICAgICAgIGYucHVzaChmb3JtS2V5KQ0KICAgICAgICB9DQogICAgICB9DQogICAgICBpZiAoIWYubGVuZ3RoKSB7DQogICAgICAgIHRoaXMuc2V0UGFnZSgpDQogICAgICAgICFwYWdlICYmICh0aGlzLnBhZ2VJbmZvLnBhZ2UgPSAxKQ0KICAgICAgICB0aGlzLnBhZ2VJbmZvLnRvdGFsID0gdGhpcy50YkRhdGEubGVuZ3RoDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgY29uc3QgdGVtVGJEYXRhID0gdGhpcy50YkRhdGEuZmlsdGVyKHYgPT4gew0KICAgICAgICB2LmNoZWNrZWQgPSBmYWxzZQ0KDQogICAgICAgIGNvbnN0IHNwbGl0QW5kQ2xlYW4gPSAoaW5wdXQpID0+IGlucHV0LnRyaW0oKS5yZXBsYWNlKC9ccysvZywgJyAnKS5zcGxpdCgnICcpDQoNCiAgICAgICAgaWYgKHRoaXMuZm9ybS5Db21wX0NvZGUudHJpbSgpKSB7DQogICAgICAgICAgY29uc3QgY29tcENvZGVBcnJheSA9IHNwbGl0QW5kQ2xlYW4odGhpcy5mb3JtLkNvbXBfQ29kZSkNCiAgICAgICAgICBpZiAoIWNvbXBDb2RlQXJyYXkuaW5jbHVkZXModlsnQ29tcF9Db2RlJ10pKSB7DQogICAgICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICBpZiAodGhpcy5mb3JtLkNvbXBfQ29kZUJsdXIudHJpbSgpKSB7DQogICAgICAgICAgY29uc3QgY29tcENvZGVCbHVyQXJyYXkgPSBzcGxpdEFuZENsZWFuKHRoaXMuZm9ybS5Db21wX0NvZGVCbHVyKQ0KICAgICAgICAgIGlmICghY29tcENvZGVCbHVyQXJyYXkuc29tZShjb2RlID0+IHZbJ0NvbXBfQ29kZSddLmluY2x1ZGVzKGNvZGUpKSkgew0KICAgICAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgaWYgKHRoaXMuZm9ybS5UeXBlICYmIHYuVHlwZSAhPT0gdGhpcy5mb3JtLlR5cGUpIHsNCiAgICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgICAgfQ0KDQogICAgICAgIGlmICh0aGlzLmZvcm0uUGFydF9Db2RlQmx1ci50cmltKCkpIHsNCiAgICAgICAgICBjb25zdCBwYXJ0Q29kZUJsdXJBcnJheSA9IHNwbGl0QW5kQ2xlYW4odGhpcy5mb3JtLlBhcnRfQ29kZUJsdXIpDQogICAgICAgICAgaWYgKCFwYXJ0Q29kZUJsdXJBcnJheS5zb21lKGNvZGUgPT4gdlsnUGFydF9Db2RlJ10uaW5jbHVkZXMoY29kZSkpKSB7DQogICAgICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICBpZiAodGhpcy5mb3JtLlBhcnRfQ29kZS50cmltKCkpIHsNCiAgICAgICAgICBjb25zdCBwYXJ0Q29kZUFycmF5ID0gc3BsaXRBbmRDbGVhbih0aGlzLmZvcm0uUGFydF9Db2RlKQ0KICAgICAgICAgIGlmICghcGFydENvZGVBcnJheS5pbmNsdWRlcyh2WydQYXJ0X0NvZGUnXSkpIHsNCiAgICAgICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIGlmICh0aGlzLmlzVmVyc2lvbkZvdXIgJiYgdGhpcy5mb3JtLkluc3RhbGxVbml0X0lkLmxlbmd0aCAmJiAhdGhpcy5mb3JtLkluc3RhbGxVbml0X0lkLmluY2x1ZGVzKHYuSW5zdGFsbFVuaXRfSWQpKSB7DQogICAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICAgIH0NCg0KICAgICAgICBpZiAodGhpcy5mb3JtLlR5cGVfTmFtZSAhPT0gJycgJiYgdi5UeXBlX05hbWUgIT09IHRoaXMuZm9ybS5UeXBlX05hbWUpIHsNCiAgICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgICAgfQ0KDQogICAgICAgIGlmICh0aGlzLmZvcm0uU3BlYy50cmltKCkgIT09ICcnKSB7DQogICAgICAgICAgY29uc3Qgc3BlY0FycmF5ID0gc3BsaXRBbmRDbGVhbih0aGlzLmZvcm0uU3BlYykNCiAgICAgICAgICBpZiAoIXNwZWNBcnJheS5zb21lKHNwZWMgPT4gdi5TcGVjLmluY2x1ZGVzKHNwZWMpKSkgew0KICAgICAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgcmV0dXJuIHRydWUNCiAgICAgIH0pDQoNCiAgICAgICFwYWdlICYmICh0aGlzLnBhZ2VJbmZvLnBhZ2UgPSAxKQ0KICAgICAgdGhpcy5wYWdlSW5mby50b3RhbCA9IHRlbVRiRGF0YS5sZW5ndGgNCiAgICAgIHRoaXMuc2V0UGFnZSh0ZW1UYkRhdGEpDQogICAgfSwNCiAgICBoYW5kbGVTZWFyY2goKSB7DQogICAgICB0aGlzLnRvdGFsU2VsZWN0aW9uID0gW10NCiAgICAgIHRoaXMuY2xlYXJTZWxlY3QoKQ0KICAgICAgaWYgKHRoaXMudGJEYXRhPy5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy50YkRhdGEuZm9yRWFjaChpdGVtID0+IGl0ZW0uY2hlY2tlZCA9IGZhbHNlKQ0KICAgICAgICB0aGlzLmZpbHRlckRhdGEoKQ0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlUmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0uVHlwZV9OYW1lID0gJycNCiAgICAgIHRoaXMuZm9ybS5Db21wX0NvZGUgPSAnJw0KICAgICAgdGhpcy5mb3JtLkNvbXBfQ29kZUJsdXIgPSAnJw0KICAgICAgdGhpcy5mb3JtLlR5cGUgPSAnJw0KICAgICAgdGhpcy5mb3JtLlNwZWMgPSAnJw0KICAgICAgdGhpcy5zZWFyY2hDb250ZW50ID0gJycNCiAgICAgIHRoaXMuaGFuZGxlU2VhcmNoKCkNCiAgICB9LA0KICAgIGhhbmRsZVNlbGVjdChkYXRhKSB7DQogICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gZGF0YQ0KICAgIH0sDQogICAgdGJTZWxlY3RDaGFuZ2UoYXJyYXkpIHsNCiAgICAgIHRoaXMudG90YWxTZWxlY3Rpb24gPSB0aGlzLnRiRGF0YS5maWx0ZXIodiA9PiB2LmNoZWNrZWQpDQogICAgfSwNCiAgICBjbGVhclNlbGVjdCgpIHsNCiAgICAgIHRoaXMuJHJlZnMueFRhYmxlMS5jbGVhckNoZWNrYm94Um93KCkNCiAgICAgIHRoaXMudG90YWxTZWxlY3Rpb24gPSBbXQ0KICAgIH0sDQogICAgYXN5bmMgZmV0Y2hEYXRhKCkgew0KICAgICAgdGhpcy50YkxvYWRpbmcgPSB0cnVlDQogICAgICBhd2FpdCB0aGlzLmdldENvbVRiRGF0YSgpDQogICAgICB0aGlzLmluaXRUYkRhdGEoKQ0KICAgICAgdGhpcy5maWx0ZXJEYXRhKCkNCiAgICAgIHRoaXMudGJMb2FkaW5nID0gZmFsc2UNCiAgICB9LA0KICAgIHNldFBhZ2VEYXRhKCkgew0KICAgICAgaWYgKHRoaXMudGJEYXRhPy5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy5wYWdlSW5mby5wYWdlID0gMQ0KICAgICAgICB0aGlzLnRiRGF0YSA9IHRoaXMudGJEYXRhLmZpbHRlcih2ID0+IHYuQ2FuX1NjaGR1bGluZ19Db3VudCA+IDApDQogICAgICAgIHRoaXMuZmlsdGVyRGF0YSgpDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVTYXZlKHR5cGUgPSAyKSB7DQogICAgICBpZiAodHlwZSA9PT0gMSkgew0KICAgICAgICB0aGlzLmFkZExvYWRpbmcgPSB0cnVlDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnNhdmVMb2FkaW5nID0gdHJ1ZQ0KICAgICAgfQ0KICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgIHRoaXMudG90YWxTZWxlY3Rpb24uZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgIGNvbnN0IGludENvdW50ID0gcGFyc2VJbnQoaXRlbS5jb3VudCkNCiAgICAgICAgICBpdGVtLlNjaGR1bGVkX0NvdW50ICs9IGludENvdW50DQogICAgICAgICAgaXRlbS5DYW5fU2NoZHVsaW5nX0NvdW50IC09IGludENvdW50DQogICAgICAgICAgaXRlbS5DYW5fU2NoZHVsaW5nX1dlaWdodCA9IGl0ZW0uQ2FuX1NjaGR1bGluZ19Db3VudCAqIGl0ZW0uV2VpZ2h0DQogICAgICAgICAgaXRlbS5tYXhDb3VudCA9IGl0ZW0uQ2FuX1NjaGR1bGluZ19Db3VudA0KICAgICAgICAgIGl0ZW0uY2hvb3NlQ291bnQgPSBpbnRDb3VudA0KICAgICAgICAgIGl0ZW0uY291bnQgPSBpdGVtLkNhbl9TY2hkdWxpbmdfQ291bnQNCiAgICAgICAgICBpdGVtLmNoZWNrZWQgPSBmYWxzZQ0KICAgICAgICB9KQ0KICAgICAgICBjb25zdCBjcCA9IGRlZXBDbG9uZSh0aGlzLnRvdGFsU2VsZWN0aW9uKQ0KDQogICAgICAgIHRoaXMuJGVtaXQoJ3NlbmRTZWxlY3RMaXN0JywgY3ApDQogICAgICAgIHRoaXMuYWRkTG9hZGluZyA9IGZhbHNlDQogICAgICAgIHRoaXMuY2xlYXJTZWxlY3QoKQ0KICAgICAgICAvLyB0aGlzLnNldFBhZ2UoKQ0KICAgICAgICB0aGlzLnNldFBhZ2VEYXRhKCkNCiAgICAgICAgY29uc29sZS5sb2coJ3R5cGUnLCB0eXBlKQ0KICAgICAgICBpZiAodHlwZSA9PT0gMikgew0KICAgICAgICAgIHRoaXMuJGVtaXQoJ2Nsb3NlJykNCiAgICAgICAgfQ0KICAgICAgfSwgMCkNCiAgICB9LA0KICAgIGluaXRUYkRhdGEoKSB7DQogICAgICAvLyDorr7nva7mlofmnKzmoYbpgInmi6nnmoTmjpLkuqfmlbDph48s6K6+572u6Ieq5a6a5LmJ5ZSv5LiA56CBDQogICAgICBjb25zdCBvYmpLZXkgPSB7fQ0KICAgICAgaWYgKCF0aGlzLnRiRGF0YT8ubGVuZ3RoKSB7DQogICAgICAgIHRoaXMudGJEYXRhID0gW10NCiAgICAgICAgdGhpcy5iYWNrZW5kVGIgPSBbXQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIHRoaXMudGJEYXRhLmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgdGhpcy4kc2V0KGl0ZW0sICdjb3VudCcsIGl0ZW0uQ2FuX1NjaGR1bGluZ19Db3VudCkNCiAgICAgICAgdGhpcy4kc2V0KGl0ZW0sICdtYXhDb3VudCcsIGl0ZW0uQ2FuX1NjaGR1bGluZ19Db3VudCkNCiAgICAgICAgaXRlbS51dWlkID0gdXVpZHY0KCkNCiAgICAgICAgb2JqS2V5W2l0ZW0uVHlwZV0gPSB0cnVlDQogICAgICB9KQ0KICAgICAgdGhpcy5iYWNrZW5kVGIgPSBkZWVwQ2xvbmUodGhpcy50YkRhdGEpDQogICAgfSwNCiAgICBhc3luYyBnZXRDb21UYkRhdGEoKSB7DQogICAgICAvLyBjb25zdCB7IGluc3RhbGwsIGFyZWFJZCB9ID0gdGhpcy4kcm91dGUucXVlcnkNCiAgICAgIGNvbnN0IHsgQ29tcF9Db2RlcywgLi4ub2JqIH0gPSB0aGlzLmZvcm0NCiAgICAgIGxldCBjb2RlcyA9IFtdDQogICAgICBpZiAoT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZy5jYWxsKENvbXBfQ29kZXMpID09PSAnW29iamVjdCBTdHJpbmddJykgew0KICAgICAgICBjb2RlcyA9IENvbXBfQ29kZXMgJiYgQ29tcF9Db2Rlcy5zcGxpdCgnICcpLmZpbHRlcih2ID0+ICEhdikNCiAgICAgIH0NCiAgICAgIHRoaXMudGJEYXRhID0gW10NCiAgICAgIGF3YWl0IEdldENhblNjaGR1bGluZ0NvbXBzKHsNCiAgICAgICAgSWRzOiB0aGlzLmN1cnJlbnRJZHMsDQogICAgICAgIC4uLm9iaiwNCiAgICAgICAgU2NoZHVsaW5nX1BsYW5fSWQ6IHRoaXMuc2NoZWR1bGVJZCwNCiAgICAgICAgQ29tcF9Db2RlczogY29kZXMsDQogICAgICAgIEJvbV9MZXZlbDogdGhpcy5sZXZlbENvZGUsDQogICAgICAgIEluc3RhbGxVbml0X0lkOiB0aGlzLmluc3RhbGxJZCwNCiAgICAgICAgQXJlYV9JZDogdGhpcy5hcmVhSWQNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIGNvbnN0IF90YkRhdGEgPSByZXMuRGF0YSB8fCBbXQ0KICAgICAgICAgIHRoaXMucGFnZUluZm8udG90YWwgPSBfdGJEYXRhPy5sZW5ndGggfHwgMA0KICAgICAgICAgIHRoaXMudGJEYXRhID0gX3RiRGF0YS5tYXAoKHYsIGlkeCkgPT4gew0KICAgICAgICAgICAgLy8g5bey5o6S5Lqn6LWL5YC8DQogICAgICAgICAgICB2Lm9yaWdpbmFsUGF0aCA9IHYuU2NoZWR1bGVkX1RlY2hub2xvZ3lfUGF0aCA/IHYuU2NoZWR1bGVkX1RlY2hub2xvZ3lfUGF0aCA6ICcnDQogICAgICAgICAgICB2LldvcmtzaG9wX0lkID0gdi5TY2hlZHVsZWRfV29ya3Nob3BfSWQNCiAgICAgICAgICAgIHYuV29ya3Nob3BfTmFtZSA9IHYuU2NoZWR1bGVkX1dvcmtzaG9wX05hbWUNCiAgICAgICAgICAgIHYuVGVjaG5vbG9neV9QYXRoID0gdi5TY2hlZHVsZWRfVGVjaG5vbG9neV9QYXRoIHx8IHYuVGVjaG5vbG9neV9QYXRoDQogICAgICAgICAgICAvLyBpZiAodi5vcmlnaW5hbFBhdGgpIHsNCiAgICAgICAgICAgIC8vIHYuaXNEaXNhYmxlZCA9IHRydWUNCiAgICAgICAgICAgIC8vIH0NCiAgICAgICAgICAgIHYuY2hlY2tlZCA9IGZhbHNlDQogICAgICAgICAgICB2LmluaXRSb3dJbmRleCA9IGlkeA0KICAgICAgICAgICAgLy8gdi50ZWNobm9sb2d5UGF0aERpc2FibGVkID0gISF2LlRlY2hub2xvZ3lfUGF0aA0KICAgICAgICAgICAgcmV0dXJuIHYNCiAgICAgICAgICB9KQ0KICAgICAgICAgIHRoaXMuc2V0UGFnZSgpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgY29uc3Qgc3VibWl0T2JqID0gdGhpcy50YkRhdGEubWFwKGl0ZW0gPT4gew0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIElkOiBpdGVtLkNvbXBfSW1wb3J0X0RldGFpbF9JZCwNCiAgICAgICAgICBUeXBlOiAyDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICBhd2FpdCBHZXRTdG9wTGlzdChzdWJtaXRPYmopLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICBjb25zdCBzdG9wTWFwID0ge30NCiAgICAgICAgICByZXMuRGF0YS5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgICAgc3RvcE1hcFtpdGVtLklkXSA9ICEhaXRlbS5Jc19TdG9wDQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLnRiRGF0YS5mb3JFYWNoKHJvdyA9PiB7DQogICAgICAgICAgICBpZiAoc3RvcE1hcC5oYXNPd25Qcm9wZXJ0eShyb3cuQ29tcF9JbXBvcnRfRGV0YWlsX0lkKSkgew0KICAgICAgICAgICAgICB0aGlzLiRzZXQocm93LCAnc3RvcEZsYWcnLCBzdG9wTWFwW3Jvdy5Db21wX0ltcG9ydF9EZXRhaWxfSWRdKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBjaGVja0NoZWNrYm94TWV0aG9kKHsgcm93IH0pIHsNCiAgICAgIHJldHVybiAhcm93LnN0b3BGbGFnDQogICAgfSwNCiAgICAvKioNCiAgICAgKiDliIbpobUNCiAgICAgKi8NCiAgICBoYW5kbGVQYWdlQ2hhbmdlKHsgY3VycmVudFBhZ2UsIHBhZ2VTaXplIH0pIHsNCiAgICAgIGlmICh0aGlzLnRiTG9hZGluZykgcmV0dXJuDQogICAgICB0aGlzLnBhZ2VJbmZvLnBhZ2UgPSBjdXJyZW50UGFnZQ0KICAgICAgdGhpcy5wYWdlSW5mby5wYWdlU2l6ZSA9IHBhZ2VTaXplDQogICAgICB0aGlzLnNldFBhZ2UoKQ0KICAgICAgdGhpcy5maWx0ZXJEYXRhKGN1cnJlbnRQYWdlKQ0KICAgIH0sDQoNCiAgICBzZXRQYWdlKHRiID0gdGhpcy50YkRhdGEpIHsNCiAgICAgIHRoaXMuZlRhYmxlID0gdGIuc2xpY2UoKHRoaXMucGFnZUluZm8ucGFnZSAtIDEpICogdGhpcy5wYWdlSW5mby5wYWdlU2l6ZSwgdGhpcy5wYWdlSW5mby5wYWdlICogdGhpcy5wYWdlSW5mby5wYWdlU2l6ZSkNCiAgICB9LA0KDQogICAgZ2V0UGFydFVzZWRQcm9jZXNzKGl0ZW0pIHsNCiAgICAgIGlmIChpdGVtLlNjaGVkdWxlZF9Vc2VkX1Byb2Nlc3MpIHsNCiAgICAgICAgcmV0dXJuIGl0ZW0uU2NoZWR1bGVkX1VzZWRfUHJvY2Vzcw0KICAgICAgfQ0KICAgICAgaWYgKGl0ZW0uQ29tcG9uZW50X1RlY2hub2xvZ3lfUGF0aCkgew0KICAgICAgICBjb25zdCBsaXN0ID0gaXRlbS5Db21wb25lbnRfVGVjaG5vbG9neV9QYXRoLnNwbGl0KCcvJykNCiAgICAgICAgaWYgKGxpc3QuaW5jbHVkZXMoaXRlbS5QYXJ0X1VzZWRfUHJvY2VzcykpIHsNCiAgICAgICAgICByZXR1cm4gaXRlbS5QYXJ0X1VzZWRfUHJvY2Vzcw0KICAgICAgICB9IGVsc2UgaWYgKGxpc3QuaW5jbHVkZXMoaXRlbS5QYXJ0X1R5cGVfVXNlZF9Qcm9jZXNzKSkgew0KICAgICAgICAgIHJldHVybiBpdGVtLlBhcnRfVHlwZV9Vc2VkX1Byb2Nlc3MNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgaWYgKGl0ZW0uUGFydF9Vc2VkX1Byb2Nlc3MpIHsNCiAgICAgICAgICByZXR1cm4gaXRlbS5QYXJ0X1VzZWRfUHJvY2Vzcw0KICAgICAgICB9IGVsc2UgaWYgKGl0ZW0uUGFydF9UeXBlX1VzZWRfUHJvY2Vzcykgew0KICAgICAgICAgIHJldHVybiBpdGVtLlBhcnRfVHlwZV9Vc2VkX1Byb2Nlc3MNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICByZXR1cm4gJycNCiAgICB9LA0KICAgIHNldFBhcnRDb2x1bW4oKSB7DQogICAgICAvLyDnuq/pm7bku7YNCiAgICAgIHRoaXMuaXNPd25lck51bGwgPSB0aGlzLnRiRGF0YS5ldmVyeSh2ID0+ICF2LkNvbXBfSW1wb3J0X0RldGFpbF9JZCkNCiAgICAgIGNvbnNvbGUubG9nKCd0aGlzLmlzT3duZXJOdWxsJywgdGhpcy5pc093bmVyTnVsbCkNCiAgICAgIGlmICh0aGlzLmlzT3duZXJOdWxsKSB7DQogICAgICAgIGNvbnN0IGlkeCA9IHRoaXMuY29sdW1ucy5maW5kSW5kZXgodiA9PiB2LkNvZGUgPT09ICdDb21wb25lbnRfQ29kZScpDQogICAgICAgIGlkeCAhPT0gLTEgJiYgdGhpcy5jb2x1bW5zLnNwbGljZShpZHgsIDEpDQogICAgICB9DQogICAgfSwNCiAgICBtZXJnZURhdGEobGlzdCkgew0KICAgICAgbGlzdA0KICAgICAgICAuZm9yRWFjaCgoZWxlbWVudCkgPT4gew0KICAgICAgICAgIGNvbnN0IGlkeCA9IHRoaXMuYmFja2VuZFRiLmZpbmRJbmRleCgNCiAgICAgICAgICAgIChpdGVtKSA9PiBlbGVtZW50LnB1dWlkICYmIGl0ZW0udXVpZCA9PT0gZWxlbWVudC5wdXVpZA0KICAgICAgICAgICkNCiAgICAgICAgICBpZiAoaWR4ICE9PSAtMSkgew0KICAgICAgICAgICAgdGhpcy50YkRhdGEuc3BsaWNlKGlkeCwgMCwgZGVlcENsb25lKHRoaXMuYmFja2VuZFRiW2lkeF0pKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCg0KICAgICAgdGhpcy50YkRhdGEuc29ydCgoYSwgYikgPT4gYS5pbml0Um93SW5kZXggLSBiLmluaXRSb3dJbmRleCkNCg0KICAgICAgdGhpcy5maWx0ZXJEYXRhKCkNCiAgICB9LA0KICAgIGhhbmRsZUNsb3NlKCkgew0KICAgICAgdGhpcy4kZW1pdCgnY2xvc2UnKQ0KICAgIH0sDQogICAgLy8gYWN0aXZlQ2VsbE1ldGhvZCh7IHJvdywgY29sdW1uLCBjb2x1bW5JbmRleCB9KSB7DQogICAgLy8gICByZXR1cm4gY29sdW1uLmZpZWxkID09PSAnU2NoZHVsaW5nX0NvdW50Jw0KICAgIC8vIH0sDQogICAgYXN5bmMgZ2V0VGFibGVDb25maWcoY29kZSkgew0KICAgICAgYXdhaXQgR2V0R3JpZEJ5Q29kZSh7DQogICAgICAgIGNvZGUNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBjb25zdCB7IElzU3VjY2VlZCwgRGF0YSwgTWVzc2FnZSB9ID0gcmVzDQogICAgICAgIGlmIChJc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLnRiQ29uZmlnID0gT2JqZWN0LmFzc2lnbih7fSwgdGhpcy50YkNvbmZpZywgRGF0YS5HcmlkKQ0KICAgICAgICAgIHRoaXMucGFnZUluZm8ucGFnZVNpemUgPSBOdW1iZXIodGhpcy50YkNvbmZpZy5Sb3dfTnVtYmVyKQ0KICAgICAgICAgIGNvbnN0IGxpc3QgPSBEYXRhLkNvbHVtbkxpc3QgfHwgW10NCiAgICAgICAgICB0aGlzLmNvbHVtbnMgPSBsaXN0LmZpbHRlcih2ID0+IHYuSXNfRGlzcGxheSkNCiAgICAgICAgICAgIC5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgICAgIGlmIChpdGVtLklzX0Zyb3plbikgew0KICAgICAgICAgICAgICAgIGl0ZW0uZml4ZWQgPSAnbGVmdCcNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAvLyB0aGlzLmNvbHVtbnMucHVzaCh7DQogICAgICAgICAgLy8gICBEaXNwbGF5X05hbWU6ICfmjpLkuqfmlbDph48nLA0KICAgICAgICAgIC8vICAgQ29kZTogJ1NjaGR1bGluZ19Db3VudCcNCiAgICAgICAgICAvLyB9KQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgZ2V0T2JqZWN0VHlwZUxpc3QoKSB7DQogICAgICBHZXRDb21wVHlwZVRyZWUoeyBwcm9mZXNzaW9uYWw6ICdTdGVlbCcgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy5PYmplY3RUeXBlTGlzdC5kYXRhID0gcmVzLkRhdGENCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoXykgPT4gew0KICAgICAgICAgICAgdGhpcy4kcmVmcy50cmVlU2VsZWN0T2JqZWN0VHlwZS50cmVlRGF0YVVwZGF0ZUZ1bihyZXMuRGF0YSkNCiAgICAgICAgICB9KQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGdldFR5cGUoKSB7DQogICAgICBHZXRQYXJ0VHlwZUxpc3QoeyBQYXJ0X0dyYWRlOiAwIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLnR5cGVPcHRpb24gPSByZXMuRGF0YQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFkZFRvTGlzdCgpIHsNCiAgICAgIGlmICghdGhpcy50b3RhbFNlbGVjdGlvbi5sZW5ndGgpIHJldHVybg0KICAgICAgdGhpcy5oYW5kbGVTYXZlKDEpDQogICAgfSwNCiAgICBnZXRJbnN0YWxsVW5pdElkTmFtZUxpc3QoaWQpIHsNCiAgICAgIGlmICghdGhpcy5hcmVhSWQpIHsNCiAgICAgICAgdGhpcy5pbnN0YWxsVW5pdElkTGlzdCA9IFtdDQogICAgICB9IGVsc2Ugew0KICAgICAgICBHZXRJbnN0YWxsVW5pdElkTmFtZUxpc3QoeyBBcmVhX0lkOiB0aGlzLmFyZWFJZCB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgdGhpcy5pbnN0YWxsVW5pdElkTGlzdCA9IHJlcy5EYXRhIHx8IFtdDQogICAgICAgICAgLy8gaWYgKHRoaXMuaW5zdGFsbFVuaXRJZExpc3QubGVuZ3RoKSB7DQogICAgICAgICAgLy8gICB0aGlzLmZvcm0uSW5zdGFsbFVuaXRfSWQgPSBbdGhpcy5pbnN0YWxsVW5pdElkTGlzdFswXS5JZF0NCiAgICAgICAgICAvLyB9DQogICAgICAgIH0pDQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["addDraft.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "addDraft.vue", "sourceRoot": "src/views/PRO/plan-production/schedule-production-new/components", "sourcesContent": ["<template>\r\n  <div class=\"contentBox\">\r\n    <el-form ref=\"form\" :model=\"form\" label-width=\"90px\">\r\n      <el-row>\r\n        <el-col :span=\"7\">\r\n          <el-form-item :label=\"`${comName}编号`\" prop=\"Comp_Codes\">\r\n            <!--              <el-input\r\n                v-model=\"form.Comp_Code\"\r\n                clearable\r\n                style=\"width: 45%\"\r\n                placeholder=\"请输入(空格区分/多个搜索)\"\r\n                type=\"text\"\r\n              />\r\n              <el-input\r\n                v-model=\"form.Comp_CodeBlur\"\r\n                clearable\r\n                style=\"width: 45%;margin-left: 16px\"\r\n                placeholder=\"模糊查找(请输入关键字)\"\r\n                type=\"text\"\r\n              />-->\r\n            <el-input\r\n              v-model=\"searchContent\"\r\n              clearable\r\n              class=\"input-with-select w100\"\r\n              placeholder=\"请输入(空格区分/多个搜索)\"\r\n              size=\"small\"\r\n            >\r\n              <el-select\r\n                slot=\"prepend\"\r\n                v-model=\"curSearch\"\r\n                placeholder=\"请选择\"\r\n                style=\"width: 100px\"\r\n              >\r\n                <el-option label=\"精准查询\" :value=\"1\" />\r\n                <el-option label=\"模糊查询\" :value=\"0\" />\r\n              </el-select>\r\n            </el-input>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"5\">\r\n          <el-form-item :label=\"`${comName}类型`\" prop=\"Type\">\r\n            <el-tree-select\r\n              ref=\"treeSelectObjectType\"\r\n              v-model=\"form.Type\"\r\n              style=\"width: 100%\"\r\n              class=\"cs-tree-x\"\r\n              :select-params=\"treeSelectParams\"\r\n              :tree-params=\"ObjectTypeList\"\r\n              value-key=\"Id\"\r\n            />\r\n            <!--              <el-select v-model=\"form.Type\" placeholder=\"请选择\" clearable @clear=\"filterData\">\r\n                <el-option label=\"全部\" value=\"\" />\r\n                <el-option\r\n                  v-for=\"item in comTypeOptions\"\r\n                  :key=\"item.value\"\r\n                  :label=\"item.label\"\r\n                  :value=\"item.value\"\r\n                />\r\n              </el-select>-->\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"规格\" prop=\"Spec\" label-width=\"50px\">\r\n            <el-input v-model.trim=\"form.Spec\" placeholder=\"请输入\" clearable />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col v-if=\"isVersionFour\" :span=\"3\">\r\n          <el-form-item label=\"批次\" label-width=\"50px\" prop=\"Create_UserName\">\r\n            <el-select\r\n              v-model=\"form.InstallUnit_Id\"\r\n              filterable\r\n              clearable\r\n              multiple\r\n              style=\"width: 100%\"\r\n              placeholder=\"请选择\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in installUnitIdList\"\r\n                :key=\"item.Id\"\r\n                :label=\"item.Name\"\r\n                :value=\"item.Id\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"5\">\r\n          <el-button style=\"margin-left: 10px\" @click=\"handleReset\">重置</el-button>\r\n          <el-button style=\"margin-left: 10px\" type=\"primary\" @click=\"handleSearch()\">查询</el-button>\r\n          <el-button :loading=\"addLoading\" style=\"margin-left: 10px\" type=\"primary\" @click=\"addToList()\">加入列表</el-button>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n    <div class=\"tb-wrapper\">\r\n      <vxe-table\r\n        ref=\"xTable1\"\r\n        :empty-render=\"{name: 'NotData'}\"\r\n        show-header-overflow\r\n        empty-text=\"暂无数据\"\r\n        height=\"auto\"\r\n        show-overflow\r\n        :checkbox-config=\"{checkField: 'checked', checkMethod: checkCheckboxMethod}\"\r\n        :loading=\"tbLoading\"\r\n        :row-config=\"{isCurrent: true, isHover: true }\"\r\n        class=\"cs-vxe-table\"\r\n        align=\"left\"\r\n        stripe\r\n        :data=\"fTable\"\r\n        resizable\r\n        :edit-config=\"{trigger: 'click', mode: 'cell'}\"\r\n        :tooltip-config=\"{ enterable: true }\"\r\n        @checkbox-all=\"tbSelectChange\"\r\n        @checkbox-change=\"tbSelectChange\"\r\n      >\r\n        <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n        <template v-for=\"item in columns\">\r\n          <vxe-column\r\n            v-if=\"item.Code === 'Is_Component'\"\r\n            :key=\"item.Code\"\r\n            :align=\"item.Align\"\r\n            :field=\"item.Code\"\r\n            :title=\"item.Display_Name\"\r\n            sortable\r\n            :min-width=\"item.Width\"\r\n          >\r\n            <template #default=\"{ row }\">\r\n              <el-tag :type=\"row.Is_Component ? 'danger' : 'success'\">{{\r\n                row.Is_Component ? \"否\" : \"是\"\r\n              }}</el-tag>\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column\r\n            v-else-if=\"['Part_Code','Comp_Code'].includes(item.Code)\"\r\n            :key=\"item.Code\"\r\n            :align=\"item.Align\"\r\n            :field=\"item.Code\"\r\n            :title=\"item.Display_Name\"\r\n            sortable\r\n            :min-width=\"item.Width\"\r\n          >\r\n            <template #default=\"{ row }\">\r\n              <el-tag v-if=\"row.Is_Change\" style=\"margin-right: 8px;\" type=\"danger\">变</el-tag>\r\n              <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n              {{ row[item.Code] | displayValue }}\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column\r\n            v-else\r\n            :key=\"item.Code\"\r\n            :align=\"item.Align\"\r\n            :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n            show-overflow=\"tooltip\"\r\n            sortable\r\n            :field=\"item.Code\"\r\n            :title=\"item.Display_Name\"\r\n            :min-width=\"item.Width\"\r\n          />\r\n        </template>\r\n      </vxe-table>\r\n    </div>\r\n    <div class=\"data-info\">\r\n      <el-tag\r\n        size=\"medium\"\r\n        class=\"info-x\"\r\n      >已选 {{ totalSelection.length }} 条数据\r\n      </el-tag>\r\n      <vxe-pager\r\n        border\r\n        background\r\n        :loading=\"tbLoading\"\r\n        :current-page.sync=\"pageInfo.page\"\r\n        :page-size.sync=\"pageInfo.pageSize\"\r\n        :page-sizes=\"pageInfo.pageSizes\"\r\n        :total=\"pageInfo.total\"\r\n        :layouts=\"['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']\"\r\n        size=\"small\"\r\n        @page-change=\"handlePageChange\"\r\n      />\r\n    </div>\r\n    <div class=\"button\">\r\n      <el-button @click=\"handleClose\">取消</el-button>\r\n      <el-button\r\n        type=\"primary\"\r\n        :disabled=\"!totalSelection.length\"\r\n        :loading=\"saveLoading\"\r\n        @click=\"handleSave(2)\"\r\n      >保存</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { GetCanSchdulingComps, GetStopList } from '@/api/PRO/production-task'\r\nimport { GetCanSchdulingParts, GetPartList } from '@/api/PRO/production-part'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { debounce, deepClone } from '@/utils'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport { GetCompTypeTree } from '@/api/PRO/professionalType'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\nimport { mapGetters } from 'vuex'\r\nimport { GetInstallUnitIdNameList } from '@/api/PRO/project'\r\n\r\nexport default {\r\n  props: {\r\n    comName: {\r\n      required: true,\r\n      type: String,\r\n      default: ''\r\n    },\r\n    levelCode: {\r\n      required: true,\r\n      type: String,\r\n      default: ''\r\n    },\r\n    scheduleId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    pageType: {\r\n      type: String,\r\n      default: 'com'\r\n    },\r\n    showDialog: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    areaId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    installId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    currentIds: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    isPartPrepare: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      pageInfo: {\r\n        page: 1,\r\n        pageSize: 500,\r\n        pageSizes: tablePageSize,\r\n        total: 0\r\n      },\r\n      form: {\r\n        Comp_Code: '',\r\n        Comp_CodeBlur: '',\r\n        Part_CodeBlur: '',\r\n        Part_Code: '',\r\n        Type_Name: '',\r\n        InstallUnit_Id: '',\r\n        Spec: '',\r\n        Type: ''\r\n      },\r\n      searchContent: '',\r\n      curSearch: 1,\r\n      isOwnerNull: true,\r\n      tbLoading: false,\r\n      addLoading: false,\r\n      saveLoading: false,\r\n      columns: [],\r\n      fTable: [],\r\n      tbConfig: {},\r\n      TotalCount: 0,\r\n      Page: 0,\r\n      multipleSelection: [],\r\n      installUnitIdList: [],\r\n      totalSelection: [],\r\n      search: () => ({}),\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        clearable: true\r\n      },\r\n      ObjectTypeList: {\r\n        // 构件类型\r\n        'check-strictly': true,\r\n        'default-expand-all': true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      },\r\n      typeOption: []\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    },\r\n    ...mapGetters('tenant', ['isVersionFour'])\r\n  },\r\n  watch: {\r\n    showDialog(newValue) {\r\n      newValue && (this.saveLoading = false)\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getConfig()\r\n    if (this.isCom) {\r\n      this.getObjectTypeList()\r\n    } else {\r\n      this.getType()\r\n    }\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    this.getInstallUnitIdNameList()\r\n  },\r\n  methods: {\r\n    async getConfig() {\r\n      let code = ''\r\n      code = this.isCom\r\n        ? 'PROComDraftEditTbConfig'\r\n        : 'PROPartDraftEditTbConfig_new'\r\n      await this.getTableConfig(code)\r\n      this.fetchData()\r\n    },\r\n    filterData(page) {\r\n      if (this.curSearch === 1) {\r\n        this.form.Comp_Code = this.searchContent\r\n        this.form.Comp_CodeBlur = ''\r\n      }\r\n      if (this.curSearch === 0) {\r\n        this.form.Comp_CodeBlur = this.searchContent\r\n        this.form.Comp_Code = ''\r\n      }\r\n\r\n      const f = []\r\n      for (const formKey in this.form) {\r\n        if (this.form[formKey] || this.form[formKey] === false) {\r\n          f.push(formKey)\r\n        }\r\n      }\r\n      if (!f.length) {\r\n        this.setPage()\r\n        !page && (this.pageInfo.page = 1)\r\n        this.pageInfo.total = this.tbData.length\r\n        return\r\n      }\r\n      const temTbData = this.tbData.filter(v => {\r\n        v.checked = false\r\n\r\n        const splitAndClean = (input) => input.trim().replace(/\\s+/g, ' ').split(' ')\r\n\r\n        if (this.form.Comp_Code.trim()) {\r\n          const compCodeArray = splitAndClean(this.form.Comp_Code)\r\n          if (!compCodeArray.includes(v['Comp_Code'])) {\r\n            return false\r\n          }\r\n        }\r\n\r\n        if (this.form.Comp_CodeBlur.trim()) {\r\n          const compCodeBlurArray = splitAndClean(this.form.Comp_CodeBlur)\r\n          if (!compCodeBlurArray.some(code => v['Comp_Code'].includes(code))) {\r\n            return false\r\n          }\r\n        }\r\n\r\n        if (this.form.Type && v.Type !== this.form.Type) {\r\n          return false\r\n        }\r\n\r\n        if (this.form.Part_CodeBlur.trim()) {\r\n          const partCodeBlurArray = splitAndClean(this.form.Part_CodeBlur)\r\n          if (!partCodeBlurArray.some(code => v['Part_Code'].includes(code))) {\r\n            return false\r\n          }\r\n        }\r\n\r\n        if (this.form.Part_Code.trim()) {\r\n          const partCodeArray = splitAndClean(this.form.Part_Code)\r\n          if (!partCodeArray.includes(v['Part_Code'])) {\r\n            return false\r\n          }\r\n        }\r\n\r\n        if (this.isVersionFour && this.form.InstallUnit_Id.length && !this.form.InstallUnit_Id.includes(v.InstallUnit_Id)) {\r\n          return false\r\n        }\r\n\r\n        if (this.form.Type_Name !== '' && v.Type_Name !== this.form.Type_Name) {\r\n          return false\r\n        }\r\n\r\n        if (this.form.Spec.trim() !== '') {\r\n          const specArray = splitAndClean(this.form.Spec)\r\n          if (!specArray.some(spec => v.Spec.includes(spec))) {\r\n            return false\r\n          }\r\n        }\r\n\r\n        return true\r\n      })\r\n\r\n      !page && (this.pageInfo.page = 1)\r\n      this.pageInfo.total = temTbData.length\r\n      this.setPage(temTbData)\r\n    },\r\n    handleSearch() {\r\n      this.totalSelection = []\r\n      this.clearSelect()\r\n      if (this.tbData?.length) {\r\n        this.tbData.forEach(item => item.checked = false)\r\n        this.filterData()\r\n      }\r\n    },\r\n    handleReset() {\r\n      this.form.Type_Name = ''\r\n      this.form.Comp_Code = ''\r\n      this.form.Comp_CodeBlur = ''\r\n      this.form.Type = ''\r\n      this.form.Spec = ''\r\n      this.searchContent = ''\r\n      this.handleSearch()\r\n    },\r\n    handleSelect(data) {\r\n      this.multipleSelection = data\r\n    },\r\n    tbSelectChange(array) {\r\n      this.totalSelection = this.tbData.filter(v => v.checked)\r\n    },\r\n    clearSelect() {\r\n      this.$refs.xTable1.clearCheckboxRow()\r\n      this.totalSelection = []\r\n    },\r\n    async fetchData() {\r\n      this.tbLoading = true\r\n      await this.getComTbData()\r\n      this.initTbData()\r\n      this.filterData()\r\n      this.tbLoading = false\r\n    },\r\n    setPageData() {\r\n      if (this.tbData?.length) {\r\n        this.pageInfo.page = 1\r\n        this.tbData = this.tbData.filter(v => v.Can_Schduling_Count > 0)\r\n        this.filterData()\r\n      }\r\n    },\r\n    handleSave(type = 2) {\r\n      if (type === 1) {\r\n        this.addLoading = true\r\n      } else {\r\n        this.saveLoading = true\r\n      }\r\n      setTimeout(() => {\r\n        this.totalSelection.forEach((item) => {\r\n          const intCount = parseInt(item.count)\r\n          item.Schduled_Count += intCount\r\n          item.Can_Schduling_Count -= intCount\r\n          item.Can_Schduling_Weight = item.Can_Schduling_Count * item.Weight\r\n          item.maxCount = item.Can_Schduling_Count\r\n          item.chooseCount = intCount\r\n          item.count = item.Can_Schduling_Count\r\n          item.checked = false\r\n        })\r\n        const cp = deepClone(this.totalSelection)\r\n\r\n        this.$emit('sendSelectList', cp)\r\n        this.addLoading = false\r\n        this.clearSelect()\r\n        // this.setPage()\r\n        this.setPageData()\r\n        console.log('type', type)\r\n        if (type === 2) {\r\n          this.$emit('close')\r\n        }\r\n      }, 0)\r\n    },\r\n    initTbData() {\r\n      // 设置文本框选择的排产数量,设置自定义唯一码\r\n      const objKey = {}\r\n      if (!this.tbData?.length) {\r\n        this.tbData = []\r\n        this.backendTb = []\r\n        return\r\n      }\r\n      this.tbData.forEach((item) => {\r\n        this.$set(item, 'count', item.Can_Schduling_Count)\r\n        this.$set(item, 'maxCount', item.Can_Schduling_Count)\r\n        item.uuid = uuidv4()\r\n        objKey[item.Type] = true\r\n      })\r\n      this.backendTb = deepClone(this.tbData)\r\n    },\r\n    async getComTbData() {\r\n      // const { install, areaId } = this.$route.query\r\n      const { Comp_Codes, ...obj } = this.form\r\n      let codes = []\r\n      if (Object.prototype.toString.call(Comp_Codes) === '[object String]') {\r\n        codes = Comp_Codes && Comp_Codes.split(' ').filter(v => !!v)\r\n      }\r\n      this.tbData = []\r\n      await GetCanSchdulingComps({\r\n        Ids: this.currentIds,\r\n        ...obj,\r\n        Schduling_Plan_Id: this.scheduleId,\r\n        Comp_Codes: codes,\r\n        Bom_Level: this.levelCode,\r\n        InstallUnit_Id: this.installId,\r\n        Area_Id: this.areaId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const _tbData = res.Data || []\r\n          this.pageInfo.total = _tbData?.length || 0\r\n          this.tbData = _tbData.map((v, idx) => {\r\n            // 已排产赋值\r\n            v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n            v.Workshop_Id = v.Scheduled_Workshop_Id\r\n            v.Workshop_Name = v.Scheduled_Workshop_Name\r\n            v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n            // if (v.originalPath) {\r\n            // v.isDisabled = true\r\n            // }\r\n            v.checked = false\r\n            v.initRowIndex = idx\r\n            // v.technologyPathDisabled = !!v.Technology_Path\r\n            return v\r\n          })\r\n          this.setPage()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      const submitObj = this.tbData.map(item => {\r\n        return {\r\n          Id: item.Comp_Import_Detail_Id,\r\n          Type: 2\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = !!item.Is_Stop\r\n          })\r\n          this.tbData.forEach(row => {\r\n            if (stopMap.hasOwnProperty(row.Comp_Import_Detail_Id)) {\r\n              this.$set(row, 'stopFlag', stopMap[row.Comp_Import_Detail_Id])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    checkCheckboxMethod({ row }) {\r\n      return !row.stopFlag\r\n    },\r\n    /**\r\n     * 分页\r\n     */\r\n    handlePageChange({ currentPage, pageSize }) {\r\n      if (this.tbLoading) return\r\n      this.pageInfo.page = currentPage\r\n      this.pageInfo.pageSize = pageSize\r\n      this.setPage()\r\n      this.filterData(currentPage)\r\n    },\r\n\r\n    setPage(tb = this.tbData) {\r\n      this.fTable = tb.slice((this.pageInfo.page - 1) * this.pageInfo.pageSize, this.pageInfo.page * this.pageInfo.pageSize)\r\n    },\r\n\r\n    getPartUsedProcess(item) {\r\n      if (item.Scheduled_Used_Process) {\r\n        return item.Scheduled_Used_Process\r\n      }\r\n      if (item.Component_Technology_Path) {\r\n        const list = item.Component_Technology_Path.split('/')\r\n        if (list.includes(item.Part_Used_Process)) {\r\n          return item.Part_Used_Process\r\n        } else if (list.includes(item.Part_Type_Used_Process)) {\r\n          return item.Part_Type_Used_Process\r\n        }\r\n      } else {\r\n        if (item.Part_Used_Process) {\r\n          return item.Part_Used_Process\r\n        } else if (item.Part_Type_Used_Process) {\r\n          return item.Part_Type_Used_Process\r\n        }\r\n      }\r\n\r\n      return ''\r\n    },\r\n    setPartColumn() {\r\n      // 纯零件\r\n      this.isOwnerNull = this.tbData.every(v => !v.Comp_Import_Detail_Id)\r\n      console.log('this.isOwnerNull', this.isOwnerNull)\r\n      if (this.isOwnerNull) {\r\n        const idx = this.columns.findIndex(v => v.Code === 'Component_Code')\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n      }\r\n    },\r\n    mergeData(list) {\r\n      list\r\n        .forEach((element) => {\r\n          const idx = this.backendTb.findIndex(\r\n            (item) => element.puuid && item.uuid === element.puuid\r\n          )\r\n          if (idx !== -1) {\r\n            this.tbData.splice(idx, 0, deepClone(this.backendTb[idx]))\r\n          }\r\n        })\r\n\r\n      this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n\r\n      this.filterData()\r\n    },\r\n    handleClose() {\r\n      this.$emit('close')\r\n    },\r\n    // activeCellMethod({ row, column, columnIndex }) {\r\n    //   return column.field === 'Schduling_Count'\r\n    // },\r\n    async getTableConfig(code) {\r\n      await GetGridByCode({\r\n        code\r\n      }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n          this.pageInfo.pageSize = Number(this.tbConfig.Row_Number)\r\n          const list = Data.ColumnList || []\r\n          this.columns = list.filter(v => v.Is_Display)\r\n            .map(item => {\r\n              if (item.Is_Frozen) {\r\n                item.fixed = 'left'\r\n              }\r\n              return item\r\n            })\r\n          // this.columns.push({\r\n          //   Display_Name: '排产数量',\r\n          //   Code: 'Schduling_Count'\r\n          // })\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getObjectTypeList() {\r\n      GetCompTypeTree({ professional: 'Steel' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.ObjectTypeList.data = res.Data\r\n          this.$nextTick((_) => {\r\n            this.$refs.treeSelectObjectType.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getType() {\r\n      GetPartTypeList({ Part_Grade: 0 }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.typeOption = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    addToList() {\r\n      if (!this.totalSelection.length) return\r\n      this.handleSave(1)\r\n    },\r\n    getInstallUnitIdNameList(id) {\r\n      if (!this.areaId) {\r\n        this.installUnitIdList = []\r\n      } else {\r\n        GetInstallUnitIdNameList({ Area_Id: this.areaId }).then(res => {\r\n          this.installUnitIdList = res.Data || []\r\n          // if (this.installUnitIdList.length) {\r\n          //   this.form.InstallUnit_Id = [this.installUnitIdList[0].Id]\r\n          // }\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.contentBox {\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .button {\r\n    margin-top: 16px;\r\n    display: flex;\r\n    justify-content: end;\r\n  }\r\n\r\n  .tb-wrapper {\r\n    flex: 1 1 auto;\r\n    height: 50vh;\r\n  }\r\n\r\n  .data-info{\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-top: 16px;\r\n  }\r\n}\r\n</style>\r\n"]}]}
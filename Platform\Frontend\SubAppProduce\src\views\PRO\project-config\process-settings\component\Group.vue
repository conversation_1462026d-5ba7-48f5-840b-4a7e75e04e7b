<template>
  <div>
    <el-transfer
      v-model="selectList"
      filterable
      :button-texts="['移除','添加']"
      :filter-method="filterMethod"
      :titles="['全部班组', '已选班组']"
      filter-placeholder="搜索..."
      :data="allList"
      :props="{label:'Display_Name',value:'Id',key:'Id'}"
      @change="change"
    />
    <div style="text-align: right;margin-top: 10px">
      <el-button @click="$emit('close')">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </div>
  </div>
</template>

<script>
import {
  GetWorkingTeamBase,
  GetWorkingTeams,
  UpdateProcessTeam
} from '@/api/PRO/technology-lib'

export default {
  data() {
    return {
      allList: [],
      selectList: [],
      list: [],
      value: [],
      processId: '',
      filterMethod(query, item) {
        return item.Display_Name.indexOf(query) > -1
      }
    }
  },
  methods: {
    async init(row) {
      this.processId = row.Id
      await this.getAllList()
      await this.getCurrentList(row)
    },
    getAllList() {
      return new Promise((resolve) => {
        GetWorkingTeams({}).then(res => {
          if (res.IsSucceed) {
            this.allList = res.Data.map(v => {
              this.$set(v, 'Display_Name', v.Name)
              return v
            })
            resolve()
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      })
    },
    getCurrentList() {
      return new Promise((resolve) => {
        GetWorkingTeamBase({
          processId: this.processId
        }).then(res => {
          if (res.IsSucceed) {
            this.selectList = res.Data.map(v => v.Id)
            resolve()
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      })
    },
    change(array) {
      console.log('array', array)
      this.selectList = array
    },
    handleSubmit() {
      UpdateProcessTeam({
        processId: this.processId,
        teams: this.selectList
      }).then(res => {
        if (res.IsSucceed) {
          this.$emit('close')
          this.$emit('refresh')
          this.$message({
            message: '修改成功',
            type: 'success'
          })
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    }
  }
}
</script>

<style scoped>

</style>

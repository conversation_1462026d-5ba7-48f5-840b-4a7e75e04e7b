{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\component\\TreeData.vue?vue&type=style&index=0&id=2415e094&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\component\\TreeData.vue", "mtime": 1757468112480}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCkBpbXBvcnQgJ35AL3N0eWxlcy9taXhpbi5zY3NzJzsNCg0KLnRyZWUtY29udGFpbmVyew0KICBoZWlnaHQ6IDEwMCU7DQogIG1hcmdpbi1yaWdodDogMTZweDsNCiAgZmxleC1iYXNpczogMjAlOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjRkZGRkZGOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAudGl0bGV7DQogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNFRUVFRUU7DQogICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICBwYWRkaW5nOiAwIDE2cHggMTZweCAxNnB4Ow0KICAgIGNvbG9yOiAjMzMzMzMzOw0KICAgIC8vIGRpc3BsYXk6IGZsZXg7DQogICAgLy8ganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgIC50aXRsZS1uYW1lIHsNCiAgICAgIGhlaWdodDogMzBweDsNCiAgICAgIGxpbmUtaGVpZ2h0OiAzMHB4Ow0KICAgIH0NCiAgICAuYnRuLXh7DQogICAgICBwYWRkaW5nOiAwIDA7DQogICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgfQ0KICB9DQoNCiAgLnRyZWUtd3JhcHBlcnsNCiAgICBwYWRkaW5nOiAxNnB4Ow0KICAgIGZsZXg6IDE7DQogICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgICAuZWwtdHJlZXsNCiAgICAgIEBpbmNsdWRlIHNjcm9sbEJhcjsNCiAgICAgIGhlaWdodDogMTAwJTsNCiAgICAgIG92ZXJmbG93OiBhdXRvOw0KICAgIH0NCiAgICAuY3MtbGFiZWx7DQogICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICBtYXJnaW4tbGVmdDogNHB4Ow0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["TreeData.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0IA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TreeData.vue", "sourceRoot": "src/views/PRO/bom-setting/structure-type-config/component", "sourcesContent": ["<template>\r\n  <div :key=\"activeType\" class=\"tree-container\">\r\n    <div class=\"title\">\r\n      <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增</el-button>\r\n    </div>\r\n    <div class=\"tree-wrapper\">\r\n      <el-tree\r\n        ref=\"tree\"\r\n        v-loading=\"loading\"\r\n        :current-node-key=\"currentNodeKey\"\r\n        element-loading-text=\"加载中\"\r\n        element-loading-spinner=\"el-icon-loading\"\r\n        empty-text=\"暂无数据\"\r\n        highlight-current\r\n        node-key=\"Id\"\r\n        default-expand-all\r\n        :expand-on-click-node=\"false\"\r\n        :data=\"treeData\"\r\n        :props=\"{\r\n          label:'Label',\r\n          children:'Children'\r\n        }\"\r\n        @node-click=\"handleNodeClick\"\r\n      >\r\n        <span slot-scope=\"{ node, data }\" class=\"custom-tree-node\">\r\n          <svg-icon\r\n            :icon-class=\"\r\n              node.expanded ? 'icon-folder-open' : 'icon-folder'\r\n            \"\r\n            class-name=\"class-icon\"\r\n          />\r\n          <span class=\"cs-label\" :title=\"node.label\">{{ node.label }}</span>\r\n        </span>\r\n      </el-tree>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetCompTypeTree } from '@/api/PRO/component-type'\r\nimport { GetPartTypeTree } from '@/api/PRO/partType'\r\n\r\nexport default {\r\n  props: {\r\n    typeCode: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    typeId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    activeType: {\r\n      type: String,\r\n      default: '-1'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      treeData: [],\r\n      bomList: [],\r\n      loading: true,\r\n      currentNodeKey: ''\r\n\r\n    }\r\n  },\r\n  watch: {\r\n    // currentNodeKey(newValue) {\r\n    //   this.$emit('showRight', newValue !== '')\r\n    // }\r\n    typeId: {\r\n      handler() {\r\n        this.currentNodeKey = ''\r\n        this.fetchData()\r\n      },\r\n      immediate: false\r\n    },\r\n    activeType(newValue) {\r\n      this.currentNodeKey = ''\r\n      this.fetchData()\r\n    }\r\n  },\r\n  async mounted() {\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n\r\n    async fetchData() {\r\n      if (!this.typeCode || !this.typeId) {\r\n        return\r\n      }\r\n      this.loading = true\r\n      console.log(this.activeType, 3313)\r\n      let res\r\n      try {\r\n        if (this.activeType === '-1') {\r\n          res = await GetCompTypeTree({ professional: this.typeCode })\r\n        } else {\r\n          res = await GetPartTypeTree({ professionalId: this.typeId, partGrade: this.activeType.toString() })\r\n        }\r\n        if (res.IsSucceed) {\r\n          this.treeData = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.treeData = []\r\n        }\r\n      } catch (error) {\r\n        this.treeData = []\r\n      }\r\n      this.loading = false\r\n      this.currentNodeKey && this.setTreeNode()\r\n    },\r\n    setTreeNode() {\r\n      this.$emit('nodeClick', this.$refs['tree'].getNode(this.currentNodeKey))\r\n      this.$nextTick(_ => {\r\n        this.$refs['tree'].setCurrentKey(this.currentNodeKey)\r\n      })\r\n    },\r\n    handleAdd() {\r\n      this.$emit('AddFirst')\r\n    },\r\n    handleNodeClick(data, node) {\r\n      this.currentNodeKey = data.Id\r\n      this.$emit('nodeClick', node)\r\n    },\r\n    resetKey(id) {\r\n      if (id === this.currentNodeKey) {\r\n        this.currentNodeKey = ''\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import '~@/styles/mixin.scss';\r\n\r\n.tree-container{\r\n  height: 100%;\r\n  margin-right: 16px;\r\n  flex-basis: 20%;\r\n  background-color: #FFFFFF;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .title{\r\n    border-bottom: 1px solid #EEEEEE;\r\n    font-weight: 500;\r\n    padding: 0 16px 16px 16px;\r\n    color: #333333;\r\n    // display: flex;\r\n    // justify-content: space-between;\r\n    .title-name {\r\n      height: 30px;\r\n      line-height: 30px;\r\n    }\r\n    .btn-x{\r\n      padding: 0 0;\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  .tree-wrapper{\r\n    padding: 16px;\r\n    flex: 1;\r\n    overflow: hidden;\r\n    .el-tree{\r\n      @include scrollBar;\r\n      height: 100%;\r\n      overflow: auto;\r\n    }\r\n    .cs-label{\r\n      font-size: 14px;\r\n      margin-left: 4px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
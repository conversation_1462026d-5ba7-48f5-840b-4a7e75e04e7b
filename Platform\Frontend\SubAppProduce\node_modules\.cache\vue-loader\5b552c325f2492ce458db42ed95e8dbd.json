{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\component\\TreeData.vue?vue&type=style&index=0&id=2415e094&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\component\\TreeData.vue", "mtime": 1756109946517}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQpAaW1wb3J0ICd+QC9zdHlsZXMvbWl4aW4uc2Nzcyc7DQoNCi50cmVlLWNvbnRhaW5lcnsNCiAgbWFyZ2luLXJpZ2h0OiAxNnB4Ow0KICBmbGV4LWJhc2lzOiAxOCU7DQogIGJhY2tncm91bmQtY29sb3I6ICNGRkZGRkY7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIC50aXRsZXsNCiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI0VFRUVFRTsNCiAgICBmb250LXdlaWdodDogNTAwOw0KICAgIHBhZGRpbmc6IDE2cHg7DQogICAgY29sb3I6ICMzMzMzMzM7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgLnRpdGxlLW5hbWUgew0KICAgICAgaGVpZ2h0OiAzMHB4Ow0KICAgICAgbGluZS1oZWlnaHQ6IDMwcHg7DQogICAgfQ0KICAgIC5idG4teHsNCiAgICAgIHBhZGRpbmc6IDAgMDsNCiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICB9DQogIH0NCg0KICAudHJlZS13cmFwcGVyew0KICAgIHBhZGRpbmc6IDE2cHg7DQogICAgZmxleDogMTsNCiAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgIC5lbC10cmVlew0KICAgICAgQGluY2x1ZGUgc2Nyb2xsQmFyOw0KICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgb3ZlcmZsb3c6IGF1dG87DQogICAgfQ0KICAgIC5jcy1sYWJlbHsNCiAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgIG1hcmdpbi1sZWZ0OiA0cHg7DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["TreeData.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+HA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TreeData.vue", "sourceRoot": "src/views/PRO/bom-setting/structure-type-config/component", "sourcesContent": ["<template>\r\n  <div class=\"tree-container\">\r\n    <div class=\"title\">\r\n      <el-radio-group v-model=\"activeType\" @change=\"changeType\">\r\n        <el-radio-button label=\"comp\">构件大类</el-radio-button>\r\n        <el-radio-button label=\"part\">零件大类</el-radio-button>\r\n      </el-radio-group>\r\n      <div class=\"btn-x\">\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"tree-wrapper\">\r\n      <el-tree\r\n        ref=\"tree\"\r\n        v-loading=\"loading\"\r\n        :current-node-key=\"currentNodeKey\"\r\n        element-loading-text=\"加载中\"\r\n        element-loading-spinner=\"el-icon-loading\"\r\n        empty-text=\"暂无数据\"\r\n        highlight-current\r\n        node-key=\"Id\"\r\n        default-expand-all\r\n        :expand-on-click-node=\"false\"\r\n        :data=\"treeData\"\r\n        :props=\"{\r\n          label:'Label',\r\n          children:'Children'\r\n        }\"\r\n        @node-click=\"handleNodeClick\"\r\n      >\r\n        <span slot-scope=\"{ node, data }\" class=\"custom-tree-node\">\r\n          <svg-icon\r\n            :icon-class=\"\r\n              node.expanded ? 'icon-folder-open' : 'icon-folder'\r\n            \"\r\n            class-name=\"class-icon\"\r\n          />\r\n          <span class=\"cs-label\" :title=\"node.label\">{{ node.label }}</span>\r\n        </span>\r\n      </el-tree>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetCompTypeTree } from '@/api/PRO/component-type'\r\nimport { GetPartTypeTree } from '@/api/PRO/partType'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\nexport default {\r\n  props: {\r\n    typeCode: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    typeId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      treeData: [],\r\n      loading: true,\r\n      currentNodeKey: '',\r\n      activeType: 'comp'\r\n    }\r\n  },\r\n  watch: {\r\n    currentNodeKey(newValue) {\r\n      this.$emit('showRight', newValue !== '')\r\n    }\r\n  },\r\n  mounted() {\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    changeType() {\r\n      this.$emit('changeType', this.activeType)\r\n      this.currentNodeKey = ''\r\n      this.fetchData()\r\n    },\r\n    async fetchData() {\r\n      this.loading = true\r\n      let res\r\n      if (this.activeType === 'comp') {\r\n        res = await GetCompTypeTree({ professional: this.typeCode })\r\n      } else {\r\n        res = await GetPartTypeTree({ professionalId: this.typeId })\r\n      }\r\n      if (res.IsSucceed) {\r\n        this.treeData = res.Data\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n        this.treeData = []\r\n      }\r\n      this.loading = false\r\n      this.currentNodeKey && this.setTreeNode()\r\n    },\r\n    setTreeNode() {\r\n      this.$emit('nodeClick', this.$refs['tree'].getNode(this.currentNodeKey))\r\n      this.$nextTick(_ => {\r\n        this.$refs['tree'].setCurrentKey(this.currentNodeKey)\r\n      })\r\n    },\r\n    handleAdd() {\r\n      this.$emit('AddFirst')\r\n    },\r\n    handleNodeClick(data, node) {\r\n      console.log(data, node)\r\n      this.currentNodeKey = data.Id\r\n      this.$emit('nodeClick', node)\r\n    },\r\n    resetKey(id) {\r\n      console.log(id, this.currentNodeKey, id === this.currentNodeKey, 'dsd')\r\n      if (id === this.currentNodeKey) {\r\n        this.currentNodeKey = ''\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import '~@/styles/mixin.scss';\r\n\r\n.tree-container{\r\n  margin-right: 16px;\r\n  flex-basis: 18%;\r\n  background-color: #FFFFFF;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .title{\r\n    border-bottom: 1px solid #EEEEEE;\r\n    font-weight: 500;\r\n    padding: 16px;\r\n    color: #333333;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    .title-name {\r\n      height: 30px;\r\n      line-height: 30px;\r\n    }\r\n    .btn-x{\r\n      padding: 0 0;\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  .tree-wrapper{\r\n    padding: 16px;\r\n    flex: 1;\r\n    overflow: hidden;\r\n    .el-tree{\r\n      @include scrollBar;\r\n      height: 100%;\r\n      overflow: auto;\r\n    }\r\n    .cs-label{\r\n      font-size: 14px;\r\n      margin-left: 4px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
<template>
  <div>
    <el-form ref="form" :rules="rules" :model="form" label-width="80px">
      <el-row>
        <el-col :span="24">
          <el-form-item label="检查类型" prop="Name">
            <el-input v-model="form.Name" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item style="text-align: right">
            <el-button @click="$emit('close')">关 闭</el-button>
            <el-button
              type="primary"
              @click="handleSubmit('form')"
            >确 定</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { AddCheckType } from '@/api/PRO/factorycheck'
import { EntityCheckType } from '@/api/PRO/factorycheck'
import { SaveCheckType } from '@/api/PRO/factorycheck'
export default {
  // props: {
  //   dialogData: {}
  // },
  data() {
    return {
      Bom_Level: '',
      check_object_id: '',
      form: {},
      rules: {
        Name: [{ required: true, message: '请填写完整表单', trigger: 'blur' }]
      },
      title: '',
      editInfo: {}
    }
  },
  // watch: {
  //   dialogData: {
  //     handler(newName, oldName) {
  //       console.log("newName",newName)
  //       if(newName) {
  //         this.form = Object.assign({},newName);
  //       }
  //     },
  //     deep: true,
  //     immediate: true
  //   },
  // },
  mounted() {},
  methods: {
    init(title, checkType, data) {
      this.title = title
      this.Check_Object_Id = checkType.Id
      this.Bom_Level = checkType.Code
      if (title === '编辑') {
        this.editInfo = data
        console.log(this.editInfo)
        this.getEntityCheckType(data)
      }
    },
    async addCheckType() {
      await AddCheckType({
        Name: this.form.Name,
        Check_Object_Id: this.Check_Object_Id,
        Bom_Level: this.Bom_Level
      }).then((res) => {
        if (res.IsSucceed) {
          this.$message({
            type: 'success',
            message: '保存成功'
          })
          this.$emit('close')
          this.dialogData = {}
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    getEntityCheckType(data) {
      EntityCheckType({ id: data.Id }).then((res) => {
        if (res.IsSucceed) {
          this.form = res.Data[0]
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    editCheckType() {
      SaveCheckType({
        Id: this.editInfo.Id,
        ...this.form,
        Check_Object_Id: this.Check_Object_Id,
        Bom_Level: this.Bom_Level
      }).then((res) => {
        if (res.IsSucceed) {
          this.$message({
            type: 'success',
            message: '编辑成功'
          })
          this.$emit('close')
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    handleSubmit(form) {
      this.$refs[form].validate((valid) => {
        if (valid) {
          this.title === '新增' ? this.addCheckType() : this.editCheckType()
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped></style>

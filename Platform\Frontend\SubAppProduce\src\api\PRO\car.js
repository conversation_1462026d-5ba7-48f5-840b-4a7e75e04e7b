
//  车辆信息API
import request from '@/utils/request'
import qs from 'qs'

// 获取单据分页列表 (Auth)
export function GetCarList(data) {
  return request({
    url: '/PRO/Car/GetCarList',
    method: 'post',
    data
  })
}

// 获取登录用户车辆信息分页列表 (Auth)
export function GetCurCarPageList(data) {
  return request({
    url: '/PRO/Car/GetCurCarPageList',
    method: 'post',
    data
  })
}

export function GetCarPageList(data) {
  return request({
    url: '/PRO/Car/GetCarPageList',
    method: 'post',
    data
  })
}
export function SaveCar(data) {
  return request({
    url: '/PRO/Car/SaveCar',
    method: 'post',
    data
  })
}

export function DeleteCar(data) {
  return request({
    url: '/PRO/Car/DeleteCar',
    method: 'post',
    data
  })
}

export function ImportCar(data) {
  return request({
    url: '/PRO/Car/ImportCar',
    method: 'post',
    data
  })
}

// 车辆导入模板 (Auth)
export function CarDataTemplate(data) {
  return request({
    url: '/PRO/Car/CarDataTemplate',
    method: 'post',
    data
  })
}
// 新增船舶

export function SaveBoat(data) {
  return request({
    url: '/PRO/Boat/SaveBoat',
    method: 'post',
    data
  })
}
//船舶列表
export function GetPageInfo(data) {
  return request({
    url: '/PRO/Boat/GetPageInfo',
    method: 'post',
    data
  })
}
//船舶删除
export function DeleteBoat(data) {
  return request({
    url: '/PRO/Boat/DeleteBoat',
    method: 'post',
    data
  })
}
//船舶模板
export function BoatDataTemplate(data) {
  return request({
    url: '/PRO/Boat/BoatDataTemplate',
    method: 'post',
    data
  })
}
//船舶导入
export function ImportBoat(data) {
  return request({
    url: '/PRO/Boat/ImportBoat',
    method: 'post',
    data
  })
}
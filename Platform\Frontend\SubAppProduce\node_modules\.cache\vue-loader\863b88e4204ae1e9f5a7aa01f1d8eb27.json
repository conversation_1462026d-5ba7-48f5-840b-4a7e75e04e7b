{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\part-type\\component\\Lack.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\part-type\\component\\Lack.vue", "mtime": 1757468112175}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRQYXJ0VHlwZUxpc3QsIFNldHRpbmdEZWZhdWx0IH0gZnJvbSAnQC9hcGkvUFJPL3BhcnRUeXBlJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGJ0bkxvYWRpbmc6IGZhbHNlLA0KICAgICAgZm9ybTogew0KICAgICAgICBJZDogJycNCiAgICAgIH0sDQogICAgICBvcHRpb25zOiBbXSwNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIElkOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqScsIHRyaWdnZXI6ICdjaGFuZ2UnIH0NCiAgICAgICAgXQ0KICAgICAgfQ0KDQogICAgfQ0KICB9LA0KICBtb3VudGVkKCkgew0KICAgIHRoaXMuZ2V0T3B0aW9uKCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGdldE9wdGlvbigpIHsNCiAgICAgIEdldFBhcnRUeXBlTGlzdCh7IFBhcnRfR3JhZGU6IDAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMub3B0aW9ucyA9IHJlcy5EYXRhDQogICAgICAgICAgdGhpcy5mb3JtLklkID0gdGhpcy5vcHRpb25zLmZpbmQoZWxlID0+IGVsZS5Jc19EZWZhdWx0KS5JZCB8fCAnJw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZUNsb3NlKCkgew0KICAgICAgdGhpcy4kZW1pdCgnY2xvc2UnKQ0KICAgIH0sDQogICAgaGFuZGxlU3VibWl0KCkgew0KICAgICAgdGhpcy4kcmVmc1snZm9ybSddLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAoIXZhbGlkKSByZXR1cm4NCiAgICAgICAgU2V0dGluZ0RlZmF1bHQoew0KICAgICAgICAgIGlkOiB0aGlzLmZvcm0uSWQNCiAgICAgICAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+aTjeS9nOaIkOWKnycsDQogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHRoaXMuJGVtaXQoJ2Nsb3NlJykNCiAgICAgICAgICAgIHRoaXMuJGVtaXQoJ3JlZnJlc2gnKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["Lack.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAqBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Lack.vue", "sourceRoot": "src/views/PRO/basic-information/part-type/component", "sourcesContent": ["<template>\r\n  <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"190px\" style=\"width: 100%\">\r\n    <el-form-item label=\"分类缺省时，默认选择为：\" prop=\"Id\">\r\n      <el-select v-model=\"form.Id\" placeholder=\"请选择\" clearable=\"\">\r\n        <el-option\r\n          v-for=\"item in options\"\r\n          :key=\"item.Id\"\r\n          :label=\"item.Name\"\r\n          :value=\"item.Id\"\r\n        />\r\n      </el-select>\r\n    </el-form-item>\r\n    <span class=\"tip\">当导入的零件清单中，零件分类缺省时，系统自动补全上述选择的分类。</span>\r\n    <el-form-item class=\"cs-footer\">\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit('form')\">确 定</el-button>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n\r\n<script>\r\nimport { GetPartTypeList, SettingDefault } from '@/api/PRO/partType'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      form: {\r\n        Id: ''\r\n      },\r\n      options: [],\r\n      rules: {\r\n        Id: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ]\r\n      }\r\n\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getOption()\r\n  },\r\n  methods: {\r\n    getOption() {\r\n      GetPartTypeList({ Part_Grade: 0 }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.options = res.Data\r\n          this.form.Id = this.options.find(ele => ele.Is_Default).Id || ''\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.$emit('close')\r\n    },\r\n    handleSubmit() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (!valid) return\r\n        SettingDefault({\r\n          id: this.form.Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '操作成功',\r\n              type: 'success'\r\n            })\r\n            this.$emit('close')\r\n            this.$emit('refresh')\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n      this.btnLoading = false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.cs-footer{\r\n  text-align: center;\r\n  margin-top: 40px;\r\n}\r\n.tip{\r\n  color: rgba(34, 40, 52, 0.4);\r\n  font-size: 12px;\r\n}\r\n</style>\r\n"]}]}
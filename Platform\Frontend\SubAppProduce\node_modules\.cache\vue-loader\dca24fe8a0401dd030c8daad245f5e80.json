{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\group\\component\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\group\\component\\detail.vue", "mtime": 1757468112129}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgQWRkVXNlciBmcm9tICcuL0FkZFVzZXInDQppbXBvcnQgew0KICBHZXRXb3JraW5nVGVhbUluZm8sDQogIFNhdmVXb3JraW5nVGVhbXMsDQogIEdldEZhY3RvcnlQZW9wbGVsaXN0DQp9IGZyb20gJ0AvYXBpL1BSTy90ZWNobm9sb2d5LWxpYicNCmltcG9ydCB7IEdldFdvcmtzaG9wUGFnZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vYmFzaWMtaW5mb3JtYXRpb24vd29ya3Nob3AnDQppbXBvcnQgeyBHZXRMb2NhdGlvbkxpc3QsIEdldFRvdGFsV2FyZWhvdXNlTGlzdE9mQ3VyRmFjdG9yeSB9IGZyb20gJ0AvYXBpL1BSTy9wcm8tc3RvY2snDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgY29tcG9uZW50czogew0KICAgIEFkZFVzZXINCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgc2hvd0RpYWxvZzogZmFsc2UsDQogICAgICB0YWdzOiBbXSwNCiAgICAgIGZvcm06IHsNCiAgICAgICAgTmFtZTogJycsDQogICAgICAgIE1hbmFnZXJfVXNlck5hbWU6ICcnLA0KICAgICAgICBNYW5hZ2VyX1VzZXJJZDogJycsDQogICAgICAgIExvYWQ6ICcnLA0KICAgICAgICBXb3Jrc2hvcF9OYW1lOiAnJywgLy8g5omA5bGe6L2m6Ze0DQogICAgICAgIFdvcmtzaG9wX0lkOiAnJywgLy8g5omA5bGe6L2m6Ze0SWQNCiAgICAgICAgTW9udGhfQXZnX0xvYWQ6IG51bGwsDQogICAgICAgIFNvcnQ6ICcwJywNCiAgICAgICAgSXNfT3V0c291cmNlOiBmYWxzZSwNCiAgICAgICAgSXNfRW5hYmxlZDogdHJ1ZSwNCiAgICAgICAgV2FyZWhvdXNlX0lkOiAnJywNCiAgICAgICAgTG9jYXRpb25fSWQ6ICcnDQogICAgICB9LA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgTmFtZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaUnLCB0cmlnZ2VyOiAnYmx1cicgfV0sDQogICAgICAgIFNvcnQ6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWlJywgdHJpZ2dlcjogJ2JsdXInIH1dDQogICAgICB9LA0KICAgICAgY2xhc3NMaXN0OiBbXSwNCiAgICAgIHdvcmtzaG9wTGlzdDogW10sDQogICAgICB3YXJlaG91c2VzOiBbXSwNCiAgICAgIGxvY2F0aW9uczogW10sDQoNCiAgICAgIElzX1dvcmtzaG9wX0VuYWJsZWQ6ICcnDQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkge30sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5nZXRUZWFtKCkNCiAgICB0aGlzLmdldFdhcmVob3VzZUxpc3QoKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLy8g6L6T5YWl5qGG5qCh6aqMDQogICAgaW5wdXRCbHVyKGUpIHsNCiAgICAgIGlmIChlIDwgMCkgew0KICAgICAgICB0aGlzLmZvcm0uTG9hZCA9IDANCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOaVsOaNruWIneWni+WMlg0KICAgIGluaXREYXRhKGlkLCBJc19Xb3Jrc2hvcF9FbmFibGVkKSB7DQogICAgICBpZiAoaWQpIHsNCiAgICAgICAgdGhpcy5pc0VkaXQgPSB0cnVlDQogICAgICAgIEdldFdvcmtpbmdUZWFtSW5mbyh7DQogICAgICAgICAgaWQNCiAgICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIGNvbnN0IHsNCiAgICAgICAgICAgICAgTWFuYWdlcl9Vc2VyTmFtZSwNCiAgICAgICAgICAgICAgTWFuYWdlcl9Vc2VySWQsDQogICAgICAgICAgICAgIExvYWQsDQogICAgICAgICAgICAgIE5hbWUsDQogICAgICAgICAgICAgIFVzZXJzLA0KICAgICAgICAgICAgICBJZCwNCiAgICAgICAgICAgICAgV29ya3Nob3BfTmFtZSwNCiAgICAgICAgICAgICAgV29ya3Nob3BfSWQsDQogICAgICAgICAgICAgIE1vbnRoX0F2Z19Mb2FkLA0KICAgICAgICAgICAgICBTb3J0LA0KICAgICAgICAgICAgICBJc19PdXRzb3VyY2UsDQogICAgICAgICAgICAgIElzX0VuYWJsZWQsDQogICAgICAgICAgICAgIFdhcmVob3VzZV9JZCwNCiAgICAgICAgICAgICAgTG9jYXRpb25fSWQNCiAgICAgICAgICAgIH0gPSByZXMuRGF0YQ0KICAgICAgICAgICAgdGhpcy5mb3JtLk1hbmFnZXJfVXNlck5hbWUgPSBNYW5hZ2VyX1VzZXJOYW1lDQogICAgICAgICAgICB0aGlzLmZvcm0uTWFuYWdlcl9Vc2VySWQgPSBNYW5hZ2VyX1VzZXJJZA0KICAgICAgICAgICAgdGhpcy5mb3JtLkxvYWQgPSBMb2FkDQogICAgICAgICAgICB0aGlzLmZvcm0uTmFtZSA9IE5hbWUNCiAgICAgICAgICAgIHRoaXMuaXNFZGl0SWQgPSBJZA0KICAgICAgICAgICAgdGhpcy5mb3JtLldvcmtzaG9wX05hbWUgPSBXb3Jrc2hvcF9OYW1lDQogICAgICAgICAgICB0aGlzLmZvcm0uV29ya3Nob3BfSWQgPSBXb3Jrc2hvcF9JZA0KICAgICAgICAgICAgdGhpcy5Jc19Xb3Jrc2hvcF9FbmFibGVkID0gSXNfV29ya3Nob3BfRW5hYmxlZA0KICAgICAgICAgICAgdGhpcy5mb3JtLk1vbnRoX0F2Z19Mb2FkID0gTW9udGhfQXZnX0xvYWQNCiAgICAgICAgICAgIHRoaXMuZm9ybS5Tb3J0ID0gU29ydA0KICAgICAgICAgICAgdGhpcy5mb3JtLklzX091dHNvdXJjZSA9IElzX091dHNvdXJjZQ0KICAgICAgICAgICAgdGhpcy5mb3JtLklzX0VuYWJsZWQgPSBJc19FbmFibGVkDQoNCiAgICAgICAgICAgIGlmIChXYXJlaG91c2VfSWQpIHsNCiAgICAgICAgICAgICAgdGhpcy5mb3JtLldhcmVob3VzZV9JZCA9IFdhcmVob3VzZV9JZA0KICAgICAgICAgICAgICB0aGlzLndhcmVDaGFuZ2UoV2FyZWhvdXNlX0lkKQ0KICAgICAgICAgICAgICB0aGlzLmZvcm0uTG9jYXRpb25fSWQgPSBMb2NhdGlvbl9JZA0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgdGhpcy50YWdzID0gVXNlcnMubWFwKCh2KSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJHNldCh2LCAnRGlzcGxheV9OYW1lJywgdi5Vc2VyX05hbWUpDQogICAgICAgICAgICAgIHRoaXMuJHNldCh2LCAnSWQnLCB2LlVzZXJfSWQpDQogICAgICAgICAgICAgIHJldHVybiB2DQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5Jc19Xb3Jrc2hvcF9FbmFibGVkID0gSXNfV29ya3Nob3BfRW5hYmxlZA0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBtYW5hZ2VyQ2hhbmdlKHZhbCkgew0KICAgICAgY29uc3QgaXRlbXMgPSB0aGlzLmNsYXNzTGlzdC5maW5kKChpKSA9PiBpLklkID09PSB2YWwpDQogICAgICBpZiAoaXRlbXMpIHsNCiAgICAgICAgdGhpcy5mb3JtLk1hbmFnZXJfVXNlck5hbWUgPSBpdGVtcy5EaXNwbGF5X05hbWUNCiAgICAgICAgY29uc29sZS5sb2codGhpcy50YWdzLmZpbmQodiA9PiB2LklkICE9PSBpdGVtcy5JZCkpDQogICAgICAgIGNvbnN0IGlkeCA9IHRoaXMudGFncy5maW5kSW5kZXgodiA9PiB2LklkID09PSBpdGVtcy5JZCkNCiAgICAgICAgaWYgKGlkeCA9PT0gLTEpIHsNCiAgICAgICAgICB0aGlzLnRhZ3MucHVzaCh7DQogICAgICAgICAgICBEaXNwbGF5X05hbWU6IGl0ZW1zLkRpc3BsYXlfTmFtZSwNCiAgICAgICAgICAgIElkOiBpdGVtcy5JZCwNCiAgICAgICAgICAgIFVzZXJfSWQ6IGl0ZW1zLklkLA0KICAgICAgICAgICAgVXNlcl9OYW1lOiBpdGVtcy5EaXNwbGF5X05hbWUNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICB3b3Jrc2hvcENoYW5nZSh2YWwpIHsNCiAgICAgIHRoaXMuZm9ybS5Xb3Jrc2hvcF9OYW1lID0gdGhpcy53b3Jrc2hvcExpc3QuZmluZCgNCiAgICAgICAgKGkpID0+IGkuSWQgPT09IHZhbA0KICAgICAgKS5EaXNwbGF5X05hbWUNCiAgICB9LA0KICAgIGdldFRlYW0oKSB7DQogICAgICBHZXRGYWN0b3J5UGVvcGxlbGlzdCgpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMuY2xhc3NMaXN0ID0gcmVzLkRhdGENCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICBHZXRXb3Jrc2hvcFBhZ2VMaXN0KHsgUGFnZTogLTEgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy53b3Jrc2hvcExpc3QgPSByZXMuRGF0YS5EYXRhDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5zaG93RGlhbG9nID0gdHJ1ZQ0KICAgIH0sDQogICAgZ2V0U2VsZWN0TGlzdChsaXN0KSB7DQogICAgICBjb25zdCBvYmpLZXkgPSB7fQ0KICAgICAgdGhpcy50YWdzID0gWy4uLnRoaXMudGFncywgLi4ubGlzdF0ucmVkdWNlKChhY2MsIGN1cikgPT4gew0KICAgICAgICBvYmpLZXlbY3VyLklkXSA/ICcnIDogKG9iaktleVtjdXIuSWRdID0gdHJ1ZSAmJiBhY2MucHVzaChjdXIpKQ0KICAgICAgICByZXR1cm4gYWNjDQogICAgICB9LCBbXSkNCiAgICB9LA0KICAgIGRlbGV0ZVRhZyhpdGVtKSB7DQogICAgICBjb25zdCBpbmRleCA9IHRoaXMudGFncy5maW5kSW5kZXgoKHYpID0+IHYuSWQgPT09IGl0ZW0uSWQpDQogICAgICBpbmRleCAhPT0gLTEgJiYgdGhpcy50YWdzLnNwbGljZShpbmRleCwgMSkNCiAgICB9LA0KICAgIGhhbmRsZVN1Ym1pdCgpIHsNCiAgICAgIC8vIHJldHVybg0KICAgICAgdGhpcy4kcmVmcy5mb3JtLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBpZiAodGhpcy5mb3JtLldhcmVob3VzZV9JZCAmJiAhdGhpcy5mb3JtLkxvY2F0aW9uX0lkKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+ivt+mAieaLqeWFs+iBlOS7k+W6k+eahOW6k+S9jScsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgICAgICB9DQogICAgICAgICAgY29uc3Qgc3ViT2JqID0gew0KICAgICAgICAgICAgTmFtZTogdGhpcy5mb3JtLk5hbWUsDQogICAgICAgICAgICBNYW5hZ2VyX1VzZXJOYW1lOiB0aGlzLmZvcm0uTWFuYWdlcl9Vc2VyTmFtZSwNCiAgICAgICAgICAgIE1hbmFnZXJfVXNlcklkOiB0aGlzLmZvcm0uTWFuYWdlcl9Vc2VySWQsDQogICAgICAgICAgICBMb2FkOiB0aGlzLmZvcm0uTG9hZCwNCiAgICAgICAgICAgIE1lbWJlcnM6IHRoaXMudGFncy5tYXAoKHYpID0+IHYuSWQpLA0KICAgICAgICAgICAgV29ya3Nob3BfSWQ6IHRoaXMuZm9ybS5Xb3Jrc2hvcF9JZCwNCiAgICAgICAgICAgIE1vbnRoX0F2Z19Mb2FkOiB0aGlzLmZvcm0uTW9udGhfQXZnX0xvYWQsDQogICAgICAgICAgICBTb3J0OiB0aGlzLmZvcm0uU29ydCwNCiAgICAgICAgICAgIElzX091dHNvdXJjZTogdGhpcy5mb3JtLklzX091dHNvdXJjZSwNCiAgICAgICAgICAgIElzX0VuYWJsZWQ6IHRoaXMuZm9ybS5Jc19FbmFibGVkLA0KICAgICAgICAgICAgV2FyZWhvdXNlX0lkOiB0aGlzLmZvcm0uV2FyZWhvdXNlX0lkLA0KICAgICAgICAgICAgTG9jYXRpb25fSWQ6IHRoaXMuZm9ybS5Mb2NhdGlvbl9JZA0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLmlzRWRpdCAmJiAoc3ViT2JqLklkID0gdGhpcy5pc0VkaXRJZCkNCiAgICAgICAgICBTYXZlV29ya2luZ1RlYW1zKHN1Yk9iaikudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAn5pON5L2c5oiQ5YqfJywNCiAgICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycNCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgdGhpcy4kZW1pdCgnY2xvc2UnKQ0KICAgICAgICAgICAgICB0aGlzLiRlbWl0KCdyZWZyZXNoJykNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUubG9nKCdlcnJvciBzdWJtaXQhIScpDQogICAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBnZXRXYXJlaG91c2VMaXN0KCkgew0KICAgICAgR2V0VG90YWxXYXJlaG91c2VMaXN0T2ZDdXJGYWN0b3J5KHsgfSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMud2FyZWhvdXNlcyA9IHJlcy5EYXRhDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICB3YXJlQ2hhbmdlKHYpIHsNCiAgICAgIHRoaXMuZm9ybS5Mb2NhdGlvbl9JZCA9ICcnDQogICAgICBHZXRMb2NhdGlvbkxpc3Qoew0KICAgICAgICBXYXJlaG91c2VfSWQ6IHYNCiAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLmxvY2F0aW9ucyA9IHJlcy5EYXRhDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVOdW1iZXJJbnB1dCh2YWx1ZSkgew0KICAgICAgLy8g56e76Zmk5omA5pyJ6Z2e5pWw5a2X5a2X56ymDQogICAgICBsZXQgY2xlYW5lZCA9IHZhbHVlLnJlcGxhY2UoL1teXGRdL2csICcnKQ0KDQogICAgICAvLyDlpITnkIbliY3lr7zpm7bvvJrlpoLmnpzmmK8gMCDlvIDlpLTkuJTmnInlkI7nu63mlbDlrZfvvIznp7vpmaTliY3lr7zpm7YNCiAgICAgIGlmIChjbGVhbmVkLmxlbmd0aCA+IDEgJiYgY2xlYW5lZC5zdGFydHNXaXRoKCcwJykpIHsNCiAgICAgICAgY2xlYW5lZCA9IGNsZWFuZWQucmVwbGFjZSgvXjArLywgJycpDQogICAgICAgIC8vIOWmguaenOWFqOmDqOaYrzDvvIzkv53nlZnkuIDkuKowDQogICAgICAgIGlmIChjbGVhbmVkID09PSAnJykgY2xlYW5lZCA9ICcwJw0KICAgICAgfQ0KDQogICAgICAvLyDmm7TmlrDlgLwNCiAgICAgIHRoaXMuZm9ybS5Tb3J0ID0gY2xlYW5lZA0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/PRO/basic-information/group/component", "sourcesContent": ["<template>\r\n  <div style=\"margin-top: 16px;\">\r\n    <el-form\r\n      ref=\"form\"\r\n      :model=\"form\"\r\n      :rules=\"rules\"\r\n      label-width=\"110px\"\r\n      style=\"width: 100%\"\r\n    >\r\n      <h3>基本信息</h3>\r\n      <el-form-item label=\"班组名称\" prop=\"Name\">\r\n        <el-input v-model=\"form.Name\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"班组长\" prop=\"Manager_UserId\">\r\n        <el-select\r\n          v-model=\"form.Manager_UserId\"\r\n          class=\"w100\"\r\n          clearable\r\n          filterable\r\n          placeholder=\"请选择\"\r\n          @change=\"managerChange\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in classList\"\r\n            :key=\"item.Id\"\r\n            :label=\"item.Display_Name\"\r\n            :value=\"item.Id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"负荷提醒线\" prop=\"Load\">\r\n        <el-input\r\n          v-model.number=\"form.Load\"\r\n          placeholder=\"请输入\"\r\n          class=\"input-number\"\r\n          type=\"number\"\r\n          min=\"0\"\r\n          @blur=\"inputBlur(form.Load)\"\r\n        >\r\n          <template slot=\"append\">吨</template>\r\n        </el-input>\r\n      </el-form-item> -->\r\n      <el-form-item label=\"班组月均负荷\" prop=\"Month_Avg_Load\">\r\n        <el-input\r\n          v-model.number=\"form.Month_Avg_Load\"\r\n          placeholder=\"请输入\"\r\n          class=\"input-number\"\r\n          type=\"number\"\r\n          min=\"0\"\r\n          @blur=\"inputBlur(form.Month_Avg_Load)\"\r\n        >\r\n          <template slot=\"append\">吨</template>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"排序号\" prop=\"Sort\">\r\n        <el-input\r\n          v-model=\"form.Sort\"\r\n          type=\"text\"\r\n          @input=\"handleNumberInput\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"是否外协\" prop=\"Is_Outsource\">\r\n        <el-radio-group v-model=\"form.Is_Outsource\">\r\n          <el-radio :label=\"true\">是</el-radio>\r\n          <el-radio :label=\"false\">否</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item label=\"是否启用\" prop=\"Is_Enabled\">\r\n        <el-radio-group v-model=\"form.Is_Enabled\">\r\n          <el-radio :label=\"true\">是</el-radio>\r\n          <el-radio :label=\"false\">否</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item label=\"关联仓库/库位\">\r\n        <el-select\r\n          ref=\"WarehouseRef\"\r\n          v-model=\"form.Warehouse_Id\"\r\n          clearable\r\n          placeholder=\"请选择仓库\"\r\n          style=\"width: 250px; margin-right: 10px;\"\r\n          @change=\"wareChange\"\r\n        >\r\n          <el-option\r\n            v-for=\"p in warehouses\"\r\n            :key=\"p.Id\"\r\n            :label=\"p.Display_Name\"\r\n            :value=\"p.Id\"\r\n          />\r\n        </el-select>\r\n        <el-select\r\n          ref=\"LocationRef\"\r\n          v-model=\"form.Location_Id\"\r\n          clearable\r\n          placeholder=\"请选择库位\"\r\n          style=\"width: 250px;\"\r\n          :disabled=\"!form.Warehouse_Id\"\r\n        >\r\n          <el-option\r\n            v-for=\"p in locations\"\r\n            :key=\"p.Id\"\r\n            :label=\"p.Display_Name\"\r\n            :value=\"p.Id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item\r\n        v-if=\"Is_Workshop_Enabled\"\r\n        label=\"所属车间\"\r\n        prop=\"Workshop_Id\"\r\n      >\r\n        <el-select\r\n          v-model=\"form.Workshop_Id\"\r\n          class=\"w100\"\r\n          clearable\r\n          filterable\r\n          placeholder=\"请选择\"\r\n          @change=\"workshopChange\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in workshopList\"\r\n            :key=\"item.Id\"\r\n            :label=\"item.Display_Name\"\r\n            :value=\"item.Id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <h3>班组成员</h3>\r\n      <div class=\"tag-x\">\r\n        <div class=\"tag-wrapper\">\r\n          <el-tag\r\n            v-for=\"tag in tags\"\r\n            :key=\"tag.Id\"\r\n            size=\"large\"\r\n            closable\r\n            type=\"info\"\r\n            @close=\"deleteTag(tag)\"\r\n          >\r\n            {{ tag.Display_Name }}\r\n          </el-tag>\r\n        </div>\r\n        <div class=\"add-btn\" @click.stop=\"handleAdd\">\r\n          <el-icon class=\"el-icon-plus\" />\r\n          <span>添加</span>\r\n        </div>\r\n      </div>\r\n    </el-form>\r\n    <footer>\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button type=\"primary\" @click=\"handleSubmit\">确 定</el-button>\r\n    </footer>\r\n    <add-user\r\n      v-if=\"showDialog\"\r\n      :tags=\"tags\"\r\n      :show.sync=\"showDialog\"\r\n      @selectList=\"getSelectList\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport AddUser from './AddUser'\r\nimport {\r\n  GetWorkingTeamInfo,\r\n  SaveWorkingTeams,\r\n  GetFactoryPeoplelist\r\n} from '@/api/PRO/technology-lib'\r\nimport { GetWorkshopPageList } from '@/api/PRO/basic-information/workshop'\r\nimport { GetLocationList, GetTotalWarehouseListOfCurFactory } from '@/api/PRO/pro-stock'\r\n\r\nexport default {\r\n  components: {\r\n    AddUser\r\n  },\r\n  data() {\r\n    return {\r\n      showDialog: false,\r\n      tags: [],\r\n      form: {\r\n        Name: '',\r\n        Manager_UserName: '',\r\n        Manager_UserId: '',\r\n        Load: '',\r\n        Workshop_Name: '', // 所属车间\r\n        Workshop_Id: '', // 所属车间Id\r\n        Month_Avg_Load: null,\r\n        Sort: '0',\r\n        Is_Outsource: false,\r\n        Is_Enabled: true,\r\n        Warehouse_Id: '',\r\n        Location_Id: ''\r\n      },\r\n      rules: {\r\n        Name: [{ required: true, message: '请输入', trigger: 'blur' }],\r\n        Sort: [{ required: true, message: '请输入', trigger: 'blur' }]\r\n      },\r\n      classList: [],\r\n      workshopList: [],\r\n      warehouses: [],\r\n      locations: [],\r\n\r\n      Is_Workshop_Enabled: ''\r\n    }\r\n  },\r\n  created() {},\r\n  mounted() {\r\n    this.getTeam()\r\n    this.getWarehouseList()\r\n  },\r\n  methods: {\r\n    // 输入框校验\r\n    inputBlur(e) {\r\n      if (e < 0) {\r\n        this.form.Load = 0\r\n      }\r\n    },\r\n    // 数据初始化\r\n    initData(id, Is_Workshop_Enabled) {\r\n      if (id) {\r\n        this.isEdit = true\r\n        GetWorkingTeamInfo({\r\n          id\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            const {\r\n              Manager_UserName,\r\n              Manager_UserId,\r\n              Load,\r\n              Name,\r\n              Users,\r\n              Id,\r\n              Workshop_Name,\r\n              Workshop_Id,\r\n              Month_Avg_Load,\r\n              Sort,\r\n              Is_Outsource,\r\n              Is_Enabled,\r\n              Warehouse_Id,\r\n              Location_Id\r\n            } = res.Data\r\n            this.form.Manager_UserName = Manager_UserName\r\n            this.form.Manager_UserId = Manager_UserId\r\n            this.form.Load = Load\r\n            this.form.Name = Name\r\n            this.isEditId = Id\r\n            this.form.Workshop_Name = Workshop_Name\r\n            this.form.Workshop_Id = Workshop_Id\r\n            this.Is_Workshop_Enabled = Is_Workshop_Enabled\r\n            this.form.Month_Avg_Load = Month_Avg_Load\r\n            this.form.Sort = Sort\r\n            this.form.Is_Outsource = Is_Outsource\r\n            this.form.Is_Enabled = Is_Enabled\r\n\r\n            if (Warehouse_Id) {\r\n              this.form.Warehouse_Id = Warehouse_Id\r\n              this.wareChange(Warehouse_Id)\r\n              this.form.Location_Id = Location_Id\r\n            }\r\n            this.tags = Users.map((v) => {\r\n              this.$set(v, 'Display_Name', v.User_Name)\r\n              this.$set(v, 'Id', v.User_Id)\r\n              return v\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      } else {\r\n        this.Is_Workshop_Enabled = Is_Workshop_Enabled\r\n      }\r\n    },\r\n\r\n    managerChange(val) {\r\n      const items = this.classList.find((i) => i.Id === val)\r\n      if (items) {\r\n        this.form.Manager_UserName = items.Display_Name\r\n        console.log(this.tags.find(v => v.Id !== items.Id))\r\n        const idx = this.tags.findIndex(v => v.Id === items.Id)\r\n        if (idx === -1) {\r\n          this.tags.push({\r\n            Display_Name: items.Display_Name,\r\n            Id: items.Id,\r\n            User_Id: items.Id,\r\n            User_Name: items.Display_Name\r\n          })\r\n        }\r\n      }\r\n    },\r\n    workshopChange(val) {\r\n      this.form.Workshop_Name = this.workshopList.find(\r\n        (i) => i.Id === val\r\n      ).Display_Name\r\n    },\r\n    getTeam() {\r\n      GetFactoryPeoplelist().then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.classList = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      GetWorkshopPageList({ Page: -1 }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.workshopList = res.Data.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleAdd() {\r\n      this.showDialog = true\r\n    },\r\n    getSelectList(list) {\r\n      const objKey = {}\r\n      this.tags = [...this.tags, ...list].reduce((acc, cur) => {\r\n        objKey[cur.Id] ? '' : (objKey[cur.Id] = true && acc.push(cur))\r\n        return acc\r\n      }, [])\r\n    },\r\n    deleteTag(item) {\r\n      const index = this.tags.findIndex((v) => v.Id === item.Id)\r\n      index !== -1 && this.tags.splice(index, 1)\r\n    },\r\n    handleSubmit() {\r\n      // return\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.Warehouse_Id && !this.form.Location_Id) {\r\n            this.$message({\r\n              message: '请选择关联仓库的库位',\r\n              type: 'error'\r\n            })\r\n            return false\r\n          }\r\n          const subObj = {\r\n            Name: this.form.Name,\r\n            Manager_UserName: this.form.Manager_UserName,\r\n            Manager_UserId: this.form.Manager_UserId,\r\n            Load: this.form.Load,\r\n            Members: this.tags.map((v) => v.Id),\r\n            Workshop_Id: this.form.Workshop_Id,\r\n            Month_Avg_Load: this.form.Month_Avg_Load,\r\n            Sort: this.form.Sort,\r\n            Is_Outsource: this.form.Is_Outsource,\r\n            Is_Enabled: this.form.Is_Enabled,\r\n            Warehouse_Id: this.form.Warehouse_Id,\r\n            Location_Id: this.form.Location_Id\r\n          }\r\n          this.isEdit && (subObj.Id = this.isEditId)\r\n          SaveWorkingTeams(subObj).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                message: '操作成功',\r\n                type: 'success'\r\n              })\r\n              this.$emit('close')\r\n              this.$emit('refresh')\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    getWarehouseList() {\r\n      GetTotalWarehouseListOfCurFactory({ }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.warehouses = res.Data\r\n        }\r\n      })\r\n    },\r\n    wareChange(v) {\r\n      this.form.Location_Id = ''\r\n      GetLocationList({\r\n        Warehouse_Id: v\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.locations = res.Data\r\n        }\r\n      })\r\n    },\r\n    handleNumberInput(value) {\r\n      // 移除所有非数字字符\r\n      let cleaned = value.replace(/[^\\d]/g, '')\r\n\r\n      // 处理前导零：如果是 0 开头且有后续数字，移除前导零\r\n      if (cleaned.length > 1 && cleaned.startsWith('0')) {\r\n        cleaned = cleaned.replace(/^0+/, '')\r\n        // 如果全部是0，保留一个0\r\n        if (cleaned === '') cleaned = '0'\r\n      }\r\n\r\n      // 更新值\r\n      this.form.Sort = cleaned\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"~@/styles/mixin.scss\";\r\n\r\nh3 {\r\n  color: #298dff;\r\n}\r\n\r\n.tag-x {\r\n  text-align: left;\r\n\r\n  .tag-wrapper {\r\n    display: inline-block;\r\n    flex-wrap: wrap;\r\n    max-height: 160px;\r\n    overflow: auto;\r\n    @include scrollBar;\r\n\r\n    .el-tag {\r\n      margin: 8px 0 0 8px;\r\n    }\r\n  }\r\n\r\n  .add-btn {\r\n    margin-top: 12px;\r\n    text-align: center;\r\n    cursor: pointer;\r\n    height: 32px;\r\n    line-height: 32px;\r\n    background: rgba(41, 141, 255, 0.03);\r\n    color: #298dff;\r\n    border: 1px dashed rgba(41, 141, 255, 0.32156862745098036);\r\n    border-radius: 4px;\r\n  }\r\n}\r\n\r\nfooter {\r\n  margin: 20px;\r\n  text-align: right;\r\n\r\n  &:first-child {\r\n    margin-right: 20px;\r\n  }\r\n}\r\n\r\n::v-deep {\r\n  .input-number {\r\n    input {\r\n      padding-right: 2px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
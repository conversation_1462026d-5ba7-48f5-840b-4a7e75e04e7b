{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\nesting-management\\components\\NestResult.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\nesting-management\\components\\NestResult.vue", "mtime": 1757468127975}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["combineURL", "GetPlateNestingResultImportFile", "ImportPlateNestingResult", "UploadExcel", "components", "data", "btnLoading", "methods", "beforeUpload", "file", "_this", "fileFormData", "FormData", "append", "then", "res", "IsSucceed", "$message", "message", "type", "$emit", "Message", "Data", "window", "open", "$baseUrl", "handleSubmit", "$refs", "upload", "handleExport", "_this2"], "sources": ["src/views/PRO/nesting-management/components/NestResult.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-alert\r\n      type=\"warning\"\r\n      :closable=\"false\"\r\n    >\r\n      <template>\r\n        <span class=\"cs-label\">注意：请先 <el-link\r\n          type=\"primary\"\r\n          :underline=\"false\"\r\n          @click=\"handleExport\"\r\n        >点击下载模板</el-link></span>\r\n      </template>\r\n    </el-alert>\r\n\r\n    <div class=\"cs-upload-x\">\r\n      <upload-excel ref=\"upload\" :before-upload=\"beforeUpload\" />\r\n    </div>\r\n    <div slot=\"footer\" class=\"dialog-footer\" style=\"text-align: right;\">\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button\r\n        type=\"primary\"\r\n        :loading=\"btnLoading\"\r\n        @click=\"handleSubmit\"\r\n      >确 定\r\n      </el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { combineURL } from '@/utils'\r\nimport { GetPlateNestingResultImportFile, ImportPlateNestingResult } from '@/api/PRO/production-task'\r\nimport UploadExcel from '@/components/UploadExcel/index.vue'\r\n\r\nexport default {\r\n  components: { UploadExcel },\r\n  data() {\r\n    return {\r\n      btnLoading: false\r\n    }\r\n  },\r\n  methods: {\r\n    beforeUpload(file) {\r\n      this.btnLoading = true\r\n      const fileFormData = new FormData()\r\n      fileFormData.append('Files', file)\r\n      ImportPlateNestingResult(fileFormData).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '导入成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('close')\r\n          this.$emit('refresh')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          res.Data && window.open(combineURL(this.$baseUrl, res.Data))\r\n        }\r\n        this.btnLoading = false\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      this.$refs.upload.handleSubmit()\r\n    },\r\n    handleExport() {\r\n      GetPlateNestingResultImportFile({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data))\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.cs-label {\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n  white-space: pre-wrap;\r\n}\r\n\r\n.cs-upload-x {\r\n  margin: 32px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  .c-upload-container{\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,SAAAA,UAAA;AACA,SAAAC,+BAAA,EAAAC,wBAAA;AACA,OAAAC,WAAA;AAEA;EACAC,UAAA;IAAAD,WAAA,EAAAA;EAAA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;IACA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAAC,IAAA;MAAA,IAAAC,KAAA;MACA,KAAAJ,UAAA;MACA,IAAAK,YAAA,OAAAC,QAAA;MACAD,YAAA,CAAAE,MAAA,UAAAJ,IAAA;MACAP,wBAAA,CAAAS,YAAA,EAAAG,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAN,KAAA,CAAAO,QAAA;YACAC,OAAA;YACAC,IAAA;UACA;UACAT,KAAA,CAAAU,KAAA;UACAV,KAAA,CAAAU,KAAA;QACA;UACAV,KAAA,CAAAO,QAAA;YACAC,OAAA,EAAAH,GAAA,CAAAM,OAAA;YACAF,IAAA;UACA;UACAJ,GAAA,CAAAO,IAAA,IAAAC,MAAA,CAAAC,IAAA,CAAAxB,UAAA,CAAAU,KAAA,CAAAe,QAAA,EAAAV,GAAA,CAAAO,IAAA;QACA;QACAZ,KAAA,CAAAJ,UAAA;MACA;IACA;IACAoB,YAAA,WAAAA,aAAA;MACA,KAAAC,KAAA,CAAAC,MAAA,CAAAF,YAAA;IACA;IACAG,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA7B,+BAAA,KAAAa,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAO,MAAA,CAAAC,IAAA,CAAAxB,UAAA,CAAA8B,MAAA,CAAAL,QAAA,EAAAV,GAAA,CAAAO,IAAA;QACA;UACAQ,MAAA,CAAAb,QAAA;YACAC,OAAA,EAAAH,GAAA,CAAAM,OAAA;YACAF,IAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
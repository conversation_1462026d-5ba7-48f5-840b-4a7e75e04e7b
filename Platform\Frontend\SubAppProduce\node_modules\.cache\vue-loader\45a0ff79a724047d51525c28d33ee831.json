{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\section-list\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\section-list\\index.vue", "mtime": 1758266753128}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBEZWxldGVwYXJ0LA0KICBFeHBvcnRQbGFucGFydEluZm8sDQogIEV4cG9ydFBsYW5wYXJ0Y291bnRJbmZvLA0KICBEZWxldGVwYXJ0QnlmaW5ka2V5d29kZXMNCn0gZnJvbSAnQC9hcGkvcGxtL3Byb2R1Y3Rpb24nDQppbXBvcnQgeyBHZXRTdG9wTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9wcm9kdWN0aW9uLXRhc2snDQppbXBvcnQgew0KICBHZXRVbml0UGFnZUxpc3QsDQogIEdldFVuaXRXZWlnaHRMaXN0LA0KICBFeHBvcnRQbGFuVW5pdEluZm8NCn0gZnJvbSAnQC9hcGkvcGxtL3NlY3Rpb24nDQppbXBvcnQgeyBHZXRHcmlkQnlDb2RlIH0gZnJvbSAnQC9hcGkvc3lzJw0KaW1wb3J0IHsgR2V0RmFjdG9yeVByb2Zlc3Npb25hbEJ5Q29kZSB9IGZyb20gJ0AvYXBpL1BSTy9wcm9mZXNzaW9uYWxUeXBlJw0KaW1wb3J0IHsNCiAgR2V0UHJvamVjdEFyZWFUcmVlTGlzdCwNCiAgR2V0SW5zdGFsbFVuaXRJZE5hbWVMaXN0DQp9IGZyb20gJ0AvYXBpL1BSTy9wcm9qZWN0Jw0KDQppbXBvcnQgVHJlZURldGFpbCBmcm9tICdAL2NvbXBvbmVudHMvVHJlZURldGFpbCcNCmltcG9ydCBUb3BIZWFkZXIgZnJvbSAnQC9jb21wb25lbnRzL1RvcEhlYWRlcicNCmltcG9ydCBjb21JbXBvcnQgZnJvbSAnLi9jb21wb25lbnQvSW1wb3J0Jw0KaW1wb3J0IENvbXBvbmVudHNIaXN0b3J5IGZyb20gJy4vY29tcG9uZW50L0NvbXBvbmVudHNIaXN0b3J5Jw0KaW1wb3J0IGNvbUltcG9ydEJ5RmFjdG9yeSBmcm9tICcuL2NvbXBvbmVudC9JbXBvcnRCeUZhY3RvcnknDQppbXBvcnQgSGlzdG9yeUV4cG9ydCBmcm9tICcuL2NvbXBvbmVudC9IaXN0b3J5RXhwb3J0Jw0KaW1wb3J0IEJhdGNoRWRpdCBmcm9tICcuL2NvbXBvbmVudC9CYXRjaEVkaXRvcicNCmltcG9ydCBDb21wb25lbnRQYWNrIGZyb20gJy4vY29tcG9uZW50L0NvbXBvbmVudFBhY2svaW5kZXgnDQppbXBvcnQgRWRpdCBmcm9tICcuL2NvbXBvbmVudC9FZGl0Jw0KaW1wb3J0IE9uZUNsaWNrR2VuZXJhdGVQYWNrIGZyb20gJy4vY29tcG9uZW50L09uZUNsaWNrR2VuZXJhdGVQYWNrJw0KaW1wb3J0IEdlbmVyYXRlUGFjayBmcm9tICcuL2NvbXBvbmVudC9HZW5lcmF0ZVBhY2snDQppbXBvcnQgRGVlcE1hdGVyaWFsIGZyb20gJy4vY29tcG9uZW50L0RlZXBNYXRlcmlhbCcNCmltcG9ydCBTY2hkdWxpbmcgZnJvbSAnLi9jb21wb25lbnQvU2NoZHVsaW5nJw0KaW1wb3J0IFBhcnRMaXN0IGZyb20gJy4vY29tcG9uZW50L1BhcnRMaXN0LnZ1ZScNCmltcG9ydCBQcm9jZXNzRGF0YSBmcm9tICcuL2NvbXBvbmVudC9Qcm9jZXNzRGF0YS52dWUnDQoNCmltcG9ydCBlbERyYWdEaWFsb2cgZnJvbSAnQC9kaXJlY3RpdmUvZWwtZHJhZy1kaWFsb2cnDQppbXBvcnQgUGFnaW5hdGlvbiBmcm9tICdAL2NvbXBvbmVudHMvUGFnaW5hdGlvbicNCmltcG9ydCB7IHRpbWVGb3JtYXQgfSBmcm9tICdAL2ZpbHRlcnMnDQovLyBpbXBvcnQgeyBDb2x1bW4sIEhlYWRlciwgVGFibGUsIFRvb2x0aXAgfSBmcm9tICd2eGUtdGFibGUnDQovLyBpbXBvcnQgVnVlIGZyb20gJ3Z1ZScNCmltcG9ydCBBdXRoQnV0dG9ucyBmcm9tICdAL21peGlucy9hdXRoLWJ1dHRvbnMnDQppbXBvcnQgYmltZGlhbG9nIGZyb20gJy4vY29tcG9uZW50L2JpbWRpYWxvZycNCmltcG9ydCBzeXNVc2VUeXBlIGZyb20gJ0AvZGlyZWN0aXZlL3N5cy11c2UtdHlwZS9pbmRleC5qcycNCmltcG9ydCB7IHByb21wdEJveCB9IGZyb20gJy4vY29tcG9uZW50L21lc3NhZ2VCb3gnDQoNCmltcG9ydCB7IGNvbWJpbmVVUkwgfSBmcm9tICdAL3V0aWxzJw0KaW1wb3J0IHsgdGFibGVQYWdlU2l6ZSB9IGZyb20gJ0Avdmlld3MvUFJPL3NldHRpbmcnDQppbXBvcnQgeyBwYXJzZU9zc1VybCB9IGZyb20gJ0AvdXRpbHMvZmlsZScNCg0KaW1wb3J0IHsgYmFzZVVybCB9IGZyb20gJ0AvdXRpbHMvYmFzZXVybCcNCmltcG9ydCB7IHY0IGFzIHV1aWR2NCB9IGZyb20gJ3V1aWQnDQppbXBvcnQgeyBHZXRTdGVlbENhZEFuZEJpbUlkIH0gZnJvbSAnQC9hcGkvUFJPL2NvbXBvbmVudCcNCmltcG9ydCB7IGdldENvbmZpZ3VyZSB9IGZyb20gJ0AvYXBpL3VzZXInDQppbXBvcnQgeyBHZXRGaWxlVHlwZSB9IGZyb20gJ0AvYXBpL3N5cycNCmltcG9ydCBFeHBhbmRhYmxlU2VjdGlvbiBmcm9tICdAL2NvbXBvbmVudHMvRXhwYW5kYWJsZVNlY3Rpb24vaW5kZXgudnVlJw0KaW1wb3J0IGNvbURyYXdkaWFsb2cgZnJvbSAnQC92aWV3cy9QUk8vcHJvZHVjdGlvbi1vcmRlci9kZWVwZW4tZmlsZXMvZGlhbG9nJyAvLyDmt7HljJbmlofku7Yt6Zu25Lu26K+m5Zu+5a+85YWlDQppbXBvcnQgVHJhY2VQbG90IGZyb20gJy4vY29tcG9uZW50L1RyYWNlUGxvdCcNCg0KaW1wb3J0IG1vZGVsRHJhd2luZyBmcm9tICdAL3ZpZXdzL1BSTy9jb21wb25lbnRzL21vZGVsRHJhd2luZy52dWUnDQppbXBvcnQgeyBHZXRCT01JbmZvIH0gZnJvbSAnQC92aWV3cy9QUk8vYm9tLXNldHRpbmcvdXRpbHMnDQoNCi8vIFZ1ZS51c2UoSGVhZGVyKS51c2UoQ29sdW1uKS51c2UoVG9vbHRpcCkudXNlKFRhYmxlKQ0KY29uc3QgU1BMSVRfU1lNQk9MID0gJyRfJCcNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ1BST1NlY3Rpb25MaXN0JywNCiAgZGlyZWN0aXZlczogeyBlbERyYWdEaWFsb2csIHN5c1VzZVR5cGUgfSwNCiAgY29tcG9uZW50czogew0KICAgIEV4cGFuZGFibGVTZWN0aW9uLA0KICAgIFRyZWVEZXRhaWwsDQogICAgVG9wSGVhZGVyLA0KICAgIGNvbUltcG9ydCwNCiAgICBjb21JbXBvcnRCeUZhY3RvcnksDQogICAgQmF0Y2hFZGl0LA0KICAgIEhpc3RvcnlFeHBvcnQsDQogICAgR2VuZXJhdGVQYWNrLA0KICAgIEVkaXQsDQogICAgQ29tcG9uZW50UGFjaywNCiAgICBPbmVDbGlja0dlbmVyYXRlUGFjaywNCiAgICBQYWdpbmF0aW9uLA0KICAgIGJpbWRpYWxvZywNCiAgICBDb21wb25lbnRzSGlzdG9yeSwNCiAgICBEZWVwTWF0ZXJpYWwsDQogICAgU2NoZHVsaW5nLA0KICAgIGNvbURyYXdkaWFsb2csDQogICAgVHJhY2VQbG90LA0KICAgIFBhcnRMaXN0LA0KICAgIG1vZGVsRHJhd2luZywNCiAgICBQcm9jZXNzRGF0YQ0KICB9LA0KICBtaXhpbnM6IFtBdXRoQnV0dG9uc10sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGFsbFN0b3BGbGFnOiBmYWxzZSwNCiAgICAgIHNob3dFeHBhbmQ6IHRydWUsDQogICAgICBkcmF3ZXI6IGZhbHNlLA0KICAgICAgZHJhd2Vyc3VsbDogZmFsc2UsDQogICAgICBpZnJhbWVLZXk6ICcnLA0KICAgICAgZnVsbHNjcmVlbmlkOiAnJywNCiAgICAgIGlmcmFtZVVybDogJycsDQogICAgICBmdWxsYmltaWQ6ICcnLA0KICAgICAgZXhwYW5kZWRLZXk6ICcnLCAvLyAtMeaYr+WFqOmDqA0KICAgICAgdGFibGVQYWdlU2l6ZTogdGFibGVQYWdlU2l6ZSwNCiAgICAgIHBhcnRUeXBlT3B0aW9uOiBbXSwNCiAgICAgIGRpcmVjdE9wdGlvbjogWw0KICAgICAgICB7IHZhbHVlOiB0cnVlLCBsYWJlbDogJ+aYrycgfSwNCiAgICAgICAgeyB2YWx1ZTogZmFsc2UsIGxhYmVsOiAn5ZCmJyB9DQogICAgICBdLA0KICAgICAgdHJlZURhdGE6IFtdLA0KICAgICAgdHJlZUxvYWRpbmc6IHRydWUsDQogICAgICBwcm9qZWN0TmFtZTogJycsDQogICAgICBzdGF0dXNUeXBlOiAnJywNCiAgICAgIHNlYXJjaEhlaWdodDogMCwNCiAgICAgIHRiRGF0YTogW10sDQogICAgICB0b3RhbDogMCwNCiAgICAgIHRiTG9hZGluZzogZmFsc2UsDQogICAgICBwZ0xvYWRpbmc6IGZhbHNlLA0KICAgICAgY291bnRMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHF1ZXJ5SW5mbzogew0KICAgICAgICBQYWdlOiAxLA0KICAgICAgICBQYWdlU2l6ZTogMTAsDQogICAgICAgIFBhcmFtZXRlckpzb246IFtdDQogICAgICB9LA0KICAgICAgY3VzdG9tUGFnZVNpemU6IFsxMCwgMjAsIDUwLCAxMDBdLA0KICAgICAgaW5zdGFsbFVuaXRJZE5hbWVMaXN0OiBbXSwgLy8g5om55qyh5pWw57uEDQogICAgICBuYW1lTW9kZTogMSwNCg0KICAgICAgY3VzdG9tUGFyYW1zOiB7DQogICAgICAgIFR5cGVJZDogJycsDQogICAgICAgIFR5cGVfTmFtZTogJycsDQogICAgICAgIENvZGU6ICcnLA0KICAgICAgICBDb2RlX0xpa2U6ICcnLA0KICAgICAgICBTcGVjOiAnJywNCiAgICAgICAgRGF0ZU5hbWU6ICcnLA0KICAgICAgICBUZXh0dXJlOiAnJywNCiAgICAgICAgLy8gS2V5d29yZHMwMTogJ0NvZGUnLA0KICAgICAgICAvLyBLZXl3b3JkczAxVmFsdWU6ICcnLA0KICAgICAgICAvLyBLZXl3b3JkczAyOiAnU3BlYycsDQogICAgICAgIC8vIEtleXdvcmRzMDJWYWx1ZTogJycsDQogICAgICAgIC8vIEtleXdvcmRzMDM6ICdMZW5ndGgnLA0KICAgICAgICAvLyBLZXl3b3JkczAzVmFsdWU6ICcnLA0KICAgICAgICAvLyBLZXl3b3JkczA0OiAnVGV4dHVyZScsDQogICAgICAgIC8vIEtleXdvcmRzMDRWYWx1ZTogJycsDQogICAgICAgIEluc3RhbGxVbml0X0lkOiBbXSwNCiAgICAgICAgSXNEaXJlY3Q6IG51bGwsIC8vIOaYr+WQpuebtOWPkQ0KICAgICAgICBQYXJ0X1R5cGVfSWQ6ICcnLA0KICAgICAgICBJbnN0YWxsVW5pdF9OYW1lOiAnJywNCiAgICAgICAgU3lzX1Byb2plY3RfSWQ6ICcnLA0KICAgICAgICBQcm9qZWN0X0lkOiAnJywNCiAgICAgICAgQXJlYV9JZDogJycsDQogICAgICAgIFByb2plY3RfTmFtZTogJycsDQogICAgICAgIEFyZWFfTmFtZTogJycNCiAgICAgIH0sDQogICAgICBuYW1lczogJycsDQogICAgICBjdXN0b21EaWFsb2dQYXJhbXM6IHt9LA0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBjdXJyZW50Q29tcG9uZW50OiAnJywNCiAgICAgIHNlbGVjdExpc3Q6IFtdLA0KICAgICAgZmFjdG9yeU9wdGlvbjogW10sDQogICAgICBwcm9qZWN0TGlzdDogW10sDQogICAgICB0eXBlT3B0aW9uOiBbXSwNCiAgICAgIGNvbHVtbnM6IFtdLA0KICAgICAgY29sdW1uc09wdGlvbjogWw0KICAgICAgICAvLyB7IERpc3BsYXlfTmFtZTogJ+mbtuS7tuWQjeensCcsIENvZGU6ICdDb2RlJyB9LA0KICAgICAgICAvLyB7IERpc3BsYXlfTmFtZTogJ+inhOagvCcsIENvZGU6ICdTcGVjJyB9LA0KICAgICAgICAvLyB7IERpc3BsYXlfTmFtZTogJ+mVv+W6picsIENvZGU6ICdMZW5ndGgnIH0sDQogICAgICAgIC8vIHsgRGlzcGxheV9OYW1lOiAn5p2Q6LSoJywgQ29kZTogJ1RleHR1cmUnIH0sDQogICAgICAgIC8vIHsgRGlzcGxheV9OYW1lOiAn5rex5YyW5pWw6YePJywgQ29kZTogJ051bScgfSwNCiAgICAgICAgLy8geyBEaXNwbGF5X05hbWU6ICfmjpLkuqfmlbDph48nLCBDb2RlOiAnU2NoZHVsaW5nX0NvdW50JyB9LA0KICAgICAgICAvLyB7IERpc3BsYXlfTmFtZTogJ+WNlemHjScsIENvZGU6ICdXZWlnaHQnIH0sDQogICAgICAgIC8vIHsgRGlzcGxheV9OYW1lOiAn5oC76YeNJywgQ29kZTogJ1RvdGFsX1dlaWdodCcgfSwNCiAgICAgICAgLy8geyBEaXNwbGF5X05hbWU6ICflvaLnirYnLCBDb2RlOiAnU2hhcGUnIH0sDQogICAgICAgIC8vIHsgRGlzcGxheV9OYW1lOiAn5p6E5Lu25ZCN56ewJywgQ29kZTogJ0NvbXBvbmVudF9Db2RlJyB9LA0KICAgICAgICAvLyB7IERpc3BsYXlfTmFtZTogJ+aTjeS9nOS6uicsIENvZGU6ICdkYXRlbmFtZScgfSwNCiAgICAgICAgLy8geyBEaXNwbGF5X05hbWU6ICfmk43kvZzml7bpl7QnLCBDb2RlOiAnRXhkYXRlJyB9DQogICAgICBdLA0KICAgICAgdGl0bGU6ICcnLA0KICAgICAgd2lkdGg6ICc2MCUnLA0KICAgICAgdGlwTGFiZWw6ICcnLA0KICAgICAgbW9ub21lckxpc3Q6IFtdLA0KICAgICAgbW9kZTogJycsDQogICAgICBpc01vbm9tZXI6IHRydWUsDQogICAgICBoaXN0b3J5VmlzaWJsZTogZmFsc2UsDQogICAgICBzeXNVc2VUeXBlOiB1bmRlZmluZWQsDQogICAgICBkZWxldGVDb250ZW50OiB0cnVlLA0KICAgICAgU3RlZWxBbW91bnRUb3RhbDogMCwgLy8g5rex5YyW5oC76YePDQogICAgICBTY2hlZHVsaW5nTnVtVG90YWw6IDAsIC8vIOaOkuS6p+aAu+mHjw0KICAgICAgU3RlZWxBbGxXZWlnaHRUb3RhbDogMCwgLy8g5rex5YyW5oC76YeNDQogICAgICBTY2hlZHVsaW5nQWxsV2VpZ2h0VG90YWw6IDAsIC8vIOaOkuS6p+aAu+mHjQ0KICAgICAgRmluaXNoQ291bnRUb3RhbDogMCwgLy8g5a6M5oiQ5pWw6YePDQogICAgICBGaW5pc2hXZWlnaHRUb3RhbDogMCwgLy8g5a6M5oiQ6YeN6YePDQogICAgICBEaXJlY3RDb3VudFRvdGFsOiAwLCAvLyDnm7Tlj5Hku7bmgLvmlbANCiAgICAgIERpcmVjdFdlaWdodFRvdGFsOiAwLCAvLyDnm7Tlj5Hku7bmgLvph48NCiAgICAgIFVuaXQ6ICcnLA0KICAgICAgZmlsZUJpbTogJycsDQogICAgICBQcm9wb3J0aW9uOiAwLCAvLyDkuJPkuJrnmoTljZXkvY3mjaLnrpcNCiAgICAgIGNvbW1hbmQ6ICdjb3ZlcicsDQogICAgICBjdXJyZW50TGFzdExldmVsOiBmYWxzZSwNCiAgICAgIHRlbXBsYXRlVXJsOiAnJywNCiAgICAgIGN1cnJlbnROb2RlOiB7fSwNCiAgICAgIGNvbURyYXdEYXRhOiB7fSwNCiAgICAgIHRyYWNrRHJhd2VyOiBmYWxzZSwNCiAgICAgIHRyYWNrRHJhd2VyVGl0bGU6ICcnLA0KICAgICAgdHJhY2tEcmF3ZXJEYXRhOiB7fSwNCiAgICAgIGRyYXdpbmdBY3RpdmU6ICcnLA0KICAgICAgZHJhd2luZ0RhdGFMaXN0OiBbXSwNCiAgICAgIGxldmVsTmFtZTogJycsDQogICAgICBsZXZlbENvZGU6ICcnDQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIHNob3dQOUJ0bigpIHsNCiAgICAgIHJldHVybiB0aGlzLkF1dGhCdXR0b25zLmJ1dHRvbnMuc29tZSgoaXRlbSkgPT4gaXRlbS5Db2RlID09PSAncDlCdG5BZGQnKQ0KICAgIH0sDQogICAgdHlwZUVudGl0eSgpIHsNCiAgICAgIHJldHVybiB0aGlzLnR5cGVPcHRpb24uZmluZCgoaSkgPT4gaS5JZCA9PT0gdGhpcy5jdXN0b21QYXJhbXMuVHlwZUlkKQ0KICAgIH0sDQogICAgUElEKCkgew0KICAgICAgcmV0dXJuIHRoaXMucHJvamVjdExpc3QuZmluZCgNCiAgICAgICAgKGkpID0+IGkuU3lzX1Byb2plY3RfSWQgPT09IHRoaXMuY3VzdG9tUGFyYW1zLlByb2plY3RfSWQNCiAgICAgICk/LklkDQogICAgfSwNCiAgICBmaWx0ZXJUZXh0KCkgew0KICAgICAgcmV0dXJuIHRoaXMucHJvamVjdE5hbWUgKyBTUExJVF9TWU1CT0wgKyB0aGlzLnN0YXR1c1R5cGUNCiAgICB9DQogIH0sDQogIHdhdGNoOiB7DQogICAgJ2N1c3RvbVBhcmFtcy5UeXBlSWQnOiBmdW5jdGlvbihuZXdWYWx1ZSwgb2xkVmFsdWUpIHsNCiAgICAgIGNvbnNvbGUubG9nKHsgb2xkVmFsdWUgfSkNCiAgICAgIGlmIChvbGRWYWx1ZSAmJiBvbGRWYWx1ZSAhPT0gJzAnKSB7DQogICAgICAgIHRoaXMuZmV0Y2hEYXRhKCkNCiAgICAgIH0NCiAgICB9LA0KICAgIG5hbWVzKG4sIG8pIHsNCiAgICAgIHRoaXMuY2hhbmdlTW9kZSgpDQogICAgfSwNCiAgICBuYW1lTW9kZShuLCBvKSB7DQogICAgICB0aGlzLmNoYW5nZU1vZGUoKQ0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLnBnTG9hZGluZyA9IHRydWUNCiAgICBjb25zb2xlLmxvZyh0aGlzLmNvbHVtbnMpDQogICAgLy8gdGhpcy5nZXRVbml0V2VpZ2h0TGlzdCgpDQogICAgdGhpcy5zZWFyY2hIZWlnaHQgPSB0aGlzLiRyZWZzLnNlYXJjaERvbS5vZmZzZXRIZWlnaHQgKyAzMjcNCiAgfSwNCiAgYXN5bmMgY3JlYXRlZCgpIHsNCiAgICBjb25zdCBsZXZlbCA9ICt0aGlzLiRyb3V0ZS5xdWVyeS5sZXZlbA0KICAgIGNvbnN0IHsgY3VycmVudEJPTUluZm8gfSA9IGF3YWl0IEdldEJPTUluZm8obGV2ZWwpDQogICAgY29uc29sZS5sb2coJ2xpc3QnLCBjdXJyZW50Qk9NSW5mbykNCiAgICB0aGlzLmxldmVsTmFtZSA9IGN1cnJlbnRCT01JbmZvPy5EaXNwbGF5X05hbWUNCiAgICB0aGlzLmxldmVsQ29kZSA9IGN1cnJlbnRCT01JbmZvPy5Db2RlDQogICAgYXdhaXQgdGhpcy5nZXRUeXBlTGlzdCgpDQogICAgLy8gYXdhaXQgdGhpcy5mZXRjaERhdGEoKQ0KICAgIHRoaXMuZmV0Y2hUcmVlRGF0YSgpDQogICAgdGhpcy5nZXRGaWxlVHlwZSgpDQogICAgaWYgKHRoaXMuS2V5d29yZHMwMVZhbHVlID09PSAn5pivJykgew0KICAgICAgY29uc29sZS5sb2coJ3RoaXMuS2V5d29yZHMwMVZhbHVlJywgdGhpcy5LZXl3b3JkczAxVmFsdWUpDQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgY2hhbmdlTW9kZSgpIHsNCiAgICAgIGlmICh0aGlzLm5hbWVNb2RlID09PSAxKSB7DQogICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLkNvZGVfTGlrZSA9IHRoaXMubmFtZXMNCiAgICAgICAgdGhpcy5jdXN0b21QYXJhbXMuQ29kZSA9ICcnDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmN1c3RvbVBhcmFtcy5Db2RlX0xpa2UgPSAnJw0KICAgICAgICB0aGlzLmN1c3RvbVBhcmFtcy5Db2RlID0gdGhpcy5uYW1lcy5yZXBsYWNlKC9ccysvZywgJ1xuJykNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g6aG555uu5Yy65Z+f5pWw5o2u6ZuGDQogICAgZmV0Y2hUcmVlRGF0YSgpIHsNCiAgICAgIEdldFByb2plY3RBcmVhVHJlZUxpc3QoeyBUeXBlOiAwLCBNZW51SWQ6IHRoaXMuJHJvdXRlLm1ldGEuSWQsIHByb2plY3ROYW1lOiB0aGlzLnByb2plY3ROYW1lLCBMZXZlbDogdGhpcy5sZXZlbENvZGUgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIC8vIGNvbnN0IHJlc0FsbCA9IFsNCiAgICAgICAgLy8gICB7DQogICAgICAgIC8vICAgICBQYXJlbnROb2RlczogbnVsbCwNCiAgICAgICAgLy8gICAgIElkOiAnLTEnLA0KICAgICAgICAvLyAgICAgQ29kZTogJ+WFqOmDqCcsDQogICAgICAgIC8vICAgICBMYWJlbDogJ+WFqOmDqCcsDQogICAgICAgIC8vICAgICBMZXZlbDogbnVsbCwNCiAgICAgICAgLy8gICAgIERhdGE6IHt9LA0KICAgICAgICAvLyAgICAgQ2hpbGRyZW46IFtdDQogICAgICAgIC8vICAgfQ0KICAgICAgICAvLyBdDQogICAgICAgIC8vIGNvbnN0IHJlc0RhdGEgPSByZXNBbGwuY29uY2F0KHJlcy5EYXRhKQ0KICAgICAgICBpZiAoIXJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy50cmVlTG9hZGluZyA9IGZhbHNlDQogICAgICAgICAgdGhpcy50cmVlRGF0YSA9IFtdDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCiAgICAgICAgaWYgKHJlcy5EYXRhLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICAgIHRoaXMudHJlZUxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICAgIGNvbnN0IHJlc0RhdGEgPSByZXMuRGF0YQ0KICAgICAgICByZXNEYXRhLm1hcCgoaXRlbSkgPT4gew0KICAgICAgICAgIGlmIChpdGVtLkNoaWxkcmVuLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICAgICAgaXRlbS5Jc19JbXBvcnRlZCA9IGZhbHNlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGl0ZW0uRGF0YS5Jc19JbXBvcnRlZCA9IGl0ZW0uQ2hpbGRyZW4uc29tZSgoaWNoKSA9PiB7DQogICAgICAgICAgICAgIHJldHVybiBpY2guRGF0YS5Jc19JbXBvcnRlZCA9PT0gdHJ1ZQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIGl0ZW0uSXNfRGlyZWN0b3J5ID0gdHJ1ZQ0KICAgICAgICAgICAgaXRlbS5DaGlsZHJlbi5tYXAoKGl0KSA9PiB7DQogICAgICAgICAgICAgIGlmIChpdC5DaGlsZHJlbi5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgICAgaXQuSXNfRGlyZWN0b3J5ID0gdHJ1ZQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgdGhpcy50cmVlRGF0YSA9IHJlc0RhdGENCiAgICAgICAgaWYgKE9iamVjdC5rZXlzKHRoaXMuY3VycmVudE5vZGUpLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICAgIC8vIHRoaXMuZmV0Y2hEYXRhKCkNCiAgICAgICAgICB0aGlzLnNldEtleSgpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5oYW5kbGVOb2RlQ2xpY2sodGhpcy5jdXJyZW50Tm9kZSkNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLnRyZWVMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgLy8gdGhpcy5leHBhbmRlZEtleSA9IHRoaXMuY3VzdG9tUGFyYW1zLkFyZWFfSWQgPyB0aGlzLmN1c3RvbVBhcmFtcy5BcmVhX0lkIDogdGhpcy5jdXN0b21QYXJhbXMuUHJvamVjdF9JZCA/IHRoaXMuY3VzdG9tUGFyYW1zLlByb2plY3RfSWQgOiByZXNEYXRhWzBdLklkIC8vICctMScNCiAgICAgICAgLy8gdGhpcy5jdXN0b21QYXJhbXMuU3lzX1Byb2plY3RfSWQgPSB0aGlzLmN1c3RvbVBhcmFtcy5TeXNfUHJvamVjdF9JZCB8fCByZXNEYXRhWzBdLkRhdGEuU3lzX1Byb2plY3RfSWQNCiAgICAgICAgLy8gdGhpcy5jdXN0b21QYXJhbXMuUHJvamVjdF9JZCA9IHRoaXMuY3VzdG9tUGFyYW1zLlByb2plY3RfSWQgfHwgcmVzRGF0YVswXS5EYXRhLklkDQogICAgICAgIC8vIHRoaXMuY3VzdG9tUGFyYW1zLkFyZWFfTmFtZSA9ICcnDQogICAgICAgIC8vIHRoaXMudHJlZUxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAvLyB0aGlzLmZldGNoRGF0YSgpDQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g6K6+572u6buY6K6k6YCJ5Lit56ys5LiA5Liq5Yy65Z+f5pyr57qn6IqC54K5DQogICAgc2V0S2V5KCkgew0KICAgICAgY29uc3QgZGVlcEZpbHRlciA9ICh0cmVlKSA9PiB7DQogICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdHJlZS5sZW5ndGg7IGkrKykgew0KICAgICAgICAgIGNvbnN0IGl0ZW0gPSB0cmVlW2ldDQogICAgICAgICAgY29uc3QgeyBEYXRhLCBDaGlsZHJlbiB9ID0gaXRlbQ0KICAgICAgICAgIGNvbnNvbGUubG9nKERhdGEpDQogICAgICAgICAgaWYgKERhdGEuUGFyZW50SWQgJiYgIUNoaWxkcmVuPy5sZW5ndGgpIHsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKERhdGEsICc/Pz8/JykNCiAgICAgICAgICAgIHRoaXMuY3VycmVudE5vZGUgPSBEYXRhDQogICAgICAgICAgICB0aGlzLmhhbmRsZU5vZGVDbGljayhpdGVtKQ0KICAgICAgICAgICAgcmV0dXJuDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGlmIChDaGlsZHJlbiAmJiBDaGlsZHJlbi5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgIHJldHVybiBkZWVwRmlsdGVyKENoaWxkcmVuKQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy5oYW5kbGVOb2RlQ2xpY2soaXRlbSkNCiAgICAgICAgICAgICAgcmV0dXJuDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgICByZXR1cm4gZGVlcEZpbHRlcih0aGlzLnRyZWVEYXRhKQ0KICAgIH0sDQogICAgLy8g6YCJ5Lit5bem5L6n6aG555uu6IqC54K5DQogICAgaGFuZGxlTm9kZUNsaWNrKGRhdGEpIHsNCiAgICAgIHRoaXMuaGFuZGVsc2VhcmNoKCdyZXNldCcsIGZhbHNlKQ0KICAgICAgdGhpcy5jdXJyZW50Tm9kZSA9IGRhdGENCiAgICAgIHRoaXMuZXhwYW5kZWRLZXkgPSBkYXRhLklkDQogICAgICB0aGlzLmN1c3RvbVBhcmFtcy5JbnN0YWxsVW5pdF9JZCA9IFtdDQogICAgICBjb25zdCBkYXRhSWQgPSBkYXRhLklkID09PSAnLTEnID8gJycgOiBkYXRhLklkDQogICAgICBjb25zb2xlLmxvZygnbm9kZURhdGEnLCBkYXRhKQ0KICAgICAgaWYgKGRhdGEuUGFyZW50Tm9kZXMpIHsNCiAgICAgICAgdGhpcy5jdXN0b21QYXJhbXMuUHJvamVjdF9JZCA9IGRhdGEuRGF0YS5Qcm9qZWN0X0lkDQogICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLkFyZWFfSWQgPSBkYXRhLklkDQogICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLkFyZWFfTmFtZSA9IGRhdGEuRGF0YS5OYW1lDQogICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLlN5c19Qcm9qZWN0X0lkID0gZGF0YS5EYXRhLlN5c19Qcm9qZWN0X0lkDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmN1c3RvbVBhcmFtcy5Qcm9qZWN0X0lkID0gZGF0YUlkDQogICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLkFyZWFfSWQgPSAnJw0KICAgICAgICB0aGlzLmN1c3RvbVBhcmFtcy5BcmVhX05hbWUgPSBkYXRhLkRhdGEuTmFtZQ0KICAgICAgICB0aGlzLmN1c3RvbVBhcmFtcy5TeXNfUHJvamVjdF9JZCA9IGRhdGEuRGF0YS5TeXNfUHJvamVjdF9JZA0KICAgICAgfQ0KICAgICAgY29uc29sZS5sb2coDQogICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLlN5c19Qcm9qZWN0X0lkLA0KICAgICAgICAndGhpcy5jdXN0b21QYXJhbXMuU3lzX1Byb2plY3RfSWQ9PT09PT09PT09PT0xMTExMScNCiAgICAgICkNCiAgICAgIGNvbnNvbGUubG9nKA0KICAgICAgICB0aGlzLmN1c3RvbVBhcmFtcy5BcmVhX0lkLA0KICAgICAgICAndGhpcy5jdXN0b21QYXJhbXMuQXJlYV9JZD09PT09PT09PT09PTExMTExJw0KICAgICAgKQ0KICAgICAgdGhpcy5jdXJyZW50TGFzdExldmVsID0gISEoZGF0YS5EYXRhLkxldmVsICYmIGRhdGEuQ2hpbGRyZW4ubGVuZ3RoID09PSAwKQ0KICAgICAgaWYgKHRoaXMuY3VycmVudExhc3RMZXZlbCkgew0KICAgICAgICB0aGlzLmN1c3RvbVBhcmFtcy5Qcm9qZWN0X05hbWUgPSBkYXRhLkRhdGE/LlByb2plY3RfTmFtZQ0KICAgICAgICB0aGlzLmN1c3RvbVBhcmFtcy5BcmVhX05hbWUgPSBkYXRhLkxhYmVsDQogICAgICB9DQogICAgICB0aGlzLnF1ZXJ5SW5mby5QYWdlID0gMQ0KICAgICAgdGhpcy5wZ0xvYWRpbmcgPSB0cnVlDQogICAgICB0aGlzLmZldGNoRGF0YSgpDQogICAgICBjb25zb2xlLmxvZyh0aGlzLmN1c3RvbVBhcmFtcy5BcmVhX0lkKQ0KICAgICAgdGhpcy5nZXRJbnN0YWxsVW5pdElkTmFtZUxpc3QoZGF0YUlkLCBkYXRhKQ0KICAgIH0sDQoNCiAgICAvLyDojrflj5bmibnmrKENCiAgICBnZXRJbnN0YWxsVW5pdElkTmFtZUxpc3QoaWQsIGRhdGEpIHsNCiAgICAgIGlmIChpZCA9PT0gJycgfHwgZGF0YS5DaGlsZHJlbi5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMuaW5zdGFsbFVuaXRJZE5hbWVMaXN0ID0gW10NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIEdldEluc3RhbGxVbml0SWROYW1lTGlzdCh7IEFyZWFfSWQ6IGlkLCBMZXZlbDogdGhpcy5sZXZlbENvZGUgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgdGhpcy5pbnN0YWxsVW5pdElkTmFtZUxpc3QgPSByZXMuRGF0YQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5bel5bqP5a6M5oiQ6YePDQogICAgZ2V0UHJvY2Vzc0RhdGEoKSB7DQogICAgICBjb25zdCBjdXN0b21QYXJhbXNEYXRhID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLmN1c3RvbVBhcmFtcykpDQogICAgICBjb25zdCBJbnN0YWxsVW5pdF9JZHMgPSBjdXN0b21QYXJhbXNEYXRhLkluc3RhbGxVbml0X0lkLmpvaW4oJywnKQ0KICAgICAgZGVsZXRlIGN1c3RvbVBhcmFtc0RhdGEuSW5zdGFsbFVuaXRfSWQNCg0KICAgICAgdGhpcy53aWR0aCA9ICc0MCUnDQogICAgICB0aGlzLmdlbmVyYXRlQ29tcG9uZW50KCfpg6jku7blt6Xluo/lrozmiJDph48nLCAnUHJvY2Vzc0RhdGEnKQ0KICAgICAgdGhpcy4kbmV4dFRpY2soKF8pID0+IHsNCiAgICAgICAgdGhpcy4kcmVmc1snY29udGVudCddLmluaXQoY3VzdG9tUGFyYW1zRGF0YSwgSW5zdGFsbFVuaXRfSWRzLCB0aGlzLnNlbGVjdExpc3QubWFwKCh2KSA9PiB2LlBhcnRfQWdncmVnYXRlX0lkKS50b1N0cmluZygpKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGdldFRhYmxlQ29uZmlnKGNvZGUpIHsNCiAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4gew0KICAgICAgICBHZXRHcmlkQnlDb2RlKHsNCiAgICAgICAgICBjb2RlOg0KICAgICAgICAgICAgY29kZSArDQogICAgICAgICAgICAnLCcgKw0KICAgICAgICAgICAgdGhpcy50eXBlT3B0aW9uLmZpbmQoKGkpID0+IGkuSWQgPT09IHRoaXMuY3VzdG9tUGFyYW1zLlR5cGVJZCkuQ29kZQ0KICAgICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICBjb25zdCB7IElzU3VjY2VlZCwgRGF0YSwgTWVzc2FnZSB9ID0gcmVzDQogICAgICAgICAgaWYgKElzU3VjY2VlZCkgew0KICAgICAgICAgICAgaWYgKCFEYXRhKSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+W9k+WJjeS4k+S4muayoeaciemFjee9ruebuOWvueW6lOihqOagvCcpDQogICAgICAgICAgICAgIHRoaXMudGJMb2FkaW5nID0gdHJ1ZQ0KICAgICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHRoaXMudGJDb25maWcgPSBPYmplY3QuYXNzaWduKHt9LCB0aGlzLnRiQ29uZmlnLCBEYXRhLkdyaWQpDQogICAgICAgICAgICBjb25zdCBsaXN0ID0gRGF0YS5Db2x1bW5MaXN0IHx8IFtdDQogICAgICAgICAgICBjb25zdCBzb3J0TGlzdCA9IGxpc3Quc29ydCgoYSwgYikgPT4gYS5Tb3J0IC0gYi5Tb3J0KQ0KICAgICAgICAgICAgdGhpcy5jb2x1bW5zID0gc29ydExpc3QNCiAgICAgICAgICAgICAgLmZpbHRlcigodikgPT4gdi5Jc19EaXNwbGF5KQ0KICAgICAgICAgICAgICAubWFwKChpdGVtKSA9PiB7DQogICAgICAgICAgICAgICAgaWYgKGl0ZW0uQ29kZSA9PT0gJ0NvZGUnKSB7DQogICAgICAgICAgICAgICAgICBpdGVtLmZpeGVkID0gJ2xlZnQnDQogICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHRoaXMucXVlcnlJbmZvLlBhZ2VTaXplID0gK0RhdGEuR3JpZC5Sb3dfTnVtYmVyIHx8IDIwDQogICAgICAgICAgICByZXNvbHZlKHRoaXMuY29sdW1ucykNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKHRoaXMuY29sdW1ucykNCiAgICAgICAgICAgIGNvbnN0IHNlbGVjdE9wdGlvbiA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy5jb2x1bW5zKSkNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKHNlbGVjdE9wdGlvbikNCiAgICAgICAgICAgIHRoaXMuY29sdW1uc09wdGlvbiA9IHNlbGVjdE9wdGlvbi5maWx0ZXIoKHYpID0+IHsNCiAgICAgICAgICAgICAgcmV0dXJuICgNCiAgICAgICAgICAgICAgICB2LkRpc3BsYXlfTmFtZSAhPT0gJ+aTjeS9nOaXtumXtCcgJiYNCiAgICAgICAgICAgICAgICB2LkRpc3BsYXlfTmFtZSAhPT0gJ+aooeWei0lEJyAmJg0KICAgICAgICAgICAgICAgIHYuRGlzcGxheV9OYW1lICE9PSAn5rex5YyW6LWE5paZJyAmJg0KICAgICAgICAgICAgICAgIHYuRGlzcGxheV9OYW1lICE9PSAn5aSH5rOoJyAmJg0KICAgICAgICAgICAgICAgIHYuRGlzcGxheV9OYW1lICE9PSAn5o6S5Lqn5pWw6YePJyAmJg0KICAgICAgICAgICAgICAgIHYuQ29kZS5pbmRleE9mKCdBdHRyJykgPT09IC0xICYmDQogICAgICAgICAgICAgICAgdi5EaXNwbGF5X05hbWUgIT09ICfmibnmrKEnDQogICAgICAgICAgICAgICkNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiBNZXNzYWdlLA0KICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfSwNCiAgICBhc3luYyBmZXRjaExpc3QoKSB7DQogICAgICBjb25zdCBjdXN0b21QYXJhbXNEYXRhID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLmN1c3RvbVBhcmFtcykpDQogICAgICBjb25zdCBJbnN0YWxsVW5pdF9JZHMgPSBjdXN0b21QYXJhbXNEYXRhLkluc3RhbGxVbml0X0lkLmpvaW4oJywnKQ0KICAgICAgZGVsZXRlIGN1c3RvbVBhcmFtc0RhdGEuSW5zdGFsbFVuaXRfSWQNCg0KICAgICAgYXdhaXQgR2V0VW5pdFBhZ2VMaXN0KHsNCiAgICAgICAgTGV2ZWw6IHRoaXMubGV2ZWxDb2RlLA0KICAgICAgICAuLi50aGlzLnF1ZXJ5SW5mbywNCiAgICAgICAgLi4uY3VzdG9tUGFyYW1zRGF0YSwNCiAgICAgICAgQ29kZTogY3VzdG9tUGFyYW1zRGF0YS5Db2RlLnRyaW0oKS5yZXBsYWNlQWxsKCcgJywgJ1xuJyksDQogICAgICAgIEluc3RhbGxVbml0X0lkczogSW5zdGFsbFVuaXRfSWRzDQogICAgICB9KQ0KICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIHRoaXMucXVlcnlJbmZvLlBhZ2VTaXplID0gcmVzLkRhdGEuUGFnZVNpemUNCiAgICAgICAgICAgIHRoaXMudG90YWwgPSByZXMuRGF0YS5Ub3RhbENvdW50DQogICAgICAgICAgICB0aGlzLnRiRGF0YSA9IHJlcy5EYXRhLkRhdGEubWFwKCh2KSA9PiB7DQogICAgICAgICAgICAgIHYuSXNfTWFpbiA9IHYuSXNfTWFpbiA/ICfmmK8nIDogJ+WQpicNCiAgICAgICAgICAgICAgdi5FeGRhdGUgPSB0aW1lRm9ybWF0KHYuRXhkYXRlLCAne3l9LXttfS17ZH0ge2h9OntpfTp7c30nKQ0KICAgICAgICAgICAgICAvLyBjb25zb2xlLmxvZyh2KQ0KICAgICAgICAgICAgICByZXR1cm4gdg0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHRoaXMuc2VsZWN0TGlzdCA9IFtdDQogICAgICAgICAgICB0aGlzLmdldFVuaXRXZWlnaHRMaXN0KCkNCiAgICAgICAgICAgIHRoaXMuZ2V0U3RvcExpc3QoKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICAuZmluYWxseSgoKSA9PiB7DQogICAgICAgICAgdGhpcy50YkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAgIHRoaXMucGdMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgfSkNCiAgICB9LA0KICAgIGFzeW5jIGdldFN0b3BMaXN0KCkgew0KICAgICAgY29uc3Qgc3VibWl0T2JqID0gdGhpcy50YkRhdGEubWFwKGl0ZW0gPT4gew0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIElkOiBpdGVtLlBhcnRfQWdncmVnYXRlX0lkLA0KICAgICAgICAgIFR5cGU6IDMNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIGF3YWl0IEdldFN0b3BMaXN0KHN1Ym1pdE9iaikudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIGNvbnN0IHN0b3BNYXAgPSB7fQ0KICAgICAgICAgIHJlcy5EYXRhLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgICBzdG9wTWFwW2l0ZW0uSWRdID0gaXRlbS5Jc19TdG9wICE9PSBudWxsDQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLnRiRGF0YS5mb3JFYWNoKHJvdyA9PiB7DQogICAgICAgICAgICBpZiAoc3RvcE1hcFtyb3cuUGFydF9BZ2dyZWdhdGVfSWRdKSB7DQogICAgICAgICAgICAgIHRoaXMuJHNldChyb3csICdzdG9wRmxhZycsIHN0b3BNYXBbcm93LlBhcnRfQWdncmVnYXRlX0lkXSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgYXN5bmMgZmV0Y2hEYXRhKCkgew0KICAgICAgY29uc29sZS5sb2coJ+abtOaWsOWIl+ihqCcpDQogICAgICAvLyDliIblvIDojrflj5bvvIzmj5Dpq5jmjqXlj6PpgJ/luqYNCiAgICAgIGNvbnN0IGFjdGl2ZU5hbWUgPSBgcGxtX2xldmVsJHt0aGlzLmxldmVsQ29kZX1fcGFnZV9saXN0YA0KICAgICAgYXdhaXQgdGhpcy5nZXRUYWJsZUNvbmZpZyhhY3RpdmVOYW1lKQ0KICAgICAgdGhpcy50YkxvYWRpbmcgPSB0cnVlDQogICAgICB0aGlzLmZldGNoTGlzdCgpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLnRiTG9hZGluZyA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQogICAgYXN5bmMgY2hhbmdlUGFnZSgpIHsNCiAgICAgIHRoaXMudGJMb2FkaW5nID0gdHJ1ZQ0KICAgICAgaWYgKA0KICAgICAgICB0eXBlb2YgdGhpcy5xdWVyeUluZm8uUGFnZVNpemUgIT09ICdudW1iZXInIHx8DQogICAgICAgIHRoaXMucXVlcnlJbmZvLlBhZ2VTaXplIDwgMQ0KICAgICAgKSB7DQogICAgICAgIHRoaXMucXVlcnlJbmZvLlBhZ2VTaXplID0gMTANCiAgICAgIH0NCiAgICAgIHRoaXMuZmV0Y2hMaXN0KCkudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMudGJMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyB0YlNlbGVjdENoYW5nZShhcnJheSkgew0KICAgIC8vICAgY29uc29sZS5sb2coJ2FycmF5JywgYXJyYXkpDQogICAgLy8gICB0aGlzLnNlbGVjdExpc3QgPSBhcnJheS5yZWNvcmRzDQogICAgLy8gICBjb25zb2xlLmxvZygndGhpcy5zZWxlY3RMaXN0JywgdGhpcy5zZWxlY3RMaXN0KQ0KICAgIC8vIH0sDQogICAgZ2V0VGJEYXRhKGRhdGEpIHsNCiAgICAgIGNvbnN0IHsgWWVhckFsbFdlaWdodCwgWWVhclN0ZWVsLCBDb3VudEluZm8gfSA9IGRhdGENCiAgICAgIC8vIHRoaXMudGlwTGFiZWwgPSBg57Sv6K6h5LiK5Lyg5p6E5Lu2JHtZZWFyU3RlZWx95Lu277yM5oC76YeNJHtZZWFyQWxsV2VpZ2h0fXTjgIJgDQogICAgICB0aGlzLnRpcExhYmVsID0gQ291bnRJbmZvDQogICAgfSwNCiAgICBhc3luYyBnZXRUeXBlTGlzdCgpIHsNCiAgICAgIGxldCByZXMgPSBudWxsDQogICAgICBsZXQgZGF0YSA9IG51bGwNCiAgICAgIHJlcyA9IGF3YWl0IEdldEZhY3RvcnlQcm9mZXNzaW9uYWxCeUNvZGUoew0KICAgICAgICBmYWN0b3J5SWQ6IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdDdXJSZWZlcmVuY2VJZCcpDQogICAgICB9KQ0KICAgICAgZGF0YSA9IHJlcy5EYXRhDQogICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICB0aGlzLnR5cGVPcHRpb24gPSBPYmplY3QuZnJlZXplKGRhdGEpDQogICAgICAgIGlmICh0aGlzLnR5cGVPcHRpb24ubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoaXMuUHJvcG9ydGlvbiA9IGRhdGFbMF0uUHJvcG9ydGlvbg0KICAgICAgICAgIHRoaXMuVW5pdCA9IGRhdGFbMF0uVW5pdA0KICAgICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLlR5cGVJZCA9IHRoaXMudHlwZU9wdGlvblswXT8uSWQNCiAgICAgICAgICB0aGlzLmN1c3RvbVBhcmFtcy5UeXBlX05hbWUgPSB0aGlzLnR5cGVPcHRpb25bMF0/Lk5hbWUNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlRGVsZXRlKCkgew0KICAgICAgdGhpcy4kY29uZmlybSgn5q2k5pON5L2c5bCG5Yig6Zmk6YCJ5oup5pWw5o2uLCDmmK/lkKbnu6fnu60/JywgJ+aPkOekuicsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgRGVsZXRlcGFydCh7DQogICAgICAgICAgICBMZXZlbDogdGhpcy5sZXZlbENvZGUsDQogICAgICAgICAgICBpZHM6IHRoaXMuc2VsZWN0TGlzdC5tYXAoKHYpID0+IHYuUGFydF9BZ2dyZWdhdGVfSWQpLnRvU3RyaW5nKCkNCiAgICAgICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICAgIHRoaXMuZmV0Y2hEYXRhKCkNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKnycsDQogICAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgIHRoaXMuZ2V0VW5pdFdlaWdodExpc3QoKQ0KICAgICAgICAgICAgICB0aGlzLmZldGNoVHJlZURhdGEoKQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICB0eXBlOiAnaW5mbycsDQogICAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raI5Yig6ZmkJw0KICAgICAgICAgIH0pDQogICAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVFZGl0KHJvdykgew0KICAgICAgdGhpcy53aWR0aCA9ICc0NSUnDQogICAgICB0aGlzLmdlbmVyYXRlQ29tcG9uZW50KCfnvJbovpHpg6jku7YnLCAnRWRpdCcpDQogICAgICB0aGlzLiRuZXh0VGljaygoXykgPT4gew0KICAgICAgICByb3cuaXNSZWFkT25seSA9IGZhbHNlDQogICAgICAgIHRoaXMuJHJlZnNbJ2NvbnRlbnQnXS5pbml0KHJvdykNCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVCYXRjaEVkaXQoKSB7DQogICAgICBjb25zdCBTY2hlZHVsQXJyID0gdGhpcy5zZWxlY3RMaXN0LmZpbHRlcigoaXRlbSkgPT4gew0KICAgICAgICByZXR1cm4gaXRlbS5TY2hkdWxpbmdfQ291bnQgIT0gbnVsbCAmJiBpdGVtLlNjaGR1bGluZ19Db3VudCA+IDANCiAgICAgIH0pDQogICAgICBpZiAoU2NoZWR1bEFyci5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIHR5cGU6ICdlcnJvcicsDQogICAgICAgICAgbWVzc2FnZTogJ+mAieS4reihjOWMheWQq+W3suaOkuS6p+eahOmDqOS7tiznvJbovpHkv6Hmga/pnIDopoHov5vooYzlj5jmm7Tmk43kvZwnDQogICAgICAgIH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLndpZHRoID0gJzQwJScNCiAgICAgICAgdGhpcy5nZW5lcmF0ZUNvbXBvbmVudCgn5om56YeP57yW6L6RJywgJ0JhdGNoRWRpdCcpDQogICAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgICAgdGhpcy4kcmVmc1snY29udGVudCddLmluaXQodGhpcy5zZWxlY3RMaXN0LCB0aGlzLmNvbHVtbnNPcHRpb24pDQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVWaWV3KHJvdykgew0KICAgICAgdGhpcy53aWR0aCA9ICc0NSUnDQogICAgICB0aGlzLmdlbmVyYXRlQ29tcG9uZW50KCfor6bmg4UnLCAnRWRpdCcpDQogICAgICB0aGlzLiRuZXh0VGljaygoXykgPT4gew0KICAgICAgICByb3cuaXNSZWFkT25seSA9IHRydWUNCiAgICAgICAgdGhpcy4kcmVmc1snY29udGVudCddLmluaXQocm93KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFzeW5jIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIGNvbnN0IG9iaiA9IHsNCiAgICAgICAgTGV2ZWw6IHRoaXMubGV2ZWxDb2RlLA0KICAgICAgICBQYXJ0X0FnZ3JlZ2F0ZV9JZHM6IHRoaXMuc2VsZWN0TGlzdA0KICAgICAgICAgIC5tYXAoKHYpID0+IHYuUGFydF9BZ2dyZWdhdGVfSWQpDQogICAgICAgICAgLnRvU3RyaW5nKCksDQogICAgICAgIFByb2Zlc3Npb25hbENvZGU6IHRoaXMudHlwZUVudGl0eS5Db2RlDQogICAgICB9DQogICAgICBFeHBvcnRQbGFuVW5pdEluZm8ob2JqKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB3aW5kb3cub3Blbihjb21iaW5lVVJMKHRoaXMuJGJhc2VVcmwsIHJlcy5EYXRhKSwgJ19ibGFuaycpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMuTWVzc2FnZSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOimhuebluWvvOWFpSBvciDmlrDlop7lr7zlhaUNCiAgICBoYW5kbGVDb21tYW5kKGNvbW1hbmQpIHsNCiAgICAgIGNvbnNvbGUubG9nKGNvbW1hbmQsICdjb21tYW5kJykNCiAgICAgIHRoaXMuY29tbWFuZCA9IGNvbW1hbmQNCiAgICAgIHRoaXMuZGVlcExpc3RJbXBvcnQoKQ0KICAgIH0sDQogICAgZGVlcExpc3RJbXBvcnQoKSB7DQogICAgICBjb25zdCBmaWxlVHlwZSA9IHsNCiAgICAgICAgQ2F0YWxvZ19Db2RlOiAnUExNRGVlcGVuRmlsZXMnLA0KICAgICAgICBDb2RlOiB0aGlzLnR5cGVFbnRpdHkuQ29kZSwNCiAgICAgICAgbmFtZTogdGhpcy50eXBlRW50aXR5Lk5hbWUNCiAgICAgIH0NCiAgICAgIHRoaXMuJHJlZnMuZGlhbG9nLmhhbmRsZU9wZW4oDQogICAgICAgICdhZGQnLA0KICAgICAgICBmaWxlVHlwZSwNCiAgICAgICAgbnVsbCwNCiAgICAgICAgdHJ1ZSwNCiAgICAgICAgdGhpcy5QSUQsDQogICAgICAgIHRoaXMuY29tbWFuZCwNCiAgICAgICAgdGhpcy5jdXN0b21QYXJhbXMNCiAgICAgICkNCiAgICB9LA0KICAgIGFzeW5jIGhhbmRsZUFsbERlbGV0ZSgpIHsNCiAgICAgIGNvbnNvbGUubG9nKHRoaXMuY3VzdG9tUGFyYW1zLlByb2plY3RfSWQpDQogICAgICBpZiAodGhpcy5jdXN0b21QYXJhbXMuUHJvamVjdF9JZCkgew0KICAgICAgICBhd2FpdCBwcm9tcHRCb3goeyB0aXRsZTogJ+WIoOmZpCcgfSkNCiAgICAgICAgYXdhaXQgRGVsZXRlcGFydEJ5ZmluZGtleXdvZGVzKHsNCiAgICAgICAgICBMZXZlbDogdGhpcy5sZXZlbENvZGUsDQogICAgICAgICAgLi4udGhpcy5jdXN0b21QYXJhbXMsDQogICAgICAgICAgLi4udGhpcy5xdWVyeUluZm8NCiAgICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJykNCiAgICAgICAgICAgIHRoaXMuZmV0Y2hEYXRhKCkNCiAgICAgICAgICAgIHRoaXMuZmV0Y2hUcmVlRGF0YSgpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLk1lc3NhZ2UpDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjpgInmi6npobnnm64nKQ0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlQ2xvc2UoKSB7DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgIH0sDQogICAgZ2VuZXJhdGVDb21wb25lbnQodGl0bGUsIGNvbXBvbmVudCkgew0KICAgICAgdGhpcy50aXRsZSA9IHRpdGxlDQogICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSBjb21wb25lbnQNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIC8vIOeCueWHu+aQnOe0og0KICAgIGhhbmRlbHNlYXJjaChyZXNldCwgaGFzU2VhcmNoID0gdHJ1ZSkgew0KICAgICAgdGhpcy5kZWxldGVDb250ZW50ID0gZmFsc2UNCiAgICAgIGlmIChyZXNldCkgew0KICAgICAgICB0aGlzLiRyZWZzLmN1c3RvbVBhcmFtcy5yZXNldEZpZWxkcygpDQogICAgICAgIHRoaXMuZGVsZXRlQ29udGVudCA9IHRydWUNCiAgICAgICAgdGhpcy5uYW1lcyA9ICcnDQogICAgICB9DQogICAgICBoYXNTZWFyY2ggJiYgdGhpcy5mZXRjaERhdGEoKQ0KICAgIH0sDQogICAgLy8g5rex5YyW6LWE5paZ5p+l55yLDQogICAgaGFuZGxlRGVlcE1hdGVyaWFsKHJvdykgew0KICAgICAgY29uc29sZS5sb2coJ2hhbmRsZURlZXBNYXRlcmlhbCcpDQogICAgICB0aGlzLndpZHRoID0gJzQ1JScNCiAgICAgIHRoaXMuZ2VuZXJhdGVDb21wb25lbnQoJ+afpeeci+a3seWMlui1hOaWmScsICdEZWVwTWF0ZXJpYWwnKQ0KICAgICAgdGhpcy4kbmV4dFRpY2soKF8pID0+IHsNCiAgICAgICAgcm93LmlzUmVhZE9ubHkgPSBmYWxzZQ0KICAgICAgICB0aGlzLiRyZWZzWydjb250ZW50J10uaW5pdChyb3cpDQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g5o6S5Lqn5pWw6YePDQogICAgaGFuZGVsU2NoZHVsaW5nKHJvdykgew0KICAgICAgdGhpcy53aWR0aCA9ICc0NSUnDQogICAgICB0aGlzLmdlbmVyYXRlQ29tcG9uZW50KCfnlJ/kuqfor6bmg4UnLCAnU2NoZHVsaW5nJykNCiAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgIHJvdy5pc1JlYWRPbmx5ID0gZmFsc2UNCiAgICAgICAgdGhpcy4kcmVmc1snY29udGVudCddLmluaXQocm93KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOafpeeci+mbtuS7tg0KICAgIGhhbmRsZVBhcnRMaXN0KHJvdykgew0KICAgICAgdGhpcy53aWR0aCA9ICc0NSUnDQogICAgICB0aGlzLmdlbmVyYXRlQ29tcG9uZW50KCfpm7bku7bmuIXljZUnLCAnUGFydExpc3QnKQ0KICAgICAgdGhpcy4kbmV4dFRpY2soKF8pID0+IHsNCiAgICAgICAgcm93LmlzUmVhZE9ubHkgPSBmYWxzZQ0KICAgICAgICB0aGlzLiRyZWZzWydjb250ZW50J10uaW5pdChyb3cpDQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g6YOo5Lu25YiG6aG157uf6K6hDQogICAgZ2V0VW5pdFdlaWdodExpc3QoKSB7DQogICAgICB0aGlzLmNvdW50TG9hZGluZyA9IHRydWUNCiAgICAgIGNvbnN0IGN1c3RvbVBhcmFtc0RhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMuY3VzdG9tUGFyYW1zKSkNCiAgICAgIGNvbnN0IEluc3RhbGxVbml0X0lkcyA9IGN1c3RvbVBhcmFtc0RhdGEuSW5zdGFsbFVuaXRfSWQuam9pbignLCcpDQogICAgICBkZWxldGUgY3VzdG9tUGFyYW1zRGF0YS5JbnN0YWxsVW5pdF9JZA0KICAgICAgR2V0VW5pdFdlaWdodExpc3Qoew0KICAgICAgICBMZXZlbDogdGhpcy5sZXZlbENvZGUsDQogICAgICAgIC4uLnRoaXMucXVlcnlJbmZvLA0KICAgICAgICAuLi5jdXN0b21QYXJhbXNEYXRhLA0KICAgICAgICBJbnN0YWxsVW5pdF9JZHMNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMuU3RlZWxBbW91bnRUb3RhbCA9IE1hdGgucm91bmQocmVzLkRhdGEuRGVlcGVuTnVtICogMTAwMCkgLyAxMDAwIC8vIOa3seWMluaAu+mHjw0KICAgICAgICAgIHRoaXMuU2NoZWR1bGluZ051bVRvdGFsID0NCiAgICAgICAgICAgIE1hdGgucm91bmQocmVzLkRhdGEuU2NoZWR1bGluZ051bSAqIDEwMDApIC8gMTAwMCAvLyDmjpLkuqfmgLvph48NCiAgICAgICAgICB0aGlzLlN0ZWVsQWxsV2VpZ2h0VG90YWwgPQ0KICAgICAgICAgICAgTWF0aC5yb3VuZChyZXMuRGF0YS5EZWVwZW5XZWlnaHQgKiAxMDAwKSAvIDEwMDAgLy8g5rex5YyW5oC76YeNDQogICAgICAgICAgdGhpcy5TY2hlZHVsaW5nQWxsV2VpZ2h0VG90YWwgPQ0KICAgICAgICAgICAgTWF0aC5yb3VuZChyZXMuRGF0YS5TY2hlZHVsaW5nV2VpZ2h0ICogMTAwMCkgLyAxMDAwIC8vIOaOkuS6p+aAu+mHjQ0KICAgICAgICAgIHRoaXMuRmluaXNoQ291bnRUb3RhbCA9DQogICAgICAgICAgICBNYXRoLnJvdW5kKHJlcy5EYXRhLkZpbmlzaF9Db3VudCAqIDEwMDApIC8gMTAwMCAvLyDlrozmiJDmgLvmlbANCiAgICAgICAgICB0aGlzLkZpbmlzaFdlaWdodFRvdGFsID0NCiAgICAgICAgICAgIE1hdGgucm91bmQocmVzLkRhdGEuRmluaXNoX1dlaWdodCAqIDEwMDApIC8gMTAwMCAvLyDlrozmiJDmgLvph40NCiAgICAgICAgICB0aGlzLkRpcmVjdENvdW50VG90YWwgPQ0KICAgICAgICAgICAgTWF0aC5yb3VuZChyZXMuRGF0YS5EaXJlY3RfQ291bnQgKiAxMDAwKSAvIDEwMDAgLy8g55u05Y+R5Lu25oC75pWwDQogICAgICAgICAgdGhpcy5EaXJlY3RXZWlnaHRUb3RhbCA9DQogICAgICAgICAgICBNYXRoLnJvdW5kKHJlcy5EYXRhLkRpcmVjdF9XZWlnaHQgKiAxMDAwKSAvIDEwMDAgLy8g55u05Y+R5Lu25oC76YePDQogICAgICAgICAgY29uc29sZS5sb2coJyB0aGlzLlN0ZWVsQWxsV2VpZ2h0VG90YWwnLCB0aGlzLlN0ZWVsQWxsV2VpZ2h0VG90YWwpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMuTWVzc2FnZSkNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLmNvdW50TG9hZGluZyA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQogICAgdGJTZWxlY3RDaGFuZ2UoYXJyYXkpIHsNCiAgICAgIHRoaXMuc2VsZWN0TGlzdCA9IGFycmF5LnJlY29yZHMNCiAgICAgIHRoaXMuU3RlZWxBbW91bnRUb3RhbCA9IDANCiAgICAgIHRoaXMuU2NoZWR1bGluZ051bVRvdGFsID0gMA0KICAgICAgdGhpcy5TdGVlbEFsbFdlaWdodFRvdGFsID0gMA0KICAgICAgdGhpcy5TY2hlZHVsaW5nQWxsV2VpZ2h0VG90YWwgPSAwDQogICAgICB0aGlzLkZpbmlzaENvdW50VG90YWwgPSAwDQogICAgICB0aGlzLkZpbmlzaFdlaWdodFRvdGFsID0gMA0KICAgICAgdGhpcy5EaXJlY3RDb3VudFRvdGFsID0gMA0KICAgICAgbGV0IFN0ZWVsQWxsV2VpZ2h0VG90YWxUZW1wID0gMA0KICAgICAgbGV0IFNjaGVkdWxpbmdBbGxXZWlnaHRUb3RhbFRlbXAgPSAwDQogICAgICBsZXQgRmluaXNoV2VpZ2h0VG90YWxUZW1wID0gMA0KICAgICAgbGV0IERpcmVjdFdlaWdodFRvdGFsVGVtcCA9IDANCiAgICAgIGlmICh0aGlzLnNlbGVjdExpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICB0aGlzLnNlbGVjdExpc3QuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgIGNvbnN0IHNjaGVkdWxpbmdOdW0gPQ0KICAgICAgICAgICAgaXRlbS5TY2hkdWxpbmdfQ291bnQgPT0gbnVsbCA/IDAgOiBpdGVtLlNjaGR1bGluZ19Db3VudA0KICAgICAgICAgIHRoaXMuU3RlZWxBbW91bnRUb3RhbCArPSBpdGVtLk51bQ0KICAgICAgICAgIHRoaXMuU2NoZWR1bGluZ051bVRvdGFsICs9IE51bWJlcihpdGVtLlNjaGR1bGluZ19Db3VudCkNCiAgICAgICAgICB0aGlzLkZpbmlzaENvdW50VG90YWwgKz0gaXRlbS5GaW5pc2hfQ291bnQNCiAgICAgICAgICBpZiAoaXRlbS5Jc0RpcmVjdCkgew0KICAgICAgICAgICAgdGhpcy5EaXJlY3RDb3VudFRvdGFsICs9IGl0ZW0uTnVtIC8vIOebtOWPkeS7tuaAu+aVsA0KICAgICAgICAgICAgRGlyZWN0V2VpZ2h0VG90YWxUZW1wICs9IGl0ZW0uVG90YWxfV2VpZ2h0IC8vIOebtOWPkeS7tuaAu+mHjw0KICAgICAgICAgIH0NCiAgICAgICAgICBTdGVlbEFsbFdlaWdodFRvdGFsVGVtcCArPSBpdGVtLlRvdGFsX1dlaWdodA0KICAgICAgICAgIFNjaGVkdWxpbmdBbGxXZWlnaHRUb3RhbFRlbXAgKz0gaXRlbS5XZWlnaHQgKiBzY2hlZHVsaW5nTnVtDQogICAgICAgICAgRmluaXNoV2VpZ2h0VG90YWxUZW1wICs9IGl0ZW0uRmluaXNoX1dlaWdodA0KICAgICAgICB9KQ0KICAgICAgICB0aGlzLlN0ZWVsQWxsV2VpZ2h0VG90YWwgPQ0KICAgICAgICAgIE1hdGgucm91bmQoKFN0ZWVsQWxsV2VpZ2h0VG90YWxUZW1wIC8gdGhpcy5Qcm9wb3J0aW9uKSAqIDEwMDApIC8gMTAwMA0KICAgICAgICB0aGlzLlNjaGVkdWxpbmdBbGxXZWlnaHRUb3RhbCA9DQogICAgICAgICAgTWF0aC5yb3VuZCgoU2NoZWR1bGluZ0FsbFdlaWdodFRvdGFsVGVtcCAvIHRoaXMuUHJvcG9ydGlvbikgKiAxMDAwKSAvDQogICAgICAgICAgMTAwMA0KICAgICAgICB0aGlzLkZpbmlzaFdlaWdodFRvdGFsID0NCiAgICAgICAgICBNYXRoLnJvdW5kKChGaW5pc2hXZWlnaHRUb3RhbFRlbXAgLyB0aGlzLlByb3BvcnRpb24pICogMTAwMCkgLyAxMDAwDQogICAgICAgIHRoaXMuRGlyZWN0V2VpZ2h0VG90YWwgPQ0KICAgICAgICAgIE1hdGgucm91bmQoKERpcmVjdFdlaWdodFRvdGFsVGVtcCAvIHRoaXMuUHJvcG9ydGlvbikgKiAxMDAwKSAvIDEwMDANCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuZ2V0VW5pdFdlaWdodExpc3QoKQ0KICAgICAgfQ0KICAgIH0sDQogICAgZmV0Y2hUcmVlRGF0YUxvY2FsKCkgew0KICAgICAgLy8gdGhpcy5maWx0ZXJUZXh0ID0gdGhpcy5wcm9qZWN0TmFtZQ0KICAgIH0sDQogICAgZ2V0UGFydEluZm8ocm93KSB7DQogICAgICBjb25zdCBkcmF3aW5nRGF0YSA9IHJvdy5EcmF3aW5nID8gcm93LkRyYXdpbmcuc3BsaXQoJywnKSA6IFtdIC8vIOWbvue6uOaVsOaNrg0KICAgICAgY29uc3QgZmlsZVVybERhdGEgPSByb3cuRmlsZV9VcmwgPyByb3cuRmlsZV9Vcmwuc3BsaXQoJywnKSA6IFtdIC8vIOWbvue6uOaVsOaNruaWh+S7tuWcsOWdgOaVsOaNrg0KICAgICAgaWYgKGZpbGVVcmxEYXRhLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAn5b2T5YmN6YOo5Lu25peg5Zu+57q4JywNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgfSkNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICBpZiAoZHJhd2luZ0RhdGEubGVuZ3RoID4gMCAmJiBmaWxlVXJsRGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMuZHJhd2luZ0FjdGl2ZSA9IGRyYXdpbmdEYXRhWzBdDQogICAgICB9DQogICAgICBpZiAoZHJhd2luZ0RhdGEubGVuZ3RoID4gMCAmJiBmaWxlVXJsRGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMuZHJhd2luZ0RhdGFMaXN0ID0gZHJhd2luZ0RhdGEubWFwKChpdGVtLCBpbmRleCkgPT4gKHsNCiAgICAgICAgICBuYW1lOiBpdGVtLA0KICAgICAgICAgIGxhYmVsOiBpdGVtLA0KICAgICAgICAgIHVybDogZmlsZVVybERhdGFbaW5kZXhdDQogICAgICAgIH0pKQ0KICAgICAgfQ0KDQogICAgICB0aGlzLmdldFBhcnRJbmZvRHJhd2luZyhyb3cpDQogICAgfSwNCg0KICAgIGdldFBhcnRJbmZvRHJhd2luZyhyb3cpIHsNCiAgICAgIGNvbnN0IGltcG9ydERldGFpbElkID0gcm93LlBhcnRfQWdncmVnYXRlX0lkDQogICAgICBHZXRTdGVlbENhZEFuZEJpbUlkKHsgaW1wb3J0RGV0YWlsSWQ6IGltcG9ydERldGFpbElkLCBMZXZlbDogdGhpcy5sZXZlbENvZGUgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgY29uc3QgZHJhd2luZ0RhdGEgPSB7DQogICAgICAgICAgICAnZXh0ZW5zaW9uTmFtZSc6IHJlcy5EYXRhWzBdLkV4dGVuc2lvbk5hbWUsDQogICAgICAgICAgICAnZmlsZUJpbSc6IHJlcy5EYXRhWzBdLmZpbGVCaW0sDQogICAgICAgICAgICAnSXNVcGxvYWQnOiByZXMuRGF0YVswXS5Jc1VwbG9hZCwNCiAgICAgICAgICAgICdDb2RlJzogcm93LkNvZGUsDQogICAgICAgICAgICAnU3lzX1Byb2plY3RfSWQnOiByb3cuU3lzX1Byb2plY3RfSWQNCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy4kcmVmcy5tb2RlbERyYXdpbmdSZWYuZHdnSW5pdChkcmF3aW5nRGF0YSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgY2hhbmdlRHJhd2luZyh0YWIsIGV2ZW50KSB7DQogICAgICBjb25zb2xlLmxvZyh0YWIsIGV2ZW50KQ0KICAgICAgY29uc3QgdGFiTmFtZSA9IHRhYi5uYW1lDQogICAgICAvLyDmn6Xmib7lr7nlupTnmoQgVVJMDQogICAgICBjb25zdCBkcmF3aW5nVGFiID0gdGhpcy5kcmF3aW5nRGF0YUxpc3QuZmluZCgNCiAgICAgICAgKGZpbGUpID0+IGZpbGUubmFtZSA9PT0gdGFiTmFtZQ0KICAgICAgKQ0KICAgICAgaWYgKGRyYXdpbmdUYWIpIHsNCiAgICAgICAgY29uc29sZS5sb2coJ1VSTDonLCBkcmF3aW5nVGFiLnVybCkgLy8g6L6T5Ye6IFVSTA0KICAgICAgICAvLyDlnKjov5nph4zkvaDlj6/ku6XlpITnkIblhbbku5bpgLvovpHvvIzlpoLmiZPlvIDpk77mjqXnrYkNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qICAgIGhhbmRsZVZpZXdEd2cocm93KSB7DQogICAgICBpZiAoIXJvdy5GaWxlX1VybCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAn5b2T5YmN6Zu25Lu25peg5Zu+57q4JywNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgfSkNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICB3aW5kb3cub3BlbignaHR0cDovL2R3Z3YxLmJpbXRrLmNvbTo1NDMyLz9DYWRVcmw9JyArIHBhcnNlT3NzVXJsKHJvdy5GaWxlX1VybCksICdfYmxhbmsnKQ0KICAgIH0sKi8NCiAgICBjdXN0b21GaWx0ZXJGdW4odmFsdWUsIGRhdGEsIG5vZGUpIHsNCiAgICAgIGNvbnN0IGFyciA9IHZhbHVlLnNwbGl0KFNQTElUX1NZTUJPTCkNCiAgICAgIGNvbnN0IGxhYmVsVmFsID0gYXJyWzBdDQogICAgICBjb25zdCBzdGF0dXNWYWwgPSBhcnJbMV0NCiAgICAgIGlmICghdmFsdWUpIHJldHVybiB0cnVlDQogICAgICBsZXQgcGFyZW50Tm9kZSA9IG5vZGUucGFyZW50DQogICAgICBsZXQgbGFiZWxzID0gW25vZGUubGFiZWxdDQogICAgICBsZXQgc3RhdHVzID0gWw0KICAgICAgICBkYXRhLkRhdGEuSXNfRGVlcGVuX0NoYW5nZQ0KICAgICAgICAgID8gJ+W3suWPmOabtCcNCiAgICAgICAgICA6IGRhdGEuRGF0YS5Jc19JbXBvcnRlZA0KICAgICAgICAgICAgPyAn5bey5a+85YWlJw0KICAgICAgICAgICAgOiAn5pyq5a+85YWlJw0KICAgICAgXQ0KICAgICAgbGV0IGxldmVsID0gMQ0KICAgICAgd2hpbGUgKGxldmVsIDwgbm9kZS5sZXZlbCkgew0KICAgICAgICBsYWJlbHMgPSBbLi4ubGFiZWxzLCBwYXJlbnROb2RlLmxhYmVsXQ0KICAgICAgICBzdGF0dXMgPSBbDQogICAgICAgICAgLi4uc3RhdHVzLA0KICAgICAgICAgIGRhdGEuRGF0YS5Jc19EZWVwZW5fQ2hhbmdlDQogICAgICAgICAgICA/ICflt7Llj5jmm7QnDQogICAgICAgICAgICA6IGRhdGEuRGF0YS5Jc19JbXBvcnRlZA0KICAgICAgICAgICAgICA/ICflt7Llr7zlhaUnDQogICAgICAgICAgICAgIDogJ+acquWvvOWFpScNCiAgICAgICAgXQ0KICAgICAgICBwYXJlbnROb2RlID0gcGFyZW50Tm9kZS5wYXJlbnQNCiAgICAgICAgbGV2ZWwrKw0KICAgICAgfQ0KICAgICAgbGFiZWxzID0gbGFiZWxzLmZpbHRlcigodikgPT4gISF2KQ0KICAgICAgc3RhdHVzID0gc3RhdHVzLmZpbHRlcigodikgPT4gISF2KQ0KICAgICAgbGV0IHJlc3VsdExhYmVsID0gdHJ1ZQ0KICAgICAgbGV0IHJlc3VsdFN0YXR1cyA9IHRydWUNCiAgICAgIGlmICh0aGlzLnN0YXR1c1R5cGUpIHsNCiAgICAgICAgcmVzdWx0U3RhdHVzID0gc3RhdHVzLnNvbWUoKHMpID0+IHMuaW5kZXhPZihzdGF0dXNWYWwpICE9PSAtMSkNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLnByb2plY3ROYW1lKSB7DQogICAgICAgIHJlc3VsdExhYmVsID0gbGFiZWxzLnNvbWUoKHMpID0+IHMuaW5kZXhPZihsYWJlbFZhbCkgIT09IC0xKQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIHJlc3VsdExhYmVsICYmIHJlc3VsdFN0YXR1cw0KICAgIH0sDQogICAgYXN5bmMgZ2V0RmlsZVR5cGUoKSB7DQogICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgIExldmVsOiB0aGlzLmxldmVsQ29kZSwNCiAgICAgICAgY2F0YWxvZ0NvZGU6ICdQTE1EZWVwZW5GaWxlcycNCiAgICAgIH0NCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IEdldEZpbGVUeXBlKHBhcmFtcykNCiAgICAgIC8vIOiOt+WPluaehOS7tuivpuWbvg0KICAgICAgY29uc3QgbGFibGUgPSBgJHt0aGlzLmxldmVsTmFtZX3or6blm75gDQogICAgICBjb25zdCBkYXRhID0gcmVzLkRhdGEuZmluZCgodikgPT4gdi5MYWJlbCA9PT0gbGFibGUpDQoNCiAgICAgIHRoaXMuY29tRHJhd0RhdGEgPSB7DQogICAgICAgIGlzU0hRRDogZmFsc2UsDQogICAgICAgIElkOiBkYXRhLklkLA0KICAgICAgICBuYW1lOiBkYXRhLkxhYmVsLA0KICAgICAgICBDYXRhbG9nX0NvZGU6IGRhdGEuQ29kZSwNCiAgICAgICAgQ29kZTogZGF0YS5EYXRhPy5FbmdsaXNoX05hbWUNCiAgICAgIH0NCg0KICAgICAgY29uc29sZS5sb2codGhpcy5jb21EcmF3RGF0YSwgJ2NvbURyYXdEYXRhJykNCiAgICB9LA0KICAgIC8vIOWbvue6uOWvvOWFpQ0KICAgIGhhbmRlbEltcG9ydCgpIHsNCiAgICAgIHRoaXMuJHJlZnMuY29tRHJhd2RpYWxvZ1JlZi5oYW5kbGVPcGVuKA0KICAgICAgICAnYWRkJywNCiAgICAgICAgdGhpcy5jb21EcmF3RGF0YSwNCiAgICAgICAgJycsDQogICAgICAgIGZhbHNlLA0KICAgICAgICB0aGlzLmN1c3RvbVBhcmFtcy5TeXNfUHJvamVjdF9JZCwNCiAgICAgICAgZmFsc2UNCiAgICAgICkNCiAgICB9LA0KICAgIC8vIOi9qOi/ueWbvg0KICAgIGhhbmRsZVRyYWNrKHJvdykgew0KICAgICAgY29uc29sZS5sb2cocm93LCAncm93JykNCiAgICAgIHRoaXMudHJhY2tEcmF3ZXIgPSB0cnVlDQogICAgICB0aGlzLnRyYWNrRHJhd2VyVGl0bGUgPSByb3cuQ29kZQ0KICAgICAgdGhpcy50cmFja0RyYXdlckRhdGEgPSByb3cNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAye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file": "index.vue", "sourceRoot": "src/views/PRO/section-list", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <div\r\n      v-loading=\"pgLoading\"\r\n      style=\"display: flex\"\r\n      class=\"h100\"\r\n      element-loading-text=\"加载中\"\r\n    >\r\n      <ExpandableSection v-model=\"showExpand\" :width=\"300\" class=\"cs-left fff\">\r\n        <div class=\"inner-wrapper\">\r\n          <div class=\"tree-search\">\r\n            <el-select\r\n              v-model=\"statusType\"\r\n              clearable\r\n              class=\"search-select\"\r\n              placeholder=\"导入状态选择\"\r\n            >\r\n              <el-option label=\"已导入\" value=\"已导入\" />\r\n              <el-option label=\"未导入\" value=\"未导入\" />\r\n              <el-option label=\"已变更\" value=\"已变更\" />\r\n            </el-select>\r\n            <el-input\r\n              v-model.trim=\"projectName\"\r\n              placeholder=\"关键词搜索\"\r\n              size=\"small\"\r\n              clearable\r\n              suffix-icon=\"el-icon-search\"\r\n              @blur=\"fetchTreeDataLocal\"\r\n              @clear=\"fetchTreeDataLocal\"\r\n              @keydown.enter.native=\"fetchTreeDataLocal\"\r\n            />\r\n          </div>\r\n          <el-divider class=\"cs-divider\" />\r\n          <div class=\"tree-x cs-scroll\">\r\n            <tree-detail\r\n              ref=\"tree\"\r\n              icon=\"icon-folder\"\r\n              is-custom-filter\r\n              :custom-filter-fun=\"customFilterFun\"\r\n              :loading=\"treeLoading\"\r\n              :tree-data=\"treeData\"\r\n              show-status\r\n              show-detail\r\n              :filter-text=\"filterText\"\r\n              :expanded-key=\"expandedKey\"\r\n              @handleNodeClick=\"handleNodeClick\"\r\n            >\r\n              <template #csLabel=\"{ showStatus, data }\">\r\n                <span\r\n                  v-if=\"!data.ParentNodes\"\r\n                  class=\"cs-blue\"\r\n                >({{ data.Code }})</span>{{ data.Label }}\r\n                <template v-if=\"showStatus && data.Label != '全部'\">\r\n                  <span v-if=\"data.Data.Is_Deepen_Change\" class=\"cs-tag redBg\">\r\n                    <i class=\"fourRed\">已变更</i></span>\r\n                  <span\r\n                    v-else\r\n                    :class=\"[\r\n                      'cs-tag',\r\n                      data.Data.Is_Imported == true ? 'greenBg' : 'orangeBg',\r\n                    ]\"\r\n                  >\r\n                    <i\r\n                      :class=\"[\r\n                        data.Data.Is_Imported == true\r\n                          ? 'fourGreen'\r\n                          : 'fourOrange',\r\n                      ]\"\r\n                    >{{\r\n                      data.Data.Is_Imported == true ? \"已导入\" : \"未导入\"\r\n                    }}</i>\r\n                  </span>\r\n                </template>\r\n              </template></tree-detail>\r\n          </div>\r\n        </div>\r\n      </ExpandableSection>\r\n      <div class=\"cs-right\" style=\"padding-right: 0\">\r\n        <div class=\"container\">\r\n          <div ref=\"searchDom\" class=\"cs-from\">\r\n            <div class=\"cs-search\">\r\n              <el-form\r\n                ref=\"customParams\"\r\n                :model=\"customParams\"\r\n                label-width=\"80px\"\r\n                class=\"demo-form-inline\"\r\n              >\r\n                <el-row>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item\r\n                      label-width=\"110px\"\r\n                      :label=\"levelName + '名称'\"\r\n                      prop=\"Names\"\r\n                    >\r\n                      <el-input\r\n                        v-model=\"names\"\r\n                        clearable\r\n                        style=\"width: 100%\"\r\n                        class=\"input-with-select\"\r\n                        placeholder=\"请输入内容\"\r\n                        size=\"small\"\r\n                      >\r\n                        <el-select\r\n                          slot=\"prepend\"\r\n                          v-model=\"nameMode\"\r\n                          placeholder=\"请选择\"\r\n                          style=\"width: 100px\"\r\n                        >\r\n                          <el-option label=\"模糊搜索\" :value=\"1\" />\r\n                          <el-option label=\"精确搜索\" :value=\"2\" />\r\n                        </el-select>\r\n                      </el-input>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item\r\n                      class=\"mb0\"\r\n                      label=\"批次\"\r\n                      prop=\"InstallUnit_Id\"\r\n                    >\r\n                      <el-select\r\n                        v-model=\"customParams.InstallUnit_Id\"\r\n                        multiple\r\n                        filterable\r\n                        clearable\r\n                        placeholder=\"请选择\"\r\n                        style=\"width: 100%\"\r\n                        :disabled=\"!Boolean(customParams.Area_Id)\"\r\n                      >\r\n                        <el-option\r\n                          v-for=\"item in installUnitIdNameList\"\r\n                          :key=\"item.Id\"\r\n                          :label=\"item.Name\"\r\n                          :value=\"item.Id\"\r\n                        />\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item\r\n                      label-width=\"92px\"\r\n                      label=\"是否直发件\"\r\n                      prop=\"IsDirect\"\r\n                    >\r\n                      <el-select\r\n                        v-model=\"customParams.IsDirect\"\r\n                        style=\"width: 100%\"\r\n                        placeholder=\"请选择\"\r\n                        clearable\r\n                      >\r\n                        <el-option\r\n                          v-for=\"item in directOption\"\r\n                          :key=\"item.value\"\r\n                          :label=\"item.label\"\r\n                          :value=\"item.value\"\r\n                        />\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item label=\"规格\" prop=\"Spec\">\r\n                      <el-input\r\n                        v-model=\"customParams.Spec\"\r\n                        placeholder=\"请输入\"\r\n                        clearable\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item\r\n                      label=\"材质\"\r\n                      prop=\"Texture\"\r\n                    >\r\n                      <el-input\r\n                        v-model=\"customParams.Texture\"\r\n                        placeholder=\"请输入\"\r\n                        clearable\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item\r\n                      label=\"操作人\"\r\n                      prop=\"DateName\"\r\n                    >\r\n                      <el-input\r\n                        v-model=\"customParams.DateName\"\r\n                        style=\"width: 100%\"\r\n                        placeholder=\"请输入\"\r\n                        clearable\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item class=\"mb0\" label-width=\"16px\">\r\n                      <el-button\r\n                        type=\"primary\"\r\n                        @click=\"handelsearch()\"\r\n                      >搜索\r\n                      </el-button>\r\n                      <el-button @click=\"handelsearch('reset')\">重置</el-button>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form>\r\n            </div>\r\n          </div>\r\n          <div class=\"fff cs-z-tb-wrapper\">\r\n            <div class=\"cs-button-box\">\r\n              <template>\r\n                <el-button\r\n                  :disabled=\"!selectList.length\"\r\n                  @click=\"handleExport\"\r\n                >导出{{ levelName }}</el-button>\r\n                <el-button\r\n                  :disabled=\"!selectList.length || selectList.some(item=>item.stopFlag)\"\r\n                  type=\"primary\"\r\n                  plain\r\n                  @click=\"handleBatchEdit\"\r\n                >批量编辑</el-button>\r\n                <!-- <el-button\r\n                  type=\"danger\"\r\n                  plain\r\n                  :disabled=\"!selectList.length\"\r\n                  @click=\"handleDelete\"\r\n                  >删除选中</el-button\r\n                > -->\r\n                <el-button\r\n                  type=\"success\"\r\n                  plain\r\n                  :disabled=\"!Boolean(customParams.Sys_Project_Id)\"\r\n                  @click=\"handelImport\"\r\n                >图纸导入\r\n                </el-button>\r\n              </template>\r\n            </div>\r\n            <div v-loading=\"countLoading\" class=\"info-box\">\r\n              <div class=\"cs-col\">\r\n                <span><span class=\"info-label\">深化总数</span><i>{{ SteelAmountTotal }} 件</i></span>\r\n                <span><span class=\"info-label\">深化总量</span><i>{{ SteelAllWeightTotal }}t</i></span>\r\n              </div>\r\n              <div class=\"cs-col\">\r\n                <span><span class=\"info-label\">排产总数</span><i>{{ SchedulingNumTotal }} 件</i></span>\r\n                <span><span class=\"info-label\">排产总量</span><i>{{ SchedulingAllWeightTotal }} t</i></span>\r\n              </div>\r\n              <div class=\"cs-col\" style=\"cursor: pointer;\" @click=\"getProcessData()\">\r\n                <span><span class=\"info-label\">完成总数</span><i>{{ FinishCountTotal }} 件</i></span>\r\n                <span><span class=\"info-label\">完成总量</span><i>{{ FinishWeightTotal }} t</i></span>\r\n              </div>\r\n              <div class=\"cs-col\">\r\n                <span><span class=\"info-label\">直发件总数</span><i>{{ DirectCountTotal }} 件</i></span>\r\n                <span><span class=\"info-label\">直发件总量</span><i>{{ DirectWeightTotal }} t</i></span>\r\n              </div>\r\n            </div>\r\n            <div class=\"tb-container\">\r\n              <vxe-table\r\n                v-loading=\"tbLoading\"\r\n                :empty-render=\"{name: 'NotData'}\"\r\n                show-header-overflow\r\n                element-loading-spinner=\"el-icon-loading\"\r\n                element-loading-text=\"拼命加载中\"\r\n                empty-text=\"暂无数据\"\r\n                class=\"cs-vxe-table\"\r\n                height=\"100%\"\r\n                align=\"left\"\r\n                stripe\r\n                :data=\"tbData\"\r\n                resizable\r\n                :tooltip-config=\"{ enterable: true }\"\r\n                :row-config=\"{ isHover: true }\"\r\n                @checkbox-all=\"tbSelectChange\"\r\n                @checkbox-change=\"tbSelectChange\"\r\n              >\r\n                <vxe-column fixed=\"left\" type=\"checkbox\" width=\"44\" />\r\n                <vxe-column\r\n                  v-for=\"(item, index) in columns\"\r\n                  :key=\"index\"\r\n                  :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                  show-overflow=\"tooltip\"\r\n                  sortable\r\n                  :align=\"item.Align\"\r\n                  :field=\"item.Code\"\r\n                  :title=\"item.Display_Name\"\r\n                  :width=\"item.Width ? item.Width : 120\"\r\n                  :min-width=\"item.Width\"\r\n                >\r\n                  <template #default=\"{ row }\">\r\n                    <div v-if=\"item.Code == 'Code'\">\r\n                      <el-tag v-if=\"row.Is_Change\" style=\"margin-right: 8px;\" type=\"danger\">变</el-tag>\r\n                      <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                      <el-link type=\"primary\" @click=\"getPartInfo(row)\"> {{ row[item.Code] | displayValue }}</el-link>\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'IsDirect'\">\r\n                      <el-tag v-if=\"row.IsDirect === true\">是</el-tag>\r\n                      <el-tag v-else type=\"danger\">否</el-tag>\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'Is_Component'\">\r\n                      <span>\r\n                        <!--                      这列表叫是否非直发件 -->\r\n                        <el-tag\r\n                          v-if=\"row.Is_Component === 'True'\"\r\n                          type=\"danger\"\r\n                        >是</el-tag>\r\n                        <el-tag v-else type=\"success\">否</el-tag>\r\n                      </span>\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'Deep_Material'\">\r\n                      <el-link\r\n                        type=\"primary\"\r\n                        @click=\"handleDeepMaterial(row)\"\r\n                      >查看</el-link>\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'Is_Trace'\">\r\n                      <span>\r\n                        <el-tag\r\n                          v-if=\"row.Is_Trace\"\r\n                          type=\"success\"\r\n                        >是</el-tag>\r\n                        <el-tag v-else type=\"danger\">否</el-tag>\r\n                      </span>\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'Launch_Time'\">\r\n                      {{ row[item.Code] | timeFormat }}\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'Num' && row[item.Code] > 0\">\r\n                      <span v-if=\"row[item.Code]\"> {{ row[item.Code] | displayValue }}件</span>\r\n                      <span v-else>-</span>\r\n                    </div>\r\n                    <div\r\n                      v-else-if=\"\r\n                        item.Code == 'Schduling_Count' && row[item.Code] > 0\r\n                      \"\r\n                    >\r\n                      <el-link\r\n                        v-if=\"row[item.Code]\"\r\n                        type=\"primary\"\r\n                        @click=\"handelSchduling(row)\"\r\n                      > {{ row[item.Code] | displayValue }}件</el-link>\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'Drawing'\">\r\n                      <span\r\n                        v-if=\"row.Drawing !== '暂无'\"\r\n                        style=\"color: #298dff; cursor: pointer\"\r\n                        @click=\"getPartInfo(row)\"\r\n                      > {{ row[item.Code] | displayValue }}\r\n                      </span>\r\n                      <span v-else> {{ row[item.Code] | displayValue }}</span>\r\n                    </div>\r\n                    <div v-else-if=\"item.Code == 'Part'\">\r\n                      <el-link\r\n                        type=\"primary\"\r\n                        @click=\"handlePartList(row)\"\r\n                      >查看</el-link>\r\n                    </div>\r\n                    <div v-else>\r\n                      <span>{{ row[item.Code] | displayValue }}</span>\r\n                    </div>\r\n                  </template>\r\n                </vxe-column>\r\n                <vxe-column\r\n                  fixed=\"right\"\r\n                  title=\"操作\"\r\n                  width=\"150\"\r\n                  show-overflow\r\n                >\r\n                  <template #default=\"{ row }\">\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"handleView(row)\"\r\n                    >详情</el-button>\r\n                    <el-button\r\n                      :disabled=\"row.stopFlag\"\r\n                      type=\"text\"\r\n                      @click=\"handleEdit(row)\"\r\n                    >编辑</el-button>\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"handleTrack(row)\"\r\n                    >轨迹图\r\n                    </el-button>\r\n                  </template>\r\n                </vxe-column>\r\n              </vxe-table>\r\n            </div>\r\n            <div class=\"cs-bottom\">\r\n              <Pagination\r\n                class=\"cs-table-pagination\"\r\n                :total=\"total\"\r\n                max-height=\"100%\"\r\n                :page-sizes=\"tablePageSize\"\r\n                :page.sync=\"queryInfo.Page\"\r\n                :limit.sync=\"queryInfo.PageSize\"\r\n                layout=\"total, sizes, prev, pager, next, jumper\"\r\n                @pagination=\"changePage\"\r\n              >\r\n                <!--                <span class=\"pg-input\">\r\n                  <el-select\r\n                    v-model.number=\"queryInfo.PageSize\"\r\n                    allow-create\r\n                    filterable\r\n                    default-first-option\r\n                    @change=\"changePage\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"(item, index) in customPageSize\"\r\n                      :key=\"index\"\r\n                      :label=\"`${item}条/页`\"\r\n                      :value=\"item\"\r\n                    />\r\n                  </el-select>\r\n                </span>-->\r\n              </Pagination>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"card\" />\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      ref=\"content\"\r\n      v-el-drag-dialog\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"width\"\r\n      class=\"z-dialog\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :select-list=\"selectList\"\r\n        :custom-params=\"customDialogParams\"\r\n        :type-id=\"customParams.TypeId\"\r\n        :type-entity=\"typeEntity\"\r\n        :project-id=\"customParams.Project_Id\"\r\n        :sys-project-id=\"customParams.Project_Id\"\r\n        @close=\"handleClose\"\r\n        @refresh=\"fetchData\"\r\n      />\r\n    </el-dialog>\r\n    <bimdialog\r\n      ref=\"dialog\"\r\n      :type-entity=\"typeEntity\"\r\n      :area-id=\"customParams.Area_Id\"\r\n      :project-id=\"customParams.Project_Id\"\r\n      @getData=\"fetchData\"\r\n      @getTreeData=\"fetchTreeData\"\r\n    />\r\n\r\n    <el-drawer\r\n      :visible.sync=\"drawersull\"\r\n      direction=\"btt\"\r\n      size=\"100%\"\r\n      destroy-on-close\r\n    >\r\n      <iframe\r\n        v-if=\"templateUrl\"\r\n        id=\"fullFrame\"\r\n        :src=\"templateUrl\"\r\n        frameborder=\"0\"\r\n        style=\"width: 96%; margin-left: 2%; height: 70vh; margin-top: 2%\"\r\n      />\r\n    </el-drawer>\r\n\r\n    <el-drawer\r\n      :visible.sync=\"trackDrawer\"\r\n      direction=\"rtl\"\r\n      size=\"30%\"\r\n      destroy-on-close\r\n      custom-class=\"trackDrawerClass\"\r\n    >\r\n      <template #title>\r\n        <div>\r\n          <span>{{ trackDrawerTitle }}</span>\r\n          <span style=\"margin-left: 24px\">{{ trackDrawerData.Num }}</span>\r\n        </div>\r\n      </template>\r\n      <TracePlot :track-drawer-data=\"trackDrawerData\" />\r\n    </el-drawer>\r\n\r\n    <comDrawdialog ref=\"comDrawdialogRef\" @getData=\"fetchData\" />\r\n    <modelDrawing ref=\"modelDrawingRef\" type=\"部件\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  Deletepart,\r\n  ExportPlanpartInfo,\r\n  ExportPlanpartcountInfo,\r\n  DeletepartByfindkeywodes\r\n} from '@/api/plm/production'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nimport {\r\n  GetUnitPageList,\r\n  GetUnitWeightList,\r\n  ExportPlanUnitInfo\r\n} from '@/api/plm/section'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\r\nimport {\r\n  GetProjectAreaTreeList,\r\n  GetInstallUnitIdNameList\r\n} from '@/api/PRO/project'\r\n\r\nimport TreeDetail from '@/components/TreeDetail'\r\nimport TopHeader from '@/components/TopHeader'\r\nimport comImport from './component/Import'\r\nimport ComponentsHistory from './component/ComponentsHistory'\r\nimport comImportByFactory from './component/ImportByFactory'\r\nimport HistoryExport from './component/HistoryExport'\r\nimport BatchEdit from './component/BatchEditor'\r\nimport ComponentPack from './component/ComponentPack/index'\r\nimport Edit from './component/Edit'\r\nimport OneClickGeneratePack from './component/OneClickGeneratePack'\r\nimport GeneratePack from './component/GeneratePack'\r\nimport DeepMaterial from './component/DeepMaterial'\r\nimport Schduling from './component/Schduling'\r\nimport PartList from './component/PartList.vue'\r\nimport ProcessData from './component/ProcessData.vue'\r\n\r\nimport elDragDialog from '@/directive/el-drag-dialog'\r\nimport Pagination from '@/components/Pagination'\r\nimport { timeFormat } from '@/filters'\r\n// import { Column, Header, Table, Tooltip } from 'vxe-table'\r\n// import Vue from 'vue'\r\nimport AuthButtons from '@/mixins/auth-buttons'\r\nimport bimdialog from './component/bimdialog'\r\nimport sysUseType from '@/directive/sys-use-type/index.js'\r\nimport { promptBox } from './component/messageBox'\r\n\r\nimport { combineURL } from '@/utils'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport { parseOssUrl } from '@/utils/file'\r\n\r\nimport { baseUrl } from '@/utils/baseurl'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { GetSteelCadAndBimId } from '@/api/PRO/component'\r\nimport { getConfigure } from '@/api/user'\r\nimport { GetFileType } from '@/api/sys'\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport comDrawdialog from '@/views/PRO/production-order/deepen-files/dialog' // 深化文件-零件详图导入\r\nimport TracePlot from './component/TracePlot'\r\n\r\nimport modelDrawing from '@/views/PRO/components/modelDrawing.vue'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\n\r\n// Vue.use(Header).use(Column).use(Tooltip).use(Table)\r\nconst SPLIT_SYMBOL = '$_$'\r\nexport default {\r\n  name: 'PROSectionList',\r\n  directives: { elDragDialog, sysUseType },\r\n  components: {\r\n    ExpandableSection,\r\n    TreeDetail,\r\n    TopHeader,\r\n    comImport,\r\n    comImportByFactory,\r\n    BatchEdit,\r\n    HistoryExport,\r\n    GeneratePack,\r\n    Edit,\r\n    ComponentPack,\r\n    OneClickGeneratePack,\r\n    Pagination,\r\n    bimdialog,\r\n    ComponentsHistory,\r\n    DeepMaterial,\r\n    Schduling,\r\n    comDrawdialog,\r\n    TracePlot,\r\n    PartList,\r\n    modelDrawing,\r\n    ProcessData\r\n  },\r\n  mixins: [AuthButtons],\r\n  data() {\r\n    return {\r\n      allStopFlag: false,\r\n      showExpand: true,\r\n      drawer: false,\r\n      drawersull: false,\r\n      iframeKey: '',\r\n      fullscreenid: '',\r\n      iframeUrl: '',\r\n      fullbimid: '',\r\n      expandedKey: '', // -1是全部\r\n      tablePageSize: tablePageSize,\r\n      partTypeOption: [],\r\n      directOption: [\r\n        { value: true, label: '是' },\r\n        { value: false, label: '否' }\r\n      ],\r\n      treeData: [],\r\n      treeLoading: true,\r\n      projectName: '',\r\n      statusType: '',\r\n      searchHeight: 0,\r\n      tbData: [],\r\n      total: 0,\r\n      tbLoading: false,\r\n      pgLoading: false,\r\n      countLoading: false,\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: 10,\r\n        ParameterJson: []\r\n      },\r\n      customPageSize: [10, 20, 50, 100],\r\n      installUnitIdNameList: [], // 批次数组\r\n      nameMode: 1,\r\n\r\n      customParams: {\r\n        TypeId: '',\r\n        Type_Name: '',\r\n        Code: '',\r\n        Code_Like: '',\r\n        Spec: '',\r\n        DateName: '',\r\n        Texture: '',\r\n        // Keywords01: 'Code',\r\n        // Keywords01Value: '',\r\n        // Keywords02: 'Spec',\r\n        // Keywords02Value: '',\r\n        // Keywords03: 'Length',\r\n        // Keywords03Value: '',\r\n        // Keywords04: 'Texture',\r\n        // Keywords04Value: '',\r\n        InstallUnit_Id: [],\r\n        IsDirect: null, // 是否直发\r\n        Part_Type_Id: '',\r\n        InstallUnit_Name: '',\r\n        Sys_Project_Id: '',\r\n        Project_Id: '',\r\n        Area_Id: '',\r\n        Project_Name: '',\r\n        Area_Name: ''\r\n      },\r\n      names: '',\r\n      customDialogParams: {},\r\n      dialogVisible: false,\r\n      currentComponent: '',\r\n      selectList: [],\r\n      factoryOption: [],\r\n      projectList: [],\r\n      typeOption: [],\r\n      columns: [],\r\n      columnsOption: [\r\n        // { Display_Name: '零件名称', Code: 'Code' },\r\n        // { Display_Name: '规格', Code: 'Spec' },\r\n        // { Display_Name: '长度', Code: 'Length' },\r\n        // { Display_Name: '材质', Code: 'Texture' },\r\n        // { Display_Name: '深化数量', Code: 'Num' },\r\n        // { Display_Name: '排产数量', Code: 'Schduling_Count' },\r\n        // { Display_Name: '单重', Code: 'Weight' },\r\n        // { Display_Name: '总重', Code: 'Total_Weight' },\r\n        // { Display_Name: '形状', Code: 'Shape' },\r\n        // { Display_Name: '构件名称', Code: 'Component_Code' },\r\n        // { Display_Name: '操作人', Code: 'datename' },\r\n        // { Display_Name: '操作时间', Code: 'Exdate' }\r\n      ],\r\n      title: '',\r\n      width: '60%',\r\n      tipLabel: '',\r\n      monomerList: [],\r\n      mode: '',\r\n      isMonomer: true,\r\n      historyVisible: false,\r\n      sysUseType: undefined,\r\n      deleteContent: true,\r\n      SteelAmountTotal: 0, // 深化总量\r\n      SchedulingNumTotal: 0, // 排产总量\r\n      SteelAllWeightTotal: 0, // 深化总重\r\n      SchedulingAllWeightTotal: 0, // 排产总重\r\n      FinishCountTotal: 0, // 完成数量\r\n      FinishWeightTotal: 0, // 完成重量\r\n      DirectCountTotal: 0, // 直发件总数\r\n      DirectWeightTotal: 0, // 直发件总量\r\n      Unit: '',\r\n      fileBim: '',\r\n      Proportion: 0, // 专业的单位换算\r\n      command: 'cover',\r\n      currentLastLevel: false,\r\n      templateUrl: '',\r\n      currentNode: {},\r\n      comDrawData: {},\r\n      trackDrawer: false,\r\n      trackDrawerTitle: '',\r\n      trackDrawerData: {},\r\n      drawingActive: '',\r\n      drawingDataList: [],\r\n      levelName: '',\r\n      levelCode: ''\r\n    }\r\n  },\r\n  computed: {\r\n    showP9Btn() {\r\n      return this.AuthButtons.buttons.some((item) => item.Code === 'p9BtnAdd')\r\n    },\r\n    typeEntity() {\r\n      return this.typeOption.find((i) => i.Id === this.customParams.TypeId)\r\n    },\r\n    PID() {\r\n      return this.projectList.find(\r\n        (i) => i.Sys_Project_Id === this.customParams.Project_Id\r\n      )?.Id\r\n    },\r\n    filterText() {\r\n      return this.projectName + SPLIT_SYMBOL + this.statusType\r\n    }\r\n  },\r\n  watch: {\r\n    'customParams.TypeId': function(newValue, oldValue) {\r\n      console.log({ oldValue })\r\n      if (oldValue && oldValue !== '0') {\r\n        this.fetchData()\r\n      }\r\n    },\r\n    names(n, o) {\r\n      this.changeMode()\r\n    },\r\n    nameMode(n, o) {\r\n      this.changeMode()\r\n    }\r\n  },\r\n  mounted() {\r\n    this.pgLoading = true\r\n    console.log(this.columns)\r\n    // this.getUnitWeightList()\r\n    this.searchHeight = this.$refs.searchDom.offsetHeight + 327\r\n  },\r\n  async created() {\r\n    const level = +this.$route.query.level\r\n    const { currentBOMInfo } = await GetBOMInfo(level)\r\n    console.log('list', currentBOMInfo)\r\n    this.levelName = currentBOMInfo?.Display_Name\r\n    this.levelCode = currentBOMInfo?.Code\r\n    await this.getTypeList()\r\n    // await this.fetchData()\r\n    this.fetchTreeData()\r\n    this.getFileType()\r\n    if (this.Keywords01Value === '是') {\r\n      console.log('this.Keywords01Value', this.Keywords01Value)\r\n    }\r\n  },\r\n  methods: {\r\n    changeMode() {\r\n      if (this.nameMode === 1) {\r\n        this.customParams.Code_Like = this.names\r\n        this.customParams.Code = ''\r\n      } else {\r\n        this.customParams.Code_Like = ''\r\n        this.customParams.Code = this.names.replace(/\\s+/g, '\\n')\r\n      }\r\n    },\r\n\r\n    // 项目区域数据集\r\n    fetchTreeData() {\r\n      GetProjectAreaTreeList({ Type: 0, MenuId: this.$route.meta.Id, projectName: this.projectName, Level: this.levelCode }).then((res) => {\r\n        // const resAll = [\r\n        //   {\r\n        //     ParentNodes: null,\r\n        //     Id: '-1',\r\n        //     Code: '全部',\r\n        //     Label: '全部',\r\n        //     Level: null,\r\n        //     Data: {},\r\n        //     Children: []\r\n        //   }\r\n        // ]\r\n        // const resData = resAll.concat(res.Data)\r\n        if (!res.IsSucceed) {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.treeLoading = false\r\n          this.treeData = []\r\n          return\r\n        }\r\n        if (res.Data.length === 0) {\r\n          this.treeLoading = false\r\n          return\r\n        }\r\n        const resData = res.Data\r\n        resData.map((item) => {\r\n          if (item.Children.length === 0) {\r\n            item.Is_Imported = false\r\n          } else {\r\n            item.Data.Is_Imported = item.Children.some((ich) => {\r\n              return ich.Data.Is_Imported === true\r\n            })\r\n            item.Is_Directory = true\r\n            item.Children.map((it) => {\r\n              if (it.Children.length > 0) {\r\n                it.Is_Directory = true\r\n              }\r\n            })\r\n          }\r\n        })\r\n        this.treeData = resData\r\n        if (Object.keys(this.currentNode).length === 0) {\r\n          // this.fetchData()\r\n          this.setKey()\r\n        } else {\r\n          this.handleNodeClick(this.currentNode)\r\n        }\r\n        this.treeLoading = false\r\n        // this.expandedKey = this.customParams.Area_Id ? this.customParams.Area_Id : this.customParams.Project_Id ? this.customParams.Project_Id : resData[0].Id // '-1'\r\n        // this.customParams.Sys_Project_Id = this.customParams.Sys_Project_Id || resData[0].Data.Sys_Project_Id\r\n        // this.customParams.Project_Id = this.customParams.Project_Id || resData[0].Data.Id\r\n        // this.customParams.Area_Name = ''\r\n        // this.treeLoading = false\r\n        // this.fetchData()\r\n      })\r\n    },\r\n    // 设置默认选中第一个区域末级节点\r\n    setKey() {\r\n      const deepFilter = (tree) => {\r\n        for (let i = 0; i < tree.length; i++) {\r\n          const item = tree[i]\r\n          const { Data, Children } = item\r\n          console.log(Data)\r\n          if (Data.ParentId && !Children?.length) {\r\n            console.log(Data, '????')\r\n            this.currentNode = Data\r\n            this.handleNodeClick(item)\r\n            return\r\n          } else {\r\n            if (Children && Children.length > 0) {\r\n              return deepFilter(Children)\r\n            } else {\r\n              this.handleNodeClick(item)\r\n              return\r\n            }\r\n          }\r\n        }\r\n      }\r\n      return deepFilter(this.treeData)\r\n    },\r\n    // 选中左侧项目节点\r\n    handleNodeClick(data) {\r\n      this.handelsearch('reset', false)\r\n      this.currentNode = data\r\n      this.expandedKey = data.Id\r\n      this.customParams.InstallUnit_Id = []\r\n      const dataId = data.Id === '-1' ? '' : data.Id\r\n      console.log('nodeData', data)\r\n      if (data.ParentNodes) {\r\n        this.customParams.Project_Id = data.Data.Project_Id\r\n        this.customParams.Area_Id = data.Id\r\n        this.customParams.Area_Name = data.Data.Name\r\n        this.customParams.Sys_Project_Id = data.Data.Sys_Project_Id\r\n      } else {\r\n        this.customParams.Project_Id = dataId\r\n        this.customParams.Area_Id = ''\r\n        this.customParams.Area_Name = data.Data.Name\r\n        this.customParams.Sys_Project_Id = data.Data.Sys_Project_Id\r\n      }\r\n      console.log(\r\n        this.customParams.Sys_Project_Id,\r\n        'this.customParams.Sys_Project_Id============11111'\r\n      )\r\n      console.log(\r\n        this.customParams.Area_Id,\r\n        'this.customParams.Area_Id============11111'\r\n      )\r\n      this.currentLastLevel = !!(data.Data.Level && data.Children.length === 0)\r\n      if (this.currentLastLevel) {\r\n        this.customParams.Project_Name = data.Data?.Project_Name\r\n        this.customParams.Area_Name = data.Label\r\n      }\r\n      this.queryInfo.Page = 1\r\n      this.pgLoading = true\r\n      this.fetchData()\r\n      console.log(this.customParams.Area_Id)\r\n      this.getInstallUnitIdNameList(dataId, data)\r\n    },\r\n\r\n    // 获取批次\r\n    getInstallUnitIdNameList(id, data) {\r\n      if (id === '' || data.Children.length > 0) {\r\n        this.installUnitIdNameList = []\r\n      } else {\r\n        GetInstallUnitIdNameList({ Area_Id: id, Level: this.levelCode }).then((res) => {\r\n          this.installUnitIdNameList = res.Data\r\n        })\r\n      }\r\n    },\r\n    // 工序完成量\r\n    getProcessData() {\r\n      const customParamsData = JSON.parse(JSON.stringify(this.customParams))\r\n      const InstallUnit_Ids = customParamsData.InstallUnit_Id.join(',')\r\n      delete customParamsData.InstallUnit_Id\r\n\r\n      this.width = '40%'\r\n      this.generateComponent('部件工序完成量', 'ProcessData')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(customParamsData, InstallUnit_Ids, this.selectList.map((v) => v.Part_Aggregate_Id).toString())\r\n      })\r\n    },\r\n    getTableConfig(code) {\r\n      return new Promise((resolve) => {\r\n        GetGridByCode({\r\n          code:\r\n            code +\r\n            ',' +\r\n            this.typeOption.find((i) => i.Id === this.customParams.TypeId).Code\r\n        }).then((res) => {\r\n          const { IsSucceed, Data, Message } = res\r\n          if (IsSucceed) {\r\n            if (!Data) {\r\n              this.$message.error('当前专业没有配置相对应表格')\r\n              this.tbLoading = true\r\n              return\r\n            }\r\n            this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n            const list = Data.ColumnList || []\r\n            const sortList = list.sort((a, b) => a.Sort - b.Sort)\r\n            this.columns = sortList\r\n              .filter((v) => v.Is_Display)\r\n              .map((item) => {\r\n                if (item.Code === 'Code') {\r\n                  item.fixed = 'left'\r\n                }\r\n\r\n                return item\r\n              })\r\n            this.queryInfo.PageSize = +Data.Grid.Row_Number || 20\r\n            resolve(this.columns)\r\n            console.log(this.columns)\r\n            const selectOption = JSON.parse(JSON.stringify(this.columns))\r\n            console.log(selectOption)\r\n            this.columnsOption = selectOption.filter((v) => {\r\n              return (\r\n                v.Display_Name !== '操作时间' &&\r\n                v.Display_Name !== '模型ID' &&\r\n                v.Display_Name !== '深化资料' &&\r\n                v.Display_Name !== '备注' &&\r\n                v.Display_Name !== '排产数量' &&\r\n                v.Code.indexOf('Attr') === -1 &&\r\n                v.Display_Name !== '批次'\r\n              )\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    async fetchList() {\r\n      const customParamsData = JSON.parse(JSON.stringify(this.customParams))\r\n      const InstallUnit_Ids = customParamsData.InstallUnit_Id.join(',')\r\n      delete customParamsData.InstallUnit_Id\r\n\r\n      await GetUnitPageList({\r\n        Level: this.levelCode,\r\n        ...this.queryInfo,\r\n        ...customParamsData,\r\n        Code: customParamsData.Code.trim().replaceAll(' ', '\\n'),\r\n        InstallUnit_Ids: InstallUnit_Ids\r\n      })\r\n        .then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.queryInfo.PageSize = res.Data.PageSize\r\n            this.total = res.Data.TotalCount\r\n            this.tbData = res.Data.Data.map((v) => {\r\n              v.Is_Main = v.Is_Main ? '是' : '否'\r\n              v.Exdate = timeFormat(v.Exdate, '{y}-{m}-{d} {h}:{i}:{s}')\r\n              // console.log(v)\r\n              return v\r\n            })\r\n            this.selectList = []\r\n            this.getUnitWeightList()\r\n            this.getStopList()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n        .finally(() => {\r\n          this.tbLoading = false\r\n          this.pgLoading = false\r\n        })\r\n    },\r\n    async getStopList() {\r\n      const submitObj = this.tbData.map(item => {\r\n        return {\r\n          Id: item.Part_Aggregate_Id,\r\n          Type: 3\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = item.Is_Stop !== null\r\n          })\r\n          this.tbData.forEach(row => {\r\n            if (stopMap[row.Part_Aggregate_Id]) {\r\n              this.$set(row, 'stopFlag', stopMap[row.Part_Aggregate_Id])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async fetchData() {\r\n      console.log('更新列表')\r\n      // 分开获取，提高接口速度\r\n      const activeName = `plm_level${this.levelCode}_page_list`\r\n      await this.getTableConfig(activeName)\r\n      this.tbLoading = true\r\n      this.fetchList().then((res) => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    async changePage() {\r\n      this.tbLoading = true\r\n      if (\r\n        typeof this.queryInfo.PageSize !== 'number' ||\r\n        this.queryInfo.PageSize < 1\r\n      ) {\r\n        this.queryInfo.PageSize = 10\r\n      }\r\n      this.fetchList().then((res) => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    // tbSelectChange(array) {\r\n    //   console.log('array', array)\r\n    //   this.selectList = array.records\r\n    //   console.log('this.selectList', this.selectList)\r\n    // },\r\n    getTbData(data) {\r\n      const { YearAllWeight, YearSteel, CountInfo } = data\r\n      // this.tipLabel = `累计上传构件${YearSteel}件，总重${YearAllWeight}t。`\r\n      this.tipLabel = CountInfo\r\n    },\r\n    async getTypeList() {\r\n      let res = null\r\n      let data = null\r\n      res = await GetFactoryProfessionalByCode({\r\n        factoryId: localStorage.getItem('CurReferenceId')\r\n      })\r\n      data = res.Data\r\n      if (res.IsSucceed) {\r\n        this.typeOption = Object.freeze(data)\r\n        if (this.typeOption.length > 0) {\r\n          this.Proportion = data[0].Proportion\r\n          this.Unit = data[0].Unit\r\n          this.customParams.TypeId = this.typeOption[0]?.Id\r\n          this.customParams.Type_Name = this.typeOption[0]?.Name\r\n        }\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    handleDelete() {\r\n      this.$confirm('此操作将删除选择数据, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          Deletepart({\r\n            Level: this.levelCode,\r\n            ids: this.selectList.map((v) => v.Part_Aggregate_Id).toString()\r\n          }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.fetchData()\r\n              this.$message({\r\n                message: '删除成功',\r\n                type: 'success'\r\n              })\r\n              this.getUnitWeightList()\r\n              this.fetchTreeData()\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n    handleEdit(row) {\r\n      this.width = '45%'\r\n      this.generateComponent('编辑部件', 'Edit')\r\n      this.$nextTick((_) => {\r\n        row.isReadOnly = false\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n    handleBatchEdit() {\r\n      const SchedulArr = this.selectList.filter((item) => {\r\n        return item.Schduling_Count != null && item.Schduling_Count > 0\r\n      })\r\n      if (SchedulArr.length > 0) {\r\n        this.$message({\r\n          type: 'error',\r\n          message: '选中行包含已排产的部件,编辑信息需要进行变更操作'\r\n        })\r\n      } else {\r\n        this.width = '40%'\r\n        this.generateComponent('批量编辑', 'BatchEdit')\r\n        this.$nextTick((_) => {\r\n          this.$refs['content'].init(this.selectList, this.columnsOption)\r\n        })\r\n      }\r\n    },\r\n    handleView(row) {\r\n      this.width = '45%'\r\n      this.generateComponent('详情', 'Edit')\r\n      this.$nextTick((_) => {\r\n        row.isReadOnly = true\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n    async handleExport() {\r\n      const obj = {\r\n        Level: this.levelCode,\r\n        Part_Aggregate_Ids: this.selectList\r\n          .map((v) => v.Part_Aggregate_Id)\r\n          .toString(),\r\n        ProfessionalCode: this.typeEntity.Code\r\n      }\r\n      ExportPlanUnitInfo(obj).then((res) => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    // 覆盖导入 or 新增导入\r\n    handleCommand(command) {\r\n      console.log(command, 'command')\r\n      this.command = command\r\n      this.deepListImport()\r\n    },\r\n    deepListImport() {\r\n      const fileType = {\r\n        Catalog_Code: 'PLMDeepenFiles',\r\n        Code: this.typeEntity.Code,\r\n        name: this.typeEntity.Name\r\n      }\r\n      this.$refs.dialog.handleOpen(\r\n        'add',\r\n        fileType,\r\n        null,\r\n        true,\r\n        this.PID,\r\n        this.command,\r\n        this.customParams\r\n      )\r\n    },\r\n    async handleAllDelete() {\r\n      console.log(this.customParams.Project_Id)\r\n      if (this.customParams.Project_Id) {\r\n        await promptBox({ title: '删除' })\r\n        await DeletepartByfindkeywodes({\r\n          Level: this.levelCode,\r\n          ...this.customParams,\r\n          ...this.queryInfo\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message.success('删除成功')\r\n            this.fetchData()\r\n            this.fetchTreeData()\r\n          } else {\r\n            this.$message.error(res.Message)\r\n          }\r\n        })\r\n      } else {\r\n        this.$message.warning('请先选择项目')\r\n      }\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    generateComponent(title, component) {\r\n      this.title = title\r\n      this.currentComponent = component\r\n      this.dialogVisible = true\r\n    },\r\n    // 点击搜索\r\n    handelsearch(reset, hasSearch = true) {\r\n      this.deleteContent = false\r\n      if (reset) {\r\n        this.$refs.customParams.resetFields()\r\n        this.deleteContent = true\r\n        this.names = ''\r\n      }\r\n      hasSearch && this.fetchData()\r\n    },\r\n    // 深化资料查看\r\n    handleDeepMaterial(row) {\r\n      console.log('handleDeepMaterial')\r\n      this.width = '45%'\r\n      this.generateComponent('查看深化资料', 'DeepMaterial')\r\n      this.$nextTick((_) => {\r\n        row.isReadOnly = false\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n    // 排产数量\r\n    handelSchduling(row) {\r\n      this.width = '45%'\r\n      this.generateComponent('生产详情', 'Schduling')\r\n      this.$nextTick((_) => {\r\n        row.isReadOnly = false\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n    // 查看零件\r\n    handlePartList(row) {\r\n      this.width = '45%'\r\n      this.generateComponent('零件清单', 'PartList')\r\n      this.$nextTick((_) => {\r\n        row.isReadOnly = false\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n    // 部件分页统计\r\n    getUnitWeightList() {\r\n      this.countLoading = true\r\n      const customParamsData = JSON.parse(JSON.stringify(this.customParams))\r\n      const InstallUnit_Ids = customParamsData.InstallUnit_Id.join(',')\r\n      delete customParamsData.InstallUnit_Id\r\n      GetUnitWeightList({\r\n        Level: this.levelCode,\r\n        ...this.queryInfo,\r\n        ...customParamsData,\r\n        InstallUnit_Ids\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.SteelAmountTotal = Math.round(res.Data.DeepenNum * 1000) / 1000 // 深化总量\r\n          this.SchedulingNumTotal =\r\n            Math.round(res.Data.SchedulingNum * 1000) / 1000 // 排产总量\r\n          this.SteelAllWeightTotal =\r\n            Math.round(res.Data.DeepenWeight * 1000) / 1000 // 深化总重\r\n          this.SchedulingAllWeightTotal =\r\n            Math.round(res.Data.SchedulingWeight * 1000) / 1000 // 排产总重\r\n          this.FinishCountTotal =\r\n            Math.round(res.Data.Finish_Count * 1000) / 1000 // 完成总数\r\n          this.FinishWeightTotal =\r\n            Math.round(res.Data.Finish_Weight * 1000) / 1000 // 完成总重\r\n          this.DirectCountTotal =\r\n            Math.round(res.Data.Direct_Count * 1000) / 1000 // 直发件总数\r\n          this.DirectWeightTotal =\r\n            Math.round(res.Data.Direct_Weight * 1000) / 1000 // 直发件总量\r\n          console.log(' this.SteelAllWeightTotal', this.SteelAllWeightTotal)\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n        this.countLoading = false\r\n      })\r\n    },\r\n    tbSelectChange(array) {\r\n      this.selectList = array.records\r\n      this.SteelAmountTotal = 0\r\n      this.SchedulingNumTotal = 0\r\n      this.SteelAllWeightTotal = 0\r\n      this.SchedulingAllWeightTotal = 0\r\n      this.FinishCountTotal = 0\r\n      this.FinishWeightTotal = 0\r\n      this.DirectCountTotal = 0\r\n      let SteelAllWeightTotalTemp = 0\r\n      let SchedulingAllWeightTotalTemp = 0\r\n      let FinishWeightTotalTemp = 0\r\n      let DirectWeightTotalTemp = 0\r\n      if (this.selectList.length > 0) {\r\n        this.selectList.forEach((item) => {\r\n          const schedulingNum =\r\n            item.Schduling_Count == null ? 0 : item.Schduling_Count\r\n          this.SteelAmountTotal += item.Num\r\n          this.SchedulingNumTotal += Number(item.Schduling_Count)\r\n          this.FinishCountTotal += item.Finish_Count\r\n          if (item.IsDirect) {\r\n            this.DirectCountTotal += item.Num // 直发件总数\r\n            DirectWeightTotalTemp += item.Total_Weight // 直发件总量\r\n          }\r\n          SteelAllWeightTotalTemp += item.Total_Weight\r\n          SchedulingAllWeightTotalTemp += item.Weight * schedulingNum\r\n          FinishWeightTotalTemp += item.Finish_Weight\r\n        })\r\n        this.SteelAllWeightTotal =\r\n          Math.round((SteelAllWeightTotalTemp / this.Proportion) * 1000) / 1000\r\n        this.SchedulingAllWeightTotal =\r\n          Math.round((SchedulingAllWeightTotalTemp / this.Proportion) * 1000) /\r\n          1000\r\n        this.FinishWeightTotal =\r\n          Math.round((FinishWeightTotalTemp / this.Proportion) * 1000) / 1000\r\n        this.DirectWeightTotal =\r\n          Math.round((DirectWeightTotalTemp / this.Proportion) * 1000) / 1000\r\n      } else {\r\n        this.getUnitWeightList()\r\n      }\r\n    },\r\n    fetchTreeDataLocal() {\r\n      // this.filterText = this.projectName\r\n    },\r\n    getPartInfo(row) {\r\n      const drawingData = row.Drawing ? row.Drawing.split(',') : [] // 图纸数据\r\n      const fileUrlData = row.File_Url ? row.File_Url.split(',') : [] // 图纸数据文件地址数据\r\n      if (fileUrlData.length === 0) {\r\n        this.$message({\r\n          message: '当前部件无图纸',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      if (drawingData.length > 0 && fileUrlData.length > 0) {\r\n        this.drawingActive = drawingData[0]\r\n      }\r\n      if (drawingData.length > 0 && fileUrlData.length > 0) {\r\n        this.drawingDataList = drawingData.map((item, index) => ({\r\n          name: item,\r\n          label: item,\r\n          url: fileUrlData[index]\r\n        }))\r\n      }\r\n\r\n      this.getPartInfoDrawing(row)\r\n    },\r\n\r\n    getPartInfoDrawing(row) {\r\n      const importDetailId = row.Part_Aggregate_Id\r\n      GetSteelCadAndBimId({ importDetailId: importDetailId, Level: this.levelCode }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const drawingData = {\r\n            'extensionName': res.Data[0].ExtensionName,\r\n            'fileBim': res.Data[0].fileBim,\r\n            'IsUpload': res.Data[0].IsUpload,\r\n            'Code': row.Code,\r\n            'Sys_Project_Id': row.Sys_Project_Id\r\n          }\r\n          this.$refs.modelDrawingRef.dwgInit(drawingData)\r\n        }\r\n      })\r\n    },\r\n\r\n    changeDrawing(tab, event) {\r\n      console.log(tab, event)\r\n      const tabName = tab.name\r\n      // 查找对应的 URL\r\n      const drawingTab = this.drawingDataList.find(\r\n        (file) => file.name === tabName\r\n      )\r\n      if (drawingTab) {\r\n        console.log('URL:', drawingTab.url) // 输出 URL\r\n        // 在这里你可以处理其他逻辑，如打开链接等\r\n      }\r\n    },\r\n    /*    handleViewDwg(row) {\r\n      if (!row.File_Url) {\r\n        this.$message({\r\n          message: '当前零件无图纸',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      window.open('http://dwgv1.bimtk.com:5432/?CadUrl=' + parseOssUrl(row.File_Url), '_blank')\r\n    },*/\r\n    customFilterFun(value, data, node) {\r\n      const arr = value.split(SPLIT_SYMBOL)\r\n      const labelVal = arr[0]\r\n      const statusVal = arr[1]\r\n      if (!value) return true\r\n      let parentNode = node.parent\r\n      let labels = [node.label]\r\n      let status = [\r\n        data.Data.Is_Deepen_Change\r\n          ? '已变更'\r\n          : data.Data.Is_Imported\r\n            ? '已导入'\r\n            : '未导入'\r\n      ]\r\n      let level = 1\r\n      while (level < node.level) {\r\n        labels = [...labels, parentNode.label]\r\n        status = [\r\n          ...status,\r\n          data.Data.Is_Deepen_Change\r\n            ? '已变更'\r\n            : data.Data.Is_Imported\r\n              ? '已导入'\r\n              : '未导入'\r\n        ]\r\n        parentNode = parentNode.parent\r\n        level++\r\n      }\r\n      labels = labels.filter((v) => !!v)\r\n      status = status.filter((v) => !!v)\r\n      let resultLabel = true\r\n      let resultStatus = true\r\n      if (this.statusType) {\r\n        resultStatus = status.some((s) => s.indexOf(statusVal) !== -1)\r\n      }\r\n      if (this.projectName) {\r\n        resultLabel = labels.some((s) => s.indexOf(labelVal) !== -1)\r\n      }\r\n      return resultLabel && resultStatus\r\n    },\r\n    async getFileType() {\r\n      const params = {\r\n        Level: this.levelCode,\r\n        catalogCode: 'PLMDeepenFiles'\r\n      }\r\n      const res = await GetFileType(params)\r\n      // 获取构件详图\r\n      const lable = `${this.levelName}详图`\r\n      const data = res.Data.find((v) => v.Label === lable)\r\n\r\n      this.comDrawData = {\r\n        isSHQD: false,\r\n        Id: data.Id,\r\n        name: data.Label,\r\n        Catalog_Code: data.Code,\r\n        Code: data.Data?.English_Name\r\n      }\r\n\r\n      console.log(this.comDrawData, 'comDrawData')\r\n    },\r\n    // 图纸导入\r\n    handelImport() {\r\n      this.$refs.comDrawdialogRef.handleOpen(\r\n        'add',\r\n        this.comDrawData,\r\n        '',\r\n        false,\r\n        this.customParams.Sys_Project_Id,\r\n        false\r\n      )\r\n    },\r\n    // 轨迹图\r\n    handleTrack(row) {\r\n      console.log(row, 'row')\r\n      this.trackDrawer = true\r\n      this.trackDrawerTitle = row.Code\r\n      this.trackDrawerData = row\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/styles/mixin.scss\";\r\n@import \"~@/styles/tabs.scss\";\r\n.min900 {\r\n  min-width: 900px;\r\n  overflow: auto;\r\n}\r\n.z-dialog {\r\n  ::v-deep {\r\n    .el-dialog__header {\r\n      background-color: #298dff;\r\n\r\n      .el-dialog__title,\r\n      .el-dialog__close {\r\n        color: #ffffff;\r\n      }\r\n    }\r\n\r\n    .el-dialog__body {\r\n      // max-height: 750px;\r\n      overflow: auto;\r\n      @include scrollBar;\r\n\r\n      &::-webkit-scrollbar {\r\n        width: 8px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.container {\r\n  padding: 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100%;\r\n}\r\n\r\n.tb-container {\r\n  padding: 0 16px 0 16px;\r\n  flex: 1;\r\n  height: 0; //解决溢出问题\r\n  // .vxe-table {\r\n  //   height: calc(100%);\r\n  // }\r\n}\r\n\r\n.cs-z-tb-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 0; //解决溢出问题\r\n}\r\n\r\n.cs-bottom {\r\n  padding: 8px 16px 8px 16px;\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: row-reverse;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  box-sizing: border-box;\r\n\r\n  .data-info {\r\n    .info-x {\r\n      margin-right: 20px;\r\n    }\r\n  }\r\n  .pg-input {\r\n    width: 100px;\r\n    margin-right: 20px;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  text-align: right;\r\n  margin: 0;\r\n  padding: 0;\r\n  ::v-deep .el-input--small .el-input__inner {\r\n    height: 28px;\r\n    line-height: 28px;\r\n  }\r\n}\r\n\r\n.cs-from {\r\n  background-color: #ffffff;\r\n  border-radius: 4px;\r\n  margin-bottom: 16px;\r\n  padding: 16px 16px 0 16px;\r\n  display: flex;\r\n  font-size: 14px;\r\n  color: rgba(34, 40, 52, 0.65);\r\n  label {\r\n    display: inline-block;\r\n    margin-right: 20px;\r\n    white-space: nowrap;\r\n    vertical-align: top;\r\n  }\r\n  .cs-from-title {\r\n    flex: 1;\r\n  }\r\n\r\n  .mb0 {\r\n    margin-bottom: 0;\r\n\r\n    ::v-deep {\r\n      .el-form-item {\r\n        margin-bottom: 0\r\n      }\r\n    }\r\n  }\r\n\r\n  .cs-search {\r\n    width: 100%;\r\n    label {\r\n      margin-bottom: 10px;\r\n    }\r\n    button {\r\n      margin-right: 10px;\r\n      margin-left: 0;\r\n      margin-bottom: 10px;\r\n    }\r\n  }\r\n}\r\n\r\n.input-with-select {\r\n  width: 250px;\r\n}\r\n\r\n.cs-button-box {\r\n  padding: 16px 16px 6px 16px;\r\n  position: relative;\r\n  background-color: #ffffff;\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  flex-wrap: wrap;\r\n\r\n  ::v-deep .el-button {\r\n    margin-left: 0 !important;\r\n    margin-right: 10px !important;\r\n    margin-bottom: 10px !important;\r\n  }\r\n}\r\n.info-box {\r\n  margin: 0 16px 16px 16px;\r\n  display: flex;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  height: 64px;\r\n  background: rgba(41, 141, 255, 0.05);\r\n\r\n  .cs-col {\r\n    display: flex;\r\n    justify-content: space-evenly;\r\n    flex-direction: column;\r\n    margin-right: 64px;\r\n  }\r\n\r\n  .info-label {\r\n    color: #999999;\r\n  }\r\n\r\n  i {\r\n    color: #00c361;\r\n    font-style: normal;\r\n    font-weight: 600;\r\n    margin-left: 10px;\r\n  }\r\n}\r\n\r\n::v-deep .el-tree-node {\r\n  min-width: 240px;\r\n  width: min-content;\r\n}\r\n::v-deep .el-tree-node > .el-tree-node__children {\r\n  overflow: inherit;\r\n}\r\n\r\n.stretch-btn {\r\n  position: absolute;\r\n  width: 20px;\r\n  height: 130px;\r\n  top: calc((100% - 130px) / 2);\r\n\r\n  display: flex;\r\n  align-items: center;\r\n  background: #eff1f3;\r\n  cursor: pointer;\r\n  .center-btn {\r\n    width: 14px;\r\n    height: 100px;\r\n    border-radius: 0 9px 9px 0;\r\n    background-color: #8c95a8;\r\n    > i {\r\n      line-height: 100px;\r\n      text-align: center;\r\n      color: #fff;\r\n    }\r\n  }\r\n}\r\n.cs-left {\r\n  position: relative;\r\n  margin-right: 20px;\r\n  .inner-wrapper {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    padding: 16px 10px 16px 16px;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n\r\n    .tree-search {\r\n      display: flex;\r\n\r\n      .search-select {\r\n        margin-right: 8px;\r\n      }\r\n    }\r\n\r\n    .tree-x {\r\n      overflow: hidden;\r\n      margin-top: 16px;\r\n      flex: 1;\r\n\r\n      .cs-scroll {\r\n        overflow-y: auto;\r\n        @include scrollBar;\r\n      }\r\n\r\n      .el-tree {\r\n        height: 100%;\r\n\r\n        //::v-deep {\r\n        //  .el-tree-node {\r\n        //    min-width: 240px;\r\n        //    width: min-content;\r\n        //\r\n        //    .el-tree-node__children {\r\n        //      overflow: inherit;\r\n        //    }\r\n        //  }\r\n        //}\r\n      }\r\n    }\r\n  }\r\n}\r\n.cs-left-contract {\r\n  padding-left: 0;\r\n  position: relative;\r\n  width: 20px;\r\n  margin-right: 26px;\r\n}\r\n.cs-right {\r\n  padding-right: 0;\r\n  flex: 1;\r\n  width: 0;\r\n}\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n.fourGreen {\r\n  color: #00c361;\r\n  font-style: normal;\r\n}\r\n\r\n.fourOrange {\r\n  color: #ff9400;\r\n  font-style: normal;\r\n}\r\n\r\n.fourRed {\r\n  color: #ff0000;\r\n  font-style: normal;\r\n}\r\n\r\n.cs-blue {\r\n  color: #5ac8fa;\r\n}\r\n\r\n.orangeBg {\r\n  background: rgba(255, 148, 0, 0.1);\r\n}\r\n\r\n.redBg {\r\n  background: rgba(252, 107, 127, 0.1);\r\n}\r\n.greenBg {\r\n  background: rgba(0, 195, 97, 0.1);\r\n}\r\n\r\n.cs-tag {\r\n  margin-left: 8px;\r\n  font-size: 12px;\r\n  padding: 2px 4px;\r\n  border-radius: 1px;\r\n}\r\n.cs-tree-x {\r\n  ::v-deep {\r\n    .el-select {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n.cs-divider {\r\n  margin: 16px 0 0 0;\r\n}\r\n</style>\r\n"]}]}
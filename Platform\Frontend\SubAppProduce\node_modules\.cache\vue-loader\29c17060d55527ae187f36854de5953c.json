{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\CheckType.vue?vue&type=style&index=0&id=5b8f6b8d&scoped=true&lang=css", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\CheckType.vue", "mtime": 1756113746834}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgovKiAudnhlLXRhYmxlIHsKICBtYXgtaGVpZ2h0OiAxMDAlOwp9ICovCg=="}, {"version": 3, "sources": ["CheckType.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6HA;AACA;AACA", "file": "CheckType.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup/components", "sourcesContent": ["<template>\n  <div style=\"height: calc(100vh - 300px)\">\n    <vxe-table\n      v-loading=\"tbLoading\"\n      :empty-render=\"{name: 'NotData'}\"\n      show-header-overflow\n      element-loading-spinner=\"el-icon-loading\"\n      element-loading-text=\"拼命加载中\"\n      empty-text=\"暂无数据\"\n      class=\"cs-vxe-table\"\n      height=\"100%\"\n      align=\"left\"\n      stripe\n      :data=\"tbData\"\n      resizable\n      :auto-resize=\"true\"\n      :tooltip-config=\"{ enterable: true }\"\n    >\n      <vxe-column\n        align=\"left\"\n        show-overflow=\"tooltip\"\n        sortable\n        field=\"Name\"\n        title=\"检查类型\"\n        width=\"calc(100vh-200px)\"\n      >\n        <template #default=\"{ row }\">\n          {{ row.Name }}\n        </template>\n      </vxe-column>\n      <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" align=\"center\" show-overflow>\n        <template #default=\"{ row }\">\n          <el-button type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\n          <el-divider direction=\"vertical\" />\n          <el-button type=\"text\" @click=\"removeEvent(row)\">删除</el-button>\n        </template>\n      </vxe-column>\n    </vxe-table>\n  </div>\n</template>\n\n<script>\nimport { GetCheckTypeList } from '@/api/PRO/factorycheck'\nimport { DeleteCheckType } from '@/api/PRO/factorycheck'\n\nexport default {\n  props: {\n    checkType: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      tbLoading: false,\n      tbData: []\n    }\n  },\n  watch: {\n    checkType: {\n      handler(newName) {\n        this.checkType = newName\n        this.getCheckTypeList()\n      },\n      deep: true\n    }\n  },\n  mounted() {\n    this.getCheckTypeList()\n  },\n  methods: {\n    getCheckTypeList() {\n      this.tbLoading = true\n      GetCheckTypeList({ check_object_id: this.checkType.Id }).then((res) => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data\n          this.tbLoading = false\n          console.log(res.Data)\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n          this.tbLoading = false\n        }\n      })\n    },\n    removeEvent(row) {\n      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DeleteCheckType({ id: row.Id }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getCheckTypeList()\n            } else {\n              this.$message({\n                type: 'error',\n                message: res.Message\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    },\n    editEvent(row) {\n      console.log('row', row)\n      this.$emit('optionFn', row)\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* .vxe-table {\n  max-height: 100%;\n} */\n</style>\n"]}]}
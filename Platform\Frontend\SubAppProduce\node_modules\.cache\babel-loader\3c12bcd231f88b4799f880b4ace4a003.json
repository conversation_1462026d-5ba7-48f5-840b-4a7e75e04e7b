{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\Add.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\Add.vue", "mtime": 1757468113389}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyBmcm9tICJEOi9wcm9qZWN0L3BsYXRmb3JtX2ZyYW1ld29ya19tYXN0ZXIvcGxhdGZvcm1fZnJhbWV3b3JrL1BsYXRmb3JtL0Zyb250ZW5kL1N1YkFwcFByb2R1Y2Uvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzLmpzIjsKaW1wb3J0IF9yZWdlbmVyYXRvclJ1bnRpbWUgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9yZWdlbmVyYXRvclJ1bnRpbWUuanMiOwppbXBvcnQgX2FzeW5jVG9HZW5lcmF0b3IgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9hc3luY1RvR2VuZXJhdG9yLmpzIjsKaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKdmFyIF9leGNsdWRlZCA9IFsiSXNfTmVzdCJdOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maW5kLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5maW5kLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC5leGVjLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnJlcGxhY2UuanMiOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwoKaW1wb3J0IHsgR2V0Qk9NSW5mbyB9IGZyb20gJ0Avdmlld3MvUFJPL2JvbS1zZXR0aW5nL3V0aWxzJzsKaW1wb3J0IHsgR2V0VXNlckxpc3QgfSBmcm9tICdAL2FwaS9zeXMnOwppbXBvcnQgeyBBZGRXb3JraW5nUHJvY2VzcywgR2V0RmFjdG9yeVBlb3BsZWxpc3QsIEdldENoZWNrR3JvdXBMaXN0LCBHZXRXb3JraW5nVGVhbXMgfSBmcm9tICdAL2FwaS9QUk8vdGVjaG5vbG9neS1saWInOwppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCc7CmltcG9ydCB7IEdldERpY3Rpb25hcnlEZXRhaWxMaXN0QnlDb2RlIH0gZnJvbSAnQC9hcGkvc3lzJzsKZXhwb3J0IGRlZmF1bHQgewogIHByb3BzOiB7CiAgICB0eXBlOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0sCiAgICByb3dJbmZvOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogZnVuY3Rpb24gX2RlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIHt9OwogICAgICB9CiAgICB9LAogICAgYm9tTGlzdDogewogICAgICB0eXBlOiBBcnJheSwKICAgICAgZGVmYXVsdDogZnVuY3Rpb24gX2RlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIFtdOwogICAgICB9CiAgICB9CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgY2hlY2tMaXN0OiBbXSwKICAgICAgcmFkaW9MaXN0OiBbXSwKICAgICAgYnRuTG9hZGluZzogZmFsc2UsCiAgICAgIGhpZGRlblBhcnQ6IGZhbHNlLAogICAgICBmb3JtOiB7CiAgICAgICAgQ29kZTogJycsCiAgICAgICAgTmFtZTogJycsCiAgICAgICAgQm9tX0xldmVsOiAnJywKICAgICAgICBNb250aF9BdmdfTG9hZDogJycsCiAgICAgICAgQ29vcmRpbmF0ZV9Vc2VySWQ6ICcnLAogICAgICAgIFNvcnQ6IHVuZGVmaW5lZCwKICAgICAgICBJc19FbmFibGU6IHRydWUsCiAgICAgICAgSXNfRXh0ZXJuYWw6IGZhbHNlLAogICAgICAgIElzX05lc3Q6IGZhbHNlLAogICAgICAgIElzX05lZWRfQ2hlY2s6IHRydWUsCiAgICAgICAgSXNfSW50ZXJfQ2hlY2s6IHRydWUsCiAgICAgICAgSXNfTmVlZF9UQzogdHJ1ZSwKICAgICAgICBJc19XZWxkaW5nX0Fzc2VtYmxpbmc6IGZhbHNlLAogICAgICAgIElzX0N1dHRpbmc6IGZhbHNlLAogICAgICAgIFRDX0NoZWNrX1VzZXJJZDogJycsCiAgICAgICAgSXNfTmVlZF9aTDogZmFsc2UsCiAgICAgICAgWkxfQ2hlY2tfVXNlcklkOiAnJywKICAgICAgICBDaGVja19TdHlsZTogJzAnLAogICAgICAgIFdvcmtpbmdfVGVhbV9JZHM6IFtdLAogICAgICAgIFJlbWFyazogJycKICAgICAgfSwKICAgICAgWkxfQ2hlY2tfVXNlcklkczogW10sCiAgICAgIFRDX0NoZWNrX1VzZXJJZHM6IFtdLAogICAgICBDaGVja0NoYW5nZTogdHJ1ZSwKICAgICAgdXNlck9wdGlvbnM6IFtdLAogICAgICBvcHRpb25zVXNlckxpc3Q6IFtdLAogICAgICBvcHRpb25zR3JvdXBMaXN0OiBbXSwKICAgICAgb3B0aW9uc1dvcmtpbmdUZWFtc0xpc3Q6IFtdLAogICAgICBydWxlczogewogICAgICAgIENvZGU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfor7fovpPlhaXku6Plj7cnLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfSwgewogICAgICAgICAgbWF4OiAzMCwKICAgICAgICAgIG1lc3NhZ2U6ICfplb/luqblnKggMzAg5Liq5a2X56ym5YaFJywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH1dLAogICAgICAgIE5hbWU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfor7fovpPlhaXlkI3np7AnLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfSwgewogICAgICAgICAgbWF4OiAzMCwKICAgICAgICAgIG1lc3NhZ2U6ICfplb/luqblnKggMzAg5Liq5a2X56ym5YaFJywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH1dLAogICAgICAgIEJvbV9MZXZlbDogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+mAieaLqeexu+WeiycsCiAgICAgICAgICB0cmlnZ2VyOiAnY2hhbmdlJwogICAgICAgIH1dLAogICAgICAgIFNvcnQ6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfor7fovpPlhaUnLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfV0sCiAgICAgICAgSXNfTmVlZF9DaGVjazogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+mAieaLqeaYr+WQpui0qOajgCcsCiAgICAgICAgICB0cmlnZ2VyOiAnY2hhbmdlJwogICAgICAgIH1dCiAgICAgIH0KICAgIH07CiAgfSwKICBjb21wdXRlZDogX29iamVjdFNwcmVhZCh7fSwgbWFwR2V0dGVycygndGVuYW50JywgWydpc1ZlcnNpb25Gb3VyJ10pKSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdGhpcy5nZXRVc2VyTGlzdCgpOwogICAgdGhpcy5nZXRGYWN0b3J5UGVvcGxlbGlzdCgpOwogICAgLy8gdGhpcy5nZXRDaGVja0dyb3VwTGlzdCgpOwogICAgLy8gdGhpcy5TZWxlY3Rpb25jaGVja0NvbWJpbmF0aW9uKCkKICAgIHRoaXMuZ2V0V29ya2luZ1RlYW1zTGlzdCgpOwogICAgY29uc29sZS5sb2coJ3R5cGUnLCB0aGlzLnJvd0luZm8pOwogICAgdGhpcy50eXBlID09PSAnZWRpdCcgJiYgdGhpcy5pbml0Rm9ybSgpOwogICAgdGhpcy5nZXRCT01JbmZvKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICByYWRpb0ludGVyQ2hhbmdlOiBmdW5jdGlvbiByYWRpb0ludGVyQ2hhbmdlKHZhbCkge30sCiAgICBnZXRCT01JbmZvOiBmdW5jdGlvbiBnZXRCT01JbmZvKCkgewogICAgICB0aGlzLnJhZGlvTGlzdCA9IHRoaXMuYm9tTGlzdDsKICAgIH0sCiAgICAvLyDojrflj5borr7lpIfnsbvlnosKICAgIGdldERpY3Rpb25hcnlEZXRhaWxMaXN0QnlDb2RlOiBmdW5jdGlvbiBnZXREaWN0aW9uYXJ5RGV0YWlsTGlzdEJ5Q29kZSgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlKCkgewogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlJChfY29udGV4dCkgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQucHJldiA9IF9jb250ZXh0Lm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAyOwogICAgICAgICAgICAgIHJldHVybiBHZXREaWN0aW9uYXJ5RGV0YWlsTGlzdEJ5Q29kZSh7CiAgICAgICAgICAgICAgICBkaWN0aW9uYXJ5Q29kZTogJ2RldmljZVR5cGUnCiAgICAgICAgICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICAgICAgICBfdGhpcy5kZXZpY2VUeXBlTGlzdCA9IHJlcy5EYXRhOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgX3RoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICBjb25zb2xlLmxvZygnIHRoaXMub3B0aW9uc0dyb3VwTGlzdCcsIF90aGlzLm9wdGlvbnNHcm91cExpc3QpOwogICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgaW5pdEZvcm06IGZ1bmN0aW9uIGluaXRGb3JtKCkgewogICAgICB0aGlzLmdldEJPTUluZm8oKTsKICAgICAgdmFyIF90aGlzJHJvd0luZm8gPSB0aGlzLnJvd0luZm8sCiAgICAgICAgSXNfTmVzdCA9IF90aGlzJHJvd0luZm8uSXNfTmVzdCwKICAgICAgICBvdGhlcnMgPSBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMoX3RoaXMkcm93SW5mbywgX2V4Y2x1ZGVkKTsKICAgICAgdGhpcy5mb3JtID0gT2JqZWN0LmFzc2lnbih7fSwgb3RoZXJzLCB7CiAgICAgICAgSXNfTmVzdDogISFJc19OZXN0CiAgICAgIH0pOwogICAgICBjb25zb2xlLmxvZygnb3RoZXJzJywgb3RoZXJzKTsKICAgICAgdGhpcy5mb3JtLkJvbV9MZXZlbCA9IFN0cmluZyh0aGlzLmZvcm0uQm9tX0xldmVsKTsKICAgICAgLy8gIGlmKHRoaXMuZm9ybS5UeXBlPT0yKXsKICAgICAgLy8gICB0aGlzLmZvcm0uVHlwZXMgPSAnMCcKICAgICAgLy8gIH1lbHNlIGlmKHRoaXMuZm9ybS5UeXBlPT0zKXsKICAgICAgLy8gICBsZXQgVHlwZXMgPSB0aGlzLnJhZGlvTGlzdC5maW5kKHYgPT4gWycxJywgJzInLCczJ10uaW5jbHVkZXModi5Db2RlKSk/LkNvZGUKICAgICAgLy8gICBjb25zb2xlLmxvZygnVHlwZXMnLCBUeXBlcykKICAgICAgLy8gICBjb25zb2xlLmxvZygndGhpcy5yYWRpb0xpc3QnLCB0aGlzLnJhZGlvTGlzdCkKICAgICAgLy8gICB0aGlzLmZvcm0uVHlwZXMgPSBUeXBlcwogICAgICAvLyAgfWVsc2UgaWYodGhpcy5mb3JtLlR5cGU9PTEpewogICAgICAvLyAgIHRoaXMuZm9ybS5UeXBlcyA9ICctMScKICAgICAgLy8gIH0KICAgICAgY29uc29sZS5sb2coJ3RoaXMuZm9ybScsIHRoaXMuZm9ybSk7CgogICAgICAvLyDlpITnkIbljoblj7LmlbDmja7lpJrpgInpl67popgKICAgICAgLy8gaWYgKHRoaXMuZm9ybS5Jc19OZWVkX0NoZWNrKSB7CiAgICAgIC8vICAgaWYgKHRoaXMuZm9ybS5DaGVja19TdHlsZSA9PT0gJzEnKSB7CgogICAgICAvLyAgIH0gZWxzZSB7CiAgICAgIC8vICAgICB0aGlzLkNoZWNrQ2hhbmdlID0gISF0aGlzLmZvcm0uSXNfTmVlZF9UQwogICAgICAvLyAgICAgaWYgKHRoaXMuZm9ybS5Jc19OZWVkX1pMICYmIHRoaXMuZm9ybS5Jc19OZWVkX1RDKSB7CiAgICAgIC8vICAgICAgIHRoaXMuZm9ybS5Jc19OZWVkX1RDID0gdHJ1ZQogICAgICAvLyAgICAgICB0aGlzLmZvcm0uSXNfTmVlZF9aTCA9IGZhbHNlCiAgICAgIC8vICAgICB9CiAgICAgIC8vICAgfQogICAgICAvLyB9CiAgICAgIHRoaXMuWkxfQ2hlY2tfVXNlcklkcyA9IHRoaXMuZm9ybS5aTF9DaGVja19Vc2VySWQgPyB0aGlzLmZvcm0uWkxfQ2hlY2tfVXNlcklkLnNwbGl0KCcsJykgOiBbXTsKICAgICAgdGhpcy5UQ19DaGVja19Vc2VySWRzID0gdGhpcy5mb3JtLlRDX0NoZWNrX1VzZXJJZCA/IHRoaXMuZm9ybS5UQ19DaGVja19Vc2VySWQuc3BsaXQoJywnKSA6IFtdOwogICAgICBjb25zb2xlLmxvZygndGhpcy5UQ19DaGVja19Vc2VySWRzJywgdGhpcy5UQ19DaGVja19Vc2VySWRzKTsKICAgIH0sCiAgICBnZXRVc2VyTGlzdDogZnVuY3Rpb24gZ2V0VXNlckxpc3QoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICBHZXRVc2VyTGlzdCh7fSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIF90aGlzMi51c2VyT3B0aW9ucyA9IHJlcy5EYXRhOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpczIuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBnZXRGYWN0b3J5UGVvcGxlbGlzdDogZnVuY3Rpb24gZ2V0RmFjdG9yeVBlb3BsZWxpc3QoKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICBHZXRGYWN0b3J5UGVvcGxlbGlzdCh7fSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIF90aGlzMy5vcHRpb25zVXNlckxpc3QgPSByZXMuRGF0YTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXMzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgZ2V0Q2hlY2tHcm91cExpc3Q6IGZ1bmN0aW9uIGdldENoZWNrR3JvdXBMaXN0KCkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlMigpIHsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTIkKF9jb250ZXh0MikgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQyLnByZXYgPSBfY29udGV4dDIubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX2NvbnRleHQyLm5leHQgPSAyOwogICAgICAgICAgICAgIHJldHVybiBHZXRDaGVja0dyb3VwTGlzdCh7fSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICAgICAgICBfdGhpczQub3B0aW9uc0dyb3VwTGlzdCA9IHJlcy5EYXRhOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgX3RoaXM0LiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgICAgY29uc29sZS5sb2coJyB0aGlzLm9wdGlvbnNHcm91cExpc3QnLCBfdGhpczQub3B0aW9uc0dyb3VwTGlzdCk7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQyLnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMik7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIGdldFdvcmtpbmdUZWFtc0xpc3Q6IGZ1bmN0aW9uIGdldFdvcmtpbmdUZWFtc0xpc3QoKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICBHZXRXb3JraW5nVGVhbXMoe30pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICBfdGhpczUub3B0aW9uc1dvcmtpbmdUZWFtc0xpc3QgPSByZXMuRGF0YTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXM1LiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy8g562b6YCJ5qOA5p+l6aG557uE5ZCICiAgICAvLyBhc3luYyAgU2VsZWN0aW9uY2hlY2tDb21iaW5hdGlvbigpewogICAgLy8gICB0aGlzLmZvcm0uQ2hlY2tfR3JvdXBfTGlzdCA9IFtdCiAgICAvLyAgIHRoaXMub3B0aW9uc0dyb3VwTGlzdCA9IFtdCiAgICAvLyAgIGF3YWl0IHRoaXMuZ2V0Q2hlY2tHcm91cExpc3QoKTsKICAgIC8vICAgbGV0IG9wdGlvbnNHcm91cExpc3RUZW1wID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLm9wdGlvbnNHcm91cExpc3QpKTsKICAgIC8vICAgY29uc29sZS5sb2coIlNlbGVjdGlvbmNoZWNrQ29tYmluYXRpb24xIiwgb3B0aW9uc0dyb3VwTGlzdFRlbXApCiAgICAvLyAgIHRoaXMuZm9ybS5UeXBlID09IDEgICYmIG9wdGlvbnNHcm91cExpc3RUZW1wICYmICAob3B0aW9uc0dyb3VwTGlzdFRlbXAgPSBvcHRpb25zR3JvdXBMaXN0VGVtcC5maWx0ZXIodiA9PiB2LkNoZWNrX09iamVjdF9JZCA9PT0gIjEiKS5sZW5ndGggPiAwID8gb3B0aW9uc0dyb3VwTGlzdFRlbXAuZmlsdGVyKHYgPT52LkNoZWNrX09iamVjdF9JZCA9PT0gIjEiKSA6IG9wdGlvbnNHcm91cExpc3RUZW1wKSAvL+mAmui/h+W3peW6j+etm+mAiSBUeXBlPSAxIENoZWNrX1R5cGUgPSAwIOaehOS7tiAyIENoZWNrX1R5cGUgPSAxIOmbtuS7tgogICAgLy8gICBjb25zb2xlLmxvZygiMSIsIG9wdGlvbnNHcm91cExpc3RUZW1wKQogICAgLy8gICB0aGlzLmZvcm0uVHlwZSA9PSAyICAmJiBvcHRpb25zR3JvdXBMaXN0VGVtcCAmJiAgKG9wdGlvbnNHcm91cExpc3RUZW1wID0gb3B0aW9uc0dyb3VwTGlzdFRlbXAuZmlsdGVyKHYgPT4gdi5DaGVja19PYmplY3RfSWQgPT09ICIyIikubGVuZ3RoID4gMCA/IG9wdGlvbnNHcm91cExpc3RUZW1wLmZpbHRlcih2ID0+IHYuQ2hlY2tfT2JqZWN0X0lkID09PSAiMiIpIDogb3B0aW9uc0dyb3VwTGlzdFRlbXApCiAgICAvLyAgIGlmKHRoaXMuZm9ybS5Jc19OZWVkX1RDICYmIHRoaXMuZm9ybS5Jc19OZWVkX1pMKXsKICAgIC8vICAgICBvcHRpb25zR3JvdXBMaXN0VGVtcCA9ICBvcHRpb25zR3JvdXBMaXN0VGVtcC5maWx0ZXIodiA9PiB2LkNoZWNrX1R5cGUgPT09IC0xKS5sZW5ndGggPiAwID8gb3B0aW9uc0dyb3VwTGlzdFRlbXAuZmlsdGVyKHYgPT4gdi5DaGVja19UeXBlID09PSAtMSkgOiBvcHRpb25zR3JvdXBMaXN0VGVtcAogICAgLy8gICAgIGNvbnNvbGUubG9nKCIyIiwgb3B0aW9uc0dyb3VwTGlzdFRlbXAsdGhpcy5mb3JtLklzX05lZWRfVEMpCiAgICAvLyAgIH1lbHNlewogICAgLy8gICAgIGlmKHRoaXMuZm9ybS5Jc19OZWVkX1RDIHx8IHRoaXMuZm9ybS5Jc19OZWVkX1pMKXsKICAgIC8vICAgICAgIHRoaXMuZm9ybS5Jc19OZWVkX1RDICYmIG9wdGlvbnNHcm91cExpc3RUZW1wICYmIChvcHRpb25zR3JvdXBMaXN0VGVtcCA9IG9wdGlvbnNHcm91cExpc3RUZW1wLmZpbHRlcih2ID0+IHYuQ2hlY2tfVHlwZSA9PT0gMiApLmxlbmd0aCA+IDAgPyBvcHRpb25zR3JvdXBMaXN0VGVtcC5maWx0ZXIodiA9PiB2LkNoZWNrX1R5cGUgPT09IDIgfHwgdi5DaGVja19UeXBlID09PSAtMSkgOiBvcHRpb25zR3JvdXBMaXN0VGVtcC5maWx0ZXIodiA9PiB2LkNoZWNrX1R5cGUgPT09IC0xKSkKICAgIC8vICAgICAgIHRoaXMuZm9ybS5UeXBlID09IDEgICYmIG9wdGlvbnNHcm91cExpc3RUZW1wICYmICAob3B0aW9uc0dyb3VwTGlzdFRlbXAgPSBvcHRpb25zR3JvdXBMaXN0VGVtcC5maWx0ZXIodiA9PiB2LkNoZWNrX09iamVjdF9JZCA9PT0gIjEiKS5sZW5ndGggPiAwID8gb3B0aW9uc0dyb3VwTGlzdFRlbXAuZmlsdGVyKHYgPT52LkNoZWNrX09iamVjdF9JZCA9PT0gIjEiKSA6IG9wdGlvbnNHcm91cExpc3RUZW1wKSAvL+mAmui/h+W3peW6j+etm+mAiSBUeXBlPSAxIENoZWNrX1R5cGUgPSAwIOaehOS7tiAyIENoZWNrX1R5cGUgPSAxIOmbtuS7tgogICAgLy8gICAgICAgIGNvbnNvbGUubG9nKCIzIiwgb3B0aW9uc0dyb3VwTGlzdFRlbXApCiAgICAvLyAgICAgICAgdGhpcy5mb3JtLlR5cGUgPT0gMiAgJiYgb3B0aW9uc0dyb3VwTGlzdFRlbXAgJiYgIChvcHRpb25zR3JvdXBMaXN0VGVtcCA9IG9wdGlvbnNHcm91cExpc3RUZW1wLmZpbHRlcih2ID0+IHYuQ2hlY2tfT2JqZWN0X0lkID09PSAiMiIpLmxlbmd0aCA+IDAgPyBvcHRpb25zR3JvdXBMaXN0VGVtcC5maWx0ZXIodiA9PiB2LkNoZWNrX09iamVjdF9JZCA9PT0gIjIiKSA6IG9wdGlvbnNHcm91cExpc3RUZW1wKQogICAgLy8gICAgICAgY29uc29sZS5sb2coIjQiLCBvcHRpb25zR3JvdXBMaXN0VGVtcCx0aGlzLmZvcm0uSXNfTmVlZF9UQykKICAgIC8vICAgICAgIHRoaXMuZm9ybS5Jc19OZWVkX1pMICYmIG9wdGlvbnNHcm91cExpc3RUZW1wICYmIChvcHRpb25zR3JvdXBMaXN0VGVtcCA9IG9wdGlvbnNHcm91cExpc3RUZW1wLmZpbHRlcih2ID0+IHYuQ2hlY2tfVHlwZSA9PT0gMSApLmxlbmd0aCA+IDAgPyBvcHRpb25zR3JvdXBMaXN0VGVtcC5maWx0ZXIodiA9PiB2LkNoZWNrX1R5cGUgPT09IDEgfHwgdi5DaGVja19UeXBlID09PSAtMSkgOiAgb3B0aW9uc0dyb3VwTGlzdFRlbXAuZmlsdGVyKHYgPT4gdi5DaGVja19UeXBlID09PSAtMSkpICAvL+mAmui/h+i0qOajgOexu+Wei+etm+mAiQogICAgLy8gICAgICAgdGhpcy5mb3JtLlR5cGUgPT0gMSAgJiYgb3B0aW9uc0dyb3VwTGlzdFRlbXAgJiYgIChvcHRpb25zR3JvdXBMaXN0VGVtcCA9IG9wdGlvbnNHcm91cExpc3RUZW1wLmZpbHRlcih2ID0+IHYuQ2hlY2tfT2JqZWN0X0lkID09PSAiMSIpLmxlbmd0aCA+IDAgPyBvcHRpb25zR3JvdXBMaXN0VGVtcC5maWx0ZXIodiA9PnYuQ2hlY2tfT2JqZWN0X0lkID09PSAiMSIpIDogb3B0aW9uc0dyb3VwTGlzdFRlbXApIC8v6YCa6L+H5bel5bqP562b6YCJIFR5cGU9IDEgQ2hlY2tfVHlwZSA9IDAg5p6E5Lu2IDIgQ2hlY2tfVHlwZSA9IDEg6Zu25Lu2CiAgICAvLyAgICAgICBjb25zb2xlLmxvZygiNSIsIG9wdGlvbnNHcm91cExpc3RUZW1wKQogICAgLy8gICAgICAgdGhpcy5mb3JtLlR5cGUgPT0gMiAgJiYgb3B0aW9uc0dyb3VwTGlzdFRlbXAgJiYgIChvcHRpb25zR3JvdXBMaXN0VGVtcCA9IG9wdGlvbnNHcm91cExpc3RUZW1wLmZpbHRlcih2ID0+IHYuQ2hlY2tfT2JqZWN0X0lkID09PSAiMiIpLmxlbmd0aCA+IDAgPyBvcHRpb25zR3JvdXBMaXN0VGVtcC5maWx0ZXIodiA9PiB2LkNoZWNrX09iamVjdF9JZCA9PT0gIjIiKSA6IG9wdGlvbnNHcm91cExpc3RUZW1wKQogICAgLy8gICAgICAgY29uc29sZS5sb2coIjYiLCBvcHRpb25zR3JvdXBMaXN0VGVtcCx0aGlzLmZvcm0uSXNfTmVlZF9aTCkKICAgIC8vICAgICAgfQogICAgLy8gICB9CiAgICAvLyAgIHRoaXMub3B0aW9uc0dyb3VwTGlzdCA9IG9wdGlvbnNHcm91cExpc3RUZW1wCiAgICAvLyAgIGNvbnNvbGUubG9nKCJvcHRpb25zR3JvdXBMaXN0VGVtcCIsb3B0aW9uc0dyb3VwTGlzdFRlbXApCiAgICAvLyAgIGNvbnNvbGUubG9nKCJTZWxlY3Rpb25jaGVja0NvbWJpbmF0aW9uIix0aGlzLm9wdGlvbnNHcm91cExpc3QpCiAgICAvLyB9LAogICAgcmFkaW9DaGVja1N0eWxlQ2hhbmdlOiBmdW5jdGlvbiByYWRpb0NoZWNrU3R5bGVDaGFuZ2UodmFsKSB7CiAgICAgIC8vIGlmICh2YWwgPT09ICcwJykgewogICAgICAvLyAgIHRoaXMuZm9ybS5Jc19OZWVkX1RDID0gdHJ1ZQogICAgICAvLyAgIHRoaXMuZm9ybS5Jc19OZWVkX1pMID0gZmFsc2UKICAgICAgLy8gfQogICAgICB0aGlzLlpMX0NoZWNrX1VzZXJJZHMgPSBbXTsKICAgICAgdGhpcy5UQ19DaGVja19Vc2VySWRzID0gW107CiAgICAgIHRoaXMuZm9ybS5aTF9DaGVja19Vc2VySWQgPSAnJzsKICAgICAgdGhpcy5mb3JtLlRDX0NoZWNrX1VzZXJJZCA9ICcnOwogICAgfSwKICAgIC8vIHJhZGlvQ2hlY2tUeXBlQ2hhbmdlKHZhbCkgewogICAgLy8gICBpZiAodmFsKSB7CiAgICAvLyAgICAgdGhpcy5mb3JtLklzX05lZWRfVEMgPSB0cnVlCiAgICAvLyAgICAgdGhpcy5mb3JtLklzX05lZWRfWkwgPSBmYWxzZQogICAgLy8gICAgIHRoaXMuWkxfQ2hlY2tfVXNlcklkcyA9IFtdCiAgICAvLyAgICAgdGhpcy5mb3JtLlpMX0NoZWNrX1VzZXJJZCA9ICcnCiAgICAvLyAgIH0gZWxzZSB7CiAgICAvLyAgICAgdGhpcy5mb3JtLklzX05lZWRfWkwgPSB0cnVlCiAgICAvLyAgICAgdGhpcy5mb3JtLklzX05lZWRfVEMgPSBmYWxzZQogICAgLy8gICAgIHRoaXMuVENfQ2hlY2tfVXNlcklkcyA9IFtdCiAgICAvLyAgICAgdGhpcy5mb3JtLlRDX0NoZWNrX1VzZXJJZCA9ICcnCiAgICAvLyAgIH0KICAgIC8vICAgLy8gdGhpcy5aTF9DaGVja19Vc2VySWRzID0gW107CiAgICAvLyAgIC8vIHRoaXMuVENfQ2hlY2tfVXNlcklkcyA9IFtdOwogICAgLy8gICAvLyB0aGlzLmZvcm0uWkxfQ2hlY2tfVXNlcklkID0gIiI7CiAgICAvLyAgIC8vIHRoaXMuZm9ybS5UQ19DaGVja19Vc2VySWQgPSAiIjsKICAgIC8vIH0sCiAgICByYWRpb0NoYW5nZTogZnVuY3Rpb24gcmFkaW9DaGFuZ2UodmFsKSB7CiAgICAgIGlmICh2YWwgPT0gZmFsc2UpIHsKICAgICAgICB0aGlzLmZvcm0uY2hlY2tDaGFuZ2UgPSBmYWxzZTsKICAgICAgICB0aGlzLmZvcm0uSXNfTmVlZF9UQyA9IGZhbHNlOwogICAgICAgIHRoaXMuZm9ybS5Jc19OZWVkX1pMID0gZmFsc2U7CiAgICAgICAgdGhpcy5UQ19DaGVja19Vc2VySWRzID0gW107CiAgICAgICAgdGhpcy5aTF9DaGVja19Vc2VySWRzID0gW107CiAgICAgICAgdGhpcy5mb3JtLlpMX0NoZWNrX1VzZXJJZCA9ICcnOwogICAgICAgIHRoaXMuZm9ybS5UQ19DaGVja19Vc2VySWQgPSAnJzsKICAgICAgICB0aGlzLmZvcm0uQ2hlY2tfU3R5bGUgPSAnJzsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyB0aGlzLmZvcm0uY2hlY2tDaGFuZ2UgPSB0cnVlCiAgICAgICAgLy8gdGhpcy5mb3JtLklzX05lZWRfVEMgPSB0cnVlCiAgICAgICAgLy8gdGhpcy5mb3JtLklzX05lZWRfWkwgPSBmYWxzZQogICAgICAgIC8vIHRoaXMuQ2hlY2tDaGFuZ2UgPSAhIXRoaXMuZm9ybS5Jc19OZWVkX1RDCiAgICAgICAgdGhpcy5mb3JtLkNoZWNrX1N0eWxlID0gJzAnOwogICAgICB9CiAgICB9LAogICAgLy8g6YCJ5oup5p6E5Lu25bel5bqPCiAgICBjaGFuZ2VUeXBlOiBmdW5jdGlvbiBjaGFuZ2VUeXBlKHZhbCkgewogICAgICAvLyB0aGlzLlNlbGVjdGlvbmNoZWNrQ29tYmluYXRpb24oKQogICAgICAvLyBjb25zdCBDb2RlID0gdmFsCiAgICAgIC8vIGNvbnNvbGUubG9nKENvZGUsICdDb2RlJyk7CiAgICAgIC8vIGlmIChDb2RlID09PSAnLTEnKSB7CiAgICAgIC8vICAgdGhpcy5mb3JtLlR5cGUgPSAxCiAgICAgIC8vIH0gZWxzZSBpZiAoQ29kZSA9PT0gJzAnKSB7CiAgICAgIC8vICAgdGhpcy5mb3JtLlR5cGUgPSAyCiAgICAgIC8vIH0gZWxzZSBpZiAoQ29kZSA9PT0gJzEnIHx8IENvZGUgPT09ICczJ3x8IENvZGUgPT09ICcyJykgewogICAgICAvLyAgIHRoaXMuZm9ybS5UeXBlID0gMwogICAgICAvLyB9CiAgICAgIC8vIGlmICh0aGlzLmZvcm0uVHlwZSA9PT0gMSB8fCB0aGlzLmZvcm0uVHlwZSA9PT0gMykgewogICAgICAvLyAgIHRoaXMuZm9ybS5Jc19DdXR0aW5nID0gdW5kZWZpbmVkCiAgICAgIC8vIH0gZWxzZSBpZiAodGhpcy5mb3JtLlR5cGUgPT09IDIpIHsKICAgICAgLy8gICB0aGlzLmZvcm0uSXNfV2VsZGluZ19Bc3NlbWJsaW5nID0gdW5kZWZpbmVkCiAgICAgIC8vIH0KICAgIH0sCiAgICB0eXBlQ2hhbmdlOiBmdW5jdGlvbiB0eXBlQ2hhbmdlKCkgewogICAgICB0aGlzLmZvcm0uVGFza19Nb2RlbCA9ICcnOwogICAgfSwKICAgIGNoYW5nZVRjOiBmdW5jdGlvbiBjaGFuZ2VUYyh2YWwpIHsKICAgICAgY29uc29sZS5sb2codmFsKTsKICAgICAgdGhpcy5mb3JtLlRDX0NoZWNrX1VzZXJJZCA9ICcnOwogICAgICBmb3IgKHZhciBpID0gMDsgaSA8IHZhbC5sZW5ndGg7IGkrKykgewogICAgICAgIGlmIChpID09IHZhbC5sZW5ndGggLSAxKSB7CiAgICAgICAgICB0aGlzLmZvcm0uVENfQ2hlY2tfVXNlcklkICs9IHZhbFtpXTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5mb3JtLlRDX0NoZWNrX1VzZXJJZCArPSB2YWxbaV0gKyAnLCc7CiAgICAgICAgfQogICAgICB9CiAgICAgIGNvbnNvbGUubG9nKHRoaXMuZm9ybS5UQ19DaGVja19Vc2VySWQsICd0aGlzLmZvcm0uVENfQ2hlY2tfVXNlcklkICcpOwogICAgfSwKICAgIGNoYW5nZVpMOiBmdW5jdGlvbiBjaGFuZ2VaTCh2YWwpIHsKICAgICAgY29uc29sZS5sb2codmFsKTsKICAgICAgdGhpcy5mb3JtLlpMX0NoZWNrX1VzZXJJZCA9ICcnOwogICAgICBmb3IgKHZhciBpID0gMDsgaSA8IHZhbC5sZW5ndGg7IGkrKykgewogICAgICAgIGlmIChpID09IHZhbC5sZW5ndGggLSAxKSB7CiAgICAgICAgICB0aGlzLmZvcm0uWkxfQ2hlY2tfVXNlcklkICs9IHZhbFtpXTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5mb3JtLlpMX0NoZWNrX1VzZXJJZCArPSB2YWxbaV0gKyAnLCc7CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgY2hlY2tib3hDaGFuZ2U6IGZ1bmN0aW9uIGNoZWNrYm94Q2hhbmdlKHZhbCwgdHlwZSkgewogICAgICBpZiAodHlwZSA9PT0gMSkgewogICAgICAgIGlmICghdmFsKSB7CiAgICAgICAgICB0aGlzLlRDX0NoZWNrX1VzZXJJZHMgPSBbXTsKICAgICAgICB9CiAgICAgIH0KICAgICAgaWYgKHR5cGUgPT09IDIpIHsKICAgICAgICBpZiAoIXZhbCkgewogICAgICAgICAgdGhpcy5aTF9DaGVja19Vc2VySWRzID0gW107CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgaGFuZGxlU3VibWl0OiBmdW5jdGlvbiBoYW5kbGVTdWJtaXQoKSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwogICAgICAvLyBkZWxldGUgdGhpcy5mb3JtLlR5cGVzCiAgICAgIGNvbnNvbGUubG9nKHRoaXMuZm9ybSwgJ3RoaXMuZm9ybScpOwogICAgICB0aGlzLiRyZWZzLmZvcm0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKCF2YWxpZCkgcmV0dXJuOwogICAgICAgIF90aGlzNi5idG5Mb2FkaW5nID0gdHJ1ZTsKICAgICAgICB2YXIgdUl0ZW1zID0gX3RoaXM2Lm9wdGlvbnNVc2VyTGlzdC5maW5kKGZ1bmN0aW9uICh2KSB7CiAgICAgICAgICByZXR1cm4gdi5JZCA9PT0gX3RoaXM2LmZvcm0uQ29vcmRpbmF0ZV9Vc2VySWQ7CiAgICAgICAgfSk7CiAgICAgICAgaWYgKHVJdGVtcykgewogICAgICAgICAgX3RoaXM2LmZvcm0uQ29vcmRpbmF0ZV9Vc2VyTmFtZSA9IHVJdGVtcy5EaXNwbGF5X05hbWU7CiAgICAgICAgfQogICAgICAgIGlmIChfdGhpczYuZm9ybS5Jc19OZWVkX0NoZWNrKSB7CiAgICAgICAgICBpZiAoX3RoaXM2LmZvcm0uSXNfTmVlZF9aTCA9PSBmYWxzZSAmJiBfdGhpczYuZm9ybS5Jc19OZWVkX1RDID09IGZhbHNlKSB7CiAgICAgICAgICAgIF90aGlzNi4kbWVzc2FnZS5lcnJvcign6K+36YCJ5oup6LSo5qOA57G75Z6LJyk7CiAgICAgICAgICAgIF90aGlzNi5idG5Mb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgIH0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXM2LmZvcm0uQ2hlY2tfU3R5bGUgPSBudWxsOwogICAgICAgICAgX3RoaXM2LmZvcm0uQ2hlY2tfR3JvdXBfTGlzdCA9IFtdOwogICAgICAgIH0KICAgICAgICB2YXIgWkwgPSBfdGhpczYuZm9ybS5Jc19OZWVkX1pMID8gX3RoaXM2LmZvcm0uWkxfQ2hlY2tfVXNlcklkIDogJyc7CiAgICAgICAgdmFyIFRDID0gX3RoaXM2LmZvcm0uSXNfTmVlZF9UQyA/IF90aGlzNi5mb3JtLlRDX0NoZWNrX1VzZXJJZCA6ICcnOwogICAgICAgIGlmIChfdGhpczYuZm9ybS5Jc19OZWVkX1pMICYmIChaTCAhPT0gbnVsbCAmJiBaTCAhPT0gdm9pZCAwID8gWkwgOiAnJykgPT0gJycpIHsKICAgICAgICAgIF90aGlzNi4kbWVzc2FnZS5lcnJvcign6K+36YCJ5oup6LSo5qOA5ZGYJyk7CiAgICAgICAgICBfdGhpczYuYnRuTG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KICAgICAgICBpZiAoX3RoaXM2LmZvcm0uSXNfTmVlZF9UQyAmJiAoVEMgIT09IG51bGwgJiYgVEMgIT09IHZvaWQgMCA/IFRDIDogJycpID09ICcnKSB7CiAgICAgICAgICBfdGhpczYuJG1lc3NhZ2UuZXJyb3IoJ+ivt+mAieaLqeaOouS8pOWRmCcpOwogICAgICAgICAgX3RoaXM2LmJ0bkxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgIHJldHVybjsKICAgICAgICB9CiAgICAgICAgQWRkV29ya2luZ1Byb2Nlc3MoX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBfdGhpczYuZm9ybSksIHt9LCB7CiAgICAgICAgICBaTF9DaGVja19Vc2VySWQ6IFpMLAogICAgICAgICAgVENfQ2hlY2tfVXNlcklkOiBUQwogICAgICAgIH0pKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICAgIF90aGlzNi4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgbWVzc2FnZTogJ+S/neWtmOaIkOWKnycsCiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICBfdGhpczYuJGVtaXQoJ3JlZnJlc2gnKTsKICAgICAgICAgICAgX3RoaXM2LiRlbWl0KCdjbG9zZScpOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgX3RoaXM2LiRtZXNzYWdlKHsKICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgICAgX3RoaXM2LmJ0bkxvYWRpbmcgPSBmYWxzZTsKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgY29kZUNoYW5nZTogZnVuY3Rpb24gY29kZUNoYW5nZShlKSB7CiAgICAgIHJldHVybiBlLnJlcGxhY2UoL1teYS16QS1aMC05XS9nLCAnJyk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["GetBOMInfo", "GetUserList", "AddWorkingProcess", "GetFactoryPeoplelist", "GetCheckGroupList", "GetWorkingTeams", "mapGetters", "GetDictionaryDetailListByCode", "props", "type", "String", "default", "rowInfo", "Object", "bomList", "Array", "data", "checkList", "radioList", "btnLoading", "hiddenPart", "form", "Code", "Name", "Bom_Level", "Month_Avg_Load", "Coordinate_UserId", "Sort", "undefined", "Is_Enable", "Is_External", "Is_Nest", "Is_Need_Check", "Is_Inter_Check", "Is_Need_TC", "Is_Welding_Assembling", "Is_Cutting", "TC_Check_UserId", "Is_Need_ZL", "ZL_Check_UserId", "Check_Style", "Working_Team_Ids", "Remark", "ZL_Check_UserIds", "TC_Check_UserIds", "CheckChange", "userOptions", "optionsUserList", "optionsGroupList", "optionsWorkingTeamsList", "rules", "required", "message", "trigger", "max", "computed", "_objectSpread", "mounted", "getUserList", "getFactoryPeoplelist", "getWorkingTeamsList", "console", "log", "initForm", "getBOMInfo", "methods", "radioInterChange", "val", "getDictionaryDetailListByCode", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "dictionaryCode", "then", "res", "IsSucceed", "deviceTypeList", "Data", "$message", "Message", "stop", "_this$rowInfo", "others", "_objectWithoutProperties", "_excluded", "assign", "split", "_this2", "_this3", "getCheckGroupList", "_this4", "_callee2", "_callee2$", "_context2", "_this5", "radioCheckStyleChange", "radioChange", "checkChange", "changeType", "typeChange", "Task_Model", "changeTc", "i", "length", "changeZL", "checkboxChange", "handleSubmit", "_this6", "$refs", "validate", "valid", "uItems", "find", "v", "Id", "Coordinate_UserName", "Display_Name", "error", "Check_Group_List", "ZL", "TC", "$emit", "codeChange", "e", "replace"], "sources": ["src/views/PRO/process-settings/management/component/Add.vue"], "sourcesContent": ["<template>\r\n  <div class=\"form-wrapper\">\r\n    <div class=\"form-x\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\" style=\"width: 100%\">\r\n        <el-form-item label=\"名称\" prop=\"Name\">\r\n          <el-input v-model=\"form.Name\" :maxlength=\"30\" placeholder=\"最多30个字\" show-word-limit />\r\n        </el-form-item>\r\n        <el-form-item label=\"代号\" prop=\"Code\">\r\n          <el-input v-model=\"form.Code\" :maxlength=\"30\" placeholder=\"字母+数字，30字符\" show-word-limit\r\n            @input=\"(e) => (form.Code = codeChange(e))\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"类型\" prop=\"Bom_Level\">\r\n          <el-radio-group v-for=\"(item, index) in radioList\" :key=\"index\" v-model=\"form.Bom_Level\" class=\"radio\"\r\n            @change=\"changeType\">\r\n            <el-radio style=\"margin-right: 8px;\" :label=\"item.Code\">{{ item.Display_Name }}</el-radio>\r\n          </el-radio-group>\r\n          <!-- <el-radio-group v-model=\"form.Type\" @change=\"changeType\" class=\"radio\">\r\n              <el-radio :label=\"1\">构件工序</el-radio>一层\r\n            <el-radio v-if=\"isVersionFour\" :label=\"3\">部件工序</el-radio>二三四层\r\n            <el-radio v-if=\"!hiddenPart\" :label=\"2\">零件工序</el-radio> 五层\r\n          </el-radio-group> -->\r\n        </el-form-item>\r\n        <el-form-item label=\"排序\" prop=\"Sort\">\r\n          <el-input-number v-model=\"form.Sort\" :min=\"0\" step-strictly :step=\"1\" class=\"cs-number-btn-hidden w100\"\r\n            placeholder=\"请输入\" clearable=\"\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"协调人\" prop=\"Coordinate_UserId\">\r\n          <el-select v-model=\"form.Coordinate_UserId\" class=\"w100\" clearable filterable placeholder=\"请选择\">\r\n            <el-option v-for=\"item in optionsUserList\" :key=\"item.Id\" :label=\"item.Display_Name\" :value=\"item.Id\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"工序月均负荷\" prop=\"Month_Avg_Load\">\r\n          <el-input v-model=\"form.Month_Avg_Load\" placeholder=\"请输入\">\r\n            <template slot=\"append\">吨</template>\r\n          </el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否外协\" prop=\"Is_External\">\r\n          <el-radio-group v-model=\"form.Is_External\">\r\n            <el-radio :label=\"true\">是</el-radio>\r\n            <el-radio :label=\"false\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否装焊工序\" prop=\"Is_Welding_Assembling\">\r\n          <el-radio-group v-model=\"form.Is_Welding_Assembling\">\r\n            <el-radio :label=\"true\">是</el-radio>\r\n            <el-radio :label=\"false\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否专检\" prop=\"Is_Need_Check\">\r\n          <el-radio-group v-model=\"form.Is_Need_Check\" @change=\"radioChange\">\r\n            <el-radio :label=\"true\">是</el-radio>\r\n            <el-radio :label=\"false\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否互检\" prop=\"Is_Inter_Check\">\r\n          <el-radio-group v-model=\"form.Is_Inter_Check\" @change=\"radioInterChange\">\r\n            <el-radio :label=\"true\">是</el-radio>\r\n            <el-radio :label=\"false\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否下料工序\" prop=\"Is_Cutting\">\r\n          <el-radio-group v-model=\"form.Is_Cutting\">\r\n            <el-radio :label=\"true\">是</el-radio>\r\n            <el-radio :label=\"false\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否套料工序\" prop=\"Is_Nest\">\r\n          <el-radio-group v-model=\"form.Is_Nest\">\r\n            <el-radio :label=\"true\">是</el-radio>\r\n            <el-radio :label=\"false\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <template v-if=\"form.Is_Need_Check\">\r\n          <el-form-item label=\"专检方式\" prop=\"Check_Style\">\r\n            <el-radio-group v-model=\"form.Check_Style\" @change=\"radioCheckStyleChange\">\r\n              <el-radio label=\"0\">抽检</el-radio>\r\n              <el-radio label=\"1\">全检</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"专检类型\" prop=\"\">\r\n            <div>\r\n              <div style=\"margin-bottom: 10px;\">\r\n                <el-checkbox v-model=\"form.Is_Need_TC\" @change=\"checkboxChange($event, 1)\">\r\n                  <span> 探伤</span>\r\n                </el-checkbox>\r\n                <span style=\"margin-left: 30px; \">\r\n                  <span style=\"color: rgba(34, 40, 52, 0.65)\">探伤员：</span>\r\n                  <el-select v-model=\"TC_Check_UserIds\" filterable clearable :disabled=\"!form.Is_Need_TC\" multiple\r\n                    placeholder=\"请选择探伤员\" @change=\"changeTc\">\r\n                    <el-option v-for=\"item in optionsUserList\" :key=\"item.Id\" :label=\"item.Display_Name\"\r\n                      :value=\"item.Id\" />\r\n                  </el-select>\r\n                </span>\r\n              </div>\r\n              <div>\r\n                <el-checkbox v-model=\"form.Is_Need_ZL\" @change=\"checkboxChange($event, 2)\">\r\n                  <span> 质量</span>\r\n                </el-checkbox>\r\n                <span style=\"margin-left: 30px\">\r\n                  <span style=\"color: rgba(34, 40, 52, 0.65)\">质检员：</span>\r\n                  <el-select v-model=\"ZL_Check_UserIds\" :disabled=\"!form.Is_Need_ZL\" filterable clearable multiple\r\n                    placeholder=\"请选择质检员\" @change=\"changeZL\">\r\n                    <el-option v-for=\"item in optionsUserList\" :key=\"item.Id\" :label=\"item.Display_Name\"\r\n                      :value=\"item.Id\" />\r\n                  </el-select>\r\n                </span>\r\n              </div>\r\n\r\n            </div>\r\n          </el-form-item>\r\n\r\n          <!-- <el-form-item label=\"检查项组合\" prop=\"Check_Group_List\">\r\n           <el-select\r\n             v-model=\"form.Check_Group_List\"\r\n             multiple\r\n             style=\"width: 100%\"\r\n             :disabled=\"!Boolean(form.Type)\"\r\n             placeholder=\"请选择检查项组合\"\r\n           >\r\n             <el-option\r\n               v-for=\"item in optionsGroupList\"\r\n               :key=\"item.Id\"\r\n               :label=\"item.Group_Name\"\r\n               :value=\"item.Id\"\r\n             />\r\n           </el-select>\r\n         </el-form-item> -->\r\n        </template>\r\n\r\n        <el-form-item label=\"是否启用\" prop=\"Is_Enable\">\r\n          <el-radio-group v-model=\"form.Is_Enable\">\r\n            <el-radio :label=\"true\">是</el-radio>\r\n            <el-radio :label=\"false\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"加工班组\" prop=\"Working_Team_Ids\">\r\n          <el-select v-model=\"form.Working_Team_Ids\" multiple style=\"width: 100%\" placeholder=\"请选择加工班组\">\r\n            <el-option v-for=\"item in optionsWorkingTeamsList\" :key=\"item.Id\" :label=\"item.Name\" :value=\"item.Id\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"form.Remark\" type=\"textarea\" />\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n    <div class=\"btn-x\">\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button :loading=\"btnLoading\" type=\"primary\" @click=\"handleSubmit\">确 定\r\n      </el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\nimport { GetUserList } from '@/api/sys'\r\nimport {\r\n  AddWorkingProcess,\r\n  GetFactoryPeoplelist,\r\n  GetCheckGroupList,\r\n  GetWorkingTeams\r\n} from '@/api/PRO/technology-lib'\r\nimport { mapGetters } from 'vuex'\r\nimport { GetDictionaryDetailListByCode } from '@/api/sys'\r\nexport default {\r\n  props: {\r\n    type: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    rowInfo: {\r\n      type: Object,\r\n      default() {\r\n        return {}\r\n      }\r\n    },\r\n    bomList: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      checkList: [],\r\n      radioList: [],\r\n      btnLoading: false,\r\n      hiddenPart: false,\r\n\r\n      form: {\r\n        Code: '',\r\n        Name: '',\r\n        Bom_Level: '',\r\n        Month_Avg_Load: '',\r\n        Coordinate_UserId: '',\r\n        Sort: undefined,\r\n        Is_Enable: true,\r\n        Is_External: false,\r\n        Is_Nest: false,\r\n        Is_Need_Check: true,\r\n        Is_Inter_Check: true,\r\n        Is_Need_TC: true,\r\n        Is_Welding_Assembling: false,\r\n        Is_Cutting: false,\r\n        TC_Check_UserId: '',\r\n        Is_Need_ZL: false,\r\n        ZL_Check_UserId: '',\r\n\r\n        Check_Style: '0',\r\n\r\n        Working_Team_Ids: [],\r\n        Remark: ''\r\n      },\r\n      ZL_Check_UserIds: [],\r\n      TC_Check_UserIds: [],\r\n      CheckChange: true,\r\n      userOptions: [],\r\n      optionsUserList: [],\r\n      optionsGroupList: [],\r\n      optionsWorkingTeamsList: [],\r\n      rules: {\r\n        Code: [\r\n          { required: true, message: '请输入代号', trigger: 'blur' },\r\n          { max: 30, message: '长度在 30 个字符内', trigger: 'blur' }\r\n        ],\r\n        Name: [\r\n          { required: true, message: '请输入名称', trigger: 'blur' },\r\n          { max: 30, message: '长度在 30 个字符内', trigger: 'blur' }\r\n        ],\r\n        Bom_Level: [{ required: true, message: '请选择类型', trigger: 'change' }],\r\n        Sort: [{ required: true, message: '请输入', trigger: 'blur' }],\r\n        Is_Need_Check: [\r\n          { required: true, message: '请选择是否质检', trigger: 'change' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters('tenant', ['isVersionFour'])\r\n\r\n  },\r\n  mounted() {\r\n    this.getUserList()\r\n    this.getFactoryPeoplelist()\r\n    // this.getCheckGroupList();\r\n    // this.SelectioncheckCombination()\r\n    this.getWorkingTeamsList()\r\n    console.log('type', this.rowInfo)\r\n    this.type === 'edit' && this.initForm()\r\n    this.getBOMInfo()\r\n  },\r\n  methods: {\r\n\r\n    radioInterChange(val) { },\r\n    getBOMInfo() {\r\n      this.radioList = this.bomList\r\n    },\r\n    // 获取设备类型\r\n    async getDictionaryDetailListByCode() {\r\n      await GetDictionaryDetailListByCode({ dictionaryCode: 'deviceType' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.deviceTypeList = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      console.log(' this.optionsGroupList', this.optionsGroupList)\r\n    },\r\n\r\n    initForm() {\r\n      this.getBOMInfo()\r\n      const { Is_Nest, ...others } = this.rowInfo\r\n      this.form = Object.assign({}, others, { Is_Nest: !!Is_Nest })\r\n      console.log('others', others)\r\n      this.form.Bom_Level = String(this.form.Bom_Level)\r\n      //  if(this.form.Type==2){\r\n      //   this.form.Types = '0'\r\n      //  }else if(this.form.Type==3){\r\n      //   let Types = this.radioList.find(v => ['1', '2','3'].includes(v.Code))?.Code\r\n      //   console.log('Types', Types)\r\n      //   console.log('this.radioList', this.radioList)\r\n      //   this.form.Types = Types\r\n      //  }else if(this.form.Type==1){\r\n      //   this.form.Types = '-1'\r\n      //  }\r\n      console.log('this.form', this.form)\r\n\r\n      // 处理历史数据多选问题\r\n      // if (this.form.Is_Need_Check) {\r\n      //   if (this.form.Check_Style === '1') {\r\n\r\n      //   } else {\r\n      //     this.CheckChange = !!this.form.Is_Need_TC\r\n      //     if (this.form.Is_Need_ZL && this.form.Is_Need_TC) {\r\n      //       this.form.Is_Need_TC = true\r\n      //       this.form.Is_Need_ZL = false\r\n      //     }\r\n      //   }\r\n      // }\r\n      this.ZL_Check_UserIds = this.form.ZL_Check_UserId\r\n        ? this.form.ZL_Check_UserId.split(',')\r\n        : []\r\n      this.TC_Check_UserIds = this.form.TC_Check_UserId\r\n        ? this.form.TC_Check_UserId.split(',')\r\n        : []\r\n      console.log('this.TC_Check_UserIds', this.TC_Check_UserIds)\r\n    },\r\n    getUserList() {\r\n      GetUserList({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.userOptions = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getFactoryPeoplelist() {\r\n      GetFactoryPeoplelist({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.optionsUserList = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async getCheckGroupList() {\r\n      await GetCheckGroupList({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.optionsGroupList = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      console.log(' this.optionsGroupList', this.optionsGroupList)\r\n    },\r\n    getWorkingTeamsList() {\r\n      GetWorkingTeams({}).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.optionsWorkingTeamsList = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 筛选检查项组合\r\n    // async  SelectioncheckCombination(){\r\n    //   this.form.Check_Group_List = []\r\n    //   this.optionsGroupList = []\r\n    //   await this.getCheckGroupList();\r\n    //   let optionsGroupListTemp = JSON.parse(JSON.stringify(this.optionsGroupList));\r\n\r\n    //   console.log(\"SelectioncheckCombination1\", optionsGroupListTemp)\r\n    //   this.form.Type == 1  && optionsGroupListTemp &&  (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Object_Id === \"1\").length > 0 ? optionsGroupListTemp.filter(v =>v.Check_Object_Id === \"1\") : optionsGroupListTemp) //通过工序筛选 Type= 1 Check_Type = 0 构件 2 Check_Type = 1 零件\r\n    //   console.log(\"1\", optionsGroupListTemp)\r\n    //   this.form.Type == 2  && optionsGroupListTemp &&  (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Object_Id === \"2\").length > 0 ? optionsGroupListTemp.filter(v => v.Check_Object_Id === \"2\") : optionsGroupListTemp)\r\n    //   if(this.form.Is_Need_TC && this.form.Is_Need_ZL){\r\n    //     optionsGroupListTemp =  optionsGroupListTemp.filter(v => v.Check_Type === -1).length > 0 ? optionsGroupListTemp.filter(v => v.Check_Type === -1) : optionsGroupListTemp\r\n    //     console.log(\"2\", optionsGroupListTemp,this.form.Is_Need_TC)\r\n\r\n    //   }else{\r\n    //     if(this.form.Is_Need_TC || this.form.Is_Need_ZL){\r\n    //       this.form.Is_Need_TC && optionsGroupListTemp && (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Type === 2 ).length > 0 ? optionsGroupListTemp.filter(v => v.Check_Type === 2 || v.Check_Type === -1) : optionsGroupListTemp.filter(v => v.Check_Type === -1))\r\n    //       this.form.Type == 1  && optionsGroupListTemp &&  (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Object_Id === \"1\").length > 0 ? optionsGroupListTemp.filter(v =>v.Check_Object_Id === \"1\") : optionsGroupListTemp) //通过工序筛选 Type= 1 Check_Type = 0 构件 2 Check_Type = 1 零件\r\n    //        console.log(\"3\", optionsGroupListTemp)\r\n    //        this.form.Type == 2  && optionsGroupListTemp &&  (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Object_Id === \"2\").length > 0 ? optionsGroupListTemp.filter(v => v.Check_Object_Id === \"2\") : optionsGroupListTemp)\r\n    //       console.log(\"4\", optionsGroupListTemp,this.form.Is_Need_TC)\r\n    //       this.form.Is_Need_ZL && optionsGroupListTemp && (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Type === 1 ).length > 0 ? optionsGroupListTemp.filter(v => v.Check_Type === 1 || v.Check_Type === -1) :  optionsGroupListTemp.filter(v => v.Check_Type === -1))  //通过质检类型筛选\r\n    //       this.form.Type == 1  && optionsGroupListTemp &&  (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Object_Id === \"1\").length > 0 ? optionsGroupListTemp.filter(v =>v.Check_Object_Id === \"1\") : optionsGroupListTemp) //通过工序筛选 Type= 1 Check_Type = 0 构件 2 Check_Type = 1 零件\r\n    //       console.log(\"5\", optionsGroupListTemp)\r\n    //       this.form.Type == 2  && optionsGroupListTemp &&  (optionsGroupListTemp = optionsGroupListTemp.filter(v => v.Check_Object_Id === \"2\").length > 0 ? optionsGroupListTemp.filter(v => v.Check_Object_Id === \"2\") : optionsGroupListTemp)\r\n    //       console.log(\"6\", optionsGroupListTemp,this.form.Is_Need_ZL)\r\n    //      }\r\n    //   }\r\n\r\n    //   this.optionsGroupList = optionsGroupListTemp\r\n\r\n    //   console.log(\"optionsGroupListTemp\",optionsGroupListTemp)\r\n    //   console.log(\"SelectioncheckCombination\",this.optionsGroupList)\r\n\r\n    // },\r\n    radioCheckStyleChange(val) {\r\n      // if (val === '0') {\r\n      //   this.form.Is_Need_TC = true\r\n      //   this.form.Is_Need_ZL = false\r\n      // }\r\n      this.ZL_Check_UserIds = []\r\n      this.TC_Check_UserIds = []\r\n      this.form.ZL_Check_UserId = ''\r\n      this.form.TC_Check_UserId = ''\r\n    },\r\n    // radioCheckTypeChange(val) {\r\n    //   if (val) {\r\n    //     this.form.Is_Need_TC = true\r\n    //     this.form.Is_Need_ZL = false\r\n    //     this.ZL_Check_UserIds = []\r\n    //     this.form.ZL_Check_UserId = ''\r\n    //   } else {\r\n    //     this.form.Is_Need_ZL = true\r\n    //     this.form.Is_Need_TC = false\r\n    //     this.TC_Check_UserIds = []\r\n    //     this.form.TC_Check_UserId = ''\r\n    //   }\r\n    //   // this.ZL_Check_UserIds = [];\r\n    //   // this.TC_Check_UserIds = [];\r\n    //   // this.form.ZL_Check_UserId = \"\";\r\n    //   // this.form.TC_Check_UserId = \"\";\r\n    // },\r\n    radioChange(val) {\r\n      if (val == false) {\r\n        this.form.checkChange = false\r\n        this.form.Is_Need_TC = false\r\n        this.form.Is_Need_ZL = false\r\n        this.TC_Check_UserIds = []\r\n        this.ZL_Check_UserIds = []\r\n        this.form.ZL_Check_UserId = ''\r\n        this.form.TC_Check_UserId = ''\r\n        this.form.Check_Style = ''\r\n      } else {\r\n        // this.form.checkChange = true\r\n        // this.form.Is_Need_TC = true\r\n        // this.form.Is_Need_ZL = false\r\n        // this.CheckChange = !!this.form.Is_Need_TC\r\n        this.form.Check_Style = '0'\r\n      }\r\n    },\r\n    // 选择构件工序\r\n    changeType(val) {\r\n      // this.SelectioncheckCombination()\r\n      // const Code = val\r\n      // console.log(Code, 'Code');\r\n      // if (Code === '-1') {\r\n      //   this.form.Type = 1\r\n      // } else if (Code === '0') {\r\n      //   this.form.Type = 2\r\n      // } else if (Code === '1' || Code === '3'|| Code === '2') {\r\n      //   this.form.Type = 3\r\n      // }\r\n      // if (this.form.Type === 1 || this.form.Type === 3) {\r\n      //   this.form.Is_Cutting = undefined\r\n      // } else if (this.form.Type === 2) {\r\n      //   this.form.Is_Welding_Assembling = undefined\r\n      // }\r\n    },\r\n    typeChange() {\r\n      this.form.Task_Model = ''\r\n    },\r\n    changeTc(val) {\r\n      console.log(val)\r\n      this.form.TC_Check_UserId = ''\r\n      for (let i = 0; i < val.length; i++) {\r\n        if (i == val.length - 1) {\r\n          this.form.TC_Check_UserId += val[i]\r\n        } else {\r\n          this.form.TC_Check_UserId += val[i] + ','\r\n        }\r\n      }\r\n      console.log(this.form.TC_Check_UserId, 'this.form.TC_Check_UserId ')\r\n    },\r\n    changeZL(val) {\r\n      console.log(val)\r\n      this.form.ZL_Check_UserId = ''\r\n      for (let i = 0; i < val.length; i++) {\r\n        if (i == val.length - 1) {\r\n          this.form.ZL_Check_UserId += val[i]\r\n        } else {\r\n          this.form.ZL_Check_UserId += val[i] + ','\r\n        }\r\n      }\r\n    },\r\n    checkboxChange(val, type) {\r\n      if (type === 1) {\r\n        if (!val) {\r\n          this.TC_Check_UserIds = []\r\n        }\r\n      }\r\n      if (type === 2) {\r\n        if (!val) {\r\n          this.ZL_Check_UserIds = []\r\n        }\r\n      }\r\n    },\r\n    handleSubmit() {\r\n      // delete this.form.Types\r\n      console.log(this.form, 'this.form')\r\n      this.$refs.form.validate((valid) => {\r\n        if (!valid) return\r\n        this.btnLoading = true\r\n        const uItems = this.optionsUserList.find(\r\n          (v) => v.Id === this.form.Coordinate_UserId\r\n        )\r\n        if (uItems) {\r\n          this.form.Coordinate_UserName = uItems.Display_Name\r\n        }\r\n        if (this.form.Is_Need_Check) {\r\n          if (this.form.Is_Need_ZL == false && this.form.Is_Need_TC == false) {\r\n            this.$message.error('请选择质检类型')\r\n            this.btnLoading = false\r\n            return\r\n          }\r\n        } else {\r\n          this.form.Check_Style = null\r\n          this.form.Check_Group_List = []\r\n        }\r\n        const ZL = this.form.Is_Need_ZL ? this.form.ZL_Check_UserId : ''\r\n        const TC = this.form.Is_Need_TC ? this.form.TC_Check_UserId : ''\r\n        if (this.form.Is_Need_ZL && (ZL ?? '') == '') {\r\n          this.$message.error('请选择质检员')\r\n          this.btnLoading = false\r\n          return\r\n        }\r\n        if (this.form.Is_Need_TC && (TC ?? '') == '') {\r\n          this.$message.error('请选择探伤员')\r\n          this.btnLoading = false\r\n          return\r\n        }\r\n\r\n        AddWorkingProcess({\r\n          ...this.form,\r\n          ZL_Check_UserId: ZL,\r\n          TC_Check_UserId: TC\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            this.$emit('refresh')\r\n            this.$emit('close')\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.btnLoading = false\r\n        })\r\n      })\r\n    },\r\n\r\n    codeChange(e) {\r\n      return e.replace(/[^a-zA-Z0-9]/g, '')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/styles/mixin.scss\";\r\n\r\n.btn-del {\r\n  margin-left: -100px;\r\n}\r\n\r\n.customRadioClass {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.checkboxFlex {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.form-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  min-height: 40vh;\r\n\r\n  .form-x {\r\n    max-height: 70vh;\r\n    overflow: auto;\r\n    padding-right: 16px;\r\n    @include scrollBar;\r\n  }\r\n\r\n  .btn-x {\r\n    padding-top: 16px;\r\n    text-align: right;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4JA,SAAAA,UAAA;AACA,SAAAC,WAAA;AACA,SACAC,iBAAA,EACAC,oBAAA,EACAC,iBAAA,EACAC,eAAA,QACA;AACA,SAAAC,UAAA;AACA,SAAAC,6BAAA;AACA;EACAC,KAAA;IACAC,IAAA;MACAA,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,OAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA,WAAAA,SAAA;QACA;MACA;IACA;IACAG,OAAA;MACAL,IAAA,EAAAM,KAAA;MACAJ,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAK,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,SAAA;MACAC,UAAA;MACAC,UAAA;MAEAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,SAAA;QACAC,cAAA;QACAC,iBAAA;QACAC,IAAA,EAAAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,OAAA;QACAC,aAAA;QACAC,cAAA;QACAC,UAAA;QACAC,qBAAA;QACAC,UAAA;QACAC,eAAA;QACAC,UAAA;QACAC,eAAA;QAEAC,WAAA;QAEAC,gBAAA;QACAC,MAAA;MACA;MACAC,gBAAA;MACAC,gBAAA;MACAC,WAAA;MACAC,WAAA;MACAC,eAAA;MACAC,gBAAA;MACAC,uBAAA;MACAC,KAAA;QACA5B,IAAA,GACA;UAAA6B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACA9B,IAAA,GACA;UAAA4B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACA7B,SAAA;UAAA2B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACA1B,IAAA;UAAAwB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACArB,aAAA,GACA;UAAAmB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAE,QAAA,EAAAC,aAAA,KACAlD,UAAA,8BAEA;EACAmD,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA;IACA,KAAAC,oBAAA;IACA;IACA;IACA,KAAAC,mBAAA;IACAC,OAAA,CAAAC,GAAA,cAAAlD,OAAA;IACA,KAAAH,IAAA,oBAAAsD,QAAA;IACA,KAAAC,UAAA;EACA;EACAC,OAAA;IAEAC,gBAAA,WAAAA,iBAAAC,GAAA;IACAH,UAAA,WAAAA,WAAA;MACA,KAAA9C,SAAA,QAAAJ,OAAA;IACA;IACA;IACAsD,6BAAA,WAAAA,8BAAA;MAAA,IAAAC,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACAvE,6BAAA;gBAAAwE,cAAA;cAAA,GAAAC,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAb,KAAA,CAAAc,cAAA,GAAAF,GAAA,CAAAG,IAAA;gBACA;kBACAf,KAAA,CAAAgB,QAAA;oBACAjC,OAAA,EAAA6B,GAAA,CAAAK,OAAA;oBACA7E,IAAA;kBACA;gBACA;cACA;YAAA;cACAoD,OAAA,CAAAC,GAAA,2BAAAO,KAAA,CAAArB,gBAAA;YAAA;YAAA;cAAA,OAAA4B,QAAA,CAAAW,IAAA;UAAA;QAAA,GAAAd,OAAA;MAAA;IACA;IAEAV,QAAA,WAAAA,SAAA;MACA,KAAAC,UAAA;MACA,IAAAwB,aAAA,QAAA5E,OAAA;QAAAmB,OAAA,GAAAyD,aAAA,CAAAzD,OAAA;QAAA0D,MAAA,GAAAC,wBAAA,CAAAF,aAAA,EAAAG,SAAA;MACA,KAAAtE,IAAA,GAAAR,MAAA,CAAA+E,MAAA,KAAAH,MAAA;QAAA1D,OAAA,IAAAA;MAAA;MACA8B,OAAA,CAAAC,GAAA,WAAA2B,MAAA;MACA,KAAApE,IAAA,CAAAG,SAAA,GAAAd,MAAA,MAAAW,IAAA,CAAAG,SAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAqC,OAAA,CAAAC,GAAA,mBAAAzC,IAAA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,KAAAsB,gBAAA,QAAAtB,IAAA,CAAAkB,eAAA,GACA,KAAAlB,IAAA,CAAAkB,eAAA,CAAAsD,KAAA,QACA;MACA,KAAAjD,gBAAA,QAAAvB,IAAA,CAAAgB,eAAA,GACA,KAAAhB,IAAA,CAAAgB,eAAA,CAAAwD,KAAA,QACA;MACAhC,OAAA,CAAAC,GAAA,+BAAAlB,gBAAA;IACA;IACAc,WAAA,WAAAA,YAAA;MAAA,IAAAoC,MAAA;MACA7F,WAAA,KAAA+E,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAY,MAAA,CAAAhD,WAAA,GAAAmC,GAAA,CAAAG,IAAA;QACA;UACAU,MAAA,CAAAT,QAAA;YACAjC,OAAA,EAAA6B,GAAA,CAAAK,OAAA;YACA7E,IAAA;UACA;QACA;MACA;IACA;IACAkD,oBAAA,WAAAA,qBAAA;MAAA,IAAAoC,MAAA;MACA5F,oBAAA,KAAA6E,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAa,MAAA,CAAAhD,eAAA,GAAAkC,GAAA,CAAAG,IAAA;QACA;UACAW,MAAA,CAAAV,QAAA;YACAjC,OAAA,EAAA6B,GAAA,CAAAK,OAAA;YACA7E,IAAA;UACA;QACA;MACA;IACA;IACAuF,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MAAA,OAAA3B,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA0B,SAAA;QAAA,OAAA3B,mBAAA,GAAAG,IAAA,UAAAyB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvB,IAAA,GAAAuB,SAAA,CAAAtB,IAAA;YAAA;cAAAsB,SAAA,CAAAtB,IAAA;cAAA,OACA1E,iBAAA,KAAA4E,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAe,MAAA,CAAAjD,gBAAA,GAAAiC,GAAA,CAAAG,IAAA;gBACA;kBACAa,MAAA,CAAAZ,QAAA;oBACAjC,OAAA,EAAA6B,GAAA,CAAAK,OAAA;oBACA7E,IAAA;kBACA;gBACA;cACA;YAAA;cACAoD,OAAA,CAAAC,GAAA,2BAAAmC,MAAA,CAAAjD,gBAAA;YAAA;YAAA;cAAA,OAAAoD,SAAA,CAAAb,IAAA;UAAA;QAAA,GAAAW,QAAA;MAAA;IACA;IACAtC,mBAAA,WAAAA,oBAAA;MAAA,IAAAyC,MAAA;MACAhG,eAAA,KAAA2E,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAmB,MAAA,CAAApD,uBAAA,GAAAgC,GAAA,CAAAG,IAAA;QACA;UACAiB,MAAA,CAAAhB,QAAA;YACAjC,OAAA,EAAA6B,GAAA,CAAAK,OAAA;YACA7E,IAAA;UACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IAEA;IACA;IAEA;IACA6F,qBAAA,WAAAA,sBAAAnC,GAAA;MACA;MACA;MACA;MACA;MACA,KAAAxB,gBAAA;MACA,KAAAC,gBAAA;MACA,KAAAvB,IAAA,CAAAkB,eAAA;MACA,KAAAlB,IAAA,CAAAgB,eAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAkE,WAAA,WAAAA,YAAApC,GAAA;MACA,IAAAA,GAAA;QACA,KAAA9C,IAAA,CAAAmF,WAAA;QACA,KAAAnF,IAAA,CAAAa,UAAA;QACA,KAAAb,IAAA,CAAAiB,UAAA;QACA,KAAAM,gBAAA;QACA,KAAAD,gBAAA;QACA,KAAAtB,IAAA,CAAAkB,eAAA;QACA,KAAAlB,IAAA,CAAAgB,eAAA;QACA,KAAAhB,IAAA,CAAAmB,WAAA;MACA;QACA;QACA;QACA;QACA;QACA,KAAAnB,IAAA,CAAAmB,WAAA;MACA;IACA;IACA;IACAiE,UAAA,WAAAA,WAAAtC,GAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA,CACA;IACAuC,UAAA,WAAAA,WAAA;MACA,KAAArF,IAAA,CAAAsF,UAAA;IACA;IACAC,QAAA,WAAAA,SAAAzC,GAAA;MACAN,OAAA,CAAAC,GAAA,CAAAK,GAAA;MACA,KAAA9C,IAAA,CAAAgB,eAAA;MACA,SAAAwE,CAAA,MAAAA,CAAA,GAAA1C,GAAA,CAAA2C,MAAA,EAAAD,CAAA;QACA,IAAAA,CAAA,IAAA1C,GAAA,CAAA2C,MAAA;UACA,KAAAzF,IAAA,CAAAgB,eAAA,IAAA8B,GAAA,CAAA0C,CAAA;QACA;UACA,KAAAxF,IAAA,CAAAgB,eAAA,IAAA8B,GAAA,CAAA0C,CAAA;QACA;MACA;MACAhD,OAAA,CAAAC,GAAA,MAAAzC,IAAA,CAAAgB,eAAA;IACA;IACA0E,QAAA,WAAAA,SAAA5C,GAAA;MACAN,OAAA,CAAAC,GAAA,CAAAK,GAAA;MACA,KAAA9C,IAAA,CAAAkB,eAAA;MACA,SAAAsE,CAAA,MAAAA,CAAA,GAAA1C,GAAA,CAAA2C,MAAA,EAAAD,CAAA;QACA,IAAAA,CAAA,IAAA1C,GAAA,CAAA2C,MAAA;UACA,KAAAzF,IAAA,CAAAkB,eAAA,IAAA4B,GAAA,CAAA0C,CAAA;QACA;UACA,KAAAxF,IAAA,CAAAkB,eAAA,IAAA4B,GAAA,CAAA0C,CAAA;QACA;MACA;IACA;IACAG,cAAA,WAAAA,eAAA7C,GAAA,EAAA1D,IAAA;MACA,IAAAA,IAAA;QACA,KAAA0D,GAAA;UACA,KAAAvB,gBAAA;QACA;MACA;MACA,IAAAnC,IAAA;QACA,KAAA0D,GAAA;UACA,KAAAxB,gBAAA;QACA;MACA;IACA;IACAsE,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA;MACArD,OAAA,CAAAC,GAAA,MAAAzC,IAAA;MACA,KAAA8F,KAAA,CAAA9F,IAAA,CAAA+F,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;QACAH,MAAA,CAAA/F,UAAA;QACA,IAAAmG,MAAA,GAAAJ,MAAA,CAAAnE,eAAA,CAAAwE,IAAA,CACA,UAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,EAAA,KAAAP,MAAA,CAAA7F,IAAA,CAAAK,iBAAA;QAAA,CACA;QACA,IAAA4F,MAAA;UACAJ,MAAA,CAAA7F,IAAA,CAAAqG,mBAAA,GAAAJ,MAAA,CAAAK,YAAA;QACA;QACA,IAAAT,MAAA,CAAA7F,IAAA,CAAAW,aAAA;UACA,IAAAkF,MAAA,CAAA7F,IAAA,CAAAiB,UAAA,aAAA4E,MAAA,CAAA7F,IAAA,CAAAa,UAAA;YACAgF,MAAA,CAAA7B,QAAA,CAAAuC,KAAA;YACAV,MAAA,CAAA/F,UAAA;YACA;UACA;QACA;UACA+F,MAAA,CAAA7F,IAAA,CAAAmB,WAAA;UACA0E,MAAA,CAAA7F,IAAA,CAAAwG,gBAAA;QACA;QACA,IAAAC,EAAA,GAAAZ,MAAA,CAAA7F,IAAA,CAAAiB,UAAA,GAAA4E,MAAA,CAAA7F,IAAA,CAAAkB,eAAA;QACA,IAAAwF,EAAA,GAAAb,MAAA,CAAA7F,IAAA,CAAAa,UAAA,GAAAgF,MAAA,CAAA7F,IAAA,CAAAgB,eAAA;QACA,IAAA6E,MAAA,CAAA7F,IAAA,CAAAiB,UAAA,KAAAwF,EAAA,aAAAA,EAAA,cAAAA,EAAA;UACAZ,MAAA,CAAA7B,QAAA,CAAAuC,KAAA;UACAV,MAAA,CAAA/F,UAAA;UACA;QACA;QACA,IAAA+F,MAAA,CAAA7F,IAAA,CAAAa,UAAA,KAAA6F,EAAA,aAAAA,EAAA,cAAAA,EAAA;UACAb,MAAA,CAAA7B,QAAA,CAAAuC,KAAA;UACAV,MAAA,CAAA/F,UAAA;UACA;QACA;QAEAjB,iBAAA,CAAAsD,aAAA,CAAAA,aAAA,KACA0D,MAAA,CAAA7F,IAAA;UACAkB,eAAA,EAAAuF,EAAA;UACAzF,eAAA,EAAA0F;QAAA,EACA,EAAA/C,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAgC,MAAA,CAAA7B,QAAA;cACAjC,OAAA;cACA3C,IAAA;YACA;YACAyG,MAAA,CAAAc,KAAA;YACAd,MAAA,CAAAc,KAAA;UACA;YACAd,MAAA,CAAA7B,QAAA;cACAjC,OAAA,EAAA6B,GAAA,CAAAK,OAAA;cACA7E,IAAA;YACA;UACA;UACAyG,MAAA,CAAA/F,UAAA;QACA;MACA;IACA;IAEA8G,UAAA,WAAAA,WAAAC,CAAA;MACA,OAAAA,CAAA,CAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\Add.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\Add.vue", "mtime": 1757581469561}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetBOMInfo", "GetUserList", "AddWorkingProcess", "GetFactoryPeoplelist", "GetCheckGroupList", "GetWorkingTeams", "mapGetters", "GetDictionaryDetailListByCode", "props", "type", "String", "default", "rowInfo", "Object", "workloadProportionAll", "Number", "data", "checkList", "btnLoading", "hiddenPart", "form", "Code", "Name", "Bom_Level", "Month_Avg_Load", "Coordinate_UserId", "Sort", "undefined", "Is_Enable", "Is_External", "Is_Nest", "Is_Need_Check", "Is_Self_Check", "Is_Inter_Check", "Is_Pick_Material", "Is_Need_TC", "Is_Welding_Assembling", "Is_Cutting", "TC_Check_UserId", "Is_Need_ZL", "ZL_Check_UserId", "Show_Model", "Check_Style", "Working_Team_Ids", "Remark", "Workload_Proportion", "ZL_Check_UserIds", "TC_Check_UserIds", "CheckChange", "userOptions", "optionsUserList", "optionsGroupList", "optionsWorkingTeamsList", "rules", "required", "message", "trigger", "max", "bomList", "comName", "partName", "computed", "_objectSpread", "created", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "_yield$GetBOMInfo", "list", "wrap", "_callee$", "_context", "prev", "next", "sent", "stop", "mounted", "getUserList", "getFactoryPeoplelist", "getWorkingTeamsList", "initForm", "methods", "_this$rowInfo", "others", "_objectWithoutProperties", "_excluded", "assign", "console", "log", "split", "radioSelfCheck", "val", "radioInterCheck", "getDictionaryDetailListByCode", "_this2", "_callee2", "_callee2$", "_context2", "dictionaryCode", "then", "res", "IsSucceed", "deviceTypeList", "Data", "$message", "Message", "_this3", "_this4", "getCheckGroupList", "_this5", "_callee3", "_callee3$", "_context3", "_this6", "radioCheckStyleChange", "radioChange", "checkChange", "changeType", "typeChange", "Task_Model", "changeTc", "i", "length", "changeZL", "checkboxChange", "handleSubmit", "_this7", "$refs", "validate", "valid", "uItems", "find", "v", "Id", "Coordinate_UserName", "Display_Name", "error", "Check_Group_List", "ZL", "TC", "$emit", "codeChange", "e", "replace", "handlePercentageInput", "value", "inputValue", "dotCount", "match", "firstDotIndex", "indexOf", "substring", "numValue", "parseFloat", "isNaN"], "sources": ["src/views/PRO/process-settings/management/component/Add.vue"], "sourcesContent": ["<template>\n  <div class=\"form-wrapper\">\n    <div class=\"form-x\">\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\" style=\"width: 100%\">\n        <el-divider content-position=\"left\">基础信息</el-divider>\n        <el-form-item label=\"名称\" prop=\"Name\">\n          <el-input v-model=\"form.Name\" :maxlength=\"30\" placeholder=\"最多30个字\" show-word-limit />\n        </el-form-item>\n        <el-form-item label=\"代号\" prop=\"Code\">\n          <el-input\n            v-model=\"form.Code\"\n            :maxlength=\"30\"\n            placeholder=\"字母+数字，30字符\"\n            show-word-limit\n            @input=\"(e) => (form.Code = codeChange(e))\"\n          />\n        </el-form-item>\n        <el-form-item label=\"类型\" prop=\"Bom_Level\">\n          <el-radio-group\n            v-for=\"(item, index) in bomList\"\n            :key=\"index\"\n            v-model=\"form.Bom_Level\"\n            class=\"radio\"\n            @change=\"changeType\"\n          >\n            <el-radio style=\"margin-right: 8px;\" :label=\"item.Code\">{{ item.Display_Name }}</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"Sort\">\n          <el-input-number\n            v-model=\"form.Sort\"\n            :min=\"0\"\n            step-strictly\n            :step=\"1\"\n            class=\"cs-number-btn-hidden w100\"\n            placeholder=\"请输入\"\n            clearable=\"\"\n          />\n        </el-form-item>\n        <el-form-item label=\"协调人\" prop=\"Coordinate_UserId\">\n          <el-select v-model=\"form.Coordinate_UserId\" class=\"w100\" clearable filterable placeholder=\"请选择\">\n            <el-option v-for=\"item in optionsUserList\" :key=\"item.Id\" :label=\"item.Display_Name\" :value=\"item.Id\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"工序月均负荷\" prop=\"Month_Avg_Load\">\n          <el-input v-model=\"form.Month_Avg_Load\" placeholder=\"请输入\">\n            <template slot=\"append\">吨</template>\n          </el-input>\n        </el-form-item>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否启用\" prop=\"Is_Enable\">\n              <el-radio-group v-model=\"form.Is_Enable\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否外协\" prop=\"Is_External\">\n              <el-radio-group v-model=\"form.Is_External\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否装焊工序\" prop=\"Is_Welding_Assembling\">\n              <el-radio-group v-model=\"form.Is_Welding_Assembling\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否下料工序\" prop=\"Is_Cutting\">\n              <el-radio-group v-model=\"form.Is_Cutting\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否套料工序\" prop=\"Is_Nest\">\n              <el-radio-group v-model=\"form.Is_Nest\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否领料工序\" prop=\"Is_Pick_Material\">\n              <el-radio-group v-model=\"form.Is_Pick_Material\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"加工班组\" prop=\"Working_Team_Ids\">\n          <el-select v-model=\"form.Working_Team_Ids\" multiple style=\"width: 100%\" placeholder=\"请选择加工班组\">\n            <el-option v-for=\"item in optionsWorkingTeamsList\" :key=\"item.Id\" :label=\"item.Name\" :value=\"item.Id\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"备注\">\n          <el-input v-model=\"form.Remark\" type=\"textarea\" />\n        </el-form-item>\n        <el-divider content-position=\"left\">质检信息</el-divider>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否自检\" prop=\"Is_Self_Check\">\n              <el-radio-group v-model=\"form.Is_Self_Check\" @change=\"radioSelfCheck\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否互检\" prop=\"Is_Inter_Check\">\n              <el-radio-group v-model=\"form.Is_Inter_Check\" @change=\"radioInterCheck\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否专检\" prop=\"Is_Need_Check\">\n              <el-radio-group v-model=\"form.Is_Need_Check\" @change=\"radioChange\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <template v-if=\"form.Is_Need_Check\">\n              <el-form-item label=\"专检方式\" prop=\"Check_Style\">\n                <el-radio-group v-model=\"form.Check_Style\" @change=\"radioCheckStyleChange\">\n                  <el-radio label=\"0\">抽检</el-radio>\n                  <el-radio label=\"1\">全检</el-radio>\n                </el-radio-group>\n              </el-form-item>\n            </template>\n          </el-col>\n        </el-row>\n\n        <template v-if=\"form.Is_Need_Check\">\n          <el-form-item label=\"专检类型\" prop=\"\">\n            <div>\n              <div style=\"margin-bottom: 10px;\">\n                <el-checkbox v-model=\"form.Is_Need_TC\" @change=\"checkboxChange($event, 1)\">\n                  <span> 探伤</span>\n                </el-checkbox>\n                <span style=\"margin-left: 30px; \">\n                  <span style=\"color: rgba(34, 40, 52, 0.65)\">探伤员：</span>\n                  <el-select\n                    v-model=\"TC_Check_UserIds\"\n                    filterable\n                    clearable\n                    :disabled=\"!form.Is_Need_TC\"\n                    multiple\n                    placeholder=\"请选择探伤员\"\n                    @change=\"changeTc\"\n                  >\n                    <el-option\n                      v-for=\"item in optionsUserList\"\n                      :key=\"item.Id\"\n                      :label=\"item.Display_Name\"\n                      :value=\"item.Id\"\n                    />\n                  </el-select>\n                </span>\n              </div>\n              <div>\n                <el-checkbox v-model=\"form.Is_Need_ZL\" @change=\"checkboxChange($event, 2)\">\n                  <span> 质量</span>\n                </el-checkbox>\n                <span style=\"margin-left: 30px\">\n                  <span style=\"color: rgba(34, 40, 52, 0.65)\">质检员：</span>\n                  <el-select\n                    v-model=\"ZL_Check_UserIds\"\n                    :disabled=\"!form.Is_Need_ZL\"\n                    filterable\n                    clearable\n                    multiple\n                    placeholder=\"请选择质检员\"\n                    @change=\"changeZL\"\n                  >\n                    <el-option\n                      v-for=\"item in optionsUserList\"\n                      :key=\"item.Id\"\n                      :label=\"item.Display_Name\"\n                      :value=\"item.Id\"\n                    />\n                  </el-select>\n                </span>\n              </div>\n            </div>\n          </el-form-item>\n        </template>\n        <el-divider content-position=\"left\">其他信息</el-divider>\n        <el-form-item label=\"工作量占比\" prop=\"Workload_Proportion\">\n          <el-input v-model=\"form.Workload_Proportion\" placeholder=\"请输入\" @input=\"handlePercentageInput\">\n            <template slot=\"append\">%</template>\n          </el-input>\n        </el-form-item>\n        <el-form-item label=\"是否展示模型\" prop=\"Show_Model\">\n          <el-radio-group v-model=\"form.Show_Model\">\n            <el-radio :label=\"true\">是</el-radio>\n            <el-radio :label=\"false\">否</el-radio>\n          </el-radio-group>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"btn-x\">\n      <el-button @click=\"$emit('close')\">取 消</el-button>\n      <el-button :loading=\"btnLoading\" type=\"primary\" @click=\"handleSubmit\">确 定\n      </el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport { GetUserList } from '@/api/sys'\nimport {\n  AddWorkingProcess,\n  GetFactoryPeoplelist,\n  GetCheckGroupList,\n  GetWorkingTeams\n} from '@/api/PRO/technology-lib'\nimport { mapGetters } from 'vuex'\nimport { GetDictionaryDetailListByCode } from '@/api/sys'\nexport default {\n  props: {\n    type: {\n      type: String,\n      default: ''\n    },\n    rowInfo: {\n      type: Object,\n      default() {\n        return {}\n      }\n    },\n    workloadProportionAll: {\n      type: Number,\n      default: 0\n    }\n  },\n  data() {\n    return {\n      checkList: [],\n      btnLoading: false,\n      hiddenPart: false,\n\n      form: {\n        Code: '',\n        Name: '',\n        Bom_Level: '',\n        Month_Avg_Load: '',\n        Coordinate_UserId: '',\n        Sort: undefined,\n        Is_Enable: true,\n        Is_External: false,\n        Is_Nest: false,\n        Is_Need_Check: true,\n        Is_Self_Check: true,\n        Is_Inter_Check: true,\n        Is_Pick_Material: false,\n        Is_Need_TC: true,\n        Is_Welding_Assembling: false,\n        Is_Cutting: false,\n        TC_Check_UserId: '',\n        Is_Need_ZL: false,\n        ZL_Check_UserId: '',\n        Show_Model: false,\n\n        Check_Style: '0',\n\n        Working_Team_Ids: [],\n        Remark: '',\n        Workload_Proportion: ''\n      },\n      ZL_Check_UserIds: [],\n      TC_Check_UserIds: [],\n      CheckChange: true,\n      userOptions: [],\n      optionsUserList: [],\n      optionsGroupList: [],\n      optionsWorkingTeamsList: [],\n      rules: {\n        Code: [\n          { required: true, message: '请输入代号', trigger: 'blur' },\n          { max: 30, message: '长度在 30 个字符内', trigger: 'blur' }\n        ],\n        Name: [\n          { required: true, message: '请输入名称', trigger: 'blur' },\n          { max: 30, message: '长度在 30 个字符内', trigger: 'blur' }\n        ],\n        Bom_Level: [{ required: true, message: '请选择类型', trigger: 'change' }],\n        Sort: [{ required: true, message: '请输入', trigger: 'blur' }],\n        Is_Need_Check: [\n          { required: true, message: '请选择是否质检', trigger: 'change' }\n        ]\n      },\n      bomList: [],\n      comName: '',\n      partName: ''\n    }\n  },\n  computed: {\n    ...mapGetters('tenant', ['isVersionFour'])\n  },\n  async created() {\n    const { comName, partName, list } = await GetBOMInfo()\n    this.comName = comName\n    this.partName = partName\n    this.bomList = list\n  },\n  mounted() {\n    this.getUserList()\n    this.getFactoryPeoplelist()\n    // this.getCheckGroupList();\n    this.getWorkingTeamsList()\n    this.type === 'edit' && this.initForm()\n  },\n  methods: {\n    initForm() {\n      const { Is_Nest, ...others } = this.rowInfo\n      this.form = Object.assign({}, others, { Is_Nest: !!Is_Nest })\n      this.form.Bom_Level = String(this.form.Bom_Level)\n      //  if(this.form.Type==2){\n      //   this.form.Types = '0'\n      //  }else if(this.form.Type==3){\n      //   let Types = this.radioList.find(v => ['1', '2','3'].includes(v.Code))?.Code\n      //   console.log('Types', Types)\n      //   console.log('this.radioList', this.radioList)\n      //   this.form.Types = Types\n      //  }else if(this.form.Type==1){\n      //   this.form.Types = '-1'\n      //  }\n      console.log('this.form', this.form)\n\n      // 处理历史数据多选问题\n      // if (this.form.Is_Need_Check) {\n      //   if (this.form.Check_Style === '1') {\n\n      //   } else {\n      //     this.CheckChange = !!this.form.Is_Need_TC\n      //     if (this.form.Is_Need_ZL && this.form.Is_Need_TC) {\n      //       this.form.Is_Need_TC = true\n      //       this.form.Is_Need_ZL = false\n      //     }\n      //   }\n      // }\n      this.ZL_Check_UserIds = this.form.ZL_Check_UserId\n        ? this.form.ZL_Check_UserId.split(',')\n        : []\n      this.TC_Check_UserIds = this.form.TC_Check_UserId\n        ? this.form.TC_Check_UserId.split(',')\n        : []\n    },\n    // 是否自检\n    radioSelfCheck(val) { },\n    // 是否互检\n    radioInterCheck(val) { },\n    // 获取设备类型\n    async getDictionaryDetailListByCode() {\n      await GetDictionaryDetailListByCode({ dictionaryCode: 'deviceType' }).then((res) => {\n        if (res.IsSucceed) {\n          this.deviceTypeList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n      console.log(' this.optionsGroupList', this.optionsGroupList)\n    },\n    getUserList() {\n      GetUserList({}).then((res) => {\n        if (res.IsSucceed) {\n          this.userOptions = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    getFactoryPeoplelist() {\n      GetFactoryPeoplelist({}).then((res) => {\n        if (res.IsSucceed) {\n          this.optionsUserList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    async getCheckGroupList() {\n      await GetCheckGroupList({}).then((res) => {\n        if (res.IsSucceed) {\n          this.optionsGroupList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n      console.log(' this.optionsGroupList', this.optionsGroupList)\n    },\n    getWorkingTeamsList() {\n      GetWorkingTeams({}).then((res) => {\n        if (res.IsSucceed) {\n          this.optionsWorkingTeamsList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    // 选择专检方式 抽检、全检\n    radioCheckStyleChange(val) {\n      // if (val === '0') {\n      //   this.form.Is_Need_TC = true\n      //   this.form.Is_Need_ZL = false\n      // }\n      this.ZL_Check_UserIds = []\n      this.TC_Check_UserIds = []\n      this.form.ZL_Check_UserId = ''\n      this.form.TC_Check_UserId = ''\n    },\n    // 是否专检\n    radioChange(val) {\n      if (val === false) {\n        this.form.checkChange = false\n        this.form.Is_Need_TC = false\n        this.form.Is_Need_ZL = false\n        this.TC_Check_UserIds = []\n        this.ZL_Check_UserIds = []\n        this.form.ZL_Check_UserId = ''\n        this.form.TC_Check_UserId = ''\n        this.form.Check_Style = ''\n      } else {\n        // this.form.checkChange = true\n        // this.form.Is_Need_TC = true\n        // this.form.Is_Need_ZL = false\n        // this.CheckChange = !!this.form.Is_Need_TC\n        this.form.Check_Style = '0'\n      }\n    },\n    // 选择BOM层级\n    changeType(val) {\n      // const Code = val\n      // console.log(Code, 'Code');\n      // if (Code === '-1') {\n      //   this.form.Type = 1\n      // } else if (Code === '0') {\n      //   this.form.Type = 2\n      // } else if (Code === '1' || Code === '3'|| Code === '2') {\n      //   this.form.Type = 3\n      // }\n      // if (this.form.Type === 1 || this.form.Type === 3) {\n      //   this.form.Is_Cutting = undefined\n      // } else if (this.form.Type === 2) {\n      //   this.form.Is_Welding_Assembling = undefined\n      // }\n    },\n    typeChange() {\n      this.form.Task_Model = ''\n    },\n    changeTc(val) {\n      console.log(val)\n      this.form.TC_Check_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i === val.length - 1) {\n          this.form.TC_Check_UserId += val[i]\n        } else {\n          this.form.TC_Check_UserId += val[i] + ','\n        }\n      }\n      console.log(this.form.TC_Check_UserId, 'this.form.TC_Check_UserId ')\n    },\n    changeZL(val) {\n      console.log(val)\n      this.form.ZL_Check_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i === val.length - 1) {\n          this.form.ZL_Check_UserId += val[i]\n        } else {\n          this.form.ZL_Check_UserId += val[i] + ','\n        }\n      }\n    },\n    checkboxChange(val, type) {\n      if (type === 1) {\n        if (!val) {\n          this.TC_Check_UserIds = []\n        }\n      }\n      if (type === 2) {\n        if (!val) {\n          this.ZL_Check_UserIds = []\n        }\n      }\n    },\n    handleSubmit() {\n      // delete this.form.Types\n      console.log(this.form, 'this.form')\n      this.$refs.form.validate((valid) => {\n        if (!valid) return\n        this.btnLoading = true\n        const uItems = this.optionsUserList.find(\n          (v) => v.Id === this.form.Coordinate_UserId\n        )\n        if (uItems) {\n          this.form.Coordinate_UserName = uItems.Display_Name\n        }\n        if (this.form.Is_Need_Check) {\n          if (this.form.Is_Need_ZL === false && this.form.Is_Need_TC === false) {\n            this.$message.error('请选择质检类型')\n            this.btnLoading = false\n            return\n          }\n        } else {\n          this.form.Check_Style = null\n          this.form.Check_Group_List = []\n        }\n        const ZL = this.form.Is_Need_ZL ? this.form.ZL_Check_UserId : ''\n        const TC = this.form.Is_Need_TC ? this.form.TC_Check_UserId : ''\n        if (this.form.Is_Need_ZL && (ZL ?? '') === '') {\n          this.$message.error('请选择质检员')\n          this.btnLoading = false\n          return\n        }\n        if (this.form.Is_Need_TC && (TC ?? '') === '') {\n          this.$message.error('请选择探伤员')\n          this.btnLoading = false\n          return\n        }\n\n        AddWorkingProcess({\n          ...this.form,\n          ZL_Check_UserId: ZL,\n          TC_Check_UserId: TC\n        }).then((res) => {\n          if (res.IsSucceed) {\n            this.$message({\n              message: '保存成功',\n              type: 'success'\n            })\n            this.$emit('refresh')\n            this.$emit('close')\n          } else {\n            this.$message({\n              message: res.Message,\n              type: 'error'\n            })\n          }\n          this.btnLoading = false\n        })\n      })\n    },\n\n    codeChange(e) {\n      return e.replace(/[^a-zA-Z0-9]/g, '')\n    },\n\n    handlePercentageInput(value) {\n      // 如果输入为空，直接返回\n      if (value === '' || value === null || value === undefined) {\n        this.form.Workload_Proportion = ''\n        return\n      }\n\n      // 转换为字符串进行处理\n      let inputValue = String(value)\n\n      // 只允许数字和一个小数点，移除其他字符（包括负号）\n      inputValue = inputValue.replace(/[^0-9.]/g, '')\n\n      // 确保只有一个小数点\n      const dotCount = (inputValue.match(/\\./g) || []).length\n      if (dotCount > 1) {\n        // 如果有多个小数点，只保留第一个\n        const firstDotIndex = inputValue.indexOf('.')\n        inputValue = inputValue.substring(0, firstDotIndex + 1) + inputValue.substring(firstDotIndex + 1).replace(/\\./g, '')\n      }\n\n      // 如果只是小数点，设置为空\n      if (inputValue === '.') {\n        this.form.Workload_Proportion = ''\n        return\n      }\n\n      // 如果处理后为空字符串，设置为空\n      if (inputValue === '') {\n        this.form.Workload_Proportion = ''\n        return\n      }\n\n      // 转换为数字进行范围检查\n      const numValue = parseFloat(inputValue)\n\n      // 如果不是有效数字，设置为空\n      if (isNaN(numValue)) {\n        this.form.Workload_Proportion = ''\n        return\n      }\n\n      // 限制范围在 0-100 之间\n      if (numValue < 0) {\n        this.form.Workload_Proportion = '0'\n      } else if (numValue > 100) {\n        this.form.Workload_Proportion = '100'\n      } else {\n        // 保持原始输入格式（包括小数点）\n        this.form.Workload_Proportion = inputValue\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/mixin.scss\";\n\n.btn-del {\n  margin-left: -100px;\n}\n\n.customRadioClass {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.checkboxFlex {\n  display: flex;\n  align-items: center;\n}\n\n.form-wrapper {\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  min-height: 40vh;\n\n  .form-x {\n    max-height: 70vh;\n    overflow: auto;\n    padding-right: 16px;\n    @include scrollBar;\n  }\n\n  .btn-x {\n    padding-top: 16px;\n    text-align: right;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoOA,SAAAA,UAAA;AACA,SAAAC,WAAA;AACA,SACAC,iBAAA,EACAC,oBAAA,EACAC,iBAAA,EACAC,eAAA,QACA;AACA,SAAAC,UAAA;AACA,SAAAC,6BAAA;AACA;EACAC,KAAA;IACAC,IAAA;MACAA,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,OAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA,WAAAA,SAAA;QACA;MACA;IACA;IACAG,qBAAA;MACAL,IAAA,EAAAM,MAAA;MACAJ,OAAA;IACA;EACA;EACAK,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,UAAA;MACAC,UAAA;MAEAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,SAAA;QACAC,cAAA;QACAC,iBAAA;QACAC,IAAA,EAAAC,SAAA;QACAC,SAAA;QACAC,WAAA;QACAC,OAAA;QACAC,aAAA;QACAC,aAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,UAAA;QACAC,qBAAA;QACAC,UAAA;QACAC,eAAA;QACAC,UAAA;QACAC,eAAA;QACAC,UAAA;QAEAC,WAAA;QAEAC,gBAAA;QACAC,MAAA;QACAC,mBAAA;MACA;MACAC,gBAAA;MACAC,gBAAA;MACAC,WAAA;MACAC,WAAA;MACAC,eAAA;MACAC,gBAAA;MACAC,uBAAA;MACAC,KAAA;QACAhC,IAAA,GACA;UAAAiC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAlC,IAAA,GACA;UAAAgC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAjC,SAAA;UAAA+B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACA9B,IAAA;UAAA4B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAzB,aAAA,GACA;UAAAuB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAE,OAAA;MACAC,OAAA;MACAC,QAAA;IACA;EACA;EACAC,QAAA,EAAAC,aAAA,KACAxD,UAAA,8BACA;EACAyD,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAAC,iBAAA,EAAAV,OAAA,EAAAC,QAAA,EAAAU,IAAA;MAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACA3E,UAAA;UAAA;YAAAqE,iBAAA,GAAAI,QAAA,CAAAG,IAAA;YAAAjB,OAAA,GAAAU,iBAAA,CAAAV,OAAA;YAAAC,QAAA,GAAAS,iBAAA,CAAAT,QAAA;YAAAU,IAAA,GAAAD,iBAAA,CAAAC,IAAA;YACAN,KAAA,CAAAL,OAAA,GAAAA,OAAA;YACAK,KAAA,CAAAJ,QAAA,GAAAA,QAAA;YACAI,KAAA,CAAAN,OAAA,GAAAY,IAAA;UAAA;UAAA;YAAA,OAAAG,QAAA,CAAAI,IAAA;QAAA;MAAA,GAAAT,OAAA;IAAA;EACA;EACAU,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA;IACA,KAAAC,oBAAA;IACA;IACA,KAAAC,mBAAA;IACA,KAAAxE,IAAA,oBAAAyE,QAAA;EACA;EACAC,OAAA;IACAD,QAAA,WAAAA,SAAA;MACA,IAAAE,aAAA,QAAAxE,OAAA;QAAAkB,OAAA,GAAAsD,aAAA,CAAAtD,OAAA;QAAAuD,MAAA,GAAAC,wBAAA,CAAAF,aAAA,EAAAG,SAAA;MACA,KAAAnE,IAAA,GAAAP,MAAA,CAAA2E,MAAA,KAAAH,MAAA;QAAAvD,OAAA,IAAAA;MAAA;MACA,KAAAV,IAAA,CAAAG,SAAA,GAAAb,MAAA,MAAAU,IAAA,CAAAG,SAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAkE,OAAA,CAAAC,GAAA,mBAAAtE,IAAA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,KAAA0B,gBAAA,QAAA1B,IAAA,CAAAoB,eAAA,GACA,KAAApB,IAAA,CAAAoB,eAAA,CAAAmD,KAAA,QACA;MACA,KAAA5C,gBAAA,QAAA3B,IAAA,CAAAkB,eAAA,GACA,KAAAlB,IAAA,CAAAkB,eAAA,CAAAqD,KAAA,QACA;IACA;IACA;IACAC,cAAA,WAAAA,eAAAC,GAAA;IACA;IACAC,eAAA,WAAAA,gBAAAD,GAAA;IACA;IACAE,6BAAA,WAAAA,8BAAA;MAAA,IAAAC,MAAA;MAAA,OAAA/B,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA8B,SAAA;QAAA,OAAA/B,mBAAA,GAAAK,IAAA,UAAA2B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzB,IAAA,GAAAyB,SAAA,CAAAxB,IAAA;YAAA;cAAAwB,SAAA,CAAAxB,IAAA;cAAA,OACApE,6BAAA;gBAAA6F,cAAA;cAAA,GAAAC,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAP,MAAA,CAAAQ,cAAA,GAAAF,GAAA,CAAAG,IAAA;gBACA;kBACAT,MAAA,CAAAU,QAAA;oBACAnD,OAAA,EAAA+C,GAAA,CAAAK,OAAA;oBACAlG,IAAA;kBACA;gBACA;cACA;YAAA;cACAgF,OAAA,CAAAC,GAAA,2BAAAM,MAAA,CAAA7C,gBAAA;YAAA;YAAA;cAAA,OAAAgD,SAAA,CAAAtB,IAAA;UAAA;QAAA,GAAAoB,QAAA;MAAA;IACA;IACAlB,WAAA,WAAAA,YAAA;MAAA,IAAA6B,MAAA;MACA3G,WAAA,KAAAoG,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAK,MAAA,CAAA3D,WAAA,GAAAqD,GAAA,CAAAG,IAAA;QACA;UACAG,MAAA,CAAAF,QAAA;YACAnD,OAAA,EAAA+C,GAAA,CAAAK,OAAA;YACAlG,IAAA;UACA;QACA;MACA;IACA;IACAuE,oBAAA,WAAAA,qBAAA;MAAA,IAAA6B,MAAA;MACA1G,oBAAA,KAAAkG,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAM,MAAA,CAAA3D,eAAA,GAAAoD,GAAA,CAAAG,IAAA;QACA;UACAI,MAAA,CAAAH,QAAA;YACAnD,OAAA,EAAA+C,GAAA,CAAAK,OAAA;YACAlG,IAAA;UACA;QACA;MACA;IACA;IACAqG,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MAAA,OAAA9C,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6C,SAAA;QAAA,OAAA9C,mBAAA,GAAAK,IAAA,UAAA0C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxC,IAAA,GAAAwC,SAAA,CAAAvC,IAAA;YAAA;cAAAuC,SAAA,CAAAvC,IAAA;cAAA,OACAvE,iBAAA,KAAAiG,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAQ,MAAA,CAAA5D,gBAAA,GAAAmD,GAAA,CAAAG,IAAA;gBACA;kBACAM,MAAA,CAAAL,QAAA;oBACAnD,OAAA,EAAA+C,GAAA,CAAAK,OAAA;oBACAlG,IAAA;kBACA;gBACA;cACA;YAAA;cACAgF,OAAA,CAAAC,GAAA,2BAAAqB,MAAA,CAAA5D,gBAAA;YAAA;YAAA;cAAA,OAAA+D,SAAA,CAAArC,IAAA;UAAA;QAAA,GAAAmC,QAAA;MAAA;IACA;IACA/B,mBAAA,WAAAA,oBAAA;MAAA,IAAAkC,MAAA;MACA9G,eAAA,KAAAgG,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAY,MAAA,CAAA/D,uBAAA,GAAAkD,GAAA,CAAAG,IAAA;QACA;UACAU,MAAA,CAAAT,QAAA;YACAnD,OAAA,EAAA+C,GAAA,CAAAK,OAAA;YACAlG,IAAA;UACA;QACA;MACA;IACA;IACA;IACA2G,qBAAA,WAAAA,sBAAAvB,GAAA;MACA;MACA;MACA;MACA;MACA,KAAA/C,gBAAA;MACA,KAAAC,gBAAA;MACA,KAAA3B,IAAA,CAAAoB,eAAA;MACA,KAAApB,IAAA,CAAAkB,eAAA;IACA;IACA;IACA+E,WAAA,WAAAA,YAAAxB,GAAA;MACA,IAAAA,GAAA;QACA,KAAAzE,IAAA,CAAAkG,WAAA;QACA,KAAAlG,IAAA,CAAAe,UAAA;QACA,KAAAf,IAAA,CAAAmB,UAAA;QACA,KAAAQ,gBAAA;QACA,KAAAD,gBAAA;QACA,KAAA1B,IAAA,CAAAoB,eAAA;QACA,KAAApB,IAAA,CAAAkB,eAAA;QACA,KAAAlB,IAAA,CAAAsB,WAAA;MACA;QACA;QACA;QACA;QACA;QACA,KAAAtB,IAAA,CAAAsB,WAAA;MACA;IACA;IACA;IACA6E,UAAA,WAAAA,WAAA1B,GAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA,CACA;IACA2B,UAAA,WAAAA,WAAA;MACA,KAAApG,IAAA,CAAAqG,UAAA;IACA;IACAC,QAAA,WAAAA,SAAA7B,GAAA;MACAJ,OAAA,CAAAC,GAAA,CAAAG,GAAA;MACA,KAAAzE,IAAA,CAAAkB,eAAA;MACA,SAAAqF,CAAA,MAAAA,CAAA,GAAA9B,GAAA,CAAA+B,MAAA,EAAAD,CAAA;QACA,IAAAA,CAAA,KAAA9B,GAAA,CAAA+B,MAAA;UACA,KAAAxG,IAAA,CAAAkB,eAAA,IAAAuD,GAAA,CAAA8B,CAAA;QACA;UACA,KAAAvG,IAAA,CAAAkB,eAAA,IAAAuD,GAAA,CAAA8B,CAAA;QACA;MACA;MACAlC,OAAA,CAAAC,GAAA,MAAAtE,IAAA,CAAAkB,eAAA;IACA;IACAuF,QAAA,WAAAA,SAAAhC,GAAA;MACAJ,OAAA,CAAAC,GAAA,CAAAG,GAAA;MACA,KAAAzE,IAAA,CAAAoB,eAAA;MACA,SAAAmF,CAAA,MAAAA,CAAA,GAAA9B,GAAA,CAAA+B,MAAA,EAAAD,CAAA;QACA,IAAAA,CAAA,KAAA9B,GAAA,CAAA+B,MAAA;UACA,KAAAxG,IAAA,CAAAoB,eAAA,IAAAqD,GAAA,CAAA8B,CAAA;QACA;UACA,KAAAvG,IAAA,CAAAoB,eAAA,IAAAqD,GAAA,CAAA8B,CAAA;QACA;MACA;IACA;IACAG,cAAA,WAAAA,eAAAjC,GAAA,EAAApF,IAAA;MACA,IAAAA,IAAA;QACA,KAAAoF,GAAA;UACA,KAAA9C,gBAAA;QACA;MACA;MACA,IAAAtC,IAAA;QACA,KAAAoF,GAAA;UACA,KAAA/C,gBAAA;QACA;MACA;IACA;IACAiF,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA;MACAvC,OAAA,CAAAC,GAAA,MAAAtE,IAAA;MACA,KAAA6G,KAAA,CAAA7G,IAAA,CAAA8G,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;QACAH,MAAA,CAAA9G,UAAA;QACA,IAAAkH,MAAA,GAAAJ,MAAA,CAAA9E,eAAA,CAAAmF,IAAA,CACA,UAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,EAAA,KAAAP,MAAA,CAAA5G,IAAA,CAAAK,iBAAA;QAAA,CACA;QACA,IAAA2G,MAAA;UACAJ,MAAA,CAAA5G,IAAA,CAAAoH,mBAAA,GAAAJ,MAAA,CAAAK,YAAA;QACA;QACA,IAAAT,MAAA,CAAA5G,IAAA,CAAAW,aAAA;UACA,IAAAiG,MAAA,CAAA5G,IAAA,CAAAmB,UAAA,cAAAyF,MAAA,CAAA5G,IAAA,CAAAe,UAAA;YACA6F,MAAA,CAAAtB,QAAA,CAAAgC,KAAA;YACAV,MAAA,CAAA9G,UAAA;YACA;UACA;QACA;UACA8G,MAAA,CAAA5G,IAAA,CAAAsB,WAAA;UACAsF,MAAA,CAAA5G,IAAA,CAAAuH,gBAAA;QACA;QACA,IAAAC,EAAA,GAAAZ,MAAA,CAAA5G,IAAA,CAAAmB,UAAA,GAAAyF,MAAA,CAAA5G,IAAA,CAAAoB,eAAA;QACA,IAAAqG,EAAA,GAAAb,MAAA,CAAA5G,IAAA,CAAAe,UAAA,GAAA6F,MAAA,CAAA5G,IAAA,CAAAkB,eAAA;QACA,IAAA0F,MAAA,CAAA5G,IAAA,CAAAmB,UAAA,KAAAqG,EAAA,aAAAA,EAAA,cAAAA,EAAA;UACAZ,MAAA,CAAAtB,QAAA,CAAAgC,KAAA;UACAV,MAAA,CAAA9G,UAAA;UACA;QACA;QACA,IAAA8G,MAAA,CAAA5G,IAAA,CAAAe,UAAA,KAAA0G,EAAA,aAAAA,EAAA,cAAAA,EAAA;UACAb,MAAA,CAAAtB,QAAA,CAAAgC,KAAA;UACAV,MAAA,CAAA9G,UAAA;UACA;QACA;QAEAhB,iBAAA,CAAA4D,aAAA,CAAAA,aAAA,KACAkE,MAAA,CAAA5G,IAAA;UACAoB,eAAA,EAAAoG,EAAA;UACAtG,eAAA,EAAAuG;QAAA,EACA,EAAAxC,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAyB,MAAA,CAAAtB,QAAA;cACAnD,OAAA;cACA9C,IAAA;YACA;YACAuH,MAAA,CAAAc,KAAA;YACAd,MAAA,CAAAc,KAAA;UACA;YACAd,MAAA,CAAAtB,QAAA;cACAnD,OAAA,EAAA+C,GAAA,CAAAK,OAAA;cACAlG,IAAA;YACA;UACA;UACAuH,MAAA,CAAA9G,UAAA;QACA;MACA;IACA;IAEA6H,UAAA,WAAAA,WAAAC,CAAA;MACA,OAAAA,CAAA,CAAAC,OAAA;IACA;IAEAC,qBAAA,WAAAA,sBAAAC,KAAA;MACA;MACA,IAAAA,KAAA,WAAAA,KAAA,aAAAA,KAAA,KAAAxH,SAAA;QACA,KAAAP,IAAA,CAAAyB,mBAAA;QACA;MACA;;MAEA;MACA,IAAAuG,UAAA,GAAA1I,MAAA,CAAAyI,KAAA;;MAEA;MACAC,UAAA,GAAAA,UAAA,CAAAH,OAAA;;MAEA;MACA,IAAAI,QAAA,IAAAD,UAAA,CAAAE,KAAA,eAAA1B,MAAA;MACA,IAAAyB,QAAA;QACA;QACA,IAAAE,aAAA,GAAAH,UAAA,CAAAI,OAAA;QACAJ,UAAA,GAAAA,UAAA,CAAAK,SAAA,IAAAF,aAAA,QAAAH,UAAA,CAAAK,SAAA,CAAAF,aAAA,MAAAN,OAAA;MACA;;MAEA;MACA,IAAAG,UAAA;QACA,KAAAhI,IAAA,CAAAyB,mBAAA;QACA;MACA;;MAEA;MACA,IAAAuG,UAAA;QACA,KAAAhI,IAAA,CAAAyB,mBAAA;QACA;MACA;;MAEA;MACA,IAAA6G,QAAA,GAAAC,UAAA,CAAAP,UAAA;;MAEA;MACA,IAAAQ,KAAA,CAAAF,QAAA;QACA,KAAAtI,IAAA,CAAAyB,mBAAA;QACA;MACA;;MAEA;MACA,IAAA6G,QAAA;QACA,KAAAtI,IAAA,CAAAyB,mBAAA;MACA,WAAA6G,QAAA;QACA,KAAAtI,IAAA,CAAAyB,mBAAA;MACA;QACA;QACA,KAAAzB,IAAA,CAAAyB,mBAAA,GAAAuG,UAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
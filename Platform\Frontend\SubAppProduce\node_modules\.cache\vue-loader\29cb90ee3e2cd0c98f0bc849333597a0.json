{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\partRecognitionConfig.vue?vue&type=template&id=9fc6fada&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\partRecognitionConfig.vue", "mtime": 1757468113429}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImZvcm0td3JhcHBlciI+CiAgPGRpdiBjbGFzcz0iZm9ybS14Ij4KICAgIDxlbC1mb3JtIHJlZj0iZm9ybSIgOm1vZGVsPSJmb3JtIiBsYWJlbC13aWR0aD0iMTIwcHgiPgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmmK/lkKblkK/nlKgiIHByb3A9ImVuYWJsZSI+CiAgICAgICAgPGVsLXJhZGlvLWdyb3VwIHYtbW9kZWw9ImZvcm0uZW5hYmxlIj4KICAgICAgICAgIDxlbC1yYWRpbyA6bGFiZWw9ImZhbHNlIj7lkKY8L2VsLXJhZGlvPgogICAgICAgICAgPGVsLXJhZGlvIDpsYWJlbD0idHJ1ZSI+5pivPC9lbC1yYWRpbz4KICAgICAgICA8L2VsLXJhZGlvLWdyb3VwPgogICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgPHRlbXBsYXRlIHYtaWY9ImZvcm0uZW5hYmxlIj4KICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLor4bliKvnsbvlnosiIHByb3A9ImlkZW50aWZ5QXR0ciI+CiAgICAgICAgICA8ZWwtcmFkaW8tZ3JvdXAgdi1tb2RlbD0iZm9ybS5pZGVudGlmeUF0dHIiPgogICAgICAgICAgICA8ZWwtcmFkaW8gOmxhYmVsPSIxIj57eyBjdXJyZW50Qm9tTmFtZSB9feWQjeensOWJjee8gDwvZWwtcmFkaW8+CiAgICAgICAgICAgIDxlbC1yYWRpbyA6bGFiZWw9IjIiPnt7IGN1cnJlbnRCb21OYW1lIH196KeE5qC85YmN57yAPC9lbC1yYWRpbz4KICAgICAgICAgIDwvZWwtcmFkaW8tZ3JvdXA+CiAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgPGVsLWZvcm0taXRlbSB2LWZvcj0iKGVsZW1lbnQsaW5kZXgpIGluIGxpc3QiIDprZXk9ImluZGV4IiA6c2hvdy1tZXNzYWdlPSJmYWxzZSIgOmxhYmVsPSJlbGVtZW50LlBhcnRfVHlwZV9OYW1lIiBwcm9wPSJtYWluUGFydCI+CiAgICAgICAgICA8ZWwtaW5wdXQgdi1tb2RlbC50cmltPSJmb3JtWydpdGVtJytpbmRleF0iIDpwbGFjZWhvbGRlcj0iYOivt+i+k+WFpe+8iOWkmuS4quS9v+eUqCcke3NwbGl0U3ltYm9sfSfpmpTlvIDvvInvvIzljZXkuKrphY3nva7kuI3otoXov4cxMOS4quWtl+espmAiIGNsZWFyYWJsZSBAYmx1cj0ibWFpbkJsdXIiIC8+CiAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgIDwvdGVtcGxhdGU+CiAgICA8L2VsLWZvcm0+CiAgPC9kaXY+CiAgPGRpdiBjbGFzcz0iYnRuLXgiPgogICAgPGVsLWJ1dHRvbiBAY2xpY2s9IiRlbWl0KCdjbG9zZScpIj7lj5Yg5raIPC9lbC1idXR0b24+CiAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIDpsb2FkaW5nPSJidG5Mb2FkaW5nIiBAY2xpY2s9ImhhbmRsZVN1Ym1pdCI+56GuIOWumjwvZWwtYnV0dG9uPgogIDwvZGl2Pgo8L2Rpdj4K"}, null]}
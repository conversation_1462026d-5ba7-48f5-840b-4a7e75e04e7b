{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\OwnerProcess.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\OwnerProcess.vue", "mtime": 1758286724448}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["OwnerProcess.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAyBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "OwnerProcess.vue", "sourceRoot": "src/views/PRO/plan-production/schedule-production-new-part/components", "sourcesContent": ["<template>\r\n  <div v-loading=\"loading\">\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"140px\">\r\n      <el-form-item v-for=\"(element) in itemOption\" :key=\"element.tType\" :label=\"element.label\" prop=\"ownerProcess\">\r\n        <el-select v-model=\"element.value\" clearable class=\"w100\" placeholder=\"请选择\">\r\n          <template>\r\n            <el-option\r\n              v-for=\"(item) in OwnerOption[element.tType]\"\r\n              :key=\"item.tType\"\r\n              :label=\"item.Name\"\r\n              :value=\"item.Code\"\r\n            />\r\n          </template>\r\n        </el-select>\r\n      </el-form-item>\r\n    </el-form>\r\n    <div class=\"dialog-footer\">\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"submit\">确 定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { GetProcessListBase } from '@/api/PRO/technology-lib'\r\nimport { getBomName } from '@/views/PRO/bom-setting/utils'\r\n\r\nexport default {\r\n  props: {\r\n    pageType: {\r\n      type: String,\r\n      default: undefined\r\n    },\r\n    partTypeOption: {\r\n      type: Array,\r\n      default: () => ([])\r\n    },\r\n    isPartPrepare: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    hasUnitPart: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    partName: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      itemOption: [{\r\n        key: '',\r\n        value: ''\r\n      }],\r\n      form: {\r\n      },\r\n      loading: false,\r\n      btnLoading: false,\r\n      OwnerOption: {},\r\n      rules: {\r\n        // ownerProcess: [\r\n        //   { required: true, message: '请选择车间', trigger: 'change' }\r\n        // ]\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    submit() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (valid) {\r\n          const result = []\r\n          this.list.forEach(item => {\r\n            const levelInfo = []\r\n            item.curLevel.forEach(level => {\r\n              const tType = item.tType + '_' + level\r\n              const itemInfo = this.itemOption.find(v => v.tType === tType)\r\n              console.log('submit item', item)\r\n              console.log('submit this.itemOption', this.itemOption)\r\n              console.log('submit itemInfo', itemInfo)\r\n              if (itemInfo) {\r\n                if (item.Scheduled_Used_Process && item.Scheduled_Used_Process !== itemInfo.value) {\r\n                  this.$message({\r\n                    message: `请和该区域批次下已排产同${this.partName}领用工序保持一致`,\r\n                    type: 'warning'\r\n                  })\r\n                } else {\r\n                  result.push(itemInfo.value)\r\n                  const obj = {\r\n                    Use_Process_Id: '',\r\n                    Bom_Level: '',\r\n                    Use_Process_Code: ''\r\n                  }\r\n                  const curProcess = this.option.find(v => v.Code === itemInfo.value)\r\n                  obj.Use_Process_Id = curProcess.Id\r\n                  obj.Bom_Level = level\r\n                  obj.Use_Process_Code = itemInfo.value\r\n                  levelInfo.push(obj)\r\n                }\r\n              }\r\n            })\r\n            item.Use_Process_List = levelInfo\r\n            console.log('submit item.levelInfo', item.Use_Process_List)\r\n            item.Part_Used_Process = result.filter(v => !!v).toString()\r\n          })\r\n          this.btnLoading = false\r\n          this.handleClose()\r\n        } else {\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    async setOption(isInline, arr) {\r\n      // console.log(7, this.isPartPrepare)\r\n      this.list = arr || []\r\n      this.levelList = []\r\n      this.list = this.list.map(item => {\r\n        item.tType = item.Type + '&' + item.Belong_To_Component\r\n        if (item.Parent_Level) {\r\n          item.curLevel = [...new Set(item.Parent_Level.split(','))]\r\n        }\r\n        this.levelList = this.levelList.concat(item.curLevel)\r\n        return item\r\n      })\r\n      this.levelList = [...new Set(this.levelList)]\r\n      const levelMap = {}\r\n      for (const lvl of this.levelList) {\r\n        levelMap[lvl] = await getBomName(lvl)\r\n      }\r\n      console.log('this.levelMap', levelMap)\r\n      console.log('this.levelList', this.levelList)\r\n      console.log('this.list', this.list)\r\n      console.log('this.hasUnitPart', this.hasUnitPart)\r\n      this.isInline = isInline\r\n      const suffix = '领用工序'\r\n      const obj = {}\r\n      this.itemOption = this.list.reduce((acc, cur) => {\r\n        cur.curLevel.forEach(levelCode => {\r\n          const partOwnerName = this.hasUnitPart ? (levelMap[levelCode]) : ''\r\n          console.log('partOwnerName', partOwnerName)\r\n          const uniqueKey = cur.tType + '_' + levelCode\r\n          if (!obj[uniqueKey] && cur.Type !== 'Direct') {\r\n            acc.push({\r\n              code: cur.Type,\r\n              label: (cur.Type_Name || '') + partOwnerName + suffix,\r\n              value: '',\r\n              tType: uniqueKey\r\n            })\r\n            this.$set(this.OwnerOption, uniqueKey, [])\r\n          }\r\n          obj[uniqueKey] = true\r\n        })\r\n\r\n        return acc\r\n      }, [])\r\n      console.log('obj', obj)\r\n      console.log('this.itemOption', this.itemOption)\r\n      await this.fetchData()\r\n      if (isInline && arr.length) {\r\n        arr.forEach(item => {\r\n          item.curLevel.forEach(level => {\r\n            const tType = item.tType + '_' + level\r\n            const itemInfo = this.itemOption.find(v => v.tType === tType)\r\n            if (itemInfo) {\r\n              const pUsedProcess = item.Part_Used_Process.split(',')\r\n              console.log('pUsedProcess', pUsedProcess)\r\n              console.log('this.OwnerOption[tType]', this.OwnerOption[tType])\r\n              const cur = this.OwnerOption[tType].find(v => pUsedProcess.includes(v.Code))\r\n              console.log('cur', cur)\r\n              itemInfo.value = cur?.Code || ''\r\n            }\r\n          })\r\n        })\r\n        // const cur = arr[0]\r\n        // const itemInfo = this.itemOption.find(v => v.tType === cur.tType)\r\n        // if (itemInfo) {\r\n        //   itemInfo.value = cur.Part_Used_Process\r\n        // }\r\n      }\r\n    },\r\n    getComOption() {\r\n      const _listMap = {}\r\n      const keyArr = []\r\n      const getProcessItem = (code) => this.option.find(v => v.Code === code)\r\n      const _option = this.option.filter(v => v.Is_Enable)\r\n      console.log('thi-s.list', this.list)\r\n      this.list.forEach((element) => {\r\n        element.curLevel.forEach(level => {\r\n          const key = element.tType + '_' + level\r\n          keyArr.push(key)\r\n          if (!_listMap[key]) {\r\n            _listMap[key] = []\r\n          }\r\n          let parentPath = 'Component_Technology_Path'\r\n          if (level === '-1') {\r\n            parentPath = 'Component_Technology_Path'\r\n          } else if (level === '1') {\r\n            parentPath = 'SubAssembly_Technology_Path'\r\n          } else if (level === '2') {\r\n            parentPath = 'SubAssembly_Technology_Path'\r\n          } else if (level === '3') {\r\n            parentPath = 'SubAssembly_Technology_Path'\r\n          }\r\n          // if (this.hasUnitPart) {\r\n          //   if (element.Belong_To_Component) {\r\n          //     parentPath = 'Component_Technology_Path'\r\n          //   } else {\r\n          //     parentPath = 'SubAssembly_Technology_Path'\r\n          //   }\r\n          // }\r\n          element[parentPath] = element[parentPath] || ''\r\n\r\n          if (element.Scheduled_Used_Process) {\r\n            const item = getProcessItem(element.Scheduled_Used_Process)\r\n            if (item) {\r\n              _listMap[key].push(item)\r\n            }\r\n          } else {\r\n            const componentProcess = element[parentPath].split('/').filter(v => !!v)\r\n            // const processItem = this.option.find(v => v.Code === element.Temp_Part_Used_Process)\r\n            console.log(666)\r\n\r\n            if (componentProcess.length) {\r\n            // if (element.Temp_Part_Used_Process && componentProcess.includes(element.Temp_Part_Used_Process)) {\r\n            //   _listMap[key].push(processItem)\r\n            // } else {\r\n            //   componentProcess.forEach(c => {\r\n            //     const cur = getProcessItem(c)\r\n            //     if (cur) {\r\n            //       _listMap[key].push(cur)\r\n            //     }\r\n            //   })\r\n            // }\r\n              componentProcess.forEach(c => {\r\n                const cur = getProcessItem(c)\r\n                console.log('cu2r', cur, _listMap[key])\r\n                if (cur) {\r\n                  if (_listMap[key].findIndex(v => v.Code === cur.Code) !== -1) {\r\n                    return\r\n                  }\r\n                  _listMap[key].push(cur)\r\n                }\r\n              })\r\n            } else {\r\n              console.log(777)\r\n              const partUsedProcess = element.Part_Used_Process\r\n              const _fp = this.option.filter(item => {\r\n                let flag = false\r\n                if (partUsedProcess && partUsedProcess === item.Code) {\r\n                  flag = true\r\n                }\r\n                if (element.Part_Type_Used_Process && element.Part_Type_Used_Process === item.Code) {\r\n                  flag = true\r\n                }\r\n                if (!flag) {\r\n                  flag = !!item.Is_Enable\r\n                }\r\n                console.log('item.Bom_Level', item.Bom_Level, level)\r\n                if (item.Bom_Level.toString() !== level.toString()) {\r\n                  flag = false\r\n                }\r\n                return flag\r\n              })\r\n              for (let i = 0; i < _fp.length; i++) {\r\n                const cur = _fp[i]\r\n                if (_listMap[key].findIndex(v => v.Code === cur.Code) !== -1) {\r\n                  continue\r\n                }\r\n                _listMap[key].push(cur)\r\n              }\r\n            }\r\n          }\r\n        })\r\n      })\r\n      console.log('this.OwnerOption', JSON.parse(JSON.stringify(_listMap)))\r\n      if (this.isInline) {\r\n        this.OwnerOption = _listMap\r\n      } else {\r\n        keyArr.forEach((keys, idx) => {\r\n          const belongType = keys.split('&')[1] === 'true' ? 1 : 3\r\n          this.OwnerOption[keys] = _option.filter(v => v.Type === belongType)\r\n        })\r\n      }\r\n    },\r\n    async fetchData() {\r\n      this.loading = true\r\n\r\n      let hasTrueCompPart\r\n      let hasFalseCompPart\r\n      if (this.hasUnitPart) {\r\n        hasTrueCompPart = this.list.some(v => !!v.Belong_To_Component)\r\n        hasFalseCompPart = this.list.some(v => !v.Belong_To_Component)\r\n      } else {\r\n        hasTrueCompPart = true\r\n        hasFalseCompPart = false\r\n      }\r\n      const fetchDataForType = async(bomLevel) => {\r\n        try {\r\n          const type = bomLevel === -1 ? 1 : 3\r\n          const res = await GetProcessListBase({ type, Bom_Level: bomLevel })\r\n          if (res.IsSucceed) {\r\n            return res.Data\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            return []\r\n          }\r\n        } catch (error) {\r\n          this.$message({\r\n            message: `请求失败`,\r\n            type: 'error'\r\n          })\r\n          return []\r\n        }\r\n      }\r\n\r\n      try {\r\n        const levels = [...new Set(this.list.reduce((acc, cur) => acc.concat(cur.curLevel), []))]\r\n        console.log('levels', levels)\r\n        const tasks = []\r\n        levels.forEach(level => {\r\n          tasks.push(fetchDataForType(level))\r\n        })\r\n        let results = []\r\n        results = await Promise.all(tasks)\r\n        console.log('results', results)\r\n        // let results = []\r\n\r\n        // if (hasTrueCompPart && hasFalseCompPart) {\r\n        //   results = await Promise.all([fetchDataForType(1), fetchDataForType(3)])\r\n        // } else if (hasTrueCompPart) {\r\n        //   results = [await fetchDataForType(1)]\r\n        // } else if (hasFalseCompPart) {\r\n        //   results = [await fetchDataForType(3)]\r\n        // }\r\n\r\n        this.option = results.filter(data => !!data.length).flat()\r\n        console.log('this.option', this.option)\r\n\r\n        this.getComOption()\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    handleClose() {\r\n      this.$emit('close')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.dialog-footer{\r\n  text-align: right;\r\n  margin-top: 30px;\r\n}\r\n</style>\r\n"]}]}
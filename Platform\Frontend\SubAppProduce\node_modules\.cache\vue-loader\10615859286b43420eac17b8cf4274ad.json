{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\OwnerProcess.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\OwnerProcess.vue", "mtime": 1758078511171}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["OwnerProcess.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAyBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "OwnerProcess.vue", "sourceRoot": "src/views/PRO/plan-production/schedule-production-new-part/components", "sourcesContent": ["<template>\r\n  <div v-loading=\"loading\">\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"140px\">\r\n      <el-form-item v-for=\"(element) in itemOption\" :key=\"element.tType\" :label=\"element.label\" prop=\"ownerProcess\">\r\n        <el-select v-model=\"element.value\" clearable class=\"w100\" placeholder=\"请选择\">\r\n          <template>\r\n            <el-option\r\n              v-for=\"(item) in OwnerOption[element.tType]\"\r\n              :key=\"item.tType\"\r\n              :label=\"item.Name\"\r\n              :value=\"item.Code\"\r\n            />\r\n          </template>\r\n        </el-select>\r\n      </el-form-item>\r\n    </el-form>\r\n    <div class=\"dialog-footer\">\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"submit\">确 定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { GetProcessListBase } from '@/api/PRO/technology-lib'\r\nimport { getBomName } from '@/views/PRO/bom-setting/utils'\r\n\r\nexport default {\r\n  props: {\r\n    pageType: {\r\n      type: String,\r\n      default: undefined\r\n    },\r\n    partTypeOption: {\r\n      type: Array,\r\n      default: () => ([])\r\n    },\r\n    isPartPrepare: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    hasUnitPart: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    partName: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      itemOption: [{\r\n        key: '',\r\n        value: ''\r\n      }],\r\n      form: {\r\n      },\r\n      loading: false,\r\n      btnLoading: false,\r\n      OwnerOption: {},\r\n      rules: {\r\n        // ownerProcess: [\r\n        //   { required: true, message: '请选择车间', trigger: 'change' }\r\n        // ]\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    submit() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (valid) {\r\n          this.list.forEach(item => {\r\n            const itemInfo = this.itemOption.find(v => v.tType === item.tType)\r\n            if (itemInfo) {\r\n              if (item.Scheduled_Used_Process && item.Scheduled_Used_Process !== itemInfo.value) {\r\n                this.$message({\r\n                  message: `请和该区域批次下已排产同${this.partName}领用工序保持一致`,\r\n                  type: 'warning'\r\n                })\r\n              } else {\r\n                item.Part_Used_Process = itemInfo.value\r\n              }\r\n            }\r\n          })\r\n          this.btnLoading = false\r\n          this.handleClose()\r\n        } else {\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    async setOption(isInline, arr) {\r\n      // console.log(7, this.isPartPrepare)\r\n      this.list = arr || []\r\n      this.levelList = []\r\n      this.list = this.list.map(item => {\r\n        item.tType = item.Type + '&' + item.Belong_To_Component\r\n        let curLevel = ''\r\n        if (item.Parent_Level) {\r\n          curLevel = item.Parent_Level.split(',')\r\n        }\r\n        this.levelList = this.levelList.concat(curLevel)\r\n        return item\r\n      })\r\n      this.levelList = [...new Set(this.levelList)]\r\n      const levelMap = {}\r\n      for (const lvl of this.levelList) {\r\n        levelMap[lvl] = await getBomName(3)\r\n      }\r\n      console.log('this.levelMap', levelMap)\r\n      console.log('this.levelList', this.levelList)\r\n      console.log('this.list', this.list)\r\n      console.log('this.hasUnitPart', this.hasUnitPart)\r\n      this.isInline = isInline\r\n      const suffix = '领用工序'\r\n      const obj = {}\r\n      this.itemOption = this.list.reduce((acc, cur) => {\r\n        let curLevel = ''\r\n        if (cur.Parent_Level) {\r\n          curLevel = cur.Parent_Level.split(',')\r\n        }\r\n        const curLevelCode = [...new Set(curLevel)]\r\n        console.log('curLevelCode', curLevelCode)\r\n\r\n        curLevelCode.forEach(levelCode => {\r\n          const partOwnerName = this.hasUnitPart ? (levelMap[levelCode]) : ''\r\n          console.log('partOwnerName', partOwnerName)\r\n          if (!obj[cur.tType] && cur.Type !== 'Direct') {\r\n            acc.push({\r\n              code: cur.Type,\r\n              label: (cur.Type_Name || '') + partOwnerName + suffix,\r\n              value: '',\r\n              tType: cur.tType\r\n            })\r\n            this.$set(this.OwnerOption, cur.tType, [])\r\n          }\r\n          obj[cur.tType] = true\r\n        })\r\n\r\n        return acc\r\n      }, [])\r\n      console.log('obj', obj)\r\n      console.log('this.itemOption', this.itemOption)\r\n      this.fetchData()\r\n      if (isInline && arr.length) {\r\n        const cur = arr[0]\r\n        const itemInfo = this.itemOption.find(v => v.tType === cur.tType)\r\n        if (itemInfo) {\r\n          itemInfo.value = cur.Part_Used_Process\r\n        }\r\n      }\r\n    },\r\n    getComOption() {\r\n      const _listMap = {}\r\n      const keyArr = []\r\n      const getProcessItem = (code) => this.option.find(v => v.Code === code)\r\n      const _option = this.option.filter(v => v.Is_Enable)\r\n      this.list.forEach((element) => {\r\n        const key = element.tType\r\n        keyArr.push(key)\r\n        if (!_listMap[key]) {\r\n          _listMap[key] = []\r\n        }\r\n        let parentPath = 'Component_Technology_Path'\r\n        if (this.hasUnitPart) {\r\n          if (element.Belong_To_Component) {\r\n            parentPath = 'Component_Technology_Path'\r\n          } else {\r\n            parentPath = 'SubAssembly_Technology_Path'\r\n          }\r\n        }\r\n        element[parentPath] = element[parentPath] || ''\r\n\r\n        if (element.Scheduled_Used_Process) {\r\n          const item = getProcessItem(element.Scheduled_Used_Process)\r\n          if (item) {\r\n            _listMap[key].push(item)\r\n          }\r\n        } else {\r\n          const componentProcess = element[parentPath].split('/').filter(v => !!v)\r\n          // const processItem = this.option.find(v => v.Code === element.Temp_Part_Used_Process)\r\n\r\n          if (componentProcess.length) {\r\n            // if (element.Temp_Part_Used_Process && componentProcess.includes(element.Temp_Part_Used_Process)) {\r\n            //   _listMap[key].push(processItem)\r\n            // } else {\r\n            //   componentProcess.forEach(c => {\r\n            //     const cur = getProcessItem(c)\r\n            //     if (cur) {\r\n            //       _listMap[key].push(cur)\r\n            //     }\r\n            //   })\r\n            // }\r\n            componentProcess.forEach(c => {\r\n              const cur = getProcessItem(c)\r\n              if (cur) {\r\n                if (_listMap[key].findIndex(v => v.Code === cur.Code) !== -1) {\r\n                  return\r\n                }\r\n                _listMap[key].push(cur)\r\n              }\r\n            })\r\n          } else {\r\n            const partUsedProcess = element.Part_Used_Process\r\n            const _fp = this.option.filter(item => {\r\n              let flag = false\r\n              if (partUsedProcess && partUsedProcess === item.Code) {\r\n                flag = true\r\n              }\r\n              if (element.Part_Type_Used_Process && element.Part_Type_Used_Process === item.Code) {\r\n                flag = true\r\n              }\r\n              if (!flag) {\r\n                flag = !!item.Is_Enable\r\n              }\r\n              return flag\r\n            })\r\n            for (let i = 0; i < _fp.length; i++) {\r\n              const cur = _fp[i]\r\n              if (_listMap[key].findIndex(v => v.Code === cur.Code) !== -1) {\r\n                continue\r\n              }\r\n              _listMap[key].push(cur)\r\n            }\r\n          }\r\n        }\r\n      })\r\n      if (this.isInline) {\r\n        this.OwnerOption = _listMap\r\n      } else {\r\n        keyArr.forEach((keys, idx) => {\r\n          const belongType = keys.split('&')[1] === 'true' ? 1 : 3\r\n          this.OwnerOption[keys] = _option.filter(v => v.Type === belongType)\r\n        })\r\n      }\r\n    },\r\n    async fetchData() {\r\n      this.loading = true\r\n\r\n      let hasTrueCompPart\r\n      let hasFalseCompPart\r\n      if (this.hasUnitPart) {\r\n        hasTrueCompPart = this.list.some(v => !!v.Belong_To_Component)\r\n        hasFalseCompPart = this.list.some(v => !v.Belong_To_Component)\r\n      } else {\r\n        hasTrueCompPart = true\r\n        hasFalseCompPart = false\r\n      }\r\n      const fetchDataForType = async(type) => {\r\n        try {\r\n          const res = await GetProcessListBase({ type })\r\n          if (res.IsSucceed) {\r\n            return res.Data\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            return []\r\n          }\r\n        } catch (error) {\r\n          console.error(`Error fetching data for type ${type}:`, error)\r\n          this.$message({\r\n            message: `请求失败`,\r\n            type: 'error'\r\n          })\r\n          return []\r\n        }\r\n      }\r\n\r\n      try {\r\n        let results = []\r\n\r\n        if (hasTrueCompPart && hasFalseCompPart) {\r\n          results = await Promise.all([fetchDataForType(1), fetchDataForType(3)])\r\n        } else if (hasTrueCompPart) {\r\n          results = [await fetchDataForType(1)]\r\n        } else if (hasFalseCompPart) {\r\n          results = [await fetchDataForType(3)]\r\n        }\r\n\r\n        this.option = results.filter(data => !!data.length).flat()\r\n        this.getComOption()\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    handleClose() {\r\n      this.$emit('close')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.dialog-footer{\r\n  text-align: right;\r\n  margin-top: 30px;\r\n}\r\n</style>\r\n"]}]}
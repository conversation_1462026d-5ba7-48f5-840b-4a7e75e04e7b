<template>
  <div class="container abs100">

    <div class="card-x">
      <tree-data ref="tree" :type-code="typeCode" :type-id="typeId" @nodeClick="nodeClick" @AddFirst="addFirst" @showRight="showRight" />
      <div class="right-card">
        <el-form v-if="showForm" ref="form" :model="form" :rules="rules" label-width="120px">
          <el-form-item :label="`${levelName}大类名称`" prop="Name">
            <el-input v-model.trim="form.Name" clearable maxlength="50" />
          </el-form-item>
          <el-form-item :label="`${levelName}大类编号`" prop="Code">
            <el-input v-model="form.Code" disabled />
          </el-form-item>
          <el-form-item label="生产周期" prop="Lead_Time">
            <el-input-number v-model.number="form.Lead_Time" class="cs-number-btn-hidden w100" clearable />
          </el-form-item>
          <el-form-item label="直发件" prop="Is_Component">
            <el-radio-group v-model="form.Is_Component">
              <el-radio :label="true">否</el-radio>
              <el-radio :label="false">是</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item>
            <el-button v-if="level<3" type="text" icon="el-icon-plus" @click="addNext">新增下一级</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :loading="submitLoading" @click="submit">保存</el-button>
            <el-button type="danger" :loading="deleteLoading" :disabled="hasChildrenNode" @click="handleDelete">删除</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <el-dialog
      v-dialogDrag
      :title="title"
      class="plm-custom-dialog"
      :visible.sync="dialogVisible"
      width="30%"
      @close="handleClose"
    >
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        ref="content"
        :add-level="addLevel"
        :parent-id="parentId"
        @close="handleClose"
        @getTreeList="getTreeData"
      />
    </el-dialog>

  </div>
</template>

<script>

import TreeData from './component/TreeData'
import Add from './component/Add'
import { DeleteComponentType, GetComponentTypeEntity, SaveProBimComponentType } from '@/api/PRO/component-type'

export default {
  name: 'ProfessionalCategoryListInfo',
  components: {
    TreeData,
    Add
  },
  data() {
    return {
      typeCode: 'Steel',
      typeId: 'd28a81a0-ce31-4b56-8e22-b86922668894',
      level: 1,
      addLevel: undefined,
      dialogVisible: false,
      submitLoading: false,
      deleteLoading: false,
      showForm: false,
      hasChildrenNode: true,
      currentComponent: '',
      parentId: '',
      title: '',
      form: {
        Name: '',
        Code: '',
        Is_Component: '',
        Lead_Time: 0
      },
      rules: {
        Name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        Code: [
          { required: true, message: '请输入编码', trigger: 'blur' }
        ],
        Is_Component: [
          { required: true, message: '请选择是否直发件', trigger: 'change' }
        ],
        Lead_Time: [
          { required: true, message: '请输入周期', trigger: 'blur' }
        ]
      },
      Is_Component: ''
    }
  },
  computed: {
    levelName() {
      return this.level === 1 ? '一级' : (this.level === 2 ? '二级' : (this.level === 3 ? '三级' : ''))
    }
  },
  methods: {
    addNext() {
      this.currentComponent = 'Add'
      this.addLevel = this.level + 1
      this.title = `新增下一级`
      this.parentId = this.form.Id
      this.dialogVisible = true
    },
    showRight(v) {
      this.showForm = v
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (!valid) {
          return false
        }
        if (this.Is_Component != this.form.Is_Component) {
          this.$confirm('直发件属性不会同步到已导入构件清单中，确认修改？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.submitConfirm()
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消修改'
            })
          })
        } else {
          this.submitConfirm()
        }
      })
    },
    submitConfirm() {
      this.submitLoading = true
      SaveProBimComponentType(this.form).then(res => {
        if (res.IsSucceed) {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.getTreeData()
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      }).finally(_ => {
        this.submitLoading = false
      })
    },
    getTreeData() {
      this.$refs['tree'].fetchData()
    },
    addFirst() {
      this.currentComponent = 'Add'
      this.title = '新增类别'
      this.addLevel = 1
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false
    },
    nodeClick(node) {
      this.level = node.level
      this.hasChildrenNode = node.childNodes.length > 0
      this.getInfo(node.data.Id)
    },
    getInfo(id) {
      GetComponentTypeEntity({
        id
      }).then(res => {
        if (res.IsSucceed) {
          Object.assign(this.form, res.Data)
          this.Is_Component = res.Data.Is_Component
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },

    handleDelete() {
      this.$confirm('是否删除当前类别?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteLoading = true
        DeleteComponentType({
          ids: this.form.Id
        }).then(res => {
          if (res.IsSucceed) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getTreeData()
            this.$refs['tree'].resetKey(this.form.Id)
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        }).finally(_ => {
          this.deleteLoading = false
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  display: flex;
  padding: 0 16px 16px;
  flex-direction: column;
  min-width: 998px;
  overflow: hidden;

  .top-x {
    line-height: 48px;
    height: 48px;
  }

  .card-x {
    height: calc(100% - 48px);
    display: flex;

    .right-card {
      display: flex;
      flex-direction: column;
      flex: 1;
      border-radius: 4px;
      background-color: #FFFFFF;
      .el-form{
        width: 50%;
        margin:  auto;
      }
    }
  }
}
</style>

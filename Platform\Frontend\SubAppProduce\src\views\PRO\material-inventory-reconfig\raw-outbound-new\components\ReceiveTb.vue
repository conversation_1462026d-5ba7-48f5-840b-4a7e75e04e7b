<template>
  <div class="receive-tb">
    <div v-if="!isView&&!isReturn" class="toolbar-container" style="margin-bottom: 8px">
      <vxe-toolbar>
        <template #buttons>
          <el-button type="primary" @click="openAddDialog(null)">新增</el-button>
          <el-button
            :disabled="!multipleSelection.length"
            type="danger"
            :loading="deleteLoading"
            @click="handleDelete"
          >删除
          </el-button>
          <el-button type="primary" :disabled="!multipleSelection.length" @click="batchDialogVisible = true">批量编辑领用项目</el-button>
          <PickSelect style="margin-left: 10px" :selected-list="currentTbData" :material-type="0" @addList="setTbData" />
          <el-button v-if="isOutsourcing" style="margin-left: 10px" :disabled="!multipleSelection.length" type="primary" @click="BulkEdit">整车含税单价</el-button>
          <DynamicTableFields
            style="margin-left: auto"
            title="表格配置"
            :table-config-code="gridCode"
            @updateColumn="init"
          />
        </template>
      </vxe-toolbar>
    </div>
    <div class="tb-x">
      <vxe-table
        v-if="renderComponent"
        ref="xTable"
        :empty-render="{name: 'NotData'}"
        show-header-overflow
        class="cs-vxe-table"
        :row-config="{ isCurrent: true, isHover: true}"
        align="left"
        height="auto"
        show-overflow
        auto-resize
        :loading="tbLoading"
        stripe
        size="medium"
        :data="currentTbData"
        resizable
        :edit-config="{
          enabled:enabledEdit,
          trigger: 'click',
          mode: 'cell',
          showIcon: !isView, showStatus: true
        }"
        :edit-rules="validRules"
        :tooltip-config="{ enterable: true }"
        show-footer
        :footer-method="footerMethod"
        @checkbox-all="tbSelectChange"
        @checkbox-change="tbSelectChange"
      >
        <vxe-column v-if="!isView" fixed="left" type="checkbox" width="60" />
        <template v-for="item in columns">
          <vxe-column
            :key="item.Code"
            :fixed="item.Is_Frozen ? (item.Frozen_Dirction || 'left') : ''"
            show-overflow="tooltip"
            :align="item.Align"
            :field="item.Code"
            :visible="item.Is_Display"
            :title="item.Display_Name"
            :min-width="item.Width"
            :edit-render="item.Is_Edit ? {} : null"
            :sortable="item.Is_Sort"
          >
            <template v-if="item.Style.tips" #header>
              <span>{{ item.Display_Name }}</span>
              <el-tooltip class="item" effect="dark">
                <div slot="content" v-html="item.Style.tips" />
                <i class="el-icon-question" style="cursor:pointer;font-size: 16px" />
              </el-tooltip>
            </template>
            <template #default="{ row }">
              <span v-if="item.Code === 'Warehouse_Location'">
                {{ row.WarehouseName }}/{{ row.LocationName }}
              </span>
              <span v-else-if="item.Code === 'InStoreDate'">
                {{ row.InStoreDate | timeFormat }}
              </span>
              <template v-else-if="item.Code === 'RawName'">
                <div>
                  <el-tag v-if="row.Is_PartA" type="danger" effect="dark" size="mini">甲供</el-tag>
                  <el-tag v-if="row.Is_Replace_Purchase" type="success" effect="dark" size="mini">代购</el-tag>
                  <el-tag v-if="row.Is_Surplus" type="warning" effect="dark" size="mini">余料</el-tag>
                  {{ row.RawName }}</div>
              </template>
              <template v-else-if="item.Code === 'OutStoreWeight'">
                {{ row.OutStoreWeight | getFormatNum(WEIGHT_DECIMAL) }}
              </template>
              <template v-else-if="item.Code === 'Voucher_Weight'">
                {{ row.Voucher_Weight | getFormatNum(3) }}
              </template>
              <span v-else> {{ row[item.Code] | displayValue }}</span>
            </template>
            <template v-if="item.Is_Edit" #edit="{ row }">
              <div v-if="item.Code === 'Actual_Thick'">
                <el-input v-model="row[item.Code]" type="text" @change="$emit('updateRow')" />
              </div>
              <div v-else-if="item.Code === 'Width'">
                <el-input
                  v-model="row[item.Code]"
                  :min="0"
                  type="number"
                  @change="checkWeight(row)"
                />
              </div>
              <div v-else-if="item.Code === 'Length'">
                <el-input
                  v-model="row[item.Code]"
                  :min="0"
                  type="number"
                  @change="checkWeight(row)"
                />
              </div>
              <div v-else-if="item.Code === 'OutStoreCount'">
                <el-input
                  v-model="row[item.Code]"
                  v-inp-num="{ toFixed: COUNT_DECIMAL, min: 0 }"
                  :min="0"
                  :disabled="!!row.PickSubId"
                  :max="row.AvailableCount"
                  @change="checkCount(row)"
                />
              </div>
              <div v-else-if="item.Code === 'OutStoreWeight'">
                <span> {{ row[item.Code] | displayValue }}</span>
              </div>
              <template v-else-if="item.Code === 'Pick_Project_Name'">
                <vxe-select
                  v-model="row.Pick_Sys_Project_Id"
                  style="width: 100%"
                  placeholder="请选择"
                  transfer
                  clearable
                  filterable
                  :disabled="!!row.PickSubId"
                  @change="(e)=>changeProject(e,row)"
                >
                  <vxe-option
                    v-for="item in projectOptions"
                    :key="item.Id"
                    :label="item.Short_Name"
                    :value="item.Sys_Project_Id"
                  />
                </vxe-select>
              </template>
              <div v-else>
                <el-input v-model="row[item.Code]" type="text" @blur="$emit('updateRow')" />
              </div>
            </template>
          </vxe-column>
        </template>
      </vxe-table>
    </div>
    <footer v-if="!isView">
      <div class="data-info">
        <el-tag v-if="!isReturn" size="medium" class="info-x">已选{{ multipleSelection.length }}条数据 </el-tag>
      </div>
      <div>
        <slot />
      </div>
    </footer>

    <el-dialog
      v-dialogDrag
      class="plm-custom-dialog"
      title="选择含税单价"
      :visible.sync="dialogVisible"
      width="30%"
      top="10vh"
      @close="handleClose"
    >
      <BatchEdit v-if="dialogVisible" @close="handleClose" @taxUnitPrice="getTaxUnitPrice" />
    </el-dialog>

    <el-dialog
      v-dialogDrag
      class="plm-custom-dialog"
      title="批量编辑领用项目"
      :visible.sync="batchDialogVisible"
      top="10vh"
      width="350px"
      @close="closeBatchDialog"
    >
      <el-select
        v-model="batchProjectId"
        style="width: 300px"
        placeholder="请选择"
        clearable
        filterable
      >
        <el-option
          v-for="item in projectOptions"
          :key="item.Id"
          :label="item.Short_Name"
          :value="item.Sys_Project_Id"
        />
      </el-select>
      <p style="margin: 20px">
        <i>注：仅能批量编辑公共库存的领用项目</i>
      </p>
      <div style="text-align: right">
        <el-button @click="closeBatchDialog">取消</el-button>
        <el-button type="primary" @click="batchChangeProject">确定</el-button>
      </div>
    </el-dialog>
  </div>

</template>
<script>
import { getTableConfig } from '@/views/PRO/material-receipt-management/utils'
import { formatNum } from '@/views/PRO/material-inventory-reconfig/raw-outbound-new/utils'
import Warehouse from '@/views/PRO/material-inventory-reconfig/raw-outbound-new/components/Warehouse.vue'
import BatchEdit from './BatchEdit.vue'
import SelectProject from '@/components/Select/SelectProject/index.vue'
import { GetProjectPageList } from '@/api/PRO/project'
import { COUNT_DECIMAL, OutBOUND_DETAIL_SUMMARY_FIELDS, WEIGHT_DECIMAL } from '@/views/PRO/material_v4/config'
import DynamicTableFields from '@/components/DynamicTableFields/index.vue'
import PickSelect from '@/views/PRO/material_v4/pickApply/select.vue'

export default {
  components: { PickSelect, DynamicTableFields, SelectProject, Warehouse, BatchEdit },
  filters: {
    getFormatNum(value, num) {
      return formatNum(value, num)
    }
  },
  props: {
    isView: {
      type: Boolean,
      default: false
    },
    isReturn: {
      type: Boolean,
      default: false
    },
    isOutsourcing: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      COUNT_DECIMAL,
      WEIGHT_DECIMAL,
      dialogVisible: false,
      enabledEdit: false,
      deleteLoading: false,
      tbLoading: false,
      currentTbData: [],
      columns: [],
      multipleSelection: [],
      bigTypeData: 1,
      validRules: {
        OutStoreCount: [
          { required: true, type: 'number', min: 0, message: '请输入' }
        ],
        Pick_Sys_Project_Id: [
          { required: true, type: 'string', min: 0, message: '请选择' }
        ]
      },
      projectOptions: [],
      batchDialogVisible: false,
      batchProjectId: '',
      excludedRoutes: ['PRORawMaterialOutboundView'],
      gridCode: 'PRORawReceiveOutList',
      renderComponent: true

    }
  },
  async mounted() {
    this.getProject()
    this.tbData = []
    await this.init()
  },
  methods: {
    // 重新渲染vux-table
    forceRerender() {
      // 从 DOM 中删除 my-component 组件
      this.renderComponent = false
      this.$nextTick(() => {
        // 在 DOM 中添加 my-component 组件
        this.renderComponent = true
      })
    },
    closeBatchDialog() {
      this.batchProjectId = ''
      this.batchDialogVisible = false
    },
    batchChangeProject() {
      this.multipleSelection.forEach((element, idx) => {
        const item = this.tbData.find((v) => v.uuid === element.uuid)
        const i = this.tbData.findIndex((v) => v.uuid === element.uuid)
        console.log({ i })
        // 更新项目ID和项目名称
        item.Pick_Sys_Project_Id = this.batchProjectId
        item.Pick_Project_Name = this.projectOptions.find(proj => proj.Sys_Project_Id === this.batchProjectId)?.Short_Name
        this.$set(this.tbData, i, item)
      })
      // 更新currentTbData以刷新视图
      this.mergeData()
      this.closeBatchDialog()
    },
    /**
     * 获取所属项目
     */
    getProject() {
      GetProjectPageList({
        Page: 1,
        PageSize: -1
      }).then((res) => {
        if (res.IsSucceed) {
          this.projectOptions = res.Data.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
    getTaxUnitPrice(val) {
      this.multipleSelection.forEach(row => {
        row.TaxUnitPrice = val
      })
    },
    async init() {
      this.enabledEdit = !this.isView
      this.tbLoading = true
      this.columns = await getTableConfig(this.gridCode)
      this.columns = this.columns.map(item => {
        item.Style = item.Style ? JSON.parse(item.Style) : ''
        return item
      })
      this.forceRerender()
      this.tbLoading = false
    },
    setTbData(list, checkOver, info) {
      list.forEach(item => {
        this.checkCount(item, checkOver)
        item.Pick_Project_Name = item.Project_Name // 默认领用项目为所属项目
      })
      this.tbData.push(...list)
      this.filterMethod()
      this.$emit('setInfo', info)
    },
    changeProject(e, item) {
      item.Pick_Project_Name = this.projectOptions.find(item => item.Sys_Project_Id === e.value)?.Short_Name
    },
    tbSelectChange(array) {
      this.multipleSelection = array.records
    },
    checkWeight(row, checkOver = true) {
      if (this.excludedRoutes.includes(this.$route.name)) {
        return
      }
      row.OutStoreWeight = (row.Unit_Weight * row.OutStoreCount).toFixed(WEIGHT_DECIMAL) / 1
      if (row.OutStoreWeight >= row.AvailableWeight && checkOver) {
        row.OutStoreWeight = row.AvailableWeight
        row.OutStoreCount = row.AvailableCount
      }
    },
    checkCount(row, checkOver = true) {
      if (row.OutStoreCount >= row.AvailableCount && checkOver) {
        row.OutStoreCount = row.AvailableCount
        row.OutStoreWeight = row.AvailableWeight
        return
      }
      this.checkWeight(row, false)
    },

    getTbData() {
      return this.tbData
    },

    openAddDialog() {
      this.$emit('openAddDialog')
    },
    handleDelete() {
      this.deleteLoading = true
      setTimeout(() => {
        const ids = this.multipleSelection.map(v => v.uuid)
        this.tbData = this.tbData.filter(row => !ids.includes(row.uuid))
        this.multipleSelection = []
        console.log(this.tbData)
        this.deleteLoading = false
        this.filterMethod()
      }, 0)
    },
    BulkEdit() {
      this.dialogVisible = true
    },
    clearTb() {
      this.tbData = []
      this.filterMethod()
    },
    mergeData() {
      // 使用深拷贝确保响应式更新
      this.currentTbData = JSON.parse(JSON.stringify(this.tbData))
    },
    filterMethod(filterInfo) {
      const filterKeys = (array, filters) => {
        return array.filter(item => {
          let flag = true
          for (let i = 0; i < filters.length; i++) {
            const element = filters[i]
            let rowLabel = item[element.key] || ''
            if (element.value === '') {
              flag = true
            }
            if (typeof rowLabel !== 'string') {
              rowLabel = rowLabel.toString()
            }
            if (rowLabel.includes(element.value)) {
              flag = true
            } else {
              flag = false
              break
            }
          }
          return flag
        })
      }

      if (!filterInfo) {
        this.currentTbData = this.tbData
      } else {
        const filters = []
        for (const filterInfoKey in filterInfo) {
          filters.push({
            key: filterInfoKey,
            value: filterInfo[filterInfoKey]
          })
        }
        console.log('filterInfoKey', filters)

        this.currentTbData = filterKeys(this.tbData, filters)
      }
      console.log('this.currentTbData', this.currentTbData)
    },
    checkValidate(tbData) {
      if (!tbData.length) {
        this.$message({
          message: '数据不能为空',
          type: 'warning'
        })
        return {
          status: false
        }
      }
      const tb = tbData.filter(item => item.OutStoreCount > 0)
      if (!tb.length) {
        this.$message({
          message: '出库数量不能为0',
          type: 'warning'
        })
        return {
          status: false
        }
      }
    },
    // 合计
    footerMethod({ columns, data }) {
      const footerData = [
        columns.map((column, index) => {
          if (OutBOUND_DETAIL_SUMMARY_FIELDS.includes(column.field)) {
            return this.sumNum(data, column.field, 5)
          }
          if (index === 0) {
            return '合计'
          }
          return null
        })
      ]
      return footerData
    },
    // 进行合计
    sumNum(costForm, field, digit) {
      let total = 0
      for (let i = 0; i < costForm.length; i++) {
        total += Number(costForm[i][field]) || 0
      }
      return total.toFixed(digit) / 1
    }
  }
}
</script>

<style scoped lang="scss">
.receive-tb{
 height: 100%;
  display: flex;
  flex-direction: column;
  .tb-x{
    flex:1;
    overflow: hidden;
  }
  footer {
    margin-top: 16px;
    display: flex;
    justify-content: space-between;
  }
}
</style>

<template>
  <div class="app-container abs100">
    <el-card v-loading="pgLoading" class="box-card h100" element-loading-text="正在处理...">
      <h4 class="topTitle"><span />基本信息</h4>
      <el-form
        ref="formInline"
        :inline="true"
        :model="formInline"
        class="demo-form-inline"
      >
        <el-form-item v-if="!isAdd" label="排产单号" prop="Schduling_Code">
          <span v-if="isView">{{ formInline.Status === 0 ? '' : formInline.Schduling_Code }}</span>
          <el-input v-else v-model="formInline.Schduling_Code" disabled />
        </el-form-item>
        <el-form-item label="计划员" prop="Create_UserName">
          <span v-if="isView">{{ formInline.Create_UserName }}</span>
          <el-input
            v-else
            v-model="formInline.Create_UserName"
            disabled
          />
        </el-form-item>
        <el-form-item
          label="要求完成时间"
          prop="Finish_Date"
          :rules="{ required: true, message: '请选择', trigger: 'change' }"
        >
          <span v-if="isView">{{ formInline.Finish_Date | timeFormat }}</span>
          <el-date-picker
            v-else
            v-model="formInline.Finish_Date"
            :picker-options="pickerOptions"
            :disabled="isView"
            value-format="yyyy-MM-dd"
            type="date"
            placeholder="选择日期"
          />
        </el-form-item>
        <el-form-item label="备注" prop="Remark">
          <span v-if="isView">{{ formInline.Remark }}</span>
          <el-input
            v-else
            v-model="formInline.Remark"
            :disabled="isView"
            style="width: 320px"
            placeholder="请输入"
          />
        </el-form-item>
      </el-form>
      <el-divider class="elDivder" />
      <div class="btn-x">
        <template v-if="!isView">
          <div>
            <el-button type="primary" @click="handleAddDialog()">添加</el-button>
            <el-button
              v-if="workshopEnabled"
              :disabled="!multipleSelection.length"
              @click="handleBatchWorkshop(1)"
            >批量分配车间
            </el-button>
            <el-button
              v-if="!isCom"
              :disabled="!multipleSelection.length"
              @click="handleSelectMenu('process')"
            >批量分配工序
            </el-button>
            <el-dropdown v-if="isCom" style="margin:0 10px" @command="handleSelectMenu">
              <el-button :disabled="!multipleSelection.length" type="primary" plain style="width: 140px">
                分配工序<i class="el-icon-arrow-down el-icon--right" />
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  command="process"
                >批量分配工序
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="isCom"
                  command="deal"
                >构件类型自动分配
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button
              v-if="!isCom && !isOwnerNull"
              :disabled="!multipleSelection.length"
              @click="handleBatchOwner(1)"
            >批量分配领用工序
            </el-button>
            <el-button
              plain
              :disabled="!tbData.length"
              :loading="false"
              @click="handleReverse"
            >反选
            </el-button>
            <el-button
              type="danger"
              plain
              :loading="deleteLoading"
              :disabled="!multipleSelection.length"
              @click="handleDelete"
            >删除
            </el-button>
          </div>
          <div v-if="!isCom">
            <el-select
              v-model="searchType"
              placeholder="请选择"
              clearable
              @change="tbFilterChange"
            >
              <el-option
                v-for="item in typeOption"
                :key="item.value"
                :label="item.label"
                :value="item.label"
              />
            </el-select>
          </div>
        </template>
      </div>
      <div class="tb-x">
        <vxe-table
          ref="xTable"
          :empty-render="{name: 'NotData'}"
          show-header-overflow
          :checkbox-config="{checkField: 'checked'}"
          class="cs-vxe-table"
          :row-config="{isCurrent: true, isHover: true}"
          align="left"
          height="100%"
          :filter-config="{showIcon:false}"
          show-overflow
          :loading="tbLoading"
          stripe
          :scroll-y="{enabled: true, gt: 20}"
          size="medium"
          :edit-config="{
            trigger: 'click',
            mode: 'cell',
            showIcon: !isView,
            activeMethod: activeCellMethod,
          }"
          :data="tbData"
          resizable
          :tooltip-config="{ enterable: true }"
          @checkbox-all="tbSelectChange"
          @checkbox-change="tbSelectChange"
        >
          <vxe-column v-if="!isView" fixed="left" type="checkbox" width="60" />
          <template v-for="item in columns">
            <vxe-column
              v-if="item.Code === 'Is_Component'"
              :key="item.Code"
              :align="item.Align"
              :field="item.Code"
              :title="item.Display_Name"
              sortable
              :width="item.Width"
            >
              <template #default="{ row }">
                <el-tag
                  :type="row.Is_Component ? 'danger' : 'success'"
                >{{ row.Is_Component ? '否' : '是' }}
                </el-tag>
              </template>
            </vxe-column>
            <vxe-column
              v-else-if="item.Code === 'Type_Name'"
              :key="item.Code"
              :align="item.Align"
              :field="item.Code"
              :filters="typeOption"
              :title="item.Display_Name"
              sortable
              :width="item.Width"
            >
              <template #default="{ row }">
                {{ row.Type_Name | displayValue }}
              </template>
            </vxe-column>
            <vxe-column
              v-else-if="item.Code === 'Technology_Path'"
              :key="item.Code"
              :align="item.Align"
              :field="item.Code"
              :title="item.Display_Name"
              sortable
              :min-width="item.Width"
            >
              <template #default="{ row }">
                {{ row.Technology_Path | displayValue }}
                <i
                  v-if="!isView"
                  class="el-icon-edit"
                  @click="openBPADialog(2, row)"
                />
              </template>
            </vxe-column>
            <vxe-column
              v-else-if="item.Code === 'Part_Used_Process'"
              :key="item.Code"
              :align="item.Align"
              :field="item.Code"
              :title="item.Display_Name"
              sortable
              :min-width="item.Width"
            >
              <template #default="{ row }">
                {{ row.Part_Used_Process | displayValue }}
                <i
                  v-if="!isView"
                  class="el-icon-edit"
                  @click="handleBatchOwner(2, row)"
                />
              </template>
            </vxe-column>
            <vxe-column
              v-else-if="item.Code === 'Workshop_Name'"
              :key="item.Code"
              :align="item.Align"
              :field="item.Code"
              :title="item.Display_Name"
              sortable
              :min-width="item.Width"
            >
              <template #default="{ row }">
                {{ row.Workshop_Name | displayValue }}
                <i
                  v-if="!isView"
                  class="el-icon-edit"
                  @click="handleBatchWorkshop(2, row)"
                />
              </template>
            </vxe-column>
            <vxe-column
              v-else
              :key="item.Id"
              :align="item.Align"
              :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
              show-overflow="tooltip"
              sortable
              :field="item.Code"
              :title="item.Display_Name"
              :min-width="item.Width"
            />
          </template>

        </vxe-table>
      </div>
      <el-divider v-if="!isView" class="elDivder" />
      <footer v-if="!isView">
        <div class="data-info">
          <el-tag
            size="medium"
            class="info-x"
          >已选 {{ multipleSelection.length }} 条数据
          </el-tag>
          <el-tag v-if="tipLabel" size="medium" class="info-x">{{
            tipLabel
          }}
          </el-tag>
        </div>
        <div>
          <el-button v-if="workshopEnabled" type="primary" @click="saveWorkShop">保存车间分配</el-button>
          <el-button
            type="primary"
            :loading="saveLoading"
            @click="saveDraft(false)"
          >保存草稿
          </el-button>
          <el-button :disabled="deleteLoading" @click="handleSubmit">下发任务</el-button>
        </div>
      </footer>
    </el-card>
    <el-dialog
      v-if="dialogVisible"
      v-dialogDrag
      class="plm-custom-dialog"
      :title="title"
      :visible.sync="dialogVisible"
      :width="dWidth"
      top="10vh"
      @close="handleClose"
    >
      <component
        :is="currentComponent"
        ref="content"
        :process-list="processList"
        :page-type="pageType"
        :part-type-option="typeOption"
        @close="handleClose"
        @sendProcess="sendProcess"
        @workShop="getWorkShop"
        @refresh="fetchData"
        @setProcessList="setProcessList"
      />
    </el-dialog>

    <el-dialog
      v-dialogDrag
      class="plm-custom-dialog"
      :title="title"
      :visible.sync="openAddDraft"
      :width="dWidth"
      top="10vh"
      @close="handleClose"
    >
      <keep-alive>
        <add-draft
          ref="draft"
          :schedule-id="scheduleId"
          :show-dialog="openAddDraft"
          :page-type="pageType"
          @sendSelectList="mergeSelectList"
          @close="handleClose"
        />
      </keep-alive>
    </el-dialog>
  </div>
</template>

<script>

import { closeTagView, debounce } from '@/utils'
import BatchProcessAdjust from './components/BatchProcessAdjust'
import {
  GetCompSchdulingInfoDetail,
  GetPartSchdulingInfoDetail,
  GetSchdulingWorkingTeams,
  SaveComponentSchedulingWorkshop,
  SaveCompSchdulingDraft,
  SavePartSchdulingDraft,
  SavePartSchedulingWorkshop,
  SaveSchdulingTaskById
} from '@/api/PRO/production-task'
import AddDraft from './components/addDraft'
import OwnerProcess from './components/OwnerProcess'
import Workshop from './components/Workshop.vue'
import { GetGridByCode } from '@/api/sys'
import { FIX_COLUMN, uniqueCode } from '@/views/PRO/plan-production/schedule-production/constant'
import { v4 as uuidv4 } from 'uuid'
import numeral from 'numeral'
import { GetLibListType, GetProcessListBase } from '@/api/PRO/technology-lib'
import { AreaGetEntity } from '@/api/plm/projects'
import { mapActions, mapGetters } from 'vuex'
import { GetPartTypeList } from '@/api/PRO/partType'
import * as moment from 'moment/moment'

const SPLIT_SYMBOL = '$_$'
export default {
  components: { BatchProcessAdjust, AddDraft, Workshop, OwnerProcess },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
        }
      },
      searchType: '',
      formInline: {
        Schduling_Code: '',
        Create_UserName: '',
        Finish_Date: '',
        Remark: ''
      },
      total: 0,
      columns: [],
      tbData: [],
      tbConfig: {},
      TotalCount: 0,
      multipleSelection: [],
      pgLoading: false,
      deleteLoading: false,
      workShopIsOpen: false,
      isOwnerNull: false,
      dialogVisible: false,
      openAddDraft: false,
      saveLoading: false,
      tbLoading: false,
      isCheckAll: false,
      currentComponent: '',
      dWidth: '25%',
      title: '',
      search: () => ({}),
      pageType: undefined,
      tipLabel: '',
      technologyOption: [],
      typeOption: [],
      workingTeam: [],
      pageStatus: undefined,
      scheduleId: '',
      partComOwnerColumn: null
    }
  },
  watch: {
    'tbData.length': {
      handler(n, o) {
        this.checkOwner()
      },
      immediate: false
    }
  },
  computed: {
    isCom() {
      return this.pageType === 'com'
    },
    isView() {
      return this.pageStatus === 'view'
    },
    isEdit() {
      return this.pageStatus === 'edit'
    },
    isAdd() {
      return this.pageStatus === 'add'
    },
    ...mapGetters('factoryInfo', ['workshopEnabled']),
    ...mapGetters('schedule', ['processList'])
  },
  beforeRouteEnter(to, from, next) {
    if (to.query.status === 'view') {
      to.meta.title = '查看'
    } else {
      to.meta.title = '草稿'
    }
    next()
  },
  async mounted() {
    this.initProcessList()
    this.tbDataMap = {}
    this.pageType = this.$route.query.pg_type
    this.pageStatus = this.$route.query.status
    this.model = this.$route.query.model
    this.scheduleId = this.$route.query.pid || ''
    // this.formInline.Create_UserName = this.$store.getters.name
    // 框架问题引起store数据丢失，已反馈，结果：此处先使用localStorage
    this.formInline.Create_UserName = localStorage.getItem('UserAccount')
    if (!this.isCom) {
      this.getType()
    } else {

    }
    this.unique = uniqueCode()
    this.checkWorkshopIsOpen()
    this.getAreaInfo()
    this.search = debounce(this.fetchData, 800, true)
    await this.mergeConfig()
    if (this.isView || this.isEdit) {
      this.fetchData()
    }
  },
  methods: {
    ...mapActions('schedule', ['changeProcessList', 'initProcessList']),
    checkOwner() {
      if (this.isCom) return
      this.isOwnerNull = this.tbData.every(v => !v.Comp_Import_Detail_Id)
      const idx = this.columns.findIndex(v => v.Code === 'Part_Used_Process')
      if (this.isOwnerNull) {
        idx !== -1 && this.columns.splice(idx, 1)
      } else {
        if (idx === -1) {
          if (!this.ownerColumn) {
            this.$message({
              message: '列表配置字段缺少零件领用工序字段',
              type: 'success'
            })
            return
          }
          this.columns.push(this.ownerColumn)
        }
      }
    },
    async mergeConfig() {
      await this.getConfig()
      await this.getWorkTeam()
    },
    async getConfig() {
      const configCode = this.isCom ? (this.isView ? 'PROComViewPageTbConfig' : 'PROComDraftPageTbConfig') : (this.isView ? 'PROPartViewPageTbConfig' : 'PROPartDraftPageTbConfig')

      await this.getTableConfig(configCode)
      if (!this.workshopEnabled) {
        this.columns = this.columns.filter(v => v.Code !== 'Workshop_Name')
      }
      this.checkOwner()
    },
    async fetchData() {
      this.tbLoading = true
      let resData = null
      if (this.isCom) {
        resData = await this.getComPageList()
      } else {
        resData = await this.getPartPageList()
      }
      this.initTbData(resData)
      this.tbLoading = false
    },
    closeView() {
      closeTagView(this.$store, this.$route)
    },
    checkWorkshopIsOpen() {
      this.workShopIsOpen = true
    },
    tbSelectChange(array) {
      this.multipleSelection = array.records
    },
    getAreaInfo() {
      AreaGetEntity({
        id: this.$route.query.areaId
      }).then(res => {
        if (res.IsSucceed) {
          if (!res.Data) {
            return []
          }

          const start = moment(res.Data?.Demand_Begin_Date)
          const end = moment(res.Data?.Demand_End_Date)
          this.pickerOptions.disabledDate = (time) => {
            return time.getTime() < start || time.getTime() > end
          }
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    handleClose() {
      this.dialogVisible = false
      this.openAddDraft = false
    },
    getComPageList() {
      return new Promise((resolve, reject) => {
        const {
          // install,
          // projectId,
          pid
          // areaId
        } = this.$route.query
        GetCompSchdulingInfoDetail({
          Schduling_Plan_Id: pid
        }).then((res) => {
          if (res.IsSucceed) {
            const { Schduling_Plan, Schduling_Comps, Process_List } = res.Data
            this.formInline = Object.assign(this.formInline, Schduling_Plan)
            Process_List.forEach(item => {
              const plist = {
                key: item.Process_Code,
                value: item
              }
              this.changeProcessList(plist)
            })
            resolve(Schduling_Comps || [])
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
            reject()
          }
        })
      })
    },
    getPartPageList() {
      return new Promise((resolve, reject) => {
        const {
          pid
        } = this.$route.query
        GetPartSchdulingInfoDetail({
          Schduling_Plan_Id: pid
        }).then((res) => {
          if (res.IsSucceed) {
            const SarePartsModel = res.Data?.SarePartsModel.map(v => {
              if (v.Scheduled_Used_Process) {
                // 已存在操作过数据
                v.Part_Used_Process = v.Scheduled_Used_Process
              }
              return v
            })
            this.formInline = Object.assign(this.formInline, res.Data?.Schduling_Plan)
            resolve(SarePartsModel || [])
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
            reject()
          }
        })
      })
    },
    initTbData(list, teamKey = 'Allocation_Teams') {
      this.tbData = list.map(row => {
        const processList = row.Technology_Path?.split('/') || []
        row.uuid = uuidv4()
        this.addElementToTbData(row)
        const newData = row[teamKey].filter((r) => processList.findIndex((p) => r.Process_Code === p) !== -1)
        newData.forEach((ele, index) => {
          const code = this.getRowUnique(row.uuid, ele.Process_Code, ele.Working_Team_Id)
          const max = this.getRowUniqueMax(row.uuid, ele.Process_Code, ele.Working_Team_Id)
          row[code] = ele.Count
          row[max] = 0
        })
        this.setInputMax(row)
        return row
      })
    },
    mergeSelectList(newList) {
      console.time('fff')
      newList.forEach((element, index) => {
        const cur = this.getMergeUniqueRow(element)

        if (!cur) {
          element.puuid = element.uuid
          element.Schduled_Count = element.chooseCount
          element.Schduled_Weight = numeral(element.chooseCount * element.Weight).format('0.[00]')
          this.tbData.push(element)
          this.addElementToTbData(element)
          return
        }

        cur.puuid = element.uuid

        cur.Schduled_Count += element.chooseCount
        cur.Schduled_Weight = numeral(cur.Schduled_Weight).add(element.chooseCount * element.Weight).format('0.[00]')
        if (!cur.Technology_Path) {
          return
        }
        this.setInputMax(cur)
      })

      // if (this.isCom) {
      //   this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)
      // } else {
      //   this.tbData.sort((a, b) => a.Part_Code.localeCompare(b.Part_Code))
      // }
      this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)
      console.timeEnd('fff')
    },
    addElementToTbData(element) {
      const key = this.isCom ? element.Comp_Code : (element.Component_Code ?? '') + element.Part_Code
      this.tbDataMap[key] = element
    },
    getMergeUniqueRow(element) {
      const key = this.isCom ? element.Comp_Code : (element.Component_Code ?? '') + element.Part_Code
      return this.tbDataMap[key]
    },
    checkForm() {
      let isValidate = true
      this.$refs['formInline'].validate((valid) => {
        if (!valid) isValidate = false
      })
      return isValidate
    },
    async saveDraft(isOrder = false) {
      const checkSuccess = this.checkForm()
      if (!checkSuccess) return false
      const { tableData, status } = this.getSubmitTbInfo()
      if (!status) return false
      if (!isOrder) {
        this.saveLoading = true
      }
      const isSuccess = await this.handleSaveDraft(tableData, isOrder)
      console.log('isSuccess', isSuccess)
      if (!isSuccess) return false
      if (isOrder) return isSuccess
      this.$refs['draft']?.fetchData()
      this.saveLoading = false
    },
    async saveWorkShop() {
      const checkSuccess = this.checkForm()
      if (!checkSuccess) return false
      const obj = {}
      if (!this.tbData.length) {
        this.$message({
          message: '数据不能为空',
          type: 'success'
        })
        return
      }
      if (this.isCom) {
        obj.Schduling_Comps = this.tbData
      } else {
        obj.SarePartsModel = this.tbData
      }
      if (this.isEdit) {
        obj.Schduling_Plan = this.formInline
      } else {
        const {
          install,
          projectId,
          areaId
        } = this.$route.query
        obj.Schduling_Plan = {
          ...this.formInline,
          Project_Id: projectId,
          InstallUnit_Id: install,
          Area_Id: areaId,
          Schduling_Model: this.model // 1构件单独排产，2零件单独排产，3构/零件一起排产
        }
      }
      this.pgLoading = true
      const _fun = this.isCom ? SaveComponentSchedulingWorkshop : SavePartSchedulingWorkshop
      _fun(obj).then(res => {
        if (res.IsSucceed) {
          this.pgLoading = false
          this.$message({
            message: '保存成功',
            type: 'success'
          })
          this.closeView()
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    getSubmitTbInfo() {
      // 处理上传的数据
      const tableData = JSON.parse(JSON.stringify(this.tbData))
      for (let i = 0; i < tableData.length; i++) {
        const element = tableData[i]
        const list = []
        if (!element.Technology_Path) {
          this.$message({
            message: '工序不能为空',
            type: 'warning'
          })
          return { status: false }
        }
        // if (!this.isCom && element.Comp_Import_Detail_Id && !element.Part_Used_Process) {
        //   // 零构件 零件单独排产
        //   this.$message({
        //     message: '零件领用工序不能为空',
        //     type: 'warning'
        //   })
        //   return { status: false }
        // }
        if (element.Scheduled_Technology_Path && element.Scheduled_Technology_Path !== element.Technology_Path) {
          this.$message({
            message: `请和该区域批次下已排产同${this.isCom ? '构件' : '零件'}保持工序一致`,
            type: 'warning'
          })
          return { status: false }
        }
        if (element.Scheduled_Used_Process && element.Scheduled_Used_Process !== element.Part_Used_Process) {
          this.$message({
            message: `请和该区域批次下已排产同零件领用工序保持一致`,
            type: 'warning'
          })
          return { status: false }
        }
        const processList = Array.from(new Set(element.Technology_Path.split('/')))
        processList.forEach(code => {
          const groups = this.workingTeam.filter(v => v.Process_Code === code)
          const groupsList = groups.map(group => {
            const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)
            const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)
            const obj = {
              Team_Task_Id: element.Team_Task_Id,
              Comp_Code: element.Comp_Code,
              Again_Count: +element[uCode] || 0, // 不填，后台让传0
              Part_Code: this.isCom ? null : '',
              Process_Code: code,
              Technology_Path: element.Technology_Path,
              Working_Team_Id: group.Working_Team_Id,
              Working_Team_Name: group.Working_Team_Name
            }
            delete element[uCode]
            delete element[uMax]
            return obj
          })
          list.push(...groupsList)
        })
        const hasInput = Object.keys(element).filter(_ => _.startsWith(element['uuid']))
        hasInput.forEach((item) => {
          delete element[item]
        })
        delete element['uuid']
        delete element['_X_ROW_KEY']
        delete element['puuid']
        element.Allocation_Teams = list
      }
      return { tableData, status: true }
    },
    async handleSaveDraft(tableData, isOrder) {
      console.log('保存草稿')
      const _fun = this.isCom ? SaveCompSchdulingDraft : SavePartSchdulingDraft
      const obj = {}
      if (this.isCom) {
        obj.Schduling_Comps = tableData
        const p = []
        for (const objKey in this.processList) {
          if (this.processList.hasOwnProperty(objKey)) {
            p.push(this.processList[objKey])
          }
        }
        obj.Process_List = p
      } else {
        obj.SarePartsModel = tableData
      }
      if (this.isEdit) {
        obj.Schduling_Plan = this.formInline
      } else {
        const {
          install,
          projectId,
          areaId
        } = this.$route.query
        obj.Schduling_Plan = {
          ...this.formInline,
          Project_Id: projectId,
          InstallUnit_Id: install,
          Area_Id: areaId,
          Schduling_Model: this.model // 1构件单独排产，2零件单独排产，3构/零件一起排产
        }
      }
      let orderSuccess = false
      console.log('obj', obj)

      await _fun(obj).then(res => {
        if (res.IsSucceed) {
          if (!isOrder) {
            this.pgLoading = false
            this.$message({
              message: '保存成功',
              type: 'success'
            })
            this.closeView()
          } else {
            this.templateScheduleCode = res.Data
            orderSuccess = true
            console.log('保存草稿成功 ')
          }
        } else {
          this.saveLoading = false
          this.pgLoading = false
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
      console.log('结束 ')
      return orderSuccess
    },
    handleDelete() {
      this.deleteLoading = true
      setTimeout(() => {
        const selectedUuids = new Set(this.multipleSelection.map(v => v.uuid))
        this.tbData = this.tbData.filter(item => {
          const isSelected = selectedUuids.has(item.uuid)
          if (isSelected) {
            const key = this.isCom ? item.Comp_Code : (item.Component_Code ?? '') + item.Part_Code
            delete this.tbDataMap[key]
          }
          return !isSelected
        })
        this.$nextTick(_ => {
          this.$refs['draft']?.mergeData(this.multipleSelection)
          this.multipleSelection = []
        })
        this.deleteLoading = false
      }, 0)
    },
    async getWorkTeam() {
      await GetSchdulingWorkingTeams({
        type: this.isCom ? 1 : 2
      }).then(res => {
        if (res.IsSucceed) {
          this.workingTeam = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    handleSubmit() {
      this.$refs['formInline'].validate((valid) => {
        if (!valid) return
        const { tableData, status } = this.getSubmitTbInfo()
        if (!status) return
        this.$confirm('是否提交当前数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.saveDraftDoSubmit(tableData)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
      })
    },
    async saveDraftDoSubmit() {
      this.pgLoading = true
      if (this.formInline?.Schduling_Code) {
        const isSuccess = await this.saveDraft(true)
        console.log('saveDraftDoSubmit', isSuccess)
        isSuccess && this.doSubmit(this.formInline.Id)
      } else {
        const isSuccess = await this.saveDraft(true)
        isSuccess && this.doSubmit(this.templateScheduleCode)
      }
    },
    doSubmit(scheduleCode) {
      SaveSchdulingTaskById({
        schdulingPlanId: scheduleCode
      }).then(res => {
        if (res.IsSucceed) {
          this.$message({
            message: '下达成功',
            type: 'success'
          })
          this.closeView()
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      }).finally(_ => {
        this.pgLoading = false
      }).catch(_ => {
        this.pgLoading = false
      })
    },
    getWorkShop(value) {
      const {
        origin,
        row,
        workShop: {
          Id,
          Display_Name
        }
      } = value
      if (origin === 2) {
        if (value.workShop?.Id) {
          row.Workshop_Name = Display_Name
          row.Workshop_Id = Id
          this.setPath(row, Id)
        } else {
          row.Workshop_Name = ''
          row.Workshop_Id = ''
        }
      } else {
        this.multipleSelection.forEach(item => {
          if (value.workShop?.Id) {
            item.Workshop_Name = Display_Name
            item.Workshop_Id = Id
            this.setPath(item, Id)
          } else {
            item.Workshop_Name = ''
            item.Workshop_Id = ''
          }
        })
      }
    },
    setPath(row, Id) {
      if (row?.Scheduled_Workshop_Id) {
        if (row.Scheduled_Workshop_Id !== Id) {
          row.Technology_Path = ''
        }
      } else {
        row.Technology_Path = ''
      }
    },
    handleBatchWorkshop(origin, row) {
      this.title = origin === 1 ? '批量分配车间' : '分配车间'
      this.currentComponent = 'Workshop'
      this.dWidth = '30%'
      this.dialogVisible = true
      this.$nextTick(_ => {
        this.$refs['content'].fetchData(origin, row)
      })
    },
    async handleAutoDeal() {
      /*      if (this.workshopEnabled) {
        const hasWorkShop = this.checkHasWorkShop(1, this.multipleSelection)
        if (!hasWorkShop) return
      }*/

      this.$confirm(`是否将选中数据按构件类型自动分配`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (this.workshopEnabled) {
          const p = this.multipleSelection.map(item => {
            return {
              uniqueType: `${item.Type}$_$${item.Workshop_Id}`
            }
          })
          const codes = Array.from(new Set(p.map(v => v.uniqueType)))
          const objKey = {}
          Promise.all(codes.map(v => {
            const info = v.split('$_$')
            return this.setLibType(info[0], info[1])
          })
          ).then(res => {
            const hasUndefined = res.some(item => item == undefined)
            if (hasUndefined) {
              this.$message({
                message: '所选车间内工序班组与构件类型工序不匹配，请手动分配工序',
                type: 'warning'
              })
            }

            res.forEach((element, idx) => {
              objKey[codes[idx]] = element
            })
            this.multipleSelection.forEach((element) => {
              element.Technology_Path = objKey[`${element.Type}$_$${element.Workshop_Id}`]
              this.resetWorkTeamMax(element, element.Technology_Path)
            })
          })
        } else {
          const p = this.multipleSelection.map(item => item.Type)
          const codes = Array.from(new Set(p))
          const objKey = {}

          Promise.all(codes.map(v => {
            return this.setLibType(v)
          })).then(res => {
            res.forEach((element, idx) => {
              objKey[codes[idx]] = element
            })
            this.multipleSelection.forEach((element) => {
              element.Technology_Path = objKey[element.Type]
              this.resetWorkTeamMax(element, element.Technology_Path)
            })
          })
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    getProcessOption(workshopId) {
      return new Promise((resolve, reject) => {
        GetProcessListBase({
          workshopId: workshopId,
          type: this.isCom ? 1 : 2 // 0:全部，工艺类型1：构件工艺，2：零件工艺
        }).then(res => {
          if (res.IsSucceed) {
            const process = res.Data.map(v => v.Code)
            resolve(process)
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      })
    },
    setLibType(code, workshopId) {
      return new Promise((resolve) => {
        const obj = {
          Component_type: code,
          type: 1
        }
        if (this.workshopEnabled) {
          obj.workshopId = workshopId
        }
        GetLibListType(obj).then(res => {
          if (res.IsSucceed) {
            if (res.Data.Data && res.Data.Data.length) {
              const info = res.Data.Data[0]
              const workCode = info.WorkCode && info.WorkCode.replace(/\\/g, '/')
              resolve(workCode)
            } else {
              resolve(undefined)
            }
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      })
    },
    inputChange(row) {
      this.setInputMax(row)
    },
    setInputMax(row) {
      const inputValuesKeys = Object.keys(row)
        .filter(v => !v.endsWith('max') && v.startsWith(row.uuid) && v.length > row.uuid.length)
      inputValuesKeys.forEach((val) => {
        const curCode = val.split(SPLIT_SYMBOL)[1]
        const otherTotal = inputValuesKeys.filter(x => {
          const code = x.split(SPLIT_SYMBOL)[1]
          return x !== val && code === curCode
        }).reduce((acc, item) => {
          return acc + numeral(row[item]).value()
        }, 0)
        row[val + SPLIT_SYMBOL + 'max'] = row.Schduled_Count - otherTotal
      })
    },
    sendProcess({ arr, str }) {
      let isSuccess = true
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i]
        if (item.originalPath && item.originalPath !== str) {
          isSuccess = false
          break
        }
        item.Technology_Path = str
      }
      if (!isSuccess) {
        this.$message({
          message: '请和该区域批次下已排产同构件保持工序一致',
          type: 'warning'
        })
      }
    },
    resetWorkTeamMax(row, str) {
      if (str) {
        row.Technology_Path = str
      } else {
        str = row.Technology_Path
      }
      const list = str?.split('/') || []
      this.workingTeam.forEach((element, idx) => {
        const cur = list.some(k => k === element.Process_Code)
        const code = this.getRowUnique(row.uuid, element.Process_Code, element.Working_Team_Id)
        const max = this.getRowUniqueMax(row.uuid, element.Process_Code, element.Working_Team_Id)
        if (cur) {
          if (!row[code]) {
            this.$set(row, code, 0)
            this.$set(row, max, row.Schduled_Count)
          }
        } else {
          this.$delete(row, code)
          this.$delete(row, max)
        }
      })
    },
    checkPermissionTeam(processStr, processCode) {
      if (!processStr) return false
      const list = processStr?.split('/') || []
      return !!list.some(v => v === processCode)
    },

    async getTableConfig(code) {
      await GetGridByCode({
        code
      }).then((res) => {
        const { IsSucceed, Data, Message } = res
        if (IsSucceed) {
          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)
          const list = Data.ColumnList || []
          this.ownerColumn = list.find(item => item.Code === 'Part_Used_Process')
          this.ownerColumn2 = list.find(item => item.Code === 'Is_Main_Part')
          this.columns = this.setColumnDisplay(list)
        } else {
          this.$message({
            message: Message,
            type: 'error'
          })
        }
      })
    },
    setColumnDisplay(list) {
      return list.filter(v => v.Is_Display).map(item => {
        if (FIX_COLUMN.includes(item.Code)) {
          item.fixed = 'left'
        }
        return item
      })
    },
    activeCellMethod({ row, column, columnIndex }) {
      if (this.isView) return false
      const processCode = column.field?.split('$_$')[1]
      return this.checkPermissionTeam(row.Technology_Path, processCode)
    },
    openBPADialog(type, row) {
      if (this.workshopEnabled) {
        if (type === 1) {
          const IsUnique = this.checkIsUniqueWorkshop()
          if (!IsUnique) return
        }
      }
      this.title = type === 2 ? '工序调整' : '批量工序调整'
      this.currentComponent = 'BatchProcessAdjust'
      this.dWidth = this.isCom ? '60%' : '30%'
      this.dialogVisible = true
      this.$nextTick(_ => {
        this.$refs['content'].setData(type === 2 ? [row] : this.multipleSelection, type === 2 ? row.Technology_Path : '')
      })
    },
    checkIsUniqueWorkshop() {
      let isUnique = true
      const firstV = this.multipleSelection[0].Workshop_Name
      for (let i = 1; i < this.multipleSelection.length; i++) {
        const item = this.multipleSelection[i]
        if (item.Workshop_Name !== firstV) {
          isUnique = false
          break
        }
      }
      if (!isUnique) {
        this.$message({
          message: '批量分配工序时只有相同车间下的才可一起批量分配',
          type: 'warning'
        })
      }
      return isUnique
    },
    checkHasWorkShop(type, arr) {
      let hasWorkShop = true
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i]
        if (!item.Workshop_Name) {
          hasWorkShop = false
          break
        }
      }
      if (!hasWorkShop) {
        this.$message({
          message: '请先选择车间后再进行工序分配',
          type: 'warning'
        })
      }
      return hasWorkShop
    },
    handleAddDialog(type = 'add') {
      if (this.isCom) {
        this.title = '构件排产'
      } else {
        this.title = '添加零件'
      }
      this.currentComponent = 'AddDraft'
      this.dWidth = '80%'
      this.openAddDraft = true
      this.$nextTick(_ => {
        this.$refs['draft'].setPageData()
      })
    },
    getRowUnique(uuid, processCode, workingId) {
      return `${uuid}${SPLIT_SYMBOL}${processCode}${SPLIT_SYMBOL}${workingId}`
    },
    getRowUniqueMax(uuid, processCode, workingId) {
      return this.getRowUnique(uuid, processCode, workingId) + `${SPLIT_SYMBOL}max`
    },
    handleSelectMenu(v) {
      if (v === 'process') {
        this.openBPADialog(1)
      } else if (v === 'deal') {
        this.handleAutoDeal(1)
      }
    },
    handleBatchOwner(type, row) {
      this.title = '批量分配领用工序'
      this.currentComponent = 'OwnerProcess'
      this.dWidth = '30%'
      this.dialogVisible = true
      this.$nextTick(_ => {
        this.$refs['content'].setOption(type === 2, type === 2 ? [row] : this.multipleSelection)
      })
    },
    handleReverse() {
      const cur = []
      this.tbData.forEach((element, idx) => {
        element.checked = !element.checked
        if (element.checked) {
          cur.push(element)
        }
      })
      this.multipleSelection = cur
      if (this.multipleSelection.length === this.tbData.length) {
        this.$refs['xTable'].setAllCheckboxRow(true)
      }
      if (this.multipleSelection.length === 0) {
        this.$refs['xTable'].setAllCheckboxRow(false)
      }
    },
    tbFilterChange() {
      const xTable = this.$refs.xTable
      const column = xTable.getColumnByField('Type_Name')
      if (!column?.filters?.length) return
      column.filters.forEach(d => {
        d.checked = d.value === this.searchType
      })
      xTable.updateData()
    },
    getType() {
      GetPartTypeList({ Part_Grade: 0 }).then(res => {
        if (res.IsSucceed) {
          this.typeOption = res.Data.map(v => {
            return {
              label: v.Name,
              value: v.Name,
              code: v.Code
            }
          })
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    setProcessList(info) {
      this.changeProcessList(info)
    }
  }
}
</script>
<style scoped lang="scss">
.pagination-container {
  padding: 0;
  text-align: right;
}

::v-deep .el-card__body {
  display: flex;
  flex-direction: column;
}

.tb-x {
  flex: 1;
  height: 0;
  margin-bottom: 10px;
  overflow: auto;
}

.topTitle {
  font-size: 14px;
  margin: 0 0 16px;

  span {
    display: inline-block;
    width: 2px;
    height: 14px;
    background: #009dff;
    vertical-align: middle;
    margin-right: 6px;
  }
}

::v-deep .elDivder {
  margin: 10px;
}

.btn-x {
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}

.el-icon-edit {
  cursor: pointer;
}

footer {
  display: flex;
  justify-content: space-between;
}

.cs-bottom {
  position: relative;
  height: 40px;
  line-height: 40px;

  .data-info {
    position: absolute;
    bottom: 0;

    .info-x {
      margin-right: 20px;
    }
  }
}
</style>

{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-product-type\\component\\TreeData.vue?vue&type=template&id=3ccd6700&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-product-type\\component\\TreeData.vue", "mtime": 1757468128034}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}
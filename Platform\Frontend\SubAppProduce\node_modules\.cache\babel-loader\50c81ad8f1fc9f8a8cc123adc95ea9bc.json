{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue", "mtime": 1757573614454}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetBOMInfo", "GetConsumingProcessAllList", "SaveConsumingProcessAllList", "GetConsumingAllList", "SaveConsumingProcessAllList2", "GetProcessList", "data", "list", "btnLoading", "selectList", "bomList", "tabBomList", "comName", "partName", "bomActiveName", "parentBomList", "computed", "filteredList", "_this", "filter", "item", "Use_Bom_Level", "toString", "mounted", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getBom", "getParentBom", "getProcessList", "stop", "methods", "_this3", "_callee2", "_yield$GetBOMInfo", "_callee2$", "_context2", "sent", "i", "Code", "length", "getTypeList", "_this4", "then", "res", "IsSucceed", "resData", "Data", "Process_List_All", "map", "Process_List_Json", "Bom_Level", "Working_Process_Id", "Working_Process_Code", "Working_Process_Name", "push", "Process_List", "for<PERSON>ach", "newItem", "exists", "some", "existingItem", "console", "log", "$message", "message", "Message", "type", "_this5", "finally", "handleSubmit", "_this6", "success", "$emit", "error", "bomClick", "_this7", "currentIndex", "findIndex", "slice", "getFilteredSelectList", "code"], "sources": ["src/views/PRO/process-settings/management/component/PartTakeConfig.vue"], "sourcesContent": ["<template>\n  <div class=\"form-wrapper\">\n    <div class=\"form-recognition-tabs\">\n      <el-tabs v-model=\"bomActiveName\" @tab-click=\"bomClick\">\n        <el-tab-pane v-for=\"(item, index) in tabBomList\" :key=\"index\" :label=\"item.Display_Name\" :name=\"item.Code\" />\n      </el-tabs>\n    </div>\n    <div class=\"form-content\">\n      <div style=\"display: flex; margin-bottom: 16px;\">\n        <div style=\"width: 120px;\" />\n        <div style=\"flex: 1; display: flex; justify-content: flex-start; font-size: 16px; color: #333333;\">\n          <div v-for=\"(item, index) in parentBomList\" :key=\"index\" :style=\"{ width: (100 / parentBomList.length) + '%', textAlign: 'center' }\">{{ item.Display_Name }}</div>\n        </div>\n      </div>\n      <div>\n        <div v-for=\"(item, index) in filteredList\" :key=\"index\" style=\"display: flex;  justify-content: center; align-items: center; margin-bottom: 16px;\">\n          <div style=\"width: 120px; font-size: 14px; color: #333333; text-align: right;\">{{ item.Part_Type_Name }}</div>\n          <div style=\"flex: 1; display: flex; justify-content: flex-start;\">\n            <div v-for=\"bom in parentBomList\" :key=\"bom.Code\" style=\"margin-left: 12px; text-align: center;\" :style=\"{ width: (100 / parentBomList.length) + '%' }\">\n              <el-select v-model=\"item.Working_Process_Id\" clearable style=\"width: 100%;\">\n                <el-option v-for=\"op in getFilteredSelectList(bom.Code)\" :key=\"op.Id\" :label=\"op.Name\" :value=\"op.Id\" />\n              </el-select>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div class=\"form-footer\">\n      <el-button @click=\"$emit('close')\">取 消</el-button>\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport { GetConsumingProcessAllList, SaveConsumingProcessAllList, GetConsumingAllList, SaveConsumingProcessAllList2 } from '@/api/PRO/partType'\nimport { GetProcessList } from '@/api/PRO/technology-lib'\n\nexport default {\n  data() {\n    return {\n      list: [],\n      btnLoading: false,\n      selectList: [],\n      bomList: [],\n      tabBomList: [],\n      comName: '',\n      partName: '',\n      bomActiveName: '',\n      parentBomList: []\n    }\n  },\n  computed: {\n    // 根据当前选中的BOM层级过滤数据\n    filteredList() {\n      if (!this.list || !this.bomActiveName) {\n        return []\n      }\n      return this.list.filter(item => item.Use_Bom_Level.toString() === this.bomActiveName)\n    }\n  },\n  async mounted() {\n    await this.getBom()\n    await this.getParentBom()\n    await this.getProcessList()\n  },\n  methods: {\n    async getBom() {\n      const { comName, partName, list } = await GetBOMInfo()\n      this.comName = comName\n      this.partName = partName\n      this.bomList = list\n      this.tabBomList = list.filter(i => i.Code !== '-1')\n      this.bomActiveName = this.tabBomList[this.tabBomList.length - 1].Code\n    },\n    getTypeList() {\n      GetConsumingAllList({}).then(res => {\n        if (res.IsSucceed) {\n          const resData = res.Data\n          const Process_List_All = []\n          this.parentBomList.map(item => {\n            const Process_List_Json = {}\n            Process_List_Json.Bom_Level = item.Code\n            Process_List_Json.Working_Process_Id = ''\n            Process_List_Json.Working_Process_Code = ''\n            Process_List_Json.Working_Process_Name = ''\n            Process_List_All.push(Process_List_Json)\n          })\n\n          // 确保 Process_List 存在\n          if (!resData.Process_List) {\n            resData.Process_List = []\n          }\n\n          // 将 Process_List_All 中不存在于 Process_List 的数据追加到 Process_List\n          Process_List_All.forEach(newItem => {\n            // 检查 Process_List 中是否已存在相同的 Bom_Level\n            const exists = resData.Process_List.some(existingItem =>\n              existingItem.Bom_Level === newItem.Bom_Level ||\n              existingItem.Bom_Level === newItem.Bom_Level.toString()\n            )\n\n            // 如果不存在，则追加到 Process_List\n            if (!exists) {\n              resData.Process_List.push(newItem)\n            }\n          })\n\n          this.list = resData\n          console.log('this.list', this.list)\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    getProcessList() {\n      GetProcessList({ }).then(res => {\n        this.selectList = res.Data\n      }).finally(() => {\n        this.getTypeList()\n      })\n    },\n    handleSubmit() {\n      this.btnLoading = true\n      SaveConsumingProcessAllList(this.list.filter(i => i.Working_Process_Id)).then(res => {\n        if (res.IsSucceed) {\n          this.$message.success('保存成功')\n          this.$emit('close')\n        } else {\n          this.$message.error(res.Message)\n        }\n      }).finally(() => {\n        this.btnLoading = false\n      })\n    },\n    bomClick() {\n      this.getParentBom()\n    },\n    // 获取当前bom层级的所有父级bom层级信息\n    getParentBom() {\n      // 找到当前code在bomList中的索引位置\n      const currentIndex = this.bomList.findIndex(item => item.Code === this.bomActiveName)\n\n      // 如果找到了，则截取该索引之前的所有数据\n      if (currentIndex > 0) {\n        this.parentBomList = this.bomList.slice(0, currentIndex)\n      } else {\n        // 如果是第一个或者没找到，则返回空数组\n        this.parentBomList = []\n      }\n    },\n    // 根据BOM层级Code过滤selectList\n    getFilteredSelectList(code) {\n      if (!this.selectList || !code) {\n        return []\n      }\n      return this.selectList.filter(item => item.Bom_Level.toString() === code || item.Bom_Level.toString() === code)\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"~@/styles/mixin.scss\";\n.form-wrapper {\n  height: 70vh;\n  display: flex;\n  flex-direction: column;\n\n  .form-content {\n    flex: 1;\n    overflow: auto;\n    padding-right: 16px;\n    @include scrollBar;\n\n    .form-x {\n      padding-bottom: 20px;\n    }\n  }\n\n  .form-footer {\n    text-align: right;\n    flex-shrink: 0;\n    padding-top: 16px;\n    background: #fff;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA,SAAAA,UAAA;AACA,SAAAC,0BAAA,EAAAC,2BAAA,EAAAC,mBAAA,EAAAC,4BAAA;AACA,SAAAC,cAAA;AAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;MACAC,UAAA;MACAC,UAAA;MACAC,OAAA;MACAC,UAAA;MACAC,OAAA;MACAC,QAAA;MACAC,aAAA;MACAC,aAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MACA,UAAAX,IAAA,UAAAO,aAAA;QACA;MACA;MACA,YAAAP,IAAA,CAAAY,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,aAAA,CAAAC,QAAA,OAAAJ,KAAA,CAAAJ,aAAA;MAAA;IACA;EACA;EACAS,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACAT,MAAA,CAAAU,MAAA;UAAA;YAAAH,QAAA,CAAAE,IAAA;YAAA,OACAT,MAAA,CAAAW,YAAA;UAAA;YAAAJ,QAAA,CAAAE,IAAA;YAAA,OACAT,MAAA,CAAAY,cAAA;UAAA;UAAA;YAAA,OAAAL,QAAA,CAAAM,IAAA;QAAA;MAAA,GAAAT,OAAA;IAAA;EACA;EACAU,OAAA;IACAJ,MAAA,WAAAA,OAAA;MAAA,IAAAK,MAAA;MAAA,OAAAd,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAa,SAAA;QAAA,IAAAC,iBAAA,EAAA7B,OAAA,EAAAC,QAAA,EAAAN,IAAA;QAAA,OAAAmB,mBAAA,GAAAG,IAAA,UAAAa,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAX,IAAA,GAAAW,SAAA,CAAAV,IAAA;YAAA;cAAAU,SAAA,CAAAV,IAAA;cAAA,OACAjC,UAAA;YAAA;cAAAyC,iBAAA,GAAAE,SAAA,CAAAC,IAAA;cAAAhC,OAAA,GAAA6B,iBAAA,CAAA7B,OAAA;cAAAC,QAAA,GAAA4B,iBAAA,CAAA5B,QAAA;cAAAN,IAAA,GAAAkC,iBAAA,CAAAlC,IAAA;cACAgC,MAAA,CAAA3B,OAAA,GAAAA,OAAA;cACA2B,MAAA,CAAA1B,QAAA,GAAAA,QAAA;cACA0B,MAAA,CAAA7B,OAAA,GAAAH,IAAA;cACAgC,MAAA,CAAA5B,UAAA,GAAAJ,IAAA,CAAAY,MAAA,WAAA0B,CAAA;gBAAA,OAAAA,CAAA,CAAAC,IAAA;cAAA;cACAP,MAAA,CAAAzB,aAAA,GAAAyB,MAAA,CAAA5B,UAAA,CAAA4B,MAAA,CAAA5B,UAAA,CAAAoC,MAAA,MAAAD,IAAA;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAAN,IAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IACA;IACAQ,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA9C,mBAAA,KAAA+C,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA,IAAAC,OAAA,GAAAF,GAAA,CAAAG,IAAA;UACA,IAAAC,gBAAA;UACAN,MAAA,CAAAlC,aAAA,CAAAyC,GAAA,WAAApC,IAAA;YACA,IAAAqC,iBAAA;YACAA,iBAAA,CAAAC,SAAA,GAAAtC,IAAA,CAAA0B,IAAA;YACAW,iBAAA,CAAAE,kBAAA;YACAF,iBAAA,CAAAG,oBAAA;YACAH,iBAAA,CAAAI,oBAAA;YACAN,gBAAA,CAAAO,IAAA,CAAAL,iBAAA;UACA;;UAEA;UACA,KAAAJ,OAAA,CAAAU,YAAA;YACAV,OAAA,CAAAU,YAAA;UACA;;UAEA;UACAR,gBAAA,CAAAS,OAAA,WAAAC,OAAA;YACA;YACA,IAAAC,MAAA,GAAAb,OAAA,CAAAU,YAAA,CAAAI,IAAA,WAAAC,YAAA;cAAA,OACAA,YAAA,CAAAV,SAAA,KAAAO,OAAA,CAAAP,SAAA,IACAU,YAAA,CAAAV,SAAA,KAAAO,OAAA,CAAAP,SAAA,CAAApC,QAAA;YAAA,CACA;;YAEA;YACA,KAAA4C,MAAA;cACAb,OAAA,CAAAU,YAAA,CAAAD,IAAA,CAAAG,OAAA;YACA;UACA;UAEAhB,MAAA,CAAA1C,IAAA,GAAA8C,OAAA;UACAgB,OAAA,CAAAC,GAAA,cAAArB,MAAA,CAAA1C,IAAA;QACA;UACA0C,MAAA,CAAAsB,QAAA;YACAC,OAAA,EAAArB,GAAA,CAAAsB,OAAA;YACAC,IAAA;UACA;QACA;MACA;IACA;IACAtC,cAAA,WAAAA,eAAA;MAAA,IAAAuC,MAAA;MACAtE,cAAA,KAAA6C,IAAA,WAAAC,GAAA;QACAwB,MAAA,CAAAlE,UAAA,GAAA0C,GAAA,CAAAG,IAAA;MACA,GAAAsB,OAAA;QACAD,MAAA,CAAA3B,WAAA;MACA;IACA;IACA6B,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAtE,UAAA;MACAN,2BAAA,MAAAK,IAAA,CAAAY,MAAA,WAAA0B,CAAA;QAAA,OAAAA,CAAA,CAAAc,kBAAA;MAAA,IAAAT,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA0B,MAAA,CAAAP,QAAA,CAAAQ,OAAA;UACAD,MAAA,CAAAE,KAAA;QACA;UACAF,MAAA,CAAAP,QAAA,CAAAU,KAAA,CAAA9B,GAAA,CAAAsB,OAAA;QACA;MACA,GAAAG,OAAA;QACAE,MAAA,CAAAtE,UAAA;MACA;IACA;IACA0E,QAAA,WAAAA,SAAA;MACA,KAAA/C,YAAA;IACA;IACA;IACAA,YAAA,WAAAA,aAAA;MAAA,IAAAgD,MAAA;MACA;MACA,IAAAC,YAAA,QAAA1E,OAAA,CAAA2E,SAAA,WAAAjE,IAAA;QAAA,OAAAA,IAAA,CAAA0B,IAAA,KAAAqC,MAAA,CAAArE,aAAA;MAAA;;MAEA;MACA,IAAAsE,YAAA;QACA,KAAArE,aAAA,QAAAL,OAAA,CAAA4E,KAAA,IAAAF,YAAA;MACA;QACA;QACA,KAAArE,aAAA;MACA;IACA;IACA;IACAwE,qBAAA,WAAAA,sBAAAC,IAAA;MACA,UAAA/E,UAAA,KAAA+E,IAAA;QACA;MACA;MACA,YAAA/E,UAAA,CAAAU,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAsC,SAAA,CAAApC,QAAA,OAAAkE,IAAA,IAAApE,IAAA,CAAAsC,SAAA,CAAApC,QAAA,OAAAkE,IAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue", "mtime": 1757557227032}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetBOMInfo", "GetConsumingProcessAllList", "SaveConsumingProcessAllList", "GetProcessList", "data", "list", "btnLoading", "selectList", "bomList", "comName", "partName", "bomActiveName", "mounted", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getBom", "getProcessList", "stop", "methods", "_this2", "_callee2", "_yield$GetBOMInfo", "_callee2$", "_context2", "sent", "filter", "i", "Code", "getTypeList", "_this3", "then", "res", "IsSucceed", "Data", "$message", "message", "Message", "type", "_this4", "finally", "handleSubmit", "_this5", "Working_Process_Id", "success", "$emit", "error", "mainBlur", "e"], "sources": ["src/views/PRO/process-settings/management/component/PartTakeConfig.vue"], "sourcesContent": ["<template>\n  <div class=\"form-wrapper\">\n    <div class=\"form-recognition-tabs\">\n      <el-tabs v-model=\"bomActiveName\">\n        <el-tab-pane v-for=\"(item, index) in bomList\" :key=\"index\" :label=\"item.Display_Name\" :name=\"item.Code\" />\n      </el-tabs>\n    </div>\n    <div class=\"form-content\">\n      <div style=\"display: flex;\">\n        <div style=\"width: 120px;\" />\n        <div style=\"flex: 1; display: flex; font-size: 16px; color: #333333;\">\n          <div style=\"flex: 1; text-align: center;\">构件</div>\n          <div style=\"flex: 1; text-align: center;\">半成品</div>\n          <div style=\"flex: 1; text-align: center;\">成品</div>\n        </div>\n      </div>\n      <div>\n        <div v-for=\"(item, index) in list\" :key=\"index\" style=\"display: flex;  justify-content: center; align-items: center; margin-bottom: 16px;\">\n          <div style=\"width: 120px; font-size: 14px; color: #333333; text-align: right; padding-right: 10px;\">{{ item.Part_Type_Name }}</div>\n          <div style=\"flex: 1; display: flex;\">\n            <div style=\"flex: 1; text-align: center;\">\n              <el-select v-model=\"item.Working_Process_Id\" clearable>\n                <el-option v-for=\"op in selectList\" :key=\"op.Id\" :label=\"op.Name\" :value=\"op.Id\" />\n              </el-select>\n            </div>\n            <div style=\"flex: 1; text-align: center;\">\n              <el-select v-model=\"item.Working_Process_Id\" clearable>\n                <el-option v-for=\"op in selectList\" :key=\"op.Id\" :label=\"op.Name\" :value=\"op.Id\" />\n              </el-select>\n            </div>\n            <div style=\"flex: 1; text-align: center;\">\n              <el-select v-model=\"item.Working_Process_Id\" clearable>\n                <el-option v-for=\"op in selectList\" :key=\"op.Id\" :label=\"op.Name\" :value=\"op.Id\" />\n              </el-select>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div class=\"form-footer\">\n      <el-button @click=\"$emit('close')\">取 消</el-button>\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport { GetConsumingProcessAllList, SaveConsumingProcessAllList } from '@/api/PRO/partType'\nimport { GetProcessList } from '@/api/PRO/technology-lib'\n\nexport default {\n  data() {\n    return {\n      list: [],\n      btnLoading: false,\n      selectList: [],\n      bomList: [],\n      comName: '',\n      partName: '',\n      bomActiveName: ''\n    }\n  },\n  async mounted() {\n    await this.getBom()\n    await this.getProcessList()\n  },\n  methods: {\n    async getBom() {\n      const { comName, partName, list } = await GetBOMInfo()\n      this.comName = comName\n      this.partName = partName\n      this.bomList = list.filter(i => i.Code !== '-1')\n      this.bomActiveName = this.bomList[0].Code\n    },\n    getTypeList() {\n      GetConsumingProcessAllList({}).then(res => {\n        if (res.IsSucceed) {\n          this.list = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    getProcessList() {\n      GetProcessList({ type: 1 }).then(res => {\n        this.selectList = res.Data\n      }).finally(() => {\n        this.getTypeList()\n      })\n    },\n    handleSubmit() {\n      this.btnLoading = true\n      SaveConsumingProcessAllList(this.list.filter(i => i.Working_Process_Id)).then(res => {\n        if (res.IsSucceed) {\n          this.$message.success('保存成功')\n          this.$emit('close')\n        } else {\n          this.$message.error(res.Message)\n        }\n      }).finally(() => {\n        this.btnLoading = false\n      })\n    },\n    mainBlur(e) {\n\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"~@/styles/mixin.scss\";\n.form-wrapper {\n  height: 70vh;\n  display: flex;\n  flex-direction: column;\n\n  .form-content {\n    flex: 1;\n    overflow: auto;\n    padding-right: 16px;\n    @include scrollBar;\n\n    .form-x {\n      padding-bottom: 20px;\n    }\n  }\n\n  .form-footer {\n    text-align: right;\n    flex-shrink: 0;\n    padding-top: 16px;\n    background: #fff;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA,SAAAA,UAAA;AACA,SAAAC,0BAAA,EAAAC,2BAAA;AACA,SAAAC,cAAA;AAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;MACAC,UAAA;MACAC,UAAA;MACAC,OAAA;MACAC,OAAA;MACAC,QAAA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACAT,KAAA,CAAAU,MAAA;UAAA;YAAAH,QAAA,CAAAE,IAAA;YAAA,OACAT,KAAA,CAAAW,cAAA;UAAA;UAAA;YAAA,OAAAJ,QAAA,CAAAK,IAAA;QAAA;MAAA,GAAAR,OAAA;IAAA;EACA;EACAS,OAAA;IACAH,MAAA,WAAAA,OAAA;MAAA,IAAAI,MAAA;MAAA,OAAAb,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAY,SAAA;QAAA,IAAAC,iBAAA,EAAApB,OAAA,EAAAC,QAAA,EAAAL,IAAA;QAAA,OAAAU,mBAAA,GAAAG,IAAA,UAAAY,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAV,IAAA,GAAAU,SAAA,CAAAT,IAAA;YAAA;cAAAS,SAAA,CAAAT,IAAA;cAAA,OACAtB,UAAA;YAAA;cAAA6B,iBAAA,GAAAE,SAAA,CAAAC,IAAA;cAAAvB,OAAA,GAAAoB,iBAAA,CAAApB,OAAA;cAAAC,QAAA,GAAAmB,iBAAA,CAAAnB,QAAA;cAAAL,IAAA,GAAAwB,iBAAA,CAAAxB,IAAA;cACAsB,MAAA,CAAAlB,OAAA,GAAAA,OAAA;cACAkB,MAAA,CAAAjB,QAAA,GAAAA,QAAA;cACAiB,MAAA,CAAAnB,OAAA,GAAAH,IAAA,CAAA4B,MAAA,WAAAC,CAAA;gBAAA,OAAAA,CAAA,CAAAC,IAAA;cAAA;cACAR,MAAA,CAAAhB,aAAA,GAAAgB,MAAA,CAAAnB,OAAA,IAAA2B,IAAA;YAAA;YAAA;cAAA,OAAAJ,SAAA,CAAAN,IAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IACA;IACAQ,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACApC,0BAAA,KAAAqC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAH,MAAA,CAAAhC,IAAA,GAAAkC,GAAA,CAAAE,IAAA;QACA;UACAJ,MAAA,CAAAK,QAAA;YACAC,OAAA,EAAAJ,GAAA,CAAAK,OAAA;YACAC,IAAA;UACA;QACA;MACA;IACA;IACArB,cAAA,WAAAA,eAAA;MAAA,IAAAsB,MAAA;MACA3C,cAAA;QAAA0C,IAAA;MAAA,GAAAP,IAAA,WAAAC,GAAA;QACAO,MAAA,CAAAvC,UAAA,GAAAgC,GAAA,CAAAE,IAAA;MACA,GAAAM,OAAA;QACAD,MAAA,CAAAV,WAAA;MACA;IACA;IACAY,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAA3C,UAAA;MACAJ,2BAAA,MAAAG,IAAA,CAAA4B,MAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAgB,kBAAA;MAAA,IAAAZ,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAS,MAAA,CAAAP,QAAA,CAAAS,OAAA;UACAF,MAAA,CAAAG,KAAA;QACA;UACAH,MAAA,CAAAP,QAAA,CAAAW,KAAA,CAAAd,GAAA,CAAAK,OAAA;QACA;MACA,GAAAG,OAAA;QACAE,MAAA,CAAA3C,UAAA;MACA;IACA;IACAgD,QAAA,WAAAA,SAAAC,CAAA,GAEA;EACA;AACA", "ignoreList": []}]}
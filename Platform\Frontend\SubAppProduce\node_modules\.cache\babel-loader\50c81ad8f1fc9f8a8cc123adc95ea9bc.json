{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue", "mtime": 1757573501486}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetBOMInfo", "GetConsumingProcessAllList", "SaveConsumingProcessAllList", "GetConsumingAllList", "SaveConsumingProcessAllList2", "GetProcessList", "data", "list", "btnLoading", "selectList", "bomList", "tabBomList", "comName", "partName", "bomActiveName", "parentBomList", "computed", "filteredList", "_this", "filter", "item", "Use_Bom_Level", "toString", "mounted", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getBom", "getParentBom", "getProcessList", "stop", "methods", "_this3", "_callee2", "_yield$GetBOMInfo", "_callee2$", "_context2", "sent", "i", "Code", "length", "getTypeList", "_this4", "then", "res", "IsSucceed", "resData", "Data", "Process_List_All", "map", "Process_List_Json", "Bom_Level", "Working_Process_Id", "Working_Process_Code", "Working_Process_Name", "Process_List", "push", "console", "log", "$message", "message", "Message", "type", "_this5", "finally", "handleSubmit", "_this6", "success", "$emit", "error", "bomClick", "_this7", "currentIndex", "findIndex", "slice", "getFilteredSelectList", "code"], "sources": ["src/views/PRO/process-settings/management/component/PartTakeConfig.vue"], "sourcesContent": ["<template>\n  <div class=\"form-wrapper\">\n    <div class=\"form-recognition-tabs\">\n      <el-tabs v-model=\"bomActiveName\" @tab-click=\"bomClick\">\n        <el-tab-pane v-for=\"(item, index) in tabBomList\" :key=\"index\" :label=\"item.Display_Name\" :name=\"item.Code\" />\n      </el-tabs>\n    </div>\n    <div class=\"form-content\">\n      <div style=\"display: flex; margin-bottom: 16px;\">\n        <div style=\"width: 120px;\" />\n        <div style=\"flex: 1; display: flex; justify-content: flex-start; font-size: 16px; color: #333333;\">\n          <div v-for=\"(item, index) in parentBomList\" :key=\"index\" :style=\"{ width: (100 / parentBomList.length) + '%', textAlign: 'center' }\">{{ item.Display_Name }}</div>\n        </div>\n      </div>\n      <div>\n        <div v-for=\"(item, index) in filteredList\" :key=\"index\" style=\"display: flex;  justify-content: center; align-items: center; margin-bottom: 16px;\">\n          <div style=\"width: 120px; font-size: 14px; color: #333333; text-align: right;\">{{ item.Part_Type_Name }}</div>\n          <div style=\"flex: 1; display: flex; justify-content: flex-start;\">\n            <div v-for=\"bom in parentBomList\" :key=\"bom.Code\" style=\"margin-left: 12px; text-align: center;\" :style=\"{ width: (100 / parentBomList.length) + '%' }\">\n              <el-select v-model=\"item.Working_Process_Id\" clearable style=\"width: 100%;\">\n                <el-option v-for=\"op in getFilteredSelectList(bom.Code)\" :key=\"op.Id\" :label=\"op.Name\" :value=\"op.Id\" />\n              </el-select>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div class=\"form-footer\">\n      <el-button @click=\"$emit('close')\">取 消</el-button>\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport { GetConsumingProcessAllList, SaveConsumingProcessAllList, GetConsumingAllList, SaveConsumingProcessAllList2 } from '@/api/PRO/partType'\nimport { GetProcessList } from '@/api/PRO/technology-lib'\n\nexport default {\n  data() {\n    return {\n      list: [],\n      btnLoading: false,\n      selectList: [],\n      bomList: [],\n      tabBomList: [],\n      comName: '',\n      partName: '',\n      bomActiveName: '',\n      parentBomList: []\n    }\n  },\n  computed: {\n    // 根据当前选中的BOM层级过滤数据\n    filteredList() {\n      if (!this.list || !this.bomActiveName) {\n        return []\n      }\n      return this.list.filter(item => item.Use_Bom_Level.toString() === this.bomActiveName)\n    }\n  },\n  async mounted() {\n    await this.getBom()\n    await this.getParentBom()\n    await this.getProcessList()\n  },\n  methods: {\n    async getBom() {\n      const { comName, partName, list } = await GetBOMInfo()\n      this.comName = comName\n      this.partName = partName\n      this.bomList = list\n      this.tabBomList = list.filter(i => i.Code !== '-1')\n      this.bomActiveName = this.tabBomList[this.tabBomList.length - 1].Code\n    },\n    getTypeList() {\n      GetConsumingAllList({}).then(res => {\n        if (res.IsSucceed) {\n          const resData = res.Data\n          const Process_List_All = []\n          this.parentBomList.map(item => {\n            const Process_List_Json = {}\n            Process_List_Json.Bom_Level = item.Code\n            Process_List_Json.Working_Process_Id = ''\n            Process_List_Json.Working_Process_Code = ''\n            Process_List_Json.Working_Process_Name = ''\n            Process_List.push(Process_List_Json)\n          })\n          this.list = resData\n          console.log('this.list', this.list)\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    getProcessList() {\n      GetProcessList({ }).then(res => {\n        this.selectList = res.Data\n      }).finally(() => {\n        this.getTypeList()\n      })\n    },\n    handleSubmit() {\n      this.btnLoading = true\n      SaveConsumingProcessAllList(this.list.filter(i => i.Working_Process_Id)).then(res => {\n        if (res.IsSucceed) {\n          this.$message.success('保存成功')\n          this.$emit('close')\n        } else {\n          this.$message.error(res.Message)\n        }\n      }).finally(() => {\n        this.btnLoading = false\n      })\n    },\n    bomClick() {\n      this.getParentBom()\n    },\n    // 获取当前bom层级的所有父级bom层级信息\n    getParentBom() {\n      // 找到当前code在bomList中的索引位置\n      const currentIndex = this.bomList.findIndex(item => item.Code === this.bomActiveName)\n\n      // 如果找到了，则截取该索引之前的所有数据\n      if (currentIndex > 0) {\n        this.parentBomList = this.bomList.slice(0, currentIndex)\n      } else {\n        // 如果是第一个或者没找到，则返回空数组\n        this.parentBomList = []\n      }\n    },\n    // 根据BOM层级Code过滤selectList\n    getFilteredSelectList(code) {\n      if (!this.selectList || !code) {\n        return []\n      }\n      return this.selectList.filter(item => item.Bom_Level.toString() === code || item.Bom_Level.toString() === code)\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"~@/styles/mixin.scss\";\n.form-wrapper {\n  height: 70vh;\n  display: flex;\n  flex-direction: column;\n\n  .form-content {\n    flex: 1;\n    overflow: auto;\n    padding-right: 16px;\n    @include scrollBar;\n\n    .form-x {\n      padding-bottom: 20px;\n    }\n  }\n\n  .form-footer {\n    text-align: right;\n    flex-shrink: 0;\n    padding-top: 16px;\n    background: #fff;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA,SAAAA,UAAA;AACA,SAAAC,0BAAA,EAAAC,2BAAA,EAAAC,mBAAA,EAAAC,4BAAA;AACA,SAAAC,cAAA;AAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;MACAC,UAAA;MACAC,UAAA;MACAC,OAAA;MACAC,UAAA;MACAC,OAAA;MACAC,QAAA;MACAC,aAAA;MACAC,aAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MACA,UAAAX,IAAA,UAAAO,aAAA;QACA;MACA;MACA,YAAAP,IAAA,CAAAY,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,aAAA,CAAAC,QAAA,OAAAJ,KAAA,CAAAJ,aAAA;MAAA;IACA;EACA;EACAS,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACAT,MAAA,CAAAU,MAAA;UAAA;YAAAH,QAAA,CAAAE,IAAA;YAAA,OACAT,MAAA,CAAAW,YAAA;UAAA;YAAAJ,QAAA,CAAAE,IAAA;YAAA,OACAT,MAAA,CAAAY,cAAA;UAAA;UAAA;YAAA,OAAAL,QAAA,CAAAM,IAAA;QAAA;MAAA,GAAAT,OAAA;IAAA;EACA;EACAU,OAAA;IACAJ,MAAA,WAAAA,OAAA;MAAA,IAAAK,MAAA;MAAA,OAAAd,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAa,SAAA;QAAA,IAAAC,iBAAA,EAAA7B,OAAA,EAAAC,QAAA,EAAAN,IAAA;QAAA,OAAAmB,mBAAA,GAAAG,IAAA,UAAAa,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAX,IAAA,GAAAW,SAAA,CAAAV,IAAA;YAAA;cAAAU,SAAA,CAAAV,IAAA;cAAA,OACAjC,UAAA;YAAA;cAAAyC,iBAAA,GAAAE,SAAA,CAAAC,IAAA;cAAAhC,OAAA,GAAA6B,iBAAA,CAAA7B,OAAA;cAAAC,QAAA,GAAA4B,iBAAA,CAAA5B,QAAA;cAAAN,IAAA,GAAAkC,iBAAA,CAAAlC,IAAA;cACAgC,MAAA,CAAA3B,OAAA,GAAAA,OAAA;cACA2B,MAAA,CAAA1B,QAAA,GAAAA,QAAA;cACA0B,MAAA,CAAA7B,OAAA,GAAAH,IAAA;cACAgC,MAAA,CAAA5B,UAAA,GAAAJ,IAAA,CAAAY,MAAA,WAAA0B,CAAA;gBAAA,OAAAA,CAAA,CAAAC,IAAA;cAAA;cACAP,MAAA,CAAAzB,aAAA,GAAAyB,MAAA,CAAA5B,UAAA,CAAA4B,MAAA,CAAA5B,UAAA,CAAAoC,MAAA,MAAAD,IAAA;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAAN,IAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IACA;IACAQ,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA9C,mBAAA,KAAA+C,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA,IAAAC,OAAA,GAAAF,GAAA,CAAAG,IAAA;UACA,IAAAC,gBAAA;UACAN,MAAA,CAAAlC,aAAA,CAAAyC,GAAA,WAAApC,IAAA;YACA,IAAAqC,iBAAA;YACAA,iBAAA,CAAAC,SAAA,GAAAtC,IAAA,CAAA0B,IAAA;YACAW,iBAAA,CAAAE,kBAAA;YACAF,iBAAA,CAAAG,oBAAA;YACAH,iBAAA,CAAAI,oBAAA;YACAC,YAAA,CAAAC,IAAA,CAAAN,iBAAA;UACA;UACAR,MAAA,CAAA1C,IAAA,GAAA8C,OAAA;UACAW,OAAA,CAAAC,GAAA,cAAAhB,MAAA,CAAA1C,IAAA;QACA;UACA0C,MAAA,CAAAiB,QAAA;YACAC,OAAA,EAAAhB,GAAA,CAAAiB,OAAA;YACAC,IAAA;UACA;QACA;MACA;IACA;IACAjC,cAAA,WAAAA,eAAA;MAAA,IAAAkC,MAAA;MACAjE,cAAA,KAAA6C,IAAA,WAAAC,GAAA;QACAmB,MAAA,CAAA7D,UAAA,GAAA0C,GAAA,CAAAG,IAAA;MACA,GAAAiB,OAAA;QACAD,MAAA,CAAAtB,WAAA;MACA;IACA;IACAwB,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAjE,UAAA;MACAN,2BAAA,MAAAK,IAAA,CAAAY,MAAA,WAAA0B,CAAA;QAAA,OAAAA,CAAA,CAAAc,kBAAA;MAAA,IAAAT,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAqB,MAAA,CAAAP,QAAA,CAAAQ,OAAA;UACAD,MAAA,CAAAE,KAAA;QACA;UACAF,MAAA,CAAAP,QAAA,CAAAU,KAAA,CAAAzB,GAAA,CAAAiB,OAAA;QACA;MACA,GAAAG,OAAA;QACAE,MAAA,CAAAjE,UAAA;MACA;IACA;IACAqE,QAAA,WAAAA,SAAA;MACA,KAAA1C,YAAA;IACA;IACA;IACAA,YAAA,WAAAA,aAAA;MAAA,IAAA2C,MAAA;MACA;MACA,IAAAC,YAAA,QAAArE,OAAA,CAAAsE,SAAA,WAAA5D,IAAA;QAAA,OAAAA,IAAA,CAAA0B,IAAA,KAAAgC,MAAA,CAAAhE,aAAA;MAAA;;MAEA;MACA,IAAAiE,YAAA;QACA,KAAAhE,aAAA,QAAAL,OAAA,CAAAuE,KAAA,IAAAF,YAAA;MACA;QACA;QACA,KAAAhE,aAAA;MACA;IACA;IACA;IACAmE,qBAAA,WAAAA,sBAAAC,IAAA;MACA,UAAA1E,UAAA,KAAA0E,IAAA;QACA;MACA;MACA,YAAA1E,UAAA,CAAAU,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAsC,SAAA,CAAApC,QAAA,OAAA6D,IAAA,IAAA/D,IAAA,CAAAsC,SAAA,CAAApC,QAAA,OAAA6D,IAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}
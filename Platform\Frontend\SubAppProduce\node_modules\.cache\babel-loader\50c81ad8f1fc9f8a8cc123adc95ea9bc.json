{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\PartTakeConfig.vue", "mtime": 1757468113428}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsdGVyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5maWx0ZXIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IEdldENvbnN1bWluZ1Byb2Nlc3NBbGxMaXN0LCBTYXZlQ29uc3VtaW5nUHJvY2Vzc0FsbExpc3QgfSBmcm9tICdAL2FwaS9QUk8vcGFydFR5cGUnOwppbXBvcnQgeyBHZXRQcm9jZXNzTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy90ZWNobm9sb2d5LWxpYic7CmV4cG9ydCBkZWZhdWx0IHsKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbGlzdDogW10sCiAgICAgIGJ0bkxvYWRpbmc6IGZhbHNlLAogICAgICBzZWxlY3RMaXN0OiBbXQogICAgfTsKICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7CiAgICB0aGlzLmdldFByb2Nlc3NMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXRUeXBlTGlzdDogZnVuY3Rpb24gZ2V0VHlwZUxpc3QoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIEdldENvbnN1bWluZ1Byb2Nlc3NBbGxMaXN0KHt9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgX3RoaXMubGlzdCA9IHJlcy5EYXRhOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGdldFByb2Nlc3NMaXN0OiBmdW5jdGlvbiBnZXRQcm9jZXNzTGlzdCgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIEdldFByb2Nlc3NMaXN0KHsKICAgICAgICB0eXBlOiAxCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzMi5zZWxlY3RMaXN0ID0gcmVzLkRhdGE7CiAgICAgIH0pLmZpbmFsbHkoZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMi5nZXRUeXBlTGlzdCgpOwogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVTdWJtaXQ6IGZ1bmN0aW9uIGhhbmRsZVN1Ym1pdCgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHRoaXMuYnRuTG9hZGluZyA9IHRydWU7CiAgICAgIFNhdmVDb25zdW1pbmdQcm9jZXNzQWxsTGlzdCh0aGlzLmxpc3QuZmlsdGVyKGZ1bmN0aW9uIChpKSB7CiAgICAgICAgcmV0dXJuIGkuV29ya2luZ19Qcm9jZXNzX0lkOwogICAgICB9KSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIF90aGlzMy4kbWVzc2FnZS5zdWNjZXNzKCfkv53lrZjmiJDlip8nKTsKICAgICAgICAgIF90aGlzMy4kZW1pdCgnY2xvc2UnKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXMzLiRtZXNzYWdlLmVycm9yKHJlcy5NZXNzYWdlKTsKICAgICAgICB9CiAgICAgIH0pLmZpbmFsbHkoZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMy5idG5Mb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIG1haW5CbHVyOiBmdW5jdGlvbiBtYWluQmx1cihlKSB7fQogIH0KfTs="}, {"version": 3, "names": ["GetConsumingProcessAllList", "SaveConsumingProcessAllList", "GetProcessList", "data", "list", "btnLoading", "selectList", "mounted", "getProcessList", "methods", "getTypeList", "_this", "then", "res", "IsSucceed", "Data", "$message", "message", "Message", "type", "_this2", "finally", "handleSubmit", "_this3", "filter", "i", "Working_Process_Id", "success", "$emit", "error", "mainBlur", "e"], "sources": ["src/views/PRO/process-settings/management/component/PartTakeConfig.vue"], "sourcesContent": ["<template>\r\n  <div class=\"form-wrapper\">\r\n    <div class=\"form-content\">\r\n      <el-form ref=\"form\" label-width=\"120px\" class=\"form-x\">\r\n        <el-form-item v-for=\"(item,index) in list\" :key=\"index\" :show-message=\"false\" :label=\"item.Part_Type_Name\" prop=\"mainPart\">\r\n          <el-select v-model=\"item.Working_Process_Id\" clearable>\r\n            <el-option v-for=\"op in selectList\" :key=\"op.Id\" :label=\"op.Name\" :value=\"op.Id\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n    <div class=\"form-footer\">\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { GetConsumingProcessAllList, SaveConsumingProcessAllList } from '@/api/PRO/partType'\r\nimport { GetProcessList } from '@/api/PRO/technology-lib'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      list: [],\r\n      btnLoading: false,\r\n      selectList: []\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getProcessList()\r\n  },\r\n  methods: {\r\n    getTypeList() {\r\n      GetConsumingProcessAllList({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.list = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getProcessList() {\r\n      GetProcessList({ type: 1 }).then(res => {\r\n        this.selectList = res.Data\r\n      }).finally(() => {\r\n        this.getTypeList()\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      this.btnLoading = true\r\n      SaveConsumingProcessAllList(this.list.filter(i => i.Working_Process_Id)).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message.success('保存成功')\r\n          this.$emit('close')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      }).finally(() => {\r\n        this.btnLoading = false\r\n      })\r\n    },\r\n    mainBlur(e) {\r\n\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"~@/styles/mixin.scss\";\r\n.form-wrapper {\r\n  height: 70vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .form-content {\r\n    flex: 1;\r\n    overflow: auto;\r\n    padding-right: 16px;\r\n    @include scrollBar;\r\n\r\n    .form-x {\r\n      padding-bottom: 20px;\r\n    }\r\n  }\r\n\r\n  .form-footer {\r\n    text-align: right;\r\n    flex-shrink: 0;\r\n    padding-top: 16px;\r\n    background: #fff;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAoBA,SAAAA,0BAAA,EAAAC,2BAAA;AACA,SAAAC,cAAA;AAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;MACAC,UAAA;MACAC,UAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,KAAA;MACAX,0BAAA,KAAAY,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAH,KAAA,CAAAP,IAAA,GAAAS,GAAA,CAAAE,IAAA;QACA;UACAJ,KAAA,CAAAK,QAAA;YACAC,OAAA,EAAAJ,GAAA,CAAAK,OAAA;YACAC,IAAA;UACA;QACA;MACA;IACA;IACAX,cAAA,WAAAA,eAAA;MAAA,IAAAY,MAAA;MACAlB,cAAA;QAAAiB,IAAA;MAAA,GAAAP,IAAA,WAAAC,GAAA;QACAO,MAAA,CAAAd,UAAA,GAAAO,GAAA,CAAAE,IAAA;MACA,GAAAM,OAAA;QACAD,MAAA,CAAAV,WAAA;MACA;IACA;IACAY,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAlB,UAAA;MACAJ,2BAAA,MAAAG,IAAA,CAAAoB,MAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,kBAAA;MAAA,IAAAd,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAS,MAAA,CAAAP,QAAA,CAAAW,OAAA;UACAJ,MAAA,CAAAK,KAAA;QACA;UACAL,MAAA,CAAAP,QAAA,CAAAa,KAAA,CAAAhB,GAAA,CAAAK,OAAA;QACA;MACA,GAAAG,OAAA;QACAE,MAAA,CAAAlB,UAAA;MACA;IACA;IACAyB,QAAA,WAAAA,SAAAC,CAAA,GAEA;EACA;AACA", "ignoreList": []}]}
// 工艺选择列表
import request from '@/utils/request'
import qs from 'qs'

// 工艺选择列表 (Auth)
export function GetLibList(data) {
  return request({
    url: '/PRO/TechnologyLib/GetLibList',
    method: 'post',
    data
  })
}

// 添加工序 (Auth)
export function AddWorkingProcess(data) {
  return request({
    url: '/PRO/TechnologyLib/AddWorkingProcess',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 添加工艺 (Auth)
export function AddTechnology(data) {
  return request({
    url: '/PRO/TechnologyLib/AddTechnology',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 添加工艺下的工序流程 (Auth)
export function AddProcessFlow(data) {
  return request({
    url: '/PRO/TechnologyLib/AddProcessFlow',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 工序列表（不分页） (Auth)
export function GetProcessList(data) {
  return request({
    url: '/PRO/TechnologyLib/GetProcessList',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 工序列表（不分页） (Auth)
export function GetProcessListBase(data) {
  return request({
    url: '/PRO/TechnologyLib/GetProcessListBase',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 所有工序列表（不分页） (Auth)
export function GetAllProcessList(data) {
  return request({
    url: '/PRO/TechnologyLib/GetAllProcessList',
    method: 'post',
    data: qs.stringify(data)
  })
}
// 当前工厂的所有工序列表 (Auth)
export function GetFactoryAllProcessList(data) {
  return request({
    url: '/PRO/TechnologyLib/GetFactoryAllProcessList',
    method: 'post',
    data: data
  })
}

// 得到工艺下的工序流程 (Auth)
export function GetProcessFlow(data) {
  return request({
    url: '/PRO/TechnologyLib/GetProcessFlow',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function GetProcessFlowListWithTechnology(data) {
  return request({
    url: '/PRO/TechnologyLib/GetProcessFlowListWithTechnology',
    method: 'post',
    data
  })
}

// 更新工序下的班组 (Auth)
export function UpdateProcessTeam(data) {
  return request({
    url: '/PRO/TechnologyLib/UpdateProcessTeam',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 得到工厂下的班组 (Aut
export function GetFactoryWorkingTeam() {
  return request({
    url: '/PRO/TechnologyLib/GetFactoryWorkingTeam',
    method: 'post'
  })
}

// 得到工序下的班组 (Aut
export function GetWorkingTeam(data) {
  return request({
    url: '/PRO/TechnologyLib/GetWorkingTeam',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 指定班组下的工序列表(默认情况下一个人只能在一个班组) (Auth)
export function GetTeamProcessList(data) {
  return request({
    url: '/PRO/TechnologyLib/GetTeamProcessList',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 获取详细的检查项组合列表 (Auth)
export function GetGroupItemsList(data) {
  return request({
    url: '/PRO/TechnologyLib/GetGroupItemsList',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 删除工艺下的工序流程 (Auth)
export function DeleteProcessFlow(data) {
  return request({
    url: '/PRO/TechnologyLib/DeleteProcessFlow',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 删除工艺 (Auth)
export function DeleteTechnology(data) {
  return request({
    url: '/PRO/TechnologyLib/DeleteTechnology',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 删除工艺 (Auth)
export function DeleteProcess(data) {
  return request({
    url: '/PRO/TechnologyLib/DeleteProcess',
    method: 'post',
    data
  })
}

// 得到工厂所有班组 (Auth)
export function GetWorkingTeams(data) {
  return request({
    url: '/PRO/TechnologyLib/GetWorkingTeams',
    method: 'post',
    data
  })
}
// 得到工厂所有班组分页
export function GetWorkingTeamsPageList(data) {
  return request({
    url: '/PRO/TechnologyLib/GetWorkingTeamsPageList',
    method: 'post',
    data
  })
}
// 保存班组
export function SaveWorkingTeams(data) {
  return request({
    url: '/PRO/TechnologyLib/SaveWorkingTeams',
    method: 'post',
    data
  })
}

// 删除班组
export function DeleteWorkingTeams(data) {
  return request({
    url: '/PRO/TechnologyLib/DeleteWorkingTeams',
    method: 'post',
    data
  })
}

// 班组信息
export function GetWorkingTeamInfo(data) {
  return request({
    url: '/PRO/TechnologyLib/GetWorkingTeamInfo',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 班组信息
export function GetWorkingTeamBase(data) {
  return request({
    url: '/PRO/TechnologyLib/GetWorkingTeamBase',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 班组信息
export function GetProcessListTeamBase(data) {
  return request({
    url: '/PRO/TechnologyLib/GetProcessListTeamBase',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function GetProcessListWithUserBase(data) {
  return request({
    url: '/PRO/TechnologyLib/GetProcessListWithUserBase',
    method: 'post',
    data
  })
}

// 班组长信息
export function GetFactoryPeoplelist(data) {
  return request({
    url: '/PRO/Factory/GetFactoryPeoplelist',
    method: 'post',
    data
  })
}

// 检查项组合列表
export function GetCheckGroupList(data) {
  return request({
    url: '/PRO/Inspection/GetCheckGroupList',
    method: 'post',
    data
  })
}
// 添加工艺工序流程
export function AddProessLib(data) {
  return request({
    url: '/PRO/TechnologyLib/AddProessLib',
    method: 'post',
    data
  })
}
// 获取构建子类
export function GetChildComponentTypeList(data) {
  return request({
    url: '/PRO/ComponentType/GetChildComponentTypeList',
    method: 'post',
    data
  })
}
// 删除工艺工序
export function DelLib(data) {
  return request({
    url: '/PRO/TechnologyLib/DelLib',
    method: 'post',
    data
  })
}

export function GetLibListType(data) {
  return request({
    url: '/PRO/TechnologyLib/GetLibListType',
    method: 'post',
    data
  })
}

export function GetProcessWorkingTeamBase(data) {
  return request({
    url: '/PRO/TechnologyLib/GetProcessWorkingTeamBase',
    method: 'post',
    data
  })
}

export function GetTeamListByUser(data) {
  return request({
    url: '/PRO/TechnologyLib/GetTeamListByUser',
    method: 'post',
    data
  })
}
export function GetEquipmentAssetPageList(data) {
  return request({
    url: '/DF/EQPTAsset/V2/GetEquipmentAssetPageList',
    method: 'post',
    data
  })
}

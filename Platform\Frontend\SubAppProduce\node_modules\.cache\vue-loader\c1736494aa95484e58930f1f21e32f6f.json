{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\production-execution\\new-report\\home.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\production-execution\\new-report\\home.vue", "mtime": 1758266753126}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgRXhwYW5kYWJsZVNlY3Rpb24gZnJvbSAnQC9jb21wb25lbnRzL0V4cGFuZGFibGVTZWN0aW9uL2luZGV4LnZ1ZScNCmltcG9ydCBQYWdpbmF0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9QYWdpbmF0aW9uJw0KaW1wb3J0IFBhcnREZXRhaWwgZnJvbSAnLi9jb21wb25lbnRzL1BhcnREZXRhaWwnDQppbXBvcnQgeyB0YWJsZVBhZ2VTaXplIH0gZnJvbSAnQC92aWV3cy9QUk8vc2V0dGluZycNCmltcG9ydCBnZXRUYkluZm8gZnJvbSAnQC9taXhpbnMvUFJPL2dldC10YWJsZS1pbmZvJw0KaW1wb3J0IENvbVJlcG9ydCBmcm9tICcuL2NvbXBvbmVudHMvQ29tUmVwb3J0Jw0KaW1wb3J0IFBhcnRSZXBvcnQgZnJvbSAnLi9jb21wb25lbnRzL1BhcnRSZXBvcnQudnVlJw0KaW1wb3J0IFRyZWVEZXRhaWwgZnJvbSAnQC9jb21wb25lbnRzL1RyZWVEZXRhaWwvaW5kZXgudnVlJw0KaW1wb3J0IFFpdGFvIGZyb20gJy4vY29tcG9uZW50cy9RaXRhbycNCmltcG9ydCB7IEdldFByb2plY3RBcmVhVHJlZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vcHJvamVjdCcNCmltcG9ydCB7IEdldFRlYW1MaXN0QnlVc2VyIH0gZnJvbSAnQC9hcGkvUFJPL3RlY2hub2xvZ3ktbGliJw0KaW1wb3J0IHsNCiAgR2V0Q29tcFRhc2tQYWdlTGlzdCwNCiAgR2V0Q29tcFRhc2tQYXJ0Q29tcGxldGlvblN0b2NrLA0KICBHZXREd2cNCn0gZnJvbSAnQC9hcGkvUFJPL3Byb2R1Y3Rpb24tdGFzaycNCmltcG9ydCB7DQogIEdldENvbXBUYXNrTGlzdCwNCiAgR2V0U2ltcGxpZmllZFBhcnRUYXNrTGlzdCwNCiAgR2V0U2ltcGxpZmllZFBhcnRUYXNrUGFnZUxpc3QNCn0gZnJvbSAnQC9hcGkvUFJPL3Byb2R1Y3Rpb24tcmVwb3J0LW5ldycNCmltcG9ydCB7IEdldFN0b3BMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL3Byb2R1Y3Rpb24tdGFzaycNCmltcG9ydCB7IHBhcnNlT3NzVXJsIH0gZnJvbSAnQC91dGlscy9maWxlJw0KaW1wb3J0IHsgbWFwR2V0dGVycyB9IGZyb20gJ3Z1ZXgnDQppbXBvcnQgc2Nyb2xsIGZyb20gJy4vbWl4aW4vc2Nyb2xsJw0KaW1wb3J0IHsgdGltZUZvcm1hdCB9IGZyb20gJ0AvdXRpbHMvdGltZUZvcm1hdCcNCmltcG9ydCB7IEdldFByb2Nlc3NpbmdQcm9ncmVzc1Rhc2sgfSBmcm9tICdAL2FwaS9QUk8vcHJvY2Vzc2luZ3Byb2dyZXNzJw0KaW1wb3J0IFByb2Nlc3NEaWFsb2cgZnJvbSAnQC92aWV3cy9QUk8vYmFzaWMtaW5mb3JtYXRpb24vcHJvZHVjdGlvbi1zY2hlZHVsaW5nL3Byb2Nlc3MvcHJvY2Vzc2RpYWxvZy52dWUnDQppbXBvcnQgbnVtZXJhbCBmcm9tICdudW1lcmFsJw0KaW1wb3J0IHsgR2V0Qk9NSW5mbyB9IGZyb20gJ0Avdmlld3MvUFJPL2JvbS1zZXR0aW5nL3V0aWxzJw0KDQpjb25zdCBTUExJVF9TWU1CT0wgPSAnJF8kJw0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7DQogICAgRXhwYW5kYWJsZVNlY3Rpb24sDQogICAgUGFnaW5hdGlvbiwNCiAgICBDb21SZXBvcnQsDQogICAgVHJlZURldGFpbCwNCiAgICBRaXRhbywNCiAgICBQYXJ0RGV0YWlsLA0KICAgIFByb2Nlc3NEaWFsb2csDQogICAgUGFydFJlcG9ydA0KICB9LA0KICBtaXhpbnM6IFtnZXRUYkluZm8sIHNjcm9sbF0sDQogIGluamVjdDogWydwYWdlVHlwZSddLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICB0aWQ6ICcnLA0KICAgICAgY29tTmFtZTogJycsDQogICAgICBjb21wQ29kZTogJycsDQogICAgICBwYXJ0TmFtZTogJycsDQogICAgICBwcm9qZWN0TmFtZTogJycsDQogICAgICBzdGF0dXNUeXBlOiAn5b6F5oql5belJywNCiAgICAgIGV4cGFuZGVkS2V5OiAnJywNCiAgICAgIHRyZWVMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHRyZWVEYXRhOiBbXSwNCiAgICAgIHNob3dFeHBhbmQ6IHRydWUsDQogICAgICB0ZWFtT3B0aW9uczogW10sDQogICAgICB2YWx1ZTogJycsDQogICAgICBvZmZzZXQ6IDAsDQogICAgICBpdGVtV2lkdGg6IDI3NSArIDEyLCAvLyBpdGVtIHdpZHRoICsgbWFyZ2luDQogICAgICB0YXNrTGlzdDogW10sDQogICAgICBmb3JtOiB7DQogICAgICAgIE5lc3RpbmdfUmVzdWx0X05hbWU6ICcnLA0KICAgICAgICBXb3JraW5nX1RlYW1fSWQ6ICcnLA0KICAgICAgICBDb2RlX0xpa2U6ICcnLA0KICAgICAgICBUYXNrX0NvZGU6ICcnLA0KICAgICAgICBTeXNfUHJvamVjdF9JZDogJycsDQogICAgICAgIEFyZWFfSWQ6ICcnLA0KICAgICAgICBJbnN0YWxsVW5pdF9JZDogJycsDQogICAgICAgIFByb2R1Y3Rpb25fU3RhdHVzOiAnJywNCiAgICAgICAgU3BlY19MaWtlOiAnJw0KICAgICAgfSwNCiAgICAgIHRiTG9hZGluZzogZmFsc2UsDQogICAgICBzaG93TmVzdFBhcnQ6IGZhbHNlLA0KICAgICAgY29sdW1uczogW10sDQogICAgICB0YkRhdGE6IFtdLA0KICAgICAgdGJDb25maWc6IHt9LA0KICAgICAgcXVlcnlJbmZvOiB7DQogICAgICAgIFBhZ2U6IDEsDQogICAgICAgIFBhZ2VTaXplOiB0YWJsZVBhZ2VTaXplWzBdDQogICAgICB9LA0KICAgICAgdG90YWw6IDAsDQogICAgICB0YWJsZVBhZ2VTaXplOiB0YWJsZVBhZ2VTaXplLA0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBjdXJyZW50Q29tcG9uZW50OiAnJywNCiAgICAgIGRpYWxvZ1RpdGxlOiAnJywNCiAgICAgIHdpZHRoOiAnODAlJywNCiAgICAgIElzU2hvd0J0bjogZmFsc2UsDQogICAgICBtdWx0aXBsZVNlbGVjdGlvbjogW10sDQogICAgICBwZ0xvYWRpbmc6IGZhbHNlLA0KICAgICAgaXNOZXN0OiBmYWxzZSwNCiAgICAgIGRpc2FibGVMZWZ0QnRuOiB0cnVlLA0KICAgICAgZGlzYWJsZVJpZ2h0QnRuOiBmYWxzZQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICBpc0NvbSgpIHsNCiAgICAgIHJldHVybiB0aGlzLnBhZ2VUeXBlID09PSAnY29tJw0KICAgIH0sDQogICAgc3RhdHVzS2V5KCkgew0KICAgICAgcmV0dXJuIHRoaXMucGFnZVR5cGUgPT09ICdjb20nDQogICAgICAgID8gJ0NvbXBfUHJvZHVjZV9TdGF0dXMnDQogICAgICAgIDogJ1BhcnRfUHJvZHVjZV9TdGF0dXMnDQogICAgfSwNCiAgICBmaWx0ZXJUZXh0KCkgew0KICAgICAgcmV0dXJuIHRoaXMucHJvamVjdE5hbWUgKyBTUExJVF9TWU1CT0wgKyB0aGlzLnN0YXR1c1R5cGUNCiAgICB9LA0KICAgIGV4cGFuZEFsbCgpIHsNCiAgICAgIHJldHVybiBwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ2RldmVsb3BtZW50Jw0KICAgIH0sDQogICAgLi4ubWFwR2V0dGVycygnZmFjdG9yeUluZm8nLCBbDQogICAgICAnSXNfU2tpcF9XYXJlaG91c2luZ19PcGVyYXRpb24nLA0KICAgICAgJ05lc3RlZF9NdXN0X0JlZm9yZV9Qcm9jZXNzaW5nJywNCiAgICAgICdJc19QYXJ0X1ByZXBhcmUnDQogICAgXSkNCiAgfSwNCg0KICBhc3luYyBtb3VudGVkKCkgew0KICAgIGNvbnN0IHsgbGlzdCwgcGFydE5hbWUsIGNvbU5hbWUgfSA9IGF3YWl0IEdldEJPTUluZm8oKQ0KICAgIHRoaXMuYm9tTGlzdCA9IGxpc3QgfHwgW10NCiAgICB0aGlzLnBhcnROYW1lID0gcGFydE5hbWUNCiAgICB0aGlzLmNvbU5hbWUgPSBjb21OYW1lDQogICAgdGhpcy5wZ0xvYWRpbmcgPSB0cnVlDQogICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMuY2hlY2tSZXNpemUpDQogICAgdGhpcy5jaGVja1Jlc2l6ZSgpIC8vIGluaXRpYWwgY2hlY2sNCiAgICB0aGlzLmdldFRhYmxlQ29uZmlnKA0KICAgICAgdGhpcy5pc0NvbSA/ICdQUk9Qcm9kdWN0aW9uQ29tTmV3Q29uZmlnJyA6ICdQUk9Qcm9kdWN0aW9uUGFydE5ld0NvbmZpZycNCiAgICApDQogICAgdGhpcy5nZXRGYWN0b3J5SW5mbygpDQogICAgYXdhaXQgdGhpcy5nZXRQcm9jZXNzVGVhbSgpDQogICAgYXdhaXQgdGhpcy5mZXRjaFRyZWVEYXRhKCkNCiAgICAvLyBhd2FpdCB0aGlzLmdldFRhc2tMaXN0KCkNCiAgfSwNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdGhpcy5jaGVja1Jlc2l6ZSkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGdldERhdGEocm93KSB7DQogICAgICB0aGlzLiRuZXh0VGljaygoXykgPT4gew0KICAgICAgICB0aGlzLiRyZWZzWydwcm9jZXNzJ10uaGFuZGxlT3Blbih7DQogICAgICAgICAgQ29kZTogcm93LlRhc2tfQ29kZSwNCiAgICAgICAgICBUeXBlOiB0aGlzLmlzQ29tID8gJzAnIDogJzEnLA0KICAgICAgICAgIFRhc2tfSWQ6IHJvdy5UYXNrX0lkLA0KICAgICAgICAgIFByb2Nlc3NOYW1lOiByb3cuV29ya2luZ19Qcm9jZXNzX05hbWUsDQogICAgICAgICAgVGVhbU5hbWU6IHJvdy5Xb3JraW5nX1RlYW1fTmFtZSwNCiAgICAgICAgICBTY2hkdWxpbmdfQ29kZTogcm93Py5UYXNrX0NvZGUuc3BsaXQoJy0nKVswXSwNCiAgICAgICAgICBUZWFtSWQ6IHRoaXMuZm9ybS5Xb3JraW5nX1RlYW1fSWQNCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfSwNCiAgICBhc3luYyBnZXRGYWN0b3J5SW5mbygpIHsNCiAgICAgIGF3YWl0IHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdmYWN0b3J5SW5mby9nZXRXb3Jrc2hvcCcpDQogICAgICBjb25zb2xlLmxvZyh0aGlzLklzX1BhcnRfUHJlcGFyZSwgJz09PT0nKQ0KICAgIH0sDQogICAgYXN5bmMgZmV0Y2hUcmVlRGF0YSgpIHsNCiAgICAgIGNvbnNvbGUubG9nKCdmZXRjaFRyZWVEYXRhJykNCiAgICAgIHRoaXMudHJlZUxvYWRpbmcgPSB0cnVlDQogICAgICBhd2FpdCBHZXRQcm9qZWN0QXJlYVRyZWVMaXN0KHsNCiAgICAgICAgTWVudUlkOiB0aGlzLiRyb3V0ZS5tZXRhLklkLA0KICAgICAgICBwcm9qZWN0TmFtZTogdGhpcy5wcm9qZWN0TmFtZSwNCiAgICAgICAgdHlwZTogdGhpcy5pc0NvbSA/IDMgOiA0DQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5EYXRhLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICAgIHRoaXMudHJlZUxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICAgIGNvbnN0IHJlc0RhdGEgPSByZXMuRGF0YS5tYXAoKGl0ZW0pID0+IHsNCiAgICAgICAgICBpdGVtLklzX0RpcmVjdG9yeSA9IHRydWUNCg0KICAgICAgICAgIHJldHVybiBpdGVtDQogICAgICAgIH0pDQogICAgICAgIHRoaXMudHJlZURhdGEgPSByZXNEYXRhDQogICAgICAgIC8vIHRoaXMuZXhwYW5kZWRLZXkgPSByZXNEYXRhWzBdPy5DaGlsZHJlblswXT8uSWQNCiAgICAgICAgLy8gdGhpcy5mb3JtLkFyZWFfSWQgPSB0aGlzLmV4cGFuZGVkS2V5DQogICAgICAgIC8vIHRoaXMuZm9ybS5TeXNfUHJvamVjdF9JZCA9IHJlc0RhdGFbMF0uRGF0YS5TeXNfUHJvamVjdF9JZA0KICAgICAgICB0aGlzLiRuZXh0VGljaygoXykgPT4gew0KICAgICAgICAgIHRoaXMuJHJlZnMudHJlZS5maWx0ZXJSZWYodGhpcy5maWx0ZXJUZXh0KQ0KICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IHRoaXMuc2V0S2V5KCkNCiAgICAgICAgICBpZiAoIXJlc3VsdCkgew0KICAgICAgICAgICAgdGhpcy5wZ0xvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgdGhpcy50cmVlTG9hZGluZyA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQogICAgc2V0S2V5KCkgew0KICAgICAgY29uc3QgZGVlcEZpbHRlciA9ICh0cmVlKSA9PiB7DQogICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdHJlZS5sZW5ndGg7IGkrKykgew0KICAgICAgICAgIGNvbnN0IGl0ZW0gPSB0cmVlW2ldDQogICAgICAgICAgY29uc3QgeyBEYXRhLCBDaGlsZHJlbiB9ID0gaXRlbQ0KICAgICAgICAgIGNvbnN0IG5vZGUgPSBnZXROb2RlKERhdGEuSWQpDQogICAgICAgICAgaWYgKERhdGEuUGFyZW50SWQgJiYgIUNoaWxkcmVuPy5sZW5ndGggJiYgbm9kZS52aXNpYmxlKSB7DQogICAgICAgICAgICB0aGlzLmhhbmRsZU5vZGVDbGljayhpdGVtKQ0KICAgICAgICAgICAgcmV0dXJuIHRydWUNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgaWYgKENoaWxkcmVuPy5sZW5ndGgpIHsNCiAgICAgICAgICAgICAgY29uc3Qgc2hvdWxkU3RvcCA9IGRlZXBGaWx0ZXIoQ2hpbGRyZW4pDQogICAgICAgICAgICAgIGlmIChzaG91bGRTdG9wKSByZXR1cm4gdHJ1ZQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgIH0NCiAgICAgIGNvbnN0IGdldE5vZGUgPSAoa2V5KSA9PiB7DQogICAgICAgIHJldHVybiB0aGlzLiRyZWZzWyd0cmVlJ10uZ2V0Tm9kZUJ5S2V5KGtleSkNCiAgICAgIH0NCiAgICAgIHJldHVybiBkZWVwRmlsdGVyKHRoaXMudHJlZURhdGEpDQogICAgfSwNCiAgICBoYW5kbGVOb2RlQ2xpY2soZGF0YSkgew0KICAgICAgdGhpcy5leHBhbmRlZEtleSA9IGRhdGEuSWQNCiAgICAgIHRoaXMuY3VycmVudE5vZGVEYXRhID0gZGF0YQ0KICAgICAgdGhpcy5hcmVhSWQgPSBkYXRhLklkDQogICAgICB0aGlzLmZvcm0uQXJlYV9JZCA9IGRhdGEuSWQNCiAgICAgIHRoaXMuZm9ybS5TeXNfUHJvamVjdF9JZCA9IGRhdGEuRGF0YS5TeXNfUHJvamVjdF9JZA0KICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbiA9IFtdDQogICAgICB0aGlzLmdldFRhc2tMaXN0KCkNCiAgICB9LA0KDQogICAgc2F2ZVNvcnRGaW5pc2goKSB7DQogICAgICB0aGlzLiRyZWZzLnRyZWUuZmlsdGVyUmVmKHRoaXMuZmlsdGVyVGV4dCkNCiAgICB9LA0KDQogICAgY3VzdG9tRmlsdGVyRnVuKHZhbHVlLCBkYXRhLCBub2RlKSB7DQogICAgICBjb25zdCBhcnIgPSB2YWx1ZS5zcGxpdChTUExJVF9TWU1CT0wpDQogICAgICBjb25zdCBsYWJlbFZhbCA9IGFyclswXQ0KICAgICAgY29uc3Qgc3RhdHVzVmFsID0gYXJyWzFdDQogICAgICBpZiAoIXZhbHVlKSByZXR1cm4gdHJ1ZQ0KICAgICAgbGV0IHBhcmVudE5vZGUgPSBub2RlLnBhcmVudA0KICAgICAgbGV0IGxhYmVscyA9IFtub2RlLmxhYmVsXQ0KICAgICAgbGV0IHN0YXR1cyA9IFtkYXRhLkRhdGFbdGhpcy5zdGF0dXNLZXldXQ0KICAgICAgbGV0IGxldmVsID0gMQ0KICAgICAgd2hpbGUgKGxldmVsIDwgbm9kZS5sZXZlbCkgew0KICAgICAgICBsYWJlbHMgPSBbLi4ubGFiZWxzLCBwYXJlbnROb2RlLmxhYmVsXQ0KICAgICAgICBzdGF0dXMgPSBbLi4uc3RhdHVzLCBkYXRhLkRhdGFbdGhpcy5zdGF0dXNLZXldXQ0KICAgICAgICBwYXJlbnROb2RlID0gcGFyZW50Tm9kZS5wYXJlbnQNCiAgICAgICAgbGV2ZWwrKw0KICAgICAgfQ0KICAgICAgbGFiZWxzID0gbGFiZWxzLmZpbHRlcigodikgPT4gISF2KQ0KICAgICAgc3RhdHVzID0gc3RhdHVzLmZpbHRlcigodikgPT4gISF2KQ0KICAgICAgbGV0IHJlc3VsdExhYmVsID0gdHJ1ZQ0KICAgICAgbGV0IHJlc3VsdFN0YXR1cyA9IHRydWUNCiAgICAgIGlmICh0aGlzLnN0YXR1c1R5cGUpIHsNCiAgICAgICAgcmVzdWx0U3RhdHVzID0gc3RhdHVzLnNvbWUoKHMpID0+IHMuaW5kZXhPZihzdGF0dXNWYWwpICE9PSAtMSkNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLnByb2plY3ROYW1lKSB7DQogICAgICAgIHJlc3VsdExhYmVsID0gbGFiZWxzLnNvbWUoKHMpID0+IHMuaW5kZXhPZihsYWJlbFZhbCkgIT09IC0xKQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIHJlc3VsdExhYmVsICYmIHJlc3VsdFN0YXR1cw0KICAgIH0sDQoNCiAgICAvLyDojrflj5bnj63nu4QNCiAgICBhc3luYyBnZXRQcm9jZXNzVGVhbSgpIHsNCiAgICAgIGF3YWl0IEdldFRlYW1MaXN0QnlVc2VyKHsNCiAgICAgICAgdHlwZTogdGhpcy5pc0NvbSA/IDEgOiAyIC8vIDA65YWo6YOo77yM5bel6Im657G75Z6LMe+8muaehOS7tuW3peiJuu+8jDLvvJrpm7bku7blt6XoiboNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLnRlYW1PcHRpb25zID0gcmVzLkRhdGEubWFwKChpdGVtKSA9PiB7DQogICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIHZhbHVlOiBpdGVtLklkLA0KICAgICAgICAgICAgbGFiZWw6IGl0ZW0uTmFtZQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgICB0aGlzLmZvcm0uV29ya2luZ19UZWFtX0lkID0gdGhpcy50ZWFtT3B0aW9uc1swXT8udmFsdWUgfHwgJycNCiAgICB9LA0KICAgIGhhbmRsZVRlYW1DaGFuZ2UodmFsdWUpIHsNCiAgICAgIHRoaXMuZm9ybS5Xb3JraW5nX1RlYW1fSWQgPSB2YWx1ZQ0KICAgICAgdGhpcy5nZXRUYXNrTGlzdCgpDQogICAgfSwNCiAgICAvLyDojrflj5bku7vliqHljZXliJfooagNCiAgICBhc3luYyBnZXRUYXNrTGlzdCgpIHsNCiAgICAgIHRoaXMuZm9ybS5UYXNrX0NvZGUgPSAnJw0KICAgICAgdGhpcy5wZ0xvYWRpbmcgPSB0cnVlDQogICAgICBjb25zdCByZXF1ZXN0Rm4gPSB0aGlzLmlzQ29tDQogICAgICAgID8gR2V0Q29tcFRhc2tMaXN0DQogICAgICAgIDogR2V0U2ltcGxpZmllZFBhcnRUYXNrTGlzdA0KDQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXMgPSBhd2FpdCByZXF1ZXN0Rm4odGhpcy5mb3JtKQ0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMudGFza0xpc3QgPSAocmVzLkRhdGEgfHwgW10pLm1hcCgodikgPT4gew0KICAgICAgICAgICAgdi5EZW1hbmRfRGF0ZSA9IHRpbWVGb3JtYXQodi5EZW1hbmRfRGF0ZSkNCiAgICAgICAgICAgIHYucGVyY2VudGFnZSA9IDANCiAgICAgICAgICAgIHJldHVybiB2DQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLmlzTmVzdCA9IHRoaXMudGFza0xpc3Quc29tZSgodikgPT4gISF2LklzX05lc3QpDQogICAgICAgICAgdGhpcy5mb3JtLlRhc2tfQ29kZSA9IHRoaXMudGFza0xpc3RbMF0/LlRhc2tfQ29kZSB8fCAnJw0KICAgICAgICAgIGlmICh0aGlzLnRhc2tMaXN0Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIGNvbnN0IGN1ciA9IHRoaXMudGFza0xpc3RbMF0NCiAgICAgICAgICAgIGlmICh0aGlzLmNoZWNrU2hvd1AoY3VyLk5lZWRfUGFydF9BbW91bnQpKSB7DQogICAgICAgICAgICAgIHRoaXMuZ2V0UGVyY2VudERldGFpbCgpDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMudGFza0xpc3QgPSBbXQ0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLk1lc3NhZ2UpDQogICAgICAgIH0NCg0KICAgICAgICBpZiAodGhpcy50YXNrTGlzdC5sZW5ndGggPiAwKSB7DQogICAgICAgICAgYXdhaXQgdGhpcy5mZXRjaERhdGEoMSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLnRiRGF0YSA9IFtdDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLmNoZWNrUmVzaXplKCkgLy8gaW5pdGlhbCBjaGVjaw0KICAgICAgICB0aGlzLnBnTG9hZGluZyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVUYXNrKGl0ZW0pIHsNCiAgICAgIHRoaXMuZm9ybS5UYXNrX0NvZGUgPSBpdGVtLlRhc2tfQ29kZQ0KICAgICAgdGhpcy5zY3JvbGxUb0FjdGl2ZUl0ZW0oaXRlbSkNCiAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24gPSBbXQ0KICAgICAgdGhpcy5mb3JtLkNvZGVfTGlrZSA9ICcnDQogICAgICB0aGlzLmZvcm0uUHJvZHVjdGlvbl9TdGF0dXMgPSAnJw0KICAgICAgdGhpcy5mb3JtLlNwZWNfTGlrZSA9ICcnDQogICAgICB0aGlzLmlzTmVzdCA9IGl0ZW0uSXNfTmVzdA0KICAgICAgdGhpcy5mZXRjaERhdGEoMSkNCiAgICB9LA0KICAgIHNjcm9sbFRvQWN0aXZlSXRlbShpdGVtKSB7DQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIGNvbnN0IGl0ZW1FbGVtZW50ID0gdGhpcy4kcmVmc1tgdGFza18ke2l0ZW0uVGFza19Db2RlfWBdWzBdDQogICAgICAgIGNvbnNvbGUubG9nKGl0ZW1FbGVtZW50KQ0KICAgICAgICBpZiAoaXRlbUVsZW1lbnQpIHsNCiAgICAgICAgICBpdGVtRWxlbWVudC5zY3JvbGxJbnRvVmlldyh7IGJlaGF2aW9yOiAnc21vb3RoJywgaW5saW5lOiAnY2VudGVyJyB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgcmVmcmVzaCgpIHsNCiAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24gPSBbXQ0KICAgICAgdGhpcy5mZXRjaERhdGEoMSkNCiAgICB9LA0KICAgIGFzeW5jIGZldGNoRGF0YShwYWdlKSB7DQogICAgICBwYWdlICYmICh0aGlzLnF1ZXJ5SW5mby5QYWdlID0gcGFnZSkNCiAgICAgIGNvbnN0IGZvcm0gPSB7IC4uLnRoaXMuZm9ybSwgLi4udGhpcy5xdWVyeUluZm8gfQ0KICAgICAgaWYgKGZvcm0uV29ya2luZ19UZWFtX0lkID09PSAnYWxsJykgew0KICAgICAgICBjb25zdCBpZHMgPSB0aGlzLnRlYW1PcHRpb25zDQogICAgICAgICAgLm1hcCgodikgPT4gdi52YWx1ZSkNCiAgICAgICAgICAuZmlsdGVyKChzKSA9PiBzICE9PSAnYWxsJykNCiAgICAgICAgICAudG9TdHJpbmcoKQ0KICAgICAgICBmb3JtLldvcmtpbmdfVGVhbV9JZCA9IGlkcw0KICAgICAgfQ0KICAgICAgaWYgKCF0aGlzLmlzQ29tKSB7DQogICAgICAgIGZvcm0uU2hvd19OZXN0X1BhcnQgPSB0aGlzLnNob3dOZXN0UGFydA0KICAgICAgfQ0KICAgICAgdGhpcy50YkxvYWRpbmcgPSB0cnVlDQogICAgICBjb25zdCByZXF1ZXN0Rm4gPSB0aGlzLmlzQ29tDQogICAgICAgID8gR2V0Q29tcFRhc2tQYWdlTGlzdA0KICAgICAgICA6IEdldFNpbXBsaWZpZWRQYXJ0VGFza1BhZ2VMaXN0DQogICAgICBhd2FpdCByZXF1ZXN0Rm4oZm9ybSkNCiAgICAgICAgLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICB0aGlzLnRiRGF0YSA9IChyZXM/LkRhdGE/LkRhdGEgfHwgW10pLm1hcCgodikgPT4gew0KICAgICAgICAgICAgICB2LmNoZWNrZWQgPSBmYWxzZQ0KICAgICAgICAgICAgICByZXR1cm4gdg0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHRoaXMudG90YWwgPSByZXMuRGF0YS5Ub3RhbENvdW50DQogICAgICAgICAgICB0aGlzLmdldFN0b3BMaXN0KHRoaXMudGJEYXRhKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMudGJMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgfSkNCiAgICAgICAgLmZpbmFsbHkoKGUpID0+IHsNCiAgICAgICAgICB0aGlzLnRiTG9hZGluZyA9IGZhbHNlDQogICAgICAgIH0pDQogICAgfSwNCiAgICBhc3luYyBnZXRTdG9wTGlzdChsaXN0KSB7DQogICAgICBjb25zdCBrZXkgPSAnSWQnDQogICAgICBjb25zdCBzdWJtaXRPYmogPSBsaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICBJZDogaXRlbVtrZXldLA0KICAgICAgICAgIFR5cGU6IHRoaXMuaXNDb20gPyAyIDogMSAvLyAx77ya6Zu25Lu277yMM++8mumDqOS7tu+8jDLvvJrmnoTku7YNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIGF3YWl0IEdldFN0b3BMaXN0KHN1Ym1pdE9iaikudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIGNvbnN0IHN0b3BNYXAgPSB7fQ0KICAgICAgICAgIHJlcy5EYXRhLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgICBzdG9wTWFwW2l0ZW0uSWRdID0gISFpdGVtLklzX1N0b3ANCiAgICAgICAgICB9KQ0KICAgICAgICAgIGxpc3QuZm9yRWFjaChyb3cgPT4gew0KICAgICAgICAgICAgaWYgKHN0b3BNYXBbcm93W2tleV1dKSB7DQogICAgICAgICAgICAgIHRoaXMuJHNldChyb3csICdzdG9wRmxhZycsIHN0b3BNYXBbcm93W2tleV1dKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOafpeeci+Wbvue6uA0KICAgIGhhbmRsZUR3Zyhyb3cpIHsNCiAgICAgIEdldER3Zyh7DQogICAgICAgIFRhc2tfSWQ6IHJvdy5UYXNrX0lkDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICBjb25zdCBmaWxldXJsID0gcmVzPy5EYXRhPy5sZW5ndGggJiYgcmVzLkRhdGFbMF0uRmlsZV9VcmwNCiAgICAgICAgICB3aW5kb3cub3BlbigNCiAgICAgICAgICAgICdodHRwOi8vZHdndjEuYmltdGsuY29tOjU0MzIvP0NhZFVybD0nICsgcGFyc2VPc3NVcmwoZmlsZXVybCksDQogICAgICAgICAgICAnX2JsYW5rJw0KICAgICAgICAgICkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyDmn6XnnIvpvZDlpZflvLnmoYYNCiAgICBoYW5kbGVRaXRhbyhyb3cpIHsNCiAgICAgIHRoaXMuZGlhbG9nVGl0bGUgPSAn6Zu25Lu26b2Q5aWXJw0KICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ1FpdGFvJw0KICAgICAgdGhpcy50aWQgPSByb3cuVGFza19JZA0KICAgICAgdGhpcy5jb21wQ29kZSA9IHJvdy5Db21wX0NvZGUNCiAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyDmn6XnnIvpm7bku7YNCiAgICB2aWV3UGFydChpdGVtKSB7DQogICAgICB0aGlzLmRpYWxvZ1RpdGxlID0gJ+mbtuS7tuaYjue7huihqCcNCiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9ICdQYXJ0RGV0YWlsJw0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgICAgY29uc3Qgb2JqID0gew0KICAgICAgICBUYXNrX0NvZGU6IGl0ZW0uVGFza19Db2RlLA0KICAgICAgICBXb3JraW5nX1RlYW1fSWQ6IHRoaXMuZm9ybS5Xb3JraW5nX1RlYW1fSWQsDQogICAgICAgIFN5c19Qcm9qZWN0X0lkOiBpdGVtLlN5c19Qcm9qZWN0X0lkLA0KICAgICAgICBOZWVkX1VzZV9QYXJ0OiBpdGVtLk5lZWRfVXNlX1BhcnQNCiAgICAgIH0NCiAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgIHRoaXMuJHJlZnNbJ2NvbnRlbnQnXS5pbml0KG9iaikNCiAgICAgIH0pDQogICAgfSwNCiAgICBjaGVjQ2hlY2tib3hrTWV0aG9kMyh7IHJvdyB9KSB7DQogICAgICBpZiAodGhpcy5pc0NvbSkgew0KICAgICAgICBpZiAodHlwZW9mIHJvdy5QcmVwYXJlX0NvdW50ID09PSAnbnVtYmVyJyAmJiB0aGlzLklzX1BhcnRfUHJlcGFyZSkgew0KICAgICAgICAgIHJldHVybiByb3cuUmVhZHlfUHJvY2Vzc19Db3VudCA+IDAgJiYgcm93LlByZXBhcmVfQ291bnQgPiAwDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgcmV0dXJuIHJvdy5SZWFkeV9Qcm9jZXNzX0NvdW50ID4gMA0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gcm93LlJlYWR5X1Byb2Nlc3NfQ291bnQgPiAwDQogICAgICB9DQogICAgfSwNCiAgICBtdWx0aVNlbGVjdGVkQ2hhbmdlKGFycmF5KSB7DQogICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gYXJyYXkucmVjb3Jkcw0KICAgIH0sDQoNCiAgICAvLyDku7vliqHljZXlt6blj7Pnp7vliqjmlrnms5UNCiAgICBjbGlja01vdmUoZGlyZWN0aW9uKSB7DQogICAgICBjb25zdCBtaWRkbGVXYXBwZXJXaWR0aCA9IHRoaXMuJHJlZnMubWlkZGxlV2FwcGVyPy5vZmZzZXRXaWR0aA0KICAgICAgY29uc3QgYm94V2FwcGVyV2lkdGggPSB0aGlzLiRyZWZzLmJveFdhcHBlcj8uc2Nyb2xsV2lkdGgNCiAgICAgIGNvbnNvbGUubG9nKG1pZGRsZVdhcHBlcldpZHRoLCBib3hXYXBwZXJXaWR0aCkNCg0KICAgICAgaWYgKG1pZGRsZVdhcHBlcldpZHRoIDwgYm94V2FwcGVyV2lkdGgpIHsNCiAgICAgICAgaWYgKGRpcmVjdGlvbiA9PT0gJ2xlZnQnKSB7DQogICAgICAgICAgdGhpcy5vZmZzZXQgPSBNYXRoLm1pbih0aGlzLm9mZnNldCArIHRoaXMuaXRlbVdpZHRoLCAwKQ0KICAgICAgICB9IGVsc2UgaWYgKGRpcmVjdGlvbiA9PT0gJ3JpZ2h0Jykgew0KICAgICAgICAgIGNvbnN0IG1heE9mZnNldCA9IG1pZGRsZVdhcHBlcldpZHRoIC0gYm94V2FwcGVyV2lkdGgNCiAgICAgICAgICBjb25zb2xlLmxvZyhtYXhPZmZzZXQsIHRoaXMub2Zmc2V0IC0gdGhpcy5pdGVtV2lkdGgpDQogICAgICAgICAgdGhpcy5vZmZzZXQgPSBNYXRoLm1heCh0aGlzLm9mZnNldCAtIHRoaXMuaXRlbVdpZHRoLCBtYXhPZmZzZXQpDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIC8vIOabtOaWsOaMiemSrueahOemgeeUqOeKtuaAgQ0KICAgICAgdGhpcy5kaXNhYmxlTGVmdEJ0biA9IHRoaXMub2Zmc2V0ID09PSAwDQogICAgICB0aGlzLmRpc2FibGVSaWdodEJ0biA9IHRoaXMub2Zmc2V0ID09PSBtaWRkbGVXYXBwZXJXaWR0aCAtIGJveFdhcHBlcldpZHRoDQogICAgICBjb25zb2xlLmxvZyh0aGlzLm9mZnNldCwgdGhpcy5kaXNhYmxlTGVmdEJ0biwgdGhpcy5kaXNhYmxlUmlnaHRCdG4pDQogICAgfSwNCiAgICBjaGVja1Jlc2l6ZSgpIHsNCiAgICAgIGNvbnN0IG1pZGRsZVdhcHBlcldpZHRoID0gdGhpcy4kcmVmcy5taWRkbGVXYXBwZXIub2Zmc2V0V2lkdGgNCiAgICAgIGNvbnN0IGJveFdhcHBlcldpZHRoID0gdGhpcy4kcmVmcy5ib3hXYXBwZXIuc2Nyb2xsV2lkdGgNCiAgICAgIC8vIGNvbnNvbGUubG9nKG1pZGRsZVdhcHBlcldpZHRoLCBib3hXYXBwZXJXaWR0aCkNCg0KICAgICAgaWYgKG1pZGRsZVdhcHBlcldpZHRoID49IGJveFdhcHBlcldpZHRoKSB7DQogICAgICAgIHRoaXMub2Zmc2V0ID0gMA0KICAgICAgICB0aGlzLklzU2hvd0J0biA9IGZhbHNlDQogICAgICB9IGVsc2Ugew0KICAgICAgICBjb25zdCBtYXhPZmZzZXQgPSBtaWRkbGVXYXBwZXJXaWR0aCAtIGJveFdhcHBlcldpZHRoDQogICAgICAgIHRoaXMub2Zmc2V0ID0gTWF0aC5tYXgodGhpcy5vZmZzZXQsIG1heE9mZnNldCkNCiAgICAgICAgdGhpcy5Jc1Nob3dCdG4gPSB0cnVlDQogICAgICB9DQogICAgICAvLyDmm7TmlrDmjInpkq7nmoTnpoHnlKjnirbmgIENCiAgICAgIHRoaXMuZGlzYWJsZUxlZnRCdG4gPSB0aGlzLm9mZnNldCA9PT0gMA0KICAgICAgdGhpcy5kaXNhYmxlUmlnaHRCdG4gPSB0aGlzLm9mZnNldCA9PT0gbWlkZGxlV2FwcGVyV2lkdGggLSBib3hXYXBwZXJXaWR0aA0KICAgICAgY29uc29sZS5sb2codGhpcy5vZmZzZXQsIHRoaXMuZGlzYWJsZUxlZnRCdG4sIHRoaXMuZGlzYWJsZVJpZ2h0QnRuKQ0KICAgIH0sDQogICAgc2hvd05lc3RDaGFuZ2UodmFsKSB7DQogICAgICB0aGlzLnJlZnJlc2goKQ0KICAgIH0sDQogICAgaGFuZGxlUmVwb3J0KCkgew0KICAgICAgaWYgKHRoaXMuaXNDb20pIHsNCiAgICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ0NvbVJlcG9ydCcNCiAgICAgICAgdGhpcy5kaWFsb2dUaXRsZSA9ICfmiqXlt6UnDQogICAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgICAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgICAgICB0aGlzLiRyZWZzWydjb250ZW50J10uaW5pdCgNCiAgICAgICAgICAgICAgSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLm11bHRpcGxlU2VsZWN0aW9uKSkNCiAgICAgICAgICAgICkNCiAgICAgICAgICB9KQ0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5kaWFsb2dUaXRsZSA9ICfpm7bku7bmiqXlt6UnDQogICAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9ICdQYXJ0UmVwb3J0Jw0KICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgICAgdGhpcy4kcmVmc1snY29udGVudCddLmluaXQoDQogICAgICAgICAgICBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMubXVsdGlwbGVTZWxlY3Rpb24pKQ0KICAgICAgICAgICkNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZUNsb3NlKCkgew0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICB9LA0KICAgIGNoZWNrU2hvd1AobnVtKSB7DQogICAgICBpZiAodHlwZW9mIG51bSAhPT0gJ251bWJlcicpIHsNCiAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICB9DQogICAgICByZXR1cm4gdHJ1ZQ0KICAgIH0sDQogICAgZ2V0UGVyY2VudERldGFpbCgpIHsNCiAgICAgIEdldENvbXBUYXNrUGFydENvbXBsZXRpb25TdG9jayh7DQogICAgICAgIFdvcmtpbmdfVGVhbV9JZDogdGhpcy5mb3JtLldvcmtpbmdfVGVhbV9JZCwNCiAgICAgICAgVGFza19Db2RlOiB0aGlzLnRhc2tMaXN0Lm1hcCgodikgPT4gdi5UYXNrX0NvZGUpDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICBjb25zdCBsaXN0ID0gcmVzLkRhdGENCiAgICAgICAgICBjb25zdCB0YXNrTWFwID0ge30NCiAgICAgICAgICB0aGlzLnRhc2tMaXN0LmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICAgIHRhc2tNYXBbaXRlbS5UYXNrX0NvZGVdID0gaXRlbQ0KICAgICAgICAgIH0pDQogICAgICAgICAgbGlzdC5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgICBjb25zdCBjdXIgPSB0YXNrTWFwW2l0ZW0uVGFza19Db2RlXQ0KICAgICAgICAgICAgaWYgKGN1ci5OZWVkX1BhcnRfQW1vdW50ID09PSAtMSkgew0KICAgICAgICAgICAgICBjdXIucGVyY2VudGFnZSA9ICcxMDAlJw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgY29uc3QgcCA9IG51bWVyYWwoaXRlbS5QYXJ0X1N0b2NrX0NvdW50KS5kaXZpZGUoDQogICAgICAgICAgICAgICAgY3VyLk5lZWRfUGFydF9BbW91bnQNCiAgICAgICAgICAgICAgKQ0KICAgICAgICAgICAgICBsZXQgcmVzdWx0ID0gJzEwMCUnDQogICAgICAgICAgICAgIGlmIChwLnZhbHVlKCkgPD0gMSkgew0KICAgICAgICAgICAgICAgIHJlc3VsdCA9IHAuZm9ybWF0KCcwLlswMF0lJykNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBjdXIucGVyY2VudGFnZSA9IHJlc3VsdA0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["home.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4XA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "home.vue", "sourceRoot": "src/views/PRO/production-execution/new-report", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <div\r\n      v-loading=\"pgLoading\"\r\n      element-loading-text=\"加载中\"\r\n      class=\"h100 app-wrapper\"\r\n    >\r\n      <ExpandableSection v-model=\"showExpand\" :width=\"300\" class=\"cs-left fff\">\r\n        <div class=\"inner-wrapper\">\r\n          <div class=\"cs-search\">\r\n            <el-row>\r\n              <el-col :span=\"24\" class=\"team-select\">\r\n                <el-select\r\n                  v-model=\"form.Working_Team_Id\"\r\n                  clearable\r\n                  placeholder=\"请选择\"\r\n                  @change=\"handleTeamChange\"\r\n                >\r\n                  <i slot=\"prefix\">\r\n                    <img src=\"@/assets/PRO/icon-search.png\" alt=\"\">\r\n                  </i>\r\n                  <el-option\r\n                    v-for=\"item in teamOptions\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"item.value\"\r\n                  />\r\n                </el-select>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row :span=\"24\" :gutter=\"8\">\r\n              <el-col :span=\"10\">\r\n                <el-select v-model=\"statusType\" clearable placeholder=\"请选择\">\r\n                  <el-option label=\"所有状态\" value=\"\" />\r\n                  <el-option label=\"待报工\" value=\"待报工\" />\r\n                  <el-option label=\"报工完成\" value=\"报工完成\" />\r\n                  <el-option label=\"排产未完成\" value=\"排产未完成\" />\r\n                </el-select>\r\n              </el-col>\r\n              <el-col :span=\"14\">\r\n                <el-input\r\n                  v-model.trim=\"projectName\"\r\n                  placeholder=\"搜索...\"\r\n                  size=\"small\"\r\n                  clearable\r\n                  suffix-icon=\"el-icon-search\"\r\n                />\r\n              </el-col>\r\n            </el-row>\r\n          </div>\r\n\r\n          <div class=\"cs-tree cs-scroll\">\r\n            <tree-detail\r\n              ref=\"tree\"\r\n              :default-expand-all=\"expandAll\"\r\n              icon=\"icon-folder\"\r\n              is-custom-filter\r\n              :custom-filter-fun=\"customFilterFun\"\r\n              :loading=\"treeLoading\"\r\n              :tree-data=\"treeData\"\r\n              show-status\r\n              show-detail\r\n              :filter-text=\"filterText\"\r\n              :expanded-key=\"expandedKey\"\r\n              :can-node-click=\"false\"\r\n              @handleNodeClick=\"handleNodeClick\"\r\n              @saveSortFinish=\"saveSortFinish\"\r\n            >\r\n              <template #csLabel=\"{ showStatus, data }\">\r\n                <span\r\n                  v-if=\"!data.ParentNodes\"\r\n                  class=\"cs-blue\"\r\n                >({{ data.Code }})</span>{{ data.Label }}\r\n                <template v-if=\"showStatus\">\r\n                  <i\r\n                    v-if=\"data.Data[statusKey]\"\r\n                    :class=\"[\r\n                      data.Data[statusKey] == '报工完成'\r\n                        ? 'fourGreen'\r\n                        : data.Data[statusKey] == '待报工'\r\n                          ? 'fourOrange'\r\n                          : data.Data[statusKey] == '排产未完成'\r\n                            ? 'fourRed'\r\n                            : '',\r\n                    ]\"\r\n                  >\r\n                    <span>({{ data.Data[statusKey] }})</span>\r\n                  </i>\r\n                </template>\r\n              </template>\r\n            </tree-detail>\r\n          </div>\r\n        </div>\r\n      </ExpandableSection>\r\n      <div class=\"cs-right fff\">\r\n        <div v-show=\"taskList.length > 0\" class=\"cs-top-wapper\">\r\n          <div\r\n            v-if=\"IsShowBtn\"\r\n            class=\"btn\"\r\n            :class=\"{ disabled: disableLeftBtn }\"\r\n            @click=\"clickMove('left')\"\r\n          >\r\n            <i class=\"el-icon-arrow-left\" />\r\n          </div>\r\n          <div\r\n            ref=\"middleWapper\"\r\n            class=\"middle-wapper\"\r\n            @mousedown.prevent=\"handleMouseDown\"\r\n            @mouseleave.prevent=\"handleMouseLeave\"\r\n            @mouseup.prevent=\"handleMouseUp\"\r\n            @mousemove.prevent=\"handleMouseMove\"\r\n          >\r\n            <div\r\n              ref=\"boxWapper\"\r\n              class=\"box-wapper\"\r\n              :style=\"{ transform: `translateX(${offset}px)` }\"\r\n            >\r\n              <div\r\n                v-for=\"(item, index) in taskList\"\r\n                :key=\"index\"\r\n                :ref=\"'task_' + item.Task_Code\"\r\n                :class=\"[\r\n                  'item',\r\n                  form.Task_Code === item.Task_Code ? 'active' : '',\r\n                ]\"\r\n                @click=\"handleTask(item)\"\r\n              >\r\n                <div v-if=\"item.Return_Count > 0\" class=\"flag\">退</div>\r\n                <div v-if=\"item.Is_Nest\" class=\"flag2\">\r\n                  <span class=\"flag2-txt\">套</span>\r\n                </div>\r\n                <div class=\"content\">\r\n                  <div class=\"title\">\r\n                    <span class=\"name\"> {{ item.Task_Code }}</span>\r\n                    <!--                    <span v-if=\"isCom && item.Part_Completion_Rate !== null\" class=\"precent\">\r\n                      {{ item.Part_Completion_Rate > 1 ? 100 : Math.round(item.Part_Completion_Rate * 100) }}%\r\n                    </span>-->\r\n                    <span\r\n                      v-if=\"checkShowP(item.Need_Part_Amount)\"\r\n                      class=\"precent\"\r\n                    >{{ item.percentage }}</span>\r\n                  </div>\r\n                  <div class=\"detail\">\r\n                    <div class=\"left\">\r\n                      <div class=\"info\">要求：{{ item.Demand_Date }}</div>\r\n                      <div class=\"info\">\r\n                        总量：{{ item.Total_Count }}/{{\r\n                          parseFloat((item.Total_Weight / 1000).toFixed(3))\r\n                        }}t\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div\r\n                      v-if=\"isCom && checkShowP(item.Need_Part_Amount)\"\r\n                      class=\"right\"\r\n                      @click.stop=\"viewPart(item)\"\r\n                    >\r\n                      查看零件\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <i\r\n                  v-if=\"form.Task_Code === item.Task_Code\"\r\n                  class=\"el-icon-caret-bottom\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div\r\n            v-if=\"IsShowBtn\"\r\n            class=\"btn\"\r\n            :class=\"{ disabled: disableRightBtn }\"\r\n            @click=\"clickMove('right')\"\r\n          >\r\n            <i class=\"el-icon-arrow-right\" />\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"cs-middle-wapper\">\r\n          <div class=\"cs-middle-wapper-left\">\r\n            <el-button\r\n              type=\"primary\"\r\n              :disabled=\"!multipleSelection.length || multipleSelection.some(item=>item.stopFlag)\"\r\n              @click=\"handleReport()\"\r\n            >报工</el-button>\r\n            <el-checkbox\r\n              v-if=\"!isCom\"\r\n              v-model=\"showNestPart\"\r\n              class=\"cs-checkbox\"\r\n              @change=\"showNestChange\"\r\n            >显示套料零件</el-checkbox>\r\n          </div>\r\n\r\n          <el-form ref=\"form\" :model=\"form\" label-width=\"80px\" inline>\r\n            <el-form-item v-if=\"!isCom\" label=\"排版编号\">\r\n              <el-input\r\n                v-model=\"form.Nesting_Result_Name\"\r\n                placeholder=\"请输入\"\r\n                clearable\r\n              />\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"生产状态\">\r\n              <el-select\r\n                v-model=\"form.Production_Status\"\r\n                placeholder=\"请选择生产状态\"\r\n                style=\"width: 140px\"\r\n                clearable\r\n              >\r\n                <el-option label=\"未完成\" value=\"未完成\" />\r\n                <el-option label=\"已完成\" value=\"已完成\" />\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item :label=\"isCom ? `${comName}名称` : `${partName}名称`\">\r\n              <el-input\r\n                v-model=\"form.Code_Like\"\r\n                :placeholder=\"isCom ? `请输入${comName}名称` : `请输入${partName}名称`\"\r\n                clearable\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"规格\" label-width=\"50px\">\r\n              <el-input\r\n                v-model=\"form.Spec_Like\"\r\n                style=\"width: 140px\"\r\n                placeholder=\"请输入规格\"\r\n                clearable\r\n              />\r\n            </el-form-item>\r\n\r\n            <el-form-item>\r\n              <el-button type=\"primary\" @click=\"refresh\">搜索</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n\r\n        <div class=\"cs-bottom-wapper\">\r\n          <div class=\"fff tb-x\">\r\n            <vxe-table\r\n              :empty-render=\"{ name: 'NotData' }\"\r\n              show-header-overflow\r\n              :loading=\"tbLoading\"\r\n              element-loading-spinner=\"el-icon-loading\"\r\n              element-loading-text=\"拼命加载中\"\r\n              empty-text=\"暂无数据\"\r\n              class=\"cs-vxe-table\"\r\n              height=\"100%\"\r\n              align=\"left\"\r\n              stripe\r\n              :data=\"tbData\"\r\n              resizable\r\n              :tooltip-config=\"{ enterable: true }\"\r\n              :checkbox-config=\"{\r\n                checkField: 'checked',\r\n                trigger: 'row',\r\n                checkMethod: checCheckboxkMethod3,\r\n              }\"\r\n              @checkbox-all=\"multiSelectedChange\"\r\n              @checkbox-change=\"multiSelectedChange\"\r\n            >\r\n              <vxe-column fixed=\"left\" type=\"checkbox\" />\r\n              <template v-for=\"item in columns\">\r\n                <vxe-column\r\n                  :key=\"item.Code\"\r\n                  :min-width=\"item.Width\"\r\n                  width=\"auto\"\r\n                  :fixed=\"\r\n                    ['Comp_Code', 'Part_Code'].includes(item.Code) ? 'left' : ''\r\n                  \"\r\n                  show-overflow=\"tooltip\"\r\n                  sortable\r\n                  :align=\"item.Align\"\r\n                  :field=\"item.Code\"\r\n                  :title=\"item.Display_Name\"\r\n                >\r\n                  <template\r\n                    v-if=\"\r\n                      item.Code === 'Comp_Code' || item.Code === 'Part_Code'\r\n                    \"\r\n                    #default=\"{ row }\"\r\n                  >\r\n                    <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                    <el-tag\r\n                      v-if=\"row.Is_Change\"\r\n                      style=\"margin-right: 6px\"\r\n                      type=\"danger\"\r\n                      class=\"cs-tag\"\r\n                    >变</el-tag>\r\n                    <el-link\r\n                      v-if=\"row.DwgCount > 0\"\r\n                      type=\"primary\"\r\n                      @click.stop=\"handleDwg(row)\"\r\n                    >\r\n                      {{ row[item.Code] | displayValue }}</el-link>\r\n                    <span v-else> {{ row[item.Code] | displayValue }}</span>\r\n                  </template>\r\n                  <template\r\n                    v-else-if=\"item.Code === 'Prepare_Count'\"\r\n                    #default=\"{ row }\"\r\n                  >\r\n                    <el-link\r\n                      v-if=\"typeof row.Prepare_Count === 'number'\"\r\n                      type=\"primary\"\r\n                      @click.stop=\"handleQitao(row)\"\r\n                    >\r\n                      {{ row[item.Code] || 0 }}</el-link>\r\n                    <span v-else> {{ row[item.Code] | displayValue }}</span>\r\n                  </template>\r\n                  <template\r\n                    v-else-if=\"item.Code === 'Production_Status'\"\r\n                    #default=\"{ row }\"\r\n                  >\r\n                    <span\r\n                      :class=\"\r\n                        row.Production_Status === '未完成'\r\n                          ? 'fourRed'\r\n                          : row.Production_Status === '已完成'\r\n                            ? 'fourGreen'\r\n                            : ''\r\n                      \"\r\n                    >{{ row.Production_Status }}</span>\r\n                  </template>\r\n                  <template\r\n                    v-else-if=\"item.Code === 'Working_Process_Name'\"\r\n                    #default=\"{ row }\"\r\n                  >\r\n                    <el-link type=\"primary\" @click=\"getData(row)\">\r\n                      {{ row[item.Code] }}</el-link>\r\n                  </template>\r\n                  <template v-else #default=\"{ row }\">\r\n                    <span> {{ row[item.Code] | displayValue }}</span>\r\n                  </template>\r\n                </vxe-column>\r\n              </template></vxe-table>\r\n          </div>\r\n          <div class=\"data-info\">\r\n            <el-tag\r\n              size=\"medium\"\r\n              class=\"info-x\"\r\n            >已选 {{ multipleSelection.length }} 条数据\r\n            </el-tag>\r\n            <Pagination\r\n              :total=\"total\"\r\n              :page-sizes=\"tablePageSize\"\r\n              :page.sync=\"queryInfo.Page\"\r\n              :limit.sync=\"queryInfo.PageSize\"\r\n              @pagination=\"pageChange\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      ref=\"content\"\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"width\"\r\n      class=\"plm-custom-dialog\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :is-nest=\"isNest\"\r\n        :tid=\"tid\"\r\n        :comp-code=\"compCode\"\r\n        @close=\"handleClose\"\r\n        @refresh=\"refresh\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <ProcessDialog ref=\"process\" :show-search=\"false\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport Pagination from '@/components/Pagination'\r\nimport PartDetail from './components/PartDetail'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport ComReport from './components/ComReport'\r\nimport PartReport from './components/PartReport.vue'\r\nimport TreeDetail from '@/components/TreeDetail/index.vue'\r\nimport Qitao from './components/Qitao'\r\nimport { GetProjectAreaTreeList } from '@/api/PRO/project'\r\nimport { GetTeamListByUser } from '@/api/PRO/technology-lib'\r\nimport {\r\n  GetCompTaskPageList,\r\n  GetCompTaskPartCompletionStock,\r\n  GetDwg\r\n} from '@/api/PRO/production-task'\r\nimport {\r\n  GetCompTaskList,\r\n  GetSimplifiedPartTaskList,\r\n  GetSimplifiedPartTaskPageList\r\n} from '@/api/PRO/production-report-new'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nimport { parseOssUrl } from '@/utils/file'\r\nimport { mapGetters } from 'vuex'\r\nimport scroll from './mixin/scroll'\r\nimport { timeFormat } from '@/utils/timeFormat'\r\nimport { GetProcessingProgressTask } from '@/api/PRO/processingprogress'\r\nimport ProcessDialog from '@/views/PRO/basic-information/production-scheduling/process/processdialog.vue'\r\nimport numeral from 'numeral'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\n\r\nconst SPLIT_SYMBOL = '$_$'\r\nexport default {\r\n  components: {\r\n    ExpandableSection,\r\n    Pagination,\r\n    ComReport,\r\n    TreeDetail,\r\n    Qitao,\r\n    PartDetail,\r\n    ProcessDialog,\r\n    PartReport\r\n  },\r\n  mixins: [getTbInfo, scroll],\r\n  inject: ['pageType'],\r\n  data() {\r\n    return {\r\n      tid: '',\r\n      comName: '',\r\n      compCode: '',\r\n      partName: '',\r\n      projectName: '',\r\n      statusType: '待报工',\r\n      expandedKey: '',\r\n      treeLoading: false,\r\n      treeData: [],\r\n      showExpand: true,\r\n      teamOptions: [],\r\n      value: '',\r\n      offset: 0,\r\n      itemWidth: 275 + 12, // item width + margin\r\n      taskList: [],\r\n      form: {\r\n        Nesting_Result_Name: '',\r\n        Working_Team_Id: '',\r\n        Code_Like: '',\r\n        Task_Code: '',\r\n        Sys_Project_Id: '',\r\n        Area_Id: '',\r\n        InstallUnit_Id: '',\r\n        Production_Status: '',\r\n        Spec_Like: ''\r\n      },\r\n      tbLoading: false,\r\n      showNestPart: false,\r\n      columns: [],\r\n      tbData: [],\r\n      tbConfig: {},\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: tablePageSize[0]\r\n      },\r\n      total: 0,\r\n      tablePageSize: tablePageSize,\r\n      dialogVisible: false,\r\n      currentComponent: '',\r\n      dialogTitle: '',\r\n      width: '80%',\r\n      IsShowBtn: false,\r\n      multipleSelection: [],\r\n      pgLoading: false,\r\n      isNest: false,\r\n      disableLeftBtn: true,\r\n      disableRightBtn: false\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    },\r\n    statusKey() {\r\n      return this.pageType === 'com'\r\n        ? 'Comp_Produce_Status'\r\n        : 'Part_Produce_Status'\r\n    },\r\n    filterText() {\r\n      return this.projectName + SPLIT_SYMBOL + this.statusType\r\n    },\r\n    expandAll() {\r\n      return process.env.NODE_ENV !== 'development'\r\n    },\r\n    ...mapGetters('factoryInfo', [\r\n      'Is_Skip_Warehousing_Operation',\r\n      'Nested_Must_Before_Processing',\r\n      'Is_Part_Prepare'\r\n    ])\r\n  },\r\n\r\n  async mounted() {\r\n    const { list, partName, comName } = await GetBOMInfo()\r\n    this.bomList = list || []\r\n    this.partName = partName\r\n    this.comName = comName\r\n    this.pgLoading = true\r\n    window.addEventListener('resize', this.checkResize)\r\n    this.checkResize() // initial check\r\n    this.getTableConfig(\r\n      this.isCom ? 'PROProductionComNewConfig' : 'PROProductionPartNewConfig'\r\n    )\r\n    this.getFactoryInfo()\r\n    await this.getProcessTeam()\r\n    await this.fetchTreeData()\r\n    // await this.getTaskList()\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.checkResize)\r\n  },\r\n  methods: {\r\n    getData(row) {\r\n      this.$nextTick((_) => {\r\n        this.$refs['process'].handleOpen({\r\n          Code: row.Task_Code,\r\n          Type: this.isCom ? '0' : '1',\r\n          Task_Id: row.Task_Id,\r\n          ProcessName: row.Working_Process_Name,\r\n          TeamName: row.Working_Team_Name,\r\n          Schduling_Code: row?.Task_Code.split('-')[0],\r\n          TeamId: this.form.Working_Team_Id\r\n        })\r\n      })\r\n    },\r\n    async getFactoryInfo() {\r\n      await this.$store.dispatch('factoryInfo/getWorkshop')\r\n      console.log(this.Is_Part_Prepare, '====')\r\n    },\r\n    async fetchTreeData() {\r\n      console.log('fetchTreeData')\r\n      this.treeLoading = true\r\n      await GetProjectAreaTreeList({\r\n        MenuId: this.$route.meta.Id,\r\n        projectName: this.projectName,\r\n        type: this.isCom ? 3 : 4\r\n      }).then((res) => {\r\n        if (res.Data.length === 0) {\r\n          this.treeLoading = false\r\n          return\r\n        }\r\n        const resData = res.Data.map((item) => {\r\n          item.Is_Directory = true\r\n\r\n          return item\r\n        })\r\n        this.treeData = resData\r\n        // this.expandedKey = resData[0]?.Children[0]?.Id\r\n        // this.form.Area_Id = this.expandedKey\r\n        // this.form.Sys_Project_Id = resData[0].Data.Sys_Project_Id\r\n        this.$nextTick((_) => {\r\n          this.$refs.tree.filterRef(this.filterText)\r\n          const result = this.setKey()\r\n          if (!result) {\r\n            this.pgLoading = false\r\n          }\r\n        })\r\n        this.treeLoading = false\r\n      })\r\n    },\r\n    setKey() {\r\n      const deepFilter = (tree) => {\r\n        for (let i = 0; i < tree.length; i++) {\r\n          const item = tree[i]\r\n          const { Data, Children } = item\r\n          const node = getNode(Data.Id)\r\n          if (Data.ParentId && !Children?.length && node.visible) {\r\n            this.handleNodeClick(item)\r\n            return true\r\n          } else {\r\n            if (Children?.length) {\r\n              const shouldStop = deepFilter(Children)\r\n              if (shouldStop) return true\r\n            }\r\n          }\r\n        }\r\n        return false\r\n      }\r\n      const getNode = (key) => {\r\n        return this.$refs['tree'].getNodeByKey(key)\r\n      }\r\n      return deepFilter(this.treeData)\r\n    },\r\n    handleNodeClick(data) {\r\n      this.expandedKey = data.Id\r\n      this.currentNodeData = data\r\n      this.areaId = data.Id\r\n      this.form.Area_Id = data.Id\r\n      this.form.Sys_Project_Id = data.Data.Sys_Project_Id\r\n      this.multipleSelection = []\r\n      this.getTaskList()\r\n    },\r\n\r\n    saveSortFinish() {\r\n      this.$refs.tree.filterRef(this.filterText)\r\n    },\r\n\r\n    customFilterFun(value, data, node) {\r\n      const arr = value.split(SPLIT_SYMBOL)\r\n      const labelVal = arr[0]\r\n      const statusVal = arr[1]\r\n      if (!value) return true\r\n      let parentNode = node.parent\r\n      let labels = [node.label]\r\n      let status = [data.Data[this.statusKey]]\r\n      let level = 1\r\n      while (level < node.level) {\r\n        labels = [...labels, parentNode.label]\r\n        status = [...status, data.Data[this.statusKey]]\r\n        parentNode = parentNode.parent\r\n        level++\r\n      }\r\n      labels = labels.filter((v) => !!v)\r\n      status = status.filter((v) => !!v)\r\n      let resultLabel = true\r\n      let resultStatus = true\r\n      if (this.statusType) {\r\n        resultStatus = status.some((s) => s.indexOf(statusVal) !== -1)\r\n      }\r\n      if (this.projectName) {\r\n        resultLabel = labels.some((s) => s.indexOf(labelVal) !== -1)\r\n      }\r\n      return resultLabel && resultStatus\r\n    },\r\n\r\n    // 获取班组\r\n    async getProcessTeam() {\r\n      await GetTeamListByUser({\r\n        type: this.isCom ? 1 : 2 // 0:全部，工艺类型1：构件工艺，2：零件工艺\r\n      }).then((res) => {\r\n        this.teamOptions = res.Data.map((item) => {\r\n          return {\r\n            value: item.Id,\r\n            label: item.Name\r\n          }\r\n        })\r\n      })\r\n      this.form.Working_Team_Id = this.teamOptions[0]?.value || ''\r\n    },\r\n    handleTeamChange(value) {\r\n      this.form.Working_Team_Id = value\r\n      this.getTaskList()\r\n    },\r\n    // 获取任务单列表\r\n    async getTaskList() {\r\n      this.form.Task_Code = ''\r\n      this.pgLoading = true\r\n      const requestFn = this.isCom\r\n        ? GetCompTaskList\r\n        : GetSimplifiedPartTaskList\r\n\r\n      try {\r\n        const res = await requestFn(this.form)\r\n        if (res.IsSucceed) {\r\n          this.taskList = (res.Data || []).map((v) => {\r\n            v.Demand_Date = timeFormat(v.Demand_Date)\r\n            v.percentage = 0\r\n            return v\r\n          })\r\n          this.isNest = this.taskList.some((v) => !!v.Is_Nest)\r\n          this.form.Task_Code = this.taskList[0]?.Task_Code || ''\r\n          if (this.taskList.length > 0) {\r\n            const cur = this.taskList[0]\r\n            if (this.checkShowP(cur.Need_Part_Amount)) {\r\n              this.getPercentDetail()\r\n            }\r\n          }\r\n        } else {\r\n          this.taskList = []\r\n          this.$message.error(res.Message)\r\n        }\r\n\r\n        if (this.taskList.length > 0) {\r\n          await this.fetchData(1)\r\n        } else {\r\n          this.tbData = []\r\n        }\r\n      } catch (error) {\r\n      } finally {\r\n        this.checkResize() // initial check\r\n        this.pgLoading = false\r\n      }\r\n    },\r\n    handleTask(item) {\r\n      this.form.Task_Code = item.Task_Code\r\n      this.scrollToActiveItem(item)\r\n      this.multipleSelection = []\r\n      this.form.Code_Like = ''\r\n      this.form.Production_Status = ''\r\n      this.form.Spec_Like = ''\r\n      this.isNest = item.Is_Nest\r\n      this.fetchData(1)\r\n    },\r\n    scrollToActiveItem(item) {\r\n      this.$nextTick(() => {\r\n        const itemElement = this.$refs[`task_${item.Task_Code}`][0]\r\n        console.log(itemElement)\r\n        if (itemElement) {\r\n          itemElement.scrollIntoView({ behavior: 'smooth', inline: 'center' })\r\n        }\r\n      })\r\n    },\r\n    refresh() {\r\n      this.multipleSelection = []\r\n      this.fetchData(1)\r\n    },\r\n    async fetchData(page) {\r\n      page && (this.queryInfo.Page = page)\r\n      const form = { ...this.form, ...this.queryInfo }\r\n      if (form.Working_Team_Id === 'all') {\r\n        const ids = this.teamOptions\r\n          .map((v) => v.value)\r\n          .filter((s) => s !== 'all')\r\n          .toString()\r\n        form.Working_Team_Id = ids\r\n      }\r\n      if (!this.isCom) {\r\n        form.Show_Nest_Part = this.showNestPart\r\n      }\r\n      this.tbLoading = true\r\n      const requestFn = this.isCom\r\n        ? GetCompTaskPageList\r\n        : GetSimplifiedPartTaskPageList\r\n      await requestFn(form)\r\n        .then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.tbData = (res?.Data?.Data || []).map((v) => {\r\n              v.checked = false\r\n              return v\r\n            })\r\n            this.total = res.Data.TotalCount\r\n            this.getStopList(this.tbData)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.tbLoading = false\r\n        })\r\n        .finally((e) => {\r\n          this.tbLoading = false\r\n        })\r\n    },\r\n    async getStopList(list) {\r\n      const key = 'Id'\r\n      const submitObj = list.map(item => {\r\n        return {\r\n          Id: item[key],\r\n          Type: this.isCom ? 2 : 1 // 1：零件，3：部件，2：构件\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = !!item.Is_Stop\r\n          })\r\n          list.forEach(row => {\r\n            if (stopMap[row[key]]) {\r\n              this.$set(row, 'stopFlag', stopMap[row[key]])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 查看图纸\r\n    handleDwg(row) {\r\n      GetDwg({\r\n        Task_Id: row.Task_Id\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const fileurl = res?.Data?.length && res.Data[0].File_Url\r\n          window.open(\r\n            'http://dwgv1.bimtk.com:5432/?CadUrl=' + parseOssUrl(fileurl),\r\n            '_blank'\r\n          )\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 查看齐套弹框\r\n    handleQitao(row) {\r\n      this.dialogTitle = '零件齐套'\r\n      this.currentComponent = 'Qitao'\r\n      this.tid = row.Task_Id\r\n      this.compCode = row.Comp_Code\r\n      this.$nextTick((_) => {\r\n        this.dialogVisible = true\r\n      })\r\n    },\r\n    // 查看零件\r\n    viewPart(item) {\r\n      this.dialogTitle = '零件明细表'\r\n      this.currentComponent = 'PartDetail'\r\n      this.dialogVisible = true\r\n      const obj = {\r\n        Task_Code: item.Task_Code,\r\n        Working_Team_Id: this.form.Working_Team_Id,\r\n        Sys_Project_Id: item.Sys_Project_Id,\r\n        Need_Use_Part: item.Need_Use_Part\r\n      }\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(obj)\r\n      })\r\n    },\r\n    checCheckboxkMethod3({ row }) {\r\n      if (this.isCom) {\r\n        if (typeof row.Prepare_Count === 'number' && this.Is_Part_Prepare) {\r\n          return row.Ready_Process_Count > 0 && row.Prepare_Count > 0\r\n        } else {\r\n          return row.Ready_Process_Count > 0\r\n        }\r\n      } else {\r\n        return row.Ready_Process_Count > 0\r\n      }\r\n    },\r\n    multiSelectedChange(array) {\r\n      this.multipleSelection = array.records\r\n    },\r\n\r\n    // 任务单左右移动方法\r\n    clickMove(direction) {\r\n      const middleWapperWidth = this.$refs.middleWapper?.offsetWidth\r\n      const boxWapperWidth = this.$refs.boxWapper?.scrollWidth\r\n      console.log(middleWapperWidth, boxWapperWidth)\r\n\r\n      if (middleWapperWidth < boxWapperWidth) {\r\n        if (direction === 'left') {\r\n          this.offset = Math.min(this.offset + this.itemWidth, 0)\r\n        } else if (direction === 'right') {\r\n          const maxOffset = middleWapperWidth - boxWapperWidth\r\n          console.log(maxOffset, this.offset - this.itemWidth)\r\n          this.offset = Math.max(this.offset - this.itemWidth, maxOffset)\r\n        }\r\n      }\r\n      // 更新按钮的禁用状态\r\n      this.disableLeftBtn = this.offset === 0\r\n      this.disableRightBtn = this.offset === middleWapperWidth - boxWapperWidth\r\n      console.log(this.offset, this.disableLeftBtn, this.disableRightBtn)\r\n    },\r\n    checkResize() {\r\n      const middleWapperWidth = this.$refs.middleWapper.offsetWidth\r\n      const boxWapperWidth = this.$refs.boxWapper.scrollWidth\r\n      // console.log(middleWapperWidth, boxWapperWidth)\r\n\r\n      if (middleWapperWidth >= boxWapperWidth) {\r\n        this.offset = 0\r\n        this.IsShowBtn = false\r\n      } else {\r\n        const maxOffset = middleWapperWidth - boxWapperWidth\r\n        this.offset = Math.max(this.offset, maxOffset)\r\n        this.IsShowBtn = true\r\n      }\r\n      // 更新按钮的禁用状态\r\n      this.disableLeftBtn = this.offset === 0\r\n      this.disableRightBtn = this.offset === middleWapperWidth - boxWapperWidth\r\n      console.log(this.offset, this.disableLeftBtn, this.disableRightBtn)\r\n    },\r\n    showNestChange(val) {\r\n      this.refresh()\r\n    },\r\n    handleReport() {\r\n      if (this.isCom) {\r\n        this.currentComponent = 'ComReport'\r\n        this.dialogTitle = '报工'\r\n        this.$nextTick((_) => {\r\n          this.dialogVisible = true\r\n          this.$nextTick((_) => {\r\n            this.$refs['content'].init(\r\n              JSON.parse(JSON.stringify(this.multipleSelection))\r\n            )\r\n          })\r\n        })\r\n      } else {\r\n        this.dialogTitle = '零件报工'\r\n        this.currentComponent = 'PartReport'\r\n        this.dialogVisible = true\r\n        this.$nextTick((_) => {\r\n          this.$refs['content'].init(\r\n            JSON.parse(JSON.stringify(this.multipleSelection))\r\n          )\r\n        })\r\n      }\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    checkShowP(num) {\r\n      if (typeof num !== 'number') {\r\n        return false\r\n      }\r\n      return true\r\n    },\r\n    getPercentDetail() {\r\n      GetCompTaskPartCompletionStock({\r\n        Working_Team_Id: this.form.Working_Team_Id,\r\n        Task_Code: this.taskList.map((v) => v.Task_Code)\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const list = res.Data\r\n          const taskMap = {}\r\n          this.taskList.forEach((item) => {\r\n            taskMap[item.Task_Code] = item\r\n          })\r\n          list.forEach((item) => {\r\n            const cur = taskMap[item.Task_Code]\r\n            if (cur.Need_Part_Amount === -1) {\r\n              cur.percentage = '100%'\r\n            } else {\r\n              const p = numeral(item.Part_Stock_Count).divide(\r\n                cur.Need_Part_Amount\r\n              )\r\n              let result = '100%'\r\n              if (p.value() <= 1) {\r\n                result = p.format('0.[00]%')\r\n              }\r\n              cur.percentage = result\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"~@/styles/mixin.scss\";\r\n// @import \"~@/styles/tabs.scss\";\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n.app-wrapper {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  overflow: hidden;\r\n\r\n  .cs-left {\r\n    display: flex;\r\n    flex-direction: column;\r\n    margin-right: 20px;\r\n\r\n    .inner-wrapper {\r\n      flex: 1;\r\n      display: flex;\r\n      flex-direction: column;\r\n      padding: 16px 16px;\r\n      border-radius: 4px;\r\n      overflow: hidden;\r\n      .cs-search {\r\n        border-bottom: 1px solid #e2e4e9;\r\n        padding-bottom: 17px;\r\n        .team-select {\r\n          margin-bottom: 12px;\r\n          .el-select {\r\n            width: 100%;\r\n          }\r\n\r\n          ::v-deep {\r\n            .el-input__inner {\r\n              border: 1px solid #298dff;\r\n              color: #298dff;\r\n              font-weight: bold;\r\n            }\r\n            .el-input__prefix {\r\n              display: flex;\r\n              justify-content: center;\r\n              align-items: center;\r\n\r\n              img {\r\n                width: 16px;\r\n                height: 16px;\r\n              }\r\n            }\r\n\r\n            .el-select__caret {\r\n              color: #298dff;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      .cs-tree {\r\n        margin-top: 22px;\r\n        flex: 1;\r\n        height: 0;\r\n\r\n        .cs-scroll {\r\n          overflow-y: auto;\r\n          @include scrollBar;\r\n        }\r\n\r\n        .el-tree {\r\n          height: 100%;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .cs-right {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n    padding: 16px 16px;\r\n\r\n    .cs-top-wapper {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      // align-items: center;\r\n      .btn {\r\n        width: 20px;\r\n        height: 20px;\r\n        border-radius: 10px;\r\n        background: #cfcfcf;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        color: #fff;\r\n        font-weight: bold;\r\n        font-size: 12px;\r\n        cursor: pointer;\r\n        margin-top: calc((96px / 2) - 10px);\r\n      }\r\n      .btn.disabled {\r\n        opacity: 0.5;\r\n        // pointer-events: none;\r\n        cursor: not-allowed !important;\r\n      }\r\n\r\n      .middle-wapper::-webkit-scrollbar {\r\n        height: 0; /* 将滚动条的宽度设为0 */\r\n        background: transparent; /* 使滚动条完全透明 */\r\n      }\r\n\r\n      .middle-wapper {\r\n        flex: 1;\r\n        width: 0;\r\n        overflow-x: auto;\r\n        position: relative;\r\n        height: calc(96px + 10px);\r\n\r\n        .box-wapper {\r\n          position: absolute;\r\n          display: flex;\r\n          flex-wrap: no-wrap;\r\n          height: 100%;\r\n          transition: transform 0.3s ease;\r\n\r\n          .item {\r\n            width: 275px;\r\n            height: calc(100% - 10px);\r\n            border-radius: 4px 4px 4px 4px;\r\n            border: 1px solid #e2e4e9;\r\n            margin-left: 12px;\r\n            position: relative;\r\n            cursor: pointer;\r\n            .content {\r\n              width: 100%;\r\n              height: 100%;\r\n              display: flex;\r\n              padding: 12px 12px;\r\n              flex-direction: column;\r\n              // align-items: center;\r\n              // justify-content: space-between;\r\n              // background: linear-gradient( 91deg, #298DFF 0%, #57C2FF 100%);\r\n              .detail {\r\n                flex: 1;\r\n                height: 0;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: space-between;\r\n              }\r\n              .title {\r\n                margin-bottom: 8px;\r\n                display: flex;\r\n                align-items: center;\r\n              }\r\n              .name {\r\n                font-weight: bold;\r\n                font-size: 16px;\r\n              }\r\n              .precent {\r\n                height: 17px;\r\n                padding: 0 4px;\r\n                background: #00cfaa;\r\n                border-radius: 9px 9px 9px 9px;\r\n                color: #ffffff;\r\n                font-size: 12px;\r\n                margin-left: 8px;\r\n                line-height: 17px;\r\n                text-align: center;\r\n              }\r\n              .info {\r\n                font-size: 14px;\r\n                color: #999999;\r\n                margin-bottom: 2px;\r\n              }\r\n\r\n              .right {\r\n                // width: 80px;\r\n                padding: 5px 8px;\r\n                border-radius: 4px 4px 4px 4px;\r\n                text-align: center;\r\n\r\n                font-size: 14px;\r\n                cursor: pointer;\r\n                background: #e9f3ff;\r\n                color: #298dff;\r\n              }\r\n            }\r\n\r\n            .flag {\r\n              position: absolute;\r\n              top: 0;\r\n              right: 0;\r\n              width: 35px;\r\n              height: 35px;\r\n              background: url(\"~@/assets/PRO/flag.png\") no-repeat;\r\n              font-size: 12px;\r\n              color: #ffffff;\r\n              display: flex;\r\n              justify-content: flex-end;\r\n              align-items: flex-start;\r\n              padding-right: 4px;\r\n              padding-top: 6px;\r\n            }\r\n            i {\r\n              position: absolute;\r\n              bottom: -10px;\r\n              left: calc((100% - 16px) / 2);\r\n              color: #298dff;\r\n            }\r\n            .flag2 {\r\n              position: absolute;\r\n              bottom: 0;\r\n              right: 0;\r\n              width: 0;\r\n              height: 0;\r\n              border-left: 35px solid transparent;\r\n              border-top: 35px solid transparent;\r\n              border-bottom: 35px solid #e6a23c;\r\n              font-size: 12px;\r\n              color: #ffffff;\r\n              .flag2-txt {\r\n                position: absolute;\r\n                right: 3px;\r\n                bottom: -33px;\r\n              }\r\n            }\r\n          }\r\n          .active {\r\n            .content {\r\n              color: #ffffff;\r\n              background: linear-gradient(91deg, #298dff 0%, #57c2ff 100%);\r\n              .info {\r\n                color: #ffffff;\r\n              }\r\n            }\r\n            .right {\r\n              background: #ffffff;\r\n            }\r\n          }\r\n          .item:last-child {\r\n            margin-right: 12px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .cs-middle-wapper {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      margin: 12px 0;\r\n      align-items: center;\r\n      .el-form-item {\r\n        margin-bottom: 0;\r\n      }\r\n    }\r\n\r\n    .cs-bottom-wapper {\r\n      flex: 1;\r\n      height: 0;\r\n      display: flex;\r\n      flex-direction: column;\r\n      .tb-x {\r\n        flex: 1;\r\n        height: 0;\r\n      }\r\n\r\n      .pagination-container {\r\n        text-align: right;\r\n        padding: 16px;\r\n        margin: 0;\r\n      }\r\n\r\n      .data-info {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.plm-custom-dialog {\r\n  ::v-deep {\r\n    .el-dialog__body {\r\n      height: 70vh;\r\n      overflow: auto;\r\n      display: flex;\r\n      flex-direction: column;\r\n    }\r\n  }\r\n}\r\n\r\n.fourGreen {\r\n  color: #00c361;\r\n  font-style: normal;\r\n}\r\n\r\n.fourOrange {\r\n  color: #ff9400;\r\n  font-style: normal;\r\n}\r\n\r\n.fourRed {\r\n  color: #ff0000;\r\n  font-style: normal;\r\n}\r\n\r\n.cs-blue {\r\n  color: #5ac8fa;\r\n}\r\n.cs-checkbox {\r\n  margin-left: 16px;\r\n  line-height: 30px;\r\n}\r\n.cs-middle-wapper-left{\r\n  display: flex;\r\n  flex-wrap: nowrap;\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\AssociatedDevice.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\AssociatedDevice.vue", "mtime": 1757485729087}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBnZXRUYkluZm8gZnJvbSAnQC9taXhpbnMvUFJPL2dldC10YWJsZS1pbmZvJwppbXBvcnQgRHluYW1pY0RhdGFUYWJsZSBmcm9tICdAL2NvbXBvbmVudHMvRHluYW1pY0RhdGFUYWJsZS9EeW5hbWljRGF0YVRhYmxlJwppbXBvcnQgVG9wSGVhZGVyIGZyb20gJ0AvY29tcG9uZW50cy9Ub3BIZWFkZXInCi8vIGltcG9ydCBkZXRhaWwgZnJvbSAiLi9jb21wb25lbnQvZGV0YWlsIjsKaW1wb3J0IHsgR2V0RGljdGlvbmFyeURldGFpbExpc3RCeUNvZGUsIEdldERpY3Rpb25hcnlEZXRhaWxMaXN0QnlQYXJlbnRJZCB9IGZyb20gJ0AvYXBpL3N5cycKaW1wb3J0IHsgR2V0RXF1aXBtZW50QXNzZXRQYWdlTGlzdCwgQWRkV29ya2luZ1Byb2Nlc3MgfSBmcm9tICdAL2FwaS9QUk8vdGVjaG5vbG9neS1saWInCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1BST0dyb3VwJywKCiAgY29tcG9uZW50czogewogICAgRHluYW1pY0RhdGFUYWJsZSwKICAgIFRvcEhlYWRlcgogICAgLy8gZGV0YWlsLAogIH0sCiAgbWl4aW5zOiBbZ2V0VGJJbmZvXSwKICBwcm9wczogewogICAgcm93RGF0YTogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIHt9CiAgICAgIH0KICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB0YkNvbmZpZzogewogICAgICAgIFBhZ2VyX0FsaWduOiAnY2VudGVyJwogICAgICB9LAogICAgICBxdWVyeUluZm86IHsKICAgICAgICBQYWdlOiAxLAogICAgICAgIFBhZ2VTaXplOiAxMAogICAgICB9LAogICAgICBkZXZpY2VJdGVtVHlwZU9wdGlvbnM6IFtdLAogICAgICBmb3JtOiB7CiAgICAgICAgZGV2aWNlTmFtZTogJycsCiAgICAgICAgZGV2aWNlVHlwZTogJycsCiAgICAgICAgVHlwZV9EZXRhaWxfSWQ6ICcnLAogICAgICAgIGRlcGFydG1lbnQ6ICcnCiAgICAgIH0sCiAgICAgIGRldmljZVR5cGVPcHRpb25zOiBbXSwKICAgICAgY3VycmVudENvbXBvbmVudDogJycsCiAgICAgIHRpdGxlOiAnJywKICAgICAgY29sdW1uczogW10sCiAgICAgIHRiRGF0YTogW10sCiAgICAgIHRvdGFsOiAwLAogICAgICB0YWJsZUtleTogTWF0aC5yYW5kb20oKSwKICAgICAgdGJMb2FkaW5nOiBmYWxzZSwKICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIHNlbGVjdExpc3Q6IFtdLAogICAgICBrZXl3b3JkczogJycKICAgIH0KICB9LAogIGFzeW5jIGNyZWF0ZWQoKSB7CiAgICB0aGlzLnRiTG9hZGluZyA9IHRydWUKICAgIHRoaXMuc2VsZWN0TGlzdCA9IFtdCiAgICBhd2FpdCB0aGlzLmdldFRhYmxlQ29uZmlnKCdwbG1fZGV2aWNlX2xpc3QnKQogICAgdGhpcy5nZXRFcXVpcG1lbnRBc3NldFBhZ2VMaXN0KCkKICAgIHRoaXMuZ2V0RGljdGlvbmFyeURldGFpbExpc3RCeUNvZGUoKQogIH0sCiAgbWV0aG9kczogewogICAgY2xlYXJTZWxlYygpIHsKICAgICAgdGhpcy4kcmVmcy5keVRhYmxlLmNsZWFyU2VsZWN0aW9uKCkKICAgIH0sCiAgICBkZXZpY2VUeXBlQ2hhbmdlKGUpIHsKICAgICAgdGhpcy5mb3JtLlR5cGVfRGV0YWlsX0lkID0gJycKICAgICAgdGhpcy5kZXZpY2VJdGVtVHlwZU9wdGlvbnMgPSBbXQogICAgICBHZXREaWN0aW9uYXJ5RGV0YWlsTGlzdEJ5UGFyZW50SWQoZSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgdGhpcy5kZXZpY2VJdGVtVHlwZU9wdGlvbnMgPSByZXMuRGF0YQogICAgICB9KQogICAgfSwKICAgIC8vIOiOt+WPluiuvuWkh+exu+WeiwogICAgYXN5bmMgZ2V0RGljdGlvbmFyeURldGFpbExpc3RCeUNvZGUoKSB7CiAgICAgIGF3YWl0IEdldERpY3Rpb25hcnlEZXRhaWxMaXN0QnlDb2RlKHsgZGljdGlvbmFyeUNvZGU6ICdkZXZpY2VUeXBlJyB9KS50aGVuKChyZXMpID0+IHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgdGhpcy5kZXZpY2VUeXBlT3B0aW9ucyA9IHJlcy5EYXRhIHx8IFtdCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICAgIGNvbnNvbGUubG9nKCcgdGhpcy5vcHRpb25zR3JvdXBMaXN0JywgdGhpcy5vcHRpb25zR3JvdXBMaXN0KQogICAgfSwKICAgIGFzeW5jIGdldEVxdWlwbWVudEFzc2V0UGFnZUxpc3QoKSB7CiAgICAgIGF3YWl0IEdldEVxdWlwbWVudEFzc2V0UGFnZUxpc3QoewogICAgICAgIERpc3BsYXlfTmFtZTogdGhpcy5mb3JtLmRldmljZU5hbWUsCiAgICAgICAgRGV2aWNlX1R5cGVfSWQ6IHRoaXMuZm9ybS5kZXZpY2VUeXBlLAogICAgICAgIERldmljZV9UeXBlX0RldGFpbF9JZDogdGhpcy5mb3JtLlR5cGVfRGV0YWlsX0lkLAogICAgICAgIERlcGFydG1lbnQ6IHRoaXMuZm9ybS5kZXBhcnRtZW50LAogICAgICAgIFBhZ2U6IHRoaXMucXVlcnlJbmZvLlBhZ2UsCiAgICAgICAgUGFnZVNpemU6IHRoaXMucXVlcnlJbmZvLlBhZ2VTaXplCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICB0aGlzLnRiRGF0YSA9IHJlcy5EYXRhLkRhdGEKICAgICAgICAgIHRoaXMudG90YWwgPSByZXMuRGF0YS5Ub3RhbENvdW50CiAgICAgICAgICB0aGlzLnRiTG9hZGluZyA9IGZhbHNlCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICAgIGNvbnNvbGUubG9nKCcgdGhpcy5vcHRpb25zR3JvdXBMaXN0JywgdGhpcy5vcHRpb25zR3JvdXBMaXN0KQogICAgfSwKICAgIGdyaWRQYWdlQ2hhbmdlKHsgcGFnZSB9KSB7CiAgICAgIHRoaXMucXVlcnlJbmZvLlBhZ2UgPSBOdW1iZXIocGFnZSkKICAgICAgdGhpcy5nZXRFcXVpcG1lbnRBc3NldFBhZ2VMaXN0KCkKICAgIH0sCiAgICBncmlkU2l6ZUNoYW5nZSh7IHNpemUgfSkgewogICAgICB0aGlzLnF1ZXJ5SW5mby5QYWdlU2l6ZSA9IE51bWJlcihzaXplKQogICAgICB0aGlzLnF1ZXJ5SW5mby5QYWdlID0gMQogICAgICB0aGlzLmdldEVxdWlwbWVudEFzc2V0UGFnZUxpc3QoKQogICAgfSwKICAgIGhhbmRsZURldmljZSgpIHsKICAgICAgaWYgKHRoaXMuc2VsZWN0TGlzdC5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIG1lc3NhZ2U6ICfor7fpgInmi6norr7lpIcnLAogICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgIH0pCiAgICAgICAgcmV0dXJuCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5yb3dEYXRhLkRldmljZV9JZHMgPSB0aGlzLnNlbGVjdExpc3QKICAgICAgICBjb25zb2xlLmxvZyh0aGlzLnJvd0RhdGEsICd0aGlzLnJvd0RhdGEnKQoKICAgICAgICBBZGRXb3JraW5nUHJvY2Vzcyh0aGlzLnJvd0RhdGEpLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgbWVzc2FnZTogJ+WFs+iBlOaIkOWKnycsCiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIHRoaXMuJGVtaXQoJ2ZldGNoRGF0YScpCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICAgIH0pCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfQogICAgfSwKCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2UobGlzdCkgewogICAgICB0aGlzLnNlbGVjdExpc3QgPSBsaXN0Lm1hcCgoaSkgPT4gaS5JZCkKICAgICAgY29uc29sZS5sb2codGhpcy5zZWxlY3RMaXN0LCAndGhpcy5zZWxlY3RMaXN0JykKICAgIH0sCiAgICBoYW5kbGVBZGQoKSB7CiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9ICdkZXRhaWwnCiAgICAgIHRoaXMudGl0bGUgPSAn5paw5aKe6L2m6Ze0JwogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlCiAgICB9LAogICAgaGFuZGxlRWRpdChyb3cpIHsKICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ2RldGFpbCcKICAgICAgdGhpcy50aXRsZSA9ICfnvJbovpHovabpl7QnCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUKICAgICAgdGhpcy4kbmV4dFRpY2soKF8pID0+IHsKICAgICAgICB0aGlzLiRyZWZzWydjb250ZW50J10uaW5pdERhdGEocm93KQogICAgICB9KQogICAgfSwKICAgIC8vIGhhbmRsZURldGFpbChyb3cpIHsKICAgIC8vICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gImluZm8iOwogICAgLy8gICB0aGlzLnRpdGxlID0gIuafpeeciyI7CiAgICAvLyAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICAvLyAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7CiAgICAvLyAgICAgdGhpcy4kcmVmc1siY29udGVudCJdLmluaXREYXRhKHJvdy5JZCk7CiAgICAvLyAgIH0pOwogICAgLy8gfSwKCiAgICBoYW5kbGVTZWFyY2goKSB7CiAgICAgIHRoaXMuZ2V0RXF1aXBtZW50QXNzZXRQYWdlTGlzdCgpCiAgICAgIHRoaXMucXVlcnlJbmZvLlBhZ2UgPSAxCiAgICB9LAogICAgcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHt9CiAgICAgIHRoaXMuZ2V0RXF1aXBtZW50QXNzZXRQYWdlTGlzdCgpCiAgICAgIHRoaXMucXVlcnlJbmZvLlBhZ2UgPSAxCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["AssociatedDevice.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "AssociatedDevice.vue", "sourceRoot": "src/views/PRO/project-config/process-settings/component", "sourcesContent": ["<template>\n\n  <div>\n\n    <el-form label-width=\"80px\" :inline=\"true\">\n      <el-form-item>\n        <el-button type=\"primary\" @click=\"handleDevice\">关联设备</el-button>\n      </el-form-item>\n      <el-form-item label=\"设备名称\" prop=\"deviceName\">\n        <el-input v-model.trim=\"form.deviceName\" :clearbale=\"true\" />\n      </el-form-item>\n      <el-form-item label=\"设备类型\" prop=\"deviceType\">\n        <el-select v-model=\"form.deviceType\" clearable placeholder=\"请选择\" style=\"width: 100%\" @change=\"deviceTypeChange\">\n          <el-option v-for=\"item in deviceTypeOptions\" :key=\"item.Id\" :label=\"item.Display_Name\" :value=\"item.Id\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"设备子类\" prop=\"Type_Detail_Id\">\n        <el-select\n          v-model=\"form.Type_Detail_Id\"\n          clearable\n          placeholder=\"请选择\"\n          :disabled=\"!form.deviceType\"\n          style=\"width: 100%\"\n        >\n          <el-option v-for=\"item in deviceItemTypeOptions\" :key=\"item.Id\" :label=\"item.Display_Name\" :value=\"item.Id\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"所属部门\" prop=\"department\">\n        <el-input v-model.trim=\"form.department\" clearbale />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\n        <el-button @click=\"reset\">重置</el-button>\n      </el-form-item>\n\n    </el-form>\n\n    <div v-loading=\"tbLoading\" class=\"fff cs-z-tb-wrapper\" style=\"height: calc(100vh - 320px);\">\n      <dynamic-data-table\n        ref=\"dyTable\"\n        :key=\"tableKey\"\n        :columns=\"columns\"\n        :config=\"tbConfig\"\n        :data=\"tbData\"\n        :page=\"queryInfo.Page\"\n        :total=\"total\"\n        border\n        stripe\n        class=\"cs-plm-dy-table\"\n        @multiSelectedChange=\"handleSelectionChange\"\n        @gridPageChange=\"gridPageChange\"\n        @gridSizeChange=\"gridSizeChange\"\n        @tableSearch=\"tableSearch\"\n      >\n        <template slot=\"Director_UserName\" slot-scope=\"{ row }\">\n          <div>{{ row.Director_UserName || \"-\" }}</div>\n        </template>\n        <template slot=\"Working_Team_Names\" slot-scope=\"{ row }\">\n          <div>{{ row.Working_Team_Names || \"-\" }}</div>\n        </template>\n\n      </dynamic-data-table>\n    </div>\n  </div>\n\n</template>\n\n<script>\nimport getTbInfo from '@/mixins/PRO/get-table-info'\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'\nimport TopHeader from '@/components/TopHeader'\n// import detail from \"./component/detail\";\nimport { GetDictionaryDetailListByCode, GetDictionaryDetailListByParentId } from '@/api/sys'\nimport { GetEquipmentAssetPageList, AddWorkingProcess } from '@/api/PRO/technology-lib'\n\nexport default {\n  name: 'PROGroup',\n\n  components: {\n    DynamicDataTable,\n    TopHeader\n    // detail,\n  },\n  mixins: [getTbInfo],\n  props: {\n    rowData: {\n      type: Object,\n      default() {\n        return {}\n      }\n    }\n  },\n  data() {\n    return {\n      tbConfig: {\n        Pager_Align: 'center'\n      },\n      queryInfo: {\n        Page: 1,\n        PageSize: 10\n      },\n      deviceItemTypeOptions: [],\n      form: {\n        deviceName: '',\n        deviceType: '',\n        Type_Detail_Id: '',\n        department: ''\n      },\n      deviceTypeOptions: [],\n      currentComponent: '',\n      title: '',\n      columns: [],\n      tbData: [],\n      total: 0,\n      tableKey: Math.random(),\n      tbLoading: false,\n      dialogVisible: false,\n      selectList: [],\n      keywords: ''\n    }\n  },\n  async created() {\n    this.tbLoading = true\n    this.selectList = []\n    await this.getTableConfig('plm_device_list')\n    this.getEquipmentAssetPageList()\n    this.getDictionaryDetailListByCode()\n  },\n  methods: {\n    clearSelec() {\n      this.$refs.dyTable.clearSelection()\n    },\n    deviceTypeChange(e) {\n      this.form.Type_Detail_Id = ''\n      this.deviceItemTypeOptions = []\n      GetDictionaryDetailListByParentId(e).then((res) => {\n        this.deviceItemTypeOptions = res.Data\n      })\n    },\n    // 获取设备类型\n    async getDictionaryDetailListByCode() {\n      await GetDictionaryDetailListByCode({ dictionaryCode: 'deviceType' }).then((res) => {\n        if (res.IsSucceed) {\n          this.deviceTypeOptions = res.Data || []\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n      console.log(' this.optionsGroupList', this.optionsGroupList)\n    },\n    async getEquipmentAssetPageList() {\n      await GetEquipmentAssetPageList({\n        Display_Name: this.form.deviceName,\n        Device_Type_Id: this.form.deviceType,\n        Device_Type_Detail_Id: this.form.Type_Detail_Id,\n        Department: this.form.department,\n        Page: this.queryInfo.Page,\n        PageSize: this.queryInfo.PageSize\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data.Data\n          this.total = res.Data.TotalCount\n          this.tbLoading = false\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n      console.log(' this.optionsGroupList', this.optionsGroupList)\n    },\n    gridPageChange({ page }) {\n      this.queryInfo.Page = Number(page)\n      this.getEquipmentAssetPageList()\n    },\n    gridSizeChange({ size }) {\n      this.queryInfo.PageSize = Number(size)\n      this.queryInfo.Page = 1\n      this.getEquipmentAssetPageList()\n    },\n    handleDevice() {\n      if (this.selectList.length === 0) {\n        this.$message({\n          message: '请选择设备',\n          type: 'error'\n        })\n        return\n      } else {\n        this.rowData.Device_Ids = this.selectList\n        console.log(this.rowData, 'this.rowData')\n\n        AddWorkingProcess(this.rowData).then((res) => {\n          if (res.IsSucceed) {\n            this.$message({\n              message: '关联成功',\n              type: 'success'\n            })\n            this.$emit('fetchData')\n          } else {\n            this.$message({\n              message: res.Message,\n              type: 'error'\n            })\n          }\n        })\n      }\n    },\n\n    handleSelectionChange(list) {\n      this.selectList = list.map((i) => i.Id)\n      console.log(this.selectList, 'this.selectList')\n    },\n    handleAdd() {\n      this.currentComponent = 'detail'\n      this.title = '新增车间'\n      this.dialogVisible = true\n    },\n    handleEdit(row) {\n      this.currentComponent = 'detail'\n      this.title = '编辑车间'\n      this.dialogVisible = true\n      this.$nextTick((_) => {\n        this.$refs['content'].initData(row)\n      })\n    },\n    // handleDetail(row) {\n    //   this.currentComponent = \"info\";\n    //   this.title = \"查看\";\n    //   this.dialogVisible = true;\n    //   this.$nextTick((_) => {\n    //     this.$refs[\"content\"].initData(row.Id);\n    //   });\n    // },\n\n    handleSearch() {\n      this.getEquipmentAssetPageList()\n      this.queryInfo.Page = 1\n    },\n    reset() {\n      this.form = {}\n      this.getEquipmentAssetPageList()\n      this.queryInfo.Page = 1\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n// .cs-dialog {\n//   ::v-deep {\n//     .el-dialog__body {\n//       padding-top: 0;\n//     }\n//   }\n// }\n::v-deep {\n  .cs-top-header-box {\n    line-height: 0px;\n  }\n}\n\n::v-deep .pagination {\n  justify-content: flex-end !important;\n  margin-top: 12px !important;\n\n  .el-input--small .el-input__inner {\n    height: 28px;\n    line-height: 28px;\n  }\n}\n</style>\n"]}]}
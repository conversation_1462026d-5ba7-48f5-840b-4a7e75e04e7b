{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\ToleranceDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\ToleranceDialog.vue", "mtime": 1757468112827}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["SaveToleranceSetting", "data", "loading", "typeOptions", "label", "value", "form", "Length", "Demand", "Type", "rules", "required", "message", "methods", "init", "type", "typeId", "Id", "handleClose", "$emit", "submitForm", "_this", "$refs", "formRef", "validate", "valid", "then", "res", "IsSucceed", "$message", "success", "error", "Message", "finally"], "sources": ["src/views/PRO/project-config/project-quality/components/Dialog/ToleranceDialog.vue"], "sourcesContent": ["<template>\r\n  <el-form ref=\"formRef\" :model=\"form\" label-width=\"120px\" :rules=\"rules\">\r\n    <el-form-item label=\"长度\" prop=\"Length\">\r\n      <div class=\"cs-flex\">\r\n        <el-select v-model=\"form.Type\" style=\"margin-right: 10px;width: 100px;\" placeholder=\"请选择\">\r\n          <el-option v-for=\"item in typeOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n        </el-select>\r\n        <el-input-number v-model.number=\"form.Length\" :min=\"0\" class=\"cs-number-btn-hidden w100\" clearble />\r\n      </div>\r\n\r\n    </el-form-item>\r\n    <el-form-item label=\"公差要求\" prop=\"Demand\">\r\n      <el-input v-model=\"form.Demand\" placeholder=\"请输入公差要求\" />\r\n    </el-form-item>\r\n    <el-form-item style=\"text-align: right\">\r\n      <el-button @click=\"handleClose\">取消</el-button>\r\n      <el-button type=\"primary\" :loading=\"loading\" @click=\"submitForm\">确定</el-button>\r\n    </el-form-item>\r\n\r\n  </el-form>\r\n\r\n</template>\r\n\r\n<script>\r\nimport { SaveToleranceSetting } from '@/api/PRO/qualityInspect/quality-management'\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      typeOptions: [\r\n        { label: '<', value: 1 },\r\n        { label: '<=', value: 2 },\r\n        { label: '>', value: 3 },\r\n        { label: '>=', value: 4 },\r\n        { label: '=', value: 5 }\r\n      ],\r\n      form: {\r\n        Length: '',\r\n        Demand: '',\r\n        Type: 1\r\n      },\r\n      rules: {\r\n        Length: [{ required: true, message: '请选择长度' }],\r\n        Demand: [{ required: true, message: '请输入公差要求' }]\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    init(type, typeId, data) {\r\n      if (type === '编辑' && data) {\r\n        this.form.Id = data.Id\r\n        this.form.Length = data.Length\r\n        this.form.Demand = data.Demand\r\n        this.form.Type = data.Type\r\n      }\r\n    },\r\n    handleClose() {\r\n      this.$emit('close')\r\n    },\r\n    submitForm() {\r\n      this.$refs.formRef.validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true\r\n\r\n          SaveToleranceSetting(this.form).then(res => {\r\n            if (res.IsSucceed) {\r\n              this.$message.success('保存成功')\r\n              this.$emit('ToleranceRefresh')\r\n              this.handleClose()\r\n            } else {\r\n              this.$message.error(res.Message)\r\n            }\r\n          }).finally(() => {\r\n            this.loading = false\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.cs-flex {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,SAAAA,oBAAA;AACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,WAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,IAAA;QACAC,MAAA;QACAC,MAAA;QACAC,IAAA;MACA;MACAC,KAAA;QACAH,MAAA;UAAAI,QAAA;UAAAC,OAAA;QAAA;QACAJ,MAAA;UAAAG,QAAA;UAAAC,OAAA;QAAA;MACA;IACA;EACA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAAC,IAAA,EAAAC,MAAA,EAAAf,IAAA;MACA,IAAAc,IAAA,aAAAd,IAAA;QACA,KAAAK,IAAA,CAAAW,EAAA,GAAAhB,IAAA,CAAAgB,EAAA;QACA,KAAAX,IAAA,CAAAC,MAAA,GAAAN,IAAA,CAAAM,MAAA;QACA,KAAAD,IAAA,CAAAE,MAAA,GAAAP,IAAA,CAAAO,MAAA;QACA,KAAAF,IAAA,CAAAG,IAAA,GAAAR,IAAA,CAAAQ,IAAA;MACA;IACA;IACAS,WAAA,WAAAA,YAAA;MACA,KAAAC,KAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAJ,KAAA,CAAAnB,OAAA;UAEAF,oBAAA,CAAAqB,KAAA,CAAAf,IAAA,EAAAoB,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,SAAA;cACAP,KAAA,CAAAQ,QAAA,CAAAC,OAAA;cACAT,KAAA,CAAAF,KAAA;cACAE,KAAA,CAAAH,WAAA;YACA;cACAG,KAAA,CAAAQ,QAAA,CAAAE,KAAA,CAAAJ,GAAA,CAAAK,OAAA;YACA;UACA,GAAAC,OAAA;YACAZ,KAAA,CAAAnB,OAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\utils\\request.js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\utils\\request.js", "mtime": 1757572678725}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "Message", "store", "getToken", "<PERSON><PERSON>", "baseUrl", "platformUrl", "dfUrl", "prototype", "$config", "window", "URLGLOBAL", "baseUrlStr", "A<PERSON>os", "option", "url", "indexOf", "replaceAll", "service", "create", "baseURL", "timeout", "interceptors", "request", "use", "config", "getters", "token", "headers", "localStorage", "getItem", "Last_Working_Object_Id", "error", "console", "log", "Promise", "reject", "response", "res", "data", "StatusCode", "dispatch", "then", "location", "reload", "message", "type", "duration", "status", "includes", "resolve", "catch"], "sources": ["D:/project/platform_framework_master/platform_framework/Platform/Frontend/SubAppProduce/src/utils/request.js"], "sourcesContent": ["import axios from 'axios'\r\nimport { Message } from 'element-ui'\r\nimport store from '@/store'\r\nimport { getToken } from '@/utils/auth'\r\nimport Vue from 'vue'\r\nimport { baseUrl, platformUrl, dfUrl } from '@/utils/baseurl'\r\n\r\n// create an axios instance\r\nVue.prototype.$config = window.URLGLOBAL\r\n\r\n/* setTimeout(() => {\r\n  delete window.URLGLOBAL // 用完之后删除\r\n}, 500) */\r\nlet baseUrlStr = baseUrl()\r\n\r\nfunction Axios(option) {\r\n  if (option.url.indexOf('/SYS/') > -1 && (option.url.indexOf('/SYS/ApiCadChaiTu/') === -1 && option.url.indexOf('/SYS/FileDownload/') === -1 && option.url.indexOf('/SYS/Sys_File_Type_Power/') === -1 && option.url.indexOf('/SYS/Sys_File_Type_SMS/') === -1 && option.url.indexOf('/SYS/Sys_File_Type_Version/') === -1 && option.url.indexOf('/SYS/Sys_File/') === -1 && option.url.indexOf('/SYS/Sys_FileType/') === -1 && option.url.indexOf('/SYS/FlowInstances/CancelFlow') === -1)) {\r\n    option.url = option.url.replaceAll('/SYS/', '/Platform/').replaceAll('/Sys/', '/Platform/')\r\n    baseUrlStr = platformUrl() // process.env.VUE_APP_PLATFORM_API\r\n  } else if (option.url.indexOf('/Platform/') > -1) {\r\n    baseUrlStr = platformUrl()\r\n  } else if (option.url === '/DF/EQPTAsset/V2/GetEquipmentAssetPageList') {\r\n    baseUrlStr = dfUrl()\r\n  } else {\r\n    baseUrlStr = baseUrl()\r\n  }\r\n  const service = axios.create({\r\n    // baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url\r\n    baseURL: baseUrlStr,\r\n    // withCredentials: true, // send cookies when cross-domain requests\r\n    timeout: option.timeout || 60 * 1000 * 20\r\n  })\r\n\r\n  // request interceptor\r\n  service.interceptors.request.use(\r\n    config => {\r\n      // do something before request is sent\r\n      if (store.getters.token || getToken()) {\r\n        // let each request carry token\r\n        // ['X-Token'] is a custom headers key\r\n        // please modify it according to the actual situation\r\n        config.headers['Authorization'] = getToken()\r\n      }\r\n      // if (store.getters.Last_Working_Object_Id) {\r\n      //   config.headers.Last_Working_Object_Id = store.getters.Last_Working_Object_Id\r\n      // }\r\n      if (localStorage.getItem('Last_Working_Object_Id')) {\r\n        config.headers.Last_Working_Object_Id = localStorage.getItem('Last_Working_Object_Id')\r\n      }\r\n      return config\r\n    },\r\n    error => {\r\n      // do something with request error\r\n      console.log(error) // for debug\r\n      return Promise.reject(error)\r\n    }\r\n  )\r\n\r\n  // response interceptor\r\n  service.interceptors.response.use(\r\n    /**\r\n     * If you want to get http information such as headers or status\r\n     * Please return  response => response\r\n     */\r\n\r\n    /**\r\n     * Determine the request status by custom code\r\n     * Here is just an example\r\n     * You can also judge the status by HTTP Status Code\r\n     */\r\n    response => {\r\n      const res = response.data\r\n\r\n      // if (res.StatusCode === 502 || res.StatusCode === 502 || res.StatusCode === 501 || res.StatusCode === 500) {\r\n      //   // 服务端返回false，同时需要获取数据\r\n      //   // to re-login\r\n      //   // MessageBox.confirm('You have been logged out, you can cancel to stay on this page, or log in again', 'Confirm logout', {\r\n      //   //   confirmButtonText: 'Re-Login',\r\n      //   //   cancelButtonText: 'Cancel',\r\n      //   //   type: 'warning'\r\n      //   // }).then(() => {\r\n      //   //   store.dispatch('user/resetToken').then(() => {\r\n      //   //     location.reload()\r\n      //   //   })\r\n      //   // })\r\n      //   return res\r\n      // } else if (res.StatusCode === 200 || res.StatusCode === 502 || res.StatusCode === 502 || res.StatusCode === 501 || res.StatusCode === 500) {\r\n      //   // Message({\r\n      //   //   message: res.Message || 'Error',\r\n      //   //   type: 'error',\r\n      //   //   duration: 5 * 1000\r\n      //   // })\r\n      // } else\r\n      if (res.StatusCode === 401) {\r\n        store.dispatch('user/resetToken').then(() => {\r\n          location.reload()\r\n        })\r\n      } else if (res.StatusCode === 502) { // res.StatusCode === 501 ||\r\n        Message({\r\n          message: res.Message || 'Error',\r\n          type: 'error',\r\n          duration: 5 * 1000\r\n        })\r\n      } else {\r\n        return res\r\n        // return Promise.reject(new Error(res.Message || ' '))\r\n      }\r\n    },\r\n    error => {\r\n      console.log('err', error) // for debug\r\n      let message = ''\r\n      let status = null\r\n      if (error.response) {\r\n        // 有响应，但响应状态码不在 2xx 范围内\r\n        status = error.response.status\r\n        switch (status) {\r\n          case 401:\r\n            message = '登录过期，请重新登录'\r\n            break\r\n          case 403:\r\n            message = '无权访问'\r\n            break\r\n          case 404:\r\n            message = '请求地址错误'\r\n            break\r\n          case 500:\r\n            message = '服务器出现错误'\r\n            break\r\n          default:\r\n            message = '网络问题或连接超时'\r\n            break\r\n        }\r\n      } else if (error.request) {\r\n        // 请求已发送，但没有收到响应\r\n        message = '没有响应或连接超时'\r\n      } else {\r\n        // 发生了错误，请求无法完成\r\n        message = error.message\r\n        // 判断是否是超时错误\r\n        if (error.message.includes('timeout')) {\r\n          message = '网络问题或连接超时'\r\n          // 在这里可以进行相关处理，例如重新发送请求\r\n        }\r\n      }\r\n      Message({\r\n        message: message,\r\n        type: 'error',\r\n        duration: 5 * 1000\r\n      })\r\n      if (status === 401) {\r\n        store.dispatch('user/resetToken').then(() => {\r\n          location.reload()\r\n        })\r\n      }\r\n      return Promise.reject(error)\r\n    }\r\n  )\r\n  // 请求处理\r\n  return new Promise((resolve, reject) => {\r\n    service(option).then(res => {\r\n      resolve(res)\r\n    }).catch(error => {\r\n      reject(error)\r\n    })\r\n  })\r\n}\r\n\r\nexport default Axios\r\n"], "mappings": ";;;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,YAAY;AACpC,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,QAAQ,QAAQ,cAAc;AACvC,OAAOC,GAAG,MAAM,KAAK;AACrB,SAASC,OAAO,EAAEC,WAAW,EAAEC,KAAK,QAAQ,iBAAiB;;AAE7D;AACAH,GAAG,CAACI,SAAS,CAACC,OAAO,GAAGC,MAAM,CAACC,SAAS;;AAExC;AACA;AACA;AACA,IAAIC,UAAU,GAAGP,OAAO,CAAC,CAAC;AAE1B,SAASQ,KAAKA,CAACC,MAAM,EAAE;EACrB,IAAIA,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAKF,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,IAAIF,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,IAAIF,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC,IAAIF,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC,IAAIF,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC,IAAIF,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,IAAIF,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,IAAIF,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,+BAA+B,CAAC,KAAK,CAAC,CAAE,EAAE;IAC1dF,MAAM,CAACC,GAAG,GAAGD,MAAM,CAACC,GAAG,CAACE,UAAU,CAAC,OAAO,EAAE,YAAY,CAAC,CAACA,UAAU,CAAC,OAAO,EAAE,YAAY,CAAC;IAC3FL,UAAU,GAAGN,WAAW,CAAC,CAAC,EAAC;EAC7B,CAAC,MAAM,IAAIQ,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE;IAChDJ,UAAU,GAAGN,WAAW,CAAC,CAAC;EAC5B,CAAC,MAAM,IAAIQ,MAAM,CAACC,GAAG,KAAK,4CAA4C,EAAE;IACtEH,UAAU,GAAGL,KAAK,CAAC,CAAC;EACtB,CAAC,MAAM;IACLK,UAAU,GAAGP,OAAO,CAAC,CAAC;EACxB;EACA,IAAMa,OAAO,GAAGlB,KAAK,CAACmB,MAAM,CAAC;IAC3B;IACAC,OAAO,EAAER,UAAU;IACnB;IACAS,OAAO,EAAEP,MAAM,CAACO,OAAO,IAAI,EAAE,GAAG,IAAI,GAAG;EACzC,CAAC,CAAC;;EAEF;EACAH,OAAO,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9B,UAAAC,MAAM,EAAI;IACR;IACA,IAAIvB,KAAK,CAACwB,OAAO,CAACC,KAAK,IAAIxB,QAAQ,CAAC,CAAC,EAAE;MACrC;MACA;MACA;MACAsB,MAAM,CAACG,OAAO,CAAC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,CAAC;IAC9C;IACA;IACA;IACA;IACA,IAAI0B,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC,EAAE;MAClDL,MAAM,CAACG,OAAO,CAACG,sBAAsB,GAAGF,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;IACxF;IACA,OAAOL,MAAM;EACf,CAAC,EACD,UAAAO,KAAK,EAAI;IACP;IACAC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAC;IACnB,OAAOG,OAAO,CAACC,MAAM,CAACJ,KAAK,CAAC;EAC9B,CACF,CAAC;;EAED;EACAd,OAAO,CAACI,YAAY,CAACe,QAAQ,CAACb,GAAG;EAC/B;AACJ;AACA;AACA;;EAEI;AACJ;AACA;AACA;AACA;EACI,UAAAa,QAAQ,EAAI;IACV,IAAMC,GAAG,GAAGD,QAAQ,CAACE,IAAI;;IAEzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAID,GAAG,CAACE,UAAU,KAAK,GAAG,EAAE;MAC1BtC,KAAK,CAACuC,QAAQ,CAAC,iBAAiB,CAAC,CAACC,IAAI,CAAC,YAAM;QAC3CC,QAAQ,CAACC,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIN,GAAG,CAACE,UAAU,KAAK,GAAG,EAAE;MAAE;MACnCvC,OAAO,CAAC;QACN4C,OAAO,EAAEP,GAAG,CAACrC,OAAO,IAAI,OAAO;QAC/B6C,IAAI,EAAE,OAAO;QACbC,QAAQ,EAAE,CAAC,GAAG;MAChB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAOT,GAAG;MACV;IACF;EACF,CAAC,EACD,UAAAN,KAAK,EAAI;IACPC,OAAO,CAACC,GAAG,CAAC,KAAK,EAAEF,KAAK,CAAC,EAAC;IAC1B,IAAIa,OAAO,GAAG,EAAE;IAChB,IAAIG,MAAM,GAAG,IAAI;IACjB,IAAIhB,KAAK,CAACK,QAAQ,EAAE;MAClB;MACAW,MAAM,GAAGhB,KAAK,CAACK,QAAQ,CAACW,MAAM;MAC9B,QAAQA,MAAM;QACZ,KAAK,GAAG;UACNH,OAAO,GAAG,YAAY;UACtB;QACF,KAAK,GAAG;UACNA,OAAO,GAAG,MAAM;UAChB;QACF,KAAK,GAAG;UACNA,OAAO,GAAG,QAAQ;UAClB;QACF,KAAK,GAAG;UACNA,OAAO,GAAG,SAAS;UACnB;QACF;UACEA,OAAO,GAAG,WAAW;UACrB;MACJ;IACF,CAAC,MAAM,IAAIb,KAAK,CAACT,OAAO,EAAE;MACxB;MACAsB,OAAO,GAAG,WAAW;IACvB,CAAC,MAAM;MACL;MACAA,OAAO,GAAGb,KAAK,CAACa,OAAO;MACvB;MACA,IAAIb,KAAK,CAACa,OAAO,CAACI,QAAQ,CAAC,SAAS,CAAC,EAAE;QACrCJ,OAAO,GAAG,WAAW;QACrB;MACF;IACF;IACA5C,OAAO,CAAC;MACN4C,OAAO,EAAEA,OAAO;MAChBC,IAAI,EAAE,OAAO;MACbC,QAAQ,EAAE,CAAC,GAAG;IAChB,CAAC,CAAC;IACF,IAAIC,MAAM,KAAK,GAAG,EAAE;MAClB9C,KAAK,CAACuC,QAAQ,CAAC,iBAAiB,CAAC,CAACC,IAAI,CAAC,YAAM;QAC3CC,QAAQ,CAACC,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC;IACJ;IACA,OAAOT,OAAO,CAACC,MAAM,CAACJ,KAAK,CAAC;EAC9B,CACF,CAAC;EACD;EACA,OAAO,IAAIG,OAAO,CAAC,UAACe,OAAO,EAAEd,MAAM,EAAK;IACtClB,OAAO,CAACJ,MAAM,CAAC,CAAC4B,IAAI,CAAC,UAAAJ,GAAG,EAAI;MAC1BY,OAAO,CAACZ,GAAG,CAAC;IACd,CAAC,CAAC,CAACa,KAAK,CAAC,UAAAnB,KAAK,EAAI;MAChBI,MAAM,CAACJ,KAAK,CAAC;IACf,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,eAAenB,KAAK", "ignoreList": []}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\utils\\request.js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\utils\\request.js", "mtime": 1757468112066}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuaW5jbHVkZXMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmV4ZWMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaW5jbHVkZXMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcucmVwbGFjZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5yZXBsYWNlLWFsbC5qcyI7CmltcG9ydCBheGlvcyBmcm9tICdheGlvcyc7CmltcG9ydCB7IE1lc3NhZ2UgfSBmcm9tICdlbGVtZW50LXVpJzsKaW1wb3J0IHN0b3JlIGZyb20gJ0Avc3RvcmUnOwppbXBvcnQgeyBnZXRUb2tlbiB9IGZyb20gJ0AvdXRpbHMvYXV0aCc7CmltcG9ydCBWdWUgZnJvbSAndnVlJzsKaW1wb3J0IHsgYmFzZVVybCwgcGxhdGZvcm1VcmwsIGRmVXJsIH0gZnJvbSAnQC91dGlscy9iYXNldXJsJzsKCi8vIGNyZWF0ZSBhbiBheGlvcyBpbnN0YW5jZQpWdWUucHJvdG90eXBlLiRjb25maWcgPSB3aW5kb3cuVVJMR0xPQkFMOwoKLyogc2V0VGltZW91dCgoKSA9PiB7DQogIGRlbGV0ZSB3aW5kb3cuVVJMR0xPQkFMIC8vIOeUqOWujOS5i+WQjuWIoOmZpA0KfSwgNTAwKSAqLwp2YXIgYmFzZVVybFN0ciA9IGJhc2VVcmwoKTsKZnVuY3Rpb24gQXhpb3Mob3B0aW9uKSB7CiAgaWYgKG9wdGlvbi51cmwuaW5kZXhPZignL1NZUy8nKSA+IC0xICYmIG9wdGlvbi51cmwuaW5kZXhPZignL1NZUy9BcGlDYWRDaGFpVHUvJykgPT09IC0xICYmIG9wdGlvbi51cmwuaW5kZXhPZignL1NZUy9GaWxlRG93bmxvYWQvJykgPT09IC0xICYmIG9wdGlvbi51cmwuaW5kZXhPZignL1NZUy9TeXNfRmlsZV9UeXBlX1Bvd2VyLycpID09PSAtMSAmJiBvcHRpb24udXJsLmluZGV4T2YoJy9TWVMvU3lzX0ZpbGVfVHlwZV9TTVMvJykgPT09IC0xICYmIG9wdGlvbi51cmwuaW5kZXhPZignL1NZUy9TeXNfRmlsZV9UeXBlX1ZlcnNpb24vJykgPT09IC0xICYmIG9wdGlvbi51cmwuaW5kZXhPZignL1NZUy9TeXNfRmlsZS8nKSA9PT0gLTEgJiYgb3B0aW9uLnVybC5pbmRleE9mKCcvU1lTL1N5c19GaWxlVHlwZS8nKSA9PT0gLTEgJiYgb3B0aW9uLnVybC5pbmRleE9mKCcvU1lTL0Zsb3dJbnN0YW5jZXMvVmVyaWZpY2F0aW9uJykgPT09IC0xICYmIG9wdGlvbi51cmwuaW5kZXhPZignL1NZUy9GbG93SW5zdGFuY2VzL0NhbmNlbEZsb3cnKSA9PT0gLTEpIHsKICAgIG9wdGlvbi51cmwgPSBvcHRpb24udXJsLnJlcGxhY2VBbGwoJy9TWVMvJywgJy9QbGF0Zm9ybS8nKS5yZXBsYWNlQWxsKCcvU3lzLycsICcvUGxhdGZvcm0vJyk7CiAgICBiYXNlVXJsU3RyID0gcGxhdGZvcm1VcmwoKTsgLy8gcHJvY2Vzcy5lbnYuVlVFX0FQUF9QTEFURk9STV9BUEkKICB9IGVsc2UgaWYgKG9wdGlvbi51cmwuaW5kZXhPZignL1BsYXRmb3JtLycpID4gLTEpIHsKICAgIGJhc2VVcmxTdHIgPSBwbGF0Zm9ybVVybCgpOwogIH0gZWxzZSBpZiAob3B0aW9uLnVybCA9PT0gJy9ERi9FUVBUQXNzZXQvVjIvR2V0RXF1aXBtZW50QXNzZXRQYWdlTGlzdCcpIHsKICAgIGJhc2VVcmxTdHIgPSBkZlVybCgpOwogIH0gZWxzZSB7CiAgICBiYXNlVXJsU3RyID0gYmFzZVVybCgpOwogIH0KICB2YXIgc2VydmljZSA9IGF4aW9zLmNyZWF0ZSh7CiAgICAvLyBiYXNlVVJMOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJLCAvLyB1cmwgPSBiYXNlIHVybCArIHJlcXVlc3QgdXJsCiAgICBiYXNlVVJMOiBiYXNlVXJsU3RyLAogICAgLy8gd2l0aENyZWRlbnRpYWxzOiB0cnVlLCAvLyBzZW5kIGNvb2tpZXMgd2hlbiBjcm9zcy1kb21haW4gcmVxdWVzdHMKICAgIHRpbWVvdXQ6IG9wdGlvbi50aW1lb3V0IHx8IDYwICogMTAwMCAqIDIwCiAgfSk7CgogIC8vIHJlcXVlc3QgaW50ZXJjZXB0b3IKICBzZXJ2aWNlLmludGVyY2VwdG9ycy5yZXF1ZXN0LnVzZShmdW5jdGlvbiAoY29uZmlnKSB7CiAgICAvLyBkbyBzb21ldGhpbmcgYmVmb3JlIHJlcXVlc3QgaXMgc2VudAogICAgaWYgKHN0b3JlLmdldHRlcnMudG9rZW4gfHwgZ2V0VG9rZW4oKSkgewogICAgICAvLyBsZXQgZWFjaCByZXF1ZXN0IGNhcnJ5IHRva2VuCiAgICAgIC8vIFsnWC1Ub2tlbiddIGlzIGEgY3VzdG9tIGhlYWRlcnMga2V5CiAgICAgIC8vIHBsZWFzZSBtb2RpZnkgaXQgYWNjb3JkaW5nIHRvIHRoZSBhY3R1YWwgc2l0dWF0aW9uCiAgICAgIGNvbmZpZy5oZWFkZXJzWydBdXRob3JpemF0aW9uJ10gPSBnZXRUb2tlbigpOwogICAgfQogICAgLy8gaWYgKHN0b3JlLmdldHRlcnMuTGFzdF9Xb3JraW5nX09iamVjdF9JZCkgewogICAgLy8gICBjb25maWcuaGVhZGVycy5MYXN0X1dvcmtpbmdfT2JqZWN0X0lkID0gc3RvcmUuZ2V0dGVycy5MYXN0X1dvcmtpbmdfT2JqZWN0X0lkCiAgICAvLyB9CiAgICBpZiAobG9jYWxTdG9yYWdlLmdldEl0ZW0oJ0xhc3RfV29ya2luZ19PYmplY3RfSWQnKSkgewogICAgICBjb25maWcuaGVhZGVycy5MYXN0X1dvcmtpbmdfT2JqZWN0X0lkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ0xhc3RfV29ya2luZ19PYmplY3RfSWQnKTsKICAgIH0KICAgIHJldHVybiBjb25maWc7CiAgfSwgZnVuY3Rpb24gKGVycm9yKSB7CiAgICAvLyBkbyBzb21ldGhpbmcgd2l0aCByZXF1ZXN0IGVycm9yCiAgICBjb25zb2xlLmxvZyhlcnJvcik7IC8vIGZvciBkZWJ1ZwogICAgcmV0dXJuIFByb21pc2UucmVqZWN0KGVycm9yKTsKICB9KTsKCiAgLy8gcmVzcG9uc2UgaW50ZXJjZXB0b3IKICBzZXJ2aWNlLmludGVyY2VwdG9ycy5yZXNwb25zZS51c2UoCiAgLyoqDQogICAqIElmIHlvdSB3YW50IHRvIGdldCBodHRwIGluZm9ybWF0aW9uIHN1Y2ggYXMgaGVhZGVycyBvciBzdGF0dXMNCiAgICogUGxlYXNlIHJldHVybiAgcmVzcG9uc2UgPT4gcmVzcG9uc2UNCiAgICovCgogIC8qKg0KICAgKiBEZXRlcm1pbmUgdGhlIHJlcXVlc3Qgc3RhdHVzIGJ5IGN1c3RvbSBjb2RlDQogICAqIEhlcmUgaXMganVzdCBhbiBleGFtcGxlDQogICAqIFlvdSBjYW4gYWxzbyBqdWRnZSB0aGUgc3RhdHVzIGJ5IEhUVFAgU3RhdHVzIENvZGUNCiAgICovCiAgZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICB2YXIgcmVzID0gcmVzcG9uc2UuZGF0YTsKCiAgICAvLyBpZiAocmVzLlN0YXR1c0NvZGUgPT09IDUwMiB8fCByZXMuU3RhdHVzQ29kZSA9PT0gNTAyIHx8IHJlcy5TdGF0dXNDb2RlID09PSA1MDEgfHwgcmVzLlN0YXR1c0NvZGUgPT09IDUwMCkgewogICAgLy8gICAvLyDmnI3liqHnq6/ov5Tlm55mYWxzZe+8jOWQjOaXtumcgOimgeiOt+WPluaVsOaNrgogICAgLy8gICAvLyB0byByZS1sb2dpbgogICAgLy8gICAvLyBNZXNzYWdlQm94LmNvbmZpcm0oJ1lvdSBoYXZlIGJlZW4gbG9nZ2VkIG91dCwgeW91IGNhbiBjYW5jZWwgdG8gc3RheSBvbiB0aGlzIHBhZ2UsIG9yIGxvZyBpbiBhZ2FpbicsICdDb25maXJtIGxvZ291dCcsIHsKICAgIC8vICAgLy8gICBjb25maXJtQnV0dG9uVGV4dDogJ1JlLUxvZ2luJywKICAgIC8vICAgLy8gICBjYW5jZWxCdXR0b25UZXh0OiAnQ2FuY2VsJywKICAgIC8vICAgLy8gICB0eXBlOiAnd2FybmluZycKICAgIC8vICAgLy8gfSkudGhlbigoKSA9PiB7CiAgICAvLyAgIC8vICAgc3RvcmUuZGlzcGF0Y2goJ3VzZXIvcmVzZXRUb2tlbicpLnRoZW4oKCkgPT4gewogICAgLy8gICAvLyAgICAgbG9jYXRpb24ucmVsb2FkKCkKICAgIC8vICAgLy8gICB9KQogICAgLy8gICAvLyB9KQogICAgLy8gICByZXR1cm4gcmVzCiAgICAvLyB9IGVsc2UgaWYgKHJlcy5TdGF0dXNDb2RlID09PSAyMDAgfHwgcmVzLlN0YXR1c0NvZGUgPT09IDUwMiB8fCByZXMuU3RhdHVzQ29kZSA9PT0gNTAyIHx8IHJlcy5TdGF0dXNDb2RlID09PSA1MDEgfHwgcmVzLlN0YXR1c0NvZGUgPT09IDUwMCkgewogICAgLy8gICAvLyBNZXNzYWdlKHsKICAgIC8vICAgLy8gICBtZXNzYWdlOiByZXMuTWVzc2FnZSB8fCAnRXJyb3InLAogICAgLy8gICAvLyAgIHR5cGU6ICdlcnJvcicsCiAgICAvLyAgIC8vICAgZHVyYXRpb246IDUgKiAxMDAwCiAgICAvLyAgIC8vIH0pCiAgICAvLyB9IGVsc2UKICAgIGlmIChyZXMuU3RhdHVzQ29kZSA9PT0gNDAxKSB7CiAgICAgIHN0b3JlLmRpc3BhdGNoKCd1c2VyL3Jlc2V0VG9rZW4nKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBsb2NhdGlvbi5yZWxvYWQoKTsKICAgICAgfSk7CiAgICB9IGVsc2UgaWYgKHJlcy5TdGF0dXNDb2RlID09PSA1MDIpIHsKICAgICAgLy8gcmVzLlN0YXR1c0NvZGUgPT09IDUwMSB8fAogICAgICBNZXNzYWdlKHsKICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSB8fCAnRXJyb3InLAogICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgZHVyYXRpb246IDUgKiAxMDAwCiAgICAgIH0pOwogICAgfSBlbHNlIHsKICAgICAgcmV0dXJuIHJlczsKICAgICAgLy8gcmV0dXJuIFByb21pc2UucmVqZWN0KG5ldyBFcnJvcihyZXMuTWVzc2FnZSB8fCAnICcpKQogICAgfQogIH0sIGZ1bmN0aW9uIChlcnJvcikgewogICAgY29uc29sZS5sb2coJ2VycicsIGVycm9yKTsgLy8gZm9yIGRlYnVnCiAgICB2YXIgbWVzc2FnZSA9ICcnOwogICAgdmFyIHN0YXR1cyA9IG51bGw7CiAgICBpZiAoZXJyb3IucmVzcG9uc2UpIHsKICAgICAgLy8g5pyJ5ZON5bqU77yM5L2G5ZON5bqU54q25oCB56CB5LiN5ZyoIDJ4eCDojIPlm7TlhoUKICAgICAgc3RhdHVzID0gZXJyb3IucmVzcG9uc2Uuc3RhdHVzOwogICAgICBzd2l0Y2ggKHN0YXR1cykgewogICAgICAgIGNhc2UgNDAxOgogICAgICAgICAgbWVzc2FnZSA9ICfnmbvlvZXov4fmnJ/vvIzor7fph43mlrDnmbvlvZUnOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSA0MDM6CiAgICAgICAgICBtZXNzYWdlID0gJ+aXoOadg+iuv+mXric7CiAgICAgICAgICBicmVhazsKICAgICAgICBjYXNlIDQwNDoKICAgICAgICAgIG1lc3NhZ2UgPSAn6K+35rGC5Zyw5Z2A6ZSZ6K+vJzsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgNTAwOgogICAgICAgICAgbWVzc2FnZSA9ICfmnI3liqHlmajlh7rnjrDplJnor68nOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgZGVmYXVsdDoKICAgICAgICAgIG1lc3NhZ2UgPSAn572R57uc6Zeu6aKY5oiW6L+e5o6l6LaF5pe2JzsKICAgICAgICAgIGJyZWFrOwogICAgICB9CiAgICB9IGVsc2UgaWYgKGVycm9yLnJlcXVlc3QpIHsKICAgICAgLy8g6K+35rGC5bey5Y+R6YCB77yM5L2G5rKh5pyJ5pS25Yiw5ZON5bqUCiAgICAgIG1lc3NhZ2UgPSAn5rKh5pyJ5ZON5bqU5oiW6L+e5o6l6LaF5pe2JzsKICAgIH0gZWxzZSB7CiAgICAgIC8vIOWPkeeUn+S6humUmeivr++8jOivt+axguaXoOazleWujOaIkAogICAgICBtZXNzYWdlID0gZXJyb3IubWVzc2FnZTsKICAgICAgLy8g5Yik5pat5piv5ZCm5piv6LaF5pe26ZSZ6K+vCiAgICAgIGlmIChlcnJvci5tZXNzYWdlLmluY2x1ZGVzKCd0aW1lb3V0JykpIHsKICAgICAgICBtZXNzYWdlID0gJ+e9kee7nOmXrumimOaIlui/nuaOpei2heaXtic7CiAgICAgICAgLy8g5Zyo6L+Z6YeM5Y+v5Lul6L+b6KGM55u45YWz5aSE55CG77yM5L6L5aaC6YeN5paw5Y+R6YCB6K+35rGCCiAgICAgIH0KICAgIH0KICAgIE1lc3NhZ2UoewogICAgICBtZXNzYWdlOiBtZXNzYWdlLAogICAgICB0eXBlOiAnZXJyb3InLAogICAgICBkdXJhdGlvbjogNSAqIDEwMDAKICAgIH0pOwogICAgaWYgKHN0YXR1cyA9PT0gNDAxKSB7CiAgICAgIHN0b3JlLmRpc3BhdGNoKCd1c2VyL3Jlc2V0VG9rZW4nKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBsb2NhdGlvbi5yZWxvYWQoKTsKICAgICAgfSk7CiAgICB9CiAgICByZXR1cm4gUHJvbWlzZS5yZWplY3QoZXJyb3IpOwogIH0pOwogIC8vIOivt+axguWkhOeQhgogIHJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbiAocmVzb2x2ZSwgcmVqZWN0KSB7CiAgICBzZXJ2aWNlKG9wdGlvbikudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgIHJlc29sdmUocmVzKTsKICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgewogICAgICByZWplY3QoZXJyb3IpOwogICAgfSk7CiAgfSk7Cn0KZXhwb3J0IGRlZmF1bHQgQXhpb3M7"}, {"version": 3, "names": ["axios", "Message", "store", "getToken", "<PERSON><PERSON>", "baseUrl", "platformUrl", "dfUrl", "prototype", "$config", "window", "URLGLOBAL", "baseUrlStr", "A<PERSON>os", "option", "url", "indexOf", "replaceAll", "service", "create", "baseURL", "timeout", "interceptors", "request", "use", "config", "getters", "token", "headers", "localStorage", "getItem", "Last_Working_Object_Id", "error", "console", "log", "Promise", "reject", "response", "res", "data", "StatusCode", "dispatch", "then", "location", "reload", "message", "type", "duration", "status", "includes", "resolve", "catch"], "sources": ["D:/project/platform_framework_master/platform_framework/Platform/Frontend/SubAppProduce/src/utils/request.js"], "sourcesContent": ["import axios from 'axios'\r\nimport { Message } from 'element-ui'\r\nimport store from '@/store'\r\nimport { getToken } from '@/utils/auth'\r\nimport Vue from 'vue'\r\nimport { baseUrl, platformUrl, dfUrl } from '@/utils/baseurl'\r\n\r\n// create an axios instance\r\nVue.prototype.$config = window.URLGLOBAL\r\n\r\n/* setTimeout(() => {\r\n  delete window.URLGLOBAL // 用完之后删除\r\n}, 500) */\r\nlet baseUrlStr = baseUrl()\r\n\r\nfunction Axios(option) {\r\n  if (option.url.indexOf('/SYS/') > -1 && (option.url.indexOf('/SYS/ApiCadChaiTu/') === -1 && option.url.indexOf('/SYS/FileDownload/') === -1 && option.url.indexOf('/SYS/Sys_File_Type_Power/') === -1 && option.url.indexOf('/SYS/Sys_File_Type_SMS/') === -1 && option.url.indexOf('/SYS/Sys_File_Type_Version/') === -1 && option.url.indexOf('/SYS/Sys_File/') === -1 && option.url.indexOf('/SYS/Sys_FileType/') === -1 && option.url.indexOf('/SYS/FlowInstances/Verification') === -1 && option.url.indexOf('/SYS/FlowInstances/CancelFlow') === -1)) {\r\n    option.url = option.url.replaceAll('/SYS/', '/Platform/').replaceAll('/Sys/', '/Platform/')\r\n    baseUrlStr = platformUrl() // process.env.VUE_APP_PLATFORM_API\r\n  } else if (option.url.indexOf('/Platform/') > -1) {\r\n    baseUrlStr = platformUrl()\r\n  } else if (option.url === '/DF/EQPTAsset/V2/GetEquipmentAssetPageList') {\r\n    baseUrlStr = dfUrl()\r\n  } else {\r\n    baseUrlStr = baseUrl()\r\n  }\r\n  const service = axios.create({\r\n    // baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url\r\n    baseURL: baseUrlStr,\r\n    // withCredentials: true, // send cookies when cross-domain requests\r\n    timeout: option.timeout || 60 * 1000 * 20\r\n  })\r\n\r\n  // request interceptor\r\n  service.interceptors.request.use(\r\n    config => {\r\n      // do something before request is sent\r\n      if (store.getters.token || getToken()) {\r\n        // let each request carry token\r\n        // ['X-Token'] is a custom headers key\r\n        // please modify it according to the actual situation\r\n        config.headers['Authorization'] = getToken()\r\n      }\r\n      // if (store.getters.Last_Working_Object_Id) {\r\n      //   config.headers.Last_Working_Object_Id = store.getters.Last_Working_Object_Id\r\n      // }\r\n      if (localStorage.getItem('Last_Working_Object_Id')) {\r\n        config.headers.Last_Working_Object_Id = localStorage.getItem('Last_Working_Object_Id')\r\n      }\r\n      return config\r\n    },\r\n    error => {\r\n      // do something with request error\r\n      console.log(error) // for debug\r\n      return Promise.reject(error)\r\n    }\r\n  )\r\n\r\n  // response interceptor\r\n  service.interceptors.response.use(\r\n    /**\r\n     * If you want to get http information such as headers or status\r\n     * Please return  response => response\r\n     */\r\n\r\n    /**\r\n     * Determine the request status by custom code\r\n     * Here is just an example\r\n     * You can also judge the status by HTTP Status Code\r\n     */\r\n    response => {\r\n      const res = response.data\r\n\r\n      // if (res.StatusCode === 502 || res.StatusCode === 502 || res.StatusCode === 501 || res.StatusCode === 500) {\r\n      //   // 服务端返回false，同时需要获取数据\r\n      //   // to re-login\r\n      //   // MessageBox.confirm('You have been logged out, you can cancel to stay on this page, or log in again', 'Confirm logout', {\r\n      //   //   confirmButtonText: 'Re-Login',\r\n      //   //   cancelButtonText: 'Cancel',\r\n      //   //   type: 'warning'\r\n      //   // }).then(() => {\r\n      //   //   store.dispatch('user/resetToken').then(() => {\r\n      //   //     location.reload()\r\n      //   //   })\r\n      //   // })\r\n      //   return res\r\n      // } else if (res.StatusCode === 200 || res.StatusCode === 502 || res.StatusCode === 502 || res.StatusCode === 501 || res.StatusCode === 500) {\r\n      //   // Message({\r\n      //   //   message: res.Message || 'Error',\r\n      //   //   type: 'error',\r\n      //   //   duration: 5 * 1000\r\n      //   // })\r\n      // } else\r\n      if (res.StatusCode === 401) {\r\n        store.dispatch('user/resetToken').then(() => {\r\n          location.reload()\r\n        })\r\n      } else if (res.StatusCode === 502) { // res.StatusCode === 501 ||\r\n        Message({\r\n          message: res.Message || 'Error',\r\n          type: 'error',\r\n          duration: 5 * 1000\r\n        })\r\n      } else {\r\n        return res\r\n        // return Promise.reject(new Error(res.Message || ' '))\r\n      }\r\n    },\r\n    error => {\r\n      console.log('err', error) // for debug\r\n      let message = ''\r\n      let status = null\r\n      if (error.response) {\r\n        // 有响应，但响应状态码不在 2xx 范围内\r\n        status = error.response.status\r\n        switch (status) {\r\n          case 401:\r\n            message = '登录过期，请重新登录'\r\n            break\r\n          case 403:\r\n            message = '无权访问'\r\n            break\r\n          case 404:\r\n            message = '请求地址错误'\r\n            break\r\n          case 500:\r\n            message = '服务器出现错误'\r\n            break\r\n          default:\r\n            message = '网络问题或连接超时'\r\n            break\r\n        }\r\n      } else if (error.request) {\r\n        // 请求已发送，但没有收到响应\r\n        message = '没有响应或连接超时'\r\n      } else {\r\n        // 发生了错误，请求无法完成\r\n        message = error.message\r\n        // 判断是否是超时错误\r\n        if (error.message.includes('timeout')) {\r\n          message = '网络问题或连接超时'\r\n          // 在这里可以进行相关处理，例如重新发送请求\r\n        }\r\n      }\r\n      Message({\r\n        message: message,\r\n        type: 'error',\r\n        duration: 5 * 1000\r\n      })\r\n      if (status === 401) {\r\n        store.dispatch('user/resetToken').then(() => {\r\n          location.reload()\r\n        })\r\n      }\r\n      return Promise.reject(error)\r\n    }\r\n  )\r\n  // 请求处理\r\n  return new Promise((resolve, reject) => {\r\n    service(option).then(res => {\r\n      resolve(res)\r\n    }).catch(error => {\r\n      reject(error)\r\n    })\r\n  })\r\n}\r\n\r\nexport default Axios\r\n"], "mappings": ";;;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,YAAY;AACpC,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,QAAQ,QAAQ,cAAc;AACvC,OAAOC,GAAG,MAAM,KAAK;AACrB,SAASC,OAAO,EAAEC,WAAW,EAAEC,KAAK,QAAQ,iBAAiB;;AAE7D;AACAH,GAAG,CAACI,SAAS,CAACC,OAAO,GAAGC,MAAM,CAACC,SAAS;;AAExC;AACA;AACA;AACA,IAAIC,UAAU,GAAGP,OAAO,CAAC,CAAC;AAE1B,SAASQ,KAAKA,CAACC,MAAM,EAAE;EACrB,IAAIA,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAKF,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,IAAIF,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,IAAIF,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC,IAAIF,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC,IAAIF,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC,IAAIF,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,IAAIF,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,IAAIF,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,iCAAiC,CAAC,KAAK,CAAC,CAAC,IAAIF,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,+BAA+B,CAAC,KAAK,CAAC,CAAE,EAAE;IAC1hBF,MAAM,CAACC,GAAG,GAAGD,MAAM,CAACC,GAAG,CAACE,UAAU,CAAC,OAAO,EAAE,YAAY,CAAC,CAACA,UAAU,CAAC,OAAO,EAAE,YAAY,CAAC;IAC3FL,UAAU,GAAGN,WAAW,CAAC,CAAC,EAAC;EAC7B,CAAC,MAAM,IAAIQ,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE;IAChDJ,UAAU,GAAGN,WAAW,CAAC,CAAC;EAC5B,CAAC,MAAM,IAAIQ,MAAM,CAACC,GAAG,KAAK,4CAA4C,EAAE;IACtEH,UAAU,GAAGL,KAAK,CAAC,CAAC;EACtB,CAAC,MAAM;IACLK,UAAU,GAAGP,OAAO,CAAC,CAAC;EACxB;EACA,IAAMa,OAAO,GAAGlB,KAAK,CAACmB,MAAM,CAAC;IAC3B;IACAC,OAAO,EAAER,UAAU;IACnB;IACAS,OAAO,EAAEP,MAAM,CAACO,OAAO,IAAI,EAAE,GAAG,IAAI,GAAG;EACzC,CAAC,CAAC;;EAEF;EACAH,OAAO,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9B,UAAAC,MAAM,EAAI;IACR;IACA,IAAIvB,KAAK,CAACwB,OAAO,CAACC,KAAK,IAAIxB,QAAQ,CAAC,CAAC,EAAE;MACrC;MACA;MACA;MACAsB,MAAM,CAACG,OAAO,CAAC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,CAAC;IAC9C;IACA;IACA;IACA;IACA,IAAI0B,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC,EAAE;MAClDL,MAAM,CAACG,OAAO,CAACG,sBAAsB,GAAGF,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;IACxF;IACA,OAAOL,MAAM;EACf,CAAC,EACD,UAAAO,KAAK,EAAI;IACP;IACAC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAC;IACnB,OAAOG,OAAO,CAACC,MAAM,CAACJ,KAAK,CAAC;EAC9B,CACF,CAAC;;EAED;EACAd,OAAO,CAACI,YAAY,CAACe,QAAQ,CAACb,GAAG;EAC/B;AACJ;AACA;AACA;;EAEI;AACJ;AACA;AACA;AACA;EACI,UAAAa,QAAQ,EAAI;IACV,IAAMC,GAAG,GAAGD,QAAQ,CAACE,IAAI;;IAEzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAID,GAAG,CAACE,UAAU,KAAK,GAAG,EAAE;MAC1BtC,KAAK,CAACuC,QAAQ,CAAC,iBAAiB,CAAC,CAACC,IAAI,CAAC,YAAM;QAC3CC,QAAQ,CAACC,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIN,GAAG,CAACE,UAAU,KAAK,GAAG,EAAE;MAAE;MACnCvC,OAAO,CAAC;QACN4C,OAAO,EAAEP,GAAG,CAACrC,OAAO,IAAI,OAAO;QAC/B6C,IAAI,EAAE,OAAO;QACbC,QAAQ,EAAE,CAAC,GAAG;MAChB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAOT,GAAG;MACV;IACF;EACF,CAAC,EACD,UAAAN,KAAK,EAAI;IACPC,OAAO,CAACC,GAAG,CAAC,KAAK,EAAEF,KAAK,CAAC,EAAC;IAC1B,IAAIa,OAAO,GAAG,EAAE;IAChB,IAAIG,MAAM,GAAG,IAAI;IACjB,IAAIhB,KAAK,CAACK,QAAQ,EAAE;MAClB;MACAW,MAAM,GAAGhB,KAAK,CAACK,QAAQ,CAACW,MAAM;MAC9B,QAAQA,MAAM;QACZ,KAAK,GAAG;UACNH,OAAO,GAAG,YAAY;UACtB;QACF,KAAK,GAAG;UACNA,OAAO,GAAG,MAAM;UAChB;QACF,KAAK,GAAG;UACNA,OAAO,GAAG,QAAQ;UAClB;QACF,KAAK,GAAG;UACNA,OAAO,GAAG,SAAS;UACnB;QACF;UACEA,OAAO,GAAG,WAAW;UACrB;MACJ;IACF,CAAC,MAAM,IAAIb,KAAK,CAACT,OAAO,EAAE;MACxB;MACAsB,OAAO,GAAG,WAAW;IACvB,CAAC,MAAM;MACL;MACAA,OAAO,GAAGb,KAAK,CAACa,OAAO;MACvB;MACA,IAAIb,KAAK,CAACa,OAAO,CAACI,QAAQ,CAAC,SAAS,CAAC,EAAE;QACrCJ,OAAO,GAAG,WAAW;QACrB;MACF;IACF;IACA5C,OAAO,CAAC;MACN4C,OAAO,EAAEA,OAAO;MAChBC,IAAI,EAAE,OAAO;MACbC,QAAQ,EAAE,CAAC,GAAG;IAChB,CAAC,CAAC;IACF,IAAIC,MAAM,KAAK,GAAG,EAAE;MAClB9C,KAAK,CAACuC,QAAQ,CAAC,iBAAiB,CAAC,CAACC,IAAI,CAAC,YAAM;QAC3CC,QAAQ,CAACC,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC;IACJ;IACA,OAAOT,OAAO,CAACC,MAAM,CAACJ,KAAK,CAAC;EAC9B,CACF,CAAC;EACD;EACA,OAAO,IAAIG,OAAO,CAAC,UAACe,OAAO,EAAEd,MAAM,EAAK;IACtClB,OAAO,CAACJ,MAAM,CAAC,CAAC4B,IAAI,CAAC,UAAAJ,GAAG,EAAI;MAC1BY,OAAO,CAACZ,GAAG,CAAC;IACd,CAAC,CAAC,CAACa,KAAK,CAAC,UAAAnB,KAAK,EAAI;MAChBI,MAAM,CAACJ,KAAK,CAAC;IACf,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,eAAenB,KAAK", "ignoreList": []}]}
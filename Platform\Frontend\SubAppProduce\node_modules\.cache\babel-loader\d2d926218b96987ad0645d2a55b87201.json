{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\unitPartRecognitionConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\unitPartRecognitionConfig.vue", "mtime": 1757468113430}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["uniqueArr", "GetFactoryPartTypeIndentifySetting", "SavePartTypeIdentifySetting", "SPLITVALUE", "props", "level", "type", "Number", "default", "bomList", "Array", "data", "form", "enable", "identifyAttr", "list", "splitSymbol", "btnLoading", "computed", "currentBomName", "_this$bomList$find", "_this", "find", "item", "Code", "Display_Name", "mounted", "getTypeList", "methods", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "Part_Grade", "then", "res", "IsSucceed", "_res$Data", "Data", "Is_Enabled", "Setting_List", "map", "v", "index", "$set", "Prefixs", "length", "Identify_Attr", "$message", "message", "Message", "stop", "handleSubmit", "_this3", "arr", "console", "log", "i", "regex", "test", "concat", "Part_Type_Name", "split", "filter", "j", "d", "push", "apply", "_toConsumableArray", "uniArr", "_objectSpread", "$emit", "finally", "mainBlur", "e"], "sources": ["src/views/PRO/process-settings/management/component/unitPartRecognitionConfig.vue"], "sourcesContent": ["<template>\r\n  <div class=\"form-wrapper\">\r\n    <div class=\"form-x\">\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"120px\">\r\n        <el-form-item label=\"是否启用\" prop=\"enable\">\r\n          <el-radio-group v-model=\"form.enable\">\r\n            <el-radio :label=\"false\">否</el-radio>\r\n            <el-radio :label=\"true\">是</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <template v-if=\"form.enable\">\r\n          <el-form-item label=\"识别类型\" prop=\"identifyAttr\">\r\n            <el-radio-group v-model=\"form.identifyAttr\">\r\n              <el-radio :label=\"1\">{{ currentBomName }}名称前缀</el-radio>\r\n              <el-radio :label=\"2\">{{ currentBomName }}规格前缀</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item v-for=\"(element,index) in list\" :key=\"index\" :show-message=\"false\" :label=\"element.Part_Type_Name\" prop=\"mainPart\">\r\n            <el-input v-model.trim=\"form['item'+index]\" :placeholder=\"`请输入（多个使用'${splitSymbol}'隔开），单个配置不超过10个字符`\" clearable @blur=\"mainBlur\" />\r\n          </el-form-item>\r\n        </template>\r\n      </el-form>\r\n    </div>\r\n    <div class=\"btn-x\">\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { uniqueArr } from '@/utils'\r\nimport {\r\n  GetFactoryPartTypeIndentifySetting,\r\n  SavePartTypeIdentifySetting\r\n} from '@/api/PRO/partType'\r\n\r\nconst SPLITVALUE = '|'\r\n\r\nexport default {\r\n  props: {\r\n    level: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    bomList: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      form: {\r\n        enable: false,\r\n        identifyAttr: 1 // 默认为零件名称前缀\r\n      },\r\n      list: [],\r\n      splitSymbol: SPLITVALUE,\r\n      btnLoading: false\r\n    }\r\n  },\r\n  computed: {\r\n    currentBomName() {\r\n      return this.bomList.find(item => +item.Code === this.level)?.Display_Name\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getTypeList()\r\n  },\r\n  methods: {\r\n    async getTypeList() {\r\n      GetFactoryPartTypeIndentifySetting({\r\n        Part_Grade: this.level\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const { Is_Enabled, Setting_List } = res.Data\r\n          this.form.enable = Is_Enabled\r\n          this.list = Setting_List.map((v, index) => {\r\n            this.$set(this.form, 'item' + index, v.Prefixs || '')\r\n            return v\r\n          })\r\n\r\n          // 获取Setting_List中的Identify_Attr，如果有效（值为1或2）则使用，否则默认为1\r\n          if (Setting_List.length > 0) {\r\n            const identifyAttr = Setting_List[0].Identify_Attr\r\n            this.form.identifyAttr = (identifyAttr === 1 || identifyAttr === 2) ? identifyAttr : 1\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      if (this.form.enable) {\r\n        const arr = []\r\n        console.log('this.form', this.form)\r\n        for (let i = 0; i < this.list.length; i++) {\r\n          const regex = /^(?!.*\\|\\|)(?!.*\\|$)(?!^\\|)[^|]{1,10}(?:\\|[^|]{1,10})*$/\r\n          if (!regex.test(this.form[`item${i}`])) {\r\n            this.$message({\r\n              message: `${this.list[i].Part_Type_Name}配置不符合要求`,\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n\r\n          const item = this.form[`item${i}`].split(this.splitSymbol).filter(v => !!v)\r\n\r\n          if (item.length === 0) {\r\n            this.$message({\r\n              message: `${this.list[i].Part_Type_Name}不能为空`,\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n\r\n          for (let j = 0; j < item.length; j++) {\r\n            const d = item[j]\r\n            if (d.length > 10) {\r\n              this.$message({\r\n                message: `${this.list[i].Part_Type_Name}单个配置，不能超过10个字符`,\r\n                type: 'warning'\r\n              })\r\n              return\r\n            }\r\n          }\r\n\r\n          arr.push(...item)\r\n        }\r\n        const uniArr = uniqueArr(arr)\r\n        if (uniArr.length !== arr.length) {\r\n          this.$message({\r\n            message: '配置不能相同',\r\n            type: 'warning'\r\n          })\r\n          return\r\n        }\r\n      }\r\n      this.btnLoading = true\r\n      SavePartTypeIdentifySetting({\r\n        Is_Enabled: this.form.enable,\r\n        Part_Grade: this.level,\r\n        Setting_List: this.list.map((v, i) => {\r\n          return {\r\n            ...v,\r\n            Prefixs: this.form[`item${i}`],\r\n            Identify_Attr: this.form.identifyAttr\r\n          }\r\n        })\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '操作成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('close')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(() => {\r\n        this.btnLoading = false\r\n      })\r\n    },\r\n    mainBlur(e) {\r\n\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"~@/styles/mixin.scss\";\r\n.form-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  max-height: 70vh;\r\n  .form-x{\r\n    overflow: auto;\r\n    padding-right: 16px;\r\n    @include scrollBar;\r\n  }\r\n  .btn-x {\r\n    padding-top: 16px;\r\n    text-align: right;\r\n  }\r\n\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,SAAAA,SAAA;AACA,SACAC,kCAAA,EACAC,2BAAA,QACA;AAEA,IAAAC,UAAA;AAEA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,OAAA;MACAH,IAAA,EAAAI,KAAA;MACAF,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;QACAC,MAAA;QACAC,YAAA;MACA;MACAC,IAAA;MACAC,WAAA,EAAAb,UAAA;MACAc,UAAA;IACA;EACA;EACAC,QAAA;IACAC,cAAA,WAAAA,eAAA;MAAA,IAAAC,kBAAA;QAAAC,KAAA;MACA,QAAAD,kBAAA,QAAAX,OAAA,CAAAa,IAAA,WAAAC,IAAA;QAAA,QAAAA,IAAA,CAAAC,IAAA,KAAAH,KAAA,CAAAhB,KAAA;MAAA,gBAAAe,kBAAA,uBAAAA,kBAAA,CAAAK,YAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACAD,WAAA,WAAAA,YAAA;MAAA,IAAAE,MAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACArC,kCAAA;gBACAsC,UAAA,EAAAV,MAAA,CAAAxB;cACA,GAAAmC,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACA,IAAAC,SAAA,GAAAF,GAAA,CAAAG,IAAA;oBAAAC,UAAA,GAAAF,SAAA,CAAAE,UAAA;oBAAAC,YAAA,GAAAH,SAAA,CAAAG,YAAA;kBACAjB,MAAA,CAAAjB,IAAA,CAAAC,MAAA,GAAAgC,UAAA;kBACAhB,MAAA,CAAAd,IAAA,GAAA+B,YAAA,CAAAC,GAAA,WAAAC,CAAA,EAAAC,KAAA;oBACApB,MAAA,CAAAqB,IAAA,CAAArB,MAAA,CAAAjB,IAAA,WAAAqC,KAAA,EAAAD,CAAA,CAAAG,OAAA;oBACA,OAAAH,CAAA;kBACA;;kBAEA;kBACA,IAAAF,YAAA,CAAAM,MAAA;oBACA,IAAAtC,YAAA,GAAAgC,YAAA,IAAAO,aAAA;oBACAxB,MAAA,CAAAjB,IAAA,CAAAE,YAAA,GAAAA,YAAA,UAAAA,YAAA,SAAAA,YAAA;kBACA;gBACA;kBACAe,MAAA,CAAAyB,QAAA;oBACAC,OAAA,EAAAd,GAAA,CAAAe,OAAA;oBACAlD,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA8B,QAAA,CAAAqB,IAAA;UAAA;QAAA,GAAAxB,OAAA;MAAA;IACA;IACAyB,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,SAAA/C,IAAA,CAAAC,MAAA;QACA,IAAA+C,GAAA;QACAC,OAAA,CAAAC,GAAA,mBAAAlD,IAAA;QACA,SAAAmD,CAAA,MAAAA,CAAA,QAAAhD,IAAA,CAAAqC,MAAA,EAAAW,CAAA;UACA,IAAAC,KAAA;UACA,KAAAA,KAAA,CAAAC,IAAA,MAAArD,IAAA,QAAAsD,MAAA,CAAAH,CAAA;YACA,KAAAT,QAAA;cACAC,OAAA,KAAAW,MAAA,MAAAnD,IAAA,CAAAgD,CAAA,EAAAI,cAAA;cACA7D,IAAA;YACA;YACA;UACA;UAEA,IAAAiB,IAAA,QAAAX,IAAA,QAAAsD,MAAA,CAAAH,CAAA,GAAAK,KAAA,MAAApD,WAAA,EAAAqD,MAAA,WAAArB,CAAA;YAAA,SAAAA,CAAA;UAAA;UAEA,IAAAzB,IAAA,CAAA6B,MAAA;YACA,KAAAE,QAAA;cACAC,OAAA,KAAAW,MAAA,MAAAnD,IAAA,CAAAgD,CAAA,EAAAI,cAAA;cACA7D,IAAA;YACA;YACA;UACA;UAEA,SAAAgE,CAAA,MAAAA,CAAA,GAAA/C,IAAA,CAAA6B,MAAA,EAAAkB,CAAA;YACA,IAAAC,CAAA,GAAAhD,IAAA,CAAA+C,CAAA;YACA,IAAAC,CAAA,CAAAnB,MAAA;cACA,KAAAE,QAAA;gBACAC,OAAA,KAAAW,MAAA,MAAAnD,IAAA,CAAAgD,CAAA,EAAAI,cAAA;gBACA7D,IAAA;cACA;cACA;YACA;UACA;UAEAsD,GAAA,CAAAY,IAAA,CAAAC,KAAA,CAAAb,GAAA,EAAAc,kBAAA,CAAAnD,IAAA;QACA;QACA,IAAAoD,MAAA,GAAA3E,SAAA,CAAA4D,GAAA;QACA,IAAAe,MAAA,CAAAvB,MAAA,KAAAQ,GAAA,CAAAR,MAAA;UACA,KAAAE,QAAA;YACAC,OAAA;YACAjD,IAAA;UACA;UACA;QACA;MACA;MACA,KAAAW,UAAA;MACAf,2BAAA;QACA2C,UAAA,OAAAjC,IAAA,CAAAC,MAAA;QACA0B,UAAA,OAAAlC,KAAA;QACAyC,YAAA,OAAA/B,IAAA,CAAAgC,GAAA,WAAAC,CAAA,EAAAe,CAAA;UACA,OAAAa,aAAA,CAAAA,aAAA,KACA5B,CAAA;YACAG,OAAA,EAAAQ,MAAA,CAAA/C,IAAA,QAAAsD,MAAA,CAAAH,CAAA;YACAV,aAAA,EAAAM,MAAA,CAAA/C,IAAA,CAAAE;UAAA;QAEA;MACA,GAAA0B,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAiB,MAAA,CAAAL,QAAA;YACAC,OAAA;YACAjD,IAAA;UACA;UACAqD,MAAA,CAAAkB,KAAA;QACA;UACAlB,MAAA,CAAAL,QAAA;YACAC,OAAA,EAAAd,GAAA,CAAAe,OAAA;YACAlD,IAAA;UACA;QACA;MACA,GAAAwE,OAAA;QACAnB,MAAA,CAAA1C,UAAA;MACA;IACA;IACA8D,QAAA,WAAAA,SAAAC,CAAA,GAEA;EACA;AACA", "ignoreList": []}]}
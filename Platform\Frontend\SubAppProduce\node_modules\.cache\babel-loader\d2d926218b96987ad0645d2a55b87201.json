{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\unitPartRecognitionConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\unitPartRecognitionConfig.vue", "mtime": 1757474258893}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["uniqueArr", "GetFactoryPartTypeIndentifySetting", "SavePartTypeIdentifySetting", "SPLITVALUE", "props", "level", "type", "Number", "default", "bomList", "Array", "data", "form", "enable", "identifyAttr", "list", "splitSymbol", "btnLoading", "computed", "currentBomName", "_this$bomList$find", "_this", "find", "item", "Code", "Display_Name", "watch", "handler", "newVal", "getTypeList", "immediate", "mounted", "methods", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "Part_Grade", "then", "res", "IsSucceed", "_res$Data", "Data", "Is_Enabled", "Setting_List", "map", "v", "index", "$set", "Prefixs", "length", "Identify_Attr", "$message", "message", "Message", "stop", "handleSubmit", "_this3", "arr", "console", "log", "i", "regex", "test", "concat", "Part_Type_Name", "split", "filter", "j", "d", "push", "apply", "_toConsumableArray", "uniArr", "_objectSpread", "$emit", "finally", "mainBlur", "e"], "sources": ["src/views/PRO/process-settings/management/component/unitPartRecognitionConfig.vue"], "sourcesContent": ["<template>\n  <div class=\"form-wrapper\">\n    <div class=\"form-x\">\n      <el-form ref=\"form\" :model=\"form\" label-width=\"120px\">\n        <el-form-item label=\"是否启用\" prop=\"enable\">\n          <el-radio-group v-model=\"form.enable\">\n            <el-radio :label=\"false\">否</el-radio>\n            <el-radio :label=\"true\">是</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <template v-if=\"form.enable\">\n          <el-form-item label=\"识别类型\" prop=\"identifyAttr\">\n            <el-radio-group v-model=\"form.identifyAttr\">\n              <el-radio :label=\"1\">{{ currentBomName }}名称前缀</el-radio>\n              <el-radio :label=\"2\">{{ currentBomName }}规格前缀</el-radio>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item v-for=\"(element,index) in list\" :key=\"index\" :show-message=\"false\" :label=\"element.Part_Type_Name\" prop=\"mainPart\">\n            <el-input v-model.trim=\"form['item'+index]\" :placeholder=\"`请输入（多个使用'${splitSymbol}'隔开），单个配置不超过10个字符`\" clearable @blur=\"mainBlur\" />\n          </el-form-item>\n        </template>\n      </el-form>\n    </div>\n    <div class=\"btn-x\">\n      <el-button @click=\"$emit('close')\">取 消</el-button>\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\n\nimport { uniqueArr } from '@/utils'\nimport {\n  GetFactoryPartTypeIndentifySetting,\n  SavePartTypeIdentifySetting\n} from '@/api/PRO/partType'\n\nconst SPLITVALUE = '|'\n\nexport default {\n  props: {\n    level: {\n      type: Number,\n      default: 0\n    },\n    bomList: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      form: {\n        enable: false,\n        identifyAttr: 1 // 默认为零件名称前缀\n      },\n      list: [],\n      splitSymbol: SPLITVALUE,\n      btnLoading: false\n    }\n  },\n  computed: {\n    currentBomName() {\n      return this.bomList.find(item => +item.Code === this.level)?.Display_Name\n    }\n  },\n  watch: {\n    level: {\n      handler(newVal) {\n        if (newVal) {\n          this.getTypeList()\n        }\n      },\n      immediate: true\n    }\n  },\n  mounted() {\n\n  },\n  methods: {\n    async getTypeList() {\n      GetFactoryPartTypeIndentifySetting({\n        Part_Grade: this.level\n      }).then(res => {\n        if (res.IsSucceed) {\n          const { Is_Enabled, Setting_List } = res.Data\n          this.form.enable = Is_Enabled\n          this.list = Setting_List.map((v, index) => {\n            this.$set(this.form, 'item' + index, v.Prefixs || '')\n            return v\n          })\n\n          // 获取Setting_List中的Identify_Attr，如果有效（值为1或2）则使用，否则默认为1\n          if (Setting_List.length > 0) {\n            const identifyAttr = Setting_List[0].Identify_Attr\n            this.form.identifyAttr = (identifyAttr === 1 || identifyAttr === 2) ? identifyAttr : 1\n          }\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    handleSubmit() {\n      if (this.form.enable) {\n        const arr = []\n        console.log('this.form', this.form)\n        for (let i = 0; i < this.list.length; i++) {\n          const regex = /^(?!.*\\|\\|)(?!.*\\|$)(?!^\\|)[^|]{1,10}(?:\\|[^|]{1,10})*$/\n          if (!regex.test(this.form[`item${i}`])) {\n            this.$message({\n              message: `${this.list[i].Part_Type_Name}配置不符合要求`,\n              type: 'warning'\n            })\n            return\n          }\n\n          const item = this.form[`item${i}`].split(this.splitSymbol).filter(v => !!v)\n\n          if (item.length === 0) {\n            this.$message({\n              message: `${this.list[i].Part_Type_Name}不能为空`,\n              type: 'warning'\n            })\n            return\n          }\n\n          for (let j = 0; j < item.length; j++) {\n            const d = item[j]\n            if (d.length > 10) {\n              this.$message({\n                message: `${this.list[i].Part_Type_Name}单个配置，不能超过10个字符`,\n                type: 'warning'\n              })\n              return\n            }\n          }\n\n          arr.push(...item)\n        }\n        const uniArr = uniqueArr(arr)\n        if (uniArr.length !== arr.length) {\n          this.$message({\n            message: '配置不能相同',\n            type: 'warning'\n          })\n          return\n        }\n      }\n      this.btnLoading = true\n      SavePartTypeIdentifySetting({\n        Is_Enabled: this.form.enable,\n        Part_Grade: this.level,\n        Setting_List: this.list.map((v, i) => {\n          return {\n            ...v,\n            Prefixs: this.form[`item${i}`],\n            Identify_Attr: this.form.identifyAttr\n          }\n        })\n      }).then(res => {\n        if (res.IsSucceed) {\n          this.$message({\n            message: '操作成功',\n            type: 'success'\n          })\n          this.$emit('close')\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      }).finally(() => {\n        this.btnLoading = false\n      })\n    },\n    mainBlur(e) {\n\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"~@/styles/mixin.scss\";\n.form-wrapper {\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  max-height: 70vh;\n  .form-x{\n    overflow: auto;\n    padding-right: 16px;\n    @include scrollBar;\n  }\n  .btn-x {\n    padding-top: 16px;\n    text-align: right;\n  }\n\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,SAAAA,SAAA;AACA,SACAC,kCAAA,EACAC,2BAAA,QACA;AAEA,IAAAC,UAAA;AAEA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,OAAA;MACAH,IAAA,EAAAI,KAAA;MACAF,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;QACAC,MAAA;QACAC,YAAA;MACA;MACAC,IAAA;MACAC,WAAA,EAAAb,UAAA;MACAc,UAAA;IACA;EACA;EACAC,QAAA;IACAC,cAAA,WAAAA,eAAA;MAAA,IAAAC,kBAAA;QAAAC,KAAA;MACA,QAAAD,kBAAA,QAAAX,OAAA,CAAAa,IAAA,WAAAC,IAAA;QAAA,QAAAA,IAAA,CAAAC,IAAA,KAAAH,KAAA,CAAAhB,KAAA;MAAA,gBAAAe,kBAAA,uBAAAA,kBAAA,CAAAK,YAAA;IACA;EACA;EACAC,KAAA;IACArB,KAAA;MACAsB,OAAA,WAAAA,QAAAC,MAAA;QACA,IAAAA,MAAA;UACA,KAAAC,WAAA;QACA;MACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA,GAEA;EACAC,OAAA;IACAH,WAAA,WAAAA,YAAA;MAAA,IAAAI,MAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAzC,kCAAA;gBACA0C,UAAA,EAAAV,MAAA,CAAA5B;cACA,GAAAuC,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACA,IAAAC,SAAA,GAAAF,GAAA,CAAAG,IAAA;oBAAAC,UAAA,GAAAF,SAAA,CAAAE,UAAA;oBAAAC,YAAA,GAAAH,SAAA,CAAAG,YAAA;kBACAjB,MAAA,CAAArB,IAAA,CAAAC,MAAA,GAAAoC,UAAA;kBACAhB,MAAA,CAAAlB,IAAA,GAAAmC,YAAA,CAAAC,GAAA,WAAAC,CAAA,EAAAC,KAAA;oBACApB,MAAA,CAAAqB,IAAA,CAAArB,MAAA,CAAArB,IAAA,WAAAyC,KAAA,EAAAD,CAAA,CAAAG,OAAA;oBACA,OAAAH,CAAA;kBACA;;kBAEA;kBACA,IAAAF,YAAA,CAAAM,MAAA;oBACA,IAAA1C,YAAA,GAAAoC,YAAA,IAAAO,aAAA;oBACAxB,MAAA,CAAArB,IAAA,CAAAE,YAAA,GAAAA,YAAA,UAAAA,YAAA,SAAAA,YAAA;kBACA;gBACA;kBACAmB,MAAA,CAAAyB,QAAA;oBACAC,OAAA,EAAAd,GAAA,CAAAe,OAAA;oBACAtD,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAkC,QAAA,CAAAqB,IAAA;UAAA;QAAA,GAAAxB,OAAA;MAAA;IACA;IACAyB,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,SAAAnD,IAAA,CAAAC,MAAA;QACA,IAAAmD,GAAA;QACAC,OAAA,CAAAC,GAAA,mBAAAtD,IAAA;QACA,SAAAuD,CAAA,MAAAA,CAAA,QAAApD,IAAA,CAAAyC,MAAA,EAAAW,CAAA;UACA,IAAAC,KAAA;UACA,KAAAA,KAAA,CAAAC,IAAA,MAAAzD,IAAA,QAAA0D,MAAA,CAAAH,CAAA;YACA,KAAAT,QAAA;cACAC,OAAA,KAAAW,MAAA,MAAAvD,IAAA,CAAAoD,CAAA,EAAAI,cAAA;cACAjE,IAAA;YACA;YACA;UACA;UAEA,IAAAiB,IAAA,QAAAX,IAAA,QAAA0D,MAAA,CAAAH,CAAA,GAAAK,KAAA,MAAAxD,WAAA,EAAAyD,MAAA,WAAArB,CAAA;YAAA,SAAAA,CAAA;UAAA;UAEA,IAAA7B,IAAA,CAAAiC,MAAA;YACA,KAAAE,QAAA;cACAC,OAAA,KAAAW,MAAA,MAAAvD,IAAA,CAAAoD,CAAA,EAAAI,cAAA;cACAjE,IAAA;YACA;YACA;UACA;UAEA,SAAAoE,CAAA,MAAAA,CAAA,GAAAnD,IAAA,CAAAiC,MAAA,EAAAkB,CAAA;YACA,IAAAC,CAAA,GAAApD,IAAA,CAAAmD,CAAA;YACA,IAAAC,CAAA,CAAAnB,MAAA;cACA,KAAAE,QAAA;gBACAC,OAAA,KAAAW,MAAA,MAAAvD,IAAA,CAAAoD,CAAA,EAAAI,cAAA;gBACAjE,IAAA;cACA;cACA;YACA;UACA;UAEA0D,GAAA,CAAAY,IAAA,CAAAC,KAAA,CAAAb,GAAA,EAAAc,kBAAA,CAAAvD,IAAA;QACA;QACA,IAAAwD,MAAA,GAAA/E,SAAA,CAAAgE,GAAA;QACA,IAAAe,MAAA,CAAAvB,MAAA,KAAAQ,GAAA,CAAAR,MAAA;UACA,KAAAE,QAAA;YACAC,OAAA;YACArD,IAAA;UACA;UACA;QACA;MACA;MACA,KAAAW,UAAA;MACAf,2BAAA;QACA+C,UAAA,OAAArC,IAAA,CAAAC,MAAA;QACA8B,UAAA,OAAAtC,KAAA;QACA6C,YAAA,OAAAnC,IAAA,CAAAoC,GAAA,WAAAC,CAAA,EAAAe,CAAA;UACA,OAAAa,aAAA,CAAAA,aAAA,KACA5B,CAAA;YACAG,OAAA,EAAAQ,MAAA,CAAAnD,IAAA,QAAA0D,MAAA,CAAAH,CAAA;YACAV,aAAA,EAAAM,MAAA,CAAAnD,IAAA,CAAAE;UAAA;QAEA;MACA,GAAA8B,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAiB,MAAA,CAAAL,QAAA;YACAC,OAAA;YACArD,IAAA;UACA;UACAyD,MAAA,CAAAkB,KAAA;QACA;UACAlB,MAAA,CAAAL,QAAA;YACAC,OAAA,EAAAd,GAAA,CAAAe,OAAA;YACAtD,IAAA;UACA;QACA;MACA,GAAA4E,OAAA;QACAnB,MAAA,CAAA9C,UAAA;MACA;IACA;IACAkE,QAAA,WAAAA,SAAAC,CAAA,GAEA;EACA;AACA", "ignoreList": []}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\actually-sent\\v4\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\actually-sent\\v4\\index.vue", "mtime": 1757468128067}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgTW9uaXRvciBmcm9tICdAL2NvbXBvbmVudHMvTW9uaXRvci9pbmRleC52dWUnDQppbXBvcnQgVG9wSGVhZGVyIGZyb20gJ0AvY29tcG9uZW50cy9Ub3BIZWFkZXIvaW5kZXgudnVlJw0KaW1wb3J0IGFkZFJvdXRlclBhZ2UgZnJvbSAnQC9taXhpbnMvYWRkLXJvdXRlci1wYWdlJw0KaW1wb3J0IER5bmFtaWNEYXRhVGFibGUgZnJvbSAnQC9jb21wb25lbnRzL0R5bmFtaWNEYXRhVGFibGUvRHluYW1pY0RhdGFUYWJsZS52dWUnDQppbXBvcnQgZ2V0VGJJbmZvIGZyb20gJ0AvbWl4aW5zL1BSTy9nZXQtdGFibGUtaW5mby1wcm8yJw0KaW1wb3J0IHsNCiAgR2V0UHJvamVjdFNlbmRpbmdJbmZvUGFnZWxpc3QsDQogIERlbGV0ZVByb2plY3RTZW5kaW5nSW5mbywNCiAgU3VibWl0UHJvamVjdFNlbmRpbmcsDQogIEdldFByb2plY3RTZW5kaW5nQWxsQ291bnQsDQogIFRyYW5zZm9ybXNXaXRob3V0V2VpZ2h0LA0KICBTdWJtaXRXZWlnaGluZ0ZvclBDLA0KICBXaXRoZHJhd0RyYWZ0LA0KICBFeHBvcnRJbnZvaWNlTGlzdCwgU3VibWl0QXBwcm92YWwsIENhbmNlbEZsb3cNCn0gZnJvbSAnQC9hcGkvUFJPL2NvbXBvbmVudC1zdG9jay1vdXQnDQppbXBvcnQgeyBHZXRQcm9qZWN0UGFnZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vcHJvLXNjaGVkdWxlcycNCmltcG9ydCB7IEdlQXJlYVRyZWVzIH0gZnJvbSAnQC9hcGkvUFJPL3Byb2plY3QnDQppbXBvcnQgeyBHZXRJbnN0YWxsVW5pdFBhZ2VMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL2luc3RhbGwtdW5pdCcNCmltcG9ydCB7IHBhcnNlVGltZSB9IGZyb20gJ0AvdXRpbHMnDQppbXBvcnQgeyBiYXNlVXJsIH0gZnJvbSAnQC91dGlscy9iYXNldXJsJw0KaW1wb3J0IHsgR2V0RmFjdG9yeVByb2Zlc3Npb25hbEJ5Q29kZSB9IGZyb20gJ0AvYXBpL1BSTy9wcm9mZXNzaW9uYWxUeXBlJw0KaW1wb3J0IFByaW50RGlhbG9nIGZyb20gJy4vY29tcG9uZW50L3ByaW50RGlhbG9nLnZ1ZScNCmltcG9ydCByYWRpb0RpYWxvZyBmcm9tICcuL2NvbXBvbmVudC9kaWFsb2cudnVlJw0KaW1wb3J0IGRpYWxvZ0V4Y2VsIGZyb20gJy4vY29tcG9uZW50L2RpYWxvZ0V4Y2VsLnZ1ZScNCmltcG9ydCBjaGVja0RpYWxvZyBmcm9tICcuL2NvbXBvbmVudC9jaGVjay52dWUnDQppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCcNCmltcG9ydCBFeHBvcnRDdXN0b21SZXBvcnQgZnJvbSAiQC9jb21wb25lbnRzL0V4cG9ydEN1c3RvbVJlcG9ydC9pbmRleC52dWUiOw0KDQpjb25zdCBTdGF0dXNNYXAgPSB7DQogIDA6ICfojYnnqL8nLA0KICAyOiAn5b6F6L+H56OFJywNCiAgMzogJ+W3sui/h+ejhScsDQogIDQ6ICfmnKrpqozmlLYnLA0KICA1OiAn6YOo5YiG6aqM5pS2JywNCiAgNjogJ+W3sumqjOaUticsDQogIDc6ICflt7LpgIDotKcnLA0KICA5OTk6ICflrqHmibnkuK0nLA0KICAnLTEnOiAn5bey6YCA5ZueJw0KfQ0KDQpleHBvcnQgZGVmYXVsdCB7DQogIGNvbXBvbmVudHM6IHsNCiAgICBFeHBvcnRDdXN0b21SZXBvcnQsDQogICAgVG9wSGVhZGVyLA0KICAgIE1vbml0b3IsDQogICAgRHluYW1pY0RhdGFUYWJsZSwNCiAgICBQcmludERpYWxvZywNCiAgICByYWRpb0RpYWxvZywNCiAgICBjaGVja0RpYWxvZywNCiAgICBkaWFsb2dFeGNlbA0KICB9LA0KICBmaWx0ZXJzOiB7DQogICAgc2VuZERhdGVGaWx0ZXIoZSkgew0KICAgICAgLy8gY29uc29sZS5sb2coZSwiZWVlZSIpOw0KICAgICAgcmV0dXJuIHBhcnNlVGltZShuZXcgRGF0ZShlKSkNCiAgICB9DQogIH0sDQogIG1peGluczogW2FkZFJvdXRlclBhZ2UsIGdldFRiSW5mb10sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHNlbGVjdExpc3Q6IFtdLA0KICAgICAgc3RhdHVzSW5mbzogU3RhdHVzTWFwLA0KICAgICAgSXNWaXNhYmVsOiBmYWxzZSwNCiAgICAgIGJ0bkxvYWRpbmc6IGZhbHNlLA0KICAgICAgZm9ybTogew0KICAgICAgICBDb2RlOiAnJywNCiAgICAgICAgU3RhdHVzOiBudWxsLA0KICAgICAgICBQcm9qZWN0SWQ6ICcnLA0KICAgICAgICBWZWhpY2xlTm86ICcnLA0KICAgICAgICBDb25zaWduZWU6ICcnLA0KICAgICAgICBJc1JldHVybjogbnVsbCwNCiAgICAgICAgSXNfV2VpZ2h0X1dhcm5pbmc6IG51bGwsDQogICAgICAgIGRhdGVSYW5nZTogWycnLCAnJ10sDQogICAgICAgIFBhZ2VJbmZvOiB7DQogICAgICAgICAgUGFyYW1ldGVySnNvbjogW10sDQogICAgICAgICAgUGFnZTogMSwNCiAgICAgICAgICBQYWdlU2l6ZTogMjANCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIGZvcm0yOiB7DQogICAgICAgIFByb2plY3RJZDogJycsDQogICAgICAgIEFyZWFfSWQ6ICcnLA0KICAgICAgICBJbnN0YWxsVW5pdF9JZDogJycNCiAgICAgIH0sDQogICAgICBydWxlczogew0KICAgICAgICBQcm9qZWN0SWQ6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oupJywgdHJpZ2dlcjogJ2NoYW5nZScgfV0NCiAgICAgICAgLy8gQXJlYV9JZDogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6kiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9XSwNCiAgICAgIH0sDQogICAgICBwaWNrZXJPcHRpb25zOiB7DQogICAgICAgIHNob3J0Y3V0czogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRleHQ6ICfku4rlpKknLA0KICAgICAgICAgICAgb25DbGljayhwaWNrZXIpIHsNCiAgICAgICAgICAgICAgY29uc3QgZW5kID0gbmV3IERhdGUoKQ0KICAgICAgICAgICAgICBjb25zdCBzdGFydCA9IG5ldyBEYXRlKCkNCiAgICAgICAgICAgICAgc3RhcnQuc2V0VGltZShzdGFydC5nZXRUaW1lKCkgLSAzNjAwICogMTAwMCAqIDI0ICogMSkNCiAgICAgICAgICAgICAgcGlja2VyLiRlbWl0KCdwaWNrJywgW3N0YXJ0LCBlbmRdKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGV4dDogJ+acgOi/keS4gOWRqCcsDQogICAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgew0KICAgICAgICAgICAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZSgpDQogICAgICAgICAgICAgIGNvbnN0IHN0YXJ0ID0gbmV3IERhdGUoKQ0KICAgICAgICAgICAgICBzdGFydC5zZXRUaW1lKHN0YXJ0LmdldFRpbWUoKSAtIDM2MDAgKiAxMDAwICogMjQgKiA3KQ0KICAgICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBbc3RhcnQsIGVuZF0pDQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0ZXh0OiAn5pyA6L+R5LiA5Liq5pyIJywNCiAgICAgICAgICAgIG9uQ2xpY2socGlja2VyKSB7DQogICAgICAgICAgICAgIGNvbnN0IGVuZCA9IG5ldyBEYXRlKCkNCiAgICAgICAgICAgICAgY29uc3Qgc3RhcnQgPSBuZXcgRGF0ZSgpDQogICAgICAgICAgICAgIHN0YXJ0LnNldFRpbWUoc3RhcnQuZ2V0VGltZSgpIC0gMzYwMCAqIDEwMDAgKiAyNCAqIDMwKQ0KICAgICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBbc3RhcnQsIGVuZF0pDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICBdDQogICAgICB9LA0KICAgICAgc2VsZWN0UGFyYW1zOiB7DQogICAgICAgIGNsZWFyYWJsZTogdHJ1ZSwNCiAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fpgInmi6knDQogICAgICB9LA0KICAgICAgUHJvamVjdElkOiAnJywNCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgcGFnZUxvYWRpbmc6IGZhbHNlLA0KICAgICAgYWRkUGFnZUFycmF5OiBbDQogICAgICAgIHsNCiAgICAgICAgICBwYXRoOiB0aGlzLiRyb3V0ZS5wYXRoICsgJy9hZGQnLA0KICAgICAgICAgIGhpZGRlbjogdHJ1ZSwNCiAgICAgICAgICBjb21wb25lbnQ6ICgpID0+IGltcG9ydCgnQC92aWV3cy9QUk8vc2hpcG1lbnQvYWN0dWFsbHktc2VudC92NC9hZGQudnVlJyksDQogICAgICAgICAgbmFtZTogJ1BST1NoaXBTZW50QWRkJywNCiAgICAgICAgICBtZXRhOiB7IHRpdGxlOiAn5paw5bu65Y+R6LSn5Y2VJyB9DQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBwYXRoOiB0aGlzLiRyb3V0ZS5wYXRoICsgJy9lZGl0JywNCiAgICAgICAgICBoaWRkZW46IHRydWUsDQogICAgICAgICAgY29tcG9uZW50OiAoKSA9PiBpbXBvcnQoJ0Avdmlld3MvUFJPL3NoaXBtZW50L2FjdHVhbGx5LXNlbnQvdjQvZWRpdC52dWUnKSwNCiAgICAgICAgICBuYW1lOiAnUFJPU2hpcFNlbnRFZGl0JywNCiAgICAgICAgICBtZXRhOiB7IHRpdGxlOiAn57yW6L6R5Y+R6LSn5Y2VJyB9DQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBwYXRoOiB0aGlzLiRyb3V0ZS5wYXRoICsgJy9kZXRhaWwnLA0KICAgICAgICAgIGhpZGRlbjogdHJ1ZSwNCiAgICAgICAgICBjb21wb25lbnQ6ICgpID0+IGltcG9ydCgnQC92aWV3cy9QUk8vc2hpcG1lbnQvYWN0dWFsbHktc2VudC92NC9kZXRhaWwudnVlJyksDQogICAgICAgICAgbmFtZTogJ1BST1NoaXBTZW50RGV0YWlsJywNCiAgICAgICAgICBtZXRhOiB7IHRpdGxlOiAn6K+m5oOFJyB9DQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBwYXRoOiB0aGlzLiRyb3V0ZS5wYXRoICsgJy9jaGFuZ2VSZWNvcmQnLA0KICAgICAgICAgIGhpZGRlbjogdHJ1ZSwNCiAgICAgICAgICBjb21wb25lbnQ6ICgpID0+IGltcG9ydCgnQC92aWV3cy9QUk8vc2hpcG1lbnQvYWN0dWFsbHktc2VudC92NC9jaGFuZ2VSZWNvcmQudnVlJyksDQogICAgICAgICAgbmFtZTogJ1BST1NoaXBTZW50Q2hhbmdlUmVjb3JkJywNCiAgICAgICAgICBtZXRhOiB7IHRpdGxlOiAn5Y+R6LSn5Y2V5Y+Y5pu06K6w5b2VJyB9DQogICAgICAgIH0NCiAgICAgIF0sDQogICAgICBwcm9qZWN0czogW10sDQogICAgICAvLyDljLrln5/mlbDmja4NCiAgICAgIHRyZWVQYXJhbXNBcmVhOiB7DQogICAgICAgICdjaGVjay1zdHJpY3RseSc6IHRydWUsDQogICAgICAgICdleHBhbmQtb24tY2xpY2stbm9kZSc6IGZhbHNlLA0KICAgICAgICAnZGVmYXVsdC1leHBhbmQtYWxsJzogdHJ1ZSwNCiAgICAgICAgZmlsdGVyYWJsZTogZmFsc2UsDQogICAgICAgIGNsaWNrUGFyZW50OiB0cnVlLA0KICAgICAgICBkYXRhOiBbXSwNCiAgICAgICAgcHJvcHM6IHsNCiAgICAgICAgICBjaGlsZHJlbjogJ0NoaWxkcmVuJywNCiAgICAgICAgICBsYWJlbDogJ0xhYmVsJywNCiAgICAgICAgICB2YWx1ZTogJ0lkJw0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgc3R5bGVzOiB7IHdpZHRoOiAnMTAwJScgfSwNCiAgICAgIFNldHVwUG9zaXRpb25EYXRhOiBbXSwNCiAgICAgIHF1ZXJ5SW5mbzogew0KICAgICAgICBQYWdlOiAxLA0KICAgICAgICBQYWdlU2l6ZTogMTAsDQogICAgICAgIFBhcmFtZXRlckpzb246IFtdDQogICAgICB9LA0KICAgICAgcXVlcnlJbmZvMjogew0KICAgICAgICBCZWdpbkRhdGU6ICcnLA0KICAgICAgICBFbmREYXRlOiAnJywNCiAgICAgICAgUGFnZUluZm86IHsNCiAgICAgICAgICBQYXJhbWV0ZXJKc29uOiBbXSwNCiAgICAgICAgICBQYWdlOiAxLA0KICAgICAgICAgIFBhZ2VTaXplOiAyDQogICAgICAgIH0NCiAgICAgIH0sDQogICAgICB0YkNvbmZpZzogew0KICAgICAgICBQYWdlcl9BbGlnbjogJ2NlbnRlcicsDQogICAgICAgIE9wX1dpZHRoOiAyODANCiAgICAgIH0sDQogICAgICBjb2x1bW5zOiBbXSwNCiAgICAgIHRiRGF0YTogW10sDQogICAgICB0b3RhbDogMCwNCiAgICAgIHRiTG9hZGluZzogZmFsc2UsDQogICAgICBzZWxlY3RSb3c6IHt9LA0KICAgICAgdG90YWxEYXRhOiB7DQogICAgICAgIEFsbHN0ZWVsYW1vdW50OiAwLA0KICAgICAgICBBbGxzdGVlbHdlaWdodDogMA0KICAgICAgfSwNCiAgICAgIFByb2Zlc3Npb25hbFR5cGU6IG51bGwsDQogICAgICB0aXRsZTogJycsDQogICAgICBJc19JbnRlZ3JhdGlvbjogZmFsc2UgLy8g5piv5ZCm5LiA5L2T5YyWDQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIHNlbGVjdEVtcHR5KCkgew0KICAgICAgcmV0dXJuIE9iamVjdC5rZXlzKHRoaXMuc2VsZWN0Um93KS5sZW5ndGggPT09IDANCiAgICB9LA0KICAgIGNvbUxpc3QoKSB7DQogICAgICBpZiAoIXRoaXMuSXNfSW50ZWdyYXRpb24pIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuY29sdW1ucy5maWx0ZXIoKGl0ZW0pID0+IHsNCiAgICAgICAgICByZXR1cm4gKA0KICAgICAgICAgICAgaXRlbS5Db2RlICE9PSAnU3VtQWNjZXB0Q291bnQnICYmIGl0ZW0uQ29kZSAhPT0gJ1N1bVBlbmRpbmdDb3VudCcNCiAgICAgICAgICApDQogICAgICAgIH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gdGhpcy5jb2x1bW5zDQogICAgICB9DQogICAgfSwNCiAgICAuLi5tYXBHZXR0ZXJzKCdmYWN0b3J5SW5mbycsIFsnQ29tcG9uZW50X1NoaXBwaW5nX0FwcHJvdmFsJywgJ2F1dG9HZW5lcmF0ZScsICdTaGlwcGluZ19BcHByb3ZhbF9Mb3dlckxpbWl0JywgJ1NoaXBwaW5nX1dlaWdoX0VuYWJsZWQnXSkNCiAgfSwNCiAgYXN5bmMgYWN0aXZhdGVkKCkgew0KICAgIGNvbnNvbGUubG9nKCdhY3RpdmF0ZWQnKQ0KICAgIGlmICh0aGlzLiRyb3V0ZS5xdWVyeS5yZWZyZXNoKSB7DQogICAgICB0aGlzLmZldGNoRGF0YSgpDQogICAgfQ0KICAgIC8vIHRoaXMuZmV0Y2hEYXRhKCkNCiAgfSwNCiAgYXN5bmMgY3JlYXRlZCgpIHsNCiAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnZmFjdG9yeUluZm8vZ2V0V29ya3Nob3AnKQ0KDQogICAgdGhpcy5Jc19JbnRlZ3JhdGlvbiA9IGF3YWl0IHRoaXMuJHN0b3JlLmRpc3BhdGNoKCd1c2VyL2dldFByZWZlcmVuY2VTZXR0aW5nJywgJ0lzX0ludGVncmF0aW9uJykNCiAgICB0aGlzLmdldEZhY3RvcnlUeXBlT3B0aW9uKCkNCiAgICB0aGlzLmdldFByb2plY3RQYWdlTGlzdCgpDQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgY29uc29sZS5sb2coJ21vdW50ZWQnKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgaGFuZGxlQ2FuY2VsRmxvdyhpbnN0YW5jZUlkKSB7DQogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbmkqTlm54/JywgJ+aPkOekuicsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgQ2FuY2VsRmxvdyh7DQogICAgICAgICAgaW5zdGFuY2VJZA0KICAgICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiAn5pON5L2c5oiQ5YqfJywNCiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgdGhpcy5mZXRjaERhdGEoKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICB0eXBlOiAnaW5mbycsDQogICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iCcNCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfSwNCiAgICBzdWJtaXRGb3JSZXZpZXcocm93KSB7DQogICAgICBjb25zdCBJZCA9IHJvdy5JZA0KICAgICAgaWYgKCFyb3cuVmVoaWNsZU5vKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICflj5HotKfljZXovabovobkv6Hmga/mnKrlrozlloTvvIzor7flrozlloTlkI7mj5DkuqQnLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm5o+Q5Lqk5a6h5qC4PycsICfmj5DnpLonLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIFN1Ym1pdEFwcHJvdmFsKHsNCiAgICAgICAgICBJZA0KICAgICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIHRoaXMuZmV0Y2hEYXRhKCkNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiAn5pON5L2c5oiQ5YqfJywNCiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICB0eXBlOiAnaW5mbycsDQogICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iCcNCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVNb25pdG9yKHJvd0lkKSB7DQogICAgICB0aGlzLiRyZWZzWydtb25pdG9yJ10ub3BlbmRpYWxvZyhyb3dJZCwgZmFsc2UpDQogICAgfSwNCiAgICBnZXRBdWRpdFN0YXR1cyhyb3cpIHsNCiAgICAgIHJldHVybiB0aGlzLkNvbXBvbmVudF9TaGlwcGluZ19BcHByb3ZhbCAmJiAodGhpcy5TaGlwcGluZ19BcHByb3ZhbF9Mb3dlckxpbWl0IHx8IDApICogMTAwMCA8IHJvdy5Qcm9qZWN0X1NlbmRpbmdfV2VpZ2h0DQogICAgfSwNCiAgICBhc3luYyBnZXRGYWN0b3J5VHlwZU9wdGlvbigpIHsNCiAgICAgIGF3YWl0IEdldEZhY3RvcnlQcm9mZXNzaW9uYWxCeUNvZGUoew0KICAgICAgICBmYWN0b3J5SWQ6IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdDdXJSZWZlcmVuY2VJZCcpDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLlByb2Zlc3Npb25hbFR5cGUgPSByZXMuRGF0YQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIGF3YWl0IHRoaXMuZ2V0VGFibGVDb25maWcoDQogICAgICAgIGBwcm9fY29tcG9uZW50X291dF9iaWxsX2xpc3QsJHt0aGlzLlByb2Zlc3Npb25hbFR5cGVbMF0uQ29kZX1gDQogICAgICApDQogICAgICB0aGlzLmZldGNoRGF0YSgpDQogICAgfSwNCiAgICBmZXRjaERhdGEoKSB7DQogICAgICB0aGlzLnRiTG9hZGluZyA9IHRydWUNCiAgICAgIGNvbnN0IGZvcm0gPSB7IC4uLnRoaXMuZm9ybSB9DQogICAgICBkZWxldGUgZm9ybVsnZGF0ZVJhbmdlJ10NCiAgICAgIHRoaXMuZm9ybS5kYXRlUmFuZ2UgPSB0aGlzLmZvcm0uZGF0ZVJhbmdlIHx8IFtdDQogICAgICBmb3JtLkJlZ2luRGF0ZSA9IHBhcnNlVGltZSh0aGlzLmZvcm0uZGF0ZVJhbmdlWzBdKQ0KICAgICAgICA/IHBhcnNlVGltZSh0aGlzLmZvcm0uZGF0ZVJhbmdlWzBdKQ0KICAgICAgICA6ICcnDQogICAgICBmb3JtLkVuZERhdGUgPSBwYXJzZVRpbWUodGhpcy5mb3JtLmRhdGVSYW5nZVsxXSkNCiAgICAgICAgPyBwYXJzZVRpbWUodGhpcy5mb3JtLmRhdGVSYW5nZVsxXSkNCiAgICAgICAgOiAnJw0KICAgICAgR2V0UHJvamVjdFNlbmRpbmdJbmZvUGFnZWxpc3QoZm9ybSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy50YkRhdGEgPSByZXMuRGF0YS5EYXRhLm1hcCgodikgPT4gew0KICAgICAgICAgICAgdi5TZW5kRGF0ZSA9IHYuU2VuZERhdGUNCiAgICAgICAgICAgICAgPyBwYXJzZVRpbWUobmV3IERhdGUodi5TZW5kRGF0ZSksICd7eX0te219LXtkfScpDQogICAgICAgICAgICAgIDogdi5TZW5kRGF0ZQ0KICAgICAgICAgICAgcmV0dXJuIHYNCiAgICAgICAgICB9KQ0KICAgICAgICAgIC8vIHRoaXMudGJEYXRhID0gcmVzLkRhdGEuRGF0YTsNCiAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzLkRhdGEuVG90YWxDb3VudA0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLnRiTG9hZGluZyA9IGZhbHNlDQogICAgICB9KQ0KICAgICAgR2V0UHJvamVjdFNlbmRpbmdBbGxDb3VudCh7IC4uLmZvcm0gfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgLy8gY29uc29sZS5sb2cocmVzLkRhdGEsInJlcy5EYXRhIik7DQogICAgICAgICAgdGhpcy50b3RhbERhdGEgPSByZXMuRGF0YQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGdldFBhZ2VMaXN0KCkgew0KICAgICAgdGhpcy5mZXRjaERhdGEoKQ0KICAgICAgY29uc29sZS5sb2codGhpcy5mb3JtLCAndGhpcy5mb3JtJykNCiAgICB9LA0KICAgIGhhbmRTdWJtaXQocm93KSB7DQogICAgICBjb25zdCBJZCA9IHJvdy5JZA0KICAgICAgaWYgKCFyb3cuVmVoaWNsZU5vKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICflj5HotKfljZXovabovobkv6Hmga/mnKrlrozlloTvvIzor7flrozlloTlkI7mj5DkuqQnLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuaPkOS6pOi/h+ejhT8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICBTdWJtaXRXZWlnaGluZ0ZvclBDKHsNCiAgICAgICAgICBJZA0KICAgICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIHRoaXMuZmV0Y2hEYXRhKCkNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuRGF0YSwNCiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICB0eXBlOiAnaW5mbycsDQogICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iCcNCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfSwNCiAgICBkYXRlUGlja2Vyd3JhcHBlcigpIHsNCiAgICAgIC8vIGNvbnNvbGUubG9nKGZvcm0sImZvcm0xMTExMTExMTExIik7DQogICAgICAvLyBjb25zb2xlLmxvZyhuZXcgRGF0ZSgiMjAyMi0wOS0yOFQxMDoxMjozNS41ODNaIiksIm5ldyBEYXRlMTExIik7DQogICAgICAvLyBjb25zb2xlLmxvZyhwYXJzZVRpbWUobmV3IERhdGUoIjIwMjItMTAtMTFUMTQ6MDM6NTQiKSksIm5ldyBEYXRlMjIyIik7DQogICAgICBpZiAoIXRoaXMuZm9ybS5kYXRlUmFuZ2UpIHsNCiAgICAgICAgdGhpcy5mb3JtLmRhdGVSYW5nZSA9IFsnJywgJyddDQogICAgICB9DQogICAgICB0aGlzLmZldGNoRGF0YSgpDQogICAgfSwNCiAgICByZXNldEZvcm0oZm9ybU5hbWUpIHsNCiAgICAgIHRoaXMuJHJlZnNbZm9ybU5hbWVdLnJlc2V0RmllbGRzKCkNCiAgICAgIHRoaXMuZmV0Y2hEYXRhKCkNCiAgICB9LA0KICAgIHN1Ym1pdEZvcm0yKGZvcm1OYW1lKSB7DQogICAgICB0aGlzLiRyZWZzW2Zvcm1OYW1lXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgY29uc3QgeyBQcm9qZWN0SWQsIEFyZWFfSWQsIEluc3RhbGxVbml0X0lkIH0gPSB0aGlzLmZvcm0yDQogICAgICAgICAgY29uc3Qgew0KICAgICAgICAgICAgTmFtZSwNCiAgICAgICAgICAgIElkLA0KICAgICAgICAgICAgQ29kZSwNCiAgICAgICAgICAgIFNQSUNfVXNlck5hbWUsDQogICAgICAgICAgICBBZGRyZXNzLA0KICAgICAgICAgICAgUmVjZWl2ZXIsDQogICAgICAgICAgICBSZWNlaXZlcl9UZWwsDQogICAgICAgICAgICBTeXNfUHJvamVjdF9JZCwNCiAgICAgICAgICAgIFJlY2VpdmVfVXNlck5hbWUNCiAgICAgICAgICB9ID0gdGhpcy5wcm9qZWN0cy5maW5kKCh2KSA9PiB2LklkID09PSB0aGlzLmZvcm0yLlByb2plY3RJZCkNCiAgICAgICAgICBjb25zdCBkYXRhID0gew0KICAgICAgICAgICAgUHJvamVjdElkLA0KICAgICAgICAgICAgQXJlYV9JZCwNCiAgICAgICAgICAgIEluc3RhbGxVbml0X0lkLA0KICAgICAgICAgICAgSWQsDQogICAgICAgICAgICBOYW1lLA0KICAgICAgICAgICAgQ29kZSwNCiAgICAgICAgICAgIEFkZHJlc3MsDQogICAgICAgICAgICBSZWNlaXZlciwNCiAgICAgICAgICAgIFJlY2VpdmVyX1RlbCwNCiAgICAgICAgICAgIFN5c19Qcm9qZWN0X0lkLA0KICAgICAgICAgICAgUmVjZWl2ZV9Vc2VyTmFtZSwNCiAgICAgICAgICAgIGF1dG9HZW5lcmF0ZTogdGhpcy5hdXRvR2VuZXJhdGUsDQogICAgICAgICAgICBQcm9mZXNzaW9uYWxUeXBlOiB0aGlzLlByb2Zlc3Npb25hbFR5cGUNCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICAgICAgbmFtZTogJ1BST1NoaXBTZW50QWRkJywNCiAgICAgICAgICAgIHF1ZXJ5OiB7DQogICAgICAgICAgICAgIHBnX3JlZGlyZWN0OiAnUFJPU2hpcFNlbnQnLA0KICAgICAgICAgICAgICBwOiBlbmNvZGVVUklDb21wb25lbnQoSlNPTi5zdHJpbmdpZnkoZGF0YSkpDQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgICAgICAgIHRoaXMuJHJlZnMuZm9ybTIucmVzZXRGaWVsZHMoKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUubG9nKCdlcnJvciBzdWJtaXQhIScpDQogICAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICByZXNldEZvcm0yKGZvcm1OYW1lKSB7DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgICAgdGhpcy4kcmVmc1tmb3JtTmFtZV0ucmVzZXRGaWVsZHMoKQ0KICAgIH0sDQogICAgaGFuZGxlQ2xvc2UoKSB7DQogICAgICB0aGlzLiRyZWZzLmZvcm0yLnJlc2V0RmllbGRzKCkNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgfSwNCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgICAvLyB0aGlzLiRyb3V0ZXIucHVzaCh7IG5hbWU6ICdQUk9TaGlwU2VudEFkZCcsIHF1ZXJ5OiB7IHBnX3JlZGlyZWN0OiAnUFJPU2hpcFNlbnQnIH19KQ0KICAgIH0sDQogICAgZ2V0UHJvamVjdFBhZ2VMaXN0KCkgew0KICAgICAgR2V0UHJvamVjdFBhZ2VMaXN0KHsgUGFnZVNpemU6IC0xIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMucHJvamVjdHMgPSByZXMuRGF0YS5EYXRhDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBwcm9qZWN0SWRDaGFuZ2UoZSkgew0KICAgICAgY29uc29sZS5sb2coZSwgJ2UnKQ0KICAgICAgLy8gaWYgKGUpIHsNCiAgICAgIC8vICAgdGhpcy5nZXRBcmVhTGlzdCgpOw0KICAgICAgLy8gfQ0KICAgIH0sDQogICAgcHJvamVjdElkQ2xlYXIoZSkgew0KICAgICAgdGhpcy4kcmVmcy5mb3JtMi5yZXNldEZpZWxkcygpDQogICAgfSwNCiAgICAvLyDojrflj5bljLrln58NCiAgICBnZXRBcmVhTGlzdCgpIHsNCiAgICAgIEdlQXJlYVRyZWVzKHsNCiAgICAgICAgcHJvamVjdElkOiB0aGlzLmZvcm0yLlByb2plY3RJZA0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy50cmVlUGFyYW1zQXJlYS5kYXRhID0gcmVzLkRhdGENCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoXykgPT4gew0KICAgICAgICAgICAgdGhpcy4kcmVmcy50cmVlU2VsZWN0QXJlYS50cmVlRGF0YVVwZGF0ZUZ1bihyZXMuRGF0YSkNCiAgICAgICAgICB9KQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGZpbHRlckZ1bih2YWwsIHJlZikgew0KICAgICAgdGhpcy4kcmVmc1tyZWZdLmZpbHRlckZ1bih2YWwpDQogICAgfSwNCiAgICBhcmVhQ2hhbmdlKGUpIHsNCiAgICAgIGNvbnNvbGUubG9nKGUsICdlJykNCiAgICAgIHRoaXMuZ2V0SW5zdGFsbCgpDQogICAgfSwNCiAgICAvLyDmuIXnqbrljLrln58NCiAgICBhcmVhQ2xlYXIoKSB7DQogICAgICB0aGlzLmZvcm0yLkFyZWFfSWQgPSAnJw0KICAgICAgdGhpcy5mb3JtLkluc3RhbGxVbml0X0lkID0gJycNCiAgICB9LA0KICAgIC8vIOiOt+WPluaJueasoQ0KICAgIGdldEluc3RhbGwoKSB7DQogICAgICBHZXRJbnN0YWxsVW5pdFBhZ2VMaXN0KHsNCiAgICAgICAgQXJlYV9JZDogdGhpcy5mb3JtMi5BcmVhX0lkLA0KICAgICAgICBQYWdlOiAxLA0KICAgICAgICBQYWdlU2l6ZTogLTENCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICB0aGlzLlNldHVwUG9zaXRpb25EYXRhID0gcmVzLkRhdGEuRGF0YQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZUVkaXQoaWQsIGlzU3ViKSB7DQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7DQogICAgICAgIG5hbWU6ICdQUk9TaGlwU2VudEVkaXQnLA0KICAgICAgICBxdWVyeTogeyBwZ19yZWRpcmVjdDogJ1BST1NoaXBTZW50JywgaWQsIGlzU3ViOiBpc1N1YiA/ICcxJyA6ICcwJywNCiAgICAgICAgICBwOiBlbmNvZGVVUklDb21wb25lbnQoSlNPTi5zdHJpbmdpZnkoeyBhdXRvR2VuZXJhdGU6IHRoaXMuYXV0b0dlbmVyYXRlIH0pKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g5pKk5Zue6Iez6I2J56i/DQogICAgaGFuZGxlV2l0aGRyYXcoaWQpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aSpOWbnuiHs+iNieeovywg5piv5ZCm57un57utPycsICfmj5DnpLonLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIFdpdGhkcmF3RHJhZnQoew0KICAgICAgICAgICAgaWQ6IGlkDQogICAgICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAn5pKk6ZSA5oiQ5YqfJywNCiAgICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycNCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgdGhpcy5mZXRjaERhdGEoKQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7IH0pDQogICAgfSwNCiAgICBoYW5kbGVTdWIocm93KSB7DQogICAgICBjb25zdCBpZCA9IHJvdy5JZA0KICAgICAgaWYgKCFyb3cuVmVoaWNsZU5vKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICflj5HotKfljZXovabovobkv6Hmga/mnKrlrozlloTvvIzor7flrozlloTlkI7mj5DkuqQnLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aPkOS6pOivpeWPkei0p+WNlSwg5piv5ZCm57un57utPycsICfmj5DnpLonLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMucGFnZUxvYWRpbmcgPSB0cnVlDQogICAgICAgICAgU3VibWl0UHJvamVjdFNlbmRpbmcoew0KICAgICAgICAgICAgSWQ6IGlkDQogICAgICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAn5Y+R6LSn5oiQ5YqfJywNCiAgICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycNCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgdGhpcy5mZXRjaERhdGEoKQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgdGhpcy5wYWdlTG9hZGluZyA9IGZhbHNlDQogICAgICAgICAgfSkNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICB0aGlzLnBhZ2VMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZURlbChpZCkgew0KICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm5Yig6Zmk6K+l5Y+R6LSn5Y2VPycsICfmj5DnpLonLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIERlbGV0ZVByb2plY3RTZW5kaW5nSW5mbyh7DQogICAgICAgICAgSWQ6IGlkDQogICAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKnycsDQogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHRoaXMuZmV0Y2hEYXRhKCkNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgdHlwZTogJ2luZm8nLA0KICAgICAgICAgIG1lc3NhZ2U6ICflt7Llj5bmtojliKDpmaQnDQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlSW5mbyhpZCkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICBuYW1lOiAnUFJPU2hpcFNlbnREZXRhaWwnLA0KICAgICAgICBxdWVyeTogeyBwZ19yZWRpcmVjdDogJ1BST1NoaXBTZW50JywgaWQgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZUNoYW5nZShpZCkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICBuYW1lOiAnUFJPU2hpcFNlbnRDaGFuZ2VSZWNvcmQnLA0KICAgICAgICBxdWVyeTogeyBwZ19yZWRpcmVjdDogJ1BST1NoaXBTZW50JywgaWQgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIHByaW50TWUoKSB7fSwNCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0aGlzLnRpdGxlID0gJ+WvvOWHuicNCiAgICAgIHRoaXMuJG5leHRUaWNrKF8gPT4gew0KICAgICAgICB0aGlzLiRyZWZzLnJhZGlvRGlhbG9nLmhhbmRsZU9wZW4odGhpcy5zZWxlY3RMaXN0KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZUV4cG9ydEV4Y2VsKCkgew0KICAgICAgdGhpcy50aXRsZSA9ICflr7zlh7onDQogICAgICB0aGlzLiRyZWZzLmRpYWxvZ0V4Y2VsLmhhbmRsZU9wZW4odGhpcy5zZWxlY3RMaXN0KQ0KICAgIH0sDQogICAgaGFuZGxlUHJpbnQoKSB7DQogICAgICB0aGlzLnRpdGxlID0gJ+aJk+WNsCcNCiAgICAgIHRoaXMuJHJlZnMucmFkaW9EaWFsb2cuaGFuZGxlT3Blbih0aGlzLnNlbGVjdExpc3QpDQogICAgfSwNCiAgICBoYW5kbGVQcmludE5vV2VpZ2h0KCkgew0KICAgICAgLy8gY29uc29sZS5sb2codGhpcy5zZWxlY3RSb3cuQ29kZSwgInRoaXMuc2VsZWN0Um93LkNvZGUiKTsNCiAgICAgIC8vIGNvbnNvbGUubG9nKHRoaXMuc2VsZWN0Um93LklkLCAidGhpcy5zZWxlY3RSb3cuSWQiKTsNCiAgICAgIFRyYW5zZm9ybXNXaXRob3V0V2VpZ2h0KHsNCiAgICAgICAgc2VuZElkOiB0aGlzLnNlbGVjdFJvdy5JZA0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgLy8gY29uc3QgdGVtcGxhdGVVcmwgPSBjb21iaW5lVVJMKHRoaXMuJGJhc2VVcmwsIHJlcy5EYXRhKTsNCiAgICAgICAgICAvLyB3aW5kb3cub3Blbih0ZW1wbGF0ZVVybCwgIl9ibGFuayIpOw0KICAgICAgICAgIGNvbnN0IHVybCA9IG5ldyBVUkwocmVzLkRhdGEsIGJhc2VVcmwoKSkNCiAgICAgICAgICB3aW5kb3cub3Blbih1cmwuaHJlZiwgJ19ibGFuaycpDQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsDQogICAgICAgICAgICBtZXNzYWdlOiAn5omT5Y2w5oiQ5YqfIScNCiAgICAgICAgICB9KQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIC8vIHRoaXMuJHJlZnMuUHJpbnREaWFsb2cub3Blbih0aGlzLnNlbGVjdFJvdy5Db2RlKQ0KICAgIH0sDQoNCiAgICAvLyDlr7zlh7oNCiAgICBoYW5kbGVMZWFkaW5nT3V0KCkgew0KICAgICAgdGhpcy5idG5Mb2FkaW5nID0gdHJ1ZQ0KICAgICAgY29uc3QgZm9ybSA9IHsgLi4udGhpcy5mb3JtIH0NCiAgICAgIGRlbGV0ZSBmb3JtWydkYXRlUmFuZ2UnXQ0KICAgICAgZGVsZXRlIGZvcm1bJ1BhZ2VJbmZvJ10NCiAgICAgIHRoaXMuZm9ybS5kYXRlUmFuZ2UgPSB0aGlzLmZvcm0uZGF0ZVJhbmdlIHx8IFtdDQogICAgICBmb3JtLkJlZ2luRGF0ZSA9IHBhcnNlVGltZSh0aGlzLmZvcm0uZGF0ZVJhbmdlWzBdKQ0KICAgICAgICA/IHBhcnNlVGltZSh0aGlzLmZvcm0uZGF0ZVJhbmdlWzBdKQ0KICAgICAgICA6ICcnDQogICAgICBmb3JtLkVuZERhdGUgPSBwYXJzZVRpbWUodGhpcy5mb3JtLmRhdGVSYW5nZVsxXSkNCiAgICAgICAgPyBwYXJzZVRpbWUodGhpcy5mb3JtLmRhdGVSYW5nZVsxXSkNCiAgICAgICAgOiAnJw0KICAgICAgRXhwb3J0SW52b2ljZUxpc3QoeyAuLi5mb3JtIH0pDQogICAgICAgIC50aGVuKHJlcyA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5a+85Ye65oiQ5YqfJykNCiAgICAgICAgICAgIGNvbnN0IHVybCA9IG5ldyBVUkwocmVzLkRhdGEsIGJhc2VVcmwoKSkNCiAgICAgICAgICAgIHdpbmRvdy5vcGVuKHVybC5ocmVmKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICAuZmluYWxseSgoKSA9PiB7DQogICAgICAgICAgLy8g57uT5p2fbG9hZGluZw0KICAgICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlDQogICAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2UobGlzdCkgew0KICAgICAgdGhpcy5zZWxlY3RMaXN0ID0gbGlzdA0KICAgIH0sDQogICAgc2VsZWN0Q2hhbmdlKHsgc2VsZWN0aW9uLCByb3cgfSkgew0KICAgICAgdGhpcy4kcmVmcy5keVRhYmxlLiRyZWZzLmR0YWJsZS5jbGVhclNlbGVjdGlvbigpDQogICAgICBpZiAoc2VsZWN0aW9uLmxlbmd0aCAhPSAwKSB7DQogICAgICAgIHRoaXMuc2VsZWN0Um93ID0gcm93DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnNlbGVjdFJvdyA9IHt9DQogICAgICB9DQogICAgICBpZiAoc2VsZWN0aW9uLmxlbmd0aCA+IDEpIHsNCiAgICAgICAgc2VsZWN0aW9uLnNoaWZ0KCkNCiAgICAgIH0NCiAgICAgIC8vIGNvbnNvbGUubG9nKHNlbGVjdGlvbiwgInNlbGVjdGlvbjIiKTsNCiAgICAgIHRoaXMuJHJlZnMuZHlUYWJsZS4kcmVmcy5kdGFibGUudG9nZ2xlUm93U2VsZWN0aW9uKA0KICAgICAgICByb3csDQogICAgICAgICEhc2VsZWN0aW9uLmxlbmd0aA0KICAgICAgKQ0KICAgIH0sDQogICAgaGFuZGxlU2VsZWN0QWxsKCkgew0KICAgICAgLy8gdGhpcy4kcmVmcy5keVRhYmxlLiRyZWZzLmR0YWJsZS5jbGVhclNlbGVjdGlvbigpDQogICAgfSwNCiAgICBoYW5kZWxWaWV3KElkKSB7DQogICAgICB0aGlzLklzVmlzYWJlbCA9IHRydWUNCiAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgIHRoaXMuJHJlZnMuY2hlY2tEaWFsb2cuaGFuZGVsT3BlbihJZCkNCiAgICAgIH0pDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAs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file": "index.vue", "sourceRoot": "src/views/PRO/shipment/actually-sent/v4", "sourcesContent": ["<!--成品发货-->\r\n<template>\r\n  <div\r\n    v-loading=\"pageLoading\"\r\n    class=\"abs100 cs-z-flex-pd16-wrap\"\r\n    style=\"display: flex; flex-direction: column\"\r\n  >\r\n    <div\r\n      class=\"cs-z-page-main-content\"\r\n      style=\"height: auto; margin-bottom: 16px\"\r\n    >\r\n      <top-header style=\"height: 100px; line-height: normal\">\r\n        <template #left>\r\n          <el-form\r\n            ref=\"searchForm\"\r\n            :inline=\"true\"\r\n            :model=\"form\"\r\n            class=\"demo-form-inline form-search\"\r\n            style=\"height: 100px\"\r\n          >\r\n            <el-form-item label=\"发货单号：\" prop=\"Code\">\r\n              <el-input\r\n                v-model=\"form.Code\"\r\n                clearable\r\n                placeholder=\"请输入\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"发货单状态：\" prop=\"Status\">\r\n              <el-select v-model=\"form.Status\" clearable placeholder=\"请选择\">\r\n                <!--                <el-option label=\"未发货\" :value=\"0\" />\r\n                <el-option label=\"已发货\" :value=\"1\" />\r\n                <el-option label=\"部分验收\" :value=\"2\" />\r\n                <el-option label=\"已验收\" :value=\"3\" />-->\r\n                <!--                <el-option label=\"草稿\" :value=\"0\" />\r\n                <el-option label=\"待过磅\" :value=\"2\" />\r\n                <el-option label=\"已过磅\" :value=\"3\" />\r\n                <el-option label=\"未验收\" :value=\"4\" />\r\n                <el-option label=\"部分验收\" :value=\"5\" />\r\n                <el-option label=\"已验收\" :value=\"6\" />-->\r\n\r\n                <el-option v-for=\"(item,key) in statusInfo\" :key=\"key\" :label=\"item\" :value=\"key\" />\r\n\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"项目名称\" prop=\"ProjectId\">\r\n              <el-select\r\n                v-model=\"form.ProjectId\"\r\n                class=\"w100\"\r\n                placeholder=\"请选择\"\r\n                filterable\r\n                clearable\r\n              >\r\n                <el-option\r\n                  v-for=\"item in projects\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Short_Name\"\r\n                  :value=\"item.Sys_Project_Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"收货人：\" prop=\"Consignee\">\r\n              <el-input\r\n                v-model=\"form.Consignee\"\r\n                clearable\r\n                placeholder=\"请输入\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"车牌号：\" prop=\"VehicleNo\">\r\n              <el-input\r\n                v-model=\"form.VehicleNo\"\r\n                clearable\r\n                placeholder=\"请输入\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"是否退货\" prop=\"IsReturn\">\r\n              <el-select v-model=\"form.IsReturn\" clearable placeholder=\"请选择\">\r\n                <el-option label=\"无退货\" :value=\"false\" />\r\n                <el-option label=\"有退货\" :value=\"true\" />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"过磅预警\" prop=\"Is_Weight_Warning\">\r\n              <el-select v-model=\"form.Is_Weight_Warning\" clearable placeholder=\"请选择\">\r\n                <el-option label=\"是\" :value=\"true\" />\r\n                <el-option label=\"否\" :value=\"false\" />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item>\r\n              <el-button\r\n                type=\"primary\"\r\n                @click=\"\r\n                  () => {\r\n                    form.PageInfo.Page = 1;\r\n                    getPageList();\r\n                  }\r\n                \"\r\n              >查询</el-button>\r\n              <el-button @click=\"resetForm('searchForm')\">重置</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </template>\r\n      </top-header>\r\n    </div>\r\n    <div class=\"cs-z-page-main-content\" style=\"flex: 1; display: -webkit-box\">\r\n      <div style=\"color: rgba(34, 40, 52, 0.65); padding: 10px 0px\">\r\n        <el-button type=\"primary\" @click=\"handleAdd\">新增发货单</el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          :disabled=\"!selectList.length\"\r\n          @click=\"handleExport\"\r\n        >导出发货单(pdf)</el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          :disabled=\"selectEmpty\"\r\n          @click=\"handleExportExcel\"\r\n        >导出发货单(excel)</el-button>\r\n        <ExportCustomReport code=\"Shipping_single_template\" style=\"margin:0 10px\" name=\"自定义发货单(excel)\" :ids=\"selectList.map(i=>i.Id)\"></ExportCustomReport>\r\n        <el-button\r\n          type=\"success\"\r\n          :disabled=\"!selectList.length\"\r\n          @click=\"handlePrint\"\r\n        >打印</el-button>\r\n        <!--        <el-button-->\r\n        <!--          type=\"success\"-->\r\n        <!--          @click=\"handlePrintNoWeight\"-->\r\n        <!--          :disabled=\"!selectRow\"-->\r\n        <!--          >预览(无重量)-->\r\n        <!--        </el-button>-->\r\n        <el-button\r\n          type=\"success\"\r\n          :loading=\"btnLoading\"\r\n          :disabled=\"tbData.length === 0\"\r\n          @click=\"handleLeadingOut\"\r\n        >导出</el-button>\r\n        <div class=\"date-picker-wrapper\">\r\n          <el-date-picker\r\n            v-model=\"form.dateRange\"\r\n            style=\"width: 100%\"\r\n            type=\"daterange\"\r\n            align=\"right\"\r\n            unlink-panels\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            :picker-options=\"pickerOptions\"\r\n            @change=\"datePickerwrapper\"\r\n          />\r\n        </div>\r\n        <div v-if=\"ProfessionalType\" class=\"total-wrapper\">\r\n          <span\r\n            style=\"margin: 0 24px 0 12px\"\r\n          >总数：{{ totalData.Allsteelamount }}</span><span>总重：{{ totalData.Allsteelweight }}（{{\r\n            ProfessionalType[0].Unit\r\n          }}）</span>\r\n        </div>\r\n      </div>\r\n      <div\r\n        v-loading=\"tbLoading\"\r\n        class=\"fff cs-z-tb-wrapper\"\r\n        style=\"flex: 1 1 auto\"\r\n      >\r\n        <dynamic-data-table\r\n          ref=\"dyTable\"\r\n          class=\"cs-plm-dy-table\"\r\n          :columns=\"comList\"\r\n          :config=\"tbConfig\"\r\n          :data=\"tbData\"\r\n          :page=\"queryInfo.Page\"\r\n          :total=\"total\"\r\n          border\r\n          stripe\r\n          @gridPageChange=\"handlePageChange\"\r\n          @gridSizeChange=\"handleSizeChange\"\r\n          @select=\"selectChange\"\r\n          @multiSelectedChange=\"handleSelectionChange\"\r\n          @selectAll=\"handleSelectAll\"\r\n        >\r\n          <template slot=\"SumNetWeight\" slot-scope=\"{ row }\">\r\n            {{ row.SumNetWeight | displayValue }}\r\n          </template>\r\n          <template slot=\"Status\" slot-scope=\"{ row }\">\r\n            {{ statusInfo[row.Status] }}\r\n          </template>\r\n          <template slot=\"Number\" slot-scope=\"{ row }\">\r\n            <div>{{ row.Number || '-' }}</div>\r\n          </template>\r\n          <template slot=\"SumReturnCount\" slot-scope=\"{ row }\">\r\n            <span>{{ row.SumReturnCount != null ? row.SumReturnCount : '-' }}</span>\r\n          </template>\r\n          <template slot=\"SumAcceptCount\" slot-scope=\"{ row }\">\r\n            <span>{{ row.SumAcceptCount != null ? row.SumAcceptCount : '-' }}</span>\r\n          </template>\r\n          <template slot=\"SumPendingCount\" slot-scope=\"{ row }\">\r\n            <span>{{ row.SumPendingCount != null ? row.SumPendingCount : '-' }}</span>\r\n          </template>\r\n          <template slot=\"SendDate\" slot-scope=\"{ row }\">\r\n            <div>\r\n              {{ row.SendDate || \"—\" }}\r\n            </div>\r\n          </template>\r\n          <template slot=\"op\" slot-scope=\"{ row, index }\">\r\n            <template v-if=\"row.Status == '0'\">\r\n              <el-button\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handleEdit(row.Id, false)\"\r\n              >编辑</el-button>\r\n              <el-button\r\n                v-if=\"Shipping_Weigh_Enabled\"\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handSubmit(row)\"\r\n              >提交过磅</el-button>\r\n              <template v-else>\r\n                <el-button v-if=\"getAuditStatus(row)\" type=\"text\" @click=\"submitForReview(row)\">提交审核</el-button>\r\n                <el-button\r\n                  v-else\r\n                  :index=\"index\"\r\n                  type=\"text\"\r\n                  @click=\"handleSub(row)\"\r\n                >提交发货</el-button>\r\n              </template>\r\n              <el-button\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                style=\"color:red\"\r\n                @click=\"handleDel(row.Id)\"\r\n              >删除</el-button>\r\n            </template>\r\n            <template v-else>\r\n              <el-button\r\n                v-if=\"row.Status == '2'\"\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handleWithdraw(row.Id)\"\r\n              >撤回草稿</el-button>\r\n              <el-button\r\n                v-if=\"row.Status != '999'\"\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handleEdit(row.Id, row.Status!='-1')\"\r\n              >编辑</el-button>\r\n              <el-button\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handleInfo(row.Id)\"\r\n              >查看</el-button>\r\n              <el-button\r\n                v-if=\"[4,5,6].includes(+row.Status)\"\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handleChange(row.Id)\"\r\n              >变更记录</el-button>\r\n              <el-button\r\n                v-if=\"Is_Integration&&[4,5,6].includes(+row.Status)\"\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handelView(row.Id)\"\r\n              >验收情况</el-button>\r\n            </template>\r\n            <el-button v-if=\"row.Status==999\" type=\"text\" @click=\"handleCancelFlow(row.FlowId)\">撤回</el-button>\r\n            <template v-if=\"row.Status==3\">\r\n              <el-button\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handleWithdraw(row.Id)\"\r\n              >撤回草稿</el-button>\r\n              <el-button v-if=\"getAuditStatus(row)\" type=\"text\" @click=\"submitForReview(row)\">提交审核</el-button>\r\n              <el-button\r\n                v-else\r\n                :index=\"index\"\r\n                type=\"text\"\r\n                @click=\"handleSub(row)\"\r\n              >提交发货</el-button>\r\n            </template>\r\n            <el-button v-if=\"row.FlowId\" type=\"text\" @click=\"handleMonitor(row.FlowId)\">监控</el-button>\r\n            <template v-if=\"row.Status==-1\">\r\n              <el-button v-if=\"Shipping_Weigh_Enabled\" type=\"text\" @click=\"handSubmit(row)\">提交过磅</el-button>\r\n              <template v-else>\r\n                <el-button v-if=\"getAuditStatus(row)\" type=\"text\" @click=\"submitForReview(row)\">提交审核</el-button>\r\n                <el-button\r\n                  v-else\r\n                  :index=\"index\"\r\n                  type=\"text\"\r\n                  @click=\"handleSub(row)\"\r\n                >提交发货</el-button>\r\n              </template>\r\n            </template>\r\n          </template>\r\n          <!-- <template slot=\"Out_Date\" slot-scope=\"{ row }\">\r\n            {{ row.Out_Date | timeFormat }}\r\n          </template> -->\r\n        </dynamic-data-table>\r\n      </div>\r\n      <el-dialog\r\n        v-dialogDrag\r\n        title=\"新增发货单\"\r\n        class=\"plm-custom-dialog\"\r\n        :visible.sync=\"dialogVisible\"\r\n        width=\"30%\"\r\n        @close=\"handleClose\"\r\n      >\r\n        <el-form\r\n          ref=\"form2\"\r\n          :model=\"form2\"\r\n          :rules=\"rules\"\r\n          label-width=\"70px\"\r\n          class=\"demo-ruleForm\"\r\n        >\r\n          <el-form-item label=\"项目\" prop=\"ProjectId\">\r\n            <el-select\r\n              v-model=\"form2.ProjectId\"\r\n              class=\"w100\"\r\n              placeholder=\"请选择\"\r\n              filterable\r\n              clearable\r\n              @change=\"projectIdChange\"\r\n              @clear=\"projectIdClear\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in projects\"\r\n                :key=\"item.Id\"\r\n                :label=\"item.Short_Name\"\r\n                :value=\"item.Id\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item style=\"text-align: right\">\r\n            <el-button @click=\"resetForm2('form2')\">取 消</el-button>\r\n            <el-button\r\n              type=\"primary\"\r\n              @click=\"submitForm2('form2')\"\r\n            >确 定</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-dialog>\r\n    </div>\r\n    <PrintDialog ref=\"PrintDialog\" />\r\n    <radioDialog\r\n      ref=\"radioDialog\"\r\n      :send-id=\"selectRow.Id\"\r\n      :title=\"title\"\r\n      :send-data=\"selectRow\"\r\n    />\r\n    <dialogExcel\r\n      ref=\"dialogExcel\"\r\n      :send-id=\"selectRow.Id\"\r\n      :title=\"title\"\r\n      :send-data=\"selectRow\"\r\n    />\r\n    <checkDialog ref=\"checkDialog\" />\r\n    <Monitor ref=\"monitor\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Monitor from '@/components/Monitor/index.vue'\r\nimport TopHeader from '@/components/TopHeader/index.vue'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable.vue'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info-pro2'\r\nimport {\r\n  GetProjectSendingInfoPagelist,\r\n  DeleteProjectSendingInfo,\r\n  SubmitProjectSending,\r\n  GetProjectSendingAllCount,\r\n  TransformsWithoutWeight,\r\n  SubmitWeighingForPC,\r\n  WithdrawDraft,\r\n  ExportInvoiceList, SubmitApproval, CancelFlow\r\n} from '@/api/PRO/component-stock-out'\r\nimport { GetProjectPageList } from '@/api/PRO/pro-schedules'\r\nimport { GeAreaTrees } from '@/api/PRO/project'\r\nimport { GetInstallUnitPageList } from '@/api/PRO/install-unit'\r\nimport { parseTime } from '@/utils'\r\nimport { baseUrl } from '@/utils/baseurl'\r\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\r\nimport PrintDialog from './component/printDialog.vue'\r\nimport radioDialog from './component/dialog.vue'\r\nimport dialogExcel from './component/dialogExcel.vue'\r\nimport checkDialog from './component/check.vue'\r\nimport { mapGetters } from 'vuex'\r\nimport ExportCustomReport from \"@/components/ExportCustomReport/index.vue\";\r\n\r\nconst StatusMap = {\r\n  0: '草稿',\r\n  2: '待过磅',\r\n  3: '已过磅',\r\n  4: '未验收',\r\n  5: '部分验收',\r\n  6: '已验收',\r\n  7: '已退货',\r\n  999: '审批中',\r\n  '-1': '已退回'\r\n}\r\n\r\nexport default {\r\n  components: {\r\n    ExportCustomReport,\r\n    TopHeader,\r\n    Monitor,\r\n    DynamicDataTable,\r\n    PrintDialog,\r\n    radioDialog,\r\n    checkDialog,\r\n    dialogExcel\r\n  },\r\n  filters: {\r\n    sendDateFilter(e) {\r\n      // console.log(e,\"eeee\");\r\n      return parseTime(new Date(e))\r\n    }\r\n  },\r\n  mixins: [addRouterPage, getTbInfo],\r\n  data() {\r\n    return {\r\n      selectList: [],\r\n      statusInfo: StatusMap,\r\n      IsVisabel: false,\r\n      btnLoading: false,\r\n      form: {\r\n        Code: '',\r\n        Status: null,\r\n        ProjectId: '',\r\n        VehicleNo: '',\r\n        Consignee: '',\r\n        IsReturn: null,\r\n        Is_Weight_Warning: null,\r\n        dateRange: ['', ''],\r\n        PageInfo: {\r\n          ParameterJson: [],\r\n          Page: 1,\r\n          PageSize: 20\r\n        }\r\n      },\r\n      form2: {\r\n        ProjectId: '',\r\n        Area_Id: '',\r\n        InstallUnit_Id: ''\r\n      },\r\n      rules: {\r\n        ProjectId: [{ required: true, message: '请选择', trigger: 'change' }]\r\n        // Area_Id: [{ required: true, message: \"请选择\", trigger: \"change\" }],\r\n      },\r\n      pickerOptions: {\r\n        shortcuts: [\r\n          {\r\n            text: '今天',\r\n            onClick(picker) {\r\n              const end = new Date()\r\n              const start = new Date()\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 1)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          },\r\n          {\r\n            text: '最近一周',\r\n            onClick(picker) {\r\n              const end = new Date()\r\n              const start = new Date()\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          },\r\n          {\r\n            text: '最近一个月',\r\n            onClick(picker) {\r\n              const end = new Date()\r\n              const start = new Date()\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)\r\n              picker.$emit('pick', [start, end])\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      selectParams: {\r\n        clearable: true,\r\n        placeholder: '请选择'\r\n      },\r\n      ProjectId: '',\r\n      dialogVisible: false,\r\n      pageLoading: false,\r\n      addPageArray: [\r\n        {\r\n          path: this.$route.path + '/add',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/shipment/actually-sent/v4/add.vue'),\r\n          name: 'PROShipSentAdd',\r\n          meta: { title: '新建发货单' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/edit',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/shipment/actually-sent/v4/edit.vue'),\r\n          name: 'PROShipSentEdit',\r\n          meta: { title: '编辑发货单' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/detail',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/shipment/actually-sent/v4/detail.vue'),\r\n          name: 'PROShipSentDetail',\r\n          meta: { title: '详情' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/changeRecord',\r\n          hidden: true,\r\n          component: () => import('@/views/PRO/shipment/actually-sent/v4/changeRecord.vue'),\r\n          name: 'PROShipSentChangeRecord',\r\n          meta: { title: '发货单变更记录' }\r\n        }\r\n      ],\r\n      projects: [],\r\n      // 区域数据\r\n      treeParamsArea: {\r\n        'check-strictly': true,\r\n        'expand-on-click-node': false,\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      styles: { width: '100%' },\r\n      SetupPositionData: [],\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: 10,\r\n        ParameterJson: []\r\n      },\r\n      queryInfo2: {\r\n        BeginDate: '',\r\n        EndDate: '',\r\n        PageInfo: {\r\n          ParameterJson: [],\r\n          Page: 1,\r\n          PageSize: 2\r\n        }\r\n      },\r\n      tbConfig: {\r\n        Pager_Align: 'center',\r\n        Op_Width: 280\r\n      },\r\n      columns: [],\r\n      tbData: [],\r\n      total: 0,\r\n      tbLoading: false,\r\n      selectRow: {},\r\n      totalData: {\r\n        Allsteelamount: 0,\r\n        Allsteelweight: 0\r\n      },\r\n      ProfessionalType: null,\r\n      title: '',\r\n      Is_Integration: false // 是否一体化\r\n    }\r\n  },\r\n  computed: {\r\n    selectEmpty() {\r\n      return Object.keys(this.selectRow).length === 0\r\n    },\r\n    comList() {\r\n      if (!this.Is_Integration) {\r\n        return this.columns.filter((item) => {\r\n          return (\r\n            item.Code !== 'SumAcceptCount' && item.Code !== 'SumPendingCount'\r\n          )\r\n        })\r\n      } else {\r\n        return this.columns\r\n      }\r\n    },\r\n    ...mapGetters('factoryInfo', ['Component_Shipping_Approval', 'autoGenerate', 'Shipping_Approval_LowerLimit', 'Shipping_Weigh_Enabled'])\r\n  },\r\n  async activated() {\r\n    console.log('activated')\r\n    if (this.$route.query.refresh) {\r\n      this.fetchData()\r\n    }\r\n    // this.fetchData()\r\n  },\r\n  async created() {\r\n    this.$store.dispatch('factoryInfo/getWorkshop')\r\n\r\n    this.Is_Integration = await this.$store.dispatch('user/getPreferenceSetting', 'Is_Integration')\r\n    this.getFactoryTypeOption()\r\n    this.getProjectPageList()\r\n  },\r\n  mounted() {\r\n    console.log('mounted')\r\n  },\r\n  methods: {\r\n    handleCancelFlow(instanceId) {\r\n      this.$confirm('是否撤回?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        CancelFlow({\r\n          instanceId\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '操作成功',\r\n              type: 'success'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    submitForReview(row) {\r\n      const Id = row.Id\r\n      if (!row.VehicleNo) {\r\n        this.$message({\r\n          message: '发货单车辆信息未完善，请完善后提交',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n\r\n      this.$confirm('是否提交审核?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        SubmitApproval({\r\n          Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.fetchData()\r\n            this.$message({\r\n              message: '操作成功',\r\n              type: 'success'\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    handleMonitor(rowId) {\r\n      this.$refs['monitor'].opendialog(rowId, false)\r\n    },\r\n    getAuditStatus(row) {\r\n      return this.Component_Shipping_Approval && (this.Shipping_Approval_LowerLimit || 0) * 1000 < row.Project_Sending_Weight\r\n    },\r\n    async getFactoryTypeOption() {\r\n      await GetFactoryProfessionalByCode({\r\n        factoryId: localStorage.getItem('CurReferenceId')\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.ProfessionalType = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      await this.getTableConfig(\r\n        `pro_component_out_bill_list,${this.ProfessionalType[0].Code}`\r\n      )\r\n      this.fetchData()\r\n    },\r\n    fetchData() {\r\n      this.tbLoading = true\r\n      const form = { ...this.form }\r\n      delete form['dateRange']\r\n      this.form.dateRange = this.form.dateRange || []\r\n      form.BeginDate = parseTime(this.form.dateRange[0])\r\n        ? parseTime(this.form.dateRange[0])\r\n        : ''\r\n      form.EndDate = parseTime(this.form.dateRange[1])\r\n        ? parseTime(this.form.dateRange[1])\r\n        : ''\r\n      GetProjectSendingInfoPagelist(form).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.Data.map((v) => {\r\n            v.SendDate = v.SendDate\r\n              ? parseTime(new Date(v.SendDate), '{y}-{m}-{d}')\r\n              : v.SendDate\r\n            return v\r\n          })\r\n          // this.tbData = res.Data.Data;\r\n          this.total = res.Data.TotalCount\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.tbLoading = false\r\n      })\r\n      GetProjectSendingAllCount({ ...form }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          // console.log(res.Data,\"res.Data\");\r\n          this.totalData = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getPageList() {\r\n      this.fetchData()\r\n      console.log(this.form, 'this.form')\r\n    },\r\n    handSubmit(row) {\r\n      const Id = row.Id\r\n      if (!row.VehicleNo) {\r\n        this.$message({\r\n          message: '发货单车辆信息未完善，请完善后提交',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      this.$confirm('是否提交过磅?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        SubmitWeighingForPC({\r\n          Id\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.fetchData()\r\n            this.$message({\r\n              message: res.Data,\r\n              type: 'success'\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    datePickerwrapper() {\r\n      // console.log(form,\"form1111111111\");\r\n      // console.log(new Date(\"2022-09-28T10:12:35.583Z\"),\"new Date111\");\r\n      // console.log(parseTime(new Date(\"2022-10-11T14:03:54\")),\"new Date222\");\r\n      if (!this.form.dateRange) {\r\n        this.form.dateRange = ['', '']\r\n      }\r\n      this.fetchData()\r\n    },\r\n    resetForm(formName) {\r\n      this.$refs[formName].resetFields()\r\n      this.fetchData()\r\n    },\r\n    submitForm2(formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          const { ProjectId, Area_Id, InstallUnit_Id } = this.form2\r\n          const {\r\n            Name,\r\n            Id,\r\n            Code,\r\n            SPIC_UserName,\r\n            Address,\r\n            Receiver,\r\n            Receiver_Tel,\r\n            Sys_Project_Id,\r\n            Receive_UserName\r\n          } = this.projects.find((v) => v.Id === this.form2.ProjectId)\r\n          const data = {\r\n            ProjectId,\r\n            Area_Id,\r\n            InstallUnit_Id,\r\n            Id,\r\n            Name,\r\n            Code,\r\n            Address,\r\n            Receiver,\r\n            Receiver_Tel,\r\n            Sys_Project_Id,\r\n            Receive_UserName,\r\n            autoGenerate: this.autoGenerate,\r\n            ProfessionalType: this.ProfessionalType\r\n          }\r\n          this.$router.push({\r\n            name: 'PROShipSentAdd',\r\n            query: {\r\n              pg_redirect: 'PROShipSent',\r\n              p: encodeURIComponent(JSON.stringify(data))\r\n            }\r\n          })\r\n          this.dialogVisible = false\r\n          this.$refs.form2.resetFields()\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    resetForm2(formName) {\r\n      this.dialogVisible = false\r\n      this.$refs[formName].resetFields()\r\n    },\r\n    handleClose() {\r\n      this.$refs.form2.resetFields()\r\n      this.dialogVisible = false\r\n    },\r\n    handleAdd() {\r\n      this.dialogVisible = true\r\n      // this.$router.push({ name: 'PROShipSentAdd', query: { pg_redirect: 'PROShipSent' }})\r\n    },\r\n    getProjectPageList() {\r\n      GetProjectPageList({ PageSize: -1 }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.projects = res.Data.Data\r\n        }\r\n      })\r\n    },\r\n    projectIdChange(e) {\r\n      console.log(e, 'e')\r\n      // if (e) {\r\n      //   this.getAreaList();\r\n      // }\r\n    },\r\n    projectIdClear(e) {\r\n      this.$refs.form2.resetFields()\r\n    },\r\n    // 获取区域\r\n    getAreaList() {\r\n      GeAreaTrees({\r\n        projectId: this.form2.ProjectId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.treeParamsArea.data = res.Data\r\n          this.$nextTick((_) => {\r\n            this.$refs.treeSelectArea.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    filterFun(val, ref) {\r\n      this.$refs[ref].filterFun(val)\r\n    },\r\n    areaChange(e) {\r\n      console.log(e, 'e')\r\n      this.getInstall()\r\n    },\r\n    // 清空区域\r\n    areaClear() {\r\n      this.form2.Area_Id = ''\r\n      this.form.InstallUnit_Id = ''\r\n    },\r\n    // 获取批次\r\n    getInstall() {\r\n      GetInstallUnitPageList({\r\n        Area_Id: this.form2.Area_Id,\r\n        Page: 1,\r\n        PageSize: -1\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          if (res.IsSucceed) {\r\n            this.SetupPositionData = res.Data.Data\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleEdit(id, isSub) {\r\n      this.$router.push({\r\n        name: 'PROShipSentEdit',\r\n        query: { pg_redirect: 'PROShipSent', id, isSub: isSub ? '1' : '0',\r\n          p: encodeURIComponent(JSON.stringify({ autoGenerate: this.autoGenerate }))\r\n        }\r\n      })\r\n    },\r\n    // 撤回至草稿\r\n    handleWithdraw(id) {\r\n      this.$confirm('撤回至草稿, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          WithdrawDraft({\r\n            id: id\r\n          }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                message: '撤销成功',\r\n                type: 'success'\r\n              })\r\n              this.fetchData()\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => { })\r\n    },\r\n    handleSub(row) {\r\n      const id = row.Id\r\n      if (!row.VehicleNo) {\r\n        this.$message({\r\n          message: '发货单车辆信息未完善，请完善后提交',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      this.$confirm('提交该发货单, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          this.pageLoading = true\r\n          SubmitProjectSending({\r\n            Id: id\r\n          }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                message: '发货成功',\r\n                type: 'success'\r\n              })\r\n              this.fetchData()\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n            this.pageLoading = false\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.pageLoading = false\r\n        })\r\n    },\r\n    handleDel(id) {\r\n      this.$confirm('是否删除该发货单?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        DeleteProjectSendingInfo({\r\n          Id: id\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '删除成功',\r\n              type: 'success'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    },\r\n    handleInfo(id) {\r\n      this.$router.push({\r\n        name: 'PROShipSentDetail',\r\n        query: { pg_redirect: 'PROShipSent', id }\r\n      })\r\n    },\r\n    handleChange(id) {\r\n      this.$router.push({\r\n        name: 'PROShipSentChangeRecord',\r\n        query: { pg_redirect: 'PROShipSent', id }\r\n      })\r\n    },\r\n    // printMe() {},\r\n    handleExport() {\r\n      this.title = '导出'\r\n      this.$nextTick(_ => {\r\n        this.$refs.radioDialog.handleOpen(this.selectList)\r\n      })\r\n    },\r\n    handleExportExcel() {\r\n      this.title = '导出'\r\n      this.$refs.dialogExcel.handleOpen(this.selectList)\r\n    },\r\n    handlePrint() {\r\n      this.title = '打印'\r\n      this.$refs.radioDialog.handleOpen(this.selectList)\r\n    },\r\n    handlePrintNoWeight() {\r\n      // console.log(this.selectRow.Code, \"this.selectRow.Code\");\r\n      // console.log(this.selectRow.Id, \"this.selectRow.Id\");\r\n      TransformsWithoutWeight({\r\n        sendId: this.selectRow.Id\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          // const templateUrl = combineURL(this.$baseUrl, res.Data);\r\n          // window.open(templateUrl, \"_blank\");\r\n          const url = new URL(res.Data, baseUrl())\r\n          window.open(url.href, '_blank')\r\n          this.$message({\r\n            type: 'success',\r\n            message: '打印成功!'\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      // this.$refs.PrintDialog.open(this.selectRow.Code)\r\n    },\r\n\r\n    // 导出\r\n    handleLeadingOut() {\r\n      this.btnLoading = true\r\n      const form = { ...this.form }\r\n      delete form['dateRange']\r\n      delete form['PageInfo']\r\n      this.form.dateRange = this.form.dateRange || []\r\n      form.BeginDate = parseTime(this.form.dateRange[0])\r\n        ? parseTime(this.form.dateRange[0])\r\n        : ''\r\n      form.EndDate = parseTime(this.form.dateRange[1])\r\n        ? parseTime(this.form.dateRange[1])\r\n        : ''\r\n      ExportInvoiceList({ ...form })\r\n        .then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message.success('导出成功')\r\n            const url = new URL(res.Data, baseUrl())\r\n            window.open(url.href)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n        .finally(() => {\r\n          // 结束loading\r\n          this.btnLoading = false\r\n        })\r\n    },\r\n    handleSelectionChange(list) {\r\n      this.selectList = list\r\n    },\r\n    selectChange({ selection, row }) {\r\n      this.$refs.dyTable.$refs.dtable.clearSelection()\r\n      if (selection.length != 0) {\r\n        this.selectRow = row\r\n      } else {\r\n        this.selectRow = {}\r\n      }\r\n      if (selection.length > 1) {\r\n        selection.shift()\r\n      }\r\n      // console.log(selection, \"selection2\");\r\n      this.$refs.dyTable.$refs.dtable.toggleRowSelection(\r\n        row,\r\n        !!selection.length\r\n      )\r\n    },\r\n    handleSelectAll() {\r\n      // this.$refs.dyTable.$refs.dtable.clearSelection()\r\n    },\r\n    handelView(Id) {\r\n      this.IsVisabel = true\r\n      this.$nextTick((_) => {\r\n        this.$refs.checkDialog.handelOpen(Id)\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.total-wrapper {\r\n  float: right;\r\n  color: #298dff;\r\n  background-color: #f5faff;\r\n  font-size: 14px;\r\n  padding: 6px 10px 6px 10px;\r\n  margin-right: 10px;\r\n}\r\n.date-picker-wrapper {\r\n  float: right;\r\n  width: 20%;\r\n}\r\n::v-deep .form-search {\r\n  width: 100%;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  .el-form-item {\r\n    width: 24%;\r\n    display: flex;\r\n  }\r\n  .el-form-item__label {\r\n    width: 100px;\r\n  }\r\n  .el-form-item__content {\r\n    min-width: 10px;\r\n    flex: 1;\r\n  }\r\n  .el-select {\r\n    width: 100%;\r\n  }\r\n}\r\n.license-box {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100px;\r\n  height: 28px;\r\n  background: #818fb7;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n\r\n  .inner-box {\r\n    width: 98px;\r\n    height: 24px;\r\n    border: 1px solid #ffffff;\r\n    border-radius: 3px;\r\n    color: #ffffff;\r\n    font-size: 14px;\r\n  }\r\n}\r\n::v-deep .custom-pagination .checked-count {\r\n  top: 20px;\r\n}\r\n::v-deep .pagination {\r\n  justify-content: right !important;\r\n}\r\n</style>\r\n"]}]}
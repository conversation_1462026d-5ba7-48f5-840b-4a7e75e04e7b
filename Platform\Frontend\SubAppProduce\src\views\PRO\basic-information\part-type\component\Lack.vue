<template>
  <el-form ref="form" :model="form" :rules="rules" label-width="190px" style="width: 100%">
    <el-form-item label="分类缺省时，默认选择为：" prop="Id">
      <el-select v-model="form.Id" placeholder="请选择" clearable="">
        <el-option
          v-for="item in options"
          :key="item.Id"
          :label="item.Name"
          :value="item.Id"
        />
      </el-select>
    </el-form-item>
    <span class="tip">当导入的零件清单中，零件分类缺省时，系统自动补全上述选择的分类。</span>
    <el-form-item class="cs-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" :loading="btnLoading" @click="handleSubmit('form')">确 定</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { GetPartTypeList, SettingDefault } from '@/api/PRO/partType'

export default {
  data() {
    return {
      btnLoading: false,
      form: {
        Id: ''
      },
      options: [],
      rules: {
        Id: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      }

    }
  },
  mounted() {
    this.getOption()
  },
  methods: {
    getOption() {
      GetPartTypeList({ Part_Grade: 0 }).then(res => {
        if (res.IsSucceed) {
          this.options = res.Data
          this.form.Id = this.options.find(ele => ele.Is_Default).Id || ''
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    handleClose() {
      this.$emit('close')
    },
    handleSubmit() {
      this.$refs['form'].validate((valid) => {
        if (!valid) return
        SettingDefault({
          id: this.form.Id
        }).then(res => {
          if (res.IsSucceed) {
            this.$message({
              message: '操作成功',
              type: 'success'
            })
            this.$emit('close')
            this.$emit('refresh')
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      })
      this.btnLoading = false
    }
  }
}
</script>

<style scoped>
.cs-footer{
  text-align: center;
  margin-top: 40px;
}
.tip{
  color: rgba(34, 40, 52, 0.4);
  font-size: 12px;
}
</style>

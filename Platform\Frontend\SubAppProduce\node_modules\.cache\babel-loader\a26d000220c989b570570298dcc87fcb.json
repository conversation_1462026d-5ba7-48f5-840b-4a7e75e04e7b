{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckItem.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckItem.vue", "mtime": 1757999301030}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetCheckItemList", "DeleteCheckItem", "props", "checkType", "type", "Object", "default", "sysProjectId", "String", "data", "tbLoading", "tbData", "watch", "handler", "newName", "old<PERSON>ame", "getCheckItemList", "deep", "mounted", "methods", "_this", "check_object_id", "Id", "Bom_Level", "Code", "then", "res", "IsSucceed", "Data", "$message", "message", "Message", "removeEvent", "row", "_this2", "$confirm", "confirmButtonText", "cancelButtonText", "id", "catch", "editEvent", "console", "log", "$emit"], "sources": ["src/views/PRO/project-config/project-quality/components/CheckItem.vue"], "sourcesContent": ["<template>\n  <div style=\"height: calc(100vh - 300px)\">\n    <vxe-table\n      v-loading=\"tbLoading\"\n      :empty-render=\"{name: 'NotData'}\"\n      show-header-overflow\n      element-loading-spinner=\"el-icon-loading\"\n      element-loading-text=\"拼命加载中\"\n      empty-text=\"暂无数据\"\n      height=\"100%\"\n      align=\"left\"\n      stripe\n      :data=\"tbData\"\n      resizable\n      :auto-resize=\"true\"\n      class=\"cs-vxe-table\"\n      :tooltip-config=\"{ enterable: true }\"\n    >\n      <vxe-column\n        show-overflow=\"tooltip\"\n        sortable\n        field=\"Check_Content\"\n        title=\"检查项内容\"\n        width=\"calc(100vh-200px)/2\"\n      />\n      <vxe-column\n        show-overflow=\"tooltip\"\n        sortable\n        field=\"Eligibility_Criteria\"\n        title=\"合格标准\"\n        width=\"calc(100vh-200px)/2\"\n      />\n      <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" align=\"center\" show-overflow>\n        <template #default=\"{ row }\">\n          <el-button type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\n          <!-- <el-divider direction=\"vertical\" />\n          <el-button type=\"text\" @click=\"removeEvent(row)\">删除</el-button> -->\n        </template>\n      </vxe-column>\n    </vxe-table>\n  </div>\n</template>\n\n<script>\nimport { GetCheckItemList } from '@/api/PRO/factorycheck'\nimport { DeleteCheckItem } from '@/api/PRO/factorycheck'\n\nexport default {\n  props: {\n    checkType: {\n      type: Object,\n      default: () => ({})\n    },\n    sysProjectId: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      tbLoading: false,\n      tbData: []\n    }\n  },\n  watch: {\n    checkType: {\n      handler(newName, oldName) {\n        this.checkType = newName\n        this.getCheckItemList()\n      },\n      deep: true\n    }\n  },\n  mounted() {\n    // this.getCheckItemList()\n  },\n  methods: {\n    getCheckItemList() {\n      this.tbLoading = true\n      GetCheckItemList({ check_object_id: this.checkType.Id, Bom_Level: this.checkType.Code, sysProjectId: this.sysProjectId }).then((res) => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data\n          this.tbLoading = false\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n          this.tbLoading = false\n        }\n      })\n    },\n    removeEvent(row) {\n      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DeleteCheckItem({ id: row.Id }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getCheckItemList()\n            } else {\n              this.$message({\n                type: 'error',\n                message: res.Message\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    },\n    editEvent(row) {\n      // 获取每行内容\n      console.log('row', row)\n      this.$emit('ItemEdit', row)\n    }\n  }\n}\n</script>\n\n<style scoped>\n\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,SAAAA,gBAAA;AACA,SAAAC,eAAA;AAEA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAC,YAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,MAAA;IACA;EACA;EACAC,KAAA;IACAT,SAAA;MACAU,OAAA,WAAAA,QAAAC,OAAA,EAAAC,OAAA;QACA,KAAAZ,SAAA,GAAAW,OAAA;QACA,KAAAE,gBAAA;MACA;MACAC,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;EAAA,CACA;EACAC,OAAA;IACAH,gBAAA,WAAAA,iBAAA;MAAA,IAAAI,KAAA;MACA,KAAAV,SAAA;MACAV,gBAAA;QAAAqB,eAAA,OAAAlB,SAAA,CAAAmB,EAAA;QAAAC,SAAA,OAAApB,SAAA,CAAAqB,IAAA;QAAAjB,YAAA,OAAAA;MAAA,GAAAkB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAP,KAAA,CAAAT,MAAA,GAAAe,GAAA,CAAAE,IAAA;UACAR,KAAA,CAAAV,SAAA;QACA;UACAU,KAAA,CAAAS,QAAA;YACAzB,IAAA;YACA0B,OAAA,EAAAJ,GAAA,CAAAK;UACA;UACAX,KAAA,CAAAV,SAAA;QACA;MACA;IACA;IACAsB,WAAA,WAAAA,YAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAjC,IAAA;MACA,GACAqB,IAAA;QACAxB,eAAA;UAAAqC,EAAA,EAAAL,GAAA,CAAAX;QAAA,GAAAG,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAO,MAAA,CAAAL,QAAA;cACAzB,IAAA;cACA0B,OAAA;YACA;YACAI,MAAA,CAAAlB,gBAAA;UACA;YACAkB,MAAA,CAAAL,QAAA;cACAzB,IAAA;cACA0B,OAAA,EAAAJ,GAAA,CAAAK;YACA;UACA;QACA;MACA,GACAQ,KAAA;QACAL,MAAA,CAAAL,QAAA;UACAzB,IAAA;UACA0B,OAAA;QACA;MACA;IACA;IACAU,SAAA,WAAAA,UAAAP,GAAA;MACA;MACAQ,OAAA,CAAAC,GAAA,QAAAT,GAAA;MACA,KAAAU,KAAA,aAAAV,GAAA;IACA;EACA;AACA", "ignoreList": []}]}
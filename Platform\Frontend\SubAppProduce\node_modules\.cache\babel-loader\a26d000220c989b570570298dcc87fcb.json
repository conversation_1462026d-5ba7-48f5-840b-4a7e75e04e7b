{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckItem.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckItem.vue", "mtime": 1758086014839}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetCheckItemList", "DeleteCheckItem", "props", "checkType", "type", "Object", "default", "sysProjectId", "String", "data", "tbLoading", "tbData", "watch", "handler", "newName", "old<PERSON>ame", "getCheckItemList", "deep", "newVal", "Id", "immediate", "mounted", "methods", "_this", "check_object_id", "Bom_Level", "Code", "then", "res", "IsSucceed", "Data", "$message", "message", "Message", "removeEvent", "row", "_this2", "$confirm", "confirmButtonText", "cancelButtonText", "id", "catch", "editEvent", "console", "log", "$emit"], "sources": ["src/views/PRO/project-config/project-quality/components/CheckItem.vue"], "sourcesContent": ["<template>\n  <div style=\"height: calc(100vh - 300px)\">\n    <vxe-table\n      v-loading=\"tbLoading\"\n      :empty-render=\"{name: 'NotData'}\"\n      show-header-overflow\n      element-loading-spinner=\"el-icon-loading\"\n      element-loading-text=\"拼命加载中\"\n      empty-text=\"暂无数据\"\n      height=\"100%\"\n      align=\"left\"\n      stripe\n      :data=\"tbData\"\n      resizable\n      :auto-resize=\"true\"\n      class=\"cs-vxe-table\"\n      :tooltip-config=\"{ enterable: true }\"\n    >\n      <vxe-column\n        show-overflow=\"tooltip\"\n        sortable\n        field=\"Check_Content\"\n        title=\"检查项内容\"\n        width=\"calc(100vh-200px)/2\"\n      />\n      <vxe-column\n        show-overflow=\"tooltip\"\n        sortable\n        field=\"Eligibility_Criteria\"\n        title=\"合格标准\"\n        width=\"calc(100vh-200px)/2\"\n      />\n      <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" align=\"center\" show-overflow>\n        <template #default=\"{ row }\">\n          <el-button type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\n          <!-- <el-divider direction=\"vertical\" />\n          <el-button type=\"text\" @click=\"removeEvent(row)\">删除</el-button> -->\n        </template>\n      </vxe-column>\n    </vxe-table>\n  </div>\n</template>\n\n<script>\nimport { GetCheckItemList } from '@/api/PRO/factorycheck'\nimport { DeleteCheckItem } from '@/api/PRO/factorycheck'\n\nexport default {\n  props: {\n    checkType: {\n      type: Object,\n      default: () => ({})\n    },\n    sysProjectId: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      tbLoading: false,\n      tbData: []\n    }\n  },\n  watch: {\n    checkType: {\n      handler(newName, oldName) {\n        this.checkType = newName\n        if (this.sysProjectId) {\n          this.getCheckItemList()\n        }\n      },\n      deep: true\n    },\n    sysProjectId: {\n      handler(newVal) {\n        if (newVal && this.checkType && this.checkType.Id) {\n          this.getCheckItemList()\n        }\n      },\n      immediate: true\n    }\n  },\n  mounted() {\n    // this.getCheckItemList()\n  },\n  methods: {\n    getCheckItemList() {\n      this.tbLoading = true\n      GetCheckItemList({ check_object_id: this.checkType.Id, Bom_Level: this.checkType.Code, sysProjectId: this.sysProjectId }).then((res) => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data\n          this.tbLoading = false\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n          this.tbLoading = false\n        }\n      })\n    },\n    removeEvent(row) {\n      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DeleteCheckItem({ id: row.Id }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getCheckItemList()\n            } else {\n              this.$message({\n                type: 'error',\n                message: res.Message\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    },\n    editEvent(row) {\n      // 获取每行内容\n      console.log('row', row)\n      this.$emit('ItemEdit', row)\n    }\n  }\n}\n</script>\n\n<style scoped>\n\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,SAAAA,gBAAA;AACA,SAAAC,eAAA;AAEA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAC,YAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,MAAA;IACA;EACA;EACAC,KAAA;IACAT,SAAA;MACAU,OAAA,WAAAA,QAAAC,OAAA,EAAAC,OAAA;QACA,KAAAZ,SAAA,GAAAW,OAAA;QACA,SAAAP,YAAA;UACA,KAAAS,gBAAA;QACA;MACA;MACAC,IAAA;IACA;IACAV,YAAA;MACAM,OAAA,WAAAA,QAAAK,MAAA;QACA,IAAAA,MAAA,SAAAf,SAAA,SAAAA,SAAA,CAAAgB,EAAA;UACA,KAAAH,gBAAA;QACA;MACA;MACAI,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;EAAA,CACA;EACAC,OAAA;IACAN,gBAAA,WAAAA,iBAAA;MAAA,IAAAO,KAAA;MACA,KAAAb,SAAA;MACAV,gBAAA;QAAAwB,eAAA,OAAArB,SAAA,CAAAgB,EAAA;QAAAM,SAAA,OAAAtB,SAAA,CAAAuB,IAAA;QAAAnB,YAAA,OAAAA;MAAA,GAAAoB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAN,KAAA,CAAAZ,MAAA,GAAAiB,GAAA,CAAAE,IAAA;UACAP,KAAA,CAAAb,SAAA;QACA;UACAa,KAAA,CAAAQ,QAAA;YACA3B,IAAA;YACA4B,OAAA,EAAAJ,GAAA,CAAAK;UACA;UACAV,KAAA,CAAAb,SAAA;QACA;MACA;IACA;IACAwB,WAAA,WAAAA,YAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAnC,IAAA;MACA,GACAuB,IAAA;QACA1B,eAAA;UAAAuC,EAAA,EAAAL,GAAA,CAAAhB;QAAA,GAAAQ,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAO,MAAA,CAAAL,QAAA;cACA3B,IAAA;cACA4B,OAAA;YACA;YACAI,MAAA,CAAApB,gBAAA;UACA;YACAoB,MAAA,CAAAL,QAAA;cACA3B,IAAA;cACA4B,OAAA,EAAAJ,GAAA,CAAAK;YACA;UACA;QACA;MACA,GACAQ,KAAA;QACAL,MAAA,CAAAL,QAAA;UACA3B,IAAA;UACA4B,OAAA;QACA;MACA;IACA;IACAU,SAAA,WAAAA,UAAAP,GAAA;MACA;MACAQ,OAAA,CAAAC,GAAA,QAAAT,GAAA;MACA,KAAAU,KAAA,aAAAV,GAAA;IACA;EACA;AACA", "ignoreList": []}]}
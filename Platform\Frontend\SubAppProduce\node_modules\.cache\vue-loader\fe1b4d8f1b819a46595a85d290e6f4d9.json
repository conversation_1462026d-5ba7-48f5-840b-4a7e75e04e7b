{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\quality_Inspection\\start-inspect\\components\\add\\addDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\quality_Inspection\\start-inspect\\components\\add\\addDialog.vue", "mtime": 1757468113520}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["addDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "addDialog.vue", "sourceRoot": "src/views/PRO/quality_Inspection/start-inspect/components/add", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"100px\">\r\n      <el-form-item label=\"质检对象\" prop=\"Check_Object_Type\">\r\n        <el-select\r\n          v-model=\"form.Check_Object_Type\"\r\n          filterable\r\n          clearable\r\n          placeholder=\"请选择\"\r\n          :disabled=\"type == '查看'\"\r\n          @change=\"changeObject\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in CheckObjectData\"\r\n            :key=\"item.Id\"\r\n            :label=\"item.Display_Name\"\r\n            :value=\"item.Id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"质检节点\" prop=\"Check_Node_Id\">\r\n        <el-select\r\n          v-model=\"form.Check_Node_Id\"\r\n          filterable\r\n          clearable\r\n          placeholder=\"请选择\"\r\n          :disabled=\"!form.Check_Object_Type || type == '查看'\"\r\n          @change=\"changeCheckNode\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in CheckNodeList\"\r\n            :key=\"item.Id\"\r\n            :label=\"item.Display_Name\"\r\n            :value=\"item.Id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"名称\" prop=\"SteelName\">\r\n        <el-input v-model=\"form.SteelName\" type=\"text\" disabled>\r\n          <el-button\r\n            slot=\"append\"\r\n            icon=\"el-icon-search\"\r\n            :disabled=\"!form.Check_Object_Type || type == '查看'\"\r\n            @click=\"chooseComponent\"\r\n          />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"质检类型\" prop=\"Check_Type\">\r\n        <el-select\r\n          v-model=\"form.Check_Type\"\r\n          placeholder=\"请选择\"\r\n          :disabled=\"\r\n            !form.Check_Node_Id || CheckTypeList.length == 1 || type == '查看'\r\n          \"\r\n          @change=\"$forceUpdate()\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in CheckTypeList\"\r\n            :key=\"item.Id\"\r\n            :label=\"item.Name\"\r\n            :value=\"item.Id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item v-if=\"type != '查看'\">\r\n        <el-button @click=\"$emit('close')\">取 消</el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          :loading=\"SaveLoading\"\r\n          @click=\"AddSave('form', false)\"\r\n        >保 存</el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          :loading=\"SubmitLoading\"\r\n          @click=\"AddSave('form', true)\"\r\n        >提 交</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'\r\nimport elDragDialog from '@/directive/el-drag-dialog'\r\nimport {\r\n  GetDictionaryDetailListByCode,\r\n  GetNodeList\r\n} from '@/api/PRO/factorycheck'\r\nimport { AddLanch, Add } from '@/api/PRO/qualityInspect/start-Inspect'\r\nexport default {\r\n  directives: { elDragDialog },\r\n  components: {\r\n    DynamicDataTable\r\n  },\r\n  data() {\r\n    return {\r\n      SaveLoading: false,\r\n      SubmitLoading: false,\r\n      form: {\r\n        Check_Object_Type: '',\r\n        SteelName: '',\r\n        Check_Node_Id: '',\r\n        Check_Type: ''\r\n      },\r\n      chooseTitle: '', // 质检对象名称\r\n      currentComponent: '',\r\n      title: '',\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      width: '60%',\r\n      type: '', // 区分是否是新增（查看）\r\n      addComTitle: '添加构件',\r\n      CheckTypeList: [\r\n        {\r\n          Name: '质量',\r\n          Id: '1'\r\n        },\r\n        {\r\n          Name: '探伤',\r\n          Id: '2'\r\n        }\r\n      ], // 质检类型\r\n      CheckNodeList: [], // 质检节点\r\n      CheckObjectData: [], // 质检对象\r\n      rules: {\r\n        Check_Object_Type: [\r\n          { required: true, message: '请填写完整表单', trigger: 'change' }\r\n        ],\r\n        Check_Node_Id: [\r\n          { required: true, message: '请填写完整表单', trigger: 'change' }\r\n        ],\r\n        Check_Type: [\r\n          { required: true, message: '请填写完整表单', trigger: 'change' }\r\n        ],\r\n        SteelName: [\r\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getCheckType()\r\n  },\r\n  methods: {\r\n    async init(type, row) {\r\n      this.type = type || ''\r\n      if (type == '查看') {\r\n        console.log('row', row)\r\n\r\n        // this.form.Check_Object_Type = row.Check_Object_Type\r\n        await this.getCheckType()\r\n        console.log('this.CheckObjectData', this.CheckObjectData)\r\n        this.form.Check_Object_Type = this.CheckObjectData.find((v) => {\r\n          return v.Display_Name === row.Check_Object_Type\r\n        })?.Id\r\n        console.log('this.form.Check_Object_Type', this.form.Check_Object_Type)\r\n        this.changeObject(this.form.Check_Object_Type)\r\n        this.form.Check_Node_Id = row.Check_Node_Id\r\n        this.form.SteelName = row.SteelName\r\n        this.form.Check_Type = row.Check_Type\r\n      }\r\n    },\r\n    // 获取带过来的构件名称\r\n    handelName(val) {\r\n      console.log(val)\r\n      this.form.SteelName = val.SteelName ? val.SteelName : val.Code\r\n      this.form.Check_Object_Id = val.Id\r\n    },\r\n    async getCheckType() {\r\n      await GetDictionaryDetailListByCode({ dictionaryCode: 'Quality_Code' })\r\n        .then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.CheckObjectData = res.Data\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: 'res.Message'\r\n            })\r\n          }\r\n        })\r\n        .catch(() => {\r\n          console.log('sdfd')\r\n        })\r\n    },\r\n    changeObject(val) {\r\n      console.log('val', val)\r\n      this.form.Check_Node_Id = ''\r\n      this.form.Check_Type = ''\r\n      this.form.SteelName = ''\r\n      const checkObj = this.CheckObjectData.find((v) => {\r\n        return v.Id == val\r\n      })?.Display_Name\r\n      this.chooseTitle = checkObj\r\n      switch (checkObj) {\r\n        case '构件':\r\n          this.check_object_id = '0'\r\n          break\r\n        case '零件':\r\n          this.check_object_id = '1'\r\n          break\r\n        case '物料':\r\n          this.check_object_id = '2'\r\n          break\r\n        case '部件':\r\n          this.check_object_id = '3'\r\n          break\r\n        default:\r\n          this.check_object_id = '0'\r\n      }\r\n      console.log('this.check_object_id', this.check_object_id)\r\n      GetNodeList({ check_object_id: val, Check_Style: '1' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.CheckNodeList = res.Data\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: 'res.Message'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    changeCheckNode(val) {\r\n      this.CheckTypeList = []\r\n      this.form.Check_Type = ''\r\n      const checkTypeId = this.CheckNodeList.find((v) => {\r\n        return v.Id === val\r\n      })?.Check_Type\r\n      console.log(checkTypeId)\r\n      if (checkTypeId == '-1') {\r\n        this.CheckTypeList = [\r\n          {\r\n            Name: '质量',\r\n            Id: '1'\r\n          },\r\n          {\r\n            Name: '探伤',\r\n            Id: '2'\r\n          }\r\n        ]\r\n        this.form.Check_Type = '1'\r\n      } else if (checkTypeId == '1') {\r\n        this.CheckTypeList = [\r\n          {\r\n            Name: '质量',\r\n            Id: '1'\r\n          }\r\n        ]\r\n        this.form.Check_Type = '1'\r\n      } else if (checkTypeId == '2') {\r\n        this.CheckTypeList = [\r\n          {\r\n            Name: '探伤',\r\n            Id: '2'\r\n          }\r\n        ]\r\n        this.form.Check_Type = '2'\r\n      }\r\n      console.log(this.form.Check_Type)\r\n    },\r\n    chooseComponent() {\r\n      this.$store.dispatch('qualityCheck/changeRadio', true)\r\n      this.$emit('openDialog', this.check_object_id, this.chooseTitle)\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    AddSave(form, val) {\r\n      if (val) {\r\n        this.SubmitLoading = true\r\n      } else {\r\n        this.SaveLoading = true\r\n      }\r\n      this.$refs[form].validate((valid) => {\r\n        if (valid) {\r\n          Add({\r\n            SheetModel: {\r\n              ...this.form,\r\n              Check_Object_Type: this.check_object_id,\r\n              Check_Object_Type_Id: this.form.Check_Object_Type,\r\n              Check_Style: 1\r\n            },\r\n            sumbimt: val\r\n          }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                type: 'success',\r\n                message: '保存成功'\r\n              })\r\n              this.SubmitLoading = false\r\n              this.SaveLoading = false\r\n              this.$emit('close')\r\n              this.$emit('refresh')\r\n            } else {\r\n              this.SubmitLoading = false\r\n              this.SaveLoading = false\r\n              this.$message({\r\n                type: 'warning',\r\n                message: res.Message\r\n              })\r\n            }\r\n          })\r\n        } else {\r\n          this.SubmitLoading = false\r\n          this.SaveLoading = false\r\n          return false\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/styles/mixin.scss\";\r\n@import \"~@/styles/variables.scss\";\r\n\r\n.add-dialog {\r\n  z-index: 9999 !important;\r\n\r\n  ::v-deep {\r\n    .el-dialog__header {\r\n      background-color: #298dff;\r\n\r\n      .el-dialog__title,\r\n      .el-dialog__close {\r\n        color: #ffffff;\r\n      }\r\n    }\r\n\r\n    .el-dialog__body {\r\n      max-height: 700px;\r\n      overflow: auto;\r\n      @include scrollBar;\r\n\r\n      &::-webkit-scrollbar {\r\n        width: 8px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .el-form-item {\r\n  .el-form-item__content {\r\n    & > .el-input {\r\n      width: 280px;\r\n    }\r\n\r\n    & > .el-select {\r\n      width: 280px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
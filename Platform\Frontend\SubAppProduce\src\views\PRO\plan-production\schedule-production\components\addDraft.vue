<template>
  <div class="contentBox">
    <el-form ref="form" :model="form" label-width="90px">
      <el-row>
        <template v-if="isCom">
          <el-col :span="10">
            <el-form-item label="构件编号" prop="Comp_Codes">
              <el-input
                v-model="form.Comp_Code"
                clearable
                style="width: 45%"
                placeholder="请输入(空格区分/多个搜索)"
                type="text"
              />
              <el-input
                v-model="form.Comp_CodeBlur"
                clearable
                style="width: 45%;margin-left: 16px"
                placeholder="模糊查找(请输入关键字)"
                type="text"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="构件类型" prop="Type">
              <el-tree-select
                ref="treeSelectObjectType"
                v-model="form.Type"
                style="width: 100%"
                class="cs-tree-x"
                :select-params="treeSelectParams"
                :tree-params="ObjectTypeList"
                value-key="Id"
              />
              <!--              <el-select v-model="form.Type" placeholder="请选择" clearable @clear="filterData">
                <el-option label="全部" value="" />
                <el-option
                  v-for="item in comTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>-->
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="规格" prop="Spec">
              <el-input v-model.trim="form.Spec" placeholder="请输入" clearable />
            </el-form-item>
          </el-col>

        </template>
        <template v-else>
          <el-col :span="7">
            <el-form-item label="所属构件" prop="Comp_Code">
              <el-input
                v-model="form.Comp_Code"
                style="width: 45%;"
                placeholder="请输入(空格区分/多个搜索)"
                clearable
              />
              <el-input
                v-model="form.Comp_CodeBlur"
                clearable
                style="width: 45%;margin-left: 16px"
                placeholder="模糊查找(请输入关键字)"
                type="text"
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="零件名称" prop="Part_Code">
              <el-input
                v-model="form.Part_Code"
                style="width: 45%;"
                placeholder="请输入(空格区分/多个搜索)"
                clearable
              />
              <el-input
                v-model="form.Part_CodeBlur"
                clearable
                style="width: 45%;margin-left: 16px"
                placeholder="模糊查找(请输入关键字)"
                type="text"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="规格" prop="Spec">
              <el-input
                v-model.trim="form.Spec"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="零件种类" prop="Type_Name">
              <el-select
                v-model="form.Type_Name"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in typeOption"
                  :key="item.Code"
                  :label="item.Name"
                  :value="item.Name"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </template>
        <el-col :span="2">
          <el-button style="margin-left: 10px" type="primary" @click="handleSearch()">查询</el-button>
        </el-col>
      </el-row>
    </el-form>
    <div class="tb-wrapper">
      <vxe-table
        ref="xTable1"
        :empty-render="{name: 'NotData'}"
        show-header-overflow
        empty-text="暂无数据"
        height="auto"
        show-overflow
        :checkbox-config="{checkField: 'checked'}"
        :loading="tbLoading"
        :row-config="{isCurrent: true, isHover: true }"
        class="cs-vxe-table"
        align="left"
        stripe
        :data="fTable"
        resizable
        :edit-config="{trigger: 'click', mode: 'cell', activeMethod: activeCellMethod}"
        :tooltip-config="{ enterable: true }"
        @checkbox-all="tbSelectChange"
        @checkbox-change="tbSelectChange"
      >
        <vxe-column fixed="left" type="checkbox" width="60" />
        <template v-for="item in columns">
          <vxe-column
            v-if="item.Code === 'customCountColumn'"
            :key="item.Code"
            :align="item.Align"
            :field="item.Code"
            :title="item.Display_Name"
            sortable
            :edit-render="{}"
            min-width="120"
          >
            <template #edit="{ row }">
              <vxe-input
                v-model.number="row.count"
                type="integer"
                :min="1"
                :max="row.maxCount"
              />
            </template>
            <template #default="{ row }">
              {{ row.count | displayValue }}
            </template>
          </vxe-column>
          <vxe-column
            v-else-if="item.Code === 'Is_Component'"
            :key="item.Code"
            :align="item.Align"
            :field="item.Code"
            :title="item.Display_Name"
            sortable
            :min-width="item.Width"
          >
            <template #default="{ row }">
              <el-tag :type="row.Is_Component ? 'danger' : 'success'">{{
                row.Is_Component ? "否" : "是"
              }}</el-tag>
            </template>
          </vxe-column>
          <vxe-column
            v-else
            :key="item.Code"
            :align="item.Align"
            :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
            show-overflow="tooltip"
            sortable
            :field="item.Code"
            :title="item.Display_Name"
            :min-width="item.Width"
          />
        </template>
      </vxe-table>
    </div>
    <div class="data-info">
      <el-tag
        size="medium"
        class="info-x"
      >已选 {{ totalSelection.length }} 条数据
      </el-tag>
      <vxe-pager
        border
        background
        :loading="tbLoading"
        :current-page.sync="pageInfo.page"
        :page-size.sync="pageInfo.pageSize"
        :page-sizes="pageInfo.pageSizes"
        :total="pageInfo.total"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
        size="small"
        @page-change="handlePageChange"
      />
    </div>
    <div class="button">
      <el-button @click="handleClose">取消</el-button>
      <el-button
        type="primary"
        :disabled="!totalSelection.length"
        :loading="saveLoading"
        @click="handleSave"
      >保存</el-button>
    </div>
  </div>
</template>

<script>
import { GetGridByCode } from '@/api/sys'
import { GetCanSchdulingComps } from '@/api/PRO/production-task'
import { GetPartList } from '@/api/PRO/production-part'
import { v4 as uuidv4 } from 'uuid'
import { FIX_COLUMN } from '@/views/PRO/plan-production/schedule-production/constant'
import { debounce, deepClone } from '@/utils'
import { tablePageSize } from '@/views/PRO/setting'
import { GetCompTypeTree } from '@/api/PRO/professionalType'
import { GetPartTypeList } from '@/api/PRO/partType'

export default {
  props: {
    scheduleId: {
      type: String,
      default: ''
    },
    pageType: {
      type: String,
      default: 'com'
    },
    showDialog: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      pageInfo: {
        page: 1,
        pageSize: 500,
        pageSizes: tablePageSize,
        total: 0
      },
      form: {
        Comp_Code: '',
        Comp_CodeBlur: '',
        Part_CodeBlur: '',
        Part_Code: '',
        Type_Name: '',
        Spec: '',
        Type: ''
      },
      isOwnerNull: true,
      tbLoading: false,
      saveLoading: false,
      columns: [],
      fTable: [],
      tbConfig: {},
      TotalCount: 0,
      Page: 0,
      multipleSelection: [],
      totalSelection: [],
      search: () => ({}),
      treeSelectParams: {
        placeholder: '请选择',
        clearable: true
      },
      ObjectTypeList: {
        // 构件类型
        'check-strictly': true,
        'default-expand-all': true,
        clickParent: true,
        data: [],
        props: {
          children: 'Children',
          label: 'Label',
          value: 'Data'
        }
      },
      typeOption: []
    }
  },
  computed: {
    isCom() {
      return this.pageType === 'com'
    }
  },
  watch: {
    showDialog(newValue) {
      newValue && (this.saveLoading = false)
    }
  },
  mounted() {
    this.getConfig()
    if (this.isCom) {
      this.getObjectTypeList()
    } else {
      this.getType()
    }
    this.search = debounce(this.fetchData, 800, true)
  },
  methods: {
    async getConfig() {
      let code = ''
      code = this.isCom
        ? 'PROComDraftEditTbConfig'
        : 'PROPartDraftEditTbConfig'
      await this.getTableConfig(code)
      this.fetchData()
    },
    filterData(page) {
      const f = []
      for (const formKey in this.form) {
        if (this.form[formKey] || this.form[formKey] === false) {
          f.push(formKey)
        }
      }
      if (!f.length) {
        this.setPage()
        !page && (this.pageInfo.page = 1)
        this.pageInfo.total = this.tbData.length
        return
      }
      const temTbData = this.tbData.filter(v => {
        v.checked = false
        if (this.form.Comp_Code.trim() && !this.form['Comp_Code'].split(' ').includes(v['Comp_Code'])) {
          return false
        }
        if (this.form.Comp_CodeBlur.trim() && !v.Comp_Code.includes(this.form.Comp_CodeBlur)) {
          return false
        }
        if (this.form.Type && v.Type !== this.form.Type) {
          return false
        }
        if (this.form.Part_CodeBlur.trim() && !v.Part_Code.includes(this.form.Part_CodeBlur)) {
          return false
        }
        if (this.form.Part_Code.trim() && !this.form['Part_Code'].split(' ').includes(v['Part_Code'])) {
          return false
        }
        if (this.form.Type_Name !== '' && v.Type_Name !== this.form.Type_Name) {
          return false
        }
        if (this.form.Spec.trim() !== '' && !v.Spec.includes(this.form.Spec)) {
          return false
        }
        return true
      })

      console.log('page', page)
      !page && (this.pageInfo.page = 1)
      this.pageInfo.total = temTbData.length
      this.setPage(temTbData)
    },
    handleSearch() {
      this.totalSelection = []
      this.clearSelect()
      if (this.tbData?.length) {
        this.tbData.forEach(item => item.checked = false)
        this.filterData()
      }
    },
    handleSelect(data) {
      this.multipleSelection = data
    },
    tbSelectChange(array) {
      console.log('array', array)
      this.totalSelection = this.tbData.filter(v => v.checked)
    },
    clearSelect() {
      this.$refs.xTable1.clearCheckboxRow()
      this.totalSelection = []
    },
    async fetchData() {
      this.tbLoading = true
      if (this.isCom) {
        await this.getComTbData()
      } else {
        await this.getPartTbData()
      }
      this.initTbData()
      this.filterData()
      this.tbLoading = false
    },
    setPageData() {
      if (this.tbData?.length) {
        this.pageInfo.page = 1
        this.tbData = this.tbData.filter(v => v.Can_Schduling_Count > 0)
        this.filterData()
      }
    },
    handleSave() {
      this.saveLoading = true
      setTimeout(() => {
        this.totalSelection.forEach((item) => {
          const intCount = parseInt(item.count)
          item.Schduled_Count += intCount
          item.Can_Schduling_Count -= intCount
          item.Can_Schduling_Weight = item.Can_Schduling_Count * item.Weight
          item.maxCount = item.Can_Schduling_Count
          item.chooseCount = intCount
          item.count = item.Can_Schduling_Count
          item.checked = false
        })
        const cp = deepClone(this.totalSelection)

        this.$emit('sendSelectList', cp)
        this.$emit('close')
        this.clearSelect()
        this.setPage()
      }, 0)
    },
    initTbData() {
      if (!this.tbData?.length) {
        this.tbData = []
        this.backendTb = []
        return
      }
      // 设置文本框选择的排产数量,设置自定义唯一码
      const objKey = {}
      this.tbData.forEach((item) => {
        this.$set(item, 'count', item.Can_Schduling_Count)
        this.$set(item, 'maxCount', item.Can_Schduling_Count)
        item.uuid = uuidv4()
        objKey[item.Type] = true
      })
      this.backendTb = deepClone(this.tbData)
    },
    async getComTbData() {
      const { install, areaId } = this.$route.query
      const { Comp_Codes, ...obj } = this.form
      let codes = []
      if (Object.prototype.toString.call(Comp_Codes) === '[object String]') {
        codes = Comp_Codes && Comp_Codes.split(' ').filter(v => !!v)
      }
      await GetCanSchdulingComps({
        ...obj,
        Schduling_Plan_Id: this.scheduleId,
        Comp_Codes: codes,
        InstallUnit_Id: install,
        Area_Id: areaId
      }).then((res) => {
        if (res.IsSucceed) {
          this.pageInfo.total = res.Data.length
          this.tbData = res.Data.map((v, idx) => {
            // 已排产赋值
            v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''
            v.Workshop_Id = v.Scheduled_Workshop_Id
            v.Workshop_Name = v.Scheduled_Workshop_Name
            v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path
            if (v.originalPath) {
              v.isDisabled = true
            }
            v.checked = false
            v.initRowIndex = idx
            return v
          })
          this.setPage()
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    /**
     * 分页
     */
    handlePageChange({ currentPage, pageSize }) {
      console.log(' currentPage, pageSize', currentPage, pageSize)
      if (this.tbLoading) return
      this.pageInfo.page = currentPage
      this.pageInfo.pageSize = pageSize
      this.setPage()
      this.filterData(currentPage)
    },

    setPage(tb = this.tbData) {
      this.fTable = tb.slice((this.pageInfo.page - 1) * this.pageInfo.pageSize, this.pageInfo.page * this.pageInfo.pageSize)
    },

    async getPartTbData() {
      const { install, areaId } = this.$route.query
      await GetPartList({
        ...this.form,
        Schduling_Plan_Id: this.scheduleId,
        InstallUnit_Id: install,
        Area_Id: areaId
      }).then((res) => {
        if (res.IsSucceed) {
          this.pageInfo.total = res.Data.length
          this.tbData = res.Data.map((v, idx) => {
            if (v.Component_Technology_Path) {
              const list = v.Component_Technology_Path.split('/')
              if (list.length && list.some(x => x === v.Part_Used_Process)) {
                v.originalUsedProcess = v.Part_Used_Process
              } else {
                v.originalUsedProcess = ''
                v.Part_Used_Process = ''
              }
            }
            v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''
            v.Workshop_Id = v.Scheduled_Workshop_Id
            v.Workshop_Name = v.Scheduled_Workshop_Name
            v.Part_Used_Process = v.Scheduled_Used_Process || v.Part_Used_Process// 是否存在已使用的领用工序
            v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path
            v.isDisabled = !!v.originalPath
            v.checked = false
            v.initRowIndex = idx
            return v
          })
          this.setPartColumn()
          this.setPage()
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    setPartColumn() {
      // 纯零件
      this.isOwnerNull = this.tbData.every(v => !v.Comp_Import_Detail_Id)
      console.log('this.isOwnerNull', this.isOwnerNull)
      if (this.isOwnerNull) {
        const idx = this.columns.findIndex(v => v.Code === 'Component_Code')
        idx !== -1 && this.columns.splice(idx, 1)
      }
    },
    mergeData(list) {
      list
        .forEach((element) => {
          const idx = this.backendTb.findIndex(
            (item) => element.puuid && item.uuid === element.puuid
          )
          if (idx !== -1) {
            this.tbData.splice(idx, 0, deepClone(this.backendTb[idx]))
          }
        })

      this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)

      this.filterData()
    },
    handleClose() {
      this.$emit('close')
    },
    activeCellMethod({ row, column, columnIndex }) {
      return column.field === 'customCountColumn'
    },
    async getTableConfig(code) {
      await GetGridByCode({
        code
      }).then((res) => {
        const { IsSucceed, Data, Message } = res
        if (IsSucceed) {
          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)
          this.pageInfo.pageSize = Number(this.tbConfig.Row_Number)
          const list = Data.ColumnList || []
          this.columns = list.filter(v => v.Is_Display).map(item => {
            if (FIX_COLUMN.includes(item.Code)) {
              item.fixed = 'left'
            }
            return item
          })
          this.columns.push({
            Display_Name: '排产数量',
            Code: 'customCountColumn'
          })
        } else {
          this.$message({
            message: Message,
            type: 'error'
          })
        }
      })
    },
    getObjectTypeList() {
      GetCompTypeTree({ professional: 'Steel' }).then((res) => {
        if (res.IsSucceed) {
          this.ObjectTypeList.data = res.Data
          this.$nextTick((_) => {
            this.$refs.treeSelectObjectType.treeDataUpdateFun(res.Data)
          })
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    getType() {
      GetPartTypeList({ Part_Grade: 0 }).then(res => {
        if (res.IsSucceed) {
          this.typeOption = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.contentBox {
  display: flex;
  flex-direction: column;

  .button {
    margin-top: 16px;
    display: flex;
    justify-content: end;
  }

  .tb-wrapper {
    flex: 1 1 auto;
    height: 50vh;
  }

  .data-info{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
  }
}
</style>

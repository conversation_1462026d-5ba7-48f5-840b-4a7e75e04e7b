{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\api\\PRO\\pro-stock.js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\api\\PRO\\pro-stock.js", "mtime": 1757468111899}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKaW1wb3J0IHFzIGZyb20gJ3FzJzsKCi8vIOWFpeW6k+aWueW8jwpleHBvcnQgdmFyIFN0b2NrSW5UeXBlcyA9IFt7CiAgbGFiZWw6ICfnlJ/kuqflhaXlupMnLAogIHZhbHVlOiAn55Sf5Lqn5YWl5bqTJwp9LCB7CiAgbGFiZWw6ICfpgIDotKflhaXlupMnLAogIHZhbHVlOiAn6YCA6LSn5YWl5bqTJwp9LCB7CiAgbGFiZWw6ICfpgIDlupPlhaXlupMnLAogIHZhbHVlOiAn6YCA5bqT5YWl5bqTJwp9XTsKLy8g5YWl5bqT57G75Z6LCmV4cG9ydCB2YXIgRnJvbV9TdG9ja19TdGF0dXNfVFlQRVMgPSBbewogIGxhYmVsOiAn55Sf5Lqn5b6F5YWl5bqTJywKICB2YWx1ZTogMAp9LCB7CiAgbGFiZWw6ICfpgIDlupPlvoXlhaXlupMnLAogIHZhbHVlOiAxCn0sIHsKICBsYWJlbDogJ+mAgOi0p+W+heWFpeW6kycsCiAgdmFsdWU6IDIKfSwgewogIGxhYmVsOiAn5bey5YWl5bqTJywKICB2YWx1ZTogMwp9XTsKZXhwb3J0IHZhciBQYWNraW5nVHlwZXMgPSBbewogIGxhYmVsOiAn55u05Y+R5Lu25YyFJywKICB2YWx1ZTogJ+ebtOWPkeS7tuWMhScKfSwgewogIGxhYmVsOiAn5p6E5Lu25YyFJywKICB2YWx1ZTogJ+aehOS7tuWMhScKfV07CmV4cG9ydCB2YXIgUGFja2luZ1N0YXR1cyA9IFt7CiAgbGFiZWw6ICflt7LlhaXlupMnLAogIHZhbHVlOiAn5bey5YWl5bqTJwp9LCB7CiAgbGFiZWw6ICflvoXlhaXlupMnLAogIHZhbHVlOiAn5b6F5YWl5bqTJwp9XTsKLy8g55uY5bqT54q25oCB77yM5bCP5LqOMyDkuLrnm5jngrnkuK0KZXhwb3J0IHZhciBJbnZlbnRvcnlDaGVja1N0YXR1cyA9IFt7CiAgbGFiZWw6ICfmnInmlYgnLAogIHZhbHVlOiAxCn0sIHsKICBsYWJlbDogJ+mHh+mbhuW8gOWniycsCiAgdmFsdWU6IDIKfSwgewogIGxhYmVsOiAn6YeH6ZuG57uT5p2fJywKICB2YWx1ZTogMwp9XTsKLy8g55uY5bqT5byC5bi457G75Z6LIFN0YXR1cyAwIOebmOS6j+W8guW4uO+8jCBTdGF0dXMgMSDnm5jnm4jlvILluLgKZXhwb3J0IHZhciBJbnZlbnRvcnlDaGVja0V4Y2VwdGlvbnMgPSBbewogIGxhYmVsOiAn6YeH6ZuG5YmN5Ye65bqTJywKICB2YWx1ZTogJ+mHh+mbhuWJjeWHuuW6kycsCiAgU3RhdHVzOiAn55uY5LqPJyAvLwp9LCB7CiAgbGFiZWw6ICfmvI/miasnLAogIHZhbHVlOiAn5ryP5omrJywKICBTdGF0dXM6ICfnm5jkuo8nCn0sIHsKICBsYWJlbDogJ+WHuuW6k+aXtuacquaJqycsCiAgdmFsdWU6ICflh7rlupPml7bmnKrmiasnLAogIFN0YXR1czogJ+ebmOS6jycKfSwgewogIGxhYmVsOiAn5YW25LuWJywKICB2YWx1ZTogJ+WFtuS7licsCiAgU3RhdHVzOiAn55uY5LqPJwp9LCB7CiAgbGFiZWw6ICflvoXlhaXlupPnirbmgIEnLAogIHZhbHVlOiAn5b6F5YWl5bqT54q25oCBJywKICBTdGF0dXM6ICfnm5jnm4gnCn0sIHsKICBsYWJlbDogJ+W3suWHuuW6k+eKtuaAgScsCiAgdmFsdWU6ICflt7Llh7rlupPnirbmgIEnLAogIFN0YXR1czogJ+ebmOebiCcKfV07CgovLyDku5PlupMv5oiQ5ZOB5YyF5Lu25YiG57G7CmV4cG9ydCB2YXIgSW52ZW50b3J5Q29tcG9uZW50VHlwZXMgPSBbewogIGxhYmVsOiAnUEPmnoTku7YnLAogIHZhbHVlOiAnUEPmnoTku7YnLAogIGljb246ICdpY29uLXByZS1jb25jcmV0ZScKfSwgewogIGxhYmVsOiAn6ZKi5p6E5p6E5Lu2JywKICB2YWx1ZTogJ+mSouaehOaehOS7ticsCiAgaWNvbjogJ2ljb24tc3RlZWwnCn0sIHsKICBsYWJlbDogJ+aJk+WMheS7ticsCiAgdmFsdWU6ICfmiZPljIXku7YnLAogIGljb246ICdpY29uLWV4cHJlc3Nib3gnCn0sIHsKICBsYWJlbDogJ+ebtOWPkeS7ticsCiAgdmFsdWU6ICfnm7Tlj5Hku7YnLAogIGljb246ICdpY29uLWxheWVycycKfV07CgovLyDojrflj5bmiYDmnInmnInmlYjku5PlupPliJfooagKZXhwb3J0IGZ1bmN0aW9uIEdldFRvdGFsV2FyZWhvdXNlTGlzdE9mQ3VyRmFjdG9yeShkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL1BSTy9XYXJlaG91c2UvR2V0VG90YWxXYXJlaG91c2VMaXN0T2ZDdXJGYWN0b3J5JywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDojrflj5bnmbvlvZXnlKjmiLfku5PlupPliJfooagKZXhwb3J0IGZ1bmN0aW9uIEdldFdhcmVob3VzZUxpc3RPZkN1ckZhY3RvcnkoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9QUk8vV2FyZWhvdXNlL0dldFdhcmVob3VzZUxpc3RPZkN1ckZhY3RvcnknLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KLy8g5qC55o2u5LuT5bqT6I635Y+W5omA5pyJ5bqT5L2NCmV4cG9ydCBmdW5jdGlvbiBHZXRMb2NhdGlvbkxpc3QoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9QUk8vTG9jYXRpb24vR2V0TG9jYXRpb25MaXN0JywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9Ci8vIOS/neWtmOWFpeW6k+WNleaNrgpleHBvcnQgZnVuY3Rpb24gU2F2ZVN0b2NrSW4oZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9QUk8vQ29tcG9uZW50U3RvY2tJbi9TYXZlU3RvY2tJbicsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQovLyDojrflj5blhaXlupPljZXlrp7kvZMKZXhwb3J0IGZ1bmN0aW9uIEdldENvbXBvbmVudFN0b2NrSW5FbnRpdHkoaWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvUFJPL0NvbXBvbmVudFN0b2NrSW4vR2V0Q29tcG9uZW50U3RvY2tJbkVudGl0eScsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIHBhcmFtczogewogICAgICBpZDogaWQKICAgIH0KICB9KTsKfQovLyDojrflj5blhaXlupPljZXmmI7nu4YKZXhwb3J0IGZ1bmN0aW9uIEdldFN0b2NrSW5EZXRhaWxMaXN0KHN0b2NrSW5JZCwgaXNFZGl0KSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL1BSTy9Db21wb25lbnRTdG9ja0luL0dldFN0b2NrSW5EZXRhaWxMaXN0JywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgcGFyYW1zOiB7CiAgICAgIHN0b2NrSW5JZDogc3RvY2tJbklkLAogICAgICBpc0VkaXQ6IGlzRWRpdAogICAgfQogIH0pOwp9Ci8vIOagoemqjOaJk+WMheS7tue8luWPt+aYr+WQpuWtmOWcqApleHBvcnQgZnVuY3Rpb24gQ2hlY2tQYWNrQ29kZShjb2RlLCBpZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9QUk8vUGFja2luZy9DaGVja1BhY2tDb2RlJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgcGFyYW1zOiB7CiAgICAgIGNvZGU6IGNvZGUsCiAgICAgIGlkOiBpZAogICAgfQogIH0pOwp9Ci8vIOiOt+WPluaJk+WMheWGheaehOS7tuivpuaDhQpleHBvcnQgZnVuY3Rpb24gR2V0UGFja2luZ0dyb3VwQnlEaXJlY3REZXRhaWxMaXN0KGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvUFJPL1BhY2tpbmcvR2V0UGFja2luZ0dyb3VwQnlEaXJlY3REZXRhaWxMaXN0JywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9Ci8vIOiOt+WPluaJk+WMheS7tuWGheaehOS7tuivpuaDhe+8iO+8iQpleHBvcnQgZnVuY3Rpb24gR2V0UGFja2luZ0RldGFpbExpc3QoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9QUk8vUGFja2luZy9HZXRQYWNraW5nRGV0YWlsTGlzdCcsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5a+85Ye65YWl5bqT5a6e5pS25Y2V6K+m5oOFCmV4cG9ydCBmdW5jdGlvbiBFeHBvcnRDb21wb25lbnRTdG9ja0luSW5mbyhpZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9QUk8vQ29tcG9uZW50U3RvY2tJbi9FeHBvcnRDb21wb25lbnRTdG9ja0luSW5mbycsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIHBhcmFtczogewogICAgICBpZDogaWQKICAgIH0KICB9KTsKfQovLyDlr7zlh7rmiZPljIXku7bmmI7nu4bliLBleGNlbApleHBvcnQgZnVuY3Rpb24gRXhwb3J0UGFja2luZ0luSW5mbyhpZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9QUk8vUGFja2luZy9FeHBvcnRQYWNraW5nSW5JbmZvJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgcGFyYW1zOiB7CiAgICAgIGlkOiBpZAogICAgfQogIH0pOwp9Ci8vIOWIoOmZpOWFpeW6k+WNleaNrgpleHBvcnQgZnVuY3Rpb24gUmVtb3ZlTWFpbihpZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9QUk8vQ29tcG9uZW50U3RvY2tJbi9SZW1vdmVNYWluJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgcGFyYW1zOiB7CiAgICAgIGlkOiBpZAogICAgfQogIH0pOwp9Ci8vIOS/neWtmOaJk+WMheS7tgpleHBvcnQgZnVuY3Rpb24gU2F2ZVBhY2tpbmcoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9QUk8vUGFja2luZy9TYXZlUGFja2luZycsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQovLyDliKDpmaTmiZPljIXku7YKZXhwb3J0IGZ1bmN0aW9uIFVuemlwUGFja2luZyhfcmVmKSB7CiAgdmFyIGlkID0gX3JlZi5pZCwKICAgIGxvY2F0aW9uSWQgPSBfcmVmLmxvY2F0aW9uSWQ7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL1BSTy9QYWNraW5nL1VuemlwUGFja2luZycsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIHBhcmFtczogewogICAgICBpZDogaWQsCiAgICAgIGxvY2F0aW9uSWQ6IGxvY2F0aW9uSWQKICAgIH0KICB9KTsKfQovLyDojrflj5bmiZPljIXku7blrp7kvZMKZXhwb3J0IGZ1bmN0aW9uIEdldFBhY2tpbmdFbnRpdHkoX3JlZjIpIHsKICB2YXIgaWQgPSBfcmVmMi5pZCwKICAgIGNvZGUgPSBfcmVmMi5jb2RlOwogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9QUk8vUGFja2luZy9HZXRQYWNraW5nRW50aXR5JywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgcGFyYW1zOiB7CiAgICAgIGlkOiBpZCwKICAgICAgY29kZTogY29kZQogICAgfQogIH0pOwp9Ci8vIOiOt+WPluenu+W6k+WNleaYjue7huWIl+ihqApleHBvcnQgZnVuY3Rpb24gR2V0U3RvY2tNb3ZlRGV0YWlsTGlzdChiaWxsSWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvUFJPL0NvbXBvbmVudFN0b2NrTW92ZS9HZXRTdG9ja01vdmVEZXRhaWxMaXN0JywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgcGFyYW1zOiB7CiAgICAgIGJpbGxJZDogYmlsbElkCiAgICB9CiAgfSk7Cn0KLy8g5L+d5a2Y56e75bqT5Y2VCmV4cG9ydCBmdW5jdGlvbiBTYXZlU3RvY2tNb3ZlKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvUFJPL0NvbXBvbmVudFN0b2NrTW92ZS9TYXZlU3RvY2tNb3ZlJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9Ci8vIOS/neWtmOebmOeCueWNlQpleHBvcnQgZnVuY3Rpb24gU2F2ZUludmVudG9yeShkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL1BSTy9Db21wb25lbnRJbnZlbnRvcnkvU2F2ZUludmVudG9yeScsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQovLyDnm5jngrnmmI7nu4blvILluLjlpITnkIYKZXhwb3J0IGZ1bmN0aW9uIEhhbmRsZUludmVudG9yeUl0ZW0oX3JlZjMpIHsKICB2YXIgaWQgPSBfcmVmMy5pZCwKICAgIHR5cGUgPSBfcmVmMy50eXBlLAogICAgdmFsdWUgPSBfcmVmMy52YWx1ZTsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvUFJPL0NvbXBvbmVudEludmVudG9yeS9IYW5kbGVJbnZlbnRvcnlJdGVtJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgcGFyYW1zOiB7CiAgICAgIGtleVZhbHVlOiBpZCwKICAgICAgdHlwZTogdHlwZSwKICAgICAgdmFsdWU6IHZhbHVlCiAgICB9CiAgfSk7Cn0KLy8g5L+d5a2Y5oql5bqf5Y2V5o2uCmV4cG9ydCBmdW5jdGlvbiBTYXZlQ29tcG9uZW50U2NyYXAoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9QUk8vQ29tcG9uZW50U2NyYXAvU2F2ZUNvbXBvbmVudFNjcmFwJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9Ci8vIOebmOW6k+e7k+adn+mHh+mbhgpleHBvcnQgZnVuY3Rpb24gRmluaXNoQ29sbGVjdChpZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9QUk8vQ29tcG9uZW50SW52ZW50b3J5L0ZpbmlzaENvbGxlY3QnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBwYXJhbXM6IHsKICAgICAga2V5VmFsdWU6IGlkCiAgICB9CiAgfSk7Cn0KLy8g5riF5Y2V6b2Q5aWX56Gu6K6kCmV4cG9ydCBmdW5jdGlvbiBVcGRhdGVCaWxsUmVhZHkoX3JlZjQpIHsKICB2YXIgaW5zdGFsbElkID0gX3JlZjQuaW5zdGFsbElkLAogICAgaXNSZWFkeSA9IF9yZWY0LmlzUmVhZHk7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL1BSTy9Qcm9kdWN0aW9uUHJlcGFyZS9VcGRhdGVCaWxsUmVhZHknLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBwYXJhbXM6IHsKICAgICAgaW5zdGFsbElkOiBpbnN0YWxsSWQsCiAgICAgIGlzUmVhZHk6IGlzUmVhZHkKICAgIH0KICB9KTsKfQovLyDnianmlpnpvZDlpZfnoa7orqQKZXhwb3J0IGZ1bmN0aW9uIFVwZGF0ZU1hdGVyaWFsUmVhZHkoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9QUk8vUHJvZHVjdGlvblByZXBhcmUvVXBkYXRlTWF0ZXJpYWxSZWFkeScsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQpleHBvcnQgZnVuY3Rpb24gRXhwb3J0V2FpdGluZ1N0b2NrSW4ybmRMaXN0KGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvUFJPL2NvbXBvbmVudHN0b2NraW4vRXhwb3J0V2FpdGluZ1N0b2NrSW4ybmRMaXN0JywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9"}, {"version": 3, "names": ["request", "qs", "StockInTypes", "label", "value", "From_Stock_Status_TYPES", "PackingTypes", "PackingStatus", "InventoryCheckStatus", "InventoryCheckExceptions", "Status", "InventoryComponentTypes", "icon", "GetTotalWarehouseListOfCurFactory", "data", "url", "method", "GetWarehouseListOfCurFactory", "GetLocationList", "SaveStockIn", "GetComponentStockInEntity", "id", "params", "GetStockInDetailList", "stockInId", "isEdit", "CheckPackCode", "code", "GetPackingGroupByDirectDetailList", "GetPackingDetailList", "ExportComponentStockInInfo", "ExportPackingInInfo", "RemoveMain", "SavePacking", "UnzipPacking", "_ref", "locationId", "GetPackingEntity", "_ref2", "GetStockMoveDetailList", "billId", "SaveStockMove", "SaveInventory", "HandleInventoryItem", "_ref3", "type", "keyValue", "SaveComponentScrap", "FinishCollect", "UpdateBillReady", "_ref4", "installId", "isReady", "UpdateMaterialReady", "ExportWaitingStockIn2ndList"], "sources": ["D:/project/platform_framework_master/platform_framework/Platform/Frontend/SubAppProduce/src/api/PRO/pro-stock.js"], "sourcesContent": ["import request from '@/utils/request'\r\nimport qs from 'qs'\r\n\r\n// 入库方式\r\nexport const StockInTypes = [{\r\n  label: '生产入库',\r\n  value: '生产入库'\r\n}, {\r\n  label: '退货入库',\r\n  value: '退货入库'\r\n}, {\r\n  label: '退库入库',\r\n  value: '退库入库'\r\n}]\r\n// 入库类型\r\nexport const From_Stock_Status_TYPES = [{\r\n  label: '生产待入库',\r\n  value: 0\r\n}, {\r\n  label: '退库待入库',\r\n  value: 1\r\n}, {\r\n  label: '退货待入库',\r\n  value: 2\r\n}, {\r\n  label: '已入库',\r\n  value: 3\r\n}]\r\nexport const PackingTypes = [{\r\n  label: '直发件包',\r\n  value: '直发件包'\r\n}, {\r\n  label: '构件包',\r\n  value: '构件包'\r\n}]\r\nexport const PackingStatus = [{\r\n  label: '已入库',\r\n  value: '已入库'\r\n}, {\r\n  label: '待入库',\r\n  value: '待入库'\r\n}]\r\n// 盘库状态，小于3 为盘点中\r\nexport const InventoryCheckStatus = [{\r\n  label: '有效',\r\n  value: 1\r\n}, {\r\n  label: '采集开始',\r\n  value: 2\r\n}, {\r\n  label: '采集结束',\r\n  value: 3\r\n}]\r\n// 盘库异常类型 Status 0 盘亏异常， Status 1 盘盈异常\r\nexport const InventoryCheckExceptions = [\r\n  {\r\n    label: '采集前出库',\r\n    value: '采集前出库',\r\n    Status: '盘亏' //\r\n  },\r\n  {\r\n    label: '漏扫',\r\n    value: '漏扫',\r\n    Status: '盘亏'\r\n  },\r\n  {\r\n    label: '出库时未扫',\r\n    value: '出库时未扫',\r\n    Status: '盘亏'\r\n  },\r\n  {\r\n    label: '其他',\r\n    value: '其他',\r\n    Status: '盘亏'\r\n  },\r\n  {\r\n    label: '待入库状态',\r\n    value: '待入库状态',\r\n    Status: '盘盈'\r\n  },\r\n  {\r\n    label: '已出库状态',\r\n    value: '已出库状态',\r\n    Status: '盘盈'\r\n  }\r\n]\r\n\r\n// 仓库/成品包件分类\r\nexport const InventoryComponentTypes = [\r\n  {\r\n    label: 'PC构件',\r\n    value: 'PC构件',\r\n    icon: 'icon-pre-concrete'\r\n  },\r\n  {\r\n    label: '钢构构件',\r\n    value: '钢构构件',\r\n    icon: 'icon-steel'\r\n  },\r\n  {\r\n    label: '打包件',\r\n    value: '打包件',\r\n    icon: 'icon-expressbox'\r\n  },\r\n  {\r\n    label: '直发件',\r\n    value: '直发件',\r\n    icon: 'icon-layers'\r\n  }\r\n]\r\n\r\n// 获取所有有效仓库列表\r\nexport function GetTotalWarehouseListOfCurFactory(data) {\r\n  return request({\r\n    url: '/PRO/Warehouse/GetTotalWarehouseListOfCurFactory',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 获取登录用户仓库列表\r\nexport function GetWarehouseListOfCurFactory(data) {\r\n  return request({\r\n    url: '/PRO/Warehouse/GetWarehouseListOfCurFactory',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 根据仓库获取所有库位\r\nexport function GetLocationList(data) {\r\n  return request({\r\n    url: '/PRO/Location/GetLocationList',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 保存入库单据\r\nexport function SaveStockIn(data) {\r\n  return request({\r\n    url: '/PRO/ComponentStockIn/SaveStockIn',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 获取入库单实体\r\nexport function GetComponentStockInEntity(id) {\r\n  return request({\r\n    url: '/PRO/ComponentStockIn/GetComponentStockInEntity',\r\n    method: 'post',\r\n    params: {\r\n      id\r\n    }\r\n  })\r\n}\r\n// 获取入库单明细\r\nexport function GetStockInDetailList(stockInId, isEdit) {\r\n  return request({\r\n    url: '/PRO/ComponentStockIn/GetStockInDetailList',\r\n    method: 'post',\r\n    params: {\r\n      stockInId,\r\n      isEdit\r\n    }\r\n  })\r\n}\r\n// 校验打包件编号是否存在\r\nexport function CheckPackCode(code, id) {\r\n  return request({\r\n    url: '/PRO/Packing/CheckPackCode',\r\n    method: 'post',\r\n    params: {\r\n      code,\r\n      id\r\n    }\r\n  })\r\n}\r\n// 获取打包内构件详情\r\nexport function GetPackingGroupByDirectDetailList(data) {\r\n  return request({\r\n    url: '/PRO/Packing/GetPackingGroupByDirectDetailList',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 获取打包件内构件详情（）\r\nexport function GetPackingDetailList(data) {\r\n  return request({\r\n    url: '/PRO/Packing/GetPackingDetailList',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 导出入库实收单详情\r\nexport function ExportComponentStockInInfo(id) {\r\n  return request({\r\n    url: '/PRO/ComponentStockIn/ExportComponentStockInInfo',\r\n    method: 'post',\r\n    params: {\r\n      id\r\n    }\r\n  })\r\n}\r\n// 导出打包件明细到excel\r\nexport function ExportPackingInInfo(id) {\r\n  return request({\r\n    url: '/PRO/Packing/ExportPackingInInfo',\r\n    method: 'post',\r\n    params: {\r\n      id\r\n    }\r\n  })\r\n}\r\n// 删除入库单据\r\nexport function RemoveMain(id) {\r\n  return request({\r\n    url: '/PRO/ComponentStockIn/RemoveMain',\r\n    method: 'post',\r\n    params: {\r\n      id\r\n    }\r\n  })\r\n}\r\n// 保存打包件\r\nexport function SavePacking(data) {\r\n  return request({\r\n    url: '/PRO/Packing/SavePacking',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 删除打包件\r\nexport function UnzipPacking({ id, locationId }) {\r\n  return request({\r\n    url: '/PRO/Packing/UnzipPacking',\r\n    method: 'post',\r\n    params: {\r\n      id,\r\n      locationId\r\n    }\r\n  })\r\n}\r\n// 获取打包件实体\r\nexport function GetPackingEntity({ id, code }) {\r\n  return request({\r\n    url: '/PRO/Packing/GetPackingEntity',\r\n    method: 'post',\r\n    params: {\r\n      id,\r\n      code\r\n    }\r\n  })\r\n}\r\n// 获取移库单明细列表\r\nexport function GetStockMoveDetailList(billId) {\r\n  return request({\r\n    url: '/PRO/ComponentStockMove/GetStockMoveDetailList',\r\n    method: 'post',\r\n    params: {\r\n      billId\r\n    }\r\n  })\r\n}\r\n// 保存移库单\r\nexport function SaveStockMove(data) {\r\n  return request({\r\n    url: '/PRO/ComponentStockMove/SaveStockMove',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 保存盘点单\r\nexport function SaveInventory(data) {\r\n  return request({\r\n    url: '/PRO/ComponentInventory/SaveInventory',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 盘点明细异常处理\r\nexport function HandleInventoryItem({ id, type, value }) {\r\n  return request({\r\n    url: '/PRO/ComponentInventory/HandleInventoryItem',\r\n    method: 'post',\r\n    params: {\r\n      keyValue: id,\r\n      type,\r\n      value\r\n    }\r\n  })\r\n}\r\n// 保存报废单据\r\nexport function SaveComponentScrap(data) {\r\n  return request({\r\n    url: '/PRO/ComponentScrap/SaveComponentScrap',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 盘库结束采集\r\nexport function FinishCollect(id) {\r\n  return request({\r\n    url: '/PRO/ComponentInventory/FinishCollect',\r\n    method: 'post',\r\n    params: {\r\n      keyValue: id\r\n    }\r\n  })\r\n}\r\n// 清单齐套确认\r\nexport function UpdateBillReady({ installId, isReady }) {\r\n  return request({\r\n    url: '/PRO/ProductionPrepare/UpdateBillReady',\r\n    method: 'post',\r\n    params: {\r\n      installId,\r\n      isReady\r\n    }\r\n  })\r\n}\r\n// 物料齐套确认\r\nexport function UpdateMaterialReady(data) {\r\n  return request({\r\n    url: '/PRO/ProductionPrepare/UpdateMaterialReady',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\nexport function ExportWaitingStockIn2ndList(data) {\r\n  return request({\r\n    url: '/PRO/componentstockin/ExportWaitingStockIn2ndList',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;AACrC,OAAOC,EAAE,MAAM,IAAI;;AAEnB;AACA,OAAO,IAAMC,YAAY,GAAG,CAAC;EAC3BC,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE;AACT,CAAC,EAAE;EACDD,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE;AACT,CAAC,EAAE;EACDD,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE;AACT,CAAC,CAAC;AACF;AACA,OAAO,IAAMC,uBAAuB,GAAG,CAAC;EACtCF,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE;AACT,CAAC,EAAE;EACDD,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE;AACT,CAAC,EAAE;EACDD,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE;AACT,CAAC,EAAE;EACDD,KAAK,EAAE,KAAK;EACZC,KAAK,EAAE;AACT,CAAC,CAAC;AACF,OAAO,IAAME,YAAY,GAAG,CAAC;EAC3BH,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE;AACT,CAAC,EAAE;EACDD,KAAK,EAAE,KAAK;EACZC,KAAK,EAAE;AACT,CAAC,CAAC;AACF,OAAO,IAAMG,aAAa,GAAG,CAAC;EAC5BJ,KAAK,EAAE,KAAK;EACZC,KAAK,EAAE;AACT,CAAC,EAAE;EACDD,KAAK,EAAE,KAAK;EACZC,KAAK,EAAE;AACT,CAAC,CAAC;AACF;AACA,OAAO,IAAMI,oBAAoB,GAAG,CAAC;EACnCL,KAAK,EAAE,IAAI;EACXC,KAAK,EAAE;AACT,CAAC,EAAE;EACDD,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE;AACT,CAAC,EAAE;EACDD,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE;AACT,CAAC,CAAC;AACF;AACA,OAAO,IAAMK,wBAAwB,GAAG,CACtC;EACEN,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,OAAO;EACdM,MAAM,EAAE,IAAI,CAAC;AACf,CAAC,EACD;EACEP,KAAK,EAAE,IAAI;EACXC,KAAK,EAAE,IAAI;EACXM,MAAM,EAAE;AACV,CAAC,EACD;EACEP,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,OAAO;EACdM,MAAM,EAAE;AACV,CAAC,EACD;EACEP,KAAK,EAAE,IAAI;EACXC,KAAK,EAAE,IAAI;EACXM,MAAM,EAAE;AACV,CAAC,EACD;EACEP,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,OAAO;EACdM,MAAM,EAAE;AACV,CAAC,EACD;EACEP,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,OAAO;EACdM,MAAM,EAAE;AACV,CAAC,CACF;;AAED;AACA,OAAO,IAAMC,uBAAuB,GAAG,CACrC;EACER,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE,MAAM;EACbQ,IAAI,EAAE;AACR,CAAC,EACD;EACET,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE,MAAM;EACbQ,IAAI,EAAE;AACR,CAAC,EACD;EACET,KAAK,EAAE,KAAK;EACZC,KAAK,EAAE,KAAK;EACZQ,IAAI,EAAE;AACR,CAAC,EACD;EACET,KAAK,EAAE,KAAK;EACZC,KAAK,EAAE,KAAK;EACZQ,IAAI,EAAE;AACR,CAAC,CACF;;AAED;AACA,OAAO,SAASC,iCAAiCA,CAACC,IAAI,EAAE;EACtD,OAAOd,OAAO,CAAC;IACbe,GAAG,EAAE,kDAAkD;IACvDC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,4BAA4BA,CAACH,IAAI,EAAE;EACjD,OAAOd,OAAO,CAAC;IACbe,GAAG,EAAE,6CAA6C;IAClDC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASI,eAAeA,CAACJ,IAAI,EAAE;EACpC,OAAOd,OAAO,CAAC;IACbe,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASK,WAAWA,CAACL,IAAI,EAAE;EAChC,OAAOd,OAAO,CAAC;IACbe,GAAG,EAAE,mCAAmC;IACxCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASM,yBAAyBA,CAACC,EAAE,EAAE;EAC5C,OAAOrB,OAAO,CAAC;IACbe,GAAG,EAAE,iDAAiD;IACtDC,MAAM,EAAE,MAAM;IACdM,MAAM,EAAE;MACND,EAAE,EAAFA;IACF;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASE,oBAAoBA,CAACC,SAAS,EAAEC,MAAM,EAAE;EACtD,OAAOzB,OAAO,CAAC;IACbe,GAAG,EAAE,4CAA4C;IACjDC,MAAM,EAAE,MAAM;IACdM,MAAM,EAAE;MACNE,SAAS,EAATA,SAAS;MACTC,MAAM,EAANA;IACF;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAEN,EAAE,EAAE;EACtC,OAAOrB,OAAO,CAAC;IACbe,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,MAAM;IACdM,MAAM,EAAE;MACNK,IAAI,EAAJA,IAAI;MACJN,EAAE,EAAFA;IACF;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASO,iCAAiCA,CAACd,IAAI,EAAE;EACtD,OAAOd,OAAO,CAAC;IACbe,GAAG,EAAE,gDAAgD;IACrDC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASe,oBAAoBA,CAACf,IAAI,EAAE;EACzC,OAAOd,OAAO,CAAC;IACbe,GAAG,EAAE,mCAAmC;IACxCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASgB,0BAA0BA,CAACT,EAAE,EAAE;EAC7C,OAAOrB,OAAO,CAAC;IACbe,GAAG,EAAE,kDAAkD;IACvDC,MAAM,EAAE,MAAM;IACdM,MAAM,EAAE;MACND,EAAE,EAAFA;IACF;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASU,mBAAmBA,CAACV,EAAE,EAAE;EACtC,OAAOrB,OAAO,CAAC;IACbe,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,MAAM;IACdM,MAAM,EAAE;MACND,EAAE,EAAFA;IACF;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASW,UAAUA,CAACX,EAAE,EAAE;EAC7B,OAAOrB,OAAO,CAAC;IACbe,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,MAAM;IACdM,MAAM,EAAE;MACND,EAAE,EAAFA;IACF;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASY,WAAWA,CAACnB,IAAI,EAAE;EAChC,OAAOd,OAAO,CAAC;IACbe,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASoB,YAAYA,CAAAC,IAAA,EAAqB;EAAA,IAAlBd,EAAE,GAAAc,IAAA,CAAFd,EAAE;IAAEe,UAAU,GAAAD,IAAA,CAAVC,UAAU;EAC3C,OAAOpC,OAAO,CAAC;IACbe,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,MAAM;IACdM,MAAM,EAAE;MACND,EAAE,EAAFA,EAAE;MACFe,UAAU,EAAVA;IACF;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASC,gBAAgBA,CAAAC,KAAA,EAAe;EAAA,IAAZjB,EAAE,GAAAiB,KAAA,CAAFjB,EAAE;IAAEM,IAAI,GAAAW,KAAA,CAAJX,IAAI;EACzC,OAAO3B,OAAO,CAAC;IACbe,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,MAAM;IACdM,MAAM,EAAE;MACND,EAAE,EAAFA,EAAE;MACFM,IAAI,EAAJA;IACF;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASY,sBAAsBA,CAACC,MAAM,EAAE;EAC7C,OAAOxC,OAAO,CAAC;IACbe,GAAG,EAAE,gDAAgD;IACrDC,MAAM,EAAE,MAAM;IACdM,MAAM,EAAE;MACNkB,MAAM,EAANA;IACF;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASC,aAAaA,CAAC3B,IAAI,EAAE;EAClC,OAAOd,OAAO,CAAC;IACbe,GAAG,EAAE,uCAAuC;IAC5CC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAAS4B,aAAaA,CAAC5B,IAAI,EAAE;EAClC,OAAOd,OAAO,CAAC;IACbe,GAAG,EAAE,uCAAuC;IAC5CC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAAS6B,mBAAmBA,CAAAC,KAAA,EAAsB;EAAA,IAAnBvB,EAAE,GAAAuB,KAAA,CAAFvB,EAAE;IAAEwB,IAAI,GAAAD,KAAA,CAAJC,IAAI;IAAEzC,KAAK,GAAAwC,KAAA,CAALxC,KAAK;EACnD,OAAOJ,OAAO,CAAC;IACbe,GAAG,EAAE,6CAA6C;IAClDC,MAAM,EAAE,MAAM;IACdM,MAAM,EAAE;MACNwB,QAAQ,EAAEzB,EAAE;MACZwB,IAAI,EAAJA,IAAI;MACJzC,KAAK,EAALA;IACF;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAAS2C,kBAAkBA,CAACjC,IAAI,EAAE;EACvC,OAAOd,OAAO,CAAC;IACbe,GAAG,EAAE,wCAAwC;IAC7CC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASkC,aAAaA,CAAC3B,EAAE,EAAE;EAChC,OAAOrB,OAAO,CAAC;IACbe,GAAG,EAAE,uCAAuC;IAC5CC,MAAM,EAAE,MAAM;IACdM,MAAM,EAAE;MACNwB,QAAQ,EAAEzB;IACZ;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAAS4B,eAAeA,CAAAC,KAAA,EAAyB;EAAA,IAAtBC,SAAS,GAAAD,KAAA,CAATC,SAAS;IAAEC,OAAO,GAAAF,KAAA,CAAPE,OAAO;EAClD,OAAOpD,OAAO,CAAC;IACbe,GAAG,EAAE,wCAAwC;IAC7CC,MAAM,EAAE,MAAM;IACdM,MAAM,EAAE;MACN6B,SAAS,EAATA,SAAS;MACTC,OAAO,EAAPA;IACF;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASC,mBAAmBA,CAACvC,IAAI,EAAE;EACxC,OAAOd,OAAO,CAAC;IACbe,GAAG,EAAE,4CAA4C;IACjDC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASwC,2BAA2BA,CAACxC,IAAI,EAAE;EAChD,OAAOd,OAAO,CAAC;IACbe,GAAG,EAAE,mDAAmD;IACxDC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ", "ignoreList": []}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\group\\component\\info.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\group\\component\\info.vue", "mtime": 1757468112139}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetWorkingTeamInfo", "data", "tags", "form", "Name", "Manager_UserName", "Manager_UserId", "Load", "Workshop_Name", "Month_Avg_Load", "Sort", "Is_Outsource", "Is_Enabled", "Warehouse_Id", "Location_Id", "Warehouse_Name", "Location_Name", "rules", "Is_Workshop_Enabled", "created", "methods", "initData", "row", "_this", "id", "Id", "then", "res", "IsSucceed", "_res$Data", "Data", "Users", "$message", "message", "Message", "type"], "sources": ["src/views/PRO/basic-information/group/component/info.vue"], "sourcesContent": ["<template>\r\n  <div style=\"padding: 16px 0;\">\r\n    <el-form\r\n      ref=\"form\"\r\n      :model=\"form\"\r\n      inline\r\n      :rules=\"rules\"\r\n      label-width=\"130px\"\r\n      style=\"width: 100%\"\r\n    >\r\n      <h3>基本信息</h3>\r\n      <el-row>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"班组名称：\">\r\n            {{ form.Name || \"-\" }}\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"班组长：\">\r\n            {{ form.Manager_UserName || \"-\" }}\r\n          </el-form-item>\r\n        </el-col>\r\n        <!-- <el-form-item label=\"负荷提醒线：\">\r\n        {{ form.Load || \"-\" }}\r\n      </el-form-item> -->\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"排序号：\">\r\n            {{ form.Sort || \"-\" }}\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"班组月均负荷(t)：\">\r\n            {{ form.Month_Avg_Load || \"-\" }}\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"是否外协：\">\r\n            {{ form.Is_Outsource === true ? \"是\" : \"否\" }}\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"是否启用：\">\r\n            {{ form. Is_Enabled === true ? \"是\" : \"否\" }}\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row>\r\n        <el-col :span=\"16\">\r\n          <el-form-item label=\"关联仓库/库位：\">\r\n            {{ form.Warehouse_Name ? form.Warehouse_Name + '/' + form.Location_Name : \"-\" }}\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <el-form-item v-if=\"Is_Workshop_Enabled\" label=\"所属车间：\">\r\n            {{ form.Workshop_Name || \"-\" }}\r\n          </el-form-item>\r\n        </el-col></el-row>\r\n      <h3>班组成员</h3>\r\n      <div class=\"tag-x\">\r\n        <div class=\"tag-wrapper\">\r\n          <el-tag\r\n            v-for=\"tag in tags\"\r\n            :key=\"tag.User_Id\"\r\n            size=\"large\"\r\n            type=\"info\"\r\n          >\r\n            {{ tag.User_Name }}\r\n          </el-tag>\r\n        </div>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetWorkingTeamInfo } from '@/api/PRO/technology-lib'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      tags: [],\r\n      form: {\r\n        Name: '',\r\n        Manager_UserName: '',\r\n        Manager_UserId: '',\r\n        Load: '',\r\n        Workshop_Name: '',\r\n        Month_Avg_Load: null,\r\n        Sort: 0,\r\n        Is_Outsource: false,\r\n        Is_Enabled: true,\r\n        Warehouse_Id: '',\r\n        Location_Id: '',\r\n        Warehouse_Name: '',\r\n        Location_Name: ''\r\n      },\r\n      rules: {},\r\n\r\n      Is_Workshop_Enabled: ''\r\n    }\r\n  },\r\n  created() {\r\n  },\r\n  methods: {\r\n    initData(row, Is_Workshop_Enabled) {\r\n      GetWorkingTeamInfo({\r\n        id: row.Id\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const { Manager_UserName, Manager_UserId, Load, Name, Users, Month_Avg_Load, Sort, Is_Outsource, Is_Enabled, Warehouse_Name, Location_Name } =\r\n            res.Data\r\n          this.form.Manager_UserName = Manager_UserName\r\n          this.form.Manager_UserId = Manager_UserId\r\n          this.form.Load = Load\r\n          this.form.Name = Name\r\n          this.tags = Users\r\n          this.form.Workshop_Name = row.Workshop_Name\r\n          this.Is_Workshop_Enabled = Is_Workshop_Enabled\r\n          this.form.Month_Avg_Load = Month_Avg_Load\r\n          this.form.Sort = Sort\r\n          this.form.Is_Outsource = Is_Outsource\r\n          this.form.Is_Enabled = Is_Enabled\r\n          this.form.Warehouse_Name = Warehouse_Name\r\n          this.form.Location_Name = Location_Name\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"~@/styles/mixin.scss\";\r\n\r\nh3 {\r\n  color: #298dff;\r\n}\r\n.tag-x {\r\n  text-align: left;\r\n  .tag-wrapper {\r\n    display: inline-block;\r\n    flex-wrap: wrap;\r\n    height: 160px;\r\n    overflow: auto;\r\n    @include scrollBar;\r\n    .el-tag {\r\n      margin: 8px 0 0 8px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6EA,SAAAA,kBAAA;AAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;MACAC,IAAA;QACAC,IAAA;QACAC,gBAAA;QACAC,cAAA;QACAC,IAAA;QACAC,aAAA;QACAC,cAAA;QACAC,IAAA;QACAC,YAAA;QACAC,UAAA;QACAC,YAAA;QACAC,WAAA;QACAC,cAAA;QACAC,aAAA;MACA;MACAC,KAAA;MAEAC,mBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA,GACA;EACAC,OAAA;IACAC,QAAA,WAAAA,SAAAC,GAAA,EAAAJ,mBAAA;MAAA,IAAAK,KAAA;MACAvB,kBAAA;QACAwB,EAAA,EAAAF,GAAA,CAAAG;MACA,GAAAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA,IAAAC,SAAA,GACAF,GAAA,CAAAG,IAAA;YADAzB,gBAAA,GAAAwB,SAAA,CAAAxB,gBAAA;YAAAC,cAAA,GAAAuB,SAAA,CAAAvB,cAAA;YAAAC,IAAA,GAAAsB,SAAA,CAAAtB,IAAA;YAAAH,IAAA,GAAAyB,SAAA,CAAAzB,IAAA;YAAA2B,KAAA,GAAAF,SAAA,CAAAE,KAAA;YAAAtB,cAAA,GAAAoB,SAAA,CAAApB,cAAA;YAAAC,IAAA,GAAAmB,SAAA,CAAAnB,IAAA;YAAAC,YAAA,GAAAkB,SAAA,CAAAlB,YAAA;YAAAC,UAAA,GAAAiB,SAAA,CAAAjB,UAAA;YAAAG,cAAA,GAAAc,SAAA,CAAAd,cAAA;YAAAC,aAAA,GAAAa,SAAA,CAAAb,aAAA;UAEAO,KAAA,CAAApB,IAAA,CAAAE,gBAAA,GAAAA,gBAAA;UACAkB,KAAA,CAAApB,IAAA,CAAAG,cAAA,GAAAA,cAAA;UACAiB,KAAA,CAAApB,IAAA,CAAAI,IAAA,GAAAA,IAAA;UACAgB,KAAA,CAAApB,IAAA,CAAAC,IAAA,GAAAA,IAAA;UACAmB,KAAA,CAAArB,IAAA,GAAA6B,KAAA;UACAR,KAAA,CAAApB,IAAA,CAAAK,aAAA,GAAAc,GAAA,CAAAd,aAAA;UACAe,KAAA,CAAAL,mBAAA,GAAAA,mBAAA;UACAK,KAAA,CAAApB,IAAA,CAAAM,cAAA,GAAAA,cAAA;UACAc,KAAA,CAAApB,IAAA,CAAAO,IAAA,GAAAA,IAAA;UACAa,KAAA,CAAApB,IAAA,CAAAQ,YAAA,GAAAA,YAAA;UACAY,KAAA,CAAApB,IAAA,CAAAS,UAAA,GAAAA,UAAA;UACAW,KAAA,CAAApB,IAAA,CAAAY,cAAA,GAAAA,cAAA;UACAQ,KAAA,CAAApB,IAAA,CAAAa,aAAA,GAAAA,aAAA;QACA;UACAO,KAAA,CAAAS,QAAA;YACAC,OAAA,EAAAN,GAAA,CAAAO,OAAA;YACAC,IAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
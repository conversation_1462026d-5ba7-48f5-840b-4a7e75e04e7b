{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\index.vue?vue&type=template&id=7290e011&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\index.vue", "mtime": 1758095444832}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\index.vue", "mtime": 1756086955309}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup", "sourcesContent": ["<template>\n  <div>\n    <div class=\"app-container abs100\">\n      <div class=\"h100 wrapper-c parent\">\n        <div class=\"title\">\n          <span\n            v-for=\"(item, index) in title\"\n            :key=\"index\"\n            style=\"cursor: pointer\"\n            :class=\"spanCurr == index ? 'clickindex' : 'index'\"\n            @click=\"handelIndex(index, item)\"\n          >{{ item.Display_Name }}</span>\n        </div>\n        <div class=\"detail\">\n          <template>\n            <el-tabs\n              v-model=\"activeName\"\n              type=\"card\"\n              style=\"width: 100%; height: 100%\"\n            >\n              <el-tab-pane label=\"检查类型\" name=\"检查类型\">\n                <CheckType\n                  ref=\"checkTypeRef\"\n                  :check-type=\"checkType\"\n                  @optionFn=\"optionEdit\"\n                />\n              </el-tab-pane>\n              <el-tab-pane label=\"检查项\" name=\"检查项\">\n                <CheckItem\n                  ref=\"checkItemRef\"\n                  :check-type=\"checkType\"\n                  @ItemEdit=\"ItemEdit\"\n                />\n              </el-tab-pane>\n              <el-tab-pane label=\"检查项组合\" name=\"检查项组合\">\n                <CheckCombination\n                  ref=\"checkCombinationRef\"\n                  :check-type=\"checkType\"\n                  @CombinationEdit=\"CombinationEdit\"\n                />\n              </el-tab-pane>\n              <el-tab-pane label=\"质检节点配置\" name=\"质检节点配置\">\n                <CheckNode\n                  ref=\"checkNodeRef\"\n                  :check-type=\"checkType\"\n                  @NodeEdit=\"NodeEdit\"\n                />\n              </el-tab-pane>\n              <el-tab-pane v-if=\"isCom\" label=\"公差配置\" name=\"公差配置\">\n                <ToleranceConfig\n                  ref=\"toleranceConfigRef\"\n                  :check-type=\"checkType\"\n                  @edit=\"addToleranceConfig\"\n                />\n              </el-tab-pane>\n              <el-button\n                type=\"primary\"\n                class=\"addbtn\"\n                @click=\"addData\"\n              >新增</el-button>\n            </el-tabs>\n          </template>\n        </div>\n      </div>\n    </div>\n    <el-dialog\n      v-if=\"dialogVisible\"\n      ref=\"content\"\n      v-el-drag-dialog\n      :title=\"dialogTitle\"\n      :visible.sync=\"dialogVisible\"\n      :width=\"width\"\n      class=\"z-dialog\"\n      @close=\"handleClose\"\n    >\n      <component\n        :is=\"currentComponent\"\n        ref=\"content\"\n        :dialog-data=\"dialogData\"\n        @ToleranceRefresh=\"ToleranceRefresh\"\n        @close=\"handleClose\"\n      />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport CheckType from './components/CheckType' // 检查类型\nimport CheckCombination from './components/CheckCombination' // 检查项组合\nimport CheckNode from './components/CheckNode' // 质检节点配置\nimport CheckItem from './components/CheckItem' // 检查项\nimport TypeDialog from './components/Dialog/TypeDialog' // 检查类型弹窗\nimport ItemDialog from './components/Dialog/ItemDialog' // 检查项弹窗\nimport CombinationDialog from './components/Dialog/CombinationDialog' // 检查项组合弹窗\nimport NodeDialog from './components/Dialog/NodeDialog' // 质检节点组合弹窗\nimport ToleranceConfig from './components/ToleranceConfig.vue' // 公差配置\nimport { GetDictionaryDetailListByCode } from '@/api/PRO/factorycheck'\nimport elDragDialog from '@/directive/el-drag-dialog'\nimport ToleranceDialog from './components/Dialog/ToleranceDialog' // 公差配置弹窗\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils.js' // BOM层级\nexport default {\n  name: 'PLMFactoryGroupList',\n  directives: { elDragDialog },\n  components: {\n    CheckType,\n    ToleranceConfig,\n    CheckCombination,\n    CheckNode,\n    CheckItem,\n    TypeDialog,\n    ItemDialog,\n    CombinationDialog,\n    NodeDialog,\n    ToleranceDialog\n  },\n  data() {\n    return {\n      spanCurr: 0,\n      title: [],\n      activeName: '检查类型',\n      checkType: {},\n      tbLoading: false,\n      tbData: [],\n      dialogVisible: false,\n      currentComponent: '',\n      dialogTitle: '',\n      isCom: false,\n      width: '60%',\n      dialogData: {}\n    }\n  },\n  created() {},\n  async mounted() {\n    const a = await GetBOMInfo()\n    console.log('aaa', a)\n    this.getCheckType()\n  },\n  methods: {\n    getCheckType() {\n      GetDictionaryDetailListByCode({ dictionaryCode: 'Quality_Code' }).then(\n        (res) => {\n          if (res.IsSucceed) {\n            this.title = res.Data // wtf\n            this.checkType = this.title[0]// wtf\n            this.isCom = res.Data.find(v => v.Value === '0')\n          } else {\n            this.$message({\n              type: 'error',\n              message: 'res.Message'\n            })\n          }\n        }\n      )\n    },\n    handelIndex(index, item) {\n      this.isCom = item.Value === '0'\n      console.log(index)\n      console.log(item)\n      if (!this.isCom && this.activeName === '公差配置') {\n        this.activeName = '检查类型'\n      }\n      this.checkType = item\n      this.spanCurr = index\n    },\n    addData() {\n      console.log(this.activeName)\n      switch (this.activeName) {\n        case '检查类型':\n          this.addCheckType()\n          break\n        case '检查项':\n          this.addCheckItem()\n          break\n        case '检查项组合':\n          this.addCheckCombination()\n          break\n        case '质检节点配置':\n          this.addCheckNode()\n          break\n        case '公差配置':\n          this.addToleranceConfig()\n          break\n        default:\n          this.addCheckType()\n          console.log('111')\n      }\n    },\n    addCheckType() {\n      this.width = '30%'\n      this.generateComponent('新增检查类型', 'TypeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType.Id)\n      })\n    },\n    editCheckType(data) {\n      this.width = '30%'\n      this.generateComponent('编辑检查类型', 'TypeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType.Id, data)\n      })\n    },\n    addCheckItem() {\n      this.width = '30%'\n      this.generateComponent('新增检查项', 'ItemDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType.Id)\n      })\n    },\n    editCheckItem(data) {\n      this.width = '30%'\n      this.generateComponent('编辑检查项', 'ItemDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType.Id, data)\n      })\n    },\n    addCheckCombination() {\n      this.width = '40%'\n      this.generateComponent('新增检查项组合', 'CombinationDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType)\n      })\n    },\n    editCheckCombination(data) {\n      this.width = '40%'\n      this.generateComponent('编辑检查项组合', 'CombinationDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType, data)\n      })\n    },\n    addCheckNode() {\n      this.width = '45%'\n      this.generateComponent('新增质检节点配置', 'NodeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType.Id)\n      })\n    },\n    editCheckNode(data) {\n      this.width = '45%'\n      this.generateComponent('编辑质检节点配置', 'NodeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType.Id, data)\n      })\n    },\n    addToleranceConfig(data) {\n      this.width = '45%'\n      this.generateComponent(data?.Id ? '编辑公差配置' : '新增公差配置', 'ToleranceDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init(data?.Id ? '编辑' : '新增', this.checkType.Id, data)\n      })\n    },\n    handleClose() {\n      switch (this.activeName) {\n        case '检查类型':\n          this.$refs.checkTypeRef.getCheckTypeList()\n          break\n        case '检查项':\n          this.$refs.checkItemRef.getCheckItemList()\n          break\n        case '检查项组合':\n          this.$refs.checkCombinationRef.getQualityList()\n          break\n        case '质检节点配置':\n          this.$refs.checkNodeRef.getNodeList()\n          break\n      }\n\n      this.dialogVisible = false\n    },\n    generateComponent(title, component) {\n      this.dialogTitle = title\n      this.currentComponent = component\n      this.dialogVisible = true\n    },\n    optionEdit(data) {\n      // this.dialogData = Object.assign({},data)\n      this.editCheckType(data)\n    },\n    ItemEdit(data) {\n      this.editCheckItem(data)\n    },\n    CombinationEdit(data) {\n      this.editCheckCombination(data)\n    },\n    NodeEdit(data) {\n      this.editCheckNode(data)\n    },\n    ToleranceRefresh(data) {\n      this.$refs.toleranceConfigRef.getToleranceList()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/mixin.scss\";\n@import \"~@/styles/variables.scss\";\n.wrapper-c {\n  background: #fff;\n  box-sizing: border-box;\n  // padding: 8px 16px 0;\n}\n.title {\n  width: 100%;\n  height: 48px;\n  padding: 0 16px;\n  background-color: white;\n\n  .index {\n    font-size: 16px;\n    line-height: 48px;\n    margin-right: 16px;\n    padding: 0 16px;\n    display: inline-block;\n    text-align: center;\n    color: #999999;\n  }\n  .clickindex {\n    border-bottom: 2px solid #298dff;\n    font-size: 16px;\n    line-height: 46px;\n    margin-right: 16px;\n    padding: 0 16px;\n    display: inline-block;\n    text-align: center;\n    color: #298dff;\n  }\n}\n::v-deep {\n  .el-tabs {\n    display: inline-block;\n  }\n  .el-tabs--card .el-tabs__header {\n    border: 0;\n    margin: 0;\n  }\n  .el-tabs--card > .el-tabs__header .el-tabs__nav {\n    border-bottom: 1px solid #dfe4ed;\n  }\n  .el-tabs__content {\n    margin-top: 16px !important;\n  }\n}\n.detail {\n  height: calc(100vh - 240px);\n  box-sizing: border-box;\n  padding: 16px;\n  border-top: 16px solid #f8f8f8;\n}\n.addbtn {\n  position: fixed;\n  right: 38px;\n  top: 210px;\n}\n.z-dialog {\n  ::v-deep {\n    .el-dialog__header {\n      background-color: #298dff;\n\n      .el-dialog__title,\n      .el-dialog__close {\n        color: #ffffff;\n      }\n    }\n\n    .el-dialog__body {\n      max-height: 700px;\n      overflow: auto;\n      @include scrollBar;\n\n      &::-webkit-scrollbar {\n        width: 8px;\n      }\n    }\n  }\n}\n</style>\n"]}]}
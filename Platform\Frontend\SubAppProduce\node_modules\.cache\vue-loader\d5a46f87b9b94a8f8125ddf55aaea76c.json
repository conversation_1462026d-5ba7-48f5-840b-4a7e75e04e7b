{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\index.vue", "mtime": 1757986401464}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup", "sourcesContent": ["<template>\n  <div>\n    <div class=\"app-container abs100\">\n      <div v-loading=\"pageLoading\" class=\"h100 wrapper-c parent\">\n        <div class=\"title\">\n          <span\n            v-for=\"(item, index) in title\"\n            :key=\"index\"\n            style=\"cursor: pointer\"\n            :class=\"spanCurr == index ? 'clickindex' : 'index'\"\n            @click=\"handelIndex(index, item)\"\n          >{{ item.Display_Name }}</span>\n        </div>\n        <div class=\"detail\">\n          <template>\n            <el-tabs\n              v-model=\"activeName\"\n              type=\"card\"\n              style=\"width: 100%; height: 100%\"\n            >\n              <el-tab-pane label=\"检查类型\" name=\"检查类型\">\n                <CheckType\n                  ref=\"checkTypeRef\"\n                  :check-type=\"checkType\"\n                  @optionFn=\"optionEdit\"\n                />\n              </el-tab-pane>\n              <el-tab-pane label=\"检查项\" name=\"检查项\">\n                <CheckItem\n                  ref=\"checkItemRef\"\n                  :check-type=\"checkType\"\n                  @ItemEdit=\"ItemEdit\"\n                />\n              </el-tab-pane>\n              <el-tab-pane label=\"检查项组合\" name=\"检查项组合\">\n                <CheckCombination\n                  ref=\"checkCombinationRef\"\n                  :check-type=\"checkType\"\n                  @CombinationEdit=\"CombinationEdit\"\n                />\n              </el-tab-pane>\n              <el-tab-pane label=\"质检节点配置\" name=\"质检节点配置\">\n                <CheckNode\n                  ref=\"checkNodeRef\"\n                  :check-type=\"checkType\"\n                  @NodeEdit=\"NodeEdit\"\n                />\n              </el-tab-pane>\n              <el-tab-pane v-if=\"isCom\" label=\"公差配置\" name=\"公差配置\">\n                <ToleranceConfig\n                  ref=\"toleranceConfigRef\"\n                  :check-type=\"checkType\"\n                  @edit=\"addToleranceConfig\"\n                />\n              </el-tab-pane>\n              <el-button\n                type=\"primary\"\n                class=\"addbtn\"\n                @click=\"addData\"\n              >新增</el-button>\n            </el-tabs>\n          </template>\n        </div>\n      </div>\n    </div>\n    <el-dialog\n      v-if=\"dialogVisible\"\n      ref=\"content\"\n      v-el-drag-dialog\n      :title=\"dialogTitle\"\n      :visible.sync=\"dialogVisible\"\n      :close-on-click-modal=\"false\"\n      :width=\"width\"\n      class=\"z-dialog\"\n      @close=\"handleClose\"\n    >\n      <component\n        :is=\"currentComponent\"\n        ref=\"content\"\n        :dialog-data=\"dialogData\"\n        @ToleranceRefresh=\"ToleranceRefresh\"\n        @refresh=\"handleRefresh\"\n        @close=\"handleClose\"\n      />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport CheckType from './components/CheckType' // 检查类型\nimport CheckCombination from './components/CheckCombination' // 检查项组合\nimport CheckNode from './components/CheckNode' // 质检节点配置\nimport CheckItem from './components/CheckItem' // 检查项\nimport TypeDialog from './components/Dialog/TypeDialog' // 检查类型弹窗\nimport ItemDialog from './components/Dialog/ItemDialog' // 检查项弹窗\nimport CombinationDialog from './components/Dialog/CombinationDialog' // 检查项组合弹窗\nimport NodeDialog from './components/Dialog/NodeDialog' // 质检节点组合弹窗\nimport ToleranceConfig from './components/ToleranceConfig.vue' // 公差配置\n// import { GetDictionaryDetailListByCode } from '@/api/PRO/factorycheck'\nimport elDragDialog from '@/directive/el-drag-dialog'\nimport ToleranceDialog from './components/Dialog/ToleranceDialog' // 公差配置弹窗\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nexport default {\n  name: 'PLMFactoryGroupList',\n  directives: { elDragDialog },\n  components: {\n    CheckType,\n    ToleranceConfig,\n    CheckCombination,\n    CheckNode,\n    CheckItem,\n    TypeDialog,\n    ItemDialog,\n    CombinationDialog,\n    NodeDialog,\n    ToleranceDialog\n  },\n  data() {\n    return {\n      spanCurr: 0,\n      title: [],\n      activeName: '检查类型',\n      checkType: {},\n      tbLoading: false,\n      tbData: [],\n      dialogVisible: false,\n      currentComponent: '',\n      dialogTitle: '',\n      isCom: false,\n      width: '60%',\n      dialogData: {},\n      pageLoading: false\n    }\n  },\n  created() {},\n  async mounted() {\n    this.getCheckType()\n  },\n  methods: {\n    async getCheckType() {\n      const bomLevel = await GetBOMInfo()\n      this.title = bomLevel.list\n      this.checkType = bomLevel.list[0]\n      this.isCom = bomLevel.list.find(v => v.Code === '-1')\n      // GetDictionaryDetailListByCode({ dictionaryCode: 'Quality_Code' }).then(\n      //   (res) => {\n      //     if (res.IsSucceed) {\n      //       this.title = res.Data // wtf\n      //       this.checkType = this.title[0]// wtf\n      //       this.isCom = res.Data.find(v => v.Value === '0')\n      //     } else {\n      //       this.$message({\n      //         type: 'error',\n      //         message: 'res.Message'\n      //       })\n      //     }\n      //   }\n      // )\n    },\n    handelIndex(index, item) {\n      this.pageLoading = true\n      this.isCom = item.Code === '-1'\n      if (!this.isCom && this.activeName === '公差配置') {\n        this.activeName = '检查类型'\n      }\n      this.checkType = item\n      this.spanCurr = index\n      setTimeout(() => {\n        this.pageLoading = false\n      }, 500)\n    },\n    addData() {\n      console.log(this.activeName)\n      switch (this.activeName) {\n        case '检查类型':\n          this.addCheckType()\n          break\n        case '检查项':\n          this.addCheckItem()\n          break\n        case '检查项组合':\n          this.addCheckCombination()\n          break\n        case '质检节点配置':\n          this.addCheckNode()\n          break\n        case '公差配置':\n          this.addToleranceConfig()\n          break\n        default:\n          this.addCheckType()\n      }\n    },\n    addCheckType() {\n      this.width = '30%'\n      this.generateComponent('新增检查类型', 'TypeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType)\n      })\n    },\n    editCheckType(data) {\n      this.width = '30%'\n      this.generateComponent('编辑检查类型', 'TypeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType, data)\n      })\n    },\n    addCheckItem() {\n      this.width = '30%'\n      this.generateComponent('新增检查项', 'ItemDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType)\n      })\n    },\n    editCheckItem(data) {\n      this.width = '30%'\n      this.generateComponent('编辑检查项', 'ItemDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType, data)\n      })\n    },\n    addCheckCombination() {\n      this.width = '40%'\n      this.generateComponent('新增检查项组合', 'CombinationDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType)\n      })\n    },\n    editCheckCombination(data) {\n      this.width = '40%'\n      this.generateComponent('编辑检查项组合', 'CombinationDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType, data)\n      })\n    },\n    addCheckNode() {\n      this.width = '45%'\n      this.generateComponent('新增质检节点配置', 'NodeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType)\n      })\n    },\n    editCheckNode(data) {\n      this.width = '45%'\n      this.generateComponent('编辑质检节点配置', 'NodeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType, data)\n      })\n    },\n    addToleranceConfig(data) {\n      this.width = '45%'\n      this.generateComponent(data?.Id ? '编辑公差配置' : '新增公差配置', 'ToleranceDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init(data?.Id ? '编辑' : '新增', this.checkType.Id, data)\n      })\n    },\n    handleRefresh() {\n      switch (this.activeName) {\n        case '检查类型':\n          this.$refs.checkTypeRef.getCheckTypeList()\n          break\n        case '检查项':\n          this.$refs.checkItemRef.getCheckItemList()\n          break\n        case '检查项组合':\n          this.$refs.checkCombinationRef.getQualityList()\n          break\n        case '质检节点配置':\n          this.$refs.checkNodeRef.getNodeList()\n          break\n      }\n    },\n    handleClose() {\n      this.dialogVisible = false\n    },\n    generateComponent(title, component) {\n      this.dialogTitle = title\n      this.currentComponent = component\n      this.dialogVisible = true\n    },\n    optionEdit(data) {\n      // this.dialogData = Object.assign({},data)\n      this.editCheckType(data)\n    },\n    ItemEdit(data) {\n      this.editCheckItem(data)\n    },\n    CombinationEdit(data) {\n      this.editCheckCombination(data)\n    },\n    NodeEdit(data) {\n      this.editCheckNode(data)\n    },\n    ToleranceRefresh(data) {\n      this.$refs.toleranceConfigRef.getToleranceList()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/mixin.scss\";\n@import \"~@/styles/variables.scss\";\n.wrapper-c {\n  background: #fff;\n  box-sizing: border-box;\n  // padding: 8px 16px 0;\n}\n.title {\n  width: 100%;\n  height: 48px;\n  padding: 0 16px;\n  background-color: #ffffff;\n\n  .index {\n    font-size: 16px;\n    line-height: 48px;\n    margin-right: 16px;\n    padding: 0 16px;\n    display: inline-block;\n    text-align: center;\n    color: #999999;\n  }\n  .clickindex {\n    border-bottom: 2px solid #298dff;\n    font-size: 16px;\n    line-height: 46px;\n    margin-right: 16px;\n    padding: 0 16px;\n    display: inline-block;\n    text-align: center;\n    color: #298dff;\n  }\n}\n::v-deep {\n  .el-tabs {\n    display: inline-block;\n  }\n  .el-tabs--card .el-tabs__header {\n    border: 0;\n    margin: 0;\n  }\n  .el-tabs--card > .el-tabs__header .el-tabs__nav {\n    border-bottom: 1px solid #dfe4ed;\n  }\n  .el-tabs__content {\n    margin-top: 16px !important;\n  }\n}\n.detail {\n  height: calc(100vh - 240px);\n  box-sizing: border-box;\n  padding: 16px;\n  border-top: 16px solid #f8f8f8;\n}\n.addbtn {\n  position: fixed;\n  right: 38px;\n  top: 210px;\n}\n.z-dialog {\n  ::v-deep {\n    .el-dialog__header {\n      background-color: #298dff;\n\n      .el-dialog__title,\n      .el-dialog__close {\n        color: #ffffff;\n      }\n    }\n\n    .el-dialog__body {\n      max-height: 700px;\n      overflow: auto;\n      @include scrollBar;\n\n      &::-webkit-scrollbar {\n        width: 8px;\n      }\n    }\n  }\n}\n</style>\n"]}]}
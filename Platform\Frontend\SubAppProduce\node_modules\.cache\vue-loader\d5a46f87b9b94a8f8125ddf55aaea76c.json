{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\index.vue", "mtime": 1757468112875}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"app-container abs100\">\r\n      <div class=\"h100 wrapper-c parent\">\r\n        <div class=\"title\">\r\n          <span\r\n            v-for=\"(item, index) in title\"\r\n            :key=\"index\"\r\n            style=\"cursor: pointer\"\r\n            :class=\"spanCurr == index ? 'clickindex' : 'index'\"\r\n            @click=\"handelIndex(index, item)\"\r\n          >{{ item.Display_Name }}</span>\r\n        </div>\r\n        <div class=\"detail\">\r\n          <template>\r\n            <el-tabs\r\n              v-model=\"activeName\"\r\n              type=\"card\"\r\n              style=\"width: 100%; height: 100%\"\r\n            >\r\n              <el-tab-pane label=\"检查类型\" name=\"检查类型\">\r\n                <CheckType\r\n                  ref=\"checkTypeRef\"\r\n                  :check-type=\"checkType\"\r\n                  @optionFn=\"optionEdit\"\r\n                />\r\n              </el-tab-pane>\r\n              <el-tab-pane label=\"检查项\" name=\"检查项\">\r\n                <CheckItem\r\n                  ref=\"checkItemRef\"\r\n                  :check-type=\"checkType\"\r\n                  @ItemEdit=\"ItemEdit\"\r\n                />\r\n              </el-tab-pane>\r\n              <el-tab-pane label=\"检查项组合\" name=\"检查项组合\">\r\n                <CheckCombination\r\n                  ref=\"checkCombinationRef\"\r\n                  :check-type=\"checkType\"\r\n                  @CombinationEdit=\"CombinationEdit\"\r\n                />\r\n              </el-tab-pane>\r\n              <el-tab-pane label=\"质检节点配置\" name=\"质检节点配置\">\r\n                <CheckNode\r\n                  ref=\"checkNodeRef\"\r\n                  :check-type=\"checkType\"\r\n                  @NodeEdit=\"NodeEdit\"\r\n                />\r\n              </el-tab-pane>\r\n              <el-tab-pane v-if=\"isCom\" label=\"公差配置\" name=\"公差配置\">\r\n                <ToleranceConfig\r\n                  ref=\"toleranceConfigRef\"\r\n                  :check-type=\"checkType\"\r\n                  @edit=\"addToleranceConfig\"\r\n                />\r\n              </el-tab-pane>\r\n              <el-button\r\n                type=\"primary\"\r\n                class=\"addbtn\"\r\n                @click=\"addData\"\r\n              >新增</el-button>\r\n            </el-tabs>\r\n          </template>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      ref=\"content\"\r\n      v-el-drag-dialog\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :close-on-click-modal=\"false\"\r\n      :width=\"width\"\r\n      class=\"z-dialog\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :dialog-data=\"dialogData\"\r\n        @ToleranceRefresh=\"ToleranceRefresh\"\r\n        @close=\"handleClose\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CheckType from './components/CheckType' // 检查类型\r\nimport CheckCombination from './components/CheckCombination' // 检查项组合\r\nimport CheckNode from './components/CheckNode' // 质检节点配置\r\nimport CheckItem from './components/CheckItem' // 检查项\r\nimport TypeDialog from './components/Dialog/TypeDialog' // 检查类型弹窗\r\nimport ItemDialog from './components/Dialog/ItemDialog' // 检查项弹窗\r\nimport CombinationDialog from './components/Dialog/CombinationDialog' // 检查项组合弹窗\r\nimport NodeDialog from './components/Dialog/NodeDialog' // 质检节点组合弹窗\r\nimport ToleranceConfig from './components/ToleranceConfig.vue' // 公差配置\r\n// import { GetDictionaryDetailListByCode } from '@/api/PRO/factorycheck'\r\nimport elDragDialog from '@/directive/el-drag-dialog'\r\nimport ToleranceDialog from './components/Dialog/ToleranceDialog' // 公差配置弹窗\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\nexport default {\r\n  name: 'PLMFactoryGroupList',\r\n  directives: { elDragDialog },\r\n  components: {\r\n    CheckType,\r\n    ToleranceConfig,\r\n    CheckCombination,\r\n    CheckNode,\r\n    CheckItem,\r\n    TypeDialog,\r\n    ItemDialog,\r\n    CombinationDialog,\r\n    NodeDialog,\r\n    ToleranceDialog\r\n  },\r\n  data() {\r\n    return {\r\n      spanCurr: 0,\r\n      title: [],\r\n      activeName: '检查类型',\r\n      checkType: {},\r\n      tbLoading: false,\r\n      tbData: [],\r\n      dialogVisible: false,\r\n      currentComponent: '',\r\n      dialogTitle: '',\r\n      isCom: false,\r\n      width: '60%',\r\n      dialogData: {}\r\n    }\r\n  },\r\n  created() {},\r\n  async mounted() {\r\n    this.getCheckType()\r\n  },\r\n  methods: {\r\n    async getCheckType() {\r\n      const bomLevel = await GetBOMInfo()\r\n      this.title = bomLevel.list\r\n      this.checkType = bomLevel.list[0]\r\n      this.isCom = bomLevel.list.find(v => v.Code === '-1')\r\n      // GetDictionaryDetailListByCode({ dictionaryCode: 'Quality_Code' }).then(\r\n      //   (res) => {\r\n      //     if (res.IsSucceed) {\r\n      //       this.title = res.Data // wtf\r\n      //       this.checkType = this.title[0]// wtf\r\n      //       this.isCom = res.Data.find(v => v.Value === '0')\r\n      //     } else {\r\n      //       this.$message({\r\n      //         type: 'error',\r\n      //         message: 'res.Message'\r\n      //       })\r\n      //     }\r\n      //   }\r\n      // )\r\n    },\r\n    handelIndex(index, item) {\r\n      this.isCom = item.Code === '-1'\r\n      if (!this.isCom && this.activeName === '公差配置') {\r\n        this.activeName = '检查类型'\r\n      }\r\n      this.checkType = item\r\n      this.spanCurr = index\r\n    },\r\n    addData() {\r\n      console.log(this.activeName)\r\n      switch (this.activeName) {\r\n        case '检查类型':\r\n          this.addCheckType()\r\n          break\r\n        case '检查项':\r\n          this.addCheckItem()\r\n          break\r\n        case '检查项组合':\r\n          this.addCheckCombination()\r\n          break\r\n        case '质检节点配置':\r\n          this.addCheckNode()\r\n          break\r\n        case '公差配置':\r\n          this.addToleranceConfig()\r\n          break\r\n        default:\r\n          this.addCheckType()\r\n          console.log('111')\r\n      }\r\n    },\r\n    addCheckType() {\r\n      this.width = '30%'\r\n      this.generateComponent('新增检查类型', 'TypeDialog')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init('新增', this.checkType)\r\n      })\r\n    },\r\n    editCheckType(data) {\r\n      this.width = '30%'\r\n      this.generateComponent('编辑检查类型', 'TypeDialog')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init('编辑', this.checkType, data)\r\n      })\r\n    },\r\n    addCheckItem() {\r\n      this.width = '30%'\r\n      this.generateComponent('新增检查项', 'ItemDialog')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init('新增', this.checkType)\r\n      })\r\n    },\r\n    editCheckItem(data) {\r\n      this.width = '30%'\r\n      this.generateComponent('编辑检查项', 'ItemDialog')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init('编辑', this.checkType, data)\r\n      })\r\n    },\r\n    addCheckCombination() {\r\n      this.width = '40%'\r\n      this.generateComponent('新增检查项组合', 'CombinationDialog')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init('新增', this.checkType)\r\n      })\r\n    },\r\n    editCheckCombination(data) {\r\n      this.width = '40%'\r\n      this.generateComponent('编辑检查项组合', 'CombinationDialog')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init('编辑', this.checkType, data)\r\n      })\r\n    },\r\n    addCheckNode() {\r\n      this.width = '45%'\r\n      this.generateComponent('新增质检节点配置', 'NodeDialog')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init('新增', this.checkType)\r\n      })\r\n    },\r\n    editCheckNode(data) {\r\n      this.width = '45%'\r\n      this.generateComponent('编辑质检节点配置', 'NodeDialog')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init('编辑', this.checkType, data)\r\n      })\r\n    },\r\n    addToleranceConfig(data) {\r\n      this.width = '45%'\r\n      this.generateComponent(data?.Id ? '编辑公差配置' : '新增公差配置', 'ToleranceDialog')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(data?.Id ? '编辑' : '新增', this.checkType.Id, data)\r\n      })\r\n    },\r\n    handleClose() {\r\n      switch (this.activeName) {\r\n        case '检查类型':\r\n          this.$refs.checkTypeRef.getCheckTypeList()\r\n          break\r\n        case '检查项':\r\n          this.$refs.checkItemRef.getCheckItemList()\r\n          break\r\n        case '检查项组合':\r\n          this.$refs.checkCombinationRef.getQualityList()\r\n          break\r\n        case '质检节点配置':\r\n          this.$refs.checkNodeRef.getNodeList()\r\n          break\r\n      }\r\n\r\n      this.dialogVisible = false\r\n    },\r\n    generateComponent(title, component) {\r\n      this.dialogTitle = title\r\n      this.currentComponent = component\r\n      this.dialogVisible = true\r\n    },\r\n    optionEdit(data) {\r\n      // this.dialogData = Object.assign({},data)\r\n      this.editCheckType(data)\r\n    },\r\n    ItemEdit(data) {\r\n      this.editCheckItem(data)\r\n    },\r\n    CombinationEdit(data) {\r\n      this.editCheckCombination(data)\r\n    },\r\n    NodeEdit(data) {\r\n      this.editCheckNode(data)\r\n    },\r\n    ToleranceRefresh(data) {\r\n      this.$refs.toleranceConfigRef.getToleranceList()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/styles/mixin.scss\";\r\n@import \"~@/styles/variables.scss\";\r\n.wrapper-c {\r\n  background: #fff;\r\n  box-sizing: border-box;\r\n  // padding: 8px 16px 0;\r\n}\r\n.title {\r\n  width: 100%;\r\n  height: 48px;\r\n  padding: 0 16px;\r\n  background-color: white;\r\n\r\n  .index {\r\n    font-size: 16px;\r\n    line-height: 48px;\r\n    margin-right: 16px;\r\n    padding: 0 16px;\r\n    display: inline-block;\r\n    text-align: center;\r\n    color: #999999;\r\n  }\r\n  .clickindex {\r\n    border-bottom: 2px solid #298dff;\r\n    font-size: 16px;\r\n    line-height: 46px;\r\n    margin-right: 16px;\r\n    padding: 0 16px;\r\n    display: inline-block;\r\n    text-align: center;\r\n    color: #298dff;\r\n  }\r\n}\r\n::v-deep {\r\n  .el-tabs {\r\n    display: inline-block;\r\n  }\r\n  .el-tabs--card .el-tabs__header {\r\n    border: 0;\r\n    margin: 0;\r\n  }\r\n  .el-tabs--card > .el-tabs__header .el-tabs__nav {\r\n    border-bottom: 1px solid #dfe4ed;\r\n  }\r\n  .el-tabs__content {\r\n    margin-top: 16px !important;\r\n  }\r\n}\r\n.detail {\r\n  height: calc(100vh - 240px);\r\n  box-sizing: border-box;\r\n  padding: 16px;\r\n  border-top: 16px solid #f8f8f8;\r\n}\r\n.addbtn {\r\n  position: fixed;\r\n  right: 38px;\r\n  top: 210px;\r\n}\r\n.z-dialog {\r\n  ::v-deep {\r\n    .el-dialog__header {\r\n      background-color: #298dff;\r\n\r\n      .el-dialog__title,\r\n      .el-dialog__close {\r\n        color: #ffffff;\r\n      }\r\n    }\r\n\r\n    .el-dialog__body {\r\n      max-height: 700px;\r\n      overflow: auto;\r\n      @include scrollBar;\r\n\r\n      &::-webkit-scrollbar {\r\n        width: 8px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
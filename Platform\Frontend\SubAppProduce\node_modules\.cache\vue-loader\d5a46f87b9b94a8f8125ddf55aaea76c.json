{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\index.vue", "mtime": 1756109219900}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup", "sourcesContent": ["<template>\n  <div>\n    <div class=\"app-container abs100\">\n      <div class=\"h100 wrapper-c parent\">\n        <div class=\"title\">\n          <span\n            v-for=\"(item, index) in title\"\n            :key=\"index\"\n            style=\"cursor: pointer\"\n            :class=\"spanCurr == index ? 'clickindex' : 'index'\"\n            @click=\"handelIndex(index, item)\"\n          >{{ item.Display_Name }}</span>\n        </div>\n        <div class=\"detail\">\n          <template>\n            <el-tabs\n              v-model=\"activeName\"\n              type=\"card\"\n              style=\"width: 100%; height: 100%\"\n            >\n              <el-tab-pane label=\"检查类型\" name=\"检查类型\">\n                <CheckType\n                  ref=\"checkTypeRef\"\n                  :check-type=\"checkType\"\n                  @optionFn=\"optionEdit\"\n                />\n              </el-tab-pane>\n              <el-tab-pane label=\"检查项\" name=\"检查项\">\n                <CheckItem\n                  ref=\"checkItemRef\"\n                  :check-type=\"checkType\"\n                  @ItemEdit=\"ItemEdit\"\n                />\n              </el-tab-pane>\n              <el-tab-pane label=\"检查项组合\" name=\"检查项组合\">\n                <CheckCombination\n                  ref=\"checkCombinationRef\"\n                  :check-type=\"checkType\"\n                  @CombinationEdit=\"CombinationEdit\"\n                />\n              </el-tab-pane>\n              <el-tab-pane label=\"质检节点配置\" name=\"质检节点配置\">\n                <CheckNode\n                  ref=\"checkNodeRef\"\n                  :check-type=\"checkType\"\n                  @NodeEdit=\"NodeEdit\"\n                />\n              </el-tab-pane>\n              <el-tab-pane v-if=\"isCom\" label=\"公差配置\" name=\"公差配置\">\n                <ToleranceConfig\n                  ref=\"toleranceConfigRef\"\n                  :check-type=\"checkType\"\n                  @edit=\"addToleranceConfig\"\n                />\n              </el-tab-pane>\n              <el-button\n                type=\"primary\"\n                class=\"addbtn\"\n                @click=\"addData\"\n              >新增</el-button>\n            </el-tabs>\n          </template>\n        </div>\n      </div>\n    </div>\n    <el-dialog\n      v-if=\"dialogVisible\"\n      ref=\"content\"\n      v-el-drag-dialog\n      :title=\"dialogTitle\"\n      :visible.sync=\"dialogVisible\"\n      :close-on-click-modal=\"false\"\n      :width=\"width\"\n      class=\"z-dialog\"\n      @close=\"handleClose\"\n    >\n      <component\n        :is=\"currentComponent\"\n        ref=\"content\"\n        :dialog-data=\"dialogData\"\n        @ToleranceRefresh=\"ToleranceRefresh\"\n        @close=\"handleClose\"\n      />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport CheckType from './components/CheckType' // 检查类型\nimport CheckCombination from './components/CheckCombination' // 检查项组合\nimport CheckNode from './components/CheckNode' // 质检节点配置\nimport CheckItem from './components/CheckItem' // 检查项\nimport TypeDialog from './components/Dialog/TypeDialog' // 检查类型弹窗\nimport ItemDialog from './components/Dialog/ItemDialog' // 检查项弹窗\nimport CombinationDialog from './components/Dialog/CombinationDialog' // 检查项组合弹窗\nimport NodeDialog from './components/Dialog/NodeDialog' // 质检节点组合弹窗\nimport ToleranceConfig from './components/ToleranceConfig.vue' // 公差配置\nimport { GetDictionaryDetailListByCode } from '@/api/PRO/factorycheck'\nimport elDragDialog from '@/directive/el-drag-dialog'\nimport ToleranceDialog from './components/Dialog/ToleranceDialog' // 公差配置弹窗\nexport default {\n  name: 'PLMFactoryGroupList',\n  directives: { elDragDialog },\n  components: {\n    CheckType,\n    ToleranceConfig,\n    CheckCombination,\n    CheckNode,\n    CheckItem,\n    TypeDialog,\n    ItemDialog,\n    CombinationDialog,\n    NodeDialog,\n    ToleranceDialog\n  },\n  data() {\n    return {\n      spanCurr: 0,\n      title: [],\n      activeName: '检查类型',\n      checkType: {},\n      tbLoading: false,\n      tbData: [],\n      dialogVisible: false,\n      currentComponent: '',\n      dialogTitle: '',\n      isCom: false,\n      width: '60%',\n      dialogData: {}\n    }\n  },\n  created() {},\n  async mounted() {\n    this.getCheckType()\n  },\n  methods: {\n    getCheckType() {\n      GetDictionaryDetailListByCode({ dictionaryCode: 'Quality_Code' }).then(\n        (res) => {\n          if (res.IsSucceed) {\n            this.title = res.Data // wtf\n            this.checkType = this.title[0]// wtf\n            this.isCom = res.Data.find(v => v.Value === '0')\n          } else {\n            this.$message({\n              type: 'error',\n              message: 'res.Message'\n            })\n          }\n        }\n      )\n    },\n    handelIndex(index, item) {\n      this.isCom = item.Value === '0'\n      console.log(index)\n      console.log(item)\n      if (!this.isCom && this.activeName === '公差配置') {\n        this.activeName = '检查类型'\n      }\n      this.checkType = item\n      this.spanCurr = index\n    },\n    addData() {\n      console.log(this.activeName)\n      switch (this.activeName) {\n        case '检查类型':\n          this.addCheckType()\n          break\n        case '检查项':\n          this.addCheckItem()\n          break\n        case '检查项组合':\n          this.addCheckCombination()\n          break\n        case '质检节点配置':\n          this.addCheckNode()\n          break\n        case '公差配置':\n          this.addToleranceConfig()\n          break\n        default:\n          this.addCheckType()\n          console.log('111')\n      }\n    },\n    addCheckType() {\n      this.width = '30%'\n      this.generateComponent('新增检查类型', 'TypeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType.Id)\n      })\n    },\n    editCheckType(data) {\n      this.width = '30%'\n      this.generateComponent('编辑检查类型', 'TypeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType.Id, data)\n      })\n    },\n    addCheckItem() {\n      this.width = '30%'\n      this.generateComponent('新增检查项', 'ItemDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType.Id)\n      })\n    },\n    editCheckItem(data) {\n      this.width = '30%'\n      this.generateComponent('编辑检查项', 'ItemDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType.Id, data)\n      })\n    },\n    addCheckCombination() {\n      this.width = '40%'\n      this.generateComponent('新增检查项组合', 'CombinationDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType)\n      })\n    },\n    editCheckCombination(data) {\n      this.width = '40%'\n      this.generateComponent('编辑检查项组合', 'CombinationDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType, data)\n      })\n    },\n    addCheckNode() {\n      this.width = '45%'\n      this.generateComponent('新增质检节点配置', 'NodeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType.Id)\n      })\n    },\n    editCheckNode(data) {\n      this.width = '45%'\n      this.generateComponent('编辑质检节点配置', 'NodeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType.Id, data)\n      })\n    },\n    addToleranceConfig(data) {\n      this.width = '45%'\n      this.generateComponent(data?.Id ? '编辑公差配置' : '新增公差配置', 'ToleranceDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init(data?.Id ? '编辑' : '新增', this.checkType.Id, data)\n      })\n    },\n    handleClose() {\n      switch (this.activeName) {\n        case '检查类型':\n          this.$refs.checkTypeRef.getCheckTypeList()\n          break\n        case '检查项':\n          this.$refs.checkItemRef.getCheckItemList()\n          break\n        case '检查项组合':\n          this.$refs.checkCombinationRef.getQualityList()\n          break\n        case '质检节点配置':\n          this.$refs.checkNodeRef.getNodeList()\n          break\n      }\n\n      this.dialogVisible = false\n    },\n    generateComponent(title, component) {\n      this.dialogTitle = title\n      this.currentComponent = component\n      this.dialogVisible = true\n    },\n    optionEdit(data) {\n      // this.dialogData = Object.assign({},data)\n      this.editCheckType(data)\n    },\n    ItemEdit(data) {\n      this.editCheckItem(data)\n    },\n    CombinationEdit(data) {\n      this.editCheckCombination(data)\n    },\n    NodeEdit(data) {\n      this.editCheckNode(data)\n    },\n    ToleranceRefresh(data) {\n      this.$refs.toleranceConfigRef.getToleranceList()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/mixin.scss\";\n@import \"~@/styles/variables.scss\";\n.wrapper-c {\n  background: #fff;\n  box-sizing: border-box;\n  // padding: 8px 16px 0;\n}\n.title {\n  width: 100%;\n  height: 48px;\n  padding: 0 16px;\n  background-color: white;\n\n  .index {\n    font-size: 16px;\n    line-height: 48px;\n    margin-right: 16px;\n    padding: 0 16px;\n    display: inline-block;\n    text-align: center;\n    color: #999999;\n  }\n  .clickindex {\n    border-bottom: 2px solid #298dff;\n    font-size: 16px;\n    line-height: 46px;\n    margin-right: 16px;\n    padding: 0 16px;\n    display: inline-block;\n    text-align: center;\n    color: #298dff;\n  }\n}\n::v-deep {\n  .el-tabs {\n    display: inline-block;\n  }\n  .el-tabs--card .el-tabs__header {\n    border: 0;\n    margin: 0;\n  }\n  .el-tabs--card > .el-tabs__header .el-tabs__nav {\n    border-bottom: 1px solid #dfe4ed;\n  }\n  .el-tabs__content {\n    margin-top: 16px !important;\n  }\n}\n.detail {\n  height: calc(100vh - 240px);\n  box-sizing: border-box;\n  padding: 16px;\n  border-top: 16px solid #f8f8f8;\n}\n.addbtn {\n  position: fixed;\n  right: 38px;\n  top: 210px;\n}\n.z-dialog {\n  ::v-deep {\n    .el-dialog__header {\n      background-color: #298dff;\n\n      .el-dialog__title,\n      .el-dialog__close {\n        color: #ffffff;\n      }\n    }\n\n    .el-dialog__body {\n      max-height: 700px;\n      overflow: auto;\n      @include scrollBar;\n\n      &::-webkit-scrollbar {\n        width: 8px;\n      }\n    }\n  }\n}\n</style>\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\api\\PRO\\car.js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\api\\PRO\\car.js", "mtime": 1758677034217}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "qs", "GetCarList", "data", "url", "method", "GetCurCarPageList", "GetCarPageList", "SaveCar", "DeleteCar", "ImportCar", "CarDataTemplate", "SaveBoat", "GetPageInfo", "DeleteBoat", "BoatDataTemplate", "ImportBoat"], "sources": ["D:/project/platform_framework_master/platform_framework/Platform/Frontend/SubAppProduce/src/api/PRO/car.js"], "sourcesContent": ["\r\n//  车辆信息API\r\nimport request from '@/utils/request'\r\nimport qs from 'qs'\r\n\r\n// 获取单据分页列表 (Auth)\r\nexport function GetCarList(data) {\r\n  return request({\r\n    url: '/PRO/Car/GetCarList',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 获取登录用户车辆信息分页列表 (Auth)\r\nexport function GetCurCarPageList(data) {\r\n  return request({\r\n    url: '/PRO/Car/GetCurCarPageList',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\nexport function GetCarPageList(data) {\r\n  return request({\r\n    url: '/PRO/Car/GetCarPageList',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\nexport function SaveCar(data) {\r\n  return request({\r\n    url: '/PRO/Car/SaveCar',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\nexport function DeleteCar(data) {\r\n  return request({\r\n    url: '/PRO/Car/DeleteCar',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\nexport function ImportCar(data) {\r\n  return request({\r\n    url: '/PRO/Car/ImportCar',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 车辆导入模板 (Auth)\r\nexport function CarDataTemplate(data) {\r\n  return request({\r\n    url: '/PRO/Car/CarDataTemplate',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 新增船舶\r\n\r\nexport function SaveBoat(data) {\r\n  return request({\r\n    url: '/PRO/Boat/SaveBoat',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n//船舶列表\r\nexport function GetPageInfo(data) {\r\n  return request({\r\n    url: '/PRO/Boat/GetPageInfo',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n//船舶删除\r\nexport function DeleteBoat(data) {\r\n  return request({\r\n    url: '/PRO/Boat/DeleteBoat',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n//船舶模板\r\nexport function BoatDataTemplate(data) {\r\n  return request({\r\n    url: '/PRO/Boat/BoatDataTemplate',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n//船舶导入\r\nexport function ImportBoat(data) {\r\n  return request({\r\n    url: '/PRO/Boat/ImportBoat',\r\n    method: 'post',\r\n    data\r\n  })\r\n}"], "mappings": "AACA;AACA,OAAOA,OAAO,MAAM,iBAAiB;AACrC,OAAOC,EAAE,MAAM,IAAI;;AAEnB;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,iBAAiBA,CAACH,IAAI,EAAE;EACtC,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASI,cAAcA,CAACJ,IAAI,EAAE;EACnC,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA,OAAO,SAASK,OAAOA,CAACL,IAAI,EAAE;EAC5B,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASM,SAASA,CAACN,IAAI,EAAE;EAC9B,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASO,SAASA,CAACP,IAAI,EAAE;EAC9B,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASQ,eAAeA,CAACR,IAAI,EAAE;EACpC,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;;AAEA,OAAO,SAASS,QAAQA,CAACT,IAAI,EAAE;EAC7B,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASU,WAAWA,CAACV,IAAI,EAAE;EAChC,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASW,UAAUA,CAACX,IAAI,EAAE;EAC/B,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASY,gBAAgBA,CAACZ,IAAI,EAAE;EACrC,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASa,UAAUA,CAACb,IAAI,EAAE;EAC/B,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ", "ignoreList": []}]}
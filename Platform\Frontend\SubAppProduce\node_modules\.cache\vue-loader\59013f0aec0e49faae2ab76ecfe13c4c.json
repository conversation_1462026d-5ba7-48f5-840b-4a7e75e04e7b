{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-product-type\\index.vue?vue&type=style&index=0&id=6ae1aa1c&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-product-type\\index.vue", "mtime": 1757468128035}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hcHAtY29udGFpbmVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAgbWluLXdpZHRoOiA5OThweDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCg0KICAudG9wLXggew0KICAgIGxpbmUtaGVpZ2h0OiA0OHB4Ow0KICAgIGhlaWdodDogNDhweDsNCiAgfQ0KICAuY2FyZC14LXRvcCB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIHBhZGRpbmc6IDE2cHggMTZweCAwIDE2cHg7DQogICAgYmFja2dyb3VuZC1jb2xvcjogI0ZGRkZGRjsNCiAgfQ0KDQogIC5jYXJkLXggew0KICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgLy8gYmFja2dyb3VuZC1jb2xvcjogI0ZGRkZGRjsNCiAgICBoZWlnaHQ6IDEwMCU7DQogICAgd2lkdGg6IDEwMCU7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIC5lbC10YWJzew0KICAgICAgd2lkdGg6IDEwMCU7DQogICAgICBwYWRkaW5nOiAxNnB4IDE2cHggMCAxNnB4Ow0KICAgICAgYmFja2dyb3VuZC1jb2xvcjogI0ZGRkZGRjsNCiAgICB9DQogICAgLmNhcmQteC1jb250ZW50ew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGZsZXg6IDE7DQogICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgIH0NCg0KICAgIC5yaWdodC1jYXJkIHsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgICAgZmxleDogMTsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNGRkZGRkY7DQogICAgICAuZWwtZm9ybXsNCiAgICAgICAgd2lkdGg6IDUwJTsNCiAgICAgICAgbWFyZ2luOiAgYXV0bzsNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuUA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/project-config/project-product-type", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <ProjectData />\r\n    <div class=\"card-x\">\r\n      <div class=\"card-x-top\">\r\n        <el-button type=\"primary\" @click=\"openAddDialog\">从项目添加</el-button>\r\n        <el-button type=\"primary\" @click=\"openCompanyDialog\">从公司添加</el-button>\r\n      </div>\r\n      <el-tabs v-model=\"activeType\" type=\"card\" @tab-click=\"handleClick\">\r\n        <el-tab-pane v-for=\"item in bomList\" :key=\"item.Code\" :label=\"item.Display_Name\" :name=\"item.Code\" />\r\n      </el-tabs>\r\n      <div class=\"card-x-content\">\r\n        <tree-data ref=\"tree\" :key=\"activeType\" :active-type=\"activeType\" :type-code=\"typeCode\" :type-id=\"typeId\" @nodeClick=\"nodeClick\" />\r\n        <div class=\"right-card\">\r\n          <el-form v-if=\"showForm\" ref=\"form\" :model=\"form\" label-width=\"120px\">\r\n            <el-form-item :label=\"`${levelName}大类名称：`\" prop=\"Name\">\r\n              <!-- <el-input v-model.trim=\"form.Name\" clearable maxlength=\"50\" /> -->\r\n              {{ form.Name }}\r\n            </el-form-item>\r\n            <el-form-item :label=\"`${levelName}大类编号：`\" prop=\"Code\">\r\n              <!-- <el-input v-model=\"form.Code\" disabled /> -->\r\n              {{ form.Code }}\r\n            </el-form-item>\r\n            <el-form-item label=\"生产周期：\" prop=\"Lead_Time\">\r\n              <!-- <el-input-number v-model.number=\"form.Lead_Time\" class=\"cs-number-btn-hidden w100\" clearable /> -->\r\n              {{ form.Lead_Time }}\r\n            </el-form-item>\r\n            <el-form-item v-if=\"showDirect\" label=\"直发件：\" prop=\"Is_Component\">\r\n              <!-- <el-radio-group v-model=\"form.Is_Component\">\r\n                  <el-radio :label=\"true\">否</el-radio>\r\n                  <el-radio :label=\"false\">是</el-radio>\r\n                </el-radio-group> -->\r\n              <el-tag :type=\"form.Is_Component ? 'danger' : 'success' \">\r\n                {{ form.Is_Component ? '否' : '是' }}\r\n              </el-tag>\r\n              <!-- {{ form.Is_Component }} -->\r\n            </el-form-item>\r\n            <!-- <el-form-item>\r\n              <el-button v-if=\"level<3\" type=\"text\" icon=\"el-icon-plus\" @click=\"addNext\">新增下一级</el-button>\r\n            </el-form-item>\r\n            <el-form-item>\r\n              <el-button type=\"primary\" :loading=\"submitLoading\" :disabled=\"isDefault\" @click=\"submit\">保存</el-button>\r\n              <el-button type=\"danger\" :loading=\"deleteLoading\" :disabled=\"hasChildrenNode || isDefault\" @click=\"handleDelete\">删除</el-button>\r\n            </el-form-item> -->\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      :title=\"title\"\r\n      class=\"plm-custom-dialog\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"width\"\r\n      top=\"5vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        v-if=\"dialogVisible\"\r\n        ref=\"content\"\r\n        :type-id=\"typeId\"\r\n        :add-level=\"addLevel\"\r\n        :parent-id=\"parentId\"\r\n        :active-type=\"activeType\"\r\n        :type-code=\"typeCode\"\r\n        :is-comp=\"isComp\"\r\n        :show-direct=\"showDirect\"\r\n        @close=\"handleClose\"\r\n        @getTreeList=\"getTreeData\"\r\n      />\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport TreeData from './component/TreeData'\r\nimport ProjectAdd from './component/ProjectAdd'\r\nimport { DeleteComponentType, GetComponentTypeEntity, SaveProBimComponentType } from '@/api/PRO/component-type'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\nimport { DeletePartType, GetPartTypeEntity, SavePartType } from '@/api/PRO/partType'\r\nimport { GetAllEntities } from '@/api/PRO/settings'\r\nimport ProjectData from '../components/ProjectData.vue'\r\nimport CompanyAdd from './component/CompanyAdd'\r\nexport default {\r\n  name: 'PROProjectProductType',\r\n  components: {\r\n    TreeData,\r\n    ProjectAdd,\r\n    ProjectData,\r\n    CompanyAdd\r\n  },\r\n  data() {\r\n    return {\r\n      bomList: [],\r\n      width: '30%',\r\n      typeCode: '',\r\n      typeId: '',\r\n      level: 1,\r\n      addLevel: undefined,\r\n      dialogVisible: false,\r\n      submitLoading: false,\r\n      deleteLoading: false,\r\n      showForm: false,\r\n      isDefault: false,\r\n      hasChildrenNode: true,\r\n      currentComponent: '',\r\n      activeType: '-1',\r\n      parentId: '',\r\n      title: '',\r\n      form: {\r\n        Name: '',\r\n        Code: '',\r\n        Is_Component: '',\r\n        Lead_Time: 0\r\n      },\r\n      // rules: {\r\n      //   Name: [\r\n      //     { required: true, message: '请输入名称', trigger: 'blur' }\r\n      //   ],\r\n      //   Code: [\r\n      //     { required: true, message: '请输入编码', trigger: 'blur' }\r\n      //   ],\r\n      //   Is_Component: [\r\n      //     { required: true, message: '请选择是否直发件', trigger: 'change' }\r\n      //   ],\r\n      //   Lead_Time: [\r\n      //     { required: true, message: '请输入周期', trigger: 'blur' }\r\n      //   ]\r\n      // },\r\n      Is_Component: ''\r\n    }\r\n  },\r\n  computed: {\r\n    levelName() {\r\n      return this.level === 1 ? '一级' : (this.level === 2 ? '二级' : (this.level === 3 ? '三级' : ''))\r\n    },\r\n    isComp() {\r\n      return this.activeType === '-1'\r\n    },\r\n    showDirect() {\r\n      return this.activeType !== '0'\r\n    }\r\n  },\r\n  async created() {\r\n    await this.getProfession()\r\n    const { list } = await GetBOMInfo()\r\n    this.bomList = list\r\n    // TreeData 组件会在自己的 mounted 中自动调用 fetchData()\r\n  },\r\n  methods: {\r\n    openAddDialog() {\r\n      this.currentComponent = 'ProjectAdd'\r\n      this.title = '添加'\r\n      this.dialogVisible = true\r\n      this.width = '80%'\r\n    },\r\n    openCompanyDialog() {\r\n      this.currentComponent = 'CompanyAdd'\r\n      this.title = '添加'\r\n      this.dialogVisible = true\r\n      this.width = '50%'\r\n    },\r\n    // addNext() {\r\n    //   this.currentComponent = 'Add'\r\n    //   this.addLevel = this.level + 1\r\n    //   this.title = `新增下一级`\r\n    //   this.parentId = this.form.Id\r\n    //   this.dialogVisible = true\r\n    // },\r\n    async getProfession() {\r\n      const res = await GetAllEntities({\r\n        companyId: localStorage.getItem('Last_Working_Object_Id'),\r\n        is_System: false\r\n      })\r\n      if (res.IsSucceed) {\r\n        const {\r\n          Code,\r\n          Id\r\n        } = res.Data?.Data?.find(item => item.Code === 'Steel') || {}\r\n        this.typeCode = Code\r\n        this.typeId = Id\r\n        console.log(this.typeCode, this.typeId)\r\n      }\r\n    },\r\n    // showRight(v) {\r\n    //   this.showForm = v\r\n    // },\r\n    handleClick(tab, event) {\r\n      this.showForm = false\r\n      console.log(tab, event)\r\n      // 由于使用了 key，组件会重新创建并在 mounted 中自动调用 fetchData()\r\n    },\r\n    submit() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (!valid) {\r\n          return false\r\n        }\r\n        if (this.Is_Component !== this.form.Is_Component) {\r\n          this.$confirm('直发件属性不会同步到已导入构件清单中，确认修改？', '提示', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n            this.submitConfirm()\r\n          }).catch(() => {\r\n            this.$message({\r\n              type: 'info',\r\n              message: '已取消修改'\r\n            })\r\n          })\r\n        } else {\r\n          this.submitConfirm()\r\n        }\r\n      })\r\n    },\r\n    submitConfirm() {\r\n      this.submitLoading = true\r\n      const submitObj = { ...this.form }\r\n      submitObj.Is_Direct = !submitObj.Is_Component\r\n      submitObj.Professional_Id = this.typeId\r\n      const postFn = this.isComp ? SaveProBimComponentType : SavePartType\r\n\r\n      postFn(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '修改成功',\r\n            type: 'success'\r\n          })\r\n          this.getTreeData()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.submitLoading = false\r\n      })\r\n    },\r\n    getTreeData() {\r\n      this.$refs['tree'].fetchData()\r\n    },\r\n\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    nodeClick(node) {\r\n      this.showForm = true\r\n      this.level = node.level\r\n      this.hasChildrenNode = node.childNodes.length > 0\r\n      this.getInfo(node.data.Id)\r\n    },\r\n    async getInfo(id) {\r\n      const postFn = this.isComp ? GetComponentTypeEntity : GetPartTypeEntity\r\n      const res = await postFn({ id })\r\n      if (res.IsSucceed) {\r\n        Object.assign(this.form, res.Data)\r\n        if (this.isComp) {\r\n          this.isDefault = false\r\n          this.Is_Component = res.Data.Is_Component\r\n        } else {\r\n          this.isDefault = !!res.Data.Is_Default\r\n          this.form.Is_Component = !res.Data.Is_Direct\r\n        }\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    handleDelete() {\r\n      this.$confirm('是否删除当前类别?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async() => {\r\n        this.deleteLoading = true\r\n        let postFn\r\n        let obj = {}\r\n        if (this.isComp) {\r\n          postFn = DeleteComponentType\r\n          obj = {\r\n            ids: this.form.Id\r\n          }\r\n        } else {\r\n          postFn = DeletePartType\r\n          obj = {\r\n            id: this.form.Id\r\n          }\r\n        }\r\n        postFn(obj).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '删除成功!'\r\n            })\r\n            this.getTreeData()\r\n            this.$refs['tree'].resetKey(this.form.Id)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        }).finally(_ => {\r\n          this.deleteLoading = false\r\n          this.showForm = false\r\n        })\r\n      }).catch((e) => {\r\n        console.log(e, 3313)\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消删除'\r\n        })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.app-container {\r\n  display: flex;\r\n  min-width: 998px;\r\n  overflow: hidden;\r\n\r\n  .top-x {\r\n    line-height: 48px;\r\n    height: 48px;\r\n  }\r\n  .card-x-top {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 16px 16px 0 16px;\r\n    background-color: #FFFFFF;\r\n  }\r\n\r\n  .card-x {\r\n    overflow: hidden;\r\n    // background-color: #FFFFFF;\r\n    height: 100%;\r\n    width: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    .el-tabs{\r\n      width: 100%;\r\n      padding: 16px 16px 0 16px;\r\n      background-color: #FFFFFF;\r\n    }\r\n    .card-x-content{\r\n      display: flex;\r\n      flex: 1;\r\n      overflow: hidden;\r\n    }\r\n\r\n    .right-card {\r\n      display: flex;\r\n      flex-direction: column;\r\n      flex: 1;\r\n      border-radius: 4px;\r\n      background-color: #FFFFFF;\r\n      .el-form{\r\n        width: 50%;\r\n        margin:  auto;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
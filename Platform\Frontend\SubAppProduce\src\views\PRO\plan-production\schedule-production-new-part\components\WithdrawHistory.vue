<template>
  <div class="wrapper">
    <div class="h100">
      <vxe-table
        :empty-render="{name: 'NotData'}"
        show-header-overflow
        empty-text="暂无数据"
        height="auto"
        show-overflow
        :row-config="{isCurrent: true, isHover: true}"
        :loading="tbLoading"
        class="cs-vxe-table"
        align="left"
        stripe
        :data="tableData"
        resizable
        :tooltip-config="{ enterable: true }"
      >
        <vxe-column
          align="left"
          sortable
          min-width="150"
          :field="isCom ? 'Comp_Code' : 'Part_Code'"
          :title="`${partName}名称`"
        />
        <vxe-column
          v-if="!isCom"
          align="left"
          sortable
          min-width="120"
          field="Comp_Code"
          :title="`所属${comName}`"
        />
        <vxe-column
          align="left"
          sortable
          min-width="120"
          field="Spec"
          title="规格"
        />
        <vxe-column
          align="left"
          sortable
          min-width="120"
          field="Texture"
          title="材质"
        />
        <vxe-column
          align="center"
          sortable
          min-width="120"
          field="Length"
          title="长度"
        />

        <vxe-column
          align="center"
          sortable
          min-width="80"
          field="Cancel_Count"
          title="数量"
        />
        <vxe-column
          align="center"
          sortable
          min-width="120"
          field="Weight"
          title="单重(kg)"
        />
        <vxe-column
          align="center"
          sortable
          min-width="150"
          field="Cancel_Date"
          title="撤回时间"
        >
          <template #default="{ row }">
            {{ row.Cancel_Date | timeFormat("{y}-{m}-{d} {h}:{i}:{s}") }}
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <footer>
      <el-button @click="$emit('close')">取 消</el-button>
    </footer>
  </div>
</template>

<script>
import { GetSchdulingCancelHistory, GetPartSchdulingCancelHistory } from '@/api/PRO/production-task'

export default {
  props: {
    partName: {
      type: String,
      default: ''
    },
    comName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableData: [],
      tbLoading: false
    }
  },
  computed: {
    isCom() {
      return this.pageType === 'com'
    }
  },
  inject: ['pageType'],
  methods: {
    init(row) {
      this.row = row
      this.fetchData()
    },
    fetchData() {
      this.tbLoading = true
      let requestFn = null
      requestFn = this.isCom ? GetSchdulingCancelHistory : GetPartSchdulingCancelHistory
      requestFn({
        schdulingCode: this.row.Schduling_Code
      }).then(res => {
        if (res.IsSucceed) {
          this.tableData = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      }).finally(_ => {
        this.tbLoading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
.wrapper {
  height: 60vh;
  display: flex;
  flex-direction: column;

  footer {
    margin: 10px;
    text-align: right;
  }
}
</style>

{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckCombination.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckCombination.vue", "mtime": 1758086053963}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["CheckCombination.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CheckCombination.vue", "sourceRoot": "src/views/PRO/project-config/project-quality/components", "sourcesContent": ["<template>\n  <div style=\"height: calc(100vh - 300px)\">\n    <vxe-table\n      v-loading=\"tbLoading\"\n      :empty-render=\"{name: 'NotData'}\"\n      show-header-overflow\n      element-loading-spinner=\"el-icon-loading\"\n      element-loading-text=\"拼命加载中\"\n      empty-text=\"暂无数据\"\n      height=\"100%\"\n      :data=\"tbData\"\n      stripe\n      resizable\n      :auto-resize=\"true\"\n      class=\"cs-vxe-table\"\n      :tooltip-config=\"{ enterable: true }\"\n    >\n      <!-- <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" /> -->\n      <vxe-column\n        v-for=\"(item, index) in columns\"\n        :key=\"index\"\n        :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n        show-overflow=\"tooltip\"\n        sortable\n        :align=\"item.Align\"\n        :field=\"item.Code\"\n        :title=\"item.Display_Name\"\n      >\n        <template #default=\"{ row }\">\n          <span>{{ row[item.Code] || '-' }}</span>\n        </template>\n      </vxe-column>\n      <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" align=\"center\" show-overflow>\n        <template #default=\"{ row }\">\n          <el-button type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\n          <el-divider direction=\"vertical\" />\n          <el-button type=\"text\" @click=\"removeEvent(row)\">删除</el-button>\n        </template>\n      </vxe-column>\n    </vxe-table>\n  </div>\n</template>\n\n<script>\nimport { GetGridByCode } from '@/api/sys'\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\nimport { QualityList } from '@/api/PRO/factorycheck'\nimport { DelQualityList } from '@/api/PRO/factorycheck'\nimport { timeFormat } from '@/filters'\nexport default {\n  props: {\n    checkType: {\n      type: Object,\n      default: () => ({})\n    },\n    sysProjectId: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      columns: null,\n      tbLoading: false,\n      TypeId: '',\n      typeOption: '',\n      tbData: []\n\n    }\n  },\n  watch: {\n    checkType: {\n      handler(newName, oldName) {\n        this.checkType = newName\n        if (this.sysProjectId) {\n          this.getQualityList()\n        }\n      },\n      deep: true\n    },\n    sysProjectId: {\n      handler(newVal) {\n        if (newVal && this.checkType && this.checkType.Id) {\n          this.getQualityList()\n        }\n      },\n      immediate: true\n    }\n  },\n  mounted() {\n    // this.getQualityList()\n    this.getTypeList()\n  },\n  methods: {\n    async getTypeList() {\n      let res = null\n      let data = null\n      res = await GetFactoryProfessionalByCode({\n        factoryId: localStorage.getItem('CurReferenceId')\n      })\n      data = res.Data\n      if (res.IsSucceed) {\n        this.typeOption = Object.freeze(data)\n        console.log(this.typeOption)\n        if (this.typeOption.length > 0) {\n          this.TypeId = this.typeOption[0]?.Id\n          this.fetchData()\n        }\n      } else {\n        this.$message({\n          message: res.Message,\n          type: 'error'\n        })\n      }\n    },\n    async fetchData() {\n      await this.getTableConfig('Check_item_combination')\n    //   this.tbLoading = true;\n    },\n    // 获取列表\n    getTableConfig(code) {\n      GetGridByCode({ code: code + ',' + this.typeOption.find((i) => i.Id === this.TypeId).Code }).then((res) => {\n        const { IsSucceed, Data, Message } = res\n        if (IsSucceed) {\n          if (!Data) {\n            this.$message.error('当前专业没有配置相对应表格')\n            this.tbLoading = true\n            return\n          }\n          this.tbLoading = false\n          const list = Data.ColumnList || []\n          this.columns = list.filter((v) => v.Is_Display).map((item) => {\n            if (item.Code === 'CheckName') {\n              item.fixed = 'left'\n            }\n            return item\n          })\n          console.log(this.columns)\n        } else {\n          this.$message({\n            message: Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    // 获取检查项组合列表\n    getQualityList() {\n      this.tbLoading = true\n      QualityList({ check_object_id: this.checkType.Id, Bom_Level: this.checkType.Code, sysProjectId: this.sysProjectId }).then((res) => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data.map((v) => {\n            switch (v.Check_Type) {\n              case 1 : v.Check_Type = '质量'; break\n              case 2 : v.Check_Type = '探伤'; break\n              case -1 : v.Check_Type = '质量、探伤'; break\n              default: v.Check_Type = ''\n            }\n            v.Create_Date = timeFormat(v.Create_Date, '{y}-{m}-{d} {h}:{i}:{s}')\n            return v\n          })\n          this.tbLoading = false\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n          this.tbLoading = false\n        }\n      })\n    },\n    // 删除单个检查项组合\n    removeEvent(row) {\n      console.log(row)\n      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DelQualityList({ id: row.Id }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getQualityList()\n            } else {\n              this.$message({\n                type: 'error',\n                message: res.Message\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    },\n    // 编辑每行信息\n    editEvent(row) {\n      // 获取每行内容\n      console.log('row', row)\n      this.$emit('CombinationEdit', row)\n    }\n  }\n}\n</script>\n\n<style scoped></style>\n"]}]}
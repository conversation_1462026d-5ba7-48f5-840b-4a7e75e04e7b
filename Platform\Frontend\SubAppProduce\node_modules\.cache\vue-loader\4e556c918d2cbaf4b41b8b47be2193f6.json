{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\template-print\\detail.vue?vue&type=style&index=0&id=0e6d778d&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\shipment\\template-print\\detail.vue", "mtime": 1757468128158}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi50aXRsZXsKICBtaW4td2lkdGg6IDEwMCU7CiAgcGFkZGluZy1sZWZ0OiA4cHg7CiAgZm9udC1zaXplOiAxOHB4OwogIG1hcmdpbi10b3A6IDIwcHg7Cn0KOjotd2Via2l0LXNjcm9sbGJhciB7CiAgd2lkdGg6IDBweDsKICBoZWlnaHQ6IDhweDsKfQo6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgLy8gYm94LXNoYWRvdyAgIDogaW5zZXQgMCAwIDVweCByZ2JhKDAsIDAsIDAsIDAuMik7CiAgYmFja2dyb3VuZDogI2RkZDsKfQo6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHsKICBib3gtc2hhZG93OiBpbnNldCAwIDAgMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgYmFja2dyb3VuZDogI2VkZWRlZDsKfQouaXRlbSB7CiAgY29sb3I6IHJnYmEoMzQsNDAsNTIsMC42NSk7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIG1hcmdpbjogMTBweCAxMHB4OwogIHdpZHRoOiAxMTJweDsKICBoZWlnaHQ6IDQ0cHg7CiAgYm9yZGVyLXJhZGl1czogNHB4IDRweCA0cHggNHB4OwogIGJvcmRlcjogMXB4IGRhc2hlZCAjRDBEM0RCOwogIGZvbnQtc2l6ZTogMTRweDsKfQoubGFiZWx7CiAgbWFyZ2luOiAwIDVweCAwIDEwcHg7Cn0KLyo6OnYtZGVlcHsqLwovKiAgICAuaGlwcmludC1vcHRpb24taXRlbS1zZXR0aW5nQnRueyovCi8qICAgICAgICBiYWNrZ3JvdW5kOiAjMGJhMWY4OyovCi8qICAgIH0qLwovKn0qLwouaGVhZGVyewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC13cmFwOiB3cmFwOwogIHJvdy1nYXA6IDEwcHg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBtYXJnaW4tYm90dG9tOiAxMHB4OwogIGNvbG9yOiByZ2JhKDM0LCA0MCwgNTIsIDAuODUpOwogIGZvbnQtc2l6ZTogMTJweDsKICAuaW5wdXR7CiAgICB3aWR0aDogMTAwcHg7CiAgfQogIC56b29tewogICAgbWFyZ2luOiAwIDEwcHg7CiAgICBib3JkZXI6IDFweCBzb2xpZCAjRDBEM0RCOwogICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgd2lkdGg6IDEwMHB4OwogICAgaGVpZ2h0OiAzMnB4OwogICAgZGlzcGxheTogZmxleDsKICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIGZvbnQtc2l6ZTogMTRweDsKICB9CiAgLnpvb20tYnRuewogICAgcGFkZGluZzogNXB4OwogICAgZm9udC1zaXplOiAxOXB4OwogIH0KfQo6OnYtZGVlcHsKICAuaGlubm4tbGF5b3V0LXNpZGVyewogICAgKnsKICAgICAgY29sb3I6IHJnYmEoMzQsIDQwLCA1MiwgMC44NSk7CiAgICB9CiAgICBpbnB1dFtwbGFjZWhvbGRlcj0i6K+36L6T5YWl5Zu+54mH5Zyw5Z2AIl0gIHsKICAgICAgd2lkdGg6IDEwMCUhaW1wb3J0YW50OwogICAgICAmICsgYnV0dG9uewogICAgICAgIGRpc3BsYXk6IG5vbmU7CiAgICAgIH0KICAgIH0KICAgIGlucHV0LHRleHRhcmVhLHNlbGVjdHsKICAgICAgYm9yZGVyLXJhZGl1czogNHB4IWltcG9ydGFudDsKICAgICAgYm9yZGVyOiAxcHggc29saWQgI0QwRDNEQiFpbXBvcnRhbnQ7CiAgICB9CiAgICBpbnB1dCxzZWxlY3R7CiAgICAgIGhlaWdodDogMzJweCFpbXBvcnRhbnQ7CiAgICAgIGxpbmUtaGVpZ2h0OiAzMnB4OwogICAgfQogICAgLmhpcHJpbnQtb3B0aW9uLWl0ZW0tc2V0dGluZ0J0bnsKICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzI5OERGRjsKICAgICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgICBoZWlnaHQ6IDMwcHg7CiAgICAgIGNvbG9yOiAjZmZmZmZmOwogICAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICB9CiAgICAuaGlwcmludC1vcHRpb24taXRlbS1kZWxldGVCdG57CiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNGQjZCN0Y7CiAgICB9CiAgfQoKfQoudG1wbC1saXN0IHsKICBtYXJnaW4tdG9wOiAxMnB4OwogIG92ZXJmbG93LXk6IGF1dG87CiAgbWluLWhlaWdodDogMTMwcHg7CiAgLnRtcGwtbWVudSB7CiAgICBib3JkZXItcmlnaHQ6IG5vbmU7CiAgICAuZWwtbWVudS1pdGVtIHsKICAgICAgaGVpZ2h0OiAzMnB4OwogICAgICBsaW5lLWhlaWdodDogMzJweDsKICAgICAgLmVsLWxpbmsgewogICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgICAgICB0b3A6IDIwJTsKICAgICAgICByaWdodDogMTJweDsKICAgICAgICBtYXJnaW4tdG9wOiAtN3B4OwogICAgICAgIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjNzOwogICAgICAgICY6bGFzdC1jaGlsZCB7CiAgICAgICAgICByaWdodDogMzZweDsKICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/PRO/shipment/template-print", "sourcesContent": ["<template>\r\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\r\n    <div style=\"display:flex;height:100%;\">\r\n      <el-aside\r\n        class=\"cs-z-page-main-content\"\r\n        style=\"background:#FFF;margin-right:16px;width: 20vw;min-width:320px;\"\r\n      >\r\n        <el-row :gutter=\"4\" style=\"flex-shrink:0;\">\r\n          <el-col :span=\"17\">\r\n            <el-input\r\n              v-model=\"keyword\"\r\n              placeholder=\"请输入内容\"\r\n              suffix-icon=\"el-icon-search\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"7\">\r\n            <el-button type=\"primary\" @click=\"createTemplate\">新建模板</el-button>\r\n          </el-col>\r\n        </el-row>\r\n        <div class=\"tmpl-list\">\r\n          <el-menu\r\n            class=\"tmpl-menu\"\r\n            :default-active=\"String(activeIndex)\"\r\n          >\r\n            <el-menu-item\r\n              v-for=\"tmpl in filteredTmplList\"\r\n              :key=\"tmpl.Id\"\r\n              :index=\"tmpl.Id\"\r\n              style=\"padding-left:12px;\"\r\n              :title=\"tmpl.Name\"\r\n            >\r\n              <div\r\n                style=\"overflow:hidden;max-width:220px;text-overflow: ellipsis;\"\r\n                @click.stop=\"tmplSelect(tmpl.Id)\"\r\n              >\r\n                <i class=\"el-icon-document\" />{{ tmpl.Name }}\r\n              </div>\r\n              <template v-if=\"String(activeIndex) === tmpl.Id\">\r\n                <!--                <el-link-->\r\n                <!--                  :underline=\"false\"-->\r\n                <!--                  type=\"primary\"-->\r\n                <!--                  @click.stop=\"toEdit = tmpl.Id\"-->\r\n                <!--                >-->\r\n                <!--                  <i class=\"right-align-icon el-icon-edit\" />-->\r\n                <!--                </el-link>-->\r\n                <el-link\r\n                  :underline=\"false\"\r\n                  type=\"danger\"\r\n                  @click=\"deleteTemplate(tmpl.Id)\"\r\n                >\r\n                  <i class=\"right-align-icon el-icon-delete\" />\r\n                </el-link>\r\n                <el-link\r\n                  :underline=\"false\"\r\n                  type=\"primary\"\r\n                  @click=\"cloneTemplate(tmpl.Id)\"\r\n                >\r\n                  <i class=\"right-align-icon el-icon-copy-document\" />\r\n                </el-link>\r\n              </template>\r\n            </el-menu-item>\r\n          </el-menu>\r\n        </div>\r\n        <div class=\"flex-row justify-center flex-wrap\" style=\"display: flex;flex-wrap: wrap\">\r\n          <!-- tid 与 defaultElementTypeProvider 中对应 -->\r\n          <!-- 包含 class=\"ep-draggable-item\" -->\r\n          <div class=\"title\">标题区</div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Logo\">\r\n            Logo图片\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.QrcodeText\">\r\n            二维码\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.ContractNumber\">\r\n            内部合同编号\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Code\">\r\n            单据号\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.SendDate\">\r\n            日期\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Number\">\r\n            发货序号\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Address\">\r\n            项目地址\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.ReceivingUnit\">\r\n            收货单位\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.ProjectName\">\r\n            项目名称\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.MakerName\">\r\n            出库人\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Consignee\">\r\n            收货人\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.ConsigneeTel\">\r\n            联系电话\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.VehicleNo\">\r\n            车牌\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Telephone\">\r\n            司机电话\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Trips\">\r\n            车次\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.LoadingsName\">\r\n            装车班\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.LoadingsPersonnelName\">\r\n            装车班人员\r\n          </div>\r\n          <div class=\"title\">数据区</div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Table\">\r\n            构件/包数据表\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Pound_Weight\">\r\n            磅重（kg）\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Tare_Weight\">\r\n            皮重（kg）\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.Net_Weight\">\r\n            净重（kg）\r\n          </div>\r\n          <div class=\"title\">其他</div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.customText\">\r\n            自定义文本\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.hline\">\r\n            横线\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.vline\">\r\n            竖线\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.rect\">\r\n            矩形\r\n          </div>\r\n          <div class=\"ep-draggable-item item\" tid=\"Shipment.oval\">\r\n            椭圆\r\n          </div>\r\n        </div>\r\n      </el-aside>\r\n      <el-container class=\"cs-z-page-main-content\">\r\n        <div class=\"header\">\r\n          <el-button type=\"primary\" sizi=\"mini\" @click=\"handlePrint\">打印预览</el-button>\r\n          <el-button type=\"success\" sizi=\"mini\" :loading=\"saveLoading\" @click=\"saveTemplate\">保存模板</el-button>\r\n          <el-button type=\"danger\" sizi=\"mini\" @click=\"clearTemplate\">清空</el-button>\r\n          <span class=\"label\">模板名称</span>\r\n          <el-input v-model=\"form.Name\" style=\"width: 150px\" :maxlength=\"50\" />\r\n          <span class=\"label\">模板布局</span>\r\n          <el-select v-model=\"curPaperType\" style=\"width: 120px\" @change=\"changePaper\">\r\n            <el-option v-for=\"item in paperTypes\" :key=\"item.type\" :value=\"item.type\" :label=\"item.type\" />\r\n          </el-select>\r\n          <div v-if=\"curPaperType==='自定义纸张'\">\r\n            <span class=\"label\">宽</span>\r\n            <el-input v-model=\"paperWidth\" type=\"input\" class=\"input\" @change=\"changePaper\" />\r\n            <span class=\"label\">高</span>\r\n            <el-input v-model=\"paperHeight\" type=\"input\" class=\"input\" @change=\"changePaper\" />\r\n          </div>\r\n          <div style=\"display: flex;align-items: center;margin-left: 10px\">\r\n            <i class=\"el-icon-zoom-out zoom-btn\" @click=\"changeScale(false)\" />\r\n            <div class=\"zoom\">{{ ~~(scaleValue * 100) }}%</div>\r\n            <i class=\"el-icon-zoom-in zoom-btn\" @click=\"changeScale(true)\" />\r\n          </div>\r\n        </div>\r\n        <!-- 设计器的 容器 -->\r\n        <div style=\"margin-top: 10px;display: flex\">\r\n          <div style=\"flex:1;padding-left: 16px;padding-top: 16px;overflow: auto\">\r\n            <div id=\"hiprint-printTemplate\" />\r\n          </div>\r\n          <div class=\"hinnn-layout-sider\" style=\"width: 20vw;min-width: 300px;margin-left: 16px\">\r\n            <div id=\"PrintElementOptionSetting\" />\r\n          </div>\r\n        </div>\r\n      </el-container>\r\n    </div>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { hiprint, hiPrintPlugin } from 'vue-plugin-hiprint'\r\nimport providers from './providers'\r\nimport {\r\n  DeletePrintTemplate,\r\n  GetPrintTemplateEntity,\r\n  GetPrintTemplateList,\r\n  SavePrintTemplateEntity\r\n} from '@/api/PRO/shipment/ship-template-print'\r\nimport { paperTypes } from './config'\r\nimport { GetPreferenceSettingValue } from '@/api/sys/system-setting'\r\nimport html2canvas from 'html2canvas'\r\nimport { GetCompany } from '@/api/plm/site'\r\nimport { deepClone } from '@/utils'\r\n\r\nhiPrintPlugin.disAutoConnect()\r\n\r\nlet hiprintTemplate\r\nexport default {\r\n  name: 'ShipTemplatePrintDetail',\r\n  data() {\r\n    return {\r\n      // 当前纸张\r\n      curPaper: {\r\n        type: 'A4',\r\n        width: 210,\r\n        height: 296.6\r\n      },\r\n      curPaperType: 'A4',\r\n      // 纸张类型\r\n      paperTypes: deepClone(paperTypes),\r\n      // 自定义纸张\r\n      paperWidth: '220',\r\n      paperHeight: '80',\r\n      tmplList: [],\r\n      mode: 1,\r\n      activeIndex: '',\r\n      keyword: '',\r\n      toEdit: '',\r\n      form: {\r\n        Name: '',\r\n        Type: 1, // 1-发货单\r\n        Data: '',\r\n        Base64Image: ''\r\n      },\r\n      logoUrl: require('@/assets/logo-inner.png'),\r\n      // 缩放\r\n      scaleValue: 1,\r\n      saveLoading: false,\r\n      scaleMax: 5,\r\n      scaleMin: 0.5\r\n    }\r\n  },\r\n  computed: {\r\n    filteredTmplList() {\r\n      return this.tmplList.filter(t => t.Name.indexOf(this.keyword) > -1)\r\n    }\r\n  },\r\n  mounted() {\r\n    this.init()\r\n    /**\r\n       * 这里必须要在 mounted 中去构建 左侧可拖拽元素 或者 设计器\r\n       * 因为都是把元素挂载到对应容器中, 必须要先找到该容器\r\n       */\r\n    this.buildLeftElement()\r\n    this.buildDesigner()\r\n  },\r\n  methods: {\r\n    changeScale(big) {\r\n      let scaleValue = this.scaleValue\r\n      if (big) {\r\n        scaleValue += 0.1\r\n        if (scaleValue > this.scaleMax) scaleValue = 5\r\n      } else {\r\n        scaleValue -= 0.1\r\n        if (scaleValue < this.scaleMin) scaleValue = 0.5\r\n      }\r\n      if (hiprintTemplate) {\r\n        // scaleValue: 放大缩小值, false: 不保存(不传也一样), 如果传 true, 打印时也会放大\r\n        hiprintTemplate.zoom(scaleValue)\r\n        this.scaleValue = scaleValue\r\n      }\r\n    },\r\n    async getImage() {\r\n      try {\r\n        return await new Promise((resolve, reject) => {\r\n          // 在浏览器空闲时执行截图操作\r\n          const executeCapture = async() => {\r\n            try {\r\n              const canvas = await html2canvas(document.getElementById('hiprint-printTemplate'), {\r\n                useCORS: true,\r\n                logging: false, // 关闭日志输出\r\n                removeContainer: true, // 自动清理临时容器\r\n                onclone: (clonedDoc) => {\r\n                  // 在克隆文档中移除不必要的元素\r\n                  const clonedElement = clonedDoc.getElementById('hiprint-printTemplate')\r\n                  if (clonedElement) {\r\n                    // 移除动画、视频等耗性能的元素\r\n                    const animations = clonedElement.querySelectorAll('[class*=\"animate\"]')\r\n                    animations.forEach(el => el.remove())\r\n                  }\r\n                }\r\n              })\r\n              const dataUrl = canvas.toDataURL('image/png')\r\n              resolve(dataUrl)\r\n            } catch (error) {\r\n              reject(error)\r\n            }\r\n          }\r\n\r\n          // 使用 requestIdleCallback 在浏览器空闲时执行\r\n          if (window.requestIdleCallback) {\r\n            window.requestIdleCallback(executeCapture, { timeout: 5000 })\r\n          } else {\r\n            setTimeout(executeCapture, 0)\r\n          }\r\n        })\r\n      } finally {\r\n      }\r\n    },\r\n    getLogo() {\r\n      GetCompany().then(res => {\r\n        this.logoUrl = res.Data.Icon\r\n      })\r\n    },\r\n    tmplSelect(id) {\r\n      this.toEdit = ''\r\n      this.activeIndex = id\r\n      if (this.form && this.form.Id === id) return\r\n      this.loadTemplate(id)\r\n    },\r\n    async cloneTemplate() {\r\n      const copyTemplate = JSON.parse(JSON.stringify(this.form))\r\n      delete copyTemplate.Id\r\n      await SavePrintTemplateEntity(copyTemplate).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message.success('复制成功')\r\n          this.getTemplateList()\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      })\r\n    },\r\n    deleteTemplate(id) {\r\n      this.$confirm('是否删除所选内容', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        center: true\r\n      })\r\n        .then(() => {\r\n          DeletePrintTemplate({ id }).then(res => {\r\n            if (res.IsSucceed) {\r\n              this.$message.success('删除成功')\r\n              this.getTemplateList()\r\n            } else {\r\n              this.$message.error(res.Message)\r\n            }\r\n          })\r\n          if (this.toEdit) {\r\n            this.toEdit = ''\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n    async init() {\r\n      // 初始化 provider\r\n      this.getLogo()\r\n      const provider = providers.find(i => i.value == this.mode)\r\n      hiprint.init({\r\n        providers: [provider.f]\r\n      })\r\n      this.getTemplateList()\r\n    },\r\n\r\n    /**\r\n       * 构建左侧可拖拽元素\r\n       * 注意: 可拖拽元素必须在 hiprint.init() 之后调用\r\n       * 而且 必须包含 class=\"ep-draggable-item\" 否则无法拖拽进设计器\r\n       */\r\n    buildLeftElement() {\r\n      hiprint.PrintElementTypeManager.buildByHtml($('.ep-draggable-item'))\r\n    },\r\n    buildDesigner(template = {}) {\r\n      // eslint-disable-next-line no-undef\r\n      $('#hiprint-printTemplate').empty() // 先清空, 避免重复构建\r\n      hiprintTemplate = new hiprint.PrintTemplate({\r\n        template,\r\n        settingContainer: '#PrintElementOptionSetting' // 元素参数容器\r\n      })\r\n      // 构建 并填充到 容器中\r\n      hiprintTemplate.design('#hiprint-printTemplate')\r\n    },\r\n    handlePrint() {\r\n      // 打印数据，key 对应 元素的 字段名\r\n      const printData = {}\r\n      const printArr = hiprintTemplate.getJson().panels[0].printElements\r\n      printArr.forEach(item => {\r\n        if (item.options.field == 'Table') {\r\n          printData[item.options.field] = JSON.parse(item.options.testData)\r\n        } else {\r\n          console.log(item)\r\n          printData[item.options.field] = item.options.testData || item.options\r\n        }\r\n      })\r\n      console.log(printData)\r\n\r\n      // let printData = hiprintTemplate.getJson()\r\n      // 参数: 打印时设置 左偏移量，上偏移量\r\n      const options = { leftOffset: -1, topOffset: -1 }\r\n      // 扩展\r\n      const ext = {\r\n        callback: () => {\r\n          console.log('浏览器打印窗口已打开')\r\n        }\r\n        // styleHandler: () => {\r\n        //   // 重写 文本 打印样式\r\n        //   return \"<style>.hiprint-printElement-text{color:red !important;}</style>\";\r\n        // }\r\n      }\r\n      // 调用浏览器打印\r\n      hiprintTemplate.print(printData, options, ext)\r\n    },\r\n    changePaper() {\r\n      const temp = this.paperTypes.find(i => i.type === this.curPaperType)\r\n      if (this.curPaperType === '自定义纸张') {\r\n        hiprintTemplate.setPaper(this.paperWidth, this.paperHeight)\r\n      } else {\r\n        hiprintTemplate.setPaper(temp.width, temp.height)\r\n      }\r\n    },\r\n    // 新建模板\r\n    createTemplate() {\r\n      this.form = {\r\n        Name: '',\r\n        Type: 1, // 1-发货单\r\n        Data: '',\r\n        Base64Image: ''\r\n      }\r\n      this.clearTemplate()\r\n    },\r\n    // 保存模板\r\n    async saveTemplate() {\r\n      this.saveLoading = true\r\n      try {\r\n        this.form.Base64Image = await this.getImage()\r\n        if (!this.form.Name) {\r\n          this.$message.error('请输入模板名称')\r\n          return\r\n        }\r\n        const json = hiprintTemplate.getJson()\r\n        this.form.Data = JSON.stringify(json)\r\n        const res = await SavePrintTemplateEntity(this.form)\r\n        if (res.IsSucceed) {\r\n          this.$message.success('保存成功')\r\n          this.getTemplateList()\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('保存失败')\r\n        console.error('保存模板失败:', error)\r\n      } finally {\r\n        this.saveLoading = false\r\n      }\r\n    },\r\n    // 加载模板\r\n    async loadTemplate(id) {\r\n      this.clearTemplate()\r\n      const res = await GetPrintTemplateEntity({ id })\r\n      this.form = res.Data\r\n      const parseData = JSON.parse(res.Data.Data)\r\n      try {\r\n        const index = parseData.panels[0].printElements.findIndex(i => i.options.field === 'Logo')\r\n        parseData.panels[0].printElements[index].options.src = this.logoUrl\r\n      } catch (e) {}\r\n      console.log()\r\n      const template = parseData\r\n      this.buildDesigner(template)\r\n\r\n      // 匹配纸张\r\n      const { width, height } = template.panels[0]\r\n      const matchedPaper = this.paperTypes.find(i => i.width == width & i.height == height)\r\n      if (matchedPaper) {\r\n        this.curPaper = matchedPaper\r\n      } else {\r\n        this.curPaper = {\r\n          type: '自定义纸张',\r\n          width,\r\n          height\r\n        }\r\n      }\r\n      this.curPaperType = this.curPaper.type\r\n      this.paperWidth = width\r\n      this.paperHeight = height\r\n      this.changePaper()\r\n    },\r\n    // 清空模板\r\n    clearTemplate() {\r\n      $('#hiprint-printTemplate').empty() // 先清空, 避免重复构建\r\n      this.buildDesigner()\r\n    },\r\n\r\n    async getTemplateList() {\r\n      const res = await GetPrintTemplateList({\r\n        type: this.mode\r\n      })\r\n      this.tmplList = res.Data\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .title{\r\n    min-width: 100%;\r\n    padding-left: 8px;\r\n    font-size: 18px;\r\n    margin-top: 20px;\r\n  }\r\n  ::-webkit-scrollbar {\r\n    width: 0px;\r\n    height: 8px;\r\n  }\r\n  ::-webkit-scrollbar-thumb {\r\n    border-radius: 4px;\r\n    // box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);\r\n    background: #ddd;\r\n  }\r\n  ::-webkit-scrollbar-track {\r\n    box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.1);\r\n    border-radius: 4px;\r\n    background: #ededed;\r\n  }\r\n  .item {\r\n    color: rgba(34,40,52,0.65);\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    margin: 10px 10px;\r\n    width: 112px;\r\n    height: 44px;\r\n    border-radius: 4px 4px 4px 4px;\r\n    border: 1px dashed #D0D3DB;\r\n    font-size: 14px;\r\n  }\r\n  .label{\r\n    margin: 0 5px 0 10px;\r\n  }\r\n  /*::v-deep{*/\r\n  /*    .hiprint-option-item-settingBtn{*/\r\n  /*        background: #0ba1f8;*/\r\n  /*    }*/\r\n  /*}*/\r\n  .header{\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    row-gap: 10px;\r\n    align-items: center;\r\n    margin-bottom: 10px;\r\n    color: rgba(34, 40, 52, 0.85);\r\n    font-size: 12px;\r\n    .input{\r\n      width: 100px;\r\n    }\r\n    .zoom{\r\n      margin: 0 10px;\r\n      border: 1px solid #D0D3DB;\r\n      border-radius: 4px;\r\n      width: 100px;\r\n      height: 32px;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      font-size: 14px;\r\n    }\r\n    .zoom-btn{\r\n      padding: 5px;\r\n      font-size: 19px;\r\n    }\r\n  }\r\n  ::v-deep{\r\n    .hinnn-layout-sider{\r\n      *{\r\n        color: rgba(34, 40, 52, 0.85);\r\n      }\r\n      input[placeholder=\"请输入图片地址\"]  {\r\n        width: 100%!important;\r\n        & + button{\r\n          display: none;\r\n        }\r\n      }\r\n      input,textarea,select{\r\n        border-radius: 4px!important;\r\n        border: 1px solid #D0D3DB!important;\r\n      }\r\n      input,select{\r\n        height: 32px!important;\r\n        line-height: 32px;\r\n      }\r\n      .hiprint-option-item-settingBtn{\r\n        background-color: #298DFF;\r\n        border-radius: 4px;\r\n        height: 30px;\r\n        color: #ffffff;\r\n        cursor: pointer;\r\n      }\r\n      .hiprint-option-item-deleteBtn{\r\n        background-color: #FB6B7F;\r\n      }\r\n    }\r\n\r\n  }\r\n  .tmpl-list {\r\n    margin-top: 12px;\r\n    overflow-y: auto;\r\n    min-height: 130px;\r\n    .tmpl-menu {\r\n      border-right: none;\r\n      .el-menu-item {\r\n        height: 32px;\r\n        line-height: 32px;\r\n        .el-link {\r\n          position: absolute;\r\n          top: 20%;\r\n          right: 12px;\r\n          margin-top: -7px;\r\n          transition: transform 0.3s;\r\n          &:last-child {\r\n            right: 36px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n</style>\r\n\r\n"]}]}
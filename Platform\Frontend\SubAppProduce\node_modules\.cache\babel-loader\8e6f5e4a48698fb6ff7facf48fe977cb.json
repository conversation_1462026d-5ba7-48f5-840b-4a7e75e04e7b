{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\component\\Add.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\component\\Add.vue", "mtime": 1756109946517}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["SaveProBimComponentType", "SavePartType", "props", "addLevel", "type", "Number", "default", "parentId", "String", "isComp", "Boolean", "data", "btnLoading", "form", "Code", "Name", "Is_Component", "Lead_Time", "rules", "required", "message", "trigger", "computed", "levelName", "watch", "value", "methods", "submit", "_this", "$refs", "validate", "_ref", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "valid", "postFN", "res", "wrap", "_callee$", "_context", "prev", "next", "abrupt", "Category", "$route", "query", "typeCode", "Professional_Id", "typeId", "Level", "Parent_Id", "sent", "IsSucceed", "$message", "$emit", "Message", "stop", "_x", "apply", "arguments"], "sources": ["src/views/PRO/bom-setting/structure-type-config/component/Add.vue"], "sourcesContent": ["<template>\r\n  <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n    <el-form-item :label=\"`${levelName}大类名称`\" prop=\"Name\">\r\n      <el-input v-model.trim=\"form.Name\" clearable maxlength=\"50\" />\r\n    </el-form-item>\r\n    <el-form-item :label=\"`${levelName}大类编号`\" prop=\"Code\">\r\n      <el-input v-model.trim=\"form.Code\" clearable maxlength=\"50\" />\r\n    </el-form-item>\r\n    <el-form-item label=\"生产周期\" prop=\"Lead_Time\">\r\n      <el-input-number v-model.number=\"form.Lead_Time\" class=\"cs-number-btn-hidden w100\" clearable />\r\n    </el-form-item>\r\n    <el-form-item v-if=\"isComp\" label=\"直发件\" prop=\"Is_Component\">\r\n      <el-radio-group v-model=\"form.Is_Component\">\r\n        <el-radio :label=\"true\">否</el-radio>\r\n        <el-radio :label=\"false\">是</el-radio>\r\n      </el-radio-group>\r\n    </el-form-item>\r\n    <el-form-item style=\"text-align: right\">\r\n      <el-button @click=\"$emit('close')\">取消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"submit\">保存</el-button>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n\r\n<script>\r\nimport { SaveProBimComponentType } from '@/api/PRO/component-type'\r\nimport { SavePartType } from '@/api/PRO/partType'\r\n\r\nexport default {\r\n  props: {\r\n    addLevel: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    parentId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    // true=构件；false=零件\r\n    isComp: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      form: {\r\n        Code: '',\r\n        Name: '',\r\n        Is_Component: '',\r\n        Lead_Time: 0\r\n      },\r\n      rules: {\r\n        Name: [\r\n          { required: true, message: '请输入名称', trigger: 'blur' }\r\n        ],\r\n        Code: [\r\n          { required: true, message: '请输入编码', trigger: 'blur' }\r\n        ],\r\n        Is_Component: [\r\n          { required: true, message: '请选择是否直发件', trigger: 'change' }\r\n        ],\r\n        Lead_Time: [\r\n          { required: true, message: '请输入周期', trigger: 'blur' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    levelName() {\r\n      return this.addLevel === 1 ? '一级' : (this.addLevel === 2 ? '二级' : (this.addLevel === 3 ? '三级' : ''))\r\n    }\r\n  },\r\n  watch: {\r\n    // 零件没有直发件字段，不需要必填\r\n    isComp(value) {\r\n      this.rules.Is_Component[0].required = value\r\n    }\r\n  },\r\n  methods: {\r\n    submit() {\r\n      this.$refs['form'].validate(async(valid) => {\r\n        if (!valid) {\r\n          return false\r\n        }\r\n        this.form.Category = this.$route.query.typeCode\r\n        this.form.Professional_Id = this.$route.query.typeId\r\n        this.form.Level = this.addLevel\r\n        if (this.addLevel > 1) {\r\n          this.form.Parent_Id = this.parentId\r\n        }\r\n        this.btnLoading = true\r\n        const postFN = this.isComp ? SaveProBimComponentType : SavePartType\r\n        const res = await postFN(this.form)\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '操作成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('close')\r\n          this.$emit('getTreeList')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.btnLoading = false\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,SAAAA,uBAAA;AACA,SAAAC,YAAA;AAEA;EACAC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,QAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACA;IACAG,MAAA;MACAL,IAAA,EAAAM,OAAA;MACAJ,OAAA;IACA;EACA;EACAK,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,YAAA;QACAC,SAAA;MACA;MACAC,KAAA;QACAH,IAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,IAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,YAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAJ,SAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,QAAA;IACAC,SAAA,WAAAA,UAAA;MACA,YAAApB,QAAA,qBAAAA,QAAA,qBAAAA,QAAA;IACA;EACA;EACAqB,KAAA;IACA;IACAf,MAAA,WAAAA,OAAAgB,KAAA;MACA,KAAAP,KAAA,CAAAF,YAAA,IAAAG,QAAA,GAAAM,KAAA;IACA;EACA;EACAC,OAAA;IACAC,MAAA,WAAAA,OAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,SAAAC,QAAA;QAAA,IAAAC,IAAA,GAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAAC,KAAA;UAAA,IAAAC,MAAA,EAAAC,GAAA;UAAA,OAAAL,mBAAA,GAAAM,IAAA,UAAAC,SAAAC,QAAA;YAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;cAAA;gBAAA,IACAP,KAAA;kBAAAK,QAAA,CAAAE,IAAA;kBAAA;gBAAA;gBAAA,OAAAF,QAAA,CAAAG,MAAA,WACA;cAAA;gBAEAhB,KAAA,CAAAf,IAAA,CAAAgC,QAAA,GAAAjB,KAAA,CAAAkB,MAAA,CAAAC,KAAA,CAAAC,QAAA;gBACApB,KAAA,CAAAf,IAAA,CAAAoC,eAAA,GAAArB,KAAA,CAAAkB,MAAA,CAAAC,KAAA,CAAAG,MAAA;gBACAtB,KAAA,CAAAf,IAAA,CAAAsC,KAAA,GAAAvB,KAAA,CAAAzB,QAAA;gBACA,IAAAyB,KAAA,CAAAzB,QAAA;kBACAyB,KAAA,CAAAf,IAAA,CAAAuC,SAAA,GAAAxB,KAAA,CAAArB,QAAA;gBACA;gBACAqB,KAAA,CAAAhB,UAAA;gBACAyB,MAAA,GAAAT,KAAA,CAAAnB,MAAA,GAAAT,uBAAA,GAAAC,YAAA;gBAAAwC,QAAA,CAAAE,IAAA;gBAAA,OACAN,MAAA,CAAAT,KAAA,CAAAf,IAAA;cAAA;gBAAAyB,GAAA,GAAAG,QAAA,CAAAY,IAAA;gBACA,IAAAf,GAAA,CAAAgB,SAAA;kBACA1B,KAAA,CAAA2B,QAAA;oBACAnC,OAAA;oBACAhB,IAAA;kBACA;kBACAwB,KAAA,CAAA4B,KAAA;kBACA5B,KAAA,CAAA4B,KAAA;gBACA;kBACA5B,KAAA,CAAA2B,QAAA;oBACAnC,OAAA,EAAAkB,GAAA,CAAAmB,OAAA;oBACArD,IAAA;kBACA;gBACA;gBACAwB,KAAA,CAAAhB,UAAA;cAAA;cAAA;gBAAA,OAAA6B,QAAA,CAAAiB,IAAA;YAAA;UAAA,GAAAvB,OAAA;QAAA,CACA;QAAA,iBAAAwB,EAAA;UAAA,OAAA5B,IAAA,CAAA6B,KAAA,OAAAC,SAAA;QAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}
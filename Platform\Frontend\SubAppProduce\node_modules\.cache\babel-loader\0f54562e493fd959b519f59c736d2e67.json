{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\part-config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\part-config\\index.vue", "mtime": 1757468112450}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetTableSettingList", "UpdateComponentPartTableSetting", "UpdateColumnSetting", "GetBOMInfo", "name", "components", "data", "activeNameApi", "activeName", "currentCode", "partName", "typeCode", "materialCode", "currentFinalTypeCode", "tabPosition", "searchVal", "majorName", "unit", "steelUnit", "templateList", "templateListNew", "loading", "systemField", "expandField", "businessField", "computed", "tabList", "label", "value", "created", "$route", "query", "mounted", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "steel_unit", "GetTableSettingListFn", "stop", "methods", "changeStatus", "$event", "id", "displayName", "find", "item", "Id", "Display_Name", "$message", "type", "message", "map", "Is_Enabled", "handleClick", "tab", "event", "searchValue", "_this2", "filterList", "search", "RegExp", "push", "saveModifyChangesFn", "_this3", "_callee2", "res", "_callee2$", "_context2", "sent", "IsSucceed", "Message", "_this4", "_callee3", "_callee3$", "_context3", "_this5", "_callee4", "_yield$GetBOMInfo", "currentParentBOMInfo", "_callee4$", "_context4", "IsComponent", "ProfessionalCode", "TypeCode", "Level", "Data", "length", "for<PERSON>ach", "v", "Code", "some", "Column_Type"], "sources": ["src/views/PRO/bom-setting/part-config/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"page-container\">\r\n      <div class=\"top-wrapper\">\r\n        <!-- <div class=\"title\">零件模板配置：</div> -->\r\n        <div class=\"info\">\r\n          <template v-if=\"!!majorName\">\r\n            <div class=\"title\">当前专业：</div>\r\n            <div class=\"value\">{{ majorName }}</div>\r\n          </template>\r\n          <template v-if=\"!!unit\">\r\n            <div class=\"title\">统计单位：</div>\r\n            <div class=\"value\">{{ unit }}</div>\r\n          </template>\r\n          <template v-if=\"!!steelUnit\">\r\n            <div class=\"title\">构件单位：</div>\r\n            <div class=\"value\">{{ steelUnit }}</div>\r\n          </template>\r\n          <template>\r\n            <div class=\"title\">单位统计字段：</div>\r\n            <div v-for=\"(item,index) in templateListNew\" v-show=\"item.Code==='Num'\" :key=\"index\" style=\"display: flex;flex-direction: row\">\r\n              {{ item.Display_Name }}\r\n            </div>\r\n            <div v-for=\"(item,index) in templateListNew\" v-show=\"item.Code==='Weight'\" :key=\"index+999\" style=\"display: flex;flex-direction: row\">\r\n              *{{ item.Display_Name }}\r\n            </div>\r\n          </template>\r\n        </div>\r\n        <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\r\n          <el-tab-pane v-for=\"(item,index) in tabList\" :key=\"partName+index\" :label=\"item.label\" :name=\"item.value\" />\r\n\r\n        </el-tabs>\r\n      </div>\r\n      <div class=\"content-wrapper\" style=\"min-height: calc(100vh - 340px)\">\r\n        <div class=\"right-c\">\r\n          <el-row type=\"flex\" justify=\"space-between\">\r\n            <div class=\"right-c-title\">\r\n              <div class=\"setting-title\">{{ systemField==true ? '系统字段' : businessField==true ? '业务字段' : expandField==true ? '拓展字段' : '' }}</div>\r\n            </div>\r\n            <div style=\"display: flex;flex-direction: row\">\r\n              <span style=\"width:140px;font-size:12px;height:32px; line-height: 32px;display: inline-block;\">字段名称：</span>\r\n              <el-input v-model=\"searchVal\" placeholder=\"请输入字段名称\" clearable />\r\n              <el-button type=\"primary\" style=\"margin-left: 10px\" @click=\"searchValue\">查询</el-button>\r\n              <el-button type=\"primary\" :loading=\"loading\" @click=\"saveModifyChangesFn\">保存设置</el-button>\r\n            </div>\r\n          </el-row>\r\n          <el-form label-width=\"120px\" style=\"margin-top: 24px\">\r\n            <template v-for=\"(item,index) in templateListNew\">\r\n              <el-row v-if=\"item.Column_Type==0\" :key=\"index\">\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"item.Code\" label-width=\"150px\">\r\n                    <el-input v-model=\"item.Display_Name\" :disabled=\"false\" style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"'备注说明'\">\r\n                    <el-input v-model=\"item.Remark\" disabled style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"排序\">\r\n                    <el-input v-model=\"item.Sort\" style=\"width: 100px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"是否显示\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </template>\r\n            <div v-show=\"businessField==true && systemField==true\" class=\"setting-title\">业务字段</div>\r\n            <template v-for=\"(item,index) in templateListNew\">\r\n              <el-row v-if=\"item.Column_Type==2\" :key=\"index\">\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"item.Code\" label-width=\"150px\">\r\n                    <el-input v-model=\"item.Display_Name\" style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"'备注说明'\">\r\n                    <el-input v-model=\"item.Remark\" disabled style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"排序\">\r\n                    <el-input v-model=\"item.Sort\" style=\"width: 100px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName==activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"是否启用\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-else :span=\"4\">\r\n                  <el-form-item label=\"是否显示\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </template>\r\n            <div v-show=\"expandField==true\" class=\"setting-title\">拓展字段</div>\r\n            <template v-for=\"(item,index) in templateListNew\">\r\n              <el-row v-if=\"item.Column_Type==1\" :key=\"index\">\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"item.Code\" label-width=\"150px\">\r\n                    <el-input v-model=\"item.Display_Name\" style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"'备注说明'\">\r\n                    <el-input v-model=\"item.Remark\" disabled style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"排序\">\r\n                    <el-input v-model=\"item.Sort\" style=\"width: 100px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName==activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"是否启用\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-else :span=\"4\">\r\n                  <el-form-item label=\"是否显示\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </template>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { GetTableSettingList, UpdateComponentPartTableSetting, UpdateColumnSetting } from '@/api/PRO/component-type'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\n\r\nexport default {\r\n  name: 'PartConfig',\r\n  components: {\r\n  },\r\n  data() {\r\n    return {\r\n      activeNameApi: 'plm_parts_field_page_list',\r\n      activeName: 'plm_parts_field_page_list',\r\n      currentCode: 'plm_parts_page_list',\r\n      partName: '',\r\n      typeCode: '',\r\n      materialCode: '',\r\n      currentFinalTypeCode: '',\r\n      tabPosition: 'left',\r\n      //   tabList: [\r\n      //     {\r\n      //       label: '零件字段维护',\r\n      //       value: 'plm_parts_field_page_list'\r\n      //     },\r\n      //     {\r\n      //       label: '零件管理列表',\r\n      //       value: 'plm_parts_page_list'\r\n      //     },\r\n      //     {\r\n      //       label: '零件深化清单',\r\n      //       value: 'plm_parts_detailImport'\r\n      //     },\r\n      //     {\r\n      //       label: '生产详情列表',\r\n      //       value: 'plm_parts_modelImport'\r\n      //     }\r\n      //   ],\r\n      searchVal: '',\r\n      majorName: '',\r\n      unit: '',\r\n      steelUnit: '',\r\n      templateList: [],\r\n      templateListNew: [],\r\n      loading: false,\r\n      systemField: false,\r\n      expandField: false,\r\n      businessField: false\r\n    }\r\n  },\r\n  computed: {\r\n    tabList() {\r\n      return [\r\n        {\r\n          label: this.partName + '字段维护',\r\n          value: 'plm_parts_field_page_list'\r\n        },\r\n        {\r\n          label: this.partName + '管理列表',\r\n          value: 'plm_parts_page_list'\r\n        },\r\n        {\r\n          label: this.partName + '深化清单',\r\n          value: 'plm_parts_detailImport'\r\n        },\r\n        {\r\n          label: '生产详情列表',\r\n          value: 'plm_parts_modelImport'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  created() {\r\n    this.typeCode = this.$route.query.typeCode || 'Steel'\r\n    this.materialCode = this.$route.query.materialCode || 'StructuralAs'\r\n    this.currentFinalTypeCode = this.typeCode\r\n  },\r\n  async mounted() {\r\n    this.majorName = this.$route.query.name || '钢结构'\r\n    this.unit = this.$route.query.unit || 't'\r\n    this.steelUnit = this.$route.query.steel_unit || 'kg'\r\n\r\n    this.GetTableSettingListFn()\r\n  },\r\n  methods: {\r\n\r\n    changeStatus($event, id) {\r\n      const displayName = this.templateList.find((item) => { return item.Id == id }).Display_Name\r\n      if (displayName == '' && $event == true) {\r\n        this.$message({ type: 'error', message: '请先填写字段名' })\r\n        this.templateList.map((item) => {\r\n          if (item.Id == id) {\r\n            item.Is_Enabled = false\r\n          }\r\n          return item\r\n        })\r\n      }\r\n    },\r\n    handleClick(tab, event) {\r\n      this.currentCode = tab.name\r\n      this.GetTableSettingListFn()\r\n    },\r\n    searchValue() {\r\n      if (!this.searchVal) {\r\n        this.templateListNew = this.templateList\r\n      } else {\r\n        const filterList = []\r\n        this.templateList.map(item => {\r\n          if (item.Display_Name.search(new RegExp(this.searchVal, 'ig')) !== -1) {\r\n            filterList.push(item)\r\n          }\r\n        })\r\n        this.templateListNew = filterList\r\n      }\r\n    },\r\n\r\n    saveModifyChangesFn() {\r\n      if (this.activeName == this.activeNameApi) {\r\n        this.UpdateComponentPartTableSetting()\r\n      } else {\r\n        this.UpdateColumnSetting()\r\n      }\r\n    },\r\n\r\n    async UpdateColumnSetting() {\r\n      this.loading = true\r\n      const res = await UpdateColumnSetting(this.templateList)\r\n      this.loading = false\r\n      if (res.IsSucceed) {\r\n        this.$message({ type: 'success', message: '保存成功' })\r\n      } else {\r\n        this.$message({ type: 'error', message: res.Message })\r\n      }\r\n    },\r\n\r\n    async UpdateComponentPartTableSetting() {\r\n      this.loading = true\r\n      const res = await UpdateComponentPartTableSetting(this.templateList)\r\n      this.loading = false\r\n      if (res.IsSucceed) {\r\n        this.$message({ type: 'success', message: '保存成功' })\r\n      } else {\r\n        this.$message({ type: 'error', message: res.Message })\r\n      }\r\n    },\r\n\r\n    async GetTableSettingListFn() {\r\n      let data = {}\r\n      if (this.activeName == this.activeNameApi) {\r\n        data = { IsComponent: false, ProfessionalCode: this.currentFinalTypeCode }\r\n      } else {\r\n        data = { IsComponent: false, ProfessionalCode: this.currentFinalTypeCode, TypeCode: this.currentCode + ',' + this.currentFinalTypeCode }\r\n      }\r\n      data.Level = 0\r\n      const { partName, currentParentBOMInfo } = await GetBOMInfo(0)\r\n      this.partName = partName\r\n      const res = await GetTableSettingList(data)\r\n      if (res.IsSucceed) {\r\n        this.templateList = res.Data || []\r\n        if (this.templateList.length > 0) {\r\n          this.templateList.forEach(v => {\r\n            if (v.Code === 'Code') {\r\n              v.Display_Name = partName\r\n            }\r\n            if (v.Code === 'Component_Code') {\r\n              v.Display_Name = currentParentBOMInfo?.Display_Name || ''\r\n            }\r\n          })\r\n        }\r\n        this.templateListNew = this.templateList\r\n        this.systemField = this.templateList.some(item => { return item.Column_Type == 0 })\r\n        this.expandField = this.templateList.some(item => { return item.Column_Type == 1 })\r\n        this.businessField = this.templateList.some(item => { return item.Column_Type == 2 })\r\n      } else {\r\n        this.$message({ type: 'error', message: res.Message })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n  <style lang=\"scss\" scoped>\r\n    .page-container{\r\n      margin:16px;\r\n      box-sizing: border-box;\r\n      .top-wrapper{\r\n        background: #fff;\r\n        padding:16px;\r\n        box-sizing: border-box;\r\n        .title{\r\n          font-size: 16px;\r\n          font-weight: 500;\r\n          color:#333333;\r\n        }\r\n        .info{\r\n          font-size: 14px;\r\n          margin:8px 0 24px 0;\r\n          display: flex;\r\n          flex-direction: row;\r\n          .title{\r\n            font-size: 14px;\r\n            color: #999999;\r\n          }\r\n          .value{\r\n            color: #333333;\r\n            margin-right: 24px;\r\n          }\r\n        }\r\n      }\r\n      .content-wrapper{\r\n        margin-top:16px;\r\n        display: flex;\r\n        flex-direction: row;\r\n        .left-c{\r\n          width: 160px;\r\n          background: #fff;\r\n          margin-right: 16px;\r\n        }\r\n        .right-c{\r\n          background: #fff;\r\n          width: 100%;\r\n          padding: 16px 24px;\r\n          box-sizing: border-box;\r\n        }\r\n      }\r\n    }\r\n    ::v-deep.el-tabs--left .el-tabs__nav-wrap.is-left::after{\r\n      left:0\r\n    }\r\n    ::v-deep.el-tabs--left .el-tabs__active-bar.is-left{\r\n      left: 0;\r\n    }\r\n    .setting-title {\r\n      font-weight: 400;\r\n      color: #1f2f3d;\r\n      margin: 30px 0 20px;\r\n      font-size: 22px;\r\n    }\r\n    .setting-title:first-child {\r\n      margin: 0;\r\n    }\r\n  </style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmKA,SAAAA,mBAAA,EAAAC,+BAAA,IAAAA,gCAAA,EAAAC,mBAAA,IAAAA,oBAAA;AACA,SAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,UAAA,GACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,UAAA;MACAC,WAAA;MACAC,QAAA;MACAC,QAAA;MACAC,YAAA;MACAC,oBAAA;MACAC,WAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC,SAAA;MACAC,SAAA;MACAC,IAAA;MACAC,SAAA;MACAC,YAAA;MACAC,eAAA;MACAC,OAAA;MACAC,WAAA;MACAC,WAAA;MACAC,aAAA;IACA;EACA;EACAC,QAAA;IACAC,OAAA,WAAAA,QAAA;MACA,QACA;QACAC,KAAA,OAAAjB,QAAA;QACAkB,KAAA;MACA,GACA;QACAD,KAAA,OAAAjB,QAAA;QACAkB,KAAA;MACA,GACA;QACAD,KAAA,OAAAjB,QAAA;QACAkB,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAlB,QAAA,QAAAmB,MAAA,CAAAC,KAAA,CAAApB,QAAA;IACA,KAAAC,YAAA,QAAAkB,MAAA,CAAAC,KAAA,CAAAnB,YAAA;IACA,KAAAC,oBAAA,QAAAF,QAAA;EACA;EACAqB,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACAT,KAAA,CAAAjB,SAAA,GAAAiB,KAAA,CAAAH,MAAA,CAAAC,KAAA,CAAA3B,IAAA;YACA6B,KAAA,CAAAhB,IAAA,GAAAgB,KAAA,CAAAH,MAAA,CAAAC,KAAA,CAAAd,IAAA;YACAgB,KAAA,CAAAf,SAAA,GAAAe,KAAA,CAAAH,MAAA,CAAAC,KAAA,CAAAY,UAAA;YAEAV,KAAA,CAAAW,qBAAA;UAAA;UAAA;YAAA,OAAAJ,QAAA,CAAAK,IAAA;QAAA;MAAA,GAAAR,OAAA;IAAA;EACA;EACAS,OAAA;IAEAC,YAAA,WAAAA,aAAAC,MAAA,EAAAC,EAAA;MACA,IAAAC,WAAA,QAAA/B,YAAA,CAAAgC,IAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,EAAA,IAAAJ,EAAA;MAAA,GAAAK,YAAA;MACA,IAAAJ,WAAA,UAAAF,MAAA;QACA,KAAAO,QAAA;UAAAC,IAAA;UAAAC,OAAA;QAAA;QACA,KAAAtC,YAAA,CAAAuC,GAAA,WAAAN,IAAA;UACA,IAAAA,IAAA,CAAAC,EAAA,IAAAJ,EAAA;YACAG,IAAA,CAAAO,UAAA;UACA;UACA,OAAAP,IAAA;QACA;MACA;IACA;IACAQ,WAAA,WAAAA,YAAAC,GAAA,EAAAC,KAAA;MACA,KAAArD,WAAA,GAAAoD,GAAA,CAAAzD,IAAA;MACA,KAAAwC,qBAAA;IACA;IACAmB,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,UAAAjD,SAAA;QACA,KAAAK,eAAA,QAAAD,YAAA;MACA;QACA,IAAA8C,UAAA;QACA,KAAA9C,YAAA,CAAAuC,GAAA,WAAAN,IAAA;UACA,IAAAA,IAAA,CAAAE,YAAA,CAAAY,MAAA,KAAAC,MAAA,CAAAH,MAAA,CAAAjD,SAAA;YACAkD,UAAA,CAAAG,IAAA,CAAAhB,IAAA;UACA;QACA;QACA,KAAAhC,eAAA,GAAA6C,UAAA;MACA;IACA;IAEAI,mBAAA,WAAAA,oBAAA;MACA,SAAA7D,UAAA,SAAAD,aAAA;QACA,KAAAN,+BAAA;MACA;QACA,KAAAC,mBAAA;MACA;IACA;IAEAA,mBAAA,WAAAA,oBAAA;MAAA,IAAAoE,MAAA;MAAA,OAAApC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAmC,SAAA;QAAA,IAAAC,GAAA;QAAA,OAAArC,mBAAA,GAAAG,IAAA,UAAAmC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjC,IAAA,GAAAiC,SAAA,CAAAhC,IAAA;YAAA;cACA4B,MAAA,CAAAjD,OAAA;cAAAqD,SAAA,CAAAhC,IAAA;cAAA,OACAxC,oBAAA,CAAAoE,MAAA,CAAAnD,YAAA;YAAA;cAAAqD,GAAA,GAAAE,SAAA,CAAAC,IAAA;cACAL,MAAA,CAAAjD,OAAA;cACA,IAAAmD,GAAA,CAAAI,SAAA;gBACAN,MAAA,CAAAf,QAAA;kBAAAC,IAAA;kBAAAC,OAAA;gBAAA;cACA;gBACAa,MAAA,CAAAf,QAAA;kBAAAC,IAAA;kBAAAC,OAAA,EAAAe,GAAA,CAAAK;gBAAA;cACA;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAA7B,IAAA;UAAA;QAAA,GAAA0B,QAAA;MAAA;IACA;IAEAtE,+BAAA,WAAAA,gCAAA;MAAA,IAAA6E,MAAA;MAAA,OAAA5C,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA2C,SAAA;QAAA,IAAAP,GAAA;QAAA,OAAArC,mBAAA,GAAAG,IAAA,UAAA0C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxC,IAAA,GAAAwC,SAAA,CAAAvC,IAAA;YAAA;cACAoC,MAAA,CAAAzD,OAAA;cAAA4D,SAAA,CAAAvC,IAAA;cAAA,OACAzC,gCAAA,CAAA6E,MAAA,CAAA3D,YAAA;YAAA;cAAAqD,GAAA,GAAAS,SAAA,CAAAN,IAAA;cACAG,MAAA,CAAAzD,OAAA;cACA,IAAAmD,GAAA,CAAAI,SAAA;gBACAE,MAAA,CAAAvB,QAAA;kBAAAC,IAAA;kBAAAC,OAAA;gBAAA;cACA;gBACAqB,MAAA,CAAAvB,QAAA;kBAAAC,IAAA;kBAAAC,OAAA,EAAAe,GAAA,CAAAK;gBAAA;cACA;YAAA;YAAA;cAAA,OAAAI,SAAA,CAAApC,IAAA;UAAA;QAAA,GAAAkC,QAAA;MAAA;IACA;IAEAnC,qBAAA,WAAAA,sBAAA;MAAA,IAAAsC,MAAA;MAAA,OAAAhD,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA+C,SAAA;QAAA,IAAA7E,IAAA,EAAA8E,iBAAA,EAAA1E,QAAA,EAAA2E,oBAAA,EAAAb,GAAA;QAAA,OAAArC,mBAAA,GAAAG,IAAA,UAAAgD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9C,IAAA,GAAA8C,SAAA,CAAA7C,IAAA;YAAA;cACApC,IAAA;cACA,IAAA4E,MAAA,CAAA1E,UAAA,IAAA0E,MAAA,CAAA3E,aAAA;gBACAD,IAAA;kBAAAkF,WAAA;kBAAAC,gBAAA,EAAAP,MAAA,CAAArE;gBAAA;cACA;gBACAP,IAAA;kBAAAkF,WAAA;kBAAAC,gBAAA,EAAAP,MAAA,CAAArE,oBAAA;kBAAA6E,QAAA,EAAAR,MAAA,CAAAzE,WAAA,SAAAyE,MAAA,CAAArE;gBAAA;cACA;cACAP,IAAA,CAAAqF,KAAA;cAAAJ,SAAA,CAAA7C,IAAA;cAAA,OACAvC,UAAA;YAAA;cAAAiF,iBAAA,GAAAG,SAAA,CAAAZ,IAAA;cAAAjE,QAAA,GAAA0E,iBAAA,CAAA1E,QAAA;cAAA2E,oBAAA,GAAAD,iBAAA,CAAAC,oBAAA;cACAH,MAAA,CAAAxE,QAAA,GAAAA,QAAA;cAAA6E,SAAA,CAAA7C,IAAA;cAAA,OACA1C,mBAAA,CAAAM,IAAA;YAAA;cAAAkE,GAAA,GAAAe,SAAA,CAAAZ,IAAA;cACA,IAAAH,GAAA,CAAAI,SAAA;gBACAM,MAAA,CAAA/D,YAAA,GAAAqD,GAAA,CAAAoB,IAAA;gBACA,IAAAV,MAAA,CAAA/D,YAAA,CAAA0E,MAAA;kBACAX,MAAA,CAAA/D,YAAA,CAAA2E,OAAA,WAAAC,CAAA;oBACA,IAAAA,CAAA,CAAAC,IAAA;sBACAD,CAAA,CAAAzC,YAAA,GAAA5C,QAAA;oBACA;oBACA,IAAAqF,CAAA,CAAAC,IAAA;sBACAD,CAAA,CAAAzC,YAAA,IAAA+B,oBAAA,aAAAA,oBAAA,uBAAAA,oBAAA,CAAA/B,YAAA;oBACA;kBACA;gBACA;gBACA4B,MAAA,CAAA9D,eAAA,GAAA8D,MAAA,CAAA/D,YAAA;gBACA+D,MAAA,CAAA5D,WAAA,GAAA4D,MAAA,CAAA/D,YAAA,CAAA8E,IAAA,WAAA7C,IAAA;kBAAA,OAAAA,IAAA,CAAA8C,WAAA;gBAAA;gBACAhB,MAAA,CAAA3D,WAAA,GAAA2D,MAAA,CAAA/D,YAAA,CAAA8E,IAAA,WAAA7C,IAAA;kBAAA,OAAAA,IAAA,CAAA8C,WAAA;gBAAA;gBACAhB,MAAA,CAAA1D,aAAA,GAAA0D,MAAA,CAAA/D,YAAA,CAAA8E,IAAA,WAAA7C,IAAA;kBAAA,OAAAA,IAAA,CAAA8C,WAAA;gBAAA;cACA;gBACAhB,MAAA,CAAA3B,QAAA;kBAAAC,IAAA;kBAAAC,OAAA,EAAAe,GAAA,CAAAK;gBAAA;cACA;YAAA;YAAA;cAAA,OAAAU,SAAA,CAAA1C,IAAA;UAAA;QAAA,GAAAsC,QAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\component\\FactoryAddDialog.vue?vue&type=style&index=0&id=40decafe&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\component\\FactoryAddDialog.vue", "mtime": 1757468128015}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYWRkLXByb2plY3QtY29udGFpbmVyIHsNCiAgaGVpZ2h0Ojcwdmg7DQoNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCg0KICAuc2VhcmNoLXNlY3Rpb24gew0KICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICB9DQoNCiAgLmluc3RydWN0aW9uLXNlY3Rpb24gew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIH0NCg0KICAuaW5zdHJ1Y3Rpb24gew0KICAgIGJhY2tncm91bmQ6ICNmMGY5ZmY7DQogICAgYm9yZGVyOiAxcHggc29saWQgI2IzZDhmZjsNCiAgICBjb2xvcjogIzE4OTBmZjsNCiAgICBwYWRkaW5nOiAxMnB4IDE2cHg7DQogICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgIG1hcmdpbi1ib3R0b206IDE2cHg7DQogICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgfQ0KDQogIC50YWJsZS1zZWN0aW9uIHsNCiAgICBmbGV4OiAxOw0KICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgIG92ZXJmbG93OiBoaWRkZW47DQogIH0NCg0KICAuZm9vdGVyLWFjdGlvbnMgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsNCiAgICBwYWRkaW5nOiAwcHggOHB4IDAgMDsNCiAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICB9DQp9DQo="}, {"version": 3, "sources": ["FactoryAddDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyNA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "FactoryAddDialog.vue", "sourceRoot": "src/views/PRO/project-config/product-mfg-path/component", "sourcesContent": ["<template>\r\n  <div class=\"add-project-container\">\r\n    <!-- 搜索区域 -->\r\n    <div class=\"search-section\">\r\n      <el-form :model=\"searchForm\" inline>\r\n        <el-form-item label=\"项目编号：\">\r\n          <el-input\r\n            v-model=\"searchForm.ProjectCode\"\r\n            placeholder=\"请输入\"\r\n            clearable\r\n            style=\"width: 200px\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"项目简称：\">\r\n          <el-input\r\n            v-model=\"searchForm.ProjectAbbr\"\r\n            placeholder=\"请输入\"\r\n            clearable\r\n            style=\"width: 200px\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">搜索</el-button>\r\n          <el-button @click=\"handleReset\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n\r\n    <!-- 提示信息 -->\r\n    <div class=\"instruction-section\">\r\n      <div class=\"instruction\">\r\n        请选择工厂级配置的工序类型，添加到项目\r\n      </div>\r\n      <el-button type=\"primary\" @click=\"handleAddToList\"> 加入列表 </el-button>\r\n    </div>\r\n\r\n    <!-- 项目表格 -->\r\n    <div class=\"table-section\">\r\n      <bt-table\r\n        ref=\"projectTable\"\r\n        code=\"AddProjectList\"\r\n        :custom-table-config=\"tableConfig\"\r\n        :grid-data-handler=\"handleGridData\"\r\n        :loading=\"loading\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        @row-click=\"handleRowClick\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 底部按钮 -->\r\n    <div class=\"footer-actions\">\r\n      <el-button @click=\"handleCancel\">取消</el-button>\r\n      <el-button type=\"primary\" :disabled=\"selectedProjects.length === 0\" @click=\"handleConfirm\">\r\n        确定\r\n      </el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetProjectPageList } from '@/api/PRO/project'\r\n\r\nexport default {\r\n  name: 'AddProject',\r\n  props: {\r\n    typeId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    addLevel: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    parentId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    activeType: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    typeCode: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    isComp: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    showDirect: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      searchForm: {\r\n        ProjectCode: '',\r\n        ProjectAbbr: ''\r\n      },\r\n      selectedProjects: [],\r\n      tableConfig: {\r\n        tableColumns: [],\r\n        tableActions: [],\r\n        tableData: [],\r\n        checkbox: false,\r\n        operateOptions: {\r\n          width: 120,\r\n          align: 'center',\r\n          isShow: false\r\n        }\r\n\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchProjectList()\r\n  },\r\n  methods: {\r\n\r\n    // 获取项目列表\r\n    async fetchProjectList() {\r\n      this.loading = true\r\n      try {\r\n        const params = {\r\n          Page: this.tableConfig.currentPage,\r\n          PageSize: this.tableConfig.pageSize\r\n          // SortName: 'Create_Date',\r\n          // SortOrder: 'DESC',\r\n          // Search: '',\r\n          // ParameterJson: JSON.stringify({\r\n          //   ProjectCode: this.searchForm.ProjectCode,\r\n          //   ProjectAbbr: this.searchForm.ProjectAbbr\r\n          // })\r\n        }\r\n\r\n        const res = await GetProjectPageList(params)\r\n        if (res.IsSucceed) {\r\n          const table = res.Data.Data || []\r\n\r\n          // 为数据添加行号\r\n          table.forEach((item, index) => {\r\n            // item.IscheckMethod = undefined\r\n          })\r\n          this.tableConfig.tableData = table\r\n        } else {\r\n          this.$message.error(res.Message || '获取项目列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取项目列表失败:', error)\r\n        this.$message.error('获取项目列表失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    handleGridData(data) {\r\n      console.log(data, 3313)\r\n      return data\r\n    },\r\n    // 搜索\r\n    handleSearch() {\r\n      this.tableConfig.currentPage = 1\r\n      this.fetchProjectList()\r\n    },\r\n\r\n    // 重置\r\n    handleReset() {\r\n      this.searchForm = {\r\n        ProjectCode: '',\r\n        ProjectAbbr: ''\r\n      }\r\n      this.tableConfig.currentPage = 1\r\n      this.fetchProjectList()\r\n    },\r\n\r\n    // 选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedProjects = selection\r\n    },\r\n\r\n    // 行点击\r\n    handleRowClick(row) {\r\n      // 可以在这里添加行点击逻辑\r\n      console.log('点击行:', row)\r\n    },\r\n\r\n    // 取消\r\n    handleCancel() {\r\n      this.$emit('close')\r\n    },\r\n\r\n    // 确认选择\r\n    handleConfirm() {\r\n      if (this.selectedProjects.length === 0) {\r\n        this.$message.warning('请至少选择一个项目')\r\n        return\r\n      }\r\n\r\n      // 这里可以处理选中的项目数据\r\n      console.log('选中的项目:', this.selectedProjects)\r\n\r\n      // 触发父组件事件\r\n      this.$emit('close')\r\n      this.$emit('getTreeList')\r\n\r\n      this.$message.success(`已选择 ${this.selectedProjects.length} 个项目`)\r\n    },\r\n    handleAddToList() {\r\n      console.log('加入列表')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.add-project-container {\r\n  height:70vh;\r\n\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .search-section {\r\n    background: #fff;\r\n    border-radius: 4px;\r\n  }\r\n\r\n  .instruction-section {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n\r\n  .instruction {\r\n    background: #f0f9ff;\r\n    border: 1px solid #b3d8ff;\r\n    color: #1890ff;\r\n    padding: 12px 16px;\r\n    border-radius: 4px;\r\n    margin-bottom: 16px;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .table-section {\r\n    flex: 1;\r\n    background: #fff;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .footer-actions {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    padding: 0px 8px 0 0;\r\n    background: #fff;\r\n  }\r\n}\r\n</style>\r\n"]}]}
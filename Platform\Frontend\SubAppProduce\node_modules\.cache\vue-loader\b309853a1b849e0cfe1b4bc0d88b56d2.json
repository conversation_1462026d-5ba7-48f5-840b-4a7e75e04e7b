{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\quality_Inspection\\quality_summary\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\quality_Inspection\\quality_summary\\index.vue", "mtime": 1757572678842}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgZnVsbENoZWNrIGZyb20gJy4vY29tcG9uZW50cy9mdWxsQ2hlY2sudnVlJw0KaW1wb3J0IHNwb3RDaGVjayBmcm9tICcuL2NvbXBvbmVudHMvc3BvdENoZWNrLnZ1ZScNCmltcG9ydCB7IEdldERpY3Rpb25hcnlEZXRhaWxMaXN0QnlDb2RlLCBHZXROb2RlTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snDQppbXBvcnQgeyBHZXRQcm9qZWN0UGFnZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vcHJvamVjdCcNCmltcG9ydCBTZWxlY3RVc2VyIGZyb20gJ0AvY29tcG9uZW50cy9TZWxlY3QvU2VsZWN0VXNlci9pbmRleC52dWUnDQppbXBvcnQgeyBDaGFuZ2VDaGVja1VzZXIgfSBmcm9tICdAL2FwaS9QUk8vcXVhbGl0eUluc3BlY3QvcXVhbGl0eS1tYW5hZ2VtZW50Jw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdQUk9TdGFydEluc3BlY3QnLA0KICBjb21wb25lbnRzOiB7DQogICAgU2VsZWN0VXNlciwNCiAgICBmdWxsQ2hlY2ssDQogICAgc3BvdENoZWNrDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGV4cG9ydExvYWRpbmc6IGZhbHNlLA0KICAgICAgYWN0aXZlTmFtZTogJ+WFqOajgCcsDQogICAgICBmb3JtOiB7DQogICAgICAgIFN0YXR1czogJycsIC8vIOWNleaNrueKtuaAgQ0KICAgICAgICBDaGVja19SZXN1bHQ6ICcnLCAvLyDotKjmo4Dnu5PmnpwNCiAgICAgICAgUHJvamVjdF9JZDogJycsIC8vIOmhueebruWQjeensA0KICAgICAgICBDaGVja19PYmplY3RfVHlwZTogJycsIC8vIOi0qOajgOWvueixoQ0KICAgICAgICBTdGVlbE5hbWU6ICcnLCAvLyDlkI3np7ANCiAgICAgICAgQ2hlY2tfTm9kZV9JZDogJycsIC8vIOi0qOajgOiKgueCuQ0KICAgICAgICBOdW1iZXJfTGlrZTogJycsIC8vIOi0qOajgOWNleWPtw0KICAgICAgICBQaWNrX0RhdGU6IFtdLCAvLyDotKjmo4Dml7bpl7QNCiAgICAgICAgQmVnaW5EYXRlOiBudWxsLA0KICAgICAgICBFbmREYXRlOiBudWxsLA0KICAgICAgICBDaGVja19Vc2VySWRzOiBbXQ0KICAgICAgfSwNCiAgICAgIENoZWNrTm9kZUxpc3Q6IFtdLCAvLyDotKjmo4DoioLngrkNCiAgICAgIENoZWNrT2JqZWN0RGF0YTogW10sIC8vIOi0qOajgOWvueixoQ0KICAgICAgY2hlY2tfb2JqZWN0X2lkOiBudWxsLA0KICAgICAgUHJvamVjdE5hbWVEYXRhOiBbXSwNCiAgICAgIENoZWNrX1N0eWxlOiAnMScsDQogICAgICBzZWxlY3RMaXN0OiBbXSwNCiAgICAgIGFzc2lnbkxvYWRpbmc6IGZhbHNlLA0KICAgICAgYXNzaWduRGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBhc3NpZ25Gb3JtOiB7DQogICAgICAgIHVzZXJJZHM6IFtdDQogICAgICB9IC8vIOW9k+WJjeimgeWIhumFjeeahOihjOaVsOaNrg0KICAgIH0NCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICBhY3RpdmVOYW1lOiB7DQogICAgICBoYW5kbGVyKG5ld05hbWUsIG9sZE5hbWUpIHsNCiAgICAgICAgdGhpcy5zZWxlY3RMaXN0ID0gW10NCiAgICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICAgIFN0YXR1czogJycsIC8vIOWNleaNrueKtuaAgQ0KICAgICAgICAgIENoZWNrX1Jlc3VsdDogJycsIC8vIOi0qOajgOe7k+aenA0KICAgICAgICAgIFByb2plY3RfSWQ6ICcnLCAvLyDpobnnm67lkI3np7ANCiAgICAgICAgICBDaGVja19PYmplY3RfVHlwZTogJycsIC8vIOi0qOajgOWvueixoQ0KICAgICAgICAgIFN0ZWVsTmFtZTogJycsIC8vIOWQjeensA0KICAgICAgICAgIENoZWNrX05vZGVfSWQ6ICcnLCAvLyDotKjmo4DoioLngrkNCiAgICAgICAgICBOdW1iZXJfTGlrZTogJycgLy8g6LSo5qOA5Y2V5Y+3DQogICAgICAgIH0NCiAgICAgICAgaWYgKG5ld05hbWUgPT09ICflhajmo4AnKSB7DQogICAgICAgICAgdGhpcy5DaGVja19TdHlsZSA9ICcxJw0KICAgICAgICB9IGVsc2UgaWYgKG5ld05hbWUgPT09ICfmir3mo4AnKSB7DQogICAgICAgICAgdGhpcy5DaGVja19TdHlsZSA9ICcwJw0KICAgICAgICB9DQogICAgICAgIHRoaXMuZXhwb3J0TG9hZGluZyA9IGZhbHNlDQogICAgICB9LA0KDQogICAgICBkZWVwOiB0cnVlDQogICAgfQ0KICB9LA0KICBtb3VudGVkKCkgew0KICAgIHRoaXMuZ2V0Q2hlY2tUeXBlKCkNCiAgICB0aGlzLmdldFByb2plY3RPcHRpb24oKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLy8g6I635Y+W6aG555uuDQogICAgZ2V0UHJvamVjdE9wdGlvbigpIHsNCiAgICAgIEdldFByb2plY3RQYWdlTGlzdCh7DQogICAgICAgIFBhZ2U6IDEsDQogICAgICAgIFBhZ2VTaXplOiAtMQ0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy5Qcm9qZWN0TmFtZURhdGEgPSByZXMuRGF0YS5EYXRhDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgZ2V0Q2hlY2tUeXBlKCkgew0KICAgICAgR2V0RGljdGlvbmFyeURldGFpbExpc3RCeUNvZGUoeyBkaWN0aW9uYXJ5Q29kZTogJ1F1YWxpdHlfQ29kZScgfSkNCiAgICAgICAgLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICB0aGlzLkNoZWNrT2JqZWN0RGF0YSA9IHJlcy5EYXRhDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InLA0KICAgICAgICAgICAgICBtZXNzYWdlOiAncmVzLk1lc3NhZ2UnDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICBjb25zb2xlLmxvZygnc2RmZCcpDQogICAgICAgIH0pDQogICAgfSwNCiAgICBjaGFuZ2VPYmplY3QodmFsKSB7DQogICAgICBjb25zb2xlLmxvZygndmFsJywgdGhpcy5mb3JtLkNoZWNrX09iamVjdF9UeXBlKQ0KICAgICAgdGhpcy5mb3JtLkNoZWNrX05vZGVfSWQgPSAnJw0KICAgICAgY29uc3QgY2hlY2tPYmogPSB0aGlzLkNoZWNrT2JqZWN0RGF0YS5maW5kKCh2KSA9PiB7DQogICAgICAgIHJldHVybiB2LkRpc3BsYXlfTmFtZSA9PT0gdmFsDQogICAgICB9KT8uSWQNCiAgICAgIGNvbnNvbGUubG9nKHRoaXMuY2hlY2tfb2JqZWN0X2lkKQ0KDQogICAgICBHZXROb2RlTGlzdCh7IGNoZWNrX29iamVjdF9pZDogY2hlY2tPYmosIENoZWNrX1N0eWxlOiB0aGlzLkNoZWNrX1N0eWxlIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMuQ2hlY2tOb2RlTGlzdCA9IHJlcy5EYXRhDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICB0eXBlOiAnZXJyb3InLA0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlU2VhcmNoKCkgew0KICAgICAgY29uc29sZS5sb2coJ3RoaXMuZm9ybS5Db2RlJywgdGhpcy5mb3JtLkNoZWNrX09iamVjdF9UeXBlKQ0KICAgICAgLy8gdGhpcy5mb3JtLkNvZGUgPSB0aGlzLmZvcm0uU2VhcmNoQ29kZS50cmltKCkucmVwbGFjZUFsbCgiICIsICJcbiIpOw0KICAgICAgaWYgKHRoaXMuYWN0aXZlTmFtZSA9PT0gJ+WFqOajgCcpIHsNCiAgICAgICAgdGhpcy4kcmVmcy5mdWxsQ2hlY2tSZWYuZmV0Y2hEYXRhKDEpDQogICAgICB9IGVsc2UgaWYgKHRoaXMuYWN0aXZlTmFtZSA9PT0gJ+aKveajgCcpIHsNCiAgICAgICAgdGhpcy4kcmVmcy5zcG90Q2hlY2tSZWYuZmV0Y2hEYXRhKDEpDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICBpZiAodGhpcy5hY3RpdmVOYW1lID09PSAn5YWo5qOAJykgew0KICAgICAgICB0aGlzLiRyZWZzLmZ1bGxDaGVja1JlZi5leHBvcnRUYigpDQogICAgICB9IGVsc2UgaWYgKHRoaXMuYWN0aXZlTmFtZSA9PT0gJ+aKveajgCcpIHsNCiAgICAgICAgdGhpcy4kcmVmcy5zcG90Q2hlY2tSZWYuZXhwb3J0VGIoKQ0KICAgICAgfQ0KICAgIH0sDQogICAgc2V0RXhwb3J0TG9hZGluZyh2YWwpIHsNCiAgICAgIGNvbnNvbGUubG9nKCd2JywgdmFsKQ0KICAgICAgdGhpcy5leHBvcnRMb2FkaW5nID0gdmFsDQogICAgfSwNCiAgICAvLyDotKjmo4DliIbphY3nm7jlhbPmlrnms5UNCiAgICBoYW5kbGVRdWFsaXR5QXNzaWduKCkgew0KICAgICAgaWYgKHRoaXMuc2VsZWN0TGlzdC5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjpgInmi6nopoHliIbphY3nmoTmlbDmja4nKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIHRoaXMuYXNzaWduRm9ybS51c2VySWRzID0gW10NCiAgICAgIHRoaXMuYXNzaWduRGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIGhhbmRsZUFzc2lnbkNsb3NlKCkgew0KICAgICAgdGhpcy5hc3NpZ25EaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICAgIHRoaXMuYXNzaWduRm9ybS51c2VySWRzID0gW10NCiAgICB9LA0KICAgIGhhbmRsZUFzc2lnblNhdmUoKSB7DQogICAgICBpZiAoIXRoaXMuYXNzaWduRm9ybS51c2VySWRzIHx8IHRoaXMuYXNzaWduRm9ybS51c2VySWRzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+mAieaLqei0qOajgOS6uuWRmCcpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICB0aGlzLmFzc2lnbkxvYWRpbmcgPSB0cnVlDQoNCiAgICAgIC8vIOiwg+eUqOi0qOajgOWIhumFjeaOpeWPow0KICAgICAgY29uc3Qgc2hlZXRJZHMgPSB0aGlzLnNlbGVjdExpc3QubWFwKGl0ZW0gPT4gaXRlbS5TaGVldElkKQ0KICAgICAgQ2hhbmdlQ2hlY2tVc2VyKHsNCiAgICAgICAgaWRzOiBzaGVldElkcywNCiAgICAgICAgdXNlcklkczogdGhpcy5hc3NpZ25Gb3JtLnVzZXJJZHMNCiAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+i0qOajgOWIhumFjeaIkOWKnycpDQogICAgICAgICAgdGhpcy5oYW5kbGVBc3NpZ25DbG9zZSgpDQogICAgICAgICAgdGhpcy5oYW5kbGVTZWFyY2goKSAvLyDliLfmlrDliJfooajmlbDmja4NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5NZXNzYWdlIHx8ICfotKjmo4DliIbphY3lpLHotKUnKQ0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+i0qOajgOWIhumFjeWksei0pScpDQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+i0qOajgOWIhumFjemUmeivrzonLCBlcnJvcikNCiAgICAgIH0pLmZpbmFsbHkoKCkgPT4gew0KICAgICAgICB0aGlzLmFzc2lnbkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8MA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/quality_Inspection/quality_summary", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <div class=\"wrapper-c\">\r\n      <div class=\"header_tab\">\r\n        <el-tabs v-model=\"activeName\">\r\n          <el-tab-pane label=\"全检\" name=\"全检\" />\r\n          <el-tab-pane label=\"抽检\" name=\"抽检\" />\r\n        </el-tabs>\r\n      </div>\r\n      <div class=\"search_wrapper\">\r\n        <el-form ref=\"form\" :model=\"form\" label-width=\"80px\">\r\n          <el-row>\r\n            <el-col :span=\"5\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item label=\"质检对象\" prop=\"Check_Object_Type\">\r\n                <el-select\r\n                  v-model=\"form.Check_Object_Type\"\r\n                  filterable\r\n                  clearable\r\n                  placeholder=\"请选择\"\r\n                  @change=\"changeObject\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in CheckObjectData\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Display_Name\"\r\n                    :value=\"item.Display_Name\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item label=\"质检节点\" prop=\"Check_Node_Id\">\r\n                <el-select\r\n                  v-model=\"form.Check_Node_Id\"\r\n                  filterable\r\n                  clearable\r\n                  placeholder=\"请选择\"\r\n                  :disabled=\"!form.Check_Object_Type\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in CheckNodeList\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Display_Name\"\r\n                    :value=\"item.Id\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\" :lg=\"4\" :xl=\"4\">\r\n              <el-form-item label=\"质检结果\" prop=\"Check_Result\">\r\n                <el-select\r\n                  v-model=\"form.Check_Result\"\r\n                  filterable\r\n                  clearable\r\n                  placeholder=\"请选择\"\r\n                >\r\n                  <el-option label=\"合格\" :value=\"'合格'\" />\r\n                  <el-option label=\"不合格\" :value=\"'不合格'\" />\r\n                  <el-option v-if=\"activeName === '全检'\" label=\"未一次合格\" :value=\"'未一次合格'\" />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item label=\"质检时间\" prop=\"Pick_Date\">\r\n                <el-date-picker\r\n                  v-model=\"form.Pick_Date\"\r\n                  type=\"daterange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item label=\"质检人\" prop=\"Check_UserIds\">\r\n                <SelectUser v-model=\"form.Check_UserIds\" multiple collapse-tags />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row>\r\n            <el-col :span=\"5\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item label=\"质检单号\" prop=\"Number_Like\">\r\n                <el-input\r\n                  v-model=\"form.Number_Like \"\r\n                  type=\"text\"\r\n                  placeholder=\"请输入质检单号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item\r\n                v-if=\"activeName == '全检'\"\r\n                label=\"名称\"\r\n                prop=\"SteelName\"\r\n              >\r\n                <el-input\r\n                  v-model=\"form.SteelName\"\r\n                  type=\"text\"\r\n                  placeholder=\"请输入（空格间隔筛选多个）\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\" :lg=\"4\" :xl=\"4\">\r\n              <el-form-item label=\"单据状态\" prop=\"Status\">\r\n                <el-select\r\n                  v-model=\"form.Status\"\r\n                  filterable\r\n                  clearable\r\n                  placeholder=\"请选择\"\r\n                >\r\n                  <el-option label=\"草稿\" :value=\"'草稿'\" />\r\n                  <el-option label=\"待整改\" :value=\"'待整改'\" />\r\n                  <el-option label=\"待复核\" :value=\"'待复核'\" />\r\n                  <el-option label=\"待质检\" :value=\"'待质检'\" />\r\n                  <el-option label=\"已完成\" :value=\"'已完成'\" />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item\r\n                v-if=\"activeName == '全检'\"\r\n                label=\"项目名称\"\r\n                prop=\"Project_Id\"\r\n              >\r\n                <el-select\r\n                  v-model=\"form.Project_Id\"\r\n                  filterable\r\n                  clearable\r\n                  placeholder=\"请选择\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in ProjectNameData\"\r\n                    :key=\"item.Id\"\r\n                    :label=\"item.Short_Name\"\r\n                    :value=\"item.Sys_Project_Id\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\" :lg=\"5\" :xl=\"5\">\r\n              <el-form-item label-width=\"16px\">\r\n                <el-button type=\"primary\" @click=\"handleSearch\">搜索</el-button>\r\n                <el-button\r\n                  @click=\"\r\n                    $refs['form'].resetFields();\r\n                    handleSearch();\r\n                  \"\r\n                >重置</el-button>\r\n                <el-button type=\"success\" :loading=\"exportLoading\" @click=\"handleExport\">导出</el-button>\r\n                <el-button\r\n                  type=\"primary\"\r\n                  :disabled=\"selectList.length == 0\"\r\n                  @click=\"handleQualityAssign\"\r\n                >质检分配</el-button>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form>\r\n      </div>\r\n\r\n      <div class=\"main-wrapper\">\r\n        <!--        <el-button style=\"margin: 10px 0 0 10px\" @click=\"\">导出</el-button>-->\r\n\r\n        <full-check\r\n          v-if=\"activeName == '全检'\"\r\n          ref=\"fullCheckRef\"\r\n          :search-detail=\"form\"\r\n          @setExportLoading=\"setExportLoading\"\r\n          @selectChange=\"(val)=>selectList = val\"\r\n        />\r\n        <spot-check\r\n          v-if=\"activeName == '抽检'\"\r\n          ref=\"spotCheckRef\"\r\n          :search-detail=\"form\"\r\n          @setExportLoading=\"setExportLoading\"\r\n          @selectChange=\"(val)=>selectList = val\"\r\n        />\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 质检分配弹窗 -->\r\n    <el-dialog\r\n      title=\"质检分配\"\r\n      :visible.sync=\"assignDialogVisible\"\r\n      width=\"400px\"\r\n      class=\"plm-custom-dialog\"\r\n      @close=\"handleAssignClose\"\r\n    >\r\n      <el-form :model=\"assignForm\" label-width=\"80px\">\r\n        <el-form-item label=\"选择人员\" required>\r\n          <SelectUser\r\n            v-model=\"assignForm.userIds\"\r\n            placeholder=\"请选择质检人员\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"handleAssignClose\">取消</el-button>\r\n        <el-button type=\"primary\" :loading=\"assignLoading\" @click=\"handleAssignSave\">保存</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport fullCheck from './components/fullCheck.vue'\r\nimport spotCheck from './components/spotCheck.vue'\r\nimport { GetDictionaryDetailListByCode, GetNodeList } from '@/api/PRO/factorycheck'\r\nimport { GetProjectPageList } from '@/api/PRO/project'\r\nimport SelectUser from '@/components/Select/SelectUser/index.vue'\r\nimport { ChangeCheckUser } from '@/api/PRO/qualityInspect/quality-management'\r\n\r\nexport default {\r\n  name: 'PROStartInspect',\r\n  components: {\r\n    SelectUser,\r\n    fullCheck,\r\n    spotCheck\r\n  },\r\n  data() {\r\n    return {\r\n      exportLoading: false,\r\n      activeName: '全检',\r\n      form: {\r\n        Status: '', // 单据状态\r\n        Check_Result: '', // 质检结果\r\n        Project_Id: '', // 项目名称\r\n        Check_Object_Type: '', // 质检对象\r\n        SteelName: '', // 名称\r\n        Check_Node_Id: '', // 质检节点\r\n        Number_Like: '', // 质检单号\r\n        Pick_Date: [], // 质检时间\r\n        BeginDate: null,\r\n        EndDate: null,\r\n        Check_UserIds: []\r\n      },\r\n      CheckNodeList: [], // 质检节点\r\n      CheckObjectData: [], // 质检对象\r\n      check_object_id: null,\r\n      ProjectNameData: [],\r\n      Check_Style: '1',\r\n      selectList: [],\r\n      assignLoading: false,\r\n      assignDialogVisible: false,\r\n      assignForm: {\r\n        userIds: []\r\n      } // 当前要分配的行数据\r\n    }\r\n  },\r\n  watch: {\r\n    activeName: {\r\n      handler(newName, oldName) {\r\n        this.selectList = []\r\n        this.form = {\r\n          Status: '', // 单据状态\r\n          Check_Result: '', // 质检结果\r\n          Project_Id: '', // 项目名称\r\n          Check_Object_Type: '', // 质检对象\r\n          SteelName: '', // 名称\r\n          Check_Node_Id: '', // 质检节点\r\n          Number_Like: '' // 质检单号\r\n        }\r\n        if (newName === '全检') {\r\n          this.Check_Style = '1'\r\n        } else if (newName === '抽检') {\r\n          this.Check_Style = '0'\r\n        }\r\n        this.exportLoading = false\r\n      },\r\n\r\n      deep: true\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getCheckType()\r\n    this.getProjectOption()\r\n  },\r\n  methods: {\r\n    // 获取项目\r\n    getProjectOption() {\r\n      GetProjectPageList({\r\n        Page: 1,\r\n        PageSize: -1\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.ProjectNameData = res.Data.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getCheckType() {\r\n      GetDictionaryDetailListByCode({ dictionaryCode: 'Quality_Code' })\r\n        .then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.CheckObjectData = res.Data\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: 'res.Message'\r\n            })\r\n          }\r\n        })\r\n        .catch(() => {\r\n          console.log('sdfd')\r\n        })\r\n    },\r\n    changeObject(val) {\r\n      console.log('val', this.form.Check_Object_Type)\r\n      this.form.Check_Node_Id = ''\r\n      const checkObj = this.CheckObjectData.find((v) => {\r\n        return v.Display_Name === val\r\n      })?.Id\r\n      console.log(this.check_object_id)\r\n\r\n      GetNodeList({ check_object_id: checkObj, Check_Style: this.Check_Style }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.CheckNodeList = res.Data\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleSearch() {\r\n      console.log('this.form.Code', this.form.Check_Object_Type)\r\n      // this.form.Code = this.form.SearchCode.trim().replaceAll(\" \", \"\\n\");\r\n      if (this.activeName === '全检') {\r\n        this.$refs.fullCheckRef.fetchData(1)\r\n      } else if (this.activeName === '抽检') {\r\n        this.$refs.spotCheckRef.fetchData(1)\r\n      }\r\n    },\r\n    handleExport() {\r\n      if (this.activeName === '全检') {\r\n        this.$refs.fullCheckRef.exportTb()\r\n      } else if (this.activeName === '抽检') {\r\n        this.$refs.spotCheckRef.exportTb()\r\n      }\r\n    },\r\n    setExportLoading(val) {\r\n      console.log('v', val)\r\n      this.exportLoading = val\r\n    },\r\n    // 质检分配相关方法\r\n    handleQualityAssign() {\r\n      if (this.selectList.length === 0) {\r\n        this.$message.warning('请先选择要分配的数据')\r\n        return\r\n      }\r\n      this.assignForm.userIds = []\r\n      this.assignDialogVisible = true\r\n    },\r\n    handleAssignClose() {\r\n      this.assignDialogVisible = false\r\n      this.assignForm.userIds = []\r\n    },\r\n    handleAssignSave() {\r\n      if (!this.assignForm.userIds || this.assignForm.userIds.length === 0) {\r\n        this.$message.warning('请选择质检人员')\r\n        return\r\n      }\r\n\r\n      this.assignLoading = true\r\n\r\n      // 调用质检分配接口\r\n      const sheetIds = this.selectList.map(item => item.SheetId)\r\n      ChangeCheckUser({\r\n        ids: sheetIds,\r\n        userIds: this.assignForm.userIds\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message.success('质检分配成功')\r\n          this.handleAssignClose()\r\n          this.handleSearch() // 刷新列表数据\r\n        } else {\r\n          this.$message.error(res.Message || '质检分配失败')\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('质检分配失败')\r\n        console.error('质检分配错误:', error)\r\n      }).finally(() => {\r\n        this.assignLoading = false\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.wrapper-c {\r\n  height: 100%;\r\n  background: #fff;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .main-wrapper {\r\n    background: #ffffff;\r\n    // height: 0;\r\n    flex: 1;\r\n    // display: flex;\r\n    // flex-direction: column;\r\n  }\r\n  .header_tab {\r\n    padding: 0 16px 0 16px;\r\n    box-sizing: border-box;\r\n  }\r\n}\r\n.search_wrapper {\r\n  padding: 16px 16px 0;\r\n  box-sizing: border-box;\r\n  ::v-deep .el-form-item {\r\n    .el-form-item__content {\r\n      & > .el-input {\r\n        width: 100%;\r\n      }\r\n      & > .el-select {\r\n        width: 100%;\r\n      }\r\n    }\r\n    .el-date-editor--daterange.el-input__inner {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .el-tabs__header {\r\n  margin: 0 !important;\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\index.vue?vue&type=style&index=0&id=7290e011&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\index.vue", "mtime": 1758080281592}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuVA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/project-config/project-quality", "sourcesContent": ["<template>\n  <div class=\"app-container abs100\">\n    <ProjectData @setProjectData=\"setProjectData\" />\n    <div v-loading=\"pageLoading\" class=\"cs-z-page-main-content\">\n      <div class=\"bom-list\">\n        <div class=\"title\">\n          <span\n            v-for=\"(item, index) in title\"\n            :key=\"index\"\n            style=\"cursor: pointer\"\n            :class=\"spanCurr == index ? 'clickindex' : 'index'\"\n            @click=\"handelIndex(index, item)\"\n          >{{ item.Display_Name }}</span>\n        </div>\n        <div class=\"btns\">\n          <el-button type=\"primary\" @click=\"handleAddProject\">同步项目配置</el-button>\n          <el-button type=\"primary\" @click=\"handleAddFactory\">恢复工厂默认配置</el-button>\n        </div>\n      </div>\n      <div class=\"detail\">\n        <template>\n          <el-tabs\n            v-model=\"activeName\"\n            type=\"card\"\n            style=\"width: 100%; height: 100%\"\n          >\n            <el-tab-pane label=\"检查类型\" name=\"检查类型\">\n              <CheckType\n                ref=\"checkTypeRef\"\n                :check-type=\"checkType\"\n                :sys-project-id=\"sysProjectId\"\n                @optionFn=\"optionEdit\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"检查项\" name=\"检查项\">\n              <CheckItem\n                ref=\"checkItemRef\"\n                :check-type=\"checkType\"\n                :sys-project-id=\"sysProjectId\"\n                @ItemEdit=\"ItemEdit\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"检查项组合\" name=\"检查项组合\">\n              <CheckCombination\n                ref=\"checkCombinationRef\"\n                :check-type=\"checkType\"\n                :sys-project-id=\"sysProjectId\"\n                @CombinationEdit=\"CombinationEdit\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"质检节点配置\" name=\"质检节点配置\">\n              <CheckNode\n                ref=\"checkNodeRef\"\n                :check-type=\"checkType\"\n                :sys-project-id=\"sysProjectId\"\n                @NodeEdit=\"NodeEdit\"\n              />\n            </el-tab-pane>\n            <el-tab-pane v-if=\"isCom\" label=\"公差配置\" name=\"公差配置\">\n              <ToleranceConfig\n                ref=\"toleranceConfigRef\"\n                :check-type=\"checkType\"\n                :sys-project-id=\"sysProjectId\"\n                @edit=\"addToleranceConfig\"\n              />\n            </el-tab-pane>\n            <el-button\n              v-if=\"activeName==='检查项组合'\"\n              type=\"primary\"\n              class=\"addbtn\"\n              @click=\"addData\"\n            >新增</el-button>\n          </el-tabs>\n        </template>\n      </div>\n    </div>\n    <el-dialog\n      v-if=\"dialogVisible\"\n      ref=\"content\"\n      v-el-drag-dialog\n      :title=\"dialogTitle\"\n      :visible.sync=\"dialogVisible\"\n      :close-on-click-modal=\"false\"\n      :width=\"width\"\n      class=\"z-dialog\"\n      @close=\"handleClose\"\n    >\n      <component\n        :is=\"currentComponent\"\n        ref=\"content\"\n        :dialog-data=\"dialogData\"\n        :sys-project-id=\"sysProjectId\"\n        @ToleranceRefresh=\"ToleranceRefresh\"\n        @refresh=\"handleRefresh\"\n        @close=\"handleClose\"\n      />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport ProjectData from '../components/ProjectData.vue'\nimport CheckType from './components/CheckType' // 检查类型\nimport CheckCombination from './components/CheckCombination' // 检查项组合\nimport CheckNode from './components/CheckNode' // 质检节点配置\nimport CheckItem from './components/CheckItem' // 检查项\nimport TypeDialog from './components/Dialog/TypeDialog' // 检查类型弹窗\nimport ItemDialog from './components/Dialog/ItemDialog' // 检查项弹窗\nimport CombinationDialog from './components/Dialog/CombinationDialog' // 检查项组合弹窗\nimport NodeDialog from './components/Dialog/NodeDialog' // 质检节点组合弹窗\nimport ToleranceConfig from './components/ToleranceConfig.vue' // 公差配置\nimport ProjectAdd from './components/Dialog/ProjectAddDialog.vue'\n// import { GetDictionaryDetailListByCode } from '@/api/PRO/factorycheck'\nimport elDragDialog from '@/directive/el-drag-dialog'\nimport ToleranceDialog from './components/Dialog/ToleranceDialog' // 公差配置弹窗\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nexport default {\n  name: 'PLMFactoryGroupList',\n  directives: { elDragDialog },\n  components: {\n    ProjectData,\n    ProjectAdd,\n    CheckType,\n    ToleranceConfig,\n    CheckCombination,\n    CheckNode,\n    CheckItem,\n    TypeDialog,\n    ItemDialog,\n    CombinationDialog,\n    NodeDialog,\n    ToleranceDialog\n  },\n  data() {\n    return {\n      spanCurr: 0,\n      title: [],\n      activeName: '检查类型',\n      checkType: {},\n      tbLoading: false,\n      tbData: [],\n      dialogVisible: false,\n      currentComponent: '',\n      dialogTitle: '',\n      isCom: false,\n      width: '60%',\n      dialogData: {},\n      sysProjectId: '',\n      pageLoading: false\n    }\n  },\n  created() {},\n  async mounted() {\n    await this.getCheckType()\n  },\n  methods: {\n    async getCheckType() {\n      const bomLevel = await GetBOMInfo()\n      this.title = bomLevel.list\n      this.checkType = bomLevel.list[0]\n      this.isCom = bomLevel.list.find(v => v.Code === '-1')\n      // GetDictionaryDetailListByCode({ dictionaryCode: 'Quality_Code' }).then(\n      //   (res) => {\n      //     if (res.IsSucceed) {\n      //       this.title = res.Data // wtf\n      //       this.checkType = this.title[0]// wtf\n      //       this.isCom = res.Data.find(v => v.Value === '0')\n      //     } else {\n      //       this.$message({\n      //         type: 'error',\n      //         message: 'res.Message'\n      //       })\n      //     }\n      //   }\n      // )\n    },\n    handelIndex(index, item) {\n      this.pageLoading = true\n      this.isCom = item.Code === '-1'\n      if (!this.isCom && this.activeName === '公差配置') {\n        this.activeName = '检查类型'\n      }\n      this.checkType = item\n      this.spanCurr = index\n      setTimeout(() => {\n        this.pageLoading = false\n      }, 500)\n    },\n    addData() {\n      console.log(this.activeName)\n      switch (this.activeName) {\n        case '检查类型':\n          this.addCheckType()\n          break\n        case '检查项':\n          this.addCheckItem()\n          break\n        case '检查项组合':\n          this.addCheckCombination()\n          break\n        case '质检节点配置':\n          this.addCheckNode()\n          break\n        case '公差配置':\n          this.addToleranceConfig()\n          break\n        default:\n          this.addCheckType()\n      }\n    },\n    addCheckType() {\n      this.width = '30%'\n      this.generateComponent('新增检查类型', 'TypeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType)\n      })\n    },\n    editCheckType(data) {\n      this.width = '30%'\n      this.generateComponent('编辑检查类型', 'TypeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType, data)\n      })\n    },\n    addCheckItem() {\n      this.width = '30%'\n      this.generateComponent('新增检查项', 'ItemDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType)\n      })\n    },\n    editCheckItem(data) {\n      this.width = '30%'\n      this.generateComponent('编辑检查项', 'ItemDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType, data)\n      })\n    },\n    addCheckCombination() {\n      this.width = '40%'\n      this.generateComponent('新增检查项组合', 'CombinationDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType)\n      })\n    },\n    editCheckCombination(data) {\n      this.width = '40%'\n      this.generateComponent('编辑检查项组合', 'CombinationDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType, data)\n      })\n    },\n    addCheckNode() {\n      this.width = '45%'\n      this.generateComponent('新增质检节点配置', 'NodeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('新增', this.checkType)\n      })\n    },\n    editCheckNode(data) {\n      this.width = '45%'\n      this.generateComponent('编辑质检节点配置', 'NodeDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init('编辑', this.checkType, data)\n      })\n    },\n    addToleranceConfig(data) {\n      this.width = '45%'\n      this.generateComponent(data?.Id ? '编辑公差配置' : '新增公差配置', 'ToleranceDialog')\n      this.$nextTick((_) => {\n        this.$refs['content'].init(data?.Id ? '编辑' : '新增', this.checkType.Id, data)\n      })\n    },\n    handleAddProject() {\n      this.width = '580px'\n      this.generateComponent('同步项目配置', 'ProjectAdd')\n    },\n    handleRefresh() {\n      switch (this.activeName) {\n        case '检查类型':\n          this.$refs.checkTypeRef.getCheckTypeList()\n          break\n        case '检查项':\n          this.$refs.checkItemRef.getCheckItemList()\n          break\n        case '检查项组合':\n          this.$refs.checkCombinationRef.getQualityList()\n          break\n        case '质检节点配置':\n          this.$refs.checkNodeRef.getNodeList()\n          break\n      }\n    },\n    handleClose() {\n      this.dialogVisible = false\n    },\n    generateComponent(title, component) {\n      this.dialogTitle = title\n      this.currentComponent = component\n      this.dialogVisible = true\n    },\n    optionEdit(data) {\n      // this.dialogData = Object.assign({},data)\n      this.editCheckType(data)\n    },\n    ItemEdit(data) {\n      this.editCheckItem(data)\n    },\n    CombinationEdit(data) {\n      this.editCheckCombination(data)\n    },\n    NodeEdit(data) {\n      this.editCheckNode(data)\n    },\n    ToleranceRefresh(data) {\n      this.$refs.toleranceConfigRef.getToleranceList()\n    },\n    setProjectData(data) {\n      this.sysProjectId = data.Sys_Project_Id\n    },\n    // 从工厂级同步\n    handleAddFactory() {\n      this.$confirm('此操作将会恢复到工厂质检配置, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.restoreFactoryProcessFromProject()\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消恢复'\n        })\n      })\n    },\n    restoreFactoryProcessFromProject() {\n\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/mixin.scss\";\n@import \"~@/styles/variables.scss\";\n.app-container{\n  display: flex;\n  flex-direction: row;\n  height: 100%;\n  .cs-z-page-main-content {\n    flex: 1;\n  }\n}\n\n.bom-list {\n  display: flex;\n  justify-content: space-between;\n  border-bottom: 1px solid #efefef;\n  margin-bottom: 16px;\n  align-items: center;\n  .title {\n    width: 100%;\n    height: 48px;\n    background-color: #ffffff;\n    .index {\n      font-size: 16px;\n      line-height: 48px;\n      margin-right: 16px;\n      padding: 0 16px;\n      display: inline-block;\n      text-align: center;\n      color: #999999;\n    }\n    .clickindex {\n      border-bottom: 2px solid #298dff;\n      font-size: 16px;\n      line-height: 46px;\n      margin-right: 16px;\n      padding: 0 16px;\n      display: inline-block;\n      text-align: center;\n      color: #298dff;\n    }\n  }\n  .btns {\n    display: flex;\n    height: 32px;\n  }\n}\n\n::v-deep {\n  .el-tabs {\n    display: inline-block;\n  }\n  .el-tabs--card .el-tabs__header {\n    border: 0;\n    margin: 0;\n  }\n  .el-tabs--card > .el-tabs__header .el-tabs__nav {\n    border-bottom: 1px solid #dfe4ed;\n  }\n  .el-tabs__content {\n    margin-top: 16px !important;\n  }\n}\n.detail {\n  height: calc(100vh - 240px);\n  box-sizing: border-box;\n}\n.addbtn {\n  position: fixed;\n  right: 38px;\n  top: 205px;\n}\n.z-dialog {\n  ::v-deep {\n    .el-dialog__header {\n      background-color: #298dff;\n\n      .el-dialog__title,\n      .el-dialog__close {\n        color: #ffffff;\n      }\n    }\n\n    .el-dialog__body {\n      max-height: 700px;\n      overflow: auto;\n      @include scrollBar;\n\n      &::-webkit-scrollbar {\n        width: 8px;\n      }\n    }\n  }\n}\n</style>\n"]}]}
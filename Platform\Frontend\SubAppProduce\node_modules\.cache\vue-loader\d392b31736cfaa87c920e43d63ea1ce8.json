{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\index.vue?vue&type=style&index=0&id=5f9c40e8&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\index.vue", "mtime": 1757583346676}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKQGltcG9ydCAifkAvc3R5bGVzL21peGluLnNjc3MiOwouY3Mtei1wYWdlLW1haW4tY29udGVudHsKICBtaW4td2lkdGg6IDEwMDBweDsKfQoKLmNzLXotdGItd3JhcHBlciB7CiAgaGVpZ2h0OiAwOwogIGZsZXg6IDE7Cn0KCjo6di1kZWVwIHsKICAuY3MtdG9wLWhlYWRlci1ib3ggewogICAgbGluZS1oZWlnaHQ6IDBweDsKICB9Cn0KCi50YiB7CiAgOjp2LWRlZXAgewogICAgQGluY2x1ZGUgc2Nyb2xsQmFyOwoKICAgICY6Oi13ZWJraXQtc2Nyb2xsYmFyIHsKICAgICAgd2lkdGg6IDhweDsKICAgIH0KICB9Cn0KCi5jcy1kaWFsb2cgewogIDo6di1kZWVwIHsKICAgIC5lbC1kaWFsb2dfX2JvZHkgewogICAgICBwYWRkaW5nOiAyMHB4IDIwcHggIWltcG9ydGFudDsKICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsKICAgIH0KICB9Cn0KCi5jcy10Yi1pY29uIHsKICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlOwp9CgouY3MtdGFnIHsKICAmOm50aC1jaGlsZCgybikgewogICAgbWFyZ2luLWxlZnQ6IDRweDsKICB9CgogICY6bnRoLWNoaWxkKG4gKyAzKSB7CiAgICBtYXJnaW4tdG9wOiA0cHg7CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6UA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/process-settings/management", "sourcesContent": ["<template>\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\n    <div class=\"cs-z-page-main-content\">\n      <top-header padding=\"0\">\n        <template #right>\n          <div style=\"display: flex;\">\n            <el-form label-width=\"80px\" :inline=\"true\">\n              <el-form-item label=\"工序名称\">\n                <el-input v-model=\"formInline.name\" clearable placeholder=\"请输入工序名称\" @keyup.enter.native=\"handleSearch\" />\n              </el-form-item>\n              <el-form-item label=\"代号\">\n                <el-input v-model=\"formInline.code\" clearable placeholder=\"请输入代号\" @keyup.enter.native=\"handleSearch\" />\n              </el-form-item>\n              <el-form-item>\n                <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\n                <el-button @click=\"reset\">重置</el-button>\n              </el-form-item>\n            </el-form>\n            <DynamicTableFields\n              title=\"表格配置\"\n              :table-config-code=\"gridCode\"\n              @updateColumn=\"changeColumn\"\n            />\n          </div>\n        </template>\n        <template #left>\n          <el-button icon=\"el-icon-plus\" type=\"primary\" size=\"small\" @click=\"handleDialog('add')\">新增</el-button>\n          <el-button type=\"success\" size=\"small\" @click=\"handleRecognitionConfig\">导入识别配置</el-button>\n          <el-button type=\"primary\" size=\"small\" @click=\"handleTakeConfig\">{{ partName }}领用配置</el-button>\n        </template>\n      </top-header>\n      <div v-loading=\"tbLoading\" class=\"fff cs-z-tb-wrapper\">\n        <vxe-table\n          ref=\"xTable\"\n          :key=\"tbKey\"\n          v-loading=\"tbLoading\"\n          :empty-render=\"{name: 'NotData'}\"\n          show-header-overflow\n          element-loading-spinner=\"el-icon-loading\"\n          element-loading-text=\"拼命加载中\"\n          empty-text=\"暂无数据\"\n          height=\"100%\"\n          :data=\"tbData\"\n          stripe\n          resizable\n          :auto-resize=\"true\"\n          class=\"cs-vxe-table\"\n          :tooltip-config=\"{ enterable: true }\"\n        >\n          <vxe-column\n            v-for=\"item in columns\"\n            :key=\"item.Code\"\n            :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n            show-overflow=\"tooltip\"\n            sortable\n            :align=\"item.Align\"\n            :field=\"item.Code\"\n            :title=\"item.Display_Name\"\n            :visible=\"item.Is_Display\"\n            :width=\"item.Width\"\n          >\n            <template #default=\"{ row }\">\n              <span v-if=\"item.Code === 'Workload_Proportion'\">\n                {{ row.Workload_Proportion ? row.Workload_Proportion + '%' : \"-\" }}\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Need_Check'\">\n                <el-tag v-if=\"row.Is_Need_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Self_Check'\">\n                <el-tag v-if=\"row.Is_Self_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Inter_Check'\">\n                <el-tag v-if=\"row.Is_Inter_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Enable'\">\n                <el-tag v-if=\"row.Is_Enable\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Nest'\">\n                <el-tag v-if=\"row.Is_Nest\" type=\"success\">是</el-tag><el-tag v-else-if=\"row.Is_Nest===false\" type=\"danger\">否</el-tag><span v-else>-</span>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Cutting'\">\n                <el-tag v-if=\"row.Is_Cutting\" type=\"success\">是</el-tag><el-tag v-else-if=\"row.Is_Cutting===false\" type=\"danger\">否</el-tag><span v-else>-</span>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Pick_Material'\">\n                <el-tag v-if=\"row.Is_Pick_Material\" type=\"success\">是</el-tag><el-tag v-else-if=\"row.Is_Pick_Material===false\" type=\"danger\">否</el-tag><span v-else>-</span>\n              </span>\n              <span v-else>{{ row[item.Code] || \"-\" }}</span>\n            </template>\n          </vxe-column>\n          <vxe-column fixed=\"right\" title=\"操作\" width=\"180\" show-overflow align=\"center\">\n            <template #default=\"{ row }\">\n              <el-button type=\"text\" size=\"small\" @click=\"handleDialog('edit', row)\">编辑</el-button>\n              <el-button type=\"text\" size=\"small\" @click=\"handleOpenDevice(row)\">关联设备</el-button>\n              <el-button class=\"txt-red\" type=\"text\" size=\"small\" @click=\"handleDelete(row.Id)\">删除</el-button>\n            </template>\n          </vxe-column>\n        </vxe-table>\n      </div>\n      <el-dialog\n        v-if=\"dialogVisible\"\n        v-dialog-drag\n        class=\"cs-dialog\"\n        :close-on-click-modal=\"false\"\n        :title=\"title\"\n        :visible.sync=\"dialogVisible\"\n        custom-class=\"dialogCustomClass\"\n        :width=\"currentComponent==='PartTakeConfig' ? '800px' : '580px'\"\n        top=\"5vh\"\n        @close=\"handleClose\"\n      >\n        <component\n          :is=\"currentComponent\"\n          ref=\"content\"\n          :total-workload-proportion=\"totalWorkloadProportion\"\n          :row-info=\"rowInfo\"\n          :type=\"type\"\n          :level=\"level\"\n          :bom-list=\"bomList\"\n          :dialog-visible=\"dialogVisible\"\n          @close=\"handleClose\"\n          @refresh=\"fetchData\"\n        />\n      </el-dialog>\n      <el-dialog\n        v-dialog-drag\n        class=\"cs-dialog\"\n        title=\"关联设备\"\n        :close-on-click-modal=\"false\"\n        :visible.sync=\"dialogVisible1\"\n        custom-class=\"dialogCustomClass\"\n        width=\"86%\"\n        top=\"5vh\"\n        @close=\"handleClose1\"\n      >\n        <AssociatedDevice ref=\"Device\" :row-data=\"rowData\" @fetchData=\"fetchData\" />\n        <!-- <span slot=\"footer\" class=\"dialog-footer\">\n          <el-button @click=\"dialogVisible1 = false\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"dialogVisible1 = false\">确 定</el-button>\n        </span> -->\n      </el-dialog>\n    </div>\n  </div>\n</template>\n\n<script>\nimport TopHeader from '@/components/TopHeader'\nimport Add from './component/Add'\nimport ZClass from './component/Group'\nimport AssociatedDevice from './component/AssociatedDevice'\nimport RecognitionConfig from './component/RecognitionConfig'\nimport PartTakeConfig from './component/PartTakeConfig'\nimport { GetProcessListBase, DeleteProcess } from '@/api/PRO/technology-lib'\nimport ElTableEmpty from '@/components/ElTableEmpty/index.vue'\nimport addRouterPage from '@/mixins/add-router-page'\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport getTbInfo from '@/mixins/PRO/get-table-info'\n\nexport default {\n  name: 'PROProcessManagement',\n  components: {\n    ElTableEmpty,\n    TopHeader,\n    Add,\n    PartTakeConfig,\n    ZClass,\n    AssociatedDevice,\n    RecognitionConfig,\n    DynamicTableFields\n  },\n  mixins: [addRouterPage, getTbInfo],\n  data() {\n    return {\n      tbLoading: false,\n      level: 0,\n      bomList: [],\n      addPageArray: [\n        {\n          path: '/AssociatedDevice',\n          hidden: true,\n          component: () => import('@/views/PRO/process-settings/management/component/AssociatedDevice.vue'),\n          name: 'AssociatedDevice',\n          meta: { title: '关联设备' }\n        }\n\n      ],\n      columns: [],\n      tbData: [],\n      currentComponent: '',\n      title: '',\n      comName: '',\n      partName: '',\n      rowInfo: null,\n      rowData: {},\n      type: '',\n      dialogVisible: false,\n      dialogVisible1: false,\n      formInline: { name: '', code: '' },\n      totalWorkloadProportion: 0,\n      tbKey: 100,\n      gridCode: 'processSettingsList'\n    }\n  },\n  async created() {\n    await this.getTableInfo()\n  },\n  mounted() {\n    this.getBOMInfo()\n    this.fetchData()\n  },\n  methods: {\n    async getTableInfo() {\n      await this.getTableConfig(this.gridCode)\n    },\n    async getBOMInfo() {\n      const { comName, partName, list } = await GetBOMInfo()\n      this.comName = comName\n      this.partName = partName\n      this.bomList = list\n    },\n    handleOpenDevice(row) {\n      this.rowData = row\n      this.dialogVisible1 = true\n      this.$nextTick(() => {\n        this.$refs.Device.clearSelec()\n      })\n      //  this.$router.push({ path: 'http://localhost:3000/produce/pro/nesting/index' })\n    },\n\n    fetchData() {\n      this.tbLoading = true\n      GetProcessListBase(this.formInline).then((res) => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data\n\n          // 计算所有 Workload_Proportion 的总和\n          this.totalWorkloadProportion = res.Data.reduce((total, item) => {\n            const proportion = parseFloat(item.Workload_Proportion) || 0\n            return total + proportion\n          }, 0)\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n        this.tbLoading = false\n        this.dialogVisible1 = false\n      })\n    },\n    handleClose() {\n      this.dialogVisible = false\n    },\n    handleClose1() {\n      this.dialogVisible1 = false\n    },\n    handleRecognitionConfig() {\n      this.title = `导入识别配置`\n      this.currentComponent = 'RecognitionConfig'\n      this.dialogVisible = true\n    },\n    handleTakeConfig() {\n      this.title = `${this.partName}领用配置`\n      this.currentComponent = 'PartTakeConfig'\n      this.dialogVisible = true\n    },\n    handleDialog(type, row) {\n      this.currentComponent = 'Add'\n      this.type = type\n      if (type === 'add') {\n        this.title = '新建'\n      } else {\n        this.title = '编辑'\n        this.rowInfo = row\n      }\n      this.dialogVisible = true\n    },\n    handleManage(row) {\n      this.currentComponent = 'ZClass'\n      this.title = '班组管理'\n      this.dialogVisible = true\n      this.$nextTick((_) => {\n        this.$refs.content.init(row)\n      })\n    },\n    handleDelete(processId) {\n      this.$confirm('是否删除当前工序?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DeleteProcess({\n            processId\n          }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                message: '删除成功',\n                type: 'success'\n              })\n              this.fetchData()\n            } else {\n              this.$message({\n                message: res.Message,\n                type: 'error'\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    },\n    async changeColumn() {\n      await this.getTableConfig(this.gridCode)\n      this.tbKey++\n    },\n    handleSearch() {\n      this.fetchData()\n    },\n    reset() {\n      this.formInline.name = ''\n      this.formInline.code = ''\n      this.fetchData()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/mixin.scss\";\n.cs-z-page-main-content{\n  min-width: 1000px;\n}\n\n.cs-z-tb-wrapper {\n  height: 0;\n  flex: 1;\n}\n\n::v-deep {\n  .cs-top-header-box {\n    line-height: 0px;\n  }\n}\n\n.tb {\n  ::v-deep {\n    @include scrollBar;\n\n    &::-webkit-scrollbar {\n      width: 8px;\n    }\n  }\n}\n\n.cs-dialog {\n  ::v-deep {\n    .el-dialog__body {\n      padding: 20px 20px !important;\n      overflow: hidden;\n    }\n  }\n}\n\n.cs-tb-icon {\n  vertical-align: middle;\n}\n\n.cs-tag {\n  &:nth-child(2n) {\n    margin-left: 4px;\n  }\n\n  &:nth-child(n + 3) {\n    margin-top: 4px;\n  }\n}\n</style>\n"]}]}
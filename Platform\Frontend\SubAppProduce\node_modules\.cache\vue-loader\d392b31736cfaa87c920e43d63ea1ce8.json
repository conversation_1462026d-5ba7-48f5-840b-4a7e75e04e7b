{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\index.vue?vue&type=style&index=0&id=5f9c40e8&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\index.vue", "mtime": 1757470958558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKQGltcG9ydCAifkAvc3R5bGVzL21peGluLnNjc3MiOwouY3Mtei1wYWdlLW1haW4tY29udGVudHsKICBtaW4td2lkdGg6IDEwMDBweDsKfQoKLnBhZ2UtaGVhZGVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIHBhZGRpbmc6ICAwOwogIG1hcmdpbi1ib3R0b206IDhweDsKCiAgLmhlYWRlci1sZWZ0IHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgZ2FwOiA4cHg7CiAgICBmbGV4LXdyYXA6IHdyYXA7CiAgfQoKICAuaGVhZGVyLXJpZ2h0IHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogIH0KfQoKLnRiIHsKICA6OnYtZGVlcCB7CiAgICBAaW5jbHVkZSBzY3JvbGxCYXI7CgogICAgJjo6LXdlYmtpdC1zY3JvbGxiYXIgewogICAgICB3aWR0aDogOHB4OwogICAgfQogIH0KfQoKLmNzLWRpYWxvZyB7CiAgOjp2LWRlZXAgewogICAgLmVsLWRpYWxvZ19fYm9keSB7CiAgICAgIG92ZXJmbG93OiBoaWRkZW47CiAgICB9CiAgfQp9CgouY3MtdGItaWNvbiB7CiAgdmVydGljYWwtYWxpZ246IG1pZGRsZTsKfQoKLmNzLXRhZyB7CiAgJjpudGgtY2hpbGQoMm4pIHsKICAgIG1hcmdpbi1sZWZ0OiA0cHg7CiAgfQoKICAmOm50aC1jaGlsZChuICsgMykgewogICAgbWFyZ2luLXRvcDogNHB4OwogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0XA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/process-settings/management", "sourcesContent": ["<template>\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\n    <div v-loading=\"pageLoading\" class=\"cs-z-page-main-content\">\n      <div class=\"page-header\">\n        <div class=\"header-left\">\n          <el-button icon=\"el-icon-plus\" type=\"primary\" size=\"small\" @click=\"handleDialog('add')\">新增</el-button>\n          <el-button type=\"success\" size=\"small\" @click=\"handleRecognitionConfig\">导入识别配置</el-button>\n          <el-button type=\"success\" size=\"small\" @click=\"handleConfig\">{{ partName }}识别配置</el-button>\n          <el-button type=\"success\" size=\"small\" @click=\"handleConfigComp\">{{ comName }}识别配置</el-button>\n          <el-button v-for=\"item in unitPartList\" :key=\"item.Code\" type=\"success\" size=\"small\" @click=\"handleUnitPartConfig(item)\">\n            {{ item.Display_Name }}识别配置</el-button>\n          <el-button type=\"primary\" size=\"small\" @click=\"handleTakeConfig\">{{ partName }}领用配置</el-button>\n        </div>\n        <div class=\"header-right\">\n          <el-form :inline=\"true\" :model=\"formInline\" class=\"demo-form-inline\" style=\"line-height: 32px\">\n            <el-form-item label=\"工序名称\">\n              <el-input v-model=\"formInline.name\" clearable placeholder=\"请输入工序名称\" />\n            </el-form-item>\n            <el-form-item label=\"代号\">\n              <el-input v-model=\"formInline.code\" clearable placeholder=\"请输入代号\" />\n            </el-form-item>\n            <el-form-item>\n              <el-button type=\"primary\" @click=\"fetchData\">查询</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n      </div>\n      <el-table class=\"cs-custom-table tb\" border stripe height=\"100%\" :data=\"tableData\" style=\"width: 100%\">\n        <template #empty>\n          <ElTableEmpty />\n        </template>\n        <el-table-column prop=\"Professional_Code\" label=\"专业\">\n          <template slot-scope=\"{ row }\">\n            {{ row.Professional_Code || '-' }}\n          </template>\n        </el-table-column>\n        <el-table-column min-width=\"150px\" prop=\"Name\" label=\"工序名称\">\n          <template slot-scope=\"{ row }\">\n            {{ row.Name || '-' }}\n          </template>\n        </el-table-column>\n        <el-table-column min-width=\"120px\" prop=\"Code\" label=\"代号\">\n          <template slot-scope=\"{ row }\">\n            {{ row.Code || '-' }}\n          </template>\n        </el-table-column>\n        <el-table-column min-width=\"150px\" prop=\"Type_Name\" label=\"类型\">\n          <template slot-scope=\"{ row }\">\n            {{ row.Type_Name || '-' }}\n            <!-- <span v-if=\"row.Type === 1\" style=\"color: #d29730\">\n              <i class=\"iconfont icon-steel cs-tb-icon\" />\n              构件工序\n            </span>\n            <span v-if=\"row.Type === 2\" style=\"color: #20bbc7\">\n              <i class=\"iconfont icon-material-filled cs-tb-icon\" />\n              零件工序\n            </span>\n            <span v-if=\"row.Type === 3\" style=\"color: #de85e4\">\n              <i class=\"iconfont icon-material-filled cs-tb-icon\" />\n              部件工序\n            </span> -->\n          </template>\n        </el-table-column>\n        <!-- <el-table-column  prop=\"Code\" label=\"工序代码\" /> -->\n\n        <el-table-column min-width=\"150px\" prop=\"Coordinate_UserName\" label=\"工序协调人\" align=\"center\">\n          <template slot-scope=\"{ row }\">\n            {{ row.Coordinate_UserName || '-' }}\n          </template>\n        </el-table-column>\n        <el-table-column min-width=\"150px\" prop=\"Is_Need_Check\" label=\"是否专检\" align=\"center\">\n          <template slot-scope=\"{ row }\">\n            <el-tag v-if=\"row.Is_Need_Check\" type=\"success\">是</el-tag>\n            <el-tag v-else type=\"danger\">否</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column min-width=\"150px\" prop=\"Check_Style\" label=\"专检方式\">\n          <template slot-scope=\"{ row }\">\n            {{\n              row.Check_Style == 0 ? \"抽检\" : row.Check_Style == 1 ? \"全检\" : \"-\"\n            }}\n          </template>\n        </el-table-column>\n        <el-table-column min-width=\"150px\" prop=\"Is_Inter_Check\" label=\"是否互检\">\n          <template slot-scope=\"{ row }\">\n            <el-tag v-if=\"row.Is_Inter_Check\" type=\"success\">是</el-tag>\n            <el-tag v-else type=\"danger\">否</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column min-width=\"150px\" prop=\"Team_Names\" label=\"加工班组\" show-overflow-tooltip>\n          <template slot-scope=\"{ row }\">\n            <div v-if=\"row.Team_Names.length\">\n              <el-tag v-for=\"(item, index) in row.Team_Names.split(';')\" :key=\"index\" class=\"cs-tag\">\n                {{ item }}\n              </el-tag>\n            </div>\n            <div v-else>{{ '-' }}</div>\n          </template>\n        </el-table-column>\n        <el-table-column min-width=\"120px\" prop=\"Is_Enable\" label=\"是否启用\" align=\"center\">\n          <template slot-scope=\"{ row }\">\n            <el-tag v-if=\"row.Is_Enable\" type=\"success\">是</el-tag>\n            <el-tag v-else type=\"danger\">否</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column min-width=\"150px\" prop=\"Is_Nest\" label=\"是否套料工序\" align=\"center\">\n          <template slot-scope=\"{ row }\">\n            <div v-if=\"row.Bom_Level === 0\">\n              <el-tag v-if=\"row.Is_Nest\" type=\"success\">是</el-tag>\n              <el-tag v-else type=\"danger\">否</el-tag>\n            </div>\n            <div v-else>\n              {{ '-' }}\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column min-width=\"150px\" prop=\"Is_Cutting\" label=\"是否下料工序\" align=\"center\">\n          <template slot-scope=\"{ row }\">\n            <div v-if=\"row.Bom_Level===0\">\n              <el-tag v-if=\"row.Is_Cutting\" type=\"success\">是</el-tag>\n              <el-tag v-else type=\"danger\">否</el-tag>\n            </div>\n            <div v-else>\n              {{ '-' }}\n            </div>\n          </template>\n        </el-table-column>\n        <el-table-column min-width=\"120px\" prop=\"Device_Name\" label=\"关联设备\">\n          <template slot-scope=\"{ row }\">\n            {{ row.Device_Name || '-' }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"Remark\" label=\"备注\" width=\"300\">\n          <template slot-scope=\"{ row }\">\n            {{ row.Remark || '-' }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" width=\"170\" fixed=\"right\">\n          <template slot-scope=\"{ row }\">\n            <el-button type=\"text\" size=\"small\" @click=\"handleDialog('edit', row)\">编辑</el-button>\n            <el-button type=\"text\" size=\"small\" @click=\"handleOpenDevice(row)\">关联设备</el-button>\n            <el-button class=\"txt-red\" type=\"text\" size=\"small\" @click=\"handleDelete(row.Id)\">删除</el-button>\n\n            <!--<el-divider direction=\"vertical\" />\n             <el-button type=\"text\" size=\"small\" @click=\"handleManage(row)\"\n              >班组管理</el-button\n            > -->\n          </template>\n        </el-table-column>\n      </el-table>\n      <el-dialog\n        v-if=\"dialogVisible\"\n        v-dialog-drag\n        class=\"cs-dialog\"\n        :close-on-click-modal=\"false\"\n        :title=\"title\"\n        :visible.sync=\"dialogVisible\"\n        custom-class=\"dialogCustomClass\"\n        width=\"580px\"\n        top=\"5vh\"\n        @close=\"handleClose\"\n      >\n        <component\n          :is=\"currentComponent\"\n          ref=\"content\"\n          :row-info=\"rowInfo\"\n          :type=\"type\"\n          :level=\"level\"\n          :bom-list=\"bomList\"\n          :dialog-visible=\"dialogVisible\"\n          @close=\"handleClose\"\n          @refresh=\"fetchData\"\n        />\n      </el-dialog>\n      <el-dialog\n        v-dialog-drag\n        class=\"cs-dialog\"\n        title=\"关联设备\"\n        :close-on-click-modal=\"false\"\n        :visible.sync=\"dialogVisible1\"\n        custom-class=\"dialogCustomClass\"\n        width=\"86%\"\n        top=\"5vh\"\n        @close=\"handleClose1\"\n      >\n        <AssociatedDevice ref=\"Device\" :row-data=\"rowData\" @fetchData=\"fetchData\" />\n        <!-- <span slot=\"footer\" class=\"dialog-footer\">\n          <el-button @click=\"dialogVisible1 = false\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"dialogVisible1 = false\">确 定</el-button>\n        </span> -->\n      </el-dialog>\n    </div>\n  </div>\n</template>\n\n<script>\nimport TopHeader from '@/components/TopHeader'\nimport Add from './component/Add'\nimport ZClass from './component/Group'\nimport AssociatedDevice from './component/AssociatedDevice'\nimport RecognitionConfig from './component/RecognitionConfig'\nimport partRecognitionConfig from './component/partRecognitionConfig'\nimport compRecognitionConfig from './component/compRecognitionConfig'\nimport unitPartRecognitionConfig from './component/unitPartRecognitionConfig'\nimport PartTakeConfig from './component/PartTakeConfig'\nimport { GetProcessListBase, DeleteProcess } from '@/api/PRO/technology-lib'\nimport ElTableEmpty from '@/components/ElTableEmpty/index.vue'\nimport addRouterPage from '@/mixins/add-router-page'\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\n\nexport default {\n  name: 'PROProcessManagement',\n  components: {\n    ElTableEmpty,\n    TopHeader,\n    Add,\n    partRecognitionConfig,\n    compRecognitionConfig,\n    PartTakeConfig,\n    ZClass,\n    AssociatedDevice,\n    unitPartRecognitionConfig,\n    RecognitionConfig\n  },\n  mixins: [addRouterPage],\n  data() {\n    return {\n      level: 0,\n      bomList: [],\n      addPageArray: [\n        {\n          path: '/AssociatedDevice',\n          hidden: true,\n          component: () => import('@/views/PRO/process-settings/management/component/AssociatedDevice.vue'),\n          name: 'AssociatedDevice',\n          meta: { title: '关联设备' }\n        }\n\n      ],\n      tableData: [],\n      currentComponent: '',\n      title: '',\n      comName: '',\n      partName: '',\n      rowInfo: null,\n      rowData: {},\n      type: '',\n      pageLoading: false,\n      dialogVisible: false,\n      dialogVisible1: false,\n      unitPartList: [],\n      formInline: { name: '', code: '' }\n    }\n  },\n\n  mounted() {\n    this.getBOMInfo()\n    this.fetchData()\n  },\n  methods: {\n    async getBOMInfo() {\n      const { comName, partName, list } = await GetBOMInfo()\n      this.comName = comName\n      this.partName = partName\n      this.bomList = list\n      this.unitPartList = list.filter(item => item.Code !== '0' && item.Code !== '-1')\n    },\n    handleOpenDevice(row) {\n      this.rowData = row\n      this.dialogVisible1 = true\n      this.$nextTick(() => {\n        this.$refs.Device.clearSelec()\n      })\n      //  this.$router.push({ path: 'http://localhost:3000/produce/pro/nesting/index' })\n    },\n\n    fetchData() {\n      this.pageLoading = true\n      GetProcessListBase(this.formInline).then((res) => {\n        if (res.IsSucceed) {\n          this.tableData = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n        this.pageLoading = false\n        this.dialogVisible1 = false\n      })\n    },\n    handleClose() {\n      this.dialogVisible = false\n    },\n    handleClose1() {\n      this.dialogVisible1 = false\n    },\n    handleRecognitionConfig() {\n      this.title = `导入识别配置`\n      this.currentComponent = 'RecognitionConfig'\n      this.dialogVisible = true\n    },\n    handleUnitPartConfig(item) {\n      this.level = +item.Code\n      this.title = `${item.Display_Name}识别配置`\n      this.currentComponent = 'unitPartRecognitionConfig'\n      this.dialogVisible = true\n    },\n    handleConfig() {\n      this.title = `${this.partName}识别配置`\n      this.currentComponent = 'partRecognitionConfig'\n      this.dialogVisible = true\n    },\n    handleConfigComp() {\n      this.title = `${this.comName}识别配置`\n      this.currentComponent = 'compRecognitionConfig'\n      this.dialogVisible = true\n    },\n    handleTakeConfig() {\n      this.title = `${this.partName}领用配置`\n      this.currentComponent = 'PartTakeConfig'\n      this.dialogVisible = true\n    },\n    handleDialog(type, row) {\n      this.currentComponent = 'Add'\n      this.type = type\n      if (type === 'add') {\n        this.title = '新建'\n      } else {\n        this.title = '编辑'\n        this.rowInfo = row\n      }\n      this.dialogVisible = true\n    },\n    handleManage(row) {\n      this.currentComponent = 'ZClass'\n      this.title = '班组管理'\n      this.dialogVisible = true\n      this.$nextTick((_) => {\n        this.$refs.content.init(row)\n      })\n    },\n    handleDelete(processId) {\n      this.$confirm('是否删除当前工序?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DeleteProcess({\n            processId\n          }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                message: '删除成功',\n                type: 'success'\n              })\n              this.fetchData()\n            } else {\n              this.$message({\n                message: res.Message,\n                type: 'error'\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/mixin.scss\";\n.cs-z-page-main-content{\n  min-width: 1000px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding:  0;\n  margin-bottom: 8px;\n\n  .header-left {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    flex-wrap: wrap;\n  }\n\n  .header-right {\n    display: flex;\n    align-items: center;\n  }\n}\n\n.tb {\n  ::v-deep {\n    @include scrollBar;\n\n    &::-webkit-scrollbar {\n      width: 8px;\n    }\n  }\n}\n\n.cs-dialog {\n  ::v-deep {\n    .el-dialog__body {\n      overflow: hidden;\n    }\n  }\n}\n\n.cs-tb-icon {\n  vertical-align: middle;\n}\n\n.cs-tag {\n  &:nth-child(2n) {\n    margin-left: 4px;\n  }\n\n  &:nth-child(n + 3) {\n    margin-top: 4px;\n  }\n}\n</style>\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\components\\ExportCustomReport\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\components\\ExportCustomReport\\index.vue", "mtime": 1757468127968}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["ExportReportByTemplate", "GetReportTemplateList", "combineURL", "name", "props", "type", "String", "default", "code", "required", "ids", "Array", "jsonData", "data", "list", "loading", "watch", "handler", "fetchTemplates", "immediate", "methods", "_this", "TypeCode", "then", "res", "Data", "handleEmptyList", "$message", "warning", "handleSingleTemplate", "length", "commandMenu", "item", "_this2", "TemplateId", "Id", "JsonData", "SelectIds", "IsSucceed", "window", "open", "$baseUrl", "error", "Message", "finally"], "sources": ["src/components/ExportCustomReport/index.vue"], "sourcesContent": ["<template>\r\n  <!-- 当模板列表为空时 -->\r\n  <el-button\r\n    v-if=\"list.length === 0\"\r\n    type=\"primary\"\r\n    :loading=\"loading\"\r\n    :disabled=\"!ids.length\"\r\n    @click=\"handleEmptyList\"\r\n  >\r\n    {{ name }}\r\n  </el-button>\r\n\r\n  <!-- 当模板列表只有一个时 -->\r\n  <el-button\r\n    v-else-if=\"list.length === 1\"\r\n    type=\"primary\"\r\n    :loading=\"loading\"\r\n    :disabled=\"!ids.length\"\r\n    @click=\"handleSingleTemplate\"\r\n  >\r\n    {{ name }}\r\n  </el-button>\r\n\r\n  <!-- 当模板列表有多个时 -->\r\n  <el-dropdown v-else trigger=\"click\" @command=\"commandMenu\">\r\n    <el-button type=\"primary\" :loading=\"loading\" :disabled=\"!ids.length\">\r\n      {{ name }}<i class=\"el-icon-arrow-down el-icon--right\" />\r\n    </el-button>\r\n    <el-dropdown-menu slot=\"dropdown\">\r\n      <el-dropdown-item v-for=\"item in list\" :key=\"item.Id\" :command=\"item\">{{ item.Template_Name }}</el-dropdown-item>\r\n    </el-dropdown-menu>\r\n  </el-dropdown>\r\n</template>\r\n<script>\r\nimport { ExportReportByTemplate, GetReportTemplateList } from '@/api/sys/custom-template'\r\nimport { combineURL } from '@/utils'\r\n\r\nexport default {\r\n  name: 'ExportCustomReport',\r\n  props: {\r\n    // 按钮名称\r\n    name: {\r\n      type: String,\r\n      default: '导出自定义报表'\r\n    },\r\n    code: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    // 业务id\r\n    ids: {\r\n      type: Array,\r\n      default: function() {\r\n        return []\r\n      }\r\n    },\r\n    jsonData: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      list: [],\r\n      loading: false\r\n    }\r\n  },\r\n  watch: {\r\n    code: {\r\n      handler() {\r\n        this.fetchTemplates()\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  methods: {\r\n    fetchTemplates() {\r\n      GetReportTemplateList({\r\n        TypeCode: this.code\r\n      }).then(res => {\r\n        this.list = res.Data\r\n      })\r\n    },\r\n    // 处理空模板列表的情况\r\n    handleEmptyList() {\r\n      this.$message.warning('未配置自定义模板，请先配置模板')\r\n    },\r\n    // 处理单个模板的情况\r\n    handleSingleTemplate() {\r\n      if (this.list.length === 1) {\r\n        this.commandMenu(this.list[0])\r\n      }\r\n    },\r\n    // 处理多个模板的情况\r\n    commandMenu(item) {\r\n      this.loading = true\r\n      ExportReportByTemplate({\r\n        TypeCode: this.code,\r\n        TemplateId: item.Id,\r\n        JsonData: this.jsonData,\r\n        SelectIds: this.ids\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        } else {\r\n          this.$message.error(res.Message)\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,SAAAA,sBAAA,EAAAC,qBAAA;AACA,SAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;IACAD,IAAA;MACAE,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,IAAA;MACAH,IAAA,EAAAC,MAAA;MACAG,QAAA;IACA;IACA;IACAC,GAAA;MACAL,IAAA,EAAAM,KAAA;MACAJ,OAAA,WAAAA,SAAA;QACA;MACA;IACA;IACAK,QAAA;MACAP,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;MACAC,OAAA;IACA;EACA;EACAC,KAAA;IACAR,IAAA;MACAS,OAAA,WAAAA,QAAA;QACA,KAAAC,cAAA;MACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;IACAF,cAAA,WAAAA,eAAA;MAAA,IAAAG,KAAA;MACApB,qBAAA;QACAqB,QAAA,OAAAd;MACA,GAAAe,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAP,IAAA,GAAAU,GAAA,CAAAC,IAAA;MACA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAAC,QAAA,CAAAC,OAAA;IACA;IACA;IACAC,oBAAA,WAAAA,qBAAA;MACA,SAAAf,IAAA,CAAAgB,MAAA;QACA,KAAAC,WAAA,MAAAjB,IAAA;MACA;IACA;IACA;IACAiB,WAAA,WAAAA,YAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,KAAAlB,OAAA;MACAf,sBAAA;QACAsB,QAAA,OAAAd,IAAA;QACA0B,UAAA,EAAAF,IAAA,CAAAG,EAAA;QACAC,QAAA,OAAAxB,QAAA;QACAyB,SAAA,OAAA3B;MACA,GAAAa,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAc,SAAA;UACAC,MAAA,CAAAC,IAAA,CAAAtC,UAAA,CAAA+B,MAAA,CAAAQ,QAAA,EAAAjB,GAAA,CAAAC,IAAA;QACA;UACAQ,MAAA,CAAAN,QAAA,CAAAe,KAAA,CAAAlB,GAAA,CAAAmB,OAAA;QACA;MACA,GAAAC,OAAA;QACAX,MAAA,CAAAlB,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
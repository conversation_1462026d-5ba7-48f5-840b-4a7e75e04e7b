{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\WithdrawHistory.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\WithdrawHistory.vue", "mtime": 1757468127988}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCgppbXBvcnQgeyBHZXRTY2hkdWxpbmdDYW5jZWxIaXN0b3J5LCBHZXRQYXJ0U2NoZHVsaW5nQ2FuY2VsSGlzdG9yeSB9IGZyb20gJ0AvYXBpL1BSTy9wcm9kdWN0aW9uLXRhc2snOwpleHBvcnQgZGVmYXVsdCB7CiAgcHJvcHM6IHsKICAgIHBhcnROYW1lOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0sCiAgICBjb21OYW1lOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB0YWJsZURhdGE6IFtdLAogICAgICB0YkxvYWRpbmc6IGZhbHNlCiAgICB9OwogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGlzQ29tOiBmdW5jdGlvbiBpc0NvbSgpIHsKICAgICAgcmV0dXJuIHRoaXMucGFnZVR5cGUgPT09ICdjb20nOwogICAgfQogIH0sCiAgaW5qZWN0OiBbJ3BhZ2VUeXBlJ10sCiAgbWV0aG9kczogewogICAgaW5pdDogZnVuY3Rpb24gaW5pdChyb3cpIHsKICAgICAgdGhpcy5yb3cgPSByb3c7CiAgICAgIHRoaXMuZmV0Y2hEYXRhKCk7CiAgICB9LAogICAgZmV0Y2hEYXRhOiBmdW5jdGlvbiBmZXRjaERhdGEoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHRoaXMudGJMb2FkaW5nID0gdHJ1ZTsKICAgICAgdmFyIHJlcXVlc3RGbiA9IG51bGw7CiAgICAgIHJlcXVlc3RGbiA9IHRoaXMuaXNDb20gPyBHZXRTY2hkdWxpbmdDYW5jZWxIaXN0b3J5IDogR2V0UGFydFNjaGR1bGluZ0NhbmNlbEhpc3Rvcnk7CiAgICAgIHJlcXVlc3RGbih7CiAgICAgICAgc2NoZHVsaW5nQ29kZTogdGhpcy5yb3cuU2NoZHVsaW5nX0NvZGUKICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIF90aGlzLnRhYmxlRGF0YSA9IHJlcy5EYXRhOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pLmZpbmFsbHkoZnVuY3Rpb24gKF8pIHsKICAgICAgICBfdGhpcy50YkxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["GetSchdulingCancelHistory", "GetPartSchdulingCancelHistory", "props", "partName", "type", "String", "default", "comName", "data", "tableData", "tbLoading", "computed", "isCom", "pageType", "inject", "methods", "init", "row", "fetchData", "_this", "requestFn", "schdulingCode", "Schduling_Code", "then", "res", "IsSucceed", "Data", "$message", "message", "Message", "finally", "_"], "sources": ["src/views/PRO/plan-production/schedule-production-new-part/components/WithdrawHistory.vue"], "sourcesContent": ["<template>\r\n  <div class=\"wrapper\">\r\n    <div class=\"h100\">\r\n      <vxe-table\r\n        :empty-render=\"{name: 'NotData'}\"\r\n        show-header-overflow\r\n        empty-text=\"暂无数据\"\r\n        height=\"auto\"\r\n        show-overflow\r\n        :row-config=\"{isCurrent: true, isHover: true}\"\r\n        :loading=\"tbLoading\"\r\n        class=\"cs-vxe-table\"\r\n        align=\"left\"\r\n        stripe\r\n        :data=\"tableData\"\r\n        resizable\r\n        :tooltip-config=\"{ enterable: true }\"\r\n      >\r\n        <vxe-column\r\n          align=\"left\"\r\n          sortable\r\n          min-width=\"150\"\r\n          :field=\"isCom ? 'Comp_Code' : 'Part_Code'\"\r\n          :title=\"`${partName}名称`\"\r\n        />\r\n        <vxe-column\r\n          v-if=\"!isCom\"\r\n          align=\"left\"\r\n          sortable\r\n          min-width=\"120\"\r\n          field=\"Comp_Code\"\r\n          :title=\"`所属${comName}`\"\r\n        />\r\n        <vxe-column\r\n          align=\"left\"\r\n          sortable\r\n          min-width=\"120\"\r\n          field=\"Spec\"\r\n          title=\"规格\"\r\n        />\r\n        <vxe-column\r\n          align=\"left\"\r\n          sortable\r\n          min-width=\"120\"\r\n          field=\"Texture\"\r\n          title=\"材质\"\r\n        />\r\n        <vxe-column\r\n          align=\"center\"\r\n          sortable\r\n          min-width=\"120\"\r\n          field=\"Length\"\r\n          title=\"长度\"\r\n        />\r\n\r\n        <vxe-column\r\n          align=\"center\"\r\n          sortable\r\n          min-width=\"80\"\r\n          field=\"Cancel_Count\"\r\n          title=\"数量\"\r\n        />\r\n        <vxe-column\r\n          align=\"center\"\r\n          sortable\r\n          min-width=\"120\"\r\n          field=\"Weight\"\r\n          title=\"单重(kg)\"\r\n        />\r\n        <vxe-column\r\n          align=\"center\"\r\n          sortable\r\n          min-width=\"150\"\r\n          field=\"Cancel_Date\"\r\n          title=\"撤回时间\"\r\n        >\r\n          <template #default=\"{ row }\">\r\n            {{ row.Cancel_Date | timeFormat(\"{y}-{m}-{d} {h}:{i}:{s}\") }}\r\n          </template>\r\n        </vxe-column>\r\n      </vxe-table>\r\n    </div>\r\n    <footer>\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n    </footer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetSchdulingCancelHistory, GetPartSchdulingCancelHistory } from '@/api/PRO/production-task'\r\n\r\nexport default {\r\n  props: {\r\n    partName: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    comName: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      tableData: [],\r\n      tbLoading: false\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    }\r\n  },\r\n  inject: ['pageType'],\r\n  methods: {\r\n    init(row) {\r\n      this.row = row\r\n      this.fetchData()\r\n    },\r\n    fetchData() {\r\n      this.tbLoading = true\r\n      let requestFn = null\r\n      requestFn = this.isCom ? GetSchdulingCancelHistory : GetPartSchdulingCancelHistory\r\n      requestFn({\r\n        schdulingCode: this.row.Schduling_Code\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.tableData = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.tbLoading = false\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.wrapper {\r\n  height: 60vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  footer {\r\n    margin: 10px;\r\n    text-align: right;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyFA,SAAAA,yBAAA,EAAAC,6BAAA;AAEA;EACAC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,OAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA,YAAAC,QAAA;IACA;EACA;EACAC,MAAA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAAC,GAAA;MACA,KAAAA,GAAA,GAAAA,GAAA;MACA,KAAAC,SAAA;IACA;IACAA,SAAA,WAAAA,UAAA;MAAA,IAAAC,KAAA;MACA,KAAAT,SAAA;MACA,IAAAU,SAAA;MACAA,SAAA,QAAAR,KAAA,GAAAZ,yBAAA,GAAAC,6BAAA;MACAmB,SAAA;QACAC,aAAA,OAAAJ,GAAA,CAAAK;MACA,GAAAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAN,KAAA,CAAAV,SAAA,GAAAe,GAAA,CAAAE,IAAA;QACA;UACAP,KAAA,CAAAQ,QAAA;YACAC,OAAA,EAAAJ,GAAA,CAAAK,OAAA;YACAzB,IAAA;UACA;QACA;MACA,GAAA0B,OAAA,WAAAC,CAAA;QACAZ,KAAA,CAAAT,SAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
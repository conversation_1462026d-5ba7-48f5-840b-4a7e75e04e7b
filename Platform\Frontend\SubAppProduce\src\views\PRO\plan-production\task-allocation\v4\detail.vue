<template>
  <div v-loading="loading" class="app-container abs100">
    <el-card class="box-card h100">
      <h4 class="topTitle"><span />基本信息</h4>

      <el-form
        ref="formInline"
        label-position="right"
        label-width="90px"
        :inline="true"
        :model="formInline"
        class="demo-form-inline"
      >
        <el-row>
          <el-col :span="20">
            <el-row>
              <el-col :span="6">
                <el-form-item label="排产单号:" label-width="75px" prop="Schduling_Code">
                  <span>{{ formInline.Schduling_Code }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="项目名称:" prop="Project_Name">
                  <span>{{ formInline.Project_Name }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="区域:" prop="Area_Name">
                  <span>{{ formInline.Area_Name }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="批次:" prop="Installunit_Name">
                  <span>{{ formInline.Installunit_Name }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="任务数量:" label-width="75px" prop="Allocation_Count">
                  <span>{{ formInline.Allocation_Count }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="任务重量:" prop="Allocation_Weight">
                  <span>{{ formInline.Allocation_Weight | filterNum }}(t)</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="已完成数量:" prop="Finish_Count">
                  <span>{{ formInline.Finish_Count }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="已完成重量:" prop="Finish_Weight">
                  <span>{{ formInline.Finish_Weight | filterNum }}(t)</span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="4">
            <qrcode-vue
              :size="79"
              :value="formInline.Schduling_Code"
              class-name="qrcode"
              level="H"
            />
          </el-col>
        </el-row>
      </el-form>
      <el-divider class="elDivder" />
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="待分配" name="first" />
        <el-tab-pane
          v-for="(element, index2) in workingTeam"
          :key="index2"
          :label="element.Working_Team_Name"
          :name="`${element.Working_Team_Name}$_$${element.Process_Code}`"
        />
      </el-tabs>
      <div class="tb-options" :style="{'justify-content': !isView ? 'space-between' : 'end'}">
        <div>
          <el-button v-if="!isView" plain type="primary" @click="reverseSelection()">反选</el-button>
          <el-button v-if="!isView" type="primary" :disabled="!multipleSelection.length" @click="Batchallocation()">批量分配</el-button>
          <el-button v-if="!isView && activeName==='first'" type="primary" :disabled="!multipleSelection.length" @click="preStepTaskAllocation()">上道工序同步</el-button>
        </div>
        <el-form :inline="true" :model="queryForm" class="demo-form-inline">
          <el-form-item label="规格">
            <el-input v-model.trim="queryForm.Spec" clearable placeholder="请输入" />
          </el-form-item>
          <el-form-item v-if="isCom" :label="isCom ? '构件类型' :isUnitPart?'部件类型':'零件类型'">
            <el-tree-select
              ref="treeSelectObjectType"
              v-model="searchType"
              style="width: 100%"
              class="cs-tree-x"
              :select-params="treeSelectParams"
              :tree-params="ObjectTypeList"
              value-key="Id"
            />
          </el-form-item>
          <el-form-item :label="isCom ? '构件编号' :isUnitPart?'部件名称':'零件名称'">
            <el-input v-if="isCom" v-model="queryForm.Comp_Codes" clearable placeholder="请输入(空格区分/多个搜索)" />
            <el-input v-else v-model="queryForm.Part_Code" clearable placeholder="请输入(空格区分/多个搜索)" />
            <el-input v-if="isCom" v-model.trim="queryForm.Comp_Codes_Vague" style="margin-left: 10px;" clearable placeholder="模糊查找(请输入关键字)" />
            <el-input v-else v-model.trim="queryForm.Part_Code_Vague" style="margin-left: 10px;" clearable placeholder="模糊查找(请输入关键字)" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="filterData">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="tb-x">
        <vxe-table
          ref="xTable"
          :empty-render="{name: 'NotData'}"
          show-header-overflow
          class="cs-vxe-table"
          :row-config="{ isCurrent: true, isHover: true }"
          align="left"
          height="100%"
          show-overflow
          :loading="tbLoading"
          :checkbox-config="{checkField: 'checked',checkMethod }"
          stripe
          size="medium"
          :edit-config="{
            trigger: 'click',
            mode: 'cell',
            showIcon: !isView,
            beforeEditMethod: activeCellMethod,
          }"
          :data="filterTbData"
          resizable
          :tooltip-config="{ enterable: true }"
          @checkbox-all="tbSelectChange"
          @checkbox-change="tbSelectChange"
        >
          <vxe-column type="checkbox" width="60" fixed="left" />
          <template v-for="item in columns">

            <!--            <vxe-column :align="item.Align"
              v-if="item.Code==='Comp_Code'||item.Code==='Part_Code'"
              :key="item.Id"
              :filters="[{ data: '' }]"
              :filter-method="filterCodeMethod"
              :filter-recover-method="filterCodeRecoverMethod"
              :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
              show-overflow="tooltip"
              sortable
              :field="item.Code"
              :title="item.Display_Name"
              :min-width="item.Width"
            >
              <template #filter="{ $panel, column }">
                <template v-for="(option, index) in column.filters">
                  <input :key="index" v-model="option.data" class="my-input" type="type" placeholder="按回车确认筛选" @input="$panel.changeOption($event, !!option.data, option)" @keyup.enter="$panel.confirmFilter()">
                </template>
              </template>
            </vxe-column>
            <vxe-column :align="item.Align"
              v-else-if="item.Code==='Type'"
              :key="item.Id"
              :filters="[{ data: '' }]"
              :filter-method="filterTypeMethod"
              :filter-recover-method="filterTypeRecoverMethod"
              :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
              show-overflow="tooltip"
              sortable
              :field="item.Code"
              :title="item.Display_Name"
              :min-width="item.Width"
            >
              <template #filter="{ $panel, column }">
                <template v-for="(option, index) in column.filters">
                  <input :key="index" v-model="option.data" class="my-input" type="type" placeholder="按回车确认筛选" @input="$panel.changeOption($event, !!option.data, option)" @keyup.enter="$panel.confirmFilter()">
                </template>
              </template>
            </vxe-column>-->
            <vxe-column
              v-if="item.Code==='Comp_Code'|| item.Code==='Part_Code'"
              :key="item.Id"
              :align="item.Align"
              :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
              show-overflow="tooltip"
              sortable
              :visible="item.visible"
              :field="item.Code"
              :title="item.Display_Name"
              :min-width="item.Width"
            >
              <template #default="{ row }">
                <el-tag v-if="!!row.stopFlag" style="margin-right: 8px;" type="danger">停</el-tag>
                <span>{{ row[item.Code] }}</span>
              </template>
            </vxe-column>
            <vxe-column
              v-else-if="item.Code==='Schduled_Count'"
              :key="`SchduledCount${item.Id}`"
              :align="item.Align"
              :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
              show-overflow="tooltip"
              sortable
              :visible="item.visible"
              :field="item.Code"
              :title="item.Display_Name"
              :min-width="item.Width"
            >
              <template #default="{ row }">
                {{ activeName === 'first' ?'':row[getTaskCode()] }}
              </template>
            </vxe-column>
            <vxe-column
              v-else
              :key="`Default${item.Id}`"
              :align="item.Align"
              :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
              :visible="item.visible"
              show-overflow="tooltip"
              sortable
              :field="item.Code"
              :title="item.Display_Name"
              :min-width="item.Width"
            />
          </template>

          <vxe-column
            v-for="(element, index2) in workingTeamColumn"
            :key="index2"
            :align="element.Align"
            :visible="element.visible"
            fixed="right"
            :field="`${element.Working_Team_Name}$_$${element.Process_Code}`"
            title="可分配数量"
            sortable
            min-width="170"
          >
            <template #edit="{ row }">
              <vxe-input
                v-model.number="
                  row[
                    getRowUnique(
                      row.uuid,
                      element.Process_Code,
                      element.Working_Team_Id
                    )
                  ]
                "
                type="integer"
                :min="0"
                :max="
                  row[
                    getRowUniqueMax(
                      row.uuid,
                      element.Process_Code,
                      element.Working_Team_Id
                    )
                  ]
                "
                @change="
                  inputChange(
                    row,
                    element.Process_Code,
                    element.Working_Team_Id
                  )
                "
              />
            </template>
            <template #default="{ row }">
              <template
                v-if="
                  checkPermissionTeam(row.Technology_Path, element.Process_Code)
                "
              >
                {{
                  row[
                    getRowUnique(
                      row.uuid,
                      element.Process_Code,
                      element.Working_Team_Id
                    )
                  ]
                }}
              </template>
              <template v-else> -</template>
            </template>
          </vxe-column>

          <vxe-column
            :key="activeName"
            align="left"
            :edit-render="{}"
            field="AllocatedCount"
            title="分配数量"
            sortable
            fixed="right"
            min-width="180"
          >
            <template #edit="{ row }">
              <vxe-input
                :key="activeName"
                v-model.number="row[getRowCCode(row,'alCount')]"
                type="integer"
                :min="0"
                :max="row[getRowCCode(row,'','Max')]"
              />
            </template>
            <template #default="{ row }">
              {{ row[getRowCCode(row,'alCount')] | displayValue }}
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <footer>
        <div class="data-info">
          <el-tag size="medium" class="info-x">已选{{ multipleSelection.length }}条数据</el-tag>
          <el-tag v-if="tipLabel" size="medium" class="info-x">{{ tipLabel }}</el-tag>
        </div>
        <div>
          <el-button @click="handleClose">取消 </el-button>
          <el-button
            v-if="!isView"
            type="primary"
            :loading="loading"
            @click="handleSubmit"
          >提交</el-button>
        </div>
      </footer>
    </el-card>
    <el-dialog
      v-dialogDrag
      title="批量分配"
      class="plm-custom-dialog"
      :visible.sync="dialogVisible"
      width="30%"
      @close="handleDialog"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="80px"
        class="demo-ruleForm"
      >
        <el-form-item label="选择班组" prop="TeamGroup">
          <el-select
            v-model="form.TeamGroup"
            class="w100"
            placeholder="请选择"
            filterable
            clearable
          >
            <el-option
              v-for="item in workingTeam"
              :key="item.Working_Team_Id"
              :label="item.Working_Team_Name"
              :value="item.Working_Team_Id"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="text-align: right">
          <el-button @click="resetForm('form')">取 消</el-button>
          <el-button
            type="primary"
            @click="submitForm('form');resetForm('form')"
          >确 定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog
      v-dialogDrag
      title="提示"
      class="plm-custom-dialog"
      :visible.sync="dialogTipsVisible"
      width="450px"
      @close="handleDialog"
    >
      <div style="text-align: center; font-size: 16px;">部分{{ isCom ? '构件' : isUnitPart ? '部件' : '零件' }}与上道工序加工班组不同，请手动分配</div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogTipsVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogTipsVisible = false">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { GetGridByCode } from '@/api/sys'
import { FIX_COLUMN } from '@/views/PRO/plan-production/schedule-production/constant'
import {
  AdjustTeamProcessAllocation,
  GetTeamProcessAllocation,
  AdjustPartTeamProcessAllocation,
  AdjustSubAssemblyTeamProcessAllocation,
  GetStopList,
  GetPreStepTaskAllocation
} from '@/api/PRO/production-task'
import QrcodeVue from 'qrcode.vue'
import { v4 as uuidv4 } from 'uuid'
import numeral from 'numeral'
import { closeTagView, deepClone } from '@/utils'
import { GetCompTypeTree } from '@/api/PRO/professionalType'

const SPLIT_SYMBOL = '$_$'
export default {
  components: {
    QrcodeVue
  },
  filters: {
    filterNum(value) {
      return numeral(value).divide(1000).format('0.[00]')
    }
  },
  data() {
    return {
      treeSelectParams: {
        placeholder: '请选择',
        clearable: true
      },
      ObjectTypeList: {
        // 构件类型
        'check-strictly': true,
        'default-expand-all': true,
        clickParent: true,
        data: [],
        props: {
          children: 'Children',
          label: 'Label',
          value: 'Data'
        }
      },
      tbLoading: false,
      loading: false,
      activeName: 'first',
      tipLabel: '',
      tbData: [],
      filterTbData: [],
      multipleSelection: [],
      columns: [],
      workingTeam: [],
      workingTeamColumn: [],
      formInline: {},
      pg_type: '',
      searchType: '',
      type: '',
      queryForm: {
        Comp_Codes: '',
        Part_Code: '',
        Spec: '',
        Comp_Codes_Vague: '',
        Part_Code_Vague: ''
      },
      dialogVisible: false,
      dialogTipsVisible: false,
      form: {
        TeamGroup: '' // 班组
      },

      rules: {
        TeamGroup: [
          { required: true, message: '请输入班组名称', trigger: 'change' }
        ]
      },
      Is_Workshop_Enabled: false,
      Working_Process_Id: ''
    }
  },
  computed: {
    isView() {
      return this.type === 'view'
    },
    isCom() {
      return this.pg_type === 'com'
    },
    isUnitPart() {
      return this.pg_type === 'unitPart'
    }
  },

  async mounted() {
    try {
      const rowInfo = JSON.parse(decodeURIComponent(this.$route.query.other))
      console.log('rowInfo', rowInfo)
      this.formInline = Object.assign({}, this.formInline, rowInfo)
      this.pg_type = this.$route.query.pg_type
      this.bomLevel = this.$route.query.bomLevel
      this.type = this.$route.query.type
      this.Is_Workshop_Enabled = this.$route.query.Is_Workshop_Enabled
      await this.getTableConfig(this.isUnitPart ? 'PROTaskUnitAllocationChange' : 'PROTaskAllocationChange')
      if (this.isCom) {
        const idx = this.columns.findIndex(item => item.Code === 'Part_Code')
        idx !== -1 && this.columns.splice(idx, 1)
        const idx2 = this.columns.findIndex(item => item.Code === 'Component_Code')
        idx2 !== -1 && this.columns.splice(idx2, 1)
      } else {
        const idx = this.columns.findIndex(item => item.Code === 'Comp_Code')
        idx !== -1 && this.columns.splice(idx, 1)
        const idx2 = this.columns.findIndex(item => item.Code === 'Type')
        idx2 !== -1 && this.columns.splice(idx2, 1)
      }
      if (!this.Is_Workshop_Enabled) {
        const idx3 = this.columns.findIndex(item => item.Code === 'Workshop_Name')
        idx3 !== -1 && this.columns.splice(idx3, 1)
      }
      this.getObjectTypeList()
      this.fetchData()
    } catch (e) {
      this.$message({
        message: '参数错误,请重新操作',
        type: 'error'
      })
    }
  },
  methods: {
    getRowCCode(row, prefix = '', suffix = '') {
      if (this.activeName === 'first') {
        return 'allocatedTask' + suffix
      } else {
        const arr = this.activeName.split(SPLIT_SYMBOL)
        const team = this.workingTeam.find(v => v.Working_Team_Name === arr[0])
        const u = this.getRowUnique(row.uuid, row.Process_Code, team.Working_Team_Id)
        if (suffix === 'Max') {
          return u
        } else {
          return prefix + u + suffix
        }
      }
    },
    handleClick(val) {
      console.log('handleClick', val)
      if (val.name === 'first') {
        const c1 = this.$refs.xTable.getColumnByField('Schduled_Count')
        const c2 = this.$refs.xTable.getColumnByField('Can_Allocation_Count')
        c1.visible = false
        c2.visible = true
      } else {
        const c2 = this.$refs.xTable.getColumnByField('Schduled_Count')
        const c3 = this.$refs.xTable.getColumnByField('Can_Allocation_Count')
        c2.visible = true
        c3.visible = false
      }

      this.workingTeam.forEach((element, idx) => {
        const codes = `${element.Working_Team_Name}$_$${element.Process_Code}`

        const c = this.$refs.xTable.getColumnByField(codes)

        c.visible = codes === val.name
      })
      this.$refs.xTable.refreshColumn()

      this.filterTbData = this.tbData.filter(row => {
        return this.filterZero(row)
      })

      this.multipleSelection = []
      this.$refs.xTable.clearCheckboxRow()
    },
    filterZero(row) {
      if (this.activeName === 'first') {
        return row.allocatedTask > 0
      } else {
        const arr = this.activeName.split(SPLIT_SYMBOL)
        const team = this.workingTeam.find(v => v.Working_Team_Name === arr[0])
        const code = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)
        return row[code] > 0
      }
    },
    fetchData() {
      const Comp_Codes = !this.queryForm.Comp_Codes ? [] : this.queryForm.Comp_Codes.trim().split(' ')
      const Part_Code = !this.queryForm.Part_Code ? [] : this.queryForm.Part_Code.trim().split(' ')
      let Process_Type = 2
      Process_Type = this.isCom ? 2 : this.isUnitPart ? 3 : 1
      this.tbLoading = true
      GetTeamProcessAllocation({
        Page: 1,
        PageSize: -1,
        Step: this.formInline.Step,
        Process_Type,
        Bom_Level: this.bomLevel,
        Schduling_Code: this.formInline.Schduling_Code,
        Process_Code: this.formInline.Process_Code,
        Workshop_Name: this.formInline.Workshop_Name,
        Area_Id: this.formInline.Area_Id,
        InstallUnit_Id: this.formInline.InstallUnit_Id,
        Comp_Codes,
        Part_Code
      }).then(async(res) => {
        if (res.IsSucceed) {
          const { Schduling_Plan, Schduling_Comps } = res.Data
          this.planInfoTemp = Schduling_Plan
          await this.getStopList(Schduling_Comps)
          this.initTbData(Schduling_Comps)
          this.Working_Process_Id = res.Data.Working_Process_Id

          if (Schduling_Comps.length) {
            this.workingTeam = Schduling_Comps[0].Allocation_Teams
            const _kk = this.workingTeam.map(v => {
              v.visible = false
              return v
            })
            this.workingTeamColumn = deepClone(_kk)
          }
          console.log(' this.tbData', this.tbData)
          this.filterData()
          console.log('filterTbData', this.filterTbData)
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      }).finally(_ => {
        this.tbLoading = false
      })
    },
    checkMethod({ row }) {
      return !row.stopFlag
    },
    async getStopList(list) {
      const key = 'Id'
      const submitObj = list.map(item => {
        return {
          Id: item[key],
          Bom_Level: this.bomLevel,
          Type: this.isCom ? 2 : this.isUnitPart ? 3 : 1 // 1：零件，3：部件，2：构件
        }
      })
      await GetStopList(submitObj).then(res => {
        if (res.IsSucceed) {
          const stopMap = {}
          res.Data.forEach(item => {
            stopMap[item.Id] = !!item.Is_Stop
          })
          list.forEach(row => {
            if (stopMap[row[key]]) {
              this.$set(row, 'stopFlag', stopMap[row[key]])
            }
          })
        }
      })
    },
    filterData() {
      console.log('searchType', this.searchType)
      this.multipleSelection = []
      this.$refs.xTable.clearCheckboxRow()
      const code = this.isCom ? 'Comp_Code' : 'Part_Code'
      const queryCode = this.isCom ? 'Comp_Codes' : 'Part_Code'
      const codeList = this.queryForm[queryCode].split(' ').filter(v => !!v)

      const queryCodeVague = this.isCom ? 'Comp_Codes_Vague' : 'Part_Code_Vague'
      const codeListVague = this.queryForm[queryCodeVague]

      const searchTbData = this.tbData.filter(v => {
        if (!codeList.length && codeListVague === '') {
          return true
        } else {
          return codeList.includes(v[code])
        }
      })

      const vagueData = searchTbData.length > 0 && codeListVague === '' ? searchTbData : searchTbData.length === 0 && codeListVague === '' ? searchTbData : this.tbData
      const searchVagueTbData = vagueData.filter(v => {
        if (codeListVague === '' && !codeList.length) {
          return true
        } else {
          return v[code].includes(codeListVague)
        }
      })

      // 合并两个数组
      const mergedArray = searchTbData.concat(searchVagueTbData)
      // 根据 Schduling_Detail_Id 进行去重
      const uniqueArray = mergedArray.reduce((acc, current) => {
        const existingObject = acc.find(item => item.Schduling_Detail_Id === current.Schduling_Detail_Id)
        if (!existingObject) {
          acc.push(current)
        }
        return acc
      }, [])

      this.filterTbData = uniqueArray.filter(v => {
        if (!this.searchType) return true
        return this.searchType === v.Type
      }).filter(v => {
        if (!this.queryForm.Spec) return true
        return (v.Spec || '').includes(this.queryForm.Spec)
      }).filter(row => {
        return this.filterZero(row)
      })
    },
    initTbData(list, teamKey = 'Allocation_Teams') {
      this.tbData = list.map(row => {
        const processList = row.Technology_Path?.split('/') || []
        // 已uuid作为row唯一值；
        // uuid+工序+班组为输入框值
        row.uuid = uuidv4()
        const newData = row[teamKey].filter((r) => processList.findIndex((p) => r.Process_Code === p) !== -1)
        row.defaultCan_Allocation_Count = row.Can_Allocation_Count
        let _inputNum = 0
        newData.forEach((ele, index) => {
          const code = this.getRowUnique(row.uuid, ele.Process_Code, ele.Working_Team_Id)
          const max = this.getRowUniqueMax(row.uuid, ele.Process_Code, ele.Working_Team_Id)
          row[code] = ele.Count
          this.$set(row, 'alCount' + code, ele.Count)
          row[max] = 0
          _inputNum += ele.Count
          this.$set(row, 'totalTask' + ele.Working_Team_Name, ele.Total_Receive_Count + ele.Count)
        })

        this.$set(row, 'allocatedTask', row.defaultCan_Allocation_Count - _inputNum)
        row.Can_Allocation_Count = row.allocatedTask
        this.$set(row, 'allocatedTaskMax', row.Can_Allocation_Count)

        this.setInputMax(row)

        row.checked = false
        return row
      })
    },
    inputChange(row) {
      this.setInputMax(row)
    },
    setInputMax(row) {
      let _inputNum = 0
      const inputValuesKeys = Object.keys(row)
        .filter(v => !v.endsWith('max') && v.startsWith(row.uuid) && v.length > row.uuid.length)
      inputValuesKeys.forEach((val) => {
        const curCode = val.split(SPLIT_SYMBOL)[1]
        const otherTotal = inputValuesKeys.filter(x => {
          const code = x.split(SPLIT_SYMBOL)[1]
          return x !== val && code === curCode
        }).reduce((acc, item) => {
          return acc + numeral(row[item]).value()
        }, 0)
        row[val + SPLIT_SYMBOL + 'max'] = row.Schduled_Count - otherTotal
        _inputNum += +row[val]
      })
      // row.allocatedCount = row.defaultCan_Allocation_Count - _inputNum
      // row.Can_Allocation_Count = row.allocatedCount
    },
    checkPermissionTeam(processStr, processCode) {
      if (!processStr) return false
      const list = processStr?.split('/') || []
      return !!list.some(v => v === processCode)
    },

    getSubmitTbInfo(tbData = this.tbData) {
      // 处理上传的数据
      const tableData = JSON.parse(JSON.stringify(tbData))
      for (let i = 0; i < tableData.length; i++) {
        const element = tableData[i]
        const list = []
        if (!element.Technology_Path) {
          this.$message({
            message: '工序不能为空',
            type: 'warning'
          })
          return { status: false }
        }
        const processList = Array.from(new Set(element.Technology_Path.split('/')))
        processList.forEach(code => {
          const groups = this.workingTeam.filter(v => v.Process_Code === code)

          const groupsList = groups.map((group, index) => {
            const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)
            const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)
            const obj = {
              Comp_Code: element.Comp_Code,
              Again_Count: +element[uCode],
              Part_Code: this.isCom ? null : element.Part_Code,
              Process_Code: code,
              Technology_Path: element.Technology_Path,
              Working_Team_Id: group.Working_Team_Id,
              Working_Team_Name: group.Working_Team_Name,
              Team_Task_Id: element.Allocation_Teams.find((item) => item.Working_Team_Id === group.Working_Team_Id).Team_Task_Id
            }
            delete element['alCount' + uCode]
            delete element['allocatedTask']
            delete element['allocatedTaskMax']
            delete element[uCode]
            delete element[uMax]
            return obj
          })
          list.push(...groupsList)
        })
        console.log(list)
        delete element['uuid']
        delete element['puuid']
        element.Allocation_Teams = list
      }
      return { tableData, status: true }
    },
    handleSubmit() {
      const { tableData, status } = this.getSubmitTbInfo()
      if (!status) return
      this.$confirm('是否提交当前数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        const obj = {
          Schduling_Plan: this.planInfoTemp,
          Schduling_Comps: tableData
        }
        const Partobj = {
          Schduling_Plan: this.planInfoTemp,
          SarePartsModel: tableData
        }
        const requestFn = this.isCom ? AdjustTeamProcessAllocation : this.isUnitPart ? AdjustSubAssemblyTeamProcessAllocation : AdjustPartTeamProcessAllocation
        console.log('obj', obj)
        requestFn(this.isCom ? obj : Partobj).then(res => {
          if (res.IsSucceed) {
            this.$message({
              message: '操作成功',
              type: 'success'
            })
            this.handleClose()
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
          this.loading = false
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    handleClose() {
      this.closeView()
    },
    closeView() {
      closeTagView(this.$store, this.$route)
    },
    tbSelectChange(array) {
      this.multipleSelection = array.records
      console.log(this.tbData)
    },
    getTaskCode(name = '') {
      if (name) return 'totalTask' + name
      return 'totalTask' + this.activeName.split(SPLIT_SYMBOL)[0]
    },
    // 反选
    reverseSelection() {
      const list = this.$refs.xTable.getCheckboxRecords().filter(item => !item.stopFlag)
      const unSelectList = this.filterTbData.filter(item => !list.includes(item) && !item.stopFlag)
      this.$refs.xTable.setCheckboxRow(list, false)
      this.$refs.xTable.setCheckboxRow(unSelectList, true)
      this.multipleSelection = this.$refs.xTable.getCheckboxRecords()
    },
    async getTableConfig(code) {
      await GetGridByCode({
        code
      }).then((res) => {
        const { IsSucceed, Data, Message } = res
        if (IsSucceed) {
          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)
          const list = Data.ColumnList || []
          this.columns = list.filter(v => v.Is_Display).map(item => {
            if (FIX_COLUMN.includes(item.Code)) {
              item.fixed = 'left'
            }
            if (item.Code === 'Schduled_Count') {
              item.visible = false
            }
            return item
          })
        } else {
          this.$message({
            message: Message,
            type: 'error'
          })
        }
      })
    },
    activeCellMethod({ row, column, columnIndex }) {
      if (this.isView) return false
      if (column.field === 'AllocatedCount') return true
      const processCode = column.field?.split('$_$')[1]
      return this.checkPermissionTeam(row.Technology_Path, processCode)
    },
    getRowUnique(uuid, processCode, workingId) {
      return `${uuid}${SPLIT_SYMBOL}${processCode}${SPLIT_SYMBOL}${workingId}`
    },
    getRowUniqueMax(uuid, processCode, workingId) {
      return this.getRowUnique(uuid, processCode, workingId) + `${SPLIT_SYMBOL}max`
    },
    // 批量分配
    Batchallocation() {
      this.dialogVisible = true
      console.log(this.workingTeam)
    },
    // 上道工序分配
    preStepTaskAllocation() {
      const Schduling_Detail_Ids = this.multipleSelection.map(item => item.Schduling_Detail_Id)
      const Working_Process_Code = this.multipleSelection[0].Process_Code
      GetPreStepTaskAllocation({
        Schduling_Detail_Ids,
        Working_Process_Code,
        Working_Process_Id: this.Working_Process_Id,
        Process_Type: this.isCom ? 2 : this.isUnitPart ? 3 : 1,
        Bom_Level: this.bomLevel
      }).then(res => {
        if (res.IsSucceed) {
          if (res.Data.length === 0) {
            this.dialogTipsVisible = true
            return
          }
          this.preDoAllocation(res.Data)
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    handleDialog() {
      this.dialogVisible = false
      // this.multipleSelection = []
    },
    handelData() {
      this.multipleSelection.forEach((item) =>
        item.Allocation_Teams.forEach((v) => {
          if (v.Working_Team_Id === this.form.TeamGroup) {
            v.Count = item.Can_Allocation_Count
          } else {
            v.Count = 0
          }
        })
      )
      // const tableData = JSON.parse(JSON.stringify(this.multipleSelection))
      for (let i = 0; i < this.multipleSelection.length; i++) {
        const element = this.multipleSelection[i]
        const processList = Array.from(new Set(element.Technology_Path.split('/')))
        processList.forEach(code => {
          const groups = this.workingTeam.filter(v => v.Process_Code === code)
          groups.forEach(group => {
            const uniCode = this.getRowUnique(element.uuid, code, this.form.TeamGroup)
            const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)
            const uniMax = this.getRowUniqueMax(element.uuid, code, this.form.TeamGroup)
            const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)

            if (uniCode === uCode && uniMax === uMax) {
              element[uCode] = element['Can_Allocation_Count']
              element[uMax] = element['Can_Allocation_Count']
            } else {
              element[uCode] = 0
              element[uMax] = 0
            }
          })
        })
      }
      console.log(this.multipleSelection)
      for (let i = 0; i < this.tbData.length; i++) {
        for (let k = 0; k < this.multipleSelection.length; k++) {
          if (this.tbData[i].uuid === this.multipleSelection[k].uuid) {
            this.$nextTick((_) => {
              this.tbData[i] = this.multipleSelection[k]
            })
          }
        }
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // this.handelData()
          this.doAllocation()
          // this.workingTeam.find(v=> v.Working_Team_Id === this.form.TeamGroup).count
          this.handleDialog()
          console.log(this.tbData)
          this.multipleSelection = []
          this.$refs.xTable.clearCheckboxRow()
        } else {
          return false
        }
      })
    },
    // 上道工序分配
    preDoAllocation(preProcessData) {
      const preProcessDataMap = new Map()
      preProcessData.forEach(item => {
        const key = `${item.Schduling_Detail_Id}_${item.Working_Team_Id}`
        preProcessDataMap.set(key, item.Current_Task_Count)
      })

      const allocateForTeam = (row, team, amount) => {
        const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)
        const bKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`
        const currentTaskCount = preProcessDataMap.get(bKey) || 0
        const allocated = Math.min(amount, currentTaskCount, row.Can_Allocation_Count)

        row[tarCode] = (Number(row[tarCode]) || 0) + allocated
        row.Can_Allocation_Count -= allocated
        row[this.getTaskCode(team.Working_Team_Name)] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + allocated
        row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - allocated
        row['alCount' + tarCode] = row[tarCode]

        return allocated
      }
      let isMessage = true
      this.multipleSelection.forEach(row => {
        if (!row.Can_Allocation_Count && this.activeName === 'first') return

        const eligibleTeams = row.Allocation_Teams.filter(team =>
          preProcessDataMap.has(`${row.Schduling_Detail_Id}_${team.Working_Team_Id}`)
        )

        if (eligibleTeams.length === 0) return
        if (this.activeName === 'first') {
          let IsAllNo = 0
          const totalAvailable = eligibleTeams.reduce((sum, team) => {
            const bKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`
            return sum + (preProcessDataMap.get(bKey) || 0)
          }, 0)

          if (row.allocatedTaskMax < totalAvailable) {
            IsAllNo++
            if (IsAllNo === this.multipleSelection.length) {
              isMessage = false
              this.dialogTipsVisible = true
            }
            return
          }

          let remaining = Math.min(row.allocatedTask, row.Can_Allocation_Count)
          const perTeamAllocation = Math.floor(remaining / eligibleTeams.length)

          eligibleTeams.forEach((team, index) => {
            if (remaining <= 0) return

            const allocateAmount = index === eligibleTeams.length - 1
              ? remaining
              : Math.min(perTeamAllocation, remaining)

            remaining -= allocateForTeam(row, team, allocateAmount)
          })

          row.allocatedTaskMax = row.Can_Allocation_Count
          row.allocatedTask = row.Can_Allocation_Count
          if (IsAllNo === this.multipleSelection.length) {
            isMessage = false
            this.dialogTipsVisible = true
            return
          }
        } else {
          eligibleTeams.forEach(team => {
            const bKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`
            const currentTaskCount = preProcessDataMap.get(bKey) || 0

            if (row[this.getRowCCode(row)] < currentTaskCount) {
              return
            }

            const selectNum = Math.min(
              row[this.getRowCCode(row, 'alCount')] || 0,
              row[this.getRowCCode(row)] || 0,
              currentTaskCount
            )

            if (selectNum > 0) {
              const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)
              row[this.getTaskCode(team.Working_Team_Name)] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + selectNum
              row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - selectNum
              row[tarCode] = (Number(row[tarCode]) || 0) + selectNum
              row[this.getRowCCode(row)] = (row[this.getRowCCode(row)] || 0) - selectNum
              row[this.getRowCCode(row, 'alCount')] = (row[this.getRowCCode(row, 'alCount')] || 0) - selectNum
              row['alCount' + tarCode] = row[tarCode]
            }
          })
        }
      })
      // if (this.activeName === 'first') {
      //   let IsAllNo = 0
      //   this.multipleSelection.forEach((row, idx) => {
      //     if (row.Can_Allocation_Count) {
      //       const validTeams = row.Allocation_Teams.filter(team => {
      //         const key = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`
      //         return preProcessDataMap.has(key)
      //       })
      //       if (validTeams.length > 0) {
      //         const team = validTeams[0]
      //         const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)

      //         const preProcessKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`
      //         const currentTaskCount = preProcessDataMap.get(preProcessKey) || 0

      //         if (currentTaskCount > row.allocatedTaskMax) {
      //           IsAllNo++
      //           return
      //         }
      //         const allocated = Math.min(row.allocatedTask, currentTaskCount)

      //         row[tarCode] = Number(row[tarCode] || 0) + allocated
      //         row.Can_Allocation_Count = row.Can_Allocation_Count - allocated
      //         row[this.getTaskCode(team.Working_Team_Name) || 0] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + allocated
      //         row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - allocated
      //         row.allocatedTaskMax = row.Can_Allocation_Count
      //         row.allocatedTask = row.Can_Allocation_Count
      //         row['alCount' + tarCode] = row[tarCode]
      //       }
      //     }
      //   })
      //   if (IsAllNo === this.multipleSelection.length) {
      //     this.dialogTipsVisible = true
      //     return
      //   }
      // } else {
      //   this.multipleSelection.forEach((row, idx) => {
      //     const validTeams = row.Allocation_Teams.filter(team => {
      //       const key = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`
      //       return preProcessDataMap.has(key)
      //     })
      //     if (validTeams.length > 0) {
      //       const team = validTeams[0]
      //       const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)

      //       const preProcessKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`
      //       const currentTaskCount = preProcessDataMap.get(preProcessKey) || 0

      //       const selectNum = Math.min(
      //         row[this.getRowCCode(row, 'alCount')] || 0,
      //         row[this.getRowCCode(row)] || 0,
      //         currentTaskCount
      //       )

      //       row[this.getTaskCode(team.Working_Team_Name)] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + selectNum
      //       row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - selectNum
      //       row[tarCode] = (Number(row[tarCode]) || 0) + selectNum
      //       row[this.getRowCCode(row)] = (row[this.getRowCCode(row)] || 0) - selectNum
      //       row[this.getRowCCode(row, 'alCount')] = (row[this.getRowCCode(row, 'alCount')] || 0) - selectNum
      //       row['alCount' + tarCode] = row[tarCode]
      //     }
      //   })
      // }
      this.filterTbData = this.tbData.filter(row => {
        return this.filterZero(row)
      })
      this.multipleSelection = []
      this.$refs.xTable.clearCheckboxRow()
      if (isMessage) {
        this.$message({
          message: '同步成功',
          type: 'success'
        })
      }
    },
    // 批量分配提交
    doAllocation() {
      if (this.activeName === 'first') {
        this.multipleSelection
          .forEach((row, idx) => {
            if (row.Can_Allocation_Count) {
              const team = row.Allocation_Teams.find(v => v.Working_Team_Id === this.form.TeamGroup)
              const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)
              row[tarCode] = Number(row[tarCode]) + row.allocatedTask
              row.Can_Allocation_Count = row.Can_Allocation_Count - row.allocatedTask
              row[this.getTaskCode(team.Working_Team_Name)] += row.allocatedTask
              row[this.getTaskCode()] -= row.allocatedTask
              row.allocatedTaskMax = row.Can_Allocation_Count
              row.allocatedTask = row.Can_Allocation_Count
              row['alCount' + tarCode] = row[tarCode]
            }
          })
      } else {
        this.multipleSelection
          .forEach((row, idx) => {
            const team = row.Allocation_Teams.find(v => v.Working_Team_Id === this.form.TeamGroup)
            const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)

            const selectNum = Math.min(row[this.getRowCCode(row, 'alCount')], row[this.getRowCCode(row)])

            row[this.getTaskCode(team.Working_Team_Name)] += selectNum
            row[this.getTaskCode()] -= selectNum

            row[tarCode] = Number(row[tarCode]) + selectNum
            row[this.getRowCCode(row)] -= selectNum
            row[this.getRowCCode(row, 'alCount')] -= selectNum

            row['alCount' + tarCode] = row[tarCode]
          })
      }
      this.filterTbData = this.tbData.filter(row => {
        return this.filterZero(row)
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.dialogVisible = false
    },
    filterTypeMethod({ option, row }) {
      return row.Type.includes(option.data)
    },
    filterTypeRecoverMethod({ option }) {
      option.data = ''
    },
    filterCodeMethod({ option, row }) {
      console.log('option, row', option, row)
      return row[this.isCom ? 'Comp_Code' : 'Part_Code'].includes(option.data)
    },
    filterCodeRecoverMethod({ option }) {
      option.data = ''
    },
    getObjectTypeList() {
      GetCompTypeTree({ professional: 'Steel' }).then((res) => {
        if (res.IsSucceed) {
          this.ObjectTypeList.data = res.Data
          this.$nextTick((_) => {
            this.$refs?.treeSelectObjectType?.treeDataUpdateFun(res.Data)
          })
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.pagination-container {
  padding: 0;
  text-align: right;
}

::v-deep .el-card__body {
  display: flex;
  flex-direction: column;

  .el-divider {
    margin-top: 0;
    margin-bottom: 16px;
    background-color: #EEEEEE;
  }

  .tb-options {
    margin-bottom: 16px;
    display: flex;

    .el-form-item--small.el-form-item {
      margin-bottom: 0;
    }
    .el-input {
      width: 250px;
    }
  }

  footer {
    text-align: inherit;
    display: flex;
    justify-content: space-between;
  }
}

.tb-x {
  flex: 1;
  height: 0;
  margin-bottom: 10px;
  overflow: auto;
}

.topTitle {
  height: 14px;
  line-height: 14px;
  font-size: 14px;
  margin: 0 0 16px;

  span {
    display: inline-block;
    width: 2px;
    height: 14px;
    background: #009dff;
    vertical-align: middle;
    margin-right: 6px;
  }
}

.el-icon-edit {
  cursor: pointer;
}

.cs-bottom {
  position: relative;
  height: 40px;
  line-height: 40px;

  .data-info {
    position: absolute;
    bottom: 0;

    .info-x {
      margin-right: 20px;
    }
  }
}
.my-input {
  margin: 10px;
  width: 140px;
  height: 32px;
}
</style>

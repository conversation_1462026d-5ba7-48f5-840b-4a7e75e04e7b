{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\index.vue?vue&type=template&id=5f9c40e8&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\index.vue", "mtime": 1757470958558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}
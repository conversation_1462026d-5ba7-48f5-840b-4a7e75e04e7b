<template>
  <div class="form-wrapper">
    <div class="form-recognition-tabs">
      <el-tabs v-model="bomActiveName" @tab-click="bomClick">
        <el-tab-pane v-for="(item, index) in tabBomList" :key="index" :label="item.Display_Name" :name="item.Code" />
      </el-tabs>
    </div>
    <div class="form-content">
      <div class="can-process-title">
        <div style="width: 120px;" />
        <div class="can-process-list">
          <div v-for="(item, index) in parentBomList" :key="index" :style="{ width: (100 / parentBomList.length) + '%', textAlign: 'center' }">{{ item.Display_Name }}</div>
        </div>
      </div>
      <div class="can-process-box">
        <div v-if="!filteredList.length" class="can-process-empty">
          暂无数据
        </div>
        <div v-for="(item, index) in filteredList" :key="index" class="can-process-item">
          <div class="can-process-type" style="width: 120px;">{{ item.Part_Type_Name }}</div>
          <div class="can-process-bom">
            <div v-for="bom in parentBomList" :key="`${item.Part_Type_Id || index}-${bom.Code}`" class="can-process-select" :style="{ width: (100 / parentBomList.length) + '%' }">
              <el-select
                :key="`select-${item.Part_Type_Id || item.Part_Type_Name || index}-${bom.Code}`"
                :value="getProcessId(item, bom.Code)"
                clearable
                style="width: 100%;"
                placeholder="请选择"
                @change="updateProcessId(item, bom.Code, $event)"
              >
                <el-option v-for="op in getFilteredSelectList(bom.Code)" :key="op.Id" :label="op.Name" :value="op.Id" />
              </el-select>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="form-footer">
      <el-button @click="$emit('close')">取 消</el-button>
      <el-button type="primary" :loading="btnLoading" @click="handleSubmit">确 定</el-button>
    </div>
  </div>
</template>

<script>
import { GetBOMInfo } from '@/views/PRO/bom-setting/utils'
import { GetConsumingAllList, SaveConsumingProcessAllList2 } from '@/api/PRO/partType'
import { GetProcessList } from '@/api/PRO/technology-lib'

export default {
  data() {
    return {
      list: [],
      btnLoading: false,
      selectList: [],
      bomList: [],
      tabBomList: [],
      comName: '',
      partName: '',
      bomActiveName: '',
      parentBomList: []
    }
  },
  computed: {
    // 根据当前选中的BOM层级过滤数据
    filteredList() {
      if (!this.list || !this.bomActiveName) {
        return []
      }
      return this.list.filter(item => item.Use_Bom_Level.toString() === this.bomActiveName)
    }
  },
  async mounted() {
    await this.getBom()
    await this.getParentBom()
    await this.getProcessList()
  },
  methods: {
    async getBom() {
      const { comName, partName, list } = await GetBOMInfo()
      this.comName = comName
      this.partName = partName
      this.bomList = list
      this.tabBomList = list.filter(i => i.Code !== '-1')
      this.bomActiveName = this.tabBomList[this.tabBomList.length - 1].Code
    },
    getTypeList() {
      GetConsumingAllList({}).then(res => {
        if (res.IsSucceed) {
          const resData = res.Data
          const Process_List_All = []
          this.parentBomList.map(item => {
            const Process_List_Json = {}
            Process_List_Json.Bom_Level = item.Code
            Process_List_Json.Working_Process_Id = ''
            Process_List_Json.Working_Process_Code = ''
            Process_List_Json.Working_Process_Name = ''
            Process_List_All.push(Process_List_Json)
          })

          // 遍历 resData 中的每个项目，为每个项目的 Process_List 添加缺失的数据
          resData.forEach(dataItem => {
            // 确保每个项目的 Process_List 存在
            if (!dataItem.Process_List) {
              dataItem.Process_List = []
            }

            // 如果 Process_List 为空，直接追加所有 Process_List_All 的数据（深拷贝）
            if (dataItem.Process_List.length === 0) {
              Process_List_All.forEach(item => {
                dataItem.Process_List.push({
                  Bom_Level: item.Bom_Level,
                  Working_Process_Id: item.Working_Process_Id,
                  Working_Process_Code: item.Working_Process_Code,
                  Working_Process_Name: item.Working_Process_Name
                })
              })
            } else {
              // 将 Process_List_All 中不存在于当前项目 Process_List 的数据追加进去
              Process_List_All.forEach(newItem => {
                // 检查当前项目的 Process_List 中是否已存在相同的 Bom_Level
                const exists = dataItem.Process_List.some(existingItem =>
                  existingItem.Bom_Level.toString() === newItem.Bom_Level.toString()
                )

                // 如果不存在，则追加到当前项目的 Process_List（深拷贝）
                if (!exists) {
                  dataItem.Process_List.push({
                    Bom_Level: newItem.Bom_Level,
                    Working_Process_Id: newItem.Working_Process_Id,
                    Working_Process_Code: newItem.Working_Process_Code,
                    Working_Process_Name: newItem.Working_Process_Name
                  })
                }
              })
            }
          })
          this.list = resData
          // 确保数据独立性
          this.$nextTick(() => {
            this.ensureDataIndependence()
          })
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    getProcessList() {
      GetProcessList({ }).then(res => {
        this.selectList = res.Data
      }).finally(() => {
        this.getTypeList()
      })
    },
    handleSubmit() {
      this.btnLoading = true
      SaveConsumingProcessAllList2(this.list).then(res => {
        if (res.IsSucceed) {
          this.$message.success('保存成功')
          this.$emit('close')
        } else {
          this.$message.error(res.Message)
        }
      }).finally(() => {
        this.btnLoading = false
      })
    },
    bomClick() {
      this.getParentBom()
    },
    // 获取当前bom层级的所有父级bom层级信息
    getParentBom() {
      // 找到当前code在bomList中的索引位置
      const currentIndex = this.bomList.findIndex(item => item.Code === this.bomActiveName)

      // 如果找到了，则截取该索引之前的所有数据
      if (currentIndex > 0) {
        this.parentBomList = this.bomList.slice(0, currentIndex)
      } else {
        // 如果是第一个或者没找到，则返回空数组
        this.parentBomList = []
      }
    },
    // 根据BOM层级Code过滤selectList
    getFilteredSelectList(code) {
      if (!this.selectList || !code) {
        return []
      }
      return this.selectList.filter(item => item.Bom_Level.toString() === code || item.Bom_Level.toString() === code)
    },
    // 获取指定item和bomCode对应的Working_Process_Id
    getProcessId(item, bomCode) {
      if (!item.Process_List || !bomCode) {
        return ''
      }

      // 确保 Process_List 是数组
      if (!Array.isArray(item.Process_List)) {
        return ''
      }

      const processItem = item.Process_List.find(p =>
        p && p.Bom_Level && p.Bom_Level.toString() === bomCode.toString()
      )

      return processItem ? (processItem.Working_Process_Id || '') : ''
    },
    // 更新指定item和bomCode对应的Working_Process_Id
    updateProcessId(item, bomCode, value) {
      // 确保 Process_List 存在且是数组
      if (!item.Process_List || !Array.isArray(item.Process_List)) {
        this.$set(item, 'Process_List', [])
      }

      // 查找对应的 Bom_Level 项目
      const processItemIndex = item.Process_List.findIndex(p =>
        p && p.Bom_Level && p.Bom_Level.toString() === bomCode.toString()
      )

      if (processItemIndex !== -1) {
        // 如果找到了对应的项目，更新其值
        const processItem = item.Process_List[processItemIndex]

        // 使用 $set 确保响应式更新
        this.$set(processItem, 'Working_Process_Id', value)

        // 同时更新对应的工艺信息
        if (value) {
          const selectedProcess = this.selectList.find(s => s.Id === value)
          if (selectedProcess) {
            this.$set(processItem, 'Working_Process_Code', selectedProcess.Code || '')
            this.$set(processItem, 'Working_Process_Name', selectedProcess.Name || '')
          }
        } else {
          this.$set(processItem, 'Working_Process_Code', '')
          this.$set(processItem, 'Working_Process_Name', '')
        }
      } else {
        // 如果没找到，创建新的项目
        const newProcessItem = {
          Bom_Level: bomCode,
          Working_Process_Id: value,
          Working_Process_Code: '',
          Working_Process_Name: ''
        }

        // 如果有选中值，填充工艺信息
        if (value) {
          const selectedProcess = this.selectList.find(s => s.Id === value)
          if (selectedProcess) {
            newProcessItem.Working_Process_Code = selectedProcess.Code || ''
            newProcessItem.Working_Process_Name = selectedProcess.Name || ''
          }
        }

        // 使用 $set 添加新项目
        item.Process_List.push(newProcessItem)
      }
    },
    // 确保每个item的Process_List都是独立的
    ensureDataIndependence() {
      if (this.list && Array.isArray(this.list)) {
        this.list.forEach(item => {
          if (item.Process_List && Array.isArray(item.Process_List)) {
            // 深拷贝 Process_List 确保数据独立
            const originalProcessList = item.Process_List
            item.Process_List = originalProcessList.map(processItem => ({
              Bom_Level: processItem.Bom_Level,
              Working_Process_Id: processItem.Working_Process_Id || '',
              Working_Process_Code: processItem.Working_Process_Code || '',
              Working_Process_Name: processItem.Working_Process_Name || ''
            }))
          }
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import "~@/styles/mixin.scss";
.form-wrapper {
  height: 50vh;
  display: flex;
  flex-direction: column;

  .form-content {
    flex: 1;
    overflow: auto;
    padding-right: 16px;
    @include scrollBar;

    .form-x {
      padding-bottom: 20px;
    }
  }

  .form-footer {
    text-align: right;
    flex-shrink: 0;
    padding-top: 16px;
    background: #fff;
  }
}

.can-process-title {
  display: flex;
  height: 38px;
  .can-process-list {
    flex: 1;
    display: flex;
    justify-content: flex-start;
    font-size: 16px;
    color: #333333;
  }
}

.can-process-box {
  height: calc(100% - 38px);
  .can-process-empty {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .can-process-item {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 16px;
    .can-process-type {
      font-size: 14px;
      color: #333333;
      text-align: right;
    }
    .can-process-bom {
      flex: 1;
      display: flex;
      justify-content: flex-start;
      .can-process-select {
        margin-left: 12px;
        text-align: center;
      }
    }
  }
}
</style>

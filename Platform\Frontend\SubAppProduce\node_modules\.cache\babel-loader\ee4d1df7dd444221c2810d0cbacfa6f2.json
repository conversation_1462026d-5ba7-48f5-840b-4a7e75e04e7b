{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\production-execution\\new-report\\home.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\production-execution\\new-report\\home.vue", "mtime": 1758266753126}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["ExpandableSection", "Pagination", "PartDetail", "tablePageSize", "getTbInfo", "ComReport", "PartReport", "TreeDetail", "Qitao", "GetProjectAreaTreeList", "GetTeamListByUser", "GetCompTaskPageList", "GetCompTaskPartCompletionStock", "GetDwg", "GetCompTaskList", "GetSimplifiedPartTaskList", "GetSimplifiedPartTaskPageList", "GetStopList", "parseOssUrl", "mapGetters", "scroll", "timeFormat", "GetProcessingProgressTask", "ProcessDialog", "numeral", "GetBOMInfo", "SPLIT_SYMBOL", "components", "mixins", "inject", "data", "tid", "comName", "compCode", "partName", "projectName", "statusType", "expandedKey", "treeLoading", "treeData", "showExpand", "teamOptions", "value", "offset", "itemWidth", "taskList", "form", "Nesting_Result_Name", "Working_Team_Id", "Code_Like", "Task_Code", "Sys_Project_Id", "Area_Id", "InstallUnit_Id", "Production_Status", "Spec_Like", "tbLoading", "showNestPart", "columns", "tbData", "tbConfig", "queryInfo", "Page", "PageSize", "total", "dialogVisible", "currentComponent", "dialogTitle", "width", "IsShowBtn", "multipleSelection", "pgLoading", "isNest", "disableLeftBtn", "disableRightBtn", "computed", "_objectSpread", "isCom", "pageType", "statusKey", "filterText", "expandAll", "process", "env", "NODE_ENV", "mounted", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "_yield$GetBOMInfo", "list", "wrap", "_callee$", "_context", "prev", "next", "sent", "bomList", "window", "addEventListener", "checkResize", "getTableConfig", "getFactoryInfo", "getProcessTeam", "fetchTreeData", "stop", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "methods", "getData", "row", "_this2", "$nextTick", "_", "$refs", "handleOpen", "Code", "Type", "Task_Id", "ProcessName", "Working_Process_Name", "TeamName", "Working_Team_Name", "Schduling_Code", "split", "TeamId", "_this3", "_callee2", "_callee2$", "_context2", "$store", "dispatch", "console", "log", "Is_Part_Prepare", "_this4", "_callee3", "_callee3$", "_context3", "MenuId", "$route", "meta", "Id", "type", "then", "res", "Data", "length", "resData", "map", "item", "Is_Directory", "tree", "filterRef", "result", "<PERSON><PERSON><PERSON>", "_this5", "deepFilter", "i", "Children", "node", "getNode", "ParentId", "visible", "handleNodeClick", "shouldStop", "key", "getNodeByKey", "currentNodeData", "areaId", "getTaskList", "saveSortFinish", "customFilterFun", "arr", "labelVal", "statusVal", "parentNode", "parent", "labels", "label", "status", "level", "concat", "_toConsumableArray", "filter", "v", "resultLabel", "resultStatus", "some", "s", "indexOf", "_this6", "_callee4", "_this6$teamOptions$", "_callee4$", "_context4", "Name", "handleTeamChange", "_this7", "_callee5", "requestFn", "_this7$taskList$", "cur", "_callee5$", "_context5", "IsSucceed", "Demand_Date", "percentage", "Is_Nest", "checkShowP", "Need_Part_Amount", "getPercentDetail", "$message", "error", "Message", "fetchData", "t0", "finish", "handleTask", "scrollToActiveItem", "_this8", "itemElement", "scrollIntoView", "behavior", "inline", "refresh", "page", "_this9", "_callee6", "ids", "_callee6$", "_context6", "toString", "Show_Nest_Part", "_res$Data", "checked", "TotalCount", "getStopList", "message", "finally", "e", "_this0", "_callee7", "submitObj", "_callee7$", "_context7", "stopMap", "for<PERSON>ach", "Is_Stop", "$set", "handleDwg", "_this1", "_res$Data2", "fileurl", "File_Url", "open", "handleQitao", "_this10", "Comp_Code", "viewPart", "_this11", "obj", "Need_Use_Part", "init", "checCheckboxkMethod3", "_ref", "Prepare_Count", "Ready_Process_Count", "multiSelectedChange", "array", "records", "clickMove", "direction", "_this$$refs$middleWap", "_this$$refs$boxWapper", "middleWapper<PERSON>idth", "middleWapper", "offsetWidth", "boxWapperWidth", "boxWapper", "scrollWidth", "Math", "min", "maxOffset", "max", "showNestChange", "val", "handleReport", "_this12", "JSON", "parse", "stringify", "handleClose", "num", "_this13", "taskMap", "p", "Part_Stock_Count", "divide", "format"], "sources": ["src/views/PRO/production-execution/new-report/home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <div\r\n      v-loading=\"pgLoading\"\r\n      element-loading-text=\"加载中\"\r\n      class=\"h100 app-wrapper\"\r\n    >\r\n      <ExpandableSection v-model=\"showExpand\" :width=\"300\" class=\"cs-left fff\">\r\n        <div class=\"inner-wrapper\">\r\n          <div class=\"cs-search\">\r\n            <el-row>\r\n              <el-col :span=\"24\" class=\"team-select\">\r\n                <el-select\r\n                  v-model=\"form.Working_Team_Id\"\r\n                  clearable\r\n                  placeholder=\"请选择\"\r\n                  @change=\"handleTeamChange\"\r\n                >\r\n                  <i slot=\"prefix\">\r\n                    <img src=\"@/assets/PRO/icon-search.png\" alt=\"\">\r\n                  </i>\r\n                  <el-option\r\n                    v-for=\"item in teamOptions\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"item.value\"\r\n                  />\r\n                </el-select>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row :span=\"24\" :gutter=\"8\">\r\n              <el-col :span=\"10\">\r\n                <el-select v-model=\"statusType\" clearable placeholder=\"请选择\">\r\n                  <el-option label=\"所有状态\" value=\"\" />\r\n                  <el-option label=\"待报工\" value=\"待报工\" />\r\n                  <el-option label=\"报工完成\" value=\"报工完成\" />\r\n                  <el-option label=\"排产未完成\" value=\"排产未完成\" />\r\n                </el-select>\r\n              </el-col>\r\n              <el-col :span=\"14\">\r\n                <el-input\r\n                  v-model.trim=\"projectName\"\r\n                  placeholder=\"搜索...\"\r\n                  size=\"small\"\r\n                  clearable\r\n                  suffix-icon=\"el-icon-search\"\r\n                />\r\n              </el-col>\r\n            </el-row>\r\n          </div>\r\n\r\n          <div class=\"cs-tree cs-scroll\">\r\n            <tree-detail\r\n              ref=\"tree\"\r\n              :default-expand-all=\"expandAll\"\r\n              icon=\"icon-folder\"\r\n              is-custom-filter\r\n              :custom-filter-fun=\"customFilterFun\"\r\n              :loading=\"treeLoading\"\r\n              :tree-data=\"treeData\"\r\n              show-status\r\n              show-detail\r\n              :filter-text=\"filterText\"\r\n              :expanded-key=\"expandedKey\"\r\n              :can-node-click=\"false\"\r\n              @handleNodeClick=\"handleNodeClick\"\r\n              @saveSortFinish=\"saveSortFinish\"\r\n            >\r\n              <template #csLabel=\"{ showStatus, data }\">\r\n                <span\r\n                  v-if=\"!data.ParentNodes\"\r\n                  class=\"cs-blue\"\r\n                >({{ data.Code }})</span>{{ data.Label }}\r\n                <template v-if=\"showStatus\">\r\n                  <i\r\n                    v-if=\"data.Data[statusKey]\"\r\n                    :class=\"[\r\n                      data.Data[statusKey] == '报工完成'\r\n                        ? 'fourGreen'\r\n                        : data.Data[statusKey] == '待报工'\r\n                          ? 'fourOrange'\r\n                          : data.Data[statusKey] == '排产未完成'\r\n                            ? 'fourRed'\r\n                            : '',\r\n                    ]\"\r\n                  >\r\n                    <span>({{ data.Data[statusKey] }})</span>\r\n                  </i>\r\n                </template>\r\n              </template>\r\n            </tree-detail>\r\n          </div>\r\n        </div>\r\n      </ExpandableSection>\r\n      <div class=\"cs-right fff\">\r\n        <div v-show=\"taskList.length > 0\" class=\"cs-top-wapper\">\r\n          <div\r\n            v-if=\"IsShowBtn\"\r\n            class=\"btn\"\r\n            :class=\"{ disabled: disableLeftBtn }\"\r\n            @click=\"clickMove('left')\"\r\n          >\r\n            <i class=\"el-icon-arrow-left\" />\r\n          </div>\r\n          <div\r\n            ref=\"middleWapper\"\r\n            class=\"middle-wapper\"\r\n            @mousedown.prevent=\"handleMouseDown\"\r\n            @mouseleave.prevent=\"handleMouseLeave\"\r\n            @mouseup.prevent=\"handleMouseUp\"\r\n            @mousemove.prevent=\"handleMouseMove\"\r\n          >\r\n            <div\r\n              ref=\"boxWapper\"\r\n              class=\"box-wapper\"\r\n              :style=\"{ transform: `translateX(${offset}px)` }\"\r\n            >\r\n              <div\r\n                v-for=\"(item, index) in taskList\"\r\n                :key=\"index\"\r\n                :ref=\"'task_' + item.Task_Code\"\r\n                :class=\"[\r\n                  'item',\r\n                  form.Task_Code === item.Task_Code ? 'active' : '',\r\n                ]\"\r\n                @click=\"handleTask(item)\"\r\n              >\r\n                <div v-if=\"item.Return_Count > 0\" class=\"flag\">退</div>\r\n                <div v-if=\"item.Is_Nest\" class=\"flag2\">\r\n                  <span class=\"flag2-txt\">套</span>\r\n                </div>\r\n                <div class=\"content\">\r\n                  <div class=\"title\">\r\n                    <span class=\"name\"> {{ item.Task_Code }}</span>\r\n                    <!--                    <span v-if=\"isCom && item.Part_Completion_Rate !== null\" class=\"precent\">\r\n                      {{ item.Part_Completion_Rate > 1 ? 100 : Math.round(item.Part_Completion_Rate * 100) }}%\r\n                    </span>-->\r\n                    <span\r\n                      v-if=\"checkShowP(item.Need_Part_Amount)\"\r\n                      class=\"precent\"\r\n                    >{{ item.percentage }}</span>\r\n                  </div>\r\n                  <div class=\"detail\">\r\n                    <div class=\"left\">\r\n                      <div class=\"info\">要求：{{ item.Demand_Date }}</div>\r\n                      <div class=\"info\">\r\n                        总量：{{ item.Total_Count }}/{{\r\n                          parseFloat((item.Total_Weight / 1000).toFixed(3))\r\n                        }}t\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div\r\n                      v-if=\"isCom && checkShowP(item.Need_Part_Amount)\"\r\n                      class=\"right\"\r\n                      @click.stop=\"viewPart(item)\"\r\n                    >\r\n                      查看零件\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <i\r\n                  v-if=\"form.Task_Code === item.Task_Code\"\r\n                  class=\"el-icon-caret-bottom\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div\r\n            v-if=\"IsShowBtn\"\r\n            class=\"btn\"\r\n            :class=\"{ disabled: disableRightBtn }\"\r\n            @click=\"clickMove('right')\"\r\n          >\r\n            <i class=\"el-icon-arrow-right\" />\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"cs-middle-wapper\">\r\n          <div class=\"cs-middle-wapper-left\">\r\n            <el-button\r\n              type=\"primary\"\r\n              :disabled=\"!multipleSelection.length || multipleSelection.some(item=>item.stopFlag)\"\r\n              @click=\"handleReport()\"\r\n            >报工</el-button>\r\n            <el-checkbox\r\n              v-if=\"!isCom\"\r\n              v-model=\"showNestPart\"\r\n              class=\"cs-checkbox\"\r\n              @change=\"showNestChange\"\r\n            >显示套料零件</el-checkbox>\r\n          </div>\r\n\r\n          <el-form ref=\"form\" :model=\"form\" label-width=\"80px\" inline>\r\n            <el-form-item v-if=\"!isCom\" label=\"排版编号\">\r\n              <el-input\r\n                v-model=\"form.Nesting_Result_Name\"\r\n                placeholder=\"请输入\"\r\n                clearable\r\n              />\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"生产状态\">\r\n              <el-select\r\n                v-model=\"form.Production_Status\"\r\n                placeholder=\"请选择生产状态\"\r\n                style=\"width: 140px\"\r\n                clearable\r\n              >\r\n                <el-option label=\"未完成\" value=\"未完成\" />\r\n                <el-option label=\"已完成\" value=\"已完成\" />\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item :label=\"isCom ? `${comName}名称` : `${partName}名称`\">\r\n              <el-input\r\n                v-model=\"form.Code_Like\"\r\n                :placeholder=\"isCom ? `请输入${comName}名称` : `请输入${partName}名称`\"\r\n                clearable\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"规格\" label-width=\"50px\">\r\n              <el-input\r\n                v-model=\"form.Spec_Like\"\r\n                style=\"width: 140px\"\r\n                placeholder=\"请输入规格\"\r\n                clearable\r\n              />\r\n            </el-form-item>\r\n\r\n            <el-form-item>\r\n              <el-button type=\"primary\" @click=\"refresh\">搜索</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n\r\n        <div class=\"cs-bottom-wapper\">\r\n          <div class=\"fff tb-x\">\r\n            <vxe-table\r\n              :empty-render=\"{ name: 'NotData' }\"\r\n              show-header-overflow\r\n              :loading=\"tbLoading\"\r\n              element-loading-spinner=\"el-icon-loading\"\r\n              element-loading-text=\"拼命加载中\"\r\n              empty-text=\"暂无数据\"\r\n              class=\"cs-vxe-table\"\r\n              height=\"100%\"\r\n              align=\"left\"\r\n              stripe\r\n              :data=\"tbData\"\r\n              resizable\r\n              :tooltip-config=\"{ enterable: true }\"\r\n              :checkbox-config=\"{\r\n                checkField: 'checked',\r\n                trigger: 'row',\r\n                checkMethod: checCheckboxkMethod3,\r\n              }\"\r\n              @checkbox-all=\"multiSelectedChange\"\r\n              @checkbox-change=\"multiSelectedChange\"\r\n            >\r\n              <vxe-column fixed=\"left\" type=\"checkbox\" />\r\n              <template v-for=\"item in columns\">\r\n                <vxe-column\r\n                  :key=\"item.Code\"\r\n                  :min-width=\"item.Width\"\r\n                  width=\"auto\"\r\n                  :fixed=\"\r\n                    ['Comp_Code', 'Part_Code'].includes(item.Code) ? 'left' : ''\r\n                  \"\r\n                  show-overflow=\"tooltip\"\r\n                  sortable\r\n                  :align=\"item.Align\"\r\n                  :field=\"item.Code\"\r\n                  :title=\"item.Display_Name\"\r\n                >\r\n                  <template\r\n                    v-if=\"\r\n                      item.Code === 'Comp_Code' || item.Code === 'Part_Code'\r\n                    \"\r\n                    #default=\"{ row }\"\r\n                  >\r\n                    <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                    <el-tag\r\n                      v-if=\"row.Is_Change\"\r\n                      style=\"margin-right: 6px\"\r\n                      type=\"danger\"\r\n                      class=\"cs-tag\"\r\n                    >变</el-tag>\r\n                    <el-link\r\n                      v-if=\"row.DwgCount > 0\"\r\n                      type=\"primary\"\r\n                      @click.stop=\"handleDwg(row)\"\r\n                    >\r\n                      {{ row[item.Code] | displayValue }}</el-link>\r\n                    <span v-else> {{ row[item.Code] | displayValue }}</span>\r\n                  </template>\r\n                  <template\r\n                    v-else-if=\"item.Code === 'Prepare_Count'\"\r\n                    #default=\"{ row }\"\r\n                  >\r\n                    <el-link\r\n                      v-if=\"typeof row.Prepare_Count === 'number'\"\r\n                      type=\"primary\"\r\n                      @click.stop=\"handleQitao(row)\"\r\n                    >\r\n                      {{ row[item.Code] || 0 }}</el-link>\r\n                    <span v-else> {{ row[item.Code] | displayValue }}</span>\r\n                  </template>\r\n                  <template\r\n                    v-else-if=\"item.Code === 'Production_Status'\"\r\n                    #default=\"{ row }\"\r\n                  >\r\n                    <span\r\n                      :class=\"\r\n                        row.Production_Status === '未完成'\r\n                          ? 'fourRed'\r\n                          : row.Production_Status === '已完成'\r\n                            ? 'fourGreen'\r\n                            : ''\r\n                      \"\r\n                    >{{ row.Production_Status }}</span>\r\n                  </template>\r\n                  <template\r\n                    v-else-if=\"item.Code === 'Working_Process_Name'\"\r\n                    #default=\"{ row }\"\r\n                  >\r\n                    <el-link type=\"primary\" @click=\"getData(row)\">\r\n                      {{ row[item.Code] }}</el-link>\r\n                  </template>\r\n                  <template v-else #default=\"{ row }\">\r\n                    <span> {{ row[item.Code] | displayValue }}</span>\r\n                  </template>\r\n                </vxe-column>\r\n              </template></vxe-table>\r\n          </div>\r\n          <div class=\"data-info\">\r\n            <el-tag\r\n              size=\"medium\"\r\n              class=\"info-x\"\r\n            >已选 {{ multipleSelection.length }} 条数据\r\n            </el-tag>\r\n            <Pagination\r\n              :total=\"total\"\r\n              :page-sizes=\"tablePageSize\"\r\n              :page.sync=\"queryInfo.Page\"\r\n              :limit.sync=\"queryInfo.PageSize\"\r\n              @pagination=\"pageChange\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      ref=\"content\"\r\n      v-dialogDrag\r\n      :title=\"dialogTitle\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"width\"\r\n      class=\"plm-custom-dialog\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :is-nest=\"isNest\"\r\n        :tid=\"tid\"\r\n        :comp-code=\"compCode\"\r\n        @close=\"handleClose\"\r\n        @refresh=\"refresh\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <ProcessDialog ref=\"process\" :show-search=\"false\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport Pagination from '@/components/Pagination'\r\nimport PartDetail from './components/PartDetail'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport ComReport from './components/ComReport'\r\nimport PartReport from './components/PartReport.vue'\r\nimport TreeDetail from '@/components/TreeDetail/index.vue'\r\nimport Qitao from './components/Qitao'\r\nimport { GetProjectAreaTreeList } from '@/api/PRO/project'\r\nimport { GetTeamListByUser } from '@/api/PRO/technology-lib'\r\nimport {\r\n  GetCompTaskPageList,\r\n  GetCompTaskPartCompletionStock,\r\n  GetDwg\r\n} from '@/api/PRO/production-task'\r\nimport {\r\n  GetCompTaskList,\r\n  GetSimplifiedPartTaskList,\r\n  GetSimplifiedPartTaskPageList\r\n} from '@/api/PRO/production-report-new'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nimport { parseOssUrl } from '@/utils/file'\r\nimport { mapGetters } from 'vuex'\r\nimport scroll from './mixin/scroll'\r\nimport { timeFormat } from '@/utils/timeFormat'\r\nimport { GetProcessingProgressTask } from '@/api/PRO/processingprogress'\r\nimport ProcessDialog from '@/views/PRO/basic-information/production-scheduling/process/processdialog.vue'\r\nimport numeral from 'numeral'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\n\r\nconst SPLIT_SYMBOL = '$_$'\r\nexport default {\r\n  components: {\r\n    ExpandableSection,\r\n    Pagination,\r\n    ComReport,\r\n    TreeDetail,\r\n    Qitao,\r\n    PartDetail,\r\n    ProcessDialog,\r\n    PartReport\r\n  },\r\n  mixins: [getTbInfo, scroll],\r\n  inject: ['pageType'],\r\n  data() {\r\n    return {\r\n      tid: '',\r\n      comName: '',\r\n      compCode: '',\r\n      partName: '',\r\n      projectName: '',\r\n      statusType: '待报工',\r\n      expandedKey: '',\r\n      treeLoading: false,\r\n      treeData: [],\r\n      showExpand: true,\r\n      teamOptions: [],\r\n      value: '',\r\n      offset: 0,\r\n      itemWidth: 275 + 12, // item width + margin\r\n      taskList: [],\r\n      form: {\r\n        Nesting_Result_Name: '',\r\n        Working_Team_Id: '',\r\n        Code_Like: '',\r\n        Task_Code: '',\r\n        Sys_Project_Id: '',\r\n        Area_Id: '',\r\n        InstallUnit_Id: '',\r\n        Production_Status: '',\r\n        Spec_Like: ''\r\n      },\r\n      tbLoading: false,\r\n      showNestPart: false,\r\n      columns: [],\r\n      tbData: [],\r\n      tbConfig: {},\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: tablePageSize[0]\r\n      },\r\n      total: 0,\r\n      tablePageSize: tablePageSize,\r\n      dialogVisible: false,\r\n      currentComponent: '',\r\n      dialogTitle: '',\r\n      width: '80%',\r\n      IsShowBtn: false,\r\n      multipleSelection: [],\r\n      pgLoading: false,\r\n      isNest: false,\r\n      disableLeftBtn: true,\r\n      disableRightBtn: false\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    },\r\n    statusKey() {\r\n      return this.pageType === 'com'\r\n        ? 'Comp_Produce_Status'\r\n        : 'Part_Produce_Status'\r\n    },\r\n    filterText() {\r\n      return this.projectName + SPLIT_SYMBOL + this.statusType\r\n    },\r\n    expandAll() {\r\n      return process.env.NODE_ENV !== 'development'\r\n    },\r\n    ...mapGetters('factoryInfo', [\r\n      'Is_Skip_Warehousing_Operation',\r\n      'Nested_Must_Before_Processing',\r\n      'Is_Part_Prepare'\r\n    ])\r\n  },\r\n\r\n  async mounted() {\r\n    const { list, partName, comName } = await GetBOMInfo()\r\n    this.bomList = list || []\r\n    this.partName = partName\r\n    this.comName = comName\r\n    this.pgLoading = true\r\n    window.addEventListener('resize', this.checkResize)\r\n    this.checkResize() // initial check\r\n    this.getTableConfig(\r\n      this.isCom ? 'PROProductionComNewConfig' : 'PROProductionPartNewConfig'\r\n    )\r\n    this.getFactoryInfo()\r\n    await this.getProcessTeam()\r\n    await this.fetchTreeData()\r\n    // await this.getTaskList()\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.checkResize)\r\n  },\r\n  methods: {\r\n    getData(row) {\r\n      this.$nextTick((_) => {\r\n        this.$refs['process'].handleOpen({\r\n          Code: row.Task_Code,\r\n          Type: this.isCom ? '0' : '1',\r\n          Task_Id: row.Task_Id,\r\n          ProcessName: row.Working_Process_Name,\r\n          TeamName: row.Working_Team_Name,\r\n          Schduling_Code: row?.Task_Code.split('-')[0],\r\n          TeamId: this.form.Working_Team_Id\r\n        })\r\n      })\r\n    },\r\n    async getFactoryInfo() {\r\n      await this.$store.dispatch('factoryInfo/getWorkshop')\r\n      console.log(this.Is_Part_Prepare, '====')\r\n    },\r\n    async fetchTreeData() {\r\n      console.log('fetchTreeData')\r\n      this.treeLoading = true\r\n      await GetProjectAreaTreeList({\r\n        MenuId: this.$route.meta.Id,\r\n        projectName: this.projectName,\r\n        type: this.isCom ? 3 : 4\r\n      }).then((res) => {\r\n        if (res.Data.length === 0) {\r\n          this.treeLoading = false\r\n          return\r\n        }\r\n        const resData = res.Data.map((item) => {\r\n          item.Is_Directory = true\r\n\r\n          return item\r\n        })\r\n        this.treeData = resData\r\n        // this.expandedKey = resData[0]?.Children[0]?.Id\r\n        // this.form.Area_Id = this.expandedKey\r\n        // this.form.Sys_Project_Id = resData[0].Data.Sys_Project_Id\r\n        this.$nextTick((_) => {\r\n          this.$refs.tree.filterRef(this.filterText)\r\n          const result = this.setKey()\r\n          if (!result) {\r\n            this.pgLoading = false\r\n          }\r\n        })\r\n        this.treeLoading = false\r\n      })\r\n    },\r\n    setKey() {\r\n      const deepFilter = (tree) => {\r\n        for (let i = 0; i < tree.length; i++) {\r\n          const item = tree[i]\r\n          const { Data, Children } = item\r\n          const node = getNode(Data.Id)\r\n          if (Data.ParentId && !Children?.length && node.visible) {\r\n            this.handleNodeClick(item)\r\n            return true\r\n          } else {\r\n            if (Children?.length) {\r\n              const shouldStop = deepFilter(Children)\r\n              if (shouldStop) return true\r\n            }\r\n          }\r\n        }\r\n        return false\r\n      }\r\n      const getNode = (key) => {\r\n        return this.$refs['tree'].getNodeByKey(key)\r\n      }\r\n      return deepFilter(this.treeData)\r\n    },\r\n    handleNodeClick(data) {\r\n      this.expandedKey = data.Id\r\n      this.currentNodeData = data\r\n      this.areaId = data.Id\r\n      this.form.Area_Id = data.Id\r\n      this.form.Sys_Project_Id = data.Data.Sys_Project_Id\r\n      this.multipleSelection = []\r\n      this.getTaskList()\r\n    },\r\n\r\n    saveSortFinish() {\r\n      this.$refs.tree.filterRef(this.filterText)\r\n    },\r\n\r\n    customFilterFun(value, data, node) {\r\n      const arr = value.split(SPLIT_SYMBOL)\r\n      const labelVal = arr[0]\r\n      const statusVal = arr[1]\r\n      if (!value) return true\r\n      let parentNode = node.parent\r\n      let labels = [node.label]\r\n      let status = [data.Data[this.statusKey]]\r\n      let level = 1\r\n      while (level < node.level) {\r\n        labels = [...labels, parentNode.label]\r\n        status = [...status, data.Data[this.statusKey]]\r\n        parentNode = parentNode.parent\r\n        level++\r\n      }\r\n      labels = labels.filter((v) => !!v)\r\n      status = status.filter((v) => !!v)\r\n      let resultLabel = true\r\n      let resultStatus = true\r\n      if (this.statusType) {\r\n        resultStatus = status.some((s) => s.indexOf(statusVal) !== -1)\r\n      }\r\n      if (this.projectName) {\r\n        resultLabel = labels.some((s) => s.indexOf(labelVal) !== -1)\r\n      }\r\n      return resultLabel && resultStatus\r\n    },\r\n\r\n    // 获取班组\r\n    async getProcessTeam() {\r\n      await GetTeamListByUser({\r\n        type: this.isCom ? 1 : 2 // 0:全部，工艺类型1：构件工艺，2：零件工艺\r\n      }).then((res) => {\r\n        this.teamOptions = res.Data.map((item) => {\r\n          return {\r\n            value: item.Id,\r\n            label: item.Name\r\n          }\r\n        })\r\n      })\r\n      this.form.Working_Team_Id = this.teamOptions[0]?.value || ''\r\n    },\r\n    handleTeamChange(value) {\r\n      this.form.Working_Team_Id = value\r\n      this.getTaskList()\r\n    },\r\n    // 获取任务单列表\r\n    async getTaskList() {\r\n      this.form.Task_Code = ''\r\n      this.pgLoading = true\r\n      const requestFn = this.isCom\r\n        ? GetCompTaskList\r\n        : GetSimplifiedPartTaskList\r\n\r\n      try {\r\n        const res = await requestFn(this.form)\r\n        if (res.IsSucceed) {\r\n          this.taskList = (res.Data || []).map((v) => {\r\n            v.Demand_Date = timeFormat(v.Demand_Date)\r\n            v.percentage = 0\r\n            return v\r\n          })\r\n          this.isNest = this.taskList.some((v) => !!v.Is_Nest)\r\n          this.form.Task_Code = this.taskList[0]?.Task_Code || ''\r\n          if (this.taskList.length > 0) {\r\n            const cur = this.taskList[0]\r\n            if (this.checkShowP(cur.Need_Part_Amount)) {\r\n              this.getPercentDetail()\r\n            }\r\n          }\r\n        } else {\r\n          this.taskList = []\r\n          this.$message.error(res.Message)\r\n        }\r\n\r\n        if (this.taskList.length > 0) {\r\n          await this.fetchData(1)\r\n        } else {\r\n          this.tbData = []\r\n        }\r\n      } catch (error) {\r\n      } finally {\r\n        this.checkResize() // initial check\r\n        this.pgLoading = false\r\n      }\r\n    },\r\n    handleTask(item) {\r\n      this.form.Task_Code = item.Task_Code\r\n      this.scrollToActiveItem(item)\r\n      this.multipleSelection = []\r\n      this.form.Code_Like = ''\r\n      this.form.Production_Status = ''\r\n      this.form.Spec_Like = ''\r\n      this.isNest = item.Is_Nest\r\n      this.fetchData(1)\r\n    },\r\n    scrollToActiveItem(item) {\r\n      this.$nextTick(() => {\r\n        const itemElement = this.$refs[`task_${item.Task_Code}`][0]\r\n        console.log(itemElement)\r\n        if (itemElement) {\r\n          itemElement.scrollIntoView({ behavior: 'smooth', inline: 'center' })\r\n        }\r\n      })\r\n    },\r\n    refresh() {\r\n      this.multipleSelection = []\r\n      this.fetchData(1)\r\n    },\r\n    async fetchData(page) {\r\n      page && (this.queryInfo.Page = page)\r\n      const form = { ...this.form, ...this.queryInfo }\r\n      if (form.Working_Team_Id === 'all') {\r\n        const ids = this.teamOptions\r\n          .map((v) => v.value)\r\n          .filter((s) => s !== 'all')\r\n          .toString()\r\n        form.Working_Team_Id = ids\r\n      }\r\n      if (!this.isCom) {\r\n        form.Show_Nest_Part = this.showNestPart\r\n      }\r\n      this.tbLoading = true\r\n      const requestFn = this.isCom\r\n        ? GetCompTaskPageList\r\n        : GetSimplifiedPartTaskPageList\r\n      await requestFn(form)\r\n        .then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.tbData = (res?.Data?.Data || []).map((v) => {\r\n              v.checked = false\r\n              return v\r\n            })\r\n            this.total = res.Data.TotalCount\r\n            this.getStopList(this.tbData)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.tbLoading = false\r\n        })\r\n        .finally((e) => {\r\n          this.tbLoading = false\r\n        })\r\n    },\r\n    async getStopList(list) {\r\n      const key = 'Id'\r\n      const submitObj = list.map(item => {\r\n        return {\r\n          Id: item[key],\r\n          Type: this.isCom ? 2 : 1 // 1：零件，3：部件，2：构件\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = !!item.Is_Stop\r\n          })\r\n          list.forEach(row => {\r\n            if (stopMap[row[key]]) {\r\n              this.$set(row, 'stopFlag', stopMap[row[key]])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 查看图纸\r\n    handleDwg(row) {\r\n      GetDwg({\r\n        Task_Id: row.Task_Id\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const fileurl = res?.Data?.length && res.Data[0].File_Url\r\n          window.open(\r\n            'http://dwgv1.bimtk.com:5432/?CadUrl=' + parseOssUrl(fileurl),\r\n            '_blank'\r\n          )\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 查看齐套弹框\r\n    handleQitao(row) {\r\n      this.dialogTitle = '零件齐套'\r\n      this.currentComponent = 'Qitao'\r\n      this.tid = row.Task_Id\r\n      this.compCode = row.Comp_Code\r\n      this.$nextTick((_) => {\r\n        this.dialogVisible = true\r\n      })\r\n    },\r\n    // 查看零件\r\n    viewPart(item) {\r\n      this.dialogTitle = '零件明细表'\r\n      this.currentComponent = 'PartDetail'\r\n      this.dialogVisible = true\r\n      const obj = {\r\n        Task_Code: item.Task_Code,\r\n        Working_Team_Id: this.form.Working_Team_Id,\r\n        Sys_Project_Id: item.Sys_Project_Id,\r\n        Need_Use_Part: item.Need_Use_Part\r\n      }\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(obj)\r\n      })\r\n    },\r\n    checCheckboxkMethod3({ row }) {\r\n      if (this.isCom) {\r\n        if (typeof row.Prepare_Count === 'number' && this.Is_Part_Prepare) {\r\n          return row.Ready_Process_Count > 0 && row.Prepare_Count > 0\r\n        } else {\r\n          return row.Ready_Process_Count > 0\r\n        }\r\n      } else {\r\n        return row.Ready_Process_Count > 0\r\n      }\r\n    },\r\n    multiSelectedChange(array) {\r\n      this.multipleSelection = array.records\r\n    },\r\n\r\n    // 任务单左右移动方法\r\n    clickMove(direction) {\r\n      const middleWapperWidth = this.$refs.middleWapper?.offsetWidth\r\n      const boxWapperWidth = this.$refs.boxWapper?.scrollWidth\r\n      console.log(middleWapperWidth, boxWapperWidth)\r\n\r\n      if (middleWapperWidth < boxWapperWidth) {\r\n        if (direction === 'left') {\r\n          this.offset = Math.min(this.offset + this.itemWidth, 0)\r\n        } else if (direction === 'right') {\r\n          const maxOffset = middleWapperWidth - boxWapperWidth\r\n          console.log(maxOffset, this.offset - this.itemWidth)\r\n          this.offset = Math.max(this.offset - this.itemWidth, maxOffset)\r\n        }\r\n      }\r\n      // 更新按钮的禁用状态\r\n      this.disableLeftBtn = this.offset === 0\r\n      this.disableRightBtn = this.offset === middleWapperWidth - boxWapperWidth\r\n      console.log(this.offset, this.disableLeftBtn, this.disableRightBtn)\r\n    },\r\n    checkResize() {\r\n      const middleWapperWidth = this.$refs.middleWapper.offsetWidth\r\n      const boxWapperWidth = this.$refs.boxWapper.scrollWidth\r\n      // console.log(middleWapperWidth, boxWapperWidth)\r\n\r\n      if (middleWapperWidth >= boxWapperWidth) {\r\n        this.offset = 0\r\n        this.IsShowBtn = false\r\n      } else {\r\n        const maxOffset = middleWapperWidth - boxWapperWidth\r\n        this.offset = Math.max(this.offset, maxOffset)\r\n        this.IsShowBtn = true\r\n      }\r\n      // 更新按钮的禁用状态\r\n      this.disableLeftBtn = this.offset === 0\r\n      this.disableRightBtn = this.offset === middleWapperWidth - boxWapperWidth\r\n      console.log(this.offset, this.disableLeftBtn, this.disableRightBtn)\r\n    },\r\n    showNestChange(val) {\r\n      this.refresh()\r\n    },\r\n    handleReport() {\r\n      if (this.isCom) {\r\n        this.currentComponent = 'ComReport'\r\n        this.dialogTitle = '报工'\r\n        this.$nextTick((_) => {\r\n          this.dialogVisible = true\r\n          this.$nextTick((_) => {\r\n            this.$refs['content'].init(\r\n              JSON.parse(JSON.stringify(this.multipleSelection))\r\n            )\r\n          })\r\n        })\r\n      } else {\r\n        this.dialogTitle = '零件报工'\r\n        this.currentComponent = 'PartReport'\r\n        this.dialogVisible = true\r\n        this.$nextTick((_) => {\r\n          this.$refs['content'].init(\r\n            JSON.parse(JSON.stringify(this.multipleSelection))\r\n          )\r\n        })\r\n      }\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    checkShowP(num) {\r\n      if (typeof num !== 'number') {\r\n        return false\r\n      }\r\n      return true\r\n    },\r\n    getPercentDetail() {\r\n      GetCompTaskPartCompletionStock({\r\n        Working_Team_Id: this.form.Working_Team_Id,\r\n        Task_Code: this.taskList.map((v) => v.Task_Code)\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const list = res.Data\r\n          const taskMap = {}\r\n          this.taskList.forEach((item) => {\r\n            taskMap[item.Task_Code] = item\r\n          })\r\n          list.forEach((item) => {\r\n            const cur = taskMap[item.Task_Code]\r\n            if (cur.Need_Part_Amount === -1) {\r\n              cur.percentage = '100%'\r\n            } else {\r\n              const p = numeral(item.Part_Stock_Count).divide(\r\n                cur.Need_Part_Amount\r\n              )\r\n              let result = '100%'\r\n              if (p.value() <= 1) {\r\n                result = p.format('0.[00]%')\r\n              }\r\n              cur.percentage = result\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"~@/styles/mixin.scss\";\r\n// @import \"~@/styles/tabs.scss\";\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n.app-wrapper {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  overflow: hidden;\r\n\r\n  .cs-left {\r\n    display: flex;\r\n    flex-direction: column;\r\n    margin-right: 20px;\r\n\r\n    .inner-wrapper {\r\n      flex: 1;\r\n      display: flex;\r\n      flex-direction: column;\r\n      padding: 16px 16px;\r\n      border-radius: 4px;\r\n      overflow: hidden;\r\n      .cs-search {\r\n        border-bottom: 1px solid #e2e4e9;\r\n        padding-bottom: 17px;\r\n        .team-select {\r\n          margin-bottom: 12px;\r\n          .el-select {\r\n            width: 100%;\r\n          }\r\n\r\n          ::v-deep {\r\n            .el-input__inner {\r\n              border: 1px solid #298dff;\r\n              color: #298dff;\r\n              font-weight: bold;\r\n            }\r\n            .el-input__prefix {\r\n              display: flex;\r\n              justify-content: center;\r\n              align-items: center;\r\n\r\n              img {\r\n                width: 16px;\r\n                height: 16px;\r\n              }\r\n            }\r\n\r\n            .el-select__caret {\r\n              color: #298dff;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      .cs-tree {\r\n        margin-top: 22px;\r\n        flex: 1;\r\n        height: 0;\r\n\r\n        .cs-scroll {\r\n          overflow-y: auto;\r\n          @include scrollBar;\r\n        }\r\n\r\n        .el-tree {\r\n          height: 100%;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .cs-right {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n    padding: 16px 16px;\r\n\r\n    .cs-top-wapper {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      // align-items: center;\r\n      .btn {\r\n        width: 20px;\r\n        height: 20px;\r\n        border-radius: 10px;\r\n        background: #cfcfcf;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        color: #fff;\r\n        font-weight: bold;\r\n        font-size: 12px;\r\n        cursor: pointer;\r\n        margin-top: calc((96px / 2) - 10px);\r\n      }\r\n      .btn.disabled {\r\n        opacity: 0.5;\r\n        // pointer-events: none;\r\n        cursor: not-allowed !important;\r\n      }\r\n\r\n      .middle-wapper::-webkit-scrollbar {\r\n        height: 0; /* 将滚动条的宽度设为0 */\r\n        background: transparent; /* 使滚动条完全透明 */\r\n      }\r\n\r\n      .middle-wapper {\r\n        flex: 1;\r\n        width: 0;\r\n        overflow-x: auto;\r\n        position: relative;\r\n        height: calc(96px + 10px);\r\n\r\n        .box-wapper {\r\n          position: absolute;\r\n          display: flex;\r\n          flex-wrap: no-wrap;\r\n          height: 100%;\r\n          transition: transform 0.3s ease;\r\n\r\n          .item {\r\n            width: 275px;\r\n            height: calc(100% - 10px);\r\n            border-radius: 4px 4px 4px 4px;\r\n            border: 1px solid #e2e4e9;\r\n            margin-left: 12px;\r\n            position: relative;\r\n            cursor: pointer;\r\n            .content {\r\n              width: 100%;\r\n              height: 100%;\r\n              display: flex;\r\n              padding: 12px 12px;\r\n              flex-direction: column;\r\n              // align-items: center;\r\n              // justify-content: space-between;\r\n              // background: linear-gradient( 91deg, #298DFF 0%, #57C2FF 100%);\r\n              .detail {\r\n                flex: 1;\r\n                height: 0;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: space-between;\r\n              }\r\n              .title {\r\n                margin-bottom: 8px;\r\n                display: flex;\r\n                align-items: center;\r\n              }\r\n              .name {\r\n                font-weight: bold;\r\n                font-size: 16px;\r\n              }\r\n              .precent {\r\n                height: 17px;\r\n                padding: 0 4px;\r\n                background: #00cfaa;\r\n                border-radius: 9px 9px 9px 9px;\r\n                color: #ffffff;\r\n                font-size: 12px;\r\n                margin-left: 8px;\r\n                line-height: 17px;\r\n                text-align: center;\r\n              }\r\n              .info {\r\n                font-size: 14px;\r\n                color: #999999;\r\n                margin-bottom: 2px;\r\n              }\r\n\r\n              .right {\r\n                // width: 80px;\r\n                padding: 5px 8px;\r\n                border-radius: 4px 4px 4px 4px;\r\n                text-align: center;\r\n\r\n                font-size: 14px;\r\n                cursor: pointer;\r\n                background: #e9f3ff;\r\n                color: #298dff;\r\n              }\r\n            }\r\n\r\n            .flag {\r\n              position: absolute;\r\n              top: 0;\r\n              right: 0;\r\n              width: 35px;\r\n              height: 35px;\r\n              background: url(\"~@/assets/PRO/flag.png\") no-repeat;\r\n              font-size: 12px;\r\n              color: #ffffff;\r\n              display: flex;\r\n              justify-content: flex-end;\r\n              align-items: flex-start;\r\n              padding-right: 4px;\r\n              padding-top: 6px;\r\n            }\r\n            i {\r\n              position: absolute;\r\n              bottom: -10px;\r\n              left: calc((100% - 16px) / 2);\r\n              color: #298dff;\r\n            }\r\n            .flag2 {\r\n              position: absolute;\r\n              bottom: 0;\r\n              right: 0;\r\n              width: 0;\r\n              height: 0;\r\n              border-left: 35px solid transparent;\r\n              border-top: 35px solid transparent;\r\n              border-bottom: 35px solid #e6a23c;\r\n              font-size: 12px;\r\n              color: #ffffff;\r\n              .flag2-txt {\r\n                position: absolute;\r\n                right: 3px;\r\n                bottom: -33px;\r\n              }\r\n            }\r\n          }\r\n          .active {\r\n            .content {\r\n              color: #ffffff;\r\n              background: linear-gradient(91deg, #298dff 0%, #57c2ff 100%);\r\n              .info {\r\n                color: #ffffff;\r\n              }\r\n            }\r\n            .right {\r\n              background: #ffffff;\r\n            }\r\n          }\r\n          .item:last-child {\r\n            margin-right: 12px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .cs-middle-wapper {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      margin: 12px 0;\r\n      align-items: center;\r\n      .el-form-item {\r\n        margin-bottom: 0;\r\n      }\r\n    }\r\n\r\n    .cs-bottom-wapper {\r\n      flex: 1;\r\n      height: 0;\r\n      display: flex;\r\n      flex-direction: column;\r\n      .tb-x {\r\n        flex: 1;\r\n        height: 0;\r\n      }\r\n\r\n      .pagination-container {\r\n        text-align: right;\r\n        padding: 16px;\r\n        margin: 0;\r\n      }\r\n\r\n      .data-info {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.plm-custom-dialog {\r\n  ::v-deep {\r\n    .el-dialog__body {\r\n      height: 70vh;\r\n      overflow: auto;\r\n      display: flex;\r\n      flex-direction: column;\r\n    }\r\n  }\r\n}\r\n\r\n.fourGreen {\r\n  color: #00c361;\r\n  font-style: normal;\r\n}\r\n\r\n.fourOrange {\r\n  color: #ff9400;\r\n  font-style: normal;\r\n}\r\n\r\n.fourRed {\r\n  color: #ff0000;\r\n  font-style: normal;\r\n}\r\n\r\n.cs-blue {\r\n  color: #5ac8fa;\r\n}\r\n.cs-checkbox {\r\n  margin-left: 16px;\r\n  line-height: 30px;\r\n}\r\n.cs-middle-wapper-left{\r\n  display: flex;\r\n  flex-wrap: nowrap;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4XA,OAAAA,iBAAA;AACA,OAAAC,UAAA;AACA,OAAAC,UAAA;AACA,SAAAC,aAAA;AACA,OAAAC,SAAA;AACA,OAAAC,SAAA;AACA,OAAAC,UAAA;AACA,OAAAC,UAAA;AACA,OAAAC,KAAA;AACA,SAAAC,sBAAA;AACA,SAAAC,iBAAA;AACA,SACAC,mBAAA,EACAC,8BAAA,EACAC,MAAA,QACA;AACA,SACAC,eAAA,EACAC,yBAAA,EACAC,6BAAA,QACA;AACA,SAAAC,WAAA;AACA,SAAAC,WAAA;AACA,SAAAC,UAAA;AACA,OAAAC,MAAA;AACA,SAAAC,UAAA;AACA,SAAAC,yBAAA;AACA,OAAAC,aAAA;AACA,OAAAC,OAAA;AACA,SAAAC,UAAA;AAEA,IAAAC,YAAA;AACA;EACAC,UAAA;IACA3B,iBAAA,EAAAA,iBAAA;IACAC,UAAA,EAAAA,UAAA;IACAI,SAAA,EAAAA,SAAA;IACAE,UAAA,EAAAA,UAAA;IACAC,KAAA,EAAAA,KAAA;IACAN,UAAA,EAAAA,UAAA;IACAqB,aAAA,EAAAA,aAAA;IACAjB,UAAA,EAAAA;EACA;EACAsB,MAAA,GAAAxB,SAAA,EAAAgB,MAAA;EACAS,MAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,GAAA;MACAC,OAAA;MACAC,QAAA;MACAC,QAAA;MACAC,WAAA;MACAC,UAAA;MACAC,WAAA;MACAC,WAAA;MACAC,QAAA;MACAC,UAAA;MACAC,WAAA;MACAC,KAAA;MACAC,MAAA;MACAC,SAAA;MAAA;MACAC,QAAA;MACAC,IAAA;QACAC,mBAAA;QACAC,eAAA;QACAC,SAAA;QACAC,SAAA;QACAC,cAAA;QACAC,OAAA;QACAC,cAAA;QACAC,iBAAA;QACAC,SAAA;MACA;MACAC,SAAA;MACAC,YAAA;MACAC,OAAA;MACAC,MAAA;MACAC,QAAA;MACAC,SAAA;QACAC,IAAA;QACAC,QAAA,EAAA5D,aAAA;MACA;MACA6D,KAAA;MACA7D,aAAA,EAAAA,aAAA;MACA8D,aAAA;MACAC,gBAAA;MACAC,WAAA;MACAC,KAAA;MACAC,SAAA;MACAC,iBAAA;MACAC,SAAA;MACAC,MAAA;MACAC,cAAA;MACAC,eAAA;IACA;EACA;EACAC,QAAA,EAAAC,aAAA;IACAC,KAAA,WAAAA,MAAA;MACA,YAAAC,QAAA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,YAAAD,QAAA,aACA,wBACA;IACA;IACAE,UAAA,WAAAA,WAAA;MACA,YAAA7C,WAAA,GAAAT,YAAA,QAAAU,UAAA;IACA;IACA6C,SAAA,WAAAA,UAAA;MACA,OAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;IACA;EAAA,GACAjE,UAAA,iBACA,iCACA,iCACA,kBACA,EACA;EAEAkE,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,IAAAC,iBAAA,EAAAC,IAAA,EAAA1D,QAAA,EAAAF,OAAA;MAAA,OAAAwD,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACAxE,UAAA;UAAA;YAAAkE,iBAAA,GAAAI,QAAA,CAAAG,IAAA;YAAAN,IAAA,GAAAD,iBAAA,CAAAC,IAAA;YAAA1D,QAAA,GAAAyD,iBAAA,CAAAzD,QAAA;YAAAF,OAAA,GAAA2D,iBAAA,CAAA3D,OAAA;YACAsD,KAAA,CAAAa,OAAA,GAAAP,IAAA;YACAN,KAAA,CAAApD,QAAA,GAAAA,QAAA;YACAoD,KAAA,CAAAtD,OAAA,GAAAA,OAAA;YACAsD,KAAA,CAAAf,SAAA;YACA6B,MAAA,CAAAC,gBAAA,WAAAf,KAAA,CAAAgB,WAAA;YACAhB,KAAA,CAAAgB,WAAA;YACAhB,KAAA,CAAAiB,cAAA,CACAjB,KAAA,CAAAT,KAAA,6DACA;YACAS,KAAA,CAAAkB,cAAA;YAAAT,QAAA,CAAAE,IAAA;YAAA,OACAX,KAAA,CAAAmB,cAAA;UAAA;YAAAV,QAAA,CAAAE,IAAA;YAAA,OACAX,KAAA,CAAAoB,aAAA;UAAA;UAAA;YAAA,OAAAX,QAAA,CAAAY,IAAA;QAAA;MAAA,GAAAjB,OAAA;IAAA;EAEA;EACAkB,aAAA,WAAAA,cAAA;IACAR,MAAA,CAAAS,mBAAA,gBAAAP,WAAA;EACA;EACAQ,OAAA;IACAC,OAAA,WAAAA,QAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,SAAA,WAAAC,CAAA;QACAF,MAAA,CAAAG,KAAA,YAAAC,UAAA;UACAC,IAAA,EAAAN,GAAA,CAAA9D,SAAA;UACAqE,IAAA,EAAAN,MAAA,CAAApC,KAAA;UACA2C,OAAA,EAAAR,GAAA,CAAAQ,OAAA;UACAC,WAAA,EAAAT,GAAA,CAAAU,oBAAA;UACAC,QAAA,EAAAX,GAAA,CAAAY,iBAAA;UACAC,cAAA,EAAAb,GAAA,aAAAA,GAAA,uBAAAA,GAAA,CAAA9D,SAAA,CAAA4E,KAAA;UACAC,MAAA,EAAAd,MAAA,CAAAnE,IAAA,CAAAE;QACA;MACA;IACA;IACAwD,cAAA,WAAAA,eAAA;MAAA,IAAAwB,MAAA;MAAA,OAAAzC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAwC,SAAA;QAAA,OAAAzC,mBAAA,GAAAK,IAAA,UAAAqC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnC,IAAA,GAAAmC,SAAA,CAAAlC,IAAA;YAAA;cAAAkC,SAAA,CAAAlC,IAAA;cAAA,OACA+B,MAAA,CAAAI,MAAA,CAAAC,QAAA;YAAA;cACAC,OAAA,CAAAC,GAAA,CAAAP,MAAA,CAAAQ,eAAA;YAAA;YAAA;cAAA,OAAAL,SAAA,CAAAxB,IAAA;UAAA;QAAA,GAAAsB,QAAA;MAAA;IACA;IACAvB,aAAA,WAAAA,cAAA;MAAA,IAAA+B,MAAA;MAAA,OAAAlD,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAiD,SAAA;QAAA,OAAAlD,mBAAA,GAAAK,IAAA,UAAA8C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5C,IAAA,GAAA4C,SAAA,CAAA3C,IAAA;YAAA;cACAqC,OAAA,CAAAC,GAAA;cACAE,MAAA,CAAAnG,WAAA;cAAAsG,SAAA,CAAA3C,IAAA;cAAA,OACAxF,sBAAA;gBACAoI,MAAA,EAAAJ,MAAA,CAAAK,MAAA,CAAAC,IAAA,CAAAC,EAAA;gBACA7G,WAAA,EAAAsG,MAAA,CAAAtG,WAAA;gBACA8G,IAAA,EAAAR,MAAA,CAAA5D,KAAA;cACA,GAAAqE,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,IAAA,CAAAC,MAAA;kBACAZ,MAAA,CAAAnG,WAAA;kBACA;gBACA;gBACA,IAAAgH,OAAA,GAAAH,GAAA,CAAAC,IAAA,CAAAG,GAAA,WAAAC,IAAA;kBACAA,IAAA,CAAAC,YAAA;kBAEA,OAAAD,IAAA;gBACA;gBACAf,MAAA,CAAAlG,QAAA,GAAA+G,OAAA;gBACA;gBACA;gBACA;gBACAb,MAAA,CAAAvB,SAAA,WAAAC,CAAA;kBACAsB,MAAA,CAAArB,KAAA,CAAAsC,IAAA,CAAAC,SAAA,CAAAlB,MAAA,CAAAzD,UAAA;kBACA,IAAA4E,MAAA,GAAAnB,MAAA,CAAAoB,MAAA;kBACA,KAAAD,MAAA;oBACAnB,MAAA,CAAAlE,SAAA;kBACA;gBACA;gBACAkE,MAAA,CAAAnG,WAAA;cACA;YAAA;YAAA;cAAA,OAAAsG,SAAA,CAAAjC,IAAA;UAAA;QAAA,GAAA+B,QAAA;MAAA;IACA;IACAmB,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,WAAA,YAAAA,WAAAL,IAAA;QACA,SAAAM,CAAA,MAAAA,CAAA,GAAAN,IAAA,CAAAL,MAAA,EAAAW,CAAA;UACA,IAAAR,IAAA,GAAAE,IAAA,CAAAM,CAAA;UACA,IAAAZ,IAAA,GAAAI,IAAA,CAAAJ,IAAA;YAAAa,QAAA,GAAAT,IAAA,CAAAS,QAAA;UACA,IAAAC,IAAA,GAAAC,OAAA,CAAAf,IAAA,CAAAJ,EAAA;UACA,IAAAI,IAAA,CAAAgB,QAAA,MAAAH,QAAA,aAAAA,QAAA,eAAAA,QAAA,CAAAZ,MAAA,KAAAa,IAAA,CAAAG,OAAA;YACAP,MAAA,CAAAQ,eAAA,CAAAd,IAAA;YACA;UACA;YACA,IAAAS,QAAA,aAAAA,QAAA,eAAAA,QAAA,CAAAZ,MAAA;cACA,IAAAkB,UAAA,GAAAR,WAAA,CAAAE,QAAA;cACA,IAAAM,UAAA;YACA;UACA;QACA;QACA;MACA;MACA,IAAAJ,OAAA,YAAAA,QAAAK,GAAA;QACA,OAAAV,MAAA,CAAA1C,KAAA,SAAAqD,YAAA,CAAAD,GAAA;MACA;MACA,OAAAT,WAAA,MAAAxH,QAAA;IACA;IACA+H,eAAA,WAAAA,gBAAAxI,IAAA;MACA,KAAAO,WAAA,GAAAP,IAAA,CAAAkH,EAAA;MACA,KAAA0B,eAAA,GAAA5I,IAAA;MACA,KAAA6I,MAAA,GAAA7I,IAAA,CAAAkH,EAAA;MACA,KAAAlG,IAAA,CAAAM,OAAA,GAAAtB,IAAA,CAAAkH,EAAA;MACA,KAAAlG,IAAA,CAAAK,cAAA,GAAArB,IAAA,CAAAsH,IAAA,CAAAjG,cAAA;MACA,KAAAmB,iBAAA;MACA,KAAAsG,WAAA;IACA;IAEAC,cAAA,WAAAA,eAAA;MACA,KAAAzD,KAAA,CAAAsC,IAAA,CAAAC,SAAA,MAAA3E,UAAA;IACA;IAEA8F,eAAA,WAAAA,gBAAApI,KAAA,EAAAZ,IAAA,EAAAoI,IAAA;MACA,IAAAa,GAAA,GAAArI,KAAA,CAAAoF,KAAA,CAAApG,YAAA;MACA,IAAAsJ,QAAA,GAAAD,GAAA;MACA,IAAAE,SAAA,GAAAF,GAAA;MACA,KAAArI,KAAA;MACA,IAAAwI,UAAA,GAAAhB,IAAA,CAAAiB,MAAA;MACA,IAAAC,MAAA,IAAAlB,IAAA,CAAAmB,KAAA;MACA,IAAAC,MAAA,IAAAxJ,IAAA,CAAAsH,IAAA,MAAArE,SAAA;MACA,IAAAwG,KAAA;MACA,OAAAA,KAAA,GAAArB,IAAA,CAAAqB,KAAA;QACAH,MAAA,MAAAI,MAAA,CAAAC,kBAAA,CAAAL,MAAA,IAAAF,UAAA,CAAAG,KAAA;QACAC,MAAA,MAAAE,MAAA,CAAAC,kBAAA,CAAAH,MAAA,IAAAxJ,IAAA,CAAAsH,IAAA,MAAArE,SAAA;QACAmG,UAAA,GAAAA,UAAA,CAAAC,MAAA;QACAI,KAAA;MACA;MACAH,MAAA,GAAAA,MAAA,CAAAM,MAAA,WAAAC,CAAA;QAAA,SAAAA,CAAA;MAAA;MACAL,MAAA,GAAAA,MAAA,CAAAI,MAAA,WAAAC,CAAA;QAAA,SAAAA,CAAA;MAAA;MACA,IAAAC,WAAA;MACA,IAAAC,YAAA;MACA,SAAAzJ,UAAA;QACAyJ,YAAA,GAAAP,MAAA,CAAAQ,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,OAAA,CAAAf,SAAA;QAAA;MACA;MACA,SAAA9I,WAAA;QACAyJ,WAAA,GAAAR,MAAA,CAAAU,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,OAAA,CAAAhB,QAAA;QAAA;MACA;MACA,OAAAY,WAAA,IAAAC,YAAA;IACA;IAEA;IACApF,cAAA,WAAAA,eAAA;MAAA,IAAAwF,MAAA;MAAA,OAAA1G,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAyG,SAAA;QAAA,IAAAC,mBAAA;QAAA,OAAA3G,mBAAA,GAAAK,IAAA,UAAAuG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArG,IAAA,GAAAqG,SAAA,CAAApG,IAAA;YAAA;cAAAoG,SAAA,CAAApG,IAAA;cAAA,OACAvF,iBAAA;gBACAuI,IAAA,EAAAgD,MAAA,CAAApH,KAAA;cACA,GAAAqE,IAAA,WAAAC,GAAA;gBACA8C,MAAA,CAAAxJ,WAAA,GAAA0G,GAAA,CAAAC,IAAA,CAAAG,GAAA,WAAAC,IAAA;kBACA;oBACA9G,KAAA,EAAA8G,IAAA,CAAAR,EAAA;oBACAqC,KAAA,EAAA7B,IAAA,CAAA8C;kBACA;gBACA;cACA;YAAA;cACAL,MAAA,CAAAnJ,IAAA,CAAAE,eAAA,KAAAmJ,mBAAA,GAAAF,MAAA,CAAAxJ,WAAA,iBAAA0J,mBAAA,uBAAAA,mBAAA,CAAAzJ,KAAA;YAAA;YAAA;cAAA,OAAA2J,SAAA,CAAA1F,IAAA;UAAA;QAAA,GAAAuF,QAAA;MAAA;IACA;IACAK,gBAAA,WAAAA,iBAAA7J,KAAA;MACA,KAAAI,IAAA,CAAAE,eAAA,GAAAN,KAAA;MACA,KAAAkI,WAAA;IACA;IACA;IACAA,WAAA,WAAAA,YAAA;MAAA,IAAA4B,MAAA;MAAA,OAAAjH,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAgH,SAAA;QAAA,IAAAC,SAAA,EAAAvD,GAAA,EAAAwD,gBAAA,EAAAC,GAAA;QAAA,OAAApH,mBAAA,GAAAK,IAAA,UAAAgH,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9G,IAAA,GAAA8G,SAAA,CAAA7G,IAAA;YAAA;cACAuG,MAAA,CAAA1J,IAAA,CAAAI,SAAA;cACAsJ,MAAA,CAAAjI,SAAA;cACAmI,SAAA,GAAAF,MAAA,CAAA3H,KAAA,GACA/D,eAAA,GACAC,yBAAA;cAAA+L,SAAA,CAAA9G,IAAA;cAAA8G,SAAA,CAAA7G,IAAA;cAAA,OAGAyG,SAAA,CAAAF,MAAA,CAAA1J,IAAA;YAAA;cAAAqG,GAAA,GAAA2D,SAAA,CAAA5G,IAAA;cACA,IAAAiD,GAAA,CAAA4D,SAAA;gBACAP,MAAA,CAAA3J,QAAA,IAAAsG,GAAA,CAAAC,IAAA,QAAAG,GAAA,WAAAoC,CAAA;kBACAA,CAAA,CAAAqB,WAAA,GAAA3L,UAAA,CAAAsK,CAAA,CAAAqB,WAAA;kBACArB,CAAA,CAAAsB,UAAA;kBACA,OAAAtB,CAAA;gBACA;gBACAa,MAAA,CAAAhI,MAAA,GAAAgI,MAAA,CAAA3J,QAAA,CAAAiJ,IAAA,WAAAH,CAAA;kBAAA,SAAAA,CAAA,CAAAuB,OAAA;gBAAA;gBACAV,MAAA,CAAA1J,IAAA,CAAAI,SAAA,KAAAyJ,gBAAA,GAAAH,MAAA,CAAA3J,QAAA,iBAAA8J,gBAAA,uBAAAA,gBAAA,CAAAzJ,SAAA;gBACA,IAAAsJ,MAAA,CAAA3J,QAAA,CAAAwG,MAAA;kBACAuD,GAAA,GAAAJ,MAAA,CAAA3J,QAAA;kBACA,IAAA2J,MAAA,CAAAW,UAAA,CAAAP,GAAA,CAAAQ,gBAAA;oBACAZ,MAAA,CAAAa,gBAAA;kBACA;gBACA;cACA;gBACAb,MAAA,CAAA3J,QAAA;gBACA2J,MAAA,CAAAc,QAAA,CAAAC,KAAA,CAAApE,GAAA,CAAAqE,OAAA;cACA;cAAA,MAEAhB,MAAA,CAAA3J,QAAA,CAAAwG,MAAA;gBAAAyD,SAAA,CAAA7G,IAAA;gBAAA;cAAA;cAAA6G,SAAA,CAAA7G,IAAA;cAAA,OACAuG,MAAA,CAAAiB,SAAA;YAAA;cAAAX,SAAA,CAAA7G,IAAA;cAAA;YAAA;cAEAuG,MAAA,CAAA7I,MAAA;YAAA;cAAAmJ,SAAA,CAAA7G,IAAA;cAAA;YAAA;cAAA6G,SAAA,CAAA9G,IAAA;cAAA8G,SAAA,CAAAY,EAAA,GAAAZ,SAAA;YAAA;cAAAA,SAAA,CAAA9G,IAAA;cAIAwG,MAAA,CAAAlG,WAAA;cACAkG,MAAA,CAAAjI,SAAA;cAAA,OAAAuI,SAAA,CAAAa,MAAA;YAAA;YAAA;cAAA,OAAAb,SAAA,CAAAnG,IAAA;UAAA;QAAA,GAAA8F,QAAA;MAAA;IAEA;IACAmB,UAAA,WAAAA,WAAApE,IAAA;MACA,KAAA1G,IAAA,CAAAI,SAAA,GAAAsG,IAAA,CAAAtG,SAAA;MACA,KAAA2K,kBAAA,CAAArE,IAAA;MACA,KAAAlF,iBAAA;MACA,KAAAxB,IAAA,CAAAG,SAAA;MACA,KAAAH,IAAA,CAAAQ,iBAAA;MACA,KAAAR,IAAA,CAAAS,SAAA;MACA,KAAAiB,MAAA,GAAAgF,IAAA,CAAA0D,OAAA;MACA,KAAAO,SAAA;IACA;IACAI,kBAAA,WAAAA,mBAAArE,IAAA;MAAA,IAAAsE,MAAA;MACA,KAAA5G,SAAA;QACA,IAAA6G,WAAA,GAAAD,MAAA,CAAA1G,KAAA,SAAAoE,MAAA,CAAAhC,IAAA,CAAAtG,SAAA;QACAoF,OAAA,CAAAC,GAAA,CAAAwF,WAAA;QACA,IAAAA,WAAA;UACAA,WAAA,CAAAC,cAAA;YAAAC,QAAA;YAAAC,MAAA;UAAA;QACA;MACA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA,KAAA7J,iBAAA;MACA,KAAAmJ,SAAA;IACA;IACAA,SAAA,WAAAA,UAAAW,IAAA;MAAA,IAAAC,MAAA;MAAA,OAAA9I,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6I,SAAA;QAAA,IAAAxL,IAAA,EAAAyL,GAAA,EAAA7B,SAAA;QAAA,OAAAlH,mBAAA,GAAAK,IAAA,UAAA2I,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzI,IAAA,GAAAyI,SAAA,CAAAxI,IAAA;YAAA;cACAmI,IAAA,KAAAC,MAAA,CAAAxK,SAAA,CAAAC,IAAA,GAAAsK,IAAA;cACAtL,IAAA,GAAA8B,aAAA,CAAAA,aAAA,KAAAyJ,MAAA,CAAAvL,IAAA,GAAAuL,MAAA,CAAAxK,SAAA;cACA,IAAAf,IAAA,CAAAE,eAAA;gBACAuL,GAAA,GAAAF,MAAA,CAAA5L,WAAA,CACA8G,GAAA,WAAAoC,CAAA;kBAAA,OAAAA,CAAA,CAAAjJ,KAAA;gBAAA,GACAgJ,MAAA,WAAAK,CAAA;kBAAA,OAAAA,CAAA;gBAAA,GACA2C,QAAA;gBACA5L,IAAA,CAAAE,eAAA,GAAAuL,GAAA;cACA;cACA,KAAAF,MAAA,CAAAxJ,KAAA;gBACA/B,IAAA,CAAA6L,cAAA,GAAAN,MAAA,CAAA5K,YAAA;cACA;cACA4K,MAAA,CAAA7K,SAAA;cACAkJ,SAAA,GAAA2B,MAAA,CAAAxJ,KAAA,GACAlE,mBAAA,GACAK,6BAAA;cAAAyN,SAAA,CAAAxI,IAAA;cAAA,OACAyG,SAAA,CAAA5J,IAAA,EACAoG,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAA4D,SAAA;kBAAA,IAAA6B,SAAA;kBACAP,MAAA,CAAA1K,MAAA,KAAAwF,GAAA,aAAAA,GAAA,gBAAAyF,SAAA,GAAAzF,GAAA,CAAAC,IAAA,cAAAwF,SAAA,uBAAAA,SAAA,CAAAxF,IAAA,SAAAG,GAAA,WAAAoC,CAAA;oBACAA,CAAA,CAAAkD,OAAA;oBACA,OAAAlD,CAAA;kBACA;kBACA0C,MAAA,CAAArK,KAAA,GAAAmF,GAAA,CAAAC,IAAA,CAAA0F,UAAA;kBACAT,MAAA,CAAAU,WAAA,CAAAV,MAAA,CAAA1K,MAAA;gBACA;kBACA0K,MAAA,CAAAf,QAAA;oBACA0B,OAAA,EAAA7F,GAAA,CAAAqE,OAAA;oBACAvE,IAAA;kBACA;gBACA;gBACAoF,MAAA,CAAA7K,SAAA;cACA,GACAyL,OAAA,WAAAC,CAAA;gBACAb,MAAA,CAAA7K,SAAA;cACA;YAAA;YAAA;cAAA,OAAAiL,SAAA,CAAA9H,IAAA;UAAA;QAAA,GAAA2H,QAAA;MAAA;IACA;IACAS,WAAA,WAAAA,YAAAnJ,IAAA;MAAA,IAAAuJ,MAAA;MAAA,OAAA5J,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA2J,SAAA;QAAA,IAAA5E,GAAA,EAAA6E,SAAA;QAAA,OAAA7J,mBAAA,GAAAK,IAAA,UAAAyJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvJ,IAAA,GAAAuJ,SAAA,CAAAtJ,IAAA;YAAA;cACAuE,GAAA;cACA6E,SAAA,GAAAzJ,IAAA,CAAA2D,GAAA,WAAAC,IAAA;gBACA;kBACAR,EAAA,EAAAQ,IAAA,CAAAgB,GAAA;kBACAjD,IAAA,EAAA4H,MAAA,CAAAtK,KAAA;gBACA;cACA;cAAA0K,SAAA,CAAAtJ,IAAA;cAAA,OACAhF,WAAA,CAAAoO,SAAA,EAAAnG,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAA4D,SAAA;kBACA,IAAAyC,OAAA;kBACArG,GAAA,CAAAC,IAAA,CAAAqG,OAAA,WAAAjG,IAAA;oBACAgG,OAAA,CAAAhG,IAAA,CAAAR,EAAA,MAAAQ,IAAA,CAAAkG,OAAA;kBACA;kBACA9J,IAAA,CAAA6J,OAAA,WAAAzI,GAAA;oBACA,IAAAwI,OAAA,CAAAxI,GAAA,CAAAwD,GAAA;sBACA2E,MAAA,CAAAQ,IAAA,CAAA3I,GAAA,cAAAwI,OAAA,CAAAxI,GAAA,CAAAwD,GAAA;oBACA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA+E,SAAA,CAAA5I,IAAA;UAAA;QAAA,GAAAyI,QAAA;MAAA;IACA;IAEA;IACAQ,SAAA,WAAAA,UAAA5I,GAAA;MAAA,IAAA6I,MAAA;MACAhP,MAAA;QACA2G,OAAA,EAAAR,GAAA,CAAAQ;MACA,GAAA0B,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAA4D,SAAA;UAAA,IAAA+C,UAAA;UACA,IAAAC,OAAA,IAAA5G,GAAA,aAAAA,GAAA,gBAAA2G,UAAA,GAAA3G,GAAA,CAAAC,IAAA,cAAA0G,UAAA,uBAAAA,UAAA,CAAAzG,MAAA,KAAAF,GAAA,CAAAC,IAAA,IAAA4G,QAAA;UACA5J,MAAA,CAAA6J,IAAA,CACA,yCAAA/O,WAAA,CAAA6O,OAAA,GACA,QACA;QACA;UACAF,MAAA,CAAAvC,QAAA;YACA0B,OAAA,EAAA7F,GAAA,CAAAqE,OAAA;YACAvE,IAAA;UACA;QACA;MACA;IACA;IACA;IACAiH,WAAA,WAAAA,YAAAlJ,GAAA;MAAA,IAAAmJ,OAAA;MACA,KAAAhM,WAAA;MACA,KAAAD,gBAAA;MACA,KAAAnC,GAAA,GAAAiF,GAAA,CAAAQ,OAAA;MACA,KAAAvF,QAAA,GAAA+E,GAAA,CAAAoJ,SAAA;MACA,KAAAlJ,SAAA,WAAAC,CAAA;QACAgJ,OAAA,CAAAlM,aAAA;MACA;IACA;IACA;IACAoM,QAAA,WAAAA,SAAA7G,IAAA;MAAA,IAAA8G,OAAA;MACA,KAAAnM,WAAA;MACA,KAAAD,gBAAA;MACA,KAAAD,aAAA;MACA,IAAAsM,GAAA;QACArN,SAAA,EAAAsG,IAAA,CAAAtG,SAAA;QACAF,eAAA,OAAAF,IAAA,CAAAE,eAAA;QACAG,cAAA,EAAAqG,IAAA,CAAArG,cAAA;QACAqN,aAAA,EAAAhH,IAAA,CAAAgH;MACA;MACA,KAAAtJ,SAAA,WAAAC,CAAA;QACAmJ,OAAA,CAAAlJ,KAAA,YAAAqJ,IAAA,CAAAF,GAAA;MACA;IACA;IACAG,oBAAA,WAAAA,qBAAAC,IAAA;MAAA,IAAA3J,GAAA,GAAA2J,IAAA,CAAA3J,GAAA;MACA,SAAAnC,KAAA;QACA,WAAAmC,GAAA,CAAA4J,aAAA,sBAAApI,eAAA;UACA,OAAAxB,GAAA,CAAA6J,mBAAA,QAAA7J,GAAA,CAAA4J,aAAA;QACA;UACA,OAAA5J,GAAA,CAAA6J,mBAAA;QACA;MACA;QACA,OAAA7J,GAAA,CAAA6J,mBAAA;MACA;IACA;IACAC,mBAAA,WAAAA,oBAAAC,KAAA;MACA,KAAAzM,iBAAA,GAAAyM,KAAA,CAAAC,OAAA;IACA;IAEA;IACAC,SAAA,WAAAA,UAAAC,SAAA;MAAA,IAAAC,qBAAA,EAAAC,qBAAA;MACA,IAAAC,iBAAA,IAAAF,qBAAA,QAAA/J,KAAA,CAAAkK,YAAA,cAAAH,qBAAA,uBAAAA,qBAAA,CAAAI,WAAA;MACA,IAAAC,cAAA,IAAAJ,qBAAA,QAAAhK,KAAA,CAAAqK,SAAA,cAAAL,qBAAA,uBAAAA,qBAAA,CAAAM,WAAA;MACApJ,OAAA,CAAAC,GAAA,CAAA8I,iBAAA,EAAAG,cAAA;MAEA,IAAAH,iBAAA,GAAAG,cAAA;QACA,IAAAN,SAAA;UACA,KAAAvO,MAAA,GAAAgP,IAAA,CAAAC,GAAA,MAAAjP,MAAA,QAAAC,SAAA;QACA,WAAAsO,SAAA;UACA,IAAAW,SAAA,GAAAR,iBAAA,GAAAG,cAAA;UACAlJ,OAAA,CAAAC,GAAA,CAAAsJ,SAAA,OAAAlP,MAAA,QAAAC,SAAA;UACA,KAAAD,MAAA,GAAAgP,IAAA,CAAAG,GAAA,MAAAnP,MAAA,QAAAC,SAAA,EAAAiP,SAAA;QACA;MACA;MACA;MACA,KAAApN,cAAA,QAAA9B,MAAA;MACA,KAAA+B,eAAA,QAAA/B,MAAA,KAAA0O,iBAAA,GAAAG,cAAA;MACAlJ,OAAA,CAAAC,GAAA,MAAA5F,MAAA,OAAA8B,cAAA,OAAAC,eAAA;IACA;IACA4B,WAAA,WAAAA,YAAA;MACA,IAAA+K,iBAAA,QAAAjK,KAAA,CAAAkK,YAAA,CAAAC,WAAA;MACA,IAAAC,cAAA,QAAApK,KAAA,CAAAqK,SAAA,CAAAC,WAAA;MACA;;MAEA,IAAAL,iBAAA,IAAAG,cAAA;QACA,KAAA7O,MAAA;QACA,KAAA0B,SAAA;MACA;QACA,IAAAwN,SAAA,GAAAR,iBAAA,GAAAG,cAAA;QACA,KAAA7O,MAAA,GAAAgP,IAAA,CAAAG,GAAA,MAAAnP,MAAA,EAAAkP,SAAA;QACA,KAAAxN,SAAA;MACA;MACA;MACA,KAAAI,cAAA,QAAA9B,MAAA;MACA,KAAA+B,eAAA,QAAA/B,MAAA,KAAA0O,iBAAA,GAAAG,cAAA;MACAlJ,OAAA,CAAAC,GAAA,MAAA5F,MAAA,OAAA8B,cAAA,OAAAC,eAAA;IACA;IACAqN,cAAA,WAAAA,eAAAC,GAAA;MACA,KAAA7D,OAAA;IACA;IACA8D,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,SAAArN,KAAA;QACA,KAAAX,gBAAA;QACA,KAAAC,WAAA;QACA,KAAA+C,SAAA,WAAAC,CAAA;UACA+K,OAAA,CAAAjO,aAAA;UACAiO,OAAA,CAAAhL,SAAA,WAAAC,CAAA;YACA+K,OAAA,CAAA9K,KAAA,YAAAqJ,IAAA,CACA0B,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAH,OAAA,CAAA5N,iBAAA,EACA;UACA;QACA;MACA;QACA,KAAAH,WAAA;QACA,KAAAD,gBAAA;QACA,KAAAD,aAAA;QACA,KAAAiD,SAAA,WAAAC,CAAA;UACA+K,OAAA,CAAA9K,KAAA,YAAAqJ,IAAA,CACA0B,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAH,OAAA,CAAA5N,iBAAA,EACA;QACA;MACA;IACA;IACAgO,WAAA,WAAAA,YAAA;MACA,KAAArO,aAAA;IACA;IACAkJ,UAAA,WAAAA,WAAAoF,GAAA;MACA,WAAAA,GAAA;QACA;MACA;MACA;IACA;IACAlF,gBAAA,WAAAA,iBAAA;MAAA,IAAAmF,OAAA;MACA5R,8BAAA;QACAoC,eAAA,OAAAF,IAAA,CAAAE,eAAA;QACAE,SAAA,OAAAL,QAAA,CAAA0G,GAAA,WAAAoC,CAAA;UAAA,OAAAA,CAAA,CAAAzI,SAAA;QAAA;MACA,GAAAgG,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAA4D,SAAA;UACA,IAAAnH,IAAA,GAAAuD,GAAA,CAAAC,IAAA;UACA,IAAAqJ,OAAA;UACAD,OAAA,CAAA3P,QAAA,CAAA4M,OAAA,WAAAjG,IAAA;YACAiJ,OAAA,CAAAjJ,IAAA,CAAAtG,SAAA,IAAAsG,IAAA;UACA;UACA5D,IAAA,CAAA6J,OAAA,WAAAjG,IAAA;YACA,IAAAoD,GAAA,GAAA6F,OAAA,CAAAjJ,IAAA,CAAAtG,SAAA;YACA,IAAA0J,GAAA,CAAAQ,gBAAA;cACAR,GAAA,CAAAK,UAAA;YACA;cACA,IAAAyF,CAAA,GAAAlR,OAAA,CAAAgI,IAAA,CAAAmJ,gBAAA,EAAAC,MAAA,CACAhG,GAAA,CAAAQ,gBACA;cACA,IAAAxD,MAAA;cACA,IAAA8I,CAAA,CAAAhQ,KAAA;gBACAkH,MAAA,GAAA8I,CAAA,CAAAG,MAAA;cACA;cACAjG,GAAA,CAAAK,UAAA,GAAArD,MAAA;YACA;UACA;QACA;UACA4I,OAAA,CAAAlF,QAAA;YACA0B,OAAA,EAAA7F,GAAA,CAAAqE,OAAA;YACAvE,IAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
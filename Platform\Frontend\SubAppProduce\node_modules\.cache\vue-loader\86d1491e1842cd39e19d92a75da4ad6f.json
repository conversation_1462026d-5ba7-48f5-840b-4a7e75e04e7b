{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\component\\ProjectAddDialog.vue?vue&type=template&id=278490d2&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\component\\ProjectAddDialog.vue", "mtime": 1757926768439}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImFkZC1wcm9qZWN0LWNvbnRhaW5lciI+CiAgPGRpdiBjbGFzcz0iaW5zdHJ1Y3Rpb24iPgogICAg6K+36YCJ5oup6aG555uuLOa3u+WKoOaJgOmAiemhueebrueahOaJgOacieeUn+S6p+i3r+W+hAogIDwvZGl2PgogIDxkaXYgY2xhc3M9InNlYXJjaC1zZWN0aW9uIj4KICAgIDxlbC1mb3JtIDptb2RlbD0ic2VhcmNoRm9ybSIgaW5saW5lPgogICAgICA8ZWwtZm9ybS1pdGVtIHYtaWY9IiFwcm9qZWN0TGlzdC5sZW5ndGgiIGxhYmVsPSLpobnnm67lkI3np7DvvJoiPgogICAgICAgIDxkaXY+5pqC5peg5Y+v5ZCM5q2l55qE6aG555uuPC9kaXY+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICA8ZWwtZm9ybS1pdGVtIHYtZWxzZSBsYWJlbD0i6aG555uu5ZCN56ew77yaIj4KICAgICAgICA8ZWwtc2VsZWN0CiAgICAgICAgICB2LW1vZGVsPSJzZWFyY2hGb3JtLlByb2plY3RDb2RlIgogICAgICAgICAgY2xlYXJhYmxlCiAgICAgICAgICBmaWx0ZXJhYmxlCiAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36YCJ5oupIgogICAgICAgICAgc3R5bGU9IndpZHRoOiAyMDBweCIKICAgICAgICA+CiAgICAgICAgICA8ZWwtb3B0aW9uIHYtZm9yPSJpdGVtIGluIHByb2plY3RMaXN0IiA6a2V5PSJpdGVtLlN5c19Qcm9qZWN0X0lkIiA6bGFiZWw9Iml0ZW0uU2hvcnRfTmFtZSIgOnZhbHVlPSJpdGVtLlN5c19Qcm9qZWN0X0lkIiAvPgogICAgICAgIDwvZWwtc2VsZWN0PgogICAgICA8L2VsLWZvcm0taXRlbT4KCiAgICA8L2VsLWZvcm0+CiAgPC9kaXY+CgogIDwhLS0g5o+Q56S65L+h5oGvIC0tPgoKICA8IS0tIOW6lemDqOaMiemSriAtLT4KICA8ZGl2IGNsYXNzPSJmb290ZXItYWN0aW9ucyI+CiAgICA8ZWwtYnV0dG9uIEBjbGljaz0iaGFuZGxlQ2FuY2VsIj7lj5bmtog8L2VsLWJ1dHRvbj4KICAgIDxlbC1idXR0b24gdi1pZj0icHJvamVjdExpc3QubGVuZ3RoIiB0eXBlPSJwcmltYXJ5IiA6bG9hZGluZz0ibG9hZGluZyIgOmRpc2FibGVkPSJzZWFyY2hGb3JtLlByb2plY3RDb2RlID09PSAnJyIgQGNsaWNrPSJoYW5kbGVDb25maXJtIj4KICAgICAg56Gu5a6aCiAgICA8L2VsLWJ1dHRvbj4KICA8L2Rpdj4KPC9kaXY+Cg=="}, null]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\main.js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\main.js", "mtime": 1757135725433}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9yZWdlbmVyYXRvclJ1bnRpbWUgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9yZWdlbmVyYXRvclJ1bnRpbWUuanMiOwppbXBvcnQgX2FzeW5jVG9HZW5lcmF0b3IgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9hc3luY1RvR2VuZXJhdG9yLmpzIjsKaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0ICJEOlxccHJvamVjdFxccGxhdGZvcm1fZnJhbWV3b3JrX21hc3RlclxccGxhdGZvcm1fZnJhbWV3b3JrXFxQbGF0Zm9ybVxcRnJvbnRlbmRcXFN1YkFwcFByb2R1Y2VcXG5vZGVfbW9kdWxlc1xcY29yZS1qc1xcbW9kdWxlc1xcZXMuYXJyYXkuaXRlcmF0b3IuanMiOwppbXBvcnQgIkQ6XFxwcm9qZWN0XFxwbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyXFxwbGF0Zm9ybV9mcmFtZXdvcmtcXFBsYXRmb3JtXFxGcm9udGVuZFxcU3ViQXBwUHJvZHVjZVxcbm9kZV9tb2R1bGVzXFxjb3JlLWpzXFxtb2R1bGVzXFxlcy5wcm9taXNlLmpzIjsKaW1wb3J0ICJEOlxccHJvamVjdFxccGxhdGZvcm1fZnJhbWV3b3JrX21hc3RlclxccGxhdGZvcm1fZnJhbWV3b3JrXFxQbGF0Zm9ybVxcRnJvbnRlbmRcXFN1YkFwcFByb2R1Y2VcXG5vZGVfbW9kdWxlc1xcY29yZS1qc1xcbW9kdWxlc1xcZXMub2JqZWN0LmFzc2lnbi5qcyI7CmltcG9ydCAiRDpcXHByb2plY3RcXHBsYXRmb3JtX2ZyYW1ld29ya19tYXN0ZXJcXHBsYXRmb3JtX2ZyYW1ld29ya1xcUGxhdGZvcm1cXEZyb250ZW5kXFxTdWJBcHBQcm9kdWNlXFxub2RlX21vZHVsZXNcXGNvcmUtanNcXG1vZHVsZXNcXGVzLnByb21pc2UuZmluYWxseS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmVycm9yLmNhdXNlLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsdGVyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmluZC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmZ1bmN0aW9uLm5hbWUuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5jb25zdHJ1Y3Rvci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmZpbHRlci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmZpbmQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5mb3ItZWFjaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLnNvbWUuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5udW1iZXIuY29uc3RydWN0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5udW1iZXIudG8tZml4ZWQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3Qua2V5cy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcubWF0Y2guanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcucmVwZWF0LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaC5qcyI7CmltcG9ydCAnLi9wdWJsaWMtcGF0aCc7CmltcG9ydCBWdWUgZnJvbSAndnVlJzsKCi8vIGltcG9ydCB7IGhhbmRsZUFkZFJvdXRlclBhZ2UgfSBmcm9tICdAL3V0aWxzJwovLyBpbXBvcnQgY29tcG9uZW50TWFwIGZyb20gJ0AvdXRpbHMvY29tcG9uZW50TWFwJwppbXBvcnQgeyB0cmFuc2ZlclJvdXRlc1dpdGhQYXJlbnQsIGdldFJvdXRlc1dpdGhQYXJlbnQgfSBmcm9tICdAL3V0aWxzL3RlbXBSb3V0ZXJNYXAnOwppbXBvcnQgJ25vcm1hbGl6ZS5jc3Mvbm9ybWFsaXplLmNzcyc7IC8vIGEgbW9kZXJuIGFsdGVybmF0aXZlIHRvIENTUyByZXNldHMKCmltcG9ydCBFbGVtZW50IGZyb20gJ2VsZW1lbnQtdWknOwppbXBvcnQgJy4vc3R5bGVzL2VsZW1lbnQtdmFyaWFibGVzLnNjc3MnOwppbXBvcnQgJ2VsZW1lbnQtdWkvbGliL3RoZW1lLWNoYWxrL2ljb24uY3NzJzsKaW1wb3J0ICdAL3N0eWxlcy9pbmRleC5zY3NzJzsgLy8gZ2xvYmFsIGNzcwoKaW1wb3J0IEFwcCBmcm9tICcuL0FwcCc7CmltcG9ydCBzdG9yZSBmcm9tICcuL3N0b3JlJzsKaW1wb3J0IHJvdXRlciwgeyByZXNldFJvdXRlciwgY29uc3RhbnRSb3V0ZXMgfSBmcm9tICcuL3JvdXRlcic7CmltcG9ydCAnLi9pY29ucyc7IC8vIGljb24KaW1wb3J0ICcuL2ljb25zL2ljb25zL2NpcmNsZUZvbnQvaWNvbmZvbnQuY3NzJzsKaW1wb3J0ICcuL3Blcm1pc3Npb24nOyAvLyBwZXJtaXNzaW9uIGNvbnRyb2wKaW1wb3J0ICcuL3V0aWxzL2Vycm9yLWxvZyc7IC8vIGVycm9yIGxvZwoKaW1wb3J0ICogYXMgZmlsdGVycyBmcm9tICcuL2ZpbHRlcnMnOyAvLyBnbG9iYWwgZmlsdGVycwppbXBvcnQgRWxUcmVlU2VsZWN0IGZyb20gJ2VsLXRyZWUtc2VsZWN0JzsKaW1wb3J0IFZ1ZUNvbnRleHRNZW51IGZyb20gJ3Z1ZS1jb250ZXh0bWVudSc7CmltcG9ydCBCaW10a1VJIGZyb20gJ0Bjb21wb25lbnRzL0JpbXRrVUknOwppbXBvcnQgUHJpbnQgZnJvbSAnQC91dGlscy9wcmludC9wcmludC1sYWJlbCc7CmltcG9ydCAnQC91dGlscy9sYW5ndWFnZWNoYW5nZSc7CmltcG9ydCAnQC91dGlscy9tYXRoJzsKaW1wb3J0IFhFVXRpbHMgZnJvbSAneGUtdXRpbHMnOwppbXBvcnQgVlhFVGFibGUgZnJvbSAndnhlLXRhYmxlJzsKaW1wb3J0IFZYRVRhYmxlUGx1Z2luRXhwb3J0WExTWCBmcm9tICd2eGUtdGFibGUtcGx1Z2luLWV4cG9ydC14bHN4JzsKaW1wb3J0ICd2eGUtdGFibGUvbGliL3N0eWxlLmNzcyc7CmltcG9ydCAnQC91dGlscy9kaXJlY3RpdmVzLmpzJzsKCi8vIFZ4ZVVJLnNldENvbmZpZyh7Ci8vICAgZW1wdHlDZWxsOiAnODgnCi8vIH0pCmltcG9ydCBQb3J0YWxWdWUgZnJvbSAncG9ydGFsLXZ1ZSc7ClZ1ZS51c2UoQmltdGtVSSwgewogIGJhc2VVUkw6ICIiLmNvbmNhdChwbGF0Zm9ybVVybCgpLCAiUGxhdGZvcm0iKSwKICAvLyDorr7nva7ooajmoLznu4Tku7bnmoRBUEnln7rnoYBVUkwo5rOo5oSP77yM6L+Z6YeM5a6e6ZmF6KaB5YaZ5YiwYmFzZVVSTOeahOS4i+S4gOe6p++8jOacieS6m+ezu+e7n+mHjOmdoueUqOeahOaYr1NZU++8jOacieS6m+eUqOeahOaYr1BsYXRmb3JtKQogIHRhYmxlUGFnZVNpemVzOiB0YWJsZVBhZ2VTaXplLAogIC8vIOiuvue9ruihqOagvOe7hOS7tueahOWIhumhteWkp+Wwj+mAiemhue+8iOm7mOiupFsxMCwgMjAsIDUwLCAxMDBd77yJCiAgdGFibGVQYWdlU2l6ZTogdGFibGVQYWdlU2l6ZVswXSAvLyDorr7nva7ooajmoLznu4Tku7bnmoTpu5jorqTliIbpobXlpKflsI/vvIjpu5jorqQyMO+8iQp9KTsKVnVlLnVzZShQb3J0YWxWdWUpOwpWWEVUYWJsZS5zZXR1cCh7CiAgdmVyc2lvbjogMCwKICB6SW5kZXg6IDk5OTksCiAgZW1wdHlDZWxsOiAnLScsCiAgaWNvbjogewogICAgVEFCTEVfVFJFRV9PUEVOOiAnY3MtdnhlLXRyZWUtdXAnLAogICAgVEFCTEVfVFJFRV9DTE9TRTogJ2NzLXZ4ZS10cmVlLWRvd24nCiAgfSwKICB0YWJsZTogewogICAgYm9yZGVyOiB0cnVlLAogICAgc2hvd0hlYWRlck92ZXJmbG93OiB0cnVlLAogICAgYXV0b1Jlc2l6ZTogdHJ1ZSwKICAgIHN0cmlwZTogdHJ1ZSwKICAgIGtlZXBTb3VyY2U6IHRydWUsCiAgICBzY3JvbGxZOiB7CiAgICAgIGVuYWJsZWQ6IHRydWUsCiAgICAgIC8vIOaYr+WQpum7mOiupOW8gOWQr+e6teWQkeiZmuaLn+a7muWKqAogICAgICBndDogNTAgLy8g5b2T5pWw5o2u5aSn5LqO5oyH5a6a5pWw6YeP5pe26Ieq5Yqo6Kem5Y+R5ZCv55So6Jma5ouf5rua5YqoCiAgICB9CiAgfQp9KTsKVlhFVGFibGUucmVuZGVyZXIuYWRkKCdOb3REYXRhJywgewogIC8vIOepuuWGheWuueaooeadvwogIHJlbmRlckVtcHR5OiBmdW5jdGlvbiByZW5kZXJFbXB0eShoLCByZW5kZXJPcHRzKSB7CiAgICByZXR1cm4gaCgiZGl2IiwgewogICAgICAic3R5bGUiOiB7CiAgICAgICAgdGV4dEFsaWduOiAnY2VudGVyJywKICAgICAgICBoZWlnaHQ6ICcxMDAlJywKICAgICAgICBkaXNwbGF5OiAnZmxleCcsCiAgICAgICAgZmxleERpcmVjdGlvbjogJ2NvbHVtbicsCiAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInCiAgICAgIH0KICAgIH0sIFtoKCJkaXYiLCB7CiAgICAgICJzdHlsZSI6IHsKICAgICAgICB3aWR0aDogJzEwMCUnLAogICAgICAgIGhlaWdodDogJzcwJScsCiAgICAgICAgbWF4V2lkdGg6ICc0MDBweCcsCiAgICAgICAgbWF4SGVpZ2h0OiAnMjY2cHgnLAogICAgICAgIGJhY2tncm91bmRJbWFnZTogInVybChcImRhdGE6aW1hZ2Uvc3ZnK3htbCwlM0NzdmcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB4bWxuczp4bGluaz0naHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluaycgd2lkdGg9JzQwMCcgaGVpZ2h0PScyNjYnIHZpZXdCb3g9JzAgMCA0MDAgMjY2JyUzRSUzQ2RlZnMlM0UlM0NzdHlsZSUzRS5hJTdCZmlsbDpub25lJTdELmYlN0JmaWxsOnVybCglMjNlKSU3RC5nJTdCZmlsbDp1cmwoJTIzZiklN0QubCU3QmZpbGw6dXJsKCUyM20pJTdEJTNDL3N0eWxlJTNFJTNDY2xpcFBhdGggaWQ9J2EnJTNFJTNDcGF0aCBjbGFzcz0nYScgdHJhbnNmb3JtPSd0cmFuc2xhdGUoLS40MjQgLS4zMTMpJyBkPSdNMCAwaDQwMHYxMzNIMHonLyUzRSUzQy9jbGlwUGF0aCUzRSUzQ2xpbmVhckdyYWRpZW50IGlkPSdiJyB4MT0nLjUnIHkxPScxLjM1MScgeDI9Jy41JyB5Mj0nLS43NTUnIGdyYWRpZW50VW5pdHM9J29iamVjdEJvdW5kaW5nQm94JyUzRSUzQ3N0b3Agb2Zmc2V0PScuMzknIHN0b3AtY29sb3I9JyUyM2ZmZicgc3RvcC1vcGFjaXR5PScwJy8lM0UlM0NzdG9wIG9mZnNldD0nLjkxJyBzdG9wLWNvbG9yPSclMjM5YmNhZmYnLyUzRSUzQy9saW5lYXJHcmFkaWVudCUzRSUzQ2xpbmVhckdyYWRpZW50IGlkPSdjJyB4MT0nLjQ5OScgeTE9JzEuMDI4JyB4Mj0nLjUnIHkyPScuMDM5JyBncmFkaWVudFVuaXRzPSdvYmplY3RCb3VuZGluZ0JveCclM0UlM0NzdG9wIG9mZnNldD0nMCcgc3RvcC1jb2xvcj0nJTIzYmZkNGYzJy8lM0UlM0NzdG9wIG9mZnNldD0nMScgc3RvcC1jb2xvcj0nJTIzZTdlZGY4Jy8lM0UlM0MvbGluZWFyR3JhZGllbnQlM0UlM0NsaW5lYXJHcmFkaWVudCBpZD0nZCcgeDE9Jy41MDcnIHkxPScxLjM5NycgeDI9Jy40ODYnIHkyPSctLjEwNicgZ3JhZGllbnRVbml0cz0nb2JqZWN0Qm91bmRpbmdCb3gnJTNFJTNDc3RvcCBvZmZzZXQ9JzAnIHN0b3AtY29sb3I9JyUyM2U3ZWRmOCcvJTNFJTNDc3RvcCBvZmZzZXQ9JzEnIHN0b3AtY29sb3I9JyUyM2M5ZGFmNCcvJTNFJTNDL2xpbmVhckdyYWRpZW50JTNFJTNDbGluZWFyR3JhZGllbnQgaWQ9J2UnIHgxPScuNScgeTE9JzEuMDMzJyB4Mj0nLjUnIHkyPScuMDM1JyBncmFkaWVudFVuaXRzPSdvYmplY3RCb3VuZGluZ0JveCclM0UlM0NzdG9wIG9mZnNldD0nMCcgc3RvcC1jb2xvcj0nJTIzZTdlZGY4Jy8lM0UlM0NzdG9wIG9mZnNldD0nMScgc3RvcC1jb2xvcj0nJTIzZmVmZWZlJy8lM0UlM0MvbGluZWFyR3JhZGllbnQlM0UlM0NsaW5lYXJHcmFkaWVudCBpZD0nZicgeDE9Jy41JyB5MT0nMS4wMzknIHgyPScuNScgeTI9Jy4wMjknIGdyYWRpZW50VW5pdHM9J29iamVjdEJvdW5kaW5nQm94JyUzRSUzQ3N0b3Agb2Zmc2V0PScwJyBzdG9wLWNvbG9yPSclMjNjYmQ4ZWUnLyUzRSUzQ3N0b3Agb2Zmc2V0PScxJyBzdG9wLWNvbG9yPSclMjNjZmRjZWYnLyUzRSUzQy9saW5lYXJHcmFkaWVudCUzRSUzQ2xpbmVhckdyYWRpZW50IGlkPSdpJyB5MT0nMS4wMzInIHkyPScuMDM4JyB4bGluazpocmVmPSclMjNlJy8lM0UlM0NsaW5lYXJHcmFkaWVudCBpZD0naicgeTE9JzEuMDM2JyB5Mj0nLjAzNicgeGxpbms6aHJlZj0nJTIzZicvJTNFJTNDbGluZWFyR3JhZGllbnQgaWQ9J2snIHgxPScuNTA3JyB5MT0nMi41ODknIHgyPScuNDk2JyB5Mj0nLS42NjcnIGdyYWRpZW50VW5pdHM9J29iamVjdEJvdW5kaW5nQm94JyUzRSUzQ3N0b3Agb2Zmc2V0PScuNTInIHN0b3AtY29sb3I9JyUyM2M5ZGFmNCcvJTNFJTNDc3RvcCBvZmZzZXQ9Jy45Micgc3RvcC1jb2xvcj0nJTIzZmZmJy8lM0UlM0MvbGluZWFyR3JhZGllbnQlM0UlM0NsaW5lYXJHcmFkaWVudCBpZD0nbCcgeTE9Jy41JyB4Mj0nMScgeTI9Jy41JyBncmFkaWVudFVuaXRzPSdvYmplY3RCb3VuZGluZ0JveCclM0UlM0NzdG9wIG9mZnNldD0nMCcgc3RvcC1jb2xvcj0nJTIzZTBlOWY3Jy8lM0UlM0NzdG9wIG9mZnNldD0nMScgc3RvcC1jb2xvcj0nJTIzZTllZWY5Jy8lM0UlM0MvbGluZWFyR3JhZGllbnQlM0UlM0NsaW5lYXJHcmFkaWVudCBpZD0nbScgeTE9Jy41JyB4Mj0nMScgeTI9Jy41JyB4bGluazpocmVmPSclMjNjJy8lM0UlM0NsaW5lYXJHcmFkaWVudCBpZD0nbicgeDI9Jy45OTknIHhsaW5rOmhyZWY9JyUyM2wnLyUzRSUzQ2xpbmVhckdyYWRpZW50IGlkPSdvJyB4MT0nLS4wMDEnIHkxPScuNTAyJyB4Mj0nMScgeTI9Jy41MDInIHhsaW5rOmhyZWY9JyUyM2MnLyUzRSUzQ2xpbmVhckdyYWRpZW50IGlkPSdwJyB4Mj0nMS4wMDEnIHhsaW5rOmhyZWY9JyUyM2wnLyUzRSUzQ2xpbmVhckdyYWRpZW50IGlkPSdyJyB4MT0nLjUnIHkxPScxLjM1MScgeTI9Jy0uNzU1JyB4bGluazpocmVmPSclMjNjJy8lM0UlM0MvZGVmcyUzRSUzQ3BhdGggY2xhc3M9J2EnIGQ9J00wIDBoNDAwdjI2NkgweicvJTNFJTNDZyB0cmFuc2Zvcm09J3RyYW5zbGF0ZSguNDI0IDEzMy4zMTMpJyBjbGlwLXBhdGg9J3VybCglMjNhKSclM0UlM0NwYXRoIGQ9J00xODIuNzMzIDBjLjgwNyAwIDEuNjE0IDAgMi41NDMuMDEzIDk5LjY4Mi45NCAxODAuMTkxIDYwLjA3NCAxODAuMTkxIDEzMi44ODcgMCA3My40LTgxLjgxMyAxMzIuOS0xODIuNzMzIDEzMi45UzAgMjA2LjI5NCAwIDEzMi45IDgxLjgxMyAwIDE4Mi43MzMgMHonIHRyYW5zZm9ybT0ndHJhbnNsYXRlKDE3LjAxIC0uNDM0KScgZmlsbD0ndXJsKCUyM2IpJy8lM0UlM0MvZyUzRSUzQ2cgdHJhbnNmb3JtPSd0cmFuc2xhdGUoMTM2LjIxIDY3Ljk5NiknJTNFJTNDcGF0aCBkPSdNMzg2LjQ5MyAxMzUuNzVoLTkxLjA0NmE5LjgxMSA5LjgxMSAwIDAgMC05Ljc4NyA5Ljc4N3YxLjk2M2gxOS41NzV2ODAuMjY0YTE2Ljg5MSAxNi44OTEgMCAwIDAgMTYuODQzIDE2LjgzNmg2My42NDhhNy42MjUgNy42MjUgMCAwIDAgNy42MDctNy42di05NC40YTYuODc2IDYuODc2IDAgMCAwLTYuODQtNi44NXonIHRyYW5zZm9ybT0ndHJhbnNsYXRlKC0yODUuNjQ4IC0xMzUuNzUpJyBmaWxsPSd1cmwoJTIzYyknLyUzRSUzQ3BhdGggZD0nTTI5NS4zOTEgMTM1Ljc1aC4wNDhhOS43NTEgOS43NTEgMCAwIDEgOS43NzUgOS43MzN2MkgyODUuNjR2LTJhOS43NTEgOS43NTEgMCAwIDEgOS43NTEtOS43MzN6JyB0cmFuc2Zvcm09J3RyYW5zbGF0ZSgtMjg1LjY0IC0xMzUuNzUpJyBmaWxsPSd1cmwoJTIzZCknLyUzRSUzQ3JlY3QgY2xhc3M9J2YnIHdpZHRoPSc1Mi40NzEnIGhlaWdodD0nNS4wOTEnIHJ4PScyLjU0NicgdHJhbnNmb3JtPSd0cmFuc2xhdGUoMzcuMzk0IDE4LjU4NiknLyUzRSUzQ3JlY3QgY2xhc3M9J2cnIHdpZHRoPSc1MC4xNTMnIGhlaWdodD0nMS44NDUnIHJ4PScuOTIyJyB0cmFuc2Zvcm09J3RyYW5zbGF0ZSgzOC41NTcgMjMuNzI1KScvJTNFJTNDcmVjdCBjbGFzcz0nZicgd2lkdGg9JzUyLjQ3MScgaGVpZ2h0PSc1LjA5MScgcng9JzIuNTQ2JyB0cmFuc2Zvcm09J3RyYW5zbGF0ZSgzNy4zOTQgMzQuNzA1KScvJTNFJTNDcmVjdCBjbGFzcz0nZycgd2lkdGg9JzUwLjE1MycgaGVpZ2h0PScxLjg0NScgcng9Jy45MjInIHRyYW5zZm9ybT0ndHJhbnNsYXRlKDM4LjU1NyAzOS44NDQpJy8lM0UlM0NyZWN0IHdpZHRoPSczMi4wOTknIGhlaWdodD0nNS4wOTEnIHJ4PScyLjU0NicgdHJhbnNmb3JtPSd0cmFuc2xhdGUoMzcuMzk0IDUxLjc3KScgZmlsbD0ndXJsKCUyM2kpJy8lM0UlM0NyZWN0IHdpZHRoPSczMC42OCcgaGVpZ2h0PScxLjg0NScgcng9Jy45MjInIHRyYW5zZm9ybT0ndHJhbnNsYXRlKDM4LjEwNyA1Ni45MDkpJyBmaWxsPSd1cmwoJTIzaiknLyUzRSUzQ3BhdGggZD0nTTM1My4wMTEgMjk0LjQ0czEuNjA1IDE0LjExOC05Ljc1MSAxMy44MDdoODIuOTU5czkuODcxLS4xIDkuNzU3LTEzLjgwN3onIHRyYW5zZm9ybT0ndHJhbnNsYXRlKC0zMDguNzQ3IC0xOTkuMzg3KScgZmlsbD0ndXJsKCUyM2spJy8lM0UlM0MvZyUzRSUzQ2cgdHJhbnNmb3JtPSd0cmFuc2xhdGUoMjg0Ljk0MSAxNzUuNTIzKSclM0UlM0NlbGxpcHNlIGN4PSc2LjE5OScgY3k9JzEuODc1JyByeD0nNi4xOTknIHJ5PScxLjg3NScgdHJhbnNmb3JtPSd0cmFuc2xhdGUoMi43MTMgMTYuMTk5KScgZmlsbD0ndXJsKCUyM2wpJy8lM0UlM0NwYXRoIGNsYXNzPSdsJyBkPSdNMTIuMDk5IDYuMDgxYTYuMDUgNi4wNSAwIDEgMC0xMC4zOCA0LjE5MyAyLjQgMi40IDAgMCAwLS4zNzcgMS4yODggMi40MzggMi40MzggMCAwIDAgMi40MzggMi40MzJoMS43NDN2My41MjhhLjUzMy41MzMgMCAxIDAgMS4wNiAwdi0zLjUyOGgxLjc0OWEyLjQzOCAyLjQzOCAwIDAgMCAyLjQzMi0yLjQzMiAyLjQzMyAyLjQzMyAwIDAgMC0uMzcxLTEuMjg4IDYuMDMyIDYuMDMyIDAgMCAwIDEuNzA2LTQuMTkzeicvJTNFJTNDL2clM0UlM0NnIHRyYW5zZm9ybT0ndHJhbnNsYXRlKDEyMy45NTIgMTYzLjAzNyknJTNFJTNDZWxsaXBzZSBjeD0nNC4yODMnIGN5PScxLjI5NCcgcng9JzQuMjgzJyByeT0nMS4yOTQnIHRyYW5zZm9ybT0ndHJhbnNsYXRlKDEuODkzIDExLjIzNyknIGZpbGw9J3VybCglMjNuKScvJTNFJTNDcGF0aCBkPSdNMjgwLjI2NiAyOTguNjMxYTQuMTkzIDQuMTkzIDAgMSAwLTcuMTg4IDIuOTE3IDEuNjg5IDEuNjg5IDAgMCAwLS4yNTcuODkyIDEuNyAxLjcgMCAwIDAgMS42ODkgMS42ODloMS4ydjIuNDVhLjM3MS4zNzEgMCAwIDAgLjczNyAwdi0yLjQ1aDEuMmExLjcgMS43IDAgMCAwIDEuNjg5LTEuNjg5IDEuNjkgMS42OSAwIDAgMC0uMjU4LS44OTMgNC4xOTMgNC4xOTMgMCAwIDAgMS4xOTItMi45MTd6JyB0cmFuc2Zvcm09J3RyYW5zbGF0ZSgtMjcxLjg4IC0yOTQuNDIxKScgZmlsbD0ndXJsKCUyM28pJy8lM0UlM0MvZyUzRSUzQ2cgdHJhbnNmb3JtPSd0cmFuc2xhdGUoMTAwLjI4IDE4MC44MjUpJyUzRSUzQ2VsbGlwc2UgY3g9JzUuMjA1JyBjeT0nMS41NzUnIHJ4PSc1LjIwNScgcnk9JzEuNTc1JyB0cmFuc2Zvcm09J3RyYW5zbGF0ZSgyLjI3NiAxMy41ODYpJyBmaWxsPSd1cmwoJTIzcCknLyUzRSUzQ3BhdGggY2xhc3M9J2wnIGQ9J00xMC4xNTkgNS4wODJhNS4wNzkgNS4wNzkgMCAxIDAtOC43MjEgMy41MzRBMi4wNDggMi4wNDggMCAwIDAgMS4xMjEgOS43YTIuMDU1IDIuMDU1IDAgMCAwIDIuMDQ5IDIuMDQ5aDEuNDd2Mi45NTlhLjQ0NC40NDQgMCAwIDAgLjg4NyAwdi0yLjk1OWgxLjQ1QTIuMDU1IDIuMDU1IDAgMCAwIDkuMDIgOS43YTIuMDQ0IDIuMDQ0IDAgMCAwLS4zMTEtMS4wODQgNS4wNTYgNS4wNTYgMCAwIDAgMS40NTYtMy41MzR6Jy8lM0UlM0MvZyUzRSUzQ3BhdGggZD0nTTUyNC45NTggMTMzLjAzNGMwLTUuNzQ0LTcuOTY2LTEwLjQtMTcuOC0xMC40cy0xNy44IDQuNjU0LTE3LjggMTAuNGMwIDQuNzQ0IDUuNTExIDguNTE4IDEyLjg1NCAxMGEzLjEzOSAzLjEzOSAwIDAgMSAyLjIxIDQuNnYuMDM2YS4xOC4xOCAwIDAgMCAuMjI4LjI0NmM3LjczOS0zLjI4MiAxMS45NDQtNS41NTMgMTQuMjMyLTcuMDQ0IDMuNzE4LTEuOTE0IDYuMDc2LTQuNzE3IDYuMDc2LTcuODM4em0tOS40MS0yLjE3NGEyLjE4NiAyLjE4NiAwIDEgMS0yLjQgMi4xNzQgMi4yODggMi4yODggMCAwIDEgMi40LTIuMTh6bS04LjM4NiAwYTIuMTg2IDIuMTg2IDAgMSAxLTIuNCAyLjE3NCAyLjI4OCAyLjI4OCAwIDAgMSAyLjQtMi4xOHptLTguMyA0LjM1NWEyLjE4NiAyLjE4NiAwIDEgMSAyLjQtMi4xOCAyLjI5NCAyLjI5NCAwIDAgMS0yLjQgMi4xNzR6JyB0cmFuc2Zvcm09J3RyYW5zbGF0ZSgtMjMwLjE1NyAtNjcuMjg1KScgZmlsbD0ndXJsKCUyM3IpJy8lM0UlM0Mvc3ZnJTNFXCIpIiwKICAgICAgICBiYWNrZ3JvdW5kU2l6ZTogJ2NvbnRhaW4nLAogICAgICAgIGJhY2tncm91bmRSZXBlYXQ6ICduby1yZXBlYXQnLAogICAgICAgIGJhY2tncm91bmRQb3NpdGlvbjogJ2NlbnRlcicsCiAgICAgICAgbWFyZ2luOiAnMCBhdXRvJwogICAgICB9CiAgICB9KSwgaCgicCIsIFsiXHU2NjgyXHU2NUUwXHU1MTg1XHU1QkI5Il0pXSk7CiAgfQp9KTsKVnVlLnVzZShWWEVUYWJsZSk7ClZYRVRhYmxlLnVzZShWWEVUYWJsZVBsdWdpbkV4cG9ydFhMU1gpOwoKLy8g5oqKWEVVVGlsc+e7keWumuWIsHRoaXPkuK3vvIzmlrnkvr/lnKjnu4Tku7bkuK3pgJrov4cgdGhpcyDorr/pl67kvb/nlKgKVnVlLlhFVXRpbHMgPSBWdWUucHJvdG90eXBlLiRYRVV0aWxzID0gWEVVdGlsczsKLy8g6Ieq5a6a5LmJ5YWo5bGA55qE5qC85byP5YyW5aSE55CG5Ye95pWwClZYRVRhYmxlLmZvcm1hdHMubWl4aW4oewogIC8vIOagvOW8j+WMluaXpeacn++8jOm7mOiupCB5eXl5LU1NLWRkIEhIOm1tOnNzCiAgZm9ybWF0RGF0ZTogZnVuY3Rpb24gZm9ybWF0RGF0ZShfcmVmLCBmb3JtYXQpIHsKICAgIHZhciBjZWxsVmFsdWUgPSBfcmVmLmNlbGxWYWx1ZTsKICAgIHJldHVybiBYRVV0aWxzLnRvRGF0ZVN0cmluZyhjZWxsVmFsdWUsIGZvcm1hdCB8fCAneXl5eS1NTS1kZCcpOwogIH0sCiAgLy8g5qC85byP5YyW5pel5pyf77yM6buY6K6kIHl5eXktTU0tZGQgSEg6bW06c3MKICBmb3JtYXREYXRlVGltZTogZnVuY3Rpb24gZm9ybWF0RGF0ZVRpbWUoX3JlZjIsIGZvcm1hdCkgewogICAgdmFyIGNlbGxWYWx1ZSA9IF9yZWYyLmNlbGxWYWx1ZTsKICAgIHJldHVybiBYRVV0aWxzLnRvRGF0ZVN0cmluZyhjZWxsVmFsdWUsIGZvcm1hdCB8fCAneXl5eS1NTS1kZCBISDptbTpzcycpOwogIH0sCiAgLy8g5Zub6IiN5LqU5YWl6YeR6aKd77yM5q+P6ZqUM+S9jemAl+WPt+WIhumalO+8jOm7mOiupDLkvY3mlbAKICBmb3JtYXRBbW91bnQ6IGZ1bmN0aW9uIGZvcm1hdEFtb3VudChfcmVmMykgewogICAgdmFyIGNlbGxWYWx1ZSA9IF9yZWYzLmNlbGxWYWx1ZTsKICAgIHZhciBkaWdpdHMgPSBhcmd1bWVudHMubGVuZ3RoID4gMSAmJiBhcmd1bWVudHNbMV0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1sxXSA6IDI7CiAgICBpZiAoTWF0aC5hYnMoY2VsbFZhbHVlKSA8IDAuMDAwMSkgcmV0dXJuIDA7CiAgICByZXR1cm4gWEVVdGlscy5jb21tYWZ5KFhFVXRpbHMudG9OdW1iZXIoY2VsbFZhbHVlKSwgewogICAgICBkaWdpdHM6IGRpZ2l0cwogICAgfSk7CiAgfSwKICBmb3JtYXROdW06IGZ1bmN0aW9uIGZvcm1hdE51bShfcmVmNCkgewogICAgdmFyIGNlbGxWYWx1ZSA9IF9yZWY0LmNlbGxWYWx1ZTsKICAgIHZhciBkaWdpdHMgPSBhcmd1bWVudHMubGVuZ3RoID4gMSAmJiBhcmd1bWVudHNbMV0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1sxXSA6IDI7CiAgICBpZiAoTWF0aC5hYnMoY2VsbFZhbHVlKSA8IDAuMDAwMSkgcmV0dXJuIDA7CiAgICB2YXIgbiA9ICcwJy5yZXBlYXQoZGlnaXRzKTsKICAgIHJldHVybiBudW1lcmFsKGNlbGxWYWx1ZSkuZm9ybWF0KCIwLDAuWyIuY29uY2F0KG4sICJdIikpOwogIH0sCiAgZm9ybWF0Q3VzdG9tOiBmdW5jdGlvbiBmb3JtYXRDdXN0b20oX3JlZjUsIGZvcm1hdCkgewogICAgdmFyIGNlbGxWYWx1ZSA9IF9yZWY1LmNlbGxWYWx1ZTsKICAgIHJldHVybiBmb3JtYXQoY2VsbFZhbHVlKTsKICB9Cn0pOwppbXBvcnQgVnVlQ2xpcGJvYXJkcyBmcm9tICd2dWUtY2xpcGJvYXJkMic7CmltcG9ydCB7IGJhc2VVcmwsIHBsYXRmb3JtVXJsIH0gZnJvbSAnQC91dGlscy9iYXNldXJsJzsKaW1wb3J0IG51bWVyYWwgZnJvbSAnbnVtZXJhbCc7CmltcG9ydCB7IHRhYmxlUGFnZVNpemUgfSBmcm9tICdAL3ZpZXdzL1BSTy9zZXR0aW5nJzsKVnVlLnByb3RvdHlwZS4kYmFzZVVybCA9IGJhc2VVcmwoKTsKVnVlLnVzZShWdWVDbGlwYm9hcmRzKTsKVnVlLnVzZShFbFRyZWVTZWxlY3QpOwpWdWUudXNlKFZ1ZUNvbnRleHRNZW51KTsKVnVlLnVzZShQcmludCk7ClZ1ZS51c2UoRWxlbWVudCwgewogIHNpemU6ICdzbWFsbCcKfSk7CnZhciAkbWVzc2FnZSA9IGZ1bmN0aW9uICRtZXNzYWdlKG9wdGlvbnMpIHsKICBpZiAodHlwZW9mIG9wdGlvbnMgPT09ICdzdHJpbmcnKSB7CiAgICBvcHRpb25zID0gewogICAgICBtZXNzYWdlOiBvcHRpb25zCiAgICB9OwogIH0KICByZXR1cm4gRWxlbWVudC5NZXNzYWdlKF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgb3B0aW9ucyksIHt9LCB7CiAgICBzaG93Q2xvc2U6IHRydWUsCiAgICBkdXJhdGlvbjogNDAwMAogIH0pKTsKfTsKWydzdWNjZXNzJywgJ3dhcm5pbmcnLCAnaW5mbycsICdlcnJvciddLmZvckVhY2goZnVuY3Rpb24gKHR5cGUpIHsKICAkbWVzc2FnZVt0eXBlXSA9IGZ1bmN0aW9uIChvcHRpb25zKSB7CiAgICBjb25zb2xlLmxvZygnb3B0aW9ucycsIG9wdGlvbnMsICRtZXNzYWdlKTsKICAgIGlmICh0eXBlb2Ygb3B0aW9ucyA9PT0gJ3N0cmluZycpIHsKICAgICAgb3B0aW9ucyA9IHsKICAgICAgICBtZXNzYWdlOiBvcHRpb25zCiAgICAgIH07CiAgICB9CiAgICBvcHRpb25zLnR5cGUgPSB0eXBlOwogICAgcmV0dXJuICRtZXNzYWdlKG9wdGlvbnMpOwogIH07Cn0pOwpWdWUucHJvdG90eXBlLiRtZXNzYWdlID0gJG1lc3NhZ2U7ClZ1ZS5wcm90b3R5cGUuJG1lc3NhZ2UuY2xvc2VBbGwgPSBFbGVtZW50Lk1lc3NhZ2UuY2xvc2VBbGw7CgovLyDms6jlhozkuIDkuKrlhajlsYDpmLLmipbmjIfku6QgYHYtdGhyb3R0bGVgLOaMh+S7pOWAvOS4uumYsuaKluaXtumXtO+8jOm7mOiupCAyMDAwbXMKVnVlLmRpcmVjdGl2ZSgndGhyb3R0bGUnLCB7CiAgLy8g5b2T6KKr57uR5a6a55qE5YWD57Sg5o+S5YWl5YiwIERPTSDkuK3ml7bigKbigKYKICBpbnNlcnRlZDogZnVuY3Rpb24gaW5zZXJ0ZWQoZWwsIGJpbmRpbmcsIHZub2RlLCBvbGRWbm9kZSkgewogICAgdmFyIHRocm90dGxlX3NlY29uZHMgPSBOdW1iZXIoYmluZGluZy52YWx1ZSkgfHwgMjAwMDsgLy8g6Ziy5oqW5bu26L+f5pe26Ze0LOavq+enkgogICAgdmFyIGlzRGlzYWJsZWQgPSBlbC5jbGFzc0xpc3QuY29udGFpbnMoJ2lzLWRpc2FibGVkJyk7IC8vIOWOn+Wni+aYr+WQpmRpc2FibGVkCiAgICBlbC5hZGRFdmVudExpc3RlbmVyKCdjbGljaycsIGZ1bmN0aW9uICgpIHsKICAgICAgZWwuc3R5bGUucG9pbnRlckV2ZW50cyA9ICdub25lJzsKICAgICAgaWYgKCFpc0Rpc2FibGVkKSBlbC5jbGFzc0xpc3QuYWRkKCdpcy1kaXNhYmxlZCcpOwogICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICBlbC5zdHlsZS5wb2ludGVyRXZlbnRzID0gJ2luaXRpYWwnOwogICAgICAgIGlmICghaXNEaXNhYmxlZCkgZWwuY2xhc3NMaXN0LnJlbW92ZSgnaXMtZGlzYWJsZWQnKTsKICAgICAgfSwgdGhyb3R0bGVfc2Vjb25kcyk7CiAgICB9KTsKICB9Cn0pOwovLyDlhajlsYDmjInpkq7mnYPpmZDmjqfliLbmjIfku6QgJ3YtYXV0aGVkJwovLyDpobXpnaLpgJrov4cgbWl4aW5zIOWFpSBAL21peGlucy9hdXRoLWJ1dHRvbnMg5o+Q5L6b5LqGIGRhdGEg5Lit55qEIEF1dGhCdXR0b25zIOinguWvn+WvueixoSjmoLzlvI86IHtidXR0b25zOltdfSAp77yMCi8vIOW5tuWwhuWFtuaPkOS+m+S4uiBwcm92aWRlIOWvueixoSBBdXRoQnV0dG9uc++8jOazqOWFpee7meWPr+iDveeahOW1jOWll+WQjuS7o+e7hOS7tuS9v+eUqOOAggovLyDlkI7ku6Pnu4Tku7bpgJrov4cgbWl4aW5zIOWFpSBAL21peGlucy9hdXRoLWJ1dHRvbnMvY2hpbGQtaW5qZWN077yM5bCGIEF1dGhCdXR0b25zIOWinuWKoOWIsOS6hue7hOS7tuWunuS+i+eahCBpbmplY3Qg5Lit44CCCi8vIOeUqOazle+8miA8ZWwgdi1hdXRoZWQ9IkF1dGhCdXR0b25zIj48L2VsPgpWdWUuZGlyZWN0aXZlKCdhdXRoZWQnLCB7CiAgaW5zZXJ0ZWQ6IGZ1bmN0aW9uIGluc2VydGVkKGVsLCBiaW5kaW5nLCB2bm9kZSwgb2xkVm5vZGUpIHsKICAgIGlmIChiaW5kaW5nLnZhbHVlICYmIGJpbmRpbmcudmFsdWUuYnV0dG9ucykgewogICAgICB2bm9kZS5jb21wb25lbnRJbnN0YW5jZS4kZWwuc3R5bGUuZGlzcGxheSA9ICdub25lJzsgLy8g5YWI6ZqQ6JeP6YG/5YWN6Zeq546wCiAgICB9CiAgfSwKICBjb21wb25lbnRVcGRhdGVkOiBmdW5jdGlvbiBjb21wb25lbnRVcGRhdGVkKGVsLCBiaW5kaW5nLCB2bm9kZSwgb2xkVm5vZGUpIHsKICAgIGlmICh2bm9kZS5jb21wb25lbnRJbnN0YW5jZS4kZWwuc3R5bGUuZGlzcGxheSAhPT0gJycpIHsKICAgICAgaWYgKGJpbmRpbmcudmFsdWUgJiYgYmluZGluZy52YWx1ZS5idXR0b25zKSB7CiAgICAgICAgaWYgKGJpbmRpbmcudmFsdWUuYnV0dG9ucy5maW5kKGZ1bmN0aW9uIChiKSB7CiAgICAgICAgICByZXR1cm4gYi5Db2RlID09PSB2bm9kZS5jb21wb25lbnRJbnN0YW5jZS4kYXR0cnMuY29kZTsKICAgICAgICB9KSkgewogICAgICAgICAgdm5vZGUuY29tcG9uZW50SW5zdGFuY2UuJGVsLnN0eWxlLmRpc3BsYXkgPSAnJzsKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdm5vZGUuY29tcG9uZW50SW5zdGFuY2UuJGVsLnN0eWxlLmRpc3BsYXkgPSAnJzsKICAgICAgfQogICAgfQogIH0KfSk7Ci8vIHJlZ2lzdGVyIGdsb2JhbCB1dGlsaXR5IGZpbHRlcnMKT2JqZWN0LmtleXMoZmlsdGVycykuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7CiAgVnVlLmZpbHRlcihrZXksIGZpbHRlcnNba2V5XSk7Cn0pOwpWdWUuY29uZmlnLnByb2R1Y3Rpb25UaXAgPSBmYWxzZTsKdmFyIGluc3RhbmNlID0gbnVsbDsKZnVuY3Rpb24gcmVuZGVyKCkgewogIHZhciBwcm9wcyA9IGFyZ3VtZW50cy5sZW5ndGggPiAwICYmIGFyZ3VtZW50c1swXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzBdIDoge307CiAgdmFyIGNvbnRhaW5lciA9IHByb3BzLmNvbnRhaW5lcjsKICAvLyByb3V0ZXIgPSBuZXcgVnVlUm91dGVyKHsKICAvLyAgIGJhc2U6IHdpbmRvdy5fX1BPV0VSRURfQllfUUlBTktVTl9fID8gJy9hcHAtdnVlLycgOiAnLycsCiAgLy8gICBtb2RlOiAnaGlzdG9yeScsCiAgLy8gICByb3V0ZXMsCiAgLy8gfSk7CiAgLy8gcm91dGVyLmJhc2UgPSB3aW5kb3cuX19QT1dFUkVEX0JZX1FJQU5LVU5fXyA/ICcvJyArIG5hbWUgKyAnLycgOiAnLycKICAvLyByb3V0ZXIubW9kZSA9ICdoYXNoJwoKICBpbnN0YW5jZSA9IG5ldyBWdWUoewogICAgcm91dGVyOiByb3V0ZXIsCiAgICBzdG9yZTogc3RvcmUsCiAgICByZW5kZXI6IGZ1bmN0aW9uIHJlbmRlcihoKSB7CiAgICAgIHJldHVybiBoKEFwcCk7CiAgICB9CiAgfSkuJG1vdW50KGNvbnRhaW5lciA/IGNvbnRhaW5lci5xdWVyeVNlbGVjdG9yKCcjYXBwJykgOiAnI2FwcCcpOwp9CgovLyDni6znq4vov5DooYzml7YKaWYgKCF3aW5kb3cuX19QT1dFUkVEX0JZX1FJQU5LVU5fXykgewogIHJlbmRlcigpOwp9CmZ1bmN0aW9uIGFkZENhY2hlZFZpZXdzKGNoaWxkcmVuKSB7CiAgY2hpbGRyZW4uZm9yRWFjaChmdW5jdGlvbiAoY2hpbGQpIHsKICAgIHN0b3JlLmRpc3BhdGNoKCd0YWdzVmlldy9hZGRDYWNoZWRWaWV3JywgY2hpbGQpOwogICAgLy8gUmVjdXJzaXZlbHkgY2hlY2sgaWYgdGhlIGN1cnJlbnQgY2hpbGQgaGFzIG5lc3RlZCBjaGlsZHJlbgogICAgaWYgKGNoaWxkLmNoaWxkcmVuICYmIGNoaWxkLmNoaWxkcmVuLmxlbmd0aCA+IDApIHsKICAgICAgYWRkQ2FjaGVkVmlld3MoY2hpbGQuY2hpbGRyZW4pOyAvLyBSZWN1cnNpdmVseSBjYWxsIGFkZENhY2hlZFZpZXdzIGZvciBuZXN0ZWQgY2hpbGRyZW4KICAgIH0KICB9KTsKfQp2YXIgZ2xvYmFsU3RhdGVDaGFuZ2VMaXN0ZW5lciA9IG51bGw7CmZ1bmN0aW9uIHN0b3JlT3B0aW9uKHByb3BzKSB7CiAgZ2xvYmFsU3RhdGVDaGFuZ2VMaXN0ZW5lciA9IHByb3BzLm9uR2xvYmFsU3RhdGVDaGFuZ2UgJiYgcHJvcHMub25HbG9iYWxTdGF0ZUNoYW5nZSgvKiNfX1BVUkVfXyovZnVuY3Rpb24gKCkgewogICAgdmFyIF9yZWY2ID0gX2FzeW5jVG9HZW5lcmF0b3IoLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUodmFsdWUsIHByZXYpIHsKICAgICAgdmFyIGFjY2Vzc1JvdXRlczsKICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUkKF9jb250ZXh0KSB7CiAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQucHJldiA9IF9jb250ZXh0Lm5leHQpIHsKICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgaWYgKCEodmFsdWUuQ3VyUGxhdGZvcm0gIT09ICcnICYmIHZhbHVlLkN1clBsYXRmb3JtICE9PSBwcmV2LkN1clBsYXRmb3JtKSkgewogICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSA5OwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICB9CiAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAzOwogICAgICAgICAgICByZXR1cm4gc3RvcmUuZGlzcGF0Y2goJ3Blcm1pc3Npb24vZ2VuZXJhdGVSb3V0ZXMnKTsKICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgX2NvbnRleHQudDAgPSBfY29udGV4dC5zZW50OwogICAgICAgICAgICBpZiAoX2NvbnRleHQudDApIHsKICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gNjsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgfQogICAgICAgICAgICBfY29udGV4dC50MCA9IFtdOwogICAgICAgICAgY2FzZSA2OgogICAgICAgICAgICBhY2Nlc3NSb3V0ZXMgPSBfY29udGV4dC50MDsKICAgICAgICAgICAgYWNjZXNzUm91dGVzLmZvckVhY2goZnVuY3Rpb24gKGVsZW1lbnQsIGlkeCkgewogICAgICAgICAgICAgIHZhciBfZWxlbWVudCRjaGlsZHJlbjsKICAgICAgICAgICAgICB2YXIgZXhpc3RpbmdSb3V0ZXMgPSByb3V0ZXIuZ2V0Um91dGVzKCk7CiAgICAgICAgICAgICAgLy8g5qOA5p+l5Li76Lev55SxCiAgICAgICAgICAgICAgdmFyIGlzTWFpbkV4aXN0ID0gZXhpc3RpbmdSb3V0ZXMuc29tZShmdW5jdGlvbiAocikgewogICAgICAgICAgICAgICAgcmV0dXJuIHIubmFtZSA9PT0gZWxlbWVudC5uYW1lOwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIC8vIOajgOafpeWtkOi3r+eUsQogICAgICAgICAgICAgIHZhciBpc0NoaWxkcmVuRXhpc3QgPSAoX2VsZW1lbnQkY2hpbGRyZW4gPSBlbGVtZW50LmNoaWxkcmVuKSA9PT0gbnVsbCB8fCBfZWxlbWVudCRjaGlsZHJlbiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2VsZW1lbnQkY2hpbGRyZW4uc29tZShmdW5jdGlvbiAoY2hpbGQpIHsKICAgICAgICAgICAgICAgIHJldHVybiBleGlzdGluZ1JvdXRlcy5zb21lKGZ1bmN0aW9uIChyKSB7CiAgICAgICAgICAgICAgICAgIHJldHVybiByLm5hbWUgPT09IGNoaWxkLm5hbWU7CiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICBpZiAoIWlzTWFpbkV4aXN0ICYmICFpc0NoaWxkcmVuRXhpc3QpIHsKICAgICAgICAgICAgICAgIHJvdXRlci5hZGRSb3V0ZShlbGVtZW50KTsKICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfot6/nlLHmt7vliqDmiJDlip8nKTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+i3r+eUseW3suWtmOWcqO+8jOi3s+i/h+a3u+WKoCcpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAvLyByb3V0ZXIuYWRkUm91dGUoZWxlbWVudCkKICAgICAgICAgICAgICBlbGVtZW50LmNoaWxkcmVuICYmIGFkZENhY2hlZFZpZXdzKGVsZW1lbnQuY2hpbGRyZW4pOwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgcm91dGVyLmFkZFJvdXRlKHsKICAgICAgICAgICAgICBwYXRoOiAnKicsCiAgICAgICAgICAgICAgcmVkaXJlY3Q6ICcvNDA0JywKICAgICAgICAgICAgICBoaWRkZW46IHRydWUKICAgICAgICAgICAgfSk7CiAgICAgICAgICBjYXNlIDk6CiAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuc3RvcCgpOwogICAgICAgIH0KICAgICAgfSwgX2NhbGxlZSk7CiAgICB9KSk7CiAgICByZXR1cm4gZnVuY3Rpb24gKF94LCBfeDIpIHsKICAgICAgcmV0dXJuIF9yZWY2LmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7CiAgICB9OwogIH0oKSwgdHJ1ZSk7Cn0KZXhwb3J0IGZ1bmN0aW9uIGJvb3RzdHJhcCgpIHsKICByZXR1cm4gX2Jvb3RzdHJhcC5hcHBseSh0aGlzLCBhcmd1bWVudHMpOwp9CmZ1bmN0aW9uIF9ib290c3RyYXAoKSB7CiAgX2Jvb3RzdHJhcCA9IF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlMigpIHsKICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlMiQoX2NvbnRleHQyKSB7CiAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0Mi5wcmV2ID0gX2NvbnRleHQyLm5leHQpIHsKICAgICAgICBjYXNlIDA6CiAgICAgICAgICBjb25zb2xlLmxvZygnW3Z1ZV0gdnVlIGFwcCBib290c3RyYXBlZCcpOwogICAgICAgIGNhc2UgMToKICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgcmV0dXJuIF9jb250ZXh0Mi5zdG9wKCk7CiAgICAgIH0KICAgIH0sIF9jYWxsZWUyKTsKICB9KSk7CiAgcmV0dXJuIF9ib290c3RyYXAuYXBwbHkodGhpcywgYXJndW1lbnRzKTsKfQpleHBvcnQgZnVuY3Rpb24gbW91bnQoX3gzKSB7CiAgcmV0dXJuIF9tb3VudC5hcHBseSh0aGlzLCBhcmd1bWVudHMpOwp9CmZ1bmN0aW9uIF9tb3VudCgpIHsKICBfbW91bnQgPSBfYXN5bmNUb0dlbmVyYXRvcigvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yUnVudGltZSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTMocHJvcHMpIHsKICAgIHZhciBhY2Nlc3NSb3V0ZXM7CiAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTMkKF9jb250ZXh0MykgewogICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDMucHJldiA9IF9jb250ZXh0My5uZXh0KSB7CiAgICAgICAgY2FzZSAwOgogICAgICAgICAgY29uc29sZS5sb2coJ1t2dWVdIHByb3BzIGZyb20gbWFpbiBmcmFtZXdvcmsnLCBwcm9wcyk7CiAgICAgICAgICBfY29udGV4dDMubmV4dCA9IDM7CiAgICAgICAgICByZXR1cm4gc3RvcmUuZGlzcGF0Y2goJ3Blcm1pc3Npb24vZ2VuZXJhdGVSb3V0ZXMnKTsKICAgICAgICBjYXNlIDM6CiAgICAgICAgICBfY29udGV4dDMudDAgPSBfY29udGV4dDMuc2VudDsKICAgICAgICAgIGlmIChfY29udGV4dDMudDApIHsKICAgICAgICAgICAgX2NvbnRleHQzLm5leHQgPSA2OwogICAgICAgICAgICBicmVhazsKICAgICAgICAgIH0KICAgICAgICAgIF9jb250ZXh0My50MCA9IFtdOwogICAgICAgIGNhc2UgNjoKICAgICAgICAgIGFjY2Vzc1JvdXRlcyA9IF9jb250ZXh0My50MDsKICAgICAgICAgIGFjY2Vzc1JvdXRlcy5mb3JFYWNoKGZ1bmN0aW9uIChlbGVtZW50LCBpZHgpIHsKICAgICAgICAgICAgcm91dGVyLmFkZFJvdXRlKGVsZW1lbnQpOwogICAgICAgICAgICBlbGVtZW50LmNoaWxkcmVuICYmIGFkZENhY2hlZFZpZXdzKGVsZW1lbnQuY2hpbGRyZW4pOwogICAgICAgICAgfSk7CiAgICAgICAgICByb3V0ZXIuYWRkUm91dGUoewogICAgICAgICAgICBwYXRoOiAnKicsCiAgICAgICAgICAgIHJlZGlyZWN0OiAnLzQwNCcsCiAgICAgICAgICAgIGhpZGRlbjogdHJ1ZQogICAgICAgICAgfSk7CgogICAgICAgICAgLy8g5aSE55CG5LuO5Y+m5aSW55qE5a2Q5bqU55So5omT5byA55qE5Li05pe26Lev55Sx77yM6buY6K6k5Yqg6L296ZyA6KaB5omT5byA55qE5Li05pe26Lev55SxCiAgICAgICAgICBwcm9wcy5zd2l0Y2hNaWNyb0FwcFRlbXBSb3V0ZSh0cmFuc2ZlclJvdXRlc1dpdGhQYXJlbnQoKSk7CiAgICAgICAgICBnZXRSb3V0ZXNXaXRoUGFyZW50KCk7CgogICAgICAgICAgLy8g5oqK5a2Q5bqU55So55qE5Z+656GA6buY6K6k6Lev55Sx5Lyg57uZ5Li75bqU55SoCiAgICAgICAgICBwcm9wcy5zd2l0Y2hNaWNyb0FwcEJhc2VSb3V0ZShjb25zdGFudFJvdXRlcywgcHJvcHMubmFtZSk7CgogICAgICAgICAgLy8g5Yqo5oCB6Lev55Sx6LWL5YC857uZ5Li75bqU55SoCiAgICAgICAgICAvLyBpZiAobG9jYWxTdG9yYWdlLmdldEl0ZW0oJ1JvdXRlclBhZ2VBcnJheScpKSB7CiAgICAgICAgICAvLyAgIGNvbnN0IHJvdXRlclBhZ2VBcnJheSA9IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oJ1JvdXRlclBhZ2VBcnJheScpKQogICAgICAgICAgLy8gICByb3V0ZXJQYWdlQXJyYXkuZm9yRWFjaChlbGVtZW50ID0+IHsKICAgICAgICAgIC8vICAgICBpZiAoZWxlbWVudC5wbGF0Zm9ybSA9PT0gcHJvcHMubmFtZSkgewogICAgICAgICAgLy8gICAgICAgZWxlbWVudC5jb21wb25lbnQgPSBjb21wb25lbnRNYXBbZWxlbWVudC5uYW1lXSAvLyDku47mmKDlsITkuK3ojrflj5bnu4Tku7YKICAgICAgICAgIC8vICAgICAgIGhhbmRsZUFkZFJvdXRlclBhZ2UoW2VsZW1lbnRdLCBlbGVtZW50LnBhcmVudE5hbWUsIGVsZW1lbnQucGFyZW50UGF0aCkKICAgICAgICAgIC8vICAgICAgIHByb3BzLnN3aXRjaE1pY3JvQXBwVGVtcFJvdXRlKFtlbGVtZW50XSwgcHJvcHMubmFtZSkKICAgICAgICAgIC8vICAgICB9CiAgICAgICAgICAvLyAgIH0pCiAgICAgICAgICAvLyB9CgogICAgICAgICAgVnVlLnByb3RvdHlwZS4kcWlhbmt1biA9IHByb3BzOwogICAgICAgICAgc3RvcmVPcHRpb24ocHJvcHMpOwogICAgICAgICAgcmVuZGVyKHByb3BzKTsKICAgICAgICBjYXNlIDE1OgogICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICByZXR1cm4gX2NvbnRleHQzLnN0b3AoKTsKICAgICAgfQogICAgfSwgX2NhbGxlZTMpOwogIH0pKTsKICByZXR1cm4gX21vdW50LmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7Cn0KZXhwb3J0IGZ1bmN0aW9uIHVubW91bnQoKSB7CiAgcmV0dXJuIF91bm1vdW50LmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7Cn0KCi8vIOmHjeWGmXRvRml4ZWTvvIzop6PlhrPnlLHkuo7pk7booYzlrrboiI3ms5Xlr7zoh7TnmoTpl67popjvvIzlkIzml7bvvIzop6PlhrNqc+iuoeeul+eyvuW6puS4ouWkseeahOmXrumimAovLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tZXh0ZW5kLW5hdGl2ZQpmdW5jdGlvbiBfdW5tb3VudCgpIHsKICBfdW5tb3VudCA9IF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlNCgpIHsKICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlNCQoX2NvbnRleHQ0KSB7CiAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0NC5wcmV2ID0gX2NvbnRleHQ0Lm5leHQpIHsKICAgICAgICBjYXNlIDA6CiAgICAgICAgICByZXNldFJvdXRlcigpOwogICAgICAgICAgaW5zdGFuY2UuJGRlc3Ryb3koKTsKICAgICAgICAgIGluc3RhbmNlLiRlbC5pbm5lckhUTUwgPSAnJzsKICAgICAgICAgIGluc3RhbmNlID0gbnVsbDsKICAgICAgICAgIGlmICh0eXBlb2YgZ2xvYmFsU3RhdGVDaGFuZ2VMaXN0ZW5lciA9PT0gJ2Z1bmN0aW9uJykgewogICAgICAgICAgICBnbG9iYWxTdGF0ZUNoYW5nZUxpc3RlbmVyKCk7IC8vIOiwg+eUqOi/lOWbnueahOWHveaVsOWPlua2iOebkeWQrAogICAgICAgICAgfQogICAgICAgICAgLy8gcm91dGVyID0gbnVsbAogICAgICAgIGNhc2UgNToKICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgcmV0dXJuIF9jb250ZXh0NC5zdG9wKCk7CiAgICAgIH0KICAgIH0sIF9jYWxsZWU0KTsKICB9KSk7CiAgcmV0dXJuIF91bm1vdW50LmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7Cn0KTnVtYmVyLnByb3RvdHlwZS50b0ZpeGVkID0gZnVuY3Rpb24gKGRpZ2l0cykgewogIC8vIOWPguaVsOmqjOivgQogIGlmIChkaWdpdHMgPCAwIHx8IGRpZ2l0cyA+IDEwMCkgewogICAgdGhyb3cgbmV3IFJhbmdlRXJyb3IoJ3RvRml4ZWQoKSBkaWdpdHMgYXJndW1lbnQgbXVzdCBiZSBiZXR3ZWVuIDAgYW5kIDEwMCcpOwogIH0KCiAgLy8g5aSE55CG54m55q6K5YC8CiAgaWYgKGlzTmFOKHRoaXMpIHx8ICFpc0Zpbml0ZSh0aGlzKSkgewogICAgcmV0dXJuIHRoaXMudG9TdHJpbmcoKTsKICB9CiAgdmFyIG51bSA9IE51bWJlcih0aGlzKTsKICB2YXIgaXNOZWdhdGl2ZSA9IG51bSA8IDA7CiAgdmFyIGFic051bSA9IE1hdGguYWJzKG51bSk7CgogIC8vIOS/ruato+a1rueCueaVsOeyvuW6puivr+W3rgogIC8vIOajgOa1i+aYr+WQpuWtmOWcqOeUseS6jua1rueCuei/kOeul+WvvOiHtOeahOW+ruWwj+ivr+W3ru+8iOWmgiA5MzcuMzM0OTk5OTk5OTk5IOW6lOivpeaYryA5MzcuMzM177yJCiAgdmFyIG51bVN0ciA9IGFic051bS50b1N0cmluZygpOwogIGlmIChudW1TdHIuaW5kZXhPZignLicpICE9PSAtMSkgewogICAgdmFyIF9kZWNpbWFsUGFydCA9IG51bVN0ci5zcGxpdCgnLicpWzFdOwogICAgLy8g5qOA5p+l5piv5ZCm5pyJ6L+e57ut55qEOeaIljDvvIzov5npgJrluLjooajnpLrnsr7luqbor6/lt64KICAgIGlmIChfZGVjaW1hbFBhcnQubGVuZ3RoID4gMTApIHsKICAgICAgLy8g5p+l5om+6L+e57ut55qEOe+8iOWQkeS4iuiIjeWFpeivr+W3ru+8iQogICAgICB2YXIgbmluZXNNYXRjaCA9IF9kZWNpbWFsUGFydC5tYXRjaCgvKDl7Nix9KSQvKTsKICAgICAgaWYgKG5pbmVzTWF0Y2gpIHsKICAgICAgICAvLyDmib7liLDov57nu63nmoQ577yM6L+b6KGM5L+u5q2jCiAgICAgICAgdmFyIHByZWNpc2lvbiA9IF9kZWNpbWFsUGFydC5sZW5ndGggLSBuaW5lc01hdGNoWzFdLmxlbmd0aDsKICAgICAgICB2YXIgY29ycmVjdGVkID0gTWF0aC5yb3VuZChhYnNOdW0gKiBNYXRoLnBvdygxMCwgcHJlY2lzaW9uKSkgLyBNYXRoLnBvdygxMCwgcHJlY2lzaW9uKTsKICAgICAgICBhYnNOdW0gPSBjb3JyZWN0ZWQ7CiAgICAgIH0KICAgICAgLy8g5p+l5om+6L+e57ut55qEMO+8iOWQkeS4i+iIjeWFpeivr+W3ru+8iQogICAgICB2YXIgemVyb3NNYXRjaCA9IF9kZWNpbWFsUGFydC5tYXRjaCgvKDB7Nix9KSQvKTsKICAgICAgaWYgKHplcm9zTWF0Y2gpIHsKICAgICAgICAvLyDmib7liLDov57nu63nmoQw77yM6L+b6KGM5L+u5q2jCiAgICAgICAgdmFyIF9wcmVjaXNpb24gPSBfZGVjaW1hbFBhcnQubGVuZ3RoIC0gemVyb3NNYXRjaFsxXS5sZW5ndGg7CiAgICAgICAgdmFyIF9jb3JyZWN0ZWQgPSBNYXRoLnJvdW5kKGFic051bSAqIE1hdGgucG93KDEwLCBfcHJlY2lzaW9uKSkgLyBNYXRoLnBvdygxMCwgX3ByZWNpc2lvbik7CiAgICAgICAgYWJzTnVtID0gX2NvcnJlY3RlZDsKICAgICAgfQogICAgfQogIH0KCiAgLy8g5aaC5p6c5bCP5pWw5L2N5pWw5Li6MO+8jOebtOaOpeWbm+iIjeS6lOWFpeWIsOaVtOaVsAogIGlmIChkaWdpdHMgPT09IDApIHsKICAgIHZhciByb3VuZGVkID0gTWF0aC5mbG9vcihhYnNOdW0gKyAwLjUpOwogICAgcmV0dXJuIChpc05lZ2F0aXZlID8gLXJvdW5kZWQgOiByb3VuZGVkKS50b1N0cmluZygpOwogIH0KCiAgLy8g6L2s5o2i5Li65a2X56ym5Liy6L+b6KGM57K+56Gu6K6h566XCiAgdmFyIGNvcnJlY3RlZE51bVN0ciA9IGFic051bS50b1N0cmluZygpOwoKICAvLyDlpITnkIbnp5HlraborqHmlbDms5UKICBpZiAoY29ycmVjdGVkTnVtU3RyLmluZGV4T2YoJ2UnKSAhPT0gLTEpIHsKICAgIHZhciBwYXJ0cyA9IGNvcnJlY3RlZE51bVN0ci5zcGxpdCgnZScpOwogICAgdmFyIGJhc2UgPSBwYXJzZUZsb2F0KHBhcnRzWzBdKTsKICAgIHZhciBleHAgPSBwYXJzZUludChwYXJ0c1sxXSk7CiAgICBjb3JyZWN0ZWROdW1TdHIgPSBiYXNlLnRvRml4ZWQoTWF0aC5tYXgoMCwgZGlnaXRzIC0gZXhwKSk7CiAgfQoKICAvLyDliIbnprvmlbTmlbDpg6jliIblkozlsI/mlbDpg6jliIYKICB2YXIgZG90SW5kZXggPSBjb3JyZWN0ZWROdW1TdHIuaW5kZXhPZignLicpOwogIHZhciBpbnRlZ2VyUGFydCA9IGRvdEluZGV4ID09PSAtMSA/IGNvcnJlY3RlZE51bVN0ciA6IGNvcnJlY3RlZE51bVN0ci5zdWJzdHJpbmcoMCwgZG90SW5kZXgpOwogIHZhciBkZWNpbWFsUGFydCA9IGRvdEluZGV4ID09PSAtMSA/ICcnIDogY29ycmVjdGVkTnVtU3RyLnN1YnN0cmluZyhkb3RJbmRleCArIDEpOwoKICAvLyDlpoLmnpzlsI/mlbDpg6jliIbplb/luqblsI/kuo7nrYnkuo7mjIflrprkvY3mlbDvvIznm7TmjqXooaXpm7bov5Tlm54KICBpZiAoZGVjaW1hbFBhcnQubGVuZ3RoIDw9IGRpZ2l0cykgewogICAgd2hpbGUgKGRlY2ltYWxQYXJ0Lmxlbmd0aCA8IGRpZ2l0cykgewogICAgICBkZWNpbWFsUGFydCArPSAnMCc7CiAgICB9CiAgICB2YXIgX3Jlc3VsdCA9IGRpZ2l0cyA9PT0gMCA/IGludGVnZXJQYXJ0IDogaW50ZWdlclBhcnQgKyAnLicgKyBkZWNpbWFsUGFydDsKICAgIHJldHVybiBpc05lZ2F0aXZlID8gJy0nICsgX3Jlc3VsdCA6IF9yZXN1bHQ7CiAgfQoKICAvLyDpnIDopoHov5vooYzlm5voiI3kupTlhaUKICB2YXIgdGFyZ2V0RGlnaXQgPSBwYXJzZUludChkZWNpbWFsUGFydC5jaGFyQXQoZGlnaXRzKSk7CiAgdmFyIHJvdW5kVXAgPSB0YXJnZXREaWdpdCA+PSA1OwoKICAvLyDmiKrlj5bliLDmjIflrprkvY3mlbAKICBkZWNpbWFsUGFydCA9IGRlY2ltYWxQYXJ0LnN1YnN0cmluZygwLCBkaWdpdHMpOwogIGlmIChyb3VuZFVwKSB7CiAgICAvLyDov5vkvY3lpITnkIYKICAgIHZhciBjYXJyeSA9IDE7CiAgICB2YXIgbmV3RGVjaW1hbFBhcnQgPSAnJzsKCiAgICAvLyDku47lj7PliLDlt6blpITnkIblsI/mlbDpg6jliIbnmoTov5vkvY0KICAgIGZvciAodmFyIGkgPSBkZWNpbWFsUGFydC5sZW5ndGggLSAxOyBpID49IDA7IGktLSkgewogICAgICB2YXIgZGlnaXQgPSBwYXJzZUludChkZWNpbWFsUGFydC5jaGFyQXQoaSkpICsgY2Fycnk7CiAgICAgIGlmIChkaWdpdCA+PSAxMCkgewogICAgICAgIG5ld0RlY2ltYWxQYXJ0ID0gJzAnICsgbmV3RGVjaW1hbFBhcnQ7CiAgICAgICAgY2FycnkgPSAxOwogICAgICB9IGVsc2UgewogICAgICAgIG5ld0RlY2ltYWxQYXJ0ID0gZGlnaXQudG9TdHJpbmcoKSArIG5ld0RlY2ltYWxQYXJ0OwogICAgICAgIGNhcnJ5ID0gMDsKICAgICAgICAvLyDlsIbliankvZnnmoTmlbDlrZfnm7TmjqXmi7zmjqUKICAgICAgICBuZXdEZWNpbWFsUGFydCA9IGRlY2ltYWxQYXJ0LnN1YnN0cmluZygwLCBpKSArIG5ld0RlY2ltYWxQYXJ0OwogICAgICAgIGJyZWFrOwogICAgICB9CiAgICB9CiAgICBkZWNpbWFsUGFydCA9IG5ld0RlY2ltYWxQYXJ0OwoKICAgIC8vIOWmguaenOWwj+aVsOmDqOWIhui/mOaciei/m+S9je+8jOmcgOimgeWkhOeQhuaVtOaVsOmDqOWIhgogICAgaWYgKGNhcnJ5ID09PSAxKSB7CiAgICAgIGlmIChkaWdpdHMgPT09IDApIHsKICAgICAgICAvLyDlpoLmnpzkv53nlZkw5L2N5bCP5pWw77yM55u05o6l57uZ5pW05pWw6YOo5YiG5YqgMQogICAgICAgIHZhciBpbnRWYWx1ZSA9IHBhcnNlSW50KGludGVnZXJQYXJ0KSArIDE7CiAgICAgICAgdmFyIF9yZXN1bHQyID0gaW50VmFsdWUudG9TdHJpbmcoKTsKICAgICAgICByZXR1cm4gaXNOZWdhdGl2ZSA/ICctJyArIF9yZXN1bHQyIDogX3Jlc3VsdDI7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5pW05pWw6YOo5YiG5YqgMe+8jOWwj+aVsOmDqOWIhumHjee9rgogICAgICAgIHZhciBfaW50VmFsdWUgPSBwYXJzZUludChpbnRlZ2VyUGFydCkgKyAxOwogICAgICAgIGludGVnZXJQYXJ0ID0gX2ludFZhbHVlLnRvU3RyaW5nKCk7CiAgICAgICAgZGVjaW1hbFBhcnQgPSAnMCcucmVwZWF0KGRpZ2l0cyk7CiAgICAgIH0KICAgIH0KICB9CgogIC8vIOe7hOijhee7k+aenAogIHZhciByZXN1bHQgPSBkaWdpdHMgPT09IDAgPyBpbnRlZ2VyUGFydCA6IGludGVnZXJQYXJ0ICsgJy4nICsgZGVjaW1hbFBhcnQ7CiAgcmV0dXJuIGlzTmVnYXRpdmUgPyAnLScgKyByZXN1bHQgOiByZXN1bHQ7Cn07CgovLyBuZXcgVnVlKHsKLy8gICBlbDogJyNhcHAnLAovLyAgIHJvdXRlciwKLy8gICBzdG9yZSwKLy8gICByZW5kZXI6IGggPT4gaChBcHApCi8vIH0p"}, {"version": 3, "names": ["<PERSON><PERSON>", "transferRoutesWithParent", "getRoutesWithParent", "Element", "App", "store", "router", "resetRouter", "constantRoutes", "filters", "ElTreeSelect", "VueContextMenu", "BimtkUI", "Print", "XEUtils", "VXETable", "VXETablePluginExportXLSX", "PortalVue", "use", "baseURL", "concat", "platformUrl", "tablePageSizes", "tablePageSize", "setup", "version", "zIndex", "emptyCell", "icon", "TABLE_TREE_OPEN", "TABLE_TREE_CLOSE", "table", "border", "showHeaderOverflow", "autoResize", "stripe", "keepSource", "scrollY", "enabled", "gt", "renderer", "add", "renderEmpty", "h", "renderOpts", "textAlign", "height", "display", "flexDirection", "justifyContent", "width", "max<PERSON><PERSON><PERSON>", "maxHeight", "backgroundImage", "backgroundSize", "backgroundRepeat", "backgroundPosition", "margin", "prototype", "$XEUtils", "formats", "mixin", "formatDate", "_ref", "format", "cellValue", "toDateString", "formatDateTime", "_ref2", "formatAmount", "_ref3", "digits", "arguments", "length", "undefined", "Math", "abs", "commafy", "toNumber", "formatNum", "_ref4", "n", "repeat", "numeral", "formatCustom", "_ref5", "VueClipboards", "baseUrl", "$baseUrl", "size", "$message", "options", "message", "Message", "_objectSpread", "showClose", "duration", "for<PERSON>ach", "type", "console", "log", "closeAll", "directive", "inserted", "el", "binding", "vnode", "oldVnode", "throttle_seconds", "Number", "value", "isDisabled", "classList", "contains", "addEventListener", "style", "pointerEvents", "setTimeout", "remove", "buttons", "componentInstance", "$el", "componentUpdated", "find", "b", "Code", "$attrs", "code", "Object", "keys", "key", "filter", "config", "productionTip", "instance", "render", "props", "container", "$mount", "querySelector", "window", "__POWERED_BY_QIANKUN__", "addCachedViews", "children", "child", "dispatch", "globalStateChangeListener", "storeOption", "onGlobalStateChange", "_ref6", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "prev", "accessRoutes", "wrap", "_callee$", "_context", "next", "CurPlatform", "t0", "sent", "element", "idx", "_element$children", "existingRoutes", "getRoutes", "isMainExist", "some", "r", "name", "isChildrenExist", "addRoute", "path", "redirect", "hidden", "stop", "_x", "_x2", "apply", "bootstrap", "_bootstrap", "_callee2", "_callee2$", "_context2", "mount", "_x3", "_mount", "_callee3", "_callee3$", "_context3", "switchMicroAppTempRoute", "switchMicroAppBaseRoute", "$qiankun", "unmount", "_unmount", "_callee4", "_callee4$", "_context4", "$destroy", "innerHTML", "toFixed", "RangeError", "isNaN", "isFinite", "toString", "num", "isNegative", "absNum", "numStr", "indexOf", "decimalPart", "split", "ninesMatch", "match", "precision", "corrected", "round", "pow", "zerosMatch", "rounded", "floor", "correctedNumStr", "parts", "base", "parseFloat", "exp", "parseInt", "max", "dotIndex", "integerPart", "substring", "result", "targetDigit", "char<PERSON>t", "roundUp", "carry", "newDecimalPart", "i", "digit", "intValue"], "sources": ["D:/project/platform_framework_master/platform_framework/Platform/Frontend/SubAppProduce/src/main.js"], "sourcesContent": ["import './public-path'\r\nimport Vue from 'vue'\r\n\r\n// import { handleAddRouterPage } from '@/utils'\r\n// import componentMap from '@/utils/componentMap'\r\nimport { transferRoutesWithParent, getRoutesWithParent } from '@/utils/tempRouterMap'\r\n\r\nimport 'normalize.css/normalize.css' // a modern alternative to CSS resets\r\n\r\nimport Element from 'element-ui'\r\nimport './styles/element-variables.scss'\r\nimport 'element-ui/lib/theme-chalk/icon.css'\r\n\r\nimport '@/styles/index.scss' // global css\r\n\r\nimport App from './App'\r\nimport store from './store'\r\nimport router, { resetRouter, constantRoutes } from './router'\r\n\r\nimport './icons' // icon\r\nimport './icons/icons/circleFont/iconfont.css'\r\n\r\nimport './permission' // permission control\r\nimport './utils/error-log' // error log\r\n\r\nimport * as filters from './filters' // global filters\r\nimport ElTreeSelect from 'el-tree-select'\r\n\r\nimport VueContextMenu from 'vue-contextmenu'\r\n\r\nimport BimtkUI from '@components/BimtkUI'\r\n\r\nimport Print from '@/utils/print/print-label'\r\nimport '@/utils/languagechange'\r\nimport '@/utils/math'\r\n\r\nimport XEUtils from 'xe-utils'\r\nimport VXETable from 'vxe-table'\r\n\r\nimport VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'\r\n\r\nimport 'vxe-table/lib/style.css'\r\nimport '@/utils/directives.js'\r\n\r\n// VxeUI.setConfig({\r\n//   emptyCell: '88'\r\n// })\r\nimport PortalVue from 'portal-vue'\r\nVue.use(BimtkUI, {\r\n  baseURL: `${platformUrl()}Platform`, // 设置表格组件的API基础URL(注意，这里实际要写到baseURL的下一级，有些系统里面用的是SYS，有些用的是Platform)\r\n  tablePageSizes: tablePageSize, // 设置表格组件的分页大小选项（默认[10, 20, 50, 100]）\r\n  tablePageSize: tablePageSize[0] // 设置表格组件的默认分页大小（默认20）\r\n})\r\nVue.use(PortalVue)\r\nVXETable.setup({\r\n  version: 0,\r\n  zIndex: 9999,\r\n  emptyCell: '-',\r\n  icon: {\r\n    TABLE_TREE_OPEN: 'cs-vxe-tree-up',\r\n    TABLE_TREE_CLOSE: 'cs-vxe-tree-down'\r\n  },\r\n  table: {\r\n    border: true,\r\n    showHeaderOverflow: true,\r\n    autoResize: true,\r\n    stripe: true,\r\n    keepSource: true,\r\n    scrollY: {\r\n      enabled: true, // 是否默认开启纵向虚拟滚动\r\n      gt: 50 // 当数据大于指定数量时自动触发启用虚拟滚动\r\n    }\r\n  }\r\n})\r\nVXETable.renderer.add('NotData', {\r\n  // 空内容模板\r\n  renderEmpty(h, renderOpts) {\r\n    return (\r\n      <div style={{\r\n        textAlign: 'center',\r\n        height: '100%',\r\n        display: 'flex',\r\n        flexDirection: 'column',\r\n        justifyContent: 'center'\r\n      }}>\r\n        <div style={{\r\n          width: '100%',\r\n          height: '70%',\r\n          maxWidth: '400px',\r\n          maxHeight: '266px',\r\n          backgroundImage: `url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width='400' height='266' viewBox='0 0 400 266'%3E%3Cdefs%3E%3Cstyle%3E.a%7Bfill:none%7D.f%7Bfill:url(%23e)%7D.g%7Bfill:url(%23f)%7D.l%7Bfill:url(%23m)%7D%3C/style%3E%3CclipPath id='a'%3E%3Cpath class='a' transform='translate(-.424 -.313)' d='M0 0h400v133H0z'/%3E%3C/clipPath%3E%3ClinearGradient id='b' x1='.5' y1='1.351' x2='.5' y2='-.755' gradientUnits='objectBoundingBox'%3E%3Cstop offset='.39' stop-color='%23fff' stop-opacity='0'/%3E%3Cstop offset='.91' stop-color='%239bcaff'/%3E%3C/linearGradient%3E%3ClinearGradient id='c' x1='.499' y1='1.028' x2='.5' y2='.039' gradientUnits='objectBoundingBox'%3E%3Cstop offset='0' stop-color='%23bfd4f3'/%3E%3Cstop offset='1' stop-color='%23e7edf8'/%3E%3C/linearGradient%3E%3ClinearGradient id='d' x1='.507' y1='1.397' x2='.486' y2='-.106' gradientUnits='objectBoundingBox'%3E%3Cstop offset='0' stop-color='%23e7edf8'/%3E%3Cstop offset='1' stop-color='%23c9daf4'/%3E%3C/linearGradient%3E%3ClinearGradient id='e' x1='.5' y1='1.033' x2='.5' y2='.035' gradientUnits='objectBoundingBox'%3E%3Cstop offset='0' stop-color='%23e7edf8'/%3E%3Cstop offset='1' stop-color='%23fefefe'/%3E%3C/linearGradient%3E%3ClinearGradient id='f' x1='.5' y1='1.039' x2='.5' y2='.029' gradientUnits='objectBoundingBox'%3E%3Cstop offset='0' stop-color='%23cbd8ee'/%3E%3Cstop offset='1' stop-color='%23cfdcef'/%3E%3C/linearGradient%3E%3ClinearGradient id='i' y1='1.032' y2='.038' xlink:href='%23e'/%3E%3ClinearGradient id='j' y1='1.036' y2='.036' xlink:href='%23f'/%3E%3ClinearGradient id='k' x1='.507' y1='2.589' x2='.496' y2='-.667' gradientUnits='objectBoundingBox'%3E%3Cstop offset='.52' stop-color='%23c9daf4'/%3E%3Cstop offset='.92' stop-color='%23fff'/%3E%3C/linearGradient%3E%3ClinearGradient id='l' y1='.5' x2='1' y2='.5' gradientUnits='objectBoundingBox'%3E%3Cstop offset='0' stop-color='%23e0e9f7'/%3E%3Cstop offset='1' stop-color='%23e9eef9'/%3E%3C/linearGradient%3E%3ClinearGradient id='m' y1='.5' x2='1' y2='.5' xlink:href='%23c'/%3E%3ClinearGradient id='n' x2='.999' xlink:href='%23l'/%3E%3ClinearGradient id='o' x1='-.001' y1='.502' x2='1' y2='.502' xlink:href='%23c'/%3E%3ClinearGradient id='p' x2='1.001' xlink:href='%23l'/%3E%3ClinearGradient id='r' x1='.5' y1='1.351' y2='-.755' xlink:href='%23c'/%3E%3C/defs%3E%3Cpath class='a' d='M0 0h400v266H0z'/%3E%3Cg transform='translate(.424 133.313)' clip-path='url(%23a)'%3E%3Cpath d='M182.733 0c.807 0 1.614 0 2.543.013 99.682.94 180.191 60.074 180.191 132.887 0 73.4-81.813 132.9-182.733 132.9S0 206.294 0 132.9 81.813 0 182.733 0z' transform='translate(17.01 -.434)' fill='url(%23b)'/%3E%3C/g%3E%3Cg transform='translate(136.21 67.996)'%3E%3Cpath d='M386.493 135.75h-91.046a9.811 9.811 0 0 0-9.787 9.787v1.963h19.575v80.264a16.891 16.891 0 0 0 16.843 16.836h63.648a7.625 7.625 0 0 0 7.607-7.6v-94.4a6.876 6.876 0 0 0-6.84-6.85z' transform='translate(-285.648 -135.75)' fill='url(%23c)'/%3E%3Cpath d='M295.391 135.75h.048a9.751 9.751 0 0 1 9.775 9.733v2H285.64v-2a9.751 9.751 0 0 1 9.751-9.733z' transform='translate(-285.64 -135.75)' fill='url(%23d)'/%3E%3Crect class='f' width='52.471' height='5.091' rx='2.546' transform='translate(37.394 18.586)'/%3E%3Crect class='g' width='50.153' height='1.845' rx='.922' transform='translate(38.557 23.725)'/%3E%3Crect class='f' width='52.471' height='5.091' rx='2.546' transform='translate(37.394 34.705)'/%3E%3Crect class='g' width='50.153' height='1.845' rx='.922' transform='translate(38.557 39.844)'/%3E%3Crect width='32.099' height='5.091' rx='2.546' transform='translate(37.394 51.77)' fill='url(%23i)'/%3E%3Crect width='30.68' height='1.845' rx='.922' transform='translate(38.107 56.909)' fill='url(%23j)'/%3E%3Cpath d='M353.011 294.44s1.605 14.118-9.751 13.807h82.959s9.871-.1 9.757-13.807z' transform='translate(-308.747 -199.387)' fill='url(%23k)'/%3E%3C/g%3E%3Cg transform='translate(284.941 175.523)'%3E%3Cellipse cx='6.199' cy='1.875' rx='6.199' ry='1.875' transform='translate(2.713 16.199)' fill='url(%23l)'/%3E%3Cpath class='l' d='M12.099 6.081a6.05 6.05 0 1 0-10.38 4.193 2.4 2.4 0 0 0-.377 1.288 2.438 2.438 0 0 0 2.438 2.432h1.743v3.528a.533.533 0 1 0 1.06 0v-3.528h1.749a2.438 2.438 0 0 0 2.432-2.432 2.433 2.433 0 0 0-.371-1.288 6.032 6.032 0 0 0 1.706-4.193z'/%3E%3C/g%3E%3Cg transform='translate(123.952 163.037)'%3E%3Cellipse cx='4.283' cy='1.294' rx='4.283' ry='1.294' transform='translate(1.893 11.237)' fill='url(%23n)'/%3E%3Cpath d='M280.266 298.631a4.193 4.193 0 1 0-7.188 2.917 1.689 1.689 0 0 0-.257.892 1.7 1.7 0 0 0 1.689 1.689h1.2v2.45a.371.371 0 0 0 .737 0v-2.45h1.2a1.7 1.7 0 0 0 1.689-1.689 1.69 1.69 0 0 0-.258-.893 4.193 4.193 0 0 0 1.192-2.917z' transform='translate(-271.88 -294.421)' fill='url(%23o)'/%3E%3C/g%3E%3Cg transform='translate(100.28 180.825)'%3E%3Cellipse cx='5.205' cy='1.575' rx='5.205' ry='1.575' transform='translate(2.276 13.586)' fill='url(%23p)'/%3E%3Cpath class='l' d='M10.159 5.082a5.079 5.079 0 1 0-8.721 3.534A2.048 2.048 0 0 0 1.121 9.7a2.055 2.055 0 0 0 2.049 2.049h1.47v2.959a.444.444 0 0 0 .887 0v-2.959h1.45A2.055 2.055 0 0 0 9.02 9.7a2.044 2.044 0 0 0-.311-1.084 5.056 5.056 0 0 0 1.456-3.534z'/%3E%3C/g%3E%3Cpath d='M524.958 133.034c0-5.744-7.966-10.4-17.8-10.4s-17.8 4.654-17.8 10.4c0 4.744 5.511 8.518 12.854 10a3.139 3.139 0 0 1 2.21 4.6v.036a.18.18 0 0 0 .228.246c7.739-3.282 11.944-5.553 14.232-7.044 3.718-1.914 6.076-4.717 6.076-7.838zm-9.41-2.174a2.186 2.186 0 1 1-2.4 2.174 2.288 2.288 0 0 1 2.4-2.18zm-8.386 0a2.186 2.186 0 1 1-2.4 2.174 2.288 2.288 0 0 1 2.4-2.18zm-8.3 4.355a2.186 2.186 0 1 1 2.4-2.18 2.294 2.294 0 0 1-2.4 2.174z' transform='translate(-230.157 -67.285)' fill='url(%23r)'/%3E%3C/svg%3E\")`,\r\n          backgroundSize: 'contain',\r\n          backgroundRepeat: 'no-repeat',\r\n          backgroundPosition: 'center',\r\n          margin: '0 auto'\r\n        }}/>\r\n        <p>暂无内容</p>\r\n      </div>\r\n    )\r\n  }\r\n})\r\n\r\nVue.use(VXETable)\r\nVXETable.use(VXETablePluginExportXLSX)\r\n\r\n// 把XEUTils绑定到this中，方便在组件中通过 this 访问使用\r\nVue.XEUtils = Vue.prototype.$XEUtils = XEUtils\r\n// 自定义全局的格式化处理函数\r\nVXETable.formats.mixin({\r\n  // 格式化日期，默认 yyyy-MM-dd HH:mm:ss\r\n  formatDate({ cellValue }, format) {\r\n    return XEUtils.toDateString(cellValue, format || 'yyyy-MM-dd')\r\n  },\r\n  // 格式化日期，默认 yyyy-MM-dd HH:mm:ss\r\n  formatDateTime({ cellValue }, format) {\r\n    return XEUtils.toDateString(cellValue, format || 'yyyy-MM-dd HH:mm:ss')\r\n  },\r\n  // 四舍五入金额，每隔3位逗号分隔，默认2位数\r\n  formatAmount({ cellValue }, digits = 2) {\r\n    if (Math.abs(cellValue) < 0.0001) return 0\r\n    return XEUtils.commafy(XEUtils.toNumber(cellValue), { digits })\r\n  },\r\n  formatNum({ cellValue }, digits = 2) {\r\n    if (Math.abs(cellValue) < 0.0001) return 0\r\n    const n = '0'.repeat(digits)\r\n    return numeral(cellValue).format(`0,0.[${n}]`)\r\n  },\r\n  formatCustom({ cellValue }, format) {\r\n    return format(cellValue)\r\n  }\r\n})\r\n\r\nimport VueClipboards from 'vue-clipboard2'\r\nimport { baseUrl, platformUrl } from '@/utils/baseurl'\r\nimport numeral from 'numeral'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\n\r\nVue.prototype.$baseUrl = baseUrl()\r\nVue.use(VueClipboards)\r\n\r\nVue.use(ElTreeSelect)\r\nVue.use(VueContextMenu)\r\nVue.use(Print)\r\n\r\nVue.use(Element, {\r\n  size: 'small'\r\n})\r\n\r\nconst $message = options => {\r\n  if (typeof options === 'string') {\r\n    options = {\r\n      message: options\r\n    }\r\n  }\r\n  return Element.Message({\r\n    ...options,\r\n    showClose: true,\r\n    duration: 4000\r\n  })\r\n}\r\n\r\n['success', 'warning', 'info', 'error'].forEach(type => {\r\n  $message[type] = options => {\r\n    console.log('options', options, $message)\r\n    if (typeof options === 'string') {\r\n      options = {\r\n        message: options\r\n      }\r\n    }\r\n    options.type = type\r\n    return $message(options)\r\n  }\r\n})\r\n\r\nVue.prototype.$message = $message\r\nVue.prototype.$message.closeAll = Element.Message.closeAll\r\n\r\n// 注册一个全局防抖指令 `v-throttle`,指令值为防抖时间，默认 2000ms\r\nVue.directive('throttle', {\r\n  // 当被绑定的元素插入到 DOM 中时……\r\n  inserted: (el, binding, vnode, oldVnode) => {\r\n    const throttle_seconds = Number(binding.value) || 2000 // 防抖延迟时间,毫秒\r\n    const isDisabled = el.classList.contains('is-disabled') // 原始是否disabled\r\n    el.addEventListener('click', () => {\r\n      el.style.pointerEvents = 'none'\r\n      if (!isDisabled) el.classList.add('is-disabled')\r\n      setTimeout(() => {\r\n        el.style.pointerEvents = 'initial'\r\n        if (!isDisabled) el.classList.remove('is-disabled')\r\n      }, throttle_seconds)\r\n    })\r\n  }\r\n})\r\n// 全局按钮权限控制指令 'v-authed'\r\n// 页面通过 mixins 入 @/mixins/auth-buttons 提供了 data 中的 AuthButtons 观察对象(格式: {buttons:[]} )，\r\n// 并将其提供为 provide 对象 AuthButtons，注入给可能的嵌套后代组件使用。\r\n// 后代组件通过 mixins 入 @/mixins/auth-buttons/child-inject，将 AuthButtons 增加到了组件实例的 inject 中。\r\n// 用法： <el v-authed=\"AuthButtons\"></el>\r\nVue.directive('authed', {\r\n  inserted: (el, binding, vnode, oldVnode) => {\r\n    if (binding.value && binding.value.buttons) {\r\n      vnode.componentInstance.$el.style.display = 'none' // 先隐藏避免闪现\r\n    }\r\n  },\r\n  componentUpdated: (el, binding, vnode, oldVnode) => {\r\n    if (vnode.componentInstance.$el.style.display !== '') {\r\n      if (binding.value && binding.value.buttons) {\r\n        if (\r\n          binding.value.buttons.find(\r\n            b => b.Code === vnode.componentInstance.$attrs.code\r\n          )\r\n        ) {\r\n          vnode.componentInstance.$el.style.display = ''\r\n        }\r\n      } else {\r\n        vnode.componentInstance.$el.style.display = ''\r\n      }\r\n    }\r\n  }\r\n})\r\n// register global utility filters\r\nObject.keys(filters).forEach(key => {\r\n  Vue.filter(key, filters[key])\r\n})\r\n\r\nVue.config.productionTip = false\r\n\r\nlet instance = null\r\nfunction render(props = {}) {\r\n  const { container } = props\r\n  // router = new VueRouter({\r\n  //   base: window.__POWERED_BY_QIANKUN__ ? '/app-vue/' : '/',\r\n  //   mode: 'history',\r\n  //   routes,\r\n  // });\r\n  // router.base = window.__POWERED_BY_QIANKUN__ ? '/' + name + '/' : '/'\r\n  // router.mode = 'hash'\r\n\r\n  instance = new Vue({\r\n    router,\r\n    store,\r\n    render: (h) => h(App)\r\n  }).$mount(container ? container.querySelector('#app') : '#app')\r\n}\r\n\r\n// 独立运行时\r\nif (!window.__POWERED_BY_QIANKUN__) {\r\n  render()\r\n}\r\n\r\nfunction addCachedViews(children) {\r\n  children.forEach((child) => {\r\n    store.dispatch('tagsView/addCachedView', child)\r\n    // Recursively check if the current child has nested children\r\n    if (child.children && child.children.length > 0) {\r\n      addCachedViews(child.children) // Recursively call addCachedViews for nested children\r\n    }\r\n  })\r\n}\r\n\r\nlet globalStateChangeListener = null\r\nfunction storeOption(props) {\r\n  globalStateChangeListener = props.onGlobalStateChange &&\r\n    props.onGlobalStateChange(async(value, prev) => {\r\n      // console.log(`[onGlobalStateChange - ${props.name}]:`, value, prev) value.CurPlatform !== '' && value.CurPlatform === prev.CurPlatform && value.ModuleId !== prev.ModuleId\r\n      // console.log('produceValue', value)\r\n      // console.log('producePrev', prev)\r\n      if (value.CurPlatform !== '' && value.CurPlatform !== prev.CurPlatform) {\r\n        const accessRoutes = await store.dispatch('permission/generateRoutes') || []\r\n        accessRoutes.forEach((element, idx) => {\r\n          const existingRoutes = router.getRoutes()\r\n          // 检查主路由\r\n          const isMainExist = existingRoutes.some(r =>\r\n            r.name === element.name\r\n          )\r\n          // 检查子路由\r\n          const isChildrenExist = element.children?.some(child =>\r\n            existingRoutes.some(r => r.name === child.name)\r\n          )\r\n          if (!isMainExist && !isChildrenExist) {\r\n            router.addRoute(element)\r\n            console.log('路由添加成功')\r\n          } else {\r\n            console.log('路由已存在，跳过添加')\r\n          }\r\n          // router.addRoute(element)\r\n          element.children && addCachedViews(element.children)\r\n        })\r\n\r\n        router.addRoute({ path: '*', redirect: '/404', hidden: true })\r\n      }\r\n    },\r\n    true\r\n    )\r\n}\r\n\r\nexport async function bootstrap() {\r\n  console.log('[vue] vue app bootstraped')\r\n}\r\nexport async function mount(props) {\r\n  console.log('[vue] props from main framework', props)\r\n\r\n  const accessRoutes = await store.dispatch('permission/generateRoutes') || []\r\n  accessRoutes.forEach((element, idx) => {\r\n    router.addRoute(element)\r\n    element.children && addCachedViews(element.children)\r\n  })\r\n\r\n  router.addRoute({ path: '*', redirect: '/404', hidden: true })\r\n\r\n  // 处理从另外的子应用打开的临时路由，默认加载需要打开的临时路由\r\n  props.switchMicroAppTempRoute(transferRoutesWithParent())\r\n  getRoutesWithParent()\r\n\r\n  // 把子应用的基础默认路由传给主应用\r\n  props.switchMicroAppBaseRoute(constantRoutes, props.name)\r\n\r\n  // 动态路由赋值给主应用\r\n  // if (localStorage.getItem('RouterPageArray')) {\r\n  //   const routerPageArray = JSON.parse(localStorage.getItem('RouterPageArray'))\r\n  //   routerPageArray.forEach(element => {\r\n  //     if (element.platform === props.name) {\r\n  //       element.component = componentMap[element.name] // 从映射中获取组件\r\n  //       handleAddRouterPage([element], element.parentName, element.parentPath)\r\n  //       props.switchMicroAppTempRoute([element], props.name)\r\n  //     }\r\n  //   })\r\n  // }\r\n\r\n  Vue.prototype.$qiankun = props\r\n  storeOption(props)\r\n  render(props)\r\n}\r\nexport async function unmount() {\r\n  resetRouter()\r\n  instance.$destroy()\r\n  instance.$el.innerHTML = ''\r\n  instance = null\r\n  if (typeof globalStateChangeListener === 'function') {\r\n    globalStateChangeListener() // 调用返回的函数取消监听\r\n  }\r\n  // router = null\r\n}\r\n\r\n// 重写toFixed，解决由于银行家舍法导致的问题，同时，解决js计算精度丢失的问题\r\n// eslint-disable-next-line no-extend-native\r\nNumber.prototype.toFixed = function(digits) {\r\n  // 参数验证\r\n  if (digits < 0 || digits > 100) {\r\n    throw new RangeError('toFixed() digits argument must be between 0 and 100')\r\n  }\r\n\r\n  // 处理特殊值\r\n  if (isNaN(this) || !isFinite(this)) {\r\n    return this.toString()\r\n  }\r\n\r\n  const num = Number(this)\r\n  const isNegative = num < 0\r\n  let absNum = Math.abs(num)\r\n\r\n  // 修正浮点数精度误差\r\n  // 检测是否存在由于浮点运算导致的微小误差（如 937.334999999999 应该是 937.335）\r\n  const numStr = absNum.toString()\r\n  if (numStr.indexOf('.') !== -1) {\r\n    const decimalPart = numStr.split('.')[1]\r\n    // 检查是否有连续的9或0，这通常表示精度误差\r\n    if (decimalPart.length > 10) {\r\n      // 查找连续的9（向上舍入误差）\r\n      const ninesMatch = decimalPart.match(/(9{6,})$/)\r\n      if (ninesMatch) {\r\n        // 找到连续的9，进行修正\r\n        const precision = decimalPart.length - ninesMatch[1].length\r\n        const corrected = Math.round(absNum * Math.pow(10, precision)) / Math.pow(10, precision)\r\n        absNum = corrected\r\n      }\r\n      // 查找连续的0（向下舍入误差）\r\n      const zerosMatch = decimalPart.match(/(0{6,})$/)\r\n      if (zerosMatch) {\r\n        // 找到连续的0，进行修正\r\n        const precision = decimalPart.length - zerosMatch[1].length\r\n        const corrected = Math.round(absNum * Math.pow(10, precision)) / Math.pow(10, precision)\r\n        absNum = corrected\r\n      }\r\n    }\r\n  }\r\n\r\n  // 如果小数位数为0，直接四舍五入到整数\r\n  if (digits === 0) {\r\n    const rounded = Math.floor(absNum + 0.5)\r\n    return (isNegative ? -rounded : rounded).toString()\r\n  }\r\n\r\n  // 转换为字符串进行精确计算\r\n  let correctedNumStr = absNum.toString()\r\n\r\n  // 处理科学计数法\r\n  if (correctedNumStr.indexOf('e') !== -1) {\r\n    const parts = correctedNumStr.split('e')\r\n    const base = parseFloat(parts[0])\r\n    const exp = parseInt(parts[1])\r\n    correctedNumStr = base.toFixed(Math.max(0, digits - exp))\r\n  }\r\n\r\n  // 分离整数部分和小数部分\r\n  const dotIndex = correctedNumStr.indexOf('.')\r\n  let integerPart = dotIndex === -1 ? correctedNumStr : correctedNumStr.substring(0, dotIndex)\r\n  let decimalPart = dotIndex === -1 ? '' : correctedNumStr.substring(dotIndex + 1)\r\n\r\n  // 如果小数部分长度小于等于指定位数，直接补零返回\r\n  if (decimalPart.length <= digits) {\r\n    while (decimalPart.length < digits) {\r\n      decimalPart += '0'\r\n    }\r\n    const result = digits === 0 ? integerPart : integerPart + '.' + decimalPart\r\n    return isNegative ? '-' + result : result\r\n  }\r\n\r\n  // 需要进行四舍五入\r\n  const targetDigit = parseInt(decimalPart.charAt(digits))\r\n  const roundUp = targetDigit >= 5\r\n\r\n  // 截取到指定位数\r\n  decimalPart = decimalPart.substring(0, digits)\r\n\r\n  if (roundUp) {\r\n    // 进位处理\r\n    let carry = 1\r\n    let newDecimalPart = ''\r\n\r\n    // 从右到左处理小数部分的进位\r\n    for (let i = decimalPart.length - 1; i >= 0; i--) {\r\n      const digit = parseInt(decimalPart.charAt(i)) + carry\r\n      if (digit >= 10) {\r\n        newDecimalPart = '0' + newDecimalPart\r\n        carry = 1\r\n      } else {\r\n        newDecimalPart = digit.toString() + newDecimalPart\r\n        carry = 0\r\n        // 将剩余的数字直接拼接\r\n        newDecimalPart = decimalPart.substring(0, i) + newDecimalPart\r\n        break\r\n      }\r\n    }\r\n\r\n    decimalPart = newDecimalPart\r\n\r\n    // 如果小数部分还有进位，需要处理整数部分\r\n    if (carry === 1) {\r\n      if (digits === 0) {\r\n        // 如果保留0位小数，直接给整数部分加1\r\n        const intValue = parseInt(integerPart) + 1\r\n        const result = intValue.toString()\r\n        return isNegative ? '-' + result : result\r\n      } else {\r\n        // 整数部分加1，小数部分重置\r\n        const intValue = parseInt(integerPart) + 1\r\n        integerPart = intValue.toString()\r\n        decimalPart = '0'.repeat(digits)\r\n      }\r\n    }\r\n  }\r\n\r\n  // 组装结果\r\n  const result = digits === 0 ? integerPart : integerPart + '.' + decimalPart\r\n  return isNegative ? '-' + result : result\r\n}\r\n\r\n// new Vue({\r\n//   el: '#app',\r\n//   router,\r\n//   store,\r\n//   render: h => h(App)\r\n// })\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAO,eAAe;AACtB,OAAOA,GAAG,MAAM,KAAK;;AAErB;AACA;AACA,SAASC,wBAAwB,EAAEC,mBAAmB,QAAQ,uBAAuB;AAErF,OAAO,6BAA6B,EAAC;;AAErC,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAO,iCAAiC;AACxC,OAAO,qCAAqC;AAE5C,OAAO,qBAAqB,EAAC;;AAE7B,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,MAAM,IAAIC,WAAW,EAAEC,cAAc,QAAQ,UAAU;AAE9D,OAAO,SAAS,EAAC;AACjB,OAAO,uCAAuC;AAE9C,OAAO,cAAc,EAAC;AACtB,OAAO,mBAAmB,EAAC;;AAE3B,OAAO,KAAKC,OAAO,MAAM,WAAW,EAAC;AACrC,OAAOC,YAAY,MAAM,gBAAgB;AAEzC,OAAOC,cAAc,MAAM,iBAAiB;AAE5C,OAAOC,OAAO,MAAM,qBAAqB;AAEzC,OAAOC,KAAK,MAAM,2BAA2B;AAC7C,OAAO,wBAAwB;AAC/B,OAAO,cAAc;AAErB,OAAOC,OAAO,MAAM,UAAU;AAC9B,OAAOC,QAAQ,MAAM,WAAW;AAEhC,OAAOC,wBAAwB,MAAM,8BAA8B;AAEnE,OAAO,yBAAyB;AAChC,OAAO,uBAAuB;;AAE9B;AACA;AACA;AACA,OAAOC,SAAS,MAAM,YAAY;AAClCjB,GAAG,CAACkB,GAAG,CAACN,OAAO,EAAE;EACfO,OAAO,KAAAC,MAAA,CAAKC,WAAW,CAAC,CAAC,aAAU;EAAE;EACrCC,cAAc,EAAEC,aAAa;EAAE;EAC/BA,aAAa,EAAEA,aAAa,CAAC,CAAC,CAAC,CAAC;AAClC,CAAC,CAAC;AACFvB,GAAG,CAACkB,GAAG,CAACD,SAAS,CAAC;AAClBF,QAAQ,CAACS,KAAK,CAAC;EACbC,OAAO,EAAE,CAAC;EACVC,MAAM,EAAE,IAAI;EACZC,SAAS,EAAE,GAAG;EACdC,IAAI,EAAE;IACJC,eAAe,EAAE,gBAAgB;IACjCC,gBAAgB,EAAE;EACpB,CAAC;EACDC,KAAK,EAAE;IACLC,MAAM,EAAE,IAAI;IACZC,kBAAkB,EAAE,IAAI;IACxBC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE;MACPC,OAAO,EAAE,IAAI;MAAE;MACfC,EAAE,EAAE,EAAE,CAAC;IACT;EACF;AACF,CAAC,CAAC;AACFxB,QAAQ,CAACyB,QAAQ,CAACC,GAAG,CAAC,SAAS,EAAE;EAC/B;EACAC,WAAW,WAAXA,WAAWA,CAACC,CAAC,EAAEC,UAAU,EAAE;IACzB,OAAAD,CAAA;MAAA,SACc;QACVE,SAAS,EAAE,QAAQ;QACnBC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,cAAc,EAAE;MAClB;IAAC,IAAAN,CAAA;MAAA,SACa;QACVO,KAAK,EAAE,MAAM;QACbJ,MAAM,EAAE,KAAK;QACbK,QAAQ,EAAE,OAAO;QACjBC,SAAS,EAAE,OAAO;QAClBC,eAAe,4lLAA0lL;QACzmLC,cAAc,EAAE,SAAS;QACzBC,gBAAgB,EAAE,WAAW;QAC7BC,kBAAkB,EAAE,QAAQ;QAC5BC,MAAM,EAAE;MACV;IAAC,IAAAd,CAAA;EAIP;AACF,CAAC,CAAC;AAEF3C,GAAG,CAACkB,GAAG,CAACH,QAAQ,CAAC;AACjBA,QAAQ,CAACG,GAAG,CAACF,wBAAwB,CAAC;;AAEtC;AACAhB,GAAG,CAACc,OAAO,GAAGd,GAAG,CAAC0D,SAAS,CAACC,QAAQ,GAAG7C,OAAO;AAC9C;AACAC,QAAQ,CAAC6C,OAAO,CAACC,KAAK,CAAC;EACrB;EACAC,UAAU,WAAVA,UAAUA,CAAAC,IAAA,EAAgBC,MAAM,EAAE;IAAA,IAArBC,SAAS,GAAAF,IAAA,CAATE,SAAS;IACpB,OAAOnD,OAAO,CAACoD,YAAY,CAACD,SAAS,EAAED,MAAM,IAAI,YAAY,CAAC;EAChE,CAAC;EACD;EACAG,cAAc,WAAdA,cAAcA,CAAAC,KAAA,EAAgBJ,MAAM,EAAE;IAAA,IAArBC,SAAS,GAAAG,KAAA,CAATH,SAAS;IACxB,OAAOnD,OAAO,CAACoD,YAAY,CAACD,SAAS,EAAED,MAAM,IAAI,qBAAqB,CAAC;EACzE,CAAC;EACD;EACAK,YAAY,WAAZA,YAAYA,CAAAC,KAAA,EAA4B;IAAA,IAAzBL,SAAS,GAAAK,KAAA,CAATL,SAAS;IAAA,IAAIM,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;IACpC,IAAIG,IAAI,CAACC,GAAG,CAACX,SAAS,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC;IAC1C,OAAOnD,OAAO,CAAC+D,OAAO,CAAC/D,OAAO,CAACgE,QAAQ,CAACb,SAAS,CAAC,EAAE;MAAEM,MAAM,EAANA;IAAO,CAAC,CAAC;EACjE,CAAC;EACDQ,SAAS,WAATA,SAASA,CAAAC,KAAA,EAA4B;IAAA,IAAzBf,SAAS,GAAAe,KAAA,CAATf,SAAS;IAAA,IAAIM,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;IACjC,IAAIG,IAAI,CAACC,GAAG,CAACX,SAAS,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC;IAC1C,IAAMgB,CAAC,GAAG,GAAG,CAACC,MAAM,CAACX,MAAM,CAAC;IAC5B,OAAOY,OAAO,CAAClB,SAAS,CAAC,CAACD,MAAM,SAAA5C,MAAA,CAAS6D,CAAC,MAAG,CAAC;EAChD,CAAC;EACDG,YAAY,WAAZA,YAAYA,CAAAC,KAAA,EAAgBrB,MAAM,EAAE;IAAA,IAArBC,SAAS,GAAAoB,KAAA,CAATpB,SAAS;IACtB,OAAOD,MAAM,CAACC,SAAS,CAAC;EAC1B;AACF,CAAC,CAAC;AAEF,OAAOqB,aAAa,MAAM,gBAAgB;AAC1C,SAASC,OAAO,EAAElE,WAAW,QAAQ,iBAAiB;AACtD,OAAO8D,OAAO,MAAM,SAAS;AAC7B,SAAS5D,aAAa,QAAQ,qBAAqB;AAEnDvB,GAAG,CAAC0D,SAAS,CAAC8B,QAAQ,GAAGD,OAAO,CAAC,CAAC;AAClCvF,GAAG,CAACkB,GAAG,CAACoE,aAAa,CAAC;AAEtBtF,GAAG,CAACkB,GAAG,CAACR,YAAY,CAAC;AACrBV,GAAG,CAACkB,GAAG,CAACP,cAAc,CAAC;AACvBX,GAAG,CAACkB,GAAG,CAACL,KAAK,CAAC;AAEdb,GAAG,CAACkB,GAAG,CAACf,OAAO,EAAE;EACfsF,IAAI,EAAE;AACR,CAAC,CAAC;AAEF,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAGC,OAAO,EAAI;EAC1B,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC/BA,OAAO,GAAG;MACRC,OAAO,EAAED;IACX,CAAC;EACH;EACA,OAAOxF,OAAO,CAAC0F,OAAO,CAAAC,aAAA,CAAAA,aAAA,KACjBH,OAAO;IACVI,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE;EAAI,EACf,CAAC;AACJ,CAAC;AAED,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,CAACC,OAAO,CAAC,UAAAC,IAAI,EAAI;EACtDR,QAAQ,CAACQ,IAAI,CAAC,GAAG,UAAAP,OAAO,EAAI;IAC1BQ,OAAO,CAACC,GAAG,CAAC,SAAS,EAAET,OAAO,EAAED,QAAQ,CAAC;IACzC,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;MAC/BA,OAAO,GAAG;QACRC,OAAO,EAAED;MACX,CAAC;IACH;IACAA,OAAO,CAACO,IAAI,GAAGA,IAAI;IACnB,OAAOR,QAAQ,CAACC,OAAO,CAAC;EAC1B,CAAC;AACH,CAAC,CAAC;AAEF3F,GAAG,CAAC0D,SAAS,CAACgC,QAAQ,GAAGA,QAAQ;AACjC1F,GAAG,CAAC0D,SAAS,CAACgC,QAAQ,CAACW,QAAQ,GAAGlG,OAAO,CAAC0F,OAAO,CAACQ,QAAQ;;AAE1D;AACArG,GAAG,CAACsG,SAAS,CAAC,UAAU,EAAE;EACxB;EACAC,QAAQ,EAAE,SAAVA,QAAQA,CAAGC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAK;IAC1C,IAAMC,gBAAgB,GAAGC,MAAM,CAACJ,OAAO,CAACK,KAAK,CAAC,IAAI,IAAI,EAAC;IACvD,IAAMC,UAAU,GAAGP,EAAE,CAACQ,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,EAAC;IACxDT,EAAE,CAACU,gBAAgB,CAAC,OAAO,EAAE,YAAM;MACjCV,EAAE,CAACW,KAAK,CAACC,aAAa,GAAG,MAAM;MAC/B,IAAI,CAACL,UAAU,EAAEP,EAAE,CAACQ,SAAS,CAACvE,GAAG,CAAC,aAAa,CAAC;MAChD4E,UAAU,CAAC,YAAM;QACfb,EAAE,CAACW,KAAK,CAACC,aAAa,GAAG,SAAS;QAClC,IAAI,CAACL,UAAU,EAAEP,EAAE,CAACQ,SAAS,CAACM,MAAM,CAAC,aAAa,CAAC;MACrD,CAAC,EAAEV,gBAAgB,CAAC;IACtB,CAAC,CAAC;EACJ;AACF,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA5G,GAAG,CAACsG,SAAS,CAAC,QAAQ,EAAE;EACtBC,QAAQ,EAAE,SAAVA,QAAQA,CAAGC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAK;IAC1C,IAAIF,OAAO,CAACK,KAAK,IAAIL,OAAO,CAACK,KAAK,CAACS,OAAO,EAAE;MAC1Cb,KAAK,CAACc,iBAAiB,CAACC,GAAG,CAACN,KAAK,CAACpE,OAAO,GAAG,MAAM,EAAC;IACrD;EACF,CAAC;EACD2E,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAGlB,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAK;IAClD,IAAID,KAAK,CAACc,iBAAiB,CAACC,GAAG,CAACN,KAAK,CAACpE,OAAO,KAAK,EAAE,EAAE;MACpD,IAAI0D,OAAO,CAACK,KAAK,IAAIL,OAAO,CAACK,KAAK,CAACS,OAAO,EAAE;QAC1C,IACEd,OAAO,CAACK,KAAK,CAACS,OAAO,CAACI,IAAI,CACxB,UAAAC,CAAC;UAAA,OAAIA,CAAC,CAACC,IAAI,KAAKnB,KAAK,CAACc,iBAAiB,CAACM,MAAM,CAACC,IAAI;QAAA,CACrD,CAAC,EACD;UACArB,KAAK,CAACc,iBAAiB,CAACC,GAAG,CAACN,KAAK,CAACpE,OAAO,GAAG,EAAE;QAChD;MACF,CAAC,MAAM;QACL2D,KAAK,CAACc,iBAAiB,CAACC,GAAG,CAACN,KAAK,CAACpE,OAAO,GAAG,EAAE;MAChD;IACF;EACF;AACF,CAAC,CAAC;AACF;AACAiF,MAAM,CAACC,IAAI,CAACxH,OAAO,CAAC,CAACwF,OAAO,CAAC,UAAAiC,GAAG,EAAI;EAClClI,GAAG,CAACmI,MAAM,CAACD,GAAG,EAAEzH,OAAO,CAACyH,GAAG,CAAC,CAAC;AAC/B,CAAC,CAAC;AAEFlI,GAAG,CAACoI,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhC,IAAIC,QAAQ,GAAG,IAAI;AACnB,SAASC,MAAMA,CAAA,EAAa;EAAA,IAAZC,KAAK,GAAAhE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACxB,IAAQiE,SAAS,GAAKD,KAAK,CAAnBC,SAAS;EACjB;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAH,QAAQ,GAAG,IAAItI,GAAG,CAAC;IACjBM,MAAM,EAANA,MAAM;IACND,KAAK,EAALA,KAAK;IACLkI,MAAM,EAAE,SAARA,MAAMA,CAAG5F,CAAC;MAAA,OAAKA,CAAC,CAACvC,GAAG,CAAC;IAAA;EACvB,CAAC,CAAC,CAACsI,MAAM,CAACD,SAAS,GAAGA,SAAS,CAACE,aAAa,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;AACjE;;AAEA;AACA,IAAI,CAACC,MAAM,CAACC,sBAAsB,EAAE;EAClCN,MAAM,CAAC,CAAC;AACV;AAEA,SAASO,cAAcA,CAACC,QAAQ,EAAE;EAChCA,QAAQ,CAAC9C,OAAO,CAAC,UAAC+C,KAAK,EAAK;IAC1B3I,KAAK,CAAC4I,QAAQ,CAAC,wBAAwB,EAAED,KAAK,CAAC;IAC/C;IACA,IAAIA,KAAK,CAACD,QAAQ,IAAIC,KAAK,CAACD,QAAQ,CAACtE,MAAM,GAAG,CAAC,EAAE;MAC/CqE,cAAc,CAACE,KAAK,CAACD,QAAQ,CAAC,EAAC;IACjC;EACF,CAAC,CAAC;AACJ;AAEA,IAAIG,yBAAyB,GAAG,IAAI;AACpC,SAASC,WAAWA,CAACX,KAAK,EAAE;EAC1BU,yBAAyB,GAAGV,KAAK,CAACY,mBAAmB,IACnDZ,KAAK,CAACY,mBAAmB;IAAA,IAAAC,KAAA,GAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,CAAC,SAAAC,QAAM3C,KAAK,EAAE4C,IAAI;MAAA,IAAAC,YAAA;MAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAJ,IAAA,GAAAI,QAAA,CAAAC,IAAA;UAAA;YAAA,MAIrCjD,KAAK,CAACkD,WAAW,KAAK,EAAE,IAAIlD,KAAK,CAACkD,WAAW,KAAKN,IAAI,CAACM,WAAW;cAAAF,QAAA,CAAAC,IAAA;cAAA;YAAA;YAAAD,QAAA,CAAAC,IAAA;YAAA,OACzC1J,KAAK,CAAC4I,QAAQ,CAAC,2BAA2B,CAAC;UAAA;YAAAa,QAAA,CAAAG,EAAA,GAAAH,QAAA,CAAAI,IAAA;YAAA,IAAAJ,QAAA,CAAAG,EAAA;cAAAH,QAAA,CAAAC,IAAA;cAAA;YAAA;YAAAD,QAAA,CAAAG,EAAA,GAAI,EAAE;UAAA;YAAtEN,YAAY,GAAAG,QAAA,CAAAG,EAAA;YAClBN,YAAY,CAAC1D,OAAO,CAAC,UAACkE,OAAO,EAAEC,GAAG,EAAK;cAAA,IAAAC,iBAAA;cACrC,IAAMC,cAAc,GAAGhK,MAAM,CAACiK,SAAS,CAAC,CAAC;cACzC;cACA,IAAMC,WAAW,GAAGF,cAAc,CAACG,IAAI,CAAC,UAAAC,CAAC;gBAAA,OACvCA,CAAC,CAACC,IAAI,KAAKR,OAAO,CAACQ,IAAI;cAAA,CACzB,CAAC;cACD;cACA,IAAMC,eAAe,IAAAP,iBAAA,GAAGF,OAAO,CAACpB,QAAQ,cAAAsB,iBAAA,uBAAhBA,iBAAA,CAAkBI,IAAI,CAAC,UAAAzB,KAAK;gBAAA,OAClDsB,cAAc,CAACG,IAAI,CAAC,UAAAC,CAAC;kBAAA,OAAIA,CAAC,CAACC,IAAI,KAAK3B,KAAK,CAAC2B,IAAI;gBAAA,EAAC;cAAA,CACjD,CAAC;cACD,IAAI,CAACH,WAAW,IAAI,CAACI,eAAe,EAAE;gBACpCtK,MAAM,CAACuK,QAAQ,CAACV,OAAO,CAAC;gBACxBhE,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;cACvB,CAAC,MAAM;gBACLD,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;cAC3B;cACA;cACA+D,OAAO,CAACpB,QAAQ,IAAID,cAAc,CAACqB,OAAO,CAACpB,QAAQ,CAAC;YACtD,CAAC,CAAC;YAEFzI,MAAM,CAACuK,QAAQ,CAAC;cAAEC,IAAI,EAAE,GAAG;cAAEC,QAAQ,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAK,CAAC,CAAC;UAAA;UAAA;YAAA,OAAAlB,QAAA,CAAAmB,IAAA;QAAA;MAAA,GAAAxB,OAAA;IAAA,CAEjE;IAAA,iBAAAyB,EAAA,EAAAC,GAAA;MAAA,OAAA9B,KAAA,CAAA+B,KAAA,OAAA5G,SAAA;IAAA;EAAA,KACD,IACA,CAAC;AACL;AAEA,gBAAsB6G,SAASA,CAAA;EAAA,OAAAC,UAAA,CAAAF,KAAA,OAAA5G,SAAA;AAAA;AAE9B,SAAA8G,WAAA;EAAAA,UAAA,GAAAhC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,CAFM,SAAA+B,SAAA;IAAA,OAAAhC,mBAAA,GAAAK,IAAA,UAAA4B,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAA/B,IAAA,GAAA+B,SAAA,CAAA1B,IAAA;QAAA;UACL5D,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QAAA;QAAA;UAAA,OAAAqF,SAAA,CAAAR,IAAA;MAAA;IAAA,GAAAM,QAAA;EAAA,CACzC;EAAA,OAAAD,UAAA,CAAAF,KAAA,OAAA5G,SAAA;AAAA;AACD,gBAAsBkH,KAAKA,CAAAC,GAAA;EAAA,OAAAC,MAAA,CAAAR,KAAA,OAAA5G,SAAA;AAAA;AAiC1B,SAAAoH,OAAA;EAAAA,MAAA,GAAAtC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,CAjCM,SAAAqC,SAAqBrD,KAAK;IAAA,IAAAmB,YAAA;IAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAkC,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAArC,IAAA,GAAAqC,SAAA,CAAAhC,IAAA;QAAA;UAC/B5D,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEoC,KAAK,CAAC;UAAAuD,SAAA,CAAAhC,IAAA;UAAA,OAE1B1J,KAAK,CAAC4I,QAAQ,CAAC,2BAA2B,CAAC;QAAA;UAAA8C,SAAA,CAAA9B,EAAA,GAAA8B,SAAA,CAAA7B,IAAA;UAAA,IAAA6B,SAAA,CAAA9B,EAAA;YAAA8B,SAAA,CAAAhC,IAAA;YAAA;UAAA;UAAAgC,SAAA,CAAA9B,EAAA,GAAI,EAAE;QAAA;UAAtEN,YAAY,GAAAoC,SAAA,CAAA9B,EAAA;UAClBN,YAAY,CAAC1D,OAAO,CAAC,UAACkE,OAAO,EAAEC,GAAG,EAAK;YACrC9J,MAAM,CAACuK,QAAQ,CAACV,OAAO,CAAC;YACxBA,OAAO,CAACpB,QAAQ,IAAID,cAAc,CAACqB,OAAO,CAACpB,QAAQ,CAAC;UACtD,CAAC,CAAC;UAEFzI,MAAM,CAACuK,QAAQ,CAAC;YAAEC,IAAI,EAAE,GAAG;YAAEC,QAAQ,EAAE,MAAM;YAAEC,MAAM,EAAE;UAAK,CAAC,CAAC;;UAE9D;UACAxC,KAAK,CAACwD,uBAAuB,CAAC/L,wBAAwB,CAAC,CAAC,CAAC;UACzDC,mBAAmB,CAAC,CAAC;;UAErB;UACAsI,KAAK,CAACyD,uBAAuB,CAACzL,cAAc,EAAEgI,KAAK,CAACmC,IAAI,CAAC;;UAEzD;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA3K,GAAG,CAAC0D,SAAS,CAACwI,QAAQ,GAAG1D,KAAK;UAC9BW,WAAW,CAACX,KAAK,CAAC;UAClBD,MAAM,CAACC,KAAK,CAAC;QAAA;QAAA;UAAA,OAAAuD,SAAA,CAAAd,IAAA;MAAA;IAAA,GAAAY,QAAA;EAAA,CACd;EAAA,OAAAD,MAAA,CAAAR,KAAA,OAAA5G,SAAA;AAAA;AACD,gBAAsB2H,OAAOA,CAAA;EAAA,OAAAC,QAAA,CAAAhB,KAAA,OAAA5G,SAAA;AAAA;;AAW7B;AACA;AAAA,SAAA4H,SAAA;EAAAA,QAAA,GAAA9C,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,CAZO,SAAA6C,SAAA;IAAA,OAAA9C,mBAAA,GAAAK,IAAA,UAAA0C,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAA7C,IAAA,GAAA6C,SAAA,CAAAxC,IAAA;QAAA;UACLxJ,WAAW,CAAC,CAAC;UACb+H,QAAQ,CAACkE,QAAQ,CAAC,CAAC;UACnBlE,QAAQ,CAACb,GAAG,CAACgF,SAAS,GAAG,EAAE;UAC3BnE,QAAQ,GAAG,IAAI;UACf,IAAI,OAAOY,yBAAyB,KAAK,UAAU,EAAE;YACnDA,yBAAyB,CAAC,CAAC,EAAC;UAC9B;UACA;QAAA;QAAA;UAAA,OAAAqD,SAAA,CAAAtB,IAAA;MAAA;IAAA,GAAAoB,QAAA;EAAA,CACD;EAAA,OAAAD,QAAA,CAAAhB,KAAA,OAAA5G,SAAA;AAAA;AAIDqC,MAAM,CAACnD,SAAS,CAACgJ,OAAO,GAAG,UAASnI,MAAM,EAAE;EAC1C;EACA,IAAIA,MAAM,GAAG,CAAC,IAAIA,MAAM,GAAG,GAAG,EAAE;IAC9B,MAAM,IAAIoI,UAAU,CAAC,qDAAqD,CAAC;EAC7E;;EAEA;EACA,IAAIC,KAAK,CAAC,IAAI,CAAC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;IAClC,OAAO,IAAI,CAACC,QAAQ,CAAC,CAAC;EACxB;EAEA,IAAMC,GAAG,GAAGlG,MAAM,CAAC,IAAI,CAAC;EACxB,IAAMmG,UAAU,GAAGD,GAAG,GAAG,CAAC;EAC1B,IAAIE,MAAM,GAAGtI,IAAI,CAACC,GAAG,CAACmI,GAAG,CAAC;;EAE1B;EACA;EACA,IAAMG,MAAM,GAAGD,MAAM,CAACH,QAAQ,CAAC,CAAC;EAChC,IAAII,MAAM,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IAC9B,IAAMC,YAAW,GAAGF,MAAM,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACxC;IACA,IAAID,YAAW,CAAC3I,MAAM,GAAG,EAAE,EAAE;MAC3B;MACA,IAAM6I,UAAU,GAAGF,YAAW,CAACG,KAAK,CAAC,UAAU,CAAC;MAChD,IAAID,UAAU,EAAE;QACd;QACA,IAAME,SAAS,GAAGJ,YAAW,CAAC3I,MAAM,GAAG6I,UAAU,CAAC,CAAC,CAAC,CAAC7I,MAAM;QAC3D,IAAMgJ,SAAS,GAAG9I,IAAI,CAAC+I,KAAK,CAACT,MAAM,GAAGtI,IAAI,CAACgJ,GAAG,CAAC,EAAE,EAAEH,SAAS,CAAC,CAAC,GAAG7I,IAAI,CAACgJ,GAAG,CAAC,EAAE,EAAEH,SAAS,CAAC;QACxFP,MAAM,GAAGQ,SAAS;MACpB;MACA;MACA,IAAMG,UAAU,GAAGR,YAAW,CAACG,KAAK,CAAC,UAAU,CAAC;MAChD,IAAIK,UAAU,EAAE;QACd;QACA,IAAMJ,UAAS,GAAGJ,YAAW,CAAC3I,MAAM,GAAGmJ,UAAU,CAAC,CAAC,CAAC,CAACnJ,MAAM;QAC3D,IAAMgJ,UAAS,GAAG9I,IAAI,CAAC+I,KAAK,CAACT,MAAM,GAAGtI,IAAI,CAACgJ,GAAG,CAAC,EAAE,EAAEH,UAAS,CAAC,CAAC,GAAG7I,IAAI,CAACgJ,GAAG,CAAC,EAAE,EAAEH,UAAS,CAAC;QACxFP,MAAM,GAAGQ,UAAS;MACpB;IACF;EACF;;EAEA;EACA,IAAIlJ,MAAM,KAAK,CAAC,EAAE;IAChB,IAAMsJ,OAAO,GAAGlJ,IAAI,CAACmJ,KAAK,CAACb,MAAM,GAAG,GAAG,CAAC;IACxC,OAAO,CAACD,UAAU,GAAG,CAACa,OAAO,GAAGA,OAAO,EAAEf,QAAQ,CAAC,CAAC;EACrD;;EAEA;EACA,IAAIiB,eAAe,GAAGd,MAAM,CAACH,QAAQ,CAAC,CAAC;;EAEvC;EACA,IAAIiB,eAAe,CAACZ,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IACvC,IAAMa,KAAK,GAAGD,eAAe,CAACV,KAAK,CAAC,GAAG,CAAC;IACxC,IAAMY,IAAI,GAAGC,UAAU,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;IACjC,IAAMG,GAAG,GAAGC,QAAQ,CAACJ,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9BD,eAAe,GAAGE,IAAI,CAACvB,OAAO,CAAC/H,IAAI,CAAC0J,GAAG,CAAC,CAAC,EAAE9J,MAAM,GAAG4J,GAAG,CAAC,CAAC;EAC3D;;EAEA;EACA,IAAMG,QAAQ,GAAGP,eAAe,CAACZ,OAAO,CAAC,GAAG,CAAC;EAC7C,IAAIoB,WAAW,GAAGD,QAAQ,KAAK,CAAC,CAAC,GAAGP,eAAe,GAAGA,eAAe,CAACS,SAAS,CAAC,CAAC,EAAEF,QAAQ,CAAC;EAC5F,IAAIlB,WAAW,GAAGkB,QAAQ,KAAK,CAAC,CAAC,GAAG,EAAE,GAAGP,eAAe,CAACS,SAAS,CAACF,QAAQ,GAAG,CAAC,CAAC;;EAEhF;EACA,IAAIlB,WAAW,CAAC3I,MAAM,IAAIF,MAAM,EAAE;IAChC,OAAO6I,WAAW,CAAC3I,MAAM,GAAGF,MAAM,EAAE;MAClC6I,WAAW,IAAI,GAAG;IACpB;IACA,IAAMqB,OAAM,GAAGlK,MAAM,KAAK,CAAC,GAAGgK,WAAW,GAAGA,WAAW,GAAG,GAAG,GAAGnB,WAAW;IAC3E,OAAOJ,UAAU,GAAG,GAAG,GAAGyB,OAAM,GAAGA,OAAM;EAC3C;;EAEA;EACA,IAAMC,WAAW,GAAGN,QAAQ,CAAChB,WAAW,CAACuB,MAAM,CAACpK,MAAM,CAAC,CAAC;EACxD,IAAMqK,OAAO,GAAGF,WAAW,IAAI,CAAC;;EAEhC;EACAtB,WAAW,GAAGA,WAAW,CAACoB,SAAS,CAAC,CAAC,EAAEjK,MAAM,CAAC;EAE9C,IAAIqK,OAAO,EAAE;IACX;IACA,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,cAAc,GAAG,EAAE;;IAEvB;IACA,KAAK,IAAIC,CAAC,GAAG3B,WAAW,CAAC3I,MAAM,GAAG,CAAC,EAAEsK,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAChD,IAAMC,KAAK,GAAGZ,QAAQ,CAAChB,WAAW,CAACuB,MAAM,CAACI,CAAC,CAAC,CAAC,GAAGF,KAAK;MACrD,IAAIG,KAAK,IAAI,EAAE,EAAE;QACfF,cAAc,GAAG,GAAG,GAAGA,cAAc;QACrCD,KAAK,GAAG,CAAC;MACX,CAAC,MAAM;QACLC,cAAc,GAAGE,KAAK,CAAClC,QAAQ,CAAC,CAAC,GAAGgC,cAAc;QAClDD,KAAK,GAAG,CAAC;QACT;QACAC,cAAc,GAAG1B,WAAW,CAACoB,SAAS,CAAC,CAAC,EAAEO,CAAC,CAAC,GAAGD,cAAc;QAC7D;MACF;IACF;IAEA1B,WAAW,GAAG0B,cAAc;;IAE5B;IACA,IAAID,KAAK,KAAK,CAAC,EAAE;MACf,IAAItK,MAAM,KAAK,CAAC,EAAE;QAChB;QACA,IAAM0K,QAAQ,GAAGb,QAAQ,CAACG,WAAW,CAAC,GAAG,CAAC;QAC1C,IAAME,QAAM,GAAGQ,QAAQ,CAACnC,QAAQ,CAAC,CAAC;QAClC,OAAOE,UAAU,GAAG,GAAG,GAAGyB,QAAM,GAAGA,QAAM;MAC3C,CAAC,MAAM;QACL;QACA,IAAMQ,SAAQ,GAAGb,QAAQ,CAACG,WAAW,CAAC,GAAG,CAAC;QAC1CA,WAAW,GAAGU,SAAQ,CAACnC,QAAQ,CAAC,CAAC;QACjCM,WAAW,GAAG,GAAG,CAAClI,MAAM,CAACX,MAAM,CAAC;MAClC;IACF;EACF;;EAEA;EACA,IAAMkK,MAAM,GAAGlK,MAAM,KAAK,CAAC,GAAGgK,WAAW,GAAGA,WAAW,GAAG,GAAG,GAAGnB,WAAW;EAC3E,OAAOJ,UAAU,GAAG,GAAG,GAAGyB,MAAM,GAAGA,MAAM;AAC3C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\NodeDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\NodeDialog.vue", "mtime": 1758095476446}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IFNhdmVOb2RlIH0gZnJvbSAnQC9hcGkvUFJPL2ZhY3RvcnljaGVjaycKaW1wb3J0IHsgR2V0RW50aXR5Tm9kZSB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snCi8vIGltcG9ydCB7IFNhdmVDaGVja1R5cGUgfSBmcm9tICJAL2FwaS9QUk8vZmFjdG9yeWNoZWNrIjsKaW1wb3J0IHsgR2V0RmFjdG9yeVBlb3BsZWxpc3QgfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJwovLyBpbXBvcnQgeyBHZXRQcm9jZXNzQ29kZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJwpleHBvcnQgZGVmYXVsdCB7CiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIG1vZGU6ICcnLCAvLyDljLrliIbpobnnm67lkozlt6XljoIKICAgICAgUHJvamVjdElkOiAnJywgLy8g6aG555uuSWQKICAgICAgQ2hlY2tfT2JqZWN0X0lkOiAnJywKICAgICAgQm9tX0xldmVsOiAnJywKICAgICAgZm9ybTogewogICAgICAgIE5vZGVfQ29kZTogJycsCiAgICAgICAgQ2hhbmdlX0NoZWNrX1R5cGU6IFtdLAogICAgICAgIERpc3BsYXlfTmFtZTogJycsCiAgICAgICAgVENfVXNlcklkOiAnJywKICAgICAgICBaTF9Vc2VySWQ6ICcnLAogICAgICAgIERlbWFuZF9TcG90X0NoZWNrX1JhdGU6IHVuZGVmaW5lZCwKICAgICAgICBSZXF1aXJlbWVudF9TcG90X0NoZWNrX1JhdGU6IHVuZGVmaW5lZCwKICAgICAgICBUQ19Vc2VySWRzOiBbXSwKICAgICAgICBaTF9Vc2VySWRzOiBbXSwKICAgICAgICBDaGVja19TdHlsZTogJycsCiAgICAgICAgVHNfUmVxdWlyZV9UaW1lOiAnJywKICAgICAgICBabF9EZW1hbmRfU3BvdF9DaGVja19SYXRlOiB1bmRlZmluZWQsCiAgICAgICAgWmxfUmVxdWlyZW1lbnRfU3BvdF9DaGVja19SYXRlOiB1bmRlZmluZWQsCiAgICAgICAgVHNfRGVtYW5kX1Nwb3RfQ2hlY2tfUmF0ZTogdW5kZWZpbmVkLAogICAgICAgIFRzX1JlcXVpcmVtZW50X1Nwb3RfQ2hlY2tfUmF0ZTogdW5kZWZpbmVkCiAgICAgIH0sCgogICAgICBydWxlczogewogICAgICAgIERpc3BsYXlfTmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOihqOWNlScsIHRyaWdnZXI6ICdjaGFuZ2UnIH0KICAgICAgICBdLAogICAgICAgIENoZWNrX1R5cGU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgICAgXSwKICAgICAgICBDaGFuZ2VfQ2hlY2tfVHlwZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdmFsaWRhdG9yOiB0aGlzLkNoZWNrX1R5cGVfcnVsZXMsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgICAgXSwKICAgICAgICBDaGVja19TdHlsZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOihqOWNlScsIHRyaWdnZXI6ICdjaGFuZ2UnIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIHJ1bGVzX1psOiB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+35aGr5YaZ5a6M5pW06KGo5Y2VJywgdHJpZ2dlcjogJ2J1cicgfSwKICAgICAgcnVsZXNfVGM6IHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnYnVyJyB9LAogICAgICBaTF9Vc2VySWRzX1J1bGVzOiBbCiAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdmFsaWRhdG9yOiB0aGlzLkNoZWNrX1pMX1VzZXJJZHMsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgIF0sCiAgICAgIFRDX1VzZXJJZHNfUnVsZXM6IFsKICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB2YWxpZGF0b3I6IHRoaXMuQ2hlY2tfVENfVXNlcklkcywgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOihqOWNlScsIHRyaWdnZXI6ICdjaGFuZ2UnIH0KICAgICAgXSwKICAgICAgdGl0bGU6ICcnLAogICAgICBlZGl0SW5mbzoge30sCiAgICAgIFF1YWxpdHlOb2RlTGlzdDogW3sgTmFtZTogJ+WFpeW6kycgfSwgeyBOYW1lOiAn5Ye65bqTJyB9XSwgLy8g6LSo5qOA6IqC54K55YiX6KGoCiAgICAgIENoZWNrVHlwZUxpc3Q6IFsKICAgICAgICB7CiAgICAgICAgICBOYW1lOiAn6LSo6YePJywKICAgICAgICAgIElkOiAxCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBOYW1lOiAn5o6i5LykJywKICAgICAgICAgIElkOiAyCiAgICAgICAgfQogICAgICBdLCAvLyDotKjmo4DnsbvlnosKICAgICAgVXNlckxpc3Q6IFtdLCAvLyDotKjph4/lkZjvvIzmjqLkvKTkurrlkZgKICAgICAgQ2hlY2tTdHlsZUxpc3Q6IFsKICAgICAgICB7CiAgICAgICAgICBOYW1lOiAn5oq95qOAJywKICAgICAgICAgIElkOiAwCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBOYW1lOiAn5YWo5qOAJywKICAgICAgICAgIElkOiAxCiAgICAgICAgfQogICAgICBdLCAvLyDotKjmo4DmlrnlvI8KICAgICAgcXVhbGl0eUluc3BlY3Rpb246IDEKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICBOb2RlX0NvZGVfQ29tOiBmdW5jdGlvbigpIHsKICAgICAgaWYgKHRoaXMuZm9ybS5Ob2RlX0NvZGUpIHsKICAgICAgICByZXR1cm4gdHJ1ZQogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiBmYWxzZQogICAgICB9CiAgICB9CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5nZXRGYWN0b3J5UGVvcGxlbGlzdCgpCiAgfSwKICBtZXRob2RzOiB7CiAgICBDaGVja19aTF9Vc2VySWRzKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgewogICAgICBpZiAodGhpcy5mb3JtLkNoYW5nZV9DaGVja19UeXBlICYmIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZS5pbmNsdWRlcygxKSAmJiB0aGlzLmZvcm0uWkxfVXNlcklkcy5sZW5ndGggPT09IDApIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+ivt+Whq+WGmeWujOaVtOihqOWNlScpKQogICAgICB9IGVsc2UgewogICAgICAgIGNhbGxiYWNrKCkKICAgICAgfQogICAgfSwKICAgIENoZWNrX1RDX1VzZXJJZHMocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7CiAgICAgIGlmICghdGhpcy5Ob2RlX0NvZGVfQ29tICYmICEodGhpcy5mb3JtLkNoYW5nZV9DaGVja19UeXBlWzBdICE9PSAyICYmIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZS5sZW5ndGggIT09IDIpICYmIHRoaXMuZm9ybS5UQ19Vc2VySWRzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6K+35aGr5YaZ5a6M5pW06KGo5Y2VJykpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgY2FsbGJhY2soKQogICAgICB9CiAgICB9LAogICAgQ2hlY2tfVHlwZV9ydWxlcyhydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZS5sZW5ndGggPT09IDApIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+ivt+Whq+WGmeWujOaVtOihqOWNlScpKQogICAgICB9IGVsc2UgewogICAgICAgIGNhbGxiYWNrKCkKICAgICAgfQogICAgfSwKICAgIFNlbGVjdFR5cGUoaXRlbSkgewogICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpCiAgICAgIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZSA9IGl0ZW0KICAgICAgaWYgKGl0ZW0ubGVuZ3RoID09PSAxKSB7CiAgICAgICAgdGhpcy5mb3JtLkNoZWNrX1R5cGUgPSBpdGVtWzBdCiAgICAgIH0gZWxzZSBpZiAoaXRlbS5sZW5ndGggPT09IDIpIHsKICAgICAgICB0aGlzLmZvcm0uQ2hlY2tfVHlwZSA9IC0xCiAgICAgIH0KCiAgICAgIGlmICghaXRlbS5pbmNsdWRlcygxKSkgewogICAgICAgIHRoaXMuZm9ybS5aTF9Vc2VySWQgPSAnJwogICAgICAgIHRoaXMuZm9ybS5aTF9Vc2VySWRzID0gW10KICAgICAgfQogICAgICBpZiAoIWl0ZW0uaW5jbHVkZXMoMikpIHsKICAgICAgICB0aGlzLmZvcm0uVENfVXNlcklkID0gJycKICAgICAgICB0aGlzLmZvcm0uVENfVXNlcklkcyA9IFtdCiAgICAgIH0KICAgICAgY29uc29sZS5sb2codGhpcy5mb3JtLkNoYW5nZV9DaGVja19UeXBlKQogICAgfSwKICAgIHJlbW92ZVR5cGUoaXRlbSkgewogICAgICBjb25zb2xlLmxvZyhpdGVtLCAnYicpCiAgICAgIC8vIGlmIChpdGVtID09IDEpIHsKICAgICAgLy8gICB0aGlzLmZvcm0uWkxfVXNlcklkID0gIiI7CiAgICAgIC8vIH0gZWxzZSBpZiAoaXRlbSA9PSAyKSB7CiAgICAgIC8vICAgdGhpcy5mb3JtLlRDX1VzZXJJZCA9ICIiOwogICAgICAvLyB9CiAgICB9LAogICAgY2xlYXJUeXBlKHZhbCkgewogICAgICBjb25zb2xlLmxvZyh2YWwpCiAgICAgIHRoaXMuZm9ybS5aTF9Vc2VySWQgPSAnJwogICAgICB0aGlzLmZvcm0uVENfVXNlcklkID0gJycKICAgICAgdGhpcy5mb3JtLlpMX1VzZXJJZHMgPSBbXQogICAgICB0aGlzLmZvcm0uVENfVXNlcklkcyA9IFtdCiAgICB9LAogICAgaW5pdCh0aXRsZSwgY2hlY2tUeXBlLCBkYXRhLCBzeXNQcm9qZWN0SWQpIHsKICAgICAgdGhpcy5DaGVja19PYmplY3RfSWQgPSBjaGVja1R5cGUuSWQKICAgICAgdGhpcy5Cb21fTGV2ZWwgPSBjaGVja1R5cGUuQ29kZQogICAgICB0aGlzLnRpdGxlID0gdGl0bGUKICAgICAgaWYgKHRpdGxlID09PSAn57yW6L6RJykgewogICAgICAgIGNvbnNvbGUubG9nKGRhdGEpCiAgICAgICAgdGhpcy5mb3JtLklkID0gZGF0YS5JZAogICAgICAgIHRoaXMuZ2V0RW50aXR5Tm9kZShkYXRhKQogICAgICB9CiAgICAgIHRoaXMuZ2V0Q2hlY2tOb2RlKCkKICAgIH0sCiAgICBhc3luYyBhZGRDaGVja05vZGUoKSB7CiAgICAgIGNvbnN0IHsgWmxfRGVtYW5kX1Nwb3RfQ2hlY2tfUmF0ZSwgWmxfUmVxdWlyZW1lbnRfU3BvdF9DaGVja19SYXRlLCBUc19EZW1hbmRfU3BvdF9DaGVja19SYXRlLCBUc19SZXF1aXJlbWVudF9TcG90X0NoZWNrX1JhdGUsIC4uLm90aGVycyB9ID0gdGhpcy5mb3JtCiAgICAgIGNvbnN0IHN1Ym1pdCA9IHsKICAgICAgICAuLi5vdGhlcnMsCiAgICAgICAgQ2hlY2tfT2JqZWN0X0lkOiB0aGlzLkNoZWNrX09iamVjdF9JZCwKICAgICAgICBCb21fTGV2ZWw6IHRoaXMuQm9tX0xldmVsCiAgICAgIH0KICAgICAgaWYgKHRoaXMuZm9ybS5DaGVja19TdHlsZSA9PT0gMCkgeyAvLyDmir3mo4AKICAgICAgICBzdWJtaXQuWmxfRGVtYW5kX1Nwb3RfQ2hlY2tfUmF0ZSA9IFpsX0RlbWFuZF9TcG90X0NoZWNrX1JhdGUKICAgICAgICBzdWJtaXQuWmxfUmVxdWlyZW1lbnRfU3BvdF9DaGVja19SYXRlID0gWmxfUmVxdWlyZW1lbnRfU3BvdF9DaGVja19SYXRlCiAgICAgICAgc3VibWl0LlRzX0RlbWFuZF9TcG90X0NoZWNrX1JhdGUgPSBUc19EZW1hbmRfU3BvdF9DaGVja19SYXRlCiAgICAgICAgc3VibWl0LlRzX1JlcXVpcmVtZW50X1Nwb3RfQ2hlY2tfUmF0ZSA9IFRzX1JlcXVpcmVtZW50X1Nwb3RfQ2hlY2tfUmF0ZQogICAgICB9CiAgICAgIGF3YWl0IFNhdmVOb2RlKHN1Ym1pdCkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfkv53lrZjmiJDlip8nCiAgICAgICAgICB9KQogICAgICAgICAgdGhpcy4kZW1pdCgncmVmcmVzaCcpCiAgICAgICAgICB0aGlzLiRlbWl0KCdjbG9zZScpCiAgICAgICAgICB0aGlzLmRpYWxvZ0RhdGEgPSB7fQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGdldEZhY3RvcnlQZW9wbGVsaXN0KCkgewogICAgICBHZXRGYWN0b3J5UGVvcGxlbGlzdCgpLnRoZW4oKHJlcykgPT4gewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICBjb25zb2xlLmxvZyhyZXMuRGF0YSkKICAgICAgICAgIHRoaXMuVXNlckxpc3QgPSByZXMuRGF0YQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIC8vIOWIpOaWreaYr+W3peWOgui/mOaYr+mhueebruiOt+WPlui0qOajgOiKgueCuQogICAgZ2V0Q2hlY2tOb2RlKCkgewogICAgICBjb25zdCBQbGF0Zm9ybSA9CiAgICAgICAgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ1BsYXRmb3JtJykgfHwgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ0N1clBsYXRmb3JtJykKICAgICAgaWYgKFBsYXRmb3JtID09PSAnMicpIHsKICAgICAgICB0aGlzLm1vZGUgPSAnZmFjdG9yeScKICAgICAgICAvLyB0aGlzLmdldEZhY3RvcnlOb2RlKCk7CiAgICAgIH0KICAgICAgLy8g6I635Y+W6aG555uuL+W3peWOgmlkCiAgICAgIHRoaXMuUHJvamVjdElkID0KICAgICAgICB0aGlzLm1vZGUgPT09ICdmYWN0b3J5JwogICAgICAgICAgPyBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnQ3VyUmVmZXJlbmNlSWQnKQogICAgICAgICAgOiB0aGlzLlByb2plY3RJZAogICAgfSwKICAgIC8vIOWmguaenOaYr+W3peWOguiOt+WPlui0qOajgOiKgueCuQogICAgLy8gZ2V0RmFjdG9yeU5vZGUoKSB7CiAgICAvLyAgIEdldFByb2Nlc3NDb2RlTGlzdCh7c3lzX3dvcmtvYmplY3RfaWQ6dGhpcy5DaGVja19PYmplY3RfSWR9KS50aGVuKChyZXMpID0+IHsKICAgIC8vICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgLy8gICAgICAgbGV0IENoZWNrSnNvbiA9IHJlcy5EYXRhOwogICAgLy8gICAgICAgQ2hlY2tKc29uLnB1c2goeyBOYW1lOiAi5YWl5bqTIiB9LCB7IE5hbWU6ICLlh7rlupMiIH0pOwogICAgLy8gICAgICAgY29uc29sZS5sb2coQ2hlY2tKc29uKTsKICAgIC8vICAgICAgIHRoaXMuUXVhbGl0eU5vZGVMaXN0ID0gQ2hlY2tKc29uOwogICAgLy8gICAgICAgY29uc29sZS5sb2codGhpcy5RdWFsaXR5Tm9kZUxpc3QpOwogICAgLy8gICAgIH0gZWxzZSB7CiAgICAvLyAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgIC8vICAgICAgICAgdHlwZTogImVycm9yIiwKICAgIC8vICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAvLyAgICAgICB9KTsKICAgIC8vICAgICB9CiAgICAvLyAgIH0pOwogICAgLy8gfSwKCiAgICAvLyDotKjmo4DoioLngrnojrflj5botKjmo4DoioLngrnlkI0KICAgIGNoYW5nZU5vZGVDb2RlKHZhbCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgTm9kZV9Db2RlOiAnJywKICAgICAgICBDaGFuZ2VfQ2hlY2tfVHlwZTogW10sCiAgICAgICAgRGlzcGxheV9OYW1lOiAnJywKICAgICAgICBUQ19Vc2VySWQ6ICcnLAogICAgICAgIFpMX1VzZXJJZDogJycsCiAgICAgICAgVENfVXNlcklkczogW10sCiAgICAgICAgWkxfVXNlcklkczogW10sCiAgICAgICAgQ2hlY2tfU3R5bGU6ICcnCiAgICAgIH0KICAgICAgdGhpcy5mb3JtLkRpc3BsYXlfTmFtZSA9IHZhbAogICAgICB0aGlzLmZvcm0uTm9kZV9Db2RlID0gbnVsbAogICAgICAvLyB0aGlzLmZvcm0uQ2hhbmdlX0NoZWNrX1R5cGUgPSBbXTsKICAgICAgLy8gdHJ5IHsKICAgICAgLy8gICB0aGlzLmZvcm0uTm9kZV9Db2RlID0gdGhpcy5RdWFsaXR5Tm9kZUxpc3QuZmluZCgodikgPT4gewogICAgICAvLyAgICAgcmV0dXJuIHYuTmFtZSA9PSB2YWw7CiAgICAgIC8vICAgfSkuSWQ7CiAgICAgIC8vIH0gY2F0Y2ggKGVycikgewogICAgICAvLyAgIHRoaXMuZm9ybS5Ob2RlX0NvZGUgPSBudWxsOwogICAgICAvLyB9CiAgICAgIC8vIGNvbnNvbGUubG9nCiAgICAgIC8vIGxldCBhcnIgPSB7fTsKICAgICAgLy8gYXJyID0gdGhpcy5RdWFsaXR5Tm9kZUxpc3QuZmluZCgodikgPT4gewogICAgICAvLyAgIHJldHVybiB2Lk5hbWUgPT0gdmFsOwogICAgICAvLyB9KTsKICAgICAgLy8gY29uc29sZS5sb2coYXJyKTsKICAgICAgLy8gaWYgKGFycikgewogICAgICAvLyAgIHRoaXMuZm9ybS5DaGVja19TdHlsZSA9IGFyci5DaGVja19TdHlsZSA/IE51bWJlcihhcnIuQ2hlY2tfU3R5bGUpIDogIiI7CiAgICAgIC8vICAgYXJyLklzX05lZWRfVEMgJiYodGhpcy5mb3JtLkNoYW5nZV9DaGVja19UeXBlLnB1c2goMikpCiAgICAgIC8vICAgYXJyLklzX05lZWRfWkwgJiYoIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZS5wdXNoKDEpKTsKICAgICAgLy8gICB0aGlzLlNlbGVjdFR5cGUodGhpcy5mb3JtLkNoYW5nZV9DaGVja19UeXBlKTsKCiAgICAgIC8vICAgdGhpcy5mb3JtLlpMX1VzZXJJZCA9IGFyci5aTF9DaGVja19Vc2VySWQgPyBhcnIuWkxfQ2hlY2tfVXNlcklkOiAiIjsKICAgICAgLy8gICB0aGlzLmZvcm0uVENfVXNlcklkID0gYXJyLlRDX0NoZWNrX1VzZXJJZCA/IGFyci5UQ19DaGVja19Vc2VySWQgOiAiIgogICAgICAvLyAgIGNvbnNvbGUubG9nKHRoaXMuZm9ybS5aTF9Vc2VySWQpCiAgICAgIC8vIH0KICAgIH0sCiAgICBjaGFuZ2VaTFVzZXIodmFsKSB7CiAgICAgIGNvbnNvbGUubG9nKHZhbCkKICAgICAgLy8g6Kej5Yaz5LiL5ouJ5qGG5Zue5pi+6Zeu6aKYCiAgICAgIHRoaXMuJGZvcmNlVXBkYXRlKCkKICAgICAgdGhpcy5mb3JtLlpMX1VzZXJJZCA9ICcnCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdmFsLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgaWYgKGkgPT09IHZhbC5sZW5ndGggLSAxKSB7CiAgICAgICAgICB0aGlzLmZvcm0uWkxfVXNlcklkICs9IHZhbFtpXQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLmZvcm0uWkxfVXNlcklkICs9IHZhbFtpXSArICcsJwogICAgICAgIH0KICAgICAgfQogICAgICBjb25zb2xlLmxvZyh0aGlzLmZvcm0uWkxfVXNlcklkLCAndGhpcy5mb3JtLlpMX1VzZXJJZCAnKQogICAgfSwKICAgIGNoYW5nZVRDVXNlcih2YWwpIHsKICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKQogICAgICB0aGlzLmZvcm0uVENfVXNlcklkID0gJycKICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB2YWwubGVuZ3RoOyBpKyspIHsKICAgICAgICBpZiAoaSA9PT0gdmFsLmxlbmd0aCAtIDEpIHsKICAgICAgICAgIHRoaXMuZm9ybS5UQ19Vc2VySWQgKz0gdmFsW2ldCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuZm9ybS5UQ19Vc2VySWQgKz0gdmFsW2ldICsgJywnCiAgICAgICAgfQogICAgICB9CiAgICAgIC8vIOino+WGs+S4i+aLieahhuWbnuaYvumXrumimAoKICAgICAgY29uc29sZS5sb2codGhpcy5mb3JtLlRDX1VzZXJJZCwgJ3RoaXMuZm9ybS5UQ19Vc2VySWQgJykKICAgIH0sCiAgICBnZXRFbnRpdHlOb2RlKGRhdGEpIHsKICAgICAgR2V0RW50aXR5Tm9kZSh7IGlkOiBkYXRhLklkIH0pLnRoZW4oKHJlcykgPT4gewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICBjb25zb2xlLmxvZyhyZXMuRGF0YSkKICAgICAgICAgIHRoaXMuZm9ybSA9IHJlcy5EYXRhWzBdCiAgICAgICAgICB0aGlzLmZvcm0uQ2hhbmdlX0NoZWNrX1R5cGUgPSBbXQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5DaGVja19UeXBlID09PSAxIHx8IHRoaXMuZm9ybS5DaGVja19UeXBlID09PSAyKSB7CiAgICAgICAgICAgIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZS5wdXNoKHRoaXMuZm9ybS5DaGVja19UeXBlKQogICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmZvcm0uQ2hlY2tfVHlwZSA9PT0gLTEpIHsKICAgICAgICAgICAgdGhpcy5mb3JtLkNoYW5nZV9DaGVja19UeXBlID0gWzEsIDJdCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLmZvcm0uQ2hhbmdlX0NoZWNrX1R5cGUgPSBbXQogICAgICAgICAgfQogICAgICAgICAgdGhpcy5mb3JtLlpMX1VzZXJJZHMgPSB0aGlzLmZvcm0uWkxfVXNlcklkID8gdGhpcy5mb3JtLlpMX1VzZXJJZC5zcGxpdCgnLCcpIDogW10KICAgICAgICAgIHRoaXMuZm9ybS5UQ19Vc2VySWRzID0gdGhpcy5mb3JtLlRDX1VzZXJJZCA/IHRoaXMuZm9ybS5UQ19Vc2VySWQuc3BsaXQoJywnKSA6IFtdCiAgICAgICAgICBjb25zb2xlLmxvZyh0aGlzLmZvcm0uWkxfVXNlcklkcywgdGhpcy5mb3JtLlRDX1VzZXJJZCkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICBoYW5kbGVTdWJtaXQoZm9ybSkgewogICAgICB0aGlzLiRyZWZzW2Zvcm1dLnZhbGlkYXRlKCh2YWxpZCkgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgdGhpcy5hZGRDaGVja05vZGUoKQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICByZXR1cm4gZmFsc2UKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlSW5wdXRGb3JtYXQodmFsdWUsIGRwLCB0eXBlKSB7CiAgICAgIC8vIOWmguaenOi+k+WFpeS4uuepuu+8jOebtOaOpei/lOWbnuepugogICAgICBpZiAodmFsdWUgPT09ICcnIHx8IHZhbHVlID09PSBudWxsIHx8IHZhbHVlID09PSB1bmRlZmluZWQpIHsKICAgICAgICByZXR1cm4gJycKICAgICAgfQoKICAgICAgLy8g6L2s5o2i5Li65a2X56ym5Liy6L+b6KGM5aSE55CGCiAgICAgIGxldCBpbnB1dFZhbHVlID0gU3RyaW5nKHZhbHVlKQoKICAgICAgLy8g56e76Zmk5omA5pyJ6Z2e5pWw5a2X5ZKM6Z2e5bCP5pWw54K555qE5a2X56ym77yI5YyF5ous6LSf5Y+377yJCiAgICAgIGlucHV0VmFsdWUgPSBpbnB1dFZhbHVlLnJlcGxhY2UoL1teMC05Ll0vZywgJycpCgogICAgICAvLyDlpoLmnpzlj6rmmK/ljZXni6znmoTlsI/mlbDngrnvvIzov5Tlm57nqboKICAgICAgaWYgKGlucHV0VmFsdWUgPT09ICcuJykgewogICAgICAgIHJldHVybiAnJwogICAgICB9CgogICAgICAvLyDnoa7kv53lj6rmnInkuIDkuKrlsI/mlbDngrkKICAgICAgY29uc3QgZG90Q291bnQgPSAoaW5wdXRWYWx1ZS5tYXRjaCgvXC4vZykgfHwgW10pLmxlbmd0aAogICAgICBpZiAoZG90Q291bnQgPiAxKSB7CiAgICAgICAgLy8g5aaC5p6c5pyJ5aSa5Liq5bCP5pWw54K577yM5Y+q5L+d55WZ56ys5LiA5LiqCiAgICAgICAgY29uc3QgZmlyc3REb3RJbmRleCA9IGlucHV0VmFsdWUuaW5kZXhPZignLicpCiAgICAgICAgaW5wdXRWYWx1ZSA9IGlucHV0VmFsdWUuc3Vic3RyaW5nKDAsIGZpcnN0RG90SW5kZXggKyAxKSArIGlucHV0VmFsdWUuc3Vic3RyaW5nKGZpcnN0RG90SW5kZXggKyAxKS5yZXBsYWNlKC9cLi9nLCAnJykKICAgICAgfQoKICAgICAgLy8g5qC55o2uIGRwIOWPguaVsOmZkOWItuWwj+aVsOS9jeaVsAogICAgICBpZiAoaW5wdXRWYWx1ZS5pbmNsdWRlcygnLicpICYmIGRwKSB7CiAgICAgICAgY29uc3QgcGFydHMgPSBpbnB1dFZhbHVlLnNwbGl0KCcuJykKICAgICAgICBpZiAocGFydHNbMV0gJiYgcGFydHNbMV0ubGVuZ3RoID4gZHApIHsKICAgICAgICAgIGlucHV0VmFsdWUgPSBwYXJ0c1swXSArICcuJyArIHBhcnRzWzFdLnN1YnN0cmluZygwLCBkcCkKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC8vIOWmguaenOWkhOeQhuWQjuS4uuepuuWtl+espuS4su+8jOi/lOWbnuepugogICAgICBpZiAoaW5wdXRWYWx1ZSA9PT0gJycpIHsKICAgICAgICByZXR1cm4gJycKICAgICAgfQoKICAgICAgLy8g6L2s5o2i5Li65pWw5a2X6L+b6KGM6IyD5Zu05qOA5p+lCiAgICAgIGNvbnN0IG51bVZhbHVlID0gcGFyc2VGbG9hdChpbnB1dFZhbHVlKQoKICAgICAgLy8g5aaC5p6c5LiN5piv5pyJ5pWI5pWw5a2X77yM6L+U5Zue56m6CiAgICAgIGlmIChpc05hTihudW1WYWx1ZSkpIHsKICAgICAgICByZXR1cm4gJycKICAgICAgfQoKICAgICAgLy8g5pyA5bCP5YC86ZmQ5Yi25Li6MAogICAgICBpZiAobnVtVmFsdWUgPCAwKSB7CiAgICAgICAgcmV0dXJuICcwJwogICAgICB9CgogICAgICAvLyDlpoLmnpzmnIkgdHlwZSDlj4LmlbDvvIzpmZDliLbmnIDlpKflgLwKICAgICAgaWYgKHR5cGUgJiYgbnVtVmFsdWUgPiB0eXBlKSB7CiAgICAgICAgLy8g5qC55o2uIGRwIOagvOW8j+WMluacgOWkp+WAvAogICAgICAgIGlmIChkcCkgewogICAgICAgICAgcmV0dXJuIHR5cGUudG9GaXhlZChkcCkucmVwbGFjZSgvXC4/MCskLywgJycpCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHJldHVybiB0eXBlLnRvU3RyaW5nKCkKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC8vIOi/lOWbnuWkhOeQhuWQjueahOWAvAogICAgICByZXR1cm4gaW5wdXRWYWx1ZQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["NodeDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "NodeDialog.vue", "sourceRoot": "src/views/PRO/project-config/project-quality/components/Dialog", "sourcesContent": ["<template>\n  <div>\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"140px\">\n      <el-row>\n        <el-col :span=\"12\">\n          <el-form-item label=\"质检节点\" prop=\"Display_Name\">\n            <el-select\n              v-model=\"form.Display_Name\"\n              :disabled=\"Node_Code_Com\"\n              clearable\n              style=\"width: 100%\"\n              filterable\n              allow-create\n              placeholder=\"请输入质检节点\"\n              @change=\"changeNodeCode\"\n            >\n              <el-option\n                v-for=\"(item, index) in QualityNodeList\"\n                :key=\"index\"\n                :label=\"item.Name\"\n                :value=\"item.Name\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"专检类型\" prop=\"Change_Check_Type\">\n            <el-select\n              v-model=\"form.Change_Check_Type\"\n              style=\"width: 100%\"\n              placeholder=\"请选择专检类型\"\n              multiple\n              :disabled=\"Node_Code_Com\"\n              @change=\"SelectType\"\n              @remove-tag=\"removeType\"\n            >\n              <el-option\n                v-for=\"(item, index) in CheckTypeList\"\n                :key=\"index\"\n                :label=\"item.Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item\n            label=\"质量员\"\n            prop=\"ZL_UserIds\"\n            :rules=\"ZL_UserIds_Rules\"\n          >\n            <el-select\n              v-model=\"form.ZL_UserIds\"\n              filterable\n              clearable\n              multiple\n              style=\"width: 100%\"\n              placeholder=\"请选择质量员\"\n              :disabled=\"\n                Node_Code_Com ||\n                  (form.Change_Check_Type[0] != 1 &&\n                    form.Change_Check_Type.length != 2)\n              \"\n              @change=\"changeZLUser\"\n            >\n              <el-option\n                v-for=\"(item, index) in UserList\"\n                :key=\"index\"\n                :label=\"item.Display_Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item\n            label=\"探伤员\"\n            prop=\"TC_UserIds\"\n            :rules=\"TC_UserIds_Rules\"\n          >\n            <el-select\n              v-model=\"form.TC_UserIds\"\n              filterable\n              clearable\n              multiple\n              style=\"width: 100%\"\n              :disabled=\"\n                Node_Code_Com ||\n                  (form.Change_Check_Type[0] != 2 &&\n                    form.Change_Check_Type.length != 2)\n              \"\n              placeholder=\"请选择探伤员\"\n              @change=\"changeTCUser\"\n            >\n              <el-option\n                v-for=\"(item, index) in UserList\"\n                :key=\"index\"\n                :label=\"item.Display_Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"专检方式\" prop=\"Check_Style\">\n            <el-select\n              v-model=\"form.Check_Style\"\n              clearable\n              :disabled=\"Node_Code_Com\"\n              style=\"width: 100%\"\n              placeholder=\"请选择专检方式\"\n            >\n              <el-option\n                v-for=\"(item, index) in CheckStyleList\"\n                :key=\"index\"\n                :label=\"item.Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"质检流程\">\n            <el-radio-group v-model=\"qualityInspection\">\n              <el-radio :label=\"1\">专检</el-radio>\n            </el-radio-group>\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Check_Style===0 && form.Change_Check_Type && form.Change_Check_Type.includes(1)\" :span=\"12\">\n          <el-form-item label=\"质量要求合格率(%)\" prop=\"Zl_Demand_Spot_Check_Rate\">\n            <el-input v-model=\"form.Zl_Demand_Spot_Check_Rate\" class=\"cs-number-btn-hidden w100\" placeholder=\"请输入\" clearable @input=\"(value) => form.Zl_Demand_Spot_Check_Rate = handleInputFormat(value, 2, 100)\" />\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Check_Style===0 && form.Change_Check_Type && form.Change_Check_Type.includes(1)\" :span=\"12\">\n          <el-form-item label=\"质量要求抽检率(%)\" prop=\"Zl_Requirement_Spot_Check_Rate\">\n            <el-input v-model=\"form.Zl_Requirement_Spot_Check_Rate\" class=\"cs-number-btn-hidden w100\" placeholder=\"请输入\" clearable @input=\"(value) => form.Zl_Requirement_Spot_Check_Rate = handleInputFormat(value, 2, 100)\" />\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Check_Style===0 && form.Change_Check_Type && form.Change_Check_Type.includes(2)\" :span=\"12\">\n          <el-form-item label=\"探伤要求合格率(%)\" prop=\"Ts_Demand_Spot_Check_Rate\">\n            <el-input v-model=\"form.Ts_Demand_Spot_Check_Rate\" class=\"cs-number-btn-hidden w100\" placeholder=\"请输入\" clearable @input=\"(value) => form.Ts_Demand_Spot_Check_Rate = handleInputFormat(value, 2, 100)\" />\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Check_Style===0 && form.Change_Check_Type && form.Change_Check_Type.includes(2)\" :span=\"12\">\n          <el-form-item label=\"探伤要求抽检率(%)\" prop=\"Ts_Requirement_Spot_Check_Rate\">\n            <el-input v-model=\"form.Ts_Requirement_Spot_Check_Rate\" class=\"cs-number-btn-hidden w100\" placeholder=\"请输入\" clearable @input=\"(value) => form.Ts_Requirement_Spot_Check_Rate = handleInputFormat(value, 2, 100)\" />\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Change_Check_Type && form.Change_Check_Type.includes(2)\" :span=\"12\">\n          <el-form-item label=\"探伤要求时间(h)\" prop=\"Ts_Require_Time\">\n            <el-input v-model=\"form.Ts_Require_Time\" placeholder=\"请输入\" @input=\"(value) => form.Ts_Require_Time = handleInputFormat(value, 1)\" />\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item style=\"text-align: right\">\n            <el-button @click=\"$emit('close')\">关 闭</el-button>\n            <el-button\n              type=\"primary\"\n              @click=\"handleSubmit('form')\"\n            >确 定</el-button>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n  </div>\n</template>\n\n<script>\nimport { SaveNode } from '@/api/PRO/factorycheck'\nimport { GetEntityNode } from '@/api/PRO/factorycheck'\n// import { SaveCheckType } from \"@/api/PRO/factorycheck\";\nimport { GetFactoryPeoplelist } from '@/api/PRO/factorycheck'\n// import { GetProcessCodeList } from '@/api/PRO/factorycheck'\nexport default {\n  data() {\n    return {\n      mode: '', // 区分项目和工厂\n      ProjectId: '', // 项目Id\n      Check_Object_Id: '',\n      Bom_Level: '',\n      form: {\n        Node_Code: '',\n        Change_Check_Type: [],\n        Display_Name: '',\n        TC_UserId: '',\n        ZL_UserId: '',\n        Demand_Spot_Check_Rate: undefined,\n        Requirement_Spot_Check_Rate: undefined,\n        TC_UserIds: [],\n        ZL_UserIds: [],\n        Check_Style: '',\n        Ts_Require_Time: '',\n        Zl_Demand_Spot_Check_Rate: undefined,\n        Zl_Requirement_Spot_Check_Rate: undefined,\n        Ts_Demand_Spot_Check_Rate: undefined,\n        Ts_Requirement_Spot_Check_Rate: undefined\n      },\n\n      rules: {\n        Display_Name: [\n          { required: true, message: '请填写完整表单', trigger: 'change' }\n        ],\n        Check_Type: [\n          { required: true, message: '请填写完整表单', trigger: 'change' }\n        ],\n        Change_Check_Type: [\n          { required: true, validator: this.Check_Type_rules, message: '请填写完整表单', trigger: 'change' }\n        ],\n        Check_Style: [\n          { required: true, message: '请填写完整表单', trigger: 'change' }\n        ]\n      },\n      rules_Zl: { required: true, message: '请填写完整表单', trigger: 'bur' },\n      rules_Tc: { required: true, message: '请填写完整表单', trigger: 'bur' },\n      ZL_UserIds_Rules: [\n        { required: true, validator: this.Check_ZL_UserIds, message: '请填写完整表单', trigger: 'change' }\n      ],\n      TC_UserIds_Rules: [\n        { required: true, validator: this.Check_TC_UserIds, message: '请填写完整表单', trigger: 'change' }\n      ],\n      title: '',\n      editInfo: {},\n      QualityNodeList: [{ Name: '入库' }, { Name: '出库' }], // 质检节点列表\n      CheckTypeList: [\n        {\n          Name: '质量',\n          Id: 1\n        },\n        {\n          Name: '探伤',\n          Id: 2\n        }\n      ], // 质检类型\n      UserList: [], // 质量员，探伤人员\n      CheckStyleList: [\n        {\n          Name: '抽检',\n          Id: 0\n        },\n        {\n          Name: '全检',\n          Id: 1\n        }\n      ], // 质检方式\n      qualityInspection: 1\n    }\n  },\n  computed: {\n    Node_Code_Com: function() {\n      if (this.form.Node_Code) {\n        return true\n      } else {\n        return false\n      }\n    }\n  },\n  mounted() {\n    this.getFactoryPeoplelist()\n  },\n  methods: {\n    Check_ZL_UserIds(rule, value, callback) {\n      if (this.form.Change_Check_Type && this.form.Change_Check_Type.includes(1) && this.form.ZL_UserIds.length === 0) {\n        callback(new Error('请填写完整表单'))\n      } else {\n        callback()\n      }\n    },\n    Check_TC_UserIds(rule, value, callback) {\n      if (!this.Node_Code_Com && !(this.form.Change_Check_Type[0] !== 2 && this.form.Change_Check_Type.length !== 2) && this.form.TC_UserIds.length === 0) {\n        callback(new Error('请填写完整表单'))\n      } else {\n        callback()\n      }\n    },\n    Check_Type_rules(rule, value, callback) {\n      if (this.form.Change_Check_Type.length === 0) {\n        callback(new Error('请填写完整表单'))\n      } else {\n        callback()\n      }\n    },\n    SelectType(item) {\n      this.$forceUpdate()\n      this.form.Change_Check_Type = item\n      if (item.length === 1) {\n        this.form.Check_Type = item[0]\n      } else if (item.length === 2) {\n        this.form.Check_Type = -1\n      }\n\n      if (!item.includes(1)) {\n        this.form.ZL_UserId = ''\n        this.form.ZL_UserIds = []\n      }\n      if (!item.includes(2)) {\n        this.form.TC_UserId = ''\n        this.form.TC_UserIds = []\n      }\n      console.log(this.form.Change_Check_Type)\n    },\n    removeType(item) {\n      console.log(item, 'b')\n      // if (item == 1) {\n      //   this.form.ZL_UserId = \"\";\n      // } else if (item == 2) {\n      //   this.form.TC_UserId = \"\";\n      // }\n    },\n    clearType(val) {\n      console.log(val)\n      this.form.ZL_UserId = ''\n      this.form.TC_UserId = ''\n      this.form.ZL_UserIds = []\n      this.form.TC_UserIds = []\n    },\n    init(title, checkType, data, sysProjectId) {\n      this.Check_Object_Id = checkType.Id\n      this.Bom_Level = checkType.Code\n      this.title = title\n      if (title === '编辑') {\n        console.log(data)\n        this.form.Id = data.Id\n        this.getEntityNode(data)\n      }\n      this.getCheckNode()\n    },\n    async addCheckNode() {\n      const { Zl_Demand_Spot_Check_Rate, Zl_Requirement_Spot_Check_Rate, Ts_Demand_Spot_Check_Rate, Ts_Requirement_Spot_Check_Rate, ...others } = this.form\n      const submit = {\n        ...others,\n        Check_Object_Id: this.Check_Object_Id,\n        Bom_Level: this.Bom_Level\n      }\n      if (this.form.Check_Style === 0) { // 抽检\n        submit.Zl_Demand_Spot_Check_Rate = Zl_Demand_Spot_Check_Rate\n        submit.Zl_Requirement_Spot_Check_Rate = Zl_Requirement_Spot_Check_Rate\n        submit.Ts_Demand_Spot_Check_Rate = Ts_Demand_Spot_Check_Rate\n        submit.Ts_Requirement_Spot_Check_Rate = Ts_Requirement_Spot_Check_Rate\n      }\n      await SaveNode(submit).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            type: 'success',\n            message: '保存成功'\n          })\n          this.$emit('refresh')\n          this.$emit('close')\n          this.dialogData = {}\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    getFactoryPeoplelist() {\n      GetFactoryPeoplelist().then((res) => {\n        if (res.IsSucceed) {\n          console.log(res.Data)\n          this.UserList = res.Data\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    // 判断是工厂还是项目获取质检节点\n    getCheckNode() {\n      const Platform =\n        localStorage.getItem('Platform') || localStorage.getItem('CurPlatform')\n      if (Platform === '2') {\n        this.mode = 'factory'\n        // this.getFactoryNode();\n      }\n      // 获取项目/工厂id\n      this.ProjectId =\n        this.mode === 'factory'\n          ? localStorage.getItem('CurReferenceId')\n          : this.ProjectId\n    },\n    // 如果是工厂获取质检节点\n    // getFactoryNode() {\n    //   GetProcessCodeList({sys_workobject_id:this.Check_Object_Id}).then((res) => {\n    //     if (res.IsSucceed) {\n    //       let CheckJson = res.Data;\n    //       CheckJson.push({ Name: \"入库\" }, { Name: \"出库\" });\n    //       console.log(CheckJson);\n    //       this.QualityNodeList = CheckJson;\n    //       console.log(this.QualityNodeList);\n    //     } else {\n    //       this.$message({\n    //         type: \"error\",\n    //         message: res.Message,\n    //       });\n    //     }\n    //   });\n    // },\n\n    // 质检节点获取质检节点名\n    changeNodeCode(val) {\n      this.form = {\n        Node_Code: '',\n        Change_Check_Type: [],\n        Display_Name: '',\n        TC_UserId: '',\n        ZL_UserId: '',\n        TC_UserIds: [],\n        ZL_UserIds: [],\n        Check_Style: ''\n      }\n      this.form.Display_Name = val\n      this.form.Node_Code = null\n      // this.form.Change_Check_Type = [];\n      // try {\n      //   this.form.Node_Code = this.QualityNodeList.find((v) => {\n      //     return v.Name == val;\n      //   }).Id;\n      // } catch (err) {\n      //   this.form.Node_Code = null;\n      // }\n      // console.log\n      // let arr = {};\n      // arr = this.QualityNodeList.find((v) => {\n      //   return v.Name == val;\n      // });\n      // console.log(arr);\n      // if (arr) {\n      //   this.form.Check_Style = arr.Check_Style ? Number(arr.Check_Style) : \"\";\n      //   arr.Is_Need_TC &&(this.form.Change_Check_Type.push(2))\n      //   arr.Is_Need_ZL &&( this.form.Change_Check_Type.push(1));\n      //   this.SelectType(this.form.Change_Check_Type);\n\n      //   this.form.ZL_UserId = arr.ZL_Check_UserId ? arr.ZL_Check_UserId: \"\";\n      //   this.form.TC_UserId = arr.TC_Check_UserId ? arr.TC_Check_UserId : \"\"\n      //   console.log(this.form.ZL_UserId)\n      // }\n    },\n    changeZLUser(val) {\n      console.log(val)\n      // 解决下拉框回显问题\n      this.$forceUpdate()\n      this.form.ZL_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i === val.length - 1) {\n          this.form.ZL_UserId += val[i]\n        } else {\n          this.form.ZL_UserId += val[i] + ','\n        }\n      }\n      console.log(this.form.ZL_UserId, 'this.form.ZL_UserId ')\n    },\n    changeTCUser(val) {\n      this.$forceUpdate()\n      this.form.TC_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i === val.length - 1) {\n          this.form.TC_UserId += val[i]\n        } else {\n          this.form.TC_UserId += val[i] + ','\n        }\n      }\n      // 解决下拉框回显问题\n\n      console.log(this.form.TC_UserId, 'this.form.TC_UserId ')\n    },\n    getEntityNode(data) {\n      GetEntityNode({ id: data.Id }).then((res) => {\n        if (res.IsSucceed) {\n          console.log(res.Data)\n          this.form = res.Data[0]\n          this.form.Change_Check_Type = []\n          if (this.form.Check_Type === 1 || this.form.Check_Type === 2) {\n            this.form.Change_Check_Type.push(this.form.Check_Type)\n          } else if (this.form.Check_Type === -1) {\n            this.form.Change_Check_Type = [1, 2]\n          } else {\n            this.form.Change_Check_Type = []\n          }\n          this.form.ZL_UserIds = this.form.ZL_UserId ? this.form.ZL_UserId.split(',') : []\n          this.form.TC_UserIds = this.form.TC_UserId ? this.form.TC_UserId.split(',') : []\n          console.log(this.form.ZL_UserIds, this.form.TC_UserId)\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    handleSubmit(form) {\n      this.$refs[form].validate((valid) => {\n        if (valid) {\n          this.addCheckNode()\n        } else {\n          return false\n        }\n      })\n    },\n    handleInputFormat(value, dp, type) {\n      // 如果输入为空，直接返回空\n      if (value === '' || value === null || value === undefined) {\n        return ''\n      }\n\n      // 转换为字符串进行处理\n      let inputValue = String(value)\n\n      // 移除所有非数字和非小数点的字符（包括负号）\n      inputValue = inputValue.replace(/[^0-9.]/g, '')\n\n      // 如果只是单独的小数点，返回空\n      if (inputValue === '.') {\n        return ''\n      }\n\n      // 确保只有一个小数点\n      const dotCount = (inputValue.match(/\\./g) || []).length\n      if (dotCount > 1) {\n        // 如果有多个小数点，只保留第一个\n        const firstDotIndex = inputValue.indexOf('.')\n        inputValue = inputValue.substring(0, firstDotIndex + 1) + inputValue.substring(firstDotIndex + 1).replace(/\\./g, '')\n      }\n\n      // 根据 dp 参数限制小数位数\n      if (inputValue.includes('.') && dp) {\n        const parts = inputValue.split('.')\n        if (parts[1] && parts[1].length > dp) {\n          inputValue = parts[0] + '.' + parts[1].substring(0, dp)\n        }\n      }\n\n      // 如果处理后为空字符串，返回空\n      if (inputValue === '') {\n        return ''\n      }\n\n      // 转换为数字进行范围检查\n      const numValue = parseFloat(inputValue)\n\n      // 如果不是有效数字，返回空\n      if (isNaN(numValue)) {\n        return ''\n      }\n\n      // 最小值限制为0\n      if (numValue < 0) {\n        return '0'\n      }\n\n      // 如果有 type 参数，限制最大值\n      if (type && numValue > type) {\n        // 根据 dp 格式化最大值\n        if (dp) {\n          return type.toFixed(dp).replace(/\\.?0+$/, '')\n        } else {\n          return type.toString()\n        }\n      }\n\n      // 返回处理后的值\n      return inputValue\n    }\n  }\n}\n</script>\n\n<style scoped></style>\n"]}]}
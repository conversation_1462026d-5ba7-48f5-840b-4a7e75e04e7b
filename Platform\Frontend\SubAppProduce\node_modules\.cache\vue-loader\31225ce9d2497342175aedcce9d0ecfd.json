{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\NodeDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\NodeDialog.vue", "mtime": 1758159951628}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IFNhdmVOb2RlIH0gZnJvbSAnQC9hcGkvUFJPL2ZhY3RvcnljaGVjaycKaW1wb3J0IHsgR2V0RW50aXR5Tm9kZSB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snCi8vIGltcG9ydCB7IFNhdmVDaGVja1R5cGUgfSBmcm9tICJAL2FwaS9QUk8vZmFjdG9yeWNoZWNrIjsKaW1wb3J0IHsgR2V0RmFjdG9yeVBlb3BsZWxpc3QgfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJwovLyBpbXBvcnQgeyBHZXRQcm9jZXNzQ29kZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJwpleHBvcnQgZGVmYXVsdCB7CiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIG1vZGU6ICcnLCAvLyDljLrliIbpobnnm67lkozlt6XljoIKICAgICAgUHJvamVjdElkOiAnJywgLy8g6aG555uuSWQKICAgICAgQ2hlY2tfT2JqZWN0X0lkOiAnJywKICAgICAgQm9tX0xldmVsOiAnJywKICAgICAgZm9ybTogewogICAgICAgIE5vZGVfQ29kZTogJycsCiAgICAgICAgQ2hhbmdlX0NoZWNrX1R5cGU6IFtdLAogICAgICAgIERpc3BsYXlfTmFtZTogJycsCiAgICAgICAgVENfVXNlcklkOiAnJywKICAgICAgICBaTF9Vc2VySWQ6ICcnLAogICAgICAgIERlbWFuZF9TcG90X0NoZWNrX1JhdGU6IHVuZGVmaW5lZCwKICAgICAgICBSZXF1aXJlbWVudF9TcG90X0NoZWNrX1JhdGU6IHVuZGVmaW5lZCwKICAgICAgICBUQ19Vc2VySWRzOiBbXSwKICAgICAgICBaTF9Vc2VySWRzOiBbXSwKICAgICAgICBDaGVja19TdHlsZTogJycsCiAgICAgICAgVHNfUmVxdWlyZV9UaW1lOiAnJywKICAgICAgICBabF9EZW1hbmRfU3BvdF9DaGVja19SYXRlOiB1bmRlZmluZWQsCiAgICAgICAgWmxfUmVxdWlyZW1lbnRfU3BvdF9DaGVja19SYXRlOiB1bmRlZmluZWQsCiAgICAgICAgVHNfRGVtYW5kX1Nwb3RfQ2hlY2tfUmF0ZTogdW5kZWZpbmVkLAogICAgICAgIFRzX1JlcXVpcmVtZW50X1Nwb3RfQ2hlY2tfUmF0ZTogdW5kZWZpbmVkCiAgICAgIH0sCgogICAgICBydWxlczogewogICAgICAgIERpc3BsYXlfTmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOihqOWNlScsIHRyaWdnZXI6ICdjaGFuZ2UnIH0KICAgICAgICBdLAogICAgICAgIENoZWNrX1R5cGU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgICAgXSwKICAgICAgICBDaGFuZ2VfQ2hlY2tfVHlwZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdmFsaWRhdG9yOiB0aGlzLkNoZWNrX1R5cGVfcnVsZXMsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgICAgXSwKICAgICAgICBDaGVja19TdHlsZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOihqOWNlScsIHRyaWdnZXI6ICdjaGFuZ2UnIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIHJ1bGVzX1psOiB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+35aGr5YaZ5a6M5pW06KGo5Y2VJywgdHJpZ2dlcjogJ2J1cicgfSwKICAgICAgcnVsZXNfVGM6IHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnYnVyJyB9LAogICAgICBaTF9Vc2VySWRzX1J1bGVzOiBbCiAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdmFsaWRhdG9yOiB0aGlzLkNoZWNrX1pMX1VzZXJJZHMsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgIF0sCiAgICAgIFRDX1VzZXJJZHNfUnVsZXM6IFsKICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB2YWxpZGF0b3I6IHRoaXMuQ2hlY2tfVENfVXNlcklkcywgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOihqOWNlScsIHRyaWdnZXI6ICdjaGFuZ2UnIH0KICAgICAgXSwKICAgICAgdGl0bGU6ICcnLAogICAgICBlZGl0SW5mbzoge30sCiAgICAgIFF1YWxpdHlOb2RlTGlzdDogW3sgTmFtZTogJ+WFpeW6kycgfSwgeyBOYW1lOiAn5Ye65bqTJyB9XSwgLy8g6LSo5qOA6IqC54K55YiX6KGoCiAgICAgIENoZWNrVHlwZUxpc3Q6IFsKICAgICAgICB7CiAgICAgICAgICBOYW1lOiAn6LSo6YePJywKICAgICAgICAgIElkOiAxCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBOYW1lOiAn5o6i5LykJywKICAgICAgICAgIElkOiAyCiAgICAgICAgfQogICAgICBdLCAvLyDotKjmo4DnsbvlnosKICAgICAgVXNlckxpc3Q6IFtdLCAvLyDotKjph4/lkZjvvIzmjqLkvKTkurrlkZgKICAgICAgQ2hlY2tTdHlsZUxpc3Q6IFsKICAgICAgICB7CiAgICAgICAgICBOYW1lOiAn5oq95qOAJywKICAgICAgICAgIElkOiAwCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBOYW1lOiAn5YWo5qOAJywKICAgICAgICAgIElkOiAxCiAgICAgICAgfQogICAgICBdLCAvLyDotKjmo4DmlrnlvI8KICAgICAgcXVhbGl0eUluc3BlY3Rpb246IDEsCiAgICAgIHN5c1Byb2plY3RJZDogJycKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICBOb2RlX0NvZGVfQ29tOiBmdW5jdGlvbigpIHsKICAgICAgaWYgKHRoaXMuZm9ybS5Ob2RlX0NvZGUpIHsKICAgICAgICByZXR1cm4gdHJ1ZQogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiBmYWxzZQogICAgICB9CiAgICB9CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5nZXRGYWN0b3J5UGVvcGxlbGlzdCgpCiAgfSwKICBtZXRob2RzOiB7CiAgICBDaGVja19aTF9Vc2VySWRzKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgewogICAgICBpZiAodGhpcy5mb3JtLkNoYW5nZV9DaGVja19UeXBlICYmIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZS5pbmNsdWRlcygxKSAmJiB0aGlzLmZvcm0uWkxfVXNlcklkcy5sZW5ndGggPT09IDApIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+ivt+Whq+WGmeWujOaVtOihqOWNlScpKQogICAgICB9IGVsc2UgewogICAgICAgIGNhbGxiYWNrKCkKICAgICAgfQogICAgfSwKICAgIENoZWNrX1RDX1VzZXJJZHMocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7CiAgICAgIGlmICghdGhpcy5Ob2RlX0NvZGVfQ29tICYmICEodGhpcy5mb3JtLkNoYW5nZV9DaGVja19UeXBlWzBdICE9PSAyICYmIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZS5sZW5ndGggIT09IDIpICYmIHRoaXMuZm9ybS5UQ19Vc2VySWRzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6K+35aGr5YaZ5a6M5pW06KGo5Y2VJykpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgY2FsbGJhY2soKQogICAgICB9CiAgICB9LAogICAgQ2hlY2tfVHlwZV9ydWxlcyhydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZS5sZW5ndGggPT09IDApIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+ivt+Whq+WGmeWujOaVtOihqOWNlScpKQogICAgICB9IGVsc2UgewogICAgICAgIGNhbGxiYWNrKCkKICAgICAgfQogICAgfSwKICAgIFNlbGVjdFR5cGUoaXRlbSkgewogICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpCiAgICAgIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZSA9IGl0ZW0KICAgICAgaWYgKGl0ZW0ubGVuZ3RoID09PSAxKSB7CiAgICAgICAgdGhpcy5mb3JtLkNoZWNrX1R5cGUgPSBpdGVtWzBdCiAgICAgIH0gZWxzZSBpZiAoaXRlbS5sZW5ndGggPT09IDIpIHsKICAgICAgICB0aGlzLmZvcm0uQ2hlY2tfVHlwZSA9IC0xCiAgICAgIH0KCiAgICAgIGlmICghaXRlbS5pbmNsdWRlcygxKSkgewogICAgICAgIHRoaXMuZm9ybS5aTF9Vc2VySWQgPSAnJwogICAgICAgIHRoaXMuZm9ybS5aTF9Vc2VySWRzID0gW10KICAgICAgfQogICAgICBpZiAoIWl0ZW0uaW5jbHVkZXMoMikpIHsKICAgICAgICB0aGlzLmZvcm0uVENfVXNlcklkID0gJycKICAgICAgICB0aGlzLmZvcm0uVENfVXNlcklkcyA9IFtdCiAgICAgIH0KICAgICAgY29uc29sZS5sb2codGhpcy5mb3JtLkNoYW5nZV9DaGVja19UeXBlKQogICAgfSwKICAgIHJlbW92ZVR5cGUoaXRlbSkgewogICAgICBjb25zb2xlLmxvZyhpdGVtLCAnYicpCiAgICAgIC8vIGlmIChpdGVtID09IDEpIHsKICAgICAgLy8gICB0aGlzLmZvcm0uWkxfVXNlcklkID0gIiI7CiAgICAgIC8vIH0gZWxzZSBpZiAoaXRlbSA9PSAyKSB7CiAgICAgIC8vICAgdGhpcy5mb3JtLlRDX1VzZXJJZCA9ICIiOwogICAgICAvLyB9CiAgICB9LAogICAgY2xlYXJUeXBlKHZhbCkgewogICAgICBjb25zb2xlLmxvZyh2YWwpCiAgICAgIHRoaXMuZm9ybS5aTF9Vc2VySWQgPSAnJwogICAgICB0aGlzLmZvcm0uVENfVXNlcklkID0gJycKICAgICAgdGhpcy5mb3JtLlpMX1VzZXJJZHMgPSBbXQogICAgICB0aGlzLmZvcm0uVENfVXNlcklkcyA9IFtdCiAgICB9LAogICAgaW5pdCh0aXRsZSwgY2hlY2tUeXBlLCBkYXRhLCBzeXNQcm9qZWN0SWQpIHsKICAgICAgdGhpcy5zeXNQcm9qZWN0SWQgPSBzeXNQcm9qZWN0SWQKICAgICAgdGhpcy5DaGVja19PYmplY3RfSWQgPSBjaGVja1R5cGUuSWQKICAgICAgdGhpcy5Cb21fTGV2ZWwgPSBjaGVja1R5cGUuQ29kZQogICAgICB0aGlzLnRpdGxlID0gdGl0bGUKICAgICAgaWYgKHRpdGxlID09PSAn57yW6L6RJykgewogICAgICAgIGNvbnNvbGUubG9nKGRhdGEpCiAgICAgICAgdGhpcy5mb3JtLklkID0gZGF0YS5JZAogICAgICAgIHRoaXMuZ2V0RW50aXR5Tm9kZShkYXRhKQogICAgICB9CiAgICAgIHRoaXMuZ2V0Q2hlY2tOb2RlKCkKICAgIH0sCiAgICBhc3luYyBhZGRDaGVja05vZGUoKSB7CiAgICAgIGNvbnN0IHsgWmxfRGVtYW5kX1Nwb3RfQ2hlY2tfUmF0ZSwgWmxfUmVxdWlyZW1lbnRfU3BvdF9DaGVja19SYXRlLCBUc19EZW1hbmRfU3BvdF9DaGVja19SYXRlLCBUc19SZXF1aXJlbWVudF9TcG90X0NoZWNrX1JhdGUsIC4uLm90aGVycyB9ID0gdGhpcy5mb3JtCiAgICAgIGNvbnN0IHN1Ym1pdCA9IHsKICAgICAgICAuLi5vdGhlcnMsCiAgICAgICAgQ2hlY2tfT2JqZWN0X0lkOiB0aGlzLkNoZWNrX09iamVjdF9JZCwKICAgICAgICBCb21fTGV2ZWw6IHRoaXMuQm9tX0xldmVsLAogICAgICAgIHN5c1Byb2plY3RJZDogdGhpcy5zeXNQcm9qZWN0SWQKICAgICAgfQogICAgICBpZiAodGhpcy5mb3JtLkNoZWNrX1N0eWxlID09PSAwKSB7IC8vIOaKveajgAogICAgICAgIHN1Ym1pdC5abF9EZW1hbmRfU3BvdF9DaGVja19SYXRlID0gWmxfRGVtYW5kX1Nwb3RfQ2hlY2tfUmF0ZQogICAgICAgIHN1Ym1pdC5abF9SZXF1aXJlbWVudF9TcG90X0NoZWNrX1JhdGUgPSBabF9SZXF1aXJlbWVudF9TcG90X0NoZWNrX1JhdGUKICAgICAgICBzdWJtaXQuVHNfRGVtYW5kX1Nwb3RfQ2hlY2tfUmF0ZSA9IFRzX0RlbWFuZF9TcG90X0NoZWNrX1JhdGUKICAgICAgICBzdWJtaXQuVHNfUmVxdWlyZW1lbnRfU3BvdF9DaGVja19SYXRlID0gVHNfUmVxdWlyZW1lbnRfU3BvdF9DaGVja19SYXRlCiAgICAgIH0KICAgICAgYXdhaXQgU2F2ZU5vZGUoc3VibWl0KS50aGVuKChyZXMpID0+IHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgICAgbWVzc2FnZTogJ+S/neWtmOaIkOWKnycKICAgICAgICAgIH0pCiAgICAgICAgICB0aGlzLiRlbWl0KCdyZWZyZXNoJykKICAgICAgICAgIHRoaXMuJGVtaXQoJ2Nsb3NlJykKICAgICAgICAgIHRoaXMuZGlhbG9nRGF0YSA9IHt9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZQogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgZ2V0RmFjdG9yeVBlb3BsZWxpc3QoKSB7CiAgICAgIEdldEZhY3RvcnlQZW9wbGVsaXN0KCkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIGNvbnNvbGUubG9nKHJlcy5EYXRhKQogICAgICAgICAgdGhpcy5Vc2VyTGlzdCA9IHJlcy5EYXRhCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZQogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgLy8g5Yik5pat5piv5bel5Y6C6L+Y5piv6aG555uu6I635Y+W6LSo5qOA6IqC54K5CiAgICBnZXRDaGVja05vZGUoKSB7CiAgICAgIGNvbnN0IFBsYXRmb3JtID0KICAgICAgICBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnUGxhdGZvcm0nKSB8fCBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnQ3VyUGxhdGZvcm0nKQogICAgICBpZiAoUGxhdGZvcm0gPT09ICcyJykgewogICAgICAgIHRoaXMubW9kZSA9ICdmYWN0b3J5JwogICAgICAgIC8vIHRoaXMuZ2V0RmFjdG9yeU5vZGUoKTsKICAgICAgfQogICAgICAvLyDojrflj5bpobnnm64v5bel5Y6CaWQKICAgICAgdGhpcy5Qcm9qZWN0SWQgPQogICAgICAgIHRoaXMubW9kZSA9PT0gJ2ZhY3RvcnknCiAgICAgICAgICA/IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdDdXJSZWZlcmVuY2VJZCcpCiAgICAgICAgICA6IHRoaXMuUHJvamVjdElkCiAgICB9LAogICAgLy8g5aaC5p6c5piv5bel5Y6C6I635Y+W6LSo5qOA6IqC54K5CiAgICAvLyBnZXRGYWN0b3J5Tm9kZSgpIHsKICAgIC8vICAgR2V0UHJvY2Vzc0NvZGVMaXN0KHtzeXNfd29ya29iamVjdF9pZDp0aGlzLkNoZWNrX09iamVjdF9JZH0pLnRoZW4oKHJlcykgPT4gewogICAgLy8gICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAvLyAgICAgICBsZXQgQ2hlY2tKc29uID0gcmVzLkRhdGE7CiAgICAvLyAgICAgICBDaGVja0pzb24ucHVzaCh7IE5hbWU6ICLlhaXlupMiIH0sIHsgTmFtZTogIuWHuuW6kyIgfSk7CiAgICAvLyAgICAgICBjb25zb2xlLmxvZyhDaGVja0pzb24pOwogICAgLy8gICAgICAgdGhpcy5RdWFsaXR5Tm9kZUxpc3QgPSBDaGVja0pzb247CiAgICAvLyAgICAgICBjb25zb2xlLmxvZyh0aGlzLlF1YWxpdHlOb2RlTGlzdCk7CiAgICAvLyAgICAgfSBlbHNlIHsKICAgIC8vICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgLy8gICAgICAgICB0eXBlOiAiZXJyb3IiLAogICAgLy8gICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgIC8vICAgICAgIH0pOwogICAgLy8gICAgIH0KICAgIC8vICAgfSk7CiAgICAvLyB9LAoKICAgIC8vIOi0qOajgOiKgueCueiOt+WPlui0qOajgOiKgueCueWQjQogICAgY2hhbmdlTm9kZUNvZGUodmFsKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBOb2RlX0NvZGU6ICcnLAogICAgICAgIENoYW5nZV9DaGVja19UeXBlOiBbXSwKICAgICAgICBEaXNwbGF5X05hbWU6ICcnLAogICAgICAgIFRDX1VzZXJJZDogJycsCiAgICAgICAgWkxfVXNlcklkOiAnJywKICAgICAgICBUQ19Vc2VySWRzOiBbXSwKICAgICAgICBaTF9Vc2VySWRzOiBbXSwKICAgICAgICBDaGVja19TdHlsZTogJycKICAgICAgfQogICAgICB0aGlzLmZvcm0uRGlzcGxheV9OYW1lID0gdmFsCiAgICAgIHRoaXMuZm9ybS5Ob2RlX0NvZGUgPSBudWxsCiAgICAgIC8vIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZSA9IFtdOwogICAgICAvLyB0cnkgewogICAgICAvLyAgIHRoaXMuZm9ybS5Ob2RlX0NvZGUgPSB0aGlzLlF1YWxpdHlOb2RlTGlzdC5maW5kKCh2KSA9PiB7CiAgICAgIC8vICAgICByZXR1cm4gdi5OYW1lID09IHZhbDsKICAgICAgLy8gICB9KS5JZDsKICAgICAgLy8gfSBjYXRjaCAoZXJyKSB7CiAgICAgIC8vICAgdGhpcy5mb3JtLk5vZGVfQ29kZSA9IG51bGw7CiAgICAgIC8vIH0KICAgICAgLy8gY29uc29sZS5sb2cKICAgICAgLy8gbGV0IGFyciA9IHt9OwogICAgICAvLyBhcnIgPSB0aGlzLlF1YWxpdHlOb2RlTGlzdC5maW5kKCh2KSA9PiB7CiAgICAgIC8vICAgcmV0dXJuIHYuTmFtZSA9PSB2YWw7CiAgICAgIC8vIH0pOwogICAgICAvLyBjb25zb2xlLmxvZyhhcnIpOwogICAgICAvLyBpZiAoYXJyKSB7CiAgICAgIC8vICAgdGhpcy5mb3JtLkNoZWNrX1N0eWxlID0gYXJyLkNoZWNrX1N0eWxlID8gTnVtYmVyKGFyci5DaGVja19TdHlsZSkgOiAiIjsKICAgICAgLy8gICBhcnIuSXNfTmVlZF9UQyAmJih0aGlzLmZvcm0uQ2hhbmdlX0NoZWNrX1R5cGUucHVzaCgyKSkKICAgICAgLy8gICBhcnIuSXNfTmVlZF9aTCAmJiggdGhpcy5mb3JtLkNoYW5nZV9DaGVja19UeXBlLnB1c2goMSkpOwogICAgICAvLyAgIHRoaXMuU2VsZWN0VHlwZSh0aGlzLmZvcm0uQ2hhbmdlX0NoZWNrX1R5cGUpOwoKICAgICAgLy8gICB0aGlzLmZvcm0uWkxfVXNlcklkID0gYXJyLlpMX0NoZWNrX1VzZXJJZCA/IGFyci5aTF9DaGVja19Vc2VySWQ6ICIiOwogICAgICAvLyAgIHRoaXMuZm9ybS5UQ19Vc2VySWQgPSBhcnIuVENfQ2hlY2tfVXNlcklkID8gYXJyLlRDX0NoZWNrX1VzZXJJZCA6ICIiCiAgICAgIC8vICAgY29uc29sZS5sb2codGhpcy5mb3JtLlpMX1VzZXJJZCkKICAgICAgLy8gfQogICAgfSwKICAgIGNoYW5nZVpMVXNlcih2YWwpIHsKICAgICAgY29uc29sZS5sb2codmFsKQogICAgICAvLyDop6PlhrPkuIvmi4nmoYblm57mmL7pl67popgKICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKQogICAgICB0aGlzLmZvcm0uWkxfVXNlcklkID0gJycKICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB2YWwubGVuZ3RoOyBpKyspIHsKICAgICAgICBpZiAoaSA9PT0gdmFsLmxlbmd0aCAtIDEpIHsKICAgICAgICAgIHRoaXMuZm9ybS5aTF9Vc2VySWQgKz0gdmFsW2ldCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuZm9ybS5aTF9Vc2VySWQgKz0gdmFsW2ldICsgJywnCiAgICAgICAgfQogICAgICB9CiAgICAgIGNvbnNvbGUubG9nKHRoaXMuZm9ybS5aTF9Vc2VySWQsICd0aGlzLmZvcm0uWkxfVXNlcklkICcpCiAgICB9LAogICAgY2hhbmdlVENVc2VyKHZhbCkgewogICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpCiAgICAgIHRoaXMuZm9ybS5UQ19Vc2VySWQgPSAnJwogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHZhbC5sZW5ndGg7IGkrKykgewogICAgICAgIGlmIChpID09PSB2YWwubGVuZ3RoIC0gMSkgewogICAgICAgICAgdGhpcy5mb3JtLlRDX1VzZXJJZCArPSB2YWxbaV0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5mb3JtLlRDX1VzZXJJZCArPSB2YWxbaV0gKyAnLCcKICAgICAgICB9CiAgICAgIH0KICAgICAgLy8g6Kej5Yaz5LiL5ouJ5qGG5Zue5pi+6Zeu6aKYCgogICAgICBjb25zb2xlLmxvZyh0aGlzLmZvcm0uVENfVXNlcklkLCAndGhpcy5mb3JtLlRDX1VzZXJJZCAnKQogICAgfSwKICAgIGdldEVudGl0eU5vZGUoZGF0YSkgewogICAgICBHZXRFbnRpdHlOb2RlKHsgaWQ6IGRhdGEuSWQgfSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIGNvbnNvbGUubG9nKHJlcy5EYXRhKQogICAgICAgICAgdGhpcy5mb3JtID0gcmVzLkRhdGFbMF0KICAgICAgICAgIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZSA9IFtdCiAgICAgICAgICBpZiAodGhpcy5mb3JtLkNoZWNrX1R5cGUgPT09IDEgfHwgdGhpcy5mb3JtLkNoZWNrX1R5cGUgPT09IDIpIHsKICAgICAgICAgICAgdGhpcy5mb3JtLkNoYW5nZV9DaGVja19UeXBlLnB1c2godGhpcy5mb3JtLkNoZWNrX1R5cGUpCiAgICAgICAgICB9IGVsc2UgaWYgKHRoaXMuZm9ybS5DaGVja19UeXBlID09PSAtMSkgewogICAgICAgICAgICB0aGlzLmZvcm0uQ2hhbmdlX0NoZWNrX1R5cGUgPSBbMSwgMl0KICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZSA9IFtdCiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLmZvcm0uWkxfVXNlcklkcyA9IHRoaXMuZm9ybS5aTF9Vc2VySWQgPyB0aGlzLmZvcm0uWkxfVXNlcklkLnNwbGl0KCcsJykgOiBbXQogICAgICAgICAgdGhpcy5mb3JtLlRDX1VzZXJJZHMgPSB0aGlzLmZvcm0uVENfVXNlcklkID8gdGhpcy5mb3JtLlRDX1VzZXJJZC5zcGxpdCgnLCcpIDogW10KICAgICAgICAgIGNvbnNvbGUubG9nKHRoaXMuZm9ybS5aTF9Vc2VySWRzLCB0aGlzLmZvcm0uVENfVXNlcklkKQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZVN1Ym1pdChmb3JtKSB7CiAgICAgIHRoaXMuJHJlZnNbZm9ybV0udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB0aGlzLmFkZENoZWNrTm9kZSgpCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHJldHVybiBmYWxzZQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICBoYW5kbGVJbnB1dEZvcm1hdCh2YWx1ZSwgZHAsIHR5cGUpIHsKICAgICAgLy8g5aaC5p6c6L6T5YWl5Li656m677yM55u05o6l6L+U5Zue56m6CiAgICAgIGlmICh2YWx1ZSA9PT0gJycgfHwgdmFsdWUgPT09IG51bGwgfHwgdmFsdWUgPT09IHVuZGVmaW5lZCkgewogICAgICAgIHJldHVybiAnJwogICAgICB9CgogICAgICAvLyDovazmjaLkuLrlrZfnrKbkuLLov5vooYzlpITnkIYKICAgICAgbGV0IGlucHV0VmFsdWUgPSBTdHJpbmcodmFsdWUpCgogICAgICAvLyDnp7vpmaTmiYDmnInpnZ7mlbDlrZflkozpnZ7lsI/mlbDngrnnmoTlrZfnrKbvvIjljIXmi6zotJ/lj7fvvIkKICAgICAgaW5wdXRWYWx1ZSA9IGlucHV0VmFsdWUucmVwbGFjZSgvW14wLTkuXS9nLCAnJykKCiAgICAgIC8vIOWmguaenOWPquaYr+WNleeLrOeahOWwj+aVsOeCue+8jOi/lOWbnuepugogICAgICBpZiAoaW5wdXRWYWx1ZSA9PT0gJy4nKSB7CiAgICAgICAgcmV0dXJuICcnCiAgICAgIH0KCiAgICAgIC8vIOehruS/neWPquacieS4gOS4quWwj+aVsOeCuQogICAgICBjb25zdCBkb3RDb3VudCA9IChpbnB1dFZhbHVlLm1hdGNoKC9cLi9nKSB8fCBbXSkubGVuZ3RoCiAgICAgIGlmIChkb3RDb3VudCA+IDEpIHsKICAgICAgICAvLyDlpoLmnpzmnInlpJrkuKrlsI/mlbDngrnvvIzlj6rkv53nlZnnrKzkuIDkuKoKICAgICAgICBjb25zdCBmaXJzdERvdEluZGV4ID0gaW5wdXRWYWx1ZS5pbmRleE9mKCcuJykKICAgICAgICBpbnB1dFZhbHVlID0gaW5wdXRWYWx1ZS5zdWJzdHJpbmcoMCwgZmlyc3REb3RJbmRleCArIDEpICsgaW5wdXRWYWx1ZS5zdWJzdHJpbmcoZmlyc3REb3RJbmRleCArIDEpLnJlcGxhY2UoL1wuL2csICcnKQogICAgICB9CgogICAgICAvLyDmoLnmja4gZHAg5Y+C5pWw6ZmQ5Yi25bCP5pWw5L2N5pWwCiAgICAgIGlmIChpbnB1dFZhbHVlLmluY2x1ZGVzKCcuJykgJiYgZHApIHsKICAgICAgICBjb25zdCBwYXJ0cyA9IGlucHV0VmFsdWUuc3BsaXQoJy4nKQogICAgICAgIGlmIChwYXJ0c1sxXSAmJiBwYXJ0c1sxXS5sZW5ndGggPiBkcCkgewogICAgICAgICAgaW5wdXRWYWx1ZSA9IHBhcnRzWzBdICsgJy4nICsgcGFydHNbMV0uc3Vic3RyaW5nKDAsIGRwKQogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g5aaC5p6c5aSE55CG5ZCO5Li656m65a2X56ym5Liy77yM6L+U5Zue56m6CiAgICAgIGlmIChpbnB1dFZhbHVlID09PSAnJykgewogICAgICAgIHJldHVybiAnJwogICAgICB9CgogICAgICAvLyDovazmjaLkuLrmlbDlrZfov5vooYzojIPlm7Tmo4Dmn6UKICAgICAgY29uc3QgbnVtVmFsdWUgPSBwYXJzZUZsb2F0KGlucHV0VmFsdWUpCgogICAgICAvLyDlpoLmnpzkuI3mmK/mnInmlYjmlbDlrZfvvIzov5Tlm57nqboKICAgICAgaWYgKGlzTmFOKG51bVZhbHVlKSkgewogICAgICAgIHJldHVybiAnJwogICAgICB9CgogICAgICAvLyDmnIDlsI/lgLzpmZDliLbkuLowCiAgICAgIGlmIChudW1WYWx1ZSA8IDApIHsKICAgICAgICByZXR1cm4gJzAnCiAgICAgIH0KCiAgICAgIC8vIOWmguaenOaciSB0eXBlIOWPguaVsO+8jOmZkOWItuacgOWkp+WAvAogICAgICBpZiAodHlwZSAmJiBudW1WYWx1ZSA+IHR5cGUpIHsKICAgICAgICAvLyDmoLnmja4gZHAg5qC85byP5YyW5pyA5aSn5YC8CiAgICAgICAgaWYgKGRwKSB7CiAgICAgICAgICByZXR1cm4gdHlwZS50b0ZpeGVkKGRwKS5yZXBsYWNlKC9cLj8wKyQvLCAnJykKICAgICAgICB9IGVsc2UgewogICAgICAgICAgcmV0dXJuIHR5cGUudG9TdHJpbmcoKQogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g6L+U5Zue5aSE55CG5ZCO55qE5YC8CiAgICAgIHJldHVybiBpbnB1dFZhbHVlCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["NodeDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "NodeDialog.vue", "sourceRoot": "src/views/PRO/project-config/project-quality/components/Dialog", "sourcesContent": ["<template>\n  <div>\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"140px\">\n      <el-row>\n        <el-col :span=\"12\">\n          <el-form-item label=\"质检节点\" prop=\"Display_Name\">\n            <el-select\n              v-model=\"form.Display_Name\"\n              :disabled=\"true\"\n              clearable\n              style=\"width: 100%\"\n              filterable\n              allow-create\n              placeholder=\"请输入质检节点\"\n              @change=\"changeNodeCode\"\n            >\n              <el-option\n                v-for=\"(item, index) in QualityNodeList\"\n                :key=\"index\"\n                :label=\"item.Name\"\n                :value=\"item.Name\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"专检类型\" prop=\"Change_Check_Type\">\n            <el-select\n              v-model=\"form.Change_Check_Type\"\n              style=\"width: 100%\"\n              placeholder=\"请选择专检类型\"\n              multiple\n              :disabled=\"Node_Code_Com\"\n              @change=\"SelectType\"\n              @remove-tag=\"removeType\"\n            >\n              <el-option\n                v-for=\"(item, index) in CheckTypeList\"\n                :key=\"index\"\n                :label=\"item.Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item\n            label=\"质量员\"\n            prop=\"ZL_UserIds\"\n            :rules=\"ZL_UserIds_Rules\"\n          >\n            <el-select\n              v-model=\"form.ZL_UserIds\"\n              filterable\n              clearable\n              multiple\n              style=\"width: 100%\"\n              placeholder=\"请选择质量员\"\n              :disabled=\"\n                Node_Code_Com ||\n                  (form.Change_Check_Type[0] != 1 &&\n                    form.Change_Check_Type.length != 2)\n              \"\n              @change=\"changeZLUser\"\n            >\n              <el-option\n                v-for=\"(item, index) in UserList\"\n                :key=\"index\"\n                :label=\"item.Display_Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item\n            label=\"探伤员\"\n            prop=\"TC_UserIds\"\n            :rules=\"TC_UserIds_Rules\"\n          >\n            <el-select\n              v-model=\"form.TC_UserIds\"\n              filterable\n              clearable\n              multiple\n              style=\"width: 100%\"\n              :disabled=\"\n                Node_Code_Com ||\n                  (form.Change_Check_Type[0] != 2 &&\n                    form.Change_Check_Type.length != 2)\n              \"\n              placeholder=\"请选择探伤员\"\n              @change=\"changeTCUser\"\n            >\n              <el-option\n                v-for=\"(item, index) in UserList\"\n                :key=\"index\"\n                :label=\"item.Display_Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"专检方式\" prop=\"Check_Style\">\n            <el-select\n              v-model=\"form.Check_Style\"\n              clearable\n              :disabled=\"Node_Code_Com\"\n              style=\"width: 100%\"\n              placeholder=\"请选择专检方式\"\n            >\n              <el-option\n                v-for=\"(item, index) in CheckStyleList\"\n                :key=\"index\"\n                :label=\"item.Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"质检流程\">\n            <el-radio-group v-model=\"qualityInspection\">\n              <el-radio :label=\"1\">专检</el-radio>\n            </el-radio-group>\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Check_Style===0 && form.Change_Check_Type && form.Change_Check_Type.includes(1)\" :span=\"12\">\n          <el-form-item label=\"质量要求合格率(%)\" prop=\"Zl_Demand_Spot_Check_Rate\">\n            <el-input v-model=\"form.Zl_Demand_Spot_Check_Rate\" class=\"cs-number-btn-hidden w100\" placeholder=\"请输入\" clearable @input=\"(value) => form.Zl_Demand_Spot_Check_Rate = handleInputFormat(value, 2, 100)\" />\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Check_Style===0 && form.Change_Check_Type && form.Change_Check_Type.includes(1)\" :span=\"12\">\n          <el-form-item label=\"质量要求抽检率(%)\" prop=\"Zl_Requirement_Spot_Check_Rate\">\n            <el-input v-model=\"form.Zl_Requirement_Spot_Check_Rate\" class=\"cs-number-btn-hidden w100\" placeholder=\"请输入\" clearable @input=\"(value) => form.Zl_Requirement_Spot_Check_Rate = handleInputFormat(value, 2, 100)\" />\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Check_Style===0 && form.Change_Check_Type && form.Change_Check_Type.includes(2)\" :span=\"12\">\n          <el-form-item label=\"探伤要求合格率(%)\" prop=\"Ts_Demand_Spot_Check_Rate\">\n            <el-input v-model=\"form.Ts_Demand_Spot_Check_Rate\" class=\"cs-number-btn-hidden w100\" placeholder=\"请输入\" clearable @input=\"(value) => form.Ts_Demand_Spot_Check_Rate = handleInputFormat(value, 2, 100)\" />\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Check_Style===0 && form.Change_Check_Type && form.Change_Check_Type.includes(2)\" :span=\"12\">\n          <el-form-item label=\"探伤要求抽检率(%)\" prop=\"Ts_Requirement_Spot_Check_Rate\">\n            <el-input v-model=\"form.Ts_Requirement_Spot_Check_Rate\" class=\"cs-number-btn-hidden w100\" placeholder=\"请输入\" clearable @input=\"(value) => form.Ts_Requirement_Spot_Check_Rate = handleInputFormat(value, 2, 100)\" />\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Change_Check_Type && form.Change_Check_Type.includes(2)\" :span=\"12\">\n          <el-form-item label=\"探伤要求时间(h)\" prop=\"Ts_Require_Time\">\n            <el-input v-model=\"form.Ts_Require_Time\" placeholder=\"请输入\" @input=\"(value) => form.Ts_Require_Time = handleInputFormat(value, 1)\" />\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item style=\"text-align: right\">\n            <el-button @click=\"$emit('close')\">关 闭</el-button>\n            <el-button\n              type=\"primary\"\n              @click=\"handleSubmit('form')\"\n            >确 定</el-button>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n  </div>\n</template>\n\n<script>\nimport { SaveNode } from '@/api/PRO/factorycheck'\nimport { GetEntityNode } from '@/api/PRO/factorycheck'\n// import { SaveCheckType } from \"@/api/PRO/factorycheck\";\nimport { GetFactoryPeoplelist } from '@/api/PRO/factorycheck'\n// import { GetProcessCodeList } from '@/api/PRO/factorycheck'\nexport default {\n  data() {\n    return {\n      mode: '', // 区分项目和工厂\n      ProjectId: '', // 项目Id\n      Check_Object_Id: '',\n      Bom_Level: '',\n      form: {\n        Node_Code: '',\n        Change_Check_Type: [],\n        Display_Name: '',\n        TC_UserId: '',\n        ZL_UserId: '',\n        Demand_Spot_Check_Rate: undefined,\n        Requirement_Spot_Check_Rate: undefined,\n        TC_UserIds: [],\n        ZL_UserIds: [],\n        Check_Style: '',\n        Ts_Require_Time: '',\n        Zl_Demand_Spot_Check_Rate: undefined,\n        Zl_Requirement_Spot_Check_Rate: undefined,\n        Ts_Demand_Spot_Check_Rate: undefined,\n        Ts_Requirement_Spot_Check_Rate: undefined\n      },\n\n      rules: {\n        Display_Name: [\n          { required: true, message: '请填写完整表单', trigger: 'change' }\n        ],\n        Check_Type: [\n          { required: true, message: '请填写完整表单', trigger: 'change' }\n        ],\n        Change_Check_Type: [\n          { required: true, validator: this.Check_Type_rules, message: '请填写完整表单', trigger: 'change' }\n        ],\n        Check_Style: [\n          { required: true, message: '请填写完整表单', trigger: 'change' }\n        ]\n      },\n      rules_Zl: { required: true, message: '请填写完整表单', trigger: 'bur' },\n      rules_Tc: { required: true, message: '请填写完整表单', trigger: 'bur' },\n      ZL_UserIds_Rules: [\n        { required: true, validator: this.Check_ZL_UserIds, message: '请填写完整表单', trigger: 'change' }\n      ],\n      TC_UserIds_Rules: [\n        { required: true, validator: this.Check_TC_UserIds, message: '请填写完整表单', trigger: 'change' }\n      ],\n      title: '',\n      editInfo: {},\n      QualityNodeList: [{ Name: '入库' }, { Name: '出库' }], // 质检节点列表\n      CheckTypeList: [\n        {\n          Name: '质量',\n          Id: 1\n        },\n        {\n          Name: '探伤',\n          Id: 2\n        }\n      ], // 质检类型\n      UserList: [], // 质量员，探伤人员\n      CheckStyleList: [\n        {\n          Name: '抽检',\n          Id: 0\n        },\n        {\n          Name: '全检',\n          Id: 1\n        }\n      ], // 质检方式\n      qualityInspection: 1,\n      sysProjectId: ''\n    }\n  },\n  computed: {\n    Node_Code_Com: function() {\n      if (this.form.Node_Code) {\n        return true\n      } else {\n        return false\n      }\n    }\n  },\n  mounted() {\n    this.getFactoryPeoplelist()\n  },\n  methods: {\n    Check_ZL_UserIds(rule, value, callback) {\n      if (this.form.Change_Check_Type && this.form.Change_Check_Type.includes(1) && this.form.ZL_UserIds.length === 0) {\n        callback(new Error('请填写完整表单'))\n      } else {\n        callback()\n      }\n    },\n    Check_TC_UserIds(rule, value, callback) {\n      if (!this.Node_Code_Com && !(this.form.Change_Check_Type[0] !== 2 && this.form.Change_Check_Type.length !== 2) && this.form.TC_UserIds.length === 0) {\n        callback(new Error('请填写完整表单'))\n      } else {\n        callback()\n      }\n    },\n    Check_Type_rules(rule, value, callback) {\n      if (this.form.Change_Check_Type.length === 0) {\n        callback(new Error('请填写完整表单'))\n      } else {\n        callback()\n      }\n    },\n    SelectType(item) {\n      this.$forceUpdate()\n      this.form.Change_Check_Type = item\n      if (item.length === 1) {\n        this.form.Check_Type = item[0]\n      } else if (item.length === 2) {\n        this.form.Check_Type = -1\n      }\n\n      if (!item.includes(1)) {\n        this.form.ZL_UserId = ''\n        this.form.ZL_UserIds = []\n      }\n      if (!item.includes(2)) {\n        this.form.TC_UserId = ''\n        this.form.TC_UserIds = []\n      }\n      console.log(this.form.Change_Check_Type)\n    },\n    removeType(item) {\n      console.log(item, 'b')\n      // if (item == 1) {\n      //   this.form.ZL_UserId = \"\";\n      // } else if (item == 2) {\n      //   this.form.TC_UserId = \"\";\n      // }\n    },\n    clearType(val) {\n      console.log(val)\n      this.form.ZL_UserId = ''\n      this.form.TC_UserId = ''\n      this.form.ZL_UserIds = []\n      this.form.TC_UserIds = []\n    },\n    init(title, checkType, data, sysProjectId) {\n      this.sysProjectId = sysProjectId\n      this.Check_Object_Id = checkType.Id\n      this.Bom_Level = checkType.Code\n      this.title = title\n      if (title === '编辑') {\n        console.log(data)\n        this.form.Id = data.Id\n        this.getEntityNode(data)\n      }\n      this.getCheckNode()\n    },\n    async addCheckNode() {\n      const { Zl_Demand_Spot_Check_Rate, Zl_Requirement_Spot_Check_Rate, Ts_Demand_Spot_Check_Rate, Ts_Requirement_Spot_Check_Rate, ...others } = this.form\n      const submit = {\n        ...others,\n        Check_Object_Id: this.Check_Object_Id,\n        Bom_Level: this.Bom_Level,\n        sysProjectId: this.sysProjectId\n      }\n      if (this.form.Check_Style === 0) { // 抽检\n        submit.Zl_Demand_Spot_Check_Rate = Zl_Demand_Spot_Check_Rate\n        submit.Zl_Requirement_Spot_Check_Rate = Zl_Requirement_Spot_Check_Rate\n        submit.Ts_Demand_Spot_Check_Rate = Ts_Demand_Spot_Check_Rate\n        submit.Ts_Requirement_Spot_Check_Rate = Ts_Requirement_Spot_Check_Rate\n      }\n      await SaveNode(submit).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            type: 'success',\n            message: '保存成功'\n          })\n          this.$emit('refresh')\n          this.$emit('close')\n          this.dialogData = {}\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    getFactoryPeoplelist() {\n      GetFactoryPeoplelist().then((res) => {\n        if (res.IsSucceed) {\n          console.log(res.Data)\n          this.UserList = res.Data\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    // 判断是工厂还是项目获取质检节点\n    getCheckNode() {\n      const Platform =\n        localStorage.getItem('Platform') || localStorage.getItem('CurPlatform')\n      if (Platform === '2') {\n        this.mode = 'factory'\n        // this.getFactoryNode();\n      }\n      // 获取项目/工厂id\n      this.ProjectId =\n        this.mode === 'factory'\n          ? localStorage.getItem('CurReferenceId')\n          : this.ProjectId\n    },\n    // 如果是工厂获取质检节点\n    // getFactoryNode() {\n    //   GetProcessCodeList({sys_workobject_id:this.Check_Object_Id}).then((res) => {\n    //     if (res.IsSucceed) {\n    //       let CheckJson = res.Data;\n    //       CheckJson.push({ Name: \"入库\" }, { Name: \"出库\" });\n    //       console.log(CheckJson);\n    //       this.QualityNodeList = CheckJson;\n    //       console.log(this.QualityNodeList);\n    //     } else {\n    //       this.$message({\n    //         type: \"error\",\n    //         message: res.Message,\n    //       });\n    //     }\n    //   });\n    // },\n\n    // 质检节点获取质检节点名\n    changeNodeCode(val) {\n      this.form = {\n        Node_Code: '',\n        Change_Check_Type: [],\n        Display_Name: '',\n        TC_UserId: '',\n        ZL_UserId: '',\n        TC_UserIds: [],\n        ZL_UserIds: [],\n        Check_Style: ''\n      }\n      this.form.Display_Name = val\n      this.form.Node_Code = null\n      // this.form.Change_Check_Type = [];\n      // try {\n      //   this.form.Node_Code = this.QualityNodeList.find((v) => {\n      //     return v.Name == val;\n      //   }).Id;\n      // } catch (err) {\n      //   this.form.Node_Code = null;\n      // }\n      // console.log\n      // let arr = {};\n      // arr = this.QualityNodeList.find((v) => {\n      //   return v.Name == val;\n      // });\n      // console.log(arr);\n      // if (arr) {\n      //   this.form.Check_Style = arr.Check_Style ? Number(arr.Check_Style) : \"\";\n      //   arr.Is_Need_TC &&(this.form.Change_Check_Type.push(2))\n      //   arr.Is_Need_ZL &&( this.form.Change_Check_Type.push(1));\n      //   this.SelectType(this.form.Change_Check_Type);\n\n      //   this.form.ZL_UserId = arr.ZL_Check_UserId ? arr.ZL_Check_UserId: \"\";\n      //   this.form.TC_UserId = arr.TC_Check_UserId ? arr.TC_Check_UserId : \"\"\n      //   console.log(this.form.ZL_UserId)\n      // }\n    },\n    changeZLUser(val) {\n      console.log(val)\n      // 解决下拉框回显问题\n      this.$forceUpdate()\n      this.form.ZL_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i === val.length - 1) {\n          this.form.ZL_UserId += val[i]\n        } else {\n          this.form.ZL_UserId += val[i] + ','\n        }\n      }\n      console.log(this.form.ZL_UserId, 'this.form.ZL_UserId ')\n    },\n    changeTCUser(val) {\n      this.$forceUpdate()\n      this.form.TC_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i === val.length - 1) {\n          this.form.TC_UserId += val[i]\n        } else {\n          this.form.TC_UserId += val[i] + ','\n        }\n      }\n      // 解决下拉框回显问题\n\n      console.log(this.form.TC_UserId, 'this.form.TC_UserId ')\n    },\n    getEntityNode(data) {\n      GetEntityNode({ id: data.Id }).then((res) => {\n        if (res.IsSucceed) {\n          console.log(res.Data)\n          this.form = res.Data[0]\n          this.form.Change_Check_Type = []\n          if (this.form.Check_Type === 1 || this.form.Check_Type === 2) {\n            this.form.Change_Check_Type.push(this.form.Check_Type)\n          } else if (this.form.Check_Type === -1) {\n            this.form.Change_Check_Type = [1, 2]\n          } else {\n            this.form.Change_Check_Type = []\n          }\n          this.form.ZL_UserIds = this.form.ZL_UserId ? this.form.ZL_UserId.split(',') : []\n          this.form.TC_UserIds = this.form.TC_UserId ? this.form.TC_UserId.split(',') : []\n          console.log(this.form.ZL_UserIds, this.form.TC_UserId)\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    handleSubmit(form) {\n      this.$refs[form].validate((valid) => {\n        if (valid) {\n          this.addCheckNode()\n        } else {\n          return false\n        }\n      })\n    },\n    handleInputFormat(value, dp, type) {\n      // 如果输入为空，直接返回空\n      if (value === '' || value === null || value === undefined) {\n        return ''\n      }\n\n      // 转换为字符串进行处理\n      let inputValue = String(value)\n\n      // 移除所有非数字和非小数点的字符（包括负号）\n      inputValue = inputValue.replace(/[^0-9.]/g, '')\n\n      // 如果只是单独的小数点，返回空\n      if (inputValue === '.') {\n        return ''\n      }\n\n      // 确保只有一个小数点\n      const dotCount = (inputValue.match(/\\./g) || []).length\n      if (dotCount > 1) {\n        // 如果有多个小数点，只保留第一个\n        const firstDotIndex = inputValue.indexOf('.')\n        inputValue = inputValue.substring(0, firstDotIndex + 1) + inputValue.substring(firstDotIndex + 1).replace(/\\./g, '')\n      }\n\n      // 根据 dp 参数限制小数位数\n      if (inputValue.includes('.') && dp) {\n        const parts = inputValue.split('.')\n        if (parts[1] && parts[1].length > dp) {\n          inputValue = parts[0] + '.' + parts[1].substring(0, dp)\n        }\n      }\n\n      // 如果处理后为空字符串，返回空\n      if (inputValue === '') {\n        return ''\n      }\n\n      // 转换为数字进行范围检查\n      const numValue = parseFloat(inputValue)\n\n      // 如果不是有效数字，返回空\n      if (isNaN(numValue)) {\n        return ''\n      }\n\n      // 最小值限制为0\n      if (numValue < 0) {\n        return '0'\n      }\n\n      // 如果有 type 参数，限制最大值\n      if (type && numValue > type) {\n        // 根据 dp 格式化最大值\n        if (dp) {\n          return type.toFixed(dp).replace(/\\.?0+$/, '')\n        } else {\n          return type.toString()\n        }\n      }\n\n      // 返回处理后的值\n      return inputValue\n    }\n  }\n}\n</script>\n\n<style scoped></style>\n"]}]}
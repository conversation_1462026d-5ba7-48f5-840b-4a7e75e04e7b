{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\NodeDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\NodeDialog.vue", "mtime": 1757921693869}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IFNhdmVOb2RlIH0gZnJvbSAnQC9hcGkvUFJPL2ZhY3RvcnljaGVjaycKaW1wb3J0IHsgR2V0RW50aXR5Tm9kZSB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snCi8vIGltcG9ydCB7IFNhdmVDaGVja1R5cGUgfSBmcm9tICJAL2FwaS9QUk8vZmFjdG9yeWNoZWNrIjsKaW1wb3J0IHsgR2V0RmFjdG9yeVBlb3BsZWxpc3QgfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJwovLyBpbXBvcnQgeyBHZXRQcm9jZXNzQ29kZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJwpleHBvcnQgZGVmYXVsdCB7CiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIG1vZGU6ICcnLCAvLyDljLrliIbpobnnm67lkozlt6XljoIKICAgICAgUHJvamVjdElkOiAnJywgLy8g6aG555uuSWQKICAgICAgQ2hlY2tfT2JqZWN0X0lkOiAnJywKICAgICAgQm9tX0xldmVsOiAnJywKICAgICAgZm9ybTogewogICAgICAgIE5vZGVfQ29kZTogJycsCiAgICAgICAgQ2hhbmdlX0NoZWNrX1R5cGU6IFtdLAogICAgICAgIERpc3BsYXlfTmFtZTogJycsCiAgICAgICAgVENfVXNlcklkOiAnJywKICAgICAgICBaTF9Vc2VySWQ6ICcnLAogICAgICAgIERlbWFuZF9TcG90X0NoZWNrX1JhdGU6IHVuZGVmaW5lZCwKICAgICAgICBSZXF1aXJlbWVudF9TcG90X0NoZWNrX1JhdGU6IHVuZGVmaW5lZCwKICAgICAgICBUQ19Vc2VySWRzOiBbXSwKICAgICAgICBaTF9Vc2VySWRzOiBbXSwKICAgICAgICBDaGVja19TdHlsZTogJycsCiAgICAgICAgVHNfUmVxdWlyZV9UaW1lOiAnJywKICAgICAgICBabF9EZW1hbmRfU3BvdF9DaGVja19SYXRlOiB1bmRlZmluZWQsCiAgICAgICAgWmxfUmVxdWlyZW1lbnRfU3BvdF9DaGVja19SYXRlOiB1bmRlZmluZWQsCiAgICAgICAgVHNfRGVtYW5kX1Nwb3RfQ2hlY2tfUmF0ZTogdW5kZWZpbmVkLAogICAgICAgIFRzX1JlcXVpcmVtZW50X1Nwb3RfQ2hlY2tfUmF0ZTogdW5kZWZpbmVkCiAgICAgIH0sCgogICAgICBydWxlczogewogICAgICAgIERpc3BsYXlfTmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOihqOWNlScsIHRyaWdnZXI6ICdjaGFuZ2UnIH0KICAgICAgICBdLAogICAgICAgIENoZWNrX1R5cGU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgICAgXSwKICAgICAgICBDaGFuZ2VfQ2hlY2tfVHlwZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdmFsaWRhdG9yOiB0aGlzLkNoZWNrX1R5cGVfcnVsZXMsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgICAgXSwKICAgICAgICBDaGVja19TdHlsZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOihqOWNlScsIHRyaWdnZXI6ICdjaGFuZ2UnIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIHJ1bGVzX1psOiB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+35aGr5YaZ5a6M5pW06KGo5Y2VJywgdHJpZ2dlcjogJ2J1cicgfSwKICAgICAgcnVsZXNfVGM6IHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnYnVyJyB9LAogICAgICBaTF9Vc2VySWRzX1J1bGVzOiBbCiAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdmFsaWRhdG9yOiB0aGlzLkNoZWNrX1pMX1VzZXJJZHMsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgIF0sCiAgICAgIFRDX1VzZXJJZHNfUnVsZXM6IFsKICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB2YWxpZGF0b3I6IHRoaXMuQ2hlY2tfVENfVXNlcklkcywgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOihqOWNlScsIHRyaWdnZXI6ICdjaGFuZ2UnIH0KICAgICAgXSwKICAgICAgdGl0bGU6ICcnLAogICAgICBlZGl0SW5mbzoge30sCiAgICAgIFF1YWxpdHlOb2RlTGlzdDogW3sgTmFtZTogJ+WFpeW6kycgfSwgeyBOYW1lOiAn5Ye65bqTJyB9XSwgLy8g6LSo5qOA6IqC54K55YiX6KGoCiAgICAgIENoZWNrVHlwZUxpc3Q6IFsKICAgICAgICB7CiAgICAgICAgICBOYW1lOiAn6LSo6YePJywKICAgICAgICAgIElkOiAxCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBOYW1lOiAn5o6i5LykJywKICAgICAgICAgIElkOiAyCiAgICAgICAgfQogICAgICBdLCAvLyDotKjmo4DnsbvlnosKICAgICAgVXNlckxpc3Q6IFtdLCAvLyDotKjph4/lkZjvvIzmjqLkvKTkurrlkZgKICAgICAgQ2hlY2tTdHlsZUxpc3Q6IFsKICAgICAgICB7CiAgICAgICAgICBOYW1lOiAn5oq95qOAJywKICAgICAgICAgIElkOiAwCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBOYW1lOiAn5YWo5qOAJywKICAgICAgICAgIElkOiAxCiAgICAgICAgfQogICAgICBdLCAvLyDotKjmo4DmlrnlvI8KICAgICAgcXVhbGl0eUluc3BlY3Rpb246IDEKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICBOb2RlX0NvZGVfQ29tOiBmdW5jdGlvbigpIHsKICAgICAgaWYgKHRoaXMuZm9ybS5Ob2RlX0NvZGUpIHsKICAgICAgICByZXR1cm4gdHJ1ZQogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiBmYWxzZQogICAgICB9CiAgICB9CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5nZXRGYWN0b3J5UGVvcGxlbGlzdCgpCiAgfSwKICBtZXRob2RzOiB7CiAgICBDaGVja19aTF9Vc2VySWRzKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgewogICAgICBpZiAodGhpcy5mb3JtLkNoYW5nZV9DaGVja19UeXBlICYmIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZS5pbmNsdWRlcygxKSAmJiB0aGlzLmZvcm0uWkxfVXNlcklkcy5sZW5ndGggPT09IDApIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+ivt+Whq+WGmeWujOaVtOihqOWNlScpKQogICAgICB9IGVsc2UgewogICAgICAgIGNhbGxiYWNrKCkKICAgICAgfQogICAgfSwKICAgIENoZWNrX1RDX1VzZXJJZHMocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7CiAgICAgIGlmICghdGhpcy5Ob2RlX0NvZGVfQ29tICYmICEodGhpcy5mb3JtLkNoYW5nZV9DaGVja19UeXBlWzBdICE9PSAyICYmIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZS5sZW5ndGggIT09IDIpICYmIHRoaXMuZm9ybS5UQ19Vc2VySWRzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6K+35aGr5YaZ5a6M5pW06KGo5Y2VJykpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgY2FsbGJhY2soKQogICAgICB9CiAgICB9LAogICAgQ2hlY2tfVHlwZV9ydWxlcyhydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZS5sZW5ndGggPT09IDApIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+ivt+Whq+WGmeWujOaVtOihqOWNlScpKQogICAgICB9IGVsc2UgewogICAgICAgIGNhbGxiYWNrKCkKICAgICAgfQogICAgfSwKICAgIFNlbGVjdFR5cGUoaXRlbSkgewogICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpCiAgICAgIHRoaXMuZm9ybS5DaGFuZ2VfQ2hlY2tfVHlwZSA9IGl0ZW0KICAgICAgaWYgKGl0ZW0ubGVuZ3RoID09PSAxKSB7CiAgICAgICAgdGhpcy5mb3JtLkNoZWNrX1R5cGUgPSBpdGVtWzBdCiAgICAgIH0gZWxzZSBpZiAoaXRlbS5sZW5ndGggPT09IDIpIHsKICAgICAgICB0aGlzLmZvcm0uQ2hlY2tfVHlwZSA9IC0xCiAgICAgIH0KCiAgICAgIGlmICghaXRlbS5pbmNsdWRlcygxKSkgewogICAgICAgIHRoaXMuZm9ybS5aTF9Vc2VySWQgPSAnJwogICAgICAgIHRoaXMuZm9ybS5aTF9Vc2VySWRzID0gW10KICAgICAgfQogICAgICBpZiAoIWl0ZW0uaW5jbHVkZXMoMikpIHsKICAgICAgICB0aGlzLmZvcm0uVENfVXNlcklkID0gJycKICAgICAgICB0aGlzLmZvcm0uVENfVXNlcklkcyA9IFtdCiAgICAgIH0KICAgICAgY29uc29sZS5sb2codGhpcy5mb3JtLkNoYW5nZV9DaGVja19UeXBlKQogICAgfSwKICAgIHJlbW92ZVR5cGUoaXRlbSkgewogICAgICBjb25zb2xlLmxvZyhpdGVtLCAnYicpCiAgICAgIC8vIGlmIChpdGVtID09IDEpIHsKICAgICAgLy8gICB0aGlzLmZvcm0uWkxfVXNlcklkID0gIiI7CiAgICAgIC8vIH0gZWxzZSBpZiAoaXRlbSA9PSAyKSB7CiAgICAgIC8vICAgdGhpcy5mb3JtLlRDX1VzZXJJZCA9ICIiOwogICAgICAvLyB9CiAgICB9LAogICAgY2xlYXJUeXBlKHZhbCkgewogICAgICBjb25zb2xlLmxvZyh2YWwpCiAgICAgIHRoaXMuZm9ybS5aTF9Vc2VySWQgPSAnJwogICAgICB0aGlzLmZvcm0uVENfVXNlcklkID0gJycKICAgICAgdGhpcy5mb3JtLlpMX1VzZXJJZHMgPSBbXQogICAgICB0aGlzLmZvcm0uVENfVXNlcklkcyA9IFtdCiAgICB9LAogICAgaW5pdCh0aXRsZSwgY2hlY2tUeXBlLCBkYXRhKSB7CiAgICAgIHRoaXMuQ2hlY2tfT2JqZWN0X0lkID0gY2hlY2tUeXBlLklkCiAgICAgIHRoaXMuQm9tX0xldmVsID0gY2hlY2tUeXBlLkNvZGUKICAgICAgdGhpcy50aXRsZSA9IHRpdGxlCiAgICAgIGlmICh0aXRsZSA9PT0gJ+e8lui+kScpIHsKICAgICAgICBjb25zb2xlLmxvZyhkYXRhKQogICAgICAgIHRoaXMuZm9ybS5JZCA9IGRhdGEuSWQKICAgICAgICB0aGlzLmdldEVudGl0eU5vZGUoZGF0YSkKICAgICAgfQogICAgICB0aGlzLmdldENoZWNrTm9kZSgpCiAgICB9LAogICAgYXN5bmMgYWRkQ2hlY2tOb2RlKCkgewogICAgICBjb25zdCB7IFpsX0RlbWFuZF9TcG90X0NoZWNrX1JhdGUsIFpsX1JlcXVpcmVtZW50X1Nwb3RfQ2hlY2tfUmF0ZSwgVHNfRGVtYW5kX1Nwb3RfQ2hlY2tfUmF0ZSwgVHNfUmVxdWlyZW1lbnRfU3BvdF9DaGVja19SYXRlLCAuLi5vdGhlcnMgfSA9IHRoaXMuZm9ybQogICAgICBjb25zdCBzdWJtaXQgPSB7CiAgICAgICAgLi4ub3RoZXJzLAogICAgICAgIENoZWNrX09iamVjdF9JZDogdGhpcy5DaGVja19PYmplY3RfSWQsCiAgICAgICAgQm9tX0xldmVsOiB0aGlzLkJvbV9MZXZlbAogICAgICB9CiAgICAgIGlmICh0aGlzLmZvcm0uQ2hlY2tfU3R5bGUgPT09IDApIHsgLy8g5oq95qOACiAgICAgICAgc3VibWl0LlpsX0RlbWFuZF9TcG90X0NoZWNrX1JhdGUgPSBabF9EZW1hbmRfU3BvdF9DaGVja19SYXRlCiAgICAgICAgc3VibWl0LlpsX1JlcXVpcmVtZW50X1Nwb3RfQ2hlY2tfUmF0ZSA9IFpsX1JlcXVpcmVtZW50X1Nwb3RfQ2hlY2tfUmF0ZQogICAgICAgIHN1Ym1pdC5Uc19EZW1hbmRfU3BvdF9DaGVja19SYXRlID0gVHNfRGVtYW5kX1Nwb3RfQ2hlY2tfUmF0ZQogICAgICAgIHN1Ym1pdC5Uc19SZXF1aXJlbWVudF9TcG90X0NoZWNrX1JhdGUgPSBUc19SZXF1aXJlbWVudF9TcG90X0NoZWNrX1JhdGUKICAgICAgfQogICAgICBhd2FpdCBTYXZlTm9kZShzdWJtaXQpLnRoZW4oKHJlcykgPT4gewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICBtZXNzYWdlOiAn5L+d5a2Y5oiQ5YqfJwogICAgICAgICAgfSkKICAgICAgICAgIHRoaXMuJGVtaXQoJ3JlZnJlc2gnKQogICAgICAgICAgdGhpcy4kZW1pdCgnY2xvc2UnKQogICAgICAgICAgdGhpcy5kaWFsb2dEYXRhID0ge30KICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICBnZXRGYWN0b3J5UGVvcGxlbGlzdCgpIHsKICAgICAgR2V0RmFjdG9yeVBlb3BsZWxpc3QoKS50aGVuKChyZXMpID0+IHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgY29uc29sZS5sb2cocmVzLkRhdGEpCiAgICAgICAgICB0aGlzLlVzZXJMaXN0ID0gcmVzLkRhdGEKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICAvLyDliKTmlq3mmK/lt6XljoLov5jmmK/pobnnm67ojrflj5botKjmo4DoioLngrkKICAgIGdldENoZWNrTm9kZSgpIHsKICAgICAgY29uc3QgUGxhdGZvcm0gPQogICAgICAgIGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdQbGF0Zm9ybScpIHx8IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdDdXJQbGF0Zm9ybScpCiAgICAgIGlmIChQbGF0Zm9ybSA9PT0gJzInKSB7CiAgICAgICAgdGhpcy5tb2RlID0gJ2ZhY3RvcnknCiAgICAgICAgLy8gdGhpcy5nZXRGYWN0b3J5Tm9kZSgpOwogICAgICB9CiAgICAgIC8vIOiOt+W<PERSON><PERSON><PERSON><PERSON><PERSON>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"}, {"version": 3, "sources": ["NodeDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "NodeDialog.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup/components/Dialog", "sourcesContent": ["<template>\n  <div>\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"140px\">\n      <el-row>\n        <el-col :span=\"12\">\n          <el-form-item label=\"质检节点\" prop=\"Display_Name\">\n            <el-select\n              v-model=\"form.Display_Name\"\n              :disabled=\"Node_Code_Com\"\n              clearable\n              style=\"width: 100%\"\n              filterable\n              allow-create\n              placeholder=\"请输入质检节点\"\n              @change=\"changeNodeCode\"\n            >\n              <el-option\n                v-for=\"(item, index) in QualityNodeList\"\n                :key=\"index\"\n                :label=\"item.Name\"\n                :value=\"item.Name\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"专检类型\" prop=\"Change_Check_Type\">\n            <el-select\n              v-model=\"form.Change_Check_Type\"\n              style=\"width: 100%\"\n              placeholder=\"请选择专检类型\"\n              multiple\n              :disabled=\"Node_Code_Com\"\n              @change=\"SelectType\"\n              @remove-tag=\"removeType\"\n            >\n              <el-option\n                v-for=\"(item, index) in CheckTypeList\"\n                :key=\"index\"\n                :label=\"item.Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item\n            label=\"质量员\"\n            prop=\"ZL_UserIds\"\n            :rules=\"ZL_UserIds_Rules\"\n          >\n            <el-select\n              v-model=\"form.ZL_UserIds\"\n              filterable\n              clearable\n              multiple\n              style=\"width: 100%\"\n              placeholder=\"请选择质量员\"\n              :disabled=\"\n                Node_Code_Com ||\n                  (form.Change_Check_Type[0] != 1 &&\n                    form.Change_Check_Type.length != 2)\n              \"\n              @change=\"changeZLUser\"\n            >\n              <el-option\n                v-for=\"(item, index) in UserList\"\n                :key=\"index\"\n                :label=\"item.Display_Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item\n            label=\"探伤员\"\n            prop=\"TC_UserIds\"\n            :rules=\"TC_UserIds_Rules\"\n          >\n            <el-select\n              v-model=\"form.TC_UserIds\"\n              filterable\n              clearable\n              multiple\n              style=\"width: 100%\"\n              :disabled=\"\n                Node_Code_Com ||\n                  (form.Change_Check_Type[0] != 2 &&\n                    form.Change_Check_Type.length != 2)\n              \"\n              placeholder=\"请选择探伤员\"\n              @change=\"changeTCUser\"\n            >\n              <el-option\n                v-for=\"(item, index) in UserList\"\n                :key=\"index\"\n                :label=\"item.Display_Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"专检方式\" prop=\"Check_Style\">\n            <el-select\n              v-model=\"form.Check_Style\"\n              clearable\n              :disabled=\"Node_Code_Com\"\n              style=\"width: 100%\"\n              placeholder=\"请选择专检方式\"\n            >\n              <el-option\n                v-for=\"(item, index) in CheckStyleList\"\n                :key=\"index\"\n                :label=\"item.Name\"\n                :value=\"item.Id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"质检流程\">\n            <el-radio-group v-model=\"qualityInspection\">\n              <el-radio :label=\"1\">专检</el-radio>\n            </el-radio-group>\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Check_Style===0 && form.Change_Check_Type && form.Change_Check_Type.includes(1)\" :span=\"12\">\n          <el-form-item label=\"质量要求合格率(%)\" prop=\"Zl_Demand_Spot_Check_Rate\">\n            <el-input v-model=\"form.Zl_Demand_Spot_Check_Rate\" class=\"cs-number-btn-hidden w100\" placeholder=\"请输入\" clearable @input=\"(value) => form.Zl_Demand_Spot_Check_Rate = handleInputFormat(value, 2, 100)\" />\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Check_Style===0 && form.Change_Check_Type && form.Change_Check_Type.includes(1)\" :span=\"12\">\n          <el-form-item label=\"质量要求抽检率(%)\" prop=\"Zl_Requirement_Spot_Check_Rate\">\n            <el-input v-model=\"form.Zl_Requirement_Spot_Check_Rate\" class=\"cs-number-btn-hidden w100\" placeholder=\"请输入\" clearable @input=\"(value) => form.Zl_Requirement_Spot_Check_Rate = handleInputFormat(value, 2, 100)\" />\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Check_Style===0 && form.Change_Check_Type && form.Change_Check_Type.includes(2)\" :span=\"12\">\n          <el-form-item label=\"探伤要求合格率(%)\" prop=\"Ts_Demand_Spot_Check_Rate\">\n            <el-input v-model=\"form.Ts_Demand_Spot_Check_Rate\" class=\"cs-number-btn-hidden w100\" placeholder=\"请输入\" clearable @input=\"(value) => form.Ts_Demand_Spot_Check_Rate = handleInputFormat(value, 2, 100)\" />\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Check_Style===0 && form.Change_Check_Type && form.Change_Check_Type.includes(2)\" :span=\"12\">\n          <el-form-item label=\"探伤要求抽检率(%)\" prop=\"Ts_Requirement_Spot_Check_Rate\">\n            <el-input v-model=\"form.Ts_Requirement_Spot_Check_Rate\" class=\"cs-number-btn-hidden w100\" placeholder=\"请输入\" clearable @input=\"(value) => form.Ts_Requirement_Spot_Check_Rate = handleInputFormat(value, 2, 100)\" />\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"form.Change_Check_Type && form.Change_Check_Type.includes(2)\" :span=\"12\">\n          <el-form-item label=\"探伤要求时间(h)\" prop=\"Ts_Require_Time\">\n            <el-input v-model=\"form.Ts_Require_Time\" placeholder=\"请输入\" @input=\"(value) => form.Ts_Require_Time = handleInputFormat(value, 1)\" />\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item style=\"text-align: right\">\n            <el-button @click=\"$emit('close')\">关 闭</el-button>\n            <el-button\n              type=\"primary\"\n              @click=\"handleSubmit('form')\"\n            >确 定</el-button>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n  </div>\n</template>\n\n<script>\nimport { SaveNode } from '@/api/PRO/factorycheck'\nimport { GetEntityNode } from '@/api/PRO/factorycheck'\n// import { SaveCheckType } from \"@/api/PRO/factorycheck\";\nimport { GetFactoryPeoplelist } from '@/api/PRO/factorycheck'\n// import { GetProcessCodeList } from '@/api/PRO/factorycheck'\nexport default {\n  data() {\n    return {\n      mode: '', // 区分项目和工厂\n      ProjectId: '', // 项目Id\n      Check_Object_Id: '',\n      Bom_Level: '',\n      form: {\n        Node_Code: '',\n        Change_Check_Type: [],\n        Display_Name: '',\n        TC_UserId: '',\n        ZL_UserId: '',\n        Demand_Spot_Check_Rate: undefined,\n        Requirement_Spot_Check_Rate: undefined,\n        TC_UserIds: [],\n        ZL_UserIds: [],\n        Check_Style: '',\n        Ts_Require_Time: '',\n        Zl_Demand_Spot_Check_Rate: undefined,\n        Zl_Requirement_Spot_Check_Rate: undefined,\n        Ts_Demand_Spot_Check_Rate: undefined,\n        Ts_Requirement_Spot_Check_Rate: undefined\n      },\n\n      rules: {\n        Display_Name: [\n          { required: true, message: '请填写完整表单', trigger: 'change' }\n        ],\n        Check_Type: [\n          { required: true, message: '请填写完整表单', trigger: 'change' }\n        ],\n        Change_Check_Type: [\n          { required: true, validator: this.Check_Type_rules, message: '请填写完整表单', trigger: 'change' }\n        ],\n        Check_Style: [\n          { required: true, message: '请填写完整表单', trigger: 'change' }\n        ]\n      },\n      rules_Zl: { required: true, message: '请填写完整表单', trigger: 'bur' },\n      rules_Tc: { required: true, message: '请填写完整表单', trigger: 'bur' },\n      ZL_UserIds_Rules: [\n        { required: true, validator: this.Check_ZL_UserIds, message: '请填写完整表单', trigger: 'change' }\n      ],\n      TC_UserIds_Rules: [\n        { required: true, validator: this.Check_TC_UserIds, message: '请填写完整表单', trigger: 'change' }\n      ],\n      title: '',\n      editInfo: {},\n      QualityNodeList: [{ Name: '入库' }, { Name: '出库' }], // 质检节点列表\n      CheckTypeList: [\n        {\n          Name: '质量',\n          Id: 1\n        },\n        {\n          Name: '探伤',\n          Id: 2\n        }\n      ], // 质检类型\n      UserList: [], // 质量员，探伤人员\n      CheckStyleList: [\n        {\n          Name: '抽检',\n          Id: 0\n        },\n        {\n          Name: '全检',\n          Id: 1\n        }\n      ], // 质检方式\n      qualityInspection: 1\n    }\n  },\n  computed: {\n    Node_Code_Com: function() {\n      if (this.form.Node_Code) {\n        return true\n      } else {\n        return false\n      }\n    }\n  },\n  mounted() {\n    this.getFactoryPeoplelist()\n  },\n  methods: {\n    Check_ZL_UserIds(rule, value, callback) {\n      if (this.form.Change_Check_Type && this.form.Change_Check_Type.includes(1) && this.form.ZL_UserIds.length === 0) {\n        callback(new Error('请填写完整表单'))\n      } else {\n        callback()\n      }\n    },\n    Check_TC_UserIds(rule, value, callback) {\n      if (!this.Node_Code_Com && !(this.form.Change_Check_Type[0] !== 2 && this.form.Change_Check_Type.length !== 2) && this.form.TC_UserIds.length === 0) {\n        callback(new Error('请填写完整表单'))\n      } else {\n        callback()\n      }\n    },\n    Check_Type_rules(rule, value, callback) {\n      if (this.form.Change_Check_Type.length === 0) {\n        callback(new Error('请填写完整表单'))\n      } else {\n        callback()\n      }\n    },\n    SelectType(item) {\n      this.$forceUpdate()\n      this.form.Change_Check_Type = item\n      if (item.length === 1) {\n        this.form.Check_Type = item[0]\n      } else if (item.length === 2) {\n        this.form.Check_Type = -1\n      }\n\n      if (!item.includes(1)) {\n        this.form.ZL_UserId = ''\n        this.form.ZL_UserIds = []\n      }\n      if (!item.includes(2)) {\n        this.form.TC_UserId = ''\n        this.form.TC_UserIds = []\n      }\n      console.log(this.form.Change_Check_Type)\n    },\n    removeType(item) {\n      console.log(item, 'b')\n      // if (item == 1) {\n      //   this.form.ZL_UserId = \"\";\n      // } else if (item == 2) {\n      //   this.form.TC_UserId = \"\";\n      // }\n    },\n    clearType(val) {\n      console.log(val)\n      this.form.ZL_UserId = ''\n      this.form.TC_UserId = ''\n      this.form.ZL_UserIds = []\n      this.form.TC_UserIds = []\n    },\n    init(title, checkType, data) {\n      this.Check_Object_Id = checkType.Id\n      this.Bom_Level = checkType.Code\n      this.title = title\n      if (title === '编辑') {\n        console.log(data)\n        this.form.Id = data.Id\n        this.getEntityNode(data)\n      }\n      this.getCheckNode()\n    },\n    async addCheckNode() {\n      const { Zl_Demand_Spot_Check_Rate, Zl_Requirement_Spot_Check_Rate, Ts_Demand_Spot_Check_Rate, Ts_Requirement_Spot_Check_Rate, ...others } = this.form\n      const submit = {\n        ...others,\n        Check_Object_Id: this.Check_Object_Id,\n        Bom_Level: this.Bom_Level\n      }\n      if (this.form.Check_Style === 0) { // 抽检\n        submit.Zl_Demand_Spot_Check_Rate = Zl_Demand_Spot_Check_Rate\n        submit.Zl_Requirement_Spot_Check_Rate = Zl_Requirement_Spot_Check_Rate\n        submit.Ts_Demand_Spot_Check_Rate = Ts_Demand_Spot_Check_Rate\n        submit.Ts_Requirement_Spot_Check_Rate = Ts_Requirement_Spot_Check_Rate\n      }\n      await SaveNode(submit).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            type: 'success',\n            message: '保存成功'\n          })\n          this.$emit('refresh')\n          this.$emit('close')\n          this.dialogData = {}\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    getFactoryPeoplelist() {\n      GetFactoryPeoplelist().then((res) => {\n        if (res.IsSucceed) {\n          console.log(res.Data)\n          this.UserList = res.Data\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    // 判断是工厂还是项目获取质检节点\n    getCheckNode() {\n      const Platform =\n        localStorage.getItem('Platform') || localStorage.getItem('CurPlatform')\n      if (Platform === '2') {\n        this.mode = 'factory'\n        // this.getFactoryNode();\n      }\n      // 获取项目/工厂id\n      this.ProjectId =\n        this.mode === 'factory'\n          ? localStorage.getItem('CurReferenceId')\n          : this.ProjectId\n    },\n    // 如果是工厂获取质检节点\n    // getFactoryNode() {\n    //   GetProcessCodeList({sys_workobject_id:this.Check_Object_Id}).then((res) => {\n    //     if (res.IsSucceed) {\n    //       let CheckJson = res.Data;\n    //       CheckJson.push({ Name: \"入库\" }, { Name: \"出库\" });\n    //       console.log(CheckJson);\n    //       this.QualityNodeList = CheckJson;\n    //       console.log(this.QualityNodeList);\n    //     } else {\n    //       this.$message({\n    //         type: \"error\",\n    //         message: res.Message,\n    //       });\n    //     }\n    //   });\n    // },\n\n    // 质检节点获取质检节点名\n    changeNodeCode(val) {\n      this.form = {\n        Node_Code: '',\n        Change_Check_Type: [],\n        Display_Name: '',\n        TC_UserId: '',\n        ZL_UserId: '',\n        TC_UserIds: [],\n        ZL_UserIds: [],\n        Check_Style: ''\n      }\n      this.form.Display_Name = val\n      this.form.Node_Code = null\n      // this.form.Change_Check_Type = [];\n      // try {\n      //   this.form.Node_Code = this.QualityNodeList.find((v) => {\n      //     return v.Name == val;\n      //   }).Id;\n      // } catch (err) {\n      //   this.form.Node_Code = null;\n      // }\n      // console.log\n      // let arr = {};\n      // arr = this.QualityNodeList.find((v) => {\n      //   return v.Name == val;\n      // });\n      // console.log(arr);\n      // if (arr) {\n      //   this.form.Check_Style = arr.Check_Style ? Number(arr.Check_Style) : \"\";\n      //   arr.Is_Need_TC &&(this.form.Change_Check_Type.push(2))\n      //   arr.Is_Need_ZL &&( this.form.Change_Check_Type.push(1));\n      //   this.SelectType(this.form.Change_Check_Type);\n\n      //   this.form.ZL_UserId = arr.ZL_Check_UserId ? arr.ZL_Check_UserId: \"\";\n      //   this.form.TC_UserId = arr.TC_Check_UserId ? arr.TC_Check_UserId : \"\"\n      //   console.log(this.form.ZL_UserId)\n      // }\n    },\n    changeZLUser(val) {\n      console.log(val)\n      // 解决下拉框回显问题\n      this.$forceUpdate()\n      this.form.ZL_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i === val.length - 1) {\n          this.form.ZL_UserId += val[i]\n        } else {\n          this.form.ZL_UserId += val[i] + ','\n        }\n      }\n      console.log(this.form.ZL_UserId, 'this.form.ZL_UserId ')\n    },\n    changeTCUser(val) {\n      this.$forceUpdate()\n      this.form.TC_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i === val.length - 1) {\n          this.form.TC_UserId += val[i]\n        } else {\n          this.form.TC_UserId += val[i] + ','\n        }\n      }\n      // 解决下拉框回显问题\n\n      console.log(this.form.TC_UserId, 'this.form.TC_UserId ')\n    },\n    getEntityNode(data) {\n      GetEntityNode({ id: data.Id }).then((res) => {\n        if (res.IsSucceed) {\n          console.log(res.Data)\n          this.form = res.Data[0]\n          this.form.Change_Check_Type = []\n          if (this.form.Check_Type === 1 || this.form.Check_Type === 2) {\n            this.form.Change_Check_Type.push(this.form.Check_Type)\n          } else if (this.form.Check_Type === -1) {\n            this.form.Change_Check_Type = [1, 2]\n          } else {\n            this.form.Change_Check_Type = []\n          }\n          this.form.ZL_UserIds = this.form.ZL_UserId ? this.form.ZL_UserId.split(',') : []\n          this.form.TC_UserIds = this.form.TC_UserId ? this.form.TC_UserId.split(',') : []\n          console.log(this.form.ZL_UserIds, this.form.TC_UserId)\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    handleSubmit(form) {\n      this.$refs[form].validate((valid) => {\n        if (valid) {\n          this.addCheckNode()\n        } else {\n          return false\n        }\n      })\n    },\n    handleInputFormat(value, dp, type) {\n      // 如果输入为空，直接返回空\n      if (value === '' || value === null || value === undefined) {\n        return ''\n      }\n\n      // 转换为字符串进行处理\n      let inputValue = String(value)\n\n      // 移除所有非数字和非小数点的字符（包括负号）\n      inputValue = inputValue.replace(/[^0-9.]/g, '')\n\n      // 如果只是单独的小数点，返回空\n      if (inputValue === '.') {\n        return ''\n      }\n\n      // 确保只有一个小数点\n      const dotCount = (inputValue.match(/\\./g) || []).length\n      if (dotCount > 1) {\n        // 如果有多个小数点，只保留第一个\n        const firstDotIndex = inputValue.indexOf('.')\n        inputValue = inputValue.substring(0, firstDotIndex + 1) + inputValue.substring(firstDotIndex + 1).replace(/\\./g, '')\n      }\n\n      // 根据 dp 参数限制小数位数\n      if (inputValue.includes('.') && dp) {\n        const parts = inputValue.split('.')\n        if (parts[1] && parts[1].length > dp) {\n          inputValue = parts[0] + '.' + parts[1].substring(0, dp)\n        }\n      }\n\n      // 如果处理后为空字符串，返回空\n      if (inputValue === '') {\n        return ''\n      }\n\n      // 转换为数字进行范围检查\n      const numValue = parseFloat(inputValue)\n\n      // 如果不是有效数字，返回空\n      if (isNaN(numValue)) {\n        return ''\n      }\n\n      // 最小值限制为0\n      if (numValue < 0) {\n        return '0'\n      }\n\n      // 如果有 type 参数，限制最大值\n      if (type && numValue > type) {\n        // 根据 dp 格式化最大值\n        if (dp) {\n          return type.toFixed(dp).replace(/\\.?0+$/, '')\n        } else {\n          return type.toString()\n        }\n      }\n\n      // 返回处理后的值\n      return inputValue\n    }\n  }\n}\n</script>\n\n<style scoped></style>\n"]}]}
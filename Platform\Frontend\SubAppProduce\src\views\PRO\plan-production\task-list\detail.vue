<template>
  <div class="abs100 cs-z-flex-pd16-wrap">
    <div class="top-btn" @click="toBack">
      <el-button>返回</el-button>
    </div>
    <div class="cs-z-page-main-content">
      <el-form ref="form" :model="queryForm" inline label-width="130px">
        <el-row>
          <el-col :span="20">
            <el-form-item label="排产单号" prop="Schduling_Code">
              {{ info.Schduling_Code }}
            </el-form-item>
            <el-form-item label="任务单号" prop="Task_Code">
              {{ info.Task_Code }}
            </el-form-item>
            <el-form-item label="项目名称" prop="projectId">
              {{ info.Project_Name }}
            </el-form-item>
            <el-form-item label="区域名称" prop="areaId">
              {{ info.Area_Name }}
            </el-form-item>
            <el-form-item v-if="!isVersionFour" label="批次" prop="install">
              {{ info.InstallUnit_Name }}
            </el-form-item>
            <!-- <br /> -->
            <el-form-item label="任务下达时间" prop="Order_Date">
              {{ info.Order_Date || '-' }}
            </el-form-item>
            <el-form-item label="工序计划开始时间" prop="Process_Start_Date">
              {{ info.Process_Start_Date || '-' }}
            </el-form-item>
            <el-form-item label="工序计划完成时间" prop="Process_Finish_Date">
              {{ info.Process_Finish_Date || '-' }}
            </el-form-item>
            <el-form-item label="完成时间" prop="Finish_Date2">
              {{ info.Finish_Date2 || '-' }}
            </el-form-item>
            <el-form-item label="备注" prop="Remark">
              {{ Remark || '-' }}
            </el-form-item>
            <el-form-item label="建议设备" prop="eqptInfoListStr">
              {{ eqptInfoListStr || '-' }}
            </el-form-item>
          </el-col>
          <el-col :span="4" style="margin-top: 12px;">
            <qrcode-vue ref="qrcodeRef" :size="79" :value="`T=${info.Task_Code}&C=${Tenant_Code}`" class-name="qrcode" level="H" />
          </el-col>
        </el-row>
        <br>
        <el-form-item label="下道工序" prop="Next_Process_Id">
          <el-select
            v-model="queryForm.Next_Process_Id"
            clearable
            placeholder="请选择"
            class="w100"
          >
            <el-option
              v-for="item in processOption"
              :key="item.Id"
              :label="item.Name"
              :value="item.Id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="下道班组" prop="Next_Team_Id">
          <el-select
            v-model="queryForm.Next_Team_Id"
            clearable
            placeholder="请选择"
            class="w100"
          >
            <el-option
              v-for="item in groupOption"
              :key="item.Id"
              :label="item.Name"
              :value="item.Id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search(1)">搜索</el-button>
          <el-button @click="handleReset()">重置</el-button>
          <el-button
            :disabled="!multipleSelection.length"
            @click="printEvent()"
          >打印
          </el-button>
          <el-button
            type="success"
            @click="handleExport"
          >导出
          </el-button>
          <el-button type="primary" @click="handleSuggestDeviceDialog()">设备详情</el-button>
        </el-form-item>
      </el-form>
      <!--      <el-divider />-->
      <div class="tb-x">
        <vxe-table
          ref="xTable"
          :empty-render="{name: 'NotData'}"
          show-header-overflow
          :print-config="printConfig"
          :row-config="{ isCurrent: true, isHover: true }"
          class="cs-vxe-table"
          align="left"
          height="auto"
          show-overflow
          :loading="tbLoading"
          stripe
          size="medium"
          :data="tbData"
          resizable
          :tooltip-config="{ enterable: true }"
          :cell-class-name="cellClassName"
          @checkbox-all="tbSelectChange"
          @checkbox-change="tbSelectChange"
          @cell-click="cellClickEvent"
        >
          <vxe-column type="checkbox" align="center" />
          <template v-for="item in columns">
            <vxe-column
              :key="item.Id"
              :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
              show-overflow="tooltip"
              sortable
              :align="item.Align"
              :field="item.Code"
              :title="item.Display_Name"
              :min-width="item.Width"
            />
          </template>
        </vxe-table>
      </div>
    </div>
    <el-dialog
      v-dialogDrag
      class="plm-custom-dialog"
      :title="title"
      :visible.sync="deviceDialogVisible"
      :width="dWidth"
      :close-on-click-modal="false"
      top="10vh"
      @close="handleClose"
    >
      <component
        :is="currentComponent"
        ref="content"
        @close="handleClose"
      />
    </el-dialog>
    <el-dialog
      v-dialogDrag
      class="plm-custom-dialog"
      title="转移详情"
      :visible.sync="dialogVisible"
      width="576px"
      @close="handleClose"
    >
      <TransferDetail ref="TransferDetail" />
    </el-dialog>
  </div>
</template>

<script>
import getTbInfo from '@/mixins/PRO/get-table-info'
import { GetTeamTaskDetails, ExportTaskCodeDetails, GetSuggestDeviceAndRemark } from '@/api/PRO/production-task'
import { debounce, closeTagView } from '@/utils'
import QrcodeVue from 'qrcode.vue'
import {
  GetProcessList,
  GetWorkingTeamsPageList
} from '@/api/PRO/technology-lib'
// import { GetProcessList } from '@/api/PRO/technology-lib'
import TransferDetail from './transferDetail'
// import { Export } from 'vxe-table'
import { combineURL } from '@/utils'
import { mapGetters } from 'vuex'
import SuggestDevice from './suggestDevice'
import { getBomCode, getBomName, checkIsUnitPart } from '@/views/PRO/bom-setting/utils'
const printStyle = `
        .title {
          text-align: center;
        }
        .is--print{
          box-sizing: border-box;
          width:95% !important;
          margin:0 auto !important;
        }
        .my-list-row {
          display: inline-block;
          width: 100%;
          margin-left:3%;
        }
        .my-list-row-first {
          margin-bottom: 10px;
        }
        .my-list-col {
          width:30%;
          display: inline-block;
          float: left;
          margin-right: 1%;
          word-wrap:break-word;
          word-break:normal;
        }
        .left{
          flex:1;
        }
        .my-top {
          display:flex;
          font-size: 12px;
          margin-bottom: 5px;
        }
        .qrcode{
          margin-right:10px
        }
        .cs-img{
          position:relative;
          right:30px
        }
        `

export default {
  name: 'PROTaskListDetail',
  components: {
    TransferDetail,
    QrcodeVue,
    SuggestDevice
  },
  mixins: [getTbInfo],
  data() {
    return {
      bomName: '',
      deviceDialogVisible: false,
      dialogVisible: false,
      queryForm: {
        // Project_Id: '',
        // Area_Id: '',
        // InstallUnit_Id: '',
        Next_Process_Id: '',
        Next_Team_Id: ''
      },
      queryInfo: {
        Page: 1,
        PageSize: -1
      },
      tbConfig: {
        Op_Width: 120
      },
      tbLoading: false,
      columns: [],
      multipleSelection: [],
      tbData: [],
      finishList: [],
      processOption: [],
      groupOption: [],
      total: 0,
      printColumns: [],
      search: () => ({}),
      pageType: '',
      info: {
        Task_Code: '',
        Project_Name: '',
        Area_Name: '',
        InstallUnit_Name: '',
        Schduling_Code: '',
        Task_Finish_Date: '',
        Finish_Date2: '',
        Order_Date: '',
        Working_Team_Name: '',
        Working_Process_Name: '',
        Process_Start_Date: '',
        Process_Finish_Date: ''
      },
      printConfig: {
        sheetName: '任务单详情',
        style: printStyle,
        beforePrintMethod: ({ content }) => {
          return this.topHtml + content
        }
      },
      Tenant_Code: localStorage.getItem('tenant'),
      Remark: '',
      eqptInfoList: [],
      eqptInfoListStr: ''
    }
  },
  computed: {
    ...mapGetters('tenant', ['isVersionFour']),
    isCom() {
      return this.pageType === getBomCode('-1')
    },
    isUnitPart() {
      return checkIsUnitPart(this.pageType)
    },
    isPart() {
      return this.pageType === getBomCode('0')
    }
  },
  async mounted() {
    this.pageType = this.$route.query.type
    this.bomName = await getBomName(this.pageType)
    this.workTeamId = this.$route.query.tid
    this.taskCode = this.$route.query.id
    this.info = JSON.parse(decodeURIComponent(this.$route.query.other))
    await this.getSuggestDeviceAndRemark()
    await this.getTableConfig(this.isCom ? 'PROComTaskListDetail' : this.isUnitPart ? 'PROUnitPartTaskListDetail' : 'PROPartTaskListDetail')
    if (this.isCom) {
      const idx = this.columns.findIndex((item) => item.Code === 'Part_Code')
      idx !== -1 && this.columns.splice(idx, 1)

      this.printColumns = this.columns.filter(column => column.Code !== 'Comp_Description')
    }
    if (this.isUnitPart) {
      this.printColumns = this.columns.filter(column => column.Code !== 'Project_Name' && column.Code !== 'Area_Name' && column.Code !== 'Finish_Count' && column.Code !== 'Finish_Weight' && column.Code !== 'Comp_Description')
    }
    if (this.isPart) {
      this.printColumns = this.columns.filter(column => column.Code !== 'Project_Name' && column.Code !== 'Area_Name' && column.Code !== 'Finish_Count' && column.Code !== 'Finish_Weight' && column.Code !== 'Comp_Description')
    }

    // else {
    //   const idx = this.columns.findIndex((item) => item.Code === 'Comp_Code')
    //   idx !== -1 && this.columns.splice(idx, 1)
    // }
    this.search = debounce(this.fetchData, 800, true)
    this.fetchData()
    this.getProcessOption()
    this.getAllTeamOption()
    this.getHtml()
  },
  methods: {
    getSuggestDeviceAndRemark() {
      GetSuggestDeviceAndRemark({
        Bom_Level: this.pageType,
        Task_Code: this.info.Task_Code
      }).then((res) => {
        if (res.IsSucceed) {
          this.Remark = res.Data?.Remark
          this.eqptInfoList = res.Data?.eqptInfoList || []
          this.eqptInfoListStr = this.eqptInfoList.map(item => item.DisplayName).join(',')
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    tbSelectChange(array) {
      this.multipleSelection = array.records
    },
    // 导出
    handleExport() {
      console.log(this.info)
      ExportTaskCodeDetails({
        Process_Type: this.isCom ? 2 : this.isPart ? 1 : 3, // 1零件，2构件
        Working_Team_Id: this.workTeamId,
        Task_Code: this.taskCode
      }).then((res) => {
        if (res.IsSucceed) {
          this.$message({
            message: '导出成功',
            type: 'success'
          })
          window.open(combineURL(this.$baseUrl, res.Data), '_blank')
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
      // const filterVal = this.columns.map((v) => v.Code)
      // const data = formatJson(filterVal, this.multipleSelection)
      // const header = this.columns.map((v) => v.Display_Name)
      // import('@/vendor/Export2Excel').then((excel) => {
      //   excel.export_json_to_excel({
      //     header: header,
      //     data,
      //     filename: `${
      //       this.info?.Task_Code ||
      //       (this.isCom ? '构件任务单详情' : '零件任务单详情')
      //     }`,
      //     autoWidth: true,
      //     bookType: 'xlsx'
      //   })
      // })
      // function formatJson(filterVal, jsonData) {
      //   return jsonData.map((v) => filterVal.map((j) => v[j]))
      // }
    },
    fetchData(page) {
      page && (this.queryInfo.Page = page)
      console.log(this.queryInfo, 'this.queryInfo')
      this.tbLoading = true
      GetTeamTaskDetails({
        // ...this.queryInfo,
        Bom_Level: this.pageType,
        Page: -1,
        PageSize: -1,
        Process_Type: this.isCom ? 2 : this.isPart ? 1 : 3, // 1零件，2构件
        Working_Team_Id: this.workTeamId,
        Task_Code: this.taskCode,
        Next_Team_Id: this.queryForm.Next_Team_Id,
        Next_Process_Id: this.queryForm.Next_Process_Id
      })
        .then((res) => {
          if (res.IsSucceed) {
            this.tbData = res.Data.Data.filter(item => {
              return item.Allocation_Count !== 0
            })
            // this.total = res.Data.TotalCount
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
        .finally((_) => {
          this.tbLoading = false
        })
    },
    handleReset() {
      this.$refs['form'].resetFields()
      this.search(1)
    },
    getProcessOption() {
      GetProcessList({
        type: this.isCom ? 1 : this.isPart ? 2 : 3, // 1构件，2零件
        Bom_Level: this.pageType
      }).then((res) => {
        if (res.IsSucceed) {
          this.processOption = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    getAllTeamOption() {
      GetWorkingTeamsPageList({
        pageInfo: { Page: -1, PageSize: -1, ParameterJson: [] }
      }).then((res) => {
        if (res.IsSucceed) {
          this.groupOption = res.Data.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    getHtml() {
      // ${this.printConfig.sheetName}
      const qr = this.$refs['qrcodeRef']
      return new Promise((resolve, reject) => {
        this.$nextTick(_ => {
          const canvas = qr.$refs['qrcode-vue']
          const dataURL = canvas.toDataURL('image/png')
          this.topHtml = `
        <h1 class="title">#${this.info.Working_Process_Name || ''}# 加工任务单</h1>
        <div class="my-top">
          <div class="left">
            <div class="my-list-row my-list-row-first">
              <div class="my-list-col">项目名称/区域：${this.info.Project_Name || ''}/${this.info.Area_Name || ''}</div>
              <div class="my-list-col">排产单号：${this.info.Schduling_Code || ''}</div>
              <div class="my-list-col">加工班组：${this.info.Working_Team_Name || ''}</div>
            </div>
            <div class="my-list-row">
              <div class="my-list-col">任务下单时间：${this.info.Order_Date || ''}</div>
              <div class="my-list-col">任务单号：${this.info?.Task_Code || ''}</div>
              <div class="my-list-col">工序计划完成时间：${this.info.Process_Finish_Date || ''}</div>
            </div>
          </div>
          <div class="right">
           <img class="cs-img" src="${dataURL}" alt="">
          </div>
        </div>
        `
          resolve()
        })
      })
    },
    printEvent() {
      this.getHtml().then((_) => {
        console.log(this.printConfig.sheetName, 'this.printConfig.sheetName')
        this.$refs.xTable.print({
          sheetName: this.printConfig.sheetName,
          style: printStyle,
          mode: 'selected',
          columns: this.printColumns.map((v) => {
            return {
              field: v.Code
            }
          }),
          beforePrintMethod: ({ content }) => {
            // 拦截打印之前，返回自定义的 html 内容
            const result = this.topHtml + content
            console.log('result', result)
            return result
          }
        })
      })
    },
    handleView(row) {
    },
    getInnerTable(row, column) {
      const ob = {
        num: 1,
        date: '2022-23-52'
      }
      for (let i = 0; i < 50; i++) {
        this.finishList.push(ob)
      }
    },
    toBack() {
      closeTagView(this.$store, this.$route)
    },

    // 关闭弹窗
    handleClose() {
      this.$refs.TransferDetail.resetForm()
      this.dialogVisible = false
      this.deviceDialogVisible = false
    },

    // 单元格点击时间
    cellClickEvent({ row, rowIndex, column, columnIndex }) {
      // if (column.property === "Finish_Count" && row.Finish_Count > 0) {
      //   this.dialogVisible = true;
      //   this.$nextTick(() => {
      //     this.$refs.TransferDetail.init(row, this.isCom);
      //   });
      // }
    },

    // 改变单元格样式
    cellClassName({ row, rowIndex, column, columnIndex }) {
      // if (column.property === "Finish_Count") {
      //   return "col-blue";
      // }
    },

    handleSuggestDeviceDialog() {
      this.title = `生产设备详情`

      this.currentComponent = 'SuggestDevice'
      this.dWidth = '800px'
      this.deviceDialogVisible = true

      this.$nextTick(_ => {
        this.$refs['draft'].initData()
      })
    }
  }
}
</script>

<style scoped lang="scss">
.el-divider {
  margin: 0 0 10px;
}

.tb-x {
  flex: 1;
  overflow:auto;

  ::v-deep {
    .cs-vxe-table .vxe-body--column.col-blue {
      color: #298dff;
      cursor: pointer;
    }
  }
}

.cs-z-flex-pd16-wrap {
  padding-top: 50px;

  .top-btn {
    position: absolute;
    top: 12px;
    left: 20px;
    z-index: 99;

    .el-button {
      background-color: #f7f8f9;
    }
  }

  .cs-z-page-main-content {
    overflow:hidden;
    ::v-deep {
      .el-form-item__content {
        min-width: 200px;
      }
    }
  }
}
</style>

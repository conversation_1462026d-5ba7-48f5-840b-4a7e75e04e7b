<template>
  <div>
    <el-row v-for="(info,index) in list" :key="info.id" class="item-x">
      <div class="item">
        <label>
          属性名称
          <el-select
            v-model="info.key"
            style="width: calc(100% - 65px)"
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in filterOption(info.key)"
              :key="item.key"
              :label="item.label"
              :value="item.key "
            />
          </el-select>
        </label>
      </div>
      <div class="item" style="line-height: 32px;">
        <label>请输入值
          <el-input-number v-if="checkType(info.key,'number')" v-model="info.val" :min="0" class="cs-number-btn-hidden" />
          <el-input v-if="checkType(info.key,'string')" v-model="info.val" />
          <el-select
            v-if="checkType(info.key,'array') && info.key==='Is_Component'"
            v-model="info.val"
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in Is_Component_Data"
              :key="item.Id"
              :label="item.Name"
              :value="item.Id"
            />
          </el-select>
          <el-tree-select
            v-show="checkType(info.key,'array') && info.key==='SteelType'"
            ref="treeSelect"
            v-model="info.val"
            :tree-params="treeParams"
            style="width: 100%;display: inline-block"
            @node-click="steelTypeChange"
          />
        </label>
      </div>
      <span v-if="index === 0" class="item-span">
        <i class="el-icon-circle-plus-outline" @click="handleAdd" />
      </span>
      <span v-else class="item-span">
        <i class="el-icon-circle-plus-outline" @click="handleAdd" />
        <i class="el-icon-remove-outline txt-red" @click="handleDelete(index)" />
      </span>
    </el-row>
    <div style="text-align: right;width: 100%;padding: 20px 2% 0 0">
      <el-button @click="$emit('close')">取消</el-button>
      <el-button type="primary" :loading="btnLoading" @click="onSubmit">确定</el-button>
    </div>
  </div>
</template>

<script>
import { v4 as uuidv4 } from 'uuid'
import { GetGridByCode } from '@/api/sys'
import { BatchUpdateComponentImportInfo } from '@/api/PRO/component'
import { GetCompTypeTree } from '@/api/PRO/component-type'
import { GetUserableAttr } from '@/api/PRO/professionalType'

export default {
  props: {
    typeEntity: {
      type: Object,
      default: () => {}
    },
    projectId: {
      type: String,
      default: ''
    },
    sysProjectId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      btnLoading: false,
      treeParams: {
        'default-expand-all': true,
        filterable: false,
        clickParent: true,
        data: [],
        props: {
          children: 'Children',
          label: 'Label',
          value: 'Data'
        }
      },
      Is_Component_Data: [{ Name: '是', Id: '否' }, { Name: '否', Id: '是' }], // 是否是直发件数据
      Is_Component: '',
      value: '',
      options: [
        {
          key: 'SetupPosition',
          label: '批次',
          type: 'string'
        },
        {
          key: 'SteelSpec',
          label: '规格',
          type: 'string'
        }, {
          key: 'SteelWeight',
          label: '单重',
          type: 'number'
        }, {
          key: 'SteelLength',
          label: '长度',
          type: 'number'
        }, {
          key: 'SteelAmount',
          label: '深化数量',
          type: 'number'
        }, {
          key: 'SteelMaterial',
          label: '材质 ',
          type: 'string'
        }, {
          key: 'Is_Component',
          label: '是否直发件',
          type: 'array'
        }, {
          key: 'Remark',
          label: '备注',
          type: 'string'
        },
        {
          key: 'GrossWeight',
          label: '单毛重',
          type: 'number'
        }
      ],
      list: [{
        id: uuidv4(),
        val: undefined,
        key: ''
      }]
    }
  },
  async mounted() {
    await this.getCompTypeTree()
    await this.getUserableAttr()
    const codeArr = this.options.filter((item, index) => index).map(i => i.key)
    const columns = await this.convertCode(this.typeEntity.Code, codeArr)
    this.options = this.options.map((item, index) => {
      if (index) {
        item.label = columns.filter((v) => v.Is_Display).find(i => i.Code === item.key)?.Display_Name
      }
      return item
    })
  },
  methods: {
    // 获取拓展字段
    async getUserableAttr() {
      await GetUserableAttr({
        IsComponent: true,
        Bom_Level: -1
      }).then(res => {
        if (res.IsSucceed) {
          const resData = res.Data
          const expandData = []
          resData.forEach(item => {
            const expandJson = {}
            expandJson.key = item.Code
            expandJson.lable = item.Display_Name
            expandJson.type = 'string'
            expandData.push(expandJson)
          })
          this.options = this.options.concat(expandData)
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    // 获取构件类型
    async getCompTypeTree() {
      await GetCompTypeTree({
        professional: this.typeEntity.Code
      }).then(res => {
        if (res.IsSucceed) {
          this.$refs.treeSelect.forEach(item => {
            item.treeDataUpdateFun(res.Data)
          })
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
          this.treeData = []
        }
      }).finally(_ => {
      })
    },

    handleAdd() {
      this.list.push({
        id: uuidv4(),
        val: undefined,
        key: ''
      })
      this.getCompTypeTree()
    },

    handleDelete(index) {
      this.list.splice(index, 1)
    },

    async onSubmit() {
      this.btnLoading = true
      const obj = {}
      for (let i = 0; i < this.list.length; i++) {
        const element = this.list[i]
        if (!element.val) {
          if (element.key === 'SteelWeight' || element.key === 'SteelLength' || element.key === 'SteelAmount' || element.key === 'GrossWeight') {
            element.val === 0 ? this.$message({ message: '值不能为0', type: 'warning' }) : this.$message({ message: '值不能为空', type: 'warning' })
          } else {
            this.$message({
              message: '值不能为空',
              type: 'warning'
            })
          }
          this.btnLoading = false
          return
        }
        // obj[element.key] = element.key=='Is_Component' ? eval(element.val.toLowerCase()) : element.val; // "true"转true
        obj[element.key] = element.val
      }
      // if (!obj.hasOwnProperty('Is_Component') && obj.hasOwnProperty('SteelType')) {
      //   obj['Is_Component'] = this.Is_Component
      // }
      await BatchUpdateComponentImportInfo({
        Ids: this.selectList.map(v => v.Id).toString(),
        EditInfo: obj
      }).then(res => {
        if (res.IsSucceed) {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.$emit('close')
          this.$emit('refresh')
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      }).finally(() => {
        this.btnLoading = false
      })
    },

    filterOption(currentValue) {
      console.log(currentValue)
      return this.options.filter(k => {
        return (!this.list.map(v => v.key).includes(k.key) || k.key === currentValue) && k.label
      })
    },

    checkType(key, type) {
      if (!key) return false
      return this.options.find(v => v.key === key).type === type
    },

    init(list, columnsOption) {
      console.log(list)
      this.selectList = list

    //   let filterarr = columnsOption.filter(v=> {
    //   return v.Display_Name != "项目名称" && v.Display_Name != "区域" && v.Display_Name != "批次" && v.Code != "SteelAllWeight" &&  v.Code != "SteelName" && v.Code != "TotalGrossWeight" && v.Code != "SteelType" && v.Code != "Part" && v.Code != "SchedulingNum"  && v.Code != "Create_UserName" && v.Code === "Is_Component_Status"
    //  })
    //  this.options = filterarr?.map(item => ({ key: item.Code, label: item.Display_Name, type: item.Code === "Is_Component" ?"array": item.Code === "SteelWeight" || item.Code === "GrossWeight" || item.Code === "SteelLength" || item.Code === "SchedulingNum"  ? "number" : "string"}))
      // this.options = columnsOption?.map(v => ({ key: v.Code, label: v.Display_Name, type: v.Code === "Is_Component" ? "array": v.Code === "SteelWeight" ||  v.Code === "SteelAllWeight" ? 'number' : 'string' }))
    },

    // 获取配置数据
    async getColumnConfiguration(code, mainType = 'plm_component_page_list') {
      const res = await GetGridByCode({ code: mainType + ',' + code })
      return res.Data.ColumnList
    },

    // 根据Code（数据）获取名称
    async convertCode(typeCode, propsArr = [], mainType) {
      console.log(propsArr)
      const SchedulArr = this.selectList.filter((item) => {
        return item.SteelType !== null && item.SteelType !== ''
      })
      const propsArrfilter = SchedulArr.length > 0 ? propsArr.filter((v) => v !== 'Is_Component') : propsArr
      const props = await this.getColumnConfiguration(typeCode, mainType)
      console.log(props)
      const columns = props.filter(i => {
        const arr = propsArrfilter.map(i => i.toLowerCase())
        return arr.includes(i.Code.toLowerCase())
      })
      console.log(columns)
      return columns
    },

    // 选择构件类型
    steelTypeChange(e) {
      this.Is_Component = e.Code == 'true' ? '是' : '否'
    }
  }
}
</script>

<style scoped lang="scss">
[class^="el-icon"] {
  font-size: 24px;
  vertical-align: middle;
  cursor: pointer;
  margin-left: 15px;
}

.item-x {
  display: flex;
  margin-bottom: 20px;
  flex: 0 1 50%;
  justify-content: space-between;

  .item {
    width: 45%;
    white-space: nowrap;
    &:not(:first-of-type) {
      margin-left: 20px;
      .cs-number-btn-hidden,.el-input,.el-select{
        width: 80%;
      }
    }
  }

  .item-span {
    width: 90px;
    padding-top: 5px;
  }

}
  ::v-deep{
    .el-tree-select-input{
      width: 80%!important;
    }
  }
</style>

{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\detailPrint.vue?vue&type=style&index=0&id=00cf5b55&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\detailPrint.vue", "mtime": 1758595482001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5lbC1kaXZpZGVyIHsKICBtYXJnaW46IDAgMCAxMHB4Owp9CgoudGIteCB7CiAgZmxleDogMTsKICBvdmVyZmxvdzphdXRvOwoKICA6OnYtZGVlcCB7CiAgICAuY3MtdnhlLXRhYmxlIC52eGUtYm9keS0tY29sdW1uLmNvbC1ibHVlIHsKICAgICAgY29sb3I6ICMyOThkZmY7CiAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgIH0KICB9Cn0KCi5jcy16LWZsZXgtcGQxNi13cmFwIHsKICBwYWRkaW5nLXRvcDogNTBweDsKICAudG9wLWJ0biB7CiAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICB0b3A6IDEycHg7CiAgICBsZWZ0OiAyMHB4OwogICAgei1pbmRleDogOTk7CgogICAgLmVsLWJ1dHRvbiB7CiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmN2Y4Zjk7CiAgICB9CiAgfQogIC50b3AtYnRuLXByaW50IHsKICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgIHRvcDogMTJweDsKICAgIHJpZ2h0OiAyMHB4OwogICAgei1pbmRleDogOTk7CiAgfQoKICAuY3Mtei1wYWdlLW1haW4tY29udGVudCB7CiAgICBvdmVyZmxvdy15OiBhdXRvOwogICAgOjp2LWRlZXAgewogICAgICAuZWwtZm9ybS1pdGVtX19jb250ZW50IHsKICAgICAgICBtaW4td2lkdGg6IDIwMHB4OwogICAgICB9CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["detailPrint.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqaA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detailPrint.vue", "sourceRoot": "src/views/PRO/plan-production/task-list", "sourcesContent": ["<template>\n  <div v-loading=\"tbLoading\" element-loading-text=\"打印数据生成中\" class=\"abs100 cs-z-flex-pd16-wrap\">\n    <div class=\"top-btn\" @click=\"toBack\">\n      <el-button>返回</el-button>\n    </div>\n    <div class=\"top-btn-print\" @click=\"printEvent\">\n      <el-button type=\"primary\">打印</el-button>\n    </div>\n    <div class=\"cs-z-page-main-content\">\n      <!-- <div v-for=\"(item, index) in printData\" :key=\"index\" style=\"height: 100%; display: flex; flex-direction: column;\"> -->\n      <el-form ref=\"form\" inline label-width=\"140px\">\n        <el-row>\n          <el-col :span=\"20\">\n            <el-form-item label=\"项目名称/区域：\">\n              {{ info.Project_Name }}/{{ info.Area_Name }}\n            </el-form-item>\n            <el-form-item label=\"排产单号：\">\n              {{ info.Schduling_Code }}\n            </el-form-item>\n            <el-form-item label=\"加工班组：\">\n              {{ info.Working_Team_Name }}\n            </el-form-item>\n            <el-form-item label=\"任务下达时间：\">\n              {{ info.Order_Date }}\n            </el-form-item>\n            <el-form-item label=\"任务单号：\">\n              {{ info.Task_Code }}\n            </el-form-item>\n            <el-form-item label=\"工序计划开始时间\" prop=\"Process_Start_Date\">\n              {{ info.Process_Start_Date || '-' }}\n            </el-form-item>\n            <el-form-item label=\"工序计划完成时间\" prop=\"Process_Finish_Date\">\n              {{ info.Process_Finish_Date || '-' }}\n            </el-form-item>\n            <el-form-item label=\"备注\" prop=\"Remark\">\n              {{ Remark || '-' }}\n            </el-form-item>\n            <el-form-item label=\"建议设备\" prop=\"eqptInfoListStr\">\n              {{ eqptInfoListStr || '-' }}\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n            <qrcode-vue ref=\"qrcodeRef\" :size=\"79\" :value=\"`T=${info.Task_Code}&C=${Tenant_Code}`\" class-name=\"qrcode\" level=\"H\" />\n          </el-col>\n        </el-row>\n      </el-form>\n      <div class=\"tb-x\">\n        <vxe-table\n          ref=\"xTable\"\n          :empty-render=\"{name: 'NotData'}\"\n          show-header-overflow\n          :print-config=\"printConfig\"\n          :row-config=\"{ isCurrent: true, isHover: true }\"\n          class=\"cs-vxe-table\"\n          align=\"left\"\n          height=\"auto\"\n          show-overflow\n          :loading=\"tbLoading\"\n          stripe\n          size=\"medium\"\n          :data=\"tbData\"\n          resizable\n          :tooltip-config=\"{ enterable: true }\"\n          :cell-class-name=\"cellClassName\"\n          @cell-click=\"cellClickEvent\"\n        >\n          <template v-for=\"column in columns\">\n            <vxe-column\n              :key=\"column.Id\"\n              :fixed=\"column.Is_Frozen ? column.Frozen_Dirction : ''\"\n              show-overflow=\"tooltip\"\n              sortable\n              :align=\"column.Align\"\n              :field=\"column.Code\"\n              :title=\"column.Display_Name\"\n              :min-width=\"column.Width\"\n            />\n          </template>\n        </vxe-table>\n      </div>\n      <!-- </div> -->\n    </div>\n  </div>\n</template>\n\n<script>\nimport getTbInfo from '@/mixins/PRO/get-table-info'\nimport { GetTeamTaskDetails, GetSuggestDeviceAndRemark } from '@/api/PRO/production-task'\nimport { closeTagView } from '@/utils'\nimport QrcodeVue from 'qrcode.vue'\nimport { mapGetters } from 'vuex'\nimport { getBomCode, getBomName, checkIsUnitPart } from '@/views/PRO/bom-setting/utils'\n\nconst printStyle = `\n        .title {\n          text-align: center;\n        }\n        .is--print{\n          box-sizing: border-box;\n          width:95% !important;\n          margin:0 auto !important;\n        }\n        .my-list-row {\n          display: inline-block;\n          width: 100%;\n          margin-left:3%;\n        }\n        .my-list-row-first {\n          margin-bottom: 10px;\n        }\n        .my-list-row-second {\n          margin-bottom: 10px;\n        }\n        .my-list-row-third {\n          margin-bottom: 10px;\n        }\n        .my-list-col {\n          width:30%;\n          display: inline-block;\n          float: left;\n          margin-right: 1%;\n          word-wrap:break-word;\n          word-break:normal;\n        }\n        .left{\n          flex:1;\n        }\n        .my-top {\n          display:flex;\n          font-size: 12px;\n          margin-bottom: 5px;\n        }\n        .qrcode{\n          margin-right:10px\n        }\n        .cs-img{\n          position:relative;\n          right:30px\n        }\n        `\n\nexport default {\n  name: 'PROTaskListDetailPrint',\n  components: {\n    QrcodeVue\n  },\n  mixins: [getTbInfo],\n  data() {\n    return {\n      bomName: '',\n      printData: [],\n      tbConfig: {\n        Op_Width: 120\n      },\n      tbLoading: false,\n      tbData: [],\n      columns: [],\n      pageType: '',\n      command: '',\n      printColumns: [],\n      info: {\n        Task_Code: '',\n        Project_Name: '',\n        Area_Name: '',\n        InstallUnit_Name: '',\n        Schduling_Code: '',\n        Task_Finish_Date: '',\n        Finish_Date2: '',\n        Order_Date: '',\n        Working_Team_Name: '',\n        Working_Process_Name: '',\n        Process_Start_Date: '',\n        Process_Finish_Date: ''\n      },\n      printConfig: {\n        sheetName: '任务单详情',\n        style: printStyle,\n        beforePrintMethod: ({ content }) => {\n          return this.topHtml + content\n        }\n      },\n      Tenant_Code: localStorage.getItem('tenant'),\n      Remark: '',\n      eqptInfoList: [],\n      eqptInfoListStr: ''\n    }\n  },\n  computed: {\n    ...mapGetters('tenant', ['isVersionFour']),\n    isCom() {\n      return this.pageType === getBomCode('-1')\n    },\n    isUnitPart() {\n      return checkIsUnitPart(this.pageType)\n    },\n    isPart() {\n      return this.pageType === getBomCode('0')\n    }\n  },\n  async mounted() {\n    this.pageType = this.$route.query.type\n    this.command = this.$route.query.command\n    this.bomName = await getBomName(this.pageType)\n    this.info = JSON.parse(decodeURIComponent(this.$route.query.other))\n    await this.getSuggestDeviceAndRemark()\n    await this.getTableConfig(this.isCom ? 'PROComTaskListDetail' : this.isUnitPart ? 'PROUnitPartTaskListDetail' : 'PROPartTaskListDetail')\n    if (this.isCom) {\n      this.columns = this.columns.filter(item => item.Code !== 'Part_Code')\n      this.printColumns = this.columns.filter(item => item.Code !== 'Part_Code' && item.Code !== 'Comp_Description')\n      if (this.command === 'code') {\n        this.columns = this.columns.filter(item => item.Code !== 'Comp_Code' && item.Code !== 'Part_Code')\n        this.printColumns = this.columns.filter(item => item.Code !== 'Comp_Code' && item.Code !== 'Comp_Description' && item.Code !== 'Part_Code')\n      }\n    }\n\n    if (this.isUnitPart) {\n      this.printColumns = this.columns.filter(column => column.Code !== 'Project_Name' && column.Code !== 'Area_Name' && column.Code !== 'Finish_Count' && column.Code !== 'Finish_Weight' && column.Code !== 'Comp_Description')\n    }\n    if (this.isPart) {\n      this.printColumns = this.columns.filter(column => column.Code !== 'Project_Name' && column.Code !== 'Area_Name' && column.Code !== 'Finish_Count' && column.Code !== 'Finish_Weight' && column.Code !== 'Comp_Description')\n    }\n\n    const data = await this.fetchData(this.info.Task_Code, this.info.Working_Team_Id)\n    if (this.isCom && this.command === 'code') {\n      this.tbData = this.mergeSimilarItems(data)\n    } else {\n      this.tbData = data\n    }\n\n    this.getHtml()\n    this.printEvent()\n  },\n  methods: {\n    getSuggestDeviceAndRemark() {\n      GetSuggestDeviceAndRemark({\n        Bom_Level: this.pageType,\n        Task_Code: this.info.Task_Code\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.Remark = res.Data?.Remark\n          this.eqptInfoList = res.Data?.eqptInfoList || []\n          this.eqptInfoListStr = this.eqptInfoList.map(item => item.DisplayName).join(',')\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    async fetchData(Task_Code, Working_Team_Id, idx) {\n      this.tbLoading = true\n      const res = await GetTeamTaskDetails({\n        Page: -1,\n        PageSize: -1,\n        Bom_Level: this.pageType,\n        Process_Type: this.isCom ? 2 : this.isPart ? 1 : 3, // 1零件，2构件\n        Working_Team_Id: Working_Team_Id,\n        Task_Code: Task_Code,\n        Next_Team_Id: '',\n        Next_Process_Id: ''\n      })\n      if (res.IsSucceed) {\n        const data = res.Data.Data.filter(item => {\n          return item.Allocation_Count !== 0\n        })\n        this.tbLoading = false\n        return data\n      } else {\n        this.tbLoading = false\n      }\n    },\n\n    mergeSimilarItems(data) {\n      // 创建一个 Map 来存储合并后的数据，使用关键字段的组合作为键\n      const mergedMap = new Map()\n\n      // 遍历原始数据\n      for (const item of data) {\n        // 如果 Steel_Code 为空或 null，直接加入结果，不参与合并\n        if (item.Steel_Code == null || item.Steel_Code === '') {\n          // 确保数值字段有默认值\n          const newItem = {\n            ...item,\n            Allocation_Weight: item.Allocation_Weight || 0,\n            Finish_Weight: item.Finish_Weight || 0,\n            Allocation_Count: item.Allocation_Count || 0,\n            Finish_Count: item.Finish_Count || 0\n          }\n          // 使用一个唯一键（如索引）来避免覆盖\n          mergedMap.set(`null_${mergedMap.size}`, newItem)\n          continue\n        }\n\n        // 创建关键字段的组合作为键\n        const key = [\n          item.Steel_Code,\n          item.Spec,\n          item.Weight,\n          item.Main_Part,\n          item.AttachedBoardsNumber,\n          item.Length,\n          item.Texture,\n          item.Next_Process_Name,\n          item.Next_Working_Team_Names,\n          item.InstallUnit_Name\n        ].join('|')\n\n        // 检查是否已经有相同的记录\n        if (mergedMap.has(key)) {\n          // 获取已存在的记录\n          const existingItem = mergedMap.get(key)\n\n          // 累加数值字段\n          existingItem.Allocation_Weight += item.Allocation_Weight || 0\n          existingItem.Finish_Weight += item.Finish_Weight || 0\n          existingItem.Allocation_Count += item.Allocation_Count || 0\n          existingItem.Finish_Count += item.Finish_Count || 0\n        } else {\n          const newItem = {\n            ...item,\n            Allocation_Weight: item.Allocation_Weight || 0,\n            Finish_Weight: item.Finish_Weight || 0,\n            Allocation_Count: item.Allocation_Count || 0,\n            Finish_Count: item.Finish_Count || 0\n          }\n\n          // 如果是第一次出现，直接放入 Map\n          mergedMap.set(key, newItem)\n        }\n      }\n\n      // 将 Map 转换回数组\n      return Array.from(mergedMap.values())\n    },\n\n    handleReset() {\n      this.$refs['form'].resetFields()\n    },\n\n    getHtml() {\n      const qr = this.$refs['qrcodeRef']\n      return new Promise((resolve, reject) => {\n        this.$nextTick(_ => {\n          const canvas = qr.$refs['qrcode-vue']\n          const dataURL = canvas.toDataURL('image/png')\n          this.topHtml = `\n        <h1 class=\"title\">#${this.info.Working_Process_Name || ''}# 加工任务单</h1>\n        <div class=\"my-top\">\n          <div class=\"left\">\n            <div class=\"my-list-row my-list-row-first\">\n              <div class=\"my-list-col\">项目名称/区域：${this.info.Project_Name || ''}/${this.info.Area_Name || ''}</div>\n              <div class=\"my-list-col\">排产单号：${this.info.Schduling_Code || ''}</div>\n              <div class=\"my-list-col\">加工班组：${this.info.Working_Team_Name || ''}</div>\n            </div>\n            <div class=\"my-list-row my-list-row-second\">\n              <div class=\"my-list-col\">任务下单时间：${this.info.Order_Date || ''}</div>\n              <div class=\"my-list-col\">任务单号：${this.info?.Task_Code || ''}</div>\n              <div class=\"my-list-col\">工序计划开始时间：${this.info.Process_Start_Date || ''}</div>\n            </div>\n            <div class=\"my-list-row my-list-row-third\">\n              <div class=\"my-list-col\">工序计划完成时间：${this.info.Process_Finish_Date || ''}</div>\n              <div class=\"my-list-col\">备注：${this.Remark || ''}</div>\n            </div>\n            <div class=\"my-list-row\">\n              <div class=\"my-list-col\">建议设备：${this.eqptInfoListStr || ''}</div>\n            </div>\n          </div>\n          <div class=\"right\">\n           <img class=\"cs-img\" src=\"${dataURL}\" alt=\"\">\n          </div>\n        </div>\n        `\n          resolve()\n        })\n      })\n    },\n    printEvent() {\n      this.getHtml().then((_) => {\n        this.$refs.xTable.print({\n          sheetName: this.printConfig.sheetName,\n          style: printStyle,\n          mode: 'current',\n          columns: this.printColumns.map((v) => {\n            return {\n              field: v.Code\n            }\n          }),\n          beforePrintMethod: ({ content }) => {\n            const result = this.topHtml + content\n            return result\n          }\n        })\n      })\n    },\n    handleView(row) {\n    },\n    toBack() {\n      closeTagView(this.$store, this.$route)\n    },\n\n    // 单元格点击时间\n    cellClickEvent({ row, rowIndex, column, columnIndex }) {\n      // if (column.property === \"Finish_Count\" && row.Finish_Count > 0) {\n      //   this.$nextTick(() => {\n      //     this.$refs.TransferDetail.init(row, this.isCom);\n      //   });\n      // }\n    },\n\n    // 改变单元格样式\n    cellClassName({ row, rowIndex, column, columnIndex }) {\n      // if (column.property === \"Finish_Count\") {\n      //   return \"col-blue\";\n      // }\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.el-divider {\n  margin: 0 0 10px;\n}\n\n.tb-x {\n  flex: 1;\n  overflow:auto;\n\n  ::v-deep {\n    .cs-vxe-table .vxe-body--column.col-blue {\n      color: #298dff;\n      cursor: pointer;\n    }\n  }\n}\n\n.cs-z-flex-pd16-wrap {\n  padding-top: 50px;\n  .top-btn {\n    position: absolute;\n    top: 12px;\n    left: 20px;\n    z-index: 99;\n\n    .el-button {\n      background-color: #f7f8f9;\n    }\n  }\n  .top-btn-print {\n    position: absolute;\n    top: 12px;\n    right: 20px;\n    z-index: 99;\n  }\n\n  .cs-z-page-main-content {\n    overflow-y: auto;\n    ::v-deep {\n      .el-form-item__content {\n        min-width: 200px;\n      }\n    }\n  }\n}\n</style>\n"]}]}
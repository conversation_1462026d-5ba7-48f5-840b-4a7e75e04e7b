{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\com-config\\index.vue?vue&type=style&index=0&id=2ed9aab9&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\com-config\\index.vue", "mtime": 1757468112228}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLnBhZ2UtY29udGFpbmVyewogIG1hcmdpbjoxNnB4OwogIGJveC1zaXppbmc6IGJvcmRlci1ib3g7CiAgLnRvcC13cmFwcGVyewogICAgYmFja2dyb3VuZDogI2ZmZjsKICAgIHBhZGRpbmc6MTZweDsKICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7CiAgICAudGl0bGV7CiAgICAgIGZvbnQtc2l6ZTogMTZweDsKICAgICAgZm9udC13ZWlnaHQ6IDUwMDsKICAgICAgY29sb3I6IzMzMzMzMzsKICAgIH0KICAgIC5pbmZvewogICAgICBmb250LXNpemU6IDE0cHg7CiAgICAgIG1hcmdpbjo4cHggMCAyNHB4IDA7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7CiAgICAgIC50aXRsZXsKICAgICAgICBmb250LXNpemU6IDE0cHg7CiAgICAgICAgY29sb3I6ICM5OTk5OTk7CiAgICAgIH0KICAgICAgLnZhbHVlewogICAgICAgIGNvbG9yOiAjMzMzMzMzOwogICAgICAgIG1hcmdpbi1yaWdodDogMjRweDsKICAgICAgfQogICAgfQogIH0KICAuY29udGVudC13cmFwcGVyewogICAgbWFyZ2luLXRvcDoxNnB4OwogICAgZGlzcGxheTogZmxleDsKICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7CiAgICAubGVmdC1jewogICAgICB3aWR0aDogMTYwcHg7CiAgICAgIGJhY2tncm91bmQ6ICNmZmY7CiAgICAgIG1hcmdpbi1yaWdodDogMTZweDsKICAgIH0KICAgIC5yaWdodC1jewogICAgICBiYWNrZ3JvdW5kOiAjZmZmOwogICAgICB3aWR0aDogMTAwJTsKICAgICAgcGFkZGluZzogMTZweCAyNHB4OwogICAgICBib3gtc2l6aW5nOiBib3JkZXItYm94OwogICAgICAucmlnaHQtYy10aXRsZSAuc2V0dGluZy10aXRsZSB7CiAgICAgICAgbWFyZ2luOiAwOwogICAgICB9CiAgICB9CiAgfQp9Ci8vIDo6di1kZWVwLmVsLXRhYnMtLWxlZnQgLmVsLXRhYnNfX25hdi13cmFwLmlzLWxlZnQ6OmFmdGVyewovLyAgIGxlZnQ6MAovLyB9Ci8vIDo6di1kZWVwLmVsLXRhYnMtLWxlZnQgLmVsLXRhYnNfX2FjdGl2ZS1iYXIuaXMtbGVmdHsKLy8gICBsZWZ0OiAwOwovLyB9Ci5zZXR0aW5nLXRpdGxlIHsKICBmb250LXdlaWdodDogNDAwOwogIGNvbG9yOiAjMWYyZjNkOwogIG1hcmdpbjogMzBweCAwIDIwcHg7CiAgZm9udC1zaXplOiAyMnB4Owp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/bom-setting/com-config", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"page-container\">\r\n      <div class=\"top-wrapper\">\r\n        <!-- <div class=\"title\">专业模板配置：</div> -->\r\n        <div class=\"info\">\r\n          <template v-if=\"!!majorName\">\r\n            <div class=\"title\">当前专业：</div>\r\n            <div class=\"value\">{{ majorName }}</div>\r\n          </template>\r\n          <template v-if=\"!!unit\">\r\n            <div class=\"title\">统计单位：</div>\r\n            <div class=\"value\">{{ unit }}</div>\r\n          </template>\r\n          <template v-if=\"!!steelUnit\">\r\n            <div class=\"title\">构件单位：</div>\r\n            <div class=\"value\">{{ steelUnit }}</div>\r\n          </template>\r\n          <template>\r\n            <div class=\"title\">单位统计字段：</div>\r\n            <div v-for=\"(item,index) in templateListNew\" v-show=\"item.Code==='SteelAmount'\" :key=\"index\" style=\"display: flex;flex-direction: row\">\r\n              {{ item.Display_Name }}\r\n            </div>\r\n            <div v-for=\"(item,index) in templateListNew\" v-show=\"item.Code==='SteelWeight'\" :key=\"index+999\" style=\"display: flex;flex-direction: row\">\r\n              *{{ item.Display_Name }}\r\n            </div>\r\n          </template>\r\n        </div>\r\n        <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\r\n          <el-tab-pane v-for=\"(item,index) in tabList\" :key=\"comName+index\" :label=\"item.label\" :name=\"item.value\" />\r\n\r\n        </el-tabs>\r\n      </div>\r\n      <div class=\"content-wrapper\" style=\"min-height: calc(100vh - 340px)\">\r\n        <div class=\"right-c\">\r\n          <el-row type=\"flex\" justify=\"space-between\">\r\n            <div class=\"right-c-title\">\r\n              <div class=\"setting-title\">{{ systemField==true ? '系统字段' : businessField==true ? '业务字段' : expandField==true ? '拓展字段' : '' }}</div>\r\n            </div>\r\n            <div style=\"display: flex;flex-direction: row\">\r\n              <span style=\"width:140px;font-size:12px;height:32px; line-height: 32px;display: inline-block;\">字段名称：</span>\r\n              <el-input v-model=\"searchVal\" placeholder=\"请输入字段名称\" clearable />\r\n              <el-button type=\"primary\" style=\"margin-left: 10px\" @click=\"searchValue\">查询</el-button>\r\n              <el-button type=\"primary\" :loading=\"loading\" @click=\"saveModifyChangesFn\">保存设置</el-button>\r\n            </div>\r\n          </el-row>\r\n          <el-form label-width=\"120px\" style=\"margin-top: 24px\">\r\n            <template v-for=\"(item,index) in templateListNew\">\r\n              <el-row v-if=\"item.Column_Type==0\" :key=\"index\">\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"item.Code\" label-width=\"150px\">\r\n                    <el-input v-model=\"item.Display_Name\" :disabled=\"false\" style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"'备注说明'\">\r\n                    <el-input v-model=\"item.Remark\" disabled style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"排序\">\r\n                    <el-input v-model=\"item.Sort\" style=\"width: 100px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"是否显示\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </template>\r\n            <div v-show=\"businessField==true && systemField==true\" class=\"setting-title\">业务字段</div>\r\n            <template v-for=\"(item,index) in templateListNew\">\r\n              <el-row v-if=\"item.Column_Type==2\" :key=\"index\">\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"item.Code\" label-width=\"150px\">\r\n                    <el-input v-model=\"item.Display_Name\" style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"'备注说明'\">\r\n                    <el-input v-model=\"item.Remark\" disabled style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"排序\">\r\n                    <el-input v-model=\"item.Sort\" style=\"width: 100px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName==activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"是否启用\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-else :span=\"4\">\r\n                  <el-form-item label=\"是否显示\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </template>\r\n            <div v-show=\"expandField==true\" class=\"setting-title\">拓展字段</div>\r\n            <template v-for=\"(item,index) in templateListNew\">\r\n              <el-row v-if=\"item.Column_Type==1\" :key=\"index\">\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"item.Code\" label-width=\"150px\">\r\n                    <el-input v-model=\"item.Display_Name\" style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"6\">\r\n                  <el-form-item :label=\"'备注说明'\">\r\n                    <el-input v-model=\"item.Remark\" disabled style=\"width: 200px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName!=activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"排序\">\r\n                    <el-input v-model=\"item.Sort\" style=\"width: 100px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"activeName==activeNameApi\" :span=\"4\">\r\n                  <el-form-item label=\"是否启用\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-else :span=\"4\">\r\n                  <el-form-item label=\"是否显示\">\r\n                    <el-switch\r\n                      v-model=\"item.Is_Enabled\"\r\n                      active-color=\"#388CFF\"\r\n                      inactive-color=\"#EEEEEE\"\r\n                      @change=\"changeStatus($event,item.Id)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </template>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { GetTableSettingList, UpdateComponentPartTableSetting, UpdateColumnSetting } from '@/api/PRO/component-type'\r\nimport { closeTagView } from '@/utils'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\nexport default {\r\n  name: 'PROComponentConfig',\r\n  components: {\r\n  },\r\n  data() {\r\n    return {\r\n      activeNameApi: 'plm_component_field_page_list',\r\n      activeName: 'plm_component_field_page_list',\r\n      currentCode: 'plm_component_page_list',\r\n      typeCode: '',\r\n      materialCode: '',\r\n      currentFinalTypeCode: '',\r\n      tabPosition: 'left',\r\n      // tabList: [\r\n      //   {\r\n      //     label: '构件字段维护',\r\n      //     value: 'plm_component_field_page_list'\r\n      //   },\r\n      //   {\r\n      //     label: '构件管理列表',\r\n      //     value: 'plm_component_page_list'\r\n      //   },\r\n      //   {\r\n      //     label: '构件深化清单',\r\n      //     value: 'plm_component_detailImport'\r\n      //   },\r\n      //   {\r\n      //     label: '构件模型清单',\r\n      //     value: 'plm_component_modelImport'\r\n      //   },\r\n      //   {\r\n      //     label: '生产详情列表',\r\n      //     value: 'plm_component_produceDetail'\r\n      //   },\r\n      //   {\r\n      //     label: '打包模板',\r\n      //     value: 'plm_component_packageTemplate'\r\n      //   },\r\n      //   {\r\n      //     label: '模型字段对照表',\r\n      //     value: 'plm_component_modelField'\r\n      //   }\r\n      // ],\r\n      searchVal: '',\r\n      majorName: '',\r\n      comName: '构件',\r\n      unit: '',\r\n      steelUnit: '',\r\n      templateList: [],\r\n      templateListNew: [],\r\n      loading: false,\r\n      systemField: false,\r\n      expandField: false,\r\n      businessField: false\r\n    }\r\n  },\r\n  computed: {\r\n    tabList() {\r\n      return [\r\n        {\r\n          label: this.comName + '字段维护',\r\n          value: 'plm_component_field_page_list'\r\n        },\r\n        {\r\n          label: this.comName + '管理列表',\r\n          value: 'plm_component_page_list'\r\n        },\r\n        // {\r\n        //   label: this.comName + '深化清单',\r\n        //   value: 'plm_component_detailImport'\r\n        // },\r\n        // {\r\n        //   label: this.comName + '模型清单',\r\n        //   value: 'plm_component_modelImport'\r\n        // },\r\n        {\r\n          label: '生产详情列表',\r\n          value: 'plm_component_produceDetail'\r\n        },\r\n        {\r\n          label: '打包模板',\r\n          value: 'plm_component_packageTemplate'\r\n        }\r\n        // {\r\n        //   label: '模型字段对照表',\r\n        //   value: 'plm_component_modelField'\r\n        // }\r\n      ]\r\n    }\r\n  },\r\n  created() {\r\n    this.typeCode = this.$route.query.typeCode || 'Steel'\r\n    this.materialCode = this.$route.query.materialCode || 'StructuralAs'\r\n    this.currentFinalTypeCode = this.typeCode\r\n  },\r\n  mounted() {\r\n    this.majorName = this.$route.query.name || '钢结构'\r\n    this.unit = this.$route.query.unit || 't'\r\n    this.steelUnit = this.$route.query.steel_unit || 'kg'\r\n    this.GetTableSettingListFn()\r\n  },\r\n  methods: {\r\n    changeStatus($event, id) {\r\n      const displayName = this.templateList.find((item) => { return item.Id == id }).Display_Name\r\n      if (displayName == '' && $event == true) {\r\n        this.$message({ type: 'error', message: '请先填写字段名' })\r\n        this.templateList.map((item) => {\r\n          if (item.Id == id) {\r\n            item.Is_Enabled = false\r\n          }\r\n          return item\r\n        })\r\n      }\r\n    },\r\n    handleClick(tab, event) {\r\n      this.currentCode = tab.name\r\n      this.GetTableSettingListFn()\r\n    },\r\n\r\n    searchValue() {\r\n      if (!this.searchVal) {\r\n        this.templateListNew = this.templateList\r\n      } else {\r\n        const filterList = []\r\n        this.templateList.map(item => {\r\n          if (item.Display_Name.search(new RegExp(this.searchVal, 'ig')) !== -1) {\r\n            filterList.push(item)\r\n          }\r\n        })\r\n        this.templateListNew = filterList\r\n      }\r\n    },\r\n\r\n    saveModifyChangesFn() {\r\n      if (this.activeName == this.activeNameApi) {\r\n        this.UpdateComponentPartTableSetting()\r\n      } else {\r\n        this.UpdateColumnSetting()\r\n      }\r\n    },\r\n\r\n    async UpdateColumnSetting() {\r\n      this.loading = true\r\n      const res = await UpdateColumnSetting(this.templateList)\r\n      this.loading = false\r\n      if (res.IsSucceed) {\r\n        this.$message({ type: 'success', message: '保存成功' })\r\n      } else {\r\n        this.$message({ type: 'error', message: res.Message })\r\n      }\r\n    },\r\n\r\n    async UpdateComponentPartTableSetting() {\r\n      this.loading = true\r\n      const res = await UpdateComponentPartTableSetting(this.templateList)\r\n      this.loading = false\r\n      if (res.IsSucceed) {\r\n        this.$message({ type: 'success', message: '保存成功' })\r\n      } else {\r\n        this.$message({ type: 'error', message: res.Message })\r\n      }\r\n    },\r\n\r\n    async GetTableSettingListFn() {\r\n      let data = {}\r\n      if (this.activeName == this.activeNameApi) {\r\n        data = { IsComponent: true, ProfessionalCode: this.currentFinalTypeCode }\r\n      } else {\r\n        data = { IsComponent: true, ProfessionalCode: this.currentFinalTypeCode, TypeCode: this.currentCode + ',' + this.currentFinalTypeCode }\r\n      }\r\n      data.Level = -1\r\n      const { comName } = await GetBOMInfo()\r\n      this.comName = comName\r\n      const res = await GetTableSettingList(data)\r\n      if (res.IsSucceed) {\r\n        this.templateList = res.Data || []\r\n        if (this.templateList.length > 0) {\r\n          this.templateList.forEach(v => {\r\n            if (v.Code === 'SteelName') {\r\n              v.Display_Name = comName\r\n            }\r\n          })\r\n        }\r\n        this.templateListNew = this.templateList\r\n        this.systemField = this.templateList.some(item => { return item.Column_Type == 0 })\r\n        this.expandField = this.templateList.some(item => { return item.Column_Type == 1 })\r\n        this.businessField = this.templateList.some(item => { return item.Column_Type == 2 })\r\n      } else {\r\n        this.$message({ type: 'error', message: res.Message })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n  .page-container{\r\n    margin:16px;\r\n    box-sizing: border-box;\r\n    .top-wrapper{\r\n      background: #fff;\r\n      padding:16px;\r\n      box-sizing: border-box;\r\n      .title{\r\n        font-size: 16px;\r\n        font-weight: 500;\r\n        color:#333333;\r\n      }\r\n      .info{\r\n        font-size: 14px;\r\n        margin:8px 0 24px 0;\r\n        display: flex;\r\n        flex-direction: row;\r\n        .title{\r\n          font-size: 14px;\r\n          color: #999999;\r\n        }\r\n        .value{\r\n          color: #333333;\r\n          margin-right: 24px;\r\n        }\r\n      }\r\n    }\r\n    .content-wrapper{\r\n      margin-top:16px;\r\n      display: flex;\r\n      flex-direction: row;\r\n      .left-c{\r\n        width: 160px;\r\n        background: #fff;\r\n        margin-right: 16px;\r\n      }\r\n      .right-c{\r\n        background: #fff;\r\n        width: 100%;\r\n        padding: 16px 24px;\r\n        box-sizing: border-box;\r\n        .right-c-title .setting-title {\r\n          margin: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  // ::v-deep.el-tabs--left .el-tabs__nav-wrap.is-left::after{\r\n  //   left:0\r\n  // }\r\n  // ::v-deep.el-tabs--left .el-tabs__active-bar.is-left{\r\n  //   left: 0;\r\n  // }\r\n  .setting-title {\r\n    font-weight: 400;\r\n    color: #1f2f3d;\r\n    margin: 30px 0 20px;\r\n    font-size: 22px;\r\n  }\r\n</style>\r\n"]}]}
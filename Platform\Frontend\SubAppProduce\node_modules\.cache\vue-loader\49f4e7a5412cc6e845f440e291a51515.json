{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\material-inventory-reconfig\\aux-outbound\\info.vue?vue&type=style&index=0&id=50243a95&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\material-inventory-reconfig\\aux-outbound\\info.vue", "mtime": 1757926768419}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmFwcC1jb250YWluZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAuYm94LWNhcmQtdGIgew0KICAgIGZsZXg6IDE7DQogICAgbWFyZ2luLXRvcDogOHB4Ow0KICB9DQp9DQovLyDooajmoLzlt6XlhbfmoI9jc3MNCi50b29sYmFyLWNvbnRhaW5lciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgLy8gbWFyZ2luOiAxMHB4IDAgMCAwOw0KICAvLyBmbGV4LXdyYXA6IG5vd3JhcDsNCiAgOjp2LWRlZXAgLmVsLXJhZGlvLWdyb3VwIHsNCiAgICB3aWR0aDogNDAwcHg7DQogIH0NCiAgLnRvb2xiYXItdGl0bGUgew0KICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICBmb250LXdlaWdodDogNjAwOw0KICAgIGNvbG9yOiAjMzMzMzMzOw0KICAgIHNwYW4gew0KICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICAgICAgd2lkdGg6IDJweDsNCiAgICAgIGhlaWdodDogMTRweDsNCiAgICAgIGJhY2tncm91bmQ6ICMwMDlkZmY7DQogICAgICBtYXJnaW4tcmlnaHQ6IDZweDsNCiAgICAgIHZlcnRpY2FsLWFsaWduOiB0ZXh0LXRvcDsNCiAgICB9DQogIH0NCiAgLnNlYXJjaC1mb3JtIHsNCiAgICB3aWR0aDogNjAlOw0KICAgIDo6di1kZWVwIHsNCiAgICAgIC5lbC1mb3JtLWl0ZW0tLXNtYWxsIHsNCiAgICAgICAgbWFyZ2luLWJvdHRvbTogMDsNCiAgICAgIH0NCiAgICAgIC5lbC1mb3JtLWl0ZW1fX2NvbnRlbnQgew0KICAgICAgICB3aWR0aDogMTEwcHg7DQogICAgICB9DQogICAgICAubGFzdC1idG4gew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kOw0KICAgICAgICAuZWwtZm9ybS1pdGVtIHsNCiAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDA7DQogICAgICAgIH0NCiAgICAgICAgLmVsLWZvcm0taXRlbV9fY29udGVudCB7DQogICAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCiAgLnN0YXRpc3RpY3MtY29udGFpbmVyIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIC5zdGF0aXN0aWNzLWl0ZW0gew0KICAgICAgbWFyZ2luLXJpZ2h0OiAzMnB4Ow0KICAgICAgc3BhbjpmaXJzdC1jaGlsZCB7DQogICAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICBsaW5lLWhlaWdodDogMThweDsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgY29sb3I6ICM5OTk5OTk7DQogICAgICAgIG1hcmdpbi1yaWdodDogMTZweCAhaW1wb3J0YW50Ow0KICAgICAgfQ0KICAgICAgc3BhbjpsYXN0LWNoaWxkIHsNCiAgICAgICAgZm9udC1zaXplOiAxOHB4Ow0KICAgICAgICBmb250LXdlaWdodDogNjAwOw0KICAgICAgICBjb2xvcjogIzAwYzM2MTsNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCi5lbC1jYXJkIHsNCiAgOjp2LWRlZXAgew0KICAgIC5lbC1jYXJkX19ib2R5IHsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIH0NCiAgfQ0KDQogIC50Yi14IHsNCiAgICBmbGV4OiAxOw0KICAgIG1hcmdpbi1ib3R0b206IDEwcHg7DQogICAgb3ZlcmZsb3c6IGF1dG87DQogIH0NCn0NCg0KOjp2LWRlZXAgLmVsRGl2ZGVyIHsNCiAgbWFyZ2luOiAxMHB4Ow0KfQ0KDQoucGFnaW5hdGlvbi1jb250YWluZXIgew0KICB0ZXh0LWFsaWduOiByaWdodDsNCiAgbWFyZ2luLXRvcDogMTBweDsNCiAgcGFkZGluZzogMDsNCn0NCg0KLnVwbG9hZC1maWxlLWxpc3Qgew0KICAmID4gZGl2IHsNCiAgICB3aWR0aDogMTAwJTsNCiAgICBoZWlnaHQ6IDMwcHg7DQogICAgbGluZS1oZWlnaHQ6IDMwcHg7DQogICAgcGFkZGluZy1sZWZ0OiAxNXB4Ow0KICAgIHBhZGRpbmctcmlnaHQ6IDE1cHg7DQogICAgY3Vyc29yOiBwb2ludGVyOw0KICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICBpIHsNCiAgICAgIG1hcmdpbi1yaWdodDogMTBweDsNCiAgICB9DQogICAgaTpsYXN0LWNoaWxkIHsNCiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICAgIHRvcDogNTAlOw0KICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01MCUpOw0KICAgICAgcmlnaHQ6IDE1cHg7DQogICAgICBjb2xvcjogIzk5OTk5OTsNCiAgICAgIGZvbnQtc2l6ZTogMThweDsNCiAgICB9DQogIH0NCiAgJiA+IGRpdjpob3ZlciB7DQogICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjhmODsNCiAgICBpOmxhc3QtY2hpbGQgew0KICAgICAgY29sb3I6ICMyOThkZmY7DQogICAgfQ0KICB9DQp9DQoNCjo6di1kZWVwIC5lbC1mb3JtLWl0ZW0gew0KICAuZWwtZm9ybS1pdGVtX19jb250ZW50IHsNCiAgICAuZWwtdHJlZS1zZWxlY3QtaW5wdXQgew0KICAgICAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDsNCiAgICB9DQogIH0NCn0NCg0KZm9vdGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KfQ0K"}, {"version": 3, "sources": ["info.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwuCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "info.vue", "sourceRoot": "src/views/PRO/material-inventory-reconfig/aux-outbound", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <el-card\r\n      class=\"box-card\"\r\n      :style=\"isRetract ? 'height: 110px; overflow: hidden;' : ''\"\r\n    >\r\n      <!-- <h3 style=\"margin-bottom: 20px\">出库单信息</h3> -->\r\n      <div\r\n        class=\"toolbar-container\"\r\n        style=\"\r\n          margin-bottom: 10px;\r\n          padding-bottom: 10;\r\n          border-bottom: 1px solid #d0d3db;\r\n        \"\r\n      >\r\n        <div class=\"toolbar-title\"><span />出库单信息</div>\r\n        <div class=\"retract-container\" @click=\"handleRetract\">\r\n          <el-button type=\"text\">{{ isRetract ? \"展开\" : \"收起\" }}</el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            :icon=\"isRetract ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"出库类型\" prop=\"OutStoreType\">\r\n              <SelectMaterialStoreType v-model=\"form.OutStoreType\" type=\"RawOutStoreType\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"出库日期\" prop=\"OutStoreDate\">\r\n              <el-date-picker\r\n                v-model=\"form.OutStoreDate\"\r\n                :disabled=\"isView|| isReturn\"\r\n                :picker-options=\"pickerOptions\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                type=\"date\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"领料部门\" prop=\"Pick_Department_Id\">\r\n              <el-select\r\n                v-if=\"isProductweight === 'true'\"\r\n                v-model=\"form.Pick_Department_Id\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请选择\"\r\n                clearable\r\n                filterable\r\n                :disabled=\"isView|| isReturn\"\r\n                @change=\"departmentChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in departmentlist\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n              <el-tree-select\r\n                v-else\r\n                ref=\"treeSelectDepart\"\r\n                v-model=\"form.Pick_Department_Id\"\r\n                :select-params=\"{\r\n                  clearable: true,\r\n                  disabled: isReturn\r\n                }\"\r\n                class=\"cs-tree-x\"\r\n                :tree-params=\"treeParamsDepart\"\r\n                @select-clear=\"departmentChange\"\r\n                @node-click=\"departmentChange\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"领料人\" prop=\"ReceiveUserId\">\r\n              <el-select\r\n                v-model=\"form.ReceiveUserId\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请选择\"\r\n                clearable\r\n                filterable\r\n                :disabled=\"isView || !form.Pick_Department_Id|| isReturn\"\r\n                @change=\"userChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in factoryPeoplelist\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col v-if=\"isPurchase\" :span=\"6\">\r\n            <el-form-item label=\"领料班组\" prop=\"WorkingTeamId\">\r\n              <el-select\r\n                v-model=\"form.WorkingTeamId\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请选择\"\r\n                clearable\r\n                filterable\r\n                :disabled=\"isView\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in WorkingTeamList\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col v-if=\"isPurchase\" :span=\"6\">\r\n            <el-form-item label=\"使用工序\" prop=\"Use_Processing_Id\">\r\n              <el-select\r\n                v-model=\"form.Use_Processing_Id\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请选择\"\r\n                clearable\r\n                filterable\r\n                :disabled=\"isView|| isReturn\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in ProcessList\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <!-- <el-col :span=\"6\">\r\n            <el-form-item label=\"领用部门\" prop=\"Pick_Department_Id\">\r\n              <el-select\r\n                v-model=\"form.Pick_Department_Id\"\r\n                filterable\r\n                style=\"width: 100%\"\r\n                clearable\r\n                placeholder=\"请选择\"\r\n                :disabled=\"isView\"\r\n                @change=\"departmentChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in departmentlist\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col> -->\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"备注\" prop=\"Remark\">\r\n              <el-input\r\n                v-model=\"form.Remark\"\r\n                :disabled=\"isView|| isReturn\"\r\n                style=\"width: 100%\"\r\n                show-word-limit\r\n                :rows=\"1\"\r\n                :maxlength=\"100\"\r\n                type=\"textarea\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col v-if=\"!isReturn\" :span=\"6\">\r\n            <el-form-item label=\"附件\" class=\"factory-img\">\r\n              <OSSUpload\r\n                class=\"upload-demo\"\r\n                action=\"alioss\"\r\n                :limit=\"5\"\r\n                :multiple=\"true\"\r\n                :on-success=\"\r\n                  (response, file, fileList) => {\r\n                    uploadSuccess(response, file, fileList);\r\n                  }\r\n                \"\r\n                :on-remove=\"uploadRemove\"\r\n                :on-preview=\"handlePreview\"\r\n                :on-exceed=\"handleExceed\"\r\n                :show-file-list=\"true\"\r\n                :file-list=\"fileListData\"\r\n                :disabled=\"isView|| isReturn\"\r\n              >\r\n                <el-button\r\n                  v-if=\"!(isView|| isReturn)\"\r\n                  type=\"primary\"\r\n                  :disabled=\"isView|| isReturn\"\r\n                >上传文件</el-button>\r\n              </OSSUpload>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col v-if=\"isReturn\" :span=\"6\">\r\n            <el-form-item label=\"退库部门\" prop=\"Return_Dept_Id\">\r\n              <SelectDepartment v-model=\"form.Return_Dept_Id\" :disabled=\"isView\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col v-if=\"isReturn\" :span=\"6\">\r\n            <el-form-item label=\"退库人\" prop=\"Return_Person_Id \">\r\n              <SelectDepartmentUser v-model=\"form.Return_Person_Id\" :department-id=\"form.Return_Dept_Id\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n      </el-form>\r\n    </el-card>\r\n    <el-card class=\"box-card box-card-tb\">\r\n      <!-- <el-divider class=\"elDivder\" /> -->\r\n      <!-- <h4>出库单明细</h4> -->\r\n      <div class=\"toolbar-container\">\r\n        <div class=\"toolbar-title\"><span />出库单明细</div>\r\n      </div>\r\n      <el-divider class=\"elDivder\" />\r\n\r\n      <div style=\"display: flex; justify-content: space-between;align-items: center;\">\r\n        <div v-if=\"!isView&&!isReturn\" style=\"display: flex;align-items: center;\">\r\n          <el-button\r\n            type=\"primary\"\r\n            @click=\"openAddDialog(null)\"\r\n          >新增</el-button>\r\n          <el-button\r\n            type=\"danger\"\r\n            :disabled=\"!multipleSelection.length\"\r\n            @click=\"handleDelete\"\r\n          >删除</el-button>\r\n          <el-button type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"batchDialogVisible = true\">批量编辑领用项目</el-button>\r\n          <PickSelect style=\"margin-left: 10px\" :selected-list=\"rootTableData\" :material-type=\"1\" @addList=\"getAddList\" />\r\n        </div>\r\n        <div style=\"margin-left: auto\">\r\n          <el-form\r\n            ref=\"searchForm\"\r\n            inline\r\n            :model=\"searchForm\"\r\n            label-width=\"80px\"\r\n          >\r\n            <el-form-item label=\"辅料名称\" prop=\"RawName\">\r\n              <el-input\r\n                v-model=\"searchForm.RawName\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请输入\"\r\n                clearable\r\n              />\r\n            </el-form-item>\r\n\r\n            <el-form-item label=\"规格\" prop=\"Spec\">\r\n              <el-input\r\n                v-model=\"searchForm.Spec\"\r\n                style=\"width: 100%\"\r\n                placeholder=\"请输入\"\r\n                clearable\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"所属项目\" prop=\"SysProjectId\">\r\n              <el-select\r\n                v-model=\"searchForm.SysProjectId\"\r\n                class=\"input\"\r\n                placeholder=\"所属项目\"\r\n                clearable\r\n                filterable\r\n              >\r\n                <el-option\r\n                  v-for=\"item in projectOptions\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Short_Name\"\r\n                  :value=\"item.Sys_Project_Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item>\r\n              <el-button type=\"primary\" @click=\"handleSearch\">搜索</el-button>\r\n              <el-button @click=\"handleReset\">重置</el-button>\r\n            </el-form-item>\r\n            <DynamicTableFields\r\n              title=\"表格配置\"\r\n              :table-config-code=\"gridCode\"\r\n              :manual-hide-columns=\"[{'Code':'PartyUnitName'}]\"\r\n              @updateColumn=\"changeColumn\"\r\n            />\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"tb-x\">\r\n        <component\r\n          :is=\"currentTbComponent\"\r\n          v-if=\"currentTbComponent\"\r\n          ref=\"table\"\r\n          :is-return=\"isReturn\"\r\n          :is-view=\"isView\"\r\n          :big-type-data=\"BigType\"\r\n          @changeStandard=\"changeStandard\"\r\n          @updateTb=\"handleUpdateTb\"\r\n          @changeWarehouse=\"changeWarehouse\"\r\n          @select=\"setSelectRow\"\r\n        />\r\n      </div>\r\n      <el-divider v-if=\"!isView\" class=\"elDivder\" />\r\n      <footer v-if=\"!isView\">\r\n        <div class=\"data-info\">\r\n          <el-tag\r\n            v-if=\"!isReturn\"\r\n            size=\"medium\"\r\n            class=\"info-x\"\r\n          >已选{{ multipleSelection.length }}条数据\r\n          </el-tag>\r\n        </div>\r\n        <div>\r\n          <el-button @click=\"closeView\">取消</el-button>\r\n          <el-button v-if=\"isReturn\" :loading=\"returning\" type=\"primary\" @click=\"handleReturn\">确认退库</el-button>\r\n\r\n          <template v-else>\r\n            <el-button\r\n              :loading=\"saveLoading\"\r\n              @click=\"saveDraft(1)\"\r\n            >保存草稿\r\n            </el-button>\r\n            <el-button type=\"primary\" :loading=\"saveLoading\" @click=\"handleSubmit\">提交出库</el-button>\r\n          </template>\r\n        </div>\r\n      </footer>\r\n    </el-card>\r\n\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"dWidth\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :page-type=\"1\"\r\n        @close=\"handleClose\"\r\n        @warehouse=\"getWarehouse\"\r\n        @batchEditor=\"batchEditorFn\"\r\n        @importData=\"importData\"\r\n        @standard=\"getStandard\"\r\n        @refresh=\"fetchData\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      title=\"新增\"\r\n      :visible.sync=\"openAddList\"\r\n      width=\"70%\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <template v-if=\"openAddList\">\r\n        <add-purchase-list\r\n          ref=\"draft\"\r\n          :big-type-data=\"BigType\"\r\n          :p-form=\"form\"\r\n          :joined-items=\"rootTableData\"\r\n          @getAddList=\"getAddList\"\r\n          @getRowName=\"getRowName\"\r\n          @close=\"handleClose\"\r\n        />\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      title=\"批量编辑领用项目\"\r\n      :visible.sync=\"batchDialogVisible\"\r\n      top=\"10vh\"\r\n      width=\"350px\"\r\n      @close=\"closeBatchDialog\"\r\n    >\r\n      <el-select\r\n        v-model=\"batchProjectId\"\r\n        style=\"width: 300px\"\r\n        placeholder=\"请选择\"\r\n        clearable\r\n        filterable\r\n      >\r\n        <el-option\r\n          v-for=\"item in projectOptions\"\r\n          :key=\"item.Id\"\r\n          :label=\"item.Short_Name\"\r\n          :value=\"item.Sys_Project_Id\"\r\n        />\r\n      </el-select>\r\n      <p style=\"margin: 20px\">\r\n        <i>注：仅能批量编辑公共库存的领用项目</i>\r\n      </p>\r\n      <div style=\"text-align: right\">\r\n        <el-button @click=\"closeBatchDialog\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"batchChangeProject\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { closeTagView, debounce, deepClone } from '@/utils'\r\nimport PurchaseTb from './components/PurchaseTb.vue'\r\nimport AddList from './components/AddList.vue'\r\nimport AddPurchaseList from './components/AddPurchaseList.vue'\r\nimport ImportFile from '../components/ImportFile.vue'\r\nimport Standard from './components/Standard.vue'\r\nimport { GetProjectPageList } from '@/api/PRO/project'\r\nimport OSSUpload from '@/views/PRO/components/ossupload'\r\nimport getCommonData from '@/mixins/PRO/get-common-data/index.js'\r\nimport { GetFirstLevelDepartsUnderFactory } from '@/api/OMA/nonOperating.js'\r\nimport {\r\n  AuxReturnByReceipt,\r\n  GetAuxDetailByReceipt, GetTeamListByUserForMateriel,\r\n  GetUserPage\r\n} from '@/api/PRO/material-warehouse/material-inventory-reconfig.js'\r\n// import { GetFactoryPeoplelist } from '@/api/PRO/basic-information/workshop'\r\nimport { GetProcessListBase } from '@/api/PRO/technology-lib'\r\nimport { getDictionary } from '@/utils/common'\r\nimport { GetPreferenceSettingValue } from '@/api/sys/system-setting'\r\nimport {\r\n  PickOutStore,\r\n  GetPickOutDetail\r\n} from '@/api/PRO/material-warehouse/material-inventory-reconfig.js'\r\nimport { GetOMALatestStatisticTime } from '@/api/PRO/materialManagement'\r\nimport { GetOssUrl, GetCompanyDepartTree } from '@/api/sys'\r\nimport ReturnTb from './components/ReturnTb.vue'\r\nimport getTableConfig from '@/mixins/PRO/get-table-info-pro/index'\r\nimport Warehouse from '@/views/PRO/material-receipt-management/components/Warehouse.vue'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport SelectMaterialStoreType from '@/components/Select/SelectMaterialStoreType/index.vue'\r\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\r\nimport { GetWorkingTeams } from '@/api/PRO/technology-lib'\r\nimport SelectDepartment from '@/components/Select/SelectDepartment/index.vue'\r\nimport SelectDepartmentUser from '@/components/Select/SelectDepartmentUser/index.vue'\r\nimport PickSelect from '@/views/PRO/material_v4/pickApply/select.vue'\r\nexport default {\r\n  components: {\r\n    PickSelect,\r\n    SelectDepartmentUser, SelectDepartment,\r\n    DynamicTableFields,\r\n    SelectMaterialStoreType,\r\n    AddPurchaseList,\r\n    PurchaseTb,\r\n    ReturnTb,\r\n    ImportFile,\r\n    Warehouse,\r\n    Standard,\r\n    AddList,\r\n    OSSUpload\r\n  },\r\n  mixins: [getCommonData, getTableConfig],\r\n  props: {\r\n    pageType: {\r\n      type: Number,\r\n      default: undefined\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      isRetract: false, // 是否展开\r\n      returning: false,\r\n      tbLoading: false,\r\n      projectOptions: [],\r\n      multipleSelection: [],\r\n      // PartyUnitData: [],\r\n      // SupplierData: [],\r\n      factoryPeoplelist: [], // 领料人列表\r\n      departmentlist: [],\r\n      ProcessList: [],\r\n      AuxOutboundTypeList: [],\r\n      WorkingTeamList: [], // 领料班组列表\r\n      searchForm: {\r\n        RawName: '',\r\n        Spec: ''\r\n      },\r\n      form: {\r\n        OutStoreNo: '', // 出库单号\r\n        OutStoreType: 1, // 出库类型\r\n        OutStoreDate: this.getDate(),\r\n        Use_Processing_Id: '', // 工序id\r\n        Pick_Department_Id: '',\r\n        // ProjectId: '',\r\n        SysProjectId: '',\r\n        Pick_Project_Name: '', // 领用项目名称\r\n        ReceiveUserId: '', // 领料人id\r\n        WorkingTeamId: '', // 领料班组id\r\n        Remark: '',\r\n        Attachment: ''\r\n      },\r\n      rules: {\r\n        OutStoreType: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        OutStoreDate: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        Pick_Department_Id: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        ReceiveUserId: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ]\r\n        // WorkingTeamId: [\r\n        //   { required: true, message: '请选择', trigger: 'change' }\r\n        // ]\r\n      },\r\n      treeParamsDepart: {\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          disabled: 'disabled',\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      backendDate: null,\r\n      pickerOptions: {\r\n        disabledDate: (time) => {\r\n          return time.getTime() < new Date(this.backendDate).getTime() // 限制选择日期不能超过当前日期\r\n        }\r\n      },\r\n      currentComponent: '',\r\n      title: '',\r\n      dWidth: '60%',\r\n      // isSingle: false,\r\n      saveLoading: false,\r\n      search: () => ({}),\r\n      openAddList: false,\r\n      dialogVisible: false,\r\n      BigType: 1,\r\n      isProductweight: null,\r\n      fileListData: [],\r\n      fileListArr: [],\r\n      searchNum: 1,\r\n      rootTableData: [],\r\n      tableData: [],\r\n      typeNumber1: 0,\r\n      typeNumber2: 0,\r\n      typeNumber3: 0,\r\n      typeNumber4: 0,\r\n      currentTbComponent: '',\r\n      batchDialogVisible: false,\r\n      batchProjectId: ''\r\n    }\r\n  },\r\n  computed: {\r\n    isAdd() {\r\n      return this.pageType === 1\r\n    },\r\n    isEdit() {\r\n      return this.pageType === 2\r\n    },\r\n    isView() {\r\n      return this.pageType === 3\r\n    },\r\n    isPurchase() {\r\n      return this.form.OutStoreType == 1 // 领用出库\r\n    },\r\n    isReturn() {\r\n      return this.pageType === 8\r\n    },\r\n    gridCode() {\r\n      return this.isReturn ? 'pro_aux_material_outbound_detail_list_return' : 'pro_aux_material_outbound_detail_list,Steel'\r\n    }\r\n  },\r\n  async mounted() {\r\n    if (this.isReturn) {\r\n      this.currentTbComponent = ReturnTb\r\n      const column = await this.getTableConfig(this.gridCode)\r\n      this.$nextTick(_ => {\r\n        this.$refs['table'].init(column)\r\n      })\r\n    } else {\r\n      this.currentTbComponent = PurchaseTb\r\n    }\r\n    await this.getCurFactory()\r\n    await this.getOMALatestStatisticTime()\r\n    this.getPreferenceSettingValue()\r\n    this.AuxOutboundTypeList = await getDictionary('AuxOutboundType')\r\n    await this.getFirstLevelDepartsUnderFactory()\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    this.getProject()\r\n    this.getProcessListBase()\r\n    // this.getFactoryPeoplelist()\r\n    this.getWorkingTeams()\r\n    if (!this.isAdd) {\r\n      this.getInfo()\r\n    }\r\n  },\r\n  methods: {\r\n    userChange(val) {\r\n      GetTeamListByUserForMateriel({\r\n        id: val\r\n      }).then(res => {\r\n        if (res.Data?.length === 1) {\r\n          this.$set(this.form, 'WorkingTeamId', res.Data[0].Id)\r\n        } else {\r\n          this.$set(this.form, 'WorkingTeamId', '')\r\n        }\r\n      })\r\n    },\r\n    closeBatchDialog() {\r\n      this.batchProjectId = ''\r\n      this.batchDialogVisible = false\r\n    },\r\n    batchChangeProject() {\r\n      const tbData = this.$refs.table.tbData\r\n      this.multipleSelection.forEach((element, idx) => {\r\n        const item = tbData.find((v) => v.index === element.index)\r\n        const i = tbData.findIndex((v) => v.index === element.index)\r\n        item.Pick_Sys_Project_Id = this.batchProjectId\r\n        // 同时更新项目名称\r\n        item.Pick_Project_Name = this.projectOptions.find(project => project.Sys_Project_Id === this.batchProjectId)?.Short_Name\r\n        this.$set(tbData, i, item)\r\n      })\r\n      this.closeBatchDialog()\r\n    },\r\n    changeColumn() {\r\n      const temp = this.currentTbComponent\r\n      this.currentTbComponent = ''\r\n      this.$nextTick(async() => {\r\n        this.currentTbComponent = temp\r\n        if (this.isReturn) {\r\n          const column = await this.getTableConfig(this.gridCode)\r\n          this.$nextTick(_ => {\r\n            this.$refs['table'].init(column)\r\n          })\r\n        }\r\n        this.$refs['table'].setData(this.tableData)\r\n      })\r\n    },\r\n    // 附件上传成功\r\n    uploadSuccess(response, file, fileList) {\r\n      this.fileListArr = JSON.parse(JSON.stringify(fileList))\r\n    },\r\n    // 移除已上传文件\r\n    uploadRemove(file, fileList) {\r\n      this.fileListArr = JSON.parse(JSON.stringify(fileList))\r\n    },\r\n    // 点击已上传文件\r\n    async handlePreview(file) {\r\n      let encryptionUrl = ''\r\n      if (file.response && file.response.encryptionUrl) {\r\n        encryptionUrl = file.response.encryptionUrl\r\n      } else {\r\n        encryptionUrl = await GetOssUrl({ url: file.encryptionUrl })\r\n        encryptionUrl = encryptionUrl.Data\r\n      }\r\n      window.open(encryptionUrl)\r\n    },\r\n    // 获取运营核算已统计的最新日期\r\n    async getOMALatestStatisticTime() {\r\n      const res = await GetOMALatestStatisticTime({})\r\n      if (res.IsSucceed) {\r\n        this.backendDate = res.Data || ''\r\n      } else {\r\n        this.message.error(res.Mesaage)\r\n      }\r\n    },\r\n    // 搜索\r\n    handleSearch() {\r\n      this.tableData = JSON.parse(JSON.stringify(this.rootTableData))\r\n      if (this.searchForm.RawName) {\r\n        const rawNameRegex = new RegExp(this.searchForm.RawName, 'i')\r\n        this.tableData = this.tableData.filter((item) => {\r\n          return rawNameRegex.test(item.RawName)\r\n        })\r\n      }\r\n\r\n      if (this.searchForm.Spec) {\r\n        const specRegex = new RegExp(this.searchForm.Spec, 'i')\r\n        this.tableData = this.tableData.filter((item) => {\r\n          return specRegex.test(item.Spec)\r\n        })\r\n      }\r\n      if (this.searchForm.SysProjectId) {\r\n        const sysProjectIdRegex = new RegExp(this.searchForm.SysProjectId, 'i')\r\n        this.tableData = this.tableData.filter((item) => {\r\n          return sysProjectIdRegex.test(item.Sys_Project_Id)\r\n        })\r\n      }\r\n      if (this.isReturn) {\r\n        this.$refs['table'].setData(this.tableData)\r\n      } else {\r\n        this.$refs['table'].tbData = this.tableData\r\n      }\r\n    },\r\n    // 更新表格数据\r\n    handleUpdateTb() {\r\n      this.rootTableData = this.$refs['table'].tbData\r\n      this.tableData = JSON.parse(JSON.stringify(this.rootTableData))\r\n      console.log(this.rootTableData, '11')\r\n      console.log(this.tableData, '22')\r\n      // this.setTabData()\r\n    },\r\n    // 获取品重偏好设置\r\n    getPreferenceSettingValue() {\r\n      GetPreferenceSettingValue({ code: 'Productweight' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.isProductweight = res.Data\r\n          // this.isProductweight = \"false\";\r\n          if (this.isProductweight !== 'true') {\r\n            this.getDepartmentTree()\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getDepartmentTree() {\r\n      GetCompanyDepartTree({ isAll: true }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const tree = res.Data\r\n          this.setDisabledTree(tree)\r\n          this.treeParamsDepart.data = tree\r\n          this.$nextTick((_) => {\r\n            this.$refs.treeSelectDepart.treeDataUpdateFun(tree)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setDisabledTree(root) {\r\n      if (!root) return\r\n      root.forEach((element) => {\r\n        const { Children } = element\r\n        if (element.Data.Is_Company === true || element.Data.Type === '1') {\r\n          element.disabled = true\r\n        } else {\r\n          element.disabled = false\r\n        }\r\n        if (Children.length > 0) {\r\n          this.setDisabledTree(Children)\r\n        }\r\n      })\r\n    },\r\n    // 重置搜索\r\n    handleReset() {\r\n      this.searchNum = 1\r\n      // this.$refs['searchForm'].resetFields()\r\n      this.searchForm.RawName = ''\r\n      this.searchForm.Spec = ''\r\n      this.$refs['table'].tbData = this.rootTableData\r\n      this.tableData = JSON.parse(JSON.stringify(this.rootTableData))\r\n    },\r\n    // 文件超出数量限制\r\n    handleExceed() {\r\n      this.$message({\r\n        type: 'warning',\r\n        message: '附件数量不能超过5个'\r\n      })\r\n    },\r\n    handleRetract() {\r\n      this.isRetract = !this.isRetract\r\n    },\r\n    fetchData() {},\r\n    getInfo() {\r\n      this.form.OutStoreNo = this.$route.query.OutStoreNo\r\n      this.form.OutStoreType = +this.$route.query.OutStoreType\r\n      // const _fun = this.$route.params.OutStoreType === 1 ? PickUpOutStoreDetail : PartyAOutStoreDetail\r\n      let _fun\r\n      let params\r\n      if (this.isReturn) {\r\n        _fun = GetAuxDetailByReceipt\r\n        params = {\r\n          Id: this.$route.query.id\r\n        }\r\n      } else {\r\n        _fun = GetPickOutDetail\r\n        params = {\r\n          outStoreNo: this.$route.query.OutStoreNo\r\n        }\r\n      }\r\n      _fun(params).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const Receipt = res.Data.Receipt\r\n          const Sub = res.Data.Sub\r\n          const {\r\n            OutStoreDate,\r\n            Remark,\r\n            SysProjectId,\r\n            Pick_Department_Id,\r\n            Use_Processing_Id,\r\n            WorkingTeamId,\r\n            ReceiveUserId,\r\n            Attachment,\r\n            Status\r\n          } = Receipt\r\n          this.form.OutStoreDate = this.getDate(new Date(OutStoreDate))\r\n          this.form.Remark = Remark\r\n          this.form.SysProjectId = SysProjectId\r\n          this.form.Use_Processing_Id = Use_Processing_Id\r\n          this.form.Pick_Department_Id = Pick_Department_Id\r\n          if (this.form.Pick_Department_Id) {\r\n            this.departmentChange()\r\n          }\r\n          this.form.Use_Processing_Id = Use_Processing_Id\r\n          this.form.WorkingTeamId = WorkingTeamId\r\n          this.form.ReceiveUserId = ReceiveUserId\r\n          this.form.Status = Status\r\n\r\n          // 处理表格数据\r\n          const SubData = Sub.map((row, index) => {\r\n            row.index = uuidv4()\r\n            row.Warehouse_Location = row.WarehouseName\r\n              ? row.WarehouseName + '/' + row.LocationName\r\n              : ''\r\n            // 临时存储kg\r\n            // row.OutStoreWeightKG = row.OutStoreWeight\r\n            // row.Pound_Weight_KG = row.Pound_Weight\r\n            // row.OutStoreWeight = Number((row.OutStoreWeightKG / 1000).toFixed(3))\r\n            // row.Pound_Weight = Number((row.Pound_Weight_KG / 1000).toFixed(3))\r\n            return row\r\n          })\r\n\r\n          this.$nextTick((_) => {\r\n            this.$refs['table'].tbData = JSON.parse(JSON.stringify(SubData))\r\n            this.tableData = JSON.parse(JSON.stringify(SubData))\r\n            this.rootTableData = JSON.parse(JSON.stringify(SubData))\r\n          })\r\n          if (Attachment) {\r\n            this.form.Attachment = Attachment\r\n            const AttachmentArr = Attachment.split(',')\r\n            AttachmentArr.forEach((item) => {\r\n              const fileUrl =\r\n                item.indexOf('?Expires=') > -1\r\n                  ? item.substring(0, item.lastIndexOf('?Expires='))\r\n                  : item\r\n              const fileName = decodeURI(fileUrl.substring(fileUrl.lastIndexOf('/') + 1))\r\n              const AttachmentJson = {}\r\n              AttachmentJson.name = fileName\r\n              AttachmentJson.url = fileUrl\r\n              AttachmentJson.encryptionUrl = fileUrl\r\n              this.fileListData.push(AttachmentJson)\r\n              this.fileListArr.push(AttachmentJson)\r\n            })\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async getProcessListBase() {\r\n      const res = await GetProcessListBase({})\r\n      if (res.IsSucceed) {\r\n        this.ProcessList = res.Data || []\r\n      } else {\r\n        this.message.error(res.Mesaage)\r\n      }\r\n    },\r\n    departmentChange() {\r\n      this.form.ReceiveUserId = ''\r\n      if (this.form.Pick_Department_Id) {\r\n        this.getUserPageList(this.form.Pick_Department_Id)\r\n      }\r\n    },\r\n    getUserPageList(Id) {\r\n      GetUserPage({\r\n        PageSize: -1,\r\n        DepartmentId: Id\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.factoryPeoplelist = res.Data.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async getFirstLevelDepartsUnderFactory() {\r\n      const res = await GetFirstLevelDepartsUnderFactory({\r\n        FactoryId: this.FactoryDetailData.Id\r\n      })\r\n      if (res.IsSucceed) {\r\n        this.departmentlist = res.Data || []\r\n        this.form.Pick_Department_Id = res.Data.find(\r\n          (item) => item.Is_Cur_User_Depart\r\n        )\r\n          ? res.Data.find((item) => item.Is_Cur_User_Depart).Id\r\n          : ''\r\n        this.departmentChange()\r\n      } else {\r\n        this.message.error(res.Mesaage)\r\n      }\r\n    },\r\n    /**\r\n     * 获取所属项目\r\n     */\r\n    getProject() {\r\n      GetProjectPageList({\r\n        Page: 1,\r\n        PageSize: -1\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.projectOptions = res.Data.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 获取领料人列表\r\n     */\r\n    // getFactoryPeoplelist() {\r\n    //   GetFactoryPeoplelist().then((res) => {\r\n    //     if (res.IsSucceed) {\r\n    //       this.factoryPeoplelist = res.Data\r\n    //     } else {\r\n    //       this.$message({\r\n    //         message: res.Message,\r\n    //         type: 'error'\r\n    //       })\r\n    //     }\r\n    //   })\r\n    // },\r\n\r\n    /**\r\n     * 获取领料班组列表\r\n     */\r\n    getWorkingTeams() {\r\n      GetWorkingTeams().then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.WorkingTeamList = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    changeWarehouse(row) {\r\n      this.currentRow = row\r\n      this.handleWarehouse(true)\r\n    },\r\n    handleWarehouse(isInline) {\r\n      this.currentComponent = 'Warehouse'\r\n      this.dWidth = '40%'\r\n      this.title = '选择仓库/库位'\r\n      !isInline && (this.currentRow = null)\r\n      this.dialogVisible = true\r\n    },\r\n    getWarehouse({ warehouse, location }) {\r\n      if (this.currentRow) {\r\n        this.currentRow.ReturnWarehoueseId = warehouse.Id\r\n        this.currentRow.ReturnLocationId = location.Id\r\n        this.$set(this.currentRow, 'ReturnWarehoueseName', warehouse.Display_Name)\r\n        this.$set(this.currentRow, 'ReturnLocationName', location.Display_Name)\r\n        this.$set(\r\n          this.currentRow,\r\n          'Warehouse_Location_Return',\r\n          warehouse.Display_Name + '/' + location.Display_Name\r\n        )\r\n      }\r\n    },\r\n    batchEditorFn(data) {\r\n      if (this.currentRow) {\r\n        data.forEach((item) => {\r\n          this.$set(this.currentRow, item.key, item.val)\r\n        })\r\n      } else {\r\n        this.multipleSelection.forEach((element, idx) => {\r\n          data.forEach((item) => {\r\n            this.$set(element, item.key, item.val)\r\n          })\r\n        })\r\n      }\r\n      this.handleClose()\r\n    },\r\n    changeStandard(row) {\r\n      this.currentRow = row\r\n      this.currentComponent = 'Standard'\r\n      this.dWidth = '40%'\r\n      this.title = '选择规格'\r\n      this.dialogVisible = true\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].getOption(row)\r\n      })\r\n    },\r\n    getStandard({ type, val }) {\r\n      if (type === 1) {\r\n        this.$set(this.currentRow, 'StandardDesc', val)\r\n      } else {\r\n        this.$set(this.currentRow, 'StandardDesc', val.StandardDesc)\r\n        this.currentRow.StandardId = val.StandardId\r\n      }\r\n    },\r\n    // 出库类型切换,表格数据清空\r\n    typeChange(n) {\r\n      if (n !== 0) {\r\n        this.BigType = 1\r\n        this.typeNumber1 = 0\r\n        this.typeNumber2 = 0\r\n        this.typeNumber3 = 0\r\n        this.typeNumber4 = 0\r\n        this.tableData = []\r\n        this.$refs['table'].tbData = []\r\n      }\r\n    },\r\n    // handleReset() {\r\n    //   this.$refs[\"form\"].resetFields();\r\n    //   this.search(1);\r\n    // },\r\n    // 新增物料\r\n    getAddList(list, info) {\r\n      this.BigType = list[0].BigType\r\n      list.map((item, index) => {\r\n        item.index = uuidv4()\r\n        item.Pick_Project_Name = item.Project_Name // 默认领用项目为所属项目\r\n      })\r\n      this.$refs['table'].addData(list)\r\n      const tbData = this.$refs.table.tbData\r\n      this.tableData = JSON.parse(JSON.stringify(tbData))\r\n      this.rootTableData = JSON.parse(JSON.stringify(tbData))\r\n      if (info) {\r\n        this.form.WorkingTeamId = info.Pick_Team_Id\r\n        this.form.Use_Processing_Id = info.Pick_Process_Id\r\n      }\r\n    },\r\n    importData(list) {\r\n      this.$refs['table'].importData(list)\r\n    },\r\n    getRowName({ Name, Id }) {\r\n      this.currentRow.Name = Name\r\n      this.currentRow.RawId = Id\r\n      this.currentRow.StandardDesc = ''\r\n    },\r\n    generateComponent(title, component) {\r\n      this.title = title\r\n      this.currentComponent = component\r\n      this.dialogVisible = true\r\n    },\r\n    handleDelete() {\r\n      const tbData = this.$refs.table.tbData\r\n      this.multipleSelection.forEach((element, idx) => {\r\n        const i = tbData.findIndex((v) => v.index === element.index)\r\n        tbData.splice(i, 1)\r\n      })\r\n      this.tableData = JSON.parse(JSON.stringify(tbData))\r\n      this.rootTableData = JSON.parse(JSON.stringify(tbData))\r\n      this.multipleSelection = []\r\n      this.$refs.table?.$refs?.xTable.clearCheckboxRow()\r\n    },\r\n    handleClose(row) {\r\n      this.openAddList = false\r\n      this.dialogVisible = false\r\n    },\r\n    tbSelectChange(array) {\r\n      // console.log(array, 'arr====')\r\n      this.multipleSelection = array.records\r\n    },\r\n    handleDetail(row) {},\r\n    // 表格数据校验\r\n    checkValidate() {\r\n      const submit = deepClone(this.$refs['table'].tbData)\r\n      if (!submit.length) {\r\n        this.$message({\r\n          message: '数据不能为空',\r\n          type: 'warning'\r\n        })\r\n        return {\r\n          status: false\r\n        }\r\n      }\r\n      const { status, msg } = this.checkTb(submit)\r\n      if (!status) {\r\n        this.$message({\r\n          message: `${msg || '必填字段'}不能为空`,\r\n          type: 'warning'\r\n        })\r\n        return {\r\n          status: false\r\n        }\r\n      }\r\n      return {\r\n        data: submit,\r\n        status: true\r\n      }\r\n    },\r\n    handleReturn() {\r\n      this.$confirm('是否提交退库信息?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          this.saveDraft(3)\r\n        })\r\n        .catch((e) => {\r\n          console.log('error', e)\r\n\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n    },\r\n    // 提交入库\r\n    handleSubmit(row) {\r\n      this.$confirm('确认提交出库单?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          this.saveDraft(3)\r\n        })\r\n        .catch((e) => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n    },\r\n    // 保存出库单\r\n    saveDraft(type = 1) {\r\n      const { data, status } = this.checkValidate()\r\n      if (!status) return\r\n      this.$refs['form'].validate((valid) => {\r\n        if (!valid) return false\r\n        this.saveLoading = true\r\n        // this.form.Attachment = this.fileListArr.join(',')\r\n        const formAttachment = []\r\n        if (this.fileListArr.length > 0) {\r\n          this.fileListArr.forEach((item) => {\r\n            formAttachment.push(\r\n              item.response && item.response.encryptionUrl\r\n                ? item.response.encryptionUrl\r\n                : item.encryptionUrl\r\n            )\r\n          })\r\n        }\r\n        this.form.Attachment = formAttachment.join(',')\r\n        // 1草稿 2审核中 3通过 4退回\r\n        this.form.Status = type == 1 ? 1 : 3\r\n        const form = { ...this.form }\r\n        // console.log(form, 'this.form===1111')\r\n        // console.log(data, 'Sub===1111')\r\n        let _fun\r\n\r\n        if (this.isReturn) {\r\n          _fun = AuxReturnByReceipt\r\n        } else {\r\n          _fun = PickOutStore\r\n        }\r\n        _fun({\r\n          Receipt: form,\r\n          Sub: data\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            this.closeView()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.saveLoading = false\r\n        })\r\n      })\r\n    },\r\n    checkTb(list) {\r\n      let check = null\r\n      const isEmpty = ['', null, undefined]\r\n      if (this.isReturn) {\r\n        isEmpty.push(0)\r\n        check = ['ReturnCount', 'Warehouse_Location_Return']\r\n      } else {\r\n        check = ['OutStoreCount']\r\n      }\r\n      for (let i = 0; i < list.length; i++) {\r\n        const item = list[i]\r\n        for (let j = 0; j < check.length; j++) {\r\n          const c = check[j]\r\n          if (isEmpty.includes(item[c])) {\r\n            const cloumns = this.$refs.table.rootColumns\r\n            const element = cloumns.find((v) => v.Code === c)\r\n            return {\r\n              status: false,\r\n              msg: element?.Display_Name\r\n            }\r\n          }\r\n        }\r\n        delete item._X_ROW_KEY\r\n        delete item.WarehouseName\r\n        delete item.LocationName\r\n      }\r\n      return {\r\n        status: true,\r\n        msg: ''\r\n      }\r\n    },\r\n    openAddDialog(row) {\r\n      this.openAddList = true\r\n    },\r\n    // openAddDialog(row) {\r\n    //    if (this.form.SysProjectId) {\r\n    //     this.openAddList = true\r\n    //   } else {\r\n    //     this.$message({\r\n    //       message: '请先选择项目',\r\n    //       type: 'warning'\r\n    //     })\r\n    //     return\r\n    //   }\r\n    // },\r\n    closeView() {\r\n      closeTagView(this.$store, this.$route)\r\n    },\r\n    setSelectRow(multipleSelection) {\r\n      this.multipleSelection = multipleSelection\r\n    },\r\n    // 日期格式化\r\n    getDate(data) {\r\n      const date = data || new Date()\r\n      const year = date.getFullYear()\r\n      const month = ('0' + (date.getMonth() + 1)).slice(-2)\r\n      const day = ('0' + date.getDate()).slice(-2)\r\n      return `${year}-${month}-${day}`\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.app-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  .box-card-tb {\r\n    flex: 1;\r\n    margin-top: 8px;\r\n  }\r\n}\r\n// 表格工具栏css\r\n.toolbar-container {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  // margin: 10px 0 0 0;\r\n  // flex-wrap: nowrap;\r\n  ::v-deep .el-radio-group {\r\n    width: 400px;\r\n  }\r\n  .toolbar-title {\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    color: #333333;\r\n    span {\r\n      display: inline-block;\r\n      width: 2px;\r\n      height: 14px;\r\n      background: #009dff;\r\n      margin-right: 6px;\r\n      vertical-align: text-top;\r\n    }\r\n  }\r\n  .search-form {\r\n    width: 60%;\r\n    ::v-deep {\r\n      .el-form-item--small {\r\n        margin-bottom: 0;\r\n      }\r\n      .el-form-item__content {\r\n        width: 110px;\r\n      }\r\n      .last-btn {\r\n        display: flex;\r\n        justify-content: flex-end;\r\n        .el-form-item {\r\n          margin-right: 0;\r\n        }\r\n        .el-form-item__content {\r\n          width: 100%;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .statistics-container {\r\n    display: flex;\r\n    .statistics-item {\r\n      margin-right: 32px;\r\n      span:first-child {\r\n        display: inline-block;\r\n        font-size: 14px;\r\n        line-height: 18px;\r\n        font-weight: 500;\r\n        color: #999999;\r\n        margin-right: 16px !important;\r\n      }\r\n      span:last-child {\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n        color: #00c361;\r\n      }\r\n    }\r\n  }\r\n}\r\n.el-card {\r\n  ::v-deep {\r\n    .el-card__body {\r\n      display: flex;\r\n      flex-direction: column;\r\n    }\r\n  }\r\n\r\n  .tb-x {\r\n    flex: 1;\r\n    margin-bottom: 10px;\r\n    overflow: auto;\r\n  }\r\n}\r\n\r\n::v-deep .elDivder {\r\n  margin: 10px;\r\n}\r\n\r\n.pagination-container {\r\n  text-align: right;\r\n  margin-top: 10px;\r\n  padding: 0;\r\n}\r\n\r\n.upload-file-list {\r\n  & > div {\r\n    width: 100%;\r\n    height: 30px;\r\n    line-height: 30px;\r\n    padding-left: 15px;\r\n    padding-right: 15px;\r\n    cursor: pointer;\r\n    position: relative;\r\n    i {\r\n      margin-right: 10px;\r\n    }\r\n    i:last-child {\r\n      position: absolute;\r\n      top: 50%;\r\n      transform: translateY(-50%);\r\n      right: 15px;\r\n      color: #999999;\r\n      font-size: 18px;\r\n    }\r\n  }\r\n  & > div:hover {\r\n    background-color: #f8f8f8;\r\n    i:last-child {\r\n      color: #298dff;\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .el-form-item {\r\n  .el-form-item__content {\r\n    .el-tree-select-input {\r\n      width: 100% !important;\r\n    }\r\n  }\r\n}\r\n\r\nfooter {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckType.vue?vue&type=template&id=0743ce1a&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckType.vue", "mtime": 1757998876657}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgc3R5bGU9ImhlaWdodDogY2FsYygxMDB2aCAtIDMwMHB4KSI+CiAgPHZ4ZS10YWJsZQogICAgdi1sb2FkaW5nPSJ0YkxvYWRpbmciCiAgICA6ZW1wdHktcmVuZGVyPSJ7bmFtZTogJ05vdERhdGEnfSIKICAgIHNob3ctaGVhZGVyLW92ZXJmbG93CiAgICBlbGVtZW50LWxvYWRpbmctc3Bpbm5lcj0iZWwtaWNvbi1sb2FkaW5nIgogICAgZWxlbWVudC1sb2FkaW5nLXRleHQ9IuaLvOWRveWKoOi9veS4rSIKICAgIGVtcHR5LXRleHQ9IuaaguaXoOaVsOaNriIKICAgIGNsYXNzPSJjcy12eGUtdGFibGUiCiAgICBoZWlnaHQ9IjEwMCUiCiAgICBhbGlnbj0ibGVmdCIKICAgIHN0cmlwZQogICAgOmRhdGE9InRiRGF0YSIKICAgIHJlc2l6YWJsZQogICAgOmF1dG8tcmVzaXplPSJ0cnVlIgogICAgOnRvb2x0aXAtY29uZmlnPSJ7IGVudGVyYWJsZTogdHJ1ZSB9IgogID4KICAgIDx2eGUtY29sdW1uCiAgICAgIHNob3ctb3ZlcmZsb3c9InRvb2x0aXAiCiAgICAgIHNvcnRhYmxlCiAgICAgIGZpZWxkPSJOYW1lIgogICAgICB0aXRsZT0i5qOA5p+l57G75Z6LIgogICAgICB3aWR0aD0iY2FsYygxMDB2aC0yMDBweCkiCiAgICAvPgogICAgPCEtLSA8dnhlLWNvbHVtbiBmaXhlZD0icmlnaHQiIHRpdGxlPSLmk43kvZwiIHdpZHRoPSIyMDAiIGFsaWduPSJjZW50ZXIiIHNob3ctb3ZlcmZsb3c+CiAgICAgIDx0ZW1wbGF0ZSAjZGVmYXVsdD0ieyByb3cgfSI+CiAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJ0ZXh0IiBAY2xpY2s9ImVkaXRFdmVudChyb3cpIj7nvJbovpE8L2VsLWJ1dHRvbj4KICAgICAgICA8ZWwtZGl2aWRlciBkaXJlY3Rpb249InZlcnRpY2FsIiAvPgogICAgICAgIDxlbC1idXR0b24gdHlwZT0idGV4dCIgQGNsaWNrPSJyZW1vdmVFdmVudChyb3cpIj7liKDpmaQ8L2VsLWJ1dHRvbj4KICAgICAgPC90ZW1wbGF0ZT4KICAgIDwvdnhlLWNvbHVtbj4gLS0+CiAgPC92eGUtdGFibGU+CjwvZGl2Pgo="}, null]}
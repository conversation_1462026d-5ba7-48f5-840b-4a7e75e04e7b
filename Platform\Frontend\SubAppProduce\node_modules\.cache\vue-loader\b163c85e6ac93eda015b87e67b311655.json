{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\components\\Select\\SelectUser\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\components\\Select\\SelectUser\\index.vue", "mtime": 1757572678709}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQovKioNCiAqIOmAieaLqeW9k+WJjeW3peWOguS4i+eahOS6uuWRmA0KICovDQppbXBvcnQgeyBkZWVwQ2xvbmUgfSBmcm9tICdAL3V0aWxzJw0KaW1wb3J0IHsgR2V0RmFjdG9yeVBlb3BsZWxpc3QgfSBmcm9tICdAL2FwaS9QUk8vc2FsYXJ5Jw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdTZWxlY3RVc2VyJywNCiAgcHJvcHM6IHsNCiAgICB2YWx1ZTogew0KICAgICAgdHlwZTogW0FycmF5LCBOdW1iZXIsIFN0cmluZ10sDQogICAgICBkZWZhdWx0OiAnJw0KICAgIH0sDQogICAgbXVsdGlwbGU6IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiBmYWxzZQ0KICAgIH0sDQogICAgY29sbGFwc2VUYWdzOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UNCiAgICB9DQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGxpc3Q6IFtdLA0KICAgICAgc2VsZWN0ZWRWYWx1ZTogdGhpcy52YWx1ZQ0KICAgIH0NCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICB2YWx1ZTogew0KICAgICAgaGFuZGxlcih2YWwpIHsNCiAgICAgICAgdGhpcy5zZWxlY3RlZFZhbHVlID0gQXJyYXkuaXNBcnJheSh2YWwpID8gZGVlcENsb25lKHZhbCkgOiB2YWwNCiAgICAgIH0sDQogICAgICBpbW1lZGlhdGU6IHRydWUNCiAgICB9DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRMaXN0KCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGhhbmRsZUNoYW5nZSgpIHsNCiAgICAgIHRoaXMuJGVtaXQoJ2lucHV0JywgdGhpcy5zZWxlY3RlZFZhbHVlKQ0KICAgICAgdGhpcy4kZW1pdCgnY2hhbmdlJywgdGhpcy5zZWxlY3RlZFZhbHVlKQ0KICAgIH0sDQogICAgYXN5bmMgZ2V0TGlzdCgpIHsNCiAgICAgIEdldEZhY3RvcnlQZW9wbGVsaXN0KCkudGhlbihyZXMgPT4gew0KICAgICAgICB0aGlzLmxpc3QgPSByZXMuRGF0YQ0KICAgICAgfSkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAoBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Select/SelectUser", "sourcesContent": ["<template>\r\n  <el-select\r\n    v-model=\"selectedValue\"\r\n    placeholder=\"请选择\"\r\n    style=\"width: 100%\"\r\n    clearable\r\n    filterable\r\n    :multiple=\"multiple\"\r\n    @change=\"handleChange\"\r\n    :collapse-tags=\"collapseTags\"\r\n  >\r\n    <el-option\r\n      v-for=\"item in list\"\r\n      :key=\"item.Id\"\r\n      :label=\"item.Display_Name\"\r\n      :value=\"item.Id\"\r\n    />\r\n  </el-select>\r\n</template>\r\n<script>\r\n/**\r\n * 选择当前工厂下的人员\r\n */\r\nimport { deepClone } from '@/utils'\r\nimport { GetFactoryPeoplelist } from '@/api/PRO/salary'\r\n\r\nexport default {\r\n  name: 'SelectUser',\r\n  props: {\r\n    value: {\r\n      type: [Array, Number, String],\r\n      default: ''\r\n    },\r\n    multiple: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    collapseTags: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      list: [],\r\n      selectedValue: this.value\r\n    }\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(val) {\r\n        this.selectedValue = Array.isArray(val) ? deepClone(val) : val\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    handleChange() {\r\n      this.$emit('input', this.selectedValue)\r\n      this.$emit('change', this.selectedValue)\r\n    },\r\n    async getList() {\r\n      GetFactoryPeoplelist().then(res => {\r\n        this.list = res.Data\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\quality_Inspection\\start-inspect\\components\\add\\addDialog.vue?vue&type=style&index=0&id=5b86ae37&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\quality_Inspection\\start-inspect\\components\\add\\addDialog.vue", "mtime": 1757468113520}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQpAaW1wb3J0ICJ+QC9zdHlsZXMvbWl4aW4uc2NzcyI7DQpAaW1wb3J0ICJ+QC9zdHlsZXMvdmFyaWFibGVzLnNjc3MiOw0KDQouYWRkLWRpYWxvZyB7DQogIHotaW5kZXg6IDk5OTkgIWltcG9ydGFudDsNCg0KICA6OnYtZGVlcCB7DQogICAgLmVsLWRpYWxvZ19faGVhZGVyIHsNCiAgICAgIGJhY2tncm91bmQtY29sb3I6ICMyOThkZmY7DQoNCiAgICAgIC5lbC1kaWFsb2dfX3RpdGxlLA0KICAgICAgLmVsLWRpYWxvZ19fY2xvc2Ugew0KICAgICAgICBjb2xvcjogI2ZmZmZmZjsNCiAgICAgIH0NCiAgICB9DQoNCiAgICAuZWwtZGlhbG9nX19ib2R5IHsNCiAgICAgIG1heC1oZWlnaHQ6IDcwMHB4Ow0KICAgICAgb3ZlcmZsb3c6IGF1dG87DQogICAgICBAaW5jbHVkZSBzY3JvbGxCYXI7DQoNCiAgICAgICY6Oi13ZWJraXQtc2Nyb2xsYmFyIHsNCiAgICAgICAgd2lkdGg6IDhweDsNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg0KOjp2LWRlZXAgLmVsLWZvcm0taXRlbSB7DQogIC5lbC1mb3JtLWl0ZW1fX2NvbnRlbnQgew0KICAgICYgPiAuZWwtaW5wdXQgew0KICAgICAgd2lkdGg6IDI4MHB4Ow0KICAgIH0NCg0KICAgICYgPiAuZWwtc2VsZWN0IHsNCiAgICAgIHdpZHRoOiAyODBweDsNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["addDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyTA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "addDialog.vue", "sourceRoot": "src/views/PRO/quality_Inspection/start-inspect/components/add", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"100px\">\r\n      <el-form-item label=\"质检对象\" prop=\"Check_Object_Type\">\r\n        <el-select\r\n          v-model=\"form.Check_Object_Type\"\r\n          filterable\r\n          clearable\r\n          placeholder=\"请选择\"\r\n          :disabled=\"type == '查看'\"\r\n          @change=\"changeObject\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in CheckObjectData\"\r\n            :key=\"item.Id\"\r\n            :label=\"item.Display_Name\"\r\n            :value=\"item.Id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"质检节点\" prop=\"Check_Node_Id\">\r\n        <el-select\r\n          v-model=\"form.Check_Node_Id\"\r\n          filterable\r\n          clearable\r\n          placeholder=\"请选择\"\r\n          :disabled=\"!form.Check_Object_Type || type == '查看'\"\r\n          @change=\"changeCheckNode\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in CheckNodeList\"\r\n            :key=\"item.Id\"\r\n            :label=\"item.Display_Name\"\r\n            :value=\"item.Id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"名称\" prop=\"SteelName\">\r\n        <el-input v-model=\"form.SteelName\" type=\"text\" disabled>\r\n          <el-button\r\n            slot=\"append\"\r\n            icon=\"el-icon-search\"\r\n            :disabled=\"!form.Check_Object_Type || type == '查看'\"\r\n            @click=\"chooseComponent\"\r\n          />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"质检类型\" prop=\"Check_Type\">\r\n        <el-select\r\n          v-model=\"form.Check_Type\"\r\n          placeholder=\"请选择\"\r\n          :disabled=\"\r\n            !form.Check_Node_Id || CheckTypeList.length == 1 || type == '查看'\r\n          \"\r\n          @change=\"$forceUpdate()\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in CheckTypeList\"\r\n            :key=\"item.Id\"\r\n            :label=\"item.Name\"\r\n            :value=\"item.Id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item v-if=\"type != '查看'\">\r\n        <el-button @click=\"$emit('close')\">取 消</el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          :loading=\"SaveLoading\"\r\n          @click=\"AddSave('form', false)\"\r\n        >保 存</el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          :loading=\"SubmitLoading\"\r\n          @click=\"AddSave('form', true)\"\r\n        >提 交</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'\r\nimport elDragDialog from '@/directive/el-drag-dialog'\r\nimport {\r\n  GetDictionaryDetailListByCode,\r\n  GetNodeList\r\n} from '@/api/PRO/factorycheck'\r\nimport { AddLanch, Add } from '@/api/PRO/qualityInspect/start-Inspect'\r\nexport default {\r\n  directives: { elDragDialog },\r\n  components: {\r\n    DynamicDataTable\r\n  },\r\n  data() {\r\n    return {\r\n      SaveLoading: false,\r\n      SubmitLoading: false,\r\n      form: {\r\n        Check_Object_Type: '',\r\n        SteelName: '',\r\n        Check_Node_Id: '',\r\n        Check_Type: ''\r\n      },\r\n      chooseTitle: '', // 质检对象名称\r\n      currentComponent: '',\r\n      title: '',\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      width: '60%',\r\n      type: '', // 区分是否是新增（查看）\r\n      addComTitle: '添加构件',\r\n      CheckTypeList: [\r\n        {\r\n          Name: '质量',\r\n          Id: '1'\r\n        },\r\n        {\r\n          Name: '探伤',\r\n          Id: '2'\r\n        }\r\n      ], // 质检类型\r\n      CheckNodeList: [], // 质检节点\r\n      CheckObjectData: [], // 质检对象\r\n      rules: {\r\n        Check_Object_Type: [\r\n          { required: true, message: '请填写完整表单', trigger: 'change' }\r\n        ],\r\n        Check_Node_Id: [\r\n          { required: true, message: '请填写完整表单', trigger: 'change' }\r\n        ],\r\n        Check_Type: [\r\n          { required: true, message: '请填写完整表单', trigger: 'change' }\r\n        ],\r\n        SteelName: [\r\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getCheckType()\r\n  },\r\n  methods: {\r\n    async init(type, row) {\r\n      this.type = type || ''\r\n      if (type == '查看') {\r\n        console.log('row', row)\r\n\r\n        // this.form.Check_Object_Type = row.Check_Object_Type\r\n        await this.getCheckType()\r\n        console.log('this.CheckObjectData', this.CheckObjectData)\r\n        this.form.Check_Object_Type = this.CheckObjectData.find((v) => {\r\n          return v.Display_Name === row.Check_Object_Type\r\n        })?.Id\r\n        console.log('this.form.Check_Object_Type', this.form.Check_Object_Type)\r\n        this.changeObject(this.form.Check_Object_Type)\r\n        this.form.Check_Node_Id = row.Check_Node_Id\r\n        this.form.SteelName = row.SteelName\r\n        this.form.Check_Type = row.Check_Type\r\n      }\r\n    },\r\n    // 获取带过来的构件名称\r\n    handelName(val) {\r\n      console.log(val)\r\n      this.form.SteelName = val.SteelName ? val.SteelName : val.Code\r\n      this.form.Check_Object_Id = val.Id\r\n    },\r\n    async getCheckType() {\r\n      await GetDictionaryDetailListByCode({ dictionaryCode: 'Quality_Code' })\r\n        .then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.CheckObjectData = res.Data\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: 'res.Message'\r\n            })\r\n          }\r\n        })\r\n        .catch(() => {\r\n          console.log('sdfd')\r\n        })\r\n    },\r\n    changeObject(val) {\r\n      console.log('val', val)\r\n      this.form.Check_Node_Id = ''\r\n      this.form.Check_Type = ''\r\n      this.form.SteelName = ''\r\n      const checkObj = this.CheckObjectData.find((v) => {\r\n        return v.Id == val\r\n      })?.Display_Name\r\n      this.chooseTitle = checkObj\r\n      switch (checkObj) {\r\n        case '构件':\r\n          this.check_object_id = '0'\r\n          break\r\n        case '零件':\r\n          this.check_object_id = '1'\r\n          break\r\n        case '物料':\r\n          this.check_object_id = '2'\r\n          break\r\n        case '部件':\r\n          this.check_object_id = '3'\r\n          break\r\n        default:\r\n          this.check_object_id = '0'\r\n      }\r\n      console.log('this.check_object_id', this.check_object_id)\r\n      GetNodeList({ check_object_id: val, Check_Style: '1' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.CheckNodeList = res.Data\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: 'res.Message'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    changeCheckNode(val) {\r\n      this.CheckTypeList = []\r\n      this.form.Check_Type = ''\r\n      const checkTypeId = this.CheckNodeList.find((v) => {\r\n        return v.Id === val\r\n      })?.Check_Type\r\n      console.log(checkTypeId)\r\n      if (checkTypeId == '-1') {\r\n        this.CheckTypeList = [\r\n          {\r\n            Name: '质量',\r\n            Id: '1'\r\n          },\r\n          {\r\n            Name: '探伤',\r\n            Id: '2'\r\n          }\r\n        ]\r\n        this.form.Check_Type = '1'\r\n      } else if (checkTypeId == '1') {\r\n        this.CheckTypeList = [\r\n          {\r\n            Name: '质量',\r\n            Id: '1'\r\n          }\r\n        ]\r\n        this.form.Check_Type = '1'\r\n      } else if (checkTypeId == '2') {\r\n        this.CheckTypeList = [\r\n          {\r\n            Name: '探伤',\r\n            Id: '2'\r\n          }\r\n        ]\r\n        this.form.Check_Type = '2'\r\n      }\r\n      console.log(this.form.Check_Type)\r\n    },\r\n    chooseComponent() {\r\n      this.$store.dispatch('qualityCheck/changeRadio', true)\r\n      this.$emit('openDialog', this.check_object_id, this.chooseTitle)\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    AddSave(form, val) {\r\n      if (val) {\r\n        this.SubmitLoading = true\r\n      } else {\r\n        this.SaveLoading = true\r\n      }\r\n      this.$refs[form].validate((valid) => {\r\n        if (valid) {\r\n          Add({\r\n            SheetModel: {\r\n              ...this.form,\r\n              Check_Object_Type: this.check_object_id,\r\n              Check_Object_Type_Id: this.form.Check_Object_Type,\r\n              Check_Style: 1\r\n            },\r\n            sumbimt: val\r\n          }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                type: 'success',\r\n                message: '保存成功'\r\n              })\r\n              this.SubmitLoading = false\r\n              this.SaveLoading = false\r\n              this.$emit('close')\r\n              this.$emit('refresh')\r\n            } else {\r\n              this.SubmitLoading = false\r\n              this.SaveLoading = false\r\n              this.$message({\r\n                type: 'warning',\r\n                message: res.Message\r\n              })\r\n            }\r\n          })\r\n        } else {\r\n          this.SubmitLoading = false\r\n          this.SaveLoading = false\r\n          return false\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/styles/mixin.scss\";\r\n@import \"~@/styles/variables.scss\";\r\n\r\n.add-dialog {\r\n  z-index: 9999 !important;\r\n\r\n  ::v-deep {\r\n    .el-dialog__header {\r\n      background-color: #298dff;\r\n\r\n      .el-dialog__title,\r\n      .el-dialog__close {\r\n        color: #ffffff;\r\n      }\r\n    }\r\n\r\n    .el-dialog__body {\r\n      max-height: 700px;\r\n      overflow: auto;\r\n      @include scrollBar;\r\n\r\n      &::-webkit-scrollbar {\r\n        width: 8px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .el-form-item {\r\n  .el-form-item__content {\r\n    & > .el-input {\r\n      width: 280px;\r\n    }\r\n\r\n    & > .el-select {\r\n      width: 280px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
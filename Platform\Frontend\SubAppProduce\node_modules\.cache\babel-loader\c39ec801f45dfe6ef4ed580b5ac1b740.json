{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckType.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckType.vue", "mtime": 1758085989932}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetCheckTypeList", "DeleteCheckType", "props", "checkType", "type", "Object", "default", "sysProjectId", "String", "data", "tbLoading", "tbData", "watch", "handler", "newName", "getCheckTypeList", "deep", "newVal", "Id", "immediate", "mounted", "methods", "_this", "check_object_id", "Bom_Level", "Code", "then", "res", "IsSucceed", "Data", "console", "log", "$message", "message", "Message", "removeEvent", "row", "_this2", "$confirm", "confirmButtonText", "cancelButtonText", "id", "catch", "editEvent", "$emit"], "sources": ["src/views/PRO/project-config/project-quality/components/CheckType.vue"], "sourcesContent": ["<template>\n  <div style=\"height: calc(100vh - 300px)\">\n    <vxe-table\n      v-loading=\"tbLoading\"\n      :empty-render=\"{name: 'NotData'}\"\n      show-header-overflow\n      element-loading-spinner=\"el-icon-loading\"\n      element-loading-text=\"拼命加载中\"\n      empty-text=\"暂无数据\"\n      class=\"cs-vxe-table\"\n      height=\"100%\"\n      align=\"left\"\n      stripe\n      :data=\"tbData\"\n      resizable\n      :auto-resize=\"true\"\n      :tooltip-config=\"{ enterable: true }\"\n    >\n      <vxe-column\n        show-overflow=\"tooltip\"\n        sortable\n        field=\"Name\"\n        title=\"检查类型\"\n        width=\"calc(100vh-200px)\"\n      />\n      <!-- <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" align=\"center\" show-overflow>\n        <template #default=\"{ row }\">\n          <el-button type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\n          <el-divider direction=\"vertical\" />\n          <el-button type=\"text\" @click=\"removeEvent(row)\">删除</el-button>\n        </template>\n      </vxe-column> -->\n    </vxe-table>\n  </div>\n</template>\n\n<script>\nimport { GetCheckTypeList } from '@/api/PRO/factorycheck'\nimport { DeleteCheckType } from '@/api/PRO/factorycheck'\n\nexport default {\n  props: {\n    checkType: {\n      type: Object,\n      default: () => ({})\n    },\n    sysProjectId: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      tbLoading: false,\n      tbData: []\n    }\n  },\n  watch: {\n    checkType: {\n      handler(newName) {\n        this.checkType = newName\n        if (this.sysProjectId) {\n          this.getCheckTypeList()\n        }\n      },\n      deep: true\n    },\n    sysProjectId: {\n      handler(newVal) {\n        if (newVal && this.checkType && this.checkType.Id) {\n          this.getCheckTypeList()\n        }\n      },\n      immediate: true\n    }\n  },\n  mounted() {\n    // this.getCheckTypeList()\n  },\n  methods: {\n    getCheckTypeList() {\n      this.tbLoading = true\n      GetCheckTypeList({ check_object_id: this.checkType.Id, Bom_Level: this.checkType.Code, sysProjectId: this.sysProjectId }).then((res) => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data\n          this.tbLoading = false\n          console.log(res.Data)\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n          this.tbLoading = false\n        }\n      })\n    },\n    removeEvent(row) {\n      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DeleteCheckType({ id: row.Id }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getCheckTypeList()\n            } else {\n              this.$message({\n                type: 'error',\n                message: res.Message\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    },\n    editEvent(row) {\n      console.log('row', row)\n      this.$emit('optionFn', row)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n/* .vxe-table {\n  max-height: 100%;\n} */\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA,SAAAA,gBAAA;AACA,SAAAC,eAAA;AAEA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAC,YAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,MAAA;IACA;EACA;EACAC,KAAA;IACAT,SAAA;MACAU,OAAA,WAAAA,QAAAC,OAAA;QACA,KAAAX,SAAA,GAAAW,OAAA;QACA,SAAAP,YAAA;UACA,KAAAQ,gBAAA;QACA;MACA;MACAC,IAAA;IACA;IACAT,YAAA;MACAM,OAAA,WAAAA,QAAAI,MAAA;QACA,IAAAA,MAAA,SAAAd,SAAA,SAAAA,SAAA,CAAAe,EAAA;UACA,KAAAH,gBAAA;QACA;MACA;MACAI,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;EAAA,CACA;EACAC,OAAA;IACAN,gBAAA,WAAAA,iBAAA;MAAA,IAAAO,KAAA;MACA,KAAAZ,SAAA;MACAV,gBAAA;QAAAuB,eAAA,OAAApB,SAAA,CAAAe,EAAA;QAAAM,SAAA,OAAArB,SAAA,CAAAsB,IAAA;QAAAlB,YAAA,OAAAA;MAAA,GAAAmB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAN,KAAA,CAAAX,MAAA,GAAAgB,GAAA,CAAAE,IAAA;UACAP,KAAA,CAAAZ,SAAA;UACAoB,OAAA,CAAAC,GAAA,CAAAJ,GAAA,CAAAE,IAAA;QACA;UACAP,KAAA,CAAAU,QAAA;YACA5B,IAAA;YACA6B,OAAA,EAAAN,GAAA,CAAAO;UACA;UACAZ,KAAA,CAAAZ,SAAA;QACA;MACA;IACA;IACAyB,WAAA,WAAAA,YAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACApC,IAAA;MACA,GACAsB,IAAA;QACAzB,eAAA;UAAAwC,EAAA,EAAAL,GAAA,CAAAlB;QAAA,GAAAQ,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAS,MAAA,CAAAL,QAAA;cACA5B,IAAA;cACA6B,OAAA;YACA;YACAI,MAAA,CAAAtB,gBAAA;UACA;YACAsB,MAAA,CAAAL,QAAA;cACA5B,IAAA;cACA6B,OAAA,EAAAN,GAAA,CAAAO;YACA;UACA;QACA;MACA,GACAQ,KAAA;QACAL,MAAA,CAAAL,QAAA;UACA5B,IAAA;UACA6B,OAAA;QACA;MACA;IACA;IACAU,SAAA,WAAAA,UAAAP,GAAA;MACAN,OAAA,CAAAC,GAAA,QAAAK,GAAA;MACA,KAAAQ,KAAA,aAAAR,GAAA;IACA;EACA;AACA", "ignoreList": []}]}
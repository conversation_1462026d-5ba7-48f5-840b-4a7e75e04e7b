<template>
  <div style="margin-top: 16px;">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="110px"
      style="width: 100%"
    >
      <h3>基本信息</h3>
      <el-form-item label="班组名称" prop="Name">
        <el-input v-model="form.Name" />
      </el-form-item>
      <el-form-item label="班组长" prop="Manager_UserId">
        <el-select
          v-model="form.Manager_UserId"
          class="w100"
          clearable
          filterable
          placeholder="请选择"
          @change="managerChange"
        >
          <el-option
            v-for="item in classList"
            :key="item.Id"
            :label="item.Display_Name"
            :value="item.Id"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="负荷提醒线" prop="Load">
        <el-input
          v-model.number="form.Load"
          placeholder="请输入"
          class="input-number"
          type="number"
          min="0"
          @blur="inputBlur(form.Load)"
        >
          <template slot="append">吨</template>
        </el-input>
      </el-form-item> -->
      <el-form-item label="班组月均负荷" prop="Month_Avg_Load">
        <el-input
          v-model.number="form.Month_Avg_Load"
          placeholder="请输入"
          class="input-number"
          type="number"
          min="0"
          @blur="inputBlur(form.Month_Avg_Load)"
        >
          <template slot="append">吨</template>
        </el-input>
      </el-form-item>
      <el-form-item label="排序号" prop="Sort">
        <el-input
          v-model="form.Sort"
          type="text"
          @input="handleNumberInput"
        />
      </el-form-item>
      <el-form-item label="是否外协" prop="Is_Outsource">
        <el-radio-group v-model="form.Is_Outsource">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否启用" prop="Is_Enabled">
        <el-radio-group v-model="form.Is_Enabled">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="关联仓库/库位">
        <el-select
          ref="WarehouseRef"
          v-model="form.Warehouse_Id"
          clearable
          placeholder="请选择仓库"
          style="width: 250px; margin-right: 10px;"
          @change="wareChange"
        >
          <el-option
            v-for="p in warehouses"
            :key="p.Id"
            :label="p.Display_Name"
            :value="p.Id"
          />
        </el-select>
        <el-select
          ref="LocationRef"
          v-model="form.Location_Id"
          clearable
          placeholder="请选择库位"
          style="width: 250px;"
          :disabled="!form.Warehouse_Id"
        >
          <el-option
            v-for="p in locations"
            :key="p.Id"
            :label="p.Display_Name"
            :value="p.Id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="Is_Workshop_Enabled"
        label="所属车间"
        prop="Workshop_Id"
      >
        <el-select
          v-model="form.Workshop_Id"
          class="w100"
          clearable
          filterable
          placeholder="请选择"
          @change="workshopChange"
        >
          <el-option
            v-for="item in workshopList"
            :key="item.Id"
            :label="item.Display_Name"
            :value="item.Id"
          />
        </el-select>
      </el-form-item>
      <h3>班组成员</h3>
      <div class="tag-x">
        <div class="tag-wrapper">
          <el-tag
            v-for="tag in tags"
            :key="tag.Id"
            size="large"
            closable
            type="info"
            @close="deleteTag(tag)"
          >
            {{ tag.Display_Name }}
          </el-tag>
        </div>
        <div class="add-btn" @click.stop="handleAdd">
          <el-icon class="el-icon-plus" />
          <span>添加</span>
        </div>
      </div>
    </el-form>
    <footer>
      <el-button @click="$emit('close')">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </footer>
    <add-user
      v-if="showDialog"
      :tags="tags"
      :show.sync="showDialog"
      @selectList="getSelectList"
    />
  </div>
</template>

<script>
import AddUser from './AddUser'
import {
  GetWorkingTeamInfo,
  SaveWorkingTeams,
  GetFactoryPeoplelist
} from '@/api/PRO/technology-lib'
import { GetWorkshopPageList } from '@/api/PRO/basic-information/workshop'
import { GetLocationList, GetTotalWarehouseListOfCurFactory } from '@/api/PRO/pro-stock'

export default {
  components: {
    AddUser
  },
  data() {
    return {
      showDialog: false,
      tags: [],
      form: {
        Name: '',
        Manager_UserName: '',
        Manager_UserId: '',
        Load: '',
        Workshop_Name: '', // 所属车间
        Workshop_Id: '', // 所属车间Id
        Month_Avg_Load: null,
        Sort: '0',
        Is_Outsource: false,
        Is_Enabled: true,
        Warehouse_Id: '',
        Location_Id: ''
      },
      rules: {
        Name: [{ required: true, message: '请输入', trigger: 'blur' }],
        Sort: [{ required: true, message: '请输入', trigger: 'blur' }]
      },
      classList: [],
      workshopList: [],
      warehouses: [],
      locations: [],

      Is_Workshop_Enabled: ''
    }
  },
  created() {},
  mounted() {
    this.getTeam()
    this.getWarehouseList()
  },
  methods: {
    // 输入框校验
    inputBlur(e) {
      if (e < 0) {
        this.form.Load = 0
      }
    },
    // 数据初始化
    initData(id, Is_Workshop_Enabled) {
      if (id) {
        this.isEdit = true
        GetWorkingTeamInfo({
          id
        }).then((res) => {
          if (res.IsSucceed) {
            const {
              Manager_UserName,
              Manager_UserId,
              Load,
              Name,
              Users,
              Id,
              Workshop_Name,
              Workshop_Id,
              Month_Avg_Load,
              Sort,
              Is_Outsource,
              Is_Enabled,
              Warehouse_Id,
              Location_Id
            } = res.Data
            this.form.Manager_UserName = Manager_UserName
            this.form.Manager_UserId = Manager_UserId
            this.form.Load = Load
            this.form.Name = Name
            this.isEditId = Id
            this.form.Workshop_Name = Workshop_Name
            this.form.Workshop_Id = Workshop_Id
            this.Is_Workshop_Enabled = Is_Workshop_Enabled
            this.form.Month_Avg_Load = Month_Avg_Load
            this.form.Sort = Sort
            this.form.Is_Outsource = Is_Outsource
            this.form.Is_Enabled = Is_Enabled

            if (Warehouse_Id) {
              this.form.Warehouse_Id = Warehouse_Id
              this.wareChange(Warehouse_Id)
              this.form.Location_Id = Location_Id
            }
            this.tags = Users.map((v) => {
              this.$set(v, 'Display_Name', v.User_Name)
              this.$set(v, 'Id', v.User_Id)
              return v
            })
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      } else {
        this.Is_Workshop_Enabled = Is_Workshop_Enabled
      }
    },

    managerChange(val) {
      const items = this.classList.find((i) => i.Id === val)
      if (items) {
        this.form.Manager_UserName = items.Display_Name
        console.log(this.tags.find(v => v.Id !== items.Id))
        const idx = this.tags.findIndex(v => v.Id === items.Id)
        if (idx === -1) {
          this.tags.push({
            Display_Name: items.Display_Name,
            Id: items.Id,
            User_Id: items.Id,
            User_Name: items.Display_Name
          })
        }
      }
    },
    workshopChange(val) {
      this.form.Workshop_Name = this.workshopList.find(
        (i) => i.Id === val
      ).Display_Name
    },
    getTeam() {
      GetFactoryPeoplelist().then((res) => {
        if (res.IsSucceed) {
          this.classList = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
      GetWorkshopPageList({ Page: -1 }).then((res) => {
        if (res.IsSucceed) {
          this.workshopList = res.Data.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    handleAdd() {
      this.showDialog = true
    },
    getSelectList(list) {
      const objKey = {}
      this.tags = [...this.tags, ...list].reduce((acc, cur) => {
        objKey[cur.Id] ? '' : (objKey[cur.Id] = true && acc.push(cur))
        return acc
      }, [])
    },
    deleteTag(item) {
      const index = this.tags.findIndex((v) => v.Id === item.Id)
      index !== -1 && this.tags.splice(index, 1)
    },
    handleSubmit() {
      // return
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.Warehouse_Id && !this.form.Location_Id) {
            this.$message({
              message: '请选择关联仓库的库位',
              type: 'error'
            })
            return false
          }
          const subObj = {
            Name: this.form.Name,
            Manager_UserName: this.form.Manager_UserName,
            Manager_UserId: this.form.Manager_UserId,
            Load: this.form.Load,
            Members: this.tags.map((v) => v.Id),
            Workshop_Id: this.form.Workshop_Id,
            Month_Avg_Load: this.form.Month_Avg_Load,
            Sort: this.form.Sort,
            Is_Outsource: this.form.Is_Outsource,
            Is_Enabled: this.form.Is_Enabled,
            Warehouse_Id: this.form.Warehouse_Id,
            Location_Id: this.form.Location_Id
          }
          this.isEdit && (subObj.Id = this.isEditId)
          SaveWorkingTeams(subObj).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                message: '操作成功',
                type: 'success'
              })
              this.$emit('close')
              this.$emit('refresh')
            } else {
              this.$message({
                message: res.Message,
                type: 'error'
              })
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    getWarehouseList() {
      GetTotalWarehouseListOfCurFactory({ }).then(res => {
        if (res.IsSucceed) {
          this.warehouses = res.Data
        }
      })
    },
    wareChange(v) {
      this.form.Location_Id = ''
      GetLocationList({
        Warehouse_Id: v
      }).then(res => {
        if (res.IsSucceed) {
          this.locations = res.Data
        }
      })
    },
    handleNumberInput(value) {
      // 移除所有非数字字符
      let cleaned = value.replace(/[^\d]/g, '')

      // 处理前导零：如果是 0 开头且有后续数字，移除前导零
      if (cleaned.length > 1 && cleaned.startsWith('0')) {
        cleaned = cleaned.replace(/^0+/, '')
        // 如果全部是0，保留一个0
        if (cleaned === '') cleaned = '0'
      }

      // 更新值
      this.form.Sort = cleaned
    }
  }
}
</script>

<style scoped lang="scss">
@import "~@/styles/mixin.scss";

h3 {
  color: #298dff;
}

.tag-x {
  text-align: left;

  .tag-wrapper {
    display: inline-block;
    flex-wrap: wrap;
    max-height: 160px;
    overflow: auto;
    @include scrollBar;

    .el-tag {
      margin: 8px 0 0 8px;
    }
  }

  .add-btn {
    margin-top: 12px;
    text-align: center;
    cursor: pointer;
    height: 32px;
    line-height: 32px;
    background: rgba(41, 141, 255, 0.03);
    color: #298dff;
    border: 1px dashed rgba(41, 141, 255, 0.32156862745098036);
    border-radius: 4px;
  }
}

footer {
  margin: 20px;
  text-align: right;

  &:first-child {
    margin-right: 20px;
  }
}

::v-deep {
  .input-number {
    input {
      padding-right: 2px;
    }
  }
}
</style>

{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\api\\PRO\\qualityInspect\\quality-management.js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\api\\PRO\\qualityInspect\\quality-management.js", "mtime": 1757572678698}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "GetPageQualityManagement", "data", "url", "method", "ManageAdd", "AddLanch", "GetDictionaryDetailListByCode", "GetNodeList", "GetPartAndSteelBacrode", "EntityQualityManagement", "SaveTesting", "GetFactoryPeoplelist", "GetSheetDwg", "GetCanvasSheetDwg", "SavePass", "timeout", "SubmitLanch", "<PERSON><PERSON><PERSON><PERSON>", "BatchManageSaveCheck", "GetEditById", "GetCheckingEntity", "GetPageFeedBack", "SaveFeedBack", "RectificationRecord", "GetCompPartForSpotCheckPageList", "GetSpotCheckingEntity", "GetCompQISummary", "ExportQISummary", "ImportQISummary", "SaveQIReportData", "SaveToleranceSetting", "DeleteToleranceSetting", "GetToleranceSettingList", "InsertBatchTrack", "BatchUpdateTrackEditableFields", "ExportstBatchTrackFromSourceAsync", "ChangeCheckUser"], "sources": ["D:/project/platform_framework_master/platform_framework/Platform/Frontend/SubAppProduce/src/api/PRO/qualityInspect/quality-management.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取质检管理列表 (Auth)\r\nexport function GetPageQualityManagement(data) {\r\n  return request({\r\n    url: '/PRO/Inspection/GetPageQualityManagement',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 管理新增单个质检单 (Auth)\r\nexport function ManageAdd(data) {\r\n  return request({\r\n    url: '/PRO/Inspection/ManageAdd',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 批量新增质检单 (Auth)\r\nexport function AddLanch(data) {\r\n  return request({\r\n    url: '/PRO/Inspection/AddLanch',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 获取质检对象 (Auth)\r\nexport function GetDictionaryDetailListByCode(data) {\r\n  return request({\r\n    url: '/SYS/Dictionary/GetDictionaryDetailListByCode',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 获取质检节点 (Auth)\r\nexport function GetNodeList(data) {\r\n  return request({\r\n    url: '/PRO/Inspection/GetNodeList',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 获取零件或者构件信息 (Auth)\r\nexport function GetPartAndSteelBacrode(data) {\r\n  return request({\r\n    url: '/PRO/Inspection/GetPartAndSteelBacrode',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 查看质检详情 (Auth)\r\nexport function EntityQualityManagement(data) {\r\n  return request({\r\n    url: '/PRO/Inspection/EntityQualityManagement',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 质检管理 质检 (Auth)\r\nexport function SaveTesting(data) {\r\n  return request({\r\n    url: '/PRO/Inspection/SaveTesting',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 获取人员列表\r\nexport function GetFactoryPeoplelist(data) {\r\n  return request({\r\n    url: '/PRO/Factory/GetFactoryPeoplelist',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 获取深化图纸\r\nexport function GetSheetDwg(data) {\r\n  return request({\r\n    url: '/PRO/Inspection/GetSheetDwg',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 获取深化图纸(批量)\r\nexport function GetCanvasSheetDwg(data) {\r\n  return request({\r\n    url: '/PRO/Inspection/GetCanvasSheetDwg',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 批量合格 (Auth)\r\nexport function SavePass(data) {\r\n  return request({\r\n    url: '/PRO/Inspection/SavePass',\r\n    method: 'post',\r\n    data,\r\n    timeout: 20 * 60 * 1000\r\n  })\r\n}\r\n// 发起质检列表提交 (Auth)\r\nexport function SubmitLanch(data) {\r\n  return request({\r\n    url: '/PRO/Inspection/SubmitLanch',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 根据id进行删除质检 (Auth)\r\nexport function DelLanch(data) {\r\n  return request({\r\n    url: '/PRO/Inspection/DelLanch',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 质检管理批量添加\r\nexport function BatchManageSaveCheck(data) {\r\n  return request({\r\n    url: '/PRO/Inspection/BatchManageSaveCheck',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 质检查看\r\nexport function GetEditById(data) {\r\n  return request({\r\n    url: '/PRO/Inspection/GetEditById',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 抽检查看\r\nexport function GetCheckingEntity(data) {\r\n  return request({\r\n    url: '/PRO/Inspection/GetCheckingEntity',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 质检反馈列表\r\nexport function GetPageFeedBack(data) {\r\n  return request({\r\n    url: '/PRO/Inspection/GetPageFeedBack',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 整改复核\r\nexport function SaveFeedBack(data) {\r\n  return request({\r\n    url: '/PRO/Inspection/SaveFeedBack',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 整改复核\r\nexport function RectificationRecord(data) {\r\n  return request({\r\n    url: '/PRO/Inspection/RectificationRecord',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\nexport function GetCompPartForSpotCheckPageList(data) {\r\n  return request({\r\n    url: '/PRO/Inspection/GetCompPartForSpotCheckPageList',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\nexport function GetSpotCheckingEntity(data) {\r\n  return request({\r\n    url: '/PRO/Inspection/GetSpotCheckingEntity',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\nexport function GetCompQISummary(data) {\r\n  return request({\r\n    // url: '/PRO/Inspection/GetCompQISummary',\r\n    url: '/PRO/Inspection/GetCompQISummary',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\nexport function ExportQISummary(data) {\r\n  return request({\r\n    url: '/PRO/Inspection/ExportQISummary',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\nexport function ImportQISummary(data) {\r\n  return request({\r\n    url: '/PRO/Inspection/ImportQISummary',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\nexport function SaveQIReportData(data) {\r\n  return request({\r\n    url: '/PRO/Inspection/SaveQIReportData',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\nexport function SaveToleranceSetting(data) {\r\n  return request({\r\n    url: '/pro/Inspection/SaveToleranceSetting',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\nexport function DeleteToleranceSetting(data) {\r\n  return request({\r\n    url: '/pro/Inspection/DeleteToleranceSetting',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\nexport function GetToleranceSettingList(data) {\r\n  return request({\r\n    url: '/pro/Inspection/GetToleranceSettingList',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\nexport function InsertBatchTrack(data) {\r\n  return request({\r\n    url: '/pro/Track/InsertBatchTrack',\r\n    method: 'post',\r\n    timeout: 5 * 60 * 1000,\r\n    data\r\n  })\r\n}\r\n\r\nexport function BatchUpdateTrackEditableFields(data) {\r\n  return request({\r\n    url: '/pro/Track/BatchUpdateTrackEditableFields',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\nexport function ExportstBatchTrackFromSourceAsync(data) {\r\n  return request({\r\n    url: '/pro/Track/ExportstBatchTrackFromSourceAsync',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 质检分配 - 更改质检人员\r\nexport function ChangeCheckUser(data) {\r\n  return request({\r\n    url: '/pro/Inspection/ChangeCheckUser',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,wBAAwBA,CAACC,IAAI,EAAE;EAC7C,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,0CAA0C;IAC/CC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASG,SAASA,CAACH,IAAI,EAAE;EAC9B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASI,QAAQA,CAACJ,IAAI,EAAE;EAC7B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASK,6BAA6BA,CAACL,IAAI,EAAE;EAClD,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,+CAA+C;IACpDC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASM,WAAWA,CAACN,IAAI,EAAE;EAChC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASO,sBAAsBA,CAACP,IAAI,EAAE;EAC3C,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,wCAAwC;IAC7CC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASQ,uBAAuBA,CAACR,IAAI,EAAE;EAC5C,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,yCAAyC;IAC9CC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASS,WAAWA,CAACT,IAAI,EAAE;EAChC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASU,oBAAoBA,CAACV,IAAI,EAAE;EACzC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,mCAAmC;IACxCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASW,WAAWA,CAACX,IAAI,EAAE;EAChC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASY,iBAAiBA,CAACZ,IAAI,EAAE;EACtC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,mCAAmC;IACxCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASa,QAAQA,CAACb,IAAI,EAAE;EAC7B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA,IAAI;IACJc,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG;EACrB,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASC,WAAWA,CAACf,IAAI,EAAE;EAChC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASgB,QAAQA,CAAChB,IAAI,EAAE;EAC7B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASiB,oBAAoBA,CAACjB,IAAI,EAAE;EACzC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASkB,WAAWA,CAAClB,IAAI,EAAE;EAChC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASmB,iBAAiBA,CAACnB,IAAI,EAAE;EACtC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,mCAAmC;IACxCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASoB,eAAeA,CAACpB,IAAI,EAAE;EACpC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASqB,YAAYA,CAACrB,IAAI,EAAE;EACjC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASsB,mBAAmBA,CAACtB,IAAI,EAAE;EACxC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,qCAAqC;IAC1CC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA,OAAO,SAASuB,+BAA+BA,CAACvB,IAAI,EAAE;EACpD,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,iDAAiD;IACtDC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA,OAAO,SAASwB,qBAAqBA,CAACxB,IAAI,EAAE;EAC1C,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,uCAAuC;IAC5CC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASyB,gBAAgBA,CAACzB,IAAI,EAAE;EACrC,OAAOF,OAAO,CAAC;IACb;IACAG,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAAS0B,eAAeA,CAAC1B,IAAI,EAAE;EACpC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAAS2B,eAAeA,CAAC3B,IAAI,EAAE;EACpC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAAS4B,gBAAgBA,CAAC5B,IAAI,EAAE;EACrC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAAS6B,oBAAoBA,CAAC7B,IAAI,EAAE;EACzC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAAS8B,sBAAsBA,CAAC9B,IAAI,EAAE;EAC3C,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,wCAAwC;IAC7CC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAAS+B,uBAAuBA,CAAC/B,IAAI,EAAE;EAC5C,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,yCAAyC;IAC9CC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASgC,gBAAgBA,CAAChC,IAAI,EAAE;EACrC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,MAAM;IACdY,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IACtBd,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASiC,8BAA8BA,CAACjC,IAAI,EAAE;EACnD,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,2CAA2C;IAChDC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASkC,iCAAiCA,CAAClC,IAAI,EAAE;EACtD,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,8CAA8C;IACnDC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASmC,eAAeA,CAACnC,IAAI,EAAE;EACpC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ", "ignoreList": []}]}
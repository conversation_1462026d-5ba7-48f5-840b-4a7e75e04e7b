{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\change-management\\contact-list\\components\\HandleEdit.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\change-management\\contact-list\\components\\HandleEdit.vue", "mtime": 1757468112546}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["allCodes", "defaultPrefix", "filterByCodeType", "generateAllCodes", "getAllCodesByType", "GetCompTypeTree", "GetPartTypeList", "GetLibList", "deepClone", "CheckCanMocName", "data", "list", "Value", "NewValue", "Field_Type", "IsCoreField", "precision", "Code", "options", "partTypeOption", "isSaveDisabled", "processOption", "treeSelectParams", "placeholder", "clearable", "ObjectTypeList", "clickParent", "props", "children", "label", "value", "methods", "init", "row", "defaultRow", "isEdit", "tbData", "tbCode", "map", "item", "CPCode", "_tbData", "JSON", "parse", "stringify", "console", "log", "_columns", "CodeType", "v", "Name", "Display_Name", "disabled", "changeCode", "$store", "state", "contactList", "uuid", "selectItem", "filter", "length", "some", "getObjectTypeList", "getPartTypeList", "getLibList", "handleClose", "$emit", "handleAdd", "index", "splice", "_this", "Type", "professional", "then", "res", "IsSucceed", "Data", "$nextTick", "_", "$refs", "treeSelectObjectType1", "treeDataUpdateFun", "$message", "type", "message", "Message", "Part_Grade", "selectChange", "code", "cur", "find", "undefined", "handleDelete", "element", "idx", "findIndex", "getAvailableOptions", "currentIndex", "selectedCodes", "option", "includes", "handleSave", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "success", "isMustInputs", "<PERSON><PERSON><PERSON><PERSON>", "wrap", "_callee$", "_context", "prev", "next", "isMustInput", "for<PERSON>ach", "checkName", "sent", "abrupt", "stop", "_this3", "_callee2", "flag", "key", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_callee2$", "_context2", "Id", "MocIdBefore", "MocAggregateIdBefore", "NewName", "findSimilarTypeItems", "concat", "targetItem", "newName", "i", "handleCancel", "_this4"], "sources": ["src/views/PRO/change-management/contact-list/components/HandleEdit.vue"], "sourcesContent": ["<template>\r\n  <div class=\"handle-edit-container\">\r\n    <div class=\"handle-edit\">\r\n      <el-button v-if=\"!list.length\" type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd(-1)\">添加</el-button>\r\n      <div v-for=\"(item, index) in list\" :key=\"index\" class=\"flex-row\">\r\n        <div class=\"flex-item flex-item-1\">\r\n          <el-input\r\n            v-model=\"item.Value\"\r\n            style=\"width: 100%;\"\r\n            clearable\r\n            readonly\r\n            placeholder=\"请输入\"\r\n            class=\"input-with-select\"\r\n          >\r\n            <el-select\r\n              slot=\"prepend\"\r\n              v-model=\"item.Code\"\r\n              placeholder=\"请选择\"\r\n              style=\"width: 160px\"\r\n              @change=\"selectChange($event,item)\"\r\n            >\r\n              <el-option v-for=\"option in getAvailableOptions(index)\" :key=\"option.Code\" :label=\"option.Name\" :value=\"option.Code\" />\r\n            </el-select>\r\n\r\n          </el-input>\r\n        </div>\r\n        <div class=\"flxe-item2\">\r\n          <span>变更后：</span>\r\n        </div>\r\n        <div class=\"flex-item\">\r\n\r\n          <el-tree-select\r\n            v-if=\"item.Code === 'SteelType'\"\r\n            ref=\"treeSelectObjectType1\"\r\n            v-model=\"item.NewValue\"\r\n            class=\"cs-tree-x\"\r\n            style=\"width: 100%;\"\r\n            :select-params=\"treeSelectParams\"\r\n            :tree-params=\"ObjectTypeList\"\r\n            value-key=\"Id\"\r\n          />\r\n          <el-select v-else-if=\"item.Code==='PartType'\" v-model=\"item.NewValue\" placeholder=\"请选择\" clearable=\"\">\r\n            <el-option\r\n              v-for=\"cur in partTypeOption\"\r\n              :key=\"cur.Code\"\r\n              :label=\"cur.Name\"\r\n              :value=\"cur.Code\"\r\n            />\r\n          </el-select>\r\n          <el-select v-else-if=\"item.Code==='Technology_Code'\" v-model=\"item.NewValue\" placeholder=\"请选择\" clearable=\"\">\r\n            <el-option\r\n              v-for=\"cur in processOption\"\r\n              :key=\"cur.Code\"\r\n              :label=\"cur.Code\"\r\n              :value=\"cur.Code\"\r\n            />\r\n          </el-select>\r\n          <el-input-number\r\n            v-else-if=\"item.Field_Type==='number'\"\r\n            v-model=\"item.NewValue\"\r\n            :min=\"0\"\r\n            :precision=\"item.precision || 0\"\r\n            style=\"width: 100%\"\r\n            class=\"cs-number-btn-hidden\"\r\n          />\r\n          <el-input v-else v-model.trim=\"item.NewValue\" style=\"width: 100%;\" clearable placeholder=\"请输入\" />\r\n\r\n        </div>\r\n        <div class=\"flex-item3 btn-x\">\r\n          <el-button\r\n            v-if=\"(list.length < options.length)\"\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            circle\r\n            @click=\"handleAdd(index)\"\r\n          />\r\n          <el-button\r\n            type=\"danger\"\r\n            icon=\"el-icon-delete\"\r\n            circle\r\n            @click=\"handleDelete(item, index)\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"btn-group\">\r\n      <el-button @click=\"handleClose\">取消</el-button>\r\n      <el-button type=\"primary\" :disabled=\"isSaveDisabled\" @click=\"handleSave\">保存</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { allCodes, defaultPrefix, filterByCodeType, generateAllCodes, getAllCodesByType } from '../utils'\r\nimport { GetCompTypeTree } from '@/api/PRO/component-type'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\nimport { GetLibList } from '@/api/PRO/technology-lib'\r\nimport { deepClone } from '@/utils'\r\nimport { CheckCanMocName } from '@/api/PRO/changeManagement'\r\n\r\nexport default {\r\n\r\n  data() {\r\n    return {\r\n      list: [{\r\n        Value: '',\r\n        NewValue: '',\r\n        Field_Type: '',\r\n        IsCoreField: '',\r\n        precision: '',\r\n        Code: ''\r\n      }],\r\n      options: [],\r\n      partTypeOption: [],\r\n      isSaveDisabled: false,\r\n      processOption: [],\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        clearable: true\r\n      },\r\n      ObjectTypeList: {\r\n        // 构件类型\r\n        'check-strictly': true,\r\n        'default-expand-all': true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    init(row, defaultRow, isEdit, tbData, allCodes) {\r\n      const tbCode = tbData.map(item => item.CPCode)\r\n      this._tbData = JSON.parse(JSON.stringify(tbData))\r\n      generateAllCodes(allCodes)\r\n      this.row = row\r\n      this.tbCode = tbCode\r\n      this.defaultRow = defaultRow\r\n      console.log('isEdit', isEdit)\r\n      console.log('row1', row)\r\n      const _columns = filterByCodeType(row.CodeType)\r\n      this.options = _columns.map(v => ({\r\n        Code: v.Code,\r\n        Name: v.Display_Name,\r\n        disabled: false,\r\n        Field_Type: v.Field_Type,\r\n        precision: v.precision,\r\n        IsCoreField: v.IsCoreField\r\n      }))\r\n      console.log('this.options', JSON.parse(JSON.stringify(this.options)))\r\n\r\n      const changeCode = deepClone(this.$store.state.contactList.changeCode)\r\n      if (changeCode[this.row.uuid]) {\r\n        this.list = changeCode[this.row.uuid]\r\n        const selectItem = this.list.filter(v => v.Field_Type === 'select')\r\n        if (selectItem.length) {\r\n          if (selectItem.some(v => v.Code === 'SteelType')) {\r\n            this.getObjectTypeList()\r\n          }\r\n          if (selectItem.some(v => v.Code === 'PartType')) {\r\n            this.getPartTypeList()\r\n          }\r\n          if (selectItem.some(v => v.Code === 'Technology_Code')) {\r\n            this.getLibList()\r\n          }\r\n        }\r\n      }\r\n    },\r\n\r\n    handleClose() {\r\n      this.$emit('close')\r\n    },\r\n    handleAdd(index) {\r\n      this.list.splice(index + 1, 0, {\r\n        Value: '',\r\n        NewValue: '',\r\n        Field_Type: '',\r\n        IsCoreField: '',\r\n        precision: 0,\r\n        Code: ''\r\n      })\r\n    },\r\n    getObjectTypeList() {\r\n      if (this.row.Type === 0) {\r\n        GetCompTypeTree({ professional: 'Steel' }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.ObjectTypeList.data = res.Data\r\n            this.$nextTick((_) => {\r\n              this.$refs.treeSelectObjectType1[0].treeDataUpdateFun(res.Data)\r\n            })\r\n          } else {\r\n            this.$message({\r\n              type: 'error',\r\n              message: res.Message\r\n            })\r\n          }\r\n        })\r\n      } else {\r\n        GetPartTypeList({ Part_Grade: 0 }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.partTypeOption = res.Data\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n    },\r\n    selectChange(code, item) {\r\n      console.log('code', code, item)\r\n      const cur = this.options.find(v => v.Code === code)\r\n      console.log(cur, 'cur')\r\n      console.log(this.defaultRow, 'this.defaultRow')\r\n      item.Field_Type = cur.Field_Type\r\n      item.IsCoreField = cur.IsCoreField\r\n      item.precision = cur.precision\r\n      item.Name = cur.Name\r\n      item.Value = this.defaultRow[code]\r\n      item.NewValue = undefined\r\n      if (code === 'SteelType' || code === 'PartType') {\r\n        this.getObjectTypeList()\r\n      }\r\n      if (code === 'Technology_Code') {\r\n        this.getLibList()\r\n      }\r\n    },\r\n    handleDelete(element, index) {\r\n      const idx = this.list.findIndex(v => v.Code === element.Code)\r\n      console.log(idx, 'omd')\r\n      if (idx !== -1) {\r\n        this.list.splice(idx, 1)\r\n      }\r\n    },\r\n    getAvailableOptions(currentIndex) {\r\n      const selectedCodes = this.list\r\n        .filter((item, idx) => idx !== currentIndex)\r\n        .map(item => item.Code)\r\n      return this.options.filter(option => !selectedCodes.includes(option.Code))\r\n    },\r\n    async handleSave() {\r\n      let success = true\r\n      const list = this.list.filter(item => !!item.Code)\r\n      console.log(list, 'list')\r\n\r\n      const isMustInputs = allCodes.filter(item => item.isMustInput).map(item => item.Code)\r\n      console.log(isMustInputs, 'isMustInputs')\r\n      list.forEach(item => {\r\n        if (isMustInputs.includes(item.Code)) {\r\n          if (!item.NewValue) {\r\n            this.$message({\r\n              message: '请输入' + item.Name,\r\n              type: 'error'\r\n            })\r\n            success = false\r\n            return\r\n          }\r\n        }\r\n      })\r\n\r\n      const isValid = await this.checkName()\r\n      console.log('isValid', isValid)\r\n      if (!isValid) {\r\n        success = false\r\n        return\r\n      }\r\n\r\n      // 根据Type判断唯一性字段：0-构件(SteelName), 1-部件(ComponentName), 2/3-零件(PartName)\r\n      // let hasRepeat = false\r\n      // let nameItem = null\r\n      // let nameField = ''\r\n      // if (this.row.Type === 0) {\r\n      //   nameField = 'SteelName'\r\n      // } else if (this.row.Type === 1) {\r\n      //   nameField = 'ComponentName'\r\n      // } else if (this.row.Type === 2 || this.row.Type === 3) {\r\n      //   nameField = 'PartName'\r\n      // }\r\n      // nameItem = list.find(v => v.Code === nameField)\r\n      // if (nameItem) {\r\n      //   const newName = nameItem.NewValue?.trim()\r\n      //   for (let i = 0; i < this._tbData.length; i++) {\r\n      //     const item = this._tbData[i]\r\n      //     if (item.CodeType === 3 || item.CodeType === 2) {\r\n      //       if (item.Part_Aggregate_Id === this.row.Part_Aggregate_Id) {\r\n      //         continue\r\n      //       }\r\n      //     } else if (item.CodeType === 1 && item.uuid === this.row.uuid) {\r\n      //       continue\r\n      //     }\r\n      //     // 只比较同类型的唯一性字段\r\n      //     if (item[nameField]?.trim() === newName && this.row.Type === item.Type) {\r\n      //       hasRepeat = true\r\n      //       break\r\n      //     }\r\n      //   }\r\n      // }\r\n\r\n      if (!success) return\r\n\r\n      // this.$store.dispatch('contactList/addChangeCode', { uuid: this.row.uuid, list: list })\r\n      this.$emit('editInfo', { row: this.row, list: list })\r\n      this.$emit('close')\r\n    },\r\n    async checkName() {\r\n      const item = this.list.find(v => v.Code === 'SteelName' || v.Code === 'ComponentName' || v.Code === 'PartName')\r\n      if (!item) return true\r\n      let flag = true\r\n      await CheckCanMocName({\r\n        Type: this.row.Type,\r\n        Id: this.row.Type === 0 ? this.row.MocIdBefore : this.row.MocAggregateIdBefore,\r\n        NewName: item.NewValue\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          flag = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          flag = false\r\n        }\r\n      })\r\n      if (!flag) {\r\n        return\r\n      }\r\n      const key = this.row.Type === 0 ? 'SteelName' : this.row.Type === 1 ? 'ComponentName' : 'PartName'\r\n      const hasSimilar = this.findSimilarTypeItems(this.row, key, item.NewValue)\r\n      console.log('hasSimilar', hasSimilar, item.NewValue)\r\n      if (hasSimilar) {\r\n        this.$message({\r\n          message: `${this.row.Type === 0 ? '构件' : this.row.Type === 1 ? '部件' : '零件'}名称已存在，请修改`,\r\n          type: 'error'\r\n        })\r\n        flag = false\r\n      }\r\n      return flag\r\n    },\r\n    findSimilarTypeItems(targetItem, key, newName) {\r\n      let flag = false\r\n      for (let i = 0; i < this._tbData.length; i++) {\r\n        const item = this._tbData[i]\r\n        if (item.uuid === targetItem.uuid) continue\r\n        if (item.CodeType !== targetItem.CodeType) continue\r\n        if (item.Type === 1 || item.Type === 2 || item.Type === 3) {\r\n          if ((targetItem.MocAggregateIdBefore !== item.MocAggregateIdBefore) && (item[key] === newName)) {\r\n            flag = true\r\n          }\r\n        } else if (item.Type === 0) {\r\n          if (item[key] === newName) {\r\n            flag = true\r\n          }\r\n        }\r\n      }\r\n      return flag\r\n    },\r\n    handleCancel() {\r\n      this.$emit('close')\r\n    },\r\n    getLibList() {\r\n      GetLibList({\r\n        type: this.row.Type === 0 ? 1 : this.row.Type === 1 ? 3 : 2\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.processOption = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.handle-edit-container {\r\n  position: relative;\r\n  .handle-edit {\r\n    max-height: 50vh;\r\n    overflow-y: auto;\r\n    .flex-row {\r\n      display: flex;\r\n      align-items: center;\r\n    justify-content: space-between;\r\n    .flex-item {\r\n      flex: 2;\r\n      margin: 10px 0;\r\n    }\r\n    .flex-item-1 {\r\n      flex: 3;\r\n    }\r\n    .flxe-item2 {\r\n      margin:0 10px;\r\n    }\r\n    .flex-item3 {\r\n      flex: 1;\r\n    }\r\n    .btn-x {\r\n      margin-left: 10px;\r\n\r\n    }\r\n  }\r\n\r\n}\r\n.btn-group {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    padding-top: 10px;\r\n    background: #fff;\r\n  }\r\n}\r\n.cs-tree-x {\r\n  ::v-deep {\r\n    .el-select {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6FA,SAAAA,QAAA,EAAAC,aAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,iBAAA;AACA,SAAAC,eAAA;AACA,SAAAC,eAAA;AACA,SAAAC,UAAA;AACA,SAAAC,SAAA;AACA,SAAAC,eAAA;AAEA;EAEAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;QACAC,KAAA;QACAC,QAAA;QACAC,UAAA;QACAC,WAAA;QACAC,SAAA;QACAC,IAAA;MACA;MACAC,OAAA;MACAC,cAAA;MACAC,cAAA;MACAC,aAAA;MACAC,gBAAA;QACAC,WAAA;QACAC,SAAA;MACA;MACAC,cAAA;QACA;QACA;QACA;QACAC,WAAA;QACAhB,IAAA;QACAiB,KAAA;UACAC,QAAA;UACAC,KAAA;UACAC,KAAA;QACA;MACA;IACA;EACA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAAC,GAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,MAAA,EAAApC,QAAA;MACA,IAAAqC,MAAA,GAAAD,MAAA,CAAAE,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,MAAA;MAAA;MACA,KAAAC,OAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAR,MAAA;MACAjC,gBAAA,CAAAH,QAAA;MACA,KAAAiC,GAAA,GAAAA,GAAA;MACA,KAAAI,MAAA,GAAAA,MAAA;MACA,KAAAH,UAAA,GAAAA,UAAA;MACAW,OAAA,CAAAC,GAAA,WAAAX,MAAA;MACAU,OAAA,CAAAC,GAAA,SAAAb,GAAA;MACA,IAAAc,QAAA,GAAA7C,gBAAA,CAAA+B,GAAA,CAAAe,QAAA;MACA,KAAA9B,OAAA,GAAA6B,QAAA,CAAAT,GAAA,WAAAW,CAAA;QAAA;UACAhC,IAAA,EAAAgC,CAAA,CAAAhC,IAAA;UACAiC,IAAA,EAAAD,CAAA,CAAAE,YAAA;UACAC,QAAA;UACAtC,UAAA,EAAAmC,CAAA,CAAAnC,UAAA;UACAE,SAAA,EAAAiC,CAAA,CAAAjC,SAAA;UACAD,WAAA,EAAAkC,CAAA,CAAAlC;QACA;MAAA;MACA8B,OAAA,CAAAC,GAAA,iBAAAJ,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAA1B,OAAA;MAEA,IAAAmC,UAAA,GAAA7C,SAAA,MAAA8C,MAAA,CAAAC,KAAA,CAAAC,WAAA,CAAAH,UAAA;MACA,IAAAA,UAAA,MAAApB,GAAA,CAAAwB,IAAA;QACA,KAAA9C,IAAA,GAAA0C,UAAA,MAAApB,GAAA,CAAAwB,IAAA;QACA,IAAAC,UAAA,QAAA/C,IAAA,CAAAgD,MAAA,WAAAV,CAAA;UAAA,OAAAA,CAAA,CAAAnC,UAAA;QAAA;QACA,IAAA4C,UAAA,CAAAE,MAAA;UACA,IAAAF,UAAA,CAAAG,IAAA,WAAAZ,CAAA;YAAA,OAAAA,CAAA,CAAAhC,IAAA;UAAA;YACA,KAAA6C,iBAAA;UACA;UACA,IAAAJ,UAAA,CAAAG,IAAA,WAAAZ,CAAA;YAAA,OAAAA,CAAA,CAAAhC,IAAA;UAAA;YACA,KAAA8C,eAAA;UACA;UACA,IAAAL,UAAA,CAAAG,IAAA,WAAAZ,CAAA;YAAA,OAAAA,CAAA,CAAAhC,IAAA;UAAA;YACA,KAAA+C,UAAA;UACA;QACA;MACA;IACA;IAEAC,WAAA,WAAAA,YAAA;MACA,KAAAC,KAAA;IACA;IACAC,SAAA,WAAAA,UAAAC,KAAA;MACA,KAAAzD,IAAA,CAAA0D,MAAA,CAAAD,KAAA;QACAxD,KAAA;QACAC,QAAA;QACAC,UAAA;QACAC,WAAA;QACAC,SAAA;QACAC,IAAA;MACA;IACA;IACA6C,iBAAA,WAAAA,kBAAA;MAAA,IAAAQ,KAAA;MACA,SAAArC,GAAA,CAAAsC,IAAA;QACAlE,eAAA;UAAAmE,YAAA;QAAA,GAAAC,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAL,KAAA,CAAA7C,cAAA,CAAAf,IAAA,GAAAgE,GAAA,CAAAE,IAAA;YACAN,KAAA,CAAAO,SAAA,WAAAC,CAAA;cACAR,KAAA,CAAAS,KAAA,CAAAC,qBAAA,IAAAC,iBAAA,CAAAP,GAAA,CAAAE,IAAA;YACA;UACA;YACAN,KAAA,CAAAY,QAAA;cACAC,IAAA;cACAC,OAAA,EAAAV,GAAA,CAAAW;YACA;UACA;QACA;MACA;QACA/E,eAAA;UAAAgF,UAAA;QAAA,GAAAb,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAL,KAAA,CAAAnD,cAAA,GAAAuD,GAAA,CAAAE,IAAA;UACA;YACAN,KAAA,CAAAY,QAAA;cACAE,OAAA,EAAAV,GAAA,CAAAW,OAAA;cACAF,IAAA;YACA;UACA;QACA;MACA;IACA;IACAI,YAAA,WAAAA,aAAAC,IAAA,EAAAjD,IAAA;MACAM,OAAA,CAAAC,GAAA,SAAA0C,IAAA,EAAAjD,IAAA;MACA,IAAAkD,GAAA,QAAAvE,OAAA,CAAAwE,IAAA,WAAAzC,CAAA;QAAA,OAAAA,CAAA,CAAAhC,IAAA,KAAAuE,IAAA;MAAA;MACA3C,OAAA,CAAAC,GAAA,CAAA2C,GAAA;MACA5C,OAAA,CAAAC,GAAA,MAAAZ,UAAA;MACAK,IAAA,CAAAzB,UAAA,GAAA2E,GAAA,CAAA3E,UAAA;MACAyB,IAAA,CAAAxB,WAAA,GAAA0E,GAAA,CAAA1E,WAAA;MACAwB,IAAA,CAAAvB,SAAA,GAAAyE,GAAA,CAAAzE,SAAA;MACAuB,IAAA,CAAAW,IAAA,GAAAuC,GAAA,CAAAvC,IAAA;MACAX,IAAA,CAAA3B,KAAA,QAAAsB,UAAA,CAAAsD,IAAA;MACAjD,IAAA,CAAA1B,QAAA,GAAA8E,SAAA;MACA,IAAAH,IAAA,oBAAAA,IAAA;QACA,KAAA1B,iBAAA;MACA;MACA,IAAA0B,IAAA;QACA,KAAAxB,UAAA;MACA;IACA;IACA4B,YAAA,WAAAA,aAAAC,OAAA,EAAAzB,KAAA;MACA,IAAA0B,GAAA,QAAAnF,IAAA,CAAAoF,SAAA,WAAA9C,CAAA;QAAA,OAAAA,CAAA,CAAAhC,IAAA,KAAA4E,OAAA,CAAA5E,IAAA;MAAA;MACA4B,OAAA,CAAAC,GAAA,CAAAgD,GAAA;MACA,IAAAA,GAAA;QACA,KAAAnF,IAAA,CAAA0D,MAAA,CAAAyB,GAAA;MACA;IACA;IACAE,mBAAA,WAAAA,oBAAAC,YAAA;MACA,IAAAC,aAAA,QAAAvF,IAAA,CACAgD,MAAA,WAAApB,IAAA,EAAAuD,GAAA;QAAA,OAAAA,GAAA,KAAAG,YAAA;MAAA,GACA3D,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAtB,IAAA;MAAA;MACA,YAAAC,OAAA,CAAAyC,MAAA,WAAAwC,MAAA;QAAA,QAAAD,aAAA,CAAAE,QAAA,CAAAD,MAAA,CAAAlF,IAAA;MAAA;IACA;IACAoF,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,OAAA,EAAAhG,IAAA,EAAAiG,YAAA,EAAAC,OAAA;QAAA,OAAAL,mBAAA,GAAAM,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAP,OAAA;cACAhG,IAAA,GAAA2F,MAAA,CAAA3F,IAAA,CAAAgD,MAAA,WAAApB,IAAA;gBAAA,SAAAA,IAAA,CAAAtB,IAAA;cAAA;cACA4B,OAAA,CAAAC,GAAA,CAAAnC,IAAA;cAEAiG,YAAA,GAAA5G,QAAA,CAAA2D,MAAA,WAAApB,IAAA;gBAAA,OAAAA,IAAA,CAAA4E,WAAA;cAAA,GAAA7E,GAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAAtB,IAAA;cAAA;cACA4B,OAAA,CAAAC,GAAA,CAAA8D,YAAA;cACAjG,IAAA,CAAAyG,OAAA,WAAA7E,IAAA;gBACA,IAAAqE,YAAA,CAAAR,QAAA,CAAA7D,IAAA,CAAAtB,IAAA;kBACA,KAAAsB,IAAA,CAAA1B,QAAA;oBACAyF,MAAA,CAAApB,QAAA;sBACAE,OAAA,UAAA7C,IAAA,CAAAW,IAAA;sBACAiC,IAAA;oBACA;oBACAwB,OAAA;oBACA;kBACA;gBACA;cACA;cAAAK,QAAA,CAAAE,IAAA;cAAA,OAEAZ,MAAA,CAAAe,SAAA;YAAA;cAAAR,OAAA,GAAAG,QAAA,CAAAM,IAAA;cACAzE,OAAA,CAAAC,GAAA,YAAA+D,OAAA;cAAA,IACAA,OAAA;gBAAAG,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAP,OAAA;cAAA,OAAAK,QAAA,CAAAO,MAAA;YAAA;cAAA,IAmCAZ,OAAA;gBAAAK,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAA,OAAAF,QAAA,CAAAO,MAAA;YAAA;cAEA;cACAjB,MAAA,CAAApC,KAAA;gBAAAjC,GAAA,EAAAqE,MAAA,CAAArE,GAAA;gBAAAtB,IAAA,EAAAA;cAAA;cACA2F,MAAA,CAAApC,KAAA;YAAA;YAAA;cAAA,OAAA8C,QAAA,CAAAQ,IAAA;UAAA;QAAA,GAAAd,OAAA;MAAA;IACA;IACAW,SAAA,WAAAA,UAAA;MAAA,IAAAI,MAAA;MAAA,OAAAlB,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAiB,SAAA;QAAA,IAAAnF,IAAA,EAAAoF,IAAA,EAAAC,GAAA,EAAAC,UAAA;QAAA,OAAArB,mBAAA,GAAAM,IAAA,UAAAgB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAd,IAAA,GAAAc,SAAA,CAAAb,IAAA;YAAA;cACA3E,IAAA,GAAAkF,MAAA,CAAA9G,IAAA,CAAA+E,IAAA,WAAAzC,CAAA;gBAAA,OAAAA,CAAA,CAAAhC,IAAA,oBAAAgC,CAAA,CAAAhC,IAAA,wBAAAgC,CAAA,CAAAhC,IAAA;cAAA;cAAA,IACAsB,IAAA;gBAAAwF,SAAA,CAAAb,IAAA;gBAAA;cAAA;cAAA,OAAAa,SAAA,CAAAR,MAAA;YAAA;cACAI,IAAA;cAAAI,SAAA,CAAAb,IAAA;cAAA,OACAzG,eAAA;gBACA8D,IAAA,EAAAkD,MAAA,CAAAxF,GAAA,CAAAsC,IAAA;gBACAyD,EAAA,EAAAP,MAAA,CAAAxF,GAAA,CAAAsC,IAAA,SAAAkD,MAAA,CAAAxF,GAAA,CAAAgG,WAAA,GAAAR,MAAA,CAAAxF,GAAA,CAAAiG,oBAAA;gBACAC,OAAA,EAAA5F,IAAA,CAAA1B;cACA,GAAA4D,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA,CAAAC,SAAA;kBACAgD,IAAA,GAAAjD,GAAA,CAAAE,IAAA;gBACA;kBACA6C,MAAA,CAAAvC,QAAA;oBACAE,OAAA,EAAAV,GAAA,CAAAW,OAAA;oBACAF,IAAA;kBACA;kBACAwC,IAAA;gBACA;cACA;YAAA;cAAA,IACAA,IAAA;gBAAAI,SAAA,CAAAb,IAAA;gBAAA;cAAA;cAAA,OAAAa,SAAA,CAAAR,MAAA;YAAA;cAGAK,GAAA,GAAAH,MAAA,CAAAxF,GAAA,CAAAsC,IAAA,uBAAAkD,MAAA,CAAAxF,GAAA,CAAAsC,IAAA;cACAsD,UAAA,GAAAJ,MAAA,CAAAW,oBAAA,CAAAX,MAAA,CAAAxF,GAAA,EAAA2F,GAAA,EAAArF,IAAA,CAAA1B,QAAA;cACAgC,OAAA,CAAAC,GAAA,eAAA+E,UAAA,EAAAtF,IAAA,CAAA1B,QAAA;cACA,IAAAgH,UAAA;gBACAJ,MAAA,CAAAvC,QAAA;kBACAE,OAAA,KAAAiD,MAAA,CAAAZ,MAAA,CAAAxF,GAAA,CAAAsC,IAAA,gBAAAkD,MAAA,CAAAxF,GAAA,CAAAsC,IAAA;kBACAY,IAAA;gBACA;gBACAwC,IAAA;cACA;cAAA,OAAAI,SAAA,CAAAR,MAAA,WACAI,IAAA;YAAA;YAAA;cAAA,OAAAI,SAAA,CAAAP,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IACA;IACAU,oBAAA,WAAAA,qBAAAE,UAAA,EAAAV,GAAA,EAAAW,OAAA;MACA,IAAAZ,IAAA;MACA,SAAAa,CAAA,MAAAA,CAAA,QAAA/F,OAAA,CAAAmB,MAAA,EAAA4E,CAAA;QACA,IAAAjG,IAAA,QAAAE,OAAA,CAAA+F,CAAA;QACA,IAAAjG,IAAA,CAAAkB,IAAA,KAAA6E,UAAA,CAAA7E,IAAA;QACA,IAAAlB,IAAA,CAAAS,QAAA,KAAAsF,UAAA,CAAAtF,QAAA;QACA,IAAAT,IAAA,CAAAgC,IAAA,UAAAhC,IAAA,CAAAgC,IAAA,UAAAhC,IAAA,CAAAgC,IAAA;UACA,IAAA+D,UAAA,CAAAJ,oBAAA,KAAA3F,IAAA,CAAA2F,oBAAA,IAAA3F,IAAA,CAAAqF,GAAA,MAAAW,OAAA;YACAZ,IAAA;UACA;QACA,WAAApF,IAAA,CAAAgC,IAAA;UACA,IAAAhC,IAAA,CAAAqF,GAAA,MAAAW,OAAA;YACAZ,IAAA;UACA;QACA;MACA;MACA,OAAAA,IAAA;IACA;IACAc,YAAA,WAAAA,aAAA;MACA,KAAAvE,KAAA;IACA;IACAF,UAAA,WAAAA,WAAA;MAAA,IAAA0E,MAAA;MACAnI,UAAA;QACA4E,IAAA,OAAAlD,GAAA,CAAAsC,IAAA,kBAAAtC,GAAA,CAAAsC,IAAA;MACA,GAAAE,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA+D,MAAA,CAAArH,aAAA,GAAAqD,GAAA,CAAAE,IAAA;QACA;UACA8D,MAAA,CAAAxD,QAAA;YACAE,OAAA,EAAAV,GAAA,CAAAW,OAAA;YACAF,IAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
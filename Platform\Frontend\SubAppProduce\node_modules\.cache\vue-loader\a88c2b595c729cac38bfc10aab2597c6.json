{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\detail.vue", "mtime": 1758683411838}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBnZXRUYkluZm8gZnJvbSAnQC9taXhpbnMvUFJPL2dldC10YWJsZS1pbmZvJwppbXBvcnQgeyBHZXRUZWFtVGFza0RldGFpbHMsIEV4cG9ydFRhc2tDb2RlRGV0YWlscywgR2V0U3VnZ2VzdERldmljZUFuZFJlbWFyayB9IGZyb20gJ0AvYXBpL1BSTy9wcm9kdWN0aW9uLXRhc2snCmltcG9ydCB7IGRlYm91bmNlLCBjbG9zZVRhZ1ZpZXcgfSBmcm9tICdAL3V0aWxzJwppbXBvcnQgUXJjb2RlVnVlIGZyb20gJ3FyY29kZS52dWUnCmltcG9ydCB7CiAgR2V0UHJvY2Vzc0xpc3QsCiAgR2V0V29ya2luZ1RlYW1zUGFnZUxpc3QKfSBmcm9tICdAL2FwaS9QUk8vdGVjaG5vbG9neS1saWInCi8vIGltcG9ydCB7IEdldFByb2Nlc3NMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL3RlY2hub2xvZ3ktbGliJwppbXBvcnQgVHJhbnNmZXJEZXRhaWwgZnJvbSAnLi90cmFuc2ZlckRldGFpbCcKLy8gaW1wb3J0IHsgRXhwb3J0IH0gZnJvbSAndnhlLXRhYmxlJwppbXBvcnQgeyBjb21iaW5lVVJMIH0gZnJvbSAnQC91dGlscycKaW1wb3J0IHsgbWFwR2V0dGVycyB9IGZyb20gJ3Z1ZXgnCmltcG9ydCBTdWdnZXN0RGV2aWNlIGZyb20gJy4vc3VnZ2VzdERldmljZScKaW1wb3J0IHsgZ2V0Qm9tQ29kZSwgZ2V0Qm9tTmFtZSwgY2hlY2tJc1VuaXRQYXJ0IH0gZnJvbSAnQC92aWV3cy9QUk8vYm9tLXNldHRpbmcvdXRpbHMnCmNvbnN0IHByaW50U3R5bGUgPSBgCiAgICAgICAgLnRpdGxlIHsKICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgICAgICB9CiAgICAgICAgLmlzLS1wcmludHsKICAgICAgICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7CiAgICAgICAgICB3aWR0aDo5NSUgIWltcG9ydGFudDsKICAgICAgICAgIG1hcmdpbjowIGF1dG8gIWltcG9ydGFudDsKICAgICAgICB9CiAgICAgICAgLm15LWxpc3Qtcm93IHsKICAgICAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICAgICAgICAgIHdpZHRoOiAxMDAlOwogICAgICAgICAgbWFyZ2luLWxlZnQ6MyU7CiAgICAgICAgfQogICAgICAgIC5teS1saXN0LXJvdy1maXJzdCB7CiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxMHB4OwogICAgICAgIH0KICAgICAgICAubXktbGlzdC1jb2wgewogICAgICAgICAgd2lkdGg6MzAlOwogICAgICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogICAgICAgICAgZmxvYXQ6IGxlZnQ7CiAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDElOwogICAgICAgICAgd29yZC13cmFwOmJyZWFrLXdvcmQ7CiAgICAgICAgICB3b3JkLWJyZWFrOm5vcm1hbDsKICAgICAgICB9CiAgICAgICAgLmxlZnR7CiAgICAgICAgICBmbGV4OjE7CiAgICAgICAgfQogICAgICAgIC5teS10b3AgewogICAgICAgICAgZGlzcGxheTpmbGV4OwogICAgICAgICAgZm9udC1zaXplOiAxMnB4OwogICAgICAgICAgbWFyZ2luLWJvdHRvbTogNXB4OwogICAgICAgIH0KICAgICAgICAucXJjb2RlewogICAgICAgICAgbWFyZ2luLXJpZ2h0OjEwcHgKICAgICAgICB9CiAgICAgICAgLmNzLWltZ3sKICAgICAgICAgIHBvc2l0aW9uOnJlbGF0aXZlOwogICAgICAgICAgcmlnaHQ6MzBweAogICAgICAgIH0KICAgICAgICBgCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1BST1Rhc2tMaXN0RGV0YWlsJywKICBjb21wb25lbnRzOiB7CiAgICBUcmFuc2ZlckRldGFpbCwKICAgIFFyY29kZVZ1ZSwKICAgIFN1Z2dlc3REZXZpY2UKICB9LAogIG1peGluczogW2dldFRiSW5mb10sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGJvbU5hbWU6ICcnLAogICAgICBkZXZpY2VEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIHF1ZXJ5Rm9ybTogewogICAgICAgIC8vIFByb2plY3RfSWQ6ICcnLAogICAgICAgIC8vIEFyZWFfSWQ6ICcnLAogICAgICAgIC8vIEluc3RhbGxVbml0X0lkOiAnJywKICAgICAgICBOZXh0X1Byb2Nlc3NfSWQ6ICcnLAogICAgICAgIE5leHRfVGVhbV9JZDogJycKICAgICAgfSwKICAgICAgcXVlcnlJbmZvOiB7CiAgICAgICAgUGFnZTogMSwKICAgICAgICBQYWdlU2l6ZTogLTEKICAgICAgfSwKICAgICAgdGJDb25maWc6IHsKICAgICAgICBPcF9XaWR0aDogMTIwCiAgICAgIH0sCiAgICAgIHRiTG9hZGluZzogZmFsc2UsCiAgICAgIGNvbHVtbnM6IFtdLAogICAgICBtdWx0aXBsZVNlbGVjdGlvbjogW10sCiAgICAgIHRiRGF0YTogW10sCiAgICAgIGZpbmlzaExpc3Q6IFtdLAogICAgICBwcm9jZXNzT3B0aW9uOiBbXSwKICAgICAgZ3JvdXBPcHRpb246IFtdLAogICAgICB0b3RhbDogMCwKICAgICAgcHJpbnRDb2x1bW5zOiBbXSwKICAgICAgc2VhcmNoOiAoKSA9PiAoe30pLAogICAgICBwYWdlVHlwZTogJycsCiAgICAgIGluZm86IHsKICAgICAgICBUYXNrX0NvZGU6ICcnLAogICAgICAgIFByb2plY3RfTmFtZTogJycsCiAgICAgICAgQXJlYV9OYW1lOiAnJywKICAgICAgICBJbnN0YWxsVW5pdF9OYW1lOiAnJywKICAgICAgICBTY2hkdWxpbmdfQ29kZTogJycsCiAgICAgICAgVGFza19GaW5pc2hfRGF0ZTogJycsCiAgICAgICAgRmluaXNoX0RhdGUyOiAnJywKICAgICAgICBPcmRlcl9EYXRlOiAnJywKICAgICAgICBXb3JraW5nX1RlYW1fTmFtZTogJycsCiAgICAgICAgV29ya2luZ19Qcm9jZXNzX05hbWU6ICcnLAogICAgICAgIFByb2Nlc3NfU3RhcnRfRGF0ZTogJycsCiAgICAgICAgUHJvY2Vzc19GaW5pc2hfRGF0ZTogJycKICAgICAgfSwKICAgICAgcHJpbnRDb25maWc6IHsKICAgICAgICBzaGVldE5hbWU6ICfku7vliqHljZXor6bmg4UnLAogICAgICAgIHN0eWxlOiBwcmludFN0eWxlLAogICAgICAgIGJlZm9yZVByaW50TWV0aG9kOiAoeyBjb250ZW50IH0pID0+IHsKICAgICAgICAgIHJldHVybiB0aGlzLnRvcEh0bWwgKyBjb250ZW50CiAgICAgICAgfQogICAgICB9LAogICAgICBUZW5hbnRfQ29kZTogbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3RlbmFudCcpLAogICAgICBSZW1hcms6ICcnLAogICAgICBlcXB0SW5mb0xpc3Q6IFtdLAogICAgICBlcXB0SW5mb0xpc3RTdHI6ICcnCiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgLi4ubWFwR2V0dGVycygndGVuYW50JywgWydpc1ZlcnNpb25Gb3VyJ10pLAogICAgaXNDb20oKSB7CiAgICAgIHJldHVybiB0aGlzLnBhZ2VUeXBlID09PSBnZXRCb21Db2RlKCctMScpCiAgICB9LAogICAgaXNVbml0UGFydCgpIHsKICAgICAgcmV0dXJuIGNoZWNrSXNVbml0UGFydCh0aGlzLnBhZ2VUeXBlKQogICAgfSwKICAgIGlzUGFydCgpIHsKICAgICAgcmV0dXJuIHRoaXMucGFnZVR5cGUgPT09IGdldEJvbUNvZGUoJzAnKQogICAgfQogIH0sCiAgYXN5bmMgbW91bnRlZCgpIHsKICAgIHRoaXMucGFnZVR5cGUgPSB0aGlzLiRyb3V0ZS5xdWVyeS50eXBlCiAgICB0aGlzLmJvbU5hbWUgPSBhd2FpdCBnZXRCb21OYW1lKHRoaXMucGFnZVR5cGUpCiAgICB0aGlzLndvcmtUZWFtSWQgPSB0aGlzLiRyb3V0ZS5xdWVyeS50aWQKICAgIHRoaXMudGFza0NvZGUgPSB0aGlzLiRyb3V0ZS5xdWVyeS5pZAogICAgdGhpcy5pbmZvID0gSlNPTi5wYXJzZShkZWNvZGVVUklDb21wb25lbnQodGhpcy4kcm91dGUucXVlcnkub3RoZXIpKQogICAgYXdhaXQgdGhpcy5nZXRTdWdnZXN0RGV2aWNlQW5kUmVtYXJrKCkKICAgIGF3YWl0IHRoaXMuZ2V0VGFibGVDb25maWcodGhpcy5pc0NvbSA/ICdQUk9Db21UYXNrTGlzdERldGFpbCcgOiB0aGlzLmlzVW5pdFBhcnQgPyAnUFJPVW5pdFBhcnRUYXNrTGlzdERldGFpbCcgOiAnUFJPUGFydFRhc2tMaXN0RGV0YWlsJykKICAgIGlmICh0aGlzLmlzQ29tKSB7CiAgICAgIGNvbnN0IGlkeCA9IHRoaXMuY29sdW1ucy5maW5kSW5kZXgoKGl0ZW0pID0+IGl0ZW0uQ29kZSA9PT0gJ1BhcnRfQ29kZScpCiAgICAgIGlkeCAhPT0gLTEgJiYgdGhpcy5jb2x1bW5zLnNwbGljZShpZHgsIDEpCgogICAgICB0aGlzLnByaW50Q29sdW1ucyA9IHRoaXMuY29sdW1ucy5maWx0ZXIoY29sdW1uID0+IGNvbHVtbi5Db2RlICE9PSAnQ29tcF9EZXNjcmlwdGlvbicpCiAgICB9CiAgICBpZiAodGhpcy5pc1VuaXRQYXJ0KSB7CiAgICAgIHRoaXMucHJpbnRDb2x1bW5zID0gdGhpcy5jb2x1bW5zLmZpbHRlcihjb2x1bW4gPT4gY29sdW1uLkNvZGUgIT09ICdQcm9qZWN0X05hbWUnICYmIGNvbHVtbi5Db2RlICE9PSAnQXJlYV9OYW1lJyAmJiBjb2x1bW4uQ29kZSAhPT0gJ0ZpbmlzaF9Db3VudCcgJiYgY29sdW1uLkNvZGUgIT09ICdGaW5pc2hfV2VpZ2h0JyAmJiBjb2x1bW4uQ29kZSAhPT0gJ0NvbXBfRGVzY3JpcHRpb24nKQogICAgfQogICAgaWYgKHRoaXMuaXNQYXJ0KSB7CiAgICAgIHRoaXMucHJpbnRDb2x1bW5zID0gdGhpcy5jb2x1bW5zLmZpbHRlcihjb2x1bW4gPT4gY29sdW1uLkNvZGUgIT09ICdQcm9qZWN0X05hbWUnICYmIGNvbHVtbi5Db2RlICE9PSAnQXJlYV9OYW1lJyAmJiBjb2x1bW4uQ29kZSAhPT0gJ0ZpbmlzaF9Db3VudCcgJiYgY29sdW1uLkNvZGUgIT09ICdGaW5pc2hfV2VpZ2h0JyAmJiBjb2x1bW4uQ29kZSAhPT0gJ0NvbXBfRGVzY3JpcHRpb24nKQogICAgfQoKICAgIC8vIGVsc2UgewogICAgLy8gICBjb25zdCBpZHggPSB0aGlzLmNvbHVtbnMuZmluZEluZGV4KChpdGVtKSA9PiBpdGVtLkNvZGUgPT09ICdDb21wX0NvZGUnKQogICAgLy8gICBpZHggIT09IC0xICYmIHRoaXMuY29sdW1ucy5zcGxpY2UoaWR4LCAxKQogICAgLy8gfQogICAgdGhpcy5zZWFyY2ggPSBkZWJvdW5jZSh0aGlzLmZldGNoRGF0YSwgODAwLCB0cnVlKQogICAgdGhpcy5mZXRjaERhdGEoKQogICAgdGhpcy5nZXRQcm9jZXNzT3B0aW9uKCkKICAgIHRoaXMuZ2V0QWxsVGVhbU9wdGlvbigpCiAgICB0aGlzLmdldEh0bWwoKQogIH0sCiAgbWV0aG9kczogewogICAgZ2V0U3VnZ2VzdERldmljZUFuZFJlbWFyaygpIHsKICAgICAgR2V0U3VnZ2VzdERldmljZUFuZFJlbWFyayh7CiAgICAgICAgQm9tX0xldmVsOiB0aGlzLnBhZ2VUeXBlLAogICAgICAgIFRhc2tfQ29kZTogdGhpcy5pbmZvLlRhc2tfQ29kZQogICAgICB9KS50aGVuKChyZXMpID0+IHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgdGhpcy5SZW1hcmsgPSByZXMuRGF0YT8uUmVtYXJrCiAgICAgICAgICB0aGlzLmVxcHRJbmZvTGlzdCA9IHJlcy5EYXRhPy5lcXB0SW5mb0xpc3QgfHwgW10KICAgICAgICAgIHRoaXMuZXFwdEluZm9MaXN0U3RyID0gdGhpcy5lcXB0SW5mb0xpc3QubWFwKGl0ZW0gPT4gaXRlbS5EaXNwbGF5TmFtZSkuam9pbignLCcpCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgdGJTZWxlY3RDaGFuZ2UoYXJyYXkpIHsKICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbiA9IGFycmF5LnJlY29yZHMKICAgIH0sCiAgICAvLyDlr7zlh7oKICAgIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgY29uc29sZS5sb2codGhpcy5pbmZvKQogICAgICBFeHBvcnRUYXNrQ29kZURldGFpbHMoewogICAgICAgIFByb2Nlc3NfVHlwZTogdGhpcy5pc0NvbSA/IDIgOiB0aGlzLmlzUGFydCA/IDEgOiAzLCAvLyAx6Zu25Lu277yMMuaehOS7tgogICAgICAgIFdvcmtpbmdfVGVhbV9JZDogdGhpcy53b3JrVGVhbUlkLAogICAgICAgIFRhc2tfQ29kZTogdGhpcy50YXNrQ29kZQogICAgICB9KS50aGVuKChyZXMpID0+IHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIG1lc3NhZ2U6ICflr7zlh7rmiJDlip8nLAogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycKICAgICAgICAgIH0pCiAgICAgICAgICB3aW5kb3cub3Blbihjb21iaW5lVVJMKHRoaXMuJGJhc2VVcmwsIHJlcy5EYXRhKSwgJ19ibGFuaycpCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICAgIC8vIGNvbnN0IGZpbHRlclZhbCA9IHRoaXMuY29sdW1ucy5tYXAoKHYpID0+IHYuQ29kZSkKICAgICAgLy8gY29uc3QgZGF0YSA9IGZvcm1hdEpzb24oZmlsdGVyVmFsLCB0aGlzLm11bHRpcGxlU2VsZWN0aW9uKQogICAgICAvLyBjb25zdCBoZWFkZXIgPSB0aGlzLmNvbHVtbnMubWFwKCh2KSA9PiB2LkRpc3BsYXlfTmFtZSkKICAgICAgLy8gaW1wb3J0KCdAL3ZlbmRvci9FeHBvcnQyRXhjZWwnKS50aGVuKChleGNlbCkgPT4gewogICAgICAvLyAgIGV4Y2VsLmV4cG9ydF9qc29uX3RvX2V4Y2VsKHsKICAgICAgLy8gICAgIGhlYWRlcjogaGVhZGVyLAogICAgICAvLyAgICAgZGF0YSwKICAgICAgLy8gICAgIGZpbGVuYW1lOiBgJHsKICAgICAgLy8gICAgICAgdGhpcy5pbmZvPy5UYXNrX0NvZGUgfHwKICAgICAgLy8gICAgICAgKHRoaXMuaXNDb20gPyAn5p6E5Lu25Lu75Yqh5Y2V6K+m5oOFJyA6ICfpm7bku7bku7vliqHljZXor6bmg4UnKQogICAgICAvLyAgICAgfWAsCiAgICAgIC8vICAgICBhdXRvV2lkdGg6IHRydWUsCiAgICAgIC8vICAgICBib29rVHlwZTogJ3hsc3gnCiAgICAgIC8vICAgfSkKICAgICAgLy8gfSkKICAgICAgLy8gZnVuY3Rpb24gZm9ybWF0SnNvbihmaWx0ZXJWYWwsIGpzb25EYXRhKSB7CiAgICAgIC8vICAgcmV0dXJuIGpzb25EYXRhLm1hcCgodikgPT4gZmlsdGVyVmFsLm1hcCgoaikgPT4gdltqXSkpCiAgICAgIC8vIH0KICAgIH0sCiAgICBmZXRjaERhdGEocGFnZSkgewogICAgICBwYWdlICYmICh0aGlzLnF1ZXJ5SW5mby5QYWdlID0gcGFnZSkKICAgICAgY29uc29sZS5sb2codGhpcy5xdWVyeUluZm8sICd0aGlzLnF1ZXJ5SW5mbycpCiAgICAgIHRoaXMudGJMb2FkaW5nID0gdHJ1ZQogICAgICBHZXRUZWFtVGFza0RldGFpbHMoewogICAgICAgIC8vIC4uLnRoaXMucXVlcnlJbmZvLAogICAgICAgIEJvbV9MZXZlbDogdGhpcy5wYWdlVHlwZSwKICAgICAgICBQYWdlOiAtMSwKICAgICAgICBQYWdlU2l6ZTogLTEsCiAgICAgICAgUHJvY2Vzc19UeXBlOiB0aGlzLmlzQ29tID8gMiA6IHRoaXMuaXNQYXJ0ID8gMSA6IDMsIC8vIDHpm7bku7bvvIwy5p6E5Lu2CiAgICAgICAgV29ya2luZ19UZWFtX0lkOiB0aGlzLndvcmtUZWFtSWQsCiAgICAgICAgVGFza19Db2RlOiB0aGlzLnRhc2tDb2RlLAogICAgICAgIE5leHRfVGVhbV9JZDogdGhpcy5xdWVyeUZvcm0uTmV4dF9UZWFtX0lkLAogICAgICAgIE5leHRfUHJvY2Vzc19JZDogdGhpcy5xdWVyeUZvcm0uTmV4dF9Qcm9jZXNzX0lkCiAgICAgIH0pCiAgICAgICAgLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgICAgdGhpcy50YkRhdGEgPSByZXMuRGF0YS5EYXRhLmZpbHRlcihpdGVtID0+IHsKICAgICAgICAgICAgICByZXR1cm4gaXRlbS5BbGxvY2F0aW9uX0NvdW50ICE9PSAwCiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIC8vIHRoaXMudG90YWwgPSByZXMuRGF0YS5Ub3RhbENvdW50CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICAgIH0pCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgICAuZmluYWxseSgoXykgPT4gewogICAgICAgICAgdGhpcy50YkxvYWRpbmcgPSBmYWxzZQogICAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlUmVzZXQoKSB7CiAgICAgIHRoaXMuJHJlZnNbJ2Zvcm0nXS5yZXNldEZpZWxkcygpCiAgICAgIHRoaXMuc2VhcmNoKDEpCiAgICB9LAogICAgZ2V0UHJvY2Vzc09wdGlvbigpIHsKICAgICAgR2V0UHJvY2Vzc0xpc3QoewogICAgICAgIHR5cGU6IHRoaXMuaXNDb20gPyAxIDogdGhpcy5pc1BhcnQgPyAyIDogMywgLy8gMeaehOS7tu+8jDLpm7bku7YKICAgICAgICBCb21fTGV2ZWw6IHRoaXMucGFnZVR5cGUKICAgICAgfSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIHRoaXMucHJvY2Vzc09wdGlvbiA9IHJlcy5EYXRhCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgZ2V0QWxsVGVhbU9wdGlvbigpIHsKICAgICAgR2V0V29ya2luZ1RlYW1zUGFnZUxpc3QoewogICAgICAgIHBhZ2VJbmZvOiB7IFBhZ2U6IC0xLCBQYWdlU2l6ZTogLTEsIFBhcmFtZXRlckpzb246IFtdIH0KICAgICAgfSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIHRoaXMuZ3JvdXBPcHRpb24gPSByZXMuRGF0YS5EYXRhCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgZ2V0SHRtbCgpIHsKICAgICAgLy8gJHt0aGlzLnByaW50Q29uZmlnLnNoZWV0TmFtZX0KICAgICAgY29uc3QgcXIgPSB0aGlzLiRyZWZzWydxcmNvZGVSZWYnXQogICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4gewogICAgICAgIHRoaXMuJG5leHRUaWNrKF8gPT4gewogICAgICAgICAgY29uc3QgY2FudmFzID0gcXIuJHJlZnNbJ3FyY29kZS12dWUnXQogICAgICAgICAgY29uc3QgZGF0YVVSTCA9IGNhbnZhcy50b0RhdGFVUkwoJ2ltYWdlL3BuZycpCiAgICAgICAgICB0aGlzLnRvcEh0bWwgPSBgCiAgICAgICAgPGgxIGNsYXNzPSJ0aXRsZSI+IyR7dGhpcy5pbmZvLldvcmtpbmdfUHJvY2Vzc19OYW1lIHx8ICcnfSMg5Yqg5bel5Lu75Yqh5Y2VPC9oMT4KICAgICAgICA8ZGl2IGNsYXNzPSJteS10b3AiPgogICAgICAgICAgPGRpdiBjbGFzcz0ibGVmdCI+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9Im15LWxpc3Qtcm93IG15LWxpc3Qtcm93LWZpcnN0Ij4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJteS1saXN0LWNvbCI+6aG555uu5ZCN56ewL+WMuuWfn++8miR7dGhpcy5pbmZvLlByb2plY3RfTmFtZSB8fCAnJ30vJHt0aGlzLmluZm8uQXJlYV9OYW1lIHx8ICcnfTwvZGl2PgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9Im15LWxpc3QtY29sIj7mjpLkuqfljZXlj7fvvJoke3RoaXMuaW5mby5TY2hkdWxpbmdfQ29kZSB8fCAnJ308L2Rpdj4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJteS1saXN0LWNvbCI+5Yqg5bel54+t57uE77yaJHt0aGlzLmluZm8uV29ya2luZ19UZWFtX05hbWUgfHwgJyd9PC9kaXY+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJteS1saXN0LXJvdyI+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ibXktbGlzdC1jb2wiPuS7u+WKoeS4i+WNleaXtumXtO+8miR7dGhpcy5pbmZvLk9yZGVyX0RhdGUgfHwgJyd9PC9kaXY+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ibXktbGlzdC1jb2wiPuS7u+WKoeWNleWPt++8miR7dGhpcy5pbmZvPy5UYXNrX0NvZGUgfHwgJyd9PC9kaXY+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ibXktbGlzdC1jb2wiPuW3peW6j+iuoeWIkuWujOaIkOaXtumXtO+8miR7dGhpcy5pbmZvLlByb2Nlc3NfRmluaXNoX0RhdGUgfHwgJyd9PC9kaXY+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJyaWdodCI+CiAgICAgICAgICAgPGltZyBjbGFzcz0iY3MtaW1nIiBzcmM9IiR7ZGF0YVVSTH0iIGFsdD0iIj4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvZGl2PgogICAgICAgIGAKICAgICAgICAgIHJlc29sdmUoKQogICAgICAgIH0pCiAgICAgIH0pCiAgICB9LAogICAgcHJpbnRFdmVudCgpIHsKICAgICAgdGhpcy5nZXRIdG1sKCkudGhlbigoXykgPT4gewogICAgICAgIGNvbnNvbGUubG9nKHRoaXMucHJpbnRDb25maWcuc2hlZXROYW1lLCAndGhpcy5wcmludENvbmZpZy5zaGVldE5hbWUnKQogICAgICAgIHRoaXMuJHJlZnMueFRhYmxlLnByaW50KHsKICAgICAgICAgIHNoZWV0TmFtZTogdGhpcy5wcmludENvbmZpZy5zaGVldE5hbWUsCiAgICAgICAgICBzdHlsZTogcHJpbnRTdHlsZSwKICAgICAgICAgIG1vZGU6ICdzZWxlY3RlZCcsCiAgICAgICAgICBjb2x1bW5zOiB0aGlzLnByaW50Q29sdW1ucy5tYXAoKHYpID0+IHsKICAgICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgICBmaWVsZDogdi5Db2RlCiAgICAgICAgICAgIH0KICAgICAgICAgIH0pLAogICAgICAgICAgYmVmb3JlUHJpbnRNZXRob2Q6ICh7IGNvbnRlbnQgfSkgPT4gewogICAgICAgICAgICAvLyDmi6bmiKrmiZPljbDkuYvliY3vvIzov5Tlm57oh6rlrprkuYnnmoQgaHRtbCDlhoXlrrkKICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gdGhpcy50b3BIdG1sICsgY29udGVudAogICAgICAgICAgICBjb25zb2xlLmxvZygncmVzdWx0JywgcmVzdWx0KQogICAgICAgICAgICByZXR1cm4gcmVzdWx0CiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfSkKICAgIH0sCiAgICBoYW5kbGVWaWV3KHJvdykgewogICAgfSwKICAgIGdldElubmVyVGFibGUocm93LCBjb2x1bW4pIHsKICAgICAgY29uc3Qgb2IgPSB7CiAgICAgICAgbnVtOiAxLAogICAgICAgIGRhdGU6ICcyMDIyLTIzLTUyJwogICAgICB9CiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgNTA7IGkrKykgewogICAgICAgIHRoaXMuZmluaXNoTGlzdC5wdXNoKG9iKQogICAgICB9CiAgICB9LAogICAgdG9CYWNrKCkgewogICAgICBjbG9zZVRhZ1ZpZXcodGhpcy4kc3RvcmUsIHRoaXMuJHJvdXRlKQogICAgfSwKCiAgICAvLyDlhbPpl63lvLnnqpcKICAgIGhhbmRsZUNsb3NlKCkgewogICAgICB0aGlzLiRyZWZzLlRyYW5zZmVyRGV0YWlsLnJlc2V0Rm9ybSgpCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlCiAgICAgIHRoaXMuZGV2aWNlRGlhbG9nVmlzaWJsZSA9IGZhbHNlCiAgICB9LAoKICAgIC8vIOWNleWFg+agvOeCueWHu+aXtumXtAogICAgY2VsbENsaWNrRXZlbnQoeyByb3csIHJvd0luZGV4LCBjb2x1bW4sIGNvbHVtbkluZGV4IH0pIHsKICAgICAgLy8gaWYgKGNvbHVtbi5wcm9wZXJ0eSA9PT0gIkZpbmlzaF9Db3VudCIgJiYgcm93LkZpbmlzaF9Db3VudCA+IDApIHsKICAgICAgLy8gICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgICAvLyAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgLy8gICAgIHRoaXMuJHJlZnMuVHJhbnNmZXJEZXRhaWwuaW5pdChyb3csIHRoaXMuaXNDb20pOwogICAgICAvLyAgIH0pOwogICAgICAvLyB9CiAgICB9LAoKICAgIC8vIOaUueWPmOWNleWFg+agvOagt+W8jwogICAgY2VsbENsYXNzTmFtZSh7IHJvdywgcm93SW5kZXgsIGNvbHVtbiwgY29sdW1uSW5kZXggfSkgewogICAgICAvLyBpZiAoY29sdW1uLnByb3BlcnR5ID09PSAiRmluaXNoX0NvdW50IikgewogICAgICAvLyAgIHJldHVybiAiY29sLWJsdWUiOwogICAgICAvLyB9CiAgICB9LAoKICAgIGhhbmRsZVN1Z2dlc3REZXZpY2VEaWFsb2coKSB7CiAgICAgIHRoaXMudGl0bGUgPSBg55Sf5Lqn6K6+5aSH6K+m5oOFYAoKICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ1N1Z2dlc3REZXZpY2UnCiAgICAgIHRoaXMuZFdpZHRoID0gJzgwMHB4JwogICAgICB0aGlzLmRldmljZURpYWxvZ1Zpc2libGUgPSB0cnVlCgogICAgICB0aGlzLiRuZXh0VGljayhfID0+IHsKICAgICAgICB0aGlzLiRyZWZzWydkcmFmdCddLmluaXREYXRhKCkKICAgICAgfSkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/PRO/plan-production/task-list", "sourcesContent": ["<template>\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\n    <div class=\"top-btn\" @click=\"toBack\">\n      <el-button>返回</el-button>\n    </div>\n    <div class=\"cs-z-page-main-content\">\n      <el-form ref=\"form\" :model=\"queryForm\" inline label-width=\"130px\">\n        <el-row>\n          <el-col :span=\"20\">\n            <el-form-item label=\"排产单号\" prop=\"Schduling_Code\">\n              {{ info.Schduling_Code }}\n            </el-form-item>\n            <el-form-item label=\"任务单号\" prop=\"Task_Code\">\n              {{ info.Task_Code }}\n            </el-form-item>\n            <el-form-item label=\"项目名称\" prop=\"projectId\">\n              {{ info.Project_Name }}\n            </el-form-item>\n            <el-form-item label=\"区域名称\" prop=\"areaId\">\n              {{ info.Area_Name }}\n            </el-form-item>\n            <el-form-item v-if=\"!isVersionFour\" label=\"批次\" prop=\"install\">\n              {{ info.InstallUnit_Name }}\n            </el-form-item>\n            <!-- <br /> -->\n            <el-form-item label=\"任务下达时间\" prop=\"Order_Date\">\n              {{ info.Order_Date || '-' }}\n            </el-form-item>\n            <el-form-item label=\"工序计划开始时间\" prop=\"Process_Start_Date\">\n              {{ info.Process_Start_Date || '-' }}\n            </el-form-item>\n            <el-form-item label=\"工序计划完成时间\" prop=\"Process_Finish_Date\">\n              {{ info.Process_Finish_Date || '-' }}\n            </el-form-item>\n            <el-form-item label=\"完成时间\" prop=\"Finish_Date2\">\n              {{ info.Finish_Date2 || '-' }}\n            </el-form-item>\n            <el-form-item label=\"备注\" prop=\"Remark\">\n              {{ Remark || '-' }}\n            </el-form-item>\n            <el-form-item label=\"建议设备\" prop=\"eqptInfoListStr\">\n              {{ eqptInfoListStr || '-' }}\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\" style=\"margin-top: 12px;\">\n            <qrcode-vue ref=\"qrcodeRef\" :size=\"79\" :value=\"`T=${info.Task_Code}&C=${Tenant_Code}`\" class-name=\"qrcode\" level=\"H\" />\n          </el-col>\n        </el-row>\n        <br>\n        <el-form-item label=\"下道工序\" prop=\"Next_Process_Id\">\n          <el-select\n            v-model=\"queryForm.Next_Process_Id\"\n            clearable\n            placeholder=\"请选择\"\n            class=\"w100\"\n          >\n            <el-option\n              v-for=\"item in processOption\"\n              :key=\"item.Id\"\n              :label=\"item.Name\"\n              :value=\"item.Id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"下道班组\" prop=\"Next_Team_Id\">\n          <el-select\n            v-model=\"queryForm.Next_Team_Id\"\n            clearable\n            placeholder=\"请选择\"\n            class=\"w100\"\n          >\n            <el-option\n              v-for=\"item in groupOption\"\n              :key=\"item.Id\"\n              :label=\"item.Name\"\n              :value=\"item.Id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" @click=\"search(1)\">搜索</el-button>\n          <el-button @click=\"handleReset()\">重置</el-button>\n          <el-button\n            :disabled=\"!multipleSelection.length\"\n            @click=\"printEvent()\"\n          >打印\n          </el-button>\n          <el-button\n            type=\"success\"\n            @click=\"handleExport\"\n          >导出\n          </el-button>\n          <el-button type=\"primary\" @click=\"handleSuggestDeviceDialog()\">设备详情</el-button>\n        </el-form-item>\n      </el-form>\n      <!--      <el-divider />-->\n      <div class=\"tb-x\">\n        <vxe-table\n          ref=\"xTable\"\n          :empty-render=\"{name: 'NotData'}\"\n          show-header-overflow\n          :print-config=\"printConfig\"\n          :row-config=\"{ isCurrent: true, isHover: true }\"\n          class=\"cs-vxe-table\"\n          align=\"left\"\n          height=\"auto\"\n          show-overflow\n          :loading=\"tbLoading\"\n          stripe\n          size=\"medium\"\n          :data=\"tbData\"\n          resizable\n          :tooltip-config=\"{ enterable: true }\"\n          :cell-class-name=\"cellClassName\"\n          @checkbox-all=\"tbSelectChange\"\n          @checkbox-change=\"tbSelectChange\"\n          @cell-click=\"cellClickEvent\"\n        >\n          <vxe-column type=\"checkbox\" align=\"center\" />\n          <template v-for=\"item in columns\">\n            <vxe-column\n              :key=\"item.Id\"\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n              show-overflow=\"tooltip\"\n              sortable\n              :align=\"item.Align\"\n              :field=\"item.Code\"\n              :title=\"item.Display_Name\"\n              :min-width=\"item.Width\"\n            />\n          </template>\n        </vxe-table>\n      </div>\n    </div>\n    <el-dialog\n      v-dialogDrag\n      class=\"plm-custom-dialog\"\n      :title=\"title\"\n      :visible.sync=\"deviceDialogVisible\"\n      :width=\"dWidth\"\n      :close-on-click-modal=\"false\"\n      top=\"10vh\"\n      @close=\"handleClose\"\n    >\n      <component\n        :is=\"currentComponent\"\n        ref=\"content\"\n        @close=\"handleClose\"\n      />\n    </el-dialog>\n    <el-dialog\n      v-dialogDrag\n      class=\"plm-custom-dialog\"\n      title=\"转移详情\"\n      :visible.sync=\"dialogVisible\"\n      width=\"576px\"\n      @close=\"handleClose\"\n    >\n      <TransferDetail ref=\"TransferDetail\" />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport getTbInfo from '@/mixins/PRO/get-table-info'\nimport { GetTeamTaskDetails, ExportTaskCodeDetails, GetSuggestDeviceAndRemark } from '@/api/PRO/production-task'\nimport { debounce, closeTagView } from '@/utils'\nimport QrcodeVue from 'qrcode.vue'\nimport {\n  GetProcessList,\n  GetWorkingTeamsPageList\n} from '@/api/PRO/technology-lib'\n// import { GetProcessList } from '@/api/PRO/technology-lib'\nimport TransferDetail from './transferDetail'\n// import { Export } from 'vxe-table'\nimport { combineURL } from '@/utils'\nimport { mapGetters } from 'vuex'\nimport SuggestDevice from './suggestDevice'\nimport { getBomCode, getBomName, checkIsUnitPart } from '@/views/PRO/bom-setting/utils'\nconst printStyle = `\n        .title {\n          text-align: center;\n        }\n        .is--print{\n          box-sizing: border-box;\n          width:95% !important;\n          margin:0 auto !important;\n        }\n        .my-list-row {\n          display: inline-block;\n          width: 100%;\n          margin-left:3%;\n        }\n        .my-list-row-first {\n          margin-bottom: 10px;\n        }\n        .my-list-col {\n          width:30%;\n          display: inline-block;\n          float: left;\n          margin-right: 1%;\n          word-wrap:break-word;\n          word-break:normal;\n        }\n        .left{\n          flex:1;\n        }\n        .my-top {\n          display:flex;\n          font-size: 12px;\n          margin-bottom: 5px;\n        }\n        .qrcode{\n          margin-right:10px\n        }\n        .cs-img{\n          position:relative;\n          right:30px\n        }\n        `\n\nexport default {\n  name: 'PROTaskListDetail',\n  components: {\n    TransferDetail,\n    QrcodeVue,\n    SuggestDevice\n  },\n  mixins: [getTbInfo],\n  data() {\n    return {\n      bomName: '',\n      deviceDialogVisible: false,\n      dialogVisible: false,\n      queryForm: {\n        // Project_Id: '',\n        // Area_Id: '',\n        // InstallUnit_Id: '',\n        Next_Process_Id: '',\n        Next_Team_Id: ''\n      },\n      queryInfo: {\n        Page: 1,\n        PageSize: -1\n      },\n      tbConfig: {\n        Op_Width: 120\n      },\n      tbLoading: false,\n      columns: [],\n      multipleSelection: [],\n      tbData: [],\n      finishList: [],\n      processOption: [],\n      groupOption: [],\n      total: 0,\n      printColumns: [],\n      search: () => ({}),\n      pageType: '',\n      info: {\n        Task_Code: '',\n        Project_Name: '',\n        Area_Name: '',\n        InstallUnit_Name: '',\n        Schduling_Code: '',\n        Task_Finish_Date: '',\n        Finish_Date2: '',\n        Order_Date: '',\n        Working_Team_Name: '',\n        Working_Process_Name: '',\n        Process_Start_Date: '',\n        Process_Finish_Date: ''\n      },\n      printConfig: {\n        sheetName: '任务单详情',\n        style: printStyle,\n        beforePrintMethod: ({ content }) => {\n          return this.topHtml + content\n        }\n      },\n      Tenant_Code: localStorage.getItem('tenant'),\n      Remark: '',\n      eqptInfoList: [],\n      eqptInfoListStr: ''\n    }\n  },\n  computed: {\n    ...mapGetters('tenant', ['isVersionFour']),\n    isCom() {\n      return this.pageType === getBomCode('-1')\n    },\n    isUnitPart() {\n      return checkIsUnitPart(this.pageType)\n    },\n    isPart() {\n      return this.pageType === getBomCode('0')\n    }\n  },\n  async mounted() {\n    this.pageType = this.$route.query.type\n    this.bomName = await getBomName(this.pageType)\n    this.workTeamId = this.$route.query.tid\n    this.taskCode = this.$route.query.id\n    this.info = JSON.parse(decodeURIComponent(this.$route.query.other))\n    await this.getSuggestDeviceAndRemark()\n    await this.getTableConfig(this.isCom ? 'PROComTaskListDetail' : this.isUnitPart ? 'PROUnitPartTaskListDetail' : 'PROPartTaskListDetail')\n    if (this.isCom) {\n      const idx = this.columns.findIndex((item) => item.Code === 'Part_Code')\n      idx !== -1 && this.columns.splice(idx, 1)\n\n      this.printColumns = this.columns.filter(column => column.Code !== 'Comp_Description')\n    }\n    if (this.isUnitPart) {\n      this.printColumns = this.columns.filter(column => column.Code !== 'Project_Name' && column.Code !== 'Area_Name' && column.Code !== 'Finish_Count' && column.Code !== 'Finish_Weight' && column.Code !== 'Comp_Description')\n    }\n    if (this.isPart) {\n      this.printColumns = this.columns.filter(column => column.Code !== 'Project_Name' && column.Code !== 'Area_Name' && column.Code !== 'Finish_Count' && column.Code !== 'Finish_Weight' && column.Code !== 'Comp_Description')\n    }\n\n    // else {\n    //   const idx = this.columns.findIndex((item) => item.Code === 'Comp_Code')\n    //   idx !== -1 && this.columns.splice(idx, 1)\n    // }\n    this.search = debounce(this.fetchData, 800, true)\n    this.fetchData()\n    this.getProcessOption()\n    this.getAllTeamOption()\n    this.getHtml()\n  },\n  methods: {\n    getSuggestDeviceAndRemark() {\n      GetSuggestDeviceAndRemark({\n        Bom_Level: this.pageType,\n        Task_Code: this.info.Task_Code\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.Remark = res.Data?.Remark\n          this.eqptInfoList = res.Data?.eqptInfoList || []\n          this.eqptInfoListStr = this.eqptInfoList.map(item => item.DisplayName).join(',')\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    tbSelectChange(array) {\n      this.multipleSelection = array.records\n    },\n    // 导出\n    handleExport() {\n      console.log(this.info)\n      ExportTaskCodeDetails({\n        Process_Type: this.isCom ? 2 : this.isPart ? 1 : 3, // 1零件，2构件\n        Working_Team_Id: this.workTeamId,\n        Task_Code: this.taskCode\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            message: '导出成功',\n            type: 'success'\n          })\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n      // const filterVal = this.columns.map((v) => v.Code)\n      // const data = formatJson(filterVal, this.multipleSelection)\n      // const header = this.columns.map((v) => v.Display_Name)\n      // import('@/vendor/Export2Excel').then((excel) => {\n      //   excel.export_json_to_excel({\n      //     header: header,\n      //     data,\n      //     filename: `${\n      //       this.info?.Task_Code ||\n      //       (this.isCom ? '构件任务单详情' : '零件任务单详情')\n      //     }`,\n      //     autoWidth: true,\n      //     bookType: 'xlsx'\n      //   })\n      // })\n      // function formatJson(filterVal, jsonData) {\n      //   return jsonData.map((v) => filterVal.map((j) => v[j]))\n      // }\n    },\n    fetchData(page) {\n      page && (this.queryInfo.Page = page)\n      console.log(this.queryInfo, 'this.queryInfo')\n      this.tbLoading = true\n      GetTeamTaskDetails({\n        // ...this.queryInfo,\n        Bom_Level: this.pageType,\n        Page: -1,\n        PageSize: -1,\n        Process_Type: this.isCom ? 2 : this.isPart ? 1 : 3, // 1零件，2构件\n        Working_Team_Id: this.workTeamId,\n        Task_Code: this.taskCode,\n        Next_Team_Id: this.queryForm.Next_Team_Id,\n        Next_Process_Id: this.queryForm.Next_Process_Id\n      })\n        .then((res) => {\n          if (res.IsSucceed) {\n            this.tbData = res.Data.Data.filter(item => {\n              return item.Allocation_Count !== 0\n            })\n            // this.total = res.Data.TotalCount\n          } else {\n            this.$message({\n              message: res.Message,\n              type: 'error'\n            })\n          }\n        })\n        .finally((_) => {\n          this.tbLoading = false\n        })\n    },\n    handleReset() {\n      this.$refs['form'].resetFields()\n      this.search(1)\n    },\n    getProcessOption() {\n      GetProcessList({\n        type: this.isCom ? 1 : this.isPart ? 2 : 3, // 1构件，2零件\n        Bom_Level: this.pageType\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.processOption = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    getAllTeamOption() {\n      GetWorkingTeamsPageList({\n        pageInfo: { Page: -1, PageSize: -1, ParameterJson: [] }\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.groupOption = res.Data.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    getHtml() {\n      // ${this.printConfig.sheetName}\n      const qr = this.$refs['qrcodeRef']\n      return new Promise((resolve, reject) => {\n        this.$nextTick(_ => {\n          const canvas = qr.$refs['qrcode-vue']\n          const dataURL = canvas.toDataURL('image/png')\n          this.topHtml = `\n        <h1 class=\"title\">#${this.info.Working_Process_Name || ''}# 加工任务单</h1>\n        <div class=\"my-top\">\n          <div class=\"left\">\n            <div class=\"my-list-row my-list-row-first\">\n              <div class=\"my-list-col\">项目名称/区域：${this.info.Project_Name || ''}/${this.info.Area_Name || ''}</div>\n              <div class=\"my-list-col\">排产单号：${this.info.Schduling_Code || ''}</div>\n              <div class=\"my-list-col\">加工班组：${this.info.Working_Team_Name || ''}</div>\n            </div>\n            <div class=\"my-list-row\">\n              <div class=\"my-list-col\">任务下单时间：${this.info.Order_Date || ''}</div>\n              <div class=\"my-list-col\">任务单号：${this.info?.Task_Code || ''}</div>\n              <div class=\"my-list-col\">工序计划完成时间：${this.info.Process_Finish_Date || ''}</div>\n            </div>\n          </div>\n          <div class=\"right\">\n           <img class=\"cs-img\" src=\"${dataURL}\" alt=\"\">\n          </div>\n        </div>\n        `\n          resolve()\n        })\n      })\n    },\n    printEvent() {\n      this.getHtml().then((_) => {\n        console.log(this.printConfig.sheetName, 'this.printConfig.sheetName')\n        this.$refs.xTable.print({\n          sheetName: this.printConfig.sheetName,\n          style: printStyle,\n          mode: 'selected',\n          columns: this.printColumns.map((v) => {\n            return {\n              field: v.Code\n            }\n          }),\n          beforePrintMethod: ({ content }) => {\n            // 拦截打印之前，返回自定义的 html 内容\n            const result = this.topHtml + content\n            console.log('result', result)\n            return result\n          }\n        })\n      })\n    },\n    handleView(row) {\n    },\n    getInnerTable(row, column) {\n      const ob = {\n        num: 1,\n        date: '2022-23-52'\n      }\n      for (let i = 0; i < 50; i++) {\n        this.finishList.push(ob)\n      }\n    },\n    toBack() {\n      closeTagView(this.$store, this.$route)\n    },\n\n    // 关闭弹窗\n    handleClose() {\n      this.$refs.TransferDetail.resetForm()\n      this.dialogVisible = false\n      this.deviceDialogVisible = false\n    },\n\n    // 单元格点击时间\n    cellClickEvent({ row, rowIndex, column, columnIndex }) {\n      // if (column.property === \"Finish_Count\" && row.Finish_Count > 0) {\n      //   this.dialogVisible = true;\n      //   this.$nextTick(() => {\n      //     this.$refs.TransferDetail.init(row, this.isCom);\n      //   });\n      // }\n    },\n\n    // 改变单元格样式\n    cellClassName({ row, rowIndex, column, columnIndex }) {\n      // if (column.property === \"Finish_Count\") {\n      //   return \"col-blue\";\n      // }\n    },\n\n    handleSuggestDeviceDialog() {\n      this.title = `生产设备详情`\n\n      this.currentComponent = 'SuggestDevice'\n      this.dWidth = '800px'\n      this.deviceDialogVisible = true\n\n      this.$nextTick(_ => {\n        this.$refs['draft'].initData()\n      })\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.el-divider {\n  margin: 0 0 10px;\n}\n\n.tb-x {\n  flex: 1;\n  overflow:auto;\n\n  ::v-deep {\n    .cs-vxe-table .vxe-body--column.col-blue {\n      color: #298dff;\n      cursor: pointer;\n    }\n  }\n}\n\n.cs-z-flex-pd16-wrap {\n  padding-top: 50px;\n\n  .top-btn {\n    position: absolute;\n    top: 12px;\n    left: 20px;\n    z-index: 99;\n\n    .el-button {\n      background-color: #f7f8f9;\n    }\n  }\n\n  .cs-z-page-main-content {\n    overflow:hidden;\n    ::v-deep {\n      .el-form-item__content {\n        min-width: 200px;\n      }\n    }\n  }\n}\n</style>\n"]}]}
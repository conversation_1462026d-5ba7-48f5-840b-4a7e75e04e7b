{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-list\\detail.vue", "mtime": 1757909680923}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgZ2V0VGJJbmZvIGZyb20gJ0AvbWl4aW5zL1BSTy9nZXQtdGFibGUtaW5mbycNCmltcG9ydCB7IEdldFRlYW1UYXNrRGV0YWlscywgRXhwb3J0VGFza0NvZGVEZXRhaWxzIH0gZnJvbSAnQC9hcGkvUFJPL3Byb2R1Y3Rpb24tdGFzaycNCmltcG9ydCB7IGRlYm91bmNlLCBjbG9zZVRhZ1ZpZXcgfSBmcm9tICdAL3V0aWxzJw0KaW1wb3J0IFFyY29kZVZ1ZSBmcm9tICdxcmNvZGUudnVlJw0KaW1wb3J0IHsNCiAgR2V0UHJvY2Vzc0xpc3QsDQogIEdldFdvcmtpbmdUZWFtc1BhZ2VMaXN0DQp9IGZyb20gJ0AvYXBpL1BSTy90ZWNobm9sb2d5LWxpYicNCi8vIGltcG9ydCB7IEdldFByb2Nlc3NMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL3RlY2hub2xvZ3ktbGliJw0KaW1wb3J0IFRyYW5zZmVyRGV0YWlsIGZyb20gJy4vdHJhbnNmZXJEZXRhaWwnDQppbXBvcnQgeyBFeHBvcnQgfSBmcm9tICd2eGUtdGFibGUnDQppbXBvcnQgeyBjb21iaW5lVVJMIH0gZnJvbSAnQC91dGlscycNCmltcG9ydCB7IG1hcEdldHRlcnMgfSBmcm9tICd2dWV4Jw0KaW1wb3J0IHsgZ2V0Qm9tQ29kZSwgY2hlY2tJc1VuaXRQYXJ0IH0gZnJvbSAnQC92aWV3cy9QUk8vYm9tLXNldHRpbmcvdXRpbHMnDQpjb25zdCBwcmludFN0eWxlID0gYA0KICAgICAgICAudGl0bGUgew0KICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICAgICAgfQ0KICAgICAgICAuaXMtLXByaW50ew0KICAgICAgICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7DQogICAgICAgICAgd2lkdGg6OTUlICFpbXBvcnRhbnQ7DQogICAgICAgICAgbWFyZ2luOjAgYXV0byAhaW1wb3J0YW50Ow0KICAgICAgICB9DQogICAgICAgIC5teS1saXN0LXJvdyB7DQogICAgICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICAgICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICAgIG1hcmdpbi1sZWZ0OjMlOw0KICAgICAgICB9DQogICAgICAgIC5teS1saXN0LXJvdy1maXJzdCB7DQogICAgICAgICAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgICAgICAgfQ0KICAgICAgICAubXktbGlzdC1jb2wgew0KICAgICAgICAgIHdpZHRoOjMwJTsNCiAgICAgICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogICAgICAgICAgZmxvYXQ6IGxlZnQ7DQogICAgICAgICAgbWFyZ2luLXJpZ2h0OiAxJTsNCiAgICAgICAgICB3b3JkLXdyYXA6YnJlYWstd29yZDsNCiAgICAgICAgICB3b3JkLWJyZWFrOm5vcm1hbDsNCiAgICAgICAgfQ0KICAgICAgICAubGVmdHsNCiAgICAgICAgICBmbGV4OjE7DQogICAgICAgIH0NCiAgICAgICAgLm15LXRvcCB7DQogICAgICAgICAgZGlzcGxheTpmbGV4Ow0KICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgICBtYXJnaW4tYm90dG9tOiA1cHg7DQogICAgICAgIH0NCiAgICAgICAgLnFyY29kZXsNCiAgICAgICAgICBtYXJnaW4tcmlnaHQ6MTBweA0KICAgICAgICB9DQogICAgICAgIC5jcy1pbWd7DQogICAgICAgICAgcG9zaXRpb246cmVsYXRpdmU7DQogICAgICAgICAgcmlnaHQ6MzBweA0KICAgICAgICB9DQogICAgICAgIGANCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnUFJPVGFza0xpc3REZXRhaWwnLA0KICBjb21wb25lbnRzOiB7DQogICAgVHJhbnNmZXJEZXRhaWwsDQogICAgUXJjb2RlVnVlDQogIH0sDQogIG1peGluczogW2dldFRiSW5mb10sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgcXVlcnlGb3JtOiB7DQogICAgICAgIC8vIFByb2plY3RfSWQ6ICcnLA0KICAgICAgICAvLyBBcmVhX0lkOiAnJywNCiAgICAgICAgLy8gSW5zdGFsbFVuaXRfSWQ6ICcnLA0KICAgICAgICBOZXh0X1Byb2Nlc3NfSWQ6ICcnLA0KICAgICAgICBOZXh0X1RlYW1fSWQ6ICcnDQogICAgICB9LA0KICAgICAgcXVlcnlJbmZvOiB7DQogICAgICAgIFBhZ2U6IDEsDQogICAgICAgIFBhZ2VTaXplOiAtMQ0KICAgICAgfSwNCiAgICAgIHRiQ29uZmlnOiB7DQogICAgICAgIE9wX1dpZHRoOiAxMjANCiAgICAgIH0sDQogICAgICB0YkxvYWRpbmc6IGZhbHNlLA0KICAgICAgY29sdW1uczogW10sDQogICAgICBtdWx0aXBsZVNlbGVjdGlvbjogW10sDQogICAgICB0YkRhdGE6IFtdLA0KICAgICAgZmluaXNoTGlzdDogW10sDQogICAgICBwcm9jZXNzT3B0aW9uOiBbXSwNCiAgICAgIGdyb3VwT3B0aW9uOiBbXSwNCiAgICAgIHRvdGFsOiAwLA0KICAgICAgcHJpbnRDb2x1bW5zOiBbXSwNCiAgICAgIHNlYXJjaDogKCkgPT4gKHt9KSwNCiAgICAgIHBhZ2VUeXBlOiAnJywNCiAgICAgIGluZm86IHsNCiAgICAgICAgVGFza19Db2RlOiAnJywNCiAgICAgICAgUHJvamVjdF9OYW1lOiAnJywNCiAgICAgICAgQXJlYV9OYW1lOiAnJywNCiAgICAgICAgSW5zdGFsbFVuaXRfTmFtZTogJycsDQogICAgICAgIFNjaGR1bGluZ19Db2RlOiAnJywNCiAgICAgICAgVGFza19GaW5pc2hfRGF0ZTogJycsDQogICAgICAgIEZpbmlzaF9EYXRlMjogJycsDQogICAgICAgIE9yZGVyX0RhdGU6ICcnLA0KICAgICAgICBXb3JraW5nX1RlYW1fTmFtZTogJycsDQogICAgICAgIFdvcmtpbmdfUHJvY2Vzc19OYW1lOiAnJw0KICAgICAgfSwNCiAgICAgIHByaW50Q29uZmlnOiB7DQogICAgICAgIHNoZWV0TmFtZTogJ+S7u+WKoeWNleivpuaDhScsDQogICAgICAgIHN0eWxlOiBwcmludFN0eWxlLA0KICAgICAgICBiZWZvcmVQcmludE1ldGhvZDogKHsgY29udGVudCB9KSA9PiB7DQogICAgICAgICAgcmV0dXJuIHRoaXMudG9wSHRtbCArIGNvbnRlbnQNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIFRlbmFudF9Db2RlOiBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndGVuYW50JykNCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgLi4ubWFwR2V0dGVycygndGVuYW50JywgWydpc1ZlcnNpb25Gb3VyJ10pLA0KICAgIGlzQ29tKCkgew0KICAgICAgcmV0dXJuIHRoaXMucGFnZVR5cGUgPT09IGdldEJvbUNvZGUoJy0xJykNCiAgICB9LA0KICAgIGlzVW5pdFBhcnQoKSB7DQogICAgICByZXR1cm4gY2hlY2tJc1VuaXRQYXJ0KHRoaXMucGFnZVR5cGUpDQogICAgfSwNCiAgICBpc1BhcnQoKSB7DQogICAgICByZXR1cm4gdGhpcy5wYWdlVHlwZSA9PT0gZ2V0Qm9tQ29kZSgnMCcpDQogICAgfQ0KICB9LA0KICBhc3luYyBtb3VudGVkKCkgew0KICAgIHRoaXMucGFnZVR5cGUgPSB0aGlzLiRyb3V0ZS5xdWVyeS50eXBlDQogICAgdGhpcy53b3JrVGVhbUlkID0gdGhpcy4kcm91dGUucXVlcnkudGlkDQogICAgdGhpcy50YXNrQ29kZSA9IHRoaXMuJHJvdXRlLnF1ZXJ5LmlkDQogICAgdGhpcy5pbmZvID0gSlNPTi5wYXJzZShkZWNvZGVVUklDb21wb25lbnQodGhpcy4kcm91dGUucXVlcnkub3RoZXIpKQ0KICAgIGF3YWl0IHRoaXMuZ2V0VGFibGVDb25maWcodGhpcy5pc0NvbSA/ICdQUk9Db21UYXNrTGlzdERldGFpbCcgOiB0aGlzLmlzVW5pdFBhcnQgPyAnUFJPVW5pdFBhcnRUYXNrTGlzdERldGFpbCcgOiAnUFJPUGFydFRhc2tMaXN0RGV0YWlsJykNCiAgICBpZiAodGhpcy5pc0NvbSkgew0KICAgICAgY29uc3QgaWR4ID0gdGhpcy5jb2x1bW5zLmZpbmRJbmRleCgoaXRlbSkgPT4gaXRlbS5Db2RlID09PSAnUGFydF9Db2RlJykNCiAgICAgIGlkeCAhPT0gLTEgJiYgdGhpcy5jb2x1bW5zLnNwbGljZShpZHgsIDEpDQoNCiAgICAgIHRoaXMucHJpbnRDb2x1bW5zID0gdGhpcy5jb2x1bW5zLmZpbHRlcihjb2x1bW4gPT4gY29sdW1uLkNvZGUgIT09ICdDb21wX0Rlc2NyaXB0aW9uJykNCiAgICB9DQogICAgaWYgKHRoaXMuaXNVbml0UGFydCkgew0KICAgICAgdGhpcy5wcmludENvbHVtbnMgPSB0aGlzLmNvbHVtbnMuZmlsdGVyKGNvbHVtbiA9PiBjb2x1bW4uQ29kZSAhPT0gJ1Byb2plY3RfTmFtZScgJiYgY29sdW1uLkNvZGUgIT09ICdBcmVhX05hbWUnICYmIGNvbHVtbi5Db2RlICE9PSAnRmluaXNoX0NvdW50JyAmJiBjb2x1bW4uQ29kZSAhPT0gJ0ZpbmlzaF9XZWlnaHQnICYmIGNvbHVtbi5Db2RlICE9PSAnQ29tcF9EZXNjcmlwdGlvbicpDQogICAgfQ0KICAgIGlmICh0aGlzLmlzUGFydCkgew0KICAgICAgdGhpcy5wcmludENvbHVtbnMgPSB0aGlzLmNvbHVtbnMuZmlsdGVyKGNvbHVtbiA9PiBjb2x1bW4uQ29kZSAhPT0gJ1Byb2plY3RfTmFtZScgJiYgY29sdW1uLkNvZGUgIT09ICdBcmVhX05hbWUnICYmIGNvbHVtbi5Db2RlICE9PSAnRmluaXNoX0NvdW50JyAmJiBjb2x1bW4uQ29kZSAhPT0gJ0ZpbmlzaF9XZWlnaHQnICYmIGNvbHVtbi5Db2RlICE9PSAnQ29tcF9EZXNjcmlwdGlvbicpDQogICAgfQ0KICAgIC8vIGVsc2Ugew0KICAgIC8vICAgY29uc3QgaWR4ID0gdGhpcy5jb2x1bW5zLmZpbmRJbmRleCgoaXRlbSkgPT4gaXRlbS5Db2RlID09PSAnQ29tcF9Db2RlJykNCiAgICAvLyAgIGlkeCAhPT0gLTEgJiYgdGhpcy5jb2x1bW5zLnNwbGljZShpZHgsIDEpDQogICAgLy8gfQ0KICAgIHRoaXMuc2VhcmNoID0gZGVib3VuY2UodGhpcy5mZXRjaERhdGEsIDgwMCwgdHJ1ZSkNCiAgICB0aGlzLmZldGNoRGF0YSgpDQogICAgdGhpcy5nZXRQcm9jZXNzT3B0aW9uKCkNCiAgICB0aGlzLmdldEFsbFRlYW1PcHRpb24oKQ0KICAgIHRoaXMuZ2V0SHRtbCgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICB0YlNlbGVjdENoYW5nZShhcnJheSkgew0KICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbiA9IGFycmF5LnJlY29yZHMNCiAgICB9LA0KICAgIC8vIOWvvOWHug0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIGNvbnNvbGUubG9nKHRoaXMuaW5mbykNCiAgICAgIEV4cG9ydFRhc2tDb2RlRGV0YWlscyh7DQogICAgICAgIFByb2Nlc3NfVHlwZTogdGhpcy5pc0NvbSA/IDIgOiB0aGlzLmlzUGFydCA/IDEgOiAzLCAvLyAx6Zu25Lu277yMMuaehOS7tg0KICAgICAgICBXb3JraW5nX1RlYW1fSWQ6IHRoaXMud29ya1RlYW1JZCwNCiAgICAgICAgVGFza19Db2RlOiB0aGlzLnRhc2tDb2RlDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6ICflr7zlh7rmiJDlip8nLA0KICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgICAgfSkNCiAgICAgICAgICB3aW5kb3cub3Blbihjb21iaW5lVVJMKHRoaXMuJGJhc2VVcmwsIHJlcy5EYXRhKSwgJ19ibGFuaycpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgLy8gY29uc3QgZmlsdGVyVmFsID0gdGhpcy5jb2x1bW5zLm1hcCgodikgPT4gdi5Db2RlKQ0KICAgICAgLy8gY29uc3QgZGF0YSA9IGZvcm1hdEpzb24oZmlsdGVyVmFsLCB0aGlzLm11bHRpcGxlU2VsZWN0aW9uKQ0KICAgICAgLy8gY29uc3QgaGVhZGVyID0gdGhpcy5jb2x1bW5zLm1hcCgodikgPT4gdi5EaXNwbGF5X05hbWUpDQogICAgICAvLyBpbXBvcnQoJ0AvdmVuZG9yL0V4cG9ydDJFeGNlbCcpLnRoZW4oKGV4Y2VsKSA9PiB7DQogICAgICAvLyAgIGV4Y2VsLmV4cG9ydF9qc29uX3RvX2V4Y2VsKHsNCiAgICAgIC8vICAgICBoZWFkZXI6IGhlYWRlciwNCiAgICAgIC8vICAgICBkYXRhLA0KICAgICAgLy8gICAgIGZpbGVuYW1lOiBgJHsNCiAgICAgIC8vICAgICAgIHRoaXMuaW5mbz8uVGFza19Db2RlIHx8DQogICAgICAvLyAgICAgICAodGhpcy5pc0NvbSA/ICfmnoTku7bku7vliqHljZXor6bmg4UnIDogJ+mbtuS7tuS7u+WKoeWNleivpuaDhScpDQogICAgICAvLyAgICAgfWAsDQogICAgICAvLyAgICAgYXV0b1dpZHRoOiB0cnVlLA0KICAgICAgLy8gICAgIGJvb2tUeXBlOiAneGxzeCcNCiAgICAgIC8vICAgfSkNCiAgICAgIC8vIH0pDQogICAgICAvLyBmdW5jdGlvbiBmb3JtYXRKc29uKGZpbHRlclZhbCwganNvbkRhdGEpIHsNCiAgICAgIC8vICAgcmV0dXJuIGpzb25EYXRhLm1hcCgodikgPT4gZmlsdGVyVmFsLm1hcCgoaikgPT4gdltqXSkpDQogICAgICAvLyB9DQogICAgfSwNCiAgICBmZXRjaERhdGEocGFnZSkgew0KICAgICAgcGFnZSAmJiAodGhpcy5xdWVyeUluZm8uUGFnZSA9IHBhZ2UpDQogICAgICBjb25zb2xlLmxvZyh0aGlzLnF1ZXJ5SW5mbywgJ3RoaXMucXVlcnlJbmZvJykNCiAgICAgIHRoaXMudGJMb2FkaW5nID0gdHJ1ZQ0KICAgICAgR2V0VGVhbVRhc2tEZXRhaWxzKHsNCiAgICAgICAgLy8gLi4udGhpcy5xdWVyeUluZm8sDQogICAgICAgIEJvbV9MZXZlbDogdGhpcy5wYWdlVHlwZSwNCiAgICAgICAgUGFnZTogLTEsDQogICAgICAgIFBhZ2VTaXplOiAtMSwNCiAgICAgICAgUHJvY2Vzc19UeXBlOiB0aGlzLmlzQ29tID8gMiA6IHRoaXMuaXNQYXJ0ID8gMSA6IDMsIC8vIDHpm7bku7bvvIwy5p6E5Lu2DQogICAgICAgIFdvcmtpbmdfVGVhbV9JZDogdGhpcy53b3JrVGVhbUlkLA0KICAgICAgICBUYXNrX0NvZGU6IHRoaXMudGFza0NvZGUsDQogICAgICAgIE5leHRfVGVhbV9JZDogdGhpcy5xdWVyeUZvcm0uTmV4dF9UZWFtX0lkLA0KICAgICAgICBOZXh0X1Byb2Nlc3NfSWQ6IHRoaXMucXVlcnlGb3JtLk5leHRfUHJvY2Vzc19JZA0KICAgICAgfSkNCiAgICAgICAgLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICB0aGlzLnRiRGF0YSA9IHJlcy5EYXRhLkRhdGEuZmlsdGVyKGl0ZW0gPT4gew0KICAgICAgICAgICAgICByZXR1cm4gaXRlbS5BbGxvY2F0aW9uX0NvdW50ICE9PSAwDQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgLy8gdGhpcy50b3RhbCA9IHJlcy5EYXRhLlRvdGFsQ291bnQNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgLmZpbmFsbHkoKF8pID0+IHsNCiAgICAgICAgICB0aGlzLnRiTG9hZGluZyA9IGZhbHNlDQogICAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVSZXNldCgpIHsNCiAgICAgIHRoaXMuJHJlZnNbJ2Zvcm0nXS5yZXNldEZpZWxkcygpDQogICAgICB0aGlzLnNlYXJjaCgxKQ0KICAgIH0sDQogICAgZ2V0UHJvY2Vzc09wdGlvbigpIHsNCiAgICAgIEdldFByb2Nlc3NMaXN0KHsNCiAgICAgICAgdHlwZTogdGhpcy5pc0NvbSA/IDEgOiB0aGlzLmlzUGFydCA/IDIgOiAzLCAvLyAx5p6E5Lu277yMMumbtuS7tg0KICAgICAgICBCb21fTGV2ZWw6IHRoaXMucGFnZVR5cGUNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMucHJvY2Vzc09wdGlvbiA9IHJlcy5EYXRhDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgZ2V0QWxsVGVhbU9wdGlvbigpIHsNCiAgICAgIEdldFdvcmtpbmdUZWFtc1BhZ2VMaXN0KHsNCiAgICAgICAgcGFnZUluZm86IHsgUGFnZTogLTEsIFBhZ2VTaXplOiAtMSwgUGFyYW1ldGVySnNvbjogW10gfQ0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy5ncm91cE9wdGlvbiA9IHJlcy5EYXRhLkRhdGENCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBnZXRIdG1sKCkgew0KICAgICAgLy8gJHt0aGlzLnByaW50Q29uZmlnLnNoZWV0TmFtZX0NCiAgICAgIGNvbnN0IHFyID0gdGhpcy4kcmVmc1sncXJjb2RlUmVmJ10NCiAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7DQogICAgICAgIHRoaXMuJG5leHRUaWNrKF8gPT4gew0KICAgICAgICAgIGNvbnN0IGNhbnZhcyA9IHFyLiRyZWZzWydxcmNvZGUtdnVlJ10NCiAgICAgICAgICBjb25zdCBkYXRhVVJMID0gY2FudmFzLnRvRGF0YVVSTCgnaW1hZ2UvcG5nJykNCiAgICAgICAgICB0aGlzLnRvcEh0bWwgPSBgDQogICAgICAgIDxoMSBjbGFzcz0idGl0bGUiPiMke3RoaXMuaW5mby5Xb3JraW5nX1Byb2Nlc3NfTmFtZSB8fCAnJ30jIOWKoOW3peS7u+WKoeWNlTwvaDE+DQogICAgICAgIDxkaXYgY2xhc3M9Im15LXRvcCI+DQogICAgICAgICAgPGRpdiBjbGFzcz0ibGVmdCI+DQogICAgICAgICAgICA8ZGl2IGNsYXNzPSJteS1saXN0LXJvdyBteS1saXN0LXJvdy1maXJzdCI+DQogICAgICAgICAgICAgIDxkaXYgY2xhc3M9Im15LWxpc3QtY29sIj7pobnnm67lkI3np7Av5Yy65Z+f77yaJHt0aGlzLmluZm8uUHJvamVjdF9OYW1lIHx8ICcnfS8ke3RoaXMuaW5mby5BcmVhX05hbWUgfHwgJyd9PC9kaXY+DQogICAgICAgICAgICAgIDxkaXYgY2xhc3M9Im15LWxpc3QtY29sIj7mjpLkuqfljZXlj7fvvJoke3RoaXMuaW5mby5TY2hkdWxpbmdfQ29kZSB8fCAnJ308L2Rpdj4NCiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ibXktbGlzdC1jb2wiPuWKoOW3peePree7hO+8miR7dGhpcy5pbmZvLldvcmtpbmdfVGVhbV9OYW1lIHx8ICcnfTwvZGl2Pg0KICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICA8ZGl2IGNsYXNzPSJteS1saXN0LXJvdyI+DQogICAgICAgICAgICAgIDxkaXYgY2xhc3M9Im15LWxpc3QtY29sIj7ku7vliqHkuIvljZXml7bpl7TvvJoke3RoaXMuaW5mby5PcmRlcl9EYXRlIHx8ICcnfTwvZGl2Pg0KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJteS1saXN0LWNvbCI+5Lu75Yqh5Y2V5Y+377yaJHt0aGlzLmluZm8/LlRhc2tfQ29kZSB8fCAnJ308L2Rpdj4NCiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ibXktbGlzdC1jb2wiPuW3peW6j+imgeaxguWujOaIkOaXtumXtO+8miR7dGhpcy5pbmZvLlByb2Nlc3NfRmluaXNoX0RhdGUgfHwgJyd9PC9kaXY+DQogICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJyaWdodCI+DQogICAgICAgICAgIDxpbWcgY2xhc3M9ImNzLWltZyIgc3JjPSIke2RhdGFVUkx9IiBhbHQ9IiI+DQogICAgICAgICAgPC9kaXY+DQogICAgICAgIDwvZGl2Pg0KICAgICAgICBgDQogICAgICAgICAgcmVzb2x2ZSgpDQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0sDQogICAgcHJpbnRFdmVudCgpIHsNCiAgICAgIHRoaXMuZ2V0SHRtbCgpLnRoZW4oKF8pID0+IHsNCiAgICAgICAgY29uc29sZS5sb2codGhpcy5wcmludENvbmZpZy5zaGVldE5hbWUsICd0aGlzLnByaW50Q29uZmlnLnNoZWV0TmFtZScpDQogICAgICAgIHRoaXMuJHJlZnMueFRhYmxlLnByaW50KHsNCiAgICAgICAgICBzaGVldE5hbWU6IHRoaXMucHJpbnRDb25maWcuc2hlZXROYW1lLA0KICAgICAgICAgIHN0eWxlOiBwcmludFN0eWxlLA0KICAgICAgICAgIG1vZGU6ICdzZWxlY3RlZCcsDQogICAgICAgICAgY29sdW1uczogdGhpcy5wcmludENvbHVtbnMubWFwKCh2KSA9PiB7DQogICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICBmaWVsZDogdi5Db2RlDQogICAgICAgICAgICB9DQogICAgICAgICAgfSksDQogICAgICAgICAgYmVmb3JlUHJpbnRNZXRob2Q6ICh7IGNvbnRlbnQgfSkgPT4gew0KICAgICAgICAgICAgLy8g5oum5oiq5omT5Y2w5LmL5YmN77yM6L+U5Zue6Ieq5a6a5LmJ55qEIGh0bWwg5YaF5a65DQogICAgICAgICAgICBjb25zdCByZXN1bHQgPSB0aGlzLnRvcEh0bWwgKyBjb250ZW50DQogICAgICAgICAgICBjb25zb2xlLmxvZygncmVzdWx0JywgcmVzdWx0KQ0KICAgICAgICAgICAgcmV0dXJuIHJlc3VsdA0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVWaWV3KHJvdykgew0KICAgIH0sDQogICAgZ2V0SW5uZXJUYWJsZShyb3csIGNvbHVtbikgew0KICAgICAgY29uc3Qgb2IgPSB7DQogICAgICAgIG51bTogMSwNCiAgICAgICAgZGF0ZTogJzIwMjItMjMtNTInDQogICAgICB9DQogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IDUwOyBpKyspIHsNCiAgICAgICAgdGhpcy5maW5pc2hMaXN0LnB1c2gob2IpDQogICAgICB9DQogICAgfSwNCiAgICB0b0JhY2soKSB7DQogICAgICBjbG9zZVRhZ1ZpZXcodGhpcy4kc3RvcmUsIHRoaXMuJHJvdXRlKQ0KICAgIH0sDQoNCiAgICAvLyDlhbPpl63lvLnnqpcNCiAgICBoYW5kbGVDbG9zZSgpIHsNCiAgICAgIHRoaXMuJHJlZnMuVHJhbnNmZXJEZXRhaWwucmVzZXRGb3JtKCkNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgfSwNCg0KICAgIC8vIOWNleWFg+agvOeCueWHu+aXtumXtA0KICAgIGNlbGxDbGlja0V2ZW50KHsgcm93LCByb3dJbmRleCwgY29sdW1uLCBjb2x1bW5JbmRleCB9KSB7DQogICAgICAvLyBpZiAoY29sdW1uLnByb3BlcnR5ID09PSAiRmluaXNoX0NvdW50IiAmJiByb3cuRmluaXNoX0NvdW50ID4gMCkgew0KICAgICAgLy8gICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgLy8gICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAvLyAgICAgdGhpcy4kcmVmcy5UcmFuc2ZlckRldGFpbC5pbml0KHJvdywgdGhpcy5pc0NvbSk7DQogICAgICAvLyAgIH0pOw0KICAgICAgLy8gfQ0KICAgIH0sDQoNCiAgICAvLyDmlLnlj5jljZXlhYPmoLzmoLflvI8NCiAgICBjZWxsQ2xhc3NOYW1lKHsgcm93LCByb3dJbmRleCwgY29sdW1uLCBjb2x1bW5JbmRleCB9KSB7DQogICAgICAvLyBpZiAoY29sdW1uLnByb3BlcnR5ID09PSAiRmluaXNoX0NvdW50Iikgew0KICAgICAgLy8gICByZXR1cm4gImNvbC1ibHVlIjsNCiAgICAgIC8vIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/PRO/plan-production/task-list", "sourcesContent": ["<template>\r\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\r\n    <div class=\"top-btn\" @click=\"toBack\">\r\n      <el-button>返回</el-button>\r\n    </div>\r\n    <div class=\"cs-z-page-main-content\">\r\n      <el-form ref=\"form\" :model=\"queryForm\" inline label-width=\"130px\">\r\n        <el-row>\r\n          <el-col :span=\"20\">\r\n            <el-form-item label=\"排产单号\" prop=\"Schduling_Code\">\r\n              {{ info.Schduling_Code }}\r\n            </el-form-item>\r\n            <el-form-item label=\"任务单号\" prop=\"Task_Code\">\r\n              {{ info.Task_Code }}\r\n            </el-form-item>\r\n            <el-form-item label=\"项目名称\" prop=\"projectId\">\r\n              {{ info.Project_Name }}\r\n            </el-form-item>\r\n            <el-form-item label=\"区域名称\" prop=\"areaId\">\r\n              {{ info.Area_Name }}\r\n            </el-form-item>\r\n            <el-form-item v-if=\"!isVersionFour\" label=\"批次\" prop=\"install\">\r\n              {{ info.InstallUnit_Name }}\r\n            </el-form-item>\r\n            <!-- <br /> -->\r\n            <el-form-item label=\"任务下达时间\" prop=\"Order_Date\">\r\n              {{ info.Order_Date }}\r\n            </el-form-item>\r\n            <el-form-item label=\"工序要求完成时间\" prop=\"Process_Finish_Date\">\r\n              {{ info.Process_Finish_Date }}\r\n            </el-form-item>\r\n            <el-form-item label=\"完成时间\" prop=\"Finish_Date2\">\r\n              {{ info.Finish_Date2 }}\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\" style=\"margin-top: 12px;\">\r\n            <qrcode-vue ref=\"qrcodeRef\" :size=\"79\" :value=\"`T=${info.Task_Code}&C=${Tenant_Code}`\" class-name=\"qrcode\" level=\"H\" />\r\n          </el-col>\r\n        </el-row>\r\n        <br>\r\n        <el-form-item label=\"下道工序\" prop=\"Next_Process_Id\">\r\n          <el-select\r\n            v-model=\"queryForm.Next_Process_Id\"\r\n            clearable\r\n            placeholder=\"请选择\"\r\n            class=\"w100\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in processOption\"\r\n              :key=\"item.Id\"\r\n              :label=\"item.Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"下道班组\" prop=\"Next_Team_Id\">\r\n          <el-select\r\n            v-model=\"queryForm.Next_Team_Id\"\r\n            clearable\r\n            placeholder=\"请选择\"\r\n            class=\"w100\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in groupOption\"\r\n              :key=\"item.Id\"\r\n              :label=\"item.Name\"\r\n              :value=\"item.Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"search(1)\">搜索</el-button>\r\n          <el-button @click=\"handleReset()\">重置</el-button>\r\n          <el-button\r\n            :disabled=\"!multipleSelection.length\"\r\n            @click=\"printEvent()\"\r\n          >打印\r\n          </el-button>\r\n          <el-button\r\n            type=\"success\"\r\n            @click=\"handleExport\"\r\n          >导出\r\n          </el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <!--      <el-divider />-->\r\n      <div class=\"tb-x\">\r\n        <vxe-table\r\n          ref=\"xTable\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          :print-config=\"printConfig\"\r\n          :row-config=\"{ isCurrent: true, isHover: true }\"\r\n          class=\"cs-vxe-table\"\r\n          align=\"left\"\r\n          height=\"auto\"\r\n          show-overflow\r\n          :loading=\"tbLoading\"\r\n          stripe\r\n          size=\"medium\"\r\n          :data=\"tbData\"\r\n          resizable\r\n          :tooltip-config=\"{ enterable: true }\"\r\n          :cell-class-name=\"cellClassName\"\r\n          @checkbox-all=\"tbSelectChange\"\r\n          @checkbox-change=\"tbSelectChange\"\r\n          @cell-click=\"cellClickEvent\"\r\n        >\r\n          <vxe-column type=\"checkbox\" align=\"center\" />\r\n          <template v-for=\"item in columns\">\r\n            <vxe-column\r\n              :key=\"item.Id\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :align=\"item.Align\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n            />\r\n          </template>\r\n        </vxe-table>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      title=\"转移详情\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"576px\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <TransferDetail ref=\"TransferDetail\" />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport { GetTeamTaskDetails, ExportTaskCodeDetails } from '@/api/PRO/production-task'\r\nimport { debounce, closeTagView } from '@/utils'\r\nimport QrcodeVue from 'qrcode.vue'\r\nimport {\r\n  GetProcessList,\r\n  GetWorkingTeamsPageList\r\n} from '@/api/PRO/technology-lib'\r\n// import { GetProcessList } from '@/api/PRO/technology-lib'\r\nimport TransferDetail from './transferDetail'\r\nimport { Export } from 'vxe-table'\r\nimport { combineURL } from '@/utils'\r\nimport { mapGetters } from 'vuex'\r\nimport { getBomCode, checkIsUnitPart } from '@/views/PRO/bom-setting/utils'\r\nconst printStyle = `\r\n        .title {\r\n          text-align: center;\r\n        }\r\n        .is--print{\r\n          box-sizing: border-box;\r\n          width:95% !important;\r\n          margin:0 auto !important;\r\n        }\r\n        .my-list-row {\r\n          display: inline-block;\r\n          width: 100%;\r\n          margin-left:3%;\r\n        }\r\n        .my-list-row-first {\r\n          margin-bottom: 10px;\r\n        }\r\n        .my-list-col {\r\n          width:30%;\r\n          display: inline-block;\r\n          float: left;\r\n          margin-right: 1%;\r\n          word-wrap:break-word;\r\n          word-break:normal;\r\n        }\r\n        .left{\r\n          flex:1;\r\n        }\r\n        .my-top {\r\n          display:flex;\r\n          font-size: 12px;\r\n          margin-bottom: 5px;\r\n        }\r\n        .qrcode{\r\n          margin-right:10px\r\n        }\r\n        .cs-img{\r\n          position:relative;\r\n          right:30px\r\n        }\r\n        `\r\n\r\nexport default {\r\n  name: 'PROTaskListDetail',\r\n  components: {\r\n    TransferDetail,\r\n    QrcodeVue\r\n  },\r\n  mixins: [getTbInfo],\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      queryForm: {\r\n        // Project_Id: '',\r\n        // Area_Id: '',\r\n        // InstallUnit_Id: '',\r\n        Next_Process_Id: '',\r\n        Next_Team_Id: ''\r\n      },\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: -1\r\n      },\r\n      tbConfig: {\r\n        Op_Width: 120\r\n      },\r\n      tbLoading: false,\r\n      columns: [],\r\n      multipleSelection: [],\r\n      tbData: [],\r\n      finishList: [],\r\n      processOption: [],\r\n      groupOption: [],\r\n      total: 0,\r\n      printColumns: [],\r\n      search: () => ({}),\r\n      pageType: '',\r\n      info: {\r\n        Task_Code: '',\r\n        Project_Name: '',\r\n        Area_Name: '',\r\n        InstallUnit_Name: '',\r\n        Schduling_Code: '',\r\n        Task_Finish_Date: '',\r\n        Finish_Date2: '',\r\n        Order_Date: '',\r\n        Working_Team_Name: '',\r\n        Working_Process_Name: ''\r\n      },\r\n      printConfig: {\r\n        sheetName: '任务单详情',\r\n        style: printStyle,\r\n        beforePrintMethod: ({ content }) => {\r\n          return this.topHtml + content\r\n        }\r\n      },\r\n      Tenant_Code: localStorage.getItem('tenant')\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters('tenant', ['isVersionFour']),\r\n    isCom() {\r\n      return this.pageType === getBomCode('-1')\r\n    },\r\n    isUnitPart() {\r\n      return checkIsUnitPart(this.pageType)\r\n    },\r\n    isPart() {\r\n      return this.pageType === getBomCode('0')\r\n    }\r\n  },\r\n  async mounted() {\r\n    this.pageType = this.$route.query.type\r\n    this.workTeamId = this.$route.query.tid\r\n    this.taskCode = this.$route.query.id\r\n    this.info = JSON.parse(decodeURIComponent(this.$route.query.other))\r\n    await this.getTableConfig(this.isCom ? 'PROComTaskListDetail' : this.isUnitPart ? 'PROUnitPartTaskListDetail' : 'PROPartTaskListDetail')\r\n    if (this.isCom) {\r\n      const idx = this.columns.findIndex((item) => item.Code === 'Part_Code')\r\n      idx !== -1 && this.columns.splice(idx, 1)\r\n\r\n      this.printColumns = this.columns.filter(column => column.Code !== 'Comp_Description')\r\n    }\r\n    if (this.isUnitPart) {\r\n      this.printColumns = this.columns.filter(column => column.Code !== 'Project_Name' && column.Code !== 'Area_Name' && column.Code !== 'Finish_Count' && column.Code !== 'Finish_Weight' && column.Code !== 'Comp_Description')\r\n    }\r\n    if (this.isPart) {\r\n      this.printColumns = this.columns.filter(column => column.Code !== 'Project_Name' && column.Code !== 'Area_Name' && column.Code !== 'Finish_Count' && column.Code !== 'Finish_Weight' && column.Code !== 'Comp_Description')\r\n    }\r\n    // else {\r\n    //   const idx = this.columns.findIndex((item) => item.Code === 'Comp_Code')\r\n    //   idx !== -1 && this.columns.splice(idx, 1)\r\n    // }\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    this.fetchData()\r\n    this.getProcessOption()\r\n    this.getAllTeamOption()\r\n    this.getHtml()\r\n  },\r\n  methods: {\r\n    tbSelectChange(array) {\r\n      this.multipleSelection = array.records\r\n    },\r\n    // 导出\r\n    handleExport() {\r\n      console.log(this.info)\r\n      ExportTaskCodeDetails({\r\n        Process_Type: this.isCom ? 2 : this.isPart ? 1 : 3, // 1零件，2构件\r\n        Working_Team_Id: this.workTeamId,\r\n        Task_Code: this.taskCode\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '导出成功',\r\n            type: 'success'\r\n          })\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      // const filterVal = this.columns.map((v) => v.Code)\r\n      // const data = formatJson(filterVal, this.multipleSelection)\r\n      // const header = this.columns.map((v) => v.Display_Name)\r\n      // import('@/vendor/Export2Excel').then((excel) => {\r\n      //   excel.export_json_to_excel({\r\n      //     header: header,\r\n      //     data,\r\n      //     filename: `${\r\n      //       this.info?.Task_Code ||\r\n      //       (this.isCom ? '构件任务单详情' : '零件任务单详情')\r\n      //     }`,\r\n      //     autoWidth: true,\r\n      //     bookType: 'xlsx'\r\n      //   })\r\n      // })\r\n      // function formatJson(filterVal, jsonData) {\r\n      //   return jsonData.map((v) => filterVal.map((j) => v[j]))\r\n      // }\r\n    },\r\n    fetchData(page) {\r\n      page && (this.queryInfo.Page = page)\r\n      console.log(this.queryInfo, 'this.queryInfo')\r\n      this.tbLoading = true\r\n      GetTeamTaskDetails({\r\n        // ...this.queryInfo,\r\n        Bom_Level: this.pageType,\r\n        Page: -1,\r\n        PageSize: -1,\r\n        Process_Type: this.isCom ? 2 : this.isPart ? 1 : 3, // 1零件，2构件\r\n        Working_Team_Id: this.workTeamId,\r\n        Task_Code: this.taskCode,\r\n        Next_Team_Id: this.queryForm.Next_Team_Id,\r\n        Next_Process_Id: this.queryForm.Next_Process_Id\r\n      })\r\n        .then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.tbData = res.Data.Data.filter(item => {\r\n              return item.Allocation_Count !== 0\r\n            })\r\n            // this.total = res.Data.TotalCount\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n        .finally((_) => {\r\n          this.tbLoading = false\r\n        })\r\n    },\r\n    handleReset() {\r\n      this.$refs['form'].resetFields()\r\n      this.search(1)\r\n    },\r\n    getProcessOption() {\r\n      GetProcessList({\r\n        type: this.isCom ? 1 : this.isPart ? 2 : 3, // 1构件，2零件\r\n        Bom_Level: this.pageType\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.processOption = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getAllTeamOption() {\r\n      GetWorkingTeamsPageList({\r\n        pageInfo: { Page: -1, PageSize: -1, ParameterJson: [] }\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.groupOption = res.Data.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getHtml() {\r\n      // ${this.printConfig.sheetName}\r\n      const qr = this.$refs['qrcodeRef']\r\n      return new Promise((resolve, reject) => {\r\n        this.$nextTick(_ => {\r\n          const canvas = qr.$refs['qrcode-vue']\r\n          const dataURL = canvas.toDataURL('image/png')\r\n          this.topHtml = `\r\n        <h1 class=\"title\">#${this.info.Working_Process_Name || ''}# 加工任务单</h1>\r\n        <div class=\"my-top\">\r\n          <div class=\"left\">\r\n            <div class=\"my-list-row my-list-row-first\">\r\n              <div class=\"my-list-col\">项目名称/区域：${this.info.Project_Name || ''}/${this.info.Area_Name || ''}</div>\r\n              <div class=\"my-list-col\">排产单号：${this.info.Schduling_Code || ''}</div>\r\n              <div class=\"my-list-col\">加工班组：${this.info.Working_Team_Name || ''}</div>\r\n            </div>\r\n            <div class=\"my-list-row\">\r\n              <div class=\"my-list-col\">任务下单时间：${this.info.Order_Date || ''}</div>\r\n              <div class=\"my-list-col\">任务单号：${this.info?.Task_Code || ''}</div>\r\n              <div class=\"my-list-col\">工序要求完成时间：${this.info.Process_Finish_Date || ''}</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"right\">\r\n           <img class=\"cs-img\" src=\"${dataURL}\" alt=\"\">\r\n          </div>\r\n        </div>\r\n        `\r\n          resolve()\r\n        })\r\n      })\r\n    },\r\n    printEvent() {\r\n      this.getHtml().then((_) => {\r\n        console.log(this.printConfig.sheetName, 'this.printConfig.sheetName')\r\n        this.$refs.xTable.print({\r\n          sheetName: this.printConfig.sheetName,\r\n          style: printStyle,\r\n          mode: 'selected',\r\n          columns: this.printColumns.map((v) => {\r\n            return {\r\n              field: v.Code\r\n            }\r\n          }),\r\n          beforePrintMethod: ({ content }) => {\r\n            // 拦截打印之前，返回自定义的 html 内容\r\n            const result = this.topHtml + content\r\n            console.log('result', result)\r\n            return result\r\n          }\r\n        })\r\n      })\r\n    },\r\n    handleView(row) {\r\n    },\r\n    getInnerTable(row, column) {\r\n      const ob = {\r\n        num: 1,\r\n        date: '2022-23-52'\r\n      }\r\n      for (let i = 0; i < 50; i++) {\r\n        this.finishList.push(ob)\r\n      }\r\n    },\r\n    toBack() {\r\n      closeTagView(this.$store, this.$route)\r\n    },\r\n\r\n    // 关闭弹窗\r\n    handleClose() {\r\n      this.$refs.TransferDetail.resetForm()\r\n      this.dialogVisible = false\r\n    },\r\n\r\n    // 单元格点击时间\r\n    cellClickEvent({ row, rowIndex, column, columnIndex }) {\r\n      // if (column.property === \"Finish_Count\" && row.Finish_Count > 0) {\r\n      //   this.dialogVisible = true;\r\n      //   this.$nextTick(() => {\r\n      //     this.$refs.TransferDetail.init(row, this.isCom);\r\n      //   });\r\n      // }\r\n    },\r\n\r\n    // 改变单元格样式\r\n    cellClassName({ row, rowIndex, column, columnIndex }) {\r\n      // if (column.property === \"Finish_Count\") {\r\n      //   return \"col-blue\";\r\n      // }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.el-divider {\r\n  margin: 0 0 10px;\r\n}\r\n\r\n.tb-x {\r\n  flex: 1;\r\n  overflow:auto;\r\n\r\n  ::v-deep {\r\n    .cs-vxe-table .vxe-body--column.col-blue {\r\n      color: #298dff;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n.cs-z-flex-pd16-wrap {\r\n  padding-top: 50px;\r\n\r\n  .top-btn {\r\n    position: absolute;\r\n    top: 12px;\r\n    left: 20px;\r\n    z-index: 99;\r\n\r\n    .el-button {\r\n      background-color: #f7f8f9;\r\n    }\r\n  }\r\n\r\n  .cs-z-page-main-content {\r\n    overflow:hidden;\r\n    ::v-deep {\r\n      .el-form-item__content {\r\n        min-width: 200px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
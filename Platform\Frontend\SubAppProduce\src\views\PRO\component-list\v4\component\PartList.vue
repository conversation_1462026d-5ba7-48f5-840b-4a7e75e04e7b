<template>
  <div class="content">
    <div class="top-info">
      <div class="top-info-title">{{ SteelName }}</div>
      <div>需求零件总数：<span>{{ steelTotalNum }}</span> 件 需求零件总重：<span>{{ steelTotalWeight }}</span> kg</div>
    </div>
    <div class="tb-container">
      <vxe-table
        v-loading="tbLoading"
        :empty-render="{name: 'NotData'}"
        show-header-overflow
        element-loading-spinner="el-icon-loading"
        element-loading-text="拼命加载中"
        empty-text="暂无数据"
        class="cs-vxe-table"
        height="500"
        align="left"
        stripe
        :data="tbData"
        resizable
        :tree-config="{transform: true, rowField: 'Id', parentField: 'ParentId'}"
        :tooltip-config="{ enterable: true}"
      >
        <template v-for="(item,idx) in columns">
          <vxe-column
            :key="item.Code"
            :tree-node="idx===0"
            :min-width="item.Min_Width"
            :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
            show-overflow="tooltip"
            sortable
            :align="item.Align"
            :field="item.Code"
            :title="item.Display_Name"
          >
            <template #default="{ row }">
              <template v-if="item.Code === 'Code'">
                <el-tag v-if="row.Is_Change" style="margin-right: 8px;" type="danger">变</el-tag>
                <el-tag v-if="row.stopFlag" style="margin-right: 8px;" type="danger">停</el-tag>
                <span>{{ row.Code }}</span>
                <span style="margin-left: 5px;">
                  <template v-for="firstName in getUnitPartGradeList(row)">
                    <el-tag v-if="row.Part_Grade>0" :key="firstName">{{ firstName }}</el-tag>
                  </template>
                  <el-tag v-if="row.Part_Grade===0" type="success">{{ getPartGradeName() }}</el-tag>
                </span>
              </template>
              <span v-else-if="item.Code === 'SchedulingNum'">
                {{ row.SchedulingNum ? row.SchedulingNum : '-' }}
              </span>
              <span v-else-if="item.Code === 'Producting_Count'">
                {{ row.Producting_Count ? row.Producting_Count : '-' }}
              </span>
              <span v-else-if="item.Code === 'Is_Main'">
                <span v-if="row.Part_Grade===1">-</span>
                <span v-else>
                  <el-tag
                    :type="row.Is_Main ? 'success' : 'danger'"
                  >{{ row.Is_Main ? "是" : "否" }}
                  </el-tag>
                </span>
              </span>
              <span v-else-if="item.Code === 'SH'">
                <div v-if="row.SH==0">/</div>
                <div v-else>
                  <el-button type="text" @click="handleView(row)">查看</el-button>
                </div>
              </span>
              <span v-else>
                {{ row[item.Code] | displayValue }}
              </span>
            </template>
          </vxe-column>
        </template>
      </vxe-table>
    </div>
    <!--    <div v-loading="tbLoading" class="tb-container">
      <dynamic-data-table
        ref="dyTable"
        :columns="columns"
        :config="tbConfig"
        :data="tbData"
        :page="queryInfo.Page"
        :total="total"
        border
        stripe
        class="cs-plm-dy-table"
      >
        <template slot="SchedulingNum" slot-scope="{ row }">
          <div>
            {{ row.SchedulingNum ? row.SchedulingNum : '-' }}
          </div>
        </template>
        <template slot="Is_Main" slot-scope="{ row }">
          <div>
            {{ row.Is_Main==true ? '是' : '否' }}
          </div>
        </template>
        <template slot="SH" slot-scope="{ row }">
          <div v-if="row.SH==0">/</div>
          <div v-else>
            <el-button type="text" @click="handleView(row)">查看</el-button>
          </div>
        </template>
      </dynamic-data-table>
    </div>-->
  </div>
</template>

<script>
import { GetPartListWithComponent } from '@/api/PRO/component'
import getTbInfo from '@/mixins/PRO/get-table-info'
import DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable.vue'
import numeral from 'numeral'
import { GetStopList } from '@/api/PRO/production-task'
import { GetBOMInfo } from '@/views/PRO/bom-setting/utils'
export default {
  components: {
    DynamicDataTable
  },
  mixins: [getTbInfo],
  props: {
    projectId: {
      type: String,
      default: ''
    },
    sysProjectId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tbLoading: false,
      tbConfig: {
        Is_Page: false,
        Height: 600
      },
      queryInfo: {
        Page: 1,
        PageSize: 10
      },
      columns: [],
      steelTotalNum: 0,
      steelTotalWeight: 0,
      total: 0,
      tbData: [],
      rowId: 0,
      SteelName: '',
      firstNameList: []
    }
  },
  mounted() {

  },
  methods: {
    async init(rowData) {
      const { comName, list } = await GetBOMInfo()
      const allName = list.map(item => item.Display_Name).join('/')
      this.firstNameList = list
      console.log('this.firstNameList', this.firstNameList)
      this.columns = [
        { Code: 'Code', Display_Name: `${allName}名称`, Is_Frozen: true, Frozen_To: 'left', Min_Width: 180, Is_Display: true, Sort: 1 },
        { Code: 'Spec', Display_Name: '规格', Min_Width: 160, Is_Display: true, Sort: 2 },
        { Code: 'Length', Display_Name: '长度', Min_Width: 160, Is_Display: true, Sort: 3 },
        { Code: 'Component_Code', Display_Name: `所属${comName}`, Min_Width: 140, Is_Display: true, Sort: 4 },
        { Code: 'Num', Display_Name: '需求数量', Min_Width: 120, Is_Display: true, Sort: 5 },
        { Code: 'SchedulingNum', Display_Name: '排产数量', Min_Width: 120, Is_Display: true, Sort: 6 },
        { Code: 'Producting_Count', Display_Name: '生产中数量', Min_Width: 140, Is_Display: true, Sort: 6 },
        { Code: 'Weight', Display_Name: '单重（kg）', Min_Width: 120, Is_Display: true, Sort: 7 },
        { Code: 'Total_Weight', Display_Name: '总重（kg）', Min_Width: 120, Is_Display: true, Sort: 8 },
        { Code: 'Is_Main', Display_Name: '是否主零件', Min_Width: 140, Is_Display: true, Sort: 10 },
        { Code: 'Remark', Display_Name: '备注', Min_Width: 160, Is_Display: true, Sort: 11 },
        { Code: 'SH', Display_Name: '深化资料', Is_Frozen: true, Frozen_To: 'right', Min_Width: 120, Is_Display: true, Sort: 12 }
      ]
      console.log('rowData', JSON.parse(JSON.stringify(rowData)))
      this.rowId = rowData.Id
      this.SteelName = rowData.SteelName
      this.tbLoading = true

      GetPartListWithComponent({ id: rowData.Id }).then(async res => {
        if (res.IsSucceed) {
          this.tbData = res.Data
          let _num = 0
          let _weight = 0
          res.Data.forEach(item => {
            if (item.Part_Grade !== 1) {
              _num += item.Num
              _weight += item.Total_Weight
            }
          })
          this.steelTotalNum = numeral(_num).format('0.[00]')
          this.steelTotalWeight = numeral(_weight).format('0.[00]')
          await this.getStopList()
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
        this.tbLoading = false
      })
    },
    async getStopList() {
      const submitObj = this.tbData.map(item => {
        return {
          Id: item.Part_Aggregate_Id,
          Type: item.Part_Grade === 0 ? 1 : 3
        }
      })
      await GetStopList(submitObj).then(res => {
        if (res.IsSucceed) {
          const stopMap = {}
          res.Data.forEach(item => {
            stopMap[item.Id] = item.Is_Stop !== null
          })
          this.tbData.forEach(row => {
            if (stopMap[row.Part_Aggregate_Id]) {
              this.$set(row, 'stopFlag', stopMap[row.Part_Aggregate_Id])
            }
          })
        }
      })
    },
    handleView(row) {
      console.log('row', row)
      this.$emit('checkSteelMeans', row)
    },
    getUnitPartGradeList(row) {
      const item = this.firstNameList.find(item => +item.Code === row.Part_Grade)
      return item.Display_Name[0]
    },
    getPartGradeName() {
      const item = this.firstNameList.find(item => +item.Code === 0)
      return item.Display_Name[0]
    }
  }
}
</script>

<style scoped lang="scss">
.content{
  display: flex;
  flex-direction: column;
  .top-info{
    font-size: 14px;
    span {
      color: #00C361
    }
    .top-info-title{
      margin-bottom: 8px;
      font-size: 24px;
      font-weight: bold;
    }
  }
  .tb-container{
    margin-top: 20px;
    flex: 1;
  }
}
</style>

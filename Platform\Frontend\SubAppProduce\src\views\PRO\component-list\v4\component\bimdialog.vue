<template>
  <el-dialog
    class="plm-custom-dialog"
    :title="title"
    :visible.sync="dialogVisible"
    width="570px"
    top="5vh"
    :loading="loading"
    @submitbtn="handleSubmit('form')"
    @cancelbtn="handleClose"
    @handleClose="handleClose"
    @close="handleClose"
  >
    <div class="cs-alert">
      <i class="el-icon-warning-outline" />注意：请先<el-button type="text" @click="getTemplate">下载构件导入模板</el-button>
    </div>
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <!--      <el-form-item v-if="!isVersionFour" label="下载模板" prop="Template_Type">
        <el-radio-group v-model="form.Template_Type" @input="radioChange">
          <el-radio :label="2">固定模板</el-radio>
          <el-radio :label="1">动态模板</el-radio>
        </el-radio-group>
      </el-form-item>-->
      <el-form-item label="导入方式" prop="areaType">
        <el-radio-group v-model="areaType">
          <el-radio :label="2">多区域导入</el-radio>
          <el-radio :label="1">单区域导入</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否跳过生产" prop="Is_Skip_Production">
        <el-radio-group v-model="form.Is_Skip_Production">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="项目名称" prop="Project_Name">
        <el-input v-model="form.Project_Name" style="width: 360px" disabled />
      </el-form-item>
      <el-form-item v-if="areaType===1" label="区域" prop="Area_Name">
        <el-input v-model="form.Area_Name" style="width: 360px" disabled />
      </el-form-item>
      <el-form-item label="类别名称" prop="Type_Name">
        <el-input v-model="form.Type_Name" style="width: 360px" disabled />
      </el-form-item>
      <el-form-item label="标题" prop="Doc_Title">
        <el-input v-model="form.Doc_Title" style="width: 360px" />
      </el-form-item>
      <el-form-item label="简要描述" prop="Doc_Content">
        <el-input v-model="form.Doc_Content" style="width: 360px" />
      </el-form-item>
      <el-form-item label="附件信息" prop="Doc_File">
        <el-input v-model="form.Doc_File" style="width: 360px" disabled />
      </el-form-item><!--      <el-form-item
        v-if="!isVersionFour&&form.Type === 1&&!isDynamicTemplate"
        :rules=" [
          { required: true, message: '请选择', trigger: 'change' }
        ]"
        label="自动拆分直发件"
        prop="Is_Auto_Split"
      >
        <el-radio-group v-model="form.Is_Auto_Split" :disabled="[true,false].includes(isAutoSplit)">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
      </el-form-item>-->

      <el-form-item label="上传附件">
        <OSSUpload
          ref="company"
          drag
          class="upload-demo"
          :action="$store.state.uploadUrl"
          :on-change="handleChange"
          :before-upload="beforeUpload"
          :file-list="fileList"
          :limit="2"
          :on-success="uploadSuccess"
          :on-error="uploadError"
          :before-remove="beforeRemove"
          :on-remove="handleRemove"
          :multiple="false"
          :accept="allowFile"
        >
          <!-- :on-exceed="onExceed" -->
          <i class="el-icon-upload" />
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        </OSSUpload>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button
        type="primary"
        :loading="btnLoading"
        @click="handleSubmit()"
      >确 定</el-button>
    </span>
  </el-dialog>

</template>

<script>
import OSSUpload from '@/views/plm/components/ossupload.vue'
import {
  GenerateDeepenFileFromDirect,
  UpdatePartAggregateId, AppendImportDeepFiles, ThreeBomImportTemplate
} from '@/api/PRO/component'
import { combineURL } from '@/utils'
import { mapGetters } from 'vuex'
const form = {
  Id: '',
  Is_Auto_Split: undefined,
  Doc_Catelog: '',
  Doc_Type: '',
  Project_Name: '',
  Project_Id: '',
  Sys_Project_Id: '',
  Area_Name: '',
  Area_Id: '',
  Type_Name: '',
  Doc_Title: '',
  Doc_Content: '',
  IsChanged: false,
  Is_Load: false,
  Doc_File: '',
  ishistory: true,
  Is_Skip_Production: false,
  ProfessionalCode: '',
  Type: 0,
  Bom_Level: '-1',
  Template_Type: 2 // 1：动态模板，2：固定模板
}
export default {
  components: { OSSUpload },
  props: {
    typeEntity: {
      type: Object,
      default: () => {}
    },
    isAutoSplit: {
      type: [Boolean, undefined],
      default: undefined
    }
  },
  computed: {
    ...mapGetters('tenant', ['isVersionFour'])
  },
  data() {
    return {
      isDynamicTemplate: false,
      btnLoading: false,
      type: '',
      areaType: 2,
      allowFile: 'image/*,video/*,.txt,.pdf,.xlsx,.xls,.docx,.doc,.zip,.rar,.dwg,.nwd,.rvt,.ifc,.bzip,.bzip2',
      fileList: [],
      dialogVisible: false,
      title: '上传文件',
      loading: false,
      form: { ...form },
      attachments: [],
      rules: {
        Doc_Title: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ]
        // Is_Auto_Split: [
        //   { required: true, message: '请选择', trigger: 'change' }
        // ]
      },
      fileType: '',
      curFile: '',
      bimvizId: '',
      isDeep: false,
      projectId: '',
      isSHQD: '',
      command: 'cover'
    }
  },
  watch: {
    isAutoSplit(newValue, oldValue) {
      this.$set(this.form, 'Is_Auto_Split', newValue)
    }
  },
  mounted() {
    this.$set(this.form, 'Is_Auto_Split', this.isAutoSplit)
  },
  created() {
    this.fileType = this.$route.name
  },

  methods: {
    onExceed() {
      this.$message.error('只能上传一个文件')
    },
    getTemplate() {
      console.log(this.form.Type, 'this.form.Type')
      console.log(this.form.Template_Type, 'form.Template_Type')
      const query = { ProfessionalCode: this.form.ProfessionalCode, Type: this.form.Type }
      // if (this.form.Type === 1) {
      query.Template_Type = this.form.Template_Type
      // }
      console.log(query, 'query=======')
      ThreeBomImportTemplate({ }).then(res => {
        if (res.IsSucceed) {
          window.open(combineURL(this.$baseUrl, res.Data), '_blank')
        // this.downFile(res.Data)
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    // 通过文件下载url拿到对应的blob对象
    getBlob(url) {
      return new Promise(resolve => {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', url, true)
        xhr.responseType = 'blob'
        xhr.onload = () => {
          if (xhr.status === 200) {
            resolve(xhr.response)
          }
        }

        xhr.send()
        console.log(xhr)
      })
    },
    // 下载文件 　　js模拟点击a标签进行下载
    saveAs(blob, filename) {
      var link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = filename
      link.click()
    },
    // 文件下载
    downFile(fileUrl) {
      this.getBlob(fileUrl).then(blob => {
        this.saveAs(blob, '信用权证使用导入模板件名.xlsx')
      })
    },

    handleChange(file, fileList) {
      this.fileList = fileList.slice(-1)
      if (fileList.length > 1) {
        this.attachments.splice(-1)
        this.form.Doc_File = ''
        this.form.Doc_Content = ''
        this.form.Doc_Title = ''
      }
    },
    beforeUpload(file) {
      this.curFile = file
      console.log('beforeFile', file)
      this.loading = true
      this.btnLoading = true
    },
    beforeRemove(file) {
      return this.$confirm(`确定移除 ${file.name}？`)
    },
    handleRemove(file, fileList) {
      let i = 0
      this.fileList.filter((item, index) => {
        if (item.name === file.name) {
          i = index
        }
      })
      this.fileList.splice(i, 1)
      this.attachments.splice(i, 1)
      this.form.Doc_File = this.form.Doc_File.replace(file.name, '')
      this.form.Doc_Content = this.form.Doc_File.replace(file.name, '')
      this.form.Doc_Title = this.form.Doc_File.replace(file.name, '')
      console.log('fileList', fileList)
      this.loading = !fileList.every((item) => item.status === 'success')
      setTimeout(() => {
        this.btnLoading = !fileList.every((item) => item.status === 'success')
      }, 1000)
    },
    uploadError(err, file, fileList) {
      this.$message.error(`${file.name}上传失败`)
    },
    uploadSuccess(response, file, fileList) {
      console.log('response', response)
      console.log('uploadSuccess', file)
      console.log('uploadSuccessList', fileList)
      this.fileList = fileList
      this.attachments.push(
        {
          File_Url: response.Data.split('*')[0],
          File_Size: response.Data.split('*')[1],
          File_Type: response.Data.split('*')[2],
          File_Name: response.Data.split('*')[3]
        }
      )
      const title = this.form.Doc_Title + (this.form.Doc_Title ? ',' : '') + response.Data.split('*')[3]
      this.form.Doc_Title = title.substring(0, title.lastIndexOf('.'))
      this.form.Doc_Content = this.form.Doc_Title
      this.form.Doc_File = this.form.Doc_File + (this.form.Doc_File ? ',' : '') + response.Data.split('*')[3]
      this.loading = !fileList.every((item) => item.status === 'success')
      setTimeout(() => {
        this.btnLoading = !fileList.every((item) => item.status === 'success')
      }, 1000)
    },
    // isDeep是否是从构件管理打开的(深化)
    handleOpen(type, row, bimvizId, isDeep = false, projectId, importType, productionConfirm, command = 'cover', customParams) {
      this.projectId = projectId
      this.isDeep = isDeep
      this.form = Object.assign(this.form, form)

      this.form.Type_Name = row.name
      this.form.Doc_Type = row.Id
      this.form.Doc_Catelog = row.Catalog_Code
      this.isSHQD = row.isSHQD
      this.form.ProfessionalCode = row.Code
      this.dialogVisible = true
      this.type = type
      this.bimvizId = bimvizId
      this.form.Type = importType
      // this.form.Is_Skip_Production = (productionConfirm === '' ? false : productionConfirm)
      this.command = command
      console.log(command, 'command========')
      this.form.Project_Name = customParams.Project_Name
      this.form.Sys_Project_Id = customParams.Sys_Project_Id
      this.form.Area_Name = customParams.Area_Name
      this.form.Area_Id = customParams.Area_Id
      this.isDynamicTemplate = false
      this.Template_Type = 2

      if (this.type === 'add') {
        this.fileList = []
        // this.title = '新增文件'
        this.form.Id = ''
        // this.$delete(this.form, "Id");
      }
      // importType  1:增量导入，2：覆盖导入 3：部分覆盖
      if (this.command === 'cover') {
        this.title = '覆盖文件'
        this.form.ImportType = 2
      } else if (this.command === 'add') {
        this.title = '新增文件'
        this.form.ImportType = 1
      } else if (this.command === 'halfcover') {
        this.title = '部分覆盖导入'
        this.form.ImportType = 3
      }
      this.$set(this.form, 'Is_Auto_Split', this.isAutoSplit)
    },
    handleClose() {
      try {
        this.attachments = []
        this.$refs['form'].resetFields()
        this.btnLoading = false
        this.loading = false
        this.fileList = []
        this.dialogVisible = false
      } catch (e) {

      }
    },
    handleSubmit(IsOk = false) {
      this.$refs['form'].validate(async(valid) => {
        if (valid) {
          this.loading = true
          this.btnLoading = true
          // this.$delete(this.form, 'Type_Name')
          this.updateInfo(IsOk)
        } else {
          this.$message({
            message: '请将表单填写完整',
            type: 'warning'
          })
          return false
        }
      })
    },

    async updateInfo(IsOk) {
      // Type 1零构件 0 构件
      const form = { ...this.form, IsOk }
      // if (form.Type === 0) {
      //   delete form['Template_Type']
      // }
      console.log(form, 'form=========')
      // if (form.Is_Auto_Split && form.Type === 1) {
      //   this.getSplitInfo().then(() => {
      //     this.updatePartAggregateId()
      //   })
      //   return
      // }
      this.submitAdd(form)
      // if (this.command === 'cover') {
      //   console.log(this.command, 'command========cover')
      //   this.submitCoverAdd(form)
      // } else if (this.command === 'add') {
      //   console.log(this.command, 'command========add')
      //   this.submitAdd(form)
      // } else if (this.command === 'halfcover') {
      //   console.log(this.command, 'command========add')
      //   this.submitCoverAdd(form)
      // }
    },

    async submitCoverAdd(form) {
      try {
        const res = await AppendImportDeepFiles({ ...form, AttachmentList: this.attachments })
        if (res.IsSucceed) {
          this.$message({
            message: '保存成功',
            type: 'success'
          })
          await this.updatePartAggregateId() // 确保在保存成功后执行
          this.$emit('getData', this.form.Doc_Type)
          this.$emit('getProjectAreaData')
          this.handleClose()
        } else {
          res.Data && window.open(combineURL(this.$baseUrl, res.Data), '_blank')
          this.$message.error(res.Message)
        }
      } catch (error) {
        this.$message.error('保存失败')
      } finally {
        this.loading = false
        this.btnLoading = false
      }
    },

    async submitAdd(form) {
      try {
        const _form = { ...form }
        if (this.areaType === 2) {
          _form.Area_Id = undefined
          _form.Area_Name = undefined
        }
        const res = await AppendImportDeepFiles({ ..._form, AttachmentList: this.attachments })

        if (res.IsSucceed) {
          if (!res.Data) {
            this.$message({
              message: '保存成功',
              type: 'success'
            })
            await this.updatePartAggregateId() // 确保在保存成功后执行
            this.$emit('getData', this.form.Doc_Type)
            this.$emit('getProjectAreaData')
            this.handleClose()
          } else {
            this.$confirm(res.Data, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.handleSubmit(true)
            }).catch(() => {
              this.$message({
                type: 'info',
                message: '已取消'
              })
            })
          }
        } else {
          res.Data && window.open(combineURL(this.$baseUrl, res.Data), '_blank')
          this.$message.error(res.Message)
        }
      } catch (error) {
        this.$message.error('保存失败')
      } finally {
        this.loading = false
        this.btnLoading = false
      }
    },
    getSplitInfo() {
      const { ProfessionalCode,
        Type,
        Is_Skip_Production,
        Sys_Project_Id,
        Area_Id
      } = this.form
      const obj = {
        'ProfessionalCode': ProfessionalCode,
        'Type': Type,
        'Is_Skip_Production': Is_Skip_Production,
        'Sys_Project_Id': Sys_Project_Id,
        'Area_Id': Area_Id,
        'AttachmentList': this.attachments,
        'Is_Auto_Split': true
      }
      GenerateDeepenFileFromDirect(obj).then(res => {
        if (res.IsSucceed) {
          this.open(res.Data)
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },

    async  updatePartAggregateId() {
      console.log('更新成功=========')
      await UpdatePartAggregateId({ AreaId: this.form.Area_Id }).then((res) => {
        if (!res.IsSucceed) {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    open(url) {
      const h = this.$createElement
      let fileName = ''
      const match = url.match(/\/([^/]+\.xls)$/)
      if (match) {
        fileName = match[1]
      }
      const form = { ...this.form }
      // if (form.Type === 0) {
      //   delete form['Template_Type']
      // }
      this.$msgbox({
        title: '提示',
        message: h('div', null, [
          h('div', null, '清单已拆分完成, 是否确定导入?'),
          h('a', {
            attrs: {
              href: combineURL(this.$baseUrl, url),
              target: '_blank',
              style: 'color: #298DFF'
            }
          }, fileName)
        ]),
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        beforeClose: async(action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.confirmButtonText = '提交...'
            // if (this.command === 'cover') {
            //   await this.submitCoverAdd(form)
            // } else if (this.command === 'add') {
            //   await this.submitAdd(form)
            // }
            await this.submitAdd(form)
            done()
            setTimeout(() => {
              instance.confirmButtonLoading = false
            }, 300)
          } else {
            this.loading = false
            this.btnLoading = false
            done()
          }
        }
      }).then(action => {

      })
    },
    radioChange(val) {
      if (val === 1) {
        this.isDynamicTemplate = true
        this.form.Is_Auto_Split = undefined
      } else {
        this.isDynamicTemplate = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .cs-alert {
    position: relative;
    height: 38px;
    line-height: 38px;
    color: #F5C15A;
    border-radius: 4px;
    margin-bottom: 30px;

    &-info {
      color: #298DFF;
    }

    .el-icon-warning-outline {
      margin-left: 16px;
    }

    &:after {
      content: '';
      top: 0;
      left: 0;
      position: absolute;
      width: 100%;
      height: 100%;
      background: #F5C15A;
      opacity: 0.12;
      pointer-events: none;
    }
  }
</style>

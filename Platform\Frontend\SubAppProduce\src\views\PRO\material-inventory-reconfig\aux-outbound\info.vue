<template>
  <div class="app-container abs100">
    <el-card
      class="box-card"
      :style="isRetract ? 'height: 110px; overflow: hidden;' : ''"
    >
      <!-- <h3 style="margin-bottom: 20px">出库单信息</h3> -->
      <div
        class="toolbar-container"
        style="
          margin-bottom: 10px;
          padding-bottom: 10;
          border-bottom: 1px solid #d0d3db;
        "
      >
        <div class="toolbar-title"><span />出库单信息</div>
        <div class="retract-container" @click="handleRetract">
          <el-button type="text">{{ isRetract ? "展开" : "收起" }}</el-button>
          <el-button
            type="text"
            :icon="isRetract ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"
          />
        </div>
      </div>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="出库类型" prop="OutStoreType">
              <SelectMaterialStoreType v-model="form.OutStoreType" type="RawOutStoreType" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="出库日期" prop="OutStoreDate">
              <el-date-picker
                v-model="form.OutStoreDate"
                :disabled="isView|| isReturn"
                :picker-options="pickerOptions"
                value-format="yyyy-MM-dd"
                style="width: 100%"
                type="date"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="领料部门" prop="Pick_Department_Id">
              <el-select
                v-if="isProductweight === 'true'"
                v-model="form.Pick_Department_Id"
                style="width: 100%"
                placeholder="请选择"
                clearable
                filterable
                :disabled="isView|| isReturn"
                @change="departmentChange"
              >
                <el-option
                  v-for="item in departmentlist"
                  :key="item.Id"
                  :label="item.Display_Name"
                  :value="item.Id"
                />
              </el-select>
              <el-tree-select
                v-else
                ref="treeSelectDepart"
                v-model="form.Pick_Department_Id"
                :select-params="{
                  clearable: true,
                  disabled: isReturn
                }"
                class="cs-tree-x"
                :tree-params="treeParamsDepart"
                @select-clear="departmentChange"
                @node-click="departmentChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="领料人" prop="ReceiveUserId">
              <el-select
                v-model="form.ReceiveUserId"
                style="width: 100%"
                placeholder="请选择"
                clearable
                filterable
                :disabled="isView || !form.Pick_Department_Id|| isReturn"
                @change="userChange"
              >
                <el-option
                  v-for="item in factoryPeoplelist"
                  :key="item.Id"
                  :label="item.Display_Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col v-if="isPurchase" :span="6">
            <el-form-item label="领料班组" prop="WorkingTeamId">
              <el-select
                v-model="form.WorkingTeamId"
                style="width: 100%"
                placeholder="请选择"
                clearable
                filterable
                :disabled="isView"
              >
                <el-option
                  v-for="item in WorkingTeamList"
                  :key="item.Id"
                  :label="item.Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="isPurchase" :span="6">
            <el-form-item label="使用工序" prop="Use_Processing_Id">
              <el-select
                v-model="form.Use_Processing_Id"
                style="width: 100%"
                placeholder="请选择"
                clearable
                filterable
                :disabled="isView|| isReturn"
              >
                <el-option
                  v-for="item in ProcessList"
                  :key="item.Id"
                  :label="item.Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="6">
            <el-form-item label="领用部门" prop="Pick_Department_Id">
              <el-select
                v-model="form.Pick_Department_Id"
                filterable
                style="width: 100%"
                clearable
                placeholder="请选择"
                :disabled="isView"
                @change="departmentChange"
              >
                <el-option
                  v-for="item in departmentlist"
                  :key="item.Id"
                  :label="item.Display_Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item label="备注" prop="Remark">
              <el-input
                v-model="form.Remark"
                :disabled="isView|| isReturn"
                style="width: 100%"
                show-word-limit
                :rows="1"
                :maxlength="100"
                type="textarea"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col v-if="!isReturn" :span="6">
            <el-form-item label="附件" class="factory-img">
              <OSSUpload
                class="upload-demo"
                action="alioss"
                :limit="5"
                :multiple="true"
                :on-success="
                  (response, file, fileList) => {
                    uploadSuccess(response, file, fileList);
                  }
                "
                :on-remove="uploadRemove"
                :on-preview="handlePreview"
                :on-exceed="handleExceed"
                :show-file-list="true"
                :file-list="fileListData"
                :disabled="isView|| isReturn"
              >
                <el-button
                  v-if="!(isView|| isReturn)"
                  type="primary"
                  :disabled="isView|| isReturn"
                >上传文件</el-button>
              </OSSUpload>
            </el-form-item>
          </el-col>
          <el-col v-if="isReturn" :span="6">
            <el-form-item label="退库部门" prop="Return_Dept_Id">
              <SelectDepartment v-model="form.Return_Dept_Id" :disabled="isView" />
            </el-form-item>
          </el-col>
          <el-col v-if="isReturn" :span="6">
            <el-form-item label="退库人" prop="Return_Person_Id ">
              <SelectDepartmentUser v-model="form.Return_Person_Id" :department-id="form.Return_Dept_Id" />
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
    </el-card>
    <el-card class="box-card box-card-tb">
      <!-- <el-divider class="elDivder" /> -->
      <!-- <h4>出库单明细</h4> -->
      <div class="toolbar-container">
        <div class="toolbar-title"><span />出库单明细</div>
      </div>
      <el-divider class="elDivder" />

      <div style="display: flex; justify-content: space-between;align-items: center;">
        <div v-if="!isView&&!isReturn" style="display: flex;align-items: center;">
          <el-button
            type="primary"
            @click="openAddDialog(null)"
          >新增</el-button>
          <el-button
            type="danger"
            :disabled="!multipleSelection.length"
            @click="handleDelete"
          >删除</el-button>
          <el-button type="primary" :disabled="!multipleSelection.length" @click="batchDialogVisible = true">批量编辑领用项目</el-button>
          <PickSelect style="margin-left: 10px" :selected-list="rootTableData" :material-type="1" @addList="getAddList" />
        </div>
        <div style="margin-left: auto">
          <el-form
            ref="searchForm"
            inline
            :model="searchForm"
            label-width="80px"
          >
            <el-form-item label="辅料名称" prop="RawName">
              <el-input
                v-model="searchForm.RawName"
                style="width: 100%"
                placeholder="请输入"
                clearable
              />
            </el-form-item>

            <el-form-item label="规格" prop="Spec">
              <el-input
                v-model="searchForm.Spec"
                style="width: 100%"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
            <el-form-item label="所属项目" prop="SysProjectId">
              <el-select
                v-model="searchForm.SysProjectId"
                class="input"
                placeholder="所属项目"
                clearable
                filterable
              >
                <el-option
                  v-for="item in projectOptions"
                  :key="item.Id"
                  :label="item.Short_Name"
                  :value="item.Sys_Project_Id"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
            <DynamicTableFields
              title="表格配置"
              :table-config-code="gridCode"
              :manual-hide-columns="[{'Code':'PartyUnitName'}]"
              @updateColumn="changeColumn"
            />
          </el-form>
        </div>
      </div>
      <div class="tb-x">
        <component
          :is="currentTbComponent"
          v-if="currentTbComponent"
          ref="table"
          :is-return="isReturn"
          :is-view="isView"
          :big-type-data="BigType"
          @changeStandard="changeStandard"
          @updateTb="handleUpdateTb"
          @changeWarehouse="changeWarehouse"
          @select="setSelectRow"
        />
      </div>
      <el-divider v-if="!isView" class="elDivder" />
      <footer v-if="!isView">
        <div class="data-info">
          <el-tag
            v-if="!isReturn"
            size="medium"
            class="info-x"
          >已选{{ multipleSelection.length }}条数据
          </el-tag>
        </div>
        <div>
          <el-button @click="closeView">取消</el-button>
          <el-button v-if="isReturn" :loading="returning" type="primary" @click="handleReturn">确认退库</el-button>

          <template v-else>
            <el-button
              :loading="saveLoading"
              @click="saveDraft(1)"
            >保存草稿
            </el-button>
            <el-button type="primary" :loading="saveLoading" @click="handleSubmit">提交出库</el-button>
          </template>
        </div>
      </footer>
    </el-card>

    <el-dialog
      v-if="dialogVisible"
      v-dialogDrag
      class="plm-custom-dialog"
      :title="title"
      :visible.sync="dialogVisible"
      :width="dWidth"
      top="10vh"
      @close="handleClose"
    >
      <component
        :is="currentComponent"
        ref="content"
        :page-type="1"
        @close="handleClose"
        @warehouse="getWarehouse"
        @batchEditor="batchEditorFn"
        @importData="importData"
        @standard="getStandard"
        @refresh="fetchData"
      />
    </el-dialog>

    <el-dialog
      v-dialogDrag
      class="plm-custom-dialog"
      title="新增"
      :visible.sync="openAddList"
      width="70%"
      top="10vh"
      @close="handleClose"
    >
      <template v-if="openAddList">
        <add-purchase-list
          ref="draft"
          :big-type-data="BigType"
          :p-form="form"
          :joined-items="rootTableData"
          @getAddList="getAddList"
          @getRowName="getRowName"
          @close="handleClose"
        />
      </template>
    </el-dialog>

    <el-dialog
      v-dialogDrag
      class="plm-custom-dialog"
      title="批量编辑领用项目"
      :visible.sync="batchDialogVisible"
      top="10vh"
      width="350px"
      @close="closeBatchDialog"
    >
      <el-select
        v-model="batchProjectId"
        style="width: 300px"
        placeholder="请选择"
        clearable
        filterable
      >
        <el-option
          v-for="item in projectOptions"
          :key="item.Id"
          :label="item.Short_Name"
          :value="item.Sys_Project_Id"
        />
      </el-select>
      <p style="margin: 20px">
        <i>注：仅能批量编辑公共库存的领用项目</i>
      </p>
      <div style="text-align: right">
        <el-button @click="closeBatchDialog">取消</el-button>
        <el-button type="primary" @click="batchChangeProject">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { closeTagView, debounce, deepClone } from '@/utils'
import PurchaseTb from './components/PurchaseTb.vue'
import AddList from './components/AddList.vue'
import AddPurchaseList from './components/AddPurchaseList.vue'
import ImportFile from '../components/ImportFile.vue'
import Standard from './components/Standard.vue'
import { GetProjectPageList } from '@/api/PRO/project'
import OSSUpload from '@/views/PRO/components/ossupload'
import getCommonData from '@/mixins/PRO/get-common-data/index.js'
import { GetFirstLevelDepartsUnderFactory } from '@/api/OMA/nonOperating.js'
import {
  AuxReturnByReceipt,
  GetAuxDetailByReceipt, GetTeamListByUserForMateriel,
  GetUserPage
} from '@/api/PRO/material-warehouse/material-inventory-reconfig.js'
// import { GetFactoryPeoplelist } from '@/api/PRO/basic-information/workshop'
import { GetProcessListBase } from '@/api/PRO/technology-lib'
import { getDictionary } from '@/utils/common'
import { GetPreferenceSettingValue } from '@/api/sys/system-setting'
import {
  PickOutStore,
  GetPickOutDetail
} from '@/api/PRO/material-warehouse/material-inventory-reconfig.js'
import { GetOMALatestStatisticTime } from '@/api/PRO/materialManagement'
import { GetOssUrl, GetCompanyDepartTree } from '@/api/sys'
import ReturnTb from './components/ReturnTb.vue'
import getTableConfig from '@/mixins/PRO/get-table-info-pro/index'
import Warehouse from '@/views/PRO/material-receipt-management/components/Warehouse.vue'
import { v4 as uuidv4 } from 'uuid'
import SelectMaterialStoreType from '@/components/Select/SelectMaterialStoreType/index.vue'
import DynamicTableFields from '@/components/DynamicTableFields/index.vue'
import { GetWorkingTeams } from '@/api/PRO/technology-lib'
import SelectDepartment from '@/components/Select/SelectDepartment/index.vue'
import SelectDepartmentUser from '@/components/Select/SelectDepartmentUser/index.vue'
import PickSelect from '@/views/PRO/material_v4/pickApply/select.vue'
export default {
  components: {
    PickSelect,
    SelectDepartmentUser, SelectDepartment,
    DynamicTableFields,
    SelectMaterialStoreType,
    AddPurchaseList,
    PurchaseTb,
    ReturnTb,
    ImportFile,
    Warehouse,
    Standard,
    AddList,
    OSSUpload
  },
  mixins: [getCommonData, getTableConfig],
  props: {
    pageType: {
      type: Number,
      default: undefined
    }
  },
  data() {
    return {
      isRetract: false, // 是否展开
      returning: false,
      tbLoading: false,
      projectOptions: [],
      multipleSelection: [],
      // PartyUnitData: [],
      // SupplierData: [],
      factoryPeoplelist: [], // 领料人列表
      departmentlist: [],
      ProcessList: [],
      AuxOutboundTypeList: [],
      WorkingTeamList: [], // 领料班组列表
      searchForm: {
        RawName: '',
        Spec: ''
      },
      form: {
        OutStoreNo: '', // 出库单号
        OutStoreType: 1, // 出库类型
        OutStoreDate: this.getDate(),
        Use_Processing_Id: '', // 工序id
        Pick_Department_Id: '',
        // ProjectId: '',
        SysProjectId: '',
        Pick_Project_Name: '', // 领用项目名称
        ReceiveUserId: '', // 领料人id
        WorkingTeamId: '', // 领料班组id
        Remark: '',
        Attachment: ''
      },
      rules: {
        OutStoreType: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        OutStoreDate: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        Pick_Department_Id: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        ReceiveUserId: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
        // WorkingTeamId: [
        //   { required: true, message: '请选择', trigger: 'change' }
        // ]
      },
      treeParamsDepart: {
        'default-expand-all': true,
        filterable: false,
        clickParent: true,
        data: [],
        props: {
          disabled: 'disabled',
          children: 'Children',
          label: 'Label',
          value: 'Id'
        }
      },
      backendDate: null,
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < new Date(this.backendDate).getTime() // 限制选择日期不能超过当前日期
        }
      },
      currentComponent: '',
      title: '',
      dWidth: '60%',
      // isSingle: false,
      saveLoading: false,
      search: () => ({}),
      openAddList: false,
      dialogVisible: false,
      BigType: 1,
      isProductweight: null,
      fileListData: [],
      fileListArr: [],
      searchNum: 1,
      rootTableData: [],
      tableData: [],
      typeNumber1: 0,
      typeNumber2: 0,
      typeNumber3: 0,
      typeNumber4: 0,
      currentTbComponent: '',
      batchDialogVisible: false,
      batchProjectId: ''
    }
  },
  computed: {
    isAdd() {
      return this.pageType === 1
    },
    isEdit() {
      return this.pageType === 2
    },
    isView() {
      return this.pageType === 3
    },
    isPurchase() {
      return this.form.OutStoreType == 1 // 领用出库
    },
    isReturn() {
      return this.pageType === 8
    },
    gridCode() {
      return this.isReturn ? 'pro_aux_material_outbound_detail_list_return' : 'pro_aux_material_outbound_detail_list,Steel'
    }
  },
  async mounted() {
    if (this.isReturn) {
      this.currentTbComponent = ReturnTb
      const column = await this.getTableConfig(this.gridCode)
      this.$nextTick(_ => {
        this.$refs['table'].init(column)
      })
    } else {
      this.currentTbComponent = PurchaseTb
    }
    await this.getCurFactory()
    await this.getOMALatestStatisticTime()
    this.getPreferenceSettingValue()
    this.AuxOutboundTypeList = await getDictionary('AuxOutboundType')
    await this.getFirstLevelDepartsUnderFactory()
    this.search = debounce(this.fetchData, 800, true)
    this.getProject()
    this.getProcessListBase()
    // this.getFactoryPeoplelist()
    this.getWorkingTeams()
    if (!this.isAdd) {
      this.getInfo()
    }
  },
  methods: {
    userChange(val) {
      GetTeamListByUserForMateriel({
        id: val
      }).then(res => {
        if (res.Data?.length === 1) {
          this.$set(this.form, 'WorkingTeamId', res.Data[0].Id)
        } else {
          this.$set(this.form, 'WorkingTeamId', '')
        }
      })
    },
    closeBatchDialog() {
      this.batchProjectId = ''
      this.batchDialogVisible = false
    },
    batchChangeProject() {
      const tbData = this.$refs.table.tbData
      this.multipleSelection.forEach((element, idx) => {
        const item = tbData.find((v) => v.index === element.index)
        const i = tbData.findIndex((v) => v.index === element.index)
        item.Pick_Sys_Project_Id = this.batchProjectId
        // 同时更新项目名称
        item.Pick_Project_Name = this.projectOptions.find(project => project.Sys_Project_Id === this.batchProjectId)?.Short_Name
        this.$set(tbData, i, item)
      })
      this.closeBatchDialog()
    },
    changeColumn() {
      const temp = this.currentTbComponent
      this.currentTbComponent = ''
      this.$nextTick(async() => {
        this.currentTbComponent = temp
        if (this.isReturn) {
          const column = await this.getTableConfig(this.gridCode)
          this.$nextTick(_ => {
            this.$refs['table'].init(column)
          })
        }
        this.$refs['table'].setData(this.tableData)
      })
    },
    // 附件上传成功
    uploadSuccess(response, file, fileList) {
      this.fileListArr = JSON.parse(JSON.stringify(fileList))
    },
    // 移除已上传文件
    uploadRemove(file, fileList) {
      this.fileListArr = JSON.parse(JSON.stringify(fileList))
    },
    // 点击已上传文件
    async handlePreview(file) {
      let encryptionUrl = ''
      if (file.response && file.response.encryptionUrl) {
        encryptionUrl = file.response.encryptionUrl
      } else {
        encryptionUrl = await GetOssUrl({ url: file.encryptionUrl })
        encryptionUrl = encryptionUrl.Data
      }
      window.open(encryptionUrl)
    },
    // 获取运营核算已统计的最新日期
    async getOMALatestStatisticTime() {
      const res = await GetOMALatestStatisticTime({})
      if (res.IsSucceed) {
        this.backendDate = res.Data || ''
      } else {
        this.message.error(res.Mesaage)
      }
    },
    // 搜索
    handleSearch() {
      this.tableData = JSON.parse(JSON.stringify(this.rootTableData))
      if (this.searchForm.RawName) {
        const rawNameRegex = new RegExp(this.searchForm.RawName, 'i')
        this.tableData = this.tableData.filter((item) => {
          return rawNameRegex.test(item.RawName)
        })
      }

      if (this.searchForm.Spec) {
        const specRegex = new RegExp(this.searchForm.Spec, 'i')
        this.tableData = this.tableData.filter((item) => {
          return specRegex.test(item.Spec)
        })
      }
      if (this.searchForm.SysProjectId) {
        const sysProjectIdRegex = new RegExp(this.searchForm.SysProjectId, 'i')
        this.tableData = this.tableData.filter((item) => {
          return sysProjectIdRegex.test(item.Sys_Project_Id)
        })
      }
      if (this.isReturn) {
        this.$refs['table'].setData(this.tableData)
      } else {
        this.$refs['table'].tbData = this.tableData
      }
    },
    // 更新表格数据
    handleUpdateTb() {
      this.rootTableData = this.$refs['table'].tbData
      this.tableData = JSON.parse(JSON.stringify(this.rootTableData))
      console.log(this.rootTableData, '11')
      console.log(this.tableData, '22')
      // this.setTabData()
    },
    // 获取品重偏好设置
    getPreferenceSettingValue() {
      GetPreferenceSettingValue({ code: 'Productweight' }).then((res) => {
        if (res.IsSucceed) {
          this.isProductweight = res.Data
          // this.isProductweight = "false";
          if (this.isProductweight !== 'true') {
            this.getDepartmentTree()
          }
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    getDepartmentTree() {
      GetCompanyDepartTree({ isAll: true }).then((res) => {
        if (res.IsSucceed) {
          const tree = res.Data
          this.setDisabledTree(tree)
          this.treeParamsDepart.data = tree
          this.$nextTick((_) => {
            this.$refs.treeSelectDepart.treeDataUpdateFun(tree)
          })
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    setDisabledTree(root) {
      if (!root) return
      root.forEach((element) => {
        const { Children } = element
        if (element.Data.Is_Company === true || element.Data.Type === '1') {
          element.disabled = true
        } else {
          element.disabled = false
        }
        if (Children.length > 0) {
          this.setDisabledTree(Children)
        }
      })
    },
    // 重置搜索
    handleReset() {
      this.searchNum = 1
      // this.$refs['searchForm'].resetFields()
      this.searchForm.RawName = ''
      this.searchForm.Spec = ''
      this.$refs['table'].tbData = this.rootTableData
      this.tableData = JSON.parse(JSON.stringify(this.rootTableData))
    },
    // 文件超出数量限制
    handleExceed() {
      this.$message({
        type: 'warning',
        message: '附件数量不能超过5个'
      })
    },
    handleRetract() {
      this.isRetract = !this.isRetract
    },
    fetchData() {},
    getInfo() {
      this.form.OutStoreNo = this.$route.query.OutStoreNo
      this.form.OutStoreType = +this.$route.query.OutStoreType
      // const _fun = this.$route.params.OutStoreType === 1 ? PickUpOutStoreDetail : PartyAOutStoreDetail
      let _fun
      let params
      if (this.isReturn) {
        _fun = GetAuxDetailByReceipt
        params = {
          Id: this.$route.query.id
        }
      } else {
        _fun = GetPickOutDetail
        params = {
          outStoreNo: this.$route.query.OutStoreNo
        }
      }
      _fun(params).then((res) => {
        if (res.IsSucceed) {
          const Receipt = res.Data.Receipt
          const Sub = res.Data.Sub
          const {
            OutStoreDate,
            Remark,
            SysProjectId,
            Pick_Department_Id,
            Use_Processing_Id,
            WorkingTeamId,
            ReceiveUserId,
            Attachment,
            Status
          } = Receipt
          this.form.OutStoreDate = this.getDate(new Date(OutStoreDate))
          this.form.Remark = Remark
          this.form.SysProjectId = SysProjectId
          this.form.Use_Processing_Id = Use_Processing_Id
          this.form.Pick_Department_Id = Pick_Department_Id
          if (this.form.Pick_Department_Id) {
            this.departmentChange()
          }
          this.form.Use_Processing_Id = Use_Processing_Id
          this.form.WorkingTeamId = WorkingTeamId
          this.form.ReceiveUserId = ReceiveUserId
          this.form.Status = Status

          // 处理表格数据
          const SubData = Sub.map((row, index) => {
            row.index = uuidv4()
            row.Warehouse_Location = row.WarehouseName
              ? row.WarehouseName + '/' + row.LocationName
              : ''
            // 临时存储kg
            // row.OutStoreWeightKG = row.OutStoreWeight
            // row.Pound_Weight_KG = row.Pound_Weight
            // row.OutStoreWeight = Number((row.OutStoreWeightKG / 1000).toFixed(3))
            // row.Pound_Weight = Number((row.Pound_Weight_KG / 1000).toFixed(3))
            return row
          })

          this.$nextTick((_) => {
            this.$refs['table'].tbData = JSON.parse(JSON.stringify(SubData))
            this.tableData = JSON.parse(JSON.stringify(SubData))
            this.rootTableData = JSON.parse(JSON.stringify(SubData))
          })
          if (Attachment) {
            this.form.Attachment = Attachment
            const AttachmentArr = Attachment.split(',')
            AttachmentArr.forEach((item) => {
              const fileUrl =
                item.indexOf('?Expires=') > -1
                  ? item.substring(0, item.lastIndexOf('?Expires='))
                  : item
              const fileName = decodeURI(fileUrl.substring(fileUrl.lastIndexOf('/') + 1))
              const AttachmentJson = {}
              AttachmentJson.name = fileName
              AttachmentJson.url = fileUrl
              AttachmentJson.encryptionUrl = fileUrl
              this.fileListData.push(AttachmentJson)
              this.fileListArr.push(AttachmentJson)
            })
          }
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    async getProcessListBase() {
      const res = await GetProcessListBase({})
      if (res.IsSucceed) {
        this.ProcessList = res.Data || []
      } else {
        this.message.error(res.Mesaage)
      }
    },
    departmentChange() {
      this.form.ReceiveUserId = ''
      if (this.form.Pick_Department_Id) {
        this.getUserPageList(this.form.Pick_Department_Id)
      }
    },
    getUserPageList(Id) {
      GetUserPage({
        PageSize: -1,
        DepartmentId: Id
      }).then((res) => {
        if (res.IsSucceed) {
          this.factoryPeoplelist = res.Data.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    async getFirstLevelDepartsUnderFactory() {
      const res = await GetFirstLevelDepartsUnderFactory({
        FactoryId: this.FactoryDetailData.Id
      })
      if (res.IsSucceed) {
        this.departmentlist = res.Data || []
        this.form.Pick_Department_Id = res.Data.find(
          (item) => item.Is_Cur_User_Depart
        )
          ? res.Data.find((item) => item.Is_Cur_User_Depart).Id
          : ''
        this.departmentChange()
      } else {
        this.message.error(res.Mesaage)
      }
    },
    /**
     * 获取所属项目
     */
    getProject() {
      GetProjectPageList({
        Page: 1,
        PageSize: -1
      }).then((res) => {
        if (res.IsSucceed) {
          this.projectOptions = res.Data.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },

    /**
     * 获取领料人列表
     */
    // getFactoryPeoplelist() {
    //   GetFactoryPeoplelist().then((res) => {
    //     if (res.IsSucceed) {
    //       this.factoryPeoplelist = res.Data
    //     } else {
    //       this.$message({
    //         message: res.Message,
    //         type: 'error'
    //       })
    //     }
    //   })
    // },

    /**
     * 获取领料班组列表
     */
    getWorkingTeams() {
      GetWorkingTeams().then((res) => {
        if (res.IsSucceed) {
          this.WorkingTeamList = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },

    changeWarehouse(row) {
      this.currentRow = row
      this.handleWarehouse(true)
    },
    handleWarehouse(isInline) {
      this.currentComponent = 'Warehouse'
      this.dWidth = '40%'
      this.title = '选择仓库/库位'
      !isInline && (this.currentRow = null)
      this.dialogVisible = true
    },
    getWarehouse({ warehouse, location }) {
      if (this.currentRow) {
        this.currentRow.ReturnWarehoueseId = warehouse.Id
        this.currentRow.ReturnLocationId = location.Id
        this.$set(this.currentRow, 'ReturnWarehoueseName', warehouse.Display_Name)
        this.$set(this.currentRow, 'ReturnLocationName', location.Display_Name)
        this.$set(
          this.currentRow,
          'Warehouse_Location_Return',
          warehouse.Display_Name + '/' + location.Display_Name
        )
      }
    },
    batchEditorFn(data) {
      if (this.currentRow) {
        data.forEach((item) => {
          this.$set(this.currentRow, item.key, item.val)
        })
      } else {
        this.multipleSelection.forEach((element, idx) => {
          data.forEach((item) => {
            this.$set(element, item.key, item.val)
          })
        })
      }
      this.handleClose()
    },
    changeStandard(row) {
      this.currentRow = row
      this.currentComponent = 'Standard'
      this.dWidth = '40%'
      this.title = '选择规格'
      this.dialogVisible = true
      this.$nextTick((_) => {
        this.$refs['content'].getOption(row)
      })
    },
    getStandard({ type, val }) {
      if (type === 1) {
        this.$set(this.currentRow, 'StandardDesc', val)
      } else {
        this.$set(this.currentRow, 'StandardDesc', val.StandardDesc)
        this.currentRow.StandardId = val.StandardId
      }
    },
    // 出库类型切换,表格数据清空
    typeChange(n) {
      if (n !== 0) {
        this.BigType = 1
        this.typeNumber1 = 0
        this.typeNumber2 = 0
        this.typeNumber3 = 0
        this.typeNumber4 = 0
        this.tableData = []
        this.$refs['table'].tbData = []
      }
    },
    // handleReset() {
    //   this.$refs["form"].resetFields();
    //   this.search(1);
    // },
    // 新增物料
    getAddList(list, info) {
      this.BigType = list[0].BigType
      list.map((item, index) => {
        item.index = uuidv4()
        item.Pick_Project_Name = item.Project_Name // 默认领用项目为所属项目
      })
      this.$refs['table'].addData(list)
      const tbData = this.$refs.table.tbData
      this.tableData = JSON.parse(JSON.stringify(tbData))
      this.rootTableData = JSON.parse(JSON.stringify(tbData))
      if (info) {
        this.form.WorkingTeamId = info.Pick_Team_Id
        this.form.Use_Processing_Id = info.Pick_Process_Id
      }
    },
    importData(list) {
      this.$refs['table'].importData(list)
    },
    getRowName({ Name, Id }) {
      this.currentRow.Name = Name
      this.currentRow.RawId = Id
      this.currentRow.StandardDesc = ''
    },
    generateComponent(title, component) {
      this.title = title
      this.currentComponent = component
      this.dialogVisible = true
    },
    handleDelete() {
      const tbData = this.$refs.table.tbData
      this.multipleSelection.forEach((element, idx) => {
        const i = tbData.findIndex((v) => v.index === element.index)
        tbData.splice(i, 1)
      })
      this.tableData = JSON.parse(JSON.stringify(tbData))
      this.rootTableData = JSON.parse(JSON.stringify(tbData))
      this.multipleSelection = []
      this.$refs.table?.$refs?.xTable.clearCheckboxRow()
    },
    handleClose(row) {
      this.openAddList = false
      this.dialogVisible = false
    },
    tbSelectChange(array) {
      // console.log(array, 'arr====')
      this.multipleSelection = array.records
    },
    handleDetail(row) {},
    // 表格数据校验
    checkValidate() {
      const submit = deepClone(this.$refs['table'].tbData)
      if (!submit.length) {
        this.$message({
          message: '数据不能为空',
          type: 'warning'
        })
        return {
          status: false
        }
      }
      const { status, msg } = this.checkTb(submit)
      if (!status) {
        this.$message({
          message: `${msg || '必填字段'}不能为空`,
          type: 'warning'
        })
        return {
          status: false
        }
      }
      return {
        data: submit,
        status: true
      }
    },
    handleReturn() {
      this.$confirm('是否提交退库信息?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.saveDraft(3)
        })
        .catch((e) => {
          console.log('error', e)

          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
    },
    // 提交入库
    handleSubmit(row) {
      this.$confirm('确认提交出库单?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.saveDraft(3)
        })
        .catch((e) => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
    },
    // 保存出库单
    saveDraft(type = 1) {
      const { data, status } = this.checkValidate()
      if (!status) return
      this.$refs['form'].validate((valid) => {
        if (!valid) return false
        this.saveLoading = true
        // this.form.Attachment = this.fileListArr.join(',')
        const formAttachment = []
        if (this.fileListArr.length > 0) {
          this.fileListArr.forEach((item) => {
            formAttachment.push(
              item.response && item.response.encryptionUrl
                ? item.response.encryptionUrl
                : item.encryptionUrl
            )
          })
        }
        this.form.Attachment = formAttachment.join(',')
        // 1草稿 2审核中 3通过 4退回
        this.form.Status = type == 1 ? 1 : 3
        const form = { ...this.form }
        // console.log(form, 'this.form===1111')
        // console.log(data, 'Sub===1111')
        let _fun

        if (this.isReturn) {
          _fun = AuxReturnByReceipt
        } else {
          _fun = PickOutStore
        }
        _fun({
          Receipt: form,
          Sub: data
        }).then((res) => {
          if (res.IsSucceed) {
            this.$message({
              message: '保存成功',
              type: 'success'
            })
            this.closeView()
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
          this.saveLoading = false
        })
      })
    },
    checkTb(list) {
      let check = null
      const isEmpty = ['', null, undefined]
      if (this.isReturn) {
        isEmpty.push(0)
        check = ['ReturnCount', 'Warehouse_Location_Return']
      } else {
        check = ['OutStoreCount']
      }
      for (let i = 0; i < list.length; i++) {
        const item = list[i]
        for (let j = 0; j < check.length; j++) {
          const c = check[j]
          if (isEmpty.includes(item[c])) {
            const cloumns = this.$refs.table.rootColumns
            const element = cloumns.find((v) => v.Code === c)
            return {
              status: false,
              msg: element?.Display_Name
            }
          }
        }
        delete item._X_ROW_KEY
        delete item.WarehouseName
        delete item.LocationName
      }
      return {
        status: true,
        msg: ''
      }
    },
    openAddDialog(row) {
      this.openAddList = true
    },
    // openAddDialog(row) {
    //    if (this.form.SysProjectId) {
    //     this.openAddList = true
    //   } else {
    //     this.$message({
    //       message: '请先选择项目',
    //       type: 'warning'
    //     })
    //     return
    //   }
    // },
    closeView() {
      closeTagView(this.$store, this.$route)
    },
    setSelectRow(multipleSelection) {
      this.multipleSelection = multipleSelection
    },
    // 日期格式化
    getDate(data) {
      const date = data || new Date()
      const year = date.getFullYear()
      const month = ('0' + (date.getMonth() + 1)).slice(-2)
      const day = ('0' + date.getDate()).slice(-2)
      return `${year}-${month}-${day}`
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  display: flex;
  flex-direction: column;
  .box-card-tb {
    flex: 1;
    margin-top: 8px;
  }
}
// 表格工具栏css
.toolbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // margin: 10px 0 0 0;
  // flex-wrap: nowrap;
  ::v-deep .el-radio-group {
    width: 400px;
  }
  .toolbar-title {
    font-size: 14px;
    font-weight: 600;
    color: #333333;
    span {
      display: inline-block;
      width: 2px;
      height: 14px;
      background: #009dff;
      margin-right: 6px;
      vertical-align: text-top;
    }
  }
  .search-form {
    width: 60%;
    ::v-deep {
      .el-form-item--small {
        margin-bottom: 0;
      }
      .el-form-item__content {
        width: 110px;
      }
      .last-btn {
        display: flex;
        justify-content: flex-end;
        .el-form-item {
          margin-right: 0;
        }
        .el-form-item__content {
          width: 100%;
        }
      }
    }
  }
  .statistics-container {
    display: flex;
    .statistics-item {
      margin-right: 32px;
      span:first-child {
        display: inline-block;
        font-size: 14px;
        line-height: 18px;
        font-weight: 500;
        color: #999999;
        margin-right: 16px !important;
      }
      span:last-child {
        font-size: 18px;
        font-weight: 600;
        color: #00c361;
      }
    }
  }
}
.el-card {
  ::v-deep {
    .el-card__body {
      display: flex;
      flex-direction: column;
    }
  }

  .tb-x {
    flex: 1;
    margin-bottom: 10px;
    overflow: auto;
  }
}

::v-deep .elDivder {
  margin: 10px;
}

.pagination-container {
  text-align: right;
  margin-top: 10px;
  padding: 0;
}

.upload-file-list {
  & > div {
    width: 100%;
    height: 30px;
    line-height: 30px;
    padding-left: 15px;
    padding-right: 15px;
    cursor: pointer;
    position: relative;
    i {
      margin-right: 10px;
    }
    i:last-child {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      right: 15px;
      color: #999999;
      font-size: 18px;
    }
  }
  & > div:hover {
    background-color: #f8f8f8;
    i:last-child {
      color: #298dff;
    }
  }
}

::v-deep .el-form-item {
  .el-form-item__content {
    .el-tree-select-input {
      width: 100% !important;
    }
  }
}

footer {
  display: flex;
  justify-content: space-between;
}
</style>

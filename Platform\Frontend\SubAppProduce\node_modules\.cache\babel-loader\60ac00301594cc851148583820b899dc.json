{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\component\\TreeData.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\structure-type-config\\component\\TreeData.vue", "mtime": 1756109946517}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9yZWdlbmVyYXRvclJ1bnRpbWUgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9yZWdlbmVyYXRvclJ1bnRpbWUuanMiOwppbXBvcnQgX2FzeW5jVG9HZW5lcmF0b3IgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9hc3luY1RvR2VuZXJhdG9yLmpzIjsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IEdldENvbXBUeXBlVHJlZSB9IGZyb20gJ0AvYXBpL1BSTy9jb21wb25lbnQtdHlwZSc7CmltcG9ydCB7IEdldFBhcnRUeXBlVHJlZSB9IGZyb20gJ0AvYXBpL1BSTy9wYXJ0VHlwZSc7CmltcG9ydCB7IEdldEJPTUluZm8gfSBmcm9tICdAL3ZpZXdzL1BSTy9ib20tc2V0dGluZy91dGlscyc7CmV4cG9ydCBkZWZhdWx0IHsKICBwcm9wczogewogICAgdHlwZUNvZGU6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnJwogICAgfSwKICAgIHR5cGVJZDogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcnCiAgICB9CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgdHJlZURhdGE6IFtdLAogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICBjdXJyZW50Tm9kZUtleTogJycsCiAgICAgIGFjdGl2ZVR5cGU6ICdjb21wJwogICAgfTsKICB9LAogIHdhdGNoOiB7CiAgICBjdXJyZW50Tm9kZUtleTogZnVuY3Rpb24gY3VycmVudE5vZGVLZXkobmV3VmFsdWUpIHsKICAgICAgdGhpcy4kZW1pdCgnc2hvd1JpZ2h0JywgbmV3VmFsdWUgIT09ICcnKTsKICAgIH0KICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7CiAgICB0aGlzLmZldGNoRGF0YSgpOwogIH0sCiAgbWV0aG9kczogewogICAgY2hhbmdlVHlwZTogZnVuY3Rpb24gY2hhbmdlVHlwZSgpIHsKICAgICAgdGhpcy4kZW1pdCgnY2hhbmdlVHlwZScsIHRoaXMuYWN0aXZlVHlwZSk7CiAgICAgIHRoaXMuY3VycmVudE5vZGVLZXkgPSAnJzsKICAgICAgdGhpcy5mZXRjaERhdGEoKTsKICAgIH0sCiAgICBmZXRjaERhdGE6IGZ1bmN0aW9uIGZldGNoRGF0YSgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlKCkgewogICAgICAgIHZhciByZXM7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUkKF9jb250ZXh0KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dC5wcmV2ID0gX2NvbnRleHQubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX3RoaXMubG9hZGluZyA9IHRydWU7CiAgICAgICAgICAgICAgaWYgKCEoX3RoaXMuYWN0aXZlVHlwZSA9PT0gJ2NvbXAnKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDc7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDQ7CiAgICAgICAgICAgICAgcmV0dXJuIEdldENvbXBUeXBlVHJlZSh7CiAgICAgICAgICAgICAgICBwcm9mZXNzaW9uYWw6IF90aGlzLnR5cGVDb2RlCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICByZXMgPSBfY29udGV4dC5zZW50OwogICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAxMDsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSA3OgogICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSA5OwogICAgICAgICAgICAgIHJldHVybiBHZXRQYXJ0VHlwZVRyZWUoewogICAgICAgICAgICAgICAgcHJvZmVzc2lvbmFsSWQ6IF90aGlzLnR5cGVJZAogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICBjYXNlIDk6CiAgICAgICAgICAgICAgcmVzID0gX2NvbnRleHQuc2VudDsKICAgICAgICAgICAgY2FzZSAxMDoKICAgICAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICAgICAgX3RoaXMudHJlZURhdGEgPSByZXMuRGF0YTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgX3RoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICBfdGhpcy50cmVlRGF0YSA9IFtdOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXMuY3VycmVudE5vZGVLZXkgJiYgX3RoaXMuc2V0VHJlZU5vZGUoKTsKICAgICAgICAgICAgY2FzZSAxMzoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBzZXRUcmVlTm9kZTogZnVuY3Rpb24gc2V0VHJlZU5vZGUoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICB0aGlzLiRlbWl0KCdub2RlQ2xpY2snLCB0aGlzLiRyZWZzWyd0cmVlJ10uZ2V0Tm9kZSh0aGlzLmN1cnJlbnROb2RlS2V5KSk7CiAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uIChfKSB7CiAgICAgICAgX3RoaXMyLiRyZWZzWyd0cmVlJ10uc2V0Q3VycmVudEtleShfdGhpczIuY3VycmVudE5vZGVLZXkpOwogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVBZGQ6IGZ1bmN0aW9uIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy4kZW1pdCgnQWRkRmlyc3QnKTsKICAgIH0sCiAgICBoYW5kbGVOb2RlQ2xpY2s6IGZ1bmN0aW9uIGhhbmRsZU5vZGVDbGljayhkYXRhLCBub2RlKSB7CiAgICAgIGNvbnNvbGUubG9nKGRhdGEsIG5vZGUpOwogICAgICB0aGlzLmN1cnJlbnROb2RlS2V5ID0gZGF0YS5JZDsKICAgICAgdGhpcy4kZW1pdCgnbm9kZUNsaWNrJywgbm9kZSk7CiAgICB9LAogICAgcmVzZXRLZXk6IGZ1bmN0aW9uIHJlc2V0S2V5KGlkKSB7CiAgICAgIGNvbnNvbGUubG9nKGlkLCB0aGlzLmN1cnJlbnROb2RlS2V5LCBpZCA9PT0gdGhpcy5jdXJyZW50Tm9kZUtleSwgJ2RzZCcpOwogICAgICBpZiAoaWQgPT09IHRoaXMuY3VycmVudE5vZGVLZXkpIHsKICAgICAgICB0aGlzLmN1cnJlbnROb2RlS2V5ID0gJyc7CiAgICAgIH0KICAgIH0KICB9Cn07"}, {"version": 3, "names": ["GetCompTypeTree", "GetPartTypeTree", "GetBOMInfo", "props", "typeCode", "type", "String", "default", "typeId", "data", "treeData", "loading", "currentNodeKey", "activeType", "watch", "newValue", "$emit", "mounted", "fetchData", "methods", "changeType", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "professional", "sent", "professionalId", "IsSucceed", "Data", "$message", "message", "Message", "setTreeNode", "stop", "_this2", "$refs", "getNode", "$nextTick", "_", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "handleAdd", "handleNodeClick", "node", "console", "log", "Id", "reset<PERSON>ey", "id"], "sources": ["src/views/PRO/bom-setting/structure-type-config/component/TreeData.vue"], "sourcesContent": ["<template>\r\n  <div class=\"tree-container\">\r\n    <div class=\"title\">\r\n      <el-radio-group v-model=\"activeType\" @change=\"changeType\">\r\n        <el-radio-button label=\"comp\">构件大类</el-radio-button>\r\n        <el-radio-button label=\"part\">零件大类</el-radio-button>\r\n      </el-radio-group>\r\n      <div class=\"btn-x\">\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"tree-wrapper\">\r\n      <el-tree\r\n        ref=\"tree\"\r\n        v-loading=\"loading\"\r\n        :current-node-key=\"currentNodeKey\"\r\n        element-loading-text=\"加载中\"\r\n        element-loading-spinner=\"el-icon-loading\"\r\n        empty-text=\"暂无数据\"\r\n        highlight-current\r\n        node-key=\"Id\"\r\n        default-expand-all\r\n        :expand-on-click-node=\"false\"\r\n        :data=\"treeData\"\r\n        :props=\"{\r\n          label:'Label',\r\n          children:'Children'\r\n        }\"\r\n        @node-click=\"handleNodeClick\"\r\n      >\r\n        <span slot-scope=\"{ node, data }\" class=\"custom-tree-node\">\r\n          <svg-icon\r\n            :icon-class=\"\r\n              node.expanded ? 'icon-folder-open' : 'icon-folder'\r\n            \"\r\n            class-name=\"class-icon\"\r\n          />\r\n          <span class=\"cs-label\" :title=\"node.label\">{{ node.label }}</span>\r\n        </span>\r\n      </el-tree>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetCompTypeTree } from '@/api/PRO/component-type'\r\nimport { GetPartTypeTree } from '@/api/PRO/partType'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\nexport default {\r\n  props: {\r\n    typeCode: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    typeId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      treeData: [],\r\n      loading: true,\r\n      currentNodeKey: '',\r\n      activeType: 'comp'\r\n    }\r\n  },\r\n  watch: {\r\n    currentNodeKey(newValue) {\r\n      this.$emit('showRight', newValue !== '')\r\n    }\r\n  },\r\n  mounted() {\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    changeType() {\r\n      this.$emit('changeType', this.activeType)\r\n      this.currentNodeKey = ''\r\n      this.fetchData()\r\n    },\r\n    async fetchData() {\r\n      this.loading = true\r\n      let res\r\n      if (this.activeType === 'comp') {\r\n        res = await GetCompTypeTree({ professional: this.typeCode })\r\n      } else {\r\n        res = await GetPartTypeTree({ professionalId: this.typeId })\r\n      }\r\n      if (res.IsSucceed) {\r\n        this.treeData = res.Data\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n        this.treeData = []\r\n      }\r\n      this.loading = false\r\n      this.currentNodeKey && this.setTreeNode()\r\n    },\r\n    setTreeNode() {\r\n      this.$emit('nodeClick', this.$refs['tree'].getNode(this.currentNodeKey))\r\n      this.$nextTick(_ => {\r\n        this.$refs['tree'].setCurrentKey(this.currentNodeKey)\r\n      })\r\n    },\r\n    handleAdd() {\r\n      this.$emit('AddFirst')\r\n    },\r\n    handleNodeClick(data, node) {\r\n      console.log(data, node)\r\n      this.currentNodeKey = data.Id\r\n      this.$emit('nodeClick', node)\r\n    },\r\n    resetKey(id) {\r\n      console.log(id, this.currentNodeKey, id === this.currentNodeKey, 'dsd')\r\n      if (id === this.currentNodeKey) {\r\n        this.currentNodeKey = ''\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import '~@/styles/mixin.scss';\r\n\r\n.tree-container{\r\n  margin-right: 16px;\r\n  flex-basis: 18%;\r\n  background-color: #FFFFFF;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .title{\r\n    border-bottom: 1px solid #EEEEEE;\r\n    font-weight: 500;\r\n    padding: 16px;\r\n    color: #333333;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    .title-name {\r\n      height: 30px;\r\n      line-height: 30px;\r\n    }\r\n    .btn-x{\r\n      padding: 0 0;\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  .tree-wrapper{\r\n    padding: 16px;\r\n    flex: 1;\r\n    overflow: hidden;\r\n    .el-tree{\r\n      @include scrollBar;\r\n      height: 100%;\r\n      overflow: auto;\r\n    }\r\n    .cs-label{\r\n      font-size: 14px;\r\n      margin-left: 4px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA,SAAAA,eAAA;AACA,SAAAC,eAAA;AACA,SAAAC,UAAA;AACA;EACAC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,MAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,OAAA;MACAC,cAAA;MACAC,UAAA;IACA;EACA;EACAC,KAAA;IACAF,cAAA,WAAAA,eAAAG,QAAA;MACA,KAAAC,KAAA,cAAAD,QAAA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACAC,UAAA,WAAAA,WAAA;MACA,KAAAJ,KAAA,oBAAAH,UAAA;MACA,KAAAD,cAAA;MACA,KAAAM,SAAA;IACA;IACAA,SAAA,WAAAA,UAAA;MAAA,IAAAG,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAV,KAAA,CAAAV,OAAA;cAAA,MAEAU,KAAA,CAAAR,UAAA;gBAAAgB,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACA/B,eAAA;gBAAAgC,YAAA,EAAAX,KAAA,CAAAjB;cAAA;YAAA;cAAAsB,GAAA,GAAAG,QAAA,CAAAI,IAAA;cAAAJ,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OAEA9B,eAAA;gBAAAiC,cAAA,EAAAb,KAAA,CAAAb;cAAA;YAAA;cAAAkB,GAAA,GAAAG,QAAA,CAAAI,IAAA;YAAA;cAEA,IAAAP,GAAA,CAAAS,SAAA;gBACAd,KAAA,CAAAX,QAAA,GAAAgB,GAAA,CAAAU,IAAA;cACA;gBACAf,KAAA,CAAAgB,QAAA;kBACAC,OAAA,EAAAZ,GAAA,CAAAa,OAAA;kBACAlC,IAAA;gBACA;gBACAgB,KAAA,CAAAX,QAAA;cACA;cACAW,KAAA,CAAAV,OAAA;cACAU,KAAA,CAAAT,cAAA,IAAAS,KAAA,CAAAmB,WAAA;YAAA;YAAA;cAAA,OAAAX,QAAA,CAAAY,IAAA;UAAA;QAAA,GAAAhB,OAAA;MAAA;IACA;IACAe,WAAA,WAAAA,YAAA;MAAA,IAAAE,MAAA;MACA,KAAA1B,KAAA,mBAAA2B,KAAA,SAAAC,OAAA,MAAAhC,cAAA;MACA,KAAAiC,SAAA,WAAAC,CAAA;QACAJ,MAAA,CAAAC,KAAA,SAAAI,aAAA,CAAAL,MAAA,CAAA9B,cAAA;MACA;IACA;IACAoC,SAAA,WAAAA,UAAA;MACA,KAAAhC,KAAA;IACA;IACAiC,eAAA,WAAAA,gBAAAxC,IAAA,EAAAyC,IAAA;MACAC,OAAA,CAAAC,GAAA,CAAA3C,IAAA,EAAAyC,IAAA;MACA,KAAAtC,cAAA,GAAAH,IAAA,CAAA4C,EAAA;MACA,KAAArC,KAAA,cAAAkC,IAAA;IACA;IACAI,QAAA,WAAAA,SAAAC,EAAA;MACAJ,OAAA,CAAAC,GAAA,CAAAG,EAAA,OAAA3C,cAAA,EAAA2C,EAAA,UAAA3C,cAAA;MACA,IAAA2C,EAAA,UAAA3C,cAAA;QACA,KAAAA,cAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
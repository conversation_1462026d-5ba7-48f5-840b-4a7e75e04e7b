<template>
  <div class="form-recognition-wrapper">
    <div class="form-recognition-tabs">
      <el-tabs v-model="bomActiveName">
        <el-tab-pane v-for="(item, index) in bomList" :key="index" :label="item.Display_Name" :name="item.Code" />
      </el-tabs>
    </div>
    <div>
      <comp-recognition-config v-if="bomActiveName === '-1'" @close="handleClose" />
      <part-recognition-config v-if="bomActiveName === '0'" :bom-list="bomList" @close="handleClose" />
      <unit-part-recognition-config v-if="bomActiveName !== '-1' && bomActiveName !== '0'" :bom-list="bomList" :level="Number(bomActiveName)" @close="handleClose" />
    </div>
  </div>
</template>

<script>
import { GetBOMInfo } from '@/views/PRO/bom-setting/utils'
import compRecognitionConfig from './compRecognitionConfig'
import partRecognitionConfig from './partRecognitionConfig'
import unitPartRecognitionConfig from './unitPartRecognitionConfig'

export default {
  components: {
    compRecognitionConfig,
    partRecognitionConfig,
    unitPartRecognitionConfig
  },
  data() {
    return {
      bomList: [],
      comName: '',
      partName: '',
      bomActiveName: '',
      btnLoading: false
    }
  },
  async mounted() {
    const { comName, partName, list } = await GetBOMInfo()
    this.comName = comName
    this.partName = partName
    this.bomList = list
    this.bomActiveName = list[0].Code
  },
  methods: {
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>

<style scoped lang="scss">
  .form-recognition-wrapper {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    max-height: 70vh;
    .form-recognition-tabs {

    }
  }
</style>

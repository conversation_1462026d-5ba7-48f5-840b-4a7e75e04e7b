{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\bom-level-config\\index.vue?vue&type=style&index=0&id=5effb47a&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\bom-level-config\\index.vue", "mtime": 1756109946500}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5ib20tbGV2ZWwtY29uZmlnIHsNCiAgLmNzLXotcGFnZS1tYWluLWNvbnRlbnQgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICBmbGV4OiAxOw0KICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgaGVpZ2h0OiAxMDAlOw0KDQogICAgLnF1ZXJ5LXNlY3Rpb24gew0KICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCg0KICAgICAgLnF1ZXJ5LWZvcm0gew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQoNCiAgICAgICAgLnF1ZXJ5LWxhYmVsIHsNCiAgICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgICAgY29sb3I6ICMzMzM7DQogICAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCiAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgIC5xdWVyeS1zZWxlY3Qgew0KICAgICAgICAgICB3aWR0aDogMjAwcHg7DQogICAgICAgICB9DQoNCiAgICAgICAgIC5zdWJtaXQtYnRuIHsNCiAgICAgICAgICAgbWFyZ2luLWxlZnQ6IDhweDsNCiAgICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQoNCiAgICAgICAudGFibGUtc2VjdGlvbiB7DQogICAgICBmbGV4OiAxOw0KICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsNCg0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwQA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/bom-setting/bom-level-config", "sourcesContent": ["<template>\r\n  <div class=\"bom-level-config abs100 cs-z-flex-pd16-wrap\">\r\n    <div class=\"cs-z-page-main-content\">\r\n      <div class=\"query-section\">\r\n        <div class=\"query-form\">\r\n          <div>\r\n            <span class=\"query-label\">请选择BOM层级数：</span>\r\n            <el-select\r\n              v-model=\"selectedType\"\r\n              placeholder=\"请选择\"\r\n              class=\"query-select\"\r\n              @change=\"handleTypeChange\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in typeOptions\"\r\n                :key=\"item.value\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"\r\n              />\r\n            </el-select>\r\n          </div>\r\n          <el-button type=\"primary\" class=\"submit-btn\" :loading=\"btnLoading\" @click=\"handleSubmit\">\r\n            提交\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"table-section\">\r\n        <vxe-table\r\n          ref=\"xTable1\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          empty-text=\"暂无数据\"\r\n          height=\"auto\"\r\n          show-overflow\r\n          :checkbox-config=\"{checkField: 'checked'}\"\r\n          :loading=\"tbLoading\"\r\n          :row-config=\"{isCurrent: true, isHover: true }\"\r\n          class=\"cs-vxe-table\"\r\n          align=\"left\"\r\n          keep-source\r\n          stripe\r\n          :data=\"tableData\"\r\n          resizable\r\n          :edit-config=\"{trigger: 'manual', mode: 'row', showStatus: true}\"\r\n        >\r\n          <vxe-column field=\"Sys_Name\" title=\"BOM层级\" align=\"center\">\r\n            <template #default=\"{ row }\">\r\n              {{ row.Sys_Name }}\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column field=\"Display_Name\" title=\"名称\" :edit-render=\"{}\" align=\"center\">\r\n            <template #edit=\"{ row }\">\r\n              <vxe-input v-model=\"row.Display_Name\" type=\"text\" />\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column field=\"Is_Default_Model\" title=\"模型默认层级\" align=\"center\" :edit-render=\"{}\">\r\n            <template #edit=\"{ row }\">\r\n              <el-switch\r\n                v-model=\"row.Is_Default_Model\"\r\n                active-color=\"#13ce66\"\r\n                inactive-color=\"#ff4949\"\r\n                @change=\"handleDefaultModelChange(row, $event)\"\r\n              />\r\n            </template>\r\n            <template #default=\"{ row }\">\r\n              <el-tag v-if=\"row.Is_Default_Model\" type=\"success\">是</el-tag>\r\n              <el-tag v-else type=\"danger\">否</el-tag>\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column title=\"操作\" width=\"160\" align=\"center\">\r\n            <template #default=\"{ row }\">\r\n              <template v-if=\"$refs.xTable1.isActiveByRow(row)\">\r\n                <el-button type=\"text\" @click=\"saveRowEvent(row)\">保存</el-button>\r\n                <el-button type=\"text\" @click=\"cancelRowEvent(row)\">取消</el-button>\r\n              </template>\r\n              <template v-else>\r\n                <el-button type=\"text\" @click=\"editRowEvent(row)\">编辑</el-button>\r\n              </template>\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n      </div>\r\n    </div>\r\n  </div></template>\r\n\r\n<script>\r\nimport { GetBomLevelList, SaveBomLevel } from '@/api/PRO/bom-level'\r\n\r\nexport default {\r\n  name: 'PROBOMLevelConfig',\r\n  data() {\r\n    return {\r\n      selectedType: 2,\r\n      btnLoading: false,\r\n      loading: false,\r\n      apiData: [],\r\n      typeOptions: [\r\n        { label: '二层', value: 2 },\r\n        { label: '三层', value: 3 },\r\n        { label: '四层', value: 4 },\r\n        { label: '五层', value: 5 }\r\n      ],\r\n      tableData: [],\r\n      tbLoading: false\r\n    }\r\n  },\r\n  created() {\r\n    this.selectedType = 2\r\n    this.getBomLevelList()\r\n  },\r\n  methods: {\r\n    getBomLevelList() {\r\n      this.tbLoading = true\r\n      return new Promise((resolve) => {\r\n        GetBomLevelList().then(res => {\r\n          const { IsSucceed, Message, Data } = res\r\n          if (IsSucceed) {\r\n            this.apiData = (Data || []).map(v => {\r\n              v.isEditing = false\r\n              v.originalName = v.Display_Name\r\n              return v\r\n            })\r\n\r\n            if (this.apiData && this.apiData.length > 0) {\r\n              const enabledCount = this.apiData.filter(item => !!item.Is_Enabled).length\r\n              if (enabledCount >= 2) {\r\n                this.selectedType = enabledCount\r\n              } else {\r\n                this.selectedType = 2\r\n              }\r\n            }\r\n\r\n            this.generateTableData(this.selectedType || 2, this.apiData)\r\n          } else {\r\n            this.$message({\r\n              message: Message || '获取BOM层级列表失败',\r\n              type: 'error'\r\n            })\r\n          }\r\n          resolve()\r\n        }).catch(error => {\r\n          console.error('获取BOM层级列表失败:', error)\r\n          this.$message.error('获取BOM层级列表失败')\r\n          resolve()\r\n        }).finally(() => {\r\n          this.tbLoading = false\r\n        })\r\n      })\r\n    },\r\n\r\n    generateTableData(levelCount, apiData = []) {\r\n      const levelCodeMap = {\r\n        2: ['-1', '0'],\r\n        3: ['-1', '1', '0'],\r\n        4: ['-1', '1', '2', '0'],\r\n        5: ['-1', '1', '2', '3', '0']\r\n      }\r\n\r\n      const validCodes = levelCodeMap[levelCount] || levelCodeMap[2]\r\n\r\n      apiData.forEach(item => {\r\n        item.Is_Enabled = false\r\n      })\r\n\r\n      const filteredData = apiData.filter(v => validCodes.includes(v.Code))\r\n\r\n      filteredData.forEach(item => {\r\n        item.Is_Enabled = true\r\n      })\r\n\r\n      filteredData.sort((a, b) => parseInt(a.Sort) - parseInt(b.Sort))\r\n\r\n      this.tableData = filteredData\r\n    },\r\n\r\n    handleTypeChange(value) {\r\n      if (this.apiData) {\r\n        this.generateTableData(value, this.apiData)\r\n      }\r\n    },\r\n\r\n    handleSubmit() {\r\n      const hasDefault = this.tableData.every(item => item.Is_Default_Model === false)\r\n      if (hasDefault) {\r\n        this.$message.warning('至少需要一个默认层级')\r\n        return\r\n      }\r\n\r\n      const saveData = [...this.apiData]\r\n\r\n      this.$confirm('确定要保存所有修改吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.btnLoading = true\r\n        SaveBomLevel(saveData).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message.success('保存成功')\r\n            this.getBomLevelList()\r\n          } else {\r\n            this.$message.error(res.Message || '保存失败')\r\n          }\r\n        }).catch(error => {\r\n          console.error('保存失败:', error)\r\n          this.$message.error('保存失败')\r\n        }).finally(() => {\r\n          this.btnLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('已取消保存')\r\n      })\r\n    },\r\n\r\n    handleEdit(row, rowIndex) {\r\n      this.customTableConfig.tableData.forEach(item => {\r\n        if (item !== row) {\r\n          row.isEditing = false\r\n        }\r\n      })\r\n      row.isEditing = true\r\n    },\r\n\r\n    handleSaveName(row, rowIndex) {\r\n      row.isEditing = false\r\n    },\r\n\r\n    handleCancel(row, rowIndex) {\r\n      row.Display_Name = row.originalName\r\n      row.isEditing = false\r\n    },\r\n\r\n    handleDefaultModelChange(row, value) {\r\n      this.tableData.forEach(item => {\r\n        if (item !== row) {\r\n          item.Is_Default_Model = false\r\n        }\r\n      })\r\n    },\r\n    editRowEvent(row) {\r\n      const $table = this.$refs.xTable1\r\n      $table.setEditRow(row)\r\n    },\r\n    saveRowEvent() {\r\n      const $table = this.$refs.xTable1\r\n      $table.clearEdit().then(() => {\r\n        this.loading = true\r\n        setTimeout(() => {\r\n          this.loading = false\r\n        }, 300)\r\n      })\r\n    },\r\n    cancelRowEvent(row) {\r\n      const $table = this.$refs.xTable1\r\n      $table.clearEdit().then(() => {\r\n        $table.revertData(row)\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.bom-level-config {\r\n  .cs-z-page-main-content {\r\n    display: flex;\r\n    flex-direction: column;\r\n    flex: 1;\r\n    overflow: hidden;\r\n    height: 100%;\r\n\r\n    .query-section {\r\n      margin-bottom: 20px;\r\n      border-radius: 4px;\r\n\r\n      .query-form {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n\r\n        .query-label {\r\n          font-size: 14px;\r\n          color: #333;\r\n          white-space: nowrap;\r\n        }\r\n\r\n                 .query-select {\r\n           width: 200px;\r\n         }\r\n\r\n         .submit-btn {\r\n           margin-left: 8px;\r\n         }\r\n      }\r\n    }\r\n\r\n       .table-section {\r\n      flex: 1;\r\n      overflow: hidden;\r\n\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\bom-level-config\\index.vue?vue&type=style&index=0&id=5effb47a&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\bom-level-config\\index.vue", "mtime": 1757909680919}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+RA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/bom-setting/bom-level-config", "sourcesContent": ["<template>\r\n  <div class=\"bom-level-config abs100 cs-z-flex-pd16-wrap\">\r\n    <div class=\"cs-z-page-main-content\">\r\n      <div class=\"title\">\r\n        <i class=\"el-icon-warning\" />\r\n        自行配置BOM层级数，<span class=\"title-span\">最多可新增5层</span>\r\n      </div>\r\n      <div class=\"box-wrapper\">\r\n        <div\r\n          v-for=\"(layer, idx) in visibleLayers\"\r\n          :key=\"layer.key\"\r\n          class=\"box\"\r\n          :class=\"layer.color\"\r\n        >\r\n          <span v-if=\"idx !== 0 && idx !== visibleLayers.length - 1\" class=\"close-icon\" @click=\"handleClose(layer, idx)\" />\r\n          <div class=\"box-title\">BOM{{ numToHan(idx) }}层</div>\r\n          <div class=\"box-subtitle\">{{ layer.subtitle }}</div>\r\n          <div class=\"box-input\">\r\n            <el-input\r\n              v-model.trim=\"layer.title\"\r\n              maxlength=\"20\"\r\n              size=\"medium\"\r\n              class=\"cs-input\"\r\n              placeholder=\"请输入\"\r\n              @blur=\"handleInputBlur\"\r\n            />\r\n            <i class=\"el-icon-edit-outline\" />\r\n          </div>\r\n          <el-divider />\r\n          <div class=\"box-bottom\">\r\n            <div class=\"box-bottom-label\">\r\n              <span\r\n                class=\"cs-bom-btn cs-bom-btn-main\"\r\n                :style=\"{background: layer.color === 'color1' ? '#298DFF' : layer.color === 'color2' ? '#3ECC93' : layer.color === 'color3' ? '#F1B430' : layer.color === 'color4' ? '#426BD8' : '#FF7D23'}\"\r\n                @click=\"handleBottomLabelClick(layer, idx)\"\r\n              >清单配置</span>\r\n              <span\r\n                class=\"cs-bom-btn cs-bom-btn-model\"\r\n                :class=\"{selected: layer.Is_Default_Model}\"\r\n                :style=\"layer.Is_Default_Model ? {borderColor: getMainColor(layer), color: getMainColor(layer)} : {borderColor: 'transparent', color: '#999'}\"\r\n                @click=\"handleModelDefaultClick(layer)\"\r\n              >\r\n                模型默认层级\r\n                <span v-if=\"layer.Is_Default_Model\" class=\"cs-bom-btn-check\" :style=\"{color: getMainColor(layer)}\">\r\n                  <svg width=\"11\" height=\"11\" viewBox=\"0 0 11 11\">\r\n                    <rect width=\"11\" height=\"11\" rx=\"2\" fill=\"currentColor\" />\r\n                    <text x=\"1.5\" y=\"9\" font-size=\"10\" fill=\"#fff\">✓</text>\r\n                  </svg>\r\n                </span>\r\n              </span>\r\n            </div>\r\n            <span class=\"box-bottom-value\">{{ (idx+1).toString().padStart(2, '0') }}</span>\r\n          </div>\r\n        </div>\r\n        <div v-if=\"visibleLayers.length < 5\" class=\"box add-box\" @click=\"handleAddLayer\">\r\n          <div class=\"add-plus\">+</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { GetBomLevelList, SaveBomLevel } from '@/api/PRO/bom-level'\r\nimport { deepClone } from '@/utils'\r\nimport addRouterPage from '@/mixins/add-router-page'\r\nexport default {\r\n  mixins: [addRouterPage],\r\n  data() {\r\n    return {\r\n\r\n      apiData: [], // GetBomLevelList接口数据\r\n      tbLoading: false,\r\n      selectedType: 2\r\n    }\r\n  },\r\n  computed: {\r\n    visibleLayers() {\r\n      // 一层和五层始终显示，中间层根据Is_Enabled\r\n      if (!this.apiData || this.apiData.length === 0) return []\r\n      const arr = this.apiData.filter((item, idx) => {\r\n        if (idx === 0 || idx === 4) return true\r\n        return !!item.Is_Enabled\r\n      })\r\n      return arr\r\n    },\r\n    comName() {\r\n      if (!this.apiData || this.apiData.length === 0) return ''\r\n      return this.apiData[0].Display_Name + '清单'\r\n    },\r\n    partName() {\r\n      if (!this.apiData || this.apiData.length === 0) return ''\r\n      return this.apiData[4].Display_Name + '清单'\r\n    },\r\n    addPageArray() {\r\n      const unitPart = this.defaultData.filter(item => item.Is_Enabled && +item.Code > 0)\r\n      const route = [{\r\n        path: this.$route.path + '/ComponentConfig',\r\n        hidden: true,\r\n        component: () => import('@/views/PRO/bom-setting/com-config/index'),\r\n        name: 'PROComponentConfig',\r\n        meta: { title: this.comName }\r\n      },\r\n      {\r\n        path: this.$route.path + '/part-config',\r\n        hidden: true,\r\n        component: () => import('@/views/PRO/bom-setting/part-config/index'),\r\n        name: 'PROPartsConfig',\r\n        meta: { title: this.partName }\r\n      }]\r\n      const curList = []\r\n      if (unitPart.length > 0) {\r\n        unitPart.forEach(item => {\r\n          curList.push({\r\n            path: this.$route.path + `/half-part-config${item.Code}`,\r\n            hidden: true,\r\n            component: () => import('@/views/PRO/bom-setting/half-part-config/index'),\r\n            name: 'PROHalfPartConfig' + item.Code,\r\n            meta: { title: item.Display_Name + '清单' }\r\n          })\r\n        })\r\n      }\r\n      route.splice(1, 0, ...curList)\r\n      console.log('route', route)\r\n      return route\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.getBomLevelList()\r\n    this.handleInitPageRoute()\r\n    console.log('this.addPageArray', this.$router.getRoutes())\r\n  },\r\n  methods: {\r\n    initPage() {\r\n      console.log('hello word 存在即合理')\r\n    },\r\n    handleClose(layer, idx) {\r\n      this.$confirm('确定要关闭该层级吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        if (idx === 0 || idx === this.visibleLayers.length - 1) return\r\n        const target = this.apiData.find(l => l.key === layer.key)\r\n        if (target) target.Is_Enabled = false\r\n        this.saveBomLevelList()\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消关闭'\r\n        })\r\n      })\r\n    },\r\n    handleAddLayer() {\r\n      const canAdd = this.apiData.find((l, idx) => idx !== 0 && idx !== 4 && !l.Is_Enabled)\r\n      if (canAdd) {\r\n        canAdd.Is_Enabled = true\r\n        this.saveBomLevelList()\r\n      }\r\n    },\r\n    handleInputBlur() {\r\n      this.saveBomLevelList()\r\n    },\r\n    saveBomLevelList() {\r\n      // console.log('this.apiData', this.apiData)\r\n      let flag = true\r\n      const payload = this.apiData.map(item => {\r\n        const { key, title, bottomValue, subtitle, color, ...others } = item\r\n\r\n        others.Display_Name = title\r\n        if (title === '') {\r\n          flag = false\r\n        }\r\n        return others\r\n      })\r\n      if (!flag) {\r\n        this.$message.error('请输入BOM层级名称')\r\n        return\r\n      }\r\n      const data1 = JSON.stringify(this.defaultData)\r\n      const data2 = JSON.stringify(payload)\r\n      if (data1 === data2) {\r\n        console.log('没有变化')\r\n        return\r\n      }\r\n      console.log('payload', JSON.parse(JSON.stringify(payload)))\r\n\r\n      this.$confirm('确定要保存所有修改吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.btnLoading = true\r\n        SaveBomLevel(payload).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message.success('保存成功')\r\n            this.$store.dispatch('bomInfo/clearBomLevelCache')\r\n            this.getBomLevelList()\r\n          } else {\r\n            this.$message.error(res.Message || '保存失败')\r\n          }\r\n        }).catch(error => {\r\n          console.error('保存失败:', error)\r\n          this.$message.error('保存失败')\r\n        }).finally(() => {\r\n          this.btnLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message.info('已取消保存')\r\n        this.apiData.forEach(item => {\r\n          const defaultItem = this.defaultData.find(d => d.Code === item.Code)\r\n          item.Is_Default_Model = defaultItem.Is_Default_Model\r\n        })\r\n      })\r\n\r\n      // this.handleInitPageRoute()\r\n      // 调用保存接口\r\n      // SaveBomLevelList(payload).then(...)\r\n    },\r\n    handleBottomLabelClick(layer, idx) {\r\n      const _name = layer.Code === '-1' ? 'PROComponentConfig' : layer.Code === '0' ? 'PROPartsConfig' : 'PROHalfPartConfig' + layer.Code\r\n      console.log('_name', _name)\r\n      const query = { pg_redirect: this.$route.name }\r\n      if (+layer.Code > 0) {\r\n        query.level = layer.Code\r\n      }\r\n      this.$router.push({ name: _name, query })\r\n      // 这里可加保存逻辑\r\n    },\r\n    handleModelDefaultClick(layer) {\r\n      this.apiData.forEach(l => { l.Is_Default_Model = false })\r\n      layer.Is_Default_Model = true\r\n      this.saveBomLevelList()\r\n    },\r\n    handleInputEditClick() {\r\n      this.inputEdit = true\r\n    },\r\n    getBomLevelList() {\r\n      this.tbLoading = true\r\n      return new Promise((resolve) => {\r\n        GetBomLevelList().then(res => {\r\n          const { IsSucceed, Message, Data } = res\r\n          if (IsSucceed) {\r\n            this.defaultData = deepClone(Data)\r\n            this.apiData = (Data || []).map((v, index) => {\r\n              v.key = v.Code\r\n              v.subtitle = index === 0 ? 'layer One' : index === 1 ? 'layer Two' : index === 2 ? 'layer Three' : index === 3 ? 'layer Four' : 'layer Five'\r\n              v.color = index === 0 ? 'color1' : index === 1 ? 'color2' : index === 2 ? 'color3' : index === 3 ? 'color4' : 'color5'\r\n              v.bottomValue = (index + 1).toString().padStart(2, '0')\r\n              v.title = v.Display_Name\r\n              return v\r\n            })\r\n            if (this.apiData && this.apiData.length > 0) {\r\n              const enabledCount = this.apiData.filter(item => !!item.Is_Enabled).length\r\n              if (enabledCount >= 2) {\r\n                this.selectedType = enabledCount\r\n              } else {\r\n                this.selectedType = 2\r\n              }\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: Message || '获取BOM层级列表失败',\r\n              type: 'error'\r\n            })\r\n          }\r\n          console.log('this.apiData', this.apiData)\r\n          resolve()\r\n        }).catch(error => {\r\n          console.error('获取BOM层级列表失败:', error)\r\n          this.$message.error('获取BOM层级列表失败')\r\n          resolve()\r\n        }).finally(() => {\r\n          this.tbLoading = false\r\n        })\r\n      })\r\n    },\r\n    numToHan(num) {\r\n      const hanArr = ['一', '二', '三', '四', '五']\r\n      return hanArr[num] || num\r\n    },\r\n    getMainColor(layer) {\r\n      return layer.color === 'color1' ? '#298DFF' : layer.color === 'color2' ? '#3ECC93' : layer.color === 'color3' ? '#F1B430' : layer.color === 'color4' ? '#426BD8' : '#FF7D23'\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.cs-z-page-main-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 50px 20px;\r\n  overflow: hidden;\r\n  width: 100%;\r\n\r\n}\r\n\r\n.title {\r\n  margin:100px 0 70px 0;\r\n  font-family: Microsoft YaHei, Microsoft YaHei;\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  color: #222834;\r\n\r\n  .title-span,\r\n  .el-icon-warning {\r\n    color: #FF7C19;\r\n  }\r\n}\r\n\r\n.box-wrapper {\r\n  display: flex;\r\n  overflow-x: auto;\r\n  width: 95%;\r\n}\r\n\r\n.box {\r\n  display: flex;\r\n  flex-direction: column;\r\n  box-sizing: border-box;\r\n  padding: 20px;\r\n  min-width: 280px;\r\n  height: 340px;\r\n  margin: 12px;\r\n  border-radius: 4px 4px 4px 4px;\r\n  position: relative;\r\n\r\n  .box-title {\r\n    display: flex;\r\n    justify-content: center;\r\n    height: 35px;\r\n    font-family: Microsoft YaHei, Microsoft YaHei;\r\n    font-weight: bold;\r\n    font-size: 26px;\r\n    color: #333333;\r\n    margin-top: 30px;\r\n  }\r\n\r\n  .box-subtitle {\r\n    display: flex;\r\n    justify-content: center;\r\n    height: 19px;\r\n    margin-top: 8px;\r\n    font-family: Microsoft YaHei, Microsoft YaHei;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #999999;\r\n  }\r\n\r\n  .box-input {\r\n    display: flex;\r\n    flex-wrap: nowrap;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-top: 56px;\r\n    gap: 8px;\r\n  }\r\n\r\n  .close-icon {\r\n    width: 27px;\r\n    height: 27px;\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    cursor: pointer;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    &::before {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 0;\r\n      right: 0;\r\n      width: 0;\r\n      height: 0;\r\n      border-top: 27px solid #ACD4FF;\r\n      border-right: 27px solid #ACD4FF;\r\n      border-bottom: 27px solid transparent;\r\n      border-left: 27px solid transparent;\r\n      z-index: 0;\r\n    }\r\n\r\n    &::after {\r\n      content: \"×\";\r\n      position: relative;\r\n      z-index: 1;\r\n      color: #ffffff;\r\n      font-size: 24px;\r\n      font-weight: bold;\r\n      left: 1px;\r\n      top: 1px;\r\n      pointer-events: none;\r\n    }\r\n  }\r\n  .el-icon-edit-outline{\r\n    font-size: 18px;\r\n  }\r\n\r\n  .box-bottom {\r\n    flex: 1;\r\n    margin-top: 8px;\r\n    display: flex;\r\n    align-items: center;\r\n    position: relative;\r\n\r\n    .box-bottom-label {\r\n      z-index: 1;\r\n      align-self: flex-end;\r\n      padding-bottom: 12px;\r\n\r\n    }\r\n\r\n    .box-bottom-value {\r\n      position: absolute;\r\n      top: 0;\r\n      right: 0;\r\n      z-index: 0;\r\n      font-size: 72px;\r\n      font-family: STHupo, STHupo;\r\n      font-weight: 400;\r\n      font-size: 72px;\r\n    }\r\n  }\r\n}\r\n\r\n.add-box {\r\n  background: #F4F5F6;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-width: 280px;\r\n  height: 323px;\r\n  margin: 12px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  .add-plus {\r\n    width: 100px;\r\n    height: 100px;\r\n    color: #C8C9CC;\r\n    font-size: 100px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    user-select: none;\r\n  }\r\n}\r\n\r\n.el-divider--horizontal {\r\n  color: #D9DBE2;\r\n  margin: 14px 17px;\r\n  width: unset;\r\n}\r\n\r\n.el-icon-edit-outline {\r\n  color: #8E95AA;\r\n  margin-left: 6px;\r\n}\r\n\r\n.cs-input {\r\n  border-color: unset;\r\n  outline: unset;\r\n  border: unset;\r\n  background: transparent;\r\n  box-shadow: unset;\r\n  width: 120px;\r\n  text-align: center;\r\n\r\n  &:focus {\r\n    border-color: unset;\r\n    outline: unset;\r\n    border: unset;\r\n    background: transparent;\r\n    box-shadow: unset;\r\n  }\r\n\r\n  ::v-deep .el-input__inner {\r\n    padding: 0;\r\n    font-size: 18px;\r\n    border: unset;\r\n    outline: unset;\r\n    background: transparent;\r\n    box-shadow: unset;\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n.color1 {\r\n  background: rgba(41, 141, 255, 0.12);\r\n  .close-icon::before {\r\n    border-top: 27px solid rgba(41, 141, 255, 0.11);\r\n    border-right: 27px solid rgba(41, 141, 255, 0.11);\r\n  }\r\n  .box-bottom-label {\r\n    color: #298DFF;\r\n  }\r\n  .box-bottom-value {\r\n    color: #D3E8FF;\r\n  }\r\n}\r\n\r\n.color2 {\r\n  background: rgba(62, 204, 147, 0.12);\r\n  .close-icon::before {\r\n    border-top: 27px solid #ABE9D0;\r\n    border-right: 27px solid #ABE9D0;\r\n  }\r\n  .box-bottom-label {\r\n    color: #3ECC93;\r\n  }\r\n  .box-bottom-value {\r\n    color: #D3F3E6;\r\n  }\r\n}\r\n\r\n.color3 {\r\n  background: rgba(255, 170, 0, 0.12);\r\n  .close-icon::before {\r\n    border-top: 27px solid #FEDA92;\r\n    border-right: 27px solid #FEDA92;\r\n  }\r\n  .box-bottom-label {\r\n    color: #F1B430;\r\n  }\r\n  .box-bottom-value {\r\n    color: #FFECC4;\r\n  }\r\n}\r\n\r\n.color4 {\r\n  background: rgba(66, 107, 216, 0.12);\r\n  .close-icon::before {\r\n    border-top: 27px solid rgba(66, 107, 216, 0.11);\r\n    border-right: 27px solid rgba(66, 107, 216, 0.11);\r\n  }\r\n  .box-bottom-label {\r\n    color: #426BD8;\r\n  }\r\n  .box-bottom-value {\r\n    color: rgba(66, 107, 216,0.12);\r\n  }\r\n}\r\n\r\n.color5 {\r\n  background: rgba(255, 125, 35, 0.12);\r\n  .close-icon::before {\r\n    border-top: 27px solid rgba(255, 125, 35, 0.11);\r\n    border-right: 27px solid rgba(255, 125, 35, 0.11);\r\n  }\r\n  .box-bottom-label {\r\n    color: #FF7D23;\r\n  }\r\n  .box-bottom-value {\r\n    color: rgba(255, 125, 35, 0.12);\r\n  }\r\n}\r\n\r\n.cs-bom-btn {\r\n  display: inline-block;\r\n  border-radius: 4px;\r\n  padding: 6px 12px;\r\n  font-size: 14px;\r\n  margin-right: 8px;\r\n  cursor: pointer;\r\n  user-select: none;\r\n  transition: border 0.2s;\r\n  &.cs-bom-btn-main {\r\n    color: #fff;\r\n    border: none;\r\n  }\r\n  &.cs-bom-btn-model {\r\n    background: #fff;\r\n    color: #999;\r\n    border: 1.5px solid transparent;\r\n    position: relative;\r\n    padding-right: 22px;\r\n    &.selected {\r\n      font-weight: bold;\r\n    }\r\n    .cs-bom-btn-check {\r\n      position: absolute;\r\n      top: 0px;\r\n      right: 0px;\r\n      width: 11px;\r\n      height: 11px;\r\n      background: transparent;\r\n      border-radius: 2px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      color: inherit;\r\n      z-index: 2;\r\n      svg {\r\n        display: block;\r\n      }\r\n    }\r\n  }\r\n}\r\n.cs-bom-btn:last-child {\r\n  margin-right: 0;\r\n}\r\n\r\n</style>\r\n"]}]}
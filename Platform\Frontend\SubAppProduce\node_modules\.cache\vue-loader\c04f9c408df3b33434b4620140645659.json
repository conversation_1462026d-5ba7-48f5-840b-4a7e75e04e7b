{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-product-type\\component\\CompanyAdd.vue?vue&type=template&id=9bf0e608", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-product-type\\component\\CompanyAdd.vue", "mtime": 1757468128032}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\Add.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\Add.vue", "mtime": 1758001438856}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IEdldEJPTUluZm8gfSBmcm9tICdAL3ZpZXdzL1BSTy9ib20tc2V0dGluZy91dGlscycKaW1wb3J0IHsgR2V0VXNlckxpc3QgfSBmcm9tICdAL2FwaS9zeXMnCmltcG9ydCB7CiAgR2V0RmFjdG9yeVBlb3BsZWxpc3QsCiAgR2V0Q2hlY2tHcm91cExpc3QsCiAgR2V0V29ya2luZ1RlYW1zLAogIFNhdmVQcm9qZWN0UHJvY2Vzcwp9IGZyb20gJ0AvYXBpL1BSTy90ZWNobm9sb2d5LWxpYicKaW1wb3J0IHsgbWFwR2V0dGVycyB9IGZyb20gJ3Z1ZXgnCmltcG9ydCB7IEdldERpY3Rpb25hcnlEZXRhaWxMaXN0QnlDb2RlIH0gZnJvbSAnQC9hcGkvc3lzJwpleHBvcnQgZGVmYXVsdCB7CiAgcHJvcHM6IHsKICAgIHR5cGU6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnJwogICAgfSwKICAgIHJvd0luZm86IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0KCkgewogICAgICAgIHJldHVybiB7fQogICAgICB9CiAgICB9LAogICAgdG90YWxXb3JrbG9hZFByb3BvcnRpb246IHsKICAgICAgdHlwZTogTnVtYmVyLAogICAgICBkZWZhdWx0OiAwCiAgICB9LAogICAgc3lzUHJvamVjdElkOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBjaGVja0xpc3Q6IFtdLAogICAgICBidG5Mb2FkaW5nOiBmYWxzZSwKICAgICAgaGlkZGVuUGFydDogZmFsc2UsCgogICAgICBmb3JtOiB7CiAgICAgICAgQ29kZTogJycsCiAgICAgICAgTmFtZTogJycsCiAgICAgICAgQm9tX0xldmVsOiAnJywKICAgICAgICBNb250aF9BdmdfTG9hZDogJycsCiAgICAgICAgQ29vcmRpbmF0ZV9Vc2VySWQ6ICcnLAogICAgICAgIFNvcnQ6IHVuZGVmaW5lZCwKICAgICAgICBJc19FbmFibGU6IHRydWUsCiAgICAgICAgSXNfRXh0ZXJuYWw6IGZhbHNlLAogICAgICAgIElzX05lc3Q6IGZhbHNlLAogICAgICAgIElzX05lZWRfQ2hlY2s6IHRydWUsCiAgICAgICAgSXNfU2VsZl9DaGVjazogdHJ1ZSwKICAgICAgICBJc19JbnRlcl9DaGVjazogdHJ1ZSwKICAgICAgICBJc19QaWNrX01hdGVyaWFsOiBmYWxzZSwKICAgICAgICBJc19OZWVkX1RDOiB0cnVlLAogICAgICAgIElzX1dlbGRpbmdfQXNzZW1ibGluZzogZmFsc2UsCiAgICAgICAgSXNfQ3V0dGluZzogZmFsc2UsCiAgICAgICAgVENfQ2hlY2tfVXNlcklkOiAnJywKICAgICAgICBJc19OZWVkX1pMOiBmYWxzZSwKICAgICAgICBaTF9DaGVja19Vc2VySWQ6ICcnLAogICAgICAgIFNob3dfTW9kZWw6IGZhbHNlLAoKICAgICAgICBDaGVja19TdHlsZTogJzAnLAoKICAgICAgICBXb3JraW5nX1RlYW1fSWRzOiBbXSwKICAgICAgICBSZW1hcms6ICcnLAogICAgICAgIFdvcmtsb2FkX1Byb3BvcnRpb246ICcnCiAgICAgIH0sCiAgICAgIFpMX0NoZWNrX1VzZXJJZHM6IFtdLAogICAgICBUQ19DaGVja19Vc2VySWRzOiBbXSwKICAgICAgQ2hlY2tDaGFuZ2U6IHRydWUsCiAgICAgIHVzZXJPcHRpb25zOiBbXSwKICAgICAgb3B0aW9uc1VzZXJMaXN0OiBbXSwKICAgICAgb3B0aW9uc0dyb3VwTGlzdDogW10sCiAgICAgIG9wdGlvbnNXb3JraW5nVGVhbXNMaXN0OiBbXSwKICAgICAgcnVsZXM6IHsKICAgICAgICBDb2RlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5Luj5Y+3JywgdHJpZ2dlcjogJ2JsdXInIH0sCiAgICAgICAgICB7IG1heDogMzAsIG1lc3NhZ2U6ICfplb/luqblnKggMzAg5Liq5a2X56ym5YaFJywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICAgIE5hbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXlkI3np7AnLCB0cmlnZ2VyOiAnYmx1cicgfSwKICAgICAgICAgIHsgbWF4OiAzMCwgbWVzc2FnZTogJ+mVv+W6puWcqCAzMCDkuKrlrZfnrKblhoUnLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIF0sCiAgICAgICAgQm9tX0xldmVsOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeexu+WeiycsIHRyaWdnZXI6ICdjaGFuZ2UnIH1dLAogICAgICAgIFNvcnQ6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWlJywgdHJpZ2dlcjogJ2JsdXInIH1dLAogICAgICAgIElzX05lZWRfQ2hlY2s6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nmmK/lkKbotKjmo4AnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgICAgXQogICAgICB9LAogICAgICBXb3JrbG9hZF9Qcm9wb3J0aW9uOiAwLAogICAgICBib21MaXN0OiBbXSwKICAgICAgY29tTmFtZTogJycsCiAgICAgIHBhcnROYW1lOiAnJwogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIC4uLm1hcEdldHRlcnMoJ3RlbmFudCcsIFsnaXNWZXJzaW9uRm91ciddKSwKICAgIGFsbG9jYWJsZVdvcmtsb2FkUHJvcG9ydGlvbigpIHsKICAgICAgcmV0dXJuIHRoaXMudG90YWxXb3JrbG9hZFByb3BvcnRpb24gLSB0aGlzLldvcmtsb2FkX1Byb3BvcnRpb24KICAgIH0KICB9LAogIGFzeW5jIGNyZWF0ZWQoKSB7CiAgICBjb25zdCB7IGNvbU5hbWUsIHBhcnROYW1lLCBsaXN0IH0gPSBhd2FpdCBHZXRCT01JbmZvKCkKICAgIHRoaXMuY29tTmFtZSA9IGNvbU5hbWUKICAgIHRoaXMucGFydE5hbWUgPSBwYXJ0TmFtZQogICAgdGhpcy5ib21MaXN0ID0gbGlzdAogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMuZ2V0VXNlckxpc3QoKQogICAgdGhpcy5nZXRGYWN0b3J5UGVvcGxlbGlzdCgpCiAgICAvLyB0aGlzLmdldENoZWNrR3JvdXBMaXN0KCk7CiAgICB0aGlzLmdldFdvcmtpbmdUZWFtc0xpc3QoKQogICAgdGhpcy50eXBlID09PSAnZWRpdCcgJiYgdGhpcy5pbml0Rm9ybSgpCiAgfSwKICBtZXRob2RzOiB7CiAgICBpbml0Rm9ybSgpIHsKICAgICAgY29uc3QgeyBJc19OZXN0LCAuLi5vdGhlcnMgfSA9IHRoaXMucm93SW5mbwogICAgICB0aGlzLmZvcm0gPSBPYmplY3QuYXNzaWduKHt9LCBvdGhlcnMsIHsgSXNfTmVzdDogISFJc19OZXN0IH0pCiAgICAgIHRoaXMuZm9ybS5Cb21fTGV2ZWwgPSBTdHJpbmcodGhpcy5mb3JtLkJvbV9MZXZlbCkKICAgICAgdGhpcy5Xb3JrbG9hZF9Qcm9wb3J0aW9uID0gdGhpcy5yb3dJbmZvLldvcmtsb2FkX1Byb3BvcnRpb24KICAgICAgLy8gIGlmKHRoaXMuZm9ybS5UeXBlPT0yKXsKICAgICAgLy8gICB0aGlzLmZvcm0uVHlwZXMgPSAnMCcKICAgICAgLy8gIH1lbHNlIGlmKHRoaXMuZm9ybS5UeXBlPT0zKXsKICAgICAgLy8gICBsZXQgVHlwZXMgPSB0aGlzLnJhZGlvTGlzdC5maW5kKHYgPT4gWycxJywgJzInLCczJ10uaW5jbHVkZXModi5Db2RlKSk/LkNvZGUKICAgICAgLy8gICBjb25zb2xlLmxvZygnVHlwZXMnLCBUeXBlcykKICAgICAgLy8gICBjb25zb2xlLmxvZygndGhpcy5yYWRpb0xpc3QnLCB0aGlzLnJhZGlvTGlzdCkKICAgICAgLy8gICB0aGlzLmZvcm0uVHlwZXMgPSBUeXBlcwogICAgICAvLyAgfWVsc2UgaWYodGhpcy5mb3JtLlR5cGU9PTEpewogICAgICAvLyAgIHRoaXMuZm9ybS5UeXBlcyA9ICctMScKICAgICAgLy8gIH0KICAgICAgY29uc29sZS5sb2coJ3RoaXMuZm9ybScsIHRoaXMuZm9ybSkKCiAgICAgIC8vIOWkhOeQhuWOhuWPsuaVsOaNruWkmumAiemXrumimAogICAgICAvLyBpZiAodGhpcy5mb3JtLklzX05lZWRfQ2hlY2spIHsKICAgICAgLy8gICBpZiAodGhpcy5mb3JtLkNoZWNrX1N0eWxlID09PSAnMScpIHsKCiAgICAgIC8vICAgfSBlbHNlIHsKICAgICAgLy8gICAgIHRoaXMuQ2hlY2tDaGFuZ2UgPSAhIXRoaXMuZm9ybS5Jc19OZWVkX1RDCiAgICAgIC8vICAgICBpZiAodGhpcy5mb3JtLklzX05lZWRfWkwgJiYgdGhpcy5mb3JtLklzX05lZWRfVEMpIHsKICAgICAgLy8gICAgICAgdGhpcy5mb3JtLklzX05lZWRfVEMgPSB0cnVlCiAgICAgIC8vICAgICAgIHRoaXMuZm9ybS5Jc19OZWVkX1pMID0gZmFsc2UKICAgICAgLy8gICAgIH0KICAgICAgLy8gICB9CiAgICAgIC8vIH0KICAgICAgdGhpcy5aTF9DaGVja19Vc2VySWRzID0gdGhpcy5mb3JtLlpMX0NoZWNrX1VzZXJJZAogICAgICAgID8gdGhpcy5mb3JtLlpMX0NoZWNrX1VzZXJJZC5zcGxpdCgnLCcpCiAgICAgICAgOiBbXQogICAgICB0aGlzLlRDX0NoZWNrX1VzZXJJZHMgPSB0aGlzLmZvcm0uVENfQ2hlY2tfVXNlcklkCiAgICAgICAgPyB0aGlzLmZvcm0uVENfQ2hlY2tfVXNlcklkLnNwbGl0KCcsJykKICAgICAgICA6IFtdCiAgICB9LAogICAgLy8g5piv5ZCm6Ieq5qOACiAgICByYWRpb1NlbGZDaGVjayh2YWwpIHsgfSwKICAgIC8vIOaYr+WQpuS6kuajgAogICAgcmFkaW9JbnRlckNoZWNrKHZhbCkgeyB9LAogICAgLy8g6I635Y+W6K6+5aSH57G75Z6LCiAgICBhc3luYyBnZXREaWN0aW9uYXJ5RGV0YWlsTGlzdEJ5Q29kZSgpIHsKICAgICAgYXdhaXQgR2V0RGljdGlvbmFyeURldGFpbExpc3RCeUNvZGUoeyBkaWN0aW9uYXJ5Q29kZTogJ2RldmljZVR5cGUnIH0pLnRoZW4oKHJlcykgPT4gewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICB0aGlzLmRldmljZVR5cGVMaXN0ID0gcmVzLkRhdGEKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgICAgY29uc29sZS5sb2coJyB0aGlzLm9wdGlvbnNHcm91cExpc3QnLCB0aGlzLm9wdGlvbnNHcm91cExpc3QpCiAgICB9LAogICAgZ2V0VXNlckxpc3QoKSB7CiAgICAgIEdldFVzZXJMaXN0KHt9KS50aGVuKChyZXMpID0+IHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgdGhpcy51c2VyT3B0aW9ucyA9IHJlcy5EYXRhCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgZ2V0RmFjdG9yeVBlb3BsZWxpc3QoKSB7CiAgICAgIEdldEZhY3RvcnlQZW9wbGVsaXN0KHt9KS50aGVuKChyZXMpID0+IHsKICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgdGhpcy5vcHRpb25zVXNlckxpc3QgPSByZXMuRGF0YQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGFzeW5jIGdldENoZWNrR3JvdXBMaXN0KCkgewogICAgICBhd2FpdCBHZXRDaGVja0dyb3VwTGlzdCh7fSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIHRoaXMub3B0aW9uc0dyb3VwTGlzdCA9IHJlcy5EYXRhCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICAgIGNvbnNvbGUubG9nKCcgdGhpcy5vcHRpb25zR3JvdXBMaXN0JywgdGhpcy5vcHRpb25zR3JvdXBMaXN0KQogICAgfSwKICAgIGdldFdvcmtpbmdUZWFtc0xpc3QoKSB7CiAgICAgIEdldFdvcmtpbmdUZWFtcyh7fSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIHRoaXMub3B0aW9uc1dvcmtpbmdUZWFtc0xpc3QgPSByZXMuRGF0YQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIC8vIOmAieaLqeS4k+ajgOaWueW8jyDmir3mo4DjgIHlhajmo4AKICAgIHJhZGlvQ2hlY2tTdHlsZUNoYW5nZSh2YWwpIHsKICAgICAgLy8gaWYgKHZhbCA9PT0gJzAnKSB7CiAgICAgIC8vICAgdGhpcy5mb3JtLklzX05lZWRfVEMgPSB0cnVlCiAgICAgIC8vICAgdGhpcy5mb3JtLklzX05lZWRfWkwgPSBmYWxzZQogICAgICAvLyB9CiAgICAgIHRoaXMuWkxfQ2hlY2tfVXNlcklkcyA9IFtdCiAgICAgIHRoaXMuVENfQ2hlY2tfVXNlcklkcyA9IFtdCiAgICAgIHRoaXMuZm9ybS5aTF9DaGVja19Vc2VySWQgPSAnJwogICAgICB0aGlzLmZvcm0uVENfQ2hlY2tfVXNlcklkID0gJycKICAgIH0sCiAgICAvLyDmmK/lkKbkuJPmo4AKICAgIHJhZGlvQ2hhbmdlKHZhbCkgewogICAgICBpZiAodmFsID09PSBmYWxzZSAmJiB0aGlzLnR5cGUgPT09ICdhZGQnKSB7CiAgICAgICAgdGhpcy5mb3JtLmNoZWNrQ2hhbmdlID0gZmFsc2UKICAgICAgICB0aGlzLmZvcm0uSXNfTmVlZF9UQyA9IGZhbHNlCiAgICAgICAgdGhpcy5mb3JtLklzX05lZWRfWkwgPSBmYWxzZQogICAgICAgIHRoaXMuVENfQ2hlY2tfVXNlcklkcyA9IFtdCiAgICAgICAgdGhpcy5aTF9DaGVja19Vc2VySWRzID0gW10KICAgICAgICB0aGlzLmZvcm0uWkxfQ2hlY2tfVXNlcklkID0gJycKICAgICAgICB0aGlzLmZvcm0uVENfQ2hlY2tfVXNlcklkID0gJycKICAgICAgICB0aGlzLmZvcm0uQ2hlY2tfU3R5bGUgPSAnJwogICAgICB9IGVsc2UgewogICAgICAgIC8vIHRoaXMuZm9ybS5jaGVja0NoYW5nZSA9IHRydWUKICAgICAgICAvLyB0aGlzLmZvcm0uSXNfTmVlZF9UQyA9IHRydWUKICAgICAgICAvLyB0aGlzLmZvcm0uSXNfTmVlZF9aTCA9IGZhbHNlCiAgICAgICAgLy8gdGhpcy5DaGVja0NoYW5nZSA9ICEhdGhpcy5mb3JtLklzX05lZWRfVEMKICAgICAgICB0aGlzLmZvcm0uQ2hlY2tfU3R5bGUgPSAnMCcKICAgICAgfQogICAgfSwKICAgIC8vIOmAieaLqUJPTeWxgue6pwogICAgY2hhbmdlVHlwZSh2YWwpIHsKICAgICAgLy8gY29uc3QgQ29kZSA9IHZhbAogICAgICAvLyBjb25zb2xlLmxvZyhDb2RlLCAnQ29kZScpOwogICAgICAvLyBpZiAoQ29kZSA9PT0gJy0xJykgewogICAgICAvLyAgIHRoaXMuZm9ybS5UeXBlID0gMQogICAgICAvLyB9IGVsc2UgaWYgKENvZGUgPT09ICcwJykgewogICAgICAvLyAgIHRoaXMuZm9ybS5UeXBlID0gMgogICAgICAvLyB9IGVsc2UgaWYgKENvZGUgPT09ICcxJyB8fCBDb2RlID09PSAnMyd8fCBDb2RlID09PSAnMicpIHsKICAgICAgLy8gICB0aGlzLmZvcm0uVHlwZSA9IDMKICAgICAgLy8gfQogICAgICAvLyBpZiAodGhpcy5mb3JtLlR5cGUgPT09IDEgfHwgdGhpcy5mb3JtLlR5cGUgPT09IDMpIHsKICAgICAgLy8gICB0aGlzLmZvcm0uSXNfQ3V0dGluZyA9IHVuZGVmaW5lZAogICAgICAvLyB9IGVsc2UgaWYgKHRoaXMuZm9ybS5UeXBlID09PSAyKSB7CiAgICAgIC8vICAgdGhpcy5mb3JtLklzX1dlbGRpbmdfQXNzZW1ibGluZyA9IHVuZGVmaW5lZAogICAgICAvLyB9CiAgICB9LAogICAgdHlwZUNoYW5nZSgpIHsKICAgICAgdGhpcy5mb3JtLlRhc2tfTW9kZWwgPSAnJwogICAgfSwKICAgIGNoYW5nZVRjKHZhbCkgewogICAgICBjb25zb2xlLmxvZyh2YWwpCiAgICAgIHRoaXMuZm9ybS5UQ19DaGVja19Vc2VySWQgPSAnJwogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHZhbC5sZW5ndGg7IGkrKykgewogICAgICAgIGlmIChpID09PSB2YWwubGVuZ3RoIC0gMSkgewogICAgICAgICAgdGhpcy5mb3JtLlRDX0NoZWNrX1VzZXJJZCArPSB2YWxbaV0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5mb3JtLlRDX0NoZWNrX1VzZXJJZCArPSB2YWxbaV0gKyAnLCcKICAgICAgICB9CiAgICAgIH0KICAgICAgY29uc29sZS5sb2codGhpcy5mb3JtLlRDX0NoZWNrX1VzZXJJZCwgJ3RoaXMuZm9ybS5UQ19DaGVja19Vc2VySWQgJykKICAgIH0sCiAgICBjaGFuZ2VaTCh2YWwpIHsKICAgICAgY29uc29sZS5sb2codmFsKQogICAgICB0aGlzLmZvcm0uWkxfQ2hlY2tfVXNlcklkID0gJycKICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB2YWwubGVuZ3RoOyBpKyspIHsKICAgICAgICBpZiAoaSA9PT0gdmFsLmxlbmd0aCAtIDEpIHsKICAgICAgICAgIHRoaXMuZm9ybS5aTF9DaGVja19Vc2VySWQgKz0gdmFsW2ldCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuZm9ybS5aTF9DaGVja19Vc2VySWQgKz0gdmFsW2ldICsgJywnCiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgY2hlY2tib3hDaGFuZ2UodmFsLCB0eXBlKSB7CiAgICAgIGlmICh0eXBlID09PSAxKSB7CiAgICAgICAgaWYgKCF2YWwpIHsKICAgICAgICAgIHRoaXMuVENfQ2hlY2tfVXNlcklkcyA9IFtdCiAgICAgICAgfQogICAgICB9CiAgICAgIGlmICh0eXBlID09PSAyKSB7CiAgICAgICAgaWYgKCF2YWwpIHsKICAgICAgICAgIHRoaXMuWkxfQ2hlY2tfVXNlcklkcyA9IFtdCiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgaGFuZGxlU3VibWl0KCkgewogICAgICAvLyBkZWxldGUgdGhpcy5mb3JtLlR5cGVzCiAgICAgIGNvbnNvbGUubG9nKHRoaXMuZm9ybSwgJ3RoaXMuZm9ybScpCiAgICAgIHRoaXMuJHJlZnMuZm9ybS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICBpZiAoIXZhbGlkKSByZXR1cm4KICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSB0cnVlCiAgICAgICAgY29uc3QgdUl0ZW1zID0gdGhpcy5vcHRpb25zVXNlckxpc3QuZmluZCgKICAgICAgICAgICh2KSA9PiB2LklkID09PSB0aGlzLmZvcm0uQ29vcmRpbmF0ZV9Vc2VySWQKICAgICAgICApCiAgICAgICAgaWYgKHVJdGVtcykgewogICAgICAgICAgdGhpcy5mb3JtLkNvb3JkaW5hdGVfVXNlck5hbWUgPSB1SXRlbXMuRGlzcGxheV9OYW1lCiAgICAgICAgfQogICAgICAgIGlmICh0aGlzLmZvcm0uSXNfTmVlZF9DaGVjaykgeyAvLyDlpoLmnpzpnIDopoHkuJPmo4AKICAgICAgICAgIGlmICh0aGlzLmZvcm0uSXNfTmVlZF9aTCA9PT0gZmFsc2UgJiYgdGhpcy5mb3JtLklzX05lZWRfVEMgPT09IGZhbHNlKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+mAieaLqei0qOajgOexu+WeiycpCiAgICAgICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlCiAgICAgICAgICAgIHJldHVybgogICAgICAgICAgfQogICAgICAgIH0gZWxzZSB7IC8vIOWmguaenOS4jemcgOimgeS4k+ajgO+8jOWImeS4jemcgOimgeS4k+ajgOaWueW8j+OAgeS4k+ajgOexu+Wei+OAgeS4k+ajgOS6uuWRmCAg5ZCO57ut6ZyA6KaB5LqS5qOA6YC76L6R6L+t5LujCiAgICAgICAgICB0aGlzLmZvcm0uY2hlY2tDaGFuZ2UgPSBmYWxzZQogICAgICAgICAgdGhpcy5mb3JtLklzX05lZWRfVEMgPSBmYWxzZQogICAgICAgICAgdGhpcy5mb3JtLklzX05lZWRfWkwgPSBmYWxzZQogICAgICAgICAgdGhpcy5UQ19DaGVja19Vc2VySWRzID0gW10KICAgICAgICAgIHRoaXMuWkxfQ2hlY2tfVXNlcklkcyA9IFtdCiAgICAgICAgICB0aGlzLmZvcm0uWkxfQ2hlY2tfVXNlcklkID0gJycKICAgICAgICAgIHRoaXMuZm9ybS5UQ19DaGVja19Vc2VySWQgPSAnJwogICAgICAgICAgdGhpcy5mb3JtLkNoZWNrX1N0eWxlID0gbnVsbAogICAgICAgIH0KICAgICAgICBjb25zdCBaTCA9IHRoaXMuZm9ybS5Jc19OZWVkX1pMID8gdGhpcy5mb3JtLlpMX0NoZWNrX1VzZXJJZCA6ICcnCiAgICAgICAgY29uc3QgVEMgPSB0aGlzLmZvcm0uSXNfTmVlZF9UQyA/IHRoaXMuZm9ybS5UQ19DaGVja19Vc2VySWQgOiAnJwogICAgICAgIGlmICh0aGlzLmZvcm0uSXNfTmVlZF9aTCAmJiAoWkwgPz8gJycpID09PSAnJykgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6K+36YCJ5oup6LSo5qOA5ZGYJykKICAgICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlCiAgICAgICAgICByZXR1cm4KICAgICAgICB9CiAgICAgICAgaWYgKHRoaXMuZm9ybS5Jc19OZWVkX1RDICYmIChUQyA/PyAnJykgPT09ICcnKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfor7fpgInmi6nmjqLkvKTlkZgnKQogICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UKICAgICAgICAgIHJldHVybgogICAgICAgIH0KCiAgICAgICAgU2F2ZVByb2plY3RQcm9jZXNzKHsKICAgICAgICAgIEJvbV9MZXZlbDogdGhpcy5mb3JtLkJvbV9MZXZlbCwKICAgICAgICAgIFdvcmtsb2FkX1Byb3BvcnRpb246IHRoaXMuZm9ybS5Xb3JrbG9hZF9Qcm9wb3J0aW9uLAogICAgICAgICAgSXNfU2VsZl9DaGVjazogdGhpcy5mb3JtLklzX1NlbGZfQ2hlY2ssCiAgICAgICAgICBJc19JbnRlcl9DaGVjazogdGhpcy5mb3JtLklzX0ludGVyX0NoZWNrLAogICAgICAgICAgSXNfTmVlZF9DaGVjazogdGhpcy5mb3JtLklzX05lZWRfQ2hlY2ssCiAgICAgICAgICBDaGVja19TdHlsZTogdGhpcy5mb3JtLkNoZWNrX1N0eWxlLAogICAgICAgICAgSXNfTmVlZF9UQzogdGhpcy5mb3JtLklzX05lZWRfVEMsCiAgICAgICAgICBJc19OZWVkX1pMOiB0aGlzLmZvcm0uSXNfTmVlZF9aTCwKICAgICAgICAgIFN5c19Qcm9qZWN0X0lkOiB0aGlzLnN5c1Byb2plY3RJZCwKICAgICAgICAgIFByb2Nlc3NfSWQ6IHRoaXMuZm9ybS5JZCwKICAgICAgICAgIFpMX0NoZWNrX1VzZXJJZDogWkwsCiAgICAgICAgICBUQ19DaGVja19Vc2VySWQ6IFRDCiAgICAgICAgfSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICBtZXNzYWdlOiAn5L+d5a2Y5oiQ5YqfJywKICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycKICAgICAgICAgICAgfSkKICAgICAgICAgICAgdGhpcy4kZW1pdCgncmVmcmVzaCcpCiAgICAgICAgICAgIHRoaXMuJGVtaXQoJ2Nsb3NlJykKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgICAgfSkKICAgICAgICAgIH0KICAgICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlCiAgICAgICAgfSkKICAgICAgfSkKICAgIH0sCgogICAgY29kZUNoYW5nZShlKSB7CiAgICAgIHJldHVybiBlLnJlcGxhY2UoL1teYS16QS1aMC05XS9nLCAnJykKICAgIH0sCgogICAgaGFuZGxlUGVyY2VudGFnZUlucHV0KHZhbHVlKSB7CiAgICAgIC8vIOWmguaenOi+k+WFpeS4uuepuu+8jOebtOaOpei/lOWbngogICAgICBpZiAodmFsdWUgPT09ICcnIHx8IHZhbHVlID09PSBudWxsIHx8IHZhbHVlID09PSB1bmRlZmluZWQpIHsKICAgICAgICB0aGlzLmZvcm0uV29ya2xvYWRfUHJvcG9ydGlvbiA9ICcnCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIC8vIOi9rOaNouS4uuWtl+espuS4sui/m+ihjOWkhOeQhgogICAgICBsZXQgaW5wdXRWYWx1ZSA9IFN0cmluZyh2YWx1ZSkKCiAgICAgIC8vIOWPquWFgeiuuOaVsOWtl+WSjOS4gOS4quWwj+aVsOeCue+8jOenu+mZpOWFtuS7luWtl+espu+8iOWMheaLrOi0n+WPt++8iQogICAgICBpbnB1dFZhbHVlID0gaW5wdXRWYWx1ZS5yZXBsYWNlKC9bXjAtOS5dL2csICcnKQoKICAgICAgLy8g56Gu5L+d5Y+q5pyJ5LiA5Liq5bCP5pWw54K5CiAgICAgIGNvbnN0IGRvdENvdW50ID0gKGlucHV0VmFsdWUubWF0Y2goL1wuL2cpIHx8IFtdKS5sZW5ndGgKICAgICAgaWYgKGRvdENvdW50ID4gMSkgewogICAgICAgIC8vIOWmguaenOacieWkmuS4quWwj+aVsOeCue+8jOWPquS/neeVmeesrOS4gOS4qgogICAgICAgIGNvbnN0IGZpcnN0RG90SW5kZXggPSBpbnB1dFZhbHVlLmluZGV4T2YoJy4nKQogICAgICAgIGlucHV0VmFsdWUgPSBpbnB1dFZhbHVlLnN1YnN0cmluZygwLCBmaXJzdERvdEluZGV4ICsgMSkgKyBpbnB1dFZhbHVlLnN1YnN0cmluZyhmaXJzdERvdEluZGV4ICsgMSkucmVwbGFjZSgvXC4vZywgJycpCiAgICAgIH0KCiAgICAgIC8vIOWmguaenOWPquaYr+Wwj+aVsOeCue+8jOiuvue9ruS4uuepugogICAgICBpZiAoaW5wdXRWYWx1ZSA9PT0gJy4nKSB7CiAgICAgICAgdGhpcy5mb3JtLldvcmtsb2FkX1Byb3BvcnRpb24gPSAnJwogICAgICAgIHJldHVybgogICAgICB9CgogICAgICAvLyDlpoLmnpzlpITnkIblkI7kuLrnqbrlrZfnrKbkuLLvvIzorr7nva7kuLrnqboKICAgICAgaWYgKGlucHV0VmFsdWUgPT09ICcnKSB7CiAgICAgICAgdGhpcy5mb3JtLldvcmtsb2FkX1Byb3BvcnRpb24gPSAnJwogICAgICAgIHJldHVybgogICAgICB9CgogICAgICAvLyDpmZDliLblsI/mlbDkvY3mlbDmnIDlpJoy5L2NCiAgICAgIGlmIChpbnB1dFZhbHVlLmluY2x1ZGVzKCcuJykpIHsKICAgICAgICBjb25zdCBwYXJ0cyA9IGlucHV0VmFsdWUuc3BsaXQoJy4nKQogICAgICAgIGlmIChwYXJ0c1sxXSAmJiBwYXJ0c1sxXS5sZW5ndGggPiAyKSB7CiAgICAgICAgICBpbnB1dFZhbHVlID0gcGFydHNbMF0gKyAnLicgKyBwYXJ0c1sxXS5zdWJzdHJpbmcoMCwgMikKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC8vIOi9rOaNouS4uuaVsOWtl+i/m+ihjOiMg+WbtOajgOafpQogICAgICBjb25zdCBudW1WYWx1ZSA9IHBhcnNlRmxvYXQoaW5wdXRWYWx1ZSkKCiAgICAgIC8vIOWmguaenOS4jeaYr+acieaViOaVsOWtl++8jOiuvue9ruS4uuepugogICAgICBpZiAoaXNOYU4obnVtVmFsdWUpKSB7CiAgICAgICAgdGhpcy5mb3JtLldvcmtsb2FkX1Byb3BvcnRpb24gPSAnJwogICAgICAgIHJldHVybgogICAgICB9CgogICAgICAvLyDmoLnmja4gdG90YWxXb3JrbG9hZFByb3BvcnRpb24g6K6h566X5pyA5aSn5YC8CiAgICAgIGxldCBtYXhWYWx1ZSA9IDEwMAogICAgICBsZXQgY3VycmVudFRvdGFsID0gMAoKICAgICAgaWYgKHRoaXMudG90YWxXb3JrbG9hZFByb3BvcnRpb24pIHsKICAgICAgICAvLyDnoa7kv53miYDmnInmlbDlgLzpg73mmK/mta7ngrnmlbDlubbkv53nlZky5L2N5bCP5pWw6L+b6KGM6K6h566XCiAgICAgICAgY29uc3QgdG90YWwgPSBwYXJzZUZsb2F0KHRoaXMudG90YWxXb3JrbG9hZFByb3BvcnRpb24udG9GaXhlZCgyKSkKICAgICAgICBjb25zdCBjdXJyZW50ID0gcGFyc2VGbG9hdCgodGhpcy5Xb3JrbG9hZF9Qcm9wb3J0aW9uIHx8IDApLnRvU3RyaW5nKCkpCgogICAgICAgIC8vIOiuoeeul+W3ruWAvOW5tuS/neeVmTLkvY3lsI/mlbAKICAgICAgICBjb25zdCBkaWZmZXJlbmNlID0gdG90YWwgLSBjdXJyZW50CiAgICAgICAgY3VycmVudFRvdGFsID0gcGFyc2VGbG9hdChkaWZmZXJlbmNlLnRvRml4ZWQoMikpCgogICAgICAgIC8vIOmqjOivgeaVsOWtpuS4gOiHtOaAp++8muehruS/nSBjdXJyZW50VG90YWwgKyBjdXJyZW50ID0gdG90YWwKICAgICAgICBjb25zdCB2ZXJpZmljYXRpb24gPSBwYXJzZUZsb2F0KChjdXJyZW50VG90YWwgKyBjdXJyZW50KS50b0ZpeGVkKDIpKQogICAgICAgIGlmICh2ZXJpZmljYXRpb24gIT09IHRvdGFsKSB7CiAgICAgICAgICAvLyDlpoLmnpzkuI3kuIDoh7TvvIzosIPmlbQgY3VycmVudFRvdGFsIOS7peS/neivgeS4gOiHtOaApwogICAgICAgICAgY3VycmVudFRvdGFsID0gcGFyc2VGbG9hdCgodG90YWwgLSBjdXJyZW50KS50b0ZpeGVkKDIpKQogICAgICAgIH0KICAgICAgfQoKICAgICAgaWYgKGN1cnJlbnRUb3RhbCA9PT0gMTAwKSB7CiAgICAgICAgLy8g5aaC5p6c5oC75ZKM5bey57uP5pivMTAw77yM5pyA5aSn5Y+q6IO96L6T5YWlMAogICAgICAgIG1heFZhbHVlID0gMAogICAgICB9IGVsc2UgaWYgKGN1cnJlbnRUb3RhbCA+IDAgJiYgY3VycmVudFRvdGFsIDwgMTAwKSB7CiAgICAgICAgLy8g5aaC5p6c5oC75ZKM5ZyoMC0xMDDkuYvpl7TvvIzmnIDlpKflgLzmmK8xMDDlh4/ljrvlvZPliY3mgLvlkowKICAgICAgICBtYXhWYWx1ZSA9IDEwMCAtIGN1cnJlbnRUb3RhbAogICAgICB9CiAgICAgIC8vIOWmguaenOaAu+WSjOaYrzDmiJbnqbrvvIzmnIDlpKflgLzkv53mjIExMDAKCiAgICAgIC8vIOmZkOWItuiMg+WbtOWcqCAwLW1heFZhbHVlIOS5i+mXtAogICAgICBpZiAobnVtVmFsdWUgPCAwKSB7CiAgICAgICAgdGhpcy5mb3JtLldvcmtsb2FkX1Byb3BvcnRpb24gPSAnMCcKICAgICAgfSBlbHNlIGlmIChudW1WYWx1ZSA+IG1heFZhbHVlKSB7CiAgICAgICAgLy8g5L+d55WZMuS9jeWwj+aVsAogICAgICAgIHRoaXMuZm9ybS5Xb3JrbG9hZF9Qcm9wb3J0aW9uID0gbWF4VmFsdWUudG9GaXhlZCgyKS5yZXBsYWNlKC9cLj8wKyQvLCAnJykKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDkv53mjIHljp/lp4vovpPlhaXmoLzlvI/vvIjljIXmi6zlsI/mlbDngrnvvInvvIzkvYbnoa7kv53kuI3otoXov4cy5L2N5bCP5pWwCiAgICAgICAgdGhpcy5mb3JtLldvcmtsb2FkX1Byb3BvcnRpb24gPSBpbnB1dFZhbHVlCiAgICAgIH0KICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["Add.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAu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file": "Add.vue", "sourceRoot": "src/views/PRO/project-config/process-settings/component", "sourcesContent": ["<template>\n  <div class=\"form-wrapper\">\n    <div class=\"form-x\">\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\" style=\"width: 100%\">\n        <el-divider content-position=\"left\">基础信息</el-divider>\n        <el-form-item label=\"名称\" prop=\"Name\">\n          <el-input v-model=\"form.Name\" :maxlength=\"30\" placeholder=\"最多30个字\" show-word-limit :disabled=\"true\" />\n        </el-form-item>\n        <el-form-item label=\"代号\" prop=\"Code\">\n          <el-input\n            v-model=\"form.Code\"\n            :maxlength=\"30\"\n            placeholder=\"字母+数字，30字符\"\n            show-word-limit\n            :disabled=\"true\"\n            @input=\"(e) => (form.Code = codeChange(e))\"\n          />\n        </el-form-item>\n        <el-form-item label=\"类型\" prop=\"Bom_Level\">\n          <el-radio-group\n            v-for=\"(item, index) in bomList\"\n            :key=\"index\"\n            v-model=\"form.Bom_Level\"\n            class=\"radio\"\n            :disabled=\"true\"\n            @change=\"changeType\"\n          >\n            <el-radio style=\"margin-right: 8px;\" :label=\"item.Code\">{{ item.Display_Name }}</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"Sort\">\n          <el-input-number\n            v-model=\"form.Sort\"\n            :min=\"0\"\n            step-strictly\n            :step=\"1\"\n            class=\"cs-number-btn-hidden w100\"\n            placeholder=\"请输入\"\n            clearable=\"\"\n            :disabled=\"true\"\n          />\n        </el-form-item>\n        <el-form-item label=\"协调人\" prop=\"Coordinate_UserId\">\n          <el-select v-model=\"form.Coordinate_UserId\" class=\"w100\" clearable filterable placeholder=\"请选择\" :disabled=\"true\">\n            <el-option v-for=\"item in optionsUserList\" :key=\"item.Id\" :label=\"item.Display_Name\" :value=\"item.Id\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"工序月均负荷\" prop=\"Month_Avg_Load\">\n          <el-input v-model=\"form.Month_Avg_Load\" placeholder=\"请输入\" :disabled=\"true\">\n            <template slot=\"append\">吨</template>\n          </el-input>\n        </el-form-item>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否启用\" prop=\"Is_Enable\">\n              <el-radio-group v-model=\"form.Is_Enable\" :disabled=\"true\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否外协\" prop=\"Is_External\">\n              <el-radio-group v-model=\"form.Is_External\" :disabled=\"true\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否装焊工序\" prop=\"Is_Welding_Assembling\">\n              <el-radio-group v-model=\"form.Is_Welding_Assembling\" :disabled=\"true\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否下料工序\" prop=\"Is_Cutting\" :disabled=\"true\">\n              <el-radio-group v-model=\"form.Is_Cutting\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否套料工序\" prop=\"Is_Nest\" :disabled=\"true\">\n              <el-radio-group v-model=\"form.Is_Nest\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.Bom_Level === '0'\" label=\"是否领料工序\" prop=\"Is_Pick_Material\" :disabled=\"true\">\n              <el-radio-group v-model=\"form.Is_Pick_Material\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"加工班组\" prop=\"Working_Team_Ids\">\n          <el-select v-model=\"form.Working_Team_Ids\" multiple style=\"width: 100%\" placeholder=\"请选择加工班组\" :disabled=\"true\">\n            <el-option v-for=\"item in optionsWorkingTeamsList\" :key=\"item.Id\" :label=\"item.Name\" :value=\"item.Id\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"备注\">\n          <el-input v-model=\"form.Remark\" type=\"textarea\" :disabled=\"true\" />\n        </el-form-item>\n        <el-divider content-position=\"left\">质检信息</el-divider>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否自检\" prop=\"Is_Self_Check\">\n              <el-radio-group v-model=\"form.Is_Self_Check\" @change=\"radioSelfCheck\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否互检\" prop=\"Is_Inter_Check\">\n              <el-radio-group v-model=\"form.Is_Inter_Check\" @change=\"radioInterCheck\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否专检\" prop=\"Is_Need_Check\">\n              <el-radio-group v-model=\"form.Is_Need_Check\" @change=\"radioChange\">\n                <el-radio :label=\"true\">是</el-radio>\n                <el-radio :label=\"false\">否</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <template v-if=\"form.Is_Need_Check\">\n              <el-form-item label=\"专检方式\" prop=\"Check_Style\">\n                <el-radio-group v-model=\"form.Check_Style\" @change=\"radioCheckStyleChange\">\n                  <el-radio label=\"0\">抽检</el-radio>\n                  <el-radio label=\"1\">全检</el-radio>\n                </el-radio-group>\n              </el-form-item>\n            </template>\n          </el-col>\n        </el-row>\n\n        <template v-if=\"form.Is_Need_Check\">\n          <el-form-item label=\"专检类型\" prop=\"\">\n            <div>\n              <div style=\"margin-bottom: 10px;\">\n                <el-checkbox v-model=\"form.Is_Need_TC\" @change=\"checkboxChange($event, 1)\">\n                  <span> 探伤</span>\n                </el-checkbox>\n                <span style=\"margin-left: 30px; \">\n                  <span style=\"color: rgba(34, 40, 52, 0.65)\">探伤员：</span>\n                  <el-select\n                    v-model=\"TC_Check_UserIds\"\n                    filterable\n                    clearable\n                    :disabled=\"!form.Is_Need_TC\"\n                    multiple\n                    placeholder=\"请选择探伤员\"\n                    @change=\"changeTc\"\n                  >\n                    <el-option\n                      v-for=\"item in optionsUserList\"\n                      :key=\"item.Id\"\n                      :label=\"item.Display_Name\"\n                      :value=\"item.Id\"\n                    />\n                  </el-select>\n                </span>\n              </div>\n              <div>\n                <el-checkbox v-model=\"form.Is_Need_ZL\" @change=\"checkboxChange($event, 2)\">\n                  <span> 质量</span>\n                </el-checkbox>\n                <span style=\"margin-left: 30px\">\n                  <span style=\"color: rgba(34, 40, 52, 0.65)\">质检员：</span>\n                  <el-select\n                    v-model=\"ZL_Check_UserIds\"\n                    :disabled=\"!form.Is_Need_ZL\"\n                    filterable\n                    clearable\n                    multiple\n                    placeholder=\"请选择质检员\"\n                    @change=\"changeZL\"\n                  >\n                    <el-option\n                      v-for=\"item in optionsUserList\"\n                      :key=\"item.Id\"\n                      :label=\"item.Display_Name\"\n                      :value=\"item.Id\"\n                    />\n                  </el-select>\n                </span>\n              </div>\n            </div>\n          </el-form-item>\n        </template>\n        <el-divider content-position=\"left\">其他信息</el-divider>\n        <el-form-item label=\"工作量占比\" prop=\"Workload_Proportion\">\n          <el-input v-model=\"form.Workload_Proportion\" placeholder=\"请输入\" @input=\"handlePercentageInput\">\n            <template slot=\"append\">%</template>\n          </el-input>\n        </el-form-item>\n        <el-form-item label=\"是否展示模型\" prop=\"Show_Model\">\n          <el-radio-group v-model=\"form.Show_Model\" :disabled=\"true\">\n            <el-radio :label=\"true\">是</el-radio>\n            <el-radio :label=\"false\">否</el-radio>\n          </el-radio-group>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"btn-x\">\n      <el-button @click=\"$emit('close')\">取 消</el-button>\n      <el-button :loading=\"btnLoading\" type=\"primary\" @click=\"handleSubmit\">确 定\n      </el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\nimport { GetUserList } from '@/api/sys'\nimport {\n  GetFactoryPeoplelist,\n  GetCheckGroupList,\n  GetWorkingTeams,\n  SaveProjectProcess\n} from '@/api/PRO/technology-lib'\nimport { mapGetters } from 'vuex'\nimport { GetDictionaryDetailListByCode } from '@/api/sys'\nexport default {\n  props: {\n    type: {\n      type: String,\n      default: ''\n    },\n    rowInfo: {\n      type: Object,\n      default() {\n        return {}\n      }\n    },\n    totalWorkloadProportion: {\n      type: Number,\n      default: 0\n    },\n    sysProjectId: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      checkList: [],\n      btnLoading: false,\n      hiddenPart: false,\n\n      form: {\n        Code: '',\n        Name: '',\n        Bom_Level: '',\n        Month_Avg_Load: '',\n        Coordinate_UserId: '',\n        Sort: undefined,\n        Is_Enable: true,\n        Is_External: false,\n        Is_Nest: false,\n        Is_Need_Check: true,\n        Is_Self_Check: true,\n        Is_Inter_Check: true,\n        Is_Pick_Material: false,\n        Is_Need_TC: true,\n        Is_Welding_Assembling: false,\n        Is_Cutting: false,\n        TC_Check_UserId: '',\n        Is_Need_ZL: false,\n        ZL_Check_UserId: '',\n        Show_Model: false,\n\n        Check_Style: '0',\n\n        Working_Team_Ids: [],\n        Remark: '',\n        Workload_Proportion: ''\n      },\n      ZL_Check_UserIds: [],\n      TC_Check_UserIds: [],\n      CheckChange: true,\n      userOptions: [],\n      optionsUserList: [],\n      optionsGroupList: [],\n      optionsWorkingTeamsList: [],\n      rules: {\n        Code: [\n          { required: true, message: '请输入代号', trigger: 'blur' },\n          { max: 30, message: '长度在 30 个字符内', trigger: 'blur' }\n        ],\n        Name: [\n          { required: true, message: '请输入名称', trigger: 'blur' },\n          { max: 30, message: '长度在 30 个字符内', trigger: 'blur' }\n        ],\n        Bom_Level: [{ required: true, message: '请选择类型', trigger: 'change' }],\n        Sort: [{ required: true, message: '请输入', trigger: 'blur' }],\n        Is_Need_Check: [\n          { required: true, message: '请选择是否质检', trigger: 'change' }\n        ]\n      },\n      Workload_Proportion: 0,\n      bomList: [],\n      comName: '',\n      partName: ''\n    }\n  },\n  computed: {\n    ...mapGetters('tenant', ['isVersionFour']),\n    allocableWorkloadProportion() {\n      return this.totalWorkloadProportion - this.Workload_Proportion\n    }\n  },\n  async created() {\n    const { comName, partName, list } = await GetBOMInfo()\n    this.comName = comName\n    this.partName = partName\n    this.bomList = list\n  },\n  mounted() {\n    this.getUserList()\n    this.getFactoryPeoplelist()\n    // this.getCheckGroupList();\n    this.getWorkingTeamsList()\n    this.type === 'edit' && this.initForm()\n  },\n  methods: {\n    initForm() {\n      const { Is_Nest, ...others } = this.rowInfo\n      this.form = Object.assign({}, others, { Is_Nest: !!Is_Nest })\n      this.form.Bom_Level = String(this.form.Bom_Level)\n      this.Workload_Proportion = this.rowInfo.Workload_Proportion\n      //  if(this.form.Type==2){\n      //   this.form.Types = '0'\n      //  }else if(this.form.Type==3){\n      //   let Types = this.radioList.find(v => ['1', '2','3'].includes(v.Code))?.Code\n      //   console.log('Types', Types)\n      //   console.log('this.radioList', this.radioList)\n      //   this.form.Types = Types\n      //  }else if(this.form.Type==1){\n      //   this.form.Types = '-1'\n      //  }\n      console.log('this.form', this.form)\n\n      // 处理历史数据多选问题\n      // if (this.form.Is_Need_Check) {\n      //   if (this.form.Check_Style === '1') {\n\n      //   } else {\n      //     this.CheckChange = !!this.form.Is_Need_TC\n      //     if (this.form.Is_Need_ZL && this.form.Is_Need_TC) {\n      //       this.form.Is_Need_TC = true\n      //       this.form.Is_Need_ZL = false\n      //     }\n      //   }\n      // }\n      this.ZL_Check_UserIds = this.form.ZL_Check_UserId\n        ? this.form.ZL_Check_UserId.split(',')\n        : []\n      this.TC_Check_UserIds = this.form.TC_Check_UserId\n        ? this.form.TC_Check_UserId.split(',')\n        : []\n    },\n    // 是否自检\n    radioSelfCheck(val) { },\n    // 是否互检\n    radioInterCheck(val) { },\n    // 获取设备类型\n    async getDictionaryDetailListByCode() {\n      await GetDictionaryDetailListByCode({ dictionaryCode: 'deviceType' }).then((res) => {\n        if (res.IsSucceed) {\n          this.deviceTypeList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n      console.log(' this.optionsGroupList', this.optionsGroupList)\n    },\n    getUserList() {\n      GetUserList({}).then((res) => {\n        if (res.IsSucceed) {\n          this.userOptions = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    getFactoryPeoplelist() {\n      GetFactoryPeoplelist({}).then((res) => {\n        if (res.IsSucceed) {\n          this.optionsUserList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    async getCheckGroupList() {\n      await GetCheckGroupList({}).then((res) => {\n        if (res.IsSucceed) {\n          this.optionsGroupList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n      console.log(' this.optionsGroupList', this.optionsGroupList)\n    },\n    getWorkingTeamsList() {\n      GetWorkingTeams({}).then((res) => {\n        if (res.IsSucceed) {\n          this.optionsWorkingTeamsList = res.Data\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    // 选择专检方式 抽检、全检\n    radioCheckStyleChange(val) {\n      // if (val === '0') {\n      //   this.form.Is_Need_TC = true\n      //   this.form.Is_Need_ZL = false\n      // }\n      this.ZL_Check_UserIds = []\n      this.TC_Check_UserIds = []\n      this.form.ZL_Check_UserId = ''\n      this.form.TC_Check_UserId = ''\n    },\n    // 是否专检\n    radioChange(val) {\n      if (val === false && this.type === 'add') {\n        this.form.checkChange = false\n        this.form.Is_Need_TC = false\n        this.form.Is_Need_ZL = false\n        this.TC_Check_UserIds = []\n        this.ZL_Check_UserIds = []\n        this.form.ZL_Check_UserId = ''\n        this.form.TC_Check_UserId = ''\n        this.form.Check_Style = ''\n      } else {\n        // this.form.checkChange = true\n        // this.form.Is_Need_TC = true\n        // this.form.Is_Need_ZL = false\n        // this.CheckChange = !!this.form.Is_Need_TC\n        this.form.Check_Style = '0'\n      }\n    },\n    // 选择BOM层级\n    changeType(val) {\n      // const Code = val\n      // console.log(Code, 'Code');\n      // if (Code === '-1') {\n      //   this.form.Type = 1\n      // } else if (Code === '0') {\n      //   this.form.Type = 2\n      // } else if (Code === '1' || Code === '3'|| Code === '2') {\n      //   this.form.Type = 3\n      // }\n      // if (this.form.Type === 1 || this.form.Type === 3) {\n      //   this.form.Is_Cutting = undefined\n      // } else if (this.form.Type === 2) {\n      //   this.form.Is_Welding_Assembling = undefined\n      // }\n    },\n    typeChange() {\n      this.form.Task_Model = ''\n    },\n    changeTc(val) {\n      console.log(val)\n      this.form.TC_Check_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i === val.length - 1) {\n          this.form.TC_Check_UserId += val[i]\n        } else {\n          this.form.TC_Check_UserId += val[i] + ','\n        }\n      }\n      console.log(this.form.TC_Check_UserId, 'this.form.TC_Check_UserId ')\n    },\n    changeZL(val) {\n      console.log(val)\n      this.form.ZL_Check_UserId = ''\n      for (let i = 0; i < val.length; i++) {\n        if (i === val.length - 1) {\n          this.form.ZL_Check_UserId += val[i]\n        } else {\n          this.form.ZL_Check_UserId += val[i] + ','\n        }\n      }\n    },\n    checkboxChange(val, type) {\n      if (type === 1) {\n        if (!val) {\n          this.TC_Check_UserIds = []\n        }\n      }\n      if (type === 2) {\n        if (!val) {\n          this.ZL_Check_UserIds = []\n        }\n      }\n    },\n    handleSubmit() {\n      // delete this.form.Types\n      console.log(this.form, 'this.form')\n      this.$refs.form.validate((valid) => {\n        if (!valid) return\n        this.btnLoading = true\n        const uItems = this.optionsUserList.find(\n          (v) => v.Id === this.form.Coordinate_UserId\n        )\n        if (uItems) {\n          this.form.Coordinate_UserName = uItems.Display_Name\n        }\n        if (this.form.Is_Need_Check) { // 如果需要专检\n          if (this.form.Is_Need_ZL === false && this.form.Is_Need_TC === false) {\n            this.$message.error('请选择质检类型')\n            this.btnLoading = false\n            return\n          }\n        } else { // 如果不需要专检，则不需要专检方式、专检类型、专检人员  后续需要互检逻辑迭代\n          this.form.checkChange = false\n          this.form.Is_Need_TC = false\n          this.form.Is_Need_ZL = false\n          this.TC_Check_UserIds = []\n          this.ZL_Check_UserIds = []\n          this.form.ZL_Check_UserId = ''\n          this.form.TC_Check_UserId = ''\n          this.form.Check_Style = null\n        }\n        const ZL = this.form.Is_Need_ZL ? this.form.ZL_Check_UserId : ''\n        const TC = this.form.Is_Need_TC ? this.form.TC_Check_UserId : ''\n        if (this.form.Is_Need_ZL && (ZL ?? '') === '') {\n          this.$message.error('请选择质检员')\n          this.btnLoading = false\n          return\n        }\n        if (this.form.Is_Need_TC && (TC ?? '') === '') {\n          this.$message.error('请选择探伤员')\n          this.btnLoading = false\n          return\n        }\n\n        SaveProjectProcess({\n          Bom_Level: this.form.Bom_Level,\n          Workload_Proportion: this.form.Workload_Proportion,\n          Is_Self_Check: this.form.Is_Self_Check,\n          Is_Inter_Check: this.form.Is_Inter_Check,\n          Is_Need_Check: this.form.Is_Need_Check,\n          Check_Style: this.form.Check_Style,\n          Is_Need_TC: this.form.Is_Need_TC,\n          Is_Need_ZL: this.form.Is_Need_ZL,\n          Sys_Project_Id: this.sysProjectId,\n          Process_Id: this.form.Id,\n          ZL_Check_UserId: ZL,\n          TC_Check_UserId: TC\n        }).then((res) => {\n          if (res.IsSucceed) {\n            this.$message({\n              message: '保存成功',\n              type: 'success'\n            })\n            this.$emit('refresh')\n            this.$emit('close')\n          } else {\n            this.$message({\n              message: res.Message,\n              type: 'error'\n            })\n          }\n          this.btnLoading = false\n        })\n      })\n    },\n\n    codeChange(e) {\n      return e.replace(/[^a-zA-Z0-9]/g, '')\n    },\n\n    handlePercentageInput(value) {\n      // 如果输入为空，直接返回\n      if (value === '' || value === null || value === undefined) {\n        this.form.Workload_Proportion = ''\n        return\n      }\n\n      // 转换为字符串进行处理\n      let inputValue = String(value)\n\n      // 只允许数字和一个小数点，移除其他字符（包括负号）\n      inputValue = inputValue.replace(/[^0-9.]/g, '')\n\n      // 确保只有一个小数点\n      const dotCount = (inputValue.match(/\\./g) || []).length\n      if (dotCount > 1) {\n        // 如果有多个小数点，只保留第一个\n        const firstDotIndex = inputValue.indexOf('.')\n        inputValue = inputValue.substring(0, firstDotIndex + 1) + inputValue.substring(firstDotIndex + 1).replace(/\\./g, '')\n      }\n\n      // 如果只是小数点，设置为空\n      if (inputValue === '.') {\n        this.form.Workload_Proportion = ''\n        return\n      }\n\n      // 如果处理后为空字符串，设置为空\n      if (inputValue === '') {\n        this.form.Workload_Proportion = ''\n        return\n      }\n\n      // 限制小数位数最多2位\n      if (inputValue.includes('.')) {\n        const parts = inputValue.split('.')\n        if (parts[1] && parts[1].length > 2) {\n          inputValue = parts[0] + '.' + parts[1].substring(0, 2)\n        }\n      }\n\n      // 转换为数字进行范围检查\n      const numValue = parseFloat(inputValue)\n\n      // 如果不是有效数字，设置为空\n      if (isNaN(numValue)) {\n        this.form.Workload_Proportion = ''\n        return\n      }\n\n      // 根据 totalWorkloadProportion 计算最大值\n      let maxValue = 100\n      let currentTotal = 0\n\n      if (this.totalWorkloadProportion) {\n        // 确保所有数值都是浮点数并保留2位小数进行计算\n        const total = parseFloat(this.totalWorkloadProportion.toFixed(2))\n        const current = parseFloat((this.Workload_Proportion || 0).toString())\n\n        // 计算差值并保留2位小数\n        const difference = total - current\n        currentTotal = parseFloat(difference.toFixed(2))\n\n        // 验证数学一致性：确保 currentTotal + current = total\n        const verification = parseFloat((currentTotal + current).toFixed(2))\n        if (verification !== total) {\n          // 如果不一致，调整 currentTotal 以保证一致性\n          currentTotal = parseFloat((total - current).toFixed(2))\n        }\n      }\n\n      if (currentTotal === 100) {\n        // 如果总和已经是100，最大只能输入0\n        maxValue = 0\n      } else if (currentTotal > 0 && currentTotal < 100) {\n        // 如果总和在0-100之间，最大值是100减去当前总和\n        maxValue = 100 - currentTotal\n      }\n      // 如果总和是0或空，最大值保持100\n\n      // 限制范围在 0-maxValue 之间\n      if (numValue < 0) {\n        this.form.Workload_Proportion = '0'\n      } else if (numValue > maxValue) {\n        // 保留2位小数\n        this.form.Workload_Proportion = maxValue.toFixed(2).replace(/\\.?0+$/, '')\n      } else {\n        // 保持原始输入格式（包括小数点），但确保不超过2位小数\n        this.form.Workload_Proportion = inputValue\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/mixin.scss\";\n\n.btn-del {\n  margin-left: -100px;\n}\n\n.customRadioClass {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.checkboxFlex {\n  display: flex;\n  align-items: center;\n}\n\n.form-wrapper {\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  min-height: 40vh;\n\n  .form-x {\n    max-height: 70vh;\n    overflow: auto;\n    padding-right: 16px;\n    @include scrollBar;\n  }\n\n  .btn-x {\n    padding-top: 16px;\n    text-align: right;\n  }\n}\n</style>\n"]}]}
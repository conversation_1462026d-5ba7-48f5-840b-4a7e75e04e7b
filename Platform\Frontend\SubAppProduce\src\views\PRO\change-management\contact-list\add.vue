<template>
  <div class="page-container">
    <div class="form-x">
      <div>
        <span class="cs-title">工程联系单</span>
      </div>
      <el-divider />
      <el-row v-loading="pageLoading" element-loading-text="加载中">
        <el-form ref="form" :rules="rules" :model="form" label-width="120px">
          <el-col :span="6">
            <el-form-item label="项目名称" prop="Sys_Project_Id">
              <el-select
                v-model="form.Sys_Project_Id"
                :disabled="isView||tbLoading"
                clearable
                class="w100"
                placeholder="请选择"
                filterable
                @change="projectChange()"
              >
                <el-option
                  v-for="item in projectList"
                  :key="item.Sys_Project_Id"
                  :label="item.Short_Name"
                  :value="item.Sys_Project_Id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="区域名称" prop="Area_Id">
              <el-tree-select
                ref="treeSelectArea"
                v-model="form.Area_Id"
                :disabled="!form.Sys_Project_Id||isView||tbLoading"
                :select-params="{
                  clearable: true,

                }"
                :tree-params="treeParamsArea"
                @select-clear="areaClear"
                @node-click="areaChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item ref="installUnitRef" label="批次" prop="InstallUnit_Ids">
              <el-select
                v-model="form.InstallUnit_Ids"
                filterable
                multiple
                class="w100"
                :disabled="isView||!form.Area_Id||tbLoading"
                placeholder="请选择"
                @change="installChange"
              >
                <el-option
                  v-for="item in installUnitList"
                  :key="item.Id"
                  :label="item.Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="变更人" prop="Handle_UserId">
              <el-select
                v-model="form.Handle_UserId"
                :disabled="isView"
                filterable
                class="w100"
                clearable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in peopleList"
                  :key="item.Id"
                  :label="item.Display_Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="变更类型" prop="Moc_Type_Id">
              <el-select
                v-model="form.Moc_Type_Id"
                class="w100"
                :disabled="isView"
                filterable
                placeholder="请选择"
                @change="mocTypeChange"
              >
                <el-option
                  v-for="item in changeTypeList"
                  :key="item.Id"
                  :label="item.Display_Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="变更费用" prop="Fee">
              <el-input-number v-model="form.Fee" placeholder="请输入" style="width: 100%" clearable :disabled="isView" class="cs-number-btn-hidden" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="变更工时" prop="Hours">
              <el-input-number v-model="form.Hours" placeholder="请输入" style="width: 100%" clearable :disabled="isView" class="cs-number-btn-hidden" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="紧急程度" prop="Urgency">
              <el-select v-model="form.Urgency" placeholder="请选择" style="width: 100%" :disabled="isView">
                <el-option label="普通" :value="1" />
                <el-option label="紧急" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="要求完成时间" prop="Demand_Date">
              <el-date-picker
                v-model="form.Demand_Date"
                :disabled="isView"
                value-format="yyyy-MM-dd"
                style="width: 100%"
                type="date"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="费用部门" prop="Fee_DepartId">
              <el-tree-select
                ref="treeSelect"
                v-model="form.Fee_DepartId"
                :disabled="isView"
                :select-params="{
                  clearable: true,
                }"
                :tree-params="treeParams"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="变更说明" prop="Remark">
              <el-input
                v-model="form.Remark"
                type="textarea"
                :disabled="isView"
                :autosize="{ minRows: 3, maxRows: 3}"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="附件" prop="region">
              <OSSUpload
                ref="upload"
                :disabled="isView"
                action=""
                :before-upload="beforeUpload"
                :limit="5"
                multiple
                :file-list="fileList"
                :on-progress="handleProgress"
                :on-exceed="handleExceed"
                :on-error="handleError"
                :on-success="uploadSuccess"
                :on-change="handleChange"
                :on-remove="handleRemove"
                :on-preview="handlePreview"
                show-file-list
                accept=".xls, .xlsx,.pdf,.jpg,.png,.dwg,.doc,.docx"
                btn-icon="el-icon-upload"
                :class="isView ? 'z-upload hiddenBtn' : 'z-upload'"
              >
                <el-button v-if="!isView" :loading="uploadLoading" type="primary">上传文件</el-button>
              </OSSUpload>
            </el-form-item>
          </el-col>
        </el-form>

      </el-row>
    </div>
    <div v-if="isView" class="cs-fee">
      <span class="cs-title">费用变动</span>
      <span class="cs-label">当前金额：<span class="fw cs-blue">{{ finishFee }}元</span></span>
      <el-divider />
      <el-timeline>
        <el-timeline-item
          v-for="(activity, index) in activities"
          :key="index"
          hide-timestamp
        >
          <div class="line-content">
            <span class="fee-name">{{ activity.Create_UserName }}</span>
            <span :class="['fee-num',{'txt-red':activity.isRed,'txt-blue':activity.isBlue}]">{{ getFeeGap(activity,index) }}</span>
            <span class="fee-time">{{ activity.Create_Date }}</span>
            <span class="fee-remark">备注：{{ activity.Remark || '-' }}</span>
          </div>
          <template #dot>
            <span class="circle" />
          </template>
        </el-timeline-item>
      </el-timeline>
    </div>
    <div class="cs-main">
      <template v-if="showDetail">
        <span class="cs-title">变更明细</span>
        <el-divider />

        <el-form inline class="change-method-form">
          <el-form-item label="变更方式：">
            <template v-if="!isView">
              <el-radio :value="changeMethod" :label="1" @input="changeMethodFun(1)">完整清单导入</el-radio>
              <el-radio :value="changeMethod" :label="2" @input="changeMethodFun(2)">部分清单导入</el-radio>
              <el-radio :value="changeMethod" :label="3" @input="changeMethodFun(3)">手动修改</el-radio>
            </template>
            <template v-else>
              {{ changeMethod === 1 ? '完整清单导入' : changeMethod === 2 ? '部分清单导入' : '手动修改' }}
            </template>
          </el-form-item>
        </el-form>
        <div v-if="filePath">
          <i style="color:#409EFF" class="el-icon-download" />
          <el-link type="primary" :underline="false" @click="handelFilePath">{{ getFileName }}</el-link>
        </div>
        <vxe-toolbar ref="xToolbar1" class="cs-toolBar">
          <template #buttons>
            <el-button v-if="!isView && changeMethod !== 3" type="primary" @click="handleImport">导入变更清单</el-button>
            <el-button v-if="!isView && changeMethod === 3" type="primary" @click="handleAdd">添加变更内容</el-button>
            <el-button v-if="!isView && changeMethod === 3" type="danger" :disabled="!multipleSelection.length" plain @click="handleCancelChange">取消变更</el-button>
          </template>
          <template #tools>
            <el-form ref="form2" inline :model="searchForm" label-width="70px">
              <el-form-item label="构件名称">
                <el-input
                  v-model="searchForm.component_name"
                  clearable
                  style="width: 260px;"
                  placeholder="请输入"
                  class="input-with-select"
                >
                  <el-select
                    slot="prepend"
                    v-model="searchForm.component_search_mode"
                    placeholder="请选择"
                    style="width: 100px"
                  >
                    <el-option
                      label="模糊搜索"
                      :value="1"
                    />
                    <el-option
                      label="精确搜索"
                      :value="2"
                    />
                  </el-select>
                </el-input>
              </el-form-item>
              <el-form-item label="部件名称">
                <el-input
                  v-model="searchForm.assembly_name"
                  style="width: 260px;"
                  clearable
                  placeholder="请输入"
                  class="input-with-select"
                >
                  <el-select
                    slot="prepend"
                    v-model="searchForm.assembly_search_mode"
                    placeholder="请选择"
                    style="width: 100px"
                  >
                    <el-option
                      label="模糊搜索"
                      :value="1"
                    />
                    <el-option
                      label="精确搜索"
                      :value="2"
                    />
                  </el-select>
                </el-input>
              </el-form-item>
              <el-form-item label="零件名称">
                <el-input
                  v-model="searchForm.part_name"
                  clearable
                  style="width: 260px;"
                  placeholder="请输入"
                  class="input-with-select"
                >
                  <el-select
                    slot="prepend"
                    v-model="searchForm.part_search_mode"
                    placeholder="请选择"
                    style="width: 100px"
                  >
                    <el-option
                      label="模糊搜索"
                      :value="1"
                    />
                    <el-option
                      label="精确搜索"
                      :value="2"
                    />
                  </el-select>
                </el-input>
              </el-form-item>
              <el-form-item>
                <el-button @click="handleReset">重置</el-button>
                <el-button type="primary" @click="handleFilter">搜索</el-button>
              </el-form-item>
            </el-form>
          </template>
        </vxe-toolbar>
        <div class="fff tb-x">
          <vxe-table
            id="uuid"
            ref="tableRef"
            :empty-render="{name: 'NotData'}"
            show-header-overflow
            :loading="tbLoading"
            element-loading-spinner="el-icon-loading"
            element-loading-text="拼命加载中"
            empty-text="暂无数据"
            class="cs-vxe-table cs-tree-table"
            height="500"
            stripe
            :filter-config="{showIcon: false}"
            :row-config="{keyField:'uuid', 'isHover': true, 'isCurrent': true}"
            :data="tbData"
            resizable
            :checkbox-config="{checkField: 'checked',labelField: 'CPCode', highlight: true}"
            :tree-config="{transform: true, showIcon: true, rowField: 'parentChildrenId', parentField: 'ParentId'}"
            :tooltip-config="{ enterable: true}"
            @checkbox-all="multiSelectedChange"
            @checkbox-change="multiSelectedChange"
          >
            <vxe-column v-if="changeMethod === 3 && !isView" fixed="left" type="checkbox" width="60" />
            <!-- :type="index===0 && changeMethod === 3 && !isView? 'checkbox':''" -->

            <template v-for="(item,index) in columns">
              <vxe-column
                :key="item.Code+changeMethod"
                :tree-node="index===0"
                :fixed="item.Is_Frozen ? (item.Frozen_Dirction || 'left') : ''"
                :min-width="item.Width"
                show-overflow="tooltip"
                sortable
                :align="item.Align"
                :field="item.Code"
                :filters="item.Code==='CPCode' ? filterCodeOptions : null"
                :filter-method="item.Code==='CPCode' ? filterNameMethod : null"
                :title="item.Display_Name"
              >
                <template v-if="item.Code==='CPCode'" #filter="{ $panel, column }">
                  <input v-for="(option, index1) in column.filters" :key="index1" v-model="option.data" type="type" @input="$panel.changeOption($event, !!option.data, option)">
                </template>
                <template v-if="item.Code==='CPCode'" #default="{ row }">
                  {{ getCpCode(row) }}
                  <el-tag v-if="row.Type===0" size="mini">构</el-tag>
                  <el-tag v-else-if="row.Type===1" type="warning" size="mini">部</el-tag>
                  <el-tag v-else type="success" size="mini">零</el-tag>
                </template>
                <template v-else-if="item.Code === 'changeContent'" #default="{ row }">

                  <el-popover
                    placement="left-start"
                    width="400"
                    trigger="click"
                    @show="handleShow(row)"
                  >
                    <el-table max-height="300" stripe resizable class="cs-custom-table" :data="changeRowContentList">
                      <el-table-column align="center" property="Name" width="100" label="变更字段" />
                      <el-table-column align="center" property="Value" label="变更前" />
                      <el-table-column align="center" property="NewValue" label="变更后" />
                    </el-table>
                    <span slot="reference" style="cursor: pointer;" :class="getChangeStyle(row.changeContent)">
                      {{ row.changeContent }}
                    </span>
                  </el-popover>

                </template>
                <template v-else-if="item.Code === 'Production_Status'" #default="{ row }">
                  <el-link v-if="row.MocIdBefore&& row[item.Code]" :type="row.Production_Status === '未生产' ? 'info' : 'primary'" @click="handleOpen(row)"> {{ row[item.Code] | displayValue }}</el-link>
                  <span v-else>-</span>
                </template>
                <template v-else-if="item.Code === 'Is_Component_Mark'" #default="{ row }">
                  <!--             是否直发件 是：是直发件；否：非直发件；-->
                  <template v-if="row.Type===0">
                    <el-tag v-if="row[item.Code]==='是'" type="primary">是</el-tag>
                    <el-tag v-else type="danger">否</el-tag>
                  </template>
                  <template v-else>
                    <el-tag v-if="row.PartType==='直发件'" type="primary">是</el-tag>
                    <el-tag v-else type="danger">否</el-tag>
                  </template>

                </template>
                <template v-else #default="{ row }">
                  <span> {{ row[item.Code] | displayValue }}</span>
                </template>
              </vxe-column>
            </template>

            <!-- Operations column for manual edit mode -->
            <vxe-column v-if="changeMethod === 3 && !isView" fixed="right" title="操作" width="160">
              <template #default="{ row }">
                <el-button
                  v-if="row.changeType !== 'isDelete'"
                  type="text"
                  size="small"
                  @click="handleEdit(row)"
                >
                  编辑
                </el-button>
                <el-button
                  v-if="row.changeType !== 'isDelete'"
                  type="text"
                  size="small"
                  style="color: #FB6B7F;"
                  @click="handleDelete(row)"
                >
                  删除
                </el-button>
                <el-button
                  v-if="row.changeType === 'isDelete' && !row.isDisabled"
                  type="text"
                  size="small"
                  style="color: #298DFF;"
                  @click="handleRestore(row)"
                >
                  撤销删除
                </el-button>
              </template>
            </vxe-column>
          </vxe-table>
        </div>
      </template>
      <footer v-if="!isView">
        <el-button :disabled="disableSave" :loading="saveLoading" @click="handleSave">保存草稿</el-button>
        <el-button :disabled="disableSave || uploadLoading" :loading="submitLoading" type="primary" @click="handleSubmit">提交审核</el-button>
      </footer>
      <footer v-if="$route.query.operate === 'audit' && $route.query.type==='2'">
        <processHead :no-style="true" :process-id="$route.query.processId" :business-id="$route.query.id" :web-id="$route.query.webId" @afterapproval="afterApproval" />
      </footer>
    </div>
    <ImportFile ref="dialog" @refresh="getTableInfo" />
    <StatusDialog ref="statusDialog" />
    <el-dialog
      v-if="dialogVisible"
      v-dialogDrag
      top="6vh"
      :title="title"
      :visible.sync="dialogVisible"
      :width="width"
      class="plm-custom-dialog"
      @close="handleClose"
    >
      <component
        :is="currentComponent"
        ref="content"
        @getMocModelList="getMocModelList"
        @editInfo="editInfo"
        @close="handleClose"
      />
    </el-dialog>
  </div>
</template>

<script>
import { GeAreaTrees, GetInstallUnitIdNameList, GetProjectPageList } from '@/api/PRO/project'
import { GetFactoryPeoplelist } from '@/api/PRO/basic-information/workshop'
import {
  GetMocOrderInfo,
  GetMocOrderTypeList,
  ImportChangFile,
  SaveMocOrder,
  SubmitMocOrder
} from '@/api/PRO/changeManagement'
import { GetCompanyDepartTree, GetOssUrl } from '@/api/sys'
import ImportFile from './components/importFile.vue'
import OSSUpload from '@/views/plm/components/ossupload.vue'
import { closeTagView, combineURL, debounce, deepClone } from '@/utils'
import StatusDialog from './components/dialog.vue'
import numeral from 'numeral'
import { GetTableSettingList } from '@/api/PRO/component-type'
import HandleEdit from './components/HandleEdit.vue'
import AddHandle from './components/addHandle.vue'
import { changeType, changeTypeReverse, getAllCodesByType } from './utils'
import { getFileNameFromUrl } from '@/utils/file'
import { isArray } from 'ali-oss/lib/common/utils/isArray'
import SteelComponentManager from '@/views/PRO/change-management/contact-list/info'
import { GetCurFactory } from '@/api/PRO/factory'
import processHead from '@/views/PRO/components/processHead'

export default {
  name: 'PROEngineeringChangeOrderAdd',
  components: {
    OSSUpload,
    StatusDialog,
    ImportFile,
    HandleEdit,
    AddHandle,
    processHead
  },
  mixins: [SteelComponentManager],
  data() {
    return {
      factoryReferenceId: '',
      curStatus: {
        del: '已删',
        change: '变更',
        add: '新增',
        increase: '数量增加',
        decrease: '数量减少',
        unChange: '无变更'
      },

      dialogVisible: false,
      title: '',
      width: '50%',
      currentComponent: '',
      filePath: '',
      finishFee: 0,
      pageLoading: false,
      saveLoading: false,
      submitLoading: false,
      uploadLoading: false,
      tbLoading: false,
      tbData: [],
      activities: [],
      fileList: [],
      multipleSelection: [],
      changeRowContentList: [],
      filterCodeOptions: [{ data: '' }],
      columns: [],
      changeMethod: 1,
      searchForm: {
        component_name: '',
        part_name: '',
        assembly_name: '',
        component_search_mode: 1,
        part_search_mode: 1,
        assembly_search_mode: 1,
        content: ''
      },
      form: {
        Sys_Project_Id: '',
        Area_Id: '',
        InstallUnit_Ids: [],
        Handle_UserId: '',
        Moc_Type_Id: '',
        Fee: undefined,
        Hours: undefined,
        Urgency: 1,
        Demand_Date: '',
        Fee_DepartId: '',
        Remark: '',
        AttachmentList: []
      },
      showImport: false,
      treeParams: {
        data: [],
        filterable: false,
        clickParent: true,
        props: {
          disabled: 'disabled',
          children: 'Children',
          label: 'Label',
          value: 'Id'
        }
      },
      peopleList: [],
      projectList: [],
      changeTypeList: [],
      installUnitList: [],
      treeParamsArea: {
        'default-expand-all': true,
        filterable: false,
        clickParent: true,
        data: [],
        props: {
          disabled: 'disabled',
          children: 'Children',
          label: 'Label',
          value: 'Id'
        }
      },
      rules: {
        Sys_Project_Id: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        Area_Id: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        Handle_UserId: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        Moc_Type_Id: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        InstallUnit_Ids: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      },
      toolbarButtons: [
        { code: 'myToolbarExport', name: '点击导出' },
        { code: 'myToolbarLink', name: '点击跳转' },
        { code: 'myToolbarCustom', name: '打开自定义列' }
      ]
    }
  },
  computed: {
    disableSave() {
      return this.submitLoading || this.uploadLoading
    },
    isView() {
      return this.$route.query.type == '2'
    },
    isEdit() {
      return this.$route.query.type == '1'
    },
    showDetail() {
      const zbtk = this.changeTypeList.find(item => {
        return item.Id === this.form.Moc_Type_Id
      })
      return zbtk?.Is_Deepen_Change || false
    },
    getFileName() {
      return getFileNameFromUrl(this.filePath)
    }
  },
  watch: {
    'form.Area_Id': {
      handler(newVal) {
        if (!newVal) {
          this.rules.InstallUnit_Ids[0].required = false
          this.$refs?.installUnitRef?.clearValidate()
        }
      },
      immediate: true
    },
    'form.InstallUnit_Ids': {
      handler(newVal) {
        if (!this.Area_Id) return
        if (this.installUnitList.length) {
          this.rules.InstallUnit_Ids[0].required = true
          this.$refs.installUnitRef.clearValidate()
        } else {
          this.rules.InstallUnit_Ids[0].required = false
          this.$refs.installUnitRef.clearValidate()
        }
      }
    }
  },
  async created() {
    const PermittedFactoryList = JSON.parse(localStorage.getItem('PermittedFactoryList')) || []
    this.factoryReferenceId = PermittedFactoryList.find(item => item.Id === this.$route.query.factoryId)?.Reference_Id || ''
    // await this.getTableConfig('PROEngChangeOrderAdd') ProfessionalCode
    this.$store.dispatch('contactList/resetChangeCode')
    this.getProjectData()
    this.getFactoryPeople()
    await this.getDepTree()
    this.getFactoryChangeTypeList()

    const id = this.$route.query.id
    id && await this.getInfo(id)
    this.getTableConfig()
    // this.getTableInfo()
  },
  methods: {
    async getTableConfig() {
      const res = await GetTableSettingList({ ProfessionalCode: 'Steel' })
      if (res.IsSucceed) {
        this.allCodes = JSON.parse(JSON.stringify(res.Data))
        // Filter out the three name columns
        const filteredColumns = res.Data.filter(item =>
          !['SteelName', 'ComponentName', 'PartName'].includes(item.Code)
        )

        // Add the new CPCode column
        const codeColumn = {
          Code: 'CPCode',
          Display_Name: '构件/部件/零件名称',
          Is_Frozen: true,
          Frozen_Dirction: 'left',
          Width: 180,
          Align: 'left'
        }

        // Insert the CPCode column at the beginning
        filteredColumns.unshift(codeColumn)
        const _customColumns = [{
          Code: 'Production_Status',
          Display_Name: '生产情况',
          Align: 'center',
          Is_Frozen: true,
          Frozen_Dirction: 'right',
          Width: 120
        }, {
          Code: 'changeContent',
          Display_Name: '变更内容',
          Align: 'center',
          Is_Frozen: true,
          Frozen_Dirction: 'right',
          Width: 120
        }]

        filteredColumns.push(..._customColumns)
        let _columns = []

        this.rootColumns = deepClone(filteredColumns.map(item => {
          const displayNameLength = item.Display_Name?.length || 0
          const width = Math.max(120, 120 + Math.max(0, displayNameLength - 4) * 10)
          return {
            ...item,
            Width: width,
            Align: 'center'
          }
        }))

        if (this.changeMethod === 3) {
          const columnCode = ['CPCode', 'SetupPosition', 'Production_Status', 'changeContent']
          _columns = this.rootColumns.filter(item => columnCode.includes(item.Code))
        } else {
          _columns = this.rootColumns
        }
        this.columns = _columns
      }
    },
    async getInfo(id) {
      this.pageLoading = true
      await GetMocOrderInfo({
        Id: id,
        factoryReferenceId: this.factoryReferenceId
      }).then(async res => {
        if (res.IsSucceed) {
          const {
            Deepen_File_Url,
            Sys_Project_Id,
            Area_Id,
            InstallUnit_Ids,
            Handle_UserId,
            Moc_Type_Id,
            Change_Type,
            Fee,
            Hours,
            Urgency,
            Demand_Date,
            Fee_DepartId,
            Remark,
            FeeHistory,
            Id,
            Status,
            AttachmentList,
            OrderDetail
          } = res.Data
          this.activities = FeeHistory
          if (FeeHistory.length) {
            const [last] = FeeHistory.slice(-1)
            this.finishFee = numeral(last?.Fee || 0).format('0.[00]')
          }
          if (Status === 3) {
            const idx = this.columns.findIndex(item => item.Code === 'Production_Status')
            if (idx !== -1) {
              this.columns.splice(idx, 1)
            }
          }

          if (AttachmentList?.length) {
            AttachmentList.forEach((element, idx) => {
              const obj = {
                name: element.File_Name,
                url: element.File_Url
              }
              this.fileList.push(obj)
              this.form.AttachmentList.push({
                File_Url: element.File_Url,
                File_Name: element.File_Name
              })
            })
          }
          await this.getAreaList(Sys_Project_Id)
          await this.getInstallUnitPageList(Area_Id)

          Object.assign(this.form, {
            ...res.Data,
            Sys_Project_Id,
            Area_Id,
            Deepen_File_Url,
            Id,
            InstallUnit_Ids: InstallUnit_Ids ? (typeof InstallUnit_Ids === 'string' ? InstallUnit_Ids.split(',') : InstallUnit_Ids) : [],
            Handle_UserId,
            Moc_Type_Id,
            Fee: Fee || undefined,
            Hour: Hours || undefined,
            Urgency: Number(Urgency),
            Demand_Date,
            Fee_DepartId,
            Remark
          })

          this.setTbData(OrderDetail)
          this.filePath = Deepen_File_Url
          this.changeMethod = Change_Type === 0 ? 1 : Change_Type === 1 ? 2 : 3
          // Deepen_File_Url
          // setTimeout(() => {
          // Deepen_File_Url && this.getTableInfo({ File_Url: Deepen_File_Url })
          // }, 0)
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
        this.pageLoading = false
      })
    },
    mocTypeChange() {
      this.tbData = []
      this.handleReset()
    },

    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 5 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    handleProgress(event, files, fileList) {
    },
    handleError(err, files, fileList) {
      console.log('err3', err, files, fileList)
      this.checkUploading(fileList)
    },
    checkUploading(fileList) {
      const flag = fileList.every(v => v.status === 'success')
      flag && (this.uploadLoading = false)
    },
    handleImport() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          console.log('valid', valid)
          this.$refs['dialog'].handleOpen()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    handleAdd() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.title = '添加变更内容'
          this.width = '70%'
          this.currentComponent = 'addHandle'
          this.dialogVisible = true
          this.$nextTick(_ => {
            this.$refs['content'].handleOpen(this.form, this.tbData)
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    handleOpen(row) {
      this.$refs['statusDialog'].handleOpen(row)
    },
    getProjectData() {
      GetProjectPageList({ PageSize: -1, factoryReferenceId: this.factoryReferenceId }).then((res) => {
        if (res.IsSucceed) {
          this.projectList = res.Data.Data
        }
      })
    },
    setDisabledTree(root) {
      if (!root) return

      root.forEach((element) => {
        const { Children } = element
        if (Children && Children.length) {
          element.disabled = true
        } else {
          element.disabled = false
          this.setDisabledTree(Children)
        }
      })
    },
    async getAreaList(Pid) {
      await GeAreaTrees({
        sysProjectId: Pid,
        factoryReferenceId: this.factoryReferenceId
      }).then(res => {
        if (res.IsSucceed) {
          const tree = res.Data
          this.setDisabledTree(tree)
          this.treeParamsArea.data = res.Data
          this.$nextTick(_ => {
            this.$refs.treeSelectArea.treeDataUpdateFun(res.Data)
          })
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    async getInstallUnitPageList(areaId) {
      await GetInstallUnitIdNameList({
        Area_Id: areaId,
        Page: 1,
        PageSize: -1
      }).then((res) => {
        if (res.IsSucceed) {
          this.installUnitList = res.Data
          if (this.installUnitList.length) {
            this.rules.InstallUnit_Ids[0].required = true
          } else {
            this.rules.InstallUnit_Ids[0].required = false
          }
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    async projectChange() {
      const Sys_Project_Id = this.form.Sys_Project_Id
      this.clearTb()
      this.form.Area_Id = ''
      this.form.InstallUnit_Ids = []
      this.treeParamsArea.data = []
      this.$nextTick(_ => {
        this.$refs.treeSelectArea.treeDataUpdateFun([])
      })
      if (Sys_Project_Id) {
        await this.getAreaList(Sys_Project_Id)
      }
    },
    async areaChange() {
      this.clearTb()
      await this.getInstallUnitPageList(this.form.Area_Id)
      if (this.installUnitList.length && this.form.Area_Id) {
        this.form.InstallUnit_Ids = [this.installUnitList[0].Id]
        this.rules.InstallUnit_Ids[0].required = true
      } else {
        this.rules.InstallUnit_Ids[0].required = false
        this.$refs.installUnitRef.clearValidate()
      }
    },
    areaClear() {
      this.form.Area_Id = ''
      this.form.InstallUnit_Ids = []
      this.clearTb()
    },
    handleClose() {
      this.dialogVisible = false
    },
    getFactoryPeople() {
      GetFactoryPeoplelist({ factoryReferenceId: this.factoryReferenceId }).then(res => {
        if (res.IsSucceed) {
          this.peopleList = Object.freeze(res.Data || [])

          const curId = localStorage.getItem('UserId')
          const cur = this.peopleList.find(v => v.Id === curId)
          if (cur) {
            this.form.Handle_UserId = cur.Id
          }
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    getFactoryChangeTypeList() {
      GetMocOrderTypeList({ factoryReferenceId: this.factoryReferenceId }).then(res => {
        if (res.IsSucceed) {
          this.changeTypeList = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    async getDepTree() {
      const getFactoryDeptId = async() => {
        return await GetCurFactory({ factoryReferenceId: this.factoryReferenceId }).then((res) => {
          if (res.IsSucceed) {
            return res?.Data[0]?.Dept_Id
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      }

      const getDept = async(depId) => {
        await GetCompanyDepartTree({}).then(res => {
          if (res.IsSucceed) {
            const origin = res.Data?.[0]
            if (origin.Children.length) {
              const tree = origin.Children.filter(v => v.Id === depId)

              const disableDirectory = (treeArray) => {
                treeArray.map(element => {
                  if (element.Children && element.Children.length > 0) {
                    element.disabled = true
                    disableDirectory(element.Children)
                  }
                })
              }
              disableDirectory(tree)
              this.$refs.treeSelect.treeDataUpdateFun(tree || [])
            }
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      }
      const depId = await getFactoryDeptId()
      await getDept(depId)
    },
    getTableInfo(fileObj) {
      this.tbLoading = true
      const form = { ...this.form }
      ImportChangFile({
        ...form,
        ImportType: this.changeMethod,
        InstallUnit_Ids: this.form.InstallUnit_Ids.toString(),
        AttachmentList: [fileObj]
      }).then(res => {
        if (res.IsSucceed) {
          this.isImportFile = true
          const { AttachmentList, MocOrderDetailList } = res.Data
          // this.getTbList(Orignal_Deepen_List, Import_Deepen_List)
          // filePath
          if (AttachmentList.length) {
            this.filePath = AttachmentList[0].File_Url
            this.form.Deepen_File_Url = this.filePath
            this.form.Deepen_File_Url_List = [fileObj]
          }
          this.setTbData(MocOrderDetailList)
        } else {
          if (res.Data && res.Data.ErrorFileUrl) {
            window.open(combineURL(this.$baseUrl, res.Data.ErrorFileUrl), '_blank')
          }
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
        this.tbLoading = false
      })
    },
    setAllWeight(row) {
      return {
        SteelAllWeight: numeral(row.SteelWeight).multiply(row.SteelAmount).format('0.[000]')
      }
    },
    setTbData(list) {
      const setItem = (list, item, isAfterValue = false) => {
        const updatedItem = {
          ...item,
          CodeType: item.Type === 0 ? 1 : item.Type === 1 ? 2 : 3,
          parentChildrenId: item.Id,
          uuid: item.MocIdBefore,
          checked: false,
          changeContent: item.MocType,
          changeType: changeTypeReverse[item.MocType],
          ...this.setAllWeight(item)
        }
        this.setItemMocContent(updatedItem, isAfterValue)
        if (updatedItem.changeType === 'isDelete') {
          const childrenItems = this.findChildItems(updatedItem, list)
          if (childrenItems.length) {
            childrenItems.forEach(childItem => {
              childItem.isDisabled = true
            })
          }
        }
        return updatedItem
      }

      this.defaultTbData = list.map(item => setItem(list, item))

      this.tbData = list.map(item => setItem(list, item, true))
    },
    setItemMocContent(item, isAfterValue = false) {
      if (item.MocContent) {
        let _MocContent = JSON.parse(item.MocContent)
        if (isArray(_MocContent) && _MocContent.length) {
          _MocContent = _MocContent.filter(m => m.ChangeFieldCode !== 'PartNum')
          const _list = _MocContent.map(m => {
            const _codes = getAllCodesByType(item.CodeType)
            const cur = _codes.find(v => v.Code === m.ChangeFieldCode)
            item[m.ChangeFieldCode] = isAfterValue ? m.AfterValue : m.BeforeValue

            return {
              Field_Type: cur?.Field_Type || 'string',
              IsCoreField: cur?.IsCoreField || false,
              Code: m.ChangeFieldCode,
              Name: m.ChangeFieldName,
              Value: m.BeforeValue,
              NewValue: m.AfterValue
            }
          })
          this.$store.dispatch('contactList/addChangeCode', { uuid: item.uuid, list: _list })
        }
      }
    },
    beforeUpload(file) {
      this.uploadLoading = true
    },
    handleChange(file, fileList) {
      this.fileList = fileList
    },
    async handlePreview(file) {
      console.log(file)
      const arr = file.name.split('.')
      const isDwg = arr[arr.length - 1] === 'dwg'
      const { Data } = await GetOssUrl({ url: file.url })
      if (isDwg) {
        window.open('http://dwgv1.bimtk.com:5432/?CadUrl=' + Data, '_blank')
      } else {
        window.open(Data)
      }
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
      this.checkUploading(fileList)
      this.form.AttachmentList = this.fileList.map(item => {
        if (item.url) {
          return {
            File_Url: item.url,
            File_Name: item.name
          }
        } else {
          const url = item.response.Data
          const fileInfo = url.split('*')
          const fileObj = {
            File_Url: fileInfo[0],
            File_Size: fileInfo[1],
            File_Type: fileInfo[2],
            File_Name: fileInfo[3]
          }
          return {
            File_Url: fileObj.File_Url,
            File_Name: fileObj.File_Name
          }
        }
      })
    },
    uploadSuccess(response, file, fileList) {
      if (!response || !response.Data) {
        return
      }
      this.checkUploading(fileList)
      this.form.AttachmentList = this.fileList.map(item => {
        if (item.url) {
          return {
            File_Url: item.url,
            File_Name: item.name
          }
        } else {
          if (item.status !== 'success') return
          const url = item.response.Data
          const fileInfo = url.split('*')
          const fileObj = {
            File_Url: fileInfo[0],
            File_Size: fileInfo[1],
            File_Type: fileInfo[2],
            File_Name: fileInfo[3]
          }
          return {
            File_Url: fileObj.File_Url,
            File_Name: fileObj.File_Name
          }
        }
      })
    },

    handleSave() {
      this.$refs['form'].validate(async(valid) => {
        if (valid) {
          this.saveLoading = true
          await this.submit(true)
          this.saveLoading = false
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    async handleSubmit() {
      this.$refs['form'].validate(async(valid) => {
        if (valid) {
          this.submitLoading = true
          await this.submit(false)
          this.submitLoading = false
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    async submit(isDraft) {
      console.log('this.form', this.form)
      const _form = { ...this.form }
      let submitTb = []
      if (this.changeMethod === 3) {
        submitTb = this.tbData.map((item) => {
          const { children, uuid, changeContent, checked, changeType, isShow, ...others } = item
          const changeMap = this.$store.state.contactList.changeCode
          const _list = (changeMap[uuid] || []).map(v => {
            others[v.Code] = v.NewValue
            return {
              ChangeFieldCode: v.Code,
              ChangeFieldName: v.Name,
              BeforeValue: v.Value,
              AfterValue: v.NewValue
            }
          })
          others.MocContent = JSON.stringify(_list)
          others.MocType = changeContent
          return others
        })
        console.log(JSON.parse(JSON.stringify(submitTb)))
        _form.Deepen_File_Url = null
        _form.Deepen_File_Url_List = null
      } else {
        submitTb = this.tbData
      }
      const isReNew = this.isImportFile && this.changeMethod !== 3 && this.isEdit
      const subObj = {
        ..._form,
        IsNewImportFile: isReNew,
        Handle_UserName: localStorage.getItem('UserName'),
        Moc_Type_Name: this.changeTypeList.find(item => item.Id === _form.Moc_Type_Id)?.Display_Name,
        Change_Type: this.changeMethod === 1 ? 0 : this.changeMethod === 2 ? 1 : 2,
        InstallUnit_Ids: Array.isArray(_form.InstallUnit_Ids) ? _form.InstallUnit_Ids.join(',') : _form.InstallUnit_Ids,
        Is_Draft: isDraft,
        OrderDetail: submitTb
      }
      if (this.changeMethod !== 3) {
        subObj.Deepen_File_Url = this.filePath
      }
      await SaveMocOrder(subObj).then(async res => {
        if (res.IsSucceed) {
          if (isDraft) {
            this.$message({
              message: '保存成功',
              type: 'success'
            })
            closeTagView(this.$store, this.$route)
          } else {
            if (!res.Data) {
              this.$message({
                message: '提交失败',
                type: 'wrarning'
              })
              return
            }
            await this.submitCheck(res.Data)
          }
        } else {
          if (res.Data && res.Data.Path) {
            window.open(combineURL(this.$baseUrl, res.Data.Path), '_blank')
          }
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    async submitCheck(Id) {
      await SubmitMocOrder({
        Id
      }).then(res => {
        if (res.IsSucceed) {
          this.$message({
            message: '提交成功',
            type: 'success'
          })
          closeTagView(this.$store, this.$route)
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    handleReset() {
      this.searchForm.component_name = ''
      this.searchForm.part_name = ''
      this.searchForm.assembly_name = ''
      this.searchForm.component_search_mode = 1
      this.searchForm.part_search_mode = 1
      this.searchForm.assembly_search_mode = 1
      this.$refs?.tableRef?.setAllTreeExpand(false)
      this.$refs?.tableRef?.clearFilter()
    },
    clearTb() {
      this.tbData = []
      this.defaultTbData = []
      this.handleReset()
    },
    handleFilter() {
      this.nameMapping = {
        ComponentName: {},
        SteelName: {},
        PartName: {}
      }
      const changeMaps = this.$store.state.contactList.changeCode
      Object.keys(changeMaps).forEach(uuid => {
        const changeList = changeMaps[uuid]
        changeList.forEach(item => {
          if (item.Code === 'ComponentName') {
            this.nameMapping.ComponentName[item.Value] = item.NewValue
          } else if (item.Code === 'SteelName') {
            this.nameMapping.SteelName[item.Value] = item.NewValue
          } else if (item.Code === 'PartName') {
            this.nameMapping.PartName[item.Value] = item.NewValue
          }
        })
      })

      const xTable = this.$refs.tableRef
      const codeColumn = xTable.getColumnByField('CPCode')
      const option = codeColumn.filters[0]
      option.data = [this.searchForm.component_name, this.searchForm.assembly_name, this.searchForm.part_name]
      option.checked = true
      xTable.updateData()
      this.$refs.tableRef.setAllTreeExpand(true)
      this.$refs.tableRef.clearCheckboxRow()
    },

    getFeeGap(activity, index) {
      if (index === 0) {
        return activity.Fee || 0
      } else {
        const result = numeral(activity.Fee || 0)
          .subtract(this.activities[index - 1].Fee || 0)

        if (result.value() < 0) {
          activity.isRed = true
        } else if (result.value() > 0) {
          activity.isBlue = true
        }
        return result.value() === 0 ? 0 : result.format('+0.[00]')
      }
    },
    handleEdit(row) {
      console.log(row, 'row')
      this.dialogVisible = true
      this.currentComponent = 'HandleEdit'
      this.width = '50%'
      this.title = `编辑（${this.getCpCode(row)}）`
      this.$nextTick(() => {
        const defaultRow = this.defaultTbData.find(item => item.uuid === row.uuid)
        console.log(defaultRow, 'defaultRow')
        this.$refs.content?.init(row, defaultRow, this.isEdit, this.tbData, this.allCodes)
      })
    },
    handleDelete(row) {
      console.log('row', row)
      this.$confirm('确认要删除这条记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteTableItem(row.uuid)
      }).catch(() => {
        // User canceled
      })
    },
    handleRestore(row) {
      this.restoreTableItem(row.uuid)
    },
    changeMethodFun(val) {
      console.log('val', val)
      const setTbCloumn = () => {
        if (val === 3) {
          const columnCode = ['CPCode', 'SetupPosition', 'Production_Status', 'changeContent']
          const _columns = this.columns.filter(item => columnCode.includes(item.Code))
          this.columns = _columns
          this.$nextTick(_ => {
            this.$refs.tableRef.refreshColumn()
          })
        } else if (val === 1) {
          this.columns = deepClone(this.rootColumns)
        } else {
          this.columns = deepClone(this.rootColumns)
        }
        this.$store.dispatch('contactList/resetChangeCode')
        this.changeMethod = val
      }

      if (this.tbData && this.tbData.length > 0) {
        return this.$confirm('切换变更方式会清空当前已添加的变更明细，是否继续？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.tbData = []
          setTbCloumn()
          this.filePath = ''
        }).catch(() => {

        })
      } else {
        this.filePath = ''
        setTbCloumn()
      }
    },
    getMocModelList(list) {
      const existingUuids = new Set(this.tbData.map(item => item.uuid))
      list = list.filter(item => !existingUuids.has(item.MocIdBefore))

      if (!list.length) {
        return
      }

      list = list.map(item => {
        // const curParent = this.findParentItem(item)
        // if (curParent && curParent.changeType === 'isDelete') {
        //   item.changeType = 'isDelete'
        //   item.changeContent = this.getChangeTypeText(item.changeType)
        //   item.isDisabled = true
        //   this.deleteTableItem(item.uuid)
        //   return {
        //     ...item,
        //     parentChildrenId: item.Id,
        //     uuid: item.MocIdBefore,
        //     checked: false,
        //     CodeType: item.Type === 0 ? 1 : item.Type === 1 ? 2 : 3,
        //     ...this.setAllWeight(item)
        //   }
        // } else {
        this.updateItemChangeStatus(item, [])
        return {
          changeType: 'isNoChange',
          changeContent: this.getChangeTypeText('isNoChange'),
          ...item,
          parentChildrenId: item.Id,
          uuid: item.MocIdBefore,
          checked: false,
          CodeType: item.Type === 0 ? 1 : item.Type === 1 ? 2 : 3,
          ...this.setAllWeight(item)
        }
        // }
      })

      this.tbData = [...this.tbData, ...list].sort((a, b) => b.Type - a.Type)

      const _defaultTbData = this.defaultTbData || []
      this.defaultTbData = JSON.parse(JSON.stringify([..._defaultTbData, ...list]))
      this.setSameItems(this.tbData)
    },
    setSameItems(tbData) {
      const changeInfos = { ...this.$store.state.contactList.changeCode }
      const mocBeforeItems = tbData.filter(item => {
        return !!changeInfos[item.uuid]
      })
      const isDeleteItems = tbData.filter(item => item.changeType === 'isDelete')
      if (isDeleteItems.length) {
        console.log(isDeleteItems, 'isDeleteItems')
        const unitPart = isDeleteItems.filter(item => item.Type === 3)
        if (unitPart.length) {
          unitPart.forEach(item => {
            const unitP = this.findParentItem(item)
            if (unitP && unitP.changeType !== 'isDelete') {
              const similarUnitPartItems = this.findSimilarItems(item)
              if (similarUnitPartItems.length) {
                similarUnitPartItems.forEach(similarItem => {
                  const isSame = this.isSameParent(item, similarItem)
                  if (!isSame) return
                  this.$set(similarItem, 'changeType', 'isDelete')
                  this.$set(similarItem, 'changeContent', this.getChangeTypeText('isDelete'))
                })
              }
            }
          })
        }
        // isDeleteItems.forEach(item => {
        //   const similarItems = this.findSimilarItems(item)
        //   if (similarItems.length) {
        //     similarItems.forEach(similarItem => {
        //       console.log(item.Code, 'similarItems')
        //       this.$set(similarItem, 'changeType', item.changeType)
        //       this.$set(similarItem, 'changeContent', item.changeContent)
        //       // const isDisabled = this.isSameParent(item, similarItem)
        //       // this.$set(similarItem, 'isDisabled', !isDisabled)
        //       // if (isDisabled) {
        // this.$set(similarItem, 'changeType', item.changeType)
        // this.$set(similarItem, 'changeContent', item.changeContent)
        //       // }
        //     })
        //   }
        // })
      }
      if (!mocBeforeItems.length) return
      mocBeforeItems.forEach(item => {
        let _list = this.findSimilarItems(item)
        _list = _list.filter(k => !changeInfos[k.uuid])
        if (_list.length) {
          _list.forEach(cur => {
            if (this.isSameParent(item, cur)) {
              const changeList = this.$store.state.contactList.changeCode[item.uuid]
              changeList.forEach(change => {
                cur[change.Code] = item[change.Code]
              })

              this.$store.dispatch('contactList/addChangeCode', {
                uuid: cur.uuid,
                list: changeList
              })
              console.log('cur', item.isDisabled)
              if (item.changeType === 'isDelete') {
                // this.$set(cur, 'isDisabled', item.isDisabled)

              } else {
                this.updateItemChangeStatus(cur, changeList)
              }
            } else {
              const { SteelAmount, ...others } = item
              const filteredList = (this.$store.state.contactList.changeCode[item.uuid] || []).filter(change => change.Code !== 'SteelAmount')
              filteredList.forEach(change => {
                cur[change.Code] = item[change.Code]
              })
              // cur.CPCode = item.CPCode
              this.$store.dispatch('contactList/addChangeCode', {
                uuid: cur.uuid,
                list: filteredList
              })
              this.updateItemChangeStatus(cur, filteredList)
            }
          })
        }
      })
    },
    editInfo({ row, list }) {
      console.log('editInfo row, list', row, list)
      const _changeMaps = {}
      list.forEach(item => {
        _changeMaps[item.Code] = item.NewValue
      })
      // this.resetDefaultVal()

      const existingChanges = this.$store.state.contactList.changeCode[row.uuid] || []
      const existingChangeCodes = existingChanges.map(change => change.Code)

      const removedChangeCodes = existingChangeCodes.filter(code => !list.some(item => item.Code === code))
      console.log('已移除的字段', removedChangeCodes)

      if (removedChangeCodes.length) {
        const defaultRow = this.defaultTbData.find(item => item.uuid === row.uuid)
        removedChangeCodes.forEach(code => {
          console.log(`重置字段 ${code} 为原始值:`, defaultRow[code])
          _changeMaps[code] = defaultRow[code]
        })
      }
      console.log('_changeMaps', JSON.parse(JSON.stringify(_changeMaps)))

      // 批量更新表格项
      this.batchUpdateTableItem(row.uuid, _changeMaps)
      // this.updateCodesName(row, _changeMaps)
    },
    // updateCodesName(targetItem, _changeMaps) {
    //   if (_changeMaps.SteelName) {
    //     targetItem.SteelName = _changeMaps.SteelName
    //     targetItem.CPCode = _changeMaps.SteelName
    //   } else if (_changeMaps.ComponentName) {
    //     targetItem.ComponentName = _changeMaps.ComponentName
    //     targetItem.CPCode = _changeMaps.ComponentName
    //   } else if (_changeMaps.PartName) {
    //     targetItem.PartName = _changeMaps.PartName
    //     targetItem.CPCode = _changeMaps.PartName
    //   } else {
    //     const defaultRow = this.defaultTbData.find(item => item.uuid === targetItem.uuid)
    //     console.log('defaultRow', JSON.parse(JSON.stringify(defaultRow)))
    //     if (defaultRow) {
    //       targetItem.SteelName = defaultRow.SteelName
    //       targetItem.CPCode = defaultRow.CPCode
    //     }
    //   }
    //   console.log('targetItem', JSON.parse(JSON.stringify(targetItem)))
    //   const _list = this.findSimilarItems(targetItem)
    //   if (_list.length) {
    //     _list.forEach(item => {
    //       item.SteelName = targetItem.SteelName
    //       item.CPCode = targetItem.CPCode
    //     })
    //   }
    // },
    handleCancelChange() {
      const selectedRecords = []
      const getIds = (array) => {
        if (!array || !array.length) return
        array.forEach(item => {
          selectedRecords.push(item.uuid)
          if (item.children && item.children.length) {
            getIds(item.children)
          }
        })
      }
      getIds(this.multipleSelection)
      console.log('selectedRecords', selectedRecords)
      selectedRecords.forEach(item => {
        this.$store.dispatch('contactList/delChangeCode', item)
      })
      this.tbData = this.tbData.filter(item => !selectedRecords.includes(item.uuid))
      this.defaultTbData = this.defaultTbData.filter(item => !selectedRecords.includes(item.uuid))
      this.multipleSelection = []
    },
    multiSelectedChange(array) {
      console.log('array', array)
      console.log('array.records', this.$refs.tableRef.getCheckboxRecords(true))
      const { records } = array
      this.multipleSelection = array.records
    },
    handelFilePath() {
      GetOssUrl({
        url: this.filePath
      }).then(res => {
        window.open(res.Data)
      })
    },
    getChangeStyle(changeName) {
      const arr = changeName.split(',')
      const rusult = ['cs-c-box']
      if (arr.includes(changeType.isAdd)) {
        rusult.push('cs-change-green')
      } else if (arr.includes(changeType.isAdjust)) {
        rusult.push('cs-change-yellow')
      } else if (arr.includes(changeType.isDecrease) || arr.includes(changeType.isIncrease)) {
        rusult.push('cs-change-blue')
      } else if (arr.includes(changeType.isDelete)) {
        rusult.push('cs-change-red')
      } else {
        rusult.push('cs-default')
      }
      return rusult
    },
    getCpCode(row) {
      if (row.Type === 0) {
        return row.SteelName
      } else if (row.Type === 1) {
        return row.ComponentName
      } else {
        return row.PartName
      }
    },
    handleShow(row) {
      const changeList = this.$store.state.contactList.changeCode[row.uuid]
      this.changeRowContentList = changeList || []
    },
    filterNameMethod({ option, values, cellValue, row, column }) {
      const result = this.filterCustom(row)
      return result
    },
    filterCustom(row) {
      const { component_name, component_search_mode, assembly_name,
        assembly_search_mode, part_name, part_search_mode } = this.searchForm

      const _ComponentName = this.nameMapping.ComponentName[row.ComponentName] || row.ComponentName || ''
      const _SteelName = this.nameMapping.SteelName[row.SteelName] || row.SteelName || ''
      const _PartName = this.nameMapping.PartName[row.PartName] || row.PartName || ''

      let partMatch = true

      if (part_name) {
        if (part_search_mode === 1) {
          partMatch = _PartName.includes(part_name)
        } else {
          partMatch = _PartName === part_name
        }
      }
      let assemblyMatch = true
      if (assembly_name) {
        if (assembly_search_mode === 1) {
          assemblyMatch = _ComponentName.includes(assembly_name)
        } else {
          assemblyMatch = _ComponentName === assembly_name
        }
      }
      let componentMatch = true
      if (component_name) {
        if (component_search_mode === 1) {
          componentMatch = _SteelName.includes(component_name)
        } else {
          componentMatch = _SteelName === component_name
        }
      }
      // console.log(componentMatch, assemblyMatch, partMatch)

      const result = componentMatch && assemblyMatch && partMatch
      if (result) {
        return true
      } else {
        return false
      }
    },
    installChange(arr) {
      if (!arr || !arr.length) {
        this.clearTb()
        return
      }
      this.tbData = this.tbData.filter(item => arr.some(id => id === item.InstallUnit_Id))
      this.defaultTbData = this.defaultTbData.filter(item => arr.some(id => id === item.InstallUnit_Id))
    },
    afterApproval() {
      closeTagView(this.$store, this.$route)
    }
  }
}
</script>

<style scoped lang="scss">
.cs-type {
  padding: 2px;
  font-weight: 400;
  font-size: 12px;
  color: #146EB4;
  border-radius: 4px;
  margin-right: 4px;
  background-color: rgba(66, 107, 216, .1);
}

.cs-change {
  padding: 2px 4px;
  font-weight: 400;
  font-size: 12px;
  color: #298DFF;
  border-radius: 4px;
  background-color: rgba(41, 141, 255, .1)
}

.cs-c-box {
  padding: 2px 4px;
  font-weight: 400;
  font-size: 12px;
  border-radius: 4px;
}

.cs-change-green {
  color: #ffffff;
  background-color: #3ECC93
}

.cs-change-green-p {
  color: #3ECC93;
  background-color: rgba(62, 204, 147, .1);
}

.cs-change-blue {
  color: #ffffff;
  background-color: #298DFF
}

.cs-change-blue-p {
  color: #298DFF;
  background-color: rgba(41, 141, 255, .1)
}

.cs-change-red {
  color: #ffffff;
  background-color: #FB6B7F
}

.cs-change-red-p {
  color: #FB6B7F;
  background-color: rgba(251, 107, 127, .1)
}

.cs-default {
  color: #ffffff;
  background-color: #8E95AA
}

.cs-default-p {
  color: #8E95AA;
  background-color: rgba(142, 149, 170, .1)
}

.cs-change-yellow {
  color: #ffffff;
  background-color:  #FB8F00;
}
.cs-green {
  background: rgba(62, 204, 147, .1);
  border-radius: 4px;
  font-size: 14px;
  color: #3ECC93;
  padding: 3px 10px;
}

.cs-yellow {
  background: rgba(241, 180, 48, .1);
  border-radius: 4px;
  font-size: 14px;
  color: #F1B430;
  padding: 3px 10px;
}

.cs-red {
  background: rgba(251, 107, 127, .1);
  border-radius: 4px;
  font-size: 14px;
  color: #FB6B7F;
  padding: 3px 10px;
}

.m-4 {
  margin: 0 4px;
}

.cs-title {
  font-weight: bold;
  font-size: 16px;
  color: rgba(34, 40, 52, 0.85);
}

.page-container {
  margin: 16px;

  .form-x {
    background-color: #FFFFFF;
    padding: 16px;
  }

  .cs-main {
    margin-top: 16px;
    background-color: #FFFFFF;
    padding: 16px;
  }
}

.cs-fee{
  margin-top: 16px;
  background-color: #FFFFFF;
  padding: 16px 16px 0 16px;
  .line-content{
    align-items: center;
    display: flex;
  }
  .cs-title{
    margin-right: 8px;
  }
  .cs-label{
    font-size: 14px;
  }
  .fw{
    font-weight: bold;
  }
  .cs-blue{
    color: #298DFF
  }
  .cs-red{
    color: #FB6B7F
  }
  .fee-name{
    font-weight: bold;
    font-size: 14px;
    color: rgba(34,40,52,0.85);
    margin-right: 32px;
  }
  .fee-sub{
    font-weight: bold;
    font-size: 12px;
    color: rgba(34,40,52,0.65);
    margin-right: 32px;
  }
  .fee-num{
    font-weight: 400;
    font-size: 14px;
    margin-right: 32px;
  }
  .fee-time{
    font-weight: 400;
    margin-right: 32px;
    font-size: 12px;
    color: rgba(34,40,52,0.65);
  }
  .fee-remark{
    font-weight: bold;
    font-size: 12px;
    color: rgba(34,40,52,0.65);
  }
  .circle {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    border: 4px solid #458CF7;
    background-color: white;
  }
  ::v-deep{
    .el-timeline-item__tail{
      height: 37%;
      margin: 18px 0;
      width: 1px;
      left: 5px;
      border: 1px solid rgba(41, 141, 255, 0.3);
    }
  }
}

.el-divider--horizontal {
  margin: 16px 0;
}

footer {
  margin-top: 16px;
  text-align: right;
}

.cs-toolBar {
  ::v-deep {
    .vxe-button--icon.vxe-icon-custom-column{
      display: none;
    }

    .vxe-button.type--button.is--circle {
      width: 97px;
      z-index: 0;
      border-radius: 4px;
    }

    .el-form-item {
      margin-bottom: 0;
    }
  }
}
.el-tree-select{
  ::v-deep{
    .el-select{
      width: 100%;
    }
  }
}

.change-method-form {
  margin-bottom: 0px;
}
.cs-tree-table{
 ::v-deep{
   .vxe-tree-cell{
     text-align: left !important;
   }
   .vxe-checkbox--label{
     color:#333333;
   }
   .col--checkbox{
    .vxe-cell{
      padding-left: 10px !important;
    }
   }
 }
}

.z-upload.hiddenBtn{
  ::v-deep{
    .el-upload{
      display: none;
    }
    .el-upload-list__item:first-child{
      margin-top: 4px;
    }
  }
}
</style>

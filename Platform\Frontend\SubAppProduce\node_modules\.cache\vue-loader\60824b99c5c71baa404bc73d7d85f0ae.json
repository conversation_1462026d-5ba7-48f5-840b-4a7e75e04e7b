{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\change-management\\contact-list\\add.vue?vue&type=style&index=0&id=77f1a670&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\change-management\\contact-list\\add.vue", "mtime": 1757468112511}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmNzLXR5cGUgew0KICBwYWRkaW5nOiAycHg7DQogIGZvbnQtd2VpZ2h0OiA0MDA7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgY29sb3I6ICMxNDZFQjQ7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgbWFyZ2luLXJpZ2h0OiA0cHg7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoNjYsIDEwNywgMjE2LCAuMSk7DQp9DQoNCi5jcy1jaGFuZ2Ugew0KICBwYWRkaW5nOiAycHggNHB4Ow0KICBmb250LXdlaWdodDogNDAwOw0KICBmb250LXNpemU6IDEycHg7DQogIGNvbG9yOiAjMjk4REZGOw0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoNDEsIDE0MSwgMjU1LCAuMSkNCn0NCg0KLmNzLWMtYm94IHsNCiAgcGFkZGluZzogMnB4IDRweDsNCiAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQp9DQoNCi5jcy1jaGFuZ2UtZ3JlZW4gew0KICBjb2xvcjogI2ZmZmZmZjsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzNFQ0M5Mw0KfQ0KDQouY3MtY2hhbmdlLWdyZWVuLXAgew0KICBjb2xvcjogIzNFQ0M5MzsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSg2MiwgMjA0LCAxNDcsIC4xKTsNCn0NCg0KLmNzLWNoYW5nZS1ibHVlIHsNCiAgY29sb3I6ICNmZmZmZmY7DQogIGJhY2tncm91bmQtY29sb3I6ICMyOThERkYNCn0NCg0KLmNzLWNoYW5nZS1ibHVlLXAgew0KICBjb2xvcjogIzI5OERGRjsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSg0MSwgMTQxLCAyNTUsIC4xKQ0KfQ0KDQouY3MtY2hhbmdlLXJlZCB7DQogIGNvbG9yOiAjZmZmZmZmOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjRkI2QjdGDQp9DQoNCi5jcy1jaGFuZ2UtcmVkLXAgew0KICBjb2xvcjogI0ZCNkI3RjsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTEsIDEwNywgMTI3LCAuMSkNCn0NCg0KLmNzLWRlZmF1bHQgew0KICBjb2xvcjogI2ZmZmZmZjsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzhFOTVBQQ0KfQ0KDQouY3MtZGVmYXVsdC1wIHsNCiAgY29sb3I6ICM4RTk1QUE7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMTQyLCAxNDksIDE3MCwgLjEpDQp9DQoNCi5jcy1jaGFuZ2UteWVsbG93IHsNCiAgY29sb3I6ICNmZmZmZmY7DQogIGJhY2tncm91bmQtY29sb3I6ICAjRkI4RjAwOw0KfQ0KLmNzLWdyZWVuIHsNCiAgYmFja2dyb3VuZDogcmdiYSg2MiwgMjA0LCAxNDcsIC4xKTsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBmb250LXNpemU6IDE0cHg7DQogIGNvbG9yOiAjM0VDQzkzOw0KICBwYWRkaW5nOiAzcHggMTBweDsNCn0NCg0KLmNzLXllbGxvdyB7DQogIGJhY2tncm91bmQ6IHJnYmEoMjQxLCAxODAsIDQ4LCAuMSk7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBjb2xvcjogI0YxQjQzMDsNCiAgcGFkZGluZzogM3B4IDEwcHg7DQp9DQoNCi5jcy1yZWQgew0KICBiYWNrZ3JvdW5kOiByZ2JhKDI1MSwgMTA3LCAxMjcsIC4xKTsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBmb250LXNpemU6IDE0cHg7DQogIGNvbG9yOiAjRkI2QjdGOw0KICBwYWRkaW5nOiAzcHggMTBweDsNCn0NCg0KLm0tNCB7DQogIG1hcmdpbjogMCA0cHg7DQp9DQoNCi5jcy10aXRsZSB7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBmb250LXNpemU6IDE2cHg7DQogIGNvbG9yOiByZ2JhKDM0LCA0MCwgNTIsIDAuODUpOw0KfQ0KDQoucGFnZS1jb250YWluZXIgew0KICBtYXJnaW46IDE2cHg7DQoNCiAgLmZvcm0teCB7DQogICAgYmFja2dyb3VuZC1jb2xvcjogI0ZGRkZGRjsNCiAgICBwYWRkaW5nOiAxNnB4Ow0KICB9DQoNCiAgLmNzLW1haW4gew0KICAgIG1hcmdpbi10b3A6IDE2cHg7DQogICAgYmFja2dyb3VuZC1jb2xvcjogI0ZGRkZGRjsNCiAgICBwYWRkaW5nOiAxNnB4Ow0KICB9DQp9DQoNCi5jcy1mZWV7DQogIG1hcmdpbi10b3A6IDE2cHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNGRkZGRkY7DQogIHBhZGRpbmc6IDE2cHggMTZweCAwIDE2cHg7DQogIC5saW5lLWNvbnRlbnR7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICB9DQogIC5jcy10aXRsZXsNCiAgICBtYXJnaW4tcmlnaHQ6IDhweDsNCiAgfQ0KICAuY3MtbGFiZWx7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KICB9DQogIC5md3sNCiAgICBmb250LXdlaWdodDogYm9sZDsNCiAgfQ0KICAuY3MtYmx1ZXsNCiAgICBjb2xvcjogIzI5OERGRg0KICB9DQogIC5jcy1yZWR7DQogICAgY29sb3I6ICNGQjZCN0YNCiAgfQ0KICAuZmVlLW5hbWV7DQogICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KICAgIGNvbG9yOiByZ2JhKDM0LDQwLDUyLDAuODUpOw0KICAgIG1hcmdpbi1yaWdodDogMzJweDsNCiAgfQ0KICAuZmVlLXN1YnsNCiAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICBmb250LXNpemU6IDEycHg7DQogICAgY29sb3I6IHJnYmEoMzQsNDAsNTIsMC42NSk7DQogICAgbWFyZ2luLXJpZ2h0OiAzMnB4Ow0KICB9DQogIC5mZWUtbnVtew0KICAgIGZvbnQtd2VpZ2h0OiA0MDA7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KICAgIG1hcmdpbi1yaWdodDogMzJweDsNCiAgfQ0KICAuZmVlLXRpbWV7DQogICAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgICBtYXJnaW4tcmlnaHQ6IDMycHg7DQogICAgZm9udC1zaXplOiAxMnB4Ow0KICAgIGNvbG9yOiByZ2JhKDM0LDQwLDUyLDAuNjUpOw0KICB9DQogIC5mZWUtcmVtYXJrew0KICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICBjb2xvcjogcmdiYSgzNCw0MCw1MiwwLjY1KTsNCiAgfQ0KICAuY2lyY2xlIHsNCiAgICB3aWR0aDogMTRweDsNCiAgICBoZWlnaHQ6IDE0cHg7DQogICAgYm9yZGVyLXJhZGl1czogNTAlOw0KICAgIGJvcmRlcjogNHB4IHNvbGlkICM0NThDRjc7DQogICAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7DQogIH0NCiAgOjp2LWRlZXB7DQogICAgLmVsLXRpbWVsaW5lLWl0ZW1fX3RhaWx7DQogICAgICBoZWlnaHQ6IDM3JTsNCiAgICAgIG1hcmdpbjogMThweCAwOw0KICAgICAgd2lkdGg6IDFweDsNCiAgICAgIGxlZnQ6IDVweDsNCiAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoNDEsIDE0MSwgMjU1LCAwLjMpOw0KICAgIH0NCiAgfQ0KfQ0KDQouZWwtZGl2aWRlci0taG9yaXpvbnRhbCB7DQogIG1hcmdpbjogMTZweCAwOw0KfQ0KDQpmb290ZXIgew0KICBtYXJnaW4tdG9wOiAxNnB4Ow0KICB0ZXh0LWFsaWduOiByaWdodDsNCn0NCg0KLmNzLXRvb2xCYXIgew0KICA6OnYtZGVlcCB7DQogICAgLnZ4ZS1idXR0b24tLWljb24udnhlLWljb24tY3VzdG9tLWNvbHVtbnsNCiAgICAgIGRpc3BsYXk6IG5vbmU7DQogICAgfQ0KDQogICAgLnZ4ZS1idXR0b24udHlwZS0tYnV0dG9uLmlzLS1jaXJjbGUgew0KICAgICAgd2lkdGg6IDk3cHg7DQogICAgICB6LWluZGV4OiAwOw0KICAgICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgIH0NCg0KICAgIC5lbC1mb3JtLWl0ZW0gew0KICAgICAgbWFyZ2luLWJvdHRvbTogMDsNCiAgICB9DQogIH0NCn0NCi5lbC10cmVlLXNlbGVjdHsNCiAgOjp2LWRlZXB7DQogICAgLmVsLXNlbGVjdHsNCiAgICAgIHdpZHRoOiAxMDAlOw0KICAgIH0NCiAgfQ0KfQ0KDQouY2hhbmdlLW1ldGhvZC1mb3JtIHsNCiAgbWFyZ2luLWJvdHRvbTogMHB4Ow0KfQ0KLmNzLXRyZWUtdGFibGV7DQogOjp2LWRlZXB7DQogICAudnhlLXRyZWUtY2VsbHsNCiAgICAgdGV4dC1hbGlnbjogbGVmdCAhaW1wb3J0YW50Ow0KICAgfQ0KICAgLnZ4ZS1jaGVja2JveC0tbGFiZWx7DQogICAgIGNvbG9yOiMzMzMzMzM7DQogICB9DQogICAuY29sLS1jaGVja2JveHsNCiAgICAudnhlLWNlbGx7DQogICAgICBwYWRkaW5nLWxlZnQ6IDEwcHggIWltcG9ydGFudDsNCiAgICB9DQogICB9DQogfQ0KfQ0KDQouei11cGxvYWQuaGlkZGVuQnRuew0KICA6OnYtZGVlcHsNCiAgICAuZWwtdXBsb2Fkew0KICAgICAgZGlzcGxheTogbm9uZTsNCiAgICB9DQogICAgLmVsLXVwbG9hZC1saXN0X19pdGVtOmZpcnN0LWNoaWxkew0KICAgICAgbWFyZ2luLXRvcDogNHB4Ow0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["add.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyrDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "add.vue", "sourceRoot": "src/views/PRO/change-management/contact-list", "sourcesContent": ["<template>\r\n  <div class=\"page-container\">\r\n    <div class=\"form-x\">\r\n      <div>\r\n        <span class=\"cs-title\">工程联系单</span>\r\n      </div>\r\n      <el-divider />\r\n      <el-row v-loading=\"pageLoading\" element-loading-text=\"加载中\">\r\n        <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"120px\">\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"项目名称\" prop=\"Sys_Project_Id\">\r\n              <el-select\r\n                v-model=\"form.Sys_Project_Id\"\r\n                :disabled=\"isView||tbLoading\"\r\n                clearable\r\n                class=\"w100\"\r\n                placeholder=\"请选择\"\r\n                filterable\r\n                @change=\"projectChange()\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in projectList\"\r\n                  :key=\"item.Sys_Project_Id\"\r\n                  :label=\"item.Short_Name\"\r\n                  :value=\"item.Sys_Project_Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"区域名称\" prop=\"Area_Id\">\r\n              <el-tree-select\r\n                ref=\"treeSelectArea\"\r\n                v-model=\"form.Area_Id\"\r\n                :disabled=\"!form.Sys_Project_Id||isView||tbLoading\"\r\n                :select-params=\"{\r\n                  clearable: true,\r\n\r\n                }\"\r\n                :tree-params=\"treeParamsArea\"\r\n                @select-clear=\"areaClear\"\r\n                @node-click=\"areaChange\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item ref=\"installUnitRef\" label=\"批次\" prop=\"InstallUnit_Ids\">\r\n              <el-select\r\n                v-model=\"form.InstallUnit_Ids\"\r\n                filterable\r\n                multiple\r\n                class=\"w100\"\r\n                :disabled=\"isView||!form.Area_Id||tbLoading\"\r\n                placeholder=\"请选择\"\r\n                @change=\"installChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in installUnitList\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"变更人\" prop=\"Handle_UserId\">\r\n              <el-select\r\n                v-model=\"form.Handle_UserId\"\r\n                :disabled=\"isView\"\r\n                filterable\r\n                class=\"w100\"\r\n                clearable\r\n                placeholder=\"请选择\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in peopleList\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"变更类型\" prop=\"Moc_Type_Id\">\r\n              <el-select\r\n                v-model=\"form.Moc_Type_Id\"\r\n                class=\"w100\"\r\n                :disabled=\"isView\"\r\n                filterable\r\n                placeholder=\"请选择\"\r\n                @change=\"mocTypeChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in changeTypeList\"\r\n                  :key=\"item.Id\"\r\n                  :label=\"item.Display_Name\"\r\n                  :value=\"item.Id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"变更费用\" prop=\"Fee\">\r\n              <el-input-number v-model=\"form.Fee\" placeholder=\"请输入\" style=\"width: 100%\" clearable :disabled=\"isView\" class=\"cs-number-btn-hidden\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"变更工时\" prop=\"Hours\">\r\n              <el-input-number v-model=\"form.Hours\" placeholder=\"请输入\" style=\"width: 100%\" clearable :disabled=\"isView\" class=\"cs-number-btn-hidden\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"紧急程度\" prop=\"Urgency\">\r\n              <el-select v-model=\"form.Urgency\" placeholder=\"请选择\" style=\"width: 100%\" :disabled=\"isView\">\r\n                <el-option label=\"普通\" :value=\"1\" />\r\n                <el-option label=\"紧急\" :value=\"2\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"要求完成时间\" prop=\"Demand_Date\">\r\n              <el-date-picker\r\n                v-model=\"form.Demand_Date\"\r\n                :disabled=\"isView\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                type=\"date\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"费用部门\" prop=\"Fee_DepartId\">\r\n              <el-tree-select\r\n                ref=\"treeSelect\"\r\n                v-model=\"form.Fee_DepartId\"\r\n                :disabled=\"isView\"\r\n                :select-params=\"{\r\n                  clearable: true,\r\n                }\"\r\n                :tree-params=\"treeParams\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"变更说明\" prop=\"Remark\">\r\n              <el-input\r\n                v-model=\"form.Remark\"\r\n                type=\"textarea\"\r\n                :disabled=\"isView\"\r\n                :autosize=\"{ minRows: 3, maxRows: 3}\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"附件\" prop=\"region\">\r\n              <OSSUpload\r\n                ref=\"upload\"\r\n                :disabled=\"isView\"\r\n                action=\"\"\r\n                :before-upload=\"beforeUpload\"\r\n                :limit=\"5\"\r\n                multiple\r\n                :file-list=\"fileList\"\r\n                :on-progress=\"handleProgress\"\r\n                :on-exceed=\"handleExceed\"\r\n                :on-error=\"handleError\"\r\n                :on-success=\"uploadSuccess\"\r\n                :on-change=\"handleChange\"\r\n                :on-remove=\"handleRemove\"\r\n                :on-preview=\"handlePreview\"\r\n                show-file-list\r\n                accept=\".xls, .xlsx,.pdf,.jpg,.png,.dwg,.doc,.docx\"\r\n                btn-icon=\"el-icon-upload\"\r\n                :class=\"isView ? 'z-upload hiddenBtn' : 'z-upload'\"\r\n              >\r\n                <el-button v-if=\"!isView\" :loading=\"uploadLoading\" type=\"primary\">上传文件</el-button>\r\n              </OSSUpload>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-form>\r\n\r\n      </el-row>\r\n    </div>\r\n    <div v-if=\"isView\" class=\"cs-fee\">\r\n      <span class=\"cs-title\">费用变动</span>\r\n      <span class=\"cs-label\">当前金额：<span class=\"fw cs-blue\">{{ finishFee }}元</span></span>\r\n      <el-divider />\r\n      <el-timeline>\r\n        <el-timeline-item\r\n          v-for=\"(activity, index) in activities\"\r\n          :key=\"index\"\r\n          hide-timestamp\r\n        >\r\n          <div class=\"line-content\">\r\n            <span class=\"fee-name\">{{ activity.Create_UserName }}</span>\r\n            <span :class=\"['fee-num',{'txt-red':activity.isRed,'txt-blue':activity.isBlue}]\">{{ getFeeGap(activity,index) }}</span>\r\n            <span class=\"fee-time\">{{ activity.Create_Date }}</span>\r\n            <span class=\"fee-remark\">备注：{{ activity.Remark || '-' }}</span>\r\n          </div>\r\n          <template #dot>\r\n            <span class=\"circle\" />\r\n          </template>\r\n        </el-timeline-item>\r\n      </el-timeline>\r\n    </div>\r\n    <div class=\"cs-main\">\r\n      <template v-if=\"showDetail\">\r\n        <span class=\"cs-title\">变更明细</span>\r\n        <el-divider />\r\n\r\n        <el-form inline class=\"change-method-form\">\r\n          <el-form-item label=\"变更方式：\">\r\n            <template v-if=\"!isView\">\r\n              <el-radio :value=\"changeMethod\" :label=\"1\" @input=\"changeMethodFun(1)\">完整清单导入</el-radio>\r\n              <el-radio :value=\"changeMethod\" :label=\"2\" @input=\"changeMethodFun(2)\">部分清单导入</el-radio>\r\n              <el-radio :value=\"changeMethod\" :label=\"3\" @input=\"changeMethodFun(3)\">手动修改</el-radio>\r\n            </template>\r\n            <template v-else>\r\n              {{ changeMethod === 1 ? '完整清单导入' : changeMethod === 2 ? '部分清单导入' : '手动修改' }}\r\n            </template>\r\n          </el-form-item>\r\n        </el-form>\r\n        <div v-if=\"filePath\">\r\n          <i style=\"color:#409EFF\" class=\"el-icon-download\" />\r\n          <el-link type=\"primary\" :underline=\"false\" @click=\"handelFilePath\">{{ getFileName }}</el-link>\r\n        </div>\r\n        <vxe-toolbar ref=\"xToolbar1\" class=\"cs-toolBar\">\r\n          <template #buttons>\r\n            <el-button v-if=\"!isView && changeMethod !== 3\" type=\"primary\" @click=\"handleImport\">导入变更清单</el-button>\r\n            <el-button v-if=\"!isView && changeMethod === 3\" type=\"primary\" @click=\"handleAdd\">添加变更内容</el-button>\r\n            <el-button v-if=\"!isView && changeMethod === 3\" type=\"danger\" :disabled=\"!multipleSelection.length\" plain @click=\"handleCancelChange\">取消变更</el-button>\r\n          </template>\r\n          <template #tools>\r\n            <el-form ref=\"form2\" inline :model=\"searchForm\" label-width=\"70px\">\r\n              <el-form-item label=\"构件名称\">\r\n                <el-input\r\n                  v-model=\"searchForm.component_name\"\r\n                  clearable\r\n                  style=\"width: 260px;\"\r\n                  placeholder=\"请输入\"\r\n                  class=\"input-with-select\"\r\n                >\r\n                  <el-select\r\n                    slot=\"prepend\"\r\n                    v-model=\"searchForm.component_search_mode\"\r\n                    placeholder=\"请选择\"\r\n                    style=\"width: 100px\"\r\n                  >\r\n                    <el-option\r\n                      label=\"模糊搜索\"\r\n                      :value=\"1\"\r\n                    />\r\n                    <el-option\r\n                      label=\"精确搜索\"\r\n                      :value=\"2\"\r\n                    />\r\n                  </el-select>\r\n                </el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"部件名称\">\r\n                <el-input\r\n                  v-model=\"searchForm.assembly_name\"\r\n                  style=\"width: 260px;\"\r\n                  clearable\r\n                  placeholder=\"请输入\"\r\n                  class=\"input-with-select\"\r\n                >\r\n                  <el-select\r\n                    slot=\"prepend\"\r\n                    v-model=\"searchForm.assembly_search_mode\"\r\n                    placeholder=\"请选择\"\r\n                    style=\"width: 100px\"\r\n                  >\r\n                    <el-option\r\n                      label=\"模糊搜索\"\r\n                      :value=\"1\"\r\n                    />\r\n                    <el-option\r\n                      label=\"精确搜索\"\r\n                      :value=\"2\"\r\n                    />\r\n                  </el-select>\r\n                </el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"零件名称\">\r\n                <el-input\r\n                  v-model=\"searchForm.part_name\"\r\n                  clearable\r\n                  style=\"width: 260px;\"\r\n                  placeholder=\"请输入\"\r\n                  class=\"input-with-select\"\r\n                >\r\n                  <el-select\r\n                    slot=\"prepend\"\r\n                    v-model=\"searchForm.part_search_mode\"\r\n                    placeholder=\"请选择\"\r\n                    style=\"width: 100px\"\r\n                  >\r\n                    <el-option\r\n                      label=\"模糊搜索\"\r\n                      :value=\"1\"\r\n                    />\r\n                    <el-option\r\n                      label=\"精确搜索\"\r\n                      :value=\"2\"\r\n                    />\r\n                  </el-select>\r\n                </el-input>\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button @click=\"handleReset\">重置</el-button>\r\n                <el-button type=\"primary\" @click=\"handleFilter\">搜索</el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n          </template>\r\n        </vxe-toolbar>\r\n        <div class=\"fff tb-x\">\r\n          <vxe-table\r\n            id=\"uuid\"\r\n            ref=\"tableRef\"\r\n            :empty-render=\"{name: 'NotData'}\"\r\n            show-header-overflow\r\n            :loading=\"tbLoading\"\r\n            element-loading-spinner=\"el-icon-loading\"\r\n            element-loading-text=\"拼命加载中\"\r\n            empty-text=\"暂无数据\"\r\n            class=\"cs-vxe-table cs-tree-table\"\r\n            height=\"500\"\r\n            stripe\r\n            :filter-config=\"{showIcon: false}\"\r\n            :row-config=\"{keyField:'uuid', 'isHover': true, 'isCurrent': true}\"\r\n            :data=\"tbData\"\r\n            resizable\r\n            :checkbox-config=\"{checkField: 'checked',labelField: 'CPCode', highlight: true}\"\r\n            :tree-config=\"{transform: true, showIcon: true, rowField: 'parentChildrenId', parentField: 'ParentId'}\"\r\n            :tooltip-config=\"{ enterable: true}\"\r\n            @checkbox-all=\"multiSelectedChange\"\r\n            @checkbox-change=\"multiSelectedChange\"\r\n          >\r\n            <vxe-column v-if=\"changeMethod === 3 && !isView\" fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n            <!-- :type=\"index===0 && changeMethod === 3 && !isView? 'checkbox':''\" -->\r\n\r\n            <template v-for=\"(item,index) in columns\">\r\n              <vxe-column\r\n                :key=\"item.Code+changeMethod\"\r\n                :tree-node=\"index===0\"\r\n                :fixed=\"item.Is_Frozen ? (item.Frozen_Dirction || 'left') : ''\"\r\n                :min-width=\"item.Width\"\r\n                show-overflow=\"tooltip\"\r\n                sortable\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :filters=\"item.Code==='CPCode' ? filterCodeOptions : null\"\r\n                :filter-method=\"item.Code==='CPCode' ? filterNameMethod : null\"\r\n                :title=\"item.Display_Name\"\r\n              >\r\n                <template v-if=\"item.Code==='CPCode'\" #filter=\"{ $panel, column }\">\r\n                  <input v-for=\"(option, index1) in column.filters\" :key=\"index1\" v-model=\"option.data\" type=\"type\" @input=\"$panel.changeOption($event, !!option.data, option)\">\r\n                </template>\r\n                <template v-if=\"item.Code==='CPCode'\" #default=\"{ row }\">\r\n                  {{ getCpCode(row) }}\r\n                  <el-tag v-if=\"row.Type===0\" size=\"mini\">构</el-tag>\r\n                  <el-tag v-else-if=\"row.Type===1\" type=\"warning\" size=\"mini\">部</el-tag>\r\n                  <el-tag v-else type=\"success\" size=\"mini\">零</el-tag>\r\n                </template>\r\n                <template v-else-if=\"item.Code === 'changeContent'\" #default=\"{ row }\">\r\n\r\n                  <el-popover\r\n                    placement=\"left-start\"\r\n                    width=\"400\"\r\n                    trigger=\"click\"\r\n                    @show=\"handleShow(row)\"\r\n                  >\r\n                    <el-table max-height=\"300\" stripe resizable class=\"cs-custom-table\" :data=\"changeRowContentList\">\r\n                      <el-table-column align=\"center\" property=\"Name\" width=\"100\" label=\"变更字段\" />\r\n                      <el-table-column align=\"center\" property=\"Value\" label=\"变更前\" />\r\n                      <el-table-column align=\"center\" property=\"NewValue\" label=\"变更后\" />\r\n                    </el-table>\r\n                    <span slot=\"reference\" style=\"cursor: pointer;\" :class=\"getChangeStyle(row.changeContent)\">\r\n                      {{ row.changeContent }}\r\n                    </span>\r\n                  </el-popover>\r\n\r\n                </template>\r\n                <template v-else-if=\"item.Code === 'Production_Status'\" #default=\"{ row }\">\r\n                  <el-link v-if=\"row.MocIdBefore&& row[item.Code]\" :type=\"row.Production_Status === '未生产' ? 'info' : 'primary'\" @click=\"handleOpen(row)\"> {{ row[item.Code] | displayValue }}</el-link>\r\n                  <span v-else>-</span>\r\n                </template>\r\n                <template v-else-if=\"item.Code === 'Is_Component_Mark'\" #default=\"{ row }\">\r\n                  <!--             是否直发件 是：是直发件；否：非直发件；-->\r\n                  <template v-if=\"row.Type===0\">\r\n                    <el-tag v-if=\"row[item.Code]==='是'\" type=\"primary\">是</el-tag>\r\n                    <el-tag v-else type=\"danger\" >否</el-tag>\r\n                  </template>\r\n                  <template v-else>\r\n                    <el-tag v-if=\"row.PartType==='直发件'\" type=\"primary\">是</el-tag>\r\n                    <el-tag v-else type=\"danger\">否</el-tag>\r\n                  </template>\r\n\r\n                </template>\r\n                <template v-else #default=\"{ row }\">\r\n                  <span> {{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n            </template>\r\n\r\n            <!-- Operations column for manual edit mode -->\r\n            <vxe-column v-if=\"changeMethod === 3 && !isView\" fixed=\"right\" title=\"操作\" width=\"160\">\r\n              <template #default=\"{ row }\">\r\n                <el-button\r\n                  v-if=\"row.changeType !== 'isDelete'\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  @click=\"handleEdit(row)\"\r\n                >\r\n                  编辑\r\n                </el-button>\r\n                <el-button\r\n                  v-if=\"row.changeType !== 'isDelete'\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  style=\"color: #FB6B7F;\"\r\n                  @click=\"handleDelete(row)\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n                <el-button\r\n                  v-if=\"row.changeType === 'isDelete' && !row.isDisabled\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  style=\"color: #298DFF;\"\r\n                  @click=\"handleRestore(row)\"\r\n                >\r\n                  撤销删除\r\n                </el-button>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-table>\r\n        </div>\r\n      </template>\r\n      <footer v-if=\"!isView\">\r\n        <el-button :disabled=\"disableSave\" :loading=\"saveLoading\" @click=\"handleSave\">保存草稿</el-button>\r\n        <el-button :disabled=\"disableSave || uploadLoading\" :loading=\"submitLoading\" type=\"primary\" @click=\"handleSubmit\">提交审核</el-button>\r\n      </footer>\r\n    </div>\r\n    <ImportFile ref=\"dialog\" @refresh=\"getTableInfo\" />\r\n    <StatusDialog ref=\"statusDialog\" />\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      v-dialogDrag\r\n      top=\"6vh\"\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"width\"\r\n      class=\"plm-custom-dialog\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        @getMocModelList=\"getMocModelList\"\r\n        @editInfo=\"editInfo\"\r\n        @close=\"handleClose\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GeAreaTrees, GetInstallUnitIdNameList, GetProjectPageList } from '@/api/PRO/project'\r\nimport { GetFactoryPeoplelist } from '@/api/PRO/basic-information/workshop'\r\nimport {\r\n  GetMocOrderInfo,\r\n  GetMocOrderTypeList,\r\n  ImportChangFile,\r\n  SaveMocOrder,\r\n  SubmitMocOrder\r\n} from '@/api/PRO/changeManagement'\r\nimport { GetCompanyDepartTree, GetOssUrl } from '@/api/sys'\r\nimport ImportFile from './components/importFile.vue'\r\nimport OSSUpload from '@/views/plm/components/ossupload.vue'\r\nimport { closeTagView, combineURL, debounce, deepClone } from '@/utils'\r\nimport StatusDialog from './components/dialog.vue'\r\nimport numeral from 'numeral'\r\nimport { GetTableSettingList } from '@/api/PRO/component-type'\r\nimport HandleEdit from './components/HandleEdit.vue'\r\nimport AddHandle from './components/addHandle.vue'\r\nimport { changeType, changeTypeReverse, getAllCodesByType } from './utils'\r\nimport { getFileNameFromUrl } from '@/utils/file'\r\nimport { isArray } from 'ali-oss/lib/common/utils/isArray'\r\nimport SteelComponentManager from '@/views/PRO/change-management/contact-list/info'\r\nimport { GetCurFactory } from '@/api/PRO/factory'\r\n\r\nexport default {\r\n  name: 'PROEngineeringChangeOrderAdd',\r\n  components: {\r\n    OSSUpload,\r\n    StatusDialog,\r\n    ImportFile,\r\n    HandleEdit,\r\n    AddHandle\r\n  },\r\n  mixins: [SteelComponentManager],\r\n  data() {\r\n    return {\r\n      curStatus: {\r\n        del: '已删',\r\n        change: '变更',\r\n        add: '新增',\r\n        increase: '数量增加',\r\n        decrease: '数量减少',\r\n        unChange: '无变更'\r\n      },\r\n\r\n      dialogVisible: false,\r\n      title: '',\r\n      width: '50%',\r\n      currentComponent: '',\r\n      filePath: '',\r\n      finishFee: 0,\r\n      pageLoading: false,\r\n      saveLoading: false,\r\n      submitLoading: false,\r\n      uploadLoading: false,\r\n      tbLoading: false,\r\n      tbData: [],\r\n      activities: [],\r\n      fileList: [],\r\n      multipleSelection: [],\r\n      changeRowContentList: [],\r\n      filterCodeOptions: [{ data: '' }],\r\n      columns: [],\r\n      changeMethod: 1,\r\n      searchForm: {\r\n        component_name: '',\r\n        part_name: '',\r\n        assembly_name: '',\r\n        component_search_mode: 1,\r\n        part_search_mode: 1,\r\n        assembly_search_mode: 1,\r\n        content: ''\r\n      },\r\n      form: {\r\n        Sys_Project_Id: '',\r\n        Area_Id: '',\r\n        InstallUnit_Ids: [],\r\n        Handle_UserId: '',\r\n        Moc_Type_Id: '',\r\n        Fee: undefined,\r\n        Hours: undefined,\r\n        Urgency: 1,\r\n        Demand_Date: '',\r\n        Fee_DepartId: '',\r\n        Remark: '',\r\n        AttachmentList: []\r\n      },\r\n      showImport: false,\r\n      treeParams: {\r\n        data: [],\r\n        filterable: false,\r\n        clickParent: true,\r\n        props: {\r\n          disabled: 'disabled',\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      peopleList: [],\r\n      projectList: [],\r\n      changeTypeList: [],\r\n      installUnitList: [],\r\n      treeParamsArea: {\r\n        'default-expand-all': true,\r\n        filterable: false,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          disabled: 'disabled',\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Id'\r\n        }\r\n      },\r\n      rules: {\r\n        Sys_Project_Id: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        Area_Id: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        Handle_UserId: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        Moc_Type_Id: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ],\r\n        InstallUnit_Ids: [\r\n          { required: true, message: '请选择', trigger: 'change' }\r\n        ]\r\n      },\r\n      toolbarButtons: [\r\n        { code: 'myToolbarExport', name: '点击导出' },\r\n        { code: 'myToolbarLink', name: '点击跳转' },\r\n        { code: 'myToolbarCustom', name: '打开自定义列' }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    disableSave() {\r\n      return this.submitLoading || this.uploadLoading\r\n    },\r\n    isView() {\r\n      return this.$route.query.type == '2'\r\n    },\r\n    isEdit() {\r\n      return this.$route.query.type == '1'\r\n    },\r\n    showDetail() {\r\n      const zbtk = this.changeTypeList.find(item => {\r\n        return item.Id === this.form.Moc_Type_Id\r\n      })\r\n      return zbtk?.Is_Deepen_Change || false\r\n    },\r\n    getFileName() {\r\n      return getFileNameFromUrl(this.filePath)\r\n    }\r\n  },\r\n  watch: {\r\n    'form.Area_Id': {\r\n      handler(newVal) {\r\n        if (!newVal) {\r\n          this.rules.InstallUnit_Ids[0].required = false\r\n          this.$refs?.installUnitRef?.clearValidate()\r\n        }\r\n      },\r\n      immediate: true\r\n    },\r\n    'form.InstallUnit_Ids': {\r\n      handler(newVal) {\r\n        if (!this.Area_Id) return\r\n        if (this.installUnitList.length) {\r\n          this.rules.InstallUnit_Ids[0].required = true\r\n          this.$refs.installUnitRef.clearValidate()\r\n        } else {\r\n          this.rules.InstallUnit_Ids[0].required = false\r\n          this.$refs.installUnitRef.clearValidate()\r\n        }\r\n      }\r\n    }\r\n  },\r\n  async created() {\r\n    // await this.getTableConfig('PROEngChangeOrderAdd') ProfessionalCode\r\n    this.$store.dispatch('contactList/resetChangeCode')\r\n    this.getProjectData()\r\n    this.getFactoryPeople()\r\n    await this.getDepTree()\r\n    this.getFactoryChangeTypeList()\r\n\r\n    const id = this.$route.query.id\r\n    id && await this.getInfo(id)\r\n    this.getTableConfig()\r\n    // this.getTableInfo()\r\n  },\r\n  methods: {\r\n    async getTableConfig() {\r\n      const res = await GetTableSettingList({ ProfessionalCode: 'Steel' })\r\n      if (res.IsSucceed) {\r\n        this.allCodes = JSON.parse(JSON.stringify(res.Data))\r\n        // Filter out the three name columns\r\n        const filteredColumns = res.Data.filter(item =>\r\n          !['SteelName', 'ComponentName', 'PartName'].includes(item.Code)\r\n        )\r\n\r\n        // Add the new CPCode column\r\n        const codeColumn = {\r\n          Code: 'CPCode',\r\n          Display_Name: '构件/部件/零件名称',\r\n          Is_Frozen: true,\r\n          Frozen_Dirction: 'left',\r\n          Width: 180,\r\n          Align: 'left'\r\n        }\r\n\r\n        // Insert the CPCode column at the beginning\r\n        filteredColumns.unshift(codeColumn)\r\n        const _customColumns = [{\r\n          Code: 'Production_Status',\r\n          Display_Name: '生产情况',\r\n          Align: 'center',\r\n          Is_Frozen: true,\r\n          Frozen_Dirction: 'right',\r\n          Width: 120\r\n        }, {\r\n          Code: 'changeContent',\r\n          Display_Name: '变更内容',\r\n          Align: 'center',\r\n          Is_Frozen: true,\r\n          Frozen_Dirction: 'right',\r\n          Width: 120\r\n        }]\r\n\r\n        filteredColumns.push(..._customColumns)\r\n        let _columns = []\r\n\r\n        this.rootColumns = deepClone(filteredColumns.map(item => {\r\n          const displayNameLength = item.Display_Name?.length || 0\r\n          const width = Math.max(120, 120 + Math.max(0, displayNameLength - 4) * 10)\r\n          return {\r\n            ...item,\r\n            Width: width,\r\n            Align: 'center'\r\n          }\r\n        }))\r\n\r\n        if (this.changeMethod === 3) {\r\n          const columnCode = ['CPCode', 'SetupPosition', 'Production_Status', 'changeContent']\r\n          _columns = this.rootColumns.filter(item => columnCode.includes(item.Code))\r\n        } else {\r\n          _columns = this.rootColumns\r\n        }\r\n        this.columns = _columns\r\n      }\r\n    },\r\n    async getInfo(id) {\r\n      this.pageLoading = true\r\n      await GetMocOrderInfo({\r\n        Id: id\r\n      }).then(async res => {\r\n        if (res.IsSucceed) {\r\n          const {\r\n            Deepen_File_Url,\r\n            Sys_Project_Id,\r\n            Area_Id,\r\n            InstallUnit_Ids,\r\n            Handle_UserId,\r\n            Moc_Type_Id,\r\n            Change_Type,\r\n            Fee,\r\n            Hours,\r\n            Urgency,\r\n            Demand_Date,\r\n            Fee_DepartId,\r\n            Remark,\r\n            FeeHistory,\r\n            Id,\r\n            Status,\r\n            AttachmentList,\r\n            OrderDetail\r\n          } = res.Data\r\n          this.activities = FeeHistory\r\n          if (FeeHistory.length) {\r\n            const [last] = FeeHistory.slice(-1)\r\n            this.finishFee = numeral(last?.Fee || 0).format('0.[00]')\r\n          }\r\n          if (Status === 3) {\r\n            const idx = this.columns.findIndex(item => item.Code === 'Production_Status')\r\n            if (idx !== -1) {\r\n              this.columns.splice(idx, 1)\r\n            }\r\n          }\r\n\r\n          if (AttachmentList?.length) {\r\n            AttachmentList.forEach((element, idx) => {\r\n              const obj = {\r\n                name: element.File_Name,\r\n                url: element.File_Url\r\n              }\r\n              this.fileList.push(obj)\r\n              this.form.AttachmentList.push({\r\n                File_Url: element.File_Url,\r\n                File_Name: element.File_Name\r\n              })\r\n            })\r\n          }\r\n          await this.getAreaList(Sys_Project_Id)\r\n          await this.getInstallUnitPageList(Area_Id)\r\n\r\n          Object.assign(this.form, {\r\n            ...res.Data,\r\n            Sys_Project_Id,\r\n            Area_Id,\r\n            Deepen_File_Url,\r\n            Id,\r\n            InstallUnit_Ids: InstallUnit_Ids ? (typeof InstallUnit_Ids === 'string' ? InstallUnit_Ids.split(',') : InstallUnit_Ids) : [],\r\n            Handle_UserId,\r\n            Moc_Type_Id,\r\n            Fee: Fee || undefined,\r\n            Hour: Hours || undefined,\r\n            Urgency: Number(Urgency),\r\n            Demand_Date,\r\n            Fee_DepartId,\r\n            Remark\r\n          })\r\n\r\n          this.setTbData(OrderDetail)\r\n          this.filePath = Deepen_File_Url\r\n          this.changeMethod = Change_Type === 0 ? 1 : Change_Type === 1 ? 2 : 3\r\n          // Deepen_File_Url\r\n          // setTimeout(() => {\r\n          // Deepen_File_Url && this.getTableInfo({ File_Url: Deepen_File_Url })\r\n          // }, 0)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.pageLoading = false\r\n      })\r\n    },\r\n    mocTypeChange() {\r\n      this.tbData = []\r\n      this.handleReset()\r\n    },\r\n\r\n    handleExceed(files, fileList) {\r\n      this.$message.warning(`当前限制选择 5 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)\r\n    },\r\n    handleProgress(event, files, fileList) {\r\n    },\r\n    handleError(err, files, fileList) {\r\n      console.log('err3', err, files, fileList)\r\n      this.checkUploading(fileList)\r\n    },\r\n    checkUploading(fileList) {\r\n      const flag = fileList.every(v => v.status === 'success')\r\n      flag && (this.uploadLoading = false)\r\n    },\r\n    handleImport() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (valid) {\r\n          console.log('valid', valid)\r\n          this.$refs['dialog'].handleOpen()\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    handleAdd() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (valid) {\r\n          this.title = '添加变更内容'\r\n          this.width = '70%'\r\n          this.currentComponent = 'addHandle'\r\n          this.dialogVisible = true\r\n          this.$nextTick(_ => {\r\n            this.$refs['content'].handleOpen(this.form, this.tbData)\r\n          })\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    handleOpen(row) {\r\n      this.$refs['statusDialog'].handleOpen(row)\r\n    },\r\n    getProjectData() {\r\n      GetProjectPageList({ PageSize: -1 }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.projectList = res.Data.Data\r\n        }\r\n      })\r\n    },\r\n    setDisabledTree(root) {\r\n      if (!root) return\r\n      root.forEach((element) => {\r\n        const { Children } = element\r\n        if (Children && Children.length) {\r\n          element.disabled = true\r\n        } else {\r\n          element.disabled = false\r\n          this.setDisabledTree(Children)\r\n        }\r\n      })\r\n    },\r\n    async getAreaList(Pid) {\r\n      await GeAreaTrees({\r\n        sysProjectId: Pid\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          const tree = res.Data\r\n          this.setDisabledTree(tree)\r\n          this.treeParamsArea.data = res.Data\r\n          this.$nextTick(_ => {\r\n            this.$refs.treeSelectArea.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async getInstallUnitPageList(areaId) {\r\n      await GetInstallUnitIdNameList({\r\n        Area_Id: areaId,\r\n        Page: 1,\r\n        PageSize: -1\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.installUnitList = res.Data\r\n          if (this.installUnitList.length) {\r\n            this.rules.InstallUnit_Ids[0].required = true\r\n          } else {\r\n            this.rules.InstallUnit_Ids[0].required = false\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async projectChange() {\r\n      const Sys_Project_Id = this.form.Sys_Project_Id\r\n      this.clearTb()\r\n      this.form.Area_Id = ''\r\n      this.form.InstallUnit_Ids = []\r\n      this.treeParamsArea.data = []\r\n      this.$nextTick(_ => {\r\n        this.$refs.treeSelectArea.treeDataUpdateFun([])\r\n      })\r\n      if (Sys_Project_Id) {\r\n        await this.getAreaList(Sys_Project_Id)\r\n      }\r\n    },\r\n    async areaChange() {\r\n      this.clearTb()\r\n      await this.getInstallUnitPageList(this.form.Area_Id)\r\n      if (this.installUnitList.length && this.form.Area_Id) {\r\n        this.form.InstallUnit_Ids = [this.installUnitList[0].Id]\r\n        this.rules.InstallUnit_Ids[0].required = true\r\n      } else {\r\n        this.rules.InstallUnit_Ids[0].required = false\r\n        this.$refs.installUnitRef.clearValidate()\r\n      }\r\n    },\r\n    areaClear() {\r\n      this.form.Area_Id = ''\r\n      this.form.InstallUnit_Ids = []\r\n      this.clearTb()\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n    },\r\n    getFactoryPeople() {\r\n      GetFactoryPeoplelist({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.peopleList = Object.freeze(res.Data)\r\n\r\n          const curId = localStorage.getItem('UserId')\r\n          const cur = this.peopleList.find(v => v.Id === curId)\r\n          if (cur) {\r\n            this.form.Handle_UserId = cur.Id\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getFactoryChangeTypeList() {\r\n      GetMocOrderTypeList({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.changeTypeList = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async getDepTree() {\r\n      const getFactoryDeptId = async() => {\r\n        return await GetCurFactory({}).then((res) => {\r\n          if (res.IsSucceed) {\r\n            return res?.Data[0]?.Dept_Id\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n\r\n      const getDept = async(depId) => {\r\n        await GetCompanyDepartTree({}).then(res => {\r\n          if (res.IsSucceed) {\r\n            const origin = res.Data?.[0]\r\n            if (origin.Children.length) {\r\n              const tree = origin.Children.filter(v => v.Id === depId)\r\n\r\n              const disableDirectory = (treeArray) => {\r\n                treeArray.map(element => {\r\n                  if (element.Children && element.Children.length > 0) {\r\n                    element.disabled = true\r\n                    disableDirectory(element.Children)\r\n                  }\r\n                })\r\n              }\r\n              disableDirectory(tree)\r\n              this.$refs.treeSelect.treeDataUpdateFun(tree || [])\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n      const depId = await getFactoryDeptId()\r\n      await getDept(depId)\r\n    },\r\n    getTableInfo(fileObj) {\r\n      this.tbLoading = true\r\n      const form = { ...this.form }\r\n      ImportChangFile({\r\n        ...form,\r\n        ImportType: this.changeMethod,\r\n        InstallUnit_Ids: this.form.InstallUnit_Ids.toString(),\r\n        AttachmentList: [fileObj]\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.isImportFile = true\r\n          const { AttachmentList, MocOrderDetailList } = res.Data\r\n          // this.getTbList(Orignal_Deepen_List, Import_Deepen_List)\r\n          // filePath\r\n          if (AttachmentList.length) {\r\n            this.filePath = AttachmentList[0].File_Url\r\n            this.form.Deepen_File_Url = this.filePath\r\n            this.form.Deepen_File_Url_List = [fileObj]\r\n          }\r\n          this.setTbData(MocOrderDetailList)\r\n        } else {\r\n          if (res.Data && res.Data.ErrorFileUrl) {\r\n            window.open(combineURL(this.$baseUrl, res.Data.ErrorFileUrl), '_blank')\r\n          }\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    setAllWeight(row) {\r\n      return {\r\n        SteelAllWeight: numeral(row.SteelWeight).multiply(row.SteelAmount).format('0.[000]')\r\n      }\r\n    },\r\n    setTbData(list) {\r\n      const setItem = (list, item, isAfterValue = false) => {\r\n        const updatedItem = {\r\n          ...item,\r\n          CodeType: item.Type === 0 ? 1 : item.Type === 1 ? 2 : 3,\r\n          parentChildrenId: item.Id,\r\n          uuid: item.MocIdBefore,\r\n          checked: false,\r\n          changeContent: item.MocType,\r\n          changeType: changeTypeReverse[item.MocType],\r\n          ...this.setAllWeight(item)\r\n        }\r\n        this.setItemMocContent(updatedItem, isAfterValue)\r\n        if (updatedItem.changeType === 'isDelete') {\r\n          const childrenItems = this.findChildItems(updatedItem, list)\r\n          if (childrenItems.length) {\r\n            childrenItems.forEach(childItem => {\r\n              childItem.isDisabled = true\r\n            })\r\n          }\r\n        }\r\n        return updatedItem\r\n      }\r\n\r\n      this.defaultTbData = list.map(item => setItem(list, item))\r\n\r\n      this.tbData = list.map(item => setItem(list, item, true))\r\n    },\r\n    setItemMocContent(item, isAfterValue = false) {\r\n      if (item.MocContent) {\r\n        let _MocContent = JSON.parse(item.MocContent)\r\n        if (isArray(_MocContent) && _MocContent.length) {\r\n          _MocContent = _MocContent.filter(m => m.ChangeFieldCode !== 'PartNum')\r\n          const _list = _MocContent.map(m => {\r\n            const _codes = getAllCodesByType(item.CodeType)\r\n            const cur = _codes.find(v => v.Code === m.ChangeFieldCode)\r\n            item[m.ChangeFieldCode] = isAfterValue ? m.AfterValue : m.BeforeValue\r\n\r\n            return {\r\n              Field_Type: cur?.Field_Type || 'string',\r\n              IsCoreField: cur?.IsCoreField || false,\r\n              Code: m.ChangeFieldCode,\r\n              Name: m.ChangeFieldName,\r\n              Value: m.BeforeValue,\r\n              NewValue: m.AfterValue\r\n            }\r\n          })\r\n          this.$store.dispatch('contactList/addChangeCode', { uuid: item.uuid, list: _list })\r\n        }\r\n      }\r\n    },\r\n    beforeUpload(file) {\r\n      this.uploadLoading = true\r\n    },\r\n    handleChange(file, fileList) {\r\n      this.fileList = fileList\r\n    },\r\n    async handlePreview(file) {\r\n      console.log(file)\r\n      const arr = file.name.split('.')\r\n      const isDwg = arr[arr.length - 1] === 'dwg'\r\n      const { Data } = await GetOssUrl({ url: file.url })\r\n      if (isDwg) {\r\n        window.open('http://dwgv1.bimtk.com:5432/?CadUrl=' + Data, '_blank')\r\n      } else {\r\n        window.open(Data)\r\n      }\r\n    },\r\n    handleRemove(file, fileList) {\r\n      this.fileList = fileList\r\n      this.checkUploading(fileList)\r\n      this.form.AttachmentList = this.fileList.map(item => {\r\n        if (item.url) {\r\n          return {\r\n            File_Url: item.url,\r\n            File_Name: item.name\r\n          }\r\n        } else {\r\n          const url = item.response.Data\r\n          const fileInfo = url.split('*')\r\n          const fileObj = {\r\n            File_Url: fileInfo[0],\r\n            File_Size: fileInfo[1],\r\n            File_Type: fileInfo[2],\r\n            File_Name: fileInfo[3]\r\n          }\r\n          return {\r\n            File_Url: fileObj.File_Url,\r\n            File_Name: fileObj.File_Name\r\n          }\r\n        }\r\n      })\r\n    },\r\n    uploadSuccess(response, file, fileList) {\r\n      if (!response || !response.Data) {\r\n        return\r\n      }\r\n      this.checkUploading(fileList)\r\n      this.form.AttachmentList = this.fileList.map(item => {\r\n        if (item.url) {\r\n          return {\r\n            File_Url: item.url,\r\n            File_Name: item.name\r\n          }\r\n        } else {\r\n          if (item.status !== 'success') return\r\n          const url = item.response.Data\r\n          const fileInfo = url.split('*')\r\n          const fileObj = {\r\n            File_Url: fileInfo[0],\r\n            File_Size: fileInfo[1],\r\n            File_Type: fileInfo[2],\r\n            File_Name: fileInfo[3]\r\n          }\r\n          return {\r\n            File_Url: fileObj.File_Url,\r\n            File_Name: fileObj.File_Name\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    handleSave() {\r\n      this.$refs['form'].validate(async(valid) => {\r\n        if (valid) {\r\n          this.saveLoading = true\r\n          await this.submit(true)\r\n          this.saveLoading = false\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    async handleSubmit() {\r\n      this.$refs['form'].validate(async(valid) => {\r\n        if (valid) {\r\n          this.submitLoading = true\r\n          await this.submit(false)\r\n          this.submitLoading = false\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    async submit(isDraft) {\r\n      console.log('this.form', this.form)\r\n      const _form = { ...this.form }\r\n      let submitTb = []\r\n      if (this.changeMethod === 3) {\r\n        submitTb = this.tbData.map((item) => {\r\n          const { children, uuid, changeContent, checked, changeType, isShow, ...others } = item\r\n          const changeMap = this.$store.state.contactList.changeCode\r\n          const _list = (changeMap[uuid] || []).map(v => {\r\n            others[v.Code] = v.NewValue\r\n            return {\r\n              ChangeFieldCode: v.Code,\r\n              ChangeFieldName: v.Name,\r\n              BeforeValue: v.Value,\r\n              AfterValue: v.NewValue\r\n            }\r\n          })\r\n          others.MocContent = JSON.stringify(_list)\r\n          others.MocType = changeContent\r\n          return others\r\n        })\r\n        console.log(JSON.parse(JSON.stringify(submitTb)))\r\n        _form.Deepen_File_Url = null\r\n        _form.Deepen_File_Url_List = null\r\n      } else {\r\n        submitTb = this.tbData\r\n      }\r\n      const isReNew = this.isImportFile && this.changeMethod !== 3 && this.isEdit\r\n      const subObj = {\r\n        ..._form,\r\n        IsNewImportFile: isReNew,\r\n        Handle_UserName: localStorage.getItem('UserName'),\r\n        Moc_Type_Name: this.changeTypeList.find(item => item.Id === _form.Moc_Type_Id)?.Display_Name,\r\n        Change_Type: this.changeMethod === 1 ? 0 : this.changeMethod === 2 ? 1 : 2,\r\n        InstallUnit_Ids: Array.isArray(_form.InstallUnit_Ids) ? _form.InstallUnit_Ids.join(',') : _form.InstallUnit_Ids,\r\n        Is_Draft: isDraft,\r\n        OrderDetail: submitTb\r\n      }\r\n      if (this.changeMethod !== 3) {\r\n        subObj.Deepen_File_Url = this.filePath\r\n      }\r\n      await SaveMocOrder(subObj).then(async res => {\r\n        if (res.IsSucceed) {\r\n          if (isDraft) {\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            closeTagView(this.$store, this.$route)\r\n          } else {\r\n            if (!res.Data) {\r\n              this.$message({\r\n                message: '提交失败',\r\n                type: 'wrarning'\r\n              })\r\n              return\r\n            }\r\n            await this.submitCheck(res.Data)\r\n          }\r\n        } else {\r\n          if (res.Data && res.Data.Path) {\r\n            window.open(combineURL(this.$baseUrl, res.Data.Path), '_blank')\r\n          }\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    async submitCheck(Id) {\r\n      await SubmitMocOrder({\r\n        Id\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '提交成功',\r\n            type: 'success'\r\n          })\r\n          closeTagView(this.$store, this.$route)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.searchForm.component_name = ''\r\n      this.searchForm.part_name = ''\r\n      this.searchForm.assembly_name = ''\r\n      this.searchForm.component_search_mode = 1\r\n      this.searchForm.part_search_mode = 1\r\n      this.searchForm.assembly_search_mode = 1\r\n      this.$refs?.tableRef?.setAllTreeExpand(false)\r\n      this.$refs?.tableRef?.clearFilter()\r\n    },\r\n    clearTb() {\r\n      this.tbData = []\r\n      this.defaultTbData = []\r\n      this.handleReset()\r\n    },\r\n    handleFilter() {\r\n      this.nameMapping = {\r\n        ComponentName: {},\r\n        SteelName: {},\r\n        PartName: {}\r\n      }\r\n      const changeMaps = this.$store.state.contactList.changeCode\r\n      Object.keys(changeMaps).forEach(uuid => {\r\n        const changeList = changeMaps[uuid]\r\n        changeList.forEach(item => {\r\n          if (item.Code === 'ComponentName') {\r\n            this.nameMapping.ComponentName[item.Value] = item.NewValue\r\n          } else if (item.Code === 'SteelName') {\r\n            this.nameMapping.SteelName[item.Value] = item.NewValue\r\n          } else if (item.Code === 'PartName') {\r\n            this.nameMapping.PartName[item.Value] = item.NewValue\r\n          }\r\n        })\r\n      })\r\n\r\n      const xTable = this.$refs.tableRef\r\n      const codeColumn = xTable.getColumnByField('CPCode')\r\n      const option = codeColumn.filters[0]\r\n      option.data = [this.searchForm.component_name, this.searchForm.assembly_name, this.searchForm.part_name]\r\n      option.checked = true\r\n      xTable.updateData()\r\n      this.$refs.tableRef.setAllTreeExpand(true)\r\n      this.$refs.tableRef.clearCheckboxRow()\r\n    },\r\n\r\n    getFeeGap(activity, index) {\r\n      if (index === 0) {\r\n        return activity.Fee || 0\r\n      } else {\r\n        const result = numeral(activity.Fee || 0)\r\n          .subtract(this.activities[index - 1].Fee || 0)\r\n\r\n        if (result.value() < 0) {\r\n          activity.isRed = true\r\n        } else if (result.value() > 0) {\r\n          activity.isBlue = true\r\n        }\r\n        return result.value() === 0 ? 0 : result.format('+0.[00]')\r\n      }\r\n    },\r\n    handleEdit(row) {\r\n      console.log(row, 'row')\r\n      this.dialogVisible = true\r\n      this.currentComponent = 'HandleEdit'\r\n      this.width = '50%'\r\n      this.title = `编辑（${this.getCpCode(row)}）`\r\n      this.$nextTick(() => {\r\n        const defaultRow = this.defaultTbData.find(item => item.uuid === row.uuid)\r\n        console.log(defaultRow, 'defaultRow')\r\n        this.$refs.content?.init(row, defaultRow, this.isEdit, this.tbData, this.allCodes)\r\n      })\r\n    },\r\n    handleDelete(row) {\r\n      console.log('row', row)\r\n      this.$confirm('确认要删除这条记录吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.deleteTableItem(row.uuid)\r\n      }).catch(() => {\r\n        // User canceled\r\n      })\r\n    },\r\n    handleRestore(row) {\r\n      this.restoreTableItem(row.uuid)\r\n    },\r\n    changeMethodFun(val) {\r\n      console.log('val', val)\r\n      const setTbCloumn = () => {\r\n        if (val === 3) {\r\n          const columnCode = ['CPCode', 'SetupPosition', 'Production_Status', 'changeContent']\r\n          const _columns = this.columns.filter(item => columnCode.includes(item.Code))\r\n          this.columns = _columns\r\n          this.$nextTick(_ => {\r\n            this.$refs.tableRef.refreshColumn()\r\n          })\r\n        } else if (val === 1) {\r\n          this.columns = deepClone(this.rootColumns)\r\n        } else {\r\n          this.columns = deepClone(this.rootColumns)\r\n        }\r\n        this.$store.dispatch('contactList/resetChangeCode')\r\n        this.changeMethod = val\r\n      }\r\n\r\n      if (this.tbData && this.tbData.length > 0) {\r\n        return this.$confirm('切换变更方式会清空当前已添加的变更明细，是否继续？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.tbData = []\r\n          setTbCloumn()\r\n          this.filePath = ''\r\n        }).catch(() => {\r\n\r\n        })\r\n      } else {\r\n        this.filePath = ''\r\n        setTbCloumn()\r\n      }\r\n    },\r\n    getMocModelList(list) {\r\n      const existingUuids = new Set(this.tbData.map(item => item.uuid))\r\n      list = list.filter(item => !existingUuids.has(item.MocIdBefore))\r\n\r\n      if (!list.length) {\r\n        return\r\n      }\r\n\r\n      list = list.map(item => {\r\n        // const curParent = this.findParentItem(item)\r\n        // if (curParent && curParent.changeType === 'isDelete') {\r\n        //   item.changeType = 'isDelete'\r\n        //   item.changeContent = this.getChangeTypeText(item.changeType)\r\n        //   item.isDisabled = true\r\n        //   this.deleteTableItem(item.uuid)\r\n        //   return {\r\n        //     ...item,\r\n        //     parentChildrenId: item.Id,\r\n        //     uuid: item.MocIdBefore,\r\n        //     checked: false,\r\n        //     CodeType: item.Type === 0 ? 1 : item.Type === 1 ? 2 : 3,\r\n        //     ...this.setAllWeight(item)\r\n        //   }\r\n        // } else {\r\n        this.updateItemChangeStatus(item, [])\r\n        return {\r\n          changeType: 'isNoChange',\r\n          changeContent: this.getChangeTypeText('isNoChange'),\r\n          ...item,\r\n          parentChildrenId: item.Id,\r\n          uuid: item.MocIdBefore,\r\n          checked: false,\r\n          CodeType: item.Type === 0 ? 1 : item.Type === 1 ? 2 : 3,\r\n          ...this.setAllWeight(item)\r\n        }\r\n        // }\r\n      })\r\n\r\n      this.tbData = [...this.tbData, ...list].sort((a, b) => b.Type - a.Type)\r\n\r\n      const _defaultTbData = this.defaultTbData || []\r\n      this.defaultTbData = JSON.parse(JSON.stringify([..._defaultTbData, ...list]))\r\n      this.setSameItems(this.tbData)\r\n    },\r\n    setSameItems(tbData) {\r\n      const changeInfos = { ...this.$store.state.contactList.changeCode }\r\n      const mocBeforeItems = tbData.filter(item => {\r\n        return !!changeInfos[item.uuid]\r\n      })\r\n      const isDeleteItems = tbData.filter(item => item.changeType === 'isDelete')\r\n      if (isDeleteItems.length) {\r\n        console.log(isDeleteItems, 'isDeleteItems')\r\n        const unitPart = isDeleteItems.filter(item => item.Type === 3)\r\n        if (unitPart.length) {\r\n          unitPart.forEach(item => {\r\n            const unitP = this.findParentItem(item)\r\n            if (unitP && unitP.changeType !== 'isDelete') {\r\n              const similarUnitPartItems = this.findSimilarItems(item)\r\n              if (similarUnitPartItems.length) {\r\n                similarUnitPartItems.forEach(similarItem => {\r\n                  const isSame = this.isSameParent(item, similarItem)\r\n                  if (!isSame) return\r\n                  this.$set(similarItem, 'changeType', 'isDelete')\r\n                  this.$set(similarItem, 'changeContent', this.getChangeTypeText('isDelete'))\r\n                })\r\n              }\r\n            }\r\n          })\r\n        }\r\n        // isDeleteItems.forEach(item => {\r\n        //   const similarItems = this.findSimilarItems(item)\r\n        //   if (similarItems.length) {\r\n        //     similarItems.forEach(similarItem => {\r\n        //       console.log(item.Code, 'similarItems')\r\n        //       this.$set(similarItem, 'changeType', item.changeType)\r\n        //       this.$set(similarItem, 'changeContent', item.changeContent)\r\n        //       // const isDisabled = this.isSameParent(item, similarItem)\r\n        //       // this.$set(similarItem, 'isDisabled', !isDisabled)\r\n        //       // if (isDisabled) {\r\n        // this.$set(similarItem, 'changeType', item.changeType)\r\n        // this.$set(similarItem, 'changeContent', item.changeContent)\r\n        //       // }\r\n        //     })\r\n        //   }\r\n        // })\r\n      }\r\n      if (!mocBeforeItems.length) return\r\n      mocBeforeItems.forEach(item => {\r\n        let _list = this.findSimilarItems(item)\r\n        _list = _list.filter(k => !changeInfos[k.uuid])\r\n        if (_list.length) {\r\n          _list.forEach(cur => {\r\n            if (this.isSameParent(item, cur)) {\r\n              const changeList = this.$store.state.contactList.changeCode[item.uuid]\r\n              changeList.forEach(change => {\r\n                cur[change.Code] = item[change.Code]\r\n              })\r\n\r\n              this.$store.dispatch('contactList/addChangeCode', {\r\n                uuid: cur.uuid,\r\n                list: changeList\r\n              })\r\n              console.log('cur', item.isDisabled)\r\n              if (item.changeType === 'isDelete') {\r\n                // this.$set(cur, 'isDisabled', item.isDisabled)\r\n\r\n              } else {\r\n                this.updateItemChangeStatus(cur, changeList)\r\n              }\r\n            } else {\r\n              const { SteelAmount, ...others } = item\r\n              const filteredList = (this.$store.state.contactList.changeCode[item.uuid] || []).filter(change => change.Code !== 'SteelAmount')\r\n              filteredList.forEach(change => {\r\n                cur[change.Code] = item[change.Code]\r\n              })\r\n              // cur.CPCode = item.CPCode\r\n              this.$store.dispatch('contactList/addChangeCode', {\r\n                uuid: cur.uuid,\r\n                list: filteredList\r\n              })\r\n              this.updateItemChangeStatus(cur, filteredList)\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    editInfo({ row, list }) {\r\n      console.log('editInfo row, list', row, list)\r\n      const _changeMaps = {}\r\n      list.forEach(item => {\r\n        _changeMaps[item.Code] = item.NewValue\r\n      })\r\n      // this.resetDefaultVal()\r\n\r\n      const existingChanges = this.$store.state.contactList.changeCode[row.uuid] || []\r\n      const existingChangeCodes = existingChanges.map(change => change.Code)\r\n\r\n      const removedChangeCodes = existingChangeCodes.filter(code => !list.some(item => item.Code === code))\r\n      console.log('已移除的字段', removedChangeCodes)\r\n\r\n      if (removedChangeCodes.length) {\r\n        const defaultRow = this.defaultTbData.find(item => item.uuid === row.uuid)\r\n        removedChangeCodes.forEach(code => {\r\n          console.log(`重置字段 ${code} 为原始值:`, defaultRow[code])\r\n          _changeMaps[code] = defaultRow[code]\r\n        })\r\n      }\r\n      console.log('_changeMaps', JSON.parse(JSON.stringify(_changeMaps)))\r\n\r\n      // 批量更新表格项\r\n      this.batchUpdateTableItem(row.uuid, _changeMaps)\r\n      // this.updateCodesName(row, _changeMaps)\r\n    },\r\n    // updateCodesName(targetItem, _changeMaps) {\r\n    //   if (_changeMaps.SteelName) {\r\n    //     targetItem.SteelName = _changeMaps.SteelName\r\n    //     targetItem.CPCode = _changeMaps.SteelName\r\n    //   } else if (_changeMaps.ComponentName) {\r\n    //     targetItem.ComponentName = _changeMaps.ComponentName\r\n    //     targetItem.CPCode = _changeMaps.ComponentName\r\n    //   } else if (_changeMaps.PartName) {\r\n    //     targetItem.PartName = _changeMaps.PartName\r\n    //     targetItem.CPCode = _changeMaps.PartName\r\n    //   } else {\r\n    //     const defaultRow = this.defaultTbData.find(item => item.uuid === targetItem.uuid)\r\n    //     console.log('defaultRow', JSON.parse(JSON.stringify(defaultRow)))\r\n    //     if (defaultRow) {\r\n    //       targetItem.SteelName = defaultRow.SteelName\r\n    //       targetItem.CPCode = defaultRow.CPCode\r\n    //     }\r\n    //   }\r\n    //   console.log('targetItem', JSON.parse(JSON.stringify(targetItem)))\r\n    //   const _list = this.findSimilarItems(targetItem)\r\n    //   if (_list.length) {\r\n    //     _list.forEach(item => {\r\n    //       item.SteelName = targetItem.SteelName\r\n    //       item.CPCode = targetItem.CPCode\r\n    //     })\r\n    //   }\r\n    // },\r\n    handleCancelChange() {\r\n      const selectedRecords = []\r\n      const getIds = (array) => {\r\n        if (!array || !array.length) return\r\n        array.forEach(item => {\r\n          selectedRecords.push(item.uuid)\r\n          if (item.children && item.children.length) {\r\n            getIds(item.children)\r\n          }\r\n        })\r\n      }\r\n      getIds(this.multipleSelection)\r\n      console.log('selectedRecords', selectedRecords)\r\n      selectedRecords.forEach(item => {\r\n        this.$store.dispatch('contactList/delChangeCode', item)\r\n      })\r\n      this.tbData = this.tbData.filter(item => !selectedRecords.includes(item.uuid))\r\n      this.defaultTbData = this.defaultTbData.filter(item => !selectedRecords.includes(item.uuid))\r\n      this.multipleSelection = []\r\n    },\r\n    multiSelectedChange(array) {\r\n      console.log('array', array)\r\n      console.log('array.records', this.$refs.tableRef.getCheckboxRecords(true))\r\n      const { records } = array\r\n      this.multipleSelection = array.records\r\n    },\r\n    handelFilePath() {\r\n      GetOssUrl({\r\n        url: this.filePath\r\n      }).then(res => {\r\n        window.open(res.Data)\r\n      })\r\n    },\r\n    getChangeStyle(changeName) {\r\n      const arr = changeName.split(',')\r\n      const rusult = ['cs-c-box']\r\n      if (arr.includes(changeType.isAdd)) {\r\n        rusult.push('cs-change-green')\r\n      } else if (arr.includes(changeType.isAdjust)) {\r\n        rusult.push('cs-change-yellow')\r\n      } else if (arr.includes(changeType.isDecrease) || arr.includes(changeType.isIncrease)) {\r\n        rusult.push('cs-change-blue')\r\n      } else if (arr.includes(changeType.isDelete)) {\r\n        rusult.push('cs-change-red')\r\n      } else {\r\n        rusult.push('cs-default')\r\n      }\r\n      return rusult\r\n    },\r\n    getCpCode(row) {\r\n      if (row.Type === 0) {\r\n        return row.SteelName\r\n      } else if (row.Type === 1) {\r\n        return row.ComponentName\r\n      } else {\r\n        return row.PartName\r\n      }\r\n    },\r\n    handleShow(row) {\r\n      const changeList = this.$store.state.contactList.changeCode[row.uuid]\r\n      this.changeRowContentList = changeList || []\r\n    },\r\n    filterNameMethod({ option, values, cellValue, row, column }) {\r\n      const result = this.filterCustom(row)\r\n      return result\r\n    },\r\n    filterCustom(row) {\r\n      const { component_name, component_search_mode, assembly_name,\r\n        assembly_search_mode, part_name, part_search_mode } = this.searchForm\r\n\r\n      const _ComponentName = this.nameMapping.ComponentName[row.ComponentName] || row.ComponentName || ''\r\n      const _SteelName = this.nameMapping.SteelName[row.SteelName] || row.SteelName || ''\r\n      const _PartName = this.nameMapping.PartName[row.PartName] || row.PartName || ''\r\n\r\n      let partMatch = true\r\n\r\n      if (part_name) {\r\n        if (part_search_mode === 1) {\r\n          partMatch = _PartName.includes(part_name)\r\n        } else {\r\n          partMatch = _PartName === part_name\r\n        }\r\n      }\r\n      let assemblyMatch = true\r\n      if (assembly_name) {\r\n        if (assembly_search_mode === 1) {\r\n          assemblyMatch = _ComponentName.includes(assembly_name)\r\n        } else {\r\n          assemblyMatch = _ComponentName === assembly_name\r\n        }\r\n      }\r\n      let componentMatch = true\r\n      if (component_name) {\r\n        if (component_search_mode === 1) {\r\n          componentMatch = _SteelName.includes(component_name)\r\n        } else {\r\n          componentMatch = _SteelName === component_name\r\n        }\r\n      }\r\n      // console.log(componentMatch, assemblyMatch, partMatch)\r\n\r\n      const result = componentMatch && assemblyMatch && partMatch\r\n      if (result) {\r\n        return true\r\n      } else {\r\n        return false\r\n      }\r\n    },\r\n    installChange(arr) {\r\n      if (!arr || !arr.length) {\r\n        this.clearTb()\r\n        return\r\n      }\r\n      this.tbData = this.tbData.filter(item => arr.some(id => id === item.InstallUnit_Id))\r\n      this.defaultTbData = this.defaultTbData.filter(item => arr.some(id => id === item.InstallUnit_Id))\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.cs-type {\r\n  padding: 2px;\r\n  font-weight: 400;\r\n  font-size: 12px;\r\n  color: #146EB4;\r\n  border-radius: 4px;\r\n  margin-right: 4px;\r\n  background-color: rgba(66, 107, 216, .1);\r\n}\r\n\r\n.cs-change {\r\n  padding: 2px 4px;\r\n  font-weight: 400;\r\n  font-size: 12px;\r\n  color: #298DFF;\r\n  border-radius: 4px;\r\n  background-color: rgba(41, 141, 255, .1)\r\n}\r\n\r\n.cs-c-box {\r\n  padding: 2px 4px;\r\n  font-weight: 400;\r\n  font-size: 12px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.cs-change-green {\r\n  color: #ffffff;\r\n  background-color: #3ECC93\r\n}\r\n\r\n.cs-change-green-p {\r\n  color: #3ECC93;\r\n  background-color: rgba(62, 204, 147, .1);\r\n}\r\n\r\n.cs-change-blue {\r\n  color: #ffffff;\r\n  background-color: #298DFF\r\n}\r\n\r\n.cs-change-blue-p {\r\n  color: #298DFF;\r\n  background-color: rgba(41, 141, 255, .1)\r\n}\r\n\r\n.cs-change-red {\r\n  color: #ffffff;\r\n  background-color: #FB6B7F\r\n}\r\n\r\n.cs-change-red-p {\r\n  color: #FB6B7F;\r\n  background-color: rgba(251, 107, 127, .1)\r\n}\r\n\r\n.cs-default {\r\n  color: #ffffff;\r\n  background-color: #8E95AA\r\n}\r\n\r\n.cs-default-p {\r\n  color: #8E95AA;\r\n  background-color: rgba(142, 149, 170, .1)\r\n}\r\n\r\n.cs-change-yellow {\r\n  color: #ffffff;\r\n  background-color:  #FB8F00;\r\n}\r\n.cs-green {\r\n  background: rgba(62, 204, 147, .1);\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  color: #3ECC93;\r\n  padding: 3px 10px;\r\n}\r\n\r\n.cs-yellow {\r\n  background: rgba(241, 180, 48, .1);\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  color: #F1B430;\r\n  padding: 3px 10px;\r\n}\r\n\r\n.cs-red {\r\n  background: rgba(251, 107, 127, .1);\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  color: #FB6B7F;\r\n  padding: 3px 10px;\r\n}\r\n\r\n.m-4 {\r\n  margin: 0 4px;\r\n}\r\n\r\n.cs-title {\r\n  font-weight: bold;\r\n  font-size: 16px;\r\n  color: rgba(34, 40, 52, 0.85);\r\n}\r\n\r\n.page-container {\r\n  margin: 16px;\r\n\r\n  .form-x {\r\n    background-color: #FFFFFF;\r\n    padding: 16px;\r\n  }\r\n\r\n  .cs-main {\r\n    margin-top: 16px;\r\n    background-color: #FFFFFF;\r\n    padding: 16px;\r\n  }\r\n}\r\n\r\n.cs-fee{\r\n  margin-top: 16px;\r\n  background-color: #FFFFFF;\r\n  padding: 16px 16px 0 16px;\r\n  .line-content{\r\n    align-items: center;\r\n    display: flex;\r\n  }\r\n  .cs-title{\r\n    margin-right: 8px;\r\n  }\r\n  .cs-label{\r\n    font-size: 14px;\r\n  }\r\n  .fw{\r\n    font-weight: bold;\r\n  }\r\n  .cs-blue{\r\n    color: #298DFF\r\n  }\r\n  .cs-red{\r\n    color: #FB6B7F\r\n  }\r\n  .fee-name{\r\n    font-weight: bold;\r\n    font-size: 14px;\r\n    color: rgba(34,40,52,0.85);\r\n    margin-right: 32px;\r\n  }\r\n  .fee-sub{\r\n    font-weight: bold;\r\n    font-size: 12px;\r\n    color: rgba(34,40,52,0.65);\r\n    margin-right: 32px;\r\n  }\r\n  .fee-num{\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    margin-right: 32px;\r\n  }\r\n  .fee-time{\r\n    font-weight: 400;\r\n    margin-right: 32px;\r\n    font-size: 12px;\r\n    color: rgba(34,40,52,0.65);\r\n  }\r\n  .fee-remark{\r\n    font-weight: bold;\r\n    font-size: 12px;\r\n    color: rgba(34,40,52,0.65);\r\n  }\r\n  .circle {\r\n    width: 14px;\r\n    height: 14px;\r\n    border-radius: 50%;\r\n    border: 4px solid #458CF7;\r\n    background-color: white;\r\n  }\r\n  ::v-deep{\r\n    .el-timeline-item__tail{\r\n      height: 37%;\r\n      margin: 18px 0;\r\n      width: 1px;\r\n      left: 5px;\r\n      border: 1px solid rgba(41, 141, 255, 0.3);\r\n    }\r\n  }\r\n}\r\n\r\n.el-divider--horizontal {\r\n  margin: 16px 0;\r\n}\r\n\r\nfooter {\r\n  margin-top: 16px;\r\n  text-align: right;\r\n}\r\n\r\n.cs-toolBar {\r\n  ::v-deep {\r\n    .vxe-button--icon.vxe-icon-custom-column{\r\n      display: none;\r\n    }\r\n\r\n    .vxe-button.type--button.is--circle {\r\n      width: 97px;\r\n      z-index: 0;\r\n      border-radius: 4px;\r\n    }\r\n\r\n    .el-form-item {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n.el-tree-select{\r\n  ::v-deep{\r\n    .el-select{\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n\r\n.change-method-form {\r\n  margin-bottom: 0px;\r\n}\r\n.cs-tree-table{\r\n ::v-deep{\r\n   .vxe-tree-cell{\r\n     text-align: left !important;\r\n   }\r\n   .vxe-checkbox--label{\r\n     color:#333333;\r\n   }\r\n   .col--checkbox{\r\n    .vxe-cell{\r\n      padding-left: 10px !important;\r\n    }\r\n   }\r\n }\r\n}\r\n\r\n.z-upload.hiddenBtn{\r\n  ::v-deep{\r\n    .el-upload{\r\n      display: none;\r\n    }\r\n    .el-upload-list__item:first-child{\r\n      margin-top: 4px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
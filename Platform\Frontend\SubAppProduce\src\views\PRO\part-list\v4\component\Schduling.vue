<template>
  <div>
    <el-row>
      <el-table stripe class="cs-custom-table" :data="tableData" empty-text="暂无数据" border style="width: 100%">
        <el-table-column prop="Scheduling_Status" :label="levelName + '状态'" align="center" />
        <el-table-column prop="Component_Count" label="数量" align="center" />
      </el-table>

      <el-col :span="24">
        <div style="text-align: right; margin-top: 10px">
          <el-button @click="$emit('close')">关 闭</el-button>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { GetSchedulingPartList } from '@/api/PRO/project'
export default {
  props: {
    typeEntity: {
      type: Object,
      default: () => {
        return {}
      }

    },
    levelName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableData: [

      ]
    }
  },
  mounted() {},
  methods: {
    init(rowData) {
      GetSchedulingPartList({ id: rowData.Part_Aggregate_Id }).then((res) => {
        if (res.IsSucceed) {
          this.tableData = res.Data.Scheduling_List
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    }
  }
}
</script>

<style scoped></style>

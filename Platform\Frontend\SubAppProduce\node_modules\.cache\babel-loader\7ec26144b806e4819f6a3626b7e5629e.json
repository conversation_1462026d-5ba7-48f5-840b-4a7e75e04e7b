{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new\\components\\BatchProcessAdjust.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new\\components\\BatchProcessAdjust.vue", "mtime": 1757468128009}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9yZWdlbmVyYXRvclJ1bnRpbWUgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9yZWdlbmVyYXRvclJ1bnRpbWUuanMiOwppbXBvcnQgX3RvQ29uc3VtYWJsZUFycmF5IGZyb20gIkQ6L3Byb2plY3QvcGxhdGZvcm1fZnJhbWV3b3JrX21hc3Rlci9wbGF0Zm9ybV9mcmFtZXdvcmsvUGxhdGZvcm0vRnJvbnRlbmQvU3ViQXBwUHJvZHVjZS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vdG9Db25zdW1hYmxlQXJyYXkuanMiOwppbXBvcnQgX2FzeW5jVG9HZW5lcmF0b3IgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9hc3luY1RvR2VuZXJhdG9yLmpzIjsKaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsdGVyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmluZC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZpbmQtaW5kZXguanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5mbGF0LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuaW5jbHVkZXMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5qb2luLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnJlZHVjZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnNvcnQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5zcGxpY2UuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS51bnNjb3BhYmxlcy5mbGF0LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5ldmVyeS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmZpbHRlci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmZpbmQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5mb3ItZWFjaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLm1hcC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLnJlZHVjZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLnNvbWUuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLmluY2x1ZGVzLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaC5qcyI7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCgppbXBvcnQgeyBHZXRQcm9jZXNzTGlzdEJhc2UgfSBmcm9tICdAL2FwaS9QUk8vdGVjaG5vbG9neS1saWInOwppbXBvcnQgRHJhZ2dhYmxlIGZyb20gJ3Z1ZWRyYWdnYWJsZSc7CmltcG9ydCB7IGRlZXBDbG9uZSwgdW5pcXVlQXJyIH0gZnJvbSAnQC91dGlscyc7CmltcG9ydCB7IG1hcEFjdGlvbnMgfSBmcm9tICd2dWV4JzsKaW1wb3J0IHsgdjQgYXMgdXVpZHY0IH0gZnJvbSAndXVpZCc7CmltcG9ydCB7IEdldExpYkxpc3QgfSBmcm9tICdAL2FwaS9QUk8vdGVjaG5vbG9neS1saWInOwpleHBvcnQgZGVmYXVsdCB7CiAgY29tcG9uZW50czogewogICAgRHJhZ2dhYmxlOiBEcmFnZ2FibGUKICB9LAogIHByb3BzOiB7CiAgICBwYWdlVHlwZTogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6IHVuZGVmaW5lZAogICAgfSwKICAgIGlzTmVzdDogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZQogICAgfSwKICAgIHByb2Nlc3NMaXN0OiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogZnVuY3Rpb24gX2RlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIHt9OwogICAgICB9CiAgICB9CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgV29ya2luZ19UZWFtX0xpc3Q6IFtdLAogICAgICBsaXN0OiBbXSwKICAgICAgb3B0aW9uczogW10sCiAgICAgIC8vIGRlZmF1bHRPcHRpb25zOiBbXSwKICAgICAgZ3lMaXN0OiBbXSwKICAgICAgYnRuTG9hZGluZzogZmFsc2UsCiAgICAgIHBnTG9hZGluZzogZmFsc2UsCiAgICAgIGNyYWZ0Q29kZTogJycsCiAgICAgIGZvcm06IHt9CiAgICB9OwogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGlzQ29tOiBmdW5jdGlvbiBpc0NvbSgpIHsKICAgICAgcmV0dXJuIHRoaXMucGFnZVR5cGUgPT09ICdjb20nOwogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgIGxpc3Q6IHsKICAgICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcihuZXdWYWwpIHsKICAgICAgICB2YXIgX3RoaXMkZ3lMaXN0JGZpbmQsCiAgICAgICAgICBfdGhpcyA9IHRoaXM7CiAgICAgICAgaWYgKCF0aGlzLmNyYWZ0Q29kZSkgcmV0dXJuOwogICAgICAgIHZhciB3b3JrQ29kZSA9IChfdGhpcyRneUxpc3QkZmluZCA9IHRoaXMuZ3lMaXN0LmZpbmQoZnVuY3Rpb24gKHYpIHsKICAgICAgICAgIHJldHVybiB2LkNvZGUgPT09IF90aGlzLmNyYWZ0Q29kZTsKICAgICAgICB9KSkgPT09IG51bGwgfHwgX3RoaXMkZ3lMaXN0JGZpbmQgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF90aGlzJGd5TGlzdCRmaW5kLldvcmtDb2RlOwogICAgICAgIHZhciBuZXdDb2RlID0gbmV3VmFsLm1hcChmdW5jdGlvbiAodikgewogICAgICAgICAgcmV0dXJuIHYudmFsdWU7CiAgICAgICAgfSkuZmlsdGVyKGZ1bmN0aW9uICh2KSB7CiAgICAgICAgICByZXR1cm4gISF2OwogICAgICAgIH0pLmpvaW4oJy8nKTsKICAgICAgICBpZiAod29ya0NvZGUgIT09IG5ld0NvZGUpIHsKICAgICAgICAgIHRoaXMuY3JhZnRDb2RlID0gJyc7CiAgICAgICAgfQogICAgICB9LAogICAgICBkZWVwOiB0cnVlCiAgICB9CiAgfSwKICBtZXRob2RzOiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIG1hcEFjdGlvbnMoJ3NjaGVkdWxlJywgWydpbml0UHJvY2Vzc0xpc3QnXSkpLCB7fSwgewogICAgZ2V0UHJvY2Vzc09wdGlvbjogZnVuY3Rpb24gZ2V0UHJvY2Vzc09wdGlvbih3b3Jrc2hvcElkKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICByZXR1cm4gbmV3IFByb21pc2UoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkgewogICAgICAgIF90aGlzMi5wZ0xvYWRpbmcgPSB0cnVlOwogICAgICAgIEdldFByb2Nlc3NMaXN0QmFzZSh7CiAgICAgICAgICB3b3Jrc2hvcElkOiB3b3Jrc2hvcElkLAogICAgICAgICAgdHlwZTogMSAvLyAwOuWFqOmDqO+8jOW3peiJuuexu+WeizHvvJrmnoTku7blt6XoibrvvIwy77ya6Zu25Lu25bel6Im6CiAgICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICBfdGhpczIub3B0aW9ucyA9IHJlcy5EYXRhLm1hcChmdW5jdGlvbiAodikgewogICAgICAgICAgICAgIF90aGlzMi4kc2V0KHYsICdkaXNhYmxlZCcsIGZhbHNlKTsKICAgICAgICAgICAgICByZXR1cm4gdjsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBfdGhpczIuJG1lc3NhZ2UoewogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgICByZXNvbHZlKCk7CiAgICAgICAgfSkuZmluYWxseShmdW5jdGlvbiAoXykgewogICAgICAgICAgX3RoaXMyLnBnTG9hZGluZyA9IGZhbHNlOwogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICBjcmFmdENoYW5nZTogZnVuY3Rpb24gY3JhZnRDaGFuZ2UodmFsKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICB0aGlzLmNyYWZ0Q29kZSA9IHZhbDsKICAgICAgaWYgKCF2YWwpIHsKICAgICAgICAvLyB0aGlzLm9wdGlvbnMgPSB0aGlzLmRlZmF1bHRPcHRpb25zCiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHZhciBpbmZvID0gdGhpcy5neUxpc3QuZmluZChmdW5jdGlvbiAodikgewogICAgICAgIHJldHVybiB2LkNvZGUgPT09IHZhbDsKICAgICAgfSk7CiAgICAgIGlmIChpbmZvKSB7CiAgICAgICAgdmFyIHBsaXN0ID0gaW5mby5Xb3JrQ29kZS5zcGxpdCgnLycpOwogICAgICAgIC8vIHRoaXMub3B0aW9ucyA9IHRoaXMuZGVmYXVsdE9wdGlvbnMuZmlsdGVyKHYgPT4gcGxpc3QuaW5jbHVkZXModi5Db2RlKSkKICAgICAgICB2YXIgbmV3TGlzdCA9IFtdOwogICAgICAgIGNvbnNvbGUubG9nKCdwbGlzdCcsIHBsaXN0KTsKICAgICAgICBwbGlzdC5mb3JFYWNoKGZ1bmN0aW9uIChsaXN0VmFsLCBpZHgpIHsKICAgICAgICAgIHZhciBpdGVtID0gX3RoaXMzLmxpc3QuZmluZChmdW5jdGlvbiAodikgewogICAgICAgICAgICByZXR1cm4gdi52YWx1ZSA9PT0gbGlzdFZhbDsKICAgICAgICAgIH0pOwogICAgICAgICAgY29uc29sZS5sb2coJ2l0ZW0nLCBpdGVtKTsKICAgICAgICAgIGlmIChpdGVtKSB7CiAgICAgICAgICAgIGlmIChpdGVtLlRlYW1zLmxlbmd0aCA9PT0gMSAmJiAhaXRlbS5Xb3JraW5nX1RlYW1fSWQpIHsKICAgICAgICAgICAgICBpdGVtLldvcmtpbmdfVGVhbV9JZCA9IGl0ZW0uVGVhbXNbMF0uSWQ7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgbmV3TGlzdC5wdXNoKGl0ZW0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdmFyIGl0ZW0yID0gX3RoaXMzLm9wdGlvbnMuZmluZChmdW5jdGlvbiAodikgewogICAgICAgICAgICAgIHJldHVybiB2LkNvZGUgPT09IGxpc3RWYWw7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgICBjb25zb2xlLmxvZygnaXRlbTInLCBpdGVtMik7CiAgICAgICAgICAgIGlmIChpdGVtMikgewogICAgICAgICAgICAgIHZhciBvYmogPSB7CiAgICAgICAgICAgICAgICBrZXk6IHV1aWR2NCgpLAogICAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0yLkNvZGUsCiAgICAgICAgICAgICAgICBXb3JraW5nX1RlYW1fSWQ6ICcnLAogICAgICAgICAgICAgICAgVGVhbXM6IGl0ZW0yLlRlYW1zLAogICAgICAgICAgICAgICAgZGF0ZTogJycKICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgIGlmIChpdGVtMi5UZWFtcy5sZW5ndGggPT09IDEgJiYgIW9iai5Xb3JraW5nX1RlYW1fSWQpIHsKICAgICAgICAgICAgICAgIG9iai5Xb3JraW5nX1RlYW1fSWQgPSBpdGVtMi5UZWFtc1swXS5JZDsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgbmV3TGlzdC5wdXNoKG9iaik7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgICB0aGlzLmxpc3QgPSBuZXdMaXN0OwogICAgICB9CiAgICB9LAogICAgZ2V0Q3JhZnRQcm9jZXNzOiBmdW5jdGlvbiBnZXRDcmFmdFByb2Nlc3MoKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICByZXR1cm4gbmV3IFByb21pc2UoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkgewogICAgICAgIEdldExpYkxpc3QoewogICAgICAgICAgSWQ6ICcnLAogICAgICAgICAgVHlwZTogMQogICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgICAgX3RoaXM0Lmd5TGlzdCA9IHJlcy5EYXRhIHx8IFtdOwogICAgICAgICAgICByZXNvbHZlKCk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBfdGhpczQuJG1lc3NhZ2UoewogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIHJlamVjdCgpOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICBzZWxlY3RDaGFuZ2U6IGZ1bmN0aW9uIHNlbGVjdENoYW5nZSh2YWwsIGVsZW1lbnQpIHsKICAgICAgdmFyIF9lbGVtZW50JFRlYW1zOwogICAgICB2YXIgYXJyID0gdGhpcy5saXN0Lm1hcChmdW5jdGlvbiAoaSkgewogICAgICAgIHJldHVybiBpLnZhbHVlOwogICAgICB9KTsKICAgICAgdGhpcy5vcHRpb25zLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0sIGluZGV4KSB7CiAgICAgICAgaXRlbS5kaXNhYmxlZCA9IGFyci5pbmNsdWRlcyhpdGVtLkNvZGUpOwogICAgICAgIGlmIChpdGVtLkNvZGUgPT09IHZhbCkgewogICAgICAgICAgZWxlbWVudC5UZWFtcyA9IGl0ZW0uVGVhbXM7CiAgICAgICAgfQogICAgICB9KTsKICAgICAgaWYgKGVsZW1lbnQpIHsKICAgICAgICBpZiAodmFsKSB7CiAgICAgICAgICB2YXIgX3RoaXMkcHJvY2Vzc0xpc3QkdmFsLCBfdGhpcyRwcm9jZXNzTGlzdCR2YWwyOwogICAgICAgICAgZWxlbWVudC5kYXRlID0gKF90aGlzJHByb2Nlc3NMaXN0JHZhbCA9IHRoaXMucHJvY2Vzc0xpc3RbdmFsXSkgPT09IG51bGwgfHwgX3RoaXMkcHJvY2Vzc0xpc3QkdmFsID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfdGhpcyRwcm9jZXNzTGlzdCR2YWwuRmluaXNoX0RhdGU7CiAgICAgICAgICBlbGVtZW50LldvcmtpbmdfVGVhbV9JZCA9IChfdGhpcyRwcm9jZXNzTGlzdCR2YWwyID0gdGhpcy5wcm9jZXNzTGlzdFt2YWxdKSA9PT0gbnVsbCB8fCBfdGhpcyRwcm9jZXNzTGlzdCR2YWwyID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfdGhpcyRwcm9jZXNzTGlzdCR2YWwyLldvcmtpbmdfVGVhbV9JZDsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgZWxlbWVudC5Xb3JraW5nX1RlYW1fSWQgPSAnJzsKICAgICAgICAgIGVsZW1lbnQuVGVhbXMgPSBbXTsKICAgICAgICB9CiAgICAgIH0KICAgICAgaWYgKCEoZWxlbWVudCAhPT0gbnVsbCAmJiBlbGVtZW50ICE9PSB2b2lkIDAgJiYgZWxlbWVudC5Xb3JraW5nX1RlYW1fSWQpICYmIChlbGVtZW50ID09PSBudWxsIHx8IGVsZW1lbnQgPT09IHZvaWQgMCB8fCAoX2VsZW1lbnQkVGVhbXMgPSBlbGVtZW50LlRlYW1zKSA9PT0gbnVsbCB8fCBfZWxlbWVudCRUZWFtcyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2VsZW1lbnQkVGVhbXMubGVuZ3RoKSA9PT0gMSkgewogICAgICAgIGVsZW1lbnQuV29ya2luZ19UZWFtX0lkID0gZWxlbWVudC5UZWFtc1swXS5JZDsKICAgICAgfQogICAgfSwKICAgIGRhdGVDaGFuZ2U6IGZ1bmN0aW9uIGRhdGVDaGFuZ2UodmFsLCBlbGVtZW50KSB7CiAgICAgIHZhciBpdGVtID0gdGhpcy5vcHRpb25zLmZpbmQoZnVuY3Rpb24gKHYpIHsKICAgICAgICByZXR1cm4gdi5Db2RlID09PSBlbGVtZW50LnZhbHVlOwogICAgICB9KTsKICAgICAgY29uc29sZS5sb2coJ2l0ZW0nLCBpdGVtLCB0aGlzLmxpc3QpOwogICAgICB2YXIgb2JqID0ge307CiAgICAgIGlmIChpdGVtKSB7CiAgICAgICAgdmFyIF90aGlzJGZvcm1JbmxpbmU7CiAgICAgICAgb2JqID0gewogICAgICAgICAgU2NoZHVsaW5nX0lkOiAoX3RoaXMkZm9ybUlubGluZSA9IHRoaXMuZm9ybUlubGluZSkgPT09IG51bGwgfHwgX3RoaXMkZm9ybUlubGluZSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX3RoaXMkZm9ybUlubGluZS5TY2hkdWxpbmdfQ29kZSwKICAgICAgICAgIFByb2Nlc3NfSWQ6IGl0ZW0uSWQsCiAgICAgICAgICBQcm9jZXNzX0NvZGU6IGl0ZW0uQ29kZSwKICAgICAgICAgIEZpbmlzaF9EYXRlOiB2YWwKICAgICAgICB9OwogICAgICB9CiAgICAgIC8vIHRoaXMuJGVtaXQoJ3NldFByb2Nlc3NMaXN0JywgeyBrZXk6IGVsZW1lbnQudmFsdWUsIHZhbHVlOiBvYmogfSkKICAgIH0sCiAgICBoYW5kbGVBZGQ6IGZ1bmN0aW9uIGhhbmRsZUFkZChpdGVtKSB7CiAgICAgIHZhciBhcnIgPSB0aGlzLmxpc3QubWFwKGZ1bmN0aW9uICh2KSB7CiAgICAgICAgcmV0dXJuIHYudmFsdWU7CiAgICAgIH0pOwogICAgICB0aGlzLm9wdGlvbnMuZm9yRWFjaChmdW5jdGlvbiAodikgewogICAgICAgIGlmIChhcnIuaW5jbHVkZXModi5Db2RlKSkgewogICAgICAgICAgdi5kaXNhYmxlZCA9IHRydWU7CiAgICAgICAgfQogICAgICB9KTsKICAgICAgdGhpcy5saXN0LnB1c2goewogICAgICAgIGtleTogdXVpZHY0KCksCiAgICAgICAgdmFsdWU6ICcnLAogICAgICAgIFdvcmtpbmdfVGVhbV9JZDogJycsCiAgICAgICAgVGVhbXM6IFtdLAogICAgICAgIGRhdGU6ICcnCiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZURlbGV0ZTogZnVuY3Rpb24gaGFuZGxlRGVsZXRlKGVsZW1lbnQpIHsKICAgICAgdmFyIGlkeCA9IHRoaXMubGlzdC5maW5kSW5kZXgoZnVuY3Rpb24gKHYpIHsKICAgICAgICByZXR1cm4gdi52YWx1ZSA9PT0gZWxlbWVudC52YWx1ZTsKICAgICAgfSk7CiAgICAgIGlmIChpZHggIT09IC0xKSB7CiAgICAgICAgdGhpcy5saXN0LnNwbGljZShpZHgsIDEpOwogICAgICAgIHRoaXMuc2VsZWN0Q2hhbmdlKCk7CiAgICAgIH0KICAgIH0sCiAgICBnZXRXb3JraW5nVGVhbTogZnVuY3Rpb24gZ2V0V29ya2luZ1RlYW0odGVhbXMsIGN1ckl0ZW0pIHsKICAgICAgdmFyIF90aGlzNSA9IHRoaXM7CiAgICAgIHZhciBuZXdUZWFtcyA9IHRlYW1zLmZpbHRlcihmdW5jdGlvbiAodikgewogICAgICAgIGlmIChfdGhpczUud29ya3Nob3BJZCkgewogICAgICAgICAgcmV0dXJuIHYuV29ya3Nob3BfSWQgPT09IF90aGlzNS53b3Jrc2hvcElkOwogICAgICAgIH0KICAgICAgICByZXR1cm4gdHJ1ZTsKICAgICAgfSk7CiAgICAgIGlmICghbmV3VGVhbXMubGVuZ3RoKSB7CiAgICAgICAgY3VySXRlbS5Xb3JraW5nX1RlYW1fSWQgPSAnJzsKICAgICAgICByZXR1cm4gW107CiAgICAgIH0KICAgICAgaWYgKG5ld1RlYW1zLmV2ZXJ5KGZ1bmN0aW9uICh2KSB7CiAgICAgICAgcmV0dXJuIHYuSWQgIT09IGN1ckl0ZW0uV29ya2luZ19UZWFtX0lkOwogICAgICB9KSkgewogICAgICAgIGN1ckl0ZW0uV29ya2luZ19UZWFtX0lkID0gJyc7CiAgICAgIH0KICAgICAgcmV0dXJuIG5ld1RlYW1zOwogICAgfSwKICAgIHNldERhdGE6IGZ1bmN0aW9uIHNldERhdGEoYXJyLCB0ZWNobm9sb2d5U3RyKSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgICAgdmFyIHRlY2hub2xvZ3lBcnIsIHdvcmtzaG9wSWQsIHBhcnRVc2VkUHJvY2VzcywgY29kZXMsIG9yaWdpbiwgY2hlY2tPcHRpb24sIHRlY2hBcnIsIHh1ciwgaW5kZXhNYXA7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUkKF9jb250ZXh0KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dC5wcmV2ID0gX2NvbnRleHQubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ2FycicsIGFyciwgdGVjaG5vbG9neVN0cik7CiAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDM7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzNi5nZXRDcmFmdFByb2Nlc3MoKTsKICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIHRlY2hub2xvZ3lBcnIgPSBbXTsKICAgICAgICAgICAgICBpZiAodGVjaG5vbG9neVN0cikgewogICAgICAgICAgICAgICAgdGVjaG5vbG9neUFyciA9IHRlY2hub2xvZ3lTdHIuc3BsaXQoJy8nKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgd29ya3Nob3BJZCA9IGFyclswXS5Xb3Jrc2hvcF9JZDsKICAgICAgICAgICAgICBfdGhpczYud29ya3Nob3BJZCA9IHdvcmtzaG9wSWQ7CiAgICAgICAgICAgICAgcGFydFVzZWRQcm9jZXNzID0gW107CiAgICAgICAgICAgICAgYXJyLmZvckVhY2goZnVuY3Rpb24gKHYpIHsKICAgICAgICAgICAgICAgIGlmICh2LlBhcnRfVXNlZF9Qcm9jZXNzKSB7CiAgICAgICAgICAgICAgICAgIHZhciBfYXJyID0gdi5QYXJ0X1VzZWRfUHJvY2Vzcy5zcGxpdCgnLCcpOwogICAgICAgICAgICAgICAgICBwYXJ0VXNlZFByb2Nlc3MucHVzaC5hcHBseShwYXJ0VXNlZFByb2Nlc3MsIF90b0NvbnN1bWFibGVBcnJheShfYXJyKSk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDExOwogICAgICAgICAgICAgIHJldHVybiBfdGhpczYuZ2V0UHJvY2Vzc09wdGlvbih3b3Jrc2hvcElkKTsKICAgICAgICAgICAgY2FzZSAxMToKICAgICAgICAgICAgICBfdGhpczYub3B0aW9ucyA9IF90aGlzNi5vcHRpb25zLmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgICAgdmFyIGZsYWcgPSBmYWxzZTsKICAgICAgICAgICAgICAgIGlmICh0ZWNobm9sb2d5QXJyLmxlbmd0aCAmJiB0ZWNobm9sb2d5QXJyLmluY2x1ZGVzKGl0ZW0uQ29kZSkpIHsKICAgICAgICAgICAgICAgICAgZmxhZyA9IHRydWU7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBpZiAocGFydFVzZWRQcm9jZXNzLmxlbmd0aCAmJiBwYXJ0VXNlZFByb2Nlc3MuaW5jbHVkZXMoaXRlbS5Db2RlKSkgewogICAgICAgICAgICAgICAgICBmbGFnID0gdHJ1ZTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIGlmIChpdGVtLlBhcnRfVHlwZV9Vc2VkX1Byb2Nlc3MgJiYgaXRlbS5QYXJ0X1R5cGVfVXNlZF9Qcm9jZXNzID09PSBpdGVtLkNvZGUpIHsKICAgICAgICAgICAgICAgICAgZmxhZyA9IHRydWU7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBpZiAoIWZsYWcpIHsKICAgICAgICAgICAgICAgICAgZmxhZyA9ICEhaXRlbS5Jc19FbmFibGU7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICByZXR1cm4gZmxhZzsKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAvLyB0aGlzLmRlZmF1bHRPcHRpb25zID0gZGVlcENsb25lKHRoaXMub3B0aW9ucykKCiAgICAgICAgICAgICAgX3RoaXM2LmFyciA9IGFyciB8fCBbXTsKICAgICAgICAgICAgICBfdGhpczYubGlzdCA9IFtdOwogICAgICAgICAgICAgIGNvZGVzID0gW107CiAgICAgICAgICAgICAgaWYgKF90aGlzNi5pc0NvbSkgewogICAgICAgICAgICAgICAgb3JpZ2luID0gYXJyLm1hcChmdW5jdGlvbiAodikgewogICAgICAgICAgICAgICAgICByZXR1cm4gKCh2ID09PSBudWxsIHx8IHYgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHYuUGFydF9Vc2VkX1Byb2Nlc3MpIHx8ICcnKS5zcGxpdCgnLCcpOwogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICBjb2RlcyA9IF90aGlzNi5nZXRVbmlxdWUob3JpZ2luLmZsYXQoKSkuZmlsdGVyKGZ1bmN0aW9uICh2KSB7CiAgICAgICAgICAgICAgICAgIHJldHVybiAhIXY7CiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIGlmIChjb2Rlcy5sZW5ndGgpIHsKICAgICAgICAgICAgICAgICAgLy8g6Zu25p6E5Lu2CiAgICAgICAgICAgICAgICAgIGNoZWNrT3B0aW9uID0gY29kZXMuZmlsdGVyKGZ1bmN0aW9uIChjKSB7CiAgICAgICAgICAgICAgICAgICAgcmV0dXJuICEhX3RoaXM2Lm9wdGlvbnMuZmluZChmdW5jdGlvbiAoaykgewogICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGsuQ29kZSA9PT0gYzsKICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGNvZGVzLCBjaGVja09wdGlvbiwgX3RoaXM2Lm9wdGlvbnMubWFwKGZ1bmN0aW9uICh2KSB7CiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHYuQ29kZTsKICAgICAgICAgICAgICAgICAgfSkpOwogICAgICAgICAgICAgICAgICAvLyBpZiAoY2hlY2tPcHRpb24ubGVuZ3RoIDwgY29kZXMubGVuZ3RoKSB7CiAgICAgICAgICAgICAgICAgIC8vICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICAgIC8vICAgICBtZXNzYWdlOiAn5b2T5YmN5p6E5Lu255Sf5Lqn5omA5bGe6L2m6Ze05YaF5rKh5pyJ6K+l5p6E5Lu25omA5bGe6Zu25Lu26aKG55So5bel5bqP77yM6K+36Iez6L2m6Ze0566h55CG5YaF5YWz6IGU55u45YWz5bel5bqP54+t57uEJywKICAgICAgICAgICAgICAgICAgLy8gICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgICAgICAgICAgICAvLyAgIH0pCiAgICAgICAgICAgICAgICAgIC8vICAgcmV0dXJuCiAgICAgICAgICAgICAgICAgIC8vIH0KCiAgICAgICAgICAgICAgICAgIGNvZGVzLmZvckVhY2goZnVuY3Rpb24gKHZhbHVlLCBpZHgpIHsKICAgICAgICAgICAgICAgICAgICB2YXIgX3RoaXM2JHByb2Nlc3NMaXN0JHZhLCBfdGhpczYkb3B0aW9ucyRmaW5kLCBfdGhpczYkcHJvY2Vzc0xpc3QkdmEyOwogICAgICAgICAgICAgICAgICAgIHZhciBvYmogPSB7CiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogdmFsdWUsCiAgICAgICAgICAgICAgICAgICAgICBpc1BhcnQ6IHRydWUsCiAgICAgICAgICAgICAgICAgICAgICBrZXk6IHV1aWR2NCgpLAogICAgICAgICAgICAgICAgICAgICAgV29ya2luZ19UZWFtX0lkOiAoX3RoaXM2JHByb2Nlc3NMaXN0JHZhID0gX3RoaXM2LnByb2Nlc3NMaXN0W3ZhbHVlXSkgPT09IG51bGwgfHwgX3RoaXM2JHByb2Nlc3NMaXN0JHZhID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfdGhpczYkcHJvY2Vzc0xpc3QkdmEuV29ya2luZ19UZWFtX0lkLAogICAgICAgICAgICAgICAgICAgICAgVGVhbXM6ICgoX3RoaXM2JG9wdGlvbnMkZmluZCA9IF90aGlzNi5vcHRpb25zLmZpbmQoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0uQ29kZSA9PT0gdmFsdWU7CiAgICAgICAgICAgICAgICAgICAgICB9KSkgPT09IG51bGwgfHwgX3RoaXM2JG9wdGlvbnMkZmluZCA9PT0gdm9pZCAwID8gdm9pZCAwIDogX3RoaXM2JG9wdGlvbnMkZmluZC5UZWFtcykgfHwgW10sCiAgICAgICAgICAgICAgICAgICAgICBkYXRlOiAoX3RoaXM2JHByb2Nlc3NMaXN0JHZhMiA9IF90aGlzNi5wcm9jZXNzTGlzdFt2YWx1ZV0pID09PSBudWxsIHx8IF90aGlzNiRwcm9jZXNzTGlzdCR2YTIgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF90aGlzNiRwcm9jZXNzTGlzdCR2YTIuRmluaXNoX0RhdGUKICAgICAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgICAgICAgIGlmIChvYmouVGVhbXMubGVuZ3RoID09PSAxICYmICFvYmouV29ya2luZ19UZWFtX0lkKSB7CiAgICAgICAgICAgICAgICAgICAgICBvYmouV29ya2luZ19UZWFtX0lkID0gb2JqLlRlYW1zWzBdLklkOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICBfdGhpczYubGlzdC5wdXNoKG9iaik7CiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBpZiAodGVjaG5vbG9neUFyci5sZW5ndGgpIHsKICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCd0aGlzLm9wdGlvbnM2NjY2JywgX3RoaXM2Lm9wdGlvbnMpOwogICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ3RoaXMucHJvY2Vzc0xpc3QnLCBfdGhpczYucHJvY2Vzc0xpc3QpOwogICAgICAgICAgICAgICAgdGVjaEFyciA9IHRlY2hub2xvZ3lBcnIubWFwKGZ1bmN0aW9uICh2KSB7CiAgICAgICAgICAgICAgICAgIHZhciBfdGhpczYkcHJvY2Vzc0xpc3QkdiwgX3RoaXM2JG9wdGlvbnMkZmluZDIsIF90aGlzNiRwcm9jZXNzTGlzdCR2MjsKICAgICAgICAgICAgICAgICAgdmFyIG9iaiA9IHsKICAgICAgICAgICAgICAgICAgICBrZXk6IHV1aWR2NCgpLAogICAgICAgICAgICAgICAgICAgIHZhbHVlOiB2LAogICAgICAgICAgICAgICAgICAgIFdvcmtpbmdfVGVhbV9JZDogKF90aGlzNiRwcm9jZXNzTGlzdCR2ID0gX3RoaXM2LnByb2Nlc3NMaXN0W3ZdKSA9PT0gbnVsbCB8fCBfdGhpczYkcHJvY2Vzc0xpc3QkdiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX3RoaXM2JHByb2Nlc3NMaXN0JHYuV29ya2luZ19UZWFtX0lkLAogICAgICAgICAgICAgICAgICAgIFRlYW1zOiAoKF90aGlzNiRvcHRpb25zJGZpbmQyID0gX3RoaXM2Lm9wdGlvbnMuZmluZChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0uQ29kZSA9PT0gdjsKICAgICAgICAgICAgICAgICAgICB9KSkgPT09IG51bGwgfHwgX3RoaXM2JG9wdGlvbnMkZmluZDIgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF90aGlzNiRvcHRpb25zJGZpbmQyLlRlYW1zKSB8fCBbXSwKICAgICAgICAgICAgICAgICAgICBkYXRlOiAoX3RoaXM2JHByb2Nlc3NMaXN0JHYyID0gX3RoaXM2LnByb2Nlc3NMaXN0W3ZdKSA9PT0gbnVsbCB8fCBfdGhpczYkcHJvY2Vzc0xpc3QkdjIgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF90aGlzNiRwcm9jZXNzTGlzdCR2Mi5GaW5pc2hfRGF0ZQogICAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgICAgICBpZiAob2JqLlRlYW1zLmxlbmd0aCA9PT0gMSAmJiAhb2JqLldvcmtpbmdfVGVhbV9JZCkgewogICAgICAgICAgICAgICAgICAgIG9iai5Xb3JraW5nX1RlYW1fSWQgPSBvYmouVGVhbXNbMF0uSWQ7CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgcmV0dXJuIG9iajsKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ3RlY2hBcnInLCB0ZWNoQXJyKTsKICAgICAgICAgICAgICAgIHRlY2hBcnIuZm9yRWFjaChmdW5jdGlvbiAoZWxlbWVudCwgaWR4KSB7CiAgICAgICAgICAgICAgICAgIGlmICghY29kZXMuaW5jbHVkZXMoZWxlbWVudC52YWx1ZSkpIHsKICAgICAgICAgICAgICAgICAgICBfdGhpczYubGlzdC5wdXNoKGVsZW1lbnQpOwogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgaWYgKCFfdGhpczYubGlzdC5sZW5ndGgpIHsKICAgICAgICAgICAgICAgIF90aGlzNi5saXN0LnB1c2goewogICAgICAgICAgICAgICAgICB2YWx1ZTogJycsCiAgICAgICAgICAgICAgICAgIGtleTogdXVpZHY0KCksCiAgICAgICAgICAgICAgICAgIFdvcmtpbmdfVGVhbV9JZDogJycsCiAgICAgICAgICAgICAgICAgIFRlYW1zOiBbXSwKICAgICAgICAgICAgICAgICAgZGF0ZTogJycKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgaWYgKF90aGlzNi5pc05lc3QpIHsKICAgICAgICAgICAgICAgICAgeHVyID0gX3RoaXM2Lm9wdGlvbnMuZmlsdGVyKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0uSXNfTmVzdDsKICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgIGlmICh4dXIubGVuZ3RoID09PSAxKSB7CiAgICAgICAgICAgICAgICAgICAgX3RoaXM2Lmxpc3RbMF0udmFsdWUgPSB4dXJbMF0uQ29kZTsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBpbmRleE1hcCA9IHRlY2hub2xvZ3lBcnIucmVkdWNlKGZ1bmN0aW9uIChtYXAsIGl0ZW0sIGluZGV4KSB7CiAgICAgICAgICAgICAgICBtYXBbaXRlbV0gPSBpbmRleDsKICAgICAgICAgICAgICAgIHJldHVybiBtYXA7CiAgICAgICAgICAgICAgfSwge30pOwogICAgICAgICAgICAgIF90aGlzNi5saXN0LnNvcnQoZnVuY3Rpb24gKGl0ZW0xLCBpdGVtMikgewogICAgICAgICAgICAgICAgcmV0dXJuIGluZGV4TWFwW2l0ZW0xLnZhbHVlXSAtIGluZGV4TWFwW2l0ZW0yLnZhbHVlXTsKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICBfdGhpczYuc2VsZWN0Q2hhbmdlKCk7CiAgICAgICAgICAgIGNhc2UgMjE6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgZ2V0VW5pcXVlOiBmdW5jdGlvbiBnZXRVbmlxdWUoYXJyKSB7CiAgICAgIHJldHVybiB1bmlxdWVBcnIoYXJyKTsKICAgIH0sCiAgICBzdWJtaXQ6IGZ1bmN0aW9uIHN1Ym1pdCgpIHsKICAgICAgdmFyIF90aGlzNyA9IHRoaXM7CiAgICAgIHZhciBsaXN0ID0gdGhpcy5saXN0Lm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLnZhbHVlOwogICAgICB9KS5maWx0ZXIoZnVuY3Rpb24gKGspIHsKICAgICAgICByZXR1cm4gISFrOwogICAgICB9KTsKICAgICAgdmFyIGlzVHJ1ZSA9IHRoaXMuY2hlY2tDb2RlKGxpc3QpOwogICAgICBpZiAoIWlzVHJ1ZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgbWVzc2FnZTogJ+ebuOmCu+W3peW6j+S4jeiDveebuOWQjCcsCiAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICB9KTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgaWYgKCFsaXN0Lmxlbmd0aCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgbWVzc2FnZTogJ+W3peW6j+S4jeiDveWFqOS4uuepuicsCiAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICB9KTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgaWYgKHRoaXMuaXNOZXN0KSB7CiAgICAgICAgdmFyIHh1ciA9IHRoaXMub3B0aW9ucy5maWx0ZXIoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgIHJldHVybiBpdGVtLklzX05lc3Q7CiAgICAgICAgfSk7CiAgICAgICAgaWYgKHh1ci5sZW5ndGgpIHsKICAgICAgICAgIHZhciBoYXNOZXN0ID0geHVyLnNvbWUoZnVuY3Rpb24gKG9iaikgewogICAgICAgICAgICByZXR1cm4gbGlzdC5pbmNsdWRlcyhvYmouQ29kZSk7CiAgICAgICAgICB9KTsKICAgICAgICAgIGlmICghaGFzTmVzdCkgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICBtZXNzYWdlOiAn6K+36Iez5bCR6YCJ5oup5LiA5Liq5aWX5paZ5bel5bqP77yBJywKICAgICAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgICAgdGhpcy5idG5Mb2FkaW5nID0gdHJ1ZTsKICAgICAgdmFyIHN0ciA9IGxpc3Quam9pbignLycpOwogICAgICB0aGlzLmxpc3QuZm9yRWFjaChmdW5jdGlvbiAoZWxlbWVudCwgaWR4KSB7CiAgICAgICAgdmFyIGl0ZW0gPSBfdGhpczcub3B0aW9ucy5maW5kKGZ1bmN0aW9uICh2KSB7CiAgICAgICAgICByZXR1cm4gdi5Db2RlID09PSBlbGVtZW50LnZhbHVlOwogICAgICAgIH0pOwogICAgICAgIHZhciBvYmogPSB7fTsKICAgICAgICBpZiAoaXRlbSkgewogICAgICAgICAgdmFyIF90aGlzNyRmb3JtSW5saW5lOwogICAgICAgICAgb2JqID0gewogICAgICAgICAgICBTY2hkdWxpbmdfSWQ6IChfdGhpczckZm9ybUlubGluZSA9IF90aGlzNy5mb3JtSW5saW5lKSA9PT0gbnVsbCB8fCBfdGhpczckZm9ybUlubGluZSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX3RoaXM3JGZvcm1JbmxpbmUuU2NoZHVsaW5nX0NvZGUsCiAgICAgICAgICAgIFByb2Nlc3NfSWQ6IGl0ZW0uSWQsCiAgICAgICAgICAgIFByb2Nlc3NfQ29kZTogaXRlbS5Db2RlLAogICAgICAgICAgICBGaW5pc2hfRGF0ZTogZWxlbWVudC5kYXRlLAogICAgICAgICAgICBXb3JraW5nX1RlYW1fSWQ6IGVsZW1lbnQuV29ya2luZ19UZWFtX0lkCiAgICAgICAgICB9OwogICAgICAgIH0KICAgICAgICBfdGhpczcuJGVtaXQoJ3NldFByb2Nlc3NMaXN0JywgewogICAgICAgICAga2V5OiBlbGVtZW50LnZhbHVlLAogICAgICAgICAgdmFsdWU6IG9iagogICAgICAgIH0pOwogICAgICB9KTsKICAgICAgdGhpcy4kZW1pdCgnc2VuZFByb2Nlc3MnLCB7CiAgICAgICAgYXJyOiB0aGlzLmFyciwKICAgICAgICBzdHI6IHN0cgogICAgICB9KTsKICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2U7CiAgICAgIHRoaXMuaGFuZGxlQ2xvc2UoKTsKICAgIH0sCiAgICBoYW5kbGVDbG9zZTogZnVuY3Rpb24gaGFuZGxlQ2xvc2UoKSB7CiAgICAgIHRoaXMuJGVtaXQoJ2Nsb3NlJyk7CiAgICB9LAogICAgY2hlY2tDb2RlOiBmdW5jdGlvbiBjaGVja0NvZGUobGlzdCkgewogICAgICB2YXIgZmxhZyA9IHRydWU7CiAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgbGlzdC5sZW5ndGg7IGkrKykgewogICAgICAgIGlmIChpICE9PSBsaXN0Lmxlbmd0aCAtIDEgJiYgbGlzdFtpXSA9PT0gbGlzdFtpICsgMV0pIHsKICAgICAgICAgIGZsYWcgPSBmYWxzZTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIH0KICAgICAgfQogICAgICByZXR1cm4gZmxhZzsKICAgIH0sCiAgICBjaGFuZ2VEcmFnZ2FibGU6IGZ1bmN0aW9uIGNoYW5nZURyYWdnYWJsZSgpIHsKICAgICAgdmFyIF90aGlzOCA9IHRoaXM7CiAgICAgIHRoaXMubGlzdC5mb3JFYWNoKGZ1bmN0aW9uICh2KSB7CiAgICAgICAgX3RoaXM4LiRzZXQodiwgJ2RhdGUnLCAnJyk7CiAgICAgIH0pOwogICAgICB0aGlzLmluaXRQcm9jZXNzTGlzdCgpOwogICAgfQogIH0pCn07"}, {"version": 3, "names": ["GetProcessListBase", "Draggable", "deepClone", "uniqueArr", "mapActions", "v4", "uuidv4", "GetLibList", "components", "props", "pageType", "type", "String", "default", "undefined", "isNest", "Boolean", "processList", "Object", "data", "Working_Team_List", "list", "options", "gyList", "btnLoading", "pgLoading", "craftCode", "form", "computed", "isCom", "watch", "handler", "newVal", "_this$gyList$find", "_this", "workCode", "find", "v", "Code", "WorkCode", "newCode", "map", "value", "filter", "join", "deep", "methods", "_objectSpread", "getProcessOption", "workshopId", "_this2", "Promise", "resolve", "reject", "then", "res", "IsSucceed", "Data", "$set", "$message", "message", "Message", "finally", "_", "craftChange", "val", "_this3", "info", "plist", "split", "newList", "console", "log", "for<PERSON>ach", "listVal", "idx", "item", "Teams", "length", "Working_Team_Id", "Id", "push", "item2", "obj", "key", "date", "getCraftProcess", "_this4", "Type", "selectChange", "element", "_element$Teams", "arr", "i", "index", "disabled", "includes", "_this$processList$val", "_this$processList$val2", "Finish_Date", "dateChange", "_this$formInline", "Schduling_Id", "formInline", "Schduling_Code", "Process_Id", "Process_Code", "handleAdd", "handleDelete", "findIndex", "splice", "getWorkingTeam", "teams", "curItem", "_this5", "newTeams", "Workshop_Id", "every", "setData", "technologyStr", "_this6", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "technologyArr", "partUsedProcess", "codes", "origin", "checkOption", "techArr", "xur", "indexMap", "wrap", "_callee$", "_context", "prev", "next", "Part_Used_Process", "apply", "_toConsumableArray", "flag", "Part_Type_Used_Process", "Is_Enable", "getUnique", "flat", "c", "k", "_this6$processList$va", "_this6$options$find", "_this6$processList$va2", "isPart", "_this6$processList$v", "_this6$options$find2", "_this6$processList$v2", "Is_Nest", "reduce", "sort", "item1", "stop", "submit", "_this7", "isTrue", "checkCode", "hasNest", "some", "str", "_this7$formInline", "$emit", "handleClose", "changeDraggable", "_this8", "initProcessList"], "sources": ["src/views/PRO/plan-production/schedule-production-new/components/BatchProcessAdjust.vue"], "sourcesContent": ["<template>\r\n  <div v-loading=\"pgLoading\" class=\"cs-container\">\r\n    <el-form ref=\"form\" :model=\"form\" label-width=\"100px\">\r\n\r\n      <el-form-item label=\"工艺代码\">\r\n        <el-select v-model=\"craftCode\" filterable placeholder=\"下拉选择支持搜索\" clearable=\"\" @change=\"craftChange\">\r\n          <el-option\r\n            v-for=\"item in gyList\"\r\n            :key=\"item.Code\"\r\n            :label=\"item.Code\"\r\n            :value=\"item.Code\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-divider />\r\n      <draggable v-model=\"list\" handle=\".icon-drag\" @change=\"changeDraggable\">\r\n        <transition-group>\r\n          <el-row v-for=\"(element,index) in list\" :key=\"element.key\">\r\n            <el-col :span=\"1\"> <i class=\"iconfont icon-drag cs-drag\" /> </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item :label=\"`排产工序${index+1}`\">\r\n                <el-select :key=\"element.key\" v-model=\"element.value\" style=\"width:90%\" :disabled=\"element.isPart\" placeholder=\"请选择\" clearable @change=\"selectChange($event,element)\">\r\n                  <el-option\r\n                    v-for=\"item in options\"\r\n                    :key=\"item.Code\"\r\n                    :label=\"item.Name\"\r\n                    :disabled=\"item.disabled\"\r\n                    :value=\"item.Code\"\r\n                  >\r\n                    <div class=\"cs-option\">\r\n                      <span class=\"cs-label\">{{ item.Name }}</span>\r\n                      <span v-if=\"item.Is_Nest && isNest\" class=\"cs-tip\">(套)</span>\r\n                    </div>\r\n                  </el-option>\r\n                </el-select>\r\n\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col v-if=\"isCom\" :span=\"7\">\r\n              <el-form-item label=\"要求完成时间\">\r\n                <el-date-picker\r\n                  :key=\"element.key\"\r\n                  v-model=\"element.date\"\r\n                  type=\"date\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  style=\"width: 100%\"\r\n                  placeholder=\"选择日期\"\r\n                  @change=\"dateChange($event,element)\"\r\n                />\r\n              </el-form-item>\r\n\r\n            </el-col>\r\n            <el-col :span=\"7\">\r\n              <el-form-item label=\"班组\" label-width=\"60px\">\r\n                <el-select v-model=\"element.Working_Team_Id\" clearable placeholder=\"请选择\">\r\n                  <el-option v-for=\"item in getWorkingTeam(element.Teams,element)\" :key=\"item.Id\" :label=\"item.Name\" :value=\"item.Id\" />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"3\">\r\n              <span class=\"btn-x\">\r\n                <el-button v-if=\"index===0 && list.length<options.length\" type=\"primary\" icon=\"el-icon-plus\" circle @click=\"handleAdd\" />\r\n                <el-button v-if=\"index!==0&&!element.isPart\" type=\"danger\" icon=\"el-icon-delete\" circle @click=\"handleDelete(element)\" />\r\n              </span>\r\n            </el-col>\r\n\r\n          </el-row>\r\n        </transition-group>\r\n      </draggable>\r\n    </el-form>\r\n    <div class=\"dialog-footer\">\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n      <el-button v-if=\"list.length\" type=\"primary\" :loading=\"btnLoading\" @click=\"submit\">确 定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetProcessListBase } from '@/api/PRO/technology-lib'\r\nimport Draggable from 'vuedraggable'\r\nimport { deepClone, uniqueArr } from '@/utils'\r\nimport { mapActions } from 'vuex'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { GetLibList } from '@/api/PRO/technology-lib'\r\nexport default {\r\n  components: {\r\n    Draggable\r\n  },\r\n  props: {\r\n    pageType: {\r\n      type: String,\r\n      default: undefined\r\n    },\r\n    isNest: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    processList: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      Working_Team_List: [],\r\n      list: [],\r\n      options: [],\r\n      // defaultOptions: [],\r\n      gyList: [],\r\n      btnLoading: false,\r\n      pgLoading: false,\r\n      craftCode: '',\r\n      form: {}\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    }\r\n  },\r\n  watch: {\r\n    list: {\r\n      handler(newVal) {\r\n        if (!this.craftCode) return\r\n        const workCode = this.gyList.find(v => v.Code === this.craftCode)?.WorkCode\r\n        const newCode = newVal.map(v => v.value).filter(v => !!v).join('/')\r\n        if (workCode !== newCode) {\r\n          this.craftCode = ''\r\n        }\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions('schedule', ['initProcessList']),\r\n    getProcessOption(workshopId) {\r\n      return new Promise((resolve, reject) => {\r\n        this.pgLoading = true\r\n        GetProcessListBase({\r\n          workshopId: workshopId,\r\n          type: 1 // 0:全部，工艺类型1：构件工艺，2：零件工艺\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.options = res.Data.map(v => {\r\n              this.$set(v, 'disabled', false)\r\n              return v\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          resolve()\r\n        }).finally(_ => {\r\n          this.pgLoading = false\r\n        })\r\n      })\r\n    },\r\n    craftChange(val) {\r\n      this.craftCode = val\r\n      if (!val) {\r\n        // this.options = this.defaultOptions\r\n        return\r\n      }\r\n      const info = this.gyList.find(v => v.Code === val)\r\n      if (info) {\r\n        const plist = info.WorkCode.split('/')\r\n        // this.options = this.defaultOptions.filter(v => plist.includes(v.Code))\r\n        const newList = []\r\n        console.log('plist', plist)\r\n        plist.forEach((listVal, idx) => {\r\n          const item = this.list.find(v => v.value === listVal)\r\n          console.log('item', item)\r\n          if (item) {\r\n            if (item.Teams.length === 1 && !item.Working_Team_Id) {\r\n              item.Working_Team_Id = item.Teams[0].Id\r\n            }\r\n            newList.push(item)\r\n          } else {\r\n            const item2 = this.options.find(v => v.Code === listVal)\r\n            console.log('item2', item2)\r\n            if (item2) {\r\n              const obj = {\r\n                key: uuidv4(),\r\n                value: item2.Code,\r\n                Working_Team_Id: '',\r\n                Teams: item2.Teams,\r\n                date: ''\r\n              }\r\n              if (item2.Teams.length === 1 && !obj.Working_Team_Id) {\r\n                obj.Working_Team_Id = item2.Teams[0].Id\r\n              }\r\n              newList.push(obj)\r\n            }\r\n          }\r\n        })\r\n        this.list = newList\r\n      }\r\n    },\r\n    getCraftProcess() {\r\n      return new Promise((resolve, reject) => {\r\n        GetLibList({\r\n          Id: '',\r\n          Type: 1\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.gyList = (res.Data || [])\r\n            resolve()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    selectChange(val, element) {\r\n      const arr = this.list.map(i => i.value)\r\n      this.options.forEach((item, index) => {\r\n        item.disabled = arr.includes(item.Code)\r\n        if (item.Code === val) {\r\n          element.Teams = item.Teams\r\n        }\r\n      })\r\n\r\n      if (element) {\r\n        if (val) {\r\n          element.date = this.processList[val]?.Finish_Date\r\n          element.Working_Team_Id = this.processList[val]?.Working_Team_Id\r\n        } else {\r\n          element.Working_Team_Id = ''\r\n          element.Teams = []\r\n        }\r\n      }\r\n\r\n      if (!element?.Working_Team_Id && element?.Teams?.length === 1) {\r\n        element.Working_Team_Id = element.Teams[0].Id\r\n      }\r\n    },\r\n    dateChange(val, element) {\r\n      const item = this.options.find(v => v.Code === element.value)\r\n      console.log('item', item, this.list)\r\n      let obj = {}\r\n      if (item) {\r\n        obj = {\r\n          Schduling_Id: this.formInline?.Schduling_Code,\r\n          Process_Id: item.Id,\r\n          Process_Code: item.Code,\r\n          Finish_Date: val\r\n        }\r\n      }\r\n      // this.$emit('setProcessList', { key: element.value, value: obj })\r\n    },\r\n    handleAdd(item) {\r\n      const arr = this.list.map(v => v.value)\r\n      this.options.forEach(v => {\r\n        if (arr.includes(v.Code)) {\r\n          v.disabled = true\r\n        }\r\n      })\r\n      this.list.push({\r\n        key: uuidv4(),\r\n        value: '',\r\n        Working_Team_Id: '',\r\n        Teams: [],\r\n        date: ''\r\n      })\r\n    },\r\n    handleDelete(element) {\r\n      const idx = this.list.findIndex(v => v.value === element.value)\r\n      if (idx !== -1) {\r\n        this.list.splice(idx, 1)\r\n        this.selectChange()\r\n      }\r\n    },\r\n    getWorkingTeam(teams, curItem) {\r\n      const newTeams = teams.filter(v => {\r\n        if (this.workshopId) {\r\n          return v.Workshop_Id === this.workshopId\r\n        }\r\n        return true\r\n      })\r\n      if (!newTeams.length) {\r\n        curItem.Working_Team_Id = ''\r\n        return []\r\n      }\r\n      if (newTeams.every(v => v.Id !== curItem.Working_Team_Id)) {\r\n        curItem.Working_Team_Id = ''\r\n      }\r\n      return newTeams\r\n    },\r\n    async setData(arr, technologyStr) {\r\n      console.log('arr', arr, technologyStr)\r\n      await this.getCraftProcess()\r\n      let technologyArr = []\r\n      if (technologyStr) {\r\n        technologyArr = technologyStr.split('/')\r\n      }\r\n      const workshopId = arr[0].Workshop_Id\r\n      this.workshopId = workshopId\r\n\r\n      const partUsedProcess = []\r\n\r\n      arr.forEach(v => {\r\n        if (v.Part_Used_Process) {\r\n          const arr = v.Part_Used_Process.split(',')\r\n          partUsedProcess.push(...arr)\r\n        }\r\n      })\r\n      await this.getProcessOption(workshopId)\r\n\r\n      this.options = this.options.filter(item => {\r\n        let flag = false\r\n        if (technologyArr.length && technologyArr.includes(item.Code)) {\r\n          flag = true\r\n        }\r\n        if (partUsedProcess.length && partUsedProcess.includes(item.Code)) {\r\n          flag = true\r\n        }\r\n        if (item.Part_Type_Used_Process && item.Part_Type_Used_Process === item.Code) {\r\n          flag = true\r\n        }\r\n        if (!flag) {\r\n          flag = !!item.Is_Enable\r\n        }\r\n        return flag\r\n      })\r\n      // this.defaultOptions = deepClone(this.options)\r\n\r\n      this.arr = arr || []\r\n      this.list = []\r\n      let codes = []\r\n      if (this.isCom) {\r\n        const origin = arr.map(v => (v?.Part_Used_Process || '').split(','))\r\n        codes = this.getUnique(origin.flat()).filter(v => !!v)\r\n\r\n        if (codes.length) {\r\n          // 零构件\r\n          const checkOption = codes.filter(c => {\r\n            return !!this.options.find(k => k.Code === c)\r\n          })\r\n          console.log(codes, checkOption, this.options.map(v => v.Code))\r\n          // if (checkOption.length < codes.length) {\r\n          //   this.$message({\r\n          //     message: '当前构件生产所属车间内没有该构件所属零件领用工序，请至车间管理内关联相关工序班组',\r\n          //     type: 'warning'\r\n          //   })\r\n          //   return\r\n          // }\r\n\r\n          codes.forEach((value, idx) => {\r\n            const obj = {\r\n              value,\r\n              isPart: true,\r\n              key: uuidv4(),\r\n              Working_Team_Id: this.processList[value]?.Working_Team_Id,\r\n              Teams: this.options.find(item => item.Code === value)?.Teams || [],\r\n              date: this.processList[value]?.Finish_Date\r\n            }\r\n            if (obj.Teams.length === 1 && !obj.Working_Team_Id) {\r\n              obj.Working_Team_Id = obj.Teams[0].Id\r\n            }\r\n            this.list.push(obj)\r\n          })\r\n        }\r\n      }\r\n      if (technologyArr.length) {\r\n        console.log('this.options6666', this.options)\r\n        console.log('this.processList', this.processList)\r\n        const techArr = technologyArr.map(v => {\r\n          const obj = {\r\n            key: uuidv4(),\r\n            value: v,\r\n            Working_Team_Id: this.processList[v]?.Working_Team_Id,\r\n            Teams: this.options.find(item => item.Code === v)?.Teams || [],\r\n            date: this.processList[v]?.Finish_Date\r\n          }\r\n          if (obj.Teams.length === 1 && !obj.Working_Team_Id) {\r\n            obj.Working_Team_Id = obj.Teams[0].Id\r\n          }\r\n          return obj\r\n        })\r\n        console.log('techArr', techArr)\r\n        techArr.forEach((element, idx) => {\r\n          if (!codes.includes(element.value)) {\r\n            this.list.push(element)\r\n          }\r\n        })\r\n      }\r\n      if (!this.list.length) {\r\n        this.list.push({\r\n          value: '',\r\n          key: uuidv4(),\r\n          Working_Team_Id: '',\r\n          Teams: [],\r\n          date: ''\r\n        })\r\n        if (this.isNest) {\r\n          const xur = this.options.filter(item => item.Is_Nest)\r\n          if (xur.length === 1) {\r\n            this.list[0].value = xur[0].Code\r\n          }\r\n        }\r\n      }\r\n      const indexMap = technologyArr.reduce((map, item, index) => {\r\n        map[item] = index\r\n        return map\r\n      }, {})\r\n\r\n      this.list.sort((item1, item2) => {\r\n        return indexMap[item1.value] - indexMap[item2.value]\r\n      })\r\n\r\n      this.selectChange()\r\n    },\r\n    getUnique(arr) {\r\n      return uniqueArr(arr)\r\n    },\r\n    submit() {\r\n      const list = this.list.map(item => item.value).filter(k => !!k)\r\n      const isTrue = this.checkCode(list)\r\n      if (!isTrue) {\r\n        this.$message({\r\n          message: '相邻工序不能相同',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      if (!list.length) {\r\n        this.$message({\r\n          message: '工序不能全为空',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n\r\n      if (this.isNest) {\r\n        const xur = this.options.filter(item => item.Is_Nest)\r\n        if (xur.length) {\r\n          const hasNest = xur.some(obj => list.includes(obj.Code))\r\n          if (!hasNest) {\r\n            this.$message({\r\n              message: '请至少选择一个套料工序！',\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n        }\r\n      }\r\n\r\n      this.btnLoading = true\r\n      const str = list.join('/')\r\n      this.list.forEach((element, idx) => {\r\n        const item = this.options.find(v => v.Code === element.value)\r\n\r\n        let obj = {}\r\n        if (item) {\r\n          obj = {\r\n            Schduling_Id: this.formInline?.Schduling_Code,\r\n            Process_Id: item.Id,\r\n            Process_Code: item.Code,\r\n            Finish_Date: element.date,\r\n            Working_Team_Id: element.Working_Team_Id\r\n          }\r\n        }\r\n        this.$emit('setProcessList', { key: element.value, value: obj })\r\n      })\r\n\r\n      this.$emit('sendProcess', { arr: this.arr, str })\r\n      this.btnLoading = false\r\n      this.handleClose()\r\n    },\r\n    handleClose() {\r\n      this.$emit('close')\r\n    },\r\n    checkCode(list) {\r\n      let flag = true\r\n      for (let i = 0; i < list.length; i++) {\r\n        if (i !== list.length - 1 && list[i] === list[i + 1]) {\r\n          flag = false\r\n          break\r\n        }\r\n      }\r\n      return flag\r\n    },\r\n    changeDraggable() {\r\n      this.list.forEach(v => {\r\n        this.$set(v, 'date', '')\r\n      })\r\n      this.initProcessList()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.btn-x{\r\n  margin-left: 20px;\r\n}\r\n.dialog-footer{\r\n  text-align: right;\r\n  margin-top: 30px;\r\n}\r\n\r\n.cs-drag{\r\n  line-height: 32px;\r\n  cursor: move;\r\n}\r\n.cs-option{\r\n  display:flex;\r\n  justify-content: space-between;\r\n  .cs-label{\r\n\r\n  }\r\n  .cs-tip{\r\n    color: #409EFF;\r\n  }\r\n}\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgFA,SAAAA,kBAAA;AACA,OAAAC,SAAA;AACA,SAAAC,SAAA,EAAAC,SAAA;AACA,SAAAC,UAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AACA,SAAAC,UAAA;AACA;EACAC,UAAA;IACAP,SAAA,EAAAA;EACA;EACAQ,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,EAAAC;IACA;IACAC,MAAA;MACAJ,IAAA,EAAAK,OAAA;MACAH,OAAA;IACA;IACAI,WAAA;MACAN,IAAA,EAAAO,MAAA;MACAL,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,iBAAA;MACAC,IAAA;MACAC,OAAA;MACA;MACAC,MAAA;MACAC,UAAA;MACAC,SAAA;MACAC,SAAA;MACAC,IAAA;IACA;EACA;EACAC,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA,YAAAnB,QAAA;IACA;EACA;EACAoB,KAAA;IACAT,IAAA;MACAU,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAC,iBAAA;UAAAC,KAAA;QACA,UAAAR,SAAA;QACA,IAAAS,QAAA,IAAAF,iBAAA,QAAAV,MAAA,CAAAa,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,IAAA,KAAAJ,KAAA,CAAAR,SAAA;QAAA,gBAAAO,iBAAA,uBAAAA,iBAAA,CAAAM,QAAA;QACA,IAAAC,OAAA,GAAAR,MAAA,CAAAS,GAAA,WAAAJ,CAAA;UAAA,OAAAA,CAAA,CAAAK,KAAA;QAAA,GAAAC,MAAA,WAAAN,CAAA;UAAA,SAAAA,CAAA;QAAA,GAAAO,IAAA;QACA,IAAAT,QAAA,KAAAK,OAAA;UACA,KAAAd,SAAA;QACA;MACA;MACAmB,IAAA;IACA;EACA;EACAC,OAAA,EAAAC,aAAA,CAAAA,aAAA,KACA3C,UAAA;IACA4C,gBAAA,WAAAA,iBAAAC,UAAA;MAAA,IAAAC,MAAA;MACA,WAAAC,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACAH,MAAA,CAAAzB,SAAA;QACAzB,kBAAA;UACAiD,UAAA,EAAAA,UAAA;UACAtC,IAAA;QACA,GAAA2C,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAN,MAAA,CAAA5B,OAAA,GAAAiC,GAAA,CAAAE,IAAA,CAAAhB,GAAA,WAAAJ,CAAA;cACAa,MAAA,CAAAQ,IAAA,CAAArB,CAAA;cACA,OAAAA,CAAA;YACA;UACA;YACAa,MAAA,CAAAS,QAAA;cACAC,OAAA,EAAAL,GAAA,CAAAM,OAAA;cACAlD,IAAA;YACA;UACA;UACAyC,OAAA;QACA,GAAAU,OAAA,WAAAC,CAAA;UACAb,MAAA,CAAAzB,SAAA;QACA;MACA;IACA;IACAuC,WAAA,WAAAA,YAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAxC,SAAA,GAAAuC,GAAA;MACA,KAAAA,GAAA;QACA;QACA;MACA;MACA,IAAAE,IAAA,QAAA5C,MAAA,CAAAa,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,IAAA,KAAA2B,GAAA;MAAA;MACA,IAAAE,IAAA;QACA,IAAAC,KAAA,GAAAD,IAAA,CAAA5B,QAAA,CAAA8B,KAAA;QACA;QACA,IAAAC,OAAA;QACAC,OAAA,CAAAC,GAAA,UAAAJ,KAAA;QACAA,KAAA,CAAAK,OAAA,WAAAC,OAAA,EAAAC,GAAA;UACA,IAAAC,IAAA,GAAAV,MAAA,CAAA7C,IAAA,CAAAe,IAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAK,KAAA,KAAAgC,OAAA;UAAA;UACAH,OAAA,CAAAC,GAAA,SAAAI,IAAA;UACA,IAAAA,IAAA;YACA,IAAAA,IAAA,CAAAC,KAAA,CAAAC,MAAA,WAAAF,IAAA,CAAAG,eAAA;cACAH,IAAA,CAAAG,eAAA,GAAAH,IAAA,CAAAC,KAAA,IAAAG,EAAA;YACA;YACAV,OAAA,CAAAW,IAAA,CAAAL,IAAA;UACA;YACA,IAAAM,KAAA,GAAAhB,MAAA,CAAA5C,OAAA,CAAAc,IAAA,WAAAC,CAAA;cAAA,OAAAA,CAAA,CAAAC,IAAA,KAAAoC,OAAA;YAAA;YACAH,OAAA,CAAAC,GAAA,UAAAU,KAAA;YACA,IAAAA,KAAA;cACA,IAAAC,GAAA;gBACAC,GAAA,EAAA9E,MAAA;gBACAoC,KAAA,EAAAwC,KAAA,CAAA5C,IAAA;gBACAyC,eAAA;gBACAF,KAAA,EAAAK,KAAA,CAAAL,KAAA;gBACAQ,IAAA;cACA;cACA,IAAAH,KAAA,CAAAL,KAAA,CAAAC,MAAA,WAAAK,GAAA,CAAAJ,eAAA;gBACAI,GAAA,CAAAJ,eAAA,GAAAG,KAAA,CAAAL,KAAA,IAAAG,EAAA;cACA;cACAV,OAAA,CAAAW,IAAA,CAAAE,GAAA;YACA;UACA;QACA;QACA,KAAA9D,IAAA,GAAAiD,OAAA;MACA;IACA;IACAgB,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,WAAApC,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACA9C,UAAA;UACAyE,EAAA;UACAQ,IAAA;QACA,GAAAlC,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACA+B,MAAA,CAAAhE,MAAA,GAAAgC,GAAA,CAAAE,IAAA;YACAL,OAAA;UACA;YACAmC,MAAA,CAAA5B,QAAA;cACAC,OAAA,EAAAL,GAAA,CAAAM,OAAA;cACAlD,IAAA;YACA;YACA0C,MAAA;UACA;QACA;MACA;IACA;IACAoC,YAAA,WAAAA,aAAAxB,GAAA,EAAAyB,OAAA;MAAA,IAAAC,cAAA;MACA,IAAAC,GAAA,QAAAvE,IAAA,CAAAoB,GAAA,WAAAoD,CAAA;QAAA,OAAAA,CAAA,CAAAnD,KAAA;MAAA;MACA,KAAApB,OAAA,CAAAmD,OAAA,WAAAG,IAAA,EAAAkB,KAAA;QACAlB,IAAA,CAAAmB,QAAA,GAAAH,GAAA,CAAAI,QAAA,CAAApB,IAAA,CAAAtC,IAAA;QACA,IAAAsC,IAAA,CAAAtC,IAAA,KAAA2B,GAAA;UACAyB,OAAA,CAAAb,KAAA,GAAAD,IAAA,CAAAC,KAAA;QACA;MACA;MAEA,IAAAa,OAAA;QACA,IAAAzB,GAAA;UAAA,IAAAgC,qBAAA,EAAAC,sBAAA;UACAR,OAAA,CAAAL,IAAA,IAAAY,qBAAA,QAAAhF,WAAA,CAAAgD,GAAA,eAAAgC,qBAAA,uBAAAA,qBAAA,CAAAE,WAAA;UACAT,OAAA,CAAAX,eAAA,IAAAmB,sBAAA,QAAAjF,WAAA,CAAAgD,GAAA,eAAAiC,sBAAA,uBAAAA,sBAAA,CAAAnB,eAAA;QACA;UACAW,OAAA,CAAAX,eAAA;UACAW,OAAA,CAAAb,KAAA;QACA;MACA;MAEA,MAAAa,OAAA,aAAAA,OAAA,eAAAA,OAAA,CAAAX,eAAA,MAAAW,OAAA,aAAAA,OAAA,gBAAAC,cAAA,GAAAD,OAAA,CAAAb,KAAA,cAAAc,cAAA,uBAAAA,cAAA,CAAAb,MAAA;QACAY,OAAA,CAAAX,eAAA,GAAAW,OAAA,CAAAb,KAAA,IAAAG,EAAA;MACA;IACA;IACAoB,UAAA,WAAAA,WAAAnC,GAAA,EAAAyB,OAAA;MACA,IAAAd,IAAA,QAAAtD,OAAA,CAAAc,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,IAAA,KAAAoD,OAAA,CAAAhD,KAAA;MAAA;MACA6B,OAAA,CAAAC,GAAA,SAAAI,IAAA,OAAAvD,IAAA;MACA,IAAA8D,GAAA;MACA,IAAAP,IAAA;QAAA,IAAAyB,gBAAA;QACAlB,GAAA;UACAmB,YAAA,GAAAD,gBAAA,QAAAE,UAAA,cAAAF,gBAAA,uBAAAA,gBAAA,CAAAG,cAAA;UACAC,UAAA,EAAA7B,IAAA,CAAAI,EAAA;UACA0B,YAAA,EAAA9B,IAAA,CAAAtC,IAAA;UACA6D,WAAA,EAAAlC;QACA;MACA;MACA;IACA;IACA0C,SAAA,WAAAA,UAAA/B,IAAA;MACA,IAAAgB,GAAA,QAAAvE,IAAA,CAAAoB,GAAA,WAAAJ,CAAA;QAAA,OAAAA,CAAA,CAAAK,KAAA;MAAA;MACA,KAAApB,OAAA,CAAAmD,OAAA,WAAApC,CAAA;QACA,IAAAuD,GAAA,CAAAI,QAAA,CAAA3D,CAAA,CAAAC,IAAA;UACAD,CAAA,CAAA0D,QAAA;QACA;MACA;MACA,KAAA1E,IAAA,CAAA4D,IAAA;QACAG,GAAA,EAAA9E,MAAA;QACAoC,KAAA;QACAqC,eAAA;QACAF,KAAA;QACAQ,IAAA;MACA;IACA;IACAuB,YAAA,WAAAA,aAAAlB,OAAA;MACA,IAAAf,GAAA,QAAAtD,IAAA,CAAAwF,SAAA,WAAAxE,CAAA;QAAA,OAAAA,CAAA,CAAAK,KAAA,KAAAgD,OAAA,CAAAhD,KAAA;MAAA;MACA,IAAAiC,GAAA;QACA,KAAAtD,IAAA,CAAAyF,MAAA,CAAAnC,GAAA;QACA,KAAAc,YAAA;MACA;IACA;IACAsB,cAAA,WAAAA,eAAAC,KAAA,EAAAC,OAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,QAAA,GAAAH,KAAA,CAAArE,MAAA,WAAAN,CAAA;QACA,IAAA6E,MAAA,CAAAjE,UAAA;UACA,OAAAZ,CAAA,CAAA+E,WAAA,KAAAF,MAAA,CAAAjE,UAAA;QACA;QACA;MACA;MACA,KAAAkE,QAAA,CAAArC,MAAA;QACAmC,OAAA,CAAAlC,eAAA;QACA;MACA;MACA,IAAAoC,QAAA,CAAAE,KAAA,WAAAhF,CAAA;QAAA,OAAAA,CAAA,CAAA2C,EAAA,KAAAiC,OAAA,CAAAlC,eAAA;MAAA;QACAkC,OAAA,CAAAlC,eAAA;MACA;MACA,OAAAoC,QAAA;IACA;IACAG,OAAA,WAAAA,QAAA1B,GAAA,EAAA2B,aAAA;MAAA,IAAAC,MAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,aAAA,EAAA5E,UAAA,EAAA6E,eAAA,EAAAC,KAAA,EAAAC,MAAA,EAAAC,WAAA,EAAAC,OAAA,EAAAC,GAAA,EAAAC,QAAA;QAAA,OAAAV,mBAAA,GAAAW,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAlE,OAAA,CAAAC,GAAA,QAAAoB,GAAA,EAAA2B,aAAA;cAAAgB,QAAA,CAAAE,IAAA;cAAA,OACAjB,MAAA,CAAAlC,eAAA;YAAA;cACAuC,aAAA;cACA,IAAAN,aAAA;gBACAM,aAAA,GAAAN,aAAA,CAAAlD,KAAA;cACA;cACApB,UAAA,GAAA2C,GAAA,IAAAwB,WAAA;cACAI,MAAA,CAAAvE,UAAA,GAAAA,UAAA;cAEA6E,eAAA;cAEAlC,GAAA,CAAAnB,OAAA,WAAApC,CAAA;gBACA,IAAAA,CAAA,CAAAqG,iBAAA;kBACA,IAAA9C,IAAA,GAAAvD,CAAA,CAAAqG,iBAAA,CAAArE,KAAA;kBACAyD,eAAA,CAAA7C,IAAA,CAAA0D,KAAA,CAAAb,eAAA,EAAAc,kBAAA,CAAAhD,IAAA;gBACA;cACA;cAAA2C,QAAA,CAAAE,IAAA;cAAA,OACAjB,MAAA,CAAAxE,gBAAA,CAAAC,UAAA;YAAA;cAEAuE,MAAA,CAAAlG,OAAA,GAAAkG,MAAA,CAAAlG,OAAA,CAAAqB,MAAA,WAAAiC,IAAA;gBACA,IAAAiE,IAAA;gBACA,IAAAhB,aAAA,CAAA/C,MAAA,IAAA+C,aAAA,CAAA7B,QAAA,CAAApB,IAAA,CAAAtC,IAAA;kBACAuG,IAAA;gBACA;gBACA,IAAAf,eAAA,CAAAhD,MAAA,IAAAgD,eAAA,CAAA9B,QAAA,CAAApB,IAAA,CAAAtC,IAAA;kBACAuG,IAAA;gBACA;gBACA,IAAAjE,IAAA,CAAAkE,sBAAA,IAAAlE,IAAA,CAAAkE,sBAAA,KAAAlE,IAAA,CAAAtC,IAAA;kBACAuG,IAAA;gBACA;gBACA,KAAAA,IAAA;kBACAA,IAAA,KAAAjE,IAAA,CAAAmE,SAAA;gBACA;gBACA,OAAAF,IAAA;cACA;cACA;;cAEArB,MAAA,CAAA5B,GAAA,GAAAA,GAAA;cACA4B,MAAA,CAAAnG,IAAA;cACA0G,KAAA;cACA,IAAAP,MAAA,CAAA3F,KAAA;gBACAmG,MAAA,GAAApC,GAAA,CAAAnD,GAAA,WAAAJ,CAAA;kBAAA,SAAAA,CAAA,aAAAA,CAAA,uBAAAA,CAAA,CAAAqG,iBAAA,SAAArE,KAAA;gBAAA;gBACA0D,KAAA,GAAAP,MAAA,CAAAwB,SAAA,CAAAhB,MAAA,CAAAiB,IAAA,IAAAtG,MAAA,WAAAN,CAAA;kBAAA,SAAAA,CAAA;gBAAA;gBAEA,IAAA0F,KAAA,CAAAjD,MAAA;kBACA;kBACAmD,WAAA,GAAAF,KAAA,CAAApF,MAAA,WAAAuG,CAAA;oBACA,SAAA1B,MAAA,CAAAlG,OAAA,CAAAc,IAAA,WAAA+G,CAAA;sBAAA,OAAAA,CAAA,CAAA7G,IAAA,KAAA4G,CAAA;oBAAA;kBACA;kBACA3E,OAAA,CAAAC,GAAA,CAAAuD,KAAA,EAAAE,WAAA,EAAAT,MAAA,CAAAlG,OAAA,CAAAmB,GAAA,WAAAJ,CAAA;oBAAA,OAAAA,CAAA,CAAAC,IAAA;kBAAA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;;kBAEAyF,KAAA,CAAAtD,OAAA,WAAA/B,KAAA,EAAAiC,GAAA;oBAAA,IAAAyE,qBAAA,EAAAC,mBAAA,EAAAC,sBAAA;oBACA,IAAAnE,GAAA;sBACAzC,KAAA,EAAAA,KAAA;sBACA6G,MAAA;sBACAnE,GAAA,EAAA9E,MAAA;sBACAyE,eAAA,GAAAqE,qBAAA,GAAA5B,MAAA,CAAAvG,WAAA,CAAAyB,KAAA,eAAA0G,qBAAA,uBAAAA,qBAAA,CAAArE,eAAA;sBACAF,KAAA,IAAAwE,mBAAA,GAAA7B,MAAA,CAAAlG,OAAA,CAAAc,IAAA,WAAAwC,IAAA;wBAAA,OAAAA,IAAA,CAAAtC,IAAA,KAAAI,KAAA;sBAAA,gBAAA2G,mBAAA,uBAAAA,mBAAA,CAAAxE,KAAA;sBACAQ,IAAA,GAAAiE,sBAAA,GAAA9B,MAAA,CAAAvG,WAAA,CAAAyB,KAAA,eAAA4G,sBAAA,uBAAAA,sBAAA,CAAAnD;oBACA;oBACA,IAAAhB,GAAA,CAAAN,KAAA,CAAAC,MAAA,WAAAK,GAAA,CAAAJ,eAAA;sBACAI,GAAA,CAAAJ,eAAA,GAAAI,GAAA,CAAAN,KAAA,IAAAG,EAAA;oBACA;oBACAwC,MAAA,CAAAnG,IAAA,CAAA4D,IAAA,CAAAE,GAAA;kBACA;gBACA;cACA;cACA,IAAA0C,aAAA,CAAA/C,MAAA;gBACAP,OAAA,CAAAC,GAAA,qBAAAgD,MAAA,CAAAlG,OAAA;gBACAiD,OAAA,CAAAC,GAAA,qBAAAgD,MAAA,CAAAvG,WAAA;gBACAiH,OAAA,GAAAL,aAAA,CAAApF,GAAA,WAAAJ,CAAA;kBAAA,IAAAmH,oBAAA,EAAAC,oBAAA,EAAAC,qBAAA;kBACA,IAAAvE,GAAA;oBACAC,GAAA,EAAA9E,MAAA;oBACAoC,KAAA,EAAAL,CAAA;oBACA0C,eAAA,GAAAyE,oBAAA,GAAAhC,MAAA,CAAAvG,WAAA,CAAAoB,CAAA,eAAAmH,oBAAA,uBAAAA,oBAAA,CAAAzE,eAAA;oBACAF,KAAA,IAAA4E,oBAAA,GAAAjC,MAAA,CAAAlG,OAAA,CAAAc,IAAA,WAAAwC,IAAA;sBAAA,OAAAA,IAAA,CAAAtC,IAAA,KAAAD,CAAA;oBAAA,gBAAAoH,oBAAA,uBAAAA,oBAAA,CAAA5E,KAAA;oBACAQ,IAAA,GAAAqE,qBAAA,GAAAlC,MAAA,CAAAvG,WAAA,CAAAoB,CAAA,eAAAqH,qBAAA,uBAAAA,qBAAA,CAAAvD;kBACA;kBACA,IAAAhB,GAAA,CAAAN,KAAA,CAAAC,MAAA,WAAAK,GAAA,CAAAJ,eAAA;oBACAI,GAAA,CAAAJ,eAAA,GAAAI,GAAA,CAAAN,KAAA,IAAAG,EAAA;kBACA;kBACA,OAAAG,GAAA;gBACA;gBACAZ,OAAA,CAAAC,GAAA,YAAA0D,OAAA;gBACAA,OAAA,CAAAzD,OAAA,WAAAiB,OAAA,EAAAf,GAAA;kBACA,KAAAoD,KAAA,CAAA/B,QAAA,CAAAN,OAAA,CAAAhD,KAAA;oBACA8E,MAAA,CAAAnG,IAAA,CAAA4D,IAAA,CAAAS,OAAA;kBACA;gBACA;cACA;cACA,KAAA8B,MAAA,CAAAnG,IAAA,CAAAyD,MAAA;gBACA0C,MAAA,CAAAnG,IAAA,CAAA4D,IAAA;kBACAvC,KAAA;kBACA0C,GAAA,EAAA9E,MAAA;kBACAyE,eAAA;kBACAF,KAAA;kBACAQ,IAAA;gBACA;gBACA,IAAAmC,MAAA,CAAAzG,MAAA;kBACAoH,GAAA,GAAAX,MAAA,CAAAlG,OAAA,CAAAqB,MAAA,WAAAiC,IAAA;oBAAA,OAAAA,IAAA,CAAA+E,OAAA;kBAAA;kBACA,IAAAxB,GAAA,CAAArD,MAAA;oBACA0C,MAAA,CAAAnG,IAAA,IAAAqB,KAAA,GAAAyF,GAAA,IAAA7F,IAAA;kBACA;gBACA;cACA;cACA8F,QAAA,GAAAP,aAAA,CAAA+B,MAAA,WAAAnH,GAAA,EAAAmC,IAAA,EAAAkB,KAAA;gBACArD,GAAA,CAAAmC,IAAA,IAAAkB,KAAA;gBACA,OAAArD,GAAA;cACA;cAEA+E,MAAA,CAAAnG,IAAA,CAAAwI,IAAA,WAAAC,KAAA,EAAA5E,KAAA;gBACA,OAAAkD,QAAA,CAAA0B,KAAA,CAAApH,KAAA,IAAA0F,QAAA,CAAAlD,KAAA,CAAAxC,KAAA;cACA;cAEA8E,MAAA,CAAA/B,YAAA;YAAA;YAAA;cAAA,OAAA8C,QAAA,CAAAwB,IAAA;UAAA;QAAA,GAAAnC,OAAA;MAAA;IACA;IACAoB,SAAA,WAAAA,UAAApD,GAAA;MACA,OAAAzF,SAAA,CAAAyF,GAAA;IACA;IACAoE,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACA,IAAA5I,IAAA,QAAAA,IAAA,CAAAoB,GAAA,WAAAmC,IAAA;QAAA,OAAAA,IAAA,CAAAlC,KAAA;MAAA,GAAAC,MAAA,WAAAwG,CAAA;QAAA,SAAAA,CAAA;MAAA;MACA,IAAAe,MAAA,QAAAC,SAAA,CAAA9I,IAAA;MACA,KAAA6I,MAAA;QACA,KAAAvG,QAAA;UACAC,OAAA;UACAjD,IAAA;QACA;QACA;MACA;MACA,KAAAU,IAAA,CAAAyD,MAAA;QACA,KAAAnB,QAAA;UACAC,OAAA;UACAjD,IAAA;QACA;QACA;MACA;MAEA,SAAAI,MAAA;QACA,IAAAoH,GAAA,QAAA7G,OAAA,CAAAqB,MAAA,WAAAiC,IAAA;UAAA,OAAAA,IAAA,CAAA+E,OAAA;QAAA;QACA,IAAAxB,GAAA,CAAArD,MAAA;UACA,IAAAsF,OAAA,GAAAjC,GAAA,CAAAkC,IAAA,WAAAlF,GAAA;YAAA,OAAA9D,IAAA,CAAA2E,QAAA,CAAAb,GAAA,CAAA7C,IAAA;UAAA;UACA,KAAA8H,OAAA;YACA,KAAAzG,QAAA;cACAC,OAAA;cACAjD,IAAA;YACA;YACA;UACA;QACA;MACA;MAEA,KAAAa,UAAA;MACA,IAAA8I,GAAA,GAAAjJ,IAAA,CAAAuB,IAAA;MACA,KAAAvB,IAAA,CAAAoD,OAAA,WAAAiB,OAAA,EAAAf,GAAA;QACA,IAAAC,IAAA,GAAAqF,MAAA,CAAA3I,OAAA,CAAAc,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,IAAA,KAAAoD,OAAA,CAAAhD,KAAA;QAAA;QAEA,IAAAyC,GAAA;QACA,IAAAP,IAAA;UAAA,IAAA2F,iBAAA;UACApF,GAAA;YACAmB,YAAA,GAAAiE,iBAAA,GAAAN,MAAA,CAAA1D,UAAA,cAAAgE,iBAAA,uBAAAA,iBAAA,CAAA/D,cAAA;YACAC,UAAA,EAAA7B,IAAA,CAAAI,EAAA;YACA0B,YAAA,EAAA9B,IAAA,CAAAtC,IAAA;YACA6D,WAAA,EAAAT,OAAA,CAAAL,IAAA;YACAN,eAAA,EAAAW,OAAA,CAAAX;UACA;QACA;QACAkF,MAAA,CAAAO,KAAA;UAAApF,GAAA,EAAAM,OAAA,CAAAhD,KAAA;UAAAA,KAAA,EAAAyC;QAAA;MACA;MAEA,KAAAqF,KAAA;QAAA5E,GAAA,OAAAA,GAAA;QAAA0E,GAAA,EAAAA;MAAA;MACA,KAAA9I,UAAA;MACA,KAAAiJ,WAAA;IACA;IACAA,WAAA,WAAAA,YAAA;MACA,KAAAD,KAAA;IACA;IACAL,SAAA,WAAAA,UAAA9I,IAAA;MACA,IAAAwH,IAAA;MACA,SAAAhD,CAAA,MAAAA,CAAA,GAAAxE,IAAA,CAAAyD,MAAA,EAAAe,CAAA;QACA,IAAAA,CAAA,KAAAxE,IAAA,CAAAyD,MAAA,QAAAzD,IAAA,CAAAwE,CAAA,MAAAxE,IAAA,CAAAwE,CAAA;UACAgD,IAAA;UACA;QACA;MACA;MACA,OAAAA,IAAA;IACA;IACA6B,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAtJ,IAAA,CAAAoD,OAAA,WAAApC,CAAA;QACAsI,MAAA,CAAAjH,IAAA,CAAArB,CAAA;MACA;MACA,KAAAuI,eAAA;IACA;EAAA;AAEA", "ignoreList": []}]}
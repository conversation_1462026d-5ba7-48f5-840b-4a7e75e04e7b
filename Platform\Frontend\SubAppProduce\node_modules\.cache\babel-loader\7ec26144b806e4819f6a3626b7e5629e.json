{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new\\components\\BatchProcessAdjust.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new\\components\\BatchProcessAdjust.vue", "mtime": 1757572678816}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9yZWdlbmVyYXRvclJ1bnRpbWUgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9yZWdlbmVyYXRvclJ1bnRpbWUuanMiOwppbXBvcnQgX3RvQ29uc3VtYWJsZUFycmF5IGZyb20gIkQ6L3Byb2plY3QvcGxhdGZvcm1fZnJhbWV3b3JrX21hc3Rlci9wbGF0Zm9ybV9mcmFtZXdvcmsvUGxhdGZvcm0vRnJvbnRlbmQvU3ViQXBwUHJvZHVjZS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vdG9Db25zdW1hYmxlQXJyYXkuanMiOwppbXBvcnQgX2FzeW5jVG9HZW5lcmF0b3IgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9hc3luY1RvR2VuZXJhdG9yLmpzIjsKaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsdGVyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmluZC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZpbmQtaW5kZXguanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5mbGF0LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuaW5jbHVkZXMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5qb2luLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnJlZHVjZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnNvcnQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5zcGxpY2UuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS51bnNjb3BhYmxlcy5mbGF0LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5ldmVyeS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmZpbHRlci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmZpbmQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5mb3ItZWFjaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLm1hcC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLnJlZHVjZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLnNvbWUuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLmluY2x1ZGVzLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaC5qcyI7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCgppbXBvcnQgeyBHZXRQcm9jZXNzTGlzdEJhc2UgfSBmcm9tICdAL2FwaS9QUk8vdGVjaG5vbG9neS1saWInOwppbXBvcnQgRHJhZ2dhYmxlIGZyb20gJ3Z1ZWRyYWdnYWJsZSc7CmltcG9ydCB7IGRlZXBDbG9uZSwgdW5pcXVlQXJyIH0gZnJvbSAnQC91dGlscyc7CmltcG9ydCB7IG1hcEFjdGlvbnMgfSBmcm9tICd2dWV4JzsKaW1wb3J0IHsgdjQgYXMgdXVpZHY0IH0gZnJvbSAndXVpZCc7CmltcG9ydCB7IEdldExpYkxpc3QgfSBmcm9tICdAL2FwaS9QUk8vdGVjaG5vbG9neS1saWInOwpleHBvcnQgZGVmYXVsdCB7CiAgY29tcG9uZW50czogewogICAgRHJhZ2dhYmxlOiBEcmFnZ2FibGUKICB9LAogIHByb3BzOiB7CiAgICBwYWdlVHlwZTogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6IHVuZGVmaW5lZAogICAgfSwKICAgIGlzTmVzdDogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZQogICAgfSwKICAgIHByb2Nlc3NMaXN0OiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogZnVuY3Rpb24gX2RlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIHt9OwogICAgICB9CiAgICB9CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgV29ya2luZ19UZWFtX0xpc3Q6IFtdLAogICAgICBsaXN0OiBbXSwKICAgICAgb3B0aW9uczogW10sCiAgICAgIC8vIGRlZmF1bHRPcHRpb25zOiBbXSwKICAgICAgZ3lMaXN0OiBbXSwKICAgICAgYnRuTG9hZGluZzogZmFsc2UsCiAgICAgIHBnTG9hZGluZzogZmFsc2UsCiAgICAgIGNyYWZ0Q29kZTogJycsCiAgICAgIGZvcm06IHt9CiAgICB9OwogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGlzQ29tOiBmdW5jdGlvbiBpc0NvbSgpIHsKICAgICAgcmV0dXJuIHRoaXMucGFnZVR5cGUgPT09ICdjb20nOwogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgIGxpc3Q6IHsKICAgICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcihuZXdWYWwpIHsKICAgICAgICB2YXIgX3RoaXMkZ3lMaXN0JGZpbmQsCiAgICAgICAgICBfdGhpcyA9IHRoaXM7CiAgICAgICAgaWYgKCF0aGlzLmNyYWZ0Q29kZSkgcmV0dXJuOwogICAgICAgIHZhciB3b3JrQ29kZSA9IChfdGhpcyRneUxpc3QkZmluZCA9IHRoaXMuZ3lMaXN0LmZpbmQoZnVuY3Rpb24gKHYpIHsKICAgICAgICAgIHJldHVybiB2LkNvZGUgPT09IF90aGlzLmNyYWZ0Q29kZTsKICAgICAgICB9KSkgPT09IG51bGwgfHwgX3RoaXMkZ3lMaXN0JGZpbmQgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF90aGlzJGd5TGlzdCRmaW5kLldvcmtDb2RlOwogICAgICAgIHZhciBuZXdDb2RlID0gbmV3VmFsLm1hcChmdW5jdGlvbiAodikgewogICAgICAgICAgcmV0dXJuIHYudmFsdWU7CiAgICAgICAgfSkuZmlsdGVyKGZ1bmN0aW9uICh2KSB7CiAgICAgICAgICByZXR1cm4gISF2OwogICAgICAgIH0pLmpvaW4oJy8nKTsKICAgICAgICBpZiAod29ya0NvZGUgIT09IG5ld0NvZGUpIHsKICAgICAgICAgIHRoaXMuY3JhZnRDb2RlID0gJyc7CiAgICAgICAgfQogICAgICB9LAogICAgICBkZWVwOiB0cnVlCiAgICB9CiAgfSwKICBtZXRob2RzOiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIG1hcEFjdGlvbnMoJ3NjaGVkdWxlJywgWydpbml0UHJvY2Vzc0xpc3QnXSkpLCB7fSwgewogICAgZ2V0UHJvY2Vzc09wdGlvbjogZnVuY3Rpb24gZ2V0UHJvY2Vzc09wdGlvbih3b3Jrc2hvcElkKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICByZXR1cm4gbmV3IFByb21pc2UoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkgewogICAgICAgIF90aGlzMi5wZ0xvYWRpbmcgPSB0cnVlOwogICAgICAgIEdldFByb2Nlc3NMaXN0QmFzZSh7CiAgICAgICAgICB3b3Jrc2hvcElkOiB3b3Jrc2hvcElkLAogICAgICAgICAgdHlwZTogMSAvLyAwOuWFqOmDqO+8jOW3peiJuuexu+WeizHvvJrmnoTku7blt6XoibrvvIwy77ya6Zu25Lu25bel6Im6CiAgICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICBfdGhpczIub3B0aW9ucyA9IHJlcy5EYXRhLm1hcChmdW5jdGlvbiAodikgewogICAgICAgICAgICAgIF90aGlzMi4kc2V0KHYsICdkaXNhYmxlZCcsIGZhbHNlKTsKICAgICAgICAgICAgICByZXR1cm4gdjsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBfdGhpczIuJG1lc3NhZ2UoewogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgICByZXNvbHZlKCk7CiAgICAgICAgfSkuZmluYWxseShmdW5jdGlvbiAoXykgewogICAgICAgICAgX3RoaXMyLnBnTG9hZGluZyA9IGZhbHNlOwogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICBjcmFmdENoYW5nZTogZnVuY3Rpb24gY3JhZnRDaGFuZ2UodmFsKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICB0aGlzLmNyYWZ0Q29kZSA9IHZhbDsKICAgICAgaWYgKCF2YWwpIHsKICAgICAgICAvLyB0aGlzLm9wdGlvbnMgPSB0aGlzLmRlZmF1bHRPcHRpb25zCiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHZhciBpbmZvID0gdGhpcy5neUxpc3QuZmluZChmdW5jdGlvbiAodikgewogICAgICAgIHJldHVybiB2LkNvZGUgPT09IHZhbDsKICAgICAgfSk7CiAgICAgIGlmIChpbmZvKSB7CiAgICAgICAgdmFyIHBsaXN0ID0gaW5mby5Xb3JrQ29kZS5zcGxpdCgnLycpOwogICAgICAgIC8vIHRoaXMub3B0aW9ucyA9IHRoaXMuZGVmYXVsdE9wdGlvbnMuZmlsdGVyKHYgPT4gcGxpc3QuaW5jbHVkZXModi5Db2RlKSkKICAgICAgICB0aGlzLm9wdGlvbnMuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgaWYgKHBsaXN0LmluY2x1ZGVzKGl0ZW0uQ29kZSkpIHsKICAgICAgICAgICAgaXRlbS5kaXNhYmxlZCA9IHRydWU7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBpdGVtLmRpc2FibGVkID0gZmFsc2U7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgICAgdmFyIG5ld0xpc3QgPSBbXTsKICAgICAgICBjb25zb2xlLmxvZygncGxpc3QnLCBwbGlzdCk7CiAgICAgICAgcGxpc3QuZm9yRWFjaChmdW5jdGlvbiAobGlzdFZhbCwgaWR4KSB7CiAgICAgICAgICB2YXIgaXRlbSA9IF90aGlzMy5saXN0LmZpbmQoZnVuY3Rpb24gKHYpIHsKICAgICAgICAgICAgcmV0dXJuIHYudmFsdWUgPT09IGxpc3RWYWw7CiAgICAgICAgICB9KTsKICAgICAgICAgIGNvbnNvbGUubG9nKCdpdGVtJywgaXRlbSk7CiAgICAgICAgICBpZiAoaXRlbSkgewogICAgICAgICAgICBpZiAoaXRlbS5UZWFtcy5sZW5ndGggPT09IDEgJiYgIWl0ZW0uV29ya2luZ19UZWFtX0lkKSB7CiAgICAgICAgICAgICAgaXRlbS5Xb3JraW5nX1RlYW1fSWQgPSBpdGVtLlRlYW1zWzBdLklkOwogICAgICAgICAgICB9CiAgICAgICAgICAgIG5ld0xpc3QucHVzaChpdGVtKTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHZhciBpdGVtMiA9IF90aGlzMy5vcHRpb25zLmZpbmQoZnVuY3Rpb24gKHYpIHsKICAgICAgICAgICAgICByZXR1cm4gdi5Db2RlID09PSBsaXN0VmFsOwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgY29uc29sZS5sb2coJ2l0ZW0yJywgaXRlbTIpOwogICAgICAgICAgICBpZiAoaXRlbTIpIHsKICAgICAgICAgICAgICB2YXIgb2JqID0gewogICAgICAgICAgICAgICAga2V5OiB1dWlkdjQoKSwKICAgICAgICAgICAgICAgIHZhbHVlOiBpdGVtMi5Db2RlLAogICAgICAgICAgICAgICAgV29ya2luZ19UZWFtX0lkOiAnJywKICAgICAgICAgICAgICAgIFRlYW1zOiBpdGVtMi5UZWFtcywKICAgICAgICAgICAgICAgIGRhdGU6ICcnCiAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICBpZiAoaXRlbTIuVGVhbXMubGVuZ3RoID09PSAxICYmICFvYmouV29ya2luZ19UZWFtX0lkKSB7CiAgICAgICAgICAgICAgICBvYmouV29ya2luZ19UZWFtX0lkID0gaXRlbTIuVGVhbXNbMF0uSWQ7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIG5ld0xpc3QucHVzaChvYmopOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgICAgdGhpcy5saXN0ID0gbmV3TGlzdDsKICAgICAgfQogICAgfSwKICAgIGdldENyYWZ0UHJvY2VzczogZnVuY3Rpb24gZ2V0Q3JhZnRQcm9jZXNzKCkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uIChyZXNvbHZlLCByZWplY3QpIHsKICAgICAgICBHZXRMaWJMaXN0KHsKICAgICAgICAgIElkOiAnJywKICAgICAgICAgIFR5cGU6IDEKICAgICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICAgIF90aGlzNC5neUxpc3QgPSByZXMuRGF0YSB8fCBbXTsKICAgICAgICAgICAgcmVzb2x2ZSgpOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgX3RoaXM0LiRtZXNzYWdlKHsKICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwKICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICByZWplY3QoKTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgc2VsZWN0Q2hhbmdlOiBmdW5jdGlvbiBzZWxlY3RDaGFuZ2UodmFsLCBlbGVtZW50KSB7CiAgICAgIHZhciBfZWxlbWVudCRUZWFtczsKICAgICAgdmFyIGFyciA9IHRoaXMubGlzdC5tYXAoZnVuY3Rpb24gKGkpIHsKICAgICAgICByZXR1cm4gaS52YWx1ZTsKICAgICAgfSk7CiAgICAgIHRoaXMub3B0aW9ucy5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtLCBpbmRleCkgewogICAgICAgIGl0ZW0uZGlzYWJsZWQgPSBhcnIuaW5jbHVkZXMoaXRlbS5Db2RlKTsKICAgICAgICBpZiAoaXRlbS5Db2RlID09PSB2YWwpIHsKICAgICAgICAgIGVsZW1lbnQuVGVhbXMgPSBpdGVtLlRlYW1zOwogICAgICAgIH0KICAgICAgfSk7CiAgICAgIGlmIChlbGVtZW50KSB7CiAgICAgICAgaWYgKHZhbCkgewogICAgICAgICAgdmFyIF90aGlzJHByb2Nlc3NMaXN0JHZhbCwgX3RoaXMkcHJvY2Vzc0xpc3QkdmFsMjsKICAgICAgICAgIGVsZW1lbnQuZGF0ZSA9IChfdGhpcyRwcm9jZXNzTGlzdCR2YWwgPSB0aGlzLnByb2Nlc3NMaXN0W3ZhbF0pID09PSBudWxsIHx8IF90aGlzJHByb2Nlc3NMaXN0JHZhbCA9PT0gdm9pZCAwID8gdm9pZCAwIDogX3RoaXMkcHJvY2Vzc0xpc3QkdmFsLkZpbmlzaF9EYXRlOwogICAgICAgICAgZWxlbWVudC5Xb3JraW5nX1RlYW1fSWQgPSAoX3RoaXMkcHJvY2Vzc0xpc3QkdmFsMiA9IHRoaXMucHJvY2Vzc0xpc3RbdmFsXSkgPT09IG51bGwgfHwgX3RoaXMkcHJvY2Vzc0xpc3QkdmFsMiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX3RoaXMkcHJvY2Vzc0xpc3QkdmFsMi5Xb3JraW5nX1RlYW1fSWQ7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGVsZW1lbnQuV29ya2luZ19UZWFtX0lkID0gJyc7CiAgICAgICAgICBlbGVtZW50LlRlYW1zID0gW107CiAgICAgICAgfQogICAgICB9CiAgICAgIGlmICghKGVsZW1lbnQgIT09IG51bGwgJiYgZWxlbWVudCAhPT0gdm9pZCAwICYmIGVsZW1lbnQuV29ya2luZ19UZWFtX0lkKSAmJiAoZWxlbWVudCA9PT0gbnVsbCB8fCBlbGVtZW50ID09PSB2b2lkIDAgfHwgKF9lbGVtZW50JFRlYW1zID0gZWxlbWVudC5UZWFtcykgPT09IG51bGwgfHwgX2VsZW1lbnQkVGVhbXMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9lbGVtZW50JFRlYW1zLmxlbmd0aCkgPT09IDEpIHsKICAgICAgICBlbGVtZW50LldvcmtpbmdfVGVhbV9JZCA9IGVsZW1lbnQuVGVhbXNbMF0uSWQ7CiAgICAgIH0KICAgIH0sCiAgICBkYXRlQ2hhbmdlOiBmdW5jdGlvbiBkYXRlQ2hhbmdlKHZhbCwgZWxlbWVudCkgewogICAgICB2YXIgaXRlbSA9IHRoaXMub3B0aW9ucy5maW5kKGZ1bmN0aW9uICh2KSB7CiAgICAgICAgcmV0dXJuIHYuQ29kZSA9PT0gZWxlbWVudC52YWx1ZTsKICAgICAgfSk7CiAgICAgIGNvbnNvbGUubG9nKCdpdGVtJywgaXRlbSwgdGhpcy5saXN0KTsKICAgICAgdmFyIG9iaiA9IHt9OwogICAgICBpZiAoaXRlbSkgewogICAgICAgIHZhciBfdGhpcyRmb3JtSW5saW5lOwogICAgICAgIG9iaiA9IHsKICAgICAgICAgIFNjaGR1bGluZ19JZDogKF90aGlzJGZvcm1JbmxpbmUgPSB0aGlzLmZvcm1JbmxpbmUpID09PSBudWxsIHx8IF90aGlzJGZvcm1JbmxpbmUgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF90aGlzJGZvcm1JbmxpbmUuU2NoZHVsaW5nX0NvZGUsCiAgICAgICAgICBQcm9jZXNzX0lkOiBpdGVtLklkLAogICAgICAgICAgUHJvY2Vzc19Db2RlOiBpdGVtLkNvZGUsCiAgICAgICAgICBGaW5pc2hfRGF0ZTogdmFsCiAgICAgICAgfTsKICAgICAgfQogICAgICAvLyB0aGlzLiRlbWl0KCdzZXRQcm9jZXNzTGlzdCcsIHsga2V5OiBlbGVtZW50LnZhbHVlLCB2YWx1ZTogb2JqIH0pCiAgICB9LAogICAgaGFuZGxlQWRkOiBmdW5jdGlvbiBoYW5kbGVBZGQoaXRlbSkgewogICAgICB2YXIgYXJyID0gdGhpcy5saXN0Lm1hcChmdW5jdGlvbiAodikgewogICAgICAgIHJldHVybiB2LnZhbHVlOwogICAgICB9KTsKICAgICAgdGhpcy5vcHRpb25zLmZvckVhY2goZnVuY3Rpb24gKHYpIHsKICAgICAgICBpZiAoYXJyLmluY2x1ZGVzKHYuQ29kZSkpIHsKICAgICAgICAgIHYuZGlzYWJsZWQgPSB0cnVlOwogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHRoaXMubGlzdC5wdXNoKHsKICAgICAgICBrZXk6IHV1aWR2NCgpLAogICAgICAgIHZhbHVlOiAnJywKICAgICAgICBXb3JraW5nX1RlYW1fSWQ6ICcnLAogICAgICAgIFRlYW1zOiBbXSwKICAgICAgICBkYXRlOiAnJwogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVEZWxldGU6IGZ1bmN0aW9uIGhhbmRsZURlbGV0ZShlbGVtZW50KSB7CiAgICAgIHZhciBpZHggPSB0aGlzLmxpc3QuZmluZEluZGV4KGZ1bmN0aW9uICh2KSB7CiAgICAgICAgcmV0dXJuIHYudmFsdWUgPT09IGVsZW1lbnQudmFsdWU7CiAgICAgIH0pOwogICAgICBpZiAoaWR4ICE9PSAtMSkgewogICAgICAgIHRoaXMubGlzdC5zcGxpY2UoaWR4LCAxKTsKICAgICAgICB0aGlzLnNlbGVjdENoYW5nZSgpOwogICAgICB9CiAgICB9LAogICAgZ2V0V29ya2luZ1RlYW06IGZ1bmN0aW9uIGdldFdvcmtpbmdUZWFtKHRlYW1zLCBjdXJJdGVtKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB2YXIgbmV3VGVhbXMgPSB0ZWFtcy5maWx0ZXIoZnVuY3Rpb24gKHYpIHsKICAgICAgICBpZiAoX3RoaXM1LndvcmtzaG9wSWQpIHsKICAgICAgICAgIHJldHVybiB2LldvcmtzaG9wX0lkID09PSBfdGhpczUud29ya3Nob3BJZDsKICAgICAgICB9CiAgICAgICAgcmV0dXJuIHRydWU7CiAgICAgIH0pOwogICAgICBpZiAoIW5ld1RlYW1zLmxlbmd0aCkgewogICAgICAgIGN1ckl0ZW0uV29ya2luZ19UZWFtX0lkID0gJyc7CiAgICAgICAgcmV0dXJuIFtdOwogICAgICB9CiAgICAgIGlmIChuZXdUZWFtcy5ldmVyeShmdW5jdGlvbiAodikgewogICAgICAgIHJldHVybiB2LklkICE9PSBjdXJJdGVtLldvcmtpbmdfVGVhbV9JZDsKICAgICAgfSkpIHsKICAgICAgICBjdXJJdGVtLldvcmtpbmdfVGVhbV9JZCA9ICcnOwogICAgICB9CiAgICAgIHJldHVybiBuZXdUZWFtczsKICAgIH0sCiAgICBzZXREYXRhOiBmdW5jdGlvbiBzZXREYXRhKGFyciwgdGVjaG5vbG9neVN0cikgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlKCkgewogICAgICAgIHZhciB0ZWNobm9sb2d5QXJyLCB3b3Jrc2hvcElkLCBwYXJ0VXNlZFByb2Nlc3MsIGNvZGVzLCBvcmlnaW4sIGNoZWNrT3B0aW9uLCB0ZWNoQXJyLCB4dXIsIGluZGV4TWFwOwogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlJChfY29udGV4dCkgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQucHJldiA9IF9jb250ZXh0Lm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdhcnInLCBhcnIsIHRlY2hub2xvZ3lTdHIpOwogICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAzOwogICAgICAgICAgICAgIHJldHVybiBfdGhpczYuZ2V0Q3JhZnRQcm9jZXNzKCk7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICB0ZWNobm9sb2d5QXJyID0gW107CiAgICAgICAgICAgICAgaWYgKHRlY2hub2xvZ3lTdHIpIHsKICAgICAgICAgICAgICAgIHRlY2hub2xvZ3lBcnIgPSB0ZWNobm9sb2d5U3RyLnNwbGl0KCcvJyk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIHdvcmtzaG9wSWQgPSBhcnJbMF0uV29ya3Nob3BfSWQ7CiAgICAgICAgICAgICAgX3RoaXM2LndvcmtzaG9wSWQgPSB3b3Jrc2hvcElkOwogICAgICAgICAgICAgIHBhcnRVc2VkUHJvY2VzcyA9IFtdOwogICAgICAgICAgICAgIGFyci5mb3JFYWNoKGZ1bmN0aW9uICh2KSB7CiAgICAgICAgICAgICAgICBpZiAodi5QYXJ0X1VzZWRfUHJvY2VzcykgewogICAgICAgICAgICAgICAgICB2YXIgX2FyciA9IHYuUGFydF9Vc2VkX1Byb2Nlc3Muc3BsaXQoJywnKTsKICAgICAgICAgICAgICAgICAgcGFydFVzZWRQcm9jZXNzLnB1c2guYXBwbHkocGFydFVzZWRQcm9jZXNzLCBfdG9Db25zdW1hYmxlQXJyYXkoX2FycikpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAxMTsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXM2LmdldFByb2Nlc3NPcHRpb24od29ya3Nob3BJZCk7CiAgICAgICAgICAgIGNhc2UgMTE6CiAgICAgICAgICAgICAgX3RoaXM2Lm9wdGlvbnMgPSBfdGhpczYub3B0aW9ucy5maWx0ZXIoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgICAgIHZhciBmbGFnID0gZmFsc2U7CiAgICAgICAgICAgICAgICBpZiAodGVjaG5vbG9neUFyci5sZW5ndGggJiYgdGVjaG5vbG9neUFyci5pbmNsdWRlcyhpdGVtLkNvZGUpKSB7CiAgICAgICAgICAgICAgICAgIGZsYWcgPSB0cnVlOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgaWYgKHBhcnRVc2VkUHJvY2Vzcy5sZW5ndGggJiYgcGFydFVzZWRQcm9jZXNzLmluY2x1ZGVzKGl0ZW0uQ29kZSkpIHsKICAgICAgICAgICAgICAgICAgZmxhZyA9IHRydWU7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBpZiAoaXRlbS5QYXJ0X1R5cGVfVXNlZF9Qcm9jZXNzICYmIGl0ZW0uUGFydF9UeXBlX1VzZWRfUHJvY2VzcyA9PT0gaXRlbS5Db2RlKSB7CiAgICAgICAgICAgICAgICAgIGZsYWcgPSB0cnVlOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgaWYgKCFmbGFnKSB7CiAgICAgICAgICAgICAgICAgIGZsYWcgPSAhIWl0ZW0uSXNfRW5hYmxlOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgcmV0dXJuIGZsYWc7CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgLy8gdGhpcy5kZWZhdWx0T3B0aW9ucyA9IGRlZXBDbG9uZSh0aGlzLm9wdGlvbnMpCgogICAgICAgICAgICAgIF90aGlzNi5hcnIgPSBhcnIgfHwgW107CiAgICAgICAgICAgICAgX3RoaXM2Lmxpc3QgPSBbXTsKICAgICAgICAgICAgICBjb2RlcyA9IFtdOwogICAgICAgICAgICAgIGlmIChfdGhpczYuaXNDb20pIHsKICAgICAgICAgICAgICAgIG9yaWdpbiA9IGFyci5tYXAoZnVuY3Rpb24gKHYpIHsKICAgICAgICAgICAgICAgICAgcmV0dXJuICgodiA9PT0gbnVsbCB8fCB2ID09PSB2b2lkIDAgPyB2b2lkIDAgOiB2LlBhcnRfVXNlZF9Qcm9jZXNzKSB8fCAnJykuc3BsaXQoJywnKTsKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgY29kZXMgPSBfdGhpczYuZ2V0VW5pcXVlKG9yaWdpbi5mbGF0KCkpLmZpbHRlcihmdW5jdGlvbiAodikgewogICAgICAgICAgICAgICAgICByZXR1cm4gISF2OwogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICBpZiAoY29kZXMubGVuZ3RoKSB7CiAgICAgICAgICAgICAgICAgIC8vIOmbtuaehOS7tgogICAgICAgICAgICAgICAgICBjaGVja09wdGlvbiA9IGNvZGVzLmZpbHRlcihmdW5jdGlvbiAoYykgewogICAgICAgICAgICAgICAgICAgIHJldHVybiAhIV90aGlzNi5vcHRpb25zLmZpbmQoZnVuY3Rpb24gKGspIHsKICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBrLkNvZGUgPT09IGM7CiAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhjb2RlcywgY2hlY2tPcHRpb24sIF90aGlzNi5vcHRpb25zLm1hcChmdW5jdGlvbiAodikgewogICAgICAgICAgICAgICAgICAgIHJldHVybiB2LkNvZGU7CiAgICAgICAgICAgICAgICAgIH0pKTsKICAgICAgICAgICAgICAgICAgLy8gaWYgKGNoZWNrT3B0aW9uLmxlbmd0aCA8IGNvZGVzLmxlbmd0aCkgewogICAgICAgICAgICAgICAgICAvLyAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgICAvLyAgICAgbWVzc2FnZTogJ+W9k+WJjeaehOS7tueUn+S6p+aJgOWxnui9pumXtOWGheayoeacieivpeaehOS7tuaJgOWxnumbtuS7tumihueUqOW3peW6j++8jOivt+iHs+i9pumXtOeuoeeQhuWGheWFs+iBlOebuOWFs+W3peW6j+ePree7hCcsCiAgICAgICAgICAgICAgICAgIC8vICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICAgICAgICAgICAgLy8gICB9KQogICAgICAgICAgICAgICAgICAvLyAgIHJldHVybgogICAgICAgICAgICAgICAgICAvLyB9CgogICAgICAgICAgICAgICAgICBjb2Rlcy5mb3JFYWNoKGZ1bmN0aW9uICh2YWx1ZSwgaWR4KSB7CiAgICAgICAgICAgICAgICAgICAgdmFyIF90aGlzNiRwcm9jZXNzTGlzdCR2YSwgX3RoaXM2JG9wdGlvbnMkZmluZCwgX3RoaXM2JHByb2Nlc3NMaXN0JHZhMjsKICAgICAgICAgICAgICAgICAgICB2YXIgb2JqID0gewogICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IHZhbHVlLAogICAgICAgICAgICAgICAgICAgICAgaXNQYXJ0OiB0cnVlLAogICAgICAgICAgICAgICAgICAgICAga2V5OiB1dWlkdjQoKSwKICAgICAgICAgICAgICAgICAgICAgIFdvcmtpbmdfVGVhbV9JZDogKF90aGlzNiRwcm9jZXNzTGlzdCR2YSA9IF90aGlzNi5wcm9jZXNzTGlzdFt2YWx1ZV0pID09PSBudWxsIHx8IF90aGlzNiRwcm9jZXNzTGlzdCR2YSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX3RoaXM2JHByb2Nlc3NMaXN0JHZhLldvcmtpbmdfVGVhbV9JZCwKICAgICAgICAgICAgICAgICAgICAgIFRlYW1zOiAoKF90aGlzNiRvcHRpb25zJGZpbmQgPSBfdGhpczYub3B0aW9ucy5maW5kKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBpdGVtLkNvZGUgPT09IHZhbHVlOwogICAgICAgICAgICAgICAgICAgICAgfSkpID09PSBudWxsIHx8IF90aGlzNiRvcHRpb25zJGZpbmQgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF90aGlzNiRvcHRpb25zJGZpbmQuVGVhbXMpIHx8IFtdLAogICAgICAgICAgICAgICAgICAgICAgZGF0ZTogKF90aGlzNiRwcm9jZXNzTGlzdCR2YTIgPSBfdGhpczYucHJvY2Vzc0xpc3RbdmFsdWVdKSA9PT0gbnVsbCB8fCBfdGhpczYkcHJvY2Vzc0xpc3QkdmEyID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfdGhpczYkcHJvY2Vzc0xpc3QkdmEyLkZpbmlzaF9EYXRlCiAgICAgICAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICAgICAgICBpZiAob2JqLlRlYW1zLmxlbmd0aCA9PT0gMSAmJiAhb2JqLldvcmtpbmdfVGVhbV9JZCkgewogICAgICAgICAgICAgICAgICAgICAgb2JqLldvcmtpbmdfVGVhbV9JZCA9IG9iai5UZWFtc1swXS5JZDsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgX3RoaXM2Lmxpc3QucHVzaChvYmopOwogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgaWYgKHRlY2hub2xvZ3lBcnIubGVuZ3RoKSB7CiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygndGhpcy5vcHRpb25zNjY2NicsIF90aGlzNi5vcHRpb25zKTsKICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCd0aGlzLnByb2Nlc3NMaXN0JywgX3RoaXM2LnByb2Nlc3NMaXN0KTsKICAgICAgICAgICAgICAgIHRlY2hBcnIgPSB0ZWNobm9sb2d5QXJyLm1hcChmdW5jdGlvbiAodikgewogICAgICAgICAgICAgICAgICB2YXIgX3RoaXM2JHByb2Nlc3NMaXN0JHYsIF90aGlzNiRvcHRpb25zJGZpbmQyLCBfdGhpczYkcHJvY2Vzc0xpc3QkdjI7CiAgICAgICAgICAgICAgICAgIHZhciBvYmogPSB7CiAgICAgICAgICAgICAgICAgICAga2V5OiB1dWlkdjQoKSwKICAgICAgICAgICAgICAgICAgICB2YWx1ZTogdiwKICAgICAgICAgICAgICAgICAgICBXb3JraW5nX1RlYW1fSWQ6IChfdGhpczYkcHJvY2Vzc0xpc3QkdiA9IF90aGlzNi5wcm9jZXNzTGlzdFt2XSkgPT09IG51bGwgfHwgX3RoaXM2JHByb2Nlc3NMaXN0JHYgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF90aGlzNiRwcm9jZXNzTGlzdCR2LldvcmtpbmdfVGVhbV9JZCwKICAgICAgICAgICAgICAgICAgICBUZWFtczogKChfdGhpczYkb3B0aW9ucyRmaW5kMiA9IF90aGlzNi5vcHRpb25zLmZpbmQoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBpdGVtLkNvZGUgPT09IHY7CiAgICAgICAgICAgICAgICAgICAgfSkpID09PSBudWxsIHx8IF90aGlzNiRvcHRpb25zJGZpbmQyID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfdGhpczYkb3B0aW9ucyRmaW5kMi5UZWFtcykgfHwgW10sCiAgICAgICAgICAgICAgICAgICAgZGF0ZTogKF90aGlzNiRwcm9jZXNzTGlzdCR2MiA9IF90aGlzNi5wcm9jZXNzTGlzdFt2XSkgPT09IG51bGwgfHwgX3RoaXM2JHByb2Nlc3NMaXN0JHYyID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfdGhpczYkcHJvY2Vzc0xpc3QkdjIuRmluaXNoX0RhdGUKICAgICAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICAgICAgaWYgKG9iai5UZWFtcy5sZW5ndGggPT09IDEgJiYgIW9iai5Xb3JraW5nX1RlYW1fSWQpIHsKICAgICAgICAgICAgICAgICAgICBvYmouV29ya2luZ19UZWFtX0lkID0gb2JqLlRlYW1zWzBdLklkOwogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIHJldHVybiBvYmo7CiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCd0ZWNoQXJyJywgdGVjaEFycik7CiAgICAgICAgICAgICAgICB0ZWNoQXJyLmZvckVhY2goZnVuY3Rpb24gKGVsZW1lbnQsIGlkeCkgewogICAgICAgICAgICAgICAgICBpZiAoIWNvZGVzLmluY2x1ZGVzKGVsZW1lbnQudmFsdWUpKSB7CiAgICAgICAgICAgICAgICAgICAgX3RoaXM2Lmxpc3QucHVzaChlbGVtZW50KTsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGlmICghX3RoaXM2Lmxpc3QubGVuZ3RoKSB7CiAgICAgICAgICAgICAgICBfdGhpczYubGlzdC5wdXNoKHsKICAgICAgICAgICAgICAgICAgdmFsdWU6ICcnLAogICAgICAgICAgICAgICAgICBrZXk6IHV1aWR2NCgpLAogICAgICAgICAgICAgICAgICBXb3JraW5nX1RlYW1fSWQ6ICcnLAogICAgICAgICAgICAgICAgICBUZWFtczogW10sCiAgICAgICAgICAgICAgICAgIGRhdGU6ICcnCiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIGlmIChfdGhpczYuaXNOZXN0KSB7CiAgICAgICAgICAgICAgICAgIHh1ciA9IF90aGlzNi5vcHRpb25zLmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgICAgICAgIHJldHVybiBpdGVtLklzX05lc3Q7CiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICBpZiAoeHVyLmxlbmd0aCA9PT0gMSkgewogICAgICAgICAgICAgICAgICAgIF90aGlzNi5saXN0WzBdLnZhbHVlID0geHVyWzBdLkNvZGU7CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgaW5kZXhNYXAgPSB0ZWNobm9sb2d5QXJyLnJlZHVjZShmdW5jdGlvbiAobWFwLCBpdGVtLCBpbmRleCkgewogICAgICAgICAgICAgICAgbWFwW2l0ZW1dID0gaW5kZXg7CiAgICAgICAgICAgICAgICByZXR1cm4gbWFwOwogICAgICAgICAgICAgIH0sIHt9KTsKICAgICAgICAgICAgICBfdGhpczYubGlzdC5zb3J0KGZ1bmN0aW9uIChpdGVtMSwgaXRlbTIpIHsKICAgICAgICAgICAgICAgIHJldHVybiBpbmRleE1hcFtpdGVtMS52YWx1ZV0gLSBpbmRleE1hcFtpdGVtMi52YWx1ZV07CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgX3RoaXM2LnNlbGVjdENoYW5nZSgpOwogICAgICAgICAgICBjYXNlIDIxOgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIGdldFVuaXF1ZTogZnVuY3Rpb24gZ2V0VW5pcXVlKGFycikgewogICAgICByZXR1cm4gdW5pcXVlQXJyKGFycik7CiAgICB9LAogICAgc3VibWl0OiBmdW5jdGlvbiBzdWJtaXQoKSB7CiAgICAgIHZhciBfdGhpczcgPSB0aGlzOwogICAgICB2YXIgbGlzdCA9IHRoaXMubGlzdC5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICByZXR1cm4gaXRlbS52YWx1ZTsKICAgICAgfSkuZmlsdGVyKGZ1bmN0aW9uIChrKSB7CiAgICAgICAgcmV0dXJuICEhazsKICAgICAgfSk7CiAgICAgIHZhciBpc1RydWUgPSB0aGlzLmNoZWNrQ29kZShsaXN0KTsKICAgICAgaWYgKCFpc1RydWUpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIG1lc3NhZ2U6ICfnm7jpgrvlt6Xluo/kuI3og73nm7jlkIwnLAogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgfSk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGlmICghbGlzdC5sZW5ndGgpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIG1lc3NhZ2U6ICflt6Xluo/kuI3og73lhajkuLrnqbonLAogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgfSk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGlmICh0aGlzLmlzTmVzdCkgewogICAgICAgIHZhciB4dXIgPSB0aGlzLm9wdGlvbnMuZmlsdGVyKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICByZXR1cm4gaXRlbS5Jc19OZXN0OwogICAgICAgIH0pOwogICAgICAgIGlmICh4dXIubGVuZ3RoKSB7CiAgICAgICAgICB2YXIgaGFzTmVzdCA9IHh1ci5zb21lKGZ1bmN0aW9uIChvYmopIHsKICAgICAgICAgICAgcmV0dXJuIGxpc3QuaW5jbHVkZXMob2JqLkNvZGUpOwogICAgICAgICAgfSk7CiAgICAgICAgICBpZiAoIWhhc05lc3QpIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgbWVzc2FnZTogJ+ivt+iHs+WwkemAieaLqeS4gOS4quWll+aWmeW3peW6j++8gScsCiAgICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICByZXR1cm47CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICAgIHRoaXMuYnRuTG9hZGluZyA9IHRydWU7CiAgICAgIHZhciBzdHIgPSBsaXN0LmpvaW4oJy8nKTsKICAgICAgdGhpcy5saXN0LmZvckVhY2goZnVuY3Rpb24gKGVsZW1lbnQsIGlkeCkgewogICAgICAgIHZhciBpdGVtID0gX3RoaXM3Lm9wdGlvbnMuZmluZChmdW5jdGlvbiAodikgewogICAgICAgICAgcmV0dXJuIHYuQ29kZSA9PT0gZWxlbWVudC52YWx1ZTsKICAgICAgICB9KTsKICAgICAgICB2YXIgb2JqID0ge307CiAgICAgICAgaWYgKGl0ZW0pIHsKICAgICAgICAgIHZhciBfdGhpczckZm9ybUlubGluZTsKICAgICAgICAgIG9iaiA9IHsKICAgICAgICAgICAgU2NoZHVsaW5nX0lkOiAoX3RoaXM3JGZvcm1JbmxpbmUgPSBfdGhpczcuZm9ybUlubGluZSkgPT09IG51bGwgfHwgX3RoaXM3JGZvcm1JbmxpbmUgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF90aGlzNyRmb3JtSW5saW5lLlNjaGR1bGluZ19Db2RlLAogICAgICAgICAgICBQcm9jZXNzX0lkOiBpdGVtLklkLAogICAgICAgICAgICBQcm9jZXNzX0NvZGU6IGl0ZW0uQ29kZSwKICAgICAgICAgICAgRmluaXNoX0RhdGU6IGVsZW1lbnQuZGF0ZSwKICAgICAgICAgICAgV29ya2luZ19UZWFtX0lkOiBlbGVtZW50LldvcmtpbmdfVGVhbV9JZAogICAgICAgICAgfTsKICAgICAgICB9CiAgICAgICAgX3RoaXM3LiRlbWl0KCdzZXRQcm9jZXNzTGlzdCcsIHsKICAgICAgICAgIGtleTogZWxlbWVudC52YWx1ZSwKICAgICAgICAgIHZhbHVlOiBvYmoKICAgICAgICB9KTsKICAgICAgfSk7CiAgICAgIHRoaXMuJGVtaXQoJ3NlbmRQcm9jZXNzJywgewogICAgICAgIGFycjogdGhpcy5hcnIsCiAgICAgICAgc3RyOiBzdHIKICAgICAgfSk7CiAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlOwogICAgICB0aGlzLmhhbmRsZUNsb3NlKCk7CiAgICB9LAogICAgaGFuZGxlQ2xvc2U6IGZ1bmN0aW9uIGhhbmRsZUNsb3NlKCkgewogICAgICB0aGlzLiRlbWl0KCdjbG9zZScpOwogICAgfSwKICAgIGNoZWNrQ29kZTogZnVuY3Rpb24gY2hlY2tDb2RlKGxpc3QpIHsKICAgICAgdmFyIGZsYWcgPSB0cnVlOwogICAgICBmb3IgKHZhciBpID0gMDsgaSA8IGxpc3QubGVuZ3RoOyBpKyspIHsKICAgICAgICBpZiAoaSAhPT0gbGlzdC5sZW5ndGggLSAxICYmIGxpc3RbaV0gPT09IGxpc3RbaSArIDFdKSB7CiAgICAgICAgICBmbGFnID0gZmFsc2U7CiAgICAgICAgICBicmVhazsKICAgICAgICB9CiAgICAgIH0KICAgICAgcmV0dXJuIGZsYWc7CiAgICB9LAogICAgY2hhbmdlRHJhZ2dhYmxlOiBmdW5jdGlvbiBjaGFuZ2VEcmFnZ2FibGUoKSB7CiAgICAgIHZhciBfdGhpczggPSB0aGlzOwogICAgICB0aGlzLmxpc3QuZm9yRWFjaChmdW5jdGlvbiAodikgewogICAgICAgIF90aGlzOC4kc2V0KHYsICdkYXRlJywgJycpOwogICAgICB9KTsKICAgICAgdGhpcy5pbml0UHJvY2Vzc0xpc3QoKTsKICAgIH0KICB9KQp9Ow=="}, {"version": 3, "names": ["GetProcessListBase", "Draggable", "deepClone", "uniqueArr", "mapActions", "v4", "uuidv4", "GetLibList", "components", "props", "pageType", "type", "String", "default", "undefined", "isNest", "Boolean", "processList", "Object", "data", "Working_Team_List", "list", "options", "gyList", "btnLoading", "pgLoading", "craftCode", "form", "computed", "isCom", "watch", "handler", "newVal", "_this$gyList$find", "_this", "workCode", "find", "v", "Code", "WorkCode", "newCode", "map", "value", "filter", "join", "deep", "methods", "_objectSpread", "getProcessOption", "workshopId", "_this2", "Promise", "resolve", "reject", "then", "res", "IsSucceed", "Data", "$set", "$message", "message", "Message", "finally", "_", "craftChange", "val", "_this3", "info", "plist", "split", "for<PERSON>ach", "item", "includes", "disabled", "newList", "console", "log", "listVal", "idx", "Teams", "length", "Working_Team_Id", "Id", "push", "item2", "obj", "key", "date", "getCraftProcess", "_this4", "Type", "selectChange", "element", "_element$Teams", "arr", "i", "index", "_this$processList$val", "_this$processList$val2", "Finish_Date", "dateChange", "_this$formInline", "Schduling_Id", "formInline", "Schduling_Code", "Process_Id", "Process_Code", "handleAdd", "handleDelete", "findIndex", "splice", "getWorkingTeam", "teams", "curItem", "_this5", "newTeams", "Workshop_Id", "every", "setData", "technologyStr", "_this6", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "technologyArr", "partUsedProcess", "codes", "origin", "checkOption", "techArr", "xur", "indexMap", "wrap", "_callee$", "_context", "prev", "next", "Part_Used_Process", "apply", "_toConsumableArray", "flag", "Part_Type_Used_Process", "Is_Enable", "getUnique", "flat", "c", "k", "_this6$processList$va", "_this6$options$find", "_this6$processList$va2", "isPart", "_this6$processList$v", "_this6$options$find2", "_this6$processList$v2", "Is_Nest", "reduce", "sort", "item1", "stop", "submit", "_this7", "isTrue", "checkCode", "hasNest", "some", "str", "_this7$formInline", "$emit", "handleClose", "changeDraggable", "_this8", "initProcessList"], "sources": ["src/views/PRO/plan-production/schedule-production-new/components/BatchProcessAdjust.vue"], "sourcesContent": ["<template>\r\n  <div v-loading=\"pgLoading\" class=\"cs-container\">\r\n    <el-form ref=\"form\" :model=\"form\" label-width=\"100px\">\r\n\r\n      <el-form-item label=\"工艺代码\">\r\n        <el-select v-model=\"craftCode\" filterable placeholder=\"下拉选择支持搜索\" clearable=\"\" @change=\"craftChange\">\r\n          <el-option\r\n            v-for=\"item in gyList\"\r\n            :key=\"item.Code\"\r\n            :label=\"item.Code\"\r\n            :value=\"item.Code\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-divider />\r\n      <draggable v-model=\"list\" handle=\".icon-drag\" @change=\"changeDraggable\">\r\n        <transition-group>\r\n          <el-row v-for=\"(element,index) in list\" :key=\"element.key\">\r\n            <el-col :span=\"1\"> <i class=\"iconfont icon-drag cs-drag\" /> </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item :label=\"`排产工序${index+1}`\">\r\n                <el-select :key=\"element.key\" v-model=\"element.value\" style=\"width:90%\" :disabled=\"element.isPart\" placeholder=\"请选择\" clearable @change=\"selectChange($event,element)\">\r\n                  <el-option\r\n                    v-for=\"item in options\"\r\n                    :key=\"item.Code\"\r\n                    :label=\"item.Name\"\r\n                    :disabled=\"item.disabled\"\r\n                    :value=\"item.Code\"\r\n                  >\r\n                    <div class=\"cs-option\">\r\n                      <span class=\"cs-label\">{{ item.Name }}</span>\r\n                      <span v-if=\"item.Is_Nest && isNest\" class=\"cs-tip\">(套)</span>\r\n                    </div>\r\n                  </el-option>\r\n                </el-select>\r\n\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col v-if=\"isCom\" :span=\"7\">\r\n              <el-form-item label=\"要求完成时间\">\r\n                <el-date-picker\r\n                  :key=\"element.key\"\r\n                  v-model=\"element.date\"\r\n                  type=\"date\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  style=\"width: 100%\"\r\n                  placeholder=\"选择日期\"\r\n                  @change=\"dateChange($event,element)\"\r\n                />\r\n              </el-form-item>\r\n\r\n            </el-col>\r\n            <el-col :span=\"7\">\r\n              <el-form-item label=\"班组\" label-width=\"60px\">\r\n                <el-select v-model=\"element.Working_Team_Id\" clearable placeholder=\"请选择\">\r\n                  <el-option v-for=\"item in getWorkingTeam(element.Teams,element)\" :key=\"item.Id\" :label=\"item.Name\" :value=\"item.Id\" />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"3\">\r\n              <span class=\"btn-x\">\r\n                <el-button v-if=\"index===0 && list.length<options.length\" type=\"primary\" icon=\"el-icon-plus\" circle @click=\"handleAdd\" />\r\n                <el-button v-if=\"index!==0&&!element.isPart\" type=\"danger\" icon=\"el-icon-delete\" circle @click=\"handleDelete(element)\" />\r\n              </span>\r\n            </el-col>\r\n\r\n          </el-row>\r\n        </transition-group>\r\n      </draggable>\r\n    </el-form>\r\n    <div class=\"dialog-footer\">\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n      <el-button v-if=\"list.length\" type=\"primary\" :loading=\"btnLoading\" @click=\"submit\">确 定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetProcessListBase } from '@/api/PRO/technology-lib'\r\nimport Draggable from 'vuedraggable'\r\nimport { deepClone, uniqueArr } from '@/utils'\r\nimport { mapActions } from 'vuex'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { GetLibList } from '@/api/PRO/technology-lib'\r\nexport default {\r\n  components: {\r\n    Draggable\r\n  },\r\n  props: {\r\n    pageType: {\r\n      type: String,\r\n      default: undefined\r\n    },\r\n    isNest: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    processList: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      Working_Team_List: [],\r\n      list: [],\r\n      options: [],\r\n      // defaultOptions: [],\r\n      gyList: [],\r\n      btnLoading: false,\r\n      pgLoading: false,\r\n      craftCode: '',\r\n      form: {}\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    }\r\n  },\r\n  watch: {\r\n    list: {\r\n      handler(newVal) {\r\n        if (!this.craftCode) return\r\n        const workCode = this.gyList.find(v => v.Code === this.craftCode)?.WorkCode\r\n        const newCode = newVal.map(v => v.value).filter(v => !!v).join('/')\r\n        if (workCode !== newCode) {\r\n          this.craftCode = ''\r\n        }\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions('schedule', ['initProcessList']),\r\n    getProcessOption(workshopId) {\r\n      return new Promise((resolve, reject) => {\r\n        this.pgLoading = true\r\n        GetProcessListBase({\r\n          workshopId: workshopId,\r\n          type: 1 // 0:全部，工艺类型1：构件工艺，2：零件工艺\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.options = res.Data.map(v => {\r\n              this.$set(v, 'disabled', false)\r\n              return v\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          resolve()\r\n        }).finally(_ => {\r\n          this.pgLoading = false\r\n        })\r\n      })\r\n    },\r\n    craftChange(val) {\r\n      this.craftCode = val\r\n      if (!val) {\r\n        // this.options = this.defaultOptions\r\n        return\r\n      }\r\n      const info = this.gyList.find(v => v.Code === val)\r\n      if (info) {\r\n        const plist = info.WorkCode.split('/')\r\n        // this.options = this.defaultOptions.filter(v => plist.includes(v.Code))\r\n        this.options.forEach((item) => {\r\n          if (plist.includes(item.Code)) {\r\n            item.disabled = true\r\n          } else {\r\n            item.disabled = false\r\n          }\r\n        })\r\n        const newList = []\r\n        console.log('plist', plist)\r\n        plist.forEach((listVal, idx) => {\r\n          const item = this.list.find(v => v.value === listVal)\r\n          console.log('item', item)\r\n          if (item) {\r\n            if (item.Teams.length === 1 && !item.Working_Team_Id) {\r\n              item.Working_Team_Id = item.Teams[0].Id\r\n            }\r\n            newList.push(item)\r\n          } else {\r\n            const item2 = this.options.find(v => v.Code === listVal)\r\n            console.log('item2', item2)\r\n            if (item2) {\r\n              const obj = {\r\n                key: uuidv4(),\r\n                value: item2.Code,\r\n                Working_Team_Id: '',\r\n                Teams: item2.Teams,\r\n                date: ''\r\n              }\r\n              if (item2.Teams.length === 1 && !obj.Working_Team_Id) {\r\n                obj.Working_Team_Id = item2.Teams[0].Id\r\n              }\r\n              newList.push(obj)\r\n            }\r\n          }\r\n        })\r\n        this.list = newList\r\n      }\r\n    },\r\n    getCraftProcess() {\r\n      return new Promise((resolve, reject) => {\r\n        GetLibList({\r\n          Id: '',\r\n          Type: 1\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.gyList = (res.Data || [])\r\n            resolve()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    selectChange(val, element) {\r\n      const arr = this.list.map(i => i.value)\r\n      this.options.forEach((item, index) => {\r\n        item.disabled = arr.includes(item.Code)\r\n        if (item.Code === val) {\r\n          element.Teams = item.Teams\r\n        }\r\n      })\r\n\r\n      if (element) {\r\n        if (val) {\r\n          element.date = this.processList[val]?.Finish_Date\r\n          element.Working_Team_Id = this.processList[val]?.Working_Team_Id\r\n        } else {\r\n          element.Working_Team_Id = ''\r\n          element.Teams = []\r\n        }\r\n      }\r\n\r\n      if (!element?.Working_Team_Id && element?.Teams?.length === 1) {\r\n        element.Working_Team_Id = element.Teams[0].Id\r\n      }\r\n    },\r\n    dateChange(val, element) {\r\n      const item = this.options.find(v => v.Code === element.value)\r\n      console.log('item', item, this.list)\r\n      let obj = {}\r\n      if (item) {\r\n        obj = {\r\n          Schduling_Id: this.formInline?.Schduling_Code,\r\n          Process_Id: item.Id,\r\n          Process_Code: item.Code,\r\n          Finish_Date: val\r\n        }\r\n      }\r\n      // this.$emit('setProcessList', { key: element.value, value: obj })\r\n    },\r\n    handleAdd(item) {\r\n      const arr = this.list.map(v => v.value)\r\n      this.options.forEach(v => {\r\n        if (arr.includes(v.Code)) {\r\n          v.disabled = true\r\n        }\r\n      })\r\n      this.list.push({\r\n        key: uuidv4(),\r\n        value: '',\r\n        Working_Team_Id: '',\r\n        Teams: [],\r\n        date: ''\r\n      })\r\n    },\r\n    handleDelete(element) {\r\n      const idx = this.list.findIndex(v => v.value === element.value)\r\n      if (idx !== -1) {\r\n        this.list.splice(idx, 1)\r\n        this.selectChange()\r\n      }\r\n    },\r\n    getWorkingTeam(teams, curItem) {\r\n      const newTeams = teams.filter(v => {\r\n        if (this.workshopId) {\r\n          return v.Workshop_Id === this.workshopId\r\n        }\r\n        return true\r\n      })\r\n      if (!newTeams.length) {\r\n        curItem.Working_Team_Id = ''\r\n        return []\r\n      }\r\n      if (newTeams.every(v => v.Id !== curItem.Working_Team_Id)) {\r\n        curItem.Working_Team_Id = ''\r\n      }\r\n      return newTeams\r\n    },\r\n    async setData(arr, technologyStr) {\r\n      console.log('arr', arr, technologyStr)\r\n      await this.getCraftProcess()\r\n      let technologyArr = []\r\n      if (technologyStr) {\r\n        technologyArr = technologyStr.split('/')\r\n      }\r\n      const workshopId = arr[0].Workshop_Id\r\n      this.workshopId = workshopId\r\n\r\n      const partUsedProcess = []\r\n\r\n      arr.forEach(v => {\r\n        if (v.Part_Used_Process) {\r\n          const arr = v.Part_Used_Process.split(',')\r\n          partUsedProcess.push(...arr)\r\n        }\r\n      })\r\n      await this.getProcessOption(workshopId)\r\n\r\n      this.options = this.options.filter(item => {\r\n        let flag = false\r\n        if (technologyArr.length && technologyArr.includes(item.Code)) {\r\n          flag = true\r\n        }\r\n        if (partUsedProcess.length && partUsedProcess.includes(item.Code)) {\r\n          flag = true\r\n        }\r\n        if (item.Part_Type_Used_Process && item.Part_Type_Used_Process === item.Code) {\r\n          flag = true\r\n        }\r\n        if (!flag) {\r\n          flag = !!item.Is_Enable\r\n        }\r\n        return flag\r\n      })\r\n      // this.defaultOptions = deepClone(this.options)\r\n\r\n      this.arr = arr || []\r\n      this.list = []\r\n      let codes = []\r\n      if (this.isCom) {\r\n        const origin = arr.map(v => (v?.Part_Used_Process || '').split(','))\r\n        codes = this.getUnique(origin.flat()).filter(v => !!v)\r\n\r\n        if (codes.length) {\r\n          // 零构件\r\n          const checkOption = codes.filter(c => {\r\n            return !!this.options.find(k => k.Code === c)\r\n          })\r\n          console.log(codes, checkOption, this.options.map(v => v.Code))\r\n          // if (checkOption.length < codes.length) {\r\n          //   this.$message({\r\n          //     message: '当前构件生产所属车间内没有该构件所属零件领用工序，请至车间管理内关联相关工序班组',\r\n          //     type: 'warning'\r\n          //   })\r\n          //   return\r\n          // }\r\n\r\n          codes.forEach((value, idx) => {\r\n            const obj = {\r\n              value,\r\n              isPart: true,\r\n              key: uuidv4(),\r\n              Working_Team_Id: this.processList[value]?.Working_Team_Id,\r\n              Teams: this.options.find(item => item.Code === value)?.Teams || [],\r\n              date: this.processList[value]?.Finish_Date\r\n            }\r\n            if (obj.Teams.length === 1 && !obj.Working_Team_Id) {\r\n              obj.Working_Team_Id = obj.Teams[0].Id\r\n            }\r\n            this.list.push(obj)\r\n          })\r\n        }\r\n      }\r\n      if (technologyArr.length) {\r\n        console.log('this.options6666', this.options)\r\n        console.log('this.processList', this.processList)\r\n        const techArr = technologyArr.map(v => {\r\n          const obj = {\r\n            key: uuidv4(),\r\n            value: v,\r\n            Working_Team_Id: this.processList[v]?.Working_Team_Id,\r\n            Teams: this.options.find(item => item.Code === v)?.Teams || [],\r\n            date: this.processList[v]?.Finish_Date\r\n          }\r\n          if (obj.Teams.length === 1 && !obj.Working_Team_Id) {\r\n            obj.Working_Team_Id = obj.Teams[0].Id\r\n          }\r\n          return obj\r\n        })\r\n        console.log('techArr', techArr)\r\n        techArr.forEach((element, idx) => {\r\n          if (!codes.includes(element.value)) {\r\n            this.list.push(element)\r\n          }\r\n        })\r\n      }\r\n      if (!this.list.length) {\r\n        this.list.push({\r\n          value: '',\r\n          key: uuidv4(),\r\n          Working_Team_Id: '',\r\n          Teams: [],\r\n          date: ''\r\n        })\r\n        if (this.isNest) {\r\n          const xur = this.options.filter(item => item.Is_Nest)\r\n          if (xur.length === 1) {\r\n            this.list[0].value = xur[0].Code\r\n          }\r\n        }\r\n      }\r\n      const indexMap = technologyArr.reduce((map, item, index) => {\r\n        map[item] = index\r\n        return map\r\n      }, {})\r\n\r\n      this.list.sort((item1, item2) => {\r\n        return indexMap[item1.value] - indexMap[item2.value]\r\n      })\r\n\r\n      this.selectChange()\r\n    },\r\n    getUnique(arr) {\r\n      return uniqueArr(arr)\r\n    },\r\n    submit() {\r\n      const list = this.list.map(item => item.value).filter(k => !!k)\r\n      const isTrue = this.checkCode(list)\r\n      if (!isTrue) {\r\n        this.$message({\r\n          message: '相邻工序不能相同',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      if (!list.length) {\r\n        this.$message({\r\n          message: '工序不能全为空',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n\r\n      if (this.isNest) {\r\n        const xur = this.options.filter(item => item.Is_Nest)\r\n        if (xur.length) {\r\n          const hasNest = xur.some(obj => list.includes(obj.Code))\r\n          if (!hasNest) {\r\n            this.$message({\r\n              message: '请至少选择一个套料工序！',\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n        }\r\n      }\r\n\r\n      this.btnLoading = true\r\n      const str = list.join('/')\r\n      this.list.forEach((element, idx) => {\r\n        const item = this.options.find(v => v.Code === element.value)\r\n\r\n        let obj = {}\r\n        if (item) {\r\n          obj = {\r\n            Schduling_Id: this.formInline?.Schduling_Code,\r\n            Process_Id: item.Id,\r\n            Process_Code: item.Code,\r\n            Finish_Date: element.date,\r\n            Working_Team_Id: element.Working_Team_Id\r\n          }\r\n        }\r\n        this.$emit('setProcessList', { key: element.value, value: obj })\r\n      })\r\n\r\n      this.$emit('sendProcess', { arr: this.arr, str })\r\n      this.btnLoading = false\r\n      this.handleClose()\r\n    },\r\n    handleClose() {\r\n      this.$emit('close')\r\n    },\r\n    checkCode(list) {\r\n      let flag = true\r\n      for (let i = 0; i < list.length; i++) {\r\n        if (i !== list.length - 1 && list[i] === list[i + 1]) {\r\n          flag = false\r\n          break\r\n        }\r\n      }\r\n      return flag\r\n    },\r\n    changeDraggable() {\r\n      this.list.forEach(v => {\r\n        this.$set(v, 'date', '')\r\n      })\r\n      this.initProcessList()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.btn-x{\r\n  margin-left: 20px;\r\n}\r\n.dialog-footer{\r\n  text-align: right;\r\n  margin-top: 30px;\r\n}\r\n\r\n.cs-drag{\r\n  line-height: 32px;\r\n  cursor: move;\r\n}\r\n.cs-option{\r\n  display:flex;\r\n  justify-content: space-between;\r\n  .cs-label{\r\n\r\n  }\r\n  .cs-tip{\r\n    color: #409EFF;\r\n  }\r\n}\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgFA,SAAAA,kBAAA;AACA,OAAAC,SAAA;AACA,SAAAC,SAAA,EAAAC,SAAA;AACA,SAAAC,UAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AACA,SAAAC,UAAA;AACA;EACAC,UAAA;IACAP,SAAA,EAAAA;EACA;EACAQ,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,EAAAC;IACA;IACAC,MAAA;MACAJ,IAAA,EAAAK,OAAA;MACAH,OAAA;IACA;IACAI,WAAA;MACAN,IAAA,EAAAO,MAAA;MACAL,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,iBAAA;MACAC,IAAA;MACAC,OAAA;MACA;MACAC,MAAA;MACAC,UAAA;MACAC,SAAA;MACAC,SAAA;MACAC,IAAA;IACA;EACA;EACAC,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA,YAAAnB,QAAA;IACA;EACA;EACAoB,KAAA;IACAT,IAAA;MACAU,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAC,iBAAA;UAAAC,KAAA;QACA,UAAAR,SAAA;QACA,IAAAS,QAAA,IAAAF,iBAAA,QAAAV,MAAA,CAAAa,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,IAAA,KAAAJ,KAAA,CAAAR,SAAA;QAAA,gBAAAO,iBAAA,uBAAAA,iBAAA,CAAAM,QAAA;QACA,IAAAC,OAAA,GAAAR,MAAA,CAAAS,GAAA,WAAAJ,CAAA;UAAA,OAAAA,CAAA,CAAAK,KAAA;QAAA,GAAAC,MAAA,WAAAN,CAAA;UAAA,SAAAA,CAAA;QAAA,GAAAO,IAAA;QACA,IAAAT,QAAA,KAAAK,OAAA;UACA,KAAAd,SAAA;QACA;MACA;MACAmB,IAAA;IACA;EACA;EACAC,OAAA,EAAAC,aAAA,CAAAA,aAAA,KACA3C,UAAA;IACA4C,gBAAA,WAAAA,iBAAAC,UAAA;MAAA,IAAAC,MAAA;MACA,WAAAC,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACAH,MAAA,CAAAzB,SAAA;QACAzB,kBAAA;UACAiD,UAAA,EAAAA,UAAA;UACAtC,IAAA;QACA,GAAA2C,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAN,MAAA,CAAA5B,OAAA,GAAAiC,GAAA,CAAAE,IAAA,CAAAhB,GAAA,WAAAJ,CAAA;cACAa,MAAA,CAAAQ,IAAA,CAAArB,CAAA;cACA,OAAAA,CAAA;YACA;UACA;YACAa,MAAA,CAAAS,QAAA;cACAC,OAAA,EAAAL,GAAA,CAAAM,OAAA;cACAlD,IAAA;YACA;UACA;UACAyC,OAAA;QACA,GAAAU,OAAA,WAAAC,CAAA;UACAb,MAAA,CAAAzB,SAAA;QACA;MACA;IACA;IACAuC,WAAA,WAAAA,YAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAxC,SAAA,GAAAuC,GAAA;MACA,KAAAA,GAAA;QACA;QACA;MACA;MACA,IAAAE,IAAA,QAAA5C,MAAA,CAAAa,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,IAAA,KAAA2B,GAAA;MAAA;MACA,IAAAE,IAAA;QACA,IAAAC,KAAA,GAAAD,IAAA,CAAA5B,QAAA,CAAA8B,KAAA;QACA;QACA,KAAA/C,OAAA,CAAAgD,OAAA,WAAAC,IAAA;UACA,IAAAH,KAAA,CAAAI,QAAA,CAAAD,IAAA,CAAAjC,IAAA;YACAiC,IAAA,CAAAE,QAAA;UACA;YACAF,IAAA,CAAAE,QAAA;UACA;QACA;QACA,IAAAC,OAAA;QACAC,OAAA,CAAAC,GAAA,UAAAR,KAAA;QACAA,KAAA,CAAAE,OAAA,WAAAO,OAAA,EAAAC,GAAA;UACA,IAAAP,IAAA,GAAAL,MAAA,CAAA7C,IAAA,CAAAe,IAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAK,KAAA,KAAAmC,OAAA;UAAA;UACAF,OAAA,CAAAC,GAAA,SAAAL,IAAA;UACA,IAAAA,IAAA;YACA,IAAAA,IAAA,CAAAQ,KAAA,CAAAC,MAAA,WAAAT,IAAA,CAAAU,eAAA;cACAV,IAAA,CAAAU,eAAA,GAAAV,IAAA,CAAAQ,KAAA,IAAAG,EAAA;YACA;YACAR,OAAA,CAAAS,IAAA,CAAAZ,IAAA;UACA;YACA,IAAAa,KAAA,GAAAlB,MAAA,CAAA5C,OAAA,CAAAc,IAAA,WAAAC,CAAA;cAAA,OAAAA,CAAA,CAAAC,IAAA,KAAAuC,OAAA;YAAA;YACAF,OAAA,CAAAC,GAAA,UAAAQ,KAAA;YACA,IAAAA,KAAA;cACA,IAAAC,GAAA;gBACAC,GAAA,EAAAhF,MAAA;gBACAoC,KAAA,EAAA0C,KAAA,CAAA9C,IAAA;gBACA2C,eAAA;gBACAF,KAAA,EAAAK,KAAA,CAAAL,KAAA;gBACAQ,IAAA;cACA;cACA,IAAAH,KAAA,CAAAL,KAAA,CAAAC,MAAA,WAAAK,GAAA,CAAAJ,eAAA;gBACAI,GAAA,CAAAJ,eAAA,GAAAG,KAAA,CAAAL,KAAA,IAAAG,EAAA;cACA;cACAR,OAAA,CAAAS,IAAA,CAAAE,GAAA;YACA;UACA;QACA;QACA,KAAAhE,IAAA,GAAAqD,OAAA;MACA;IACA;IACAc,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,WAAAtC,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACA9C,UAAA;UACA2E,EAAA;UACAQ,IAAA;QACA,GAAApC,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAiC,MAAA,CAAAlE,MAAA,GAAAgC,GAAA,CAAAE,IAAA;YACAL,OAAA;UACA;YACAqC,MAAA,CAAA9B,QAAA;cACAC,OAAA,EAAAL,GAAA,CAAAM,OAAA;cACAlD,IAAA;YACA;YACA0C,MAAA;UACA;QACA;MACA;IACA;IACAsC,YAAA,WAAAA,aAAA1B,GAAA,EAAA2B,OAAA;MAAA,IAAAC,cAAA;MACA,IAAAC,GAAA,QAAAzE,IAAA,CAAAoB,GAAA,WAAAsD,CAAA;QAAA,OAAAA,CAAA,CAAArD,KAAA;MAAA;MACA,KAAApB,OAAA,CAAAgD,OAAA,WAAAC,IAAA,EAAAyB,KAAA;QACAzB,IAAA,CAAAE,QAAA,GAAAqB,GAAA,CAAAtB,QAAA,CAAAD,IAAA,CAAAjC,IAAA;QACA,IAAAiC,IAAA,CAAAjC,IAAA,KAAA2B,GAAA;UACA2B,OAAA,CAAAb,KAAA,GAAAR,IAAA,CAAAQ,KAAA;QACA;MACA;MAEA,IAAAa,OAAA;QACA,IAAA3B,GAAA;UAAA,IAAAgC,qBAAA,EAAAC,sBAAA;UACAN,OAAA,CAAAL,IAAA,IAAAU,qBAAA,QAAAhF,WAAA,CAAAgD,GAAA,eAAAgC,qBAAA,uBAAAA,qBAAA,CAAAE,WAAA;UACAP,OAAA,CAAAX,eAAA,IAAAiB,sBAAA,QAAAjF,WAAA,CAAAgD,GAAA,eAAAiC,sBAAA,uBAAAA,sBAAA,CAAAjB,eAAA;QACA;UACAW,OAAA,CAAAX,eAAA;UACAW,OAAA,CAAAb,KAAA;QACA;MACA;MAEA,MAAAa,OAAA,aAAAA,OAAA,eAAAA,OAAA,CAAAX,eAAA,MAAAW,OAAA,aAAAA,OAAA,gBAAAC,cAAA,GAAAD,OAAA,CAAAb,KAAA,cAAAc,cAAA,uBAAAA,cAAA,CAAAb,MAAA;QACAY,OAAA,CAAAX,eAAA,GAAAW,OAAA,CAAAb,KAAA,IAAAG,EAAA;MACA;IACA;IACAkB,UAAA,WAAAA,WAAAnC,GAAA,EAAA2B,OAAA;MACA,IAAArB,IAAA,QAAAjD,OAAA,CAAAc,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,IAAA,KAAAsD,OAAA,CAAAlD,KAAA;MAAA;MACAiC,OAAA,CAAAC,GAAA,SAAAL,IAAA,OAAAlD,IAAA;MACA,IAAAgE,GAAA;MACA,IAAAd,IAAA;QAAA,IAAA8B,gBAAA;QACAhB,GAAA;UACAiB,YAAA,GAAAD,gBAAA,QAAAE,UAAA,cAAAF,gBAAA,uBAAAA,gBAAA,CAAAG,cAAA;UACAC,UAAA,EAAAlC,IAAA,CAAAW,EAAA;UACAwB,YAAA,EAAAnC,IAAA,CAAAjC,IAAA;UACA6D,WAAA,EAAAlC;QACA;MACA;MACA;IACA;IACA0C,SAAA,WAAAA,UAAApC,IAAA;MACA,IAAAuB,GAAA,QAAAzE,IAAA,CAAAoB,GAAA,WAAAJ,CAAA;QAAA,OAAAA,CAAA,CAAAK,KAAA;MAAA;MACA,KAAApB,OAAA,CAAAgD,OAAA,WAAAjC,CAAA;QACA,IAAAyD,GAAA,CAAAtB,QAAA,CAAAnC,CAAA,CAAAC,IAAA;UACAD,CAAA,CAAAoC,QAAA;QACA;MACA;MACA,KAAApD,IAAA,CAAA8D,IAAA;QACAG,GAAA,EAAAhF,MAAA;QACAoC,KAAA;QACAuC,eAAA;QACAF,KAAA;QACAQ,IAAA;MACA;IACA;IACAqB,YAAA,WAAAA,aAAAhB,OAAA;MACA,IAAAd,GAAA,QAAAzD,IAAA,CAAAwF,SAAA,WAAAxE,CAAA;QAAA,OAAAA,CAAA,CAAAK,KAAA,KAAAkD,OAAA,CAAAlD,KAAA;MAAA;MACA,IAAAoC,GAAA;QACA,KAAAzD,IAAA,CAAAyF,MAAA,CAAAhC,GAAA;QACA,KAAAa,YAAA;MACA;IACA;IACAoB,cAAA,WAAAA,eAAAC,KAAA,EAAAC,OAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,QAAA,GAAAH,KAAA,CAAArE,MAAA,WAAAN,CAAA;QACA,IAAA6E,MAAA,CAAAjE,UAAA;UACA,OAAAZ,CAAA,CAAA+E,WAAA,KAAAF,MAAA,CAAAjE,UAAA;QACA;QACA;MACA;MACA,KAAAkE,QAAA,CAAAnC,MAAA;QACAiC,OAAA,CAAAhC,eAAA;QACA;MACA;MACA,IAAAkC,QAAA,CAAAE,KAAA,WAAAhF,CAAA;QAAA,OAAAA,CAAA,CAAA6C,EAAA,KAAA+B,OAAA,CAAAhC,eAAA;MAAA;QACAgC,OAAA,CAAAhC,eAAA;MACA;MACA,OAAAkC,QAAA;IACA;IACAG,OAAA,WAAAA,QAAAxB,GAAA,EAAAyB,aAAA;MAAA,IAAAC,MAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,aAAA,EAAA5E,UAAA,EAAA6E,eAAA,EAAAC,KAAA,EAAAC,MAAA,EAAAC,WAAA,EAAAC,OAAA,EAAAC,GAAA,EAAAC,QAAA;QAAA,OAAAV,mBAAA,GAAAW,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACA9D,OAAA,CAAAC,GAAA,QAAAkB,GAAA,EAAAyB,aAAA;cAAAgB,QAAA,CAAAE,IAAA;cAAA,OACAjB,MAAA,CAAAhC,eAAA;YAAA;cACAqC,aAAA;cACA,IAAAN,aAAA;gBACAM,aAAA,GAAAN,aAAA,CAAAlD,KAAA;cACA;cACApB,UAAA,GAAA6C,GAAA,IAAAsB,WAAA;cACAI,MAAA,CAAAvE,UAAA,GAAAA,UAAA;cAEA6E,eAAA;cAEAhC,GAAA,CAAAxB,OAAA,WAAAjC,CAAA;gBACA,IAAAA,CAAA,CAAAqG,iBAAA;kBACA,IAAA5C,IAAA,GAAAzD,CAAA,CAAAqG,iBAAA,CAAArE,KAAA;kBACAyD,eAAA,CAAA3C,IAAA,CAAAwD,KAAA,CAAAb,eAAA,EAAAc,kBAAA,CAAA9C,IAAA;gBACA;cACA;cAAAyC,QAAA,CAAAE,IAAA;cAAA,OACAjB,MAAA,CAAAxE,gBAAA,CAAAC,UAAA;YAAA;cAEAuE,MAAA,CAAAlG,OAAA,GAAAkG,MAAA,CAAAlG,OAAA,CAAAqB,MAAA,WAAA4B,IAAA;gBACA,IAAAsE,IAAA;gBACA,IAAAhB,aAAA,CAAA7C,MAAA,IAAA6C,aAAA,CAAArD,QAAA,CAAAD,IAAA,CAAAjC,IAAA;kBACAuG,IAAA;gBACA;gBACA,IAAAf,eAAA,CAAA9C,MAAA,IAAA8C,eAAA,CAAAtD,QAAA,CAAAD,IAAA,CAAAjC,IAAA;kBACAuG,IAAA;gBACA;gBACA,IAAAtE,IAAA,CAAAuE,sBAAA,IAAAvE,IAAA,CAAAuE,sBAAA,KAAAvE,IAAA,CAAAjC,IAAA;kBACAuG,IAAA;gBACA;gBACA,KAAAA,IAAA;kBACAA,IAAA,KAAAtE,IAAA,CAAAwE,SAAA;gBACA;gBACA,OAAAF,IAAA;cACA;cACA;;cAEArB,MAAA,CAAA1B,GAAA,GAAAA,GAAA;cACA0B,MAAA,CAAAnG,IAAA;cACA0G,KAAA;cACA,IAAAP,MAAA,CAAA3F,KAAA;gBACAmG,MAAA,GAAAlC,GAAA,CAAArD,GAAA,WAAAJ,CAAA;kBAAA,SAAAA,CAAA,aAAAA,CAAA,uBAAAA,CAAA,CAAAqG,iBAAA,SAAArE,KAAA;gBAAA;gBACA0D,KAAA,GAAAP,MAAA,CAAAwB,SAAA,CAAAhB,MAAA,CAAAiB,IAAA,IAAAtG,MAAA,WAAAN,CAAA;kBAAA,SAAAA,CAAA;gBAAA;gBAEA,IAAA0F,KAAA,CAAA/C,MAAA;kBACA;kBACAiD,WAAA,GAAAF,KAAA,CAAApF,MAAA,WAAAuG,CAAA;oBACA,SAAA1B,MAAA,CAAAlG,OAAA,CAAAc,IAAA,WAAA+G,CAAA;sBAAA,OAAAA,CAAA,CAAA7G,IAAA,KAAA4G,CAAA;oBAAA;kBACA;kBACAvE,OAAA,CAAAC,GAAA,CAAAmD,KAAA,EAAAE,WAAA,EAAAT,MAAA,CAAAlG,OAAA,CAAAmB,GAAA,WAAAJ,CAAA;oBAAA,OAAAA,CAAA,CAAAC,IAAA;kBAAA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;;kBAEAyF,KAAA,CAAAzD,OAAA,WAAA5B,KAAA,EAAAoC,GAAA;oBAAA,IAAAsE,qBAAA,EAAAC,mBAAA,EAAAC,sBAAA;oBACA,IAAAjE,GAAA;sBACA3C,KAAA,EAAAA,KAAA;sBACA6G,MAAA;sBACAjE,GAAA,EAAAhF,MAAA;sBACA2E,eAAA,GAAAmE,qBAAA,GAAA5B,MAAA,CAAAvG,WAAA,CAAAyB,KAAA,eAAA0G,qBAAA,uBAAAA,qBAAA,CAAAnE,eAAA;sBACAF,KAAA,IAAAsE,mBAAA,GAAA7B,MAAA,CAAAlG,OAAA,CAAAc,IAAA,WAAAmC,IAAA;wBAAA,OAAAA,IAAA,CAAAjC,IAAA,KAAAI,KAAA;sBAAA,gBAAA2G,mBAAA,uBAAAA,mBAAA,CAAAtE,KAAA;sBACAQ,IAAA,GAAA+D,sBAAA,GAAA9B,MAAA,CAAAvG,WAAA,CAAAyB,KAAA,eAAA4G,sBAAA,uBAAAA,sBAAA,CAAAnD;oBACA;oBACA,IAAAd,GAAA,CAAAN,KAAA,CAAAC,MAAA,WAAAK,GAAA,CAAAJ,eAAA;sBACAI,GAAA,CAAAJ,eAAA,GAAAI,GAAA,CAAAN,KAAA,IAAAG,EAAA;oBACA;oBACAsC,MAAA,CAAAnG,IAAA,CAAA8D,IAAA,CAAAE,GAAA;kBACA;gBACA;cACA;cACA,IAAAwC,aAAA,CAAA7C,MAAA;gBACAL,OAAA,CAAAC,GAAA,qBAAA4C,MAAA,CAAAlG,OAAA;gBACAqD,OAAA,CAAAC,GAAA,qBAAA4C,MAAA,CAAAvG,WAAA;gBACAiH,OAAA,GAAAL,aAAA,CAAApF,GAAA,WAAAJ,CAAA;kBAAA,IAAAmH,oBAAA,EAAAC,oBAAA,EAAAC,qBAAA;kBACA,IAAArE,GAAA;oBACAC,GAAA,EAAAhF,MAAA;oBACAoC,KAAA,EAAAL,CAAA;oBACA4C,eAAA,GAAAuE,oBAAA,GAAAhC,MAAA,CAAAvG,WAAA,CAAAoB,CAAA,eAAAmH,oBAAA,uBAAAA,oBAAA,CAAAvE,eAAA;oBACAF,KAAA,IAAA0E,oBAAA,GAAAjC,MAAA,CAAAlG,OAAA,CAAAc,IAAA,WAAAmC,IAAA;sBAAA,OAAAA,IAAA,CAAAjC,IAAA,KAAAD,CAAA;oBAAA,gBAAAoH,oBAAA,uBAAAA,oBAAA,CAAA1E,KAAA;oBACAQ,IAAA,GAAAmE,qBAAA,GAAAlC,MAAA,CAAAvG,WAAA,CAAAoB,CAAA,eAAAqH,qBAAA,uBAAAA,qBAAA,CAAAvD;kBACA;kBACA,IAAAd,GAAA,CAAAN,KAAA,CAAAC,MAAA,WAAAK,GAAA,CAAAJ,eAAA;oBACAI,GAAA,CAAAJ,eAAA,GAAAI,GAAA,CAAAN,KAAA,IAAAG,EAAA;kBACA;kBACA,OAAAG,GAAA;gBACA;gBACAV,OAAA,CAAAC,GAAA,YAAAsD,OAAA;gBACAA,OAAA,CAAA5D,OAAA,WAAAsB,OAAA,EAAAd,GAAA;kBACA,KAAAiD,KAAA,CAAAvD,QAAA,CAAAoB,OAAA,CAAAlD,KAAA;oBACA8E,MAAA,CAAAnG,IAAA,CAAA8D,IAAA,CAAAS,OAAA;kBACA;gBACA;cACA;cACA,KAAA4B,MAAA,CAAAnG,IAAA,CAAA2D,MAAA;gBACAwC,MAAA,CAAAnG,IAAA,CAAA8D,IAAA;kBACAzC,KAAA;kBACA4C,GAAA,EAAAhF,MAAA;kBACA2E,eAAA;kBACAF,KAAA;kBACAQ,IAAA;gBACA;gBACA,IAAAiC,MAAA,CAAAzG,MAAA;kBACAoH,GAAA,GAAAX,MAAA,CAAAlG,OAAA,CAAAqB,MAAA,WAAA4B,IAAA;oBAAA,OAAAA,IAAA,CAAAoF,OAAA;kBAAA;kBACA,IAAAxB,GAAA,CAAAnD,MAAA;oBACAwC,MAAA,CAAAnG,IAAA,IAAAqB,KAAA,GAAAyF,GAAA,IAAA7F,IAAA;kBACA;gBACA;cACA;cACA8F,QAAA,GAAAP,aAAA,CAAA+B,MAAA,WAAAnH,GAAA,EAAA8B,IAAA,EAAAyB,KAAA;gBACAvD,GAAA,CAAA8B,IAAA,IAAAyB,KAAA;gBACA,OAAAvD,GAAA;cACA;cAEA+E,MAAA,CAAAnG,IAAA,CAAAwI,IAAA,WAAAC,KAAA,EAAA1E,KAAA;gBACA,OAAAgD,QAAA,CAAA0B,KAAA,CAAApH,KAAA,IAAA0F,QAAA,CAAAhD,KAAA,CAAA1C,KAAA;cACA;cAEA8E,MAAA,CAAA7B,YAAA;YAAA;YAAA;cAAA,OAAA4C,QAAA,CAAAwB,IAAA;UAAA;QAAA,GAAAnC,OAAA;MAAA;IACA;IACAoB,SAAA,WAAAA,UAAAlD,GAAA;MACA,OAAA3F,SAAA,CAAA2F,GAAA;IACA;IACAkE,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACA,IAAA5I,IAAA,QAAAA,IAAA,CAAAoB,GAAA,WAAA8B,IAAA;QAAA,OAAAA,IAAA,CAAA7B,KAAA;MAAA,GAAAC,MAAA,WAAAwG,CAAA;QAAA,SAAAA,CAAA;MAAA;MACA,IAAAe,MAAA,QAAAC,SAAA,CAAA9I,IAAA;MACA,KAAA6I,MAAA;QACA,KAAAvG,QAAA;UACAC,OAAA;UACAjD,IAAA;QACA;QACA;MACA;MACA,KAAAU,IAAA,CAAA2D,MAAA;QACA,KAAArB,QAAA;UACAC,OAAA;UACAjD,IAAA;QACA;QACA;MACA;MAEA,SAAAI,MAAA;QACA,IAAAoH,GAAA,QAAA7G,OAAA,CAAAqB,MAAA,WAAA4B,IAAA;UAAA,OAAAA,IAAA,CAAAoF,OAAA;QAAA;QACA,IAAAxB,GAAA,CAAAnD,MAAA;UACA,IAAAoF,OAAA,GAAAjC,GAAA,CAAAkC,IAAA,WAAAhF,GAAA;YAAA,OAAAhE,IAAA,CAAAmD,QAAA,CAAAa,GAAA,CAAA/C,IAAA;UAAA;UACA,KAAA8H,OAAA;YACA,KAAAzG,QAAA;cACAC,OAAA;cACAjD,IAAA;YACA;YACA;UACA;QACA;MACA;MAEA,KAAAa,UAAA;MACA,IAAA8I,GAAA,GAAAjJ,IAAA,CAAAuB,IAAA;MACA,KAAAvB,IAAA,CAAAiD,OAAA,WAAAsB,OAAA,EAAAd,GAAA;QACA,IAAAP,IAAA,GAAA0F,MAAA,CAAA3I,OAAA,CAAAc,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,IAAA,KAAAsD,OAAA,CAAAlD,KAAA;QAAA;QAEA,IAAA2C,GAAA;QACA,IAAAd,IAAA;UAAA,IAAAgG,iBAAA;UACAlF,GAAA;YACAiB,YAAA,GAAAiE,iBAAA,GAAAN,MAAA,CAAA1D,UAAA,cAAAgE,iBAAA,uBAAAA,iBAAA,CAAA/D,cAAA;YACAC,UAAA,EAAAlC,IAAA,CAAAW,EAAA;YACAwB,YAAA,EAAAnC,IAAA,CAAAjC,IAAA;YACA6D,WAAA,EAAAP,OAAA,CAAAL,IAAA;YACAN,eAAA,EAAAW,OAAA,CAAAX;UACA;QACA;QACAgF,MAAA,CAAAO,KAAA;UAAAlF,GAAA,EAAAM,OAAA,CAAAlD,KAAA;UAAAA,KAAA,EAAA2C;QAAA;MACA;MAEA,KAAAmF,KAAA;QAAA1E,GAAA,OAAAA,GAAA;QAAAwE,GAAA,EAAAA;MAAA;MACA,KAAA9I,UAAA;MACA,KAAAiJ,WAAA;IACA;IACAA,WAAA,WAAAA,YAAA;MACA,KAAAD,KAAA;IACA;IACAL,SAAA,WAAAA,UAAA9I,IAAA;MACA,IAAAwH,IAAA;MACA,SAAA9C,CAAA,MAAAA,CAAA,GAAA1E,IAAA,CAAA2D,MAAA,EAAAe,CAAA;QACA,IAAAA,CAAA,KAAA1E,IAAA,CAAA2D,MAAA,QAAA3D,IAAA,CAAA0E,CAAA,MAAA1E,IAAA,CAAA0E,CAAA;UACA8C,IAAA;UACA;QACA;MACA;MACA,OAAAA,IAAA;IACA;IACA6B,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAtJ,IAAA,CAAAiD,OAAA,WAAAjC,CAAA;QACAsI,MAAA,CAAAjH,IAAA,CAAArB,CAAA;MACA;MACA,KAAAuI,eAAA;IACA;EAAA;AAEA", "ignoreList": []}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\detail.vue?vue&type=style&index=0&id=a5aa7006&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\detail.vue", "mtime": 1758519821153}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLnBhZ2luYXRpb24tY29udGFpbmVyIHsKICBwYWRkaW5nOiAwOwogIHRleHQtYWxpZ246IHJpZ2h0Owp9Cgo6OnYtZGVlcCAuZWwtY2FyZF9fYm9keSB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwoKICAuZWwtZGl2aWRlciB7CiAgICBtYXJnaW4tdG9wOiAwOwogICAgbWFyZ2luLWJvdHRvbTogMTZweDsKICAgIGJhY2tncm91bmQtY29sb3I6ICNFRUVFRUU7CiAgfQoKICAudGItb3B0aW9ucyB7CiAgICBtYXJnaW4tYm90dG9tOiAxNnB4OwogICAgZGlzcGxheTogZmxleDsKCiAgICAuZWwtZm9ybS1pdGVtLS1zbWFsbC5lbC1mb3JtLWl0ZW0gewogICAgICBtYXJnaW4tYm90dG9tOiAwOwogICAgfQogICAgLmVsLWlucHV0IHsKICAgICAgd2lkdGg6IDI1MHB4OwogICAgfQogIH0KCiAgZm9vdGVyIHsKICAgIHRleHQtYWxpZ246IGluaGVyaXQ7CiAgICBkaXNwbGF5OiBmbGV4OwogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIH0KfQoKLnRiLXggewogIGZsZXg6IDE7CiAgaGVpZ2h0OiAwOwogIG1hcmdpbi1ib3R0b206IDEwcHg7CiAgb3ZlcmZsb3c6IGF1dG87Cn0KCi50b3BUaXRsZSB7CiAgaGVpZ2h0OiAxNHB4OwogIGxpbmUtaGVpZ2h0OiAxNHB4OwogIGZvbnQtc2l6ZTogMTRweDsKICBtYXJnaW46IDAgMCAxNnB4OwoKICBzcGFuIHsKICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICAgIHdpZHRoOiAycHg7CiAgICBoZWlnaHQ6IDE0cHg7CiAgICBiYWNrZ3JvdW5kOiAjMDA5ZGZmOwogICAgdmVydGljYWwtYWxpZ246IG1pZGRsZTsKICAgIG1hcmdpbi1yaWdodDogNnB4OwogIH0KfQoKLmVsLWljb24tZWRpdCB7CiAgY3Vyc29yOiBwb2ludGVyOwp9CgouY3MtYm90dG9tIHsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgaGVpZ2h0OiA0MHB4OwogIGxpbmUtaGVpZ2h0OiA0MHB4OwoKICAuZGF0YS1pbmZvIHsKICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgIGJvdHRvbTogMDsKCiAgICAuaW5mby14IHsKICAgICAgbWFyZ2luLXJpZ2h0OiAyMHB4OwogICAgfQogIH0KfQoubXktaW5wdXQgewogIG1hcmdpbjogMTBweDsKICB3aWR0aDogMTQwcHg7CiAgaGVpZ2h0OiAzMnB4Owp9Cg=="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6tCA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/PRO/plan-production/task-allocation/v4", "sourcesContent": ["<template>\n  <div v-loading=\"loading\" class=\"app-container abs100\">\n    <el-card class=\"box-card h100\">\n      <h4 class=\"topTitle\"><span />基本信息</h4>\n      <el-form\n        ref=\"formInline\"\n        label-position=\"right\"\n        label-width=\"90px\"\n        :inline=\"true\"\n        :model=\"formInline\"\n        class=\"demo-form-inline\"\n      >\n        <el-row>\n          <el-col :span=\"20\">\n            <el-row>\n              <el-col :span=\"6\">\n                <el-form-item label=\"排产单号:\" label-width=\"75px\" prop=\"Schduling_Code\">\n                  <span>{{ formInline.Schduling_Code }}</span>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"6\">\n                <el-form-item label=\"项目名称:\" prop=\"Project_Name\">\n                  <span>{{ formInline.Project_Name }}</span>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"6\">\n                <el-form-item label=\"区域:\" prop=\"Area_Name\">\n                  <span>{{ formInline.Area_Name }}</span>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"6\">\n                <el-form-item label=\"批次:\" prop=\"Installunit_Name\">\n                  <span>{{ formInline.Installunit_Name }}</span>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"6\">\n                <el-form-item label=\"任务数量:\" label-width=\"75px\" prop=\"Allocation_Count\">\n                  <span>{{ formInline.Allocation_Count }}</span>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"6\">\n                <el-form-item label=\"任务重量:\" prop=\"Allocation_Weight\">\n                  <span>{{ formInline.Allocation_Weight | filterNum }}(t)</span>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"6\">\n                <el-form-item label=\"已完成数量:\" prop=\"Finish_Count\">\n                  <span>{{ formInline.Finish_Count }}</span>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"6\">\n                <el-form-item label=\"已完成重量:\" prop=\"Finish_Weight\">\n                  <span>{{ formInline.Finish_Weight | filterNum }}(t)</span>\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </el-col>\n          <el-col :span=\"4\">\n            <qrcode-vue\n              :size=\"79\"\n              :value=\"formInline.Schduling_Code\"\n              class-name=\"qrcode\"\n              level=\"H\"\n            />\n          </el-col>\n        </el-row>\n      </el-form>\n      <el-divider class=\"elDivder\" />\n      <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\n        <el-tab-pane label=\"待分配\" name=\"first\" />\n        <el-tab-pane\n          v-for=\"(element, index2) in workingTeam\"\n          :key=\"index2\"\n          :label=\"element.Working_Team_Name\"\n          :name=\"`${element.Working_Team_Name}$_$${element.Process_Code}`\"\n        />\n      </el-tabs>\n      <div class=\"tb-options\" :style=\"{'justify-content': !isView ? 'space-between' : 'end'}\">\n        <div>\n          <el-button v-if=\"!isView\" plain type=\"primary\" @click=\"reverseSelection()\">反选</el-button>\n          <el-button v-if=\"!isView\" type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"Batchallocation()\">批量分配</el-button>\n          <el-button v-if=\"!isView && activeName==='first'\" type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"preStepTaskAllocation()\">上道工序同步</el-button>\n        </div>\n        <el-form :inline=\"true\" :model=\"queryForm\" class=\"demo-form-inline\">\n          <el-form-item label=\"规格\">\n            <el-input v-model.trim=\"queryForm.Spec\" clearable placeholder=\"请输入\" />\n          </el-form-item>\n          <el-form-item v-if=\"isCom\" :label=\"`${bomName}类型`\">\n            <el-tree-select\n              ref=\"treeSelectObjectType\"\n              v-model=\"searchType\"\n              style=\"width: 100%\"\n              class=\"cs-tree-x\"\n              :select-params=\"treeSelectParams\"\n              :tree-params=\"ObjectTypeList\"\n              value-key=\"Id\"\n            />\n          </el-form-item>\n          <el-form-item :label=\"`${bomName}名称`\">\n            <el-input v-if=\"isCom\" v-model=\"queryForm.Comp_Codes\" clearable placeholder=\"请输入(空格区分/多个搜索)\" />\n            <el-input v-else v-model=\"queryForm.Part_Code\" clearable placeholder=\"请输入(空格区分/多个搜索)\" />\n            <el-input v-if=\"isCom\" v-model.trim=\"queryForm.Comp_Codes_Vague\" style=\"margin-left: 10px;\" clearable placeholder=\"模糊查找(请输入关键字)\" />\n            <el-input v-else v-model.trim=\"queryForm.Part_Code_Vague\" style=\"margin-left: 10px;\" clearable placeholder=\"模糊查找(请输入关键字)\" />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"filterData\">查询</el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n      <div class=\"tb-x\">\n        <vxe-table\n          ref=\"xTable\"\n          :empty-render=\"{name: 'NotData'}\"\n          show-header-overflow\n          class=\"cs-vxe-table\"\n          :row-config=\"{ isCurrent: true, isHover: true }\"\n          align=\"left\"\n          height=\"100%\"\n          show-overflow\n          :loading=\"tbLoading\"\n          :checkbox-config=\"{checkField: 'checked',checkMethod }\"\n          stripe\n          size=\"medium\"\n          :edit-config=\"{\n            trigger: 'click',\n            mode: 'cell',\n            showIcon: !isView,\n            beforeEditMethod: activeCellMethod,\n          }\"\n          :data=\"filterTbData\"\n          resizable\n          :tooltip-config=\"{ enterable: true }\"\n          @checkbox-all=\"tbSelectChange\"\n          @checkbox-change=\"tbSelectChange\"\n        >\n          <vxe-column type=\"checkbox\" width=\"60\" fixed=\"left\" />\n          <template v-for=\"item in columns\">\n\n            <!--            <vxe-column :align=\"item.Align\"\n              v-if=\"item.Code==='Comp_Code'||item.Code==='Part_Code'\"\n              :key=\"item.Id\"\n              :filters=\"[{ data: '' }]\"\n              :filter-method=\"filterCodeMethod\"\n              :filter-recover-method=\"filterCodeRecoverMethod\"\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n              show-overflow=\"tooltip\"\n              sortable\n              :field=\"item.Code\"\n              :title=\"item.Display_Name\"\n              :min-width=\"item.Width\"\n            >\n              <template #filter=\"{ $panel, column }\">\n                <template v-for=\"(option, index) in column.filters\">\n                  <input :key=\"index\" v-model=\"option.data\" class=\"my-input\" type=\"type\" placeholder=\"按回车确认筛选\" @input=\"$panel.changeOption($event, !!option.data, option)\" @keyup.enter=\"$panel.confirmFilter()\">\n                </template>\n              </template>\n            </vxe-column>\n            <vxe-column :align=\"item.Align\"\n              v-else-if=\"item.Code==='Type'\"\n              :key=\"item.Id\"\n              :filters=\"[{ data: '' }]\"\n              :filter-method=\"filterTypeMethod\"\n              :filter-recover-method=\"filterTypeRecoverMethod\"\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n              show-overflow=\"tooltip\"\n              sortable\n              :field=\"item.Code\"\n              :title=\"item.Display_Name\"\n              :min-width=\"item.Width\"\n            >\n              <template #filter=\"{ $panel, column }\">\n                <template v-for=\"(option, index) in column.filters\">\n                  <input :key=\"index\" v-model=\"option.data\" class=\"my-input\" type=\"type\" placeholder=\"按回车确认筛选\" @input=\"$panel.changeOption($event, !!option.data, option)\" @keyup.enter=\"$panel.confirmFilter()\">\n                </template>\n              </template>\n            </vxe-column>-->\n            <vxe-column\n              v-if=\"item.Code==='Comp_Code'|| item.Code==='Part_Code'\"\n              :key=\"item.Id\"\n              :align=\"item.Align\"\n              :fixed=\"item.fixed\"\n              show-overflow=\"tooltip\"\n              sortable\n              :visible=\"item.visible\"\n              :field=\"item.Code\"\n              :title=\"item.Display_Name\"\n              :min-width=\"item.Width\"\n            >\n              <template #default=\"{ row }\">\n                <el-tag v-if=\"!!row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\n                <span>{{ row[item.Code] }}</span>\n              </template>\n            </vxe-column>\n            <vxe-column\n              v-else-if=\"item.Code==='Schduled_Count'\"\n              :key=\"`SchduledCount${item.Id}`\"\n              :align=\"item.Align\"\n              :fixed=\"item.fixed\"\n              show-overflow=\"tooltip\"\n              sortable\n              :visible=\"item.visible\"\n              :field=\"item.Code\"\n              :title=\"item.Display_Name\"\n              :min-width=\"item.Width\"\n            >\n              <template #default=\"{ row }\">\n                {{ activeName === 'first' ?'':row[getTaskCode()] }}\n              </template>\n            </vxe-column>\n            <vxe-column\n              v-else\n              :key=\"`Default${item.Id}`\"\n              :align=\"item.Align\"\n              :fixed=\"item.fixed\"\n              :visible=\"item.visible\"\n              show-overflow=\"tooltip\"\n              sortable\n              :field=\"item.Code\"\n              :title=\"item.Display_Name\"\n              :min-width=\"item.Width\"\n            />\n          </template>\n\n          <vxe-column\n            v-for=\"(element, index2) in workingTeamColumn\"\n            :key=\"index2\"\n            :align=\"element.Align\"\n            :visible=\"element.visible\"\n            fixed=\"right\"\n            :field=\"`${element.Working_Team_Name}$_$${element.Process_Code}`\"\n            title=\"可分配数量\"\n            sortable\n            min-width=\"170\"\n          >\n            <template #edit=\"{ row }\">\n              <vxe-input\n                v-model.number=\"\n                  row[\n                    getRowUnique(\n                      row.uuid,\n                      element.Process_Code,\n                      element.Working_Team_Id\n                    )\n                  ]\n                \"\n                type=\"integer\"\n                :min=\"0\"\n                :max=\"\n                  row[\n                    getRowUniqueMax(\n                      row.uuid,\n                      element.Process_Code,\n                      element.Working_Team_Id\n                    )\n                  ]\n                \"\n                @change=\"\n                  inputChange(\n                    row,\n                    element.Process_Code,\n                    element.Working_Team_Id\n                  )\n                \"\n              />\n            </template>\n            <template #default=\"{ row }\">\n              <template\n                v-if=\"\n                  checkPermissionTeam(row.Technology_Path, element.Process_Code)\n                \"\n              >\n                {{\n                  row[\n                    getRowUnique(\n                      row.uuid,\n                      element.Process_Code,\n                      element.Working_Team_Id\n                    )\n                  ]\n                }}\n              </template>\n              <template v-else> -</template>\n            </template>\n          </vxe-column>\n\n          <vxe-column\n            :key=\"activeName\"\n            align=\"left\"\n            :edit-render=\"{}\"\n            field=\"AllocatedCount\"\n            title=\"分配数量\"\n            sortable\n            fixed=\"right\"\n            min-width=\"180\"\n          >\n            <template #edit=\"{ row }\">\n              <vxe-input\n                :key=\"activeName\"\n                v-model.number=\"row[getRowCCode(row,'alCount')]\"\n                type=\"integer\"\n                :min=\"0\"\n                :max=\"row[getRowCCode(row,'','Max')]\"\n              />\n            </template>\n            <template #default=\"{ row }\">\n              {{ row[getRowCCode(row,'alCount')] | displayValue }}\n            </template>\n          </vxe-column>\n        </vxe-table>\n      </div>\n      <footer>\n        <div class=\"data-info\">\n          <el-tag size=\"medium\" class=\"info-x\">已选{{ multipleSelection.length }}条数据</el-tag>\n          <el-tag v-if=\"tipLabel\" size=\"medium\" class=\"info-x\">{{ tipLabel }}</el-tag>\n        </div>\n        <div>\n          <el-button @click=\"handleClose\">取消 </el-button>\n          <el-button\n            v-if=\"!isView\"\n            type=\"primary\"\n            :loading=\"loading\"\n            @click=\"handleSubmit\"\n          >提交</el-button>\n        </div>\n      </footer>\n    </el-card>\n    <el-dialog\n      v-dialogDrag\n      title=\"批量分配\"\n      class=\"plm-custom-dialog\"\n      :visible.sync=\"dialogVisible\"\n      width=\"30%\"\n      @close=\"handleDialog\"\n    >\n      <el-form\n        ref=\"form\"\n        :model=\"form\"\n        :rules=\"rules\"\n        label-width=\"80px\"\n        class=\"demo-ruleForm\"\n      >\n        <el-form-item label=\"选择班组\" prop=\"TeamGroup\">\n          <el-select\n            v-model=\"form.TeamGroup\"\n            class=\"w100\"\n            placeholder=\"请选择\"\n            filterable\n            clearable\n          >\n            <el-option\n              v-for=\"item in workingTeam\"\n              :key=\"item.Working_Team_Id\"\n              :label=\"item.Working_Team_Name\"\n              :value=\"item.Working_Team_Id\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item style=\"text-align: right\">\n          <el-button @click=\"resetForm('form')\">取 消</el-button>\n          <el-button\n            type=\"primary\"\n            @click=\"submitForm('form');resetForm('form')\"\n          >确 定</el-button>\n        </el-form-item>\n      </el-form>\n    </el-dialog>\n    <el-dialog\n      v-dialogDrag\n      title=\"提示\"\n      class=\"plm-custom-dialog\"\n      :visible.sync=\"dialogTipsVisible\"\n      width=\"450px\"\n      @close=\"handleDialog\"\n    >\n      <div style=\"text-align: center; font-size: 16px;\">部分{{ bomName }}与上道工序加工班组不同，请手动分配</div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogTipsVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"dialogTipsVisible = false\">确 定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport getTbInfo from '@/mixins/PRO/get-table-info'\nimport { FIX_COLUMN } from '@/views/PRO/plan-production/schedule-production/constant'\nimport {\n  AdjustTeamProcessAllocation,\n  GetTeamProcessAllocation,\n  AdjustPartTeamProcessAllocation,\n  AdjustSubAssemblyTeamProcessAllocation,\n  GetStopList,\n  GetPreStepTaskAllocation\n} from '@/api/PRO/production-task'\nimport QrcodeVue from 'qrcode.vue'\nimport { v4 as uuidv4 } from 'uuid'\nimport numeral from 'numeral'\nimport { closeTagView, deepClone } from '@/utils'\nimport { GetCompTypeTree } from '@/api/PRO/professionalType'\nimport { GetBOMInfo, getBomCode, getBomName, checkIsUnitPart } from '@/views/PRO/bom-setting/utils'\n\nconst SPLIT_SYMBOL = '$_$'\nexport default {\n  components: {\n    QrcodeVue\n  },\n  filters: {\n    filterNum(value) {\n      return numeral(value).divide(1000).format('0.[00]')\n    }\n  },\n  mixins: [getTbInfo],\n  data() {\n    return {\n      treeSelectParams: {\n        placeholder: '请选择',\n        clearable: true\n      },\n      ObjectTypeList: {\n        // 构件类型\n        'check-strictly': true,\n        'default-expand-all': true,\n        clickParent: true,\n        data: [],\n        props: {\n          children: 'Children',\n          label: 'Label',\n          value: 'Data'\n        }\n      },\n      tbLoading: false,\n      loading: false,\n      activeName: 'first',\n      tipLabel: '',\n      tbData: [],\n      filterTbData: [],\n      multipleSelection: [],\n      columns: [],\n      workingTeam: [],\n      workingTeamColumn: [],\n      formInline: {},\n      pg_type: '',\n      searchType: '',\n      type: '',\n      queryForm: {\n        Comp_Codes: '',\n        Part_Code: '',\n        Spec: '',\n        Comp_Codes_Vague: '',\n        Part_Code_Vague: ''\n      },\n      dialogVisible: false,\n      dialogTipsVisible: false,\n      form: {\n        TeamGroup: '' // 班组\n      },\n\n      rules: {\n        TeamGroup: [\n          { required: true, message: '请输入班组名称', trigger: 'change' }\n        ]\n      },\n      Is_Workshop_Enabled: false,\n      Working_Process_Id: '',\n      bomList: [],\n      bomLevel: '',\n      bomName: ''\n    }\n  },\n  computed: {\n    isView() {\n      return this.type === 'view'\n    },\n    isCom() {\n      return this.bomLevel === getBomCode('-1')\n    },\n    isUnitPart() {\n      return checkIsUnitPart(this.bomLevel)\n    }\n  },\n\n  async mounted() {\n    try {\n      const { list } = await GetBOMInfo()\n      this.bomList = list || []\n      const rowInfo = JSON.parse(decodeURIComponent(this.$route.query.other))\n      console.log('rowInfo', rowInfo)\n      this.formInline = Object.assign({}, this.formInline, rowInfo)\n      this.pg_type = this.$route.query.pg_type\n      this.bomLevel = this.$route.query.bomLevel\n      this.bomName = await getBomName(this.bomLevel)\n      this.type = this.$route.query.type\n      this.Is_Workshop_Enabled = this.$route.query.Is_Workshop_Enabled\n      await this.getTableConfig(this.isUnitPart ? 'PROTaskUnitAllocationChange' : 'PROTaskAllocationChange')\n      this.columns = this.columns.filter(v => v.Is_Display).map(item => {\n        if (FIX_COLUMN.includes(item.Code)) {\n          item.fixed = 'left'\n        }\n        if (item.Code === 'Schduled_Count') {\n          item.visible = false\n        }\n        return item\n      })\n\n      if (this.isCom) {\n        const idx = this.columns.findIndex(item => item.Code === 'Part_Code')\n        idx !== -1 && this.columns.splice(idx, 1)\n        const idx2 = this.columns.findIndex(item => item.Code === 'Component_Code')\n        idx2 !== -1 && this.columns.splice(idx2, 1)\n      } else {\n        const idx = this.columns.findIndex(item => item.Code === 'Comp_Code')\n        idx !== -1 && this.columns.splice(idx, 1)\n        const idx2 = this.columns.findIndex(item => item.Code === 'Type')\n        idx2 !== -1 && this.columns.splice(idx2, 1)\n      }\n      if (!this.Is_Workshop_Enabled) {\n        const idx3 = this.columns.findIndex(item => item.Code === 'Workshop_Name')\n        idx3 !== -1 && this.columns.splice(idx3, 1)\n      }\n      this.getObjectTypeList()\n      this.fetchData()\n    } catch (e) {\n      this.$message({\n        message: '参数错误,请重新操作',\n        type: 'error'\n      })\n    }\n  },\n  methods: {\n    getRowCCode(row, prefix = '', suffix = '') {\n      if (this.activeName === 'first') {\n        return 'allocatedTask' + suffix\n      } else {\n        const arr = this.activeName.split(SPLIT_SYMBOL)\n        const team = this.workingTeam.find(v => v.Working_Team_Name === arr[0])\n        const u = this.getRowUnique(row.uuid, row.Process_Code, team.Working_Team_Id)\n        if (suffix === 'Max') {\n          return u\n        } else {\n          return prefix + u + suffix\n        }\n      }\n    },\n    handleClick(val) {\n      console.log('handleClick', val)\n      if (val.name === 'first') {\n        const c1 = this.$refs.xTable.getColumnByField('Schduled_Count')\n        const c2 = this.$refs.xTable.getColumnByField('Can_Allocation_Count')\n        c1.visible = false\n        c2.visible = true\n      } else {\n        const c2 = this.$refs.xTable.getColumnByField('Schduled_Count')\n        const c3 = this.$refs.xTable.getColumnByField('Can_Allocation_Count')\n        c2.visible = true\n        c3.visible = false\n      }\n\n      this.workingTeam.forEach((element, idx) => {\n        const codes = `${element.Working_Team_Name}$_$${element.Process_Code}`\n\n        const c = this.$refs.xTable.getColumnByField(codes)\n\n        c.visible = codes === val.name\n      })\n      this.$refs.xTable.refreshColumn()\n\n      this.filterTbData = this.tbData.filter(row => {\n        return this.filterZero(row)\n      })\n\n      this.multipleSelection = []\n      this.$refs.xTable.clearCheckboxRow()\n    },\n    filterZero(row) {\n      if (this.activeName === 'first') {\n        return row.allocatedTask > 0\n      } else {\n        const arr = this.activeName.split(SPLIT_SYMBOL)\n        const team = this.workingTeam.find(v => v.Working_Team_Name === arr[0])\n        const code = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\n        return row[code] > 0\n      }\n    },\n    fetchData() {\n      const Comp_Codes = !this.queryForm.Comp_Codes ? [] : this.queryForm.Comp_Codes.trim().split(' ')\n      const Part_Code = !this.queryForm.Part_Code ? [] : this.queryForm.Part_Code.trim().split(' ')\n      let Process_Type = 2\n      Process_Type = this.isCom ? 2 : this.isUnitPart ? 3 : 1\n      this.tbLoading = true\n      GetTeamProcessAllocation({\n        Page: 1,\n        PageSize: -1,\n        Step: this.formInline.Step,\n        Process_Type,\n        Bom_Level: this.bomLevel,\n        Schduling_Code: this.formInline.Schduling_Code,\n        Process_Code: this.formInline.Process_Code,\n        Workshop_Name: this.formInline.Workshop_Name,\n        Area_Id: this.formInline.Area_Id,\n        InstallUnit_Id: this.formInline.InstallUnit_Id,\n        Comp_Codes,\n        Part_Code\n      }).then(async(res) => {\n        if (res.IsSucceed) {\n          const { Schduling_Plan, Schduling_Comps } = res.Data\n          this.planInfoTemp = Schduling_Plan\n          await this.getStopList(Schduling_Comps)\n          this.initTbData(Schduling_Comps)\n          this.Working_Process_Id = res.Data.Working_Process_Id\n\n          if (Schduling_Comps.length) {\n            this.workingTeam = Schduling_Comps[0].Allocation_Teams\n            const _kk = this.workingTeam.map(v => {\n              v.visible = false\n              return v\n            })\n            this.workingTeamColumn = deepClone(_kk)\n          }\n          console.log(' this.tbData', this.tbData)\n          this.filterData()\n          console.log('filterTbData', this.filterTbData)\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n      }).finally(_ => {\n        this.tbLoading = false\n      })\n    },\n    checkMethod({ row }) {\n      return !row.stopFlag\n    },\n    async getStopList(list) {\n      const key = 'Id'\n      const submitObj = list.map(item => {\n        return {\n          Id: item[key],\n          Bom_Level: this.bomLevel,\n          Type: this.isCom ? 2 : this.isUnitPart ? 3 : 1 // 1：零件，3：部件，2：构件\n        }\n      })\n      await GetStopList(submitObj).then(res => {\n        if (res.IsSucceed) {\n          const stopMap = {}\n          res.Data.forEach(item => {\n            stopMap[item.Id] = !!item.Is_Stop\n          })\n          list.forEach(row => {\n            if (stopMap[row[key]]) {\n              this.$set(row, 'stopFlag', stopMap[row[key]])\n            }\n          })\n        }\n      })\n    },\n    filterData() {\n      console.log('searchType', this.searchType)\n      this.multipleSelection = []\n      this.$refs.xTable.clearCheckboxRow()\n      const code = this.isCom ? 'Comp_Code' : 'Part_Code'\n      const queryCode = this.isCom ? 'Comp_Codes' : 'Part_Code'\n      const codeList = this.queryForm[queryCode].split(' ').filter(v => !!v)\n\n      const queryCodeVague = this.isCom ? 'Comp_Codes_Vague' : 'Part_Code_Vague'\n      const codeListVague = this.queryForm[queryCodeVague]\n\n      const searchTbData = this.tbData.filter(v => {\n        if (!codeList.length && codeListVague === '') {\n          return true\n        } else {\n          return codeList.includes(v[code])\n        }\n      })\n\n      const vagueData = searchTbData.length > 0 && codeListVague === '' ? searchTbData : searchTbData.length === 0 && codeListVague === '' ? searchTbData : this.tbData\n      const searchVagueTbData = vagueData.filter(v => {\n        if (codeListVague === '' && !codeList.length) {\n          return true\n        } else {\n          return v[code].includes(codeListVague)\n        }\n      })\n\n      // 合并两个数组\n      const mergedArray = searchTbData.concat(searchVagueTbData)\n      // 根据 Schduling_Detail_Id 进行去重\n      const uniqueArray = mergedArray.reduce((acc, current) => {\n        const existingObject = acc.find(item => item.Schduling_Detail_Id === current.Schduling_Detail_Id)\n        if (!existingObject) {\n          acc.push(current)\n        }\n        return acc\n      }, [])\n\n      this.filterTbData = uniqueArray.filter(v => {\n        if (!this.searchType) return true\n        return this.searchType === v.Type\n      }).filter(v => {\n        if (!this.queryForm.Spec) return true\n        return (v.Spec || '').includes(this.queryForm.Spec)\n      }).filter(row => {\n        return this.filterZero(row)\n      })\n    },\n    initTbData(list, teamKey = 'Allocation_Teams') {\n      this.tbData = list.map(row => {\n        const processList = row.Technology_Path?.split('/') || []\n        // 已uuid作为row唯一值；\n        // uuid+工序+班组为输入框值\n        row.uuid = uuidv4()\n        const newData = row[teamKey].filter((r) => processList.findIndex((p) => r.Process_Code === p) !== -1)\n        row.defaultCan_Allocation_Count = row.Can_Allocation_Count\n        let _inputNum = 0\n        newData.forEach((ele, index) => {\n          const code = this.getRowUnique(row.uuid, ele.Process_Code, ele.Working_Team_Id)\n          const max = this.getRowUniqueMax(row.uuid, ele.Process_Code, ele.Working_Team_Id)\n          row[code] = ele.Count\n          this.$set(row, 'alCount' + code, ele.Count)\n          row[max] = 0\n          _inputNum += ele.Count\n          this.$set(row, 'totalTask' + ele.Working_Team_Name, ele.Total_Receive_Count + ele.Count)\n        })\n\n        this.$set(row, 'allocatedTask', row.defaultCan_Allocation_Count - _inputNum)\n        row.Can_Allocation_Count = row.allocatedTask\n        this.$set(row, 'allocatedTaskMax', row.Can_Allocation_Count)\n\n        this.setInputMax(row)\n\n        row.checked = false\n        return row\n      })\n    },\n    inputChange(row) {\n      this.setInputMax(row)\n    },\n    setInputMax(row) {\n      let _inputNum = 0\n      const inputValuesKeys = Object.keys(row)\n        .filter(v => !v.endsWith('max') && v.startsWith(row.uuid) && v.length > row.uuid.length)\n      inputValuesKeys.forEach((val) => {\n        const curCode = val.split(SPLIT_SYMBOL)[1]\n        const otherTotal = inputValuesKeys.filter(x => {\n          const code = x.split(SPLIT_SYMBOL)[1]\n          return x !== val && code === curCode\n        }).reduce((acc, item) => {\n          return acc + numeral(row[item]).value()\n        }, 0)\n        row[val + SPLIT_SYMBOL + 'max'] = row.Schduled_Count - otherTotal\n        _inputNum += +row[val]\n      })\n      // row.allocatedCount = row.defaultCan_Allocation_Count - _inputNum\n      // row.Can_Allocation_Count = row.allocatedCount\n    },\n    checkPermissionTeam(processStr, processCode) {\n      if (!processStr) return false\n      const list = processStr?.split('/') || []\n      return !!list.some(v => v === processCode)\n    },\n\n    getSubmitTbInfo(tbData = this.tbData) {\n      // 处理上传的数据\n      const tableData = JSON.parse(JSON.stringify(tbData))\n      for (let i = 0; i < tableData.length; i++) {\n        const element = tableData[i]\n        const list = []\n        if (!element.Technology_Path) {\n          this.$message({\n            message: '工序不能为空',\n            type: 'warning'\n          })\n          return { status: false }\n        }\n        const processList = Array.from(new Set(element.Technology_Path.split('/')))\n        processList.forEach(code => {\n          const groups = this.workingTeam.filter(v => v.Process_Code === code)\n\n          const groupsList = groups.map((group, index) => {\n            const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)\n            const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)\n            const obj = {\n              Comp_Code: element.Comp_Code,\n              Again_Count: +element[uCode],\n              Part_Code: this.isCom ? null : element.Part_Code,\n              Process_Code: code,\n              Technology_Path: element.Technology_Path,\n              Working_Team_Id: group.Working_Team_Id,\n              Working_Team_Name: group.Working_Team_Name,\n              Team_Task_Id: element.Allocation_Teams.find((item) => item.Working_Team_Id === group.Working_Team_Id).Team_Task_Id\n            }\n            delete element['alCount' + uCode]\n            delete element['allocatedTask']\n            delete element['allocatedTaskMax']\n            delete element[uCode]\n            delete element[uMax]\n            return obj\n          })\n          list.push(...groupsList)\n        })\n        console.log(list)\n        delete element['uuid']\n        delete element['puuid']\n        element.Allocation_Teams = list\n      }\n      return { tableData, status: true }\n    },\n    handleSubmit() {\n      const { tableData, status } = this.getSubmitTbInfo()\n      if (!status) return\n      this.$confirm('是否提交当前数据?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.loading = true\n        const obj = {\n          Schduling_Plan: this.planInfoTemp,\n          Schduling_Comps: tableData\n        }\n        const Partobj = {\n          Schduling_Plan: this.planInfoTemp,\n          SarePartsModel: tableData\n        }\n        const requestFn = this.isCom ? AdjustTeamProcessAllocation : this.isUnitPart ? AdjustSubAssemblyTeamProcessAllocation : AdjustPartTeamProcessAllocation\n        console.log('obj', obj)\n        requestFn(this.isCom ? obj : Partobj).then(res => {\n          if (res.IsSucceed) {\n            this.$message({\n              message: '操作成功',\n              type: 'success'\n            })\n            this.handleClose()\n          } else {\n            this.$message({\n              message: res.Message,\n              type: 'error'\n            })\n          }\n          this.loading = false\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消'\n        })\n      })\n    },\n    handleClose() {\n      this.closeView()\n    },\n    closeView() {\n      closeTagView(this.$store, this.$route)\n    },\n    tbSelectChange(array) {\n      this.multipleSelection = array.records\n      console.log(this.tbData)\n    },\n    getTaskCode(name = '') {\n      if (name) return 'totalTask' + name\n      return 'totalTask' + this.activeName.split(SPLIT_SYMBOL)[0]\n    },\n    // 反选\n    reverseSelection() {\n      const list = this.$refs.xTable.getCheckboxRecords().filter(item => !item.stopFlag)\n      const unSelectList = this.filterTbData.filter(item => !list.includes(item) && !item.stopFlag)\n      this.$refs.xTable.setCheckboxRow(list, false)\n      this.$refs.xTable.setCheckboxRow(unSelectList, true)\n      this.multipleSelection = this.$refs.xTable.getCheckboxRecords()\n    },\n    // async getTableConfig(code) {\n    //   await GetGridByCode({\n    //     code\n    //   }).then((res) => {\n    //     const { IsSucceed, Data, Message } = res\n    //     if (IsSucceed) {\n    //       this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\n    //       const list = Data.ColumnList || []\n    //       this.columns = list.filter(v => v.Is_Display).map(item => {\n    //         if (FIX_COLUMN.includes(item.Code)) {\n    //           item.fixed = 'left'\n    //         }\n    //         if (item.Code === 'Schduled_Count') {\n    //           item.visible = false\n    //         }\n    //         return item\n    //       })\n    //     } else {\n    //       this.$message({\n    //         message: Message,\n    //         type: 'error'\n    //       })\n    //     }\n    //   })\n    // },\n    activeCellMethod({ row, column, columnIndex }) {\n      if (this.isView) return false\n      if (column.field === 'AllocatedCount') return true\n      const processCode = column.field?.split('$_$')[1]\n      return this.checkPermissionTeam(row.Technology_Path, processCode)\n    },\n    getRowUnique(uuid, processCode, workingId) {\n      return `${uuid}${SPLIT_SYMBOL}${processCode}${SPLIT_SYMBOL}${workingId}`\n    },\n    getRowUniqueMax(uuid, processCode, workingId) {\n      return this.getRowUnique(uuid, processCode, workingId) + `${SPLIT_SYMBOL}max`\n    },\n    // 批量分配\n    Batchallocation() {\n      this.dialogVisible = true\n      console.log(this.workingTeam)\n    },\n    // 上道工序分配\n    preStepTaskAllocation() {\n      const Schduling_Detail_Ids = this.multipleSelection.map(item => item.Schduling_Detail_Id)\n      const Working_Process_Code = this.multipleSelection[0].Process_Code\n      GetPreStepTaskAllocation({\n        Schduling_Detail_Ids,\n        Working_Process_Code,\n        Working_Process_Id: this.Working_Process_Id,\n        Process_Type: this.isCom ? 2 : this.isUnitPart ? 3 : 1,\n        Bom_Level: this.bomLevel\n      }).then(res => {\n        if (res.IsSucceed) {\n          if (res.Data.length === 0) {\n            this.dialogTipsVisible = true\n            return\n          }\n          this.preDoAllocation(res.Data)\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    handleDialog() {\n      this.dialogVisible = false\n      // this.multipleSelection = []\n    },\n    handelData() {\n      this.multipleSelection.forEach((item) =>\n        item.Allocation_Teams.forEach((v) => {\n          if (v.Working_Team_Id === this.form.TeamGroup) {\n            v.Count = item.Can_Allocation_Count\n          } else {\n            v.Count = 0\n          }\n        })\n      )\n      // const tableData = JSON.parse(JSON.stringify(this.multipleSelection))\n      for (let i = 0; i < this.multipleSelection.length; i++) {\n        const element = this.multipleSelection[i]\n        const processList = Array.from(new Set(element.Technology_Path.split('/')))\n        processList.forEach(code => {\n          const groups = this.workingTeam.filter(v => v.Process_Code === code)\n          groups.forEach(group => {\n            const uniCode = this.getRowUnique(element.uuid, code, this.form.TeamGroup)\n            const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)\n            const uniMax = this.getRowUniqueMax(element.uuid, code, this.form.TeamGroup)\n            const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)\n\n            if (uniCode === uCode && uniMax === uMax) {\n              element[uCode] = element['Can_Allocation_Count']\n              element[uMax] = element['Can_Allocation_Count']\n            } else {\n              element[uCode] = 0\n              element[uMax] = 0\n            }\n          })\n        })\n      }\n      console.log(this.multipleSelection)\n      for (let i = 0; i < this.tbData.length; i++) {\n        for (let k = 0; k < this.multipleSelection.length; k++) {\n          if (this.tbData[i].uuid === this.multipleSelection[k].uuid) {\n            this.$nextTick((_) => {\n              this.tbData[i] = this.multipleSelection[k]\n            })\n          }\n        }\n      }\n    },\n    submitForm(formName) {\n      this.$refs[formName].validate((valid) => {\n        if (valid) {\n          // this.handelData()\n          this.doAllocation()\n          // this.workingTeam.find(v=> v.Working_Team_Id === this.form.TeamGroup).count\n          this.handleDialog()\n          console.log(this.tbData)\n          this.multipleSelection = []\n          this.$refs.xTable.clearCheckboxRow()\n        } else {\n          return false\n        }\n      })\n    },\n    // 上道工序分配\n    preDoAllocation(preProcessData) {\n      const preProcessDataMap = new Map()\n      preProcessData.forEach(item => {\n        const key = `${item.Schduling_Detail_Id}_${item.Working_Team_Id}`\n        preProcessDataMap.set(key, item.Current_Task_Count)\n      })\n\n      const allocateForTeam = (row, team, amount) => {\n        const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\n        const bKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\n        const currentTaskCount = preProcessDataMap.get(bKey) || 0\n        const allocated = Math.min(amount, currentTaskCount, row.Can_Allocation_Count)\n\n        row[tarCode] = (Number(row[tarCode]) || 0) + allocated\n        row.Can_Allocation_Count -= allocated\n        row[this.getTaskCode(team.Working_Team_Name)] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + allocated\n        row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - allocated\n        row['alCount' + tarCode] = row[tarCode]\n\n        return allocated\n      }\n      let isMessage = true\n      this.multipleSelection.forEach(row => {\n        if (!row.Can_Allocation_Count && this.activeName === 'first') return\n\n        const eligibleTeams = row.Allocation_Teams.filter(team =>\n          preProcessDataMap.has(`${row.Schduling_Detail_Id}_${team.Working_Team_Id}`)\n        )\n\n        if (eligibleTeams.length === 0) return\n        if (this.activeName === 'first') {\n          let IsAllNo = 0\n          const totalAvailable = eligibleTeams.reduce((sum, team) => {\n            const bKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\n            return sum + (preProcessDataMap.get(bKey) || 0)\n          }, 0)\n\n          if (row.allocatedTaskMax < totalAvailable) {\n            IsAllNo++\n            if (IsAllNo === this.multipleSelection.length) {\n              isMessage = false\n              this.dialogTipsVisible = true\n            }\n            return\n          }\n\n          let remaining = Math.min(row.allocatedTask, row.Can_Allocation_Count)\n          const perTeamAllocation = Math.floor(remaining / eligibleTeams.length)\n\n          eligibleTeams.forEach((team, index) => {\n            if (remaining <= 0) return\n\n            const allocateAmount = index === eligibleTeams.length - 1\n              ? remaining\n              : Math.min(perTeamAllocation, remaining)\n\n            remaining -= allocateForTeam(row, team, allocateAmount)\n          })\n\n          row.allocatedTaskMax = row.Can_Allocation_Count\n          row.allocatedTask = row.Can_Allocation_Count\n          if (IsAllNo === this.multipleSelection.length) {\n            isMessage = false\n            this.dialogTipsVisible = true\n            return\n          }\n        } else {\n          eligibleTeams.forEach(team => {\n            const bKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\n            const currentTaskCount = preProcessDataMap.get(bKey) || 0\n\n            if (row[this.getRowCCode(row)] < currentTaskCount) {\n              return\n            }\n\n            const selectNum = Math.min(\n              row[this.getRowCCode(row, 'alCount')] || 0,\n              row[this.getRowCCode(row)] || 0,\n              currentTaskCount\n            )\n\n            if (selectNum > 0) {\n              const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\n              row[this.getTaskCode(team.Working_Team_Name)] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + selectNum\n              row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - selectNum\n              row[tarCode] = (Number(row[tarCode]) || 0) + selectNum\n              row[this.getRowCCode(row)] = (row[this.getRowCCode(row)] || 0) - selectNum\n              row[this.getRowCCode(row, 'alCount')] = (row[this.getRowCCode(row, 'alCount')] || 0) - selectNum\n              row['alCount' + tarCode] = row[tarCode]\n            }\n          })\n        }\n      })\n      // if (this.activeName === 'first') {\n      //   let IsAllNo = 0\n      //   this.multipleSelection.forEach((row, idx) => {\n      //     if (row.Can_Allocation_Count) {\n      //       const validTeams = row.Allocation_Teams.filter(team => {\n      //         const key = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\n      //         return preProcessDataMap.has(key)\n      //       })\n      //       if (validTeams.length > 0) {\n      //         const team = validTeams[0]\n      //         const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\n\n      //         const preProcessKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\n      //         const currentTaskCount = preProcessDataMap.get(preProcessKey) || 0\n\n      //         if (currentTaskCount > row.allocatedTaskMax) {\n      //           IsAllNo++\n      //           return\n      //         }\n      //         const allocated = Math.min(row.allocatedTask, currentTaskCount)\n\n      //         row[tarCode] = Number(row[tarCode] || 0) + allocated\n      //         row.Can_Allocation_Count = row.Can_Allocation_Count - allocated\n      //         row[this.getTaskCode(team.Working_Team_Name) || 0] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + allocated\n      //         row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - allocated\n      //         row.allocatedTaskMax = row.Can_Allocation_Count\n      //         row.allocatedTask = row.Can_Allocation_Count\n      //         row['alCount' + tarCode] = row[tarCode]\n      //       }\n      //     }\n      //   })\n      //   if (IsAllNo === this.multipleSelection.length) {\n      //     this.dialogTipsVisible = true\n      //     return\n      //   }\n      // } else {\n      //   this.multipleSelection.forEach((row, idx) => {\n      //     const validTeams = row.Allocation_Teams.filter(team => {\n      //       const key = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\n      //       return preProcessDataMap.has(key)\n      //     })\n      //     if (validTeams.length > 0) {\n      //       const team = validTeams[0]\n      //       const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\n\n      //       const preProcessKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\n      //       const currentTaskCount = preProcessDataMap.get(preProcessKey) || 0\n\n      //       const selectNum = Math.min(\n      //         row[this.getRowCCode(row, 'alCount')] || 0,\n      //         row[this.getRowCCode(row)] || 0,\n      //         currentTaskCount\n      //       )\n\n      //       row[this.getTaskCode(team.Working_Team_Name)] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + selectNum\n      //       row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - selectNum\n      //       row[tarCode] = (Number(row[tarCode]) || 0) + selectNum\n      //       row[this.getRowCCode(row)] = (row[this.getRowCCode(row)] || 0) - selectNum\n      //       row[this.getRowCCode(row, 'alCount')] = (row[this.getRowCCode(row, 'alCount')] || 0) - selectNum\n      //       row['alCount' + tarCode] = row[tarCode]\n      //     }\n      //   })\n      // }\n      this.filterTbData = this.tbData.filter(row => {\n        return this.filterZero(row)\n      })\n      this.multipleSelection = []\n      this.$refs.xTable.clearCheckboxRow()\n      if (isMessage) {\n        this.$message({\n          message: '同步成功',\n          type: 'success'\n        })\n      }\n    },\n    // 批量分配提交\n    doAllocation() {\n      if (this.activeName === 'first') {\n        this.multipleSelection\n          .forEach((row, idx) => {\n            if (row.Can_Allocation_Count) {\n              const team = row.Allocation_Teams.find(v => v.Working_Team_Id === this.form.TeamGroup)\n              const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\n              row[tarCode] = Number(row[tarCode]) + row.allocatedTask\n              row.Can_Allocation_Count = row.Can_Allocation_Count - row.allocatedTask\n              row[this.getTaskCode(team.Working_Team_Name)] += row.allocatedTask\n              row[this.getTaskCode()] -= row.allocatedTask\n              row.allocatedTaskMax = row.Can_Allocation_Count\n              row.allocatedTask = row.Can_Allocation_Count\n              row['alCount' + tarCode] = row[tarCode]\n            }\n          })\n      } else {\n        this.multipleSelection\n          .forEach((row, idx) => {\n            const team = row.Allocation_Teams.find(v => v.Working_Team_Id === this.form.TeamGroup)\n            const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\n\n            const selectNum = Math.min(row[this.getRowCCode(row, 'alCount')], row[this.getRowCCode(row)])\n\n            row[this.getTaskCode(team.Working_Team_Name)] += selectNum\n            row[this.getTaskCode()] -= selectNum\n\n            row[tarCode] = Number(row[tarCode]) + selectNum\n            row[this.getRowCCode(row)] -= selectNum\n            row[this.getRowCCode(row, 'alCount')] -= selectNum\n\n            row['alCount' + tarCode] = row[tarCode]\n          })\n      }\n      this.filterTbData = this.tbData.filter(row => {\n        return this.filterZero(row)\n      })\n    },\n    resetForm(formName) {\n      this.$refs[formName].resetFields()\n      this.dialogVisible = false\n    },\n    filterTypeMethod({ option, row }) {\n      return row.Type.includes(option.data)\n    },\n    filterTypeRecoverMethod({ option }) {\n      option.data = ''\n    },\n    filterCodeMethod({ option, row }) {\n      console.log('option, row', option, row)\n      return row[this.isCom ? 'Comp_Code' : 'Part_Code'].includes(option.data)\n    },\n    filterCodeRecoverMethod({ option }) {\n      option.data = ''\n    },\n    getObjectTypeList() {\n      GetCompTypeTree({ professional: 'Steel' }).then((res) => {\n        if (res.IsSucceed) {\n          this.ObjectTypeList.data = res.Data\n          this.$nextTick((_) => {\n            this.$refs?.treeSelectObjectType?.treeDataUpdateFun(res.Data)\n          })\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.pagination-container {\n  padding: 0;\n  text-align: right;\n}\n\n::v-deep .el-card__body {\n  display: flex;\n  flex-direction: column;\n\n  .el-divider {\n    margin-top: 0;\n    margin-bottom: 16px;\n    background-color: #EEEEEE;\n  }\n\n  .tb-options {\n    margin-bottom: 16px;\n    display: flex;\n\n    .el-form-item--small.el-form-item {\n      margin-bottom: 0;\n    }\n    .el-input {\n      width: 250px;\n    }\n  }\n\n  footer {\n    text-align: inherit;\n    display: flex;\n    justify-content: space-between;\n  }\n}\n\n.tb-x {\n  flex: 1;\n  height: 0;\n  margin-bottom: 10px;\n  overflow: auto;\n}\n\n.topTitle {\n  height: 14px;\n  line-height: 14px;\n  font-size: 14px;\n  margin: 0 0 16px;\n\n  span {\n    display: inline-block;\n    width: 2px;\n    height: 14px;\n    background: #009dff;\n    vertical-align: middle;\n    margin-right: 6px;\n  }\n}\n\n.el-icon-edit {\n  cursor: pointer;\n}\n\n.cs-bottom {\n  position: relative;\n  height: 40px;\n  line-height: 40px;\n\n  .data-info {\n    position: absolute;\n    bottom: 0;\n\n    .info-x {\n      margin-right: 20px;\n    }\n  }\n}\n.my-input {\n  margin: 10px;\n  width: 140px;\n  height: 32px;\n}\n</style>\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\detail.vue?vue&type=style&index=0&id=a5aa7006&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\task-allocation\\v4\\detail.vue", "mtime": 1757909680922}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoucGFnaW5hdGlvbi1jb250YWluZXIgew0KICBwYWRkaW5nOiAwOw0KICB0ZXh0LWFsaWduOiByaWdodDsNCn0NCg0KOjp2LWRlZXAgLmVsLWNhcmRfX2JvZHkgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KDQogIC5lbC1kaXZpZGVyIHsNCiAgICBtYXJnaW4tdG9wOiAwOw0KICAgIG1hcmdpbi1ib3R0b206IDE2cHg7DQogICAgYmFja2dyb3VuZC1jb2xvcjogI0VFRUVFRTsNCiAgfQ0KDQogIC50Yi1vcHRpb25zIHsNCiAgICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICAgIGRpc3BsYXk6IGZsZXg7DQoNCiAgICAuZWwtZm9ybS1pdGVtLS1zbWFsbC5lbC1mb3JtLWl0ZW0gew0KICAgICAgbWFyZ2luLWJvdHRvbTogMDsNCiAgICB9DQogICAgLmVsLWlucHV0IHsNCiAgICAgIHdpZHRoOiAyNTBweDsNCiAgICB9DQogIH0NCg0KICBmb290ZXIgew0KICAgIHRleHQtYWxpZ246IGluaGVyaXQ7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIH0NCn0NCg0KLnRiLXggew0KICBmbGV4OiAxOw0KICBoZWlnaHQ6IDA7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQogIG92ZXJmbG93OiBhdXRvOw0KfQ0KDQoudG9wVGl0bGUgew0KICBoZWlnaHQ6IDE0cHg7DQogIGxpbmUtaGVpZ2h0OiAxNHB4Ow0KICBmb250LXNpemU6IDE0cHg7DQogIG1hcmdpbjogMCAwIDE2cHg7DQoNCiAgc3BhbiB7DQogICAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICAgIHdpZHRoOiAycHg7DQogICAgaGVpZ2h0OiAxNHB4Ow0KICAgIGJhY2tncm91bmQ6ICMwMDlkZmY7DQogICAgdmVydGljYWwtYWxpZ246IG1pZGRsZTsNCiAgICBtYXJnaW4tcmlnaHQ6IDZweDsNCiAgfQ0KfQ0KDQouZWwtaWNvbi1lZGl0IHsNCiAgY3Vyc29yOiBwb2ludGVyOw0KfQ0KDQouY3MtYm90dG9tIHsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICBoZWlnaHQ6IDQwcHg7DQogIGxpbmUtaGVpZ2h0OiA0MHB4Ow0KDQogIC5kYXRhLWluZm8gew0KICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICBib3R0b206IDA7DQoNCiAgICAuaW5mby14IHsNCiAgICAgIG1hcmdpbi1yaWdodDogMjBweDsNCiAgICB9DQogIH0NCn0NCi5teS1pbnB1dCB7DQogIG1hcmdpbjogMTBweDsNCiAgd2lkdGg6IDE0MHB4Ow0KICBoZWlnaHQ6IDMycHg7DQp9DQo="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4sCA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/PRO/plan-production/task-allocation/v4", "sourcesContent": ["<template>\r\n  <div v-loading=\"loading\" class=\"app-container abs100\">\r\n    <el-card class=\"box-card h100\">\r\n      <h4 class=\"topTitle\"><span />基本信息</h4>\r\n\r\n      <el-form\r\n        ref=\"formInline\"\r\n        label-position=\"right\"\r\n        label-width=\"90px\"\r\n        :inline=\"true\"\r\n        :model=\"formInline\"\r\n        class=\"demo-form-inline\"\r\n      >\r\n        <el-row>\r\n          <el-col :span=\"20\">\r\n            <el-row>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"排产单号:\" label-width=\"75px\" prop=\"Schduling_Code\">\r\n                  <span>{{ formInline.Schduling_Code }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"项目名称:\" prop=\"Project_Name\">\r\n                  <span>{{ formInline.Project_Name }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"区域:\" prop=\"Area_Name\">\r\n                  <span>{{ formInline.Area_Name }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"批次:\" prop=\"Installunit_Name\">\r\n                  <span>{{ formInline.Installunit_Name }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"任务数量:\" label-width=\"75px\" prop=\"Allocation_Count\">\r\n                  <span>{{ formInline.Allocation_Count }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"任务重量:\" prop=\"Allocation_Weight\">\r\n                  <span>{{ formInline.Allocation_Weight | filterNum }}(t)</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"已完成数量:\" prop=\"Finish_Count\">\r\n                  <span>{{ formInline.Finish_Count }}</span>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"已完成重量:\" prop=\"Finish_Weight\">\r\n                  <span>{{ formInline.Finish_Weight | filterNum }}(t)</span>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <qrcode-vue\r\n              :size=\"79\"\r\n              :value=\"formInline.Schduling_Code\"\r\n              class-name=\"qrcode\"\r\n              level=\"H\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <el-divider class=\"elDivder\" />\r\n      <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\r\n        <el-tab-pane label=\"待分配\" name=\"first\" />\r\n        <el-tab-pane\r\n          v-for=\"(element, index2) in workingTeam\"\r\n          :key=\"index2\"\r\n          :label=\"element.Working_Team_Name\"\r\n          :name=\"`${element.Working_Team_Name}$_$${element.Process_Code}`\"\r\n        />\r\n      </el-tabs>\r\n      <div class=\"tb-options\" :style=\"{'justify-content': !isView ? 'space-between' : 'end'}\">\r\n        <div>\r\n          <el-button v-if=\"!isView\" plain type=\"primary\" @click=\"reverseSelection()\">反选</el-button>\r\n          <el-button v-if=\"!isView\" type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"Batchallocation()\">批量分配</el-button>\r\n          <el-button v-if=\"!isView && activeName==='first'\" type=\"primary\" :disabled=\"!multipleSelection.length\" @click=\"preStepTaskAllocation()\">上道工序同步</el-button>\r\n        </div>\r\n        <el-form :inline=\"true\" :model=\"queryForm\" class=\"demo-form-inline\">\r\n          <el-form-item label=\"规格\">\r\n            <el-input v-model.trim=\"queryForm.Spec\" clearable placeholder=\"请输入\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"isCom\" :label=\"isCom ? '构件类型' :isUnitPart?'部件类型':'零件类型'\">\r\n            <el-tree-select\r\n              ref=\"treeSelectObjectType\"\r\n              v-model=\"searchType\"\r\n              style=\"width: 100%\"\r\n              class=\"cs-tree-x\"\r\n              :select-params=\"treeSelectParams\"\r\n              :tree-params=\"ObjectTypeList\"\r\n              value-key=\"Id\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item :label=\"isCom ? '构件编号' :isUnitPart?'部件名称':'零件名称'\">\r\n            <el-input v-if=\"isCom\" v-model=\"queryForm.Comp_Codes\" clearable placeholder=\"请输入(空格区分/多个搜索)\" />\r\n            <el-input v-else v-model=\"queryForm.Part_Code\" clearable placeholder=\"请输入(空格区分/多个搜索)\" />\r\n            <el-input v-if=\"isCom\" v-model.trim=\"queryForm.Comp_Codes_Vague\" style=\"margin-left: 10px;\" clearable placeholder=\"模糊查找(请输入关键字)\" />\r\n            <el-input v-else v-model.trim=\"queryForm.Part_Code_Vague\" style=\"margin-left: 10px;\" clearable placeholder=\"模糊查找(请输入关键字)\" />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" @click=\"filterData\">查询</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n      <div class=\"tb-x\">\r\n        <vxe-table\r\n          ref=\"xTable\"\r\n          :empty-render=\"{name: 'NotData'}\"\r\n          show-header-overflow\r\n          class=\"cs-vxe-table\"\r\n          :row-config=\"{ isCurrent: true, isHover: true }\"\r\n          align=\"left\"\r\n          height=\"100%\"\r\n          show-overflow\r\n          :loading=\"tbLoading\"\r\n          :checkbox-config=\"{checkField: 'checked',checkMethod }\"\r\n          stripe\r\n          size=\"medium\"\r\n          :edit-config=\"{\r\n            trigger: 'click',\r\n            mode: 'cell',\r\n            showIcon: !isView,\r\n            beforeEditMethod: activeCellMethod,\r\n          }\"\r\n          :data=\"filterTbData\"\r\n          resizable\r\n          :tooltip-config=\"{ enterable: true }\"\r\n          @checkbox-all=\"tbSelectChange\"\r\n          @checkbox-change=\"tbSelectChange\"\r\n        >\r\n          <vxe-column type=\"checkbox\" width=\"60\" fixed=\"left\" />\r\n          <template v-for=\"item in columns\">\r\n\r\n            <!--            <vxe-column :align=\"item.Align\"\r\n              v-if=\"item.Code==='Comp_Code'||item.Code==='Part_Code'\"\r\n              :key=\"item.Id\"\r\n              :filters=\"[{ data: '' }]\"\r\n              :filter-method=\"filterCodeMethod\"\r\n              :filter-recover-method=\"filterCodeRecoverMethod\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #filter=\"{ $panel, column }\">\r\n                <template v-for=\"(option, index) in column.filters\">\r\n                  <input :key=\"index\" v-model=\"option.data\" class=\"my-input\" type=\"type\" placeholder=\"按回车确认筛选\" @input=\"$panel.changeOption($event, !!option.data, option)\" @keyup.enter=\"$panel.confirmFilter()\">\r\n                </template>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column :align=\"item.Align\"\r\n              v-else-if=\"item.Code==='Type'\"\r\n              :key=\"item.Id\"\r\n              :filters=\"[{ data: '' }]\"\r\n              :filter-method=\"filterTypeMethod\"\r\n              :filter-recover-method=\"filterTypeRecoverMethod\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #filter=\"{ $panel, column }\">\r\n                <template v-for=\"(option, index) in column.filters\">\r\n                  <input :key=\"index\" v-model=\"option.data\" class=\"my-input\" type=\"type\" placeholder=\"按回车确认筛选\" @input=\"$panel.changeOption($event, !!option.data, option)\" @keyup.enter=\"$panel.confirmFilter()\">\r\n                </template>\r\n              </template>\r\n            </vxe-column>-->\r\n            <vxe-column\r\n              v-if=\"item.Code==='Comp_Code'|| item.Code==='Part_Code'\"\r\n              :key=\"item.Id\"\r\n              :align=\"item.Align\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :visible=\"item.visible\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                <el-tag v-if=\"!!row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                <span>{{ row[item.Code] }}</span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column\r\n              v-else-if=\"item.Code==='Schduled_Count'\"\r\n              :key=\"`SchduledCount${item.Id}`\"\r\n              :align=\"item.Align\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :visible=\"item.visible\"\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n            >\r\n              <template #default=\"{ row }\">\r\n                {{ activeName === 'first' ?'':row[getTaskCode()] }}\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column\r\n              v-else\r\n              :key=\"`Default${item.Id}`\"\r\n              :align=\"item.Align\"\r\n              :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n              :visible=\"item.visible\"\r\n              show-overflow=\"tooltip\"\r\n              sortable\r\n              :field=\"item.Code\"\r\n              :title=\"item.Display_Name\"\r\n              :min-width=\"item.Width\"\r\n            />\r\n          </template>\r\n\r\n          <vxe-column\r\n            v-for=\"(element, index2) in workingTeamColumn\"\r\n            :key=\"index2\"\r\n            :align=\"element.Align\"\r\n            :visible=\"element.visible\"\r\n            fixed=\"right\"\r\n            :field=\"`${element.Working_Team_Name}$_$${element.Process_Code}`\"\r\n            title=\"可分配数量\"\r\n            sortable\r\n            min-width=\"170\"\r\n          >\r\n            <template #edit=\"{ row }\">\r\n              <vxe-input\r\n                v-model.number=\"\r\n                  row[\r\n                    getRowUnique(\r\n                      row.uuid,\r\n                      element.Process_Code,\r\n                      element.Working_Team_Id\r\n                    )\r\n                  ]\r\n                \"\r\n                type=\"integer\"\r\n                :min=\"0\"\r\n                :max=\"\r\n                  row[\r\n                    getRowUniqueMax(\r\n                      row.uuid,\r\n                      element.Process_Code,\r\n                      element.Working_Team_Id\r\n                    )\r\n                  ]\r\n                \"\r\n                @change=\"\r\n                  inputChange(\r\n                    row,\r\n                    element.Process_Code,\r\n                    element.Working_Team_Id\r\n                  )\r\n                \"\r\n              />\r\n            </template>\r\n            <template #default=\"{ row }\">\r\n              <template\r\n                v-if=\"\r\n                  checkPermissionTeam(row.Technology_Path, element.Process_Code)\r\n                \"\r\n              >\r\n                {{\r\n                  row[\r\n                    getRowUnique(\r\n                      row.uuid,\r\n                      element.Process_Code,\r\n                      element.Working_Team_Id\r\n                    )\r\n                  ]\r\n                }}\r\n              </template>\r\n              <template v-else> -</template>\r\n            </template>\r\n          </vxe-column>\r\n\r\n          <vxe-column\r\n            :key=\"activeName\"\r\n            align=\"left\"\r\n            :edit-render=\"{}\"\r\n            field=\"AllocatedCount\"\r\n            title=\"分配数量\"\r\n            sortable\r\n            fixed=\"right\"\r\n            min-width=\"180\"\r\n          >\r\n            <template #edit=\"{ row }\">\r\n              <vxe-input\r\n                :key=\"activeName\"\r\n                v-model.number=\"row[getRowCCode(row,'alCount')]\"\r\n                type=\"integer\"\r\n                :min=\"0\"\r\n                :max=\"row[getRowCCode(row,'','Max')]\"\r\n              />\r\n            </template>\r\n            <template #default=\"{ row }\">\r\n              {{ row[getRowCCode(row,'alCount')] | displayValue }}\r\n            </template>\r\n          </vxe-column>\r\n        </vxe-table>\r\n      </div>\r\n      <footer>\r\n        <div class=\"data-info\">\r\n          <el-tag size=\"medium\" class=\"info-x\">已选{{ multipleSelection.length }}条数据</el-tag>\r\n          <el-tag v-if=\"tipLabel\" size=\"medium\" class=\"info-x\">{{ tipLabel }}</el-tag>\r\n        </div>\r\n        <div>\r\n          <el-button @click=\"handleClose\">取消 </el-button>\r\n          <el-button\r\n            v-if=\"!isView\"\r\n            type=\"primary\"\r\n            :loading=\"loading\"\r\n            @click=\"handleSubmit\"\r\n          >提交</el-button>\r\n        </div>\r\n      </footer>\r\n    </el-card>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      title=\"批量分配\"\r\n      class=\"plm-custom-dialog\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"30%\"\r\n      @close=\"handleDialog\"\r\n    >\r\n      <el-form\r\n        ref=\"form\"\r\n        :model=\"form\"\r\n        :rules=\"rules\"\r\n        label-width=\"80px\"\r\n        class=\"demo-ruleForm\"\r\n      >\r\n        <el-form-item label=\"选择班组\" prop=\"TeamGroup\">\r\n          <el-select\r\n            v-model=\"form.TeamGroup\"\r\n            class=\"w100\"\r\n            placeholder=\"请选择\"\r\n            filterable\r\n            clearable\r\n          >\r\n            <el-option\r\n              v-for=\"item in workingTeam\"\r\n              :key=\"item.Working_Team_Id\"\r\n              :label=\"item.Working_Team_Name\"\r\n              :value=\"item.Working_Team_Id\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item style=\"text-align: right\">\r\n          <el-button @click=\"resetForm('form')\">取 消</el-button>\r\n          <el-button\r\n            type=\"primary\"\r\n            @click=\"submitForm('form');resetForm('form')\"\r\n          >确 定</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-dialog>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      title=\"提示\"\r\n      class=\"plm-custom-dialog\"\r\n      :visible.sync=\"dialogTipsVisible\"\r\n      width=\"450px\"\r\n      @close=\"handleDialog\"\r\n    >\r\n      <div style=\"text-align: center; font-size: 16px;\">部分{{ isCom ? '构件' : isUnitPart ? '部件' : '零件' }}与上道工序加工班组不同，请手动分配</div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogTipsVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"dialogTipsVisible = false\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { FIX_COLUMN } from '@/views/PRO/plan-production/schedule-production/constant'\r\nimport {\r\n  AdjustTeamProcessAllocation,\r\n  GetTeamProcessAllocation,\r\n  AdjustPartTeamProcessAllocation,\r\n  AdjustSubAssemblyTeamProcessAllocation,\r\n  GetStopList,\r\n  GetPreStepTaskAllocation\r\n} from '@/api/PRO/production-task'\r\nimport QrcodeVue from 'qrcode.vue'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport numeral from 'numeral'\r\nimport { closeTagView, deepClone } from '@/utils'\r\nimport { GetCompTypeTree } from '@/api/PRO/professionalType'\r\n\r\nconst SPLIT_SYMBOL = '$_$'\r\nexport default {\r\n  components: {\r\n    QrcodeVue\r\n  },\r\n  filters: {\r\n    filterNum(value) {\r\n      return numeral(value).divide(1000).format('0.[00]')\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        clearable: true\r\n      },\r\n      ObjectTypeList: {\r\n        // 构件类型\r\n        'check-strictly': true,\r\n        'default-expand-all': true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      },\r\n      tbLoading: false,\r\n      loading: false,\r\n      activeName: 'first',\r\n      tipLabel: '',\r\n      tbData: [],\r\n      filterTbData: [],\r\n      multipleSelection: [],\r\n      columns: [],\r\n      workingTeam: [],\r\n      workingTeamColumn: [],\r\n      formInline: {},\r\n      pg_type: '',\r\n      searchType: '',\r\n      type: '',\r\n      queryForm: {\r\n        Comp_Codes: '',\r\n        Part_Code: '',\r\n        Spec: '',\r\n        Comp_Codes_Vague: '',\r\n        Part_Code_Vague: ''\r\n      },\r\n      dialogVisible: false,\r\n      dialogTipsVisible: false,\r\n      form: {\r\n        TeamGroup: '' // 班组\r\n      },\r\n\r\n      rules: {\r\n        TeamGroup: [\r\n          { required: true, message: '请输入班组名称', trigger: 'change' }\r\n        ]\r\n      },\r\n      Is_Workshop_Enabled: false,\r\n      Working_Process_Id: ''\r\n    }\r\n  },\r\n  computed: {\r\n    isView() {\r\n      return this.type === 'view'\r\n    },\r\n    isCom() {\r\n      return this.pg_type === 'com'\r\n    },\r\n    isUnitPart() {\r\n      return this.pg_type === 'unitPart'\r\n    }\r\n  },\r\n\r\n  async mounted() {\r\n    try {\r\n      const rowInfo = JSON.parse(decodeURIComponent(this.$route.query.other))\r\n      console.log('rowInfo', rowInfo)\r\n      this.formInline = Object.assign({}, this.formInline, rowInfo)\r\n      this.pg_type = this.$route.query.pg_type\r\n      this.bomLevel = this.$route.query.bomLevel\r\n      this.type = this.$route.query.type\r\n      this.Is_Workshop_Enabled = this.$route.query.Is_Workshop_Enabled\r\n      await this.getTableConfig(this.isUnitPart ? 'PROTaskUnitAllocationChange' : 'PROTaskAllocationChange')\r\n      if (this.isCom) {\r\n        const idx = this.columns.findIndex(item => item.Code === 'Part_Code')\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n        const idx2 = this.columns.findIndex(item => item.Code === 'Component_Code')\r\n        idx2 !== -1 && this.columns.splice(idx2, 1)\r\n      } else {\r\n        const idx = this.columns.findIndex(item => item.Code === 'Comp_Code')\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n        const idx2 = this.columns.findIndex(item => item.Code === 'Type')\r\n        idx2 !== -1 && this.columns.splice(idx2, 1)\r\n      }\r\n      if (!this.Is_Workshop_Enabled) {\r\n        const idx3 = this.columns.findIndex(item => item.Code === 'Workshop_Name')\r\n        idx3 !== -1 && this.columns.splice(idx3, 1)\r\n      }\r\n      this.getObjectTypeList()\r\n      this.fetchData()\r\n    } catch (e) {\r\n      this.$message({\r\n        message: '参数错误,请重新操作',\r\n        type: 'error'\r\n      })\r\n    }\r\n  },\r\n  methods: {\r\n    getRowCCode(row, prefix = '', suffix = '') {\r\n      if (this.activeName === 'first') {\r\n        return 'allocatedTask' + suffix\r\n      } else {\r\n        const arr = this.activeName.split(SPLIT_SYMBOL)\r\n        const team = this.workingTeam.find(v => v.Working_Team_Name === arr[0])\r\n        const u = this.getRowUnique(row.uuid, row.Process_Code, team.Working_Team_Id)\r\n        if (suffix === 'Max') {\r\n          return u\r\n        } else {\r\n          return prefix + u + suffix\r\n        }\r\n      }\r\n    },\r\n    handleClick(val) {\r\n      console.log('handleClick', val)\r\n      if (val.name === 'first') {\r\n        const c1 = this.$refs.xTable.getColumnByField('Schduled_Count')\r\n        const c2 = this.$refs.xTable.getColumnByField('Can_Allocation_Count')\r\n        c1.visible = false\r\n        c2.visible = true\r\n      } else {\r\n        const c2 = this.$refs.xTable.getColumnByField('Schduled_Count')\r\n        const c3 = this.$refs.xTable.getColumnByField('Can_Allocation_Count')\r\n        c2.visible = true\r\n        c3.visible = false\r\n      }\r\n\r\n      this.workingTeam.forEach((element, idx) => {\r\n        const codes = `${element.Working_Team_Name}$_$${element.Process_Code}`\r\n\r\n        const c = this.$refs.xTable.getColumnByField(codes)\r\n\r\n        c.visible = codes === val.name\r\n      })\r\n      this.$refs.xTable.refreshColumn()\r\n\r\n      this.filterTbData = this.tbData.filter(row => {\r\n        return this.filterZero(row)\r\n      })\r\n\r\n      this.multipleSelection = []\r\n      this.$refs.xTable.clearCheckboxRow()\r\n    },\r\n    filterZero(row) {\r\n      if (this.activeName === 'first') {\r\n        return row.allocatedTask > 0\r\n      } else {\r\n        const arr = this.activeName.split(SPLIT_SYMBOL)\r\n        const team = this.workingTeam.find(v => v.Working_Team_Name === arr[0])\r\n        const code = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\r\n        return row[code] > 0\r\n      }\r\n    },\r\n    fetchData() {\r\n      const Comp_Codes = !this.queryForm.Comp_Codes ? [] : this.queryForm.Comp_Codes.trim().split(' ')\r\n      const Part_Code = !this.queryForm.Part_Code ? [] : this.queryForm.Part_Code.trim().split(' ')\r\n      let Process_Type = 2\r\n      Process_Type = this.isCom ? 2 : this.isUnitPart ? 3 : 1\r\n      this.tbLoading = true\r\n      GetTeamProcessAllocation({\r\n        Page: 1,\r\n        PageSize: -1,\r\n        Step: this.formInline.Step,\r\n        Process_Type,\r\n        Bom_Level: this.bomLevel,\r\n        Schduling_Code: this.formInline.Schduling_Code,\r\n        Process_Code: this.formInline.Process_Code,\r\n        Workshop_Name: this.formInline.Workshop_Name,\r\n        Area_Id: this.formInline.Area_Id,\r\n        InstallUnit_Id: this.formInline.InstallUnit_Id,\r\n        Comp_Codes,\r\n        Part_Code\r\n      }).then(async(res) => {\r\n        if (res.IsSucceed) {\r\n          const { Schduling_Plan, Schduling_Comps } = res.Data\r\n          this.planInfoTemp = Schduling_Plan\r\n          await this.getStopList(Schduling_Comps)\r\n          this.initTbData(Schduling_Comps)\r\n          this.Working_Process_Id = res.Data.Working_Process_Id\r\n\r\n          if (Schduling_Comps.length) {\r\n            this.workingTeam = Schduling_Comps[0].Allocation_Teams\r\n            const _kk = this.workingTeam.map(v => {\r\n              v.visible = false\r\n              return v\r\n            })\r\n            this.workingTeamColumn = deepClone(_kk)\r\n          }\r\n          console.log(' this.tbData', this.tbData)\r\n          this.filterData()\r\n          console.log('filterTbData', this.filterTbData)\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    checkMethod({ row }) {\r\n      return !row.stopFlag\r\n    },\r\n    async getStopList(list) {\r\n      const key = 'Id'\r\n      const submitObj = list.map(item => {\r\n        return {\r\n          Id: item[key],\r\n          Bom_Level: this.bomLevel,\r\n          Type: this.isCom ? 2 : this.isUnitPart ? 3 : 1 // 1：零件，3：部件，2：构件\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = !!item.Is_Stop\r\n          })\r\n          list.forEach(row => {\r\n            if (stopMap[row[key]]) {\r\n              this.$set(row, 'stopFlag', stopMap[row[key]])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    filterData() {\r\n      console.log('searchType', this.searchType)\r\n      this.multipleSelection = []\r\n      this.$refs.xTable.clearCheckboxRow()\r\n      const code = this.isCom ? 'Comp_Code' : 'Part_Code'\r\n      const queryCode = this.isCom ? 'Comp_Codes' : 'Part_Code'\r\n      const codeList = this.queryForm[queryCode].split(' ').filter(v => !!v)\r\n\r\n      const queryCodeVague = this.isCom ? 'Comp_Codes_Vague' : 'Part_Code_Vague'\r\n      const codeListVague = this.queryForm[queryCodeVague]\r\n\r\n      const searchTbData = this.tbData.filter(v => {\r\n        if (!codeList.length && codeListVague === '') {\r\n          return true\r\n        } else {\r\n          return codeList.includes(v[code])\r\n        }\r\n      })\r\n\r\n      const vagueData = searchTbData.length > 0 && codeListVague === '' ? searchTbData : searchTbData.length === 0 && codeListVague === '' ? searchTbData : this.tbData\r\n      const searchVagueTbData = vagueData.filter(v => {\r\n        if (codeListVague === '' && !codeList.length) {\r\n          return true\r\n        } else {\r\n          return v[code].includes(codeListVague)\r\n        }\r\n      })\r\n\r\n      // 合并两个数组\r\n      const mergedArray = searchTbData.concat(searchVagueTbData)\r\n      // 根据 Schduling_Detail_Id 进行去重\r\n      const uniqueArray = mergedArray.reduce((acc, current) => {\r\n        const existingObject = acc.find(item => item.Schduling_Detail_Id === current.Schduling_Detail_Id)\r\n        if (!existingObject) {\r\n          acc.push(current)\r\n        }\r\n        return acc\r\n      }, [])\r\n\r\n      this.filterTbData = uniqueArray.filter(v => {\r\n        if (!this.searchType) return true\r\n        return this.searchType === v.Type\r\n      }).filter(v => {\r\n        if (!this.queryForm.Spec) return true\r\n        return (v.Spec || '').includes(this.queryForm.Spec)\r\n      }).filter(row => {\r\n        return this.filterZero(row)\r\n      })\r\n    },\r\n    initTbData(list, teamKey = 'Allocation_Teams') {\r\n      this.tbData = list.map(row => {\r\n        const processList = row.Technology_Path?.split('/') || []\r\n        // 已uuid作为row唯一值；\r\n        // uuid+工序+班组为输入框值\r\n        row.uuid = uuidv4()\r\n        const newData = row[teamKey].filter((r) => processList.findIndex((p) => r.Process_Code === p) !== -1)\r\n        row.defaultCan_Allocation_Count = row.Can_Allocation_Count\r\n        let _inputNum = 0\r\n        newData.forEach((ele, index) => {\r\n          const code = this.getRowUnique(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n          const max = this.getRowUniqueMax(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n          row[code] = ele.Count\r\n          this.$set(row, 'alCount' + code, ele.Count)\r\n          row[max] = 0\r\n          _inputNum += ele.Count\r\n          this.$set(row, 'totalTask' + ele.Working_Team_Name, ele.Total_Receive_Count + ele.Count)\r\n        })\r\n\r\n        this.$set(row, 'allocatedTask', row.defaultCan_Allocation_Count - _inputNum)\r\n        row.Can_Allocation_Count = row.allocatedTask\r\n        this.$set(row, 'allocatedTaskMax', row.Can_Allocation_Count)\r\n\r\n        this.setInputMax(row)\r\n\r\n        row.checked = false\r\n        return row\r\n      })\r\n    },\r\n    inputChange(row) {\r\n      this.setInputMax(row)\r\n    },\r\n    setInputMax(row) {\r\n      let _inputNum = 0\r\n      const inputValuesKeys = Object.keys(row)\r\n        .filter(v => !v.endsWith('max') && v.startsWith(row.uuid) && v.length > row.uuid.length)\r\n      inputValuesKeys.forEach((val) => {\r\n        const curCode = val.split(SPLIT_SYMBOL)[1]\r\n        const otherTotal = inputValuesKeys.filter(x => {\r\n          const code = x.split(SPLIT_SYMBOL)[1]\r\n          return x !== val && code === curCode\r\n        }).reduce((acc, item) => {\r\n          return acc + numeral(row[item]).value()\r\n        }, 0)\r\n        row[val + SPLIT_SYMBOL + 'max'] = row.Schduled_Count - otherTotal\r\n        _inputNum += +row[val]\r\n      })\r\n      // row.allocatedCount = row.defaultCan_Allocation_Count - _inputNum\r\n      // row.Can_Allocation_Count = row.allocatedCount\r\n    },\r\n    checkPermissionTeam(processStr, processCode) {\r\n      if (!processStr) return false\r\n      const list = processStr?.split('/') || []\r\n      return !!list.some(v => v === processCode)\r\n    },\r\n\r\n    getSubmitTbInfo(tbData = this.tbData) {\r\n      // 处理上传的数据\r\n      const tableData = JSON.parse(JSON.stringify(tbData))\r\n      for (let i = 0; i < tableData.length; i++) {\r\n        const element = tableData[i]\r\n        const list = []\r\n        if (!element.Technology_Path) {\r\n          this.$message({\r\n            message: '工序不能为空',\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        const processList = Array.from(new Set(element.Technology_Path.split('/')))\r\n        processList.forEach(code => {\r\n          const groups = this.workingTeam.filter(v => v.Process_Code === code)\r\n\r\n          const groupsList = groups.map((group, index) => {\r\n            const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)\r\n            const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)\r\n            const obj = {\r\n              Comp_Code: element.Comp_Code,\r\n              Again_Count: +element[uCode],\r\n              Part_Code: this.isCom ? null : element.Part_Code,\r\n              Process_Code: code,\r\n              Technology_Path: element.Technology_Path,\r\n              Working_Team_Id: group.Working_Team_Id,\r\n              Working_Team_Name: group.Working_Team_Name,\r\n              Team_Task_Id: element.Allocation_Teams.find((item) => item.Working_Team_Id === group.Working_Team_Id).Team_Task_Id\r\n            }\r\n            delete element['alCount' + uCode]\r\n            delete element['allocatedTask']\r\n            delete element['allocatedTaskMax']\r\n            delete element[uCode]\r\n            delete element[uMax]\r\n            return obj\r\n          })\r\n          list.push(...groupsList)\r\n        })\r\n        console.log(list)\r\n        delete element['uuid']\r\n        delete element['puuid']\r\n        element.Allocation_Teams = list\r\n      }\r\n      return { tableData, status: true }\r\n    },\r\n    handleSubmit() {\r\n      const { tableData, status } = this.getSubmitTbInfo()\r\n      if (!status) return\r\n      this.$confirm('是否提交当前数据?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.loading = true\r\n        const obj = {\r\n          Schduling_Plan: this.planInfoTemp,\r\n          Schduling_Comps: tableData\r\n        }\r\n        const Partobj = {\r\n          Schduling_Plan: this.planInfoTemp,\r\n          SarePartsModel: tableData\r\n        }\r\n        const requestFn = this.isCom ? AdjustTeamProcessAllocation : this.isUnitPart ? AdjustSubAssemblyTeamProcessAllocation : AdjustPartTeamProcessAllocation\r\n        console.log('obj', obj)\r\n        requestFn(this.isCom ? obj : Partobj).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '操作成功',\r\n              type: 'success'\r\n            })\r\n            this.handleClose()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.loading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.closeView()\r\n    },\r\n    closeView() {\r\n      closeTagView(this.$store, this.$route)\r\n    },\r\n    tbSelectChange(array) {\r\n      this.multipleSelection = array.records\r\n      console.log(this.tbData)\r\n    },\r\n    getTaskCode(name = '') {\r\n      if (name) return 'totalTask' + name\r\n      return 'totalTask' + this.activeName.split(SPLIT_SYMBOL)[0]\r\n    },\r\n    // 反选\r\n    reverseSelection() {\r\n      const list = this.$refs.xTable.getCheckboxRecords().filter(item => !item.stopFlag)\r\n      const unSelectList = this.filterTbData.filter(item => !list.includes(item) && !item.stopFlag)\r\n      this.$refs.xTable.setCheckboxRow(list, false)\r\n      this.$refs.xTable.setCheckboxRow(unSelectList, true)\r\n      this.multipleSelection = this.$refs.xTable.getCheckboxRecords()\r\n    },\r\n    async getTableConfig(code) {\r\n      await GetGridByCode({\r\n        code\r\n      }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n          const list = Data.ColumnList || []\r\n          this.columns = list.filter(v => v.Is_Display).map(item => {\r\n            if (FIX_COLUMN.includes(item.Code)) {\r\n              item.fixed = 'left'\r\n            }\r\n            if (item.Code === 'Schduled_Count') {\r\n              item.visible = false\r\n            }\r\n            return item\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    activeCellMethod({ row, column, columnIndex }) {\r\n      if (this.isView) return false\r\n      if (column.field === 'AllocatedCount') return true\r\n      const processCode = column.field?.split('$_$')[1]\r\n      return this.checkPermissionTeam(row.Technology_Path, processCode)\r\n    },\r\n    getRowUnique(uuid, processCode, workingId) {\r\n      return `${uuid}${SPLIT_SYMBOL}${processCode}${SPLIT_SYMBOL}${workingId}`\r\n    },\r\n    getRowUniqueMax(uuid, processCode, workingId) {\r\n      return this.getRowUnique(uuid, processCode, workingId) + `${SPLIT_SYMBOL}max`\r\n    },\r\n    // 批量分配\r\n    Batchallocation() {\r\n      this.dialogVisible = true\r\n      console.log(this.workingTeam)\r\n    },\r\n    // 上道工序分配\r\n    preStepTaskAllocation() {\r\n      const Schduling_Detail_Ids = this.multipleSelection.map(item => item.Schduling_Detail_Id)\r\n      const Working_Process_Code = this.multipleSelection[0].Process_Code\r\n      GetPreStepTaskAllocation({\r\n        Schduling_Detail_Ids,\r\n        Working_Process_Code,\r\n        Working_Process_Id: this.Working_Process_Id,\r\n        Process_Type: this.isCom ? 2 : this.isUnitPart ? 3 : 1,\r\n        Bom_Level: this.bomLevel\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (res.Data.length === 0) {\r\n            this.dialogTipsVisible = true\r\n            return\r\n          }\r\n          this.preDoAllocation(res.Data)\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleDialog() {\r\n      this.dialogVisible = false\r\n      // this.multipleSelection = []\r\n    },\r\n    handelData() {\r\n      this.multipleSelection.forEach((item) =>\r\n        item.Allocation_Teams.forEach((v) => {\r\n          if (v.Working_Team_Id === this.form.TeamGroup) {\r\n            v.Count = item.Can_Allocation_Count\r\n          } else {\r\n            v.Count = 0\r\n          }\r\n        })\r\n      )\r\n      // const tableData = JSON.parse(JSON.stringify(this.multipleSelection))\r\n      for (let i = 0; i < this.multipleSelection.length; i++) {\r\n        const element = this.multipleSelection[i]\r\n        const processList = Array.from(new Set(element.Technology_Path.split('/')))\r\n        processList.forEach(code => {\r\n          const groups = this.workingTeam.filter(v => v.Process_Code === code)\r\n          groups.forEach(group => {\r\n            const uniCode = this.getRowUnique(element.uuid, code, this.form.TeamGroup)\r\n            const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)\r\n            const uniMax = this.getRowUniqueMax(element.uuid, code, this.form.TeamGroup)\r\n            const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)\r\n\r\n            if (uniCode === uCode && uniMax === uMax) {\r\n              element[uCode] = element['Can_Allocation_Count']\r\n              element[uMax] = element['Can_Allocation_Count']\r\n            } else {\r\n              element[uCode] = 0\r\n              element[uMax] = 0\r\n            }\r\n          })\r\n        })\r\n      }\r\n      console.log(this.multipleSelection)\r\n      for (let i = 0; i < this.tbData.length; i++) {\r\n        for (let k = 0; k < this.multipleSelection.length; k++) {\r\n          if (this.tbData[i].uuid === this.multipleSelection[k].uuid) {\r\n            this.$nextTick((_) => {\r\n              this.tbData[i] = this.multipleSelection[k]\r\n            })\r\n          }\r\n        }\r\n      }\r\n    },\r\n    submitForm(formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          // this.handelData()\r\n          this.doAllocation()\r\n          // this.workingTeam.find(v=> v.Working_Team_Id === this.form.TeamGroup).count\r\n          this.handleDialog()\r\n          console.log(this.tbData)\r\n          this.multipleSelection = []\r\n          this.$refs.xTable.clearCheckboxRow()\r\n        } else {\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    // 上道工序分配\r\n    preDoAllocation(preProcessData) {\r\n      const preProcessDataMap = new Map()\r\n      preProcessData.forEach(item => {\r\n        const key = `${item.Schduling_Detail_Id}_${item.Working_Team_Id}`\r\n        preProcessDataMap.set(key, item.Current_Task_Count)\r\n      })\r\n\r\n      const allocateForTeam = (row, team, amount) => {\r\n        const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\r\n        const bKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\r\n        const currentTaskCount = preProcessDataMap.get(bKey) || 0\r\n        const allocated = Math.min(amount, currentTaskCount, row.Can_Allocation_Count)\r\n\r\n        row[tarCode] = (Number(row[tarCode]) || 0) + allocated\r\n        row.Can_Allocation_Count -= allocated\r\n        row[this.getTaskCode(team.Working_Team_Name)] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + allocated\r\n        row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - allocated\r\n        row['alCount' + tarCode] = row[tarCode]\r\n\r\n        return allocated\r\n      }\r\n      let isMessage = true\r\n      this.multipleSelection.forEach(row => {\r\n        if (!row.Can_Allocation_Count && this.activeName === 'first') return\r\n\r\n        const eligibleTeams = row.Allocation_Teams.filter(team =>\r\n          preProcessDataMap.has(`${row.Schduling_Detail_Id}_${team.Working_Team_Id}`)\r\n        )\r\n\r\n        if (eligibleTeams.length === 0) return\r\n        if (this.activeName === 'first') {\r\n          let IsAllNo = 0\r\n          const totalAvailable = eligibleTeams.reduce((sum, team) => {\r\n            const bKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\r\n            return sum + (preProcessDataMap.get(bKey) || 0)\r\n          }, 0)\r\n\r\n          if (row.allocatedTaskMax < totalAvailable) {\r\n            IsAllNo++\r\n            if (IsAllNo === this.multipleSelection.length) {\r\n              isMessage = false\r\n              this.dialogTipsVisible = true\r\n            }\r\n            return\r\n          }\r\n\r\n          let remaining = Math.min(row.allocatedTask, row.Can_Allocation_Count)\r\n          const perTeamAllocation = Math.floor(remaining / eligibleTeams.length)\r\n\r\n          eligibleTeams.forEach((team, index) => {\r\n            if (remaining <= 0) return\r\n\r\n            const allocateAmount = index === eligibleTeams.length - 1\r\n              ? remaining\r\n              : Math.min(perTeamAllocation, remaining)\r\n\r\n            remaining -= allocateForTeam(row, team, allocateAmount)\r\n          })\r\n\r\n          row.allocatedTaskMax = row.Can_Allocation_Count\r\n          row.allocatedTask = row.Can_Allocation_Count\r\n          if (IsAllNo === this.multipleSelection.length) {\r\n            isMessage = false\r\n            this.dialogTipsVisible = true\r\n            return\r\n          }\r\n        } else {\r\n          eligibleTeams.forEach(team => {\r\n            const bKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\r\n            const currentTaskCount = preProcessDataMap.get(bKey) || 0\r\n\r\n            if (row[this.getRowCCode(row)] < currentTaskCount) {\r\n              return\r\n            }\r\n\r\n            const selectNum = Math.min(\r\n              row[this.getRowCCode(row, 'alCount')] || 0,\r\n              row[this.getRowCCode(row)] || 0,\r\n              currentTaskCount\r\n            )\r\n\r\n            if (selectNum > 0) {\r\n              const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\r\n              row[this.getTaskCode(team.Working_Team_Name)] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + selectNum\r\n              row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - selectNum\r\n              row[tarCode] = (Number(row[tarCode]) || 0) + selectNum\r\n              row[this.getRowCCode(row)] = (row[this.getRowCCode(row)] || 0) - selectNum\r\n              row[this.getRowCCode(row, 'alCount')] = (row[this.getRowCCode(row, 'alCount')] || 0) - selectNum\r\n              row['alCount' + tarCode] = row[tarCode]\r\n            }\r\n          })\r\n        }\r\n      })\r\n      // if (this.activeName === 'first') {\r\n      //   let IsAllNo = 0\r\n      //   this.multipleSelection.forEach((row, idx) => {\r\n      //     if (row.Can_Allocation_Count) {\r\n      //       const validTeams = row.Allocation_Teams.filter(team => {\r\n      //         const key = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\r\n      //         return preProcessDataMap.has(key)\r\n      //       })\r\n      //       if (validTeams.length > 0) {\r\n      //         const team = validTeams[0]\r\n      //         const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\r\n\r\n      //         const preProcessKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\r\n      //         const currentTaskCount = preProcessDataMap.get(preProcessKey) || 0\r\n\r\n      //         if (currentTaskCount > row.allocatedTaskMax) {\r\n      //           IsAllNo++\r\n      //           return\r\n      //         }\r\n      //         const allocated = Math.min(row.allocatedTask, currentTaskCount)\r\n\r\n      //         row[tarCode] = Number(row[tarCode] || 0) + allocated\r\n      //         row.Can_Allocation_Count = row.Can_Allocation_Count - allocated\r\n      //         row[this.getTaskCode(team.Working_Team_Name) || 0] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + allocated\r\n      //         row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - allocated\r\n      //         row.allocatedTaskMax = row.Can_Allocation_Count\r\n      //         row.allocatedTask = row.Can_Allocation_Count\r\n      //         row['alCount' + tarCode] = row[tarCode]\r\n      //       }\r\n      //     }\r\n      //   })\r\n      //   if (IsAllNo === this.multipleSelection.length) {\r\n      //     this.dialogTipsVisible = true\r\n      //     return\r\n      //   }\r\n      // } else {\r\n      //   this.multipleSelection.forEach((row, idx) => {\r\n      //     const validTeams = row.Allocation_Teams.filter(team => {\r\n      //       const key = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\r\n      //       return preProcessDataMap.has(key)\r\n      //     })\r\n      //     if (validTeams.length > 0) {\r\n      //       const team = validTeams[0]\r\n      //       const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\r\n\r\n      //       const preProcessKey = `${row.Schduling_Detail_Id}_${team.Working_Team_Id}`\r\n      //       const currentTaskCount = preProcessDataMap.get(preProcessKey) || 0\r\n\r\n      //       const selectNum = Math.min(\r\n      //         row[this.getRowCCode(row, 'alCount')] || 0,\r\n      //         row[this.getRowCCode(row)] || 0,\r\n      //         currentTaskCount\r\n      //       )\r\n\r\n      //       row[this.getTaskCode(team.Working_Team_Name)] = (row[this.getTaskCode(team.Working_Team_Name)] || 0) + selectNum\r\n      //       row[this.getTaskCode()] = (row[this.getTaskCode()] || 0) - selectNum\r\n      //       row[tarCode] = (Number(row[tarCode]) || 0) + selectNum\r\n      //       row[this.getRowCCode(row)] = (row[this.getRowCCode(row)] || 0) - selectNum\r\n      //       row[this.getRowCCode(row, 'alCount')] = (row[this.getRowCCode(row, 'alCount')] || 0) - selectNum\r\n      //       row['alCount' + tarCode] = row[tarCode]\r\n      //     }\r\n      //   })\r\n      // }\r\n      this.filterTbData = this.tbData.filter(row => {\r\n        return this.filterZero(row)\r\n      })\r\n      this.multipleSelection = []\r\n      this.$refs.xTable.clearCheckboxRow()\r\n      if (isMessage) {\r\n        this.$message({\r\n          message: '同步成功',\r\n          type: 'success'\r\n        })\r\n      }\r\n    },\r\n    // 批量分配提交\r\n    doAllocation() {\r\n      if (this.activeName === 'first') {\r\n        this.multipleSelection\r\n          .forEach((row, idx) => {\r\n            if (row.Can_Allocation_Count) {\r\n              const team = row.Allocation_Teams.find(v => v.Working_Team_Id === this.form.TeamGroup)\r\n              const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\r\n              row[tarCode] = Number(row[tarCode]) + row.allocatedTask\r\n              row.Can_Allocation_Count = row.Can_Allocation_Count - row.allocatedTask\r\n              row[this.getTaskCode(team.Working_Team_Name)] += row.allocatedTask\r\n              row[this.getTaskCode()] -= row.allocatedTask\r\n              row.allocatedTaskMax = row.Can_Allocation_Count\r\n              row.allocatedTask = row.Can_Allocation_Count\r\n              row['alCount' + tarCode] = row[tarCode]\r\n            }\r\n          })\r\n      } else {\r\n        this.multipleSelection\r\n          .forEach((row, idx) => {\r\n            const team = row.Allocation_Teams.find(v => v.Working_Team_Id === this.form.TeamGroup)\r\n            const tarCode = this.getRowUnique(row.uuid, team.Process_Code, team.Working_Team_Id)\r\n\r\n            const selectNum = Math.min(row[this.getRowCCode(row, 'alCount')], row[this.getRowCCode(row)])\r\n\r\n            row[this.getTaskCode(team.Working_Team_Name)] += selectNum\r\n            row[this.getTaskCode()] -= selectNum\r\n\r\n            row[tarCode] = Number(row[tarCode]) + selectNum\r\n            row[this.getRowCCode(row)] -= selectNum\r\n            row[this.getRowCCode(row, 'alCount')] -= selectNum\r\n\r\n            row['alCount' + tarCode] = row[tarCode]\r\n          })\r\n      }\r\n      this.filterTbData = this.tbData.filter(row => {\r\n        return this.filterZero(row)\r\n      })\r\n    },\r\n    resetForm(formName) {\r\n      this.$refs[formName].resetFields()\r\n      this.dialogVisible = false\r\n    },\r\n    filterTypeMethod({ option, row }) {\r\n      return row.Type.includes(option.data)\r\n    },\r\n    filterTypeRecoverMethod({ option }) {\r\n      option.data = ''\r\n    },\r\n    filterCodeMethod({ option, row }) {\r\n      console.log('option, row', option, row)\r\n      return row[this.isCom ? 'Comp_Code' : 'Part_Code'].includes(option.data)\r\n    },\r\n    filterCodeRecoverMethod({ option }) {\r\n      option.data = ''\r\n    },\r\n    getObjectTypeList() {\r\n      GetCompTypeTree({ professional: 'Steel' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.ObjectTypeList.data = res.Data\r\n          this.$nextTick((_) => {\r\n            this.$refs?.treeSelectObjectType?.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.pagination-container {\r\n  padding: 0;\r\n  text-align: right;\r\n}\r\n\r\n::v-deep .el-card__body {\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .el-divider {\r\n    margin-top: 0;\r\n    margin-bottom: 16px;\r\n    background-color: #EEEEEE;\r\n  }\r\n\r\n  .tb-options {\r\n    margin-bottom: 16px;\r\n    display: flex;\r\n\r\n    .el-form-item--small.el-form-item {\r\n      margin-bottom: 0;\r\n    }\r\n    .el-input {\r\n      width: 250px;\r\n    }\r\n  }\r\n\r\n  footer {\r\n    text-align: inherit;\r\n    display: flex;\r\n    justify-content: space-between;\r\n  }\r\n}\r\n\r\n.tb-x {\r\n  flex: 1;\r\n  height: 0;\r\n  margin-bottom: 10px;\r\n  overflow: auto;\r\n}\r\n\r\n.topTitle {\r\n  height: 14px;\r\n  line-height: 14px;\r\n  font-size: 14px;\r\n  margin: 0 0 16px;\r\n\r\n  span {\r\n    display: inline-block;\r\n    width: 2px;\r\n    height: 14px;\r\n    background: #009dff;\r\n    vertical-align: middle;\r\n    margin-right: 6px;\r\n  }\r\n}\r\n\r\n.el-icon-edit {\r\n  cursor: pointer;\r\n}\r\n\r\n.cs-bottom {\r\n  position: relative;\r\n  height: 40px;\r\n  line-height: 40px;\r\n\r\n  .data-info {\r\n    position: absolute;\r\n    bottom: 0;\r\n\r\n    .info-x {\r\n      margin-right: 20px;\r\n    }\r\n  }\r\n}\r\n.my-input {\r\n  margin: 10px;\r\n  width: 140px;\r\n  height: 32px;\r\n}\r\n</style>\r\n"]}]}
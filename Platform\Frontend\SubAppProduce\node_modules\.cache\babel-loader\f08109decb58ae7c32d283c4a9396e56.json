{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\bom-import-temp-config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\bom-setting\\bom-import-temp-config\\index.vue", "mtime": 1756109946500}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["closeTagView", "deepClone", "GetTableSettingList", "RestoreTemplateType", "SavDeepenTemplateSetting", "v4", "uuidv4", "name", "data", "activeName", "tabList", "label", "value", "searchValue", "majorName", "unit", "steelUnit", "templateListNew", "list", "form", "pgLoading", "saveLoading", "restoreLoading1", "restoreLoading2", "unitInfo", "mounted", "$route", "query", "steel_unit", "fetchData", "methods", "_this", "ProfessionalCode", "then", "res", "IsSucceed", "result", "defaultList", "Data", "map", "item", "uuid", "Code", "concat", "Display_Name", "showRed", "Column_Type", "$message", "message", "Message", "type", "filterList", "_this2", "filter", "includes", "save", "_this3", "hasEmpty", "some", "trim", "error", "nameSet", "Set", "duplicates", "has", "add", "length", "d", "join", "$confirm", "confirmButtonText", "cancelButtonText", "submitList", "Professional_Code", "Is_Component", "Sort", "Remark", "Is_Enabled", "catch", "restore", "_this4", "Type"], "sources": ["src/views/PRO/bom-setting/bom-import-temp-config/index.vue"], "sourcesContent": ["\r\n<template>\r\n  <div v-loading=\"pgLoading\" class=\"page-container\">\r\n    <!-- <el-button style=\"margin-bottom: 16px\" @click=\"backPage\">返回</el-button>-->\r\n    <div class=\"top-wrapper\">\r\n      <div class=\"info\">\r\n        <template v-if=\"!!majorName\">\r\n          <div class=\"title\">当前专业：</div>\r\n          <div class=\"value\">{{ majorName }}</div>\r\n        </template>\r\n        <template v-if=\"!!unit\">\r\n          <div class=\"title\">统计单位：</div>\r\n          <div class=\"value\">{{ unit }}</div>\r\n        </template>\r\n        <template v-if=\"!!steelUnit\">\r\n          <div class=\"title\">构件单位：</div>\r\n          <div class=\"value\">{{ steelUnit }}</div>\r\n        </template>\r\n        <template>\r\n          <div class=\"title\">单位统计字段：</div>\r\n          {{ unitInfo }}\r\n        </template>\r\n      </div>\r\n      <el-tabs v-model=\"activeName\">\r\n        <el-tab-pane v-for=\"(item,index) in tabList\" :key=\"index\" :label=\"item.label\" :name=\"item.value\" />\r\n      </el-tabs>\r\n    </div>\r\n\r\n    <div class=\"cs-content-wrapper\">\r\n      <div class=\"content-top\">\r\n        <span class=\"content-title\">系统字段</span>\r\n        <div class=\"content-top-right\">\r\n          <label class=\"cs-label\">\r\n            <span>字段名称：</span>\r\n            <el-input v-model=\"searchValue\" placeholder=\"请输入\" clearable=\"\" />\r\n          </label>\r\n          <div>\r\n            <el-button type=\"primary\" @click=\"filterList\">查询</el-button>\r\n            <el-button type=\"primary\" :loading=\"saveLoading\" @click=\"save\">保存设置</el-button>\r\n            <el-button type=\"success\" :loading=\"restoreLoading1\" @click=\"restore(1)\">恢复默认二级清单</el-button>\r\n            <el-button type=\"success\" :loading=\"restoreLoading2\" @click=\"restore(2)\">恢复默认三级清单</el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"100px\">\r\n        <el-row v-for=\"item in list\" :key=\"item.uuid\">\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"字段名：\">\r\n              <el-input v-model=\"item.Display_Name\" clearble :class=\"['w100',{'showRed':item.showRed}]\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"Code：\">\r\n              <el-input v-model=\"item.Code\" clearble class=\"w100\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"备注说明：\">\r\n              <el-input v-model=\"item.Remark\" clearble class=\"w100\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"3\">\r\n            <el-form-item label=\"排序：\">\r\n              <el-input-number v-model.number=\"item.Sort\" :min=\"0\" class=\"w100 cs-number-btn-hidden\" clearble />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col v-if=\"!item.showRed\" :span=\"3\">\r\n            <el-form-item label=\"是否启用：\">\r\n              <el-switch v-model=\"item.Is_Enabled\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { closeTagView, deepClone } from '@/utils'\r\nimport { GetTableSettingList, RestoreTemplateType, SavDeepenTemplateSetting } from '@/api/PRO/component-type'\r\nimport { v4 as uuidv4 } from 'uuid'\r\n\r\nexport default {\r\n  name: 'PROBomImportTemplateConfig',\r\n  data() {\r\n    return {\r\n      activeName: 'pz',\r\n      tabList: [{\r\n        label: '深化清单导入配置',\r\n        value: 'pz'\r\n      }],\r\n      searchValue: '',\r\n      majorName: '',\r\n      unit: '',\r\n      steelUnit: '',\r\n      templateListNew: [],\r\n      list: [],\r\n      form: {},\r\n      pgLoading: false,\r\n      saveLoading: false,\r\n      restoreLoading1: false,\r\n      restoreLoading2: false,\r\n      unitInfo: ''\r\n\r\n    }\r\n  },\r\n  mounted() {\r\n    this.majorName = this.$route.query.name || ''\r\n    this.unit = this.$route.query.unit || ''\r\n    this.steelUnit = this.$route.query.steel_unit || ''\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n\r\n    fetchData() {\r\n      this.pgLoading = true\r\n      GetTableSettingList({\r\n        ProfessionalCode: 'Steel'\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          let result = ''\r\n          this.defaultList = res.Data.map(item => {\r\n            item.uuid = uuidv4()\r\n            if (item.Code === 'SteelAmount') {\r\n              result += `${item.Display_Name || ''}*`\r\n            }\r\n            if (item.Code === 'SteelWeight') {\r\n              result += `${item.Display_Name || ''}`\r\n            }\r\n            item.showRed = item.Column_Type === 0\r\n            return item\r\n          })\r\n          this.list = deepClone(this.defaultList)\r\n          this.unitInfo = result\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.pgLoading = false\r\n      })\r\n    },\r\n    filterList() {\r\n      if (!this.searchValue) {\r\n        this.list = deepClone(this.defaultList)\r\n        return\r\n      }\r\n      this.list = this.defaultList.filter(item => {\r\n        return item.Display_Name.includes(this.searchValue)\r\n      })\r\n    },\r\n    save() {\r\n      const hasEmpty = this.list.some(item =>\r\n        !item.Display_Name || item.Display_Name.trim() === ''\r\n      )\r\n      if (hasEmpty) {\r\n        this.$message.error('字段名不能为空')\r\n        return\r\n      }\r\n\r\n      const nameSet = new Set()\r\n      const duplicates = this.list.filter(item => {\r\n        if (nameSet.has(item.Display_Name)) {\r\n          return true\r\n        }\r\n        nameSet.add(item.Display_Name)\r\n        return false\r\n      })\r\n\r\n      if (duplicates.length > 0) {\r\n        this.$message.error(`存在重复的字段名 : ${duplicates.map(d => d.Display_Name).join(', ')}`)\r\n        return []\r\n      }\r\n\r\n      this.$confirm('是否保存当前配置?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.saveLoading = true\r\n        const submitList = this.list.map(item => {\r\n          return {\r\n            Professional_Code: 'Steel',\r\n            Is_Component: '',\r\n            Code: item.Code,\r\n            Display_Name: item.Display_Name,\r\n            Column_Type: item.Column_Type,\r\n            Sort: item.Sort,\r\n            Remark: item.Remark,\r\n            Is_Enabled: item.Is_Enabled\r\n          }\r\n        })\r\n        SavDeepenTemplateSetting(submitList).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            this.fetchData()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.saveLoading = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n        this.saveLoading = false\r\n      })\r\n    },\r\n    restore(type) {\r\n      const label = type === 1 ? '二级' : '三级'\r\n      if (type === 1) {\r\n        this.restoreLoading1 = true\r\n      } else {\r\n        this.restoreLoading2 = true\r\n      }\r\n      this.$confirm(`此是否恢复默认${label}清单?`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        RestoreTemplateType({\r\n          ProfessionalCode: 'Steel',\r\n          Type: type\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.fetchData()\r\n            this.$message({\r\n              type: 'success',\r\n              message: '恢复成功!'\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          this.restoreLoading1 = false\r\n          this.restoreLoading2 = false\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n        this.restoreLoading1 = false\r\n        this.restoreLoading2 = false\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-container {\r\n  margin: 16px;\r\n  box-sizing: border-box;\r\n\r\n  .top-wrapper {\r\n    background: #fff;\r\n    padding: 16px 16px 0 16px;\r\n    box-sizing: border-box;\r\n\r\n    .title {\r\n      font-size: 16px;\r\n      font-weight: 500;\r\n      color: #333333;\r\n    }\r\n    .info{\r\n      font-size: 14px;\r\n      display: flex;\r\n      flex-direction: row;\r\n      margin-bottom: 16px;\r\n      .title{\r\n        font-size: 14px;\r\n        color: #999999;\r\n      }\r\n      .value{\r\n        color: #333333;\r\n        margin-right: 24px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .cs-content-wrapper{\r\n    background-color: #ffffff;\r\n    margin-top: 16px;\r\n    padding: 16px;\r\n    .content-top{\r\n      margin-bottom: 32px;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      .content-title{\r\n        font-weight: 400;\r\n        color: #1f2f3d;\r\n        font-size: 22px;\r\n      }\r\n      .cs-label{\r\n        font-size: 14px;\r\n        font-family: \"Microsoft YaHei\", \"微软雅黑\", \"PingFang SC\", \"Hiragino Sans GB\", \"Helvetica Neue\", Arial, sans-serif;\r\n        font-weight: normal;\r\n        display: flex;\r\n        white-space: nowrap;\r\n        align-items: center;\r\n        margin-right: 8px;\r\n\r\n        span{\r\n          margin-right: 16px;\r\n        }\r\n      }\r\n      .content-top-right{\r\n        display: flex;\r\n      }\r\n    }\r\n    .showRed{\r\n      ::v-deep{\r\n        .el-input__inner {\r\n          color:red !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6EA,SAAAA,YAAA,EAAAC,SAAA;AACA,SAAAC,mBAAA,EAAAC,mBAAA,EAAAC,wBAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,OAAA;QACAC,KAAA;QACAC,KAAA;MACA;MACAC,WAAA;MACAC,SAAA;MACAC,IAAA;MACAC,SAAA;MACAC,eAAA;MACAC,IAAA;MACAC,IAAA;MACAC,SAAA;MACAC,WAAA;MACAC,eAAA;MACAC,eAAA;MACAC,QAAA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAX,SAAA,QAAAY,MAAA,CAAAC,KAAA,CAAApB,IAAA;IACA,KAAAQ,IAAA,QAAAW,MAAA,CAAAC,KAAA,CAAAZ,IAAA;IACA,KAAAC,SAAA,QAAAU,MAAA,CAAAC,KAAA,CAAAC,UAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IAEAD,SAAA,WAAAA,UAAA;MAAA,IAAAE,KAAA;MACA,KAAAX,SAAA;MACAlB,mBAAA;QACA8B,gBAAA;MACA,GAAAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACA,IAAAC,MAAA;UACAL,KAAA,CAAAM,WAAA,GAAAH,GAAA,CAAAI,IAAA,CAAAC,GAAA,WAAAC,IAAA;YACAA,IAAA,CAAAC,IAAA,GAAAnC,MAAA;YACA,IAAAkC,IAAA,CAAAE,IAAA;cACAN,MAAA,OAAAO,MAAA,CAAAH,IAAA,CAAAI,YAAA;YACA;YACA,IAAAJ,IAAA,CAAAE,IAAA;cACAN,MAAA,OAAAO,MAAA,CAAAH,IAAA,CAAAI,YAAA;YACA;YACAJ,IAAA,CAAAK,OAAA,GAAAL,IAAA,CAAAM,WAAA;YACA,OAAAN,IAAA;UACA;UACAT,KAAA,CAAAb,IAAA,GAAAjB,SAAA,CAAA8B,KAAA,CAAAM,WAAA;UACAN,KAAA,CAAAP,QAAA,GAAAY,MAAA;QACA;UACAL,KAAA,CAAAgB,QAAA;YACAC,OAAA,EAAAd,GAAA,CAAAe,OAAA;YACAC,IAAA;UACA;QACA;QACAnB,KAAA,CAAAX,SAAA;MACA;IACA;IACA+B,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,UAAAvC,WAAA;QACA,KAAAK,IAAA,GAAAjB,SAAA,MAAAoC,WAAA;QACA;MACA;MACA,KAAAnB,IAAA,QAAAmB,WAAA,CAAAgB,MAAA,WAAAb,IAAA;QACA,OAAAA,IAAA,CAAAI,YAAA,CAAAU,QAAA,CAAAF,MAAA,CAAAvC,WAAA;MACA;IACA;IACA0C,IAAA,WAAAA,KAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,QAAA,QAAAvC,IAAA,CAAAwC,IAAA,WAAAlB,IAAA;QAAA,OACA,CAAAA,IAAA,CAAAI,YAAA,IAAAJ,IAAA,CAAAI,YAAA,CAAAe,IAAA;MAAA,CACA;MACA,IAAAF,QAAA;QACA,KAAAV,QAAA,CAAAa,KAAA;QACA;MACA;MAEA,IAAAC,OAAA,OAAAC,GAAA;MACA,IAAAC,UAAA,QAAA7C,IAAA,CAAAmC,MAAA,WAAAb,IAAA;QACA,IAAAqB,OAAA,CAAAG,GAAA,CAAAxB,IAAA,CAAAI,YAAA;UACA;QACA;QACAiB,OAAA,CAAAI,GAAA,CAAAzB,IAAA,CAAAI,YAAA;QACA;MACA;MAEA,IAAAmB,UAAA,CAAAG,MAAA;QACA,KAAAnB,QAAA,CAAAa,KAAA,uDAAAjB,MAAA,CAAAoB,UAAA,CAAAxB,GAAA,WAAA4B,CAAA;UAAA,OAAAA,CAAA,CAAAvB,YAAA;QAAA,GAAAwB,IAAA;QACA;MACA;MAEA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACArB,IAAA;MACA,GAAAjB,IAAA;QACAuB,MAAA,CAAAnC,WAAA;QACA,IAAAmD,UAAA,GAAAhB,MAAA,CAAAtC,IAAA,CAAAqB,GAAA,WAAAC,IAAA;UACA;YACAiC,iBAAA;YACAC,YAAA;YACAhC,IAAA,EAAAF,IAAA,CAAAE,IAAA;YACAE,YAAA,EAAAJ,IAAA,CAAAI,YAAA;YACAE,WAAA,EAAAN,IAAA,CAAAM,WAAA;YACA6B,IAAA,EAAAnC,IAAA,CAAAmC,IAAA;YACAC,MAAA,EAAApC,IAAA,CAAAoC,MAAA;YACAC,UAAA,EAAArC,IAAA,CAAAqC;UACA;QACA;QACAzE,wBAAA,CAAAoE,UAAA,EAAAvC,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAqB,MAAA,CAAAT,QAAA;cACAC,OAAA;cACAE,IAAA;YACA;YACAM,MAAA,CAAA3B,SAAA;UACA;YACA2B,MAAA,CAAAT,QAAA;cACAC,OAAA,EAAAd,GAAA,CAAAe,OAAA;cACAC,IAAA;YACA;UACA;UACAM,MAAA,CAAAnC,WAAA;QACA;MACA,GAAAyD,KAAA;QACAtB,MAAA,CAAAT,QAAA;UACAG,IAAA;UACAF,OAAA;QACA;QACAQ,MAAA,CAAAnC,WAAA;MACA;IACA;IACA0D,OAAA,WAAAA,QAAA7B,IAAA;MAAA,IAAA8B,MAAA;MACA,IAAArE,KAAA,GAAAuC,IAAA;MACA,IAAAA,IAAA;QACA,KAAA5B,eAAA;MACA;QACA,KAAAC,eAAA;MACA;MACA,KAAA8C,QAAA,8CAAA1B,MAAA,CAAAhC,KAAA;QACA2D,iBAAA;QACAC,gBAAA;QACArB,IAAA;MACA,GAAAjB,IAAA;QACA9B,mBAAA;UACA6B,gBAAA;UACAiD,IAAA,EAAA/B;QACA,GAAAjB,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACA6C,MAAA,CAAAnD,SAAA;YACAmD,MAAA,CAAAjC,QAAA;cACAG,IAAA;cACAF,OAAA;YACA;UACA;YACAgC,MAAA,CAAAjC,QAAA;cACAC,OAAA,EAAAd,GAAA,CAAAe,OAAA;cACAC,IAAA;YACA;UACA;UACA8B,MAAA,CAAA1D,eAAA;UACA0D,MAAA,CAAAzD,eAAA;QACA;MACA,GAAAuD,KAAA;QACAE,MAAA,CAAAjC,QAAA;UACAG,IAAA;UACAF,OAAA;QACA;QACAgC,MAAA,CAAA1D,eAAA;QACA0D,MAAA,CAAAzD,eAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\BatchProcessAdjust.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new-part\\components\\BatchProcessAdjust.vue", "mtime": 1757572678814}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRQcm9jZXNzTGlzdEJhc2UsIEdldExpYkxpc3QgfSBmcm9tICdAL2FwaS9QUk8vdGVjaG5vbG9neS1saWInDQppbXBvcnQgRHJhZ2dhYmxlIGZyb20gJ3Z1ZWRyYWdnYWJsZScNCmltcG9ydCB7IHVuaXF1ZUFyciwgZGVlcENsb25lIH0gZnJvbSAnQC91dGlscycNCmltcG9ydCB7IG1hcEFjdGlvbnMgfSBmcm9tICd2dWV4Jw0KaW1wb3J0IHsgdjQgYXMgdXVpZHY0IH0gZnJvbSAndXVpZCcNCmV4cG9ydCBkZWZhdWx0IHsNCiAgY29tcG9uZW50czogew0KICAgIERyYWdnYWJsZQ0KICB9LA0KICBwcm9wczogew0KICAgIHBhZ2VUeXBlOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiB1bmRlZmluZWQNCiAgICB9LA0KICAgIGlzTmVzdDogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfSwNCiAgICBwcm9jZXNzTGlzdDogew0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgZGVmYXVsdDogKCkgPT4gKHt9KQ0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgbGlzdDogW10sDQogICAgICBvcHRpb25zOiBbXSwNCiAgICAgIC8vIGRlZmF1bHRPcHRpb25zOiBbXSwNCiAgICAgIGd5TGlzdDogW10sDQogICAgICBjcmFmdENvZGU6ICcnLA0KICAgICAgYnRuTG9hZGluZzogZmFsc2UsDQogICAgICBwZ0xvYWRpbmc6IGZhbHNlLA0KICAgICAgZm9ybToge30NCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgaXNDb20oKSB7DQogICAgICByZXR1cm4gdGhpcy5wYWdlVHlwZSA9PT0gJ2NvbScNCiAgICB9DQogIH0sDQogIHdhdGNoOiB7DQogICAgbGlzdDogew0KICAgICAgaGFuZGxlcihuZXdWYWwpIHsNCiAgICAgICAgaWYgKCF0aGlzLmNyYWZ0Q29kZSkgcmV0dXJuDQogICAgICAgIGNvbnN0IHdvcmtDb2RlID0gdGhpcy5neUxpc3QuZmluZCh2ID0+IHYuQ29kZSA9PT0gdGhpcy5jcmFmdENvZGUpPy5Xb3JrQ29kZQ0KICAgICAgICBjb25zdCBuZXdDb2RlID0gbmV3VmFsLm1hcCh2ID0+IHYudmFsdWUpLmZpbHRlcih2ID0+ICEhdikuam9pbignLycpDQogICAgICAgIGlmICh3b3JrQ29kZSAhPT0gbmV3Q29kZSkgew0KICAgICAgICAgIHRoaXMuY3JhZnRDb2RlID0gJycNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIGRlZXA6IHRydWUNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAuLi5tYXBBY3Rpb25zKCdzY2hlZHVsZScsIFsnaW5pdFByb2Nlc3NMaXN0J10pLA0KICAgIGdldENyYWZ0UHJvY2VzcygpIHsNCiAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7DQogICAgICAgIEdldExpYkxpc3Qoew0KICAgICAgICAgIElkOiAnJywNCiAgICAgICAgICBUeXBlOiAyDQogICAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgdGhpcy5neUxpc3QgPSAocmVzLkRhdGEgfHwgW10pDQogICAgICAgICAgICByZXNvbHZlKCkNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgcmVqZWN0KCkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0sDQogICAgZ2V0UHJvY2Vzc09wdGlvbih3b3Jrc2hvcElkKSB7DQogICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4gew0KICAgICAgICB0aGlzLnBnTG9hZGluZyA9IHRydWUNCiAgICAgICAgR2V0UHJvY2Vzc0xpc3RCYXNlKHsNCiAgICAgICAgICB3b3Jrc2hvcElkOiB3b3Jrc2hvcElkLA0KICAgICAgICAgIHR5cGU6IHRoaXMuaXNDb20gPyAxIDogMiAvLyAwOuWFqOmDqO+8jOW3peiJuuexu+WeizHvvJrmnoTku7blt6XoibrvvIwy77ya6Zu25Lu25bel6Im6DQogICAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgdGhpcy5vcHRpb25zID0gcmVzLkRhdGEubWFwKHYgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRzZXQodiwgJ2Rpc2FibGVkJywgZmFsc2UpDQogICAgICAgICAgICAgIHJldHVybiB2DQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICAgIHJlc29sdmUoKQ0KICAgICAgICB9KS5maW5hbGx5KF8gPT4gew0KICAgICAgICAgIHRoaXMucGdMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfSwNCiAgICBzZWxlY3RDaGFuZ2UodmFsLCBlbGVtZW50KSB7DQogICAgICBjb25zb2xlLmxvZygndmFsJywgdmFsKQ0KICAgICAgY29uc29sZS5sb2coJ2VsZW1lbnQnLCBlbGVtZW50KQ0KICAgICAgY29uc3QgYXJyID0gdGhpcy5saXN0Lm1hcChpID0+IGkudmFsdWUpDQogICAgICB0aGlzLm9wdGlvbnMuZm9yRWFjaCgoaXRlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgaXRlbS5kaXNhYmxlZCA9IGFyci5pbmNsdWRlcyhpdGVtLkNvZGUpDQogICAgICAgIGlmIChpdGVtLkNvZGUgPT09IHZhbCkgew0KICAgICAgICAgIGVsZW1lbnQuVGVhbXMgPSBpdGVtLlRlYW1zDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICBpZiAoZWxlbWVudCkgew0KICAgICAgICBlbGVtZW50LmRhdGUgPSB0aGlzLnByb2Nlc3NMaXN0W3ZhbF0/LkZpbmlzaF9EYXRlDQogICAgICAgIGlmICghdmFsKSB7DQogICAgICAgICAgZWxlbWVudC5Xb3JraW5nX1RlYW1fSWQgPSAnJw0KICAgICAgICAgIGVsZW1lbnQuVGVhbXMgPSBbXQ0KICAgICAgICB9DQogICAgICB9DQogICAgICBpZiAoIWVsZW1lbnQ/LldvcmtpbmdfVGVhbV9JZCAmJiBlbGVtZW50Py5UZWFtcz8ubGVuZ3RoID09PSAxKSB7DQogICAgICAgIGVsZW1lbnQuV29ya2luZ19UZWFtX0lkID0gZWxlbWVudC5UZWFtc1swXS5JZA0KICAgICAgfQ0KICAgIH0sDQogICAgZGF0ZUNoYW5nZSh2YWwsIGVsZW1lbnQpIHsNCiAgICAgIGNvbnN0IGl0ZW0gPSB0aGlzLm9wdGlvbnMuZmluZCh2ID0+IHYuQ29kZSA9PT0gZWxlbWVudC52YWx1ZSkNCiAgICAgIGNvbnNvbGUubG9nKCdpdGVtJywgaXRlbSwgdGhpcy5saXN0KQ0KICAgICAgbGV0IG9iaiA9IHt9DQogICAgICBpZiAoaXRlbSkgew0KICAgICAgICBvYmogPSB7DQogICAgICAgICAgU2NoZHVsaW5nX0lkOiB0aGlzLmZvcm1JbmxpbmU/LlNjaGR1bGluZ19Db2RlLA0KICAgICAgICAgIFByb2Nlc3NfSWQ6IGl0ZW0uSWQsDQogICAgICAgICAgUHJvY2Vzc19Db2RlOiBpdGVtLkNvZGUsDQogICAgICAgICAgRmluaXNoX0RhdGU6IHZhbA0KICAgICAgICB9DQogICAgICB9DQogICAgICAvLyB0aGlzLiRlbWl0KCdzZXRQcm9jZXNzTGlzdCcsIHsga2V5OiBlbGVtZW50LnZhbHVlLCB2YWx1ZTogb2JqIH0pDQogICAgfSwNCiAgICBoYW5kbGVBZGQoaXRlbSkgew0KICAgICAgY29uc3QgYXJyID0gdGhpcy5saXN0Lm1hcCh2ID0+IHYudmFsdWUpDQogICAgICB0aGlzLm9wdGlvbnMuZm9yRWFjaCh2ID0+IHsNCiAgICAgICAgaWYgKGFyci5pbmNsdWRlcyh2LkNvZGUpKSB7DQogICAgICAgICAgdi5kaXNhYmxlZCA9IHRydWUNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIHRoaXMubGlzdC5wdXNoKHsNCiAgICAgICAga2V5OiB1dWlkdjQoKSwNCiAgICAgICAgdmFsdWU6ICcnLA0KICAgICAgICBXb3JraW5nX1RlYW1fSWQ6ICcnLA0KICAgICAgICBUZWFtczogW10sDQogICAgICAgIGRhdGU6ICcnDQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlRGVsZXRlKGVsZW1lbnQpIHsNCiAgICAgIGNvbnN0IGlkeCA9IHRoaXMubGlzdC5maW5kSW5kZXgodiA9PiB2LnZhbHVlID09PSBlbGVtZW50LnZhbHVlKQ0KICAgICAgaWYgKGlkeCAhPT0gLTEpIHsNCiAgICAgICAgdGhpcy5saXN0LnNwbGljZShpZHgsIDEpDQogICAgICAgIHRoaXMuc2VsZWN0Q2hhbmdlKCkNCiAgICAgIH0NCiAgICB9LA0KICAgIGdldFdvcmtpbmdUZWFtKHRlYW1zLCBjdXJJdGVtKSB7DQogICAgICBjb25zdCBuZXdUZWFtcyA9IHRlYW1zLmZpbHRlcih2ID0+IHsNCiAgICAgICAgaWYgKHRoaXMud29ya3Nob3BJZCkgew0KICAgICAgICAgIHJldHVybiB2LldvcmtzaG9wX0lkID09PSB0aGlzLndvcmtzaG9wSWQNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgfSkNCiAgICAgIGlmICghbmV3VGVhbXMubGVuZ3RoKSB7DQogICAgICAgIGN1ckl0ZW0uV29ya2luZ19UZWFtX0lkID0gJycNCiAgICAgICAgcmV0dXJuIFtdDQogICAgICB9DQogICAgICBpZiAobmV3VGVhbXMuZXZlcnkodiA9PiB2LklkICE9PSBjdXJJdGVtLldvcmtpbmdfVGVhbV9JZCkpIHsNCiAgICAgICAgY3VySXRlbS5Xb3JraW5nX1RlYW1fSWQgPSAnJw0KICAgICAgfQ0KICAgICAgcmV0dXJuIG5ld1RlYW1zDQogICAgfSwNCiAgICBhc3luYyBzZXREYXRhKGFyciwgdGVjaG5vbG9neVN0cikgew0KICAgICAgYXdhaXQgdGhpcy5nZXRDcmFmdFByb2Nlc3MoKQ0KICAgICAgbGV0IHRlY2hub2xvZ3lBcnIgPSBbXQ0KICAgICAgaWYgKHRlY2hub2xvZ3lTdHIpIHsNCiAgICAgICAgdGVjaG5vbG9neUFyciA9IHRlY2hub2xvZ3lTdHIuc3BsaXQoJy8nKQ0KICAgICAgfQ0KICAgICAgY29uc3Qgd29ya3Nob3BJZCA9IGFyclswXS5Xb3Jrc2hvcF9JZA0KICAgICAgdGhpcy53b3Jrc2hvcElkID0gd29ya3Nob3BJZA0KDQogICAgICBjb25zdCBwYXJ0VXNlZFByb2Nlc3MgPSBhcnJbMF0uUGFydF9Vc2VkX1Byb2Nlc3MNCiAgICAgIGF3YWl0IHRoaXMuZ2V0UHJvY2Vzc09wdGlvbih3b3Jrc2hvcElkKQ0KDQogICAgICB0aGlzLm9wdGlvbnMgPSB0aGlzLm9wdGlvbnMuZmlsdGVyKGl0ZW0gPT4gew0KICAgICAgICBsZXQgZmxhZyA9IGZhbHNlDQogICAgICAgIGlmICh0ZWNobm9sb2d5QXJyLmxlbmd0aCAmJiB0ZWNobm9sb2d5QXJyLmluY2x1ZGVzKGl0ZW0uQ29kZSkpIHsNCiAgICAgICAgICBmbGFnID0gdHJ1ZQ0KICAgICAgICB9DQogICAgICAgIGlmIChwYXJ0VXNlZFByb2Nlc3MgJiYgcGFydFVzZWRQcm9jZXNzID09PSBpdGVtLkNvZGUpIHsNCiAgICAgICAgICBmbGFnID0gdHJ1ZQ0KICAgICAgICB9DQogICAgICAgIGlmICghZmxhZykgew0KICAgICAgICAgIGZsYWcgPSAhIWl0ZW0uSXNfRW5hYmxlDQogICAgICAgIH0NCiAgICAgICAgcmV0dXJuIGZsYWcNCiAgICAgIH0pDQogICAgICAvLyB0aGlzLmRlZmF1bHRPcHRpb25zID0gZGVlcENsb25lKHRoaXMub3B0aW9ucykNCg0KICAgICAgdGhpcy5hcnIgPSBhcnIgfHwgbnVsbA0KICAgICAgdGhpcy5saXN0ID0gW10NCiAgICAgIGxldCBjb2RlcyA9IFtdDQogICAgICBpZiAodGhpcy5pc0NvbSkgew0KICAgICAgICBjb25zdCBvcmlnaW4gPSBhcnIubWFwKHYgPT4gKHY/LlBhcnRfVXNlZF9Qcm9jZXNzIHx8ICcnKS5zcGxpdCgnLCcpKQ0KICAgICAgICBjb2RlcyA9IHRoaXMuZ2V0VW5pcXVlKG9yaWdpbi5mbGF0KCkpLmZpbHRlcih2ID0+ICEhdikNCg0KICAgICAgICBpZiAoY29kZXMubGVuZ3RoKSB7DQogICAgICAgICAgLy8g6Zu25p6E5Lu2DQogICAgICAgICAgY29uc3QgY2hlY2tPcHRpb24gPSBjb2Rlcy5maWx0ZXIoYyA9PiB7DQogICAgICAgICAgICByZXR1cm4gISF0aGlzLm9wdGlvbnMuZmluZChrID0+IGsuQ29kZSA9PT0gYykNCiAgICAgICAgICB9KQ0KICAgICAgICAgIGNvbnNvbGUubG9nKGNvZGVzLCBjaGVja09wdGlvbiwgdGhpcy5vcHRpb25zKQ0KICAgICAgICAgIGlmIChjaGVja09wdGlvbi5sZW5ndGggPCBjb2Rlcy5sZW5ndGgpIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiAn5b2T5YmN5p6E5Lu255Sf5Lqn5omA5bGe6L2m6Ze05YaF5rKh5pyJ6K+l5p6E5Lu25omA5bGe6Zu25Lu26aKG55So5bel5bqP77yM6K+36Iez6L2m6Ze0566h55CG5YaF5YWz6IGU55u45YWz5bel5bqP54+t57uEJywNCiAgICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgcmV0dXJuDQogICAgICAgICAgfQ0KDQogICAgICAgICAgY29kZXMuZm9yRWFjaCgodmFsdWUsIGlkeCkgPT4gew0KICAgICAgICAgICAgY29uc3Qgb2JqID0gew0KICAgICAgICAgICAgICB2YWx1ZSwNCiAgICAgICAgICAgICAgaXNQYXJ0OiB0cnVlLA0KICAgICAgICAgICAgICBrZXk6IHV1aWR2NCgpLA0KICAgICAgICAgICAgICBXb3JraW5nX1RlYW1fSWQ6IHRoaXMucHJvY2Vzc0xpc3RbdmFsdWVdPy5Xb3JraW5nX1RlYW1fSWQsDQogICAgICAgICAgICAgIFRlYW1zOiB0aGlzLm9wdGlvbnMuZmluZChpdGVtID0+IGl0ZW0uQ29kZSA9PT0gdmFsdWUpPy5UZWFtcyB8fCBbXSwNCiAgICAgICAgICAgICAgZGF0ZTogdGhpcy5wcm9jZXNzTGlzdFt2YWx1ZV0/LkZpbmlzaF9EYXRlDQogICAgICAgICAgICB9DQogICAgICAgICAgICBpZiAob2JqLlRlYW1zLmxlbmd0aCA9PT0gMSAmJiAhb2JqLldvcmtpbmdfVGVhbV9JZCkgew0KICAgICAgICAgICAgICBvYmouV29ya2luZ19UZWFtX0lkID0gb2JqLlRlYW1zWzBdLklkDQogICAgICAgICAgICB9DQogICAgICAgICAgICB0aGlzLmxpc3QucHVzaChvYmopDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgaWYgKHRlY2hub2xvZ3lBcnIubGVuZ3RoKSB7DQogICAgICAgIGNvbnN0IHRlY2hBcnIgPSB0ZWNobm9sb2d5QXJyLm1hcCh2ID0+IHsNCiAgICAgICAgICBjb25zdCBvYmogPSB7DQogICAgICAgICAgICBrZXk6IHV1aWR2NCgpLA0KICAgICAgICAgICAgdmFsdWU6IHYsDQogICAgICAgICAgICBXb3JraW5nX1RlYW1fSWQ6IHRoaXMucHJvY2Vzc0xpc3Rbdl0/LldvcmtpbmdfVGVhbV9JZCwNCiAgICAgICAgICAgIFRlYW1zOiB0aGlzLm9wdGlvbnMuZmluZChpdGVtID0+IGl0ZW0uQ29kZSA9PT0gdik/LlRlYW1zIHx8IFtdLA0KICAgICAgICAgICAgZGF0ZTogdGhpcy5wcm9jZXNzTGlzdFt2XT8uRmluaXNoX0RhdGUNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKG9iai5UZWFtcy5sZW5ndGggPT09IDEgJiYgIW9iai5Xb3JraW5nX1RlYW1fSWQpIHsNCiAgICAgICAgICAgIG9iai5Xb3JraW5nX1RlYW1fSWQgPSBvYmouVGVhbXNbMF0uSWQNCiAgICAgICAgICB9DQogICAgICAgICAgcmV0dXJuIG9iag0KICAgICAgICB9KQ0KICAgICAgICBjb25zb2xlLmxvZygndGVjaEFycicsIHRlY2hBcnIpDQogICAgICAgIHRlY2hBcnIuZm9yRWFjaCgoZWxlbWVudCwgaWR4KSA9PiB7DQogICAgICAgICAgaWYgKCFjb2Rlcy5pbmNsdWRlcyhlbGVtZW50LnZhbHVlKSkgew0KICAgICAgICAgICAgdGhpcy5saXN0LnB1c2goZWxlbWVudCkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9DQogICAgICBpZiAoIXRoaXMubGlzdC5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy5saXN0LnB1c2goew0KICAgICAgICAgIHZhbHVlOiAnJywNCiAgICAgICAgICBrZXk6IHV1aWR2NCgpLA0KICAgICAgICAgIFdvcmtpbmdfVGVhbV9JZDogJycsDQogICAgICAgICAgVGVhbXM6IFtdLA0KICAgICAgICAgIGRhdGU6ICcnDQogICAgICAgIH0pDQogICAgICAgIGlmICh0aGlzLmlzTmVzdCkgew0KICAgICAgICAgIGNvbnN0IHh1ciA9IHRoaXMub3B0aW9ucy5maWx0ZXIoaXRlbSA9PiBpdGVtLklzX05lc3QpDQogICAgICAgICAgaWYgKHh1ci5sZW5ndGggPT09IDEpIHsNCiAgICAgICAgICAgIHRoaXMubGlzdFswXS52YWx1ZSA9IHh1clswXS5Db2RlDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgICBjb25zdCBpbmRleE1hcCA9IHRlY2hub2xvZ3lBcnIucmVkdWNlKChtYXAsIGl0ZW0sIGluZGV4KSA9PiB7DQogICAgICAgIG1hcFtpdGVtXSA9IGluZGV4DQogICAgICAgIHJldHVybiBtYXANCiAgICAgIH0sIHt9KQ0KDQogICAgICB0aGlzLmxpc3Quc29ydCgoaXRlbTEsIGl0ZW0yKSA9PiB7DQogICAgICAgIHJldHVybiBpbmRleE1hcFtpdGVtMS52YWx1ZV0gLSBpbmRleE1hcFtpdGVtMi52YWx1ZV0NCiAgICAgIH0pDQoNCiAgICAgIHRoaXMuc2VsZWN0Q2hhbmdlKCkNCiAgICB9LA0KICAgIGdldFVuaXF1ZShhcnIpIHsNCiAgICAgIHJldHVybiB1bmlxdWVBcnIoYXJyKQ0KICAgIH0sDQogICAgc3VibWl0KCkgew0KICAgICAgY29uc3QgbGlzdCA9IHRoaXMubGlzdC5tYXAoaXRlbSA9PiBpdGVtLnZhbHVlKS5maWx0ZXIoayA9PiAhIWspDQogICAgICBjb25zdCBpc1RydWUgPSB0aGlzLmNoZWNrQ29kZShsaXN0KQ0KICAgICAgaWYgKCFpc1RydWUpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogJ+ebuOmCu+W3peW6j+S4jeiDveebuOWQjCcsDQogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgIH0pDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgaWYgKCFsaXN0Lmxlbmd0aCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAn5bel5bqP5LiN6IO95YWo5Li656m6JywNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgfSkNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIGlmICh0aGlzLmlzTmVzdCkgew0KICAgICAgICBjb25zdCB4dXIgPSB0aGlzLm9wdGlvbnMuZmlsdGVyKGl0ZW0gPT4gaXRlbS5Jc19OZXN0KQ0KICAgICAgICBpZiAoeHVyLmxlbmd0aCkgew0KICAgICAgICAgIGNvbnN0IGhhc05lc3QgPSB4dXIuc29tZShvYmogPT4gbGlzdC5pbmNsdWRlcyhvYmouQ29kZSkpDQogICAgICAgICAgaWYgKCFoYXNOZXN0KSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+ivt+iHs+WwkemAieaLqeS4gOS4quWll+aWmeW3peW6j++8gScsDQogICAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHJldHVybg0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICB0aGlzLmJ0bkxvYWRpbmcgPSB0cnVlDQogICAgICBjb25zdCBzdHIgPSBsaXN0LmpvaW4oJy8nKQ0KICAgICAgdGhpcy5saXN0LmZvckVhY2goKGVsZW1lbnQsIGlkeCkgPT4gew0KICAgICAgICBjb25zdCBpdGVtID0gdGhpcy5vcHRpb25zLmZpbmQodiA9PiB2LkNvZGUgPT09IGVsZW1lbnQudmFsdWUpDQoNCiAgICAgICAgbGV0IG9iaiA9IHt9DQogICAgICAgIGlmIChpdGVtKSB7DQogICAgICAgICAgb2JqID0gew0KICAgICAgICAgICAgU2NoZHVsaW5nX0lkOiB0aGlzLmZvcm1JbmxpbmU/LlNjaGR1bGluZ19Db2RlLA0KICAgICAgICAgICAgUHJvY2Vzc19JZDogaXRlbS5JZCwNCiAgICAgICAgICAgIFByb2Nlc3NfQ29kZTogaXRlbS5Db2RlLA0KICAgICAgICAgICAgRmluaXNoX0RhdGU6IGVsZW1lbnQuZGF0ZSwNCiAgICAgICAgICAgIFdvcmtpbmdfVGVhbV9JZDogZWxlbWVudC5Xb3JraW5nX1RlYW1fSWQNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgdGhpcy4kZW1pdCgnc2V0UHJvY2Vzc0xpc3QnLCB7IGtleTogZWxlbWVudC52YWx1ZSwgdmFsdWU6IG9iaiB9KQ0KICAgICAgfSkNCg0KICAgICAgdGhpcy4kZW1pdCgnc2VuZFByb2Nlc3MnLCB7IGFycjogdGhpcy5hcnIsIHN0ciB9KQ0KICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UNCiAgICAgIHRoaXMuaGFuZGxlQ2xvc2UoKQ0KICAgIH0sDQogICAgY3JhZnRDaGFuZ2UodmFsKSB7DQogICAgICB0aGlzLmNyYWZ0Q29kZSA9IHZhbA0KICAgICAgaWYgKCF2YWwpIHsNCiAgICAgICAgLy8gdGhpcy5vcHRpb25zID0gdGhpcy5kZWZhdWx0T3B0aW9ucw0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIGNvbnN0IGluZm8gPSB0aGlzLmd5TGlzdC5maW5kKHYgPT4gdi5Db2RlID09PSB2YWwpDQogICAgICBpZiAoaW5mbykgew0KICAgICAgICBjb25zdCBwbGlzdCA9IGluZm8uV29ya0NvZGUuc3BsaXQoJy8nKQ0KICAgICAgICAvLyB0aGlzLm9wdGlvbnMgPSB0aGlzLmRlZmF1bHRPcHRpb25zLmZpbHRlcih2ID0+IHBsaXN0LmluY2x1ZGVzKHYuQ29kZSkpDQogICAgICAgIHRoaXMub3B0aW9ucy5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgaWYgKHBsaXN0LmluY2x1ZGVzKGl0ZW0uQ29kZSkpIHsNCiAgICAgICAgICAgIGl0ZW0uZGlzYWJsZWQgPSB0cnVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGl0ZW0uZGlzYWJsZWQgPSBmYWxzZQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgY29uc3QgbmV3TGlzdCA9IFtdDQogICAgICAgIHBsaXN0LmZvckVhY2goKGxpc3RWYWwsIGlkeCkgPT4gew0KICAgICAgICAgIGNvbnN0IGl0ZW0gPSB0aGlzLmxpc3QuZmluZCh2ID0+IHYudmFsdWUgPT09IGxpc3RWYWwpDQogICAgICAgICAgaWYgKGl0ZW0pIHsNCiAgICAgICAgICAgIGlmIChpdGVtLlRlYW1zLmxlbmd0aCA9PT0gMSAmJiAhaXRlbS5Xb3JraW5nX1RlYW1fSWQpIHsNCiAgICAgICAgICAgICAgaXRlbS5Xb3JraW5nX1RlYW1fSWQgPSBpdGVtLlRlYW1zWzBdLklkDQogICAgICAgICAgICB9DQogICAgICAgICAgICBuZXdMaXN0LnB1c2goaXRlbSkNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgY29uc3QgaXRlbTIgPSB0aGlzLm9wdGlvbnMuZmluZCh2ID0+IHYuQ29kZSA9PT0gbGlzdFZhbCkNCiAgICAgICAgICAgIGlmIChpdGVtMikgew0KICAgICAgICAgICAgICBjb25zdCBvYmogPSB7DQogICAgICAgICAgICAgICAga2V5OiB1dWlkdjQoKSwNCiAgICAgICAgICAgICAgICB2YWx1ZTogaXRlbTIuQ29kZSwNCiAgICAgICAgICAgICAgICBXb3JraW5nX1RlYW1fSWQ6ICcnLA0KICAgICAgICAgICAgICAgIFRlYW1zOiBpdGVtMi5UZWFtcywNCiAgICAgICAgICAgICAgICBkYXRlOiAnJw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIGlmIChpdGVtMi5UZWFtcy5sZW5ndGggPT09IDEgJiYgIW9iai5Xb3JraW5nX1RlYW1fSWQpIHsNCiAgICAgICAgICAgICAgICBvYmouV29ya2luZ19UZWFtX0lkID0gaXRlbTIuVGVhbXNbMF0uSWQNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBuZXdMaXN0LnB1c2gob2JqKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgdGhpcy5saXN0ID0gbmV3TGlzdA0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlQ2xvc2UoKSB7DQogICAgICB0aGlzLiRlbWl0KCdjbG9zZScpDQogICAgfSwNCiAgICBjaGVja0NvZGUobGlzdCkgew0KICAgICAgbGV0IGZsYWcgPSB0cnVlDQogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGxpc3QubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgaWYgKGkgIT09IGxpc3QubGVuZ3RoIC0gMSAmJiBsaXN0W2ldID09PSBsaXN0W2kgKyAxXSkgew0KICAgICAgICAgIGZsYWcgPSBmYWxzZQ0KICAgICAgICAgIGJyZWFrDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHJldHVybiBmbGFnDQogICAgfSwNCiAgICBjaGFuZ2VEcmFnZ2FibGUoKSB7DQogICAgICB0aGlzLmxpc3QuZm9yRWFjaCh2ID0+IHsNCiAgICAgICAgdGhpcy4kc2V0KHYsICdkYXRlJywgJycpDQogICAgICB9KQ0KICAgICAgdGhpcy5pbml0UHJvY2Vzc0xpc3QoKQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["BatchProcessAdjust.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BatchProcessAdjust.vue", "sourceRoot": "src/views/PRO/plan-production/schedule-production-new-part/components", "sourcesContent": ["<template>\r\n  <div v-loading=\"pgLoading\" class=\"cs-container\">\r\n    <el-form ref=\"form\" :model=\"form\" label-width=\"100px\">\r\n\r\n      <el-form-item label=\"工艺代码\">\r\n        <el-select\r\n          v-model=\"craftCode\"\r\n          filterable\r\n          placeholder=\"下拉选择支持搜索\"\r\n          clearable=\"\"\r\n          @change=\"craftChange\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in gyList\"\r\n            :key=\"item.Code\"\r\n            :label=\"item.Code\"\r\n            :value=\"item.Code\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-divider />\r\n      <draggable v-model=\"list\" handle=\".icon-drag\" @change=\"changeDraggable\">\r\n        <transition-group>\r\n          <el-row v-for=\"(element,index) in list\" :key=\"element.key\">\r\n            <el-col :span=\"1\"> <i class=\"iconfont icon-drag cs-drag\" /> </el-col>\r\n            <el-col :span=\"10\">\r\n              <el-form-item :label=\"`排产工序${index+1}`\">\r\n                <el-select :key=\"element.key\" v-model=\"element.value\" style=\"width:90%\" :disabled=\"element.isPart\" placeholder=\"请选择\" clearable @change=\"selectChange($event,element)\">\r\n                  <el-option\r\n                    v-for=\"item in options\"\r\n                    :key=\"item.Code\"\r\n                    :label=\"item.Name\"\r\n                    :disabled=\"item.disabled\"\r\n                    :value=\"item.Code\"\r\n                  >\r\n                    <div class=\"cs-option\">\r\n                      <span class=\"cs-label\">{{ item.Name }}</span>\r\n                      <span v-if=\"item.Is_Nest && isNest\" class=\"cs-tip\">(套)</span>\r\n                    </div>\r\n                  </el-option>\r\n                </el-select>\r\n\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"10\">\r\n              <el-form-item label=\"班组\" label-width=\"60px\">\r\n                <el-select v-model=\"element.Working_Team_Id\" clearable placeholder=\"请选择\">\r\n                  <el-option v-for=\"item in getWorkingTeam(element.Teams,element)\" :key=\"item.Id\" :label=\"item.Name\" :value=\"item.Id\" />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"3\">\r\n              <span class=\"btn-x\">\r\n                <el-button v-if=\"index===0 && list.length<options.length\" type=\"primary\" icon=\"el-icon-plus\" circle @click=\"handleAdd\" />\r\n                <el-button v-if=\"index!==0&&!element.isPart\" type=\"danger\" icon=\"el-icon-delete\" circle @click=\"handleDelete(element)\" />\r\n              </span>\r\n            </el-col>\r\n\r\n          </el-row>\r\n        </transition-group>\r\n      </draggable>\r\n    </el-form>\r\n    <div class=\"dialog-footer\">\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n      <el-button v-if=\"list.length\" type=\"primary\" :loading=\"btnLoading\" @click=\"submit\">确 定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetProcessListBase, GetLibList } from '@/api/PRO/technology-lib'\r\nimport Draggable from 'vuedraggable'\r\nimport { uniqueArr, deepClone } from '@/utils'\r\nimport { mapActions } from 'vuex'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nexport default {\r\n  components: {\r\n    Draggable\r\n  },\r\n  props: {\r\n    pageType: {\r\n      type: String,\r\n      default: undefined\r\n    },\r\n    isNest: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    processList: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      list: [],\r\n      options: [],\r\n      // defaultOptions: [],\r\n      gyList: [],\r\n      craftCode: '',\r\n      btnLoading: false,\r\n      pgLoading: false,\r\n      form: {}\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    }\r\n  },\r\n  watch: {\r\n    list: {\r\n      handler(newVal) {\r\n        if (!this.craftCode) return\r\n        const workCode = this.gyList.find(v => v.Code === this.craftCode)?.WorkCode\r\n        const newCode = newVal.map(v => v.value).filter(v => !!v).join('/')\r\n        if (workCode !== newCode) {\r\n          this.craftCode = ''\r\n        }\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions('schedule', ['initProcessList']),\r\n    getCraftProcess() {\r\n      return new Promise((resolve, reject) => {\r\n        GetLibList({\r\n          Id: '',\r\n          Type: 2\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.gyList = (res.Data || [])\r\n            resolve()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    getProcessOption(workshopId) {\r\n      return new Promise((resolve, reject) => {\r\n        this.pgLoading = true\r\n        GetProcessListBase({\r\n          workshopId: workshopId,\r\n          type: this.isCom ? 1 : 2 // 0:全部，工艺类型1：构件工艺，2：零件工艺\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.options = res.Data.map(v => {\r\n              this.$set(v, 'disabled', false)\r\n              return v\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n          resolve()\r\n        }).finally(_ => {\r\n          this.pgLoading = false\r\n        })\r\n      })\r\n    },\r\n    selectChange(val, element) {\r\n      console.log('val', val)\r\n      console.log('element', element)\r\n      const arr = this.list.map(i => i.value)\r\n      this.options.forEach((item, index) => {\r\n        item.disabled = arr.includes(item.Code)\r\n        if (item.Code === val) {\r\n          element.Teams = item.Teams\r\n        }\r\n      })\r\n      if (element) {\r\n        element.date = this.processList[val]?.Finish_Date\r\n        if (!val) {\r\n          element.Working_Team_Id = ''\r\n          element.Teams = []\r\n        }\r\n      }\r\n      if (!element?.Working_Team_Id && element?.Teams?.length === 1) {\r\n        element.Working_Team_Id = element.Teams[0].Id\r\n      }\r\n    },\r\n    dateChange(val, element) {\r\n      const item = this.options.find(v => v.Code === element.value)\r\n      console.log('item', item, this.list)\r\n      let obj = {}\r\n      if (item) {\r\n        obj = {\r\n          Schduling_Id: this.formInline?.Schduling_Code,\r\n          Process_Id: item.Id,\r\n          Process_Code: item.Code,\r\n          Finish_Date: val\r\n        }\r\n      }\r\n      // this.$emit('setProcessList', { key: element.value, value: obj })\r\n    },\r\n    handleAdd(item) {\r\n      const arr = this.list.map(v => v.value)\r\n      this.options.forEach(v => {\r\n        if (arr.includes(v.Code)) {\r\n          v.disabled = true\r\n        }\r\n      })\r\n      this.list.push({\r\n        key: uuidv4(),\r\n        value: '',\r\n        Working_Team_Id: '',\r\n        Teams: [],\r\n        date: ''\r\n      })\r\n    },\r\n    handleDelete(element) {\r\n      const idx = this.list.findIndex(v => v.value === element.value)\r\n      if (idx !== -1) {\r\n        this.list.splice(idx, 1)\r\n        this.selectChange()\r\n      }\r\n    },\r\n    getWorkingTeam(teams, curItem) {\r\n      const newTeams = teams.filter(v => {\r\n        if (this.workshopId) {\r\n          return v.Workshop_Id === this.workshopId\r\n        }\r\n        return true\r\n      })\r\n      if (!newTeams.length) {\r\n        curItem.Working_Team_Id = ''\r\n        return []\r\n      }\r\n      if (newTeams.every(v => v.Id !== curItem.Working_Team_Id)) {\r\n        curItem.Working_Team_Id = ''\r\n      }\r\n      return newTeams\r\n    },\r\n    async setData(arr, technologyStr) {\r\n      await this.getCraftProcess()\r\n      let technologyArr = []\r\n      if (technologyStr) {\r\n        technologyArr = technologyStr.split('/')\r\n      }\r\n      const workshopId = arr[0].Workshop_Id\r\n      this.workshopId = workshopId\r\n\r\n      const partUsedProcess = arr[0].Part_Used_Process\r\n      await this.getProcessOption(workshopId)\r\n\r\n      this.options = this.options.filter(item => {\r\n        let flag = false\r\n        if (technologyArr.length && technologyArr.includes(item.Code)) {\r\n          flag = true\r\n        }\r\n        if (partUsedProcess && partUsedProcess === item.Code) {\r\n          flag = true\r\n        }\r\n        if (!flag) {\r\n          flag = !!item.Is_Enable\r\n        }\r\n        return flag\r\n      })\r\n      // this.defaultOptions = deepClone(this.options)\r\n\r\n      this.arr = arr || null\r\n      this.list = []\r\n      let codes = []\r\n      if (this.isCom) {\r\n        const origin = arr.map(v => (v?.Part_Used_Process || '').split(','))\r\n        codes = this.getUnique(origin.flat()).filter(v => !!v)\r\n\r\n        if (codes.length) {\r\n          // 零构件\r\n          const checkOption = codes.filter(c => {\r\n            return !!this.options.find(k => k.Code === c)\r\n          })\r\n          console.log(codes, checkOption, this.options)\r\n          if (checkOption.length < codes.length) {\r\n            this.$message({\r\n              message: '当前构件生产所属车间内没有该构件所属零件领用工序，请至车间管理内关联相关工序班组',\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n\r\n          codes.forEach((value, idx) => {\r\n            const obj = {\r\n              value,\r\n              isPart: true,\r\n              key: uuidv4(),\r\n              Working_Team_Id: this.processList[value]?.Working_Team_Id,\r\n              Teams: this.options.find(item => item.Code === value)?.Teams || [],\r\n              date: this.processList[value]?.Finish_Date\r\n            }\r\n            if (obj.Teams.length === 1 && !obj.Working_Team_Id) {\r\n              obj.Working_Team_Id = obj.Teams[0].Id\r\n            }\r\n            this.list.push(obj)\r\n          })\r\n        }\r\n      }\r\n      if (technologyArr.length) {\r\n        const techArr = technologyArr.map(v => {\r\n          const obj = {\r\n            key: uuidv4(),\r\n            value: v,\r\n            Working_Team_Id: this.processList[v]?.Working_Team_Id,\r\n            Teams: this.options.find(item => item.Code === v)?.Teams || [],\r\n            date: this.processList[v]?.Finish_Date\r\n          }\r\n          if (obj.Teams.length === 1 && !obj.Working_Team_Id) {\r\n            obj.Working_Team_Id = obj.Teams[0].Id\r\n          }\r\n          return obj\r\n        })\r\n        console.log('techArr', techArr)\r\n        techArr.forEach((element, idx) => {\r\n          if (!codes.includes(element.value)) {\r\n            this.list.push(element)\r\n          }\r\n        })\r\n      }\r\n      if (!this.list.length) {\r\n        this.list.push({\r\n          value: '',\r\n          key: uuidv4(),\r\n          Working_Team_Id: '',\r\n          Teams: [],\r\n          date: ''\r\n        })\r\n        if (this.isNest) {\r\n          const xur = this.options.filter(item => item.Is_Nest)\r\n          if (xur.length === 1) {\r\n            this.list[0].value = xur[0].Code\r\n          }\r\n        }\r\n      }\r\n      const indexMap = technologyArr.reduce((map, item, index) => {\r\n        map[item] = index\r\n        return map\r\n      }, {})\r\n\r\n      this.list.sort((item1, item2) => {\r\n        return indexMap[item1.value] - indexMap[item2.value]\r\n      })\r\n\r\n      this.selectChange()\r\n    },\r\n    getUnique(arr) {\r\n      return uniqueArr(arr)\r\n    },\r\n    submit() {\r\n      const list = this.list.map(item => item.value).filter(k => !!k)\r\n      const isTrue = this.checkCode(list)\r\n      if (!isTrue) {\r\n        this.$message({\r\n          message: '相邻工序不能相同',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      if (!list.length) {\r\n        this.$message({\r\n          message: '工序不能全为空',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n\r\n      if (this.isNest) {\r\n        const xur = this.options.filter(item => item.Is_Nest)\r\n        if (xur.length) {\r\n          const hasNest = xur.some(obj => list.includes(obj.Code))\r\n          if (!hasNest) {\r\n            this.$message({\r\n              message: '请至少选择一个套料工序！',\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n        }\r\n      }\r\n\r\n      this.btnLoading = true\r\n      const str = list.join('/')\r\n      this.list.forEach((element, idx) => {\r\n        const item = this.options.find(v => v.Code === element.value)\r\n\r\n        let obj = {}\r\n        if (item) {\r\n          obj = {\r\n            Schduling_Id: this.formInline?.Schduling_Code,\r\n            Process_Id: item.Id,\r\n            Process_Code: item.Code,\r\n            Finish_Date: element.date,\r\n            Working_Team_Id: element.Working_Team_Id\r\n          }\r\n        }\r\n        this.$emit('setProcessList', { key: element.value, value: obj })\r\n      })\r\n\r\n      this.$emit('sendProcess', { arr: this.arr, str })\r\n      this.btnLoading = false\r\n      this.handleClose()\r\n    },\r\n    craftChange(val) {\r\n      this.craftCode = val\r\n      if (!val) {\r\n        // this.options = this.defaultOptions\r\n        return\r\n      }\r\n      const info = this.gyList.find(v => v.Code === val)\r\n      if (info) {\r\n        const plist = info.WorkCode.split('/')\r\n        // this.options = this.defaultOptions.filter(v => plist.includes(v.Code))\r\n        this.options.forEach((item) => {\r\n          if (plist.includes(item.Code)) {\r\n            item.disabled = true\r\n          } else {\r\n            item.disabled = false\r\n          }\r\n        })\r\n        const newList = []\r\n        plist.forEach((listVal, idx) => {\r\n          const item = this.list.find(v => v.value === listVal)\r\n          if (item) {\r\n            if (item.Teams.length === 1 && !item.Working_Team_Id) {\r\n              item.Working_Team_Id = item.Teams[0].Id\r\n            }\r\n            newList.push(item)\r\n          } else {\r\n            const item2 = this.options.find(v => v.Code === listVal)\r\n            if (item2) {\r\n              const obj = {\r\n                key: uuidv4(),\r\n                value: item2.Code,\r\n                Working_Team_Id: '',\r\n                Teams: item2.Teams,\r\n                date: ''\r\n              }\r\n              if (item2.Teams.length === 1 && !obj.Working_Team_Id) {\r\n                obj.Working_Team_Id = item2.Teams[0].Id\r\n              }\r\n              newList.push(obj)\r\n            }\r\n          }\r\n        })\r\n        this.list = newList\r\n      }\r\n    },\r\n    handleClose() {\r\n      this.$emit('close')\r\n    },\r\n    checkCode(list) {\r\n      let flag = true\r\n      for (let i = 0; i < list.length; i++) {\r\n        if (i !== list.length - 1 && list[i] === list[i + 1]) {\r\n          flag = false\r\n          break\r\n        }\r\n      }\r\n      return flag\r\n    },\r\n    changeDraggable() {\r\n      this.list.forEach(v => {\r\n        this.$set(v, 'date', '')\r\n      })\r\n      this.initProcessList()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.btn-x {\r\n  margin-left: 20px;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n  margin-top: 30px;\r\n}\r\n\r\n.cs-drag {\r\n  line-height: 32px;\r\n  cursor: move;\r\n}\r\n\r\n.cs-option {\r\n  display: flex;\r\n  justify-content: space-between;\r\n\r\n  .cs-label {\r\n\r\n  }\r\n\r\n  .cs-tip {\r\n    color: #409EFF;\r\n  }\r\n}\r\n\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\AssociatedDevice.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\process-settings\\management\\component\\AssociatedDevice.vue", "mtime": 1757468113413}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["AssociatedDevice.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "AssociatedDevice.vue", "sourceRoot": "src/views/PRO/process-settings/management/component", "sourcesContent": ["<template>\r\n\r\n  <div>\r\n\r\n    <el-form label-width=\"80px\" :inline=\"true\">\r\n      <el-form-item>\r\n        <el-button type=\"primary\" @click=\"handleDevice\">关联设备</el-button>\r\n      </el-form-item>\r\n      <el-form-item label=\"设备名称\" prop=\"deviceName\">\r\n        <el-input v-model.trim=\"form.deviceName\" :clearbale=\"true\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"设备类型\" prop=\"deviceType\">\r\n        <el-select v-model=\"form.deviceType\" clearable placeholder=\"请选择\" style=\"width: 100%\" @change=\"deviceTypeChange\">\r\n          <el-option v-for=\"item in deviceTypeOptions\" :key=\"item.Id\" :label=\"item.Display_Name\" :value=\"item.Id\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"设备子类\" prop=\"Type_Detail_Id\">\r\n        <el-select v-model=\"form.Type_Detail_Id\" clearable placeholder=\"请选择\" :disabled=\"!form.deviceType\"\r\n          style=\"width: 100%\">\r\n          <el-option v-for=\"item in deviceItemTypeOptions\" :key=\"item.Id\" :label=\"item.Display_Name\" :value=\"item.Id\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"所属部门\" prop=\"department\">\r\n        <el-input v-model.trim=\"form.department\" clearbale />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\r\n        <el-button @click=\"reset\">重置</el-button>\r\n      </el-form-item>\r\n\r\n    </el-form>\r\n\r\n\r\n\r\n\r\n\r\n\r\n    <div v-loading=\"tbLoading\" class=\"fff cs-z-tb-wrapper\" style=\"height: calc(100vh - 320px);\">\r\n      <dynamic-data-table ref=\"dyTable\" :key=\"tableKey\" :columns=\"columns\" :config=\"tbConfig\" :data=\"tbData\"\r\n        :page=\"queryInfo.Page\" :total=\"total\" border stripe class=\"cs-plm-dy-table\"\r\n        @multiSelectedChange=\"handleSelectionChange\" @gridPageChange=\"gridPageChange\" @gridSizeChange=\"gridSizeChange\"\r\n        @tableSearch=\"tableSearch\">\r\n        <template slot=\"Director_UserName\" slot-scope=\"{ row }\">\r\n          <div>{{ row.Director_UserName || \"-\" }}</div>\r\n        </template>\r\n        <template slot=\"Working_Team_Names\" slot-scope=\"{ row }\">\r\n          <div>{{ row.Working_Team_Names || \"-\" }}</div>\r\n        </template>\r\n\r\n      </dynamic-data-table>\r\n    </div>\r\n  </div>\r\n\r\n\r\n</template>\r\n\r\n<script>\r\nimport getTbInfo from \"@/mixins/PRO/get-table-info\";\r\nimport DynamicDataTable from \"@/components/DynamicDataTable/DynamicDataTable\";\r\nimport TopHeader from \"@/components/TopHeader\";\r\n// import detail from \"./component/detail\";\r\nimport { GetDictionaryDetailListByCode, GetDictionaryDetailListByParentId, } from '@/api/sys'\r\nimport { GetEquipmentAssetPageList, AddWorkingProcess } from '@/api/PRO/technology-lib'\r\n\r\n\r\n\r\nexport default {\r\n  name: \"PROGroup\",\r\n\r\n  components: {\r\n    DynamicDataTable,\r\n    TopHeader,\r\n    // detail,\r\n  },\r\n  mixins: [getTbInfo],\r\n  props: {\r\n    rowData: {\r\n      type: Object,\r\n      default() {\r\n        return {}\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      tbConfig: {\r\n        Pager_Align: \"center\",\r\n      },\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: 10,\r\n      },\r\n      deviceItemTypeOptions: [],\r\n      form: {\r\n        deviceName: \"\",\r\n        deviceType: \"\",\r\n        Type_Detail_Id: \"\",\r\n        department: \"\",\r\n      },\r\n      deviceTypeOptions: [],\r\n      currentComponent: \"\",\r\n      title: \"\",\r\n      columns: [],\r\n      tbData: [],\r\n      total: 0,\r\n      tableKey: Math.random(),\r\n      tbLoading: false,\r\n      dialogVisible: false,\r\n      selectList: [],\r\n      keywords: \"\",\r\n    };\r\n  },\r\n  async created() {\r\n    this.tbLoading = true;\r\n    this.selectList = []\r\n    await this.getTableConfig(\"plm_device_list\");\r\n    this.getEquipmentAssetPageList();\r\n    this.getDictionaryDetailListByCode();\r\n  },\r\n  methods: {\r\n    clearSelec() {\r\n      this.$refs.dyTable.clearSelection()\r\n    },\r\n    deviceTypeChange(e) {\r\n      this.form.Type_Detail_Id = ''\r\n      this.deviceItemTypeOptions = []\r\n      GetDictionaryDetailListByParentId(e).then((res) => {\r\n        this.deviceItemTypeOptions = res.Data;\r\n      });\r\n    },\r\n    // 获取设备类型\r\n    async getDictionaryDetailListByCode() {\r\n      await GetDictionaryDetailListByCode({ dictionaryCode: 'deviceType' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.deviceTypeOptions = res.Data || [];\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      console.log(' this.optionsGroupList', this.optionsGroupList)\r\n    },\r\n    async getEquipmentAssetPageList() {\r\n\r\n      await GetEquipmentAssetPageList({\r\n        Display_Name: this.form.deviceName,\r\n        Device_Type_Id: this.form.deviceType,\r\n        Device_Type_Detail_Id: this.form.Type_Detail_Id,\r\n        Department: this.form.department,\r\n        Page: this.queryInfo.Page,\r\n        PageSize: this.queryInfo.PageSize,\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.Data;\r\n          this.total = res.Data.TotalCount;\r\n          this.tbLoading = false;\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      console.log(' this.optionsGroupList', this.optionsGroupList)\r\n    },\r\n    gridPageChange({ page }) {\r\n      this.queryInfo.Page = Number(page);\r\n      this.getEquipmentAssetPageList();\r\n    },\r\n    gridSizeChange({ size }) {\r\n      this.queryInfo.PageSize = Number(size);\r\n      this.queryInfo.Page = 1;\r\n      this.getEquipmentAssetPageList();\r\n    },\r\n    handleDevice() {\r\n      if (this.selectList.length === 0) {\r\n        this.$message({\r\n          message: '请选择设备',\r\n          type: 'error'\r\n        })\r\n        return\r\n      } else {\r\n        \r\n        this.rowData.Device_Ids = this.selectList\r\n        console.log(this.rowData,'this.rowData');\r\n\r\n        AddWorkingProcess(this.rowData).then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '关联成功',\r\n              type: 'success'\r\n            })\r\n            this.$emit(\"fetchData\");\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n\r\n    },\r\n\r\n\r\n\r\n\r\n    handleSelectionChange(list) {\r\n      this.selectList = list.map((i) => i.Id);\r\n      console.log(this.selectList, 'this.selectList')\r\n    },\r\n    handleAdd() {\r\n      this.currentComponent = \"detail\";\r\n      this.title = \"新增车间\";\r\n      this.dialogVisible = true;\r\n    },\r\n    handleEdit(row) {\r\n      this.currentComponent = \"detail\";\r\n      this.title = \"编辑车间\";\r\n      this.dialogVisible = true;\r\n      this.$nextTick((_) => {\r\n        this.$refs[\"content\"].initData(row);\r\n      });\r\n    },\r\n    // handleDetail(row) {\r\n    //   this.currentComponent = \"info\";\r\n    //   this.title = \"查看\";\r\n    //   this.dialogVisible = true;\r\n    //   this.$nextTick((_) => {\r\n    //     this.$refs[\"content\"].initData(row.Id);\r\n    //   });\r\n    // },\r\n\r\n    handleSearch() {\r\n      this.getEquipmentAssetPageList();\r\n      this.queryInfo.Page = 1;\r\n    },\r\n    reset() {\r\n      this.form = {}\r\n      this.getEquipmentAssetPageList();\r\n      this.queryInfo.Page = 1;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n// .cs-dialog {\r\n//   ::v-deep {\r\n//     .el-dialog__body {\r\n//       padding-top: 0;\r\n//     }\r\n//   }\r\n// }\r\n::v-deep {\r\n  .cs-top-header-box {\r\n    line-height: 0px;\r\n  }\r\n}\r\n\r\n::v-deep .pagination {\r\n  justify-content: flex-end !important;\r\n  margin-top: 12px !important;\r\n\r\n  .el-input--small .el-input__inner {\r\n    height: 28px;\r\n    line-height: 28px;\r\n  }\r\n}\r\n</style>\r\n"]}]}
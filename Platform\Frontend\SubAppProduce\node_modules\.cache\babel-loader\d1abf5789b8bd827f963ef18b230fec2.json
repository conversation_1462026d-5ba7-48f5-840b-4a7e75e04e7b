{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\ProjectAddDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\ProjectAddDialog.vue", "mtime": 1757991910768}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetProcessOfProjectList", "SyncProjectProcessFromProject", "components", "props", "sysProjectId", "type", "String", "default", "data", "btnLoading", "projectList", "form", "From_Sys_Project_Id", "mounted", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getProcessOfProjectList", "stop", "methods", "_this2", "_callee2", "res", "_callee2$", "_context2", "sent", "IsSucceed", "Data", "handleSubmit", "_this3", "$message", "warning", "To_Sys_Project_Id", "then", "$emit", "success", "error", "msg"], "sources": ["src/views/PRO/project-config/project-quality/components/Dialog/ProjectAddDialog.vue"], "sourcesContent": ["<template>\n  <div class=\"form-wrapper\">\n    <div class=\"instruction\">请选择项目，添加所选项目的<span>所有</span></div>\n    <div>\n      <el-form ref=\"form\" :model=\"form\" label-width=\"82px\">\n        <el-form-item v-if=\"!projectList.length\" label=\"项目名称：\">\n          <div>暂无可同步的项目</div>\n        </el-form-item>\n        <el-form-item v-else label=\"项目名称：\">\n          <el-select v-model=\"form.From_Sys_Project_Id\" placeholder=\"请选择项目\" style=\"width: 300px\">\n            <el-option v-for=\"(item, index) in projectList\" :key=\"index\" :label=\"item.Short_Name\" :value=\"item.Sys_Project_Id\" :disabled=\"item.Sys_Project_Id===sysProjectId\" />\n          </el-select>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"btn-x\">\n      <el-button @click=\"$emit('close')\">取 消</el-button>\n      <el-button v-if=\"projectList.length\" type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { GetProcessOfProjectList, SyncProjectProcessFromProject } from '@/api/PRO/technology-lib'\nexport default {\n  components: {\n  },\n  props: {\n    sysProjectId: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      btnLoading: false,\n      projectList: [],\n      form: {\n        From_Sys_Project_Id: ''\n      }\n    }\n  },\n  async mounted() {\n    await this.getProcessOfProjectList()\n  },\n  methods: {\n    async getProcessOfProjectList() {\n      const res = await GetProcessOfProjectList({ })\n      if (res.IsSucceed) {\n        this.projectList = res.Data\n      }\n    },\n    handleSubmit() {\n      if (this.form.From_Sys_Project_Id === '') return this.$message.warning('请选择项目')\n      this.btnLoading = true\n      SyncProjectProcessFromProject({\n        From_Sys_Project_Id: this.form.From_Sys_Project_Id,\n        To_Sys_Project_Id: this.sysProjectId\n      }).then(res => {\n        if (res.IsSucceed) {\n          this.$emit('refresh')\n          this.$emit('close')\n          this.$message.success('同步成功！')\n        } else {\n          this.$message.error(res.msg)\n        }\n        this.btnLoading = false\n      })\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n  .form-wrapper {\n    display: flex;\n    flex-direction: column;\n    overflow: hidden;\n    max-height: 70vh;\n\n    .btn-x {\n      padding-top: 16px;\n      text-align: right;\n    }\n    .instruction {\n      background: #f0f9ff;\n      border: 1px solid #b3d8ff;\n      color: #1890ff;\n      padding: 12px 16px;\n      border-radius: 4px;\n      margin-bottom: 16px;\n      font-weight: 500;\n    }\n  }\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,SAAAA,uBAAA,EAAAC,6BAAA;AACA;EACAC,UAAA,GACA;EACAC,KAAA;IACAC,YAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,WAAA;MACAC,IAAA;QACAC,mBAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACAT,KAAA,CAAAU,uBAAA;UAAA;UAAA;YAAA,OAAAH,QAAA,CAAAI,IAAA;QAAA;MAAA,GAAAP,OAAA;IAAA;EACA;EACAQ,OAAA;IACAF,uBAAA,WAAAA,wBAAA;MAAA,IAAAG,MAAA;MAAA,OAAAZ,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAW,SAAA;QAAA,IAAAC,GAAA;QAAA,OAAAb,mBAAA,GAAAG,IAAA,UAAAW,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAT,IAAA,GAAAS,SAAA,CAAAR,IAAA;YAAA;cAAAQ,SAAA,CAAAR,IAAA;cAAA,OACAvB,uBAAA;YAAA;cAAA6B,GAAA,GAAAE,SAAA,CAAAC,IAAA;cACA,IAAAH,GAAA,CAAAI,SAAA;gBACAN,MAAA,CAAAjB,WAAA,GAAAmB,GAAA,CAAAK,IAAA;cACA;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAAN,IAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IACA;IACAO,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,SAAAzB,IAAA,CAAAC,mBAAA,qBAAAyB,QAAA,CAAAC,OAAA;MACA,KAAA7B,UAAA;MACAR,6BAAA;QACAW,mBAAA,OAAAD,IAAA,CAAAC,mBAAA;QACA2B,iBAAA,OAAAnC;MACA,GAAAoC,IAAA,WAAAX,GAAA;QACA,IAAAA,GAAA,CAAAI,SAAA;UACAG,MAAA,CAAAK,KAAA;UACAL,MAAA,CAAAK,KAAA;UACAL,MAAA,CAAAC,QAAA,CAAAK,OAAA;QACA;UACAN,MAAA,CAAAC,QAAA,CAAAM,KAAA,CAAAd,GAAA,CAAAe,GAAA;QACA;QACAR,MAAA,CAAA3B,UAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckNode.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckNode.vue", "mtime": 1758099078953}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["CheckNode.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CheckNode.vue", "sourceRoot": "src/views/PRO/project-config/project-quality/components", "sourcesContent": ["<template>\n  <div style=\"height: calc(100vh - 300px)\">\n    <vxe-table\n      v-loading=\"tbLoading\"\n      :empty-render=\"{name: 'NotData'}\"\n      show-header-overflow\n      element-loading-spinner=\"el-icon-loading\"\n      element-loading-text=\"拼命加载中\"\n      empty-text=\"暂无数据\"\n      height=\"100%\"\n      :data=\"tbData\"\n      stripe\n      resizable\n      :auto-resize=\"true\"\n      class=\"cs-vxe-table\"\n      :tooltip-config=\"{ enterable: true }\"\n    >\n      <!-- <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" /> -->\n      <vxe-column\n        v-for=\"(item, index) in columns\"\n        :key=\"index\"\n        :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n        show-overflow=\"tooltip\"\n        sortable\n        :align=\"item.Align\"\n        :field=\"item.Code\"\n        :title=\"item.Display_Name\"\n      >\n        <template #default=\"{ row }\">\n          <span v-if=\"item.Code === 'Is_Special_Check'\">\n            <el-tag v-if=\"row.Is_Special_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n          </span>\n          <span v-else-if=\"item.Code === 'Is_Inter_Check'\">\n            <el-tag v-if=\"row.Is_Inter_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n          </span>\n          <span v-else-if=\"item.Code === 'Is_Self_Check'\">\n            <el-tag v-if=\"row.Is_Self_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n          </span>\n          <span v-else>{{ row[item.Code] | displayValue }}</span>\n        </template>\n      </vxe-column>\n      <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" show-overflow align=\"center\">\n        <template #default=\"{ row }\">\n          <el-button type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\n          <!-- <el-button v-if=\"!row.Node_Code||(row.Node_Code&&row.Check_Style === '抽检')\" type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\n          <el-divider v-if=\"!row.Node_Code\" direction=\"vertical\" />\n          <el-button v-if=\"!row.Node_Code\" type=\"text\" @click=\"removeEvent(row)\">删除</el-button> -->\n        </template>\n      </vxe-column>\n    </vxe-table>\n  </div>\n</template>\n\n<script>\nimport { GetGridByCode } from '@/api/sys'\nimport { DelNode } from '@/api/PRO/factorycheck'\nimport { GetNodeList } from '@/api/PRO/factorycheck'\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\nimport { timeFormat } from '@/filters'\nexport default {\n  props: {\n    checkType: {\n      type: Object,\n      default: () => ({})\n    },\n    sysProjectId: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      tbData: [],\n      columns: [],\n      tbLoading: false\n    }\n  },\n  watch: {\n    checkType: {\n      handler(newName, oldName) {\n        this.checkType = newName\n        if (this.sysProjectId) {\n          this.getNodeList()\n        }\n      },\n      deep: true\n    },\n    sysProjectId: {\n      handler(newVal) {\n        if (newVal && this.checkType && this.checkType.Id) {\n          this.getNodeList()\n        }\n      },\n      immediate: true\n    }\n  },\n  mounted() {\n    // this.getNodeList()\n    this.getTypeList()\n  },\n  methods: {\n    async getTypeList() {\n      const res = await GetFactoryProfessionalByCode({\n        factoryId: localStorage.getItem('CurReferenceId')\n      })\n      const data = res.Data\n      if (res.IsSucceed) {\n        this.typeOption = Object.freeze(data)\n        console.log(this.typeOption)\n        if (this.typeOption.length > 0) {\n          this.TypeId = this.typeOption[0]?.Id\n          this.fetchData()\n        }\n      } else {\n        this.$message({\n          message: res.Message,\n          type: 'error'\n        })\n      }\n    },\n    fetchData() {\n      this.getTableConfig('Quality_Inspection_Node')\n      //   this.tbLoading = true;\n    },\n    getNodeList() {\n      this.tbLoading = true\n      GetNodeList({ check_object_id: this.checkType.Id, Bom_Level: this.checkType.Code, sysProjectId: this.sysProjectId }).then((res) => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data.map(v => {\n            switch (v.Check_Style) {\n              case 0 : v.Check_Style = '抽检'; break // 谁写的，坑死了\n              case 1 : v.Check_Style = '全检'; break\n              default: v.Check_Style = ''\n            }\n            switch (v.Check_Type) {\n              case 1 : v.Check_Type = '质量'; break\n              case 2 : v.Check_Type = '探伤'; break\n              case -1 : v.Check_Type = '质量/探伤'; break\n              default: v.Check_Type = ''\n            }\n            v.Create_Date = timeFormat(v.Create_Date, '{y}-{m}-{d} {h}:{i}:{s}')\n            return v\n          })\n          console.log(res.Data)\n          this.tbLoading = false\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n          this.tbLoading = false\n        }\n      })\n    },\n    getTableConfig(code) {\n      GetGridByCode({ code: code + ',' + this.typeOption.find((i) => i.Id === this.TypeId).Code }).then((res) => {\n        const { IsSucceed, Data, Message } = res\n        if (IsSucceed) {\n          if (!Data) {\n            this.$message.error('当前专业没有配置相对应表格')\n            this.tbLoading = true\n            return\n          }\n          const list = Data.ColumnList || []\n          this.columns = list\n          console.log(this.columns)\n          this.tbLoading = false\n        } else {\n          this.$message({\n            message: Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    // 删除单个检查项组合\n    removeEvent(row) {\n      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DelNode({ id: row.Id }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getNodeList()\n            } else {\n              this.$message({\n                type: 'error',\n                message: res.Message\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    },\n\n    // 编辑每行信息\n    editEvent(row) {\n      // 获取每行内容\n      console.log('row', row)\n      this.$emit('NodeEdit', row)\n    }\n  }\n}\n</script>\n\n<style scoped></style>\n"]}]}
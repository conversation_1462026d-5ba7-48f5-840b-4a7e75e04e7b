{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckNode.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckNode.vue", "mtime": 1758085378559}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["CheckNode.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CheckNode.vue", "sourceRoot": "src/views/PRO/project-config/project-quality/components", "sourcesContent": ["<template>\n  <div style=\"height: calc(100vh - 300px)\">\n    <vxe-table\n      v-loading=\"tbLoading\"\n      :empty-render=\"{name: 'NotData'}\"\n      show-header-overflow\n      element-loading-spinner=\"el-icon-loading\"\n      element-loading-text=\"拼命加载中\"\n      empty-text=\"暂无数据\"\n      height=\"100%\"\n      :data=\"tbData\"\n      stripe\n      resizable\n      :auto-resize=\"true\"\n      class=\"cs-vxe-table\"\n      :tooltip-config=\"{ enterable: true }\"\n    >\n      <!-- <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" /> -->\n      <vxe-column\n        v-for=\"(item, index) in columns\"\n        :key=\"index\"\n        :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n        show-overflow=\"tooltip\"\n        sortable\n        :align=\"item.Align\"\n        :field=\"item.Code\"\n        :title=\"item.Display_Name\"\n      >\n        <template #default=\"{ row }\">\n          <span v-if=\"item.Code === 'Is_Special_Check'\">\n            <el-tag v-if=\"row.Is_Special_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n          </span>\n          <span v-else-if=\"item.Code === 'Is_Inter_Check'\">\n            <el-tag v-if=\"row.Is_Inter_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n          </span>\n          <span v-else-if=\"item.Code === 'Is_Self_Check'\">\n            <el-tag v-if=\"row.Is_Self_Check\" type=\"success\">是</el-tag><el-tag v-else type=\"danger\">否</el-tag>\n          </span>\n          <span v-else>{{ row[item.Code] | displayValue }}</span>\n        </template>\n      </vxe-column>\n      <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" show-overflow align=\"center\">\n        <template #default=\"{ row }\">\n          <el-button v-if=\"!row.Node_Code||(row.Node_Code&&row.Check_Style === '抽检')\" type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\n          <el-divider v-if=\"!row.Node_Code\" direction=\"vertical\" />\n          <el-button v-if=\"!row.Node_Code\" type=\"text\" @click=\"removeEvent(row)\">删除</el-button>\n        </template>\n      </vxe-column>\n    </vxe-table>\n  </div>\n</template>\n\n<script>\nimport { GetGridByCode } from '@/api/sys'\nimport { DelNode } from '@/api/PRO/factorycheck'\nimport { GetNodeList } from '@/api/PRO/factorycheck'\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\nimport { timeFormat } from '@/filters'\nexport default {\n  props: {\n    checkType: {\n      type: Object,\n      default: () => ({})\n    },\n    sysProjectId: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      tbData: [],\n      columns: [],\n      tbLoading: false\n    }\n  },\n  watch: {\n    checkType: {\n      handler(newName, oldName) {\n        this.checkType = newName\n        this.getNodeList()\n      },\n      deep: true\n    }\n  },\n  mounted() {\n    // this.getNodeList()\n    this.getTypeList()\n  },\n  methods: {\n    async getTypeList() {\n      const res = await GetFactoryProfessionalByCode({\n        factoryId: localStorage.getItem('CurReferenceId')\n      })\n      const data = res.Data\n      if (res.IsSucceed) {\n        this.typeOption = Object.freeze(data)\n        console.log(this.typeOption)\n        if (this.typeOption.length > 0) {\n          this.TypeId = this.typeOption[0]?.Id\n          this.fetchData()\n        }\n      } else {\n        this.$message({\n          message: res.Message,\n          type: 'error'\n        })\n      }\n    },\n    fetchData() {\n      this.getTableConfig('Quality_Inspection_Node')\n      //   this.tbLoading = true;\n    },\n    getNodeList() {\n      this.tbLoading = true\n      GetNodeList({ check_object_id: this.checkType.Id, Bom_Level: this.checkType.Code, sysProjectId: this.sysProjectId }).then((res) => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data.map(v => {\n            switch (v.Check_Style) {\n              case 0 : v.Check_Style = '抽检'; break // 谁写的，坑死了\n              case 1 : v.Check_Style = '全检'; break\n              default: v.Check_Style = ''\n            }\n            switch (v.Check_Type) {\n              case 1 : v.Check_Type = '质量'; break\n              case 2 : v.Check_Type = '探伤'; break\n              case -1 : v.Check_Type = '质量/探伤'; break\n              default: v.Check_Type = ''\n            }\n            v.Create_Date = timeFormat(v.Create_Date, '{y}-{m}-{d} {h}:{i}:{s}')\n            return v\n          })\n          console.log(res.Data)\n          this.tbLoading = false\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n          this.tbLoading = false\n        }\n      })\n    },\n    getTableConfig(code) {\n      GetGridByCode({ code: code + ',' + this.typeOption.find((i) => i.Id === this.TypeId).Code }).then((res) => {\n        const { IsSucceed, Data, Message } = res\n        if (IsSucceed) {\n          if (!Data) {\n            this.$message.error('当前专业没有配置相对应表格')\n            this.tbLoading = true\n            return\n          }\n          const list = Data.ColumnList || []\n          this.columns = list\n          console.log(this.columns)\n          this.tbLoading = false\n        } else {\n          this.$message({\n            message: Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    // 删除单个检查项组合\n    removeEvent(row) {\n      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DelNode({ id: row.Id }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getNodeList()\n            } else {\n              this.$message({\n                type: 'error',\n                message: res.Message\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    },\n\n    // 编辑每行信息\n    editEvent(row) {\n      // 获取每行内容\n      console.log('row', row)\n      this.$emit('NodeEdit', row)\n    }\n  }\n}\n</script>\n\n<style scoped></style>\n"]}]}
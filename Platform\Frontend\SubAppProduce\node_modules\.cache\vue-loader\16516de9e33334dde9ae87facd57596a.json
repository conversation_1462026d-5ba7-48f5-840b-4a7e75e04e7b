{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\CheckCombination.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\CheckCombination.vue", "mtime": 1757919153145}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["CheckCombination.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CheckCombination.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup/components", "sourcesContent": ["<template>\n  <div style=\"height: calc(100vh - 300px)\">\n    <vxe-table\n      v-loading=\"tbLoading\"\n      :empty-render=\"{name: 'NotData'}\"\n      show-header-overflow\n      element-loading-spinner=\"el-icon-loading\"\n      element-loading-text=\"拼命加载中\"\n      empty-text=\"暂无数据\"\n      height=\"100%\"\n      :data=\"tbData\"\n      stripe\n      resizable\n      :auto-resize=\"true\"\n      class=\"cs-vxe-table\"\n      :tooltip-config=\"{ enterable: true }\"\n    >\n      <!-- <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" /> -->\n      <vxe-column\n        v-for=\"(item, index) in columns\"\n        :key=\"index\"\n        :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n        show-overflow=\"tooltip\"\n        sortable\n        :align=\"item.Align\"\n        :field=\"item.Code\"\n        :title=\"item.Display_Name\"\n      >\n        <template #default=\"{ row }\">\n          <span>{{ row[item.Code] || '-' }}</span>\n        </template>\n      </vxe-column>\n      <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" align=\"center\" show-overflow>\n        <template #default=\"{ row }\">\n          <el-button type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\n          <el-divider direction=\"vertical\" />\n          <el-button type=\"text\" @click=\"removeEvent(row)\">删除</el-button>\n        </template>\n      </vxe-column>\n    </vxe-table>\n  </div>\n</template>\n\n<script>\nimport { GetGridByCode } from '@/api/sys'\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\nimport { QualityList } from '@/api/PRO/factorycheck'\nimport { DelQualityList } from '@/api/PRO/factorycheck'\nimport { timeFormat } from '@/filters'\nexport default {\n  props: {\n    checkType: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      columns: null,\n      tbLoading: false,\n      TypeId: '',\n      typeOption: '',\n      tbData: []\n\n    }\n  },\n  watch: {\n    checkType: {\n      handler(newName, oldName) {\n        this.checkType = newName\n        this.getQualityList()\n      },\n      deep: true\n    }\n  },\n  mounted() {\n    // this.getQualityList()\n    this.getTypeList()\n  },\n  methods: {\n    async getTypeList() {\n      let res = null\n      let data = null\n      res = await GetFactoryProfessionalByCode({\n        factoryId: localStorage.getItem('CurReferenceId')\n      })\n      data = res.Data\n      if (res.IsSucceed) {\n        this.typeOption = Object.freeze(data)\n        console.log(this.typeOption)\n        if (this.typeOption.length > 0) {\n          this.TypeId = this.typeOption[0]?.Id\n          this.fetchData()\n        }\n      } else {\n        this.$message({\n          message: res.Message,\n          type: 'error'\n        })\n      }\n    },\n    async fetchData() {\n      await this.getTableConfig('Check_item_combination')\n    //   this.tbLoading = true;\n    },\n    // 获取列表\n    getTableConfig(code) {\n      GetGridByCode({ code: code + ',' + this.typeOption.find((i) => i.Id === this.TypeId).Code }).then((res) => {\n        const { IsSucceed, Data, Message } = res\n        if (IsSucceed) {\n          if (!Data) {\n            this.$message.error('当前专业没有配置相对应表格')\n            this.tbLoading = true\n            return\n          }\n          this.tbLoading = false\n          const list = Data.ColumnList || []\n          this.columns = list.filter((v) => v.Is_Display).map((item) => {\n            if (item.Code === 'CheckName') {\n              item.fixed = 'left'\n            }\n            return item\n          })\n          console.log(this.columns)\n        } else {\n          this.$message({\n            message: Message,\n            type: 'error'\n          })\n        }\n      })\n    },\n    // 获取检查项组合列表\n    getQualityList() {\n      this.tbLoading = true\n      QualityList({ check_object_id: this.checkType.Id, Bom_Level: this.checkType.Code }).then((res) => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data.map((v) => {\n            switch (v.Check_Type) {\n              case 1 : v.Check_Type = '质量'; break\n              case 2 : v.Check_Type = '探伤'; break\n              case -1 : v.Check_Type = '质量、探伤'; break\n              default: v.Check_Type = ''\n            }\n            v.Create_Date = timeFormat(v.Create_Date, '{y}-{m}-{d} {h}:{i}:{s}')\n            return v\n          })\n          this.tbLoading = false\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n          this.tbLoading = false\n        }\n      })\n    },\n    // 删除单个检查项组合\n    removeEvent(row) {\n      console.log(row)\n      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          DelQualityList({ id: row.Id }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getQualityList()\n            } else {\n              this.$message({\n                type: 'error',\n                message: res.Message\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    },\n    // 编辑每行信息\n    editEvent(row) {\n      // 获取每行内容\n      console.log('row', row)\n      this.$emit('CombinationEdit', row)\n    }\n  }\n}\n</script>\n\n<style scoped></style>\n"]}]}
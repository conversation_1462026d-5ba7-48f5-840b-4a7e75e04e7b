{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\CheckCombination.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\CheckCombination.vue", "mtime": 1757468112631}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["CheckCombination.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CheckCombination.vue", "sourceRoot": "src/views/PRO/factoryQuality/checkoutGroup/components", "sourcesContent": ["<template>\r\n  <div style=\"height: calc(100vh - 300px)\">\r\n    <vxe-table\r\n      v-loading=\"tbLoading\"\r\n      :empty-render=\"{name: 'NotData'}\"\r\n      show-header-overflow\r\n      element-loading-spinner=\"el-icon-loading\"\r\n      element-loading-text=\"拼命加载中\"\r\n      empty-text=\"暂无数据\"\r\n      height=\"100%\"\r\n      :data=\"tbData\"\r\n      stripe\r\n      resizable\r\n      :auto-resize=\"true\"\r\n      class=\"cs-vxe-table\"\r\n      :tooltip-config=\"{ enterable: true }\"\r\n    >\r\n      <!-- <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" /> -->\r\n      <vxe-column\r\n        v-for=\"(item, index) in columns\"\r\n        :key=\"index\"\r\n        :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n        show-overflow=\"tooltip\"\r\n        sortable\r\n        :align=\"item.Align\"\r\n        :field=\"item.Code\"\r\n        :title=\"item.Display_Name\"\r\n      >\r\n        <template #default=\"{ row }\">\r\n          <span>{{ row[item.Code] || '-' }}</span>\r\n        </template>\r\n      </vxe-column>\r\n      <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" align=\"center\" show-overflow>\r\n        <template #default=\"{ row }\">\r\n          <el-button type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\r\n          <el-divider direction=\"vertical\" />\r\n          <el-button type=\"text\" @click=\"removeEvent(row)\">删除</el-button>\r\n        </template>\r\n      </vxe-column>\r\n    </vxe-table>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\r\nimport { QualityList } from '@/api/PRO/factorycheck'\r\nimport { DelQualityList } from '@/api/PRO/factorycheck'\r\nimport { timeFormat } from '@/filters'\r\nexport default {\r\n  props: {\r\n    checkType: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      columns: null,\r\n      tbLoading: false,\r\n      TypeId: '',\r\n      typeOption: '',\r\n      tbData: []\r\n\r\n    }\r\n  },\r\n  watch: {\r\n    checkType: {\r\n      handler(newName, oldName) {\r\n        this.checkType = newName\r\n        this.getQualityList()\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getQualityList()\r\n    this.getTypeList()\r\n  },\r\n  methods: {\r\n    async getTypeList() {\r\n      let res = null\r\n      let data = null\r\n      res = await GetFactoryProfessionalByCode({\r\n        factoryId: localStorage.getItem('CurReferenceId')\r\n      })\r\n      data = res.Data\r\n      if (res.IsSucceed) {\r\n        this.typeOption = Object.freeze(data)\r\n        console.log(this.typeOption)\r\n        if (this.typeOption.length > 0) {\r\n          this.TypeId = this.typeOption[0]?.Id\r\n          this.fetchData()\r\n        }\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    async fetchData() {\r\n      await this.getTableConfig('Check_item_combination')\r\n    //   this.tbLoading = true;\r\n    },\r\n    // 获取列表\r\n    getTableConfig(code) {\r\n      GetGridByCode({ code: code + ',' + this.typeOption.find((i) => i.Id === this.TypeId).Code }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          if (!Data) {\r\n            this.$message.error('当前专业没有配置相对应表格')\r\n            this.tbLoading = true\r\n            return\r\n          }\r\n          this.tbLoading = false\r\n          const list = Data.ColumnList || []\r\n          this.columns = list.filter((v) => v.Is_Display).map((item) => {\r\n            if (item.Code === 'CheckName') {\r\n              item.fixed = 'left'\r\n            }\r\n            return item\r\n          })\r\n          console.log(this.columns)\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 获取检查项组合列表\r\n    getQualityList() {\r\n      this.tbLoading = true\r\n      QualityList({ check_object_id: this.checkType.Id, Bom_Level: this.checkType.Code }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data.map((v) => {\r\n            switch (v.Check_Type) {\r\n              case 1 : v.Check_Type = '质量'; break\r\n              case 2 : v.Check_Type = '探伤'; break\r\n              case -1 : v.Check_Type = '质量、探伤'; break\r\n              default: v.Check_Type = ''\r\n            }\r\n            v.Create_Date = timeFormat(v.Create_Date, '{y}-{m}-{d} {h}:{i}:{s}')\r\n            return v\r\n          })\r\n          this.tbLoading = false\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n          this.tbLoading = false\r\n        }\r\n      })\r\n    },\r\n    // 删除单个检查项组合\r\n    removeEvent(row) {\r\n      console.log(row)\r\n      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          DelQualityList({ id: row.Id }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                type: 'success',\r\n                message: '删除成功!'\r\n              })\r\n              this.getQualityList()\r\n            } else {\r\n              this.$message({\r\n                type: 'error',\r\n                message: res.Message\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n    // 编辑每行信息\r\n    editEvent(row) {\r\n      // 获取每行内容\r\n      console.log('row', row)\r\n      this.$emit('CombinationEdit', row)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped></style>\r\n"]}]}
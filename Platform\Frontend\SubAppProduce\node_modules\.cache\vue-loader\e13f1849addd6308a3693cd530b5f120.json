{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\CombinationDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\Dialog\\CombinationDialog.vue", "mtime": 1758097916777}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IEFkZENoZWNrSXRlbUNvbWJpbmF0aW9uIH0gZnJvbSAnQC9hcGkvUFJPL2ZhY3RvcnljaGVjaycKaW1wb3J0IHsgRW50aXR5UXVhbGl0eUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJwoKaW1wb3J0IHsgR2V0Q2hlY2tUeXBlTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snCmltcG9ydCB7IEdldENoZWNrSXRlbUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJwppbXBvcnQgeyBHZXROb2RlTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snCmltcG9ydCB7IEdldENvbXBUeXBlVHJlZSB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snCmltcG9ydCB7CiAgR2V0RmFjdG9yeVByb2Zlc3Npb25hbEJ5Q29kZSwKICBHZXRNYXRlcmlhbFR5cGUKfSBmcm9tICdAL2FwaS9QUk8vZmFjdG9yeWNoZWNrJwppbXBvcnQgeyBHZXRQYXJ0VHlwZVRyZWUgfSBmcm9tICdAL2FwaS9QUk8vcGFydFR5cGUnCgpleHBvcnQgZGVmYXVsdCB7CiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIG1vZGU6ICcnLCAvLyDljLrliIbpobnnm67lkozlt6XljoIKICAgICAgUHJvamVjdElkOiAnJywgLy8g6aG555uuSWQKICAgICAgQ2hlY2tfT2JqZWN0X0lkOiAnJywKICAgICAgY2hlY2tUeXBlOiB7fSwgLy8g5Yy65YiG5p6E5Lu244CB6Zu25Lu244CB54mp5paZCiAgICAgIGZvcm06IHsKICAgICAgICBPYmplY3RfVHlwZV9JZHM6IFtdCiAgICAgIH0sCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgQ2hlY2tfQ29udGVudDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOihqOWNlScsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXSwKICAgICAgICBFbGlnaWJpbGl0eV9Dcml0ZXJpYTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOihqOWNlScsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXSwKICAgICAgICBHcm91cF9OYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+35aGr5YaZ5a6M5pW06KGo5Y2VJywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICAgIENoZWNrX1R5cGU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnY2hhbmdlJyB9CiAgICAgICAgXSwKICAgICAgICBRdWVzdGlvbmxhYl9JZHM6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7floavlhpnlrozmlbTooajljZUnLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgdGl0bGU6ICcnLAogICAgICBvcHRpb25zOiBbXSwKICAgICAgUHJvY2Vzc0Zsb3c6IFtdLAogICAgICBDaGVja1R5cGVMaXN0OiBbXSwgLy8g5qOA5p+l57G75Z6L5LiL5ouJCiAgICAgIENoZWNrSXRlbUxpc3Q6IFtdLCAvLyDmo4Dmn6XpobnkuIvmi4kKICAgICAgQ2hhbmdlX0NoZWNrX1R5cGU6IFtdLAogICAgICBRdWFsaXR5VHlwZUxpc3Q6IFsKICAgICAgICB7CiAgICAgICAgICBOYW1lOiAn6LSo6YePJywKICAgICAgICAgIElkOiAxCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBOYW1lOiAn5o6i5LykJywKICAgICAgICAgIElkOiAyCiAgICAgICAgfQogICAgICBdLCAvLyDotKjmo4DnsbvlnosKICAgICAgUHJvQ2F0ZWdvcnlMaXN0OiBbXSwgLy8g5LiT5Lia57G75Yir5LiL5ouJCiAgICAgIENoZWNrTm9kZUxpc3Q6IFtdLCAvLyDotKjmo4DoioLngrnkuIvmi4kKICAgICAgdmVyaWZpY2F0aW9uOiBmYWxzZSwKICAgICAgUHJvQ2F0ZWdvcnlDb2RlOiAnJywgLy8g5LiT5Lia57G75YirQ29kZQogICAgICBFbGlnaWJpbGl0eV9Dcml0ZXJpYTogJycsCiAgICAgIE9iamVjdFR5cGVMaXN0OiB7CiAgICAgICAgLy8g5a+56LGh57G75Z6LCiAgICAgICAgJ2NoZWNrLXN0cmljdGx5JzogdHJ1ZSwKICAgICAgICAnZGVmYXVsdC1leHBhbmQtYWxsJzogdHJ1ZSwKICAgICAgICBmaWx0ZXJhYmxlOiBmYWxzZSwKICAgICAgICBjbGlja1BhcmVudDogdHJ1ZSwKICAgICAgICBkYXRhOiBbXSwKICAgICAgICBwcm9wczogewogICAgICAgICAgY2hpbGRyZW46ICdDaGlsZHJlbicsCiAgICAgICAgICBsYWJlbDogJ0xhYmVsJywKICAgICAgICAgIHZhbHVlOiAnSWQnCiAgICAgICAgfQogICAgICB9LAogICAgICBJc2Rpc2FibGU6IGZhbHNlLAogICAgICB0eXBlQ29kZTogJycsCiAgICAgIHR5cGVJZDogJycsCiAgICAgIHBhcnRHcmFkZTogJycsCiAgICAgIHBhZ2VMb2FkaW5nOiBmYWxzZQogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgIFByb2Nlc3NGbG93OiB7CiAgICAgIGhhbmRsZXIobmV3TmFtZSwgb2xkTmFtZSkgewogICAgICAgIGNvbnNvbGUubG9nKG5ld05hbWUpCiAgICAgICAgdGhpcy5mb3JtLlF1ZXN0aW9ubGFiX0lkcyA9IFtdCiAgICAgICAgdGhpcy5Qcm9jZXNzRmxvdy5mb3JFYWNoKChpdGVtKSA9PiB7CiAgICAgICAgICBpZiAoCiAgICAgICAgICAgIGl0ZW0uUXVlc3Rpb25sYWJfSWQgJiYKICAgICAgICAgICAgIXRoaXMuZm9ybS5RdWVzdGlvbmxhYl9JZHMuaW5jbHVkZXMoaXRlbS5RdWVzdGlvbmxhYl9JZCkKICAgICAgICAgICkgewogICAgICAgICAgICB0aGlzLmZvcm0uUXVlc3Rpb25sYWJfSWRzLnB1c2goaXRlbS5RdWVzdGlvbmxhYl9JZCkKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICB9LAogICAgICBkZWVwOiB0cnVlCiAgICB9CiAgfSwKICBtb3VudGVkKCkge30sCiAgbWV0aG9kczogewogICAgYXN5bmMgaW5pdCh0aXRsZSwgY2hlY2tUeXBlLCBkYXRhKSB7CiAgICAgIGlmICh0aXRsZSA9PT0gJ+e8lui+kScpIHsKICAgICAgICB0aGlzLnBhZ2VMb2FkaW5nID0gdHJ1ZQogICAgICB9CiAgICAgIHRoaXMucGFydEdyYWRlID0gY2hlY2tUeXBlLkNvZGUKICAgICAgdGhpcy5DaGVja19PYmplY3RfSWQgPSBjaGVja1R5cGUuSWQKICAgICAgdGhpcy5jaGVja1R5cGUgPSBjaGVja1R5cGUKICAgICAgdGhpcy50aXRsZSA9IHRpdGxlCiAgICAgIHRoaXMuZm9ybS5DaGVja19PYmplY3RfSWQgPSBjaGVja1R5cGUuSWQKICAgICAgdGhpcy5mb3JtLkJvbV9MZXZlbCA9IGNoZWNrVHlwZS5Db2RlCiAgICAgIGF3YWl0IHRoaXMuZ2V0UHJvZmVzc2lvbmFsVHlwZSgpIC8vIOS4k+S4muexu+WIqwogICAgICBhd2FpdCB0aGlzLmdldENoZWNrVHlwZUxpc3QoKSAvLyDmo4Dmn6XnsbvlnosKICAgICAgYXdhaXQgdGhpcy5nZXRDaGVja0l0ZW1MaXN0KCkKICAgICAgYXdhaXQgdGhpcy5nZXROb2RlTGlzdChkYXRhKSAvLyDotKjmo4DoioLngrkKICAgIH0sCiAgICBhc3luYyBhZGRDaGVja0l0ZW1Db21iaW5hdGlvbigpIHsKICAgICAgYXdhaXQgQWRkQ2hlY2tJdGVtQ29tYmluYXRpb24oewogICAgICAgIEdyb3VwOiB0aGlzLmZvcm0sCiAgICAgICAgSXRlbXM6IHRoaXMuUHJvY2Vzc0Zsb3cKICAgICAgfSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfkv53lrZjmiJDlip8nCiAgICAgICAgICB9KQogICAgICAgICAgdGhpcy4kZW1pdCgncmVmcmVzaCcpCiAgICAgICAgICB0aGlzLiRlbWl0KCdjbG9zZScpCiAgICAgICAgICB0aGlzLmRpYWxvZ0RhdGEgPSB7fQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIHJlbW92ZVRhZ0ZuKGlkcywgdGFnKSB7CiAgICAgIGNvbnNvbGUubG9nKCdpZHMnLCBpZHMpCiAgICAgIGNvbnNvbGUubG9nKCd0YWcnLCB0YWcpCiAgICB9LAogICAgU2VsZWN0VHlwZShpdGVtKSB7CiAgICAgIGNvbnNvbGUubG9nKCdpdGVtJywgaXRlbSkKCiAgICAgIGlmIChpdGVtLmxlbmd0aCA9PT0gMSkgewogICAgICAgIHRoaXMuZm9ybS5DaGVja19UeXBlID0gaXRlbVswXQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuZm9ybS5DaGVja19UeXBlID0gLTEKICAgICAgfQogICAgICBjb25zb2xlLmxvZygndGhpcy5mb3JtLkNoZWNrX1R5cGUnLCB0aGlzLmZvcm0uQ2hlY2tfVHlwZSkKICAgIH0sCiAgICBjaGFuZ2VOb2RlKHZhbCkgewogICAgICBjb25zb2xlLmxvZyh2YWwpCiAgICAgIGNvbnNvbGUubG9nKHRoaXMuQ2hlY2tOb2RlTGlzdCkKICAgICAgaWYgKHZhbCkgewogICAgICAgIHRoaXMuZm9ybS5DaGVja19UeXBlID0gdGhpcy5DaGVja05vZGVMaXN0LmZpbmQoKHYpID0+IHsKICAgICAgICAgIHJldHVybiB2LklkID09PSB2YWwKICAgICAgICB9KS5DaGVja19UeXBlCiAgICAgICAgLy8g5aSE55CG6LSo5qOA57G75Z6L5pWw5o2uCgogICAgICAgIHRoaXMuQ2hhbmdlX0NoZWNrX1R5cGUgPSBbXQogICAgICAgIGlmICh0aGlzLmZvcm0uQ2hlY2tfVHlwZSA9PT0gMSB8fCB0aGlzLmZvcm0uQ2hlY2tfVHlwZSA9PT0gMikgewogICAgICAgICAgdGhpcy5Jc2Rpc2FibGUgPSB0cnVlCiAgICAgICAgICB0aGlzLkNoYW5nZV9DaGVja19UeXBlLnB1c2godGhpcy5mb3JtLkNoZWNrX1R5cGUpCiAgICAgICAgfSBlbHNlIGlmICh0aGlzLmZvcm0uQ2hlY2tfVHlwZSA9PT0gLTEpIHsKICAgICAgICAgIHRoaXMuSXNkaXNhYmxlID0gZmFsc2UgLy8g6LSo5qOA57G75Z6L5Y+v57yW6L6RCiAgICAgICAgICB0aGlzLkNoYW5nZV9DaGVja19UeXBlID0gW10KICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5DaGFuZ2VfQ2hlY2tfVHlwZSA9IFtdCiAgICAgICAgICB0aGlzLklzZGlzYWJsZSA9IGZhbHNlCiAgICAgICAgfQogICAgICAgIGNvbnNvbGUubG9nKCcgdGhpcy5Jc2Rpc2FibGUnLCB0aGlzLklzZGlzYWJsZSkKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLkNoYW5nZV9DaGVja19UeXBlID0gW10KICAgICAgfQogICAgfSwKICAgIGdldEVudGl0eUNoZWNrVHlwZShkYXRhKSB7CiAgICAgIGNvbnNvbGUubG9nKGRhdGEpCiAgICAgIEVudGl0eVF1YWxpdHlMaXN0KHsKICAgICAgICBpZDogZGF0YS5JZCwKICAgICAgICBjaGVja19vYmplY3RfaWQ6IHRoaXMuQ2hlY2tfT2JqZWN0X0lkCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gewogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICB0aGlzLmZvcm0gPSByZXMuRGF0YVswXS5Hcm91cAogICAgICAgICAgY29uc29sZS5sb2codGhpcy5mb3JtLk9iamVjdF9UeXBlX0lkcywgJ09iamVjdF9UeXBlX0lkcycpCgogICAgICAgICAgdGhpcy5Qcm9jZXNzRmxvdyA9IHJlcy5EYXRhWzBdLkl0ZW1zCiAgICAgICAgICB0aGlzLkNoYW5nZV9DaGVja19UeXBlID0gW10KICAgICAgICAgIC8vIOWkhOeQhui0qOajgOexu+Wei+aVsOaNrgogICAgICAgICAgaWYgKHRoaXMuZm9ybS5DaGVja19UeXBlID09PSAxIHx8IHRoaXMuZm9ybS5DaGVja19UeXBlID09PSAyKSB7CiAgICAgICAgICAgIHRoaXMuQ2hhbmdlX0NoZWNrX1R5cGUucHVzaCh0aGlzLmZvcm0uQ2hlY2tfVHlwZSkKICAgICAgICAgICAgaWYgKHJlcy5EYXRhWzBdLkNoZWNrTm9kZV9UeXBlID09PSAtMSkgewogICAgICAgICAgICAgIHRoaXMuSXNkaXNhYmxlID0gZmFsc2UKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLklzZGlzYWJsZSA9IHRydWUKICAgICAgICAgICAgfQogICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmZvcm0uQ2hlY2tfVHlwZSA9PT0gLTEpIHsKICAgICAgICAgICAgdGhpcy5DaGFuZ2VfQ2hlY2tfVHlwZSA9IFsxLCAyXQogICAgICAgICAgICB0aGlzLklzZGlzYWJsZSA9IHRydWUgLy8g6LSo5qOA57G75Z6L5LiN5Y+v57yW6L6RCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLkNoYW5nZV9DaGVja19UeXBlID0gW10KICAgICAgICAgICAgdGhpcy5Jc2Rpc2FibGUgPSBmYWxzZQogICAgICAgICAgfQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICAgIHRoaXMucGFnZUxvYWRpbmcgPSBmYWxzZQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZVN1Ym1pdChmb3JtKSB7CiAgICAgIGlmICh0aGlzLkNoYW5nZV9DaGVja19UeXBlLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgIG1lc3NhZ2U6ICfor7fpgInmi6nmo4Dmn6XnsbvlnosnCiAgICAgICAgfSkKICAgICAgICByZXR1cm4KICAgICAgfQogICAgICBsZXQgdmVyaWZpY2F0aW9uID0gdHJ1ZQogICAgICBpZiAodGhpcy5Qcm9jZXNzRmxvdy5sZW5ndGggPT09IDApIHsKICAgICAgICB2ZXJpZmljYXRpb24gPSBmYWxzZQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuUHJvY2Vzc0Zsb3cuZm9yRWFjaCgodmFsKSA9PiB7CiAgICAgICAgICBmb3IgKGNvbnN0IGtleSBpbiB2YWwpIHsKICAgICAgICAgICAgaWYgKHZhbFtrZXldID09PSAnJykgewogICAgICAgICAgICAgIHZlcmlmaWNhdGlvbiA9IGZhbHNlCiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9KQogICAgICB9CiAgICAgIGlmICghdmVyaWZpY2F0aW9uKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+Whq+WGmeWujOaVtOajgOafpemhueiuvue9ruWGheWuuScKICAgICAgICB9KQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICBjb25zdCBwcm9jZXNzRmxvd0NvcHkgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMuUHJvY2Vzc0Zsb3cpKQogICAgICBjb25zdCBwcm9jZXNzRmxvd05ldyA9IFtdCiAgICAgIHByb2Nlc3NGbG93Q29weS5mb3JFYWNoKChpdGVtKSA9PiB7CiAgICAgICAgY29uc3QgcHJvY2Vzc0Zsb3dKc29uID0ge30KICAgICAgICBwcm9jZXNzRmxvd0pzb24uQ2hlY2tfSXRlbV9JZCA9IGl0ZW0uQ2hlY2tfSXRlbV9JZAogICAgICAgIHByb2Nlc3NGbG93SnNvbi5FbGlnaWJpbGl0eV9Dcml0ZXJpYSA9IGl0ZW0uRWxpZ2liaWxpdHlfQ3JpdGVyaWEKICAgICAgICBwcm9jZXNzRmxvd0pzb24uUXVlc3Rpb25sYWJfSWQgPSBpdGVtLlF1ZXN0aW9ubGFiX0lkCiAgICAgICAgcHJvY2Vzc0Zsb3dOZXcucHVzaChwcm9jZXNzRmxvd0pzb24pCiAgICAgIH0pCiAgICAgIGNvbnN0IHByb2Nlc3NGbG93VGVtcCA9IHByb2Nlc3NGbG93TmV3Lm1hcCgoaXRlbSkgPT4gewogICAgICAgIHJldHVybiBKU09OLnN0cmluZ2lmeShpdGVtKQogICAgICB9KQogICAgICBpZiAobmV3IFNldChwcm9jZXNzRmxvd1RlbXApLnNpemUgIT09IHByb2Nlc3NGbG93VGVtcC5sZW5ndGgpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICBtZXNzYWdlOiAn5qOA5p+l6aG56K6+572u5YaF5a655LiN6IO95a6M5YWo55u45ZCMJwogICAgICAgIH0pCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIGNvbnN0IHByb2Nlc3NGbG93QXJyID0gdGhpcy5Qcm9jZXNzRmxvdy5tYXAoKHYpID0+IHsKICAgICAgICByZXR1cm4gdi5RdWVzdGlvbmxhYl9JZAogICAgICB9KQoKICAgICAgY29uc3QgaXNJbmNsdWRlcyA9IHRoaXMuZm9ybS5RdWVzdGlvbmxhYl9JZHMuZXZlcnkoKGl0ZW0pID0+CiAgICAgICAgcHJvY2Vzc0Zsb3dBcnIuaW5jbHVkZXMoaXRlbSkKICAgICAgKQogICAgICBpZiAoIWlzSW5jbHVkZXMpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICBtZXNzYWdlOiAn5qOA5p+l6aG56K6+572u5b+F6aG75YyF5ZCr5bey6YCJ5qOA5p+l57G75Z6LJwogICAgICAgIH0pCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIHRoaXMuJHJlZnNbZm9ybV0udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB0aGlzLmFkZENoZWNrSXRlbUNvbWJpbmF0aW9uKCkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgcmV0dXJuIGZhbHNlCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIC8vIOiOt+WPluS4k+S4muexu+WIqwogICAgYXN5bmMgZ2V0UHJvZmVzc2lvbmFsVHlwZSgpIHsKICAgICAgY29uc3QgUGxhdGZvcm0gPQogICAgICAgIGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdQbGF0Zm9ybScpIHx8IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdDdXJQbGF0Zm9ybScpCiAgICAgIGlmIChQbGF0Zm9ybSA9PT0gJzInKSB7CiAgICAgICAgdGhpcy5tb2RlID0gJ2ZhY3RvcnknCiAgICAgICAgYXdhaXQgR2V0RmFjdG9yeVByb2Zlc3Npb25hbEJ5Q29kZSgpLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgICAgdGhpcy5Qcm9DYXRlZ29yeUxpc3QgPSByZXMuRGF0YQogICAgICAgICAgICBjb25zdCB7CiAgICAgICAgICAgICAgQ29kZSwKICAgICAgICAgICAgICBJZAogICAgICAgICAgICB9ID0gcmVzLkRhdGE/LmZpbmQoaXRlbSA9PiBpdGVtLkNvZGUgPT09ICdTdGVlbCcpIHx8IHt9CiAgICAgICAgICAgIHRoaXMudHlwZUNvZGUgPSBDb2RlCiAgICAgICAgICAgIHRoaXMudHlwZUlkID0gSWQKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UKICAgICAgICAgICAgfSkKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICB9CgogICAgICAvLyDojrflj5bpobnnm64v5bel5Y6CaWQKICAgICAgdGhpcy5Qcm9qZWN0SWQgPQogICAgICAgIHRoaXMubW9kZSA9PT0gJ2ZhY3RvcnknCiAgICAgICAgICA/IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdDdXJSZWZlcmVuY2VJZCcpCiAgICAgICAgICA6IHRoaXMuUHJvamVjdElkCiAgICB9LAoKICAgIC8vIOiOt+WPluajgOafpeexu+Wei+S4i+aLieahhgogICAgYXN5bmMgZ2V0Q2hlY2tUeXBlTGlzdCgpIHsKICAgICAgYXdhaXQgR2V0Q2hlY2tUeXBlTGlzdCh7IGNoZWNrX29iamVjdF9pZDogdGhpcy5DaGVja19PYmplY3RfSWQsIEJvbV9MZXZlbDogdGhpcy5mb3JtLkJvbV9MZXZlbCB9KS50aGVuKAogICAgICAgIChyZXMpID0+IHsKICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7CiAgICAgICAgICAgIHRoaXMuQ2hlY2tUeXBlTGlzdCA9IHJlcy5EYXRhCiAgICAgICAgICAgIGNvbnNvbGUubG9nKHJlcy5EYXRhKQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZQogICAgICAgICAgICB9KQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgKQogICAgfSwKICAgIC8vIOajgOafpemhueWGheWuuQogICAgYXN5bmMgZ2V0Q2hlY2tJdGVtTGlzdCgpIHsKICAgICAgYXdhaXQgR2V0Q2hlY2tJdGVtTGlzdCh7IGNoZWNrX29iamVjdF9pZDogdGhpcy5DaGVja19PYmplY3RfSWQgfSkudGhlbigKICAgICAgICAocmVzKSA9PiB7CiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICB0aGlzLkNoZWNrSXRlbUxpc3QgPSByZXMuRGF0YQogICAgICAgICAgICBjb25zb2xlLmxvZyhyZXMuRGF0YSkKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UKICAgICAgICAgICAgfSkKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgICkKICAgIH0sCiAgICAvLyDpgJrov4fkuJPkuJrnsbvliKvpgInmi6nlr7nosaHnsbvlnosKICAgIGNoYW5nZUNhdGVnb3J5KHZhbCkgewogICAgICB0aGlzLmZvcm0uT2JqZWN0X1R5cGVfSWRzID0gW10KICAgICAgdGhpcy5jaG9vc2VUeXBlKHZhbCkKICAgIH0sCiAgICAvLyDpgJrov4fkuJPkuJrnsbvliKvpgInmi6nlr7nosaHnsbvlnosKICAgIGNob29zZVR5cGUodmFsKSB7CiAgICAgIGNvbnNvbGUubG9nKHRoaXMuUHJvQ2F0ZWdvcnlMaXN0KQogICAgICB0aGlzLlByb0NhdGVnb3J5Q29kZSA9IHRoaXMuUHJvQ2F0ZWdvcnlMaXN0LmZpbmQoKHYpID0+IHsKICAgICAgICByZXR1cm4gdi5JZCA9PT0gdmFsCiAgICAgIH0pLkNvZGUKICAgICAgdGhpcy5nZXRPYmplY3RUeXBlTGlzdCh0aGlzLlByb0NhdGVnb3J5Q29kZSkgLy8g5a+56LGh57G75Z6LCiAgICB9LAogICAgLy8g6YCJ5Lit6KGo5qC85aSW5qOA5p+l57G75Z6LCiAgICBDaGFuZ2VDaGVja1R5cGUodmFsKSB7CiAgICAgIGNvbnN0IGFyckpzb24gPSBPYmplY3QuYXNzaWduKFtdLCB2YWwpCiAgICAgIC8vIGxldCBpbmRleCA9IGFyckpzb24uaW5kZXhPZihJc2V4aXN0KTsKICAgICAgLy8gdGhpcy5Qcm9jZXNzRmxvdy5zcGxpY2UoaW5kZXgsIDEpOwogICAgICBjb25zb2xlLmxvZyhhcnJKc29uKQogICAgICBpZiAodGhpcy5Qcm9jZXNzRmxvdy5sZW5ndGggPiBhcnJKc29uLmxlbmd0aCkgewogICAgICAgIGNvbnN0IGFyckpzb25UZW1wID0gYXJySnNvbi5tYXAoKGl0ZW0pID0+IHsKICAgICAgICAgIGNvbnN0IGl0ZW1GaWVsZCA9IHsKICAgICAgICAgICAgQ2hlY2tfSXRlbV9JZDogJycsCiAgICAgICAgICAgIEVsaWdpYmlsaXR5X0NyaXRlcmlhOiAnJywKICAgICAgICAgICAgUXVlc3Rpb25sYWJfSWQ6IGl0ZW0KICAgICAgICAgIH0KICAgICAgICAgIHRoaXMuUHJvY2Vzc0Zsb3cuZm9yRWFjaCgoaXRlbXMpID0+IHsKICAgICAgICAgICAgaWYgKGl0ZW1zLlF1ZXN0aW9ubGFiX0lkID09PSBpdGVtKSB7CiAgICAgICAgICAgICAgaXRlbUZpZWxkLkNoZWNrX0l0ZW1fSWQgPSBpdGVtcy5DaGVja19JdGVtX0lkCiAgICAgICAgICAgICAgaXRlbUZpZWxkLkVsaWdpYmlsaXR5X0NyaXRlcmlhID0gaXRlbXMuRWxpZ2liaWxpdHlfQ3JpdGVyaWEKICAgICAgICAgICAgfQogICAgICAgICAgfSkKCiAgICAgICAgICByZXR1cm4gaXRlbUZpZWxkCiAgICAgICAgfSkKICAgICAgICB0aGlzLlByb2Nlc3NGbG93ID0gW10uY29uY2F0KGFyckpzb25UZW1wKQogICAgICB9IGVsc2UgewogICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgYXJySnNvbi5sZW5ndGg7IGkrKykgewogICAgICAgICAgY29uc3QgSXNleGlzdCA9IHRoaXMuUHJvY2Vzc0Zsb3cuZmluZCgodikgPT4gewogICAgICAgICAgICByZXR1cm4gdi5RdWVzdGlvbmxhYl9JZCA9PT0gYXJySnNvbltpXQogICAgICAgICAgfSkKICAgICAgICAgIGlmICghSXNleGlzdCkgewogICAgICAgICAgICB0aGlzLlByb2Nlc3NGbG93LnB1c2goewogICAgICAgICAgICAgIFF1ZXN0aW9ubGFiX0lkOiBhcnJKc29uW2ldLAogICAgICAgICAgICAgIENoZWNrX0l0ZW1fSWQ6ICcnLAogICAgICAgICAgICAgIEVsaWdpYmlsaXR5X0NyaXRlcmlhOiAnJwogICAgICAgICAgICB9KQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQoKICAgICAgY29uc29sZS5sb2coJ0NoYW5nZUNoZWNrVHlwZSgpJywgdGhpcy5Qcm9jZXNzRmxvdykKICAgIH0sCgogICAgcmVtb3ZlQ2hlY2tUeXBlKHZhbCkgewogICAgICBjb25zdCBJc2V4aXN0ID0gdGhpcy5Qcm9jZXNzRmxvdy5maW5kKCh2KSA9PiB7CiAgICAgICAgcmV0dXJuIHYuUXVlc3Rpb25sYWJfSWQgPT09IHZhbAogICAgICB9KQogICAgICBjb25zdCBpbmRleCA9IHRoaXMuUHJvY2Vzc0Zsb3cuaW5kZXhPZihJc2V4aXN0KQogICAgICBpZiAoSXNleGlzdCkgewogICAgICAgIHRoaXMuUHJvY2Vzc0Zsb3cuc3BsaWNlKGluZGV4LCAxKQogICAgICB9CiAgICB9LAogICAgLy8g6YCJ5Lit5qOA5p+l6aG55YaF5a65CiAgICBDaGFuZ2VJdGVtKGRhdGEsIGluZGV4LCByb3cpIHsKICAgICAgLy8gY29uc29sZS5sb2coZGF0YSk7CiAgICAgIC8vIGNvbnNvbGUubG9nKGluZGV4KTsKICAgICAgLy8gY29uc29sZS5sb2cocm93KQogICAgICAvLyBjb25zb2xlLmxvZyh0aGlzLkNoZWNrSXRlbUxpc3QpOwogICAgICByb3cuRWxpZ2liaWxpdHlfQ3JpdGVyaWEgPSAnJwogICAgICB0aGlzLkVsaWdpYmlsaXR5X0NyaXRlcmlhID0gJycKICAgICAgdGhpcy5FbGlnaWJpbGl0eV9Dcml0ZXJpYSA9IHRoaXMuQ2hlY2tJdGVtTGlzdC5maW5kKCh2KSA9PiB7CiAgICAgICAgcmV0dXJuIHYuSWQgPT09IGRhdGEKICAgICAgfSk/LkVsaWdpYmlsaXR5X0NyaXRlcmlhCiAgICAgIHRoaXMuJHNldCgKICAgICAgICB0aGlzLlByb2Nlc3NGbG93W2luZGV4XSwKICAgICAgICAnRWxpZ2liaWxpdHlfQ3JpdGVyaWEnLAogICAgICAgIHRoaXMuRWxpZ2liaWxpdHlfQ3JpdGVyaWEKICAgICAgKQogICAgICB0aGlzLiRzZXQodGhpcy5Qcm9jZXNzRmxvd1tpbmRleF0sICdzb3J0JywgaW5kZXgpCiAgICAgIGNvbnNvbGUubG9nKHRoaXMuUHJvY2Vzc0Zsb3cpCiAgICB9LAoKICAgIGFzeW5jIGVkaXRIYW5kbGVEYXRhKGRhdGEpIHsKICAgICAgaWYgKHRoaXMudGl0bGUgPT09ICfnvJbovpEnKSB7CiAgICAgICAgY29uc29sZS5sb2coJ2RhdGEnLCBkYXRhKQogICAgICAgIHRoaXMuZm9ybS5JZCA9IGRhdGEuSWQKICAgICAgICB0aGlzLmdldEVudGl0eUNoZWNrVHlwZShkYXRhKQogICAgICAgIGF3YWl0IHRoaXMuY2hvb3NlVHlwZShkYXRhLlByb19DYXRlZ29yeV9JZCkKICAgICAgfQogICAgfSwKCiAgICAvLyDotKjmo4DoioLngrnkuIvmi4noj5zljZUKICAgIGFzeW5jIGdldE5vZGVMaXN0KGRhdGEpIHsKICAgICAgYXdhaXQgR2V0Tm9kZUxpc3QoeyBjaGVja19vYmplY3RfaWQ6IHRoaXMuQ2hlY2tfT2JqZWN0X0lkLCBCb21fTGV2ZWw6IHRoaXMuZm9ybS5Cb21fTGV2ZWwgfSkudGhlbigKICAgICAgICAocmVzKSA9PiB7CiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICB0aGlzLkNoZWNrTm9kZUxpc3QgPSByZXMuRGF0YQogICAgICAgICAgICB0aGlzLmVkaXRIYW5kbGVEYXRhKGRhdGEpCiAgICAgICAgICAgIGNvbnNvbGUubG9nKHJlcy5EYXRhKQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZQogICAgICAgICAgICB9KQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgKQogICAgfSwKICAgIC8vIOWvueixoeexu+Wei+S4i+aLiQogICAgYXN5bmMgZ2V0T2JqZWN0VHlwZUxpc3QoY29kZSkgewogICAgICBpZiAodGhpcy5jaGVja1R5cGUuRGlzcGxheV9OYW1lID09PSAn54mp5paZJykgewogICAgICAgIEdldE1hdGVyaWFsVHlwZSh7fSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgICB0aGlzLk9iamVjdFR5cGVMaXN0ID0gcmVzLkRhdGEKICAgICAgICB9KQogICAgICB9IGVsc2UgewogICAgICAgIGxldCByZXMKICAgICAgICBpZiAodGhpcy5wYXJ0R3JhZGUgPT09ICctMScpIHsKICAgICAgICAgIHJlcyA9IGF3YWl0IEdldENvbXBUeXBlVHJlZSh7IHByb2Zlc3Npb25hbDogY29kZSB9KQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICByZXMgPSBhd2FpdCBHZXRQYXJ0VHlwZVRyZWUoeyBwcm9mZXNzaW9uYWxJZDogdGhpcy50eXBlSWQsIHBhcnRHcmFkZTogdGhpcy5wYXJ0R3JhZGUgfSkKICAgICAgICB9CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIHRoaXMuT2JqZWN0VHlwZUxpc3QuZGF0YSA9IHJlcy5EYXRhCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoXykgPT4gewogICAgICAgICAgICB0aGlzLiRyZWZzLnRyZWVTZWxlY3RPYmplY3RUeXBlLnRyZWVEYXRhVXBkYXRlRnVuKHJlcy5EYXRhKQogICAgICAgICAgfSkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLAogICAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgICB9KQogICAgICAgICAgdGhpcy5PYmplY3RUeXBlTGlzdC5kYXRhID0gW10KICAgICAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7CiAgICAgICAgICAgIHRoaXMuJHJlZnMudHJlZVNlbGVjdE9iamVjdFR5cGUudHJlZURhdGFVcGRhdGVGdW4oW10pCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfQogICAgfSwKCiAgICAvLyDmo4Dmn6Xpobnorr7nva7pg6jliIYKICAgIGFkZFRhYmxlRGF0YSgpIHsKICAgICAgdGhpcy5Qcm9jZXNzRmxvdy5wdXNoKHsKICAgICAgICBDaGVja19JdGVtX0lkOiAnJywKICAgICAgICBFbGlnaWJpbGl0eV9Dcml0ZXJpYTogJycsCiAgICAgICAgUXVlc3Rpb25sYWJfSWQ6ICcnCiAgICAgIH0pCiAgICAgIGNvbnNvbGUubG9nKCdhZGRUYWJsZURhdGEoKScsIHRoaXMuUHJvY2Vzc0Zsb3cpCiAgICB9LAogICAgZGVsZXRlUm93KGluZGV4LCByb3dzKSB7CiAgICAgIGNvbnNvbGUubG9nKGluZGV4KQogICAgICByb3dzLnNwbGljZShpbmRleCwgMSkKICAgICAgY29uc29sZS5sb2codGhpcy5Qcm9jZXNzRmxvdykKICAgICAgaWYgKHRoaXMuUHJvY2Vzc0Zsb3cubGVuZ3RoID4gMCAmJiBpbmRleCAhPT0gdGhpcy5Qcm9jZXNzRmxvdy5sZW5ndGgpIHsKICAgICAgICB0aGlzLiRzZXQodGhpcy5Qcm9jZXNzRmxvd1tpbmRleF0sICdzb3J0JywgaW5kZXgpCiAgICAgIH0KICAgIH0sCiAgICBtb3ZlVXB3YXJkKHJvdywgaW5kZXgpIHsKICAgICAgY29uc29sZS5sb2coaW5kZXgpCiAgICAgIGNvbnN0IHVwRGF0YSA9IHRoaXMuUHJvY2Vzc0Zsb3dbaW5kZXggLSAxXQogICAgICB0aGlzLlByb2Nlc3NGbG93LnNwbGljZShpbmRleCAtIDEsIDEpCiAgICAgIHRoaXMuUHJvY2Vzc0Zsb3cuc3BsaWNlKGluZGV4LCAwLCB1cERhdGEpCiAgICAgIHRoaXMuJHNldCh0aGlzLlByb2Nlc3NGbG93W2luZGV4IC0gMV0sICdzb3J0JywgaW5kZXggLSAxKQogICAgICB0aGlzLiRzZXQodGhpcy5Qcm9jZXNzRmxvd1tpbmRleF0sICdzb3J0JywgaW5kZXgpCiAgICAgIGNvbnNvbGUubG9nKHRoaXMuUHJvY2Vzc0Zsb3cpCiAgICB9LAogICAgbW92ZURvd24ocm93LCBpbmRleCkgewogICAgICBjb25zb2xlLmxvZyhpbmRleCkKICAgICAgY29uc3QgZG93bkRhdGEgPSB0aGlzLlByb2Nlc3NGbG93W2luZGV4ICsgMV0KICAgICAgdGhpcy5Qcm9jZXNzRmxvdy5zcGxpY2UoaW5kZXggKyAxLCAxKQogICAgICB0aGlzLlByb2Nlc3NGbG93LnNwbGljZShpbmRleCwgMCwgZG93bkRhdGEpCiAgICAgIGNvbnNvbGUubG9nKHRoaXMuUHJvY2Vzc0Zsb3cpCiAgICAgIHRoaXMuJHNldCh0aGlzLlByb2Nlc3NGbG93W2luZGV4XSwgJ3NvcnQnLCBpbmRleCkKICAgICAgdGhpcy4kc2V0KHRoaXMuUHJvY2Vzc0Zsb3dbaW5kZXggKyAxXSwgJ3NvcnQnLCBpbmRleCArIDEpCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["CombinationDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAk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file": "CombinationDialog.vue", "sourceRoot": "src/views/PRO/project-config/project-quality/components/Dialog", "sourcesContent": ["<template>\n  <div v-loading=\"pageLoading\" element-loading-text=\"加载中...\">\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"130px\">\n      <el-row>\n        <el-form-item label=\"检查项组合名称\" prop=\"Group_Name\">\n          <el-input v-model=\"form.Group_Name\" />\n        </el-form-item>\n\n        <el-form-item label=\"质检节点\" prop=\"Check_Node_Id\">\n          <el-select\n            v-model=\"form.Check_Node_Id\"\n            clearable\n            style=\"width: 100%\"\n            placeholder=\"请选择质检节点\"\n            @change=\"changeNode\"\n          >\n            <el-option\n              v-for=\"(item, index) in CheckNodeList\"\n              :key=\"index\"\n              :label=\"item.Display_Name\"\n              :value=\"item.Id\"\n            />\n          </el-select>\n        </el-form-item>\n\n        <el-form-item label=\"质检类型\" prop=\"Check_Type\">\n          <el-select\n            v-model=\"Change_Check_Type\"\n            clearable\n            multiple\n            :multiple-limit=\"1\"\n            style=\"width: 100%\"\n            :disabled=\"Isdisable\"\n            placeholder=\"请选择质检类型\"\n            @change=\"SelectType\"\n          >\n            <el-option\n              v-for=\"(item, index) in QualityTypeList\"\n              :key=\"index\"\n              :label=\"item.Name\"\n              :value=\"item.Id\"\n            />\n          </el-select>\n        </el-form-item>\n\n        <el-form-item label=\"专业类别\" prop=\"Pro_Category_Id\">\n          <el-select\n            v-model=\"form.Pro_Category_Id\"\n            clearable\n            style=\"width: 100%\"\n            placeholder=\"请选择专业类别\"\n            @change=\"changeCategory\"\n          >\n            <el-option\n              v-for=\"(item, index) in ProCategoryList\"\n              :key=\"index\"\n              :label=\"item.Name\"\n              :value=\"item.Id\"\n            />\n          </el-select>\n        </el-form-item>\n\n        <el-form-item label=\"检查类型\" prop=\"Questionlab_Ids\">\n          <el-select\n            v-model=\"form.Questionlab_Ids\"\n            style=\"width: 100%\"\n            multiple\n            placeholder=\"请选择检查类型\"\n            @change=\"ChangeCheckType\"\n            @remove-tag=\"removeCheckType\"\n          >\n            <el-option\n              v-for=\"(item, index) in CheckTypeList\"\n              :key=\"index\"\n              :label=\"item.Name\"\n              :value=\"item.Id\"\n            />\n          </el-select>\n        </el-form-item>\n\n        <el-form-item\n          label=\"产品类型\"\n          prop=\"Object_Type_Ids\"\n        >\n          <el-tree-select\n            ref=\"treeSelectObjectType\"\n            v-model=\"form.Object_Type_Ids\"\n            :disabled=\"!Boolean(form.Pro_Category_Id)\"\n            class=\"cs-tree-x\"\n            :tree-params=\"ObjectTypeList\"\n            value-key=\"Id\"\n            @removeTag=\"removeTagFn\"\n          />\n        </el-form-item>\n\n        <el-col :span=\"24\">\n          <h3>检查项设置</h3>\n          <el-form-item label=\"\" prop=\"\" class=\"checkItem\">\n            <el-table :data=\"ProcessFlow\" border style=\"width: 100%\">\n              <el-table-column prop=\"\" label=\"*检查类型\" align=\"center\">\n                <template slot-scope=\"{ row }\">\n                  <el-select\n                    v-model=\"row.Questionlab_Id\"\n                    style=\"width: 100%\"\n                    clearable\n                    placeholder=\"请选择\"\n                  >\n                    <el-option\n                      v-for=\"(item, index) in CheckTypeList\"\n                      :key=\"index\"\n                      :label=\"item.Name\"\n                      :value=\"item.Id\"\n                    />\n                  </el-select>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"\" label=\"*检查项内容\" align=\"center\">\n                <template slot-scope=\"{ row, $index }\">\n                  <el-select\n                    v-model=\"row.Check_Item_Id\"\n                    style=\"width: 100%\"\n                    clearable\n                    placeholder=\"请选择\"\n                    @change=\"ChangeItem($event, $index, row)\"\n                  >\n                    <el-option\n                      v-for=\"(item, index) in CheckItemList\"\n                      :key=\"index\"\n                      :label=\"item.Check_Content\"\n                      :value=\"item.Id\"\n                    />\n                  </el-select>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"\" label=\"*合格标准\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  <el-input\n                    v-model=\"scope.row.Eligibility_Criteria\"\n                    disabled\n                  />\n                </template>\n              </el-table-column>\n              <el-table-column\n                prop=\"address\"\n                label=\"操作\"\n                width=\"140\"\n                align=\"center\"\n              >\n                <template slot-scope=\"{ row, $index }\">\n                  <el-button\n                    type=\"text\"\n                    icon=\"el-icon-top\"\n                    :disabled=\"$index == 0\"\n                    @click=\"moveUpward(row, $index)\"\n                  />\n                  <el-button\n                    type=\"text\"\n                    icon=\"el-icon-bottom\"\n                    :disabled=\"$index == ProcessFlow.length - 1\"\n                    @click=\"moveDown(row, $index)\"\n                  />\n                  <el-button\n                    type=\"text\"\n                    icon=\"el-icon-delete\"\n                    style=\"color: #f56c6c\"\n                    @click.native.prevent=\"deleteRow($index, ProcessFlow)\"\n                  />\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-button\n            type=\"text\"\n            class=\"addcheckItem\"\n            @click=\"addTableData\"\n          >+ 新增检查项</el-button>\n        </el-col>\n        <el-col :span=\"24\" style=\"text-align: right\">\n          <el-form-item style=\"text-align: right\">\n            <el-button @click=\"$emit('close')\">关 闭</el-button>\n            <el-button\n              type=\"primary\"\n              @click=\"handleSubmit('form')\"\n            >确 定</el-button>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n  </div>\n</template>\n\n<script>\nimport { AddCheckItemCombination } from '@/api/PRO/factorycheck'\nimport { EntityQualityList } from '@/api/PRO/factorycheck'\n\nimport { GetCheckTypeList } from '@/api/PRO/factorycheck'\nimport { GetCheckItemList } from '@/api/PRO/factorycheck'\nimport { GetNodeList } from '@/api/PRO/factorycheck'\nimport { GetCompTypeTree } from '@/api/PRO/factorycheck'\nimport {\n  GetFactoryProfessionalByCode,\n  GetMaterialType\n} from '@/api/PRO/factorycheck'\nimport { GetPartTypeTree } from '@/api/PRO/partType'\n\nexport default {\n  data() {\n    return {\n      mode: '', // 区分项目和工厂\n      ProjectId: '', // 项目Id\n      Check_Object_Id: '',\n      checkType: {}, // 区分构件、零件、物料\n      form: {\n        Object_Type_Ids: []\n      },\n      rules: {\n        Check_Content: [\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\n        ],\n        Eligibility_Criteria: [\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\n        ],\n        Group_Name: [\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\n        ],\n        Check_Type: [\n          { required: true, message: '请填写完整表单', trigger: 'change' }\n        ],\n        Questionlab_Ids: [\n          { required: true, message: '请填写完整表单', trigger: 'blur' }\n        ]\n      },\n      title: '',\n      options: [],\n      ProcessFlow: [],\n      CheckTypeList: [], // 检查类型下拉\n      CheckItemList: [], // 检查项下拉\n      Change_Check_Type: [],\n      QualityTypeList: [\n        {\n          Name: '质量',\n          Id: 1\n        },\n        {\n          Name: '探伤',\n          Id: 2\n        }\n      ], // 质检类型\n      ProCategoryList: [], // 专业类别下拉\n      CheckNodeList: [], // 质检节点下拉\n      verification: false,\n      ProCategoryCode: '', // 专业类别Code\n      Eligibility_Criteria: '',\n      ObjectTypeList: {\n        // 对象类型\n        'check-strictly': true,\n        'default-expand-all': true,\n        filterable: false,\n        clickParent: true,\n        data: [],\n        props: {\n          children: 'Children',\n          label: 'Label',\n          value: 'Id'\n        }\n      },\n      Isdisable: false,\n      typeCode: '',\n      typeId: '',\n      partGrade: '',\n      pageLoading: false\n    }\n  },\n  watch: {\n    ProcessFlow: {\n      handler(newName, oldName) {\n        console.log(newName)\n        this.form.Questionlab_Ids = []\n        this.ProcessFlow.forEach((item) => {\n          if (\n            item.Questionlab_Id &&\n            !this.form.Questionlab_Ids.includes(item.Questionlab_Id)\n          ) {\n            this.form.Questionlab_Ids.push(item.Questionlab_Id)\n          }\n        })\n      },\n      deep: true\n    }\n  },\n  mounted() {},\n  methods: {\n    async init(title, checkType, data) {\n      if (title === '编辑') {\n        this.pageLoading = true\n      }\n      this.partGrade = checkType.Code\n      this.Check_Object_Id = checkType.Id\n      this.checkType = checkType\n      this.title = title\n      this.form.Check_Object_Id = checkType.Id\n      this.form.Bom_Level = checkType.Code\n      await this.getProfessionalType() // 专业类别\n      await this.getCheckTypeList() // 检查类型\n      await this.getCheckItemList()\n      await this.getNodeList(data) // 质检节点\n    },\n    async addCheckItemCombination() {\n      await AddCheckItemCombination({\n        Group: this.form,\n        Items: this.ProcessFlow\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.$message({\n            type: 'success',\n            message: '保存成功'\n          })\n          this.$emit('refresh')\n          this.$emit('close')\n          this.dialogData = {}\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n      })\n    },\n    removeTagFn(ids, tag) {\n      console.log('ids', ids)\n      console.log('tag', tag)\n    },\n    SelectType(item) {\n      console.log('item', item)\n\n      if (item.length === 1) {\n        this.form.Check_Type = item[0]\n      } else {\n        this.form.Check_Type = -1\n      }\n      console.log('this.form.Check_Type', this.form.Check_Type)\n    },\n    changeNode(val) {\n      console.log(val)\n      console.log(this.CheckNodeList)\n      if (val) {\n        this.form.Check_Type = this.CheckNodeList.find((v) => {\n          return v.Id === val\n        }).Check_Type\n        // 处理质检类型数据\n\n        this.Change_Check_Type = []\n        if (this.form.Check_Type === 1 || this.form.Check_Type === 2) {\n          this.Isdisable = true\n          this.Change_Check_Type.push(this.form.Check_Type)\n        } else if (this.form.Check_Type === -1) {\n          this.Isdisable = false // 质检类型可编辑\n          this.Change_Check_Type = []\n        } else {\n          this.Change_Check_Type = []\n          this.Isdisable = false\n        }\n        console.log(' this.Isdisable', this.Isdisable)\n      } else {\n        this.Change_Check_Type = []\n      }\n    },\n    getEntityCheckType(data) {\n      console.log(data)\n      EntityQualityList({\n        id: data.Id,\n        check_object_id: this.Check_Object_Id\n      }).then((res) => {\n        if (res.IsSucceed) {\n          this.form = res.Data[0].Group\n          console.log(this.form.Object_Type_Ids, 'Object_Type_Ids')\n\n          this.ProcessFlow = res.Data[0].Items\n          this.Change_Check_Type = []\n          // 处理质检类型数据\n          if (this.form.Check_Type === 1 || this.form.Check_Type === 2) {\n            this.Change_Check_Type.push(this.form.Check_Type)\n            if (res.Data[0].CheckNode_Type === -1) {\n              this.Isdisable = false\n            } else {\n              this.Isdisable = true\n            }\n          } else if (this.form.Check_Type === -1) {\n            this.Change_Check_Type = [1, 2]\n            this.Isdisable = true // 质检类型不可编辑\n          } else {\n            this.Change_Check_Type = []\n            this.Isdisable = false\n          }\n        } else {\n          this.$message({\n            type: 'error',\n            message: res.Message\n          })\n        }\n        this.pageLoading = false\n      })\n    },\n    handleSubmit(form) {\n      if (this.Change_Check_Type.length === 0) {\n        this.$message({\n          type: 'error',\n          message: '请选择检查类型'\n        })\n        return\n      }\n      let verification = true\n      if (this.ProcessFlow.length === 0) {\n        verification = false\n      } else {\n        this.ProcessFlow.forEach((val) => {\n          for (const key in val) {\n            if (val[key] === '') {\n              verification = false\n            }\n          }\n        })\n      }\n      if (!verification) {\n        this.$message({\n          type: 'error',\n          message: '请填写完整检查项设置内容'\n        })\n        return\n      }\n\n      const processFlowCopy = JSON.parse(JSON.stringify(this.ProcessFlow))\n      const processFlowNew = []\n      processFlowCopy.forEach((item) => {\n        const processFlowJson = {}\n        processFlowJson.Check_Item_Id = item.Check_Item_Id\n        processFlowJson.Eligibility_Criteria = item.Eligibility_Criteria\n        processFlowJson.Questionlab_Id = item.Questionlab_Id\n        processFlowNew.push(processFlowJson)\n      })\n      const processFlowTemp = processFlowNew.map((item) => {\n        return JSON.stringify(item)\n      })\n      if (new Set(processFlowTemp).size !== processFlowTemp.length) {\n        this.$message({\n          type: 'error',\n          message: '检查项设置内容不能完全相同'\n        })\n        return\n      }\n\n      const processFlowArr = this.ProcessFlow.map((v) => {\n        return v.Questionlab_Id\n      })\n\n      const isIncludes = this.form.Questionlab_Ids.every((item) =>\n        processFlowArr.includes(item)\n      )\n      if (!isIncludes) {\n        this.$message({\n          type: 'error',\n          message: '检查项设置必须包含已选检查类型'\n        })\n        return\n      }\n\n      this.$refs[form].validate((valid) => {\n        if (valid) {\n          this.addCheckItemCombination()\n        } else {\n          return false\n        }\n      })\n    },\n    // 获取专业类别\n    async getProfessionalType() {\n      const Platform =\n        localStorage.getItem('Platform') || localStorage.getItem('CurPlatform')\n      if (Platform === '2') {\n        this.mode = 'factory'\n        await GetFactoryProfessionalByCode().then((res) => {\n          if (res.IsSucceed) {\n            this.ProCategoryList = res.Data\n            const {\n              Code,\n              Id\n            } = res.Data?.find(item => item.Code === 'Steel') || {}\n            this.typeCode = Code\n            this.typeId = Id\n          } else {\n            this.$message({\n              type: 'error',\n              message: res.Message\n            })\n          }\n        })\n      }\n\n      // 获取项目/工厂id\n      this.ProjectId =\n        this.mode === 'factory'\n          ? localStorage.getItem('CurReferenceId')\n          : this.ProjectId\n    },\n\n    // 获取检查类型下拉框\n    async getCheckTypeList() {\n      await GetCheckTypeList({ check_object_id: this.Check_Object_Id, Bom_Level: this.form.Bom_Level }).then(\n        (res) => {\n          if (res.IsSucceed) {\n            this.CheckTypeList = res.Data\n            console.log(res.Data)\n          } else {\n            this.$message({\n              type: 'error',\n              message: res.Message\n            })\n          }\n        }\n      )\n    },\n    // 检查项内容\n    async getCheckItemList() {\n      await GetCheckItemList({ check_object_id: this.Check_Object_Id }).then(\n        (res) => {\n          if (res.IsSucceed) {\n            this.CheckItemList = res.Data\n            console.log(res.Data)\n          } else {\n            this.$message({\n              type: 'error',\n              message: res.Message\n            })\n          }\n        }\n      )\n    },\n    // 通过专业类别选择对象类型\n    changeCategory(val) {\n      this.form.Object_Type_Ids = []\n      this.chooseType(val)\n    },\n    // 通过专业类别选择对象类型\n    chooseType(val) {\n      console.log(this.ProCategoryList)\n      this.ProCategoryCode = this.ProCategoryList.find((v) => {\n        return v.Id === val\n      }).Code\n      this.getObjectTypeList(this.ProCategoryCode) // 对象类型\n    },\n    // 选中表格外检查类型\n    ChangeCheckType(val) {\n      const arrJson = Object.assign([], val)\n      // let index = arrJson.indexOf(Isexist);\n      // this.ProcessFlow.splice(index, 1);\n      console.log(arrJson)\n      if (this.ProcessFlow.length > arrJson.length) {\n        const arrJsonTemp = arrJson.map((item) => {\n          const itemField = {\n            Check_Item_Id: '',\n            Eligibility_Criteria: '',\n            Questionlab_Id: item\n          }\n          this.ProcessFlow.forEach((items) => {\n            if (items.Questionlab_Id === item) {\n              itemField.Check_Item_Id = items.Check_Item_Id\n              itemField.Eligibility_Criteria = items.Eligibility_Criteria\n            }\n          })\n\n          return itemField\n        })\n        this.ProcessFlow = [].concat(arrJsonTemp)\n      } else {\n        for (var i = 0; i < arrJson.length; i++) {\n          const Isexist = this.ProcessFlow.find((v) => {\n            return v.Questionlab_Id === arrJson[i]\n          })\n          if (!Isexist) {\n            this.ProcessFlow.push({\n              Questionlab_Id: arrJson[i],\n              Check_Item_Id: '',\n              Eligibility_Criteria: ''\n            })\n          }\n        }\n      }\n\n      console.log('ChangeCheckType()', this.ProcessFlow)\n    },\n\n    removeCheckType(val) {\n      const Isexist = this.ProcessFlow.find((v) => {\n        return v.Questionlab_Id === val\n      })\n      const index = this.ProcessFlow.indexOf(Isexist)\n      if (Isexist) {\n        this.ProcessFlow.splice(index, 1)\n      }\n    },\n    // 选中检查项内容\n    ChangeItem(data, index, row) {\n      // console.log(data);\n      // console.log(index);\n      // console.log(row)\n      // console.log(this.CheckItemList);\n      row.Eligibility_Criteria = ''\n      this.Eligibility_Criteria = ''\n      this.Eligibility_Criteria = this.CheckItemList.find((v) => {\n        return v.Id === data\n      })?.Eligibility_Criteria\n      this.$set(\n        this.ProcessFlow[index],\n        'Eligibility_Criteria',\n        this.Eligibility_Criteria\n      )\n      this.$set(this.ProcessFlow[index], 'sort', index)\n      console.log(this.ProcessFlow)\n    },\n\n    async editHandleData(data) {\n      if (this.title === '编辑') {\n        console.log('data', data)\n        this.form.Id = data.Id\n        this.getEntityCheckType(data)\n        await this.chooseType(data.Pro_Category_Id)\n      }\n    },\n\n    // 质检节点下拉菜单\n    async getNodeList(data) {\n      await GetNodeList({ check_object_id: this.Check_Object_Id, Bom_Level: this.form.Bom_Level }).then(\n        (res) => {\n          if (res.IsSucceed) {\n            this.CheckNodeList = res.Data\n            this.editHandleData(data)\n            console.log(res.Data)\n          } else {\n            this.$message({\n              type: 'error',\n              message: res.Message\n            })\n          }\n        }\n      )\n    },\n    // 对象类型下拉\n    async getObjectTypeList(code) {\n      if (this.checkType.Display_Name === '物料') {\n        GetMaterialType({}).then((res) => {\n          this.ObjectTypeList = res.Data\n        })\n      } else {\n        let res\n        if (this.partGrade === '-1') {\n          res = await GetCompTypeTree({ professional: code })\n        } else {\n          res = await GetPartTypeTree({ professionalId: this.typeId, partGrade: this.partGrade })\n        }\n        if (res.IsSucceed) {\n          this.ObjectTypeList.data = res.Data\n          this.$nextTick((_) => {\n            this.$refs.treeSelectObjectType.treeDataUpdateFun(res.Data)\n          })\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n          this.ObjectTypeList.data = []\n          this.$nextTick((_) => {\n            this.$refs.treeSelectObjectType.treeDataUpdateFun([])\n          })\n        }\n      }\n    },\n\n    // 检查项设置部分\n    addTableData() {\n      this.ProcessFlow.push({\n        Check_Item_Id: '',\n        Eligibility_Criteria: '',\n        Questionlab_Id: ''\n      })\n      console.log('addTableData()', this.ProcessFlow)\n    },\n    deleteRow(index, rows) {\n      console.log(index)\n      rows.splice(index, 1)\n      console.log(this.ProcessFlow)\n      if (this.ProcessFlow.length > 0 && index !== this.ProcessFlow.length) {\n        this.$set(this.ProcessFlow[index], 'sort', index)\n      }\n    },\n    moveUpward(row, index) {\n      console.log(index)\n      const upData = this.ProcessFlow[index - 1]\n      this.ProcessFlow.splice(index - 1, 1)\n      this.ProcessFlow.splice(index, 0, upData)\n      this.$set(this.ProcessFlow[index - 1], 'sort', index - 1)\n      this.$set(this.ProcessFlow[index], 'sort', index)\n      console.log(this.ProcessFlow)\n    },\n    moveDown(row, index) {\n      console.log(index)\n      const downData = this.ProcessFlow[index + 1]\n      this.ProcessFlow.splice(index + 1, 1)\n      this.ProcessFlow.splice(index, 0, downData)\n      console.log(this.ProcessFlow)\n      this.$set(this.ProcessFlow[index], 'sort', index)\n      this.$set(this.ProcessFlow[index + 1], 'sort', index + 1)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n::v-deep {\n  .checkItem {\n    width: 100%;\n    .el-form-item__content {\n      margin-left: 0 !important;\n    }\n  }\n  .addcheckItem {\n    font-size: 16px;\n    margin-bottom: 10px;\n  }\n}\n::v-deep .el-form-item {\n  display: inline-block;\n  .el-form-item__content {\n    & > .el-input {\n      width: 220px !important;\n    }\n    & > .el-select {\n      width: 220px !important;\n    }\n    .el-tree-select-input {\n      width: 220px !important;\n    }\n  }\n}\n</style>\n"]}]}
import request from '@/utils/request'
import qs from 'qs'

// 入库方式
export const StockInTypes = [{
  label: '生产入库',
  value: '生产入库'
}, {
  label: '退货入库',
  value: '退货入库'
}, {
  label: '退库入库',
  value: '退库入库'
}]
// 入库类型
export const From_Stock_Status_TYPES = [{
  label: '生产待入库',
  value: 0
}, {
  label: '退库待入库',
  value: 1
}, {
  label: '退货待入库',
  value: 2
}, {
  label: '已入库',
  value: 3
}]
export const PackingTypes = [{
  label: '直发件包',
  value: '直发件包'
}, {
  label: '构件包',
  value: '构件包'
}]
export const PackingStatus = [{
  label: '已入库',
  value: '已入库'
}, {
  label: '待入库',
  value: '待入库'
}]
// 盘库状态，小于3 为盘点中
export const InventoryCheckStatus = [{
  label: '有效',
  value: 1
}, {
  label: '采集开始',
  value: 2
}, {
  label: '采集结束',
  value: 3
}]
// 盘库异常类型 Status 0 盘亏异常， Status 1 盘盈异常
export const InventoryCheckExceptions = [
  {
    label: '采集前出库',
    value: '采集前出库',
    Status: '盘亏' //
  },
  {
    label: '漏扫',
    value: '漏扫',
    Status: '盘亏'
  },
  {
    label: '出库时未扫',
    value: '出库时未扫',
    Status: '盘亏'
  },
  {
    label: '其他',
    value: '其他',
    Status: '盘亏'
  },
  {
    label: '待入库状态',
    value: '待入库状态',
    Status: '盘盈'
  },
  {
    label: '已出库状态',
    value: '已出库状态',
    Status: '盘盈'
  }
]

// 仓库/成品包件分类
export const InventoryComponentTypes = [
  {
    label: 'PC构件',
    value: 'PC构件',
    icon: 'icon-pre-concrete'
  },
  {
    label: '钢构构件',
    value: '钢构构件',
    icon: 'icon-steel'
  },
  {
    label: '打包件',
    value: '打包件',
    icon: 'icon-expressbox'
  },
  {
    label: '直发件',
    value: '直发件',
    icon: 'icon-layers'
  }
]

// 获取所有有效仓库列表
export function GetTotalWarehouseListOfCurFactory(data) {
  return request({
    url: '/PRO/Warehouse/GetTotalWarehouseListOfCurFactory',
    method: 'post',
    data
  })
}

// 获取登录用户仓库列表
export function GetWarehouseListOfCurFactory(data) {
  return request({
    url: '/PRO/Warehouse/GetWarehouseListOfCurFactory',
    method: 'post',
    data
  })
}
// 根据仓库获取所有库位
export function GetLocationList(data) {
  return request({
    url: '/PRO/Location/GetLocationList',
    method: 'post',
    data
  })
}
// 保存入库单据
export function SaveStockIn(data) {
  return request({
    url: '/PRO/ComponentStockIn/SaveStockIn',
    method: 'post',
    data
  })
}
// 获取入库单实体
export function GetComponentStockInEntity(id) {
  return request({
    url: '/PRO/ComponentStockIn/GetComponentStockInEntity',
    method: 'post',
    params: {
      id
    }
  })
}
// 获取入库单明细
export function GetStockInDetailList(stockInId, isEdit) {
  return request({
    url: '/PRO/ComponentStockIn/GetStockInDetailList',
    method: 'post',
    params: {
      stockInId,
      isEdit
    }
  })
}
// 校验打包件编号是否存在
export function CheckPackCode(code, id) {
  return request({
    url: '/PRO/Packing/CheckPackCode',
    method: 'post',
    params: {
      code,
      id
    }
  })
}
// 获取打包内构件详情
export function GetPackingGroupByDirectDetailList(data) {
  return request({
    url: '/PRO/Packing/GetPackingGroupByDirectDetailList',
    method: 'post',
    data
  })
}
// 获取打包件内构件详情（）
export function GetPackingDetailList(data) {
  return request({
    url: '/PRO/Packing/GetPackingDetailList',
    method: 'post',
    data
  })
}

// 导出入库实收单详情
export function ExportComponentStockInInfo(id) {
  return request({
    url: '/PRO/ComponentStockIn/ExportComponentStockInInfo',
    method: 'post',
    params: {
      id
    }
  })
}
// 导出打包件明细到excel
export function ExportPackingInInfo(id) {
  return request({
    url: '/PRO/Packing/ExportPackingInInfo',
    method: 'post',
    params: {
      id
    }
  })
}
// 删除入库单据
export function RemoveMain(id) {
  return request({
    url: '/PRO/ComponentStockIn/RemoveMain',
    method: 'post',
    params: {
      id
    }
  })
}
// 保存打包件
export function SavePacking(data) {
  return request({
    url: '/PRO/Packing/SavePacking',
    method: 'post',
    data
  })
}
// 删除打包件
export function UnzipPacking({ id, locationId }) {
  return request({
    url: '/PRO/Packing/UnzipPacking',
    method: 'post',
    params: {
      id,
      locationId
    }
  })
}
// 获取打包件实体
export function GetPackingEntity({ id, code }) {
  return request({
    url: '/PRO/Packing/GetPackingEntity',
    method: 'post',
    params: {
      id,
      code
    }
  })
}
// 获取移库单明细列表
export function GetStockMoveDetailList(billId) {
  return request({
    url: '/PRO/ComponentStockMove/GetStockMoveDetailList',
    method: 'post',
    params: {
      billId
    }
  })
}
// 保存移库单
export function SaveStockMove(data) {
  return request({
    url: '/PRO/ComponentStockMove/SaveStockMove',
    method: 'post',
    data
  })
}
// 保存盘点单
export function SaveInventory(data) {
  return request({
    url: '/PRO/ComponentInventory/SaveInventory',
    method: 'post',
    data
  })
}
// 盘点明细异常处理
export function HandleInventoryItem({ id, type, value }) {
  return request({
    url: '/PRO/ComponentInventory/HandleInventoryItem',
    method: 'post',
    params: {
      keyValue: id,
      type,
      value
    }
  })
}
// 保存报废单据
export function SaveComponentScrap(data) {
  return request({
    url: '/PRO/ComponentScrap/SaveComponentScrap',
    method: 'post',
    data
  })
}
// 盘库结束采集
export function FinishCollect(id) {
  return request({
    url: '/PRO/ComponentInventory/FinishCollect',
    method: 'post',
    params: {
      keyValue: id
    }
  })
}
// 清单齐套确认
export function UpdateBillReady({ installId, isReady }) {
  return request({
    url: '/PRO/ProductionPrepare/UpdateBillReady',
    method: 'post',
    params: {
      installId,
      isReady
    }
  })
}
// 物料齐套确认
export function UpdateMaterialReady(data) {
  return request({
    url: '/PRO/ProductionPrepare/UpdateMaterialReady',
    method: 'post',
    data
  })
}

export function ExportWaitingStockIn2ndList(data) {
  return request({
    url: '/PRO/componentstockin/ExportWaitingStockIn2ndList',
    method: 'post',
    data
  })
}

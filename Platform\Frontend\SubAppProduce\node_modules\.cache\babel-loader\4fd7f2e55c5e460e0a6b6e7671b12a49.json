{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\api\\PRO\\control-plan.js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\api\\PRO\\control-plan.js", "mtime": 1757572678681}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "GetPurchaseControlPlan", "data", "url", "method", "GetProduceControlPlan", "GetDeepenControlPlan", "GetConstructionControlPlan", "SavePurchaseControlPlan", "SaveProduceControlPlan", "SaveConstructionControlPlan", "GetAreaPlanBusinessData", "SaveDeepenControlPlan", "GetWarnConfigs", "SaveWarnConfigs", "ResetWarnConfig", "SyncAreaWarnConfig"], "sources": ["D:/project/platform_framework_master/platform_framework/Platform/Frontend/SubAppProduce/src/api/PRO/control-plan.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取采购计划跟踪\r\nexport function GetPurchaseControlPlan(data) {\r\n  return request({\r\n    url: '/PRO/ControlPlan/GetPurchaseControlPlan',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 生产计划跟踪\r\nexport function GetProduceControlPlan(data) {\r\n  return request({\r\n    url: '/PRO/ControlPlan/GetProduceControlPlan',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 深化计划跟踪\r\nexport function GetDeepenControlPlan(data) {\r\n  return request({\r\n    url: '/PRO/ControlPlan/GetDeepenControlPlan',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 施工计划跟踪\r\nexport function GetConstructionControlPlan(data) {\r\n  return request({\r\n    url: '/PRO/ControlPlan/GetConstructionControlPlan',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 保存采购跟踪数据\r\nexport function SavePurchaseControlPlan(data) {\r\n  return request({\r\n    url: '/PRO/ControlPlan/SavePurchaseControlPlan',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 保存生产计划跟踪数据\r\nexport function SaveProduceControlPlan(data) {\r\n  return request({\r\n    url: '/PRO/ControlPlan/SaveProduceControlPlan',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 保存施工计划跟踪数据\r\nexport function SaveConstructionControlPlan(data) {\r\n  return request({\r\n    url: '/PRO/ControlPlan/SaveConstructionControlPlan',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\nexport function GetAreaPlanBusinessData(data) {\r\n  return request({\r\n    url: '/PRO/ControlPlan/GetAreaPlanBusinessData',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 保存深化计划跟踪\r\nexport function SaveDeepenControlPlan(data) {\r\n  return request({\r\n    url: '/PRO/ControlPlan/SaveDeepenControlPlan',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 获取预警配置\r\nexport function GetWarnConfigs(data) {\r\n  return request({\r\n    url: '/PRO/ControlPlanConfig/GetWarnConfigs',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 保存预警配置\r\nexport function SaveWarnConfigs(data) {\r\n  return request({\r\n    url: '/PRO/ControlPlanConfig/SaveWarnConfigs',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 重置预警配置\r\nexport function ResetWarnConfig(data) {\r\n  return request({\r\n    url: '/PRO/ControlPlanConfig/ResetWarnConfig',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 同步至项目分区\r\nexport function SyncAreaWarnConfig(data) {\r\n  return request({\r\n    url: '/PRO/ControlPlanConfig/SyncAreaWarnConfig',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,sBAAsBA,CAACC,IAAI,EAAE;EAC3C,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,yCAAyC;IAC9CC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,qBAAqBA,CAACH,IAAI,EAAE;EAC1C,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,wCAAwC;IAC7CC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,oBAAoBA,CAACJ,IAAI,EAAE;EACzC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,uCAAuC;IAC5CC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASK,0BAA0BA,CAACL,IAAI,EAAE;EAC/C,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,6CAA6C;IAClDC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASM,uBAAuBA,CAACN,IAAI,EAAE;EAC5C,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,0CAA0C;IAC/CC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASO,sBAAsBA,CAACP,IAAI,EAAE;EAC3C,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,yCAAyC;IAC9CC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASQ,2BAA2BA,CAACR,IAAI,EAAE;EAChD,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,8CAA8C;IACnDC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AAEA,OAAO,SAASS,uBAAuBA,CAACT,IAAI,EAAE;EAC5C,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,0CAA0C;IAC/CC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASU,qBAAqBA,CAACV,IAAI,EAAE;EAC1C,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,wCAAwC;IAC7CC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASW,cAAcA,CAACX,IAAI,EAAE;EACnC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,uCAAuC;IAC5CC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASY,eAAeA,CAACZ,IAAI,EAAE;EACpC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,wCAAwC;IAC7CC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASa,eAAeA,CAACb,IAAI,EAAE;EACpC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,wCAAwC;IAC7CC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASc,kBAAkBA,CAACd,IAAI,EAAE;EACvC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,2CAA2C;IAChDC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ", "ignoreList": []}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\group\\index.vue?vue&type=style&index=0&id=90801ad8&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\group\\index.vue", "mtime": 1758509456111}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5jcy1kaWFsb2cgewogIDo6di1kZWVwIHsKICAgIC5lbC1kaWFsb2dfX2JvZHkgewogICAgICBwYWRkaW5nLXRvcDogMDsKICAgIH0KICB9Cn0KOjp2LWRlZXAgewogIC5jcy10b3AtaGVhZGVyLWJveCB7CiAgICBsaW5lLWhlaWdodDogMHB4OwogIH0KfQoKLmNzLXotdGItd3JhcHBlciB7CiAgaGVpZ2h0OiAwOwp9Cgo6OnYtZGVlcCAucGFnaW5hdGlvbiB7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kICFpbXBvcnRhbnQ7CiAgICBtYXJnaW4tdG9wOiAxMnB4ICFpbXBvcnRhbnQ7CiAgICAuZWwtaW5wdXQtLXNtYWxsIC5lbC1pbnB1dF9faW5uZXIgewogICAgICAgIGhlaWdodDogMjhweDsKICAgICAgICBsaW5lLWhlaWdodDogMjhweDsKICAgIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/basic-information/group", "sourcesContent": ["<template>\n  <div class=\"abs100 cs-z-flex-pd16-wrap\">\n    <div class=\"cs-z-page-main-content\">\n      <top-header padding=\"0\">\n        <template #right>\n          <el-form label-width=\"80px\" :inline=\"true\" @submit.native.prevent>\n            <el-form-item label=\"班组名称\">\n              <el-input v-model=\"keywords\" placeholder=\"请输入\" clearable @keyup.enter.native=\"handleSearch\" />\n            </el-form-item>\n            <el-form-item>\n              <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\n              <el-button @click=\"reset\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </template>\n        <template #left>\n          <el-button type=\"primary\" @click=\"handleAdd\">新增</el-button>\n          <el-button\n            type=\"danger\"\n            :disabled=\"!selectList.length\"\n            @click=\"handleDelete(true)\"\n          >删除</el-button>\n        </template>\n      </top-header>\n      <div v-loading=\"tbLoading\" class=\"fff cs-z-tb-wrapper\">\n        <vxe-table\n          ref=\"xTable\"\n          v-loading=\"tbLoading\"\n          :empty-render=\"{name: 'NotData'}\"\n          show-header-overflow\n          element-loading-spinner=\"el-icon-loading\"\n          element-loading-text=\"拼命加载中\"\n          empty-text=\"暂无数据\"\n          height=\"100%\"\n          :data=\"tbData\"\n          stripe\n          resizable\n          :auto-resize=\"true\"\n          class=\"cs-vxe-table\"\n          :tooltip-config=\"{ enterable: true }\"\n          @checkbox-all=\"handleSelectionChange\"\n          @checkbox-change=\"handleSelectionChange\"\n        >\n          <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" />\n          <vxe-column\n            v-for=\"(item, index) in columns\"\n            :key=\"index\"\n            :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\n            show-overflow=\"tooltip\"\n            sortable\n            :align=\"item.Align\"\n            :field=\"item.Code\"\n            :title=\"item.Display_Name\"\n            :visible=\"item.Is_Display\"\n          >\n            <template #default=\"{ row }\">\n              <span v-if=\"item.Code === 'Manager_UserName'\">\n                <div>{{ row.Manager_UserName || \"-\" }}</div>\n              </span>\n              <span v-else-if=\"item.Code === 'Load_Unit'\">\n                <div>{{ row.Load_Unit || \"-\" }}</div>\n              </span>\n              <span v-else-if=\"item.Code === 'Workshop_Name'\">\n                <div>{{ row.Workshop_Name || \"-\" }}</div>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Outsource'\">\n                <el-tag v-if=\"row.Is_Outsource\" type=\"success\">是</el-tag><el-tag v-else type=\"info\">否</el-tag>\n              </span>\n              <span v-else-if=\"item.Code === 'Is_Enabled'\">\n                <el-tag v-if=\"row.Is_Enabled\" type=\"success\">是</el-tag><el-tag v-else type=\"info\">否</el-tag>\n              </span>\n              <span v-else-if=\"item.Code === 'Sort'\">\n                <span>{{ item.Sort || item.Sort === 0 ? row[item.Code] : '-' }}</span>\n              </span>\n              <span v-else>{{ row[item.Code] || \"-\" }}</span>\n            </template>\n          </vxe-column>\n          <vxe-column fixed=\"right\" title=\"操作\" width=\"160\" show-overflow align=\"center\">\n            <template #default=\"{ row }\">\n              <el-button type=\"text\" @click=\"handleDetail(row)\">查看</el-button>\n              <el-button type=\"text\" @click=\"handleEdit(row)\">编辑</el-button>\n              <el-button\n                type=\"text\"\n                style=\"color: red\"\n                @click=\"handleDelete(false, row)\"\n              >删除</el-button>\n            </template>\n          </vxe-column>\n        </vxe-table>\n        <!-- <dynamic-data-table\n          ref=\"dyTable\"\n          :columns=\"columns\"\n          :config=\"tbConfig\"\n          :data=\"tbData\"\n          :page=\"queryInfo.Page\"\n          :total=\"total\"\n          border\n          stripe\n          class=\"cs-plm-dy-table\"\n          @multiSelectedChange=\"handleSelectionChange\"\n          @gridPageChange=\"handlePageChange\"\n          @gridSizeChange=\"handlePageChange\"\n          @tableSearch=\"tableSearch\"\n        >\n          <template slot=\"Manager_UserName\" slot-scope=\"{ row }\">\n            <div>{{ row.Manager_UserName || \"-\" }}</div>\n          </template>\n          <template slot=\"Load_Unit\" slot-scope=\"{ row }\">\n            <div>{{ row.Load_Unit || \"-\" }}</div>\n          </template>\n          <template slot=\"Workshop_Name\" slot-scope=\"{ row }\">\n            <div>{{ row.Workshop_Name || \"-\" }}</div>\n          </template>\n          <template slot=\"Is_Outsource\" slot-scope=\"{ row }\">\n            <div><el-tag v-if=\"row.Is_Outsource\" type=\"success\">是</el-tag><el-tag v-else type=\"info\">否</el-tag></div>\n          </template>\n          <template slot=\"Is_Enabled\" slot-scope=\"{ row }\">\n            <div><el-tag v-if=\"row.Is_Enabled\" type=\"success\">是</el-tag><el-tag v-else type=\"info\">否</el-tag></div>\n          </template>\n          <template slot=\"op\" slot-scope=\"{ row }\">\n            <el-button type=\"text\" @click=\"handleDetail(row)\">查看</el-button>\n            <el-button type=\"text\" @click=\"handleEdit(row)\">编辑</el-button>\n            <el-button\n              type=\"text\"\n              style=\"color: red\"\n              @click=\"handleDelete(false, row)\"\n            >删除</el-button>\n          </template>\n        </dynamic-data-table> -->\n      </div>\n    </div>\n\n    <el-dialog\n      v-dialogDrag\n      class=\"cs-dialog\"\n      :title=\"title\"\n      :visible.sync=\"dialogVisible\"\n      :close-on-click-modal=\"false\"\n      custom-class=\"dialogCustomClass\"\n      width=\"900px\"\n      @close=\"handleClose\"\n    >\n      <component\n        :is=\"currentComponent\"\n        v-if=\"dialogVisible\"\n        ref=\"content\"\n        :dialog-visible=\"dialogVisible\"\n        @close=\"handleClose\"\n        @refresh=\"fetchData\"\n      />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport getTbInfo from '@/mixins/PRO/get-table-info'\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'\nimport TopHeader from '@/components/TopHeader'\nimport { DeleteWorkingTeams, GetWorkingTeams, GetWorkingTeamsPageList } from '@/api/PRO/technology-lib'\nimport detail from './component/detail'\nimport info from './component/info'\nimport getCommonData from '@/mixins/PRO/get-common-data'\n\nexport default {\n  name: 'PROGroup',\n  components: {\n    DynamicDataTable,\n    TopHeader,\n    info,\n    detail\n  },\n  mixins: [getTbInfo, getCommonData],\n  data() {\n    return {\n      tbConfig: {\n        Pager_Align: 'center'\n      },\n      queryInfo: {\n        Page: 1,\n        PageSize: 10,\n        SortName: 'Sort',\n        SortOrder: 'asc',\n        ParameterJson: []\n      },\n      currentComponent: '',\n      title: '',\n      columns: [],\n      tbData: [],\n      total: 0,\n      tbLoading: false,\n      dialogVisible: false,\n      selectList: [],\n      keywords: ''\n    }\n  },\n  watch: {\n    columns(e) {\n      // 车间未开启不显示所属车间信息\n      if (!this.FactoryDetailData.Is_Workshop_Enabled) {\n        e.map((item) => {\n          if (item.Code === 'Workshop_Name') {\n            item.Is_Display = false\n          }\n        })\n      }\n    }\n  },\n  async created() {\n    // 获取工厂详情\n    await this.getCurFactory()\n    this.tbLoading = true\n    await this.getTableConfig('pro_group_list')\n    await this.fetchData()\n  },\n  methods: {\n    fetchData() {\n      this.tbLoading = true\n      GetWorkingTeamsPageList({ keywords: this.keywords, pageInfo: this.queryInfo }).then((res) => {\n        if (res.IsSucceed) {\n          this.tbData = res.Data.Data\n          this.total = res.Data.TotalCount\n          console.log(this.total)\n        } else {\n          this.$message({\n            message: res.Message,\n            type: 'error'\n          })\n        }\n        this.tbLoading = false\n      })\n    },\n    handleClose() {\n      this.dialogVisible = false\n    },\n    handleSelectionChange(list) {\n      console.log('list', list)\n      this.selectList = list.records\n    },\n    handleAdd() {\n      this.currentComponent = 'detail'\n      this.title = '新增'\n      this.dialogVisible = true\n      this.$nextTick((_) => {\n        this.$refs['content'].initData('', this.FactoryDetailData.Is_Workshop_Enabled)\n      })\n    },\n    handleEdit(row) {\n      this.currentComponent = 'detail'\n      this.title = '编辑'\n      this.dialogVisible = true\n      this.$nextTick((_) => {\n        this.$refs['content'].initData(row.Id, this.FactoryDetailData.Is_Workshop_Enabled)\n      })\n    },\n    handleDetail(row) {\n      this.currentComponent = 'info'\n      this.title = '查看'\n      this.dialogVisible = true\n      this.$nextTick((_) => {\n        this.$refs['content'].initData(row, this.FactoryDetailData.Is_Workshop_Enabled)\n      })\n    },\n    handleDelete(isAll, row) {\n      const ids = !isAll ? row.Id : this.selectList.map((i) => i.Id).toString()\n      this.$confirm('是否删除选中班组?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      })\n        .then(() => {\n          console.log('id', ids)\n          DeleteWorkingTeams({\n            ids: ids.toString()\n          }).then((res) => {\n            if (res.IsSucceed) {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.$refs.xTable.clearCheckboxRow()\n              this.selectList = []\n              this.fetchData()\n            } else {\n              this.$message({\n                message: res.Message,\n                type: 'error'\n              })\n            }\n          })\n        })\n        .catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n    },\n    handleSearch() {\n      this.$refs.xTable.clearCheckboxRow()\n      this.selectList = []\n      this.fetchData()\n    },\n    reset() {\n      this.keywords = ''\n      this.$refs.xTable.clearCheckboxRow()\n      this.selectList = []\n      this.fetchData()\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.cs-dialog {\n  ::v-deep {\n    .el-dialog__body {\n      padding-top: 0;\n    }\n  }\n}\n::v-deep {\n  .cs-top-header-box {\n    line-height: 0px;\n  }\n}\n\n.cs-z-tb-wrapper {\n  height: 0;\n}\n\n::v-deep .pagination {\n    justify-content: flex-end !important;\n    margin-top: 12px !important;\n    .el-input--small .el-input__inner {\n        height: 28px;\n        line-height: 28px;\n    }\n}\n</style>\n"]}]}
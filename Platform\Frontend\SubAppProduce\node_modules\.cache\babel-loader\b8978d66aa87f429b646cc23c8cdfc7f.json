{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\component\\ProjectAddDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\component\\ProjectAddDialog.vue", "mtime": 1757926768439}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9yZWdlbmVyYXRvclJ1bnRpbWUgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9yZWdlbmVyYXRvclJ1bnRpbWUuanMiOwppbXBvcnQgX2FzeW5jVG9HZW5lcmF0b3IgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9hc3luY1RvR2VuZXJhdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsdGVyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5maWx0ZXIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IEdldFByb2plY3RQYWdlTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9wcm9qZWN0JzsKaW1wb3J0IHsgU3luY1Byb2plY3RUZWNobm9sb2d5RnJvbVByb2plY3QgfSBmcm9tICdAL2FwaS9QUk8vdGVjaG5vbG9neS1saWInOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0FkZFByb2plY3QnLAogIHByb3BzOiB7CiAgICBzeXNQcm9qZWN0SWQ6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnJwogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICBwcm9qZWN0TGlzdDogW10sCiAgICAgIHNlYXJjaEZvcm06IHsKICAgICAgICBQcm9qZWN0Q29kZTogJycKICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmZldGNoUHJvamVjdExpc3QoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOiOt+W<PERSON><PERSON>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"}, {"version": 3, "names": ["GetProjectPageList", "SyncProjectTechnologyFromProject", "name", "props", "sysProjectId", "type", "String", "default", "data", "loading", "projectList", "searchForm", "ProjectCode", "created", "fetchProjectList", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "params", "res", "_res$Data", "table", "wrap", "_callee$", "_context", "prev", "next", "sent", "IsSucceed", "Data", "filter", "item", "Sys_Project_Id", "$message", "error", "Message", "t0", "console", "finish", "stop", "handleCancel", "$emit", "handleConfirm", "_this2", "warning", "From_Sys_Project_Id", "To_Sys_Project_Id", "then", "success", "finally"], "sources": ["src/views/PRO/project-config/product-mfg-path/component/ProjectAddDialog.vue"], "sourcesContent": ["<template>\r\n  <div class=\"add-project-container\">\r\n    <div class=\"instruction\">\r\n      请选择项目,添加所选项目的所有生产路径\r\n    </div>\r\n    <div class=\"search-section\">\r\n      <el-form :model=\"searchForm\" inline>\r\n        <el-form-item v-if=\"!projectList.length\" label=\"项目名称：\">\r\n          <div>暂无可同步的项目</div>\r\n        </el-form-item>\r\n        <el-form-item v-else label=\"项目名称：\">\r\n          <el-select\r\n            v-model=\"searchForm.ProjectCode\"\r\n            clearable\r\n            filterable\r\n            placeholder=\"请选择\"\r\n            style=\"width: 200px\"\r\n          >\r\n            <el-option v-for=\"item in projectList\" :key=\"item.Sys_Project_Id\" :label=\"item.Short_Name\" :value=\"item.Sys_Project_Id\" />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n      </el-form>\r\n    </div>\r\n\r\n    <!-- 提示信息 -->\r\n\r\n    <!-- 底部按钮 -->\r\n    <div class=\"footer-actions\">\r\n      <el-button @click=\"handleCancel\">取消</el-button>\r\n      <el-button v-if=\"projectList.length\" type=\"primary\" :loading=\"loading\" :disabled=\"searchForm.ProjectCode === ''\" @click=\"handleConfirm\">\r\n        确定\r\n      </el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetProjectPageList } from '@/api/PRO/project'\r\nimport { SyncProjectTechnologyFromProject } from '@/api/PRO/technology-lib'\r\nexport default {\r\n  name: 'AddProject',\r\n  props: {\r\n    sysProjectId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      projectList: [],\r\n      searchForm: {\r\n        ProjectCode: ''\r\n      }\r\n\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchProjectList()\r\n  },\r\n  methods: {\r\n\r\n    // 获取项目列表\r\n    async fetchProjectList() {\r\n      try {\r\n        const params = {\r\n        }\r\n        const res = await GetProjectPageList(params)\r\n        if (res.IsSucceed) {\r\n          const table = res?.Data?.Data || []\r\n          this.projectList = table.filter(item => item.Sys_Project_Id !== this.sysProjectId)\r\n        } else {\r\n          this.$message.error(res.Message || '获取项目列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取项目列表失败:', error)\r\n        this.$message.error('获取项目列表失败')\r\n      } finally {\r\n      }\r\n    },\r\n    handleCancel() {\r\n      this.$emit('close')\r\n    },\r\n    // 确认选择\r\n    handleConfirm() {\r\n      if (this.searchForm.ProjectCode === '') {\r\n        this.$message.warning('请至少选择一个项目')\r\n        return\r\n      }\r\n      this.loading = true\r\n\r\n      SyncProjectTechnologyFromProject({\r\n        From_Sys_Project_Id: this.searchForm.ProjectCode,\r\n        To_Sys_Project_Id: this.sysProjectId\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$emit('refresh')\r\n          this.$emit('close')\r\n          this.$message.success('同步成功！')\r\n        } else {\r\n          this.$message.error(res.Message || '同步失败')\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.add-project-container {\r\n\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .search-section {\r\n    background: #fff;\r\n    border-radius: 4px;\r\n  }\r\n\r\n  .instruction {\r\n    background: #f0f9ff;\r\n    border: 1px solid #b3d8ff;\r\n    color: #1890ff;\r\n    padding: 12px 16px;\r\n    border-radius: 4px;\r\n    margin-bottom: 16px;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .table-section {\r\n    flex: 1;\r\n    background: #fff;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .footer-actions {\r\n    margin-top: 16px;\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    padding: 0px 8px 0 0;\r\n    background: #fff;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCA,SAAAA,kBAAA;AACA,SAAAC,gCAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACAC,YAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,WAAA;MACAC,UAAA;QACAC,WAAA;MACA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,gBAAA;EACA;EACAC,OAAA;IAEA;IACAD,gBAAA,WAAAA,iBAAA;MAAA,IAAAE,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,MAAA,EAAAC,GAAA,EAAAC,SAAA,EAAAC,KAAA;QAAA,OAAAN,mBAAA,GAAAO,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAEAP,MAAA,IACA;cAAAM,QAAA,CAAAE,IAAA;cAAA,OACA7B,kBAAA,CAAAqB,MAAA;YAAA;cAAAC,GAAA,GAAAK,QAAA,CAAAG,IAAA;cACA,IAAAR,GAAA,CAAAS,SAAA;gBACAP,KAAA,IAAAF,GAAA,aAAAA,GAAA,gBAAAC,SAAA,GAAAD,GAAA,CAAAU,IAAA,cAAAT,SAAA,uBAAAA,SAAA,CAAAS,IAAA;gBACAhB,KAAA,CAAAN,WAAA,GAAAc,KAAA,CAAAS,MAAA,WAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAAC,cAAA,KAAAnB,KAAA,CAAAZ,YAAA;gBAAA;cACA;gBACAY,KAAA,CAAAoB,QAAA,CAAAC,KAAA,CAAAf,GAAA,CAAAgB,OAAA;cACA;cAAAX,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAY,EAAA,GAAAZ,QAAA;cAEAa,OAAA,CAAAH,KAAA,cAAAV,QAAA,CAAAY,EAAA;cACAvB,KAAA,CAAAoB,QAAA,CAAAC,KAAA;YAAA;cAAAV,QAAA,CAAAC,IAAA;cAAA,OAAAD,QAAA,CAAAc,MAAA;YAAA;YAAA;cAAA,OAAAd,QAAA,CAAAe,IAAA;UAAA;QAAA,GAAAtB,OAAA;MAAA;IAGA;IACAuB,YAAA,WAAAA,aAAA;MACA,KAAAC,KAAA;IACA;IACA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,SAAAnC,UAAA,CAAAC,WAAA;QACA,KAAAwB,QAAA,CAAAW,OAAA;QACA;MACA;MACA,KAAAtC,OAAA;MAEAR,gCAAA;QACA+C,mBAAA,OAAArC,UAAA,CAAAC,WAAA;QACAqC,iBAAA,OAAA7C;MACA,GAAA8C,IAAA,WAAA5B,GAAA;QACA,IAAAA,GAAA,CAAAS,SAAA;UACAe,MAAA,CAAAF,KAAA;UACAE,MAAA,CAAAF,KAAA;UACAE,MAAA,CAAAV,QAAA,CAAAe,OAAA;QACA;UACAL,MAAA,CAAAV,QAAA,CAAAC,KAAA,CAAAf,GAAA,CAAAgB,OAAA;QACA;MACA,GAAAc,OAAA;QACAN,MAAA,CAAArC,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
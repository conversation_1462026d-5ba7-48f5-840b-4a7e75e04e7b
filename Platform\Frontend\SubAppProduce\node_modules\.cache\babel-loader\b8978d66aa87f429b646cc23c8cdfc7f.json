{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\component\\ProjectAddDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\product-mfg-path\\component\\ProjectAddDialog.vue", "mtime": 1758266768053}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9yZWdlbmVyYXRvclJ1bnRpbWUgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9yZWdlbmVyYXRvclJ1bnRpbWUuanMiOwppbXBvcnQgX2FzeW5jVG9HZW5lcmF0b3IgZnJvbSAiRDovcHJvamVjdC9wbGF0Zm9ybV9mcmFtZXdvcmtfbWFzdGVyL3BsYXRmb3JtX2ZyYW1ld29yay9QbGF0Zm9ybS9Gcm9udGVuZC9TdWJBcHBQcm9kdWNlL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9hc3luY1RvR2VuZXJhdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCgppbXBvcnQgeyBHZXRUZWNobm9sb2d5T2ZQcm9qZWN0TGlzdCB9IGZyb20gJ0AvYXBpL1BSTy90ZWNobm9sb2d5LWxpYic7CmltcG9ydCB7IFN5bmNQcm9qZWN0VGVjaG5vbG9neUZyb21Qcm9qZWN0IH0gZnJvbSAnQC9hcGkvUFJPL3RlY2hub2xvZ3ktbGliJzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdBZGRQcm9qZWN0JywKICBwcm9wczogewogICAgc3lzUHJvamVjdElkOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgcExvYWRpbmc6IGZhbHNlLAogICAgICBwcm9qZWN0TGlzdDogW10sCiAgICAgIHNlYXJjaEZvcm06IHsKICAgICAgICBQcm9qZWN0Q29kZTogJycKICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmZldGNoUHJvamVjdExpc3QoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOiOt+W<PERSON><PERSON>hueebruWIl+ihqAogICAgZmV0Y2hQcm9qZWN0TGlzdDogZnVuY3Rpb24gZmV0Y2hQcm9qZWN0TGlzdCgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlKCkgewogICAgICAgIHZhciBwYXJhbXMsIHJlcywgdGFibGU7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUkKF9jb250ZXh0KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dC5wcmV2ID0gX2NvbnRleHQubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX2NvbnRleHQucHJldiA9IDA7CiAgICAgICAgICAgICAgcGFyYW1zID0ge307CiAgICAgICAgICAgICAgX3RoaXMucExvYWRpbmcgPSB0cnVlOwogICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSA1OwogICAgICAgICAgICAgIHJldHVybiBHZXRUZWNobm9sb2d5T2ZQcm9qZWN0TGlzdChwYXJhbXMpOwogICAgICAgICAgICBjYXNlIDU6CiAgICAgICAgICAgICAgcmVzID0gX2NvbnRleHQuc2VudDsKICAgICAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgewogICAgICAgICAgICAgICAgdGFibGUgPSAocmVzID09PSBudWxsIHx8IHJlcyA9PT0gdm9pZCAwID8gdm9pZCAwIDogcmVzLkRhdGEpIHx8IFtdOwogICAgICAgICAgICAgICAgX3RoaXMucHJvamVjdExpc3QgPSB0YWJsZTsKICAgICAgICAgICAgICAgIC8vIHRoaXMucHJvamVjdExpc3QgPSB0YWJsZS5maWx0ZXIoaXRlbSA9PiBpdGVtLlN5c19Qcm9qZWN0X0lkICE9PSB0aGlzLnN5c1Byb2plY3RJZCkKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgX3RoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLk1lc3NhZ2UgfHwgJ+iOt+WP<PERSON>hueebruWIl+ihqOWksei0pScpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gMTM7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgOToKICAgICAgICAgICAgICBfY29udGV4dC5wcmV2ID0gOTsKICAgICAgICAgICAgICBfY29udGV4dC50MCA9IF9jb250ZXh0WyJjYXRjaCJdKDApOwogICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlumhueebruWIl+ihqOWksei0pTonLCBfY29udGV4dC50MCk7CiAgICAgICAgICAgICAgX3RoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlumhueebruWIl+ihqOWksei0pScpOwogICAgICAgICAgICBjYXNlIDEzOgogICAgICAgICAgICAgIF9jb250ZXh0LnByZXYgPSAxMzsKICAgICAgICAgICAgICBfdGhpcy5wTG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5maW5pc2goMTMpOwogICAgICAgICAgICBjYXNlIDE2OgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZSwgbnVsbCwgW1swLCA5LCAxMywgMTZdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIGhhbmRsZUNhbmNlbDogZnVuY3Rpb24gaGFuZGxlQ2FuY2VsKCkgewogICAgICB0aGlzLiRlbWl0KCdjbG9zZScpOwogICAgfSwKICAgIC8vIOehruiupOmAieaLqQogICAgaGFuZGxlQ29uZmlybTogZnVuY3Rpb24gaGFuZGxlQ29uZmlybSgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIGlmICh0aGlzLnNlYXJjaEZvcm0uUHJvamVjdENvZGUgPT09ICcnKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7foh7PlsJHpgInmi6nkuIDkuKrpobnnm64nKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgU3luY1Byb2plY3RUZWNobm9sb2d5RnJvbVByb2plY3QoewogICAgICAgIEZyb21fU3lzX1Byb2plY3RfSWQ6IHRoaXMuc2VhcmNoRm9ybS5Qcm9qZWN0Q29kZSwKICAgICAgICBUb19TeXNfUHJvamVjdF9JZDogdGhpcy5zeXNQcm9qZWN0SWQKICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsKICAgICAgICAgIF90aGlzMi4kZW1pdCgncmVmcmVzaCcpOwogICAgICAgICAgX3RoaXMyLiRlbWl0KCdjbG9zZScpOwogICAgICAgICAgX3RoaXMyLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WQjOatpeaIkOWKn++8gScpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpczIuJG1lc3NhZ2UuZXJyb3IocmVzLk1lc3NhZ2UgfHwgJ+WQjOatpeWksei0pScpOwogICAgICAgIH0KICAgICAgfSkuZmluYWxseShmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMyLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["GetTechnologyOfProjectList", "SyncProjectTechnologyFromProject", "name", "props", "sysProjectId", "type", "String", "default", "data", "loading", "pLoading", "projectList", "searchForm", "ProjectCode", "created", "fetchProjectList", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "params", "res", "table", "wrap", "_callee$", "_context", "prev", "next", "sent", "IsSucceed", "Data", "$message", "error", "Message", "t0", "console", "finish", "stop", "handleCancel", "$emit", "handleConfirm", "_this2", "warning", "From_Sys_Project_Id", "To_Sys_Project_Id", "then", "success", "finally"], "sources": ["src/views/PRO/project-config/product-mfg-path/component/ProjectAddDialog.vue"], "sourcesContent": ["<template>\r\n  <div class=\"add-project-container\">\r\n    <div class=\"instruction\">\r\n      请选择项目，添加所选项目的所有生产路径\r\n    </div>\r\n    <div class=\"search-section\">\r\n      <el-form :model=\"searchForm\" inline>\r\n        <el-form-item v-if=\"!projectList.length\" label=\"项目名称：\">\r\n          <div v-loading=\"pLoading\">暂无可同步的项目</div>\r\n        </el-form-item>\r\n        <el-form-item v-else label=\"项目名称：\">\r\n          <el-select\r\n            v-model=\"searchForm.ProjectCode\"\r\n            clearable\r\n            filterable\r\n            placeholder=\"请选择项目\"\r\n            style=\"width: 200px\"\r\n          >\r\n            <el-option v-for=\"item in projectList\" :key=\"item.Sys_Project_Id\" :label=\"item.Short_Name\" :value=\"item.Sys_Project_Id\" :disabled=\"item.Sys_Project_Id===sysProjectId\" />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n      </el-form>\r\n    </div>\r\n\r\n    <!-- 提示信息 -->\r\n\r\n    <!-- 底部按钮 -->\r\n    <div class=\"footer-actions\">\r\n      <el-button @click=\"handleCancel\">取消</el-button>\r\n      <el-button v-if=\"projectList.length\" type=\"primary\" :loading=\"loading\" :disabled=\"searchForm.ProjectCode === ''\" @click=\"handleConfirm\">\r\n        确定\r\n      </el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetTechnologyOfProjectList } from '@/api/PRO/technology-lib'\r\nimport { SyncProjectTechnologyFromProject } from '@/api/PRO/technology-lib'\r\nexport default {\r\n  name: 'AddProject',\r\n  props: {\r\n    sysProjectId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      pLoading: false,\r\n      projectList: [],\r\n      searchForm: {\r\n        ProjectCode: ''\r\n      }\r\n\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchProjectList()\r\n  },\r\n  methods: {\r\n\r\n    // 获取项目列表\r\n    async fetchProjectList() {\r\n      try {\r\n        const params = {\r\n        }\r\n        this.pLoading = true\r\n        const res = await GetTechnologyOfProjectList(params)\r\n        if (res.IsSucceed) {\r\n          const table = res?.Data || []\r\n          this.projectList = table\r\n          // this.projectList = table.filter(item => item.Sys_Project_Id !== this.sysProjectId)\r\n        } else {\r\n          this.$message.error(res.Message || '获取项目列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取项目列表失败:', error)\r\n        this.$message.error('获取项目列表失败')\r\n      } finally {\r\n        this.pLoading = false\r\n      }\r\n    },\r\n    handleCancel() {\r\n      this.$emit('close')\r\n    },\r\n    // 确认选择\r\n    handleConfirm() {\r\n      if (this.searchForm.ProjectCode === '') {\r\n        this.$message.warning('请至少选择一个项目')\r\n        return\r\n      }\r\n      this.loading = true\r\n\r\n      SyncProjectTechnologyFromProject({\r\n        From_Sys_Project_Id: this.searchForm.ProjectCode,\r\n        To_Sys_Project_Id: this.sysProjectId\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$emit('refresh')\r\n          this.$emit('close')\r\n          this.$message.success('同步成功！')\r\n        } else {\r\n          this.$message.error(res.Message || '同步失败')\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.add-project-container {\r\n\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .search-section {\r\n    background: #fff;\r\n    border-radius: 4px;\r\n  }\r\n\r\n  .instruction {\r\n    background: #f0f9ff;\r\n    border: 1px solid #b3d8ff;\r\n    color: #1890ff;\r\n    padding: 12px 16px;\r\n    border-radius: 4px;\r\n    margin-bottom: 16px;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .table-section {\r\n    flex: 1;\r\n    background: #fff;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .footer-actions {\r\n    margin-top: 16px;\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    padding: 0px 8px 0 0;\r\n    background: #fff;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCA,SAAAA,0BAAA;AACA,SAAAC,gCAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACAC,YAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,QAAA;MACAC,WAAA;MACAC,UAAA;QACAC,WAAA;MACA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,gBAAA;EACA;EACAC,OAAA;IAEA;IACAD,gBAAA,WAAAA,iBAAA;MAAA,IAAAE,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,MAAA,EAAAC,GAAA,EAAAC,KAAA;QAAA,OAAAL,mBAAA,GAAAM,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAEAN,MAAA,IACA;cACAL,KAAA,CAAAP,QAAA;cAAAiB,QAAA,CAAAE,IAAA;cAAA,OACA7B,0BAAA,CAAAsB,MAAA;YAAA;cAAAC,GAAA,GAAAI,QAAA,CAAAG,IAAA;cACA,IAAAP,GAAA,CAAAQ,SAAA;gBACAP,KAAA,IAAAD,GAAA,aAAAA,GAAA,uBAAAA,GAAA,CAAAS,IAAA;gBACAf,KAAA,CAAAN,WAAA,GAAAa,KAAA;gBACA;cACA;gBACAP,KAAA,CAAAgB,QAAA,CAAAC,KAAA,CAAAX,GAAA,CAAAY,OAAA;cACA;cAAAR,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAS,EAAA,GAAAT,QAAA;cAEAU,OAAA,CAAAH,KAAA,cAAAP,QAAA,CAAAS,EAAA;cACAnB,KAAA,CAAAgB,QAAA,CAAAC,KAAA;YAAA;cAAAP,QAAA,CAAAC,IAAA;cAEAX,KAAA,CAAAP,QAAA;cAAA,OAAAiB,QAAA,CAAAW,MAAA;YAAA;YAAA;cAAA,OAAAX,QAAA,CAAAY,IAAA;UAAA;QAAA,GAAAlB,OAAA;MAAA;IAEA;IACAmB,YAAA,WAAAA,aAAA;MACA,KAAAC,KAAA;IACA;IACA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,SAAA/B,UAAA,CAAAC,WAAA;QACA,KAAAoB,QAAA,CAAAW,OAAA;QACA;MACA;MACA,KAAAnC,OAAA;MAEAR,gCAAA;QACA4C,mBAAA,OAAAjC,UAAA,CAAAC,WAAA;QACAiC,iBAAA,OAAA1C;MACA,GAAA2C,IAAA,WAAAxB,GAAA;QACA,IAAAA,GAAA,CAAAQ,SAAA;UACAY,MAAA,CAAAF,KAAA;UACAE,MAAA,CAAAF,KAAA;UACAE,MAAA,CAAAV,QAAA,CAAAe,OAAA;QACA;UACAL,MAAA,CAAAV,QAAA,CAAAC,KAAA,CAAAX,GAAA,CAAAY,OAAA;QACA;MACA,GAAAc,OAAA;QACAN,MAAA,CAAAlC,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
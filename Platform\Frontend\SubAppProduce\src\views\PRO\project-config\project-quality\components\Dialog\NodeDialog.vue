<template>
  <div>
    <el-form ref="form" :rules="rules" :model="form" label-width="140px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="质检节点" prop="Display_Name">
            <el-select
              v-model="form.Display_Name"
              :disabled="Node_Code_Com"
              clearable
              style="width: 100%"
              filterable
              allow-create
              placeholder="请输入质检节点"
              @change="changeNodeCode"
            >
              <el-option
                v-for="(item, index) in QualityNodeList"
                :key="index"
                :label="item.Name"
                :value="item.Name"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="专检类型" prop="Change_Check_Type">
            <el-select
              v-model="form.Change_Check_Type"
              style="width: 100%"
              placeholder="请选择专检类型"
              multiple
              :disabled="Node_Code_Com"
              @change="SelectType"
              @remove-tag="removeType"
            >
              <el-option
                v-for="(item, index) in CheckTypeList"
                :key="index"
                :label="item.Name"
                :value="item.Id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="质量员"
            prop="ZL_UserIds"
            :rules="ZL_UserIds_Rules"
          >
            <el-select
              v-model="form.ZL_UserIds"
              filterable
              clearable
              multiple
              style="width: 100%"
              placeholder="请选择质量员"
              :disabled="
                Node_Code_Com ||
                  (form.Change_Check_Type[0] != 1 &&
                    form.Change_Check_Type.length != 2)
              "
              @change="changeZLUser"
            >
              <el-option
                v-for="(item, index) in UserList"
                :key="index"
                :label="item.Display_Name"
                :value="item.Id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="探伤员"
            prop="TC_UserIds"
            :rules="TC_UserIds_Rules"
          >
            <el-select
              v-model="form.TC_UserIds"
              filterable
              clearable
              multiple
              style="width: 100%"
              :disabled="
                Node_Code_Com ||
                  (form.Change_Check_Type[0] != 2 &&
                    form.Change_Check_Type.length != 2)
              "
              placeholder="请选择探伤员"
              @change="changeTCUser"
            >
              <el-option
                v-for="(item, index) in UserList"
                :key="index"
                :label="item.Display_Name"
                :value="item.Id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="专检方式" prop="Check_Style">
            <el-select
              v-model="form.Check_Style"
              clearable
              :disabled="Node_Code_Com"
              style="width: 100%"
              placeholder="请选择专检方式"
            >
              <el-option
                v-for="(item, index) in CheckStyleList"
                :key="index"
                :label="item.Name"
                :value="item.Id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="质检流程">
            <el-radio-group v-model="qualityInspection">
              <el-radio :label="1">专检</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col v-if="form.Check_Style===0 && form.Change_Check_Type && form.Change_Check_Type.includes(1)" :span="12">
          <el-form-item label="质量要求合格率(%)" prop="Zl_Demand_Spot_Check_Rate">
            <el-input v-model="form.Zl_Demand_Spot_Check_Rate" class="cs-number-btn-hidden w100" placeholder="请输入" clearable @input="(value) => form.Zl_Demand_Spot_Check_Rate = handleInputFormat(value, 2, 100)" />
          </el-form-item>
        </el-col>
        <el-col v-if="form.Check_Style===0 && form.Change_Check_Type && form.Change_Check_Type.includes(1)" :span="12">
          <el-form-item label="质量要求抽检率(%)" prop="Zl_Requirement_Spot_Check_Rate">
            <el-input v-model="form.Zl_Requirement_Spot_Check_Rate" class="cs-number-btn-hidden w100" placeholder="请输入" clearable @input="(value) => form.Zl_Requirement_Spot_Check_Rate = handleInputFormat(value, 2, 100)" />
          </el-form-item>
        </el-col>
        <el-col v-if="form.Check_Style===0 && form.Change_Check_Type && form.Change_Check_Type.includes(2)" :span="12">
          <el-form-item label="探伤要求合格率(%)" prop="Ts_Demand_Spot_Check_Rate">
            <el-input v-model="form.Ts_Demand_Spot_Check_Rate" class="cs-number-btn-hidden w100" placeholder="请输入" clearable @input="(value) => form.Ts_Demand_Spot_Check_Rate = handleInputFormat(value, 2, 100)" />
          </el-form-item>
        </el-col>
        <el-col v-if="form.Check_Style===0 && form.Change_Check_Type && form.Change_Check_Type.includes(2)" :span="12">
          <el-form-item label="探伤要求抽检率(%)" prop="Ts_Requirement_Spot_Check_Rate">
            <el-input v-model="form.Ts_Requirement_Spot_Check_Rate" class="cs-number-btn-hidden w100" placeholder="请输入" clearable @input="(value) => form.Ts_Requirement_Spot_Check_Rate = handleInputFormat(value, 2, 100)" />
          </el-form-item>
        </el-col>
        <el-col v-if="form.Change_Check_Type && form.Change_Check_Type.includes(2)" :span="12">
          <el-form-item label="探伤要求时间(h)" prop="Ts_Require_Time">
            <el-input v-model="form.Ts_Require_Time" placeholder="请输入" @input="(value) => form.Ts_Require_Time = handleInputFormat(value, 1)" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item style="text-align: right">
            <el-button @click="$emit('close')">关 闭</el-button>
            <el-button
              type="primary"
              @click="handleSubmit('form')"
            >确 定</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { SaveNode } from '@/api/PRO/factorycheck'
import { GetEntityNode } from '@/api/PRO/factorycheck'
// import { SaveCheckType } from "@/api/PRO/factorycheck";
import { GetFactoryPeoplelist } from '@/api/PRO/factorycheck'
// import { GetProcessCodeList } from '@/api/PRO/factorycheck'
export default {
  data() {
    return {
      mode: '', // 区分项目和工厂
      ProjectId: '', // 项目Id
      Check_Object_Id: '',
      Bom_Level: '',
      form: {
        Node_Code: '',
        Change_Check_Type: [],
        Display_Name: '',
        TC_UserId: '',
        ZL_UserId: '',
        Demand_Spot_Check_Rate: undefined,
        Requirement_Spot_Check_Rate: undefined,
        TC_UserIds: [],
        ZL_UserIds: [],
        Check_Style: '',
        Ts_Require_Time: '',
        Zl_Demand_Spot_Check_Rate: undefined,
        Zl_Requirement_Spot_Check_Rate: undefined,
        Ts_Demand_Spot_Check_Rate: undefined,
        Ts_Requirement_Spot_Check_Rate: undefined
      },

      rules: {
        Display_Name: [
          { required: true, message: '请填写完整表单', trigger: 'change' }
        ],
        Check_Type: [
          { required: true, message: '请填写完整表单', trigger: 'change' }
        ],
        Change_Check_Type: [
          { required: true, validator: this.Check_Type_rules, message: '请填写完整表单', trigger: 'change' }
        ],
        Check_Style: [
          { required: true, message: '请填写完整表单', trigger: 'change' }
        ]
      },
      rules_Zl: { required: true, message: '请填写完整表单', trigger: 'bur' },
      rules_Tc: { required: true, message: '请填写完整表单', trigger: 'bur' },
      ZL_UserIds_Rules: [
        { required: true, validator: this.Check_ZL_UserIds, message: '请填写完整表单', trigger: 'change' }
      ],
      TC_UserIds_Rules: [
        { required: true, validator: this.Check_TC_UserIds, message: '请填写完整表单', trigger: 'change' }
      ],
      title: '',
      editInfo: {},
      QualityNodeList: [{ Name: '入库' }, { Name: '出库' }], // 质检节点列表
      CheckTypeList: [
        {
          Name: '质量',
          Id: 1
        },
        {
          Name: '探伤',
          Id: 2
        }
      ], // 质检类型
      UserList: [], // 质量员，探伤人员
      CheckStyleList: [
        {
          Name: '抽检',
          Id: 0
        },
        {
          Name: '全检',
          Id: 1
        }
      ], // 质检方式
      qualityInspection: 1
    }
  },
  computed: {
    Node_Code_Com: function() {
      if (this.form.Node_Code) {
        return true
      } else {
        return false
      }
    }
  },
  mounted() {
    this.getFactoryPeoplelist()
  },
  methods: {
    Check_ZL_UserIds(rule, value, callback) {
      if (this.form.Change_Check_Type && this.form.Change_Check_Type.includes(1) && this.form.ZL_UserIds.length === 0) {
        callback(new Error('请填写完整表单'))
      } else {
        callback()
      }
    },
    Check_TC_UserIds(rule, value, callback) {
      if (!this.Node_Code_Com && !(this.form.Change_Check_Type[0] !== 2 && this.form.Change_Check_Type.length !== 2) && this.form.TC_UserIds.length === 0) {
        callback(new Error('请填写完整表单'))
      } else {
        callback()
      }
    },
    Check_Type_rules(rule, value, callback) {
      if (this.form.Change_Check_Type.length === 0) {
        callback(new Error('请填写完整表单'))
      } else {
        callback()
      }
    },
    SelectType(item) {
      this.$forceUpdate()
      this.form.Change_Check_Type = item
      if (item.length === 1) {
        this.form.Check_Type = item[0]
      } else if (item.length === 2) {
        this.form.Check_Type = -1
      }

      if (!item.includes(1)) {
        this.form.ZL_UserId = ''
        this.form.ZL_UserIds = []
      }
      if (!item.includes(2)) {
        this.form.TC_UserId = ''
        this.form.TC_UserIds = []
      }
      console.log(this.form.Change_Check_Type)
    },
    removeType(item) {
      console.log(item, 'b')
      // if (item == 1) {
      //   this.form.ZL_UserId = "";
      // } else if (item == 2) {
      //   this.form.TC_UserId = "";
      // }
    },
    clearType(val) {
      console.log(val)
      this.form.ZL_UserId = ''
      this.form.TC_UserId = ''
      this.form.ZL_UserIds = []
      this.form.TC_UserIds = []
    },
    init(title, checkType, data) {
      this.Check_Object_Id = checkType.Id
      this.Bom_Level = checkType.Code
      this.title = title
      if (title === '编辑') {
        console.log(data)
        this.form.Id = data.Id
        this.getEntityNode(data)
      }
      this.getCheckNode()
    },
    async addCheckNode() {
      const { Zl_Demand_Spot_Check_Rate, Zl_Requirement_Spot_Check_Rate, Ts_Demand_Spot_Check_Rate, Ts_Requirement_Spot_Check_Rate, ...others } = this.form
      const submit = {
        ...others,
        Check_Object_Id: this.Check_Object_Id,
        Bom_Level: this.Bom_Level
      }
      if (this.form.Check_Style === 0) { // 抽检
        submit.Zl_Demand_Spot_Check_Rate = Zl_Demand_Spot_Check_Rate
        submit.Zl_Requirement_Spot_Check_Rate = Zl_Requirement_Spot_Check_Rate
        submit.Ts_Demand_Spot_Check_Rate = Ts_Demand_Spot_Check_Rate
        submit.Ts_Requirement_Spot_Check_Rate = Ts_Requirement_Spot_Check_Rate
      }
      await SaveNode(submit).then((res) => {
        if (res.IsSucceed) {
          this.$message({
            type: 'success',
            message: '保存成功'
          })
          this.$emit('refresh')
          this.$emit('close')
          this.dialogData = {}
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    getFactoryPeoplelist() {
      GetFactoryPeoplelist().then((res) => {
        if (res.IsSucceed) {
          console.log(res.Data)
          this.UserList = res.Data
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    // 判断是工厂还是项目获取质检节点
    getCheckNode() {
      const Platform =
        localStorage.getItem('Platform') || localStorage.getItem('CurPlatform')
      if (Platform === '2') {
        this.mode = 'factory'
        // this.getFactoryNode();
      }
      // 获取项目/工厂id
      this.ProjectId =
        this.mode === 'factory'
          ? localStorage.getItem('CurReferenceId')
          : this.ProjectId
    },
    // 如果是工厂获取质检节点
    // getFactoryNode() {
    //   GetProcessCodeList({sys_workobject_id:this.Check_Object_Id}).then((res) => {
    //     if (res.IsSucceed) {
    //       let CheckJson = res.Data;
    //       CheckJson.push({ Name: "入库" }, { Name: "出库" });
    //       console.log(CheckJson);
    //       this.QualityNodeList = CheckJson;
    //       console.log(this.QualityNodeList);
    //     } else {
    //       this.$message({
    //         type: "error",
    //         message: res.Message,
    //       });
    //     }
    //   });
    // },

    // 质检节点获取质检节点名
    changeNodeCode(val) {
      this.form = {
        Node_Code: '',
        Change_Check_Type: [],
        Display_Name: '',
        TC_UserId: '',
        ZL_UserId: '',
        TC_UserIds: [],
        ZL_UserIds: [],
        Check_Style: ''
      }
      this.form.Display_Name = val
      this.form.Node_Code = null
      // this.form.Change_Check_Type = [];
      // try {
      //   this.form.Node_Code = this.QualityNodeList.find((v) => {
      //     return v.Name == val;
      //   }).Id;
      // } catch (err) {
      //   this.form.Node_Code = null;
      // }
      // console.log
      // let arr = {};
      // arr = this.QualityNodeList.find((v) => {
      //   return v.Name == val;
      // });
      // console.log(arr);
      // if (arr) {
      //   this.form.Check_Style = arr.Check_Style ? Number(arr.Check_Style) : "";
      //   arr.Is_Need_TC &&(this.form.Change_Check_Type.push(2))
      //   arr.Is_Need_ZL &&( this.form.Change_Check_Type.push(1));
      //   this.SelectType(this.form.Change_Check_Type);

      //   this.form.ZL_UserId = arr.ZL_Check_UserId ? arr.ZL_Check_UserId: "";
      //   this.form.TC_UserId = arr.TC_Check_UserId ? arr.TC_Check_UserId : ""
      //   console.log(this.form.ZL_UserId)
      // }
    },
    changeZLUser(val) {
      console.log(val)
      // 解决下拉框回显问题
      this.$forceUpdate()
      this.form.ZL_UserId = ''
      for (let i = 0; i < val.length; i++) {
        if (i === val.length - 1) {
          this.form.ZL_UserId += val[i]
        } else {
          this.form.ZL_UserId += val[i] + ','
        }
      }
      console.log(this.form.ZL_UserId, 'this.form.ZL_UserId ')
    },
    changeTCUser(val) {
      this.$forceUpdate()
      this.form.TC_UserId = ''
      for (let i = 0; i < val.length; i++) {
        if (i === val.length - 1) {
          this.form.TC_UserId += val[i]
        } else {
          this.form.TC_UserId += val[i] + ','
        }
      }
      // 解决下拉框回显问题

      console.log(this.form.TC_UserId, 'this.form.TC_UserId ')
    },
    getEntityNode(data) {
      GetEntityNode({ id: data.Id }).then((res) => {
        if (res.IsSucceed) {
          console.log(res.Data)
          this.form = res.Data[0]
          this.form.Change_Check_Type = []
          if (this.form.Check_Type === 1 || this.form.Check_Type === 2) {
            this.form.Change_Check_Type.push(this.form.Check_Type)
          } else if (this.form.Check_Type === -1) {
            this.form.Change_Check_Type = [1, 2]
          } else {
            this.form.Change_Check_Type = []
          }
          this.form.ZL_UserIds = this.form.ZL_UserId ? this.form.ZL_UserId.split(',') : []
          this.form.TC_UserIds = this.form.TC_UserId ? this.form.TC_UserId.split(',') : []
          console.log(this.form.ZL_UserIds, this.form.TC_UserId)
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    handleSubmit(form) {
      this.$refs[form].validate((valid) => {
        if (valid) {
          this.addCheckNode()
        } else {
          return false
        }
      })
    },
    handleInputFormat(value, dp, type) {
      // 如果输入为空，直接返回空
      if (value === '' || value === null || value === undefined) {
        return ''
      }

      // 转换为字符串进行处理
      let inputValue = String(value)

      // 移除所有非数字和非小数点的字符（包括负号）
      inputValue = inputValue.replace(/[^0-9.]/g, '')

      // 如果只是单独的小数点，返回空
      if (inputValue === '.') {
        return ''
      }

      // 确保只有一个小数点
      const dotCount = (inputValue.match(/\./g) || []).length
      if (dotCount > 1) {
        // 如果有多个小数点，只保留第一个
        const firstDotIndex = inputValue.indexOf('.')
        inputValue = inputValue.substring(0, firstDotIndex + 1) + inputValue.substring(firstDotIndex + 1).replace(/\./g, '')
      }

      // 根据 dp 参数限制小数位数
      if (inputValue.includes('.') && dp) {
        const parts = inputValue.split('.')
        if (parts[1] && parts[1].length > dp) {
          inputValue = parts[0] + '.' + parts[1].substring(0, dp)
        }
      }

      // 如果处理后为空字符串，返回空
      if (inputValue === '') {
        return ''
      }

      // 转换为数字进行范围检查
      const numValue = parseFloat(inputValue)

      // 如果不是有效数字，返回空
      if (isNaN(numValue)) {
        return ''
      }

      // 最小值限制为0
      if (numValue < 0) {
        return '0'
      }

      // 如果有 type 参数，限制最大值
      if (type && numValue > type) {
        // 根据 dp 格式化最大值
        if (dp) {
          return type.toFixed(dp).replace(/\.?0+$/, '')
        } else {
          return type.toString()
        }
      }

      // 返回处理后的值
      return inputValue
    }
  }
}
</script>

<style scoped></style>

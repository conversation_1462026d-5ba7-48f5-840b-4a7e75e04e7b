<template>
  <div class="app-container abs100">
    <ProjectData />
    <div class="card-x">
      <div class="card-x-top">
        <el-button type="primary" @click="openAddDialog">从项目添加</el-button>
        <el-button type="primary" @click="openCompanyDialog">从公司添加</el-button>
      </div>
      <el-tabs v-model="activeType" type="card" @tab-click="handleClick">
        <el-tab-pane v-for="item in bomList" :key="item.Code" :label="item.Display_Name" :name="item.Code" />
      </el-tabs>
      <div class="card-x-content">
        <tree-data ref="tree" :key="activeType" :active-type="activeType" :type-code="typeCode" :type-id="typeId" @nodeClick="nodeClick" />
        <div class="right-card">
          <el-form v-if="showForm" ref="form" :model="form" label-width="120px">
            <el-form-item :label="`${levelName}大类名称：`" prop="Name">
              <!-- <el-input v-model.trim="form.Name" clearable maxlength="50" /> -->
              {{ form.Name }}
            </el-form-item>
            <el-form-item :label="`${levelName}大类编号：`" prop="Code">
              <!-- <el-input v-model="form.Code" disabled /> -->
              {{ form.Code }}
            </el-form-item>
            <el-form-item label="生产周期：" prop="Lead_Time">
              <!-- <el-input-number v-model.number="form.Lead_Time" class="cs-number-btn-hidden w100" clearable /> -->
              {{ form.Lead_Time }}
            </el-form-item>
            <el-form-item v-if="showDirect" label="直发件：" prop="Is_Component">
              <!-- <el-radio-group v-model="form.Is_Component">
                  <el-radio :label="true">否</el-radio>
                  <el-radio :label="false">是</el-radio>
                </el-radio-group> -->
              <el-tag :type="form.Is_Component ? 'danger' : 'success' ">
                {{ form.Is_Component ? '否' : '是' }}
              </el-tag>
              <!-- {{ form.Is_Component }} -->
            </el-form-item>
            <!-- <el-form-item>
              <el-button v-if="level<3" type="text" icon="el-icon-plus" @click="addNext">新增下一级</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :loading="submitLoading" :disabled="isDefault" @click="submit">保存</el-button>
              <el-button type="danger" :loading="deleteLoading" :disabled="hasChildrenNode || isDefault" @click="handleDelete">删除</el-button>
            </el-form-item> -->
          </el-form>
        </div>
      </div>

    </div>

    <el-dialog
      v-dialogDrag
      :title="title"
      class="plm-custom-dialog"
      :visible.sync="dialogVisible"
      :width="width"
      top="5vh"
      @close="handleClose"
    >
      <component
        :is="currentComponent"
        v-if="dialogVisible"
        ref="content"
        :type-id="typeId"
        :add-level="addLevel"
        :parent-id="parentId"
        :active-type="activeType"
        :type-code="typeCode"
        :is-comp="isComp"
        :show-direct="showDirect"
        @close="handleClose"
        @getTreeList="getTreeData"
      />
    </el-dialog>

  </div>
</template>

<script>

import TreeData from './component/TreeData'
import ProjectAdd from './component/ProjectAdd'
import { DeleteComponentType, GetComponentTypeEntity, SaveProBimComponentType } from '@/api/PRO/component-type'
import { GetBOMInfo } from '@/views/PRO/bom-setting/utils'
import { DeletePartType, GetPartTypeEntity, SavePartType } from '@/api/PRO/partType'
import { GetAllEntities } from '@/api/PRO/settings'
import ProjectData from '../components/ProjectData.vue'
import CompanyAdd from './component/CompanyAdd'
export default {
  name: 'PROProjectProductType',
  components: {
    TreeData,
    ProjectAdd,
    ProjectData,
    CompanyAdd
  },
  data() {
    return {
      bomList: [],
      width: '30%',
      typeCode: '',
      typeId: '',
      level: 1,
      addLevel: undefined,
      dialogVisible: false,
      submitLoading: false,
      deleteLoading: false,
      showForm: false,
      isDefault: false,
      hasChildrenNode: true,
      currentComponent: '',
      activeType: '-1',
      parentId: '',
      title: '',
      form: {
        Name: '',
        Code: '',
        Is_Component: '',
        Lead_Time: 0
      },
      // rules: {
      //   Name: [
      //     { required: true, message: '请输入名称', trigger: 'blur' }
      //   ],
      //   Code: [
      //     { required: true, message: '请输入编码', trigger: 'blur' }
      //   ],
      //   Is_Component: [
      //     { required: true, message: '请选择是否直发件', trigger: 'change' }
      //   ],
      //   Lead_Time: [
      //     { required: true, message: '请输入周期', trigger: 'blur' }
      //   ]
      // },
      Is_Component: ''
    }
  },
  computed: {
    levelName() {
      return this.level === 1 ? '一级' : (this.level === 2 ? '二级' : (this.level === 3 ? '三级' : ''))
    },
    isComp() {
      return this.activeType === '-1'
    },
    showDirect() {
      return this.activeType !== '0'
    }
  },
  async created() {
    await this.getProfession()
    const { list } = await GetBOMInfo()
    this.bomList = list
    // TreeData 组件会在自己的 mounted 中自动调用 fetchData()
  },
  methods: {
    openAddDialog() {
      this.currentComponent = 'ProjectAdd'
      this.title = '添加'
      this.dialogVisible = true
      this.width = '80%'
    },
    openCompanyDialog() {
      this.currentComponent = 'CompanyAdd'
      this.title = '添加'
      this.dialogVisible = true
      this.width = '50%'
    },
    // addNext() {
    //   this.currentComponent = 'Add'
    //   this.addLevel = this.level + 1
    //   this.title = `新增下一级`
    //   this.parentId = this.form.Id
    //   this.dialogVisible = true
    // },
    async getProfession() {
      const res = await GetAllEntities({
        companyId: localStorage.getItem('Last_Working_Object_Id'),
        is_System: false
      })
      if (res.IsSucceed) {
        const {
          Code,
          Id
        } = res.Data?.Data?.find(item => item.Code === 'Steel') || {}
        this.typeCode = Code
        this.typeId = Id
        console.log(this.typeCode, this.typeId)
      }
    },
    // showRight(v) {
    //   this.showForm = v
    // },
    handleClick(tab, event) {
      this.showForm = false
      console.log(tab, event)
      // 由于使用了 key，组件会重新创建并在 mounted 中自动调用 fetchData()
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (!valid) {
          return false
        }
        if (this.Is_Component !== this.form.Is_Component) {
          this.$confirm('直发件属性不会同步到已导入构件清单中，确认修改？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.submitConfirm()
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消修改'
            })
          })
        } else {
          this.submitConfirm()
        }
      })
    },
    submitConfirm() {
      this.submitLoading = true
      const submitObj = { ...this.form }
      submitObj.Is_Direct = !submitObj.Is_Component
      submitObj.Professional_Id = this.typeId
      const postFn = this.isComp ? SaveProBimComponentType : SavePartType

      postFn(submitObj).then(res => {
        if (res.IsSucceed) {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.getTreeData()
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      }).finally(_ => {
        this.submitLoading = false
      })
    },
    getTreeData() {
      this.$refs['tree'].fetchData()
    },

    handleClose() {
      this.dialogVisible = false
    },
    nodeClick(node) {
      this.showForm = true
      this.level = node.level
      this.hasChildrenNode = node.childNodes.length > 0
      this.getInfo(node.data.Id)
    },
    async getInfo(id) {
      const postFn = this.isComp ? GetComponentTypeEntity : GetPartTypeEntity
      const res = await postFn({ id })
      if (res.IsSucceed) {
        Object.assign(this.form, res.Data)
        if (this.isComp) {
          this.isDefault = false
          this.Is_Component = res.Data.Is_Component
        } else {
          this.isDefault = !!res.Data.Is_Default
          this.form.Is_Component = !res.Data.Is_Direct
        }
      } else {
        this.$message({
          message: res.Message,
          type: 'error'
        })
      }
    },
    handleDelete() {
      this.$confirm('是否删除当前类别?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        this.deleteLoading = true
        let postFn
        let obj = {}
        if (this.isComp) {
          postFn = DeleteComponentType
          obj = {
            ids: this.form.Id
          }
        } else {
          postFn = DeletePartType
          obj = {
            id: this.form.Id
          }
        }
        postFn(obj).then(res => {
          if (res.IsSucceed) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getTreeData()
            this.$refs['tree'].resetKey(this.form.Id)
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        }).finally(_ => {
          this.deleteLoading = false
          this.showForm = false
        })
      }).catch((e) => {
        console.log(e, 3313)
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  display: flex;
  min-width: 998px;
  overflow: hidden;

  .top-x {
    line-height: 48px;
    height: 48px;
  }
  .card-x-top {
    display: flex;
    align-items: center;
    padding: 16px 16px 0 16px;
    background-color: #FFFFFF;
  }

  .card-x {
    overflow: hidden;
    // background-color: #FFFFFF;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    .el-tabs{
      width: 100%;
      padding: 16px 16px 0 16px;
      background-color: #FFFFFF;
    }
    .card-x-content{
      display: flex;
      flex: 1;
      overflow: hidden;
    }

    .right-card {
      display: flex;
      flex-direction: column;
      flex: 1;
      border-radius: 4px;
      background-color: #FFFFFF;
      .el-form{
        width: 50%;
        margin:  auto;
      }
    }
  }
}
</style>

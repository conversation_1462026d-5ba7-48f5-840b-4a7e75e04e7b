{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\Add.vue?vue&type=template&id=3aa2eeae&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\process-settings\\component\\Add.vue", "mtime": 1758004693525}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}
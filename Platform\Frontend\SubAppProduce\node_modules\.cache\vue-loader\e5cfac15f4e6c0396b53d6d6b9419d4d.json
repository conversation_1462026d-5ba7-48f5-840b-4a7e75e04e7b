{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckItem.vue?vue&type=template&id=75c32573&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\project-config\\project-quality\\components\\CheckItem.vue", "mtime": 1758086014839}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750141674740}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgc3R5bGU9ImhlaWdodDogY2FsYygxMDB2aCAtIDMwMHB4KSI+CiAgPHZ4ZS10YWJsZQogICAgdi1sb2FkaW5nPSJ0YkxvYWRpbmciCiAgICA6ZW1wdHktcmVuZGVyPSJ7bmFtZTogJ05vdERhdGEnfSIKICAgIHNob3ctaGVhZGVyLW92ZXJmbG93CiAgICBlbGVtZW50LWxvYWRpbmctc3Bpbm5lcj0iZWwtaWNvbi1sb2FkaW5nIgogICAgZWxlbWVudC1sb2FkaW5nLXRleHQ9IuaLvOWRveWKoOi9veS4rSIKICAgIGVtcHR5LXRleHQ9IuaaguaXoOaVsOaNriIKICAgIGhlaWdodD0iMTAwJSIKICAgIGFsaWduPSJsZWZ0IgogICAgc3RyaXBlCiAgICA6ZGF0YT0idGJEYXRhIgogICAgcmVzaXphYmxlCiAgICA6YXV0by1yZXNpemU9InRydWUiCiAgICBjbGFzcz0iY3MtdnhlLXRhYmxlIgogICAgOnRvb2x0aXAtY29uZmlnPSJ7IGVudGVyYWJsZTogdHJ1ZSB9IgogID4KICAgIDx2eGUtY29sdW1uCiAgICAgIHNob3ctb3ZlcmZsb3c9InRvb2x0aXAiCiAgICAgIHNvcnRhYmxlCiAgICAgIGZpZWxkPSJDaGVja19Db250ZW50IgogICAgICB0aXRsZT0i5qOA5p+l6aG55YaF5a65IgogICAgICB3aWR0aD0iY2FsYygxMDB2aC0yMDBweCkvMiIKICAgIC8+CiAgICA8dnhlLWNvbHVtbgogICAgICBzaG93LW92ZXJmbG93PSJ0b29sdGlwIgogICAgICBzb3J0YWJsZQogICAgICBmaWVsZD0iRWxpZ2liaWxpdHlfQ3JpdGVyaWEiCiAgICAgIHRpdGxlPSLlkIjmoLzmoIflh4YiCiAgICAgIHdpZHRoPSJjYWxjKDEwMHZoLTIwMHB4KS8yIgogICAgLz4KICAgIDx2eGUtY29sdW1uIGZpeGVkPSJyaWdodCIgdGl0bGU9IuaTjeS9nCIgd2lkdGg9IjIwMCIgYWxpZ249ImNlbnRlciIgc2hvdy1vdmVyZmxvdz4KICAgICAgPHRlbXBsYXRlICNkZWZhdWx0PSJ7IHJvdyB9Ij4KICAgICAgICA8ZWwtYnV0dG9uIHR5cGU9InRleHQiIEBjbGljaz0iZWRpdEV2ZW50KHJvdykiPue8lui+kTwvZWwtYnV0dG9uPgogICAgICAgIDwhLS0gPGVsLWRpdmlkZXIgZGlyZWN0aW9uPSJ2ZXJ0aWNhbCIgLz4KICAgICAgICA8ZWwtYnV0dG9uIHR5cGU9InRleHQiIEBjbGljaz0icmVtb3ZlRXZlbnQocm93KSI+5Yig6ZmkPC9lbC1idXR0b24+IC0tPgogICAgICA8L3RlbXBsYXRlPgogICAgPC92eGUtY29sdW1uPgogIDwvdnhlLXRhYmxlPgo8L2Rpdj4K"}, null]}
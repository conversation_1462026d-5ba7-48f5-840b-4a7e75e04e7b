{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\ship\\component\\Import.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\basic-information\\ship\\component\\Import.vue", "mtime": 1758677034219}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgVXBsb2FkRXhjZWwgZnJvbSAnQC9jb21wb25lbnRzL1VwbG9hZEV4Y2VsJw0KaW1wb3J0IHsgSW1wb3J0Qm9hdCwgQm9hdERhdGFUZW1wbGF0ZSB9IGZyb20gJ0AvYXBpL1BSTy9jYXInDQppbXBvcnQgeyBjb21iaW5lVVJMIH0gZnJvbSAnQC91dGlscycNCmltcG9ydCB7IEdldEZhY3RvcnlMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL3Byby1zY2hlZHVsZXMnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ0ltcG9ydCcsDQogIGNvbXBvbmVudHM6IHsNCiAgICBVcGxvYWRFeGNlbA0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBvcHRpb25zOiBbXSwNCiAgICAgIGJ0bkxvYWRpbmc6IGZhbHNlLA0KICAgICAgLy8gUHJvamVjdElkOiAnJywNCiAgICAgIHByb09wdGlvbjogW10sDQogICAgICBmaWxlTGlzdDpbXQ0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCg0KICB9LA0KICBtZXRob2RzOiB7DQogICAgZ2V0RmFjdG9yeSgpIHsNCiAgICAgIEdldEZhY3RvcnlMaXN0KHt9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIHRoaXMucHJvT3B0aW9uID0gcmVzPy5EYXRhDQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlRG93bmxvYWQoKSB7DQogICAgICBCb2F0RGF0YVRlbXBsYXRlKHt9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgd2luZG93Lm9wZW4oY29tYmluZVVSTCh0aGlzLiRiYXNlVXJsLCByZXMuRGF0YSksICdfYmxhbmsnKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGJlZm9yZVVwbG9hZChmaWxlKSB7DQogICAgICBjb25zb2xlLmxvZyhmaWxlKQ0KICAgICAgY29uc3QgZmlsZUZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCkNCiAgICAgIC8vIGZpbGVGb3JtRGF0YS5hcHBlbmQoJ2ZhY3RvcnlJZCcsIHRoaXMuUHJvamVjdElkKQ0KICAgICAgZmlsZUZvcm1EYXRhLmFwcGVuZCgnZmlsZXMnLCBmaWxlKQ0KICAgICAgdGhpcy5idG5Mb2FkaW5nID0gdHJ1ZQ0KICAgICAgSW1wb3J0Qm9hdChmaWxlRm9ybURhdGEpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogJ+WvvOWFpeaIkOWKnycsDQogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycNCiAgICAgICAgICB9KQ0KICAgICAgICAgIHRoaXMuJGVtaXQoJ3JlZnJlc2gnKQ0KICAgICAgICAgIHRoaXMuJGVtaXQoJ2Nsb3NlJykNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZVN1Ym1pdCgpIHsNCiAgICAgIC8qICAgICAgaWYgKCF0aGlzLlByb2plY3RJZCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAn6K+36YCJ5oup5bel5Y6CJywNCiAgICAgICAgICB0eXBlOiAnaW5mbycNCiAgICAgICAgfSkNCiAgICAgICAgcmV0dXJuDQogICAgICB9Ki8NCiAgICAgIGNvbnNvbGUubG9nKHRoaXMuZmlsZUxpc3QpDQogICAgICB0aGlzLiRyZWZzLnVwbG9hZC5oYW5kbGVTdWJtaXQoKQ0KICAgIH0sDQogICAgaGFuZGxlQ2hhbmdlKGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICBjb25zb2xlLmxvZyhmaWxlLGZpbGVMaXN0KQ0KICAgICAgLy8gdGhpcy5maWxlTGlzdCA9IGZpbGVMaXN0LnNsaWNlKC0xKTsNCiAgICAgIHRoaXMuZmlsZUxpc3QgPSBmaWxlTGlzdDsNCiAgICAgIGNvbnNvbGUubG9nKHRoaXMuZmlsZUxpc3QpDQogICAgICBpZihmaWxlTGlzdC5sZW5ndGg+MSkgew0KICAgICAgICB0aGlzLmZpbGVMaXN0LnNoaWZ0KCkNCiAgICAgIH0NCiAgICB9LA0KICB9DQp9DQo="}, {"version": 3, "sources": ["Import.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Import.vue", "sourceRoot": "src/views/PRO/basic-information/ship/component", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"box\">\r\n      <span>1.下载模板</span>\r\n      <el-button size=\"large\" @click=\"handleDownload\">\r\n        <svg-icon icon-class=\"document_form_icon\" />\r\n        船舶模板\r\n      </el-button>\r\n    </div>\r\n\r\n    <div class=\"upload-box\">\r\n      <span style=\"margin-bottom: 20px;display: inline-block\">\r\n        2. 完善内容，重新上传。\r\n      </span>\r\n      <upload-excel ref=\"upload\" :before-upload=\"beforeUpload\" :limit=\"2\" :on-change=\"handleChange\" :file-list = \"fileList\"/>\r\n    </div>\r\n\r\n    <!--    <div class=\"box\">\r\n      <span style=\"display: inline-block;\">3. 选择导入工厂</span>\r\n      <el-select v-model=\"ProjectId\" filterable clearable style=\"width: 70%\" placeholder=\"请选择\">\r\n        <el-option\r\n          v-for=\"item in proOption\"\r\n          :key=\"item.Id\"\r\n          :label=\"item.Name\"\r\n          :value=\"item.Id\"\r\n        />\r\n      </el-select>\r\n    </div>-->\r\n\r\n    <footer class=\"cs-footer\">\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button type=\"primary\" :loading=\"btnLoading\" @click=\"handleSubmit\">确 定</el-button>\r\n    </footer>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UploadExcel from '@/components/UploadExcel'\r\nimport { ImportBoat, BoatDataTemplate } from '@/api/PRO/car'\r\nimport { combineURL } from '@/utils'\r\nimport { GetFactoryList } from '@/api/PRO/pro-schedules'\r\n\r\nexport default {\r\n  name: 'Import',\r\n  components: {\r\n    UploadExcel\r\n  },\r\n  data() {\r\n    return {\r\n      options: [],\r\n      btnLoading: false,\r\n      // ProjectId: '',\r\n      proOption: [],\r\n      fileList:[]\r\n    }\r\n  },\r\n  mounted() {\r\n\r\n  },\r\n  methods: {\r\n    getFactory() {\r\n      GetFactoryList({}).then(res => {\r\n        this.proOption = res?.Data\r\n      })\r\n    },\r\n    handleDownload() {\r\n      BoatDataTemplate({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    beforeUpload(file) {\r\n      console.log(file)\r\n      const fileFormData = new FormData()\r\n      // fileFormData.append('factoryId', this.ProjectId)\r\n      fileFormData.append('files', file)\r\n      this.btnLoading = true\r\n      ImportBoat(fileFormData).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.btnLoading = false\r\n          this.$message({\r\n            message: '导入成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('refresh')\r\n          this.$emit('close')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.btnLoading = false\r\n        }\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      /*      if (!this.ProjectId) {\r\n        this.$message({\r\n          message: '请选择工厂',\r\n          type: 'info'\r\n        })\r\n        return\r\n      }*/\r\n      console.log(this.fileList)\r\n      this.$refs.upload.handleSubmit()\r\n    },\r\n    handleChange(file, fileList) {\r\n      console.log(file,fileList)\r\n      // this.fileList = fileList.slice(-1);\r\n      this.fileList = fileList;\r\n      console.log(this.fileList)\r\n      if(fileList.length>1) {\r\n        this.fileList.shift()\r\n      }\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.box{\r\n  border: 1px dashed #D9DBE2;\r\n  padding: 0 16px;\r\n  display: flex;\r\n  height: 64px;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background-color: #F7F8F9;\r\n  & ~ .box{\r\n    margin-top: 20px;\r\n  }\r\n\r\n}\r\n.upload-box{\r\n  background-color:  #F7F8F9;\r\n  border: 1px dashed #D9DBE2;\r\n  margin-top: 16px;\r\n  padding: 16px;\r\n}\r\n.cs-footer{\r\n  margin-top: 10px;\r\n  text-align: center;\r\n}\r\n</style>\r\n"]}]}
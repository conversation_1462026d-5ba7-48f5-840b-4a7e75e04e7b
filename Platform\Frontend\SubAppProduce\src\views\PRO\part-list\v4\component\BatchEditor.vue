<template>
  <div>
    <el-row v-for="(info, index) in list" :key="info.id" class="item-x">
      <div class="item">
        <label>
          属性名称
          <el-select
            v-model="info.key"
            style="width: calc(100% - 65px)"
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in filterOption(info.key)"
              :key="item.key"
              :label="item.label"
              :value="item.key"
            />
          </el-select>
        </label>
      </div>
      <div class="item" style="line-height: 32px">
        <label>请输入值
          <el-input-number
            v-if="checkType(info.key, 'number')"
            v-model="info.val"
            :min="0"
            class="cs-number-btn-hidden"
          />
          <el-input v-if="checkType(info.key, 'string')" v-model="info.val" />
          <el-select
            v-if="checkType(info.key, 'array') && info.key === 'Is_Main'"
            v-model="info.val"
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in Is_Main_Data"
              :key="item.Id"
              :label="item.Name"
              :value="item.Name"
            />
          </el-select>
          <!-- <el-tree-select
            v-show="checkType(info.key, 'array') && info.key === 'AreaPosition'"
            ref="treeSelect"
            v-model="info.val"
            :tree-params="treeParams"
            style="width: 100%; display: inline-block"
          /> -->
        </label>
      </div>
      <span v-if="index === 0" class="item-span">
        <i class="el-icon-circle-plus-outline" @click="handleAdd" />
      </span>
      <span v-else class="item-span">
        <i class="el-icon-circle-plus-outline" @click="handleAdd" />
        <i
          class="el-icon-remove-outline txt-red"
          @click="handleDelete(index)"
        />
      </span>
    </el-row>
    <div style="text-align: right; width: 100%; padding: 20px 2% 0 0">
      <el-button @click="$emit('close')">取消</el-button>
      <el-button type="primary" :loading="btnLoading" @click="onSubmit">确定</el-button>
    </div>
  </div>
</template>

<script>
import { EditPartpagelist } from '@/api/plm/production'
import { v4 as uuidv4 } from 'uuid'
import { convertCode } from '@/utils/multi-specialty'
import { GetGridByCode } from '@/api/sys'
import { GetUserableAttr } from '@/api/PRO/professionalType'
export default {
  props: {
    typeEntity: {
      type: Object,
      default: () => {}
    },
    AreaId: {
      type: String,
      default: ''
    },
    ProjectId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      btnLoading: false,
      treeParams: {
        'default-expand-all': true,
        filterable: true,
        data: [],
        props: {
          children: 'Children',
          label: 'Label',
          value: 'Id'
        }
      },
      value: '',
      options: [
        {
          key: 'Spec',
          label: '规格',
          type: 'string'
        },
        {
          key: 'Length',
          label: '长度',
          type: 'number'
        },
        {
          key: 'Texture',
          label: '材质',
          type: 'string'
        },
        // {
        //   key: 'Num',
        //   label: '深化数量',
        //   type: 'number'
        // },
        {
          key: 'Weight',
          label: '单重',
          type: 'number'
        },
        {
          key: 'Shape',
          label: '形状',
          type: 'string'
        },
        // {
        //   key: "Component_Code",
        //   label: "所属构件 ",
        //   type: "string",
        // },
        {
          key: 'Is_Main',
          label: '是否主零件',
          type: 'array'
        },
        {
          key: 'Times',
          label: '单数',
          type: 'number'
        },
        {
          key: 'Remark',
          label: '备注',
          type: 'string'
        }
      ],
      list: [
        {
          id: uuidv4(),
          val: undefined,
          key: ''
        }
      ],
      Is_Main_Data: [{ Name: '是', Id: true }, { Name: '否', Id: false }]
    }
  },
  async mounted() {
    await this.getUserableAttr()
    const codeArr = this.options.filter((item, index) => index).map(i => i.key)
    const columns = await this.convertCode(
      this.typeEntity.Code,
      codeArr,
      'plm_parts_page_list'
    )
    console.log(columns)
    this.options = this.options.map((item, index) => {
      if (index) {
        item.label = columns.filter((v) => v.Is_Display).find((i) => i.Code === item.key)?.Display_Name
      }
      return item
    })

    this.options = [...this.options]
    console.log({ columns })
    console.log(this.AreaId)
  },
  methods: {
    // 获取拓展字段
    async getUserableAttr() {
      await GetUserableAttr({
        IsComponent: false,
        Bom_Level: 0
      }).then(res => {
        if (res.IsSucceed) {
          const resData = res.Data
          const expandData = []
          resData.forEach(item => {
            const expandJson = {}
            expandJson.key = item.Code
            expandJson.lable = item.Display_Name
            expandJson.type = 'string'
            expandData.push(expandJson)
          })
          this.options = this.options.concat(expandData)
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    init(list, columnsOption) {
      this.selectList = list
      console.log(list)
      const arr = list.filter(item => item.Component_Code !== null && item.Component_Code !== '')
      console.log(arr)
      this.options = arr.length > 0 ? this.options.filter(v => v.key != 'Num') : this.options
      //  let filterarr = columnsOption.filter(v=> {
      //   return v.Display_Name != "项目名称" && v.Display_Name != "区域" && v.Display_Name != "批次" && v.Code != "Total_Weight" &&  v.Code != "Code" && v.Code != "Times" && v.Code != "Schduling_Count"
      //  })
      //  this.options = filterarr?.map(item => ({ key: item.Code, label: item.Display_Name, type: item.Code === "Is_Main"?"array": item.Code === "Num" || item.Code === "Schduling_Count" || item.Code === "Weight" || item.Code === "Times" || item.Code === "Length" ? "number" : "string"}))
    },
    handleAdd() {
      this.list.push({
        id: uuidv4(),
        val: undefined,
        key: ''
      })
    },
    handleDelete(index) {
      this.list.splice(index, 1)
    },
    async onSubmit() {
      this.btnLoading = true
      const Keysmodel = []
      for (let i = 0; i < this.list.length; i++) {
        console.log(this.list)
        const obj = {}
        const element = this.list[i]
        console.log(element)
        if (!element.val) {
          if (element.key === 'Length' || element.key === 'Num' || element.key === 'Weight' || element.key === 'Times') {
            element.val === 0 ? this.$message({ message: '值不能为0', type: 'warning' }) : this.$message({ message: '值不能为空', type: 'warning' })
          } else {
            this.$message({
              message: '值不能为空',
              type: 'warning'
            })
          }
          this.btnLoading = false
          return
        }
        obj.code = element.key
        obj.value = element.val
        Keysmodel.push(obj)
        console.log(Keysmodel)
      }
      await EditPartpagelist({
        Ids: this.selectList.map((v) => v.Part_Aggregate_Id).toString(),
        Keysmodel,
        Area_Id: this.AreaId,
        Project_Id: this.ProjectId
      }).then((res) => {
        if (res.IsSucceed) {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.$emit('close')
          this.$emit('refresh')
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      }).finally(() => {
        this.btnLoading = false
      })
    },
    filterOption(currentValue) {
      console.log(currentValue)
      return this.options.filter((k) => {
        return (
          (!this.list.map((v) => v.key).includes(k.key) ||
            k.key === currentValue) &&
          k.label
        )
      })
    },

    checkType(key, type) {
      if (!key) return false
      return this.options.find((v) => v.key === key).type === type
    },

    // 获取配置数据
    async getColumnConfiguration(code, mainType = 'plm_parts_page_list') {
      const res = await GetGridByCode({ code: mainType + ',' + code })
      return res.Data.ColumnList
    },

    // 根据Code（数据）获取名称
    async convertCode(typeCode, propsArr = [], mainType) {
      const props = await this.getColumnConfiguration(typeCode, mainType)
      console.log(props)
      const columns = props.filter(i => {
        const arr = propsArr.map(i => i.toLowerCase())
        return arr.includes(i.Code.toLowerCase())
      })
      console.log(columns)
      return columns
    }
  }
}
</script>

<style scoped lang="scss">
[class^="el-icon"] {
  font-size: 24px;
  vertical-align: middle;
  cursor: pointer;
  margin-left: 15px;
}

.item-x {
  display: flex;
  margin-bottom: 20px;
  flex: 0 1 50%;
  justify-content: space-between;

  .item {
    width: 45%;
    white-space: nowrap;
    &:not(:first-of-type) {
      margin-left: 20px;
      .cs-number-btn-hidden,
      .el-input,
      .el-select {
        width: 80%;
      }
    }
  }

  .item-span {
    width: 90px;
    padding-top: 5px;
  }
}
::v-deep {
  .el-tree-select-input {
    width: 80% !important;
  }
}
</style>

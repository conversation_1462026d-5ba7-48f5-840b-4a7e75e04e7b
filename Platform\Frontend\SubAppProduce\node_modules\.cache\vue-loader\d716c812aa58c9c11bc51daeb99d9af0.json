{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new\\components\\WithdrawHistory.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new\\components\\WithdrawHistory.vue", "mtime": 1757468128011}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRTY2hkdWxpbmdDYW5jZWxIaXN0b3J5LCBHZXRQYXJ0U2NoZHVsaW5nQ2FuY2VsSGlzdG9yeSB9IGZyb20gJ0AvYXBpL1BSTy9wcm9kdWN0aW9uLXRhc2snDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgcHJvcHM6IHsNCiAgICBjb21OYW1lOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnJw0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgdGFibGVEYXRhOiBbXSwNCiAgICAgIHRiTG9hZGluZzogZmFsc2UNCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgaXNDb20oKSB7DQogICAgICByZXR1cm4gdGhpcy5wYWdlVHlwZSA9PT0gJ2NvbScNCiAgICB9DQogIH0sDQogIGluamVjdDogWydwYWdlVHlwZSddLA0KICBtZXRob2RzOiB7DQogICAgaW5pdChyb3cpIHsNCiAgICAgIHRoaXMucm93ID0gcm93DQogICAgICB0aGlzLmZldGNoRGF0YSgpDQogICAgfSwNCiAgICBmZXRjaERhdGEoKSB7DQogICAgICB0aGlzLnRiTG9hZGluZyA9IHRydWUNCiAgICAgIGxldCByZXF1ZXN0Rm4gPSBudWxsDQogICAgICByZXF1ZXN0Rm4gPSB0aGlzLmlzQ29tID8gR2V0U2NoZHVsaW5nQ2FuY2VsSGlzdG9yeSA6IEdldFBhcnRTY2hkdWxpbmdDYW5jZWxIaXN0b3J5DQogICAgICByZXF1ZXN0Rm4oew0KICAgICAgICBzY2hkdWxpbmdDb2RlOiB0aGlzLnJvdy5TY2hkdWxpbmdfQ29kZQ0KICAgICAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMudGFibGVEYXRhID0gcmVzLkRhdGENCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pLmZpbmFsbHkoXyA9PiB7DQogICAgICAgIHRoaXMudGJMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["WithdrawHistory.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "WithdrawHistory.vue", "sourceRoot": "src/views/PRO/plan-production/schedule-production-new/components", "sourcesContent": ["<template>\r\n  <div class=\"wrapper\">\r\n    <div class=\"h100\">\r\n      <vxe-table\r\n        :empty-render=\"{name: 'NotData'}\"\r\n        show-header-overflow\r\n        empty-text=\"暂无数据\"\r\n        height=\"auto\"\r\n        show-overflow\r\n        :row-config=\"{isCurrent: true, isHover: true}\"\r\n        :loading=\"tbLoading\"\r\n        class=\"cs-vxe-table\"\r\n        align=\"left\"\r\n        stripe\r\n        :data=\"tableData\"\r\n        resizable\r\n        :tooltip-config=\"{ enterable: true }\"\r\n      >\r\n        <vxe-column\r\n          align=\"left\"\r\n          sortable\r\n          min-width=\"150\"\r\n          :field=\"isCom ? 'Comp_Code' : 'Part_Code'\"\r\n          :title=\"`${comName}名称`\"\r\n        />\r\n        <vxe-column\r\n          v-if=\"!isCom\"\r\n          align=\"left\"\r\n          sortable\r\n          min-width=\"120\"\r\n          field=\"Comp_Code\"\r\n          :title=\"`所属${comName}`\"\r\n        />\r\n        <vxe-column\r\n          align=\"left\"\r\n          sortable\r\n          min-width=\"120\"\r\n          field=\"Spec\"\r\n          title=\"规格\"\r\n        />\r\n        <vxe-column\r\n          align=\"left\"\r\n          sortable\r\n          min-width=\"120\"\r\n          field=\"Texture\"\r\n          title=\"材质\"\r\n        />\r\n        <vxe-column\r\n          align=\"center\"\r\n          sortable\r\n          min-width=\"120\"\r\n          field=\"Length\"\r\n          title=\"长度\"\r\n        />\r\n        <vxe-column\r\n          v-if=\"isCom\"\r\n          align=\"left\"\r\n          sortable\r\n          min-width=\"120\"\r\n          field=\"Type\"\r\n          :title=\"`${comName}类型`\"\r\n        />\r\n        <vxe-column\r\n          align=\"center\"\r\n          sortable\r\n          min-width=\"80\"\r\n          field=\"Cancel_Count\"\r\n          title=\"数量\"\r\n        />\r\n        <vxe-column\r\n          align=\"center\"\r\n          sortable\r\n          min-width=\"120\"\r\n          field=\"Weight\"\r\n          title=\"单重(kg)\"\r\n        />\r\n        <vxe-column\r\n          align=\"center\"\r\n          sortable\r\n          min-width=\"150\"\r\n          field=\"Cancel_Date\"\r\n          title=\"撤回时间\"\r\n        >\r\n          <template #default=\"{ row }\">\r\n            {{ row.Cancel_Date | timeFormat(\"{y}-{m}-{d} {h}:{i}:{s}\") }}\r\n          </template>\r\n        </vxe-column>\r\n      </vxe-table>\r\n    </div>\r\n    <footer>\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n    </footer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetSchdulingCancelHistory, GetPartSchdulingCancelHistory } from '@/api/PRO/production-task'\r\n\r\nexport default {\r\n  props: {\r\n    comName: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      tableData: [],\r\n      tbLoading: false\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    }\r\n  },\r\n  inject: ['pageType'],\r\n  methods: {\r\n    init(row) {\r\n      this.row = row\r\n      this.fetchData()\r\n    },\r\n    fetchData() {\r\n      this.tbLoading = true\r\n      let requestFn = null\r\n      requestFn = this.isCom ? GetSchdulingCancelHistory : GetPartSchdulingCancelHistory\r\n      requestFn({\r\n        schdulingCode: this.row.Schduling_Code\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.tableData = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.tbLoading = false\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.wrapper {\r\n  height: 60vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  footer {\r\n    margin: 10px;\r\n    text-align: right;\r\n  }\r\n}\r\n</style>\r\n"]}]}
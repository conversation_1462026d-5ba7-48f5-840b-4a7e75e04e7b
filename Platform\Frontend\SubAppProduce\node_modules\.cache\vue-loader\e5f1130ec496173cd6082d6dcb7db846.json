{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new\\draft.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production-new\\draft.vue", "mtime": 1757468128014}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQoNCmltcG9ydCB7IGNsb3NlVGFnVmlldywgZGVib3VuY2UgfSBmcm9tICdAL3V0aWxzJw0KaW1wb3J0IEJhdGNoUHJvY2Vzc0FkanVzdCBmcm9tICcuL2NvbXBvbmVudHMvQmF0Y2hQcm9jZXNzQWRqdXN0Jw0KaW1wb3J0IHsNCiAgR2V0Q2FuU2NoZHVsaW5nUGFydExpc3QsDQogIEdldENvbXBTY2hkdWxpbmdJbmZvRGV0YWlsLA0KICBHZXREd2csDQogIEdldFBhcnRTY2hkdWxpbmdJbmZvRGV0YWlsLA0KICBHZXRTY2hkdWxpbmdXb3JraW5nVGVhbXMsDQogIFNhdmVDb21wb25lbnRTY2hlZHVsaW5nV29ya3Nob3AsDQogIFNhdmVDb21wU2NoZHVsaW5nRHJhZnQsDQogIFNhdmVQYXJ0U2NoZHVsaW5nRHJhZnROZXcsDQogIFNhdmVQYXJ0U2NoZWR1bGluZ1dvcmtzaG9wTmV3LA0KICBTYXZlU2NoZHVsaW5nVGFza0J5SWQNCn0gZnJvbSAnQC9hcGkvUFJPL3Byb2R1Y3Rpb24tdGFzaycNCmltcG9ydCB7IEdldFN0b3BMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL3Byb2R1Y3Rpb24tdGFzaycNCmltcG9ydCBBZGREcmFmdCBmcm9tICcuL2NvbXBvbmVudHMvYWRkRHJhZnQnDQppbXBvcnQgT3duZXJQcm9jZXNzIGZyb20gJy4vY29tcG9uZW50cy9Pd25lclByb2Nlc3MnDQppbXBvcnQgV29ya3Nob3AgZnJvbSAnLi9jb21wb25lbnRzL1dvcmtzaG9wLnZ1ZScNCmltcG9ydCB7IEdldEdyaWRCeUNvZGUgfSBmcm9tICdAL2FwaS9zeXMnDQppbXBvcnQgeyB1bmlxdWVDb2RlIH0gZnJvbSAnLi9jb25zdGFudCcNCmltcG9ydCB7IHY0IGFzIHV1aWR2NCB9IGZyb20gJ3V1aWQnDQppbXBvcnQgbnVtZXJhbCBmcm9tICdudW1lcmFsJw0KaW1wb3J0IHsgR2V0TGliTGlzdFR5cGUsIEdldFByb2Nlc3NGbG93TGlzdFdpdGhUZWNobm9sb2d5LCBHZXRQcm9jZXNzTGlzdEJhc2UgfSBmcm9tICdAL2FwaS9QUk8vdGVjaG5vbG9neS1saWInDQppbXBvcnQgeyBBcmVhR2V0RW50aXR5IH0gZnJvbSAnQC9hcGkvcGxtL3Byb2plY3RzJw0KaW1wb3J0IHsgbWFwQWN0aW9ucywgbWFwR2V0dGVycyB9IGZyb20gJ3Z1ZXgnDQppbXBvcnQgeyBHZXRQYXJ0VHlwZUxpc3QgfSBmcm9tICdAL2FwaS9QUk8vcGFydFR5cGUnDQppbXBvcnQgbW9tZW50IGZyb20gJ21vbWVudCcNCmltcG9ydCBFeHBhbmRhYmxlU2VjdGlvbiBmcm9tICdAL2NvbXBvbmVudHMvRXhwYW5kYWJsZVNlY3Rpb24vaW5kZXgudnVlJw0KaW1wb3J0IFRyZWVEZXRhaWwgZnJvbSAnQC9jb21wb25lbnRzL1RyZWVEZXRhaWwvaW5kZXgudnVlJw0KaW1wb3J0IHsgR2V0SW5zdGFsbFVuaXRJZE5hbWVMaXN0LCBHZXRQcm9qZWN0QXJlYVRyZWVMaXN0IH0gZnJvbSAnQC9hcGkvUFJPL3Byb2plY3QnDQoNCmltcG9ydCB7IEdldENvbXBUeXBlVHJlZSB9IGZyb20gJ0AvYXBpL1BSTy9mYWN0b3J5Y2hlY2snDQppbXBvcnQgeyBwYXJzZU9zc1VybCB9IGZyb20gJ0AvdXRpbHMvZmlsZScNCmltcG9ydCBEeW5hbWljVGFibGVGaWVsZHMgZnJvbSAnQC9jb21wb25lbnRzL0R5bmFtaWNUYWJsZUZpZWxkcy9pbmRleC52dWUnDQppbXBvcnQgeyBHZXRCT01JbmZvIH0gZnJvbSAnQC92aWV3cy9QUk8vYm9tLXNldHRpbmcvdXRpbHMnDQoNCmNvbnN0IFNQTElUX1NZTUJPTCA9ICckXyQnDQpleHBvcnQgZGVmYXVsdCB7DQogIGNvbXBvbmVudHM6IHsgRHluYW1pY1RhYmxlRmllbGRzLCBUcmVlRGV0YWlsLCBFeHBhbmRhYmxlU2VjdGlvbiwgQmF0Y2hQcm9jZXNzQWRqdXN0LCBBZGREcmFmdCwgV29ya3Nob3AsIE93bmVyUHJvY2VzcyB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBib21MaXN0OiBbXSwNCiAgICAgIGNvbU5hbWU6ICcnLA0KICAgICAgbGV2ZWxDb2RlOiAnJywNCiAgICAgIGlzQ29tcG9uZW50T3B0aW9uczogWw0KICAgICAgICB7IGxhYmVsOiAn5pivJywgdmFsdWU6IGZhbHNlIH0sDQogICAgICAgIHsgbGFiZWw6ICflkKYnLCB2YWx1ZTogdHJ1ZSB9DQogICAgICBdLA0KICAgICAgc3BlY09wdGlvbnM6IFt7IGRhdGE6ICcnIH1dLA0KICAgICAgZmlsdGVyVHlwZU9wdGlvbjogW3sgZGF0YTogJycgfV0sDQogICAgICBmaWx0ZXJDb2RlT3B0aW9uOiBbeyBkYXRhOiAnJyB9XSwNCiAgICAgIHBpY2tlck9wdGlvbnM6IHsNCiAgICAgICAgZGlzYWJsZWREYXRlKHRpbWUpIHsNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIGlubmVyRm9ybTogew0KICAgICAgICBzZWFyY2hDb250ZW50OiAnJywNCiAgICAgICAgc2VhcmNoQ29tVHlwZVNlYXJjaDogJycsDQogICAgICAgIHNlYXJjaFNwZWNTZWFyY2g6ICcnLA0KICAgICAgICBzZWFyY2hEaXJlY3Q6ICcnDQogICAgICB9LA0KICAgICAgY3VyU2VhcmNoOiAxLA0KICAgICAgc2VhcmNoVHlwZTogJycsDQogICAgICBmb3JtSW5saW5lOiB7DQogICAgICAgIFNjaGR1bGluZ19Db2RlOiAnJywNCiAgICAgICAgQ3JlYXRlX1VzZXJOYW1lOiAnJywNCiAgICAgICAgRmluaXNoX0RhdGU6ICcnLA0KICAgICAgICBJbnN0YWxsVW5pdF9JZDogJycsDQogICAgICAgIFJlbWFyazogJycNCiAgICAgIH0sDQogICAgICB0b3RhbDogMCwNCiAgICAgIGN1cnJlbnRJZHM6ICcnLA0KICAgICAgZ3JpZENvZGU6ICcnLA0KICAgICAgY29sdW1uczogW10sDQogICAgICB0YkRhdGE6IFtdLA0KICAgICAgdGJDb25maWc6IHt9LA0KICAgICAgVG90YWxDb3VudDogMCwNCiAgICAgIG11bHRpcGxlU2VsZWN0aW9uOiBbXSwNCiAgICAgIHNob3dFeHBhbmQ6IHRydWUsDQogICAgICBwZ0xvYWRpbmc6IGZhbHNlLA0KICAgICAgZGVsZXRlTG9hZGluZzogZmFsc2UsDQogICAgICB3b3JrU2hvcElzT3BlbjogZmFsc2UsDQogICAgICBpc093bmVyTnVsbDogZmFsc2UsDQogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIG9wZW5BZGREcmFmdDogZmFsc2UsDQogICAgICBzYXZlTG9hZGluZzogZmFsc2UsDQogICAgICB0YkxvYWRpbmc6IGZhbHNlLA0KICAgICAgaXNDaGVja0FsbDogZmFsc2UsDQogICAgICBjdXJyZW50Q29tcG9uZW50OiAnJywNCiAgICAgIGRXaWR0aDogJzI1JScsDQogICAgICB0aXRsZTogJycsDQogICAgICB0YktleTogMTAwLA0KICAgICAgc2VhcmNoOiAoKSA9PiAoe30pLA0KICAgICAgcGFnZVR5cGU6IHVuZGVmaW5lZCwNCiAgICAgIHRpcExhYmVsOiAnJywNCiAgICAgIHRlY2hub2xvZ3lPcHRpb246IFtdLA0KICAgICAgdHlwZU9wdGlvbjogW10sDQogICAgICB3b3JraW5nVGVhbTogW10sDQogICAgICBwYWdlU3RhdHVzOiB1bmRlZmluZWQsDQogICAgICBzY2hlZHVsZUlkOiAnJywNCiAgICAgIHBhcnRDb21Pd25lckNvbHVtbjogbnVsbCwNCg0KICAgICAgaW5zdGFsbFVuaXRJZExpc3Q6IFtdLA0KICAgICAgcHJvamVjdElkOiAnJywNCiAgICAgIGFyZWFJZDogJycsDQogICAgICBwcm9qZWN0TmFtZTogJycsDQogICAgICBzdGF0dXNUeXBlOiAn5Y+v5o6S5LqnJywNCiAgICAgIGV4cGFuZGVkS2V5OiAnJywNCiAgICAgIHRyZWVMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHRyZWVEYXRhOiBbXSwNCiAgICAgIHRyZWVQYXJhbXNDb21wb25lbnRUeXBlOiB7DQogICAgICAgICdkZWZhdWx0LWV4cGFuZC1hbGwnOiB0cnVlLA0KICAgICAgICAnY2hlY2stc3RyaWN0bHknOiB0cnVlLA0KICAgICAgICBmaWx0ZXJhYmxlOiB0cnVlLA0KICAgICAgICBjbGlja1BhcmVudDogdHJ1ZSwNCiAgICAgICAgZGF0YTogW10sDQogICAgICAgIHByb3BzOiB7DQogICAgICAgICAgY2hpbGRyZW46ICdDaGlsZHJlbicsDQogICAgICAgICAgbGFiZWw6ICdMYWJlbCcsDQogICAgICAgICAgdmFsdWU6ICdEYXRhJw0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgdHJlZVNlbGVjdFBhcmFtczogew0KICAgICAgICBwbGFjZWhvbGRlcjogJ+ivt+mAieaLqScsDQogICAgICAgIGNvbGxhcHNlVGFnczogdHJ1ZSwNCiAgICAgICAgY2xlYXJhYmxlOiB0cnVlDQogICAgICB9LA0KICAgICAgZGlzYWJsZWRBZGQ6IHRydWUNCiAgICB9DQogIH0sDQogIHdhdGNoOiB7DQogICAgJ3RiRGF0YS5sZW5ndGgnOiB7DQogICAgICBoYW5kbGVyKG4sIG8pIHsNCiAgICAgICAgdGhpcy5jaGVja093bmVyKCkNCiAgICAgIH0sDQogICAgICBpbW1lZGlhdGU6IGZhbHNlDQogICAgfQ0KDQogIH0sDQoNCiAgY29tcHV0ZWQ6IHsNCiAgICBpc0NvbSgpIHsNCiAgICAgIHJldHVybiB0aGlzLnBhZ2VUeXBlID09PSAnY29tJw0KICAgIH0sDQogICAgaXNWaWV3KCkgew0KICAgICAgcmV0dXJuIHRoaXMucGFnZVN0YXR1cyA9PT0gJ3ZpZXcnDQogICAgfSwNCiAgICBpc0VkaXQoKSB7DQogICAgICByZXR1cm4gdGhpcy5wYWdlU3RhdHVzID09PSAnZWRpdCcNCiAgICB9LA0KICAgIGlzQWRkKCkgew0KICAgICAgcmV0dXJuIHRoaXMucGFnZVN0YXR1cyA9PT0gJ2FkZCcNCiAgICB9LA0KICAgIGFkZERyYWZ0S2V5KCkgew0KICAgICAgcmV0dXJuIHRoaXMuZXhwYW5kZWRLZXkgKyB0aGlzLmZvcm1JbmxpbmUuSW5zdGFsbFVuaXRfSWQNCiAgICB9LA0KICAgIGZpbHRlclRleHQoKSB7DQogICAgICByZXR1cm4gdGhpcy5wcm9qZWN0TmFtZSArIFNQTElUX1NZTUJPTCArIHRoaXMuc3RhdHVzVHlwZQ0KICAgIH0sDQogICAgc3RhdHVzQ29kZSgpIHsNCiAgICAgIHJldHVybiB0aGlzLmlzQ29tID8gJ0NvbXBfU2NoZHVsZV9TdGF0dXMnIDogJ1BhcnRfU2NoZHVsZV9TdGF0dXMnDQogICAgfSwNCiAgICBpbnN0YWxsTmFtZSgpIHsNCiAgICAgIGNvbnN0IGl0ZW0gPSB0aGlzLmluc3RhbGxVbml0SWRMaXN0LmZpbmQodiA9PiB2LklkID09PSB0aGlzLmZvcm1JbmxpbmUuSW5zdGFsbFVuaXRfSWQpDQogICAgICBpZiAoaXRlbSkgew0KICAgICAgICByZXR1cm4gaXRlbS5OYW1lDQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gJycNCiAgICAgIH0NCiAgICB9LA0KICAgIGlzUGFydFByZXBhcmUoKSB7DQogICAgICByZXR1cm4gdGhpcy5nZXRJc1BhcnRQcmVwYXJlICYmICF0aGlzLmlzQ29tDQogICAgfSwNCiAgICBpc05lc3QoKSB7DQogICAgICByZXR1cm4gdGhpcy4kcm91dGUucXVlcnkudHlwZSA9PT0gJzEnDQogICAgfSwNCiAgICAuLi5tYXBHZXR0ZXJzKCdmYWN0b3J5SW5mbycsIFsnd29ya3Nob3BFbmFibGVkJywgJ2dldElzUGFydFByZXBhcmUnXSksDQogICAgLi4ubWFwR2V0dGVycygnc2NoZWR1bGUnLCBbJ3Byb2Nlc3NMaXN0JywgJ25lc3RJZHMnXSksDQogICAgLi4ubWFwR2V0dGVycygndGVuYW50JywgWydpc1ZlcnNpb25Gb3VyJ10pDQogIH0sDQogIGFzeW5jIG1vdW50ZWQoKSB7DQogICAgY29uc3QgeyBsaXN0LCBjb21OYW1lLCBjdXJyZW50Qk9NSW5mbyB9ID0gYXdhaXQgR2V0Qk9NSW5mbygtMSkNCiAgICB0aGlzLmJvbUxpc3QgPSBsaXN0IHx8IFtdDQogICAgdGhpcy5jb21OYW1lID0gY29tTmFtZQ0KICAgIHRoaXMubGV2ZWxDb2RlID0gY3VycmVudEJPTUluZm8/LkNvZGUNCiAgICBjb25zb2xlLmxvZygnY3VycmVudEJPTUluZm8nLCBjdXJyZW50Qk9NSW5mbykNCiAgICBjb25zb2xlLmxvZygnbGV2ZWxDb2RlJywgdGhpcy5sZXZlbENvZGUpDQogICAgdGhpcy5pbml0UHJvY2Vzc0xpc3QoKQ0KICAgIHRoaXMudGJEYXRhTWFwID0ge30NCiAgICB0aGlzLmNyYWZ0Q29kZU1hcCA9IHt9DQogICAgdGhpcy5wYWdlVHlwZSA9IHRoaXMuJHJvdXRlLnF1ZXJ5LnBnX3R5cGUNCiAgICB0aGlzLnBhZ2VTdGF0dXMgPSB0aGlzLiRyb3V0ZS5xdWVyeS5zdGF0dXMNCiAgICB0aGlzLm1vZGVsID0gdGhpcy4kcm91dGUucXVlcnkubW9kZWwNCiAgICB0aGlzLnNjaGVkdWxlSWQgPSB0aGlzLiRyb3V0ZS5xdWVyeS5waWQgfHwgJycNCiAgICAvLyAvLyB0aGlzLmZvcm1JbmxpbmUuQ3JlYXRlX1VzZXJOYW1lID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5uYW1lDQogICAgLy8gLy8g5qGG5p626Zeu6aKY5byV6LW3c3RvcmXmlbDmja7kuKLlpLHvvIzlt7Llj43ppojvvIznu5PmnpzvvJrmraTlpITlhYjkvb/nlKhsb2NhbFN0b3JhZ2UNCiAgICB0aGlzLmZvcm1JbmxpbmUuQ3JlYXRlX1VzZXJOYW1lID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ1VzZXJBY2NvdW50JykNCiAgICAvLyBpZiAoIXRoaXMuaXNDb20pIHsNCiAgICAvLyAgIHRoaXMuZ2V0UGFydFR5cGUoKQ0KICAgIC8vIH0gZWxzZSB7DQogICAgLy8gfQ0KDQogICAgdGhpcy51bmlxdWUgPSB1bmlxdWVDb2RlKCkNCiAgICB0aGlzLmNoZWNrV29ya3Nob3BJc09wZW4oKQ0KDQogICAgdGhpcy5zZWFyY2ggPSBkZWJvdW5jZSh0aGlzLmZldGNoRGF0YSwgODAwLCB0cnVlKQ0KICAgIGF3YWl0IHRoaXMubWVyZ2VDb25maWcoKQ0KICAgIGlmICh0aGlzLmlzVmlldyB8fCB0aGlzLmlzRWRpdCkgew0KICAgICAgY29uc3QgeyBhcmVhSWQsIGluc3RhbGwgfSA9IHRoaXMuJHJvdXRlLnF1ZXJ5DQogICAgICB0aGlzLmFyZWFJZCA9IGFyZWFJZA0KICAgICAgdGhpcy5mb3JtSW5saW5lLkluc3RhbGxVbml0X0lkID0gaW5zdGFsbA0KICAgICAgdGhpcy5nZXRJbnN0YWxsVW5pdElkTmFtZUxpc3QoKQ0KICAgICAgdGhpcy5mZXRjaERhdGEoKQ0KICAgIH0NCg0KICAgIGlmICh0aGlzLmlzQWRkKSB7DQogICAgICB0aGlzLmZldGNoVHJlZURhdGEoKQ0KICAgICAgdGhpcy5nZXRUeXBlKCkNCiAgICB9DQogICAgaWYgKHRoaXMuaXNFZGl0KSB7DQogICAgICB0aGlzLmdldFR5cGUoKQ0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC4uLm1hcEFjdGlvbnMoJ3NjaGVkdWxlJywgWydjaGFuZ2VQcm9jZXNzTGlzdCcsICdpbml0UHJvY2Vzc0xpc3QnXSksDQogICAgY2hlY2tPd25lcigpIHsNCiAgICAgIGlmICh0aGlzLmlzQ29tKSByZXR1cm4NCiAgICAgIHRoaXMuaXNPd25lck51bGwgPSB0aGlzLnRiRGF0YS5ldmVyeSh2ID0+ICF2LkNvbXBfSW1wb3J0X0RldGFpbF9JZCkgJiYgIXRoaXMuaXNOZXN0DQogICAgICBjb25zdCBpZHggPSB0aGlzLmNvbHVtbnMuZmluZEluZGV4KHYgPT4gdi5Db2RlID09PSAnUGFydF9Vc2VkX1Byb2Nlc3MnKQ0KICAgICAgaWYgKHRoaXMuaXNPd25lck51bGwpIHsNCiAgICAgICAgaWR4ICE9PSAtMSAmJiB0aGlzLmNvbHVtbnMuc3BsaWNlKGlkeCwgMSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGlmIChpZHggPT09IC0xKSB7DQogICAgICAgICAgaWYgKCF0aGlzLm93bmVyQ29sdW1uKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIl+ihqOmFjee9ruWtl+autee8uuWwkembtuS7tumihueUqOW3peW6j+Wtl+autScsDQogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHJldHVybg0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLmNvbHVtbnMucHVzaCh0aGlzLm93bmVyQ29sdW1uKQ0KICAgICAgICB9DQogICAgICAgIHRoaXMuY29tUGFydCA9IHRydWUNCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIG1lcmdlQ29uZmlnKCkgew0KICAgICAgYXdhaXQgdGhpcy5nZXRDb25maWcoKQ0KICAgICAgYXdhaXQgdGhpcy5nZXRXb3JrVGVhbSgpDQogICAgfSwNCiAgICBhc3luYyBnZXRDb25maWcoKSB7DQogICAgICBsZXQgY29uZmlnQ29kZSA9ICcnDQogICAgICBpZiAodGhpcy5pc05lc3QpIHsNCiAgICAgICAgaWYgKHRoaXMuaXNWaWV3KSB7DQogICAgICAgICAgY29uZmlnQ29kZSA9ICdQUk9OZXN0aW5nU2NoZWR1bGVEZXRhaWwnDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uZmlnQ29kZSA9ICdQUk9OZXN0aW5nU2NoZWR1bGVDb25maWcnDQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNvbmZpZ0NvZGUgPSAodGhpcy5pc1ZpZXcgPyAnUFJPQ29tVmlld1BhZ2VUYkNvbmZpZycgOiAnUFJPQ29tRHJhZnRQYWdlVGJDb25maWcnKQ0KICAgICAgfQ0KICAgICAgdGhpcy5ncmlkQ29kZSA9IGNvbmZpZ0NvZGUNCiAgICAgIGF3YWl0IHRoaXMuZ2V0VGFibGVDb25maWcoY29uZmlnQ29kZSkNCiAgICAgIGlmICghdGhpcy53b3Jrc2hvcEVuYWJsZWQpIHsNCiAgICAgICAgdGhpcy5jb2x1bW5zID0gdGhpcy5jb2x1bW5zLmZpbHRlcih2ID0+IHYuQ29kZSAhPT0gJ1dvcmtzaG9wX05hbWUnKQ0KICAgICAgfQ0KICAgICAgaWYgKCF0aGlzLmlzVmVyc2lvbkZvdXIpIHsNCiAgICAgICAgdGhpcy5jb2x1bW5zID0gdGhpcy5jb2x1bW5zLmZpbHRlcih2ID0+IHYuQ29kZSAhPT0gJ1RlY2hub2xvZ3lfQ29kZScpDQogICAgICB9DQogICAgICB0aGlzLmNoZWNrT3duZXIoKQ0KICAgIH0sDQogICAgYXN5bmMgY2hhbmdlQ29sdW1uKCkgew0KICAgICAgYXdhaXQgdGhpcy5nZXRUYWJsZUNvbmZpZyh0aGlzLmdyaWRDb2RlKQ0KICAgICAgdGhpcy50YktleSsrDQogICAgfSwNCiAgICBoYW5kbGVOb2RlQ2xpY2soZGF0YSkgew0KICAgICAgdGhpcy5leHBhbmRlZEtleSA9IGRhdGEuSWQNCiAgICAgIGlmICh0aGlzLmFyZWFJZCA9PT0gZGF0YS5JZCkgew0KICAgICAgICB0aGlzLmRpc2FibGVkQWRkID0gZmFsc2UNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICB0aGlzLmRpc2FibGVkQWRkID0gdHJ1ZQ0KICAgICAgaWYgKCFkYXRhLlBhcmVudE5vZGVzIHx8IGRhdGEuQ2hpbGRyZW4/Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICBpZiAoZGF0YT8uRGF0YVt0aGlzLnN0YXR1c0NvZGVdID09PSAn5pyq5a+85YWlJykgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAn5riF5Y2V5pyq5a+85YWl77yM6K+36IGU57O75rex5YyW5Lq65ZGY5a+85YWl5riF5Y2VJywNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgfSkNCg0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgY29uc3QgaW5pdERhdGEgPSAoeyBEYXRhIH0pID0+IHsNCiAgICAgICAgdGhpcy5hcmVhSWQgPSBEYXRhLklkDQogICAgICAgIHRoaXMucHJvamVjdElkID0gRGF0YS5Qcm9qZWN0X0lkDQogICAgICAgIHRoaXMuZXhwYW5kZWRLZXkgPSB0aGlzLmFyZWFJZA0KICAgICAgICB0aGlzLmZvcm1JbmxpbmUuRmluaXNoX0RhdGUgPSAnJw0KICAgICAgICB0aGlzLmZvcm1JbmxpbmUuSW5zdGFsbFVuaXRfSWQgPSAnJw0KICAgICAgICB0aGlzLmZvcm1JbmxpbmUuUmVtYXJrID0gJycNCiAgICAgICAgdGhpcy50YkRhdGEgPSBbXQ0KICAgICAgICB0aGlzLmdldEFyZWFJbmZvKCkNCiAgICAgICAgdGhpcy5nZXRJbnN0YWxsVW5pdElkTmFtZUxpc3QoKQ0KICAgICAgfQ0KDQogICAgICBpZiAodGhpcy50YkRhdGEubGVuZ3RoKSB7DQogICAgICAgIHRoaXMuJGNvbmZpcm0oJ+WIh+aNouWMuuWfn+WPs+S+p+aVsOaNrua4heepuu+8jOaYr+WQpuehruiupD8nLCAn5o+Q56S6Jywgew0KICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgICAgaW5pdERhdGEoZGF0YSkNCiAgICAgICAgICB0aGlzLmRpc2FibGVkQWRkID0gZmFsc2UNCiAgICAgICAgICB0aGlzLnRiRGF0YU1hcCA9IHt9DQogICAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIHR5cGU6ICdpbmZvJywNCiAgICAgICAgICAgIG1lc3NhZ2U6ICflt7Llj5bmtognDQogICAgICAgICAgfSkNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuZGlzYWJsZWRBZGQgPSBmYWxzZQ0KICAgICAgICBpbml0RGF0YShkYXRhKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBjdXN0b21GaWx0ZXJGdW4odmFsdWUsIGRhdGEsIG5vZGUpIHsNCiAgICAgIC8vIGNvbnNvbGUubG9nKCdjdXN0b21GaWx0ZXJGdW4nLCB2YWx1ZSwgZGF0YSwgbm9kZSkNCg0KICAgICAgY29uc3QgYXJyID0gdmFsdWUuc3BsaXQoU1BMSVRfU1lNQk9MKQ0KICAgICAgY29uc3QgbGFiZWxWYWwgPSBhcnJbMF0NCiAgICAgIGNvbnN0IHN0YXR1c1ZhbCA9IGFyclsxXQ0KICAgICAgaWYgKCF2YWx1ZSkgcmV0dXJuIHRydWUNCiAgICAgIGxldCBwYXJlbnROb2RlID0gbm9kZS5wYXJlbnQNCiAgICAgIGxldCBsYWJlbHMgPSBbbm9kZS5sYWJlbF0NCiAgICAgIGxldCBzdGF0dXMgPSBbZGF0YS5EYXRhW3RoaXMuc3RhdHVzQ29kZV1dDQogICAgICBsZXQgbGV2ZWwgPSAxDQogICAgICB3aGlsZSAobGV2ZWwgPCBub2RlLmxldmVsKSB7DQogICAgICAgIGxhYmVscyA9IFsuLi5sYWJlbHMsIHBhcmVudE5vZGUubGFiZWxdDQogICAgICAgIHN0YXR1cyA9IFsuLi5zdGF0dXMsIGRhdGEuRGF0YVt0aGlzLnN0YXR1c0NvZGVdXQ0KICAgICAgICBwYXJlbnROb2RlID0gcGFyZW50Tm9kZS5wYXJlbnQNCiAgICAgICAgbGV2ZWwrKw0KICAgICAgfQ0KICAgICAgbGFiZWxzID0gbGFiZWxzLmZpbHRlcih2ID0+ICEhdikNCiAgICAgIHN0YXR1cyA9IHN0YXR1cy5maWx0ZXIodiA9PiAhIXYpDQogICAgICBsZXQgcmVzdWx0TGFiZWwgPSB0cnVlDQogICAgICBsZXQgcmVzdWx0U3RhdHVzID0gdHJ1ZQ0KICAgICAgaWYgKHRoaXMuc3RhdHVzVHlwZSkgew0KICAgICAgICByZXN1bHRTdGF0dXMgPSBzdGF0dXMuc29tZShzID0+IHMuaW5kZXhPZihzdGF0dXNWYWwpICE9PSAtMSkNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLnByb2plY3ROYW1lKSB7DQogICAgICAgIHJlc3VsdExhYmVsID0gbGFiZWxzLnNvbWUocyA9PiBzLmluZGV4T2YobGFiZWxWYWwpICE9PSAtMSkNCiAgICAgIH0NCiAgICAgIHJldHVybiByZXN1bHRMYWJlbCAmJiByZXN1bHRTdGF0dXMNCiAgICB9LA0KICAgIGFzeW5jIGZldGNoRGF0YSgpIHsNCiAgICAgIHRoaXMudGJMb2FkaW5nID0gdHJ1ZQ0KICAgICAgbGV0IHJlc0RhdGEgPSBudWxsDQogICAgICBpZiAodGhpcy5pc05lc3QpIHsNCiAgICAgICAgaWYgKHRoaXMuaXNWaWV3KSB7DQogICAgICAgICAgcmVzRGF0YSA9IGF3YWl0IHRoaXMuZ2V0UGFydFBhZ2VMaXN0KCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICByZXNEYXRhID0gYXdhaXQgdGhpcy5nZXROZXN0UGFnZUxpc3QoKQ0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXNEYXRhID0gYXdhaXQgdGhpcy5nZXRDb21QYWdlTGlzdCgpDQogICAgICAgIGNvbnNvbGUubG9nKCdyZXNEYXRhJywgcmVzRGF0YSkNCiAgICAgIH0NCg0KICAgICAgdGhpcy5pbml0VGJEYXRhKHJlc0RhdGEpDQogICAgICB0aGlzLnRiTG9hZGluZyA9IGZhbHNlDQogICAgfSwNCiAgICBmZXRjaFRyZWVEYXRhTG9jYWwoKSB7DQogICAgICAvLyB0aGlzLmZpbHRlclRleHQgPSB0aGlzLnByb2plY3ROYW1lDQogICAgfSwNCiAgICBmZXRjaFRyZWVTdGF0dXMoKSB7DQogICAgICAvLyB0aGlzLmZpbHRlclRleHQgPSB0aGlzLnN0YXR1c1R5cGUNCiAgICB9LA0KICAgIGZldGNoVHJlZURhdGEoKSB7DQogICAgICB0aGlzLnRyZWVMb2FkaW5nID0gdHJ1ZQ0KICAgICAgY29uc29sZS5sb2coJzc4LHRoaXMuJHJvdXRlLm1ldGEnLCB0aGlzLiRyb3V0ZSwgdGhpcy4kcm91dGVyKQ0KICAgICAgR2V0UHJvamVjdEFyZWFUcmVlTGlzdCh7IE1lbnVJZDogdGhpcy4kcm91dGUubWV0YS5JZCwgcHJvamVjdE5hbWU6IHRoaXMucHJvamVjdE5hbWUsIFR5cGU6IHRoaXMuaXNDb20gPyAxIDogMiB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKCFyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICAgIHRoaXMudHJlZURhdGEgPSBbXQ0KICAgICAgICAgIHRoaXMudHJlZUxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICAgIGlmIChyZXMuRGF0YS5sZW5ndGggPT09IDApIHsNCiAgICAgICAgICB0aGlzLnRyZWVMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICB0aGlzLnRyZWVEYXRhID0gW10NCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KICAgICAgICBjb25zdCByZXNEYXRhID0gcmVzLkRhdGEubWFwKGl0ZW0gPT4gew0KICAgICAgICAgIGl0ZW0uSXNfRGlyZWN0b3J5ID0gdHJ1ZQ0KICAgICAgICAgIHJldHVybiBpdGVtDQogICAgICAgIH0pDQogICAgICAgIHRoaXMudHJlZURhdGEgPSByZXNEYXRhDQogICAgICAgIHRoaXMuJG5leHRUaWNrKF8gPT4gew0KICAgICAgICAgIHRoaXMuJHJlZnMudHJlZS5maWx0ZXJSZWYodGhpcy5maWx0ZXJUZXh0KQ0KICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IHRoaXMuc2V0S2V5KCkNCiAgICAgICAgICBpZiAoIXJlc3VsdCkgew0KICAgICAgICAgICAgdGhpcy5wZ0xvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgdGhpcy50cmVlTG9hZGluZyA9IGZhbHNlDQogICAgICB9KS5jYXRjaCgoZSkgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygnY2F0Y2hlJywgZSkNCiAgICAgICAgdGhpcy50cmVlTG9hZGluZyA9IGZhbHNlDQogICAgICAgIHRoaXMudHJlZURhdGEgPSBbXQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHNldEtleSgpIHsNCiAgICAgIGNvbnN0IGRlZXBGaWx0ZXIgPSAodHJlZSkgPT4gew0KICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRyZWUubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgICBjb25zdCBpdGVtID0gdHJlZVtpXQ0KICAgICAgICAgIGNvbnN0IHsgRGF0YSwgQ2hpbGRyZW4gfSA9IGl0ZW0NCiAgICAgICAgICBjb25zdCBub2RlID0gZ2V0Tm9kZShEYXRhLklkKQ0KICAgICAgICAgIGlmIChEYXRhLlBhcmVudElkICYmICFDaGlsZHJlbj8ubGVuZ3RoICYmIG5vZGUudmlzaWJsZSkgew0KICAgICAgICAgICAgdGhpcy5oYW5kbGVOb2RlQ2xpY2soaXRlbSkNCiAgICAgICAgICAgIHJldHVybiB0cnVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGlmIChDaGlsZHJlbj8ubGVuZ3RoKSB7DQogICAgICAgICAgICAgIGNvbnN0IHNob3VsZFN0b3AgPSBkZWVwRmlsdGVyKENoaWxkcmVuKQ0KICAgICAgICAgICAgICBpZiAoc2hvdWxkU3RvcCkgcmV0dXJuIHRydWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICB9DQogICAgICBjb25zdCBnZXROb2RlID0gKGtleSkgPT4gew0KICAgICAgICByZXR1cm4gdGhpcy4kcmVmc1sndHJlZSddLmdldE5vZGVCeUtleShrZXkpDQogICAgICB9DQogICAgICByZXR1cm4gZGVlcEZpbHRlcih0aGlzLnRyZWVEYXRhKQ0KICAgIH0sDQogICAgY2xvc2VWaWV3KCkgew0KICAgICAgY2xvc2VUYWdWaWV3KHRoaXMuJHN0b3JlLCB0aGlzLiRyb3V0ZSkNCiAgICB9LA0KICAgIGNoZWNrV29ya3Nob3BJc09wZW4oKSB7DQogICAgICB0aGlzLndvcmtTaG9wSXNPcGVuID0gdHJ1ZQ0KICAgIH0sDQogICAgdGJTZWxlY3RDaGFuZ2UoYXJyYXkpIHsNCiAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24gPSBhcnJheS5yZWNvcmRzDQogICAgfSwNCiAgICBnZXRBcmVhSW5mbygpIHsNCiAgICAgIHRoaXMuZm9ybUlubGluZS5GaW5pc2hfRGF0ZSA9ICcnDQogICAgICBBcmVhR2V0RW50aXR5KHsNCiAgICAgICAgaWQ6IHRoaXMuYXJlYUlkDQogICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgaWYgKCFyZXMuRGF0YSkgew0KICAgICAgICAgICAgcmV0dXJuIFtdDQogICAgICAgICAgfQ0KDQogICAgICAgICAgY29uc3Qgc3RhcnQgPSBtb21lbnQocmVzLkRhdGE/LkRlbWFuZF9CZWdpbl9EYXRlKQ0KICAgICAgICAgIGNvbnN0IGVuZCA9IG1vbWVudChyZXMuRGF0YT8uRGVtYW5kX0VuZF9EYXRlKQ0KICAgICAgICAgIHRoaXMucGlja2VyT3B0aW9ucy5kaXNhYmxlZERhdGUgPSAodGltZSkgPT4gew0KICAgICAgICAgICAgcmV0dXJuIHRpbWUuZ2V0VGltZSgpIDwgc3RhcnQgfHwgdGltZS5nZXRUaW1lKCkgPiBlbmQNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlQ2xvc2UoKSB7DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgICAgdGhpcy5vcGVuQWRkRHJhZnQgPSBmYWxzZQ0KICAgIH0sDQogICAgZ2V0TmVzdFBhZ2VMaXN0KCkgew0KICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHsNCiAgICAgICAgR2V0Q2FuU2NoZHVsaW5nUGFydExpc3Qoew0KICAgICAgICAgIElkczogdGhpcy5uZXN0SWRzDQogICAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgY29uc3QgX2xpc3QgPSByZXM/LkRhdGEgfHwgW10NCiAgICAgICAgICAgIGNvbnN0IGxpc3QgPSBfbGlzdC5tYXAodiA9PiB7DQogICAgICAgICAgICAgIGlmICh2LlNjaGVkdWxlZF9Vc2VkX1Byb2Nlc3MpIHsNCiAgICAgICAgICAgICAgICAvLyDlt7LlrZjlnKjmk43kvZzov4fmlbDmja4NCiAgICAgICAgICAgICAgICB2LlBhcnRfVXNlZF9Qcm9jZXNzID0gdi5TY2hlZHVsZWRfVXNlZF9Qcm9jZXNzDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgLy8gdi5vcmlnaW5hbFBhdGggPSB2LlNjaGVkdWxlZF9UZWNobm9sb2d5X1BhdGggPyB2LlNjaGVkdWxlZF9UZWNobm9sb2d5X1BhdGggOiAnJw0KICAgICAgICAgICAgICB2LldvcmtzaG9wX0lkID0gdi5TY2hlZHVsZWRfV29ya3Nob3BfSWQNCiAgICAgICAgICAgICAgdi5Xb3Jrc2hvcF9OYW1lID0gdi5TY2hlZHVsZWRfV29ya3Nob3BfTmFtZQ0KICAgICAgICAgICAgICB2LlRlY2hub2xvZ3lfUGF0aCA9IHYuU2NoZWR1bGVkX1RlY2hub2xvZ3lfUGF0aCB8fCB2LlRlY2hub2xvZ3lfUGF0aA0KICAgICAgICAgICAgICB2LmNob29zZUNvdW50ID0gdi5DYW5fU2NoZHVsaW5nX0NvdW50DQogICAgICAgICAgICAgIHJldHVybiB2DQogICAgICAgICAgICB9KQ0KDQogICAgICAgICAgICByZXNvbHZlKGxpc3QpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHJlamVjdCgpDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFzeW5jIGdldENvbVBhZ2VMaXN0KCkgew0KICAgICAgY29uc3Qgew0KICAgICAgICBwaWQNCiAgICAgIH0gPSB0aGlzLiRyb3V0ZS5xdWVyeQ0KICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgR2V0Q29tcFNjaGR1bGluZ0luZm9EZXRhaWwoew0KICAgICAgICBTY2hkdWxpbmdfUGxhbl9JZDogcGlkDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICBjb25zdCB7IFNjaGR1bGluZ19QbGFuLCBTY2hkdWxpbmdfQ29tcHMsIFByb2Nlc3NfTGlzdCB9ID0gcmVzLkRhdGENCiAgICAgICAgICB0aGlzLmZvcm1JbmxpbmUgPSBPYmplY3QuYXNzaWduKHRoaXMuZm9ybUlubGluZSwgU2NoZHVsaW5nX1BsYW4pDQogICAgICAgICAgUHJvY2Vzc19MaXN0LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgICBjb25zdCBwbGlzdCA9IHsNCiAgICAgICAgICAgICAga2V5OiBpdGVtLlByb2Nlc3NfQ29kZSwNCiAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHRoaXMuY2hhbmdlUHJvY2Vzc0xpc3QocGxpc3QpDQogICAgICAgICAgfSkNCiAgICAgICAgICBjb25zdCBsaXN0ID0gU2NoZHVsaW5nX0NvbXBzLm1hcCh2ID0+IHsNCiAgICAgICAgICAgIHYuY2hvb3NlQ291bnQgPSB2LkNhbl9TY2hkdWxpbmdfQ291bnQNCiAgICAgICAgICAgIHJldHVybiB2DQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLmdldFN0b3BMaXN0KGxpc3QpDQogICAgICAgICAgcmV0dXJuIGxpc3QgfHwgW10NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICByZXR1cm4gcmVzdWx0IHx8IFtdDQogICAgfSwNCiAgICBhc3luYyBnZXRTdG9wTGlzdChsaXN0KSB7DQogICAgICBjb25zb2xlLmxvZygnZ2V0U3RvcExpc3QnLCBsaXN0KQ0KICAgICAgY29uc3Qgc3VibWl0T2JqID0gbGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgSWQ6IGl0ZW0uQ29tcF9JbXBvcnRfRGV0YWlsX0lkLA0KICAgICAgICAgIFR5cGU6IDINCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIGF3YWl0IEdldFN0b3BMaXN0KHN1Ym1pdE9iaikudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIGNvbnN0IHN0b3BNYXAgPSB7fQ0KICAgICAgICAgIHJlcy5EYXRhLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgICBzdG9wTWFwW2l0ZW0uSWRdID0gISFpdGVtLklzX1N0b3ANCiAgICAgICAgICB9KQ0KICAgICAgICAgIGxpc3QuZm9yRWFjaChyb3cgPT4gew0KICAgICAgICAgICAgaWYgKHN0b3BNYXBbcm93LkNvbXBfSW1wb3J0X0RldGFpbF9JZF0pIHsNCiAgICAgICAgICAgICAgdGhpcy4kc2V0KHJvdywgJ3N0b3BGbGFnJywgc3RvcE1hcFtyb3cuQ29tcF9JbXBvcnRfRGV0YWlsX0lkXSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgZ2V0UGFydFBhZ2VMaXN0KCkgew0KICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHsNCiAgICAgICAgY29uc3Qgew0KICAgICAgICAgIHBpZA0KICAgICAgICB9ID0gdGhpcy4kcm91dGUucXVlcnkNCiAgICAgICAgR2V0UGFydFNjaGR1bGluZ0luZm9EZXRhaWwoew0KICAgICAgICAgIFNjaGR1bGluZ19QbGFuX0lkOiBwaWQNCiAgICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIGNvbnN0IFNhcmVQYXJ0c01vZGVsID0gcmVzLkRhdGE/LlNhcmVQYXJ0c01vZGVsLm1hcCh2ID0+IHsNCiAgICAgICAgICAgICAgaWYgKHYuU2NoZWR1bGVkX1VzZWRfUHJvY2Vzcykgew0KICAgICAgICAgICAgICAgIC8vIOW3suWtmOWcqOaTjeS9nOi/h+aVsOaNrg0KICAgICAgICAgICAgICAgIHYuUGFydF9Vc2VkX1Byb2Nlc3MgPSB2LlNjaGVkdWxlZF9Vc2VkX1Byb2Nlc3MNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB2LmNob29zZUNvdW50ID0gdi5DYW5fU2NoZHVsaW5nX0NvdW50DQogICAgICAgICAgICAgIHJldHVybiB2DQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgdGhpcy5mb3JtSW5saW5lID0gT2JqZWN0LmFzc2lnbih0aGlzLmZvcm1JbmxpbmUsIHJlcy5EYXRhPy5TY2hkdWxpbmdfUGxhbikNCiAgICAgICAgICAgIHJlc29sdmUoU2FyZVBhcnRzTW9kZWwgfHwgW10pDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHJlamVjdCgpDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGluaXRUYkRhdGEobGlzdCwgdGVhbUtleSA9ICdBbGxvY2F0aW9uX1RlYW1zJykgew0KICAgICAgdGhpcy50YkRhdGEgPSBsaXN0Lm1hcChyb3cgPT4gew0KICAgICAgICBjb25zdCBwcm9jZXNzTGlzdCA9IHJvdy5UZWNobm9sb2d5X1BhdGg/LnNwbGl0KCcvJykgfHwgW10NCiAgICAgICAgcm93LnV1aWQgPSB1dWlkdjQoKQ0KICAgICAgICB0aGlzLmFkZEVsZW1lbnRUb1RiRGF0YShyb3cpDQogICAgICAgIGlmIChyb3dbdGVhbUtleV0pIHsNCiAgICAgICAgICBjb25zdCBuZXdEYXRhID0gcm93W3RlYW1LZXldLmZpbHRlcigocikgPT4gcHJvY2Vzc0xpc3QuZmluZEluZGV4KChwKSA9PiByLlByb2Nlc3NfQ29kZSA9PT0gcCkgIT09IC0xKQ0KICAgICAgICAgIG5ld0RhdGEuZm9yRWFjaCgoZWxlLCBpbmRleCkgPT4gew0KICAgICAgICAgICAgY29uc3QgY29kZSA9IHRoaXMuZ2V0Um93VW5pcXVlKHJvdy51dWlkLCBlbGUuUHJvY2Vzc19Db2RlLCBlbGUuV29ya2luZ19UZWFtX0lkKQ0KICAgICAgICAgICAgY29uc3QgbWF4ID0gdGhpcy5nZXRSb3dVbmlxdWVNYXgocm93LnV1aWQsIGVsZS5Qcm9jZXNzX0NvZGUsIGVsZS5Xb3JraW5nX1RlYW1fSWQpDQogICAgICAgICAgICByb3dbY29kZV0gPSBlbGUuQ291bnQNCiAgICAgICAgICAgIHJvd1ttYXhdID0gMA0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgICAgdGhpcy5zZXRJbnB1dE1heChyb3cpDQogICAgICAgIHJldHVybiByb3cNCiAgICAgIH0pDQogICAgICBsZXQgaWRzID0gJycNCiAgICAgIGlmICh0aGlzLmlzQ29tKSB7DQogICAgICAgIGlkcyA9IHRoaXMudGJEYXRhLm1hcCh2ID0+IHYuQ29tcF9JbXBvcnRfRGV0YWlsX0lkKS50b1N0cmluZygpDQogICAgICB9IGVsc2Ugew0KICAgICAgICBpZHMgPSB0aGlzLnRiRGF0YS5tYXAodiA9PiB2LlBhcnRfQWdncmVnYXRlX0lkKS50b1N0cmluZygpDQogICAgICB9DQogICAgICB0aGlzLmN1cnJlbnRJZHMgPSBpZHMNCiAgICB9LA0KICAgIGFzeW5jIG1lcmdlU2VsZWN0TGlzdChuZXdMaXN0KSB7DQogICAgICBpZiAodGhpcy5pc1ZlcnNpb25Gb3VyKSB7DQogICAgICAgIGF3YWl0IHRoaXMubWVyZ2VDcmFmdFByb2Nlc3MobmV3TGlzdCkNCiAgICAgIH0NCiAgICAgIGNvbnNvbGUudGltZSgnbWVyZ2VTZWxlY3RMaXN0VGltZScpDQogICAgICBsZXQgaGFzVXNlZFBhcnRGbGFnID0gdHJ1ZQ0KICAgICAgbmV3TGlzdC5mb3JFYWNoKChlbGVtZW50LCBpbmRleCkgPT4gew0KICAgICAgICBjb25zdCBjdXIgPSB0aGlzLmdldE1lcmdlVW5pcXVlUm93KGVsZW1lbnQpDQogICAgICAgIGlmICghY3VyKSB7DQogICAgICAgICAgZWxlbWVudC5wdXVpZCA9IGVsZW1lbnQudXVpZA0KICAgICAgICAgIGVsZW1lbnQuU2NoZHVsZWRfQ291bnQgPSBlbGVtZW50LmNob29zZUNvdW50DQogICAgICAgICAgZWxlbWVudC5TY2hkdWxlZF9XZWlnaHQgPSBudW1lcmFsKGVsZW1lbnQuY2hvb3NlQ291bnQgKiBlbGVtZW50LldlaWdodCkuZm9ybWF0KCcwLlswMF0nKQ0KICAgICAgICAgIGlmICh0aGlzLmlzVmVyc2lvbkZvdXIgJiYgIWVsZW1lbnQuVGVjaG5vbG9neV9QYXRoKSB7DQogICAgICAgICAgICAvKiAgICAgICAgICBpZiAodGhpcy5jcmFmdENvZGVNYXBbZWxlbWVudC5UZWNobm9sb2d5X0NvZGVdICYmIHRoaXMuY3JhZnRDb2RlTWFwW2VsZW1lbnQuVGVjaG5vbG9neV9Db2RlXSBpbnN0YW5jZW9mIEFycmF5KSB7DQogICAgICAgICAgICAgIGNvbnN0IGN1clBhdGhBcnIgPSB0aGlzLmNyYWZ0Q29kZU1hcFtlbGVtZW50LlRlY2hub2xvZ3lfQ29kZV0NCiAgICAgICAgICAgICAgaWYgKGVsZW1lbnQuUGFydF9Vc2VkX1Byb2Nlc3MgJiYgIWN1clBhdGhBcnIuaW5jbHVkZXMoZWxlbWVudC5QYXJ0X1VzZWRfUHJvY2VzcykpIHsNCiAgICAgICAgICAgICAgICBoYXNVc2VkUGFydEZsYWcgPSBmYWxzZQ0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIGVsZW1lbnQuVGVjaG5vbG9neV9QYXRoID0gY3VyUGF0aEFyci5qb2luKCcvJykNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSovDQogICAgICAgICAgICBpZiAodGhpcy5jcmFmdENvZGVNYXBbZWxlbWVudC5UZWNobm9sb2d5X0NvZGVdIGluc3RhbmNlb2YgQXJyYXkpIHsNCiAgICAgICAgICAgICAgY29uc3QgY3VyUGF0aEFyciA9IHRoaXMuY3JhZnRDb2RlTWFwW2VsZW1lbnQuVGVjaG5vbG9neV9Db2RlXQ0KICAgICAgICAgICAgICBpZiAoZWxlbWVudC5QYXJ0X1VzZWRfUHJvY2Vzcykgew0KICAgICAgICAgICAgICAgIGNvbnN0IHBhcnRVc2VkUHJvY2Vzc0FyciA9IGVsZW1lbnQuUGFydF9Vc2VkX1Byb2Nlc3Muc3BsaXQoJywnKQ0KICAgICAgICAgICAgICAgIGNvbnN0IGFsbFBhcnRzSW5jbHVkZWQgPSBwYXJ0VXNlZFByb2Nlc3NBcnIuZXZlcnkocGFydCA9PiBjdXJQYXRoQXJyLmluY2x1ZGVzKHBhcnQpKQ0KDQogICAgICAgICAgICAgICAgaWYgKCFhbGxQYXJ0c0luY2x1ZGVkKSB7DQogICAgICAgICAgICAgICAgICBoYXNVc2VkUGFydEZsYWcgPSBmYWxzZQ0KICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICBlbGVtZW50LlRlY2hub2xvZ3lfUGF0aCA9IGN1clBhdGhBcnIuam9pbignLycpDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIGVsZW1lbnQuVGVjaG5vbG9neV9QYXRoID0gY3VyUGF0aEFyci5qb2luKCcvJykNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLnRiRGF0YS5wdXNoKGVsZW1lbnQpDQogICAgICAgICAgdGhpcy5hZGRFbGVtZW50VG9UYkRhdGEoZWxlbWVudCkNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KDQogICAgICAgIGN1ci5wdXVpZCA9IGVsZW1lbnQudXVpZA0KDQogICAgICAgIGN1ci5TY2hkdWxlZF9Db3VudCArPSBlbGVtZW50LmNob29zZUNvdW50DQogICAgICAgIGN1ci5TY2hkdWxlZF9XZWlnaHQgPSBudW1lcmFsKGN1ci5TY2hkdWxlZF9XZWlnaHQpLmFkZChlbGVtZW50LmNob29zZUNvdW50ICogZWxlbWVudC5XZWlnaHQpLmZvcm1hdCgnMC5bMDBdJykNCiAgICAgICAgaWYgKCFjdXIuVGVjaG5vbG9neV9QYXRoKSB7DQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCiAgICAgICAgdGhpcy5zZXRJbnB1dE1heChjdXIpDQogICAgICB9KQ0KICAgICAgdGhpcy5zaG93Q3JhZnRVc2VkUGFydFJlc3VsdChoYXNVc2VkUGFydEZsYWcpDQoNCiAgICAgIC8vIGlmICh0aGlzLmlzQ29tKSB7DQogICAgICAvLyAgIHRoaXMudGJEYXRhLnNvcnQoKGEsIGIpID0+IGEuaW5pdFJvd0luZGV4IC0gYi5pbml0Um93SW5kZXgpDQogICAgICAvLyB9IGVsc2Ugew0KICAgICAgLy8gICB0aGlzLnRiRGF0YS5zb3J0KChhLCBiKSA9PiBhLlBhcnRfQ29kZS5sb2NhbGVDb21wYXJlKGIuUGFydF9Db2RlKSkNCiAgICAgIC8vIH0NCiAgICAgIHRoaXMudGJEYXRhLnNvcnQoKGEsIGIpID0+IGEuaW5pdFJvd0luZGV4IC0gYi5pbml0Um93SW5kZXgpDQogICAgICBjb25zb2xlLnRpbWVFbmQoJ21lcmdlU2VsZWN0TGlzdFRpbWUnKQ0KICAgIH0sDQogICAgYWRkRWxlbWVudFRvVGJEYXRhKGVsZW1lbnQpIHsNCiAgICAgIGNvbnN0IGtleSA9IHRoaXMuZ2V0VW5pS2V5KGVsZW1lbnQpDQogICAgICB0aGlzLnRiRGF0YU1hcFtrZXldID0gZWxlbWVudA0KICAgIH0sDQogICAgZ2V0TWVyZ2VVbmlxdWVSb3coZWxlbWVudCkgew0KICAgICAgY29uc3Qga2V5ID0gdGhpcy5nZXRVbmlLZXkoZWxlbWVudCkNCiAgICAgIHJldHVybiB0aGlzLnRiRGF0YU1hcFtrZXldDQogICAgfSwNCiAgICBnZXRVbmlLZXkoZWxlbWVudCkgew0KICAgICAgaWYgKHRoaXMuaXNWZXJzaW9uRm91cikgew0KICAgICAgICByZXR1cm4gdGhpcy5pc0NvbSA/IChlbGVtZW50LkNvbXBfQ29kZSArIGVsZW1lbnQuSW5zdGFsbFVuaXRfTmFtZSkudG9TdHJpbmcoKS50cmltKCkgOiAoKGVsZW1lbnQuQ29tcG9uZW50X0NvZGUgPz8gJycpICsgZWxlbWVudC5QYXJ0X0NvZGUgKyBlbGVtZW50LlBhcnRfQWdncmVnYXRlX0lkKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuaXNDb20gPyBlbGVtZW50LkNvbXBfQ29kZSA6ICgoZWxlbWVudC5Db21wb25lbnRfQ29kZSA/PyAnJykgKyBlbGVtZW50LlBhcnRfQ29kZSArIGVsZW1lbnQuUGFydF9BZ2dyZWdhdGVfSWQpDQogICAgICB9DQogICAgfSwNCiAgICBjaGVja0Zvcm0oKSB7DQogICAgICBsZXQgaXNWYWxpZGF0ZSA9IHRydWUNCiAgICAgIHRoaXMuJHJlZnNbJ2Zvcm1JbmxpbmUnXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKCF2YWxpZCkgaXNWYWxpZGF0ZSA9IGZhbHNlDQogICAgICB9KQ0KICAgICAgcmV0dXJuIGlzVmFsaWRhdGUNCiAgICB9LA0KICAgIGFzeW5jIHNhdmVEcmFmdChpc09yZGVyID0gZmFsc2UpIHsNCiAgICAgIGNvbnN0IGNoZWNrU3VjY2VzcyA9IHRoaXMuY2hlY2tGb3JtKCkNCiAgICAgIGlmICghY2hlY2tTdWNjZXNzKSByZXR1cm4gZmFsc2UNCiAgICAgIGNvbnN0IHsgdGFibGVEYXRhLCBzdGF0dXMgfSA9IHRoaXMuZ2V0U3VibWl0VGJJbmZvKCkNCiAgICAgIGlmICghc3RhdHVzKSByZXR1cm4gZmFsc2UNCiAgICAgIGlmICghaXNPcmRlcikgew0KICAgICAgICB0aGlzLnNhdmVMb2FkaW5nID0gdHJ1ZQ0KICAgICAgfQ0KDQogICAgICBjb25zdCBpc1N1Y2Nlc3MgPSBhd2FpdCB0aGlzLmhhbmRsZVNhdmVEcmFmdCh0YWJsZURhdGEsIGlzT3JkZXIpDQogICAgICBjb25zb2xlLmxvZygnaXNTdWNjZXNzJywgaXNTdWNjZXNzKQ0KICAgICAgaWYgKCFpc1N1Y2Nlc3MpIHJldHVybiBmYWxzZQ0KICAgICAgaWYgKGlzT3JkZXIpIHJldHVybiBpc1N1Y2Nlc3MNCiAgICAgIHRoaXMuJHJlZnNbJ2RyYWZ0J10/LmZldGNoRGF0YSgpDQogICAgICB0aGlzLnNhdmVMb2FkaW5nID0gZmFsc2UNCiAgICB9LA0KICAgIGFzeW5jIHNhdmVXb3JrU2hvcCgpIHsNCiAgICAgIGNvbnN0IGNoZWNrU3VjY2VzcyA9IHRoaXMuY2hlY2tGb3JtKCkNCiAgICAgIGlmICghY2hlY2tTdWNjZXNzKSByZXR1cm4gZmFsc2UNCiAgICAgIGNvbnN0IG9iaiA9IHt9DQogICAgICBpZiAoIXRoaXMudGJEYXRhLmxlbmd0aCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAn5pWw5o2u5LiN6IO95Li656m6JywNCiAgICAgICAgICB0eXBlOiAnc3VjY2VzcycNCiAgICAgICAgfSkNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICBpZiAodGhpcy5pc0NvbSkgew0KICAgICAgICBvYmouU2NoZHVsaW5nX0NvbXBzID0gdGhpcy50YkRhdGENCiAgICAgIH0gZWxzZSB7DQogICAgICAgIG9iai5TYXJlUGFydHNNb2RlbCA9IHRoaXMudGJEYXRhDQogICAgICB9DQogICAgICBpZiAodGhpcy5pc0VkaXQpIHsNCiAgICAgICAgb2JqLlNjaGR1bGluZ19QbGFuID0gdGhpcy5mb3JtSW5saW5lDQogICAgICB9IGVsc2Ugew0KICAgICAgICBvYmouU2NoZHVsaW5nX1BsYW4gPSB7DQogICAgICAgICAgLi4udGhpcy5mb3JtSW5saW5lLA0KICAgICAgICAgIFByb2plY3RfSWQ6IHRoaXMucHJvamVjdElkLA0KICAgICAgICAgIEFyZWFfSWQ6IHRoaXMuYXJlYUlkLA0KICAgICAgICAgIFNjaGR1bGluZ19Nb2RlbDogdGhpcy5tb2RlbCAvLyAx5p6E5Lu25Y2V54us5o6S5Lqn77yMMumbtuS7tuWNleeLrOaOkuS6p++8jDPmnoQv6Zu25Lu25LiA6LW35o6S5LqnDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMucGdMb2FkaW5nID0gdHJ1ZQ0KICAgICAgY29uc3QgX2Z1biA9IHRoaXMuaXNDb20gPyBTYXZlQ29tcG9uZW50U2NoZWR1bGluZ1dvcmtzaG9wIDogU2F2ZVBhcnRTY2hlZHVsaW5nV29ya3Nob3BOZXcNCiAgICAgIF9mdW4ob2JqKS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy5wZ0xvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogJ+S/neWtmOaIkOWKnycsDQogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycNCiAgICAgICAgICB9KQ0KICAgICAgICAgIHRoaXMuY2xvc2VWaWV3KCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy5wZ0xvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgZ2V0U3VibWl0VGJJbmZvKCkgew0KICAgICAgLy8g5aSE55CG5LiK5Lyg55qE5pWw5o2uDQogICAgICBsZXQgdGFibGVEYXRhID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLnRiRGF0YSkpDQogICAgICB0YWJsZURhdGEgPSB0YWJsZURhdGEuZmlsdGVyKGl0ZW0gPT4gaXRlbS5TY2hkdWxlZF9Db3VudCA+IDApDQogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRhYmxlRGF0YS5sZW5ndGg7IGkrKykgew0KICAgICAgICBjb25zdCBlbGVtZW50ID0gdGFibGVEYXRhW2ldDQogICAgICAgIGxldCBsaXN0ID0gW10NCiAgICAgICAgaWYgKCFlbGVtZW50LlRlY2hub2xvZ3lfUGF0aCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogJ+W3peW6j+S4jeiDveS4uuepuicsDQogICAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgICB9KQ0KICAgICAgICAgIHJldHVybiB7IHN0YXR1czogZmFsc2UgfQ0KICAgICAgICB9DQogICAgICAgIGlmICh0aGlzLmlzUGFydFByZXBhcmUgJiYgIWVsZW1lbnQuUGFydF9Vc2VkX1Byb2Nlc3MgJiYgZWxlbWVudC5UeXBlICE9PSAnRGlyZWN0JyAmJiB0aGlzLmNvbVBhcnQpIHsNCiAgICAgICAgICBjb25zdCBtc2cgPSAn6aKG55So5bel5bqP5LiN6IO95Li656m6Jw0KICAgICAgICAgIGlmICh0aGlzLmlzTmVzdCkgew0KICAgICAgICAgICAgaWYgKGVsZW1lbnQuQ29tcF9JbXBvcnRfRGV0YWlsX0lkKSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgIG1lc3NhZ2U6IG1zZywNCiAgICAgICAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgcmV0dXJuIHsgc3RhdHVzOiBmYWxzZSB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiBtc2csDQogICAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHJldHVybiB7IHN0YXR1czogZmFsc2UgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICAvLyBpZiAoIXRoaXMuaXNDb20gJiYgZWxlbWVudC5Db21wX0ltcG9ydF9EZXRhaWxfSWQgJiYgIWVsZW1lbnQuUGFydF9Vc2VkX1Byb2Nlc3MpIHsNCiAgICAgICAgLy8gICAvLyDpm7bmnoTku7Yg6Zu25Lu25Y2V54us5o6S5LqnDQogICAgICAgIC8vICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgIC8vICAgICBtZXNzYWdlOiAn6Zu25Lu26aKG55So5bel5bqP5LiN6IO95Li656m6JywNCiAgICAgICAgLy8gICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICAvLyAgIH0pDQogICAgICAgIC8vICAgcmV0dXJuIHsgc3RhdHVzOiBmYWxzZSB9DQogICAgICAgIC8vIH0NCiAgICAgICAgaWYgKGVsZW1lbnQuU2NoZWR1bGVkX1RlY2hub2xvZ3lfUGF0aCAmJiBlbGVtZW50LlNjaGVkdWxlZF9UZWNobm9sb2d5X1BhdGggIT09IGVsZW1lbnQuVGVjaG5vbG9neV9QYXRoKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiBg6K+35ZKM6K+l5Yy65Z+f5om55qyh5LiL5bey5o6S5Lqn5ZCMJHt0aGlzLmlzQ29tID8gYCR7dGhpcy5jb21OYW1lfWAgOiAn6Zu25Lu2J33kv53mjIHlt6Xluo/kuIDoh7RgLA0KICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgICAgfSkNCiAgICAgICAgICByZXR1cm4geyBzdGF0dXM6IGZhbHNlIH0NCiAgICAgICAgfQ0KICAgICAgICBpZiAoZWxlbWVudC5TY2hlZHVsZWRfVXNlZF9Qcm9jZXNzICYmIGVsZW1lbnQuU2NoZWR1bGVkX1VzZWRfUHJvY2VzcyAhPT0gZWxlbWVudC5QYXJ0X1VzZWRfUHJvY2Vzcykgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogYOivt+WSjOivpeWMuuWfn+aJueasoeS4i+W3suaOkuS6p+WQjOmbtuS7tumihueUqOW3peW6j+S/neaMgeS4gOiHtGAsDQogICAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgICB9KQ0KICAgICAgICAgIHJldHVybiB7IHN0YXR1czogZmFsc2UgfQ0KICAgICAgICB9DQogICAgICAgIGNvbnN0IHByb2Nlc3NMaXN0ID0gQXJyYXkuZnJvbShuZXcgU2V0KGVsZW1lbnQuVGVjaG5vbG9neV9QYXRoLnNwbGl0KCcvJykpKQ0KICAgICAgICAvKiBwcm9jZXNzTGlzdC5mb3JFYWNoKGNvZGUgPT4gew0KICAgICAgICAgIGNvbnN0IGdyb3VwcyA9IHRoaXMud29ya2luZ1RlYW0uZmlsdGVyKHYgPT4gdi5Qcm9jZXNzX0NvZGUgPT09IGNvZGUpDQogICAgICAgICAgY29uc3QgZ3JvdXBzTGlzdCA9IGdyb3Vwcy5tYXAoZ3JvdXAgPT4gew0KICAgICAgICAgICAgY29uc3QgdUNvZGUgPSB0aGlzLmdldFJvd1VuaXF1ZShlbGVtZW50LnV1aWQsIGNvZGUsIGdyb3VwLldvcmtpbmdfVGVhbV9JZCkNCiAgICAgICAgICAgIGNvbnN0IHVNYXggPSB0aGlzLmdldFJvd1VuaXF1ZU1heChlbGVtZW50LnV1aWQsIGNvZGUsIGdyb3VwLldvcmtpbmdfVGVhbV9JZCkNCiAgICAgICAgICAgIGNvbnN0IG9iaiA9IHsNCiAgICAgICAgICAgICAgVGVhbV9UYXNrX0lkOiBlbGVtZW50LlRlYW1fVGFza19JZCwNCiAgICAgICAgICAgICAgQ29tcF9Db2RlOiBlbGVtZW50LkNvbXBfQ29kZSwNCiAgICAgICAgICAgICAgQWdhaW5fQ291bnQ6ICtlbGVtZW50W3VDb2RlXSB8fCAwLCAvLyDkuI3loavvvIzlkI7lj7DorqnkvKAwDQogICAgICAgICAgICAgIFBhcnRfQ29kZTogdGhpcy5pc0NvbSA/IG51bGwgOiAnJywNCiAgICAgICAgICAgICAgUHJvY2Vzc19Db2RlOiBjb2RlLA0KICAgICAgICAgICAgICBUZWNobm9sb2d5X1BhdGg6IGVsZW1lbnQuVGVjaG5vbG9neV9QYXRoLA0KICAgICAgICAgICAgICBXb3JraW5nX1RlYW1fSWQ6IGdyb3VwLldvcmtpbmdfVGVhbV9JZCwNCiAgICAgICAgICAgICAgV29ya2luZ19UZWFtX05hbWU6IGdyb3VwLldvcmtpbmdfVGVhbV9OYW1lDQogICAgICAgICAgICB9DQogICAgICAgICAgICBkZWxldGUgZWxlbWVudFt1Q29kZV0NCiAgICAgICAgICAgIGRlbGV0ZSBlbGVtZW50W3VNYXhdDQogICAgICAgICAgICByZXR1cm4gb2JqDQogICAgICAgICAgfSkNCiAgICAgICAgICBsaXN0LnB1c2goLi4uZ3JvdXBzTGlzdCkNCiAgICAgICAgfSkqLw0KICAgICAgICBmb3IgKGxldCBqID0gMDsgaiA8IHByb2Nlc3NMaXN0Lmxlbmd0aDsgaisrKSB7DQogICAgICAgICAgY29uc3QgY29kZSA9IHByb2Nlc3NMaXN0W2pdDQogICAgICAgICAgY29uc3Qgc2NoZHVsZWRDb3VudCA9IGVsZW1lbnQuU2NoZHVsZWRfQ291bnQgfHwgMA0KICAgICAgICAgIGxldCBncm91cHMgPSBbXQ0KICAgICAgICAgIGlmIChlbGVtZW50LkFsbG9jYXRpb25fVGVhbXMpIHsNCiAgICAgICAgICAgIGdyb3VwcyA9IGVsZW1lbnQuQWxsb2NhdGlvbl9UZWFtcy5maWx0ZXIodiA9PiB2LlByb2Nlc3NfQ29kZSA9PT0gY29kZSkNCiAgICAgICAgICB9DQogICAgICAgICAgY29uc3QgYWdhaW5Db3VudCA9IGdyb3Vwcy5yZWR1Y2UoKGFjYywgY3VyKSA9PiB7DQogICAgICAgICAgICByZXR1cm4gYWNjICsgKGN1ci5BZ2Fpbl9Db3VudCB8fCAwKQ0KICAgICAgICAgIH0sIDApDQogICAgICAgICAgaWYgKGFnYWluQ291bnQgPiBzY2hkdWxlZENvdW50KSB7DQogICAgICAgICAgICBsaXN0ID0gW10NCiAgICAgICAgICAgIGJyZWFrDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGxpc3QucHVzaCguLi5ncm91cHMpDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIGNvbnN0IGhhc0lucHV0ID0gT2JqZWN0LmtleXMoZWxlbWVudCkuZmlsdGVyKF8gPT4gXy5zdGFydHNXaXRoKGVsZW1lbnRbJ3V1aWQnXSkpDQogICAgICAgIGhhc0lucHV0LmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICBkZWxldGUgZWxlbWVudFtpdGVtXQ0KICAgICAgICB9KQ0KICAgICAgICBkZWxldGUgZWxlbWVudFsndXVpZCddDQogICAgICAgIGRlbGV0ZSBlbGVtZW50WydfWF9ST1dfS0VZJ10NCiAgICAgICAgZGVsZXRlIGVsZW1lbnRbJ3B1dWlkJ10NCiAgICAgICAgZWxlbWVudC5BbGxvY2F0aW9uX1RlYW1zID0gbGlzdA0KICAgICAgfQ0KICAgICAgcmV0dXJuIHsgdGFibGVEYXRhLCBzdGF0dXM6IHRydWUgfQ0KICAgIH0sDQogICAgYXN5bmMgaGFuZGxlU2F2ZURyYWZ0KHRhYmxlRGF0YSwgaXNPcmRlcikgew0KICAgICAgY29uc29sZS5sb2coJ+S/neWtmOiNieeovycpDQogICAgICBjb25zdCBfZnVuID0gdGhpcy5pc0NvbSA/IFNhdmVDb21wU2NoZHVsaW5nRHJhZnQgOiBTYXZlUGFydFNjaGR1bGluZ0RyYWZ0TmV3DQogICAgICBjb25zdCBvYmogPSB7fQ0KICAgICAgaWYgKHRoaXMuaXNDb20pIHsNCiAgICAgICAgb2JqLlNjaGR1bGluZ19Db21wcyA9IHRhYmxlRGF0YQ0KICAgICAgICBjb25zdCBwID0gW10NCiAgICAgICAgZm9yIChjb25zdCBvYmpLZXkgaW4gdGhpcy5wcm9jZXNzTGlzdCkgew0KICAgICAgICAgIGlmICh0aGlzLnByb2Nlc3NMaXN0Lmhhc093blByb3BlcnR5KG9iaktleSkpIHsNCiAgICAgICAgICAgIHAucHVzaCh0aGlzLnByb2Nlc3NMaXN0W29iaktleV0pDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIG9iai5Qcm9jZXNzX0xpc3QgPSBwDQogICAgICB9IGVsc2Ugew0KICAgICAgICBvYmouU2FyZVBhcnRzTW9kZWwgPSB0YWJsZURhdGENCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmlzRWRpdCkgew0KICAgICAgICBvYmouU2NoZHVsaW5nX1BsYW4gPSB0aGlzLmZvcm1JbmxpbmUNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIG9iai5TY2hkdWxpbmdfUGxhbiA9IHsNCiAgICAgICAgICAuLi50aGlzLmZvcm1JbmxpbmUsDQogICAgICAgICAgUHJvamVjdF9JZDogdGhpcy5wcm9qZWN0SWQsDQogICAgICAgICAgQXJlYV9JZDogdGhpcy5hcmVhSWQsDQogICAgICAgICAgU2NoZHVsaW5nX01vZGVsOiB0aGlzLm1vZGVsIC8vIDHmnoTku7bljZXni6zmjpLkuqfvvIwy6Zu25Lu25Y2V54us5o6S5Lqn77yMM+aehC/pm7bku7bkuIDotbfmjpLkuqcNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgbGV0IG9yZGVyU3VjY2VzcyA9IGZhbHNlDQogICAgICBjb25zb2xlLmxvZygnb2JqJywgb2JqKQ0KDQogICAgICBhd2FpdCBfZnVuKG9iaikudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIGlmICghaXNPcmRlcikgew0KICAgICAgICAgICAgdGhpcy5wZ0xvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6ICfkv53lrZjmiJDlip8nLA0KICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICB0aGlzLmNsb3NlVmlldygpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMudGVtcGxhdGVTY2hlZHVsZUNvZGUgPSByZXMuRGF0YQ0KICAgICAgICAgICAgb3JkZXJTdWNjZXNzID0gdHJ1ZQ0KICAgICAgICAgICAgY29uc29sZS5sb2coJ+S/neWtmOiNieeov+aIkOWKnyAnKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLnNhdmVMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICB0aGlzLnBnTG9hZGluZyA9IGZhbHNlDQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgY29uc29sZS5sb2coJ+e7k+adnyAnKQ0KICAgICAgcmV0dXJuIG9yZGVyU3VjY2Vzcw0KICAgIH0sDQogICAgaGFuZGxlRGVsZXRlKCkgew0KICAgICAgdGhpcy5kZWxldGVMb2FkaW5nID0gdHJ1ZQ0KICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgIGNvbnN0IHNlbGVjdGVkVXVpZHMgPSBuZXcgU2V0KHRoaXMubXVsdGlwbGVTZWxlY3Rpb24ubWFwKHYgPT4gdi51dWlkKSkNCiAgICAgICAgdGhpcy50YkRhdGEgPSB0aGlzLnRiRGF0YS5maWx0ZXIoaXRlbSA9PiB7DQogICAgICAgICAgY29uc3QgaXNTZWxlY3RlZCA9IHNlbGVjdGVkVXVpZHMuaGFzKGl0ZW0udXVpZCkNCiAgICAgICAgICBpZiAoaXNTZWxlY3RlZCkgew0KICAgICAgICAgICAgY29uc3Qga2V5ID0gdGhpcy5nZXRVbmlLZXkoaXRlbSkNCiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLnRiRGF0YU1hcFtrZXldDQogICAgICAgICAgfQ0KICAgICAgICAgIHJldHVybiAhaXNTZWxlY3RlZA0KICAgICAgICB9KQ0KICAgICAgICB0aGlzLiRuZXh0VGljayhfID0+IHsNCiAgICAgICAgICB0aGlzLiRyZWZzWydkcmFmdCddPy5tZXJnZURhdGEodGhpcy5tdWx0aXBsZVNlbGVjdGlvbikNCiAgICAgICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gW10NCiAgICAgICAgfSkNCiAgICAgICAgdGhpcy5kZWxldGVMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0sIDApDQogICAgfSwNCiAgICBhc3luYyBnZXRXb3JrVGVhbSgpIHsNCiAgICAgIGF3YWl0IEdldFNjaGR1bGluZ1dvcmtpbmdUZWFtcyh7DQogICAgICAgIHR5cGU6IHRoaXMuaXNDb20gPyAxIDogMg0KICAgICAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHRoaXMud29ya2luZ1RlYW0gPSByZXMuRGF0YQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZVN1Ym1pdCgpIHsNCiAgICAgIHRoaXMuJHJlZnNbJ2Zvcm1JbmxpbmUnXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKCF2YWxpZCkgcmV0dXJuDQogICAgICAgIGNvbnN0IHsgdGFibGVEYXRhLCBzdGF0dXMgfSA9IHRoaXMuZ2V0U3VibWl0VGJJbmZvKCkNCiAgICAgICAgaWYgKCFzdGF0dXMpIHJldHVybg0KICAgICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbmj5DkuqTlvZPliY3mlbDmja4/JywgJ+aPkOekuicsIHsNCiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuc2F2ZURyYWZ0RG9TdWJtaXQodGFibGVEYXRhKQ0KICAgICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICB0eXBlOiAnaW5mbycsDQogICAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raIJw0KICAgICAgICAgIH0pDQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0sDQogICAgYXN5bmMgc2F2ZURyYWZ0RG9TdWJtaXQoKSB7DQogICAgICB0aGlzLnBnTG9hZGluZyA9IHRydWUNCiAgICAgIGlmICh0aGlzLmZvcm1JbmxpbmU/LlNjaGR1bGluZ19Db2RlKSB7DQogICAgICAgIGNvbnN0IGlzU3VjY2VzcyA9IGF3YWl0IHRoaXMuc2F2ZURyYWZ0KHRydWUpDQogICAgICAgIGNvbnNvbGUubG9nKCdzYXZlRHJhZnREb1N1Ym1pdCcsIGlzU3VjY2VzcykNCiAgICAgICAgaXNTdWNjZXNzICYmIHRoaXMuZG9TdWJtaXQodGhpcy5mb3JtSW5saW5lLklkKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgY29uc3QgaXNTdWNjZXNzID0gYXdhaXQgdGhpcy5zYXZlRHJhZnQodHJ1ZSkNCiAgICAgICAgaXNTdWNjZXNzICYmIHRoaXMuZG9TdWJtaXQodGhpcy50ZW1wbGF0ZVNjaGVkdWxlQ29kZSkNCiAgICAgIH0NCiAgICB9LA0KICAgIGRvU3VibWl0KHNjaGVkdWxlQ29kZSkgew0KICAgICAgU2F2ZVNjaGR1bGluZ1Rhc2tCeUlkKHsNCiAgICAgICAgc2NoZHVsaW5nUGxhbklkOiBzY2hlZHVsZUNvZGUNCiAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6ICfkuIvovr7miJDlip8nLA0KICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLmNsb3NlVmlldygpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KS5maW5hbGx5KF8gPT4gew0KICAgICAgICB0aGlzLnBnTG9hZGluZyA9IGZhbHNlDQogICAgICB9KS5jYXRjaChfID0+IHsNCiAgICAgICAgdGhpcy5wZ0xvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFzeW5jIGdldFdvcmtTaG9wKHZhbHVlKSB7DQogICAgICBjb25zdCB7DQogICAgICAgIG9yaWdpbiwNCiAgICAgICAgcm93LA0KICAgICAgICB3b3JrU2hvcDogew0KICAgICAgICAgIElkLA0KICAgICAgICAgIERpc3BsYXlfTmFtZQ0KICAgICAgICB9DQogICAgICB9ID0gdmFsdWUNCiAgICAgIGlmIChvcmlnaW4gPT09IDIpIHsNCiAgICAgICAgaWYgKHZhbHVlLndvcmtTaG9wPy5JZCkgew0KICAgICAgICAgIHJvdy5Xb3Jrc2hvcF9OYW1lID0gRGlzcGxheV9OYW1lDQogICAgICAgICAgcm93LldvcmtzaG9wX0lkID0gSWQNCiAgICAgICAgICB0aGlzLnNldFBhdGgocm93LCBJZCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICByb3cuV29ya3Nob3BfTmFtZSA9ICcnDQogICAgICAgICAgcm93LldvcmtzaG9wX0lkID0gJycNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8gY29uc3QgZ3lNYXAgPSBhd2FpdCB0aGlzLmdldENyYWZ0UHJvY2VzcygpDQogICAgICAgIC8vIGNvbnN0IF9wcm9jZXNzID0gYXdhaXQgdGhpcy5nZXRQcm9jZXNzT3B0aW9uKHZhbHVlLndvcmtTaG9wPy5JZCkNCiAgICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgIGlmICh2YWx1ZS53b3JrU2hvcD8uSWQpIHsNCiAgICAgICAgICAgIGl0ZW0uV29ya3Nob3BfTmFtZSA9IERpc3BsYXlfTmFtZQ0KICAgICAgICAgICAgaXRlbS5Xb3Jrc2hvcF9JZCA9IElkDQogICAgICAgICAgICB0aGlzLnNldFBhdGgoaXRlbSwgSWQpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGl0ZW0uV29ya3Nob3BfTmFtZSA9ICcnDQogICAgICAgICAgICBpdGVtLldvcmtzaG9wX0lkID0gJycNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICBzZXRQYXRoKHJvdywgSWQpIHsNCiAgICAgIGlmIChyb3c/LlNjaGVkdWxlZF9Xb3Jrc2hvcF9JZCkgew0KICAgICAgICBpZiAocm93LlNjaGVkdWxlZF9Xb3Jrc2hvcF9JZCAhPT0gSWQpIHsNCiAgICAgICAgICByb3cuVGVjaG5vbG9neV9QYXRoID0gJycNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcm93LlRlY2hub2xvZ3lfUGF0aCA9ICcnDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVCYXRjaFdvcmtzaG9wKG9yaWdpbiwgcm93KSB7DQogICAgICB0aGlzLnRpdGxlID0gb3JpZ2luID09PSAxID8gJ+aJuemHj+WIhumFjei9pumXtCcgOiAn5YiG6YWN6L2m6Ze0Jw0KICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ1dvcmtzaG9wJw0KICAgICAgdGhpcy5kV2lkdGggPSAnMzAlJw0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgICAgdGhpcy4kbmV4dFRpY2soXyA9PiB7DQogICAgICAgIHRoaXMuJHJlZnNbJ2NvbnRlbnQnXS5mZXRjaERhdGEob3JpZ2luLCByb3csIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24pDQogICAgICB9KQ0KICAgIH0sDQogICAgYXN5bmMgbWVyZ2VDcmFmdFByb2Nlc3MobGlzdCkgew0KICAgICAgbGV0IGNvZGVzID0gWy4uLm5ldyBTZXQobGlzdC5tYXAodiA9PiB2LlRlY2hub2xvZ3lfQ29kZSkpXQ0KICAgICAgZm9yIChjb25zdCBrZXkgaW4gdGhpcy5jcmFmdENvZGVNYXApIHsNCiAgICAgICAgaWYgKHRoaXMuY3JhZnRDb2RlTWFwLmhhc093blByb3BlcnR5KGtleSkpIHsNCiAgICAgICAgICBjb2RlcyA9IGNvZGVzLmZpbHRlcihjb2RlID0+IGNvZGUgIT09IGtleSkNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgY29uc3QgX2NyYWZ0Q29kZU1hcCA9IGF3YWl0IHRoaXMuZ2V0Q3JhZnRQcm9jZXNzKGNvZGVzKQ0KICAgICAgT2JqZWN0LmFzc2lnbih0aGlzLmNyYWZ0Q29kZU1hcCwgX2NyYWZ0Q29kZU1hcCkNCiAgICB9LA0KICAgIGdldENyYWZ0UHJvY2VzcyhneUdyb3VwID0gW10pIHsNCiAgICAgIGd5R3JvdXAgPSBneUdyb3VwLmZpbHRlcih2ID0+ICEhdikNCiAgICAgIGlmICghZ3lHcm91cC5sZW5ndGgpIHJldHVybiBQcm9taXNlLnJlc29sdmUoe30pDQogICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4gew0KICAgICAgICBHZXRQcm9jZXNzRmxvd0xpc3RXaXRoVGVjaG5vbG9neSh7DQogICAgICAgICAgVGVjaG5vbG9neUNvZGVzOiBneUdyb3VwLA0KICAgICAgICAgIFR5cGU6IDENCiAgICAgICAgfSkudGhlbihyZXMgPT4gew0KICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICBjb25zdCBneUxpc3QgPSByZXMuRGF0YSB8fCBbXQ0KICAgICAgICAgICAgY29uc3QgZ3lNYXAgPSBneUxpc3QucmVkdWNlKChhY2MsIGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgYWNjW2l0ZW0uQ29kZV0gPSBpdGVtLlRlY2hub2xvZ3lfUGF0aA0KICAgICAgICAgICAgICByZXR1cm4gYWNjDQogICAgICAgICAgICB9LCB7fSkNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdneU1hcCcsIGd5TWFwKQ0KICAgICAgICAgICAgcmVzb2x2ZShneU1hcCkNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgcmVqZWN0KCkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0sDQogICAgLyogICBjaGVja1Byb2Nlc3MocExpc3QsIGZsb3dMaXN0KSB7DQogICAgICByZXR1cm4gZmxvd0xpc3QuZXZlcnkoaXRlbSA9PiBwTGlzdC5pbmNsdWRlcyhpdGVtKSkNCiAgICB9LCovDQogICAgYXN5bmMgaGFuZGxlQXV0b0RlYWwoKSB7DQogICAgICAvKiAgICAgIGlmICh0aGlzLndvcmtzaG9wRW5hYmxlZCkgew0KICAgICAgICBjb25zdCBoYXNXb3JrU2hvcCA9IHRoaXMuY2hlY2tIYXNXb3JrU2hvcCgxLCB0aGlzLm11bHRpcGxlU2VsZWN0aW9uKQ0KICAgICAgICBpZiAoIWhhc1dvcmtTaG9wKSByZXR1cm4NCiAgICAgIH0qLw0KDQogICAgICB0aGlzLiRjb25maXJtKGDmmK/lkKblsIbpgInkuK3mlbDmja7mjIkke3RoaXMuY29tTmFtZX3nsbvlnovoh6rliqjliIbphY1gLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICBpZiAodGhpcy53b3Jrc2hvcEVuYWJsZWQpIHsNCiAgICAgICAgICBjb25zdCBwID0gdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICB1bmlxdWVUeXBlOiBgJHtpdGVtLlR5cGV9JF8kJHtpdGVtLldvcmtzaG9wX0lkfWANCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICAgIGNvbnN0IGNvZGVzID0gQXJyYXkuZnJvbShuZXcgU2V0KHAubWFwKHYgPT4gdi51bmlxdWVUeXBlKSkpDQogICAgICAgICAgY29uc3Qgb2JqS2V5ID0ge30NCiAgICAgICAgICBQcm9taXNlLmFsbChjb2Rlcy5tYXAodiA9PiB7DQogICAgICAgICAgICBjb25zdCBpbmZvID0gdi5zcGxpdCgnJF8kJykNCiAgICAgICAgICAgIHJldHVybiB0aGlzLnNldExpYlR5cGUoaW5mb1swXSwgaW5mb1sxXSkNCiAgICAgICAgICB9KQ0KICAgICAgICAgICkudGhlbihyZXMgPT4gew0KICAgICAgICAgICAgY29uc3QgaGFzVW5kZWZpbmVkID0gcmVzLnNvbWUoaXRlbSA9PiBpdGVtID09IHVuZGVmaW5lZCkNCiAgICAgICAgICAgIGlmIChoYXNVbmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgbWVzc2FnZTogYOaJgOmAiei9pumXtOWGheW3peW6j+ePree7hOS4jiR7dGhpcy5jb21OYW1lfeexu+Wei+W3peW6j+S4jeWMuemFje+8jOivt+aJi+WKqOWIhumFjeW3peW6j2AsDQogICAgICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIHJlcy5mb3JFYWNoKChlbGVtZW50LCBpZHgpID0+IHsNCiAgICAgICAgICAgICAgb2JqS2V5W2NvZGVzW2lkeF1dID0gZWxlbWVudA0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24uZm9yRWFjaCgoZWxlbWVudCkgPT4gew0KICAgICAgICAgICAgICBlbGVtZW50LlRlY2hub2xvZ3lfUGF0aCA9IG9iaktleVtgJHtlbGVtZW50LlR5cGV9JF8kJHtlbGVtZW50LldvcmtzaG9wX0lkfWBdDQogICAgICAgICAgICAgIHRoaXMucmVzZXRXb3JrVGVhbU1heChlbGVtZW50LCBlbGVtZW50LlRlY2hub2xvZ3lfUGF0aCkNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zdCBwID0gdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLlR5cGUpDQogICAgICAgICAgY29uc3QgY29kZXMgPSBBcnJheS5mcm9tKG5ldyBTZXQocCkpDQogICAgICAgICAgY29uc3Qgb2JqS2V5ID0ge30NCg0KICAgICAgICAgIFByb21pc2UuYWxsKGNvZGVzLm1hcCh2ID0+IHsNCiAgICAgICAgICAgIHJldHVybiB0aGlzLnNldExpYlR5cGUodikNCiAgICAgICAgICB9KSkudGhlbihyZXMgPT4gew0KICAgICAgICAgICAgcmVzLmZvckVhY2goKGVsZW1lbnQsIGlkeCkgPT4gew0KICAgICAgICAgICAgICBvYmpLZXlbY29kZXNbaWR4XV0gPSBlbGVtZW50DQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5mb3JFYWNoKChlbGVtZW50KSA9PiB7DQogICAgICAgICAgICAgIGVsZW1lbnQuVGVjaG5vbG9neV9QYXRoID0gb2JqS2V5W2VsZW1lbnQuVHlwZV0NCiAgICAgICAgICAgICAgdGhpcy5yZXNldFdvcmtUZWFtTWF4KGVsZW1lbnQsIGVsZW1lbnQuVGVjaG5vbG9neV9QYXRoKQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIHR5cGU6ICdpbmZvJywNCiAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raIJw0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGdldFByb2Nlc3NPcHRpb24od29ya3Nob3BJZCkgew0KICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHsNCiAgICAgICAgR2V0UHJvY2Vzc0xpc3RCYXNlKHsNCiAgICAgICAgICB3b3Jrc2hvcElkOiB3b3Jrc2hvcElkLA0KICAgICAgICAgIHR5cGU6IDEgLy8gMDrlhajpg6jvvIzlt6Xoibrnsbvlnosx77ya5p6E5Lu25bel6Im677yMMu+8mumbtuS7tuW3peiJug0KICAgICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICAgIGNvbnN0IHByb2Nlc3MgPSByZXMuRGF0YS5tYXAodiA9PiB2LkNvZGUpDQogICAgICAgICAgICByZXNvbHZlKHByb2Nlc3MpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0sDQogICAgc2V0TGliVHlwZShjb2RlLCB3b3Jrc2hvcElkKSB7DQogICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHsNCiAgICAgICAgY29uc3Qgb2JqID0gew0KICAgICAgICAgIENvbXBvbmVudF90eXBlOiBjb2RlLA0KICAgICAgICAgIHR5cGU6IDENCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy53b3Jrc2hvcEVuYWJsZWQpIHsNCiAgICAgICAgICBvYmoud29ya3Nob3BJZCA9IHdvcmtzaG9wSWQNCiAgICAgICAgfQ0KICAgICAgICBHZXRMaWJMaXN0VHlwZShvYmopLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgaWYgKHJlcy5EYXRhLkRhdGEgJiYgcmVzLkRhdGEuRGF0YS5sZW5ndGgpIHsNCiAgICAgICAgICAgICAgY29uc3QgaW5mbyA9IHJlcy5EYXRhLkRhdGFbMF0NCiAgICAgICAgICAgICAgY29uc3Qgd29ya0NvZGUgPSBpbmZvLldvcmtDb2RlICYmIGluZm8uV29ya0NvZGUucmVwbGFjZSgvXFwvZywgJy8nKQ0KICAgICAgICAgICAgICByZXNvbHZlKHdvcmtDb2RlKQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgcmVzb2x2ZSh1bmRlZmluZWQpDQogICAgICAgICAgICB9DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0sDQogICAgaW5wdXRDaGFuZ2Uocm93KSB7DQogICAgICB0aGlzLnNldElucHV0TWF4KHJvdykNCiAgICB9LA0KICAgIHNldElucHV0TWF4KHJvdykgew0KICAgICAgY29uc3QgaW5wdXRWYWx1ZXNLZXlzID0gT2JqZWN0LmtleXMocm93KQ0KICAgICAgICAuZmlsdGVyKHYgPT4gIXYuZW5kc1dpdGgoJ21heCcpICYmIHYuc3RhcnRzV2l0aChyb3cudXVpZCkgJiYgdi5sZW5ndGggPiByb3cudXVpZC5sZW5ndGgpDQogICAgICBpbnB1dFZhbHVlc0tleXMuZm9yRWFjaCgodmFsKSA9PiB7DQogICAgICAgIGNvbnN0IGN1ckNvZGUgPSB2YWwuc3BsaXQoU1BMSVRfU1lNQk9MKVsxXQ0KICAgICAgICBjb25zdCBvdGhlclRvdGFsID0gaW5wdXRWYWx1ZXNLZXlzLmZpbHRlcih4ID0+IHsNCiAgICAgICAgICBjb25zdCBjb2RlID0geC5zcGxpdChTUExJVF9TWU1CT0wpWzFdDQogICAgICAgICAgcmV0dXJuIHggIT09IHZhbCAmJiBjb2RlID09PSBjdXJDb2RlDQogICAgICAgIH0pLnJlZHVjZSgoYWNjLCBpdGVtKSA9PiB7DQogICAgICAgICAgcmV0dXJuIGFjYyArIG51bWVyYWwocm93W2l0ZW1dKS52YWx1ZSgpDQogICAgICAgIH0sIDApDQogICAgICAgIHJvd1t2YWwgKyBTUExJVF9TWU1CT0wgKyAnbWF4J10gPSByb3cuU2NoZHVsZWRfQ291bnQgLSBvdGhlclRvdGFsDQogICAgICB9KQ0KICAgIH0sDQogICAgc2VuZFByb2Nlc3MoeyBhcnIsIHN0ciB9KSB7DQogICAgICBsZXQgaXNTdWNjZXNzID0gdHJ1ZQ0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBhcnIubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgY29uc3QgaXRlbSA9IGFycltpXQ0KICAgICAgICBpZiAoaXRlbS5vcmlnaW5hbFBhdGggJiYgaXRlbS5vcmlnaW5hbFBhdGggIT09IHN0cikgew0KICAgICAgICAgIGlzU3VjY2VzcyA9IGZhbHNlDQogICAgICAgICAgYnJlYWsNCiAgICAgICAgfQ0KICAgICAgICBpdGVtLlRlY2hub2xvZ3lfUGF0aCA9IHN0cg0KICAgICAgfQ0KICAgICAgaWYgKCFpc1N1Y2Nlc3MpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogYOivt+WSjOivpeWMuuWfn+aJueasoeS4i+W3suaOkuS6p+WQjCR7dGhpcy5jb21OYW1lfeS/neaMgeW3peW6j+S4gOiHtGAsDQogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICByZXNldFdvcmtUZWFtTWF4KHJvdywgc3RyKSB7DQogICAgICBpZiAoc3RyKSB7DQogICAgICAgIHJvdy5UZWNobm9sb2d5X1BhdGggPSBzdHINCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHN0ciA9IHJvdy5UZWNobm9sb2d5X1BhdGgNCiAgICAgIH0NCiAgICAgIGNvbnN0IGxpc3QgPSBzdHI/LnNwbGl0KCcvJykgfHwgW10NCiAgICAgIHRoaXMud29ya2luZ1RlYW0uZm9yRWFjaCgoZWxlbWVudCwgaWR4KSA9PiB7DQogICAgICAgIGNvbnN0IGN1ciA9IGxpc3Quc29tZShrID0+IGsgPT09IGVsZW1lbnQuUHJvY2Vzc19Db2RlKQ0KICAgICAgICBjb25zdCBjb2RlID0gdGhpcy5nZXRSb3dVbmlxdWUocm93LnV1aWQsIGVsZW1lbnQuUHJvY2Vzc19Db2RlLCBlbGVtZW50LldvcmtpbmdfVGVhbV9JZCkNCiAgICAgICAgY29uc3QgbWF4ID0gdGhpcy5nZXRSb3dVbmlxdWVNYXgocm93LnV1aWQsIGVsZW1lbnQuUHJvY2Vzc19Db2RlLCBlbGVtZW50LldvcmtpbmdfVGVhbV9JZCkNCiAgICAgICAgaWYgKGN1cikgew0KICAgICAgICAgIGlmICghcm93W2NvZGVdKSB7DQogICAgICAgICAgICB0aGlzLiRzZXQocm93LCBjb2RlLCAwKQ0KICAgICAgICAgICAgdGhpcy4kc2V0KHJvdywgbWF4LCByb3cuU2NoZHVsZWRfQ291bnQpDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJGRlbGV0ZShyb3csIGNvZGUpDQogICAgICAgICAgdGhpcy4kZGVsZXRlKHJvdywgbWF4KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgY2hlY2tQZXJtaXNzaW9uVGVhbShwcm9jZXNzU3RyLCBwcm9jZXNzQ29kZSkgew0KICAgICAgaWYgKCFwcm9jZXNzU3RyKSByZXR1cm4gZmFsc2UNCiAgICAgIGNvbnN0IGxpc3QgPSBwcm9jZXNzU3RyPy5zcGxpdCgnLycpIHx8IFtdDQogICAgICByZXR1cm4gISFsaXN0LnNvbWUodiA9PiB2ID09PSBwcm9jZXNzQ29kZSkNCiAgICB9LA0KDQogICAgYXN5bmMgZ2V0VGFibGVDb25maWcoY29kZSkgew0KICAgICAgYXdhaXQgR2V0R3JpZEJ5Q29kZSh7DQogICAgICAgIGNvZGUNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBjb25zdCB7IElzU3VjY2VlZCwgRGF0YSwgTWVzc2FnZSB9ID0gcmVzDQogICAgICAgIGlmIChJc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLnRiQ29uZmlnID0gT2JqZWN0LmFzc2lnbih7fSwgdGhpcy50YkNvbmZpZywgRGF0YS5HcmlkKQ0KICAgICAgICAgIGNvbnN0IGxpc3QgPSBEYXRhLkNvbHVtbkxpc3QgfHwgW10NCiAgICAgICAgICB0aGlzLm93bmVyQ29sdW1uID0gbGlzdC5maW5kKGl0ZW0gPT4gaXRlbS5Db2RlID09PSAnUGFydF9Vc2VkX1Byb2Nlc3MnKQ0KICAgICAgICAgIHRoaXMub3duZXJDb2x1bW4yID0gbGlzdC5maW5kKGl0ZW0gPT4gaXRlbS5Db2RlID09PSAnSXNfTWFpbl9QYXJ0JykNCiAgICAgICAgICB0aGlzLmNvbHVtbnMgPSB0aGlzLnNldENvbHVtbkRpc3BsYXkobGlzdCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IE1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHNldENvbHVtbkRpc3BsYXkobGlzdCkgew0KICAgICAgcmV0dXJuIGxpc3QuZmlsdGVyKHYgPT4gdi5Jc19EaXNwbGF5KQ0KICAgICAgLy8gLm1hcChpdGVtID0+IHsNCiAgICAgIC8vICAgaWYgKEZJWF9DT0xVTU4uaW5jbHVkZXMoaXRlbS5Db2RlKSkgew0KICAgICAgLy8gICAgIGl0ZW0uZml4ZWQgPSAnbGVmdCcNCiAgICAgIC8vICAgfQ0KICAgICAgLy8gICByZXR1cm4gaXRlbQ0KICAgICAgLy8gfSkNCiAgICB9LA0KICAgIGFjdGl2ZUNlbGxNZXRob2QoeyByb3csIGNvbHVtbiwgY29sdW1uSW5kZXggfSkgew0KICAgICAgaWYgKHRoaXMuaXNWaWV3KSByZXR1cm4gZmFsc2UNCiAgICAgIGNvbnN0IHByb2Nlc3NDb2RlID0gY29sdW1uLmZpZWxkPy5zcGxpdCgnJF8kJylbMV0NCiAgICAgIHJldHVybiB0aGlzLmNoZWNrUGVybWlzc2lvblRlYW0ocm93LlRlY2hub2xvZ3lfUGF0aCwgcHJvY2Vzc0NvZGUpDQogICAgfSwNCiAgICBvcGVuQlBBRGlhbG9nKHR5cGUsIHJvdykgew0KICAgICAgaWYgKHRoaXMud29ya3Nob3BFbmFibGVkKSB7DQogICAgICAgIGlmICh0eXBlID09PSAxKSB7DQogICAgICAgICAgY29uc3QgSXNVbmlxdWUgPSB0aGlzLmNoZWNrSXNVbmlxdWVXb3Jrc2hvcCgpDQogICAgICAgICAgaWYgKCFJc1VuaXF1ZSkgcmV0dXJuDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMudGl0bGUgPSB0eXBlID09PSAyID8gJ+W3peW6j+iwg+aVtCcgOiAn5om56YeP5bel5bqP6LCD5pW0Jw0KICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ0JhdGNoUHJvY2Vzc0FkanVzdCcNCiAgICAgIHRoaXMuZFdpZHRoID0gdGhpcy5pc0NvbSA/ICc2MCUnIDogJzM1JScNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuJG5leHRUaWNrKF8gPT4gew0KICAgICAgICB0aGlzLiRyZWZzWydjb250ZW50J10uc2V0RGF0YSh0eXBlID09PSAyID8gW3Jvd10gOiB0aGlzLm11bHRpcGxlU2VsZWN0aW9uLCB0eXBlID09PSAyID8gcm93LlRlY2hub2xvZ3lfUGF0aCA6ICcnKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGNoZWNrSXNVbmlxdWVXb3Jrc2hvcCgpIHsNCiAgICAgIGxldCBpc1VuaXF1ZSA9IHRydWUNCiAgICAgIGNvbnN0IGZpcnN0ViA9IHRoaXMubXVsdGlwbGVTZWxlY3Rpb25bMF0uV29ya3Nob3BfTmFtZQ0KICAgICAgZm9yIChsZXQgaSA9IDE7IGkgPCB0aGlzLm11bHRpcGxlU2VsZWN0aW9uLmxlbmd0aDsgaSsrKSB7DQogICAgICAgIGNvbnN0IGl0ZW0gPSB0aGlzLm11bHRpcGxlU2VsZWN0aW9uW2ldDQogICAgICAgIGlmIChpdGVtLldvcmtzaG9wX05hbWUgIT09IGZpcnN0Vikgew0KICAgICAgICAgIGlzVW5pcXVlID0gZmFsc2UNCiAgICAgICAgICBicmVhaw0KICAgICAgICB9DQogICAgICB9DQogICAgICBpZiAoIWlzVW5pcXVlKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICfmibnph4/liIbphY3lt6Xluo/ml7blj6rmnInnm7jlkIzovabpl7TkuIvnmoTmiY3lj6/kuIDotbfmibnph4/liIbphY0nLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGlzVW5pcXVlDQogICAgfSwNCiAgICBjaGVja0hhc1dvcmtTaG9wKHR5cGUsIGFycikgew0KICAgICAgbGV0IGhhc1dvcmtTaG9wID0gdHJ1ZQ0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBhcnIubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgY29uc3QgaXRlbSA9IGFycltpXQ0KICAgICAgICBpZiAoIWl0ZW0uV29ya3Nob3BfTmFtZSkgew0KICAgICAgICAgIGhhc1dvcmtTaG9wID0gZmFsc2UNCiAgICAgICAgICBicmVhaw0KICAgICAgICB9DQogICAgICB9DQogICAgICBpZiAoIWhhc1dvcmtTaG9wKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICfor7flhYjpgInmi6novabpl7TlkI7lho3ov5vooYzlt6Xluo/liIbphY0nLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGhhc1dvcmtTaG9wDQogICAgfSwNCiAgICBoYW5kbGVBZGREaWFsb2codHlwZSA9ICdhZGQnKSB7DQogICAgICBpZiAodGhpcy5pc0NvbSkgew0KICAgICAgICB0aGlzLnRpdGxlID0gYCR7dGhpcy5jb21OYW1lfeaOkuS6p2ANCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMudGl0bGUgPSAn5re75Yqg6Zu25Lu2Jw0KICAgICAgfQ0KICAgICAgdGhpcy5jdXJyZW50Q29tcG9uZW50ID0gJ0FkZERyYWZ0Jw0KICAgICAgdGhpcy5kV2lkdGggPSAnODAlJw0KICAgICAgdGhpcy5vcGVuQWRkRHJhZnQgPSB0cnVlDQogICAgICB0aGlzLiRuZXh0VGljayhfID0+IHsNCiAgICAgICAgdGhpcy4kcmVmc1snZHJhZnQnXS5zZXRQYWdlRGF0YSgpDQogICAgICB9KQ0KICAgIH0sDQogICAgZ2V0Um93VW5pcXVlKHV1aWQsIHByb2Nlc3NDb2RlLCB3b3JraW5nSWQpIHsNCiAgICAgIHJldHVybiBgJHt1dWlkfSR7U1BMSVRfU1lNQk9MfSR7cHJvY2Vzc0NvZGV9JHtTUExJVF9TWU1CT0x9JHt3b3JraW5nSWR9YA0KICAgIH0sDQogICAgZ2V0Um93VW5pcXVlTWF4KHV1aWQsIHByb2Nlc3NDb2RlLCB3b3JraW5nSWQpIHsNCiAgICAgIHJldHVybiB0aGlzLmdldFJvd1VuaXF1ZSh1dWlkLCBwcm9jZXNzQ29kZSwgd29ya2luZ0lkKSArIGAke1NQTElUX1NZTUJPTH1tYXhgDQogICAgfSwNCiAgICBhc3luYyBoYW5kbGVTZWxlY3RNZW51KHYpIHsNCiAgICAgIGlmICh2ID09PSAncHJvY2VzcycpIHsNCiAgICAgICAgdGhpcy5vcGVuQlBBRGlhbG9nKDEpDQogICAgICB9IGVsc2UgaWYgKHYgPT09ICdkZWFsJykgew0KICAgICAgICBhd2FpdCB0aGlzLmhhbmRsZUF1dG9EZWFsKDEpDQogICAgICB9IGVsc2UgaWYgKHYgPT09ICdjcmFmdCcpIHsNCiAgICAgICAgYXdhaXQgdGhpcy5oYW5kbGVTZXRDcmFmdFByb2Nlc3MoKQ0KICAgICAgfQ0KICAgIH0sDQogICAgYXN5bmMgaGFuZGxlU2V0Q3JhZnRQcm9jZXNzKCkgew0KICAgICAgY29uc3Qgc2hvd1N1Y2Nlc3MgPSAoKSA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICflt7LliIbphY3miJDlip8nLA0KICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgICAgY29uc3Qgcm93TGlzdCA9IHRoaXMubXVsdGlwbGVTZWxlY3Rpb24ubWFwKHYgPT4gdi5UZWNobm9sb2d5X0NvZGUpLmZpbHRlcih2ID0+ICEhdikNCiAgICAgIGlmICghcm93TGlzdC5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogJ+W3peiJuuS7o+eggeS4jeWtmOWcqCcsDQogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgIH0pDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgYXdhaXQgdGhpcy5tZXJnZUNyYWZ0UHJvY2Vzcyh0aGlzLm11bHRpcGxlU2VsZWN0aW9uKQ0KICAgICAgY29uc3Qgd29ya3Nob3BJZHMgPSBBcnJheS5mcm9tKG5ldyBTZXQodGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5tYXAodiA9PiB2LldvcmtzaG9wX0lkKS5maWx0ZXIodiA9PiAhIXYpKSkNCiAgICAgIGNvbnN0IHdfcHJvY2VzcyA9IFtdDQogICAgICBpZiAod29ya3Nob3BJZHMubGVuZ3RoKSB7DQogICAgICAgIHdvcmtzaG9wSWRzLmZvckVhY2god29ya3Nob3BJZCA9PiB7DQogICAgICAgICAgd19wcm9jZXNzLnB1c2godGhpcy5nZXRQcm9jZXNzT3B0aW9uKHdvcmtzaG9wSWQpLnRoZW4ocmVzdWx0ID0+ICh7DQogICAgICAgICAgICBbd29ya3Nob3BJZF06IHJlc3VsdA0KICAgICAgICAgIH0pKSkNCiAgICAgICAgfSkNCiAgICAgICAgY29uc3Qgd29ya3Nob3BQcm9taXNlID0gUHJvbWlzZS5hbGwod19wcm9jZXNzKS50aGVuKCh2YWx1ZXMpID0+IHsNCiAgICAgICAgICByZXR1cm4gT2JqZWN0LmFzc2lnbih7fSwgLi4udmFsdWVzKQ0KICAgICAgICB9KQ0KICAgICAgICB3b3Jrc2hvcFByb21pc2UudGhlbih3b3Jrc2hvcCA9PiB7DQogICAgICAgICAgbGV0IGZsYWcgPSB0cnVlDQogICAgICAgICAgbGV0IHVzZWRQYXJ0RmxhZyA9IHRydWUNCiAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMubXVsdGlwbGVTZWxlY3Rpb24ubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgICAgIGNvbnN0IGN1clJvdyA9IHRoaXMubXVsdGlwbGVTZWxlY3Rpb25baV0NCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdjdXJSb3cnLCBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KGN1clJvdykpKQ0KICAgICAgICAgICAgY29uc3Qgd29ya3Nob3BQcm9jZXNzID0gd29ya3Nob3BbY3VyUm93LldvcmtzaG9wX0lkXQ0KICAgICAgICAgICAgY29uc29sZS5sb2coJ3dvcmtzaG9wUHJvY2VzcycsIHdvcmtzaG9wUHJvY2VzcykNCiAgICAgICAgICAgIGNvbnN0IGNyYWZ0QXJyYXkgPSB0aGlzLmNyYWZ0Q29kZU1hcFtjdXJSb3cuVGVjaG5vbG9neV9Db2RlXQ0KICAgICAgICAgICAgY29uc29sZS5sb2coJ2NyYWZ0QXJyYXknLCBjcmFmdEFycmF5KQ0KDQogICAgICAgICAgICBpZiAoY3JhZnRBcnJheSkgew0KICAgICAgICAgICAgICBjb25zdCBpc0luY2x1ZGVkID0gY3JhZnRBcnJheS5ldmVyeShwcm9jZXNzID0+IHdvcmtzaG9wUHJvY2Vzcy5pbmNsdWRlcyhwcm9jZXNzKSkNCiAgICAgICAgICAgICAgaWYgKCFpc0luY2x1ZGVkKSB7DQogICAgICAgICAgICAgICAgZmxhZyA9IGZhbHNlDQogICAgICAgICAgICAgICAgY29udGludWUNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBjb25zdCBoYXNVc2VkUGFydCA9IHRoaXMuY2hlY2tIYXNDcmFmdFVzZWRQYXJ0KGN1clJvdywgY3JhZnRBcnJheSkNCiAgICAgICAgICAgICAgaWYgKGhhc1VzZWRQYXJ0KSB7DQogICAgICAgICAgICAgICAgY3VyUm93LlRlY2hub2xvZ3lfUGF0aCA9IGNyYWZ0QXJyYXkuam9pbignLycpDQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgdXNlZFBhcnRGbGFnID0gZmFsc2UNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAoIWZsYWcpIHsNCiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRhbGVydCgn5omA6YCJ6L2m6Ze05LiL54+t57uE5Yqg5bel5bel5bqP5LiN5YyF5ZCr5bel6Im65Luj56CB5bel5bqP6K+35omL5Yqo5o6S5LqnJywgJ+aPkOekuicsIHsNCiAgICAgICAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicNCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIH0sIDIwMCkNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBjb25zdCBpc1N1Y2Nlc3MgPSB0aGlzLnNob3dDcmFmdFVzZWRQYXJ0UmVzdWx0KHVzZWRQYXJ0RmxhZykNCiAgICAgICAgICBmbGFnICYmIGlzU3VjY2VzcyAmJiBzaG93U3VjY2VzcygpDQogICAgICAgIH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICBsZXQgdXNlZFBhcnRGbGFnID0gdHJ1ZQ0KICAgICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uLmZvckVhY2goKGN1clJvdykgPT4gew0KICAgICAgICAgIGNvbnN0IGNyYWZ0QXJyYXkgPSB0aGlzLmNyYWZ0Q29kZU1hcFtjdXJSb3cuVGVjaG5vbG9neV9Db2RlXQ0KICAgICAgICAgIGlmIChjcmFmdEFycmF5KSB7DQogICAgICAgICAgICBjb25zdCBoYXNVc2VkUGFydCA9IHRoaXMuY2hlY2tIYXNDcmFmdFVzZWRQYXJ0KGN1clJvdywgY3JhZnRBcnJheSkNCiAgICAgICAgICAgIGlmIChoYXNVc2VkUGFydCkgew0KICAgICAgICAgICAgICBjdXJSb3cuVGVjaG5vbG9neV9QYXRoID0gY3JhZnRBcnJheS5qb2luKCcvJykNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHVzZWRQYXJ0RmxhZyA9IGZhbHNlDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICBjb25zdCBpc1N1Y2Nlc3MgPSB0aGlzLnNob3dDcmFmdFVzZWRQYXJ0UmVzdWx0KHVzZWRQYXJ0RmxhZykNCiAgICAgICAgaXNTdWNjZXNzICYmIHNob3dTdWNjZXNzKCkNCiAgICAgIH0NCiAgICB9LA0KICAgIGNoZWNrSGFzQ3JhZnRVc2VkUGFydChjdXJSb3csIGNyYWZ0QXJyYXkpIHsNCiAgICAgIGlmICghY3VyUm93LlBhcnRfVXNlZF9Qcm9jZXNzKSByZXR1cm4gdHJ1ZQ0KICAgICAgY29uc3QgcGFydFVzZWRQcm9jZXNzID0gY3VyUm93LlBhcnRfVXNlZF9Qcm9jZXNzLnNwbGl0KCcsJykNCiAgICAgIGNvbnN0IHJlc3VsdCA9IHBhcnRVc2VkUHJvY2Vzcy5ldmVyeShpdGVtID0+IGNyYWZ0QXJyYXkuaW5jbHVkZXMoaXRlbSkpDQogICAgICByZXR1cm4gcmVzdWx0DQogICAgICAvLyByZXR1cm4gIShjdXJSb3cuUGFydF9Vc2VkX1Byb2Nlc3MgJiYgIWNyYWZ0QXJyYXkuaW5jbHVkZXMoY3VyUm93LlBhcnRfVXNlZF9Qcm9jZXNzKSkNCiAgICB9LA0KICAgIHNob3dDcmFmdFVzZWRQYXJ0UmVzdWx0KGhhc1VzZWRQYXJ0KSB7DQogICAgICBpZiAoaGFzVXNlZFBhcnQpIHJldHVybiB0cnVlDQogICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgdGhpcy4kYWxlcnQoYOmDqOWIhiR7dGhpcy5jb21OYW1lfeW3peW6j+i3r+W+hOWGheS4jeWMheWQq+mbtuS7tumihueUqOW3peW6j+ivt+aJi+WKqOaOkuS6p2AsICfmj5DnpLonLCB7DQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponDQogICAgICAgIH0pDQogICAgICB9LCAyMDApDQogICAgICByZXR1cm4gZmFsc2UNCiAgICB9LA0KICAgIGhhbmRsZUJhdGNoT3duZXIodHlwZSwgcm93KSB7DQogICAgICB0aGlzLnRpdGxlID0gJ+aJuemHj+WIhumFjemihueUqOW3peW6jycNCiAgICAgIHRoaXMuY3VycmVudENvbXBvbmVudCA9ICdPd25lclByb2Nlc3MnDQogICAgICB0aGlzLmRXaWR0aCA9ICczMCUnDQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgICB0aGlzLiRuZXh0VGljayhfID0+IHsNCiAgICAgICAgdGhpcy4kcmVmc1snY29udGVudCddLnNldE9wdGlvbih0eXBlID09PSAyLCB0eXBlID09PSAyID8gW3Jvd10gOiB0aGlzLm11bHRpcGxlU2VsZWN0aW9uKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZVJldmVyc2UoKSB7DQogICAgICBjb25zdCBjdXIgPSBbXQ0KICAgICAgdGhpcy50YkRhdGEuZm9yRWFjaCgoZWxlbWVudCwgaWR4KSA9PiB7DQogICAgICAgIGVsZW1lbnQuY2hlY2tlZCA9ICFlbGVtZW50LmNoZWNrZWQNCiAgICAgICAgaWYgKGVsZW1lbnQuY2hlY2tlZCkgew0KICAgICAgICAgIGN1ci5wdXNoKGVsZW1lbnQpDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gY3VyDQogICAgICBpZiAodGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5sZW5ndGggPT09IHRoaXMudGJEYXRhLmxlbmd0aCkgew0KICAgICAgICB0aGlzLiRyZWZzWyd4VGFibGUnXS5zZXRBbGxDaGVja2JveFJvdyh0cnVlKQ0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMubXVsdGlwbGVTZWxlY3Rpb24ubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJHJlZnNbJ3hUYWJsZSddLnNldEFsbENoZWNrYm94Um93KGZhbHNlKQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8gdGJGaWx0ZXJDaGFuZ2UoKSB7DQogICAgLy8gICBjb25zdCB4VGFibGUgPSB0aGlzLiRyZWZzLnhUYWJsZQ0KICAgIC8vICAgY29uc3QgY29sdW1uID0geFRhYmxlLmdldENvbHVtbkJ5RmllbGQoJ1R5cGVfTmFtZScpDQogICAgLy8gICBpZiAoIWNvbHVtbj8uZmlsdGVycz8ubGVuZ3RoKSByZXR1cm4NCiAgICAvLyAgIGNvbHVtbi5maWx0ZXJzLmZvckVhY2goZCA9PiB7DQogICAgLy8gICAgIGQuY2hlY2tlZCA9IGQudmFsdWUgPT09IHRoaXMuc2VhcmNoVHlwZQ0KICAgIC8vICAgfSkNCiAgICAvLyAgIHhUYWJsZS51cGRhdGVEYXRhKCkNCiAgICAvLyB9LA0KICAgIGdldFR5cGUoKSB7DQogICAgICBjb25zdCBnZXRDb21wVHJlZSA9ICgpID0+IHsNCiAgICAgICAgY29uc3QgZnVuID0gdGhpcy5pc0NvbSA/IEdldENvbXBUeXBlVHJlZSA6IEdldFBhcnRUeXBlTGlzdA0KICAgICAgICBmdW4oe30pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgbGV0IHJlc3VsdCA9IHJlcy5EYXRhDQogICAgICAgICAgICBpZiAoIXRoaXMuaXNDb20pIHsNCiAgICAgICAgICAgICAgcmVzdWx0ID0gcmVzdWx0DQogICAgICAgICAgICAgICAgLm1hcCgodiwgaWR4KSA9PiB7DQogICAgICAgICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICAgICAgICBEYXRhOiB2Lk5hbWUsDQogICAgICAgICAgICAgICAgICAgIExhYmVsOiB2Lk5hbWUNCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgdGhpcy50cmVlUGFyYW1zQ29tcG9uZW50VHlwZS5kYXRhID0gcmVzdWx0DQogICAgICAgICAgICB0aGlzLiRuZXh0VGljaygoXykgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRyZWZzLnRyZWVTZWxlY3RDb21wb25lbnRUeXBlPy50cmVlRGF0YVVwZGF0ZUZ1bihyZXN1bHQpDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfQ0KDQogICAgICBnZXRDb21wVHJlZSgpDQogICAgfSwNCiAgICAvLyDmn6XnnIvlm77nurgNCiAgICBoYW5kbGVEd2cocm93KSB7DQogICAgICBjb25zdCBvYmogPSB7fQ0KICAgICAgaWYgKHRoaXMuaXNDb20pIHsNCiAgICAgICAgb2JqLkNvbXBfSWQgPSByb3cuQ29tcF9JbXBvcnRfRGV0YWlsX0lkDQogICAgICB9IGVsc2Ugew0KICAgICAgICBvYmouUGFydF9JZCA9IHJvdy5QYXJ0X0FnZ3JlZ2F0ZV9JZA0KICAgICAgfQ0KICAgICAgR2V0RHdnKG9iaikudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIGNvbnN0IGZpbGV1cmwgPSByZXM/LkRhdGE/Lmxlbmd0aCAmJiByZXMuRGF0YVswXS5GaWxlX1VybA0KICAgICAgICAgIHdpbmRvdy5vcGVuKCdodHRwOi8vZHdndjEuYmltdGsuY29tOjU0MzIvP0NhZFVybD0nICsgcGFyc2VPc3NVcmwoZmlsZXVybCksICdfYmxhbmsnKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHNldFByb2Nlc3NMaXN0KGluZm8pIHsNCiAgICAgIHRoaXMuY2hhbmdlUHJvY2Vzc0xpc3QoaW5mbykNCiAgICB9LA0KICAgIHJlc2V0SW5uZXJGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmc1snc2VhcmNoRm9ybSddLnJlc2V0RmllbGRzKCkNCiAgICAgIHRoaXMuJHJlZnMueFRhYmxlLmNsZWFyRmlsdGVyKCkNCiAgICB9LA0KICAgIGlubmVyRmlsdGVyKCkgew0KICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbiA9IFtdDQogICAgICBjb25zdCBhcnIgPSBbXQ0KICAgICAgaWYgKHRoaXMuaXNDb20pIHsNCiAgICAgICAgYXJyLnB1c2goJ1R5cGUnLCAnQ29tcF9Db2RlJywgJ1NwZWMnLCAnSXNfQ29tcG9uZW50JykNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGFyci5wdXNoKCdQYXJ0X0NvZGUnLCAnU3BlYycsICdUeXBlX05hbWUnKQ0KICAgICAgfQ0KDQogICAgICBjb25zdCB4VGFibGUgPSB0aGlzLiRyZWZzLnhUYWJsZQ0KICAgICAgeFRhYmxlLmNsZWFyQ2hlY2tib3hSb3coKQ0KICAgICAgYXJyLmZvckVhY2goKGVsZW1lbnQsIGlkeCkgPT4gew0KICAgICAgICBjb25zdCBjb2x1bW4gPSB4VGFibGUuZ2V0Q29sdW1uQnlGaWVsZChlbGVtZW50KQ0KICAgICAgICBpZiAoZWxlbWVudCA9PT0gJ0lzX0NvbXBvbmVudCcpIHsNCiAgICAgICAgICBjb2x1bW4uZmlsdGVycy5mb3JFYWNoKChvcHRpb24sIGlkeCkgPT4gew0KICAgICAgICAgICAgb3B0aW9uLmNoZWNrZWQgPSBpZHggPT09ICh0aGlzLmlubmVyRm9ybS5zZWFyY2hEaXJlY3QgPyAwIDogMSkNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICAgIGlmIChlbGVtZW50ID09PSAnU3BlYycpIHsNCiAgICAgICAgICBjb25zdCBvcHRpb24gPSBjb2x1bW4uZmlsdGVyc1swXQ0KICAgICAgICAgIG9wdGlvbi5kYXRhID0gdGhpcy5pbm5lckZvcm0uc2VhcmNoU3BlY1NlYXJjaA0KICAgICAgICAgIG9wdGlvbi5jaGVja2VkID0gdHJ1ZQ0KICAgICAgICB9DQogICAgICAgIGlmIChlbGVtZW50ID09PSAnVHlwZScgfHwgZWxlbWVudCA9PT0gJ1R5cGVfTmFtZScpIHsNCiAgICAgICAgICBjb25zdCBvcHRpb24gPSBjb2x1bW4uZmlsdGVyc1swXQ0KICAgICAgICAgIG9wdGlvbi5kYXRhID0gdGhpcy5pbm5lckZvcm0uc2VhcmNoQ29tVHlwZVNlYXJjaA0KICAgICAgICAgIG9wdGlvbi5jaGVja2VkID0gdHJ1ZQ0KICAgICAgICB9DQogICAgICAgIGlmIChlbGVtZW50ID09PSAnQ29tcF9Db2RlJyB8fCBlbGVtZW50ID09PSAnUGFydF9Db2RlJykgew0KICAgICAgICAgIGNvbnN0IG9wdGlvbiA9IGNvbHVtbi5maWx0ZXJzWzBdDQogICAgICAgICAgb3B0aW9uLmRhdGEgPSB0aGlzLmlubmVyRm9ybS5zZWFyY2hDb250ZW50DQogICAgICAgICAgb3B0aW9uLmNoZWNrZWQgPSB0cnVlDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICB4VGFibGUudXBkYXRlRGF0YSgpDQogICAgfSwNCiAgICBmaWx0ZXJDb21wb25lbnRNZXRob2QoeyBvcHRpb24sIHJvdyB9KSB7DQogICAgICBpZiAodGhpcy5pbm5lckZvcm0uc2VhcmNoRGlyZWN0ID09PSAnJykgew0KICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIHJvdy5Jc19Db21wb25lbnQgPT09ICF0aGlzLmlubmVyRm9ybS5zZWFyY2hEaXJlY3QNCiAgICB9LA0KICAgIGZpbHRlclNwZWNNZXRob2QoeyBvcHRpb24sIHJvdyB9KSB7DQogICAgICBpZiAodGhpcy5pbm5lckZvcm0uc2VhcmNoU3BlY1NlYXJjaC50cmltKCkgPT09ICcnKSB7DQogICAgICAgIHJldHVybiB0cnVlDQogICAgICB9DQogICAgICBjb25zdCBzcGxpdEFuZENsZWFuID0gKGlucHV0KSA9PiBpbnB1dC50cmltKCkucmVwbGFjZSgvXHMrL2csICcgJykuc3BsaXQoJyAnKQ0KICAgICAgY29uc3Qgc3BlY0FycmF5ID0gc3BsaXRBbmRDbGVhbih0aGlzLmlubmVyRm9ybS5zZWFyY2hTcGVjU2VhcmNoKQ0KICAgICAgcmV0dXJuIHNwZWNBcnJheS5zb21lKGNvZGUgPT4gKHJvdy5TcGVjIHx8ICcnKS5pbmNsdWRlcyhjb2RlKSkNCiAgICB9LA0KDQogICAgZmlsdGVyVHlwZU1ldGhvZCh7IG9wdGlvbiwgcm93IH0pIHsNCiAgICAgIGlmICh0aGlzLmlubmVyRm9ybS5zZWFyY2hDb21UeXBlU2VhcmNoID09PSAnJykgew0KICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgfQ0KICAgICAgY29uc3QgY3VyID0gdGhpcy5pc0NvbSA/ICdUeXBlJyA6ICdUeXBlX05hbWUnDQogICAgICByZXR1cm4gcm93W2N1cl0gPT09IHRoaXMuaW5uZXJGb3JtLnNlYXJjaENvbVR5cGVTZWFyY2gNCiAgICB9LA0KICAgIGZpbHRlckNvZGVNZXRob2QoeyBvcHRpb24sIHJvdyB9KSB7DQogICAgICBpZiAodGhpcy5pbm5lckZvcm0uc2VhcmNoQ29udGVudC50cmltKCkgPT09ICcnKSB7DQogICAgICAgIHJldHVybiB0cnVlDQogICAgICB9DQoNCiAgICAgIGNvbnN0IHNwbGl0QW5kQ2xlYW4gPSAoaW5wdXQpID0+IGlucHV0LnRyaW0oKS5yZXBsYWNlKC9ccysvZywgJyAnKS5zcGxpdCgnICcpDQoNCiAgICAgIGNvbnN0IGN1ciA9IHRoaXMuaXNDb20gPyAnQ29tcF9Db2RlJyA6ICdQYXJ0X0NvZGUnDQoNCiAgICAgIGNvbnN0IGFyciA9IHNwbGl0QW5kQ2xlYW4odGhpcy5pbm5lckZvcm0uc2VhcmNoQ29udGVudCkNCg0KICAgICAgaWYgKHRoaXMuY3VyU2VhcmNoID09PSAxKSB7DQogICAgICAgIHJldHVybiBhcnIuc29tZShjb2RlID0+IHJvd1tjdXJdID09PSBjb2RlKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBhcnIubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgICBjb25zdCBpdGVtID0gYXJyW2ldDQogICAgICAgICAgaWYgKHJvd1tjdXJdLmluY2x1ZGVzKGl0ZW0pKSB7DQogICAgICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgIH0NCiAgICB9LA0KICAgIGNvbXBvbmVudFR5cGVGaWx0ZXIoZSkgew0KICAgICAgdGhpcy4kcmVmcz8udHJlZVNlbGVjdENvbXBvbmVudFR5cGUuZmlsdGVyRnVuKGUpDQogICAgfSwNCiAgICBnZXRJbnN0YWxsVW5pdElkTmFtZUxpc3QoaWQpIHsNCiAgICAgIGlmICghdGhpcy5hcmVhSWQgfHwgdGhpcy5pc1ZlcnNpb25Gb3VyKSB7DQogICAgICAgIHRoaXMuaW5zdGFsbFVuaXRJZExpc3QgPSBbXQ0KICAgICAgICB0aGlzLmRpc2FibGVkQWRkID0gZmFsc2UNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuZGlzYWJsZWRBZGQgPSB0cnVlDQogICAgICAgIEdldEluc3RhbGxVbml0SWROYW1lTGlzdCh7IEFyZWFfSWQ6IHRoaXMuYXJlYUlkIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICB0aGlzLmluc3RhbGxVbml0SWRMaXN0ID0gcmVzLkRhdGENCiAgICAgICAgICBpZiAodGhpcy5pbnN0YWxsVW5pdElkTGlzdC5sZW5ndGgpIHsNCiAgICAgICAgICAgIHRoaXMuZm9ybUlubGluZS5JbnN0YWxsVW5pdF9JZCA9IHRoaXMuaW5zdGFsbFVuaXRJZExpc3RbMF0uSWQNCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy5kaXNhYmxlZEFkZCA9IGZhbHNlDQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICBpbnN0YWxsQ2hhbmdlKCkgew0KICAgICAgaWYgKCF0aGlzLnRiRGF0YS5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy4kcmVmc1snc2VhcmNoRm9ybSddLnJlc2V0RmllbGRzKCkNCiAgICAgICAgdGhpcy4kcmVmcy54VGFibGUuY2xlYXJGaWx0ZXIoKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+WIh+aNouWMuuWfn+WPs+S+p+aVsOaNrua4heepuiwg5piv5ZCm56Gu6K6kPycsICfmj5DnpLonLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMudGJEYXRhID0gW10NCiAgICAgICAgdGhpcy5yZXNldElubmVyRm9ybSgpDQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIHR5cGU6ICdpbmZvJywNCiAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raIJw0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHNob3dQYXJ0VXNlZFByb2Nlc3Mocm93KSB7DQogICAgICBpZiAodGhpcy5pc05lc3QpIHsNCiAgICAgICAgcmV0dXJuICEhcm93LkNvbXBfSW1wb3J0X0RldGFpbF9JZA0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuICF0aGlzLmlzVmlldyAmJiByb3cuVHlwZSAhPT0gJ0RpcmVjdCcNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIGlmICghdGhpcy50YkRhdGEubGVuZ3RoKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICfmmoLml6DmlbDmja4nLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIGNvbnNvbGUubG9nKDcsIHRoaXMuJHJlZnMueFRhYmxlKQ0KICAgICAgY29uc3QgaXRlbSA9IHRoaXMudGJEYXRhWzBdDQogICAgICB0aGlzLiRyZWZzLnhUYWJsZS5leHBvcnREYXRhKHsNCiAgICAgICAgZmlsZW5hbWU6IGAke3RoaXMuY29tTmFtZX3mjpLkuqctJHtpdGVtLlByb2plY3RfTmFtZX0tJHtpdGVtLkFyZWFfTmFtZX0tJHt0aGlzLmZvcm1JbmxpbmUuU2NoZHVsaW5nX0NvZGV9KCR7dGhpcy5jb21OYW1lfSlgLA0KICAgICAgICB0eXBlOiAneGxzeCcsDQogICAgICAgIGRhdGE6IHRoaXMudGJEYXRhDQogICAgICB9KQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["draft.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAskBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "draft.vue", "sourceRoot": "src/views/PRO/plan-production/schedule-production-new", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100 flex-row\">\r\n    <div v-if=\"isAdd\" class=\"cs-left\">\r\n      <ExpandableSection v-model=\"showExpand\">\r\n        <div class=\"cs-tree-wrapper\">\r\n          <div class=\"tree-search\">\r\n            <el-select\r\n              v-model=\"statusType\"\r\n              clearable\r\n              class=\"search-select\"\r\n              placeholder=\"请选择\"\r\n              @change=\"fetchTreeStatus\"\r\n            >\r\n              <el-option label=\"可排产\" value=\"可排产\" />\r\n              <el-option label=\"排产完成\" value=\"排产完成\" />\r\n              <el-option label=\"未导入\" value=\"未导入\" />\r\n            </el-select>\r\n            <el-input\r\n              v-model.trim=\"projectName\"\r\n              placeholder=\"搜索...\"\r\n              size=\"small\"\r\n              clearable\r\n              suffix-icon=\"el-icon-search\"\r\n              @blur=\"fetchTreeDataLocal\"\r\n              @clear=\"fetchTreeDataLocal\"\r\n              @keydown.enter.native=\"fetchTreeDataLocal\"\r\n            />\r\n          </div>\r\n          <el-divider />\r\n          <tree-detail\r\n            ref=\"tree\"\r\n            icon=\"icon-folder\"\r\n            is-custom-filter\r\n            :custom-filter-fun=\"customFilterFun\"\r\n            :loading=\"treeLoading\"\r\n            :tree-data=\"treeData\"\r\n            show-status\r\n            show-detail\r\n            :filter-text=\"filterText\"\r\n            :expanded-key=\"expandedKey\"\r\n            @handleNodeClick=\"handleNodeClick\"\r\n          >\r\n            <template #csLabel=\"{showStatus,data}\">\r\n              <span v-if=\"!data.ParentNodes\" class=\"cs-blue\">({{ data.Code }})</span>{{ data.Label }}\r\n              <template v-if=\"showStatus\">\r\n                <i\r\n                  v-if=\"data.Data[statusCode]\"\r\n                  :class=\"[data.Data[statusCode]=='可排产' ? 'fourGreen' : data.Data[statusCode]=='排产完成' ?'fourOrange':data.Data[statusCode]=='未导入'?'fourRed':'']\"\r\n                >\r\n                  <span>({{ data.Data[statusCode] }})</span>\r\n                </i>\r\n              </template>\r\n            </template>\r\n          </tree-detail>\r\n        </div>\r\n      </ExpandableSection>\r\n    </div>\r\n    <div class=\"cs-right\">\r\n      <el-card v-loading=\"pgLoading\" class=\"box-card h100\" element-loading-text=\"正在处理...\">\r\n        <h4 class=\"topTitle\"><span />基本信息</h4>\r\n        <el-form\r\n          ref=\"formInline\"\r\n          :inline=\"true\"\r\n          :model=\"formInline\"\r\n          class=\"demo-form-inline\"\r\n        >\r\n          <el-form-item v-if=\"!isAdd&&!isNest\" label=\"排产单号\" prop=\"Schduling_Code\">\r\n            <span v-if=\"isView\">{{ formInline.Status === 0 ? '' : formInline.Schduling_Code }}</span>\r\n            <el-input v-else v-model=\"formInline.Schduling_Code\" disabled />\r\n          </el-form-item>\r\n          <el-form-item label=\"计划员\" prop=\"Create_UserName\">\r\n            <span v-if=\"isView\">{{ formInline.Create_UserName }}</span>\r\n            <el-input\r\n              v-else\r\n              v-model=\"formInline.Create_UserName\"\r\n              disabled\r\n            />\r\n          </el-form-item>\r\n          <el-form-item\r\n            label=\"要求完成时间\"\r\n            prop=\"Finish_Date\"\r\n            :rules=\"{ required: true, message: '请选择', trigger: 'change' }\"\r\n          >\r\n            <span v-if=\"isView\">{{ formInline.Finish_Date | timeFormat }}</span>\r\n            <el-date-picker\r\n              v-else\r\n              v-model=\"formInline.Finish_Date\"\r\n              :picker-options=\"pickerOptions\"\r\n              :disabled=\"isView\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              type=\"date\"\r\n              placeholder=\"选择日期\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"!isNest && !isVersionFour\" label=\"批次\" prop=\"Create_UserName\">\r\n            <span v-if=\"isView\">{{ installName }}</span>\r\n            <el-select\r\n              v-else\r\n              v-model=\"formInline.InstallUnit_Id\"\r\n              :disabled=\"!isAdd\"\r\n              filterable\r\n              placeholder=\"请选择\"\r\n              @change=\"installChange\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in installUnitIdList\"\r\n                :key=\"item.Id\"\r\n                :label=\"item.Name\"\r\n                :value=\"item.Id\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"备注\" prop=\"Remark\">\r\n            <span v-if=\"isView\">{{ formInline.Remark }}</span>\r\n            <el-input\r\n              v-else\r\n              v-model=\"formInline.Remark\"\r\n              :disabled=\"isView\"\r\n              style=\"width: 320px\"\r\n              placeholder=\"请输入\"\r\n            />\r\n          </el-form-item>\r\n\r\n        </el-form>\r\n        <el-divider class=\"elDivder\" />\r\n        <div class=\"btn-x\">\r\n          <div v-if=\"!isView\">\r\n            <div ref=\"searchDom\" class=\"search-container\">\r\n              <el-form ref=\"searchForm\" :model=\"innerForm\" inline>\r\n                <el-form-item label-width=\"80px\" prop=\"searchContent\" :label=\"`${comName}名称` \">\r\n                  <el-input\r\n                    v-model=\"innerForm.searchContent\"\r\n                    clearable\r\n                    class=\"input-with-select\"\r\n                    placeholder=\"请输入(空格区分/多个搜索)\"\r\n                    size=\"small\"\r\n                  >\r\n                    <el-select\r\n                      slot=\"prepend\"\r\n                      v-model=\"curSearch\"\r\n                      placeholder=\"请选择\"\r\n                      style=\"width: 100px\"\r\n                    >\r\n                      <el-option label=\"精准查询\" :value=\"1\" />\r\n                      <el-option label=\"模糊查询\" :value=\"0\" />\r\n                    </el-select>\r\n                  </el-input>\r\n                </el-form-item>\r\n\r\n                <el-form-item label-width=\"80px\" :label=\"isCom?`${comName}类型`:'零件类型'\" prop=\"searchComTypeSearch\">\r\n                  <el-tree-select\r\n                    v-if=\"$route.query.status!=='view'\"\r\n                    ref=\"treeSelectComponentType\"\r\n                    v-model=\"innerForm.searchComTypeSearch\"\r\n                    placeholder=\"请选择\"\r\n                    :select-params=\"treeSelectParams\"\r\n                    class=\"cs-tree-x\"\r\n                    :tree-params=\"treeParamsComponentType\"\r\n                    @searchFun=\"componentTypeFilter\"\r\n                  />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"规格\" label-width=\"50px\" prop=\"searchSpecSearch\">\r\n                  <el-input v-model=\"innerForm.searchSpecSearch\" placeholder=\"请输入\" clearable=\"\" />\r\n                </el-form-item>\r\n                <el-form-item v-if=\"isCom\" label=\"是否直发件\" prop=\"searchDirect\">\r\n                  <el-select v-model=\"innerForm.searchDirect\" placeholder=\"请选择\" clearable style=\"width: 120px\">\r\n                    <el-option label=\"是\" :value=\"true\" />\r\n                    <el-option label=\"否\" :value=\"false\" />\r\n                  </el-select>\r\n                </el-form-item>\r\n\r\n                <el-form-item>\r\n                  <el-button type=\"primary\" @click=\"innerFilter\">搜索</el-button>\r\n                  <el-button @click=\"resetInnerForm\">重置</el-button>\r\n                </el-form-item>\r\n              </el-form>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <vxe-toolbar\r\n          ref=\"xToolbar1\"\r\n        >\r\n          <template #buttons>\r\n            <template v-if=\"!isView\">\r\n              <el-button v-if=\"!isNest\" type=\"primary\" :disabled=\"disabledAdd\" @click=\"handleAddDialog()\">添加</el-button>\r\n              <el-button\r\n                v-if=\"workshopEnabled\"\r\n                :disabled=\"!multipleSelection.length\"\r\n                @click=\"handleBatchWorkshop(1)\"\r\n              >分配车间\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"!isCom\"\r\n                :disabled=\"!multipleSelection.length\"\r\n                @click=\"handleSelectMenu('process')\"\r\n              >分配工序\r\n              </el-button>\r\n              <el-dropdown v-if=\"isCom\" style=\"margin:0 10px\" @command=\"handleSelectMenu\">\r\n                <el-button :disabled=\"!multipleSelection.length\" type=\"primary\" plain>\r\n                  分配工序<i class=\"el-icon-arrow-down el-icon--right\" />\r\n                </el-button>\r\n                <el-dropdown-menu slot=\"dropdown\">\r\n                  <el-dropdown-item\r\n                    command=\"process\"\r\n                  >批量分配工序\r\n                  </el-dropdown-item>\r\n                  <el-dropdown-item\r\n                    v-if=\"isCom\"\r\n                    command=\"deal\"\r\n                  >{{ comName }}类型自动分配\r\n                  </el-dropdown-item>\r\n                  <el-dropdown-item\r\n                    v-if=\"isVersionFour\"\r\n                    command=\"craft\"\r\n                  >工艺代码分配\r\n                  </el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </el-dropdown>\r\n              <el-button\r\n                v-if=\"!isCom && !isOwnerNull\"\r\n                :disabled=\"!multipleSelection.length\"\r\n                @click=\"handleBatchOwner(1)\"\r\n              >批量分配领用工序\r\n              </el-button>\r\n              <el-button\r\n                plain\r\n                :disabled=\"!tbData.length\"\r\n                :loading=\"false\"\r\n                @click=\"handleReverse\"\r\n              >反选\r\n              </el-button>\r\n              <el-button\r\n                type=\"danger\"\r\n                plain\r\n                :loading=\"deleteLoading\"\r\n                :disabled=\"!multipleSelection.length\"\r\n                @click=\"handleDelete\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n            <template v-else>\r\n              <el-button :disabled=\"!tbData.length\" @click=\"handleExport\">导出</el-button>\r\n            </template>\r\n          </template>\r\n          <template #tools>\r\n            <DynamicTableFields\r\n              title=\"表格配置\"\r\n              :table-config-code=\"gridCode\"\r\n              @updateColumn=\"changeColumn\"\r\n            />\r\n          </template>\r\n        </vxe-toolbar>\r\n\r\n        <div class=\"tb-x\">\r\n          <!--          activeMethod: activeCellMethod,-->\r\n          <vxe-table\r\n            ref=\"xTable\"\r\n            :key=\"tbKey\"\r\n            :empty-render=\"{name: 'NotData'}\"\r\n            show-header-overflow\r\n            :checkbox-config=\"{checkField: 'checked'}\"\r\n            class=\"cs-vxe-table\"\r\n            :row-config=\"{isCurrent: true, isHover: true}\"\r\n            align=\"left\"\r\n            height=\"100%\"\r\n            :filter-config=\"{showIcon:false}\"\r\n            show-overflow\r\n            :loading=\"tbLoading\"\r\n            stripe\r\n            :scroll-y=\"{enabled: true, gt: 20}\"\r\n            size=\"medium\"\r\n            :edit-config=\"{\r\n              trigger: 'click',\r\n              mode: 'cell',\r\n              showIcon: !isView,\r\n\r\n            }\"\r\n            :data=\"tbData\"\r\n            resizable\r\n            :tooltip-config=\"{ enterable: true }\"\r\n            @checkbox-all=\"tbSelectChange\"\r\n            @checkbox-change=\"tbSelectChange\"\r\n          >\r\n            <vxe-column v-if=\"!isView\" fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n            <template v-for=\"item in columns\">\r\n              <vxe-column\r\n                v-if=\"item.Code === 'Is_Component'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :filters=\"isComponentOptions\"\r\n                :filter-method=\"filterComponentMethod\"\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <el-tag\r\n                    :type=\"row.Is_Component ? 'danger' : 'success'\"\r\n                  >{{ row.Is_Component ? '否' : '是' }}\r\n                  </el-tag>\r\n                </template>\r\n              </vxe-column>\r\n\r\n              <vxe-column\r\n                v-else-if=\"['Type','Type_Name'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :filter-method=\"filterTypeMethod\"\r\n                :field=\"item.Code\"\r\n                :filters=\"filterTypeOption\"\r\n                :title=\"item.Display_Name\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <input v-for=\"(option, index) in column.filters\" :key=\"index\" v-model=\"option.data\" type=\"type\" @input=\"$panel.changeOption($event, !!option.data, option)\">\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row[item.Code] | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"['Comp_Code','Part_Code'].includes(item.Code)\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :filter-method=\"filterCodeMethod\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"filterCodeOption\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <input v-for=\"(option, index) in column.filters\" :key=\"index\" v-model=\"option.data\" type=\"type\" @input=\"$panel.changeOption($event, !!option.data, option)\">\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  <el-tag v-if=\"row.Is_Change\" style=\"margin: 8px;\" type=\"danger\">变</el-tag>\r\n                  <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                  <!--                  <el-link v-if=\"row.DwgCount>0\" type=\"primary\" @click.stop=\"handleDwg(row)\"> {{  row[item.Code]  | displayValue }}</el-link>-->\r\n                  <span>{{ row[item.Code] | displayValue }}</span>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Spec'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :filters=\"specOptions\"\r\n                :filter-method=\"filterSpecMethod\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #filter=\"{ $panel, column }\">\r\n                  <input v-for=\"(option, index) in column.filters\" :key=\"index\" v-model=\"option.data\" type=\"type\" @input=\"$panel.changeOption($event, !!option.data, option)\">\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Spec | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Schduled_Weight'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                sortable\r\n                :width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  {{ (row.Schduled_Count * row.Weight).toFixed(2)/1 }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Technology_Path'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :field=\"item.Code\"\r\n                :show-overflow=\"false\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <div class=\"cs-column-row\">\r\n                    <div class=\"cs-ell\">\r\n                      <el-tooltip class=\"item\" effect=\"dark\" :content=\"row.Technology_Path\" placement=\"top\">\r\n                        <span>{{ row.Technology_Path | displayValue }}</span>\r\n                      </el-tooltip>\r\n                    </div>\r\n                    <i\r\n                      v-if=\"!isView\"\r\n                      class=\"el-icon-edit\"\r\n                      @click=\"openBPADialog(2, row)\"\r\n                    />\r\n                  </div>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Part_Used_Process'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :show-overflow=\"false\"\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <div class=\"cs-column-row\">\r\n                    <div class=\"cs-ell\">\r\n                      <el-tooltip class=\"item\" effect=\"dark\" :content=\"row.Part_Used_Process\" placement=\"top\">\r\n                        <span>{{ row.Part_Used_Process | displayValue }}</span>\r\n                      </el-tooltip>\r\n                    </div>\r\n                    <i\r\n                      v-if=\"showPartUsedProcess(row)\"\r\n                      class=\"el-icon-edit\"\r\n                      @click=\"handleBatchOwner(2, row)\"\r\n                    />\r\n                  </div>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Workshop_Name'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :show-overflow=\"false\"\r\n                :field=\"item.Code\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <div class=\"cs-column-row\">\r\n                    <div class=\"cs-ell\">\r\n                      <el-tooltip class=\"item\" effect=\"dark\" :content=\"row.Workshop_Name\" placement=\"top\">\r\n                        <span>{{ row.Workshop_Name | displayValue }}</span>\r\n                      </el-tooltip>\r\n                    </div>\r\n                    <i\r\n                      v-if=\"!isView\"\r\n                      class=\"el-icon-edit\"\r\n                      @click=\"handleBatchWorkshop(2, row)\"\r\n                    />\r\n                  </div>\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else-if=\"item.Code === 'Schduled_Count'\"\r\n                :key=\"item.Code\"\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                sortable\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                :edit-render=\"{enabled:!isView}\"\r\n                :min-width=\"item.Width\"\r\n              >\r\n                <template #edit=\"{ row }\">\r\n                  <vxe-input\r\n                    v-model.number=\"row.Schduled_Count\"\r\n                    type=\"integer\"\r\n                    min=\"0\"\r\n                    :max=\"row.chooseCount\"\r\n                  />\r\n                </template>\r\n                <template #default=\"{ row }\">\r\n                  {{ row.Schduled_Count | displayValue }}\r\n                </template>\r\n              </vxe-column>\r\n              <vxe-column\r\n                v-else\r\n                :key=\"item.Id\"\r\n                :align=\"item.Align\"\r\n                :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n                show-overflow=\"tooltip\"\r\n                sortable\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                :min-width=\"item.Width\"\r\n              />\r\n            </template>\r\n\r\n          </vxe-table>\r\n        </div>\r\n        <el-divider v-if=\"!isView\" class=\"elDivder\" />\r\n        <footer v-if=\"!isView\">\r\n          <div class=\"data-info\">\r\n            <el-tag\r\n              size=\"medium\"\r\n              class=\"info-x\"\r\n            >已选 {{ multipleSelection.length }} 条数据\r\n            </el-tag>\r\n            <el-tag v-if=\"tipLabel\" size=\"medium\" class=\"info-x\">{{\r\n              tipLabel\r\n            }}\r\n            </el-tag>\r\n          </div>\r\n          <div>\r\n            <el-button v-if=\"workshopEnabled&&!isNest \" type=\"primary\" @click=\"saveWorkShop\">保存车间分配</el-button>\r\n            <el-button\r\n              v-if=\"!isNest\"\r\n              type=\"primary\"\r\n              :loading=\"saveLoading\"\r\n              @click=\"saveDraft(false)\"\r\n            >保存草稿\r\n            </el-button>\r\n            <el-button :disabled=\"deleteLoading || tbData.some(item=>item.stopFlag)\" @click=\"handleSubmit\">下发任务</el-button>\r\n          </div>\r\n        </footer>\r\n      </el-card>\r\n    </div>\r\n\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"dWidth\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :is-nest=\"isNest\"\r\n        :is-version-four=\"isVersionFour\"\r\n        :is-part-prepare=\"isPartPrepare\"\r\n        :process-list=\"processList\"\r\n        :page-type=\"pageType\"\r\n        :part-type-option=\"typeOption\"\r\n        :level-code=\"levelCode\"\r\n        @close=\"handleClose\"\r\n        @sendProcess=\"sendProcess\"\r\n        @workShop=\"getWorkShop\"\r\n        @refresh=\"fetchData\"\r\n        @setProcessList=\"setProcessList\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      :key=\"addDraftKey\"\r\n      v-dialogDrag\r\n      class=\"plm-custom-dialog\"\r\n      :title=\"title\"\r\n      :visible.sync=\"openAddDraft\"\r\n      :width=\"dWidth\"\r\n      top=\"10vh\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <add-draft\r\n        ref=\"draft\"\r\n        :level-code=\"levelCode\"\r\n        :com-name=\"comName\"\r\n        :current-ids=\"currentIds\"\r\n        :is-part-prepare=\"isPartPrepare\"\r\n        :area-id=\"areaId\"\r\n        :install-id=\"formInline.InstallUnit_Id\"\r\n        :schedule-id=\"scheduleId\"\r\n        :show-dialog=\"openAddDraft\"\r\n        :page-type=\"pageType\"\r\n        @sendSelectList=\"mergeSelectList\"\r\n        @close=\"handleClose\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { closeTagView, debounce } from '@/utils'\r\nimport BatchProcessAdjust from './components/BatchProcessAdjust'\r\nimport {\r\n  GetCanSchdulingPartList,\r\n  GetCompSchdulingInfoDetail,\r\n  GetDwg,\r\n  GetPartSchdulingInfoDetail,\r\n  GetSchdulingWorkingTeams,\r\n  SaveComponentSchedulingWorkshop,\r\n  SaveCompSchdulingDraft,\r\n  SavePartSchdulingDraftNew,\r\n  SavePartSchedulingWorkshopNew,\r\n  SaveSchdulingTaskById\r\n} from '@/api/PRO/production-task'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nimport AddDraft from './components/addDraft'\r\nimport OwnerProcess from './components/OwnerProcess'\r\nimport Workshop from './components/Workshop.vue'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { uniqueCode } from './constant'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport numeral from 'numeral'\r\nimport { GetLibListType, GetProcessFlowListWithTechnology, GetProcessListBase } from '@/api/PRO/technology-lib'\r\nimport { AreaGetEntity } from '@/api/plm/projects'\r\nimport { mapActions, mapGetters } from 'vuex'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\nimport moment from 'moment'\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport TreeDetail from '@/components/TreeDetail/index.vue'\r\nimport { GetInstallUnitIdNameList, GetProjectAreaTreeList } from '@/api/PRO/project'\r\n\r\nimport { GetCompTypeTree } from '@/api/PRO/factorycheck'\r\nimport { parseOssUrl } from '@/utils/file'\r\nimport DynamicTableFields from '@/components/DynamicTableFields/index.vue'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\n\r\nconst SPLIT_SYMBOL = '$_$'\r\nexport default {\r\n  components: { DynamicTableFields, TreeDetail, ExpandableSection, BatchProcessAdjust, AddDraft, Workshop, OwnerProcess },\r\n  data() {\r\n    return {\r\n      bomList: [],\r\n      comName: '',\r\n      levelCode: '',\r\n      isComponentOptions: [\r\n        { label: '是', value: false },\r\n        { label: '否', value: true }\r\n      ],\r\n      specOptions: [{ data: '' }],\r\n      filterTypeOption: [{ data: '' }],\r\n      filterCodeOption: [{ data: '' }],\r\n      pickerOptions: {\r\n        disabledDate(time) {\r\n        }\r\n      },\r\n      innerForm: {\r\n        searchContent: '',\r\n        searchComTypeSearch: '',\r\n        searchSpecSearch: '',\r\n        searchDirect: ''\r\n      },\r\n      curSearch: 1,\r\n      searchType: '',\r\n      formInline: {\r\n        Schduling_Code: '',\r\n        Create_UserName: '',\r\n        Finish_Date: '',\r\n        InstallUnit_Id: '',\r\n        Remark: ''\r\n      },\r\n      total: 0,\r\n      currentIds: '',\r\n      gridCode: '',\r\n      columns: [],\r\n      tbData: [],\r\n      tbConfig: {},\r\n      TotalCount: 0,\r\n      multipleSelection: [],\r\n      showExpand: true,\r\n      pgLoading: false,\r\n      deleteLoading: false,\r\n      workShopIsOpen: false,\r\n      isOwnerNull: false,\r\n      dialogVisible: false,\r\n      openAddDraft: false,\r\n      saveLoading: false,\r\n      tbLoading: false,\r\n      isCheckAll: false,\r\n      currentComponent: '',\r\n      dWidth: '25%',\r\n      title: '',\r\n      tbKey: 100,\r\n      search: () => ({}),\r\n      pageType: undefined,\r\n      tipLabel: '',\r\n      technologyOption: [],\r\n      typeOption: [],\r\n      workingTeam: [],\r\n      pageStatus: undefined,\r\n      scheduleId: '',\r\n      partComOwnerColumn: null,\r\n\r\n      installUnitIdList: [],\r\n      projectId: '',\r\n      areaId: '',\r\n      projectName: '',\r\n      statusType: '可排产',\r\n      expandedKey: '',\r\n      treeLoading: false,\r\n      treeData: [],\r\n      treeParamsComponentType: {\r\n        'default-expand-all': true,\r\n        'check-strictly': true,\r\n        filterable: true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      },\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        collapseTags: true,\r\n        clearable: true\r\n      },\r\n      disabledAdd: true\r\n    }\r\n  },\r\n  watch: {\r\n    'tbData.length': {\r\n      handler(n, o) {\r\n        this.checkOwner()\r\n      },\r\n      immediate: false\r\n    }\r\n\r\n  },\r\n\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    },\r\n    isView() {\r\n      return this.pageStatus === 'view'\r\n    },\r\n    isEdit() {\r\n      return this.pageStatus === 'edit'\r\n    },\r\n    isAdd() {\r\n      return this.pageStatus === 'add'\r\n    },\r\n    addDraftKey() {\r\n      return this.expandedKey + this.formInline.InstallUnit_Id\r\n    },\r\n    filterText() {\r\n      return this.projectName + SPLIT_SYMBOL + this.statusType\r\n    },\r\n    statusCode() {\r\n      return this.isCom ? 'Comp_Schdule_Status' : 'Part_Schdule_Status'\r\n    },\r\n    installName() {\r\n      const item = this.installUnitIdList.find(v => v.Id === this.formInline.InstallUnit_Id)\r\n      if (item) {\r\n        return item.Name\r\n      } else {\r\n        return ''\r\n      }\r\n    },\r\n    isPartPrepare() {\r\n      return this.getIsPartPrepare && !this.isCom\r\n    },\r\n    isNest() {\r\n      return this.$route.query.type === '1'\r\n    },\r\n    ...mapGetters('factoryInfo', ['workshopEnabled', 'getIsPartPrepare']),\r\n    ...mapGetters('schedule', ['processList', 'nestIds']),\r\n    ...mapGetters('tenant', ['isVersionFour'])\r\n  },\r\n  async mounted() {\r\n    const { list, comName, currentBOMInfo } = await GetBOMInfo(-1)\r\n    this.bomList = list || []\r\n    this.comName = comName\r\n    this.levelCode = currentBOMInfo?.Code\r\n    console.log('currentBOMInfo', currentBOMInfo)\r\n    console.log('levelCode', this.levelCode)\r\n    this.initProcessList()\r\n    this.tbDataMap = {}\r\n    this.craftCodeMap = {}\r\n    this.pageType = this.$route.query.pg_type\r\n    this.pageStatus = this.$route.query.status\r\n    this.model = this.$route.query.model\r\n    this.scheduleId = this.$route.query.pid || ''\r\n    // // this.formInline.Create_UserName = this.$store.getters.name\r\n    // // 框架问题引起store数据丢失，已反馈，结果：此处先使用localStorage\r\n    this.formInline.Create_UserName = localStorage.getItem('UserAccount')\r\n    // if (!this.isCom) {\r\n    //   this.getPartType()\r\n    // } else {\r\n    // }\r\n\r\n    this.unique = uniqueCode()\r\n    this.checkWorkshopIsOpen()\r\n\r\n    this.search = debounce(this.fetchData, 800, true)\r\n    await this.mergeConfig()\r\n    if (this.isView || this.isEdit) {\r\n      const { areaId, install } = this.$route.query\r\n      this.areaId = areaId\r\n      this.formInline.InstallUnit_Id = install\r\n      this.getInstallUnitIdNameList()\r\n      this.fetchData()\r\n    }\r\n\r\n    if (this.isAdd) {\r\n      this.fetchTreeData()\r\n      this.getType()\r\n    }\r\n    if (this.isEdit) {\r\n      this.getType()\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions('schedule', ['changeProcessList', 'initProcessList']),\r\n    checkOwner() {\r\n      if (this.isCom) return\r\n      this.isOwnerNull = this.tbData.every(v => !v.Comp_Import_Detail_Id) && !this.isNest\r\n      const idx = this.columns.findIndex(v => v.Code === 'Part_Used_Process')\r\n      if (this.isOwnerNull) {\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n      } else {\r\n        if (idx === -1) {\r\n          if (!this.ownerColumn) {\r\n            this.$message({\r\n              message: '列表配置字段缺少零件领用工序字段',\r\n              type: 'success'\r\n            })\r\n            return\r\n          }\r\n          this.columns.push(this.ownerColumn)\r\n        }\r\n        this.comPart = true\r\n      }\r\n    },\r\n    async mergeConfig() {\r\n      await this.getConfig()\r\n      await this.getWorkTeam()\r\n    },\r\n    async getConfig() {\r\n      let configCode = ''\r\n      if (this.isNest) {\r\n        if (this.isView) {\r\n          configCode = 'PRONestingScheduleDetail'\r\n        } else {\r\n          configCode = 'PRONestingScheduleConfig'\r\n        }\r\n      } else {\r\n        configCode = (this.isView ? 'PROComViewPageTbConfig' : 'PROComDraftPageTbConfig')\r\n      }\r\n      this.gridCode = configCode\r\n      await this.getTableConfig(configCode)\r\n      if (!this.workshopEnabled) {\r\n        this.columns = this.columns.filter(v => v.Code !== 'Workshop_Name')\r\n      }\r\n      if (!this.isVersionFour) {\r\n        this.columns = this.columns.filter(v => v.Code !== 'Technology_Code')\r\n      }\r\n      this.checkOwner()\r\n    },\r\n    async changeColumn() {\r\n      await this.getTableConfig(this.gridCode)\r\n      this.tbKey++\r\n    },\r\n    handleNodeClick(data) {\r\n      this.expandedKey = data.Id\r\n      if (this.areaId === data.Id) {\r\n        this.disabledAdd = false\r\n        return\r\n      }\r\n      this.disabledAdd = true\r\n      if (!data.ParentNodes || data.Children?.length > 0) {\r\n        return\r\n      }\r\n      if (data?.Data[this.statusCode] === '未导入') {\r\n        this.$message({\r\n          message: '清单未导入，请联系深化人员导入清单',\r\n          type: 'warning'\r\n        })\r\n\r\n        return\r\n      }\r\n\r\n      const initData = ({ Data }) => {\r\n        this.areaId = Data.Id\r\n        this.projectId = Data.Project_Id\r\n        this.expandedKey = this.areaId\r\n        this.formInline.Finish_Date = ''\r\n        this.formInline.InstallUnit_Id = ''\r\n        this.formInline.Remark = ''\r\n        this.tbData = []\r\n        this.getAreaInfo()\r\n        this.getInstallUnitIdNameList()\r\n      }\r\n\r\n      if (this.tbData.length) {\r\n        this.$confirm('切换区域右侧数据清空，是否确认?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          initData(data)\r\n          this.disabledAdd = false\r\n          this.tbDataMap = {}\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n      } else {\r\n        this.disabledAdd = false\r\n        initData(data)\r\n      }\r\n    },\r\n\r\n    customFilterFun(value, data, node) {\r\n      // console.log('customFilterFun', value, data, node)\r\n\r\n      const arr = value.split(SPLIT_SYMBOL)\r\n      const labelVal = arr[0]\r\n      const statusVal = arr[1]\r\n      if (!value) return true\r\n      let parentNode = node.parent\r\n      let labels = [node.label]\r\n      let status = [data.Data[this.statusCode]]\r\n      let level = 1\r\n      while (level < node.level) {\r\n        labels = [...labels, parentNode.label]\r\n        status = [...status, data.Data[this.statusCode]]\r\n        parentNode = parentNode.parent\r\n        level++\r\n      }\r\n      labels = labels.filter(v => !!v)\r\n      status = status.filter(v => !!v)\r\n      let resultLabel = true\r\n      let resultStatus = true\r\n      if (this.statusType) {\r\n        resultStatus = status.some(s => s.indexOf(statusVal) !== -1)\r\n      }\r\n      if (this.projectName) {\r\n        resultLabel = labels.some(s => s.indexOf(labelVal) !== -1)\r\n      }\r\n      return resultLabel && resultStatus\r\n    },\r\n    async fetchData() {\r\n      this.tbLoading = true\r\n      let resData = null\r\n      if (this.isNest) {\r\n        if (this.isView) {\r\n          resData = await this.getPartPageList()\r\n        } else {\r\n          resData = await this.getNestPageList()\r\n        }\r\n      } else {\r\n        resData = await this.getComPageList()\r\n        console.log('resData', resData)\r\n      }\r\n\r\n      this.initTbData(resData)\r\n      this.tbLoading = false\r\n    },\r\n    fetchTreeDataLocal() {\r\n      // this.filterText = this.projectName\r\n    },\r\n    fetchTreeStatus() {\r\n      // this.filterText = this.statusType\r\n    },\r\n    fetchTreeData() {\r\n      this.treeLoading = true\r\n      console.log('78,this.$route.meta', this.$route, this.$router)\r\n      GetProjectAreaTreeList({ MenuId: this.$route.meta.Id, projectName: this.projectName, Type: this.isCom ? 1 : 2 }).then((res) => {\r\n        if (!res.IsSucceed) {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.treeData = []\r\n          this.treeLoading = false\r\n          return\r\n        }\r\n        if (res.Data.length === 0) {\r\n          this.treeLoading = false\r\n          this.treeData = []\r\n          return\r\n        }\r\n        const resData = res.Data.map(item => {\r\n          item.Is_Directory = true\r\n          return item\r\n        })\r\n        this.treeData = resData\r\n        this.$nextTick(_ => {\r\n          this.$refs.tree.filterRef(this.filterText)\r\n          const result = this.setKey()\r\n          if (!result) {\r\n            this.pgLoading = false\r\n          }\r\n        })\r\n        this.treeLoading = false\r\n      }).catch((e) => {\r\n        console.log('catche', e)\r\n        this.treeLoading = false\r\n        this.treeData = []\r\n      })\r\n    },\r\n    setKey() {\r\n      const deepFilter = (tree) => {\r\n        for (let i = 0; i < tree.length; i++) {\r\n          const item = tree[i]\r\n          const { Data, Children } = item\r\n          const node = getNode(Data.Id)\r\n          if (Data.ParentId && !Children?.length && node.visible) {\r\n            this.handleNodeClick(item)\r\n            return true\r\n          } else {\r\n            if (Children?.length) {\r\n              const shouldStop = deepFilter(Children)\r\n              if (shouldStop) return true\r\n            }\r\n          }\r\n        }\r\n        return false\r\n      }\r\n      const getNode = (key) => {\r\n        return this.$refs['tree'].getNodeByKey(key)\r\n      }\r\n      return deepFilter(this.treeData)\r\n    },\r\n    closeView() {\r\n      closeTagView(this.$store, this.$route)\r\n    },\r\n    checkWorkshopIsOpen() {\r\n      this.workShopIsOpen = true\r\n    },\r\n    tbSelectChange(array) {\r\n      this.multipleSelection = array.records\r\n    },\r\n    getAreaInfo() {\r\n      this.formInline.Finish_Date = ''\r\n      AreaGetEntity({\r\n        id: this.areaId\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (!res.Data) {\r\n            return []\r\n          }\r\n\r\n          const start = moment(res.Data?.Demand_Begin_Date)\r\n          const end = moment(res.Data?.Demand_End_Date)\r\n          this.pickerOptions.disabledDate = (time) => {\r\n            return time.getTime() < start || time.getTime() > end\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n      this.openAddDraft = false\r\n    },\r\n    getNestPageList() {\r\n      return new Promise((resolve, reject) => {\r\n        GetCanSchdulingPartList({\r\n          Ids: this.nestIds\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            const _list = res?.Data || []\r\n            const list = _list.map(v => {\r\n              if (v.Scheduled_Used_Process) {\r\n                // 已存在操作过数据\r\n                v.Part_Used_Process = v.Scheduled_Used_Process\r\n              }\r\n              // v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n              v.Workshop_Id = v.Scheduled_Workshop_Id\r\n              v.Workshop_Name = v.Scheduled_Workshop_Name\r\n              v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n              v.chooseCount = v.Can_Schduling_Count\r\n              return v\r\n            })\r\n\r\n            resolve(list)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    async getComPageList() {\r\n      const {\r\n        pid\r\n      } = this.$route.query\r\n      const result = await GetCompSchdulingInfoDetail({\r\n        Schduling_Plan_Id: pid\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const { Schduling_Plan, Schduling_Comps, Process_List } = res.Data\r\n          this.formInline = Object.assign(this.formInline, Schduling_Plan)\r\n          Process_List.forEach(item => {\r\n            const plist = {\r\n              key: item.Process_Code,\r\n              value: item\r\n            }\r\n            this.changeProcessList(plist)\r\n          })\r\n          const list = Schduling_Comps.map(v => {\r\n            v.chooseCount = v.Can_Schduling_Count\r\n            return v\r\n          })\r\n          this.getStopList(list)\r\n          return list || []\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      return result || []\r\n    },\r\n    async getStopList(list) {\r\n      console.log('getStopList', list)\r\n      const submitObj = list.map(item => {\r\n        return {\r\n          Id: item.Comp_Import_Detail_Id,\r\n          Type: 2\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = !!item.Is_Stop\r\n          })\r\n          list.forEach(row => {\r\n            if (stopMap[row.Comp_Import_Detail_Id]) {\r\n              this.$set(row, 'stopFlag', stopMap[row.Comp_Import_Detail_Id])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getPartPageList() {\r\n      return new Promise((resolve, reject) => {\r\n        const {\r\n          pid\r\n        } = this.$route.query\r\n        GetPartSchdulingInfoDetail({\r\n          Schduling_Plan_Id: pid\r\n        }).then((res) => {\r\n          if (res.IsSucceed) {\r\n            const SarePartsModel = res.Data?.SarePartsModel.map(v => {\r\n              if (v.Scheduled_Used_Process) {\r\n                // 已存在操作过数据\r\n                v.Part_Used_Process = v.Scheduled_Used_Process\r\n              }\r\n              v.chooseCount = v.Can_Schduling_Count\r\n              return v\r\n            })\r\n            this.formInline = Object.assign(this.formInline, res.Data?.Schduling_Plan)\r\n            resolve(SarePartsModel || [])\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    initTbData(list, teamKey = 'Allocation_Teams') {\r\n      this.tbData = list.map(row => {\r\n        const processList = row.Technology_Path?.split('/') || []\r\n        row.uuid = uuidv4()\r\n        this.addElementToTbData(row)\r\n        if (row[teamKey]) {\r\n          const newData = row[teamKey].filter((r) => processList.findIndex((p) => r.Process_Code === p) !== -1)\r\n          newData.forEach((ele, index) => {\r\n            const code = this.getRowUnique(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n            const max = this.getRowUniqueMax(row.uuid, ele.Process_Code, ele.Working_Team_Id)\r\n            row[code] = ele.Count\r\n            row[max] = 0\r\n          })\r\n        }\r\n        this.setInputMax(row)\r\n        return row\r\n      })\r\n      let ids = ''\r\n      if (this.isCom) {\r\n        ids = this.tbData.map(v => v.Comp_Import_Detail_Id).toString()\r\n      } else {\r\n        ids = this.tbData.map(v => v.Part_Aggregate_Id).toString()\r\n      }\r\n      this.currentIds = ids\r\n    },\r\n    async mergeSelectList(newList) {\r\n      if (this.isVersionFour) {\r\n        await this.mergeCraftProcess(newList)\r\n      }\r\n      console.time('mergeSelectListTime')\r\n      let hasUsedPartFlag = true\r\n      newList.forEach((element, index) => {\r\n        const cur = this.getMergeUniqueRow(element)\r\n        if (!cur) {\r\n          element.puuid = element.uuid\r\n          element.Schduled_Count = element.chooseCount\r\n          element.Schduled_Weight = numeral(element.chooseCount * element.Weight).format('0.[00]')\r\n          if (this.isVersionFour && !element.Technology_Path) {\r\n            /*          if (this.craftCodeMap[element.Technology_Code] && this.craftCodeMap[element.Technology_Code] instanceof Array) {\r\n              const curPathArr = this.craftCodeMap[element.Technology_Code]\r\n              if (element.Part_Used_Process && !curPathArr.includes(element.Part_Used_Process)) {\r\n                hasUsedPartFlag = false\r\n              } else {\r\n                element.Technology_Path = curPathArr.join('/')\r\n              }\r\n            }*/\r\n            if (this.craftCodeMap[element.Technology_Code] instanceof Array) {\r\n              const curPathArr = this.craftCodeMap[element.Technology_Code]\r\n              if (element.Part_Used_Process) {\r\n                const partUsedProcessArr = element.Part_Used_Process.split(',')\r\n                const allPartsIncluded = partUsedProcessArr.every(part => curPathArr.includes(part))\r\n\r\n                if (!allPartsIncluded) {\r\n                  hasUsedPartFlag = false\r\n                } else {\r\n                  element.Technology_Path = curPathArr.join('/')\r\n                }\r\n              } else {\r\n                element.Technology_Path = curPathArr.join('/')\r\n              }\r\n            }\r\n          }\r\n          this.tbData.push(element)\r\n          this.addElementToTbData(element)\r\n          return\r\n        }\r\n\r\n        cur.puuid = element.uuid\r\n\r\n        cur.Schduled_Count += element.chooseCount\r\n        cur.Schduled_Weight = numeral(cur.Schduled_Weight).add(element.chooseCount * element.Weight).format('0.[00]')\r\n        if (!cur.Technology_Path) {\r\n          return\r\n        }\r\n        this.setInputMax(cur)\r\n      })\r\n      this.showCraftUsedPartResult(hasUsedPartFlag)\r\n\r\n      // if (this.isCom) {\r\n      //   this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n      // } else {\r\n      //   this.tbData.sort((a, b) => a.Part_Code.localeCompare(b.Part_Code))\r\n      // }\r\n      this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n      console.timeEnd('mergeSelectListTime')\r\n    },\r\n    addElementToTbData(element) {\r\n      const key = this.getUniKey(element)\r\n      this.tbDataMap[key] = element\r\n    },\r\n    getMergeUniqueRow(element) {\r\n      const key = this.getUniKey(element)\r\n      return this.tbDataMap[key]\r\n    },\r\n    getUniKey(element) {\r\n      if (this.isVersionFour) {\r\n        return this.isCom ? (element.Comp_Code + element.InstallUnit_Name).toString().trim() : ((element.Component_Code ?? '') + element.Part_Code + element.Part_Aggregate_Id)\r\n      } else {\r\n        return this.isCom ? element.Comp_Code : ((element.Component_Code ?? '') + element.Part_Code + element.Part_Aggregate_Id)\r\n      }\r\n    },\r\n    checkForm() {\r\n      let isValidate = true\r\n      this.$refs['formInline'].validate((valid) => {\r\n        if (!valid) isValidate = false\r\n      })\r\n      return isValidate\r\n    },\r\n    async saveDraft(isOrder = false) {\r\n      const checkSuccess = this.checkForm()\r\n      if (!checkSuccess) return false\r\n      const { tableData, status } = this.getSubmitTbInfo()\r\n      if (!status) return false\r\n      if (!isOrder) {\r\n        this.saveLoading = true\r\n      }\r\n\r\n      const isSuccess = await this.handleSaveDraft(tableData, isOrder)\r\n      console.log('isSuccess', isSuccess)\r\n      if (!isSuccess) return false\r\n      if (isOrder) return isSuccess\r\n      this.$refs['draft']?.fetchData()\r\n      this.saveLoading = false\r\n    },\r\n    async saveWorkShop() {\r\n      const checkSuccess = this.checkForm()\r\n      if (!checkSuccess) return false\r\n      const obj = {}\r\n      if (!this.tbData.length) {\r\n        this.$message({\r\n          message: '数据不能为空',\r\n          type: 'success'\r\n        })\r\n        return\r\n      }\r\n      if (this.isCom) {\r\n        obj.Schduling_Comps = this.tbData\r\n      } else {\r\n        obj.SarePartsModel = this.tbData\r\n      }\r\n      if (this.isEdit) {\r\n        obj.Schduling_Plan = this.formInline\r\n      } else {\r\n        obj.Schduling_Plan = {\r\n          ...this.formInline,\r\n          Project_Id: this.projectId,\r\n          Area_Id: this.areaId,\r\n          Schduling_Model: this.model // 1构件单独排产，2零件单独排产，3构/零件一起排产\r\n        }\r\n      }\r\n      this.pgLoading = true\r\n      const _fun = this.isCom ? SaveComponentSchedulingWorkshop : SavePartSchedulingWorkshopNew\r\n      _fun(obj).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.pgLoading = false\r\n          this.$message({\r\n            message: '保存成功',\r\n            type: 'success'\r\n          })\r\n          this.closeView()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          this.pgLoading = false\r\n        }\r\n      })\r\n    },\r\n    getSubmitTbInfo() {\r\n      // 处理上传的数据\r\n      let tableData = JSON.parse(JSON.stringify(this.tbData))\r\n      tableData = tableData.filter(item => item.Schduled_Count > 0)\r\n      for (let i = 0; i < tableData.length; i++) {\r\n        const element = tableData[i]\r\n        let list = []\r\n        if (!element.Technology_Path) {\r\n          this.$message({\r\n            message: '工序不能为空',\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        if (this.isPartPrepare && !element.Part_Used_Process && element.Type !== 'Direct' && this.comPart) {\r\n          const msg = '领用工序不能为空'\r\n          if (this.isNest) {\r\n            if (element.Comp_Import_Detail_Id) {\r\n              this.$message({\r\n                message: msg,\r\n                type: 'warning'\r\n              })\r\n              return { status: false }\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: msg,\r\n              type: 'warning'\r\n            })\r\n            return { status: false }\r\n          }\r\n        }\r\n        // if (!this.isCom && element.Comp_Import_Detail_Id && !element.Part_Used_Process) {\r\n        //   // 零构件 零件单独排产\r\n        //   this.$message({\r\n        //     message: '零件领用工序不能为空',\r\n        //     type: 'warning'\r\n        //   })\r\n        //   return { status: false }\r\n        // }\r\n        if (element.Scheduled_Technology_Path && element.Scheduled_Technology_Path !== element.Technology_Path) {\r\n          this.$message({\r\n            message: `请和该区域批次下已排产同${this.isCom ? `${this.comName}` : '零件'}保持工序一致`,\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        if (element.Scheduled_Used_Process && element.Scheduled_Used_Process !== element.Part_Used_Process) {\r\n          this.$message({\r\n            message: `请和该区域批次下已排产同零件领用工序保持一致`,\r\n            type: 'warning'\r\n          })\r\n          return { status: false }\r\n        }\r\n        const processList = Array.from(new Set(element.Technology_Path.split('/')))\r\n        /* processList.forEach(code => {\r\n          const groups = this.workingTeam.filter(v => v.Process_Code === code)\r\n          const groupsList = groups.map(group => {\r\n            const uCode = this.getRowUnique(element.uuid, code, group.Working_Team_Id)\r\n            const uMax = this.getRowUniqueMax(element.uuid, code, group.Working_Team_Id)\r\n            const obj = {\r\n              Team_Task_Id: element.Team_Task_Id,\r\n              Comp_Code: element.Comp_Code,\r\n              Again_Count: +element[uCode] || 0, // 不填，后台让传0\r\n              Part_Code: this.isCom ? null : '',\r\n              Process_Code: code,\r\n              Technology_Path: element.Technology_Path,\r\n              Working_Team_Id: group.Working_Team_Id,\r\n              Working_Team_Name: group.Working_Team_Name\r\n            }\r\n            delete element[uCode]\r\n            delete element[uMax]\r\n            return obj\r\n          })\r\n          list.push(...groupsList)\r\n        })*/\r\n        for (let j = 0; j < processList.length; j++) {\r\n          const code = processList[j]\r\n          const schduledCount = element.Schduled_Count || 0\r\n          let groups = []\r\n          if (element.Allocation_Teams) {\r\n            groups = element.Allocation_Teams.filter(v => v.Process_Code === code)\r\n          }\r\n          const againCount = groups.reduce((acc, cur) => {\r\n            return acc + (cur.Again_Count || 0)\r\n          }, 0)\r\n          if (againCount > schduledCount) {\r\n            list = []\r\n            break\r\n          } else {\r\n            list.push(...groups)\r\n          }\r\n        }\r\n        const hasInput = Object.keys(element).filter(_ => _.startsWith(element['uuid']))\r\n        hasInput.forEach((item) => {\r\n          delete element[item]\r\n        })\r\n        delete element['uuid']\r\n        delete element['_X_ROW_KEY']\r\n        delete element['puuid']\r\n        element.Allocation_Teams = list\r\n      }\r\n      return { tableData, status: true }\r\n    },\r\n    async handleSaveDraft(tableData, isOrder) {\r\n      console.log('保存草稿')\r\n      const _fun = this.isCom ? SaveCompSchdulingDraft : SavePartSchdulingDraftNew\r\n      const obj = {}\r\n      if (this.isCom) {\r\n        obj.Schduling_Comps = tableData\r\n        const p = []\r\n        for (const objKey in this.processList) {\r\n          if (this.processList.hasOwnProperty(objKey)) {\r\n            p.push(this.processList[objKey])\r\n          }\r\n        }\r\n        obj.Process_List = p\r\n      } else {\r\n        obj.SarePartsModel = tableData\r\n      }\r\n      if (this.isEdit) {\r\n        obj.Schduling_Plan = this.formInline\r\n      } else {\r\n        obj.Schduling_Plan = {\r\n          ...this.formInline,\r\n          Project_Id: this.projectId,\r\n          Area_Id: this.areaId,\r\n          Schduling_Model: this.model // 1构件单独排产，2零件单独排产，3构/零件一起排产\r\n        }\r\n      }\r\n      let orderSuccess = false\r\n      console.log('obj', obj)\r\n\r\n      await _fun(obj).then(res => {\r\n        if (res.IsSucceed) {\r\n          if (!isOrder) {\r\n            this.pgLoading = false\r\n            this.$message({\r\n              message: '保存成功',\r\n              type: 'success'\r\n            })\r\n            this.closeView()\r\n          } else {\r\n            this.templateScheduleCode = res.Data\r\n            orderSuccess = true\r\n            console.log('保存草稿成功 ')\r\n          }\r\n        } else {\r\n          this.saveLoading = false\r\n          this.pgLoading = false\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n      console.log('结束 ')\r\n      return orderSuccess\r\n    },\r\n    handleDelete() {\r\n      this.deleteLoading = true\r\n      setTimeout(() => {\r\n        const selectedUuids = new Set(this.multipleSelection.map(v => v.uuid))\r\n        this.tbData = this.tbData.filter(item => {\r\n          const isSelected = selectedUuids.has(item.uuid)\r\n          if (isSelected) {\r\n            const key = this.getUniKey(item)\r\n            delete this.tbDataMap[key]\r\n          }\r\n          return !isSelected\r\n        })\r\n        this.$nextTick(_ => {\r\n          this.$refs['draft']?.mergeData(this.multipleSelection)\r\n          this.multipleSelection = []\r\n        })\r\n        this.deleteLoading = false\r\n      }, 0)\r\n    },\r\n    async getWorkTeam() {\r\n      await GetSchdulingWorkingTeams({\r\n        type: this.isCom ? 1 : 2\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.workingTeam = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      this.$refs['formInline'].validate((valid) => {\r\n        if (!valid) return\r\n        const { tableData, status } = this.getSubmitTbInfo()\r\n        if (!status) return\r\n        this.$confirm('是否提交当前数据?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.saveDraftDoSubmit(tableData)\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消'\r\n          })\r\n        })\r\n      })\r\n    },\r\n    async saveDraftDoSubmit() {\r\n      this.pgLoading = true\r\n      if (this.formInline?.Schduling_Code) {\r\n        const isSuccess = await this.saveDraft(true)\r\n        console.log('saveDraftDoSubmit', isSuccess)\r\n        isSuccess && this.doSubmit(this.formInline.Id)\r\n      } else {\r\n        const isSuccess = await this.saveDraft(true)\r\n        isSuccess && this.doSubmit(this.templateScheduleCode)\r\n      }\r\n    },\r\n    doSubmit(scheduleCode) {\r\n      SaveSchdulingTaskById({\r\n        schdulingPlanId: scheduleCode\r\n      }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '下达成功',\r\n            type: 'success'\r\n          })\r\n          this.closeView()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      }).finally(_ => {\r\n        this.pgLoading = false\r\n      }).catch(_ => {\r\n        this.pgLoading = false\r\n      })\r\n    },\r\n    async getWorkShop(value) {\r\n      const {\r\n        origin,\r\n        row,\r\n        workShop: {\r\n          Id,\r\n          Display_Name\r\n        }\r\n      } = value\r\n      if (origin === 2) {\r\n        if (value.workShop?.Id) {\r\n          row.Workshop_Name = Display_Name\r\n          row.Workshop_Id = Id\r\n          this.setPath(row, Id)\r\n        } else {\r\n          row.Workshop_Name = ''\r\n          row.Workshop_Id = ''\r\n        }\r\n      } else {\r\n        // const gyMap = await this.getCraftProcess()\r\n        // const _process = await this.getProcessOption(value.workShop?.Id)\r\n        this.multipleSelection.forEach(item => {\r\n          if (value.workShop?.Id) {\r\n            item.Workshop_Name = Display_Name\r\n            item.Workshop_Id = Id\r\n            this.setPath(item, Id)\r\n          } else {\r\n            item.Workshop_Name = ''\r\n            item.Workshop_Id = ''\r\n          }\r\n        })\r\n      }\r\n    },\r\n    setPath(row, Id) {\r\n      if (row?.Scheduled_Workshop_Id) {\r\n        if (row.Scheduled_Workshop_Id !== Id) {\r\n          row.Technology_Path = ''\r\n        }\r\n      } else {\r\n        row.Technology_Path = ''\r\n      }\r\n    },\r\n    handleBatchWorkshop(origin, row) {\r\n      this.title = origin === 1 ? '批量分配车间' : '分配车间'\r\n      this.currentComponent = 'Workshop'\r\n      this.dWidth = '30%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].fetchData(origin, row, this.multipleSelection)\r\n      })\r\n    },\r\n    async mergeCraftProcess(list) {\r\n      let codes = [...new Set(list.map(v => v.Technology_Code))]\r\n      for (const key in this.craftCodeMap) {\r\n        if (this.craftCodeMap.hasOwnProperty(key)) {\r\n          codes = codes.filter(code => code !== key)\r\n        }\r\n      }\r\n      const _craftCodeMap = await this.getCraftProcess(codes)\r\n      Object.assign(this.craftCodeMap, _craftCodeMap)\r\n    },\r\n    getCraftProcess(gyGroup = []) {\r\n      gyGroup = gyGroup.filter(v => !!v)\r\n      if (!gyGroup.length) return Promise.resolve({})\r\n      return new Promise((resolve, reject) => {\r\n        GetProcessFlowListWithTechnology({\r\n          TechnologyCodes: gyGroup,\r\n          Type: 1\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            const gyList = res.Data || []\r\n            const gyMap = gyList.reduce((acc, item) => {\r\n              acc[item.Code] = item.Technology_Path\r\n              return acc\r\n            }, {})\r\n            console.log('gyMap', gyMap)\r\n            resolve(gyMap)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            reject()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    /*   checkProcess(pList, flowList) {\r\n      return flowList.every(item => pList.includes(item))\r\n    },*/\r\n    async handleAutoDeal() {\r\n      /*      if (this.workshopEnabled) {\r\n        const hasWorkShop = this.checkHasWorkShop(1, this.multipleSelection)\r\n        if (!hasWorkShop) return\r\n      }*/\r\n\r\n      this.$confirm(`是否将选中数据按${this.comName}类型自动分配`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        if (this.workshopEnabled) {\r\n          const p = this.multipleSelection.map(item => {\r\n            return {\r\n              uniqueType: `${item.Type}$_$${item.Workshop_Id}`\r\n            }\r\n          })\r\n          const codes = Array.from(new Set(p.map(v => v.uniqueType)))\r\n          const objKey = {}\r\n          Promise.all(codes.map(v => {\r\n            const info = v.split('$_$')\r\n            return this.setLibType(info[0], info[1])\r\n          })\r\n          ).then(res => {\r\n            const hasUndefined = res.some(item => item == undefined)\r\n            if (hasUndefined) {\r\n              this.$message({\r\n                message: `所选车间内工序班组与${this.comName}类型工序不匹配，请手动分配工序`,\r\n                type: 'warning'\r\n              })\r\n            }\r\n\r\n            res.forEach((element, idx) => {\r\n              objKey[codes[idx]] = element\r\n            })\r\n            this.multipleSelection.forEach((element) => {\r\n              element.Technology_Path = objKey[`${element.Type}$_$${element.Workshop_Id}`]\r\n              this.resetWorkTeamMax(element, element.Technology_Path)\r\n            })\r\n          })\r\n        } else {\r\n          const p = this.multipleSelection.map(item => item.Type)\r\n          const codes = Array.from(new Set(p))\r\n          const objKey = {}\r\n\r\n          Promise.all(codes.map(v => {\r\n            return this.setLibType(v)\r\n          })).then(res => {\r\n            res.forEach((element, idx) => {\r\n              objKey[codes[idx]] = element\r\n            })\r\n            this.multipleSelection.forEach((element) => {\r\n              element.Technology_Path = objKey[element.Type]\r\n              this.resetWorkTeamMax(element, element.Technology_Path)\r\n            })\r\n          })\r\n        }\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    getProcessOption(workshopId) {\r\n      return new Promise((resolve, reject) => {\r\n        GetProcessListBase({\r\n          workshopId: workshopId,\r\n          type: 1 // 0:全部，工艺类型1：构件工艺，2：零件工艺\r\n        }).then(res => {\r\n          if (res.IsSucceed) {\r\n            const process = res.Data.map(v => v.Code)\r\n            resolve(process)\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    setLibType(code, workshopId) {\r\n      return new Promise((resolve) => {\r\n        const obj = {\r\n          Component_type: code,\r\n          type: 1\r\n        }\r\n        if (this.workshopEnabled) {\r\n          obj.workshopId = workshopId\r\n        }\r\n        GetLibListType(obj).then(res => {\r\n          if (res.IsSucceed) {\r\n            if (res.Data.Data && res.Data.Data.length) {\r\n              const info = res.Data.Data[0]\r\n              const workCode = info.WorkCode && info.WorkCode.replace(/\\\\/g, '/')\r\n              resolve(workCode)\r\n            } else {\r\n              resolve(undefined)\r\n            }\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    inputChange(row) {\r\n      this.setInputMax(row)\r\n    },\r\n    setInputMax(row) {\r\n      const inputValuesKeys = Object.keys(row)\r\n        .filter(v => !v.endsWith('max') && v.startsWith(row.uuid) && v.length > row.uuid.length)\r\n      inputValuesKeys.forEach((val) => {\r\n        const curCode = val.split(SPLIT_SYMBOL)[1]\r\n        const otherTotal = inputValuesKeys.filter(x => {\r\n          const code = x.split(SPLIT_SYMBOL)[1]\r\n          return x !== val && code === curCode\r\n        }).reduce((acc, item) => {\r\n          return acc + numeral(row[item]).value()\r\n        }, 0)\r\n        row[val + SPLIT_SYMBOL + 'max'] = row.Schduled_Count - otherTotal\r\n      })\r\n    },\r\n    sendProcess({ arr, str }) {\r\n      let isSuccess = true\r\n      for (let i = 0; i < arr.length; i++) {\r\n        const item = arr[i]\r\n        if (item.originalPath && item.originalPath !== str) {\r\n          isSuccess = false\r\n          break\r\n        }\r\n        item.Technology_Path = str\r\n      }\r\n      if (!isSuccess) {\r\n        this.$message({\r\n          message: `请和该区域批次下已排产同${this.comName}保持工序一致`,\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n    resetWorkTeamMax(row, str) {\r\n      if (str) {\r\n        row.Technology_Path = str\r\n      } else {\r\n        str = row.Technology_Path\r\n      }\r\n      const list = str?.split('/') || []\r\n      this.workingTeam.forEach((element, idx) => {\r\n        const cur = list.some(k => k === element.Process_Code)\r\n        const code = this.getRowUnique(row.uuid, element.Process_Code, element.Working_Team_Id)\r\n        const max = this.getRowUniqueMax(row.uuid, element.Process_Code, element.Working_Team_Id)\r\n        if (cur) {\r\n          if (!row[code]) {\r\n            this.$set(row, code, 0)\r\n            this.$set(row, max, row.Schduled_Count)\r\n          }\r\n        } else {\r\n          this.$delete(row, code)\r\n          this.$delete(row, max)\r\n        }\r\n      })\r\n    },\r\n    checkPermissionTeam(processStr, processCode) {\r\n      if (!processStr) return false\r\n      const list = processStr?.split('/') || []\r\n      return !!list.some(v => v === processCode)\r\n    },\r\n\r\n    async getTableConfig(code) {\r\n      await GetGridByCode({\r\n        code\r\n      }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n          const list = Data.ColumnList || []\r\n          this.ownerColumn = list.find(item => item.Code === 'Part_Used_Process')\r\n          this.ownerColumn2 = list.find(item => item.Code === 'Is_Main_Part')\r\n          this.columns = this.setColumnDisplay(list)\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setColumnDisplay(list) {\r\n      return list.filter(v => v.Is_Display)\r\n      // .map(item => {\r\n      //   if (FIX_COLUMN.includes(item.Code)) {\r\n      //     item.fixed = 'left'\r\n      //   }\r\n      //   return item\r\n      // })\r\n    },\r\n    activeCellMethod({ row, column, columnIndex }) {\r\n      if (this.isView) return false\r\n      const processCode = column.field?.split('$_$')[1]\r\n      return this.checkPermissionTeam(row.Technology_Path, processCode)\r\n    },\r\n    openBPADialog(type, row) {\r\n      if (this.workshopEnabled) {\r\n        if (type === 1) {\r\n          const IsUnique = this.checkIsUniqueWorkshop()\r\n          if (!IsUnique) return\r\n        }\r\n      }\r\n      this.title = type === 2 ? '工序调整' : '批量工序调整'\r\n      this.currentComponent = 'BatchProcessAdjust'\r\n      this.dWidth = this.isCom ? '60%' : '35%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].setData(type === 2 ? [row] : this.multipleSelection, type === 2 ? row.Technology_Path : '')\r\n      })\r\n    },\r\n    checkIsUniqueWorkshop() {\r\n      let isUnique = true\r\n      const firstV = this.multipleSelection[0].Workshop_Name\r\n      for (let i = 1; i < this.multipleSelection.length; i++) {\r\n        const item = this.multipleSelection[i]\r\n        if (item.Workshop_Name !== firstV) {\r\n          isUnique = false\r\n          break\r\n        }\r\n      }\r\n      if (!isUnique) {\r\n        this.$message({\r\n          message: '批量分配工序时只有相同车间下的才可一起批量分配',\r\n          type: 'warning'\r\n        })\r\n      }\r\n      return isUnique\r\n    },\r\n    checkHasWorkShop(type, arr) {\r\n      let hasWorkShop = true\r\n      for (let i = 0; i < arr.length; i++) {\r\n        const item = arr[i]\r\n        if (!item.Workshop_Name) {\r\n          hasWorkShop = false\r\n          break\r\n        }\r\n      }\r\n      if (!hasWorkShop) {\r\n        this.$message({\r\n          message: '请先选择车间后再进行工序分配',\r\n          type: 'warning'\r\n        })\r\n      }\r\n      return hasWorkShop\r\n    },\r\n    handleAddDialog(type = 'add') {\r\n      if (this.isCom) {\r\n        this.title = `${this.comName}排产`\r\n      } else {\r\n        this.title = '添加零件'\r\n      }\r\n      this.currentComponent = 'AddDraft'\r\n      this.dWidth = '80%'\r\n      this.openAddDraft = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['draft'].setPageData()\r\n      })\r\n    },\r\n    getRowUnique(uuid, processCode, workingId) {\r\n      return `${uuid}${SPLIT_SYMBOL}${processCode}${SPLIT_SYMBOL}${workingId}`\r\n    },\r\n    getRowUniqueMax(uuid, processCode, workingId) {\r\n      return this.getRowUnique(uuid, processCode, workingId) + `${SPLIT_SYMBOL}max`\r\n    },\r\n    async handleSelectMenu(v) {\r\n      if (v === 'process') {\r\n        this.openBPADialog(1)\r\n      } else if (v === 'deal') {\r\n        await this.handleAutoDeal(1)\r\n      } else if (v === 'craft') {\r\n        await this.handleSetCraftProcess()\r\n      }\r\n    },\r\n    async handleSetCraftProcess() {\r\n      const showSuccess = () => {\r\n        this.$message({\r\n          message: '已分配成功',\r\n          type: 'success'\r\n        })\r\n      }\r\n      const rowList = this.multipleSelection.map(v => v.Technology_Code).filter(v => !!v)\r\n      if (!rowList.length) {\r\n        this.$message({\r\n          message: '工艺代码不存在',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      await this.mergeCraftProcess(this.multipleSelection)\r\n      const workshopIds = Array.from(new Set(this.multipleSelection.map(v => v.Workshop_Id).filter(v => !!v)))\r\n      const w_process = []\r\n      if (workshopIds.length) {\r\n        workshopIds.forEach(workshopId => {\r\n          w_process.push(this.getProcessOption(workshopId).then(result => ({\r\n            [workshopId]: result\r\n          })))\r\n        })\r\n        const workshopPromise = Promise.all(w_process).then((values) => {\r\n          return Object.assign({}, ...values)\r\n        })\r\n        workshopPromise.then(workshop => {\r\n          let flag = true\r\n          let usedPartFlag = true\r\n          for (let i = 0; i < this.multipleSelection.length; i++) {\r\n            const curRow = this.multipleSelection[i]\r\n            console.log('curRow', JSON.parse(JSON.stringify(curRow)))\r\n            const workshopProcess = workshop[curRow.Workshop_Id]\r\n            console.log('workshopProcess', workshopProcess)\r\n            const craftArray = this.craftCodeMap[curRow.Technology_Code]\r\n            console.log('craftArray', craftArray)\r\n\r\n            if (craftArray) {\r\n              const isIncluded = craftArray.every(process => workshopProcess.includes(process))\r\n              if (!isIncluded) {\r\n                flag = false\r\n                continue\r\n              }\r\n              const hasUsedPart = this.checkHasCraftUsedPart(curRow, craftArray)\r\n              if (hasUsedPart) {\r\n                curRow.Technology_Path = craftArray.join('/')\r\n              } else {\r\n                usedPartFlag = false\r\n              }\r\n            }\r\n          }\r\n          if (!flag) {\r\n            setTimeout(() => {\r\n              this.$alert('所选车间下班组加工工序不包含工艺代码工序请手动排产', '提示', {\r\n                confirmButtonText: '确定'\r\n              })\r\n            }, 200)\r\n          }\r\n\r\n          const isSuccess = this.showCraftUsedPartResult(usedPartFlag)\r\n          flag && isSuccess && showSuccess()\r\n        })\r\n      } else {\r\n        let usedPartFlag = true\r\n        this.multipleSelection.forEach((curRow) => {\r\n          const craftArray = this.craftCodeMap[curRow.Technology_Code]\r\n          if (craftArray) {\r\n            const hasUsedPart = this.checkHasCraftUsedPart(curRow, craftArray)\r\n            if (hasUsedPart) {\r\n              curRow.Technology_Path = craftArray.join('/')\r\n            } else {\r\n              usedPartFlag = false\r\n            }\r\n          }\r\n        })\r\n        const isSuccess = this.showCraftUsedPartResult(usedPartFlag)\r\n        isSuccess && showSuccess()\r\n      }\r\n    },\r\n    checkHasCraftUsedPart(curRow, craftArray) {\r\n      if (!curRow.Part_Used_Process) return true\r\n      const partUsedProcess = curRow.Part_Used_Process.split(',')\r\n      const result = partUsedProcess.every(item => craftArray.includes(item))\r\n      return result\r\n      // return !(curRow.Part_Used_Process && !craftArray.includes(curRow.Part_Used_Process))\r\n    },\r\n    showCraftUsedPartResult(hasUsedPart) {\r\n      if (hasUsedPart) return true\r\n      setTimeout(() => {\r\n        this.$alert(`部分${this.comName}工序路径内不包含零件领用工序请手动排产`, '提示', {\r\n          confirmButtonText: '确定'\r\n        })\r\n      }, 200)\r\n      return false\r\n    },\r\n    handleBatchOwner(type, row) {\r\n      this.title = '批量分配领用工序'\r\n      this.currentComponent = 'OwnerProcess'\r\n      this.dWidth = '30%'\r\n      this.dialogVisible = true\r\n      this.$nextTick(_ => {\r\n        this.$refs['content'].setOption(type === 2, type === 2 ? [row] : this.multipleSelection)\r\n      })\r\n    },\r\n    handleReverse() {\r\n      const cur = []\r\n      this.tbData.forEach((element, idx) => {\r\n        element.checked = !element.checked\r\n        if (element.checked) {\r\n          cur.push(element)\r\n        }\r\n      })\r\n      this.multipleSelection = cur\r\n      if (this.multipleSelection.length === this.tbData.length) {\r\n        this.$refs['xTable'].setAllCheckboxRow(true)\r\n      }\r\n      if (this.multipleSelection.length === 0) {\r\n        this.$refs['xTable'].setAllCheckboxRow(false)\r\n      }\r\n    },\r\n    // tbFilterChange() {\r\n    //   const xTable = this.$refs.xTable\r\n    //   const column = xTable.getColumnByField('Type_Name')\r\n    //   if (!column?.filters?.length) return\r\n    //   column.filters.forEach(d => {\r\n    //     d.checked = d.value === this.searchType\r\n    //   })\r\n    //   xTable.updateData()\r\n    // },\r\n    getType() {\r\n      const getCompTree = () => {\r\n        const fun = this.isCom ? GetCompTypeTree : GetPartTypeList\r\n        fun({}).then(res => {\r\n          if (res.IsSucceed) {\r\n            let result = res.Data\r\n            if (!this.isCom) {\r\n              result = result\r\n                .map((v, idx) => {\r\n                  return {\r\n                    Data: v.Name,\r\n                    Label: v.Name\r\n                  }\r\n                })\r\n            }\r\n            this.treeParamsComponentType.data = result\r\n            this.$nextTick((_) => {\r\n              this.$refs.treeSelectComponentType?.treeDataUpdateFun(result)\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      }\r\n\r\n      getCompTree()\r\n    },\r\n    // 查看图纸\r\n    handleDwg(row) {\r\n      const obj = {}\r\n      if (this.isCom) {\r\n        obj.Comp_Id = row.Comp_Import_Detail_Id\r\n      } else {\r\n        obj.Part_Id = row.Part_Aggregate_Id\r\n      }\r\n      GetDwg(obj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const fileurl = res?.Data?.length && res.Data[0].File_Url\r\n          window.open('http://dwgv1.bimtk.com:5432/?CadUrl=' + parseOssUrl(fileurl), '_blank')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setProcessList(info) {\r\n      this.changeProcessList(info)\r\n    },\r\n    resetInnerForm() {\r\n      this.$refs['searchForm'].resetFields()\r\n      this.$refs.xTable.clearFilter()\r\n    },\r\n    innerFilter() {\r\n      this.multipleSelection = []\r\n      const arr = []\r\n      if (this.isCom) {\r\n        arr.push('Type', 'Comp_Code', 'Spec', 'Is_Component')\r\n      } else {\r\n        arr.push('Part_Code', 'Spec', 'Type_Name')\r\n      }\r\n\r\n      const xTable = this.$refs.xTable\r\n      xTable.clearCheckboxRow()\r\n      arr.forEach((element, idx) => {\r\n        const column = xTable.getColumnByField(element)\r\n        if (element === 'Is_Component') {\r\n          column.filters.forEach((option, idx) => {\r\n            option.checked = idx === (this.innerForm.searchDirect ? 0 : 1)\r\n          })\r\n        }\r\n        if (element === 'Spec') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.searchSpecSearch\r\n          option.checked = true\r\n        }\r\n        if (element === 'Type' || element === 'Type_Name') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.searchComTypeSearch\r\n          option.checked = true\r\n        }\r\n        if (element === 'Comp_Code' || element === 'Part_Code') {\r\n          const option = column.filters[0]\r\n          option.data = this.innerForm.searchContent\r\n          option.checked = true\r\n        }\r\n      })\r\n      xTable.updateData()\r\n    },\r\n    filterComponentMethod({ option, row }) {\r\n      if (this.innerForm.searchDirect === '') {\r\n        return true\r\n      }\r\n      return row.Is_Component === !this.innerForm.searchDirect\r\n    },\r\n    filterSpecMethod({ option, row }) {\r\n      if (this.innerForm.searchSpecSearch.trim() === '') {\r\n        return true\r\n      }\r\n      const splitAndClean = (input) => input.trim().replace(/\\s+/g, ' ').split(' ')\r\n      const specArray = splitAndClean(this.innerForm.searchSpecSearch)\r\n      return specArray.some(code => (row.Spec || '').includes(code))\r\n    },\r\n\r\n    filterTypeMethod({ option, row }) {\r\n      if (this.innerForm.searchComTypeSearch === '') {\r\n        return true\r\n      }\r\n      const cur = this.isCom ? 'Type' : 'Type_Name'\r\n      return row[cur] === this.innerForm.searchComTypeSearch\r\n    },\r\n    filterCodeMethod({ option, row }) {\r\n      if (this.innerForm.searchContent.trim() === '') {\r\n        return true\r\n      }\r\n\r\n      const splitAndClean = (input) => input.trim().replace(/\\s+/g, ' ').split(' ')\r\n\r\n      const cur = this.isCom ? 'Comp_Code' : 'Part_Code'\r\n\r\n      const arr = splitAndClean(this.innerForm.searchContent)\r\n\r\n      if (this.curSearch === 1) {\r\n        return arr.some(code => row[cur] === code)\r\n      } else {\r\n        for (let i = 0; i < arr.length; i++) {\r\n          const item = arr[i]\r\n          if (row[cur].includes(item)) {\r\n            return true\r\n          }\r\n        }\r\n        return false\r\n      }\r\n    },\r\n    componentTypeFilter(e) {\r\n      this.$refs?.treeSelectComponentType.filterFun(e)\r\n    },\r\n    getInstallUnitIdNameList(id) {\r\n      if (!this.areaId || this.isVersionFour) {\r\n        this.installUnitIdList = []\r\n        this.disabledAdd = false\r\n      } else {\r\n        this.disabledAdd = true\r\n        GetInstallUnitIdNameList({ Area_Id: this.areaId }).then(res => {\r\n          this.installUnitIdList = res.Data\r\n          if (this.installUnitIdList.length) {\r\n            this.formInline.InstallUnit_Id = this.installUnitIdList[0].Id\r\n          }\r\n          this.disabledAdd = false\r\n        })\r\n      }\r\n    },\r\n    installChange() {\r\n      if (!this.tbData.length) {\r\n        this.$refs['searchForm'].resetFields()\r\n        this.$refs.xTable.clearFilter()\r\n        return\r\n      }\r\n      this.$confirm('切换区域右侧数据清空, 是否确认?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.tbData = []\r\n        this.resetInnerForm()\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    showPartUsedProcess(row) {\r\n      if (this.isNest) {\r\n        return !!row.Comp_Import_Detail_Id\r\n      } else {\r\n        return !this.isView && row.Type !== 'Direct'\r\n      }\r\n    },\r\n    handleExport() {\r\n      if (!this.tbData.length) {\r\n        this.$message({\r\n          message: '暂无数据',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      console.log(7, this.$refs.xTable)\r\n      const item = this.tbData[0]\r\n      this.$refs.xTable.exportData({\r\n        filename: `${this.comName}排产-${item.Project_Name}-${item.Area_Name}-${this.formInline.Schduling_Code}(${this.comName})`,\r\n        type: 'xlsx',\r\n        data: this.tbData\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.flex-row {\r\n  display: flex;\r\n\r\n  .cs-left {\r\n    background-color: #ffffff;\r\n    margin-right: 20px;\r\n    border-radius: 4px;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n\r\n    .cs-tree-wrapper {\r\n      height: 100%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      overflow: hidden;\r\n      padding: 16px;\r\n\r\n      .tree-search {\r\n        display: flex;\r\n\r\n        .search-select {\r\n          margin-right: 8px;\r\n        }\r\n      }\r\n\r\n      .el-tree {\r\n        flex: 1;\r\n        overflow: auto;\r\n      }\r\n    }\r\n  }\r\n\r\n  .cs-right {\r\n    flex: 1;\r\n    overflow: hidden;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  padding: 0;\r\n  text-align: right;\r\n}\r\n\r\n::v-deep .el-card__body {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.tb-x {\r\n  flex: 1;\r\n  height: 0;\r\n  margin-bottom: 10px;\r\n  overflow: auto;\r\n}\r\n\r\n.topTitle {\r\n  font-size: 14px;\r\n  margin: 0 0 16px;\r\n\r\n  span {\r\n    display: inline-block;\r\n    width: 2px;\r\n    height: 14px;\r\n    background: #009dff;\r\n    vertical-align: middle;\r\n    margin-right: 6px;\r\n  }\r\n}\r\n\r\n::v-deep .elDivder {\r\n  margin: 10px;\r\n}\r\n\r\n.btn-x {\r\n  //margin-bottom: 10px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.el-icon-edit {\r\n  cursor: pointer;\r\n}\r\n\r\nfooter {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.cs-bottom {\r\n  position: relative;\r\n  height: 40px;\r\n  line-height: 40px;\r\n\r\n  .data-info {\r\n    position: absolute;\r\n    bottom: 0;\r\n\r\n    .info-x {\r\n      margin-right: 20px;\r\n    }\r\n  }\r\n}\r\n\r\n.fourGreen {\r\n  color: #00C361;\r\n  font-style: normal;\r\n}\r\n\r\n.fourOrange {\r\n  color: #FF9400;\r\n  font-style: normal;\r\n}\r\n\r\n.fourRed {\r\n  color: #FF0000;\r\n  font-style: normal;\r\n}\r\n\r\n.cs-blue {\r\n  color: #5AC8FA;\r\n}\r\n.cs-column-row{\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .cs-ell{\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n\r\n  }\r\n}\r\n</style>\r\n"]}]}
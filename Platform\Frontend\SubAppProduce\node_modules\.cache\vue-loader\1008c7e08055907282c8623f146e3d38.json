{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production\\components\\addDraft.vue?vue&type=style&index=0&id=76ef319b&scoped=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\plan-production\\schedule-production\\components\\addDraft.vue", "mtime": 1757468113335}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5jb250ZW50Qm94IHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCg0KICAuYnV0dG9uIHsNCiAgICBtYXJnaW4tdG9wOiAxNnB4Ow0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAganVzdGlmeS1jb250ZW50OiBlbmQ7DQogIH0NCg0KICAudGItd3JhcHBlciB7DQogICAgZmxleDogMSAxIGF1dG87DQogICAgaGVpZ2h0OiA1MHZoOw0KICB9DQoNCiAgLmRhdGEtaW5mb3sNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIG1hcmdpbi10b3A6IDE2cHg7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["addDraft.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgnBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "addDraft.vue", "sourceRoot": "src/views/PRO/plan-production/schedule-production/components", "sourcesContent": ["<template>\r\n  <div class=\"contentBox\">\r\n    <el-form ref=\"form\" :model=\"form\" label-width=\"90px\">\r\n      <el-row>\r\n        <template v-if=\"isCom\">\r\n          <el-col :span=\"10\">\r\n            <el-form-item label=\"构件编号\" prop=\"Comp_Codes\">\r\n              <el-input\r\n                v-model=\"form.Comp_Code\"\r\n                clearable\r\n                style=\"width: 45%\"\r\n                placeholder=\"请输入(空格区分/多个搜索)\"\r\n                type=\"text\"\r\n              />\r\n              <el-input\r\n                v-model=\"form.Comp_CodeBlur\"\r\n                clearable\r\n                style=\"width: 45%;margin-left: 16px\"\r\n                placeholder=\"模糊查找(请输入关键字)\"\r\n                type=\"text\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-form-item label=\"构件类型\" prop=\"Type\">\r\n              <el-tree-select\r\n                ref=\"treeSelectObjectType\"\r\n                v-model=\"form.Type\"\r\n                style=\"width: 100%\"\r\n                class=\"cs-tree-x\"\r\n                :select-params=\"treeSelectParams\"\r\n                :tree-params=\"ObjectTypeList\"\r\n                value-key=\"Id\"\r\n              />\r\n              <!--              <el-select v-model=\"form.Type\" placeholder=\"请选择\" clearable @clear=\"filterData\">\r\n                <el-option label=\"全部\" value=\"\" />\r\n                <el-option\r\n                  v-for=\"item in comTypeOptions\"\r\n                  :key=\"item.value\"\r\n                  :label=\"item.label\"\r\n                  :value=\"item.value\"\r\n                />\r\n              </el-select>-->\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"5\">\r\n            <el-form-item label=\"规格\" prop=\"Spec\">\r\n              <el-input v-model.trim=\"form.Spec\" placeholder=\"请输入\" clearable />\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n        </template>\r\n        <template v-else>\r\n          <el-col :span=\"7\">\r\n            <el-form-item label=\"所属构件\" prop=\"Comp_Code\">\r\n              <el-input\r\n                v-model=\"form.Comp_Code\"\r\n                style=\"width: 45%;\"\r\n                placeholder=\"请输入(空格区分/多个搜索)\"\r\n                clearable\r\n              />\r\n              <el-input\r\n                v-model=\"form.Comp_CodeBlur\"\r\n                clearable\r\n                style=\"width: 45%;margin-left: 16px\"\r\n                placeholder=\"模糊查找(请输入关键字)\"\r\n                type=\"text\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"7\">\r\n            <el-form-item label=\"零件名称\" prop=\"Part_Code\">\r\n              <el-input\r\n                v-model=\"form.Part_Code\"\r\n                style=\"width: 45%;\"\r\n                placeholder=\"请输入(空格区分/多个搜索)\"\r\n                clearable\r\n              />\r\n              <el-input\r\n                v-model=\"form.Part_CodeBlur\"\r\n                clearable\r\n                style=\"width: 45%;margin-left: 16px\"\r\n                placeholder=\"模糊查找(请输入关键字)\"\r\n                type=\"text\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-form-item label=\"规格\" prop=\"Spec\">\r\n              <el-input\r\n                v-model.trim=\"form.Spec\"\r\n                placeholder=\"请输入\"\r\n                clearable\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-form-item label=\"零件种类\" prop=\"Type_Name\">\r\n              <el-select\r\n                v-model=\"form.Type_Name\"\r\n                placeholder=\"请选择\"\r\n                clearable\r\n              >\r\n                <el-option\r\n                  v-for=\"item in typeOption\"\r\n                  :key=\"item.Code\"\r\n                  :label=\"item.Name\"\r\n                  :value=\"item.Name\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </template>\r\n        <el-col :span=\"2\">\r\n          <el-button style=\"margin-left: 10px\" type=\"primary\" @click=\"handleSearch()\">查询</el-button>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n    <div class=\"tb-wrapper\">\r\n      <vxe-table\r\n        ref=\"xTable1\"\r\n        :empty-render=\"{name: 'NotData'}\"\r\n        show-header-overflow\r\n        empty-text=\"暂无数据\"\r\n        height=\"auto\"\r\n        show-overflow\r\n        :checkbox-config=\"{checkField: 'checked'}\"\r\n        :loading=\"tbLoading\"\r\n        :row-config=\"{isCurrent: true, isHover: true }\"\r\n        class=\"cs-vxe-table\"\r\n        align=\"left\"\r\n        stripe\r\n        :data=\"fTable\"\r\n        resizable\r\n        :edit-config=\"{trigger: 'click', mode: 'cell', activeMethod: activeCellMethod}\"\r\n        :tooltip-config=\"{ enterable: true }\"\r\n        @checkbox-all=\"tbSelectChange\"\r\n        @checkbox-change=\"tbSelectChange\"\r\n      >\r\n        <vxe-column fixed=\"left\" type=\"checkbox\" width=\"60\" />\r\n        <template v-for=\"item in columns\">\r\n          <vxe-column\r\n            v-if=\"item.Code === 'customCountColumn'\"\r\n            :key=\"item.Code\"\r\n            :align=\"item.Align\"\r\n            :field=\"item.Code\"\r\n            :title=\"item.Display_Name\"\r\n            sortable\r\n            :edit-render=\"{}\"\r\n            min-width=\"120\"\r\n          >\r\n            <template #edit=\"{ row }\">\r\n              <vxe-input\r\n                v-model.number=\"row.count\"\r\n                type=\"integer\"\r\n                :min=\"1\"\r\n                :max=\"row.maxCount\"\r\n              />\r\n            </template>\r\n            <template #default=\"{ row }\">\r\n              {{ row.count | displayValue }}\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column\r\n            v-else-if=\"item.Code === 'Is_Component'\"\r\n            :key=\"item.Code\"\r\n            :align=\"item.Align\"\r\n            :field=\"item.Code\"\r\n            :title=\"item.Display_Name\"\r\n            sortable\r\n            :min-width=\"item.Width\"\r\n          >\r\n            <template #default=\"{ row }\">\r\n              <el-tag :type=\"row.Is_Component ? 'danger' : 'success'\">{{\r\n                row.Is_Component ? \"否\" : \"是\"\r\n              }}</el-tag>\r\n            </template>\r\n          </vxe-column>\r\n          <vxe-column\r\n            v-else\r\n            :key=\"item.Code\"\r\n            :align=\"item.Align\"\r\n            :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n            show-overflow=\"tooltip\"\r\n            sortable\r\n            :field=\"item.Code\"\r\n            :title=\"item.Display_Name\"\r\n            :min-width=\"item.Width\"\r\n          />\r\n        </template>\r\n      </vxe-table>\r\n    </div>\r\n    <div class=\"data-info\">\r\n      <el-tag\r\n        size=\"medium\"\r\n        class=\"info-x\"\r\n      >已选 {{ totalSelection.length }} 条数据\r\n      </el-tag>\r\n      <vxe-pager\r\n        border\r\n        background\r\n        :loading=\"tbLoading\"\r\n        :current-page.sync=\"pageInfo.page\"\r\n        :page-size.sync=\"pageInfo.pageSize\"\r\n        :page-sizes=\"pageInfo.pageSizes\"\r\n        :total=\"pageInfo.total\"\r\n        :layouts=\"['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']\"\r\n        size=\"small\"\r\n        @page-change=\"handlePageChange\"\r\n      />\r\n    </div>\r\n    <div class=\"button\">\r\n      <el-button @click=\"handleClose\">取消</el-button>\r\n      <el-button\r\n        type=\"primary\"\r\n        :disabled=\"!totalSelection.length\"\r\n        :loading=\"saveLoading\"\r\n        @click=\"handleSave\"\r\n      >保存</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { GetCanSchdulingComps } from '@/api/PRO/production-task'\r\nimport { GetPartList } from '@/api/PRO/production-part'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { FIX_COLUMN } from '@/views/PRO/plan-production/schedule-production/constant'\r\nimport { debounce, deepClone } from '@/utils'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport { GetCompTypeTree } from '@/api/PRO/professionalType'\r\nimport { GetPartTypeList } from '@/api/PRO/partType'\r\n\r\nexport default {\r\n  props: {\r\n    scheduleId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    pageType: {\r\n      type: String,\r\n      default: 'com'\r\n    },\r\n    showDialog: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      pageInfo: {\r\n        page: 1,\r\n        pageSize: 500,\r\n        pageSizes: tablePageSize,\r\n        total: 0\r\n      },\r\n      form: {\r\n        Comp_Code: '',\r\n        Comp_CodeBlur: '',\r\n        Part_CodeBlur: '',\r\n        Part_Code: '',\r\n        Type_Name: '',\r\n        Spec: '',\r\n        Type: ''\r\n      },\r\n      isOwnerNull: true,\r\n      tbLoading: false,\r\n      saveLoading: false,\r\n      columns: [],\r\n      fTable: [],\r\n      tbConfig: {},\r\n      TotalCount: 0,\r\n      Page: 0,\r\n      multipleSelection: [],\r\n      totalSelection: [],\r\n      search: () => ({}),\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        clearable: true\r\n      },\r\n      ObjectTypeList: {\r\n        // 构件类型\r\n        'check-strictly': true,\r\n        'default-expand-all': true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      },\r\n      typeOption: []\r\n    }\r\n  },\r\n  computed: {\r\n    isCom() {\r\n      return this.pageType === 'com'\r\n    }\r\n  },\r\n  watch: {\r\n    showDialog(newValue) {\r\n      newValue && (this.saveLoading = false)\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getConfig()\r\n    if (this.isCom) {\r\n      this.getObjectTypeList()\r\n    } else {\r\n      this.getType()\r\n    }\r\n    this.search = debounce(this.fetchData, 800, true)\r\n  },\r\n  methods: {\r\n    async getConfig() {\r\n      let code = ''\r\n      code = this.isCom\r\n        ? 'PROComDraftEditTbConfig'\r\n        : 'PROPartDraftEditTbConfig'\r\n      await this.getTableConfig(code)\r\n      this.fetchData()\r\n    },\r\n    filterData(page) {\r\n      const f = []\r\n      for (const formKey in this.form) {\r\n        if (this.form[formKey] || this.form[formKey] === false) {\r\n          f.push(formKey)\r\n        }\r\n      }\r\n      if (!f.length) {\r\n        this.setPage()\r\n        !page && (this.pageInfo.page = 1)\r\n        this.pageInfo.total = this.tbData.length\r\n        return\r\n      }\r\n      const temTbData = this.tbData.filter(v => {\r\n        v.checked = false\r\n        if (this.form.Comp_Code.trim() && !this.form['Comp_Code'].split(' ').includes(v['Comp_Code'])) {\r\n          return false\r\n        }\r\n        if (this.form.Comp_CodeBlur.trim() && !v.Comp_Code.includes(this.form.Comp_CodeBlur)) {\r\n          return false\r\n        }\r\n        if (this.form.Type && v.Type !== this.form.Type) {\r\n          return false\r\n        }\r\n        if (this.form.Part_CodeBlur.trim() && !v.Part_Code.includes(this.form.Part_CodeBlur)) {\r\n          return false\r\n        }\r\n        if (this.form.Part_Code.trim() && !this.form['Part_Code'].split(' ').includes(v['Part_Code'])) {\r\n          return false\r\n        }\r\n        if (this.form.Type_Name !== '' && v.Type_Name !== this.form.Type_Name) {\r\n          return false\r\n        }\r\n        if (this.form.Spec.trim() !== '' && !v.Spec.includes(this.form.Spec)) {\r\n          return false\r\n        }\r\n        return true\r\n      })\r\n\r\n      console.log('page', page)\r\n      !page && (this.pageInfo.page = 1)\r\n      this.pageInfo.total = temTbData.length\r\n      this.setPage(temTbData)\r\n    },\r\n    handleSearch() {\r\n      this.totalSelection = []\r\n      this.clearSelect()\r\n      if (this.tbData?.length) {\r\n        this.tbData.forEach(item => item.checked = false)\r\n        this.filterData()\r\n      }\r\n    },\r\n    handleSelect(data) {\r\n      this.multipleSelection = data\r\n    },\r\n    tbSelectChange(array) {\r\n      console.log('array', array)\r\n      this.totalSelection = this.tbData.filter(v => v.checked)\r\n    },\r\n    clearSelect() {\r\n      this.$refs.xTable1.clearCheckboxRow()\r\n      this.totalSelection = []\r\n    },\r\n    async fetchData() {\r\n      this.tbLoading = true\r\n      if (this.isCom) {\r\n        await this.getComTbData()\r\n      } else {\r\n        await this.getPartTbData()\r\n      }\r\n      this.initTbData()\r\n      this.filterData()\r\n      this.tbLoading = false\r\n    },\r\n    setPageData() {\r\n      if (this.tbData?.length) {\r\n        this.pageInfo.page = 1\r\n        this.tbData = this.tbData.filter(v => v.Can_Schduling_Count > 0)\r\n        this.filterData()\r\n      }\r\n    },\r\n    handleSave() {\r\n      this.saveLoading = true\r\n      setTimeout(() => {\r\n        this.totalSelection.forEach((item) => {\r\n          const intCount = parseInt(item.count)\r\n          item.Schduled_Count += intCount\r\n          item.Can_Schduling_Count -= intCount\r\n          item.Can_Schduling_Weight = item.Can_Schduling_Count * item.Weight\r\n          item.maxCount = item.Can_Schduling_Count\r\n          item.chooseCount = intCount\r\n          item.count = item.Can_Schduling_Count\r\n          item.checked = false\r\n        })\r\n        const cp = deepClone(this.totalSelection)\r\n\r\n        this.$emit('sendSelectList', cp)\r\n        this.$emit('close')\r\n        this.clearSelect()\r\n        this.setPage()\r\n      }, 0)\r\n    },\r\n    initTbData() {\r\n      if (!this.tbData?.length) {\r\n        this.tbData = []\r\n        this.backendTb = []\r\n        return\r\n      }\r\n      // 设置文本框选择的排产数量,设置自定义唯一码\r\n      const objKey = {}\r\n      this.tbData.forEach((item) => {\r\n        this.$set(item, 'count', item.Can_Schduling_Count)\r\n        this.$set(item, 'maxCount', item.Can_Schduling_Count)\r\n        item.uuid = uuidv4()\r\n        objKey[item.Type] = true\r\n      })\r\n      this.backendTb = deepClone(this.tbData)\r\n    },\r\n    async getComTbData() {\r\n      const { install, areaId } = this.$route.query\r\n      const { Comp_Codes, ...obj } = this.form\r\n      let codes = []\r\n      if (Object.prototype.toString.call(Comp_Codes) === '[object String]') {\r\n        codes = Comp_Codes && Comp_Codes.split(' ').filter(v => !!v)\r\n      }\r\n      await GetCanSchdulingComps({\r\n        ...obj,\r\n        Schduling_Plan_Id: this.scheduleId,\r\n        Comp_Codes: codes,\r\n        InstallUnit_Id: install,\r\n        Area_Id: areaId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.pageInfo.total = res.Data.length\r\n          this.tbData = res.Data.map((v, idx) => {\r\n            // 已排产赋值\r\n            v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n            v.Workshop_Id = v.Scheduled_Workshop_Id\r\n            v.Workshop_Name = v.Scheduled_Workshop_Name\r\n            v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n            if (v.originalPath) {\r\n              v.isDisabled = true\r\n            }\r\n            v.checked = false\r\n            v.initRowIndex = idx\r\n            return v\r\n          })\r\n          this.setPage()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 分页\r\n     */\r\n    handlePageChange({ currentPage, pageSize }) {\r\n      console.log(' currentPage, pageSize', currentPage, pageSize)\r\n      if (this.tbLoading) return\r\n      this.pageInfo.page = currentPage\r\n      this.pageInfo.pageSize = pageSize\r\n      this.setPage()\r\n      this.filterData(currentPage)\r\n    },\r\n\r\n    setPage(tb = this.tbData) {\r\n      this.fTable = tb.slice((this.pageInfo.page - 1) * this.pageInfo.pageSize, this.pageInfo.page * this.pageInfo.pageSize)\r\n    },\r\n\r\n    async getPartTbData() {\r\n      const { install, areaId } = this.$route.query\r\n      await GetPartList({\r\n        ...this.form,\r\n        Schduling_Plan_Id: this.scheduleId,\r\n        InstallUnit_Id: install,\r\n        Area_Id: areaId\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.pageInfo.total = res.Data.length\r\n          this.tbData = res.Data.map((v, idx) => {\r\n            if (v.Component_Technology_Path) {\r\n              const list = v.Component_Technology_Path.split('/')\r\n              if (list.length && list.some(x => x === v.Part_Used_Process)) {\r\n                v.originalUsedProcess = v.Part_Used_Process\r\n              } else {\r\n                v.originalUsedProcess = ''\r\n                v.Part_Used_Process = ''\r\n              }\r\n            }\r\n            v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''\r\n            v.Workshop_Id = v.Scheduled_Workshop_Id\r\n            v.Workshop_Name = v.Scheduled_Workshop_Name\r\n            v.Part_Used_Process = v.Scheduled_Used_Process || v.Part_Used_Process// 是否存在已使用的领用工序\r\n            v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path\r\n            v.isDisabled = !!v.originalPath\r\n            v.checked = false\r\n            v.initRowIndex = idx\r\n            return v\r\n          })\r\n          this.setPartColumn()\r\n          this.setPage()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    setPartColumn() {\r\n      // 纯零件\r\n      this.isOwnerNull = this.tbData.every(v => !v.Comp_Import_Detail_Id)\r\n      console.log('this.isOwnerNull', this.isOwnerNull)\r\n      if (this.isOwnerNull) {\r\n        const idx = this.columns.findIndex(v => v.Code === 'Component_Code')\r\n        idx !== -1 && this.columns.splice(idx, 1)\r\n      }\r\n    },\r\n    mergeData(list) {\r\n      list\r\n        .forEach((element) => {\r\n          const idx = this.backendTb.findIndex(\r\n            (item) => element.puuid && item.uuid === element.puuid\r\n          )\r\n          if (idx !== -1) {\r\n            this.tbData.splice(idx, 0, deepClone(this.backendTb[idx]))\r\n          }\r\n        })\r\n\r\n      this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)\r\n\r\n      this.filterData()\r\n    },\r\n    handleClose() {\r\n      this.$emit('close')\r\n    },\r\n    activeCellMethod({ row, column, columnIndex }) {\r\n      return column.field === 'customCountColumn'\r\n    },\r\n    async getTableConfig(code) {\r\n      await GetGridByCode({\r\n        code\r\n      }).then((res) => {\r\n        const { IsSucceed, Data, Message } = res\r\n        if (IsSucceed) {\r\n          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n          this.pageInfo.pageSize = Number(this.tbConfig.Row_Number)\r\n          const list = Data.ColumnList || []\r\n          this.columns = list.filter(v => v.Is_Display).map(item => {\r\n            if (FIX_COLUMN.includes(item.Code)) {\r\n              item.fixed = 'left'\r\n            }\r\n            return item\r\n          })\r\n          this.columns.push({\r\n            Display_Name: '排产数量',\r\n            Code: 'customCountColumn'\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getObjectTypeList() {\r\n      GetCompTypeTree({ professional: 'Steel' }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.ObjectTypeList.data = res.Data\r\n          this.$nextTick((_) => {\r\n            this.$refs.treeSelectObjectType.treeDataUpdateFun(res.Data)\r\n          })\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getType() {\r\n      GetPartTypeList({ Part_Grade: 0 }).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.typeOption = res.Data\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.contentBox {\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .button {\r\n    margin-top: 16px;\r\n    display: flex;\r\n    justify-content: end;\r\n  }\r\n\r\n  .tb-wrapper {\r\n    flex: 1 1 auto;\r\n    height: 50vh;\r\n  }\r\n\r\n  .data-info{\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-top: 16px;\r\n  }\r\n}\r\n</style>\r\n"]}]}
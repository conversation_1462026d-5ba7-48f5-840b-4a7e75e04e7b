{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\component-list\\v4\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\component-list\\v4\\index.vue", "mtime": 1757468112612}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRQcmVmZXJlbmNlU2V0dGluZ1ZhbHVlIH0gZnJvbSAnQC9hcGkvc3lzL3N5c3RlbS1zZXR0aW5nJw0KaW1wb3J0IHsgR2V0R3JpZEJ5Q29kZSB9IGZyb20gJ0AvYXBpL3N5cycNCmltcG9ydCB7IEdldEZhY3RvcnlQcm9mZXNzaW9uYWxCeUNvZGUgfSBmcm9tICdAL2FwaS9QUk8vcHJvZmVzc2lvbmFsVHlwZScNCmltcG9ydCB7DQogIEdldENvbXBvbmVudEltcG9ydERldGFpbFBhZ2VMaXN0LA0KICBEZWxldGVDb21wb25lbnRzLA0KICBEZWxldGVBbGxDb21wb25lbnRXaXRoUXVlcnksDQogIEdldENvbXBvbmVudFN1bW1hcnlJbmZvLA0KICBFeHBvcnRDb21wb25lbnRJbmZvLA0KICBFeHBvcnRDb21wb25lbnRTY2hlZHVsaW5nSW5mbywNCiAgRXhwb3J0VGhyZWVCb20sDQogIEV4cG9ydERlZXBlbkZ1bGxTY2hlZHVsaW5nSW5mbw0KfSBmcm9tICdAL2FwaS9QUk8vY29tcG9uZW50Jw0KaW1wb3J0IHsNCiAgR2V0UHJvamVjdEFyZWFUcmVlTGlzdCwNCiAgR2V0SW5zdGFsbFVuaXRJZE5hbWVMaXN0DQp9IGZyb20gJ0AvYXBpL1BSTy9wcm9qZWN0Jw0KaW1wb3J0IHsgZ2V0Q29uZmlndXJlIH0gZnJvbSAnQC9hcGkvdXNlcicNCmltcG9ydCB7IEdldENvbXBUeXBlVHJlZSB9IGZyb20gJ0AvYXBpL1BSTy9jb21wb25lbnQtdHlwZScNCmltcG9ydCB7IEdldFN0ZWVsQ2FkQW5kQmltSWQgfSBmcm9tICdAL2FwaS9QUk8vY29tcG9uZW50Jw0KaW1wb3J0IHsgR2V0RmlsZVR5cGUgfSBmcm9tICdAL2FwaS9zeXMnDQoNCmltcG9ydCBUcmVlRGV0YWlsIGZyb20gJ0AvY29tcG9uZW50cy9UcmVlRGV0YWlsL2luZGV4LnZ1ZScNCmltcG9ydCBUb3BIZWFkZXIgZnJvbSAnQC9jb21wb25lbnRzL1RvcEhlYWRlci9pbmRleC52dWUnDQppbXBvcnQgY29tSW1wb3J0IGZyb20gJ0Avdmlld3MvUFJPL2NvbXBvbmVudC1saXN0L3Y0L2NvbXBvbmVudC9JbXBvcnQudnVlJw0KaW1wb3J0IENvbXBvbmVudHNIaXN0b3J5IGZyb20gJ0Avdmlld3MvUFJPL2NvbXBvbmVudC1saXN0L3Y0L2NvbXBvbmVudC9Db21wb25lbnRzSGlzdG9yeS52dWUnDQppbXBvcnQgY29tSW1wb3J0QnlGYWN0b3J5IGZyb20gJ0Avdmlld3MvUFJPL2NvbXBvbmVudC1saXN0L3Y0L2NvbXBvbmVudC9JbXBvcnRCeUZhY3RvcnkudnVlJw0KaW1wb3J0IEhpc3RvcnlFeHBvcnQgZnJvbSAnQC92aWV3cy9QUk8vY29tcG9uZW50LWxpc3QvdjQvY29tcG9uZW50L0hpc3RvcnlFeHBvcnQudnVlJw0KaW1wb3J0IEJhdGNoRWRpdCBmcm9tICdAL3ZpZXdzL1BSTy9jb21wb25lbnQtbGlzdC92NC9jb21wb25lbnQvQmF0Y2hFZGl0b3IudnVlJw0KaW1wb3J0IENvbXBvbmVudFBhY2sgZnJvbSAnQC92aWV3cy9QUk8vY29tcG9uZW50LWxpc3QvdjQvY29tcG9uZW50L0NvbXBvbmVudFBhY2svaW5kZXgudnVlJw0KaW1wb3J0IEVkaXQgZnJvbSAnQC92aWV3cy9QUk8vY29tcG9uZW50LWxpc3QvdjQvY29tcG9uZW50L0VkaXQudnVlJw0KaW1wb3J0IE9uZUNsaWNrR2VuZXJhdGVQYWNrIGZyb20gJ0Avdmlld3MvUFJPL2NvbXBvbmVudC1saXN0L3Y0L2NvbXBvbmVudC9PbmVDbGlja0dlbmVyYXRlUGFjay52dWUnDQppbXBvcnQgR2VuZXJhdGVQYWNrIGZyb20gJ0Avdmlld3MvUFJPL2NvbXBvbmVudC1saXN0L3Y0L2NvbXBvbmVudC9HZW5lcmF0ZVBhY2sudnVlJw0KaW1wb3J0IFByb2R1Y3Rpb25Db25maXJtIGZyb20gJ0Avdmlld3MvUFJPL2NvbXBvbmVudC1saXN0L3Y0L2NvbXBvbmVudC9Qcm9kdWN0aW9uQ29uZmlybS52dWUnDQppbXBvcnQgUGFydExpc3QgZnJvbSAnQC92aWV3cy9QUk8vY29tcG9uZW50LWxpc3QvdjQvY29tcG9uZW50L1BhcnRMaXN0LnZ1ZScNCmltcG9ydCBTdGVlbE1lYW5zIGZyb20gJ0Avdmlld3MvUFJPL2NvbXBvbmVudC1saXN0L3Y0L2NvbXBvbmVudC9TdGVlbE1lYW5zLnZ1ZScNCmltcG9ydCBQcm9jZXNzRGF0YSBmcm9tICdAL3ZpZXdzL1BSTy9jb21wb25lbnQtbGlzdC92NC9jb21wb25lbnQvUHJvY2Vzc0RhdGEudnVlJw0KaW1wb3J0IE1vZGVsQ29tcG9uZW50Q29kZSBmcm9tICdAL3ZpZXdzL1BSTy9jb21wb25lbnQtbGlzdC92NC9jb21wb25lbnQvTW9kZWxDb21wb25lbnRDb2RlLnZ1ZScNCmltcG9ydCBQcm9kdWN0aW9uRGV0YWlscyBmcm9tICdAL3ZpZXdzL1BSTy9jb21wb25lbnQtbGlzdC92NC9jb21wb25lbnQvUHJvZHVjdGlvbkRldGFpbHMudnVlJw0KaW1wb3J0IE1vZGVsTGlzdEltcG9ydCBmcm9tICdAL3ZpZXdzL1BSTy9jb21wb25lbnQtbGlzdC92NC9jb21wb25lbnQvTW9kZWxMaXN0SW1wb3J0LnZ1ZScNCmltcG9ydCBjb21EcmF3ZGlhbG9nIGZyb20gJ0Avdmlld3MvUFJPL3Byb2R1Y3Rpb24tb3JkZXIvZGVlcGVuLWZpbGVzL2RpYWxvZy52dWUnIC8vIOa3seWMluaWh+S7ti3mnoTku7bor6blm77lr7zlhaUNCg0KaW1wb3J0IGVsRHJhZ0RpYWxvZyBmcm9tICdAL2RpcmVjdGl2ZS9lbC1kcmFnLWRpYWxvZycNCmltcG9ydCBQYWdpbmF0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9QYWdpbmF0aW9uL2luZGV4LnZ1ZScNCmltcG9ydCB7IHRpbWVGb3JtYXQgfSBmcm9tICdAL2ZpbHRlcnMnDQovLyBpbXBvcnQgeyBDb2x1bW4sIEhlYWRlciwgVGFibGUsIFRvb2x0aXAgfSBmcm9tICd2eGUtdGFibGUnDQovLyBpbXBvcnQgVnVlIGZyb20gJ3Z1ZScNCmltcG9ydCBBdXRoQnV0dG9ucyBmcm9tICdAL21peGlucy9hdXRoLWJ1dHRvbnMnDQppbXBvcnQgYmltZGlhbG9nIGZyb20gJ0Avdmlld3MvUFJPL2NvbXBvbmVudC1saXN0L3Y0L2NvbXBvbmVudC9iaW1kaWFsb2cudnVlJw0KaW1wb3J0IGF4aW9zIGZyb20gJ2F4aW9zJw0KDQppbXBvcnQgc3lzVXNlVHlwZSBmcm9tICdAL2RpcmVjdGl2ZS9zeXMtdXNlLXR5cGUnDQppbXBvcnQgeyBjb21iaW5lVVJMIH0gZnJvbSAnQC91dGlscycNCmltcG9ydCB7IHRhYmxlUGFnZVNpemUgfSBmcm9tICdAL3ZpZXdzL1BSTy9zZXR0aW5nJw0KaW1wb3J0IHsgdjQgYXMgdXVpZHY0IH0gZnJvbSAndXVpZCcNCmltcG9ydCB7IGJhc2VVcmwgfSBmcm9tICdAL3V0aWxzL2Jhc2V1cmwnDQppbXBvcnQgeyBmaW5kRmlyc3ROb2RlIH0gZnJvbSAnQC91dGlscy90cmVlJw0KaW1wb3J0IHsgR2V0U3RvcExpc3QgfSBmcm9tICdAL2FwaS9QUk8vcHJvZHVjdGlvbi10YXNrJw0KaW1wb3J0IExvY2F0aW9uSW1wb3J0IGZyb20gJ0Avdmlld3MvUFJPL2NvbXBvbmVudC1saXN0L3Y0L2NvbXBvbmVudC9Mb2NhdGlvbkltcG9ydC52dWUnDQppbXBvcnQgRXhwYW5kYWJsZVNlY3Rpb24gZnJvbSAnQC9jb21wb25lbnRzL0V4cGFuZGFibGVTZWN0aW9uL2luZGV4LnZ1ZScNCmltcG9ydCBUcmFjZVBsb3QgZnJvbSAnQC92aWV3cy9QUk8vY29tcG9uZW50LWxpc3QvdjQvY29tcG9uZW50L1RyYWNlUGxvdC52dWUnDQppbXBvcnQgbnVtZXJhbCBmcm9tICdudW1lcmFsJw0KaW1wb3J0IHsgR2V0Qk9NSW5mbyB9IGZyb20gJ0Avdmlld3MvUFJPL2JvbS1zZXR0aW5nL3V0aWxzJw0KaW1wb3J0IHsgbWFwR2V0dGVycyB9IGZyb20gJ3Z1ZXgnDQoNCmltcG9ydCBtb2RlbERyYXdpbmcgZnJvbSAnQC92aWV3cy9QUk8vY29tcG9uZW50cy9tb2RlbERyYXdpbmcudnVlJw0KLy8gVnVlLnVzZShIZWFkZXIpLnVzZShDb2x1bW4pLnVzZShUb29sdGlwKS51c2UoVGFibGUpDQpjb25zdCBTUExJVF9TWU1CT0wgPSAnJF8kJw0KZXhwb3J0IGRlZmF1bHQgew0KICBkaXJlY3RpdmVzOiB7IGVsRHJhZ0RpYWxvZywgc3lzVXNlVHlwZSB9LA0KICBjb21wb25lbnRzOiB7DQogICAgRXhwYW5kYWJsZVNlY3Rpb24sDQogICAgTG9jYXRpb25JbXBvcnQsDQogICAgVHJlZURldGFpbCwNCiAgICBUb3BIZWFkZXIsDQogICAgY29tSW1wb3J0LA0KICAgIGNvbUltcG9ydEJ5RmFjdG9yeSwNCiAgICBCYXRjaEVkaXQsDQogICAgSGlzdG9yeUV4cG9ydCwNCiAgICBHZW5lcmF0ZVBhY2ssDQogICAgRWRpdCwNCiAgICBDb21wb25lbnRQYWNrLA0KICAgIE9uZUNsaWNrR2VuZXJhdGVQYWNrLA0KICAgIFBhZ2luYXRpb24sDQogICAgYmltZGlhbG9nLA0KICAgIENvbXBvbmVudHNIaXN0b3J5LA0KICAgIFByb2R1Y3Rpb25Db25maXJtLA0KICAgIFBhcnRMaXN0LA0KICAgIFN0ZWVsTWVhbnMsDQogICAgTW9kZWxDb21wb25lbnRDb2RlLA0KICAgIFByb2R1Y3Rpb25EZXRhaWxzLA0KICAgIE1vZGVsTGlzdEltcG9ydCwNCiAgICBjb21EcmF3ZGlhbG9nLA0KICAgIFRyYWNlUGxvdCwNCiAgICBtb2RlbERyYXdpbmcsDQogICAgUHJvY2Vzc0RhdGENCiAgfSwNCiAgbWl4aW5zOiBbQXV0aEJ1dHRvbnNdLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBhbGxTdG9wRmxhZzogZmFsc2UsDQogICAgICBzaG93RXhwYW5kOiB0cnVlLA0KICAgICAgaXNBdXRvU3BsaXQ6IHVuZGVmaW5lZCwNCiAgICAgIHRhYmxlUGFnZVNpemU6IHRhYmxlUGFnZVNpemUsDQogICAgICBzeW5jVmlzaWJsZTogZmFsc2UsDQogICAgICBzeW5jRm9ybTogew0KICAgICAgICBJc19TeW5jX1RvX1BhcnQ6IG51bGwNCiAgICAgIH0sDQogICAgICBzeW5jUnVsZXM6IHsNCiAgICAgICAgSXNfU3luY19Ub19QYXJ0OiB7DQogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgbWVzc2FnZTogJ+ivt+mAieaLqeaYr+WQpuWQjOatpeWIsOebuOWFs+mbtuS7ticsDQogICAgICAgICAgdHJpZ2dlcjogJ2NoYW5nZScNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIHRyZWVTZWxlY3RQYXJhbXM6IHsNCiAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fpgInmi6knLA0KICAgICAgICBjbGVhcmFibGU6IHRydWUNCiAgICAgIH0sDQogICAgICBPYmplY3RUeXBlTGlzdDogew0KICAgICAgICAvLyDmnoTku7bnsbvlnosNCiAgICAgICAgJ2NoZWNrLXN0cmljdGx5JzogdHJ1ZSwNCiAgICAgICAgJ2RlZmF1bHQtZXhwYW5kLWFsbCc6IHRydWUsDQogICAgICAgIGNsaWNrUGFyZW50OiB0cnVlLA0KICAgICAgICBkYXRhOiBbXSwNCiAgICAgICAgcHJvcHM6IHsNCiAgICAgICAgICBjaGlsZHJlbjogJ0NoaWxkcmVuJywNCiAgICAgICAgICBsYWJlbDogJ0xhYmVsJywNCiAgICAgICAgICB2YWx1ZTogJ0RhdGEnDQogICAgICAgIH0NCiAgICAgIH0sDQoNCiAgICAgIHRyZWVEYXRhOiBbXSwNCiAgICAgIHRyZWVMb2FkaW5nOiB0cnVlLA0KICAgICAgZXhwYW5kZWRLZXk6ICcnLCAvLyAtMeaYr+WFqOmDqA0KICAgICAgcHJvamVjdE5hbWU6ICcnLA0KICAgICAgc3RhdHVzVHlwZTogJycsDQogICAgICBzZWFyY2hIZWlnaHQ6IDAsDQogICAgICBzZWFyY2hTdGF0dXM6IHRydWUsDQogICAgICB0YkRhdGE6IFtdLA0KICAgICAgdG90YWw6IDAsDQogICAgICB0YkxvYWRpbmc6IGZhbHNlLA0KICAgICAgcGdMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHF1ZXJ5SW5mbzogew0KICAgICAgICBQYWdlOiAxLA0KICAgICAgICBQYWdlU2l6ZTogMTAsDQogICAgICAgIFBhcmFtZXRlckpzb246IFtdDQogICAgICB9LA0KICAgICAgY3VzdG9tUGFnZVNpemU6IFsxMCwgMjAsIDUwLCAxMDBdLA0KICAgICAgaW5zdGFsbFVuaXRJZE5hbWVMaXN0OiBbXSwgLy8g5om55qyh5pWw57uEDQogICAgICBuYW1lTW9kZTogMSwNCiAgICAgIG5hbWVzOiAnJywNCiAgICAgIGN1c3RvbVBhcmFtczogew0KICAgICAgICBDb2RlX0xpa2U6ICcnLA0KICAgICAgICBTcGVjOiAnJywNCiAgICAgICAgVGV4dHVyZTogJycsDQogICAgICAgIElzX0RpcmVjdDogJycsDQogICAgICAgIENyZWF0ZV9Vc2VyTmFtZTogJycsDQogICAgICAgIEluc3RhbGxVbml0X0lkczogW10sDQogICAgICAgIFN0ZWVsTmFtZXM6ICcnLA0KICAgICAgICBUeXBlSWQ6ICcnLA0KICAgICAgICBTeXNfUHJvamVjdF9JZDogJycsDQogICAgICAgIFByb2plY3RfSWQ6ICcnLA0KICAgICAgICBBcmVhX0lkOiAnJywNCiAgICAgICAgUHJvamVjdF9OYW1lOiAnJywNCiAgICAgICAgU3RlZWxDb2RlOiAnJywNCiAgICAgICAgU3RlZWxOdW1iZXI6ICcnLA0KICAgICAgICBBcmVhX05hbWU6ICcnDQogICAgICB9LA0KICAgICAgVW5pdDogJycsDQogICAgICBQcm9wb3J0aW9uOiAwLCAvLyDkuJPkuJrnmoTljZXkvY3mjaLnrpcNCiAgICAgIGN1c3RvbURpYWxvZ1BhcmFtczoge30sDQogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGN1cnJlbnRDb21wb25lbnQ6ICcnLA0KICAgICAgc2VsZWN0TGlzdDogW10sDQogICAgICBmYWN0b3J5T3B0aW9uOiBbXSwNCiAgICAgIHByb2plY3RMaXN0OiBbXSwNCiAgICAgIHR5cGVPcHRpb246IFtdLA0KICAgICAgdHJlZVBhcmFtc1N0ZWVsOiBbXSwNCiAgICAgIGNvbHVtbnM6IFtdLA0KICAgICAgY29sdW1uc09wdGlvbjogWw0KICAgICAgICAvLyB7IERpc3BsYXlfTmFtZTogIuaehOS7tuWQjeensCIsIENvZGU6ICJTdGVlbE5hbWUiIH0sDQogICAgICAgIC8vIHsgRGlzcGxheV9OYW1lOiAi6KeE5qC8IiwgQ29kZTogIlN0ZWVsU3BlYyIgfSwNCiAgICAgICAgLy8geyBEaXNwbGF5X05hbWU6ICLplb/luqYiLCBDb2RlOiAiU3RlZWxMZW5ndGgiIH0sDQogICAgICAgIC8vIHsgRGlzcGxheV9OYW1lOiAi5p6E5Lu257G75Z6LIiwgQ29kZTogIlN0ZWVsVHlwZSIgfSwNCiAgICAgICAgLy8geyBEaXNwbGF5X05hbWU6ICLmnZDotKgiLCBDb2RlOiAiU3RlZWxNYXRlcmlhbCIgfSwNCiAgICAgICAgLy8geyBEaXNwbGF5X05hbWU6ICLmt7HljJbmlbDph48iLCBDb2RlOiAiU3RlZWxBbW91bnQiIH0sDQogICAgICAgIC8vIHsgRGlzcGxheV9OYW1lOiAi5o6S5Lqn5pWw6YePIiwgQ29kZTogIlNjaGVkdWxpbmdOdW0iIH0sDQogICAgICAgIC8vIHsgRGlzcGxheV9OYW1lOiAi5Y2V6YeNIiwgQ29kZTogIlN0ZWVsV2VpZ2h0IiB9LA0KICAgICAgICAvLyB7IERpc3BsYXlfTmFtZTogIuaAu+mHjSIsIENvZGU6ICJTdGVlbEFsbFdlaWdodCIgfSwNCiAgICAgICAgLy8geyBEaXNwbGF5X05hbWU6ICLnm7Tlj5Hku7YiLCBDb2RlOiAiSXNfQ29tcG9uZW50X1N0YXR1cyIgfSwNCiAgICAgICAgLy8geyBEaXNwbGF5X05hbWU6ICLmk43kvZzkuroiLCBDb2RlOiAiQ3JlYXRlX1VzZXJOYW1lIiB9LA0KICAgICAgICAvLyB7IERpc3BsYXlfTmFtZTogIuaTjeS9nOaXtumXtCIsIENvZGU6ICJDcmVhdGVfRGF0ZSIgfSwNCiAgICAgIF0sDQogICAgICB0aXRsZTogJycsDQogICAgICB3aWR0aDogJzYwJScsDQogICAgICB0aXBMYWJlbDogJycsDQogICAgICBtb25vbWVyTGlzdDogW10sDQogICAgICBtb2RlOiAnJywNCiAgICAgIGlzTW9ub21lcjogdHJ1ZSwNCiAgICAgIGhpc3RvcnlWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHN5c1VzZVR5cGU6IHVuZGVmaW5lZCwNCiAgICAgIHByb2R1Y3Rpb25Db25maXJtOiAnJywNCiAgICAgIFN0ZWVsRm9ybUVkaXREYXRhOiB7fSwNCiAgICAgIGRlZXBlblRvdGFsTGVuZ3RoOiAwLCAvLyDmt7HljJbmgLvph48NCiAgICAgIFN0ZWVsQW1vdW50VG90YWw6IDAsIC8vIOa3seWMluaAu+mHjw0KICAgICAgU2NoZWR1bGluZ051bVRvdGFsOiAwLCAvLyDmjpLkuqfmgLvph48NCiAgICAgIFN0ZWVsQWxsV2VpZ2h0VG90YWw6IDAsIC8vIOa3seWMluaAu+mHjQ0KICAgICAgU2NoZWR1bGluZ0FsbFdlaWdodFRvdGFsOiAwLCAvLyDmjpLkuqfmgLvph40NCiAgICAgIEZpbmlzaENvdW50VG90YWw6IDAsIC8vIOWujOaIkOaVsOmHjw0KICAgICAgRmluaXNoV2VpZ2h0VG90YWw6IDAsIC8vIOWujOaIkOmHjemHjw0KICAgICAgSXNDb21wb25lbnRUb3RhbDogMCwNCiAgICAgIFRvdGFsR3Jvc3NXZWlnaHQ6IDAsDQogICAgICBJc0NvbXBvbmVudFRvdGFsU3RlZWxBbGxXZWlnaHQ6IDAsDQogICAgICBsZWZ0Q29sOiA0LA0KICAgICAgcmlnaHRDb2w6IDQwLA0KICAgICAgbGVmdFdpZHRoOiAzMjAsDQogICAgICBkcmF3ZXI6IGZhbHNlLA0KICAgICAgc2NoZWR1bGVMb2FkaW5nOiBmYWxzZSwNCiAgICAgIGNvbW1hbmQ6ICdjb3ZlcicsIC8vICBjb3ZlcuimhuebluWvvOWFpSAgYWRk5paw5aKe5a+85YWlDQogICAgICBjdXJyZW50TGFzdExldmVsOiBmYWxzZSwgLy8gIOW9k+WJjeWMuuWfn+aYr+WQpuaYr+acgOWGheWxgg0KICAgICAgY2FkUm93Q29kZTogJycsDQogICAgICBjYWRSb3dQcm9qZWN0SWQ6ICcnLA0KICAgICAgSXNVcGxvYWRDYWQ6IGZhbHNlLA0KICAgICAgY29tRHJhd0RhdGE6IHt9LA0KICAgICAgY3VycmVudE5vZGU6IHt9LA0KICAgICAgdHJhY2tEcmF3ZXI6IGZhbHNlLA0KICAgICAgdHJhY2tEcmF3ZXJUaXRsZTogJycsDQogICAgICB0cmFja0RyYXdlckRhdGE6IHt9LA0KICAgICAgbGV2ZWxOYW1lOiAnJywNCiAgICAgIGxldmVsQ29kZTogJycNCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgLi4ubWFwR2V0dGVycygndGVuYW50JywgWydpc1ZlcnNpb25Gb3VyJ10pLA0KICAgIHR5cGVFbnRpdHkoKSB7DQogICAgICByZXR1cm4gdGhpcy50eXBlT3B0aW9uLmZpbmQoKGkpID0+IGkuSWQgPT09IHRoaXMuY3VzdG9tUGFyYW1zLlR5cGVJZCkNCiAgICB9LA0KICAgIHNob3dUb3RhbExlbmd0aCgpIHsNCiAgICAgIGNvbnN0IGFyciA9IFsNCiAgICAgICAgdGhpcy5jdXN0b21QYXJhbXMuRnV6enlfU2VhcmNoX0NvbCwNCiAgICAgICAgdGhpcy5jdXN0b21QYXJhbXMuRnV6enlfU2VhcmNoX0NvbDIsDQogICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLkZ1enp5X1NlYXJjaF9Db2wzLA0KICAgICAgICB0aGlzLmN1c3RvbVBhcmFtcy5GdXp6eV9TZWFyY2hfQ29sNA0KICAgICAgXQ0KICAgICAgcmV0dXJuIGFyci5pbmNsdWRlcygnU3RlZWxMZW5ndGgnKSAmJiBhcnIuaW5jbHVkZXMoJ1N0ZWVsU3BlYycpDQogICAgfSwNCiAgICBmaWx0ZXJUZXh0KCkgew0KICAgICAgcmV0dXJuIHRoaXMucHJvamVjdE5hbWUgKyBTUExJVF9TWU1CT0wgKyB0aGlzLnN0YXR1c1R5cGUNCiAgICB9LA0KICAgIFRvdGFsR3Jvc3NXZWlnaHRUKCkgew0KICAgICAgcmV0dXJuIG51bWVyYWwodGhpcy5Ub3RhbEdyb3NzV2VpZ2h0IHx8IDApLmZvcm1hdCgnMC5bMDAwXScpDQogICAgfQ0KICB9LA0KICB3YXRjaDogew0KICAgICdjdXN0b21QYXJhbXMuVHlwZUlkJzogZnVuY3Rpb24obmV3VmFsdWUsIG9sZFZhbHVlKSB7DQogICAgICBjb25zb2xlLmxvZyh7IG9sZFZhbHVlIH0pDQogICAgICBpZiAob2xkVmFsdWUgJiYgb2xkVmFsdWUgIT09ICcwJykgew0KICAgICAgICB0aGlzLmZldGNoRGF0YSgpDQogICAgICB9DQogICAgfSwNCiAgICBuYW1lcyhuLCBvKSB7DQogICAgICB0aGlzLmNoYW5nZU1vZGUoKQ0KICAgIH0sDQogICAgbmFtZU1vZGUobiwgbykgew0KICAgICAgdGhpcy5jaGFuZ2VNb2RlKCkNCiAgICB9DQogIH0sDQogIGFzeW5jIGNyZWF0ZWQoKSB7DQogICAgY29uc3QgeyBjdXJyZW50Qk9NSW5mbyB9ID0gYXdhaXQgR2V0Qk9NSW5mbygtMSkNCiAgICBjb25zb2xlLmxvZygnbGlzdCcsIGN1cnJlbnRCT01JbmZvKQ0KICAgIHRoaXMubGV2ZWxOYW1lID0gY3VycmVudEJPTUluZm8/LkRpc3BsYXlfTmFtZQ0KICAgIHRoaXMubGV2ZWxDb2RlID0gY3VycmVudEJPTUluZm8/LkNvZGUNCiAgICBhd2FpdCB0aGlzLmdldFByZWZlcmVuY2VTZXR0aW5nVmFsdWUoKQ0KICAgIGF3YWl0IHRoaXMuZ2V0VHlwZUxpc3QoKQ0KICAgIC8vIGF3YWl0IHRoaXMuZmV0Y2hEYXRhKCkNCiAgICAvLyBhd2FpdCB0aGlzLmdldENvbXBvbmVudFN1bW1hcnlJbmZvKCkNCiAgICB0aGlzLmZldGNoVHJlZURhdGEoKQ0KICAgIHRoaXMuZ2V0RmlsZVR5cGUoKQ0KICB9LA0KICBtb3VudGVkKCkgew0KICB9LA0KICBhY3RpdmF0ZWQoKSB7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBjaGFuZ2VNb2RlKG4pIHsNCiAgICAgIGlmICh0aGlzLm5hbWVNb2RlID09PSAxKSB7DQogICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLkNvZGVfTGlrZSA9IHRoaXMubmFtZXMNCiAgICAgICAgdGhpcy5jdXN0b21QYXJhbXMuU3RlZWxOYW1lcyA9ICcnDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmN1c3RvbVBhcmFtcy5Db2RlX0xpa2UgPSAnJw0KICAgICAgICB0aGlzLmN1c3RvbVBhcmFtcy5TdGVlbE5hbWVzID0gdGhpcy5uYW1lcy5yZXBsYWNlKC9ccysvZywgJ1xuJykNCiAgICAgIH0NCiAgICB9LA0KDQogICAgZ2V0Q29tcG9uZW50SW5mbyhyb3cpIHsNCiAgICAgIGNvbnN0IGRyYXdpbmdEYXRhID0gcm93LkRyYXdpbmcgPyByb3cuRHJhd2luZy5zcGxpdCgnLCcpIDogW10gLy8g5Zu+57q45pWw5o2uDQogICAgICBjb25zdCBmaWxlVXJsRGF0YSA9IHJvdy5GaWxlX1VybCA/IHJvdy5GaWxlX1VybC5zcGxpdCgnLCcpIDogW10gLy8g5Zu+57q45pWw5o2u5paH5Lu25Zyw5Z2A5pWw5o2uDQogICAgICBpZiAoZHJhd2luZ0RhdGEubGVuZ3RoID4gMCAmJiBmaWxlVXJsRGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMuZHJhd2luZ0FjdGl2ZSA9IGRyYXdpbmdEYXRhWzBdDQogICAgICB9DQogICAgICBpZiAoZHJhd2luZ0RhdGEubGVuZ3RoID4gMCAmJiBmaWxlVXJsRGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMuZHJhd2luZ0RhdGFMaXN0ID0gZHJhd2luZ0RhdGEubWFwKChpdGVtLCBpbmRleCkgPT4gKHsNCiAgICAgICAgICBuYW1lOiBpdGVtLA0KICAgICAgICAgIGxhYmVsOiBpdGVtLA0KICAgICAgICAgIHVybDogZmlsZVVybERhdGFbaW5kZXhdDQogICAgICAgIH0pKQ0KICAgICAgfQ0KICAgICAgdGhpcy5nZXRDb21wb25lbnRJbmZvRHJhd2luZyhyb3cpDQogICAgfSwNCg0KICAgIC8qKg0KICAgICAqIOiOt+WPlmZlYXR1cmVJZCDmqKHlnovmnoTku7ZpZCAgIGNhZElkIENBROWbvue6uGlkDQogICAgICovDQogICAgZ2V0Q29tcG9uZW50SW5mb0RyYXdpbmcocm93KSB7DQogICAgICBjb25zdCBpbXBvcnREZXRhaWxJZCA9IHJvdy5JZA0KICAgICAgR2V0U3RlZWxDYWRBbmRCaW1JZCh7IGltcG9ydERldGFpbElkOiBpbXBvcnREZXRhaWxJZCB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICBjb25zdCBfZGF0YSA9IHJlcy5EYXRhPy5bMF0NCiAgICAgICAgICBpZiAoIXJvdy5GaWxlX1VybCAmJiAhX2RhdGEuRXh0ZW5zaW9uTmFtZSkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6ICflvZPliY3mnoTku7bml6Dlm77nurjlkozmqKHlnosnLA0KICAgICAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICB9DQoNCiAgICAgICAgICBjb25zdCBkcmF3aW5nRGF0YSA9IHsNCiAgICAgICAgICAgICdleHRlbnNpb25OYW1lJzogX2RhdGEuRXh0ZW5zaW9uTmFtZSwNCiAgICAgICAgICAgICdmaWxlQmltJzogX2RhdGEuZmlsZUJpbSwNCiAgICAgICAgICAgICdJc1VwbG9hZCc6IF9kYXRhLklzVXBsb2FkLA0KICAgICAgICAgICAgJ0NvZGUnOiByb3cuU3RlZWxOYW1lLA0KICAgICAgICAgICAgJ1N5c19Qcm9qZWN0X0lkJzogcm93LlN5c19Qcm9qZWN0X0lkDQogICAgICAgICAgfQ0KDQogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKF8pID0+IHsNCiAgICAgICAgICAgIHRoaXMuJHJlZnMubW9kZWxEcmF3aW5nUmVmLmR3Z0luaXQoZHJhd2luZ0RhdGEpDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g6aG555uu5Yy65Z+f5pWw5o2u6ZuGDQogICAgZmV0Y2hUcmVlRGF0YSgpIHsNCiAgICAgIEdldFByb2plY3RBcmVhVHJlZUxpc3Qoew0KICAgICAgICBUeXBlOiAwLA0KICAgICAgICBCb21fTGV2ZWw6IHRoaXMubGV2ZWxDb2RlLA0KICAgICAgICBNZW51SWQ6IHRoaXMuJHJvdXRlLm1ldGEuSWQsDQogICAgICAgIHByb2plY3ROYW1lOiB0aGlzLnByb2plY3ROYW1lDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgLy8gY29uc3QgcmVzQWxsID0gWw0KICAgICAgICAvLyAgIHsNCiAgICAgICAgLy8gICAgIFBhcmVudE5vZGVzOiBudWxsLA0KICAgICAgICAvLyAgICAgSWQ6ICctMScsDQogICAgICAgIC8vICAgICBDb2RlOiAn5YWo6YOoJywNCiAgICAgICAgLy8gICAgIExhYmVsOiAn5YWo6YOoJywNCiAgICAgICAgLy8gICAgIExldmVsOiBudWxsLA0KICAgICAgICAvLyAgICAgRGF0YToge30sDQogICAgICAgIC8vICAgICBDaGlsZHJlbjogW10NCiAgICAgICAgLy8gICB9DQogICAgICAgIC8vIF0NCiAgICAgICAgLy8gY29uc3QgcmVzRGF0YSA9IHJlc0FsbC5jb25jYXQocmVzLkRhdGEpDQogICAgICAgIGlmIChyZXMuRGF0YS5sZW5ndGggPT09IDApIHsNCiAgICAgICAgICB0aGlzLnRyZWVMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KICAgICAgICBjb25zdCByZXNEYXRhID0gcmVzLkRhdGENCiAgICAgICAgcmVzRGF0YS5tYXAoKGl0ZW0pID0+IHsNCiAgICAgICAgICBpZiAoaXRlbS5DaGlsZHJlbi5sZW5ndGggPT09IDApIHsNCiAgICAgICAgICAgIGl0ZW0uRGF0YS5Jc19JbXBvcnRlZCA9IGZhbHNlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGl0ZW0uRGF0YS5Jc19JbXBvcnRlZCA9IGl0ZW0uQ2hpbGRyZW4uc29tZSgoaWNoKSA9PiB7DQogICAgICAgICAgICAgIHJldHVybiBpY2guRGF0YS5Jc19JbXBvcnRlZCA9PT0gdHJ1ZQ0KICAgICAgICAgICAgfSkNCg0KICAgICAgICAgICAgaXRlbS5Jc19EaXJlY3RvcnkgPSB0cnVlDQogICAgICAgICAgICBpdGVtLkNoaWxkcmVuLm1hcCgoaXQpID0+IHsNCiAgICAgICAgICAgICAgaWYgKGl0LkNoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgICBpdC5Jc19EaXJlY3RvcnkgPSB0cnVlDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICAgIHJldHVybiBpdGVtDQogICAgICAgIH0pDQogICAgICAgIHRoaXMudHJlZURhdGEgPSByZXNEYXRhDQogICAgICAgIGlmIChPYmplY3Qua2V5cyh0aGlzLmN1cnJlbnROb2RlKS5sZW5ndGggPT09IDApIHsNCiAgICAgICAgICB0aGlzLnNldEtleSgpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5oYW5kbGVOb2RlQ2xpY2sodGhpcy5jdXJyZW50Tm9kZSkNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLnRyZWVMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyDorr7nva7pu5jorqTpgInkuK3nrKzkuIDkuKrljLrln5/mnKvnuqfoioLngrkNCiAgICBzZXRLZXkoKSB7DQogICAgICBjb25zdCBkZWVwRmlsdGVyID0gKHRyZWUpID0+IHsNCiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0cmVlLmxlbmd0aDsgaSsrKSB7DQogICAgICAgICAgY29uc3QgaXRlbSA9IHRyZWVbaV0NCiAgICAgICAgICBjb25zdCB7IERhdGEsIENoaWxkcmVuIH0gPSBpdGVtDQogICAgICAgICAgY29uc29sZS5sb2coRGF0YSkNCiAgICAgICAgICBpZiAoRGF0YS5QYXJlbnRJZCAmJiAhQ2hpbGRyZW4/Lmxlbmd0aCkgew0KICAgICAgICAgICAgY29uc29sZS5sb2coRGF0YSwgJz8/Pz8nKQ0KICAgICAgICAgICAgdGhpcy5jdXJyZW50Tm9kZSA9IERhdGENCiAgICAgICAgICAgIHRoaXMuaGFuZGxlTm9kZUNsaWNrKGl0ZW0pDQogICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgaWYgKENoaWxkcmVuICYmIENoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgcmV0dXJuIGRlZXBGaWx0ZXIoQ2hpbGRyZW4pDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLmhhbmRsZU5vZGVDbGljayhpdGVtKQ0KICAgICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHJldHVybiBkZWVwRmlsdGVyKHRoaXMudHJlZURhdGEpDQogICAgfSwNCiAgICAvLyDpgInkuK3lt6bkvqfpobnnm67oioLngrkNCiAgICBoYW5kbGVOb2RlQ2xpY2soZGF0YSkgew0KICAgICAgdGhpcy5oYW5kbGVTZWFyY2goJ3Jlc2V0JywgZmFsc2UsICdkZWZhdWx0JykNCiAgICAgIHRoaXMuY3VycmVudE5vZGUgPSBkYXRhDQogICAgICB0aGlzLmV4cGFuZGVkS2V5ID0gZGF0YS5JZA0KICAgICAgdGhpcy4kbmV4dFRpY2soKF8pID0+IHsNCiAgICAgICAgY29uc3QgY3VyID0gdGhpcy4kcmVmc1sndHJlZSddLiRyZWZzLnRyZWUuZ2V0Tm9kZSh0aGlzLmV4cGFuZGVkS2V5KQ0KICAgICAgICBpZiAoY3VyKSB7DQogICAgICAgICAgdGhpcy5pc0F1dG9TcGxpdCA9IGN1cj8uZGF0YS5EYXRhLklzX0F1dG9fU3BsaXQNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIGNvbnNvbGUubG9nKGRhdGEsICdkYXRhMj09PT09PT09PT09PScpDQogICAgICB0aGlzLkluc3RhbGxVbml0X0lkID0gJycNCiAgICAgIGlmIChkYXRhLlBhcmVudE5vZGVzID09PSBudWxsICYmIGRhdGEuQ29kZSAhPT0gJ+WFqOmDqCcpIHsNCiAgICAgICAgdGhpcy5jdXN0b21QYXJhbXMuU3lzX1Byb2plY3RfSWQgPSBkYXRhLkRhdGEuU3lzX1Byb2plY3RfSWQNCiAgICAgICAgdGhpcy5jdXN0b21QYXJhbXMuUHJvamVjdF9JZCA9IGRhdGEuRGF0YS5JZA0KICAgICAgICB0aGlzLmN1c3RvbVBhcmFtcy5BcmVhX05hbWUgPSAnJw0KICAgICAgICB0aGlzLmN1c3RvbVBhcmFtcy5BcmVhX0lkID0gJycNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLlN5c19Qcm9qZWN0X0lkID0gZGF0YS5EYXRhLlN5c19Qcm9qZWN0X0lkDQogICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLlByb2plY3RfSWQgPSBkYXRhLkRhdGEuUHJvamVjdF9JZA0KICAgICAgICB0aGlzLmN1c3RvbVBhcmFtcy5BcmVhX0lkID0gZGF0YS5EYXRhLklkDQogICAgICB9DQogICAgICB0aGlzLmlzQXV0b1NwbGl0ID0gZGF0YS5EYXRhPy5Jc19BdXRvX1NwbGl0DQogICAgICB0aGlzLmN1cnJlbnRMYXN0TGV2ZWwgPSAhIShkYXRhLkRhdGEuTGV2ZWwgJiYgZGF0YS5DaGlsZHJlbi5sZW5ndGggPT09IDApDQogICAgICBpZiAodGhpcy5jdXJyZW50TGFzdExldmVsKSB7DQogICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLlByb2plY3RfTmFtZSA9IGRhdGEuRGF0YT8uUHJvamVjdF9OYW1lDQogICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLkFyZWFfTmFtZSA9IGRhdGEuTGFiZWwNCiAgICAgIH0NCiAgICAgIGNvbnN0IGRhdGFJRCA9IGRhdGEuSWQgPT09IC0xID8gJycgOiBkYXRhLklkDQogICAgICBjb25zb2xlLmxvZygNCiAgICAgICAgdGhpcy5jdXN0b21QYXJhbXMuU3lzX1Byb2plY3RfSWQsDQogICAgICAgICd0aGlzLmN1c3RvbVBhcmFtcy5TeXNfUHJvamVjdF9JZD09PT09PT09PT09PTExMTExJw0KICAgICAgKQ0KICAgICAgY29uc29sZS5sb2coDQogICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLkFyZWFfSWQsDQogICAgICAgICd0aGlzLmN1c3RvbVBhcmFtcy5BcmVhX0lkPT09PT09PT09PT09MTExMTEnDQogICAgICApDQogICAgICBjb25zb2xlLmxvZygNCiAgICAgICAgdGhpcy5jdXN0b21QYXJhbXMuUHJvamVjdF9JZCwNCiAgICAgICAgJ3RoaXMuY3VzdG9tUGFyYW1zLlByb2plY3RfSWQ9PT09PT09PT09PT0xMTExMScNCiAgICAgICkNCiAgICAgIHRoaXMucGdMb2FkaW5nID0gdHJ1ZQ0KICAgICAgdGhpcy5nZXRJbnN0YWxsVW5pdElkTmFtZUxpc3QoZGF0YUlELCBkYXRhKQ0KICAgICAgdGhpcy5mZXRjaERhdGEoKQ0KICAgICAgdGhpcy5nZXRDb21wb25lbnRTdW1tYXJ5SW5mbygpDQogICAgfSwNCg0KICAgIC8vIOiOt+WPluaJueasoQ0KICAgIGdldEluc3RhbGxVbml0SWROYW1lTGlzdChpZCwgZGF0YSkgew0KICAgICAgY29uc29sZS5sb2coZGF0YSwgJz8/Pz8/Pz8/Pz8/JykNCiAgICAgIGlmIChpZCA9PT0gJycgfHwgZGF0YS5DaGlsZHJlbi5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMuaW5zdGFsbFVuaXRJZE5hbWVMaXN0ID0gW10NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIEdldEluc3RhbGxVbml0SWROYW1lTGlzdCh7IEFyZWFfSWQ6IGlkIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIHRoaXMuaW5zdGFsbFVuaXRJZE5hbWVMaXN0ID0gcmVzLkRhdGENCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5pCc57SiDQogICAgaGFuZGxlU2VhcmNoKHJlc2V0LCBoYXNTZWFyY2ggPSB0cnVlLCB0eXBlID0gJycpIHsNCiAgICAgIHRoaXMuc2VhcmNoU3RhdHVzID0gZmFsc2UNCiAgICAgIGlmIChyZXNldCkgew0KICAgICAgICB0aGlzLiRyZWZzLmN1c3RvbVBhcmFtcy5yZXNldEZpZWxkcygpDQogICAgICAgIHRoaXMubmFtZXMgPSAnJw0KICAgICAgICAvLyB0aGlzLmN1c3RvbVBhcmFtcy5GdXp6eV9TZWFyY2hfQ29sID0gJ1N0ZWVsTmFtZScNCiAgICAgICAgLy8gdGhpcy5jdXN0b21QYXJhbXMuRnV6enlfU2VhcmNoX0NvbDIgPSAnU3RlZWxNYXRlcmlhbCcNCiAgICAgICAgLy8gdGhpcy5jdXN0b21QYXJhbXMuRnV6enlfU2VhcmNoX0NvbDMgPSAnU3RlZWxTcGVjJw0KICAgICAgICAvLyB0aGlzLmN1c3RvbVBhcmFtcy5GdXp6eV9TZWFyY2hfQ29sNCA9ICdTdGVlbFdlaWdodCcNCiAgICAgICAgdGhpcy5zZWFyY2hTdGF0dXMgPSB0cnVlDQogICAgICB9DQogICAgICAvLyBsZXQgU3RlZWxOYW1lcyA9IHRoaXMuY3VzdG9tUGFyYW1zLlN0ZWVsTmFtZXNGb3JtYXQudHJpbSgpDQogICAgICAvLyBTdGVlbE5hbWVzID0gU3RlZWxOYW1lcy5yZXBsYWNlKC9ccysvZywgJ1xuJykNCiAgICAgIC8vIHRoaXMuY3VzdG9tUGFyYW1zLlN0ZWVsTmFtZXMgPSBTdGVlbE5hbWVzDQoNCiAgICAgIGhhc1NlYXJjaCAmJiB0aGlzLmZldGNoRGF0YSgpDQogICAgICBpZiAodHlwZSA9PT0gJycpIHsNCiAgICAgICAgdGhpcy5nZXRDb21wb25lbnRTdW1tYXJ5SW5mbygpDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOiOt+WPluezu+e7n+WBj+Wlve+8jOaYr+WQpuW8ueWHuueUn+S6p+euoeeQhui/h+eoi+eahOW8ueahhg0KICAgIGFzeW5jIGdldFByZWZlcmVuY2VTZXR0aW5nVmFsdWUoKSB7DQogICAgICBHZXRQcmVmZXJlbmNlU2V0dGluZ1ZhbHVlKHsgQ29kZTogJ1Byb2R1Y3Rpb25fQ29uZmlybScgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMucHJvZHVjdGlvbkNvbmZpcm0gPSByZXMuRGF0YQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5p6E5Lu257uf6K6hDQogICAgZ2V0Q29tcG9uZW50U3VtbWFyeUluZm8oKSB7DQogICAgICBHZXRDb21wb25lbnRTdW1tYXJ5SW5mbyh7DQogICAgICAgIC4uLnRoaXMuY3VzdG9tUGFyYW1zDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgICB0aGlzLlN0ZWVsQW1vdW50VG90YWwgPSBNYXRoLnJvdW5kKHJlcy5EYXRhLkRlZXBlbk51bSAqIDEwMDApIC8gMTAwMCAvLyDmt7HljJbmgLvph48NCiAgICAgICAgICB0aGlzLlNjaGVkdWxpbmdOdW1Ub3RhbCA9DQogICAgICAgICAgICBNYXRoLnJvdW5kKHJlcy5EYXRhLlNjaGVkdWxpbmdOdW0gKiAxMDAwKSAvIDEwMDAgLy8g5o6S5Lqn5oC76YePDQogICAgICAgICAgdGhpcy5TdGVlbEFsbFdlaWdodFRvdGFsID0NCiAgICAgICAgICAgIE1hdGgucm91bmQocmVzLkRhdGEuRGVlcGVuV2VpZ2h0ICogMTAwMCkgLyAxMDAwIC8vIOa3seWMluaAu+mHjQ0KICAgICAgICAgIHRoaXMuU2NoZWR1bGluZ0FsbFdlaWdodFRvdGFsID0NCiAgICAgICAgICAgIE1hdGgucm91bmQocmVzLkRhdGEuU2NoZWR1bGluZ1dlaWdodCAqIDEwMDApIC8gMTAwMCAvLyDmjpLkuqfmgLvph40NCiAgICAgICAgICB0aGlzLkZpbmlzaENvdW50VG90YWwgPQ0KICAgICAgICAgICAgTWF0aC5yb3VuZChyZXMuRGF0YS5GaW5pc2hfQ291bnQgKiAxMDAwKSAvIDEwMDAgLy8g5a6M5oiQ5oC75pWwDQogICAgICAgICAgdGhpcy5GaW5pc2hXZWlnaHRUb3RhbCA9DQogICAgICAgICAgICBNYXRoLnJvdW5kKHJlcy5EYXRhLkZpbmlzaF9XZWlnaHQgKiAxMDAwKSAvIDEwMDAgLy8g5a6M5oiQ5oC76YeNDQogICAgICAgICAgdGhpcy5Jc0NvbXBvbmVudFRvdGFsID0gcmVzLkRhdGEuRGlyZWN0X0NvdW50IHx8IDANCiAgICAgICAgICB0aGlzLlRvdGFsR3Jvc3NXZWlnaHQgPSByZXMuRGF0YS5Ub3RhbEdyb3NzV2VpZ2h0IHx8IDANCiAgICAgICAgICB0aGlzLklzQ29tcG9uZW50VG90YWxTdGVlbEFsbFdlaWdodCA9DQogICAgICAgICAgICBNYXRoLnJvdW5kKChyZXMuRGF0YS5EaXJlY3RfV2VpZ2h0IHx8IDApICogMTAwMCkgLyAxMDAwDQogICAgICAgICAgdGhpcy5hbGxTdG9wRmxhZyA9ICEhcmVzLkRhdGEuSXNfU3RvcA0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5bel5bqP5a6M5oiQ6YePDQogICAgZ2V0UHJvY2Vzc0RhdGEoKSB7DQogICAgICB0aGlzLndpZHRoID0gJzQwJScNCiAgICAgIHRoaXMuZ2VuZXJhdGVDb21wb25lbnQoJ+aehOS7tuW3peW6j+WujOaIkOmHjycsICdQcm9jZXNzRGF0YScpDQogICAgICB0aGlzLiRuZXh0VGljaygoXykgPT4gew0KICAgICAgICB0aGlzLiRyZWZzWydjb250ZW50J10uaW5pdCh0aGlzLmN1c3RvbVBhcmFtcywgdGhpcy5zZWxlY3RMaXN0Lm1hcCgodikgPT4gdi5JZCkudG9TdHJpbmcoKSkNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOiOt+WPluihqOagvOmFjee9rg0KICAgIGdldFRhYmxlQ29uZmlnKGNvZGUpIHsNCiAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4gew0KICAgICAgICBHZXRHcmlkQnlDb2RlKHsNCiAgICAgICAgICBjb2RlOg0KICAgICAgICAgICAgY29kZSArDQogICAgICAgICAgICAnLCcgKw0KICAgICAgICAgICAgdGhpcy50eXBlT3B0aW9uLmZpbmQoKGkpID0+IGkuSWQgPT09IHRoaXMuY3VzdG9tUGFyYW1zLlR5cGVJZCkuQ29kZQ0KICAgICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICBjb25zdCB7IElzU3VjY2VlZCwgRGF0YSwgTWVzc2FnZSB9ID0gcmVzDQogICAgICAgICAgaWYgKElzU3VjY2VlZCkgew0KICAgICAgICAgICAgaWYgKCFEYXRhKSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+W9k+WJjeS4k+S4muayoeaciemFjee9ruebuOWvueW6lOihqOagvCcpDQogICAgICAgICAgICAgIHRoaXMudGJMb2FkaW5nID0gdHJ1ZQ0KICAgICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHRoaXMudGJDb25maWcgPSBPYmplY3QuYXNzaWduKHt9LCB0aGlzLnRiQ29uZmlnLCBEYXRhLkdyaWQpDQogICAgICAgICAgICBjb25zdCBsaXN0ID0gRGF0YS5Db2x1bW5MaXN0IHx8IFtdDQogICAgICAgICAgICB0aGlzLmNvbHVtbnMgPSBsaXN0DQogICAgICAgICAgICAgIC5maWx0ZXIoKHYpID0+IHYuSXNfRGlzcGxheSkNCiAgICAgICAgICAgICAgLm1hcCgoaXRlbSkgPT4gew0KICAgICAgICAgICAgICAgIGlmIChpdGVtLkNvZGUgPT09ICdTdGVlbE5hbWUnKSB7DQogICAgICAgICAgICAgICAgICBpdGVtLmZpeGVkID0gJ2xlZnQnDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIHJldHVybiBpdGVtDQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICB0aGlzLnF1ZXJ5SW5mby5QYWdlU2l6ZSA9ICtEYXRhLkdyaWQuUm93X051bWJlciB8fCAyMA0KDQogICAgICAgICAgICBjb25zdCBzZWxlY3RPcHRpb24gPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMuY29sdW1ucykpDQoNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKHNlbGVjdE9wdGlvbikNCiAgICAgICAgICAgIHRoaXMuY29sdW1uc09wdGlvbiA9IHNlbGVjdE9wdGlvbi5maWx0ZXIoKHYpID0+IHsNCiAgICAgICAgICAgICAgcmV0dXJuICgNCiAgICAgICAgICAgICAgICB2LkRpc3BsYXlfTmFtZSAhPT0gJ+aTjeS9nOaXtumXtCcgJiYNCiAgICAgICAgICAgICAgICB2LkRpc3BsYXlfTmFtZSAhPT0gJ+WuieijheS9jee9ricgJiYNCiAgICAgICAgICAgICAgICB2LkRpc3BsYXlfTmFtZSAhPT0gJ+aooeWei0lEJyAmJg0KICAgICAgICAgICAgICAgIHYuRGlzcGxheV9OYW1lICE9PSAn5rex5YyW6LWE5paZJyAmJg0KICAgICAgICAgICAgICAgIHYuRGlzcGxheV9OYW1lICE9PSAn5aSH5rOoJyAmJg0KICAgICAgICAgICAgICAgIHYuRGlzcGxheV9OYW1lICE9PSAn6Zu25Lu2JyAmJg0KICAgICAgICAgICAgICAgIHYuRGlzcGxheV9OYW1lICE9PSAn5o6S5Lqn5pWw6YePJyAmJg0KICAgICAgICAgICAgICAgIHYuQ29kZS5pbmRleE9mKCdBdHRyJykgPT09IC0xICYmDQogICAgICAgICAgICAgICAgdi5EaXNwbGF5X05hbWUgIT09ICfmnoTku7bnsbvlnosnICYmDQogICAgICAgICAgICAgICAgdi5EaXNwbGF5X05hbWUgIT09ICfmibnmrKEnDQogICAgICAgICAgICAgICkNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICByZXNvbHZlKHRoaXMuY29sdW1ucykNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6IE1lc3NhZ2UsDQogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOaehOS7tuWIl+ihqA0KICAgIGFzeW5jIGdldENvbXBvbmVudEltcG9ydERldGFpbFBhZ2VMaXN0KCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0Q29tcG9uZW50SW1wb3J0RGV0YWlsUGFnZUxpc3Qoew0KICAgICAgICAgIC4uLnRoaXMucXVlcnlJbmZvLA0KICAgICAgICAgIC4uLnRoaXMuY3VzdG9tUGFyYW1zDQogICAgICAgIH0pDQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy50YkRhdGEgPSAocmVzLkRhdGEuRGF0YSB8fCBbXSkubWFwKCh2KSA9PiB7DQogICAgICAgICAgICB2LkNyZWF0ZV9EYXRlID0gdGltZUZvcm1hdCgNCiAgICAgICAgICAgICAgdi5DcmVhdGVfRGF0ZSwNCiAgICAgICAgICAgICAgJ3t5fS17bX0te2R9IHtofTp7aX06e3N9Jw0KICAgICAgICAgICAgKQ0KICAgICAgICAgICAgcmV0dXJuIHYNCiAgICAgICAgICB9KQ0KICAgICAgICAgIHRoaXMuZGVlcGVuVG90YWxMZW5ndGggPSByZXMuRGF0YS5EZWVwZW5Ub3RhbExlbmd0aCB8fCAwDQogICAgICAgICAgdGhpcy5xdWVyeUluZm8uUGFnZVNpemUgPSByZXMuRGF0YS5QYWdlU2l6ZQ0KICAgICAgICAgIHRoaXMudG90YWwgPSByZXMuRGF0YS5Ub3RhbENvdW50DQogICAgICAgICAgdGhpcy5zZWxlY3RMaXN0ID0gW10NCiAgICAgICAgICBhd2FpdCB0aGlzLmdldFN0b3BMaXN0KCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogJ+iOt+WPluaehOS7tuWIl+ihqOWksei0pScsDQogICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQogICAgYXN5bmMgZ2V0U3RvcExpc3QoKSB7DQogICAgICBpZiAoIXRoaXMudGJEYXRhIHx8ICF0aGlzLnRiRGF0YS5sZW5ndGgpIHJldHVybg0KICAgICAgY29uc3Qgc3VibWl0T2JqID0gdGhpcy50YkRhdGEubWFwKChpdGVtKSA9PiAoew0KICAgICAgICBJZDogaXRlbS5JZCwNCiAgICAgICAgVHlwZTogMiwNCiAgICAgICAgQm9tX0xldmVsOiB0aGlzLmxldmVsQ29kZQ0KICAgICAgfSkpDQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBHZXRTdG9wTGlzdChzdWJtaXRPYmopDQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgY29uc3Qgc3RvcE1hcCA9IHt9DQogICAgICAgICAgcmVzLkRhdGEuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgICAgc3RvcE1hcFtpdGVtLklkXSA9IGl0ZW0uSXNfU3RvcCAhPT0gbnVsbA0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy50YkRhdGEuZm9yRWFjaChyb3cgPT4gew0KICAgICAgICAgICAgaWYgKHN0b3BNYXBbcm93LklkXSkgew0KICAgICAgICAgICAgICB0aGlzLiRzZXQocm93LCAnc3RvcEZsYWcnLCBzdG9wTWFwW3Jvdy5JZF0pDQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZSkge30NCiAgICB9LA0KDQogICAgLy8g6I635Y+W6KGo5qC85pWw5o2uDQogICAgYXN5bmMgZmV0Y2hEYXRhKCkgew0KICAgICAgY29uc29sZS5sb2coJ+WIl+ihqOabtOaWsOaIkOWKnycpDQogICAgICAvLyDliIblvIDojrflj5bvvIzmj5Dpq5jmjqXlj6PpgJ/luqYNCiAgICAgIGF3YWl0IHRoaXMuZ2V0VGFibGVDb25maWcoJ3BsbV9jb21wb25lbnRfcGFnZV9saXN0JykNCiAgICAgIHRoaXMudGJMb2FkaW5nID0gdHJ1ZQ0KICAgICAgdGhpcy5nZXRDb21wb25lbnRJbXBvcnREZXRhaWxQYWdlTGlzdCgpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLnRiTG9hZGluZyA9IGZhbHNlDQogICAgICAgIHRoaXMucGdMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIGFzeW5jIGNoYW5nZVBhZ2UoKSB7DQogICAgICB0aGlzLnRiTG9hZGluZyA9IHRydWUNCiAgICAgIGlmICgNCiAgICAgICAgdHlwZW9mIHRoaXMucXVlcnlJbmZvLlBhZ2VTaXplICE9PSAnbnVtYmVyJyB8fA0KICAgICAgICB0aGlzLnF1ZXJ5SW5mby5QYWdlU2l6ZSA8IDENCiAgICAgICkgew0KICAgICAgICB0aGlzLnF1ZXJ5SW5mby5QYWdlU2l6ZSA9IDEwDQogICAgICB9DQogICAgICB0aGlzLmdldENvbXBvbmVudEltcG9ydERldGFpbFBhZ2VMaXN0KCkudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMudGJMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIHRiU2VsZWN0Q2hhbmdlKGFycmF5KSB7DQogICAgICBjb25zb2xlLmxvZygnYXJyYXknLCBhcnJheSkNCiAgICAgIHRoaXMuc2VsZWN0TGlzdCA9IGFycmF5LnJlY29yZHMNCiAgICAgIHRoaXMuU3RlZWxBbW91bnRUb3RhbCA9IDANCiAgICAgIHRoaXMuU2NoZWR1bGluZ051bVRvdGFsID0gMA0KICAgICAgdGhpcy5TdGVlbEFsbFdlaWdodFRvdGFsID0gMA0KICAgICAgdGhpcy5TY2hlZHVsaW5nQWxsV2VpZ2h0VG90YWwgPSAwDQogICAgICB0aGlzLkZpbmlzaENvdW50VG90YWwgPSAwDQogICAgICB0aGlzLkZpbmlzaFdlaWdodFRvdGFsID0gMA0KICAgICAgdGhpcy5Jc0NvbXBvbmVudFRvdGFsID0gMA0KICAgICAgdGhpcy5Ub3RhbEdyb3NzV2VpZ2h0ID0gMA0KICAgICAgdGhpcy5Jc0NvbXBvbmVudFRvdGFsU3RlZWxBbGxXZWlnaHQgPSAwDQogICAgICBsZXQgU3RlZWxBbGxXZWlnaHRUb3RhbFRlbXAgPSAwDQogICAgICBsZXQgU2NoZWR1bGluZ0FsbFdlaWdodFRvdGFsVGVtcCA9IDANCiAgICAgIGxldCBGaW5pc2hXZWlnaHRUb3RhbFRlbXAgPSAwDQogICAgICBsZXQgSXNDb21wb25lbnRUb3RhbFN0ZWVsQWxsV2VpZ2h0VGVtcCA9IDANCiAgICAgIGlmICh0aGlzLnNlbGVjdExpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICB0aGlzLnNlbGVjdExpc3QuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgIGNvbnN0IHNjaGVkdWxpbmdOdW0gPQ0KICAgICAgICAgICAgaXRlbS5TY2hlZHVsaW5nTnVtID09IG51bGwgPyAwIDogaXRlbS5TY2hlZHVsaW5nTnVtDQogICAgICAgICAgdGhpcy5TdGVlbEFtb3VudFRvdGFsICs9IGl0ZW0uU3RlZWxBbW91bnQNCiAgICAgICAgICB0aGlzLlNjaGVkdWxpbmdOdW1Ub3RhbCArPSBpdGVtLlNjaGVkdWxpbmdOdW0NCiAgICAgICAgICB0aGlzLkZpbmlzaENvdW50VG90YWwgKz0gaXRlbS5GaW5pc2hfQ291bnQNCiAgICAgICAgICB0aGlzLlRvdGFsR3Jvc3NXZWlnaHQgKz0gaXRlbS5Ub3RhbEdyb3NzV2VpZ2h0IC8gMTAwMA0KICAgICAgICAgIFN0ZWVsQWxsV2VpZ2h0VG90YWxUZW1wICs9IGl0ZW0uU3RlZWxBbGxXZWlnaHQNCiAgICAgICAgICBTY2hlZHVsaW5nQWxsV2VpZ2h0VG90YWxUZW1wICs9IGl0ZW0uU3RlZWxXZWlnaHQgKiBzY2hlZHVsaW5nTnVtDQogICAgICAgICAgRmluaXNoV2VpZ2h0VG90YWxUZW1wICs9IGl0ZW0uRmluaXNoX1dlaWdodA0KICAgICAgICAgIHRoaXMuSXNDb21wb25lbnRUb3RhbCArPQ0KICAgICAgICAgICAgaXRlbS5Jc19Db21wb25lbnQgPT09ICdGYWxzZScgPyBpdGVtLlN0ZWVsQW1vdW50IDogMA0KICAgICAgICAgIElzQ29tcG9uZW50VG90YWxTdGVlbEFsbFdlaWdodFRlbXAgKz0NCiAgICAgICAgICAgIGl0ZW0uSXNfQ29tcG9uZW50ID09PSAnRmFsc2UnID8gaXRlbS5TdGVlbEFsbFdlaWdodCA6IDANCiAgICAgICAgfSkNCiAgICAgICAgdGhpcy5TdGVlbEFsbFdlaWdodFRvdGFsID0NCiAgICAgICAgICBNYXRoLnJvdW5kKChTdGVlbEFsbFdlaWdodFRvdGFsVGVtcCAvIHRoaXMuUHJvcG9ydGlvbikgKiAxMDAwKSAvIDEwMDANCiAgICAgICAgdGhpcy5TY2hlZHVsaW5nQWxsV2VpZ2h0VG90YWwgPQ0KICAgICAgICAgIE1hdGgucm91bmQoKFNjaGVkdWxpbmdBbGxXZWlnaHRUb3RhbFRlbXAgLyB0aGlzLlByb3BvcnRpb24pICogMTAwMCkgLw0KICAgICAgICAgIDEwMDANCiAgICAgICAgdGhpcy5GaW5pc2hXZWlnaHRUb3RhbCA9DQogICAgICAgICAgTWF0aC5yb3VuZCgoRmluaXNoV2VpZ2h0VG90YWxUZW1wIC8gdGhpcy5Qcm9wb3J0aW9uKSAqIDEwMDApIC8gMTAwMA0KICAgICAgICB0aGlzLklzQ29tcG9uZW50VG90YWxTdGVlbEFsbFdlaWdodCA9DQogICAgICAgICAgTWF0aC5yb3VuZCgNCiAgICAgICAgICAgIChJc0NvbXBvbmVudFRvdGFsU3RlZWxBbGxXZWlnaHRUZW1wIC8gdGhpcy5Qcm9wb3J0aW9uKSAqIDEwMDANCiAgICAgICAgICApIC8gMTAwMA0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5nZXRDb21wb25lbnRTdW1tYXJ5SW5mbygpDQogICAgICB9DQogICAgfSwNCg0KICAgIGdldFRiRGF0YShkYXRhKSB7DQogICAgICBjb25zdCB7IENvdW50SW5mbyB9ID0gZGF0YQ0KICAgICAgLy8gdGhpcy50aXBMYWJlbCA9IGDntK/orqHkuIrkvKDmnoTku7Yke1llYXJTdGVlbH3ku7bvvIzmgLvph40ke1llYXJBbGxXZWlnaHR9dOOAgmANCiAgICAgIHRoaXMudGlwTGFiZWwgPSBDb3VudEluZm8NCiAgICB9LA0KDQogICAgYXN5bmMgZ2V0VHlwZUxpc3QoKSB7DQogICAgICBjb25zdCByZXMgPSBhd2FpdCBHZXRGYWN0b3J5UHJvZmVzc2lvbmFsQnlDb2RlKHsNCiAgICAgICAgZmFjdG9yeUlkOiBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnQ3VyUmVmZXJlbmNlSWQnKQ0KICAgICAgfSkNCiAgICAgIGNvbnN0IGRhdGEgPSByZXMuRGF0YQ0KICAgICAgaWYgKHJlcy5Jc1N1Y2NlZWQpIHsNCiAgICAgICAgdGhpcy5Qcm9wb3J0aW9uID0gZGF0YVswXS5Qcm9wb3J0aW9uDQogICAgICAgIHRoaXMuVW5pdCA9IGRhdGFbMF0uVW5pdA0KICAgICAgICB0aGlzLnR5cGVPcHRpb24gPSBPYmplY3QuZnJlZXplKGRhdGEpDQogICAgICAgIGlmICh0aGlzLnR5cGVPcHRpb24ubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zLlR5cGVJZCA9IHRoaXMudHlwZU9wdGlvblswXT8uSWQNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLmdldENvbXBUeXBlVHJlZSh0aGlzLnR5cGVPcHRpb25bMF0uQ29kZSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgZ2V0Q29tcFR5cGVUcmVlKENvZGUpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIEdldENvbXBUeXBlVHJlZSh7DQogICAgICAgIHByb2Zlc3Npb25hbDogQ29kZQ0KICAgICAgfSkNCiAgICAgICAgLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgICB0aGlzLnRyZWVQYXJhbXNTdGVlbCA9IHJlcy5EYXRhDQogICAgICAgICAgICB0aGlzLk9iamVjdFR5cGVMaXN0LmRhdGEgPSByZXMuRGF0YQ0KICAgICAgICAgICAgdGhpcy4kbmV4dFRpY2soKF8pID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kcmVmcy50cmVlU2VsZWN0T2JqZWN0VHlwZS50cmVlRGF0YVVwZGF0ZUZ1bihyZXMuRGF0YSkNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHRoaXMudHJlZURhdGEgPSBbXQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgLmZpbmFsbHkoKF8pID0+IHsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB9KQ0KICAgIH0sDQoNCiAgICAvLyDliKDpmaTmn6Xor6Lnu5PmnpwNCiAgICBoYW5kbGVTZWFyY2hEZWxldGUoKSB7DQogICAgICBpZiAodGhpcy5jdXN0b21QYXJhbXMuUHJvamVjdF9JZCA9PT0gJycpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnLA0KICAgICAgICAgIG1lc3NhZ2U6ICfor7fpgInmi6npobnnm64nDQogICAgICAgIH0pDQogICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgfQ0KICAgICAgdGhpcy4kY29uZmlybSgn5q2k5pON5L2c5bCG5Yig6Zmk5pCc57Si55qE5pWw5o2uLCDmmK/lkKbnu6fnu60/JywgJ+aPkOekuicsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgRGVsZXRlQWxsQ29tcG9uZW50V2l0aFF1ZXJ5KHsNCiAgICAgICAgICAgIC4uLnRoaXMuY3VzdG9tUGFyYW1zDQogICAgICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgICB0aGlzLmZldGNoRGF0YSgpDQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTmiJDlip8nLA0KICAgICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICB0eXBlOiAnaW5mbycsDQogICAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raI5Yig6ZmkJw0KICAgICAgICAgIH0pDQogICAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOWIoOmZpOmAieS4rQ0KICAgIGhhbmRsZURlbGV0ZSgpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+atpOaTjeS9nOWwhuWIoOmZpOmAieaLqeaVsOaNriwg5piv5ZCm57un57utPycsICfmj5DnpLonLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMudGJMb2FkaW5nID0gdHJ1ZQ0KICAgICAgICAgIERlbGV0ZUNvbXBvbmVudHMoew0KICAgICAgICAgICAgaWRzOiB0aGlzLnNlbGVjdExpc3QubWFwKCh2KSA9PiB2LklkKS50b1N0cmluZygpDQogICAgICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgICB0aGlzLmZldGNoRGF0YSgpDQogICAgICAgICAgICAgIHRoaXMuZmV0Y2hUcmVlRGF0YSgpDQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTmiJDlip8nLA0KICAgICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgbWVzc2FnZTogcmVzLk1lc3NhZ2UsDQogICAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgICAgICAuZmluYWxseSgoKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMudGJMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICAgIH0pDQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICB0eXBlOiAnaW5mbycsDQogICAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raI5Yig6ZmkJw0KICAgICAgICAgIH0pDQogICAgICAgIH0pDQogICAgfSwNCg0KICAgIGhhbmRsZUVkaXQocm93KSB7DQogICAgICB0aGlzLndpZHRoID0gJzQ1JScNCiAgICAgIHRoaXMuZ2VuZXJhdGVDb21wb25lbnQoJ+e8lui+keaehOS7ticsICdFZGl0JykNCiAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgIHJvdy5pc1JlYWRPbmx5ID0gZmFsc2UNCiAgICAgICAgdGhpcy4kcmVmc1snY29udGVudCddLmluaXQocm93KQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgaGFuZGxlQmF0Y2hFZGl0KCkgew0KICAgICAgY29uc3QgU2NoZWR1bEFyciA9IHRoaXMuc2VsZWN0TGlzdC5maWx0ZXIoKGl0ZW0pID0+IHsNCiAgICAgICAgcmV0dXJuIGl0ZW0uU2NoZWR1bGluZ051bSAhPSBudWxsICYmIGl0ZW0uU2NoZWR1bGluZ051bSA+IDANCiAgICAgIH0pDQogICAgICBpZiAoU2NoZWR1bEFyci5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIHR5cGU6ICdlcnJvcicsDQogICAgICAgICAgbWVzc2FnZTogJ+mAieS4reihjOWMheWQq+W3suaOkuS6p+eahOaehOS7tiznvJbovpHkv6Hmga/pnIDopoHov5vooYzlj5jmm7Tmk43kvZwnDQogICAgICAgIH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLndpZHRoID0gJzQwJScNCiAgICAgICAgdGhpcy5nZW5lcmF0ZUNvbXBvbmVudCgn5om56YeP57yW6L6RJywgJ0JhdGNoRWRpdCcpDQogICAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgICAgdGhpcy4kcmVmc1snY29udGVudCddLmluaXQodGhpcy5zZWxlY3RMaXN0LCB0aGlzLmNvbHVtbnNPcHRpb24pDQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCg0KICAgIGhhbmRsZVZpZXcocm93KSB7DQogICAgICB0aGlzLndpZHRoID0gJzQ1JScNCiAgICAgIHRoaXMuZ2VuZXJhdGVDb21wb25lbnQoJ+afpeeci+aehOS7ticsICdFZGl0JykNCiAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgIHJvdy5pc1JlYWRPbmx5ID0gdHJ1ZQ0KICAgICAgICB0aGlzLiRyZWZzWydjb250ZW50J10uaW5pdChyb3cpDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvLyDmn6XnnIvmnoTku7bnmoTpm7bku7YNCiAgICBoYW5kbGVWaWV3UGFydChyb3cpIHsNCiAgICAgIHRoaXMud2lkdGggPSAnNjAlJw0KICAgICAgdGhpcy5nZW5lcmF0ZUNvbXBvbmVudCgn6Zu26YOo5Lu25riF5Y2VJywgJ1BhcnRMaXN0JykNCiAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgIHRoaXMuJHJlZnNbJ2NvbnRlbnQnXS5pbml0KHJvdykNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOafpeeci+a3seWMlui1hOaWmSB0eXBlIDDmnoTku7YgIDHpm7bku7YNCiAgICBoYW5kbGVWaWV3U0gocm93LCB0eXBlKSB7DQogICAgICB0aGlzLndpZHRoID0gJzQwJScNCiAgICAgIHRoaXMuZ2VuZXJhdGVDb21wb25lbnQoJ+afpeeci+a3seWMlui1hOaWmScsICdTdGVlbE1lYW5zJykNCiAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgIHRoaXMuJHJlZnNbJ2NvbnRlbnQnXS5pbml0KHJvdywgdHlwZSkNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOWbnuiwg+afpeeci+mbtuS7tueahOa3seWMlui1hOaWmQ0KICAgIGhhbmRsZVN0ZWVsTWVhbnMocm93KSB7DQogICAgICB0aGlzLmhhbmRsZVZpZXdTSChyb3csIDEpDQogICAgfSwNCg0KICAgIC8vIOa3seWMluaooeWei+WUr+S4gOeggQ0KICAgIGhhbmRsZVZpZXdNb2RlbChyb3cpIHsNCiAgICAgIHRoaXMud2lkdGggPSAnNDAlJw0KICAgICAgdGhpcy5nZW5lcmF0ZUNvbXBvbmVudCgn5qih5Z6L5p6E5Lu25ZSv5LiA56CB5YiX6KGoJywgJ01vZGVsQ29tcG9uZW50Q29kZScpDQogICAgICB0aGlzLiRuZXh0VGljaygoXykgPT4gew0KICAgICAgICB0aGlzLiRyZWZzWydjb250ZW50J10uaW5pdChyb3cpDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvLyDmjpLkuqfmlbDph4/ngrnlh7vnmoTnlJ/kuqfor6bmg4UNCiAgICBoYW5kbGVWaWV3U2NoZWR1bGluZyhyb3cpIHsNCiAgICAgIHRoaXMud2lkdGggPSAnMzAlJw0KICAgICAgdGhpcy5nZW5lcmF0ZUNvbXBvbmVudCgn55Sf5Lqn6K+m5oOFJywgJ1Byb2R1Y3Rpb25EZXRhaWxzJykNCiAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgIHRoaXMuJHJlZnNbJ2NvbnRlbnQnXS5pbml0KHJvdykNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIGhhbmRsZUhpc3Rvcnkocm93KSB7DQogICAgICBjb25zb2xlLmxvZyh7IHJvdyB9KQ0KICAgICAgdGhpcy5nZW5lcmF0ZUNvbXBvbmVudCgn5p6E5Lu25Y+Y5pu05Y6G5Y+yJywgJ0NvbXBvbmVudHNIaXN0b3J5JykNCiAgICAgIHRoaXMuY3VzdG9tRGlhbG9nUGFyYW1zID0gew0KICAgICAgICBzdGVlbFVuaXF1ZTogcm93LlN0ZWVsVW5pcXVlDQogICAgICB9DQogICAgfSwNCg0KICAgIGxvY2F0aW9uRXhwb3J0KCkgew0KICAgICAgdGhpcy5oYW5kbGVTdGVlbEV4cG9ydCgzKQ0KICAgIH0sDQogICAgaGFuZGxlRXhwb3J0KHYpIHsNCiAgICAgIGlmICh2ID09PSAnY29tJykgew0KICAgICAgICB0aGlzLmhhbmRsZVN0ZWVsRXhwb3J0KDIpDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmhhbmRsZUV4cG9ydEFsbCgpDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVFeHBvcnRBbGwoKSB7DQogICAgICBFeHBvcnRUaHJlZUJvbSh7DQogICAgICAgIC8vIG1vZGVsOiB7DQogICAgICAgIC8vICAgUHJvamVjdF9JZDogdGhpcy5jdXN0b21QYXJhbXMuUHJvamVjdF9JZCwNCiAgICAgICAgLy8gICBBcmVhX0lkOiB0aGlzLmN1c3RvbVBhcmFtcy5BcmVhX0lkDQogICAgICAgIC8vIH0NCiAgICAgICAgLi4udGhpcy5xdWVyeUluZm8sDQogICAgICAgIC4uLnRoaXMuY3VzdG9tUGFyYW1zLA0KICAgICAgICBJZHM6IHRoaXMuc2VsZWN0TGlzdC5tYXAoKHYpID0+IHYuSWQpLnRvU3RyaW5nKCkNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgIHdpbmRvdy5vcGVuKGNvbWJpbmVVUkwodGhpcy4kYmFzZVVybCwgcmVzLkRhdGEpLCAnX2JsYW5rJykNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5NZXNzYWdlLA0KICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyDlr7zlh7rmnoTku7YgIHR5cGUgMOWvvOWHuuacque7keWumueahOaehOS7tiAgIDHlr7zlh7rlt7Lnu5HlrprnmoTmnoTku7YgICAgMuWvvOWHuuaehOS7tg0KICAgIGFzeW5jIGhhbmRsZVN0ZWVsRXhwb3J0KHR5cGUpIHsNCiAgICAgIGlmICgNCiAgICAgICAgdGhpcy5jdXN0b21QYXJhbXMuU3lzX1Byb2plY3RfSWQgPT09ICcnICYmDQogICAgICAgIHRoaXMuc2VsZWN0TGlzdC5sZW5ndGggPT09IDANCiAgICAgICkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycsDQogICAgICAgICAgbWVzc2FnZTogJ+ivt+mAieaLqemhueebricNCiAgICAgICAgfSkNCiAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICB9DQogICAgICBjb25zdCBvYmogPSB7DQogICAgICAgIEJvbV9MZXZlbDogdGhpcy5sZXZlbENvZGUsDQogICAgICAgIFR5cGU6IHR5cGUsDQogICAgICAgIEltcG9ydF9EZXRhaWxfSWRzOiB0aGlzLnNlbGVjdExpc3QubWFwKCh2KSA9PiB2LklkKSwNCiAgICAgICAgLi4udGhpcy5jdXN0b21QYXJhbXMsDQogICAgICAgIFN5c19Qcm9qZWN0X0lkOiB0aGlzLmN1c3RvbVBhcmFtcy5TeXNfUHJvamVjdF9JZA0KICAgICAgfQ0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgRXhwb3J0Q29tcG9uZW50SW5mbyhvYmopDQoNCiAgICAgIGlmICghcmVzLklzU3VjY2VlZCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICB0eXBlOiAnZXJyb3InDQogICAgICAgIH0pDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXVudXNlZC12YXJzDQogICAgICBsZXQgZmlsZU5hbWUgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnUHJvamVjdE5hbWUnKSArICdf5p6E5Lu25a+85Ye65piO57uGJw0KICAgICAgaWYgKHJlcy50eXBlID09PSAnYXBwbGljYXRpb24vb2N0ZXQtc3RyZWFtJykgew0KICAgICAgICBmaWxlTmFtZSArPSAnLnJhcicNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGZpbGVOYW1lICs9ICcueGxzJw0KICAgICAgfQ0KICAgICAgd2luZG93Lm9wZW4oY29tYmluZVVSTCh0aGlzLiRiYXNlVXJsLCByZXMuRGF0YSksICdfYmxhbmsnKQ0KICAgICAgLy8gZG93bmxvYWRCbG9iRmlsZShyZXMuRGF0YSwgZmlsZU5hbWUsICcgJykNCiAgICB9LA0KDQogICAgLy8g6I635Y+W5paH5Lu255qEYXJyYXlidWZmZXLmoLzlvI/lubbkvKDlhaXov5vooYzmiZPljIXlh4blpIcNCiAgICBnZXRGaWxlKHVybCkgew0KICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHsNCiAgICAgICAgYXhpb3Moew0KICAgICAgICAgIG1ldGhvZDogJ2dldCcsDQogICAgICAgICAgdXJsLA0KICAgICAgICAgIHJlc3BvbnNlVHlwZTogJ2FycmF5YnVmZmVyJw0KICAgICAgICB9KQ0KICAgICAgICAgIC50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgIHJlc29sdmUocmVzLmRhdGEpDQogICAgICAgICAgfSkNCiAgICAgICAgICAuY2F0Y2goKGVycm9yKSA9PiB7DQogICAgICAgICAgICByZWplY3QoZXJyb3IudG9TdHJpbmcoKSkNCiAgICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5qih5Z6L5riF5Y2V5a+85YWlDQogICAgbW9kZWxMaXN0SW1wb3J0KCkgew0KICAgICAgdGhpcy53aWR0aCA9ICczMCUnDQogICAgICB0aGlzLmdlbmVyYXRlQ29tcG9uZW50KCfmqKHlnovmuIXljZXlr7zlhaUnLCAnTW9kZWxMaXN0SW1wb3J0JykNCiAgICB9LA0KDQogICAgTG9jYXRpb25JbXBvcnQoKSB7DQogICAgICB0aGlzLndpZHRoID0gJzMwJScNCiAgICAgIHRoaXMuZ2VuZXJhdGVDb21wb25lbnQoJ+S9jee9ruS/oeaBr+WvvOWFpScsICdMb2NhdGlvbkltcG9ydCcpDQogICAgfSwNCg0KICAgIC8vIOaWsOWinuWvvOWFpSBvciDopobnm5blr7zlhaUNCiAgICBoYW5kbGVDb21tYW5kKGNvbW1hbmQsIHR5cGUpIHsNCiAgICAgIC8vIGNvbnNvbGUubG9nKGNvbW1hbmQsICdjb21tYW5kJykNCiAgICAgIC8vIGNvbnNvbGUubG9nKHR5cGUsICd0eXBlJykNCiAgICAgIHRoaXMuY29tbWFuZCA9IGNvbW1hbmQNCiAgICAgIGlmICh0eXBlID09PSAxKSB7DQogICAgICAgIHRoaXMuZGVlcExpc3RJbXBvcnQoMSkNCiAgICAgIH0gZWxzZSBpZiAodHlwZSA9PT0gMCkgew0KICAgICAgICB0aGlzLmRlZXBMaXN0SW1wb3J0KDApDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOaJk+W8gOWvvOWFpeW8ueahhiBpbXBvcnRUeXBlIDHpm7bmnoTku7YgMCDmnoTku7YNCiAgICBkZWVwTGlzdEltcG9ydChpbXBvcnRUeXBlKSB7DQogICAgICBjb25zb2xlLmxvZyhpbXBvcnRUeXBlLCAnaW1wb3J0VHlwZScpDQogICAgICBjb25zdCBmaWxlVHlwZSA9IHsNCiAgICAgICAgQ2F0YWxvZ19Db2RlOiAnUExNRGVlcGVuRmlsZXMnLA0KICAgICAgICBDb2RlOiB0aGlzLnR5cGVFbnRpdHkuQ29kZSwNCiAgICAgICAgbmFtZTogdGhpcy50eXBlRW50aXR5Lk5hbWUNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLnByb2R1Y3Rpb25Db25maXJtID09PSAndHJ1ZScgJiYgaW1wb3J0VHlwZSA9PT0gMCkgew0KICAgICAgICB0aGlzLndpZHRoID0gJzMwJScNCiAgICAgICAgdGhpcy5nZW5lcmF0ZUNvbXBvbmVudCgn5a+85YWl5p6E5Lu2JywgJ1Byb2R1Y3Rpb25Db25maXJtJykNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJHJlZnMuZGlhbG9nLmhhbmRsZU9wZW4oDQogICAgICAgICAgJ2FkZCcsDQogICAgICAgICAgZmlsZVR5cGUsDQogICAgICAgICAgbnVsbCwNCiAgICAgICAgICB0cnVlLA0KICAgICAgICAgICcnLA0KICAgICAgICAgIGltcG9ydFR5cGUsDQogICAgICAgICAgJycsDQogICAgICAgICAgdGhpcy5jb21tYW5kLA0KICAgICAgICAgIHRoaXMuY3VzdG9tUGFyYW1zDQogICAgICAgICkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5Zue6LCD5piv5ZCm5pyJ55Sf5Lqn6L+H56iLDQogICAgZGVlcExpc3RJbXBvcnRBZ2luKHByb2R1Y3Rpb25Db25maXJtRGF0YSkgew0KICAgICAgY29uc3QgZmlsZVR5cGUgPSB7DQogICAgICAgIENhdGFsb2dfQ29kZTogJ1BMTURlZXBlbkZpbGVzJywNCiAgICAgICAgQ29kZTogdGhpcy50eXBlRW50aXR5LkNvZGUsDQogICAgICAgIG5hbWU6IHRoaXMudHlwZUVudGl0eS5OYW1lDQogICAgICB9DQogICAgICB0aGlzLiRyZWZzLmRpYWxvZy5oYW5kbGVPcGVuKA0KICAgICAgICAnYWRkJywNCiAgICAgICAgZmlsZVR5cGUsDQogICAgICAgIG51bGwsDQogICAgICAgIHRydWUsDQogICAgICAgICcnLA0KICAgICAgICAwLA0KICAgICAgICBwcm9kdWN0aW9uQ29uZmlybURhdGEsDQogICAgICAgIHRoaXMuY29tbWFuZCwNCiAgICAgICAgdGhpcy5jdXN0b21QYXJhbXMNCiAgICAgICkNCiAgICB9LA0KDQogICAgLy8g5a+85Ye65o6S5Lqn5Y2VDQogICAgaGFuZGxlU2NoZWR1bGluZ0luZm9FeHBvcnQoKSB7DQogICAgICBFeHBvcnRDb21wb25lbnRTY2hlZHVsaW5nSW5mbyh7DQogICAgICAgIGlkczogdGhpcy5zZWxlY3RMaXN0Lm1hcCgodikgPT4gdi5JZCkudG9TdHJpbmcoKQ0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgd2luZG93Lm9wZW4oY29tYmluZVVSTCh0aGlzLiRiYXNlVXJsLCByZXMuRGF0YSksICdfYmxhbmsnKQ0KICAgICAgICAgIGlmIChyZXMuTWVzc2FnZSkgew0KICAgICAgICAgICAgdGhpcy4kYWxlcnQocmVzLk1lc3NhZ2UsICflr7zlh7rpgJrnn6UnLCB7DQogICAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn5oiR55+l6YGT5LqGJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQoNCiAgICBoYW5kbGVIaXN0b3J5RXhwb3J0KCkgew0KICAgICAgaWYgKHRoaXMuY3VzdG9tUGFyYW1zLlByb2plY3RfSWQgPT09ICcnKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywNCiAgICAgICAgICBtZXNzYWdlOiAn6K+36YCJ5oup6aG555uuJw0KICAgICAgICB9KQ0KICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgIH0NCiAgICAgIHRoaXMud2lkdGggPSAnNjAlJw0KICAgICAgdGhpcy5nZW5lcmF0ZUNvbXBvbmVudCgn5Y6G5Y+y5riF5Y2V5a+85Ye6JywgJ0hpc3RvcnlFeHBvcnQnKQ0KICAgIH0sDQoNCiAgICBoYW5kbGVTY2hlZHVsZUV4cG9ydCgpIHsNCiAgICAgIHRoaXMuc2NoZWR1bGVMb2FkaW5nID0gdHJ1ZQ0KICAgICAgY29uc3QgaWRzID0gdGhpcy5zZWxlY3RMaXN0Lm1hcCgodikgPT4gdi5JZCkudG9TdHJpbmcoKQ0KICAgICAgRXhwb3J0RGVlcGVuRnVsbFNjaGVkdWxpbmdJbmZvKHsNCiAgICAgICAgSWRzOiBpZHMNCiAgICAgIH0pDQogICAgICAgIC50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICBpZiAocmVzLklzU3VjY2VlZCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6ICflr7zlh7rmiJDlip8nLA0KICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICB3aW5kb3cub3Blbihjb21iaW5lVVJMKHRoaXMuJGJhc2VVcmwsIHJlcy5EYXRhKSwgJ19ibGFuaycpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICAgIC5maW5hbGx5KChfKSA9PiB7DQogICAgICAgICAgdGhpcy5zY2hlZHVsZUxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB9KQ0KICAgIH0sDQoNCiAgICBoYW5kbGVDb21wb25lbnRQYWNrKHsgZGF0YSwgdHlwZSA9IDIgfSkgew0KICAgICAgY29uc29sZS5sb2coJ2luZGV4JywgZGF0YSwgdHlwZSkNCiAgICAgIHRoaXMud2lkdGggPSAnODAlJw0KICAgICAgdGhpcy5nZW5lcmF0ZUNvbXBvbmVudCgn5p+l55yL5p6E5Lu25YyFJywgJ0NvbXBvbmVudFBhY2snKQ0KICAgICAgdGhpcy4kbmV4dFRpY2soKF8pID0+IHsNCiAgICAgICAgaWYgKGRhdGEpIHsNCiAgICAgICAgICB0aGlzLiRyZWZzWydjb250ZW50J10uZ2V0U3VibWl0T2JqKGRhdGEpDQogICAgICAgIH0NCiAgICAgICAgdGhpcy4kcmVmc1snY29udGVudCddLmhhbmRsZVBhY2thZ2UodHlwZSkNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIGhhbmRsZUFsbFBhY2soKSB7DQogICAgICB0aGlzLndpZHRoID0gJzMwJScNCiAgICAgIHRoaXMuZ2VuZXJhdGVDb21wb25lbnQoJ+afpeivoue7k+aenOS4gOmUruaJk+WMhScsICdPbmVDbGlja0dlbmVyYXRlUGFjaycpDQogICAgICB0aGlzLmN1c3RvbURpYWxvZ1BhcmFtcyA9IHRoaXMuY3VzdG9tUGFyYW1zDQogICAgfSwNCg0KICAgIGhhbmRsZUdlbmVyYXRlKCkgew0KICAgICAgdGhpcy53aWR0aCA9ICczMCUnDQogICAgICB0aGlzLmdlbmVyYXRlQ29tcG9uZW50KCfnlJ/miJDmnoTku7bljIUnLCAnR2VuZXJhdGVQYWNrJykNCiAgICAgIHRoaXMuJG5leHRUaWNrKChfKSA9PiB7DQogICAgICAgIHRoaXMuJHJlZnNbJ2NvbnRlbnQnXS5pbml0KHRoaXMuc2VsZWN0TGlzdCkNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIGhhbmRsZUNsb3NlKGRhdGEpIHsNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlDQoNCiAgICAgIC8vIOmAieaLqeaYr+WQpumcgOimgeeUn+S6p+euoeeQhui/h+eoi+WQjuWbnuiwg+WGjeasoeW8ueahhiBpbXBvcnRUeXBl6IKv5a6a5pivMA0KICAgICAgaWYgKGRhdGEgPT09IHRydWUgfHwgZGF0YSA9PT0gZmFsc2UpIHsNCiAgICAgICAgdGhpcy5kZWVwTGlzdEltcG9ydEFnaW4oZGF0YSkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgZ2VuZXJhdGVDb21wb25lbnQodGl0bGUsIGNvbXBvbmVudCkgew0KICAgICAgdGhpcy50aXRsZSA9IHRpdGxlDQogICAgICB0aGlzLmN1cnJlbnRDb21wb25lbnQgPSBjb21wb25lbnQNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIGZldGNoVHJlZURhdGFMb2NhbCgpIHsNCiAgICAgIC8vIHRoaXMuZmlsdGVyVGV4dCA9IHRoaXMucHJvamVjdE5hbWUNCiAgICB9LA0KICAgIGN1c3RvbUZpbHRlckZ1bih2YWx1ZSwgZGF0YSwgbm9kZSkgew0KICAgICAgY29uc3QgYXJyID0gdmFsdWUuc3BsaXQoU1BMSVRfU1lNQk9MKQ0KICAgICAgY29uc3QgbGFiZWxWYWwgPSBhcnJbMF0NCiAgICAgIGNvbnN0IHN0YXR1c1ZhbCA9IGFyclsxXQ0KICAgICAgaWYgKCF2YWx1ZSkgcmV0dXJuIHRydWUNCiAgICAgIGxldCBwYXJlbnROb2RlID0gbm9kZS5wYXJlbnQNCiAgICAgIGxldCBsYWJlbHMgPSBbbm9kZS5sYWJlbF0NCiAgICAgIGxldCBzdGF0dXMgPSBbDQogICAgICAgIGRhdGEuRGF0YS5Jc19EZWVwZW5fQ2hhbmdlDQogICAgICAgICAgPyAn5bey5Y+Y5pu0Jw0KICAgICAgICAgIDogZGF0YS5EYXRhLklzX0ltcG9ydGVkDQogICAgICAgICAgICA/ICflt7Llr7zlhaUnDQogICAgICAgICAgICA6ICfmnKrlr7zlhaUnDQogICAgICBdDQogICAgICBsZXQgbGV2ZWwgPSAxDQogICAgICB3aGlsZSAobGV2ZWwgPCBub2RlLmxldmVsKSB7DQogICAgICAgIGxhYmVscyA9IFsuLi5sYWJlbHMsIHBhcmVudE5vZGUubGFiZWxdDQogICAgICAgIHN0YXR1cyA9IFsNCiAgICAgICAgICAuLi5zdGF0dXMsDQogICAgICAgICAgZGF0YS5EYXRhLklzX0RlZXBlbl9DaGFuZ2UNCiAgICAgICAgICAgID8gJ+W3suWPmOabtCcNCiAgICAgICAgICAgIDogZGF0YS5EYXRhLklzX0ltcG9ydGVkDQogICAgICAgICAgICAgID8gJ+W3suWvvOWFpScNCiAgICAgICAgICAgICAgOiAn5pyq5a+85YWlJw0KICAgICAgICBdDQogICAgICAgIHBhcmVudE5vZGUgPSBwYXJlbnROb2RlLnBhcmVudA0KICAgICAgICBsZXZlbCsrDQogICAgICB9DQogICAgICBsYWJlbHMgPSBsYWJlbHMuZmlsdGVyKCh2KSA9PiAhIXYpDQogICAgICBzdGF0dXMgPSBzdGF0dXMuZmlsdGVyKCh2KSA9PiAhIXYpDQogICAgICBsZXQgcmVzdWx0TGFiZWwgPSB0cnVlDQogICAgICBsZXQgcmVzdWx0U3RhdHVzID0gdHJ1ZQ0KICAgICAgaWYgKHRoaXMuc3RhdHVzVHlwZSkgew0KICAgICAgICByZXN1bHRTdGF0dXMgPSBzdGF0dXMuc29tZSgocykgPT4gcy5pbmRleE9mKHN0YXR1c1ZhbCkgIT09IC0xKQ0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMucHJvamVjdE5hbWUpIHsNCiAgICAgICAgcmVzdWx0TGFiZWwgPSBsYWJlbHMuc29tZSgocykgPT4gcy5pbmRleE9mKGxhYmVsVmFsKSAhPT0gLTEpDQogICAgICB9DQogICAgICByZXR1cm4gcmVzdWx0TGFiZWwgJiYgcmVzdWx0U3RhdHVzDQogICAgfSwNCiAgICAvLw0KICAgIGFzeW5jIGdldEZpbGVUeXBlKCkgew0KICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICBjYXRhbG9nQ29kZTogJ1BMTURlZXBlbkZpbGVzJw0KICAgICAgfQ0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgR2V0RmlsZVR5cGUocGFyYW1zKQ0KICAgICAgLy8g6I635Y+W5p6E5Lu26K+m5Zu+DQogICAgICBjb25zdCBkYXRhID0gcmVzLkRhdGEuZmluZCgodikgPT4gdi5MYWJlbCA9PT0gJ+aehOS7tuivpuWbvicpDQoNCiAgICAgIHRoaXMuY29tRHJhd0RhdGEgPSB7DQogICAgICAgIGlzU0hRRDogZmFsc2UsDQogICAgICAgIElkOiBkYXRhLklkLA0KICAgICAgICBuYW1lOiBkYXRhLkxhYmVsLA0KICAgICAgICBDYXRhbG9nX0NvZGU6IGRhdGEuQ29kZSwNCiAgICAgICAgQ29kZTogZGF0YS5EYXRhPy5FbmdsaXNoX05hbWUNCiAgICAgIH0NCg0KICAgICAgY29uc29sZS5sb2codGhpcy5jb21EcmF3RGF0YSwgJ2NvbURyYXdEYXRhJykNCiAgICB9LA0KICAgIC8vIOWbvue6uOWvvOWFpQ0KICAgIGhhbmRlbEltcG9ydCgpIHsNCiAgICAgIHRoaXMuJHJlZnMuY29tRHJhd2RpYWxvZ1JlZi5oYW5kbGVPcGVuKA0KICAgICAgICAnYWRkJywNCiAgICAgICAgdGhpcy5jb21EcmF3RGF0YSwNCiAgICAgICAgJycsDQogICAgICAgIGZhbHNlLA0KICAgICAgICB0aGlzLmN1c3RvbVBhcmFtcy5TeXNfUHJvamVjdF9JZCwNCiAgICAgICAgZmFsc2UNCiAgICAgICkNCiAgICB9LA0KDQogICAgLy8g6L2o6L+55Zu+DQogICAgaGFuZGxlVHJhY2socm93KSB7DQogICAgICBjb25zb2xlLmxvZyhyb3csICdyb3cnKQ0KICAgICAgdGhpcy50cmFja0RyYXdlciA9IHRydWUNCiAgICAgIHRoaXMudHJhY2tEcmF3ZXJUaXRsZSA9IHJvdy5TdGVlbE5hbWUNCiAgICAgIHRoaXMudHJhY2tEcmF3ZXJEYXRhID0gcm93DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsmBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/PRO/component-list/v4", "sourcesContent": ["<template>\r\n  <div class=\"app-container abs100\">\r\n    <div\r\n      v-loading=\"pgLoading\"\r\n      class=\"h100 app-wrapper\"\r\n      element-loading-text=\"加载中\"\r\n    >\r\n      <ExpandableSection v-model=\"showExpand\" :width=\"300\" class=\"cs-left fff\">\r\n        <div class=\"inner-wrapper\">\r\n          <div class=\"tree-search\">\r\n            <el-select\r\n              v-model=\"statusType\"\r\n              clearable\r\n              class=\"search-select\"\r\n              placeholder=\"导入状态选择\"\r\n            >\r\n              <el-option label=\"已导入\" value=\"已导入\" />\r\n              <el-option label=\"未导入\" value=\"未导入\" />\r\n              <el-option label=\"已变更\" value=\"已变更\" />\r\n            </el-select>\r\n            <el-input\r\n              v-model.trim=\"projectName\"\r\n              placeholder=\"关键词搜索\"\r\n              size=\"small\"\r\n              clearable\r\n              suffix-icon=\"el-icon-search\"\r\n              @blur=\"fetchTreeDataLocal\"\r\n              @clear=\"fetchTreeDataLocal\"\r\n              @keydown.enter.native=\"fetchTreeDataLocal\"\r\n            />\r\n          </div>\r\n          <el-divider class=\"cs-divider\" />\r\n          <div class=\"tree-x cs-scroll\">\r\n            <tree-detail\r\n              ref=\"tree\"\r\n              icon=\"icon-folder\"\r\n              is-custom-filter\r\n              :custom-filter-fun=\"customFilterFun\"\r\n              :loading=\"treeLoading\"\r\n              :tree-data=\"treeData\"\r\n              show-status\r\n              show-detail\r\n              :filter-text=\"filterText\"\r\n              :expanded-key=\"expandedKey\"\r\n              @handleNodeClick=\"handleNodeClick\"\r\n            >\r\n              <template #csLabel=\"{ showStatus, data }\">\r\n                <span\r\n                  v-if=\"!data.ParentNodes\"\r\n                  class=\"cs-blue\"\r\n                >({{ data.Code }})</span>{{ data.Label }}\r\n                <template v-if=\"showStatus && data.Label != '全部'\">\r\n                  <span v-if=\"data.Data.Is_Deepen_Change\" class=\"cs-tag redBg\">\r\n                    <i class=\"fourRed\">已变更</i></span>\r\n                  <span\r\n                    v-else\r\n                    :class=\"[\r\n                      'cs-tag',\r\n                      data.Data.Is_Imported == true ? 'greenBg' : 'orangeBg',\r\n                    ]\"\r\n                  >\r\n                    <i\r\n                      :class=\"[\r\n                        data.Data.Is_Imported == true\r\n                          ? 'fourGreen'\r\n                          : 'fourOrange',\r\n                      ]\"\r\n                    >{{\r\n                      data.Data.Is_Imported == true ? \"已导入\" : \"未导入\"\r\n                    }}</i>\r\n                  </span>\r\n                </template>\r\n              </template>\r\n            </tree-detail>\r\n          </div>\r\n        </div>\r\n      </ExpandableSection>\r\n      <div class=\"cs-right\">\r\n        <div ref=\"searchDom\" class=\"cs-from\">\r\n          <div class=\"cs-search\">\r\n            <el-form\r\n              ref=\"customParams\"\r\n              :model=\"customParams\"\r\n              label-width=\"80px\"\r\n              class=\"demo-form-inline\"\r\n            >\r\n              <el-row>\r\n                <el-col :span=\"6\" :lg=\"6\" :xl=\"6\">\r\n                  <el-form-item :label=\"levelName + '名称'\" prop=\"Names\">\r\n                    <el-input\r\n                      v-model=\"names\"\r\n                      clearable\r\n                      style=\"width: 100%\"\r\n                      class=\"input-with-select\"\r\n                      placeholder=\"请输入内容\"\r\n                      size=\"small\"\r\n                    >\r\n                      <el-select\r\n                        slot=\"prepend\"\r\n                        v-model=\"nameMode\"\r\n                        placeholder=\"请选择\"\r\n                        style=\"width: 100px\"\r\n                      >\r\n                        <el-option label=\"模糊搜索\" :value=\"1\" />\r\n                        <el-option label=\"精确搜索\" :value=\"2\" />\r\n                      </el-select>\r\n                    </el-input>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\" :lg=\"5\" :xl=\"6\">\r\n                  <el-form-item\r\n                    label-width=\"60px\"\r\n                    class=\"mb0\"\r\n                    label=\"批次\"\r\n                    prop=\"InstallUnit_Ids\"\r\n                  >\r\n                    <el-select\r\n                      v-model=\"customParams.InstallUnit_Ids\"\r\n                      filterable\r\n                      clearable\r\n                      multiple\r\n                      placeholder=\"请选择\"\r\n                      style=\"width: 100%\"\r\n                      :disabled=\"!Boolean(customParams.Area_Id)\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"item in installUnitIdNameList\"\r\n                        :key=\"item.Id\"\r\n                        :label=\"item.Name\"\r\n                        :value=\"item.Id\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\" :lg=\"5\" :xl=\"4\">\r\n                  <el-form-item label-width=\"92px\" prop=\"SteelType\">\r\n                    <template #label><span>{{ levelName + '类型' }}</span></template>\r\n                    <el-tree-select\r\n                      ref=\"treeSelectObjectType\"\r\n                      v-model=\"customParams.SteelType\"\r\n                      class=\"cs-tree-x\"\r\n                      :select-params=\"treeSelectParams\"\r\n                      :tree-params=\"ObjectTypeList\"\r\n                      value-key=\"Id\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\" :lg=\"4\" :xl=\"4\">\r\n                  <el-form-item :label=\"levelName + '号'\" prop=\"SteelNumber\">\r\n                    <el-input\r\n                      v-model=\"customParams.SteelNumber\"\r\n                      placeholder=\"请输入\"\r\n                      clearable\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\" :lg=\"4\" :xl=\"4\">\r\n                  <el-form-item :label=\"levelName + '序号'\" prop=\"SteelCode\">\r\n                    <el-input\r\n                      v-model=\"customParams.SteelCode\"\r\n                      placeholder=\"请输入\"\r\n                      clearable\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"6\" :lg=\"6\" :xl=\"6\">\r\n                  <el-form-item label=\"规格\" prop=\"Spec\">\r\n                    <el-input\r\n                      v-model=\"customParams.Spec\"\r\n                      placeholder=\"请输入\"\r\n                      clearable\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\" :lg=\"5\" :xl=\"6\">\r\n                  <el-form-item label-width=\"60px\" label=\"材质\" prop=\"Texture\">\r\n                    <el-input\r\n                      v-model=\"customParams.Texture\"\r\n                      placeholder=\"请输入\"\r\n                      clearable\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\" :lg=\"5\" :xl=\"4\">\r\n                  <el-form-item\r\n                    label-width=\"92px\"\r\n                    class=\"mb0\"\r\n                    label=\"是否直发件\"\r\n                    prop=\"Is_Direct\"\r\n                  >\r\n                    <el-select\r\n                      v-model=\"customParams.Is_Direct\"\r\n                      style=\"width: 100%\"\r\n                      placeholder=\"请选择\"\r\n                      clearable\r\n                    >\r\n                      <el-option label=\"全部\" value=\"\" />\r\n                      <el-option label=\"是\" :value=\"true\" />\r\n                      <el-option label=\"否\" :value=\"false\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\" :lg=\"4\" :xl=\"4\">\r\n                  <el-form-item label=\"操作人\" prop=\"Create_UserName\">\r\n                    <el-input\r\n                      v-model=\"customParams.Create_UserName\"\r\n                      style=\"width: 100%\"\r\n                      placeholder=\"请输入\"\r\n                      clearable\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\" :lg=\"4\" :xl=\"4\">\r\n                  <el-form-item class=\"mb0\" label-width=\"16px\">\r\n                    <el-button\r\n                      type=\"primary\"\r\n                      @click=\"handleSearch()\"\r\n                    >搜索\r\n                    </el-button>\r\n                    <el-button @click=\"handleSearch('reset')\">重置</el-button>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"fff cs-z-tb-wrapper\">\r\n          <div class=\"cs-button-box\">\r\n            <div>\r\n              <el-dropdown\r\n                trigger=\"click\"\r\n                placement=\"bottom-start\"\r\n                @command=\"handleCommand($event, 1)\"\r\n              >\r\n                <el-button\r\n                  type=\"primary\"\r\n                  :disabled=\"!currentLastLevel\"\r\n                >多级清单导入\r\n                  <i class=\"el-icon-arrow-down el-icon--right\" />\r\n                </el-button>\r\n                <el-dropdown-menu slot=\"dropdown\">\r\n                  <el-dropdown-item\r\n                    :disabled=\"allStopFlag\"\r\n                    command=\"add\"\r\n                  >新增导入</el-dropdown-item>\r\n                  <el-dropdown-item\r\n                    :disabled=\"allStopFlag\"\r\n                    command=\"cover\"\r\n                  >覆盖导入</el-dropdown-item>\r\n                  <el-dropdown-item\r\n                    :disabled=\"allStopFlag\"\r\n                    command=\"halfcover\"\r\n                  >部分覆盖导入</el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </el-dropdown>\r\n              <!-- <el-button\r\n                  type=\"primary\"\r\n                  @click=\"deepListImport(1)\"\r\n                >构件/零件导入</el-button>\r\n                <el-button\r\n                  type=\"primary\"\r\n                  @click=\"deepListImport(0)\"\r\n                >构件导入</el-button> -->\r\n              <el-button\r\n                type=\"primary\"\r\n                @click=\"modelListImport\"\r\n              >导入模型清单\r\n              </el-button>\r\n              <el-button\r\n                type=\"primary\"\r\n                @click=\"LocationImport\"\r\n              >位置信息导入\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"!isVersionFour\"\r\n                :disabled=\"!selectList.length\"\r\n                @click=\"handleSchedulingInfoExport\"\r\n              >导出排产单模板\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"!isVersionFour\"\r\n                @click=\"handleSteelExport(2)\"\r\n              >导出构件</el-button>\r\n              <el-dropdown\r\n                v-else\r\n                trigger=\"click\"\r\n                placement=\"bottom-start\"\r\n                @command=\"handleExport\"\r\n              >\r\n                <el-button\r\n                  type=\"primary\"\r\n                  :disabled=\"!currentLastLevel\"\r\n                >导出\r\n                  <i class=\"el-icon-arrow-down el-icon--right\" />\r\n                </el-button>\r\n                <el-dropdown-menu slot=\"dropdown\">\r\n                  <el-dropdown-item command=\"com\">纯构件</el-dropdown-item>\r\n                  <el-dropdown-item command=\"all\">完整清单</el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </el-dropdown>\r\n              <el-button @click=\"handleHistoryExport\">历史清单导出</el-button>\r\n              <el-button\r\n                :loading=\"scheduleLoading\"\r\n                :disabled=\"!selectList.length\"\r\n                @click=\"handleScheduleExport\"\r\n              >排产单导出</el-button>\r\n              <el-button\r\n                :disabled=\"\r\n                  !selectList.length || selectList.some((item) => item.stopFlag)\r\n                \"\r\n                type=\"primary\"\r\n                plain\r\n                @click=\"handleBatchEdit\"\r\n              >批量编辑\r\n              </el-button>\r\n              <el-button\r\n                type=\"danger\"\r\n                plain\r\n                :disabled=\"\r\n                  !selectList.length || selectList.some((item) => item.stopFlag)\r\n                \"\r\n                @click=\"handleDelete\"\r\n              >删除选中\r\n              </el-button>\r\n              <el-button\r\n                type=\"success\"\r\n                plain\r\n                :disabled=\"!Boolean(customParams.Sys_Project_Id)\"\r\n                @click=\"handelImport\"\r\n              >图纸导入\r\n              </el-button>\r\n            </div>\r\n            <div v-if=\"showTotalLength\" class=\"cs-length\">\r\n              <span class=\"txt-green\">累计长度：{{ deepenTotalLength }}</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"info-box\">\r\n            <div class=\"cs-col\">\r\n              <span>\r\n                <span class=\"info-label\">深化总数</span>\r\n                <i>{{ SteelAmountTotal }} 件</i>\r\n              </span>\r\n              <span><span class=\"info-label\">深化总量</span>\r\n                <i>{{ SteelAllWeightTotal }} t</i></span>\r\n            </div>\r\n            <div class=\"cs-col\">\r\n              <span><span class=\"info-label\">排产总数</span>\r\n                <i>{{ SchedulingNumTotal }} 件</i></span>\r\n              <span><span class=\"info-label\">排产总量</span>\r\n                <i>{{ SchedulingAllWeightTotal }} t</i></span>\r\n            </div>\r\n            <div class=\"cs-col\" style=\"cursor: pointer;\" @click=\"getProcessData()\">\r\n              <span><span class=\"info-label\">完成总数</span>\r\n                <i>{{ FinishCountTotal }} 件</i></span>\r\n              <span><span class=\"info-label\">完成总量</span>\r\n                <i>{{ FinishWeightTotal }} t</i></span>\r\n            </div>\r\n            <div class=\"cs-col\">\r\n              <span><span class=\"info-label\">直发件总数</span>\r\n                <i>{{ IsComponentTotal }} 件</i></span>\r\n              <span><span class=\"info-label\">直发件总量</span>\r\n                <i>{{ IsComponentTotalSteelAllWeight }} t</i></span>\r\n            </div>\r\n            <div class=\"cs-col\">\r\n              <span><span class=\"info-label\">毛重合计</span>\r\n                <i>{{ TotalGrossWeightT }} t</i></span>\r\n            </div>\r\n          </div>\r\n          <div class=\"tb-container\">\r\n            <vxe-table\r\n              v-loading=\"tbLoading\"\r\n              :empty-render=\"{ name: 'NotData' }\"\r\n              show-header-overflow\r\n              element-loading-spinner=\"el-icon-loading\"\r\n              element-loading-text=\"拼命加载中\"\r\n              empty-text=\"暂无数据\"\r\n              class=\"cs-vxe-table\"\r\n              height=\"auto\"\r\n              auto-resize\r\n              align=\"left\"\r\n              stripe\r\n              :data=\"tbData\"\r\n              resizable\r\n              :tooltip-config=\"{ enterable: true }\"\r\n              :row-config=\"{ isHover: true }\"\r\n              @checkbox-all=\"tbSelectChange\"\r\n              @checkbox-change=\"tbSelectChange\"\r\n            >\r\n              <vxe-column fixed=\"left\" type=\"checkbox\" width=\"44\" />\r\n              <vxe-column\r\n                v-for=\"(item, index) in columns\"\r\n                :key=\"index\"\r\n                :fixed=\"item.Is_Frozen ? item.Frozen_Dirction : ''\"\r\n                show-overflow=\"tooltip\"\r\n                sortable\r\n                :align=\"item.Align\"\r\n                :field=\"item.Code\"\r\n                :title=\"item.Display_Name\"\r\n                :min-width=\"item.Width ? item.Width : 120\"\r\n              >\r\n                <!-- <template #default=\"{ row }\">\r\n                  <div v-if=\"item.Code == 'Is_Component'\">\r\n                      <span v-if=\"row.Is_Component === 'True'\">否</span>\r\n                      <span v-else-if=\"row.Is_Component === 'False'\">是</span>\r\n                      <span v-else>-</span>\r\n                    </div>\r\n                </template> -->\r\n                <template #default=\"{ row }\">\r\n                  <div v-if=\"item.Code == 'SteelName'\">\r\n                    <el-tag\r\n                      v-if=\"row.Is_Change\"\r\n                      style=\"margin-right: 8px\"\r\n                      type=\"danger\"\r\n                    >变</el-tag>\r\n                    <el-tag\r\n                      v-if=\"row.stopFlag\"\r\n                      style=\"margin-right: 8px\"\r\n                      type=\"danger\"\r\n                    >停</el-tag>\r\n                    <!-- :class=\"[{ isPicActive: row.Drawing !== '暂无' }]\" -->\r\n                    <span\r\n                      class=\"isPicActive\"\r\n                      @click=\"getComponentInfo(row)\"\r\n                    >\r\n                      {{ row[item.Code] | displayValue }}</span>\r\n                  </div>\r\n                  <div v-else-if=\"item.Code == 'SteelAmount'\">\r\n                    <span\r\n                      v-if=\"row.Is_Component_Status == true\"\r\n                      style=\"color: #298dff\"\r\n                    >\r\n                      {{ row[item.Code] | displayValue }} 件</span>\r\n                    <span\r\n                      v-else\r\n                      style=\"color: #298dff; cursor: pointer\"\r\n                      @click=\"handleViewModel(row)\"\r\n                    >\r\n                      {{ row[item.Code] | displayValue }} 件</span>\r\n                  </div>\r\n                  <div v-else-if=\"item.Code == 'SchedulingNum'\">\r\n                    <span\r\n                      v-if=\"row[item.Code]\"\r\n                      style=\"color: #298dff; cursor: pointer\"\r\n                      @click=\"handleViewScheduling(row)\"\r\n                    >{{ row[item.Code] + \" 件\" }}</span>\r\n                    <span v-else>-</span>\r\n                  </div>\r\n                  <div v-else-if=\"item.Code == 'SH'\">\r\n                    <el-link\r\n                      type=\"primary\"\r\n                      @click=\"handleViewSH(row, 0)\"\r\n                    >查看\r\n                    </el-link>\r\n                  </div>\r\n                  <div v-else-if=\"item.Code == 'Part'\">\r\n                    <el-link\r\n                      type=\"primary\"\r\n                      @click=\"handleViewPart(row)\"\r\n                    >查看\r\n                    </el-link>\r\n                  </div>\r\n                  <div v-else-if=\"item.Code == 'Is_Component'\">\r\n                    <span>\r\n                      <!--                      这玩意叫 是否是直发件 -->\r\n                      <el-tag\r\n                        v-if=\"row.Is_Component === 'True'\"\r\n                        type=\"danger\"\r\n                      >否</el-tag>\r\n                      <el-tag v-else type=\"success\">是</el-tag>\r\n                    </span>\r\n                  </div>\r\n                  <div v-else-if=\"item.Code == 'Is_Component_Status'\">\r\n                    <span>\r\n                      <el-tag\r\n                        v-if=\"row.Is_Component === 'True'\"\r\n                        type=\"danger\"\r\n                      >否</el-tag>\r\n                      <el-tag v-else type=\"success\">是</el-tag>\r\n                    </span>\r\n                  </div>\r\n                  <div v-else-if=\"item.Code == 'Drawing'\">\r\n                    <span\r\n                      v-if=\"row.Drawing !== '暂无'\"\r\n                      style=\"color: #298dff; cursor: pointer\"\r\n                      @click=\"getComponentInfo(row)\"\r\n                    >\r\n                      {{ row[item.Code] | displayValue }}\r\n                    </span>\r\n                    <span v-else> {{ row[item.Code] | displayValue }}</span>\r\n                  </div>\r\n\r\n                  <div v-else>\r\n                    <span>{{ row[item.Code] || \"-\" }}</span>\r\n                  </div>\r\n                </template>\r\n              </vxe-column>\r\n\r\n              <vxe-column\r\n                fixed=\"right\"\r\n                align=\"left\"\r\n                title=\"操作\"\r\n                width=\"150\"\r\n                show-overflow\r\n              >\r\n                <template #default=\"{ row }\">\r\n                  <el-button\r\n                    type=\"text\"\r\n                    @click=\"handleView(row)\"\r\n                  >详情\r\n                  </el-button>\r\n                  <el-button\r\n                    :disabled=\"row.stopFlag\"\r\n                    type=\"text\"\r\n                    @click=\"handleEdit(row)\"\r\n                  >编辑\r\n                  </el-button>\r\n                  <el-button\r\n                    type=\"text\"\r\n                    @click=\"handleTrack(row)\"\r\n                  >轨迹图\r\n                  </el-button>\r\n                </template>\r\n              </vxe-column>\r\n            </vxe-table>\r\n          </div>\r\n          <div class=\"cs-bottom\">\r\n            <Pagination\r\n              class=\"cs-table-pagination\"\r\n              :total=\"total\"\r\n              max-height=\"100%\"\r\n              :page-sizes=\"tablePageSize\"\r\n              :page.sync=\"queryInfo.Page\"\r\n              :limit.sync=\"queryInfo.PageSize\"\r\n              layout=\"total, sizes, prev, pager, next, jumper\"\r\n              @pagination=\"changePage\"\r\n            >\r\n              <!--                  <span class=\"pg-input\">\r\n                      <el-select\r\n                        v-model.number=\"queryInfo.PageSize\"\r\n                        allow-create\r\n                        filterable\r\n                        default-first-option\r\n                        @change=\"changePage\"\r\n                      >\r\n                        <el-option v-for=\"(item,index) in customPageSize\" :key=\"index\" :label=\"`${item}条/页`\" :value=\"item\" />\r\n                      </el-select>\r\n                    </span>-->\r\n            </Pagination>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <el-dialog\r\n      v-if=\"dialogVisible\"\r\n      ref=\"content\"\r\n      v-el-drag-dialog\r\n      :title=\"title\"\r\n      :visible.sync=\"dialogVisible\"\r\n      :width=\"width\"\r\n      class=\"z-dialog\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <component\r\n        :is=\"currentComponent\"\r\n        ref=\"content\"\r\n        :select-list=\"selectList\"\r\n        :custom-params=\"customDialogParams\"\r\n        :type-id=\"customParams.TypeId\"\r\n        :type-entity=\"typeEntity\"\r\n        :params-steel=\"treeParamsSteel\"\r\n        :project-id=\"customParams.Project_Id\"\r\n        :sys-project-id=\"customParams.Sys_Project_Id\"\r\n        @close=\"handleClose\"\r\n        @refresh=\"fetchData\"\r\n        @checkPackage=\"handleComponentPack\"\r\n        @checkSteelMeans=\"handleSteelMeans\"\r\n        @checkModelList=\"handleSteelExport\"\r\n        @locationExport=\"locationExport\"\r\n      />\r\n    </el-dialog>\r\n    <bimdialog\r\n      ref=\"dialog\"\r\n      :is-auto-split=\"isAutoSplit\"\r\n      :type-entity=\"typeEntity\"\r\n      @getData=\"fetchData\"\r\n      @getProjectAreaData=\"fetchTreeData\"\r\n    />\r\n    <el-drawer\r\n      :visible.sync=\"trackDrawer\"\r\n      direction=\"rtl\"\r\n      size=\"30%\"\r\n      destroy-on-close\r\n      custom-class=\"trackDrawerClass\"\r\n    >\r\n      <template #title>\r\n        <div>\r\n          <span>{{ trackDrawerTitle }}</span>\r\n          <span style=\"margin-left: 24px\">{{\r\n            trackDrawerData.SteelAmount\r\n          }}</span>\r\n        </div>\r\n      </template>\r\n      <TracePlot :track-drawer-data=\"trackDrawerData\" />\r\n    </el-drawer>\r\n\r\n    <comDrawdialog ref=\"comDrawdialogRef\" @getData=\"fetchData\" />\r\n    <modelDrawing ref=\"modelDrawingRef\" type=\"构件\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetPreferenceSettingValue } from '@/api/sys/system-setting'\r\nimport { GetGridByCode } from '@/api/sys'\r\nimport { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'\r\nimport {\r\n  GetComponentImportDetailPageList,\r\n  DeleteComponents,\r\n  DeleteAllComponentWithQuery,\r\n  GetComponentSummaryInfo,\r\n  ExportComponentInfo,\r\n  ExportComponentSchedulingInfo,\r\n  ExportThreeBom,\r\n  ExportDeepenFullSchedulingInfo\r\n} from '@/api/PRO/component'\r\nimport {\r\n  GetProjectAreaTreeList,\r\n  GetInstallUnitIdNameList\r\n} from '@/api/PRO/project'\r\nimport { getConfigure } from '@/api/user'\r\nimport { GetCompTypeTree } from '@/api/PRO/component-type'\r\nimport { GetSteelCadAndBimId } from '@/api/PRO/component'\r\nimport { GetFileType } from '@/api/sys'\r\n\r\nimport TreeDetail from '@/components/TreeDetail/index.vue'\r\nimport TopHeader from '@/components/TopHeader/index.vue'\r\nimport comImport from '@/views/PRO/component-list/v4/component/Import.vue'\r\nimport ComponentsHistory from '@/views/PRO/component-list/v4/component/ComponentsHistory.vue'\r\nimport comImportByFactory from '@/views/PRO/component-list/v4/component/ImportByFactory.vue'\r\nimport HistoryExport from '@/views/PRO/component-list/v4/component/HistoryExport.vue'\r\nimport BatchEdit from '@/views/PRO/component-list/v4/component/BatchEditor.vue'\r\nimport ComponentPack from '@/views/PRO/component-list/v4/component/ComponentPack/index.vue'\r\nimport Edit from '@/views/PRO/component-list/v4/component/Edit.vue'\r\nimport OneClickGeneratePack from '@/views/PRO/component-list/v4/component/OneClickGeneratePack.vue'\r\nimport GeneratePack from '@/views/PRO/component-list/v4/component/GeneratePack.vue'\r\nimport ProductionConfirm from '@/views/PRO/component-list/v4/component/ProductionConfirm.vue'\r\nimport PartList from '@/views/PRO/component-list/v4/component/PartList.vue'\r\nimport SteelMeans from '@/views/PRO/component-list/v4/component/SteelMeans.vue'\r\nimport ProcessData from '@/views/PRO/component-list/v4/component/ProcessData.vue'\r\nimport ModelComponentCode from '@/views/PRO/component-list/v4/component/ModelComponentCode.vue'\r\nimport ProductionDetails from '@/views/PRO/component-list/v4/component/ProductionDetails.vue'\r\nimport ModelListImport from '@/views/PRO/component-list/v4/component/ModelListImport.vue'\r\nimport comDrawdialog from '@/views/PRO/production-order/deepen-files/dialog.vue' // 深化文件-构件详图导入\r\n\r\nimport elDragDialog from '@/directive/el-drag-dialog'\r\nimport Pagination from '@/components/Pagination/index.vue'\r\nimport { timeFormat } from '@/filters'\r\n// import { Column, Header, Table, Tooltip } from 'vxe-table'\r\n// import Vue from 'vue'\r\nimport AuthButtons from '@/mixins/auth-buttons'\r\nimport bimdialog from '@/views/PRO/component-list/v4/component/bimdialog.vue'\r\nimport axios from 'axios'\r\n\r\nimport sysUseType from '@/directive/sys-use-type'\r\nimport { combineURL } from '@/utils'\r\nimport { tablePageSize } from '@/views/PRO/setting'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport { baseUrl } from '@/utils/baseurl'\r\nimport { findFirstNode } from '@/utils/tree'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nimport LocationImport from '@/views/PRO/component-list/v4/component/LocationImport.vue'\r\nimport ExpandableSection from '@/components/ExpandableSection/index.vue'\r\nimport TracePlot from '@/views/PRO/component-list/v4/component/TracePlot.vue'\r\nimport numeral from 'numeral'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\nimport { mapGetters } from 'vuex'\r\n\r\nimport modelDrawing from '@/views/PRO/components/modelDrawing.vue'\r\n// Vue.use(Header).use(Column).use(Tooltip).use(Table)\r\nconst SPLIT_SYMBOL = '$_$'\r\nexport default {\r\n  directives: { elDragDialog, sysUseType },\r\n  components: {\r\n    ExpandableSection,\r\n    LocationImport,\r\n    TreeDetail,\r\n    TopHeader,\r\n    comImport,\r\n    comImportByFactory,\r\n    BatchEdit,\r\n    HistoryExport,\r\n    GeneratePack,\r\n    Edit,\r\n    ComponentPack,\r\n    OneClickGeneratePack,\r\n    Pagination,\r\n    bimdialog,\r\n    ComponentsHistory,\r\n    ProductionConfirm,\r\n    PartList,\r\n    SteelMeans,\r\n    ModelComponentCode,\r\n    ProductionDetails,\r\n    ModelListImport,\r\n    comDrawdialog,\r\n    TracePlot,\r\n    modelDrawing,\r\n    ProcessData\r\n  },\r\n  mixins: [AuthButtons],\r\n  data() {\r\n    return {\r\n      allStopFlag: false,\r\n      showExpand: true,\r\n      isAutoSplit: undefined,\r\n      tablePageSize: tablePageSize,\r\n      syncVisible: false,\r\n      syncForm: {\r\n        Is_Sync_To_Part: null\r\n      },\r\n      syncRules: {\r\n        Is_Sync_To_Part: {\r\n          required: true,\r\n          message: '请选择是否同步到相关零件',\r\n          trigger: 'change'\r\n        }\r\n      },\r\n      treeSelectParams: {\r\n        placeholder: '请选择',\r\n        clearable: true\r\n      },\r\n      ObjectTypeList: {\r\n        // 构件类型\r\n        'check-strictly': true,\r\n        'default-expand-all': true,\r\n        clickParent: true,\r\n        data: [],\r\n        props: {\r\n          children: 'Children',\r\n          label: 'Label',\r\n          value: 'Data'\r\n        }\r\n      },\r\n\r\n      treeData: [],\r\n      treeLoading: true,\r\n      expandedKey: '', // -1是全部\r\n      projectName: '',\r\n      statusType: '',\r\n      searchHeight: 0,\r\n      searchStatus: true,\r\n      tbData: [],\r\n      total: 0,\r\n      tbLoading: false,\r\n      pgLoading: false,\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: 10,\r\n        ParameterJson: []\r\n      },\r\n      customPageSize: [10, 20, 50, 100],\r\n      installUnitIdNameList: [], // 批次数组\r\n      nameMode: 1,\r\n      names: '',\r\n      customParams: {\r\n        Code_Like: '',\r\n        Spec: '',\r\n        Texture: '',\r\n        Is_Direct: '',\r\n        Create_UserName: '',\r\n        InstallUnit_Ids: [],\r\n        SteelNames: '',\r\n        TypeId: '',\r\n        Sys_Project_Id: '',\r\n        Project_Id: '',\r\n        Area_Id: '',\r\n        Project_Name: '',\r\n        SteelCode: '',\r\n        SteelNumber: '',\r\n        Area_Name: ''\r\n      },\r\n      Unit: '',\r\n      Proportion: 0, // 专业的单位换算\r\n      customDialogParams: {},\r\n      dialogVisible: false,\r\n      currentComponent: '',\r\n      selectList: [],\r\n      factoryOption: [],\r\n      projectList: [],\r\n      typeOption: [],\r\n      treeParamsSteel: [],\r\n      columns: [],\r\n      columnsOption: [\r\n        // { Display_Name: \"构件名称\", Code: \"SteelName\" },\r\n        // { Display_Name: \"规格\", Code: \"SteelSpec\" },\r\n        // { Display_Name: \"长度\", Code: \"SteelLength\" },\r\n        // { Display_Name: \"构件类型\", Code: \"SteelType\" },\r\n        // { Display_Name: \"材质\", Code: \"SteelMaterial\" },\r\n        // { Display_Name: \"深化数量\", Code: \"SteelAmount\" },\r\n        // { Display_Name: \"排产数量\", Code: \"SchedulingNum\" },\r\n        // { Display_Name: \"单重\", Code: \"SteelWeight\" },\r\n        // { Display_Name: \"总重\", Code: \"SteelAllWeight\" },\r\n        // { Display_Name: \"直发件\", Code: \"Is_Component_Status\" },\r\n        // { Display_Name: \"操作人\", Code: \"Create_UserName\" },\r\n        // { Display_Name: \"操作时间\", Code: \"Create_Date\" },\r\n      ],\r\n      title: '',\r\n      width: '60%',\r\n      tipLabel: '',\r\n      monomerList: [],\r\n      mode: '',\r\n      isMonomer: true,\r\n      historyVisible: false,\r\n      sysUseType: undefined,\r\n      productionConfirm: '',\r\n      SteelFormEditData: {},\r\n      deepenTotalLength: 0, // 深化总量\r\n      SteelAmountTotal: 0, // 深化总量\r\n      SchedulingNumTotal: 0, // 排产总量\r\n      SteelAllWeightTotal: 0, // 深化总重\r\n      SchedulingAllWeightTotal: 0, // 排产总重\r\n      FinishCountTotal: 0, // 完成数量\r\n      FinishWeightTotal: 0, // 完成重量\r\n      IsComponentTotal: 0,\r\n      TotalGrossWeight: 0,\r\n      IsComponentTotalSteelAllWeight: 0,\r\n      leftCol: 4,\r\n      rightCol: 40,\r\n      leftWidth: 320,\r\n      drawer: false,\r\n      scheduleLoading: false,\r\n      command: 'cover', //  cover覆盖导入  add新增导入\r\n      currentLastLevel: false, //  当前区域是否是最内层\r\n      cadRowCode: '',\r\n      cadRowProjectId: '',\r\n      IsUploadCad: false,\r\n      comDrawData: {},\r\n      currentNode: {},\r\n      trackDrawer: false,\r\n      trackDrawerTitle: '',\r\n      trackDrawerData: {},\r\n      levelName: '',\r\n      levelCode: ''\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters('tenant', ['isVersionFour']),\r\n    typeEntity() {\r\n      return this.typeOption.find((i) => i.Id === this.customParams.TypeId)\r\n    },\r\n    showTotalLength() {\r\n      const arr = [\r\n        this.customParams.Fuzzy_Search_Col,\r\n        this.customParams.Fuzzy_Search_Col2,\r\n        this.customParams.Fuzzy_Search_Col3,\r\n        this.customParams.Fuzzy_Search_Col4\r\n      ]\r\n      return arr.includes('SteelLength') && arr.includes('SteelSpec')\r\n    },\r\n    filterText() {\r\n      return this.projectName + SPLIT_SYMBOL + this.statusType\r\n    },\r\n    TotalGrossWeightT() {\r\n      return numeral(this.TotalGrossWeight || 0).format('0.[000]')\r\n    }\r\n  },\r\n  watch: {\r\n    'customParams.TypeId': function(newValue, oldValue) {\r\n      console.log({ oldValue })\r\n      if (oldValue && oldValue !== '0') {\r\n        this.fetchData()\r\n      }\r\n    },\r\n    names(n, o) {\r\n      this.changeMode()\r\n    },\r\n    nameMode(n, o) {\r\n      this.changeMode()\r\n    }\r\n  },\r\n  async created() {\r\n    const { currentBOMInfo } = await GetBOMInfo(-1)\r\n    console.log('list', currentBOMInfo)\r\n    this.levelName = currentBOMInfo?.Display_Name\r\n    this.levelCode = currentBOMInfo?.Code\r\n    await this.getPreferenceSettingValue()\r\n    await this.getTypeList()\r\n    // await this.fetchData()\r\n    // await this.getComponentSummaryInfo()\r\n    this.fetchTreeData()\r\n    this.getFileType()\r\n  },\r\n  mounted() {\r\n  },\r\n  activated() {\r\n  },\r\n  methods: {\r\n    changeMode(n) {\r\n      if (this.nameMode === 1) {\r\n        this.customParams.Code_Like = this.names\r\n        this.customParams.SteelNames = ''\r\n      } else {\r\n        this.customParams.Code_Like = ''\r\n        this.customParams.SteelNames = this.names.replace(/\\s+/g, '\\n')\r\n      }\r\n    },\r\n\r\n    getComponentInfo(row) {\r\n      const drawingData = row.Drawing ? row.Drawing.split(',') : [] // 图纸数据\r\n      const fileUrlData = row.File_Url ? row.File_Url.split(',') : [] // 图纸数据文件地址数据\r\n      if (drawingData.length > 0 && fileUrlData.length > 0) {\r\n        this.drawingActive = drawingData[0]\r\n      }\r\n      if (drawingData.length > 0 && fileUrlData.length > 0) {\r\n        this.drawingDataList = drawingData.map((item, index) => ({\r\n          name: item,\r\n          label: item,\r\n          url: fileUrlData[index]\r\n        }))\r\n      }\r\n      this.getComponentInfoDrawing(row)\r\n    },\r\n\r\n    /**\r\n     * 获取featureId 模型构件id   cadId CAD图纸id\r\n     */\r\n    getComponentInfoDrawing(row) {\r\n      const importDetailId = row.Id\r\n      GetSteelCadAndBimId({ importDetailId: importDetailId }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          const _data = res.Data?.[0]\r\n          if (!row.File_Url && !_data.ExtensionName) {\r\n            this.$message({\r\n              message: '当前构件无图纸和模型',\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n\r\n          const drawingData = {\r\n            'extensionName': _data.ExtensionName,\r\n            'fileBim': _data.fileBim,\r\n            'IsUpload': _data.IsUpload,\r\n            'Code': row.SteelName,\r\n            'Sys_Project_Id': row.Sys_Project_Id\r\n          }\r\n\r\n          this.$nextTick((_) => {\r\n            this.$refs.modelDrawingRef.dwgInit(drawingData)\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 项目区域数据集\r\n    fetchTreeData() {\r\n      GetProjectAreaTreeList({\r\n        Type: 0,\r\n        Bom_Level: this.levelCode,\r\n        MenuId: this.$route.meta.Id,\r\n        projectName: this.projectName\r\n      }).then((res) => {\r\n        // const resAll = [\r\n        //   {\r\n        //     ParentNodes: null,\r\n        //     Id: '-1',\r\n        //     Code: '全部',\r\n        //     Label: '全部',\r\n        //     Level: null,\r\n        //     Data: {},\r\n        //     Children: []\r\n        //   }\r\n        // ]\r\n        // const resData = resAll.concat(res.Data)\r\n        if (res.Data.length === 0) {\r\n          this.treeLoading = false\r\n          return\r\n        }\r\n        const resData = res.Data\r\n        resData.map((item) => {\r\n          if (item.Children.length === 0) {\r\n            item.Data.Is_Imported = false\r\n          } else {\r\n            item.Data.Is_Imported = item.Children.some((ich) => {\r\n              return ich.Data.Is_Imported === true\r\n            })\r\n\r\n            item.Is_Directory = true\r\n            item.Children.map((it) => {\r\n              if (it.Children.length > 0) {\r\n                it.Is_Directory = true\r\n              }\r\n            })\r\n          }\r\n          return item\r\n        })\r\n        this.treeData = resData\r\n        if (Object.keys(this.currentNode).length === 0) {\r\n          this.setKey()\r\n        } else {\r\n          this.handleNodeClick(this.currentNode)\r\n        }\r\n        this.treeLoading = false\r\n      })\r\n    },\r\n    // 设置默认选中第一个区域末级节点\r\n    setKey() {\r\n      const deepFilter = (tree) => {\r\n        for (let i = 0; i < tree.length; i++) {\r\n          const item = tree[i]\r\n          const { Data, Children } = item\r\n          console.log(Data)\r\n          if (Data.ParentId && !Children?.length) {\r\n            console.log(Data, '????')\r\n            this.currentNode = Data\r\n            this.handleNodeClick(item)\r\n            return\r\n          } else {\r\n            if (Children && Children.length > 0) {\r\n              return deepFilter(Children)\r\n            } else {\r\n              this.handleNodeClick(item)\r\n              return\r\n            }\r\n          }\r\n        }\r\n      }\r\n      return deepFilter(this.treeData)\r\n    },\r\n    // 选中左侧项目节点\r\n    handleNodeClick(data) {\r\n      this.handleSearch('reset', false, 'default')\r\n      this.currentNode = data\r\n      this.expandedKey = data.Id\r\n      this.$nextTick((_) => {\r\n        const cur = this.$refs['tree'].$refs.tree.getNode(this.expandedKey)\r\n        if (cur) {\r\n          this.isAutoSplit = cur?.data.Data.Is_Auto_Split\r\n        }\r\n      })\r\n      console.log(data, 'data2============')\r\n      this.InstallUnit_Id = ''\r\n      if (data.ParentNodes === null && data.Code !== '全部') {\r\n        this.customParams.Sys_Project_Id = data.Data.Sys_Project_Id\r\n        this.customParams.Project_Id = data.Data.Id\r\n        this.customParams.Area_Name = ''\r\n        this.customParams.Area_Id = ''\r\n      } else {\r\n        this.customParams.Sys_Project_Id = data.Data.Sys_Project_Id\r\n        this.customParams.Project_Id = data.Data.Project_Id\r\n        this.customParams.Area_Id = data.Data.Id\r\n      }\r\n      this.isAutoSplit = data.Data?.Is_Auto_Split\r\n      this.currentLastLevel = !!(data.Data.Level && data.Children.length === 0)\r\n      if (this.currentLastLevel) {\r\n        this.customParams.Project_Name = data.Data?.Project_Name\r\n        this.customParams.Area_Name = data.Label\r\n      }\r\n      const dataID = data.Id === -1 ? '' : data.Id\r\n      console.log(\r\n        this.customParams.Sys_Project_Id,\r\n        'this.customParams.Sys_Project_Id============11111'\r\n      )\r\n      console.log(\r\n        this.customParams.Area_Id,\r\n        'this.customParams.Area_Id============11111'\r\n      )\r\n      console.log(\r\n        this.customParams.Project_Id,\r\n        'this.customParams.Project_Id============11111'\r\n      )\r\n      this.pgLoading = true\r\n      this.getInstallUnitIdNameList(dataID, data)\r\n      this.fetchData()\r\n      this.getComponentSummaryInfo()\r\n    },\r\n\r\n    // 获取批次\r\n    getInstallUnitIdNameList(id, data) {\r\n      console.log(data, '???????????')\r\n      if (id === '' || data.Children.length > 0) {\r\n        this.installUnitIdNameList = []\r\n      } else {\r\n        GetInstallUnitIdNameList({ Area_Id: id }).then((res) => {\r\n          this.installUnitIdNameList = res.Data\r\n        })\r\n      }\r\n    },\r\n\r\n    // 搜索\r\n    handleSearch(reset, hasSearch = true, type = '') {\r\n      this.searchStatus = false\r\n      if (reset) {\r\n        this.$refs.customParams.resetFields()\r\n        this.names = ''\r\n        // this.customParams.Fuzzy_Search_Col = 'SteelName'\r\n        // this.customParams.Fuzzy_Search_Col2 = 'SteelMaterial'\r\n        // this.customParams.Fuzzy_Search_Col3 = 'SteelSpec'\r\n        // this.customParams.Fuzzy_Search_Col4 = 'SteelWeight'\r\n        this.searchStatus = true\r\n      }\r\n      // let SteelNames = this.customParams.SteelNamesFormat.trim()\r\n      // SteelNames = SteelNames.replace(/\\s+/g, '\\n')\r\n      // this.customParams.SteelNames = SteelNames\r\n\r\n      hasSearch && this.fetchData()\r\n      if (type === '') {\r\n        this.getComponentSummaryInfo()\r\n      }\r\n    },\r\n\r\n    // 获取系统偏好，是否弹出生产管理过程的弹框\r\n    async getPreferenceSettingValue() {\r\n      GetPreferenceSettingValue({ Code: 'Production_Confirm' }).then((res) => {\r\n        this.productionConfirm = res.Data\r\n      })\r\n    },\r\n\r\n    // 构件统计\r\n    getComponentSummaryInfo() {\r\n      GetComponentSummaryInfo({\r\n        ...this.customParams\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.SteelAmountTotal = Math.round(res.Data.DeepenNum * 1000) / 1000 // 深化总量\r\n          this.SchedulingNumTotal =\r\n            Math.round(res.Data.SchedulingNum * 1000) / 1000 // 排产总量\r\n          this.SteelAllWeightTotal =\r\n            Math.round(res.Data.DeepenWeight * 1000) / 1000 // 深化总重\r\n          this.SchedulingAllWeightTotal =\r\n            Math.round(res.Data.SchedulingWeight * 1000) / 1000 // 排产总重\r\n          this.FinishCountTotal =\r\n            Math.round(res.Data.Finish_Count * 1000) / 1000 // 完成总数\r\n          this.FinishWeightTotal =\r\n            Math.round(res.Data.Finish_Weight * 1000) / 1000 // 完成总重\r\n          this.IsComponentTotal = res.Data.Direct_Count || 0\r\n          this.TotalGrossWeight = res.Data.TotalGrossWeight || 0\r\n          this.IsComponentTotalSteelAllWeight =\r\n            Math.round((res.Data.Direct_Weight || 0) * 1000) / 1000\r\n          this.allStopFlag = !!res.Data.Is_Stop\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 工序完成量\r\n    getProcessData() {\r\n      this.width = '40%'\r\n      this.generateComponent('构件工序完成量', 'ProcessData')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(this.customParams, this.selectList.map((v) => v.Id).toString())\r\n      })\r\n    },\r\n\r\n    // 获取表格配置\r\n    getTableConfig(code) {\r\n      return new Promise((resolve) => {\r\n        GetGridByCode({\r\n          code:\r\n            code +\r\n            ',' +\r\n            this.typeOption.find((i) => i.Id === this.customParams.TypeId).Code\r\n        }).then((res) => {\r\n          const { IsSucceed, Data, Message } = res\r\n          if (IsSucceed) {\r\n            if (!Data) {\r\n              this.$message.error('当前专业没有配置相对应表格')\r\n              this.tbLoading = true\r\n              return\r\n            }\r\n            this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)\r\n            const list = Data.ColumnList || []\r\n            this.columns = list\r\n              .filter((v) => v.Is_Display)\r\n              .map((item) => {\r\n                if (item.Code === 'SteelName') {\r\n                  item.fixed = 'left'\r\n                }\r\n                return item\r\n              })\r\n            this.queryInfo.PageSize = +Data.Grid.Row_Number || 20\r\n\r\n            const selectOption = JSON.parse(JSON.stringify(this.columns))\r\n\r\n            console.log(selectOption)\r\n            this.columnsOption = selectOption.filter((v) => {\r\n              return (\r\n                v.Display_Name !== '操作时间' &&\r\n                v.Display_Name !== '安装位置' &&\r\n                v.Display_Name !== '模型ID' &&\r\n                v.Display_Name !== '深化资料' &&\r\n                v.Display_Name !== '备注' &&\r\n                v.Display_Name !== '零件' &&\r\n                v.Display_Name !== '排产数量' &&\r\n                v.Code.indexOf('Attr') === -1 &&\r\n                v.Display_Name !== '构件类型' &&\r\n                v.Display_Name !== '批次'\r\n              )\r\n            })\r\n            resolve(this.columns)\r\n          } else {\r\n            this.$message({\r\n              message: Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    // 构件列表\r\n    async getComponentImportDetailPageList() {\r\n      try {\r\n        const res = await GetComponentImportDetailPageList({\r\n          ...this.queryInfo,\r\n          ...this.customParams\r\n        })\r\n        if (res.IsSucceed) {\r\n          this.tbData = (res.Data.Data || []).map((v) => {\r\n            v.Create_Date = timeFormat(\r\n              v.Create_Date,\r\n              '{y}-{m}-{d} {h}:{i}:{s}'\r\n            )\r\n            return v\r\n          })\r\n          this.deepenTotalLength = res.Data.DeepenTotalLength || 0\r\n          this.queryInfo.PageSize = res.Data.PageSize\r\n          this.total = res.Data.TotalCount\r\n          this.selectList = []\r\n          await this.getStopList()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      } catch (e) {\r\n        this.$message({\r\n          message: '获取构件列表失败',\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n    async getStopList() {\r\n      if (!this.tbData || !this.tbData.length) return\r\n      const submitObj = this.tbData.map((item) => ({\r\n        Id: item.Id,\r\n        Type: 2,\r\n        Bom_Level: this.levelCode\r\n      }))\r\n      try {\r\n        const res = await GetStopList(submitObj)\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach((item) => {\r\n            stopMap[item.Id] = item.Is_Stop !== null\r\n          })\r\n          this.tbData.forEach(row => {\r\n            if (stopMap[row.Id]) {\r\n              this.$set(row, 'stopFlag', stopMap[row.Id])\r\n            }\r\n          })\r\n        }\r\n      } catch (e) {}\r\n    },\r\n\r\n    // 获取表格数据\r\n    async fetchData() {\r\n      console.log('列表更新成功')\r\n      // 分开获取，提高接口速度\r\n      await this.getTableConfig('plm_component_page_list')\r\n      this.tbLoading = true\r\n      this.getComponentImportDetailPageList().then((res) => {\r\n        this.tbLoading = false\r\n        this.pgLoading = false\r\n      })\r\n    },\r\n\r\n    async changePage() {\r\n      this.tbLoading = true\r\n      if (\r\n        typeof this.queryInfo.PageSize !== 'number' ||\r\n        this.queryInfo.PageSize < 1\r\n      ) {\r\n        this.queryInfo.PageSize = 10\r\n      }\r\n      this.getComponentImportDetailPageList().then((res) => {\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n\r\n    tbSelectChange(array) {\r\n      console.log('array', array)\r\n      this.selectList = array.records\r\n      this.SteelAmountTotal = 0\r\n      this.SchedulingNumTotal = 0\r\n      this.SteelAllWeightTotal = 0\r\n      this.SchedulingAllWeightTotal = 0\r\n      this.FinishCountTotal = 0\r\n      this.FinishWeightTotal = 0\r\n      this.IsComponentTotal = 0\r\n      this.TotalGrossWeight = 0\r\n      this.IsComponentTotalSteelAllWeight = 0\r\n      let SteelAllWeightTotalTemp = 0\r\n      let SchedulingAllWeightTotalTemp = 0\r\n      let FinishWeightTotalTemp = 0\r\n      let IsComponentTotalSteelAllWeightTemp = 0\r\n      if (this.selectList.length > 0) {\r\n        this.selectList.forEach((item) => {\r\n          const schedulingNum =\r\n            item.SchedulingNum == null ? 0 : item.SchedulingNum\r\n          this.SteelAmountTotal += item.SteelAmount\r\n          this.SchedulingNumTotal += item.SchedulingNum\r\n          this.FinishCountTotal += item.Finish_Count\r\n          this.TotalGrossWeight += item.TotalGrossWeight / 1000\r\n          SteelAllWeightTotalTemp += item.SteelAllWeight\r\n          SchedulingAllWeightTotalTemp += item.SteelWeight * schedulingNum\r\n          FinishWeightTotalTemp += item.Finish_Weight\r\n          this.IsComponentTotal +=\r\n            item.Is_Component === 'False' ? item.SteelAmount : 0\r\n          IsComponentTotalSteelAllWeightTemp +=\r\n            item.Is_Component === 'False' ? item.SteelAllWeight : 0\r\n        })\r\n        this.SteelAllWeightTotal =\r\n          Math.round((SteelAllWeightTotalTemp / this.Proportion) * 1000) / 1000\r\n        this.SchedulingAllWeightTotal =\r\n          Math.round((SchedulingAllWeightTotalTemp / this.Proportion) * 1000) /\r\n          1000\r\n        this.FinishWeightTotal =\r\n          Math.round((FinishWeightTotalTemp / this.Proportion) * 1000) / 1000\r\n        this.IsComponentTotalSteelAllWeight =\r\n          Math.round(\r\n            (IsComponentTotalSteelAllWeightTemp / this.Proportion) * 1000\r\n          ) / 1000\r\n      } else {\r\n        this.getComponentSummaryInfo()\r\n      }\r\n    },\r\n\r\n    getTbData(data) {\r\n      const { CountInfo } = data\r\n      // this.tipLabel = `累计上传构件${YearSteel}件，总重${YearAllWeight}t。`\r\n      this.tipLabel = CountInfo\r\n    },\r\n\r\n    async getTypeList() {\r\n      const res = await GetFactoryProfessionalByCode({\r\n        factoryId: localStorage.getItem('CurReferenceId')\r\n      })\r\n      const data = res.Data\r\n      if (res.IsSucceed) {\r\n        this.Proportion = data[0].Proportion\r\n        this.Unit = data[0].Unit\r\n        this.typeOption = Object.freeze(data)\r\n        if (this.typeOption.length > 0) {\r\n          this.customParams.TypeId = this.typeOption[0]?.Id\r\n        }\r\n        this.getCompTypeTree(this.typeOption[0].Code)\r\n      } else {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n      }\r\n    },\r\n\r\n    getCompTypeTree(Code) {\r\n      this.loading = true\r\n      GetCompTypeTree({\r\n        professional: Code\r\n      })\r\n        .then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.treeParamsSteel = res.Data\r\n            this.ObjectTypeList.data = res.Data\r\n            this.$nextTick((_) => {\r\n              this.$refs.treeSelectObjectType.treeDataUpdateFun(res.Data)\r\n            })\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n            this.treeData = []\r\n          }\r\n        })\r\n        .finally((_) => {\r\n          this.loading = false\r\n        })\r\n    },\r\n\r\n    // 删除查询结果\r\n    handleSearchDelete() {\r\n      if (this.customParams.Project_Id === '') {\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '请选择项目'\r\n        })\r\n        return false\r\n      }\r\n      this.$confirm('此操作将删除搜索的数据, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          DeleteAllComponentWithQuery({\r\n            ...this.customParams\r\n          }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.fetchData()\r\n              this.$message({\r\n                message: '删除成功',\r\n                type: 'success'\r\n              })\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n\r\n    // 删除选中\r\n    handleDelete() {\r\n      this.$confirm('此操作将删除选择数据, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          this.tbLoading = true\r\n          DeleteComponents({\r\n            ids: this.selectList.map((v) => v.Id).toString()\r\n          }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.fetchData()\r\n              this.fetchTreeData()\r\n              this.$message({\r\n                message: '删除成功',\r\n                type: 'success'\r\n              })\r\n            } else {\r\n              this.$message({\r\n                message: res.Message,\r\n                type: 'error'\r\n              })\r\n            }\r\n          })\r\n            .finally(() => {\r\n              this.tbLoading = false\r\n            })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n\r\n    handleEdit(row) {\r\n      this.width = '45%'\r\n      this.generateComponent('编辑构件', 'Edit')\r\n      this.$nextTick((_) => {\r\n        row.isReadOnly = false\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n\r\n    handleBatchEdit() {\r\n      const SchedulArr = this.selectList.filter((item) => {\r\n        return item.SchedulingNum != null && item.SchedulingNum > 0\r\n      })\r\n      if (SchedulArr.length > 0) {\r\n        this.$message({\r\n          type: 'error',\r\n          message: '选中行包含已排产的构件,编辑信息需要进行变更操作'\r\n        })\r\n      } else {\r\n        this.width = '40%'\r\n        this.generateComponent('批量编辑', 'BatchEdit')\r\n        this.$nextTick((_) => {\r\n          this.$refs['content'].init(this.selectList, this.columnsOption)\r\n        })\r\n      }\r\n    },\r\n\r\n    handleView(row) {\r\n      this.width = '45%'\r\n      this.generateComponent('查看构件', 'Edit')\r\n      this.$nextTick((_) => {\r\n        row.isReadOnly = true\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n\r\n    // 查看构件的零件\r\n    handleViewPart(row) {\r\n      this.width = '60%'\r\n      this.generateComponent('零部件清单', 'PartList')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n\r\n    // 查看深化资料 type 0构件  1零件\r\n    handleViewSH(row, type) {\r\n      this.width = '40%'\r\n      this.generateComponent('查看深化资料', 'SteelMeans')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(row, type)\r\n      })\r\n    },\r\n\r\n    // 回调查看零件的深化资料\r\n    handleSteelMeans(row) {\r\n      this.handleViewSH(row, 1)\r\n    },\r\n\r\n    // 深化模型唯一码\r\n    handleViewModel(row) {\r\n      this.width = '40%'\r\n      this.generateComponent('模型构件唯一码列表', 'ModelComponentCode')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n\r\n    // 排产数量点击的生产详情\r\n    handleViewScheduling(row) {\r\n      this.width = '30%'\r\n      this.generateComponent('生产详情', 'ProductionDetails')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(row)\r\n      })\r\n    },\r\n\r\n    handleHistory(row) {\r\n      console.log({ row })\r\n      this.generateComponent('构件变更历史', 'ComponentsHistory')\r\n      this.customDialogParams = {\r\n        steelUnique: row.SteelUnique\r\n      }\r\n    },\r\n\r\n    locationExport() {\r\n      this.handleSteelExport(3)\r\n    },\r\n    handleExport(v) {\r\n      if (v === 'com') {\r\n        this.handleSteelExport(2)\r\n      } else {\r\n        this.handleExportAll()\r\n      }\r\n    },\r\n    handleExportAll() {\r\n      ExportThreeBom({\r\n        // model: {\r\n        //   Project_Id: this.customParams.Project_Id,\r\n        //   Area_Id: this.customParams.Area_Id\r\n        // }\r\n        ...this.queryInfo,\r\n        ...this.customParams,\r\n        Ids: this.selectList.map((v) => v.Id).toString()\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 导出构件  type 0导出未绑定的构件   1导出已绑定的构件    2导出构件\r\n    async handleSteelExport(type) {\r\n      if (\r\n        this.customParams.Sys_Project_Id === '' &&\r\n        this.selectList.length === 0\r\n      ) {\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '请选择项目'\r\n        })\r\n        return false\r\n      }\r\n      const obj = {\r\n        Bom_Level: this.levelCode,\r\n        Type: type,\r\n        Import_Detail_Ids: this.selectList.map((v) => v.Id),\r\n        ...this.customParams,\r\n        Sys_Project_Id: this.customParams.Sys_Project_Id\r\n      }\r\n      const res = await ExportComponentInfo(obj)\r\n\r\n      if (!res.IsSucceed) {\r\n        this.$message({\r\n          message: res.Message,\r\n          type: 'error'\r\n        })\r\n        return\r\n      }\r\n      // eslint-disable-next-line no-unused-vars\r\n      let fileName = localStorage.getItem('ProjectName') + '_构件导出明细'\r\n      if (res.type === 'application/octet-stream') {\r\n        fileName += '.rar'\r\n      } else {\r\n        fileName += '.xls'\r\n      }\r\n      window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n      // downloadBlobFile(res.Data, fileName, ' ')\r\n    },\r\n\r\n    // 获取文件的arraybuffer格式并传入进行打包准备\r\n    getFile(url) {\r\n      return new Promise((resolve, reject) => {\r\n        axios({\r\n          method: 'get',\r\n          url,\r\n          responseType: 'arraybuffer'\r\n        })\r\n          .then((res) => {\r\n            resolve(res.data)\r\n          })\r\n          .catch((error) => {\r\n            reject(error.toString())\r\n          })\r\n      })\r\n    },\r\n\r\n    // 模型清单导入\r\n    modelListImport() {\r\n      this.width = '30%'\r\n      this.generateComponent('模型清单导入', 'ModelListImport')\r\n    },\r\n\r\n    LocationImport() {\r\n      this.width = '30%'\r\n      this.generateComponent('位置信息导入', 'LocationImport')\r\n    },\r\n\r\n    // 新增导入 or 覆盖导入\r\n    handleCommand(command, type) {\r\n      // console.log(command, 'command')\r\n      // console.log(type, 'type')\r\n      this.command = command\r\n      if (type === 1) {\r\n        this.deepListImport(1)\r\n      } else if (type === 0) {\r\n        this.deepListImport(0)\r\n      }\r\n    },\r\n\r\n    // 打开导入弹框 importType 1零构件 0 构件\r\n    deepListImport(importType) {\r\n      console.log(importType, 'importType')\r\n      const fileType = {\r\n        Catalog_Code: 'PLMDeepenFiles',\r\n        Code: this.typeEntity.Code,\r\n        name: this.typeEntity.Name\r\n      }\r\n      if (this.productionConfirm === 'true' && importType === 0) {\r\n        this.width = '30%'\r\n        this.generateComponent('导入构件', 'ProductionConfirm')\r\n      } else {\r\n        this.$refs.dialog.handleOpen(\r\n          'add',\r\n          fileType,\r\n          null,\r\n          true,\r\n          '',\r\n          importType,\r\n          '',\r\n          this.command,\r\n          this.customParams\r\n        )\r\n      }\r\n    },\r\n\r\n    // 回调是否有生产过程\r\n    deepListImportAgin(productionConfirmData) {\r\n      const fileType = {\r\n        Catalog_Code: 'PLMDeepenFiles',\r\n        Code: this.typeEntity.Code,\r\n        name: this.typeEntity.Name\r\n      }\r\n      this.$refs.dialog.handleOpen(\r\n        'add',\r\n        fileType,\r\n        null,\r\n        true,\r\n        '',\r\n        0,\r\n        productionConfirmData,\r\n        this.command,\r\n        this.customParams\r\n      )\r\n    },\r\n\r\n    // 导出排产单\r\n    handleSchedulingInfoExport() {\r\n      ExportComponentSchedulingInfo({\r\n        ids: this.selectList.map((v) => v.Id).toString()\r\n      }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n          if (res.Message) {\r\n            this.$alert(res.Message, '导出通知', {\r\n              confirmButtonText: '我知道了'\r\n            })\r\n          }\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    handleHistoryExport() {\r\n      if (this.customParams.Project_Id === '') {\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '请选择项目'\r\n        })\r\n        return false\r\n      }\r\n      this.width = '60%'\r\n      this.generateComponent('历史清单导出', 'HistoryExport')\r\n    },\r\n\r\n    handleScheduleExport() {\r\n      this.scheduleLoading = true\r\n      const ids = this.selectList.map((v) => v.Id).toString()\r\n      ExportDeepenFullSchedulingInfo({\r\n        Ids: ids\r\n      })\r\n        .then((res) => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '导出成功',\r\n              type: 'success'\r\n            })\r\n            window.open(combineURL(this.$baseUrl, res.Data), '_blank')\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n        .finally((_) => {\r\n          this.scheduleLoading = false\r\n        })\r\n    },\r\n\r\n    handleComponentPack({ data, type = 2 }) {\r\n      console.log('index', data, type)\r\n      this.width = '80%'\r\n      this.generateComponent('查看构件包', 'ComponentPack')\r\n      this.$nextTick((_) => {\r\n        if (data) {\r\n          this.$refs['content'].getSubmitObj(data)\r\n        }\r\n        this.$refs['content'].handlePackage(type)\r\n      })\r\n    },\r\n\r\n    handleAllPack() {\r\n      this.width = '30%'\r\n      this.generateComponent('查询结果一键打包', 'OneClickGeneratePack')\r\n      this.customDialogParams = this.customParams\r\n    },\r\n\r\n    handleGenerate() {\r\n      this.width = '30%'\r\n      this.generateComponent('生成构件包', 'GeneratePack')\r\n      this.$nextTick((_) => {\r\n        this.$refs['content'].init(this.selectList)\r\n      })\r\n    },\r\n\r\n    handleClose(data) {\r\n      this.dialogVisible = false\r\n\r\n      // 选择是否需要生产管理过程后回调再次弹框 importType肯定是0\r\n      if (data === true || data === false) {\r\n        this.deepListImportAgin(data)\r\n      }\r\n    },\r\n\r\n    generateComponent(title, component) {\r\n      this.title = title\r\n      this.currentComponent = component\r\n      this.dialogVisible = true\r\n    },\r\n    fetchTreeDataLocal() {\r\n      // this.filterText = this.projectName\r\n    },\r\n    customFilterFun(value, data, node) {\r\n      const arr = value.split(SPLIT_SYMBOL)\r\n      const labelVal = arr[0]\r\n      const statusVal = arr[1]\r\n      if (!value) return true\r\n      let parentNode = node.parent\r\n      let labels = [node.label]\r\n      let status = [\r\n        data.Data.Is_Deepen_Change\r\n          ? '已变更'\r\n          : data.Data.Is_Imported\r\n            ? '已导入'\r\n            : '未导入'\r\n      ]\r\n      let level = 1\r\n      while (level < node.level) {\r\n        labels = [...labels, parentNode.label]\r\n        status = [\r\n          ...status,\r\n          data.Data.Is_Deepen_Change\r\n            ? '已变更'\r\n            : data.Data.Is_Imported\r\n              ? '已导入'\r\n              : '未导入'\r\n        ]\r\n        parentNode = parentNode.parent\r\n        level++\r\n      }\r\n      labels = labels.filter((v) => !!v)\r\n      status = status.filter((v) => !!v)\r\n      let resultLabel = true\r\n      let resultStatus = true\r\n      if (this.statusType) {\r\n        resultStatus = status.some((s) => s.indexOf(statusVal) !== -1)\r\n      }\r\n      if (this.projectName) {\r\n        resultLabel = labels.some((s) => s.indexOf(labelVal) !== -1)\r\n      }\r\n      return resultLabel && resultStatus\r\n    },\r\n    //\r\n    async getFileType() {\r\n      const params = {\r\n        catalogCode: 'PLMDeepenFiles'\r\n      }\r\n      const res = await GetFileType(params)\r\n      // 获取构件详图\r\n      const data = res.Data.find((v) => v.Label === '构件详图')\r\n\r\n      this.comDrawData = {\r\n        isSHQD: false,\r\n        Id: data.Id,\r\n        name: data.Label,\r\n        Catalog_Code: data.Code,\r\n        Code: data.Data?.English_Name\r\n      }\r\n\r\n      console.log(this.comDrawData, 'comDrawData')\r\n    },\r\n    // 图纸导入\r\n    handelImport() {\r\n      this.$refs.comDrawdialogRef.handleOpen(\r\n        'add',\r\n        this.comDrawData,\r\n        '',\r\n        false,\r\n        this.customParams.Sys_Project_Id,\r\n        false\r\n      )\r\n    },\r\n\r\n    // 轨迹图\r\n    handleTrack(row) {\r\n      console.log(row, 'row')\r\n      this.trackDrawer = true\r\n      this.trackDrawerTitle = row.SteelName\r\n      this.trackDrawerData = row\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/styles/mixin.scss\";\r\n@import \"~@/styles/tabs.scss\";\r\n\r\n.min900 {\r\n  min-width: 900px;\r\n  overflow: auto;\r\n}\r\n\r\n.app-wrapper {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  overflow: hidden;\r\n\r\n  .cs-left {\r\n    display: flex;\r\n    flex-direction: column;\r\n    margin-right: 20px;\r\n\r\n    .inner-wrapper {\r\n      flex: 1;\r\n      display: flex;\r\n      flex-direction: column;\r\n      padding: 16px 10px 16px 16px;\r\n      border-radius: 4px;\r\n      overflow: hidden;\r\n\r\n      .tree-search {\r\n        display: flex;\r\n\r\n        .search-select {\r\n          margin-right: 8px;\r\n        }\r\n      }\r\n\r\n      .tree-x {\r\n        overflow: hidden;\r\n        margin-top: 16px;\r\n        flex: 1;\r\n\r\n        .cs-scroll {\r\n          overflow-y: auto;\r\n          @include scrollBar;\r\n        }\r\n\r\n        .el-tree {\r\n          height: 100%;\r\n\r\n          //::v-deep {\r\n          //  .el-tree-node {\r\n          //    min-width: 240px;\r\n          //    width: min-content;\r\n          //\r\n          //    .el-tree-node__children {\r\n          //      overflow: inherit;\r\n          //    }\r\n          //  }\r\n          //}\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .cs-right {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n\r\n    .cs-z-tb-wrapper {\r\n      overflow: hidden;\r\n      display: flex;\r\n      flex-direction: column;\r\n      flex: 1;\r\n      height: 0;\r\n\r\n      .tb-container {\r\n        overflow: hidden;\r\n        padding: 0 16px;\r\n        flex: 1;\r\n        height: 0;\r\n      }\r\n    }\r\n\r\n    .cs-bottom {\r\n      padding: 8px 16px 8px 16px;\r\n      position: relative;\r\n      display: flex;\r\n      flex-direction: row-reverse;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      box-sizing: border-box;\r\n\r\n      .data-info {\r\n        .info-x {\r\n          margin-right: 20px;\r\n        }\r\n      }\r\n\r\n      .pg-input {\r\n        width: 100px;\r\n        margin-right: 20px;\r\n      }\r\n\r\n      .pagination-container {\r\n        text-align: right;\r\n        margin: 0;\r\n        padding: 0;\r\n\r\n        ::v-deep .el-input--small .el-input__inner {\r\n          height: 28px;\r\n          line-height: 28px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.z-dialog {\r\n  ::v-deep {\r\n    .el-dialog__header {\r\n      background-color: #298dff;\r\n\r\n      .el-dialog__title,\r\n      .el-dialog__close {\r\n        color: #ffffff;\r\n      }\r\n    }\r\n\r\n    .el-dialog__body {\r\n      // max-height: 740px;\r\n      overflow: auto;\r\n      @include scrollBar;\r\n\r\n      &::-webkit-scrollbar {\r\n        width: 8px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.cs-from {\r\n  background-color: #ffffff;\r\n  border-radius: 4px;\r\n  margin-bottom: 16px;\r\n  padding: 16px 16px 0 16px;\r\n  display: flex;\r\n  font-size: 14px;\r\n  color: rgba(34, 40, 52, 0.65);\r\n\r\n  label {\r\n    display: inline-block;\r\n    margin-right: 20px;\r\n    white-space: nowrap;\r\n    vertical-align: top;\r\n  }\r\n\r\n  .cs-from-title {\r\n    flex: 1;\r\n  }\r\n\r\n  .mb0 {\r\n    margin-bottom: 0;\r\n\r\n    ::v-deep {\r\n      .el-form-item {\r\n        margin-bottom: 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  .cs-search {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.input-with-select {\r\n  //width: 250px;\r\n}\r\n\r\n.cs-button-box {\r\n  padding: 16px 16px 6px 16px;\r\n  box-sizing: border-box;\r\n  position: relative;\r\n  background-color: #ffffff;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  flex-wrap: wrap;\r\n\r\n  ::v-deep .el-button {\r\n    margin-left: 0 !important;\r\n    margin-right: 10px !important;\r\n    margin-bottom: 10px !important;\r\n  }\r\n\r\n  .cs-length {\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    flex-direction: row-reverse;\r\n  }\r\n}\r\n\r\n.info-box {\r\n  margin: 0 16px 16px 16px;\r\n  display: flex;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  height: 64px;\r\n  background: rgba(41, 141, 255, 0.05);\r\n\r\n  .cs-col {\r\n    display: flex;\r\n    justify-content: space-evenly;\r\n    flex-direction: column;\r\n    margin-right: 64px;\r\n  }\r\n\r\n  .info-label {\r\n    color: #999999;\r\n  }\r\n\r\n  i {\r\n    color: #00c361;\r\n    font-style: normal;\r\n    font-weight: 600;\r\n    margin-left: 10px;\r\n  }\r\n}\r\n\r\n::v-deep .el-tree-node {\r\n  min-width: 240px;\r\n  width: min-content;\r\n}\r\n\r\n::v-deep .el-tree-node > .el-tree-node__children {\r\n  overflow: inherit;\r\n}\r\n\r\n.stretch-btn {\r\n  position: absolute;\r\n  width: 20px;\r\n  height: 130px;\r\n  top: calc((100% - 130px) / 2);\r\n  right: -20px;\r\n  display: flex;\r\n  align-items: center;\r\n  background: #eff1f3;\r\n  cursor: pointer;\r\n\r\n  .center-btn {\r\n    width: 14px;\r\n    height: 100px;\r\n    border-radius: 0 9px 9px 0;\r\n    background-color: #8c95a8;\r\n\r\n    > i {\r\n      line-height: 100px;\r\n      text-align: center;\r\n      color: #fff;\r\n    }\r\n  }\r\n}\r\n\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n\r\n.fourGreen {\r\n  color: #00c361;\r\n  font-style: normal;\r\n}\r\n\r\n.fourOrange {\r\n  color: #ff9400;\r\n  font-style: normal;\r\n}\r\n\r\n.fourRed {\r\n  color: #ff0000;\r\n  font-style: normal;\r\n}\r\n\r\n.cs-blue {\r\n  color: #5ac8fa;\r\n}\r\n\r\n.orangeBg {\r\n  background: rgba(255, 148, 0, 0.1);\r\n}\r\n\r\n.redBg {\r\n  background: rgba(252, 107, 127, 0.1);\r\n}\r\n.greenBg {\r\n  background: rgba(0, 195, 97, 0.1);\r\n}\r\n\r\n.cs-tag {\r\n  margin-left: 8px;\r\n  font-size: 12px;\r\n  padding: 2px 4px;\r\n  border-radius: 1px;\r\n}\r\n\r\n.cs-tree-x {\r\n  ::v-deep {\r\n    .el-select {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n.cs-divider {\r\n  margin: 16px 0 0 0;\r\n}\r\n.isPicActive {\r\n  color: #298dff;\r\n  cursor: pointer;\r\n}\r\n</style>\r\n"]}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\component-list\\v4\\component\\PartList.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\component-list\\v4\\component\\PartList.vue", "mtime": 1757583738729}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBHZXRQYXJ0TGlzdFdpdGhDb21wb25lbnQgfSBmcm9tICdAL2FwaS9QUk8vY29tcG9uZW50Jw0KaW1wb3J0IGdldFRiSW5mbyBmcm9tICdAL21peGlucy9QUk8vZ2V0LXRhYmxlLWluZm8nDQppbXBvcnQgRHluYW1pY0RhdGFUYWJsZSBmcm9tICdAL2NvbXBvbmVudHMvRHluYW1pY0RhdGFUYWJsZS9EeW5hbWljRGF0YVRhYmxlLnZ1ZScNCmltcG9ydCBudW1lcmFsIGZyb20gJ251bWVyYWwnDQppbXBvcnQgeyBHZXRTdG9wTGlzdCB9IGZyb20gJ0AvYXBpL1BSTy9wcm9kdWN0aW9uLXRhc2snDQppbXBvcnQgeyBHZXRCT01JbmZvIH0gZnJvbSAnQC92aWV3cy9QUk8vYm9tLXNldHRpbmcvdXRpbHMnDQpleHBvcnQgZGVmYXVsdCB7DQogIGNvbXBvbmVudHM6IHsNCiAgICBEeW5hbWljRGF0YVRhYmxlDQogIH0sDQogIG1peGluczogW2dldFRiSW5mb10sDQogIHByb3BzOiB7DQogICAgcHJvamVjdElkOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnJw0KICAgIH0sDQogICAgc3lzUHJvamVjdElkOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnJw0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgdGJMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHRiQ29uZmlnOiB7DQogICAgICAgIElzX1BhZ2U6IGZhbHNlLA0KICAgICAgICBIZWlnaHQ6IDYwMA0KICAgICAgfSwNCiAgICAgIHF1ZXJ5SW5mbzogew0KICAgICAgICBQYWdlOiAxLA0KICAgICAgICBQYWdlU2l6ZTogMTANCiAgICAgIH0sDQogICAgICBjb2x1bW5zOiBbXSwNCiAgICAgIHN0ZWVsVG90YWxOdW06IDAsDQogICAgICBzdGVlbFRvdGFsV2VpZ2h0OiAwLA0KICAgICAgdG90YWw6IDAsDQogICAgICB0YkRhdGE6IFtdLA0KICAgICAgcm93SWQ6IDAsDQogICAgICBTdGVlbE5hbWU6ICcnLA0KICAgICAgZmlyc3ROYW1lTGlzdDogW10NCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQoNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGFzeW5jIGluaXQocm93RGF0YSkgew0KICAgICAgY29uc3QgeyBjb21OYW1lLCBsaXN0IH0gPSBhd2FpdCBHZXRCT01JbmZvKCkNCiAgICAgIGNvbnN0IGFsbE5hbWUgPSBsaXN0Lm1hcChpdGVtID0+IGl0ZW0uRGlzcGxheV9OYW1lKS5qb2luKCcvJykNCiAgICAgIHRoaXMuZmlyc3ROYW1lTGlzdCA9IGxpc3QNCiAgICAgIGNvbnNvbGUubG9nKCd0aGlzLmZpcnN0TmFtZUxpc3QnLCB0aGlzLmZpcnN0TmFtZUxpc3QpDQogICAgICB0aGlzLmNvbHVtbnMgPSBbDQogICAgICAgIHsgQ29kZTogJ0NvZGUnLCBEaXNwbGF5X05hbWU6IGAke2FsbE5hbWV95ZCN56ewYCwgSXNfRnJvemVuOiB0cnVlLCBGcm96ZW5fVG86ICdsZWZ0JywgTWluX1dpZHRoOiAxODAsIElzX0Rpc3BsYXk6IHRydWUsIFNvcnQ6IDEgfSwNCiAgICAgICAgeyBDb2RlOiAnU3BlYycsIERpc3BsYXlfTmFtZTogJ+inhOagvCcsIE1pbl9XaWR0aDogMTYwLCBJc19EaXNwbGF5OiB0cnVlLCBTb3J0OiAyIH0sDQogICAgICAgIHsgQ29kZTogJ0xlbmd0aCcsIERpc3BsYXlfTmFtZTogJ+mVv+W6picsIE1pbl9XaWR0aDogMTYwLCBJc19EaXNwbGF5OiB0cnVlLCBTb3J0OiAzIH0sDQogICAgICAgIHsgQ29kZTogJ0NvbXBvbmVudF9Db2RlJywgRGlzcGxheV9OYW1lOiBg5omA5bGeJHtjb21OYW1lfWAsIE1pbl9XaWR0aDogMTQwLCBJc19EaXNwbGF5OiB0cnVlLCBTb3J0OiA0IH0sDQogICAgICAgIHsgQ29kZTogJ051bScsIERpc3BsYXlfTmFtZTogJ+mcgOaxguaVsOmHjycsIE1pbl9XaWR0aDogMTIwLCBJc19EaXNwbGF5OiB0cnVlLCBTb3J0OiA1IH0sDQogICAgICAgIHsgQ29kZTogJ1NjaGVkdWxpbmdOdW0nLCBEaXNwbGF5X05hbWU6ICfmjpLkuqfmlbDph48nLCBNaW5fV2lkdGg6IDEyMCwgSXNfRGlzcGxheTogdHJ1ZSwgU29ydDogNiB9LA0KICAgICAgICB7IENvZGU6ICdQcm9kdWN0aW5nX0NvdW50JywgRGlzcGxheV9OYW1lOiAn55Sf5Lqn5Lit5pWw6YePJywgTWluX1dpZHRoOiAxNDAsIElzX0Rpc3BsYXk6IHRydWUsIFNvcnQ6IDYgfSwNCiAgICAgICAgeyBDb2RlOiAnV2VpZ2h0JywgRGlzcGxheV9OYW1lOiAn5Y2V6YeN77yIa2fvvIknLCBNaW5fV2lkdGg6IDEyMCwgSXNfRGlzcGxheTogdHJ1ZSwgU29ydDogNyB9LA0KICAgICAgICB7IENvZGU6ICdUb3RhbF9XZWlnaHQnLCBEaXNwbGF5X05hbWU6ICfmgLvph43vvIhrZ++8iScsIE1pbl9XaWR0aDogMTIwLCBJc19EaXNwbGF5OiB0cnVlLCBTb3J0OiA4IH0sDQogICAgICAgIHsgQ29kZTogJ0lzX01haW4nLCBEaXNwbGF5X05hbWU6ICfmmK/lkKbkuLvpm7bku7YnLCBNaW5fV2lkdGg6IDE0MCwgSXNfRGlzcGxheTogdHJ1ZSwgU29ydDogMTAgfSwNCiAgICAgICAgeyBDb2RlOiAnUmVtYXJrJywgRGlzcGxheV9OYW1lOiAn5aSH5rOoJywgTWluX1dpZHRoOiAxNjAsIElzX0Rpc3BsYXk6IHRydWUsIFNvcnQ6IDExIH0sDQogICAgICAgIHsgQ29kZTogJ1NIJywgRGlzcGxheV9OYW1lOiAn5rex5YyW6LWE5paZJywgSXNfRnJvemVuOiB0cnVlLCBGcm96ZW5fVG86ICdyaWdodCcsIE1pbl9XaWR0aDogMTIwLCBJc19EaXNwbGF5OiB0cnVlLCBTb3J0OiAxMiB9DQogICAgICBdDQogICAgICBjb25zb2xlLmxvZygncm93RGF0YScsIEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkocm93RGF0YSkpKQ0KICAgICAgdGhpcy5yb3dJZCA9IHJvd0RhdGEuSWQNCiAgICAgIHRoaXMuU3RlZWxOYW1lID0gcm93RGF0YS5TdGVlbE5hbWUNCiAgICAgIHRoaXMudGJMb2FkaW5nID0gdHJ1ZQ0KDQogICAgICBHZXRQYXJ0TGlzdFdpdGhDb21wb25lbnQoeyBpZDogcm93RGF0YS5JZCB9KS50aGVuKGFzeW5jIHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgdGhpcy50YkRhdGEgPSByZXMuRGF0YQ0KICAgICAgICAgIGxldCBfbnVtID0gMA0KICAgICAgICAgIGxldCBfd2VpZ2h0ID0gMA0KICAgICAgICAgIHJlcy5EYXRhLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgICBpZiAoaXRlbS5QYXJ0X0dyYWRlICE9PSAxKSB7DQogICAgICAgICAgICAgIF9udW0gKz0gaXRlbS5OdW0NCiAgICAgICAgICAgICAgX3dlaWdodCArPSBpdGVtLlRvdGFsX1dlaWdodA0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgICAgdGhpcy5zdGVlbFRvdGFsTnVtID0gbnVtZXJhbChfbnVtKS5mb3JtYXQoJzAuWzAwXScpDQogICAgICAgICAgdGhpcy5zdGVlbFRvdGFsV2VpZ2h0ID0gbnVtZXJhbChfd2VpZ2h0KS5mb3JtYXQoJzAuWzAwXScpDQogICAgICAgICAgYXdhaXQgdGhpcy5nZXRTdG9wTGlzdCgpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXMuTWVzc2FnZSwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICAgIHRoaXMudGJMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICBhc3luYyBnZXRTdG9wTGlzdCgpIHsNCiAgICAgIGNvbnN0IHN1Ym1pdE9iaiA9IHRoaXMudGJEYXRhLm1hcChpdGVtID0+IHsNCiAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICBJZDogaXRlbS5QYXJ0X0FnZ3JlZ2F0ZV9JZCwNCiAgICAgICAgICBUeXBlOiBpdGVtLlBhcnRfR3JhZGUgPT09IDAgPyAxIDogMw0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgYXdhaXQgR2V0U3RvcExpc3Qoc3VibWl0T2JqKS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuSXNTdWNjZWVkKSB7DQogICAgICAgICAgY29uc3Qgc3RvcE1hcCA9IHt9DQogICAgICAgICAgcmVzLkRhdGEuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICAgIHN0b3BNYXBbaXRlbS5JZF0gPSBpdGVtLklzX1N0b3AgIT09IG51bGwNCiAgICAgICAgICB9KQ0KICAgICAgICAgIHRoaXMudGJEYXRhLmZvckVhY2gocm93ID0+IHsNCiAgICAgICAgICAgIGlmIChzdG9wTWFwW3Jvdy5QYXJ0X0FnZ3JlZ2F0ZV9JZF0pIHsNCiAgICAgICAgICAgICAgdGhpcy4kc2V0KHJvdywgJ3N0b3BGbGFnJywgc3RvcE1hcFtyb3cuUGFydF9BZ2dyZWdhdGVfSWRdKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVWaWV3KHJvdykgew0KICAgICAgY29uc29sZS5sb2coJ3JvdycsIHJvdykNCiAgICAgIHRoaXMuJGVtaXQoJ2NoZWNrU3RlZWxNZWFucycsIHJvdykNCiAgICB9LA0KICAgIGdldFVuaXRQYXJ0R3JhZGVMaXN0KHJvdykgew0KICAgICAgY29uc3QgaXRlbSA9IHRoaXMuZmlyc3ROYW1lTGlzdC5maW5kKGl0ZW0gPT4gK2l0ZW0uQ29kZSA9PT0gcm93LlBhcnRfR3JhZGUpDQogICAgICByZXR1cm4gaXRlbS5EaXNwbGF5X05hbWVbMF0NCiAgICB9LA0KICAgIGdldFBhcnRHcmFkZU5hbWUoKSB7DQogICAgICBjb25zdCBpdGVtID0gdGhpcy5maXJzdE5hbWVMaXN0LmZpbmQoaXRlbSA9PiAraXRlbS5Db2RlID09PSAwKQ0KICAgICAgcmV0dXJuIGl0ZW0uRGlzcGxheV9OYW1lWzBdDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["PartList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8GA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PartList.vue", "sourceRoot": "src/views/PRO/component-list/v4/component", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"top-info\">\r\n      <div class=\"top-info-title\">{{ SteelName }}</div>\r\n      <div>需求零件总数：<span>{{ steelTotalNum }}</span> 件 需求零件总重：<span>{{ steelTotalWeight }}</span> kg</div>\r\n    </div>\r\n    <div class=\"tb-container\">\r\n      <vxe-table\r\n        v-loading=\"tbLoading\"\r\n        :empty-render=\"{name: 'NotData'}\"\r\n        show-header-overflow\r\n        element-loading-spinner=\"el-icon-loading\"\r\n        element-loading-text=\"拼命加载中\"\r\n        empty-text=\"暂无数据\"\r\n        class=\"cs-vxe-table\"\r\n        height=\"500\"\r\n        align=\"left\"\r\n        stripe\r\n        :data=\"tbData\"\r\n        resizable\r\n        :tree-config=\"{transform: true, rowField: 'Id', parentField: 'ParentId'}\"\r\n        :tooltip-config=\"{ enterable: true}\"\r\n      >\r\n        <template v-for=\"(item,idx) in columns\">\r\n          <vxe-column\r\n            :key=\"item.Code\"\r\n            :tree-node=\"idx===0\"\r\n            :min-width=\"item.Min_Width\"\r\n            :fixed=\"item.Is_Frozen?item.Frozen_Dirction:''\"\r\n            show-overflow=\"tooltip\"\r\n            sortable\r\n            :align=\"item.Align\"\r\n            :field=\"item.Code\"\r\n            :title=\"item.Display_Name\"\r\n          >\r\n            <template #default=\"{ row }\">\r\n              <template v-if=\"item.Code === 'Code'\">\r\n                <el-tag v-if=\"row.Is_Change\" style=\"margin-right: 8px;\" type=\"danger\">变</el-tag>\r\n                <el-tag v-if=\"row.stopFlag\" style=\"margin-right: 8px;\" type=\"danger\">停</el-tag>\r\n                <span>{{ row.Code }}</span>\r\n                <span style=\"margin-left: 5px;\">\r\n                  <template v-for=\"firstName in getUnitPartGradeList(row)\">\r\n                    <el-tag v-if=\"row.Part_Grade>0\" :key=\"firstName\">{{ firstName }}</el-tag>\r\n                  </template>\r\n                  <el-tag v-if=\"row.Part_Grade===0\" type=\"success\">{{ getPartGradeName() }}</el-tag>\r\n                </span>\r\n              </template>\r\n              <span v-else-if=\"item.Code === 'SchedulingNum'\">\r\n                {{ row.SchedulingNum ? row.SchedulingNum : '-' }}\r\n              </span>\r\n              <span v-else-if=\"item.Code === 'Producting_Count'\">\r\n                {{ row.Producting_Count ? row.Producting_Count : '-' }}\r\n              </span>\r\n              <span v-else-if=\"item.Code === 'Is_Main'\">\r\n                <span v-if=\"row.Part_Grade===1\">-</span>\r\n                <span v-else>\r\n                  <el-tag\r\n                    :type=\"row.Is_Main ? 'success' : 'danger'\"\r\n                  >{{ row.Is_Main ? \"是\" : \"否\" }}\r\n                  </el-tag>\r\n                </span>\r\n              </span>\r\n              <span v-else-if=\"item.Code === 'SH'\">\r\n                <div v-if=\"row.SH==0\">/</div>\r\n                <div v-else>\r\n                  <el-button type=\"text\" @click=\"handleView(row)\">查看</el-button>\r\n                </div>\r\n              </span>\r\n              <span v-else>\r\n                {{ row[item.Code] | displayValue }}\r\n              </span>\r\n            </template>\r\n          </vxe-column>\r\n        </template>\r\n      </vxe-table>\r\n    </div>\r\n    <!--    <div v-loading=\"tbLoading\" class=\"tb-container\">\r\n      <dynamic-data-table\r\n        ref=\"dyTable\"\r\n        :columns=\"columns\"\r\n        :config=\"tbConfig\"\r\n        :data=\"tbData\"\r\n        :page=\"queryInfo.Page\"\r\n        :total=\"total\"\r\n        border\r\n        stripe\r\n        class=\"cs-plm-dy-table\"\r\n      >\r\n        <template slot=\"SchedulingNum\" slot-scope=\"{ row }\">\r\n          <div>\r\n            {{ row.SchedulingNum ? row.SchedulingNum : '-' }}\r\n          </div>\r\n        </template>\r\n        <template slot=\"Is_Main\" slot-scope=\"{ row }\">\r\n          <div>\r\n            {{ row.Is_Main==true ? '是' : '否' }}\r\n          </div>\r\n        </template>\r\n        <template slot=\"SH\" slot-scope=\"{ row }\">\r\n          <div v-if=\"row.SH==0\">/</div>\r\n          <div v-else>\r\n            <el-button type=\"text\" @click=\"handleView(row)\">查看</el-button>\r\n          </div>\r\n        </template>\r\n      </dynamic-data-table>\r\n    </div>-->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetPartListWithComponent } from '@/api/PRO/component'\r\nimport getTbInfo from '@/mixins/PRO/get-table-info'\r\nimport DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable.vue'\r\nimport numeral from 'numeral'\r\nimport { GetStopList } from '@/api/PRO/production-task'\r\nimport { GetBOMInfo } from '@/views/PRO/bom-setting/utils'\r\nexport default {\r\n  components: {\r\n    DynamicDataTable\r\n  },\r\n  mixins: [getTbInfo],\r\n  props: {\r\n    projectId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    sysProjectId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      tbLoading: false,\r\n      tbConfig: {\r\n        Is_Page: false,\r\n        Height: 600\r\n      },\r\n      queryInfo: {\r\n        Page: 1,\r\n        PageSize: 10\r\n      },\r\n      columns: [],\r\n      steelTotalNum: 0,\r\n      steelTotalWeight: 0,\r\n      total: 0,\r\n      tbData: [],\r\n      rowId: 0,\r\n      SteelName: '',\r\n      firstNameList: []\r\n    }\r\n  },\r\n  mounted() {\r\n\r\n  },\r\n  methods: {\r\n    async init(rowData) {\r\n      const { comName, list } = await GetBOMInfo()\r\n      const allName = list.map(item => item.Display_Name).join('/')\r\n      this.firstNameList = list\r\n      console.log('this.firstNameList', this.firstNameList)\r\n      this.columns = [\r\n        { Code: 'Code', Display_Name: `${allName}名称`, Is_Frozen: true, Frozen_To: 'left', Min_Width: 180, Is_Display: true, Sort: 1 },\r\n        { Code: 'Spec', Display_Name: '规格', Min_Width: 160, Is_Display: true, Sort: 2 },\r\n        { Code: 'Length', Display_Name: '长度', Min_Width: 160, Is_Display: true, Sort: 3 },\r\n        { Code: 'Component_Code', Display_Name: `所属${comName}`, Min_Width: 140, Is_Display: true, Sort: 4 },\r\n        { Code: 'Num', Display_Name: '需求数量', Min_Width: 120, Is_Display: true, Sort: 5 },\r\n        { Code: 'SchedulingNum', Display_Name: '排产数量', Min_Width: 120, Is_Display: true, Sort: 6 },\r\n        { Code: 'Producting_Count', Display_Name: '生产中数量', Min_Width: 140, Is_Display: true, Sort: 6 },\r\n        { Code: 'Weight', Display_Name: '单重（kg）', Min_Width: 120, Is_Display: true, Sort: 7 },\r\n        { Code: 'Total_Weight', Display_Name: '总重（kg）', Min_Width: 120, Is_Display: true, Sort: 8 },\r\n        { Code: 'Is_Main', Display_Name: '是否主零件', Min_Width: 140, Is_Display: true, Sort: 10 },\r\n        { Code: 'Remark', Display_Name: '备注', Min_Width: 160, Is_Display: true, Sort: 11 },\r\n        { Code: 'SH', Display_Name: '深化资料', Is_Frozen: true, Frozen_To: 'right', Min_Width: 120, Is_Display: true, Sort: 12 }\r\n      ]\r\n      console.log('rowData', JSON.parse(JSON.stringify(rowData)))\r\n      this.rowId = rowData.Id\r\n      this.SteelName = rowData.SteelName\r\n      this.tbLoading = true\r\n\r\n      GetPartListWithComponent({ id: rowData.Id }).then(async res => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data\r\n          let _num = 0\r\n          let _weight = 0\r\n          res.Data.forEach(item => {\r\n            if (item.Part_Grade !== 1) {\r\n              _num += item.Num\r\n              _weight += item.Total_Weight\r\n            }\r\n          })\r\n          this.steelTotalNum = numeral(_num).format('0.[00]')\r\n          this.steelTotalWeight = numeral(_weight).format('0.[00]')\r\n          await this.getStopList()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n        this.tbLoading = false\r\n      })\r\n    },\r\n    async getStopList() {\r\n      const submitObj = this.tbData.map(item => {\r\n        return {\r\n          Id: item.Part_Aggregate_Id,\r\n          Type: item.Part_Grade === 0 ? 1 : 3\r\n        }\r\n      })\r\n      await GetStopList(submitObj).then(res => {\r\n        if (res.IsSucceed) {\r\n          const stopMap = {}\r\n          res.Data.forEach(item => {\r\n            stopMap[item.Id] = item.Is_Stop !== null\r\n          })\r\n          this.tbData.forEach(row => {\r\n            if (stopMap[row.Part_Aggregate_Id]) {\r\n              this.$set(row, 'stopFlag', stopMap[row.Part_Aggregate_Id])\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleView(row) {\r\n      console.log('row', row)\r\n      this.$emit('checkSteelMeans', row)\r\n    },\r\n    getUnitPartGradeList(row) {\r\n      const item = this.firstNameList.find(item => +item.Code === row.Part_Grade)\r\n      return item.Display_Name[0]\r\n    },\r\n    getPartGradeName() {\r\n      const item = this.firstNameList.find(item => +item.Code === 0)\r\n      return item.Display_Name[0]\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.content{\r\n  display: flex;\r\n  flex-direction: column;\r\n  .top-info{\r\n    font-size: 14px;\r\n    span {\r\n      color: #00C361\r\n    }\r\n    .top-info-title{\r\n      margin-bottom: 8px;\r\n      font-size: 24px;\r\n      font-weight: bold;\r\n    }\r\n  }\r\n  .tb-container{\r\n    margin-top: 20px;\r\n    flex: 1;\r\n  }\r\n}\r\n</style>\r\n"]}]}
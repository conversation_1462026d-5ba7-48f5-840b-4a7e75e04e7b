{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\utils\\tempRouterMap.js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\utils\\tempRouterMap.js", "mtime": 1757572678742}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "handleAddRouterPage", "otherAppTempRouter", "thisAppTempRouter", "path", "hidden", "component", "Promise", "resolve", "then", "_interopRequireWildcard", "require", "meta", "title", "transferRoutesWithParent", "map", "item", "_objectSpread", "parentName", "parentPath", "getRoutesWithParent", "platform"], "sources": ["D:/project/platform_framework_master/platform_framework/Platform/Frontend/SubAppProduce/src/utils/tempRouterMap.js"], "sourcesContent": ["// src/utils/tempRouterMap.js\r\nimport { name } from '../../package.json'\r\nimport { handleAddRouterPage } from '@/utils'\r\n\r\nconst otherAppTempRouter = [\r\n  // {\r\n  //   path: 'unit-template-setting',\r\n  //   hidden: true,\r\n  //   name: 'SYSUnitPartTemp',\r\n  //   platform: 'produce',\r\n  //   meta: { title: '专用模板配置' }\r\n  // }\r\n]\r\nconst thisAppTempRouter = [\r\n  // {\r\n  //   path: 'unit-template-setting',\r\n  //   hidden: true,\r\n  //   component: () => import('@/views/sys/professional-category/unitPartTemp.vue'),\r\n  //   name: 'SYSUnitPartTemp',\r\n  //   meta: { title: '专用模板配置' }\r\n  // },\r\n  // {\r\n  //   path: 'template-setting',\r\n  //   hidden: true,\r\n  //   component: () => import('@/views/sys/professional-category/templateSetting'),\r\n  //   name: 'TemplateSetting',\r\n  //   meta: { title: '专用模板配置' }\r\n  // },\r\n  {\r\n    path: 'pro-contact-list-view',\r\n    hidden: true,\r\n    component: () => import('@/views/PRO/change-management/contact-list/add.vue'),\r\n    name: 'PROEngineeringChangeOrderView',\r\n    meta: { title: '工程联系单' }\r\n  }\r\n]\r\n\r\nexport function transferRoutesWithParent() {\r\n  return otherAppTempRouter.map(item => ({\r\n    ...item,\r\n    parentName: null,\r\n    parentPath: null\r\n  }))\r\n}\r\n\r\nexport function getRoutesWithParent() {\r\n  handleAddRouterPage(thisAppTempRouter, '', '')\r\n  return thisAppTempRouter.map(item => ({\r\n    ...item,\r\n    parentName: null,\r\n    parentPath: null,\r\n    platform: name\r\n  }))\r\n}\r\n\r\n"], "mappings": ";;;;;;AAAA;AACA,SAASA,IAAI,QAAQ,oBAAoB;AACzC,SAASC,mBAAmB,QAAQ,SAAS;AAE7C,IAAMC,kBAAkB,GAAG;EACzB;EACA;EACA;EACA;EACA;EACA;EACA;AAAA,CACD;AACD,IAAMC,iBAAiB,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,IAAI,EAAE,uBAAuB;EAC7BC,MAAM,EAAE,IAAI;EACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,OAAAC,uBAAA,CAAAC,OAAA,CAAe,oDAAoD;IAAA;EAAA,CAAC;EAC7EX,IAAI,EAAE,+BAA+B;EACrCY,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAQ;AACzB,CAAC,CACF;AAED,OAAO,SAASC,wBAAwBA,CAAA,EAAG;EACzC,OAAOZ,kBAAkB,CAACa,GAAG,CAAC,UAAAC,IAAI;IAAA,OAAAC,aAAA,CAAAA,aAAA,KAC7BD,IAAI;MACPE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;IAAI;EAAA,CAChB,CAAC;AACL;AAEA,OAAO,SAASC,mBAAmBA,CAAA,EAAG;EACpCnB,mBAAmB,CAACE,iBAAiB,EAAE,EAAE,EAAE,EAAE,CAAC;EAC9C,OAAOA,iBAAiB,CAACY,GAAG,CAAC,UAAAC,IAAI;IAAA,OAAAC,aAAA,CAAAA,aAAA,KAC5BD,IAAI;MACPE,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,IAAI;MAChBE,QAAQ,EAAErB;IAAI;EAAA,CACd,CAAC;AACL", "ignoreList": []}]}
<template>
  <el-select
    v-model="selectedValue"
    placeholder="请选择"
    style="width: 100%"
    clearable
    filterable
    :multiple="multiple"
    @change="handleChange"
    :collapse-tags="collapseTags"
  >
    <el-option
      v-for="item in list"
      :key="item.Id"
      :label="item.Display_Name"
      :value="item.Id"
    />
  </el-select>
</template>
<script>
/**
 * 选择当前工厂下的人员
 */
import { deepClone } from '@/utils'
import { GetFactoryPeoplelist } from '@/api/PRO/salary'

export default {
  name: 'SelectUser',
  props: {
    value: {
      type: [Array, Number, String],
      default: ''
    },
    multiple: {
      type: Boolean,
      default: false
    },
    collapseTags: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      list: [],
      selectedValue: this.value
    }
  },
  watch: {
    value: {
      handler(val) {
        this.selectedValue = Array.isArray(val) ? deepClone(val) : val
      },
      immediate: true
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleChange() {
      this.$emit('input', this.selectedValue)
      this.$emit('change', this.selectedValue)
    },
    async getList() {
      GetFactoryPeoplelist().then(res => {
        this.list = res.Data
      })
    }
  }
}
</script>

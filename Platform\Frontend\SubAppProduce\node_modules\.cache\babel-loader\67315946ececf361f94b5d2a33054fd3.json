{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\api\\PRO\\bom-level.js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\api\\PRO\\bom-level.js", "mtime": 1757468111870}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOiOt+WPlkJPTeWxgue6p+WIl+ihqApleHBvcnQgZnVuY3Rpb24gR2V0Qm9tTGV2ZWxMaXN0KGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvUFJPL0JvbUxldmVsL0dldEJvbUxldmVsTGlzdCcsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+d5a2YQk9N5bGC57qn6YWN572uCmV4cG9ydCBmdW5jdGlvbiBTYXZlQm9tTGV2ZWwoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9QUk8vQm9tTGV2ZWwvU2F2ZUJvbUxldmVsJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9"}, {"version": 3, "names": ["request", "GetBomLevelList", "data", "url", "method", "SaveBomLevel"], "sources": ["D:/project/platform_framework_master/platform_framework/Platform/Frontend/SubAppProduce/src/api/PRO/bom-level.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取BOM层级列表\r\nexport function GetBomLevelList(data) {\r\n  return request({\r\n    url: '/PRO/BomLevel/GetBomLevelList',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 保存BOM层级配置\r\nexport function SaveBomLevel(data) {\r\n  return request({\r\n    url: '/PRO/BomLevel/SaveBomLevel',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,eAAeA,CAACC,IAAI,EAAE;EACpC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,YAAYA,CAACH,IAAI,EAAE;EACjC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ", "ignoreList": []}]}
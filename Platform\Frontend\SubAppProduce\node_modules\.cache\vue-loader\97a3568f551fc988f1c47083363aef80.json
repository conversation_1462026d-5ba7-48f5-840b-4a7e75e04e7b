{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\components\\processHead.vue?vue&type=style&index=0&id=6a9422d2&scope=true&lang=scss", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\components\\processHead.vue", "mtime": 1757572678807}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoucHJvY2Vzc2hlYWR7DQogICAgd2lkdGg6MTAwJTsNCiAgICBoZWlnaHQ6NjRweDsNCiAgICBiYWNrZ3JvdW5kOiNmZmY7DQogICAgbGluZS1oZWlnaHQ6MjRweDsNCiAgICBkaXNwbGF5OmZsZXg7DQogICAganVzdGlmeS1jb250ZW50OnNwYWNlLWJldHdlZW47DQogICAgYm94LXNoYWRvdzogMHB4IDFweCAzcHggMXB4IHJnYmEoMjAsMzUsNzgsMC4wOCk7DQogICAgbWFyZ2luLWJvdHRvbToxNXB4Ow0KICAgIC8vIGJvcmRlcjoxcHggc29saWQgIzAwMDsNCiAgICBwYWRkaW5nOjIwcHggMTZweDsNCiAgICAudGl0bGV7DQogICAgICAgIGZvbnQtc2l6ZTogMThweDsNCiAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICAgIGNvbG9yOiByZ2JhKDM0LDQwLDUyLDAuODUpOw0KICAgIH0NCiAgICAuc3BhbnsNCiAgICAgICAgYmFja2dyb3VuZC1pbWFnZTogbGluZWFyLWdyYWRpZW50KDE4MGRlZywgIzcxQjNGRiAwJSwgIzI5OERGRiAxMDAlKTsNCiAgICAgICAgd2lkdGg6NHB4Ow0KICAgICAgICBoZWlnaHQ6MTRweDsNCiAgICAgICAgZGlzcGxheTppbmxpbmUtYmxvY2s7DQogICAgICAgIGJvcmRlci1yYWRpdXM6M3B4Ow0KICAgICAgICBtYXJnaW4tcmlnaHQ6OHB4Ow0KICAgIH0NCn0NCg=="}, {"version": 3, "sources": ["processHead.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "processHead.vue", "sourceRoot": "src/views/PRO/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <div v-if=\"noStyle\">\r\n      <el-button type=\"primary\" @click=\"opendialog('approve')\">{{ approve }}</el-button>\r\n      <el-button v-if=\"showReject\" type=\"danger\" @click=\"opendialog('reject')\">{{ reject }}</el-button>\r\n      <el-button v-if=\"showrefuse\" type=\"danger\" @click=\"opendialog('refuse')\">{{ refuse }}</el-button>\r\n    </div>\r\n    <div v-else class=\"processhead\">\r\n      <div class=\"title\"><span class=\"span\" />{{ title }}</div>\r\n      <div>\r\n        <el-button type=\"primary\" @click=\"opendialog('approve')\">{{ approve }}</el-button>\r\n        <el-button v-if=\"showReject\" type=\"danger\" @click=\"opendialog('reject')\">{{ reject }}</el-button>\r\n        <el-button v-if=\"showrefuse\" type=\"danger\" @click=\"opendialog('refuse')\">{{ refuse }}</el-button>\r\n      </div>\r\n    </div>\r\n    <bimdialog\r\n      dialog-title=\"审批意见\"\r\n      dialog-width=\"660px\"\r\n      :visible.sync=\"showaudit\"\r\n      :hidebtn=\"false\"\r\n      append-to-body\r\n      @submitbtn=\"audit\"\r\n      @cancelbtn=\"closeaudit\"\r\n      @handleClose=\"closeaudit\"\r\n    >\r\n      <el-form ref=\"form\">\r\n        <el-form-item label=\"审批意见:\" :required=\"required\" label-width=\"80\">\r\n          <el-input v-model=\"VerificationOpinion\" type=\"textarea\" />\r\n        </el-form-item>\r\n      </el-form>\r\n    </bimdialog>\r\n  </div>\r\n</template>\r\n<script>\r\nimport bimdialog from '@/views/plm/components/dialog'\r\n// import { Verification } from '@/api/plm/processmanagement'\r\nimport { Verification } from '@/api/sys/flow'\r\nimport { closeTagView } from '@/utils'\r\nimport { baseUrl } from '@/utils/baseurl'\r\nexport default {\r\n  components: {\r\n    bimdialog\r\n  },\r\n  props: {\r\n    title: {\r\n      type: String,\r\n      default: '流程计划审批'\r\n    },\r\n    // 流程Id\r\n    processId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    approve: {\r\n      type: String,\r\n      default: '通 过'\r\n    },\r\n    reject: {\r\n      type: String,\r\n      default: '驳 回'\r\n    },\r\n    nodeRejectType: {\r\n      type: String,\r\n      default: '1'\r\n    },\r\n    nodeRejectStep: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    refuse: {\r\n      type: String,\r\n      default: '不通过'\r\n    },\r\n    showrefuse: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    beforeapprove: {\r\n      type: Function,\r\n      default: () => { return new Promise((resolve, reject) => { resolve() }) }\r\n    },\r\n    // 审批意见必填\r\n    required: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    noStyle: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    showReject: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    webId: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    businessId: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      showaudit: false,\r\n      type: '',\r\n      VerificationOpinion: ''\r\n    }\r\n  },\r\n  methods: {\r\n    opendialog(type) {\r\n      this.showaudit = true\r\n      this.type = type\r\n    },\r\n    closeaudit() {\r\n      this.VerificationOpinion = ''\r\n      this.showaudit = false\r\n    },\r\n    audit() {\r\n      if (this.type === 'approve') {\r\n        this.approveIt()\r\n      } else if (this.type === 'reject') {\r\n        this.rejectIt()\r\n      } else if (this.type === 'refuse') {\r\n        this.refuseIt()\r\n      }\r\n    },\r\n    // flowInstanceId:流程Id\r\n    // VerificationFinally:1:同意；2：不同意；3：驳回\r\n    // VerificationOpinion:审核意见\r\n    // nodeRejectType:驳回至0:前一步/1:第一步/2：指定节点\r\n    // nodeRejectStep:当驳回类型为2时，驳回结点code\r\n    // 同意\r\n    async approveIt() {\r\n      if (this.required && !this.VerificationOpinion && this.VerificationOpinion !== 0) {\r\n        this.$message.warning('请填写审核意见')\r\n        return\r\n      }\r\n      this.beforeapprove().then(() => {\r\n        var param = {\r\n          flowInstanceId: this.processId,\r\n          VerificationFinally: 1,\r\n          VerificationOpinion: this.VerificationOpinion,\r\n          NodeRejectType: this.nodeRejectType,\r\n          NodeRejectStep: this.nodeRejectStep,\r\n          WebId: this.webId,\r\n          BusinessDateld: this.businessId,\r\n          FormDataJson: '',\r\n          PlateForm_Url: baseUrl()\r\n        }\r\n        Verification(param).then(res => {\r\n          if (res.IsSucceed) {\r\n            this.$message({\r\n              message: '提交成功',\r\n              type: 'success'\r\n            })\r\n            this.$emit('afterapproval', 'approve')\r\n            this.showaudit = false\r\n            // this.cancel()\r\n          } else {\r\n            this.$message({\r\n              message: res.Message,\r\n              type: 'error'\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    // 驳回\r\n    async rejectIt() {\r\n      if (this.required && !this.VerificationOpinion && this.VerificationOpinion !== 0) {\r\n        this.$message.warning('请填写审核意见')\r\n        return\r\n      }\r\n      var param = {\r\n        flowInstanceId: this.processId,\r\n        VerificationFinally: 2,\r\n        VerificationOpinion: this.VerificationOpinion,\r\n        NodeRejectType: this.nodeRejectType,\r\n        NodeRejectStep: this.nodeRejectStep,\r\n        WebId: this.webId,\r\n        BusinessDateld: this.businessId,\r\n        FormDataJson: '',\r\n        PlateForm_Url: baseUrl()\r\n      }\r\n      Verification(param).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '提交成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('afterapproval', 'reject')\r\n          this.showaudit = false\r\n          // this.cancel()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 不同意\r\n    async refuseIt() {\r\n      if (this.required && !this.VerificationOpinion && this.VerificationOpinion !== 0) {\r\n        this.$message.warning('请填写审核意见')\r\n        return\r\n      }\r\n      var param = {\r\n        flowInstanceId: this.processId,\r\n        VerificationFinally: 2,\r\n        VerificationOpinion: this.VerificationOpinion,\r\n        NodeRejectType: this.nodeRejectType,\r\n        NodeRejectStep: this.nodeRejectStep\r\n      }\r\n      Verification(param).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '提交成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('afterapproval', 'refuse')\r\n          this.showaudit = false\r\n          // this.cancel()\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 关闭当前页\r\n    cancel() {\r\n      closeTagView(this.$store, this.$route)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scope lang=\"scss\">\r\n.processhead{\r\n    width:100%;\r\n    height:64px;\r\n    background:#fff;\r\n    line-height:24px;\r\n    display:flex;\r\n    justify-content:space-between;\r\n    box-shadow: 0px 1px 3px 1px rgba(20,35,78,0.08);\r\n    margin-bottom:15px;\r\n    // border:1px solid #000;\r\n    padding:20px 16px;\r\n    .title{\r\n        font-size: 18px;\r\n        font-weight: bold;\r\n        color: rgba(34,40,52,0.85);\r\n    }\r\n    .span{\r\n        background-image: linear-gradient(180deg, #71B3FF 0%, #298DFF 100%);\r\n        width:4px;\r\n        height:14px;\r\n        display:inline-block;\r\n        border-radius:3px;\r\n        margin-right:8px;\r\n    }\r\n}\r\n</style>\r\n"]}]}
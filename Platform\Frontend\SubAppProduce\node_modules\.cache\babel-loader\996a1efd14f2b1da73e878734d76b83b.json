{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\CheckType.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\factoryQuality\\checkoutGroup\\components\\CheckType.vue", "mtime": 1757468112719}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["GetCheckTypeList", "DeleteCheckType", "props", "checkType", "type", "Object", "default", "data", "tbLoading", "tbData", "watch", "handler", "newName", "getCheckTypeList", "deep", "mounted", "methods", "_this", "check_object_id", "Id", "Bom_Level", "Code", "then", "res", "IsSucceed", "Data", "console", "log", "$message", "message", "Message", "removeEvent", "row", "_this2", "$confirm", "confirmButtonText", "cancelButtonText", "id", "catch", "editEvent", "$emit"], "sources": ["src/views/PRO/factoryQuality/checkoutGroup/components/CheckType.vue"], "sourcesContent": ["<template>\r\n  <div style=\"height: calc(100vh - 300px)\">\r\n    <vxe-table\r\n      v-loading=\"tbLoading\"\r\n      :empty-render=\"{name: 'NotData'}\"\r\n      show-header-overflow\r\n      element-loading-spinner=\"el-icon-loading\"\r\n      element-loading-text=\"拼命加载中\"\r\n      empty-text=\"暂无数据\"\r\n      class=\"cs-vxe-table\"\r\n      height=\"100%\"\r\n      align=\"left\"\r\n      stripe\r\n      :data=\"tbData\"\r\n      resizable\r\n      :auto-resize=\"true\"\r\n      :tooltip-config=\"{ enterable: true }\"\r\n    >\r\n      <vxe-column\r\n        show-overflow=\"tooltip\"\r\n        sortable\r\n        field=\"Name\"\r\n        title=\"检查类型\"\r\n        width=\"calc(100vh-200px)\"\r\n      />\r\n      <vxe-column fixed=\"right\" title=\"操作\" width=\"200\" align=\"center\" show-overflow>\r\n        <template #default=\"{ row }\">\r\n          <el-button type=\"text\" @click=\"editEvent(row)\">编辑</el-button>\r\n          <el-divider direction=\"vertical\" />\r\n          <el-button type=\"text\" @click=\"removeEvent(row)\">删除</el-button>\r\n        </template>\r\n      </vxe-column>\r\n    </vxe-table>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { GetCheckTypeList } from '@/api/PRO/factorycheck'\r\nimport { DeleteCheckType } from '@/api/PRO/factorycheck'\r\n\r\nexport default {\r\n  props: {\r\n    checkType: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      tbLoading: false,\r\n      tbData: []\r\n    }\r\n  },\r\n  watch: {\r\n    checkType: {\r\n      handler(newName) {\r\n        this.checkType = newName\r\n        this.getCheckTypeList()\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getCheckTypeList()\r\n  },\r\n  methods: {\r\n    getCheckTypeList() {\r\n      this.tbLoading = true\r\n      GetCheckTypeList({ check_object_id: this.checkType.Id, Bom_Level: this.checkType.Code }).then((res) => {\r\n        if (res.IsSucceed) {\r\n          this.tbData = res.Data\r\n          this.tbLoading = false\r\n          console.log(res.Data)\r\n        } else {\r\n          this.$message({\r\n            type: 'error',\r\n            message: res.Message\r\n          })\r\n          this.tbLoading = false\r\n        }\r\n      })\r\n    },\r\n    removeEvent(row) {\r\n      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          DeleteCheckType({ id: row.Id }).then((res) => {\r\n            if (res.IsSucceed) {\r\n              this.$message({\r\n                type: 'success',\r\n                message: '删除成功!'\r\n              })\r\n              this.getCheckTypeList()\r\n            } else {\r\n              this.$message({\r\n                type: 'error',\r\n                message: res.Message\r\n              })\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n    editEvent(row) {\r\n      console.log('row', row)\r\n      this.$emit('optionFn', row)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* .vxe-table {\r\n  max-height: 100%;\r\n} */\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA,SAAAA,gBAAA;AACA,SAAAC,eAAA;AAEA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,MAAA;IACA;EACA;EACAC,KAAA;IACAP,SAAA;MACAQ,OAAA,WAAAA,QAAAC,OAAA;QACA,KAAAT,SAAA,GAAAS,OAAA;QACA,KAAAC,gBAAA;MACA;MACAC,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAF,gBAAA;EACA;EACAG,OAAA;IACAH,gBAAA,WAAAA,iBAAA;MAAA,IAAAI,KAAA;MACA,KAAAT,SAAA;MACAR,gBAAA;QAAAkB,eAAA,OAAAf,SAAA,CAAAgB,EAAA;QAAAC,SAAA,OAAAjB,SAAA,CAAAkB;MAAA,GAAAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,SAAA;UACAP,KAAA,CAAAR,MAAA,GAAAc,GAAA,CAAAE,IAAA;UACAR,KAAA,CAAAT,SAAA;UACAkB,OAAA,CAAAC,GAAA,CAAAJ,GAAA,CAAAE,IAAA;QACA;UACAR,KAAA,CAAAW,QAAA;YACAxB,IAAA;YACAyB,OAAA,EAAAN,GAAA,CAAAO;UACA;UACAb,KAAA,CAAAT,SAAA;QACA;MACA;IACA;IACAuB,WAAA,WAAAA,YAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAhC,IAAA;MACA,GACAkB,IAAA;QACArB,eAAA;UAAAoC,EAAA,EAAAL,GAAA,CAAAb;QAAA,GAAAG,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAS,MAAA,CAAAL,QAAA;cACAxB,IAAA;cACAyB,OAAA;YACA;YACAI,MAAA,CAAApB,gBAAA;UACA;YACAoB,MAAA,CAAAL,QAAA;cACAxB,IAAA;cACAyB,OAAA,EAAAN,GAAA,CAAAO;YACA;UACA;QACA;MACA,GACAQ,KAAA;QACAL,MAAA,CAAAL,QAAA;UACAxB,IAAA;UACAyB,OAAA;QACA;MACA;IACA;IACAU,SAAA,WAAAA,UAAAP,GAAA;MACAN,OAAA,CAAAC,GAAA,QAAAK,GAAA;MACA,KAAAQ,KAAA,aAAAR,GAAA;IACA;EACA;AACA", "ignoreList": []}]}
{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\sys\\professional-category\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\sys\\professional-category\\index.vue", "mtime": 1757468113629}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\babel.config.js", "mtime": 1745557753558}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750141583789}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["table", "bimdialog", "nodeList", "GetProfessionalDelete", "addRouterPage", "name", "components", "mixins", "data", "companyId", "localStorage", "getItem", "searchValue", "addPageArray", "path", "hidden", "component", "Promise", "resolve", "then", "_interopRequireWildcard", "require", "meta", "title", "$route", "methods", "getSelectionData", "getData", "$refs", "refresh", "addProfession", "dialog", "handleOpen", "edit", "item", "row", "is_system", "$message", "type", "message", "delete", "_this", "$confirm", "confirmButtonText", "cancelButtonText", "id", "res", "IsSucceed", "catch", "getClick", "$router", "push", "query", "pg_redirect", "unit", "steel_unit", "typeCode", "code", "materialCode", "materialcode", "typeId"], "sources": ["src/views/sys/professional-category/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"page-container\">\r\n      <el-row type=\"flex\" justify=\"space-between\" style=\"margin-bottom: 16px\">\r\n        <el-col :span=\"4\">\r\n          <el-button v-if=\"false\" type=\"primary\" @click=\"addProfession\">新增专业</el-button>\r\n        </el-col>\r\n        <el-col :span=\"20\">\r\n          <el-row type=\"flex\" justify=\"end\">\r\n            <el-input v-model=\"searchValue\" placeholder=\"请输入关键字\" style=\"width:250px \" clearable />\r\n            <div>\r\n              <el-button type=\"primary\" style=\"margin-left: 16px\" @click=\"getData\">搜索</el-button>\r\n            </div>\r\n          </el-row>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <div class=\"list-wrapper\">\r\n        <el-table\r\n          ref=\"table\"\r\n          tablecode=\"plm_professionaltype_list_pro\"\r\n          :custom-param=\"{ is_System: false, typeid: '', name: searchValue, companyId: companyId }\"\r\n          @get-selection-data=\"getSelectionData\"\r\n          @getbutton=\"getClick\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <bimdialog ref=\"dialog\" @getData=\"getData\" />\r\n    <nodeList ref=\"nodeList\" @getData=\"getData\" />\r\n  </div>\r\n</template>\r\n<script>\r\nimport table from '@/views/plm/components/table'\r\nimport bimdialog from './dialog'\r\nimport nodeList from './nodeList'\r\nimport { GetProfessionalDelete } from '@/api/plm/settings'\r\nimport addRouterPage from '@/mixins/add-router-page/index'\r\nexport default {\r\n  name: 'ProfessionalCategoryList',\r\n  components: {\r\n    'el-table': table,\r\n    bimdialog,\r\n    nodeList\r\n  },\r\n  mixins: [addRouterPage],\r\n  data() {\r\n    return {\r\n      companyId: localStorage.getItem('Last_Working_Object_Id'),\r\n      searchValue: '',\r\n      addPageArray: [\r\n        {\r\n          path: 'unit-template-setting',\r\n          hidden: true,\r\n          component: () => import('@/views/sys/professional-category/unitPartTemp.vue'),\r\n          name: 'SYSUnitPartTemp',\r\n          meta: { title: '专用模板配置' }\r\n        },\r\n        {\r\n          path: 'template-setting',\r\n          hidden: true,\r\n          component: () => import('@/views/sys/professional-category/templateSetting'),\r\n          name: 'TemplateSetting',\r\n          meta: { title: '专用模板配置' }\r\n        },\r\n        {\r\n          path: 'template-setting-lj',\r\n          hidden: true,\r\n          component: () => import('@/views/sys/professional-category/templateSettingLj'),\r\n          name: 'TemplateSettingLj',\r\n          meta: { title: '零件模板配置' }\r\n        },\r\n        {\r\n          path: this.$route.path + '/category',\r\n          hidden: true,\r\n          component: () => import('@/views/sys/professional-category/category/index.vue'),\r\n          name: 'ProfessionalCategoryListInfo',\r\n          meta: { title: '零构件类型' }\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    getSelectionData() {},\r\n    getData() {\r\n      this.$refs.table.refresh()\r\n    },\r\n    addProfession() {\r\n      this.$refs.dialog.handleOpen('add')\r\n    },\r\n    edit(item, row) {\r\n      if (row.row.is_system === true) {\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '该类别属于系统级别，不可操作'\r\n        })\r\n        return false\r\n      }\r\n      this.$refs.dialog.handleOpen('edit', row.row)\r\n    },\r\n    delete(item, row) {\r\n      if (row.row.is_system === true) {\r\n        this.$message({\r\n          type: 'warning',\r\n          message: '该类别属于系统级别，不可操作'\r\n        })\r\n        return false\r\n      }\r\n      this.$confirm(' 确认删除?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      })\r\n        .then(() => {\r\n          GetProfessionalDelete({ id: row.row.id }).then((res) => {\r\n            if (res.IsSucceed === true) {\r\n              this.$message({\r\n                type: 'success',\r\n                message: '删除成功'\r\n              })\r\n              this.getData()\r\n            }\r\n          })\r\n        })\r\n        .catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消删除'\r\n          })\r\n        })\r\n    },\r\n    getClick(item, row) {\r\n      switch (item) {\r\n        case 'btnedit':\r\n          this.edit(item, row)\r\n          break\r\n        case 'btndelete':\r\n          this.delete(item, row)\r\n          break\r\n        case 'jdedit':\r\n          this.$refs.nodeList.handleOpen(true, row.row)\r\n          break\r\n        case 'unitPartCode':\r\n          this.$router.push({\r\n            name: 'SYSUnitPartTemp',\r\n            query: {\r\n              pg_redirect: this.$route.name,\r\n              name: row.row.name,\r\n              unit: row.row.unit,\r\n              steel_unit: row.row.steel_unit\r\n            }\r\n          })\r\n          break\r\n        case 'mbedit':\r\n          this.$router.push(\r\n            { name: 'TemplateSetting',\r\n              query: {\r\n                pg_redirect: this.$route.name,\r\n                typeCode: row.row.code,\r\n                materialCode: row.row.materialcode,\r\n                name: row.row.name,\r\n                unit: row.row.unit,\r\n                steel_unit: row.row.steel_unit\r\n              }\r\n            })\r\n          break\r\n        case 'ljedit':\r\n          this.$router.push(\r\n            { name: 'TemplateSettingLj',\r\n              query: {\r\n                pg_redirect: this.$route.name,\r\n                typeCode: row.row.code,\r\n                materialCode: row.row.materialcode,\r\n                name: row.row.name,\r\n                unit: row.row.unit,\r\n                steel_unit: row.row.steel_unit\r\n              }\r\n            })\r\n          break\r\n        case 'gjedit':\r\n          this.$router.push(\r\n            { name: 'ProfessionalCategoryListInfo',\r\n              query: {\r\n                pg_redirect: this.$route.name,\r\n                typeCode: row.row.code,\r\n                materialCode: row.row.materialcode,\r\n                name: row.row.name,\r\n                unit: row.row.unit,\r\n                steel_unit: row.row.steel_unit,\r\n                typeId: row.row.id\r\n              }\r\n            })\r\n          break\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.page-container{\r\n  margin:16px;\r\n  background: #fff;\r\n  padding:16px;\r\n  box-sizing: border-box;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,OAAAA,KAAA;AACA,OAAAC,SAAA;AACA,OAAAC,QAAA;AACA,SAAAC,qBAAA;AACA,OAAAC,aAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACA,YAAAN,KAAA;IACAC,SAAA,EAAAA,SAAA;IACAC,QAAA,EAAAA;EACA;EACAK,MAAA,GAAAH,aAAA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA,EAAAC,YAAA,CAAAC,OAAA;MACAC,WAAA;MACAC,YAAA,GACA;QACAC,IAAA;QACAC,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAhB,IAAA;QACAiB,IAAA;UAAAC,KAAA;QAAA;MACA,GACA;QACAT,IAAA;QACAC,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAhB,IAAA;QACAiB,IAAA;UAAAC,KAAA;QAAA;MACA,GACA;QACAT,IAAA;QACAC,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAhB,IAAA;QACAiB,IAAA;UAAAC,KAAA;QAAA;MACA,GACA;QACAT,IAAA,OAAAU,MAAA,CAAAV,IAAA;QACAC,MAAA;QACAC,SAAA,WAAAA,UAAA;UAAA,OAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA;YAAA,OAAAC,uBAAA,CAAAC,OAAA;UAAA;QAAA;QACAhB,IAAA;QACAiB,IAAA;UAAAC,KAAA;QAAA;MACA;IAEA;EACA;EACAE,OAAA;IACAC,gBAAA,WAAAA,iBAAA;IACAC,OAAA,WAAAA,QAAA;MACA,KAAAC,KAAA,CAAA5B,KAAA,CAAA6B,OAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA,KAAAF,KAAA,CAAAG,MAAA,CAAAC,UAAA;IACA;IACAC,IAAA,WAAAA,KAAAC,IAAA,EAAAC,GAAA;MACA,IAAAA,GAAA,CAAAA,GAAA,CAAAC,SAAA;QACA,KAAAC,QAAA;UACAC,IAAA;UACAC,OAAA;QACA;QACA;MACA;MACA,KAAAX,KAAA,CAAAG,MAAA,CAAAC,UAAA,SAAAG,GAAA,CAAAA,GAAA;IACA;IACAK,MAAA,WAAAA,QAAAN,IAAA,EAAAC,GAAA;MAAA,IAAAM,KAAA;MACA,IAAAN,GAAA,CAAAA,GAAA,CAAAC,SAAA;QACA,KAAAC,QAAA;UACAC,IAAA;UACAC,OAAA;QACA;QACA;MACA;MACA,KAAAG,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAN,IAAA;MACA,GACAnB,IAAA;QACAhB,qBAAA;UAAA0C,EAAA,EAAAV,GAAA,CAAAA,GAAA,CAAAU;QAAA,GAAA1B,IAAA,WAAA2B,GAAA;UACA,IAAAA,GAAA,CAAAC,SAAA;YACAN,KAAA,CAAAJ,QAAA;cACAC,IAAA;cACAC,OAAA;YACA;YACAE,KAAA,CAAAd,OAAA;UACA;QACA;MACA,GACAqB,KAAA;QACAP,KAAA,CAAAJ,QAAA;UACAC,IAAA;UACAC,OAAA;QACA;MACA;IACA;IACAU,QAAA,WAAAA,SAAAf,IAAA,EAAAC,GAAA;MACA,QAAAD,IAAA;QACA;UACA,KAAAD,IAAA,CAAAC,IAAA,EAAAC,GAAA;UACA;QACA;UACA,KAAAK,MAAA,CAAAN,IAAA,EAAAC,GAAA;UACA;QACA;UACA,KAAAP,KAAA,CAAA1B,QAAA,CAAA8B,UAAA,OAAAG,GAAA,CAAAA,GAAA;UACA;QACA;UACA,KAAAe,OAAA,CAAAC,IAAA;YACA9C,IAAA;YACA+C,KAAA;cACAC,WAAA,OAAA7B,MAAA,CAAAnB,IAAA;cACAA,IAAA,EAAA8B,GAAA,CAAAA,GAAA,CAAA9B,IAAA;cACAiD,IAAA,EAAAnB,GAAA,CAAAA,GAAA,CAAAmB,IAAA;cACAC,UAAA,EAAApB,GAAA,CAAAA,GAAA,CAAAoB;YACA;UACA;UACA;QACA;UACA,KAAAL,OAAA,CAAAC,IAAA,CACA;YAAA9C,IAAA;YACA+C,KAAA;cACAC,WAAA,OAAA7B,MAAA,CAAAnB,IAAA;cACAmD,QAAA,EAAArB,GAAA,CAAAA,GAAA,CAAAsB,IAAA;cACAC,YAAA,EAAAvB,GAAA,CAAAA,GAAA,CAAAwB,YAAA;cACAtD,IAAA,EAAA8B,GAAA,CAAAA,GAAA,CAAA9B,IAAA;cACAiD,IAAA,EAAAnB,GAAA,CAAAA,GAAA,CAAAmB,IAAA;cACAC,UAAA,EAAApB,GAAA,CAAAA,GAAA,CAAAoB;YACA;UACA;UACA;QACA;UACA,KAAAL,OAAA,CAAAC,IAAA,CACA;YAAA9C,IAAA;YACA+C,KAAA;cACAC,WAAA,OAAA7B,MAAA,CAAAnB,IAAA;cACAmD,QAAA,EAAArB,GAAA,CAAAA,GAAA,CAAAsB,IAAA;cACAC,YAAA,EAAAvB,GAAA,CAAAA,GAAA,CAAAwB,YAAA;cACAtD,IAAA,EAAA8B,GAAA,CAAAA,GAAA,CAAA9B,IAAA;cACAiD,IAAA,EAAAnB,GAAA,CAAAA,GAAA,CAAAmB,IAAA;cACAC,UAAA,EAAApB,GAAA,CAAAA,GAAA,CAAAoB;YACA;UACA;UACA;QACA;UACA,KAAAL,OAAA,CAAAC,IAAA,CACA;YAAA9C,IAAA;YACA+C,KAAA;cACAC,WAAA,OAAA7B,MAAA,CAAAnB,IAAA;cACAmD,QAAA,EAAArB,GAAA,CAAAA,GAAA,CAAAsB,IAAA;cACAC,YAAA,EAAAvB,GAAA,CAAAA,GAAA,CAAAwB,YAAA;cACAtD,IAAA,EAAA8B,GAAA,CAAAA,GAAA,CAAA9B,IAAA;cACAiD,IAAA,EAAAnB,GAAA,CAAAA,GAAA,CAAAmB,IAAA;cACAC,UAAA,EAAApB,GAAA,CAAAA,GAAA,CAAAoB,UAAA;cACAK,MAAA,EAAAzB,GAAA,CAAAA,GAAA,CAAAU;YACA;UACA;UACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
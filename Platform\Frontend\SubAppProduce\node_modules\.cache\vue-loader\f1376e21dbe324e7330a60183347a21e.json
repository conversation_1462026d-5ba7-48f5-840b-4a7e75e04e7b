{"remainingRequest": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\nesting-management\\components\\NestResult.vue?vue&type=style&index=0&id=c3d3537e&lang=scss&scoped=true", "dependencies": [{"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\src\\views\\PRO\\nesting-management\\components\\NestResult.vue", "mtime": 1757468127975}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750141574391}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750141670001}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750141583771}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750141569231}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750141569292}, {"path": "D:\\project\\platform_framework_master\\platform_framework\\Platform\\Frontend\\SubAppProduce\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750141614095}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouY3MtbGFiZWwgew0KICBmb250LXNpemU6IDE0cHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIHdoaXRlLXNwYWNlOiBwcmUtd3JhcDsNCn0NCg0KLmNzLXVwbG9hZC14IHsNCiAgbWFyZ2luOiAzMnB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgLmMtdXBsb2FkLWNvbnRhaW5lcnsNCiAgICB3aWR0aDogMTAwJTsNCiAgfQ0KfQ0KDQo="}, {"version": 3, "sources": ["NestResult.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqFA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "NestResult.vue", "sourceRoot": "src/views/PRO/nesting-management/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-alert\r\n      type=\"warning\"\r\n      :closable=\"false\"\r\n    >\r\n      <template>\r\n        <span class=\"cs-label\">注意：请先 <el-link\r\n          type=\"primary\"\r\n          :underline=\"false\"\r\n          @click=\"handleExport\"\r\n        >点击下载模板</el-link></span>\r\n      </template>\r\n    </el-alert>\r\n\r\n    <div class=\"cs-upload-x\">\r\n      <upload-excel ref=\"upload\" :before-upload=\"beforeUpload\" />\r\n    </div>\r\n    <div slot=\"footer\" class=\"dialog-footer\" style=\"text-align: right;\">\r\n      <el-button @click=\"$emit('close')\">取 消</el-button>\r\n      <el-button\r\n        type=\"primary\"\r\n        :loading=\"btnLoading\"\r\n        @click=\"handleSubmit\"\r\n      >确 定\r\n      </el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { combineURL } from '@/utils'\r\nimport { GetPlateNestingResultImportFile, ImportPlateNestingResult } from '@/api/PRO/production-task'\r\nimport UploadExcel from '@/components/UploadExcel/index.vue'\r\n\r\nexport default {\r\n  components: { UploadExcel },\r\n  data() {\r\n    return {\r\n      btnLoading: false\r\n    }\r\n  },\r\n  methods: {\r\n    beforeUpload(file) {\r\n      this.btnLoading = true\r\n      const fileFormData = new FormData()\r\n      fileFormData.append('Files', file)\r\n      ImportPlateNestingResult(fileFormData).then(res => {\r\n        if (res.IsSucceed) {\r\n          this.$message({\r\n            message: '导入成功',\r\n            type: 'success'\r\n          })\r\n          this.$emit('close')\r\n          this.$emit('refresh')\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n          res.Data && window.open(combineURL(this.$baseUrl, res.Data))\r\n        }\r\n        this.btnLoading = false\r\n      })\r\n    },\r\n    handleSubmit() {\r\n      this.$refs.upload.handleSubmit()\r\n    },\r\n    handleExport() {\r\n      GetPlateNestingResultImportFile({}).then(res => {\r\n        if (res.IsSucceed) {\r\n          window.open(combineURL(this.$baseUrl, res.Data))\r\n        } else {\r\n          this.$message({\r\n            message: res.Message,\r\n            type: 'error'\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.cs-label {\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n  white-space: pre-wrap;\r\n}\r\n\r\n.cs-upload-x {\r\n  margin: 32px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  .c-upload-container{\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n</style>\r\n"]}]}
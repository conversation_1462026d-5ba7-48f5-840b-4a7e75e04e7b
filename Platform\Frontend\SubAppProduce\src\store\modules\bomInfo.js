import { GetBomLevelList } from '@/api/PRO/bom-level'

const state = {
  bomLevelList: [],
  bomLevelLoading: false
}

const mutations = {
  SET_BOM_LEVEL_LIST(state, list) {
    state.bomLevelList = list
  },
  SET_BOM_LEVEL_LOADING(state, loading) {
    state.bomLevelLoading = loading
  }
}

const actions = {
  async getBomLevelList({ commit, state }) {
    if (state.bomLevelList.length > 0) {
      return {
        IsSucceed: true,
        Data: state.bomLevelList
      }
    }

    commit('SET_BOM_LEVEL_LOADING', true)
    try {
      const res = await GetBomLevelList()
      if (res.IsSucceed) {
        const list = (res.Data || []).filter(v => v.Is_Enabled).map(item => {
          item.Sort = parseInt(item.Sort)
          return item
        }).sort((a, b) => a.Sort - b.Sort)

        commit('SET_BOM_LEVEL_LIST', list)
        return {
          IsSucceed: true,
          Data: list
        }
      }
      return res
    } catch (error) {
      console.error('获取BOM层级列表失败:', error)
      return {
        IsSucceed: false,
        Message: '获取BOM层级列表失败'
      }
    } finally {
      commit('SET_BOM_LEVEL_LOADING', false)
    }
  },

  clearBomLevelCache({ commit }) {
    commit('SET_BOM_LEVEL_LIST', [])
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

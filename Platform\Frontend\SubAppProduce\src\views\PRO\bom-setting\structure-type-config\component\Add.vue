<template>
  <el-form ref="form" :model="form" :rules="rules" label-width="120px">
    <el-form-item :label="`${levelName}大类名称`" prop="Name">
      <el-input v-model.trim="form.Name" clearable maxlength="50" />
    </el-form-item>
    <el-form-item :label="`${levelName}大类编号`" prop="Code">
      <el-input v-model.trim="form.Code" clearable maxlength="50" />
    </el-form-item>
    <el-form-item label="生产周期" prop="Lead_Time">
      <el-input-number v-model.number="form.Lead_Time" class="cs-number-btn-hidden w100" clearable />
    </el-form-item>
    <el-form-item v-if="showDirect" label="直发件" prop="Is_Component">
      <el-radio-group v-model="form.Is_Component">
        <el-radio :label="true">否</el-radio>
        <el-radio :label="false">是</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item style="text-align: right">
      <el-button @click="$emit('close')">取消</el-button>
      <el-button type="primary" :loading="btnLoading" @click="submit">保存</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { SaveProBimComponentType } from '@/api/PRO/component-type'
import { SavePartType } from '@/api/PRO/partType'

export default {
  props: {
    addLevel: {
      type: Number,
      default: 1
    },
    typeId: {
      type: String,
      default: ''
    },
    parentId: {
      type: String,
      default: ''
    },
    activeType: {
      type: String,
      default: ''
    },
    typeCode: {
      type: String,
      default: ''
    },
    // true=构件；false=零件
    isComp: {
      type: Boolean,
      default: true
    },
    showDirect: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      btnLoading: false,
      form: {
        Code: '',
        Name: '',
        Is_Component: '',
        Lead_Time: 0
      },
      rules: {
        Name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        Code: [
          { required: true, message: '请输入编码', trigger: 'blur' }
        ],
        Is_Component: [
          { required: true, message: '请选择是否直发件', trigger: 'change' }
        ],
        Lead_Time: [
          { required: true, message: '请输入周期', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    levelName() {
      return this.addLevel === 1 ? '一级' : (this.addLevel === 2 ? '二级' : (this.addLevel === 3 ? '三级' : ''))
    }
  },
  methods: {
    submit() {
      this.$refs['form'].validate(async(valid) => {
        if (!valid) {
          return false
        }
        this.form.Category = this.typeCode
        this.form.Professional_Id = this.typeId
        this.form.Level = this.addLevel
        if (this.addLevel > 1) {
          this.form.Parent_Id = this.parentId
        }
        this.btnLoading = true
        let postFN
        const submitObj = { ...this.form }
        if (this.isComp) {
          postFN = SaveProBimComponentType
        } else {
          submitObj.Part_Grade = this.activeType.toString()
          submitObj.Is_Direct = !this.form.Is_Component
          postFN = SavePartType
        }
        console.log(submitObj, 'submitObj')
        const res = await postFN(submitObj)
        if (res.IsSucceed) {
          this.$message({
            message: '操作成功',
            type: 'success'
          })
          this.$emit('close')
          this.$emit('getTreeList')
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
        this.btnLoading = false
      })
    }
  }
}
</script>

<style scoped>

</style>
